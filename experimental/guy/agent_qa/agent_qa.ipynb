{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "import os\n", "from pathlib import Path\n", "\n", "from experimental.guy.retrieval_server.retrieval_client import (\n", "    RetrievalClient,\n", "    RetrievalRequest,\n", ")\n", "\n", "model = \"claude-3-5-sonnet-20240620\"\n", "\n", "api_key = (\n", "    Path(\"/home/<USER>/.config/anthropic/api_key\").read_text(encoding=\"utf8\").strip()\n", ")\n", "\n", "chat_client = anthropic.Anthropic(api_key=api_key)\n", "\n", "retrieval_client = RetrievalClient(\"localhost:5050\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add documents to the server\n", "\n", "# retrieval_client.remove_all_documents()\n", "\n", "\n", "def add_files_to_retriever(root_dir):\n", "    allowed_extensions = (\".py\", \".ts\", \".rs\", \".go\")\n", "    for root, _, files in os.walk(root_dir):\n", "        if \".pnpm\" in root or \"node_modules\" in root:\n", "            continue\n", "        for file in files:\n", "            if file.endswith(allowed_extensions):\n", "                file_path = os.path.join(root, file)\n", "                with open(file_path, \"r\", encoding=\"utf-8\") as f:\n", "                    content = f.read()\n", "                reported_path = os.path.relpath(file_path, root_dir)\n", "                retrieval_client.add_document(reported_path, content)\n", "                print(f\"Added: {reported_path}\")\n", "\n", "\n", "add_files_to_retriever(\"/home/<USER>/augment\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================================================\n", "Agent answering question: Implement a new production prompt formatter for the slack bot, which only looks at the last user question (and does not look at previous messages in the thread)\n", "========================================================================================================================\n", "DIALOG:\n", "{'role': 'user', 'content': 'Implement a new production prompt formatter for the slack bot, which only looks at the last user question (and does not look at previous messages in the thread)'}\n", "\n", "Chain-of-thought:\n", "To implement a new production prompt formatter for the Slack bot that only looks at the last user question, we'll need to make some changes to the existing codebase. Let's break this down into smaller steps and use the available tools to gather information about the current implementation.\n", "\n", "First, let's find out where the current prompt formatter is implemented.\n", "========================================================================================================================\n", "answer_simple_question(Where is the current prompt formatter implemented for the Slack bot?)\n", "========================================================================================================================\n", "Based on the code snippets provided, there doesn't seem to be a specific prompt formatter implemented for a Slack bot. The code snippets show various prompt formatters for different models and use cases, but none explicitly mention a Slack bot.\n", "\n", "However, if you're looking for a general prompt formatter that could potentially be used or adapted for a Slack bot, some relevant options from the code include:\n", "\n", "1. The `StructuredBinksPromptFormatter` in `base/prompt_format_chat/structured_binks_prompt_formatter.py`:\n", "\n", "```python\n", "class StructuredBinksPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):\n", "    \"\"\"The class formats prompts for the Binks chat model.\"\"\"\n", "```\n", "\n", "This formatter is designed for chat-based interactions and could potentially be adapted for use with a Slack bot.\n", "\n", "2. The `ChatPromptFormatter` interface in `base/prompt_format_chat/prompt_formatter.py`:\n", "\n", "```python\n", "class ChatPromptFormatter(Protocol[OutputT]):\n", "    \"\"\"Interface for chat prompt formatters.\"\"\"\n", "```\n", "\n", "This is a general interface for chat prompt formatters that could be implemented specifically for a Slack bot.\n", "\n", "3. Various other chat-based formatters like `GeminiBinksChatPromptFormatter`, `ClaudeBinksChatPromptFormatter`, or `DeepSeekCoderV2BinksChatPromptFormatter` in different files under the `base/prompt_format_chat/` directory.\n", "\n", "If there isn't a specific Slack bot prompt formatter implemented yet, you might need to create a new one based on these existing chat formatters, tailoring it to the specific requirements of your Slack bot interaction.\n", "\n", "To implement a Slack bot-specific prompt formatter, you would likely create a new file in the `base/prompt_format_chat/` directory, perhaps named something like `slack_bot_prompt_formatter.py`, and implement a class that follows the `ChatPromptFormatter` interface or extends one of the existing chat formatters.\n", "DIALOG:\n", "{'role': 'user', 'content': 'Implement a new production prompt formatter for the slack bot, which only looks at the last user question (and does not look at previous messages in the thread)'}\n", "{'role': 'assistant', 'content': [TextBlock(text=\"To implement a new production prompt formatter for the Slack bot that only looks at the last user question, we'll need to make some changes to the existing codebase. Let's break this down into smaller steps and use the available tools to gather information about the current implementation.\\n\\nFirst, let's find out where the current prompt formatter is implemented.\", type='text'), ToolUseBlock(id='toolu_01V2iYGTDC44grPvUCdTuLpc', input={'question': 'Where is the current prompt formatter implemented for the Slack bot?'}, name='answer_simple_question', type='tool_use')]}\n", "{'role': 'user', 'content': [{'type': 'tool_result', 'tool_use_id': 'toolu_01V2iYGTDC44grPvUCdTuLpc', 'content': 'Based on the code snippets provided, there doesn\\'t seem to be a specific prompt formatter implemented for a Slack bot. The code snippets show various prompt formatters for different models and use cases, but none explicitly mention a Slack bot.\\n\\nHowever, if you\\'re looking for a general prompt formatter that could potentially be used or adapted for a Slack bot, some relevant options from the code include:\\n\\n1. The `StructuredBinksPromptFormatter` in `base/prompt_format_chat/structured_binks_prompt_formatter.py`:\\n\\n```python\\nclass StructuredBinksPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):\\n    \"\"\"The class formats prompts for the Binks chat model.\"\"\"\\n```\\n\\nThis formatter is designed for chat-based interactions and could potentially be adapted for use with a Slack bot.\\n\\n2. The `ChatPromptFormatter` interface in `base/prompt_format_chat/prompt_formatter.py`:\\n\\n```python\\nclass ChatPromptFormatter(Protocol[OutputT]):\\n    \"\"\"Interface for chat prompt formatters.\"\"\"\\n```\\n\\nThis is a general interface for chat prompt formatters that could be implemented specifically for a Slack bot.\\n\\n3. Various other chat-based formatters like `GeminiBinksChatPromptFormatter`, `ClaudeBinksChatPromptFormatter`, or `DeepSeekCoderV2BinksChatPromptFormatter` in different files under the `base/prompt_format_chat/` directory.\\n\\nIf there isn\\'t a specific Slack bot prompt formatter implemented yet, you might need to create a new one based on these existing chat formatters, tailoring it to the specific requirements of your Slack bot interaction.\\n\\nTo implement a Slack bot-specific prompt formatter, you would likely create a new file in the `base/prompt_format_chat/` directory, perhaps named something like `slack_bot_prompt_formatter.py`, and implement a class that follows the `ChatPromptFormatter` interface or extends one of the existing chat formatters.'}]}\n", "\n", "Chain-of-thought:\n", "Thank you for the information. It seems that we don't have a specific Slack bot prompt formatter implemented yet. We'll need to create a new one based on the existing chat formatters. Let's gather more information about the structure of the existing chat formatters to help us implement the new Slack bot prompt formatter.\n", "========================================================================================================================\n", "answer_simple_question(Can you show me the structure of the StructuredBinksPromptFormatter class?)\n", "========================================================================================================================\n", "Certainly! I'll provide you with an overview of the structure of the `StructuredBinksPromptFormatter` class based on the information available in the codebase. Here's a breakdown of its main components:\n", "\n", "```python\n", "class StructuredBinksPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):\n", "    def __init__(\n", "        self,\n", "        token_counter: <PERSON><PERSON><PERSON><PERSON>nter,\n", "        system_prompt: StringFormatter,\n", "        retrieval_section_builder: RetrievalSectionBuilder,\n", "        selected_code_formatter: SelectedCodePromptFormatterV2,\n", "        token_apportionment: Optional[ChatTokenApportionment] = None,\n", "    ):\n", "        # Initialize instance variables\n", "        \n", "    def format_prompt(\n", "        self, prompt_input: ChatPromptInput\n", "    ) -> StructuredChatPromptOutput:\n", "        # Format the prompt\n", "        \n", "    @classmethod\n", "    def create(\n", "        cls,\n", "        token_counter: <PERSON><PERSON><PERSON><PERSON>nter,\n", "        token_apportionment: ChatTokenApportionment | None = None,\n", "        system_prompt_factory: Callable[\n", "            [<PERSON><PERSON><PERSON><PERSON><PERSON>], <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "        ] = get_binks_system_prompt_formatter,\n", "    ):\n", "        # Create an instance of StructuredBinksPromptFormatter\n", "```\n", "\n", "Key components of the class:\n", "\n", "1. It inherits from `ChatPromptFormatter` with a type parameter of `StructuredChatPromptOutput`.\n", "\n", "2. The `__init__` method initializes the class with:\n", "   - `token_counter`: A `TokenCounter` object for counting tokens.\n", "   - `system_prompt`: A `StringFormatter` for formatting the system prompt.\n", "   - `retrieval_section_builder`: A `RetrievalSectionBuilder` for building retrieval sections.\n", "   - `selected_code_formatter`: A `SelectedCodePromptFormatterV2` for formatting selected code.\n", "   - `token_apportionment`: An optional `ChatTokenApportionment` for token budgeting.\n", "\n", "3. The `format_prompt` method:\n", "   - Takes a `ChatPromptInput` as input.\n", "   - Returns a `StructuredChatPromptOutput`.\n", "   - This is where the main logic for formatting the prompt is implemented.\n", "\n", "4. The `create` class method:\n", "   - A factory method for creating instances of `StructuredBinksPromptFormatter`.\n", "   - Takes a `token_counter`, optional `token_apportionment`, and an optional `system_prompt_factory`.\n", "   - Returns a new instance of `StructuredBinksPromptFormatter`.\n", "\n", "This class is responsible for formatting prompts for the Binks chat model, handling system prompts, chat history, retrievals, and selected code sections within the specified token budgets.\n", "DIALOG:\n", "{'role': 'user', 'content': 'Implement a new production prompt formatter for the slack bot, which only looks at the last user question (and does not look at previous messages in the thread)'}\n", "{'role': 'assistant', 'content': [TextBlock(text=\"To implement a new production prompt formatter for the Slack bot that only looks at the last user question, we'll need to make some changes to the existing codebase. Let's break this down into smaller steps and use the available tools to gather information about the current implementation.\\n\\nFirst, let's find out where the current prompt formatter is implemented.\", type='text'), ToolUseBlock(id='toolu_01V2iYGTDC44grPvUCdTuLpc', input={'question': 'Where is the current prompt formatter implemented for the Slack bot?'}, name='answer_simple_question', type='tool_use')]}\n", "{'role': 'user', 'content': [{'type': 'tool_result', 'tool_use_id': 'toolu_01V2iYGTDC44grPvUCdTuLpc', 'content': 'Based on the code snippets provided, there doesn\\'t seem to be a specific prompt formatter implemented for a Slack bot. The code snippets show various prompt formatters for different models and use cases, but none explicitly mention a Slack bot.\\n\\nHowever, if you\\'re looking for a general prompt formatter that could potentially be used or adapted for a Slack bot, some relevant options from the code include:\\n\\n1. The `StructuredBinksPromptFormatter` in `base/prompt_format_chat/structured_binks_prompt_formatter.py`:\\n\\n```python\\nclass StructuredBinksPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):\\n    \"\"\"The class formats prompts for the Binks chat model.\"\"\"\\n```\\n\\nThis formatter is designed for chat-based interactions and could potentially be adapted for use with a Slack bot.\\n\\n2. The `ChatPromptFormatter` interface in `base/prompt_format_chat/prompt_formatter.py`:\\n\\n```python\\nclass ChatPromptFormatter(Protocol[OutputT]):\\n    \"\"\"Interface for chat prompt formatters.\"\"\"\\n```\\n\\nThis is a general interface for chat prompt formatters that could be implemented specifically for a Slack bot.\\n\\n3. Various other chat-based formatters like `GeminiBinksChatPromptFormatter`, `ClaudeBinksChatPromptFormatter`, or `DeepSeekCoderV2BinksChatPromptFormatter` in different files under the `base/prompt_format_chat/` directory.\\n\\nIf there isn\\'t a specific Slack bot prompt formatter implemented yet, you might need to create a new one based on these existing chat formatters, tailoring it to the specific requirements of your Slack bot interaction.\\n\\nTo implement a Slack bot-specific prompt formatter, you would likely create a new file in the `base/prompt_format_chat/` directory, perhaps named something like `slack_bot_prompt_formatter.py`, and implement a class that follows the `ChatPromptFormatter` interface or extends one of the existing chat formatters.'}]}\n", "{'role': 'assistant', 'content': [TextBlock(text=\"Thank you for the information. It seems that we don't have a specific Slack bot prompt formatter implemented yet. We'll need to create a new one based on the existing chat formatters. Let's gather more information about the structure of the existing chat formatters to help us implement the new Slack bot prompt formatter.\", type='text'), ToolUseBlock(id='toolu_01Eje5iRRpdhEtRoeAv16p9V', input={'question': 'Can you show me the structure of the StructuredBinksPromptFormatter class?'}, name='answer_simple_question', type='tool_use')]}\n", "{'role': 'user', 'content': [{'type': 'tool_result', 'tool_use_id': 'toolu_01Eje5iRRpdhEtRoeAv16p9V', 'content': \"Certainly! I'll provide you with an overview of the structure of the `StructuredBinksPromptFormatter` class based on the information available in the codebase. Here's a breakdown of its main components:\\n\\n```python\\nclass StructuredBinksPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):\\n    def __init__(\\n        self,\\n        token_counter: TokenCounter,\\n        system_prompt: StringFormatter,\\n        retrieval_section_builder: RetrievalSectionBuilder,\\n        selected_code_formatter: SelectedCodePromptFormatterV2,\\n        token_apportionment: Optional[ChatTokenApportionment] = None,\\n    ):\\n        # Initialize instance variables\\n        \\n    def format_prompt(\\n        self, prompt_input: ChatPromptInput\\n    ) -> StructuredChatPromptOutput:\\n        # Format the prompt\\n        \\n    @classmethod\\n    def create(\\n        cls,\\n        token_counter: TokenCounter,\\n        token_apportionment: ChatTokenApportionment | None = None,\\n        system_prompt_factory: Callable[\\n            [TokenCounter], StringFormatter\\n        ] = get_binks_system_prompt_formatter,\\n    ):\\n        # Create an instance of StructuredBinksPromptFormatter\\n```\\n\\nKey components of the class:\\n\\n1. It inherits from `ChatPromptFormatter` with a type parameter of `StructuredChatPromptOutput`.\\n\\n2. The `__init__` method initializes the class with:\\n   - `token_counter`: A `TokenCounter` object for counting tokens.\\n   - `system_prompt`: A `StringFormatter` for formatting the system prompt.\\n   - `retrieval_section_builder`: A `RetrievalSectionBuilder` for building retrieval sections.\\n   - `selected_code_formatter`: A `SelectedCodePromptFormatterV2` for formatting selected code.\\n   - `token_apportionment`: An optional `ChatTokenApportionment` for token budgeting.\\n\\n3. The `format_prompt` method:\\n   - Takes a `ChatPromptInput` as input.\\n   - Returns a `StructuredChatPromptOutput`.\\n   - This is where the main logic for formatting the prompt is implemented.\\n\\n4. The `create` class method:\\n   - A factory method for creating instances of `StructuredBinksPromptFormatter`.\\n   - Takes a `token_counter`, optional `token_apportionment`, and an optional `system_prompt_factory`.\\n   - Returns a new instance of `StructuredBinksPromptFormatter`.\\n\\nThis class is responsible for formatting prompts for the Binks chat model, handling system prompts, chat history, retrievals, and selected code sections within the specified token budgets.\"}]}\n", "\n", "Chain-of-thought:\n", "Thank you for providing the structure of the `StructuredBinksPromptFormatter` class. Now, let's implement a new `SlackBotPromptFormatter` that only looks at the last user question. We'll create a new file called `slack_bot_prompt_formatter.py` in the `base/prompt_format_chat/` directory.\n", "\n", "Here's the implementation of the new `SlackBotPromptFormatter`:\n", "\n", "Final answer:\n", "To implement a new production prompt formatter for the Slack bot that only looks at the last user question, we'll create a new file called `slack_bot_prompt_formatter.py` in the `base/prompt_format_chat/` directory. Here's the implementation:\n", "\n", "```python\n", "from typing import Optional\n", "from base.prompt_format_chat.prompt_formatter import ChatPromptFormatter, ChatPromptInput\n", "from base.prompt_format_chat.structured_chat_prompt_output import StructuredChatPromptOutput\n", "from base.token_count import TokenCounter\n", "from base.string_format.string_formatter import StringFormatter\n", "from base.prompt_format_chat.chat_token_apportionment import ChatTokenApportionment\n", "\n", "class SlackBotPromptFormatter(ChatPromptFormatter[StructuredChatPromptOutput]):\n", "    def __init__(\n", "        self,\n", "        token_counter: <PERSON><PERSON><PERSON><PERSON>nter,\n", "        system_prompt: StringFormatter,\n", "        token_apportionment: Optional[ChatTokenApportionment] = None,\n", "    ):\n", "        self.token_counter = token_counter\n", "        self.system_prompt = system_prompt\n", "        self.token_apportionment = token_apportionment or ChatTokenApportionment()\n", "\n", "    def format_prompt(\n", "        self, prompt_input: ChatPromptInput\n", "    ) -> StructuredChatPromptOutput:\n", "        system_prompt = self.system_prompt.format()\n", "        \n", "        # Only take the last user message\n", "        last_user_message = prompt_input.messages[-1] if prompt_input.messages else None\n", "        \n", "        formatted_prompt = f\"{system_prompt}\\n\\nUser: {last_user_message.content if last_user_message else ''}\"\n", "        \n", "        return StructuredChatPromptOutput(\n", "            formatted_prompt=formatted_prompt,\n", "            num_tokens=self.token_counter.count_tokens(formatted_prompt),\n", "            system_prompt=system_prompt,\n", "            messages=[last_user_message] if last_user_message else [],\n", "            retrieval_sections=[],\n", "            selected_code_sections=[],\n", "        )\n", "\n", "    @classmethod\n", "    def create(\n", "        cls,\n", "        token_counter: <PERSON><PERSON><PERSON><PERSON>nter,\n", "        token_apportionment: Optional[ChatTokenApportionment] = None,\n", "        system_prompt_factory: Callable[\n", "            [<PERSON><PERSON><PERSON><PERSON><PERSON>], <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "        ] = get_slack_bot_system_prompt_formatter,\n", "    ):\n", "        system_prompt = system_prompt_factory(token_counter)\n", "        return cls(token_counter, system_prompt, token_apportionment)\n", "\n", "def get_slack_bot_system_prompt_formatter(token_counter: TokenCounter) -> StringFormatter:\n", "    # Implement this function to return an appropriate system prompt for the Slack bot\n", "    return StringFormatter(\"You are a helpful AI assistant for the Slack bot.\")\n", "```\n", "\n", "This implementation creates a new `SlackBotPromptFormatter` class that:\n", "\n", "1. Inherits from `ChatPromptFormatter` with `StructuredChatPromptOutput` as the output type.\n", "2. Initializes with a `token_counter`, `system_prompt`, and optional `token_apportionment`.\n", "3. Implements the `format_prompt` method to:\n", "   - Format the system prompt.\n", "   - Take only the last user message from the input.\n", "   - Combine the system prompt and last user message into a formatted prompt.\n", "   - Return a `StructuredChatPromptOutput` with the formatted prompt and other necessary information.\n", "4. Provides a `create` class method for easy instantiation.\n", "5. Includes a simple `get_slack_bot_system_prompt_formatter` function that can be customized as needed.\n", "\n", "To use this new formatter:\n", "\n", "1. Place the `slack_bot_prompt_formatter.py` file in the `base/prompt_format_chat/` directory.\n", "2. Import and use the `SlackBotPromptFormatter` in your Slack bot implementation.\n", "3. When creating an instance, provide the necessary `token_counter` and optionally customize the `system_prompt_factory` and `token_apportionment`.\n", "\n", "This implementation ensures that only the last user question is considered when formatting the prompt for the Slack bot, ignoring any previous messages in the thread. You can further customize the `get_slack_bot_system_prompt_formatter` function to provide a more specific system prompt for your Slack bot's use case.\n"]}], "source": ["system_prompt = \"\"\"\\\n", "You are a helpful assistant, designed to answer complex questions about the codebase.\n", "You have access to tools to help you understand the codebase. Break down complex questions\n", "into simpler questions, and use tools to answer simple questions. Then use the simple\n", "answers and reason about them in order to provide an answer to the complex question.\n", "\n", "When you have a final answer, call the correct_final_answer tool to provide it to the user.\n", "Include the most relevant code snippets and paths in your response.\n", "\"\"\"\n", "\n", "answer_simple_question_tool = {\n", "    \"name\": \"answer_simple_question\",\n", "    \"description\": \"Answers simple questions about the codebase. A simple question is one that can be answered by looking at just a few locations in the codebase, and does not involve complex reasoning. The simpler the question, the more likely the tool is to provide a useful answer. The tool will answer with natural language, and also with pointers to the codebase.\",\n", "    \"input_schema\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"question\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The simple question about the codebase.\",\n", "            }\n", "        },\n", "        \"required\": [\"question\"],\n", "    },\n", "}\n", "\n", "\n", "correct_final_answer_tool = {\n", "    \"name\": \"correct_final_answer\",\n", "    \"description\": \"Provide a correct and final answer to the user. Call this when you have collected enough information and can provide a final and correct answer.\",\n", "    \"input_schema\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"final_answer\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The correct and final answer to provide the user.\",\n", "            }\n", "        },\n", "        \"required\": [\"final_answer\"],\n", "    },\n", "}\n", "\n", "\n", "def answer_simple_question(chat_client, retrieval_client, question: str):\n", "    print(\"=\" * 120)\n", "    print(f\"answer_simple_question({question})\")\n", "\n", "    retrieved_chunks = retrieval_client.retrieve(\n", "        RetrievalRequest(top_k=50, message=question)\n", "    )\n", "\n", "    system_prompt = \"\"\"\\\n", "You are a helpful assistant, designed to answer questions about the codebase.\n", "\"\"\"\n", "\n", "    user_message = \"\"\"\\\n", "I have a question about the codebase. Below are relevant snippets of code from the codebase.\n", "Consider them when answering the question.\n", "Repeat relevant code snippets and paths in your response.\n", "\"\"\"\n", "\n", "    for chunk in retrieved_chunks:\n", "        user_message += f\"Here is a code snippet from the file path `{chunk.path}`:\\n\"\n", "        user_message += f\"```\\n{chunk.text}\\n```\\n\\n\"\n", "\n", "    user_message += f\"The question:\\n{question}\\n\"\n", "\n", "    response = chat_client.messages.create(\n", "        model=model,\n", "        system=system_prompt,\n", "        messages=[{\"role\": \"user\", \"content\": user_message}],  # type: ignore\n", "        max_tokens=2048,\n", "    )\n", "\n", "    print(\"=\" * 120)\n", "    return response.content[0].text\n", "\n", "\n", "def run_agent(question, prev_dialog=[]):\n", "    print(\"=\" * 120)\n", "    print(f\"Agent answering question: {question}\")\n", "    print(\"=\" * 120)\n", "\n", "    if prev_dialog:\n", "        # Previous answer was a tool call, to we need to keep the tool call entry\n", "        dialog = list(prev_dialog)\n", "\n", "        # Add another string-type content for the question\n", "        dialog[-1][\"content\"].append({\"type\": \"text\", \"text\": question})\n", "    else:\n", "        dialog = [{\"role\": \"user\", \"content\": question}]\n", "\n", "    is_done = False\n", "\n", "    while not is_done:\n", "        print(\"DIALOG:\")\n", "        for message in dialog:\n", "            print(message)\n", "\n", "        response = chat_client.messages.create(\n", "            model=model,\n", "            system=system_prompt,\n", "            messages=dialog,  # type: ignore\n", "            max_tokens=2048,\n", "            tools=[answer_simple_question_tool, correct_final_answer_tool],  # type: ignore\n", "        )\n", "\n", "        dialog.append(\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": response.content,\n", "            }\n", "        )\n", "\n", "        has_tool_call = any(content.type == \"tool_use\" for content in response.content)\n", "\n", "        for content in response.content:\n", "            if content.type == \"tool_use\":\n", "                if content.name == \"answer_simple_question\":\n", "                    tool_result = answer_simple_question(\n", "                        chat_client, retrieval_client, content.input[\"question\"]\n", "                    )  # type: ignore\n", "                    print(tool_result)\n", "\n", "                    dialog.append(\n", "                        {\n", "                            \"role\": \"user\",\n", "                            \"content\": [\n", "                                {\n", "                                    \"type\": \"tool_result\",\n", "                                    \"tool_use_id\": content.id,\n", "                                    \"content\": tool_result,\n", "                                }\n", "                            ],\n", "                        },\n", "                    )\n", "                elif content.name == \"correct_final_answer\":\n", "                    print(\"\\nFinal answer:\")\n", "                    print(content.input[\"final_answer\"])  # type: ignore\n", "                    is_done = True\n", "\n", "                    dialog.append(\n", "                        {\n", "                            \"role\": \"user\",\n", "                            \"content\": [\n", "                                {\n", "                                    \"type\": \"tool_result\",\n", "                                    \"tool_use_id\": content.id,\n", "                                    \"content\": \"Thank you for answering the question!\",\n", "                                }\n", "                            ],\n", "                        },\n", "                    )\n", "                else:\n", "                    raise Exception(f\"Unknown tool: {content.name}\")\n", "            elif content.type == \"text\":\n", "                if has_tool_call:\n", "                    print(\"\\nChain-of-thought:\")\n", "                    print(content.text)\n", "                else:\n", "                    print(\"\\nFinal answer (without tool call):\")\n", "                    print(content.text)\n", "                    is_done = True\n", "\n", "    return dialog\n", "\n", "\n", "question = \"Implement a new production prompt formatter for the slack bot, which only looks at the last user question (and does not look at previous messages in the thread)\"\n", "prev_dialog = run_agent(question)\n", "\n", "# question = \"Are completions that are filtered out by the quality filter shown in the history panel?\"\n", "# prev_dialog = run_agent(question)\n", "\n", "# question = \"Explain the lifetime of a chat request through the system\"\n", "# prev_dialog = run_agent(question)\n", "\n", "# question = \"Explain the protocols involved in each step (HTTP, ...)\"\n", "# prev_dialog = run_agent(question, prev_dialog)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}