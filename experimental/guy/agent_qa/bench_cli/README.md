# Bench CLI

A terminal client for Bench that enables command execution through WebSocket connections.

## Features

- WebSocket-based communication with Bench server
- Command execution with real-time output streaming
- Environment-based configuration (local/production)
- Token-based authentication
- Automatic token management

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

Run the CLI with:
```bash
python -m bench_cli.cli [options]
```

### Command Line Options

- `--env {local,production}`: Environment to connect to (default: production)
- `--custom-url URL`: Custom URL to connect to
- `--debug`: Enable debug logging

### Authentication

On first run, the CLI will:
1. Generate a new authentication token
2. Save it to `~/.bench.env`
3. Provide a URL to authenticate your session

Visit the provided URL to authenticate. The token will be reused for subsequent runs.

## Architecture

- `cli.py`: Main entry point and WebSocket handling
- `config.py`: Environment and configuration management
- `auth_token.py`: Token generation and management

### Message Format

Messages use a standard format:
```json
{
    "action": "message",
    "channelId": "<token>",
    "clientType": "server",
    "message": {
        "type": "response",
        "id": "<random_id>",
        "data": {
            "output": "<command_output>",
            "error": "<error_if_any>"
        }
    }
}
```

## Development

### Running in Debug Mode

For detailed logging:
```bash
python -m bench_cli.cli --debug
```

### Custom Server

To connect to a custom server:
```bash
python -m bench_cli.cli --custom-url https://your-server.com
```
