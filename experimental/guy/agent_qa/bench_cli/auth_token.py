"""Token management for Bench CLI."""

import logging
import os
import random
import string
from pathlib import Path


logger = logging.getLogger(__name__)


class TokenManager:
    """Manages authentication tokens for Bench CLI."""

    def __init__(self):
        """Initialize token manager."""
        self.file_path = Path.home() / ".bench.env"

    def load_token(self) -> str | None:
        """Load token from config file.
        
        Returns:
            The token if found, None otherwise
        """
        if not self.file_path.exists():
            return None

        content = self.file_path.read_text()
        for line in content.splitlines():
            if line.startswith("BENCH_TOKEN="):
                token = line.split("=", 1)[1]
                logger.debug("Loaded existing token from config file")
                return token

        return None

    def save_token(self, token: str) -> None:
        """Save token to config file.
        
        Args:
            token: The token to save
        """
        content = f"BENCH_TOKEN={token}\n"
        self.file_path.write_text(content)
        logger.debug("Saved token to config file")

    @staticmethod
    def generate_token(length: int = 32) -> str:
        """Generate a new random token.
        
        Args:
            length: Length of token to generate (default: 32)
            
        Returns:
            A random token string
        """
        charset = string.ascii_letters + string.digits
        return "".join(random.choice(charset) for _ in range(length))
