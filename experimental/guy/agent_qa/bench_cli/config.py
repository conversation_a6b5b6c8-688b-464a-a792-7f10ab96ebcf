"""Configuration for Bench CLI."""

from dataclasses import dataclass
from enum import Enum, auto
from urllib.parse import urljoin
from yarl import URL


class Environment(Enum):
    """Environment settings for Bench CLI."""
    LOCAL = auto()
    PRODUCTION = auto()
    CUSTOM = auto()

    def __str__(self) -> str:
        """Return string representation of environment."""
        return self.name.lower()


@dataclass
class Config:
    """Configuration for Bench CLI."""
    base_url: URL

    @classmethod
    def new(cls, env: Environment, custom_url: str | None = None) -> "Config":
        """Create a new configuration.
        
        Args:
            env: The environment to use
            custom_url: Optional custom URL for CUSTOM environment
            
        Returns:
            Config instance
            
        Raises:
            ValueError: If CUSTOM environment is used without custom_url
        """
        if env == Environment.CUSTOM and not custom_url:
            raise ValueError("Custom environment requires a URL")

        base_url = {
            Environment.LOCAL: "http://localhost:3001",
            Environment.PRODUCTION: "https://chatbench.dev",
            Environment.CUSTOM: custom_url,
        }[env]

        return cls(base_url=URL(base_url))

    def ws_url_endpoint(self) -> str:
        """Get WebSocket URL endpoint."""
        return str(self.base_url / "api/terminal/ws-url")

    def auth_url(self, token: str) -> str:
        """Get authentication URL for a token."""
        return str(self.base_url / "auth" / token)
