"""Bench CLI - Terminal client for Bench."""

import argparse
import asyncio
import json
import logging
import os
import platform
import random
import string
import subprocess
import sys
import uuid
from dataclasses import dataclass
from pathlib import Path
from typing import Any, AsyncGenerator
from urllib.parse import urlencode

import aiohttp
import websockets
from websockets.client import WebSocketClientProtocol

from experimental.guy.agent_qa.bench_cli.config import Config, Environment
from experimental.guy.agent_qa.bench_cli.auth_token import TokenManager


logger = logging.getLogger(__name__)


def generate_message_id(length: int = 8) -> str:
    """Generate a random message ID.

    Args:
        length: Length of the ID (default: 8)

    Returns:
        A random string of specified length
    """
    chars = string.ascii_letters + string.digits
    return "".join(random.choice(chars) for _ in range(length))


@dataclass
class ConnectionMetadata:
    """Metadata for WebSocket connection."""

    version: str
    os: str
    working_dir: str
    client_type: str = "terminal"

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(
            {
                "version": self.version,
                "os": self.os,
                "working_dir": self.working_dir,
                "type": self.client_type,
            }
        )


@dataclass
class WsMessage:
    """WebSocket message format."""

    action: str
    channel_id: str
    client_type: str
    message: dict[str, Any]

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(
            {
                "action": self.action,
                "channelId": self.channel_id,
                "clientType": self.client_type,
                "message": self.message,
            }
        )


async def execute_command(command: str) -> tuple[str, str | None]:
    """Execute a shell command and return its output.

    Args:
        command: The command to execute

    Returns:
        Tuple of (output, error)
    """
    try:
        # Use shell=True to support shell features like pipes
        proc = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await proc.communicate()

        result = []
        if stdout:
            result.append("STDOUT:\\n")
            result.append(stdout.decode())
            result.append("\\n")
        if stderr:
            result.append("STDERR:\\n")
            result.append(stderr.decode())
            result.append("\\n")

        result.append(f"Command exited with status code: {proc.returncode}")
        return "".join(result), None
    except Exception as e:
        return "", str(e)


async def get_ws_url(config: Config, token: str) -> str:
    """Get WebSocket URL from server.

    Args:
        config: Configuration instance
        token: Authentication token

    Returns:
        WebSocket URL

    Raises:
        aiohttp.ClientError: If request fails
    """
    async with aiohttp.ClientSession() as session:
        async with session.get(
            config.ws_url_endpoint(),
            headers={"Authorization": f"Bearer {token}"},
        ) as resp:
            resp.raise_for_status()
            data = await resp.json()
            return data["url"]


async def handle_ws_connection(ws_url: str, token: str) -> None:
    """Handle WebSocket connection.

    Args:
        ws_url: WebSocket URL
        token: Authentication token
    """
    # Create connection metadata
    metadata = ConnectionMetadata(
        version="0.1.0",  # TODO: Get from package
        os=platform.system(),
        working_dir=str(Path.cwd()),
    )

    # Add metadata to URL
    params = {
        "clientType": "server",
        "channelId": token,
        "metadata": metadata.to_json(),
    }
    url = f"{ws_url}?{urlencode(params)}"

    async with websockets.connect(url) as websocket:
        logger.info("WebSocket connection established")

        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    logger.debug("Received message: %s", data)

                    if "message" in data and "data" in data["message"]:
                        msg_data = data["message"]["data"]
                        if "command" in msg_data:
                            command = msg_data["command"]
                            logger.info("Executing command: %s", command)

                            output, error = await execute_command(command)
                            response = WsMessage(
                                action="message",
                                channel_id=token,
                                client_type="server",
                                message={
                                    "type": "response",
                                    "id": generate_message_id(),
                                    "data": {
                                        "output": output,
                                        "error": error,
                                    },
                                    "correlationId": data["message"].get("id"),
                                },
                            )

                            await websocket.send(response.to_json())

                except json.JSONDecodeError:
                    logger.error("Failed to parse message: %s", message)
                except Exception as e:
                    logger.error("Error handling message: %s", e)

        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed by server")


async def main_async(args: argparse.Namespace) -> None:
    """Async main function.

    Args:
        args: Command line arguments
    """
    # Set up environment
    env = (
        Environment.CUSTOM
        if args.custom_url
        else getattr(Environment, args.env.upper())
    )
    config = Config.new(env, args.custom_url)
    logger.info("CLI starting up in %s environment", env)
    logger.debug("Using configuration: %s", config)

    # Set up token
    token_manager = TokenManager()
    if token := token_manager.load_token():
        logger.info("Using existing authentication token")
    else:
        token = token_manager.generate_token()
        token_manager.save_token(token)
        logger.info("Generated new authentication token")

    auth_url = config.auth_url(token)
    print(f"Please visit {auth_url} to authenticate your session")

    while True:
        try:
            ws_url = await get_ws_url(config, token)
            logger.info("WebSocket URL obtained: %s", ws_url)

            await handle_ws_connection(ws_url, token)

        except aiohttp.ClientResponseError as e:
            logger.error("Failed to get WebSocket URL: %s", e)
            if e.status == 401:
                print(f"Please visit {auth_url} to authenticate your session")

        except Exception as e:
            logger.error("Error in WebSocket connection: %s", e)

        await asyncio.sleep(5)


def main() -> None:
    """Main entry point."""
    print("Starting main()")  # Debug print
    parser = argparse.ArgumentParser(description="Bench CLI Terminal Client")
    parser.add_argument(
        "--env",
        choices=["local", "production"],
        default="production",
        help="Environment to connect to",
    )
    parser.add_argument(
        "--custom-url",
        help="Custom URL to connect to",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    args = parser.parse_args()
    print(f"Parsed args: {args}")  # Debug print

    # Set up logging
    logging.basicConfig(
        level=logging.DEBUG if args.debug else logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[logging.StreamHandler()],
    )

    try:
        asyncio.run(main_async(args))
    except KeyboardInterrupt:
        logger.info("Shutting down")
        sys.exit(0)


if __name__ == "__main__":
    main()
