#!/usr/bin/env python3
"""Test for handling ToolFormattedResult objects in chat_history."""

from research.llm_apis.llm_client import ToolFormattedResult, TextPrompt, TextResult
from research.agents.tools import Too<PERSON><PERSON>all<PERSON>ogger, LoggedLanguageModelCall
from experimental.guy.agent_qa.chat_history import (
    get_chat_history,
)


def test_tool_formatted_result():
    """Test that ToolFormattedResult objects are handled correctly."""
    # Create a tool call logger
    tool_call_logger = ToolCallLogger()

    # Create a mock LLM call with a ToolFormattedResult in the messages
    llm_call = LoggedLanguageModelCall(
        started=True,
        messages=[
            [
                TextPrompt(text="What is the output of ls?"),
                ToolFormattedResult(
                    tool_call_id="tool_1",
                    tool_name="shell",
                    tool_output="file1.txt\nfile2.txt\nfile3.txt",
                ),
            ]
        ],
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=None,
        response_metadata={},
    )

    # Add the LLM call to the logger
    tool_call_logger.logged_calls.append(llm_call)

    # Add the end of the LLM call
    end_call = LoggedLanguageModelCall(
        started=False,
        messages=[
            [
                TextPrompt(text="What is the output of ls?"),
                ToolFormattedResult(
                    tool_call_id="tool_1",
                    tool_name="shell",
                    tool_output="file1.txt\nfile2.txt\nfile3.txt",
                ),
            ]
        ],
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=[],
        response_metadata={},
        duration=0.1,
    )
    tool_call_logger.logged_calls.append(end_call)

    # Call the chat_history method
    try:
        # Create a DialogMessages object for testing
        from research.agents.tools import DialogMessages

        dialog = DialogMessages()
        # Add a simple user message and assistant response
        dialog.add_user_prompt("Test message")
        dialog.add_model_response([TextResult(text="Test response")])
        history = get_chat_history(dialog)
        print("Success! chat_history() returned without errors.")
        print(f"History: {history}")
        return True
    except Exception as e:
        print(f"Error: {e}")
        return False


if __name__ == "__main__":
    import sys

    sys.exit(0 if test_tool_formatted_result() else 1)
