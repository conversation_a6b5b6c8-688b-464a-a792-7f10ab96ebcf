"""<PERSON><PERSON><PERSON> to try next-edit location request with repository changes."""

import tempfile
from pathlib import Path
import subprocess

from base.blob_names import blob_names_pb2
from experimental.guy.agent_qa.prototyping_client import (
    AugmentPrototypingClient,
    get_dev_deployment_api_proxy_url,
    UploadContent,
)
from services.next_edit_host import next_edit_pb2
from services.next_edit_host.next_edit_pb2 import (
    NextEditLocationRequest,
    NextEditLocationResponse,
)
from research.core.diff_utils import (
    parse_git_diff_output,
    get_source_path,
    get_target_path,
)
from base.blob_names.python.blob_names import get_blob_name, encode_blob_name


def get_file_blob_name(path: str | Path) -> str:
    """Get blob name for a file path."""
    path_str = str(path)
    with open(path_str, "rb") as f:
        content = f.read()
        return get_blob_name(path_str, content)


def vcs_change_from_patch(patch: str, repo_path: Path) -> next_edit_pb2.VCSChange:
    """Convert a git diff patch to a VCSChange object."""
    changes = []

    # Parse the patch using diff_utils
    patch_set = parse_git_diff_output(patch)

    for patched_file in patch_set:
        before_path = get_source_path(patched_file)
        after_path = get_target_path(patched_file)

        if before_path is None:
            # Added file
            full_path = repo_path / after_path
            with open(full_path, "rb") as f:
                current_blob = get_blob_name(after_path, f.read())
            changes.append(
                next_edit_pb2.WorkingDirectoryChange(
                    before_path="",
                    after_path=after_path,
                    change_type=next_edit_pb2.ChangeType.ADDED,
                    head_blob_name="",
                    current_blob_name=current_blob,
                    indexed_blob_name=current_blob,
                )
            )
        elif after_path is None:
            # Deleted file
            with open(repo_path / before_path, "rb") as f:
                head_blob = get_blob_name(before_path, f.read())
            changes.append(
                next_edit_pb2.WorkingDirectoryChange(
                    before_path=before_path,
                    after_path="",
                    change_type=next_edit_pb2.ChangeType.DELETED,
                    head_blob_name=head_blob,
                    current_blob_name="",
                    indexed_blob_name="",
                )
            )
        else:
            # Modified file
            # Get content before the change
            result = subprocess.run(
                ["git", "show", "HEAD:" + str(before_path)],
                cwd=repo_path,
                capture_output=True,
            )
            if result.returncode == 0:
                head_content = result.stdout
                head_blob = get_blob_name(before_path, head_content)
            else:
                head_blob = ""

            # Get current content
            with open(repo_path / after_path, "rb") as f:
                current_content = f.read()
                current_blob = get_blob_name(after_path, current_content)

            changes.append(
                next_edit_pb2.WorkingDirectoryChange(
                    before_path=before_path,
                    after_path=after_path,
                    change_type=next_edit_pb2.ChangeType.MODIFIED,
                    head_blob_name=head_blob,
                    current_blob_name=current_blob,
                    indexed_blob_name=current_blob,
                )
            )

    return next_edit_pb2.VCSChange(working_directory_changes=changes)


def setup_test_repo():
    """Set up a test repository with math functions and rename one."""
    repo_dir = tempfile.mkdtemp()
    repo_path = Path(repo_dir)

    # Create src directory
    (repo_path / "src").mkdir()

    # Create individual math function files
    with open(repo_path / "src" / "add.py", "w") as f:
        f.write("""def add_numbers(a: float, b: float) -> float:
    \"\"\"Add two numbers together.\"\"\"
    return a + b
""")

    with open(repo_path / "src" / "subtract.py", "w") as f:
        f.write("""def subtract_numbers(a: float, b: float) -> float:
    \"\"\"Subtract b from a.\"\"\"
    return a - b
""")

    with open(repo_path / "src" / "multiply.py", "w") as f:
        f.write("""def multiply_numbers(a: float, b: float) -> float:
    \"\"\"Multiply two numbers together.\"\"\"
    return a * b
""")

    with open(repo_path / "src" / "divide.py", "w") as f:
        f.write("""def divide_numbers(a: float, b: float) -> float:
    \"\"\"Divide a by b.\"\"\"
    if b == 0:
        raise ValueError("Cannot divide by zero")
    return a / b
""")

    with open(repo_path / "src" / "power.py", "w") as f:
        f.write("""def power_numbers(base: float, exponent: float) -> float:
    \"\"\"Raise base to the given exponent.\"\"\"
    return base ** exponent
""")

    # Create main script that uses all functions
    with open(repo_path / "src" / "main.py", "w") as f:
        f.write("""from src.add import add_numbers
from src.subtract import subtract_numbers
from src.multiply import multiply_numbers
from src.divide import divide_numbers
from src.power import power_numbers

def calculate_expression(x: float, y: float) -> float:
    \"\"\"Calculate a complex expression using all operations.\"\"\"
    # First add the numbers
    result = add_numbers(x, y)

    # Multiply by their difference
    result = multiply_numbers(result, subtract_numbers(x, y))

    # Divide by their sum (if possible)
    sum_xy = add_numbers(x, y)
    if sum_xy != 0:
        result = divide_numbers(result, sum_xy)

    # Finally raise to a power
    result = power_numbers(result, 2)

    return result

# Example usage
if __name__ == "__main__":
    print(calculate_expression(5, 3))
""")

    # Initialize git repo
    result = subprocess.run(
        ["git", "init"],
        cwd=repo_dir,
        capture_output=True,
        text=True,
    )
    if result.returncode != 0:
        raise Exception(f"git init failed: {result.stderr}")

    result = subprocess.run(
        ["git", "add", "."],
        cwd=repo_dir,
        capture_output=True,
        text=True,
    )
    if result.returncode != 0:
        raise Exception(f"git add failed: {result.stderr}")

    result = subprocess.run(
        ["git", "commit", "-m", "Initial commit"],
        cwd=repo_dir,
        capture_output=True,
        text=True,
    )
    if result.returncode != 0:
        raise Exception(f"git commit failed: {result.stderr}")

    # Make the change: rename multiply_numbers to compute_product in multiply.py only
    with open(repo_path / "src" / "multiply.py", "w") as f:
        f.write("""def compute_product(a: float, b: float) -> float:
    \"\"\"Multiply two numbers together.\"\"\"
    return a * b
""")

    # Leave main.py unchanged - it will have a compilation error

    # Get the diff
    result = subprocess.run(
        ["git", "diff"],
        cwd=repo_dir,
        capture_output=True,
        text=True,
    )
    if result.returncode != 0:
        raise Exception(f"git diff failed: {result.stderr}")

    return repo_path, result.stdout


def main():
    # Set up repository and get changes
    repo_path, patch = setup_test_repo()
    print(f"Created test repository at {repo_path}")
    print("\nGit diff:")
    print(patch)

    # Convert patch to VCSChange
    vcs_change = vcs_change_from_patch(patch, repo_path)
    print("\nVCSChange:")
    for change in vcs_change.working_directory_changes:
        print(f"- {change.change_type}: {change.before_path} -> {change.after_path}")

    # Create client
    client = AugmentPrototypingClient(
        api_proxy_url=get_dev_deployment_api_proxy_url(),
    )

    # Compute blob names for all files in their final state
    print("\nComputing blob names for all files...")
    all_blob_names = []
    for file_path in repo_path.rglob("*.py"):
        if file_path.is_file():
            with open(file_path, "rb") as f:
                content = f.read()
                relative_path = str(file_path.relative_to(repo_path))
                blob_name = get_blob_name(relative_path, content)
                all_blob_names.append(blob_name)
                print(f"- {relative_path}: {blob_name}")

    # Sort blob names
    added_blobs = sorted([encode_blob_name(name) for name in all_blob_names])

    # Send next-edit location request
    print("\nSending next-edit location request...")

    response = client.next_edit_location(
        instruction="",  # No instruction
        blobs=blob_names_pb2.Blobs(
            added=added_blobs,
            deleted=[],
        ),
        mode=next_edit_pb2.NextEditMode.FOREGROUND,
        vcs_change=vcs_change,
    )

    print("\nResponse:")
    print(response)


if __name__ == "__main__":
    main()
