#!/usr/bin/env python3
"""Test WorkspaceManagerImpl snapshot and diff functions in git and non-git environments."""

import os
import tempfile
import shutil
import subprocess
from pathlib import Path
import unittest
from unittest.mock import MagicMock

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


class TestWorkspaceManagerSnapshots(unittest.TestCase):
    """Base class for testing WorkspaceManagerImpl snapshot and diff functions."""

    def setUp(self):
        """Set up a temporary workspace."""
        # Create a temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_root = Path(self.temp_dir)
        self.cache_dir = tempfile.mkdtemp()

        # Create some test files
        self.test_files = {
            "file1.txt": "Content of file1.txt",
            "file2.py": "def hello():\n    print('Hello, world!')\n",
            "subdir/file3.js": "function greet() {\n    console.log('Hello!');\n}\n",
            "subdir/nested/file4.md": "# Test Markdown\n\nThis is a test file.\n",
        }

        # Create the files with content
        for file_path, content in self.test_files.items():
            full_path = self.workspace_root / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content)

        # Create a mock AugmentPrototypingClient
        self.mock_client = MagicMock()

        # Create the WorkspaceManagerImpl
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=self.workspace_root,
            cache_root=Path(self.cache_dir),
        )

    def tearDown(self):
        """Clean up the temporary directories."""
        shutil.rmtree(self.temp_dir)
        shutil.rmtree(self.cache_dir)

    def _modify_file(self, file_path, new_content):
        """Helper method to modify a file."""
        full_path = self.workspace_root / file_path
        full_path.write_text(new_content)

    def _add_file(self, file_path, content):
        """Helper method to add a new file."""
        full_path = self.workspace_root / file_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        full_path.write_text(content)

    def _delete_file(self, file_path):
        """Helper method to delete a file."""
        full_path = self.workspace_root / file_path
        full_path.unlink()


class TestWorkspaceManagerSnapshotsNonGit(TestWorkspaceManagerSnapshots):
    """Test WorkspaceManagerImpl snapshot and diff functions in a non-git environment."""

    def test_snapshot_workspace(self):
        """Test that snapshot_workspace() creates a snapshot of the current state."""
        # Take an initial snapshot
        sequence = self.workspace_manager.snapshot_workspace()

        # Verify the sequence number
        self.assertEqual(sequence, 0, "First snapshot should have sequence number 0")

        # Modify a file
        self._modify_file("file1.txt", "Modified content")

        # Take another snapshot
        sequence = self.workspace_manager.snapshot_workspace()

        # Verify the sequence number
        self.assertEqual(sequence, 1, "Second snapshot should have sequence number 1")

        # Verify the number of snapshots
        self.assertEqual(
            self.workspace_manager.num_snapshots(), 2, "Should have 2 snapshots"
        )

    def test_revert_to_snapshot(self):
        """Test that revert_to_snapshot() reverts the workspace to a specific snapshot."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Modify a file
        original_content = self.test_files["file1.txt"]
        modified_content = "Modified content"
        self._modify_file("file1.txt", modified_content)

        # Add a new file
        new_file = "new_file.txt"
        new_content = "New file content"
        self._add_file(new_file, new_content)

        # Delete a file
        file_to_delete = "subdir/file3.js"
        self._delete_file(file_to_delete)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()  # We don't need the sequence number

        # Verify the file changes
        self.assertEqual(
            (self.workspace_root / "file1.txt").read_text(), modified_content
        )
        self.assertEqual((self.workspace_root / new_file).read_text(), new_content)
        self.assertFalse((self.workspace_root / file_to_delete).exists())

        # Revert to the initial snapshot
        self.workspace_manager.revert_to_snapshot(initial_sequence)

        # Verify the files are reverted
        self.assertEqual(
            (self.workspace_root / "file1.txt").read_text(), original_content
        )
        self.assertFalse((self.workspace_root / new_file).exists())
        self.assertTrue((self.workspace_root / file_to_delete).exists())
        self.assertEqual(
            (self.workspace_root / file_to_delete).read_text(),
            self.test_files[file_to_delete],
        )

    def test_diff_snapshots(self):
        """Test that diff_snapshots() computes the diff between two snapshots."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "file1.txt"
        original_content = self.test_files[file_to_modify]
        modified_content = "Modified content"
        self._modify_file(file_to_modify, modified_content)

        # Take another snapshot
        modified_sequence = self.workspace_manager.snapshot_workspace()

        # Get the diff between snapshots
        diff = self.workspace_manager.diff_snapshots(
            initial_sequence, modified_sequence
        )

        # Verify the diff contains the modified file
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")
        # The diff source file might have 'a/' prefix in git format
        self.assertTrue(
            diff[0].source_file == file_to_modify
            or diff[0].source_file == f"a/{file_to_modify}",
            f"Diff should be for {file_to_modify}",
        )

        # Verify the diff shows the content change
        # The diff format might vary, but should contain the modified content
        diff_str = str(diff)
        self.assertTrue(
            "-" + original_content in diff_str or original_content in diff_str,
            f"Original content not found in diff: {diff_str}",
        )
        self.assertIn("+" + modified_content, diff_str)

    def test_diff_with_current(self):
        """Test that diff_with_current() computes the diff between a snapshot and current state."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "file2.py"
        original_content = self.test_files[file_to_modify]
        modified_content = (
            "# Modified Python file\ndef hello():\n    print('Modified!')\n"
        )
        self._modify_file(file_to_modify, modified_content)

        # Get the diff between the snapshot and current state
        diff = self.workspace_manager.diff_with_current(initial_sequence)

        # Verify the diff contains the modified file
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")
        # The diff source file might have 'a/' prefix in git format
        self.assertTrue(
            diff[0].source_file == file_to_modify
            or diff[0].source_file == f"a/{file_to_modify}",
            f"Diff should be for {file_to_modify}",
        )

        # Verify the diff shows the content change
        # The diff format might vary, but should contain the modified content
        diff_str = str(diff)
        self.assertTrue(
            "-" + original_content.split("\n")[0] in diff_str
            or original_content.split("\n")[0] in diff_str,
            f"Original content not found in diff: {diff_str}",
        )
        self.assertIn("+" + modified_content.split("\n")[0], diff_str)

    def test_get_last_turn_diff(self):
        """Test that get_last_turn_diff() gets the diff between the last two snapshots."""
        # Take an initial snapshot
        self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "subdir/nested/file4.md"
        original_content = self.test_files[file_to_modify]
        modified_content = "# Modified Markdown\n\nThis file has been modified.\n"
        self._modify_file(file_to_modify, modified_content)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()

        # Get the diff between the last two snapshots
        diff = self.workspace_manager.get_last_turn_diff()

        # Verify the diff contains the modified file
        self.assertIsNotNone(diff, "Diff should not be None")
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")
        # The diff source file might have 'a/' prefix in git format
        self.assertTrue(
            diff[0].source_file == file_to_modify
            or diff[0].source_file == f"a/{file_to_modify}",
            f"Diff should be for {file_to_modify}",
        )

        # Verify the diff shows the content change
        # The diff format might vary, but should contain the modified content
        diff_str = str(diff)
        self.assertTrue(
            "-" + original_content.split("\n")[0] in diff_str
            or original_content.split("\n")[0] in diff_str,
            f"Original content not found in diff: {diff_str}",
        )
        self.assertIn("+" + modified_content.split("\n")[0], diff_str)

    def test_revert_to_initial_state(self):
        """Test that revert_to_initial_state() reverts the workspace to its initial state."""
        # Take an initial snapshot
        self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "file1.txt"
        original_content = self.test_files[file_to_modify]
        modified_content = "Modified content"
        self._modify_file(file_to_modify, modified_content)

        # Add a new file
        new_file = "new_file.txt"
        new_content = "New file content"
        self._add_file(new_file, new_content)

        # Delete a file
        file_to_delete = "subdir/file3.js"
        self._delete_file(file_to_delete)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()

        # Verify the file changes
        self.assertEqual(
            (self.workspace_root / file_to_modify).read_text(), modified_content
        )
        self.assertEqual((self.workspace_root / new_file).read_text(), new_content)
        self.assertFalse((self.workspace_root / file_to_delete).exists())

        # Revert to the initial state with skip_confirmation=True
        self.workspace_manager.revert_to_initial_state(skip_confirmation=True)

        # Verify the files are reverted
        self.assertEqual(
            (self.workspace_root / file_to_modify).read_text(), original_content
        )
        self.assertFalse((self.workspace_root / new_file).exists())
        self.assertTrue((self.workspace_root / file_to_delete).exists())
        self.assertEqual(
            (self.workspace_root / file_to_delete).read_text(),
            self.test_files[file_to_delete],
        )


class TestWorkspaceManagerSnapshotsGit(TestWorkspaceManagerSnapshots):
    """Test WorkspaceManagerImpl snapshot and diff functions in a git environment."""

    def setUp(self):
        """Set up a temporary git workspace."""
        super().setUp()

        # Initialize git repository
        subprocess.run(["git", "init"], cwd=self.workspace_root, check=True)
        subprocess.run(
            ["git", "config", "user.name", "Test User"],
            cwd=self.workspace_root,
            check=True,
        )
        subprocess.run(
            ["git", "config", "user.email", "<EMAIL>"],
            cwd=self.workspace_root,
            check=True,
        )

        # Add and commit the files
        subprocess.run(["git", "add", "."], cwd=self.workspace_root, check=True)
        subprocess.run(
            ["git", "commit", "-m", "Initial commit"],
            cwd=self.workspace_root,
            check=True,
        )

    def test_snapshot_workspace(self):
        """Test that snapshot_workspace() creates a snapshot of the current state in a git repo."""
        # Take an initial snapshot
        sequence = self.workspace_manager.snapshot_workspace()

        # Verify the sequence number
        self.assertEqual(sequence, 0, "First snapshot should have sequence number 0")

        # Modify a file
        self._modify_file("file1.txt", "Modified content")

        # Take another snapshot
        sequence = self.workspace_manager.snapshot_workspace()

        # Verify the sequence number
        self.assertEqual(sequence, 1, "Second snapshot should have sequence number 1")

        # Verify the number of snapshots
        self.assertEqual(
            self.workspace_manager.num_snapshots(), 2, "Should have 2 snapshots"
        )

    def test_revert_to_snapshot(self):
        """Test that revert_to_snapshot() reverts the workspace to a specific snapshot in a git repo."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Modify a file
        original_content = self.test_files["file1.txt"]
        modified_content = "Modified content"
        self._modify_file("file1.txt", modified_content)

        # Add a new file
        new_file = "new_file.txt"
        new_content = "New file content"
        self._add_file(new_file, new_content)

        # Delete a file
        file_to_delete = "subdir/file3.js"
        self._delete_file(file_to_delete)

        # Make sure the workspace manager is aware of all changes
        self.workspace_manager.update()

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()  # We don't need the sequence number

        # Verify the file changes
        self.assertEqual(
            (self.workspace_root / "file1.txt").read_text(), modified_content
        )
        self.assertEqual((self.workspace_root / new_file).read_text(), new_content)
        self.assertFalse((self.workspace_root / file_to_delete).exists())

        # Revert to the initial snapshot
        self.workspace_manager.revert_to_snapshot(initial_sequence)

        # Verify the files are reverted
        self.assertEqual(
            (self.workspace_root / "file1.txt").read_text(), original_content
        )
        self.assertFalse((self.workspace_root / new_file).exists())
        self.assertTrue((self.workspace_root / file_to_delete).exists())
        self.assertEqual(
            (self.workspace_root / file_to_delete).read_text(),
            self.test_files[file_to_delete],
        )

    def test_diff_snapshots(self):
        """Test that diff_snapshots() computes the diff between two snapshots in a git repo."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "file1.txt"
        original_content = self.test_files[file_to_modify]
        modified_content = "Modified content"
        self._modify_file(file_to_modify, modified_content)

        # Take another snapshot
        modified_sequence = self.workspace_manager.snapshot_workspace()

        # Get the diff between snapshots
        diff = self.workspace_manager.diff_snapshots(
            initial_sequence, modified_sequence
        )

        # Verify the diff contains the modified file
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")
        # The diff source file might have 'a/' prefix in git format
        self.assertTrue(
            diff[0].source_file == file_to_modify
            or diff[0].source_file == f"a/{file_to_modify}",
            f"Diff should be for {file_to_modify}",
        )

        # Verify the diff shows the content change
        # The diff format might vary, but should contain the modified content
        diff_str = str(diff)
        self.assertTrue(
            "-" + original_content in diff_str or original_content in diff_str,
            f"Original content not found in diff: {diff_str}",
        )
        self.assertIn("+" + modified_content, diff_str)

    def test_diff_with_current(self):
        """Test that diff_with_current() computes the diff between a snapshot and current state in a git repo."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "file2.py"
        original_content = self.test_files[file_to_modify]
        modified_content = (
            "# Modified Python file\ndef hello():\n    print('Modified!')\n"
        )
        self._modify_file(file_to_modify, modified_content)

        # Get the diff between the snapshot and current state
        diff = self.workspace_manager.diff_with_current(initial_sequence)

        # Verify the diff contains the modified file
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")
        # The diff source file might have 'a/' prefix in git format
        self.assertTrue(
            diff[0].source_file == file_to_modify
            or diff[0].source_file == f"a/{file_to_modify}",
            f"Diff should be for {file_to_modify}",
        )

        # Verify the diff shows the content change
        # The diff format might vary, but should contain the modified content
        diff_str = str(diff)
        self.assertTrue(
            "-" + original_content.split("\n")[0] in diff_str
            or original_content.split("\n")[0] in diff_str,
            f"Original content not found in diff: {diff_str}",
        )
        self.assertIn("+" + modified_content.split("\n")[0], diff_str)

    def test_get_last_turn_diff(self):
        """Test that get_last_turn_diff() gets the diff between the last two snapshots in a git repo."""
        # Take an initial snapshot
        self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "subdir/nested/file4.md"
        original_content = self.test_files[file_to_modify]
        modified_content = "# Modified Markdown\n\nThis file has been modified.\n"
        self._modify_file(file_to_modify, modified_content)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()

        # Get the diff between the last two snapshots
        diff = self.workspace_manager.get_last_turn_diff()

        # Verify the diff contains the modified file
        self.assertIsNotNone(diff, "Diff should not be None")
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")
        # The diff source file might have 'a/' prefix in git format
        self.assertTrue(
            diff[0].source_file == file_to_modify
            or diff[0].source_file == f"a/{file_to_modify}",
            f"Diff should be for {file_to_modify}",
        )

        # Verify the diff shows the content change
        self.assertIn("-" + original_content.split("\n")[0], str(diff))
        self.assertIn("+" + modified_content.split("\n")[0], str(diff))

    def test_revert_to_initial_state(self):
        """Test that revert_to_initial_state() reverts the workspace to its initial state in a git repo."""
        # Take an initial snapshot
        self.workspace_manager.snapshot_workspace()

        # Modify a file
        file_to_modify = "file1.txt"
        original_content = self.test_files[file_to_modify]
        modified_content = "Modified content"
        self._modify_file(file_to_modify, modified_content)

        # Add a new file
        new_file = "new_file.txt"
        new_content = "New file content"
        self._add_file(new_file, new_content)

        # Delete a file
        file_to_delete = "subdir/file3.js"
        self._delete_file(file_to_delete)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()

        # Verify the file changes
        self.assertEqual(
            (self.workspace_root / file_to_modify).read_text(), modified_content
        )
        self.assertEqual((self.workspace_root / new_file).read_text(), new_content)
        self.assertFalse((self.workspace_root / file_to_delete).exists())

        # Revert to the initial state with skip_confirmation=True
        self.workspace_manager.revert_to_initial_state(skip_confirmation=True)

        # Verify the files are reverted
        self.assertEqual(
            (self.workspace_root / file_to_modify).read_text(), original_content
        )
        self.assertFalse((self.workspace_root / new_file).exists())
        self.assertTrue((self.workspace_root / file_to_delete).exists())
        self.assertEqual(
            (self.workspace_root / file_to_delete).read_text(),
            self.test_files[file_to_delete],
        )

    def test_git_untracked_files(self):
        """Test that WorkspaceManagerImpl correctly handles untracked files in a git repo."""
        # Take an initial snapshot
        initial_sequence = self.workspace_manager.snapshot_workspace()

        # Add a new untracked file
        new_file = "untracked.txt"
        new_content = "This is an untracked file"
        self._add_file(new_file, new_content)

        # Make sure the workspace manager is aware of the new file
        self.workspace_manager.update()

        # Take another snapshot
        modified_sequence = self.workspace_manager.snapshot_workspace()

        # Get the diff between snapshots
        diff = self.workspace_manager.diff_snapshots(
            initial_sequence, modified_sequence
        )

        # The diff now contains the untracked file

        # Verify the diff contains the new file
        self.assertEqual(len(diff), 1, "Diff should contain 1 file")

        # For new files, the source file is /dev/null and the target file is the new file
        # The target file might have 'b/' prefix in git format
        self.assertTrue(
            diff[0].target_file == new_file or diff[0].target_file == f"b/{new_file}",
            f"Diff should be for {new_file}, but target_file is {diff[0].target_file}",
        )

        # Verify the diff shows the new content
        self.assertIn("+" + new_content, str(diff))

        # Revert to the initial snapshot
        self.workspace_manager.revert_to_snapshot(initial_sequence)

        # Verify the untracked file is removed
        self.assertFalse((self.workspace_root / new_file).exists())


if __name__ == "__main__":
    unittest.main()
