"""Tests for the process management tools v2."""

import time
from pathlib import Path
from unittest.mock import Mock

import pytest

from experimental.guy.agent_qa.process_tools_v2 import (
    ProcessTools,
    create_process_tools,
)
from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


@pytest.fixture
def mock_workspace_manager(tmp_path):
    """Create a mock workspace manager."""
    mock = Mock(spec=WorkspaceManagerImpl)
    mock.root = tmp_path
    return mock


@pytest.fixture
def mock_command_approval_manager():
    """Create a mock command approval manager."""
    mock = Mock()
    mock.get_approval.return_value = True
    return mock


@pytest.fixture
def mock_tool_call_logger():
    """Create a mock tool call logger."""
    return Mock()


@pytest.fixture
def process_tools(mock_workspace_manager, mock_command_approval_manager):
    """Create a ProcessTools instance."""
    return ProcessTools(
        workspace_manager=mock_workspace_manager,
        command_approval_manager=mock_command_approval_manager,
        ask_user_permission=False,
    )


def test_launch_process(process_tools):
    """Test launching a process."""
    # Launch a process that outputs something and exits
    result = process_tools.launch_process({"command": "echo 'test output'"})
    assert "Process launched with PID" in result.tool_output
    pid = int(result.tool_output.split()[-1])  # Extract PID from launch message
    assert pid > 0


def test_launch_process_with_cwd(process_tools, tmp_path):
    """Test launching a process with custom working directory."""
    # Create a test file in tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("test content")

    # Launch a process that reads the file
    result = process_tools.launch_process(
        {
            "command": "cat test.txt",
            "cwd": str(tmp_path),
        }
    )
    assert "Process launched with PID" in result.tool_output
    pid = int(result.tool_output.split()[-1])

    # Read output to verify it worked
    time.sleep(0.1)  # Give process time to complete
    result = process_tools.read_process({"process_id": pid})
    assert "test content" in result.tool_output


def test_kill_process(process_tools):
    """Test killing a process."""
    # Launch a long-running process
    result = process_tools.launch_process({"command": "sleep 10"})
    pid = int(result.tool_output.split()[-1])

    # Kill it
    result = process_tools.kill_process({"process_id": pid})
    assert f"Process {pid} killed" in result.tool_output

    # Try to read from it
    result = process_tools.read_process({"process_id": pid})
    assert f"Process {pid} not found" in result.tool_output


def test_read_process(process_tools):
    """Test reading from a process."""
    # Launch a process that outputs something and exits
    result = process_tools.launch_process(
        {"command": "echo 'test output' && sleep 0.1 && echo 'more output'"}
    )
    pid = int(result.tool_output.split()[-1])

    # First read should get first output
    result = process_tools.read_process({"process_id": pid})
    assert "test output" in result.tool_output
    assert "<return-code>" not in result.tool_output  # Process still running

    # Wait for process to complete and read all output
    more_output_seen = False
    return_code_seen = False
    for _ in range(50):  # Try up to 50 times (5 seconds)
        result = process_tools.read_process({"process_id": pid})

        if "more output" in result.tool_output:
            more_output_seen = True
        if "<return-code>" in result.tool_output:
            return_code_seen = True
            assert "0" in result.tool_output  # Should have exit code 0

        if more_output_seen and return_code_seen:
            break

        time.sleep(0.1)

    assert more_output_seen, "Should have seen 'more output'"
    assert return_code_seen, "Should have seen return code"


def test_write_process(process_tools):
    """Test writing to a process."""
    # Launch a process that reads from stdin
    result = process_tools.launch_process({"command": "cat"})
    pid = int(result.tool_output.split()[-1])

    # Write some input
    result = process_tools.write_process(
        {
            "process_id": pid,
            "input_text": "test input\n",
        }
    )
    assert f"Input written to process {pid}" in result.tool_output

    # Read the output
    result = process_tools.read_process({"process_id": pid})
    assert "test input" in result.tool_output


def test_list_processes(process_tools):
    """Test listing processes."""
    # Initially should have no processes
    result = process_tools.list_processes({})
    assert "No processes found" in result.tool_output

    # Launch a process
    result = process_tools.launch_process({"command": "sleep 0.5"})
    pid = int(result.tool_output.split()[-1])

    # List should show the process
    result = process_tools.list_processes({})
    assert str(pid) in result.tool_output
    assert "running" in result.tool_output.lower()

    # Wait for process to complete
    time.sleep(0.6)

    # List should show process as completed
    result = process_tools.list_processes({})
    assert str(pid) in result.tool_output
    assert "exited" in result.tool_output.lower()


def test_launch_process_with_wait_completes(process_tools):
    """Test launching a process that completes within wait time."""
    result = process_tools.launch_process(
        {
            "command": "echo 'test output' && sleep 0.1 && echo 'more output'",
            "wait": True,
            "wait_seconds": 1,
        }
    )
    assert "test output" in result.tool_output
    assert "more output" in result.tool_output
    assert "<return-code>" in result.tool_output
    assert "0" in result.tool_output  # Should have exit code 0


def test_launch_process_with_wait_exceeds(process_tools):
    """Test launching a process that exceeds wait time."""
    result = process_tools.launch_process(
        {
            "command": "sleep 2 && echo 'should not see this'",
            "wait": True,
            "wait_seconds": 0.5,
        }
    )
    assert "Command is still running" in result.tool_output
    pid = int(
        result.tool_output.split("PID ")[-1].split()[0]
    )  # Extract PID from message

    # Verify we can read and kill the process
    result = process_tools.read_process({"process_id": pid})
    assert "running" in result.tool_output.lower()

    result = process_tools.kill_process({"process_id": pid})
    assert f"Process {pid} killed" in result.tool_output


def test_launch_process_with_wait_fails(process_tools):
    """Test launching a process that fails within wait time."""
    result = process_tools.launch_process(
        {
            "command": "ls /nonexistent/path",
            "wait": True,
            "wait_seconds": 1,
        }
    )
    assert "<return-code>" in result.tool_output
    assert "0" not in result.tool_output  # Should have non-zero exit code
    assert "No such file or directory" in result.tool_output


def test_launch_process_with_wait_cwd(process_tools, tmp_path):
    """Test launching a process with wait and custom working directory."""
    # Create a test file in tmp_path
    test_file = tmp_path / "test.txt"
    test_file.write_text("test content")

    result = process_tools.launch_process(
        {
            "command": "cat test.txt",
            "wait": True,
            "wait_seconds": 1,
            "cwd": str(tmp_path),
        }
    )
    assert "test content" in result.tool_output
    assert "<return-code>" in result.tool_output
    assert "0" in result.tool_output  # Should have exit code 0


def test_launch_process_with_wait_stdin(process_tools):
    """Test launching a process that needs stdin with wait."""
    result = process_tools.launch_process(
        {
            "command": "cat",
            "wait": True,
            "wait_seconds": 1,
        }
    )
    assert "Command is still running" in result.tool_output
    pid = int(result.tool_output.split("PID ")[-1].split()[0])

    # Write some input
    result = process_tools.write_process(
        {
            "process_id": pid,
            "input_text": "test input\n",
        }
    )
    assert f"Input written to process {pid}" in result.tool_output

    # Read the output
    result = process_tools.read_process({"process_id": pid})
    assert "test input" in result.tool_output

    # Kill the process
    result = process_tools.kill_process({"process_id": pid})
    assert f"Process {pid} killed" in result.tool_output


def test_create_process_tools(
    mock_workspace_manager, mock_command_approval_manager, mock_tool_call_logger
):
    """Test creating all process tools."""
    tools = create_process_tools(
        workspace_manager=mock_workspace_manager,
        command_approval_manager=mock_command_approval_manager,
        tool_call_logger=mock_tool_call_logger,
        ask_user_permission=False,
    )

    # Should create 5 tools
    assert len(tools) == 5

    # Each tool should be properly initialized
    tool_names = {tool.name for tool in tools}
    assert tool_names == {
        "launch_process",
        "kill_process",
        "read_process",
        "write_process",
        "list_processes",
    }

    # Each tool should have proper schema
    for tool in tools:
        assert tool.input_schema is not None
        assert isinstance(tool.input_schema, dict)
        assert "type" in tool.input_schema
        assert tool.input_schema["type"] == "object"


# Remove test_create_process_tools_includes_execute since it's redundant with test_create_process_tools
