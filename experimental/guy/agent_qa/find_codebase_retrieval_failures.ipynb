{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import glob\n", "import os\n", "import readline\n", "import time\n", "from pathlib import Path\n", "\n", "from termcolor import colored\n", "\n", "from base.augment_client.client import AugmentClient\n", "from experimental.guy.agent_qa.agent import Agent\n", "from experimental.guy.agent_qa.builtin_tools import (\n", "    ClarifyTool,\n", "    CodebaseKnowledgeTool,\n", "    CodebaseRetrievalTool,\n", "    EditFileAgent,\n", "    EditFileTool,\n", "    ExecuteCommandTool,\n", "    FileEditClient,\n", "    ReadFileTool,\n", "    SaveFileTool,\n", ")\n", "from experimental.guy.agent_qa.changes import RecentChangesMode, RecentChangesProvider\n", "from experimental.guy.agent_qa.tools import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    WorkspaceManagerImpl,\n", ")\n", "from research.agents.tools import (\n", "    LoggingLLMClient,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from research.llm_apis.llm_client import (\n", "    # LLMClient,\n", "    AnthropicVertexClient,\n", ")\n", "from experimental.guy.agent_qa.prototyping_client import (\n", "    AugmentPrototypingClient,\n", "    get_dev_deployment_api_proxy_url,\n", ")\n", "\n", "auth_token_file = Path(\"/home/<USER>/.augment/token\")\n", "token = auth_token_file.read_text(\"utf8\").strip()\n", "\n", "anthropic_client = AnthropicVertexClient(model_name=\"claude-3-5-sonnet-v2@20241022\")\n", "\n", "augment_client = AugmentPrototypingClient(\n", "    get_dev_deployment_api_proxy_url(), auth_token=token\n", ")\n", "\n", "path_filter = GlobPathFilter(\"*.py\")\n", "\n", "root = Path(\"/home/<USER>/augment_agent_workspace\")\n", "\n", "workspace_manager = WorkspaceManagerImpl(augment_client, root, path_filter=path_filter)\n", "\n", "augment_public_api_client = AugmentClient(\n", "    get_dev_deployment_api_proxy_url(), token=token\n", ")\n", "file_edit_client = FileEditClient(augment_public_api_client)\n", "\n", "tool_call_logger = ToolCallLogger(\n", "    verbose=True,\n", "    use_tool_supplied_messages=True,\n", "    log_file=Path(\"/tmp/notebook_log.txt\"),\n", "    pickle_log_file=Path(\"/tmp/notebook_log.pickle\"),\n", ")\n", "\n", "logging_anthropic_client = LoggingLLMClient(\n", "    client=anthropic_client, tool_call_logger=tool_call_logger\n", ")\n", "\n", "codebase_retrieval_tool = CodebaseRetrievalTool(\n", "    tool_call_logger,\n", "    augment_client,\n", "    workspace_manager,\n", "    max_tool_chars=20000,\n", "    max_retrieval_chunks=1000,\n", "    max_result_chunks=20,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The following code sections were retrieved:\n", "Request code description: implementation of VCSChange and WorkingDirectoryChange\n", "Path: experimental/guy/agent_qa/next_edit_utils.py\n", "\"\"\"Utilities for using next-edit.\"\"\"\n", "\n", "from research.core.diff_utils import (\n", "    Repository,\n", "    compute_repo_diff,\n", "    get_source_path,\n", "    get_target_path,\n", ")\n", "from base.blob_names.python.blob_names import get_blob_name\n", "from base.datasets.next_edit import VCSChange, WorkingDirectoryChange\n", "\n", "\n", "def vcs_change_from_repo_change(\n", "    repo_before: Repository, repo_after: Repository\n", ") -> VCSChange:\n", "    \"\"\"Convert a Repository change to a VCSChange object.\"\"\"\n", "    patch = compute_repo_diff(repo_before, repo_after)\n", "    changes: list[WorkingDirectoryChange] = []\n", "\n", "    # Create maps for easier lookup\n", "    before_files = {f.path: f.contents for f in repo_before.files}\n", "    after_files = {f.path: f.contents for f in repo_after.files}\n", "\n", "    # Handle added files\n", "    for patched_file in patch.added_files:\n", "        path = get_target_path(patched_file)\n", "        if path is None:\n", "            raise ValueError(f\"Added file has no target path: {patched_file}\")\n", "        after_content = after_files[path]\n", "...\n", "                current_blob_name=\"\",\n", "                indexed_blob_name=\"\",\n", "            )\n", "        )\n", "\n", "    # Handle modified files\n", "    for patched_file in patch.modified_files:\n", "        path = get_target_path(patched_file)\n", "        if path is None:\n", "            raise ValueError(f\"Modified file has no target path: {patched_file}\")\n", "        before_content = before_files[path]\n", "        after_content = after_files[path]\n", "        head_blob = get_blob_name(path, before_content.encode())\n", "        current_blob = get_blob_name(path, after_content.encode())\n", "        changes.append(\n", "            WorkingDirectoryChange(\n", "                before_path=path,\n", "                after_path=path,\n", "                change_type=WorkingDirectoryChange.ChangeType.MODIFIED,\n", "                head_blob_name=head_blob,\n", "                current_blob_name=current_blob,\n", "                indexed_blob_name=current_blob,\n", "            )\n", "        )\n", "\n", "    return VCSChange(working_directory_changes=changes)\n", "...\n", "Path: experimental/guy/agent_qa/vcs_change_from_patch.py\n", "\"\"\"Module for converting git diff patches to VCSChange objects.\"\"\"\n", "\n", "from pathlib import Path\n", "from typing import Dict\n", "\n", "from base.blob_names.python.blob_names import get_blob_name\n", "from base.datasets.next_edit import VCSChange, WorkingDirectoryChange\n", "from research.core.diff_utils import (\n", "    parse_git_diff_output,\n", "    get_source_path,\n", "    get_target_path,\n", ")\n", "\n", "\n", "def get_file_blob_name(path: str | Path) -> str:\n", "    \"\"\"Get blob name for a file path.\"\"\"\n", "    path_str = str(path)\n", "    with open(path_str, \"rb\") as f:\n", "        content = f.read()\n", "        return get_blob_name(path_str, content)\n", "\n", "\n", "def vcs_change_from_patch(\n", "    patch: str,\n", "    repo_path: Path,\n", "    initial_blob_names: Dict[str, str] | None = None,\n", ") -> VCSChange:\n", "    \"\"\"Convert a git diff patch to a VCSChange object.\n", "\n", "    Args:\n", "        patch: The git diff output as a string\n", "        repo_path: Path to the repository root where the patch was applied\n", "        initial_blob_names: Optional dictionary mapping file paths to their blob names\n", "\n", "    Returns:\n", "        VCSChange object representing the changes in the patch\n", "    \"\"\"\n", "    # Store blob names for files we've seen\n", "    blob_names: Dict[str, str] = initial_blob_names.copy() if initial_blob_names else {}\n", "\n", "    # Parse the patch using diff_utils\n", "    patch_set = parse_git_diff_output(patch)\n", "\n", "    changes: list[WorkingDirectoryChange] = []\n", "\n", "    for patched_file in patch_set:\n", "        before_path = get_source_path(patched_file)\n", "        after_path = get_target_path(patched_file)\n", "\n", "        if before_path is None:\n", "            # Added file\n", "            full_path = repo_path / after_path\n", "            current_blob = get_file_blob_name(full_path)\n", "            blob_names[after_path] = current_blob\n", "            changes.append(\n", "                WorkingDirectoryChange(\n", "                    before_path=\"\",\n", "...\n", "        else:\n", "            # Modified file\n", "            full_path = repo_path / after_path\n", "            current_blob = get_file_blob_name(full_path)\n", "            blob_names[after_path] = current_blob\n", "            # For head blob, use stored blob name if available\n", "            head_blob = blob_names.get(before_path) or get_file_blob_name(\n", "                repo_path / before_path\n", "            )\n", "            blob_names[before_path] = head_blob\n", "            changes.append(\n", "                WorkingDirectoryChange(\n", "                    before_path=before_path,\n", "                    after_path=after_path,\n", "                    change_type=WorkingDirectoryChange.ChangeType.MODIFIED,\n", "                    head_blob_name=head_blob,\n", "                    current_blob_name=current_blob,\n", "                    indexed_blob_name=current_blob,\n", "                )\n", "            )\n", "\n", "    return VCSChange(working_directory_changes=changes)\n", "...\n", "Path: research/model_server/vcs_utils.py\n", "\"\"\"VCS utils.\n", "\n", "Utilities for working with VCS changes.\n", "\"\"\"\n", "\n", "from dataclasses import dataclass\n", "import logging\n", "from pathlib import Path\n", "import threading\n", "from typing import Callable, Iterable, Mapping\n", "\n", "from base.blob_names.python.blob_names import BlobName\n", "from research.core.changes import Added, Changed, Deleted, Modified\n", "from research.core.diff_utils import File\n", "from research.core.types import Document, DocumentId\n", "from research.core.utils import assert_eq\n", "from research.model_server.model_server_requests import (\n", "    WorkingDirectoryChange,\n", ")\n", "from research.utils.repo_change_utils import is_training_source_file\n", "\n", "\n", "@dataclass\n", "class VcsConversionResult:\n", "    file_changes: list[Changed[File]]\n", "    unknown_blob_names: set[DocumentId]\n", "    error_messages: list[str]\n", "\n", "\n", "def convert_vcs_changes(\n", "    system_lock: threading.RLock,\n", "    get_documents: Callable[[Iterable[DocumentId]], list[Document]],\n", "    new_docs: Mapping[BlobName, Document],\n", "    working_changes: list[WorkingDirectoryChange],\n", "    is_source_file: Callable[[Path], bool] = is_training_source_file,\n", ") -> VcsConversionResult:\n", "    \"\"\"Convert the working directory changes to a list of Changed[File].\n", "\n", "    Args:\n", "        get_documents: a function that fetches documents by blob names.\n", "        new_docs: a mapping from indexed blob name to updated document (with\\\n", "            recent changes applied). Although the front end sends the updated blob\\\n", "            names, we use this mapping to perform extra checking to ensure that our\\\n", "            reconstruction matches what the front end wanted to send.\n", "        working_changes: the working directory changes from the VCS.\n", "    \"\"\"\n", "\n", "    def get_document(doc_id: DocumentId) -> Document | None:\n", "        match get_documents([doc_id]):\n", "            case [doc]:\n", "                return doc\n", "            case _:\n", "...\n", "        )\n", "        if len(result.error_messages) > 0:\n", "            logging.warning(\n", "                f\"{len(result.error_messages)} conversion errors:\\n\"\n", "                + \"\\n\".join(result.error_messages)\n", "            )\n", "        return result\n", "\n", "\n", "def convert_single_vcs_change(\n", "    result: VcsConversionResult,\n", "    get_document: Callable[[DocumentId], Document | None],\n", "    new_docs: Mapping[BlobName, Document],\n", "    wdc: WorkingDirectoryChange,\n", ") -> None:\n", "    \"\"\"Convert the working directory change to a Changed[File] and add to the result.\"\"\"\n", "    if not wdc.head_blob_name:\n", "        before_file = None\n", "    else:\n", "        before_doc = get_document(wdc.head_blob_name)\n", "        if before_doc is None:\n", "            result.error_messages.append(\n", "                \"Could not find the before document: \"\n", "                f\"path={wdc.before_path}, blob_name={wdc.head_blob_name}\"\n", "            )\n", "            result.unknown_blob_names.add(wdc.head_blob_name)\n", "            return\n", "        assert_eq(before_doc.path, wdc.before_path)\n", "...\n", "Path: research/utils/repo_change_utils.py\n", "...\n", "\n", "    def to_file(self) -> File:\n", "        return File(str(self.path), self.code)\n", "\n", "\n", "class GitOperationError(Exception):\n", "    \"\"\"Exception raised when a Git operation fails.\"\"\"\n", "\n", "\n", "@dataclass(frozen=True)\n", "class RepoChange:\n", "    \"\"\"A repo level change (such as a commit) stored as two snapshots.\n", "\n", "    Note that we use the immutable PyrMap and PyrSet (instead of dict and set) to\n", "    enable efficient updates without making full copies of the snapshots.\n", "    \"\"\"\n", "\n", "    before_files: PyrMap[Path, str]\n", "    \"\"\"Maps (relative) path of each file to its content before the change.\"\"\"\n", "\n", "    after_files: PyrMap[Path, str]\n", "    \"\"\"Maps (relative) path of each file to its content after the change.\"\"\"\n", "\n", "    changed_files: PyrVector[Changed[FileTuple]]\n", "    \"\"\"Records the list of changed files, sorted from oldest to newest.\n", "\n", "    See also: `show_changed_files` to print the set of changed paths in a Git style.\n", "    \"\"\"\n", "\n", "    @staticmethod\n", "...\n", "                    file_change = Deleted(\n", "                        FileTuple(before_path, before_files[before_path])\n", "                    )\n", "                case Added(after_path):\n", "                    file_change = Added(FileTuple(after_path, after_files[after_path]))\n", "                case _:\n", "                    raise AssertionError(f\"Shouldn't happen: {path_change=}\")\n", "            changed_files.append(file_change)\n", "\n", "        return <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "            before_files=before_files,\n", "            after_files=after_files,\n", "            changed_files=pyr_vector(changed_files),\n", "        )\n", "\n", "    def before_repo(self) -> Repository:\n", "        \"\"\"Return the repository before the change.\"\"\"\n", "        return Repository(\n", "            files=[File(str(path), code) for path, code in self.before_files.items()]\n", "        )\n", "\n", "    def after_repo(self) -> Repository:\n", "        \"\"\"Return the repository before the change.\"\"\"\n", "        return Repository(\n", "            files=[File(str(path), code) for path, code in self.after_files.items()]\n", "        )\n", "\n", "...\n", "Path: experimental/guy/agent_qa/try_next_edit_location.py\n", "...\n", "    with open(path_str, \"rb\") as f:\n", "        content = f.read()\n", "        return get_blob_name(path_str, content)\n", "\n", "\n", "def vcs_change_from_patch(patch: str, repo_path: Path) -> next_edit_pb2.VCSChange:\n", "    \"\"\"Convert a git diff patch to a VCSChange object.\"\"\"\n", "    changes = []\n", "\n", "    # Parse the patch using diff_utils\n", "    patch_set = parse_git_diff_output(patch)\n", "\n", "    for patched_file in patch_set:\n", "        before_path = get_source_path(patched_file)\n", "        after_path = get_target_path(patched_file)\n", "\n", "        if before_path is None:\n", "            # Added file\n", "            full_path = repo_path / after_path\n", "            with open(full_path, \"rb\") as f:\n", "                current_blob = get_blob_name(after_path, f.read())\n", "            changes.append(\n", "                next_edit_pb2.WorkingDirectoryChange(\n", "                    before_path=\"\",\n", "                    after_path=after_path,\n", "                    change_type=next_edit_pb2.ChangeType.ADDED,\n", "                    head_blob_name=\"\",\n", "...\n", "Path: experimental/guy/agent_qa/next_edit_utils_test.py\n", "\"\"\"Tests for next_edit_utils module.\"\"\"\n", "\n", "import unittest\n", "from unittest.mock import patch\n", "\n", "from research.core.diff_utils import File, Repository\n", "from base.datasets.next_edit import WorkingDirectoryChange\n", "\n", "from experimental.guy.agent_qa.next_edit_utils import vcs_change_from_repo_change\n", "\n", "\n", "class TestVCSChangeFromRepoChange(unittest.TestCase):\n", "    \"\"\"Tests for vcs_change_from_repo_change function.\"\"\"\n", "\n", "    def setUp(self):\n", "        \"\"\"Set up test repositories.\"\"\"\n", "        # Create initial repository with some files\n", "        self.repo_before = Repository(\n", "            files=[\n", "                File(\"file1.txt\", \"original content 1\"),\n", "                File(\"file2.txt\", \"original content 2\"),\n", "                File(\"to_delete.txt\", \"will be deleted\"),\n", "            ]\n", "        )\n", "\n", "        # Create repository after changes\n", "        self.repo_after = Repository(\n", "            files=[\n", "                File(\"file1.txt\", \"modified content 1\"),  # Modified\n", "                File(\"file2.txt\", \"original content 2\"),  # Unchanged\n", "                File(\"new_file.txt\", \"new content\"),  # Added\n", "                # to_delete.txt is deleted\n", "            ]\n", "        )\n", "\n", "    def test_vcs_change_from_repo_change(self):\n", "        \"\"\"Test converting repository changes to VCSChange.\"\"\"\n", "        vcs_change = vcs_change_from_repo_change(self.repo_before, self.repo_after)\n", "        changes = vcs_change.working_directory_changes\n", "\n", "        # Should have 3 changes: modified, added, deleted\n", "        self.assertEqual(len(changes), 3)\n", "\n", "        # Find changes by type\n", "        modified = next(\n", "            c\n", "            for c in changes\n", "            if c.change_type == WorkingDirectoryChange.ChangeType.MODIFIED\n", "        )\n", "        added = next(\n", "            c\n", "            for c in changes\n", "            if c.change_type == WorkingDirectoryChange.ChangeType.ADDED\n", "        )\n", "        deleted = next(\n", "            c\n", "            for c in changes\n", "            if c.change_type == WorkingDirectoryChange.ChangeType.DELETED\n", "        )\n", "\n", "...\n", "Path: experimental/guy/agent_qa/vcs_change_from_patch_test.py\n", "...\n", "        # Create a file that will be deleted\n", "        with open(self.repo_path / \"deleted.txt\", \"w\") as f:\n", "            f.write(\"content to delete\\n\")\n", "\n", "    def test_added_file(self):\n", "        \"\"\"Test handling of an added file.\"\"\"\n", "        # Create the new file\n", "        new_file = self.repo_path / \"new.txt\"\n", "        with open(new_file, \"w\") as f:\n", "            f.write(\"new content\\n\")\n", "\n", "        patch = \"\"\"diff --git a/new.txt b/new.txt\n", "new file mode 100644\n", "index 0000000..3e75765\n", "--- /dev/null\n", "+++ b/new.txt\n", "@@ -0,0 +1 @@\n", "+new content\n", "\"\"\"\n", "\n", "        result = vcs_change_from_patch(patch, self.repo_path)\n", "\n", "        self.assertEqual(len(result.working_directory_changes), 1)\n", "        change = result.working_directory_changes[0]\n", "        self.assertEqual(change.change_type, WorkingDirectoryChange.ChangeType.ADDED)\n", "        self.assertEqual(change.after_path, \"new.txt\")\n", "        self.assertEqual(change.before_path, \"\")\n", "        self.assertNotEqual(change.current_blob_name, \"\")\n", "        self.assertEqual(change.head_blob_name, \"\")\n", "\n", "    def test_modified_file(self):\n", "        \"\"\"Test handling of a modified file.\"\"\"\n", "        # Modify the file\n", "        modified_file = self.repo_path / \"modified.txt\"\n", "        with open(modified_file, \"w\") as f:\n", "            f.write(\"modified content\\n\")\n", "\n", "        patch = \"\"\"diff --git a/modified.txt b/modified.txt\n", "index 3e75765..2e65efe 100644\n", "--- a/modified.txt\n", "+++ b/modified.txt\n", "@@ -1 +1 @@\n", "-original content\n", "+modified content\n", "\"\"\"\n", "\n", "        result = vcs_change_from_patch(patch, self.repo_path)\n", "\n", "        self.assertEqual(len(result.working_directory_changes), 1)\n", "        change = result.working_directory_changes[0]\n", "        self.assertEqual(change.change_type, WorkingDirectoryChange.ChangeType.MODIFIED)\n", "        self.assertEqual(change.after_path, \"modified.txt\")\n", "        self.assertEqual(change.before_path, \"modified.txt\")\n", "        self.assertNotEqual(change.current_blob_name, \"\")\n", "        self.assertNotEqual(change.head_blob_name, \"\")\n", "\n", "    def test_deleted_file(self):\n", "        \"\"\"Test handling of a deleted file.\"\"\"\n", "...\n", "        \"\"\"Test handling multiple file changes in one patch.\"\"\"\n", "        # Setup files\n", "        with open(self.repo_path / \"file1.txt\", \"w\") as f:\n", "            f.write(\"new content 1\\n\")\n", "        with open(self.repo_path / \"file2.txt\", \"w\") as f:\n", "            f.write(\"modified content 2\\n\")\n", "\n", "        patch = \"\"\"diff --git a/file1.txt b/file1.txt\n", "new file mode 100644\n", "index 0000000..3e75765\n", "--- /dev/null\n", "+++ b/file1.txt\n", "@@ -0,0 +1 @@\n", "+new content 1\n", "diff --git a/file2.txt b/file2.txt\n", "index 3e75765..2e65efe 100644\n", "--- a/file2.txt\n", "+++ b/file2.txt\n", "@@ -1 +1 @@\n", "-original content 2\n", "+modified content 2\n", "\"\"\"\n", "\n", "        result = vcs_change_from_patch(patch, self.repo_path)\n", "\n", "        self.assertEqual(len(result.working_directory_changes), 2)\n", "        # Check first change (added file)\n", "        self.assertEqual(\n", "            result.working_directory_changes[0].change_type,\n", "            WorkingDirectoryChange.ChangeType.ADDED,\n", "...\n", "Path: services/autofix/server/handler.py\n", "...\n", "                blobs.add(change.head_blob_name)\n", "        return blobs\n", "\n", "    @staticmethod\n", "    def _convert_vcs_changes(\n", "        vcs_change: autofix_pb2.VCSChange,\n", "        fetched_content: BlobContentMap,\n", "    ) -> list[Changed[File]]:\n", "        result: list[Changed[File]] = []\n", "        skipped_changes = []\n", "        for change in vcs_change.working_directory_changes:\n", "            if change.change_type == autofix_pb2.ChangeType.ADDED:\n", "                content = fetched_content.get(change.current_blob_name)\n", "                if content is None:\n", "                    skipped_changes.append(change)\n", "                    continue\n", "                result.append(\n", "                    Added(after=File(path=change.after_path, contents=content))\n", "                )\n", "            elif change.change_type == autofix_pb2.ChangeType.DELETED:\n", "                content = fetched_content.get(change.head_blob_name)\n", "                if content is None:\n", "                    skipped_changes.append(change)\n", "                    continue\n", "...\n", "Path: services/next_edit_host/server/next_edit_handler.py\n", "...\n", "                changes.append(Deleted(before=get_file(wd_change.head_blob_name)))\n", "            elif wd_change.change_type == next_edit_pb2.ChangeType.MODIFIED:\n", "                changes.append(\n", "                    Modified(\n", "                        before=get_file(wd_change.head_blob_name),\n", "                        after=get_file(wd_change.indexed_blob_name),\n", "                    )\n", "                )\n", "\n", "        return changes\n", "\n", "    @override\n", "    def get_current_locations(\n", "        self,\n", "        request: next_edit_pb2.NextEditLocationRequest,\n", "        request_context: RequestContext,\n", "        auth_info: AuthInfo,\n", "        executor: concurrent.futures.Executor,\n", "    ) -> next_edit_pb2.NextEditLocationResponse:\n", "        \"\"\"Get update-to-date candidate chunks for editing.\"\"\"\n", "        missing_blobs = set()\n", "\n", "        changes = self._vcs_change_to_changes(\n", "            request.vcs_change,\n", "            request_context,\n", "            missing_blobs,\n", "            auth_info,\n", "        )\n", "\n", "        prompt_input = LocalizationNextEditPromptInput(\n", "...\n", "Path: base/datasets/next_edit.py\n", "...\n", "    )\n", "\n", "\n", "@dataclass_json\n", "@dataclass(frozen=True)\n", "class VCSChange(dataclasses_json.DataClassJsonMixin):\n", "    \"\"\"Represents a VCS change.\"\"\"\n", "\n", "    working_directory_changes: list[WorkingDirectoryChange]\n", "\n", "\n", "def proto_to_vcs_change(proto: next_edit_pb2.VCSChange) -> VCSChange:\n", "    \"\"\"Converts a proto to a VCSChange.\"\"\"\n", "    return VCSChange(\n", "        working_directory_changes=[\n", "            proto_to_working_directory_change(change)\n", "            for change in proto.working_directory_changes\n", "        ],\n", "    )\n", "\n", "\n", "@dataclass_json\n", "@dataclass(frozen=True)\n", "class NextEditRequest(dataclasses_json.DataClassJsonMixin):\n", "    class NextEditMode(enum.Enum):\n", "        UNKNOWN_NEXT_EDIT_MODE = 0\n", "        BACKGROUND = 1\n", "        FOREGROUND = 2\n", "        FORCED = 3\n", "\n", "...\n", "Path: research/model_server/model_server_requests.py\n", "...\n", "\n", "@dataclass\n", "class BatchUploadRequest:\n", "    \"\"\"A request to upload a batch of blobs.\"\"\"\n", "\n", "    blobs: list[BlobPayload]\n", "    \"\"\"List of blob paths and contents.\"\"\"\n", "\n", "\n", "@dataclass\n", "class BatchUploadResponse:\n", "    \"\"\"Response to the batch upload request.\"\"\"\n", "\n", "    blob_names: list[DocumentId]\n", "    \"\"\"List of computed blob names.\"\"\"\n", "\n", "\n", "@dataclass\n", "class WorkingDirectoryChange:\n", "    \"\"\"Represent changes from the same VSCode working directory.\"\"\"\n", "\n", "    before_path: Optional[str] = None\n", "    \"\"\"The previous path in HEAD.\"\"\"\n", "    after_path: Optional[str] = None\n", "    \"\"\"The current path.\"\"\"\n", "    head_blob_name: Optional[DocumentId] = None\n", "    \"\"\"The before version in HEAD.\"\"\"\n", "    indexed_blob_name: Optional[DocumentId] = None\n", "    \"\"\"The indexed version in the current workspace.\"\"\"\n", "    current_blob_name: Optional[DocumentId] = None\n", "...\n", "More results found, but only the top results are shown.\n", "\n"]}], "source": ["response = codebase_retrieval_tool.run_impl(\n", "    {\n", "        \"code_section_requests\": [\n", "            {\"description\": \"implementation of VCSChange and WorkingDirectoryChange\"}\n", "        ]\n", "    }\n", ")\n", "\n", "print(response[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}