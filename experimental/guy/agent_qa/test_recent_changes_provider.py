import pytest
from unittest.mock import Mock, patch
from pathlib import Path
import subprocess

from experimental.guy.agent_qa.changes import RecentC<PERSON><PERSON><PERSON><PERSON><PERSON>, RecentChangesMode
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager


@pytest.fixture
def mock_workspace_manager():
    workspace_manager = Mock(spec=WorkspaceManager)
    workspace_manager.root = Path("/mock/workspace")
    return workspace_manager


@pytest.fixture
def recent_changes_provider(mock_workspace_manager):
    return Recent<PERSON><PERSON><PERSON><PERSON><PERSON>ider(mock_workspace_manager)


def test_run_git_command(recent_changes_provider):
    with patch("subprocess.run") as mock_run:
        mock_run.return_value.stdout = "mock output"
        result = recent_changes_provider._run_git_command(["git", "status"])
        assert result == "mock output"
        mock_run.assert_called_once_with(
            ["git", "status"],
            capture_output=True,
            text=True,
            check=True,
            cwd=Path("/mock/workspace"),  # Changed this line to use Path
        )


def test_run_git_command_error(recent_changes_provider):
    with patch("subprocess.run") as mock_run:
        mock_run.side_effect = subprocess.CalledProcessError(
            1, "git", stderr="mock error"
        )
        result = recent_changes_provider._run_git_command(["git", "status"])
        assert result == "Git error: mock error"


def test_get_untracked_files_diff(recent_changes_provider):
    with patch.object(
        recent_changes_provider, "_run_git_command"
    ) as mock_run_git_command:
        mock_run_git_command.return_value = "file1.py\nfile2.py"
        with patch("pathlib.Path.read_text") as mock_read_text:
            mock_read_text.side_effect = ["content1", "content2"]
            result = recent_changes_provider._get_untracked_files_diff()
            assert "diff --git a/file1.py b/file1.py" in result
            assert "diff --git a/file2.py b/file2.py" in result
            assert "+content1" in result
            assert "+content2" in result


def test_get_untracked_files_diff_name_only(recent_changes_provider):
    with patch.object(
        recent_changes_provider, "_run_git_command"
    ) as mock_run_git_command:
        mock_run_git_command.return_value = "file1.py\nfile2.py"
        result = recent_changes_provider._get_untracked_files_diff(name_only=True)
        assert result == "file1.py\nfile2.py"


def test_filter_empty_lines(recent_changes_provider):
    input_str = "line1\n\nline2\n  \nline3"
    result = recent_changes_provider._filter_empty_lines(input_str)
    assert result == "line1\nline2\nline3"


def test_get_current_branch(recent_changes_provider):
    with patch.object(
        recent_changes_provider, "_run_git_command"
    ) as mock_run_git_command:
        mock_run_git_command.return_value = "feature-branch\n"
        result = recent_changes_provider._get_current_branch()
        assert result == "feature-branch"


@pytest.mark.parametrize(
    "mode, name_only",
    [
        (RecentChangesMode.SINCE_BRANCH_POINT, False),
        (RecentChangesMode.SINCE_BRANCH_POINT, True),
        (RecentChangesMode.SINCE_LAST_COMMIT, False),
        (RecentChangesMode.SINCE_LAST_COMMIT, True),
        (RecentChangesMode.STAGED_CHANGES, False),
        (RecentChangesMode.STAGED_CHANGES, True),
        (RecentChangesMode.UNSTAGED_CHANGES, False),
        (RecentChangesMode.UNSTAGED_CHANGES, True),
    ],
)
def test_get_recent_changes(recent_changes_provider, mode, name_only):
    with patch.object(
        recent_changes_provider, "_run_git_command"
    ) as mock_run_git_command, patch.object(
        recent_changes_provider, "_get_untracked_files_diff"
    ) as mock_get_untracked_files_diff, patch.object(
        recent_changes_provider, "_get_current_branch"
    ) as mock_get_current_branch:
        mock_run_git_command.return_value = "mock changes"
        mock_get_untracked_files_diff.return_value = "mock untracked"
        mock_get_current_branch.return_value = "feature-branch"

        result = recent_changes_provider.get_recent_changes(mode, name_only)

        assert "mock changes" in result
        if mode != RecentChangesMode.STAGED_CHANGES:
            assert "mock untracked" in result
        if mode == RecentChangesMode.SINCE_BRANCH_POINT and not name_only:
            assert "Current branch name: feature-branch" in result


def test_get_recent_changes_invalid_mode(recent_changes_provider):
    result = recent_changes_provider.get_recent_changes("invalid_mode")
    assert "Invalid mode" in result
