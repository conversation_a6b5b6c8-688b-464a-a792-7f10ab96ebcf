"""Tools for managing long-running processes."""

from pathlib import Path
from typing import Any, Optional

from research.agents.tools import DialogMessages, LLMTool, ToolCallLogger, ToolImplOutput

from experimental.guy.agent_qa.process_manager import ProcessManager
from experimental.guy.agent_qa.command_approval import CommandApprovalManager
from experimental.guy.agent_qa.workspace_manager import WorkspaceManager


class ProcessManagerTool(LLMTool):
    """A tool that manages long-running processes."""

    name = "process_manager"
    description = """\
A tool for managing long-running processes. Supports:
- Launching a new process with a shell command
- Killing a process by its process ID
- Reading output from a running process
- Writing input to a process's stdin
- Listing all processes and their states

The tool returns process IDs that can be used to track and control processes.
Output is read incrementally - each read returns only new output since the last read.

Important: When running Python scripts, use python -u to disable output buffering.
For example: 'python -u script.py' instead of 'python script.py'.
This ensures the process manager can read the output in real-time.
"""

    input_schema = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "description": "The action to perform: launch, kill, read, write, or list",
                "enum": ["launch", "kill", "read", "write", "list"],
            },
            "command": {
                "type": "string",
                "description": "The command to execute. Required for launch action.",
            },
            "process_id": {
                "type": "integer",
                "description": "Process ID to kill, read from, or write to. Required for kill/read/write actions.",
            },
            "input_text": {
                "type": "string",
                "description": "Text to write to the process's stdin. Required for write action.",
            },
            "ask_user_permission": {
                "type": "boolean",
                "description": "Ask the user whether to execute the command.",
            },
            "cwd": {
                "type": "string",
                "description": "Working directory for the command. If not supplied, uses the current working directory.",
            },
        },
        "required": ["action"],
    }

    def __init__(
        self,
        tool_call_logger: ToolCallLogger,
        workspace_manager: WorkspaceManager,
        command_approval_manager: CommandApprovalManager,
        ask_user_permission: bool = True,
        cwd: Optional[Path] = None,
        char_budget: int = 100_000,
    ):
        super().__init__(tool_call_logger)
        self.workspace_manager = workspace_manager
        self.ask_user_permission = ask_user_permission
        self.cwd = cwd or workspace_manager.root
        self._command_approval_manager = command_approval_manager
        self._process_manager = ProcessManager(char_budget=char_budget)

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        action = tool_input["action"]

        # Validate required parameters based on action
        if action == "launch":
            if "command" not in tool_input:
                return ToolImplOutput(
                    "Error: command parameter is required for launch action",
                    "Missing required parameter",
                )
        elif action in ["kill", "read", "write"]:
            if "process_id" not in tool_input:
                return ToolImplOutput(
                    f"Error: process_id parameter is required for {action} action",
                    "Missing required parameter",
                )

        if action == "launch":
            command = tool_input["command"]
            if self.ask_user_permission or tool_input.get("ask_user_permission", False):
                if not self._command_approval_manager.get_approval(command):
                    return ToolImplOutput(
                        "Command not run because user did not give permission.",
                        "Not executing command",
                    )

            # Get working directory from input or use instance default
            cwd = Path(tool_input["cwd"]) if "cwd" in tool_input else self.cwd

            try:
                pid = self._process_manager.launch(command, cwd)
                return ToolImplOutput(
                    f"Process launched with PID {pid}",
                    f"Launched process {pid}",
                )
            except Exception as e:
                return ToolImplOutput(
                    f"Failed to launch process: {str(e)}",
                    "Failed to launch process",
                )

        elif action == "kill":
            pid = tool_input["process_id"]
            if self._process_manager.kill(pid):
                return ToolImplOutput(
                    f"Process {pid} killed",
                    f"Killed process {pid}",
                )
            else:
                return ToolImplOutput(
                    f"Process {pid} not found",
                    f"Process {pid} not found",
                )

        elif action == "read":
            pid = tool_input["process_id"]
            output = self._process_manager.read_output(pid)
            if output is None:
                return ToolImplOutput(
                    f"Process {pid} not found",
                    f"Process {pid} not found",
                )

            tool_answer = f"""\
Here is the output from process {pid}:

<stdout>
{output.stdout}
</stdout>

<stderr>
{output.stderr}
</stderr>
"""
            if output.return_code is not None:
                tool_answer += f"""
<return-code>
{output.return_code}
</return-code>
"""

            status = (
                "completed"
                if output.return_code is not None
                else "still running"
            )
            return ToolImplOutput(
                tool_answer,
                f"Read from process {pid} ({status})",
            )

        elif action == "write":
            pid = tool_input["process_id"]
            if "input_text" not in tool_input:
                return ToolImplOutput(
                    "Error: input_text parameter is required for write action",
                    "Missing required parameter",
                )

            if self._process_manager.write_input(pid, tool_input["input_text"]):
                return ToolImplOutput(
                    f"Input written to process {pid}",
                    f"Wrote to process {pid}",
                )
            else:
                return ToolImplOutput(
                    f"Process {pid} not found or write failed",
                    f"Failed to write to process {pid}",
                )

        elif action == "list":
            processes = self._process_manager.list_processes()
            if not processes:
                return ToolImplOutput(
                    "No processes found",
                    "No processes found",
                )

            # Format process list
            lines = []
            for proc in processes:
                status = f"{proc.state}"
                if proc.return_code is not None:
                    status += f" (return code: {proc.return_code})"
                lines.append(f"PID {proc.pid}: {proc.command} - {status}")

            tool_answer = "Here are all known processes:\n\n" + "\n".join(lines)
            return ToolImplOutput(
                tool_answer,
                f"Listed {len(processes)} processes",
            )

        return ToolImplOutput(
            f"Unknown action: {action}",
            f"Unknown action: {action}",
        )

    def get_tool_start_message(self, tool_input: dict[str, Any]) -> str:
        action = tool_input["action"]
        if action == "launch":
            return f"Launching process: {tool_input['command']}"
        elif action == "kill":
            return f"Killing process {tool_input['process_id']}"
        elif action == "read":
            return f"Reading from process {tool_input['process_id']}"
        elif action == "write":
            return f"Writing to process {tool_input['process_id']}"
        elif action == "list":
            return "Listing processes"
        else:
            return f"Unknown action: {action}"
