import json
from pathlib import Path
from typing import Any, Optional

from termcolor import colored
from research.agents.tools import (
    DialogMessages,
    LLMTool,
    ToolCallLogger,
    ToolImplOutput,
)
from research.llm_apis.llm_client import (
    AnthropicRedactedThinkingBlock,
    AnthropicThinkingBlock,
    LLMClient,
    TextPrompt,
    TextResult,
    ToolCall,
    ToolFormattedResult,
)


class Memories:
    """Proxy for managing memories."""

    def __init__(self, root_path: Path):
        self.root_path = root_path

    def get_memories(self) -> str:
        memories_path = self.root_path / "memories"
        if not memories_path.exists():
            return ""
        return memories_path.read_text()

    def set_memories(self, memories: str):
        memories = memories.strip("`\n")
        memories_path = self.root_path / "memories"
        memories_path.write_text(memories)


class RememberTool(LLMTool):
    name = "remember"
    """A tool for remembering things (creating memories)."""

    description = """\
Call this tool when user asks you:
- to remember something
- to create memory/memories
"""
    input_schema = {
        "type": "object",
        "properties": {
            "memory": {
                "type": "string",
                "description": "The concise (1 sentence) memory to remember.",
            },
        },
        "required": ["memory"],
    }

    prompt = """Here are the memories already saved:
```
{current_memories}
```

Here is the new memory to remember:
```
{new_memory}
```

Incorporate the new memory into the current memories, by adding/removing/modifying memories as needed.

Return ONLY full updated memories and nothing else.
"""

    def __init__(
        self,
        client: LLMClient,
        tool_call_logger: ToolCallLogger,
        memories: Memories,
    ):
        super().__init__(tool_call_logger)
        self.client = client
        self.memories = memories

    def run_impl(
        self,
        tool_input: dict[str, Any],
        dialog_messages: Optional[DialogMessages] = None,
    ) -> ToolImplOutput:
        if len(self.memories.get_memories()) == 0:
            self.memories.set_memories(f"- {tool_input['memory']}\n")
            return ToolImplOutput("Memory saved", "Memory saved")

        out, _ = self.client.generate(
            messages=[
                [
                    TextPrompt(
                        text=self.prompt.format(
                            current_memories=self.memories.get_memories(),
                            new_memory=tool_input["memory"],
                        )
                    )
                ]
            ],
            max_tokens=2048,
        )

        assert len(out) == 1
        assert isinstance(out[0], TextResult)

        self.memories.set_memories(out[0].text)

        return ToolImplOutput("Memory saved", "Memory saved")


def distill_memory(dialog: DialogMessages, target_msg: str, client: LLMClient) -> str:
    """Formulate the memory from the dialog and target message."""

    msg_num = len(dialog._message_lists) // 2

    prompt = f"""{dialog_to_str(dialog)}

Here is the last message from the user:
<user message_number={msg_num}>
{target_msg}
</user message_number={msg_num}>

User asks you to remember guides/instructions in the last message for long-term use.
Your task is to formulate it in a 1-sentence memory.
Return JSON with a single key "memory" containing the memory.
Return only JSON and nothing else.
"""

    out, _ = client.generate(
        messages=[[TextPrompt(text=prompt)]],
        max_tokens=2048,
    )

    assert len(out) == 1
    assert isinstance(out[0], TextResult)

    return json.loads(out[0].text)["memory"]


def classify_and_distill_memory(
    dialog: DialogMessages, target_msg: str, client: LLMClient
):
    """Classify if the target message is worth remembering and distill the memory from it."""

    msg_num = len(dialog._message_lists) // 2

    prompt = f"""{dialog_to_str(dialog)}

Here is the last message from the user:
<user message_number={msg_num}>
{target_msg}
</user message_number={msg_num}>

Your task is to detect if the last message contains some information worth remembering in long-term.
Information is worth remembering if in the last information user gives some new information, asks to do something differently or describes user preferences.
Information is only worth remembering if it might be useful in long-term and might be applicable to other tasks/situations, not just current one.

Return JSON with three keys: "explanation" (str), "worth_remembering" (bool) and "content" (str).
"explanation" should be short (1 sentence) text that describes why the information is worth remembering or not.
"content" should be short (1 sentence) text that describes the information worth remembering.
If "worth_remembering" is False, then "content" should be empty.

Return only JSON and nothing else.
"""

    out, _ = client.generate(
        messages=[[TextPrompt(text=prompt)]],
        max_tokens=2048,
        thinking_tokens=0,
    )

    assert len(out) == 1
    assert isinstance(out[0], TextResult)

    return json.loads(out[0].text)


def handle_remember_command(
    full_command: str,
    dialog: DialogMessages,
    client: LLMClient,
    memories: Memories,
    tool_call_logger: ToolCallLogger,
) -> None:
    """
    Emulates the press of a remember button in the UI.

    Form of the command: "/remember_button <shift>".

    Shift is a negative number that specifies how many instructions ago, to remember.
    -1 => previous instruction
    -2 => instruction before previous instruction
    etc.
    """
    assert dialog.is_user_turn()

    args = full_command.split(" ")
    if len(args) != 2:
        print(colored(f"Incorrect call to remember_button: '{full_command}'", "red"))
        return
    try:
        # Negative number (-1 means previous instruction, -2 means instruction before previous instruction, etc.)
        shift = int(args[1])

        message_lists = dialog.get_messages_for_llm_client()

        # Here we pop chat turns until we find the one with user input
        while shift != 0:
            cur_turn = message_lists.pop()
            user_input_turn = any(
                isinstance(message, TextPrompt) for message in cur_turn
            )
            if user_input_turn:
                shift += 1
        if shift != 0:
            print(colored(f"Failed to find {int(args[1])} turns ago", "red"))
            return

        # Create new one with messages up to the instruction
        dialog = DialogMessages()
        dialog._message_lists = message_lists

        memory = distill_memory(
            dialog,
            next(filter(lambda x: isinstance(x, TextPrompt), cur_turn)).text,  # type: ignore
            client,
        )

        remember_tool = RememberTool(
            client,
            tool_call_logger,
            memories,
        )
        remember_tool.run_impl({"memory": memory})
    except ValueError:
        print(colored(f"Invalid argument to remember_button: '{full_command}'", "red"))
    except Exception as e:
        print(colored(f"Failed to handle remember command: {e}", "red"))


def maybe_create_memory_from_instruction(
    instruction: str,
    dialog: DialogMessages,
    client: LLMClient,
    memories: Memories,
    tool_call_logger: ToolCallLogger,
    verbose: bool,
) -> None:
    message_lists = dialog.get_messages_for_llm_client()

    # We need to look for instruction, because since then new turns could've been added
    another_remember_call_found = False
    found_instruction = False
    instruction_text = ""

    # If there are no message lists, we can't proceed
    if not message_lists:
        print(
            colored(
                "[maybe_create_memory_from_instruction] No message lists found in dialog",
                "yellow",
            )
        )
        return

    # Get the most recent user turn
    cur_turn = message_lists.pop()

    # Find any text prompts in the current turn
    text_prompts = [message for message in cur_turn if isinstance(message, TextPrompt)]

    if text_prompts:
        # Use the most recent text prompt as the instruction
        instruction_text = text_prompts[-1].text
        found_instruction = True

    # We don't want to create memory from instruction if there was another remember call
    # in the same turn, so that we don't do it twice
    another_remember_call_found = any(
        isinstance(message, ToolCall) and message.tool_name == "remember"
        for message in cur_turn
    )

    if not found_instruction:
        print(
            colored(
                "[maybe_create_memory_from_instruction] No text prompt found in the most recent turn",
                "yellow",
            )
        )
        return
    if another_remember_call_found:
        if verbose:
            print(
                colored(
                    "[maybe_create_memory_from_instruction] "
                    "Another remember call found in the same turn.",
                    "yellow",
                )
            )
        return

    # Create new one with messages up to the instruction
    dialog = DialogMessages()
    dialog._message_lists = message_lists

    try:
        memories_detection = classify_and_distill_memory(
            dialog, instruction_text, client
        )
    except Exception as e:
        print(
            colored(
                f"[maybe_create_memory_from_instruction] Failed to classify and distill memory: {e}",
                "red",
            )
        )
        return

    if memories_detection["worth_remembering"]:
        print(
            colored(
                "[maybe_create_memory_from_instruction] "
                f"Model decided to create memories from previous instruction. "
                f"Memory : '{memories_detection['content']}'. "
                f"Explanation: '{memories_detection['explanation']}'",
                "yellow",
            )
        )
        remember_tool = RememberTool(
            client,
            tool_call_logger,
            memories,
        )
        remember_tool.run_impl({"memory": memories_detection["content"]})
    else:
        if verbose:
            print(
                colored(
                    "[maybe_create_memory_from_instruction] "
                    "Model decided not to create memories from previous instruction. "
                    f"Explanation: '{memories_detection['explanation']}'",
                    "yellow",
                )
            )


def dialog_to_str(dialog: DialogMessages):
    """Format the dialog messages as a string to put into prompt."""

    result = ""
    for i, message_list in enumerate(dialog.get_messages_for_llm_client()):
        if i % 2 == 0:
            result += f"<user message_number={i // 2}>\n"
            for msg in message_list:
                if isinstance(msg, TextPrompt):
                    result += msg.text + "\n"
                elif isinstance(msg, ToolFormattedResult):
                    result += f"Tool ({msg.tool_name}): {msg.tool_output}\n"
                else:
                    raise ValueError(f"Unknown message type: {type(msg)}")
            result += f"</user message_number={i // 2}>\n\n"
        else:
            result += f"<assistant message_number={i // 2}>\n"
            for msg in message_list:
                if isinstance(msg, TextResult):
                    result += msg.text + "\n"
                elif isinstance(msg, ToolCall):
                    result += f"Tool call: {msg.tool_name} {msg.tool_input}\n"
                elif isinstance(msg, AnthropicRedactedThinkingBlock):
                    result += ""
                elif isinstance(msg, AnthropicThinkingBlock):
                    result += ""
                else:
                    raise ValueError(f"Unknown message type: {type(msg)}")
            result += f"</assistant message_number={i // 2}>\n\n"
    return result
