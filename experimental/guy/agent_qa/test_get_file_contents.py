#!/usr/bin/env python3
"""Test the get_file_contents method of WorkspaceManagerImpl."""

import os
import tempfile
import shutil
from pathlib import Path
import unittest
from unittest.mock import MagicMock

from experimental.guy.agent_qa.workspace_manager import WorkspaceManagerImpl


class TestGetFileContents(unittest.TestCase):
    """Test the get_file_contents method of WorkspaceManagerImpl."""

    def setUp(self):
        """Set up a temporary workspace."""
        # Create a temporary directory
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_root = Path(self.temp_dir)
        self.cache_dir = tempfile.mkdtemp()

        # Create a test file
        self.test_file_path = self.workspace_root / "test_file.txt"
        self.test_file_content = "This is the original content."
        self.test_file_path.write_text(self.test_file_content)

        # Create a mock AugmentPrototypingClient
        self.mock_client = MagicMock()

        # Create the WorkspaceManagerImpl
        self.workspace_manager = WorkspaceManagerImpl(
            augment_client=self.mock_client,
            root=self.workspace_root,
            cache_root=Path(self.cache_dir),
        )

        # Take an initial snapshot
        self.initial_snapshot = self.workspace_manager.snapshot_workspace()

    def tearDown(self):
        """Clean up the temporary directories."""
        shutil.rmtree(self.temp_dir)
        shutil.rmtree(self.cache_dir)

    def test_get_file_contents(self):
        """Test that get_file_contents returns the correct file contents."""
        # Verify that we can get the contents of the test file from the initial snapshot
        contents = self.workspace_manager.get_file_contents(
            self.initial_snapshot, "test_file.txt"
        )
        self.assertEqual(contents, self.test_file_content)

        # Modify the test file
        new_content = "This is the modified content."
        self.test_file_path.write_text(new_content)

        # Take another snapshot
        modified_snapshot = self.workspace_manager.snapshot_workspace()

        # Verify that we can get the contents of the test file from the modified snapshot
        contents = self.workspace_manager.get_file_contents(
            modified_snapshot, "test_file.txt"
        )
        self.assertEqual(contents, new_content)

        # Verify that we can still get the original contents from the initial snapshot
        contents = self.workspace_manager.get_file_contents(
            self.initial_snapshot, "test_file.txt"
        )
        self.assertEqual(contents, self.test_file_content)

        # Verify that we can get the contents using an absolute path
        contents = self.workspace_manager.get_file_contents(
            self.initial_snapshot, self.workspace_root / "test_file.txt"
        )
        self.assertEqual(contents, self.test_file_content)

        # Verify that we get None for a file that doesn't exist
        contents = self.workspace_manager.get_file_contents(
            self.initial_snapshot, "nonexistent.txt"
        )
        self.assertIsNone(contents)

        # Verify that we get None for a path outside the workspace
        outside_path = Path("/tmp/outside.txt")
        contents = self.workspace_manager.get_file_contents(
            self.initial_snapshot, outside_path
        )
        self.assertIsNone(contents)

        # Verify that we can use negative indices to get snapshots from the end
        contents = self.workspace_manager.get_file_contents(
            -1, "test_file.txt"
        )  # Last snapshot
        self.assertEqual(contents, new_content)

        contents = self.workspace_manager.get_file_contents(
            -2, "test_file.txt"
        )  # Second-to-last snapshot
        self.assertEqual(contents, self.test_file_content)

    def test_get_file_contents_without_modifying_workspace(self):
        """Test that get_file_contents doesn't modify the workspace."""
        # Modify the test file
        new_content = "This is the modified content."
        self.test_file_path.write_text(new_content)

        # Take another snapshot
        self.workspace_manager.snapshot_workspace()  # We don't need the snapshot ID

        # Get the contents of the test file from the initial snapshot
        # This should not modify the file on disk
        _ = self.workspace_manager.get_file_contents(
            self.initial_snapshot, "test_file.txt"
        )

        # Verify that the file on disk still has the modified content
        self.assertEqual(self.test_file_path.read_text(), new_content)


if __name__ == "__main__":
    unittest.main()
