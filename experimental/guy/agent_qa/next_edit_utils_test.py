"""Tests for next_edit_utils module."""

import unittest
from unittest.mock import patch

from research.core.diff_utils import File, Repository
from base.datasets.next_edit import WorkingDirectoryChange

from experimental.guy.agent_qa.next_edit_utils import vcs_change_from_repo_change


class TestVCSChangeFromRepoChange(unittest.TestCase):
    """Tests for vcs_change_from_repo_change function."""

    def setUp(self):
        """Set up test repositories."""
        # Create initial repository with some files
        self.repo_before = Repository(
            files=[
                File("file1.txt", "original content 1"),
                File("file2.txt", "original content 2"),
                File("to_delete.txt", "will be deleted"),
            ]
        )

        # Create repository after changes
        self.repo_after = Repository(
            files=[
                File("file1.txt", "modified content 1"),  # Modified
                File("file2.txt", "original content 2"),  # Unchanged
                File("new_file.txt", "new content"),  # Added
                # to_delete.txt is deleted
            ]
        )

    def test_vcs_change_from_repo_change(self):
        """Test converting repository changes to VCSChange."""
        vcs_change = vcs_change_from_repo_change(self.repo_before, self.repo_after)
        changes = vcs_change.working_directory_changes

        # Should have 3 changes: modified, added, deleted
        self.assertEqual(len(changes), 3)

        # Find changes by type
        modified = next(
            c
            for c in changes
            if c.change_type == WorkingDirectoryChange.ChangeType.MODIFIED
        )
        added = next(
            c
            for c in changes
            if c.change_type == WorkingDirectoryChange.ChangeType.ADDED
        )
        deleted = next(
            c
            for c in changes
            if c.change_type == WorkingDirectoryChange.ChangeType.DELETED
        )

        # Test modified file
        self.assertEqual(modified.before_path, "file1.txt")
        self.assertEqual(modified.after_path, "file1.txt")
        self.assertNotEqual(modified.head_blob_name, modified.current_blob_name)
        self.assertEqual(modified.indexed_blob_name, modified.current_blob_name)

        # Test added file
        self.assertEqual(added.before_path, "")
        self.assertEqual(added.after_path, "new_file.txt")
        self.assertEqual(added.head_blob_name, "")
        self.assertNotEqual(added.current_blob_name, "")
        self.assertEqual(added.indexed_blob_name, added.current_blob_name)

        # Test deleted file
        self.assertEqual(deleted.before_path, "to_delete.txt")
        self.assertEqual(deleted.after_path, "")
        self.assertNotEqual(deleted.head_blob_name, "")
        self.assertEqual(deleted.current_blob_name, "")
        self.assertEqual(deleted.indexed_blob_name, "")

    def test_unchanged_file_not_included(self):
        """Test that unchanged files are not included in the changes."""
        vcs_change = vcs_change_from_repo_change(self.repo_before, self.repo_after)
        changes = vcs_change.working_directory_changes

        # Verify file2.txt (unchanged) is not in the changes
        unchanged_paths = [c.after_path for c in changes if c.after_path == "file2.txt"]
        self.assertEqual(len(unchanged_paths), 0)

    def test_blob_names_are_consistent(self):
        """Test that blob names are consistent for same content."""
        # Create two repositories with same file content
        repo1 = Repository(files=[File("test.txt", "same content")])
        repo2 = Repository(files=[File("test.txt", "same content")])

        vcs_change1 = vcs_change_from_repo_change(repo1, repo2)

        # Create new repositories with same content
        repo3 = Repository(files=[File("test.txt", "same content")])
        repo4 = Repository(files=[File("test.txt", "same content")])

        vcs_change2 = vcs_change_from_repo_change(repo3, repo4)

        # No changes should be reported since content is same
        self.assertEqual(len(vcs_change1.working_directory_changes), 0)
        self.assertEqual(len(vcs_change2.working_directory_changes), 0)

    def test_none_path_handling(self):
        """Test that None paths are handled properly."""
        with patch(
            "experimental.guy.agent_qa.next_edit_utils.get_target_path",
            return_value=None,
        ):
            with self.assertRaises(ValueError) as cm:
                vcs_change_from_repo_change(self.repo_before, self.repo_after)
            self.assertIn("has no target path", str(cm.exception))


if __name__ == "__main__":
    unittest.main()
