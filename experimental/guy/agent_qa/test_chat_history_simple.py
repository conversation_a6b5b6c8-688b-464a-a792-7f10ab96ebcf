#!/usr/bin/env python3
"""
Simple test for the chat_history method in interactive_agent.py.
"""

import json

from experimental.guy.agent_qa.chat_history import (
    get_chat_history,
)
from research.agents.tools import (
    ToolCallLogger,
    LoggedLanguageModelCall,
    LoggedToolCall,
    ToolFormattedResult,
)
from research.llm_apis.llm_client import Text<PERSON>rom<PERSON>, TextResult, ToolCall
from services.api_proxy.public_api_pb2 import ChangedFile
from unittest.mock import Mock


class CLIInterface:
    """Mock CLIInterface for testing."""

    def __init__(self):
        self.tool_call_logger = ToolCallLogger()
        from research.agents.tools import DialogMessages

        self.agent = Mock()
        self.agent.dialog = DialogMessages()

    def chat_history(self) -> dict:
        """Build a RemoteAgentChatHistoryResponse proto from the DialogMessages object."""
        return get_chat_history(self.agent.dialog)


def get_logged_tool_call(started=True):
    return LoggedToolCall(
        started=started,
        tool=Mock(),
        tool_input=Mock(),
        tool_output=Mock(),
        tool_message=Mock(),
    )


def get_logged_llm_call(started=True, messages=None, response=None):
    return LoggedLanguageModelCall(
        started=started,
        messages=messages or [],
        max_tokens=1000,
        system_prompt="You are a helpful assistant.",
        temperature=0.0,
        tools=[],
        tool_choice=None,
        response=response,
        response_metadata={},
    )


def test_chat_history_with_sequential_ids():
    """Test that the chat_history method correctly assigns sequential IDs based on array indices."""
    # Create a CLI interface
    cli = CLIInterface()

    # Add messages to DialogMessages to simulate a conversation

    # First user message
    cli.agent.dialog.add_user_prompt("Hello, world!")

    # First assistant response
    cli.agent.dialog.add_model_response(
        [TextResult(text="Hi there! How can I help you?")]
    )

    # Second user message
    cli.agent.dialog.add_user_prompt("What's the weather like?")

    # Tool call in response
    cli.agent.dialog.add_model_response(
        [
            ToolCall(
                tool_call_id="123",
                tool_name="weather",
                tool_input={"location": "San Francisco"},
            )
        ]
    )

    # Tool call result
    from research.agents.tools import ToolCallParameters

    cli.agent.dialog.add_tool_call_result(
        parameters=ToolCallParameters(
            tool_call_id="123",
            tool_name="weather",
            tool_input={"location": "San Francisco"},
        ),
        result="Sunny, 75°F",
    )

    # Final assistant response
    cli.agent.dialog.add_model_response(
        [TextResult(text="Great weather in San Francisco today!")]
    )

    # Get the chat history
    history = cli.chat_history()

    # Print the history for debugging
    print(json.dumps(history, indent=2))

    # Verify that we have the expected number of exchanges
    assert "chat_history" in history, "chat_history field missing"
    assert (
        len(history["chat_history"]) == 3
    ), f"Expected 3 exchanges, got {len(history['chat_history'])}"

    print("Test passed!")


if __name__ == "__main__":
    test_chat_history_with_sequential_ids()
