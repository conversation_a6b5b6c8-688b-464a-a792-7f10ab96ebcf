"""A decorator for running python functions in a subprocess.

Source, MIT license: https://gist.github.com/flbraun/8199fc1a41c2107982053aba809838c6
"""

from functools import wraps
from multiprocessing import Process, Queue
import sys
import traceback


def processify(func):
    """Decorator to run a function as a process.

    Be sure that every argument and the return value is pickable. The created
    process is joined, so the code does not run in parallel.
    """

    def process_func(q, *args, **kwargs):
        try:
            ret = func(*args, **kwargs)
        except Exception:  # pylint: disable=broad-except
            ex_type, ex_value, tb = sys.exc_info()
            error = ex_type, ex_value, "".join(traceback.format_tb(tb))
            ret = None
        else:
            error = None

        q.put((ret, error))

    # register original function with different name
    # in sys.modules so it is pickable
    process_func.__name__ = func.__name__ + "processify_func"
    setattr(sys.modules[__name__], process_func.__name__, process_func)

    @wraps(func)
    def wrapper(*args, **kwargs):
        q = Queue()
        p = Process(target=process_func, args=(q,) + args, kwargs=kwargs)
        p.start()
        p.join()

        if p.exitcode != 0:
            raise RuntimeError(f"Subprocess failed with return code {p.exitcode}")

        ret, error = q.get()

        if error:
            exc_type, exc_value, tb_str = error
            message = f"{exc_value} (in subprocess)\n{tb_str}"
            raise exc_type(message)

        return ret

    return wrapper
