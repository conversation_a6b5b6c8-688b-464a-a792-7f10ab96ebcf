import argparse
from research.llm_apis.chat_utils import Llama3ChatClient


def main():
    parser = argparse.ArgumentParser(description="Health check script for servers")
    parser.add_argument(
        "addresses", nargs="+", help="List of server addresses (e.g., ********:8000)"
    )
    args = parser.parse_args()

    addresses = args.addresses

    print("Running health checks...")
    for address in addresses:
        try:
            client = Llama3ChatClient("triton", address=address, timeout=10)
            response = client.generate(messages=["hello"], max_tokens=32)
            print(f"Health check for {client.address}: {response}")
        except Exception as e:  # pylint: disable=broad-except
            print(f"Health check for {address} failed: {e}")
    print("Health check completed.")


if __name__ == "__main__":
    main()
