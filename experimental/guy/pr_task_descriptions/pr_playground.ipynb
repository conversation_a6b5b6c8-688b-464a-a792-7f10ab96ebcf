{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas\n", "\n", "path = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr-instructions-v0/pr-instructions\")\n", "\n", "df = pandas.read_parquet(path)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages.diff_utils import File, Repository, compute_repo_diff, apply_diff, diff_subset, parse_git_diff_output\n", "\n", "repo1 = Repository(files=[\n", "    File(path=\"a.txt\", contents=\"1\\n2\\n3\\n4\\n\"),\n", "])\n", "\n", "repo2 = Repository(files=[\n", "    File(path=\"a.txt\", contents=\"extra line\\n1\\n2\\n3\\n4\\n\"),\n", "])\n", "\n", "diff = compute_repo_diff(repo1, repo2, num_context_lines=0)\n", "print(diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas\n", "from pathlib import Path\n", "from unidiff import PatchSet, PatchedFile\n", "\n", "\n", "from research.data.spark.pipelines.stages.diff_utils import File, Repository, compute_repo_diff, apply_diff, diff_subset, parse_git_diff_output\n", "\n", "\n", "directory = Path(\"/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location/joined-prs-and-repos/\")\n", "paths = directory.glob(\"*.parquet\")\n", "# path = next(directory.glob(\"*.parquet\"))\n", "# df = pandas.read_parquet(path)\n", "\n", "hunk_counts = []\n", "\n", "for i, path in enumerate(paths):\n", "    if i > 30:\n", "        break\n", "    df = pandas.read_parquet(path)\n", "\n", "    for pr in df.to_dict(orient=\"records\"):\n", "        past_to_future_diff = parse_git_diff_output(pr[\"pr_diff\"])\n", "        num_hunks = 0\n", "\n", "        for file in past_to_future_diff:\n", "            num_hunks += len(file)\n", "\n", "        hunk_counts.append(num_hunks)\n", "\n", "    # print(\"Avg hunks per PR:\", sum(hunk_counts) / len(hunk_counts))\n", "\n", "    # prs_with_one_hunk = len([hunk_count for hunk_count in hunk_counts if hunk_count == 1])\n", "    # prs_with_zero_or_one_hunk = len([hunk_count for hunk_count in hunk_counts if hunk_count <= 1])\n", "    # print(f\"After {i+1} files: In {len(hunk_counts)} PRs, found {prs_with_one_hunk} with one hunk, or {prs_with_one_hunk/len(hunk_counts)*100:.2f}%\")\n", "    # print(f\"After {i+1} files: In {len(hunk_counts)} PRs, found {prs_with_zero_or_one_hunk} with <=1 hunk, or {prs_with_zero_or_one_hunk/len(hunk_counts)*100:.2f}%\")\n", "\n", "import matplotlib.pyplot as plt\n", "plt.hist(hunk_counts, bins=list(range(50)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sum([h for h in hunk_counts if h <4 ]) / len(hunk_counts)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas\n", "from pathlib import Path\n", "from unidiff import PatchSet, PatchedFile\n", "\n", "\n", "from research.data.spark.pipelines.stages.diff_utils import File, Repository, compute_repo_diff, apply_diff, diff_subset, parse_git_diff_output\n", "\n", "\n", "directory = Path(\"/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location/joined-prs-and-repos/\")\n", "paths = directory.glob(\"*.parquet\")\n", "# path = next(directory.glob(\"*.parquet\"))\n", "# df = pandas.read_parquet(path)\n", "\n", "total = 0\n", "failed = 0\n", "\n", "for path in paths:\n", "    df = pandas.read_parquet(path)\n", "\n", "    for pr in df.to_dict(orient=\"records\"):\n", "        past_repo = Repository(files=[\n", "            File(path=file[\"path\"], contents=file[\"content\"])\n", "            for file in pr[\"files\"]\n", "        ])\n", "\n", "        past_to_future_diff = parse_git_diff_output(pr[\"pr_diff\"])\n", "\n", "        total += 1\n", "\n", "        try:\n", "            future_repo = apply_diff(past_repo, past_to_future_diff)\n", "        except ValueError as exc:\n", "            failed += 1\n", "\n", "            root = Path(\"/tmp/bad_repos\")\n", "            root.mkdir(parents=True, exist_ok=True)\n", "            repo_root = root / f\"{failed}\"\n", "            past_repo.save(repo_root / \"repo\")\n", "            Path(repo_root / \"pr_diff.txt\").write_text(pr[\"pr_diff\"])\n", "            Path(repo_root / \"pr_diff_processed.txt\").write_text(str(past_to_future_diff))\n", "            Path(repo_root / \"info.json\").write_text(json.dumps(\n", "                {\"repo_name\": pr[\"repo_name\"], \"id\": pr[\"id\"], \"title\": pr[\"title\"], \"number\": pr[\"number\"]},\n", "                indent=2,\n", "            ))\n", "\n", "            # print(\"failed diff files:\")\n", "            # for file in past_to_future_diff:\n", "            #     print(file.path)\n", "            #     print(f\"\\tbefore: {file.source_file}\")\n", "            #     print(f\"\\tafter: {file.target_file}\")\n", "            # print(\"\\nfailed diff:\")\n", "            # # print(pr[\"pr_diff\"])\n", "            # for i, line in enumerate(str(past_to_future_diff).splitlines()):\n", "            #     print(f\"{i:03d}: {line}\")\n", "\n", "            # print(exc)\n", "\n", "            # raise\n", "    print(f\"File {path} done: Failed {failed} out of {total} ({failed/total:.2%})\")\n", "\n", "print(f\"Failed {failed} out of {total} ({failed/total:.2%})\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unidiff import PatchSet\n", "\n", "diff_str = row[\"pr_diff\"]\n", "# print(diff_str)\n", "patch_set = PatchSet(diff_str)\n", "\n", "# print(patch_set)\n", "# str(patch_set) == diff_str\n", "print(len(patch_set))\n", "\n", "for patch_file in patch_set:\n", "    # print(type(patch_file))\n", "    # for item in dir(patch_file):\n", "    #     print(item)\n", "    print(len(patch_file))\n", "    print(patch_file[0])\n", "    another = PatchedFile(patch_file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unidiff import PatchSet, PatchedFile\n", "diff_str = \"\"\"\\\n", "--- a.txt       2024-03-16 02:06:20.650349295 +0000\n", "+++ b.txt       2024-03-16 02:06:33.690338259 +0000\n", "@@ -3,2 +3,5 @@\n", " 3\n", "+A line\n", "+A line\n", "+A line\n", " 4\n", "@@ -7,2 +10,4 @@\n", " 7\n", "+B line\n", "+B line\n", " 8\n", "\"\"\"\n", "\n", "patch_set = PatchSet(diff_str)\n", "\n", "for patch_file in patch_set:\n", "    for hunk in patch_file:\n", "        print(\"hunk:\", type(hunk))\n", "        print(hunk)\n", "\n", "hunk.target_start -= 3\n", "print(\"updated hunk:\")\n", "print(hunk)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["l = [1, 2, 3]\n", "help(l.pop)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["git_diff = \"\"\"\\\n", "diff --git a/a/a.txt b/b/a.txt\n", "index 8a1218a..6310241 100644\n", "--- a/a/a.txt\n", "+++ b/b/a.txt\n", "@@ -1,5 +1,10 @@\n", " 1\n", " 2\n", "+A line\n", "+A line\n", "+A line\n", " 3\n", " 4\n", " 5\n", "+B line\n", "+B line\n", "diff --git a/a/b.txt b/b/b.txt\n", "index 6b34e8b..36aef8f 100644\n", "--- a/a/b.txt\n", "+++ b/b/b.txt\n", "@@ -1,4 +1,7 @@\n", "+C line\n", " foo\n", " bar\n", "-hello\n", "+D line\n", "+D line\n", "+D line\n", " world\n", "diff --git a/a/c.txt b/b/c_renamed.txt\n", "similarity index 100%\n", "rename from a/c.txt\n", "rename to b/c_renamed.txt\n", "diff --git a/a/foo b/a/foo\n", "deleted file mode 100755\n", "index ea6b90b..0000000\n", "Binary files a/a/foo and /dev/null differ\n", "\"\"\"\n", "\n", "patch_set = PatchSet(git_diff)\n", "\n", "for file in patch_set:\n", "    print(file.source_file, file.target_file, file.path, file.is_rename, file.is_binary_file)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["diff = \"\"\"\\\n", "diff --git a/a.txt b/a.txt\n", "index 8a1218a..6310241 100644\n", "--- a/a.txt\n", "+++ b/a.txt\n", "@@ -2,2 +2,5 @@\n", " 2\n", "+A line\n", "+A line\n", "+A line\n", " 3\n", "@@ -5 +8,3 @@\n", " 5\n", "+B line\n", "+B line\n", "diff --git a/b.txt b/b.txt\n", "index 6b34e8b..36aef8f 100644\n", "--- a/b.txt\n", "+++ b/b.txt\n", "@@ -1,4 +1,7 @@\n", "+C line\n", " foo\n", " bar\n", "-hello\n", "+D line\n", "+D line\n", "+D line\n", " world\n", "diff --git a/c.txt b/c_renamed.txt\n", "similarity index 100%\n", "rename from a/c.txt\n", "rename to b/c_renamed.txt\n", "diff --git a/foo a/foo\n", "deleted file mode 100755\n", "index ea6b90b..0000000\n", "Binary files a/foo and /dev/null differ\n", "\"\"\"\n", "\n", "ps = PatchSet(diff)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for p in ps:\n", "    if p.path == \"c_renamed.txt\":\n", "        print(len(p))\n", "        for hunk in p:\n", "            print(hunk)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unidiff import PatchSet, PatchedFile\n", "\n", "diff = \"\"\"\\\n", "diff --git a/a.txt b/a.txt\n", "index 8a1218a..6310241 100644\n", "--- a/a.txt\n", "+++ b/a.txt\n", "@@ -1,5 +1,10 @@\n", " 1\n", " 2\n", "+A line\n", "+A line\n", "+A line\n", " 3\n", " 4\n", " 5\n", "+B line\n", "+B line\n", "diff --git a/b.txt b/b.txt\n", "index 6b34e8b..36aef8f 100644\n", "--- a/b.txt\n", "+++ b/b.txt\n", "@@ -1,4 +1,7 @@\n", "+C line\n", " foo\n", " bar\n", "-hello\n", "+D line\n", "+D line\n", "+D line\n", " world\n", "diff --git a/c.txt b/c_renamed.txt\n", "similarity index 100%\n", "rename from a/c.txt\n", "rename to b/c_renamed.txt\n", "\"\"\"\n", "\n", "ps = PatchSet(diff)\n", "for pf in ps:\n", "    print(f\"{pf.path} has {len(pf)} hunks\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir(pf)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["str(PatchSet(\"\"))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import numpy as np\n", "\n", "data = [\n", "    {\"a\": \"foo\", \"b\": \"bar\"},\n", "    {\"a\": \"foo2\", \"b\": \"bar2\"},\n", "]\n", "df = pandas.DataFrame(data)\n", "\n", "for row in df.to_dict(orient=\"records\"):\n", "    print(row)\n", "    print(type(row))\n", "\n", "for _ in range(10):\n", "    print(np.random.rand())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.iter\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unidiff.patch import PatchInfo\n", "\n", "diff = \"\"\"\\\n", "diff --git a/xyz.txt b/xyz_renamed.txt\n", "similarity index 100%\n", "rename from a/xyz.txt\n", "rename to b/xyz_renamed.txt\n", "\"\"\"\n", "\n", "ps = PatchSet(diff)\n", "\n", "for pf in ps:\n", "    print(len(pf))\n", "    print(pf.source_file)\n", "    print(pf.target_file)\n", "    print(pf.is_rename)\n", "    pf.source_file = \"xyz.txt\"\n", "    pf.target_file = \"xyz_renamed.txt\"\n", "    print(\"after:\")\n", "    print(pf.source_file)\n", "    print(pf.target_file)\n", "\n", "    print(f\"patch info: type={type(pf.patch_info)}\")\n", "    print(pf.patch_info)\n", "\n", "    def fix_patch_info_line(line):\n", "        line = line.replace(\"rename from a/\", \"rename from \")\n", "        line = line.replace(\"rename to b/\", \"rename to \")\n", "        return line\n", "\n", "    new_patch_info = PatchInfo([\n", "        fix_patch_info_line(line) for line in pf.patch_info\n", "    ])\n", "    pf.patch_info = new_patch_info\n", "\n", "    for hunk in pf:\n", "        print(\"hunk:\", line)\n", "\n", "print(\"\\nPatchSet after:\")\n", "print(ps[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "\n", "directory = Path(\"/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location/prs-with-simulated-diff-subsets/\")\n", "path = next(directory.glob(\"*.parquet\"))\n", "\n", "print(path)\n", "df = pandas.read_parquet(path)\n", "print(df.columns)\n", "\n", "row = df.iloc[1]\n", "print(len(row.wip_files))\n", "print(len(row.wip_chunks))\n", "\n", "# print(row.wip_files)\n", "\n", "for chunk in row.wip_chunks:\n", "    if chunk[\"overlaps_with_future_changes\"]:\n", "        print(chunk)\n", "\n", "\n", "print(row.wip_to_future_diff)\n", "\n", "# for pr in df.to_dict(orient=\"records\"):\n", "#     print(pr[\"pr_diff\"])\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}