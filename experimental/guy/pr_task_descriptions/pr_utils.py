"""Utilities for generating synthetic PR-related data."""

import json
import re
from pathlib import Path
from dataclasses import dataclass
from typing import Optional
from dataclasses_json import DataClassJsonMixin
import random

from research.data.synthetic_code_edit.api_lib import <PERSON><PERSON><PERSON><PERSON><PERSON>, OpenAIChatModels
from research.llm_apis.chat_utils import ChatClient


def is_bad_sample(sample):
    if "pr_diff" not in sample or sample["pr_diff"] is None:
        return True
    elif not sample["pr_diff"].startswith("diff"):
        return True

    if not sample["title"] or not sample["body"]:
        return True

    return False


def extract_instructions(text: str) -> list[str]:
    """Extract the instructions from the given text.

    Text has the form:
    1. ... instruction 1 ...
    2. ... instruction 2 ...
    3. ... instruction 3 ...

    Each instruction can span multiple lines.
    """
    pattern = re.compile(r"^(\d+)\.\s(.*?)(?=\n\d+\.\s|$)", re.DOTALL | re.MULTILINE)
    return [match.group(2) for match in pattern.finditer(text)]


def extract_paragraph_before_instructions(text: str) -> Optional[str]:
    match = re.search(r"^(.*)\n1\.\s", text, re.DOTALL | re.MULTILINE)
    if match:
        return match.group(1)
    return None


def test_extract_instructions():
    text = """\
1. ... instruction 1 ...
2. ... instruction 2 ...
3. ... instruction 3 ...
"""
    assert extract_instructions(text) == [
        "... instruction 1 ...",
        "... instruction 2 ...",
        "... instruction 3 ...",
    ]


def test_extract_instructions_with_extra_lines():
    text = """\
extra
lines
1. ... instruction 1 ...
2. ... instruction 2 ...
3. ... instruction 3 ...
extra
lines
"""
    assert extract_instructions(text) == [
        "... instruction 1 ...",
        "... instruction 2 ...",
        "... instruction 3 ...",
    ]


def test_extra_instructions_real_data():
    text = """\
Paragraph-length description: The `watch.js` file now handles the heartbeat separately, allowing for a more modular approach to checking if WatchJS is present on the page. This reduces coupling and improves testability.
    Instructions:
    1. extract watcher loop, unit tests 2. upd8 unit tsts
3. add unwatch fxn to agent
"""
    assert extract_instructions(text) == [
        "extract watcher loop, unit tests",
        "upd8 unit tsts",
        "add unwatch fxn to agent",
    ]


def test_extract_paragraph_before_instructions():
    text = """\
Paragraph before instructions.

1. ... instruction 1 ...
2. ... instruction 2 ...
3. ... instruction 3 ...

Paragraph after instructions.
"""
    actual = extract_paragraph_before_instructions(text)
    assert actual.strip() == "Paragraph before instructions.", actual


# HTML utils
def diff_to_html(diff_text: str) -> str:
    html = """<pre class="code-snippet">\n"""
    lines = diff_text.splitlines(keepends=False)

    for line in lines:
        if line.startswith("@@") or line.startswith("diff") or line.startswith("index"):
            html += f"<span class='header'>{line}</span>"
        elif line.startswith("+"):
            html += f"<span class='plus'>{line}</span>"
        elif line.startswith("-"):
            html += f"<span class='minus'>{line}</span>"
        else:
            html += f"{line}"
        html += "\n"

    html += "\n</pre>"
    return html


# Function to convert JSON to HTML
def json_to_html(json_array):
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>PR Instructions</title>
</head>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 20px;
            background-color: #f9f9f9;
        }
        .sample {
            background-color: #ffffff;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
        }
        h3 {
            color: #555;
        }
        ul {
            list-style-type: none;
            padding: 0;
        }
        li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        li:last-child {
            border-bottom: none;
        }
        p, li {
            color: #666;
            line-height: 1.6;
        }
        .code-snippet {
            font-family: 'Courier New', Courier, monospace; /* Specific font for the code snippet */
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            white-space: pre-wrap;
            overflow-x: auto; /* Ensures code lines do not overflow the container */
        }
        .code-snippet .plus {
            color: green;
        }
        .code-snippet .minus {
            color: red;
        }
        .code-snippet .header {
            color: black;
            font-weight: bold;
        }
        ul.custom-bullets {
            list-style-type: disc; /* Ensures bullets are shown. Change 'disc' to other styles like 'circle', 'square', etc., if preferred */
            margin-left: 20px; /* Standard indent for bullets */
        }
    </style>
<body>
    <h2>PR Instructions</h2>
    <div id="jsonData">
"""

    for item in json_array:
        html_content += f"<div class='sample'><h1>Sample {item['id']}</h1><ul><li><b>Title:</b> {item['title']}<li><b>Body:</b> {item['body']}"
        if "description_paragraph" in item:
            html_content += f"<li><b>Generated paragraph-length description:</b> {item['description_paragraph']}"
        if "raw_label" in item:
            html_content += (
                f"<li><b>Raw generated label:</b> <pre>{item['raw_label']}</pre>"
            )
        html_content += "</ul>"

        html_content += "<h2>Instructions</h2>\n<p>"
        for group in item["instructions"]:
            html_content += f"<h3>{group}</h3>\n<ul class='custom-bullets'>"
            for instruction in item["instructions"][group]:
                html_content += f"<li>{instruction}</li>"
            html_content += "</ul>"

        # html_content += f"<h3>Diff</h3>\n<pre><code>{item['pr_diff']}</pre></code>"
        html_content += f"<h2>Diff</h2>\n<p>{diff_to_html(item['pr_diff'])}</p>"
        html_content += "</div>\n"

    # Close the HTML tags
    html_content += """
    </div>
</body>
</html>
"""

    return html_content


@dataclass
class PRSample(DataClassJsonMixin):
    """The details of a single PR needed for generating PR instructions."""

    id: str
    repo_name: str
    number: int
    title: str
    body: str
    pr_diff: str


@dataclass
class GeneratedPRInstructions(DataClassJsonMixin):
    """Result of generating PR instructions."""

    instructions: dict[str, list[str]]
    """The generated instructions, organized by group."""

    description_paragraph: Optional[str]
    """If generated, the paragraph-length description of the PR."""

    prompt: str
    """How the model was prompted."""

    full_response: str
    """The full model response."""


def get_pr_instructions_prompt(sample: PRSample) -> tuple[str, str]:
    """Generate instructions for the given PR sample using one dialog turn.

    Returns the generated insturctions.
    """
    persona_prompts = {
        "main": """\
Use this format:

1. very short succinct instruction, just a few words
2. brief instruction, a full sentence
3. moderately long instruction, with good formatting and punctuation
""",
        "brief": """\
Write three instructions as someone who knows what they're doing but want
to write a very brief instruction with less keystrokes:
use bad formatting, phrasing, punctuation. Use this format:

1. brief instruction 1
2. brief instruction 2
3. brief instruction 3
""",
        "detailed": """\
Write three instructions as someone who knows what they're doing and
are providing a lot more details, across multiple lines, like this:

1. detailed, thoughtful instruction 1
2. detailed, thoughtful instruction 2
3. detailed, thoughtful instruction 3
""",
    }

    persona = random.choice(list(persona_prompts.keys()))
    persona_prompt = persona_prompts[persona]

    prompt = f"""\
Here is a diff from a github PR:

```
{sample.pr_diff}
```

Here are the PR details:

```
title: {sample.title}
description:
{sample.body}
```

Write a paragraph summarizing this PR.

After the paragraph, write better descriptions for this change, phrased as
instructions for someone who is tasked with implementing this PR.  Generate
several different descriptions of this PR.

{persona_prompt}

Do not label the instructions, just put numbers as above followed by
the instructions.
"""
    return prompt, persona


def generate_pr_instructions_single_turn(
    chat_client: ChatClient, prompt: str, persona: str, max_output_tokens: int = 1024
) -> GeneratedPRInstructions:
    instructions: dict[str, list[str]] = {}
    # print(f"Sending prompt: {prompt}")
    response = chat_client.generate(
        messages=[prompt],
        max_tokens=max_output_tokens,
    )
    instructions[persona] = extract_instructions(response)
    generated_description_paragraph = extract_paragraph_before_instructions(response)
    if generated_description_paragraph:
        generated_description_paragraph.strip()

    return GeneratedPRInstructions(
        prompt=prompt,
        full_response=response,
        description_paragraph=generated_description_paragraph,
        instructions=instructions,
    )


def process_sample(
    chat_client: ChatClient, sample: PRSample, with_cot: bool
) -> GeneratedPRInstructions:
    """Generate instructions for the given PR sample.

    Returns the generated insturctions.
    """
    prompt_without_cot = f"""\
Here is a diff from a github PR:

```
{sample.pr_diff}
```

Here are the PR details:
title: {sample.title}
description: {sample.body}

Write better descriptions for this change, phrased as instructions for
someone who is tasked with implementing this PR.
Generate several different descriptions of this PR, with this format:

1. very short succinct instruction, just a few words
2. brief instruction, a full sentence
3. moderately long instruction, with good formatting and punctuation

Do not label the instructions, just put numbers as above followed by
the instructions.
"""

    prompt_with_cot = f"""\
Here is a diff from a github PR:

```
{sample.pr_diff}
```

Here are the PR details:
title: {sample.title}
description: {sample.body}

Write a paragraph summarizing this PR.

After the paragraph, write better descriptions for this change, phrased as
instructions for someone who is tasked with implementing this PR.  Generate
several different descriptions of this PR, with this format:

1. very short succinct instruction, just a few words
2. brief instruction, a full sentence
3. moderately long instruction, with good formatting and punctuation

Do not label the instructions, just put numbers as above followed by
the instructions.
"""

    if with_cot:
        prompt = prompt_with_cot
    else:
        prompt = prompt_without_cot

    instructions: dict[str, list[str]] = {}
    response = chat_client.generate(
        messages=[prompt],
        max_tokens=8192,
    )
    instructions["main"] = extract_instructions(response)
    generated_description_paragraph = extract_paragraph_before_instructions(response)
    if generated_description_paragraph:
        generated_description_paragraph.strip()

    # print("Diff:")
    # print(sample.pr_diff)
    # print("Title:", sample.title)
    # print("Body:", sample.body)
    # print(response)

    formatting_prompts = {
        "brief": """\
Rewrite the instructions as someone who knows what they're doing but want
to write a very brief instruction with less keystrokes:

""",
        "detailed": """\
Rewrite the instructions as someone who knows what they're doing and
are providing a lot more details, across multiple lines, like this:

1. rewritten instruction 1
2. rewritten instruction 2
3. rewritten instruction 3
""",
    }

    for group_name, formatting_prompt in formatting_prompts.items():
        formatted_response = chat_client.generate(
            messages=[prompt, response, formatting_prompt],
            max_tokens=8192,
        )
        instructions[group_name] = extract_instructions(formatted_response)
        # print(f"\n{formatting_prompt}\n{formatted_response}\n")

    # print("=" * 80)

    return GeneratedPRInstructions(
        prompt=prompt,
        full_response=response,
        description_paragraph=generated_description_paragraph,
        instructions=instructions,
    )


def load_pr_data(filename: str, max_samples: int) -> list:
    """Load pull-request samples from a file."""
    if filename.endswith("jsonl"):
        pr_data = [
            json.loads(line) for line in Path(filename).open("r", encoding="utf8")
        ]
    elif filename.endswith("json"):
        pr_data = json.loads(Path(filename).read_text(encoding="utf8"))
    else:
        raise ValueError(f"Unknown file type: {filename}")

    print(f"Loaded {len(pr_data)} samples")
    samples = []

    while len(samples) < max_samples and pr_data:
        sample = pr_data.pop(0)

        if "body" not in sample:
            sample["body"] = ""

        if is_bad_sample(sample):
            continue

        samples.append(sample)

    return samples
