"""Generate PR instructions with Triton servers."""

import argparse
from pathlib import Path
import pandas
import traceback
import time


from experimental.guy.apis.process_chat_tasks import (
    process_tasks,
    TaskProcessorInput,
    load_addresses_from_yaml,
)
from experimental.guy.pr_task_descriptions.pr_utils import (
    is_bad_sample,
    PRSample,
    GeneratedPRInstructions,
    generate_pr_instructions_single_turn,
    get_pr_instructions_prompt,
)
from research.llm_apis.chat_utils import (
    Llama3ChatClient,
    ChatClient,
    run_health_checks,
)
from experimental.guy.pr_task_descriptions.processify import processify


def get_logging_header(thread_idx: int, task_idx: int) -> str:
    return f"[{thread_idx:03d} task={task_idx:04d}]"


@processify
def process_parquet_file(
    filename,
    thread_idx: int,
    task_idx: int,
    chat_client: ChatClient,
    output_file: Path,
    skip_repo_names: list[str],
    max_input_length: int,
    write_every: int = 50,
) -> None:
    """Process a parquet file with PR data and generate instructions."""
    header = get_logging_header(thread_idx, task_idx)
    try:
        print(f"{header} Processing {filename}")
        partial_output_file = output_file.with_suffix(filename.suffix + ".partial")

        df = pandas.read_parquet(filename)
        print(f"{header} Loaded {filename}")

        if partial_output_file.exists():
            print(f"{header} Loading partial file")
            results = pandas.read_parquet(partial_output_file).to_dict(orient="records")
            previously_processed_ids = set([r["id"] for r in results])
            print(
                f"{header} Partial file {partial_output_file} exists, loaded {len(results)} samples"
            )
        else:
            previously_processed_ids = set()
            results = []

        processing_times = []
        num_samples = len(df)
        num_samples_since_last_write = 0

        for j, pr in enumerate(df.to_dict(orient="records")):
            try:
                print(f"{header} Processing sample {j+1}/{num_samples}")

                if is_bad_sample(pr):
                    print(f"{header} Sample {j+1}/{num_samples} is bad, skipping")
                    continue

                if pr["repo_name"] in skip_repo_names:
                    print(
                        f"{header} Sample {j+1}/{num_samples} has bad repo name, skipping"
                    )
                    continue

                if pr["id"] in previously_processed_ids:
                    print(
                        f"{header} Sample {j+1}/{num_samples} already processed, skipping"
                    )
                    continue

                sample: PRSample = PRSample.from_dict(pr)  # pylint: disable=no-member
                print(f"{header} Sample {j+1}/{num_samples} loaded")

                start = time.time()
                prompt, persona = get_pr_instructions_prompt(sample)
                print(
                    f"{header} Sample {j+1}/{num_samples} prompt length: {len(prompt)}"
                )
                token_length = len(
                    chat_client.tokenizer.encode(prompt, bos=True, eos=True)
                )
                print(
                    f"{header} Sample {j+1}/{num_samples} prompt encoded, token length: {token_length}"
                )
                if token_length > max_input_length:
                    print(
                        f"{header} Skipping {sample.id} due to prompt length {token_length} > {max_input_length}"
                    )
                    continue

                generated_instructions: GeneratedPRInstructions = (
                    generate_pr_instructions_single_turn(
                        chat_client,
                        prompt=prompt,
                        persona=persona,
                        max_output_tokens=512,
                    )
                )
                elapsed = time.time() - start
                processing_times.append(elapsed)

                prompt_length = len(
                    chat_client.tokenizer.encode(
                        generated_instructions.prompt, bos=True, eos=True
                    )
                )

                # Keep all the fields from the original PR, and add the generated instructions
                pr_result = {**pr, **generated_instructions.to_dict()}  # pylint: disable=no-member
                results.append(pr_result)

                print(
                    f"{header} Processed sample {j+1}/{num_samples} in {elapsed:.1f} seconds, prompt length {prompt_length} (avg {sum(processing_times) / len(processing_times):.1f} sec/sample, max {max(processing_times):.1f} sec)"
                )

                num_samples_since_last_write += 1
                if num_samples_since_last_write >= write_every:
                    print(
                        f"{header} Writing partial file {partial_output_file} with {len(results)} samples"
                    )
                    pandas.DataFrame(results).to_parquet(partial_output_file)
                    num_samples_since_last_write = 0
            except Exception as e:  # pylint: disable=broad-except
                print(f"{header} Failed to process {pr['id']}: {e}")
                traceback.print_exc()

        pandas.DataFrame(results).to_parquet(partial_output_file)
        print(
            f"{header} Processing of {filename} completed, renaming partial file {partial_output_file} to {output_file}"
        )
        partial_output_file.rename(output_file)
    except Exception as e:  # pylint: disable=broad-except
        print(f"{header} Failed to process task: {e}")
        traceback.print_exc()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--address",
        type=str,
        default="",
        help="The chat server addresses, comma separated. Example: ********:8000,********:9000",
    )
    parser.add_argument(
        "--address_yaml_file",
        type=Path,
        help="A yaml file that contains server addresses. See docstring for example.",
    )
    parser.add_argument(
        "--max_input_length",
        type=int,
        default=8192,
        help="Max number of tokens in sample prompts.",
    )
    parser.add_argument(
        "--input_path",
        type=str,
        default="/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha",
        help="The input path to the parquet files.",
    )
    parser.add_argument(
        "--output_path",
        type=str,
        required=True,
        help="Directory in which to save the parquet files.",
    )
    parser.add_argument(
        "--server_request_timeout_secs",
        type=int,
        default=180,
        help="Max number of tokens in sample prompts.",
    )
    parser.add_argument(
        "--skip_health_check",
        action="store_true",
        help="Skip health checks for chat servers",
    )
    args = parser.parse_args()

    input_path = Path(args.input_path)
    output_path = Path(args.output_path)
    output_path.mkdir(parents=True, exist_ok=True)
    skip_repo_names = ["NixOS/nixpkgs", "microsoft/AzureTRE"]

    if args.address and args.address_yaml_file:
        raise ValueError("Must specify either --address or --address_yaml_file")
    elif args.address:
        addresses = args.address.split(",")
    else:
        addresses = load_addresses_from_yaml(args.address_yaml_file)

    if len(addresses) == 0:
        raise ValueError("Must specify --address or --address_yaml_file")

    if not args.skip_health_check:
        run_health_checks(addresses)

    chat_clients: list[ChatClient] = [
        Llama3ChatClient(
            "triton", address=address, timeout=args.server_request_timeout_secs
        )
        for address in addresses
    ]

    def process_task(task_processor_input: TaskProcessorInput):
        filename = task_processor_input.task_data
        output_file = output_path / filename.name
        header = get_logging_header(
            task_processor_input.thread_idx, task_processor_input.task_idx
        )
        if output_file.exists():
            print(
                f"{header} File {filename} was already processed in {output_file}, skipping"
            )
            return
        process_parquet_file(
            filename=filename,
            thread_idx=task_processor_input.thread_idx,
            task_idx=task_processor_input.task_idx,
            chat_client=task_processor_input.chat_client,
            output_file=output_file,
            skip_repo_names=skip_repo_names,
            max_input_length=args.max_input_length,
        )
        if not output_file.exists():
            print(f"{header} Failed to process {filename}")

    # Process larger files first. This is to avoid waiting for a single large file
    # to get processed on a single server at the end of the pipeline.
    tasks = sorted(
        list(input_path.glob("*.parquet")), key=lambda x: x.stat().st_size, reverse=True
    )
    process_tasks(
        chat_clients=chat_clients,
        tasks=tasks,
        task_processor_fn=process_task,
        per_thread_init_delay=2,
    )
    print("Task processing complete.")


if __name__ == "__main__":
    main()
