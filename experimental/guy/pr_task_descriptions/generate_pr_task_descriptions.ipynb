{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic generation"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "from pathlib import Path\n", "\n", "from pr_utils import *\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "\n", "gpt_wrapper = GptWrapper()\n", "# model = \"gpt-3.5-turbo-1106\"\n", "# model = \"gpt-4-0125-preview\"\n", "model = \"gpt-4-1106-preview\"\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = Path(os.environ[\"HOME\"], \".config/openai/api_key\").read_text().strip()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'prs_filtered.json'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 10\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# for filename in [\"prs_filtered.json\", \"prs_unfiltered.json\"]:\u001b[39;00m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m filename \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprs_filtered.json\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[0;32m---> 10\u001b[0m     samples \u001b[38;5;241m=\u001b[39m \u001b[43mload_pr_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnum_samples\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m20\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     12\u001b[0m     samples_with_instructions \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m     14\u001b[0m     json_output_path \u001b[38;5;241m=\u001b[39m Path(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mwith_instructions_cot\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mwith_cot\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m filename)\n", "File \u001b[0;32m~/augment/experimental/guy/pr_task_descriptions/pr_utils.py:350\u001b[0m, in \u001b[0;36mload_pr_data\u001b[0;34m(filename, num_samples)\u001b[0m\n\u001b[1;32m    346\u001b[0m     pr_data \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m    347\u001b[0m         json\u001b[38;5;241m.\u001b[39mloads(line) \u001b[38;5;28;01mfor\u001b[39;00m line \u001b[38;5;129;01min\u001b[39;00m Path(filename)\u001b[38;5;241m.\u001b[39mopen(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m\"\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutf8\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    348\u001b[0m     ]\n\u001b[1;32m    349\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m filename\u001b[38;5;241m.\u001b[39mendswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mjson\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[0;32m--> 350\u001b[0m     pr_data \u001b[38;5;241m=\u001b[39m json\u001b[38;5;241m.\u001b[39mloads(\u001b[43mPath\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_text\u001b[49m\u001b[43m(\u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mutf8\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m)\n\u001b[1;32m    351\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    352\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnknown file type: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfilename\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/pathlib.py:1266\u001b[0m, in \u001b[0;36mPath.read_text\u001b[0;34m(self, encoding, errors)\u001b[0m\n\u001b[1;32m   1262\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mread_text\u001b[39m(\u001b[38;5;28mself\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, errors\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m   1263\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1264\u001b[0m \u001b[38;5;124;03m    Open the file in text mode, read it, and close the file.\u001b[39;00m\n\u001b[1;32m   1265\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1266\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mr\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m   1267\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m f\u001b[38;5;241m.\u001b[39mread()\n", "File \u001b[0;32m/opt/conda/lib/python3.9/pathlib.py:1252\u001b[0m, in \u001b[0;36mPath.open\u001b[0;34m(self, mode, buffering, encoding, errors, newline)\u001b[0m\n\u001b[1;32m   1246\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mopen\u001b[39m(\u001b[38;5;28mself\u001b[39m, mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m, buffering\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1247\u001b[0m          errors\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, newline\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[1;32m   1248\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   1249\u001b[0m \u001b[38;5;124;03m    Open the file pointed by this path and return a file object, as\u001b[39;00m\n\u001b[1;32m   1250\u001b[0m \u001b[38;5;124;03m    the built-in open() function does.\u001b[39;00m\n\u001b[1;32m   1251\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1252\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mio\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbuffering\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnewline\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1253\u001b[0m \u001b[43m                   \u001b[49m\u001b[43mopener\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_opener\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/pathlib.py:1120\u001b[0m, in \u001b[0;36mPath._opener\u001b[0;34m(self, name, flags, mode)\u001b[0m\n\u001b[1;32m   1118\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_opener\u001b[39m(\u001b[38;5;28mself\u001b[39m, name, flags, mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0o666\u001b[39m):\n\u001b[1;32m   1119\u001b[0m     \u001b[38;5;66;03m# A stub for the opener argument to built-in open()\u001b[39;00m\n\u001b[0;32m-> 1120\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_accessor\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mopen\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'prs_filtered.json'"]}], "source": ["import json\n", "from pathlib import Path\n", "import traceback\n", "\n", "# Generate instructions and render with HTML\n", "with_cot = True\n", "\n", "# for filename in [\"prs_filtered.json\", \"prs_unfiltered.json\"]:\n", "for filename in [\"prs_filtered.json\"]:\n", "    samples = load_pr_data(filename, num_samples=20)\n", "\n", "    samples_with_instructions = []\n", "\n", "    json_output_path = Path(f\"with_instructions_cot{with_cot}_\" + filename)\n", "    html_path = Path(f\"/mnt/efs/augment/public_html/guy/pr_instructions/{json_output_path.stem}.html\")\n", "\n", "    for i, sample in enumerate(samples):\n", "        try:\n", "            sample_with_instructions = process_sample(gpt_wrapper, model, sample, with_cot=with_cot)\n", "            samples_with_instructions.append(sample_with_instructions)\n", "            print(f\"=== example succeeded {i+1} of {len(samples)} ===\")\n", "            # print(\"full response:\")\n", "            # print(sample_with_instructions[\"full_response\"])\n", "        except Exception as e:\n", "            print(\"sample failed:\", sample)\n", "            print(e)\n", "            traceback.print_exc()\n", "\n", "        json_output_path.write_text(json.dumps(samples_with_instructions, indent=2))\n", "        print(f\"{filename} -> {json_output_path}: Collected {len(samples_with_instructions)} samples\")\n", "        html_output = json_to_html(samples_with_instructions)\n", "        html_path.write_text(html_output)\n", "        print(f\"Wrote HTML to {html_path}\")\n", "\n", "print(json.dumps(gpt_wrapper.get_stats(), indent=2))\n", "print(\"Done\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpt_wrapper.get_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate instructions for a single sample\n", "samples = json.loads(Path(\"prs_filtered.json\").read_text(encoding=\"utf8\"))\n", "\n", "# sample_id = \"33257839\"\n", "sample_id = \"568266\"\n", "\n", "for sample in samples:\n", "    if sample[\"id\"] == sample_id:\n", "        print(f\"found sample {sample_id}\")\n", "        sample_with_instructions = process_sample(gpt_wrapper, model, sample, with_cot=True)\n", "        print(json.dumps(sample_with_instructions[\"instructions\"], indent=2))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# HTML Rendering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "# Convert JSON to HTML\n", "json_path = Path(\"with_instructions_cotTrue_prs_filtered.json\")\n", "json_data = json.loads(json_path.read_text(encoding=\"utf8\"))\n", "html_output = json_to_html(json_data)\n", "html_path = Path(f\"/mnt/efs/augment/public_html/guy/pr_instructions/{json_path.stem}.html\")\n", "html_path.write_text(html_output)\n", "print(f\"HTML file was created: {html_path}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Amplification using internal models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "json_path = Path(\"instructions_from_few_shot_code_llama_70b_instruct_cotTrue.json\")\n", "raw_data = json.loads(json_path.read_text(encoding=\"utf8\"))\n", "samples = []\n", "\n", "for raw_sample in raw_data:\n", "    instructions = extract_instructions(raw_sample[\"raw_label\"])\n", "    instruction_group = raw_sample[\"prompt_info\"][\"instruction_group\"]\n", "    sample = {\n", "        **raw_sample[\"original_sample\"],\n", "        \"raw_label\": raw_sample[\"raw_label\"],\n", "        \"instructions\": {\n", "            instruction_group: instructions\n", "        }\n", "    }\n", "    if is_bad_sample(sample):\n", "        continue\n", "    samples.append(sample)\n", "\n", "html_output = json_to_html(samples)\n", "with open(f\"/mnt/efs/augment/public_html/guy/pr_instructions/{json_path.stem}.html\", \"w\") as file:\n", "    file.write(html_output)\n", "print(\"HTML file was created\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "data = json.loads(Path(\"/mnt/efs/augment/user/guy/pr_instructions/with_instructions_cotTrue_1k_prs_filtered_10k.json\").read_text())\n", "\n", "print(data[0][\"prompt\"])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["PROMPT = \"\"\"\\\n", "Here is a diff from a github PR:\n", "        \n", "```\n", "diff --git a/lib/i18n/backend/chain.rb b/lib/i18n/backend/chain.rb\n", "index 2bc83cb7..79227d36 100644\n", "--- a/lib/i18n/backend/chain.rb\n", "+++ b/lib/i18n/backend/chain.rb\n", "@@ -26,6 +26,15 @@ def initialize(*backends)\n", "           self.backends = backends\n", "         end\n", " \n", "+        def initialized?\n", "+          backends.all? do |backend|\n", "+            backend.instance_eval do\n", "+              return false unless initialized?\n", "+            end\n", "+          end\n", "+          true\n", "+        end\n", "+          \n", "         def reload!\n", "           backends.each { |backend| backend.reload! }\n", "         end\n", "@@ -74,6 +83,19 @@ def localize(locale, object, format = :default, options = EMPTY_HASH)\n", "         end\n", " \n", "         protected\n", "+          def init_translations\n", "+            backends.each do |backend|\n", "+              backend.send(:init_translations)\n", "+            end\n", "+          end\n", "+\n", "+          def translations\n", "+            backends.first.instance_eval do\n", "+              init_translations unless initialized?\n", "+              translations\n", "+            end\n", "+          end  \n", "+\n", "           def namespace_lookup?(result, options)\n", "             result.is_a?(Hash) && !options.has_key?(:count)\n", "           end\n", "diff --git a/lib/i18n/backend/key_value.rb b/lib/i18n/backend/key_value.rb\n", "index 1d0e0611..0a4ccf89 100644\n", "--- a/lib/i18n/backend/key_value.rb\n", "+++ b/lib/i18n/backend/key_value.rb\n", "@@ -76,6 +76,10 @@ def initialize(store, subtrees=true)\n", "           @store, @subtrees = store, subtrees\n", "         end\n", " \n", "+        def initialized?\n", "+          !@store.nil?\n", "+        end\n", "+\n", "         def store_translations(locale, data, options = EMPTY_HASH)\n", "           escape = options.fetch(:escape, true)\n", "           flatten_translations(locale, data, escape, @subtrees).each do |key, value|\n", "@@ -105,6 +109,26 @@ def available_locales\n", " \n", "       protected\n", " \n", "+        # Queries the translations from the key-value store and converts\n", "+        # them into a hash such as the one returned from loading the\n", "+        # haml files\n", "+        def translations\n", "+          @translations = @store.keys.clone.map do |main_key|\n", "+            main_value = JSON.decode(@store[main_key])\n", "+            main_key.to_s.split(\".\").reverse.inject(main_value) do |value, key|\n", "+              {key.to_sym => value}\n", "+            end\n", "+          end.inject{|hash, elem| hash.deep_merge!(elem)}.deep_symbolize_keys\n", "+        end\n", "+\n", "+        def init_translations\n", "+          # NO OP\n", "+          # This call made also inside Simple Backend and accessed by\n", "+          # other plugins like I18n-js and babilu and\n", "+          # to use it along with the Chain backend we need to\n", "+          # provide a uniform API even for protected methods :S\n", "+        end\n", "+\n", "         def subtrees?\n", "           @subtrees\n", "         end\n", "diff --git a/test/backend/chain_test.rb b/test/backend/chain_test.rb\n", "index 2fae0945..18884e74 100644\n", "--- a/test/backend/chain_test.rb\n", "+++ b/test/backend/chain_test.rb\n", "@@ -81,6 +81,39 @@ def setup\n", "     I18n.backend.store_translations :foo, {:bar => :baz}, {:option => 'persists'}\n", "   end\n", " \n", "+  test 'store should call initialize on all backends and return true if all initialized' do\n", "+    @first.send :init_translations\n", "+    @second.send :init_translations\n", "+    assert I18n.backend.initialized?\n", "+  end\n", "+\n", "+  test 'store should call initialize on all backends and return false if one not initialized' do\n", "+    @first.reload!\n", "+    @second.send :init_translations\n", "+    assert !I18n.backend.initialized?\n", "+  end\n", "+\n", "+  test 'should reload all backends' do\n", "+    @first.send :init_translations\n", "+    @second.send :init_translations\n", "+    I18n.backend.reload!\n", "+    assert !@first.initialized?\n", "+    assert !@second.initialized?\n", "+  end\n", "+\n", "+  test 'should be able to get all translations of the first backend' do\n", "+    assert_equal I18n.backend.send(:translations),\n", "+                 en: {\n", "+                   foo: 'Foo',\n", "+                   formats: {\n", "+                     short: 'short',\n", "+                     subformats: { short: 'short' }\n", "+                   },\n", "+                   plural_1: { one: \"%{count}\" },\n", "+                   dates: { a: 'A' }\n", "+                 }\n", "+  end\n", "+\n", "   protected\n", " \n", "     def backend(translations)\n", "diff --git a/test/backend/key_value_test.rb b/test/backend/key_value_test.rb\n", "index a3695172..1c962fda 100644\n", "--- a/test/backend/key_value_test.rb\n", "+++ b/test/backend/key_value_test.rb\n", "@@ -49,6 +49,19 @@ def assert_flattens(expected, nested, escape=true, subtree=true)\n", "     end\n", "   end\n", " \n", "+  test 'initialized? checks that a store is available' do\n", "+    setup_backend!\n", "+    I18n.backend.reload!\n", "+    assert_equal I18n.backend.initialized?, true\n", "+  end\n", "+\n", "+  test 'translations gets the translations from the store' do\n", "+    setup_backend!\n", "+    I18n.backend.send(:translations)\n", "+    expected = { :en => {:foo => { :bar => 'bar', :baz => 'baz' }} }\n", "+    assert_equal expected, translations\n", "+  end \n", "+  \n", "   test \"subtrees enabled: given incomplete pluralization data it raises I18n::InvalidPluralizationData\" do\n", "     setup_backend!\n", "     store_translations(:en, :bar => { :one => \"One\" })\n", "\n", "```\n", "\n", "Here are the PR details:\n", "title: `Provide a uniform api between Simple, KeyValue and Chain stores`\n", "body:\n", "```\n", "Provide a uniform api between Simple, KeyValue and Chain stores for public and private methods.\n", "\n", "This is because a couple of gems I use out there need to call some of the internal methods of the simple store. When you try to use other gems configured with a Chain backend call those calls fail.\n", "\n", "Basically I added the initialized?, translations and init_translations to the KeyValue and Chain stores.\n", "\n", "Also added the functionality to be able to read back the translations from a key value store back to a hash as if it where read from the YAML files.\n", "\n", "The gems that i know that would benefit from this would be i18n-js and babilu\n", "```\n", "\n", "\n", "Please analyze the given PR details and write a one-paragraph description of the PR.\n", "\n", "Then, write a better body and title for the PR to be more clear and descriptive.\n", "Include a bulleted list of key changes in the body. Keep the initial description\n", "short, one sentence.\n", "\n", "Then, write a better description for this change, phrased as a high-level instruction for\n", "a developer who is tasked with implementing this PR. The instruction should \n", "describe the full PR at a high level. It should be up to 2 sentences long.\n", "\n", "The output format should be:\n", "\n", "One-paragraph description: a clear, descriptive description\n", "\n", "Better title: a better title\n", "Better body: a better body\n", "Instruction: a brief instruction for implementing this PR\n", "\"\"\"\n", "\n", "response = gpt_wrapper(messages=[PROMPT], model=model)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["One-paragraph description:\n", "\n", "This PR enhances the I18n library by standardizing the API across the Simple, Ke\n", "yValue, and Chain backend stores, introducing uniform methods for initial state \n", "checking, translations retrieval, and translations initialization. This change a\n", "llows other gems, which previously relied on internal methods specific to the Si\n", "mple store, to function correctly with different backend configurations, includi\n", "ng the Chain backend. The new `initialized?`, `translations`, and `init_translat\n", "ions` methods facilitate a seamless integration with gems like i18n-js and babil\n", "u, by allowing them to interact with the I18n backends uniformly, thus improving\n", " compatibility and enabling the retrieval of translations as hashes, mirroring t\n", "he format provided by YAML files.\n", "\n", "\n", "Better title: Uniform API for I18n Backend Stores for Improved Gem Compatibility\n", "\n", "\n", "Better body:\n", "\n", "This PR introduces a series of enhancements to the I18n gem, aiming to provide a\n", " consistent API across different backend stores, including Simple, KeyValue, and\n", " Chain. The main changes include:\n", "\n", "\n", "- Addition of the `initialized?` method to check if the backend is properly init\n", "ialized.\n", "\n", "- Addition of the `translations` method to retrieve translations in a hash forma\n", "t.\n", "\n", "- Addition of the `init_translations` method to ensure all backends can initiali\n", "ze translations uniformly.\n", "\n", "- Added capability in KeyValue backend to reconstruct hash from stored translati\n", "ons, simulating YAML file structure.\n", "\n", "- Tests to verify new functionality and backend compatibility.\n", "\n", "\n", "These changes are particularly beneficial for the i18n-js and babilu gems, which\n", " depend on calling internal methods of the Simple store.\n", "\n", "\n", "Instruction:\n", "\n", "Ensure all I18n backend stores (Simple, KeyValue, and Chain) provide a consisten\n", "t set of methods (`initialized?`, `translations`, `init_translations`) to check \n", "initialization status, retrieve and initialize translations uniformly, respectiv\n", "ely. Also, modify the KeyValue backend to enable translation data retrieval in a\n", " hash structure akin to YAML files, and validate these changes with correspondin\n", "g tests.\n", "\n"]}], "source": ["# print the response with line wrapping at 80 characters\n", "for line in response.split(\"\\n\"):\n", "    while line:\n", "        print(line[:80])\n", "        line = line[80:]\n", "    print(\"\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ensure all I18n backend stores (Simple, KeyValue, and Chain) provide a consisten t set of methods (`initialized?`, `translations`, `init_translations`) to check initialization status, retrieve and initialize translations uniformly, respectiv ely. Also, modify the KeyValue backend to enable translation data retrieval in a hash structure akin to YAML files, and validate these changes with correspondin g tests.\n", "\n", "Add `initialized?`, `translations`, and `init_translations` methods to KeyValue and Chain stores for API uniformity."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}