"""Label more samples by starting with the seed set, used as few-shot prompt."""

import json
import random
from dataclasses import asdict, dataclass
from pathlib import Path

from utils import LlamaCppClient

from research.core.llama_prompt_formatters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Dialog

# def test_model(code_llama, prompt_formatter):
#     tokens = prompt_formatter.prepare_chat_prompt(Dialog(messages=["write a factorial function"]))
#     print("Token length:", len(tokens))
#     prompt = prompt_formatter.tokenizer.detokenize(tokens)
#     response = code_llama.generate(
#         prompt=prompt,
#         # max_generated_tokens=1024,
#         max_generated_tokens=32,
#         stop=["EOT", "Source: assistant"],  # add 4. to try and limit it to 3 instructions
#     )
#     print("Response:")
#     print(response.content)

# test_model()
# import sys; sys.exit(0)

PROMPT_TEMPLATE = """\
    Here is a diff from a github PR:

    ```
    {diff}
    ```

    Here are the PR details:
    title: {title}
    description: {body}

    Write better descriptions for this change, phrased as instructions for
    someone who is tasked with implementing this PR.
    Generate several different descriptions of this PR.

    [FEW SHOT ONLY]Paragraph-length description: {description_paragraph}

    Instructions:
    """


@dataclass
class FewShotPromptResponse:
    """Response to a few-shot prompt creation request."""

    few_shot_prompt: str
    num_shots: int
    shot_samples: list
    instruction_group: str


def make_few_shot_prompt(labelled_samples, num_shots):
    few_shot_prompt = ""
    shot_samples = random.sample(labelled_samples, num_shots)
    group = random.sample(list(labelled_samples[0]["instructions"].keys()), 1)[0]
    print("chosen group:", group)

    for sample in shot_samples:
        sample_prompt = PROMPT_TEMPLATE.replace("[FEW SHOT ONLY]", "").format(
            diff=sample["pr_diff"],
            title=sample["title"],
            body=sample["body"],
            description_paragraph=sample["description_paragraph"],
        )

        instructions = sample["instructions"][group]
        for i, instruction in enumerate(instructions):
            sample_prompt += f"{i+1}. {instruction}\n"

        few_shot_prompt += sample_prompt + "\n\n"

    return FewShotPromptResponse(
        few_shot_prompt=few_shot_prompt,
        num_shots=num_shots,
        shot_samples=shot_samples,
        instruction_group=group,
    )


def main():
    random.seed(42)

    code_llama = LlamaCppClient(address="************:8080", timeout=9999)
    prompt_formatter = CodeLlamaChatFormatter(model_with_step_token=True)
    # prompt_formatter = DeepSeekCoderInstructFormatter()

    all_samples = json.loads(Path("prs_filtered.json").read_text(encoding="utf8"))
    labelled_samples = json.loads(
        Path("with_instructions_cotTrue_prs_filtered.json").read_text(encoding="utf8")
    )
    labelled_ids = [sample["id"] for sample in labelled_samples]
    unlabelled_samples = [
        sample for sample in all_samples if sample["id"] not in labelled_ids
    ]

    new_labelled_samples = []

    # Use same prompt for everyone
    # few_shot_prompt_response = make_few_shot_prompt(labelled_samples, num_shots=2)

    for sample in unlabelled_samples:
        # Use a different prompt for every sample
        few_shot_prompt_response = make_few_shot_prompt(labelled_samples, num_shots=2)

        sample_prompt = PROMPT_TEMPLATE.split("[FEW SHOT ONLY]")[0].format(
            diff=sample["pr_diff"],
            title=sample["title"],
            body=sample["body"],
        )

        print("Title:", sample["title"])
        print("Body:", sample["body"])

        prompt = few_shot_prompt_response.few_shot_prompt + sample_prompt

        tokens = prompt_formatter.prepare_chat_prompt(Dialog(messages=[prompt]))
        print("Token length:", len(tokens))
        if len(tokens) > 14000:
            print("Prompt too long, skipping")
            continue

        prompt = prompt_formatter.tokenizer.detokenize(tokens)

        # forced_response_prefix = "1."  # try to steer it to giving an instruction
        forced_response_prefix = "Paragraph-length description:"  # for CoT

        prompt += forced_response_prefix

        # print("=========================== Prompt: =============================")
        # print(prompt)

        response = code_llama.generate(
            prompt=prompt,
            max_generated_tokens=1024,
            # max_generated_tokens=32,
            stop=[
                "EOT",
                "Source: assistant",
                "</s>",
                "```",
                "4. ",
            ],  # add 4. to try and limit it to 3 instructions
        )
        # Add the header which we included in the prompt
        content = forced_response_prefix + response.content
        print("========================= Response: =============================")
        print(content)

        new_labelled_samples.append(
            {
                "original_sample": sample,
                "prompt_info": asdict(few_shot_prompt_response),
                "raw_label": content,
            }
        )

        with Path(
            "instructions_from_few_shot_code_llama_70b_instruct_cotTrue.json"
        ).open("w", encoding="utf8") as file:
            json.dump(new_labelled_samples, file, indent=2)
        print("new labelled sample done")


if __name__ == "__main__":
    main()
