"""Compute PR instructions prompt statistics."""

import json
from pathlib import Path

import pandas

from research.llm_apis.llama3_tokenizer import Tokenizer

# Use the llama 3 tokenizer
tokenizer_model_path: str = (
    "/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B-Instruct/tokenizer.model"
)
tokenizer = Tokenizer(tokenizer_model_path)

tokenizer.encode("hello there", eos=False, bos=False)

input_path = Path("/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train")
output_path = Path("pr_instruction_prompt_token_lengths.json")
token_lengths = {}

for path_obj in input_path.glob("*.parquet"):
    path = str(path_obj)
    token_lengths[path] = []
    df = pandas.read_parquet(path)
    for row in df.to_dict(orient="records"):
        if "title" in row and "body" in row and "pr_diff" in row:
            title = row["title"]
            body = row["body"]
            pr_diff = row["pr_diff"]
            text = f"{title}\n{body}\n{pr_diff}"
            tokens = tokenizer.encode(text, eos=False, bos=False)
            token_lengths[path].append(len(tokens))
            print("Length summary:")
            for path_, lengths in token_lengths.items():
                percent_above_8k = sum(1 for l in lengths if l > 8192) / len(lengths)
                percent_above_16k = sum(1 for l in lengths if l > 16384) / len(lengths)
                print(
                    f"\t{path_}: {len(lengths)} samples, avg {round(sum(lengths) / len(lengths))} tokens/sample, max {max(lengths)} tokens, {round(percent_above_8k * 100)}% > 8k, {round(percent_above_16k * 100)}% > 16k"
                )
            output_path.write_text(
                json.dumps(token_lengths, indent=2), encoding="utf-8"
            )
