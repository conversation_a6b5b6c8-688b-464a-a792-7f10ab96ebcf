{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas\n", "import random\n", "\n", "root = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_triton\")\n", "\n", "paths = list(root.glob(\"*.parquet\"))\n", "random.seed(42)\n", "path = random.choice(paths)\n", "df = pandas.read_parquet(path)\n", "data = df.to_dict(orient=\"records\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for pr in sorted(data, key=lambda x: len(x[\"prompt\"]), reverse=True):\n", "    print(len(pr[\"prompt\"]))\n", "    print(pr[\"instructions\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "\n", "def make_layout(height):\n", "    return widgets.Layout(width=\"90%\", height=height)\n", "\n", "\n", "class PRViewer:\n", "    def __init__(self, data):\n", "        self.data = data\n", "        self.index = 0\n", "\n", "        style = {\n", "            \"font_weight\": \"bold\",\n", "            \"font_family\": \"Arial\",\n", "            \"font_size\": \"14px\",\n", "            \"color\": \"black\",\n", "        }\n", "\n", "        self.index_text = widgets.Textarea(\n", "            value=\"\",\n", "            placeholder=\"\",\n", "            description=\"Element:\",\n", "            disabled=True,\n", "            layout=make_layout(\"30px\"),\n", "            style=style,\n", "        )\n", "        self.title_text = widgets.Textarea(\n", "            value=\"\",\n", "            placeholder=\"\",\n", "            description=\"Title:\",\n", "            disabled=True,\n", "            layout=make_layout(\"70px\"),\n", "            style=style,\n", "        )\n", "        self.body_text = widgets.Textarea(\n", "            value=\"\",\n", "            placeholder=\"\",\n", "            description=\"Body:\",\n", "            disabled=True,\n", "            layout=make_layout(\"500px\"),\n", "            style=style,\n", "        )\n", "        self.pr_diff_text = widgets.Textarea(\n", "            value=\"\",\n", "            placeholder=\"\",\n", "            description=\"PR Diff:\",\n", "            disabled=True,\n", "            layout=make_layout(\"500px\"),\n", "            style=style,\n", "        )\n", "        self.instructions_text = widgets.Textarea(\n", "            value=\"\",\n", "            placeholder=\"\",\n", "            description=\"Instructions:\",\n", "            disabled=True,\n", "            layout=make_layout(\"150px\"),\n", "            style=style,\n", "        )\n", "\n", "        self.next_button = widgets.Button(description=\"Next\")\n", "        self.prev_button = widgets.Button(description=\"Previous\")\n", "\n", "        self.next_button.on_click(self.next_element)\n", "        self.prev_button.on_click(self.prev_element)\n", "\n", "        self.display_element()\n", "\n", "        self.app = widgets.VBox(\n", "            [\n", "                widgets.HBox([self.prev_button, self.next_button]),\n", "                self.index_text,\n", "                self.title_text,\n", "                self.body_text,\n", "                self.pr_diff_text,\n", "                self.instructions_text,\n", "            ]\n", "        )\n", "\n", "    def display_element(self):\n", "        element = self.data[self.index]\n", "        self.index_text.value = f\"{self.index+1}/{len(self.data)}\"\n", "        self.title_text.value = element[\"title\"]\n", "        self.body_text.value = element[\"body\"]\n", "        self.pr_diff_text.value = element[\"pr_diff\"]\n", "\n", "        instructions_text = \"\"\n", "        instructions = {\n", "            persona: [inst for inst in element[\"instructions\"][persona]]\n", "            for persona in element[\"instructions\"]\n", "            if element[\"instructions\"][persona] is not None\n", "        }\n", "        for persona in instructions:\n", "            instructions_text += f\"{persona}:\\n\"\n", "            for i, instruction in enumerate(instructions[persona]):\n", "                instructions_text += f\"{i+1}. {instruction}\\n\"\n", "        self.instructions_text.value = instructions_text\n", "\n", "    def next_element(self, b):\n", "        self.index = min(len(self.data) - 1, self.index + 1)\n", "        self.display_element()\n", "\n", "    def prev_element(self, b):\n", "        self.index = max(0, self.index - 1)\n", "        self.display_element()\n", "\n", "    def show(self):\n", "        display(self.app)\n", "\n", "\n", "# Example usage:\n", "# data = [...]  # list of dicts with title, body, pr_diff, instructions fields\n", "app = PRViewer(data)\n", "app.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}