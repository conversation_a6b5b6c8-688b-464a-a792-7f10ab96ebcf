"""Stage functions for generating PR instructions using LLaMA."""

from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, Union
import random
import pandas
from functools import partial
from pyspark.sql import SparkSession

from research.data.spark.pipelines.stages.common import (
    declare_processing_success,
    get_limited_map_parquet_input,
)
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.stage_function_registry import (
    register_stage_function,
)

from research.llm_apis.chat_utils import Llama3LlamaCppApiClient, ChatClient
from experimental.guy.pr_task_descriptions.pr_utils import (
    process_sample,
    PRSample,
    GeneratedPRInstructions,
)


@dataclass
class PRInstructionsConfig:
    """Configuration of PR dataset."""

    name: str

    input: Path
    """PR dataset."""

    output: Path
    """Output path."""

    llama_cpp_server_addresses: list[str]
    """Addresses of the llama.cpp servers, e.g. ********:8080"""

    skip_repo_names: list[str] = field(default_factory=list)
    """Repositories to skip."""

    with_cot: bool = True
    """Whether to use a CoT prompt."""

    limit_num_input_parquet_files: Optional[int] = None
    """Limit the number of parquet files to process."""


def _get_chat_client(address: str) -> ChatClient:
    return Llama3LlamaCppApiClient(address, timeout=600)


# PandasFuncType = Callable[[pd.DataFrame], Union[pd.DataFrame, pd.Series, Iterable[Any]]]
def _create_pr_instructions_pandas_func(
    df: pandas.DataFrame,
    llama_cpp_server_addresses: list[str],
    with_cot: bool,
    skip_repo_names: list[str],
) -> pandas.DataFrame:
    chat_clients = [_get_chat_client(address) for address in llama_cpp_server_addresses]

    results = []

    for pr in df.to_dict(orient="records"):
        if pr["repo_name"] in skip_repo_names:
            continue

        client = random.choice(chat_clients)

        sample: PRSample = PRSample.from_dict(pr)  # pylint: disable=no-member

        generated_instructions: GeneratedPRInstructions = process_sample(
            client, sample, with_cot=with_cot
        )

        pr_result = {**sample.to_dict(), **generated_instructions.to_dict()}  # pylint: disable=no-member
        results.append(pr_result)

    return pandas.DataFrame(results)


def _chat_server_health_check(address: str):
    client = _get_chat_client(address)
    result = client.generate(messages=["hello"], max_tokens=1024)
    if not result:
        raise ValueError(f"Health check failed for {address}")
    print(f"Health check for {address}: {result}")


@register_stage_function("create_pr_instructions")
def create_pr_instructions(
    config: Union[dict, PRInstructionsConfig], spark: SparkSession
):
    """Stage function for creating PR instructions."""
    if isinstance(config, dict):
        config = PRInstructionsConfig(**config)

    for address in config.llama_cpp_server_addresses:
        _chat_server_health_check(address)

    map_parquet_input = get_limited_map_parquet_input(
        config.input, config.limit_num_input_parquet_files
    )

    pandas_func = partial(
        _create_pr_instructions_pandas_func,
        llama_cpp_server_addresses=config.llama_cpp_server_addresses,
        with_cot=config.with_cot,
        skip_repo_names=config.skip_repo_names,
    )

    map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=pandas_func,
        input_path=map_parquet_input,
        output_path=str(config.output),
        ignore_error=True,
        timeout=3600 * 24,
        # batch_size=20,  # each sample takes about 30 seconds
        task_info_location="/mnt/efs/spark-data/user/guy/pr-instructions-task-info",
    )

    declare_processing_success(config.output)
