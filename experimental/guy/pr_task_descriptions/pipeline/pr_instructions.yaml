global:
  data_processing_root: /mnt/efs/spark-data/user/guy/data-pipeline
  tokenizer: starcoder
  is_production_tokenizer: true
  efs_path: /mnt/efs/spark-data

stages:
  - name: create_pr_instructions
    input: /mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train
    output: pr-instructions-v1

    # These are very large repos that cause the pipeline to choke
    skip_repo_names: ["NixOS/nixpkgs", "microsoft/AzureTRE"]

    llama_cpp_server_addresses:
      - **************:8081
      - **************:8082
      - **************:8083
      - **************:8084
      - **************:8085
      - **************:8086
      - **************:8087

    with_cot: true

    # For debugging purposes
    # limit_num_input_parquet_files: 2
