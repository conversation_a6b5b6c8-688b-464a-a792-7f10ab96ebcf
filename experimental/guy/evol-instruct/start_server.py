"""Start a llama.cpp server."""

import argparse
import os
import subprocess
from pathlib import Path

CHECKPOINTS_ROOT = "/mnt/efs/augment/checkpoints"

MODELS = {
    "code-llama-34b-q4": f"--model {CHECKPOINTS_ROOT}/llama/CodeLlama-34b/ggml-model-q4_k_m.gguf --alias code-llama-34b --ctx-size 16384 --rope-freq-base 1000000",
    "code-llama-34b-q6": f"--model {CHECKPOINTS_ROOT}/llama/CodeLlama-34b/ggml-model-q6_k.gguf --alias code-llama-34b --ctx-size 16384 --rope-freq-base 1000000",
    "code-llama-34b-q8": f"--model {CHECKPOINTS_ROOT}/llama/CodeLlama-34b/ggml-model-q8_0.gguf --alias code-llama-34b --ctx-size 16384 --rope-freq-base 1000000",
    # TODO(guy) quantize with a higher quantization
    "llama-70b-q4": f"--model {CHECKPOINTS_ROOT}/llama/llama-2-70b-MP8/ggml-model-q4_k_m.gguf --alias llama-70b --ctx-size 4096",
    "deepseek-coder-base-33b-q4": f"--model {CHECKPOINTS_ROOT}/llama/gguf/deepseek-coder-33B-base-GGUF/deepseek-coder-33b-base.Q4_K_M.gguf --alias deepseek-coder-base-33b --ctx-size 16384",
    "deepseek-coder-base-33b-q6": f"--model {CHECKPOINTS_ROOT}/llama/gguf/deepseek-coder-33B-base-GGUF/deepseek-coder-33b-base.Q6_K.gguf --alias deepseek-coder-base-33b --ctx-size 16384",
    "deepseek-coder-base-33b-q8": f"--model {CHECKPOINTS_ROOT}/llama/gguf/deepseek-coder-33B-base-GGUF/deepseek-coder-33b-base.Q8_0.gguf --alias deepseek-coder-base-33b --ctx-size 16384",
    "deepseek-coder-instruct-33b-q4": f"--model {CHECKPOINTS_ROOT}/llama/gguf/deepseek-coder-33B-instruct-GGUF/deepseek-coder-33b-instruct.Q4_K_M.gguf --alias deepseek-coder-instruct-33b --ctx-size 16384",
    "deepseek-coder-instruct-33b-q6": f"--model {CHECKPOINTS_ROOT}/llama/gguf/deepseek-coder-33B-instruct-GGUF/deepseek-coder-33b-instruct.Q6_K.gguf --alias deepseek-coder-instruct-33b --ctx-size 16384",
}


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        required=True,
        help=f"The model to use. Available models: {', '.join(MODELS)}",
    )
    parser.add_argument(
        "--llama_executable",
        type=Path,
        default=Path(f"{os.environ['HOME']}/augment/third_party/llama.cpp/server"),
        help="The path to the llama.cpp executable.",
    )
    parser.add_argument(
        "--host",
        type=str,
        default="127.0.0.1",
        help="Address to listen on, default 127.0.0.1.",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="The port to run the server on.",
    )
    parser.add_argument(
        "--gpu",
        type=int,
        default=0,
        help="Which GPU do use.",
    )
    args = parser.parse_args()

    os.chdir(os.path.expanduser(str(args.llama_executable.parent)))
    model_args = MODELS[args.model]
    cmd = f"CUDA_VISIBLE_DEVICES={args.gpu} ./server --host {args.host} --port {args.port} --n-gpu-layers 1000 {model_args}"
    print(f"Server launch command:\n${cmd}")
    subprocess.run(cmd, check=True, shell=True)


if __name__ == "__main__":
    main()
