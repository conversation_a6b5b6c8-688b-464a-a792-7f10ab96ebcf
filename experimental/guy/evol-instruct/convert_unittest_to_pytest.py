"""Convert out code from unittest to pytest."""

import os
import re
from pathlib import Path

from utils import CompletionResponse, LlamaCppClient


def main():
    clients = {
        "code_llama_34b": LlamaCppClient(address="**************:8080"),
        "llama_70b": LlamaCppClient(address="**************:8081"),
        "deepseek-coder-base-33b": LlamaCppClient(address="**************:8082"),
        "deepseek-coder-instruct-33b": LlamaCppClient(
            address="**************:8083",
            prompt_template="You are an AI programming assistant, utilizing the Deepseek Coder model, developed by Deepseek Company, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\n### Instruction: {prompt}\n### Response:\n",
            timeout=600,
        ),
    }

    client: LlamaCppClient = clients["deepseek-coder-instruct-33b"]
    ROOT = f"{os.environ['HOME']}/augment/research"

    unittest_paths: list[Path] = [
        Path(f"{ROOT}/{path}")
        for path in Path("list_of_unittest_files.txt")
        .read_text(encoding="utf8")
        .splitlines()
    ]

    for source_path in unittest_paths:
        assert source_path.exists()

        target_paths = [
            source_path.with_name(
                source_path.stem + "_pytest_model_generated" + source_path.suffix
            ),
            source_path.with_name(source_path.stem + "_pytest" + source_path.suffix),
        ]

        already_processed = False
        for path in target_paths:
            assert path != source_path
            if path.exists():
                print(f"Skipping {source_path} since target already exists")
                already_processed = True
                break
        if already_processed:
            continue

        source_text = source_path.read_text(encoding="utf8")

        prompt = """\
Convert the following unit test written with unittest to pytest. Follow these instructions:
- Use pytest parameterize instead of parameterized
- Keep all docstrings from the original code

```python
"""
        prompt += source_text
        prompt += "\n```\n"

        # print(prompt)

        print(f"Generating for {source_path} ...")

        response: CompletionResponse = client.generate(
            prompt=prompt,
            max_generated_tokens=8192,
            temperature=0.0,
            stop=[],
        )

        result = response.content
        result = re.sub(r"^.*```python", "", result, flags=re.DOTALL)
        result = re.sub(r"```.*$", "", result, flags=re.DOTALL)

        # print(result)

        for target_path in target_paths:
            target_path.write_text(result, encoding="utf8")
            print(f"Result saved to {target_path}")

            print("Running black")
            os.system(f"black {target_path}")

        # break


if __name__ == "__main__":
    main()
