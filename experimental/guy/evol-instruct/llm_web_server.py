"""A simple web interface for talking to <PERSON><PERSON>."""

# import argparse
import multiprocessing.pool

import gradio
from utils import LlamaCppClient, ModelDetails, generate_model_response

from research.core.llama_prompt_formatters import (
    CodeLlamaChatFormatter,
    DeepSeekCoderInstructFormatter,
)

models = [
    ModelDetails(
        name="code-llama-70b-instruct",
        client=LlamaCppClient(address="************:5000", timeout=40),
        prompt_formatter=CodeLlamaChatFormatter(model_with_step_token=True),
        stop=["Source: assistant"],
    ),
    ModelDetails(
        name="deepseek-33b-instruct",
        client=LlamaCppClient(address="***********:5000"),
        prompt_formatter=DeepSeekCoderInstructFormatter(),
        stop=["### Prompt:", "### Instruction:"],
    ),
    # ModelDetails(
    #    name="mock",
    #    client=LlamaCppClient(mock=True),
    #    prompt_formatter=DeepSeekCoderInstructFormatter(),
    #    stop=[],
    # ),
]


canned_instructions = [
    'write a basic PySpark script that finds all rows where column "foo" is longer than N',
]


def main():
    # parser = argparse.ArgumentParser()
    # parser.add_argument(
    #     "--address", type=str, required=True, help="The address of the llama.cpp server"
    # )
    # args = parser.parse_args()

    theme = gradio.themes.Base(
        font_mono=[
            gradio.themes.GoogleFont("Monaco"),
            "ui-monospace",
            "Consolas",
            "monospace",
        ],
    )
    # theme = "gradio/default"

    shortcut_js = """
<script>
function shortcuts(event) {
    if ((event.metaKey || event.ctrlKey) && event.key == "Enter") {
        document.getElementById("my_btn").click();
    }
}
document.addEventListener('keydown', shortcuts);
</script>
"""

    def generate_all_responses(prompt: str, max_generated_tokens: int) -> list[str]:
        with multiprocessing.pool.ThreadPool(processes=len(models)) as pool:
            generated_texts: list[str] = pool.map(
                lambda model: generate_model_response(
                    model,
                    prompt,
                    max_generated_tokens=max_generated_tokens,
                    verbose=True,
                ),
                models,
            )
        return generated_texts

    with gradio.Blocks(theme=theme, head=shortcut_js) as server_interface:
        gradio.Markdown(
            """
## Talk to language models

Hint: You can use `cmd/ctrl + enter` to generate responses.
"""
        )

        with gradio.Row():
            max_tokens_slider = gradio.Slider(
                minimum=1,
                maximum=512,
                step=1,
                value=512,
                label="Max generated tokens",
            )

        instruction = gradio.Code(
            lines=5, value="write a factorial function", label="Instruction"
        )

        with gradio.Row():
            generated_textboxes = [
                gradio.Code(lines=15, label=f"Generated by {model.name}")
                for model in models
            ]

        generate_button = gradio.Button("Generate", elem_id="my_btn")
        generate_button.click(
            fn=generate_all_responses,
            inputs=[instruction, max_tokens_slider],
            outputs=generated_textboxes,
        )

    server_interface.launch(server_name="127.0.0.1", server_port=8080)


if __name__ == "__main__":
    main()
