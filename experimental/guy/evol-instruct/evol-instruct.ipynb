{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "import numpy as np\n", "from termcolor import colored\n", "import re\n", "import yaml\n", "\n", "from utils import CompletionResponse, LlamaCppClient, print_prompt\n", "\n", "\n", "HEADER = \"=\" * 80\n", "\n", "\n", "clients = {\n", "    \"code_llama_34b\": LlamaCppClient(address=\"**************:8080\"),\n", "    \"llama_70b\": LlamaCppClient(address=\"**************:8081\"),\n", "    \"deepseek-coder-base-33b\": LlamaCppClient(address=\"**************:8082\"),\n", "    \"deepseek-coder-instruct-33b\": LlamaCppClient(\n", "        address=\"**************:8083\",\n", "        prompt_template=\"You are an AI programming assistant, utilizing the Deepseek Coder model, developed by Deepseek Company, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.\\n### Instruction: {prompt}\\n### Response:\\n\",\n", "        ),\n", "}\n", "\n", "\n", "def load_samples(path: str) -> list[dict]:\n", "    return yaml.safe_load(open(path))\n", "\n", "\n", "def load_basic_samples() -> list[dict]:\n", "    return load_samples(\"basic_samples.yaml\")\n", "\n", "\n", "def load_categories(path: Path):\n", "    with path.open(\"r\", encoding=\"utf8\") as file:\n", "        lines = file.read().splitlines()\n", "        categories = [line for line in lines if not line.startswith(\"#\")]\n", "        return categories\n", "\n", "\n", "basic_samples = load_basic_samples()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example generation for testing and debugging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SYSTEM_PROMPT = \"Translate the instruction to a valid bash shell command.\"\n", "INSTRUCTION_HEADER = \"Instruction:\"\n", "COMMAND_HEADER = \"Bash command:\"\n", "\n", "def make_single_shot_prompt(instruction, shell_command):\n", "    return f\"\"\"\\\n", "{SYSTEM_PROMPT}\n", "\n", "{INSTRUCTION_HEADER}\n", "{instruction}\n", "\n", "{COMMAND_HEADER}\n", "{shell_command}\n", "\"\"\"\n", "\n", "labelled_samples = [\n", "    {\n", "        \"instruction\": \"print the second column of file.txt\",\n", "        \"shell_command\": \"cat file.txt | awk '{print $2}'\",\n", "        \"type\": \"files\",\n", "    },\n", "    {\n", "        \"instruction\": \"rename train-* to valid-*\",\n", "        \"shell_command\": 'for f in train-*; do mv \"$f\" \"valid-${f#train-}\"; done',\n", "        \"type\": \"files\",\n", "    },\n", "    {\n", "        \"instruction\": \"delete git branch fix_bug2\",\n", "        \"shell_command\": \"git branch -d fix_bug2\",\n", "        \"type\": \"git\",\n", "    },\n", "]\n", "\n", "\n", "def make_few_shot_prompt(num_shots, samples=labelled_samples, prepare_next_sample=True):\n", "    shot_samples = np.random.choice(samples, size=num_shots, replace=False)\n", "    assert len(shot_samples) == num_shots\n", "    shot_prompts = [\n", "        make_single_shot_prompt(\n", "            instruction=sample[\"instruction\"],\n", "            shell_command=sample[\"shell_command\"])\n", "        for sample in shot_samples\n", "    ]\n", "    prompt = \"\\n\\n\".join(shot_prompts)\n", "    if prepare_next_sample:\n", "        prompt += f\"\\n\\n{SYSTEM_PROMPT}\\n\\n{INSTRUCTION_HEADER}\\n\"\n", "    return prompt\n", "\n", "\n", "def extract_answer(generated_text: str) -> tuple[str, str]:\n", "    \"\"\"Returns the instruction, command from the output.\"\"\"\n", "    first_answer = generated_text.split(SYSTEM_PROMPT)[0]\n", "    instruction, command = [elem.strip() for elem in first_answer.split(COMMAND_HEADER)]\n", "    return instruction, command\n", "\n", "\n", "preamble = make_few_shot_prompt(num_shots=2)\n", "# print(f\"Prompt:\\n'''{prompt}'''\\n\\n\")\n", "\n", "for name, client in clients.items():\n", "        response = client.generate(preamble, max_generated_tokens=128, temperature=0.8)\n", "        print(colored(f\"=== Generated from model {response.model}:\", \"blue\"))\n", "        print(\"'''\" + response.content + \"'''\")\n", "        instruction, command = extract_answer(response.content)\n", "        snippet = \"{\" + f'''\n", "    \"instruction\": \"{instruction}\",\n", "    \"shell_command\": \"{command}\",\n", "''' + \"}\"\n", "        print(snippet)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate diverse instructions and commands\n", "\n", "Use a pre-generated list of categories."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["categories = load_categories(Path(\"categories.txt\"))\n", "print(f\"Loaded {len(categories)} categories.\")\n", "\n", "SYSTEM_PROMPT = \"Translate the instruction to a valid shell command.\"\n", "\n", "labelled_samples = basic_samples\n", "\n", "def make_prompt(samples: list[dict], include_type: bool, include_command: bool):\n", "    prompt = \"\"\n", "\n", "    for i, sample in enumerate(samples):\n", "        if i > 0:\n", "            prompt += \"\\n\"\n", "        \n", "        if include_type:\n", "            type_line  = f\"type: {sample['type']}\\n\"\n", "        else:\n", "            type_line = \"\"\n", "\n", "        instruction_line = f\"instruction: {sample['instruction']}\\n\"\n", "\n", "        if include_command:\n", "            command_line = f\"command: {sample['command']}\\n\"\n", "        else:\n", "            command_line = \"\"\n", "\n", "        prompt += f\"\"\"\\\n", "{SYSTEM_PROMPT}\n", "\n", "{type_line}{instruction_line}{command_line}\"\"\"\n", "\n", "    # finally, add the next system prompt so the model doesn't need to generate it\n", "    prompt += f\"\"\"\\\n", "\n", "{SYSTEM_PROMPT}\n", "\"\"\"\n", "\n", "    return prompt\n", "\n", "\n", "client = clients[\"code_llama_34b\"]\n", "temperature = 0.8\n", "\n", "\n", "\n", "def is_good_command(command: str):\n", "    if command.startswith(\"Error:\"):\n", "        return False\n", "    if re.search(r\"<\\S+>\", command):\n", "        return False\n", "    return True\n", "\n", "\n", "# SHOT_STOP_CONDITIONS = [f\"\\n{SYSTEM_PROMPT}\", \"\\n```\"]\n", "num_skipped = 0\n", "good_samples = []\n", "\n", "while len(good_samples) < 100:\n", "    print(\"=\" * 80)\n", "\n", "    # generate instruction in a sampled category\n", "    category = np.random.choice(categories)\n", "    preamble = make_prompt(labelled_samples, include_type=True, include_command=True)\n", "    preamble = preamble + f\"type: {category}\\n\"\n", "    # print(colored(\"\\nfirst prompt:\", \"green\"))\n", "    # print_prompt(prompt)\n", "    # print(\"prompt:\")\n", "    # print(prompt)\n", "\n", "    response = client.generate(preamble, max_generated_tokens=128, temperature=temperature, stop=[\"\\n\"])\n", "    # print(f\"type: {category}\")\n", "    # print(response.content)\n", "\n", "    m = re.match(r\"^instruction: (.*)\", response.content)\n", "    if not m:\n", "        print(\"Could not find instruction:\", response.content)\n", "        continue\n", "\n", "    instruction = m.group(1).strip()\n", "    print(colored(\"sampled type: \", \"green\") + category)\n", "    print(colored(\"generated instruction: \", \"green\") + instruction)\n", "\n", "    # generate commands: show the model the instruction but not the category\n", "    preamble = make_prompt(labelled_samples, include_type=False, include_command=True)\n", "    prompt_with_instruction = preamble + f\"instruction: {instruction}\\n\"\n", "    # print(colored(\"\\nsecond prompt:\", \"green\"))\n", "    # print_prompt(prompt_with_instruction)\n", "    response = client.generate(prompt_with_instruction, max_generated_tokens=128, temperature=0., stop=[\"\\n\"])\n", "\n", "    m = re.match(r\"^command: (.*)\", response.content)\n", "    if not m:\n", "        print(\"Could not find command:\", response.content)\n", "        continue\n", "    command = m.group(1).strip()\n", "\n", "    if not is_good_command(command):\n", "        print(\"Skipping bad command:\", command)\n", "        num_skipped += 1\n", "        continue\n", "\n", "    generated_sample = {\n", "        \"type\": category,\n", "        \"instruction\": instruction,\n", "        \"command\": command,\n", "    }\n", "\n", "    good_samples.append(generated_sample)\n", "\n", "    print(colored(\"generated: \", \"green\") + instruction)\n", "    print(json.dumps(generated_sample, indent=2))\n", "\n", "    # print(colored(\"temperature:\", 'green'), temperature)\n", "    # for _ in range(3):\n", "    #     response = client.generate(prompt_with_instruction, max_generated_tokens=128, temperature=temperature, stop=[\"\\n\"])\n", "    #     command = response.content\n", "    #     print(f'        \"command\": \"{command}\",')\n", "\n", "total_generated = len(good_samples) + num_skipped\n", "print(f\"Generated {len(good_samples)} good samples, skipped {num_skipped} bad samples, which is {num_skipped/total_generated:.1%}\")\n", "\n", "with Path(\"good_samples_draft.txt\").open(\"w\", encoding=\"utf-8\") as file:\n", "    json.dump(good_samples, file, indent=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Increase the complexity\n", "\n", "A pre-evolution experiment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["labelled_samples = load_basic_samples()\n", "\n", "preamble = \"\"\n", "\n", "def make_single_shot(sample):\n", "    prompt = f\"\"\"\\\n", "Here is an instruction with a shell command, followed by a complicated version of the instruction with its command.\n", "\n", "instruction: {sample[\"instruction\"]}\n", "command: {sample[\"command\"]}\n", "\"\"\"\n", "\n", "    if \"complicated_instruction\" in sample:\n", "        prompt += f\"\"\"\\\n", "complicated instruction: {sample[\"complicated_instruction\"]}\n", "complicated command: {sample[\"complicated_command\"]}\n", "\"\"\"\n", "\n", "    return prompt\n", "\n", "for sample in labelled_samples:\n", "    if \"complicated_instruction\" in sample:\n", "        assert \"complicated_command\" in sample\n", "        preamble += \"\\n\" + make_single_shot(sample)\n", "\n", "client = clients[\"code_llama_34b\"]\n", "\n", "new_samples = load_samples(\"good_samples.yaml\")\n", "\n", "for sample in new_samples:\n", "    assert \"complicated_instruction\" not in sample\n", "    print(HEADER)\n", "    prompt = preamble + \"\\n\" + make_single_shot(sample)\n", "    # print_prompt(prompt)\n", "    print(f\"original instruction: {sample['instruction']}\")\n", "    print(f\"original command: {sample['command']}\")\n", "    response = client.generate(prompt, max_generated_tokens=64, temperature=0.0, stop=[\"\\n\\n\"])\n", "    print(response.content)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evolution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from copy import copy, deepcopy\n", "from dataclasses import dataclass\n", "\n", "\n", "Sample = dict\n", "\n", "\n", "def find_missing_fields(source: dict, target: dict) -> list[str]:\n", "    \"\"\"Returns the set of fields that exist in \"source\" but not in \"target\".\"\"\"\n", "    return [field for field in source if field not in target]\n", "\n", "\n", "@dataclass\n", "class SampleTransformation:\n", "    transformation_type: str\n", "    sample_before: <PERSON><PERSON>\n", "    sample_after: <PERSON><PERSON>\n", "\n", "\n", "@dataclass\n", "class SamplesWithTransformations:\n", "    samples: list[<PERSON><PERSON>]\n", "    transformations: list[SampleTransformation]\n", "\n", "    def extend(self, other: \"SamplesWithTransformations\"):\n", "        self.samples.extend(other.samples)\n", "        self.transformations.extend(other.transformations)\n", "\n", "\n", "def flatten_sample(sample: <PERSON><PERSON>) -> SamplesWithTransformations:\n", "    \"\"\"Takes a sample with a transformation tree and returns a flat list.\n", "\n", "    The first returned sample is the root of the transformation tree.\n", "\n", "    If the sample has fields not contained in a sub-sample, those fields are\n", "    considered \"inherited\" and will be added to the sub-sample.\n", "\n", "    The original sample object is unchanged.\n", "    \"\"\"\n", "    sample = copy(sample)\n", "    result = SamplesWithTransformations(samples=[sample], transformations=[])\n", "\n", "    if \"transformations\" in sample:\n", "        sample_transformations = sample[\"transformations\"]\n", "        del sample[\"transformations\"]\n", "\n", "        for transformed_sample in sample_transformations:\n", "            transformed_sample = copy(transformed_sample)\n", "\n", "            # Copy transformation type aside\n", "            assert \"transformation_type\" in transformed_sample\n", "            transformation_type = transformed_sample[\"transformation_type\"]\n", "            del transformed_sample[\"transformation_type\"]\n", "\n", "            # Add inherited fields\n", "            inherited_fields = find_missing_fields(sample, transformed_sample)\n", "            for field in inherited_fields:\n", "                transformed_sample[field] = sample[field]\n", "\n", "            # Flatten recursively\n", "            recursive_samples: SamplesWithTransformations = flatten_sample(\n", "                transformed_sample\n", "            )\n", "            result.extend(recursive_samples)\n", "\n", "            # Record the transformation\n", "            result.transformations.append(\n", "                SampleTransformation(\n", "                    transformation_type=transformation_type,\n", "                    sample_before=sample,\n", "                    sample_after=recursive_samples.samples[0],\n", "                )\n", "            )\n", "\n", "    return result\n", "\n", "\n", "def flatten_loaded_samples(loaded_samples: list[Sample]) -> SamplesWithTransformations:\n", "    result = SamplesWithTransformations(samples=[], transformations=[])\n", "    for sample in loaded_samples:\n", "        flattened_sample = flatten_sample(sample)\n", "        result.extend(flattened_sample)\n", "    return result\n", "\n", "\n", "samples: SamplesWithTransformations = flatten_loaded_samples(\n", "    load_samples(\"basic_samples.yaml\")\n", ")\n", "\n", "\n", "def select_few_shot_samples(\n", "    samples: list[<PERSON><PERSON>],\n", "    few_shot_fields: list[str],\n", "    num_shots=3,\n", "    ) -> list[Sample]:\n", "    \"\"\"Sample few-shot samples for the prompt.\"\"\"\n", "    few_shot_samples: list[Sample] = []\n", "\n", "    def any_fields_repeat(sample: <PERSON><PERSON>) -> bool:\n", "        for prompt_sample in few_shot_samples:\n", "            for field in few_shot_fields:\n", "                if prompt_sample[field] == sample[field]:\n", "                    return True\n", "        return False\n", "\n", "    while len(few_shot_samples) < num_shots:\n", "        candidate: Sample = np.random.choice(samples, size=1)[0]  # type: ignore\n", "        # if any_fields_repeat(candidate):\n", "        #     continue\n", "        few_shot_samples.append(candidate)\n", "\n", "    return few_shot_samples\n", "\n", "\n", "def make_prompt(\n", "    system_prompt: str,\n", "    few_shot_samples: list[<PERSON><PERSON>],\n", "    new_sample_input: dict,\n", "    few_shot_fields: list[str],\n", "    new_sample_fields: list[str],\n", ") -> str:\n", "    # Prepare the few-shot prompt string\n", "    sample_prompts = []\n", "    system_prompt_header = system_prompt + \"\\n\\n\"\n", "\n", "    for sample in few_shot_samples:\n", "        sample_prompt = system_prompt_header\n", "        for field in few_shot_fields:\n", "            sample_prompt += f\"{field}: {sample[field]}\\n\"\n", "        sample_prompts.append(sample_prompt)\n", "\n", "    prompt = \"\\n\".join(sample_prompts)\n", "\n", "    # Add the question part of the prompt\n", "    prompt += \"\\n\" + system_prompt_header\n", "    for field in new_sample_fields:\n", "        prompt += f\"{field}: {new_sample_input[field]}\\n\"\n", "        # prompt += f\"{field}: {question[field]}\\n\"\n", "\n", "    return prompt\n", "\n", "\n", "def make_prompt_with_sampled_shots(\n", "    system_prompt: str,\n", "    samples: list[<PERSON><PERSON>],\n", "    new_sample_input: dict,\n", "    common_fields: list[str],\n", "    additional_few_shot_fields: list[str],\n", "    num_shots=3,\n", ") -> str:\n", "    # Sample a question\n", "    # samples = copy(samples)\n", "    # question: Sample = np.random.choice(samples, size=1)[0]  # type: ignore\n", "    # samples.remove(question)\n", "\n", "    few_shot_fields = common_fields + additional_few_shot_fields\n", "    few_shot_samples = select_few_shot_samples(\n", "        samples=samples,\n", "        few_shot_fields=few_shot_fields,\n", "        num_shots=num_shots)\n", "\n", "    prompt = make_prompt(\n", "        system_prompt=system_prompt,\n", "        few_shot_samples=few_shot_samples,\n", "        new_sample_input=new_sample_input,\n", "        few_shot_fields=few_shot_fields,\n", "        new_sample_fields=common_fields,\n", "    )\n", "    \n", "    return prompt\n", "        \n", "# np.random.seed(42)\n", "system_prompt = \"Translate the instruction to a valid bash shell command.\"\n", "categories = load_categories(Path(\"categories.txt\"))\n", "\n", "new_sample_input = {\n", "    \"type\": np.random.choice(categories, size=1)[0],\n", "    \"difficulty\": np.random.choice([\"easy\", \"medium\", \"hard\"], size=1)[0],\n", "}\n", "print(colored(\"new_sample_input:\", \"green\"))\n", "print(json.dumps(new_sample_input, indent=2))\n", "\n", "# prompt = make_prompt_with_sampled_shots(\n", "#     system_prompt=system_prompt,\n", "#     samples=samples.samples,\n", "#     new_sample_input=new_sample_input,\n", "#     common_fields=[\"type\", \"difficulty\"],\n", "#     additional_few_shot_fields=[\"instruction\", \"command\"],\n", "#     num_shots=5,\n", "# )\n", "\n", "print(colored(\"\\nprompt:\", \"green\"))\n", "print(prompt)\n", "client = clients[\"code_llama_34b\"]\n", "response = client.generate(prompt, max_generated_tokens=64, temperature=0.8, stop=[system_prompt])\n", "print(colored(\"response:\", \"green\"))\n", "print(response.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from abc import abstractmethod\n", "\n", "\n", "# class Sample:\n", "#     category: str\n", "#     instruction: str\n", "#     command: str\n", "\n", "\n", "# class SampleGenerator:\n", "#     @abstractmethod\n", "#     def prepare_prompt(seed_samples, question_sample) -> str:\n", "#         pass\n", "\n", "#     @abstractmethod\n", "#     def extract_generated_sample(generated_text):\n", "#         pass\n", "\n", "\n", "transformation_prompts = {\n", "    \"clarify\": \"Clarify the instruction by adding more details.\",\n", "    \"complicate\": \"Write a more complex version of the instruction.\",\n", "    \"rephrase\": \"Rephrase the instruction while maintaining its meaning.\",\n", "    \"terse\": \"Write a more terse version of the instruction.\",\n", "}\n", "\n", "seed_samples = load_samples(\"basic_samples.yaml\")\n", "question_samples = load_samples(\"good_samples.yaml\")\n", "\n", "\n", "# What a sample looks like:\n", "# {\n", "#     \"type\": \"images\",\n", "#     \"instruction\": \"resize my_image.png to a width of 800 pixels\",\n", "#     \"command\": \"convert my_image.png -resize 800x my_image_resized.png\",\n", "#     \"transformations\": [\n", "#         {\n", "#             \"transformation_type\": \"complicate\",\n", "#             \"instruction\": \"Find all JPEG and PNG files in the current directory and its subdirectories, resize them to a width of 800 pixels while maintaining aspect ratio, and save them with a \\\"_small\\\" suffix before the file extension.\",\n", "#             \"command\": \"find . -type f \\\\( -iname \\\"*.jpg\\\" -o -iname \\\"*.jpeg\\\" -o -iname \\\"*.png\\\" \\\\) -exec convert {} -resize 800x \\\\>{}_small\\\\> \\\\;\",\n", "#         },\n", "#     ]\n", "# },\n", "\n", "\n", "def get_evolution_prompt(\n", "        labelled_samples: list[dict],\n", "        question_sample: dict,\n", "        transformation_type: str):\n", "    assert transformation_type in transformation_prompts\n", "    shots = 3\n", "\n", "    # find all examples that have the transformation type\n", "    # TODO do this hierarchically\n", "    transformation_type_samples = []\n", "    for sample in labelled_samples:\n", "        if \"transformations\" in sample:\n", "            for transformation in sample[\"transformations\"]:\n", "                if transformation[\"transformation_type\"] == transformation_type:\n", "                    transformation_type_samples.append({\n", "                        \"before\": {\n", "                            \"instruction\": sample[\"instruction\"],\n", "                            \"command\": sample[\"command\"],\n", "                        },\n", "                        \"after\": {\n", "                            \"instruction\": transformation[\"instruction\"],\n", "                            \"command\": transformation[\"command\"],\n", "                        },\n", "                    })\n", "                    break\n", "\n", "    assert len(transformation_type_samples) >= shots\n", "\n", "    prompt_samples: list[dict] = np.random.choice(transformation_type_samples, shots, replace=False)  # type: ignore\n", "    sample_prompt = \"\"\n", "\n", "    for sample in prompt_samples:\n", "#         sample_prompt += f\"\"\"\\\n", "\n", "# {transformation_prompts[transformation_type]}\n", "\n", "# original instruction: {sample[\"before\"][\"instruction\"]}\n", "# transformed instruction: {sample[\"after\"][\"instruction\"]}\n", "# \"\"\"\n", "        sample_prompt += f\"\"\"\\\n", "\n", "{transformation_prompts[transformation_type]}\n", "\n", "original:\n", "- instruction: {sample[\"before\"][\"instruction\"]}\n", "- command: {sample[\"before\"][\"command\"]}\n", "new:\n", "- instruction: {sample[\"after\"][\"instruction\"]}\n", "- command: {sample[\"after\"][\"command\"]}\n", "\"\"\"\n", "\n", "    sample_prompt += f\"\"\"\\\n", "\n", "{transformation_prompts[transformation_type]}\n", "\n", "original:\n", "- instruction: {question_sample[\"instruction\"]}\n", "- command: {question_sample[\"command\"]}\n", "new:\n", "\"\"\"\n", "\n", "    return sample_prompt\n", "\n", "\n", "def evolve_a_sample(client: LlamaCppClient, seed_samples: list[dict], question_samples: list[dict], transformation_type: str):\n", "    question_sample = np.random.choice(question_samples, 1)[0]  # type: ignore\n", "    prompt = get_evolution_prompt(seed_samples, question_sample, transformation_type=transformation_type)\n", "    # print(colored(\"prompt:\", \"green\"))\n", "    # print(\"'''\" + prompt + \"'''\")\n", "    response = client.generate(prompt, max_generated_tokens=64, temperature=0.8, stop=[transformation_prompts[transformation_type]])\n", "    print(colored(\"question:\", \"green\"))\n", "    print(\"instruction:\", question_sample[\"instruction\"])\n", "    print(\"command:\", question_sample[\"command\"])\n", "    print(colored(\"response:\", \"green\"))\n", "    print(response.content)\n", "    return response.content\n", "\n", "\n", "for _ in range(10):\n", "    answer = evolve_a_sample(clients[\"code_llama_34b\"], seed_samples, question_samples, transformation_type=\"complicate\")\n", "    # print(answer)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Critique hallucinated examples (failed)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "SYSTEM_PROMPT = \"\"\"\\\n", "The following is an instruction translated to a shell command.\n", "Does the command uses files, URLs, or other objects that were not specified in the insturction?\n", "Specify which part was hallucinated and then answer True or False.\"\"\"\n", "\n", "hallucination_samples = [\n", "    {\n", "        \"instruction\": \"sync the files in /home/<USER>\",\n", "        \"command\": \"rsync -avz user@server:/home/<USER>/remote/path\",\n", "        \"hallucinated\": \"/remote/path\",\n", "        \"hallucinated_detailed\": \"'/home/<USER>' is specified and 'server' is specified, but target path '/remote/path' is not specified\",\n", "        \"hallucinated_detailed2\": \"'rsync' is a general command, '-avz' is a flag of the command, 'user' is a username which was not specified in the instruction\",\n", "        \"answer\": True\n", "    },\n", "    {\n", "        \"instruction\": \"show the average number of users logged in per day\",\n", "        \"command\": \"SELECT AVG(logins) AS logins FROM user_info;\",\n", "        \"hallucinated\": \"logins\",\n", "        \"hallucinated_detailed\": \"column 'logins' is not specified and table 'user_info' is not specified\",\n", "        \"hallucinated_detailed2\": \"'AVG' is a function, 'logins' is a column name which was not specified in the instruction\",\n", "        \"answer\": True\n", "    },\n", "    {\n", "        \"instruction\": \"how much disk space\",\n", "        \"command\": \"df -h\",\n", "        \"hallucinated\": \"\",\n", "        \"hallucinated_detailed\": \"no objects were used\",\n", "        \"hallucinated_detailed2\": \"'df' is a general command, '-h' is a flag of the command\",\n", "        \"answer\": <PERSON><PERSON><PERSON>\n", "    },\n", "    {\n", "        \"instruction\": \"show changes since commit 'foo'\",\n", "        \"command\": \"git diff foo HEAD\",\n", "        \"hallucinated\": \"\",\n", "        \"hallucinated_detailed\": \"commit 'foo' is specified\",\n", "        \"hallucinated_detailed2\": \"'git diff' is a general command, 'foo' is a commit which was specified in the instruction, 'HEAD' is a general git term\",\n", "        \"answer\": <PERSON><PERSON><PERSON>\n", "    },\n", "    {\n", "        \"instruction\": \"move all .txt files from folder foo/bar to folder /tmp\",\n", "        \"command\": \"mv foo/bar/*.txt /tmp/\",\n", "        \"hallucinated\": \"\",\n", "        \"hallucinated_detailed\": \"folder 'foo/bar' is specified, '/tmp' is specified, and file extension '.txt' is specified\",\n", "        \"hallucinated_detailed2\": \"'mv' is a general command, 'foo/bar' folder was specified in the instruction, '*.txt' was specified in the instruction, '/tmp/' was specified in the instruction\",\n", "        \"answer\": <PERSON><PERSON><PERSON>\n", "    },\n", "    {\n", "        \"instruction\": \"connect to *******\",\n", "        \"command\": \"ftp *******\",\n", "        \"hallucinated\": \"ftp\",\n", "        \"hallucinated_detailed\": \"address '*******' was specified but the protocol 'ftp' was not specified\",\n", "        \"hallucinated_detailed2\": \"the command 'ftp' refers to the ftp protocol which was not specified in the instruction\",\n", "        \"answer\": True\n", "    },\n", "    {\n", "        \"instruction\": \"list all lines with the word \\\"the\\\"\",\n", "        \"command\": \"grep -i the file.txt\",\n", "        \"hallucinated\": \"file.txt\",\n", "        \"hallucinated_detailed\": \"the string 'the' was specified but filename 'file.txt' was not specified\",\n", "        \"hallucinated_detailed2\": \"'grep -i' is a general command, 'the' was specified in the instruction, 'file.txt' was not specified in the instruction\",\n", "        \"answer\": True,\n", "    },\n", "]\n", "\n", "validation_hallucination_samples = [\n", "    {\n", "        \"instruction\": \"send <NAME_EMAIL> with subject test and body testing\",\n", "        \"command\": \"echo 'testing' | mutt -s \\\"test\\\" -- <EMAIL>\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"show database names\",\n", "        \"command\": \"mysql --execute='show databases;'\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"copy everything from directory1/directory2/file.txt to /home/<USER>\",\n", "        \"command\": \"rsync -avzr --info=progress2 /tmp/directory1/directory2/file.txt /home/<USER>\",\n", "        \"hallucinated\": \"/tmp/\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"add a red square on the image\",\n", "        \"command\": \"convert -draw 'color 10,20,30,40 rectangle 0,0,640,480' input.jpg output.jpg\",\n", "        \"hallucinated\": \"input.jpg\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"scan the network to find all running ssh servers\",\n", "        \"command\": \"nmap -p 22 --open -n -oG -\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"send an email to your friend\",\n", "        \"command\": 'echo \"Hello, world!\" | mail -s \"Welcome\" <EMAIL>',\n", "        \"hallucinated\": \"<EMAIL>\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"list running containers\",\n", "        \"command\": \"docker ps\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"compress file.txt with gzip\",\n", "        \"command\": \"gzip -c file.txt > file.gz\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"list all files in S3 bucket\",\n", "        \"command\": \"aws s3 ls <bucket-name>\",\n", "        \"hallucinated\": \"<bucket-name>\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"download file.txt from server\",\n", "        \"command\": \"curl -O ftp://server/file.txt\",\n", "        \"hallucinated\": \"ftp\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"count number of lines in file.txt\",\n", "        \"command\": \"cat file.txt | wc -l\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"what is my current working directory\",\n", "        \"command\": \"pwd\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"upload file.txt to server-1\",\n", "        \"command\": \"scp file.txt root@server-1:/root/file.txt\",\n", "        \"hallucinated\": \"root\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"list all the available files\",\n", "        \"command\": \"wget --mirror --convert-links --adjust-extension -e robots=off -P /home/<USER>//files.katacoda.com/http/example\",\n", "        \"hallucinated\": \"/home/<USER>\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"send <NAME_EMAIL>\",\n", "        \"command\": 'mail -s \"Message from test\" <EMAIL>',\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"download file.txt\",\n", "        \"command\": \"ftpget /home/<USER>/file.txt\",\n", "        \"hallucinated\": \"/home/<USER>\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"create a zip file\",\n", "        \"command\": \"zip -r archive.zip folder/\",\n", "        \"hallucinated\": \"folder/\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"send an email to alice\",\n", "        \"command\": 'echo \"Hello\" | mail -s \"Hi Alice\" <EMAIL>',\n", "        \"hallucinated\": \"example.com\",\n", "        \"answer\": True,\n", "    },\n", "    {\n", "        \"instruction\": \"get status of pods\",\n", "        \"command\": \"kube<PERSON>l get pods\",\n", "        \"hallucinated\": \"\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "        \"instruction\": \"download the file.txt\",\n", "        \"command\": \"wget http://localhost/file.txt\",\n", "        \"hallucinated\": \"localhost\",\n", "        \"answer\": <PERSON><PERSON><PERSON>,\n", "    },\n", "]\n", "\n", "def make_single_hallucination_critique_prompt(sample, detailed=True):\n", "    prompt = f\"\"\"\\\n", "{SYSTEM_PROMPT}\n", "\n", "instruction: {sample[\"instruction\"]}\n", "command: {sample[\"command\"]}\n", "\"\"\"\n", "\n", "    if \"hallucinated\" in sample and \"answer\" in sample:\n", "        if detailed:\n", "            hallucination = sample[\"hallucinated_detailed2\"]\n", "        else:\n", "            hallucination = sample[\"hallucinated\"]\n", "        prompt += f\"\"\"\\\n", "hallucinated: {hallucination}\n", "answer: {sample[\"answer\"]}\n", "\"\"\"\n", "\n", "    return prompt\n", "\n", "\n", "def get_model_answer_with_random_prompt(client, val_sample: dict) -> Optional[bool]:\n", "    num_shots = 4\n", "    samples_for_prompt = np.random.choice(hallucination_samples, size=num_shots)\n", "    np.random.shuffle(samples_for_prompt)\n", "\n", "    critique_prompt = \"\\n\".join([make_single_hallucination_critique_prompt(sample) for sample in samples_for_prompt])\n", "\n", "    val_sample_for_prompt = dict(val_sample)\n", "    del val_sample_for_prompt[\"hallucinated\"]\n", "    del val_sample_for_prompt[\"answer\"]\n", "    prompt = critique_prompt + \"\\n\" + make_single_hallucination_critique_prompt(val_sample_for_prompt)\n", "\n", "    # print(colored(\"prompt:\", \"blue\"))\n", "    # print(\"'''\" + prompt + \"'''\")\n", "\n", "    print(colored(\"\\nsample:\", \"blue\"))\n", "    print(make_single_hallucination_critique_prompt(samples_for_prompt[0]) + \"\\n\" + make_single_hallucination_critique_prompt(val_sample_for_prompt))\n", "\n", "    # print(\"val sample:\")\n", "    # print(json.dumps(val_sample, indent=2))\n", "\n", "    stop_string = \"The following is\"\n", "    assert SYSTEM_PROMPT.startswith(stop_string)\n", "\n", "    response = client.generate(prompt, max_generated_tokens=64, temperature=0.0, stop=[stop_string])\n", "    # print(f\"answer generated by {response.model}\")\n", "    print(colored(f\"generated by {response.model}:\", \"blue\"))\n", "    print(response.content)\n", "\n", "    # parse the response\n", "    m = re.match(r\"^hallucinated:([^\\n]*)\\nanswer:([^\\n]*)\", response.content)\n", "    if m:\n", "        # hallucinated = m.group(1).strip()\n", "        answer = m.group(2).strip()\n", "        bool_answer: bool = answer.lower() == \"true\"\n", "        # print(colored(\"model generated:\", \"green\"))\n", "        # print(\"hallucinated:\", hallucinated)\n", "        # print(f\"answer: '''{answer}''' bool={bool_answer}\")\n", "        return bool_answer\n", "    else:\n", "        print(\"Invalid response:\")\n", "        print(\"'''\" + response.content + \"'''\")\n", "        return None\n", "        # raise ValueError(\"Invalid response\")\n", "\n", "\n", "majority_voting_trials = 3\n", "# majority_trials = 1\n", "assert majority_voting_trials % 2 == 1\n", "\n", "# client = clients[\"llama_70b\"]\n", "# client = clients[\"code_llama_34b\"]\n", "\n", "results = {}\n", "\n", "for model_name, client in clients.items():\n", "    if \"instruct\" in model_name:\n", "        continue\n", "\n", "    num_correct = 0\n", "    for i, val_sample in enumerate(validation_hallucination_samples):\n", "        print (f\"\\n=== model: {model_name} sample: {i}/{len(validation_hallucination_samples)} ===\" + \"=\" * 80)\n", "        model_answers = []\n", "        while len(model_answers) < majority_voting_trials:\n", "            model_answer = get_model_answer_with_random_prompt(client, val_sample)\n", "            if model_answer is not None:\n", "                model_answers.append(model_answer)\n", "        avg_answer = np.mean(model_answers)\n", "        model_answer = avg_answer > 0.5\n", "        # print(f\"model_answers={model_answers} avg_answer={avg_answer} model_answer={model_answer}\")\n", "\n", "        correct = (model_answer == val_sample[\"answer\"])\n", "        num_correct += int(correct)\n", "        print(\"correct?\", correct)\n", "\n", "    print(f\"num correct: {num_correct}\")\n", "    acc = num_correct / len(validation_hallucination_samples) * 100\n", "    print(f\"accuracy: {acc:.1f}%\")\n", "    results[model_name] = acc\n", "\n", "print(\"All accuracies:\")\n", "print(json.dumps(results, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate categories (succeeded)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shell_categories_prompt = (\n", "    \"A list of categories of things that users typically do in a shell terminal.\"\n", ")\n", "\n", "shell_seed_categories = [\n", "    \"file-manipulation\",\n", "    \"systems\",\n", "    \"docker\",\n", "    \"process-management\",\n", "    \"database-queries\",\n", "    \"git\",\n", "    \"k8s\",\n", "    \"vim\",\n", "    \"ssh\",\n", "    \"networking\",\n", "    \"file-transfer\",\n", "    \"environment\",\n", "    \"irc\",\n", "]\n", "\n", "code_edit_categories_prompt = (\n", "    \"A list of categories of ways developers typically edit their code.\"\n", ")\n", "\n", "code_edit_seed_categories = [\n", "    \"formatting\",\n", "    \"linting\",\n", "    \"refactoring\",\n", "    \"documenting\",\n", "    \"debugging\",\n", "    \"fixing\",\n", "    \"performance\",\n", "    \"optimization\",\n", "    \"variable-renaming\",\n", "    \"structure-data\",\n", "    \"i18n\",\n", "    \"translation\",\n", "]\n", "\n", "\n", "def generate_categories_one_round(\n", "    client: LlamaCppClient,\n", "    seed_categories: list[str],\n", "    categories_prompt: str,\n", "    min_number: int,\n", "    blacklist: list[str],\n", ") -> list[str]:\n", "    categories = []\n", "    num_shots = 5\n", "    item_prefix = \"\\n- \"\n", "    while len(categories) < min_number:\n", "        prompt_categories = np.random.choice(seed_categories, num_shots, replace=False)\n", "        prompt = categories_prompt + \"\\n\"\n", "        prompt += item_prefix + item_prefix.join(prompt_categories) + item_prefix\n", "        # print(f\"'''{prompt}'''\")\n", "        response = client.generate(prompt, max_generated_tokens=64, temperature=0.8)\n", "        answers = response.content.split(item_prefix)\n", "\n", "        def is_good_answer(answer):\n", "            if not answer:\n", "                return False\n", "            if answer in seed_categories or answer in categories:\n", "                return False\n", "            if answer in blacklist:\n", "                return False\n", "            if \"\\n\" in answer:\n", "                return False\n", "            if not re.match(r\"^[a-zA-Z][a-zA-Z-]*$\", answer):\n", "                return False\n", "            return True\n", "\n", "        for answer in answers:\n", "            answer = answer.strip()\n", "            if is_good_answer(answer):\n", "                categories.append(answer)\n", "    return categories\n", "\n", "\n", "def generate_categories(\n", "    client: LlamaCppClient,\n", "    seed_categories: list[str],\n", "    categories_prompt: str,\n", "    target_num_categories: int,\n", "    blacklist: list[str] = [],\n", ") -> list[str]:\n", "    num_categories_per_round = 3\n", "    categories = list(seed_categories)\n", "\n", "    while len(categories) < target_num_categories:\n", "        print(f\"=== starting round with {len(categories)} categories ===\")\n", "        new_categories = generate_categories_one_round(\n", "            client, categories, categories_prompt, num_categories_per_round, blacklist\n", "        )\n", "        print(f\"generated {len(new_categories)} categories:\")\n", "        for cat in new_categories:\n", "            print(f\"\\t{cat}\")\n", "        categories.extend(new_categories)\n", "\n", "    return categories\n", "\n", "\n", "def save_categories(path: str, categories: list[str]):\n", "    with Path(path).open(\"w\", encoding=\"utf-8\") as file:\n", "        file.write(\"\\n\".join(categories) + \"\\n\")\n", "    print(f\"{len(categories)} categories saved to {path}\")\n", "\n", "\n", "# generated_categories = generate_categories(\n", "#     clients[\"code_llama_34b\"],\n", "#     shell_seed_categories,\n", "#     shell_categories_prompt,\n", "#     target_num_categories=100,\n", "#     blacklist=[\"irc\"],\n", "#     )\n", "# save_categories(\"shell_categories_draft.txt\", generated_categories)\n", "\n", "generated_categories = generate_categories(\n", "    clients[\"code_llama_34b\"],\n", "    code_edit_seed_categories,\n", "    code_edit_categories_prompt,\n", "    target_num_categories=80,\n", "    blacklist=[\"syntax-highlighting\"],\n", ")\n", "save_categories(\"code_edit_categories_draft.txt\", generated_categories)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Directly ask DeepSeek Coder Instruct"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = clients[\"deepseek-coder-instruct-33b\"]\n", "\n", "SYSTEM_PROMPT = \"Convert the following instruction to a one-line bash command:\"\n", "\n", "num_prompt_samples = 3\n", "orig_prompt = \"\"\n", "for sample in samples[:num_prompt_samples]:\n", "    orig_prompt += f\"\"\"\\\n", "\n", "{SYSTEM_PROMPT}\n", "instruction: {sample[\"instruction\"]}\n", "command: {sample[\"command\"]}\n", "\"\"\"\n", "\n", "orig_prompt += f\"\\n{SYSTEM_PROMPT}\\n\"\n", "\n", "instruction = \"list all tables in personnel.sqlite\"\n", "prompt = orig_prompt + f\"\"\"\\\n", "instruction: {instruction}\n", "\"\"\"\n", "response = client.generate(prompt, max_generated_tokens=64, temperature=0., stop=[])\n", "print(response.content)\n", "\n", "\n", "# val_samples = samples[num_prompt_samples:]\n", "\n", "# for sample in val_samples:\n", "#     preamble = orig_prompt + f\"\"\"\\\n", "# instruction: {sample[\"instruction\"]}\n", "# \"\"\"\n", "#     # print(prompt)\n", "#     response = client.generate(preamble, max_generated_tokens=64, temperature=0., stop=[])\n", "#     print(json.dumps(sample, indent=2))\n", "#     print(\"deepseek command:\", response.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = clients[\"deepseek-coder-instruct-33b\"]\n", "\n", "preamble = f\"\"\"\\\n", "The following is an instruction translated to a shell command.\n", "Does the command uses files, URLs, or other objects that were not specified in the instruction?\n", "Specify which part was not specified and then answer \"yes\" or \"no\".\n", "\n", "instruction: sync the files in /home/<USER>\n", "command: rsync -avz user@server:/home/<USER>/remote/path,\n", "hallucination: user@server was not specified in the instruction\n", "answer: yes\n", "\n", "The following is an instruction translated to a shell command.\n", "Does the command uses files, URLs, or other objects that were not specified in the instruction?\n", "Specify which part was not specified and then answer \"Yes\" or \"No\".\n", "\n", "instruction: count number of lines in file.txt\n", "command: cat file.txt | wc -l\n", "hallucination: file.txt was specified in the instruction\n", "answer: no\n", "\n", "The following is an instruction translated to a shell command.\n", "Does the command uses files, URLs, or other objects that were not specified in the instruction?\n", "Specify which part was not specified and then answer \"Yes\" or \"No\".\n", "\n", "instruction: upload file.txt to server-1\n", "command: scp file.txt root@server-1:/root/file.txt\n", "hallucinated: root user was not specified\n", "answer: yes\n", "\n", "The following is an instruction translated to a shell command.\n", "Does the command uses files, URLs, or other objects that were not specified in the instruction?\n", "Specify which part was not specified and then answer \"yes\" or \"no\".\n", "\"\"\"\n", "\n", "\n", "# instruction: what is my current working directory\n", "# command: pwd\n", "# hallucinated: it uses the correct command\n", "# answer: no\n", "\n", "response = client.generate(preamble, max_generated_tokens=128)\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## First attempt at code edits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# client = clients[\"code_llama_34b\"]\n", "# client = clients[\"deepseek-coder-instruct-33b\"]\n", "client = clients[\"llama_70b\"]\n", "\n", "prompt = \"\"\"\\\n", "Edit the code according to the following instruction.\n", "\n", "type: editing\n", "instruction: change double quote to single\n", "code:\n", "```python\n", "    with (Path(__file__).parent / \"language_extensions.json\").open(\n", "        \"r\", encoding=\"utf8\"\n", "    ) as file:\n", "        language_data = json.load(file)\n", "        supported_languages = [\n", "            {\n", "                \"name\": lang[\"name\"],\n", "                \"vscode_name\": lang[\"vscode_name\"],\n", "                \"extensions\": lang[\"extensions\"],\n", "            }\n", "            for lang in language_data\n", "            if \"vscode_name\" in lang and is_language_supported(lang)\n", "        ]\n", "```\n", "edited code:\n", "```python\n", "    with (Path(__file__).parent / 'language_extensions.json').open(\n", "        'r', encoding='utf8'\n", "    ) as file:\n", "        language_data = json.load(file)\n", "        supported_languages = [\n", "            {\n", "                'name': lang['name'],\n", "                'vscode_name': lang['vscode_name'],\n", "                'extensions': lang['extensions'],\n", "            }\n", "            for lang in language_data\n", "            if 'vscode_name' in lang and is_language_supported(lang)\n", "        ]\n", "```\n", "\n", "Edit the code according to the following instruction.\n", "\n", "type: formatting\n", "instruction: fix the formatting\n", "code:\n", "```python\n", "def _get_generation_options(\n", "    max_generated_tokens: int,\n", "    request_content: Union[CompletionRequest, ReplacementRequest],\n", "):\n", "    def get(value, default):\n", "        if value is None:\n", "            return default\n", "        return value\n", "\n", "    temperature = get(\n", "        request_content.temperature, default=GenerationOptions.temperature\n", "    )\n", "    top_k = get(request_content.top_k, default=GenerationOptions.top_k)\n", "    top_p = get(request_content.top_p, default=GenerationOptions.top_p)\n", "    max_tokens = get(request_content.max_tokens, default=max_generated_tokens)\n", "```\n", "edited code:\n", "```python\n", "def _get_generation_options( max_generated_tokens: int, request_content: Union[CompletionRequest, ReplacementRequest],\n", "):\n", "    def get(value, default):\n", "        if value is None:\n", "            return default\n", "        return value\n", "\n", "    temperature = get( request_content.temperature,\n", "                            default=GenerationOptions.temperature\n", "    )\n", "    top_k = get(request_content.top_k, \n", "        default=GenerationOptions.top_k)\n", "    top_p = get(request_content.top_p, default=GenerationOptions.top_p)\n", "    max_tokens = get(request_content.max_tokens, default=max_generated_tokens)\n", "```\n", "\n", "Edit the code according to the following instruction.\n", "\n", "type: refactoring\n", "instruction: move the main logic to a function\n", "code:\n", "```python\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--storage_path\",\n", "        type=str,\n", "        default=None,\n", "        help=\"path to the file store\",\n", "    )\n", "    args = parser.parse_args()\n", "\n", "    file_store = FileStore(Path(args.storage_path))\n", "    for doc in file_store.get_files():\n", "        print(doc.path)\n", "        print(doc.text)\n", "```\n", "edited code:\n", "```python\n", "def print_files(storage_path: str):\n", "    file_store = FileStore(Path(storage_path))\n", "    for doc in file_store.get_files():\n", "        print(doc.path)\n", "        print(doc.text)\n", "\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--storage_path\",\n", "        type=str,\n", "        default=None,\n", "        help=\"path to the file store\",\n", "    )\n", "    args = parser.parse_args()\n", "    print_files(args.storage_path)\n", "```\n", "\n", "Edit the code according to the following instruction.\n", "\n", "type: bugfix\n", "instruction: fix the bug\n", "code:\n", "```python\n", "def factorial(n):\n", "    return n * factorial(n-1)\n", "```\n", "edited code:\n", "```python\n", "def factorial(n):\n", "    if n == 0:\n", "        return 1\n", "    return n * factorial(n - 1)\n", "\n", "Edit the code according to the following instruction.\n", "\n", "type: translation\n", "instruction: turn it into valid python\n", "```python\n", "import argparse\n", "\n", "def main():\n", "    parse --text flag\n", "```\n", "edited code:\n", "```python\n", "import argparse\n", "\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--text\",\n", "        type=str,\n", "        default=\"Hello, <PERSON>!\",\n", "        help=\"text to tokenize\",\n", "    )\n", "    args = parser.parse_args()\n", "```\n", "\n", "Edit the code according to the following instruction.\n", "\n", "type: format\n", "instruction:\"\"\"\n", "\n", "response = client.generate(prompt=prompt, max_generated_tokens=1024, temperature=0.0, stop=[])\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scratchpad and old evol-instruct code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["few_shot_preamble = \"\"\"\\\n", "Please increase the difficulty of the given programming test question a bit.\n", "\n", "You can increase the difficulty using, but not limited to, the following methods:\n", "Add new constraints and requirements to the original problem, adding approximately 10 additional words.\n", "\n", "Original question:\n", "Create an array of length 5 which contains all even numbers between 1 and 10.\n", "\n", "More difficult question:\n", "Create an array of length k which contains all the even numbers between 1 and 2k.\n", "\n", "\n", "Please increase the difficulty of the given programming test question a bit.\n", "\n", "You can increase the difficulty using, but not limited to, the following methods:\n", "If the original problem can be solved with only a few logical steps, please add more reasoning steps.\n", "\n", "Original question:\n", "Create an array of length 15 containing numbers divisible by 3 up to 45.\n", "\n", "More difficult question:\n", "Create an array of length 15 containing numbers divisible by 3 or 4 up to 60.\n", "\"\"\"\n", "\n", "preamble = get_evol_prompt(code_alpaca_data[5][\"instruction\"])\n", "print(\"Prompt:\\n'''\")\n", "print(preamble, end=\"\")\n", "print(\"'''\\n\")\n", "\n", "full_prompt = few_shot_preamble + \"\\n\\n\" + preamble\n", "\n", "max_generated = 64\n", "\n", "for name, client in clients.items():\n", "    response = client.generate(preamble, max_generated)\n", "    # print(json.dumps(response.full_response, indent=2))\n", "    print(f\"Model: {response.model}\")\n", "    print(f\"Generated:\\n{response.content}\")\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "with Path(\"/mnt/efs/augment/user/guy/code_alpaca_20k.json\").open(\"r\", encoding=\"utf-8\") as file:\n", "    code_alpaca_data = json.load(file)\n", "\n", "def get_evol_prompt(question):\n", "    methods = [\n", "        \"Add new constraints and requirements to the original problem, adding approximately 10 additional words.\",\n", "        \"Replace a commonly used requirement in the programming task with a less common and more specific one.\",\n", "        \"If the original problem can be solved with only a few logical steps, please add more reasoning steps.\",\n", "        # \"Provide a piece of erroneous code as a reference to increase misdirection.\",\n", "        \"Propose more strict space complexity requirements.\",\n", "    ]\n", "    method = np.random.choice(methods)\n", "    prompt = f\"\"\"\\\n", "Please increase the difficulty of the given programming test question a bit.\n", "\n", "You can increase the difficulty using, but not limited to, the following methods:\n", "{method}\n", "\n", "Original question:\n", "{question}\n", "\n", "More difficult question:\n", "\"\"\"\n", "    return prompt\n", "\n", "# prompt = \"def hello():\"\n", "# max_generated = 64\n", "\n", "# for name, client in clients.items():\n", "#     response = client.generate(prompt, max_generated)\n", "#     # print(json.dumps(response.full_response, indent=2))\n", "#     print(\"Model:\", response.model)\n", "#     print(\"Generated:\", response.content)\n", "#     print(\"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}