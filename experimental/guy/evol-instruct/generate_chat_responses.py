"""Generate model responses from a canned list of instructions."""

import os
from pathlib import Path

from utils import LlamaCppClient, MistralClient, ModelDetails, generate_model_responses

from research.core.llama_prompt_formatters import (
    CodeLlamaChatFormatter,
    DeepSeekCoderInstructFormatter,
)

models = [
    ModelDetails(
        name="code-llama-70b-instruct",
        client=LlamaCppClient(address="************:5000", timeout=40),
        prompt_formatter=CodeLlamaChatFormatter(model_with_step_token=True),
        stop=["Source: assistant"],
    ),
    ModelDetails(
        name="deepseek-33b-instruct",
        client=LlamaCppClient(address="***********:5000"),
        prompt_formatter=DeepSeekCoderInstructFormatter(),
        stop=["### Prompt:", "### Instruction:"],
    ),
    ModelDetails(
        name="mistral-medium",
        client=MistralClient(os.environ["MISTRAL_API_KEY"]),
        prompt_formatter=None,
    ),
]


def main():
    max_generated_tokens = 256
    text = Path("chat-examples.txt").read_text(encoding="utf8")
    examples = [ex for ex in text.split("###\n") if ex.strip()]
    for ex in examples:
        print(f"example:\n{ex}\n")
        responses = generate_model_responses(
            models=models,
            prompt=ex,
            max_generated_tokens=max_generated_tokens,
        )
        for model, response in zip(models, responses):
            print(f"{model.name}:\n{response}\n")
        print("")


if __name__ == "__main__":
    main()
