[{"type": "https", "instruction": "what is my ip", "command": "hostname -i"}, {"type": "configuring", "instruction": "list all configuration files", "command": "ls -l /etc"}, {"type": "compression", "instruction": "compress the file.txt", "command": "gzip file.txt"}, {"type": "terminal", "instruction": "list the environment variables", "command": "env"}, {"type": "versioning", "instruction": "get the current branch", "command": "git branch"}, {"type": "go", "instruction": "go build", "command": "go build"}, {"type": "misc", "instruction": "show the processes running", "command": "ps -ef"}, {"type": "top", "instruction": "what is the name of my shell", "command": "echo $SHELL"}, {"type": "systems", "instruction": "show the uptime of my system", "command": "uptime"}, {"type": "ia", "instruction": "the first command of a shell script", "command": "#!/bin/bash"}, {"type": "dotnet", "instruction": "build the code", "command": "make"}, {"type": "search", "instruction": "list all processes with the word firefox", "command": "ps aux | grep firefox"}, {"type": "versioning", "instruction": "get the latest version of the software", "command": "git pull"}, {"type": "diffs", "instruction": "diff file1.txt with the latest version in the repo", "command": "git diff file1.txt"}, {"type": "database-queries", "instruction": "show all columns in table users", "command": "mysql -e 'SELECT * FROM users'"}, {"type": "fish", "instruction": "how do I enable colors in the shell", "command": "echo $TERM"}, {"type": "zsh-commands", "instruction": "how do I exit?", "command": "exit"}, {"type": "manpages", "instruction": "show the manual of ls command", "command": "man ls"}, {"type": "python", "instruction": "import math", "command": "import math"}, {"type": "docker-images", "instruction": "list all docker images", "command": "docker images"}, {"type": "search", "instruction": "find all files containing 'hello world'", "command": "grep -R 'hello world'"}, {"type": "k8s", "instruction": "list all pods with status running or pending", "command": "kubectl get pods | grep -E '(Running|Pending)'"}, {"type": "disk-usage", "instruction": "how much free space", "command": "df -h"}, {"type": "process-management", "instruction": "list all running processes", "command": "ps -ef"}, {"type": "k8s", "instruction": "list all pods with status running", "command": "kubectl get pods | grep Running"}, {"type": "process-management", "instruction": "kill all nginx processes", "command": "killall nginx"}, {"type": "k8s", "instruction": "list all deployments in the namespace my-namespace", "command": "kubectl get deployments -n my-namespace"}, {"type": "find-command", "instruction": "find the command to run a web server", "command": "man httpd"}, {"type": "networking", "instruction": "show my ip", "command": "curl ifconfig.me"}, {"type": "search", "instruction": "search for file.txt", "command": "find / -name file.txt"}, {"type": "networking", "instruction": "show network statistics", "command": "netstat -s"}, {"type": "mail", "instruction": "send the message with subject 'hello' to <EMAIL>", "command": "echo 'hello' | mail -s 'hello' <EMAIL>"}, {"type": "systems", "instruction": "show all running processes", "command": "ps -ef"}, {"type": "ia", "instruction": "check the cpu status", "command": "top"}, {"type": "docker-images", "instruction": "list all docker images", "command": "docker images"}, {"type": "kubernetes", "instruction": "list all pods with status running or pending", "command": "kubectl get pods | grep -E '(Running|Pending)'"}, {"type": "search", "instruction": "how to search", "command": "grep"}, {"type": "package-management", "instruction": "list all packages", "command": "dpkg -l"}, {"type": "shell-scripts", "instruction": "print the number of lines in file.txt", "command": "wc -l file.txt"}, {"type": "compression", "instruction": "unzip file.zip", "command": "unzip file.zip"}, {"type": "ia", "instruction": "list all instances", "command": "aws ec2 describe-instances"}, {"type": "terminal", "instruction": "clear the terminal", "command": "clear"}, {"type": "vim", "instruction": "set line number to show", "command": "set nu"}, {"type": "disk-usage", "instruction": "show the size of file.txt in bytes", "command": "stat -c %s file.txt"}, {"type": "find-command", "instruction": "what is the command for listing processes", "command": "ps"}, {"type": "security", "instruction": "show the current processes", "command": "ps -ef"}, {"type": "awk", "instruction": "print the second column of file.txt", "command": "cat file.txt | awk '{print $2}'"}, {"type": "editors", "instruction": "start vim", "command": "vim"}, {"type": "system-monitoring", "instruction": "monitor all processes", "command": "top"}, {"type": "ffmpeg", "instruction": "mute the video", "command": "amixer set Master mute"}, {"type": "disk-usage", "instruction": "print the size of the files", "command": "du -sh *"}, {"type": "disk-usage", "instruction": "show space usage", "command": "df -h"}, {"type": "search", "instruction": "find all occurrences of the word 'test'", "command": "grep -r 'test'"}, {"type": "python-pip", "instruction": "install numpy", "command": "pip install numpy"}, {"type": "rust", "instruction": "install rust", "command": "curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"}, {"type": "manpages-and-info", "instruction": "show the manual for git", "command": "man git"}, {"type": "environment", "instruction": "list the environment variables", "command": "env"}, {"type": "shell-scripting", "instruction": "count the number of directories", "command": "find . -type d | wc -l"}, {"type": "ssh-keyscan", "instruction": "scan for ssh keys", "command": "find ~/.ssh -type f"}, {"type": "search", "instruction": "search for the word hello in the file file.txt", "command": "grep 'hello' file.txt"}, {"type": "shell-scripts", "instruction": "list all the files in this directory", "command": "ls"}, {"type": "awk", "instruction": "print the first column of file.txt", "command": "cat file.txt | awk '{print $1}'"}, {"type": "aws", "instruction": "show all vpcs", "command": "aws ec2 describe-vpcs"}, {"type": "systems", "instruction": "what is the operating system", "command": "uname -a"}, {"type": "file-management", "instruction": "delete the file.txt", "command": "rm file.txt"}, {"type": "etc", "instruction": "show the version of the shell", "command": "echo $BASH_VERSION"}, {"type": "shell-scripting", "instruction": "show me the history", "command": "history"}, {"type": "shell-programming", "instruction": "get the first 10 lines of file.txt", "command": "head -n 10 file.txt"}, {"type": "systems", "instruction": "check the number of cpu cores", "command": "nproc"}, {"type": "directory-traversal", "instruction": "show all files and directories", "command": "ls -a"}, {"type": "sed", "instruction": "remove everything after the last forward slash in a line", "command": "sed 's#.*/##'"}, {"type": "cpio", "instruction": "extract the cpio archive", "command": "cpio -idmv"}, {"type": "node-npm-yarn", "instruction": "install dependencies", "command": "npm install"}, {"type": "web-browsing", "instruction": "download the file from http://example.com/", "command": "curl -O http://example.com/"}, {"type": "directory-manipulation", "instruction": "show the files in a directory", "command": "ls"}, {"type": "kubernetes", "instruction": "list all pods with status running or pending", "command": "kubectl get pods | grep -E '(Running|Pending)'"}, {"type": "git", "instruction": "show all the commits that have been made", "command": "git log"}, {"type": "package-management", "instruction": "update all packages", "command": "apt update && apt upgrade"}, {"type": "dns", "instruction": "resolve the ip of domain google.com", "command": "dig +short google.com"}, {"type": "emacs", "instruction": "open file.txt", "command": "xdg-open file.txt"}, {"type": "git", "instruction": "git branch", "command": "git branch"}, {"type": "go", "instruction": "print the first element", "command": "echo $1"}, {"type": "archives", "instruction": "list the contents of archive.zip", "command": "unzip -l archive.zip"}, {"type": "kubernetes", "instruction": "get all the namespaces", "command": "kubectl get namespaces"}, {"type": "cron", "instruction": "run the script every day at midnight", "command": "crontab -e"}, {"type": "versioning", "instruction": "show the current git branch", "command": "git branch | grep \\*"}, {"type": "node-npm-yarn", "instruction": "install the node dependencies", "command": "npm install"}, {"type": "docker-images", "instruction": "list all docker images", "command": "docker images"}, {"type": "versioning", "instruction": "check the version of git", "command": "git --version"}, {"type": "python", "instruction": "run python script.py", "command": "python script.py"}, {"type": "shell-scripting", "instruction": "iterate over all elements of the array", "command": "for i in \"${array[@]}\"; do echo \"$i\"; done"}, {"type": "systems", "instruction": "list all processes", "command": "ps -ef"}, {"type": "dotnet", "instruction": "create a new dotnet application", "command": "dotnet new console"}, {"type": "http", "instruction": "show the http headers for github.com", "command": "curl -I https://github.com"}, {"type": "mail", "instruction": "send an <NAME_EMAIL>", "command": "mail -s \"subject\" <EMAIL>"}, {"type": "zsh-commands", "instruction": "print the working directory", "command": "pwd"}, {"type": "top", "instruction": "list all running processes", "command": "ps -ef"}, {"type": "aws", "instruction": "aws list all vpcs", "command": "aws ec2 describe-vpcs"}, {"type": "programming", "instruction": "what is a hashmap in go", "command": "https://golang.org/pkg/hash/#Map"}, {"type": "mutt", "instruction": "set the default editor to vi", "command": "export EDITOR=vi"}]