file-manipulation
systems
docker
process-management
database-queries
git
k8s
# vim
ssh
networking
file-transfer
environment
tmux
crypto
mail
git-and-hg
ssh-and-scp
shell-scripting
versioning
manpages-and-info
email
editors
package-management
shell-scripts
web-browsing
vcs
manpages
wget
curl
ffmpeg
zsh-commands
python
nmap
tcpdump
wireshark
dns
image-processing
graphviz
ftp
emacs
directory-manipulation
diffs
compiling-and-linking
cpio
search
web
security
misc
etc
games
vpn
aws
vi
grep
find-command
cron
file-management
man-pages
programming
tmux-and-screen
apt
top
rsync
scp
tar
psql
node-npm-yarn
python-pip
java-maven-gradle
dotnet
kubernetes
disk-usage
directory-traversal
configuring
ssh-agent
ia
vagrant
shell-programming
http
xtools
fish
archives
compression
tarball
get
https
git-interaction
system-monitoring
ssh-keyscan
docker-images
terminal
sed
awk
