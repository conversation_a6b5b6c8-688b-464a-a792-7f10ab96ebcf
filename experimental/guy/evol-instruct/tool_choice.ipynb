{"cells": [{"cell_type": "code", "execution_count": 17, "id": "9c073204", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Message(id='msg_018AzgxHHjdL5Ep7RL4VQHVe', content=[TextBlock(citations=None, text='I can calculate the sum of 1234 and 12765 without using tools.\\n\\n1234 + 12765 = 13999\\n\\nSo the answer is 13,999.', type='text')], model='claude-3-7-sonnet-20250219', role='assistant', stop_reason='end_turn', stop_sequence=None, type='message', usage=Usage(cache_creation_input_tokens=0, cache_read_input_tokens=0, input_tokens=592, output_tokens=45))\n"]}], "source": ["# Prerequisites:\n", "# > pip3 install --upgrade --user google-cloud-aiplatform\n", "# > gcloud auth application-default login\n", "# > pip3 install -U 'anthropic[vertex]'\n", "\n", "from anthropic import Anthropic, AnthropicVertex\n", "\n", "from pathlib import Path\n", "import os\n", "\n", "region = \"us-east5\"\n", "project_id = \"augment-387916\"\n", "# model = \"claude-3-5-sonnet@20240620\"\n", "model = \"claude-3-7-sonnet-20250219\"\n", "\n", "vertex_client = AnthropicVertex(region=region, project_id=project_id)\n", "\n", "api_key = (\n", "    Path(os.environ[\"HOME\"], \".config/anthropic/api_key\")\n", "    .read_text(encoding=\"utf8\")\n", "    .strip()\n", ")\n", "\n", "direct_client = Anthropic(api_key=api_key)\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": \"how much is 1234 + 12765 ? (DO NOT USE TOOLS)\"},\n", "]\n", "\n", "tools = [\n", "    {\n", "        \"name\": \"print_sentiment_scores\",\n", "        \"description\": \"Prints the sentiment scores of a given tweet or piece of text.\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"positive_score\": {\n", "                    \"type\": \"number\",\n", "                    \"description\": \"The positive sentiment score, ranging from 0.0 to 1.0.\",\n", "                },\n", "                \"negative_score\": {\n", "                    \"type\": \"number\",\n", "                    \"description\": \"The negative sentiment score, ranging from 0.0 to 1.0.\",\n", "                },\n", "                \"neutral_score\": {\n", "                    \"type\": \"number\",\n", "                    \"description\": \"The neutral sentiment score, ranging from 0.0 to 1.0.\",\n", "                },\n", "            },\n", "            \"required\": [\"positive_score\", \"negative_score\", \"neutral_score\"],\n", "        },\n", "    },\n", "    {\n", "        \"name\": \"calculator\",\n", "        \"description\": \"Adds two number\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"num1\": {\"type\": \"number\", \"description\": \"first number to add\"},\n", "                \"num2\": {\"type\": \"number\", \"description\": \"second number to add\"},\n", "            },\n", "            \"required\": [\"num1\", \"num2\"],\n", "        },\n", "    },\n", "]\n", "\n", "client = direct_client\n", "\n", "message = client.messages.create(\n", "    model=model,\n", "    max_tokens=64,\n", "    messages=dialog,\n", "    tools=tools,\n", "    tool_choice={\"type\": \"none\"},\n", "    # tool_choice={\"type\": \"tool\", \"name\": \"print_sentiment_scores\"},\n", ")\n", "\n", "# for node in message.content:\n", "#     print(node)\n", "\n", "print(message)\n", "# response = message.content[0].text\n", "# print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "9d60eacc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}