###
write a python Spark script that finds all rows where col foo is < N
###
write a sql query to find all rows where column "foo" starts with "hello"
###
translate to pytnon:

#!/bin/bash
for i in {1..10}
do
echo $i
done
###
what happens if i do kill -signal -1 in shell
###
use tcpdump to log all HTTP packets from ******* to *******
###
if i have an object of a generic type in python, how can I check at runtime what its generic type is? I want to distinguish between `MyClass[Foo]` and `MyClass[Bar]`
###
write a CSS that uses two complementary colors that go well together for the titles and text colors
