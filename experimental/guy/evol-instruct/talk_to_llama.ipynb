{"cells": [{"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Usage(cache_creation_input_tokens=14, cache_read_input_tokens=2005, input_tokens=4, output_tokens=216)\n"]}], "source": ["import os\n", "\n", "from anthropic import Anthropic\n", "\n", "from pathlib import Path\n", "\n", "api_key = (\n", "    Path(os.environ[\"HOME\"], \".config/anthropic/api_key\")\n", "    .read_text(encoding=\"utf8\")\n", "    .strip()\n", ")\n", "\n", "client = Anthropic(api_key=api_key)\n", "\n", "model = \"claude-3-7-sonnet-20250219\"\n", "\n", "response = client.messages.create(\n", "    model=\"claude-3-5-sonnet-20240620\",\n", "    max_tokens=1024,\n", "    system=[\n", "        {\n", "            \"type\": \"text\",\n", "            \"text\": \"Here are some numbers.\",\n", "            \"cache_control\": {\"type\": \"ephemeral\"},\n", "        },\n", "        {\n", "            \"type\": \"text\",\n", "            \"text\": \" \".join(str(x) for x in range(1000)),\n", "            \"cache_control\": {\"type\": \"ephemeral\"},\n", "        },\n", "    ],\n", "    # messages=[{\"role\": \"user\", \"content\": \"What are the first 10 numbers you see.\"}],\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": \"What are the first 100 numbers you see.\",\n", "                    \"cache_control\": {\"type\": \"ephemeral\"},\n", "                }\n", "            ],\n", "        }\n", "    ],\n", ")\n", "print(response.usage)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Usage(cache_creation_input_tokens=0, cache_read_input_tokens=2235, input_tokens=18, output_tokens=216)\n", "[TextBlock(citations=None, text='The next 100 numbers I see in the list are:\\n\\n100 101 102 103 104 105 106 107 108 109 110 111 112 113 114 115 116 117 118 119 120 121 122 123 124 125 126 127 128 129 130 131 132 133 134 135 136 137 138 139 140 141 142 143 144 145 146 147 148 149 150 151 152 153 154 155 156 157 158 159 160 161 162 163 164 165 166 167 168 169 170 171 172 173 174 175 176 177 178 179 180 181 182 183 184 185 186 187 188 189 190 191 192 193 194 195 196 197 198 199', type='text')]\n"]}], "source": ["response2 = client.messages.create(\n", "    model=\"claude-3-5-sonnet-20240620\",\n", "    max_tokens=1024,\n", "    system=[\n", "        {\n", "            \"type\": \"text\",\n", "            \"text\": \"Here are some numbers.\",\n", "            \"cache_control\": {\"type\": \"ephemeral\"},\n", "        },\n", "        {\n", "            \"type\": \"text\",\n", "            \"text\": \" \".join(str(x) for x in range(1000)),\n", "            \"cache_control\": {\"type\": \"ephemeral\"},\n", "        },\n", "    ],\n", "    messages=[\n", "        # {\"role\": \"user\", \"content\": \"What are the first 100 numbers you see.\"},\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": \"What are the first 100 numbers you see.\",\n", "                    \"cache_control\": {\"type\": \"ephemeral\"},\n", "                }\n", "            ],\n", "        },\n", "        # {\"role\": \"assistant\", \"content\": response.content[0].text},\n", "        {\n", "            \"role\": \"assistant\",\n", "            \"content\": [\n", "                {\n", "                    \"type\": \"text\",\n", "                    \"text\": response.content[0].text,\n", "                    \"cache_control\": {\"type\": \"ephemeral\"},\n", "                }\n", "            ],\n", "        },\n", "        {\"role\": \"user\", \"content\": \"What are the next 100 numbers you see.\"},\n", "    ],\n", ")\n", "print(response2.usage)\n", "print(response2.content)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[TextBlock(citations=None, text='The first 10 numbers I see in the list are:\\n\\n0 1 2 3 4 5 6 7 8 9', type='text')]\n"]}], "source": ["print(response.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from anthropic import Anthropic\n", "\n", "from pathlib import Path\n", "\n", "api_key = (\n", "    Path(os.environ[\"HOME\"], \".config/anthropic/api_key\")\n", "    .read_text(encoding=\"utf8\")\n", "    .strip()\n", ")\n", "\n", "client = Anthropic(api_key=api_key)\n", "\n", "model = \"claude-3-5-sonnet-20241022\"\n", "\n", "system = \"\"\"\\\n", "You are Augment, an AI code assistant developed by Augment Code that lives in a company's Slack workspace and has access to their GitHub repos.\n", "Your role is to help the company by following their instructions and answering their Slack questions and conversations related to their code, their product, and general software engineering.\n", "\n", "When answering the developers' questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information. Remember, you're in a Slack workspace which usually has brief and succint messages.\n", "- Always write code in the programming language of the files the user is asking about. For example, if the user asks about the file foo/bar.rs, use Rust unless explicitly asked to use a different language.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- When referencing classes, functions, variables, files, or directories in your response, always wrap them in backticks (``).\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "- Note that users may refer to each other by name or Slack-formatted user ID tags, the latter of which is provided for every message. When you respond, prefer to use Slack-formatted User ID tags if you need to refer to a user, e.g. <@U12345>.\n", "- When responding, remember that Slack uses its own Slack \"mrkdwn\" format and it differs significantly from normal markdown. So, when responding, use the following mrkdwn formatting:\n", "  - *bold* text NOT **bold** text\n", "  - _italics_ text NOT *italics* text\n", "  - ~strikethrough~ text NOT ~~strikethrough~~ text\n", "  - > quoted text\n", "\n", "You will receive code excerpts retrieved from the user's GitHub repos using an embeddings-based search that matches the conversation against an index of all repo files.\n", "The excerpts will be from the following repos. Note that since indexing takes time, the latest commit time may temporarily be ahead of the last indexed time.\n", "It's also possible that a newly added repo hasn't finished being indexed yet, in which case its last indexed time will be empty:\n", "repo name: augment\n", "repo owner: augmentcode\n", "repo last indexed time: 09:12:13 PST 2024/11/05\n", "repo last indexed commit sha: 4c56e0fb0fd36d29aec01c5aefae944cfc8463a9\n", "repo latest commit time: 09:12:13 PST 2024/11/05\n", "repo latest commit sha: 4c56e0fb0fd36d29aec01c5aefae944cfc8463a9\n", "\n", "You will then receive a conversation in which you have been mentioned. Please reply with your response.\n", "Your User ID is U070YG7J4V8. You are in the channel productux. The current time is 13:59:41 PST 2024/11/05.\n", "\"\"\"\n", "\n", "msg1 = '''\\\n", "Below are some relevant files from my project.\n", "\n", "Here is an excerpt from the file `research/infra/svc/augi-console/README.md`:\n", "\n", "```\n", "# <PERSON><PERSON> Console\n", "\n", "The **augi-console** is a simple host with the `augi` CLI available. Users are\n", "automatically provisioned when SSH Authorized Keys are available. Keys can be\n", "managed at https://userauth.r.augmentcode.com; only a Google Workspace\n", "@augmentcode.com account is required.\n", "\n", "WARNING: This host is ephemeral, home directories are not persistent.\n", "\n", "See Also:\n", "\n", " - [🚀 Augment Research Infra - Quick Start](https://www.notion.so/Augment-Research-Infra-Quick-Start-f43f2c6062d84eb1a5e746c072a7b2f8)\n", " - [🛠️ augi CLI / <PERSON><PERSON>](https://www.notion.so/Research-augi-CLI-2edc188bd4ff490bb0c9e1e26168e6ad?pvs=4#f5861cb60111454999621a72ef100069)\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/airbnb/epoxy/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY ./run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Support programming language: Java\n", "RUN apt -y install openjdk-11-jdk\n", "\n", "# Install Android SDK\n", "RUN apt -y install android-sdk\n", "RUN apt -y install sdkmanager\n", "ENV ANDROID_HOME /usr/lib/android-sdk\n", "RUN yes | sdkmanager --licenses\n", "\n", "# Download and patch git repo\n", "COPY ./epoxy.diff /data/epoxy.diff\n", "ARG GIT_SHA=9a43af4d043e71f29afe4cbeadf8c008740a5184\n", "RUN git clone https://github.com/airbnb/epoxy.git\n", "RUN cp -rT /code/epoxy /code\n", "RUN rm -fr /code/epoxy/\n", "RUN git checkout ${GIT_SHA}\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/collection/reddit/README.md`:\n", "\n", "```\n", "Reddit Posts and Comments\n", "========================\n", "\n", "Up-to-date Reddit dumps can be downloaded from the Arctic Shift project.\n", "\n", "Most recent versions are found on the [Releases page](https://github.com/ArthurHeitmann/arctic_shift/releases).\n", "\n", "Note that please use the direct download option instead of P2P.\n", "\n", "\n", "\n", "Downloading\n", "------------\n", "\n", "As of the last update, direct download is found at [filen](https://drive.filen.io/f/fb67389b-2eb2-42e8-9d2f-474ca153e105#cgZ5eW2NWXuS9n9rVhdnTkPDmZAeuOhk).\n", "\n", "This contains all recent dumps, so please only download one of them.\n", "\n", "Please make sure you also download associated schema JSON definition from the [repo](https://github.com/ArthurHeitmann/arctic_shift/tree/master/schemas) for\n", "the particular date dump.  Every dump may have a slightly different schema!\n", "\n", "\n", "Decompression\n", "--------------\n", "\n", "The files are in a special `zst_blocks` format.  This can be decompressed using the cli tools from Arctic Shift,\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/vaticle/typedb/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY ./run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Support programming language: Java\n", "RUN apt -y install openjdk-8-jdk\n", "\n", "# Download and patch git repo\n", "COPY ./typedb.diff /data/typedb.diff\n", "ARG GIT_SHA=22b3a8fb744322d6b7f00dc8c5ef8e748b828987\n", "RUN git clone https://github.com/vaticle/typedb.git\n", "RUN cp -rT /code/typedb /code\n", "RUN rm -fr /code/typedb/\n", "RUN git checkout ${GIT_SHA}\n", "RUN git apply /data/typedb.diff\n", "\n", "# Install build system: Bazel\n", "RUN wget https://github.com/bazelbuild/bazelisk/releases/download/v1.15.0/bazelisk-linux-amd64\n", "RUN chmod u+x bazelisk-linux-amd64\n", "RUN mv bazelisk-linux-amd64 /bin/bazel\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/gpt-neox/README.md`:\n", "\n", "```\n", "...\n", "This repository records [EleutherAI](https://www.eleuther.ai)'s work-in-progress for training large-scale language models on GPUs. Our current framework is based on NVIDIA's [Megatron Language Model](https://github.com/NVIDIA/Megatron-LM) and has been augmented with techniques from [DeepSpeed](https://www.deepspeed.ai) as well as some novel optimizations.\n", "\n", "We aim to make this repo a centralized and accessible place to gather techniques for training large-scale autoregressive language models, and accelerate research into large-scale training. Additionally, we hope to train and open source a 175B parameter GPT-3 replication along the way. Please note, however, that this is a research codebase that is primarily designed for performance over ease of use. We endeavour to make it as easy to use as is feasible, but if there's anything in the readme that is unclear or you think you've found a bug, please open an issue.\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/infra/cfg/vendor/github.com/jsonnet-libs/xtd/docs/README.md`:\n", "\n", "```\n", "---\n", "permalink: /\n", "---\n", "\n", "# xtd\n", "\n", "```j<PERSON>net\n", "local xtd = import \"github.com/jsonnet-libs/xtd/main.libsonnet\"\n", "```\n", "\n", "`xtd` aims to collect useful functions not included in the Jsonnet standard library (`std`).\n", "\n", "This package serves as a test field for functions intended to be contributed to `std`\n", "in the future, but also provides a place for less general, yet useful utilities.\n", "\n", "\n", "* [aggregate](aggregate.md)\n", "* [array](array.md)\n", "* [ascii](ascii.md)\n", "* [camelcase](camelcase.md)\n", "* [date](date.md)\n", "* [inspect](inspect.md)\n", "* [jsonpath](jsonpath.md)\n", "* [number](number.md)\n", "* [string](string.md)\n", "* [url](url.md)...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/moment/luxon/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential vim curl\n", "# Upgrade node to v19\n", "RUN apt remove -y libnode-dev\n", "RUN curl -fsSL https://deb.nodesource.com/setup_19.x | bash - && apt-get install -y nodejs\n", "RUN apt autoremove\n", "RUN ln -s /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "COPY luxon.diff .hydra/luxon.diff\n", "COPY lang .hydra/lang\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply any patches\n", "ARG GIT_SHA=9575754a\n", "RUN git clone https://github.com/moment/luxon.git\n", "RUN cp -rT /code/luxon /code\n", "RUN rm -fr /code/luxon/\n", "RUN git checkout ${GIT_SHA}\n", "RUN git apply .hydra/luxon.diff\n", "\n", "RUN npm install\n", "\n", "# Final repo specific commands\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `deploy/quickstart/README.md`:\n", "\n", "```\n", "# Augment developer quickstart\n", "\n", "## Launching a new host in the cloud for development work.\n", "\n", "### TL;DR\n", "- complete your first-day items\n", "  - AWS login, including creation of an access key and secret\n", "  - Coreweave login, including installing the .kubeconfig\n", "  - GitHub account setup\n", "  - Augment email setup\n", "- clone the code to your laptop\n", "  - [Fork the augment repository](https://github.com/augmentcode/augment/fork) into your github account\n", "- Satisfy the local configuration requirements (qs_setup.sh)\n", "- launch a cloud host in AWS or Coreweave for your development work\n", "- success!\n", "\n", "## Local configuration requirements before you start\n", "\n", "All of these items can be configured by running the qs_setup.sh script.\n", "*You will be prompted for your AWS credentials, and possibly your local password for sudo.*\n", "\n", "    EMAIL_NAME=<username> ./qs_setup.sh\n", "\n", "- `~/.augment/user.json`\n", "\n", "    used to tell the system who you are; this is generally the \"username\" portion of your @augmentcode.com email address.\n", "\n", "- AWS CLI\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/mrdoob/three.js/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential vim curl\n", "# Upgrade node to v19\n", "RUN apt remove -y libnode-dev\n", "RUN curl -fsSL https://deb.nodesource.com/setup_19.x | bash - && apt-get install -y nodejs\n", "RUN apt autoremove\n", "RUN ln -s /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply any patches\n", "ARG GIT_SHA=a645ee1\n", "RUN git clone https://github.com/mrdoob/three.js.git\n", "RUN cp -rT /code/three.js /code\n", "RUN rm -fr /code/three.js/\n", "RUN git checkout ${GIT_SHA}\n", "\n", "RUN npm install\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `tools/feature_flags/Readme.md`:\n", "\n", "```\n", "# Feature Flags Tool\n", "\n", "This tool handles configuration of Feature Flags (Infrastructure as Code)\n", "\n", "Changes to feature flag configurations are synchronized with a feature flag service provider (currently LaunchDarkly)\n", "\n", "## Usage - How to change flags\n", "\n", "### Edit the flags\n", "Update the file `flags.yaml` with the desired changes\n", "\n", "#### `flags.yml` format example\n", "```yaml\n", "  flag_key_1:\n", "    # The unique identifier for the key\n", "    sync: true\n", "    # Only flags with sync true will be synced to LaunchDarkly, the others are purely informational (for validation or documentation purposes)\n", "    description: \"Flag description\"\n", "    default_return_value: \"my_default_value\"  # default value returned when no rules match\n", "    envs:\n", "      env_key:  # production/test\n", "        on: true  # If the env is off, rules will not be evaluated for it and the default value will always be returned\n", "        archived: false  # Archiving the key for the env is equivalent to deleting it\n", "        rules:\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/ethereum/go-ethereum/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply patch\n", "ARG GIT_SHA=67979022aa8ea6a96b618c086f60b49af7d2a568\n", "RUN git clone https://github.com/ethereum/go-ethereum.git\n", "RUN cp -rT /code/go-ethereum /code\n", "RUN rm -fr /code/go-ethereum/\n", "RUN git checkout ${GIT_SHA}\n", "\n", "# Install essential build tools and Go\n", "RUN apt-get update\n", "RUN apt-get install -y --no-install-recommends ca-certificates\n", "RUN rm -rf /var/lib/apt/lists/*\n", "RUN curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -\n", "\n", "# Set Go environment variables\n", "ENV PATH=\"/usr/local/go/bin:${PATH}\"\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/api_proxy/client/Readme.md`:\n", "\n", "```\n", "# Augment API Client\n", "\n", "Python client for the Augment API.\n", "\n", "Includes a Python client and a CLI utility for manual testing against a deployed server\n", "\n", "## Authentication Prerequirements\n", "\n", "set your token at `~/.config/augment/token`\n", "\n", "## Sample usage of CLI util\n", "\n", "#### Code Edit\n", "```sh\n", "bazel run services/api_proxy/client/util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com edit --model droid-33B-FP8-r1-edit --selected-text 'def print_hello():\\\\n    print(\\\\\"Hello, <PERSON>!\\\\\")\\\\n' --instruction \"Convert to Rust\"\n", "```\n", "\n", "#### Resolve Edit\n", "```sh\n", "bazel run services/api_proxy/client/util -- --endpoint https://dev-$USER.us-central.api.augmentcode.com resolve-edit --request-id  b8568259-5e49-4eb7-9e1f-9dae2222f91a --emit-time \"2024-01-01 15:00:00\" --resolve-time \"2024-01-01 15:00:01\" --is-accepted true\n", "```\n", "\n", "#### Checkpoint Blobs\n", "```sh\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/caddyserver/caddy/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply patch\n", "ARG GIT_SHA=7103ea096fffd851a5efd67fd6d30cf65bc38873\n", "RUN git clone https://github.com/caddyserver/caddy.git\n", "RUN cp -rT /code/caddy /code\n", "RUN rm -fr /code/caddy/\n", "RUN git checkout ${GIT_SHA}\n", "\n", "# Install essential build tools and Go\n", "RUN apt-get update\n", "RUN apt-get install -y --no-install-recommends ca-certificates\n", "RUN rm -rf /var/lib/apt/lists/*\n", "RUN curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -\n", "\n", "# Set Go environment variables\n", "ENV PATH=\"/usr/local/go/bin:${PATH}\"\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `base/static_analysis/parsing_test.py`:\n", "\n", "```\n", "...\n", "TYPESCRIPT_BASIC_EXAMPLE = \"\"\"\\\n", "import * as package from './package';\n", "import { imported_f } from './another/package';\n", "\n", "interface ICoordinate {\n", "    x: number;\n", "    y: number;\n", "}\n", "\n", "class Main {\n", "    static globVar: number = imported_f() + 1;\n", "\n", "    /**\n", "     * This is a docstring. */\n", "    static glob_f(x: number): number {\n", "        // this is just a comment\n", "        return x + Main.globVar;\n", "    }\n", "}\n", "\n", "abstract class A {\n", "    abstract method(z: number): number;\n", "}\n", "\n", "export class DataClass extends A implements ICoordinate {\n", "    x: number; // x is a field\n", "    private y: number;\n", "\n", "    constructor(x: number, y: number) {\n", "        super();\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/Readme.md`:\n", "\n", "```\n", "# Services\n", "\n", "This directory contains service (REST API/grpc API) of the Application.\n", "\n", "## Services Types\n", "\n", "There are multiple types of services:\n", "\n", "- Shard Services: Most services run within a shard namespace. It will communicate with other shard services using MTLS with a root of trust valid and available only with in the shard. It might call RPC on central and\n", "global services and use cloud resources.\n", "- Central Services: Central services are shared between all shards in a cloud. An examples are services that require a H100 GPU (embedder, inference). The communication is using\n", "an MTLS with a root of trust that is valid for all shards and to central.\n", "- Global Services: Global services are like central services, but the only exist in the lead cloud (e.g. US_CENTRAL1_PROD). The communication is using MTLS with a root of trust that is valid for all shards and to central. The global services using a internal load balancer that is contained in the VPC, but exposed to\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/jaegertracing/jaeger/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply patch\n", "ARG GIT_SHA=3021d314f71b9ce0707e8f5a443dd49b6a73d7da\n", "RUN git clone https://github.com/jaegertracing/jaeger.git\n", "RUN cp -rT /code/jaeger /code\n", "RUN rm -fr /code/jaeger/\n", "RUN git checkout ${GIT_SHA}\n", "\n", "# Install essential build tools and Go\n", "RUN apt-get update\n", "RUN apt-get install -y --no-install-recommends ca-certificates\n", "RUN rm -rf /var/lib/apt/lists/*\n", "RUN curl -L https://golang.org/dl/go1.20.linux-amd64.tar.gz | tar -C /usr/local -xzf -\n", "\n", "# Set Go environment variables\n", "ENV PATH=\"/usr/local/go/bin:${PATH}\"\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/resilience4j/resilience4j/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade &&  apt-get update\n", "RUN apt -y install sudo wget git gcc g++ build-essential vim curl\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY ./run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Download and patch git repo\n", "#COPY ./<repo_name>.patch /data/<repo_name>.patch\n", "ARG GIT_SHA=56b2eddbc6a22a67e844d1451e6bad9fefdbec68\n", "RUN git clone https://github.com/resilience4j/resilience4j.git\n", "RUN cp -rT /code/resilience4j /code\n", "RUN rm -fr /code/resilience4j/\n", "RUN git checkout ${GIT_SHA}\n", "\n", "# These are flakey\n", "RUN rm resilience4j-bulkhead/src/test/java/io/github/resilience4j/bulkhead/*Test.java\n", "RUN rm resilience4j-bulkhead/src/test/java/io/github/resilience4j/event/*Test.java\n", "RUN rm resilience4j-bulkhead/src/test/java/io/github/resilience4j/internal/*Test.java\n", "RUN rm resilience4j-micrometer/src/test/java/io/github/resilience4j/micrometer/*Test.java\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/vscode/README.md`:\n", "\n", "```\n", "# Augment Code for Visual Studio Code\n", "\n", "[Augment Code](https://augmentcode.com) is the AI-powered coding platform that understands your entire codebase. Augment analyzes your entire project, providing insights and suggestions that truly fit the way your team works.\n", "\n", "[![Augment Code](https://augmentcode.com/readme-hero.png)](https://www.augmentcode.com)\n", "\n", "### Get up to speed\n", "\n", "Find out how a system works, investigate a bug, or learn to use a new API. Whether you’ve been in a codebase from the first commit or are onboarding to a new team, you’ll be able to get to coding faster than ever.\n", "\n", "### Stay in the flow\n", "\n", "Keeping everything in your head–function definitions, API payloads, database schemas–is almost impossible in large, complex systems. You’ll have everything you need at your fingertips as you work.\n", "\n", "### Everything you need to get more done\n", "\n", "1. Intelligent chat\n", "    1. Get instant answers that would typically require you to search documentation, interrupt teammates, or open a ticket.\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/infra/README.md`:\n", "\n", "```\n", "# Augment Research Infra\n", "\n", "This directory contains a number of libraries and a single CLI -- `augi` -- for\n", "Augment research infrastructure operations.\n", "\n", "## Bootstrapping\n", "\n", "For most users, `augi` will already be installed as `/usr/local/bin/augi`. If not, to bootstrap:\n", "\n", "```\n", "$ go run augi.go install\n", "```\n", "\n", "if go itself is not already on your system, and you are using Ubuntu:\n", "\n", "```\n", "$ sudo snap install go --classic\n", "```\n", "\n", "## Quick Start\n", "\n", " - **Bootstrap:** `go run augi.go install`\n", " - **Use:** `augi ...`\n", " - **Use from source:** `augi build -- ...`\n", " - **Reinstall from source:** `augi build -- install`\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `tools/genie/backend/BUILD`:\n", "\n", "```\n", "load(\"//tools/bzl:rust.bzl\", \"rust_binary\", \"rust_oci_image\", \"rust_test\")\n", "load(\"@crates//:defs.bzl\", \"aliases\", \"all_crate_deps\")\n", "load(\"@rules_rust//cargo:defs.bzl\", \"cargo_build_script\")\n", "load(\"//tools/bzl:kubecfg.bzl\", \"kubecfg\")\n", "\n", "proto_library(\n", "    name = \"genie_store_proto\",\n", "    srcs = [\"genie_store.proto\"],\n", "    visibility = [\"//tools:__subpackages__\"],\n", "    deps = [\n", "        \"//base/proto/services:access_proto\",\n", "        \"@protobuf//:timestamp_proto\",\n", "    ],\n", ")\n", "\n", "rust_binary(\n", "    name = \"server\",\n", "    srcs = glob([\"src/**/*.rs\"]),\n", "    aliases = aliases(),\n", "    data = [\"//tools/genie/frontend\"],\n", "    edition = \"2021\",\n", "    proc_macro_deps = all_crate_deps(\n", "        proc_macro = True,\n", "    ),\n", "    deps = all_crate_deps(\n", "        normal = True,\n", "    ) + [\n", "        \":proto_gen\",\n", "        \"//base/cloud/iap:iap_rs\",\n", "        \"//base/logging:struct_logging_rs\",\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/dxy/edits/reverse_get_instruction.py`:\n", "\n", "```\n", "\"\"\"Helper function to reversely generate the instructions.\"\"\"\n", "from termcolor import colored\n", "\n", "import experimental.dxy.edits.api_lib as api_lib\n", "import experimental.dxy.edits.data_type as data_type\n", "import research.core.utils_for_str as str_lib\n", "\n", "system_prompt = r\"\"\"You are helpful and super smart programing expert that can solve any any coding-related problems reasonable and correctly.\"\"\"\n", "\n", "typical_instructions = [\n", "    \"put a nice docstring to well explain this class\",\n", "    \"use promise chain instead of async\",\n", "    \"extend it to else condition\",\n", "    \"fix errors\",\n", "    \"move the accesstoken to url as a parameter of token=\",\n", "    \"make this line more readable\",\n", "    \"generate floats, not only ints\",\n", "    \"add a param to choose upper/lower\",\n", "    \"change all command names to snake case\",\n", "    \"replace True by False\",\n", "    \"this function seems will never trigger the default branch of executeChat, fix it\",\n", "    \"refactor this function by using optional_map func\",\n", "    \"simplify the cache control\",\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/seata/seata/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY ./run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Support programming language: Java\n", "RUN apt -y install openjdk-11-jdk\n", "\n", "# Download and patch git repo\n", "COPY ./seata.diff /data/seata.diff\n", "ARG GIT_SHA=5b920e8eeab164c0e35625ad6ab7cd4a22c2c04b\n", "RUN git clone https://github.com/seata/seata.git\n", "RUN cp -rT /code/seata /code\n", "RUN rm -fr /code/seata/\n", "RUN git checkout ${GIT_SHA}\n", "RUN git apply /data/seata.diff\n", "\n", "# Install build system: Maven\n", "ARG MAVEN_VERSION=3.9.4\n", "ARG BASE_URL=https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries\n", "RUN mkdir -p /usr/share/maven /usr/share/maven/ref \\\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `deploy/common/eng.jsonnet`:\n", "\n", "```\n", "// list of all engineers\n", "//\n", "// Please add yourself to this list in alphabetical order to get access to services\n", "// username: Use your primary augmentcode.com email address prefix\n", "// gcp_access: 'full' if GCP dev VM access is required, null otherwise\n", "// piiAccess: If 'full', the user is able to view raw PII in the prod BigQuery analytics dataset. If\n", "//            'masked', the user will see a hash instead of the real value. Users with 'full' should\n", "//            also have a business justification in a comment.\n", "[\n", "  {\n", "    fullname: '<PERSON><PERSON><PERSON>',\n", "    username: 'a<PERSON><PERSON>',\n", "    gcp_access: null,\n", "    github: null,\n", "    // Full PII access needed for managing customer relations.\n", "    piiAccess: 'full',\n", "  },\n", "  {\n", "    fullname: '<PERSON><PERSON>',\n", "    username: 'alyah',\n", "    gcp_access: null,\n", "    github: null,\n", "    piiAccess: 'masked',\n", "  },\n", "  {\n", "    fullname: '<PERSON>',\n", "    username: 'andre',\n", "    gcp_access: 'full',\n", "    github: 'a2chang',\n", "    // Full PII access needed for managing customer relations.\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/google/guava/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim emacs\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY ./run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Support programming language: Java\n", "RUN apt -y install openjdk-8-jdk\n", "\n", "# Install git repo and apply patch\n", "COPY ./guava.diff /data/guava.diff\n", "ARG GIT_SHA=068e94d5dc81b6c4eac9941c474295294324a520\n", "RUN git clone https://github.com/google/guava.git guava_repo\n", "RUN cp -rT /code/guava_repo/ /code\n", "RUN rm -fr /code/guava_repo\n", "RUN git checkout ${GIT_SHA}\n", "RUN git apply /data/guava.diff\n", "\n", "# Install build system: Maven\n", "ARG MAVEN_VERSION=3.9.4\n", "ARG BASE_URL=https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries\n", "RUN mkdir -p /usr/share/maven /usr/share/maven/ref \\\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `experimental/guy/dotfiles/.bashrc`:\n", "\n", "```\n", "...\n", "alias clone=\"<NAME_EMAIL>:augmentcode/augment.git\"\n", "\n", "pod_create() { python3 $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py $*; }\n", "pod_connect() { kubectl --context coreweave --namespace tenant-augment-eng exec --stdin --tty $* -- sudo su - augment; }\n", "pod_connect_ssh() { ssh -p22022 augment@$1.tenant-augment-eng.coreweave.cloud; }\n", "pod_destroy() { $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py --cluster CW destroy $1; }\n", "pod_show() { kubectl get pods | egrep ^guy-; }\n", "pod_show_full() { kubectl get pods -o wide | egrep ^guy-; }\n", "pod_ls() { kubectl get pods | egrep ^guy-; }\n", "\n", "# Setup a new node\n", "setup_node() { cd ~; <NAME_EMAIL>:augmentcode/augment.git; cd augment/research; ./research-init.sh; }\n", "\n", "fix_ssh() { eval $(ssh-agent); ssh-add ~/.ssh/augment_rsa; }\n", "\n", "alias gs=\"gt submit --cli\"\n", "\n", "alias pytest=\"pytest -s --disable-warnings\"\n", "\n", "alias fixit=\"python3 $HOME/augment/experimental/guy/format_staged_files.py --add\"\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `tools/docker/dockerfiles/ubuntu_ci.Dockerfile`:\n", "\n", "```\n", "FROM ubuntu:22.04\n", "ENV DEBIAN_FRONTEND=noninteractive\n", "# add sudo and git\n", "# git is needed for bazel to fetch dependencies\n", "# sudo because needed for wipe\n", "# tzdata is needed by bigquery-emulator\n", "RUN apt-get update -y && \\\n", "    apt-get install -y sudo git ca-certificates tzdata && \\\n", "    apt-get upgrade -y && \\\n", "    apt-get clean && \\\n", "    rm -rf /var/lib/apt/lists/*\n", "# we do need an user\n", "RUN useradd --create-home --uid 1000 --shell /bin/bash augment && \\\n", "  usermod -aG sudo augment && \\\n", "  echo \"augment ALL=(ALL) NOPASSWD: ALL\" >> /etc/sudoers\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/gephi/gephi/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential curl vim\n", "RUN apt-get -y install python3.9\n", "RUN cp /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY ./run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Support programming language: Java\n", "RUN apt -y install openjdk-11-jdk\n", "\n", "# Install git repo and apply patch\n", "COPY ./gephi.diff /data/gephi.diff\n", "ARG GIT_SHA=a42603fd0cb2c209f96c79481dcc1f7195058822\n", "RUN git clone https://github.com/gephi/gephi.git\n", "RUN cp -rT /code/gephi /code\n", "RUN rm -fr /code/gephi/\n", "RUN git checkout ${GIT_SHA}\n", "RUN git apply /data/gephi.diff\n", "\n", "# Support build system: Maven\n", "ARG MAVEN_VERSION=3.9.4\n", "ARG BASE_URL=https://apache.osuosl.org/maven/maven-3/${MAVEN_VERSION}/binaries\n", "RUN mkdir -p /usr/share/maven /usr/share/maven/ref \\\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `tools/monitoring/Readme.md`:\n", "\n", "```\n", "...\n", "[Grafana](https://grafana.com/) is an observability platform that combines different observability data sources into a single UI. It support adhoc metrics, dashboards and alerting.\n", "\n", "The Grafana instance is available at [https://grafana.eng.augmentcode.com/].\n", "The instance is configured with the following data sources:\n", "\n", "- [Prometheus (metrics)](https://grafana.eng.augmentcode.com/explore?orgId=1&left=%7B%22datasource%22:%22PBFA97CFB590B2093%22,%22queries%22:%5B%7B%22refId%22:%22A%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D)\n", "\n", "The metrics are queried with a query language called [PromQL](https://prometheus.io/docs/prometheus/latest/querying/basics/).\n", "\n", "## Tracing / Cloud Trace / OpenTelemetry Collector\n", "\n", "Our original tracing system is Jaeger. We are evaluating and likely migrating to Google Cloud Trace.\n", "\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/google/mobly/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential vim curl pip\n", "RUN ln -s /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply any patches\n", "ARG OWNER=google\n", "ARG REPO=mobly\n", "ARG GIT_SHA=6392f83\n", "RUN git clone https://github.com/${OWNER}/${REPO}.git code-tmp\n", "RUN cp -rT /code/code-tmp /code\n", "RUN rm -fr /code/code-tmp\n", "RUN git checkout ${GIT_SHA}\n", "\n", "RUN pip install pytest\n", "RUN python setup.py install\n", "RUN pip install pytz\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/eval/repos/pallets/flask/v1/Dockerfile`:\n", "\n", "```\n", "FROM amd64/ubuntu\n", "\n", "# General installs\n", "RUN apt -y update && apt -y upgrade\n", "RUN apt -y install sudo wget git gcc g++ build-essential vim curl pip\n", "RUN ln -s /usr/bin/python3 /usr/bin/python\n", "WORKDIR /code\n", "\n", "# Copy in run script\n", "RUN mkdir .hydra\n", "COPY run.sh .hydra/run.sh\n", "RUN chmod ugo+rwx .hydra/run.sh\n", "\n", "# Install git repo and apply any patches\n", "ARG OWNER=pallets\n", "ARG REPO=flask\n", "ARG GIT_SHA=90967cc\n", "RUN git clone https://github.com/${OWNER}/${REPO}.git code-tmp\n", "RUN cp -rT /code/code-tmp /code\n", "RUN rm -fr /code/code-tmp\n", "RUN git checkout ${GIT_SHA}\n", "\n", "RUN pip install pytest\n", "RUN pip install -e .\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `research/data/collection/github/README.md`:\n", "\n", "```\n", "Github Processing Pipeline\n", "======\n", "Several pipelines for discovery and process of github repos, files and commits\n", "\n", "Each task is a separate kubernetes deployement that can be separately scaled or shutdown.\n", "\n", "Task scheduling, error handling and logs are all through kubernetes.\n", "\n", "Tasks\n", "------\n", "* `gh-download-process`\n", "Clone Github repos, store them and analyze and find unique new files and commits.\n", "New files and commits are appended into JSON files and whole repos are stored as .tar.gz files.\n", "These are all stored in S3.\n", "\n", "* `gh-archive-scan`\n", "Scan GH archive stream for new repos\n", "\n", "\n", "Structure\n", "------\n", "```bash\n", "github\n", "├── README.md                   This file\n", "├── controllers                 Python package for business logic\n", "│   ├── __init__.py\n", "│   ├── batch_job.py            Batch job utils\n", "│   ├── languages.py            Suffix-language mapping\n", "│   ├── git_clone_worker.py     Download and process repos\n", "│   └── process_gh_archive.py   Scan GH archive\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `clients/intellij/README.md`:\n", "\n", "```\n", "# Augment Code for JetBrains IDEs\n", "\n", "<!-- Plugin description -->\n", "\n", "[Augment Code](https://augmentcode.com) is the AI-powered coding platform that understands your entire codebase. Augment analyzes your entire project, providing insights and suggestions that truly fit the way your team works.\n", "\n", "[![Augment Code](https://augmentcode.com/readme-hero.png)](https://www.augmentcode.com)\n", "\n", "### Get up to speed\n", "\n", "Find out how a system works, investigate a bug, or learn to use a new API. Whether you’ve been in a codebase from the first commit or are onboarding to a new team, you’ll be able to get to coding faster than ever.\n", "\n", "### Stay in the flow\n", "\n", "Keeping everything in your head–function definitions, API payloads, database schemas–is almost impossible in large, complex systems. You’ll have everything you need at your fingertips as you work.\n", "\n", "### Everything you need to get more done\n", "\n", "1. Intelligent chat\n", "   1. Get instant answers that would typically require you to search documentation, interrupt teammates, or open a ticket.\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `services/auth/central/Readme.md`:\n", "\n", "```\n", "# Auth Service\n", "\n", "The Auth service provides an endpoint for authorizing users to access Augment\n", "according to RFC 6749 (https://datatracker.ietf.org/doc/html/rfc6749).\n", "\n", "Since the auth service is global - i.e., it is not isolated to a specific tenant - it\n", "does not store or access any tenant-specific information like authorization codes or\n", "access tokens. The service exposes an authorize endpoint that authenticates the user\n", "(e.g., with IAP), determines the appropriate tenant depending on the user identity,\n", "and then redirects the user to the isolated auth service for the appropriate tenant,\n", "Auth-Tenant.\n", "...\n", "```\n", "\n", "'''\n", "\n", "response1 = \"\"\"\\\n", "Understood. I'll refer to the excerpts for context, and ignore them for general questions.\n", "\"\"\"\n", "\n", "\n", "question = \"\"\"\\\n", "Here is the previous slack conversation:\n", "\n", "U0767CLGZV1: Throwing this topic over here since I don't want to derail the substantive thread that's happening already (plus is more general anyway).\n", "\n", "When we add an `.augmentrules` rules file should we consider consolidating the mechanism for that and the .augmentignore file? Which then gives us a platform to add more files for control?\n", "\n", "Something like\n", "```.augment/\n", "  |- rules\n", "  |- ignore```\n", "Quoted text:\n", ":speech_balloon: User-specified chat rules in action.\n", "\n", "This is a good time to share the plan, such as it is, and get feedback. I’m implementing support for two kinds of rules:\n", "• *Workspace rules:* If the workspace has `.augmentrules` file at the root, those are read and added to the system prompt (with a little header explaining what it is). This is meant to be a place for the team to specify the rules they want, and check them in to the repo so they apply to the whole team.\n", "• *User rules:* The user can have a string setting `augment.advanced.chat.rules`. If specified, that string also gets added to the system prompt (again with a little header). This is a place for the user to specify their personal rules.\n", "The model will know the difference between these (and where each set of rules comes from), so we can use the same terminology in our docs and they will match what the model says (like you see in this example).\n", "\n", "Comments/feedback welcome.\n", "U042RCQ3N7R: I would really wish for a more meaningful discussion of the alternatives and tradeoffs about the proposal?\n", "1. why user prompts via a vscode-only approach? how what that work with n-clients or slack bot (or other future features)\n", "2. what is the tradeoff between a file based approach and using the Setting service?\n", "3. is it good to use a very general term like \"rules\" for something that is only used by (apparently) IDE Chat? \n", "U04FW08SH0T: Good questions — I have a better handle on the scope now, and there was some initial discussion on slack, so I’ll write up a doc and we can discuss it there or in a meeting if necessary.\n", "U04FW08SH0T: To be clear, everything about the proposal is subject to change based on these discussions.\n", "U04FW08SH0T: &gt; 1. what is the tradeoff between a file based approach and using the Setting service?\n", "What’s the Setting service?\n", "U066CDURP1A: <https://augment-wic8570.slack.com/archives/C06K4P4UL92/p1730840670888179?thread_ts=1730838256.439359&amp;cid=C06K4P4UL92>\n", "\n", "Can see that it could be useful to have rules work for setting workspace context too:\n", "Quoted text:\n", "Adding ADRs and related documentation is pretty clever.  A couple of things:\n", "1. Is it possible through VSCode workspace settings to automatically add documents and repositories to the context of an active repo when the repo is opened?\n", "    a. I could also see it being useful to have the ability to load a style guide for all repos in our org that acts as a prompt to guide Augment completions.\n", "2. Is it possible now, or are you planning on adding the ability to provide remote context from sources like Github, Jira and Confluence?\n", "U066CDURP1A: <https://augment-wic8570.slack.com/archives/C07KTCP6UFN/p1730842771860519>\n", "Quoted text:\n", "I think this is a feature request, but having a little user input for the general instructions for the prompt would be super nice. Like just a floating text box where I can put in some extra instructions. For the code completion, is it possible to add to that prompt at all or no?\n", "\n", "\n", "Here is the user question for you:\n", "\n", "U04FW08SH0T: <@U070YG7J4V8> what is ADR?\n", "\"\"\"\n", "\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": msg1},\n", "    {\"role\": \"assistant\", \"content\": response1},\n", "    {\"role\": \"user\", \"content\": question},\n", "]\n", "\n", "message = client.messages.create(\n", "    model=model,\n", "    max_tokens=256,\n", "    system=system,\n", "    messages=dialog,\n", ")\n", "response = message.content[0].text\n", "print(response)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["To calculate the sum of 1234 and 3218764289476, we can use the \"calculator\" function that's available to us. Let's use this tool to perform the addition.\n"]}], "source": ["# Prerequisites:\n", "# > pip3 install --upgrade --user google-cloud-aiplatform\n", "# > gcloud auth application-default login\n", "# > pip3 install -U 'anthropic[vertex]'\n", "\n", "from anthropic import AnthropicVertex\n", "\n", "region = \"us-east5\"\n", "project_id = \"augment-387916\"\n", "model = \"claude-3-5-sonnet@20240620\"\n", "\n", "client = AnthropicVertex(region=region, project_id=project_id)\n", "\n", "dialog = [\n", "    {\"role\": \"user\", \"content\": \"how much is 1234 + 3218764289476 ?\"},\n", "]\n", "\n", "tools = [\n", "    {\n", "        \"name\": \"print_sentiment_scores\",\n", "        \"description\": \"Prints the sentiment scores of a given tweet or piece of text.\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"positive_score\": {\n", "                    \"type\": \"number\",\n", "                    \"description\": \"The positive sentiment score, ranging from 0.0 to 1.0.\",\n", "                },\n", "                \"negative_score\": {\n", "                    \"type\": \"number\",\n", "                    \"description\": \"The negative sentiment score, ranging from 0.0 to 1.0.\",\n", "                },\n", "                \"neutral_score\": {\n", "                    \"type\": \"number\",\n", "                    \"description\": \"The neutral sentiment score, ranging from 0.0 to 1.0.\",\n", "                },\n", "            },\n", "            \"required\": [\"positive_score\", \"negative_score\", \"neutral_score\"],\n", "        },\n", "    },\n", "    {\n", "        \"name\": \"calculator\",\n", "        \"description\": \"Adds two number\",\n", "        \"input_schema\": {\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"num1\": {\"type\": \"number\", \"description\": \"first number to add\"},\n", "                \"num2\": {\"type\": \"number\", \"description\": \"second number to add\"},\n", "            },\n", "            \"required\": [\"num1\", \"num2\"],\n", "        },\n", "    },\n", "]\n", "\n", "message = client.messages.create(\n", "    model=model,\n", "    max_tokens=64,\n", "    messages=dialog,\n", "    tools=tools,\n", ")\n", "response = message.content[0].text\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import AnthropicVertexAIChatClient\n", "\n", "client = AnthropicVertexAIChatClient()\n", "\n", "# response = client.generate(messages=[\"say hello 50 times\"], system_prompt=\"you are helpful\", max_tokens=32)\n", "# print(response)\n", "\n", "for chunk in client.generate_stream(\n", "    messages=[\"write factorial in python, typescript, and rust\"], max_tokens=1024\n", "):\n", "    print(chunk, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from google.auth.transport import requests\n", "from google.auth import default\n", "\n", "import openai\n", "\n", "location = \"us-central1\"\n", "\n", "# Not sure which project to use, but none of them work\n", "# project_id = \"system-services-prod\"\n", "# project_id = \"system-services-dev\"\n", "project_id = \"augment-387916\"\n", "\n", "base_url = f\"https://{location}-aiplatform.googleapis.com/v1beta1/projects/{project_id}/locations/{location}/endpoints/openapi/chat/completions?\"\n", "model = \"meta/llama3-405b-instruct-maas\"\n", "\n", "# This makes default() happier\n", "os.environ[\"GOOGLE_CLOUD_PROJECT\"] = project_id\n", "gcp_creds, def_project_id = default()\n", "\n", "# This extra step is needed to get the token\n", "auth_request = requests.Request()\n", "gcp_creds.refresh(auth_request)\n", "api_key = gcp_creds.token\n", "# print(api_key)\n", "\n", "client = openai.OpenAI(base_url=base_url, api_key=api_key)\n", "\n", "response = client.chat.completions.create(\n", "    model=model, messages=[{\"role\": \"user\", \"content\": \"Hello, Llama 3.1!\"}]\n", ")\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import LLaMA31VertexAIChatClient\n", "import os\n", "\n", "# project_id = \"system-services-prod\"\n", "# project_id = \"system-services-dev\"\n", "# project_id = \"augment-387916\"\n", "\n", "# os.environ[\"GOOGLE_CLOUD_PROJECT\"] = project_id\n", "\n", "client = LLaMA31VertexAIChatClient()\n", "response = client.generate(messages=[\"what model are you?\"], max_tokens=32)\n", "print(response)\n", "\n", "for chunk in client.generate_stream(\n", "    messages=[\"write factorial in python, typescript, and rust\"], max_tokens=1024\n", "):\n", "    print(chunk, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.completion_utils import LlamaCppClient\n", "from research.llm_apis.chat_utils import DeepSeekCoderV2ChatClient\n", "\n", "address = \"**************:8080\"\n", "\n", "# completion_client  = LlamaCppClient(address=address, timeout=180)\n", "\n", "# prompt = \"\"\"\\\n", "# <｜begin▁of▁sentence｜>User: hello\n", "\n", "# <｜end▁of▁sentence｜>Assistant:\"\"\"\n", "\n", "# response = completion_client.generate(prompt=prompt, max_generated_tokens=128)\n", "# print(response.content)\n", "\n", "chat_client = DeepSeekCoderV2ChatClient(address=address, timeout=1000)\n", "\n", "question = \"hi\"\n", "\n", "for response in chat_client.generate_stream(\n", "    messages=[message1, response1, message2],\n", "    system_prompt=system_prompt,\n", "    max_tokens=2048,\n", "):\n", "    print(response, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"\\\n", "You are Aug<PERSON>, an AI code assistant.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.\n", "\"\"\"\n", "\n", "message1 = \"\"\"\\\n", "I currently have the file `dashboards/activeusage/7da-sans-tenzero.sql` open, and I am actively working on it. Here is an excerpt from it:\n", "\n", "```\n", "WITH UnifiedResolutions AS (\n", "    SELECT \n", "        CAST(resolution.time AS DATE) AS resolution_date, \n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.completion_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.completion_request AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "        resolution.tenant NOT IN ('tenzero', 'i0-vanguard0')\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        CAST(resolution.time AS DATE) AS resolution_date,\n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.edit_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "        resolution.tenant NOT IN ('tenzero', 'i0-vanguard0')\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        CAST(chr.time AS DATE) AS resolution_date,\n", "        meta.user_id\n", "    FROM \n", "        `prod_request_insight_dataset.chat_host_request` AS chr\n", "    JOIN \n", "        `prod_request_insight_dataset.customers_request_metadata` AS meta\n", "        ON chr.request_id = meta.request_id\n", "    WHERE \n", "        meta.tenant <> 'tenzero'\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(resolution_date) AS min_date,\n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS resolution_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "...\n", "```\n", "\n", "Below are some relevant files from my project.\n", "\n", "Here is an excerpt from the file `dashboards/instructions/non-eu-instruction-7da.sql`:\n", "\n", "```\n", "WITH UnifiedResolutions AS (\n", "    SELECT \n", "        CAST(resolution.time AS DATE) AS resolution_date,\n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.edit_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(resolution_date) AS min_date,\n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS resolution_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/tenents/7da-by-tenant.sql`:\n", "\n", "```\n", "...\n", "        CAST(chr.time AS DATE) AS resolution_date,\n", "        meta.user_id,\n", "        meta.tenant\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        U.tenant,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateRange D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.max_date, INTERVAL 6 DAY) AND D.max_date\n", "    GROUP BY \n", "        U.tenant\n", ")\n", "SELECT \n", "    tenant,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    unique_active_users DESC;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/tenents/7da-by-tenant-eu.sql`:\n", "\n", "```\n", "...\n", "        CAST(chr.time AS DATE) AS resolution_date,\n", "        meta.user_id,\n", "        meta.tenant\n", "    FROM \n", "        eu_prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        eu_prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        U.tenant,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateRange D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.max_date, INTERVAL 6 DAY) AND D.max_date\n", "    GROUP BY \n", "        U.tenant\n", ")\n", "SELECT \n", "    tenant,\n", "    unique_active_users\n", "FROM \n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/instructions/eu-instruction-7da.sql`:\n", "\n", "```\n", "WITH UnifiedResolutions AS (\n", "    SELECT \n", "        CAST(resolution.time AS DATE) AS resolution_date,\n", "        request.user_id\n", "    FROM \n", "        eu_prod_request_insight_dataset.edit_resolution AS resolution\n", "    JOIN \n", "        eu_prod_request_insight_dataset.customers_request_metadata AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(resolution_date) AS min_date,\n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS resolution_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/tenant/7da-over-time.sql`:\n", "\n", "```\n", "...\n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/monthy-actives.sql`:\n", "\n", "```\n", "...\n", "        chr.time AS resolution_time,\n", "        meta.user_id,\n", "        'chat' AS resolution_type\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "MonthlyActiveUsers AS (\n", "    SELECT \n", "        FORMAT_TIMESTAMP('%B, %Y', TIMESTAMP_TRUNC(resolution_time, MONTH, \"America/Los_Angeles\")) AS month_year,\n", "        TIMESTAMP_TRUNC(resolution_time, MONTH, \"America/Los_Angeles\") AS sortable_month,\n", "        COUNT(DISTINCT user_id) AS monthly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        month_year, sortable_month\n", ")\n", "\n", "SELECT \n", "    month_year,\n", "    monthly_active_users,\n", "\tsortable_month\n", "FROM \n", "    MonthlyActiveUsers\n", "ORDER BY \n", "    sortable_month;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/7da.sql`:\n", "\n", "```\n", "...\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(resolution_date) AS min_date,\n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS resolution_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/monthly-actives-eu.sql`:\n", "\n", "```\n", "...\n", "        chr.time AS resolution_time,\n", "        meta.user_id,\n", "        'chat' AS resolution_type\n", "    FROM \n", "        eu_prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        eu_prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "MonthlyActiveUsers AS (\n", "    SELECT \n", "        FORMAT_TIMESTAMP('%B, %Y', TIMESTAMP_TRUNC(resolution_time, MONTH, \"America/Los_Angeles\")) AS month_year,\n", "        TIMESTAMP_TRUNC(resolution_time, MONTH, \"America/Los_Angeles\") AS sortable_month,\n", "        COUNT(DISTINCT user_id) AS monthly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        month_year, sortable_month\n", ")\n", "\n", "SELECT \n", "    month_year,\n", "    monthly_active_users,\n", "\tsortable_month\n", "FROM \n", "    MonthlyActiveUsers\n", "ORDER BY \n", "    sortable_month;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/weekly-actives-sans-tenzero.sql`:\n", "\n", "```\n", "WITH UnifiedResolutions AS (\n", "    SELECT \n", "        resolution.time AS resolution_time,\n", "        request.user_id,\n", "        'completion' AS resolution_type\n", "    FROM \n", "        prod_request_insight_dataset.completion_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.completion_request AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "  \t\trequest.tenant <> 'tenzero'\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        resolution.time AS resolution_time,\n", "        request.user_id,\n", "        'instruction' AS resolution_type\n", "    FROM \n", "        prod_request_insight_dataset.edit_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.request_metadata AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "  \t\trequest.tenant <> 'tenzero'\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        chr.time AS resolution_time,\n", "        meta.user_id,\n", "  \t\t'chat' AS resolution_type\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "    WHERE \n", "        meta.tenant <> 'tenzero'\n", "),\n", "WeeklyActiveUsers AS (\n", "    SELECT \n", "        FORMAT_TIMESTAMP('%Y-%U', TIMESTAMP_TRUNC(resolution_time, WEEK(MONDAY), \"America/Los_Angeles\")) AS week_year,\n", "        TIMESTAMP_TRUNC(resolution_time, WEEK(MONDAY), \"America/Los_Angeles\") AS sortable_week,\n", "        COUNT(DISTINCT user_id) AS weekly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        week_year, sortable_week\n", ")\n", "\n", "SELECT \n", "    week_year AS week,\n", "    weekly_active_users,\n", "\tsortable_week AS week_start_date\n", "FROM \n", "    WeeklyActiveUsers\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/devdays.sql`:\n", "\n", "```\n", "...\n", "        chr.time,\n", "        meta.user_id,\n", "        'chat' AS resolution_type\n", "    FROM \n", "        `prod_request_insight_dataset.chat_host_request` AS chr\n", "    JOIN \n", "        `prod_request_insight_dataset.customers_request_metadata` AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "DeveloperDays AS (\n", "    SELECT \n", "        DATE(resolution.time, \"America/Los_Angeles\") AS event_date, \n", "        COUNT(DISTINCT user_id) AS developer_days\n", "    FROM \n", "        UnifiedResolutions AS resolution\n", "    GROUP BY \n", "        event_date\n", ")\n", "\n", "SELECT \n", "    event_date,\n", "    developer_days,\n", "    SUM(developer_days) OVER (ORDER BY event_date) AS cumulative_developer_days\n", "FROM \n", "    DeveloperDays\n", "WHERE event_date < CURRENT_DATE(\"America/Los_Angeles\")\n", "ORDER BY \n", "    event_date;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/weekly-actives-noneu-sans-tenzero.sql`:\n", "\n", "```\n", "WITH UnifiedResolutions AS (\n", "    SELECT \n", "        TIMESTAMP_TRUNC(resolution.time, WEEK(MONDAY), \"America/Los_Angeles\") AS week_start,\n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.completion_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.completion_request AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "        resolution.tenant NOT IN ('tenzero', 'i0-vanguard0')\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        TIMESTAMP_TRUNC(resolution.time, WEEK(MONDAY), \"America/Los_Angeles\") AS week_start,\n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.edit_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "        resolution.tenant NOT IN ('tenzero', 'i0-vanguard0')\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        TIMESTAMP_TRUNC(chr.time, WEEK(MONDAY), \"America/Los_Angeles\") AS week_start,\n", "        meta.user_id\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "    WHERE \n", "        meta.tenant <> 'tenzero'\n", "),\n", "WeeklyActiveUsers AS (\n", "    SELECT \n", "        week_start,\n", "        COUNT(DISTINCT user_id) AS weekly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        week_start\n", ")\n", "\n", "SELECT \n", "    week_start AS week_start_date,\n", "    weekly_active_users\n", "FROM \n", "    WeeklyActiveUsers\n", "ORDER BY \n", "    week_start;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/7da-eu.sql`:\n", "\n", "```\n", "...\n", "    FROM \n", "        eu_prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        eu_prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "    WHERE \n", "        meta.tenant <> 'tenzero'\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(resolution_date) AS min_date,\n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS resolution_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/devdays-eu.sql`:\n", "\n", "```\n", "...\n", "        chr.time,\n", "        meta.user_id,\n", "        'chat' AS resolution_type\n", "    FROM \n", "        eu_prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        eu_prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "DeveloperDays AS (\n", "    SELECT \n", "        DATE(resolution.time, \"America/Los_Angeles\") AS event_date, \n", "        COUNT(DISTINCT user_id) AS developer_days\n", "    FROM \n", "        UnifiedResolutions AS resolution\n", "    GROUP BY \n", "        event_date\n", ")\n", "\n", "SELECT \n", "    event_date,\n", "    developer_days,\n", "    SUM(developer_days) OVER (ORDER BY event_date) AS cumulative_developer_days\n", "FROM \n", "    DeveloperDays\n", "WHERE event_date < CURRENT_DATE(\"America/Los_Angeles\")\n", "ORDER BY \n", "    event_date;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/chat/chat-7-daus.sql`:\n", "\n", "```\n", "WITH UnifiedRequests AS (\n", "    SELECT \n", "        CAST(request.time AS DATE) AS request_date,\n", "        metadata.user_id\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS request\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS metadata\n", "    ON \n", "        request.request_id = metadata.request_id\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(request_date) AS min_date,\n", "        MAX(request_date) AS max_date\n", "    FROM \n", "        UnifiedRequests\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS request_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedRequests U ON U.request_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    request_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    request_date;\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/weekly-actives-eu.sql`:\n", "\n", "```\n", "...\n", "    FROM \n", "        eu_prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        eu_prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "WeeklyActiveUsers AS (\n", "    SELECT \n", "        week_start,\n", "        COUNT(DISTINCT user_id) AS weekly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        week_start\n", ")\n", "\n", "SELECT \n", "    week_start AS week_start_date,\n", "    weekly_active_users\n", "FROM \n", "    WeeklyActiveUsers\n", "ORDER BY \n", "    week_start;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/users/new-users.sql`:\n", "\n", "```\n", "WITH FirstTimeResolutions AS (\n", "  SELECT \n", "    req.user_id,\n", "    MIN(DATE(req.time)) AS first_acceptance\n", "  FROM \n", "    prod_request_insight_dataset.customers_completion_resolution AS r\n", "  JOIN \n", "    prod_request_insight_dataset.completion_request AS req\n", "    ON req.request_id = r.request_id\n", "  WHERE \n", "    r.accepted\n", "  GROUP BY \n", "    req.user_id\n", "),\n", "RankedUsers AS (\n", "  SELECT \n", "    f.user_id,\n", "    f.first_acceptance,\n", "    ROW_NUMBER() OVER(PARTITION BY f.user_id ORDER BY f.first_acceptance) AS rn\n", "  FROM \n", "    FirstTimeResolutions AS f\n", "),\n", "CompletionsCount AS (\n", "  SELECT\n", "    req.user_id,\n", "    COUNT(req.request_id) AS total_completions\n", "  FROM\n", "    prod_request_insight_dataset.completion_request AS req\n", "  GROUP BY\n", "    req.user_id\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/weekly-actives-noneu.sql`:\n", "\n", "```\n", "...\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "),\n", "WeeklyActiveUsers AS (\n", "    SELECT \n", "        week_start,\n", "        COUNT(DISTINCT user_id) AS weekly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        week_start\n", ")\n", "\n", "SELECT \n", "    week_start AS week_start_date,\n", "    weekly_active_users\n", "FROM \n", "    WeeklyActiveUsers\n", "ORDER BY \n", "    week_start;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/users/established-users.sql`:\n", "\n", "```\n", "WITH Resolutions AS (\n", "  SELECT \n", "    user_id,\n", "    MIN(DATE(request.time)) AS first_accepted_resolution_time,\n", "    MAX(DATE(request.time)) AS last_accepted_resolution_time,\n", "    COUNT(*) AS acceptances_count\n", "  FROM \n", "    prod_request_insight_dataset.customers_completion_resolution AS resolution\n", "  JOIN \n", "    prod_request_insight_dataset.completion_request AS request\n", "    ON request.request_id = resolution.request_id\n", "  WHERE \n", "    resolution.accepted\n", "  GROUP BY \n", "    user_id\n", "  HAVING \n", "    first_accepted_resolution_time < DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)\n", "),\n", "UserCompletions AS (\n", "  SELECT\n", "    user_id,\n", "    DATE(request.time) AS activity_date,\n", "    COUNT(*) AS completions\n", "  FROM\n", "    prod_request_insight_dataset.customers_completion_resolution AS resolution\n", "  JOIN\n", "    prod_request_insight_dataset.completion_request AS request\n", "    ON request.request_id = resolution.request_id\n", "  WHERE\n", "    resolution.accepted\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/monthly-actives-sans-tenzero.sql`:\n", "\n", "```\n", "...\n", "\n", "    SELECT \n", "        chr.time AS resolution_time,\n", "        meta.user_id,\n", "        'chat' AS resolution_type\n", "    FROM \n", "        prod_request_insight_dataset.chat_host_request AS chr\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS meta\n", "        ON chr.request_id = meta.request_id\n", "    WHERE\n", "        meta.tenant <> 'tenzero'\n", "),\n", "MonthlyActiveUsers AS (\n", "    SELECT \n", "        FORMAT_TIMESTAMP('%B, %Y', TIMESTAMP_TRUNC(resolution_time, MONTH, \"America/Los_Angeles\")) AS month_year,\n", "        TIMESTAMP_TRUNC(resolution_time, MONTH, \"America/Los_Angeles\") AS sortable_month,\n", "        COUNT(DISTINCT user_id) AS monthly_active_users\n", "    FROM \n", "        UnifiedResolutions\n", "    GROUP BY \n", "        month_year, sortable_month\n", ")\n", "\n", "SELECT \n", "    month_year,\n", "    monthly_active_users,\n", "\tsortable_month\n", "FROM \n", "    MonthlyActiveUsers\n", "...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/activeusage/devdays-per-tenant.sql`:\n", "\n", "```\n", "...\n", "        meta.tenant,\n", "        chr.time,\n", "        meta.user_id\n", "    FROM \n", "        `prod_request_insight_dataset.chat_host_request` AS chr\n", "    JOIN \n", "        `prod_request_insight_dataset.customers_request_metadata` AS meta\n", "        ON chr.request_id = meta.request_id\n", "\n", "),\n", "DeveloperDays AS (\n", "    SELECT \n", "        resolution.tenant, \n", "        DATE(resolution.time, \"America/Los_Angeles\") AS event_date, \n", "        COUNT(DISTINCT user_id) AS developer_days\n", "    FROM \n", "        UnifiedResolutions AS resolution\n", "    GROUP BY \n", "        resolution.tenant, event_date\n", ")\n", "\n", "SELECT \n", "    tenant,\n", "    event_date,\n", "    developer_days\n", "FROM \n", "    DeveloperDays\n", "ORDER BY \n", "    tenant, event_date;...\n", "```\n", "\n", "Here is an excerpt from the file `dashboards/users/longest-active-users.sql`:\n", "\n", "```\n", "WITH CompletionDates AS (\n", "  SELECT\n", "    req.user_id,\n", "    MIN(DATE(req.time)) AS first_completion,\n", "    MAX(DATE(req.time)) AS last_completion\n", "  FROM\n", "    prod_request_insight_dataset.completion_request AS req\n", "  WHERE user_id <> 'health-check-1'\n", "  GROUP BY\n", "    req.user_id\n", "),\n", "DateDifferences AS (\n", "  SELECT\n", "    cd.user_id,\n", "    cd.first_completion,\n", "    cd.last_completion,\n", "    DATE_DIFF(cd.last_completion, cd.first_completion, DAY) AS days\n", "  FROM\n", "    CompletionDates AS cd\n", ")\n", "SELECT\n", "  user_id,\n", "  first_completion,\n", "  last_completion,\n", "  days\n", "FROM\n", "  DateDifferences\n", "ORDER BY\n", "  days DESC;...\n", "```\n", "\"\"\"\n", "\n", "response1 = \"\"\"\\\n", "Understood. I'll refer to the excerpts for context, and ignore them for general questions.\n", "\"\"\"\n", "\n", "message2 = \"\"\"\\\n", "The query below calculates the 7 day actives. Create a new BigQuery SQL query that takes two dates, finds the users who had actives in those dates, and show which ones are unique between them.\n", "\n", "E.g. <PERSON> 2024-06-27 and 2024-06-28, find that there are 100 users who are in both, but <PERSON> and <PERSON> are in 27 only, and <PERSON> and <PERSON> and <PERSON> are in 28 only.\n", "\n", "```sql\n", "WITH UnifiedResolutions AS (\n", "    SELECT \n", "        CAST(resolution.time AS DATE) AS resolution_date, \n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.completion_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.completion_request AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "        resolution.tenant NOT IN ('tenzero', 'i0-vanguard0')\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        CAST(resolution.time AS DATE) AS resolution_date,\n", "        request.user_id\n", "    FROM \n", "        prod_request_insight_dataset.edit_resolution AS resolution\n", "    JOIN \n", "        prod_request_insight_dataset.customers_request_metadata AS request\n", "        ON resolution.request_id = request.request_id\n", "    WHERE \n", "        resolution.accepted AND\n", "        resolution.tenant NOT IN ('tenzero', 'i0-vanguard0')\n", "\n", "    UNION ALL\n", "\n", "    SELECT \n", "        CAST(chr.time AS DATE) AS resolution_date,\n", "        meta.user_id\n", "    FROM \n", "        `prod_request_insight_dataset.chat_host_request` AS chr\n", "    JOIN \n", "        `prod_request_insight_dataset.customers_request_metadata` AS meta\n", "        ON chr.request_id = meta.request_id\n", "    WHERE \n", "        meta.tenant <> 'tenzero'\n", "),\n", "DateRange AS (\n", "    SELECT \n", "        MIN(resolution_date) AS min_date,\n", "        MAX(resolution_date) AS max_date\n", "    FROM \n", "        UnifiedResolutions\n", "),\n", "DateList AS (\n", "    SELECT \n", "        DATE_ADD(min_date, INTERVAL day_offset DAY) AS date\n", "    FROM \n", "        DateRange\n", "    CROSS JOIN \n", "        UNNEST(GENERATE_ARRAY(0, DATE_DIFF(max_date, min_date, DAY))) AS day_offset\n", "),\n", "RollingActiveUsers AS (\n", "    SELECT\n", "        D.date AS resolution_date,\n", "        COUNT(DISTINCT U.user_id) AS unique_active_users\n", "    FROM \n", "        DateList D\n", "    LEFT JOIN \n", "        UnifiedResolutions U ON U.resolution_date BETWEEN DATE_SUB(D.date, INTERVAL 6 DAY) AND D.date\n", "    GROUP BY \n", "        D.date\n", ")\n", "\n", "SELECT \n", "    resolution_date,\n", "    unique_active_users\n", "FROM \n", "    RollingActiveUsers\n", "ORDER BY \n", "    resolution_date;\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Iterable\n", "from research.llm_apis.chat_utils import Llama3LlamaCppApiClient\n", "\n", "\n", "address = \"**************:8080\"\n", "client = Llama3LlamaCppApiClient(address=address, timeout=60)\n", "\n", "\n", "def iter_lines(stream: Iterable[str]):\n", "    partial_line = \"\"\n", "    split_text = \"\\n\"\n", "    for text in stream:\n", "        while split_text in text:\n", "            elems = text.split(split_text, maxsplit=1)\n", "            assert len(elems) == 2, len(elems)\n", "            line = partial_line + elems[0]\n", "            print(\"yielding line\")\n", "            text = elems[1]\n", "            partial_line = \"\"\n", "        partial_line += text\n", "    yield partial_line\n", "\n", "\n", "for response in iter_lines(\n", "    client.generate_stream(messages=[\"python hello-world script\"], max_tokens=1024)\n", "):\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "address = \"**************:8080\"\n", "url = f\"http://{address}/completion\"\n", "\n", "headers = {\n", "    \"Content-Type\": \"application/json\",\n", "}\n", "data = {\n", "    \"prompt\": \"how much is 2+2\",\n", "    \"n_predict\": 128,\n", "    \"temperature\": 0,\n", "    \"stream\": True,\n", "    # \"stop\": stop,\n", "}\n", "\n", "with requests.post(\n", "    url, headers=headers, data=json.dumps(data), timeout=20, stream=True\n", ") as http_response:\n", "    for line in http_response.iter_lines():\n", "        text = line.decode(encoding=\"utf-8\")\n", "        print(text)\n", "        data = json.loads(text.replace(\"data: \", \"\"))\n", "        print(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.guy.apis.llama3_tokenizer import (\n", "    Message,\n", "    <PERSON><PERSON>,\n", "    <PERSON><PERSON><PERSON>,\n", "    ChatFormat,\n", "    Role,\n", ")\n", "from experimental.guy.pr_task_descriptions.generate_pr_instructions import (\n", "    process_sample,\n", "    load_pr_data,\n", ")\n", "from pathlib import Path\n", "import json\n", "\n", "llama = LlamaCppClient(address=\"**************:8080\")\n", "tokenizer = Tokenizer(\n", "    \"/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B-Instruct/tokenizer.model\"\n", ")\n", "prompt_formatter = ChatFormat(tokenizer)\n", "\n", "dialog = [\n", "    Message(\n", "        role=\"user\",\n", "        content=\"write a factorial function\",\n", "    )\n", "]\n", "\n", "# prompt = tokenizer.decode(prompt_formatter.encode_dialog_prompt(dialog))\n", "# response = llama.generate(prompt=prompt, max_generated_tokens=1024)\n", "# print(response.content)\n", "\n", "\n", "class LlamaGptWrapper:\n", "    def __call__(self, messages: list[str], model: str, **kwargs) -> str:\n", "        roles: list[Role] = [\"user\", \"assistant\"]\n", "        dialog: list[Message] = []\n", "\n", "        for i, message in enumerate(messages):\n", "            dialog.append(Message(role=roles[i % 2], content=message))\n", "\n", "        prompt = tokenizer.decode(prompt_formatter.encode_dialog_prompt(dialog))\n", "        response = llama.generate(prompt=prompt, max_generated_tokens=1024)\n", "        return response.content\n", "\n", "\n", "data_root = Path(\"/mnt/efs/augment/user/guy/pr_instructions\")\n", "filename = Path(\"prs_filtered_10k.jsonl\")\n", "samples = load_pr_data(str(data_root / filename), num_samples=5000)\n", "\n", "gpt_wrapper = LlamaGptWrapper()\n", "\n", "results = []\n", "\n", "# for sample in samples:\n", "#     result = process_sample(gpt_wrapper, \"model\", sample, with_cot=True)\n", "#     results.append(result)\n", "#     with Path(\"/mnt/efs/augment/user/guy/pr_instructions_from_llama3.jsonl\").open(\"w\") as f:\n", "#         for result in results:\n", "#             f.write(json.dumps(result) + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import Llama3LlamaCppApiClient\n", "from research.core.llama_prompt_formatters import CodeLlamaChatFormatter, Dialog\n", "\n", "llama3 = Llama3LlamaCppApiClient(address=\"**************:8080\", timeout=60)\n", "\n", "question_phase1 = \"\"\"\\\n", "Consider the following code-editing instruction:\n", "-----\n", "- replace function-body with an appropriate placeholder such as `...`, `pass`, `throw new NotImplementedException();` or `// ...`\n", "-----\n", "\n", "Apply the code-editing instruction to the following code chunk. Adapt the instruction if necessary.\n", "\n", "\n", "Code chunk:\n", "\n", "```\n", "\n", "import java.util.HashMap;\n", "import java.util.Map;\n", "\n", "/**\n", " * Cat上下文信息\n", " * <AUTHOR>\n", " * @date 2020/11/24\n", " */\n", "public class CatContext implements Cat.Context {\n", "\n", "    /** 存储内容 */\n", "    private Map properties = new HashMap<>();\n", "\n", "    @Override\n", "    public void addProperty(String key, String value) {\n", "        properties.put(key, value);\n", "    }\n", "\n", "\n", "```\n", "\n", "The output should be shown as follows:\n", "- The adapted instruction, formatted as \"Instruction: ...\"\n", "- The modified code chunk\n", "Yes/No - check your answer and tell me whether the instruction was correctly followed\n", "Yes/No - does the instruction result in meaningful change?\n", "\n", "Don't show me anything else!\n", "\n", "Example\n", "-------\n", "Instruction: Replace \" with '\n", "```\n", "a = 6\n", "```\n", "Yes\n", "No\n", "\"\"\"\n", "\n", "question_phase1_modified = \"\"\"\\\n", "Consider the following code-editing instruction:\n", "-----\n", "- replace function-body with an appropriate placeholder such as `...`, `pass`, `throw new NotImplementedException();` or `// ...`\n", "-----\n", "\n", "Apply the code-editing instruction to the following code chunk. Adapt the instruction if necessary.\n", "\n", "\n", "Code chunk:\n", "\n", "```\n", "\n", "import java.util.HashMap;\n", "import java.util.Map;\n", "\n", "/**\n", " * Cat上下文信息\n", " * <AUTHOR>\n", " * @date 2020/11/24\n", " */\n", "public class CatContext implements Cat.Context {\n", "\n", "    /** 存储内容 */\n", "    private Map properties = new HashMap<>();\n", "\n", "    @Override\n", "    public void addProperty(String key, String value) {\n", "        properties.put(key, value);\n", "    }\n", "\n", "\n", "```\n", "\n", "The output should be shown as follows:\n", "- The adapted instruction, formatted as \"Instruction: ...\"\n", "- The modified code chunk\n", "Yes/No - check your answer and tell me whether the instruction was correctly followed\n", "Yes/No - does the instruction result in meaningful change?\n", "\n", "Don't show me anything else!\n", "\n", "Example\n", "-------\n", "Instruction: Replace \" with '\n", "```\n", "a = 6\n", "```\n", "Yes\n", "No\n", "\"\"\"\n", "\n", "question_phase2 = \"\"\"\\\n", "Code block A:\n", "```\n", "import java.util.HashMap;\n", "import java.util.Map;\n", "\n", "/**\n", " * Cat上下文信息\n", " * <AUTHOR>\n", " * @date 2020/11/24\n", " */\n", "public class CatContext implements Cat.Context {\n", "\n", "    /** 存储内容 */\n", "    private Map properties = new HashMap<>();\n", "\n", "    @Override\n", "    public void addProperty(String key, String value) {\n", "        throw new UnsupportedOperationException();\n", "    }\n", "\n", "```\n", "\n", "Code block B:\n", "```\n", "import java.util.HashMap;\n", "import java.util.Map;\n", "\n", "/**\n", " * Cat上下文信息\n", " * <AUTHOR>\n", " * @date 2020/11/24\n", " */\n", "public class CatContext implements Cat.Context {\n", "\n", "    /** 存储内容 */\n", "    private Map properties = new HashMap<>();\n", "\n", "    @Override\n", "    public void addProperty(String key, String value) {\n", "        properties.put(key, value);\n", "    }\n", "\n", "```\n", "\n", "Give me an instruction that a developer may have followed to edit code block A to get code block B. The instruction shouldn't mention \"code block A\" or \"code block B\": e.g., say \"fix formatting\" instead of \"fix formatting in code block A\".\n", "\n", "Give me versions of the instruction with different complexity from normal (say 8 words) to extremely terse (2 words, maybe just \"fix it\", \"add missing\", etc.) The output should just consist one instruction version per line formatted as \"Instruction: ...\"\n", "\n", "Once that's printed out, check over the whole example. Do you think this is a high-quality example of applying an instruction to a chunk of code?  Output one more line assessing the quality as High, Medium or Low. E.g., \"Quality: Medium\"\n", "\"\"\"\n", "\n", "response = llama3.generate(messages=[question_phase1], max_tokens=1024)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import Llama3LlamaCppApiClient\n", "\n", "llama3 = Llama3LlamaCppApiClient(address=\"**************:8080\", timeout=60)\n", "\n", "question = \"write a python function to render a tweet given the text and an unordered list of its entities (hashtags, URLs, @mentions). Each entity is represented by the replacement text, a start index and an end index.\"\n", "\n", "answer = '''\\\n", "Here is a Python function that renders a tweet given the text and an unordered list of its entities:\n", "```\n", "def render_tweet(text, entities):\n", "    \"\"\"\n", "    Render a tweet with entities (hashtags, URLs, @mentions) highlighted.\n", "\n", "    Args:\n", "        text (str): The tweet text.\n", "        entities (list): A list of entities, where each entity is a dict with:\n", "            - `replacement` (str): The text to replace the entity with (e.g. \"<a href='...'>...</a>\").\n", "            - `start` (int): The start index of the entity in the tweet text.\n", "            - `end` (int): The end index of the entity in the tweet text.\n", "\n", "    Returns:\n", "        str: The rendered tweet with entities highlighted.\n", "    \"\"\"\n", "    # Create a copy of the original text to avoid modifying it\n", "    rendered_text = text[:]\n", "\n", "    # Sort entities by start index to ensure correct replacement order\n", "    entities.sort(key=lambda x: x['start'])\n", "\n", "    # Iterate over entities and replace them in the text\n", "    for entity in entities:\n", "        replacement = entity['replacement']\n", "        start = entity['start']\n", "        end = entity['end']\n", "        rendered_text = rendered_text[:start] + replacement + rendered_text[end:]\n", "\n", "    return rendered_text\n", "```\n", "Here's an example usage:\n", "```\n", "text = \"Check out this cool article! https://example.com/article @john_doe #python\"\n", "entities = [\n", "    {'replacement': '<a href=\"https://example.com/article\">https://example.com/article</a>', 'start': 24, 'end': 44},\n", "    {'replacement': '<a href=\"https://twitter.com/john_doe\">@john_doe</a>', 'start': 45, 'end': 54},\n", "    {'replacement': '<a href=\"https://twitter.com/hashtag/python\">#python</a>', 'start': 55, 'end': 61}\n", "]\n", "\n", "rendered_tweet = render_tweet(text, entities)\n", "print(rendered_tweet)\n", "```\n", "Output:\n", "```\n", "Check out this cool article! <a href=\"https://example.com/article\">https://example.com/article</a> <a href=\"https://twitter.com/john_doe\">@john_doe</a> <a href=\"https://twitter.com/hashtag/python\">#python</a>\n", "```\n", "Note that this implementation assumes that the `replacement` text does not contain any characters that would affect the indexing of the original text (e.g. no newline characters). If you need to handle more complex cases, you may need to modify the implementation accordingly.\n", "'''\n", "\n", "messages = [question, answer, \"This has a bug. Can you fix it?\"]\n", "\n", "response = llama3.generate(messages=messages, max_tokens=1024)\n", "print(response)\n", "\n", "# dialog = [\n", "#     {\n", "#         \"role\": \"user\",\n", "#         \"content\": \"write a factorial function\",\n", "#     }\n", "# ]\n", "\n", "# prompt = \"<s>\"\n", "# for m in dialog:\n", "#     prompt += f\"Source: {m['role']}\\n\\n {m['content'].strip()}\"\n", "#     prompt += \" <step> \"\n", "# prompt += \"Source: assistant\\nDestination: user\\n\\n \"\n", "\n", "# response = code_llama.generate(prompt=prompt, max_generated_tokens=128)\n", "# tokens = prompt_formatter.prepare_chat_prompt(Dialog(messages=[\"write a factorial function\"]))\n", "# prompt = prompt_formatter.tokenizer.detokenize(tokens)\n", "# response = code_llama.generate(prompt=prompt, max_generated_tokens=1024)\n", "# # response = code_llama.generate(prompt=\"write a factorial function\", max_generated_tokens=128)\n", "# print(response.content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from utils import LlamaCppClient\n", "\n", "deepseek = LlamaCppClient(address=\"*************:8080\")\n", "\n", "prompt = \"\"\"\\\n", "def process(s):\n", "    # trim all the whitespace\n", "    s = \"\"\"\n", "response = deepseek.generate(prompt=prompt, max_generated_tokens=64)\n", "print(response.content)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}