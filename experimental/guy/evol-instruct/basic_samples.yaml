[
    {
        "type": "files",
        "difficulty": "easy",
        "instruction": "print the second column of file.txt",
        "command": "cat file.txt | awk '{print $2}'",
        # "terse_instruction": "column 2 of file.txt",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "medium",
                "instruction": "sum the numbers in the second column of mydata.txt",
                "command": "awk '{sum += $2} END {print sum}' mydata.txt",
            },
            {
                "transformation_type": "terse",
                "instruction": "column 2 of file.txt",
            },
        ]
    },
    {
        "type": "databases",
        "difficulty": "easy",
        "instruction": "list all databases",
        "command": "Error: database type not specified",  # hallucination sink
        "transformations": [
            {
                "transformation_type": "clarify",
                "difficulty": "easy",
                "instruction": "list all tables in personnel.sqlite",
                "command": "sqlite3 personnel.sqlite \".tables\"",
            },
        ]
    },
    {
        "type": "k8s",
        "difficulty": "medium",
        "instruction": "list all pods with status running or pending",
        "command": "kubectl get pods | grep -E '(Running|Pending)'",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "hard",
                "instruction": "list all pods in london context with status running or pending that start with virt-",
                "command": "kubectl get pods --context london | grep -E '^virt-' | grep -E '(Running|Pending)'",
            },
            {
                "transformation_type": "terse",
                "instruction": "running or pending pods",
            },
        ]
    },
    {
        "type": "shell",
        "difficulty": "easy",
        "instruction": "what is the name of my shell",
        "command": "echo $SHELL",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "medium",
                "instruction": "what is the name of my shell and its version",
                "command": "echo $SHELL | awk -F/ '{print $NF}' | xargs -I {} sh -c '{} --version | head -n1'",
            },
            {
                "transformation_type": "terse",
                "instruction": "shell name",
            },
            {
                "transformation_type": "rephrase",
                "instruction": "how is my shell called",
            },
        ]
    },
    {
        "type": "file-manipulation",
        "difficulty": "easy",
        "instruction": "show the value of the environment variable",
        "command": "echo $<MY_ENV_VAR>",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "medium",
                "instruction": "show all environment variables that point to existing file paths",
                "command": "printenv | awk -F= '{print $1\"=\"$2}' | while IFS='=' read -r name value; do [ -e \"$value\" ] && echo \"$name=$value\"; done",
            },
        ]
    },
    {
        "type": "system",
        "difficulty": "easy",
        "instruction": "how much free memory",
        "command": "free -h"
    },
    {
        "type": "system",
        "difficulty": "easy",
        "instruction": "what is my ip",
        "command": "hostname -i",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "medium",
                "instruction": "what is my public ip",
                "command": "my ip",
            },
            {
                "transformation_type": "terse",
                "instruction": "my ip"
            },
        ]
    },
    {
        "type": "ssh",
        "difficulty": "easy",
        "instruction": "add the key to my ssh agent",
        "command": "ssh-add <path/to/key>"  # hallucination sink
    },
    {
        "type": "aws",
        "difficulty": "easy",
        "instruction": "aws list all vpcs",
        "command": "aws ec2 describe-vpcs",
    },
    {
        "type": "compiling-and-linking",
        "difficulty": "easy",
        "instruction": "compile the code",
        "command": "gcc -o <target> <source>",  # hallucination sink
        "transformations": [
            {
                "transformation_type": "clarify",
                "difficulty": "easy",
                "instruction": "compile handler.c to handler",
                "command": "gcc -o handler handler.c",
            },
        ]
    },
    {
        "type": "file-manipulation",
        "difficulty": "medium",
        "instruction": "merge all csv files, sort them and remove duplicate lines",
        "command": "cat *.csv | sort | uniq",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "hard",
                "instruction": "Merge all CSV files in the current directory, remove duplicate lines, sort the data by the second column in descending order, and then save the output to a new file named merged_sorted_unique.csv",
                "command": "cat *.csv | sort | uniq | sort -t, -k2,2nr > merged_sorted_unique.csv",
            },
        ]
    },
    {
        "type": "images",
        "difficulty": "medium",
        "instruction": "resize my_image.png to a width of 800 pixels",
        "command": "convert my_image.png -resize 800x my_image_resized.png",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "hard",
                "instruction": "Find all JPEG and PNG files in the current directory and its subdirectories, resize them to a width of 800 pixels while maintaining aspect ratio, and save them with a \"_small\" suffix before the file extension.",
                "command": "find . -type f \\( -iname \"*.jpg\" -o -iname \"*.jpeg\" -o -iname \"*.png\" \\) -exec convert {} -resize 800x \\>{}_small\\> \\;",
            },
        ]
    },
    {
        "type": "git",
        "difficulty": "easy",
        "instruction": "undo the last commit",
        "command": "git reset --soft HEAD~1",
        "transformations": [
            {
                "transformation_type": "complicate",
                "difficulty": "medium",
                "instruction": "undo the last 3 commits, discarding the changes",
                "command": "git reset --hard HEAD~3",
            },
            {
                "transformation_type": "complicate",
                "difficulty": "medium",
                "instruction": "undo the last 4 commits, keeping the changes",
                "command": "git reset --soft HEAD~4",
            },
        ]
    },
    {
        "type": "git",
        "difficulty": "easy",
        "instruction": "unstage the file",
        "command": "git restore --staged <file>",
        "transformations": [
            {
                "transformation_type": "clarify",
                "difficulty": "easy",
                "instruction": "unstage server.py",
                "command": "git restore --staged server.py",
            },
        ]
    },
]
