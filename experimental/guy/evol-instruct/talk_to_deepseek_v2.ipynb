{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import OpenAIAPIClient\n", "\n", "def deconstruct_llama_prompt(prompt: str) -> list[dict[str, str]]:\n", "    system_prompt_header = \"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\"\n", "    user_header = \"<|start_header_id|>user<|end_header_id|>\"\n", "    assistant_header = \"<|start_header_id|>assistant<|end_header_id|>\"\n", "\n", "    assert prompt.endswith(assistant_header)\n", "    prompt = prompt[:-len(assistant_header)]\n", "\n", "    elems = prompt.split(\"<|eot_id|>\")\n", "    # assert len(elems) % 2 == 0\n", "\n", "    assert elems[0].startswith(system_prompt_header)\n", "    system_prompt = elems[0].replace(system_prompt_header, \"\")\n", "    dialog = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system_prompt,\n", "        }\n", "    ]\n", "\n", "    for i, elem in enumerate(elems[1:]):\n", "        elem = elem.replace(user_header, \"\")\n", "        elem = elem.replace(assistant_header, \"\")\n", "        assert \"<|\" not in elem\n", "        if i % 2 == 0:\n", "            dialog.append({\"role\": \"user\", \"content\": elem})\n", "        else:\n", "            dialog.append({\"role\": \"assistant\", \"content\": elem})\n", "\n", "    return dialog\n", "\n", "dialog = deconstruct_llama_prompt(raw_prompt)\n", "\n", "system_prompt = dialog[0][\"content\"]\n", "messages = [m[\"content\"] for m in dialog[1:]]\n", "\n", "address = \"*************:8000\"\n", "client = OpenAIAPIClient(address=address, vllm_prompt_workaround=True)\n", "#messages = [\"Count to 10\"]\n", "\n", "# response = client.generate(messages=messages, max_tokens=10000)\n", "# print(response)\n", "\n", "for response in client.generate_stream(messages=messages, system_prompt=system_prompt, max_tokens=10000):\n", "    print(response, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_prompt = '''\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "You are Aug<PERSON>, an AI code assistant.\n", "Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.\n", "\n", "Below are some relevant files from the developer's project.\n", "\n", "Here is the file `base/datasets/edit.py`:\n", "\n", "```\n", "\"\"\"Dataclasses for exported Request Insight code-instructions.\"\"\"\n", "\n", "import logging\n", "from dataclasses import dataclass\n", "from datetime import datetime\n", "from typing import Optional\n", "\n", "from dataclasses_json import dataclass_json\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "@dataclass_json\n", "@dataclass(frozen=True)\n", "class EditPosition:\n", "    \"\"\"Represents the position of a completion request relative to a cached blob.\"\"\"\n", "\n", "    blob_name: str\n", "    \"\"\"The blob name of the cached copy of the file.\"\"\"\n", "\n", "    prefix_begin: int\n", "    \"\"\"The offset in Unicode characters where the prefix region begins in `blob_name`.\"\"\"\n", "\n", "    suffix_end: int\n", "    \"\"\"The offset in Unicode characters where the suffix region ends in `blob_name`.\"\"\"\n", "\n", "    original_prefix_length: int\n", "    \"\"\"The length of the prefix in the original request (in Unicode characters).\"\"\"\n", "\n", "    original_suffix_length: int\n", "    \"\"\"The length of the suffix in the original request (in Unicode characters).\"\"\"\n", "\n", "\n", "@dataclass_json\n", "@dataclass(frozen=True)\n", "class EditRequest:\n", "    \"\"\"Represents a single edit request.\"\"\"\n", "\n", "    prefix: str\n", "    \"\"\"The prefix in the edit request.\n", "\n", "    To make it easier to use, the prefix is expanded to begin at the start of the file.\n", "    \"\"\"\n", "\n", "    suffix: str\n", "    \"\"\"The suffix in the edit request.\n", "\n", "    To make it easier to use, the suffix is expanded to the end of the file.\n", "    \"\"\"\n", "\n", "    selected_text: str\n", "    \"\"\"The selected text in the edit request.\"\"\"\n", "\n", "    instruction: str\n", "    \"\"\"The instruction in the edit request.\"\"\"\n", "\n", "    path: str\n", "    \"\"\"The path of the current file being completed.\"\"\"\n", "\n", "    # NOTE(arun): the type of this field needs to be a list because dataclasses-json\n", "    # doesn't understand Sequence.\n", "    blob_names: list[str]\n", "    \"\"\"The list of blob names (i.e. files) in the request.\n", "\n", "    This list does not contain the blob name for \"current file\" (i.e. the file being\n", "    completed); instead the current file contents is `prefix + suffix`.\n", "    \"\"\"\n", "\n", "    timestamp: datetime\n", "    \"\"\"The timestamp of the edit request.\"\"\"\n", "\n", "    lang: str\n", "    \"\"\"Programming language of the current file.\"\"\"\n", "\n", "    position: Optional[EditPosition] = None\n", "    \"\"\"The position of the edit request relative to the cached blob.\n", "\n", "    This information is useful when replaying requests against a production model\n", "    client.\n", "    \"\"\"\n", "\n", "    @property\n", "    def original_prefix(self) -> str:\n", "        \"\"\"The original prefix in the edit request.\"\"\"\n", "        return (\n", "            self.prefix[-self.position.original_prefix_length :]\n", "            if self.position\n", "            else self.prefix\n", "        )\n", "\n", "    @property\n", "    def original_suffix(self) -> str:\n", "        \"\"\"The original suffix in the edit request.\"\"\"\n", "        return (\n", "            self.suffix[: self.position.original_suffix_length]\n", "            if self.position\n", "            else self.suffix\n", "        )\n", "\n", "\n", "@dataclass_json\n", "@dataclass\n", "class EditResponse:\n", "    \"\"\"Represents a single completion response.\"\"\"\n", "\n", "    timestamp: datetime\n", "    \"\"\"The timestamp of the completion response.\"\"\"\n", "\n", "    text: str\n", "    \"\"\"The completion text generated by the system.\"\"\"\n", "\n", "    model_name: str\n", "    \"\"\"The model used to generate the edit.\"\"\"\n", "\n", "    unknown_blob_names: list[str]\n", "    \"\"\"The list of blob names that were not found at the time of the request.\"\"\"\n", "\n", "    checkpoint_not_found: bool\n", "    \"\"\"True if the checkpoint for the completion response was not found.\"\"\"\n", "\n", "\n", "@dataclass_json\n", "@dataclass\n", "class EditResolution:\n", "    \"\"\"Represents an edit resolution.\"\"\"\n", "\n", "    is_accepted: bool\n", "    annotated_text: Optional[str] = None\n", "    annotated_instruction: Optional[str] = None\n", "    timestamp: Optional[datetime] = None\n", "\n", "\n", "@dataclass_json\n", "@dataclass\n", "class EditDatum:\n", "    \"\"\"Represents a full edit event with its request, response.\"\"\"\n", "\n", "    request_id: str\n", "    \"\"\"The request ID of the completion event.\"\"\"\n", "\n", "    user_id: str\n", "    \"\"\"The user ID of the user who made the edit.\"\"\"\n", "\n", "    session_id: str\n", "    \"\"\"The session ID of the user who made the edit.\"\"\"\n", "\n", "    user_agent: str\n", "    \"\"\"The user ID of the user who made the edit.\"\"\"\n", "\n", "    request: EditRequest\n", "...\n", "```\n", "\n", "Here is the file `experimental/dxy/edits/data_type.py`:\n", "\n", "```\n", "\"\"\"The foundamental data types used in the edit project.\"\"\"\n", "import copy\n", "import dataclasses\n", "import hashlib\n", "import json\n", "import re\n", "import typing\n", "\n", "import termcolor\n", "import tqdm\n", "import zstandard as zstd\n", "\n", "from research.core import utils_for_dataclass\n", "\n", "LineOriginType = typing.Literal[\" \", \"+\", \"-\"]\n", "\n", "\n", "@dataclasses.dataclass\n", "class Line:\n", "    content: str\n", "    origin: LineOriginType\n", "\n", "\n", "@dataclasses.dataclass\n", "class SimpleEditData:\n", "    \"\"\"The simple edit data class.\"\"\"\n", "\n", "    prefix: typing.Optional[str] = None\n", "    suffix: typing.Optional[str] = None\n", "    selected_code: typing.Optional[str] = None\n", "    modified_code: typing.Optional[str] = None\n", "    instruction: typing.Optional[str] = None\n", "\n", "\n", "@dataclasses.dataclass\n", "class EditData:\n", "    \"\"\"The edit data class.\n", "\n", "    Note that the lines may contain the following characters:\n", "    - \" \" : the line is unchanged.\n", "    - \"+\" : the line is added.\n", "    - \"-\" : the line is deleted.\n", "    There is no guarantee that for the order of these 3 characters.\n", "    \"\"\"\n", "\n", "    repo: str\n", "    \"\"\"The repository name.\"\"\"\n", "\n", "    commit_sha: str\n", "    \"\"\"The commit SHA string.\"\"\"\n", "\n", "    file_path: str\n", "    \"\"\"The file path.\"\"\"\n", "\n", "    lines: typing.List[Line]\n", "    \"\"\"The lines in the hunk.\"\"\"\n", "\n", "    old_file_content: str\n", "    \"\"\"The full content in the old file.\"\"\"\n", "\n", "...\n", "    def get_prefix_via_lrange(self, lrange: typing.Tuple[int, int]) -> str:\n", "        lines = self.new_file_content.splitlines(keepends=True)\n", "        partial_lines = lines[: lrange[0]]\n", "        return \"\".join(partial_lines)\n", "\n", "    def get_suffix_via_lrange(self, lrange: typing.Tuple[int, int]) -> str:\n", "        lines = self.new_file_content.splitlines(keepends=True)\n", "        partial_lines = lines[lrange[1] :]\n", "        return \"\".join(partial_lines)\n", "\n", "    def validate_is_consecutive_edit(self) -> bool:\n", "        \"\"\"Validate the edit data.\"\"\"\n", "        deletes, adds = [], []\n", "        for index, line in enumerate(self.lines):\n", "            if line.origin == \"-\":\n", "                deletes.append(index)\n", "            elif line.origin == \"+\":\n", "                adds.append(index)\n", "        if deletes and not is_consecutive(deletes):\n", "            return False\n", "        if adds and not is_consecutive(adds):\n", "            return False\n", "        if not deletes and not adds:\n", "            return False\n", "        return True\n", "\n", "\n", "@dataclasses.dataclass\n", "...\n", "```\n", "\n", "Here is the file `experimental/dxy/edits/data_type_test.py`:\n", "\n", "```\n", "\"\"\"The unit test for the data_type.py file.\n", "\n", "pytest experimental/dxy/edits/data_type_test.py\n", "\"\"\"\n", "import dataclasses\n", "import json\n", "import typing\n", "import unittest\n", "\n", "from experimental.dxy.edits import data_type\n", "from research.core import utils_for_dataclass\n", "\n", "\n", "def create_edit_data(lines: typing.List[data_type.Line]):\n", "    data = data_type.EditData(\n", "        repo=\"\",\n", "        commit_sha=\"\",\n", "        file_path=\"\",\n", "        lines=lines,\n", "        old_file_content=\"\",\n", "        new_file_content=\"\",\n", "        old_hunk_range=(0, 0),\n", "        new_hunk_range=(1, 1),\n", "    )\n", "    return data\n", "\n", "\n", "class TestDataType(unittest.TestCase):\n", "    \"\"\"Test the Data Type.\"\"\"\n", "\n", "...\n", "```\n", "\n", "Here is the file `research/data/synthetic_code_edit/types.py`:\n", "\n", "```\n", "\"\"\"Sample data for the code edit.\"\"\"\n", "import dataclasses\n", "import typing\n", "\n", "\n", "@dataclasses.dataclass\n", "class Instruction:\n", "    \"\"\"The instruction.\"\"\"\n", "\n", "    text: str\n", "    \"\"\"The instruction used in code edit.\"\"\"\n", "\n", "    tags: typing.Optional[list[str]]\n", "    \"\"\"The tags.\"\"\"\n", "\n", "    @staticmethod\n", "    def load_from_any(data) -> \"Instruction\":\n", "        \"\"\"Load the instruction from any data.\"\"\"\n", "        if isinstance(data, str):\n", "            return Instruction(data, None)\n", "        elif isinstance(data, dict):\n", "            return Instruction(data[\"instruction\"], data[\"tags\"])\n", "        else:\n", "            raise ValueError(f\"Unknown data type: {type(data)}\")\n", "\n", "\n", "@dataclasses.dataclass\n", "class CodeEditData:\n", "    \"\"\"The simple raw edit scope.\"\"\"\n", "\n", "    repo_url: str\n", "    \"\"\"The url to the GitHub repository.\"\"\"\n", "\n", "    file_name: str\n", "    \"\"\"The file name.\"\"\"\n", "\n", "    prefix: str\n", "    \"\"\"The preceding string of the selected code in a file .\"\"\"\n", "\n", "    suffix: str\n", "    \"\"\"The succeeding string of the selected code in a file.\"\"\"\n", "\n", "    selected_code: typing.Optional[str] = None\n", "    \"\"\"The selected code in a file, before applying the instruction.\"\"\"\n", "\n", "    updated_code: typing.Optional[str] = None\n", "    \"\"\"The updated code, after applying the instruction to selected_code.\"\"\"\n", "\n", "    instructions: typing.Optional[list[Instruction]] = None\n", "    \"\"\"The instruction string(s).\"\"\"\n", "\n", "    inverse_instructions: typing.Optional[list[Instruction]] = None\n", "    \"\"\"The inverse instruction string(s).\"\"\"\n", "\n", "    metadata: typing.Dict[str, typing.Any] = dataclasses.field(default_factory=dict)\n", "    \"\"\"The miscellaneous data.\"\"\"\n", "\n", "    @property\n", "    def lines_before_edit(self) -> list[str]:\n", "        assert self.selected_code is not None\n", "...\n", "```\n", "\n", "Here is the file `research/core/edit_prompt_input.py`:\n", "\n", "```\n", "\"\"\"Definition of the data format for code edits research.\"\"\"\n", "\n", "import copy\n", "import dataclasses\n", "import typing\n", "\n", "from base.prompt_format_edit.prompt_formatter import EditPromptInput\n", "from base.ranges.range_types import LineRange\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk\n", "\n", "\n", "@dataclasses.dataclass(frozen=True)\n", "class ResearchEditPromptInput:\n", "    \"\"\"Extensible adaptation of the EditPromptInput for research.\n", "\n", "    TODO: Update to inherit from base.prompt_format_edit.prompt_formatter.EditPromptInput.\n", "    In order to do so, we need to migrate the research codebase from using the research.core.type.Chunk\n", "    abstraction to the base.prompt_format_completion.prompt_formatter.PromptChunk abstraction.\n", "\n", "    We also need to add the prefix_begin and suffix_end fields.\n", "    \"\"\"\n", "\n", "    path: str\n", "    \"\"\"The file path.\"\"\"\n", "\n", "    prefix: str\n", "    \"\"\"The content before the selection.\"\"\"\n", "\n", "    selected_code: str\n", "...\n", "```\n", "\n", "Here is the file `research/core/types.py`:\n", "\n", "```\n", "...\n", "@dataclass\n", "class Document:\n", "    \"\"\"Represents a named sequence of text for retrieval.\n", "\n", "    A Document is a sequence of text of any length, and usually corresponds to a\n", "    single source file. The document may be split by the index into one or more\n", "    blocks of shorter sequences, called chunks, for retrieval.\n", "\n", "    Args:\n", "        id: unique ID to refer to this document, typically the sha256 hash of\n", "            its contents\n", "        text: document text\n", "        meta: dictionary with implementation specific metadata for the document\n", "        path: (optional) path name for the document\n", "    \"\"\"\n", "\n", "    id: DocumentId\n", "    text: str\n", "    path: Optional[str] = None\n", "    meta: Optional[dict] = None\n", "\n", "    def __str__(self):\n", "        d = {\n", "            \"text\": self.text,\n", "            \"id\": self.id,\n", "            \"meta\": self.meta,\n", "            \"path\": self.path,\n", "        }\n", "        return str(d)\n", "\n", "...\n", "    \"\"\"Represents the smallest unit of retrieval.\n", "\n", "    A Chunk represents a part of a Document that is the smallest unit of\n", "    retrieval. The sequence of text in the chunk may not exactly match the\n", "    document text covered by the span (char_offset + length).\n", "\n", "    Args:\n", "        id: a uniue chunk ID\n", "        text: retrieved text\n", "        parent_doc: original document from which this chunk was formed.\n", "        char_offset: starting character offset, 0-based, within the original\n", "            document that is represented by the chunk.\n", "        length: number of characters in the span represented by the chunk.\n", "        line_offset: starting line offset, 0-based, within the original\n", "            document that is represented by the chunk.\n", "        length_in_lines: number of lines in the span represented by the chunk.\n", "        meta: dictionary with implementation specific metadata for the chunk.\n", "    \"\"\"\n", "\n", "    id: ChunkId\n", "    text: str\n", "    parent_doc: Document\n", "    char_offset: int\n", "    length: int\n", "    line_offset: int\n", "...\n", "\n", "    def __repr__(self) -> str:\n", "        line_range = CharRange(\n", "            self.line_offset, self.line_offset + self.length_in_lines\n", "        )\n", "        return (\n", "            f\"Chunk(range={str(self.range)}, line_range={str(line_range)}, \"\n", "            f\"path={self.parent_doc.path})\"\n", "        )\n", "\n", "    def __hash__(self):\n", "        return hash(self.id)\n", "\n", "\n", "@dataclass\n", "class _FrozenChunk(Chunk):\n", "    def __post_init__(self):\n", "        self.__setattr__(\"__is_frozen\", True)\n", "\n", "    def __setattr__(self, key: str, value: Any) -> None:\n", "        if getattr(self, \"__is_frozen\", False):\n", "            raise AttributeError(\"Cannot set attributes on frozen chunk.\")\n", "        super().__setattr__(key, value)\n", "\n", "\n", "EMPTY_CHUNK = _FrozenChunk(\n", "    id=\"\",\n", "    text=\"\",\n", "    parent_doc=EMPTY_DOCUMENT,\n", "    char_offset=0,\n", "    length=0,\n", "    line_offset=0,\n", "    length_in_lines=0,\n", ")\n", "\n", "\n", "# Represent a type that is serializable to JSON,\n", "# and will be deserialized into the exact same object.\n", "JSONSerializableType = Union[\n", "    str,\n", "    int,\n", "    float,\n", "    bool,\n", "    None,\n", "    Dict[str, \"JSONSerializableType\"],\n", "    List[\"JSONSerializableType\"],\n", "]\n", "\n", "\n", "ItemT = TypeVar(\"ItemT\", covariant=True)\n", "\n", "\n", "@dataclass(frozen=True)\n", "class Scored(Generic[ItemT]):\n", "    \"\"\"A generic item with a score.\"\"\"\n", "\n", "    item: ItemT\n", "    \"\"\"The item.\"\"\"\n", "\n", "    score: float\n", "...\n", "```\n", "\n", "Here is the file `base/datasets/edit_dataset.py`:\n", "\n", "```\n", "\"\"\"Dataset for edits logged via RequestInsight.\"\"\"\n", "\n", "import logging\n", "import re\n", "from collections.abc import Iterable, Sequence\n", "from dataclasses import dataclass, field, replace\n", "from datetime import datetime\n", "from pathlib import Path\n", "from typing import Literal, Optional, TypedDict, cast\n", "\n", "from dataclasses_json import config as json_config\n", "from dataclasses_json import dataclass_json\n", "from google.cloud import bigquery, storage  # type: ignore\n", "from google.protobuf.json_format import ParseDict\n", "from marshmallow import fields\n", "from typing_extensions import assert_never\n", "\n", "from base.datasets.edit import (\n", "    EditDatum,\n", "    EditPosition,\n", "    EditRequest,\n", "    EditResolution,\n", "    EditResponse,\n", ")\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from base.datasets.gcs_blob_cache import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    CheckpointCache,\n", "    Checkpoint<PERSON><PERSON>nt,\n", "    GCSBlobCache,\n", "...\n", "```\n", "\n", "Here is the file `research/model_server/model_server_requests.py`:\n", "\n", "```\n", "\"\"\"Dataclasses representing model server requests and responses.\n", "\n", "See public_api.proto for the reference.\n", "\"\"\"\n", "\n", "from dataclasses import dataclass, field\n", "from typing import Optional, Sequence, TypedDict, Union\n", "\n", "from base.prompt_format.next_edit_location.prompt_formatter import DiffHunk\n", "from research.core.next_edit_location_prompt_input import FileLocation\n", "from research.core.types import DocumentId, Scored\n", "\n", "\n", "@dataclass\n", "class Blobs:\n", "    \"\"\"Represents a list of blobs based on a baseline version and delta.\"\"\"\n", "\n", "    checkpoint_id: Optional[str]\n", "    \"\"\"The starting blob-list version in which to apply changes.\"\"\"\n", "\n", "    added_blobs: list[str]\n", "    \"\"\"Blob names to add to the starting blob list.\"\"\"\n", "\n", "    deleted_blobs: list[str]\n", "    \"\"\"Blob names to remove from the starting blob list.\"\"\"\n", "\n", "\n", "@dataclass\n", "class TabSwitchEvent:\n", "    \"\"\"A tab switch event.\"\"\"\n", "...\n", "    metadata: list[KeyValuePair] = field(default_factory=list)\n", "    \"\"\"Blob metadata.\"\"\"\n", "\n", "\n", "@dataclass\n", "class BatchUploadRequest:\n", "    \"\"\"A request to upload a batch of blobs.\"\"\"\n", "\n", "    blobs: list[BlobPayload]\n", "    \"\"\"List of blob paths and contents.\"\"\"\n", "\n", "\n", "@dataclass\n", "class BatchUploadResponse:\n", "    \"\"\"Response to the batch upload request.\"\"\"\n", "\n", "    blob_names: list[str]\n", "    \"\"\"List of computed blob names.\"\"\"\n", "\n", "\n", "@dataclass\n", "class CodeEditRequest:\n", "    \"\"\"A request to the code edit model.\"\"\"\n", "\n", "    # As defined here https://github.com/augmentcode/augment/blob/main/clients/vscode/src/augment-api.ts#L217\n", "    # our code edit feature accepts these inputs:\n", "    #   - instruction: string;\n", "    #   - selected_code: string;\n", "    #   - prefix: string;\n", "    #   - suffix: string;\n", "...\n", "    Leaving this to <PERSON><PERSON><PERSON> will let the model decide whether there should be a\n", "    change to the selected code. Note that even if this is true, the changes are not\n", "    guaranteed since then model may still output code that contains no change.\n", "    \"\"\"\n", "\n", "    # Completion position information\n", "    blob_name: str = \"\"\n", "    \"\"\"The blob name of the current file.\"\"\"\n", "    # The offsets below are in Unicode characters relative to the contents in `blob_name`.\n", "    prefix_begin: int = 0\n", "    \"\"\"The offset where the prefix region begins.\"\"\"\n", "    selection_begin: int = 0\n", "    \"\"\"The offset where the selection region begins.\"\"\"\n", "    selection_end: int = 0\n", "    \"\"\"The offset where the selection region ends.\"\"\"\n", "    suffix_end: int = 0\n", "    \"\"\"The offset where the suffix region ends.\"\"\"\n", "\n", "\n", "@dataclass\n", "class CodeEditAnnotationRequest:\n", "    \"\"\"A request to the code edit model.\"\"\"\n", "\n", "    # See `CodeEditAnnotationPayload` defined here https://github.com/augmentcode/augment/blob/main/clients/vscode/src/augment-api.ts#L217\n", "...\n", "```\n", "\n", "Here is the file `experimental/yangguang/vscode-ui-prototype/src/lib/types.ts`:\n", "\n", "```\n", "...\n", "}\n", "\n", "export interface ChatResponse extends ResponseBase {\n", "}\n", "\n", "\n", "/**\n", " * Code edit APIs.\n", " */\n", "export interface EditRequest {\n", "    selectedCode: string;\n", "    instruction: string;\n", "    // Augment specific\n", "    prefix?: string;  // All the lines before the selected part.\n", "    suffix?: string;  // All the lines after the selected part.\n", "    // Not used for now.\n", "    fullFile?: string;\n", "    selectionRange?: Range;\n", "}\n", "\n", "export interface EditResponse extends ResponseBase {\n", "    modifiedCode: string;\n", "}\n", "\n", "\n", "/**\n", " * Edit explain APIs.\n", " */\n", "export interface ExplainRequest {\n", "    selectedCode: string;\n", "...\n", "```\n", "\n", "Here is the file `experimental/dxy/edits/notebooks/regular/explore-2023-10-12.ipynb`:\n", "\n", "```\n", "{\n", " \"cells\": [\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"%load_ext autoreload\\n\",\n", "    \"%autoreload 2\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"import pathlib\\n\",\n", "    \"import tqdm\\n\",\n", "    \"import json\\n\",\n", "    \"from augment.research.core import utils_for_dataclass, utils_for_file\\n\",\n", "    \"from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\\n\",\n", "    \"from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\\n\",\n", "    \"\\n\",\n", "    \"\\n\",\n", "    \"json_file = (\\n\",\n", "    \"    \\\"/home/<USER>/datasets/edit/organized-basic-instruct/instruct-del_01_01-per60.json\\\"\\n\",\n", "    \")\\n\",\n", "    \"\\n\",\n", "    \"with pathlib.Path(json_file).open(\\\"r\\\") as f:\\n\",\n", "    \"    for line in f.readlines():\\n\",\n", "    \"        xdict = json.loads(line)\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"import pathlib\\n\",\n", "    \"import tqdm\\n\",\n", "    \"from augment.research.core import utils_for_dataclass, utils_for_file\\n\",\n", "    \"from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\\n\",\n", "    \"from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\\n\",\n", "    \"\\n\",\n", "    \"\\n\",\n", "    \"suffixes = [\\n\",\n", "    \"    \\\"del_00_00-per60\\\",\\n\",\n", "    \"    \\\"del_01_01-per60\\\",\\n\",\n", "    \"    \\\"del_02_02-per60\\\",\\n\",\n", "    \"    \\\"del_03_03-per60\\\",\\n\",\n", "    \"    \\\"del_04_04-per60\\\",\\n\",\n", "    \"    \\\"del_05_05-per60\\\",\\n\",\n", "    \"    \\\"del_06_06-per60\\\",\\n\",\n", "    \"    \\\"del_07_07-per60\\\",\\n\",\n", "    \"    \\\"del_08_08-per60\\\",\\n\",\n", "    \"    \\\"del_09_09-per60\\\",\\n\",\n", "    \"    \\\"del_10_10-per60\\\",\\n\",\n", "...\n", "```\n", "\n", "Here is the file `clients/vscode/src/augment-api.ts`:\n", "\n", "```\n", "...\n", "    prefix?: string;\n", "    selected_text: string;\n", "    suffix?: string;\n", "    blobs?: BlobsPayload;\n", "    path?: string;\n", "    instruction: string;\n", "    lang?: string;\n", "    blob_name?: string;\n", "    prefix_begin?: number;\n", "    suffix_end?: number;\n", "    sequence_id?: number;\n", "};\n", "\n", "type ChatPayload = {\n", "    model?: string;\n", "    path?: string;\n", "    prefix?: string;\n", "    selected_code?: string;\n", "    suffix?: string;\n", "    message: string;\n", "    chat_history: Exchange[];\n", "    lang?: string;\n", "    blobs?: BlobsPayload;\n", "};\n", "\n", "type NextEditLocationPayload = {\n", "    instruction?: string;\n", "    recent_changes?: DiffHunkPayload[];\n", "    blobs?: BlobsPayload;\n", "};\n", "...\n", "```\n", "\n", "Here is the file `research/eval/harness/systems/remote_lib.py`:\n", "\n", "```\n", "\"\"\"A collection of functions for interacting with remote systems.\"\"\"\n", "\n", "import logging\n", "import os\n", "import time\n", "from dataclasses import dataclass\n", "from itertools import islice\n", "from pathlib import Path, PurePath\n", "from typing import Iterable, Iterator, Optional, Sequence, TypeVar\n", "\n", "from base.augment_client.client import (\n", "    AugmentClient,\n", "    AugmentModelClient,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    EditResponse,\n", "    GetModelsResponse,\n", "    Language,\n", "    Model,\n", "    UploadContent,\n", ")\n", "from research.core.edit_prompt_input import ResearchEditPromptInput\n", "from research.core.model_input import ModelInput\n", "from research.core.types import CharRange\n", "from research.retrieval.types import Document\n", "\n", "logger = logging.getLogger(__name__)\n", "logger.setLevel(logging.INFO)\n", "\n", "\n", "def get_model_info(\n", "...\n", "```\n", "\n", "Here is the file `experimental/dxy/edits/pipelines/__init__.py`:\n", "\n", "```\n", "\"\"\"The meta definition of the data generation pipeline.\"\"\"\n", "import abc\n", "import dataclasses\n", "import random\n", "import typing\n", "\n", "from experimental.dxy.edits.api_lib import OpenAIChatModels\n", "from research.data.synthetic_code_edit.types import CodeEditData\n", "\n", "\n", "def code_edit_data_get_key(sample: CodeEditData, key: str):\n", "    if hasattr(sample, key):\n", "        return getattr(sample, key)\n", "    elif key in sample.metadata:\n", "        return sample.metadata[key]\n", "    else:\n", "        return None\n", "\n", "\n", "@dataclasses.dataclass\n", "class Pipeline:\n", "    \"\"\"The pipeline template used to synthetically generate data.\"\"\"\n", "\n", "    DEBUG: bool = False\n", "    REQUIRED_KEYS: typing.Tuple[str, ...] = ()\n", "    SEED_EXAMPLES: typing.Tuple[CodeEditData, ...] = ()\n", "\n", "    def __post_init__(self):\n", "        seeds_by_category = {}\n", "        for seed in self.SEED_EXAMPLES:\n", "...\n", "```\n", "\n", "Here is the file `research/eval/harness/tasks/can_it_edit.py`:\n", "\n", "```\n", "...\n", "    suffix: str\n", "    \"\"\"The suffix string for the task.\"\"\"\n", "\n", "    prompt: str\n", "    \"\"\"The actual prompt used for generation.\n", "\n", "    The prefix, suffix, and chunks may be changed to form the actual prompt,\n", "    based on budget constraints.\n", "    \"\"\"\n", "\n", "    generation: str\n", "    \"\"\"The full output text from generation.\"\"\"\n", "\n", "    metrics: Scores\n", "    \"\"\"Metrics for the task.\"\"\"\n", "\n", "    exec_metrics: Optional[dict] = None\n", "    \"\"\"Additional metrics from executing the code.\"\"\"\n", "\n", "\n", "class CanItEdit(CodeInstructTask):\n", "    \"\"\"The CanItEdit Task.\"\"\"\n", "\n", "    class VariantType(Enum):\n", "        \"\"\"Variant types for the CanItEdit task.\"\"\"\n", "\n", "        DESCRIPTIVE = \"descriptive\"\n", "        LAZY = \"lazy\"\n", "\n", "    def __init__(\n", "...\n", "```\n", "\n", "Here is the file `experimental/yuri/edits/review_data_v2.ipynb`:\n", "\n", "```\n", "{\n", " \"cells\": [\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"import sys\\n\",\n", "    \"\\n\",\n", "    \"sys.path.append(\\\"/home/<USER>/repos/augment/research/gpt-neox\\\")\\n\",\n", "    \"sys.path.append(\\\"/home/<USER>/repos/augment\\\")\"\n", "   ]\n", "  },\n", "  {\n", "   \"cell_type\": \"code\",\n", "   \"execution_count\": null,\n", "   \"metadata\": {},\n", "   \"outputs\": [],\n", "   \"source\": [\n", "    \"import json\\n\",\n", "    \"import difflib\\n\",\n", "    \"\\n\",\n", "    \"from pathlib import Path\\n\",\n", "    \"from collections import defaultdict\\n\",\n", "    \"from base.datasets.edit import EditDatum\\n\",\n", "    \"from tqdm import tqdm\\n\",\n", "    \"from dataclasses import asdict\"\n", "   ]\n", "  },\n", "...\n", "```\n", "\n", "Here is the file `research/tools/patch_viewer/patch_service.py`:\n", "\n", "```\n", "\"\"\"Implements the patch service backend used by the patch viewer.\"\"\"\n", "from __future__ import annotations\n", "\n", "import ast\n", "import collections\n", "import logging\n", "import pathlib\n", "from dataclasses import dataclass, field\n", "from textwrap import dedent\n", "from typing import Mapping, MutableMapping, NewType, Optional\n", "\n", "import flask\n", "\n", "from research.core.types import Chunk\n", "from research.eval.patch_lib import Patch\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "# Schemas for the JSON messages exchanged between server and client.\n", "# IMPORTANT: these should be kept in sync with the definitions in\n", "#   `./src/PatchService.tsx`.\n", "SystemId = NewType(\"SystemId\", str)\n", "PatchId = NewType(\"PatchId\", str)\n", "\n", "\n", "@dataclass\n", "class Annotation:\n", "    \"\"\"Patch-level annotations.\n", "\n", "...\n", "```\n", "\n", "Here is the file `research/tools/export_edit_data/export_edits_to_jsonl.py`:\n", "\n", "```\n", "...\n", "    return {\n", "        \"request_id\": datum.request_id,\n", "        \"user_id\": datum.user_id,\n", "        \"event_type\": \"edit\",\n", "        \"request\": {\n", "            \"path\": datum.request.path,\n", "            \"prefix\": datum.request.prefix,\n", "            \"selected_text\": datum.request.selected_text,\n", "            \"suffix\": datum.request.suffix,\n", "            \"position\": {\n", "                \"prefix_begin\": (\n", "                    0\n", "                    if not datum.request.position\n", "                    else datum.request.position.prefix_begin\n", "                ),\n", "                \"suffix_end\": (\n", "                    0\n", "                    if not datum.request.position\n", "                    else datum.request.position.suffix_end\n", "                ),\n", "            },\n", "            \"instruction\": datum.request.instruction,\n", "            \"file_count\": len(datum.request.blob_names),\n", "        },\n", "        \"feedback\": {\n", "            \"is_accepted\": datum.resolution.is_accepted,\n", "            \"annotated_text\": datum.resolution.annotated_text,\n", "...\n", "```\n", "\n", "When answering the developer's questions, please follow these guidelines:\n", "\n", "- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.\n", "- When referencing a file in your response, always include the FULL file path.\n", "- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "where do we export code edits<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "According to the codebase, code edits are exported to a JSONL file. This is evident from the file `research/tools/export_edit_data/export_edits_to_jsonl.py`.\n", "\n", "In this file, there is a function `process_edit` that processes an `EditDatum` object and returns a dictionary. This dictionary is then written to a JSONL file.\n", "\n", "The JSONL file is a line-delimited JSON file, where each line represents a single code edit sample. This file can be used to store and export code edit data for further analysis or processing.\n", "\n", "The export process is initiated by running the `export_edits_to_jsonl.py` script, which takes care of authenticating with Google, generating the proto library files, and exporting the code edit data to a JSONL file.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "where is EditDatum defined<|eot_id|><|start_header_id|>assistant<|end_header_id|>'''\n", "\n", "elems = raw_prompt.split(\"<|eot_id|>\")\n", "print(len(elems))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def deconstruct_llama_prompt(prompt: str) -> list[dict[str, str]]:\n", "    system_prompt_header = \"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\"\n", "    user_header = \"<|start_header_id|>user<|end_header_id|>\"\n", "    assistant_header = \"<|start_header_id|>assistant<|end_header_id|>\"\n", "\n", "    assert prompt.endswith(assistant_header)\n", "    prompt = prompt[:-len(assistant_header)]\n", "\n", "    elems = raw_prompt.split(\"<|eot_id|>\")\n", "    # assert len(elems) % 2 == 0\n", "\n", "    assert elems[0].startswith(system_prompt_header)\n", "    system_prompt = elems[0].replace(system_prompt_header, \"\")\n", "    dialog = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system_prompt,\n", "        }\n", "    ]\n", "\n", "    for i, elem in enumerate(elems[1:]):\n", "        elem = elem.replace(user_header, \"\")\n", "        elem = elem.replace(assistant_header, \"\")\n", "        assert \"<|\" not in elem\n", "        if i % 2 == 0:\n", "            dialog.append({\"role\": \"user\", \"content\": elem})\n", "        else:\n", "            dialog.append({\"role\": \"assistant\", \"content\": elem})\n", "\n", "    return dialog\n", "\n", "dialog = deconstruct_llama_prompt(raw_prompt)\n", "\n", "for entry in dialog:\n", "    print(entry[\"role\"], \":\", entry[\"content\"][:100], \"...\", entry[\"content\"][-100:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}