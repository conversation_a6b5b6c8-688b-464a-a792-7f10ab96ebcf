{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Machine learning is a subfield of artificial intelligence (AI) that involves the use of algorithms and statistical models to enable machines to perform a specific task without using explicit\n"]}], "source": ["\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "address = \"trt-llm-h100-00.gcp-sing.r.augmentcode.com:8000\"\n", "\n", "client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "response = client.generate(messages=[\"What is machine learning?\"], max_tokens=32)\n", "\n", "print(response)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "tokenizer = create_tokenizer_by_name(\"llama3_instruct\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["'<|eot_id|>'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.detokenize([tokenizer.special_tokens.eos])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! It's nice to meet you. Is there something I can help you with, or would you like to chat?\n"]}], "source": ["from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "client = Llama3ChatClient(\"llama.cpp\", address=\"************:8000\", timeout=180)\n", "\n", "response = client.generate(messages=[\"hello\"], max_tokens=32)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.augment_client.client import AugmentClient, AugmentModelClient\n", "\n", "from pathlib import Path\n", "\n", "# Initialize the AugmentClient\n", "augment_api_token = (\n", "    Path(\"/home/<USER>/.config/augment/api_token\").read_text(encoding=\"utf8\").strip()\n", ")\n", "\n", "augment_client = AugmentClient(\n", "    url=\"https://staging-shard-0.api.augmentcode.com/\",\n", "    token=augment_api_token,\n", ")\n", "\n", "# Create an AugmentModelClient for a specific model\n", "# model_name = \"claude-sonnet-3-5-16k-chat\"\n", "# model_name = \"gemini-1-5-flash-001-chat\"\n", "model_name = \"binks-v12-fp16-longoutput\"\n", "model_client = augment_client.client_for_model(model_name)\n", "\n", "# Send a simple chat message\n", "import time\n", "\n", "start = time.time()\n", "response = model_client.chat(\n", "    selected_code=\"\",  # No code selected for this example\n", "    message=message,\n", "    prefix=\"\",\n", "    suffix=\"\",\n", "    path=\"\",  # No specific file path for this example\n", ")\n", "elapsed = time.time() - start\n", "\n", "# Print the response\n", "print(f\"Chat response: {response.text}\")\n", "print(f\"Request ID: {response.request_id}\")\n", "print(f\"<PERSON><PERSON> took {elapsed:.2f} seconds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "client = Llama3ChatClient(\"triton\", address=\"trt-llm-a100-00.tenant-augment-eng.coreweave.cloud:8000\", timeout=60)\n", "\n", "message = '''\\\n", "suggest a useful way to edit this code. write it as a short instruction, up to 6 words. don't use punctuation.\n", "\n", "``` \n", "def get_modified_ranges(pfile: PatchedFile) -> Iterable[LineRange]:\n", "    \"\"\"Compute the modified lines in the after version of the file.\n", "\n", "    NOTE: This function reports deleted lines as single line changes while other\n", "    functions decide to report them as zero-length changes. You have been warned.\n", "\n", "    E.g.,\n", "\n", "    @@ -1,5 +1,2 @@\n", "    0\n", "    -1\n", "    -2\n", "    -3\n", "    4\n", "\n", "    would be returned as [<PERSON><PERSON><PERSON><PERSON>(1, 2)].\n", "    \"\"\"\n", "    modified_lines = set[int]()\n", "    for hunk in pfile:\n", "        last_line = 0\n", "        for line in hunk:\n", "            if line.is_added:\n", "                # We use an appending semantics for added line, while PachedFile uses a\n", "                # prepending semantics for added line, so we need to offset the\n", "                # reported location by -1.\n", "                last_line = check_not_none(line.target_line_no)\n", "                modified_lines.add(last_line - 1)\n", "            elif line.is_removed:\n", "                # Mark the \"next\" line as the modified one.\n", "                modified_lines.add(last_line)\n", "            # target_line_no can be None if the trailing new line is removed.\n", "            elif line.target_line_no is not None:\n", "                last_line = check_not_none(line.target_line_no)\n", "    # Merge them into line ranges.\n", "    ranges = [LineRange(line, line + 1) for line in sorted(modified_lines)]\n", "    result_ranges = list[LineRange]()\n", "    for r in ranges:\n", "        if result_ranges and result_ranges[-1].adjoins(r):\n", "            result_ranges[-1] = result_ranges[-1].merge(r)\n", "        else:\n", "            result_ranges.append(r)\n", "    return result_ranges\n", "```\n", "'''\n", "\n", "import time\n", "\n", "start = time.time()\n", "response = client.generate(messages=[message], max_tokens=32)\n", "elapsed = time.time() - start\n", "\n", "print(response)\n", "\n", "print(f\"Generation took {elapsed:.2f} seconds, or {elapsed / 32:.3f} seconds/token\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "root = \"/mnt/efs/spark-data/shared/next-edit/stage0/prv2-pr_grouped_10k/repo_changes/\"\n", "\n", "# filename = \"part-09983-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet\"\n", "filename = \"part-09983-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet\"\n", "\n", "path = root + filename\n", "\n", "df = pandas.read_parquet(path)\n", "print(df.columns)"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["import pickle\n", "data = df[\"pickled_results\"].iloc[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.diff_utils import compute_repo_diff\n", "\n", "for change in pickle.loads(data):\n", "    if len(change.repo_change.changed_paths()) == 1:\n", "        print(\"=\" * 120)\n", "        print(f\"Message: {change.commit_meta.message}\")\n", "        print()\n", "        # print(change.repo_change)\n", "\n", "        before_repo = change.repo_change.before_repo()\n", "        after_repo = change.repo_change.after_repo()\n", "        diff = compute_repo_diff(before_repo, after_repo)\n", "        print(diff)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["change.repo_change.changed_paths()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from typing import Protocol, runtime_checkable\n", "\n", "@runtime_checkable\n", "class Closable(Protocol):\n", "    def close(self):\n", "        raise NotImplementedError()\n", "\n", "    @property\n", "    def closed(self) -> bool:\n", "        raise NotImplementedError()\n", "\n", "\n", "class Foo:\n", "    def foo(self):\n", "        pass\n", "\n", "    def close(self):\n", "        raise NotImplementedError()\n", "\n", "    @property\n", "    def closed(self) -> bool:\n", "        raise NotImplementedError()\n", "\n", "obj = Foo()\n", "isinstance(obj, Closable)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.tokenizers import get_tokenizer\n", "\n", "# tokenizer = get_tokenizer(\"StarCoder2Tokenizer\")\n", "tokenizer = get_tokenizer(\"llama3_base\")\n", "\n", "tokenizer.vocab[b\"hello\"]\n", "tokenizer.inv_vocab[tokenizer.vocab[b\"hello\"]]\n", "\n", "# text = \"\"\"\\\n", "# from: base/tokenizers/tiktoken_starcoder_tokenizer.py\n", "# class StarCoderSpecialTokens(\n", "#     RerankerSpecialTokens, RetrievalSpecialTokens, FimSpecialTokens\n", "# ):\n", "# attributes: skip: int = ...; pause: int = ...; retrieval_section: int = ...; ret_start: int = ...; ret_body: int = ...; prefix_body: int = ...; nearby_prefix: int = ...; nearby_suffix: int = ...; sig_lookup: int = ...; sig_begin: int = ...; sig_end: int = ...; signature_section: int = ...; far_prefix: int = ...; far_suffix: int = ...; padding: int = ...; eos: int = ...; fim_prefix: int = ...; fim_middle: int = ...; fim_suffix: int = ...; fim_pad: int = ...; filename: int = ...; gh_stars: int = ...; issue_start: int = ...; issue_comment: int = ...; issue_closed: int = ...; jupyter_start: int = ...; jupyter_text: int = ...; jupyter_code: int = ...; jupyter_output: int = ...; empty_output: int = ...; commit_before: int = ...; commit_msg: int = ...; commit_after: int = ...; reponame: int = ...; newline: int = ...; start_of_sequence: int = ...; end_of_query: int = ...; end_of_key: int = ...; instruction: int = ...; selected_code: int = ...; diff_section: int = ...; diff_hunk: int = ...; has_change: int[...]\n", "# methods: begin_sequence\n", "#     def __init__(self, tokenizer: FimTokenizer): ...\n", "\n", "# from: research/gpt-neox/megatron/tokenizer/tokenizer.py\n", "# class StarCoderBaseTokenizer(HFGPT2Tokenizer):\n", "# attributes: eod_id: Token = 0; eod_token = ...; filename_id: Token; filename_token: str; skip_id: Token = 49153; skip_token = ...; pause_id: Token = 49154; pause_token = ...; retrieval_section_id = 49155; retrieval_section_token = ...; retrieval_start_id = 49156; retrieval_start_token = ...; retrieval_body_id = 49157; retrieval_body_token = ...; prefix_body_id = 49158; prefix_body_token = ...; nearby_prefix_id = 49159; nearby_prefix_token = ...; sig_lookup_id = 49160; sig_lookup_token = ...; sig_begin_id = 49161; sig_begin_token = ...; sig_end_id = 49162; sig_end_token = ...; nearby_suffix_id = 49163; nearby_suffix_token = ...; signature_section_id = 49164; signature_section_token = ...; startofsequence_id = 49165; sos_id = 49165; startofsequence_token = ...; ret_endofquery_id = 49166; ret_endofquery_token = ...; ret_endofkey_id = 49167; ret_endofkey_token = ...; far_prefix_id = 49168; far_prefix_token = ...; far_suffix_id = 49169; far_suffix_token = ...; instruction_id = 49170; instruction_token [...]\n", "#     def __init__(self, vocab_file): ...\n", "\n", "# from research/core/prod_adapters/tokenizer_wrapper.py:\n", "# class Llama3BaseWrappedProdTokenizer(ResearchTokenizerWrapper):\n", "#     def __init__(self): ...\n", "# \"\"\"\n", "\n", "# text2 = \"\"\"\\\n", "# from: base/tokenizers/tiktoken_starcoder_tokenizer.py\n", "# class StarCoderSpecialTokens(\n", "#     RerankerSpecialTokens, RetrievalSpecialTokens, FimSpecialTokens\n", "# ):\n", "# attributes: skip: int; pause: int; retrieval_section: int; ret_start: int; ret_body: int; prefix_body: int; nearby_prefix: int; nearby_suffix: int; sig_lookup: int; sig_begin: int; sig_end: int; signature_section: int; far_prefix: int; far_suffix: int; padding: int; eos: int; fim_prefix: int; fim_middle: int; fim_suffix: int; fim_pad: int; filename: int; gh_stars: int; issue_start: int; issue_comment: int; issue_closed: int; jupyter_start: int; jupyter_text: int; jupyter_code: int; jupyter_output: int; empty_output: int; commit_before: int; commit_msg: int; commit_after: int; reponame: int; newline: int; start_of_sequence: int; end_of_query: int; end_of_key: int; instruction: int; selected_code: int; diff_section: int; diff_hunk: int; has_change: int[...]\n", "# methods: begin_sequence\n", "#     def __init__(self, tokenizer: FimTokenizer): ...\n", "\n", "# from: research/gpt-neox/megatron/tokenizer/tokenizer.py\n", "# class StarCoderBaseTokenizer(HFGPT2Tokenizer):\n", "# attributes: eod_id: Token = 0; eod_token; filename_id: Token; filename_token: str; skip_id: Token = 49153; skip_token; pause_id: Token = 49154; pause_token; retrieval_section_id = 49155; retrieval_section_token; retrieval_start_id = 49156; retrieval_start_token; retrieval_body_id = 49157; retrieval_body_token; prefix_body_id = 49158; prefix_body_token; nearby_prefix_id = 49159; nearby_prefix_token; sig_lookup_id = 49160; sig_lookup_token; sig_begin_id = 49161; sig_begin_token; sig_end_id = 49162; sig_end_token; nearby_suffix_id = 49163; nearby_suffix_token; signature_section_id = 49164; signature_section_token; startofsequence_id = 49165; sos_id = 49165; startofsequence_token; ret_endofquery_id = 49166; ret_endofquery_token; ret_endofkey_id = 49167; ret_endofkey_token; far_prefix_id = 49168; far_prefix_token; far_suffix_id = 49169; far_suffix_token; instruction_id = 49170; instruction_token [...]\n", "#     def __init__(self, vocab_file): ...\n", "\n", "# from research/core/prod_adapters/tokenizer_wrapper.py:\n", "# class Llama3BaseWrappedProdTokenizer(ResearchTokenizerWrapper):\n", "#     def __init__(self): ...\n", "# \"\"\"\n", "\n", "# len(tokenizer.tokenize_safe(text2)) - len(tokenizer.tokenize_safe(text))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import concurrent.futures\n", "import threading\n", "import time\n", "\n", "def process_file(filename):\n", "    try:\n", "        thread_index = threading.get_ident()\n", "        time.sleep(0.1)\n", "        print(f\"Processing {filename} in thread {thread_index}\")\n", "    except Exception as e:\n", "        print(e)\n", "\n", "\n", "with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:\n", "    executor.map(process_file, \"*\" * 1000)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "data = []\n", "\n", "with Path(\"/home/<USER>/augment/experimental/guy/summarization/jvector_summaries.jsonl\").open(\"r\") as f:\n", "    for line in f:\n", "        data.append(json.loads(line))\n", "\n", "for d in sorted(data, key=lambda x: x[\"prompt_token_len\"], reverse=True):\n", "    print(f\"Token len: {d['prompt_token_len']}\")\n", "    print(f\"Summary: {d['summary'].strip()}\")\n", "    print(\"\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import TypedDict\n", "\n", "class ExtraFields(TypedDict):\n", "    foo: int\n", "    bar: str\n", "\n", "d = ExtraFields(foo=1)\n", "\n", "d[\"foo\"]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.retrieval.types import (\n", "    Chunk,\n", "    <PERSON><PERSON>,\n", "    ChunkId,\n", "    Document,\n", "    DocumentId,\n", ")\n", "from research.retrieval.chunking_functions import ScopeAwareChunker\n", "\n", "text = \"\"\"\n", "def top_level_func():\n", "    pass\n", "\n", "class MyClass:\n", "    class Foo:\n", "        def foo(self):\n", "            print(\"foo\")\n", "\n", "        def bar(self):\n", "            def inner():\n", "                print(\"inner\")\n", "\n", "            print(\"bar\")\n", "\"\"\"\n", "\n", "doc = Document(\n", "    id=\"0\",\n", "    text=text,\n", "    path=\"foo.py\"\n", ")\n", "\n", "chunker = ScopeAwareChunker(max_lines_per_chunk=20, include_scope_path_in_chunk_text=True)\n", "chunks = chunker.split_into_chunks(doc)\n", "\n", "for chunk in chunks:\n", "    print(\"=\" * 80)\n", "    print(chunk.text)\n", "    print(chunk.meta)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["namespace my_namespace {\n", "\n", "    class Class {\n", "    public:\n", "        void foo() {\n", "            // ...\n", "        }\n", "    };\n", "\n", "}\n", "\n", "# class MyClass:\n", "#   class Foo:\n", "\n", "# MyClass.Foo\n", "      def foo(self):\n", "          ..."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "t = StarCoderTokenizer()\n", "tokens = [51, 2664, 4122, 544, 322, 1884, 266, 380, 6798, 1542, 7103, 446, 665, 1156, 1664, 3207, 784, 30, 1542, 44, 3678, 30, 11745, 81, 2184, 44, 26038, 2031, 711, 291, 630, 32, 1160, 280, 1542, 291, 630, 32, 19407, 81, 2184, 280, 11745, 81, 2184, 446, 665, 2454, 26, 784, 711, 291, 1524, 3013, 322, 2664, 7103, 291, 630, 32, 1160, 32, 944, 346, 446, 665, 707, 944, 26, 784, 711, 291, 1524, 42356, 322, 2664, 7103, 291, 630, 32, 1160, 32, 36476, 346, 446, 665, 4450, 26, 784, 30, 1542, 81, 1131, 44, 3678, 1612, 27, 967, 8825, 77, 466, 30, 1149, 77, 410, 614, 1149, 77, 7126, 33504, 291, 1524, 10505, 312, 13364, 7103, 291, 6717, 81, 10931, 280, 24188, 17339, 18352, 346, 291, 11745, 280, 630, 32, 1160, 32, 5536, 26, 324, 1542, 81, 1131, 30, 630, 32, 19407, 81, 2184, 30, 6717, 81, 10931, 291, 829, 291, 442, 11745, 30, 6717, 81, 10931, 32, 13232, 81, 8395, 30, 1605, 446, 665, 1245, 81, 38447, 81, 18037, 367, 26, 291, 630, 30, 1542, 81, 1131, 44, 3678, 1612, 30, 30728, 44, 596, 284, 829, 967, 5648, 77, 1808, 2786, 291, 1524, 6970, 322, 21161, 1245, 36313, 432, 322, 30728, 30, 3700, 318, 544, 322, 1542, 1509, 32, 584, 3759, 1665, 436, 322, 3469, 1135, 432, 600, 3502, 30728, 32, 291, 1524, 291, 442, 1665, 446, 665, 4233, 81, 35063, 424, 26, 784, 711, 291, 1524, 6790, 1346, 12827, 645, 322, 11566, 424, 32, 584, 1348, 438, 6364, 436, 44092, 963, 322, 1550, 5867, 645, 322, 11566, 424, 32, 291, 1524, 291, 630, 32, 1160, 32, 3815, 81, 35063, 424, 346, 446, 477, 17614, 284, 665, 645, 81]\n", "print(t.<PERSON><PERSON>(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from augment.research.retrieval.chunking_functions import ScopeAwareChunker\n", "from augment.research.core.types import Document\n", "\n", "chunker = ScopeAwareChunker(max_lines_per_chunk=40, merge_empty_chunks=False, keep_empty_chunks=True)\n", "\n", "path = \"/home/<USER>/augment/research/model_server/file_store.py\"\n", "text = Path(path).read_text(encoding=\"utf8\")\n", "doc = Document(\n", "    id=\"0\",\n", "    text=text,\n", "    path=path)\n", "chunks = chunker.split_into_chunks(doc)\n", "print(len(chunks))\n", "\n", "for chunk in sorted(chunks, key=lambda chunk: chunk.line_offset):\n", "    print(f\"line_offset={chunk.line_offset} length_in_lines={chunk.length_in_lines}\")\n", "    print(\">>>\" + chunk.text + \"<<<\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "\n", "def comma_separated_list(value: str) -> list[str]:\n", "    print(\"comma_separated_list called:\", value)\n", "    return value.split(\",\")\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--foo\", \n", "    # default=\"a,b,c\",\n", "    default=[\"a\", \"b\", \"c\"],\n", "    # default=3,\n", "    type=comma_separated_list)\n", "\n", "# args = parser.parse_args([\"--foo\", \"d,e,f\"])\n", "args = parser.parse_args([])\n", "print(args.foo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.types import IntRange\n", "\n", "\n", "IntRange(start=1, stop=2).intersect(IntRange(start=0, stop=0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from time import time\n", "import hashlib\n", "import sys\n", "entries = 200_000\n", "lookups = 50\n", "\n", "for k in range():\n", "    hashes = [hashlib.md5(bytes(str(i), encoding=\"utf8\")) for i in range(entries)]\n", "    unseen_hashes = [hashlib.md5(bytes(str(-i), encoding=\"utf8\")) for i in range(lookups)]\n", "    start = time()\n", "    big_dict = {hash_id: 1 for hash_id in hashes}\n", "    elapsed = time() - start\n", "    print(f\"took {1000*elapsed:.1f} ms for {entries} entries\")\n", "    print(f\"dict size in memory: {sys.getsizeof(big_dict)/1e6} MB\")\n", "\n", "    start = time()\n", "    for l in range(lookups):\n", "        is_in = hashes[l] in big_dict\n", "        is_in2 = unseen_hashes[l] in big_dict\n", "    elapsed = time() - start\n", "    print(f\"took {1000*elapsed:.1f} ms for {lookups*2} lookups\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "p = Path(\"/tmp/a/.augmentignore\")\n", "p.match(\"/tmp/a\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from gitignore_parser import parse_gitignore\n", "matches = parse_gitignore('/home/<USER>/augment/.augmentignore')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["matches('bazel-a/b.txt')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}