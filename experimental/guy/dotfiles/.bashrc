# ~/.bashrc: executed by bash(1) for non-login shells.
# see /usr/share/doc/bash/examples/startup-files (in the package bash-doc)
# for examples

# If not running interactively, don't do anything
case $- in
    *i*) ;;
      *) return;;
esac

EDITOR=vim

export PATH="/opt/conda/bin:$PATH"
#export PATH=${PATH}:${HOME}/.local/bin
export TOKENIZERS_PARALLELISM=false

# tiktoken
export PATH="${HOME}/.cargo/bin:$PATH"
export TIKTOKEN_DIR="megatron/tokenizer/tiktoken_lib"

alias connect=$HOME/augment/deploy/dev/dev_container/connect.sh
alias launch=$HOME/augment/deploy/dev/dev_container/launch.sh

launch_pod() { python3 $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py $*; }
lp() { python3 $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py $*; }

alias git_clone_augment="<NAME_EMAIL>:augmentcode/augment.git"
alias clone_augment="<NAME_EMAIL>:augmentcode/augment.git"
alias git_clone="<NAME_EMAIL>:augmentcode/augment.git"
alias clone="<NAME_EMAIL>:augmentcode/augment.git"
alias dev_deploy="bazel run //services/deploy:dev_deploy"

pod_create() { python3 $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py $*; }
pod_connect() { kubectl --context coreweave --namespace tenant-augment-eng exec --stdin --tty $* -- sudo su - augment; }
pod_connect_ssh() { ssh -p22022 augment@$1.tenant-augment-eng.coreweave.cloud; }
pod_destroy() { $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py --cluster CW destroy $1; }
pod_show() { kubectl get pods | egrep ^guy-; }
pod_show_full() { kubectl get pods -o wide | egrep ^guy-; }
pod_ls() { kubectl get pods | egrep ^guy-; }

# Setup a new node
setup_node() { cd ~; <NAME_EMAIL>:augmentcode/augment.git; cd augment/research; ./research-init.sh; }

fix_ssh() { eval $(ssh-agent); ssh-add ~/.ssh/augment_rsa; }

alias gs="gt submit --cli"

alias pytest="pytest -s --disable-warnings"

alias fixit="python3 $HOME/augment/experimental/guy/format_staged_files.py --add"

alias update_graphite="brew update && brew upgrade withgraphite/tap/graphite"

# sum the numbers
alias total='awk '\''{sum += $1} END {print sum}'\'''

#alias au="python3 $HOME/augment/experimental/guy/llama/augment_terminal_helper.py"
#alias au="PYTHONPATH=$HOME/augment python experimental/joel/augment.py '$@'"
#alias au="PYTHONPATH=$HOME/augment python $HOME/augment/experimental/joel/augment.py --no-input --type chat '$@'"

au() { PYTHONPATH=$HOME/augment python $HOME/augment/experimental/joel/augment.py --no-input --type chat "$*"; }

if [ -e "${HOME}/.aws.env" ]; then
    source ${HOME}/.aws.env
fi

# don't put duplicate lines or lines starting with space in the history.
# See bash(1) for more options
HISTCONTROL=ignoreboth

# append to the history file, don't overwrite it
shopt -s histappend

# for setting history length see HISTSIZE and HISTFILESIZE in bash(1)
HISTSIZE=1000
HISTFILESIZE=2000

# For GCP (and I think this supports CW as well):
export DET_MASTER=https://determined-gcp.eng.augmentcode.com
# For CW:
#export DET_MASTER=https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/

# check the window size after each command and, if necessary,
# update the values of LINES and COLUMNS.
shopt -s checkwinsize

# If set, the pattern "**" used in a pathname expansion context will
# match all files and zero or more directories and subdirectories.
#shopt -s globstar

# make less more friendly for non-text input files, see lesspipe(1)
[ -x /usr/bin/lesspipe ] && eval "$(SHELL=/bin/sh lesspipe)"

# set variable identifying the chroot you work in (used in the prompt below)
if [ -z "${debian_chroot:-}" ] && [ -r /etc/debian_chroot ]; then
    debian_chroot=$(cat /etc/debian_chroot)
fi

# set a fancy prompt (non-color, unless we know we "want" color)
case "$TERM" in
    xterm-color|*-256color) color_prompt=yes;;
esac

# uncomment for a colored prompt, if the terminal has the capability; turned
# off by default to not distract the user: the focus in a terminal window
# should be on the output of commands, not on the prompt
#force_color_prompt=yes

if [ -n "$force_color_prompt" ]; then
    if [ -x /usr/bin/tput ] && tput setaf 1 >&/dev/null; then
	# We have color support; assume it's compliant with Ecma-48
	# (ISO/IEC-6429). (Lack of such support is extremely rare, and such
	# a case would tend to support setf rather than setaf.)
	color_prompt=yes
    else
	color_prompt=
    fi
fi

if [ "$color_prompt" = yes ]; then
#    PS1='${debian_chroot:+($debian_chroot)}\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\] \w \[\033[00m\] > '

PROMPT_COMMAND=__prompt_command    # Function to generate PS1 after CMDs
__prompt_command() {
    local EXIT="$?"                # This needs to be first

    # Reset color
    local RCol='\[\e[0m\]'

    local Red='\[\e[0;31m\]'
    local Gre='\[\e[0;32m\]'
    local BYellow='\[\e[1;33m\]'
    local BBlue='\[\e[1;34m\]'
    local Purple='\[\e[0;35m\]'
    local Cyan='\[\e[0;36m\]'
    local BCyan='\[\e[1;36m\]'
    local White='\[\e[0;37m\]'
    local BWhite='\[\e[1;37m\]'

    if [ -f "/.dockerenv" ]; then
        DOCKER_PROMPT="${RCol}${Gre}[docker] "
    else
        DOCKER_PROMPT=""
    fi

    # Check if there's a stash.
    local STASH=""
    if [ "$(git stash list 2>/dev/null)" ]; then
        STASH="|${Red}stash${Purple}"
    fi

    GIT_STATUS="${Purple}[git:$(git branch 2>/dev/null | grep "^*" | awk '{print $2}')${STASH}]"

    PS1="${DOCKER_PROMPT}${RCol}${BCyan}\h ${GIT_STATUS} ${White}[${EXIT}] ${Cyan}\w ${BWhite}> ${RCol}"
    #PS1="${RCol}${BCyan}\u@\h ${White}[${EXIT}] ${Cyan}\W ${BWhite}> ${RCol}"
}
else
    PS1='${debian_chroot:+($debian_chroot)}\u@\h:\w\$ '
fi
unset color_prompt force_color_prompt

# If this is an xterm set the title to user@host:dir
case "$TERM" in
xterm*|rxvt*)
    PS1="\[\e]0;${debian_chroot:+($debian_chroot)}\u@\h: \w\a\]$PS1"
    ;;
*)
    ;;
esac


# enable color support of ls and also add handy aliases
if [ -x /usr/bin/dircolors ]; then
    test -r ~/.dircolors && eval "$(dircolors -b ~/.dircolors)" || eval "$(dircolors -b)"
    alias ls="ls --color -F -h"
    #alias ls='ls --color=auto'
    #alias dir='dir --color=auto'
    #alias vdir='vdir --color=auto'

    alias grep='grep --color=auto'
    alias fgrep='fgrep --color=auto'
    alias egrep='egrep --color=auto'
fi

# colored GCC warnings and errors
#export GCC_COLORS='error=01;31:warning=01;35:note=01;36:caret=01;32:locus=01:quote=01'

# some more ls aliases
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'

# Add an "alert" alias for long running commands.  Use like so:
#   sleep 10; alert
alias alert='notify-send --urgency=low -i "$([ $? = 0 ] && echo terminal || echo error)" "$(history|tail -n1|sed -e '\''s/^\s*[0-9]\+\s*//;s/[;&|]\s*alert$//'\'')"'

# Alias definitions.
# You may want to put all your additions into a separate file like
# ~/.bash_aliases, instead of adding them here directly.
# See /usr/share/doc/bash-doc/examples in the bash-doc package.

if [ -f ~/.bash_aliases ]; then
    . ~/.bash_aliases
fi

# enable programmable completion features (you don't need to enable
# this, if it's already enabled in /etc/bash.bashrc and /etc/profile
# sources /etc/bash.bashrc).
if ! shopt -oq posix; then
  if [ -f /usr/share/bash-completion/bash_completion ]; then
    . /usr/share/bash-completion/bash_completion
  elif [ -f /etc/bash_completion ]; then
    . /etc/bash_completion
  fi
fi

if [[ -f "$HOME/.git-completion.bash" ]]; then
. $HOME/.git-completion.bash
fi

#### Section managed by deploy/dev_container/launch.sh
if [[ -f "/activate_env.sh" ]]; then
  source /activate_env.sh
fi
#### END section managed by deploy/dev_container/launch.sh
if [[ -f "$HOME/.cargo/env" ]]; then
. "$HOME/.cargo/env"
fi

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
[ -s ~/.local/google-cloud-sdk/completion.bash.inc ] && source ~/.local/google-cloud-sdk/completion.bash.inc
[ -s ~/.local/google-cloud-sdk/path.bash.inc ] && source ~/.local/google-cloud-sdk/path.bash.inc
[ -s /home/<USER>/.linuxbrew/bin/brew ] && eval "$(/home/<USER>/.linuxbrew/bin/brew shellenv)"
###-begin-gt-completions-###
#
# yargs command completion script
#
# Installation: gt completion >> ~/.bashrc
#    or gt completion >> ~/.bash_profile on OSX.
#
_gt_yargs_completions()
{
    local cur_word args type_list

    cur_word="${COMP_WORDS[COMP_CWORD]}"
    args=("${COMP_WORDS[@]}")

    # ask yargs to generate completions.
    type_list=$(gt --get-yargs-completions "${args[@]}")

    COMPREPLY=( $(compgen -W "${type_list}" -- ${cur_word}) )

    # if no match was found, fall back to filename completion
    if [ ${#COMPREPLY[@]} -eq 0 ]; then
      COMPREPLY=()
    fi

    return 0
}
complete -o bashdefault -o default -F _gt_yargs_completions gt
###-end-gt-completions-###
