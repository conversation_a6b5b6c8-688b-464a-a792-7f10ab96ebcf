#!/bin/bash
#
# Setup dotfiles on a new CoreWeave machine.
#

mkdir -p ~/dotfiles-backup
mv ~/.bashrc ~/.gitconfig ~/.inputrc ~/.tmux.conf ~/.vimrc ~/dotfiles-backup

ln -s ${PWD}/.bashrc ~/.bashrc
ln -s ${PWD}/.gitconfig ~/.gitconfig
ln -s ${PWD}/.inputrc ~/.inputrc
ln -s ${PWD}/.tmux.conf ~/.tmux.conf
ln -s ${PWD}/.vimrc ~/.vimrc
ln -s ${PWD}/.pylintrc ~/.pylintrc
ln -s ${PWD}/.git-completion.bash ~/.git-completion.bash

#mkdir -p ~/.kube

echo "Dotfiles were setup successfully."
echo ""
echo "Files that include secrets need to be copied separately."
echo ""
echo "1. Copy SSH config from the laptop to get access to guy-dev:"
echo "scp ~/.ssh/* this-machine:.ssh/"
echo ""
echo "2. Copy secrets from guy-dev:"
echo "scp guy-dev:.s3cfg ~"
echo "scp guy-dev:.kube/* ~/.kube"
echo "scp guy-dev:.aws.env ~"
