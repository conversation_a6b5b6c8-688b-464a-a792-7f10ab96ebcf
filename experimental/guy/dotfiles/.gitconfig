[user]
	email = <EMAIL>
	name = <PERSON>
[alias]
        st = status
        amend = "!f() { git commit --amend --reuse-message=\"HEAD~1\"; }; f"
[core]
        editor = vim
[credential]
	helper = "!f() { /home/<USER>/.vscode-server/bin/695af097c7bd098fbf017ce3ac85e09bbc5dda06/node /tmp/vscode-remote-containers-649495f3-e3e3-4237-85bd-3bab033f4d3a.js git-credential-helper $*; }; f"
[filter "lfs"]
	clean = git-lfs clean -- %f
	smudge = git-lfs smudge -- %f
	process = git-lfs filter-process
	required = true
[push]
	autoSetupRemote = true
[merge]
	conflictStyle = diff3
