bind-key -n C-Right next-window
bind-key -n C-Left previous-window

set -g default-terminal "screen-256color"

set -g status-bg black
set -g status-fg white

set-option -g history-limit 100000

set-option -g renumber-windows on

# Set the status bar to display the windows
#set -g status-left ""

# Define the style for inactive windows
set -g window-status-style bg=default,fg=white

# Define the style for the currently active window
set -g window-status-current-style bg=blue,fg=black

# Customize the window status format
set -g window-status-format "#I:#W"
set -g window-status-current-format "#I:#W"
