import nested_pb2
import signature_pb2
import time
import numpy as np
import json

from google.protobuf.json_format import MessageToDict


def set_random_symbol_name_usage(name_usage: signature_pb2.SymbolNameUsage):
    name_usage.name = "foo"
    name_usage.use_site.start = np.random.randint(1000)
    name_usage.use_site.stop = np.random.randint(1000)
    name_usage.kind = "kind"
    name_usage.def_range.path.value = "path"
    name_usage.def_range.crange.start = np.random.randint(1000)
    name_usage.def_range.crange.stop = np.random.randint(1000)
    name_usage.has_def_range = True


def set_random_symbol_signature(msg: signature_pb2.SymbolSignature):
    msg.text = "hello"
    msg.path.value = "path.txt"
    msg.crange.start = np.random.randint(1000)
    msg.crange.stop = np.random.randint(1000)
    msg.lrange.start = np.random.randint(1000)
    msg.lrange.stop = np.random.randint(1000)


def set_random_symbol_definition_id(msg: signature_pb2.SymbolDefinitionId):
    msg.path.value = "path.txt"
    msg.full_crange.start = np.random.randint(1000)
    msg.full_crange.stop = np.random.randint(1000)
    msg.name = "the symbol name"


def set_random_symbol_definition(msg: signature_pb2.SymbolDefinition):
    msg.name = "foo"
    msg.path.value = "path.txt"
    msg.full_crange.start = np.random.randint(1000)
    msg.full_crange.stop = np.random.randint(1000)
    msg.name_crange.start = np.random.randint(1000)
    msg.name_crange.stop = np.random.randint(1000)
    msg.prefix_crange.start = np.random.randint(1000)
    msg.prefix_crange.stop = np.random.randint(1000)
    msg.in_class = False
    set_random_symbol_definition_id(msg.parent_id)
    msg.has_parent = True
    msg.kind = "kind"
    msg.variable_summary = "summary of the variable"


def set_random_var_occurence(msg: signature_pb2.VarOccurrence):
    msg.name = "foo"
    for _ in range(np.random.randint(10)):
        rng = msg.ranges.add()
        rng.start = np.random.randint(1000)
        rng.stop = np.random.randint(1000)


def signature_protos():
    serialized_msgs = []
    json_serialized_msgs = []
    n = 1000

    # Serialize
    start = time.time()
    for _ in range(n):
        msg = signature_pb2.FileSummaryWithSignatures()

        ### summary
        msg.summary.lang = "python"
        msg.summary.path.value = "foo.py"
        msg.summary.size_chars = np.random.randint(1000)
        msg.summary.size_lines = np.random.randint(1000)

        for _ in range(np.random.randint(100)):
            symb: signature_pb2.SymbolDefinition = msg.summary.definitions.add()
            set_random_symbol_definition(symb)

        # LocalUsageAnalysis local_analysis = 6;
        for _ in range(np.random.randint(100)):
            var_occ: signature_pb2.VarOccurrence = msg.summary.local_analysis.var_occurrences.add()
            set_random_var_occurence(var_occ)

            name_usage: signature_pb2.SymbolNameUsage = msg.summary.local_analysis.name_usages.add()
            set_random_symbol_name_usage(name_usage)

        # TODO  ScopeTreeStructure scope_structure = 7;

        ### signature_info
        set_random_symbol_signature(msg.signature_info.module_signature)
        for _ in range(np.random.randint(100)):
            symb_def_id = msg.signature_info.symbol_signatures_keys.add()
            set_random_symbol_definition_id(symb_def_id)

            symb_sig = msg.signature_info.symbol_signatures_values.add()
            set_random_symbol_signature(symb_sig)

        ### serialize
        serialized_data = msg.SerializeToString()
        serialized_msgs.append(serialized_data)

        # convert protobufs to dict
        json_serialized_msgs.append(json.dumps(MessageToDict(msg)))
        # print(json_serialized_msgs[0])
        # print(len(json_serialized_msgs[0]))
        # import sys; sys.exit(0)

    elapsed = time.time() - start
    print(f"Elapsed serialization: {elapsed*1000:.1f} msec total, or {elapsed*1000/n:.3f} msec/sample")

    # Deserialize protobuf
    assert len(serialized_msgs) == n
    start = time.time()
    for serialized_data in serialized_msgs:
        msg = signature_pb2.FileSummaryWithSignatures()
        msg.ParseFromString(serialized_data)

    elapsed = time.time() - start
    print(f"Elapsed deserialization (protobuf): {elapsed*1000:.1f} msec total, or {elapsed*1000/n:.3f} msec/sample")

    # Deserialize json
    assert len(json_serialized_msgs) == n
    start = time.time()
    for serialized_data in json_serialized_msgs:
        msg = json.loads(serialized_data)

    elapsed = time.time() - start
    print(f"Elapsed deserialization (JSON): {elapsed*1000:.1f} msec total, or {elapsed*1000/n:.3f} msec/sample")



def toy_protos():
    serialized_msgs = []
    json_serialized_msgs = []
    n = 10000

    # Serialize
    start = time.time()
    for _ in range(n):
        level1 = nested_pb2.Level1()
        level1.name = "Top Level"
        level1.id = 1
        level1.level2.is_enabled = True
        level1.level2.tags.extend([f"tag{i}" for i in range(1000)])
        level1.level2.level3.status = nested_pb2.ACTIVE
        level1.level2.level3.numbers.extend([np.random.randint(1000) for _ in range(100)])
        level1.level2.level3.level4.description = "A complex structure"

        for _ in range(10):
            level5_instance = level1.level2.level3.level4.level5s.add()
            level5_instance.score = np.random.random()
            level6_instance = level5_instance.level6s.add()
            for _ in range(10):
                level6_instance.properties.append(f"value{np.random.randint(100)}")
            level6_instance.value = "Deep Value"

        serialized_data = level1.SerializeToString()
        serialized_msgs.append(serialized_data)

        # convert protobufs to dict
        json_serialized_msgs.append(json.dumps(MessageToDict(level1)))
        # print(json_serialized_msgs[0])
        # import sys; sys.exit(0)

    elapsed = time.time() - start
    print(f"Elapsed serialization: {elapsed*1000:.1f} msec total, or {elapsed*1000/n:.3f} msec/sample")

    # Deserialize protobuf
    assert len(serialized_msgs) == n
    start = time.time()
    for serialized_data in serialized_msgs:
        msg = nested_pb2.Level1()
        msg.ParseFromString(serialized_data)

    elapsed = time.time() - start
    print(f"Elapsed deserialization (protobuf): {elapsed*1000:.1f} msec total, or {elapsed*1000/n:.3f} msec/sample")

    # Deserialize json
    assert len(json_serialized_msgs) == n
    start = time.time()
    for serialized_data in json_serialized_msgs:
        msg = json.loads(serialized_data)

    elapsed = time.time() - start
    print(f"Elapsed deserialization (JSON): {elapsed*1000:.1f} msec total, or {elapsed*1000/n:.3f} msec/sample")


signature_protos()
# toy_protos()
