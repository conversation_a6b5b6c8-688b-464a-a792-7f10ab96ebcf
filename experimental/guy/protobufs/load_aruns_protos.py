import signature_pb2
import time
from pathlib import Path

start = time.time()
paths = list(Path("/tmp/arun_protos").glob("*.bin"))
for path in paths:
    data = path.read_bytes()
    msg = signature_pb2.FileSummaryWithSignatures()
    msg.ParseFromString(data)
    # print(path)
elapsed = time.time() - start
print(f"Took {elapsed*1000:.1f} msec total, {elapsed/len(paths)*1000:.1f} msec/sample for {len(paths)} samples")
