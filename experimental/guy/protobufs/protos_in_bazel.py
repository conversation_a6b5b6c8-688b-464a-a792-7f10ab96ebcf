import time
from pathlib import Path
import multiprocessing

from base.static_analysis import (
    proto_wrapper,
    signature_pb2,
)


def load_proto(content):
    """Returns the elapsed time in msec."""
    start = time.time()
    pb = signature_pb2.FileSummaryWithSignatures()
    pb.ParseFromString(content)
    obj = proto_wrapper.from_proto(pb)  # type: ignore
    elapsed = time.time() - start
    # print(type(obj))
    # print(obj.summary.path)

    # msg = signature_pb2.FileSummaryWithSignatures()
    # msg.ParseFromString(content)

    # Must return the object itself, otherwise the timing is not accurate
    # (should include pickle/unpickle)
    return obj, elapsed * 1000


def main():
    sample_times_msec = []
    paths = list(Path("experimental/guy/protobufs/arun_protos").glob("*.bin"))
    reps = 2
    with_multiprocessing = True

    all_contents = [path.read_bytes() for path in paths]

    start = time.time()

    if with_multiprocessing:
        for _ in range(reps):
            with multiprocessing.Pool(16) as pool:
                results = pool.map(load_proto, all_contents)
                sample_times_msec.extend([result[1] for result in results])
    else:
        for _ in range(reps):
            for content in all_contents:
                sample_start = time.time()
                load_proto(content)
                sample_elapsed = time.time() - sample_start
                sample_times_msec.append(sample_elapsed * 1000)

    elapsed = time.time() - start
    total_samples = len(paths) * reps
    print("With multiprocessing:", with_multiprocessing)
    print(f"Took {elapsed*1000:.1f} msec total, {elapsed/total_samples*1000:.1f} msec/sample for {total_samples} samples")

    print(f"Max sample time: {max(sample_times_msec):.1f} msec")

    # for sample_time in sample_times_msec:
    #     print(f"{sample_time:.1f}")


if __name__ == "__main__":
    main()
