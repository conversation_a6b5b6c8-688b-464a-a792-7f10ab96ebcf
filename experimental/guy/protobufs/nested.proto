syntax = "proto3";

enum Status {
  UNKNOWN = 0;
  ACTIVE = 1;
  INACTIVE = 2;
}

message Level1 {
  string name = 1;
  int32 id = 2;
  Level2 level2 = 3;
}

message Level2 {
  bool is_enabled = 1;
  repeated string tags = 2;
  Level3 level3 = 3;
}

message Level3 {
  Status status = 1;
  repeated int32 numbers = 2;
  Level4 level4 = 3;
}

message Level4 {
  string description = 1;
  repeated Level5 level5s = 2;
}

message Level5 {
  double score = 1;
  repeated Level6 level6s = 2;
}

message Level6 {
  repeated string properties = 1; // Adjusting for compatibility: Using a list of strings instead of a map
  string value = 2;
}
