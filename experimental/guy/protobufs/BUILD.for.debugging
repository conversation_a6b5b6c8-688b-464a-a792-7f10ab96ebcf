#
# This is a debugging build file that requires visibility to //services.
# To use it, add the following to the handler target in /services/signature_search_host/server/BUILD:
#
# +    visibility = ["//experimental:__subpackages__"],
#

load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "protos_in_bazel",
    srcs = [
        "protos_in_bazel.py",
    ],
    data = glob(["arun_protos/*.bin"]),
    deps = [
        "//services/signature_search_host/server:handler",
    ],
)
