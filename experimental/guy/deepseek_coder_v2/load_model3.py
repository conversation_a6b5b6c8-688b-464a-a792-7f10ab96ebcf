from accelerate import init_empty_weights
from accelerate import load_checkpoint_and_dispatch
from transformers import AutoConfig, AutoModelForCausalLM
from accelerate.big_modeling import get_balanced_memory
import torch
from accelerate.utils.modeling import infer_auto_device_map
import json

checkpoint = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct/"
config = AutoConfig.from_pretrained(checkpoint, trust_remote_code=True)

print(config)

with init_empty_weights():
    model = AutoModelForCausalLM.from_config(config, trust_remote_code=True)

print("init model done")

device_map = "auto"
max_memory = None
no_split_module_classes = None
dtype = torch.bfloat16

# max_memory = get_balanced_memory(
#    model,
#    max_memory=max_memory,
#    no_split_module_classes=no_split_module_classes,
#    dtype=dtype,
#    low_zero=(device_map == "balanced_low_0"),
# )
#
# print("max_memory:")
# print(max_memory)

# max_memory = {0: 59270249512, 1: 59270249512, 2: 59270249512, 3: 59270249512, 4: 59270249512, 5: 59270249512, 6: 59270249512, 7: 84386119680, 'cpu': 2115035009024}
# max_memory = {0: 59270249512, 1: 59270249512, 2: 59270249512, 3: 59270249512, 4: 59270249512, 5: 59270249512, 6: 59270249512, 7: 59270249512, 'cpu': 2115035009024}

max_memory = {gpu_index: "70GB" for gpu_index in range(8)}
max_memory["cpu"] = "1500GB"

device_map = infer_auto_device_map(
    model=model,
    max_memory=max_memory,
    no_split_module_classes=None,
    dtype=torch.bfloat16,
    # verbose: bool = False,
)

print(json.dumps(device_map, indent=4))

# model = load_checkpoint_and_dispatch(
#    model, checkpoint, device_map=device_map, offload_buffers=True, no_split_module_classes=no_split_module_classes, max_memory=max_memory, dtype=dtype
# )

# print("loading done")
