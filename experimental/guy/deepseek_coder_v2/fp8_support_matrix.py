import torch

cuda = "cuda:0"
dtype = torch.float8_e4m3fn
# dtype = torch.float8_e5m2
print("> torch.__version__:", torch.__version__)

print("> torch.empty")
try:
    print("✔ cpu: ", torch.empty((3,), dtype=dtype))
except Exception as e:
    print("✘ cpu: ", e)
try:
    print("✔ cuda:", torch.empty((3,), dtype=dtype, device=cuda))
except Exception as e:
    print("✘ cuda:", e)

print("> torch.zeros")
try:
    print("✔ cpu: ", torch.zeros((3,), dtype=dtype))
except Exception as e:
    print("✘ cpu: ", e)
try:
    print("✔ cuda:", torch.zeros((3,), dtype=dtype, device=cuda))
except Exception as e:
    print("✘ cuda:", e)

print("> torch.ones")
try:
    print("✔ cpu: ", torch.ones((3,), dtype=dtype))
except Exception as e:
    print("✘ cpu: ", e)
try:
    print("✔ cuda:", torch.ones((3,), dtype=dtype, device=cuda))
except Exception as e:
    print("✘ cuda:", e)

print("> torch.zeros_like")
t = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", torch.zeros_like(t))
except Exception as e:
    print("✘ cpu: ", e)
t = t.to(cuda)
try:
    print("✔ cuda:", torch.zeros_like(t))
except Exception as e:
    print("✘ cuda:", e)

print("> torch.ones_like")
t = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", torch.ones_like(t))
except Exception as e:
    print("✘ cpu: ", e)
t = t.to(cuda)
try:
    print("✔ cuda:", torch.ones_like(t))
except Exception as e:
    print("✘ cuda:", e)

print("> t1 + t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", t1 + t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1 + t2)
except Exception as e:
    print("✘ cuda:", e)

print("> t1 - t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", t1 - t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1 - t2)
except Exception as e:
    print("✘ cuda:", e)

print("> t1 * t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", t1 * t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1 * t2)
except Exception as e:
    print("✘ cuda:", e)

print("> t1 / t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", t1 / t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1 / t2)
except Exception as e:
    print("✘ cuda:", e)

print("> t1 ** t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.tensor(2, dtype=dtype)
try:
    print("✔ cpu: ", t1**t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1**t2)
except Exception as e:
    print("✘ cuda:", e)

print("> t1 // t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.tensor(2, dtype=dtype)
try:
    print("✔ cpu: ", t1 // t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1 // t2)
except Exception as e:
    print("✘ cuda:", e)

print("> t1 % t2")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.tensor(2, dtype=dtype)
try:
    print("✔ cpu: ", t1 % t2)
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", t1 % t2)
except Exception as e:
    print("✘ cuda:", e)

print("> torch.cat([t1, t2], dim=0)")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.empty((2,), dtype=dtype)
try:
    print("✔ cpu: ", torch.cat([t1, t2], dim=0))
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", torch.cat([t1, t2], dim=0))
except Exception as e:
    print("✘ cuda:", e)

print("> torch.stack([t1, t2], dim=0)")
t1 = torch.empty((3,), dtype=dtype)
t2 = torch.empty((3,), dtype=dtype)
try:
    print("✔ cpu: ", torch.stack([t1, t2], dim=0))
except Exception as e:
    print("✘ cpu: ", e)
t1, t2 = t1.to(cuda), t2.to(cuda)
try:
    print("✔ cuda:", torch.stack([t1, t2], dim=0))
except Exception as e:
    print("✘ cuda:", e)
