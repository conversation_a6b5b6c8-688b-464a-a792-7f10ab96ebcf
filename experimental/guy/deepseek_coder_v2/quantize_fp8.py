from datasets import load_dataset
from transformers import AutoModelForCausalLM, AutoTokenizer
from auto_fp8 import AutoFP8ForCausalLM, BaseQuantizeConfig

# pretrained_model_dir = '/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct'
# quantized_model_dir = '/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct-FP8'
##max_memory = {gpu_index: "4GB" for gpu_index in range(8)}
# max_memory = {gpu_index: "40GB" for gpu_index in range(8)}
# max_memory["cpu"] = "1500GB"

pretrained_model_dir = (
    "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct"
)
quantized_model_dir = (
    "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct-FP8"
)
max_memory = {gpu_index: "79GB" for gpu_index in range(8)}
max_memory["cpu"] = "1500GB"

tokenizer = AutoTokenizer.from_pretrained(pretrained_model_dir, use_fast=True)
tokenizer.pad_token = tokenizer.eos_token

# Load and tokenize 512 dataset samples for calibration of activation scales
ds = load_dataset("mgoin/ultrachat_2k", split="train_sft").select(range(512))
examples = [
    tokenizer.apply_chat_template(batch["messages"], tokenize=False) for batch in ds
]
examples = tokenizer(examples, padding=True, truncation=True, return_tensors="pt").to(
    "cuda"
)

# Define quantization config with static activation scales
quantize_config = BaseQuantizeConfig(quant_method="fp8", activation_scheme="static")

# tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True)
# model = AutoModelForCausalLM.from_pretrained(path, trust_remote_code=True, torch_dtype=torch.bfloat16, device_map="auto", max_memory=max_memory)

# model = AutoModelForCausalLM.from_pretrained(pretrained_model_dir, trust_remote_code=True, device_map=device_map)
# for name, module in model.named_modules():
#    print(name)


# Load the model, quantize, and save checkpoint
# model = AutoFP8ForCausalLM.from_pretrained(pretrained_model_dir, quantize_config, trust_remote_code=True, device_map="auto", max_memory=max_memory)
model = AutoFP8ForCausalLM.from_pretrained(
    pretrained_model_dir,
    quantize_config,
    trust_remote_code=True,
    device_map="sequential",
    max_memory=max_memory,
)
# model = AutoFP8ForCausalLM.from_pretrained(pretrained_model_dir, quantize_config, trust_remote_code=True, device_map="cpu")

model.quantize(examples)
model.save_quantized(quantized_model_dir)
