from accelerate import init_empty_weights
from accelerate import load_checkpoint_and_dispatch
from transformers import AutoConfig, AutoModelForCausalLM
from accelerate.big_modeling import get_balanced_memory
import torch
from accelerate.utils.modeling import infer_auto_device_map
import json

checkpoint = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct-FP8/"

# max_memory = {gpu_index: "79GB" for gpu_index in range(8)}
# max_memory["cpu"] = "1500GB"

model = AutoModelForCausalLM.from_pretrained(
    checkpoint, trust_remote_code=True, device_map="auto"
)
input_text = "Quicksort is"
inputs = tokenizer(input_text, return_tensors="pt").to(model.device)
outputs = model.generate(**inputs, max_length=128)
print(tokenizer.decode(outputs[0], skip_special_tokens=True))
