#!/bin/bash

set -e

    #--model facebook/opt-125m \

export CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7

cd ~/vllm

#MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct
MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct-FP8
#MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct
#MODEL=/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct-AutoFP8

python -m vllm.entrypoints.openai.api_server \
    --model $MODEL \
    --trust-remote-code \
    --tensor-parallel-size 8 \
    --max-model-len 512 \
    --quantization fp8 \
    --enforce-eager
