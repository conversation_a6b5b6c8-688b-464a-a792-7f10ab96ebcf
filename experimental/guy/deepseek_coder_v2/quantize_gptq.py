from transformers import AutoModelForCausalLM, AutoTokenizer
from optimum.gptq import GPTQQuantizer, load_quantized_model
import torch

# model_path = '/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct'
# quant_path = '/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct-GPTQ'

model_path = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct"
quant_path = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct-GPTQ"

max_memory = {gpu_index: "79GB" for gpu_index in range(8)}
max_memory["cpu"] = "1500GB"

tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.bfloat16,
    trust_remote_code=True,
    max_memory=max_memory,
    device_map="sequential",
)

# for name, module in model.named_modules():
#    print(name)

quantizer = GPTQQuantizer(
    bits=4, dataset="c4", block_name_to_quantize="model.layers", model_seqlen=1024
)
quantized_model = quantizer.quantize_model(model, tokenizer)

# TODO(guy) why model and not quantized_model?
quantizer.save(model, quant_path)
tokenizer.save_pretrained(quant_path)
