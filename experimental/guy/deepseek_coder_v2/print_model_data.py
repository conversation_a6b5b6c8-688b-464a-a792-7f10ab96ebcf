from safetensors import safe_open
import os
import re
import json


def print_sharded_model_structure(directory):
    # List all safetensors files in the directory
    files = [f for f in os.listdir(directory) if f.endswith(".safetensors")]
    files.sort()  # Sort files to maintain the order, if necessary

    # Aggregate and print model information from each shard
    device_map = {}
    for file in files:
        shard_path = os.path.join(directory, file)

        print(f"Shard: {file}")
        with safe_open(shard_path, framework="pt", device="cpu") as f:
            for key in f.keys():
                tensor = f.get_tensor(key)
                print(f"  Layer: {key}, Shape: {tensor.shape}, Dtype: {tensor.dtype}")

                if key.startswith("model.layers."):
                    m = re.match(r"model\.layers\.(\d+)", key)
                    layer = int(m.group(1))
                    device = layer // 8
                else:
                    device = 0
                device_map[key] = device
                json.dump(device_map, open("device_map.txt", "w"), indent=2)
        print()


# Path to the directory containing your sharded .safetensors files
directory_path = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct/"
print_sharded_model_structure(directory_path)
