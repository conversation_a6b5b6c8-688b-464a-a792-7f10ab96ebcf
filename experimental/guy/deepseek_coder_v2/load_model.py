from transformers import AutoModelForCausalLM, AutoTokenizer

# pretrained_model_dir = '/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct'
# quantized_model_dir = '/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct-FP8'

pretrained_model_dir = (
    "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct"
)
quantized_model_dir = (
    "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct-FP8"
)

tokenizer = AutoTokenizer.from_pretrained(pretrained_model_dir, use_fast=True)
tokenizer.pad_token = tokenizer.eos_token

# import json
# device_map = json.load(open("device_map_cpu.txt", "r"))

model = AutoModelForCausalLM.from_pretrained(
    pretrained_model_dir, trust_remote_code=True, device_map="auto"
)
print("Loaded")
# for name, module in model.named_modules():
#    print(name)
