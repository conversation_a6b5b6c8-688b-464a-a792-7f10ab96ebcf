from transformers import AutoTokenizer, AutoModelForCausalLM
import torch

# path = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct/"
path = "/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Instruct/"

max_memory = {gpu_index: "70GB" for gpu_index in range(8)}
max_memory["cpu"] = "200GB"

# max_memory = {0: per_gpu_memory, 1: 59270249512, 2: 59270249512, 3: 59270249512, 4: 59270249512, 5: 59270249512, 6: 59270249512, 7: 59270249512, 'cpu': 2115035009024}
# max_memory = {0: 59270249512, 1: 59270249512, 2: 59270249512, 3: 59270249512, 4: 59270249512, 5: 59270249512, 6: 59270249512, 7: 59270249512, 'cpu': 2115035009024}

tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(
    path,
    trust_remote_code=True,
    torch_dtype=torch.bfloat16,
    device_map="auto",
    max_memory=max_memory,
)
input_text = "#write a quick sort algorithm"
inputs = tokenizer(input_text, return_tensors="pt").to(model.device)
outputs = model.generate(**inputs, max_length=128)
print(tokenizer.decode(outputs[0], skip_special_tokens=True))
