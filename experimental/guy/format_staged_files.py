"""Run black + isort on staged python files."""

import argparse
import subprocess
from pathlib import Path


def fix_research_imports(file: Path) -> int:
    """Fix any incorrect research imports in the given file.

    Return the number of fixed imports, or -1 if none.
    """
    n_fixed = 0

    def fix_line(line: str):
        nonlocal n_fixed
        if line.startswith("import research."):
            n_fixed += 1
            return "import research." + line[len("import research.") :]
        if line.startswith("from research."):
            n_fixed += 1
            return "from research." + line[len("from research.") :]
        return line

    assert file.suffix == ".py"
    old_code = file.read_text()
    lines = old_code.splitlines(keepends=True)
    new_code = "".join(fix_line(line) for line in lines)
    if new_code != old_code:
        file.write_text(new_code)
        return n_fixed
    return n_fixed


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--add",
        action="store_true",
        help="add files after black and isort have run",
    )
    args = parser.parse_args()

    # Get the root directory of the git repository
    git_root = Path(
        subprocess.check_output(
            ["git", "rev-parse", "--show-toplevel"], text=True
        ).strip()
    )

    # Use git to get a list of staged python files
    git_staged_output = subprocess.check_output(
        ["git", "diff", "--name-only", "--staged"], text=True
    )

    # Filter only .py files that have been added or modified, prefix with git root
    staged_files = [
        str(git_root / line)
        for line in git_staged_output.splitlines()
        if line.endswith(".py")
    ]

    # If there are staged python files, then run black and isort on them
    if staged_files:
        import_fixed = list[tuple[Path, int]]()
        for file in staged_files:
            if n_fixed := fix_research_imports(Path(file)):
                import_fixed.append((Path(file), n_fixed))
        print("Fixed research imports for the following files:")
        for file, n_fixed in import_fixed:
            print(f"\t{file} ({n_fixed} imports)")

        print("Running black and isort on the following files:")
        for file in staged_files:
            print(f"\t{file}")
        # subprocess.run(["black"] + staged_files, check=True)
        # subprocess.run(["isort"] + staged_files, check=True)
        subprocess.run(["ruff", "check", "--fix"] + staged_files, check=True)
        subprocess.run(["ruff", "format"] + staged_files, check=True)

        if args.add:
            print("Adding the formatted files to git")
            subprocess.run(["git", "add"] + staged_files, check=True)

        print("Done!")
    else:
        print("No Python files have been staged.")


if __name__ == "__main__":
    main()
