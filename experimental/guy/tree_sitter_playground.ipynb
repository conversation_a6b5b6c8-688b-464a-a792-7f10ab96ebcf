{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import tree_sitter as ts\n", "from tree_sitter_language_pack import get_parser\n", "\n", "parser = get_parser(\"python\")  # type: ignore\n", "\n", "tree = parser.parse(\n", "    bytes(\n", "        \"\"\"\n", "def foo():\n", "    if bar:\n", "        baz()\n", "\"\"\",\n", "        \"utf8\",\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["<Node type=function_definition, start_point=(1, 0), end_point=(3, 13)>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["tree.root_node.children[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}