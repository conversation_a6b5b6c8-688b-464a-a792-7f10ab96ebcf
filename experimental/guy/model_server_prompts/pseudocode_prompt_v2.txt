# Here we write succinct, type-safe code that is easy to read and understand.
# Code is short and to the point, with no unneeded comments and explanations.
#
# We do explicit error handling for some subtle errors that would be difficult
# to debug, but for the most part we rely on the compiler or interpreter to
# raise exceptions when needed.
#
# In the examples below, we convert mixed pseudocode to 100% valid Python code.
# After every example, we only output the converted pseudocode. We don't output
# the original code surrounding the pseudocode.

# [[[Example with pseudocode]]]

def sum_odds(arr):
    """Sum all odd numbers in an array."""
    total = 0
    for x in arr:
# start pseudocode
        if x is odd then add it to total
# end pseudocode
    return total

# [[[Example converted to 100% valid Python code]]]

if x % 2 == 1:
    total += x

# [[[Example with pseudocode]]]

def factorial(n):
# start pseudocode
    1. if n is 0 then return 1
    2. otherwise return n * factorial(n - 1)
# end pseudocode

# [[[Example converted to 100% valid Python code]]]

# start converted code
    if n == 0:
        return 1
    return n * factorial(n - 1)
# end converted code

# [[[Example with pseudocode]]]

import matplotlib.pyplot as plt
import pandas

df = pandas.read_json("data/data.jsonl", lines=True)

def plot_data(df):
# start pseudocode
    1. create a figure with two subplots side by side
    2. on the left, plot "age" vs. "height"
    3. on the right, make a histogram of "age"
# end pseudocode

# [[[Example converted to 100% valid Python code]]]

# start converted code
    fig, (ax1, ax2) = plt.subplots(1, 2)
    ax1.scatter(df["age"], df["height"])
    ax2.hist(df["age"])
# end converted code

# [[[Example with pseudocode]]]

    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--max_generated_tokens",
        type=int,
        default=32,
        help="max number of tokens to generate per request",
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.2,
        help="sampling temperature",
    )
# start pseudocode
    add an optional string argument for a prompt file
    string argument for a model checkpoint path (required)
# end pseudocode
    args = parser.parse_args()

# [[[Example converted 100% valid Python code]]]

# start converted code
    parser.add_argument(
        "--prompt_file",
        type=str,
        default=None,
        help="path to a file containing a prompt to start the generation",
    )
    parser.add_argument(
        "--model_checkpoint",
        type=str,
        required=True,
        help="path to a model checkpoint",
    )
# end converted code
