"""<PERSON><PERSON><PERSON> to analyze CLI usage from BigQuery data.

This script analyzes CLI usage patterns by:
1. Finding all tenants that used CLI in the past week
2. Counting total requests per tenant, grouped by request type
3. Displaying the results in a readable format
"""

import argparse
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Tuple
import pandas as pd
from google.cloud import bigquery
from pathlib import Path

from base.datasets.gcp_creds import get_gcp_creds


def get_cli_tenants(
    client: bigquery.Client, start_date: datetime, end_date: datetime
) -> List[str]:
    """Get list of tenants that used CLI in the date range."""

    query = """
    SELECT DISTINCT tenant
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
    WHERE TIMESTAMP_TRUNC(time, DAY) >= TIMESTAMP(@start_date)
    AND TIMESTAMP_TRUNC(time, DAY) <= TIMESTAMP(@end_date)
    AND CONTAINS_SUBSTR(user_agent, "cli/")
    AND NOT STARTS_WITH(tenant, "aitutor-")
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "TIMESTAMP", start_date),
            bigquery.ScalarQueryParameter("end_date", "TIMESTAMP", end_date),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df["tenant"].tolist()


def get_all_usage_data_for_cli_tenants(
    client: bigquery.Client,
    start_date: datetime,
    end_date: datetime,
    cli_tenants: List[str],
) -> pd.DataFrame:
    """Query BigQuery for ALL usage data for tenants that use CLI."""

    # Convert tenant list to SQL format
    tenant_list = "', '".join(cli_tenants)

    query = f"""
    SELECT
        tenant,
        request_type,
        SPLIT(user_agent, '/')[OFFSET(0)] as user_agent_base,
        COUNT(*) as request_count,
        COUNT(DISTINCT opaque_user_id) as user_count
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
    WHERE TIMESTAMP_TRUNC(time, DAY) >= TIMESTAMP(@start_date)
    AND TIMESTAMP_TRUNC(time, DAY) <= TIMESTAMP(@end_date)
    AND tenant IN ('{tenant_list}')
    AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
    AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
    GROUP BY tenant, request_type, user_agent_base
    ORDER BY tenant, request_count DESC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "TIMESTAMP", start_date),
            bigquery.ScalarQueryParameter("end_date", "TIMESTAMP", end_date),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df


def get_tenants_summary(
    client: bigquery.Client,
    start_date: datetime,
    end_date: datetime,
    cli_tenants: List[str],
) -> pd.DataFrame:
    """Get summary of ALL usage by CLI tenants."""

    # Convert tenant list to SQL format
    tenant_list = "', '".join(cli_tenants)

    query = f"""
    SELECT
        tenant,
        COUNT(*) as total_requests,
        COUNT(DISTINCT request_type) as unique_request_types,
        COUNT(DISTINCT DATE(time)) as active_days,
        SUM(CASE WHEN CONTAINS_SUBSTR(user_agent, "cli/") THEN 1 ELSE 0 END) as cli_requests,
        COUNT(DISTINCT SPLIT(user_agent, '/')[OFFSET(0)]) as unique_user_agents
    FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
    WHERE TIMESTAMP_TRUNC(time, DAY) >= TIMESTAMP(@start_date)
    AND TIMESTAMP_TRUNC(time, DAY) <= TIMESTAMP(@end_date)
    AND tenant IN ('{tenant_list}')
    AND CONTAINS_SUBSTR(request_type, 'AGENT_CHAT')
    AND SPLIT(user_agent, '/')[OFFSET(0)] != 'AugmentHealthCheck'
    GROUP BY tenant
    ORDER BY total_requests DESC
    """

    job_config = bigquery.QueryJobConfig(
        query_parameters=[
            bigquery.ScalarQueryParameter("start_date", "TIMESTAMP", start_date),
            bigquery.ScalarQueryParameter("end_date", "TIMESTAMP", end_date),
        ],
        use_query_cache=True,
        use_legacy_sql=False,
    )

    df = (
        client.query(query, job_config=job_config)
        .result()
        .to_dataframe(create_bqstorage_client=False)
    )
    return df


def create_agent_summary(usage_df: pd.DataFrame) -> pd.DataFrame:
    """Create a summarized view of agent requests in three buckets."""

    # Filter for agent-related request types (all types containing "AGENT_CHAT")
    agent_df = usage_df[
        usage_df["request_type"].str.contains("AGENT_CHAT", na=False)
    ].copy()

    # Create buckets
    summary_data = []

    for tenant in agent_df["tenant"].unique():
        tenant_data = agent_df[agent_df["tenant"] == tenant]

        # Bucket 1: AGENT_CHAT requests (any user agent)
        agent_chat_data = tenant_data[
            (tenant_data["user_agent_base"] != "augment.cli")
             & (tenant_data["user_agent_base"] != "beachhead")
        ]
        agent_chat = agent_chat_data["request_count"].sum()
        agent_chat_users = agent_chat_data["user_count"].sum()

        # Bucket 2: augment.cli
        remote_cli_data = tenant_data[(tenant_data["user_agent_base"] == "augment.cli")]
        remote_cli = remote_cli_data["request_count"].sum()
        remote_cli_users = remote_cli_data["user_count"].sum()

        # Bucket 3: beachhead
        remote_beachhead_data = tenant_data[(tenant_data["user_agent_base"] == "beachhead")]
        remote_beachhead = remote_beachhead_data["request_count"].sum()
        remote_beachhead_users = remote_beachhead_data["user_count"].sum()

        total_agent = agent_chat + remote_cli + remote_beachhead

        if total_agent > 0:  # Only include tenants with agent requests
            cli_percentage = (remote_cli / total_agent) * 100 if total_agent > 0 else 0
            summary_data.append(
                {
                    "tenant": tenant,
                    "agent_chat_any": agent_chat,
                    "agent_chat_users": agent_chat_users,
                    "remote_agent_cli": remote_cli,
                    "remote_cli_users": remote_cli_users,
                    "remote_agent_beachhead": remote_beachhead,
                    "remote_beachhead_users": remote_beachhead_users,
                    "total_agent": total_agent,
                    "cli_percentage": cli_percentage,
                }
            )

    return pd.DataFrame(summary_data).sort_values("cli_percentage", ascending=False)


def format_results(usage_df: pd.DataFrame, summary_df: pd.DataFrame) -> str:
    """Format the results for display."""

    output = []
    output.append("=" * 80)
    output.append("CLI USAGE ANALYSIS")
    output.append("=" * 80)
    output.append("")

    # Summary statistics
    total_tenants = len(summary_df)
    total_requests = summary_df["total_requests"].sum()

    output.append("SUMMARY:")
    output.append(f"  Total tenants using CLI: {total_tenants}")
    output.append(f"  Total requests (filtered types): {total_requests:,}")
    total_cli_requests = summary_df["cli_requests"].sum()
    output.append(f"  Total CLI requests: {total_cli_requests:,}")
    output.append("  Request types: All types containing 'AGENT_CHAT' (AGENT_CHAT, REMOTE_AGENT_CHAT, CLI_AGENT_CHAT)")
    output.append("")

    # Top tenants by request volume
    output.append("TOP TENANTS BY REQUEST VOLUME (FILTERED REQUESTS):")
    output.append("-" * 55)
    for _, row in summary_df.head(10).iterrows():
        total_reqs = row["total_requests"]
        cli_reqs = row["cli_requests"]
        cli_pct = (cli_reqs / total_reqs) * 100 if total_reqs != 0 else 0
        output.append(
            f"  {row['tenant']:<30} {total_reqs:>8,} total ({cli_reqs:,} CLI, {cli_pct:4.1f}%) | {row['unique_user_agents']} agents, {row['active_days']} days"
        )
    output.append("")

    # Agent requests summary
    agent_summary_df = create_agent_summary(usage_df)
    if not agent_summary_df.empty:
        output.append("AGENT REQUESTS SUMMARY:")
        output.append("-" * 40)
        output.append(
            f"{'Tenant':<20} {'AGENT_CHAT':<15} {'CLI':<12} {'Beachhead':<15} {'Total':<8} {'CLI %':<8}"
        )
        output.append(
            f"{'':20} {'(reqs/users)':<15} {'(reqs/users)':<12} {'(reqs/users)':<15} {'':8} {'':8}"
        )
        output.append("-" * 85)

        for _, row in agent_summary_df.iterrows():
            output.append(
                f"{row['tenant']:<20} {row['agent_chat_any']:>7,}/{row['agent_chat_users']:<4,} {row['remote_agent_cli']:>6,}/{row['remote_cli_users']:<3,} {row['remote_agent_beachhead']:>7,}/{row['remote_beachhead_users']:<4,} {row['total_agent']:>7,} {row['cli_percentage']:>6.1f}%"
            )
        output.append("")

    # Detailed breakdown by tenant, request type, and user agent (sorted by CLI usage)
    output.append("DETAILED BREAKDOWN BY TENANT, REQUEST TYPE, AND USER AGENT:")
    output.append("-" * 70)

    # Sort usage_df by CLI percentage using the agent summary order
    if not agent_summary_df.empty:
        tenant_order = agent_summary_df["tenant"].tolist()
        usage_df_sorted = usage_df.set_index("tenant").loc[tenant_order].reset_index()
    else:
        usage_df_sorted = usage_df

    current_tenant = None
    for _, row in usage_df_sorted.iterrows():
        tenant_name = str(row["tenant"])
        if current_tenant != tenant_name:
            if current_tenant is not None:
                output.append("")
            current_tenant = tenant_name
            tenant_total = summary_df[summary_df["tenant"] == current_tenant][
                "total_requests"
            ].iloc[0]
            output.append(f"{current_tenant} (Total: {tenant_total:,} requests)")
            output.append("  " + "-" * 60)

        percentage = (row["request_count"] / tenant_total) * 100
        output.append(
            f"    {row['request_type']:<20} | {row['user_agent_base']:<30} | {row['request_count']:>6,} ({percentage:4.1f}%)"
        )

    return "\n".join(output)


def main():
    parser = argparse.ArgumentParser(description="Analyze CLI usage from BigQuery data")
    parser.add_argument(
        "--days",
        type=int,
        help="Number of days to look back (default: 7)",
        default=7,
    )
    parser.add_argument(
        "--start_date",
        type=str,
        help="Start date in YYYY-MM-DD format (overrides --days)",
        default=None,
    )
    parser.add_argument(
        "--end_date",
        type=str,
        help="End date in YYYY-MM-DD format (default: today)",
        default=None,
    )
    parser.add_argument(
        "--output_file",
        type=str,
        help="File to save results (optional)",
        default=None,
    )
    args = parser.parse_args()

    # Setup BigQuery client
    gcp_creds, _ = get_gcp_creds()
    client = bigquery.Client(project="system-services-prod", credentials=gcp_creds)

    # Calculate date range
    if args.start_date and args.end_date:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d")
    elif args.start_date:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d")
        end_date = datetime.now()
    else:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=args.days)

    print(f"Analyzing CLI usage from {start_date.date()} to {end_date.date()}")
    print("Querying BigQuery...")

    # Get the data
    cli_tenants = get_cli_tenants(client, start_date, end_date)
    if not cli_tenants:
        print("No CLI usage found in the specified date range.")
        return

    print(f"Found {len(cli_tenants)} tenants using CLI: {', '.join(cli_tenants)}")

    usage_df = get_all_usage_data_for_cli_tenants(
        client, start_date, end_date, cli_tenants
    )
    summary_df = get_tenants_summary(client, start_date, end_date, cli_tenants)

    if usage_df.empty:
        print("No CLI usage found in the specified date range.")
        return

    # Format and display results
    results = format_results(usage_df, summary_df)
    print(results)

    # Save to file if requested
    if args.output_file:
        output_path = Path(args.output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        with open(output_path, "w") as f:
            f.write(results)
        print(f"\nResults saved to {output_path}")

        # Also save raw data as CSV
        csv_path = output_path.with_suffix(".csv")
        usage_df.to_csv(csv_path, index=False)
        print(f"Raw data saved to {csv_path}")


if __name__ == "__main__":
    main()
