<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Aug<PERSON>, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `base/static_analysis/common.py`:

```

from base import languages
from base.ranges import ByteMap, ByteRange, CharRange, IntRange
from base.ranges.line_map import LineMap

AT = TypeVar("AT")
BT = TypeVar("BT")


LanguageID = Literal[
    "python",
    "java",
    "cpp",
    "javascript",
    "typescript",
    "go",
    "rust",
    "c_sharp",
    "php",
    "html",
    "dart",
    "css",
    "bash",
    "scala",
    "ruby",
    "lua",
    "sql",
    "kotlin",
    "markdown",
]

```

Here is the snippet from `experimental/dxy/edits/notebooks/regular/inspect-2023-11-01.ipynb`:

```
      "context-desc=By localising web page on the page itself, you no longer need to worry if the word you are translating is a verb or noun.\n",
      "\n",
      "# How\n",
      "how-desc=Pontoon is a very simple and intuitive tool that requires little to no technical skill for localisers to use.\n",
      "hover-desc=Move your mouse over headings, links, paragraphs or other text blocks on this page. A dashed rectangle will appear around each of these blocks, marking strings that are available for localisation on the page itself.\n",
      "\n",
      "# More\n",
      "\n",
      "# Developers\n",
      "\n",
      "# Footer\n",
      "author=Crafted by Mozilla\n",
      "\n",
      "<PREFIX># Title tag\n",
      "\n",
      "# Navigation\n",
      "<UPDATED> ```\n",
      "navigation-title=Pontoon Intro\n",
      "navigation-developers=Developers\n",
      "\n",
      "# Header\n",
      "upper-title=Pontoon by Mozilla\n",
      "headline-1=LocaliSe the web.\n",
      "headline-2=In Place.\n",

```

Here is the snippet from `base/languages/language_guesser_test.py`:

```
    ("C++", "// "),
    ("Python", "# "),
    ("JavaScript", "// "),
    ("Go", "// "),
    ("Java", "// "),
    ("Rust", "// "),
    ("TypeScript", "// "),
]


@pytest.mark.parametrize("language, expected", _TEST_COMMENT_PREFIXES)
def test_language_map_get_comment_prefix(
    language: LanguageId | None, expected: str | None
):
    """Tests the comment prefix guesser."""
    comment_prefix_guesser = LanguageMapLanguageGuesser()
    assert comment_prefix_guesser.get_comment_prefix(language) == expected


@pytest.mark.parametrize("language, expected", _TEST_COMMENT_PREFIXES)
def test_guess_comment_prefix(language: LanguageId | None, expected: str | None):
    """Tests the comment prefix guesser."""
    assert guess_comment_prefix(language) == expected

```

Here is the snippet from `research/core/legacy_comment_utils.py`:

```


# TODO: switch to use the utils from `base.languages.language_guesser`.
def make_comment_block(
    lang: LanguageID, text: str, comment_after_common_indentation: bool = True
):
    r"""Turn the given text into a comment block.

    For example:

    make_comment_block("python", "some text\nmore text\n")
        -> "# some text\n# more text\n"

    Args:
        lang: The language name
        text: Text to turn into a comment
        comment_after_common_indentation: If True, and there is common indentation
            in the text lines, the comment symbol will be placed after the indentation
            rather than at the beginning of the line.

    Returns:
        The text, commented out.
    """
    raw_lines = text.splitlines(keepends=True)

    common_indentation = ""
    lines = raw_lines
    if comment_after_common_indentation and len(raw_lines) > 1:
        # extract the common indentation
        common_indentation = re.sub(

```

Here is the snippet from `research/eval/tests/test_patch_lib.py`:

```

    for patch in patches:
        assert patch.patched_file_content == patch.file_content

    original_text = textwrap.dedent(
        """
    def hello_world():
        print("hello world!")
    """
    )

    expected_patches = [patch_lib.Patch.for_span(original_text, 20, 46, patch_id="/0")]
    assert patches == expected_patches


def test_load_patches_from_text_multiple():
    patches = patch_lib.load_patches_from_text(
        textwrap.dedent(
            """
    @dataclass
    class Turtle:
    # START COMPLETION
        x: float = 0.0
        y: float = 0.0
        dx: float = 1.0
        dy: float = 0.0
    # END COMPLETION


    def turn_right(turtle):

```

Here is the snippet from `experimental/vzhao/20231129_star_ethanol/modeling/tests/test_ethanol.py`:

```
    )
    assert header_tokens == [0, 1, 2]

```

Here is the snippet from `experimental/guy/analysis/tokenizer.ipynb`:

```
       " 'ĠWong': 27247,\n",
       " 'fight': 15481,\n",
       " 'Ġwhim': 29923,\n",
       " 'State': 9012,\n",
       " 'Jon': 18219,\n",
       " 'ulture': 6456,\n",
       " 'Ġangular': 32558,\n",
       " 'ĠDisclosure': 41806,\n",
       " 'entimes': 43598,\n",
       " 'Ġglory': 13476,\n",
       " 'Ġconception': 19759,\n",
       " '185': 21652,\n",
       " 'arij': 39010,\n",
       " '199': 19104,\n",
       " 'emetery': 19785,\n",
       " 'Charlie': 37136,\n",
       " 'ĠKelley': 34560,\n",
       " 'ĠLem': 20607,\n",
       " 'hart': 18647,\n",
       " 'Ġãĥ': 14524,\n",
       " 'ĠÎ¼g': 44415,\n",
       " 'erey': 48023,\n",
       " 'ĠCDs': 36731,\n",
       " 'Gl': 9861,\n",
       " 'leaders': 37553,\n",
       " 'Ġcaller': 24955,\n",
       " 'Ġangels': 21981,\n",
       " 'ĠBlow': 26588,\n",
       " 'Ġtouted': 28275,\n",
       " 'ĠAAA': 25734,\n",

```

Here is the snippet from `base/languages/languages.py`:

```
"""Canonical names for programming languages."""

import typing

# TODO(arun,guy): We should migrate this to be an enum.
# This list should correspond to the languages in `languages.jsonnet`. Run this
# command to get the list: `jq '. | keys' languages.json`.
LanguageId = typing.Literal[
    "Astro",
    "C",
    "C++",
    "CSharp",
    "Go",
    "HTML",
    "Java",
    "JavaScript",
    "JavaScript JSX",
    "Jsonnet",
    "Kotlin",
    "Lua",
    "Markdown",
    "PHP",
    "Perl",
    "Plain Text",
    "Protobuf",
    "Python",
    "R",
    "Racket",
    "Ruby",
    "Rust",

```

Here is the snippet from `experimental/yuri/pr_edits/eval_real_comments.ipynb`:

```
    "    <div id=\"code-diff\">{diff_html}</div>\n",
    "\"\"\"\n",
    "    if another_header is not None:\n",
    "        html = f\"\"\"<h4>{another_header}</h4>\"\"\" + html\n",
    "    return html\n",
    "\n",
    "def mark_lines(code, line_range):\n",
    "    result = []\n",
    "    for i, line in enumerate(code.splitlines(True)):\n",
    "        if line_range[0] <= i < line_range[1]:\n",
    "            result.append(f\"|>{line}\")\n",
    "        else:\n",
    "            result.append(f\"  {line}\")\n",
    "    return \"\".join(result)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "HTML_START = f\"\"\"\n",
    "<!DOCTYPE html>\n",
    "<html>\n",
    "<head>\n",
    "    <title>Code Visualization</title>\n",
    "    <style>\n",
    "        pre {{\n",
    "            background-color: #f4f4f4;\n",

```

Here is the snippet from `research/data/synthetic_code_edit/util_lib.py`:

```
        file_name=annotation["file_name"],
        prefix=prefix,
        selected_code=selected_code,
        suffix=suffix,
        updated_code=updated_code,
        instructions=[Instruction.load_from_any(x) for x in annotation["instructions"]],
        inverse_instructions=[
            Instruction.load_from_any(x) for x in annotation["inverse_instructions"]
        ],
        metadata=remove_keys(
            annotation,
            ("repo_url", "file_name", "instructions", "inverse_instructions"),
        ),
    )


def _build_dialogue_html_str(
    dialogue: list[typing.Union[dict[str, str], str]], title: str, unique_index: int
) -> str:
    """Build the html string of the dialogue."""
    if not dialogue:
        return ""
    dialog_elems = []
    # Dialog in the OAI messages format
    # Dialog is a list of messages
    # Each message is a dict with 2 keys:
    #   - 'role': user/assistant
    #   - 'content' - text content of message
    for index, m in enumerate(dialogue):

```

Here is the snippet from `clients/vscode/webviews/src/common/components/SimpleMonaco.svelte`:

```
<script lang="ts">
    import { onDestroy } from "svelte";
    import Monaco from "./Monaco.svelte";
    import hljs from "highlight.js";
    import { editor, languages, Uri } from "monaco-editor";

    export let text: string;
    export let lang: string | undefined = undefined;
    export let pathName: string | undefined = undefined;
    export let options: Partial<editor.IStandaloneEditorConstructionOptions> = {};

    const supportedLanguages: string[] = languages.getLanguages().map((lang) => lang.id);

    // We use HLJS to guess the language and highlight if we are not passed the language by the model
    $: composedLang =
        lang && supportedLanguages.includes(lang)
            ? lang
            : hljs.highlightAuto(text, supportedLanguages).language;

    // If a pathname is not passed, we create a unique URI for the model
    $: uri = pathName ? Uri.parse(`file://` + pathName + `#${crypto.randomUUID()}`) : undefined;
    let model: editor.ITextModel;
    $: {
        model?.dispose();

```

Here is the snippet from `experimental/guy/analysis/tokenizer.ipynb`:

```
      "\n",
      "==================================================\n",
      "\n",
      "Tokenized:\n",
      "[198, 4299, 22944, 33529, 198, 50284, 4798, 7203, 15496, 11, 2159, 2474, 8, 628, 50284, 1640, 2124, 45579, 287, 2837, 32590, 3064, 11, 1802, 2599, 198, 50280, 21943, 796, 2124, 45579, 1635, 362, 1343, 642, 198, 50280, 4798, 7203, 1212, 318, 22944, 25, 1600, 22944, 8, 198, 50280, 361, 22944, 1875, 657, 25, 198, 50276, 4798, 7203, 1026, 338, 3967, 2474, 8, 198]\n",
      "['\\\\n', 'def', '_foo', '():', '\\\\n', '    ', 'print', '(\"', 'Hello', ',', '_World', '!\"', ')', '\\\\n\\\\n', '    ', 'for', '_x', 'yz', '_in', '_range', '(-', '100', ',', '_100', '):', '\\\\n', '        ', 'foo', '_=', '_x', 'yz', '_*', '_2', '_+', '_5', '\\\\n', '        ', 'print', '(\"', 'This', '_is', '_foo', ':', '\",', '_foo', ')', '\\\\n', '        ', 'if', '_foo', '_>', '_0', ':', '\\\\n', '            ', 'print', '(\"', 'It', \"'s\", '_positive', '!\"', ')', '\\\\n']\n"
     ]
    }
   ],
   "source": [
    "#\n",

```

Here is the snippet from `research/eval/vulcan/data.py`:

```
                ]
            )
            meta = yaml.safe_load(frontmatter)
            text = text[frontmatter_end + len(frontmatter_delimiter) :]
        else:
            meta = {}

        # Find the cursor text.
        # We support a few types of cursors.
        if (cursor_idx := text.find(cursor_token)) >= 0:
            # A <FILL-HERE> type of cursor.
            expected = meta.pop("expected", "")
            prefix = text[0:cursor_idx]
            suffix = text[cursor_idx + len(cursor_token) :]
        elif (begin_pos := text.find(inline_begin_token)) >= 0 and (
            end_pos := text.find(inline_end_token, begin_pos + len(inline_begin_token))
        ) >= 0:
            expected = text[begin_pos + len(inline_begin_token) : end_pos]
            prefix = text[0:begin_pos]
            suffix = text[end_pos + len(inline_end_token) :]
        elif (
            begin_idx := re.search(begin_token, text, re.MULTILINE | re.IGNORECASE)

```

Here is the snippet from `research/eval/tests/test_patch_lib.py`:

```
    )

    for patch in patches:
        assert patch.patched_file_content == patch.file_content

    expected_patches = [
        patch_lib.Patch.for_span(original_text, 26, 104, patch_id="/0"),
        patch_lib.Patch.for_span(original_text, 130, 179, patch_id="/1"),
        patch_lib.Patch.for_span(original_text, 204, 253, patch_id="/2"),
    ]

    assert patches == expected_patches


def test_patch_asdict():
    patch = patch_lib.Patch(
        file_content="some text",
        char_start=0,
        char_end=4,
        patch_content="any",
        patch_id="id",
        repository="repo",
        commit_sha="commit_sha",
        file_name="file_name",
    )
    assert dataclasses.asdict(patch) == {
        "file_content": "some text",
        "char_start": 0,
        "char_end": 4,
        "patch_content": "any",

```

Here is the snippet from `research/fim/fim_prompt.py`:

```
from research.utils.inspect_indexed_dataset import highlight_special_tokens

# to be extended as a Union type to support other models
TokenizerType = AbstractTokenizer

Token = int
TokenSeq = list[Token]
TokenArray = np.ndarray


def to_token_array(tokens: Sequence[int], vocab_size: int) -> TokenArray:
    int_type = np.uint16 if vocab_size < 2**16 else np.int32
    return np.array(tokens, dtype=int_type)


@dataclass
class FormattedFimProblem:
    """Contains the prompt tokens along with information about different sections."""

    tokens: TokenArray
    """All formatted tokens."""

    filename_range: IntRange
    """The index range of the filename section in `tokens`."""

    signature_range: IntRange
    """The index range of the signature section in `tokens`."""

    prefix_range: IntRange
    """The index range of the prefix in `tokens`."""

```

Here is the snippet from `base/tokenizers/data_gym_test.py`:

```
        ("Ĕ", 0x14),
        ("ĕ", 0x15),
        ("Ė", 0x16),
        ("ė", 0x17),
        ("Ę", 0x18),
        ("ę", 0x19),
        ("Ě", 0x1A),
        ("ě", 0x1B),
        ("Ĝ", 0x1C),
        ("ĝ", 0x1D),
        ("Ğ", 0x1E),
        ("ğ", 0x1F),
        ("Ġ", 0x20),
        ("!", 0x21),
        ('"', 0x22),
        ("#", 0x23),
        ("$", 0x24),
        ("%", 0x25),
        ("&", 0x26),
        ("'", 0x27),
        ("(", 0x28),
        (")", 0x29),
        ("*", 0x2A),
        ("+", 0x2B),
        (",", 0x2C),
        ("-", 0x2D),
        (".", 0x2E),
        ("/", 0x2F),
        ("0", 0x30),
        ("1", 0x31),

```

Here is the snippet from `clients/vscode/src/chat/editor-utils.ts`:

```
    if (!editor?.selection?.active) {
        return;
    }

    const cursorSelection = editor.selection;

    // We need to construct the snippet string and then `appendText` in
    // order to escape strings properly and insert it as plaintext. For the
    // snippet docs describing this, see the below link:
    // https://macromates.com/textmate/manual/snippets#plain-text
    // The VSCode docs describing this are here:
    // https://code.visualstudio.com/api/references/vscode-api#SnippetString
    const snippet = new vscode.SnippetString().appendText(code);

    // Snippets take advantage of language features, formatting, etc.
    // For more details: https://code.visualstudio.com/docs/editor/userdefinedsnippets
    editor.insertSnippet(snippet, cursorSelection);
}

```

Here is the snippet from `tools/bazel_runner/review_edit_bot/text_test.py`:

```
    assert actual_prompt.line == 197

```

The developer has file `/home/<USER>/augment/services/auth/central/server/tenant_map.py` open and has selected part of the code.

Here is the full file:

```
"""Tenant information."""

import logging
from typing import Optional, Iterable

import auth_dao
from config import TenantConfig
from concurrent.futures import ThreadPoolExecutor

import base.feature_flags
import services.tenant_watcher.tenant_watcher_pb2 as tenant_watcher_pb2
from base.feature_flags import BoolFlag
from services.tenant_watcher.client.client import TenantsClient
from services.tenant_watcher.client.tenant_cache import TenantCache

AUTH_CENTRAL_USE_TENANT_WATCHER = BoolFlag("auth_central_use_tenant_watcher", False)


def _auth_url(namespace: str, auth_hostname_domain: str) -> str:
    """Builds a URL to the auth service for this tenant."""
    return f"https://auth.{namespace}.{auth_hostname_domain}/authorize"


def _tenant_url(namespace: str, api_proxy_hostname_domain: str) -> str:
    """Builds a URL to the api-proxy service for this tenant."""
    return f"https://{namespace}.{api_proxy_hostname_domain}/"


class TenantMap:
    """A map from email domains to tenant information.

    This works for cases where we have one tenant per namespace, but does not
    support shards, with multiple tenants per namespace.
    """

    def __init__(
        self,
        dao_factory: auth_dao.DaoFactory,
        tenants_list: list[TenantConfig],
        tenant_watcher_client: TenantsClient,
        auth_hostname_domain: str,
        api_proxy_hostname_domain: str,
        use_tenant_watcher: Optional[bool] = None,
        executor: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=1),
        namespace: str = "",  # empty string means all namespaces
        retry_interval: int = 30,
    ):
        self._tenants_by_domain = self.build_tenant_map(tenants_list)
        self._tenants_by_namespace = {t.namespace: t for t in tenants_list}
        self._dao_factory = dao_factory

        self._use_tenant_watcher = use_tenant_watcher
        self._tenant_watcher_client = tenant_watcher_client
        self._namespace = namespace
        self._tenant_cache = TenantCache.new_tenant_cache(
            executor=executor,
            client=self._tenant_watcher_client,
            namespace=self._namespace,
            retry_interval=retry_interval,
        )
        self._auth_hostname_domain = auth_hostname_domain
        self._api_proxy_hostname_domain = api_proxy_hostname_domain

    def use_tenant_watcher(self) -> bool:
        if self._use_tenant_watcher is not None:
            return self._use_tenant_watcher
        else:
            return AUTH_CENTRAL_USE_TENANT_WATCHER.get(
                base.feature_flags.Context.default()
            )

    def get_tenants(self, namespace: str) -> Iterable[tenant_watcher_pb2.Tenant]:
        """
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

        Args:
            namespace: the namespace to filter the tenants on. If empty, return all tenants

        Returns:
            a list of tenants
        """
        #
        if self._namespace:
            logging.info(
                f"Attempting to get tenants from tenant cache in namespace: {namespace}"
            )
            return self._tenant_cache.get_tenants_in_namespace(namespace)  # type: ignore
        else:
            logging.info(
                "Attempting to get all tenants from tenant cache in empty namespace"
            )
            return self._tenant_cache.get_all_tenants()  # type: ignore

    def has_tenant(self, tenant_name: str) -> bool:
        # Right now, these two are equivalent
        namespace = tenant_name
        if self.use_tenant_watcher():
            # Get tenants from namespace if there is a namespace, otherwise get all tenants
            tenants = self.get_tenants(namespace)
            for tenant in tenants:
                if tenant.name == tenant_name:
                    return True
            return False
        else:
            return namespace in self._tenants_by_namespace

    def build_tenant_map(
        self, tenants_list: list[TenantConfig]
    ) -> dict[str, TenantConfig]:
        result: dict[str, TenantConfig] = {}

        for tenant in tenants_list:
            if (
                tenant.domain is not None
            ):  # support tenants without domain for individuals authentication
                result[tenant.domain] = tenant

        return result

    def get_tenant_for_email_address(self, email: str) -> Optional[TenantConfig]:
        """Get the tenant for the domain associated with the email address.

        Args:
            email (str): email address. Example: "<EMAIL>"
        """
        if self.use_tenant_watcher():
            # Get tenants from namespace if there is a namespace, otherwise get all tenants
            tenants = self.get_tenants(self._namespace)

            email_domain = self._get_domain_from_email(email)
            if email_domain is not None:
                for tenant in tenants:
                    if tenant.auth_configuration.domain == email_domain:
                        logging.info("Found tenant for email domain: %s", tenant.name)
                        return TenantConfig(
                            name=tenant.name,
                            namespace=tenant.shard_namespace,
                            tenant_id=tenant.id,
                            domain=tenant.auth_configuration.domain,
                            auth_url=_auth_url(
                                tenant.shard_namespace,
                                self._auth_hostname_domain,  # type: ignore
                            ),
                            auth_url_v2=_tenant_url(
                                tenant.shard_namespace,
                                self._api_proxy_hostname_domain,  # type: ignore
                            )
                            + "authorize",
                            tenant_url=_tenant_url(
                                tenant.shard_namespace,
                                self._api_proxy_hostname_domain,  # type: ignore
                            ),
                        )
        else:
            by_domain = self._get_tenant_by_email_domain(email)
            if by_domain is not None:
                logging.info("Found tenant for email domain: %s", by_domain.namespace)
                return by_domain

        # If searching by domain does not work, try searching by individual
        # using bigtable
        by_individuals = self._get_tenant_for_individuals(email)
        if by_individuals is not None:
            logging.info("Found tenant for individual: %s", by_individuals.namespace)
            return by_individuals

        return None

    def _get_tenant_for_individuals(self, email: str) -> Optional[TenantConfig]:
        users = self._dao_factory.get_user_dao().find_all()
        user = next(filter(lambda u: u.email == email, users), None)
        if user is None:
            return None

        mapping = self._dao_factory.get_user_tenant_mapping_dao().find_all_for_user(
            user.id
        )

        if len(mapping) == 0:
            return None

        if self.use_tenant_watcher():
            # Get tenants from namespace if there is a namespace, otherwise get all tenants
            tenants = self.get_tenants(self._namespace)

            for tenant in tenants:
                if tenant.shard_namespace == mapping[0].tenant:
                    return TenantConfig(
                        name=tenant.name,
                        namespace=tenant.shard_namespace,
                        tenant_id=tenant.id,
                        domain=tenant.auth_configuration.domain,
                        auth_url=_auth_url(
                            tenant.shard_namespace,
                            self._auth_hostname_domain,  # type: ignore
                        ),
                        auth_url_v2=_tenant_url(
                            tenant.shard_namespace,
                            self._api_proxy_hostname_domain,  # type: ignore
                        )
                        + "authorize",
                        tenant_url=_tenant_url(
                            tenant.shard_namespace,
                            self._api_proxy_hostname_domain,  # type: ignore
                        ),
                    )
            return None
        else:
            return self._tenants_by_namespace.get(mapping[0].tenant)

    def _get_domain_from_email(self, email: str) -> Optional[str
```

Here is the selected code:

```
        Using the cache, get tenants from namespace if there is a namespace, otherwise get all tenants
```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

make the highlighted section betterr english<|eot_id|><|start_header_id|>assistant<|end_header_id|>
