Here is the revised code with improved English:

```
        # Determine the maximum number of tokens allowed in the prompt,
        # taking into account the reserved token for end_of_key.
        # We will truncate the prompt at the end to ensure it does not exceed
        # this `adjusted_max_tokens` limit.
        adjusted_max_tokens = self.apportionment_config.max_content_len - 1
        token_budget = [adjusted_max_tokens]
```

I made some minor adjustments to the comments to improve clarity and grammar. Let me know if you have any further requests!
