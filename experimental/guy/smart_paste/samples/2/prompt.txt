<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `research/data/spark/notebooks/redpajama_pipeline.ipynb`:

```
    "    'xml', 'markdown', 'svg',\n",
    "    'yaml', 'css', 'gettext-catalog',\n",
    "    'unity3d-asset', 'restructuredtext',\n",
    "    'ini',\n",
    "    'asciidoc',\n",
    "]\n",
    "extra = ['jupyter-notebook', 'c++']\n",
    "\n",
    "starcoder_base = 's3a://starcoder/raw'\n",
    "starcoder_lang = spark.read.option(\"basePath\", starcoder_base).parquet(starcoder_base).select('lang').distinct().collect()\n",
    "starcoder_lang = [row.lang for row in starcoder_lang]\n",
    "\n",
    "stack = stack.select('langpart', 'content').filter(~F.col('langpart').isin(noncode+extra+starcoder_lang))\n",
    "\n",
    "# print(stack.select('langpart').distinct().collect())\n",
    "tokenize_df(stack, 'stack-lang-tokenized', size_limit=50000, partitions=1000, content_column='content', bucket='starcoder')\n"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "85e55757-14ce-4a9f-98ba-bdc614e80039",
   "metadata": {},
   "source": [
    "### Step 6. Combine source to get StarCoder+\n",
    "\n",

```

Here is the snippet from `experimental/xiaolei/generate_samples_script_v2.ipynb`:

```
                "        if file[FILE_LANG_COLUMN] not in config.languages:\n",
                "            continue\n",
                "\n",
                "        # Construct multiple prefix, suffix, middle samples from file\n",
                "        base_samples = _file_to_samples(\n",
                "            file_content=file[CONTENT_COLUMN],\n",
                "            file_id=file[ID_COLUMN],\n",
                "            file_path=file[PATH_COLUMN],\n",
                "            config=config,\n",
                "            sampler=sampler,\n",
                "        )\n",
                "        for sample in base_samples:\n",
                "            sample = sample.truncated(\n",
                "                max_prefix_chars=config.max_prefix_chars,\n",
                "                max_suffix_chars=config.max_suffix_chars,\n",
                "            )\n",
                "\n",
                "            # For each sample, find retrieved chunks\n",

```

Here is the snippet from `base/languages/__init__.py`:

```
from .language_guesser import (
    LanguageGuesser,
    default_language_guesser,
    guess_comment_prefix,
    guess_language,
)
from .languages import LanguageId, Languages, check_language

```

Here is the snippet from `research/data/spark/notebooks/starcoder_pipeline.ipynb`:

```
    "tags": []
   },
   "outputs": [],
   "source": [
    "spark.sparkContext.setJobDescription(f'Tokenize all languages')\n",
    "spark.read.parquet(\n",
    "    's3a://starcoder/chunked/'\n",
    ").withColumn(\n",
    "    'tokenized', tokenize_udf('chunk')\n",
    ").write.mode(\n",
    "    'overwrite'\n",
    ").parquet('s3a://starcoder/tokenized/')"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "c810ac64-ac96-435a-89b0-c86c836f8c3f",
   "metadata": {},
   "source": [
    "Count total number of tokens out there, just to be sure\n",
    "\n",
    "don't need the extra memory per core any more, so switch it back"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "id": "808eccb5-6acf-4595-ae36-becaaafe9bce",
   "metadata": {
    "execution": {

```

Here is the snippet from `research/data/pyarrow/data_step/preprocess.py`:

```

class _Encoder(object):
    def __init__(self, args, lang_config):
        self.args = args
        self.lang_config = lang_config

    def initializer(self):
        # Use _Encoder class as a container for global data
        _Encoder.tokenizer = build_tokenizer(self.args)

    def encode(self, args):
        (doc_content, doc_path, doc_size, doc_lang) = args

        # optionally apply ftfy fixups
        if self.args.ftfy:
            doc_content = ftfy.fix_text(doc_content)

        doc_ids = []

        # optionally prepend the language prefix (e.g., "<|python|>")
        if self.args.prepend_lang:
            lang_prefix = self.lang_config[doc_lang]["doc_prefix"]
            doc_ids.extend(_Encoder.tokenizer.tokenize(lang_prefix))

        # optionally prepend the path (e.g., "<|src/module/component.py|>")
        if self.args.prepend_path:
            doc_ids.extend(_Encoder.tokenizer.tokenize(f"<|{doc_path}|>"))

        # tokenize the text

```

Here is the snippet from `experimental/guy/evol-instruct/evol-instruct.ipynb`:

```
    "\n",
    "Edit the code according to the following instruction.\n",
    "\n",
    "type: translation\n",
    "instruction: turn it into valid python\n",
    "```python\n",
    "import argparse\n",
    "\n",
    "def main():\n",
    "    parse --text flag\n",
    "```\n",
    "edited code:\n",
    "```python\n",
    "import argparse\n",
    "\n",
    "def main():\n",
    "    parser = argparse.ArgumentParser()\n",
    "    parser.add_argument(\n",
    "        \"--text\",\n",
    "        type=str,\n",
    "        default=\"Hello, World!\",\n",
    "        help=\"text to tokenize\",\n",
    "    )\n",
    "    args = parser.parse_args()\n",
    "```\n",
    "\n",
    "Edit the code according to the following instruction.\n",
    "\n",
    "type: format\n",
    "instruction:\"\"\"\n",

```

Here is the snippet from `research/data/train/tests/test_lang_utils.py`:

```
"""Unit tests for lang_utils."""

from research.data.train.common.lang_utils import (
    STARCODER_EXTRA,
    STARCODER_LANGUAGES,
    get_standard_language,
)


class TestLangUtils:
    """Unit tests for lang_utils."""

    def test_starcoder(self):
        assert len(STARCODER_LANGUAGES) == 88
        assert len(STARCODER_EXTRA) == 4

    def test_common_conversion(self):
        test_cases = [
            # These are real examples from starcoder
            ("c#", "c-sharp"),
            ("common lisp", "common-lisp"),
            ("emacs lisp", "emacs-lisp"),
            ("f#", "f-sharp"),
            ("java server pages", "java-server-pages"),
            ("literate agda", "literate-agda"),
            ("literate coffeescript", "literate-coffeescript"),
            ("literate haskell", "literate-haskell"),
            ("protocol buffer", "protocol-buffer"),
            ("standard ml", "standard-ml"),
            ("visual basic", "visual-basic"),

```

Here is the snippet from `base/static_analysis/common.py`:

```

from base import languages
from base.ranges import ByteMap, ByteRange, CharRange, IntRange
from base.ranges.line_map import LineMap

AT = TypeVar("AT")
BT = TypeVar("BT")


LanguageID = Literal[
    "python",
    "java",
    "cpp",
    "javascript",
    "typescript",
    "go",
    "rust",
    "c_sharp",
    "php",
    "html",
    "dart",
    "css",
    "bash",
    "scala",
    "ruby",
    "lua",
    "sql",
    "kotlin",
    "markdown",
]

```

Here is the snippet from `experimental/vzhao/airflow/dags/ppl/sample_ppl_data.py`:

```
                # Only add files of main languages to be trained on
                if file[FILE_LANG_COLUMN] not in allowed_languages:
                    continue

                # Construct multiple prefix, suffix, middle samples from file
                base_samples = common.file_to_samples(
                    file_content=file[CONTENT_COLUMN],
                    file_id=file[ID_COLUMN],
                    file_path=file[PATH_COLUMN],
                    sampler=sampler,
                    every_n_lines=CONFIG["every_n_lines"],
                    max_problems_per_file=CONFIG["max_problems_per_file"],
                    random_seed=CONFIG["random_seed"],
                )
                for sample in base_samples:
                    sample = sample.truncated(
                        max_prefix_chars=CONFIG["max_prefix_chars"],
                        max_suffix_chars=CONFIG["max_suffix_chars"],
                    )
                    model_input = ModelInput(

```

Here is the snippet from `experimental/xiaolei/download_commitpack.py`:

```
    "xml",
    "xpages",
    "xproc",
    "xquery",
    "xs",
    "xslt",
    "xtend",
    "yacc",
    "yaml",
    "yang",
    "zephir",
    "zig",
]


for lang in langs:
    path = Path(".") / f"lang={lang}"
    path.mkdir()
    print(f"Downloading {lang} to {path}")
    dataset = load_dataset("bigcode/commitpackft", lang)
    dataset["train"].to_parquet(str(path / "train.parquet"))

```

Here is the snippet from `base/languages/unit_test_guesser.py`:

```
    r"(test|tests|(test|tests)(-|_)[a-zA-Z0-9_-]*|[a-zA-Z0-9_-]*(-|_)(test|tests))"
)

pattern_list_by_langid: dict[LanguageId | None, tuple[str, ...]] = {
    "Python": (
        rf"{TEST_FOLDER_PATTERN}.*({LANG2EXTGROUP['Python']})$",
        rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({LANG2EXTGROUP['Python']})$",
        rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({LANG2EXTGROUP['Python']})$",
        rf"(^|.*/)conftest({LANG2EXTGROUP['Python']})$",
    ),
    "C++": (
        rf"{TEST_FOLDER_PATTERN}.*({LANG2EXTGROUP['C++']})$",
        rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({LANG2EXTGROUP['C++']})$",
        rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({LANG2EXTGROUP['C++']})$",
    ),
    "Java": (
        rf"{TEST_FOLDER_PATTERN}.*({LANG2EXTGROUP['Java']})$",
        rf"(^|.*/){CAMEL_CASE_FILE_PATTERN}({LANG2EXTGROUP['Java']})$",
        rf"(^|.*/){SNAKE_CASE_FILE_PATTERN}({LANG2EXTGROUP['Java']})$",
    ),
    "TypeScript": (
        rf"{TEST_FOLDER_PATTERN}.*({LANG2EXTGROUP['TypeScript']})$",

```

Here is the snippet from `base/languages/language_guesser_test.py`:

```
"""Unit tests for the language guesser."""
from __future__ import annotations

import pytest

from base.languages.language_guesser import (
    LanguageMapLanguageGuesser,
    guess_comment_prefix,
    guess_language,
)
from base.languages.languages import LanguageId

_TEST_EXTENSIONS: list[tuple[str, LanguageId | None]] = [
    # C
    ("foo.c", "C"),
    # NOTE(arun): we prefer C++ over C by default.
    ("foo.h", "C++"),
    # C++
    ("foo.cpp", "C++"),
    ("foo.c++", "C++"),
    ("foo.cc", "C++"),
    ("foo.cxx", "C++"),
    ("foo.h", "C++"),
    ("foo.h++", "C++"),
    ("foo.hpp", "C++"),
    ("foo.hxx", "C++"),
    # Go
    ("foo.go", "Go"),
    # Java
    ("foo.java", "Java"),

```

Here is the snippet from `research/data/spark/notebooks/dataset_playground.ipynb`:

```
     "text": [
      "                                                                                \r"
     ]
    },
    {
     "name": "stdout",
     "output_type": "stream",
     "text": [
      "Fixing dataset verilog\n"
     ]
    },
    {
     "name": "stderr",
     "output_type": "stream",
     "text": [
      "                                                                                \r"
     ]
    }
   ],
   "source": [
    "starcoder_backup = 's3a://starcoder/backup'\n",
    "starcoder_base = 's3a://starcoder/raw'\n",
    "\n",
    "fix_langs = ['xslt', 'verilog', 'cpp', 'c', 'c-sharp', 'elixir']\n",
    "\n",
    "for lang in fix_langs:\n",
    "    try:\n",
    "        probe = spark.read.parquet(f'{starcoder_base}/lang={lang}/*.parquet')\n",
    "        need_fix = False\n",
    "        for field in probe.schema.jsonValue()['fields']:\n",

```

Here is the snippet from `research/data/train/common/starcoder_filter.py`:

```
    "lookml": [],
    "nextjs": [r".*\.next"],
    "nuxtjs": [r".*\.nuxt"],
    "objective-c": [r".*\.m"],
    "objective-cpp": [],
    "project_files": ["manifest", "description", "namespace"],
    "readme": ["README"],
    "svelte": [r".*\.svelte"],
    "swift": [],
    "vagrantfile": ["vagrantfile"],
    "vue": [],
    "xpages": [r".*\.xsp\.metadata"],
    # Existing languages in starcoder but new patterns
    # Extensions with >1 parts are not handled correctly during initial etl
    "assembly": [r".*\.s\.in", r".*\.s"],
    "clojure": [r".*\.cljs\.hl"],
    "cmake": [r".*\.cmake\.in"],
    "dockerfile": ["dockerfile", r"dockerfile\..*"],
    "emacs-lisp": [r".*\.emacs\.desktop"],
    "html": [r".*\.html\.hl"],
    "makefile": ["makefile", r"makefile\..*"],
    "python": ["pipfile", r"requirements\.txt"],
    "restructuredtext": [r".*\.rest\.txt", r".*\.rst\.txt"],
    "ruby": ["Gemfile", "Gemspec", "rakefile"],
    "rust": [r".*\.rs\.in"],
    "shell": [r".*\.sh\.in"],
}


def update_lang(

```

Here is the snippet from `research/data/collection/github/controllers/convert_files_stream.py`:

```
        lang = lookup.get(ext, "")
        return lang.lower()

    df = stream_from_files(
        spark,
        INPUT_BASE,
        sample_path=INPUT_SAMPLE,
        mode="DROPMALFORMED",
        maxFilesPerTrigger=1000,
    )
    df = (
        df.withColumn("lang", get_lang(F.col("path")))
        .filter(F.length("content") < MAX_LENGTH)
        .withColumn("is_head", F.col("pct_modified") > 100)
        .withColumn("line_lengths", F.transform(F.split("content", "\n"), F.length))
        .withColumn(
            "avg_line_length",
            # line_lengths is an array of lengths for each row.  take row-wise average
            F.expr(
                "AGGREGATE(line_lengths, CAST(0 AS DOUBLE), (a, x) -> a + x, a -> a / SIZE(line_lengths))"
            ),
        )
        .withColumn("max_line_length", F.array_max("line_lengths"))
        .withColumn(
            "alphanum_fraction",
            F.length(F.regexp_replace("content", "[^a-zA-Z0-9]", ""))
            / F.length("content").cast("double"),

```

Here is the snippet from `research/data/spark/notebooks/dataset_playground.ipynb`:

```
    "            if field['name'] == 'max_stars_count' and field['type'] == 'double':\n",
    "                need_fix = True\n",
    "    except:\n",
    "        need_fix = True\n",
    "    if not need_fix:\n",
    "        continue\n",
    "    print(f'Fixing dataset {lang}')\n",
    "    df = spark.read.parquet(f'{starcoder_backup}/lang={lang}/*.parquet')\n",
    "    df.withColumn('max_stars_count', sp.col('max_stars_count').cast('bigint')).write.mode('overwrite').parquet(f'{starcoder_base}/lang={lang}/')\n"
   ]
  },
  {
   "cell_type": "markdown",
   "id": "780509e5-9839-4c9e-ae0a-49a1333f7322",
   "metadata": {},
   "source": [
    "#### High level statistics\n",
    "Here we also run some statistics across the entire dataset to make sure it works.  It is also in a way a performance test.\n",
    "\n",
    "(Expected runtime is about 20~30min to analyze through the entire dataset with 16CPUs.)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": 5,

```

Here is the snippet from `research/data/train/common/lang_utils.py`:

```

    Common rules:
        * All leading and trailing whitespaces are removed.
        * All characters are lowercased.
        * All spaces are replaced with dashes.
        * All # characters are replaced with '-sharp'.
    """
    std_lang = language.strip().lower().replace(" ", "-").replace("#", "-sharp")

    if std_lang in SPECIAL_CONVERSIONS:
        return SPECIAL_CONVERSIONS[std_lang]
    else:
        return std_lang

```

Here is the snippet from `base/languages/unit_test_guesser.py`:

```
    for lang in LanguageId.__args__:  # type: ignore
        if lang not in lang2extensions:
            raise ValueError(f"Missing extensions for {lang}")
    return lang2extensions


LANG2EXTS = get_lang2extensions()
LANG2EXTGROUP = {
    lang: "|".join([re.escape(ext) for ext in exts]) for lang, exts in LANG2EXTS.items()
}

# NOTE: This is a heuristic and may not be perfect, we will continue to refine it.
# Currently, we prioritize the precision of the pattern over the recall.
# If you find something missing or inperfection, please let add a test case in unit_test_guesser_test.py and then update this list.
#
# Overall, we try to design the regex pattern as general pattern + specific pattern for each language.
TEST_FOLDER_PATTERN = r"(^(test|tests)/|.*/(test|tests)/|.*/[a-z0-9]+(_|-)(test|tests)/)|.*/test(_|-)[a-z0-9]+|.*/Test[A-Z0-9]+/"
CAMEL_CASE_FILE_PATTERN = (
    r"([a-zA-Z0-9_-]*(Test|Tests)|[a-zA-Z0-9_-]*(Test|Tests)[A-Z0-9]+[a-zA-Z0-9_-]*)"
)
SNAKE_CASE_FILE_PATTERN = (

```

Here is the snippet from `research/data/train/tests/test_lang_utils.py`:

```
            # Additional test cases
            ("Python", "python"),
            (" python ", "python"),
        ]

        for input_lang, expected in test_cases:
            assert get_standard_language(input_lang) == expected

    def test_special_cases(self):
        test_cases = [
            ("c++", "cpp"),
        ]

        for input_lang, expected in test_cases:
            assert get_standard_language(input_lang) == expected

    def test_starcoder_conversion(self):
        # Test that all languages in starcoder are unchanged after conversion
        for input_lang in STARCODER_LANGUAGES:
            assert get_standard_language(input_lang) == input_lang

```

The developer has file `/home/<USER>/augment/base/prompt_format_retrieve/chatanol_prompt_formatter.py` open and has selected part of the code.

Here is the full file:

```
"""Prompt formatter to format prompts for Ethanol embedding keys and queries."""

from typing import Optional

from base.prompt_format.common import Exchange
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    RetrieverPromptFormatter,
    PromptFormatterOutput,
)
from base.tokenizers import (
    RetrievalSpecialTokens,
    Tokenizer,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderSpecialTokens


class Chatanol6QueryFormatter(RetrieverPromptFormatter[ChatRetrieverPromptInput]):
    """Query formatter for Chatanol1-16-3 models."""

    input_type = ChatRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.preamble = (
            [tokenizer.special_tokens.begin_sequence]
            if isinstance(tokenizer.special_tokens, DeepSeekCoderSpecialTokens)
            else []
        )

    def _format_user_message(self, message: str, add_newline: bool):
        tokenize = self.tokenizer.tokenize_safe
        tokens = tokenize("### Instruction:\n") + tokenize(message)
        if add_newline:
            tokens += tokenize("\n")
        return tokens

    def _format_chat_message(self, message: str):
        tokenize = self.tokenizer.tokenize_safe
        tokens = tokenize("### Response:\n") + tokenize(message)
        # We always add a newline at the end of the response,
        # since we always expect to see a new instruction.
        tokens += tokenize("\n")
        return tokens

    def _format_exchange(self, exchange: Exchange):
        return self._format_user_message(
            exchange.request_message, True
        ) + self._format_chat_message(exchange.response_text)

    def format_prompt(
        self,
        prompt_input: ChatRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Returns tokenized prompt and metadata."""
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

        adjusted_max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_key
        )
        # How many tokens we have left in the prompt
        token_budget = adjusted_max_tokens - len(self.preamble)

        # Start by adding the last user message
        message = prompt_input.message
        assert message
        prompt_tokens = self._format_user_message(message, False)
        token_budget -= len(prompt_tokens)

        # Include conversation history if budget allows that
        for exchange in reversed(prompt_input.chat_history):
            exchange_tokens = self._format_exchange(exchange)
            if len(exchange_tokens) <= token_budget:
                prompt_tokens = exchange_tokens + prompt_tokens
                token_budget -= len(exchange_tokens)
            else:
                break
        prompt_tokens = self.preamble + prompt_tokens

        if len(prompt_tokens) > adjusted_max_tokens:
            prompt_tokens = prompt_tokens[:adjusted_max_tokens]
        return PromptFormatterOutput([prompt_tokens])

```

Here is the selected code:

```
        # How many tokens we allow in the prompt if we
        # reserve 1 token for end_of_key.
        # We will truncate the prompt at the very end to make
        # sure we don't exceed this `adjusted_max_tokens` limit.```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

can you add default value empty list?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Here is the modified code:

```
    chat_history: Sequence[Exchange] = []
    """The conversation history as a list of request_message/response_text pairs."""
```

I added a default value of an empty list to the `chat_history` field.<|eot_id|><|start_header_id|>user<|end_header_id|>

but that's a wrong way to add empty list as a default value for dataclasses<|eot_id|><|start_header_id|>assistant<|end_header_id|>

I apologize for the mistake. You are correct, the way I added the default value is not correct for dataclasses.

Here is the correct way to add an empty list as a default value for a dataclass field:

```
    chat_history: Sequence[Exchange] = field(default_factory=list)
    """The conversation history as a list of request_message/response_text pairs."""
```

By using `field(default_factory=list)`, we ensure that a new empty list is created for each instance of the dataclass, rather than sharing the same list across all instances.<|eot_id|><|start_header_id|>user<|end_header_id|>

improve english<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Here is the revised text with improved English:

```
    chat_history: Sequence[Exchange] = field(default_factory=list)
    """The conversation history, represented as a sequence of request_message and response_text pairs."""
```

I made some minor adjustments to the docstring to improve clarity and grammar. Let me know if you have any further requests!<|eot_id|><|start_header_id|>user<|end_header_id|>

b<|eot_id|><|start_header_id|>assistant<|end_header_id|>

It seems like you want me to modify the selected code. Here is the modified code:

```
        # How many tokens we allow in the prompt if we
        # reserve 1 token for end_of_key.
        # We will truncate the prompt at the very end to make
        # sure we don't exceed this `adjusted_max_tokens` limit.
        adjusted_max_tokens = self.apportionment_config.max_content_len - 1
        token_budget = [adjusted_max_tokens]
```<|eot_id|><|start_header_id|>user<|end_header_id|>

improve english<|eot_id|><|start_header_id|>assistant<|end_header_id|>
