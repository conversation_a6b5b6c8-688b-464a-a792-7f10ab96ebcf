<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Aug<PERSON>, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `services/tenant_watcher/util/tenant_watcher_util.py`:

```
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    tenants_map = {}
    for tenant in api_response["items"]:
        tenants_map[tenant["metadata"]["name"]] = tenant["spec"]["tenant_id"]
    return tenants_map

def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()

    parser.ad

    parser.add_argument("--shard-namespace", help="The shard namespace", required=True)
    parser.add_argument(
        "--cloud",
        help="The cloud to use",
        required=True,
        choices=cloud_lib.get_cloud_list(),
    )
    parser.add_argument("--tenant-name", help="tenant name")
    parser.add_argument(
        "--path",

```

Here is the snippet from `services/tenant_watcher/util/tenant_watcher_util.py`:

```
"""
CLI Utility to get tenant ids from tenant name
"""

import argparse
import logging
import pathlib
import sys
import json

from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.console_logging import setup_console_logging
from base.python.cloud import cloud as cloud_lib
import getpass


def _get_tenant_id(args) -> str | None:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    for tenant in api_response["items"]:
        if tenant["metadata"]["name"] == args.tenant_name:
            return tenant["spec"]["tenant_id"]

def _get_tenant_ids_list(args) -> dict[str, str]:

```

Here is the snippet from `services/tenant_watcher/client/client.py`:

```
    def __init__(
        self,
        endpoint: str,
        credentials: grpc.ChannelCredentials | None,
        options: list[tuple[str, str]] | None = None,
    ):
        self.stub = setup_stub(endpoint, credentials, options=options)

    def get_tenants(
        self, shard_namespace: str = ""
    ) -> typing.Sequence[tenant_watcher_pb2.Tenant]:
        """Get the list of tenants.

        see tenant_proto for details

        Args:
            shard_namespace: the namespace to filter the tenants on

        Returns:
            a list of tenants
        """

        request = tenant_watcher_pb2.GetTenantsRequest(shard_namespace=shard_namespace)
        response = self.stub.GetTenants(request)
        return response.tenants

    def get_tenant_id(self, tenant_name: str) -> str | None:
        """Get the tenant ID for the given tenant name.

        Args:

```

Here is the snippet from `services/tenant_watcher/util/tenant_watcher_util.py`:

```
        help="relative path name",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".augment/service_token.txt"),
    )
    parser.add_argument(
        "--kube-config-file",
        help="Path to kube config file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )
    parser.set_defaults(action=None)

    args = parser.parse_args()
    if args


if __name__ == "__main__":
    main()

```

Here is the snippet from `services/inference_host/server/continuous_batching/server.py`:

```
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    logging.info("Args %s", args)
    env_info.print_env_info()

    if "POD_NAMESPACE" not in os.environ:
        raise RuntimeError("POD_NAMESPACE environment variable not set")
    namespace = os.environ["POD_NAMESPACE"]

    # This goes out and overrides the grpc package so the opentelemetry
    # interceptor is automatically added during client creation.
    grpc_client_instrumentor = (
        opentelemetry.instrumentation.grpc.GrpcInstrumentorClient()
    )
    grpc_client_instrumentor.instrument()

    # begin listening for Prometheus requests
    start_http_server(9090)

    config = _load_config(args.config_file)
    logging.info("Config %s", config)

    if config.feature_flags_sdk_key_path is not None:
        context = base.feature_flags.Context.setup(

```

Here is the snippet from `services/tenant_watcher/server/main.go`:

```
	}
	return nil
}

func initTracer(ctx context.Context) error {
	option := otlptracegrpc.WithInsecure()
	exporter, err := otlptracegrpc.New(ctx,
		option)
	if err != nil {
		return err
	}

	// Create trace provider with OTLP exporter
	tp := trace.NewTracerProvider(
		trace.WithBatcher(exporter),
	)

	// Register the trace provider as the global provider
	otel.SetTracerProvider(tp)
	return nil
}

func main() {
	// Hoping that nanosecond timestamps will help keep log messages ordered in Google Cloud Logging
	zerolog.TimeFieldFormat = time.RFC3339Nano

	flag.Parse()
	log.Info().Msgf("Config file: %s", *configFile)

	var config Config

```

Here is the snippet from `experimental/andre/tenant_management/add_tenant_config.py`:

```
    """
    print("##### Share this token with users: #####")
    print(token_str)
    print("########################################")


def add_tenant(tenant_name, tenant_url, region_str, api_users):
    tenants_str = get_tenants_string(tenant_name, tenant_url, region_str)
    output_tenants_string(tenant_name, tenants_str)

    if len(api_users) > 0:
        # Generate tokens and keys.
        tokens = {}
        keys = {}
        for user in api_users:
            tokens[user] = _generate_token()
            keys[user] = _get_sha256(tokens[user])

        kubecfg_str = get_kubecfg_string(keys)
        output_kubecfg_string(tenant_name, kubecfg_str)

        token_str = get_token_string(tokens)
        output_token_string(tenant_name, token_str)

        ns_configs_str = get_ns_configs_string(tenant_name)
        output_ns_configs_string(tenant_name, ns_configs_str)


if __name__ == "__main__":
    region_map = {

```

Here is the snippet from `services/token_exchange/util/util.py`:

```
    logging.info("%s", url)

    secret = getpass.getpass("> ")
    if not secret:
        logging.error("No secret provided")
        sys.exit(1)

    if p.exists():
        if p.stat().st_mode & 0o777 != 0o600:
            logging.warning("Changing permissions of %s", p)
        p.chmod(0o600)
    else:
        p.touch(mode=0o600)  # to restrict access
    p.write_text(secret)
    logging.info("Wrote token to %s", p)


def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()

    parser.add_argument("--shard-namespace", help="The shard namespace", required=True)
    parser.add_argument(
        "--cloud",
        help="The cloud to use",
        required=True,
        choices=cloud_lib.get_cloud_list(),
    )
    parser.add_argument("--tenant-name", help="tenant name")

```

Here is the snippet from `services/auth/query/server/src/main.rs`:

```
            panic!("metrics_server failed: {metrics_server:?}");
        }
        api_token_db = api_token_db.run() => {
            panic!("api_token_db failed: {api_token_db:?}");
        }
        tenant_cache = tenant_cache.run() => {
            panic!("tenant_cache failed: {tenant_cache:?}");
        }
    };
}

fn create_tenant_watcher_client(
    tenant_watcher_endpoint: &str,
    client_tls_config: Option<ClientTlsConfig>,
) -> Arc<dyn TenantWatcherClient + Send + Sync> {
    tracing::info!("Creating tenant watcher client: endpoint={tenant_watcher_endpoint}",);
    Arc::new(tenant_watcher_client::TenantWatcherClientImpl::new(
        tenant_watcher_endpoint,
        client_tls_config,
        Duration::from_secs(120),
    ))
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    setup_struct_logging().expect("Failed to setup logging");

    let args = CliArguments::parse();
    tracing::info!("{:?}", args);


```

Here is the snippet from `tools/genie/backend/src/main.rs`:

```
    let client = TenantWatcherClientImpl::new(endpoint, tls_config, Duration::from_secs(5));
    Ok(Arc::new(client))
}

async fn get_namespaces(_req: HttpRequest, state: web::Data<GenieState>) -> HttpResponse {
    let namespaces = state.granter.list_namespaces().await;
    match namespaces {
        Ok(namespaces) => HttpResponse::Ok().json(namespaces),
        Err(e) => {
            tracing::error!("Failed to list namespaces: {}", e);
            HttpResponse::InternalServerError().json("")
        }
    }
}

#[derive(Serialize, Deserialize)]
struct TenantItem {
    name: String,
    id: String,
    shard_namespace: String,
}

async fn get_tenants(_req: HttpRequest, state: web::Data<GenieState>) -> HttpResponse {
    // get the tenants for all namespaces
    let tenants = state.tenant_watcher_client.get_tenants("").await;
    match tenants {
        Ok(tenants) => HttpResponse::Ok().json(
            tenants
                .into_iter()
                .map(|t| TenantItem {

```

Here is the snippet from `services/tenant_watcher/client/tenant_watcher_client.go`:

```

	connLock sync.Mutex
	conn     *grpc.ClientConn
	client   tenantproto.TenantWatcherClient
}

func (cl *tenantWatcherClientImpl) getClient() (tenantproto.TenantWatcherClient, error) {
	cl.connLock.Lock()
	defer cl.connLock.Unlock()
	if cl.client == nil {
		log.Info().Msgf("Creating new tenant watcher client to %s with options %v", cl.endpoint, cl.grpcOpts)
		conn, err := grpc.NewClient(cl.endpoint, cl.grpcOpts...)
		if err != nil {
			return nil, err
		}
		cl.conn = conn
		cl.client = tenantproto.NewTenantWatcherClient(cl.conn)
	}
	return cl.client, nil
}

func (cl *tenantWatcherClientImpl) GetTenants(ctx context.Context, shardNamespace string) ([]*tenantproto.Tenant, error) {
	inner, err := cl.getClient()
	if err != nil {
		return nil, err
	}

	resp, err := inner.GetTenants(ctx, &tenantproto.GetTenantsRequest{ShardNamespace: shardNamespace})
	if err != nil {
		return nil, err

```

Here is the snippet from `services/request_insight/blob_exporter/blob_exporter.py`:

```
        )
    )

    return GrpcTokenExchangeClient(
        config.token_exchange_endpoint,
        os.environ["POD_NAMESPACE"],
        tls_config.get_client_tls_creds(config.central_client_mtls),
        options=options,
    )


def main():
    _ = StandardSignalHandler()
    setup_struct_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-file", default=pathlib.Path("/config/config.json"), type=pathlib.Path
    )
    args = parser.parse_args()
    log.info("Args %s", args)

    config = _load_config(args.config_file)
    log.info("Config: %s", config)

    start_http_server(9090)

    blob_persistence = CloudStoragePersistence(
        config.project_id,
        config.bucket_name,

```

Here is the snippet from `services/token_exchange/util/util.py`:

```
    parser.add_argument(
        "--path",
        help="relative path name",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".augment/service_token.txt"),
    )
    parser.add_argument(
        "--kube-config-file",
        help="Path to kube config file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )
    parser.set_defaults(action=None)

    args = parser.parse_args()
    _get_token(args)


if __name__ == "__main__":
    main()

```

Here is the snippet from `services/token_exchange/util/util.py`:

```
    client.login(args.cloud)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    for tenant in api_response["items"]:
        if tenant["metadata"]["name"] == args.tenant_name:
            return tenant["spec"]["tenant_id"]


def _get_token(args: argparse.Namespace):
    p: pathlib.Path = args.path
    if not p.is_absolute():
        raise ValueError("Path must be absolute")

    if not args.tenant_name:
        args.tenant_name = args.shard_namespace

    tenant_id = _get_tenant_id(args)
    if not tenant_id:
        logging.error("Tenant %s not found", args.tenant_name)
        sys.exit(1)

    url = _get_support_url(args.shard_namespace, tenant_id, args.cloud)
    logging.info("Visit and Copy the token to the clipboard")
    logging.info("Genie permissions are required to get a token")

```

Here is the snippet from `services/tenant_watcher/server/main.go`:

```
	if *configFile == "" {
		log.Fatal().Msg("Missing config file")
		os.Exit(1)
	}

	f, err := os.Open(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Error opening config file")
		os.Exit(1)
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		log.Fatal().Err(err).Msg("Error decoding config file")
		os.Exit(1)
	}
	log.Info().Msgf("Config: %v", config)

	if err := sanityCheck(&config); err != nil {
		log.Fatal().Err(err).Msg("Error in config file")
		os.Exit(1)
	}

	ctx := context.Background()

	clientset, err := CreateDynamicClient(*kubeconfig)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating clientset")

```

Here is the snippet from `research/data/slurm/diffs_retriever_dataset_v1_2023_05_23/gather_diff_out_shards.py`:

```
"""This is a script to gather sharded output from running repos_to_diff_pairs.py in sharded mode.

If you set --shard-index in repos_to_diff_pairs.py,
then data processing inputs and outputs will be sharded.
We want to gather these at the end of training.
eg. we want to transform
-  /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v8_shard0/
-  /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v8_shard1/
-  /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v8_shard2/
-  /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v8_shard3/
to
-  /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v8_consolidated/
"""

import argparse
import glob
import shutil
from pathlib import Path

from tqdm import tqdm


def process_args():
    parser = argparse.ArgumentParser(
        description="CLI for mapping repositories to Parquet files"
    )

    parser.add_argument("--path", help="path excluding _shardn suffix")

    args = parser.parse_args()

```

Here is the snippet from `research/gpt-neox/megatron/tokenizer/train_tokenizer.py`:

```
    )
    parser.add_argument(
        "-v",
        "--vocab_size",
        help="vocabulary size of tokenizer, default=52k",
        type=int,
        default=52000,
    )
    return parser.parse_args()


if __name__ == "__main__":

    args = parse_args()

    train_tokenizer(
        args.json_input_dir,
        save_path=args.tokenizer_output_path,
        tokenizer_type=args.tokenizer_type,
        vocab_size=args.vocab_size,
    )

```

Here is the snippet from `services/tenant_watcher/client/tenant_watcher_client.rs`:

```
        tracing::info!("Starting tenant cache");
        loop {
            let s = self
                .client
                .watch_tenants(self.shard_namespace.as_str())
                .await;
            tokio::pin!(s);
            loop {
                let res = s.recv().await;
                if let Some(res) = res {
                    let b = match res {
                        Ok(tenant_change) => {
                            self.update_tenants(tenant_change).await;
                            false
                        }
                        Err(e) => {
                            tracing::error!("Failed to receive tenant change: {:?}", e);
                            true
                        }
                    };
                    if b {
                        break;
                    }
                } else {
                    tracing::error!("Tenant change stream ended");
                    break;
                }
            }
        }
    }

```

The developer has file `/home/<USER>/surbhi-augment/services/tenant_watcher/util/tenant_watcher_util.py` open and has selected part of the code.

Here is the full file:

```
"""
CLI Utility to get tenant ids from tenant name
"""

import argparse
import logging
import pathlib
import sys
import json

from base.cloud.k8s.kubernetes_client import create_kubernetes_client
from base.logging.console_logging import setup_console_logging
from base.python.cloud import cloud as cloud_lib
import getpass
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

```

Here is the selected code:

```


def _get_tenant_id(args) -> str | None:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    for tenant in api_response["items"]:
        if tenant["metadata"]["name"] == args.tenant_name:
            return tenant["spec"]["tenant_id"]

def _get_tenant_ids_list(args) -> dict[str, str]:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    tenants_map = {}
    for tenant in api_response["items"]:
        tenants_map[tenant["metadata"]["name"]] = tenant["spec"]["tenant_id"]
    return tenants_map

def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()

    parser.ad

    parser.add_argument("--shard-namespace", help="The shard namespace", required=True)
    parser.add_argument(
        "--cloud",
        help="The cloud to use",
        required=True,
        choices=cloud_lib.get_cloud_list(),
    )
    parser.add_argument("--tenant-name", help="tenant name")
    parser.add_argument(
        "--path",
        help="relative path name",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".augment/service_token.txt"),
    )
    parser.add_argument(
        "--kube-config-file",
        help="Path to kube config file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )
    parser.set_defaults(action=None)

    args = parser.parse_args()
    if args


if __name__ == "__main__":
    main()
```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

test<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Hello! How can I assist you with your computer science or programming questions today?
<|eot_id|><|start_header_id|>user<|end_header_id|>

test<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Hello! How can I assist you with your computer science or programming questions today?
<|eot_id|><|start_header_id|>user<|end_header_id|>

test<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Hello! How can I assist you with your computer science or programming questions today?
<|eot_id|><|start_header_id|>user<|end_header_id|>

testsetset<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Hello! How can I assist you with your computer science or programming questions today?

<|eot_id|><|start_header_id|>user<|end_header_id|>

wahts a cli utility<|eot_id|><|start_header_id|>assistant<|end_header_id|>

A CLI (Command-Line Interface) utility is a program that is operated using commands typed into a terminal or command prompt. It's a way to interact with a computer program using text commands, rather than a graphical user interface (GUI).

In the context of the selected code, "CLI Utility to get tenant ids from tenant name" suggests that the program is a command-line tool that allows users to retrieve tenant IDs by providing a tenant name as input.<|eot_id|><|start_header_id|>user<|end_header_id|>

can you give me an exmaple of a command line prompt i can yse for token exchange util to get the tenant id<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Based on the code snippet you provided, here's an example of a command-line prompt you can use to get the tenant ID using the `token_exchange` util:

```
python token_exchange_util.py --shard-namespace <shard_namespace> --cloud <cloud_name> --tenant-name <tenant_name> --path <output_file_path> --kube-config-file <kube_config_file_path>
```

Replace the placeholders with your actual values:

* `<shard_namespace>`: the namespace of the shard
* `<cloud_name>`: the name of the cloud (e.g., "aws", "gcp", etc.)
* `<tenant_name>`: the name of the tenant
* `<output_file_path>`: the path where you want to save the token (e.g., `/home/<USER>/token.txt`)
* `<kube_config_file_path>`: the path to your Kubernetes configuration file (e.g., `~/.kube/config`)

For example:

```
python token_exchange_util.py --shard-namespace my-shard --cloud aws --tenant-name my-tenant --path /home/<USER>/token.txt --kube-config-file ~/.kube/config
```

This command will execute the `token_exchange_util` script, which will retrieve the tenant ID and write it to the specified output file.<|eot_id|><|start_header_id|>user<|end_header_id|>

i have two functions i want to be able to parse, the action "get tenant id" or "list tenant ids" with the only difference that get tenant id needs tenant name and the other doesnt<|eot_id|><|start_header_id|>assistant<|end_header_id|>

You can modify the `argparse` parser to accommodate both actions. Here's an example:
```
parser = argparse.ArgumentParser()

parser.add_argument("--shard-namespace", help="The shard namespace", required=True)
parser.add_argument(
    "--cloud",
    help="The cloud to use",
    required=True,
    choices=cloud_lib.get_cloud_list(),
)
parser.add_argument(
    "--path",
    help="relative path name",
    type=pathlib.Path,
    default=pathlib.Path.home().joinpath(".augment/service_token.txt"),
)
parser.add_argument(
    "--kube-config-file",
    help="Path to kube config file",
    type=pathlib.Path,
    default=pathlib.Path.home().joinpath(".kube", "config"),
)

subparsers = parser.add_subparsers(dest="action", required=True)

get_tenant_id_parser = subparsers.add_parser("get-tenant-id")
get_tenant_id_parser.add_argument("--tenant-name", help="tenant name", required=True)

list_tenant_ids_parser = subparsers.add_parser("list-tenant-ids")

args = parser.parse_args()
```
This sets up two subparsers: `get-tenant-id` and `list-tenant-ids`. The `get-tenant-id` subparser requires the `--tenant-name` argument, while the `list-tenant-ids` subparser does not.

You can then use the `args.action` attribute to determine which action to perform:
```
if args.action == "get-tenant-id":
    # Call _get_tenant_id function with args.tenant_name
    tenant_id = _get_tenant_id(args)
    print(tenant_id)
elif args.action == "list-tenant-ids":
    # Call _get_tenant_ids_list function
    tenants_map = _get_tenant_ids_list(args)
    print(tenants_map)
```
With this setup, you can run the script with either of the following commands:
```
python token_exchange_util.py get-tenant-id --shard-namespace my-shard --cloud aws --tenant-name my-tenant --path /home/<USER>/token.txt --kube-config-file ~/.kube/config
```
or
```
python token_exchange_util.py list-tenant-ids --shard-namespace my-shard --cloud aws --path /home/<USER>/token.txt --kube-config-file ~/.kube/config
```
Note that I've used `dest="action"` to specify that the subparser name should be stored in the `args.action` attribute. This allows you to easily determine which action to perform based on the subparser used.<|eot_id|><|start_header_id|>user<|end_header_id|>

i need this for tenant watcher here



def _get_tenant_id(args) -> str | None:

client = create_kubernetes_client(args.kube_config_file)

api_instance = client.get_custom_api(args.cloud)

api_response = api_instance.list_namespaced_custom_object(

group="eng.augmentcode.com",

version="v1",

namespace=args.shard_namespace,

plural="tenants",

 )

for tenant in api_response["items"]:

if tenant["metadata"]["name"] == args.tenant_name:

return tenant["spec"]["tenant_id"]

def _get_tenant_ids_list(args) -> dict[str, str]:

client = create_kubernetes_client(args.kube_config_file)

api_instance = client.get_custom_api(args.cloud)

api_response = api_instance.list_namespaced_custom_object(

group="eng.augmentcode.com",

version="v1",

namespace=args.shard_namespace,

plural="tenants",

 )

tenants_map = {}

for tenant in api_response["items"]:

tenants_map[tenant["metadata"]["name"]] = tenant["spec"]["tenant_id"]

return tenants_map




def main():

"""Main function."""

setup_console_logging()

parser = argparse.ArgumentParser()




parser.ad




parser.add_argument("--shard-namespace", help="The shard namespace", required=True)

parser.add_argument(

"--cloud",

help="The cloud to use",

required=True,

choices=cloud_lib.get_cloud_list(),

 )

parser.add_argument("--tenant-name", help="tenant name")

parser.add_argument(

"--path",

help="relative path name",

type=pathlib.Path,

default=pathlib.Path.home().joinpath(".augment/service_token.txt"),

 )

parser.add_argument(

"--kube-config-file",

help="Path to kube config file",

type=pathlib.Path,

default=pathlib.Path.home().joinpath(".kube", "config"),

 )

parser.set_defaults(action=None)




args = parser.parse_args()

if args





if __name__ == "__main__":

main()<|eot_id|><|start_header_id|>assistant<|end_header_id|>
