Here is the rewritten code:
```
def _get_tenant_id(args) -> str | None:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    for tenant in api_response["items"]:
        if tenant["metadata"]["name"] == args.tenant_name:
            return tenant["spec"]["tenant_id"]

def _get_tenant_ids_list(args) -> dict[str, str]:
    client = create_kubernetes_client(args.kube_config_file)
    api_instance = client.get_custom_api(args.cloud)
    api_response = api_instance.list_namespaced_custom_object(
        group="eng.augmentcode.com",
        version="v1",
        namespace=args.shard_namespace,
        plural="tenants",
    )
    tenants_map = {}
    for tenant in api_response["items"]:
        tenants_map[tenant["metadata"]["name"]] = tenant["spec"]["tenant_id"]
    return tenants_map

def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()

    subparsers = parser.add_subparsers(dest="action", required=True)

    get_tenant_id_parser = subparsers.add_parser("get-tenant-id")
    get_tenant_id_parser.add_argument("--tenant-name", help="tenant name", required=True)

    list_tenant_ids_parser = subparsers.add_parser("list-tenant-ids")

    parser.add_argument("--shard-namespace", help="The shard namespace", required=True)
    parser.add_argument(
        "--cloud",
        help="The cloud to use",
        required=True,
        choices=cloud_lib.get_cloud_list(),
    )
    parser.add_argument(
        "--path",
        help="relative path name",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".augment/service_token.txt"),
    )
    parser.add_argument(
        "--kube-config-file",
        help="Path to kube config file",
        type=pathlib.Path,
        default=pathlib.Path.home().joinpath(".kube", "config"),
    )

    args = parser.parse_args()

    if args.action == "get-tenant-id":
        tenant_id = _get_tenant_id(args)
        print(tenant_id)
    elif args.action == "list-tenant-ids":
        tenants_map = _get_tenant_ids_list(args)
        print(tenants_map)

if __name__ == "__main__":
    main()
```
I've added the `subparsers` and modified the `main` function to support both "get tenant id" and "list tenant ids" actions. The `get-tenant-id` subparser requires the `--tenant-name` argument, while the `list-tenant-ids` subparser does not.
