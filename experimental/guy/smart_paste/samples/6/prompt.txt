<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Aug<PERSON>, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `research/core/tests/test_layout_helper.py`:

```
            list_of_tokens=[[i] for i in range(5)] * 500 + [[5]] * 500,
            separator_tokens=[2],
            max_total_tokens=9,
            append_separator_at_start=True,
            append_separator_at_end=True,
            truncate_tail_then_reverse=False,
        )
        assert prompt_tokens == [2, 0, 2, 1, 2, 2, 2, 3, 2]

        prompt_tokens = helper.concat_list_of_tokens(
            list_of_tokens=[[i] for i in range(5)] * 500 + [[5]] * 500,
            separator_tokens=[2],
            max_total_tokens=9,
            append_separator_at_start=True,
            append_separator_at_end=True,
            truncate_tail_then_reverse=True,
        )
        assert prompt_tokens == [2, 3, 2, 2, 2, 1, 2, 0, 2]

    def test_make_comment_chunk_layout(self):
        """Tests `make_comment_chunk_layout`."""
        dummy_input = ModelInput(
            prefix="PREFIX",
            suffix="SUFFIX",
            retrieved_chunks=[
                _create_random_chunk(

```

Here is the snippet from `experimental/hieu/cuda_study/gemm_cuda_kernels/utils.h`:

```
    thrust::host_vector<float> hv_fp32 = dv_fp32;
    return hv_fp32;
}


template <typename T> thrust::device_vector<T> to_device(const thrust::host_vector<float> &v);

template <>
thrust::device_vector<float> to_device(const thrust::host_vector<float> &v) {
    thrust::device_vector<float> dv = v;
    return dv;
}


struct fp32_to_fp16_functor {
    __device__ __half operator()(float val) const {
        return __float2half(val);
    }
};


template <>
thrust::device_vector<__half> to_device(const thrust::host_vector<float> &v) {
    thrust::device_vector<float> dv = v;
    thrust::device_vector<__half> dv_T(v.size());
    thrust::transform(dv.begin(), dv.end(), dv_T.begin(), fp32_to_fp16_functor());
    return dv_T;
}



```

Here is the snippet from `base/fastforward/fwd_utils.py`:

```

    padded_step = PaddedStepFunction(step, round_sizes)

    def step_fn(
        tokens: Sequence[int], attn: cached_attention.Attention
    ) -> fwd.ModelOutput:
        start = 0
        num_tokens = len(tokens)
        combined_logits = []
        while start < num_tokens:
            remaining_length = num_tokens - start
            round_size = min(max_round_size, remaining_length)
            logits = padded_step(tokens[start : start + round_size], attn).checked_cast(
                torch.Tensor
            )
            assert logits.shape[0] == round_size
            combined_logits.append(logits)
            start += round_size
        assert start == num_tokens
        return fwd_torch.TorchLogits2D(torch.cat(combined_logits, dim=0))

    return step_fn


def generate(
    step: fwd.ForwardStepFn,
    attn: cached_attention.BasicAttention,
    prefix: list[int],
    max_tokens: int,
) -> tuple[list[int], float]:

```

Here is the snippet from `base/static_analysis/common.py`:

```
    index = all_siblings.index(node)
    sibling_index = index + offset
    if 0 <= sibling_index < len(all_siblings):
        return all_siblings[sibling_index]
    else:
        return None


def optional_map(x: Optional[AT], f: Callable[[AT], BT]) -> Optional[BT]:
    """Apply f to x if x is not None, otherwise return None."""
    return None if x is None else f(x)


def deduplicate(
    xs: Iterable[AT], key: Callable[[AT], Hashable] | None = None
) -> list[AT]:
    """Return a new list with duplicates removed.

    If `key` is provided, will use it to compute uniqueness.
    """
    if key is None:
        return list(dict.fromkeys(xs))
    unique_dict = dict()
    for x in xs:
        k = key(x)
        if k not in unique_dict:
            unique_dict[k] = x
    return list(unique_dict.values())



```

Here is the snippet from `research/utils/skip_token.py`:

```
        suffix_replacement_text += leading_whitespaces + closing + segments.pop(0)
        skipped_suffix += leading_whitespaces + closing
    return ReplaceSkipTokenResult(
        completion_text, suffix_replacement_text, skipped_suffix
    )

```

Here is the snippet from `research/core/utils_for_str.py`:

```
def get_last_n_lines(doc: str, n: int = 1):
    """Get the last N lines of a given document."""
    if n < 0:
        raise ValueError(f"Invalid parameter n={n}, which should < 0.")
    elif n == 0:
        return ""
    else:
        lines = doc.splitlines(True)
        return "".join(lines[-n:])


def get_first_n_lines(doc: str, n: int = 1):
    """Get the first N lines of a given document."""
    if n < 0:
        raise ValueError(f"Invalid parameter n={n}, which should < 0.")
    elif n == 0:
        return ""
    else:
        lines = doc.splitlines(True)
        return "".join(lines[:n])


#################################################################################
# String Creation
#################################################################################


def trim_string(s: str, stop_strs: Sequence[str]) -> str:
    """Trim the input string if met any stop strings."""
    pattern = "|".join(re.escape(stop_str) for stop_str in stop_strs)

```

Here is the snippet from `experimental/hieu/cutie/matrix_transpose/matrix_transpose_lib.py`:

```
"""Matrix transpose library."""
# pylint: disable=no-name-in-module

import torch
from matrix_transpose_api import transpose_fp32  # type: ignore


def transpose(x: torch.Tensor) -> torch.Tensor:
    y = torch.zeros(x.size(1), x.size(0), dtype=x.dtype, device=x.device)

    if x.dtype == torch.float32:
        transpose_fp32(x.data_ptr(), x.size(0), x.size(1), y.data_ptr())
    else:
        raise NotImplementedError(f"{x.dtype=} is not supported.")

    return y

```

Here is the snippet from `clients/vscode/src/truncation.ts`:

```
export function takeLastNTokens(str: string, maxTokenLength = 1000): string {
    // TODO - actually tokenize the string and take the last N tokens; substring will have to do for now
    return str.slice(0 - maxTokenLength);
}

```

Here is the snippet from `research/models/fastforward_models.py`:

```
            extra_outputs: a hook that used to output extra information.

        Returns:
            Object storing the generated text and other information.
        """
        prompt_tokens, _ = self.prompt_formatter.prepare_prompt(inputs)
        if extra_outputs:
            extra_outputs.prompt_tokens = prompt_tokens
        text = self.raw_generate(prompt_tokens, options)
        if isinstance(self.tokenizer, StarCoderBaseTokenizer):
            # These family of tokenizers support skip tokens
            result = replace_skip_tokens(text, inputs.suffix, self.tokenizer.skip_token)
            if extra_outputs:
                extra_outputs.suffix_replacement_text = result.suffix_replacement_text
                extra_outputs.skipped_suffix = result.skipped_suffix
            return result.completion_text
        else:
            return text

    def _forward_pass_single_logits(self, input_tokens: torch.Tensor) -> torch.Tensor:
        """Runs a raw forward pass on a single tokenized input.


```

Here is the snippet from `experimental/dxy/xtry/arithmetic.py`:

```
    """Convert a number to a string."""
    if isinstance(obj, Number):
        output_digits = sorted(obj.digits, key=lambda x: -x[-1])
        return "".join(map(str, [digit[0] for digit in output_digits]))
    else:
        return str(int(obj))

```

Here is the snippet from `experimental/dxy/edits/notebooks/regular/old-test-11-28.ipynb`:

```
      "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n",
      "    for idx, message in enumerate(messages):\n",
      "        role = \"user\" if idx == 0 else \"assistant\"\n",
      "        request_messages.append({\"role\": role, \"content\": message})\n",
      "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n",
      "instruction: replace double quotes with single quote\n",
      "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n",
      "\u001b[32mOpenAIChatModels = typing.Literal['gpt-4', 'gpt-3.5-turbo']\n",
      "\n",
      "logger = logging.getLogger(__name__)\n",
      "\n",
      "\n",
      "def generate_response_via_chat(\n",
      "    messages: list[str],\n",
      "    system_prompt: str,\n",
      "    temperature: float = 0.2,\n",
      "    max_tokens: int = 256,\n",
      "    model: OpenAIChatModels = 'gpt-4',\n",
      ") -> str:\n",

```

Here is the snippet from `research/gpt-neox/eval_tasks/eval_adapter.py`:

```
                memory_snapshot.clear_memory_snapshot_cache()

            # convert to tokens and trim

            # TODO: converting the prompts to tokens and back again is not a
            # great way to handle truncation, since the callee simply tokenizes
            # the prompt again. AU-192
            max_prompt_tokens = self.max_length - self.max_gen_toks
            prompts_tokens = [self.tokenizer.encode(x) for x in prompts]
            prompts_tokens = [x[-max_prompt_tokens:] for x in prompts_tokens]
            stop_tokens = [self.tokenizer.encode(i) for i in until]
            cont = self.generate(
                text_tokens=prompts_tokens,
                stop_tokens=stop_tokens,
                recompute=self.neox_args.recompute,
                mem_object_names=memories,
            )

            if not cont:
                s = ["" for _ in prompts]
            else:
                s = [x["text"] for x in cont]

            # re-attach the index for output re-sorting

```

Here is the snippet from `clients/vscode/src/__mocks__/mock-filesystem.ts`:

```
            if (!(dir instanceof Directory)) {
                throw new NotADirectoryException(pathName);
            }
            const res: T[] = [];
            for (const [name, entry] of dir) {
                if (entry instanceof Directory) {
                    res.push(xlator(name, FileType.dir, undefined, entry.mtime));
                } else {
                    res.push(xlator(name, FileType.file, entry.contents.length, entry.mtime));
                }
            }

            // return sorted list of results so tests can control the order in which
            // files are enumerated
            return res.sort();
        }

        makeDir(pathName: string): void {
            const [dir, pathList] = this.resolveToDirAndName(pathName);
            if (pathList.length === 0) {
                // This only happens if the pathname consists solely of separators
                throw new FileExistsException(pathName);
            }
            const trailingName = pathList.at(-1)!;

```

Here is the snippet from `research/gpt-neox/eval_tasks/eval_adapter.py`:

```
        return self.tokenizer.encode(string)

    def tok_decode(self, tokens):
        return self.tokenizer.decode(tokens)

    Prompt = tuple[str, tuple[str]]  # <prompt string, list of memory names>
    Stops = list[str]
    GenRequest = tuple[Prompt, Stops]  # <prompt, list of stop strings>

    def greedy_until(self, requests: list[GenRequest]):
        """Greedy generation.

        Greedy until is lm_eval harness' way to say "do greedy generation" - necessary for some tasks.
        the eval harness dispatches requests to the model, and the model does argmax generation, the results of which
        are returned to the eval harness to evaluate.
        """
        self.model.module.inference_mode(use_cache=True)  # tell model to cache kv pairs

        # Group equivalent prompts together, to run as a batch. Add an id to
        # each request, for later re-sorting into input order.
        enum_requests = [x for x in enumerate(requests)]

```

Here is the snippet from `services/completion_host/single_model_server/single_round_handler.py`:

```
            except ValueError:
                text.append(tokenizer.detokenize(token_ids[start:]))
                break

            text.append(tokenizer.detokenize(token_ids[start:next_skip]))
            # Identify the portion of string skipped
            for i in range(n_skipped, len(suffix)):
                if not suffix[i].isspace():
                    # index of first char after whitespaces
                    ws_tail = i
                    break
            else:
                # there is only white space.  Cannot match skip to suffix.
                break
            for closing in closing_strings:
                if suffix[ws_tail : ws_tail + len(closing)] == closing:
                    skipped_chars = suffix[n_skipped : ws_tail + len(closing)]
                    text.append(skipped_chars)
                    skipped_text.append(skipped_chars)
                    n_skipped += len(skipped_chars)
                    break
            else:  # be conservative and stop the generation here

```

Here is the snippet from `research/models/gpt_neox_helper.py`:

```
    return generated_tokens


def pad_tensor(data: list[list[int]], pad_idx: int) -> tuple[torch.Tensor, list[int]]:
    """Convert a list of variable length token-sequences into a torch tensor, by padding to the longest token-sequence length.

    Args:
        data: a list of variable length token-sequences into a torch tensor
        pad_idx: index of padding token

    Returns:
        a torch tensor of shape (number of token-sequences, length of longest token-sequence),
        a list of ints such that num_pad_toks[i] is the number of padding tokens in padded_data[i, :]
    """
    max_len = max([len(datum) for datum in data])
    padded_data = torch.ones((len(data), max_len)) * pad_idx
    num_pad_toks = []

    for i, datum in enumerate(data):
        datum_len = len(datum)
        padded_data[i, :datum_len] = torch.tensor(datum, dtype=torch.int64)
        num_pad_toks.append(max_len - datum_len)  # Count padding toks

    return padded_data.long(), num_pad_toks


def unpad_tensor(

```

Here is the snippet from `base/static_analysis/common.py`:

```
"""Utils that are commonly used across the static_analysis package."""

from __future__ import annotations

import collections
import difflib
import sys
import types
import typing
from dataclasses import dataclass
from functools import cached_property
from itertools import accumulate
from pathlib import Path
from typing import (
    Callable,
    Collection,
    Hashable,
    Iterable,
    Literal,
    Mapping,
    Optional,
    Sequence,
    TypeVar,
    Union,
    cast,
    get_args,
)

import tree_sitter
from typing_extensions import TypeGuard, assert_never

```

Here is the snippet from `experimental/vzhao/data/pandas_functions.py`:

```
    return pd.Series(
        {
            "ppg": json.dumps(ppg),
            "ppl": json.dumps(ppl),
            "retrieved_chunks": common.serialize_retrieved_chunks(
                retrieved_chunks,
                add_parent_doc_text=True,
            ),
            "retrieval_rank": json.dumps(retrieval_rank),
        }
    )


def _log_with_time(*args):
    print(datetime.datetime.now().strftime("%d.%b %Y %H:%M:%S"), *args)


# class ComputeSequenceLength:
#     """Computes sequence length for `prefix`, `suffix` and `middle`."""

#     def __init__(self, tokenizer_fn=StarCoderTokenizer):
#         self.tokenizer_fn = tokenizer_fn
#         self.tokenizer = None

#     @map_parquet.passthrough_feature(bound=True)
#     @map_parquet.allow_unused_args(bound=True)
#     def __call__(self, prefix, suffix, middle) -> pd.Series:
#         if self.tokenizer is None:
#             self.tokenizer = self.tokenizer_fn()


```

Here is the snippet from `research/utils/process_map.py`:

```

    tag_f = _TaggedFunc(f, key_args)
    arg_tuples = zip(range(n), *f_args)
    results: list["AT | None"] = [None] * n

    with (
        ProcessPool(max_workers, initializer=cast(Any, initializer)) as pool,
        tqdm(total=n, desc=desc, **tqdm_args) as pbar,
    ):
        future = pool.map(tag_f, arg_tuples, chunksize=chunksize, timeout=timeout)
        iterator = future.result()

        while True:
            try:
                i, r = next(iterator)
                results[i] = r
            except StopIteration:
                break
            except FutureTimeout:
                print(f"Chunk ({chunksize=}) timeout in {timeout}s.")
            except ProcessExpired as error:
                print("%s. Exit code: %d" % (error, error.exitcode))
            pbar.update()

    return [results[i] for i in range(n)]

```

Here is the snippet from `experimental/dxy/code/str_helper.py`:

```
            return "\n".join(result_lines[:index])
    return result

```

Here is the snippet from `clients/vscode/src/__tests__/__utils__/waitable-mock.ts`:

```
// This is from https://github.com/jestjs/jest/issues/7432
export const waitableJestFn = (times: number): WaitableMock => {
    let _resolve: Function;
    const promise = new Promise<void>((resolve) => (_resolve = resolve));

    let i: number = 0;
    const mock = jest.fn(() => {
        i++;
        if (i >= times) {
            _resolve();
        }
    }) as WaitableMock; // force casting

    mock.waitUntilComplete = () => promise;

    return mock;
};

type WaitableMock = jest.Mock & {
    waitUntilComplete(): Promise<void>;
};

```

Here is the snippet from `base/static_analysis/common.py`:

```
    # to make sure that we only use files that are actually parsable code.
    return file_ext_to_lang_id


def guess_lang_from_fp(fname: str | Path | None) -> LanguageID | None:
    """Guess language id from the file path."""
    if fname is None:
        return None
    extension = Path(fname).suffix.lower()
    return file_ext_to_lang_id.get(extension)


def get_vscode_language_name(lang: LanguageID) -> str:
    """Return the VSCode language name for a given static_analysis LanguageID."""
    # so far, all our supported language ids are the same as the ones used by VS Code.
    return lang


def decode_bytes(byte_seq: bytes) -> str:
    """Decodes a bytes object into a string assuming utf-8 encoding."""
    return byte_seq.decode("utf-8")


def shorten_str(
    string: str,
    max_len: int = 60,
    omit_mode: Literal["middle", "right", "left"] = "middle",
) -> str:
    """Shorten a string to some maximum length. Omit some parts if too long.


```

Here is the snippet from `experimental/hieu/cuda_study/gemm_cuda_kernels/utils.h`:

```
#pragma once

#include <cstdio>
#include <iomanip>
#include <iostream>

#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <cuda_bf16.h>

#include <thrust/host_vector.h>
#include <thrust/iterator/counting_iterator.h>
#include <thrust/device_vector.h>
#include <thrust/transform.h>
#include <curand_kernel.h>


struct fp16_to_fp32_functor {
    __device__ float operator()(__half val) const {
        return __half2float(val);
    }
};


template <typename T> thrust::host_vector<float> to_host(const thrust::device_vector<T> &v);

template <>
thrust::host_vector<float> to_host(const thrust::device_vector<__half> &v) {
    thrust::device_vector<float> dv_fp32 = v;
    thrust::transform(v.begin(), v.end(), dv_fp32.begin(), fp16_to_fp32_functor());

```

The developer has file `/home/<USER>/augment/research/next_edits/edit_localization_stages.py` open and has selected part of the code.

Here is the full file:

```
        except Exception:
            logger.warning("Failed %s: couldn't format query.", problem.pr_meta)
            continue

        for chunk in retrieval_problem.positive_chunks:
            tokens += document_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=str(chunk.parent_doc.path),
                ),
            ).tokens()
            # Add the score
            tokens += [
                *tokenizer.tokenize_safe(str(0)),
                tokenizer.special_tokens.end_of_key,
            ]

        # Combine the negative chunks
        negative_chunks = []
        negative_retrieval_strategies = (
            config.negative_retrieval_strategies
            or retrieval_problem.negative_chunks.keys()
        )

        for strategy in negative_retrieval_strategies:
            if strategy not in retrieval_problem.negative_chunks:
                logger.warning(
                    "Couldn't find the strategy %s in the input. Found %s.",
                    strategy,
                    retrieval_problem.negative_chunks.keys(),
                )
                continue
            # TODO(arun): We should actually swizzle these so that we always have a
            # good distribution of the different strategies.
            negative_chunks.extend(retrieval_problem.negative_chunks[strategy])

        for chunk in negative_chunks:
            tokens += document_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=str(chunk.parent_doc.path),
                )
            ).tokens()
            # Add the score
            tokens += [
                *tokenizer.tokenize_safe(str(-1)),
                tokenizer.special_tokens.end_of_key,
            ]

        yield to_token_array(tokens, tokenizer.vocab_size)


class ChunkCache:
    """Cache document chunking results."""

    def __init__(self, chunker: Chunker):
        self.chunker = chunker
        self._doc_to_chunks_cache: dict[DocumentId, list[Chunk]] = {}

    def get_chunks(self, doc: Document) -> list[Chunk]:
        if doc.id not in self._doc_to_chunks_cache:
            self._doc_to_chunks_cache[doc.id] = (
                self.chunker.split_into_chunks(doc) or []
            )
        return self._doc_to_chunks_cache[doc.id]


# Create one per process.
@lru_cache()
def _get_chunk_cache(chunker_options: Sequence[tuple[str, Hashable]]) -> ChunkCache:
    chunker_config = dict(chunker_options)
    cls_name = chunker_config.pop("name")
    return ChunkCache(chunking_functions.get_chunker(cls_name, **chunker_config))


@dataclass
class Timer:
    start_time_s: float | None = None
    """The start time of the timer if started."""

    cumulative_time_s: float = 0.0
    """The cumulative elapsed time of the timer."""

    def start(self):
        """Start the timer if it hasn't already been started."""
        if self.start_time_s is None:
            self.start_time_s = time.time()

    def pause(self):
        """Pause the timer if it is running."""
        if self.start_time_s is not None:
            self.cumulative_time_s += time.time() - self.start_time_s
            self.start_time_s = None

    @property
    def elapsed_s(self):
        """Get the elapsed time of the timer."""
        if self.start_time_s is not None:
            return self.cumulative_time_s + time.time() - self.start_time_s
        else:
            return self.cumulative_time_s

    def __enter__(self):
        self.start()
        return self

    def __exit__(self, *args):
        self.pause()


@lru_cache()
def repo_timer(repo_name: str) -> Timer:
    return Timer()


@lru_cache()
def load_instructions(instructions_path: Path | str) -> Mapping[PRMeta, list[str]]:
    """Load generated instructions."""
    ret = {}

    for shard in Path(instructions_path).glob("*.parquet"):
        # There are some empty shards that can't be loaded when filtering columns,
        # so we manually exclude them here.
        if shard.stat().st_size < 1024:
            continue
        for dct in pd.read_parquet(
            shard, columns=["repo_name", "number", "title", "body", "instructions"]
        ).to_dict(orient="records"):
            pr_meta = PRMeta(
                repo_name=dct["repo_name"],
                pr_number=dct["number"],
                title=dct["title"],
                body=dct["body"],
            )
            ret[pr_meta] = [
                i
                for instructions in dct["instructions"].values()
                if instructions is not None
                for i in instructions
            ]

    return ret


@lru_cache()
def create_retriever(config_json: str) -> RetrievalDatabase:
    from research.eval.harness import factories

    retriever = factories.create_retriever(json.loads(config_json))
    assert isinstance(retriever, RetrievalDatabase)

    return retriever


[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

```

Here is the selected code:

```
T = TypeVar("T")


def interleave_sequences(sequences: Sequence[Iterable[T]]) -> Iterable[T]:
    """Interleave multiple sequences into a single sequence.

    Args:
        sequences: A sequence of sequences.

    Returns:
        A single sequence with elements from the input sequences interleaved.
    """
    iterators = [iter(sequence) for sequence in sequences]
    while any(iterators):
        for iterator in list(iterators):
            try:
                yield next(iterator)
            except StopIteration:
                iterators.remove(iterator)
```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Is there a more elegant way to implement this function?<|eot_id|><|start_header_id|>assistant<|end_header_id|>
