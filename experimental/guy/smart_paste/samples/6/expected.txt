Here is the rewritten code:
```
T = TypeVar("T")


def interleave_sequences(sequences: Sequence[Iterable[T]]) -> Iterable[T]:
    """Interleave multiple sequences into a single sequence.

    Args:
        sequences: A sequence of sequences.

    Returns:
        A single sequence with elements from the input sequences interleaved.
    """
    return itertools.chain.from_iterable(itertools.zip_longest(*sequences))
```
This implementation uses the `itertools` module to interleave the sequences in a more concise and efficient way.
