Here is the rewritten code:
```
T = TypeVar("T")


def interleave_sequences(sequences: Sequence[Iterable[T]]) -> Iterable[T]:
    """Interleave multiple sequences into a single sequence.

    Args:
        sequences: A sequence of sequences.

    Returns:
        A single sequence with elements from the input sequences interleaved.
    """
    return itertools.chain.from_iterable(itertools.zip_longest(*sequences))
```
Note that I replaced the entire implementation with a single line using `itertools.chain` and `itertools.zip_longest`. This is a more elegant and efficient way to interleave the sequences.
