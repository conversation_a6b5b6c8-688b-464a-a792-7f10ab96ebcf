Yes, you can use the `itertools` module to implement the `interleave_sequences` function in a more elegant way. Here's an example:
```
import itertools

def interleave_sequences(sequences):
    return itertools.chain.from_iterable(itertools.zip_longest(*sequences))
```
This implementation uses the `itertools.zip_longest` function to interleave the sequences, and then uses the `itertools.chain.from_iterable` function to flatten the resulting iterator of tuples into a single iterator.

This implementation is more concise and efficient than the original implementation, and it also handles the case where the input sequences have different lengths.
