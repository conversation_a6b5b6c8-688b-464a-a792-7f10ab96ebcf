Here is the modified selected code:

```
    ALLOWED_TOKENIZERS = (
        StarCoderTokenizer,
        StarCoder2Tokenizer,
        DeepSeekCoderBaseTokenizer,
        CodestralTokenizer,
        tokenizer_adapters.DeepSeekCoderBaseWrappedProdTokenizer,
        tokenizer_adapters.Llama3BaseWrappedProdTokenizer,
        tokenizer_adapters.StarCoder2WrappedProdTokenizer,
        tokenizer_adapters.StarCoder1WrappedProdTokenizer,
    )

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        """Create a simple prompt for the Star Coder model.

        Args:
            input: an instance of ModelInput class, containing all raw input.
            tokenizer: the self.tokenizer.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation: number of prefix chars post-truncation
                num_suffix_chars_post_truncation: number of suffix chars post-truncation
        """
        metadata = {}

        assert isinstance(self.tokenizer, self.ALLOWED_TOKENIZERS)
```
