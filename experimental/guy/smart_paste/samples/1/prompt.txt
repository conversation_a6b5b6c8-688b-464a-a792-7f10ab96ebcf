<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I currently have the file `research/core/prompt_formatters.py` open, and I am actively working on it. Here is an excerpt from it:

```
"""Different kinds of prompt formatter.

pytest research/core/prompt_formatters_test.py
"""

import logging
from dataclasses import dataclass
from typing import Annotated, Optional, Sequence

from megatron.tokenizer.tokenizer import (
    AbstractTokenizer,
    CodestralTokenizer,
    DeepSeekCoderBaseTokenizer,
    LLama3BaseTokenizer,
    StarCoder2Tokenizer,
    StarCoderBaseTokenizer,
    StarCoderTokenizer,
)

from base.prompt_format.util import trailing_n
from base.prompt_format_completion import rogue_prompt_formatter
from base.prompt_format_completion.stateless_caching import (
    segment_prefix_stateless,
    segment_suffix_stateless,
)
from research.core import prod_adapters
from research.core.abstract_prompt_formatter import (
    AbstractPromptFormatter,
    register_prompt_formatter,
)
...
    def create_default_tokenizer(self) -> StarCoderBaseTokenizer:
        if self.for_starcoder2:
            return StarCoder2Tokenizer()
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        """Create a simple prompt for the Star Coder model.

        A FIM prompt will be prepared iff model_input.suffix is not empty,
        and self.max_suffix_tokens is not zero (it can be either positive
        or negative, just not zero).

        If a path is provided and self.include_filename_in_prompt is set, it will be
        prepended to the path with appropriate special tokens. Also, if
        self.github_stars is set, it will also be prepended to the path using
        the StarCoder format.

        Args:
            input: an instance of ModelInput class, containing all raw input.
            tokenizer: the self.tokenizer.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
...
        "Truncate filename to this many tokens from left",
    ] = 25
    max_number_chunks: Annotated[
        Optional[int], "Maximum number of retrieved chunks to include."
    ] = None
    prepend_bos_token: Annotated[
        bool, "If true, prepend BOS token to the beginning of the prompt."
    ] = False

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        """Create a simple prompt for the Star Coder model.

        Args:
            input: an instance of ModelInput class, containing all raw input.
            tokenizer: the self.tokenizer.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation: number of prefix chars post-truncation
                num_suffix_chars_post_truncation: number of suffix chars post-truncation
        """
        metadata = {}
        assert isinstance(
            self.tokenizer,
            (
                StarCoderTokenizer,
                DeepSeekCoderBaseTokenizer,
                StarCoder2Tokenizer,
                CodestralTokenizer,
            ),
        )

        preamble_tokens = []
        if self.prepend_bos_token:
            preamble_tokens.append(self.tokenizer.bos_id)
        if self.preamble:
            preamble_tokens.extend(self.tokenizer.tokenize_safe(self.preamble))

        prefix_chars = model_input.prefix
        if self.prefix_char_offset > 0:
            prefix_chars = prefix_chars[: -self.prefix_char_offset]
        prefix_tokens: list[int] = self.tokenizer.tokenize_safe(prefix_chars)
        prefix_prepend_tokens: list[int] = []
        if self.prepend_path_to_prefix and model_input.path:
            prefix_prepend_tokens += (
                [self.tokenizer.filename_id]
                + trailing_n(
...
                        self.tokenizer.tokenize_safe(chunk_path),
                        self.max_filename_tokens,
                    )
                    tokenized_block += [self.tokenizer.retrieval_body_id]
                    tokenized_block += self.tokenizer.tokenize(chunk.text)

            if accum + len(tokenized_block) > retrieval_budget:
                break

            tokenized_blocks.append(tokenized_block)
            accum += len(tokenized_block)

        # Order chunks such that the most important chunk is at the end of the block,
        # to be closest to the context.
        tokenized_blocks.reverse()

        for block in tokenized_blocks:
            retrieval_tokens.extend(block)

        if self.add_retrieval_after_context:
            prompt_tokens = preamble_tokens + context_tokens + retrieval_tokens
        else:
            prompt_tokens = preamble_tokens + retrieval_tokens + context_tokens

        if self.nearby_prefix_char_len > 0:
...
    max_filename_tokens: int = 25
    """Truncate filename to this many tokens from left"""

    component_order: Sequence[str] = ("retrieval", "signature", "prefix", "suffix")
    """Order of components: prefix, suffix, signatures, retrieval, nearby_prefix."""

    # Caching parameters
    context_quant_token_len: int = 0
    """The prefix and suffix are quantized to multiple of this number of tokens from beginning/end of file."""

    nearby_prefix_token_len: int = 0
    """The number of tokens in nearby prefix component (prefix close to cursor)."""

    nearby_prefix_token_overlap: int = 0
    """The number of tokens in nearby prefix that are also in far prefix."""

    nearby_suffix_token_len: int = 0
    """The number of tokens in nearby suffix component (suffix close to cursor)."""

    nearby_suffix_token_overlap: int = 0
    """The number of tokens in nearby suffix that are also in far suffix."""



    def create_default_tokenizer(self) -> AbstractTokenizer:
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        """Create a simple prompt for the Star Coder model.

        Args:
            input: an instance of ModelInput class, containing all raw input.
            tokenizer: the self.tokenizer.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation: number of prefix chars post-truncation
                num_suffix_chars_post_truncation: number of suffix chars post-truncation
        """
        metadata = {}

        assert isinstance(
            self.tokenizer,
            (
                StarCoderTokenizer,
                StarCoder2Tokenizer,
                DeepSeekCoderBaseTokenizer,
                CodestralTokenizer,
                tokenizer_adapters.DeepSeekCoderBaseWrappedProdTokenizer,
...
            max_path_tokens: the maximal number of tokens that should be included for any path
        """
        super().__init__(
            special_tokens_factory=rogue_prompt_formatter.RogueSpecialTokens,
            prod_prompt_formatter_factory=rogue_prompt_formatter.RoguePromptFormatter,
            research_tokenizer_factory=StarCoderTokenizer,
            # Arguments below this line are passed to the prod prompt formatter as
            # part of the token apportionment config.
            max_output_token_count=max_output_token_count,
            max_content_len=max_content_len,
            input_fraction=input_fraction,
            prefix_fraction=prefix_fraction,
            max_path_tokens=max_path_tokens,
        )


@register_prompt_formatter("rogue_statelesscache")
class PromptFormatterRogueSLCache(AbstractPromptFormatter):
    """Prompter for Rogue model with stateless caching."""

    # The config used to build the prompt.

    # Budgets of different components
...
    nearby_prefix_token_overlap: int = 0
    """The number of tokens in nearby prefix that are also in far prefix."""

    nearby_suffix_token_len: int = 0
    """The number of tokens in nearby suffix component (suffix close to cursor)."""

    nearby_suffix_token_overlap: int = 0
    """The number of tokens in nearby suffix that are also in far suffix."""

    prepend_bos_token: Annotated[
        bool, "If true, prepend BOS token to the beginning of the prompt."
    ] = False

    use_far_prefix_token: Annotated[
        bool,
        "If true, use far prefix token for prefix and prefix token for nearby prefix.",
    ] = False

    use_far_suffix_token: Annotated[
        bool,
        "If true, use far suffix token for suffix and suffix token for nearby suffix.",
    ] = False

    def create_default_tokenizer(self) -> AbstractTokenizer:
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
...
                tokenizer_adapters.StarCoder2WrappedProdTokenizer,
                tokenizer_adapters.StarCoder1WrappedProdTokenizer,
            ),
        )

        far_prefix_id = (
            self.tokenizer.far_prefix_id
            if self.use_far_prefix_token
            else self.tokenizer.fim_prefix_id
        )
        nearby_prefix_id = (
            self.tokenizer.fim_prefix_id
            if self.use_far_prefix_token
            else self.tokenizer.nearby_prefix_id
        )
        far_suffix_id = (
            self.tokenizer.far_suffix_id
            if self.use_far_suffix_token
            else self.tokenizer.fim_suffix_id
        )
        nearby_suffix_id = (
            self.tokenizer.fim_suffix_id
            if self.use_far_suffix_token
            else self.tokenizer.nearby_suffix_id
        )

        if self.use_far_prefix_token and "nearby_prefix" not in self.component_order:
            raise ValueError(
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `services/deploy/configs/repo_model_config.proto`:

```
...

  // number of heads
  uint32 num_heads = 5;

  // dimension per head
  uint32 head_dim = 6;

  // rotary embedding percentage
  float rotary_pct = 7;

  // norm epsilon
  float norm_eps = 8;
}

message InferenceModelConfig {
  // name of the tokenizer to use.
  // the tokenizer needs to match the tokenizer used to train/fine-tune the model
  string tokenizer_name = 1;

  reserved 2;

  // name of the prompt formatter to use
  // the prompt formatter needs to match the prompts used to train/fine-tune the model
  string prompt_formatter_name = 3;

  // languages supported for the given model
  repeated string languages = 4;

  // The maximal number of tokens supported by the model.
  //
...
```

Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/20231004_ppg_data_sampling.py`:

```
...
        spark_gpu.stop()

    sample_data_task = _sample_data()

    # ====== Tokenize ======
    @task(task_id="tokenize")
    def _tokenize():
        context = get_current_context()
        dag_params = context["params"]
        s3root = pathlib.Path(dag_params["s3root"])
        if dag_params["skip_tokenize"]:
            return
        spark_stages.stage_dual_encoder_tokenize(
            input_path=s3root / "stage_6_label",
            output_path=s3root / "stage_7_token",
            query_formatter=dag_params["query_formatter"],
            key_formatter=dag_params["key_formatter"],
            encoder_seq_length=dag_params["encoder_seq_length"],
            max_retrieved_docs=dag_params["parameters"]["total_chunks"] - 1,
            max_workers=dag_params["max_workers"],
        )

    tokenize_task = _tokenize()
    tokenize_task << sample_data_task

    # ====== Explode DataFrame ======
    @task(task_id="explode")
    def _explode():
        context = get_current_context()
...
```

Here is an excerpt from the file `research/data/spark/notebooks/util.py`:

```
"""A few common utils for pipeline notebooks."""
import os
from pathlib import Path
from tempfile import TemporaryDirectory

import boto3
import botocore
import pandas as pd
import pyspark.sql.functions as F
import torch
from megatron.data import indexed_dataset
from megatron.tokenizer.tokenizer import get_tokenizer
from pyspark.sql import Row
from pyspark.sql.types import ArrayType, IntegerType, StringType
from retry import retry

# Create a tokenizer for various downstream tasks
tokenizer = get_tokenizer("CodeGenTokenizer")


def tokenize_row(text):
    token_ids = tokenizer.tokenize(text)
    if token_ids and token_ids[-1] != tokenizer.eod_id:
        token_ids.append(tokenizer.eod_id)
    return token_ids


@F.pandas_udf(ArrayType(IntegerType()))
def tokenize_udf(s: pd.Series) -> pd.Series:
    result = s.map(tokenize_row)
...
```

Here is an excerpt from the file `research/data/spark/pipelines/configs/fim_sample.yaml`:

```
global:
  data_processing_root: /mnt/efs/aug-cw-lga1-nvme/data-pipeline/the-stack/
  tokenizer: HFGPT2Tokenizer
  tokenizer_args: ["/mnt/efs/aug-cw-lga1/checkpoints/tokenizers/starcoderbase"]

stages:
  # Subsample the data significantly
  - name: limit
    input: filtered
    output: filtered_1M_py
    size: 1_000_000
    numPartitions: 64
    ext: py

  # Sample FIM problems
  - name: sample_fim_and_tokenize
    input: filtered_1M_py
    output: fim_tokenized_1M_py
    force: true

  - name: add_global_batch_id
    input: fim_tokenized_1M_py
    output: fim_tokenized_batched_1M_py
    batch_size: 128
    force: true

  - name: export_indexed_dataset
    input: fim_tokenized_batched_1M_py
    output: fim_indexed_dataset_1M_py
    force: true
...
```

Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/20231101_ppldistill_tokenize_and_export.py`:

```
...
            description="If True, skip the skip_explode stage and any thereafter.",
        ),
        # Stage: Tokenize
        "query_formatter": Param(
            {
                "name": "ethanol3_query",
                "max_tokens": 1024 - 1,
                "max_lines": -1,
                "add_path": True,
                "retokenize": True,
            },
            type="object",
            description="Config for query formatter.",
        ),
        "key_formatter": Param(
            {
                "name": "simple_document",
                "add_path": True,
                "max_tokens": 1024 - 1,
            },
            type="object",
        ),
        "encoder_seq_length": 1024,
        "max_workers": 64,
    },
    # Only trigger manually.
    schedule=None,
    start_date=datetime.datetime(2021, 1, 1),
    catchup=False,
    tags=["ethanol_plus", "data_analysis"],
...
```

Here is an excerpt from the file `experimental/vzhao/airflow/dags/ppl/20231030_ppg_data_sampling_use_weak_negatives_exp.py`:

```
...
            return
        input_path = os.path.join(dag_params["s3root"], "stage_6_label")
        output_path = os.path.join(dag_params["s3root"], "stage_7_token")
        spark_stages.stage_dual_encoder_tokenize(
            input_path=input_path,
            output_path=output_path,
            query_formatter=dag_params["query_formatter"],
            key_formatter=dag_params["key_formatter"],
            encoder_seq_length=dag_params["encoder_seq_length"],
            max_retrieved_docs=dag_params["parameters"]["total_chunks"] - 1,
            max_workers=dag_params["max_workers"],
        )

    tokenize_task = _tokenize()
    tokenize_task << sample_data_task

    # ====== Explode DataFrame ======
    @task(task_id="explode")
    def _explode():
        context = get_current_context()
        dag_params = context["params"]
        if dag_params["skip_tokenize"]:
            return
        spark_stages.stage_explode(
            input_path=os.path.join(dag_params["s3root"], "stage_7_token"),
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

I have the file `/home/<USER>/augment/augment/research/core/prompt_formatters.py` open and has selected part of the code.

Here is the full file:

```
_post_truncation"] = num_prefix_chars_post_truncation
        metadata["num_suffix_chars_post_truncation"] = num_suffix_chars_post_truncation

        nearby_prefix_tokens: list[int] = []
        if self.nearby_prefix_char_len > 0:
            nearby_prefix = model_input.prefix[-self.nearby_prefix_char_len :]
            nearby_prefix_tokens = [
                self.tokenizer.nearby_prefix_id
            ] + self.tokenizer.tokenize_safe(nearby_prefix)

        # Clip the budget
        tokens_used = len(preamble_tokens) + len(context_tokens) + 1
        if self.nearby_prefix_char_len > 0:
            tokens_used += len(nearby_prefix_tokens)

        retrieval_budget = max(0, self.max_prompt_tokens - tokens_used)
        if self.max_retrieved_chunk_tokens >= 0:
            # Negative max is considered no limit
            retrieval_budget = min(retrieval_budget, self.max_retrieved_chunk_tokens)

        # The first token is used by `retrieval_section_id`.
        retrieval_tokens: list[int] = [self.tokenizer.retrieval_section_id]
        retrieval_budget -= 1
        retrieved_chunks = filter_retrieved_chunks_that_overlap_with_prompt(
            model_input=model_input,
            num_prefix_chars=num_prefix_chars_post_truncation,
            num_suffix_chars=num_suffix_chars_post_truncation,
        )
        tokenized_blocks: list[list[int]] = []
        accum = 0
        if self.max_number_chunks is None:
            max_number_chunks = len(retrieved_chunks)
        else:
            max_number_chunks = self.max_number_chunks
        for chunk in retrieved_chunks[:max_number_chunks]:
            tokenized_block = []
            tokenized_block += [self.tokenizer.retrieval_start_id]
            if self.prepend_path_to_retrieved:
                chunk_path = chunk.parent_doc.path
                if chunk_path is not None:
                    tokenized_block += [self.tokenizer.filename_id]
                    tokenized_block += trailing_n(
                        self.tokenizer.tokenize_safe(chunk_path),
                        self.max_filename_tokens,
                    )
                    tokenized_block += [self.tokenizer.retrieval_body_id]
                    tokenized_block += self.tokenizer.tokenize(chunk.text)

            if accum + len(tokenized_block) > retrieval_budget:
                break

            tokenized_blocks.append(tokenized_block)
            accum += len(tokenized_block)

        # Order chunks such that the most important chunk is at the end of the block,
        # to be closest to the context.
        tokenized_blocks.reverse()

        for block in tokenized_blocks:
            retrieval_tokens.extend(block)

        if self.add_retrieval_after_context:
            prompt_tokens = preamble_tokens + context_tokens + retrieval_tokens
        else:
            prompt_tokens = preamble_tokens + retrieval_tokens + context_tokens

        if self.nearby_prefix_char_len > 0:
            prompt_tokens = prompt_tokens + nearby_prefix_tokens

        # Middle token always at end
        prompt_tokens = prompt_tokens + [self.tokenizer.fim_middle_id]

        assert (
            len(prompt_tokens) <= self.max_prompt_tokens
        ), f"Returned {len(prompt_tokens)}, max {self.max_prompt_tokens}"

        return prompt_tokens, metadata


@register_prompt_formatter("ender")
class PromptFormatterEnder(AbstractPromptFormatter):
    """Prompter for RAG-finetuned StarCoder model with signatures."""

    # The config used to build the prompt.
    max_prefix_tokens: int = -1
    """The maximum number of tokens for prefix, where -1 means infinity."""

    max_suffix_tokens: int = -1
    """The maximum number of tokens for suffix, where -1 means infinity."""

    max_signature_tokens: int = -1
    """The maximum number of tokens for signatures, where -1 means infinity."""

    max_retrieved_chunk_tokens: int = -1
    """The maximum number of tokens for the retrieved chunks"""

    max_filename_tokens: int = 25
    """Truncate filename to this many tokens from left"""

    component_order: Sequence[str] = ("retrieval", "signature", "prefix", "suffix")
    """Order of components: prefix, suffix, signatures, retrieval, nearby_prefix."""

    # Caching parameters
    context_quant_token_len: int = 0
    """The prefix and suffix are quantized to multiple of this number of tokens from beginning/end of file."""

    nearby_prefix_token_len: int = 0
    """The number of tokens in nearby prefix component (prefix close to cursor)."""

    nearby_prefix_token_overlap: int = 0
    """The number of tokens in nearby prefix that are also in far prefix."""

    nearby_suffix_token_len: int = 0
    """The number of tokens in nearby suffix component (suffix close to cursor)."""

    nearby_suffix_token_overlap: int = 0
    """The number of tokens in nearby suffix that are also in far suffix."""
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

        newline_id = self.tokenizer.tokenize("\n")[0]

        # Current path
        path_tokens: list[int] = [self.tokenizer.filename_id]
        raw_path_tokens = self.tokenizer.tokenize_safe(model_input.path)
        path_tokens += trailing_n(raw_path_tokens, self.max_filename_tokens) + [
            newline_id
        ]

        # Construct prefix
        if self.max_prefix_tokens < 0:
            raise ValueError(
                "max_prefix_tokens must be >= 0, infinite prefix tokens not supported"
            )

        raw_prefix_tokens: list[int] = self.tokenizer.tokenize_safe(model_input.prefix)

        prefix_range, nearby_prefix_range = segment_prefix_stateless(
            raw_prefix_token_len=len(raw_prefix_tokens),
            max_prompt_prefix_tokens=self.max_prefix_tokens
            + self.nearby_prefix_token_len,
            nearby_prefix_token_len=self.nearby_prefix_token_len,
            nearby_prefix_token_overlap=self.nearby_prefix_token_overlap,
            quantize_token_len=self.context_quant_token_len,
        )

        nearby_prefix_tokens: list[int] = []
        prefix_tokens: list[int] = []
        if nearby_prefix_range is not None:
            nearby_prefix_tokens += [self.tokenizer.fim_prefix_id]
            nearby_prefix_tokens += raw_prefix_tokens[nearby_prefix_range.start :]
            # Use fim prefix token for nearby prefix and far prefix for rest of prefix.
            # We hit this code path iff nearby_prefix_len > 0, so this token setting is
            # per deployment, not different for individual samples.
            prefix_tokens.append(self.tokenizer.far_prefix_id)
        else:
            prefix_tokens.append(self.tokenizer.fim_prefix_id)

        prefix_tokens += raw_prefix_tokens[prefix_range.start : prefix_range.stop]

        # Construct suffix
        if self.max_suffix_tokens < 0:
            raise ValueError(
                "max_suffix_tokens must be >= 0, infinite suffix tokens not supported"
            )

        raw_suffix_tokens = self.tokenizer.tokenize_safe(model_input.suffix)

        suffix_range, nearby_suffix_range = segment_suffix_stateless(
            raw_suffix_token_len=len(raw_suffix_tokens),
            max_prompt_suffix_tokens=self.max_suffix_tokens
            + self.nearby_suffix_token_len,
            nearby_suffix_token_len=self.nearby_suffix_token_len,
            nearby_suffix_token_overlap=self.nearby_suffix_token_overlap,
            quantize_token_len=self.context_quant_token_len,
        )

        nearby_suffix_tokens: list[int] = []
        suffix_tokens: list[int] = []
        if nearby_suffix_range is not None:
            nearby_suffix_tokens += [self.tokenizer.fim_suffix_id]
            nearby_suffix_tokens += raw_suffix_tokens[: nearby_suffix_range.stop]
            # Use fim suffix token for nearby suffix and far suffix for rest of suffix.
            # We hit this code path iff nearby_prefix_len > 0, so this token setting is
            # per deployment, not different for individual samples.
            suffix_tokens.append(self.tokenizer.far_suffix_id)
        else:
            suffix_tokens.append(self.tokenizer.fim_suffix_id)

        suffix_tokens += raw_suffix_tokens[suffix_range.start : suffix_range.stop]

        num_prefix_chars_post_truncation = len(
            self.tokenizer.detokenize(raw_prefix_tokens[prefix_range.start :])
        )
        num_suffix_chars_post_truncation = len(
            self.tokenizer.detokenize(raw_suffix_tokens[: suffix_range.stop])
        )
        metadata["num_prefix_chars_post_truncation"] = num_prefix_chars_post_truncation
        metadata["num_suffix_chars_post_truncation"] = num_suffix_chars_post_truncation

        # Construct signatures
        if self.max_signature_tokens < 0:
            raise ValueError(
                "max_signature_tokens must be >= 0, infinite signature tokens not supported"
            )

        if (
            self.max_signature_tokens > 0
            and "signature_chunks" not in model_input.extra
        ):
            raise ValueError("No signatures found in model input.")

        signature_start_tokens = [self.tokenizer.sig_begin_id, newline_id]
        signature_end_tokens = [newline_id, self.tokenizer.sig_end_id]

        signature_budget = (
            self.max_signature_tokens
            - 4  # signature start and end each take up 2 tokens
        )
        signature_chunks: list[Chunk] = model_input.extra["signature_chunks"]

        # Sometimes signatures have unnecessary trailing newlines - we add newlines
        # ourselves for readability, so we first strip those.
        signature_strings = [chunk.text.rstrip() for chunk in signature_chunks]
        # Signatures are ordered from closest to cursor to furthest away. Closer
        # signatures are more important, so we put them towards the end of the prompt.
        signature_tokens: list[int] = []
        for signature in signature_strings:
            next_signature_tokens = self.tokenizer.tokenize_safe(
                signature + ("\n\n" if signature_tokens else "")
            )
            if len(signature_tokens) + len(next_signature_tokens) <= signature
```

Here is the selected code:

```



    def create_default_tokenizer(self) -> AbstractTokenizer:
        return StarCoderTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        """Create a simple prompt for the Star Coder model.

        Args:
            input: an instance of ModelInput class, containing all raw input.
            tokenizer: the self.tokenizer.

        Returns:
            A prompt of length at most self.max_prompt_tokens, in tokens.
            A metadata dict containing:
                num_prefix_chars_post_truncation: number of prefix chars post-truncation
                num_suffix_chars_post_truncation: number of suffix chars post-truncation
        """
        metadata = {}

        assert isinstance(
            self.tokenizer,
            (
                StarCoderTokenizer,
                StarCoder2Tokenizer,
                DeepSeekCoderBaseTokenizer,
                CodestralTokenizer,
                tokenizer_adapters.DeepSeekCoderBaseWrappedProdTokenizer,
                tokenizer_adapters.Llama3BaseWrappedProdTokenizer,
                tokenizer_adapters.StarCoder2WrappedProdTokenizer,
                tokenizer_adapters.StarCoder1WrappedProdTokenizer,
            ),
        )
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.<|eot_id|><|start_header_id|>user<|end_header_id|>

factor allowed tokenizers to class attribute<|eot_id|><|start_header_id|>assistant<|end_header_id|>
