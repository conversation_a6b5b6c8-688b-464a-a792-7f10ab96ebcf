<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are <PERSON><PERSON>, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I want to use only a limited number of partitions<|eot_id|><|start_header_id|>assistant<|end_header_id|>

You can use the `limit_by_rdd_partition` function from `research/data/spark/pipelines/stages/common.py` to limit the number of partitions. This function subsamples the data by limiting each RDD partition, which is an efficient way to subsample the data when the RDD partitions are already balanced.

For example, you can use it like this:
```
input_df = limit_by_rdd_partition(input_df, per_partition_limit=100)
```
This will limit each RDD partition to 100 samples, resulting in a total of `per_partition_limit * input_df.rdd.getNumPartitions()` samples.

Alternatively, you can use the `limit_by_partition` function, which subsamples the data by limiting each partition based on a specific column. For example:
```
input_df = limit_by_partition(input_df, partition_column="repository_name", per_partition_limit=100)
```
This will limit each partition based on the `repository_name` column to 100 samples.

Let me know if you have any further questions or if there's anything else I can help you with!<|eot_id|><|start_header_id|>user<|end_header_id|>

That doesn't quite for what I'm doing here. I'm using map parquet apply, so I can only manually set input paths as a list of parquet file paths<|eot_id|><|start_header_id|>assistant<|end_header_id|>

In that case, you can manually specify the input paths as a list of parquet file paths to control the number of partitions. For example:
```
input_paths = [f"{stage4_uri}/{i}.parquet" for i in range(desired_partitions)]
map_parquet.apply(
    spark,
    partial(
        pack_prompts,
        query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
        key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
    ),
    input_path=input_paths,
    output_path=stage5_uri,
    output_column="prompt_tokens",
    timeout=7200,
    pass_as_kwargs=True,
)
```
By specifying the input paths as a list of parquet file paths, you can control the number of partitions by setting `desired_partitions` to the desired number of partitions.<|eot_id|><|start_header_id|>user<|end_header_id|>

from input path, which is a directory, find me a list of parquet filepaths, shuffle them, and pick the first max_partitions<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Here's how you can do it:
```
import os
import random

input_path = stage4_uri
parquet_files = [os.path.join(input_path, f) for f in os.listdir(input_path) if f.endswith('.parquet')]
random.shuffle(parquet_files)
input_paths = parquet_files[:max_partitions]

map_parquet.apply(
    spark,
    partial(
        pack_prompts,
        query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
        key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
    ),
    input_path=input_paths,
    output_path=stage5_uri,
    output_column="prompt_tokens",
    timeout=7200,
    pass_as_kwargs=True,
)
```
This code lists all parquet files in the input directory, shuffles them, and then selects the first `max_partitions` files. These file paths are then used as the input paths for `map_parquet.apply`.<|eot_id|><|start_header_id|>user<|end_header_id|>

Here is the selected code:

\`\`\`
    input_path = stage4_uri

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
            key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
        ),
        input_path=stage4_uri,
        output_path=stage5_uri,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
    )
    spark.stop()
\`\`\`

Rewrite the selected code to apply all these changes.
Fit all new and modified code in your response.
Do not repeat any code that already exists outside the selected code.
Include all parts of the selected code that weren't changed.
<|eot_id|><|start_header_id|>assistant<|end_header_id|>
