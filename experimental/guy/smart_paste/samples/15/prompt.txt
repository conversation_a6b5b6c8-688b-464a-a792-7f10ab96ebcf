<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Augment, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- When referencing a file in your response, always include the FULL file path.
- If the provided files are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

I currently have the file `experimental/michiel/research/data/retrieval/ethanol6_distill.py` open, and I am actively working on it. Here is an excerpt from it:

```
# Imports

import json
import random
import time
from functools import partial
from pathlib import Path
from statistics import mean
from types import SimpleNamespace
from typing import Any, Dict, List, Sequence

import numpy as np
import pyspark.sql.functions as F
from megatron.data.indexed_dataset import MMapIndexedDataset, make_builder
from megatron.tokenizer import get_tokenizer

from base.prompt_format_retrieve import (
    DocumentRetrieverPromptInput,
    get_retrieval_prompt_formatter_by_name,
)
from base.tokenizers import create_tokenizer_by_name
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset
from research.data.spark.pipelines.utils import map_parquet
from research.retrieval.types import Chunk, Document

# GLOBAL CONFIGURATION --------------------------------------------------------

TEMP_BUCKET_URI = "s3a://augment-temporary/michiel/"
BUCKET_URI = "s3a://michiel-dev-bucket/retrieval/"
PATHS = dict(
    STAGE3_URI="s3a://igor-dev-bucket/ethanol6/ethanol6-16/05_with_ppl_scores/",
    STAGE4_URI=BUCKET_URI + "04_shuffled",
    STAGE5_URI=BUCKET_URI + "{}/05_tokenized{}",
    STAGE6_URI=BUCKET_URI + "{}/06_exploded{}",
    OUTPUT_PATH="/mnt/efs/augment/user/michiel/data/retrieval/{}{}",
)


# TOKENIZATION CONFIGURATION --------------------------------------------------

dataset_config = SimpleNamespace(
    tokenizer_name="starcoder",
    research_tokenizer_name="StarCoderTokenizer",
    query_prompt_formatter_name="ethanol6-query",
    key_prompt_formatter_name="ethanol6-embedding-with-path-key",
    doc_seq_length=1000,
    allow_doc_clipping=False,
    seq_length=8192,
    retrieved_docs=127,  # in Ethanol dataset, we only have 127 docs, not 128
    doc_batch_group_size=1,
    shuffle_docs=False,
    num_validation_samples=512,
    known_chunks_budget=8000,
    dataset_format=2,
    include_known_chunk_labels=True,
...
    spark = k8s_session(
        max_workers=64, name="igor-code-distill-shuffle", conf=spark_config
    )
    spark.read.parquet(stage3_uri).orderBy(F.rand()).repartition(
        desired_partitions
    ).write.parquet(stage4_uri)
    spark.stop()


def stage5(stage4_uri, stage5_uri, small_cluster, max_partitions=None):
    spark_config = {
        "spark.executor.pyspark.memory": "1050g",
    }
    spark = k8s_session(
        name="igor-code-distill-tokenize",
        conf=spark_config,
        max_workers=32,
    )

    def pack_prompt(prompt: List[int], pad_token: int, should_pad=True) -> bytearray:
        if should_pad:
            prompt_arr = np.pad(
                prompt,
                (0, 1 + dataset_config.seq_length - len(prompt)),
                constant_values=pad_token,
            )
        else:
            prompt_arr = np.array(prompt)

        return bytearray(prompt_arr.astype(np.uint16).newbyteorder("<").tobytes())
...
        pad_token = key_prompt_formatter.tokenizer.special_tokens.padding

        query_prompt = query_prompt_formatter.format_prompt(
            ModelInput(
                prefix=prefix,
                suffix=suffix,
                path=file_path,
            )
        ).tokens()

        query_prompt.append(end_of_query_token)
        if len(query_prompt) > dataset_config.seq_length:
            raise ValueError(
                f"Query token length exceeds seq_len: {len(query_prompt)} > {dataset_config.seq_length}"
            )

        scores = ppl_scores["scores"]

        # Fix up the initial "empty chunk". We no longer use this, but the existing datasets
        # have it. (We used to have the "empty chunk" perplexities as a baseline for perplexity
        # gain scores, which we don't use - we just use the perplexity scores directly without
        # subtracting the baseline.)
        assert len(retrieved_chunks) == len(retrieval_rank)
        retrieved_chunks = [
...
                print(key_prompt_formatter.tokenizer.detokenize(prompt))
                print("===================================================")
                raise ValueError(
                    f"{id} token length exceeds seq_len: {len(prompt)} > {dataset_config.seq_length}"
                )

            doc_prompts.append((ppl_score, prompt))

        # sort the documents in descending order of perplexity and keep the better ones
        if False:
            doc_prompts = [
                prompt
                for _ppl_score, prompt in sorted(
                    doc_prompts, key=lambda x: x[0], reverse=True
                )
            ][: dataset_config.retrieved_docs]
        else:
            assert len(doc_prompts) <= dataset_config.retrieved_docs
            doc_prompts = [prompt for _ppl_score, prompt in doc_prompts]

        # optionally shuffle the docs -- if the retriever training will see the order
        # of the docs (e.g., if multiple docs are packed into each sequence) -- shuffling
        # is important.
        if dataset_config.shuffle_docs:
            random.shuffle(doc_prompts)

        # group the documents into prompts
        assert dataset_config.dataset_format == 2
        all_tokens = query_prompt
        all_tokens.extend(sum(doc_prompts, []))

        # Note: this can exceed the maximum query length -- we won't have a loss
        # on these tokens
        if dataset_config.include_known_chunk_labels:
            all_tokens.extend(
                query_prompt_formatter.tokenizer.tokenize_safe(
                    json.dumps(
                        {
                            "known_chunk_labels": known_chunk_labels,
                        }
                    )
                )
            )
            return pack_prompt(all_tokens, pad_token, False)

    input_path = stage4_uri

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
            key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
        ),
        input_path=stage4_uri,
        output_path=stage5_uri,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
    )
    spark.stop()


def stage6(stage5_uri, stage6_uri, small_cluster):
    def explode_prompts(batch, config=None):
        assert dataset_config.dataset_format == 2
        assert dataset_config.doc_batch_group_size == 1

        filtered_batch = batch[batch["prompt_tokens"].apply(len) > 0]

        # HACK one-time use only. In future, stage5 should take care of this so that
        # the first filtering step is sufficient.
        from research.data.train.common.pack_utils import unpack_tokens

        filtered_batch = batch[
            batch["prompt_tokens"].apply(
                lambda x: sum(1 for t in unpack_tokens(x) if t == 49167)
                == 2 * dataset_config.retrieved_docs
            )
        ]

        # results = filtered_batch.explode("prompt_tokens")
        results = filtered_batch
        return results if len(results) > 0 else None

    print("Exploding prompts...", stage5_uri, stage6_uri)
    spark = k8s_session(
        name="igor-code-distill-explode",
        max_workers=32 if not small_cluster else 2,
    )
    map_parquet.apply_pandas(
        spark,
        partial(
            explode_prompts,
            config=dataset_config,
        ),
        input_path=stage5_uri,
        output_path=stage6_uri,
        output_column="prompt_tokens",
        timeout=7200,
    )


def stage7(stage6_uri, output_path, small_cluster):
    spark = k8s_session(
        name="igor-code-distill-export_indexed_dataset",
        max_workers=32 if not small_cluster else 2,
    )
    export_indexed_dataset(
        config=ObjectDict(
            {
                "name": "export-dataset",
                "input": stage6_uri,
                "output": Path(output_path),
                "samples_column": "prompt_tokens",
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(dataset_config.research_tokenizer_name),
    )

    spark.stop()


def stage8(output_path, small_cluster):
    dataset = MMapIndexedDataset(str(Path(output_path) / "dataset"))
    train_builder = make_builder(str(Path(output_path) / "train.bin"), "mmap")
    valid_builder = make_builder(str(Path(output_path) / "valid.bin"), "mmap")

    train_samples = max(0, len(dataset) - dataset_config.num_validation_samples)

    for idx in range(len(dataset)):
        if idx < train_samples:
            train_builder.add_array_item(dataset[idx])
            train_builder.end_document()
        else:
            valid_builder.add_array_item(dataset[idx])
...
```

Below are some relevant files from my project.

Here is an excerpt from the file `research/data/spark/pipelines/stages/common.py`:

```
...
    Args:
        input_path: A path that contains parquet files to be processed.
        output_path: The output directory for saving the split and the validation files.
        num_validation_rows: The number of rows to be in the validation set. This is
        a soft limit, and the actual number of validation rows will typically be
        higher.
    """
    result: list[dict] = []
    output_parquet_file = output_path / "split.parquet"

    input_parquet_files = list(input_path.glob("*.parquet"))
    random.shuffle(input_parquet_files)

    num_collected_validation_rows = 0
    validation_parquet_files: list[Path] = []

    for parquet_path in input_parquet_files:
        if num_collected_validation_rows < num_validation_rows:
            split = "validation"
            df = pandas.read_parquet(parquet_path)
            num_collected_validation_rows += len(df)
            validation_parquet_files.append(parquet_path)
        else:
            split = "train"

        result.append(
            {
...
        partition_column: Which column to partition the data by.
        per_partition_limit: How many samples to pick from each partition.

    Returns:
        A new dataframe with the same schema as `df`. It will have a number of rows
        equal to `per_partition_limit` * the number of values in `partition_column`.
    """
    # Assuming df is your DataFrame and "language" is your column
    input_df = input_df.withColumn("_id", F.monotonically_increasing_id())

    window = Window.partitionBy(input_df[partition_column]).orderBy(input_df["_id"])
    input_df = input_df.withColumn("_rank", F.rank().over(window))

    input_df = input_df.filter(input_df["_rank"] <= per_partition_limit)

    # Optionally, drop the auxiliary columns
    input_df = input_df.drop("_id", "_rank")
    return input_df


def limit_by_rdd_partition(input_df: DataFrame, per_partition_limit: int) -> DataFrame:
    """Subsample the data by limiting each _RDD_ partition.

    This routine is an efficient hack to subsample the data fairly across partitions,
    and will only work when the RDD partitions are themselves balanced.

    For example, when reading data from the stack, the data is already partitioned by
    language, and this routine will give you a subsample of the data balanced by
    language.

    It is far more efficient than `limit_by_partition` above as we do not need to
    shuffle the data.

    Args:
        df: An input data frame.
        per_partition_limit: How many samples to pick from each RDD partition.

    Returns:
        A new dataframe with the same schema as `input_df`. It's size will be equal to
        `per_partition_limit` * the `input_df.rdd.getNumPartitions()`.
    """
    return input_df.rdd.mapPartitions(
        lambda it: itertools.islice(it, per_partition_limit)
    ).toDF(input_df.schema)


def get_limited_map_parquet_input(
    input_path: Path,
    limit_num_input_parquet_files: Optional[int],
) -> Union[str, Sequence[str]]:
    """Returns a list of parquet files to be processed.

    Args:
        input_path: A directory path that contains parquet files to be processed.
        limit_num_input_parquet_files: If specified, limits the number of parquet
            files to be processed.
    """
    if limit_num_input_parquet_files is not None:
        all_parquet_files = list(input_path.glob("*.parquet"))
        random.shuffle(all_parquet_files)
        return [
            str(parquet_file)
            for parquet_file in all_parquet_files[:limit_num_input_parquet_files]
        ]
    else:
        return str(input_path)
...
```

Here is an excerpt from the file `research/data/pyarrow/data_step/shuffle.py`:

```
"""Data pipeline step to shuffle records within each pyarrow fragment."""

from pathlib import Path

import pyarrow
from pyarrow import parquet
from tqdm import tqdm


def _shuffle_file(args):
    (file_path_in, file_path_out) = args

    # read
    df = parquet.read_table(file_path_in).to_pandas()

    # shuffle
    df = df.sample(frac=1).reset_index(drop=True)
    shuffled_table = pyarrow.Table.from_pandas(df)

    # write
    parquet.write_table(shuffled_table, file_path_out, row_group_size=1024)


def _shuffled_file_path(shuffled_path, file_idx):
    return shuffled_path / f"data.{file_idx:05d}.parquet"


def run(raw_path, shuffled_path):
    shuffled_path = Path(shuffled_path)

    # Create the output directory. Fail if the directories already exist, indicating
    # that another run may be in progress.
    shuffled_path.mkdir()

    # Get the list of files.
    raw_file_paths = parquet.ParquetDataset(raw_path, use_legacy_dataset=False).files

    # Shuffle the records in each file.
    for idx, path in tqdm(enumerate(raw_file_paths), total=len(raw_file_paths)):
        shuffled_file_path = _shuffled_file_path(shuffled_path, idx)
        _shuffle_file((path, str(shuffled_file_path)))
...
```

Here is an excerpt from the file `research/data/pyarrow/data_step/merge_shuffled.py`:

```
...
        yield _arrange_batch(batch, schema)

    for shuffled_file in shuffled_files:
        assert shuffled_file.num_rows == 0


def run(shuffled_path, merged_path, random_seed=42, batch_size=1024):
    dataset = parquet.ParquetDataset(shuffled_path, use_legacy_dataset=False)
    shuffled_files = [_ShuffleRecordReader(file, batch_size) for file in dataset.files]
    total_rows = sum(shuffled_file.num_rows for shuffled_file in shuffled_files)
    print(using("after start"))

    pbar = tqdm(total=total_rows)

    res_iter = _merge_shuffle(
        shuffled_files,
        dataset.schema,
        total_rows,
        batch_size,
        random_seed,
        progress_cb=pbar.update,
    )

    pyarrow.dataset.write_dataset(
        data=res_iter,
        base_dir=merged_path,
        schema=dataset.schema,
        format="parquet",
        max_rows_per_file=1024 * 128,
        max_rows_per_group=1024 * 128,
    )
    print(using("end"))
...
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Understood. I'll refer to the excerpts for context, and ignore them for general questions.<|eot_id|><|start_header_id|>user<|end_header_id|>

I have the file `/home/<USER>/augment/augment/experimental/michiel/research/data/retrieval/ethanol6_distill.py` open and has selected part of the code.

Here is the full file:

```
.query_prompt_formatter_name, tokenizer
        )
        key_prompt_formatter = get_retrieval_prompt_formatter_by_name(
            dataset_config.key_prompt_formatter_name, tokenizer
        )

        end_of_query_token = (
            query_prompt_formatter.tokenizer.special_tokens.end_of_query
        )
        end_of_key_token = key_prompt_formatter.tokenizer.special_tokens.end_of_key
        eod_token = key_prompt_formatter.tokenizer.special_tokens.eos
        pad_token = key_prompt_formatter.tokenizer.special_tokens.padding

        query_prompt = query_prompt_formatter.format_prompt(
            ModelInput(
                prefix=prefix,
                suffix=suffix,
                path=file_path,
            )
        ).tokens()

        query_prompt.append(end_of_query_token)
        if len(query_prompt) > dataset_config.seq_length:
            raise ValueError(
                f"Query token length exceeds seq_len: {len(query_prompt)} > {dataset_config.seq_length}"
            )

        scores = ppl_scores["scores"]

        # Fix up the initial "empty chunk". We no longer use this, but the existing datasets
        # have it. (We used to have the "empty chunk" perplexities as a baseline for perplexity
        # gain scores, which we don't use - we just use the perplexity scores directly without
        # subtracting the baseline.)
        assert len(retrieved_chunks) == len(retrieval_rank)
        retrieved_chunks = [
            c for i, c in enumerate(retrieved_chunks) if retrieval_rank[i] >= 0
        ]
        scores = [s for i, s in enumerate(scores) if retrieval_rank[i] >= 0]
        assert len(retrieved_chunks) + 1 == len(retrieval_rank)
        assert len(retrieved_chunks) == len(scores)

        if len(retrieved_chunks) < dataset_config.retrieved_docs:
            return bytearray()

        assert len(retrieved_chunks) == dataset_config.retrieved_docs
        assert len(scores) == dataset_config.retrieved_docs

        chunk_tuples = [(chunk, scores[i]) for i, chunk in enumerate(retrieved_chunks)]

        doc_prompts = []
        for chunk, ppl_score in chunk_tuples:
            # Format the prompt
            prompt = key_prompt_formatter.format_prompt(
                DocumentRetrieverPromptInput(
                    text=chunk.text,
                    path=chunk.parent_doc.path,
                ),
            ).tokens()
            if len(prompt) > dataset_config.doc_seq_length:
                if dataset_config.allow_doc_clipping:
                    prompt = prompt[: dataset_config.doc_seq_length]
                else:
                    raise ValueError(
                        f"Prompt too long: {len(prompt)} > {dataset_config.doc_seq_length}"
                    )

            # Encode the perplexity score into tokens.
            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize_safe(
                f"{ppl_score}"
            )

            # Format the footer of the prompt
            if dataset_config.dataset_format == 1:
                suffix = [end_of_key_token] + ppl_info_tokens + [eod_token]
            else:
                assert prompt[-1] == end_of_key_token
                suffix = ppl_info_tokens + [end_of_key_token]

            prompt.extend(suffix)

            # Check that the prompt is not too long
            if len(prompt) > dataset_config.seq_length:
                print("===================================================")
                print(key_prompt_formatter.tokenizer.detokenize(prompt))
                print("===================================================")
                raise ValueError(
                    f"{id} token length exceeds seq_len: {len(prompt)} > {dataset_config.seq_length}"
                )

            doc_prompts.append((ppl_score, prompt))

        # sort the documents in descending order of perplexity and keep the better ones
        if False:
            doc_prompts = [
                prompt
                for _ppl_score, prompt in sorted(
                    doc_prompts, key=lambda x: x[0], reverse=True
                )
            ][: dataset_config.retrieved_docs]
        else:
            assert len(doc_prompts) <= dataset_config.retrieved_docs
            doc_prompts = [prompt for _ppl_score, prompt in doc_prompts]

        # optionally shuffle the docs -- if the retriever training will see the order
        # of the docs (e.g., if multiple docs are packed into each sequence) -- shuffling
        # is important.
        if dataset_config.shuffle_docs:
            random.shuffle(doc_prompts)

        # group the documents into prompts
        assert dataset_config.dataset_format == 2
        all_tokens = query_prompt
        all_tokens.extend(sum(doc_prompts, []))

        # Note: this can exceed the maximum query length -- we won't have a loss
        # on these tokens
        if dataset_config.include_known_chunk_labels:
            all_tokens.extend(
                query_prompt_formatter.tokenizer.tokenize_safe(
                    json.dumps(
                        {
                            "known_chunk_labels": known_chunk_labels,
                        }
                    )
                )
            )
            return pack_prompt(all_tokens, pad_token, False)

[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]



def stage6(stage5_uri, stage6_uri, small_cluster):
    def explode_prompts(batch, config=None):
        assert dataset_config.dataset_format == 2
        assert dataset_config.doc_batch_group_size == 1

        filtered_batch = batch[batch["prompt_tokens"].apply(len) > 0]

        # HACK one-time use only. In future, stage5 should take care of this so that
        # the first filtering step is sufficient.
        from research.data.train.common.pack_utils import unpack_tokens

        filtered_batch = batch[
            batch["prompt_tokens"].apply(
                lambda x: sum(1 for t in unpack_tokens(x) if t == 49167)
                == 2 * dataset_config.retrieved_docs
            )
        ]

        # results = filtered_batch.explode("prompt_tokens")
        results = filtered_batch
        return results if len(results) > 0 else None

    print("Exploding prompts...", stage5_uri, stage6_uri)
    spark = k8s_session(
        name="igor-code-distill-explode",
        max_workers=32 if not small_cluster else 2,
    )
    map_parquet.apply_pandas(
        spark,
        partial(
            explode_prompts,
            config=dataset_config,
        ),
        input_path=stage5_uri,
        output_path=stage6_uri,
        output_column="prompt_tokens",
        timeout=7200,
    )


def stage7(stage6_uri, output_path, small_cluster):
    spark = k8s_session(
        name="igor-code-distill-export_indexed_dataset",
        max_workers=32 if not small_cluster else 2,
    )
    export_indexed_dataset(
        config=ObjectDict(
            {
                "name": "export-dataset",
                "input": stage6_uri,
                "output": Path(output_path),
                "samples_column": "prompt_tokens",
            }
        ),
        spark=spark,
        tokenizer=get_tokenizer(dataset_config.research_tokenizer_name),
    )

    spark.stop()


def stage8(output_path, small_cluster):
    dataset = MMapIndexedDataset(str(Path(output_path) / "dataset"))
    train_builder = make_builder(str(Path(output_path) / "train.bin"), "mmap")
    valid_builder = make_builder(str(Path(output_path) / "valid.bin"), "mmap")

    train_samples = max(0, len(dataset) - dataset_config.num_validation_samples)

    for idx in range(len(dataset)):
        if idx < train_samples:
            train_builder.add_array_item(dataset[idx])
            train_builder.end_document()
        else:
            valid_builder.add_array_item(dataset[idx])
            valid_builder.end_document()

    train_builder.finalize(str(Path(output_path) / "train.idx"))
    valid_builder.finalize(str(Path(output_path) / "valid.idx"))


if __name__ == "__main__":
    import argparse

    # Parse the arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("stages", type=int, help="stage to run", nargs="+")
    parser.add_argument(
        "--suffix", type=str, help="suffix to add to input/output paths", default=""
    )
    parser.add_argument("--version", type=str, help="model version", default="")
    parser.add_argument(
        "--small-cluster", action="store_true", help="use a small cluster"
    )
    args = parser.parse_args()

    # Stage functions
    stages = [
        (4, stage4, ["STAGE3_URI", "STAGE4_URI"]),
        (5, stage5, ["STAGE4_URI", "STAGE5_URI"]),
        (6, stage6, ["STAGE5_URI", "STAGE6_URI"]),
        (7, stage7, ["STAGE6_URI", "OUTPUT_PATH"]),
        (8, stage8, ["OUTPUT_PATH"]),
    ]

    # Run the stages
    for stage_id, stage_f, stage_path_names in stages:
        if stage_id in args.stages:
            suffix = "." + args.suffix if args.suffix else ""
            stage_paths = [
                PATHS[name].format(args.version, suffix) for name in stage_path_names
            ]

            print(f"Running stage {stage_id} with paths: {stage_paths}")
            stage_f(*stage_paths, small_cluster=args.small_cluster)

```

Here is the selected code:

```
    input_path = stage4_uri

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
            key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
        ),
        input_path=stage4_uri,
        output_path=stage5_uri,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
    )
    spark.stop()
```

<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Noted. I'll output exactly the modified selected code for any edit requests. I won't add context from outside the selected code. I won't delete any context from inside the selected code, unless this is relevant to addressing the instruction.<|eot_id|><|start_header_id|>user<|end_header_id|>

I want to use only a limited number of partitions<|eot_id|><|start_header_id|>assistant<|end_header_id|>

You can use the `limit_by_rdd_partition` function from `research/data/spark/pipelines/stages/common.py` to limit the number of partitions. This function subsamples the data by limiting each RDD partition, which is an efficient way to subsample the data when the RDD partitions are already balanced.

For example, you can use it like this:
```
input_df = limit_by_rdd_partition(input_df, per_partition_limit=100)
```
This will limit each RDD partition to 100 samples, resulting in a total of `per_partition_limit * input_df.rdd.getNumPartitions()` samples.

Alternatively, you can use the `limit_by_partition` function, which subsamples the data by limiting each partition based on a specific column. For example:
```
input_df = limit_by_partition(input_df, partition_column="repository_name", per_partition_limit=100)
```
This will limit each partition based on the `repository_name` column to 100 samples.

Let me know if you have any further questions or if there's anything else I can help you with!<|eot_id|><|start_header_id|>user<|end_header_id|>

That doesn't quite for what I'm doing here. I'm using map parquet apply, so I can only manually set input paths as a list of parquet file paths<|eot_id|><|start_header_id|>assistant<|end_header_id|>

In that case, you can manually specify the input paths as a list of parquet file paths to control the number of partitions. For example:
```
input_paths = [f"{stage4_uri}/{i}.parquet" for i in range(desired_partitions)]
map_parquet.apply(
    spark,
    partial(
        pack_prompts,
        query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
        key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
    ),
    input_path=input_paths,
    output_path=stage5_uri,
    output_column="prompt_tokens",
    timeout=7200,
    pass_as_kwargs=True,
)
```
By specifying the input paths as a list of parquet file paths, you can control the number of partitions by setting `desired_partitions` to the desired number of partitions.<|eot_id|><|start_header_id|>user<|end_header_id|>

from input path, which is a directory, find me a list of parquet filepaths, shuffle them, and pick the first max_partitions<|eot_id|><|start_header_id|>assistant<|end_header_id|>
