Here's how you can do it:
```
import os
import random

input_path = stage4_uri
parquet_files = [os.path.join(input_path, f) for f in os.listdir(input_path) if f.endswith('.parquet')]
random.shuffle(parquet_files)
input_paths = parquet_files[:max_partitions]

map_parquet.apply(
    spark,
    partial(
        pack_prompts,
        query_prompt_formatter_name=dataset_config.query_prompt_formatter_name,
        key_prompt_formatter_name=dataset_config.key_prompt_formatter_name,
    ),
    input_path=input_paths,
    output_path=stage5_uri,
    output_column="prompt_tokens",
    timeout=7200,
    pass_as_kwargs=True,
)
```
This code lists all parquet files in the input directory, shuffles them, and then selects the first `max_partitions` files. These file paths are then used as the input paths for `map_parquet.apply`.
