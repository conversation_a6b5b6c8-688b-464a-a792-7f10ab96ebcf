Here is the rewritten code with the added `if __name__ == "__main__":` boilerplate:

```
    def get_next_state(self, current_state: ChatState) -> ChatState:
        """Get the next state based on the current state and the transition matrix."""
        states, probs = zip(*self.transition_matrix[current_state].items())
        return np.random.choice(states, p=probs)

    def generate_state_sequence(self, start_state: ChatState, length: int) -> List[ChatState]:
        """Generate a sequence of chat states of a given length starting from a given state."""
        sequence = [start_state]
        for _ in range(length - 1):
            sequence.append(self.get_next_state(sequence[-1]))
        return sequence


if __name__ == "__main__":
    css = ChatStatesSequence(TRANSITION_MATRIX)
    sequence = css.generate_state_sequence(ChatState.NEW_TOPIC, 10)
    print(sequence)
```

Let me know if you have any other requests!
