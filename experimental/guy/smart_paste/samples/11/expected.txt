Here is the modified code:

```
for sample_a, sample_b, datum in loop_list[:10]:
    # if not sample_b["ground_truth"].startswith(sample_b["generation"][:1]) and sample_a["generation"] == sample_a["ground_truth"]:

    if (
        True
        # not (
        #     datum["ground_truth"][:1].isalnum()
        #     and datum["completion"]["request"]["prefix"][-1:].isalnum()
        # )
        # and sample_a["generation"] == sample_a["ground_truth"]
        # and sample_b["generation"]!= sample_b["ground_truth"]
    ):
        color_print("REQUEST", "white")
        print(
            sample_b["artifacts"][0]["request_ids"][0]
            if "artifacts" in sample_b and sample_b["artifacts"][0]["request_ids"]
            else None
        )
        print(sample_a["path"])

        color_print("PREFIX", "blue")
        print(sample_b["prefix"][-100:])
        color_print("SUFFIX", "yellow")
        print(sample_b["suffix"][:100])
        color_print("GROUND TRUTH", "green")
        print(datum["ground_truth"])
        color_print("GENERATION", "red")
        print("model:", model_a)
        print(sample_a["generation"])
        print("model:", model_b)
        print(sample_b["generation"])
        print("\n\n")
        # break
