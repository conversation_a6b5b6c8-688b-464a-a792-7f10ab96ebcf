<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Aug<PERSON>, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `tools/genie/frontend/public/prism.min.js`:

```
          }
          if (
            (j.hooks.run("before-sanity-check", i),
            (s = i.element.parentElement) &&
              "pre" === s.nodeName.toLowerCase() &&
              !s.hasAttribute("tabindex") &&
              s.setAttribute("tabindex", "0"),
            !i.code)
          )
            return j.hooks.run("complete", i), void (n && n.call(i.element));
          j.hooks.run("before-highlight", i),
            i.grammar
              ? t && o.Worker
                ? (((t = new Worker(j.filename)).onmessage = function (e) {
                    l(e.data);
                  }),
                  t.postMessage(
                    JSON.stringify({
                      language: i.language,
                      code: i.code,
                      immediateClose: !0,
                    }),
                  ))
                : l(j.highlight(i.code, i.grammar, i.language))
              : l(j.util.encode(i.code));
        },
        highlight: function (e, t, n) {

```

Here is the snippet from `experimental/dxy/edits/notebooks/random/launch_ft.py`:

```
            + f"lines_in_prefix_suffix = {lines_in_prefix_suffix}"
        )
        if lines_in_prefix_suffix <= 0:
            prefix, suffix = None, None
        else:
            prefix, suffix = original_prefix, original_suffix
        start_time = time.time()
        response = GLOBAL_EDIT_MODEL(
            selected_code,
            instruction,
            prefix=prefix,
            suffix=suffix,
            lines_in_prefix_suffix=lines_in_prefix_suffix,
            top_p=top_p,
            temperature=temperature,
        )
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        time_cost = time.time() - start_time
        debug_json_data = {}
        try:
            debug_json_data = {
                "request_id": request_id,
                "selected_code": selected_code,

```

Here is the snippet from `research/gpt-neox/jobs/internal/job_create.py`:

```

    # Create a symlink to the checkpoint
    if checkpoint is not None:
        checkpoint_path = _expand_checkpoint_path(config, checkpoint)
        (temp_job_path / "initial").symlink_to(checkpoint_path)

    # Write the 'latest' file to point to the initial checkpoint
    latest_path = temp_job_path / "latest"
    with latest_path.open("w", encoding="utf-8") as f:
        f.write("initial")

    # Write the command line into a file
    cmd_line_path = temp_job_path / "cmd_line.txt"
    cmd_line = shlex.join(sys.argv)
    with cmd_line_path.open("w", encoding="utf-8") as f:
        print(cmd_line, file=f)

    # Create a directory for the config files
    config_path = temp_job_path / "config"
    config_path.mkdir()

    # Get the list of config names to later check for duplicates
    config_names = [f.name for f in config_files]

    # Copy the configuration files
    for i, config_file in enumerate(config_files):
        config_file_path = config_path / f"{i:04d}-{config_file.name}"

```

Here is the snippet from `experimental/dxy/demo/launch_flask.py`:

```
            + f"top_k = {top_k}\n"
            + f"top_p = {top_p}\n"
            + f"temperature = {temperature}\n"
            + f"lines_in_prefix_suffix = {lines_in_prefix_suffix}"
        )
        if lines_in_prefix_suffix <= 0:
            prefix, suffix = None, None
        else:
            prefix, suffix = original_prefix, original_suffix
        start_time = time.time()
        response, full_response, prompt = GLOBAL_EDIT_MODEL(
            selected_code,
            instruction,
            prefix=prefix,
            suffix=suffix,
            lines_in_prefix_suffix=lines_in_prefix_suffix,
            top_k=top_k,
            top_p=top_p,
            temperature=temperature,
        )
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        time_cost = time.time() - start_time

```

Here is the snippet from `base/fastforward/llama/fwd_llama_fp8_multigpu_cuda_graph_test.py`:

```
            ),  # to account for space needed during graph capturing
        )
    finally:
        del step_fn

```

Here is the snippet from `tools/genie/frontend/public/prism.min.js`:

```
          run: function (e, t) {
            var n = j.hooks.all[e];
            if (n && n.length) for (var a, r = 0; (a = n[r++]); ) a(t);
          },
        },
        Token: C,
      };
    function C(e, t, n, a) {
      (this.type = e),
        (this.content = t),
        (this.alias = n),
        (this.length = 0 | (a || "").length);
    }
    function O(e, t, n, a) {
      e.lastIndex = t;
      n = e.exec(n);
      return (
        n &&
          a &&
          n[1] &&
          ((a = n[1].length), (n.index += a), (n[0] = n[0].slice(a))),
        n
      );
    }
    function s() {
      var e = { value: null, prev: null, next: null },
        t = { value: null, prev: e, next: null };
      (e.next = t), (this.head = e), (this.tail = t), (this.length = 0);
    }
    function z(e, t, n) {

```

Here is the snippet from `experimental/rich/systems/multi_round_RAG_system.py`:

```
        logger.debug("== Generate small->large model ==")
        (
            final_gen,
            prompt_toks_final,
            chunks,
            prompt_toks_draft,
            draft_gen,
        ) = self._generate_multi(self.draft_model, self.model, model_input)

        single_prompt = self.model.tokenizer.detokenize(prompt_toks_single)
        draft_prompt = self.draft_model.tokenizer.detokenize(prompt_toks_draft)
        final_prompt = self.model.tokenizer.detokenize(prompt_toks_final)

        fields = {
            "id": shortuuid.uuid()[:8],
            "model_input": {
                "prefix": model_input.prefix,
                "suffix": model_input.suffix,
                "path": model_input.path,
            },
            "single": {"prompt": single_prompt, "generation": single_gen},
            "multi_round": {
                "prompt": final_prompt,
                "generation": final_gen,
                "draft_prompt": draft_prompt,
                "draft_generation": draft_gen,

```

Here is the snippet from `clients/vscode/src/chat/chat-model.ts`:

```
    selectedCode?: string;
    prefix?: string;
    suffix?: string;
    pathName?: string;
    language?: string;
};

```

Here is the snippet from `experimental/yangguang/vscode-ui-prototype/src/lib/diff.ts`:

```
                    new vscode.Position(Math.max(0, line_n + span_lines_count - 1), 0),
                )
                addLineRange.push(thisAddLineRange);
                for (let i = 0; i < span_lines_count; i++) {
                    editStateStore.addedLines.push(line_n + i);
                }
                chunk_remember_added = span;
                chunk_remember_added_line = line_n;
                line_n += span_lines_count;
                if (chunk_remember_removed) {
                    const diff_char = Diff.diffChars(chunk_remember_removed, chunk_remember_added);
                    let char_del_line = chunk_remember_removed_line;
                    let char_ins_line = chunk_remember_added_line;
                    let char_del_pos = 0;
                    let char_ins_pos = 0;
                    diff_char.forEach((part_char: any) => {
                        let txt = part_char.value;
                        if (part_char.removed) {

```

Here is the snippet from `tools/bazel_runner/control/client.py`:

```
                    f"jobName={self.get_run_job_name(run_id, job_id)}",
                    "--tla-str",
                    f"jobId={job_id}",
                    "--tla-str",
                    f"repo={repo_name}",
                    "--tla-str",
                    f"imageName={self.runner_image}",
                    "--tla-str",
                    f"executionSpec={text_format.MessageToString(run_spec)}",
                    "--tla-str",
                    f"cloud={self.cloud}",
                    "--tla-str",
                    f"env={env_str}",
                    "--tla-str",
                    f"pvId={volume_id}",
                    "--tla-str",
                    f"serviceAccountName={self.runner_test_service_account_name}",
                    "--tla-str",
                    f"bazelCacheEndpoint={self.bazel_cache_endpoint}",
                    "--tla-str",
                    f"besEndpoint={self.bes_endpoint}",
                ],
                stdout=config_file,
            )

```

Here is the snippet from `base/fastforward/starcoder/fwd_starcoder_fp8.py`:

```
    auto_capture_graphs: bool = False,
    batch_sizes: Sequence[int] | None = None,
) -> fwd.ForwardStepFn:
    """Generate a forward step function for a given model spec.

    Args:
        model_spec: Model spec.
        num_processes: Number of GPUs to use.
        auto_capture_graphs: Whether to use CUDA graph capturing.
        batch_sizes: Allowed batch sizes; only needed for graph capturing.
    """
    assert num_processes == 1, "Only single GPU is supported for now."
    device = torch.device("cuda")

    logging.info("Using base.fastforward")

    # TODO(arun): Load the checkpoint from a "pipeline" format.
    state_dict = torch.load(model_spec.checkpoint_path, map_location=device)
    model = torch_utils.init_with_weights(
        StarCoder,
        state_dict,
        model_spec,
        auto_capture_graphs=auto_capture_graphs,
        batch_sizes=batch_sizes,
        device=device,
    )

    return model



```

Here is the snippet from `tools/genie/frontend/public/prism.min.js`:

```
                    ? (t.textContent =
                        "✖ Error " +
                        r.status +
                        " while fetching file: " +
                        r.statusText)
                    : (t.textContent =
                        "✖ Error: File does not exist or is empty")));
          }),
          r.send(null));
      }),
      (e = !(Prism.plugins.fileHighlight = {
        highlight: function (e) {
          for (
            var t, n = (e || document).querySelectorAll(u), a = 0;
            (t = n[a++]);

          )
            Prism.highlightElement(t);
        },
      })),
      (Prism.fileHighlight = function () {
        e ||
          (console.warn(
            "Prism.fileHighlight is deprecated. Use `Prism.plugins.fileHighlight.highlight` instead.",
          ),
          (e = !0)),
          Prism.plugins.fileHighlight.highlight.apply(this, arguments);
      }));
  })();

```

Here is the snippet from `tools/bazel_runner/control/client.py`:

```
                JSONNET_BIN,
                "tools/bazel_runner/control/bazel_runner_checkout.jsonnet",
                "-J",
                ".",
                "-y",
                "--tla-str",
                f"namespace={self.namespace}",
                "--tla-str",
                f"runId={run_id}",
                "--tla-str",
                f"jobName={self.get_checkout_job_name(run_id, job_id)}",
                "--tla-str",
                f"jobId={job_id}",
                "--tla-str",
                f"checkoutConfig={text_format.MessageToString(checkout_config)}",
                "--tla-str",
                f"cloud={self.cloud}",
                "--tla-str",
                f"imageName={self.runner_image}",
                "--tla-str",
                f"pvId={volume_id}",
                "--tla-code",
                f"wipe={wipe_flag}",
                "--tla-str",
                f"serviceAccountName={self.runner_service_account_name}",
            ]

```

Here is the snippet from `clients/vscode/webviews/src/apps/next-edit/components/MonacoDiff.svelte`:

```
        path: string | undefined,
        originalCode: string | undefined,
        modifiedCode: string | undefined,
        extraPrefixLines: string[] = [],
        extraSuffixLines: string[] = [],
    ) {
        originalModel?.dispose();
        modifiedModel?.dispose();

        originalCode = originalCode || "";
        modifiedCode = modifiedCode || "";
        const extraPrefix = extraPrefixLines.join("");
        const extraSuffix = extraSuffixLines.join("");
        originalCode = extraPrefix + originalCode + extraSuffix;
        modifiedCode = extraPrefix + modifiedCode + extraSuffix;

        // Monaco requires all URIs passed here to be unique, so we append identifiers below
        originalModel = editor.createModel(
            originalCode,
            undefined,
            path !== undefined
                ? Uri.parse("file://" + path + `#${crypto.randomUUID()}`)
                : undefined,
        );
        modifiedModel = editor.createModel(
            modifiedCode,

```

Here is the snippet from `tools/genie/frontend/public/prism.min.js`:

```
                      m = !!g.greedy,
                      h = g.alias;
                    m &&
                      !g.pattern.global &&
                      ((c = g.pattern.toString().match(/[imsuy]*$/)[0]),
                      (g.pattern = RegExp(g.pattern.source, c + "g")));
                    for (
                      var f = g.pattern || g, b = r.next, y = s;
                      b !== n.tail && !(i && y >= i.reach);
                      y += b.value.length, b = b.next
                    ) {
                      var v = b.value;
                      if (n.length > t.length) return;
                      if (!(v instanceof C)) {
                        var F,
                          k = 1;
                        if (m) {
                          if (!(F = O(f, y, t, p))) break;
                          var x = F.index,
                            w = F.index + F[0].length,
                            P = y;
                          for (P += b.value.length; P <= x; )

```

Here is the snippet from `clients/vscode/src/workspace/blobs-checkpoint-manager.ts`:

```
    // TODO(rich): Enable caching or remove the code.
    async saveToFile(): Promise<void> {
        const data = {
            checkpointId: this._checkpointId,
            blobs: Array.from(this._checkpointBlobNames),
        };

        const serializedData = JSON.stringify(data);
        this._logger.debug(`persisting to ${this._cacheFileUri}`);

        await makeDirs(this._cacheDirUri.fsPath);
        await writeFileUtf8(this._tmpFileUri.fsPath, serializedData);
        await rename(this._tmpFileUri.fsPath, this._cacheFileUri.fsPath);

        this._logger.info(`persisted ${data.blobs.length} entries to ${this._cacheFileUri}`);
    }

    // Load the previously checkpointed blob names from the cache.
    // TODO(rich): Enable caching or remove the code.
    async loadFromFile(): Promise<void> {
        const cacheFileUri = this._cacheFileUri;
        this._logger.info(`reading ${cacheFileUri}`);
        try {
            // TODO(rich): validate what's read from the cache file


```

Here is the snippet from `experimental/dxy/edits/notebooks/random/launch_ft.py`:

```
    debug = data.get("debug", False)

    if request_id is None or instruction is None:
        logger.info("Did not find the request_id or instruction")
        return jsonify({"status": "incorrect inputs"})

    try:
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(f"status: {status}")
        thread_id = threading.get_ident()
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            "FEEDBACK-"
            + utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        debug_json_data = {}
        try:
            debug_json_data = {
                "instruction": instruction,
                "request_id": request_id,
                "thread_id": thread_id,
                "human_annotated_text": human_annotated_text,

```

Here is the snippet from `research/model_server/launch_model_server.py`:

```
        response = CodeEditResponse(
            text=generated_text,
            unknown_blob_names=unknown_blob_names,
        )

        json_data = asdict(request_content)

        json_data.update(**misc_dict)
        json_data.update(
            {
                "response": generated_text,
                "sender_ip": request.remote_addr,
                "timestamp": datetime.now().isoformat(),
                "time_cost_s": time.time() - start_time,
            }
        )

        json_data["blobs"] = doc_ids

        # Log the user id as well
        json_data["user_id"] = get_user_id()
        json_data["system_spec"] = current_app.system_config_str
        # Save all the necessary information into a json file
        save_code_edit_data_json(
            json_data,
            get_request_id(),
            "-Call",
            request_content.completion_url,
        )


```

Here is the snippet from `research/models/llama2_models.py`:

```
        min_prompt_len = len(prompt_tokens)
        # the total buffer size
        if options.max_generated_tokens is None:
            total_len = self.seq_length
        else:
            total_len = min(
                len(prompt_tokens) + options.max_generated_tokens, self.seq_length
            )

        eod_id = self.tokenizer.eod_id
        pad_id = self.tokenizer.pad_id

        device = f"cuda:{int(os.environ.get('LOCAL_RANK', 0))}"
        with torch.inference_mode():
            tokens = torch.full(
                (bsz, total_len), pad_id, dtype=torch.long, device=device
            )
            tokens[0, : len(prompt_tokens)] = torch.tensor(
                prompt_tokens, dtype=torch.long, device=device
            )

            prev_pos = 0
            eod_reached = torch.tensor([False] * bsz, device=device)
            input_text_mask = tokens != pad_id
            for cur_pos in range(min_prompt_len, total_len):
                logits = self.model_runner.generate(

```

Here is the snippet from `clients/vscode/src/code-edit.ts`:

```
                    completionLocation.prefixBegin,
                    completionLocation.suffixEnd,
                    language,
                    // Explicitly do not use checkpoints here since they won't work
                    // when we use a different server for next edits.
                    // TODO: Remove this exception when we switch to production.
                    {
                        checkpointId: undefined,
                        addedBlobs: completionContext.blobNames,
                        deletedBlobs: [],
                    },
                    completionContext.toReplacementText(),
                    nextEditGenerationInfo.gitDiff,
                    nextEditGenerationInfo.yolo
                );
            } else {
                editCodeResponse = await this._apiServer.editCode(
                    requestId,
                    instruction,
                    selectedText,
                    prefixInfo.fullPrefix,
                    suffixInfo.fullSuffix,

```

The developer has file `/home/<USER>/augment/base/prompt_format_retrieve/chatanol_prompt_formatter.py` open and has selected part of the code.

Here is the full file:

```
"""Prompt formatter to format prompts for Ethanol embedding keys and queries."""

from typing import Optional

from base.prompt_format.common import Exchange
from base.prompt_format_completion.token_apportionment import TokenApportionmentConfig
from base.prompt_format_retrieve.prompt_formatter import (
    ChatRetrieverPromptInput,
    RetrieverPromptFormatter,
    PromptFormatterOutput,
)
from base.tokenizers import (
    RetrievalSpecialTokens,
    Tokenizer,
)
from base.tokenizers.deepseek_tokenizer import DeepSeekCoderSpecialTokens


class Chatanol6QueryFormatter(RetrieverPromptFormatter[ChatRetrieverPromptInput]):
    """Query formatter for Chatanol1-16-3 models."""

    input_type = ChatRetrieverPromptInput

    def __init__(
        self,
        apportionment_config: Optional[TokenApportionmentConfig],
        tokenizer: Tokenizer,
    ):
        if not apportionment_config:
            apportionment_config = TokenApportionmentConfig(
                max_content_len=1024,
                input_fraction=0,
                prefix_fraction=0,
                max_path_tokens=0,
            )
        self.apportionment_config = apportionment_config
        self.tokenizer = tokenizer
        assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)
        self.special_tokens = tokenizer.special_tokens
        self.preamble = (
            [tokenizer.special_tokens.begin_sequence]
            if isinstance(tokenizer.special_tokens, DeepSeekCoderSpecialTokens)
            else []
        )

    def _format_user_message(self, message: str, add_newline: bool):
        tokenize = self.tokenizer.tokenize_safe
        tokens = tokenize("### Instruction:\n") + tokenize(message)
        if add_newline:
            tokens += tokenize("\n")
        return tokens

    def _format_chat_message(self, message: str):
        tokenize = self.tokenizer.tokenize_safe
        tokens = tokenize("### Response:\n") + tokenize(message)
        # We always add a newline at the end of the response,
        # since we always expect to see a new instruction.
        tokens += tokenize("\n")
        return tokens

    def _format_exchange(self, exchange: Exchange):
        return self._format_user_message(
            exchange.request_message, True
        ) + self._format_chat_message(exchange.response_text)

    def format_prompt(
        self,
        prompt_input: ChatRetrieverPromptInput,
    ) -> PromptFormatterOutput:
        """Returns tokenized prompt and metadata."""
        # Determine the maximum number of tokens allowed in the prompt,
        # taking into account the reserved token for end_of_key.
        # We will truncate the prompt at the end to ensure it does not exceed
        # this `adjusted_max_tokens` limit.
        adjusted_max_tokens = (
            self.apportionment_config.max_content_len
            - 1  # reserve 1 token for end_of_key
        )
        # How many tokens we have left in the prompt
        token_budget = adjusted_max_tokens - len(self.preamble)

        # Start by adding the last user message
        message = prompt_input.message
        assert message
        prompt_tokens = self._format_user_message(message, False)
        token_budget -= len(prompt_tokens)

[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

        for exchange in reversed(prompt_input.chat_history):
            exchange_tokens = self._format_exchange(exchange)
            if len(exchange_tokens) <= token_budget:
                prompt_tokens = exchange_tokens + prompt_tokens
                token_budget -= len(exchange_tokens)
            else:
                break
        # Finally, we prepend the preamble.
        prompt_tokens = self.preamble + prompt_tokens

        if len(prompt_tokens) > adjusted_max_tokens:
            prompt_tokens = prompt_tokens[:adjusted_max_tokens]
        return PromptFormatterOutput([prompt_tokens])

```

Here is the selected code:

```
        # Include conversation history if budget allows that```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

can you add default value empty list?<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Here is the modified code:

```
    chat_history: Sequence[Exchange] = []
    """The conversation history as a list of request_message/response_text pairs."""
```

I added a default value of an empty list to the `chat_history` field.<|eot_id|><|start_header_id|>user<|end_header_id|>

but that's a wrong way to add empty list as a default value for dataclasses<|eot_id|><|start_header_id|>assistant<|end_header_id|>

I apologize for the mistake. You are correct, the way I added the default value is not correct for dataclasses.

Here is the correct way to add an empty list as a default value for a dataclass field:

```
    chat_history: Sequence[Exchange] = field(default_factory=list)
    """The conversation history as a list of request_message/response_text pairs."""
```

By using `field(default_factory=list)`, we ensure that a new empty list is created for each instance of the dataclass, rather than sharing the same list across all instances.<|eot_id|><|start_header_id|>user<|end_header_id|>

improve english<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Here is the revised text with improved English:

```
    chat_history: Sequence[Exchange] = field(default_factory=list)
    """The conversation history, represented as a sequence of request_message and response_text pairs."""
```

I made some minor adjustments to the docstring to improve clarity and grammar. Let me know if you have any further requests!<|eot_id|><|start_header_id|>user<|end_header_id|>

b<|eot_id|><|start_header_id|>assistant<|end_header_id|>

It seems like you want me to modify the selected code. Here is the modified code:

```
        # How many tokens we allow in the prompt if we
        # reserve 1 token for end_of_key.
        # We will truncate the prompt at the very end to make
        # sure we don't exceed this `adjusted_max_tokens` limit.
        adjusted_max_tokens = self.apportionment_config.max_content_len - 1
        token_budget = [adjusted_max_tokens]
```<|eot_id|><|start_header_id|>user<|end_header_id|>

improve english<|eot_id|><|start_header_id|>assistant<|end_header_id|>

Here is the revised code with improved English:

```
        # Determine the maximum number of tokens allowed in the prompt,
        # taking into account the reserved token for end_of_key.
        # We will truncate the prompt at the end to ensure it does not exceed
        # this `adjusted_max_tokens` limit.
        adjusted_max_tokens = self.apportionment_config.max_content_len - 1
        token_budget = [adjusted_max_tokens]
```

I made some minor adjustments to the comments to improve clarity and grammar. Let me know if you have any further requests!<|eot_id|><|start_header_id|>user<|end_header_id|>

highligh that we prepend the history<|eot_id|><|start_header_id|>assistant<|end_header_id|>
