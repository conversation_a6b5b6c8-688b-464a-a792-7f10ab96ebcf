<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are Aug<PERSON>, an AI code assistant.
Your role is to help software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `clients/vscode/webviews/src/__mocks__/chat/chat-mock.ts`:

```
\`What about a REALLY long code span? Let's make this go longer. On a single line. More. MORE. MOREEEEEEE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!TheEnd.\`

`;

const HTML_RESPONSE = `
To change the key that triggers the \`onSendUserMessage\` function from Enter to Shift+Enter, you can modify the \`onKey\` function to check for the \`Shift\` key along with the \`Enter\` key. Here's how you can do it:

\`\`\`
<script lang="ts">
    import { onKey } from "../../common/utils/keypress";

    // ...

    function onSendUserMessage(event: KeyboardEvent) {
        if (!newMessage.trim()) {
            // Nothing to send
            return;
        }

        // ...
    }

    // ...
</script>

<!-- ... -->

<textarea
    rows="1"
    disabled={disableInput}

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```
        openFileManager.startTracking(pathName, document);

        const insertOffset1 = 0;
        const insertText1 = "xyz";
        kit.applyTextDocumentUpdate(
            openFileManager,
            pathName,
            document,
            insertOffset1,
            insertText1
        );
        kit.verifyNoUpload();

        // Do a second insert more than prefixSize characters away from the first insert. Should
        // trigger upload.
        const insertOffset2 = insertOffset1 + kit.prefixSize + insertText1.length + 1;
        const insertText2 = "pdq";
        kit.applyTextDocumentUpdate(
            openFileManager,
            pathName,
            document,
            insertOffset2,
            insertText2
        );
        kit.verifyUpload(pathName, document);
    });

    test("upload when focus lost", () => {
        const openFileManager = kit.makeOpenFileManager();
        const pathName = "foo.py";

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/change-tracker.test.ts`:

```
            expect(expectedXstart).toBe(testCase.xStart);
            expect(expectedXlength).toBe(testCase.xLength);
        }
    );

    test("delete entire file", () => {
        const origSize = 10;
        const kit = new ChangeTrackerTestKit(origSize);
        kit.apply(0, origSize, 0);
        const [expectedXstart, expectedXlength] = kit.translate(0, 0);
        expect(expectedXstart).toBe(0);
        expect(expectedXlength).toBe(origSize);
    });

    test("delete middle", () => {
        const kit = new ChangeTrackerTestKit(10);
        kit.apply(3, 3, 0);
        const [expectedXstart, expectedXlength] = kit.translate(3, 0);
        expect(expectedXstart).toBe(3);
        expect(expectedXlength).toBe(3);
    });
});

describe("change tracker advance", () => {
    test("advance", () => {
        const kit = new ChangeTrackerTestKit(10000);
        kit.apply(7, 2, 5);
        const newChanges = kit.advance();
        kit.apply(3, 3, 1);
        kit.verifyReconstruct(newChanges);

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```

        const document = kit.openDocument(pathName);
        openFileManager.startTracking(pathName, document);

        kit.applyTextDocumentUpdate(openFileManager, pathName, document, 0, "hello");
        kit.verifyNoUpload();

        // Tell the blob manager that the document no longer has focus. Should trigger upload.
        openFileManager.loseFocus();
        kit.verifyUpload(pathName, document);
    });

    test("upload after large change", () => {
        const openFileManager = kit.makeOpenFileManager();
        const pathName = "foo.py";

        const document = kit.openDocument(pathName);
        openFileManager.startTracking(pathName, document);
        kit.verifyNoUpload();

        // Insert text that is larger than the prefix size. Should trigger upload.
        const insertText = "X".repeat(kit.prefixSize + 1);
        kit.applyTextDocumentUpdate(openFileManager, pathName, document, 0, insertText);
        kit.verifyUpload(pathName, document);
    });


```

Here is the snippet from `clients/vscode/src/__tests__/workspace/change-tracker.test.ts`:

```
        const seq = this.seq++;
        this.tracker.apply(seq, start, charsToDelete, charsToInsert);
        const repeats = Math.ceil(charsToInsert / ChangeTrackerTestKit.insertString.length);
        const toInsert = ChangeTrackerTestKit.insertString
            .repeat(repeats)
            .substring(0, charsToInsert);
        this.document =
            this.document.slice(0, start) + toInsert + this.document.slice(start + charsToDelete);
    }

    public translate(start: number, length: number): [number, number] {
        return this.tracker.translate(start, length);
    }

    public advance(): number {
        this.tracker.advance();
        this.origDocument = this.document.slice();
        return this.tracker.seq;
    }

    public verifyEdits(expected: RawEdit[]) {
        const edits = this.tracker.getEdits();
        this._verifyEdits(edits, expected);
        this._verifyReconstruct(edits);
    }

    public verifyChunks(maxChunkSize: number, maxOffset: number, expected: RawEdit[]) {

```

Here is the snippet from `clients/vscode/webviews/src/__mocks__/chat/chat-mock.ts`:

```
    }

    function onSendUserMessage(event: KeyboardEvent) {
        if (!newMessage.trim()) {
            // Nothing to send
            return;
        }

        // ...
    }

    // ...
</script>

<!-- ... -->

<textarea
    rows="1"
    disabled={disableInput}
    on:keyup={onKey("Enter", onSendUserMessage, true)} // Pass true as the third argument to check for Shift key
    bind:value={newMessage}
    on:focus={() => (inputFocused = true)}
    on:blur={() => (inputFocused = false)}
    placeholder="Type a message..."
></textarea>

<!-- ... -->
\`\`\`

In this custom \`onKey\` function, we're checking if the \`shiftKey\` property of the \`KeyboardEvent\` is \`true\` when the key is \`Enter\`. If \`shiftKey\` is \`true\`, the callback function is called. If \`shiftKey\` is \`false\`, the function does nothing.

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/change-tracker.test.ts`:

```
        { start: 0, length: 3, xStart: 0, xLength: 6 },
        { start: 0, length: 4, xStart: 0, xLength: 6 },
        { start: 0, length: 5, xStart: 0, xLength: 7 },
        { start: 0, length: 10, xStart: 0, xLength: 11 },
        { start: 0, length: 14, xStart: 0, xLength: 13 },
        { start: 3, length: 11, xStart: 3, xLength: 10 },
        { start: 4, length: 10, xStart: 3, xLength: 10 },
        { start: 7, length: 5, xStart: 9, xLength: 2 },
        { start: 10, length: 4, xStart: 9, xLength: 4 },
        { start: 12, length: 2, xStart: 9, xLength: 4 },
    ])(
        "translation($start, $length)",
        (testCase: { start: number; length: number; xStart: number; xLength: number }) => {
            const kit = new ChangeTrackerTestKit(10000);
            kit.apply(3, 3, 1);
            kit.apply(7, 2, 5);
            const [expectedXstart, expectedXlength] = kit.translate(
                testCase.start,
                testCase.length
            );

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```
    ["foo.py", "this is foo.py"],
    ["bar.py", "this is bar.py"],
    ["foo.ipynb", "this is foo.ipynb"],
]);

class OpenFileManagerTestKit {
    static readonly maxBlobSize = 10000;
    static readonly prefixSize = 100;
    static readonly suffixSize = 100;
    static readonly chunkSize = 20;

    public readonly fs = new Map<string, string>();
    public readonly workspaceName = "test workspace";
    public readonly apiServer = new MockAPIServer(OpenFileManagerTestKit.maxBlobSize);
    public readonly completionServer = new CompletionServer(
        this.apiServer,
        OpenFileManagerTestKit.prefixSize,
        OpenFileManagerTestKit.suffixSize,
        OpenFileManagerTestKit.chunkSize
    );
    public readonly blobNameCalculator = new BlobNameCalculator(OpenFileManagerTestKit.maxBlobSize);
    public readonly pathMap = new PathMap();

    public readonly openFileManagers = new Array<OpenFileManager>();

    public readonly logger = getLogger("OpenFileManagerTestKit");


```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```
                endOffset: expectedEnd,
            });
        }
    );

    test.each([
        [10, 30, 10, 30],
        [10, 50, 10, 55],
        [10, 60, 10, 65],
        [50, 60, 50, 65],
        [60, 70, 65, 75],
    ])(
        "translate range with delete(%i, %i)",
        (beginOffset: number, endOffset: number, expectedBegin: number, expectedEnd: number) => {
            const openFileManager = kit.makeOpenFileManager();
            const pathName = "file.py";
            kit.createFile(pathName, "x".repeat(100));
            const document = kit.openDocument(pathName);
            openFileManager.startTracking(pathName, document);
            const blobName = openFileManager.getBlobName(pathName);
            kit.applyTextDocumentUpdate(openFileManager, pathName, document, 50, "", 5);

            let documentRange = { pathName, beginOffset, endOffset };
            const blobRange = openFileManager.translateRange(documentRange);
            expect(blobRange).toEqual({
                blobName,

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```
        [10, 50, 10, 50],
        [10, 52, 10, 50],
        [10, 60, 10, 55],
        [50, 60, 50, 55],
        [52, 60, 50, 55],
        [60, 70, 55, 65],
    ])(
        "translate range with insert(%i, %i)",
        (beginOffset: number, endOffset: number, expectedBegin: number, expectedEnd: number) => {
            const openFileManager = kit.makeOpenFileManager();
            const pathName = "file.py";
            kit.createFile(pathName, "x".repeat(100));
            const document = kit.openDocument(pathName);
            openFileManager.startTracking(pathName, document);
            const blobName = openFileManager.getBlobName(pathName);
            kit.applyTextDocumentUpdate(openFileManager, pathName, document, 50, "abcde", 0);

            let documentRange = { pathName, beginOffset, endOffset };
            const blobRange = openFileManager.translateRange(documentRange);
            expect(blobRange).toEqual({
                blobName,
                beginOffset: expectedBegin,

```

Here is the snippet from `clients/vscode/webviews/src/apps/chat/Chat.svelte`:

```
    let chatModel = new ChatModel();
    let element: RichTextInput;
    let newMessage = "";
    let inputFocused: boolean = false;

    // See if we can send the message -- if we can, we send it and clear the input
    $: canSendMsg = newMessage.trim() !== "" && !$chatModel.awaitingReply;
    const sendMsg = () => {
        if (!canSendMsg) {
            return;
        }
        chatModel.onSendUserMessage(newMessage);
        element?.clearContent();
    };
    // When we clear history, we need to focus the textarea so
    // that the user can start typing again
    const clearHistory = () => {
        element?.focus();
        chatModel.clearHistory();
    };

    // When the element is first rendered and goes from undefined => an HTMLElement,
    // we need to focus it so that the user can start typing
    onMount(() => {
        if (!element) {
            console.warn(
                "Chat element is undefined on mount. This is likely a problem with Svelte.",
            );
            return;

```

Here is the snippet from `clients/vscode/src/__tests__/completions/manual-completion.test.ts`:

```
                _completionLocation: any,
                _language: string,
                _context: any
            ): Promise<CompletionResult> => {
                publishTextDocumentChange(
                    new TextDocumentChangeEvent(
                        new TextDocument(new Uri("example/file", "output"), ""),
                        [
                            TextDocumentContentChangeEvent.forInsert(
                                kit.document,
                                kit.document.positionAt(0),
                                0,
                                "Hello"
                            ),
                        ],
                        undefined
                    )
                );
                return kit.apiCompletionResult;
            }
        );

        await kit.manualCompletion.trigger();
        expect(kit.activeEditor!.edit).toHaveBeenCalledTimes(1);
        expect(kit.editBuilder.insert).toHaveBeenCalledTimes(1);
    });


```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```
        for (let idx = 0; idx < rangeCount; idx++) {
            const toReplace = `${idx}`;
            kit.applyTextDocumentUpdate(
                openFileManager,
                pathName,
                document,
                idx * rangeSize,
                toReplace,
                toReplace.length
            );

            const chunkInfo = kit.getRecentChunkInfo(openFileManager, true);
            expect(chunkInfo.length).toBeGreaterThan(0);
            expect(chunkInfo.length).toBeLessThanOrEqual(maxTotalChunks * 2);
            chunkCountHWM = Math.max(chunkCountHWM, chunkInfo.length);
        }
        expect(chunkCountHWM).toBeLessThanOrEqual(maxTotalChunks * 2);
    });

    // Verifies that the blob manager doesn't accumulate an unbounded number of "recent" chunks
    // from multiple files.
    test("gc chunks, multi file", async () => {
        jest.useFakeTimers();

        const documentSize = 10;
        const openFileManager = kit.makeOpenFileManager();


```

Here is the snippet from `clients/vscode/src/__tests__/workspace/change-tracker.test.ts`:

```
            kit.verifyEdits(testCase.expected);
        }
    );

    test("single replacement", () => {
        const kit = new ChangeTrackerTestKit();
        kit.apply(100, 10, 5);
        kit.verifyEdits([{ start: 100, length: 5, origStart: 100, origLength: 10 }]);
    });

    test("two replacements", () => {
        const kit = new ChangeTrackerTestKit();
        kit.apply(102, 5, 3);
        kit.apply(108, 2, 4);
        kit.verifyEdits([
            { start: 102, length: 3, origStart: 102, origLength: 5 },
            { start: 108, length: 4, origStart: 110, origLength: 2 },
        ]);
    });

    // "multiple replacements" tests interactions between replacements. Each test case applies a
    // replacement against a predetermined state with two replacements, and verifies the result.
    // The test cases are annotated with the position of their replacement relative to the two
    // original replacements.
    test.each([
        /*
         * Start before first update
         */
        {

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```
                return { blobName };
            }
        );
        kit.apiServer.findMissing.mockImplementation(
            async (_: string[]): Promise<FindMissingResult> => {
                return {
                    unknownBlobNames: [],
                    nonindexedBlobNames: [],
                };
            }
        );

        // Make an update in the middle of the file. Choose an odd-numbered range, because we
        // will be updating the even ranges below. Set `delayBlobName` to force a delay in the
        // upload of this blob.
        const initialRange = 5;
        const initialOffset = initialRange * rangeSize;
        const toReplace = "AAA";
        const editEvent = document.replace(initialOffset, 0, toReplace);
        const uploadedText1 = document.getText();
        const uploadedBlobName1 = kit.calculateBlobName(pathName, uploadedText1)!;
        delayBlobName = uploadedBlobName1;
        openFileManager.applyTextDocumentChange(pathName, editEvent);


```

The developer has file `/home/<USER>/augment-fe/clients/vscode/src/utils/webviews/messaging.ts` open and has selected part of the code.

Here is the full file:

```
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

```

Here is the selected code:

```
import type * as vscode from "vscode";

import {
    type WebViewMessage,
    type AsyncWebViewMessage,
    WebViewMessageType,
} from "../../webview-providers/webview-messages";

import { getNonce } from "../../webview-providers/webview-utils";


interface SendFnT<ResT> {
    (msg: ResT): void;
}
interface RecvFnRegistrar<ReqT> {
    (handler: (msg: ReqT) => Promise<boolean>): () => void;
}

function getHostRecvFnRegistrar<ReqT extends WebViewMessage>(
    webview: vscode.Webview
): RecvFnRegistrar<ReqT> {
    return (handler: (msg: ReqT) => Promise<boolean>): (() => void) => {
        const disposer = webview.onDidReceiveMessage(handler);
        return disposer.dispose;
    };
}

/**
 *
 * @param handler A function to register on the window to listen for webview messages
 *              and handle them. Note that the handler will be called specifically with
 *              a WebViewMessage, and not a generic MessageEvent.
 * @returns A function to call to unregister the handler
 */
function clientRecvFnRegistrar(
    handler: (msg: WebViewMessage) => Promise<boolean>
): () => void {
    // Wrap the handler to see if it's a WebViewMessage
    const wrappedHandler = async (e: MessageEvent<WebViewMessage>) => {
        handler(e.data);
    };
    window.addEventListener("message", wrappedHandler);

    // Return a function to unregister the handler
    return () => {
        window.removeEventListener("message", wrappedHandler);
    };
}

interface PromiseFns<T> {
    resolve: (value: T | PromiseLike<T>) => void;
    reject: (reason?: any) => void;
}

export class AsyncWebviewMessageHandler {
    private _disposers = new Array<() => void>();

    static fromHost(webview: vscode.Webview): AsyncWebviewMessageHandler {
        return new AsyncWebviewMessageHandler(
            (msg: AsyncWebViewMessage<WebViewMessage>) => webview.postMessage(msg),
            getHostRecvFnRegistrar(webview)
        );
    }

    static fromWebview(
        postMessage: SendFnT<AsyncWebViewMessage<WebViewMessage>>
    ): AsyncWebviewMessageHandler {
        return new AsyncWebviewMessageHandler(postMessage, clientRecvFnRegistrar);
    }

    constructor(
        private _postMessage: SendFnT<AsyncWebViewMessage<WebViewMessage>>,
        private _receiverRegistrar: RecvFnRegistrar<WebViewMessage>
    ) {}

    private createWrappedResponse = <ResT extends WebViewMessage>(
        requestId: string,
        response: ResT
    ): AsyncWebViewMessage<ResT> => {
        return {
            type: WebViewMessageType.asyncWrapper,
            requestId,
            baseMsg: response,
        };
    };

    /**
     * Creates a wrapped message handler that can be used to handle messages of a specific type in
     * an async fashion. The handler itself handles the base type, and the wrapped handler performs the following:
     * - Type checks and unwraps incoming async messages
     * - Calls the handler with the unwrapped message
     * - Wraps the response in an async message and sends it
     *
     * @param type The type of underlying message that the handler should handle.
     * @param handler The function that will be called with the underlying message of the specified type.
     * @returns A function that can be used to handle messages of the specified type wrapped in an async message.
     */
    private createWrappedHandler = <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => Promise<ResT>
    ): ((msg: WebViewMessage) => Promise<boolean>) => {
        return async (msg: WebViewMessage): Promise<boolean> => {
            // We only handle async wrapper types that wrap the passed type
            if (msg.type !== WebViewMessageType.asyncWrapper || msg.baseMsg.type !== type) {
                return false;
            }

            // Handle the unwrapped message
            const response = await handler(msg.baseMsg as ReqT);

            // Create a wrapped response and send it
            const wrappedResponse = this.createWrappedResponse(msg.requestId, response);
            this._postMessage(wrappedResponse);
            return true;
        };
    };

    /**
     * Registers a handler for a specific type of message.
     *
     * @param type The type of message to handle.
     * @param handler The function to handle the message.
     */
    registerHandler = <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => Promise<ResT>
    ): void => {
        const wrappedHandler = this.createWrappedHandler(type, handler);
        const disposer = this._receiverRegistrar(wrappedHandler);
        this._disposers.push(disposer);
    };

    dispose = () => {
        for (const disposer of this._disposers) {
            disposer();
        }
    };
}

export class AsyncWebviewMessageSender {
    private _idToPromiseFns = new Map<string, PromiseFns<WebViewMessage>>();
    public dispose: () => void;

    /**
     * Creates an instance of AsyncWebviewMessageSender for a host of a webview.
     *
     * @param webview The webview to which messages will be posted.
     * @returns An instance of AsyncWebviewMessageSender.
     */
    static fromHost(webview: vscode.Webview): AsyncWebviewMessageSender {
        return new AsyncWebviewMessageSender(
            (msg: WebViewMessage) => webview.postMessage(msg),
            getHostRecvFnRegistrar(webview)
        );
    }

    /**
     * Creates an instance of AsyncWebviewMessageSender for a webview.
     *
     * @param postMessage A function to post messages to the webview.
     * @returns An instance of AsyncWebviewMessageSender.
     */
    static fromWebview(postMessage: SendFnT<WebViewMessage>): AsyncWebviewMessageSender {
        return new AsyncWebviewMessageSender(postMessage, windowWebviewReceiverRegistrar);
    }

    /**
     * Constructs an AsyncWebviewMessageSender instance.
     *
     * @param _postMsgFn A function to post messages to the webview.
     * @param _receiverRegistrar A function to register a message handler for the webview.
     * @param _timeoutMs The timeout duration in milliseconds for message responses.
     */
    constructor(
        private _postMsgFn: SendFnT<WebViewMessage>,
        private _receiverRegistrar: RecvFnRegistrar<WebViewMessage>,
        private _timeoutMs: number = 1000
    ) {
        this.dispose = this._receiverRegistrar(this.handleMessage);
    }

    /**
     * Wraps a message in an AsyncWebViewMessage.
     *
     * @param msg The message to wrap.
     * @returns The wrapped message.
     */
    private createWrappedMsg = <T extends WebViewMessage>(msg: T): AsyncWebViewMessage<T> => {
        return {
            type: WebViewMessageType.asyncWrapper,
            requestId: getNonce(),
            baseMsg: msg,
        };
    };

    /**
     * Handles incoming messages and resolves promises if applicable.
     *
     * @param msg The incoming message.
     * @returns A boolean indicating whether the message was handled.
     */
    private handleMessage = async (msg: WebViewMessage): Promise<boolean> => {
        // We only handle async wrapper types
        if (msg.type !== WebViewMessageType.asyncWrapper) {
            return false;
        }

        // If there is no outstanding promise, then do not resolve
        const fns = this._idToPromiseFns.get(msg.requestId);
        if (!fns) {
            return false;
        }

        // Resolve the function with the unwrapped result
        this._idToPromiseFns.delete(msg.requestId);
        fns.resolve(msg.baseMsg);
        return true;
    };

    /**
     * Sends a message and sets a timeout for response.
     *
     * @param msg The message to send.
     */
    private _sendOrTimeout = <ReqT extends WebViewMessage>(
        msg: AsyncWebViewMessage<ReqT>
    ): void => {
        this._postMsgFn(msg);

        // If we don't get a response in time, reject the promise
        setTimeout(() => {
            const fns = this._idToPromiseFns.get(msg.requestId);
            if (fns) {
                fns.reject(new Error("Request timed out"));
            }
        }, this._timeoutMs);
    };

    /**
     * Sends a message and returns a promise that resolves with the response.
     *
     * @param msg The message to send.
     * @returns A promise that resolves with the response message.
     */
    send = async <ReqT extends WebViewMessage, ResT extends WebViewMessage>(
        msg: ReqT
    ): Promise<ResT> => {
        const wrappedReq = this.createWrappedMsg(msg);

        // Registers the requests's resolver and rejectors
        const promise = new Promise<ResT>((resolve: any, reject: any) => {
            this._idToPromiseFns.set(wrappedReq.requestId, { resolve, reject });
        });

        this._sendOrTimeout(wrappedReq);
        return promise;
    };
}
```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If provided snippets are not enough to answer a question, politely ask user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

Can you replace all full-word "message" references to "msg" instead, with the correct capitalization?<|eot_id|><|start_header_id|>assistant<|end_header_id|>
