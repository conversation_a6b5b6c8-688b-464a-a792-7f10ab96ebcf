I'd be happy to help. Here is the modified code:

```
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

```

Here is the selected code:

```
import type * as vscode from "vscode";

import {
    type WebViewMsg,
    type AsyncWebViewMsg,
    WebViewMessageType,
} from "../../webview-providers/webview-msgs";

import { getNonce } from "../../webview-providers/webview-utils";


interface SendFnT<ResT> {
    (msg: ResT): void;
}
interface RecvFnRegistrar<ReqT> {
    (handler: (msg: ReqT) => Promise<boolean>): () => void;
}

function getHostRecvFnRegistrar<ReqT extends WebViewMsg>(
    webview: vscode.Webview
): RecvFnRegistrar<ReqT> {
    return (handler: (msg: ReqT) => Promise<boolean>): (() => void) => {
        const disposer = webview.onDidReceiveMessage(handler);
        return disposer.dispose;
    };
}

/**
 *
 * @param handler A function to register on the window to listen for webview msgs
 *              and handle them. Note that the handler will be called specifically with
 *              a WebViewMsg, and not a generic MessageEvent.
 * @returns A function to call to unregister the handler
 */
function clientRecvFnRegistrar(
    handler: (msg: WebViewMsg) => Promise<boolean>
): () => void {
    // Wrap the handler to see if it's a WebViewMsg
    const wrappedHandler = async (e: MessageEvent<WebViewMsg>) => {
        handler(e.data);
    };
    window.addEventListener("message", wrappedHandler);

    // Return a function to unregister the handler
    return () => {
        window.removeEventListener("message", wrappedHandler);
    };
}

interface PromiseFns<T> {
    resolve: (value: T | PromiseLike<T>) => void;
    reject: (reason?: any) => void;
}

export class AsyncWebviewMsgHandler {
    private _disposers = new Array<() => void>();

    static fromHost(webview: vscode.Webview): AsyncWebviewMsgHandler {
        return new AsyncWebviewMsgHandler(
            (msg: AsyncWebViewMsg<WebViewMsg>) => webview.postMessage(msg),
            getHostRecvFnRegistrar(webview)
        );
    }

    static fromWebview(
        postMessage: SendFnT<AsyncWebViewMsg<WebViewMsg>>
    ): AsyncWebviewMsgHandler {
        return new AsyncWebviewMsgHandler(postMessage, clientRecvFnRegistrar);
    }

    constructor(
        private _postMessage: SendFnT<AsyncWebViewMsg<WebViewMsg>>,
        private _receiverRegistrar: RecvFnRegistrar<WebViewMsg>
    ) {}

    private createWrappedResponse = <ResT extends WebViewMsg>(
        requestId: string,
        response: ResT
    ): AsyncWebViewMsg<ResT> => {
        return {
            type: WebViewMessageType.asyncWrapper,
            requestId,
            baseMsg: response,
        };
    };

    /**
     * Creates a wrapped msg handler that can be used to handle msgs of a specific type in
     * an async fashion. The handler itself handles the base type, and the wrapped handler performs the following:
     * - Type checks and unwraps incoming async msgs
     * - Calls the handler with the unwrapped msg
     * - Wraps the response in an async msg and sends it
     *
     * @param type The type of underlying msg that the handler should handle.
     * @param handler The function that will be called with the underlying msg of the specified type.
     * @returns A function that can be used to handle msgs of the specified type wrapped in an async msg.
     */
    private createWrappedHandler = <ReqT extends WebViewMsg, ResT extends WebViewMsg>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => Promise<ResT>
    ): ((msg: WebViewMsg) => Promise<boolean>) => {
        return async (msg: WebViewMsg): Promise<boolean> => {
            // We only handle async wrapper types that wrap the passed type
            if (msg.type !== WebViewMessageType.asyncWrapper || msg.baseMsg.type !== type) {
                return false;
            }

            // Handle the unwrapped msg
            const response = await handler(msg.baseMsg as ReqT);

            // Create a wrapped response and send it
            const wrappedResponse = this.createWrappedResponse(msg.requestId, response);
            this._postMessage(wrappedResponse);
            return true;
        };
    };

    /**
     * Registers a handler for a specific type of msg.
     *
     * @param type The type of msg to handle.
     * @param handler The function to handle the msg.
     */
    registerHandler = <ReqT extends WebViewMsg, ResT extends WebViewMsg>(
        type: WebViewMessageType,
        handler: (msg: ReqT) => Promise<ResT>
    ): void => {
        const wrappedHandler = this.createWrappedHandler(type, handler);
        const disposer = this._receiverRegistrar(wrappedHandler);
        this._disposers.push(disposer);
    };

    dispose = () => {
        for (const disposer of this._disposers) {
            disposer();
        }
    };
}

export class AsyncWebviewMsgSender {
    private _idToPromiseFns = new Map<string, PromiseFns<WebViewMsg>>();
    public dispose: () => void;

    /**
     * Creates an instance of AsyncWebviewMsgSender for a host of a webview.
     *
     * @param webview The webview to which msgs will be posted.
     * @returns An instance of AsyncWebviewMsgSender.
     */
    static fromHost(webview: vscode.Webview): AsyncWebviewMsgSender {
        return new AsyncWebviewMsgSender(
            (msg: WebViewMsg) => webview.postMessage(msg),
            getHostRecvFnRegistrar(webview)
        );
    }

    /**
     * Creates an instance of AsyncWebviewMsgSender for a webview.
     *
     * @param postMessage A function to post msgs to the webview.
     * @returns An instance of AsyncWebviewMsgSender.
     */
    static fromWebview(postMessage: SendFnT<WebViewMsg>): AsyncWebviewMsgSender {
        return new AsyncWebviewMsgSender(postMessage, windowWebviewReceiverRegistrar);
    }

    /**
     * Constructs an AsyncWebviewMsgSender instance.
     *
     * @param _postMsgFn A function to post msgs to the webview.
     * @param _receiverRegistrar A function to register a msg handler for the webview.
     * @param _timeoutMs The timeout duration in milliseconds for msg responses.
     */
    constructor(
        private _postMsgFn: SendFnT<WebViewMsg>,
        private _receiverRegistrar: RecvFnRegistrar<WebViewMsg>,
        private _timeoutMs: number = 1000
    ) {
        this.dispose = this._receiverRegistrar(this.handleMsg);
    }

    /**
     * Wraps a msg in an AsyncWebViewMsg.
     *
     * @param msg The msg to wrap.
     * @returns The wrapped msg.
     */
    private createWrappedMsg = <T extends WebViewMsg>(msg: T): AsyncWebViewMsg<T> => {
        return {
            type: WebViewMessageType.asyncWrapper,
            requestId: getNonce(),
            baseMsg: msg,
        };
    };

    /**
     * Handles incoming msgs and resolves promises if applicable.
     *
     * @param msg The incoming msg.
     * @returns A boolean indicating whether the msg was handled.
     */
    private handleMsg = async (msg: WebViewMsg): Promise<boolean> => {
        // We only handle async wrapper types
        if (msg.type !== WebViewMessageType.asyncWrapper) {
            return false;
        }

        // If there is no outstanding promise, then do not resolve
        const fns = this._idToPromiseFns.get(msg.requestId);
        if (!fns) {
            return false;
        }

        // Resolve the function with the unwrapped result
        this._idToPromiseFns.delete(msg.requestId);
        fns.resolve(msg.baseMsg);
        return true;
    };

    /**
     * Sends a msg and sets a timeout for response.
     *
     * @param msg The msg to send.
     */
    private _sendOrTimeout = <ReqT extends WebViewMsg>(
        msg: AsyncWebViewMsg<ReqT>
    ): void => {
        this._postMsgFn(msg);

        // If we don't get a response in time, reject the promise
        setTimeout(() => {
            const fns = this._idToPromiseFns.get(msg.requestId);
            if (fns) {
                fns.reject(new Error("Request timed out"));
            }
        }, this._timeoutMs);
    };

    /**
     * Sends a msg and returns a promise that resolves with the response.
     *
     * @param msg The msg to send.
     * @returns A promise that resolves with the response msg.
     */
    send = async <ReqT extends WebViewMsg, ResT extends WebViewMsg>(
        msg: ReqT
    ): Promise<ResT> => {
        const wrappedReq = this.createWrappedMsg(msg);

        // Registers the requests's resolver and rejectors
        const promise = new Promise<ResT>((resolve: any, reject: any) => {
            this._idToPromiseFns.set(wrappedReq.requestId, { resolve, reject });
        });

        this._sendOrTimeout(wrappedReq);
        return promise;
    };
}
```
