Here is a template for implementing an `AbstractSystem` using a `RetrievalDatabase` for retrieval:
```
from research.eval.harness.systems.abs_system import AbstractSystem
from research.retrieval.retrieval_database import RetrievalDatabase

class MySystem(AbstractSystem):
    def __init__(self, model, generation_options, retriever_config):
        self.model = model
        self.generation_options = generation_options
        self.retriever = RetrievalDatabase(**retriever_config)
        self.__loaded = False

    def load(self):
        if not self.__loaded:
            self.model.load()
            self.retriever.load()
        self.__loaded = True

    def unload(self):
        if self.__loaded:
            self.model.unload()
            self.retriever.unload()
            self.__loaded = False

    def add_docs(self, src_files: list[Document]):
        self.retriever.add_documents(src_files)

    def clear_retriever(self):
        self.retriever.clear()

    def generate(self, model_input: ModelInput) -> tuple[str, list[int], list[Chunk]]:
        retrieved_chunks = self._retrieve(model_input)
        model_input.retrieved_chunks = retrieved_chunks
        prompt_toks, _ = self.model.prompt_formatter.prepare_prompt(model_input)
        generation, prompt_toks = self._generate(model_input)
        generation = self._completion_postprocessing(generation)
        return generation, prompt_toks, retrieved_chunks

    def _retrieve(self, model_input: ModelInput) -> list[Chunk]:
        query = self.model.prompt_formatter.get_query(model_input)
        retrieved_chunks = self.retriever.query(query, top_k=10)
        return retrieved_chunks

    def _generate(self, model_input: ModelInput) -> tuple[str, list[int]]:
        # Implement how to generate a completion using the model
        pass

    def _completion_postprocessing(self, generation: str) -> str:
        # Implement any post-processing on the generated completion
        pass

    def log_likelihood_continuation(
        self, model_input: ModelInput, continuation: str
    ) -> Optional[float]:
        # Implement how to compute the log likelihood of a continuation
        pass
```
Important details to note:

* The `RetrievalDatabase` is used to store and retrieve documents.
* The `add_docs` method adds documents to the retriever.
* The `clear_retriever` method clears the retriever.
* The `_retrieve` method is used to retrieve chunks from the retriever based on a query.
* The `generate` method uses the `_retrieve` method to retrieve chunks, and then generates a completion using the model.
* The `_generate` method should be implemented to generate a completion using the model.
* The `_completion_postprocessing` method should be implemented to perform any post-processing on the generated completion.
* The `log_likelihood_continuation` method should be implemented to compute the log likelihood of a continuation.

This template provides a basic structure for implementing an `AbstractSystem` using a `RetrievalDatabase` for retrieval. You will need to fill in the implementation details for the `_generate`, `_completion_postprocessing`, and `log_likelihood_continuation` methods.
