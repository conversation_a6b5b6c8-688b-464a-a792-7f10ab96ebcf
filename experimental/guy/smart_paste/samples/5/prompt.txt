<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are <PERSON><PERSON>, an AI code assistant.
Your role is to help a software developer by following their instructions and answering their questions related to code and general software engineering.

Below are some relevant code snippets from the developer's project.

Here is the snippet from `experimental/yangguang/vscode-ui-prototype/webview/vite.config.ts`:

```
        assetFileNames: `assets/[name].[ext]`,
        sourcemap: 'inline',

      },
      plugins: [
        {
          // An attempt to speed up local development build process.
          // If not doing anything code highlighting related, comment out `import './monaco-workers';` on `/src/main.ts` can make `npm run watch` faster.
          name: 'skip-workers',
          transform(code, id) {
            if (id.includes('workers')) {
              return code;
            }
          }
        },
      ],
    },
  },
});

```

Here is the snippet from `services/support/frontend/jest.config.js`:

```
module.exports = {
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.svg$": "jest-transform-stub",
    "^.+\\.css$": "jest-transform-stub",
  },
};

```

Here is the snippet from `research/core/tests/test_llama_prompt_formatter.py`:

```
        formatter = CodeEditTemplateBasedPromptFormatter(
            template=template, tokenizer_name="deepseekcoderinstructtokenizer"
        )
        actual_token_ids, _ = formatter.prepare_prompt(example)
        actual_tokens = formatter.tokenizer.detokenize(actual_token_ids)
        assert actual_tokens == expected_tokens

        # Test formatter with specified `template_file`.
        with tempfile.NamedTemporaryFile(mode="w+t", delete=False) as temp_file:
            temp_file.write(template)
            temp_file_name = temp_file.name

        formatter = CodeEditTemplateBasedPromptFormatter(
            template_file=temp_file_name,
            tokenizer_name="deepseekcoderinstructtokenizer",
        )

        actual_token_ids, _ = formatter.prepare_prompt(example)
        actual_tokens = formatter.tokenizer.detokenize(actual_token_ids)
        assert actual_tokens == expected_tokens

    def test_format_ignore_context(self):
        """Test a simple example."""


```

Here is the snippet from `tools/deploy_runner/web/frontend/jest.config.js`:

```
module.exports = {
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.svg$": "jest-transform-stub",
    "^.+\\.css$": "jest-transform-stub",
  },
};

```

Here is the snippet from `clients/vscode/src/chat/chat-model.ts`:

```
    selectedCode?: string;
    prefix?: string;
    suffix?: string;
    pathName?: string;
    language?: string;
};

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/change-tracker.test.ts`:

```
        { start: 0, length: 3, xStart: 0, xLength: 6 },
        { start: 0, length: 4, xStart: 0, xLength: 6 },
        { start: 0, length: 5, xStart: 0, xLength: 7 },
        { start: 0, length: 10, xStart: 0, xLength: 11 },
        { start: 0, length: 14, xStart: 0, xLength: 13 },
        { start: 3, length: 11, xStart: 3, xLength: 10 },
        { start: 4, length: 10, xStart: 3, xLength: 10 },
        { start: 7, length: 5, xStart: 9, xLength: 2 },
        { start: 10, length: 4, xStart: 9, xLength: 4 },
        { start: 12, length: 2, xStart: 9, xLength: 4 },
    ])(
        "translation($start, $length)",
        (testCase: { start: number; length: number; xStart: number; xLength: number }) => {
            const kit = new ChangeTrackerTestKit(10000);
            kit.apply(3, 3, 1);
            kit.apply(7, 2, 5);
            const [expectedXstart, expectedXlength] = kit.translate(
                testCase.start,
                testCase.length
            );

```

Here is the snippet from `research/data/eval/data_step/extract_code.py`:

```
    """
    try:
        buf = filename.read_bytes()
        return buf.decode("UTF-8")
    except UnicodeDecodeError:
        # bad encoding, try different encoding
        try:
            enc = chardet.detect(buf)
            if enc["encoding"] is None:
                print(f"No encoding found for {filename}", file=sys.stderr)
                return None
            return buf.decode(enc["encoding"])
        except UnicodeDecodeError as e:
            print(f"Unicode decode error {filename}: {str(e)}", file=sys.stderr)
            return None


def run(language: str, proj_dir: pathlib.Path, out_dir: pathlib.Path):
    # Use Pygments to get language extensions.
    # Note: by lower()ing the extension, we may misidentify C files as C++ files,
    # and vice-versa.
    lexer = get_lexer_by_name(language)
    language_extensions = set(ext.lower()[1:] for ext in lexer.filenames)

    out_dir.mkdir(parents=True, exist_ok=True)

    files_found = 0
    for root, _, files in os.walk(proj_dir):

```

Here is the snippet from `tools/genie/frontend/jest.config.js`:

```
module.exports = {
  testEnvironment: "jsdom",
  transform: {
    "^.+\\.svg$": "jest-transform-stub",
    "^.+\\.css$": "jest-transform-stub",
  },
};

```

Here is the snippet from `research/environments/containers/gcp-sync.py`:

```
    gsutil_cmd = (
        ["/usr/local/google-cloud-sdk/bin/gsutil"]
        + gsutil_options
        + [
            "rsync",
            "-Pr",
            str(source_path),
            gsurl,
        ]
    )

    with (
        tempfile.TemporaryDirectory() as tmpdir,
        tempfile.NamedTemporaryFile(mode="w+", dir=tmpdir, delete=False) as f,
    ):
        f.write(ssh_key)
        f.flush()
        Path(f.name).chmod(0o400)
        sync_cmd = [
            "ssh",
            "-i",
            f.name,
            "-o",
            "StrictHostKeyChecking=no",
            "-o",
            "UserKnownHostsFile=/dev/null",
            f"augment@{sync_ip}",
            " ".join(gsutil_cmd),  # type: ignore
        ]
        subprocess.check_call(sync_cmd)

```

Here is the snippet from `tools/bazel_runner/control/client.py`:

```
        with tempfile.NamedTemporaryFile(delete=False) as config_file:
            env_str = "CPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.SINGLE_GPU:
                env_str = "SINGLE_GPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.MULTI_GPU:
                env_str = "MULTI_GPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.LARGE_GPU:
                env_str = "LARGE_GPU"
            if run_spec.env == bazel_runner_pb2.SystemEnv.PREMIUM_CPU:
                env_str = "PREMIUM_CPU"
            subprocess.check_call(
                [
                    JSONNET_BIN,
                    "tools/bazel_runner/control/bazel_runner_run.jsonnet",
                    "-y",
                    "-J",
                    ".",
                    "--tla-str",
                    f"namespace={self.namespace}",
                    "--tla-str",
                    f"runId={run_id}",
                    "--tla-str",

```

Here is the snippet from `clients/vscode/webviews/vite-common.ts`:

```
        },
    });
}

```

Here is the snippet from `clients/vscode/src/logging.ts`:

```
            }),
            format.printf(
                (info) => `${info.timestamp} [${info.level}] '${info.prefix}': ${info.message}`
            )
        ),
        transports: logEndpoints,
    });
    return logger;
}

export function getLogger(prefix: string): Logger {
    return singletonLogger().child({ prefix });
}

```

Here is the snippet from `experimental/dxy/exps/xlib.py`:

```
            self.retriever.remove_all_docs()

    def generate(
        self,
        model_input: ModelInput,
    ) -> CompletionResult:
        """Generate a completion."""
        model_input = self.preprocessor(
            model_input, self.retriever, self.model.prompt_formatter
        )
        extra_out = ExtraGenerationOutputs()
        generation = self.model.generate(
            model_input, self.generation_options, extra_out
        )
        final_generation = self.postprocessor(generation, model_input)
        return CompletionResult(
            generated_text=final_generation,
            prompt_tokens=extra_out.prompt_tokens or [],
            retrieved_chunks=model_input.retrieved_chunks,
        )

```

Here is the snippet from `clients/vscode/webviews/vite-common.ts`:

```
import { defineConfig } from "vite";
import { svelte } from "@sveltejs/vite-plugin-svelte";
import { resolve } from "path";
import replace from "@rollup/plugin-replace";

export function createConfig(
    entryPoint: string,
    appName: string,
    inlineDynamicImports: boolean = true,
) {
    return defineConfig({
        plugins: [svelte()],
        resolve: {
            preserveSymlinks: true,
        },
        build: {
            outDir: "dist",
            lib: {
                entry: resolve(__dirname, entryPoint),
                fileName: appName,
                formats: ["es"],
            },
            rollupOptions: {
                output: {
                    // Force output to be a single file.
                    inlineDynamicImports,
                    assetFileNames: `${appName}.[ext]`,
                },
                plugins: [
                    /**

```

Here is the snippet from `experimental/dxy/notebooks/hydra-2023-07-21.ipynb`:

```
    "    prefix = patch.file_content[:patch.char_start]\n",
    "    suffix = patch.file_content[patch.char_end:]\n",
    "    gen_limit = 128\n",
    "    len_limit = model.seq_length - gen_limit\n",
    "    prefix = prefix[-len_limit:] if len(prefix) > len_limit else prefix\n",
    "    completion = model.generate(\n",
    "        core.ModelInput(prefix=prefix),\n",
    "        models.GenerationOptions(temperature=0., max_generated_tokens=gen_limit))\n",
    "    if completion is None:\n",
    "        print(colored(prefix, color=\"blue\"))\n",
    "    # print(f\"completion length: {len(completion)}\\n\")\n",
    "    patch = patch.with_patch_content(completion)\n",
    "\n",
    "    thread = threading.Thread(target=driver.dispatch, args=(patch,))\n",
    "    # thread.start()\n",
    "    dispatch_threads.append(thread)\n",
    "\n",
    "\n",
    "# for thread in dispatch_threads:\n",
    "#     thread.join()\n",
    "# driver.wait_until_finish()\n",
    "# driver.collect_result()"
   ]
  },
  {

```

Here is the snippet from `experimental/dxy/notebooks/test-2024-06-02.ipynb`:

```
{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "%load_ext autoreload\n",
    "%autoreload 2"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import re\n",
    "from base.languages.language_guesser import guess_language\n",
    "from base.languages.unit_test_guesser import is_unit_test, pattern_list_by_langid\n",
    "\n",
    "# path = \"foo.test.ts\"\n",
    "# path = 'a/unit-test/x.py'\n",
    "path = 'src/commands/generateTest/generateTestAction.ts'\n",
    "lang = guess_language(path)\n",
    "\n",
    "patterns = pattern_list_by_langid[lang]\n",
    "for pattern in patterns:\n",
    "    print(f\"Pattern: {pattern}\")\n",

```

Here is the snippet from `experimental/dxy/notebooks/xtry/chatbot.ipynb`:

```
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "result = model.generate(ModelInput(prefix, suffix), options)\n",
    "print(\"\\nresult:\")\n",
    "print(result)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "tokens = model.tokenizer.tokenize(result)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "model.tokenizer.tokenize(\"for\") in tokens"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {

```

Here is the snippet from `clients/vscode/src/__tests__/workspace/open-file-manager.test.ts`:

```

    test.each([
        [10, 30, 10, 30],
        [10, 50, 10, 50],
        [10, 52, 10, 50],
        [10, 60, 10, 55],
        [50, 60, 50, 55],
        [52, 60, 50, 55],
        [60, 70, 55, 65],
    ])(
        "translate range with insert(%i, %i)",
        (beginOffset: number, endOffset: number, expectedBegin: number, expectedEnd: number) => {
            const openFileManager = kit.openFileManager;
            const pathName = "file.py";
            kit.createFile(pathName, "x".repeat(100));
            const document = kit.openDocument(pathName);
            openFileManager.startTracking(pathName, document);
            const blobName = openFileManager.getBlobName(pathName);
            kit.applyTextDocumentUpdate(openFileManager, pathName, document, 50, "abcde", 0);

            let documentRange = { pathName, beginOffset, endOffset };
            const blobRange = openFileManager.translateRange(documentRange);
            expect(blobRange).toEqual({
                blobName,

```

Here is the snippet from `experimental/dxy/code/file_helper.py`:

```
def pickle_load(file_path: str, ext: str = ".pbz2"):
    """Use pickle to load the file on different systems."""
    # return pickle.load(open(file_path, "rb"))
    file_path = append_ext_if_possible(file_path, ext)
    if not os.path.isfile(file_path):
        print(f"Did not find the file {file_path}.")
        return None
    with bz2.BZ2File(file_path, "rb") as cfile:
        return pickle.load(cfile)

```

Here is the snippet from `research/data/collection/jupyter_notebook/tokenize_and_pack.py`:

```
        ignore_error=True,
    )

    spark.stop()


if __name__ == "__main__":
    main()

```

Here is the snippet from `services/chat_host/server/chat_handler_test.py`:

```
        chat_history = [
            chat_pb2.Exchange(
                request_message="How you doing?", response_text="Fine thanks"
            )
        ]

    chat_position = chat_pb2.ChatPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    chat_request = chat_pb2.ChatRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_code=selected_code,
        chat_history=[
            chat_pb2.Exchange(
                request_message=item.request_message, response_text=item.response_text
            )
            for item in chat_history
        ],
        suffix=suffix,
        message=message,
        position=chat_position,
        lang=lang,
        sequence_id=0,
        blobs=blobs,
        enable_preference_collection=enable_preference_collection,

```

Here is the snippet from `clients/vscode/webviews/src/common/components/SimpleMonaco.svelte`:

```
<script lang="ts">
    import { onDestroy } from "svelte";
    import Monaco from "./Monaco.svelte";
    import hljs from "highlight.js";
    import { editor, languages, Uri } from "monaco-editor";

    export let text: string;
    export let lang: string | undefined = undefined;
    export let pathName: string | undefined = undefined;
    export let options: Partial<editor.IStandaloneEditorConstructionOptions> = {};
    export let editorInstance: editor.IStandaloneCodeEditor | undefined = undefined;

    const supportedLanguages: string[] = languages.getLanguages().map((lang) => lang.id);

    // We use HLJS to guess the language and highlight if we are not passed the language by the model
    $: composedLang =
        lang && supportedLanguages.includes(lang)
            ? lang
            : hljs.highlightAuto(text, supportedLanguages).language;

    // Keep the URI updated with the path name. If the URI ever changes, we need to re-create the model
    let model: editor.ITextModel | undefined;

```

Here is the snippet from `base/augment_client/client.py`:

```
        blobs: Optional[BlobsJson | dict] = None,
        chat_history: Optional[list[Exchange | dict]] = None,
    ) -> ChatResponse:
        """Chat with the model."""
        json = {
            "model": self.model_name,
            "selected_code": selected_code,
            "message": message,
            "prefix": prefix,
            "suffix": suffix,
            "path": path,
            "blob_names": blob_names,
        }
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if suffix_end is not None:
            json["suffix_end"] = suffix_end

        if blobs is None:
            # The vscode extension will create an empty Blobs field by default.
            # We replicate this behavior in the api-proxy client as an additional
            # validation test, to better catch possible regressions in the backend.
            json["blobs"] = asdict(

```

Here is the snippet from `research/notebooks/inspect_model.ipynb`:

```
  "vscode": {
   "interpreter": {
    "hash": "36cf16204b8548560b1c020c4e8fb5b57f0e4c58016f52f2d4be01e192833930"
   }
  }
 },
 "nbformat": 4,
 "nbformat_minor": 2
}

```

Here is the snippet from `experimental/dxy/misc/test_2023_07_12.py`:

```
    print(f"Model Name: {args.name}\n")
    generated_text = model.generate(prefix=prompt, temperature=0)
    print(colored(prompt, color="red"), end="")
    print(colored(generated_text, color="green"))

    import pdb

    pdb.set_trace()

    print("-")

```

Here is the snippet from `services/edit_host/server/edit_handler_test.py`:

```
    suffix: str = "(a, b)",
    instruction: str = "Rename bar to foo",
    prefix_begin: typing.Optional[int] = None,
    suffix_end: typing.Optional[int] = None,
    path: str = "src/foo.py",
    blob_name: str = "blob0",
    lang: str = "python",
    blobs: blob_names_pb2.Blobs = Blobs.from_fake_blob_names(
        ["blob1", "blob2"]
    ).to_proto(),
) -> edit_pb2.EditRequest:
    """Factory function for creating edit request."""

    if prefix_begin is None:
        prefix_begin = 0
    if suffix_end is None:
        suffix_end = len(prefix) + len(selected_text) + len(suffix) - 1

    edit_position = edit_pb2.EditPosition(
        prefix_begin=prefix_begin,
        suffix_end=suffix_end,
        blob_name=blob_name,
    )

    edit_request = edit_pb2.EditRequest(
        model_name="test_model",
        path=path,
        prefix=prefix,
        selected_text=selected_text,
        suffix=suffix,

```

The developer has file `/home/<USER>/augment/services/inference_host/server/continuous_batching/longest_overlap_lm.py` open and has selected part of the code.

Here is the full file:

```
"""Longest overlap language model used for speculative decoding."""

from typing import Sequence

import numpy as np

from services.inference_host.server.continuous_batching import speculation

Token = int


P_POWER = np.power(31, np.arange(50000))


def _get_hash(tokens: np.ndarray):
    return np.cumsum(tokens * P_POWER[: len(tokens)])


def _find_match_pos(
    searchable_tokens: np.ndarray,
    sublist: np.ndarray,
    searchable_token_hashes: np.ndarray,
) -> Sequence[int]:
    """Rabin-Karp algorithm algorithm for pattern matching."""
    if len(sublist) > len(searchable_tokens):
        return []

    current_hashes = (
        searchable_token_hashes[len(sublist) - 1 :]
        - np.concatenate(  # type: ignore
            [[0], searchable_token_hashes[: -len(sublist)]]  # type: ignore
        )
    )

    hash_sublist = np.sum(sublist * P_POWER[: len(sublist)])
    current_hash_sublist = (
        hash_sublist * P_POWER[: len(searchable_tokens) - len(sublist) + 1]
    )

    result = np.nonzero(current_hashes == current_hash_sublist)[0] + len(sublist) - 1
    return list(result)


class LongestOverlapLM(speculation.Model):
    """Predicts tokens based on a single training sequence.

    Idea originates from Yury Zemlyanskiy. See the "NGram LM accuracy vs confidence" slide:
    https://docs.google.com/presentation/d/1uoTACq-qusGAqeKI3JcioNCuoKOYXeiKWrEQFBzM_Zc/edit

    This model is tained on a single sequence of training tokens, which is typically the
    request to the inference server (which may include the prefix, suffix, and retrieval
    tokens). Then, given a potentially short prompt, which are the most recent tokens, this
    model searches for the longest overlap with the training sequence.

    In the "fit" call, we precompute the hashes of the training tokens. The hash function here
    is arbitrary and of poor quality, and optimized for speed. Collisions are possible and
    tolerared. The assumption is that this model is used for speculative decoding, and
    collisions are not a problem.
    """

    def __init__(
        self,
        max_prompt_length: int = 100,
        max_tokens_to_speculate: int | None = None,
    ):
        """Initialize the model.

        Args:
            max_prompt_length (int, optional): The maximum length of the prompt. Defaults to 100.
            max_tokens_to_speculate (int, optional): The maximum number of tokens to speculate.
        """
        self.fit([])  # initializes fields
[START SELECTED REGION]
...
[selected code goes here]
...
[END SELECTED REGION]

    def fit(self, tokens: Sequence[Token]):
        """Train the model on the given tokens."""
        self.tokens = np.array(tokens)
        self.searchable_tokens = self.tokens[:-1]
        self.hash_tokens = _get_hash(self.searchable_tokens)

    def predict_next_k_tokens(
        self, prompt: list[int], k: int, num_predictions: int
    ) -> list[tuple[Token]]:
        """Predict the next k tokens given the prompt.

        Args:
            prompt (list[int]): The prompt tokens.
            k (int): The number of tokens to predict. Predictions may be shorter.
            num_predictions (int): The number of predictions to return. May return fewer.

        Returns:
            list[tuple[Token]]: The list of predictions.
        """
        if num_predictions < 1:
            raise speculation.SpeculationException(
                f"num_predictions must be positive. Got {num_predictions}."
            )
        if k < 0:
            raise speculation.SpeculationException(f"k must be positive. Got {k}.")
        if len(prompt) == 0 or k == 0:
            return []

        if self.max_tokens_to_speculate is not None:
            k = min(self.max_tokens_to_speculate, k)

        prompt_array = np.array(
            prompt[-self.max_prompt_length :]
        )  # This avoids repeated conversions of the prompot tokens into ndarrays.

        # the overlap length is within interval [min_length; max_length)
        min_length = 0
        max_length = min(len(self.searchable_tokens), len(prompt_array) + 1)
        # binary search
        while max_length - min_length > 1:
            mid_length = int((min_length + max_length) / 2)
            target_suffix = prompt_array[-mid_length:]
            target_pos = _find_match_pos(
                self.searchable_tokens,
                target_suffix,
                searchable_token_hashes=self.hash_tokens,
            )
            if len(target_pos) == 0:
                max_length = mid_length
            else:
                min_length = mid_length

        if min_length == 0:
            return []

        predictions = []
        positions_set, predictions_set = set(), set()
        for prompt_length in reversed(range(1, min_length + 1)):
            target_suffix = prompt_array[-prompt_length:]
            target_positions = _find_match_pos(
                self.searchable_tokens,
                target_suffix,
                searchable_token_hashes=self.hash_tokens,
            )
            for target_position in reversed(target_positions):
                if target_position in positions_set:
                    continue
                current_prediction = tuple(
                    self.tokens[
                        target_position + 1 : min(
                            target_position + k + 1, len(self.tokens)
                        )
                    ]
                )
                assert len(current_prediction) >= 1
                if current_prediction in predictions_set:
                    continue
                predictions.append(current_prediction)
                positions_set.add(target_position)
                predictions_set.add(current_prediction)
                if len(predictions) >= num_predictions:
                    break
            if len(predictions) >= num_predictions:
                break
        return predictions

```

Here is the selected code:

```
        self.max_prompt_length = max_prompt_length
        if max_tokens_to_speculate is not None and max_tokens_to_speculate < 1:
            raise ValueError(
                f"max_tokens_to_speculate must be positive. Got {max_tokens_to_speculate}."
            )
        self.max_tokens_to_speculate = max_tokens_to_speculate
```

If developer asks to modify code then assume they mean to ONLY modify this selected code above.

When answering the developer's questions, please follow these guidelines:

- Be concise and to-the-point in your answers. Provide only the most relevant and actionable information.
- If the provided snippets are not enough to answer a question, politely ask the user to reformulate their question.<|eot_id|><|start_header_id|>user<|end_header_id|>

rewrite this in javascript<|eot_id|><|start_header_id|>assistant<|end_header_id|>
