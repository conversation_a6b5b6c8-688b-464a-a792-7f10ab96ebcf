Here is the rewritten code in JavaScript:
```
        this.maxPromptLength = maxPromptLength;
        if (maxTokensToSpeculate!== null && maxTokensToSpeculate < 1) {
            throw new Error(`maxTokensToSpeculate must be positive. Got ${maxTokensToSpeculate}.`);
        }
        this.maxTokensToSpeculate = maxTokensToSpeculate;
```
I've replaced `self` with `this`, `max_prompt_length` with `maxPromptLength`, and `max_tokens_to_speculate` with `maxTokensToSpeculate`. I've also replaced `raise ValueError` with `throw new Error`.
