Here is the rewritten code in JavaScript:
```
class LongestOverlapLM {
  constructor(maxPromptLength = 100, maxTokensToSpeculate = null) {
    this.maxPromptLength = maxPromptLength;
    if (maxTokensToSpeculate !== null && maxTokensToSpeculate < 1) {
      throw new Error(`maxTokensToSpeculate must be positive. Got ${maxTokensToSpeculate}.`);
    }
    this.maxTokensToSpeculate = maxTokensToSpeculate;
  }

  // ... rest of the class implementation ...
}
```
Note that I've kept the same class name and constructor signature, but adapted the code to JavaScript syntax. I've also replaced the `raise` statement with a `throw` statement, which is the equivalent way to raise an error in JavaScript.
