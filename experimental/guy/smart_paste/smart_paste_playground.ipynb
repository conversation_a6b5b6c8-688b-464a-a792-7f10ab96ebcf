{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import anthropic\n", "\n", "from pathlib import Path\n", "import re\n", "\n", "\n", "def get_file_components(prompt: str) -> tuple[str, str, str]:\n", "    pattern = r\"\"\"\n", "Here is the full file:\n", "\n", "```\n", "(.*?)\n", "\\[START SELECTED REGION\\]\n", "\\.\\.\\.\n", "\\[selected code goes here\\]\n", "\\.\\.\\.\n", "\\[END SELECTED REGION\\]\n", "(.*?)\n", "```\n", "\n", "Here is the selected code:\n", "\n", "```\n", "(.*?)```\n", "\"\"\"\n", "\n", "    match = re.search(pattern, prompt, re.DOTALL | re.MULTILINE)\n", "    assert match\n", "    prefix = match.group(1)\n", "    suffix = match.group(2)\n", "    selected = match.group(3)\n", "    return prefix, suffix, selected\n", "\n", "\n", "def deconstruct_llama_prompt(prompt: str) -> list[dict[str, str]]:\n", "    system_prompt_header = \"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\"\n", "    user_header = \"<|start_header_id|>user<|end_header_id|>\"\n", "    assistant_header = \"<|start_header_id|>assistant<|end_header_id|>\"\n", "\n", "    if prompt.endswith(\"\\n\"):\n", "        prompt = prompt[:-1]\n", "\n", "    assert prompt.endswith(assistant_header)\n", "    prompt = prompt[: -len(assistant_header)]\n", "\n", "    elems = prompt.split(\"<|eot_id|>\")\n", "    # assert len(elems) % 2 == 0\n", "\n", "    assert elems[0].startswith(system_prompt_header)\n", "    system_prompt = elems[0].replace(system_prompt_header, \"\")\n", "    dialog = [\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": system_prompt,\n", "        }\n", "    ]\n", "\n", "    for i, elem in enumerate(elems[1:]):\n", "        elem = elem.replace(user_header, \"\")\n", "        elem = elem.replace(assistant_header, \"\")\n", "        assert \"<|\" not in elem\n", "        if i % 2 == 0:\n", "            dialog.append({\"role\": \"user\", \"content\": elem})\n", "        else:\n", "            dialog.append({\"role\": \"assistant\", \"content\": elem})\n", "\n", "    return dialog\n", "\n", "\n", "def generate_pasteable_code_with_tools(\n", "    prompt: str, output: str, verbose: bool, print_response: bool\n", ") -> str:\n", "    dialog = deconstruct_llama_prompt(prompt)\n", "\n", "    assert dialog[0][\"role\"] == \"system\"\n", "    system_prompt = dialog[0][\"content\"]\n", "    dialog = dialog[1:]\n", "\n", "    assert dialog[-1][\"role\"] == \"assistant\"\n", "    dialog[-1][\"content\"] = output\n", "\n", "    region = \"us-east5\"\n", "    project_id = \"augment-387916\"\n", "    model = \"claude-3-5-sonnet@20240620\"\n", "    client = anthropic.AnthropicVertex(region=region, project_id=project_id)\n", "\n", "    prefix, suffix, selected = get_file_components(prompt)\n", "    # full_file = prefix + selected + suffix\n", "\n", "    prefix = \"\".join(prefix.splitlines(keepends=True)[-10:])\n", "    suffix = \"\".join(suffix.splitlines(keepends=True)[:10])\n", "\n", "    def get_lines(text: str, start_line_offset: int, prefix: str = \" \") -> list[str]:\n", "        lines = []\n", "        for i, line in enumerate(text.splitlines()):\n", "            lines.append(f\"{prefix}{i+1+start_line_offset:04d}: {line}\\n\")\n", "        return lines\n", "\n", "    all_lines = []\n", "    all_lines += get_lines(prefix, 0, prefix=\" \")\n", "    all_lines += get_lines(selected, len(all_lines), prefix=\"*\")\n", "    all_lines += get_lines(suffix, len(all_lines), prefix=\" \")\n", "\n", "    full_file_with_lines = \"\".join(all_lines)\n", "\n", "    # full_file_with_lines = \"\".join(f\"{i+1:04d}: {line}\" for i, line in enumerate(full_file.splitlines(keepends=True)))\n", "    # print(full_file_with_lines)\n", "\n", "    question = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"\"\"\\\n", "Can you please apply the changes to the selected code? Use the tool.\n", "\n", "Here is the text before editing, including line numbers. The selected code lines are marked with *.\n", "```\n", "{full_file_with_lines}\n", "```\n", "\"\"\",\n", "        }\n", "    ]\n", "\n", "    #     question = [\n", "    #         {\n", "    #             \"role\": \"user\",\n", "    #             \"content\": \"\"\"\\\n", "    # Can you please apply the changes to the code? Use the tool.\n", "    # \"\"\",\n", "    #         }\n", "    #     ]\n", "\n", "    print(question[0][\"content\"])\n", "\n", "    message = client.messages.create(\n", "        model=model,\n", "        max_tokens=8000,\n", "        system=system_prompt,\n", "        messages=dialog + question,\n", "        # tools=[\n", "        #     {\n", "        #         \"name\": \"replace_selected_code\",\n", "        #         \"description\": \"Replace selected code with new text\",\n", "        #         \"input_schema\": {\n", "        #             \"type\": \"object\",\n", "        #             \"properties\": {\n", "        #                 \"replacement_text\": {\n", "        #                     \"type\": \"string\",\n", "        #                     \"description\": \"The new text\"\n", "        #                 },\n", "        #             },\n", "        #             \"required\": [\"original_text\", \"replacement_text\"]\n", "        #         }\n", "        #     }\n", "        # ],\n", "        tools=[\n", "            {\n", "                \"name\": \"replace_text\",\n", "                \"description\": \"Replace text with new text\",\n", "                \"input_schema\": {\n", "                    \"type\": \"object\",\n", "                    \"properties\": {\n", "                        # \"original_text\": {\n", "                        #     \"type\": \"string\",\n", "                        #     \"description\": \"The old text\"\n", "                        # },\n", "                        \"start_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text starts, inclusive\",\n", "                        },\n", "                        \"end_line_number\": {\n", "                            \"type\": \"integer\",\n", "                            \"description\": \"The line number where the original text ends, inclusive\",\n", "                        },\n", "                        \"replacement_text\": {\n", "                            \"type\": \"string\",\n", "                            \"description\": \"The new text\",\n", "                        },\n", "                    },\n", "                    \"required\": [\n", "                        \"start_line_number\",\n", "                        \"end_line_number\",\n", "                        \"replacement_text\",\n", "                    ],\n", "                },\n", "            }\n", "        ],\n", "    )\n", "\n", "    # print(message.content[0].text)\n", "    # import termcolor\n", "    # print(termcolor.colored(selected, color=\"green\"))\n", "    # print(dialog[-2][\"content\"])\n", "\n", "    tool_message = message.content[1]\n", "    assert tool_message.type == \"tool_use\"\n", "    print(tool_message.name)\n", "\n", "    replacement_text = tool_message.input[\"replacement_text\"]\n", "\n", "    if verbose:\n", "        # print(\"Before:\")\n", "        # print(\"```\")\n", "        # print(tool_message.input[\"original_text\"])\n", "        # print(\"```\")\n", "\n", "        print(\"Lines:\")\n", "        print(f\"Start: {tool_message.input['start_line_number']}\")\n", "        print(f\"End: {tool_message.input['end_line_number']}\")\n", "\n", "        print(\"After:\")\n", "        print(\"```\")\n", "        print(replacement_text)\n", "        print(\"```\")\n", "\n", "        # print(\"Expected:\")\n", "        # print(expected)\n", "\n", "        # print(tool_message.original_text)\n", "        # print(tool_message.replacement_text)\n", "\n", "    return replacement_text"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Can you please apply the changes to the selected code? Use the tool.\n", "\n", "Here is the text before editing, including line numbers. The selected code lines are marked with *.\n", "```\n", " 0001:     def _format_exchange(self, exchange: Exchange):\n", " 0002:         return self._format_user_message(\n", " 0003:             exchange.request_message, True\n", " 0004:         ) + self._format_chat_message(exchange.response_text)\n", " 0005: \n", " 0006:     def format_prompt(\n", " 0007:         self,\n", " 0008:         prompt_input: ChatRetrieverPromptInput,\n", " 0009:     ) -> PromptFormatterOutput:\n", " 0010:         \"\"\"Returns tokenized prompt and metadata.\"\"\"\n", "*0011:         # How many tokens we allow in the prompt if we\n", "*0012:         # reserve 1 token for end_of_key.\n", "*0013:         # We will truncate the prompt at the very end to make\n", "*0014:         # sure we don't exceed this `adjusted_max_tokens` limit.\n", " 0015: \n", " 0016:         adjusted_max_tokens = (\n", " 0017:             self.apportionment_config.max_content_len\n", " 0018:             - 1  # reserve 1 token for end_of_key\n", " 0019:         )\n", " 0020:         # How many tokens we have left in the prompt\n", " 0021:         token_budget = adjusted_max_tokens - len(self.preamble)\n", " 0022: \n", " 0023:         # Start by adding the last user message\n", " 0024:         message = prompt_input.message\n", "\n", "```\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["replace_text\n", "Lines:\n", "Start: 11\n", "End: 14\n", "After:\n", "```\n", "        # Determine the maximum number of tokens allowed in the prompt,\n", "        # taking into account the reserved token for end_of_key.\n", "        # We will truncate the prompt at the end to ensure it does not exceed\n", "        # this `adjusted_max_tokens` limit.\n", "```\n", "ANSWER:\n", "        # Determine the maximum number of tokens allowed in the prompt,\n", "        # taking into account the reserved token for end_of_key.\n", "        # We will truncate the prompt at the end to ensure it does not exceed\n", "        # this `adjusted_max_tokens` limit.\n"]}], "source": ["directory = \"samples/2\"\n", "\n", "prompt = Path(f\"{directory}/prompt.txt\").read_text(encoding=\"utf8\")\n", "output = Path(f\"{directory}/output.txt\").read_text(encoding=\"utf8\")\n", "expected = Path(f\"{directory}/expected.txt\").read_text(encoding=\"utf8\")\n", "\n", "# print(prompt)\n", "answer = generate_pasteable_code_with_tools(\n", "    prompt, output, verbose=True, print_response=False\n", ")\n", "\n", "print(\"ANSWER:\")\n", "print(answer)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.llm_apis.completion_utils import TritonClient\n", "\n", "directory = \"samples/15\"\n", "\n", "prompt = Path(f\"{directory}/prompt.txt\").read_text(encoding=\"utf8\")\n", "output = Path(f\"{directory}/output.txt\").read_text(encoding=\"utf8\")\n", "\n", "address = \"*************:8000\"\n", "\n", "client = TritonClient(eod_id=2, address=address, timeout=180)\n", "\n", "\n", "def extract_selected_code(response):\n", "    return response.split(\"Here is the selected code:\\n\\n```\\n\")[1].split(\"```\")[0]\n", "\n", "\n", "def extract_code(response):\n", "    return response.split(\"```\\n\")[1].split(\"```\")[0]\n", "\n", "\n", "def get_response(prompt, print_response: bool = True):\n", "    response = \"\"\n", "    done = False\n", "\n", "    for response_piece in client.generate_stream(\n", "        prompt=prompt, max_generated_tokens=1500\n", "    ):\n", "        if \"assistant\" in response_piece:\n", "            print(f\"[STOPPING BECAUSE OF: {response_piece}]\")\n", "            done = True\n", "            response_piece = response_piece.split(\"assistant\")[0]\n", "\n", "        response += response_piece\n", "\n", "        if print_response:\n", "            print(response_piece, end=\"\")\n", "\n", "        if done:\n", "            break\n", "\n", "    return response\n", "\n", "\n", "# new_response = get_response(prompt)\n", "# print(new_response)\n", "\n", "# Solves the first question\n", "# question = \"Great, now rewrite the selected code to apply all these changes. Fit all new and modified code in your response. Do not repeat any code that already exists ourside the selected code.\"\n", "\n", "\n", "def generate_pasteable_code(\n", "    prompt: str, output: str, verbose: bool, print_response: bool\n", ") -> str:\n", "    selected_code = extract_selected_code(prompt)\n", "\n", "    question = f\"\"\"\\\n", "Here is the selected code:\n", "\n", "```\n", "{selected_code}\n", "```\n", "\n", "Rewrite the selected code to apply all these changes.\n", "Fit all new and modified code in your response.\n", "Do not repeat any code that already exists outside the selected code.\n", "Include all parts of the selected code that weren't changed.\n", "\"\"\"\n", "\n", "    if verbose:\n", "        print(\"\\nORIGINAL OUTPUT:\")\n", "        print(output)\n", "\n", "        print(\"\\nQUESTION:\")\n", "        print(question)\n", "\n", "    new_prompt = (\n", "        prompt\n", "        + output\n", "        + f\"<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n{question}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\"\n", "    )\n", "\n", "    print(\"\")\n", "    print(\"=\" * 100)\n", "\n", "    response2 = get_response(new_prompt, print_response)\n", "\n", "    return response2\n", "\n", "\n", "# generated_code = generate_pasteable_code(prompt, output, verbose=True, print_response=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Regression test"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~=~\n", "Sample:  12\n", "Can you please apply the changes to the selected code? Use the tool.\n", "\n", "Here is the text before editing, including line numbers. The selected code lines are marked with *.\n", "```\n", " 0001:             # Assume record is a dictionary with input data\n", " 0002:             input_data = record['input_data']\n", " 0003:             # Run the forward pass metrics\n", " 0004:             forward_metrics = system.forward_pass(input_data)\n", " 0005:             # Log the metrics\n", " 0006:             self.log_metrics(forward_metrics)\n", " 0007: \n", " 0008:         return {'metrics': self.metrics}\n", " 0009: \n", " 0010:     @classmethod\n", "*0011:     def from_yaml_config(cls, config: dict) -> \"ForwardPassEvalTask\":\n", "*0012:         jsonl_file_path = config[\"jsonl_file_path\"]\n", "*0013:         jsonl_file_path = config[\"jsonl_file_path\"]\n", "*0014:         return cls(jsonl_file_path)\n", "*0015: \n", "\n", "```\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["replace_text\n", "Lines:\n", "Start: 11\n", "End: 15\n", "After:\n", "```\n", "    def from_yaml_config(cls, config: dict, system) -> \"ForwardPassEvalTask\":\n", "        jsonl_file_path = config[\"jsonl_file_path\"]\n", "        return cls(jsonl_file_path)\n", "```\n", "SAMPLE 12 CORRECT!\n", "Accuracy: 1.0\n"]}], "source": ["root = Path(\"samples\")\n", "\n", "acc = 0\n", "# samples = [\"1\", \"2\", \"3\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\", \"13\", \"14\", \"15\"]\n", "samples = [\"12\"]\n", "# samples = [\"1\", \"2\", \"3\"]\n", "# samples = [\"11\"]\n", "\n", "for sample in samples:\n", "    print(\"\")\n", "    print(\"=~\" * 50)\n", "    print(\"Sample: \", sample)\n", "    directory = root / sample\n", "    prompt = Path(f\"{directory}/prompt.txt\").read_text(encoding=\"utf8\")\n", "    output = Path(f\"{directory}/output.txt\").read_text(encoding=\"utf8\")\n", "\n", "    all_expected = []\n", "    all_expected_code = []\n", "\n", "    for expected_path in Path(directory).glob(\"expected*.txt\"):\n", "        expected = expected_path.read_text(encoding=\"utf8\")\n", "        all_expected.append(expected)\n", "        all_expected_code.append(extract_code(expected))\n", "\n", "    # generated_response = generate_pasteable_code(prompt, output, verbose=True, print_response=False)\n", "    # generated_code = extract_code(generated_response)\n", "\n", "    generated_response = generate_pasteable_code_with_tools(\n", "        prompt, output, verbose=True, print_response=False\n", "    )\n", "    generated_code = generated_response\n", "\n", "    if generated_code[-1] != \"\\n\":\n", "        generated_code += \"\\n\"\n", "\n", "    any_match = any(\n", "        generated_code == expected_code for expected_code in all_expected_code\n", "    )\n", "\n", "    if any_match:\n", "        acc += 1\n", "        print(f\"SAMPLE {sample} CORRECT!\")\n", "    else:\n", "        print(f\"\\nSAMPLE {sample} INCORRECT:\\n\")\n", "        print(\"\\nFOR EXAMPLE EXPECTED[0]:\")\n", "        print(f\"<<<{all_expected_code[0]}>>>\")\n", "        print(\"ACTUAL:\")\n", "        print(f\"<<<{generated_code}>>>\")\n", "\n", "acc /= len(samples)\n", "print(f\"Accuracy: {acc}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}