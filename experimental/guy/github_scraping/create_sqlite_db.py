"""Create the sqlite database for keeping track of download state.

This script ports the data from the old scraping that relied on S3 for
state to the new.
"""
import argparse
import re
import sqlite3
from pathlib import Path


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--db",
        type=Path,
        default=Path("scrape_db.sqlite"),
        help="the sqlite database file that keeps track of the download state",
    )
    parser.add_argument(
        "--s3-dump",
        type=Path,
        default=Path("aws_s3_dump.txt"),
        help="a file that contains the result of 's3cmd s3://augment-github/bare-repos/'",
    )
    parser.add_argument(
        "--discarded_repos",
        type=Path,
        default=Path("discarded_bare_repos.txt"),
        help="list of repos that failed downloading",
    )
    args = parser.parse_args()

    conn = sqlite3.connect(args.db)
    cursor = conn.cursor()

    # Create table
    cursor.execute("DROP TABLE IF EXISTS visited_repos")

    cursor.execute(
        """
        CREATE TABLE IF NOT EXISTS visited_repos
        (id INTEGER PRIMARY KEY AUTOINCREMENT,
         repo_name TEXT,
         fork_name TEXT DEFAULT NULL,
         size INTEGER DEFAULT NULL,
         timestamp TEXT DEFAULT NULL,
         status TEXT CHECK (status IN ('download_complete', 'download_failed'))
         )"""
    )

    cursor.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_name ON visited_repos (repo_name)
    """
    )

    # Insert completed repos
    completed_repos = []

    with args.s3_dump.open("r") as file:
        for line in file:
            date, time, size, url = line.split()
            del date, time
            m = re.match(r"s3://augment-github/bare-repos/(.*)\.tar\.gz", url)
            if not m:
                raise ValueError("Couldn't parse URL: " + url)
            repo_name = m.group(1)
            completed_repos.append((repo_name, int(size)))

    cursor.executemany(
        """
        INSERT INTO visited_repos (repo_name, size, status)
        VALUES (?, ?, 'download_complete')
    """,
        completed_repos,
    )
    print(f"Inserted {len(completed_repos)} completed repos")

    # Insert failed repos
    failed_repos = []

    with args.discarded_repos.open("r") as file:
        for line in file:
            fork_name, repo_name = line.strip().split("/")
            failed_repos.append((repo_name, fork_name))

    cursor.executemany(
        """
        INSERT INTO visited_repos (repo_name, fork_name, status)
        VALUES (?, ?, 'download_failed')
    """,
        failed_repos,
    )
    print(f"Inserted {len(failed_repos)} failed repos")

    conn.commit()
    conn.close()


if __name__ == "__main__":
    main()
