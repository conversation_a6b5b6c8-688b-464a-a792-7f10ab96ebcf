"""Move scraped full repos into a subdir in S3."""

import boto3


def main():
    bucket_name = "augment-github"
    full_repos_path = "full-repos"
    s3 = boto3.resource("s3")
    bucket = s3.Bucket(bucket_name)

    for i, obj in enumerate(bucket.objects.all()):
        print(i, obj.key)
        if full_repos_path in obj.key:
            continue

        # copy then delete original (there is no move/rename command)
        s3.Object(bucket_name, f"{full_repos_path}/{obj.key}").copy_from(
            CopySource=f"{bucket_name}/{obj.key}"
        )
        s3.Object(bucket_name, obj.key).delete()


if __name__ == "__main__":
    main()
