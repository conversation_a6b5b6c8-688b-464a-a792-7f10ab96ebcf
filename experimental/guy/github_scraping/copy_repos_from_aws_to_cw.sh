#!/bin/bash

# Variables
src_bucket="s3://augment-github"
dst_bucket="s3://github-bare-repos"
host2_name="guy-dev"

# Create a temporary directory on host2
ssh ${host2_name} "mkdir -p /tmp/s3_sync"

# already transferred: run 's3cmd ls' on the target host and extract the filename
echo "Listing files that were already transferred..."
ssh ${host2_name} "/usr/bin/python3 /home/<USER>/.local/bin/s3cmd ls ${dst_bucket}/" | awk '{print $4}' | awk 'BEGIN {FS="/"} {print $4}' > already_transferred.txt

echo "Listing available files..."
aws s3 ls ${src_bucket}/bare-repos/ | awk '{print $4}' > available.txt

# Get list of files in the source bucket
echo "Getting list of remaining files..."
files=$(cat available.txt | grep -F -x -v -f already_transferred.txt)

# Copy each file from the source bucket to host2, then to the destination bucket, and delete it from host2
echo "Copying..."
for file in ${files}; do
    echo $file

    # Download the file from the source bucket to host1
    aws s3 cp "${src_bucket}/bare-repos/${file}" "/tmp/${file}"

    # Copy the file from host1 to host2
    scp "/tmp/${file}" "${host2_name}:/tmp/s3_sync/${file}"

    # Upload the file from host2 to the destination bucket
    ssh ${host2_name} "s3cmd put '/tmp/s3_sync/${file}' '${dst_bucket}/${file}'"

    # Delete the file from host2
    ssh ${host2_name} "rm -f '/tmp/s3_sync/${file}'"

    # Delete the file from host1
    rm -f "/tmp/${file}"

    echo ${file} >> completed_transfer_to_cw.txt
done

# Remove the temporary directory on host2
ssh ${host2_name} "rm -rf /tmp/s3_sync"
