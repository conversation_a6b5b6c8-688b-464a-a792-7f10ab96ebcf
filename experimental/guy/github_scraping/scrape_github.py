"""Scrape GitHub, downloading whole repositories.

State is maintained in a sqlite database. Repos are cloned with --bare to
save space.
"""

import argparse
import json
import multiprocessing
import os
import sqlite3
import subprocess
import tempfile
import time
import traceback
import urllib
from datetime import datetime, timezone
from pathlib import Path

import boto3
from tqdm import tqdm

DEFAULT_EXECUTE_TIMEOUT = 600

# some repos are very big.
# max S3 file size is 5GB, we leave a bit of buffer.
MAX_ARCHIVE_SIZE = 4e9
PREFERRED_LANGUAGES = ["C", "C++", "Go", "Java", "JavaScript", "Python"]


def execute(cmd, timeout=DEFAULT_EXECUTE_TIMEOUT, check=True):
    """Execute a shell command."""
    try:
        print(f"> {cmd}")
        subprocess.run(cmd, shell=True, timeout=timeout, check=check)
        return 0
    except subprocess.TimeoutExpired:
        return 1


def execute_with_retries(
    cmd, timeout=DEFAULT_EXECUTE_TIMEOUT, max_retries=3, secs_between_retries=10
):
    """Execute a shell command with retries."""
    attempts_left = max_retries + 1
    while attempts_left:
        try:
            execute(cmd, timeout=timeout, check=True)
            return 0
        except Exception:  # pylint: disable=broad-exception-caught
            attempts_left -= 1
            print(
                f"command, {attempts_left} attempts left "
                f"(sleeping for {secs_between_retries} before retrying)"
            )
            if attempts_left:
                time.sleep(secs_between_retries)
    return 1


def parse_repo_line(line):
    """Parse a line from the repositories file."""
    elems = line.strip().split(",")
    if len(elems) != 2:
        # print(
        #     "skipping line which doesn't have two elements "
        #     f"(probably missing lang): {line.strip()}"
        # )
        raise ValueError("missing lang")
    full_repo_name, lang = line.strip().split(",")
    fork_name, repo_name = full_repo_name.split("/")
    if len(repo_name) == 0:
        # print(f"Empty repository name, skipping: {full_repo_name}")
        raise ValueError("empty repo name")
    return repo_name, fork_name, full_repo_name, lang


def current_time_utc() -> str:
    """Current UTC time as a string."""
    return datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")


def process_repo(args, lock, repo_name, fork_name, full_repo_name, lang):
    """Clone, archive, and upload a repository to S3."""
    worker_id = multiprocessing.current_process().name
    print(f"[{worker_id}] processing repo: {repo_name}")

    del lang

    # --bare: clone bare repos, which means we get the full history but without
    # checking out HEAD (which just duplicates the info that's in the repo)
    #
    # --filter=blob:limit=10m: don't download blobs larger than this. filters
    # out large files and especially large text or binary files that people
    # check in to repos and are not useful to us.
    git_clone_flags = "--bare --filter=blob:limit=10m"

    print(f"[{worker_id}] connecting to bucket")
    s3 = boto3.resource(
        "s3",
        aws_access_key_id=args.access_key,
        aws_secret_access_key=args.secret_key,
        endpoint_url=args.endpoint_url,
    )
    bucket = s3.Bucket(args.bucket_name)

    def log_failed_repo(repo_name, fork_name):
        print(f"[{worker_id}] logging failed repo")
        with lock:
            print(f"[{worker_id}] acquired lock")
            db = sqlite3.connect(args.db)
            cursor = db.cursor()
            print(f"[{worker_id}] connected to db")
            cursor.execute(
                """
                INSERT INTO visited_repos (repo_name, fork_name, timestamp, status)
                VALUES (?, ?, ?, 'download_failed')
            """,
                (repo_name, fork_name, current_time_utc()),
            )
            db.commit()
            db.close()
            print(f"[{worker_id}] db connection closed")

    def log_completed_repo(repo_name, fork_name, size):
        print(f"[{worker_id}] logging completed repo")
        with lock:
            print(f"[{worker_id}] acquired lock")
            db = sqlite3.connect(args.db)
            cursor = db.cursor()
            print(f"[{worker_id}] connected to db")
            cursor.execute(
                """
                INSERT INTO visited_repos
                (repo_name, fork_name, size, timestamp, status)
                VALUES (?, ?, ?, ?, 'download_complete')
            """,
                (repo_name, fork_name, size, current_time_utc()),
            )
            db.commit()
            db.close()
            print(f"[{worker_id}] db connection closed")

    compressed_filename = f"{repo_name}.tar.gz"
    s3_key = f"{args.bucket_subdir}/{compressed_filename}"

    url = f"https://github.com/{full_repo_name}"

    # first check if the repo exists via HTTP.
    # if it doesn't, git clone will get stuck asking to authentication
    print(f"[{worker_id}] checking if url exists: {url}")
    try:
        conn = urllib.request.urlopen(url)
        conn.read()
    except Exception:  # pylint: disable=broad-exception-caught
        print(
            (
                f"[{worker_id}] cannot access repo {full_repo_name} at {url} , "
                "discarding"
            )
        )
        log_failed_repo(repo_name, fork_name)
        return

    # prev_dir = os.getcwd()
    with tempfile.TemporaryDirectory() as temp_dir:
        os.chdir(temp_dir)

        # clone the repo
        print(f"[{worker_id}] cloning repo {url}...")
        retcode = execute_with_retries(f"git clone {git_clone_flags} {url}")
        if retcode:
            print("failed to clone repo, skipping")
            return

        # save some simple metadata
        metadata = {
            "full_repo_name": full_repo_name,
            "repo_name": repo_name,
            "download_timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S %z"),
        }
        with Path("metadata.json").open("w", encoding="utf8") as metadata_file:
            json.dump(metadata, metadata_file)

        repo_dir = f"{repo_name}.git"

        try:
            # compress
            # (add ./ to deal with files that start with dash, which tar
            # interprets as options)
            print(f"[{worker_id}] archiving repo...")
            execute(f"tar czf ./{compressed_filename} ./{repo_dir} metadata.json")
            compressed_size = Path(compressed_filename).stat().st_size

            if compressed_size <= MAX_ARCHIVE_SIZE:
                # upload to S3
                print(f"[{worker_id}] uploading repo to S3..")
                with Path(compressed_filename).open("rb") as compressed_file:
                    bucket.put_object(Key=s3_key, Body=compressed_file)
            else:
                print(
                    (
                        f"[{worker_id}] archive {compressed_filename} is too "
                        "large, discarding"
                    )
                )
                log_failed_repo(repo_name, fork_name)

            log_completed_repo(repo_name, fork_name, compressed_size)
        except subprocess.CalledProcessError as exc:
            log_failed_repo(repo_name, fork_name)
            print(f"[{worker_id}] error occurred, skipping repository: {exc}")

        print("")
    # os.chdir(prev_dir)

    print(f"[{worker_id}] done processing repo {repo_name}")


def process_repo_wrapper(*process_repo_args):
    """Wraps process_repo and captures exceptions."""
    try:
        return process_repo(*process_repo_args)
    except Exception:
        traceback.print_exc()
        raise


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--db",
        type=Path,
        default=Path("scrape_db.sqlite"),
        help="the sqlite database file that keeps track of the download state",
    )
    parser.add_argument(
        "--repos",
        type=Path,
        default=Path("the_stack_repo_names_shuffled.txt"),
        help="repository list to download",
    )
    parser.add_argument(
        "--access-key",
        type=str,
        default=os.environ["AWS_ACCESS_KEY_ID"],
        help="S3 access key",
    )
    parser.add_argument(
        "--secret-key",
        type=str,
        default=os.environ["AWS_SECRET_ACCESS_KEY"],
        help="S3 secret key",
    )
    parser.add_argument(
        "--endpoint-url",
        type=str,
        default="https://object.lga1.coreweave.com",
        help="S3 endpoint URL",
    )
    parser.add_argument(
        "--bucket-name",
        type=str,
        default="augment-github",
        help="S3 bucket to write repos to",
    )
    parser.add_argument(
        "--bucket-subdir",
        type=str,
        default="bare-repos",
        help="subdir within the S3 bucket to write to",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=3,
        help="number of concurrent workers",
    )
    args = parser.parse_args()

    # Make the db path absolute. The worker processes chdir to temporary
    # directories, so they need the absolute path to access the database.
    args.db = Path.cwd() / args.db

    return args


def main():
    """Main."""
    args = parse_args()

    db = sqlite3.connect(args.db)
    cursor = db.cursor()

    def was_visited(repo_name):
        cursor.execute(
            """SELECT repo_name FROM visited_repos WHERE repo_name = ?""",
            (repo_name,),
        )
        row = cursor.fetchone()
        return row is not None

    manager = multiprocessing.Manager()
    lock = manager.Lock()
    task_list = []

    with args.repos.open("r") as repo_names_file:
        repo_names_lines = repo_names_file.readlines()
        for line in tqdm(repo_names_lines, "constructing repo list"):
            try:
                repo_name, fork_name, full_repo_name, lang = parse_repo_line(line)
            except ValueError:
                continue

            if lang not in PREFERRED_LANGUAGES:
                continue

            if was_visited(repo_name):
                continue

            task_list.append((args, lock, repo_name, fork_name, full_repo_name, lang))
            # process_repo(args, lock, repo_name, fork_name, full_repo_name, lang)

    print(f"Found {len(task_list)} repositories to download.")

    with multiprocessing.Pool(args.num_workers) as pool:
        pool.starmap(process_repo_wrapper, task_list)


if __name__ == "__main__":
    main()
