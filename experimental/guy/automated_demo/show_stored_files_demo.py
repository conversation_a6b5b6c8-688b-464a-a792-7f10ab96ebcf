"""Automatic version of the show_stored_files.py demo.

The goal of this task is to create a utility called show_stored_files.py,
which prints the files stored in the FileStore (the file storage class used
by the research server). It is run against a specific commit of the Augment
repo, ensuring a deterministic retrieval corpus.

The script acts as a client (simulating the extension) and can be executed
against both the production system and the research server.

This script will:
- clone the augment repo at the specified commit hash
- check that the server contains the expected files
- run completions repeatedly to generate the show_stored_files.py utility
- apply interventions as needed during the completions (simulating the user
  intervening to do things like insert whitespace here and there)
- compare the output to the expected output

Evaluation metrics:
- reached_eof: whether the model reached the end of the file before running out of completions
- outputs_match: whether the output matches the expected output
"""

import argparse
import logging
import subprocess
import sys
import tempfile
from pathlib import Path

from client import AugmentClient, AugmentModelClient, UploadContent

from base.blob_names.python.blob_names import get_blob_name

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        # logging.FileHandler('my_app.log'),
        logging.StreamHandler()
    ],
)

DEFAULT_TOKEN_FILE = Path("~/.config/augment/api_token").expanduser()


def get_api_token(args):
    if not args.token_file.exists():
        logging.error(
            "Must specify a token with --token-file or at %s", DEFAULT_TOKEN_FILE
        )
        sys.exit(1)
    token = args.token_file.read_text(encoding="utf-8").splitlines()[0].rstrip()
    return token


def checkout_repo(args, temp_dir):
    logging.info("checking out the repo: %s hash=%s" % (args.repo_url, args.repo_hash))
    subprocess.check_call(
        [
            "git",
            "clone",
            "-q",
            "--single-branch",
            "--branch",
            "main",
            args.repo_url,
            temp_dir,
        ]
    )
    subprocess.check_call(
        [
            "git",
            "-C",
            temp_dir,
            "checkout",
            "-q",
            args.repo_hash,
        ]
    )


def get_blob_name_to_path(repo_dir: str) -> dict:
    """Returns the map of blob-name to path from the local repo.

    This involves cloning the repo and checking out the target commit hash.
    """
    blob_name_to_path = {}

    # TODO(guy) this logic is hard-coded for the demo, will likely need to be improved.
    # most of this should be covered by using the .augmentignore file.
    for path in Path(repo_dir).rglob("*.py"):
        rel_path = path.relative_to(repo_dir)
        if "third_party" in str(rel_path):
            continue

        # TODO(guy) not sure why this file is not on the server. once we
        # upload missing files we can upload this file as well.
        if (
            str(rel_path)
            == "experimental/hieu/cutlass-3.2/tools/library/scripts/generator.py"
        ):
            continue

        contents = path.read_bytes()
        if len(contents) == 0:
            continue

        blob_name = get_blob_name(str(rel_path), contents)
        blob_name_to_path[blob_name] = str(rel_path)

    return blob_name_to_path


def upload_files(client: AugmentClient, root: str, relative_paths: list[str], blob_names: list[str]):
    """Uploads a file to the server."""
    to_upload: list[UploadContent] = []
    for path in relative_paths:
        content = Path(root, path).read_text(encoding="utf-8")
        to_upload.append(UploadContent(content=content, path_name=path))
    returned_blob_names = client.batch_upload(to_upload)
    assert blob_names == returned_blob_names


def upload_missing_files(args, client: AugmentClient, upload_chunk_size=100) -> dict:
    """Uploads missing files to the server.

    This involves cloning the repo and checking out the target commit hash.

    Returns the map of blob-name to path from the repo.
    """
    blob_name_to_path = {}
    with tempfile.TemporaryDirectory() as temp_dir:
        checkout_repo(args, temp_dir)
        blob_name_to_path = get_blob_name_to_path(temp_dir)
        missing_blob_names: list[str] = client.find_missing(list(blob_name_to_path.keys()))

        if missing_blob_names:
            logging.info(f"Uploading {len(missing_blob_names)} missing files")
            for chunk_idx in range(0, len(missing_blob_names), upload_chunk_size):
                missing_blob_names_chunk = missing_blob_names[chunk_idx : chunk_idx + upload_chunk_size]
                missing_paths_chunk = [blob_name_to_path[blob_name] for blob_name in missing_blob_names_chunk]
                upload_files(client, temp_dir, missing_paths_chunk, missing_blob_names_chunk)
        else:
            logging.info("No missing files")

    return blob_name_to_path


def compare_outputs(args, actual_output_path: Path, completion: str, metrics: dict):
    expected = args.expected_output_path.read_text(encoding="utf-8")
    metrics["outputs_match"] = completion == expected
    if not metrics["outputs_match"]:
        logging.error(
            "expected output does not match actual output, see %s. diff:"
            % args.expected_output_path
        )
        result = subprocess.run(
            [
                "diff",
                "-u",
                str(args.expected_output_path),
                str(actual_output_path),
            ],
            stdout=subprocess.PIPE,
            text=True,
            check=False,
        )
        logging.error("\n%s" % result.stdout)


def evaluate_model(args, client: AugmentClient, model: str, blob_name_to_path: dict):
    """Evaluates a model."""
    logging.info("============== Evaluating model: %s ===============", model)

    model_client = AugmentModelClient(client, model)
    metrics = {
        "reached_eof": False,
        "outputs_match": None,
    }

    completion = ""
    suffix = ""
    prefix_begin = 0

    for _ in range(args.max_completions):
        logging.info("sending completion request")
        response = model_client.complete(
            prompt=completion,
            suffix=suffix,
            path="research/model_server/show_stored_files.py",
            memory_object_names=list(blob_name_to_path.keys()),
            prefix_begin=prefix_begin,
            cursor_position=prefix_begin + len(completion),
            suffix_end=prefix_begin + len(completion) + len(suffix),
        )

        completion += response.text

        # this is needed to keep the demo on track
        if completion.endswith("import argparse"):
            logging.info("applying expected intervention: inserting whitespace")
            completion += "\n\n"

        if not response.text:
            logging.info("received empty response, generation complete")
            metrics["reached_eof"] = True
            break

    actual_output_path = Path(args.actual_output_path.replace("{model}", model))
    actual_output_path.write_text(completion, encoding="utf-8")
    logging.info(
        f"final generated text, also saved to {actual_output_path}:\n" + completion
    )

    compare_outputs(args, actual_output_path, completion, metrics)

    logging.info("evaluation metrics:")
    for metric in metrics:
        logging.info("\t%s: %s" % (metric, metrics[metric]))


def parse_command_line_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--endpoint",
        type=str,
        default="https://dogfood.api.augmentcode.com",
        help="Server endpoint",
    )
    parser.add_argument(
        "--token-file",
        type=Path,
        default=DEFAULT_TOKEN_FILE,
        help="Path to file containing token",
    )
    parser.add_argument(
        "--get_models",
        action="store_true",
        help="Show the available models and quit",
    )
    parser.add_argument(
        "--models",
        type=str,
        help="Which models to use, comma-separated, or 'all' to use all (required unless --get_models)",
    )

    # demo parameters
    parser.add_argument(
        "--repo_url",
        type=str,
        default="**************:augmentcode/augment.git",
        help="Repository URL to clone",
    )
    parser.add_argument(
        "--repo_hash",
        type=str,
        default="6d3f65c8dec052cd0da638224884f532c25f9b7f",  # pragma: allowlist secret
        help="Repository commit hash to use",
    )
    parser.add_argument(
        "--max_completions",
        type=int,
        default=20,
        help="Max number of completion requests",
    )
    parser.add_argument(
        "--expected_output_path",
        type=Path,
        default=Path("data/expected_output.txt"),
        help="Where to find the expected output",
    )
    parser.add_argument(
        "--actual_output_path",
        type=str,
        default="/tmp/actual_show_stored_files_output_{model}.txt",
        help="Where to save the actual output",
    )
    args = parser.parse_args()
    return args


def main():
    args = parse_command_line_args()

    if not args.expected_output_path.exists():
        logging.error(
            (
                "expected output file %s does not exist. "
                "if you are running in bazel, use --expected_output_path=... to "
                "point to the file in the bazel environment, by setting the path "
                "relative to the repository root (the bazel runfiles directory)."
                % args.expected_output_path
            )
        )
        sys.exit(1)

    token = get_api_token(args)
    client = AugmentClient(url=args.endpoint, token=token)

    if args.get_models:
        models = client.get_models()
        for model in models.models:
            print(model.name)
        sys.exit(0)

    if not args.models:
        logging.error("Must specify --models or --get_models")
        sys.exit(1)

    blob_name_to_path = upload_missing_files(args, client)

    if args.models == "all":
        models = [model.name for model in client.get_models().models]
    else:
        models = args.models.split(",")

    for model in models:
        evaluate_model(args, client, model, blob_name_to_path)


if __name__ == "__main__":
    main()
