# pylint: disable=protected-access
"""Client for the external augment API.

TODO(guy): Originally copied from services/api_proxy/client/client.py because it wasn't
available in the research. That code is now at base/augment_client/client.py, so this
file can be deleted.
"""
from __future__ import annotations

import logging
import time
import urllib.parse
import uuid
from dataclasses import asdict, dataclass
from typing import Any, Optional, Tuple

import requests


class ClientException(Exception):
    """Exception thrown if a request with the augment client failed."""

    def __init__(self, request_id: uuid.UUID, response: requests.Response):
        self.request_id = request_id
        self.response = response

    def is_client_error(self) -> bool:
        """Returns true if and only if the HTTP status code indicates a client error.

        Client error here refers to error where the client provided invalid arguments or other
        cases where the error can go away by making different requests.
        """
        return self.response.status_code >= 400 and self.response.status_code < 500


@dataclass
class CompletionItem:
    """One option provided to VSCode, containing text and associated data."""

    text: str
    skipped_suffix: str
    suffix_replacement_text: str


@dataclass
class CompleteResponse:
    """Class returned by a completion call."""

    text: str
    completion_items: list[CompletionItem]
    unknown_memory_names: list[str]
    request_id: uuid.UUID


@dataclass
class Language:
    """Class containing information about a programming language."""

    name: str
    vscode_name: str
    extensions: list[str]


@dataclass
class Model:
    """Class containing information about a currently available model."""

    name: str
    suggested_prefix_char_count: int
    suggested_suffix_char_count: int
    max_memorize_size_bytes: int


@dataclass
class GetModelsResponse:
    """Class returned by a get_model call."""

    default_model: Optional[str]
    models: list[Model]
    languages: list[Language]


@dataclass
class CompletionResolution:
    """Class that describes the resolution (acceptance or rejection) of a code completion."""

    request_id: str
    emit_time_sec: int
    emit_time_nsec: int
    resolve_time_sec: int
    resolve_time_nsec: int
    accepted_idx: int


@dataclass(frozen=True)
class UploadContent:
    """Class that describes the content to be memorized."""

    content: str
    path_name: str


@dataclass(frozen=True)
class Blobs:
    """Describes the blob names using a starting checkpoint id and delta."""

    checkpoint_id: Optional[str]
    added_blobs: list[str]
    deleted_blobs: list[str]


@dataclass(frozen=True)
class CheckpointBlobsRequest:
    """Class that describes the blob names to be checkpointed."""

    blobs: Blobs


@dataclass(frozen=True)
class CheckpointBlobsResponse:
    """Response to a checkpoint request with the new checkpoint id."""

    new_checkpoint_id: str


class AugmentClient:
    """Python client to issue requests against an Augment API endpoint."""

    def __init__(
        self,
        url: str,
        token: str,
        timeout: int = 60,
        retry_count: int = 2,
        retry_sleep: float = 0.1,
        session_id: Optional[uuid.UUID] = None,
        user_agent: Optional[str] = None,
    ):
        self.url = url
        self.token = token
        self.timeout = timeout
        self.retry_count = retry_count
        self.retry_sleep = retry_sleep
        self.request_session_id = session_id if session_id else uuid.uuid4()
        self.last_request_id: Optional[uuid.UUID] = None
        # see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/User-Agent
        self.user_agent = user_agent if user_agent else "api_proxy_client/0 (Python)"

    def __repr__(self):
        return f"AugmentClient(url={self.url}, request_session_id={self.request_session_id}, last_request_id={self.last_request_id})"

    def _post(
        self, url_suffix: str, json: Any, headers: Optional[dict[str, str]] = None
    ) -> Tuple[requests.Response, uuid.UUID]:
        """Post with build-in retry loop to hide small service outages."""
        url = urllib.parse.urljoin(self.url, url_suffix)
        response = None
        request_id = None

        for retry in range(self.retry_count + 1):
            try:
                request_id = uuid.uuid4()
                logging.debug(
                    "Posting to %s request id %s retry %d",
                    url_suffix,
                    request_id,
                    retry,
                )

                self.last_request_id = request_id
                request_headers = {
                    "authorization": f"Bearer {self.token}",
                    "x-request-id": str(request_id),
                    "x-request-session-id": str(self.request_session_id),
                    "user-agent": self.user_agent,
                }
                if headers:
                    request_headers.update(headers)
                response = requests.post(
                    url, json=json, headers=request_headers, timeout=60
                )
                if str(response.status_code).startswith("5"):
                    time.sleep(self.retry_sleep)
                    continue
                return (response, request_id)
            except requests.exceptions.SSLError:
                time.sleep(self.retry_sleep)
                continue
        # return the last response we got
        assert response is not None
        assert request_id is not None
        return (response, request_id)

    def memorize(self, content: str, path: str, blob_name: Optional[str] = None) -> str:
        """Memorizes the content and returns the matching object name."""
        data = {"model": "", "t": content, "path": path}
        if blob_name:
            data["blob_name"] = blob_name
        response, request_id = self._post("memorize", json=data)
        logging.debug("Memorize finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        assert "mem_object_name" in j
        return j["mem_object_name"]

    def batch_upload(self, blobs: list[UploadContent]) -> list[str]:
        """Uploads the contents and returns the matching blob names."""
        data = {
            "blobs": [
                {"path": blob.path_name, "content": blob.content} for blob in blobs
            ]
        }
        response, request_id = self._post("batch-upload", json=data)
        logging.debug("Upload finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        assert "blob_names" in j
        return j["blob_names"]

    def find_missing(self, memory_object_names: list[str]) -> list[str]:
        """Find missing memories."""
        response, request_id = self._post(
            "find-missing",
            json={"model": "", "mem_object_names": memory_object_names},
        )
        logging.debug("FindMissing finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["unknown_memory_names"]

    def get_models(self) -> GetModelsResponse:
        """Returns the list of all supported models.

        The models might not be ready at any given point, so calls
        might return 503.
        """
        response, request_id = self._post("get-models", json={})
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        models = [
            Model(
                name=i["name"],
                suggested_prefix_char_count=i["suggested_prefix_char_count"],
                suggested_suffix_char_count=i["suggested_suffix_char_count"],
                max_memorize_size_bytes=i["max_memorize_size_bytes"],
            )
            for i in j["models"]
        ]
        return GetModelsResponse(
            default_model=j["default_model"],
            models=models,
            languages=[
                Language(
                    name=lang["name"],
                    vscode_name=lang["vscode_name"],
                    extensions=lang["extensions"],
                )
                for lang in j["languages"]
            ],
        )

    def resolve_completions(
        self, client_name: str, resolutions: list[CompletionResolution]
    ) -> None:
        """Report on the resolution of the given list of completions."""
        resolutions_as_dicts = [asdict(resolution) for resolution in resolutions]
        response, request_id = self._post(
            "resolve-completions",
            json={"client_name": client_name, "resolutions": resolutions_as_dicts},
        )
        if not response.ok:
            raise ClientException(request_id, response)

    def checkpoint_blobs(self, blobs: Blobs) -> str:
        """Return the new checkpoint id for the given blobs."""
        response, request_id = self._post(
            "checkpoint-blobs", json={"blobs": asdict(blobs)}
        )
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        return j["new_checkpoint_id"]

    def client_for_model(self, model_name: str):
        """Returns the AugmentModelClient for a specific model."""
        return AugmentModelClient(self, model_name)


class AugmentModelClient:
    """Client to access the augment API for a specific model."""

    def __init__(self, augment_client: AugmentClient, model_name: str):
        self.augment_client_ = augment_client
        self.model_name = model_name

    def __repr__(self):
        return f"AugmentModelClient(url={self.augment_client_.url}, request_session_id={self.augment_client_.request_session_id}, last_request_id={self.augment_client_.last_request_id}, model={self.model_name})"

    def complete(
        self,
        prompt: str,
        path: Optional[str],
        memory_object_names: list[str],
        suffix: Optional[str] = None,
        top_k: Optional[int] = None,
        top_p: Optional[float] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        lang: Optional[str] = None,
        blob_name: Optional[str] = None,
        prefix_begin: Optional[int] = None,
        cursor_position: Optional[int] = None,
        suffix_end: Optional[int] = None,
        probe_only: Optional[bool] = False,
    ) -> CompleteResponse:
        """Runs a completion on the given prompt."""
        json = {
            "model": self.model_name,
            "prompt": prompt,
            "memories": memory_object_names,
        }
        if suffix:
            json["suffix"] = suffix
        if path:
            json["path"] = path
        if top_k:
            json["top_k"] = top_k
        if top_p:
            json["top_p"] = top_p
        if temperature:
            json["temperature"] = temperature
        if max_tokens:
            json["max_tokens"] = max_tokens
        if lang:
            json["lang"] = lang
        if blob_name:
            json["blob_name"] = blob_name
        if prefix_begin is not None:
            json["prefix_begin"] = prefix_begin
        if cursor_position is not None:
            json["cursor_position"] = cursor_position
        if suffix_end is not None:
            json["suffix_end"] = suffix_end
        if probe_only:
            json["probe_only"] = probe_only
        logging.debug("Request %s", json)
        response, request_id = self.augment_client_._post("completion", json=json)
        logging.debug("Completion finished: %s %s", request_id, response.status_code)
        if not response.ok:
            raise ClientException(request_id, response)
        j = response.json()
        text = j["text"]
        unknown_memory_names = j.get("unknown_memory_names")
        if unknown_memory_names is None:
            unknown_memory_names = []
        completion_items = [
            CompletionItem(**entry) for entry in j.get("completion_items") or []
        ]
        return CompleteResponse(
            text,
            completion_items=completion_items,
            unknown_memory_names=unknown_memory_names,
            request_id=request_id,
        )
