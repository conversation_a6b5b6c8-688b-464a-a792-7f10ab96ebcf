"""Show the files stored in the server's file store."""

import argparse

from research.model_server.file_store import FileStore


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--file_store_path",
        type=Path,
        default=Path("/tmp"),
        help="Path to the file store",
    )
    args = parser.parse_args()

    file_store = FileStore(args.file_store_path)
    for doc in file_store.get_files():
        print(doc)


if __name__ == "__main__":
    main()
