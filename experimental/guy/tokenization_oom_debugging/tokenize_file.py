"""Tokenize a file."""

import argparse
from pathlib import Path

from megatron.tokenizer import get_tokenizer


def main():
    """Main."""
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=str,
        default=None,
        help="input path",
    )
    parser.add_argument(
        "--output",
        type=str,
        default=None,
        help="output path",
    )
    args = parser.parse_args()

    tokenizer = get_tokenizer("CodeGenTokenizer")

    with Path(args.input).open("r", encoding="utf8") as input_file:
        text = input_file.read()
        tokens = tokenizer.tokenize(text)
        print(tokens)

    # with Path(args.output).open("wb") as output_file:
    #     output_file.write(tokens.encode("utf8"))


if __name__ == "__main__":
    main()
