"""Debugging a memory leak in tokenization.

- Loading a 1.2GB parquet file
- After loading the data, the process takes 11GB
- When tokenizing every row and throwing away the result, memory stays fixed at
  11GB (up to row 50k of 170k)
- doing this leads to growth in memory to >>20GB and beyond:
    df['tokenized_content'] = df['content'].apply(tokenizer.tokenize)


"""


import subprocess
import sys
from concurrent.futures import ProcessPoolExecutor
from pathlib import Path

import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from megatron.tokenizer import get_tokenizer
from stages.common import tokenize_and_pack

input_path = Path("/mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data")
output_path = Path(
    "/mnt/efs/augment-lga1-nvme/user/guy/data-pipeline/the-stack/tokenized_new"
)
num_workers = 5


def show_free():
    command = "free -h"
    result = subprocess.run(
        command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE
    )

    # Check if the command was successful
    if result.returncode == 0:
        print("Command output:")
        print(result.stdout.decode("utf-8"))  # decode to convert bytes to string
    else:
        print("Command failed. Error message:")
        print(result.stderr.decode("utf-8"))  # decode to convert bytes to string


def add_tokenized_column(path):
    print("Processing:", path)
    tokenizer = get_tokenizer("CodeGenTokenizer")

    def tokenize_content(content):
        # nonlocal tokenizer
        # idx = row["index"]

        # if idx % 1000 == 0:
        #     # print(f"[{idx}] Rebuilding tokenizer")
        #     print(f"[{idx}] Not rebuilding tokenizer")
        #     show_free()
        #     sys.stdout.flush()
        #     # tokenizer = get_tokenizer("CodeGenTokenizer")

        # content = row["content"]
        # print(f"[{idx}] Tokenizing {len(content)} characters: {content[:20]}")
        # sys.stdout.flush()

        # return tokenizer.tokenize(content)
        return tokenize_and_pack(tokenizer, content)

    table = pq.read_table(str(path))
    df = table.to_pandas()
    print(f"File {path} has {len(table)} rows")
    # df['tokenized_content'] = df['content'].apply(tokenizer.tokenize)
    df["tokenized_content"] = df["content"].apply(tokenize_content)
    # df['tokenized_content'] = df['content'].reset_index().apply(tokenize_content, axis=1)

    # for idx, row in df.iterrows():
    #     tokenizer.tokenize(row["content"])

    #     if idx % 1000 == 0:
    #         # print(f"[{idx}] Rebuilding tokenizer")
    #         print(f"[{idx}] Not rebuilding tokenizer")
    #         show_free()
    #         sys.stdout.flush()

    output_file = output_path / path.relative_to(input_path)
    print("Saving to:", output_file)
    sys.stdout.flush()
    output_file.parent.mkdir(parents=True, exist_ok=True)
    pq.write_table(df, str(output_file))


parquet_files = list(Path(input_path).rglob("*.parquet"))
# parquet_files = [Path("/mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data/langpart=text/data_0018.parquet")]
print(f"Processing {len(parquet_files)} files")

# for i, path in enumerate(parquet_files):
#     print(f"[{i+1}/{len(parquet_files)}] Processing {path}")
#     add_tokenized_column(path)

with ProcessPoolExecutor(max_workers=num_workers) as executor:
    executor.map(add_tokenized_column, parquet_files)

print("All done")
