"""Tokenize each language partition separately.

Trying to debug OOMs when tokenizing the whole dataset.
"""

import argparse
import logging
import sys
from functools import partial
from pathlib import Path

import pandas
import pyspark.sql.functions as F
from megatron.tokenizer import get_tokenizer
from pipeline import load_config, setup_spark, setup_tokenizer
from pyspark.sql.types import (
    ArrayType,
    BinaryType,
    IntegerType,
    StructField,
    StructType,
)
from stage_function_registry import get_stage_functions
from stages.common import SUCCESS_FILE, TOKEN_SIZE, tokenize_and_pack
from stages.the_stack import _tokenize_column

# def _tokenize_column(
#     spark, df, in_column, out_column, tokenizer, bytearray_packing=True
# ):
#     """Tokenize the given text column, adding it as a new column.

#     Args:
#         df: Spark DataFrame
#         in_column: Name of the text column to tokenize
#         out_column: Name of the binary tokens column to create
#         tokenizer: tokenizer to use
#         bytearray_packing: If True, pack tokens into bytearray. If False, store
#             them as a list of ints (takes up more space, easier to work with).

#     Returns:
#         Updated DataFrame that contains the new column
#     """
#     udf_tokenize = udf(
#         lambda text: _safe_tokenize(tokenizer, text),
#         ArrayType(IntegerType()),
#     )
#     return df.withColumn(out_column, udf_tokenize(in_column))


# def _safe_tokenize(tokenizer, text: str) -> list[int]:
#     all_tokens = []
#     segment_size = 1024
#     for idx in range(0, len(text), segment_size):
#         segment_tokens = tokenizer.tokenize(text[idx:idx + segment_size])
#         all_tokens.extend(segment_tokens)
#         break  # TODO

#     return all_tokens


def tokenize_column_with_pandas(iterator):
    # tokenizer = setup_tokenizer()
    for pdf in iterator:
        tokenizer = get_tokenizer("CodeGenTokenizer")
        # tokenize_fn = tokenizer.tokenize
        # tokenize_fn = partial(_safe_tokenize, tokenizer)
        tokenize_fn = partial(tokenize_and_pack, tokenizer)
        pdf["tokenized_content"] = pdf["content"].apply(tokenize_fn)
        yield pdf


def process_partition(spark_config, stage_config, args, partition_path):
    partition_name = partition_path.name

    print(f"================== Processing partition: {partition_name}")
    sys.stdout.flush()

    output_path = stage_config.output / partition_name
    if Path(output_path, SUCCESS_FILE).exists():
        print("Processing already complete, skipping")
        sys.stdout.flush()
        return

    # Setup and load
    # tokenizer = setup_tokenizer()
    spark = setup_spark(spark_config, args.py_file, verbose=False)

    # TODO do we need this?
    # spark.conf.set("spark.sql.parquet.filterPushdown", "false")

    df = spark.read.option("pathGlobFilter", "*.parquet").parquet(str(partition_path))
    # df = spark.read.option("pathGlobFilter", parquet_path.name).parquet(str(partition_path))

    # Basic filtering
    df = df.filter(
        (F.col("avg_line_length") <= 100)
        & (F.col("max_line_length") <= 1000)
        & (F.col("alphanum_fraction") >= 0.25)
        & (F.col("alphanum_fraction") < 0.9)
    )

    # Size filtering
    max_size = 1_000_000
    df = df.filter(F.col("size") < max_size)

    # Tokenize
    # df = _tokenize_column(spark, df, "content", "tokenized_content", tokenizer, bytearray_packing=stage_config.token_bytearray_packing)

    # Tokenize with pandas UDF
    # result_schema = StructType(df.schema.fields + [StructField("tokenized_content", ArrayType(IntegerType()), nullable=True)])
    result_schema = StructType(
        df.schema.fields
        + [StructField("tokenized_content", BinaryType(), nullable=True)]
    )
    # udf = F.pandas_udf(tokenize_column_with_pandas, result_schema)
    # df = df.mapInPandas(udf.func, schema=udf.returnType)
    # df = df.repartition(5000).mapInPandas(tokenize_column_with_pandas, result_schema)
    df = df.mapInPandas(tokenize_column_with_pandas, result_schema)

    # Save
    print("Saving to:", output_path)
    sys.stdout.flush()
    # TODO currently we're always overwriting
    df.write.mode("overwrite").parquet(str(output_path))
    print("Done saving")
    sys.stdout.flush()

    spark.stop()
    # del tokenizer


def main():
    logging.basicConfig(level=logging.INFO)
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config",
        type=Path,
        default="configs/tokenize_the_stack.yaml",
        help="the pipeline config yaml file",
    )
    parser.add_argument(
        "--py_file",
        action="append",
        help=".py or .zip file to add to pyspark (can provide multiple ones)",
    )
    args = parser.parse_args()
    config = load_config(args.config)
    # stage_functions = get_stage_functions(spark, tokenizer)

    def normalize_path(path):
        """Turn into Path and prepend data_processing_root."""
        return Path(config["global"].data_processing_root) / Path(path)

    stage_config = config.stages[0]
    assert stage_config.name == "filter_and_tokenize"
    stage_config.input = normalize_path(stage_config.input)
    stage_config.output = normalize_path(stage_config.output)
    print(stage_config.input)

    for partition_path in Path(stage_config.input).glob("langpart=*"):
        process_partition(config.spark, stage_config, args, partition_path)


if __name__ == "__main__":
    main()
