"""Count tokens in repositories.

Counts tokens using processed repositories, and per-file tokens using
unprocessed repos (jsonl files).
"""

import json
import os
import sys
from pathlib import Path

import megatron.data.indexed_dataset as indexed_dataset
from megatron.tokenizer.tokenizer import CodeGenTokenizer


def get_preprocessed_token_counts(path):
    """Read counts from pre-processed datasets (.bin and .idx files)."""
    # disable noisy output
    saved_stdout = sys.stdout
    sys.stdout = Path("/dev/null").open("w", encoding="utf8")
    dataset = indexed_dataset.make_dataset(path, "infer")
    sys.stdout = saved_stdout
    return list(dataset.sizes)


def show_per_repo_statistics():
    """Print per-repository token counts."""
    ROOT = "/mnt/efs/augment/data/processed/polycoder.v3/repos_splits_tokenized"
    print("path\ttokens")

    for root, _, files in os.walk(ROOT):
        for name in sorted(files):
            if not name.endswith(".bin"):
                continue
            path = os.path.join(root, name).replace(".bin", "")
            token_counts = get_preprocessed_token_counts(path)
            total_token_count = sum(token_counts)

            short_path = path.replace(ROOT + "/", "")
            print(f"{short_path}\t{total_token_count}")


def show_per_file_statistics():
    """Print per-file token counts."""
    # Tokenize jsonl files from scratch (very slow)
    ROOT = "/mnt/efs/augment/data/processed/polycoder.v3/repos_splits"

    tokenizer = CodeGenTokenizer()

    def get_token_counts(jsonl_path):
        token_counts = {}
        with Path(jsonl_path).open("r", encoding="utf8") as f:
            for line in f:
                data = json.loads(line)
                text = data["text"]
                filename = data["meta"]["file_name"]
                tokens = tokenizer.tokenize(text)
                token_counts[filename] = len(tokens)
        return token_counts

    print("repo\tfilename\ttokens")
    for root, _, files in os.walk(ROOT):
        for name in files:
            if not name.endswith("jsonl"):
                continue
            path = Path(root, name)
            token_counts = get_token_counts(path)
            for filename, tokens in token_counts.items():
                print(f"{path}\t{filename}\t{tokens}")


# show_per_repo_statistics()
show_per_file_statistics()
