#!/bin/bash
#
# Compile llama3 models for Triton. Should be run on the Triton pod.
# Models compiled on the TRT-LLM pod do not work with the Triton server.

set -x
set -e

TRITON_OUTPUT_ROOT=/mnt/efs/augment/user/guy/triton

for MODEL_NAME in Meta-Llama-3-70B-Instruct Meta-Llama-3-8B-Instruct
do
for QFORMAT in fp8 w4a8_awq int4_awq
do

time python3 prepare_llama_engine.py \
	--model_dir /mnt/efs/augment/checkpoints/llama3/hf/$MODEL_NAME \
	--context_length 8192 \
	--output_root $TRITON_OUTPUT_ROOT \
	--tensor_parallelism 1,2 \
	--qformat $QFORMAT \
	--skip_if_exists

done
done
