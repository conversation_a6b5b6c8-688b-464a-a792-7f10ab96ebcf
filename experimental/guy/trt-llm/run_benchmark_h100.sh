#!/bin/bash

set -e
set -x

OUTPUT_DIR=/mnt/efs/augment/user/guy/trt-llm/benchmark_results
LOG_DIR=logs/
 
mkdir -p $OUTPUT_DIR
mkdir -p $LOG_DIR

# 8B
MODEL=Meta-Llama-3-8B
MODEL_DIR=/mnt/efs/augment/checkpoints/llama3/hf/$MODEL

for QFORMAT in fp8 w4a8_awq
do
for TP in 1 2 4 8
do
	ENGINE_DIR=/mnt/efs/augment/user/guy/trt-llm/${MODEL}-${QFORMAT}-tp${TP}/engine

	time python3 benchmark_trt_llm.py \
		--model_dir $MODEL_DIR \
		--engine_dir $ENGINE_DIR \
		--tensor_parallelism $TP \
		--qformat $QFORMAT \
        --context_length 8192 \
		--num_input_tokens 128,256,512,1024,2048,4096,6144 \
		--num_generated_tokens 1 \
		--output ${OUTPUT_DIR}/output_${MODEL}_${QFORMAT}_tp${TP}_cl.jsonl |& tee ${LOG_DIR}/log_${MODEL}_${QFORMAT}_tp${TP}_cl.txt

	time python3 benchmark_trt_llm.py \
		--model_dir $MODEL_DIR \
		--engine_dir $ENGINE_DIR \
		--tensor_parallelism $TP \
		--qformat $QFORMAT \
        --context_length 8192 \
		--num_input_tokens 16 \
		--num_generated_tokens 64,128,256,512 \
		--output ${OUTPUT_DIR}/output_${MODEL}_${QFORMAT}_tp${TP}_ng.jsonl |& tee ${LOG_DIR}/log_${MODEL}_${QFORMAT}_tp${TP}_ng.txt
done
done

# 70B
MODEL=Meta-Llama-3-70B
MODEL_DIR=/mnt/efs/augment/checkpoints/llama3/hf/$MODEL

for QFORMAT in fp8 w4a8_awq
do
for TP in 1 2 4 8
do
	ENGINE_DIR=/mnt/efs/augment/user/guy/trt-llm/${MODEL}-${QFORMAT}-tp${TP}/engine

	time python3 benchmark_trt_llm.py \
		--model_dir $MODEL_DIR \
		--engine_dir $ENGINE_DIR \
		--tensor_parallelism $TP \
		--qformat $QFORMAT \
        --context_length 8192 \
		--num_input_tokens 128,256,512,1024,2048,4096,6144 \
		--num_generated_tokens 1 \
		--output ${OUTPUT_DIR}/output_${MODEL}_${QFORMAT}_tp${TP}_cl.jsonl |& tee ${LOG_DIR}/log_${MODEL}_${QFORMAT}_tp${TP}_cl.txt

	time python3 benchmark_trt_llm.py \
		--model_dir $MODEL_DIR \
		--engine_dir $ENGINE_DIR \
		--tensor_parallelism $TP \
		--qformat $QFORMAT \
        --context_length 8192 \
		--num_input_tokens 16 \
		--num_generated_tokens 64,128,256,512 \
		--output ${OUTPUT_DIR}/output_${MODEL}_${QFORMAT}_tp${TP}_ng.jsonl |& tee ${LOG_DIR}/log_${MODEL}_${QFORMAT}_tp${TP}_ng.txt
done
done
