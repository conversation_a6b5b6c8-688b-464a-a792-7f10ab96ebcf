#!/bin/bash

set -e
set -x

apt-get update
apt-get -y install python3.10 python3-pip openmpi-bin libopenmpi-dev git
apt-get -y install vim tmux git-lfs

pip3 install tensorrt_llm -U --pre --extra-index-url https://pypi.nvidia.com

python3 -c "import tensorrt_llm"

# Gives This
# python3 -c "import tensorrt_llm"
# [TensorRT-LLM] TensorRT-LLM version: 0.10.0.dev2024043000

cd ~
git clone https://github.com/NVIDIA/TensorRT-LLM.git
cd TensorRT-LLM
pip install -r examples/llama/requirements.txt
git lfs install

# Install AMMO for quantization
# Source: https://github.com/NVIDIA/TensorRT-LLM/blob/main/examples/quantization/README.md#preparation

pip install --no-cache-dir --extra-index-url https://pypi.nvidia.com nvidia-ammo==0.9.3

# Workaround for problem with <PERSON>th<PERSON> and pyyaml before installing requirements
# Source: https://stackoverflow.com/questions/77490435/attributeerror-cython-sources
pip install "cython<3.0.0" wheel
pip install "pyyaml==5.4.1" --no-build-isolation

pip install -r examples/quantization/requirements.txt
