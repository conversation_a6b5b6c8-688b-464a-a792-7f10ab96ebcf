#!/bin/bash
#
# Run this to prepare the necessary files for running a LLaMA 3 Triton server.
# Then run start-llama3-triton-server.sh to start the server.
#

set -x
set -e

# Tensor parallelism level
TP=1
#TP=2

# Quantization format
#QFORMAT=fp8
#QFORMAT=w4a8_awq
QFORMAT=int4_awq

# Model name
#MODEL_NAME=Meta-Llama-3-8B
#MODEL_NAME=Meta-Llama-3-8B-Instruct
#MODEL_NAME=Meta-Llama-3-70B
MODEL_NAME=Meta-Llama-3-70B-Instruct

#CONTEXT_LENGTH=8192
MAX_BATCH_SIZE=64

# Script starts here
#MODEL_VARIANT_NAME=${MODEL_NAME}-${QFORMAT}-tp${TP}-ctx${CONTEXT_LENGTH}
MODEL_VARIANT_NAME=${MODEL_NAME}-${QFORMAT}-tp${TP}

COMPILED_MODELS_ROOT=/mnt/efs/augment/user/guy/triton

MODEL_DIR=/mnt/efs/augment/checkpoints/llama3/hf/$MODEL_NAME
CHECKPOINT_DIR=$COMPILED_MODELS_ROOT/$MODEL_VARIANT_NAME/checkpoint
OUTPUT_DIR=$COMPILED_MODELS_ROOT/$MODEL_VARIANT_NAME/engine

[ -d "$MODEL_DIR" ] || exit 1
[ -d "$CHECKPOINT_DIR" ] || exit 1
[ -d "$OUTPUT_DIR" ] || exit 1

# === Prepare the servers ===

# Clone the backend repo
cd ~
REPO_DIR=tensorrtllm_backend
rm -rf $REPO_DIR
git clone --branch v0.9.0 https://github.com/triton-inference-server/tensorrtllm_backend.git $REPO_DIR
cd $REPO_DIR

# Copy the weights into the server directory
mkdir triton_model_repo
cp -r all_models/inflight_batcher_llm/* triton_model_repo/
cp $OUTPUT_DIR/* triton_model_repo/tensorrt_llm/1

# Prepare the model configuration by filling the template
python3 tools/fill_template.py -i triton_model_repo/preprocessing/config.pbtxt tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:${MAX_BATCH_SIZE},preprocessing_instance_count:1

python3 tools/fill_template.py -i triton_model_repo/postprocessing/config.pbtxt tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:${MAX_BATCH_SIZE},postprocessing_instance_count:1

python3 tools/fill_template.py -i triton_model_repo/tensorrt_llm_bls/config.pbtxt triton_max_batch_size:${MAX_BATCH_SIZE},bls_instance_count:1,accumulate_tokens:True,decoupled_mode:True

python3 tools/fill_template.py -i triton_model_repo/ensemble/config.pbtxt triton_max_batch_size:${MAX_BATCH_SIZE},decoupled_mode:True

python3 tools/fill_template.py -i triton_model_repo/tensorrt_llm/config.pbtxt triton_max_batch_size:${MAX_BATCH_SIZE},max_beam_width:1,engine_dir:${OUTPUT_DIR},exclude_input_in_output:True,enable_kv_cache_reuse:False,batching_strategy:inflight_fused_batching,max_queue_delay_microseconds:0,decoupled_mode:True,triton_backend:tensorrtllm

# === Start the servers ===
for n in 0 1 2 3 4 5 6 7; do

# Start the server
HTTP_PORT=800$n
GRPC_PORT=900$n
METRICS_PORT=1000$n

echo "Starting server $n..."
CUDA_VISIBLE_DEVICES=$n python3 ./scripts/launch_triton_server.py \
    --model_repo ./triton_model_repo \
    --world_size $TP \
    --http_port $HTTP_PORT \
    --grpc_port $GRPC_PORT \
    --metrics_port $METRICS_PORT \
    --log \
    --log-file log_${n}.txt &
done

wait
