{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "# addresses = [\n", "#     \"*************\",\n", "#     \"**************\",\n", "#     \"**************\",\n", "#     \"**************\",\n", "#     \"**************\",\n", "#     \"**************\",\n", "#     \"**************\",\n", "#     \"**************\"\n", "# ]\n", "\n", "# addresses = [\"*************\"]\n", "addresses = [\"*************\"]\n", "\n", "for address in addresses:\n", "    client = Llama3ChatClient(\"triton\", address=f\"{address}:8000\", timeout=10)\n", "    print(client.generate(messages=[\"hello\"], max_tokens=32))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Iterable\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "address = \"**************:8010\"\n", "client = Llama3ChatClient(\"triton\", address=address, timeout=60)\n", "\n", "question = \"Write a script that downloads a github repo, archives it, and uploads to S3.\"\n", "\n", "response = client.generate(messages=[question], max_tokens=12)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Code with dialog"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "import re\n", "import subprocess\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "import pathlib\n", "from tempfile import TemporaryDirectory\n", "from typing import Optional\n", "\n", "REPO_ROOT = \"/home/<USER>/augment_demo\"\n", "\n", "\n", "def execute_code(code: str, max_output_lines: int) -> str:\n", "    with TemporaryDirectory() as tmp_dir:\n", "        with (pathlib.Path(tmp_dir) / \"code.py\").open(\"w\") as f:\n", "            f.write(code)\n", "        result = subprocess.run(\n", "            f\"python3 {tmp_dir}/code.py\",\n", "            shell=True,\n", "            capture_output=True,\n", "            text=True,\n", "            cwd=REPO_ROOT,\n", "            # check=True,\n", "        )\n", "        output = result.stdout + result.stderr\n", "        truncated_output = \"\".join(output.splitlines(keepends=True)[:max_output_lines])\n", "        if len(output) > len(truncated_output):\n", "            truncated_output += \"...\\n\"\n", "        return truncated_output\n", "\n", "\n", "CODE_TEMPLATE = \"\"\"\\\n", "Please run this code for me:\n", "```\n", "(.*)\n", "```\n", "\"\"\"\n", "\n", "\n", "def contains_tool_use(message: str) -> bool:\n", "    return bool(re.search(CODE_TEMPLATE, message, re.DOTALL))\n", "\n", "\n", "def execute_dialog_style_tools(message: str, max_output_lines: int = 50) -> str:\n", "    match = re.search(CODE_TEMPLATE, message, re.DOTALL)\n", "    assert match\n", "\n", "    code = match.group(1)\n", "\n", "    print(f\"\\n[EXECUTING CODE:]\\n{code}\")\n", "    output = execute_code(code, max_output_lines)\n", "    return output\n", "    # return \"\"\n", "\n", "\n", "address = \"**************:8000\"\n", "client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "system_prompt_dialog_code = \"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. If needed, you can ask the\n", "user to run a python script on the codebase and report the results back to help you answer the question.\n", "Results may be truncated, so make sure to print the most important results first.\n", "\n", "In this project, every file has documentation in another file in the same dir called {FILENAME}.summary.md.\n", "Every directory has a summary of that directory in a file called directory.summary.md.\n", "You can access these files if it helps to answer the question.\n", "\n", "To identify code the user should run, wrap the code using this formatting:\n", "\n", "Please run this code for me:\n", "```\n", "...\n", "```\n", "\"\"\"\n", "\n", "system_prompt_dialog_sql = \"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. The codebase is stored in a SQL\n", "database with the following schema:\n", "\n", "```\n", "CREATE TABLE files (\n", "    path VARCHAR(255) PRIMARY KEY,\n", "    size INT,\n", "    language VARCHAR(255),\n", "    contents VARCHAR(1000000)\n", ");\n", "\n", "CREATE TABLE directories (\n", "    path VARCHAR(255) PRIMARY KEY,\n", "    recursive_size INT\n", ");\n", "```\n", "\n", "The `summary` fields contain natural-language summaries of the files and directories.\n", "\n", "If needed, you can ask the user to run a SQL query code database and report the\n", "results back to help you answer the question.  Results may be truncated, so make\n", "sure to print the most important results first.\n", "\n", "To identify code the user should run, wrap the code using this formatting:\n", "\n", "Please run this query for me:\n", "```\n", "...\n", "```\n", "\"\"\"\n", "\n", "# system_prompt = system_prompt_dialog_code\n", "system_prompt = system_prompt_dialog_sql\n", "\n", "# What are the most common languages in this project?\n", "# What are the top-level modules in this project?\n", "# What are the top languages used in research/ ?\n", "# Are there modules in this project that don't have much test coverage?\n", "\n", "# question = \"\"\"\\\n", "# what are the most common languages in this project?\n", "# \"\"\"\n", "\n", "# questions = [\n", "#     \"what are the most common languages in this project?\",\n", "#     \"I mean only programming languages\",\n", "# ]\n", "\n", "questions = [\n", "    \"what are the most common languages in this project?\",\n", "]\n", "\n", "# questions = [\n", "#     \"Are there any webhooks or API integrations in this project?\"\n", "# ]\n", "\n", "# questions = [\n", "# ]\n", "\n", "# questions = [\n", "#     \"what are the top-level modules in this project?\",\n", "#     \"what is the purpose of the base/ module?\",\n", "#     # \"what's in base/?\",\n", "#     # \"maybe the readme file says what's in this directory?\",\n", "# ]\n", "\n", "dialog = [questions.pop(0)]\n", "print(\"USER:\", dialog[0])\n", "\n", "while True:\n", "    response = client.generate(messages=dialog, max_tokens=1024, system_prompt=system_prompt)\n", "    dialog.append(response)\n", "\n", "    print(\"\\n[ASSISTANT:]\\n\" + response)\n", "\n", "    if contains_tool_use(response):\n", "        break\n", "        exec_output: str = execute_dialog_style_tools(response)\n", "        user_response = f\"Here is the output:\\n```\\n{exec_output}\\n```\"\n", "        dialog.append(user_response)\n", "        print(\"\\n[EXECUTION OUTPUT:]\\n\" + user_response)\n", "    else:\n", "        if questions:\n", "            next_question = questions.pop(0)\n", "            dialog.append(next_question)\n", "            print(\"\\nUSER:\", next_question)\n", "        else:\n", "            break\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Code runs inside the message"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "import re\n", "import subprocess\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "import pathlib\n", "from tempfile import TemporaryDirectory\n", "from typing import Optional\n", "\n", "TOOLS_PATTERN = r\"{{tool: (.*?)}}\"\n", "\n", "REPO_ROOT = \"/home/<USER>/augment_demo\"\n", "\n", "\n", "def contains_tool_use(message: str) -> str:\n", "    return re.search(TOOLS_PATTERN, message)\n", "\n", "@dataclass\n", "class ExecutionOutput:\n", "    command_output: str\n", "    updated_message: str\n", "\n", "def execute_command(command: str, max_output_lines: int) -> str:\n", "    result = subprocess.run(\n", "        command,\n", "        shell=True,\n", "        capture_output=True,\n", "        text=True,\n", "        cwd=REPO_ROOT,\n", "        check=True,\n", "    )\n", "\n", "    output = result.stdout\n", "    truncated_output = \"\".join(output.splitlines(keepends=True)[:max_output_lines])\n", "    if len(output) > len(truncated_output):\n", "        truncated_output += \"...\\n\"\n", "    return truncated_output\n", "\n", "\n", "def execute_code(code: str, max_output_lines: int) -> str:\n", "    with TemporaryDirectory() as tmp_dir:\n", "        with (pathlib.Path(tmp_dir) / \"code.py\").open(\"w\") as f:\n", "            f.write(code)\n", "        result = subprocess.run(\n", "            f\"python3 {tmp_dir}/code.py\",\n", "            shell=True,\n", "            capture_output=True,\n", "            text=True,\n", "            cwd=REPO_ROOT,\n", "            check=True,\n", "        )\n", "        output = result.stdout\n", "        truncated_output = \"\".join(output.splitlines(keepends=True)[:max_output_lines])\n", "        if len(output) > len(truncated_output):\n", "            truncated_output += \"...\\n\"\n", "        return truncated_output\n", "\n", "\n", "def execute_tools(message: str, max_output_lines: int = 50) -> ExecutionOutput:\n", "    # commands = re.findall(TOOLS_PATTERN, message)\n", "    # if not commands:\n", "    #     return message\n", "    # command = commands[0]\n", "\n", "    match = re.search(TOOLS_PATTERN, message)\n", "    if not match:\n", "        return ExecutionOutput(\"\", message)\n", "\n", "    # Truncate everything after the first tool use\n", "    message = message[:match.end()] + \"\\n\"\n", "    command = match.group(1)\n", "\n", "    # TODO(guy) ugh llama\n", "    # command = command.replace(\"find.\", \"find .\")\n", "    # command = command.replace(\"tokei.\", \"tokei .\")\n", "    # command = command.replace(\"tree.\", \"tree .\")\n", "    command = re.sub(r\"(\\w)\\.([^\\.])\", r\"\\1 .\\2\", command)\n", "\n", "    command = command.replace(\"tokei\", \"tokei -C -s lines\")\n", "\n", "    print(f\"[EXECUTING COMMAND: {command}]\")\n", "    output = execute_command(command, max_output_lines)\n", "    return ExecutionOutput(output, message)\n", "\n", "\n", "def execute_dialog_style_tools(message: str, max_output_lines: int = 50) -> Optional[str]:\n", "    code_template = \"\"\"\\\n", "Please run this code for me:\n", "```\n", "(.*)\n", "```\n", "\"\"\"\n", "    match = re.search(code_template, message)\n", "\n", "    if not match:\n", "        return None\n", "\n", "    code = match.group(1)\n", "\n", "    print(f\"[EXECUTING CODE:]\\n{code}\")\n", "    # output = execute_code(code, max_output_lines)\n", "    # return output\n", "    return \"\"\n", "\n", "\n", "address = \"**************:8000\"\n", "client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "system_prompt_cmd_line = r\"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. You have access\n", "to:\n", "- common command-line utilities including find, grep, ls, cat, and sed\n", "- tokei, a command-line tool that provides summary statistics about a codebase, including languages and lines of code.\n", "- summarize, a command-line tool that generates a natural language summary of a file or directory\n", "\n", "To use a tool, wrap the command around {{tool: ...}} like this:\n", "\n", "{{tool: find . -name '*.py' | xargs grep -l 'def main' | xargs sed -i 's/def main/def main_old/g'}}\n", "{{tool: tokei directory}}\n", "{{tool: cat directory/README*}}\n", "{{tool: summarize directory/}}\n", "{{tool: summarize file.py}}\n", "\"\"\"\n", "\n", "system_prompt_code = r\"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. The code files are available\n", "in the current directory, and you can write python code to inspect them. The results of that\n", "inspection can help you answer the user's question.\n", "\n", "To inspect files, write code that prints out the result you would like to inspect.\n", "Based on the result, answer appropriately.\n", "\n", "Example:\n", "User asks: what are the common languages in this project?\n", "\n", "Assistant can write code like this:\n", "\n", "```\n", "import os\n", "import collections\n", "\n", "def count_extensions(root_dir):\n", "    extension_counts = collections.defaultdict(int)\n", "    for root, dirs, files in os.walk(root_dir):\n", "        for file in files:\n", "            file_path = os.path.join(root, file)\n", "            file_ext = os.path.splitext(file_path)[1]\n", "            extension_counts[file_ext] += 1\n", "    return extension_counts\n", "\n", "def print_top_extensions(extension_counts, num_top=5):\n", "    sorted_extensions = sorted(extension_counts.items(), key=lambda x: x[1], reverse=True)\n", "    for extension, count in sorted_extensions[:num_top]:\n", "        print(f\"{extension}: {count}\")\n", "\n", "extension_counts = count_extensions(\".\")\n", "print_top_extensions(extension_counts)\n", "```\n", "\n", "Example:\n", "User asks: How are dependencies managed in the project\n", "\n", "You can write code like this:\n", "\n", "```\n", "import os\n", "import subprocess\n", "\n", "def find_dependency_management(root_dir):\n", "    # Check for presence of package managers like pip, npm, or yarn\n", "    package_managers = [\"pip\", \"npm\", \"yarn\"]\n", "    for manager in package_managers:\n", "        if os.path.exists(os.path.join(root_dir, f\"{manager}.lock\")):\n", "            print(f\"Found {manager} lockfile, indicating {manager} is used for dependency management\")\n", "            return\n", "\n", "    # Check for presence of requirements.txt file (common in Python projects)\n", "    if os.path.exists(os.path.join(root_dir, \"requirements.txt\")):\n", "        print(\"Found requirements.txt file, indicating pip is used for dependency management\")\n", "        return\n", "\n", "    # Check for presence of package.json file (common in Node.js projects)\n", "    if os.path.exists(os.path.join(root_dir, \"package.json\")):\n", "        print(\"Found package.json file, indicating npm or yarn is used for dependency management\")\n", "        return\n", "\n", "    # If none of the above, try to find other clues\n", "    for root, dirs, files in os.walk(root_dir):\n", "        for file in files:\n", "            if file.endswith(\".toml\") or file.endswith(\".xml\"):  # Check for Cargo.toml or Maven pom.xml files\n", "                print(f\"Found {file}, indicating {file.split('.')[0]} is used for dependency management\")\n", "                return\n", "\n", "    print(\"Unable to determine dependency management method\")\n", "\n", "# Call the function with the project root directory\n", "find_dependency_management(\".\")\n", "```\n", "\"\"\"\n", "\n", "system_prompt_dialog = \"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. If needed, you can ask the\n", "user to run a python script on the codebase and report the results back to help you answer the question.\n", "Results may be truncated, so make sure to print the most important results first.\n", "\n", "To identify code the user should run, wrap the code using this formatting:\n", "\n", "Please run this code for me:\n", "```\n", "...\n", "```\n", "\"\"\"\n", "\n", "system_prompt = system_prompt_dialog\n", "\n", "# What are the most common languages in this project?\n", "# What are the top-level modules in this project?\n", "# What are the top languages used in research/ ?\n", "# Are there modules in this project that don't have much test coverage?\n", "\n", "# question = \"\"\"\\\n", "# what are the most common languages in this project?\n", "# \"\"\"\n", "\n", "questions = [\n", "    \"what are the most common languages in this project?\",\n", "    \"I mean only programming languages\",\n", "]\n", "\n", "# questions = [\n", "#     \"Are there any webhooks or API integrations in this project?\"\n", "# ]\n", "\n", "# questions = [\n", "# ]\n", "\n", "# questions = [\n", "#     \"what are the top-level modules in this project?\",\n", "#     \"tell me about base/\",\n", "# ]\n", "\n", "dialog = [questions.pop(0)]\n", "print(\"USER:\", dialog[0])\n", "\n", "while True:\n", "    response = client.generate(messages=dialog, max_tokens=1024, system_prompt=system_prompt)\n", "    dialog.append(response)\n", "\n", "    if contains_tool_use(response):\n", "        exec_output = execute_tools(response)\n", "        dialog[-1] = exec_output.updated_message\n", "        print(\"\\nASSISTANT:\", dialog[-1])\n", "        dialog.append(f\"Command output:\\n{exec_output.command_output}\\n\")\n", "        print(\"[EXECUTE COMMAND]\")\n", "        # print(\"[Updated message:]\")\n", "        # print(dialog[-2])\n", "        print(\"[Tool output:]\")\n", "        print(dialog[-1])\n", "    else:\n", "        print(\"\\nASSISTANT:\", dialog[-1])\n", "        if questions:\n", "            next_question = questions.pop(0)\n", "            dialog.append(next_question)\n", "            print(\"\\nUSER:\", next_question)\n", "        else:\n", "            break\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [") -> Optional[str]:\n", "    code_template = \"\"\"\\\n", "Please run this code for me:\n", "```\n", "(.*)\n", "```\n", "\"\"\"\n", "    match = re.search(code_template, message)\n", "\n", "    if not match:\n", "        return None\n", "\n", "    code = match.group(1)\n", "\n", "    print(f\"[EXECUTING CODE:]\\n{code}\")\n", "    output = execute_command(code, max_output_lines)\n", "\n", "    \n", "\n", "address = \"**************:8000\"\n", "client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "system_prompt_cmd_line = r\"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. You have access\n", "to:\n", "- common command-line utilities including find, grep, ls, cat, and sed\n", "- tokei, a command-line tool that provides summary statistics about a codebase, including languages and lines of code.\n", "- summarize, a command-line tool that generates a natural language summary of a file or directory\n", "\n", "To use a tool, wrap the command around {{tool: ...}} like this:\n", "\n", "{{tool: find . -name '*.py' | xargs grep -l 'def main' | xargs sed -i 's/def main/def main_old/g'}}\n", "{{tool: tokei directory}}\n", "{{tool: cat directory/README*}}\n", "{{tool: summarize directory/}}\n", "{{tool: summarize file.py}}\n", "\"\"\"\n", "\n", "system_prompt_code = r\"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. The code files are available\n", "in the current directory, and you can write python code to inspect them. The results of that\n", "inspection can help you answer the user's question.\n", "\n", "To inspect files, write code that prints out the result you would like to inspect.\n", "Based on the result, answer appropriately.\n", "\n", "Example:\n", "User asks: what are the common languages in this project?\n", "\n", "Assistant can write code like this:\n", "\n", "```\n", "import os\n", "import collections\n", "\n", "def count_extensions(root_dir):\n", "    extension_counts = collections.defaultdict(int)\n", "    for root, dirs, files in os.walk(root_dir):\n", "        for file in files:\n", "            file_path = os.path.join(root, file)\n", "            file_ext = os.path.splitext(file_path)[1]\n", "            extension_counts[file_ext] += 1\n", "    return extension_counts\n", "\n", "def print_top_extensions(extension_counts, num_top=5):\n", "    sorted_extensions = sorted(extension_counts.items(), key=lambda x: x[1], reverse=True)\n", "    for extension, count in sorted_extensions[:num_top]:\n", "        print(f\"{extension}: {count}\")\n", "\n", "extension_counts = count_extensions(\".\")\n", "print_top_extensions(extension_counts)\n", "```\n", "\n", "Example:\n", "User asks: How are dependencies managed in the project\n", "\n", "You can write code like this:\n", "\n", "```\n", "import os\n", "import subprocess\n", "\n", "def find_dependency_management(root_dir):\n", "    # Check for presence of package managers like pip, npm, or yarn\n", "    package_managers = [\"pip\", \"npm\", \"yarn\"]\n", "    for manager in package_managers:\n", "        if os.path.exists(os.path.join(root_dir, f\"{manager}.lock\")):\n", "            print(f\"Found {manager} lockfile, indicating {manager} is used for dependency management\")\n", "            return\n", "\n", "    # Check for presence of requirements.txt file (common in Python projects)\n", "    if os.path.exists(os.path.join(root_dir, \"requirements.txt\")):\n", "        print(\"Found requirements.txt file, indicating pip is used for dependency management\")\n", "        return\n", "\n", "    # Check for presence of package.json file (common in Node.js projects)\n", "    if os.path.exists(os.path.join(root_dir, \"package.json\")):\n", "        print(\"Found package.json file, indicating npm or yarn is used for dependency management\")\n", "        return\n", "\n", "    # If none of the above, try to find other clues\n", "    for root, dirs, files in os.walk(root_dir):\n", "        for file in files:\n", "            if file.endswith(\".toml\") or file.endswith(\".xml\"):  # Check for Cargo.toml or Maven pom.xml files\n", "                print(f\"Found {file}, indicating {file.split('.')[0]} is used for dependency management\")\n", "                return\n", "\n", "    print(\"Unable to determine dependency management method\")\n", "\n", "# Call the function with the project root directory\n", "find_dependency_management(\".\")\n", "```\n", "\"\"\"\n", "\n", "system_prompt_dialog = \"\"\"\\\n", "You are a helpful coding assistant, operating within a codebase. If needed, you can ask the\n", "user to run a python script on the codebase and report the results back to help you answer the question.\n", "Results may be truncated, so make sure to print the most important results first.\n", "\n", "To identify code the user should run, wrap the code using this formatting:\n", "\n", "Please run this code for me:\n", "```\n", "...\n", "```\n", "\"\"\"\n", "\n", "system_prompt = system_prompt_dialog\n", "\n", "# What are the most common languages in this project?\n", "# What are the top-level modules in this project?\n", "# What are the top languages used in research/ ?\n", "# Are there modules in this project that don't have much test coverage?\n", "\n", "# question = \"\"\"\\\n", "# what are the most common languages in this project?\n", "# \"\"\"\n", "\n", "questions = [\n", "    \"what are the most common languages in this project?\",\n", "    \"I mean only programming languages\",\n", "]\n", "\n", "# questions = [\n", "#     \"Are there any webhooks or API integrations in this project?\"\n", "# ]\n", "\n", "# questions = [\n", "# ]\n", "\n", "# questions = [\n", "#     \"what are the top-level modules in this project?\",\n", "#     \"tell me about base/\",\n", "# ]\n", "\n", "dialog = [questions.pop(0)]\n", "print(\"USER:\", dialog[0])\n", "\n", "while True:\n", "    response = client.generate(messages=dialog, max_tokens=1024, system_prompt=system_prompt)\n", "    dialog.append(response)\n", "\n", "    print(response)\n", "    break\n", "\n", "    if contains_tool_use(response):\n", "        exec_output = execute_tools(response)\n", "        dialog[-1] = exec_output.updated_message\n", "        print(\"\\nASSISTANT:\", dialog[-1])\n", "        dialog.append(f\"Command output:\\n{exec_output.command_output}\\n\")\n", "        print(\"[EXECUTE COMMAND]\")\n", "        # print(\"[Updated message:]\")\n", "        # print(dialog[-2])\n", "        print(\"[Tool output:]\")\n", "        print(dialog[-1])\n", "    else:\n", "        print(\"\\nASSISTANT:\", dialog[-1])\n", "        if questions:\n", "            next_question = questions.pop(0)\n", "            dialog.append(next_question)\n", "            print(\"\\nUSER:\", next_question)\n", "        else:\n", "            break\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "from research.llm_apis.completion_utils import LlamaCppClient, TritonClient\n", "\n", "# addresses = load_addresses_from_yaml(\"/home/<USER>/augment/experimental/guy/pr_task_descriptions/triton_server_addresses.yaml\")\n", "\n", "address = \"*************:8000\"\n", "\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "prompt_path = Path(\"/home/<USER>/augment/experimental/guy/llama_next_edit/prompt.txt\")\n", "prompt = prompt_path.read_text(\"utf8\")\n", "\n", "selected_code = prompt.split(\"Here is the selected code:\\n```\\n\")[1].split(\"```\")[0]\n", "lines = selected_code.splitlines(keepends=True)\n", "# print(selected_code)\n", "\n", "selected_code_prefix = lines[0] + lines[1]\n", "selected_code_suffix = lines[-2] + lines[-1] + \"\\n\"\n", "\n", "modified_code_placeholder = \"... modified code ...\\n\"\n", "\n", "prompt = prompt.replace(modified_code_placeholder, selected_code_prefix + modified_code_placeholder + selected_code_suffix)\n", "\n", "# print(prompt)\n", "\n", "response = chat_client.generate(messages=[prompt], max_tokens=1024)\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "from research.llm_apis.completion_utils import LlamaCppClient, TritonClient\n", "\n", "# addresses = load_addresses_from_yaml(\"/home/<USER>/augment/experimental/guy/pr_task_descriptions/triton_server_addresses.yaml\")\n", "# addresses = [\"*************:8000\"]\n", "# address = addresses[0]\n", "\n", "address = \"**************:8000\"\n", "\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "client = chat_client.client\n", "\n", "# question = \"QUESTION\"\n", "# test_prompt = chat_client._prepare_prompt_text(messages=[question], system_prompt=\"SYSTEM_PROMPT\")\n", "# print(test_prompt)\n", "\n", "# prompt = \"\"\"\\\n", "# <|begin_of_text|><|start_header_id|>user<|end_header_id|>\n", "\n", "# \"\"\"\n", "\n", "system_prompt = \"\"\"\\\n", "You are a helpful assistant, designed to answer questions about software\n", "engineering. You can access tools and functions to answer those questions, invoked with <tool>\n", "\"\"\"\n", "\n", "prompt = f\"\"\"\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "Find websites that have recipes.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "Let's run a web search to see. \n", "\"\"\"\n", "\n", "response = client.generate(prompt=prompt, max_generated_tokens=64, temperature=0)\n", "print(response.content)\n", "\n", "# for _ in range(10):\n", "#     response = client.generate(prompt=prompt, max_generated_tokens=256, temperature=2)\n", "#     print(\"=\" * 80)\n", "#     print(response.content)\n", "\n", "# for address in addresses:\n", "#     chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "#     # tokens = chat_client.tokenizer.encode(prompt, bos=True, eos=True)\n", "#     # print(f\"Prompt token length: {len(tokens)}\")\n", "#     summary = chat_client.generate(messages=[prompt], max_tokens=512)\n", "#     print(f\"{address}: {summary}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for t in chat_client.tokenizer.special_tokens:\n", "    print(t)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "from research.models.remote_models import LLaMA3Instruct_Triton_Model\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "\n", "from experimental.guy.apis.process_chat_tasks import (\n", "    process_tasks,\n", "    TaskProcessorInput,\n", "    load_addresses_from_yaml,\n", ")\n", "\n", "path = Path(\"/home/<USER>/jvector/jvector-base/src/main/java/io/github/jbellis/jvector/util/ArrayUtil.java\")\n", "\n", "contents = path.read_text(\"utf8\")\n", "prompt = f\"\"\"\\\n", "Summarize the following code file in one paragraph. Describe the code's purpose\n", "and how it works.  Be sure to mention the most important parts of the code,\n", "including the most important functions, classes, and other elements.\n", "\n", "Path: {path}\n", "Contents:\n", "\n", "{contents}\n", "\"\"\"\n", "\n", "# addresses = load_addresses_from_yaml(\"/home/<USER>/augment/experimental/guy/pr_task_descriptions/triton_server_addresses.yaml\")\n", "\n", "addresses = [\"*************:8000\"]\n", "\n", "for address in addresses:\n", "    chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "    # tokens = chat_client.tokenizer.encode(prompt, bos=True, eos=True)\n", "    # print(f\"Prompt token length: {len(tokens)}\")\n", "    summary = chat_client.generate(messages=[prompt], max_tokens=512)\n", "    print(f\"{address}: {summary}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(contents)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "\n", "# address = \"*************:8010\"\n", "address = \"**************:8000\"\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "system_prompt = \"\"\"\\\n", "You are a helpful software engineering assistant, that can suggest the next edit\n", "in a code repository given previous edits that have been performed so far.\n", "\"\"\"\n", "\n", "\n", "retrieved = \"\"\"\\\n", "def load_examples(\n", "    jsonl_file: Path, fix_missing_annotation_chars: bool\n", ") -> list[EditDatum]:\n", "    examples = []\n", "    for line in jsonl_file.open(\"r\", encoding=\"utf-8\"):\n", "        example = EditDatum.from_json(line)  # type: ignore  pylint: disable=no-member\n", "        if fix_missing_annotation_chars:\n", "            _fix_missing_annotation_chars(example)\n", "        examples.append(example)\n", "    return examples\n", "\"\"\"\n", "\n", "\n", "# code_to_edit = \"\"\"\\\n", "# def export_to_csv(jsonl_file: Path, output_csv: Path, fix_missing_annotation_chars):\n", "#     headers = [\"Request ID\", \"Worker\", \"Instruction\", \"Resolution\", \"Annotated\"]\n", "\n", "#     csv_data = []\n", "#     examples = load_examples(jsonl_file, fix_missing_annotation_chars)  # type: ignore\n", "#     for example in sorted(examples, key=lambda x: x.request_id):\n", "#         csv_data.append(\n", "#             [\n", "#                 example.request_id,\n", "#                 example.user_id,\n", "#                 example.request.instruction,\n", "#                 example.status,\n", "#                 \"Yes\" if example.annotated_text is not None else \"No\",\n", "#             ]\n", "#         )\n", "\n", "#     with output_csv.open(\"w\", newline=\"\") as f:\n", "#         writer = csv.writer(f)\n", "#         writer.writerow(headers)\n", "#         writer.writerows(csv_data)\n", "# \"\"\"\n", "\n", "\n", "code_to_edit = \"\"\"\\\n", "    def _process_batch(self, batch: Iterable[_Row]) -> Iterable[EditDatum]:\n", "        request_pbs = [\n", "            ParseDict(row[\"request_json\"], request_insight_pb2.RIEditRequest())\n", "            for row in batch\n", "        ]\n", "\n", "        requests = [\n", "            self._build_request(request_pb, row[\"request_timestamp\"])\n", "            for row, request_pb in zip(batch, request_pbs)\n", "        ]\n", "        # Reconstruct after resolving, since we will remove the current\n", "        # blob_name from blob_names.\n", "        requests = self._resolve_checkpoints(requests, request_pbs)\n", "        if self._should_reconstruct_files:\n", "            requests = self._reconstruct_files(requests)\n", "\n", "        responses = [\n", "            self._build_response(\n", "                ParseDict(row[\"response_json\"], request_insight_pb2.RIEditResponse()),\n", "                request_pb,\n", "                row[\"response_timestamp\"],\n", "            )\n", "            for row, request_pb in zip(batch, request_pbs)\n", "        ]\n", "\n", "        resolutions = [\n", "            (\n", "                self._build_resolution(\n", "                    ParseDict(\n", "                        row[\"resolution_json\"], request_insight_pb2.EditResolution()\n", "                    ),\n", "                    row[\"resolution_timestamp\"],\n", "                )\n", "                if (\n", "                    row[\"resolution_json\"] is not None\n", "                    and row[\"resolution_timestamp\"] is not None\n", "                )\n", "                else None\n", "            )\n", "            for row in batch\n", "        ]\n", "\n", "        return [\n", "            EditDatum(\n", "                request_id=row[\"request_id\"],\n", "                user_id=row[\"user_id\"],\n", "                user_agent=row[\"user_agent\"],\n", "                request=request,\n", "                response=response,\n", "                resolution=resolution,\n", "            )\n", "            for row, request, response, resolution in zip(\n", "                batch, requests, responses, resolutions\n", "            )\n", "        ]\n", "\"\"\"\n", "\n", "\n", "prompt = f'''\\\n", "Here is a change the user made:\n", "\n", "```\n", "diff --git a/base/datasets/edit.py b/base/datasets/edit.py\n", "index 8ff7a8029..7d4dbcd51 100644\n", "--- a/base/datasets/edit.py\n", "+++ b/base/datasets/edit.py\n", "@@ -10,6 +10,18 @@ from dataclasses_json import dataclass_json\n", " logger = logging.getLogger(__name__)\n", " \n", " \n", "+@dataclass_json\n", "+@dataclass\n", "+class EditUserDetails:\n", "+    \"\"\"Details of the user making the request.\"\"\"\n", "+\n", "+    user_id: str\n", "+    \"\"\"The user ID of the user who made the edit.\"\"\"\n", "+\n", "+    user_agent: str\n", "+    \"\"\"The user ID of the user who made the edit.\"\"\"\n", "+\n", "+\n", " @dataclass_json\n", " @dataclass(frozen=True)\n", " class EditPosition:\n", "@@ -138,11 +150,8 @@ class EditDatum:\n", "     request_id: str\n", "     \"\"\"The request ID of the completion event.\"\"\"\n", " \n", "-    user_id: str\n", "-    \"\"\"The user ID of the user who made the edit.\"\"\"\n", "-\n", "-    user_agent: str\n", "-    \"\"\"The user ID of the user who made the edit.\"\"\"\n", "+    user_details: EditUserDetails\n", "+    \"\"\"Details of the user making the request.\"\"\"\n", " \n", "     request: EditRequest\n", "     \"\"\"The edit request.\"\"\"\n", "```\n", "\n", "Here is some relevant code to help you:\n", "\n", "```\n", "{retrieved}\n", "```\n", "\n", "Please suggest how to edit the following piece of code, to continue the developer's work.\n", "Output the result in the form of a diff between the old and new code, with a small number of context lines.\n", "\n", "```\n", "{code_to_edit}\n", "```\n", "'''\n", "\n", "response = chat_client.generate(messages=[prompt], system_prompt=system_prompt, max_tokens=2048)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["code = \"\"\"\\\n", "{\n", "    id: 'realistic_timing_test',\n", "    users: generateUsersFromSplits({\n", "      // '<EMAIL>': 1,\n", "      arunchaganty: 1,\n", "      aswin: 1,\n", "      // '<EMAIL>': 6,\n", "      // carl: 19,\n", "      // colin: 1,\n", "      '<EMAIL>': 1,\n", "      // dion: 7,\n", "      // dirk: 37,\n", "      // '<EMAIL>': 4,\n", "      // '<EMAIL>': 70,\n", "      guy: 1,\n", "      // '<EMAIL>': 2,\n", "      // '<EMAIL>': 2,\n", "      // igoros: 10,\n", "      // jacqueline: 23,\n", "      // '<EMAIL>': 28,\n", "      jiayi: 1,\n", "      joel: 1,\n", "      liam: 1,\n", "      '<EMAIL>': 1,\n", "      // '<EMAIL>': 16,\n", "      markus: 1,\n", "      // '<EMAIL>': 50,\n", "      mattgauntseo: 1,\n", "      // mb: 6,\n", "      // '<EMAIL>': 6,\n", "      // mlm: 11,\n", "      // moogi: 19,\n", "      // '<EMAIL>': 6,\n", "      // msdejong: 1,\n", "      // ran: 7,\n", "      // '<EMAIL>': 18,\n", "      rich: 1,\n", "      // 'sam.pullara': 3,\n", "      // vincent: 1,\n", "      // xiaolei: 2,\n", "    }),\n", "  }\n", "  \"\"\"\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "\n", "address = \"**************:8000\"\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "prompt_works = f\"\"\"\\\n", "Comment out the remaining users\n", "\n", "```\n", "{code}\n", "```\n", "\"\"\"\n", "\n", "prompt_doesnt_work = f\"\"\"\\\n", "Comment out remaining users\n", "\n", "```\n", "{code}\n", "```\n", "\"\"\"\n", "\n", "prompt = prompt_doesnt_work\n", "\n", "for i in range(10):\n", "  print(f\"\\n================= Response {i+1}:\")\n", "  response = chat_client.generate(messages=[prompt], max_tokens=512, temperature=0.8)\n", "  print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ipywidgets as widgets\n", "\n", "# Sample data\n", "elements = results\n", "\n", "# layout = widgets.Layout(width='auto', height='200px')\n", "\n", "full_width = \"75%\"\n", "\n", "# Create text widgets to display the current element's fields\n", "# diff_text = widgets.Textarea(value=\"\", placeholder=\"\", description=\"Diff:\", layout_width=\"90%\", layout_height=\"400px\", layout=layout)\n", "diff_text = widgets.Textarea(\n", "    value=\"\",\n", "    placeholder=\"\",\n", "    description=\"Diff:\",\n", "    layout=widgets.Layout(width=full_width, height=\"400px\"),\n", ")\n", "instruction_text = widgets.Text(\n", "    value=\"\",\n", "    placeholder=\"\",\n", "    description=\"Instruction:\",\n", "    layout=widgets.Layout(width=full_width),\n", ")\n", "description_text = widgets.Text(\n", "    value=\"\",\n", "    placeholder=\"\",\n", "    description=\"Description:\",\n", "    layout=widgets.Layout(width=full_width),\n", ")\n", "sample_text = widgets.Text(\n", "    value=\"\",\n", "    placeholder=\"\",\n", "    description=\"Sample:\",\n", "    layout=widgets.Layout(width=full_width),\n", ")\n", "\n", "# Create buttons for navigation\n", "prev_button = widgets.Button(description=\"Prev\", layout=widgets.Layout(width=\"auto\"))\n", "next_button = widgets.Button(description=\"Next\", layout=widgets.Layout(width=\"auto\"))\n", "\n", "# Create a layout to hold the widgets\n", "layout = widgets.VBox(\n", "    [\n", "        diff_text,\n", "        instruction_text,\n", "        description_text,\n", "        sample_text,\n", "        widgets.HBox([prev_button, next_button]),\n", "    ]\n", ")\n", "\n", "# Display the layout\n", "display(layout)\n", "\n", "# Initialize the current index\n", "current_index = 0\n", "\n", "\n", "# Define a function to update the text fields when the index changes\n", "def update_text_fields(index):\n", "    element = elements[index]\n", "    diff_text.value = element[\"diff\"]\n", "    instruction_text.value = element[\"instruction\"]\n", "    description_text.value = element[\"description\"]\n", "    sample_text.value = f\"{index+1} / {len(elements)}\"\n", "\n", "\n", "# Define a function to handle the prev button click\n", "def prev_button_clicked(b):\n", "    global current_index\n", "    current_index = max(0, current_index - 1)\n", "    update_text_fields(current_index)\n", "\n", "\n", "# Define a function to handle the next button click\n", "def next_button_clicked(b):\n", "    global current_index\n", "    current_index = min(len(elements) - 1, current_index + 1)\n", "    update_text_fields(current_index)\n", "\n", "\n", "# Link the button clicks to the functions\n", "prev_button.on_click(prev_button_clicked)\n", "next_button.on_click(next_button_clicked)\n", "\n", "# Initialize the text fields with the first element\n", "update_text_fields(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import configparser\n", "from tempfile import TemporaryDirectory\n", "import boto3\n", "import pickle\n", "import pandas as pd\n", "from pathlib import Path\n", "from dataclasses import dataclass\n", "from unidiff import PatchSet\n", "from typing import Optional\n", "import re\n", "\n", "from research.core.diff_utils import compute_single_file_diff, File\n", "from research.next_edits.edit_gen_sampler import EditGenProblem, EditGenOutput\n", "from research.utils.repo_change_utils import (\n", "    CommitInfo,\n", "    FileTuple,\n", "    RepoChange,\n", "    patchset_from_repo_change,\n", ")\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient, ChatClient\n", "\n", "address = \"**************:8000\"\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "\n", "config = configparser.ConfigParser()\n", "config.read(\"/home/<USER>/.s3cfg\")\n", "\n", "access_key = config.get(\"default\", \"access_key\")\n", "secret_key = config.get(\"default\", \"secret_key\")\n", "host_base = config.get(\"default\", \"host_base\")\n", "\n", "s3 = boto3.client(\n", "    \"s3\",\n", "    aws_access_key_id=access_key,\n", "    aws_secret_access_key=secret_key,\n", "    endpoint_url=f\"https://{host_base}\",\n", ")\n", "\n", "response = s3.list_objects(\n", "    Bucket=\"next-edit\", Prefix=\"stage1/100K_repos/S1.5_keep_most_1000p_2000f/\"\n", ")\n", "\n", "num_printed = 0\n", "\n", "\n", "def is_done():\n", "    return num_printed > 20\n", "\n", "\n", "# TODO(guy) make max_input_length configurable\n", "def generate_edit_output_description(\n", "    client: ChatClient,\n", "    past_to_wip_diff: PatchSet,\n", "    target_diff: PatchSet,\n", "    max_input_length: int = 8192,\n", ") -> Optional[str]:\n", "    system_prompt = r\"\"\"\\\n", "You are a helpful assistant who can correctly analyze code diffs and explain what they mean.\n", "You excel in correctness, clarity, and brevity.\n", "\n", "Here is an example of a diff and its description.\n", "\n", "```\n", "diff --git a/gunicorn.conf.py b/gunicorn.conf.py\n", "index d60419e..24172c5 100644\n", "--- a/gunicorn.conf.py\n", "+++ b/gunicorn.conf.py\n", "@@ -1,8 +1,8 @@\n", "-def setup_context(): # work around a bug in zmq, python dies if zmq.Context is \n", "+def setup_context(): # work around a bug in zmq, python dies if zmq.Context is called more than once\n", "     if 'zmq_context' not in __builtins__:\n", "         __builtins__['zmq_context'] = zmq.Context()\n", " \n", " requests = {}\n", " \n", " def pre_request(worker, req):\n", "     setup_context()\n", "```\n", "\n", "Description: Finish the comment.\n", "\n", "Here is another example:\n", "\n", "```\n", "diff --git a/gstats.py b/gstats.py\n", "index 83c2018..c94abe9 100644\n", "--- a/gstats.py\n", "+++ b/gstats.py\n", "@@ -104,15 +104,15 @@ class StatsCollector(Thread):\n", "             time_avg = time_total / float(finished_cnt)\n", " \n", "             ret[prefix] = {\n", "                 'started': data['started'].count,\n", "                 'finished': data['finished'].count,\n", "                 'processing_time': {\n", "                     'avg': time_avg,\n", "-                    'std': sqrt(sum(((t - time_avg) ** 2 for t in finished)) / (finished_cnt)\n", "+                    'std': sqrt(sum(((t - time_avg) ** 2 for t in finished)) / finished_cnt)\n", "                 }\n", "             }\n", " \n", "         return ret\n", " \n", "     def die(self, *args):\n", "         raise StopThread()\n", "```\n", "\n", "Description: Remove extra open parenthesis.\n", "\"\"\"\n", "\n", "#     prompt = f\"\"\"\\\n", "# Here is a diff showing a change done to a code repository.\n", "\n", "# ```\n", "# {past_to_wip_diff}\n", "# ```\n", "\n", "# And here is the diff showing the next change to be made.\n", "\n", "# ```\n", "# {target_diff}\n", "# ```\n", "\n", "# Please describe the diff of next change to be made in 1 brief sentence.\n", "\n", "# Follow these guidelines:\n", "# - Avoid using the words \"the change\" or \"the next change\".\n", "# - Use an active voice: say \"update ...\" instead of \"... being updated ...\" etc.\n", "# - Stick to describing the changed lines, not the unchanged context lines\n", "\n", "# Use this format:\n", "\n", "# Description: ... the description ...\n", "# \"\"\"\n", "\n", "    prompt = f\"\"\"\\\n", "Here is a diff showing a change done to a code file.\n", "\n", "```\n", "{target_diff}\n", "```\n", "\n", "Please describe the diff in 1 brief sentence.\n", "\n", "Follow these guidelines:\n", "- Avoid using the words \"the change\" or \"the next change\".\n", "- Use an active voice: say \"update ...\" instead of \"... being updated ...\" etc.\n", "\n", "Use this format:\n", "\n", "Description: ... the description ...\n", "\"\"\"\n", "\n", "    token_length = client.get_prompt_token_length(messages=[prompt])\n", "    if token_length > max_input_length:\n", "        return None\n", "\n", "    response = chat_client.generate(messages=[prompt], system_prompt=system_prompt, max_tokens=512)\n", "    match = re.search(r\"Description: (.*)\", response)\n", "    description = match.group(1) if match else None\n", "    return description\n", "\n", "\n", "results = []\n", "\n", "\n", "with TemporaryDirectory() as tmp_dir:\n", "    for obj in response[\"Contents\"]:\n", "        path = obj[\"Key\"]\n", "        print(path)\n", "        assert path.endswith(\".parquet\")\n", "\n", "        # Load the DataFrame from S3\n", "        obj = s3.get_object(\n", "            Bucket=\"next-edit\",\n", "            Key=path,\n", "        )\n", "        downloaded_path = Path(tmp_dir) / \"file.parquet\"\n", "        downloaded_path.write_bytes(obj[\"Body\"].read())\n", "        df = pd.read_parquet(str(downloaded_path))\n", "\n", "        # Iterate over the rows of the DataFrame\n", "        for index, row in df.iterrows():\n", "            repo_path = row[\"repo_path\"]\n", "            num_problems = row[\"num_problems\"]\n", "            pickled_results = row[\"pickled_results\"]\n", "\n", "            # Deserialize the EditGenProblem objects\n", "            edit_gen_problems = pickle.loads(pickled_results)\n", "\n", "            # Print out the EditGenProblem objects\n", "            for problem in edit_gen_problems:\n", "                if not problem.output.changed:\n", "                    continue\n", "\n", "                # print(\n", "                #     f\"Current Code: {problem.current_code[:100]}\"\n", "                # )  # truncate code for brevity\n", "                # print(f\"Instruction: {problem.instruction}\")\n", "\n", "                past_to_wip = patchset_from_repo_change(\n", "                    problem.repo_change, num_context_lines=5\n", "                )\n", "                # print(f\"\\nPast to WIP:\\n{past_to_wip}\")\n", "\n", "                code_before_change = problem.current_code\n", "                code_after_change = (\n", "                    problem.current_code[: problem.edit_region.start]\n", "                    + problem.output.replacement\n", "                    + problem.current_code[problem.edit_region.stop :]\n", "                )\n", "\n", "                file_before = File(str(problem.current_path), code_before_change)\n", "                file_after = File(str(problem.current_path), code_after_change)\n", "                problem_diff = compute_single_file_diff(file_before, file_after, num_context_lines=7)\n", "\n", "                print(f\"\\nDiff:\\n{problem_diff}\")\n", "\n", "                response = generate_edit_output_description(\n", "                    chat_client, past_to_wip, problem_diff\n", "                )\n", "                # print(f\"Generated description:\\n{response}\")\n", "                print(response)\n", "\n", "                results.append({\n", "                    \"instruction\": problem.instruction,\n", "                    \"past_to_wip\": str(past_to_wip),\n", "                    \"diff\": str(problem_diff),\n", "                    \"description\": response,\n", "                })\n", "\n", "                # print(f\"Output: {problem.output}\")\n", "                print(\"-\" * 120)\n", "                num_printed += 1\n", "\n", "                if is_done():\n", "                    break\n", "\n", "            if is_done():\n", "                break\n", "\n", "        downloaded_path.unlink()\n", "        if is_done():\n", "            break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from research.llm_apis.completion_utils import TritonClient\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "# address = \"************:8000\"\n", "address = \"**************:8000\"\n", "\n", "temperature = 1\n", "\n", "client = TritonClient(eod_id=2, address=address, timeout=180)\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "prompt = chat_client._prepare_prompt(messages=[\"question\", \"answer\"], system_prompt=\"system prompt\")\n", "\n", "# print(\"Prompt example:\")\n", "# print(prompt)\n", "# print(\"=\" * 80)\n", "\n", "system_prompt = \"\"\"\\\n", "You are a helpful chat bot, designed to answer questions about software engineering.\n", "If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.\n", "If you don't know the answer to a question, please don't share false information.\n", "\"\"\"\n", "\n", "prompt = f\"\"\"\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\"\"\"\n", "\n", "\n", "# for seed in range(2):\n", "#     extra_payload = {\n", "#         \"return_context_logits\": <PERSON>als<PERSON>,\n", "#         \"return_log_probs\": <PERSON><PERSON><PERSON>,\n", "#         \"return_generation_logits\": <PERSON>als<PERSON>,\n", "#         \"random_seed\": seed,\n", "#     }\n", "#     response = client.generate(prompt=prompt, max_generated_tokens=32, temperature=temperature, extra_payload=extra_payload)\n", "#     print(response.content)\n", "\n", "# print(json.dumps(response.full_response, indent=2))\n", "\n", "prompt = \"Provide examples of software engineering questions a developer might ask about their code. Include one or more code snippets, and a question or instruction about that code.\"\n", "\n", "for i in range(5):\n", "    response = chat_client.generate(messages=[prompt], max_tokens=32, temperature=temperature)\n", "    print(f\"\\n================= Response {i+1}:\")\n", "    print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.completion_utils import TritonClient\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "# address = \"************:8000\"\n", "address = \"**************:8000\"\n", "\n", "temperature = 9.0\n", "\n", "client = TritonClient(eod_id=2, address=address, timeout=180)\n", "chat_client = Llama3ChatClient(\"triton\", address=address, timeout=180)\n", "\n", "prompt = chat_client._prepare_prompt(messages=[\"question\", \"answer\"], system_prompt=\"system prompt\")\n", "\n", "# print(\"Prompt example:\")\n", "# print(prompt)\n", "# print(\"=\" * 80)\n", "\n", "system_prompt = \"\"\"\\\n", "You are a helpful chat bot, designed to answer questions about software engineering.\n", "If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.\n", "If you don't know the answer to a question, please don't share false information.\n", "\"\"\"\n", "\n", "prompt = f\"\"\"\\\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\"\"\"\n", "\n", "response = client.generate(prompt=prompt, max_generated_tokens=64, temperature=temperature)\n", "print(response.content)\n", "\n", "# prompt = \"Provide examples of software engineering questions a developer might ask about their code. Include one or more code snippets, and a question or instruction about that code.\"\n", "\n", "# response = chat_client.generate(messages=[prompt], max_tokens=256)\n", "# print(response)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}