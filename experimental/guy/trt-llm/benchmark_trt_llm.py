"""Benchmark TensorRT-LLM."""

import argparse
import json
from pathlib import Path
import os
import re
import subprocess
import sys

from transformers import AutoTokenizer


def find_gpu_type() -> tuple[str, str]:
    """Find the available GPU type.

    Returns the number of GPUs, GPU name, GPU memory.
    """
    cmd = "nvidia-smi --query-gpu=name,memory.total --format=csv,noheader"
    result = subprocess.run(cmd, shell=True, check=True, capture_output=True)
    lines = result.stdout.decode("utf-8").strip().split("\n")
    num_gpus = len(lines)
    line = lines[0].strip()
    gpu_name, gpu_memory = [s.strip() for s in line.split(",")]
    return num_gpus, gpu_name, gpu_memory


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model_dir",
        type=Path,
        default="/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-70B-Instruct",
        help="The directory containing the model.",
    )
    parser.add_argument(
        "--engine_dir",
        type=Path,
        required=True,
        help="The directory containing the built TRT-LLM engine.",
    )
    parser.add_argument(
        "--output",
        type=Path,
        required=True,
        help="Name of jsonl file to save the output.",
    )
    parser.add_argument(
        "--tensor_parallelism",
        type=int,
        required=True,
        help="The number of GPUs to use.",
    )
    parser.add_argument(
        "--qformat",
        type=str,
        required=True,
        help="Quantization format",
    )
    parser.add_argument(
        "--context_length",
        type=str,
        required=True,
        help="The maximum input length of the model (i.e. the model's context length).",
    )
    parser.add_argument(
        "--num_input_tokens",
        type=str,
        required=True,
        help="Number of tokens to put in the context. Comma-separated list.",
    )
    parser.add_argument(
        "--num_generated_tokens",
        type=str,
        required=True,
        help="Number of tokens to generate. Comma-separated list.",
    )
    parser.add_argument(
        "--num_extra_input_tokens",
        type=int,
        default=2,
        help="How many extra tokens are added to the input. Default value is for LLaMA 3.",
    )
    parser.add_argument(
        "--num_extra_output_tokens",
        type=int,
        default=1,
        help="How many extra tokens are added to the output. Default value is for LLaMA 3.",
    )
    args = parser.parse_args()

    num_gpus, gpu_name, gpu_mem = find_gpu_type()
    print(f"Num GPUs: {num_gpus}, GPU: {gpu_name}, Memory: {gpu_mem}")

    tokenizer = AutoTokenizer.from_pretrained(args.model_dir)

    tp = args.tensor_parallelism
    all_num_input_tokens = [int(cl) for cl in args.num_input_tokens.split(",")]
    num_gen = [int(ng) for ng in args.num_generated_tokens.split(",")]
    results = []

    if args.output.exists():
        with args.output.open("r", encoding="utf8") as file:
            for line in file:
                results.append(json.loads(line))

    ngt_tolerance = 2

    for num_input_tokens in all_num_input_tokens:
        for num_generated_tokens in num_gen:
            expected_ngt = num_generated_tokens + args.num_extra_output_tokens

            text = "hello " * (num_input_tokens - args.num_extra_input_tokens)
            tokens = tokenizer.encode(text)

            result_exists = False
            for result in results:
                if (
                    result["gpu"] == gpu_name
                    and result["gpu_mem"] == gpu_mem
                    and result["model_dir"] == str(args.model_dir)
                    and result["enginer_dir"] == str(args.engine_dir)
                    and result["tensor_parallelism"] == args.tensor_parallelism
                    and result["qformat"] == args.qformat
                    and result["num_input_tokens"] == len(tokens)
                    and abs(result["num_generated_tokens"] - expected_ngt)
                    <= ngt_tolerance
                ):
                    result_exists = True
            if result_exists:
                print("Result found, skipping")
                continue

            print(f"Tokenized: {tokens}")
            print(f"Token length: {len(tokens)}")
            assert (
                abs(len(tokens) - num_input_tokens) <= 1
            ), f"Aimed for {num_input_tokens} context tokens but got {len(tokens)}"

            run_result = subprocess.run(
                [
                    "mpirun",
                    "-n",
                    str(tp),
                    "--allow-run-as-root",
                    "python3",
                    f"{os.environ['HOME']}/TensorRT-LLM/examples/run.py",
                    "--tokenizer_dir",
                    str(args.model_dir),
                    "--engine_dir",
                    str(args.engine_dir),
                    "--input_text",
                    text,
                    "--max_input_length",
                    str(args.context_length),
                    "--max_output_len",
                    str(num_generated_tokens),
                    "--run_profiling",
                ],
                check=True,
                capture_output=True,
            )

            output = run_result.stdout.decode("utf8")

            m = re.search(
                r'Output \[Text 0 Beam 0\]: "([^"]*)"', output, re.DOTALL | re.MULTILINE
            )
            if m:
                generated_text = m.group(1)
            else:
                print(output)
                raise ValueError("Couldn't find output text")

            m = re.search(
                r"batch_size: 1, avg latency of 10 iterations: : ([\d\.]*) sec",
                output,
                re.DOTALL | re.MULTILINE,
            )
            if m:
                time_per_iteration = float(m.group(1))
            else:
                print(output)
                raise ValueError("Couldn't find timing")

            print("Full output:")
            print(output)
            print("\n\n")

            generated_tokens = tokenizer.encode(generated_text)
            print("generated text:", generated_text)
            print("tokens:", generated_tokens)
            print("token len:", len(generated_tokens))
            print("time per iteration:", time_per_iteration)

            assert (
                len(generated_tokens) > 0
            ), "Did not get any generated tokens, probably indicates a problem, check the logs"
            assert (
                abs(len(generated_tokens) - expected_ngt) <= ngt_tolerance
            ), f"Got output length {len(generated_tokens)}, expected {expected_ngt}"

            result = {
                "gpu": gpu_name,
                "gpu_mem": gpu_mem,
                "model_dir": str(args.model_dir),
                "enginer_dir": str(args.engine_dir),
                "tensor_parallelism": args.tensor_parallelism,
                "qformat": args.qformat,
                "num_input_tokens": len(tokens),
                "num_generated_tokens": len(generated_tokens),
                "seconds": time_per_iteration,
            }
            results.append(result)

            results_text = "\n".join([json.dumps(result) for result in results]) + "\n"
            args.output.write_text(results_text, encoding="utf8")


if __name__ == "__main__":
    main()
