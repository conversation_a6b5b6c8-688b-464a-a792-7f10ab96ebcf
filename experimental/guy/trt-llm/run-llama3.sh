#!/bin/bash

set -e
#set -x

TP=2
MODEL_NAME=Meta-Llama-3-70B-fp8-tp${TP}
TOKENIZER_DIR=/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-70B

CHECKPOINT_DIR=/mnt/efs/augment/user/guy/trt-llm/$MODEL_NAME/checkpoint
OUTPUT_DIR=/mnt/efs/augment/user/guy/trt-llm/$MODEL_NAME/engine

#INPUT_FILE=$HOME/augment/experimental/guy/trt-llm/hello_3k.txt
INPUT_FILE=$HOME/augment/experimental/guy/trt-llm/hello_short.txt

cd ~/TensorRT-LLM/

time mpirun -n $TP --allow-run-as-root python3 ./examples/run.py \
               --tokenizer_dir $TOKENIZER_DIR \
               --engine_dir $OUTPUT_DIR \
               --max_output_len 1 \
	       --max_input_len 7000 \
               --input_text "`cat $INPUT_FILE`" \
	       --run_profiling
