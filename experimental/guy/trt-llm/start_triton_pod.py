#!/usr/bin/env python3
#
# Create a 8xA100 or 8xH100 pod with the TritonRT-LLM image. The memory is required for checkpoint conversion from HF to TRT-LLM.
#
# For podspecs (where the memory number came from), see: https://github.com/augmentcode/augment/pull/6770/files

import argparse
import subprocess
import os
import sys

TRITON_SERVER_IMAGE = "nvcr.io/nvidia/tritonserver:24.04-trtllm-python-py3"

A100_MAX_SPECS = {
    "cpu_count": 96,
    "memory": 948,
}

H100_MAX_SPECS = {
    "cpu_count": 96,
    "memory": 1000,
}


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--pod_name", type=str, required=True, help="the name of the pod to create"
    )
    parser.add_argument("--gpu_type", type=str, required=True, help="A100 or H100")
    parser.add_argument(
        "--gpu_count", type=int, required=True, help="how many GPUs, between 1 and 8"
    )
    args = parser.parse_args()

    args.gpu_type = args.gpu_type.upper()

    if args.gpu_count < 1 or args.gpu_count > 8:
        raise ValueError(f"gpu_count must be between 1 and 8, got {args.gpu_count}")
    if args.gpu_type not in ["A100", "H100"]:
        raise ValueError(f"gpu_type must be A100 or H100, got {args.gpu_type}")

    # TODO(guy): remove specs, cpu_count and memory
    if args.gpu_type == "A100":
        pod_specs = A100_MAX_SPECS
    elif args.gpu_type == "H100":
        pod_specs = H100_MAX_SPECS
    else:
        raise ValueError(f"Unknown gpu_type: {args.gpu_type}")
    cpu_count = pod_specs["cpu_count"] * args.gpu_count // 8
    memory = pod_specs["memory"] * args.gpu_count // 8

    cmd = [
        "python3",
        f'{os.environ["HOME"]}/augment/deploy/dev/reserved_nodes/launch_pod.py',
        "--cluster",
        "CW",
        "create",
        "--gpu-type",
        args.gpu_type,
        "--gpu-count",
        str(args.gpu_count),
        # "--cpu-count",
        # str(cpu_count),
        # "--memory",
        # str(memory),
        "--custom-image",
        TRITON_SERVER_IMAGE,
        args.pod_name,
    ]

    print(" ".join(cmd))
    subprocess.run(cmd, check=True, stdout=sys.stdout, stderr=sys.stderr)


if __name__ == "__main__":
    main()
