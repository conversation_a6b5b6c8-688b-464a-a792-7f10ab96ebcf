{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "from dataclasses import asdict, dataclass\n", "import json\n", "import pandas\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "from pathlib import Path\n", "import os\n", "\n", "sns.set_style(\"whitegrid\")\n", "sns.set_theme(style=\"whitegrid\", palette=\"deep\")\n", "\n", "output_dir = Path(\"/mnt/efs/augment/user/guy/trt-llm/benchmark_results/\")\n", "\n", "\n", "def load_data(path: Path) -> pandas.DataFrame:\n", "    rows = []\n", "    with path.open(\"r\", encoding=\"utf-8\") as f:\n", "        rows.extend([json.loads(line) for line in f])\n", "    df = pandas.DataFrame(rows)\n", "    df[\"model\"] = df[\"model_dir\"].apply(lambda x: x.split(\"/\")[-1])\n", "    df[\"short_model\"] = df[\"model\"].apply(lambda x: x.replace(\"Meta-\", \"\"))\n", "    df[\"short_gpu\"] = df[\"gpu\"].apply(lambda x: x.replace(\"NVIDIA\", \"\").replace(\"80GB\", \"\").replace(\"HBM3\", \"\").replace(\"-SXM4-\", \"\").strip())\n", "    return df\n", "\n", "\n", "@dataclass(frozen=True)\n", "class ExperimentParams:\n", "    gpu: str\n", "    model: str\n", "    tensor_parallelism: int\n", "    qformat: str\n", "\n", "    @property\n", "    def label(self) -> str:\n", "        return f\"{self.gpu} {self.model} tp:{self.tensor_parallelism} q:{self.qformat}\"\n", "\n", "\n", "def get_experiment_params(df: pandas.DataFrame) -> ExperimentParams:\n", "    row = df.iloc[0]\n", "    return ExperimentParams(\n", "        gpu=row[\"short_gpu\"],\n", "        model=row[\"short_model\"],\n", "        tensor_parallelism=row[\"tensor_parallelism\"],\n", "        qformat=row[\"qformat\"],\n", "    )\n", "\n", "\n", "def get_slope(x, y):\n", "    return np.polyfit(x, y, 1)[0]\n", "\n", "\n", "plot_raw_data = False\n", "\n", "if plot_raw_data:\n", "    fig, axes = plt.subplots(nrows=1, ncols=2, figsize=(14, 6))\n", "    ng_ax = axes[0]\n", "    cl_ax = axes[1]\n", "\n", "ng_results = []\n", "cl_results = []\n", "combined_results: dict[ExperimentParams, dict] = defaultdict(dict)\n", "\n", "\n", "def get_msec_per_token(x, y):\n", "    return 1000 * get_slope(x, y)\n", "\n", "\n", "def get_results_dict(exp_params: ExperimentParams, x, y) -> dict:\n", "    msec_per_token = get_msec_per_token(x, y)\n", "    tokens_per_sec = 1000 / msec_per_token\n", "    msec_per_token_approx = 1000 * y.iloc[-1] / x.iloc[-1]\n", "    return {\n", "        **asdict(exp_params), \n", "        \"tokens_per_sec\": tokens_per_sec,\n", "        \"msec_per_token\": msec_per_token,\n", "        \"msec_per_token_approx\": msec_per_token_approx,\n", "        }\n", "\n", "\n", "for path in output_dir.glob(\"*.jsonl\"):\n", "    df = load_data(path)\n", "\n", "    exp_params = get_experiment_params(df)\n", "\n", "    if \"_ng\" in path.name:\n", "        x = df[\"num_generated_tokens\"]\n", "        y = df[\"seconds\"]\n", "        combined_results[exp_params][\"decoding_msec_per_token\"] = get_msec_per_token(x, y)\n", "        ng_results.append(get_results_dict(exp_params, x, y))\n", "        if plot_raw_data:\n", "            ng_ax.plot(x, y, \".-\", label=exp_params.label)\n", "    elif \"_cl\" in path.name:\n", "        x = df[\"num_input_tokens\"]\n", "        y = df[\"seconds\"]\n", "        combined_results[exp_params][\"context_msec_per_token\"] = get_msec_per_token(x, y)\n", "        cl_result = get_results_dict(exp_params, x, y)\n", "        cl_result[\"msec_per_2k_tokens\"] = round(2048 * cl_result[\"msec_per_token\"])\n", "        cl_result[\"msec_per_4k_tokens\"] = round(4096 * cl_result[\"msec_per_token\"])\n", "        cl_result[\"msec_per_8k_tokens\"] = round(8192 * cl_result[\"msec_per_token\"])\n", "        cl_result[\"msec_per_32k_tokens\"] = round(4 * 8192 * cl_result[\"msec_per_token\"])\n", "        cl_results.append(cl_result)\n", "\n", "        if plot_raw_data:\n", "            if exp_params.qformat == \"w4a8_awq\":\n", "                cl_ax.plot(x, y, \".-\", label=exp_params.label)\n", "    else:\n", "        raise ValueError(f\"Unknown experiment: {path}\")\n", "\n", "ng_df = pandas.DataFrame(ng_results)\n", "cl_df = pandas.DataFrame(cl_results)\n", "combined_df = pandas.DataFrame([\n", "    {**asdict(exp_params), **exp_params_results}\n", "    for exp_params, exp_params_results in combined_results.items()\n", "    ])\n", "\n", "if plot_raw_data:\n", "    ng_ax.grid()\n", "    ng_ax.set_xlabel(\"Num generated tokens\")\n", "    ng_ax.set_ylabel(\"Time (secs)\")\n", "    ng_ax.legend()\n", "\n", "    cl_ax.grid()\n", "    cl_ax.set_xlabel(\"Num input tokens\")\n", "    cl_ax.set_ylabel(\"Time (secs)\")\n", "    cl_ax.legend()\n", "\n", "    plt.show()\n", "\n", "# Save results\n", "cl_df.sort_values([\"model\", \"gpu\", \"qformat\", \"tensor_parallelism\"]).to_csv(os.environ[\"HOME\"] + \"/trt-llm-benchmark-results-cl.csv\")\n", "ng_df.sort_values([\"model\", \"gpu\", \"qformat\", \"tensor_parallelism\"]).to_csv(os.environ[\"HOME\"] + \"/trt-llm-benchmark-results-ng.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.ticker as ticker\n", "\n", "for model in [\"Llama-3-8B\", \"Llama-3-70B\"]:\n", "    fig, axes = plt.subplots(nrows=1, ncols=2, figsize=(14, 6))\n", "    ng_ax = axes[1]\n", "    cl_ax = axes[0]\n", "\n", "    # Decoding\n", "    model_ng_df = ng_df[ng_df[\"model\"] == model].sort_values([\"model\", \"gpu\"])\n", "\n", "    for name, group in model_ng_df.groupby([\"model\", \"gpu\", \"qformat\"]):\n", "        group = group.sort_values(\"tensor_parallelism\")\n", "        ng_ax.plot(group[\"tensor_parallelism\"], group[\"msec_per_token\"], \".-\", label=\" \".join(name[1:]))\n", "\n", "    ng_ax.grid(True)\n", "    ng_ax.set_xlabel(\"Tensor parallelism\")\n", "    ng_ax.set_ylabel(\"Time per generated token (msec)\")\n", "    ng_ax.set_title(f\"{model}: Decoding\")\n", "    ng_ax.legend()\n", "\n", "    if model == \"Llama-3-8B\":\n", "        ng_ax.yaxis.set_major_locator(ticker.MultipleLocator(0.5))\n", "    else:\n", "        ng_ax.yaxis.set_major_locator(ticker.MultipleLocator(2))\n", "\n", "    # Context processing\n", "    model_cl_df = cl_df[cl_df[\"model\"] == model].sort_values([\"model\", \"gpu\"])\n", "\n", "    for name, group in model_cl_df.groupby([\"model\", \"gpu\", \"qformat\"]):\n", "        group = group.sort_values(\"tensor_parallelism\")\n", "        cl_ax.plot(group[\"tensor_parallelism\"], 1024 * group[\"msec_per_token\"], \".-\", label=\" \".join(name[1:]))\n", "\n", "    cl_ax.grid(True)\n", "    cl_ax.set_xlabel(\"Tensor parallelism\")\n", "    cl_ax.set_ylabel(\"Time per 1024 processed tokens (msec)\")\n", "    cl_ax.set_title(f\"{model}: Context processing\")\n", "    cl_ax.legend()\n", "\n", "    if model == \"Llama-3-8B\":\n", "        cl_ax.yaxis.set_major_locator(ticker.MultipleLocator(5))\n", "    else:\n", "        cl_ax.yaxis.set_major_locator(ticker.MultipleLocator(25))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config = {\n", "    \"gpu\": \"H100\",\n", "    \"model\": \"Llama-3-70B\",\n", "    # \"tensor_parallelism\": 2,\n", "    \"qformat\": \"fp8\",\n", "}\n", "\n", "my_df = combined_df\n", "for k, v in config.items():\n", "    my_df = my_df[my_df[k] == v]\n", "\n", "\n", "my_df[\"msec_roundsize_8_approx\"] = my_df[\"decoding_msec_per_token\"]\n", "my_df[\"msec_roundsize_512_approx\"] = my_df[\"context_msec_per_token\"] * 512\n", "\n", "my_df.sort_values([\"tensor_parallelism\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}