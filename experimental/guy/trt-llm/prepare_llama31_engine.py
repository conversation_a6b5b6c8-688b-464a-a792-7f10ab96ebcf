"""Prepare TensorRT-LLM engines for LLAMA 3.1."""

import argparse
import os
from pathlib import Path
import subprocess
import sys


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model_dir",
        type=str,
        required=True,
        help="comma-separated list of model directories, for example: /mnt/efs/augment/checkpoints/llama3.1/hf/Meta-Llama-3.1-70B-Instruct",
    )
    parser.add_argument(
        "--context_length",
        type=int,
        required=True,
        help="model context length",
    )
    parser.add_argument(
        "--output_root",
        type=Path,
        default="/mnt/efs/augment/user/guy/trt-llm",
    )
    parser.add_argument(
        "--skip_if_exists",
        action="store_true",
        help="skip if the engine already exists",
    )
    parser.add_argument(
        "--tensor_parallelism",
        "--tp",
        type=str,
        required=True,
        help="comma-separated list of tensor parallelism values",
    )
    parser.add_argument(
        "--qformat",
        type=str,
        required=True,
        help="quantization format: fp8, int4_awq, w4a8_awq, full_prec",
    )

    args = parser.parse_args()

    model_dirs = [Path(d) for d in args.model_dir.split(",")]
    tps = [int(tp) for tp in args.tensor_parallelism.split(",")]

    print("model_dirs:", model_dirs)
    print("tps:", tps)

    if args.qformat == "fp8" or args.qformat == "w4a8_awq":
        quantization_flags = ["--calib_size", "512", "--kv_cache_dtype", "fp8"]
    elif args.qformat == "int4_awq":
        quantization_flags = [
            "--awq_block_size",
            "128",
            "--calib_size",
            "32",
            "--kv_cache_dtype",
            "int8",
        ]
    else:
        quantization_flags = []

    for model_dir in model_dirs:
        for tp in tps:
            model = model_dir.name
            print(f"\nProcessing: model={model} qformat={args.qformat} tp={tp}")
            trt_llm_dir = Path(os.environ["HOME"]) / "TensorRT-LLM"

            output_model_name = f"{model}-{args.qformat}-tp{tp}-ctx{args.context_length}"
            checkpoint_dir = args.output_root / output_model_name / "checkpoint"
            output_dir = args.output_root / output_model_name / "engine"

            print("output_model_name:", output_model_name)
            print("checkpoint_dir:", checkpoint_dir)
            print("output_dir:", output_dir)

            if args.skip_if_exists and output_dir.exists():
                print(f"Skipping {output_dir} because it already exists.")
                continue

            if checkpoint_dir.exists():
                raise ValueError(
                    f"Checkpoint directory {checkpoint_dir} already exists."
                )

            if output_dir.exists():
                raise ValueError(f"Output directory {output_dir} already exists.")

            # Quantize HF LLaMA 3.1 and export trtllm checkpoint
            quantize_cmd = [
                "python3",
                f"{trt_llm_dir}/examples/quantization/quantize.py",
                "--model_dir",
                str(model_dir),
                "--dtype",
                "float16",
                "--qformat",
                args.qformat,
                "--output_dir",
                str(checkpoint_dir),
                "--tp_size",
                str(tp),
            ]
            quantize_cmd.extend(quantization_flags)
            print(" ".join(quantize_cmd))
            subprocess.run(
                quantize_cmd,
                stdout=sys.stdout,
                stderr=sys.stderr,
                check=True,
            )
            print(f"Checkpoint written to {checkpoint_dir}")

            # Build trtllm engines from the trtllm checkpoint
            trtllm_cmd = [
                "trtllm-build",
                "--checkpoint_dir",
                str(checkpoint_dir),
                "--output_dir",
                str(output_dir),
                "--max_input_len",
                str(args.context_length),
                "--max_seq_len",
                str(args.context_length),
                "--gemm_plugin",
                "float16",
                "--workers",
                str(tp),
            ]
            print(" ".join(trtllm_cmd))
            subprocess.run(
                trtllm_cmd,
                stdout=sys.stdout,
                stderr=sys.stderr,
                check=True,
            )
            print(f"Engine output written to {output_dir}")


if __name__ == "__main__":
    main()