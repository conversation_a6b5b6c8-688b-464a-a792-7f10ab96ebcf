This directory contains scripts for running benchmarking experiments with TensorRT-LLM.

# Background

TensorRT-LLM is Nvidia's inference library for language models. It supports various
models including LLaMA, tensor parallelism, quantization, etc.

Nvidia also provides an inference server called Triton, which supports TRT-LLM
as a backend. Here we focus on TRT-LLM and don't use Triton.

It lives here: https://github.com/NVIDIA/TensorRT-LLM

At a high level, benchmarking TensorRT-LLM involves the following steps:

```
# Launch the 8xA100 and 8xH100 pods using the Nvidia image
# Note: pod names are currently hard-coded in the script
./start_trt_llm_pod.sh my-a100-pod my-h100-pod

# The following is for the A100 pod. Change A100->H100 in all commands to run things
# on H100.

# Connect to a pod (for long-running jobs, recommended to run tmux inside the pod
# because kubectl can disconnect)
kubectl exec -it my-a100-pod -- /bin/bash

# Inside the pod, set up SSH access to GitHub, then:
<NAME_EMAIL>:augmentcode/augment.git
cd augment/experimental/guy/trt-llm
./install-trt-llm-pod.sh

# Build all the models
./prepare_llama_a100.sh

# Run the benchmark
# Results are saved to /mnt/efs/augment/user/guy/trt-llm/benchmark_results/
./run_benchmark_a100.sh
```
