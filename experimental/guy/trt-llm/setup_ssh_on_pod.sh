#!/bin/bash

set -x
set -e

POD_NAME=$1
REMOTE_HOME=/root/
REMOTE_SSH_DIR=/root/.ssh/

cd ~/.ssh

kubectl exec -it $POD_NAME -- /bin/bash -c "mkdir -p $REMOTE_SSH_DIR"
kubectl cp augment_rsa $POD_NAME:$REMOTE_SSH_DIR
kubectl cp augment_rsa.pub $POD_NAME:$REMOTE_SSH_DIR
kubectl cp config $POD_NAME:$REMOTE_SSH_DIR
kubectl exec -it $POD_NAME -- /bin/bash -c "chown root.root $REMOTE_SSH_DIR/*"

cd ~
kubectl cp .gitconfig $POD_NAME:$REMOTE_HOME
