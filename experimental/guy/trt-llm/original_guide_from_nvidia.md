# Start Cluster - use nvcr.io/nvidia/tritonserver:24.04-trtllm-python-py3 container
ngc base-command job run --name "alex_trt_llm_test" \
--priority NORMAL \
--order 50 \
--preempt RUNONCE \
--min-timeslice 105592s \
--total-runtime 105592s \
--ace sa-nvex-iad2-ace \
--instance dgxa100.80g.1.norm \
--commandline "/bin/bash -c 'apt update && apt install -y python3-pip && pip3 install jupyterlab && jupyter lab --ip=0.0.0.0 --allow-root --no-browser --NotebookApp.token= --notebook-dir=/ --NotebookApp.allow_origin=*'" \
--result /results \
--image "nvcr.io/nvidia/tritonserver:24.04-trtllm-python-py3" \
--org r2kuatviomfd \
--team internal-sandbox \
--workspace uATq9Z83TXKqZ_pedEkbsg:/mount/data:RW \
--port 8888

# Or go here and build the container then run in similar fashion to above
https://github.com/triton-inference-server/tensorrtllm_backend?tab=readme-ov-file#option-1-build-via-the-buildpy-script-in-server-repo

#install git-lfs (needed for HF models)
apt-get install git-lfs

# Log into HF
git config --global credential.helper store
pip install huggingface_hub
huggingface-cli login

# Get the HF model (you may have to approve first)
git-lfs clone https://huggingface.co/mistralai/Mistral-7B-Instruct-v0.2
mv -r Mistral-7B-Instruct-v0.2 /mount/data/.

#install requirements
git clone https://github.com/NVIDIA/TensorRT-LLM.git
cd TensorRT-LLM/

# Build engine with a few options
MODEL_DIR=/mount/data/Mistral-7B-Instruct-v0.2
CHECKPOINT_DIR=/mount/data/Mistral-7B-Instruct-v0.2_checkpoint
OUTPUT_DIR=/mount/data/Mistral-7B-Instruct-v0.2_trtllm

rm -rf $CHECKPOINT_DIR
rm -rf $OUTPUT_DIR
python3 ./examples/llama/convert_checkpoint.py --model_dir $MODEL_DIR \
                             --output_dir $CHECKPOINT_DIR \
                             --dtype float16

trtllm-build --checkpoint_dir $CHECKPOINT_DIR \
            --output_dir $OUTPUT_DIR \
            --gemm_plugin float16 \
            --gpt_attention_plugin float16 \
            --max_input_len 32256

# Run Mistral 7B fp16 inference with sliding window/cache size 4096
mpirun -n 1 --allow-run-as-root python3 ./examples/run.py \
               --tokenizer_dir $MODEL_DIR \
               --engine_dir $OUTPUT_DIR \
               --max_output_len 1000 \
               --input_text "who is  is Jensen Huang?"


# Copy Files
cd ..
rm -rf tensorrtllm_backend
git clone https://github.com/triton-inference-server/tensorrtllm_backend.git
cd tensorrtllm_backend

rm -rf triton_model_repo
mkdir triton_model_repo
cp -r all_models/inflight_batcher_llm/* triton_model_repo/
cp $OUTPUT_DIR/* triton_model_repo/tensorrt_llm/1


python3 tools/fill_template.py -i triton_model_repo/preprocessing/config.pbtxt tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:64,preprocessing_instance_count:1

python3 tools/fill_template.py -i triton_model_repo/postprocessing/config.pbtxt tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:64,postprocessing_instance_count:1

python3 tools/fill_template.py -i triton_model_repo/tensorrt_llm_bls/config.pbtxt triton_max_batch_size:64,bls_instance_count:1,accumulate_tokens:True,decoupled_mode:True

python3 tools/fill_template.py -i triton_model_repo/ensemble/config.pbtxt triton_max_batch_size:64,decoupled_mode:True

python3 tools/fill_template.py -i triton_model_repo/tensorrt_llm/config.pbtxt triton_max_batch_size:64,max_beam_width:1,engine_dir:${OUTPUT_DIR},max_tokens_in_paged_kv_cache:2560,max_attention_window_size:2560,kv_cache_free_gpu_mem_fraction:0.5,exclude_input_in_output:True,enable_kv_cache_reuse:False,batching_strategy:inflight_fused_batching,max_queue_delay_microseconds:0,decoupled_mode:True


# run server
python3 ./scripts/launch_triton_server.py --model_repo ./triton_model_repo --world_size 1


# Open a notebook and run this
import requests
import json
import sseclient

url = 'http://localhost:8000/v2/models/ensemble/generate_stream'
data = {
    "text_input": "who is  is Jensen Huang?",
    "parameters": {
        "max_tokens": 1000,
        "stream": True,
#         "headers": headers
    }
}

all_data = []
response = requests.post(url, json=data, stream=True)
client = sseclient.SSEClient(response)
for event in client.events():
    data = json.loads(event.data)
    all_data.append(data)
    print(data['text_output'], end=" ")
