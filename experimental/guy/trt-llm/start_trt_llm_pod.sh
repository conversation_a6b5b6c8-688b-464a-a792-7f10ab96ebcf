#!/bin/bash
#
# Create a 8xA100 or 8xH100 pod with the TritonRT-LLM image. The memory is required for checkpoint conversion from HF to TRT-LLM.
#
# For podspecs (where the memory number came from), see: https://github.com/augmentcode/augment/pull/6770/files

A100_POD_NAME=$1
H100_POD_NAME=$2

python3 $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py \
    --cluster CW create \
    --gpu-type A100 \
    --gpu-count 8 \
    --cpu-count 96 \
    --memory 948 \
    --custom-image nvidia/cuda:12.1.0-devel-ubuntu22.04 \
    $A100_POD_NAME

python3 $HOME/augment/deploy/dev/reserved_nodes/launch_pod.py \
    --cluster CW create \
    --gpu-type H100 \
    --gpu-count 8 \
    --cpu-count 96 \
    --memory 1000 \
    --custom-image nvidia/cuda:12.1.0-devel-ubuntu22.04 \
    $H100_POD_NAME
