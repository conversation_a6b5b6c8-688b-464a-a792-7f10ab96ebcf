Build a new image with

  docker build -t trt-llm-augment:$IMAGE_TAG \
    -t au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/trt-llm-augment:$IMAGE_TAG \
    -t asia-southeast1-docker.pkg.dev/augment-387916/au-docker-singapore/trt-llm-augment:$IMAGE_TAG .

Then push the images and relaunch.sh to restart

View progress:

  kubectl --context $context get pod,service -l aug.app=trt-llm


Test a server.  Ports will be 80N0 where 0<N<8

for ip in $(kubectl --context gcp-sing get service,pod -l aug.app=trt-llm | awk '/LoadBalancer/ {print $4}'); do
  for port in $(seq 0 7); do
    time curl -X POST ${ip}:80${port}0/v2/models/ensemble/generate \
      -d '{"text_input": "What is machine learning?", "max_tokens": 20, "bad_words": ["intelligence", "modes", "learn"], "pad_id": 2, "end_id": 2}';
  done
done

for ip in $(kubectl --context coreweave get service,pod -l aug.app=trt-llm | awk '/ClusterIP/ {print $3}'); do
  for port in $(seq 0 7); do
    time curl -X POST ${ip}:80${port}0/v2/models/ensemble/generate \
      -d '{"text_input": "What is machine learning?", "max_tokens": 20, "bad_words": ["intelligence", "modes", "learn"], "pad_id": 2, "end_id": 2}';
  done
done
