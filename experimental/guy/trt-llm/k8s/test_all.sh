kubectl --context=gcp-sing get svc -l aug.app=trt-llm -o template='{{range .items}}{{.metadata.name}} {{range .status.loadBalancer.ingress}}{{.ip}}{{end}}{{"\n"}}{{end}}' \
| while read -r svc ip; do
  for port in $(seq 0 7); do
    cmd=(
      curl -s -X POST ${ip}:80${port}0/v2/models/ensemble/generate
      -d '{"text_input": "What is machine learning?", "max_tokens": 20, "bad_words": ["intelligence", "modes", "learn"], "pad_id": 2, "end_id": 2}'
    )
    echo == "svc/$svc" == "${cmd[*]}"
    "${cmd[@]}" | jq
    echo
  done
done

kubectl --context=coreweave get svc -l aug.app=trt-llm -o template='{{range .items}}{{.metadata.name}} {{range .status.loadBalancer.ingress}}{{.ip}}{{end}}{{"\n"}}{{end}}' \
| while read -r svc ip; do
  for port in $(seq 0 7); do
    cmd=(
      curl -s -X POST ${ip}:80${port}0/v2/models/ensemble/generate
      -d '{"text_input": "What is machine learning?", "max_tokens": 20, "bad_words": ["intelligence", "modes", "learn"], "pad_id": 2, "end_id": 2}'
    )
    echo == "svc/$svc" == "${cmd[*]}"
    "${cmd[@]}" | jq
    echo
  done
done
