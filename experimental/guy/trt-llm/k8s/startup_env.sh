# These should be set by the container env:
# export TP=1
# export QFORMAT=fp8   # H100 maybe?
# export QFORMAT=int4_awq  # A100
# export MODEL_NAME=Meta-Llama-3-70B-Instruct
# export OPAL_PREFIX=/opt/hpcx/ompi

# Derived from container env:
export MODEL_VARIANT_NAME=${MODEL_NAME}-${QFORMAT}-tp${TP}
export MODEL_DIR=/mnt/efs/augment/checkpoints/llama3/hf/${MODEL_NAME}

export COMPILED_MODELS_ROOT=/mnt/efs/augment/user/guy/triton
export CHECKPOINT_DIR=${COMPILED_MODELS_ROOT}/${MODEL_VARIANT_NAME}/checkpoint
export OUTPUT_DIR=${COMPILED_MODELS_ROOT}/${MODEL_VARIANT_NAME}/engine

export PATH=/usr/local/mpi/bin:$PATH
export LD_LIBRARY_PATH=/usr/local/tensorrt/lib/:/opt/tritonserver/backends/tensorrtllm:/usr/local/tensorrt/lib:/usr/local/lib/python3.10/dist-packages/torch/lib:/usr/local/lib/python3.10/dist-packages/torch_tensorrt/lib:/usr/local/cuda/compat/lib:/usr/local/nvidia/lib:/usr/local/nvidia/lib64
