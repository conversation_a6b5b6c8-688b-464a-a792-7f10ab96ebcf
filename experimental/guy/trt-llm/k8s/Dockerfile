FROM nvcr.io/nvidia/tritonserver:24.04-trtllm-python-py3

RUN apt-get update && \
    apt-get install -y \
    git-lfs \
    net-tools \
    tmux \
    vim

WORKDIR /home/<USER>
RUN chown -R 1000:1000 /home/<USER>
USER 1000:1000

# mpirun breaks in strange and wonderful ways without this.
ENV OPAL_PREFIX=/opt/hpcx/ompi

RUN curl -Lo TensorRT.tar.gz \
    https://github.com/NVIDIA/TensorRT-LLM/archive/refs/tags/v0.9.0.tar.gz && \
    tar -xzf TensorRT.tar.gz && \
    rm TensorRT.tar.gz && \
    mv TensorRT-LLM-0.9.0 TensorRT-LLM

RUN curl -L -o tensorrtllm_backend.tar.gz \
    https://github.com/triton-inference-server/tensorrtllm_backend/archive/refs/tags/v0.9.0.tar.gz && \
    tar -xzf tensorrtllm_backend.tar.gz && \
    rm tensorrtllm_backend.tar.gz && \
    mv tensorrtllm_backend-0.9.0 tensorrtllm_backend

WORKDIR tensorrtllm_backend

COPY --chown=1000:1000 startup.sh /home/<USER>/startup.sh
COPY --chown=1000:1000 startup_env.sh /home/<USER>/startup_env.sh
