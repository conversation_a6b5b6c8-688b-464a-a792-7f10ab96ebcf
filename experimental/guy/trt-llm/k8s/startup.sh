#!/bin/bash

set -x
set -e

source $(dirname $0)/startup_env.sh

# ENV TP=1
# ENV QFORMAT=fp8
# ENV MODEL_NAME=Meta-Llama-3-70B-Instruct
# ENV MODEL_VARIANT_NAME=${MODEL_NAME}-${QFORMAT}-tp${TP}
# ENV MODEL_DIR=/mnt/efs/augment/checkpoints/llama3/hf/${MODEL_NAME}

# ENV COMPILED_MODELS_ROOT=/mnt/efs/augment/user/marcmac/triton
# ENV CHECKPOINT_DIR=${COMPILED_MODELS_ROOT}/${MODEL_VARIANT_NAME}/checkpoint
# ENV OUTPUT_DIR=${COMPILED_MODELS_ROOT}/${MODEL_VARIANT_NAME}/engine

# TP, MODEL_DIR, OUTPUT_DIR are required

if [[ -z "${TP}" ]]; then
    echo "TP not set"
    exit 1
fi

if [[ -z "${MODEL_DIR}" ]]; then
    echo "MODEL_DIR not set"
    exit 1
fi

if [[ -z "${OUTPUT_DIR}" ]]; then
    echo "OUTPUT_DIR not set"
    exit 1
fi

BASE=$(dirname $0)/tensorrtllm_backend

rm -rf ${BASE}/triton_model_repo
mkdir -p ${BASE}/triton_model_repo

cp -r ${BASE}/all_models/inflight_batcher_llm/* ${BASE}/triton_model_repo/

rsync -av ${OUTPUT_DIR}/* ${BASE}/triton_model_repo/tensorrt_llm/1/

cd ${BASE}
python3 tools/fill_template.py -i \
    triton_model_repo/preprocessing/config.pbtxt \
    tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:${MAX_BATCH_SIZE},preprocessing_instance_count:1
python3 tools/fill_template.py -i \
    triton_model_repo/postprocessing/config.pbtxt \
    tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:${MAX_BATCH_SIZE},postprocessing_instance_count:1
python3 tools/fill_template.py -i \
    triton_model_repo/tensorrt_llm_bls/config.pbtxt \
    triton_max_batch_size:${MAX_BATCH_SIZE},bls_instance_count:1,accumulate_tokens:True,decoupled_mode:True
python3 tools/fill_template.py -i \
    triton_model_repo/ensemble/config.pbtxt \
    triton_max_batch_size:${MAX_BATCH_SIZE},decoupled_mode:True
python3 tools/fill_template.py -i \
    triton_model_repo/tensorrt_llm/config.pbtxt \
    triton_max_batch_size:${MAX_BATCH_SIZE},max_beam_width:1,engine_dir:${OUTPUT_DIR},kv_cache_free_gpu_mem_fraction:0.5,exclude_input_in_output:True,enable_kv_cache_reuse:False,batching_strategy:inflight_fused_batching,max_queue_delay_microseconds:0,decoupled_mode:True,triton_backend:tensorrtllm

# Run server
for CUDA_DEVICE in $(echo ${CUDA_VISIBLE_DEVICES} | sed -e 's/,/ /g'); do
    PORT_PREFIX="80${CUDA_DEVICE}"
    HTTP_PORT="${PORT_PREFIX}0"
    GRPC_PORT="${PORT_PREFIX}1"
    METRICS_PORT="${PORT_PREFIX}2"
    export CUDA_VISIBLE_DEVICES=${CUDA_DEVICE}
    python3 ./scripts/launch_triton_server.py \
        -f \
        --model_repo ./triton_model_repo \
        --http_port ${HTTP_PORT} \
        --grpc_port ${GRPC_PORT} \
        --metrics_port ${METRICS_PORT} \
        --log --log-file log_${CUDA_DEVICE}.txt \
        --world_size ${TP} |& tee ${CUDA_DEVICE}_log.txt &
done

wait
