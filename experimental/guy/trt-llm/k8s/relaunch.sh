#
action=${1:-"apply"}
context="cw-east4-admin"
namespace="cw-east4"
kubectl="kubectl --context ${context} ${action} -f"
image_revision="2024052701"

# # Currently 1 H100 node in CW
# cw_h100_ct=1

# for node in $(seq 1 $cw_h100_ct); do
#   idx=$(( $node - 1 ))
#   ${kubectl} \
#     <(jsonnet -y deployment-cw.jsonnet \
#       --ext-str image_revision="${image_revision}" \
#       --ext-str name="trt-llm-h100-$idx" \
#       --ext-str gpu_type="H100_NVLINK_80GB" \
#       --ext-str qformat=fp8 \
#       --ext-str model_name=Meta-Llama-3-70B-Instruct
#     )
# done

# Currently 1 A100 node in CW
cw_a100_ct=8

for node in $(seq 1 $cw_a100_ct); do
  idx=$(( $node - 1 ))
  ${kubectl} \
    <(jsonnet -y deployment-cw.jsonnet \
      --ext-str image_revision="${image_revision}" \
      --ext-str namespace="${namespace}" \
      --ext-str name="trt-llm-a100-$idx" \
      --ext-str gpu_type="A100_NVLINK_80GB" \
      --ext-str qformat=int4_awq \
      --ext-str model_name=Meta-Llama-3-70B-Instruct
    )
done
