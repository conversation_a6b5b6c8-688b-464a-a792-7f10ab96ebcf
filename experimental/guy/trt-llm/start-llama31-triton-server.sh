#!/bin/bash
#
# Then run start-llama31-triton-server.sh to start the server.
#

set -x
set -e

# Tensor parallelism level
# TP=1
TP=2
TOTAL_GPUS=8  # Adjust this to the total number of available GPUs
NUM_INSTANCES=$((TOTAL_GPUS / TP))

# Quantization format
#QFORMAT=fp8
#QFORMAT=w4a8_awq
# QFORMAT=int4_awq
QFORMAT=fp8

# Model name
MODEL_NAME=Meta-Llama-3.1-8B-Instruct

CONTEXT_LENGTH=8192
MAX_BATCH_SIZE=64

# Script starts here
#MODEL_VARIANT_NAME=${MODEL_NAME}-${QFORMAT}-tp${TP}-ctx${CONTEXT_LENGTH}
MODEL_VARIANT_NAME=${MODEL_NAME}-${QFORMAT}-tp${TP}-ctx${CONTEXT_LENGTH}

COMPILED_MODELS_ROOT=/mnt/efs/augment/user/tamuz/triton

MODEL_DIR=/mnt/efs/augment/checkpoints/llama3.1/hf/$MODEL_NAME
OUTPUT_DIR=$COMPILED_MODELS_ROOT/$MODEL_VARIANT_NAME/engine

[ -d "$MODEL_DIR" ] || exit 1
[ -d "$OUTPUT_DIR" ] || exit 1

# === Prepare the servers ===

# Clone the backend repo
cd ~
REPO_DIR=tensorrtllm_backend
rm -rf $REPO_DIR
git clone --branch v0.12.0 https://github.com/triton-inference-server/tensorrtllm_backend.git $REPO_DIR
cd $REPO_DIR

# Copy the weights into the server directory
mkdir triton_model_repo
cp -r all_models/inflight_batcher_llm/* triton_model_repo/
cp $OUTPUT_DIR/* triton_model_repo/tensorrt_llm/1

# Prepare the model configuration by filling the template
python3 tools/fill_template.py -i triton_model_repo/preprocessing/config.pbtxt tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:${MAX_BATCH_SIZE},preprocessing_instance_count:1

python3 tools/fill_template.py -i triton_model_repo/postprocessing/config.pbtxt tokenizer_dir:${MODEL_DIR},tokenizer_type:auto,triton_max_batch_size:${MAX_BATCH_SIZE},postprocessing_instance_count:1

python3 tools/fill_template.py -i triton_model_repo/tensorrt_llm_bls/config.pbtxt triton_max_batch_size:${MAX_BATCH_SIZE},bls_instance_count:1,accumulate_tokens:True,decoupled_mode:True

python3 tools/fill_template.py -i triton_model_repo/ensemble/config.pbtxt triton_max_batch_size:${MAX_BATCH_SIZE},decoupled_mode:True

python3 tools/fill_template.py -i triton_model_repo/tensorrt_llm/config.pbtxt triton_max_batch_size:${MAX_BATCH_SIZE},max_beam_width:1,engine_dir:${OUTPUT_DIR},exclude_input_in_output:True,enable_kv_cache_reuse:False,batching_strategy:inflight_fused_batching,max_queue_delay_microseconds:0,decoupled_mode:True,triton_backend:tensorrtllm

# Function to clean up background processes
cleanup() {
    echo "Cleaning up..."
    pkill -P $$
    exit
}

# Set up trap to call cleanup function if the script is interrupted
trap cleanup INT TERM

# Start the servers
for n in $(seq 0 $((NUM_INSTANCES - 1))); do
    BASE_GPU=$((n * TP))
    HTTP_PORT=$((8000 + BASE_GPU))
    GRPC_PORT=$((9000 + BASE_GPU))
    METRICS_PORT=$((10000 + BASE_GPU))

    # Construct CUDA_VISIBLE_DEVICES string
    GPU_DEVICES=""
    for i in $(seq 0 $((TP - 1))); do
        if [ $i -ne 0 ]; then
            GPU_DEVICES+=","
        fi
        GPU_DEVICES+=$((BASE_GPU + i))
    done

    echo "Starting server instance $n using GPUs: $GPU_DEVICES"
    CUDA_VISIBLE_DEVICES=$GPU_DEVICES mpirun -n $TP --allow-run-as-root \
    python3 ./scripts/launch_triton_server.py \
        --model_repo ./triton_model_repo \
        --world_size $TP \
        --http_port $HTTP_PORT \
        --grpc_port $GRPC_PORT \
        --metrics_port $METRICS_PORT \
        --log \
        --log-file log_${BASE_GPU}.txt &

    for i in {1..30}; do
        if curl -s -f http://localhost:$HTTP_PORT/v2/health/ready; then
            echo "Server instance $n started successfully."
            break
        fi
        if [ $i -eq 30 ]; then
            echo "Failed to start server instance $n. Check log_${BASE_GPU}.txt for details."
            cleanup
        fi
        sleep 10
    done
done

wait


