#!/bin/bash

time python3 prepare_llama_engine.py \
	--model_dir /mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B \
	--context_length 8192 \
	--tensor_parallelism 1,2,4,8 \
	--qformat fp8

time python3 prepare_llama_engine.py \
	--model_dir /mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B \
	--context_length 8192 \
	--tensor_parallelism 1,2,4,8 \
	--qformat w4a8_awq

time python3 prepare_llama_engine.py \
	--model_dir /mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-70B \
	--context_length 8192 \
	--tensor_parallelism 1,2,4,8 \
	--qformat fp8

time python3 prepare_llama_engine.py \
	--model_dir /mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-70B \
	--context_length 8192 \
	--tensor_parallelism 1,2,4,8 \
	--qformat w4a8_awq
