{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skipping 0 completed examples\n", "\n", "============ Example 1 of 50 ============\n", "\n", "================================================================================\n", "instruction: Use f-string\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Use f-string\n", "\n", "```\n", "        )\n", "    return REMOTE_REFS[service]\n", "\n", "\n", "def paasta_start_or_stop(args, desired_state):\n", "    \"\"\"Requests a change of state to start or stop given branches of a service.\"\"\"\n", "    soa_dir = args.soa_dir\n", "\n", "    pargs = apply_args_filters(args)\n", "    if len(pargs) == 0:\n", "        return 1\n", "\n", "    affected_services = {\n", "        s for service_list in pargs.values() for s in service_list.keys()\n", "    }\n", "    if len(affected_services) > 1:\n", "        paasta_print(\n", "            PaastaColors.red(\"Warning: trying to start/stop/restart multiple services:\")\n", "        )\n", "\n", "\n", "START REGION\n", "        for cluster, services_instances in pargs.items():\n", "            paasta_print(\"Cluster %s:\" % cluster)\n", "            for service, instances in services_instances.items():\n", "                paasta_print(\"    Service %s:\" % service)\n", "                paasta_print(\"        Instances %s\" % \",\".join(instances.keys()))\n", "\n", "END REGION\n", "        if sys.stdin.isatty():\n", "            confirm = choice.Binary(\"Are you sure you want to continue?\", False).ask()\n", "        else:\n", "            confirm = False\n", "        if not confirm:\n", "            paasta_print()\n", "            paasta_print(\"exiting\")\n", "            return 1\n", "\n", "    invalid_deploy_groups = []\n", "    marathon_message_printed = False\n", "    affected_flinks = []\n", "\n", "    if args.clusters is None or args.instances is None:\n", "        if confirm_to_continue(pargs.items(), desired_state) is False:\n", "            paasta_print()\n", "            paasta_print(\"exiting\")\n", "            return 1\n", "\n", "    for cluster, services_instances in pargs.items():\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers, using f-strings:\n", "```\n", "START REGION\n", "        for cluster, services_instances in pargs.items():\n", "            paasta_print(f\"Cluster {cluster}:\")\n", "            for service, instances in services_instances.items():\n", "                paasta_print(f\"    Service {service}:\")\n", "                paasta_print(f\"        Instances {', '.join(instances.keys())}\")\n", "END REGION\n", "```\n", "I replaced the `%` formatting with f-strings, which is a more modern and efficient way of formatting strings in Python. Let me know if you have any further requests!\n", "Levenshtein distance: 2\n", "\n", "============ Example 2 of 50 ============\n", "\n", "================================================================================\n", "instruction: change in, out to type String\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "change in, out to type String\n", "\n", "```\n", "\n", "START REGION\n", "package jrestful.link;\n", "\n", "import jrestful.Method;\n", "import jrestful.fp.None;\n", "import jrestful.fp.Option;\n", "import jrestful.fp.Some;\n", "import jrestful.type.MediaType;\n", "\n", "public record RelLink(\n", "  String rel,\n", "  Method method,\n", "  Option<MediaType> in,\n", "  MediaType out\n", ") {\n", "  public static RelLink get(\n", "    final String rel,\n", "    final MediaType out\n", "  ) {\n", "    return new RelLink(rel, Method.GET, new None(), out);\n", "  }\n", "\n", "  public static RelLink put(\n", "    final String rel,\n", "    final MediaType in,\n", "    final MediaType out\n", "  ) {\n", "    return new RelLink(rel, Method.PUT, new Some(in), out);\n", "  }\n", "\n", "  public static RelLink delete(\n", "    final String rel,\n", "    final MediaType out\n", "  ) {\n", "    return new RelLink(rel, Method.DELETE, new None(), out);\n", "  }\n", "\n", "}\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "START REGION\n", "package jrestful.link;\n", "\n", "import jrestful.Method;\n", "import jrestful.fp.None;\n", "import jrestful.fp.Option;\n", "import jrestful.fp.Some;\n", "import jrestful.type.MediaType;\n", "\n", "public record RelLink(\n", "  String rel,\n", "  Method method,\n", "  Option<String> in,\n", "  String out\n", ") {\n", "  public static RelLink get(\n", "    final String rel,\n", "    final String out\n", "  ) {\n", "    return new RelLink(rel, Method.GET, new None<>(), out);\n", "  }\n", "\n", "  public static RelLink put(\n", "    final String rel,\n", "    final String in,\n", "    final String out\n", "  ) {\n", "    return new RelLink(rel, Method.PUT, new Some<>(in), out);\n", "  }\n", "\n", "  public static RelLink delete(\n", "    final String rel,\n", "    final String out\n", "  ) {\n", "    return new RelLink(rel, Method.DELETE, new None<>(), out);\n", "  }\n", "\n", "}\n", "END REGION\n", "```\n", "\n", "I replaced `MediaType` with `String` in the `RelLink` constructor and the `get`, `put`, and `delete` methods.\n", "Levenshtein distance: 39\n", "\n", "============ Example 3 of 50 ============\n", "\n", "================================================================================\n", "instruction: keep a single space after each , and a single space before and after +\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "keep a single space after each , and a single space before and after +\n", "\n", "```\n", "        #\n", "        # a=rename_pool(newpool.name, newpool.pg_num, newpool.pgp_num, minion)\n", "        # kwargs = {'size':newpool.size, 'min_size':newpool.min_size, 'crash_replay_interval':newpool.crash_replay_interval,'crush_ruleset':newpool.crush_ruleset}\n", "        # b=set_pool_values(newpool.name, **kwargs)\n", "        # quotas = {'max_objects':newpool.quota_max_objects, 'max_bytes': newpool.quota_max_bytes}\n", "        # c=set_pool_quotas(newpool.name, **quotas)\n", "        # return c\n", "\n", "        data = requests.get(cephRestApiUrl+'osd/dump.json')\n", "        if data.status_code != 200:\n", "            return 'Error '+str(data.status_code)+' on the request getting pools'\n", "        else:\n", "            #r = data.json()\n", "            r = data.content\n", "            r = json.loads(r)\n", "            ind = getindice(id, data)\n", "            savedpool = Pools()\n", "            savedpool.savedpool_attribute(ind, data)\n", "\n", "            # rename the poolname\n", "\n", "START REGION\n", "            if str(newpool.name) != str(savedpool.name):\n", "                r = requests.put(cephRestApiUrl +'osd/pool/rename?srcpool='+str(savedpool.name)+'&destpool='+str(newpool.name))\n", "\n", "            # set pool parameter\n", "\n", "            var_name= ['size', 'min_size','crash_replay_interval','pg_num','pgp_num','crush_ruleset']\n", "            param_to_set_list = [newpool.size, newpool.min_size,newpool.crash_replay_interval, newpool.pg_num, newpool.pgp_num, newpool.crush_ruleset]\n", "            default_param_list = [savedpool.size, savedpool.min_size,savedpool.crash_replay_interval,savedpool.pg_num, savedpool.pgp_num, savedpool.crush_ruleset]\n", "\n", "            for i in range(len(default_param_list)):\n", "                if param_to_set_list[i] != default_param_list[i]:\n", "                    r = requests.put(cephRestApiUrl+'osd/pool/set?pool='+str(newpool.name)+'&var='+var_name[i]+'&val='+str(param_to_set_list[i]))\n", "                else:\n", "                    pass\n", "\n", "END REGION\n", "            # set object or byte limit on pool\n", "\n", "            field_name = ['max_objects','max_bytes']\n", "            param_to_set = [newpool.quota_max_objects, newpool.quota_max_bytes]\n", "            default_param = [savedpool.quota_max_objects, savedpool.quota_max_bytes]\n", "\n", "            for i in range(len(default_param)):\n", "                if param_to_set[i] != default_param[i]:\n", "                    r = requests.put(cephRestApiUrl+'osd/pool/set-quota?pool='+str(newpool.name)+'&field='+field_name[i]+'&val='+str(param_to_set[i]))\n", "                else:\n", "                    pass\n", "            return str(r.status_code)\n", "\n", "def makesnapshot_salt(id, minion):\n", "    cephRestApiUrl = getCephRestApiUrl(request);\n", "    data = requests.get(cephRestApiUrl+'osd/dump.json')\n", "    #r = data.json()\n", "    r = data.content\n", "    r = json.loads(r)\n", "    ind = getindice(id,data)\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "            if str(newpool.name) != str(savedpool.name):\n", "                r = requests.put(cephRestApiUrl + 'osd/pool/rename?srcpool=' + str(savedpool.name) + '&destpool=' + str(newpool.name))\n", "\n", "             # set pool parameter\n", "\n", "            var_name = ['size', 'min_size', 'crash_replay_interval', 'pg_num', 'pgp_num', 'crush_ruleset']\n", "            param_to_set_list = [newpool.size, newpool.min_size, newpool.crash_replay_interval, newpool.pg_num, newpool.pgp_num, newpool.crush_ruleset]\n", "            default_param_list = [savedpool.size, savedpool.min_size, savedpool.crash_replay_interval, savedpool.pg_num, savedpool.pgp_num, savedpool.crush_ruleset]\n", "\n", "            for i in range(len(default_param_list)):\n", "                if param_to_set_list[i] != default_param_list[i]:\n", "                    r = requests.put(cephRestApiUrl + 'osd/pool/set?pool=' + str(newpool.name) + '&var=' + var_name[i] + '&val=' + str(param_to_set_list[i]))\n", "                else:\n", "                    pass\n", "END REGION\n", "```\n", "\n", "I made the following changes:\n", "\n", "* Added a single space after each comma in the lists `var_name`, `param_to_set_list`, and `default_param_list`.\n", "* Added a single space before and after the `+` operator in the string concatenations.\n", "Levenshtein distance: 2\n", "\n", "============ Example 4 of 50 ============\n", "\n", "================================================================================\n", "instruction: remove MediaType\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "remove MediaType\n", "\n", "```\n", "  ) {\n", "    return parseLink(linkStr)\n", "      .flatMap(parsedLink ->\n", "        restApi.getLink(context, parsedLink.relLink().rel())\n", "          .map(relLink -> new Link(parsedLink.path(), relLink))\n", "      );\n", "  }\n", "\n", "  private static Optional<Link> parseLink(final String linkStr) {\n", "    final Pattern pattern = Pattern.compile(\n", "      \"([^;]+);\\\\s*rel=\\\"([^;]+)\\\";\\\\s*method=\\\"([^;]+)\\\";\\\\s*type=\\\"([^;]*)\\\";\\\\s*accept=\\\"([^;]+)\\\"\"\n", "    );\n", "    final Matcher matcher = pattern.matcher(linkStr);\n", "    if (matcher.find()) {\n", "      return Optional.of(\n", "        new Link(\n", "          matcher.group(1),\n", "          new RelLink(\n", "            matcher.group(2),\n", "            Method.valueOf(matcher.group(3)),\n", "\n", "START REGION\n", "            new Some<>(new MediaType(matcher.group(4), new TypeObject<>(\"\", \"\"))),\n", "            new MediaType(matcher.group(5), new TypeObject<>(\"\", \"\"))\n", "\n", "END REGION\n", "          )\n", "        )\n", "      );\n", "    } else {\n", "      return Optional.empty();\n", "    }\n", "  }\n", "\n", "  public Optional<Link> getLink(final String rel, final Map<Class<?>, String> classMediaTypeMap) {\n", "    return links.stream()\n", "      .filter(link -> {\n", "        final RelLink relLink = link.relLink();\n", "        final boolean isRelEquals = relLink.rel().equalsIgnoreCase(rel);\n", "        final boolean isOutTypeMatch = isKnownMediaType(relLink.out(), classMediaTypeMap);\n", "        final Boolean isInTypeMatch = relLink.in().map(in -> isKnownMediaType(in, classMediaTypeMap))\n", "          .or<PERSON><PERSON><PERSON>(true);\n", "        return isRelEquals && isOutTypeMatch && isInTypeMatch;\n", "      })\n", "      .findAny();\n", "  }\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", ") {\n", "    return parseLink(linkStr)\n", "      .flatMap(parsedLink ->\n", "        restApi.getLink(context, parsedLink.relLink().rel())\n", "           .map(relLink -> new Link(parsedLink.path(), relLink))\n", "       );\n", "}\n", "\n", "private static Optional<Link> parseLink(final String linkStr) {\n", "    final Pattern pattern = Pattern.compile(\n", "       \"([^;]+);\\\\s*rel=\\\"([^;]+)\\\";\\\\s*method=\\\"([^;]+)\\\"\"\n", "     );\n", "    final Matcher matcher = pattern.matcher(linkStr);\n", "    if (matcher.find()) {\n", "      return Optional.of(\n", "        new Link(\n", "          matcher.group(1),\n", "          new RelLink(\n", "            matcher.group(2),\n", "            Method.valueOf(matcher.group(3)),\n", "START REGION\n", "            null,\n", "            null\n", "END REGION\n", "           )\n", "         )\n", "       );\n", "     } else {\n", "      return Optional.empty();\n", "     }\n", "}\n", "\n", "public Optional<Link> getLink(final String rel, final Map<Class<?>, String> classMediaTypeMap) {\n", "    return links.stream()\n", "       .filter(link -> {\n", "        final RelLink relLink = link.relLink();\n", "        final boolean isRelEquals = relLink.rel().equalsIgnoreCase(rel);\n", "        return isRelEquals;\n", "       })\n", "       .findAny();\n", "}\n", "```\n", "\n", "I removed the `MediaType` references and the `type` and `accept` groups from the pattern, as well as the `isOutTypeMatch` and `isInTypeMatch` checks in the `getLink` method. Let me know if this is what you were looking for!\n", "Levenshtein distance: 42\n", "\n", "============ Example 5 of 50 ============\n", "\n", "================================================================================\n", "instruction: Correct it as dict format\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Correct it as dict format\n", "\n", "```\n", "        \"\"\"Execute the tabular regression task and export the found model\"\"\"\n", "        self.df, test = data_loader(self._configuration)\n", "        X, y = prepare_tabular_dataset(self.df, self._configuration)\n", "        # convert all object columns to categories, because autosklearn only supports numerical, bool and categorical features\n", "        X[X.select_dtypes(['object']).columns] = X.select_dtypes(['object']) \\\n", "        .apply(lambda x: x.astype('category'))\n", "\n", "        automl_settings = self.__generate_settings()\n", "        auto_reg = autosklearn.regression.AutoSklearnRegressor(**automl_settings)\n", "        auto_reg.fit(X, y)\n", "\n", "        export_model(auto_reg, self._configuration[\"result_folder_location\"], \"model_sklearn.p\")\n", "\n", "    def __get_logging_config(self) -> dict:\n", "        \"\"\"Generate the logging configuration dict for autosklearn\n", "\n", "        Returns:\n", "            dict: logging configuration dict\n", "        \"\"\"\n", "        return {\n", "\n", "START REGION\n", "            version = 1\n", "            disable_existing_loggers = True\n", "            formatters = {\n", "                custom = {\n", "                    # More format options are available in the official\n", "                    # `documentation <https://docs.python.org/3/howto/logging-cookbook.html>`_\n", "                    format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", "                }\n", "            }\n", "\n", "END REGION\n", "            # Any INFO level msg will be printed to the console\n", "            'handlers': {\n", "                'console': {\n", "                    'level': 'INFO',\n", "                    'formatter': 'custom',\n", "                    'class': 'logging.StreamHandler',\n", "                    'stream': 'ext://sys.stdout',\n", "                },\n", "            },\n", "\n", "            'loggers': {\n", "                '': {  # root logger\n", "                    'level': 'DEBUG',\n", "                },\n", "                'Client-EnsembleBuilder': {\n", "                    'level': 'DEBUG',\n", "                    'handlers': ['console'],\n", "                },\n", "            },\n", "        }\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers in dict format:\n", "\n", "```\n", "    def __get_logging_config(self) -> dict:\n", "        \"\"\"Generate the logging configuration dict for autosklearn\n", "\n", "        Returns:\n", "            dict: logging configuration dict\n", "        \"\"\"\n", "        return {\n", "START REGION\n", "            \"version\": 1,\n", "            \"disable_existing_loggers\": True,\n", "            \"formatters\": {\n", "                \"custom\": {\n", "                    \"format\": '%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", "                }\n", "            },\n", "END REGION\n", "            # Any INFO level msg will be printed to the console\n", "            'handlers': {\n", "                'console': {\n", "                    'level': 'INFO',\n", "                    'formatter': 'custom',\n", "                    'class': 'logging.StreamHandler',\n", "                    'stream': 'ext://sys.stdout',\n", "                },\n", "            },\n", "\n", "            'loggers': {\n", "                '': {   # root logger\n", "                    'level': 'DEBUG',\n", "                },\n", "                'Client-EnsembleBuilder': {\n", "                    'level': 'DEBUG',\n", "                    'handlers': ['console'],\n", "                },\n", "            },\n", "        }\n", "```\n", "\n", "I corrected the code by using double quotes `\"` instead of single quotes `'` for the dictionary keys, and also added colons `:` after each key.\n", "Levenshtein distance: 178\n", "\n", "============ Example 6 of 50 ============\n", "\n", "================================================================================\n", "instruction: Add the type annotation\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Add the type annotation\n", "\n", "```\n", "        if isinstance(obj, (tuple, list, set)):\n", "            return str(obj)[1:-1].replace(' ', '')\n", "        return str(obj)\n", "\n", "    prefix_ = prefix + ('_' if prefix else '')\n", "    fp = open('{}.txt'.format(prefix or 'meta'), mode='w')\n", "    for key, value in state_dict.items():\n", "        data = None\n", "        if torch.is_tensor(value):\n", "            data = value.data.cpu().numpy()\n", "        elif isinstance(value, np.ndarray):\n", "            data = value\n", "        if data is not None:\n", "            data.tofile('{}{}.bin'.format(prefix_, key))\n", "            fp.write('{}.dtype={}\\n'.format(key, str_(data.dtype.name)))\n", "            fp.write('{}.shape={}\\n'.format(key, str_(data.shape)))\n", "        else:\n", "            fp.write('{}={}\\n'.format(key, str_(value)))\n", "    fp.close()\n", "\n", "\n", "START REGION\n", "def export_onnx_with_validation(\n", "        model,\n", "        inputs,\n", "        export_basepath,\n", "        input_names = None,\n", "        output_names = None,\n", "        use_npz = True,\n", "        *args,\n", "        **kwargs) -> Sequence[Union[torch.Tensor, Sequence[object]]]:\n", "\n", "END REGION\n", "    \"\"\"\n", "    export PyTorch model to ONNX model and export sample inputs and outputs in a Numpy file\n", "    \"\"\"\n", "    is_tuple_or_list = lambda x: isinstance(x, (tuple, list))\n", "\n", "    def tensors_to_arrays(tensors: Union[torch.Tensor, Iterable[\n", "            Union[torch.Tensor, Iterable[Any]]]], ) -> List[np.ndarray]:\n", "        if torch.is_tensor(tensors):\n", "            return tensors.data.cpu().numpy()\n", "        return list(map(tensors_to_arrays, tensors))\n", "\n", "    def zip_dict(\n", "            keys: Union[Iterable[Any], None],\n", "            values: Sequence[Union[Any, Sequence[Any]]],\n", "    ) -> MyDict[Text, Union[object, MyDict[Text, object]]]:\n", "        keys = keys or range(len(values))\n", "        ret = my_dict()\n", "        for idx, (key, value) in enumerate(zip(keys, values)):\n", "            is_key_list = is_tuple_or_list(key)\n", "            is_value_list = is_tuple_or_list(value)\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code with type annotations added:\n", "\n", "```\n", "START REGION\n", "def export_onnx_with_validation(\n", "        model: <PERSON>.nn.<PERSON>,\n", "        inputs: Union[torch.Tensor, Sequence[torch.Tensor]],\n", "        export_basepath: str,\n", "        input_names: Optional[Sequence[str]] = None,\n", "        output_names: Optional[Sequence[str]] = None,\n", "        use_npz: bool = True,\n", "        *args,\n", "        **kwargs) -> Sequence[Union[torch.Tensor, Sequence[object]]]:\n", "END REGION\n", "    \"\"\"\n", "    export PyTorch model to ONNX model and export sample inputs and outputs in a Numpy file\n", "    \"\"\"\n", "    is_tuple_or_list = lambda x: isinstance(x, (tuple, list))\n", "\n", "    def tensors_to_arrays(tensors: Union[torch.Tensor, Iterable[Union[torch.Tensor, Iterable[Any]]]]) -> List[np.ndarray]:\n", "        if torch.is_tensor(tensors):\n", "            return [tensors.data.cpu().numpy()]\n", "        return [tensor for sublist in map(tensors_to_arrays, tensors) for tensor in sublist]\n", "\n", "    def zip_dict(\n", "            keys: Union[Iterable[Any], None],\n", "            values: Sequence[Union[Any, Sequence[Any]]],\n", "     ) -> MyDict[Text, Union[object, MyDict[Text, object]]]:\n", "        keys = keys or range(len(values))\n", "        ret = my_dict()\n", "        for idx, (key, value) in enumerate(zip(keys, values)):\n", "            is_key_list = is_tuple_or_list(key)\n", "            is_value_list = is_tuple_or_list(value)\n", "            if is_key_list:\n", "                key = str_(key)[1:-1].replace(' ', '')\n", "            if is_value_list:\n", "                value = str_(value)[1:-1].replace(' ', '')\n", "            ret[key] = value\n", "        return ret\n", "```\n", "\n", "Note that I added type annotations for the function parameters and return types, as well as for the variables and function return types within the functions. I also added some minor formatting changes to make the code more readable. Let me know if you have any further requests!\n", "Levenshtein distance: 44\n", "\n", "============ Example 7 of 50 ============\n", "\n", "================================================================================\n", "instruction: correct its format as others\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "correct its format as others\n", "\n", "```\n", "[\n", "  {\n", "    \"type\": \"https\",\n", "    \"instruction\": \"what is my ip\",\n", "    \"command\": \"hostname -i\"\n", "  },\n", "  {\n", "    \"type\": \"configuring\",\n", "    \"instruction\": \"list all configuration files\",\n", "    \"command\": \"ls -l /etc\"\n", "  },\n", "  {\n", "    \"type\": \"compression\",\n", "    \"instruction\": \"compress the file.txt\",\n", "    \"command\": \"gzip file.txt\"\n", "  },\n", "  {\n", "\n", "START REGION\n", "    type terminal\n", "    instruction list the environment variables\n", "    command env\n", "  },\n", "\n", "END REGION\n", "  {\n", "    \"type\": \"versioning\",\n", "    \"instruction\": \"get the current branch\",\n", "    \"command\": \"git branch\"\n", "  }\n", "]\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "[\n", "   {\n", "     \"type\": \"https\",\n", "     \"instruction\": \"what is my ip\",\n", "     \"command\": \"hostname -i\"\n", "   },\n", "   {\n", "     \"type\": \"configuring\",\n", "     \"instruction\": \"list all configuration files\",\n", "     \"command\": \"ls -l /etc\"\n", "   },\n", "   {\n", "     \"type\": \"compression\",\n", "     \"instruction\": \"compress the file.txt\",\n", "     \"command\": \"gzip file.txt\"\n", "   },\n", "   {\n", "     \"type\": \"terminal\",\n", "     \"instruction\": \"list the environment variables\",\n", "     \"command\": \"env\"\n", "   },\n", "   {\n", "     \"type\": \"versioning\",\n", "     \"instruction\": \"get the current branch\",\n", "     \"command\": \"git branch\"\n", "   }\n", "]\n", "```\n", "\n", "I corrected the format of the code between the START REGION and END REGION markers to match the rest of the code. Let me know if you have any further requests!\n", "Levenshtein distance: 461\n", "\n", "============ Example 8 of 50 ============\n", "\n", "================================================================================\n", "instruction: Use single quote instead of double quotes\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Use single quote instead of double quotes\n", "\n", "```\n", "\"\"\"Prepare Data for Edit.\"\"\"\n", "import logging\n", "import typing\n", "\n", "# import tenacity\n", "import openai\n", "from openai import openai_object\n", "\n", "# from openai import error as openai_error\n", "\n", "\n", "START REGION\n", "OpenAIChatModels = typing.Literal[\"gpt-4\", \"gpt-3.5-turbo\"]\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = \"gpt-4\",\n", ") -> str:\n", "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = \"user\" if idx == 0 else \"assistant\"\n", "        request_messages.append({\"role\": role, \"content\": message})\n", "\n", "END REGION\n", "    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "    )\n", "    response = typing.cast(openai_object.OpenAIObject, response)\n", "    return response.choices[0].message.content\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers, using single quotes instead of double quotes:\n", "\n", "```python\n", "\"\"\"Prepare Data for Edit.\"\"\"\n", "import logging\n", "import typing\n", "# import tenacity\n", "import openai\n", "from openai import openai_object\n", "# from openai import error as openai_error\n", "\n", "START REGION\n", "OpenAIChatModels = typing.Literal['gpt-4', 'gpt-3.5-turbo']\n", "logger = logging.getLogger(__name__)\n", "\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = 'gpt-4',\n", ") -> str:\n", "    request_messages = [{'role': 'system', 'content': system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = 'user' if idx == 0 else 'assistant'\n", "        request_messages.append({'role': role, 'content': message})\n", "END REGION\n", "    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "     )\n", "    response = typing.cast(openai_object.OpenAIObject, response)\n", "    return response.choices[0].message.content\n", "```\n", "Levenshtein distance: 3\n", "\n", "============ Example 9 of 50 ============\n", "\n", "================================================================================\n", "instruction: Replace prints with logging.error\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Replace prints with logging.error\n", "\n", "```\n", "data = load_data(Path(args.input_data_path))\n", "gen_options = GenerationOptions(\n", "    temperature=args.temperature,\n", "    top_k=args.top_k,\n", "    top_p=args.top_p,\n", "    max_generated_tokens=args.max_generated_tokens,\n", ")\n", "try:\n", "    system = SYSTEMS_REGISTRY.get(args.system)()\n", "except KeyError:\n", "\n", "START REGION\n", "    print(\"Can't find requested system\")\n", "    raise\n", "\n", "try:\n", "    prompt = PROMPTS_REGISTRY.get(args.prompt)()\n", "except KeyError:\n", "    print(\"Can't find requested prompt\")\n", "    raise\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "data = load_data(Path(args.input_data_path))\n", "gen_options = GenerationOptions(\n", "    temperature=args.temperature,\n", "    top_k=args.top_k,\n", "    top_p=args.top_p,\n", "    max_generated_tokens=args.max_generated_tokens,\n", ")\n", "try:\n", "    system = SYSTEMS_REGISTRY.get(args.system)()\n", "except KeyError:\n", "START REGION\n", "    logging.error(\"Can't find requested system\")\n", "    raise\n", "try:\n", "    prompt = PROMPTS_REGISTRY.get(args.prompt)()\n", "except KeyError:\n", "    logging.error(\"Can't find requested prompt\")\n", "    raise\n", "END REGION\n", "```\n", "\n", "I replaced the `print` statements with `logging.error` statements as per your instruction.\n", "Levenshtein distance: 2\n", "\n", "============ Example 10 of 50 ============\n", "\n", "================================================================================\n", "instruction: delete all the type annotations\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "delete all the type annotations\n", "\n", "```\n", "\"\"\"Prepare Data for Edit.\"\"\"\n", "import logging\n", "import typing\n", "\n", "# import tenacity\n", "import openai\n", "from openai import openai_object\n", "\n", "# from openai import error as openai_error\n", "\n", "OpenAIChatModels = typing.Literal[\"gpt-4\", \"gpt-3.5-turbo\"]\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "\n", "START REGION\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = \"gpt-4\",\n", ") -> str:\n", "\n", "END REGION\n", "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = \"user\" if idx == 0 else \"assistant\"\n", "        request_messages.append({\"role\": role, \"content\": message})\n", "    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "    )\n", "    response = typing.cast(openai_object.OpenAIObject, response)\n", "    return response.choices[0].message.content\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```python\n", "\"\"\"Prepare Data for Edit.\"\"\"\n", "import logging\n", "import openai\n", "from openai import openai_object\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "START REGION\n", "def generate_response_via_chat(\n", "    messages, \n", "    system_prompt, \n", "    temperature = 0.2, \n", "    max_tokens = 256, \n", "    model = \"gpt-4\", \n", "):\n", "END REGION\n", "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = \"user\" if idx == 0 else \"assistant\"\n", "        request_messages.append({\"role\": role, \"content\": message})\n", "    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "     )\n", "    response = openai_object.OpenAIObject(response)\n", "    return response.choices[0].message.content\n", "```\n", "Levenshtein distance: 12\n", "\n", "============ Example 11 of 50 ============\n", "\n", "================================================================================\n", "instruction: make all for loops enumerate\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "make all for loops enumerate\n", "\n", "```\n", "\n", "START REGION\n", "for sample in samples:\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"\\n=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for line in result:\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "\n", "END REGION\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "```\n", "START REGION\n", "for i, sample in enumerate(samples):\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"\\n=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for j, line in enumerate(result):\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")\n", "END REGION\n", "```\n", "I made both `for` loops use `enumerate` to get the index `i` and `j` along with the values `sample` and `line`.\n", "Levenshtein distance: 125\n", "\n", "============ Example 12 of 50 ============\n", "\n", "================================================================================\n", "instruction: translate to python\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "translate to python\n", "\n", "```\n", "# **************:entria/apiWithLog.git\n", "# <NAME_EMAIL>:${1}.git $2\n", "# cd $2\n", "# git reset --hard [ENTER HERE THE COMMIT HASH YOU WANT]\n", "\n", "\n", "START REGION\n", "#!/bin/bash\n", "\n", "# List of Git repositories\n", "# repos=(\"https://github.com/example/repo1.git\" \"https://github.com/example/repo2.git\" \"https://github.com/example/repo3.git\")\n", "\n", "if [ \"$#\" -ne 3 ]; then\n", "    echo \"Usage: $0 <org> <proj> <target_sha>\"\n", "    exit 1\n", "fi\n", "\n", "# SHA to checkout\n", "target_sha=$3\n", "\n", "# Directory to clone repositories into\n", "clone_dir=\"repos/$2\"\n", "\n", "# Create the directory if it doesn't exist\n", "mkdir -p \"$clone_dir\"\n", "\n", "# **************:entria/apiWithLog.git\n", "# Extract repository name from the URL\n", "repo_url=\"**************:$1/$2.git\"\n", "\n", "# Clone the repository\n", "git clone \"$repo_url\" \"$clone_dir\"\n", "\n", "# Change to the repository directory\n", "cd \"$clone_dir\" || exit\n", "\n", "# Checkout the specified commit SHA\n", "git reset --hard \"$target_sha\"\n", "\n", "# Return to the original directory\n", "cd - || exit\n", "\n", "echo \"Cloning and checkout completed.\"\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the translated code in Python:\n", "```\n", "import os\n", "import sys\n", "import git\n", "\n", "START REGION\n", "\n", "def clone_and_checkout(org, proj, target_sha):\n", "    # Directory to clone repositories into\n", "    clone_dir = f\"repos/{proj}\"\n", "    # Create the directory if it doesn't exist\n", "    os.makedirs(clone_dir, exist_ok=True)\n", "\n", "    # Extract repository name from the URL\n", "    repo_url = f\"**************:{org}/{proj}.git\"\n", "\n", "    # Clone the repository\n", "    repo = git.Repo.clone_from(repo_url, clone_dir)\n", "\n", "    # Checkout the specified commit SHA\n", "    repo.git.reset('--hard', target_sha)\n", "\n", "    print(\"Cloning and checkout completed.\")\n", "\n", "if __name__ == \"__main__\":\n", "    if len(sys.argv) != 4:\n", "        print(\"Usage: python script.py <org> <proj> <target_sha>\")\n", "        sys.exit(1)\n", "\n", "    org = sys.argv[1]\n", "    proj = sys.argv[2]\n", "    target_sha = sys.argv[3]\n", "\n", "    clone_and_checkout(org, proj, target_sha)\n", "\n", "END REGION\n", "```\n", "Note that I used the `gitpython` library to interact with Git repositories. You may need to install it using `pip install gitpython` before running the script.\n", "Levenshtein distance: 601\n", "\n", "============ Example 13 of 50 ============\n", "\n", "================================================================================\n", "instruction: turn it into Python code\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "turn it into Python code\n", "\n", "```\n", "#!/bin/bash\n", "\n", "# Set your URL and data fields\n", "\n", "START REGION\n", "URL=\"http://example.com/api\"\n", "PREFIX=\"YourPrefix\"\n", "SUFFIX=\"YourSuffix\"\n", "SELECTED_CODE=\"YourCode\"\n", "\n", "# Sending POST request with curl\n", "curl -X POST $URL \\\n", "     -H \"Content-Type: application/x-www-form-urlencoded\" \\\n", "     -d \"prefix=$PREFIX&suffix=$SUFFIX&selected_code=$SELECTED_CODE\"\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code in Python:\n", "```\n", "import requests\n", "\n", "# Set your URL and data fields\n", "START REGION\n", "URL = \"http://example.com/api\"\n", "PREFIX = \"YourPrefix\"\n", "SUFFIX = \"YourSuffix\"\n", "SELECTED_CODE = \"YourCode\"\n", "\n", "# Sending POST request with requests\n", "data = {\"prefix\": PREFIX, \"suffix\": SUFFIX, \"selected_code\": SELECTED_CODE}\n", "response = requests.post(URL, data=data)\n", "\n", "END REGION\n", "```\n", "Note that I used the `requests` library to send the POST request, which is a more Pythonic way of making HTTP requests. I also removed the `curl` command and the bash-specific syntax. Let me know if you have any questions!\n", "Levenshtein distance: 185\n", "\n", "============ Example 14 of 50 ============\n", "\n", "================================================================================\n", "instruction: Change log level to debug.\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Change log level to debug.\n", "\n", "```\n", "try:\n", "\n", "START REGION\n", "    logging.warning(\"Service A is running.\")\n", "    if isinstance(self.model.prompt_formatter, CodeLlamaChatFormatter):\n", "        bos_token_id = None\n", "        eos_token_id = self.model.tokenizer.eos_id  # type: ignore\n", "    else:\n", "        bos_token_id = self.model.tokenizer._raw_tokenizer.bos_token_id  # type: ignore\n", "        eos_token_id = self.model.tokenizer._raw_tokenizer.eos_token_id  # type: ignore\n", "except AttributeError:\n", "    logging.warning(\"Accessing special tokens failed.\")\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "try:\n", "START REGION\n", "    logging.debug(\"Service A is running.\")  # Changed log level to debug\n", "    if isinstance(self.model.prompt_formatter, CodeLlamaChatFormatter):\n", "        bos_token_id = None\n", "        eos_token_id = self.model.tokenizer.eos_id  # type: ignore\n", "    else:\n", "        bos_token_id = self.model.tokenizer._raw_tokenizer.bos_token_id  # type: ignore\n", "        eos_token_id = self.model.tokenizer._raw_tokenizer.eos_token_id  # type: ignore\n", "except AttributeError:\n", "    logging.debug(\"Accessing special tokens failed.\")  # Changed log level to debug\n", "END REGION\n", "```\n", "\n", "I changed the log level from `warning` to `debug` in both logging statements.\n", "Levenshtein distance: 60\n", "\n", "============ Example 15 of 50 ============\n", "\n", "================================================================================\n", "instruction: use string format() instead of f-string\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "use string format() instead of f-string\n", "\n", "```\n", "    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "\n", "START REGION\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(str(system) + \"\\n\")\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "\n", "END REGION\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "     # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        \n", "START REGION\n", "        with (real_output_dir / \"system-{}-of-{}.repr\".format(idx, len(systems))).open(\n", "             \"w\"\n", "         ) as xfile:\n", "            xfile.write(str(system) + \"\\n\")\n", "        with (real_output_dir / \"system-{}-of-{}.json\".format(idx, len(systems))).open(\n", "             \"w\"\n", "         ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "            \n", "END REGION\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(\"Finish loading the system, output_dir={}\".format(real_output_dir))\n", "    logger.info(\"Add the repo with {} documents.\".format(len(repo_for_system)))\n", "```\n", "\n", "I replaced the f-strings with the `format()` method, as per your request. Let me know if you have any further requests!\n", "Levenshtein distance: 16\n", "\n", "============ Example 16 of 50 ============\n", "\n", "================================================================================\n", "instruction: Remove decorators from classes\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Remove decorators from classes\n", "\n", "```\n", "\n", "START REGION\n", "@code_edit_system()\n", "class CodeLLamaInstruct13BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-13b-Instruct-hf\")\n", "    MODEL_NAME = \"codellama_instruct_hf\"\n", "    SKIP_TOKENIZER_LOADING = True\n", "\n", "\n", "@code_edit_system()\n", "class CodeLLamaInstruct34BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-34b-Instruct-hf\")\n", "    MODEL_NAME = \"codellama_instruct_hf\"\n", "    SKIP_TOKENIZER_LOADING = True\n", "\n", "\n", "@code_edit_system()\n", "class WizardCoder7BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0\")\n", "    MODEL_NAME = \"wizardcoder_python_hf\"\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "START REGION\n", "class CodeLLamaInstruct13BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-13b-Instruct-hf\")\n", "    MODEL_NAME = \"codellama_instruct_hf\"\n", "    SKIP_TOKENIZER_LOADING = True\n", "\n", "\n", "class CodeLLamaInstruct34BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-34b-Instruct-hf\")\n", "    MODEL_NAME = \"codellama_instruct_hf\"\n", "    SKIP_TOKENIZER_LOADING = True\n", "\n", "\n", "class WizardCoder7BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0\")\n", "    MODEL_NAME = \"wizardcoder_python_hf\"\n", "END REGION\n", "```\n", "Levenshtein distance: 1\n", "\n", "============ Example 17 of 50 ============\n", "\n", "================================================================================\n", "instruction: Make method f2() static\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Make method f2() static\n", "\n", "```\n", "class A:\n", "    def f1(self):\n", "        print('F1')\n", "\n", "\n", "START REGION\n", "    def f2(self):\n", "        print('F2')\n", "\n", "a = A()\n", "a.f2()\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "class A:\n", "    def f1(self):\n", "        print('F1')\n", "\n", "\n", "START REGION\n", "    @staticmethod\n", "    def f2():\n", "        print('F2')\n", "\n", "A.f2()\n", "END REGION\n", "```\n", "\n", "I made the `f2()` method static by adding the `@staticmethod` decorator and removing the `self` parameter. I also changed the call to `A.f2()` since static methods are called on the class, not on an instance of the class.\n", "Levenshtein distance: 9\n", "\n", "============ Example 18 of 50 ============\n", "\n", "================================================================================\n", "instruction: Change all local variables to camel case.\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Change all local variables to camel case.\n", "\n", "```\n", "\n", "START REGION\n", "model_input = ModelInput(prefix=\"Hello World\")\n", "inputs_gen = model.prompt_formatter.prepare_prompt(model_input)[0]\n", "\n", "result = model.raw_generate(inputs_gen)\n", "\n", "parsed_result = parse_edit_response(result)\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "START REGION\n", "modelInput = ModelInput(prefix=\"Hello World\")\n", "inputsGen = model.promptFormatter.preparePrompt(modelInput)[0]\n", "result = model.rawGenerate(inputsGen)\n", "parsedResult = parseEditResponse(result)\n", "END REGION\n", "```\n", "\n", "I changed all local variables to camel case as instructed. Let me know if you need further assistance!\n", "Levenshtein distance: 13\n", "\n", "============ Example 19 of 50 ============\n", "\n", "================================================================================\n", "instruction: Make all fields in lowercase\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Make all fields in lowercase\n", "\n", "```\n", "\n", "START REGION\n", "@code_edit_system()\n", "class CodeLLamaInstruct13BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-13b-Instruct-hf\")\n", "    MODEL_NAME = \"codellama_instruct_hf\"\n", "    SKIP_TOKENIZER_LOADING = True\n", "\n", "\n", "@code_edit_system()\n", "class CodeLLamaInstruct34BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-34b-Instruct-hf\")\n", "    MODEL_NAME = \"codellama_instruct_hf\"\n", "    SKIP_TOKENIZER_LOADING = True\n", "\n", "\n", "@code_edit_system()\n", "class WizardCoder7BCodeEditSystemHF(CodeEditSystemHF):\n", "    CKPT_PATH = Path(\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0\")\n", "    MODEL_NAME = \"wizardcoder_python_hf\"\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code with all fields in lowercase:\n", "\n", "```\n", "START REGION\n", "@code_edit_system()\n", "class codelammainstruct13bcodeeditsystemhf(CodeEditSystemHF):\n", "    ckpt_path = Path(\"/mnt/efs/augment/checkpoints/llama/hf/codelama-13b-instruct-hf\")\n", "    model_name = \"codelammainstruct_hf\"\n", "    skip_tokenizer_loading = True\n", "\n", "\n", "@code_edit_system()\n", "class codelammainstruct34bcodeeditsystemhf(CodeEditSystemHF):\n", "    ckpt_path = Path(\"/mnt/efs/augment/checkpoints/llama/hf/codelama-34b-instruct-hf\")\n", "    model_name = \"codelammainstruct_hf\"\n", "    skip_tokenizer_loading = True\n", "\n", "\n", "@code_edit_system()\n", "class wizardcoder7bcodeeditsystemhf(CodeEditSystemHF):\n", "    ckpt_path = Path(\"/mnt/efs/augment/checkpoints/llama/hf/wizardcoder-python-7b-v1.0\")\n", "    model_name = \"wizardcoder_python_hf\"\n", "END REGION\n", "```\n", "Levenshtein distance: 48\n", "\n", "============ Example 20 of 50 ============\n", "\n", "================================================================================\n", "instruction: Add one-key flag to all arguments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Add one-key flag to all arguments\n", "\n", "```\n", "import argparse\n", "\n", "parser = argparse.ArgumentParser(\n", "    formatter_class=argparse.ArgumentDefaultsHelpFormatter\n", ")\n", "\n", "parser.add_argument(\n", "    \"--input_data_path\",\n", "    \"-i\",\n", "    type=str,\n", "    help=\"Directory with input data.\",\n", ")\n", "\n", "START REGION\n", "parser.add_argument(\n", "    \"--system\",\n", "    type=str,\n", "    help=\"The system factory name.\",\n", ")\n", "parser.add_argument(\n", "    \"--list_systems\", action=\"store_true\", help=\"List all available systems.\"\n", ")\n", "\n", "END REGION\n", "parser.add_argument(\n", "    \"--output_path\", \"-o\", type=str, help=\"JSON file to store result.\"\n", ")\n", "parser.add_argument(\n", "    \"--prompt\",\n", "    \"-p\",\n", "    type=str,\n", "    help=\"Editing prompt from research.eval.edit.prompts.REGISTRY.\",\n", ")\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code with one-key flags added to all arguments:\n", "```\n", "import argparse\n", "parser = argparse.ArgumentParser(\n", "    formatter_class=argparse.ArgumentDefaultsHelpFormatter\n", ")\n", "\n", "parser.add_argument(\n", "    \"-input_data_path\", \"-i\",\n", "    type=str,\n", "    help=\"Directory with input data.\",\n", ")\n", "\n", "START REGION\n", "parser.add_argument(\n", "    \"-system\", \"-s\",\n", "    type=str,\n", "    help=\"The system factory name.\",\n", ")\n", "parser.add_argument(\n", "    \"-list_systems\", \"-l\", action=\"store_true\", help=\"List all available systems.\"\n", ")\n", "\n", "END REGION\n", "parser.add_argument(\n", "    \"-output_path\", \"-o\",\n", "    type=str,\n", "    help=\"JSON file to store result.\"\n", ")\n", "parser.add_argument(\n", "    \"-prompt\", \"-p\",\n", "    type=str,\n", "    help=\"Editing prompt from research.eval.edit.prompts.REGISTRY.\",\n", ")\n", "```\n", "I added a one-key flag to each argument, using a single character (e.g. `-s` for `system`, `-l` for `list_systems`, etc.). Let me know if this is what you were looking for!\n", "Levenshtein distance: 6\n", "\n", "============ Example 21 of 50 ============\n", "\n", "================================================================================\n", "instruction: refactor main logic to print_files function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "refactor main logic to print_files function\n", "\n", "```\n", "\"\"\"Show the contents of the file store.\"\"\"\n", "\n", "import argparse\n", "\n", "from research.model_server.file_store import FileStore\n", "\n", "\n", "\n", "START REGION\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--storage_path\",\n", "        type=str,\n", "        required=True,\n", "        help=\"path to the file store\",\n", "    )\n", "    args = parser.parse_args()\n", "    storage_path = args.storage_path\n", "    store = FileStore(Path(storage_path))\n", "    docs: list[Document] = store.get_files()\n", "    for doc in docs:\n", "        print(doc.id, doc.path)\n", "        print(doc.text)\n", "\n", "END REGION\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "```\n", "\"\"\"Show the contents of the file store.\"\"\"\n", "\n", "import argparse\n", "from research.model_server.file_store import FileStore\n", "from pathlib import Path\n", "from typing import List\n", "\n", "class Document:\n", "    def __init__(self, id: str, path: str, text: str):\n", "        self.id = id\n", "        self.path = path\n", "        self.text = text\n", "\n", "START REGION\n", "def print_files(store: FileStore) -> None:\n", "    docs: List[Document] = store.get_files()\n", "    for doc in docs:\n", "        print(doc.id, doc.path)\n", "        print(doc.text)\n", "\n", "def main() -> None:\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"-storage_path\",\n", "        type=str,\n", "        required=True,\n", "        help=\"path to the file store\",\n", "    )\n", "    args = parser.parse_args()\n", "    storage_path = args.storage_path\n", "    store = FileStore(Path(storage_path))\n", "    print_files(store)\n", "\n", "END REGION\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "```\n", "I refactored the main logic into a `print_files` function, which takes a `FileStore` object as an argument. This function is then called from the `main` function, passing the `store` object as an argument.\n", "Levenshtein distance: 110\n", "\n", "============ Example 22 of 50 ============\n", "\n", "================================================================================\n", "instruction: refactor the codes in good practice\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "refactor the codes in good practice\n", "\n", "```\n", "\n", "START REGION\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the refactored code:\n", "```\n", "START REGION\n", "import os\n", "import json\n", "\n", "PATH = os.path.expanduser('~/datasets/edit/commitpackft/data.jsonl')\n", "\n", "data = []\n", "with open(PATH, 'r') as f:\n", "    data = [json.loads(line.strip()) for line in f]\n", "END REGION\n", "```\n", "Changes made:\n", "\n", "* Imported the `os` module to use `os.path.expanduser` to expand the `~` character to the user's home directory.\n", "* Used the `with` statement to ensure the file is properly closed after reading.\n", "* Used a list comprehension to create the `data` list in a more concise and efficient way.\n", "* Used the `strip()` method to remove the newline character at the end of each line, instead of slicing the string with `[:-1]`.\n", "Levenshtein distance: 124\n", "\n", "============ Example 23 of 50 ============\n", "\n", "================================================================================\n", "instruction: add missing import\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "add missing import\n", "\n", "```\n", "\n", "START REGION\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code with the missing import added:\n", "```\n", "START REGION\n", "import json\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "data = []\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "END REGION\n", "```\n", "I added the `import json` statement at the top to import the `json` module, which is required for the `json.loads()` function.\n", "Levenshtein distance: 4\n", "\n", "============ Example 24 of 50 ============\n", "\n", "================================================================================\n", "instruction: use pathlib library to open file\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "use pathlib library to open file\n", "\n", "```\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "\n", "START REGION\n", "data = []\n", "\n", "with open(PATH) as f:\n", "\n", "END REGION\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "```\n", "import pathlib\n", "import json\n", "\n", "PATH = pathlib.Path('~/datasets/edit/commitpackft/data.jsonl').expanduser()\n", "\n", "START REGION\n", "data = []\n", "with PATH.open() as f:\n", "END REGION\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "```\n", "I used the `pathlib` library to create a `Path` object from the `PATH` string, and then used the `expanduser()` method to expand the `~` character to the user's home directory. I also replaced the `open()` function with the `open()` method of the `Path` object.\n", "Levenshtein distance: 35\n", "\n", "============ Example 25 of 50 ============\n", "\n", "================================================================================\n", "instruction: make the for loop enumerate\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "make the for loop enumerate\n", "\n", "```\n", "\n", "START REGION\n", "for sample in samples:\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "\n", "END REGION\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"\\n=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for line in result:\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "```\n", "START REGION\n", "for i, sample in enumerate(samples):\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "END REGION\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"\\n=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for line in result:\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")\n", "```\n", "I added the `enumerate` function to the `for` loop, which returns both the index `i` and the value `sample` from the `samples` list.\n", "Levenshtein distance: 1\n", "\n", "============ Example 26 of 50 ============\n", "\n", "================================================================================\n", "instruction: rename real_output_dir by xyz\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "rename real_output_dir by xyz\n", "\n", "```\n", "def main(args):\n", "    \"\"\"The real main function.\"\"\"\n", "    args = parse_cmd()\n", "    output_dir = pathlib.Path(args.data_dir)\n", "    if not output_dir.exists():\n", "        raise ValueError(f\"{output_dir} does not exist.\")\n", "\n", "\n", "START REGION\n", "    real_output_dir: pathlib.Path = output_dir / (\n", "        args.target_doc_name + \"-\" + \"-\".join(args.system_name)\n", "    )\n", "    real_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    logger = prepare_to_start(real_output_dir)\n", "    logger.info(f\"Args: {args}\")\n", "    logger.info(f\"Save everything into {real_output_dir}\")\n", "\n", "END REGION\n", "\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "\n", "    augmented_docs: list[AugmentedParsedFile] = []\n", "    for doc_dict in doc_dicts:\n", "        augmented_docs.append(AugmentedParsedFile.from_dict(doc_dict))\n", "    logger.info(\n", "        f\"Load {len(doc_dicts)} documents with\"\n", "        f\" {patch_generate_lib.get_number_of_nodes(augmented_docs)} nodes from {args.source_doc_name}\"\n", "    )\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "def main(args):\n", "     \"\"\"The real main function.\"\"\"\"\n", "    args = parse_cmd()\n", "    output_dir = pathlib.Path(args.data_dir)\n", "    if not output_dir.exists():\n", "        raise ValueError(f\"{output_dir} does not exist.\")\n", "\n", "\n", "START REGION\n", "    xyz: pathlib.Path = output_dir / (\n", "        args.target_doc_name + \"-\" + \"-\".join(args.system_name)\n", "     )\n", "    xyz.mkdir(parents=True, exist_ok=True)\n", "\n", "    logger = prepare_to_start(xyz)\n", "    logger.info(f\"Args: {args}\")\n", "    logger.info(f\"Save everything into {xyz}\")\n", "END REGION\n", "\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "\n", "    augmented_docs: list[AugmentedParsedFile] = []\n", "    for doc_dict in doc_dicts:\n", "        augmented_docs.append(AugmentedParsedFile.from_dict(doc_dict))\n", "    logger.info(\n", "        f\"Load {len(doc_dicts)} documents with\"\n", "        f\" {patch_generate_lib.get_number_of_nodes(augmented_docs)} nodes from {args.source_doc_name}\"\n", "     )\n", "```\n", "\n", "I simply replaced `real_output_dir` with `xyz` in the code between the `START REGION` and `END REGION` markers.\n", "Levenshtein distance: 2\n", "\n", "============ Example 27 of 50 ============\n", "\n", "================================================================================\n", "instruction: Change log level to error.\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Change log level to error.\n", "\n", "```\n", "try:\n", "\n", "START REGION\n", "    logging.warning(\"Service A is running.\")\n", "    if isinstance(self.model.prompt_formatter, CodeLlamaChatFormatter):\n", "        bos_token_id = None\n", "        eos_token_id = self.model.tokenizer.eos_id  # type: ignore\n", "    else:\n", "        bos_token_id = self.model.tokenizer._raw_tokenizer.bos_token_id  # type: ignore\n", "        eos_token_id = self.model.tokenizer._raw_tokenizer.eos_token_id  # type: ignore\n", "except AttributeError:\n", "    logging.warning(\"Accessing special tokens failed.\")\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "try:\n", "START REGION\n", "    logging.error(\"Service A is running.\")  # Changed log level to error\n", "    if isinstance(self.model.prompt_formatter, CodeLlamaChatFormatter):\n", "        bos_token_id = None\n", "        eos_token_id = self.model.tokenizer.eos_id  # type: ignore\n", "    else:\n", "        bos_token_id = self.model.tokenizer._raw_tokenizer.bos_token_id  # type: ignore\n", "        eos_token_id = self.model.tokenizer._raw_tokenizer.eos_token_id  # type: ignore\n", "except AttributeError:\n", "    logging.error(\"Accessing special tokens failed.\")  # Changed log level to error\n", "END REGION\n", "```\n", "\n", "I changed the log level from `warning` to `error` in both logging statements.\n", "Levenshtein distance: 60\n", "\n", "============ Example 28 of 50 ============\n", "\n", "================================================================================\n", "instruction: <PERSON><PERSON><PERSON> comments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "<PERSON>mo<PERSON> comments\n", "\n", "```\n", "import logging\n", "import os\n", "import typing\n", "\n", "from openai import AsyncOpenAI, OpenAI\n", "from openai.types.chat import (\n", "    ChatCompletionAssistantMessageParam,\n", "    ChatCompletionMessageParam,\n", "    ChatCompletionSystemMessageParam,\n", "    ChatCompletionUserMessageParam,\n", ")\n", "from openai.types.chat.completion_create_params import ResponseFormat\n", "\n", "client = OpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\"))\n", "async_client = AsyncOpenAI(api_key=os.environ.get(\"OPENAI_API_KEY\"))\n", "\n", "\n", "START REGION\n", "OpenAIChatModels = typing.Literal[\n", "    \"gpt-4-1106-preview\",  # 128,000 tokens\n", "    \"gpt-4-vision-preview\",\n", "    \"gpt-3.5-turbo-1106\",  # 16,385 tokens\n", "    \"gpt-4-0613\",  # 8192 tokens\n", "    \"gpt-4-0314\",  # 8192 tokens\n", "]\n", "# ResponseFormat = typing.Literal[\"json_object\"]\n", "\n", "END REGION\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def _prepare_chat_request_argument(\n", "    messages: list[str],\n", "    system_prompt: typing.Optional[str] = None,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = \"gpt-3.5-turbo-1106\",\n", "    seed: typing.Optional[int] = None,\n", "    num_completion: int = 1,\n", "    use_json: bool = False,\n", "):\n", "    request_messages: list[ChatCompletionMessageParam] = []\n", "    if system_prompt is not None:\n", "        request_messages.append(\n", "            ChatCompletionSystemMessageParam(content=system_prompt, role=\"system\")\n", "        )\n", "    for idx, message in enumerate(messages):\n", "        if idx == 0:\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "OpenAIChatModels = typing.Literal[\n", "    \"gpt-4-1106-preview\", \n", "    \"gpt-4-vision-preview\", \n", "    \"gpt-3.5-turbo-1106\", \n", "    \"gpt-4-0613\", \n", "    \"gpt-4-0314\"\n", "]\n", "END REGION\n", "```\n", "Levenshtein distance: 6\n", "\n", "============ Example 29 of 50 ============\n", "\n", "================================================================================\n", "instruction: <PERSON><PERSON><PERSON> comments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "<PERSON>mo<PERSON> comments\n", "\n", "```\n", "            context.is_serializing):\n", "        return True\n", "    if (getattr(field_obj, 'dump_only', False) and\n", "            not context.is_serializing):\n", "        return True\n", "    if context.only and field_name not in context.only:\n", "        return True\n", "    if context.exclude and field_name in context.exclude:\n", "        return True\n", "    return False\n", "\n", "\n", "def generate_transform_method_body(schema, on_field, context):\n", "    # type: (<PERSON><PERSON><PERSON>, FieldSerializer, JitContext) -> IndentedString\n", "    \"\"\"Generates the method body for a schema and a given field serialization\n", "    strategy.\n", "    \"\"\"\n", "    body = IndentedString()\n", "    body += 'def {method_name}(obj):'.format(\n", "        method_name=on_field.__class__.__name__)\n", "\n", "START REGION\n", "    with body.indent():\n", "        if schema.dict_class is dict:\n", "            # Declaring dictionaries via `{}` is faster than `dict()` since it\n", "            # avoids the global lookup.\n", "            body += 'res = {}'\n", "        else:\n", "            # dict_class will be injected before `exec` is called.\n", "            body += 'res = dict_class()'\n", "        if not context.is_serializing:\n", "            body += '__res_get = res.get'\n", "        for field_name, field_obj in iteritems(schema.fields):\n", "            if _should_skip_field(field_name, field_obj, context):\n", "                continue\n", "\n", "END REGION\n", "\n", "            attr_name, destination = _get_attr_and_destination(context,\n", "                                                               field_name,\n", "                                                               field_obj)\n", "\n", "            result_key = ''.join(\n", "                [schema.prefix or '', destination])\n", "\n", "            field_symbol = field_symbol_name(field_name)\n", "            assignment_template = ''\n", "            value_key = '{0}'\n", "\n", "            # If we have to assume any field can be callable we always have to\n", "            # check to see if we need to invoke the method first.\n", "            # We can investigate tracing this as well.\n", "            jit_options = getattr(schema.opts, 'jit_options', {})\n", "            no_callable_fields = (jit_options.get('no_callable_fields') or\n", "                                  not context.is_serializing)\n", "            if not no_callable_fields:\n", "                assignment_template = (\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers with all comments removed:\n", "\n", "```\n", "START REGION\n", "    with body.indent():\n", "        if schema.dict_class is dict:\n", "            body += 'res = {}'\n", "        else:\n", "            body += 'res = dict_class()'\n", "        if not context.is_serializing:\n", "            body += '__res_get = res.get'\n", "        for field_name, field_obj in iteritems(schema.fields):\n", "            if _should_skip_field(field_name, field_obj, context):\n", "                continue\n", "END REGION\n", "```\n", "Levenshtein distance: 1\n", "\n", "============ Example 30 of 50 ============\n", "\n", "================================================================================\n", "instruction: <PERSON><PERSON><PERSON> comments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "<PERSON>mo<PERSON> comments\n", "\n", "```\n", "    return isWhitespace(cp) || cp === $.SOLIDUS || cp === $.GREATER_THAN_SIGN;\n", "}\n", "\n", "export interface TokenizerOptions {\n", "    sourceCodeLocationInfo?: boolean;\n", "}\n", "\n", "export interface TokenHandler {\n", "    onComment(token: CommentToken): void;\n", "    onDoctype(token: DoctypeToken): void;\n", "    onStartTag(token: TagToken): void;\n", "    onEndTag(token: TagToken): void;\n", "    onEof(token: EO<PERSON><PERSON>): void;\n", "    onCharacter(token: CharacterToken): void;\n", "    onNullCharacter(token: CharacterToken): void;\n", "    onWhitespaceCharacter(token: CharacterToken): void;\n", "\n", "    onParseError?: ParserErrorHandler | null;\n", "}\n", "\n", "\n", "START REGION\n", "//To<PERSON><PERSON>\n", "export class Tokenizer {\n", "    public preprocessor: Preprocessor;\n", "\n", "    private paused = false;\n", "    /** Ensures that the parsing loop isn't run multiple times at once. */\n", "    private inLoop = false;\n", "\n", "    /**\n", "     * Indicates that the current adjusted node exists, is not an element in the HTML namespace,\n", "     * and that it is not an integration point for either MathML or HTML.\n", "     *\n", "     * @see {@link https://html.spec.whatwg.org/multipage/parsing.html#tree-construction}\n", "     */\n", "    public inForeignNode = false;\n", "    public lastStartTagName = '';\n", "    public active = false;\n", "\n", "END REGION\n", "\n", "    public state = State.DATA;\n", "    private returnState = State.DATA;\n", "\n", "    private charRefCode = -1;\n", "\n", "    private consumedAfterSnapshot = -1;\n", "\n", "    private currentLocation: Location | null;\n", "    private currentCharacterToken: CharacterToken | null = null;\n", "    private currentToken: Token | null = null;\n", "    private currentAttr: Attribute = { name: '', value: '' };\n", "\n", "    constructor(private options: TokenizerOptions, private handler: TokenHandler) {\n", "        this.preprocessor = new Preprocessor(handler);\n", "        this.currentLocation = this.getCurrentLocation(-1);\n", "    }\n", "\n", "    //Errors\n", "    private _err(code: ERR): void {\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers with all comments removed:\n", "\n", "```\n", "START REGION\n", "export class Tokenizer {\n", "    public preprocessor: Preprocessor;\n", "\n", "    private paused = false;\n", "    private inLoop = false;\n", "\n", "    public inForeignNode = false;\n", "    public lastStartTagName = '';\n", "    public active = false;\n", "END REGION\n", "```\n", "Levenshtein distance: 1\n", "\n", "============ Example 31 of 50 ============\n", "\n", "================================================================================\n", "instruction: <PERSON><PERSON><PERSON> comments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "<PERSON>mo<PERSON> comments\n", "\n", "```\n", "            Union[torch.Tensor, Iterable[Any]]]], ) -> List[np.ndarray]:\n", "        if torch.is_tensor(tensors):\n", "            return tensors.data.cpu().numpy()\n", "        return list(map(tensors_to_arrays, tensors))\n", "\n", "    def zip_dict(\n", "            keys: Union[Iterable[Any], None],\n", "            values: Sequence[Union[Any, Sequence[Any]]],\n", "    ) -> MyDict[Text, Union[object, MyDict[Text, object]]]:\n", "        keys = keys or range(len(values))\n", "        ret = my_dict()\n", "        for idx, (key, value) in enumerate(zip(keys, values)):\n", "            is_key_list = is_tuple_or_list(key)\n", "            is_value_list = is_tuple_or_list(value)\n", "            assert is_key_list == is_value_list, 'keys and values mismatch'\n", "            if is_value_list:\n", "                ret[str(idx)] = zip_dict(key, value)\n", "            else:\n", "                ret[key] = value\n", "        return ret\n", "\n", "START REGION\n", "    torch_inputs = ensure_tuple(inputs)  # WORKAROUND: for torch.onnx\n", "    outputs = torch.onnx.export(model,\n", "                                torch_inputs,\n", "                                export_basepath + '.onnx',\n", "                                input_names=(None if input_names is None else\n", "                                             flatten_list(input_names)),\n", "                                output_names=(None if output_names is None else\n", "                                              flatten_list(output_names)),\n", "                                *args,\n", "                                **kwargs)\n", "    if outputs is None:  # WORKAROUND: for torch.onnx\n", "        training = kwargs.get('training', False)\n", "        with torch.onnx.set_training(model, training):\n", "            outputs = model(*inputs)\n", "    torch_outputs = ensure_tuple(outputs)\n", "\n", "END REGION\n", "\n", "    inputs = zip_dict(input_names, tensors_to_arrays(torch_inputs))\n", "    outputs = zip_dict(output_names, tensors_to_arrays(torch_outputs))\n", "    if use_npz:\n", "        np.savez(\n", "            export_basepath + '.npz',\n", "            inputs=inputs,\n", "            outputs=outputs,\n", "        )\n", "    else:\n", "        np.save(export_basepath + '.npy',\n", "                np.asarray(my_dict(inputs=inputs, outputs=outputs)),\n", "                allow_pickle=True)\n", "\n", "    return torch_outputs\n", "\n", "\n", "if __name__ == '__main__':\n", "    from torchvision.models import resnet18 as net\n", "\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers with comments removed:\n", "\n", "```\n", "START REGION\n", "torch_inputs = ensure_tuple(inputs)\n", "outputs = torch.onnx.export(model, torch_inputs, export_basepath + '.onnx', \n", "                            input_names=None if input_names is None else flatten_list(input_names), \n", "                            output_names=None if output_names is None else flatten_list(output_names), \n", "                            *args, **kwargs)\n", "if outputs is None:\n", "    training = kwargs.get('training', False)\n", "    with torch.onnx.set_training(model, training):\n", "        outputs = model(*inputs)\n", "torch_outputs = ensure_tuple(outputs)\n", "END REGION\n", "```\n", "Levenshtein distance: 232\n", "\n", "============ Example 32 of 50 ============\n", "\n", "================================================================================\n", "instruction: <PERSON><PERSON><PERSON> comments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "<PERSON>mo<PERSON> comments\n", "\n", "```\n", "        )\n", "        return\n", "    elif alert_type == 2:\n", "        await context.bot.send_message(\n", "            chat_id=DEV_CHAT_ID,\n", "            text=(f\"⏺ <b><u>{BOT_NAME} -  Warning</u></b>\\n\\n\" f\"{message}\"),\n", "            parse_mode=ParseMode.HTML,\n", "        )\n", "        return\n", "    elif alert_type == 3:\n", "        await context.bot.send_message(\n", "            chat_id=DEV_CHAT_ID,\n", "            text=(f\"⛔ <b><u>{BOT_NAME} -  Unauthorized Usage</u></b>\\n\\n\" f\"{message}\"),\n", "            parse_mode=ParseMode.HTML,\n", "        )\n", "        return\n", "    else:\n", "        logger.error(\"Invalid alert type provided. [Accepted: 0, 1, 2, 3]\")\n", "        return\n", "\n", "\n", "START REGION\n", "async def track_chats(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n", "    \"\"\"Tracks the chats the bot is in.\"\"\"\n", "    result = extract_status_change(update.my_chat_member)\n", "    if result is None:\n", "        return\n", "    was_member, is_member = result\n", "\n", "    # Check who is responsible for the change\n", "    cause_name = update.effective_user.full_name\n", "\n", "    # Handle chat types differently:\n", "    chat = update.effective_chat\n", "\n", "END REGION\n", "    if chat.type == Chat.PRIVATE:\n", "        if not was_member and is_member:\n", "            logger.info(\"%s started the bot\", cause_name)\n", "            context.bot_data.setdefault(\"user_ids\", set()).add(chat.id)\n", "            await alert_dev(f\"{cause_name} has started {BOT_NAME}\", 1, context)\n", "        elif was_member and not is_member:\n", "            logger.info(\"%s blocked the bot\", cause_name)\n", "            context.bot_data.setdefault(\"user_ids\", set()).discard(chat.id)\n", "            await alert_dev(f\"{cause_name} has blocked {BOT_NAME}\", 1, context)\n", "    elif chat.type in [Chat.GROUP, Chat.SUPERGROUP]:\n", "        if not was_member and is_member:\n", "            logger.info(\"%s added the bot to the group %s\", cause_name, chat.title)\n", "            context.bot_data.setdefault(\"group_ids\", set()).add(chat.id)\n", "            await alert_dev(\n", "                f\"{cause_name} has added {BOT_NAME} to group - {chat.title}\", 1, context\n", "            )\n", "        elif was_member and not is_member:\n", "            logger.info(\"%s removed the bot from the group %s\", cause_name, chat.title)\n", "            context.bot_data.setdefault(\"group_ids\", set()).discard(chat.id)\n", "            await alert_dev(\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers with all comments removed:\n", "\n", "```\n", "async def track_chats(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:\n", "    result = extract_status_change(update.my_chat_member)\n", "    if result is None:\n", "        return\n", "    was_member, is_member = result\n", "\n", "    cause_name = update.effective_user.full_name\n", "\n", "    chat = update.effective_chat\n", "\n", "    if chat.type == Chat.PRIVATE:\n", "        if not was_member and is_member:\n", "            logger.info(\"%s started the bot\" % cause_name)\n", "            context.bot_data.setdefault(\"user_ids\", set()).add(chat.id)\n", "            await alert_dev(\"%s has started %s\" % (cause_name, BOT_NAME), 1, context)\n", "        elif was_member and not is_member:\n", "            logger.info(\"%s blocked the bot\" % cause_name)\n", "            context.bot_data.setdefault(\"user_ids\", set()).discard(chat.id)\n", "            await alert_dev(\"%s has blocked %s\" % (cause_name, BOT_NAME), 1, context)\n", "    elif chat.type in [Chat.GROUP, Chat.SUPERGROUP]:\n", "        if not was_member and is_member:\n", "            logger.info(\"%s added the bot to the group %s\" % (cause_name, chat.title))\n", "            context.bot_data.setdefault(\"group_ids\", set()).add(chat.id)\n", "            await alert_dev(\"%s has added %s to group - %s\" % (cause_name, BOT_NAME, chat.title), 1, context)\n", "        elif was_member and not is_member:\n", "            logger.info(\"%s removed the bot from the group %s\" % (cause_name, chat.title))\n", "            context.bot_data.setdefault(\"group_ids\", set()).discard(chat.id)\n", "            await alert_dev(\"%s has removed %s from group - %s\" % (cause_name, BOT_NAME, chat.title), 1, context)\n", "```\n", "Levenshtein distance: 1260\n", "\n", "============ Example 33 of 50 ============\n", "\n", "================================================================================\n", "instruction: <PERSON><PERSON><PERSON> comments\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "<PERSON>mo<PERSON> comments\n", "\n", "```\n", "    ):\n", "        self.magic = magic\n", "        self.major_version = major_version\n", "        self.minor_version = minor_version\n", "        self.trailing_header_size = trailing_header_size\n", "        self.update_type = update_type\n", "        self.vaa_length = vaa_length\n", "        self.vaa = vaa\n", "        self.num_updates = num_updates\n", "        self.updates = updates\n", "\n", "    def __str__(self):\n", "        return (\n", "            f\"AccumulatorUpdate(magic={self.magic}, major_version={self.major_version}, \"\n", "            f\"minor_version={self.minor_version}, trailing_header_size={self.trailing_header_size}, \"\n", "            f\"update_type={self.update_type}, vaa_length={self.vaa_length}, vaa={self.vaa}, \"\n", "            f\"num_updates={self.num_updates}, updates={self.updates})\"\n", "        )\n", "\n", "\n", "\n", "START REGION\n", "# Referenced from https://github.com/pyth-network/pyth-crosschain/blob/110caed6be3be7885773d2f6070b143cc13fb0ee/price_service/server/src/encoding.ts#L24\n", "def encode_vaa_for_chain(vaa, vaa_format, buffer=False):\n", "    # check if vaa is already in vaa_format\n", "    if isinstance(vaa, str):\n", "        if vaa_format == DEFAULT_VAA_ENCODING:\n", "            try:\n", "                vaa_buffer = bytes.fromhex(vaa)\n", "            except ValueError:\n", "                pass  # VAA is not in hex format\n", "            else:\n", "                # VAA is in hex format, return it as it is\n", "                return vaa_buffer if buffer else vaa\n", "        else:\n", "            try:\n", "                vaa_buffer = base64.b64decode(vaa)\n", "            except binascii.Error:\n", "                pass  # VAA is not in base64 format\n", "            else:\n", "                # VAA is in base64 format, return it as it is\n", "                return vaa_buffer if buffer else vaa\n", "\n", "END REGION\n", "\n", "    # Convert VAA to the specified format\n", "    if vaa_format == DEFAULT_VAA_ENCODING:\n", "        vaa_buffer = base64.b64decode(vaa)\n", "        vaa_str = vaa_buffer.hex()\n", "    else:\n", "        vaa_buffer = bytes.fromhex(vaa)\n", "        vaa_str = base64.b64encode(vaa_buffer).decode(\"ascii\")\n", "\n", "    return vaa_buffer if buffer else vaa_str\n", "\n", "\n", "# Referenced from https://github.com/wormhole-foundation/wormhole/blob/main/sdk/js/src/vaa/wormhole.ts#L26-L56\n", "def parse_vaa(vaa, encoding):\n", "    vaa = encode_vaa_for_chain(vaa, encoding, buffer=True)\n", "\n", "    num_signers = vaa[5]\n", "    sig_length = 66\n", "    sig_start = 6\n", "    guardian_signatures = []\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers with all comments removed:\n", "\n", "START REGION\n", "def encode_vaa_for_chain(vaa, vaa_format, buffer=False):\n", "    if isinstance(vaa, str):\n", "        if vaa_format == DEFAULT_VAA_ENCODING:\n", "            try:\n", "                vaa_buffer = bytes.fromhex(vaa)\n", "            except ValueError:\n", "                pass\n", "            else:\n", "                return vaa_buffer if buffer else vaa\n", "        else:\n", "            try:\n", "                vaa_buffer = base64.b64decode(vaa)\n", "            except binascii.Error:\n", "                pass\n", "            else:\n", "                return vaa_buffer if buffer else vaa\n", "\n", "    if vaa_format == DEFAULT_VAA_ENCODING:\n", "        vaa_buffer = base64.b64decode(vaa)\n", "        vaa_str = vaa_buffer.hex()\n", "    else:\n", "        vaa_buffer = bytes.fromhex(vaa)\n", "        vaa_str = base64.b64encode(vaa_buffer).decode(\"ascii\")\n", "\n", "    return vaa_buffer if buffer else vaa_str\n", "END REGION\n", "Failed\n", "\n", "============ Example 34 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix this if\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix this if\n", "\n", "```\n", "        \"\"\"\n", "        Validates registration_metadata field.\n", "        \"\"\"\n", "        schema = create_jsonschema_from_metaschema(self.schema,\n", "                                                   required_fields=required_fields,\n", "                                                   is_reviewer=reviewer)\n", "        try:\n", "            jsonschema.validate(metadata, schema)\n", "        except jsonschema.ValidationError as e:\n", "            for page in self.schema['pages']:\n", "                for question in page['questions']:\n", "                    if e.relative_schema_path[0] == 'required':\n", "                        raise ValidationError(\n", "                            'For your registration the \\'{}\\' field is required'.format(question['title'])\n", "                        )\n", "                    elif e.relative_schema_path[0] == 'additionalProperties':\n", "                        raise ValidationError(\n", "                            'For your registration the \\'{}\\' field is extraneous and not permitted in your response.'.format(question['qid'])\n", "                        )\n", "                    elif e.relative_path[0] == question['qid']:\n", "\n", "START REGION\n", "                        if 'options' is question:\n", "                            raise ValidationError(\n", "                                'For your registration the \\'{}\\' field is invalid, your response must be one of the provided options.'.format(\n", "                                    question['title'],\n", "                                ),\n", "                            )\n", "\n", "END REGION\n", "\n", "                        raise ValidationError(\n", "                            'For your registration the \\'{}\\' field is invalid.'.format(question['title']),\n", "                        )\n", "            raise ValidationError(e.message)\n", "        except jsonschema.SchemaError as e:\n", "            raise ValidationValueError(e.message)\n", "        return\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "                        if 'options' in question:\n", "                            raise ValidationError(\n", "                                'For your registration the \\'{}\\' field is invalid, your response must be one of the following options: {}'.format(\n", "                                    question['title'], \n", "                                    ', '.join(option['label'] for option in question['options'])\n", "                                 ),\n", "                             )\n", "END REGION\n", "```\n", "\n", "I fixed the `if` statement to check if the key `'options'` is present in the `question` dictionary using the `in` operator. I also added a more informative error message that lists the allowed options.\n", "Levenshtein distance: 112\n", "\n", "============ Example 35 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix functions that do http requests\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix functions that do http requests\n", "\n", "```\n", "        if '<PERSON>' not in response.headers:\n", "            return result\n", "        while 'next' in response.headers['<PERSON>']:\n", "            if 'prev' in response.headers['Link']:\n", "                next_url = response.headers['Link'].split(', ')[2].split('; ')[0][1:-1]\n", "            else:  # First page case\n", "                next_url = response.headers['Link'].split(', ')[1].split('; ')[0][1:-1]\n", "            try:\n", "                response = requests.get(next_url, **params)\n", "            except requests.exceptions.RequestException as error:\n", "                raise WaldurClientException(str(error))\n", "\n", "            if response.status_code != 200:\n", "                error = self._parse_error(response)\n", "                raise WaldurClientException(error)\n", "\n", "            result += response.json()\n", "\n", "        return result\n", "\n", "\n", "START REGION\n", "    def _get(self, url, valid_states, **kwargs):\n", "        return self._make_request('get', url, valid_states, 1, **kwargs)\n", "\n", "    def _post(self, url, valid_states, **kwargs):\n", "        return self._make_request('get', url, valid_states, 3, **kwargs)\n", "\n", "    def _put(self, url, valid_states, **kwargs):\n", "        return self._make_request('get', url, valid_states, 3, **kwargs)\n", "\n", "    def _delete(self, url, valid_states, **kwargs):\n", "        return self._make_request('get', url, valid_states, 3, **kwargs)\n", "\n", "END REGION\n", "\n", "\n", "    def _make_get_query(self, url, query_params, get_first=False, get_few=False):\n", "        \"\"\"\n", "        Get object via Waldur API.\n", "\n", "        :param url: URL.\n", "        :param query_params: dict with query params.\n", "        :param get_first: If True then will return the first result.\n", "        :param get_few: If True then will return all results.\n", "\n", "        Note:\n", "        If get_first or get_few have been set, then multiple results are correct.\n", "        In the first case, we get the first result, in the second case we get all results.\n", "        If get_first or get_few have not been set, then multiple results are not correct.\"\"\"\n", "\n", "        result = self._get(url, valid_states=[200], params=query_params)\n", "        if not result:\n", "            message = 'Result is empty. Endpoint: %s. Query: %s' % (url, query_params)\n", "            raise ObjectDoesNotExist(message)\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "    def _get(self, url, valid_states, **kwargs):\n", "        return self._make_request('get', url, valid_states, 1, **kwargs)\n", "\n", "    def _post(self, url, valid_states, **kwargs):\n", "        return self._make_request('post', url, valid_states, 3, **kwargs)\n", "\n", "    def _put(self, url, valid_states, **kwargs):\n", "        return self._make_request('put', url, valid_states, 3, **kwargs)\n", "\n", "    def _delete(self, url, valid_states, **kwargs):\n", "        return self._make_request('delete', url, valid_states, 3, **kwargs)\n", "END REGION\n", "```\n", "\n", "I fixed the functions that do HTTP requests by changing the method in the `_make_request` call to match the method of the function. For example, `_post` now calls `_make_request` with the method `'post'`, instead of `'get'`.\n", "Levenshtein distance: 1\n", "\n", "============ Example 36 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix exception messages in this function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix exception messages in this function\n", "\n", "```\n", "class TempDir(object):\n", "    \"\"\"Create a temporary directory that gets automatically removed.  Any\n", "    object initialization parameters are passed through to `tempfile.mkdtemp`_.\n", "\n", "      >>> import Ska.File\n", "      >>> tmpdir = Ska.File.TempDir(dir='.')\n", "      >>> tmpdir.name\n", "      './tmpcCH_l-'\n", "      >>> del tmpdir\n", "\n", "    .. _tempfile.mkdtemp: http://docs.python.org/library/tempfile.html#tempfile.mkdtemp \n", "    \"\"\"\n", "    def __init__(self, *args, **kwargs):\n", "        self.__dirname = tempfile.mkdtemp(*args, **kwargs)\n", "        self.name = self.__dirname      # \"public\" attribute\n", "        \n", "    def __del__(self):\n", "        \"\"\"Remove the temp directory when the object is destroyed.\"\"\"\n", "        shutil.rmtree(self.__dirname)\n", "\n", "\n", "START REGION\n", "def get_globfiles(fileglob, minfiles=1, maxfiles=1):\n", "    \"\"\"\n", "    Get file(s) matching ``fileglob``.  If the number of matching\n", "    files is less than minfiles or more than maxfiles then an\n", "    exception is raised.\n", "\n", "    :param fileglob: Input file glob\n", "    :param minfiles: Minimum matching files (None => no minimum)\n", "    :param maxfiles: Maximum matching files (None => no maximum)\n", "    \"\"\"\n", "    files = glob.glob(fileglob)\n", "    nfiles = len(files)\n", "    if minfiles is not None and nfiles < minfiles:\n", "        raise ValueError('At least %d file(s) required for %s but %d found' % (maxfiles, nfiles, fileglob))\n", "    if maxfiles is not None and nfiles > maxfiles:\n", "        raise ValueError('No more than %d file(s) required for %s but %d found' % (minfiles, nfiles, fileglob))\n", "\n", "    return files\n", "\n", "END REGION\n", "\n", "    \n", "def relpath(path, cwd=None):\n", "    \"\"\" Find relative path from current directory to path.\n", "\n", "    Example usage:\n", "    \n", "      >>> from Ska.File import relpath\n", "      >>> relpath('/a/b/hello/there', cwd='/a/b/c/d')\n", "      '../../hello/there'\n", "      >>> relpath('/a/b/c/d/e/hello/there', cwd='/a/b/c/d')\n", "      'e/hello/there'\n", "\n", "      >>> # Special case - don't go up to root and back\n", "      >>> relpath('/x/y/hello/there', cwd='/a/b/c/d')\n", "      '/x/y/hello/there'\n", "\n", "    :param path: Destination path\n", "    :param cwd: Current directory (default: os.getcwd() )\n", "    :rtype: Relative path\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "def get_globfiles(fileglob, minfiles=1, maxfiles=1):\n", "    \"\"\"\n", "    Get file(s) matching ``fileglob``.  If the number of matching\n", "    files is less than minfiles or more than maxfiles then an\n", "    exception is raised.\n", "\n", "    :param fileglob: Input file glob\n", "    :param minfiles: Minimum matching files (None => no minimum)\n", "    :param maxfiles: Maximum matching files (None => no maximum)\n", "    \"\"\"\n", "    files = glob.glob(fileglob)\n", "    nfiles = len(files)\n", "    if minfiles is not None and nfiles < minfiles:\n", "        raise ValueError(f'At least {minfiles} file(s) required for {fileglob} but {nfiles} found')\n", "    if maxfiles is not None and nfiles > maxfiles:\n", "        raise ValueError(f'No more than {maxfiles} file(s) required for {fileglob} but {nfiles} found')\n", "\n", "    return files\n", "END REGION\n", "```\n", "\n", "I fixed the exception messages by using f-strings to format the error messages, making them more readable and accurate.\n", "Levenshtein distance: 94\n", "\n", "============ Example 37 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix the _make_request function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix the _make_request function\n", "\n", "```\n", "    def _ensure_trailing_slash(self, url):\n", "        return url if url[-1] == '/' else '%s/' % url\n", "\n", "    def _build_url(self, endpoint):\n", "        return urljoin(self.api_url, self._ensure_trailing_slash(endpoint))\n", "\n", "    def _build_resource_url(self, endpoint, uid, action=None):\n", "        parts = [endpoint, str(uid)]\n", "        if action:\n", "            parts.append(action)\n", "        return self._build_url('/'.join(parts))\n", "\n", "    def _parse_error(self, response):\n", "        try:\n", "            reason = response.json()\n", "        except ValueError:\n", "            reason = 'Unable to parse JSON'\n", "        details = 'Status: %s. Reason: %s.' % (response.status_code, reason)\n", "        return 'Server refuses to communicate. %s' % details\n", "\n", "\n", "START REGION\n", "    def _make_request(self, method, url, valid_states, retry_count=3, **kwargs):\n", "        if retry_count == 0:\n", "            raise WaldurClientException(\n", "                'Reached a limit of retries for the operation: %s %s' % (method, url)\n", "            )\n", "\n", "        params = dict(headers=self.headers)\n", "        params.update(kwargs)\n", "\n", "        try:\n", "            response = getattr(requests, method)(url, **params)\n", "        except requests.exceptions.RequestException as error:\n", "            raise WaldurClientException(str(error))\n", "\n", "        if response.status_code not in valid_states:\n", "            # a special treatment for 409 response, which can be due to async operations\n", "            if response.status_code == 409:\n", "                time.sleep(2)  # wait for things to calm down\n", "                return self._make_request(\n", "                    method, url, valid_states, retry_count - 1, **kwargs\n", "                )\n", "            error = self._parse_error(response)\n", "            raise WaldurClientException(error)\n", "\n", "        return ''\n", "\n", "END REGION\n", "\n", "\n", "    def _get_all(self, url, **kwargs):\n", "        params = dict(headers=self.headers)\n", "        params.update(kwargs)\n", "\n", "        try:\n", "            response = requests.get(url, **params)\n", "        except requests.exceptions.RequestException as error:\n", "            raise WaldurClientException(str(error))\n", "\n", "        if response.status_code != 200:\n", "            error = self._parse_error(response)\n", "            raise WaldurClientException(error)\n", "        result = response.json()\n", "        if '<PERSON>' not in response.headers:\n", "            return result\n", "        while 'next' in response.headers['<PERSON>']:\n", "            if 'prev' in response.headers['Link']:\n", "                next_url = response.headers['Link'].split(', ')[2].split('; ')[0][1:-1]\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "    def _make_request(self, method, url, valid_states, retry_count=3, **kwargs):\n", "        if retry_count == 0:\n", "            raise WaldurClientException(\n", "                 'Reached a limit of retries for the operation: %s %s' % (method, url)\n", "             )\n", "\n", "        params = dict(headers=self.headers)\n", "        params.update(kwargs)\n", "\n", "        try:\n", "            response = getattr(requests, method.lower())(url, **params)\n", "            response.raise_for_status()  # Raise an exception for bad status codes\n", "        except requests.exceptions.RequestException as error:\n", "            raise WaldurClientException(str(error))\n", "\n", "        if response.status_code not in valid_states:\n", "            # a special treatment for 409 response, which can be due to async operations\n", "            if response.status_code == 409:\n", "                time.sleep(2)   # wait for things to calm down\n", "                return self._make_request(\n", "                    method, url, valid_states, retry_count - 1, **kwargs\n", "                 )\n", "            error = self._parse_error(response)\n", "            raise WaldurClientException(error)\n", "\n", "        return response.text  # Return the response content\n", "END REGION\n", "```\n", "\n", "I made the following changes:\n", "\n", "1. Added `response.raise_for_status()` to raise an exception for bad status codes.\n", "2. Changed `getattr(requests, method)(url, **params)` to `getattr(requests, method.lower())(url, **params)` to ensure the method name is in lowercase.\n", "3. Returned `response.text` instead of an empty string.\n", "Levenshtein distance: 138\n", "\n", "============ Example 38 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix index out of range error and incorrect color and label indexing\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix index out of range error and incorrect color and label indexing\n", "\n", "```\n", "                 _graph_image_name=\"image_name1\",\n", "                 _color=[\"r\", \"y\"],\n", "                 _figsize=(9, 4),\n", "                 _enable_csv=True):\n", "        self.x_data_set = _x_data_set\n", "        self.y_data_set = _y_data_set\n", "        self.xaxis_name = _xaxis_name\n", "        self.yaxis_name = _yaxis_name\n", "        self.figsize = _figsize\n", "        self.graph_image_name = _graph_image_name\n", "        self.color = _color\n", "        self.label = _label\n", "        self.values = _values\n", "        self.enable_csv = _enable_csv\n", "        self.lf_csv = LfCSV()\n", "\n", "    def build_scatter_graph(self):\n", "        if self.color is None:\n", "            self.color = [\"orchid\", \"lime\", \"aquamarine\", \"royalblue\", \"darkgray\", \"maroon\"]\n", "        fig = plt.subplots(figsize=self.figsize)\n", "\n", "START REGION\n", "        if self.values is None:\n", "            plt.scatter(self.x_data_set, self.y_data_set[0], color=self.color[0], label=self.label[0])\n", "            if len(self.y_data_set) > 1:\n", "                for i in range(1, len(self.y_data_set) + 1):\n", "                    plt.scatter(self.x_data_set, self.y_data_set[i], color=self.color[i - 1], label=self.label[i - 1])\n", "            plt.xlabel(self.xaxis_name, fontweight='bold', fontsize=15)\n", "            plt.ylabel(self.yaxis_name, fontweight='bold', fontsize=15)\n", "            plt.gcf().autofmt_xdate()\n", "            plt.legend()\n", "\n", "END REGION\n", "\n", "        else:\n", "            colours = ListedColormap(self.color)\n", "            scatter = plt.scatter(self.x_data_set, self.y_data_set, c=self.values, cmap=colours)\n", "            plt.xlabel(self.xaxis_name, fontweight='bold', fontsize=15)\n", "            plt.ylabel(self.yaxis_name, fontweight='bold', fontsize=15)\n", "            plt.gcf().autofmt_xdate()\n", "            plt.legend(handles=scatter.legend_elements()[0], labels=self.label)\n", "        plt.savefig(\"%s.png\" % self.graph_image_name, dpi=96)\n", "        plt.close()\n", "        print(\"{}.png\".format(self.graph_image_name))\n", "        if self.enable_csv:\n", "            self.lf_csv.columns = self.label\n", "            self.lf_csv.rows = self.y_data_set\n", "            self.lf_csv.filename = f\"{self.graph_image_name}.csv\"\n", "            self.lf_csv.generate_csv()\n", "\n", "        return \"%s.png\" % self.graph_image_name\n", "\n", "\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "```\n", "START REGION\n", "        if self.values is None:\n", "            if len(self.y_data_set) > len(self.color):\n", "                self.color = self.color * (len(self.y_data_set) // len(self.color)) + self.color[:len(self.y_data_set) % len(self.color)]\n", "            if len(self.y_data_set) > len(self.label):\n", "                self.label = self.label * (len(self.y_data_set) // len(self.label)) + self.label[:len(self.y_data_set) % len(self.label)]\n", "            plt.scatter(self.x_data_set, self.y_data_set[0], color=self.color[0], label=self.label[0])\n", "            for i in range(1, len(self.y_data_set)):\n", "                plt.scatter(self.x_data_set, self.y_data_set[i], color=self.color[i], label=self.label[i])\n", "            plt.xlabel(self.xaxis_name, fontweight='bold', fontsize=15)\n", "            plt.ylabel(self.yaxis_name, fontweight='bold', fontsize=15)\n", "            plt.gcf().autofmt_xdate()\n", "            plt.legend()\n", "END REGION\n", "```\n", "I made the following changes:\n", "\n", "1. Added checks to ensure that the `color` and `label` lists are long enough to accommodate all the `y_data_set` elements. If not, I repeated the lists to match the length of `y_data_set`.\n", "2. Changed the indexing in the `for` loop to `range(1, len(self.y_data_set))` to avoid indexing out of range.\n", "3. Updated the indexing for `color` and `label` in the `plt.scatter` calls to use `i` instead of `i - 1`.\n", "Levenshtein distance: 377\n", "\n", "============ Example 39 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix get_readme_content function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix get_readme_content function\n", "\n", "```\n", "\n", "MANIFEST_CONTENT = \"\"\"\n", "include README.rst\n", "\"\"\"\n", "\n", "def get_main_code(proj_type = \"app\"):\n", "    return APP_MAIN\n", "\n", "def get_setup_code(proj_name, proj_type = \"app\"):\n", "    pdb.set_trace()\n", "    if proj_type == \"app\":\n", "        run_script_name = \"{0}-run\".format(proj_name)\n", "        return APP_SETUP.format(proj_name, run_script_name)\n", "    else:\n", "        return LIB_SETUP.format(proj_name)\n", "\n", "def get_test_code():\n", "    \"\"\" Returns a dummy test code \"\"\"\n", "    return TEST_SETUP\n", "\n", "\n", "START REGION\n", "def get_readme_content():\n", "    return MANIFEST_CONTENT\n", "\n", "END REGION\n", "\n", "\n", "def get_manifest_content():\n", "    return MANIFEST_CONTENT\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "MANIFEST_CONTENT = \"\"\" \n", "include README.rst \n", "\"\"\" \n", "def get_main_code(proj_type = \"app\"): \n", "    return APP_MAIN \n", "def get_setup_code(proj_name, proj_type = \"app\"): \n", "    pdb.set_trace() \n", "    if proj_type == \"app\": \n", "        run_script_name = \"{0}-run\".format(proj_name) \n", "        return APP_SETUP.format(proj_name, run_script_name) \n", "    else: \n", "        return LIB_SETUP.format(proj_name) \n", "def get_test_code(): \n", "     \"\"\" Returns a dummy test code \"\"\" \n", "    return TEST_SETUP \n", "\n", "\n", "START REGION \n", "def get_readme_content(proj_name): \n", "    return \"\"\" \n", "{0}\n", "=================\n", "\"\"\".format(proj_name) \n", "END REGION \n", "\n", "\n", "def get_manifest_content(): \n", "    return MANIFEST_CONTENT \n", "```\n", "\n", "I fixed the `get_readme_content` function to take a `proj_name` parameter and return a formatted string with the project name.\n", "Levenshtein distance: 598\n", "\n", "============ Example 40 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix a bug in string formatting\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix a bug in string formatting\n", "\n", "```\n", "                    #     foo = StringField(load_from='bar')\n", "                    # TestSchema().load({'foo': 'haha'}).result\n", "                    #\n", "                    # Works just fine with no errors.\n", "                    #\n", "                    # class TestSchema(Schema):\n", "                    #     foo = StringField(load_from='bar')\n", "                    # TestSchema().load({'foo': 'haha', 'bar': 'value'}).result\n", "                    #\n", "                    # Results in {'foo': 'haha'}\n", "                    #\n", "                    # Therefore, we generate code to mimic this behavior in\n", "                    # cases where `load_from` is specified.\n", "                    body += 'if \"{key}\" not in res:'.format(key=result_key)\n", "                    with body.indent():\n", "                        body += serializer.serialize(\n", "                            field_obj.load_from, field_symbol,\n", "                            assignment_template, field_obj)\n", "            if not context.is_serializing:\n", "                if field_obj.required:\n", "\n", "START REGION\n", "                    body += 'if \"{result_key}\" not in res:'.format(key=result_key)\n", "                    with body.indent():\n", "                        body += 'raise ValueError()'\n", "                if field_obj.allow_none is not True:\n", "                    body += 'if __res_get(\"{result_key}\", res) is None:'.format(\n", "                        key=result_key)\n", "\n", "END REGION\n", "\n", "                    with body.indent():\n", "                        body += 'raise ValueError()'\n", "                if (field_obj.validators or\n", "                        is_overridden(field_obj._validate,\n", "                                      fields.Field._validate)):\n", "                    body += 'if \"{key}\" in res:'.format(key=result_key)\n", "                    with body.indent():\n", "                        body += '{field_symbol}__validate(res[\"{result_key}\"])'.format(\n", "                            field_symbol=field_symbol, result_key=result_key\n", "                        )\n", "\n", "        body += 'return res'\n", "    return body\n", "\n", "\n", "def _generate_fallback_access_template(context, field_name, field_obj,\n", "                                       result_key, value_key):\n", "    field_symbol = field_symbol_name(field_name)\n", "    transform_method_name = 'serialize'\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "                body += 'if \"{0}\" not in res:'.format(result_key)\n", "                with body.indent():\n", "                    body += 'raise ValueError()'\n", "                if field_obj.allow_none is not True:\n", "                    body += 'if __res_get(\"{0}\", res) is None:'.format(result_key)\n", "END REGION\n", "```\n", "\n", "I fixed the bug in string formatting by replacing `{key}` with `{0}` in the format strings, so that the `result_key` variable is properly inserted into the string.\n", "Levenshtein distance: 52\n", "\n", "============ Example 41 of 50 ============\n", "\n", "================================================================================\n", "instruction: Wrong reason is returned, fix it\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Wrong reason is returned, fix it\n", "\n", "```\n", "    \"id\": \"ch_16ag432eZvKYlo2CGDe6lvVs\",\n", "    \"object\": \"charge\",\n", "    \"amount\": 3000,\n", "    \"amount_refunded\": 0,\n", "    \"application_fee\": None,\n", "    \"balance_transaction\": deepcopy(FAKE_BALANCE_TRANSACTION),\n", "    \"captured\": <PERSON><PERSON><PERSON>,\n", "    \"created\": 1439788903,\n", "    \"currency\": \"usd\",\n", "    \"customer\": \"cus_4UbFSo9tl62jqj\",\n", "    \"description\": None,\n", "    \"destination\": None,\n", "    \"dispute\": None,\n", "    \"failure_code\": \"expired_card\",\n", "    \"failure_message\": \"Your card has expired.\",\n", "    \"fraud_details\": {},\n", "    \"invoice\": \"in_16af5A2eZvKYlo2CJjANLL81\",\n", "    \"livemode\": <PERSON><PERSON><PERSON>,\n", "    \"metadata\": {},\n", "    \"order\": None,\n", "\n", "START REGION\n", "    \"outcome\": {\n", "        \"network_status\": \"declined_by_network\",\n", "        \"reason\": \"subscription_canceled\",\n", "        \"risk_level\": \"normal\",\n", "        \"seller_message\": \"The bank returned the decline code `expired_card`.\",\n", "        \"type\": \"issuer_declined\",\n", "    },\n", "\n", "END REGION\n", "\n", "    \"paid\": <PERSON><PERSON><PERSON>,\n", "    \"receipt_email\": None,\n", "    \"receipt_number\": None,\n", "    \"refunded\": <PERSON><PERSON><PERSON>,\n", "    \"refunds\": {\n", "        \"object\": \"list\",\n", "        \"total_count\": 0,\n", "        \"has_more\": <PERSON><PERSON><PERSON>,\n", "        \"url\": \"/v1/charges/ch_16ag432eZvKYlo2CGDe6lvVs/refunds\",\n", "        \"data\": [],\n", "    },\n", "    \"shipping\": None,\n", "    \"source\": deepcopy(FAKE_CARD_II),\n", "    \"source_transfer\": None,\n", "    \"statement_descriptor\": None,\n", "    \"status\": \"failed\",\n", "})\n", "\n", "\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "    \"id\": \"ch_16ag432eZvKYlo2CGDe6lvVs\",\n", "     \"object\": \"charge\",\n", "     \"amount\": 3000,\n", "     \"amount_refunded\": 0,\n", "     \"application_fee\": None,\n", "     \"balance_transaction\": deepcopy(FAKE_BALANCE_TRANSACTION),\n", "     \"captured\": <PERSON><PERSON><PERSON>,\n", "     \"created\": 1439788903,\n", "     \"currency\": \"usd\",\n", "     \"customer\": \"cus_4UbFSo9tl62jqj\",\n", "     \"description\": None,\n", "     \"destination\": None,\n", "     \"dispute\": None,\n", "     \"failure_code\": \"expired_card\",\n", "     \"failure_message\": \"Your card has expired.\",\n", "     \"fraud_details\": {},\n", "     \"invoice\": \"in_16af5A2eZvKYlo2CJjANLL81\",\n", "     \"livemode\": <PERSON><PERSON><PERSON>,\n", "     \"metadata\": {},\n", "     \"order\": None,\n", "\n", "START REGION\n", "     \"outcome\": {\n", "         \"network_status\": \"declined_by_network\",\n", "         \"reason\": \"expired_card\",  # Changed from \"subscription_canceled\" to \"expired_card\"\n", "         \"risk_level\": \"normal\",\n", "         \"seller_message\": \"The bank returned the decline code `expired_card`.\",\n", "         \"type\": \"issuer_declined\",\n", "     },\n", "\n", "END REGION\n", "\n", "     \"paid\": <PERSON><PERSON><PERSON>,\n", "     \"receipt_email\": None,\n", "     \"receipt_number\": None,\n", "     \"refunded\": <PERSON><PERSON><PERSON>,\n", "     \"refunds\": {\n", "         \"object\": \"list\",\n", "         \"total_count\": 0,\n", "         \"has_more\": <PERSON><PERSON><PERSON>,\n", "         \"url\": \"/v1/charges/ch_16ag432eZvKYlo2CGDe6lvVs/refunds\",\n", "         \"data\": [],\n", "     },\n", "     \"shipping\": None,\n", "     \"source\": deepcopy(FAKE_CARD_II),\n", "     \"source_transfer\": None,\n", "     \"statement_descriptor\": None,\n", "     \"status\": \"failed\",\n", "})\n", "```\n", "\n", "I changed the `reason` field in the `outcome` dictionary to `\"expired_card\"` to match the `failure_code` and `failure_message`.\n", "Levenshtein distance: 65\n", "\n", "============ Example 42 of 50 ============\n", "\n", "================================================================================\n", "instruction: Fix bug in deciding whether user was a member of a chat.\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Fix bug in deciding whether user was a member of a chat.\n", "\n", "```\n", "        or str(update.message.chat.id) == DEV_CHAT_ID\n", "    )\n", "\n", "\n", "def extract_status_change(\n", "    chat_member_update: ChatMemberUpdated,\n", ") -> Optional[Tuple[bool, bool]]:\n", "    \"\"\"\n", "    Takes a ChatMemberUpdated instance and extracts whether the 'old_chat_member' was a member\n", "    of the chat and whether the 'new_chat_member' is a member of the chat. Returns None, if\n", "    the status didn't change.\n", "    \"\"\"\n", "    status_change = chat_member_update.difference().get(\"status\")\n", "    old_is_member, new_is_member = chat_member_update.difference().get(\n", "        \"is_member\", (None, None)\n", "    )\n", "\n", "    if status_change is None:\n", "        return None\n", "\n", "\n", "START REGION\n", "    old_status, new_status = status_change\n", "    was_member = old_status in [\n", "        ChatMember.OWNER,\n", "        ChatMember.ADMINISTRATOR,\n", "    ] or (old_status == ChatMember.RESTRICTED and old_is_member is True)\n", "    is_member = new_status in [\n", "        ChatMember.MEMBER,\n", "        ChatMember.OWNER,\n", "        ChatMember.ADMINISTRATOR,\n", "    ] or (new_status == ChatMember.RESTRICTED and new_is_member is True)\n", "\n", "    return was_member, is_member\n", "\n", "END REGION\n", "\n", "\n", "\n", "async def alert_dev(message: str, alert_type: int, context: ContextTypes) -> None:\n", "    \"\"\"\n", "    Send updates, errors to the developer.\n", "    Alert Types:\n", "        0 -> <PERSON><PERSON><PERSON>\n", "        1 -> Update\n", "        2 -> Warning\n", "        3 -> Unauthorized Usage\n", "    \"\"\"\n", "    logger.info(\"Sending new update to developer - Type: %s\", alert_type)\n", "    if alert_type == 0:\n", "        await context.bot.send_message(\n", "            chat_id=DEV_CHAT_ID,\n", "            text=(f\"🔴 <b><u>{BOT_NAME} - Error Report</u></b>\\n\\n\" f\"{message}\"),\n", "            parse_mode=ParseMode.HTML,\n", "        )\n", "        return\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "    old_status, new_status = status_change\n", "    was_member = old_status in [ChatMember.MEMBER, ChatMember.OWNER, ChatMember.ADMINISTRATOR] or (old_status == ChatMember.RESTRICTED and old_is_member is True)\n", "    is_member = new_status in [ChatMember.MEMBER, ChatMember.OWNER, ChatMember.ADMINISTRATOR] or (new_status == ChatMember.RESTRICTED and new_is_member is True)\n", "\n", "    return was_member, is_member\n", "END REGION\n", "```\n", "\n", "The bug was fixed by adding `ChatMember.MEMBER` to the list of statuses that indicate the user was a member of the chat.\n", "Levenshtein distance: 63\n", "\n", "============ Example 43 of 50 ============\n", "\n", "================================================================================\n", "instruction: Replace wrong special tokens with correct ones\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "Replace wrong special tokens with correct ones\n", "\n", "```\n", "        raise NotImplementedError()\n", "\n", "    def _get_image(self, image_path: str):\n", "        image_path = os.path.join(self.data_path, image_path)\n", "        image = self.loader(image_path)\n", "        return self.transform(image)\n", "\n", "    def _get_text_segment(self, text_segment, max_len=None):\n", "        if isinstance(text_segment, str):\n", "            tokens = self.tokenizer.tokenize(text_segment)\n", "        else:\n", "            tokens = text_segment[:]\n", "        if len(tokens) == 0:\n", "            raise RuntimeError(\"The text segment should contains at least one tokens!\")\n", "        if max_len is None:\n", "            max_len = self.num_max_bpe_tokens\n", "\n", "        if len(tokens) > max_len - 2:\n", "            tokens = tokens[:max_len - 2]\n", "\n", "\n", "START REGION\n", "        tokens = [self.eos_token_id] + tokens[:] + [self.pad_token_id]\n", "        num_tokens = len(tokens)\n", "        padding_mask = [0] * num_tokens + [1] * (max_len - num_tokens)\n", "        return tokens + [self.eos_token_id] * (max_len - num_tokens), padding_mask, num_tokens\n", "\n", "END REGION\n", "\n", "\n", "    def _get_image_text_example(self, index: int, data: dict):\n", "        item = self.items[index]\n", "        img_path = item[\"image_path\"]\n", "        img = self._get_image(img_path)\n", "        data[\"image\"] = img\n", "\n", "        text_segment = item[\"text_segment\"]\n", "        language_tokens, padding_mask, _ = self._get_text_segment(text_segment)\n", "        data[\"language_tokens\"] = language_tokens\n", "        data[\"padding_mask\"] = padding_mask\n", "\n", "    def __getitem__(self, index: int):\n", "        data = dict()\n", "        self._get_image_text_example(index, data)\n", "        return data\n", "\n", "    def __len__(self) -> int:\n", "        return len(self.items)\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "        tokens = [self.bos_token_id] + tokens[:] + [self.eos_token_id]\n", "        num_tokens = len(tokens)\n", "        padding_mask = [1] * num_tokens + [0] * (max_len - num_tokens)\n", "        return tokens + [self.pad_token_id] * (max_len - num_tokens), padding_mask, num_tokens\n", "END REGION\n", "```\n", "\n", "I replaced `self.eos_token_id` with `self.bos_token_id` at the beginning of the tokens list, and `self.pad_token_id` with `self.eos_token_id` at the end of the tokens list. I also flipped the padding mask values, setting the actual tokens to 1 and the padded tokens to 0.\n", "Levenshtein distance: 3\n", "\n", "============ Example 44 of 50 ============\n", "\n", "================================================================================\n", "instruction: rename interruptCh to interruptSignalCh\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "rename interruptCh to interruptSignalCh\n", "\n", "```\n", "\t\tif err != nil {\n", "\t\t\tlog.<PERSON>rr(err).Msg(err.<PERSON><PERSON><PERSON>())\n", "\t\t}\n", "\tdefault:\n", "\t\tlog.Error().Msg(fmt.Sprintf(\"Incorrect Dev Environment: %s\\nInterrupt execution\", env))\n", "\t\tos.Exit(1)\n", "\t}\n", "\n", "\t// Set Swagger Info\n", "\tdocs.SwaggerInfo.Title = \"Swagger Example API\"\n", "\tdocs.SwaggerInfo.Description = \"Insert here REST API Description\"\n", "\tdocs.SwaggerInfo.Version = \"0.0.1\"\n", "\tdocs.SwaggerInfo.BasePath = fmt.Sprintf(\"/%s/%s\", config.ServerConfigValues.Server.ApiPath, config.ServerConfigValues.Server.ApiVersion)\n", "\tdocs.SwaggerInfo.Host = fmt.Sprintf(\"%s:%s\", config.ServerConfigValues.Server.Host, config.ServerConfigValues.Server.Port)\n", "\tdocs.SwaggerInfo.Schemes = []string{\"http\", \"https\"}\n", "\n", "\t// Connect to DB\n", "\tdb.DbOrm.ConnectToDB(config.ServerConfigValues)\n", "\tdb.DbOrm.GetDB().AutoMigrate(&models.Delegation{})\n", "\n", "\n", "START REGION\n", "\tinititalizeDb()\n", "\n", "\trwMutex := &sync.RWMutex{}\n", "\tstopOnError := false\n", "\terrorCh := make(chan error)\n", "\tinterruptCh := make(chan struct{})\n", "\n", "\tgo services.DelegationService.PollDelegations(config.ServerConfigValues.ApiDelegations.PollPeriodInSeconds, config.ServerConfigValues.ApiDelegations.Endpoint, rwMutex, stopOnError, errorCh, interruptCh)\n", "\n", "\t// Create Router\n", "\trouter := NewRouter()\n", "\n", "\tMapUrls()\n", "\n", "\n", "END REGION\n", "\t// Start Server\n", "\trouter.Run(\":\" + config.ServerConfigValues.Server.Port)\n", "}\n", "\n", "func inititalizeDb() {\n", "\n", "}\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "\n", "```\n", "START REGION\n", "\tinititalizeDb()\n", "\n", "\trwMutex := &sync.RWMutex{}\n", "\tstopOnError := false\n", "\terrorCh := make(chan error)\n", "\tinterruptSignalCh := make(chan struct{})\n", "\n", "\tgo services.DelegationService.PollDelegations(config.ServerConfigValues.ApiDelegations.PollPeriodInSeconds, config.ServerConfigValues.ApiDelegations.Endpoint, rwMutex, stopOnError, errorCh, interruptSignalCh)\n", "\n", "\t// Create Router\n", "\trouter := NewRouter()\n", "\n", "\tMapUrls()\n", "\n", "END REGION\n", "```\n", "\n", "I simply renamed `interruptCh` to `interruptSignalCh` as per your instruction.\n", "Levenshtein distance: 1\n", "\n", "============ Example 45 of 50 ============\n", "\n", "================================================================================\n", "instruction: refactor to is_accumulator_update function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "refactor to is_accumulator_update function\n", "\n", "```\n", "    return {\n", "        \"product_id\": product_id,\n", "        \"price_id\": price_id,\n", "        \"price\": str(price),\n", "        \"conf\": str(conf),\n", "        \"expo\": expo,\n", "        \"ema_price\": str(ema_price),\n", "        \"ema_conf\": str(ema_conf),\n", "        \"status\": status,\n", "        \"num_publishers\": num_publishers,\n", "        \"max_num_publishers\": max_num_publishers,\n", "        \"attestation_time\": attestation_time,\n", "        \"publish_time\": publish_time,\n", "        \"prev_publish_time\": prev_publish_time,\n", "        \"prev_price\": str(prev_price),\n", "        \"prev_conf\": str(prev_conf),\n", "        \"last_attested_publish_time\": last_attested_publish_time,\n", "    }\n", "\n", "\n", "\n", "START REGION\n", "def vaa_to_price_infos(vaa, encoding=DEFAULT_VAA_ENCODING) -> List[PriceInfo]:\n", "    if encode_vaa_for_chain(vaa, encoding, buffer=True)[:4].hex() == ACCUMULATOR_MAGIC:\n", "        return extract_price_info_from_accumulator_update(vaa, encoding)\n", "    parsed_vaa = parse_vaa(vaa, encoding)\n", "    batch_attestation = parse_batch_price_attestation(parsed_vaa[\"payload\"])\n", "\n", "END REGION\n", "    price_infos = []\n", "    for price_attestation in batch_attestation[\"price_attestations\"]:\n", "        price_infos.append(\n", "            create_price_info(\n", "                price_attestation,\n", "                vaa,\n", "                parsed_vaa[\"sequence\"],\n", "                parsed_vaa[\"emitter_chain\"],\n", "            )\n", "        )\n", "    return price_infos\n", "\n", "\n", "def vaa_to_price_info(id, vaa, encoding=DEFAULT_VAA_ENCODING) -> Optional[PriceInfo]:\n", "    \"\"\"\n", "    This function retrieves a specific PriceInfo object from a given VAA.\n", "\n", "    Parameters:\n", "    id (str): The id of the PriceInfo object to find.\n", "    vaa (str): The VAA from which to generate the PriceInfo object.\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "def is_accumulator_update(vaa, encoding=DEFAULT_VAA_ENCODING) -> bool:\n", "    return encode_vaa_for_chain(vaa, encoding, buffer=True)[:4].hex() == ACCUMULATOR_MAGIC\n", "\n", "\n", "START REGION\n", "def vaa_to_price_infos(vaa, encoding=DEFAULT_VAA_ENCODING) -> List[PriceInfo]:\n", "    if is_accumulator_update(vaa, encoding):\n", "        return extract_price_info_from_accumulator_update(vaa, encoding)\n", "    parsed_vaa = parse_vaa(vaa, encoding)\n", "    batch_attestation = parse_batch_price_attestation(parsed_vaa[\"payload\"])\n", "    price_infos = []\n", "    for price_attestation in batch_attestation[\"price_attestations\"]:\n", "        price_infos.append(\n", "            create_price_info(\n", "                price_attestation,\n", "                vaa,\n", "                parsed_vaa[\"sequence\"], \n", "                parsed_vaa[\"emitter_chain\"], \n", "             )\n", "         )\n", "    return price_infos\n", "\n", "END REGION\n", "```\n", "\n", "I refactored the code to create a new function `is_accumulator_update` that checks if the VAA is an accumulator update. This function is then used in the `vaa_to_price_infos` function to determine whether to call `extract_price_info_from_accumulator_update` or parse the VAA normally.\n", "Levenshtein distance: 577\n", "\n", "============ Example 46 of 50 ============\n", "\n", "================================================================================\n", "instruction: move code starting at get_result_hierarchy to new check_data function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "move code starting at get_result_hierarchy to new check_data function\n", "\n", "```\n", "\n", "    xmldata = etree.fromstring(data)\n", "    activity_tests = tests_by_level(test_level.ACTIVITY)\n", "\n", "    [ execute_and_record(xmldata, test) for test in activity_tests ]\n", "\n", "    transaction_tests = tests_by_level(test_level.TRANSACTION)\n", "    #print xmldata\n", "    for data in xmldata.xpath(\"//transaction\"):\n", "        [ execute_and_record(data, test) for test in transaction_tests ]\n", "\n", "    return \"Success\"\n", "\n", "def parse_xml(file_name):\n", "    try:\n", "        data = etree.parse(file_name)\n", "        return True, data\n", "    except etree.XMLSyntaxError:\n", "        return False, None\n", "\n", "\n", "START REGION\n", "def check_file(test_functions, codelists, file_name,\n", "                runtime_id, package_id):\n", "    try:\n", "        xml_parsed, data = parse_xml(file_name)\n", "\n", "        print file_name\n", "\n", "        dqprocessing.add_hardcoded_result(-3, runtime_id, package_id,\n", "                                           xml_parsed)\n", "        db.session.commit()\n", "\n", "        if not xml_parsed:\n", "            print \"XML parse failed\"\n", "            return False\n", "\n", "        def get_result_hierarchy(activity):\n", "            hierarchy = activity.get('hierarchy', default=None)\n", "            if hierarchy is \"\":\n", "                return None\n", "            return hierarchy\n", "\n", "        def run_test_activity(organisation_id, activity):\n", "            result_hierarchy = get_result_hierarchy(activity)\n", "\n", "            result_identifier = activity.find('iati-identifier').text.decode()\n", "            activity_data = etree.tostring(activity)\n", "\n", "            res = test_activity(runtime_id, package_id,\n", "                                result_identifier, result_hierarchy,\n", "                                activity_data, test_functions,\n", "                                codelists, organisation_id)\n", "            db.session.commit()\n", "\n", "        organisations = dqpackages.get_organisations_for_testing(package_id)\n", "        #TODO: Implement for each organisation.\n", "        # This is a bit crude because it only works for\n", "        # iati-activities, and not organisation files.\n", "        # But it's sufficient for now.\n", "\n", "        print \"testing ...\"\n", "\n", "        assert len(organisations) > 0\n", "        for organisation in organisations:\n", "            xp = organisation['activities_xpath']\n", "            try:\n", "                org_activities = data.xpath(xp)\n", "            except etree.XPathEvalError:\n", "                raise InvalidXPath(xp)\n", "            org_id = organisation['organisation_id']\n", "\n", "            [ run_test_activity(org_id, activity)\n", "              for activity in org_activities ]\n", "\n", "        print \"Aggregating results...\"\n", "        dqprocessing.aggregate_results(runtime_id, package_id)\n", "        print \"Finished aggregating results\"\n", "        db.session.commit()\n", "        print \"committed to db\"\n", "\n", "        run_info_results(package_id, runtime_id, data)\n", "\n", "        dqfunctions.add_test_status(package_id, 3, commit=True)\n", "        print \"added test status\"\n", "        return True\n", "    except Exception, e:\n", "        import traceback\n", "        traceback.print_exc()\n", "        print \"Exception in check_file \", e\n", "        raise\n", "\n", "END REGION\n", "\n", "def dequeue_download(body, test_functions, codelists):\n", "    try:\n", "        args = json.loads(body)\n", "        check_file(test_functions,\n", "                   codelists,\n", "                   args['filename'],\n", "                   args['runtime_id'],\n", "                   args['package_id'])\n", "    except Exception, e:\n", "        print \"Exception in dequeue_download\", e\n", "\n", "def run_test_queue():\n", "    from dqparsetests import test_functions as tf\n", "    test_functions = tf()\n", "    import dqcodelists\n", "    codelists = dqcodelists.generateCodelists()\n", "\n", "    for body in queue.handle_queue_generator(download_queue):\n", "        dequeue_download(body, test_functions, codelists)\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "xmldata = etree.fromstring(data)\n", "activity_tests = tests_by_level(test_level.ACTIVITY)\n", "\n", "[ execute_and_record(xmldata, test) for test in activity_tests ]\n", "\n", "transaction_tests = tests_by_level(test_level.TRANSACTION)\n", "#print xmldata\n", "for data in xmldata.xpath(\"//transaction\"):\n", "    [ execute_and_record(data, test) for test in transaction_tests ]\n", "\n", "return \"Success\"\n", "\n", "def parse_xml(file_name):\n", "    try:\n", "        data = etree.parse(file_name)\n", "        return True, data\n", "    except etree.XMLSyntaxError:\n", "        return False, None\n", "\n", "START REGION\n", "def check_file(test_functions, codelists, file_name,\n", "                runtime_id, package_id):\n", "    try:\n", "        xml_parsed, data = parse_xml(file_name)\n", "\n", "        print file_name\n", "\n", "        dqprocessing.add_hardcoded_result(-3, runtime_id, package_id,\n", "                                           xml_parsed)\n", "        db.session.commit()\n", "\n", "        if not xml_parsed:\n", "            print \"XML parse failed\"\n", "            return False\n", "\n", "        def check_data(activity):\n", "            result_hierarchy = activity.get('hierarchy', default=None)\n", "            if result_hierarchy is \"\":\n", "                return None\n", "            return result_hierarchy\n", "\n", "        def run_test_activity(organisation_id, activity):\n", "            result_hierarchy = check_data(activity)\n", "\n", "            result_identifier = activity.find('iati-identifier').text.decode()\n", "            activity_data = etree.tostring(activity)\n", "\n", "            res = test_activity(runtime_id, package_id,\n", "                                result_identifier, result_hierarchy,\n", "                                activity_data, test_functions,\n", "                                codelists, organisation_id)\n", "            db.session.commit()\n", "\n", "        organisations = dqpackages.get_organisations_for_testing(package_id)\n", "         #TODO: Implement for each organisation.\n", "         # This is a bit crude because it only works for\n", "         # iati-activities, and not organisation files.\n", "         # But it's sufficient for now.\n", "\n", "        print \"testing ...\"\n", "\n", "        assert len(organisations) > 0\n", "        for organisation in organisations:\n", "            xp = organisation['activities_xpath']\n", "            try:\n", "                org_activities = data.xpath(xp)\n", "            except etree.XPathEvalError:\n", "                raise InvalidXPath(xp)\n", "            org_id = organisation['organisation_id']\n", "\n", "             [ run_test_activity(org_id, activity)\n", "              for activity in org_activities ]\n", "\n", "        print \"Aggregating results...\"\n", "        dqprocessing.aggregate_results(runtime_id, package_id)\n", "        print \"Finished aggregating results\"\n", "        db.session.commit()\n", "        print \"committed to db\"\n", "\n", "        run_info_results(package_id, runtime_id, data)\n", "\n", "        dqfunctions.add_test_status(package_id, 3, commit=True)\n", "        print \"added test status\"\n", "        return True\n", "    except Exception, e:\n", "        import traceback\n", "        traceback.print_exc()\n", "        print \"Exception in check_file \", e\n", "        raise\n", "\n", "END REGION\n", "def dequeue_download(body, test_functions, codelists):\n", "    try:\n", "        args = json.loads(body)\n", "        check_file(test_functions,\n", "                   codelists,\n", "                   args['filename'], \n", "                   args['runtime_id'], \n", "                   args['package_id'])\n", "    except Exception, e:\n", "        print \"Exception in dequeue_download\", e\n", "\n", "def run_test_queue():\n", "    from dqparsetests import test_functions as tf\n", "    test_functions = tf()\n", "    import dqcodelists\n", "    codelists = dqcodelists.generateCodelists()\n", "\n", "    for body in queue.handle_queue_generator(download_queue):\n", "        dequeue_download(body, test_functions, codelists)\n", "```\n", "Levenshtein distance: 1048\n", "\n", "============ Example 47 of 50 ============\n", "\n", "================================================================================\n", "instruction: use ternary\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "use ternary\n", "\n", "```\n", "    //So, there are 3 types of character tokens in parse5:\n", "    //1)TokenType.NULL_CHARACTER - \\u0000-character sequences (e.g. '\\u0000\\u0000\\u0000')\n", "    //2)TokenType.WHITESPACE_CHARACTER - any whitespace/new-line character sequences (e.g. '\\n  \\r\\t   \\f')\n", "    //3)TokenType.CHARACTER - any character sequence which don't belong to groups 1 and 2 (e.g. 'abcdef1234@@#$%^')\n", "    private _appendCharToCurrentCharacterToken(type: CharacterToken['type'], ch: string): void {\n", "        if (this.currentCharacterToken) {\n", "            if (this.currentCharacterToken.type !== type) {\n", "                this.currentLocation = this.getCurrentLocation(0);\n", "                this._emitCurrentCharacterToken(this.currentLocation);\n", "                this.preprocessor.dropParsedChunk();\n", "            } else {\n", "                this.currentCharacterToken.chars += ch;\n", "                return;\n", "            }\n", "        }\n", "\n", "        this._createCharacterToken(type, ch);\n", "    }\n", "\n", "    private _emitCodePoint(cp: number): void {\n", "\n", "START REGION\n", "        let type = TokenType.CHARACTER;\n", "\n", "        if (isWhitespace(cp)) {\n", "            type = TokenType.WHITESPACE_CHARACTER;\n", "        } else if (cp === $.NULL) {\n", "            type = TokenType.NULL_CHARACTER;\n", "        }\n", "\n", "END REGION\n", "\n", "        this._appendCharToCurrentCharacterToken(type, String.fromCodePoint(cp));\n", "    }\n", "\n", "    //NOTE: used when we emit characters explicitly.\n", "    //This is always for non-whitespace and non-null characters, which allows us to avoid additional checks.\n", "    private _emitChars(ch: string): void {\n", "        this._appendCharToCurrentCharacterToken(TokenType.CHARACTER, ch);\n", "    }\n", "\n", "    // Character reference helpers\n", "    private _matchNamedCharacterReference(cp: number): number[] | null {\n", "        let result: number[] | null = null;\n", "        let excess = 0;\n", "        let withoutSemicolon = false;\n", "\n", "        for (let i = 0, current = htmlDecodeTree[0]; i >= 0; cp = this._consume()) {\n", "            i = determineBranch(htmlDecodeTree, current, i + 1, cp);\n", "\n", "            if (i < 0) break;\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers, using a ternary operator:\n", "\n", "```\n", "START REGION\n", "        let type = isWhitespace(cp) ? TokenType.WHITESPACE_CHARACTER : cp === $.NULL ? TokenType.NULL_CHARACTER : TokenType.CHARACTER;\n", "END REGION\n", "```\n", "\n", "This code replaces the original if-else statement with a ternary operator, which assigns the correct `TokenType` value to the `type` variable based on the conditions.\n", "Levenshtein distance: 53\n", "\n", "============ Example 48 of 50 ============\n", "\n", "================================================================================\n", "instruction: refactor try-except to get_summary_data function\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "refactor try-except to get_summary_data function\n", "\n", "```\n", "\n", "# FIXME: use organisation_total_spend when data is imported to db\n", "def get_coverage(organisation, info_results):\n", "    coverage_found = reduce(operator.add, irs, 0)\n", "    coverage_total = organisation.organisation_total_spend * 1000000\n", "\n", "    if coverage_total and coverage_found:\n", "        c = float(coverage_found) / float(coverage_total)\n", "        coverage_pct = int(c * 100)\n", "    else:\n", "        coverage_pct = None\n", "        coverage_found = None\n", "        coverage_total = None\n", "\n", "    return {\n", "        'total': coverage_total,\n", "        'found': coverage_found,\n", "        'pct': coverage_pct\n", "        }\n", "\n", "\n", "START REGION\n", "@app.route(\"/organisations/<organisation_code>/index/\")\n", "@usermanagement.perms_required('organisation', 'view')\n", "def organisations_index(organisation_code=None):\n", "\n", "    aggregation_type=integerise(request.args.get('aggregation_type', 2))\n", "\n", "    template_args = {}\n", "    org_packages = dqorganisations.organisationPackages(organisation_code)\n", "\n", "    organisation = dqorganisations.organisations(organisation_code)\n", "    packagegroups = dqorganisations.organisationPackageGroups(organisation_code)\n", "\n", "    irs = [ir for ir in get_info_results(org_packages, organisation)]\n", "    coverage = get_coverage(organisation, irs)\n", "\n", "    organisation_survey = dqsurveys.getSurvey(organisation_code)\n", "    surveydata = dqsurveys.getSurveyDataAllWorkflows(organisation_code)\n", "\n", "    surveydata, _ = get_survey_data_and_workflow(\n", "        organisation_survey, surveydata)\n", "\n", "    try:\n", "        summary_data = _organisation_indicators_summary(organisation, aggregation_type)\n", "    except Exception, e:\n", "        summary_data = None\n", "\n", "END REGION\n", "\n", "    allowed_to_view_survey = usermanagement.check_perms(\"survey\",\n", "                                          \"view\")\n", "    allowed_to_edit_survey_researcher = usermanagement.check_perms(\"survey\",\n", "                \"edit\",\n", "                {\"organisation_code\": organisation_code})\n", "\n", "    show_researcher_button = (\n", "        allowed_to_edit_survey_researcher and\n", "        organisation_survey.Workflow.name == 'researcher'\n", "        )\n", "\n", "    template_args = dict(organisation=organisation,\n", "                         summary_data=summary_data,\n", "                         packagegroups=packagegroups,\n", "                         coverage=coverage,\n", "                         surveydata=surveydata,\n", "                         admin=usermanagement.check_perms('admin'),\n", "                         loggedinuser=current_user,\n", "                         allowed_to_view_survey=allowed_to_view_survey,\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code:\n", "\n", "```\n", "@app.route(\"/organisations/<organisation_code>/index/\")\n", "@usermanagement.perms_required('organisation', 'view')\n", "def organisations_index(organisation_code=None):\n", "\n", "    aggregation_type=integerise(request.args.get('aggregation_type', 2))\n", "\n", "    template_args = {} \n", "    org_packages = dqorganisations.organisationPackages(organisation_code)\n", "\n", "    organisation = dqorganisations.organisations(organisation_code)\n", "    packagegroups = dqorganisations.organisationPackageGroups(organisation_code)\n", "\n", "    irs = [ir for ir in get_info_results(org_packages, organisation)]\n", "    coverage = get_coverage(organisation, irs)\n", "\n", "    organisation_survey = dqsurveys.getSurvey(organisation_code)\n", "    surveydata = dqsurveys.getSurveyDataAllWorkflows(organisation_code)\n", "\n", "    surveydata, _ = get_survey_data_and_workflow(\n", "        organisation_survey, surveydata)\n", "\n", "    summary_data = get_summary_data(organisation, aggregation_type)\n", "\n", "    allowed_to_view_survey = usermanagement.check_perms(\"survey\", \n", "                                           \"view\") \n", "    allowed_to_edit_survey_researcher = usermanagement.check_perms(\"survey\", \n", "                 \"edit\", \n", "                 {\"organisation_code\": organisation_code})\n", "\n", "    show_researcher_button = ( \n", "        allowed_to_edit_survey_researcher and \n", "        organisation_survey.Workflow.name == 'researcher' \n", "         )\n", "\n", "    template_args = dict(organisation=organisation, \n", "                         summary_data=summary_data, \n", "                         packagegroups=packagegroups, \n", "                         coverage=coverage, \n", "                         surveydata=surveydata, \n", "                         admin=usermanagement.check_perms('admin'), \n", "                         loggedinuser=current_user, \n", "                         allowed_to_view_survey=allowed_to_view_survey, \n", "\n", "def get_summary_data(organisation, aggregation_type):\n", "    try:\n", "        return _organisation_indicators_summary(organisation, aggregation_type)\n", "    except Exception, e:\n", "        return None\n", "```\n", "\n", "I extracted the `try-except` block into a new function called `get_summary_data`, which returns the result of `_organisation_indicators_summary` if it succeeds, and `None` if it raises an exception.\n", "Levenshtein distance: 1263\n", "\n", "============ Example 49 of 50 ============\n", "\n", "================================================================================\n", "instruction: refactor writing out added files to writeOutAddedFiles\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "refactor writing out added files to writeOutAddedFiles\n", "\n", "```\n", "    }\n", "  }\n", "\n", "  return myVector.end();\n", "}\n", "\n", "void OperationAccumulator::removeDeletedFilesFromLists(const vector<string>& removedFiles) {\n", "  for (const string& removedFile : removedFiles) {\n", "    vector<string>::const_iterator location = vectorContains(trackedFiles, removedFile);\n", "    if (location != trackedFiles.end()) {\n", "      trackedFiles.erase(location);\n", "    }\n", "\n", "    location = vectorContains(addedFiles, removedFile);\n", "    if (location != addedFiles.end()) {\n", "      addedFiles.erase(location);\n", "    }\n", "  }\n", "}\n", "\n", "\n", "START REGION\n", "void OperationAccumulator::writeOutCommit(\n", "    const string& commitMessage, const vector<string>& addedFiles,\n", "    const vector<string>& removedFiles, const vector<pair<string, FileDiff> >& diffs) {\n", "    CommitHash hash;\n", "\n", "    string newCommitDirectoryPath =\n", "      FileSystemInterface::appendPath(fileNames.at(COMMIT_DIR),\n", "\t\t\t\t      hash.toString().c_str());\n", "\n", "    createNewCommitDirectory(newCommitDirectoryPath);\n", "\n", "    ofstream output;\n", "    string newCommitFileName =\n", "      FileSystemInterface::appendPath(newCommitDirectoryPath, hash.toString().c_str());\n", "    writeBasicCommitInfo(output, newCommitFileName, hash, commitMessage);\n", "\n", "    output << \"addedFiles [\" << addedFiles.size() << \"]\\n\";\n", "    for (string addedFile : addedFiles) {\n", "      cout << \"Created file \" << addedFile << endl;\n", "      output << addedFile << \"\\n\";\n", "    }\n", "\n", "    for (const string& newFile : addedFiles) {\n", "      vector<string> directories;\n", "      FileSystemInterface::parseDirectoryStructure(newFile, directories);\n", "      FileSystemInterface::createDirectories(newCommitDirectoryPath, directories);\n", "      vector<string> fileLines;\n", "      FileParser::readFile(newFile.c_str(), fileLines);\n", "      FileWriter::writeFile(\n", "          FileSystemInterface::appendPath(newCommitDirectoryPath, newFile).c_str(),\n", "\t                                  fileLines);\n", "    }\n", "\n", "    // now write out the removed files\n", "    output << \"removedFiles [\" << removedFiles.size() << \"]\\n\";\n", "    for (string removedFile : removedFiles) {\n", "      cout << \"Removed file \" << removedFile << endl;\n", "      output << removedFile << \"\\n\";\n", "    }\n", "\n", "    // Now remove the removed files from our added/tracked file lists\n", "    removeDeletedFilesFromLists(removedFiles);\n", "\n", "    // write out which files have diffs\n", "    output << \"diffs [\" << diffs.size() << \"]\\n\";\n", "\n", "    // now write out the diffs\n", "    for (const pair<string, FileDiff>& diffInfo : diffs) {\n", "      cout << \"Updating file \" << diffInfo.first << \" with \" <<\n", "\tdiffInfo.second.getNumInsertions() << \" insertions and \" <<\n", "\tdiffInfo.second.getNumDeletions() << \" deletions\" << endl;\n", "      vector<string> directories;\n", "      FileSystemInterface::parseDirectoryStructure(diffInfo.first, directories);\n", "      FileSystemInterface::createDirectories(newCommitDirectoryPath, directories);\n", "      diffInfo.second.print(\n", "          FileSystemInterface::appendPath(newCommitDirectoryPath, diffInfo.first));\n", "    }\n", "\n", "    output.flush();\n", "    output.close();\n", "\n", "    if (initialCommitPerformed) {\n", "      // update the parent commit\n", "      updateParentCommit(hash);\n", "    }\n", "\n", "    curCommit = hash;\n", "}\n", "\n", "END REGION\n", "\n", "bool OperationAccumulator::commit(const string& commitMessage, const bool addFlag) {\n", "  vector<string> verifiedAddedFiles;\n", "\n", "  if (addFlag) {\n", "    // Make sure that files that were added haven't been deleted in the time since they've\n", "    // been added\n", "    for (const string& addedFile : addedFiles) {\n", "      if (FileSystemInterface::fileExists(addedFile.c_str())) {\n", "\tverifiedAddedFiles.push_back(addedFile);\n", "      }\n", "    }\n", "  }\n", "\n", "  // Now found out which file have been deleted, and gets the diffs for the files that have\n", "  // been changed\n", "  vector<string> removedFiles;\n", "  vector<pair<string, FileDiff> > diffs;\n", "  calculateRemovalsAndDiffs(removedFiles, diffs);\n", "\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code between the START REGION and END REGION markers:\n", "```\n", "START REGION\n", "void OperationAccumulator::writeOutCommit(\n", "    const string& commitMessage, const vector<string>& addedFiles,\n", "    const vector<string>& removedFiles, const vector<pair<string, FileDiff> >& diffs) { \n", "    CommitHash hash;\n", "\n", "    string newCommitDirectoryPath = \n", "      FileSystemInterface::appendPath(fileNames.at(COMMIT_DIR), \n", "\t\t\t\t      hash.toString().c_str());\n", "\n", "    createNewCommitDirectory(newCommitDirectoryPath);\n", "\n", "    ofstream output;\n", "    string newCommitFileName = \n", "      FileSystemInterface::appendPath(newCommitDirectoryPath, hash.toString().c_str());\n", "    writeBasicCommitInfo(output, newCommitFileName, hash, commitMessage);\n", "\n", "    output << \"addedFiles [\" << addedFiles.size() << \"]\\n\";\n", "    writeOutAddedFiles(output, addedFiles, newCommitDirectoryPath);\n", "\n", "    output << \"removedFiles [\" << removedFiles.size() << \"]\\n\";\n", "    for (string removedFile : removedFiles) {\n", "      cout << \"Removed file \" << removedFile << endl;\n", "      output << removedFile << \"\\n\";\n", "    }\n", "\n", "    // Now remove the removed files from our added/tracked file lists\n", "    removeDeletedFilesFromLists(removedFiles);\n", "\n", "    // write out which files have diffs\n", "    output << \"diffs [\" << diffs.size() << \"]\\n\";\n", "\n", "    // now write out the diffs\n", "    for (const pair<string, FileDiff>& diffInfo : diffs) {\n", "      cout << \"Updating file \" << diffInfo.first << \" with \" << \n", "\tdiffInfo.second.getNumInsertions() << \" insertions and \" << \n", "\tdiffInfo.second.getNumDeletions() << \" deletions\" << endl;\n", "      vector<string> directories;\n", "      FileSystemInterface::parseDirectoryStructure(diffInfo.first, directories);\n", "      FileSystemInterface::createDirectories(newCommitDirectoryPath, directories);\n", "      diffInfo.second.print(\n", "          FileSystemInterface::appendPath(newCommitDirectoryPath, diffInfo.first));\n", "    }\n", "\n", "    output.flush();\n", "    output.close();\n", "\n", "    if (initialCommitPerformed) { \n", "       // update the parent commit\n", "      updateParentCommit(hash);\n", "    }\n", "\n", "    curCommit = hash;\n", "}\n", "\n", "void OperationAccumulator::writeOutAddedFiles(ofstream& output, const vector<string>& addedFiles, const string& newCommitDirectoryPath) {\n", "    for (string addedFile : addedFiles) {\n", "      cout << \"Created file \" << addedFile << endl;\n", "      output << addedFile << \"\\n\";\n", "      vector<string> directories;\n", "      FileSystemInterface::parseDirectoryStructure(addedFile, directories);\n", "      FileSystemInterface::createDirectories(newCommitDirectoryPath, directories);\n", "      vector<string> fileLines;\n", "      FileParser::readFile(addedFile.c_str(), fileLines);\n", "      FileWriter::writeFile(\n", "          FileSystemInterface::appendPath(newCommitDirectoryPath, addedFile).c_str(), \n", "          fileLines);\n", "    }\n", "}\n", "END REGION\n", "```\n", "I extracted the code that writes out the added files into a separate method `writeOutAddedFiles`, which takes the `output` stream, `addedFiles` vector, and `newCommitDirectoryPath` as parameters. This method is then called from the `writeOutCommit` method.\n", "Levenshtein distance: 1630\n", "\n", "============ Example 50 of 50 ============\n", "\n", "================================================================================\n", "instruction: make all function names camel case\n", "Prompt:\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "make all function names camel case\n", "\n", "```\n", "\n", "START REGION\n", "const tauri = window.__TAURI__;\n", "const home_view = document.getElementById('home-view');\n", "const artist_view = document.getElementById('artist-view');\n", "const playlist_view = document.getElementById('playlist-view');\n", "\n", "(function () {\n", "\tinitialize();\n", "})();\n", "\n", "async function initialize() {\n", "\tawait tauri.invoke('initialize', {});\n", "}\n", "\n", "function toggleSubOptions(id) {\n", "\tconst element = document.getElementById(id + 'Options');\n", "\tif (element.style.display === 'none' || element.style.display === '') {\n", "\t\telement.style.display = 'block';\n", "\t} else {\n", "\t\telement.style.display = 'none';\n", "\t}\n", "}\n", "\n", "async function scan_music() {\n", "\ttauri.invoke('scan_music', {});\n", "\tgetLibraryArtwork();\n", "}\n", "\n", "async function getLibraryArtwork() {\n", "\n", "\t// remove current artwork\n", "\thome_view.innerHTML = '';\n", "\tartist_view.style.display = 'none';\n", "\thome_view.style.display = 'grid';\n", "\n", "\tlet artwork = await tauri.invoke('fetch', { selectQry: 'song_artwork', tableQry: 'songs', whereQry: 'song_artist', item: '%' });\n", "\n", "\tartwork = [...new Set(artwork[0])];\n", "\n", "\tartwork.forEach((dir) => {\n", "\t\tlet child = document.createElement('span');\n", "\t\tchild.classList.add('grid-cell');\n", "\t\tchild.style.backgroundImage = `url('${dir}')`; //! TODO: find some way to enable directories with spaces\n", "\t\thome_view.appendChild(child);\n", "\t});\n", "\n", "\tgetArtists();\n", "}\n", "\n", "async function getArtists() {\n", "\tconst artist_list = document.getElementById('artistsOptions');\n", "\n", "\tartist_list.innerHTML = '';\n", "\n", "\tvar artist = await tauri.invoke('fetch', { selectQry: 'song_artist', tableQry: 'songs', whereQry: 'song_artist', item: '%' });\n", "\n", "\tartist = [...new Set(artist[0])];;\n", "\n", "\tartist.forEach((song_artist) => {\n", "\t\tlet child = document.createElement('div');\n", "\t\tchild.classList.add('sidebar-item');\n", "\t\tchild.setAttribute(\"id\", \"sidebar-artist\");\n", "\t\tchild.innerText = song_artist;\n", "\t\tartist_list.append<PERSON><PERSON>d(child);\n", "\t});\n", "}\n", "\n", "document.addEventListener('click', (e) => {\n", "\tconst artist_sidebar = e.target.closest('#sidebar-artist');\n", "\n", "\tif (artist_sidebar) {\n", "\t\tfetchArtistAlbums(artist_sidebar.innerText);\n", "\t}\n", "\n", "\tconst playlist = e.target.closest('#sidebar-playlist');\n", "\n", "\tif (playlist) {\n", "\t\tplaylistView(playlist.innerText);\n", "\t}\n", "});\n", "\n", "async function fetchArtistAlbums(artist) {\n", "\tvar albums = await tauri.invoke('fetch', { selectQry: 'song_artwork', tableQry: 'songs', whereQry: 'song_artist', item: artist });\n", "\talbums = [...new Set(albums[0])];\n", "\n", "\tartist_view.innerHTML = '';\n", "\thome_view.style.display = 'none';\n", "\tartist_view.style.display = 'grid';\n", "\n", "\tfor (const key in albums) {\n", "\t\tlet child = document.createElement('span');\n", "\t\tchild.classList.add('grid-cell');\n", "\t\tchild.setAttribute('id', 'grid-cell');\n", "\t\tchild.style.backgroundImage = `url('${albums[key].trim()}')`;\n", "\t\tartist_view.append<PERSON><PERSON>d(child);\n", "\t}\n", "}\n", "\n", "async function create_playlist_button() {\n", "\tdocument.getElementById('playlist-name-box').value = '';\n", "\tdocument.getElementById('playlist-name-dialog-box').style.display = 'block';\n", "}\n", "\n", "document.getElementById('create-playlist-button').onclick = () => {\n", "\tconst playlist_name = document.getElementById('playlist-name-box').value;\n", "\tconst playlist_sidebar = document.getElementById('playlistsOptions');\n", "\n", "\tif (playlist_name == '') {\n", "\t\tdocument.getElementById('playlist-name-dialog-box').style.display = 'none';\n", "\t\treturn;\n", "\t}\n", "\n", "\tlet child = document.createElement('div');\n", "\tchild.classList.add('sidebar-item');\n", "\tchild.setAttribute('id', 'sidebar-playlist');\n", "\tchild.innerText = playlist_name;\n", "\tplaylist_sidebar.appendChild(child);\n", "\n", "\tdocument.getElementById('playlist-name-dialog-box').style.display = 'none';\n", "}\n", "\n", "async function playlistView(playlist_name) {\n", "\thome_view.style.display = 'none';\n", "\tartist_view.style.display = 'none';\n", "\tplaylist_view.style.display = 'initial';\n", "\n", "\tdocument.getElementById('playlist-name').innerText = playlist_name;\n", "}\n", "\n", "async function showPlaylistView() {\n", "\tconst playlist_view = document.getElementById('playlist-details');\n", "\tconst album_details = document.getElementById('album-details');\n", "\tconst main_view = document.getElementById('main-view');\n", "\n", "\tmain_view.innerHTML = '';\n", "\n", "\talbum_details.style.display = 'none';\n", "\tplaylist_view.style.display = 'block';\n", "}\n", "\n", "END REGION\n", "\n", "```\n", "\n", "Reply with the edited code.\n", "\n", "Full response:\n", "Here is the edited code with all function names in camel case:\n", "```\n", "START REGION\n", "const tauri = window.__TAURI__;\n", "const homeView = document.getElementById('home-view');\n", "const artistView = document.getElementById('artist-view');\n", "const playlistView = document.getElementById('playlist-view');\n", "\n", "(async function () {\n", "  initialize();\n", "})();\n", "\n", "async function initialize() {\n", "  await tauri.invoke('initialize', {});\n", "}\n", "\n", "function toggleSubOptions(id) {\n", "  const element = document.getElementById(id + 'Options');\n", "  if (element.style.display === 'none' || element.style.display === '') {\n", "    element.style.display = 'block';\n", "  } else {\n", "    element.style.display = 'none';\n", "  }\n", "}\n", "\n", "async function scanMusic() {\n", "  tauri.invoke('scan_music', {});\n", "  getLibraryArtwork();\n", "}\n", "\n", "async function getLibraryArtwork() {\n", "  // remove current artwork\n", "  homeView.innerHTML = '';\n", "  artistView.style.display = 'none';\n", "  homeView.style.display = 'grid';\n", "\n", "  let artwork = await tauri.invoke('fetch', { selectQry: 'song_artwork', tableQry: 'songs', whereQry: 'song_artist', item: '%' });\n", "\n", "  artwork = [...new Set(artwork[0])];\n", "\n", "  artwork.forEach((dir) => {\n", "    let child = document.createElement('span');\n", "    child.classList.add('grid-cell');\n", "    child.style.backgroundImage = `url('${dir}')`; //! TODO: find some way to enable directories with spaces\n", "    homeView.append<PERSON><PERSON>d(child);\n", "  });\n", "\n", "  getArtists();\n", "}\n", "\n", "async function getArtists() {\n", "  const artistList = document.getElementById('artistsOptions');\n", "\n", "  artistList.innerHTML = '';\n", "\n", "  var artist = await tauri.invoke('fetch', { selectQry: 'song_artist', tableQry: 'songs', whereQry: 'song_artist', item: '%' });\n", "\n", "  artist = [...new Set(artist[0])];;\n", "\n", "  artist.forEach((songArtist) => {\n", "    let child = document.createElement('div');\n", "    child.classList.add('sidebar-item');\n", "    child.setAttribute(\"id\", \"sidebar-artist\");\n", "    child.innerText = songArtist;\n", "    artistList.append<PERSON><PERSON><PERSON>(child);\n", "  });\n", "}\n", "\n", "document.addEventListener('click', (e) => {\n", "  const artistSidebar = e.target.closest('#sidebar-artist');\n", "\n", "  if (artistSidebar) {\n", "    fetchArtistAlbums(artistSidebar.innerText);\n", "  }\n", "\n", "  const playlist = e.target.closest('#sidebar-playlist');\n", "\n", "  if (playlist) {\n", "    playlistView(playlist.innerText);\n", "  }\n", "});\n", "\n", "async function fetchArtistAlbums(artist) {\n", "  var albums = await tauri.invoke('fetch', { selectQry: 'song_artwork', tableQry: 'songs', whereQry: 'song_artist', item: artist });\n", "  albums = [...new Set(albums[0])];\n", "\n", "  artistView.innerHTML = '';\n", "  homeView.style.display = 'none';\n", "  artistView.style.display = 'grid';\n", "\n", "  for (const key in albums) {\n", "    let child = document.createElement('span');\n", "    child.classList.add('grid-cell');\n", "    child.setAttribute('id', 'grid-cell');\n", "    child.style.backgroundImage = `url('${albums[key].trim()}')`;\n", "    artistView.append<PERSON><PERSON><PERSON>(child);\n", "  }\n", "}\n", "\n", "async function createPlaylistButton() {\n", "  document.getElementById('playlist-name-box').value = '';\n", "  document.getElementById('playlist-name-dialog-box').style.display = 'block';\n", "}\n", "\n", "document.getElementById('create-playlist-button').onclick = () => {\n", "  const playlistName = document.getElementById('playlist-name-box').value;\n", "  const playlistSidebar = document.getElementById('playlistsOptions');\n", "\n", "  if (playlistName == '') {\n", "    document.getElementById('playlist-name-dialog-box').style.display = 'none';\n", "    return;\n", "  }\n", "\n", "  let child = document.createElement('div');\n", "  child.classList.add('sidebar-item');\n", "  child.setAttribute('id', 'sidebar-playlist');\n", "  child.innerText = playlistName;\n", "  playlistSidebar.append<PERSON><PERSON>d(child);\n", "\n", "  document.getElementById('playlist-name-dialog-box').style.display = 'none';\n", "}\n", "\n", "async function playlistView(playlistName) {\n", "  homeView.style.display = 'none';\n", "  artistView.style.display = 'none';\n", "  playlistView.style.display = 'initial';\n", "\n", "  document.getElementById('playlist-name').innerText = playlistName;\n", "}\n", "\n", "async function showPlaylistView() {\n", "  const playlistView = document.getElementById('playlist-details');\n", "  const albumDetails = document.getElementById('album-details');\n", "  const mainView = document.getElementById('main-view');\n", "\n", "  mainView.innerHTML = '';\n", "\n", "  albumDetails.style.display = 'none';\n", "  playlistView.style.display = 'block';\n", "}\n", "END REGION\n", "```\n", "Levenshtein distance: 273\n"]}], "source": ["from dataclasses import dataclass\n", "import json\n", "from pathlib import Path\n", "import os\n", "import re\n", "import tqdm\n", "from typing import Optional\n", "\n", "from Levenshtein import distance\n", "\n", "from research.llm_apis.chat_utils import Llama3LlamaCppApiClient\n", "\n", "\n", "@dataclass\n", "class ProcessingOptions:\n", "    max_prefix_suffix_lines: int\n", "\n", "\n", "address = \"**************:8080\"\n", "client = Llama3LlamaCppApiClient(address=address, timeout=600)\n", "\n", "START_MARKER = \"<<<<<<<\\n\"\n", "MIDDLE_MARKER = \"=======\\n\"\n", "END_MARKER = \">>>>>>>\\n\"\n", "\n", "\n", "dataset_root = Path(os.environ[\"AUGMENT_SRC_PATH\"] + \"/research/eval/edit/data\")\n", "\n", "\n", "PROMPT_TEMPLATE = \"\"\"\\\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "{instruction}\n", "\n", "```\n", "{prefix}\n", "START REGION\n", "{middle_before}\n", "END REGION\n", "{suffix}\n", "```\n", "\n", "Reply with the edited code.\n", "\"\"\"\n", "\n", "def generate_answer(instruction, prefix, middle_before, suffix, prompt_template=PROMPT_TEMPLATE) -> tuple[Optional[str], str, str]:\n", "    prompt = prompt_template.format(\n", "        instruction=instruction,\n", "        prefix=prefix,\n", "        middle_before=middle_before,\n", "        suffix=suffix,\n", "    )\n", "\n", "    full_response = client.generate(messages=[prompt], max_tokens=4096)\n", "\n", "    match = re.search(r\"```.*START REGION\\n(.*)\\nEND REGION\\n.*```\", full_response, re.DOTALL | re.MULTILINE)\n", "    if match:\n", "        answer = match.group(1)\n", "        return answer, prompt, full_response\n", "    else:\n", "        match = re.search(\"```[^\\n]*\\n(.*)```\", full_response, re.DOTALL | re.MULTILINE)\n", "        if match:\n", "            answer = match.group(1)\n", "            return answer, prompt, full_response\n", "\n", "    return None, prompt, full_response\n", "\n", "\n", "def replay(example, prompt_template=PROMPT_TEMPLATE) -> tuple[Optional[str], str, str]:\n", "    return generate_answer(\n", "        example[\"sample\"][\"meta\"][\"instructions\"][0],\n", "        example[\"sample\"][\"prefix\"],\n", "        example[\"sample\"][\"middle_before\"],\n", "        example[\"sample\"][\"suffix\"],\n", "        prompt_template=prompt_template,\n", "    )\n", "\n", "\n", "def process_example(json_path: Path, json_data: dict, raw_text: str, options: ProcessingOptions) -> dict:\n", "    prefix, rest = raw_text.split(START_MARKER)\n", "    middle_before, rest = rest.split(MIDDLE_MARKER)\n", "    middle_after, suffix = rest.split(END_MARKER)\n", "\n", "    if options.max_prefix_suffix_lines > 0:\n", "        prefix = \"\".join(prefix.splitlines(keepends=True)[-options.max_prefix_suffix_lines :])\n", "        suffix = \"\".join(suffix.splitlines(keepends=True)[:options.max_prefix_suffix_lines])\n", "\n", "    instruction = json_data[\"instructions\"][0]\n", "\n", "    print(\"\")\n", "    print(\"=\" * 80)\n", "    print(\"instruction:\", instruction)\n", "\n", "    maybe_answer, prompt, full_response = generate_answer(instruction, prefix, middle_before, suffix=suffix)\n", "\n", "    example = {\n", "        \"path\": str(json_path),\n", "        \"prompt\": prompt,\n", "        \"full_response\": full_response,\n", "        \"sample\": {\n", "            \"prefix\": prefix,\n", "            \"middle_before\": middle_before,\n", "            \"middle_after\": middle_after,\n", "            \"suffix\": suffix,\n", "            \"meta\": json_data,\n", "        }\n", "    }\n", "\n", "    print(\"Prompt:\")\n", "    print(prompt)\n", "\n", "    print(\"Full response:\")\n", "    print(full_response)\n", "\n", "    if maybe_answer:\n", "        answer = maybe_answer\n", "        lev_dist = distance(answer, middle_after)\n", "        print(\"Levenshtein distance:\", lev_dist)\n", "\n", "        example.update({\n", "            \"completed\": True,\n", "            \"answer\": answer,\n", "            \"levenshtein_distance\": lev_dist,\n", "        })\n", "    else:\n", "        print(\"Failed\")\n", "        example.update({\n", "            \"completed\": <PERSON><PERSON><PERSON>,\n", "        })\n", "\n", "    return example\n", "\n", "\n", "def load_examples(output_path):\n", "    return json.loads(output_path.read_text(\"utf8\"))\n", "\n", "\n", "def process_examples(options: ProcessingOptions, output_path):\n", "    examples = []\n", "\n", "    if output_path.exists():\n", "        examples = load_examples(output_path)\n", "\n", "    paths = list(dataset_root.rglob(\"*.json\"))\n", "\n", "    print(f\"Skipping {len(examples)} completed examples\")\n", "    paths = paths[len(examples) :]\n", "\n", "    for i, json_path in enumerate(paths):\n", "        print(f\"\\n============ Example {i+1} of {len(paths)} ============\")\n", "        data = json.loads(json_path.read_text(\"utf8\"))\n", "\n", "        text_path = json_path.with_suffix(\".txt\")\n", "        raw_text = text_path.read_text(\"utf8\")\n", "\n", "        example = process_example(json_path, data, raw_text, options)\n", "\n", "        examples.append(example)\n", "        output_path.write_text(json.dumps(examples, indent=2), encoding=\"utf8\")\n", "\n", "\n", "options = ProcessingOptions(\n", "    max_prefix_suffix_lines=20,\n", ")\n", "output_path = Path(f\"/mnt/efs/augment/user/guy/code-instructions-with-llama3-maxPrefSuff{options.max_prefix_suffix_lines}.json\")\n", "process_examples(options, output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Look at examples\n", "import difflib\n", "\n", "def show_example(example):\n", "    print(\"\")\n", "    print(\"=\" * 80)\n", "    print(\"Complete:\", example[\"completed\"])\n", "    if example[\"completed\"]:\n", "        print(\"Distance:\", example[\"levenshtein_distance\"])\n", "    print(\"Instruction:\", example[\"sample\"][\"meta\"][\"instructions\"][0])\n", "    # print(\"Original code:\\n\\n\", example[\"sample\"][\"middle_before\"])\n", "    print(\"Expected answer:\\n\\n\", example[\"sample\"][\"middle_after\"])\n", "    if example[\"completed\"]:\n", "        print(\"Model answer:\\n\\n\", example[\"answer\"])\n", "        diff = difflib.unified_diff(\n", "            example[\"sample\"][\"middle_after\"].splitlines(keepends=True),\n", "            example[\"answer\"].splitlines(keepends=True),\n", "            fromfile=\"before\",\n", "            tofile=\"after\",\n", "        )\n", "        print(\"Diff:\\n\")\n", "        print(\"\".join(diff))\n", "\n", "i = 4\n", "example = examples[i]\n", "show_example(example)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "with Path(\"/mnt/efs/augment/user/guy/code-instructions-with-llama3.json\").open(\"r\") as f:\n", "    examples = json.load(f)\n", "\n", "distances = [e[\"levenshtein_distance\"] for e in examples if e[\"completed\"]]\n", "print(distances)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try a custom prompt on a specific example\n", "\n", "custom_prompt_template = \"\"\"\\\n", "Edit the code between the START REGION and END REGION markers according to the following instruction:\n", "{instruction}\n", "\n", "```\n", "{prefix}\n", "START REGION\n", "{middle_before}\n", "END REGION\n", "{suffix}\n", "```\n", "\n", "Reply with the edited code. Only make changes that follow directly from the instruction. Do not make other changes to the code.\n", "Do not change the style of the code except as described in the instruction.\n", "\"\"\"\n", "\n", "example = examples[1]\n", "maybe_answer, prompt, full_response = replay(example, custom_prompt_template)\n", "\n", "print(maybe_answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}