{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Compare Mistral 7B and Mixtral 8x7B weights and see how similar they are."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import safetensors as st\n", "import torch\n", "\n", "mistral_path = \"/mnt/efs/augment/checkpoints/mistral/Mistral-7B-v0.1\"\n", "mixtral_path = \"/mnt/efs/augment/checkpoints/mistral/Mixtral-8x7B-v0.1\"\n", "\n", "def read_index(model_path: str) -> dict:\n", "    with open(f\"{model_path}/model.safetensors.index.json\", \"r\") as f:\n", "        return json.load(f)\n", "\n", "def get_tensor_names(model_path: str) -> list[str]:\n", "    index = read_index(model_path)\n", "    return list(index[\"weight_map\"].keys())\n", "\n", "def get_tensor(model_path: str, name: str):\n", "    index = read_index(model_path)\n", "    tensor_path = model_path + \"/\" + index[\"weight_map\"][name]\n", "    tensors = st.safe_open(tensor_path, framework=\"pt\")\n", "    return tensors.get_tensor(name)\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Mistral:\")\n", "for name in get_tensor_names(mistral_path):\n", "    print(name)\n", "\n", "print(\"\\nMixtral:\")\n", "for name in get_tensor_names(mixtral_path):\n", "    print(name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Mixtral:\")\n", "for name in get_tensor_names(mistral_path):\n", "    if \"layers.1.\" in name:\n", "        print(name)\n", "\n", "print(\"\\nMistral:\")\n", "for name in get_tensor_names(mixtral_path):\n", "    if \"layers.1.\" in name:\n", "        print(name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "# sns.set_style(\"whitegrid\")\n", "\n", "tensor_pairs = [\n", "    (\"down_proj\", \"w2\"),\n", "    (\"gate_proj\", \"w1\"),\n", "    (\"up_proj\", \"w3\"),\n", "]\n", "\n", "# experts = range(8)\n", "# layers = range(0, 22+1)\n", "\n", "expert_idx = 2\n", "layer_idx = 1\n", "similarities = []\n", "\n", "for dense_name, expert_name in tensor_pairs:\n", "    print(dense_name, expert_name, \"...\")\n", "    base_tensor = get_tensor(mistral_path, f\"model.layers.{layer_idx}.mlp.{dense_name}.weight\").float()\n", "    expert_tensor = get_tensor(mixtral_path, f\"model.layers.{layer_idx}.block_sparse_moe.experts.{expert_idx}.{expert_name}.weight\").float()\n", "    assert base_tensor.shape == expert_tensor.shape\n", "\n", "    flat_base_tensor = base_tensor.reshape([-1])\n", "    flat_expert_tensor = expert_tensor.reshape([-1])\n", "\n", "    sim = torch.nn.functional.cosine_similarity(flat_base_tensor, flat_expert_tensor, dim=0).item()\n", "    print(f\"cosine sim: {sim}\")\n", "    similarities.append(sim)\n", "\n", "    dist = torch.linalg.norm(flat_base_tensor - flat_expert_tensor) / torch.linalg.norm(flat_base_tensor)\n", "    print(f\"dist: {dist}  base norm: {torch.linalg.norm(flat_base_tensor)}  expert norm: {torch.linalg.norm(flat_expert_tensor)}\")\n", "\n", "    # rank_of_diff = torch.linalg.matrix_rank((base_tensor - expert_tensor).to(torch.float32))\n", "    # print(rank_of_diff)\n", "\n", "print(\"cosine similarities:\", similarities)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Scratch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["help(torch.linalg.norm)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.linalg.matrix_rank(l18e0w1.to(torch.float32))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base_tensor = \n", "torch.linalg.matrix_rank((l18e0w1-l18e1w1).to(torch.float32))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}