#!/bin/bash

MODEL_FILE=/mnt/efs/augment/checkpoints/llama3/gguf/Meta-Llama-3-70B-Instruct-GGUF/Meta-Llama-3-70B-Instruct.Q4_K_M.gguf

FLAGS="--host 0.0.0.0 -ngl 1000 --ctx-size 8192 --model $MODEL_FILE"

# One server
#CUDA_VISIBLE_DEVICES=0,1 ./server $FLAGS

# Four servers
#CUDA_VISIBLE_DEVICES=0,1 ./server $FLAGS --port 8080 &
#sleep 20
#CUDA_VISIBLE_DEVICES=2,3 ./server $FLAGS --port 8081 &
#sleep 20
#CUDA_VISIBLE_DEVICES=4,5 ./server $FLAGS --port 8082 &
#sleep 20
#CUDA_VISIBLE_DEVICES=6,7 ./server $FLAGS --port 8083 &

# Eight servers
CUDA_VISIBLE_DEVICES=0 ./server $FLAGS --port 8080 &
sleep 15
CUDA_VISIBLE_DEVICES=1 ./server $FLAGS --port 8081 &
sleep 15
CUDA_VISIBLE_DEVICES=2 ./server $FLAGS --port 8082 &
sleep 15
CUDA_VISIBLE_DEVICES=3 ./server $FLAGS --port 8083 &
sleep 15
CUDA_VISIBLE_DEVICES=4 ./server $FLAGS --port 8084 &
sleep 15
CUDA_VISIBLE_DEVICES=5 ./server $FLAGS --port 8085 &
sleep 15
CUDA_VISIBLE_DEVICES=6 ./server $FLAGS --port 8086 &
sleep 15
CUDA_VISIBLE_DEVICES=7 ./server $FLAGS --port 8087 &

wait
