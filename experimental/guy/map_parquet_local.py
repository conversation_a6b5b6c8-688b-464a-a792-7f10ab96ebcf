"""A local version of apply_pandas for debugging.

It's a draft version, not fully debugged, so use with care.
"""

from pathlib import Path

from research.data.spark.pipelines.utils.map_parquet import *


def apply_pandas_local(
    pandas_func: PandasFuncType,
    input_path: Union[str, Path, Sequence[str], Sequence[Path]],
    output_path: Union[str, Path],
):
    """Apply a function to a set of parquet files.

    Processes files serially, and meant to be used for debugging. Writes the
    resulting data to parquet files. Only supports locally mounted filesystem.

    Args:
        pandas_func (Callable): Function that takes a pandas df and returns a
            DataFrame. Other types are not supported.
        input_path: Path to directory with parquet files, or a list of parquet files.
            Must be on a locally mounted filesystem.
        output_path: Path to write output parquet file.
            Must be on a locally mounted filesystem.
    """
    print("in apply_pandas_local")
    output_path = Path(output_path)

    # if the input is a list, we assume it is a list of files; other wise assume a
    # directory and we need to identify all the files
    input_files: list[Path] = []
    if not isinstance(input_path, (str, Path)) and isinstance(input_path, Iterable):
        input_files.extend(Path(path) for path in input_path)
    else:
        input_files.extend(Path(input_path).glob("*.parquet"))

    print(f"Found {len(input_files)} files to process.")

    for input_file in input_files:
        print("in loop")
        output_file = output_path / input_file.name
        print(f"map_parquet: Processing {input_file} to {output_file}")
        df = pd.read_parquet(input_file)
        result = pandas_func(df)
        if result is None:
            continue
        if isinstance(result, pd.DataFrame):
            result.to_parquet(str(output_file))
        else:
            raise ValueError(
                f"Result type {type(result)} not supported, only DataFrame is supported."
            )
