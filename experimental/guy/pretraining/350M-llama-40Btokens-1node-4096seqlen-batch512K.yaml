# Pretrain a 350M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
#- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/350M-model-def.yml

determined:
  name: arun-350M-llama-pretrain-40Btokens-seqlen4096-1node-batch512K-lr3.0e-4  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["350M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "4xA100.yaml"
  gpu_count: 4
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  load: null
  tokenizer_type: StarCoderTokenizer
  dataset_type: direct
  shuffle_direct_dataset: True

  # data_path: "/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096/dataset"
  data_path: null
  train_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/dataset"]
  valid_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
  data_impl: mmap

  wandb_name: arun-350M-llama-pretrain-40Btokens-seqlen4096-1node-batch512K-lr3.0e-4  # pragma: allowlist secret
  wandb_group: llama-starcoder-pretraining
  wandb_project: guy-pretrain

  # GPT-3 reference hyperparameters:
  # Batch size: 0.5M tokens.
  # LR: 3e-4 --> 3e-5 with cosine annealing.
  # Warmup: 2k iters (from StarCoder; GPT-3 was 0.1)
  # Batch size: 0.5M tokens.

  # 512k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 1
  train_batch_size: 128   # 4 * 32

  # 80k steps @ 512k tokens ~ 40B tokens
  lr_decay_iters: 82000
  train_iters: 82000
  warmup: 0.0243902439 # 1B tokens ~ 2k iters

  save_interval: 5000  # Don't save too often

  # Optimization
  min_lr: 3.0e-5
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 3.0e-4
    type: Adam
