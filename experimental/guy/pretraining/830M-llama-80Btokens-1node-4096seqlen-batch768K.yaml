# Pretrain a 830M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
#- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml

determined:
  name: guy-830M-llama-pretrain-80Btokens-seqlen4096-1node-batch512K  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  load: null
  tokenizer_type: StarCoderTokenizer
  dataset_type: direct
  shuffle_direct_dataset: True

  # data_path: "/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096/dataset"
  data_path: null
  train_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/dataset"]
  valid_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
  data_impl: mmap

  wandb_name: arun-830M-llama-pretrain-80Btokens-seqlen4096-1node-batch512K  # pragma: allowlist secret
  wandb_group: llama-starcoder-pretraining
  wandb_project: guy-pretrain

  # 768k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 24
  gradient_accumulation_steps: 1
  train_batch_size: 192   # 24 * 8

  # 80B tokens ~ 768k tokens @ 160k steps
  lr_decay_iters: 108_000
  train_iters: 108_000
  warmup: 0.0125  # ~1B ~= 1.3k iters

  save_interval: 5000  # Don't save too often

  # Optimization: GPT3-LR * 1.5 (for linear scaling)
  min_lr: 3.75e-5
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 3.75e-4  # GPT-3 LR
    type: Adam
