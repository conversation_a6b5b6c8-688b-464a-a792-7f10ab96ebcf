# Pretrain a 350M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
#- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/350M-model-def.yml

determined:
  name: guy-350M-llama-pretrain-40Btokens-seqlen4096-1node-batch512K  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["350M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  load: null
  tokenizer_type: StarCoderTokenizer
  dataset_type: direct
  shuffle_direct_dataset: True

  # data_path: "/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096/dataset"
  data_path: null
  train_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/dataset"]
  valid_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
  test_data_paths: ["/mnt/efs/augment/user/guy/data-pipeline/starcoder/exported_dataset_4096_split/validation_dataset"]
  data_impl: mmap

  wandb_name: arun-350M-llama-pretrain-40Btokens-seqlen4096-1node-batch1024K-lr1.2e-3  # pragma: allowlist secret
  wandb_group: llama-starcoder-pretraining
  wandb_project: guy-pretrain

  # 1M tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 1
  train_batch_size: 256   # 8 * 32

  # 40B tokens ~ 40k steps @ 1024k tokens
  lr_decay_iters: 41000
  train_iters: 41000
  warmup: 0.0243902439 # 1k iters

  save_interval: 5000  # Don't save too often

  # Optimization: 2x GPT-3 LR
  min_lr: 1.2e-4
  optimizer:
    params:
      betas:
      - 0.9
      - 0.95
      eps: 1.0e-08
      lr: 1.2e-3  # GPT-3 LR
    type: Adam
