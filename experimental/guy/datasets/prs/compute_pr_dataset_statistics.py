import json
from pathlib import Path

import pandas

root = Path("/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha/")

num_rows = []
file_sizes = []
succeeded_files = []
failed_files = []

for parquet_file in root.glob("*.parquet"):
    try:
        df = pandas.read_parquet(parquet_file)
        num_rows.append(len(df))
        file_sizes.append(parquet_file.stat().st_size)
        succeeded_files.append(str(parquet_file))
    except Exception as exc:
        print(f"Failed to read {parquet_file}: {exc}")
        failed_files.append(str(parquet_file))

    results = {
        "num_rows": num_rows,
        "file_sizes": file_sizes,
        "succeeded_files": succeeded_files,
        "failed_files": failed_files,
    }

    Path("/mnt/efs/augment/user/guy/pr_dataset_statistics.json").write_text(
        json.dumps(results, indent=2), encoding="utf8"
    )
