spark:
  app_name: "ProcessTheStack"
  log_level: WARN
  master_url: "spark://guy-dev-data-processing-lga1:7077"

  # total-mem = executor_instances * (executor_mem + executor_mem_overhead)
  #   + driver_mem + driver_mem_overhead

  # spark.executor.memory: "30g"
  # spark.executor.memoryOverhead: "18g"
  # spark.executor.instances: "4"

  # spark.executor.memory: "20g"
  # spark.executor.memoryOverhead: "12g"
  # spark.executor.instances: "6"

  spark.executor.memory: "10g"
  spark.executor.memoryOverhead: "6g"
  spark.executor.instances: "12"

  spark.executor.cores: "5"

  # spark.driver.memory: "22g"
  spark.driver.memory: "20g"
  spark.driver.memoryOverhead: "6g"
  spark.driver.maxResultSize: "2g"
  spark.sql.debug.maxToStringFields: "4096"
  spark.serializer: "org.apache.spark.serializer.KryoSerializer"
  spark.sql.files.maxPartitionBytes: "128m"
  # This is trying to solve a memory problem with tokenizing the whole dataset
  spark.sql.parquet.enableVectorizedReader: false
  spark.sql.parquet.columnarReaderBatchSize: "2048" # default 4096
