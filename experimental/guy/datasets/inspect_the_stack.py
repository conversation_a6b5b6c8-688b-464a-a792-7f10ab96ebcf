"""Compute statistics on files in The Stack."""

import argparse
import pickle
from pathlib import Path

import numpy as np
import pandas as pd
from megatron.tokenizer.tokenizer import CodeGenTokenizer


def analyze_statistics(path):
    with Path(path).open("rb") as file:
        stats = pickle.load(file)
    rows = []
    for lang, data in stats.items():
        row = {
            "language": lang,
            "avg_chars": np.mean(data["char_counts"]),
            "avg_tokens": np.mean(data["token_counts"]),
            "avg_lines": np.mean(data["line_counts"]),
            "std_chars": np.std(data["char_counts"]),
            "std_tokens": np.std(data["token_counts"]),
            "std_lines": np.std(data["line_counts"]),
            "max_chars": np.max(data["char_counts"]),
            "max_tokens": np.max(data["token_counts"]),
            "max_lines": np.max(data["line_counts"]),
        }
        rows.append(row)

    df = pd.DataFrame(rows)
    df["chars_per_token"] = df["avg_chars"] / df["avg_tokens"]
    df["tokens_per_line"] = df["avg_tokens"] / df["avg_lines"]
    print("NOTE: Unless specified, numbers are per source file")
    print(df)


def main():
    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--dataset_path",
        type=str,
        default="/mnt/efs/augment-las1/data/raw/the-stack-dedup.2022-11-19/data",
        help="path to The Stack data",
    )
    parser.add_argument(
        "--num_data_files_per_language",
        type=int,
        default=10,
        help="number of files to process per language",
    )
    parser.add_argument(
        "--num_rows_per_data_file",
        type=int,
        default=2000,
        help="number of rows (i.e. source files) to process per data file",
    )
    parser.add_argument(
        "--statistics_output",
        type=str,
        default="the_stack_statistics.pickle",
        help="path to store the collected statistics",
    )
    parser.add_argument(
        "--analyze",
        action="store_true",
        help="just analyze the stored statistics",
    )

    args = parser.parse_args()

    if args.analyze:
        analyze_statistics(args.statistics_output)
        return 0

    codegen_tokenizer = CodeGenTokenizer()
    tokenizer = codegen_tokenizer.tokenizer
    statistics_per_language = {}

    for language_path in Path(args.dataset_path).glob("*"):
        language = language_path.name
        print("Processing language:", language)

        char_counts = []
        token_counts = []
        line_counts = []

        for i, data_file in enumerate(sorted(language_path.glob("*.parquet"))):
            print("Processing file:", data_file)
            if i >= args.num_data_files_per_language:
                break
            df = pd.read_parquet(data_file)

            for j, (_, row) in enumerate(df.iterrows()):
                if j > args.num_rows_per_data_file:
                    break
                content = row["content"]
                char_counts.append(len(content))

                num_lines = len(content.split("\n"))
                line_counts.append(num_lines)

                tokens = tokenizer.encode(content)
                token_counts.append(len(tokens))

        statistics_per_language[language] = {
            "token_counts": token_counts,
            "char_counts": char_counts,
            "line_counts": line_counts,
        }

        with Path(args.statistics_output).open("wb") as file:
            pickle.dump(statistics_per_language, file)
            # analyze_statistics(args.statistics_output)


if __name__ == "__main__":
    main()
