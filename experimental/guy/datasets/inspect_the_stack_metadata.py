"""Compute statistics on repository metadata in The Stack.

The latest version of The Stack includes stars/forks/issues metadata.
"""

import argparse
import bisect
import sqlite3
from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns

sns.set_style("whitegrid")

# Everything except 'content', which is the actual file content that takes
# up about 85% of the memory.
COLUMNS = [
    "hexsha",
    "size",
    "ext",
    "lang",
    "max_stars_repo_path",
    "max_stars_repo_name",
    "max_stars_repo_head_hexsha",
    "max_stars_repo_licenses",
    "max_stars_count",
    "max_stars_repo_stars_event_min_datetime",
    "max_stars_repo_stars_event_max_datetime",
    "max_issues_repo_path",
    "max_issues_repo_name",
    "max_issues_repo_head_hexsha",
    "max_issues_repo_licenses",
    "max_issues_count",
    "max_issues_repo_issues_event_min_datetime",
    "max_issues_repo_issues_event_max_datetime",
    "max_forks_repo_path",
    "max_forks_repo_name",
    "max_forks_repo_head_hexsha",
    "max_forks_repo_licenses",
    "max_forks_count",
    "max_forks_repo_forks_event_min_datetime",
    "max_forks_repo_forks_event_max_datetime",
    "avg_line_length",
    "max_line_length",
    "alphanum_fraction",
]

# A useful subset of languages
LANGUAGES = ["C", "C++", "Go", "Java", "JavaScript", "Python"]

ATTRIBUTES = ["forks", "stars", "issues"]
METADATA_FILENAME = "/mnt/efs/augment/user/guy/the_stack_metadata.pickle"


def plot_log_hist(ax, data, nbins=40):
    """Plot a log histogram."""
    data = np.array(data)
    logbins = np.geomspace(data.min(), data.max(), nbins)
    ax.hist(data, bins=logbins)
    ax.set_xscale("log")


def generate_dataset_files(args):
    """Iterate over the dataset files."""
    for i, data_file in enumerate(sorted(Path(args.dataset).glob("*.parquet"))):
        if args.num_files and i >= args.num_files:
            return
        yield data_file


def generate_dataset_dataframes(args, columns):
    """Iterate over the dataset files in DataFrame form."""
    for data_file in generate_dataset_files(args):
        print(f"Loading {data_file}...")
        df = pd.read_parquet(data_file, columns=columns)
        if args.num_rows:
            df = df.head(args.num_rows)
        yield df


def sample_repos(args):
    """Sample and show a few random repos."""
    np.random.seed(args.seed)
    attribute_columns = [f"max_{attribute}_count" for attribute in ATTRIBUTES]
    columns = [args.repo_name_column] + attribute_columns
    for df in generate_dataset_dataframes(args, columns):
        df = df[
            ~df[attribute_columns[0]].isna()
            | ~df[attribute_columns[1]].isna()
            | ~df[attribute_columns[2]].isna()
        ]
        for _ in range(args.sample_repos):
            i = np.random.randint(0, len(df))
            print(df.iloc[i])
        break


def convert_to_sqlite(args):
    """Convert The Stack from parquet files to sqlite."""
    db_file = args.convert_to_sqlite
    table_name = "the_stack"
    if Path(db_file).exists():
        raise ValueError(f"db file {db_file} already exists")
    conn = sqlite3.connect(db_file)

    print(f"Converting to sqlite database {db_file} in table {table_name}...")
    for parquet_file in generate_dataset_files(args):
        print(f"Processing {parquet_file}")
        data_frame = pd.read_parquet(parquet_file, columns=COLUMNS)
        data_frame.to_sql(table_name, conn, if_exists="append", index=False)
        if args.small_test:
            print("NOTE: Test run, skipping remaining files")
            break

    cur = conn.cursor()

    print("Creating the index...")
    cur.execute(
        """
CREATE INDEX stars_and_lang_index
ON the_stack(max_stars_repo_name, lang)
"""
    )

    print("Creating statistics tables...")

    # Repo statistics grouping by max_stars_repo and lang
    cur.execute(
        """
CREATE TABLE repo_and_lang_statistics AS
SELECT
    max_stars_repo_name,
    lang,
    sum(size) AS size,
    count(1) AS count,
    max(max_stars_count) AS max_stars_count,
    max(max_forks_count) AS max_forks_count,
    max(max_issues_count) AS max_issues_count
FROM the_stack
GROUP BY max_stars_repo_name, lang;
"""
    )

    # Repo statistics after filtering by language
    cur.execute(
        """
CREATE TABLE repo_statistics AS
SELECT
    max_stars_repo_name,
    sum(size) AS size,
    sum(count) AS count,
    max(max_stars_count) AS max_stars_count,
    max(max_forks_count) AS max_forks_count,
    max(max_issues_count) AS max_issues_count
FROM repo_and_lang_statistics
WHERE lang in ("C", "C++", "Java", "Go", "Python")
GROUP BY max_stars_repo_name;
"""
    )

    conn.commit()
    conn.close()


def _analyze_hand_labeled_repos(conn):
    with Path("the_stack/hand_labeled_repos.csv").open("r", encoding="utf8") as file:
        repos = []
        labels = []
        for line in file:
            repo, label = line.strip().split(",")
            repos.append(repo)
            labels.append(label)
        repos_str = ", ".join(f'"{r}"' for r in repos)
        df = pd.read_sql_query(
            f"""
SELECT max_stars_repo_name, size, max_stars_count
FROM repo_statistics
WHERE max_stars_repo_name in ({repos_str})
""",
            conn,
        )

        new_df = pd.DataFrame()
        for r in repos:
            new_df = pd.concat([new_df, df[df["max_stars_repo_name"] == r]])
        new_df["quality"] = labels
        print(new_df.sort_values("size", ascending=False))
        print("")
        print(new_df.sort_values("max_stars_count", ascending=True))


def size_to_str(size):
    """Return a human-readable size string."""
    if size >= 1e12:
        return f"{np.round(size/1e12, 1)}T"
    if size >= 1e9:
        return f"{np.round(size/1e9, 1)}G"
    if size >= 1e6:
        return f"{np.round(size/1e6, 1)}M"
    if size >= 1e3:
        return f"{np.round(size/1e3, 1)}K"
    return f"{size}"


def _analyze_per_language_statistics_old(conn):
    """Older analysis of per-language statistics.

    Used hand-chosen thresholds.
    """

    def find_thresholds(values, sorted_fractions, target_fractions):
        threshold_values = []
        for target in target_fractions:
            index = bisect.bisect_left(sorted_fractions, target)
            threshold_values.append(values[index])
        return threshold_values

    per_lang_thresholds_df = pd.DataFrame(
        columns=["lang", "threshold_type", "threshold", "size_percentile"]
    )
    target_fractions = [0.25, 0.33, 0.5, 0.66, 0.75]

    def process_lang(lang, field, size_query, per_lang_thresholds_df):
        df = pd.read_sql_query(size_query, conn)
        sorted_fractions = df["size"].cumsum() / sum(df["size"])

        thresholds = find_thresholds(df[field], sorted_fractions, target_fractions)

        for threshold, target in zip(thresholds, target_fractions):
            per_lang_thresholds_df = per_lang_thresholds_df.append(
                {
                    "lang": lang,
                    "threshold_type": field,
                    "threshold": size_to_str(threshold),
                    "size_percentile": 100 * target,
                },
                ignore_index=True,
            )

        return per_lang_thresholds_df

    for lang in LANGUAGES:
        stars_query = f"""
SELECT max_stars_count, size FROM repo_and_lang_statistics
WHERE lang = "{lang}" ORDER BY max_stars_count ASC
"""
        per_lang_thresholds_df = process_lang(
            lang, "max_stars_count", stars_query, per_lang_thresholds_df
        )

        size_query = f"""
SELECT size FROM repo_and_lang_statistics
WHERE lang = "{lang}" ORDER BY size ASC
"""

        per_lang_thresholds_df = process_lang(
            lang, "size", size_query, per_lang_thresholds_df
        )

    print(per_lang_thresholds_df.to_string())

    # Early analysis with hard-coded star/size thresholds


#     def count_and_size(lang, threshold_str=None):
#         query = f"""
# SELECT COUNT(*) AS count, SUM(size) AS size FROM repo_and_lang_statistics
# WHERE lang = "{lang}"
# """
#         if threshold_str:
#             query += f" AND {threshold_str}"
#         result = pd.read_sql_query(query, conn)
#         count = result["count"].iloc[0]
#         size = result["size"].iloc[0]
#         assert count is not None
#         assert size is not None
#         return count, size

#     star_thresholds = [1, 2, 5, 10, 25, 100, 1000]
#     size_thresholds = [1e4, 1e5, 5e5, 1e6, 5e6, 1e7]

#     per_lang_df = pd.DataFrame(columns=[
#         "lang", "threshold_type", "threshold", "count", "size",
#         "percent_of_repos", "percent_of_size"
#     ])

#     for lang in LANGUAGES:
#         total_count, total_size = count_and_size(lang)

#         for field, threshold_type, thresholds in zip(
#             ["max_stars_count", "size"],
#             ["stars", "size"],
#             [star_thresholds, size_thresholds]
#             ):

#             for threshold in thresholds:
#                 count, size = count_and_size(
#                     lang, f"{field} >= {threshold}")
#                 per_lang_df = per_lang_df.append({
#                     "lang": lang,
#                     "threshold_type": threshold_type,
#                     "threshold": int(threshold),
#                     "count": size_to_str(count),
#                     "size": size_to_str(size),
#                     "percent_of_repos": np.round(100*count/total_count, 1),
#                     "percent_of_size": np.round(100*size/total_size, 1),
#                 }, ignore_index=True)
#     print(per_lang_df.to_string())


def _sample_good_repos(conn, stats_dfs):
    sampled_good_repos = pd.DataFrame()
    stars_fraction = 0.8
    size_fraction = 0.5

    for lang in LANGUAGES:
        index = bisect.bisect_left(
            stats_dfs[lang]["max_stars_count"]["sorted_size_fractions"], stars_fraction
        )
        stars_threshold = stats_dfs[lang]["max_stars_count"].iloc[index][
            "max_stars_count"
        ]

        index = bisect.bisect_left(
            stats_dfs[lang]["size"]["sorted_size_fractions"], size_fraction
        )
        size_threshold = stats_dfs[lang]["size"].iloc[index]["size"]

        # print("stars_threshold:", stars_threshold)
        # print("size_threshold:", size_threshold)

        df = pd.read_sql_query(
            f"""
            SELECT lang, max_stars_repo_name, max_stars_count, size
            FROM repo_and_lang_statistics
            WHERE
                lang = "{lang}" AND
                max_stars_count >= {stars_threshold} AND
                size >= {size_threshold}
            ORDER BY RANDOM()
            LIMIT 2
            """,
            conn,
        )
        sampled_good_repos = pd.concat([sampled_good_repos, df], ignore_index=True)

    print("\nGood index:")
    print(sampled_good_repos.to_string(index=False))


def _analyze_per_language_statistics(conn):
    """Analyze statistics per programming language."""
    nrows = 2
    ncols = len(LANGUAGES) // nrows

    queries = {
        "max_stars_count": lambda lang: f"""
            SELECT max_stars_count, size FROM repo_and_lang_statistics
            WHERE lang = "{lang}" ORDER BY max_stars_count ASC
            """,
        "size": lambda lang: f"""
            SELECT size FROM repo_and_lang_statistics
            WHERE lang = "{lang}" ORDER BY size ASC
            """,
    }

    def find_value_at_threshold(fraction_series, value_series, threshold):
        """Find the value when the fraction causes the threshold."""
        index = bisect.bisect_left(fraction_series, threshold)
        return value_series.iloc[index]

    def process_lang(lang, field, size_axes, count_axes, size_query):
        """Process and plot the statistics of a language.

        Plots the cumulative size (in bytes) as a function of the threshold
        we place on the field.

        Args:
            lang: Programming language
            field: Which field we're sorting by
            size_axes: Where to plot size-based fractions
                (will plot on all of them)
            count_axes: Where to plot count-based fractions
            size_query: SQL query that sorts the size by the chosen field
        """
        df = pd.read_sql_query(size_query, conn)
        df["sorted_size_fractions"] = df["size"].cumsum() / sum(df["size"])
        df["ones"] = 1
        df["sorted_count_fractions"] = df["ones"].cumsum() / sum(df["ones"])
        sampled_repos = pd.DataFrame()

        for ax in size_axes:
            ax.plot(df[field], df["sorted_size_fractions"], "-", label=lang)
            ax.set_xscale("log")
            ax.set_xlabel(f"{field} <= x-value")
            ax.set_title(lang)
            ax.set_ylabel("fraction of cumulative size")

        for ax in count_axes:
            ax.plot(df[field], df["sorted_count_fractions"], "-", label=lang)
            ax.set_xscale("log")
            ax.set_xlabel(f"{field} <= x-value")
            ax.set_title(lang)
            ax.set_ylabel("fraction of repos")

        # Sample repositories
        lo_value = find_value_at_threshold(df["sorted_size_fractions"], df[field], 0.33)
        hi_value = find_value_at_threshold(df["sorted_size_fractions"], df[field], 0.66)

        if not np.isnan(lo_value):
            lo_df = pd.read_sql_query(
                f"""
                SELECT max_stars_repo_name, max_stars_count, size
                FROM repo_and_lang_statistics
                WHERE lang = "{lang}" AND
                ({field} <= {lo_value} OR {field} IS NULL)
                ORDER BY RANDOM()
                LIMIT 1
                """,
                conn,
            )
            lo_df["lang"] = lang
            lo_df["field_name"] = field
            lo_df["threshold"] = lo_value
            lo_df["lo_or_hi"] = "lo"
            sampled_repos = pd.concat([sampled_repos, lo_df], ignore_index=True)

        if not np.isnan(hi_value):
            hi_df = pd.read_sql_query(
                f"""
                SELECT max_stars_repo_name, max_stars_count, size
                FROM repo_and_lang_statistics
                WHERE lang = "{lang}" AND {field} >= {hi_value}
                ORDER BY RANDOM()
                LIMIT 1
                """,
                conn,
            )
            hi_df["lang"] = lang
            hi_df["field_name"] = field
            hi_df["threshold"] = hi_value
            hi_df["lo_or_hi"] = "hi"
            sampled_repos = pd.concat([sampled_repos, hi_df], ignore_index=True)

        # print(f"lang={lang} field={field} lo_value={lo_value} hi_value={hi_value}")
        # print(sampled_repos.to_string())
        return df, sampled_repos

    all_sampled_repos = pd.DataFrame()
    stats_dfs = {lang: {} for lang in LANGUAGES}
    thresholds_df = pd.DataFrame()

    for field, query_fn in queries.items():
        size_fig, size_axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(12, 8))
        count_fig, count_axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(12, 8))
        summary_fig, summary_ax = plt.subplots(figsize=(4, 4))

        for i, lang in enumerate(LANGUAGES):
            query = query_fn(lang)
            size_ax = size_axes[i // ncols][i % ncols]
            size_ax.set_yticks(np.arange(0, 1.1, 0.1))
            count_ax = count_axes[i // ncols][i % ncols]
            count_ax.set_yticks(np.arange(0, 1.1, 0.1))

            stats_df, sampled_repos = process_lang(
                lang, field, [size_ax, summary_ax], [count_ax], query
            )

            for threshold in np.arange(0.1, 1.1, 0.1):
                for fraction_type in ["size", "count"]:
                    value = find_value_at_threshold(
                        stats_df[f"sorted_{fraction_type}_fractions"],
                        stats_df[field],
                        threshold,
                    )
                    row = pd.DataFrame(
                        [
                            {
                                "lang": lang,
                                "field": field,
                                "fraction_type": fraction_type,
                                "fraction_threshold": threshold,
                                "field_value": value,
                            }
                        ]
                    )
                    thresholds_df = pd.concat([thresholds_df, row], ignore_index=True)

            stats_dfs[lang][field] = stats_df
            del stats_df
            all_sampled_repos = pd.concat([all_sampled_repos, sampled_repos])
            if i == 0:
                size_ax.set_title(f"{field}: {lang}")
                count_ax.set_title(f"{field}: {lang}")

        summary_ax.set_yticks(np.arange(0, 1.1, 0.1))
        summary_ax.set_title(f"{field} summary")
        summary_ax.legend()
        size_fig.tight_layout()
        size_fig.savefig(f"{field}_size_percentiles.png", dpi=256)
        count_fig.tight_layout()
        count_fig.savefig(f"{field}_count_percentiles.png", dpi=256)
        summary_fig.tight_layout()
        summary_fig.savefig(f"{field}_size_percentiles_summary.png", dpi=256)

    thresholds_df.to_csv("thresholds.csv", sep="\t", index=False)
    print("Thresholds saved to thresholds.csv")

    print("\nall_sampled_repos:")
    print(all_sampled_repos.to_string(index=False))
    _sample_good_repos(conn, stats_dfs)


def _analyze_metadata_correlations(conn):
    """Covariance matrix between stars, forks, issues, size, count."""
    df = pd.read_sql_query(
        """
SELECT size, count, max_stars_count, max_forks_count, max_issues_count
FROM repo_statistics;
""",
        conn,
    )

    print("\nCorrelation matrix:")
    print(df.corr())

    print("\nCorrelation matrix (on log10 values):")
    print(np.log10(df).corr())


def _analyze_typical_repos(conn):
    """Typical repos at various star thresholds."""
    star_df = pd.DataFrame(columns=["star_threshold", "max_stars_repo_name"])
    for star_threshold in (0, 1, 1e1, 1e2, 1e3, 1e4, 1e5):
        star_df = pd.concat(
            [
                star_df,
                pd.read_sql_query(
                    f"""
SELECT {star_threshold} as star_threshold, max_stars_repo_name FROM repo_statistics
WHERE max_stars_count > {star_threshold}
ORDER BY RANDOM()
LIMIT 2;
""",
                    conn,
                ),
            ],
            ignore_index=True,
        )
    print("\nTypical repos at various star thresholds:")
    print(star_df)

    # Typical repos at various size thresholds
    size_df = pd.DataFrame(columns=["size_threshold", "max_stars_repo_name"])
    for size_threshold in (0, 1e3, 1e4, 1e5, 1e6, 1e7, 1e8, 1e9):
        size_df = pd.concat(
            [
                size_df,
                pd.read_sql_query(
                    f"""
SELECT {size_threshold} as size_threshold, max_stars_repo_name FROM repo_statistics
WHERE size > {size_threshold}
ORDER BY RANDOM()
LIMIT 2;
""",
                    conn,
                ),
            ],
            ignore_index=True,
        )
    print("\nTypical repos at various size thresholds:")
    print(size_df)


def analyze_sqlite(args):
    """Analyze metadata with sqlite."""
    conn = sqlite3.connect(args.analyze_sqlite)

    # _analyze_hand_labeled_repos(conn)
    _analyze_per_language_statistics(conn)
    # _analyze_per_language_statistics_old(conn)
    # _analyze_metadata_correlations(conn)
    # _analyze_typical_repos(conn)

    conn.close()


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dataset",
        type=str,
        default="/mnt/efs/igor-temp/the-stack-dedup.2023-02-04.03",
        help="path to the dataset (should contain parquet files)",
    )
    parser.add_argument(
        "--num_files", type=int, default=None, help="number of files to process"
    )
    parser.add_argument(
        "--num_rows", type=int, default=None, help="number of rows to process per file"
    )
    parser.add_argument("--bins", type=int, default=50, help="number of histogram bins")
    parser.add_argument("--seed", type=int, default=42, help="random seed")
    parser.add_argument(
        "--repo_name_column",
        type=str,
        default="max_forks_repo_name",
        help="column to use as the repository name",
    )
    parser.add_argument(
        "--sample_repos",
        type=int,
        help="show data for this many random repos",
    )
    parser.add_argument(
        "--convert_to_sqlite",
        type=str,
        help=(
            "convert the dataset to sqlite at the given path, "
            "keeping just the metadata and dropping the file content. "
            "output drive should be fast for best performance."
        ),
    )
    parser.add_argument(
        "--analyze_sqlite",
        type=str,
        help=(
            "analyze metadata using sqlite database. "
            "for best performance, keep sqlite database on a fast drive"
        ),
    )
    parser.add_argument(
        "--small_test",
        action="store_true",
        help="create sqlite database with a small fraction of the data",
    )
    args = parser.parse_args()

    if args.sample_repos:
        sample_repos(args)
    if args.convert_to_sqlite:
        convert_to_sqlite(args)
    if args.analyze_sqlite:
        analyze_sqlite(args)


if __name__ == "__main__":
    main()
