"""Find the max content length by looking at The Stack parquet files."""

from pathlib import Path

import pyarrow.parquet as pq


def human_readable_size(size):
    """Returns human readable string."""
    suffixes = ["B", "KB", "MB", "GB", "TB"]
    factor = 1024

    for suffix in suffixes:
        if size < factor:
            break
        size /= factor

    return f"{size:.2f} {suffix}"


def main():
    the_stack_root = Path(
        "/mnt/efs/augment-lga1/data/raw/the-stack-dedup.2023-02-04/data"
    )

    max_so_far = 0
    # max_path = None

    with Path("content_sizes.tsv").open("w", encoding="utf8") as output:
        for path in the_stack_root.rglob("*.parquet"):
            parquet_file = pq.ParquetFile(path)
            schema = parquet_file.schema
            column_names = schema.names
            column_name = "content"
            column_index = column_names.index(column_name)
            # column_data = parquet_file.read_column(column_index)
            column_data = parquet_file.read_row_group(0).column(column_index)

            # TODO can actually do this just using the "size" column
            max_size = max(len(str(element)) for element in column_data)
            output.write(f"{path}\t{max_size}\n")

            if max_size > max_so_far:
                print(f"{path}\t{max_size}\t{human_readable_size(max_size)}")
                max_so_far = max_size
                # max_path = path


if __name__ == "__main__":
    main()
