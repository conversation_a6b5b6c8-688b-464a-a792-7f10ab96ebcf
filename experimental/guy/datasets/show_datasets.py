"""Explore existing datasets."""

from pathlib import Path

from megatron.data.data_utils import build_train_valid_test_data_iterators
from megatron.initialize import initialize_megatron
from megatron.neox_arguments import NeoXArgs


def show_tokenizer(neox_args):
    """Show the tokenizer in action."""
    tokenizer = neox_args.tokenizer
    print("Tokenizer type:", type(tokenizer))

    print(tokenizer.vocab["[SEP]"])
    print(tokenizer.vocab["[EOS]"])

    # token_ids = tokenizer.tokenizer("suffix [SEP] prefix middle [EOS]")["input_ids"]
    token_ids = tokenizer.tokenizer("suffix[SEP]prefix middle[EOS]")["input_ids"]
    print(token_ids)

    inv_vocab = {v: k for k, v in tokenizer.vocab.items()}

    print([inv_vocab[id] for id in token_ids])


def show_datasets(neox_args):
    """Show a few samples from the loaded datasets."""
    (
        train_data_iterator,
        valid_data_loader,
        test_data_iterator,
    ) = build_train_valid_test_data_iterators(neox_args=neox_args)

    del valid_data_loader
    del test_data_iterator

    # before_file = Path("before.txt").open("w", encoding="utf8")
    after_file = Path("after.txt").open("w", encoding="utf8")

    print("")
    print("=" * 120)
    print("")

    for i, batch in enumerate(train_data_iterator):
        # print(f"{batch=}")
        tokens = batch["text"][0]
        # orig_tokens = batch["untransformed_text"][0]

        decoded = neox_args.tokenizer.detokenize(tokens)
        # orig_decoded = neox_args.tokenizer.detokenize(orig_tokens)
        print("*** BEFORE ***")
        print(decoded)
        print("-" * 120)
        # print("*** AFTER ***")
        # print(orig_decoded)
        # print("-" * 120)

        sample_str = f"\nSAMPLE {i}\n\n"
        # before_file.write(sample_str)
        # before_file.write(orig_decoded)
        after_file.write(sample_str)
        after_file.write(decoded)

        if i > 20:
            break


def main():
    """Main."""
    neox_args = NeoXArgs.consume_neox_args()
    neox_args.configure_distributed_args()
    # tokenizer needs to be build in training in order to set the padding vocab
    neox_args.build_tokenizer()
    # is initialized if tensorboard directory is defined
    neox_args.initialize_tensorboard_writer()
    initialize_megatron(neox_args=neox_args)
    # show_tokenizer(neox_args)
    show_datasets(neox_args)


if __name__ == "__main__":
    main()
