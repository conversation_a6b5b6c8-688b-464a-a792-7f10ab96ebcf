"""Show the content statistics of a dataset using Spark."""

import argparse
import os
import sys
from pathlib import Path

import pandas

sys.path.append(os.environ["HOME"])
sys.path.append(f"{os.environ['HOME']}/augment/research/data/spark/pipelines")

import pyspark.sql.functions as F

from research.data.spark.pipelines.pipeline import load_config, setup_spark
from research.data.spark.pipelines.stages.common import human_readable, load_dataset


def show_content_statistics(df, spark):
    """Print out and save basic statistics about the content."""
    spark.sparkContext.setJobDescription("Show content statistics")

    char_count_thresholds = [1e2, 1e3, 3e3, 1e4, 3e4, 1e5, 1e6, 1e7, 1e8, 1e9]

    def get_thresh_str(thresh: float) -> str:
        return f"{thresh:.0e}".replace("+0", "")

    # Collect all the queries first, so that spark can do it in a single pass
    queries = []

    # Total size and file count
    queries.extend(
        [
            F.sum(F.col("size")).alias("sum_size"),
            F.count(F.col("size")).alias("count"),
        ]
    )

    # Total size under each thereshold
    queries.extend(
        [
            F.sum(F.when(F.col("size") <= thresh, F.col("size"))).alias(
                f"size_under_{get_thresh_str(thresh)}"
            )
            for thresh in char_count_thresholds
        ]
    )

    # File count under each threshold
    queries.extend(
        [
            F.count(F.when(F.col("size") <= thresh, F.col("size"))).alias(
                f"count_under_{get_thresh_str(thresh)}"
            )
            for thresh in char_count_thresholds
        ]
    )

    # Run the query and collect the results
    df = df.select(*queries)
    data = df.collect()[0].asDict()

    # Convert results to pandas, print and save
    results = []

    for thresh in char_count_thresholds:
        size_under = data[f"size_under_{get_thresh_str(thresh)}"]
        count_under = data[f"count_under_{get_thresh_str(thresh)}"]
        results.append(
            {
                "threshold": thresh,
                "human_thresh": human_readable(thresh, factor=1000),
                "size_under": human_readable(size_under, factor=1000),
                "size_frac_under": size_under / data["sum_size"],
                "count_under": human_readable(count_under, in_bytes=False, factor=1000),
                "count_frac_under": count_under / data["count"],
            }
        )

    pdf = pandas.DataFrame(results)
    print(pdf.to_string(float_format="{:.2f}".format))

    pdf.to_csv("dataset_content_statistics.csv", index=False)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dataset",
        default=Path(
            "/mnt/efs/augment-lga1-nvme/data/raw/the-stack-dedup.2023-02-04/data"
        ),
        help="the dataset to analyze",
    )
    parser.add_argument(
        "--spark_config",
        default=Path("spark_config.yml"),
        help="the spark config yaml file",
    )
    parser.add_argument(
        "--skip_langs",
        default="",
        help="comma-separated list of languages to skip, e.g. text,csv (case sensitive!)",
    )
    parser.add_argument(
        "--py_file",
        action="append",
        help=".py or .zip file to add to pyspark (can provide multiple ones)",
    )
    args = parser.parse_args()

    config = load_config(args.spark_config)
    spark = setup_spark(config.spark, args.py_file)
    df = load_dataset(spark, args.dataset)

    if args.skip_langs:
        skip_langs = args.skip_langps.split(",")
        print("Skipping langs:", skip_langs)
        df = df.filter(~F.col("lang").isin(skip_langs))

    # StarCoder doesn't have "size" pre-computed
    if "size" not in df.columns:
        df = df.withColumn("size", F.length(df["content"]))

    show_content_statistics(df, spark)


if __name__ == "__main__":
    main()
