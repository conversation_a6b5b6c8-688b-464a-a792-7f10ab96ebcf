"""Inspect generated stars/size quality column for The Stack."""

import pyarrow
import pyarrow.dataset

for i in range(10):
    filename = f"/mnt/efs/igor-temp/user/rich/the-stack.08/part-{i}.parquet"
    dataset = pyarrow.dataset.dataset(filename, format="parquet")

    print(f"\n{filename}:")

    df = dataset.to_table().to_pandas()
    view = df.groupby("star_quality").aggregate({"star_quality": "count"})
    print(view)

    view = df.groupby("size_quality").aggregate({"size_quality": "count"})
    print(view)
