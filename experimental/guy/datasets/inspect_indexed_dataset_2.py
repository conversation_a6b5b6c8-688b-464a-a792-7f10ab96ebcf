"""Inspect an IndexedDataset.

Prints a few items from the given dataset.
"""

import argparse
from types import SimpleNamespace

from megatron.data import indexed_dataset
from megatron.tokenizer import build_tokenizer


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dataset", type=str, required=True, help="indexed dataset path"
    )
    args = parser.parse_args()

    more_args = SimpleNamespace(
        output_prefix="doc",
        dataset_impl="mmap",
        rank=0,
        tokenizer_type="CodeGenTokenizer",
        make_vocab_size_divisible_by=128,
        model_parallel_size=1,
    )

    tokenizer = build_tokenizer(more_args)

    dataset = indexed_dataset.make_dataset(args.dataset, "infer")

    for item in dataset[:10]:
        print(item)
        print(len(item))
        print(tokenizer.detokenize(item))


if __name__ == "__main__":
    main()
