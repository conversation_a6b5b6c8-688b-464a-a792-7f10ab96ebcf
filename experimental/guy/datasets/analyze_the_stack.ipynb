{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 6 languages:\n", "['c++', 'javascript', 'c', 'python', 'go', 'java']\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "import pickle\n", "from pathlib import Path\n", "import seaborn as sns\n", "\n", "sns.set_style(\"whitegrid\")\n", "\n", "def plot_log_hist(ax, data, nbins=10):\n", "    data = np.array(data)\n", "    logbins = np.geomspace(data.min(), data.max(), nbins)\n", "    ax.hist(data, bins=logbins)\n", "    ax.set_xscale('log')\n", "\n", "with Path(\"the_stack_statistics.pickle\").open(\"rb\") as file:\n", "    stats = pickle.load(file)\n", "\n", "num_langs = len(stats)\n", "print(f\"Found {num_langs} languages:\")\n", "print(list(stats.keys()))\n", "\n", "ncols = 3\n", "fig, axes = plt.subplots(ncols=ncols, nrows=num_langs//ncols, figsize=(12, 7))\n", "row = 0\n", "col = 0\n", "\n", "for lang in stats:\n", "    ax = axes[row][col]\n", "    plot_log_hist(ax, stats[lang][\"token_counts\"])\n", "    ax.set_title(f\"{lang}: token counts\")\n", "\n", "    col += 1\n", "    if col % ncols == 0:\n", "        col = 0\n", "        row += 1\n", "\n", "fig.tight_layout()\n", "plt.show()\n", "\n", "# rows = []\n", "# for lang, data in stats.items():\n", "#     row = {\n", "#         \"language\": lang,\n", "\n", "#         \"avg_chars\": np.mean(data[\"char_counts\"]),\n", "#         \"avg_tokens\": np.mean(data[\"token_counts\"]),\n", "#         \"avg_lines\": np.mean(data[\"line_counts\"]),\n", "\n", "#         \"std_chars\": np.std(data[\"char_counts\"]),\n", "#         \"std_tokens\": np.std(data[\"token_counts\"]),\n", "#         \"std_lines\": np.std(data[\"line_counts\"]),\n", "\n", "#         \"max_chars\": np.max(data[\"char_counts\"]),\n", "#         \"max_tokens\": np.max(data[\"token_counts\"]),\n", "#         \"max_lines\": np.max(data[\"line_counts\"]),\n", "#     }\n", "#     rows.append(row)\n", "\n", "# df = pd.DataFrame(rows)\n", "# df[\"chars_per_token\"] = df[\"avg_chars\"] / df[\"avg_tokens\"]\n", "# df[\"tokens_per_line\"] = df[\"avg_tokens\"] / df[\"avg_lines\"]\n", "\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ncols = 3\n", "fig, axes = plt.subplots(ncols=ncols, nrows=num_langs//ncols, figsize=(12, 5))\n", "row = 0\n", "col = 0\n", "\n", "for lang in stats:\n", "    token_counts = stats[lang][\"token_counts\"]\n", "    char_counts = stats[lang][\"char_counts\"]\n", "    char_to_token_ratios = [c/t for t, c in zip(token_counts, char_counts)]\n", "\n", "    ax = axes[row][col]\n", "    ax.hist(char_to_token_ratios)\n", "    # plot_log_hist(ax, char_to_token_ratios)\n", "    ax.set_title(f\"char/token in {lang}\")\n", "\n", "    col += 1\n", "    if col % ncols == 0:\n", "        col = 0\n", "        row += 1\n", "\n", "fig.tight_layout()\n", "plt.show()\n", "    "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["series: 380.0\n", "series: 0    380.0\n", "1    370.0\n", "2     24.0\n", "3     26.0\n", "Name: <PERSON>, dtype: float64\n", "800.0\n"]}], "source": ["import pandas as pd\n", "df = pd.DataFrame({'Animal': ['<PERSON>', 'Falcon',\n", "                              'Parrot', '<PERSON>rrot'],\n", "                   'Max Speed': [380., 370., 24., 26.]})\n", "\n", "# help(df.groupby('Animal').agg)\n", "\n", "from functools import reduce\n", "\n", "def test_sum(series):\n", "    return reduce(lambda x, y: x + y, series)\n", "\n", "print(df[\"Max Speed\"].agg(test_sum))\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([100203., 100112., 100163.,  99680.,  99418., 100441.,  99858.,\n", "        100109.,  99759., 100257.]),\n", " array([1.35768027e-06, 1.00001190e-01, 2.00001023e-01, 3.00000856e-01,\n", "        4.00000689e-01, 5.00000521e-01, 6.00000354e-01, 7.00000187e-01,\n", "        8.00000019e-01, 8.99999852e-01, 9.99999685e-01]),\n", " <BarContainer object of 10 artists>)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "x = np.random.rand(1000000)\n", "\n", "plt.hist(x)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "All arrays must be of the same length", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[17], line 22\u001b[0m\n\u001b[1;32m     19\u001b[0m     \u001b[39mprint\u001b[39m(\u001b[39m\"\u001b[39m\u001b[39mresult:\u001b[39m\u001b[39m\"\u001b[39m, result[:\u001b[39m10\u001b[39m])\n\u001b[1;32m     20\u001b[0m     \u001b[39mreturn\u001b[39;00m result\n\u001b[0;32m---> 22\u001b[0m df \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39;49mDataF<PERSON>e({\u001b[39m'\u001b[39;49m\u001b[39mname\u001b[39;49m\u001b[39m'\u001b[39;49m: [\u001b[39m'\u001b[39;49m\u001b[39ma\u001b[39;49m\u001b[39m'\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mb\u001b[39;49m\u001b[39m'\u001b[39;49m, \u001b[39m'\u001b[39;49m\u001b[39mc\u001b[39;49m\u001b[39m'\u001b[39;49m], \u001b[39m'\u001b[39;49m\u001b[39mvalues\u001b[39;49m\u001b[39m'\u001b[39;49m: [[\u001b[39m1.\u001b[39;49m], [\u001b[39m2.\u001b[39;49m], [\u001b[39m3.\u001b[39;49m], \u001b[39mfloat\u001b[39;49m(\u001b[39m'\u001b[39;49m\u001b[39mnan\u001b[39;49m\u001b[39m'\u001b[39;49m)]})\n\u001b[1;32m     23\u001b[0m \u001b[39mprint\u001b[39m(df)\n\u001b[1;32m     25\u001b[0m df[\u001b[39m'\u001b[39m\u001b[39mvalues\u001b[39m\u001b[39m'\u001b[39m]\u001b[39m.\u001b[39mreplace({\u001b[39mfloat\u001b[39m(\u001b[39m'\u001b[39m\u001b[39mnan\u001b[39m\u001b[39m'\u001b[39m): []}, inplace\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n", "File \u001b[0;32m~/.local/lib/python3.9/site-packages/pandas/core/frame.py:664\u001b[0m, in \u001b[0;36mDataFrame.__init__\u001b[0;34m(self, data, index, columns, dtype, copy)\u001b[0m\n\u001b[1;32m    658\u001b[0m     mgr \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_init_mgr(\n\u001b[1;32m    659\u001b[0m         data, axes\u001b[39m=\u001b[39m{\u001b[39m\"\u001b[39m\u001b[39mindex\u001b[39m\u001b[39m\"\u001b[39m: index, \u001b[39m\"\u001b[39m\u001b[39mcolumns\u001b[39m\u001b[39m\"\u001b[39m: columns}, dtype\u001b[39m=\u001b[39mdtype, copy\u001b[39m=\u001b[39mcopy\n\u001b[1;32m    660\u001b[0m     )\n\u001b[1;32m    662\u001b[0m \u001b[39melif\u001b[39;00m \u001b[39misinstance\u001b[39m(data, \u001b[39mdict\u001b[39m):\n\u001b[1;32m    663\u001b[0m     \u001b[39m# GH#38939 de facto copy defaults to False only in non-dict cases\u001b[39;00m\n\u001b[0;32m--> 664\u001b[0m     mgr \u001b[39m=\u001b[39m dict_to_mgr(data, index, columns, dtype\u001b[39m=\u001b[39;49mdtype, copy\u001b[39m=\u001b[39;49mcopy, typ\u001b[39m=\u001b[39;49mmanager)\n\u001b[1;32m    665\u001b[0m \u001b[39melif\u001b[39;00m \u001b[39misinstance\u001b[39m(data, ma\u001b[39m.\u001b[39mMaskedArray):\n\u001b[1;32m    666\u001b[0m     \u001b[39mimport\u001b[39;00m \u001b[39mnumpy\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mma\u001b[39;00m\u001b[39m.\u001b[39;00m\u001b[39mmrecords\u001b[39;00m \u001b[39mas\u001b[39;00m \u001b[39mmrecords\u001b[39;00m\n", "File \u001b[0;32m~/.local/lib/python3.9/site-packages/pandas/core/internals/construction.py:493\u001b[0m, in \u001b[0;36mdict_to_mgr\u001b[0;34m(data, index, columns, dtype, typ, copy)\u001b[0m\n\u001b[1;32m    489\u001b[0m     \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    490\u001b[0m         \u001b[39m# dtype check to exclude e.g. range objects, scalars\u001b[39;00m\n\u001b[1;32m    491\u001b[0m         arrays \u001b[39m=\u001b[39m [x\u001b[39m.\u001b[39mcopy() \u001b[39mif\u001b[39;00m \u001b[39mhasattr\u001b[39m(x, \u001b[39m\"\u001b[39m\u001b[39mdtype\u001b[39m\u001b[39m\"\u001b[39m) \u001b[39melse\u001b[39;00m x \u001b[39mfor\u001b[39;00m x \u001b[39min\u001b[39;00m arrays]\n\u001b[0;32m--> 493\u001b[0m \u001b[39mreturn\u001b[39;00m arrays_to_mgr(arrays, columns, index, dtype\u001b[39m=\u001b[39;49mdtype, typ\u001b[39m=\u001b[39;49mtyp, consolidate\u001b[39m=\u001b[39;49mcopy)\n", "File \u001b[0;32m~/.local/lib/python3.9/site-packages/pandas/core/internals/construction.py:118\u001b[0m, in \u001b[0;36marrays_to_mgr\u001b[0;34m(arrays, columns, index, dtype, verify_integrity, typ, consolidate)\u001b[0m\n\u001b[1;32m    115\u001b[0m \u001b[39mif\u001b[39;00m verify_integrity:\n\u001b[1;32m    116\u001b[0m     \u001b[39m# figure out the index, if necessary\u001b[39;00m\n\u001b[1;32m    117\u001b[0m     \u001b[39mif\u001b[39;00m index \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[0;32m--> 118\u001b[0m         index \u001b[39m=\u001b[39m _extract_index(arrays)\n\u001b[1;32m    119\u001b[0m     \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    120\u001b[0m         index \u001b[39m=\u001b[39m ensure_index(index)\n", "File \u001b[0;32m~/.local/lib/python3.9/site-packages/pandas/core/internals/construction.py:666\u001b[0m, in \u001b[0;36m_extract_index\u001b[0;34m(data)\u001b[0m\n\u001b[1;32m    664\u001b[0m lengths \u001b[39m=\u001b[39m \u001b[39mlist\u001b[39m(\u001b[39mset\u001b[39m(raw_lengths))\n\u001b[1;32m    665\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mlen\u001b[39m(lengths) \u001b[39m>\u001b[39m \u001b[39m1\u001b[39m:\n\u001b[0;32m--> 666\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\u001b[39m\"\u001b[39m\u001b[39mAll arrays must be of the same length\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m    668\u001b[0m \u001b[39mif\u001b[39;00m have_dicts:\n\u001b[1;32m    669\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m    670\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mMixing dicts with non-Series may lead to ambiguous ordering.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    671\u001b[0m     )\n", "\u001b[0;31mValueError\u001b[0m: All arrays must be of the same length"]}], "source": ["\n", "import pandas as pd\n", "import functools\n", "import math\n", "\n", "def concat_lists(series):\n", "    def to_list(x):\n", "        if isinstance(x, list):\n", "            return x\n", "        assert isinstance(x, float)\n", "        if math.isnan(x):\n", "            return []\n", "        return [x]\n", "\n", "    def concat_two(x, y):\n", "        x = to_list(x)\n", "        y = to_list(y)\n", "        return x + y\n", "    result = functools.reduce(concat_two, series)\n", "    print(\"result:\", result[:10])\n", "    return result\n", "\n", "df = pd.DataFrame({'name': ['a', 'b', 'c'], 'values': [[1.], [2.], [3.], float('nan')]})\n", "print(df)\n", "\n", "df['values'].replace({float('nan'): []}, inplace=True)\n", "print(df)\n", "# result = df['values'].aggregate(concat_lists)\n", "# print(\"outcome:\")\n", "# print(result)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["[1.0, 2.0, 3.0]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df['values'].where(~df['values'].isna()).aggregate(sum)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>value1</th>\n", "      <th>value2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>a</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>a</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  name  value1  value2\n", "0    a     1.0     1.0\n", "1    b     2.0     2.0\n", "3    a     4.0     NaN"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "df = pd.DataFrame({\n", "    \"name\": [\"a\", \"b\", \"c\", \"a\"],\n", "    \"value1\": [1, 2, np.nan, 4],\n", "    \"value2\": [1, 2, np.nan, np.nan],\n", "})\n", "df[~df[\"value1\"].isna() | ~df[\"value2\"].isna()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "c2e0331f5556c51be39aa32a1de0134f4b239d1748b5a47100507da36c656ca4"}}}, "nbformat": 4, "nbformat_minor": 2}