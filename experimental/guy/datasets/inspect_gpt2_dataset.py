"""Inspect a .bin, .idx dataset using GPT2Dataset.

GPT2Dataset is the main class used to prepare data for GPT NeoX training.
It wraps IndexedDataset and handles things like shuffling and batching.
"""

import os

import torch.distributed as dist
from megatron import mpu
from megatron.data.data_utils import build_the_dataset


def main():
    os.environ["MASTER_ADDR"] = "127.0.0.1"
    os.environ[
        "MASTER_PORT"
    ] = "12455"  # this can be any reasonable port number, doesn't matter
    dist.init_process_group(backend="nccl", rank=0, world_size=1)
    mpu.initialize_model_parallel(1)

    path = (
        "/mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document"
    )
    seq_length = 2048

    gpt2_ds = build_the_dataset(
        data_prefix=path,
        name="inspected_dataset",
        data_impl="mmap",
        num_samples=10,
        seq_length=seq_length,
        seed=42,
        skip_warmup=True,
        build_index_mappings=True,
        dataset_type="gpt2",
    )

    sample = gpt2_ds[0]
    print(sample["text"])


if __name__ == "__main__":
    main()
