"""Inspect an indexed dataset (.bin, .idx)."""

import argparse
from pathlib import Path

import numpy as np
from megatron.data import indexed_dataset
from megatron.tokenizer.tokenizer import get_tokenizer


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "dataset",
        type=Path,
        default=None,
        help="path to an indexed dataset (with or without the .bin/.idx suffix)",
    )
    parser.add_argument(
        "--n",
        type=int,
        default=1,
        help="how many samples to show",
    )
    parser.add_argument(
        "--tokenizer",
        type=str,
        default="StarCoderTokenizer",
        help="name of the tokenizer, e.g. StarCoderTokenizer or CodeGenTokenizer",
    )
    args = parser.parse_args()

    tokenizer = get_tokenizer(args.tokenizer)
    ds = indexed_dataset.make_dataset(
        str(args.dataset.with_suffix("")), impl="mmap", skip_warmup=True
    )

    print(">>> Dataset length:", len(ds))
    print("")

    for _ in range(args.n):
        sample_idx = np.random.randint(len(ds))

        # Make it even
        # if sample_idx % 2:
        #     sample_idx -= 1

        print("\n===================================================")
        for idx in [sample_idx, sample_idx + 1]:
            # Change negative (masked-out) to positive token IDs
            item = [abs(int(x)) for x in ds[idx]]
            # print("item:", item)
            # print(type(item))
            # print(len(item))
            text = tokenizer.detokenize(item)

            # if np.random.randint(10000) == 0:
            print(f">>> i={idx} token_len={len(item)}:")
            print(text.split("<|padding|>")[0])
            print("")


if __name__ == "__main__":
    main()
