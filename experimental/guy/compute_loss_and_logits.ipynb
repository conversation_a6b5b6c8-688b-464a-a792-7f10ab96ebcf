{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Compute the loss and logits of a prompt. This code probably has some import issues\n", "and maybe other issues."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.core import (\n", "    CodeGen_350M_Multi,\n", ")\n", "\n", "import logging\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "checkpoints_root = \"/mnt/efs/augment/checkpoints\"\n", "\n", "print(\"Loading the model...\")\n", "model = CodeGen_350M_Multi(checkpoints_root)\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn.functional as F\n", "\n", "def compute_loss(prompt, model, tokenizer):\n", "    \"\"\"Compute the cross-entropy loss on the given prompt.\n", "\n", "    Args:\n", "        prompt: a string prompt\n", "        model: a neox model (megatron model)\n", "        tokenizer: a tokenizer\n", "    \n", "    Returns:\n", "        The per-token loss\n", "    \"\"\"\n", "    prompt = \"The quick brown fox jumps over the lazy dog.\"\n", "    tokens = tokenizer.tokenize(prompt)\n", "\n", "    # Add an initial token because the returned number of predictions is one\n", "    # less than the tokens length, and adding a token in the beginning makes\n", "    # the predictions align correctly with the tokens (tested on a simple\n", "    # example).\n", "    data_iterator = iter([\n", "        {\"text\": torch.tensor([[tokenizer.eod_id] + tokens], dtype=torch.int64)}\n", "    ])\n", "\n", "    eval_result = model.eval_batch(data_iterator, return_logits=True)\n", "    model.module.clear_cache()  # clear the k,v cache\n", "\n", "    # eval_batch computes the loss\n", "    loss = eval_result[0].cpu().numpy()\n", "    print(\"Computed cross-entropy loss:\", loss)\n", "\n", "    # Compute the loss using the logits\n", "    logits = eval_result[1][0]\n", "    alternative_loss = F.cross_entropy(\n", "        logits,\n", "        torch.tensor(tokens, dtype=torch.int64).cuda(),\n", "        reduction=\"mean\").cpu().numpy()\n", "\n", "    print(\"Loss computed from the logits (should agree):\", alternative_loss)\n", "\n", "    pred_tokens = torch.argmax(logits, dim=-1).cpu().numpy()\n", "    inv_vocab = {v: k for k, v in tokenizer.vocab.items()}\n", "    print(\"Prompt tokens:\\n\", [inv_vocab[t] for t in tokens])\n", "    print(\"Predicted tokens:\\n\", [inv_vocab[t] for t in pred_tokens])\n", "\n", "    return loss\n", "\n", "\n", "compute_loss(\"The quick brown fix jumps over the lazy dog\", model.neox_model, model.tokenizer)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}