apiVersion: apps/v1
kind: Deployment
metadata:
  name: augment-guy-3-las1
  labels:
    app: training

# Define how many instances we want
spec:
  replicas: 1
  selector:
    matchLabels:
      app: training

# define the specific container and pod details
  template:
    metadata:
      labels:
        app: training
    spec:
      containers:
      - name: augment-guy-3-env
        resources:
          limits:
            memory: 960Gi
            nvidia.com/gpu: 8
          requests:
            cpu: 110
            memory: 960Gi
        image: au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/au_model:7
        command: ["sleep", "inf"]
        workingDir: /home/<USER>/
        # Storage volume definition
        volumeMounts:
        - mountPath: /mnt/efs/augment
          name: augment-shared-mount
      volumes:
      - name: augment-shared-mount
        persistentVolumeClaim:
          claimName: aug-cw-las1
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: gpu.nvidia.com/class
                operator: In
                values:
                  - A100_NVLINK_80GB
              - key: topology.kubernetes.io/region
                operator: In
                values:
                  - LAS1
