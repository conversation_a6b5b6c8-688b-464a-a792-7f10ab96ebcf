"""Turn HumanEval into a few-shot dataset.

HumanEval doesn't have a train/validation split, so we use train examples as
few-shot examples. This utility will create a few-shot dataset from the given
HumanEval dataset, with fixed few-shot examples for each.
"""

import argparse
import json
import random
from pathlib import Path


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dataset",
        type=Path,
        required=True,
        help="path to the HumanEval dataset, a .jsonl file",
    )
    parser.add_argument(
        "--output",
        type=Path,
        required=True,
        help="where to save the generated dataset, another .jsonl file",
    )
    parser.add_argument(
        "--shots",
        type=int,
        required=True,
        help="number of shots in the few-shot prompts",
    )
    args = parser.parse_args()

    with args.dataset.open(encoding="utf-8") as file:
        lines = file.readlines()
        samples = [json.loads(line) for line in lines]

    for i, sample in enumerate(samples):
        # print(json.dumps(sample, indent=2))

        few_shot_samples = random.sample(
            [fs_sample for j, fs_sample in enumerate(samples) if i != j], args.shots
        )

        few_shot_prompts = [
            fs_sample["prompt"] + fs_sample["canonical_solution"]
            for fs_sample in few_shot_samples
        ]

        sample["few_shot"] = few_shot_prompts

    with args.output.open("w", encoding="utf-8") as file:
        for sample in samples:
            # print(json.dumps(sample, indent=2))
            # for fs_sample in sample["few_shot"]:
            #     print(fs_sample)
            file.write(json.dumps(sample) + "\n")


if __name__ == "__main__":
    main()
