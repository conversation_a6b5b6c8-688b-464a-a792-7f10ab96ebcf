"""Print commands to fix a 'no apt_pkg' error.

The error happens when trying to run a non-existent program:

   guy-dev-a100-ord1 [0] ~ > foo
   Traceback (most recent call last):
     File "/usr/lib/command-not-found", line 28, in <module>
       from CommandNotFound import CommandNotFound
     File "/usr/lib/python3/dist-packages/CommandNotFound/CommandNotFound.py", line 19, in <module>
       from CommandNotFound.db.db import SqliteDatabase
     File "/usr/lib/python3/dist-packages/CommandNotFound/db/db.py", line 5, in <module>
       import apt_pkg
   ModuleNotFoundError: No module named 'apt_pkg'

Source for solution: https://stackoverflow.com/questions/13708180/python-dev-installation-error-importerror-no-module-named-apt-pkg
"""

import sys
from pathlib import Path


def main():
    """Main."""
    target_dir = Path("/usr/lib/python3/dist-packages")
    target_file = "apt_pkg.so"
    paths = [path.name for path in Path(target_dir).glob("*apt_pkg*.so")]
    if target_file in paths:
        print(f"{target_file} already exists in {target_dir}, nothing to do")
        sys.exit(1)
    if len(paths) != 1:
        print(f"Expected 1 file in {target_dir}, found {len(paths)}")
        sys.exit(1)

    print("Run the following command to fix the apt_pkg problem:\n")
    print(f"> sudo ln -s {target_dir/paths[0]} {target_dir}/apt_pkg.so")


if __name__ == "__main__":
    main()
