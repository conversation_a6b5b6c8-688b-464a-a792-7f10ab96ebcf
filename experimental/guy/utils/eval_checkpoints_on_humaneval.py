"""Evaluate starcoder 3b python checkpoints on humaneval."""

import argparse
import csv
import json
import subprocess
import time
from pathlib import Path

import numpy as np
from download_checkpoint import download_checkpoint

GIT_ROOT = (
    subprocess.check_output(["git", "rev-parse", "--show-toplevel"])
    .decode("utf-8")
    .strip()
)
EVAL_SCRIPT = Path(GIT_ROOT) / "research/eval/eval.py"


HYDRA_CONFIG = """
system:
    name: basic_rag
    model:
        name: starcoderbase_3b
        checkpoint_path: [CHECKPOINT_PATH]
        prompt:
            max_prefix_tokens: 4096
            max_suffix_tokens: 0
            max_prompt_tokens: 4096
            always_fim_style: True
            #fill_to_context_window: True
    generation_options:
        temperature: 0
        top_k: 0
        top_p: 0
        #max_generated_tokens: 32
        max_generated_tokens: 280
    retriever:
        name: null
        chunker: line_level
        max_chunk: 20
    experimental:
        remove_suffix: False
        retriever_top_k: 25
        trim_on_dedent: True
        trim_on_max_lines: null
"""

HUMANEVAL_0SHOT_TASK = """
task:
    name: humaneval
    variant: 0-shot
"""

HUMANEVAL_1SHOT_TASK = """
task:
    name: humaneval
    variant: 1-shot
"""

HUMANEVAL_5SHOT_TASK = """
task:
    name: humaneval
    variant: 5-shot
"""

HUMANEVAL_FIM_LIGHT_TASK = """
task:
    name: humaneval_fim
    variant: multiline_light
    exec: true
    iterations: 1
"""

TASK_SECTIONS = {
    "humaneval": HUMANEVAL_0SHOT_TASK,
    "humaneval_1shot": HUMANEVAL_1SHOT_TASK,
    # "humaneval_5shot": HUMANEVAL_5SHOT_TASK,
    "humaneval_fim_light": HUMANEVAL_FIM_LIGHT_TASK,
}


def list_checkpoints(experiment_id):
    """List checkpoints for a given determined experiment id."""
    cmd = f"det experiment list-checkpoints --csv {experiment_id}"
    print(f"Running: {cmd}")
    output = subprocess.check_output(cmd, shell=True).decode("utf-8")
    csv.field_size_limit(10_000_000)
    data = csv.DictReader(output.splitlines())
    checkpoints = []
    for row in data:
        del row["Resources"]
        steps = row["# of Batches"]
        checkpoint_uuid = row["UUID"]
        checkpoints.append({"step": int(steps), "uuid": checkpoint_uuid})
        print("Found checkpoint:", row)
    return checkpoints


def download_checkpoints(args):
    checkpoints = list_checkpoints(args.experiment_id)
    for ckpt in checkpoints:
        download_path = (
            args.checkpoints_root / f"{args.experiment_name}-{ckpt['step']:05d}steps"
        )
        # print(f"Downloading checkpoint {ckpt} to {download_path}")
        download_checkpoint(
            checkpoint_id=ckpt["uuid"],
            download_path=str(download_path),
            retries=args.download_retries,
            verbose=True,
        )


def evaluate_checkpoints(args):
    tmp_config_path = Path("/tmp/my_eval_config.yml")
    args.eval_root.mkdir(parents=True, exist_ok=True)
    num_cases = 164
    accs = {}
    for task, task_config in TASK_SECTIONS.items():
        print(f"======== experiment_name={args.experiment_name} task={task}")
        task_eval_root = args.eval_root / task
        task_eval_root.mkdir(parents=True, exist_ok=True)
        for checkpoint in sorted(
            args.checkpoints_root.glob(f"{args.experiment_name}-*")
        ):
            eval_path = task_eval_root / checkpoint.name
            if not eval_path.exists():
                print("")
                print(f"Evaluating {checkpoint} ...")
                hydra_config = HYDRA_CONFIG + task_config
                hydra_config = hydra_config.replace(
                    "[CHECKPOINT_PATH]", str(checkpoint)
                )
                with tmp_config_path.open("w", encoding="utf8") as tmp_config_file:
                    tmp_config_file.write(hydra_config)
                cmd = f"python3 {EVAL_SCRIPT} --local --v2 --job_root {eval_path} {tmp_config_path}"
                print(cmd)
                start = time.time()
                subprocess.run(cmd, shell=True, check=True)
                elapsed = time.time() - start
                print(f"Evaluation took {elapsed:.1f} seconds")

            try:
                results_path = list(eval_path.glob("*.jsonl"))[0]
            except IndexError as exc:
                raise RuntimeError(
                    f"No results found in {eval_path} ."
                    " This is probably due to a failed evaluation."
                ) from exc

            with results_path.open("r", encoding="utf8") as results_file:
                # These jsonl files should have just one line
                results = json.load(results_file)
                accs[str(checkpoint.name)] = results["metrics"]["pass_at_k"]["pass@1"]

        print("pass@1:")
        pass_rates = []
        for name, acc in accs.items():
            pass_rate = round(acc * num_cases)
            pass_rates.append(pass_rate)
            print(f"{name}: {acc*100:.1f}%\t{pass_rate}/{num_cases}")

        print(
            f"pass rate mean={np.mean(pass_rates):.1f} stddev={np.std(pass_rates):.1f}"
        )


def main():
    """Evaluate checkpoints on humaneval and its variants."""

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--experiment_id",
        type=str,
        default=None,
        help="determined experiment id. if specified, checkpoints will be downloaded",
    )
    parser.add_argument(
        "--experiment_name",
        type=str,
        required=True,
        help="experiment name, to be used when saving checkpoints",
    )
    parser.add_argument(
        "--download_retries",
        type=int,
        default=10,
        help=(
            "number of times to retry downloading a checkpoint. "
            "set this to a large value because S3 downloads are unreliable."
        ),
    )
    parser.add_argument(
        "--checkpoints_root",
        type=Path,
        default=Path("/mnt/efs/augment/user/guy/checkpoints"),
        help="path to a directory containing model checkpoints",
    )
    parser.add_argument(
        "--eval_root",
        type=Path,
        default=Path("/mnt/efs/augment/user/guy/eval"),
        help="path to a directory where evaluation results will be saved",
    )
    args = parser.parse_args()

    if args.experiment_id is not None:
        download_checkpoints(args)
    evaluate_checkpoints(args)


if __name__ == "__main__":
    main()
