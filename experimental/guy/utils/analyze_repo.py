"""Analyze a code repository and display statistics per programming language."""

import os
import collections
import argparse
import pandas


def count_lines_and_chars(file_path):
    """
    Count the number of lines and characters in a file.

    Args:
        file_path (str): Path to the file

    Returns:
        tuple: (num_lines, num_chars)
    """
    with open(file_path, "r") as f:
        lines = f.readlines()
        num_lines = len(lines)
        num_chars = sum(len(line) for line in lines)
    return num_lines, num_chars


def analyze_repository(repo_path):
    """
    Analyze a code repository and display statistics per programming language.

    Args:
        repo_path (str): Path to the code repository

    Returns:
        None
    """
    language_stats = collections.defaultdict(
        lambda: {"files": 0, "lines": [], "chars": []}
    )

    for root, dirs, files in os.walk(repo_path):
        for file in files:
            if "." not in file:
                continue
            file_path = os.path.join(root, file)
            language = file.split(".")[-1]  # assume language is the file extension
            try:
                num_lines, num_chars = count_lines_and_chars(file_path)
            except Exception:
                print(f"Failed to count lines and chars for {file_path}")
                continue

            language_stats[language]["files"] += 1
            language_stats[language]["lines"].append(num_lines)
            language_stats[language]["chars"].append(num_chars)

    data = [
        {
            "language": language,
            "files": stats["files"],
            "total_lines": sum(stats["lines"]),
            "avg_lines_per_file": sum(stats["lines"]) / len(stats["lines"]),
            "max_lines_per_file": max(stats["lines"]),
            "total_chars": sum(stats["chars"]),
            "avg_chars_per_file": sum(stats["chars"]) / len(stats["chars"]),
            "approx_total_tokens": sum(stats["chars"]) // 3,
            "approx_max_tokens_per_file": max(stats["chars"]) // 3,
        }
        for language, stats in language_stats.items()
    ]

    df = pandas.DataFrame(data)

    pandas.options.display.float_format = "{:.0f}".format
    print(df.sort_values("total_lines", ascending=False).to_string(index=False))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Analyze a code repository and display statistics per programming language."
    )
    parser.add_argument("repo_path", help="Path to the code repository")
    args = parser.parse_args()
    analyze_repository(args.repo_path)
