"""Download a determined checkpoint, fix its config."""

import argparse
import subprocess
from pathlib import Path

import yaml


def download_checkpoint(
    checkpoint_id: str, download_path: str, retries: int = 1, verbose: bool = False
):
    """Download a checkpoint from S3, and fix its config.

    The config is adjusted so it can be used for inference.

    Args:
        checkpoint_id: The checkpoint UUID to download
        download_path: Where to download the checkpoint to
        retries: Number of times to retry downloading the checkpoint
        verbose: Print progress to stdout
    """
    if Path(download_path).exists():
        if verbose:
            print("Checkpoint already downloaded")
        return

    Path(download_path).mkdir(parents=True, exist_ok=True)

    attempt = 1

    while attempt <= retries:
        if verbose:
            print(
                f"downloading checkpoint {checkpoint_id} to {download_path} (attempt {attempt} of {retries})..."
            )
        try:
            # cmd = f"bash -c 's3cmd sync --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/{checkpoint_id} {download_path} > /dev/null'"
            cmd = f"bash -c 's3cmd sync --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/{checkpoint_id} {download_path}'"
            subprocess.run(cmd, shell=True, check=True)
            subprocess.run(
                f"mv {download_path}/{checkpoint_id}/* {download_path}",
                shell=True,
                check=True,
            )
            subprocess.run(
                f"rmdir {download_path}/{checkpoint_id}", shell=True, check=True
            )
            break
        except subprocess.CalledProcessError as e:
            print("Error occurred while downloading:")
            print(e)
            attempt += 1

    if attempt > retries:
        raise RuntimeError("Failed to download checkpoint")

    # change checkpoint path in the config
    if verbose:
        print(f"changing config.yml to load checkpoint from {download_path}")
    config_path = Path(download_path, "config.yml")
    with config_path.open("r", encoding="utf-8") as file:
        config = yaml.safe_load(file)
    config["load"] = download_path
    config["train_batch_size"] = 1
    config["train_micro_batch_size_per_gpu"] = 1
    config["gradient_accumulation_steps"] = 1
    config["global_num_gpus"] = 1
    with config_path.open("w", encoding="utf-8") as file:
        yaml.safe_dump(config, file)

    if verbose:
        print("done")


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--checkpoint",
        type=str,
        help="The determined checkpoint ID to download, looks like 094bd66f-f1bd-4ba6-9f6e-25774eeee7d5",
    )
    parser.add_argument(
        "--path",
        type=str,
        required=True,
        help="The directory to download the checkpoint to",
    )
    args = parser.parse_args()
    download_checkpoint(
        checkpoint_id=args.checkpoint, download_path=args.path, verbose=True
    )


if __name__ == "__main__":
    main()
