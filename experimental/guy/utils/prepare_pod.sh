#!/bin/bash

set -e

# FROM_POD=augment-training2-las1-769f6bd5f6-67r9w
FROM_POD=augment-guy-1-las1-7bcc7446cc-cwqqk

# TO_POD=augment-guy-2-las1-76c4dfb6d4-j7qnj
# TO_POD=augment-guy-3-las1-578c79569f-sjcxg
TO_POD=augment-guy-4-las1-667595ccfd-4z7n5

STAGING=/tmp/staging

# Prepare directories
mkdir -p $STAGING
kubectl exec ${TO_POD} -- sudo chown augment.augment .ssh

# Copy config files
for FILE in .bashrc .inputrc .vimrc .tmux.conf .gitconfig .netrc
do
echo Copying $FILE to local
rm -f ${STAGING}/${FILE}
kubectl cp ${FROM_POD}:${FILE} ${STAGING}/${FILE}
kubectl cp ${STAGING}/${FILE} ${TO_POD}:${FILE}
done

# Copy .ssh files
for FILE in .ssh/augment_rsa .ssh/augment_rsa.pub .ssh/config
do
echo Copying $FILE
kubectl cp ${FILE} ${TO_POD}:${FILE}
done

# Clone the code
echo "Run this in the pod to clone the code:"
echo "<NAME_EMAIL>:guygurari/augment.git"
