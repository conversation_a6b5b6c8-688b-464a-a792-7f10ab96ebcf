#!/usr/bin/env python
"""
Demonstration of what happens when the ToolSupportProvider is not enabled.

This script demonstrates the error that occurs when trying to use tools with a model
that doesn't natively support tools, without enabling the ToolSupportProvider.

Usage:
    PYTHONPATH=$HOME/au4 python experimental/guy/try_lms_without_tool_support_provider.py

Requirements:
    - A valid Fireworks API key in $HOME/.augment/fireworks_api_token
"""

import json
import os
import sys
from pathlib import Path

from research.llm_apis.llm_client import (
    FireworksClient,
    TextPrompt,
    ToolParam,
)


def main():
    """Run the demonstration."""
    # Get the API key
    api_key_path = Path(os.path.expanduser("~/.augment/fireworks_api_token"))
    if not api_key_path.exists():
        print(f"Error: API key file not found at {api_key_path}")
        sys.exit(1)

    api_key = api_key_path.read_text().strip()
    if not api_key:
        print("Error: API key is empty")
        sys.exit(1)

    # Create the FireworksClient with the deepseek-r1 model
    print("Creating FireworksClient with deepseek-r1 model...")
    client = FireworksClient(
        api_key=api_key,
        model_name=FireworksClient.MODEL_DEEPSEEK_R1,  # This model doesn't natively support tools
        use_tool_support_provider=False,  # Disable the ToolSupportProvider
    )

    # Define the get_temperature tool
    get_temperature_tool = ToolParam(
        name="get_temperature",
        description="Get the current temperature in a given location",
        input_schema={
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The location to get the temperature for",
                }
            },
            "required": ["location"],
        },
    )

    # User question
    user_question = "What's the temperature in San Francisco today?"

    print("\n" + "=" * 80)
    print("Sending the question to the model...")
    print(f"Question: {user_question}")
    print("=" * 80 + "\n")

    try:
        # Try to call the model with tools
        response, _ = client.generate(
            messages=[[TextPrompt(text=user_question)]],
            max_tokens=1024,
            tools=[get_temperature_tool],
        )

        # This should not be reached
        print("Unexpected success! The model should have raised an error.")

    except ValueError as e:
        # This is the expected behavior
        print(f"Expected error: {e}")
        print("\nThis error is expected because we disabled the ToolSupportProvider.")
        print(
            "To use tools with this model, enable the ToolSupportProvider by setting:"
        )
        print("use_tool_support_provider=True in the FireworksClient constructor.")

    print("\n" + "=" * 80)
    print("Demonstration completed successfully!")
    print("=" * 80)


if __name__ == "__main__":
    main()
