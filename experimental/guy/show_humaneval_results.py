"""Show HumanEval results."""

import argparse
import json
from pathlib import Path

from colorama import Fore, Style


def shorten_string(s, max_string_len=30):
    if len(s) > max_string_len:
        return f"{s[:max_string_len]}..."
    else:
        return s


def shorten_data(data: dict) -> dict:
    shorter = {}
    for k, v in data.items():
        if isinstance(v, str):
            shorter[k] = shorten_string(v)
        elif isinstance(v, dict):
            shorter[k] = shorten_data(v)
        else:
            shorter[k] = v

    return shorter


def print_result(data):
    print("=" * 80)
    print(data["patch"]["task_id"], end="")
    if data["patch"]["passed"]:
        print(Fore.GREEN + " passed" + Style.RESET_ALL)
    else:
        print(Fore.RED + " didn't pass" + Style.RESET_ALL)
    # short_data = shorten_data(data)
    # print(json.dumps(short_data, indent=2))
    print(data["patch"]["prompt"], end="")
    print(Fore.GREEN + data["generation"] + Style.RESET_ALL)
    print("")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--results", type=Path, default=None, help="results jsonl file")
    parser.add_argument(
        "--results2", type=Path, default=None, help="second results jsonl file"
    )
    parser.add_argument(
        "-n", type=int, default=None, help="number of questions to print"
    )
    args = parser.parse_args()

    with args.results.open("r", encoding="utf8") as file:
        with args.results2.open("r", encoding="utf8") as file2:
            for i, (line, line2) in enumerate(zip(file, file2)):
                data = json.loads(line)
                data2 = json.loads(line2)

                if data["patch"]["passed"] == data2["patch"]["passed"]:
                    continue

                print("Model 1:")
                print_result(data)
                print("Model 2:")
                print_result(data2)

                if args.n and i >= args.n:
                    break


if __name__ == "__main__":
    main()
