{"cells": [{"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# diff = '''diff --git a/bar.py b/bar.py\n", "# index 94ebaf9..e8a01cd 100644\n", "# --- a/bar.py\n", "# +++ b/bar.py\n", "# @@ -1,4 +1,3 @@\n", "#  1\n", "#  2\n", "# -3\n", "#  4\n", "# diff --git a/foo.py b/foo.py\n", "# index e050acb..c86ffdc 100644\n", "# --- a/foo.py\n", "# +++ b/foo.py\n", "# @@ -1,3 +1,4 @@\n", "#  print(1)\n", "# -print(2)\n", "# +print(2.5)\n", "# +print(2.5)\n", "#  print(3)\n", "# '''\n", "\n", "diff = '''diff --git a/foo.py b/foo.py\n", "index 1679abe..53c9c63 100644\n", "--- a/foo.py\n", "+++ b/foo.py\n", "@@ -5,0 +6,4 @@ print(5)\n", "+  \n", "+# add line\n", "+# add another line\n", "+  \n", "'''\n", "\n", "from unidiff import PatchSet\n", "patchset = PatchSet(diff)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["@@ -5,0 +6,4 @@ print(5)\n", "+  \n", "+# add line\n", "+# add another line\n", "+  \n", "\n", "hunk.target_start=6\n", "hunk.target_length=4\n", "target text:\n", "['+  \\n', '+# add line\\n', '+# add another line\\n', '+  \\n']\n"]}], "source": ["hunk = patchset[0][0]\n", "print(hunk)\n", "print(f\"{hunk.target_start=}\")\n", "print(f\"{hunk.target_length=}\")\n", "print(\"target text:\")\n", "print(hunk.target)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["line: # add line\n", "\n", "line: # add another line\n", "\n"]}], "source": ["for line in hunk.target_lines():\n", "    print(\"line:\", line.value)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["''"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["lines = list(hunk.target_lines())\n", "lines[0].value.strip()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["['__add__',\n", " '__class__',\n", " '__class_getitem__',\n", " '__contains__',\n", " '__delattr__',\n", " '__delitem__',\n", " '__dict__',\n", " '__dir__',\n", " '__doc__',\n", " '__eq__',\n", " '__format__',\n", " '__ge__',\n", " '__getattribute__',\n", " '__getitem__',\n", " '__gt__',\n", " '__hash__',\n", " '__iadd__',\n", " '__imul__',\n", " '__init__',\n", " '__init_subclass__',\n", " '__iter__',\n", " '__le__',\n", " '__len__',\n", " '__lt__',\n", " '__module__',\n", " '__mul__',\n", " '__ne__',\n", " '__new__',\n", " '__reduce__',\n", " '__reduce_ex__',\n", " '__repr__',\n", " '__reversed__',\n", " '__rmul__',\n", " '__setattr__',\n", " '__setitem__',\n", " '__sizeof__',\n", " '__str__',\n", " '__subclasshook__',\n", " '__weakref__',\n", " '_added',\n", " '_removed',\n", " 'added',\n", " 'append',\n", " 'clear',\n", " 'copy',\n", " 'count',\n", " 'extend',\n", " 'index',\n", " 'insert',\n", " 'is_valid',\n", " 'pop',\n", " 'remove',\n", " 'removed',\n", " 'reverse',\n", " 'section_header',\n", " 'sort',\n", " 'source',\n", " 'source_length',\n", " 'source_lines',\n", " 'source_start',\n", " 'target',\n", " 'target_length',\n", " 'target_lines',\n", " 'target_start']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dir(hunk)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2\n", "2\n"]}], "source": ["print(hunk.target_start)\n", "print(hunk.target_length)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["diff output:\n", "diff --git a/tmp/before.txt b/tmp/after.txt\n", "index d1e49e4c..b0340ba7 100644\n", "--- a/tmp/before.txt\n", "+++ b/tmp/after.txt\n", "@@ -1,6 +1,9 @@\n", " 1\n", " 2\n", " 22\n", "+2,\n", " 3\n", "-4\n", "+4,\n", "+4,\n", "+4,\n", " 5\n", "\\ No newline at end of file\n", "\n", "hunk.target_start=1 hunk.target_length=0\n"]}], "source": ["import tempfile\n", "from pathlib import Path\n", "import subprocess\n", "from unidiff import PatchSet\n", "\n", "def git_diff(\n", "    before_text: str,\n", "    before_path: str,\n", "    after_text: str,\n", "    after_path: str,\n", "    git_options: str = \"\",\n", ") -> str:\n", "    \"\"\"Run git-diff on the two pieces of text and return the output.\n", "\n", "    Args:\n", "        before_text: The first piece of text\n", "        after_text: The second piece of text\n", "        git_options: Command-line options to pass to 'git diff'\n", "\n", "    Returns:\n", "        The git diff output.\n", "    \"\"\"\n", "    with tempfile.TemporaryDirectory() as tmp_dir:\n", "        temp_before_path = Path(tmp_dir) / \"before.txt\"\n", "        temp_after_path = Path(tmp_dir) / \"after.txt\"\n", "\n", "        with temp_before_path.open(\"w\", encoding=\"utf8\") as before_file:\n", "            before_file.write(before_text)\n", "\n", "        with temp_after_path.open(\"w\", encoding=\"utf8\") as after_file:\n", "            after_file.write(after_text)\n", "\n", "        proc = subprocess.run(\n", "            f\"git diff {git_options} {temp_before_path} {temp_after_path}\",\n", "            shell=True,\n", "            check=False,\n", "            capture_output=True,\n", "        )\n", "        stdout = proc.stdout.decode()\n", "        stderr = proc.stderr.decode()\n", "        if stderr:\n", "            raise ValueError(f\"git diff failed: {stderr}\")\n", "        output = stdout\n", "        if not before_path.startswith(\"/\"):\n", "            before_path = f\"/{before_path}\"\n", "        if not after_path.startswith(\"/\"):\n", "            after_path = f\"/{after_path}\"\n", "        output = output.replace(str(temp_before_path), before_path)\n", "        output = output.replace(str(temp_after_path), after_path)\n", "        return output\n", "\n", "path = \"hello.txt\"\n", "\n", "before_text = \"'''docstring'''\\nimport foo\\nimport bar\\n\"\n", "after_text = \"'''docstring'''\\nimport bar\\n\"\n", "\n", "# before_text = \"d\\nf\\n\"\n", "# after_text = \"d\\ne\\nf\\n\"\n", "\n", "diff_text: str = git_diff(\n", "    before_text=before_text,\n", "    before_path=path,\n", "    after_text=after_text,\n", "    after_path=path,\n", "    git_options=\"-U0\",\n", ")\n", "diff_patchset = PatchSet(diff_text)\n", "hunk = diff_patchset[0][0]\n", "\n", "print(f\"before:\\n{before_text}\")\n", "print(f\"after:\\n{after_text}\")\n", "print(f\"{hunk.target_start=} {hunk.target_length=}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["hunk.target_start"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gradio\n", "\n", "def diff(before, after, git_options):\n", "    diff = git_diff(before, after, git_options)\n", "    patch_set = unidiff.PatchSet(diff)\n", "    out = \"\"\n", "    for patch in patch_set:\n", "        out += f\"patch: {patch.path}\\n\"\n", "        for i, hunk in enumerate(patch):\n", "            out += f\"hunk {i}: target_start={hunk.target_start} target_length={hunk.target_length} source_start={hunk.source_start} source_length={hunk.source_length}\\n\"\n", "    return out\n", "\n", "model_playground = gradio.Interface(\n", "    fn=diff,\n", "    inputs=[\n", "        gradio.Textbox(lines=7, placeholder=\"1\\n3\\n\"),\n", "        gradio.Textbox(lines=7, placeholder=\"1\\n2\\n3\\n\"),\n", "        gradio.Textbox(lines=1, placeholder=\"-U0\"),\n", "        ],\n", "    outputs=gradio.Textbox(lines=7, placeholder=\"\"),\n", "    title=f\"git diff\",\n", "    live=True,\n", "    examples=[[\"\\n\".join(map(str, [1, 2, 3, 4, 5, 6])), \"\\n\".join(map(str, [1, 2, 3, 4, 5, 6])), \"-U0\"]],\n", "    )\n", "\n", "model_playground.launch(server_name=\"0.0.0.0\", height=800, share=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}