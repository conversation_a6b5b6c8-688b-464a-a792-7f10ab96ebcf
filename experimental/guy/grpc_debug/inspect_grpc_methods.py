"""Inspect gRPC methods, including their input and output schemas.

This is an interactive tool that lists the gRPC methods and their
input and output schemas in a given module. In order to inspect a protobuf
module, it needs to be imported by this script.
"""

import argparse
import importlib
from typing import List, Dict, Any
from types import ModuleType
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.descriptor import (
    ServiceDescriptor,
    MethodDescriptor,
    FileDescriptor,
)

# Get the global symbol database
_sym_db = _symbol_database.Default()

# List of available _pb2 modules
PB2_MODULES = [
    "services.chat_host.chat_pb2",
    "services.content_manager.content_manager_pb2",
    "services.completion_host.completion_pb2",
    "services.edit_host.edit_pb2",
    "services.grpc_debug.grpc_debug_pb2",
]


def get_field_type(field_type: int) -> str:
    type_map = {
        1: "double",
        2: "float",
        3: "int64",
        4: "uint64",
        5: "int32",
        6: "fixed64",
        7: "fixed32",
        8: "bool",
        9: "string",
        10: "group",
        11: "message",
        12: "bytes",
        13: "uint32",
        14: "enum",
        15: "sfixed32",
        16: "sfixed64",
        17: "sint32",
        18: "sint64",
    }
    return type_map.get(field_type, f"unknown({field_type})")


def get_services(module: ModuleType) -> List[ServiceDescriptor]:
    """Returns the services for the given module.

    Args:
        module: An imported python module that was generated from a protobuf
        definition, usually ends with _pb2. E.g. services.chat_host.chat_pb2
    """
    services = []

    if hasattr(module, "DESCRIPTOR"):
        descriptor = getattr(module, "DESCRIPTOR")
        if isinstance(descriptor, FileDescriptor):
            # Use services_by_name instead of services
            services.extend(descriptor.services_by_name.values())

    if not services:
        for name, obj in module.__dict__.items():
            if isinstance(obj, type) and hasattr(obj, "DESCRIPTOR"):
                services.extend(obj.DESCRIPTOR.services_by_name.values())  # type: ignore

    return services


def get_message_schema(message_type) -> Dict[str, Any]:
    schema = {"type": message_type.DESCRIPTOR.full_name, "fields": []}
    for field in message_type.DESCRIPTOR.fields:
        field_info = {
            "name": field.name,
            "type": get_field_type(field.type),
            "repeated": field.label == field.LABEL_REPEATED,
        }
        if field.type == 11:  # 11 represents a nested message
            nested_message = _sym_db.GetPrototype(field.message_type)
            field_info["nested_schema"] = get_message_schema(nested_message)
        elif field.type == 14:  # 14 represents an enum
            field_info["enum_values"] = [
                (enum_value.name, enum_value.number)
                for enum_value in field.enum_type.values
            ]
        schema["fields"].append(field_info)
    return schema


def inspect_method(method: MethodDescriptor) -> Dict[str, Any]:
    input_message = _sym_db.GetPrototype(method.input_type)
    output_message = _sym_db.GetPrototype(method.output_type)

    return {
        "name": method.name,
        "input_schema": get_message_schema(input_message),
        "output_schema": get_message_schema(output_message),
    }


def main() -> None:
    """Prints the gRPC methods and their input and output schemas."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--module",
        type=str,
        default="services.chat_host.chat_pb2",
        help="The module to inspect. E.g. services.chat_host.chat_pb2, services.content_manager.content_manager_pb2",
    )
    args = parser.parse_args()

    if args.module not in PB2_MODULES:
        print(
            f"Error: {args.module} is not a valid module. Available modules are: {', '.join(PB2_MODULES)}"
        )
        return

    try:
        module = importlib.import_module(args.module)
    except ImportError as e:
        print(f"Error importing module {args.module}: {e}")
        return

    services = get_services(module)
    if not services:
        print(f"No services found in the {module.__name__} module.")
        return

    def print_schema(schema, indent, field_name):
        print(" " * indent + f"{field_name}: {schema['type']}")
        for field in schema["fields"]:
            field_type = field["type"]
            if field["repeated"]:
                field_type = f"repeated {field_type}"
            if "nested_schema" in field:
                print(" " * (indent + 2) + f"{field['name']}: {field_type}")
                print_schema(field["nested_schema"], indent + 4, "[nested]")
            elif "enum_values" in field:
                print(" " * (indent + 2) + f"{field['name']}: {field_type}")
                print(" " * (indent + 4) + "Enum values:")
                for enum_name, enum_number in field["enum_values"]:
                    print(" " * (indent + 6) + f"{enum_name}: {enum_number}")
            else:
                print(" " * (indent + 2) + f"{field['name']}: {field_type}")

    for service in services:
        print(f"\nService: {service.name}")
        for method in service.methods:
            method_info = inspect_method(method)
            print(f"\n  Method: {method_info['name']}")
            print_schema(method_info["input_schema"], 4, "[method-input]")
            print_schema(method_info["output_schema"], 4, "[method-output]")


if __name__ == "__main__":
    main()
