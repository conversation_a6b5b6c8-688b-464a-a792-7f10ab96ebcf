def has_close_elements(numbers: list[float], threshold: float) -> bool:
    """ Check if in given list of numbers, are any two numbers closer to each other than
    given threshold.
    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)
    False
    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)
    True
    """
    # two nested loops to check all pairs of numbers
    for idx, elem in enumerate(numbers):
        for idx2, elem2 in enumerate(numbers):
            if idx != idx2:
                return


def test_has_close_elements():
    assert has_close_elements([1.0, 2.0, 3.0],0.5) == False
    assert has_close_elements([1.0, 2.8, 3.0
