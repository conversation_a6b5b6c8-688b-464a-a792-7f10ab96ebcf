def hello():
    print("Hello World!")


def forward(turtle):
    turtle.x += turtle.dx
    turtle.y += turtle.dy


def backward(turtle):
    turtle.x -= turtle.dx
    turtle.y -= turtle.dy


@dataclass
class Turtle:
# START COMPLETION
    x: float = 0.0
    y: float = 0.0
    dx: float = 1.0
    dy: float = 0.0
# END COMPLETION


def turn_right(turtle):
# START COMPLETION
    turtle.dx, turtle.dy = turtle.dy, -turtle.dx
# END COMPLETION


def turn_left(turtle):
# START COMPLETION
    turtle.dx, turtle.dy = -turtle.dy, turtle.dx
# END COMPLETION


def test_turn_right():
# START COMPLETION
    turtle = Turtle()
    turn_right(turtle)
    assert turtle.dx == 0.0
    assert turtle.dy == -1.0
    turn_right(turtle)
    assert turtle.dx == -1.0
    assert turtle.dy == 0.0
    turn_right(turtle)
    assert turtle.dx == 0.0
    assert turtle.dy == 1.0
    turn_right(turtle)
    assert turtle.dx == 1.0
    assert turtle.dy == 0.0
# END COMPLETION


def draw_square(turtle):
# START COMPLETION
    for i in range(4):
        forward(turtle)
        turn_right(turtle)
# END COMPLETION


def turn_angle(turtle, angle):
    """Turn by the given amount"""
    pass


def draw_triangle(turtle):
    # start pseudocode
    compute rotation angle for triangle: 2*pi/3
    move forward, turn
    move forward, turn
    move forward, turn
    # end pseudocode


# [[[Example converted to 100% valid Python code]]]


def forward(turtle):
    turtle.x += turtle.dx
    turtle.y += turtle.dy


def backward(turtle):
    turtle.x -= turtle.dx
    turtle.y -= turtle.dy


@dataclass
class Turtle:
    x: float = 0.0
    y: float = 0.0
    dx: float = 1.0
    dy: float = 0.0


def turn_right(turtle):
    turtle.dx, turtle.dy = turtle.dy, -turtle.dx


def turn_left(turtle):
    turtle.dx, turtle.dy = -turtle.dy, turtle.dx


def test_turn_right():
    turtle = Turtle()
    turn_right(turtle)
    assert turtle.dx == 0.0
    assert turtle.dy == -1.0
    turn_right(turtle)
    assert turtle.dx == -1.0
    assert turtle.dy == 0.0
    turn_right(turtle)
    assert turtle.dx == 0.0
    assert turtle.dy == 1.0
    turn_right(turtle)
    assert turtle.dx == 1.0

def draw_square(turtle):
    for i in range(4):
        forward(turtle)
        turn_right(turtle)


def turn_angle(turtle, angle):
    """Turn by the given amount"""
    pass


def draw_triangle(turtle):
# START COMPLETION
    turn_angle(turtle, 2 * math.pi / 3)
    forward(turtle)
    turn_angle(turtle, 2 * math.pi / 3)
    forward(turtle)
    turn_angle(turtle, 2 * math.pi / 3)
# END COMPLETION
