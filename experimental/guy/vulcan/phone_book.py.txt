"""Implement a phone book stored on disk."""

from pathlib import Path


class PhoneBook:
    """Implement a phone book stored on disk."""

    def __init__(self, path: Path) -> None:
        """Initialize the phone book."""
        self.path = path

    def add(self, name: str, phone: str) -> None:
        """Add a new entry to the phone book."""
        with open(self.path, "a") as file:
            file.write(f"{name} {phone}\n")

    def search(self, name: str) -> str:
        """Search for a name in the phone book."""
        with open(self.path, "r") as
