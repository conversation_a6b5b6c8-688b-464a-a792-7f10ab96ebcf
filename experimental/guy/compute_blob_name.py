"""Compute the blob name of a file."""

import argparse
from pathlib import Path

from base.blob_names.python.blob_names import get_blob_name


def main():
    """Main entry function."""
    parser = argparse.ArgumentParser()
    parser.add_argument("path", help="Compute the blob name of this file")
    args = parser.parse_args()

    print(get_blob_name(args.path, Path(args.path).read_bytes()))


if __name__ == "__main__":
    main()
