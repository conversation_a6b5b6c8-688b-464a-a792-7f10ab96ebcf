import argparse
import json
from pathlib import Path

from humaneval_utils import format_sample


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("input", type=Path, help="jsonl file containing samples")
    args = parser.parse_args()

    for line in args.input.open("r", encoding="utf8"):
        sample = json.loads(line)
        print(format_sample(sample))


if __name__ == "__main__":
    main()
