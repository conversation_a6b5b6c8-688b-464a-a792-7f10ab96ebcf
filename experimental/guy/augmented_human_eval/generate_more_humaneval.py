"""Generate more human eval data with few-shot prompting."""

import argparse
import json
import os
from pathlib import Path
import random
from typing import Iterable, Optional

from humaneval_utils import (
    load_humaneval_samples,
    format_sample,
    check_sample_correctness,
    parse_formatted_sample,
)

from research.data.synthetic_code_edit.api_lib import GptWrapper


def generate_sample(
    gpt_wrapper: GptWrapper,
    model: str,
    seed_humaneval_samples: list[dict],
    existing_samples: list[dict],
    num_shots: int,
    num_turns: int = 3,
) -> list[dict]:
    """Generate a valid sample.

    If num_attempts > 1, will ask the model to fix the sample if it fails.
    """
    question = """\
Here are a few examples of well-written and correct python functions. Each
function is followed by a "check(candidate)" function which unit tests the
candidate function.  Please write another such example. Answer with "Example:"
followed by the example function and the check function.

Do not generate the following functions: {entry_points}
"""
    shot_samples = random.choices(seed_humaneval_samples, k=num_shots)
    shots = [format_sample(sample) for sample in shot_samples]
    existing_entry_points = ", ".join(
        [sample["entry_point"] for sample in existing_samples]
    )

    prompt = (
        question.format(entry_points=existing_entry_points) + "\n" + "\n\n".join(shots)
    )

    dialog = [prompt]
    generated_samples = []

    for _ in range(num_turns):
        response_text = gpt_wrapper(dialog, model=model)
        assert isinstance(response_text, str)

        try:
            response_sample = parse_formatted_sample(response_text)
        except ValueError:
            print("Failed to parse sample")
            break

        correctness = check_sample_correctness(response_sample)

        if correctness.did_pass:
            extra = {
                "model": model,
                "prompt": prompt,
                "shot_samples": shot_samples,
                "shots": shots,
                "response_text": response_text,
            }
            response_sample.update({"extra": extra})
            generated_samples.append(response_sample)
            print("Got a good one!")
        else:
            print("Got a bad one that didn't pass the tests :(")

        dialog.append(response_text)
        dialog.append("Generate another one.")

    return generated_samples


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        default="gpt-4-1106-preview",
        help="model name",
    )
    parser.add_argument(
        "--num_shots",
        type=int,
        default=5,
        help="number of shots in prompt",
    )
    parser.add_argument(
        "--num_samples_to_generate",
        type=int,
        default=5,
        help="number of samples to generate",
    )
    parser.add_argument(
        "--output",
        type=Path,
        default=Path("generated_humaneval.jsonl"),
        help="where to save the results",
    )
    parser.add_argument(
        "--resume",
        action="store_true",
        help="whether to resume from the output file",
    )
    parser.add_argument(
        "--seed",
        type=int,
        default=42,
        help="random seed",
    )
    args = parser.parse_args()

    generated_samples = []

    if args.output.exists():
        if args.resume:
            for line in args.output.open("r", encoding="utf8"):
                generated_samples.append(json.loads(line))
            print(f"Loaded {len(generated_samples)} samples from {args.output}")
        else:
            raise ValueError(f"Output file {args.output} already exists")

    random.seed(args.seed)

    os.environ["OPENAI_API_KEY"] = (
        Path(os.environ["HOME"], ".config/openai/api_key")
        .read_text(encoding="utf8")
        .strip()
    )

    gpt_wrapper = GptWrapper()

    humaneval_samples = load_humaneval_samples()

    with args.output.open("a", encoding="utf8") as output_file:
        while len(generated_samples) < args.num_samples_to_generate:
            all_samples = humaneval_samples + generated_samples

            new_samples = generate_sample(
                gpt_wrapper,
                args.model,
                seed_humaneval_samples=all_samples,
                existing_samples=generated_samples,
                num_shots=args.num_shots,
            )

            print(f"Call generated {len(new_samples)} new samples")

            for generated_sample in new_samples:
                if generated_sample["entry_point"] in [
                    sample["entry_point"] for sample in generated_samples
                ]:
                    print(
                        f"Skipping duplicate entry point {generated_sample['entry_point']}"
                    )
                    continue
                print(format_sample(generated_sample))
                print()

                generated_sample["task_id"] = len(generated_samples)
                generated_samples.append(generated_sample)
                output_file.write(json.dumps(generated_sample) + "\n")
                output_file.flush()

                print(
                    f"Generated {len(generated_samples)} out of {args.num_samples_to_generate} samples"
                )
                print(f"Total price: ${gpt_wrapper.get_stats()['total_price']:.2f}")

    print("Done")


if __name__ == "__main__":
    main()
