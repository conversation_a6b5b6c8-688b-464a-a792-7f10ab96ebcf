{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import os\n", "import pandas\n", "\n", "from research.data.synthetic_code_edit.api_lib import GptWrapper\n", "\n", "gpt_wrapper = GptWrapper()\n", "# model = \"gpt-3.5-turbo-1106\"\n", "# model = \"gpt-4-0125-preview\"\n", "model = \"gpt-4-1106-preview\"\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = (\n", "    Path(os.environ[\"HOME\"], \".config/openai/api_key\")\n", "    .read_text(encoding=\"utf8\")\n", "    .strip()\n", ")\n", "\n", "humaneval_data_file = Path(\"/mnt/efs/augment/user/guy/openai_humaneval/openai_humaneval/test-00000-of-00001.parquet\")\n", "\n", "df = pandas.read_parquet(humaneval_data_file)\n", "human_eval_samples = df.to_dict(orient=\"records\")\n", "\n", "print(list(human_eval_samples[0].keys()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for sample in human_eval_samples:\n", "    print(sample[\"prompt\"] + sample[\"canonical_solution\"])\n", "# entry_points = [sample[\"entry_point\"] for sample in human_eval_samples]\n", "# for entry_point in sorted(entry_points):\n", "#     print(entry_point)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "from humaneval_utils import load_humaneval_samples, format_sample, check_sample_correctness\n", "\n", "# samples = load_humaneval_samples()\n", "\n", "samples = [json.loads(line) for line in Path(\"generated_humaneval_samples.jsonl\").open(\"r\")]\n", "\n", "corrects = [check_sample_correctness(sample)[0] for sample in samples]\n", "\n", "print(corrects)\n", "\n", "print(f\"{sum(corrects)} out of {len(corrects)} correct\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "\n", "for i, line in enumerate(Path(\"generated_humaneval_samples.jsonl\").open(\"r\")):\n", "    generated_sample = json.loads(line)\n", "    print(f\"Sample {i}:\")\n", "    print(format_sample(generated_sample))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}