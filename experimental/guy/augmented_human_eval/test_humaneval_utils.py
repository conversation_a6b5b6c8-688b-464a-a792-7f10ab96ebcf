import json
from humaneval_utils import (
    format_sample,
    parse_formatted_sample,
    load_humaneval_samples,
    clean_sample,
)


def test_format_unformat_sample():
    samples = load_humaneval_samples()
    for sample in samples:
        formatted_sample = format_sample(sample)
        unformatted_sample = parse_formatted_sample(formatted_sample)
        for key, value in unformatted_sample.items():
            expected = clean_sample(sample)[key]
            assert (
                expected.strip() == value.strip()
            ), f"{key}:\n{expected}\n.... != ....\n{value}"
