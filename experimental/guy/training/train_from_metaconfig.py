"""Train models starting from a meta-config file.

A meta-config is a YAML file that completely specifies an experiment definition.
It can include other YAML config files, and also allows for inline configs.
For example:

includes:
  - /mnt/efs/augment/configs/codegen-H/lr/3e-4.yml
  - /mnt/efs/augment/configs/codegen-H/train/big_batch.f.p4.yml
  - /mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml
overrides:
  save: /mnt/efs/augment/checkpoints/my_experiment/
  train_batch_size: 288
  train_micro_batch_size_per_gpu: 1
  gradient_accumulation_steps: 3

These are processed in order, so that param values are overwritten if they
appear in later files or in the inline configs.
"""

import argparse
import os
from pathlib import Path

import git
import yaml

CONFIG_FILENAME = "/tmp/train_config.yml"
TRAIN_SCRIPT_FILENAME = "/tmp/train.sh"


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--metaconfig",
        "-mc",
        type=str,
        required=True,
        help="path to a metaconfig yaml file with the experiment definition",
    )
    parser.add_argument(
        "--output_config",
        type=str,
        default="/tmp/train_config.yml",
        help="where to save the override configs",
    )
    parser.add_argument(
        "--output_train_script",
        type=str,
        default="/tmp/train.sh",
        help="where to save the training shell script",
    )
    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="dry run, save output files but do not train",
    )
    args = parser.parse_args()

    with Path(args.metaconfig).open("r", encoding="utf8") as file:
        metaconfig = yaml.safe_load(file)

    save_dir = metaconfig["overrides"]["save"]

    if Path(save_dir).exists() and not args.dry_run:
        print((f"Save directory {save_dir} already exists, aborting"))
        return

    includes = " ".join(metaconfig["includes"])

    with Path(args.output_config).open("w", encoding="utf8") as config_file:
        yaml.dump(metaconfig["overrides"], config_file)
    print(f"Saved training config in {args.output_config}")

    gpt_neox_dir = (
        git.Repo(".", search_parent_directories=True).working_tree_dir
        + "/research/gpt-neox"
    )
    if not Path(gpt_neox_dir).exists():
        raise ValueError(f"gpt-neox dir doens't exist: {gpt_neox_dir}")

    train_script = f"""
mkdir -p {save_dir}
cd {gpt_neox_dir}
python ./deepy.py train.py {includes} {args.output_config}
"""

    with Path(args.output_train_script).open("w", encoding="utf-8") as script_file:
        script_file.write(train_script)
    print(f"Saved training shell script in {args.output_train_script}")

    if args.dry_run:
        print("Dry run, not actually training")
    else:
        print("Launching training...")
        os.system(f"chmod +x {args.output_train_script}")
        result = os.system(f"/bin/bash -c {args.output_train_script}")
        if result:
            raise ValueError("Training run failed, aborting")


if __name__ == "__main__":
    main()
