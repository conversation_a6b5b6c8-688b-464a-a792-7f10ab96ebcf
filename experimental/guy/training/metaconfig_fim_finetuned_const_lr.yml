includes:
- /mnt/efs/augment/configs/codegen-H/lr/3e-4.yml
- /mnt/efs/augment/configs/codegen-H/train/big_batch.f.p4.yml
- /mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml
overrides:
    wandb_name: FIM FT seq:1024 sched:const lr:3.2e-5 beta2:0.95
    save: /mnt/efs/augment/checkpoints/fim/test5_ft_beta0.95_s1024_lr3.2e-5_const

    data_path: /mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document
    #train_data_paths: [/mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document]
    #valid_data_paths: []
    #test_data_paths: []

    load: /mnt/efs/augment/checkpoints/codegen-2B-multi

    train_batch_size: 384
    train_micro_batch_size_per_gpu: 48
    gradient_accumulation_steps: 1
    seq_length: 1024

    eval_interval: 1000
    early_stopping: False
    early_stopping_threshold: 0.005

    train_iters: 50000
    lr_decay_iters: 50000
    warmup: 0.
    lr_decay_style: constant

    fim_probability: 0.5

    optimizer:
        params:
            betas:
            - 0.9
            - 0.95
            eps: 1.0e-08
            lr: 3.2e-05
        type: Adam

    save_interval: 1000
    keep_last_n_checkpoints: 1
    # to keep all checkpoints
    #keep_last_n_checkpoints: null
    no_save_optim: False
