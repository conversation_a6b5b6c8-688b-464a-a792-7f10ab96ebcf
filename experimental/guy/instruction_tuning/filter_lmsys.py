"""Filter the lmsys-chat-1m dataset.

Keep conversations that are about software engineering, in English, and that
are not obviously for synthetic data generation.
"""

from pathlib import Path
import json

import pandas


def should_skip(conversation: list):
    first_question = conversation[0]["content"]
    if "assistant:" in first_question:
        return True
    return False


def main():
    code_classification_path = Path(
        "/mnt/efs/augment/user/guy/lmsys-chat-1m/code_classification.jsonl"
    )
    code_conversation_ids = set()

    total_samples = 0

    for line in code_classification_path.open("r", encoding="utf8"):
        data = json.loads(line)
        total_samples += 1
        if data["is_code"]:
            code_conversation_ids.add(data["conversation_id"])

    print(
        f"{len(code_conversation_ids)} / {total_samples} ({len(code_conversation_ids) / total_samples * 100:.2f}%) are code"
    )

    df = pandas.read_parquet("/mnt/efs/augment/user/guy/lmsys-chat-1m/data")
    print(f"Loaded {len(df)} rows")

    df = df[df["conversation_id"].isin(code_conversation_ids)]
    print(f"After code filtering: {len(df)}")

    df = df[~df["conversation"].apply(should_skip)]
    print(f"After skipping: {len(df)}")

    df.to_parquet(
        "/mnt/efs/augment/user/guy/lmsys-chat-1m/lmsys_chat_1m_code_only.parquet"
    )

    print(f"Saved {len(df)} rows")


if __name__ == "__main__":
    main()
