from pathlib import Path
import json
from requests.exceptions import HTTPError
import pandas
import threading

from research.llm_apis.chat_utils import (
    Llama3ChatClient,
    ChatClient,
    run_health_checks,
)
from experimental.guy.apis.process_chat_tasks import (
    process_tasks,
    TaskProcessorInput,
    load_addresses_from_yaml,
)

code_classification_path = Path(
    "/mnt/efs/augment/user/guy/lmsys-chat-1m/code_classification.jsonl"
)
write_lock = threading.Lock()


def task_processor_fn(task_input: TaskProcessorInput) -> None:
    row: dict = task_input.task_data
    full_convo = ""

    for turn in row["conversation"]:
        full_convo += f"{turn['role']}:\n"
        full_convo += turn["content"] + "\n\n"

    prompt = f"""\
Is the following conversation an English dialog about software engineering? Answer Yes or No.

{full_convo}

Is the conversation above an English dialog about software engineering? Answer Yes or No.
"""

    try:
        response = task_input.chat_client.generate(messages=[prompt], max_tokens=32)
    except HTTPError as e:
        print(f"HTTP error: {e}")
        return

    if "yes" in response.lower():
        is_code = True
    elif "no" in response.lower():
        is_code = False
    else:
        print(prompt)
        print(f"Length: {len(prompt)}")
        print("Response:")
        print(response)
        raise ValueError(f"Unexpected response: {response}")

    with write_lock:
        result = json.dumps(
            {
                "conversation_id": row["conversation_id"],
                "is_code": is_code,
            }
        )
        with code_classification_path.open("a", encoding="utf8") as output_file:
            output_file.write(result + "\n")
        print(result)


def main():
    addresses = load_addresses_from_yaml(
        yaml_file="/home/<USER>/augment/experimental/guy/apis/triton_server_addresses.yaml"
    )
    clients: list[ChatClient] = [
        Llama3ChatClient("triton", address=address, timeout=180)
        for address in addresses
    ]
    run_health_checks(clients)

    path = Path(
        # "/mnt/efs/augment/user/guy/lmsys-chat-1m/data/train-00000-of-00006-4feeb3f83346a0e9.parquet"
        "/mnt/efs/augment/user/guy/lmsys-chat-1m/data"
    )

    print("\nLoading data...")
    df = pandas.read_parquet(path)
    print(f"Loaded {len(df)} rows")
    rows = df.to_dict(orient="records")

    if code_classification_path.exists():
        already_classified = set()
        for line in code_classification_path.open("r", encoding="utf8"):
            data = json.loads(line)
            already_classified.add(data["conversation_id"])
        rows = [row for row in rows if row["conversation_id"] not in already_classified]
        print(
            f"Skipping {len(already_classified)} already classified rows, {len(rows)} remaining"
        )

    process_tasks(chat_clients=clients, tasks=rows, task_processor_fn=task_processor_fn)


if __name__ == "__main__":
    main()
