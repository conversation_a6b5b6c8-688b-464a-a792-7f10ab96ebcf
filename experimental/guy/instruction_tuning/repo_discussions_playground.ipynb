{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 9\u001b[0m\n\u001b[1;32m      6\u001b[0m input_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m      7\u001b[0m pr_repo_reference_file \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m----> 9\u001b[0m repo_file_manager \u001b[38;5;241m=\u001b[39m \u001b[43mRepositoryFileManager\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpr_repo_reference_parquet_file\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPath\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpr_repo_reference_file\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     11\u001b[0m \u001b[43m)\u001b[49m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m parquet_file \u001b[38;5;129;01min\u001b[39;00m Path(input_path)\u001b[38;5;241m.\u001b[39mglob(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m*.parquet\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m     14\u001b[0m     data \u001b[38;5;241m=\u001b[39m pandas\u001b[38;5;241m.\u001b[39mread_parquet(parquet_file)\n", "File \u001b[0;32m~/augment/research/data/spark/pipelines/utils/prs_dataset.py:24\u001b[0m, in \u001b[0;36mRepositoryFileManager.__init__\u001b[0;34m(self, pr_repo_reference_parquet_file)\u001b[0m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, pr_repo_reference_parquet_file: Path):\n\u001b[0;32m---> 24\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpr_repo_join_data \u001b[38;5;241m=\u001b[39m \u001b[43mpandas\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_parquet\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m     25\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mpr_repo_reference_parquet_file\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     26\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     28\u001b[0m     \u001b[38;5;66;03m# repo_name -> file_sha -> content\u001b[39;00m\n\u001b[1;32m     29\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrepo_files: \u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, \u001b[38;5;28mstr\u001b[39m]] \u001b[38;5;241m=\u001b[39m {}\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/pandas/io/parquet.py:667\u001b[0m, in \u001b[0;36mread_parquet\u001b[0;34m(path, engine, columns, storage_options, use_nullable_dtypes, dtype_backend, filesystem, filters, **kwargs)\u001b[0m\n\u001b[1;32m    664\u001b[0m     use_nullable_dtypes \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[1;32m    665\u001b[0m check_dtype_backend(dtype_backend)\n\u001b[0;32m--> 667\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mimpl\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    668\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    669\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    670\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfilters\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfilters\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    671\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    672\u001b[0m \u001b[43m    \u001b[49m\u001b[43muse_nullable_dtypes\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_nullable_dtypes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    673\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdtype_backend\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype_backend\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    674\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfilesystem\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfilesystem\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    675\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    676\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/pandas/io/parquet.py:267\u001b[0m, in \u001b[0;36mPyArrowImpl.read\u001b[0;34m(self, path, columns, filters, use_nullable_dtypes, dtype_backend, storage_options, filesystem, **kwargs)\u001b[0m\n\u001b[1;32m    264\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m manager \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124marray\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    265\u001b[0m     to_pandas_kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msplit_blocks\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m  \u001b[38;5;66;03m# type: ignore[assignment]\u001b[39;00m\n\u001b[0;32m--> 267\u001b[0m path_or_handle, handles, filesystem \u001b[38;5;241m=\u001b[39m \u001b[43m_get_path_or_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    268\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    269\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfilesystem\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    270\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    271\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrb\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m    272\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    273\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    274\u001b[0m     pa_table \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mapi\u001b[38;5;241m.\u001b[39mparquet\u001b[38;5;241m.\u001b[39mread_table(\n\u001b[1;32m    275\u001b[0m         path_or_handle,\n\u001b[1;32m    276\u001b[0m         columns\u001b[38;5;241m=\u001b[39mcolumns,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    279\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs,\n\u001b[1;32m    280\u001b[0m     )\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/pandas/io/parquet.py:140\u001b[0m, in \u001b[0;36m_get_path_or_handle\u001b[0;34m(path, fs, storage_options, mode, is_dir)\u001b[0m\n\u001b[1;32m    130\u001b[0m handles \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    131\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[1;32m    132\u001b[0m     \u001b[38;5;129;01mnot\u001b[39;00m fs\n\u001b[1;32m    133\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m is_dir\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    138\u001b[0m     \u001b[38;5;66;03m# fsspec resources can also point to directories\u001b[39;00m\n\u001b[1;32m    139\u001b[0m     \u001b[38;5;66;03m# this branch is used for example when reading from non-fsspec URLs\u001b[39;00m\n\u001b[0;32m--> 140\u001b[0m     handles \u001b[38;5;241m=\u001b[39m \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    141\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpath_or_handle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mis_text\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstorage_options\u001b[49m\n\u001b[1;32m    142\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    143\u001b[0m     fs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m    144\u001b[0m     path_or_handle \u001b[38;5;241m=\u001b[39m handles\u001b[38;5;241m.\u001b[39mhandle\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/pandas/io/common.py:882\u001b[0m, in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    873\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mopen\u001b[39m(\n\u001b[1;32m    874\u001b[0m             handle,\n\u001b[1;32m    875\u001b[0m             ioargs\u001b[38;5;241m.\u001b[39mmode,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    878\u001b[0m             newline\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    879\u001b[0m         )\n\u001b[1;32m    880\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    881\u001b[0m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[0;32m--> 882\u001b[0m         handle \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mioargs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    883\u001b[0m     handles\u001b[38;5;241m.\u001b[39mappend(handle)\n\u001b[1;32m    885\u001b[0m \u001b[38;5;66;03m# Convert BytesIO or file objects passed with an encoding\u001b[39;00m\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet'"]}], "source": ["from pathlib import Path\n", "import pandas\n", "\n", "from research.data.spark.pipelines.utils.prs_dataset import RepositoryFileManager\n", "\n", "input_path = \"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\"\n", "pr_repo_reference_file = \"/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet\"\n", "\n", "repo_file_manager = RepositoryFileManager(\n", "    pr_repo_reference_parquet_file=Path(pr_repo_reference_file)\n", ")\n", "\n", "for parquet_file in Path(input_path).glob(\"*.parquet\"):\n", "    data = pandas.read_parquet(parquet_file)\n", "    print(len(data))\n", "    break\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}