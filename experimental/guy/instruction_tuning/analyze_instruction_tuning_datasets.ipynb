{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# evol_codealpaca_v1 : /mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1\n", "# glaive_code_assistant: /mnt/efs/augment/user/yury/theblackcat102/glaive_code_assistant\n", "# multiround_programming_convo: /mnt/efs/augment/user/yury/theblackcat102/multiround_programming_convo\n", "\n", "import json\n", "from pathlib import Path\n", "\n", "dataset_paths = {\n", "    \"evol_codealpaca_v1\": Path(\n", "        \"/mnt/efs/augment/user/yury/theblackcat102/evol_codealpaca_v1\"\n", "    ),\n", "    \"glaive_code_assistant\": Path(\n", "        \"/mnt/efs/augment/user/yury/theblackcat102/glaive_code_assistant\"\n", "    ),\n", "    \"multiround_programming_convo\": Path(\n", "        \"/mnt/efs/augment/user/yury/theblackcat102/multiround_programming_convo\"\n", "    ),\n", "}\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "from textwrap import fill\n", "\n", "from pygments import highlight\n", "from pygments.lexers import PythonLexer\n", "from pygments.formatters import TerminalFormatter\n", "\n", "def print_instruction_output(instruction_output_dict):\n", "    for k, v in instruction_output_dict.items():\n", "        print(f\"{k}:\\n{fill(v, width=100, replace_whitespace=False)}\\n\")\n", "    # instruction = instruction_output_dict[\"instruction\"]\n", "    # output_code = instruction_output_dict[\"output\"]\n", "\n", "    # print(f\"Instruction:\")\n", "    # print(fill(instruction, width=100))\n", "    # print(\"\\nOutput:\")\n", "    # print(output_code)\n", "\n", "    # print(\"Output:\")\n", "    # lexer = PythonLexer()\n", "    # formatter = TerminalFormatter(linenos=True, cssclass=\"code\")\n", "    # highlighted_code = highlight(output_code, lexer, formatter)\n", "    # print(highlighted_code)\n", "\n", "dataset = \"evol_codealpaca_v1\"\n", "# dataset = \"glaive_code_assistant\"\n", "# dataset = \"multiround_programming_convo\"\n", "\n", "samples = []\n", "with open(dataset_paths[dataset] / \"train.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line)\n", "        samples.append(data)\n", "\n", "\n", "random.seed(42)\n", "samples_subset = random.sample(samples, 10)\n", "\n", "\n", "for sample in samples_subset:\n", "    print()\n", "    print(\"=\" * 120)\n", "    print()\n", "    print_instruction_output(sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "from textwrap import fill\n", "import termcolor\n", "\n", "\n", "# dataset = \"evol_codealpaca_v1\"\n", "# dataset = \"glaive_code_assistant\"\n", "dataset = \"multiround_programming_convo\"\n", "\n", "samples = []\n", "with open(dataset_paths[dataset] / \"train.jsonl\") as f:\n", "    for line in f:\n", "        data = json.loads(line)\n", "        samples.append(data)\n", "\n", "\n", "random.seed(42)\n", "samples_subset = random.sample(samples, 10)\n", "\n", "for sample in samples_subset:\n", "    print()\n", "    print(\"=\" * 120)\n", "    print()\n", "\n", "    for turn in sample[\"conversations\"]:\n", "        termcolor.cprint(f\"{turn['from']}:\", \"green\")\n", "        # print(f\"{turn['from']}:\")\n", "        print(f\"{fill(turn['text'], width=100, replace_whitespace=False)}\")\n", "        print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# lmsys-1m\n", "\n", "1M conversations. https://huggingface.co/datasets/lmsys/lmsys-chat-1m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "import termcolor\n", "from textwrap import fill\n", "\n", "import pandas\n", "\n", "code_classification_path = Path(\"/mnt/efs/augment/user/guy/lmsys-chat-1m/code_classification.jsonl\")\n", "code_conversation_ids = set()\n", "\n", "total_samples = 0\n", "\n", "for line in code_classification_path.open(\"r\"):\n", "    data = json.loads(line)\n", "    total_samples += 1\n", "    if data[\"is_code\"]:\n", "        code_conversation_ids.add(data[\"conversation_id\"])\n", "\n", "print(f\"{len(code_conversation_ids)} / {total_samples} ({len(code_conversation_ids) / total_samples * 100:.2f}%) are code\")\n", "\n", "# df = pandas.read_parquet(\"/mnt/efs/augment/user/guy/lmsys-chat-1m/data\")\n", "df = pandas.read_parquet(\"/mnt/efs/augment/user/guy/lmsys-chat-1m/data/train-00000-of-00006-4feeb3f83346a0e9.parquet\")\n", "df = df[df[\"conversation_id\"].isin(code_conversation_ids)]\n", "\n", "# df.to_parquet(\"/mnt/efs/augment/user/guy/lmsys-chat-1m/lmsys_chat_1m_code_only.parquet\")\n", "\n", "def should_skip(conversation: list):\n", "    first_question = conversation[0][\"content\"]\n", "    if \"assistant:\" in first_question:\n", "        return True\n", "    return False\n", "\n", "for row in df.to_dict(orient=\"records\"):\n", "    print(\"=\" * 120)\n", "    termcolor.cprint(row[\"conversation_id\"], \"blue\")\n", "\n", "    if should_skip(row[\"conversation\"]):\n", "        continue\n", "\n", "    for turn in row[\"conversation\"]:\n", "        termcolor.cprint(f\"{turn['role']}:\", \"green\")\n", "        print(f\"{fill(turn['content'], width=100, replace_whitespace=False)}\")\n", "        print()\n", "        # break  # TODO(guy)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}