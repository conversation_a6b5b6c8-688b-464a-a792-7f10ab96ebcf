{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas\n", "\n", "input_root = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\")\n", "output_root = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_triton\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "lengths = []\n", "\n", "# parquet_files = list(output_root.glob(\"*.parquet\"))\n", "parquet_files = list(input_root.glob(\"*.parquet\"))\n", "random.shuffle(parquet_files)\n", "\n", "for i, path in enumerate(parquet_files):\n", "    df = pandas.read_parquet(path, columns=[\"id\"])\n", "    lengths.append(len(df))\n", "\n", "    avg_rows_per_file = sum(lengths) / len(lengths)\n", "    extrapolated_total_rows = avg_rows_per_file * len(parquet_files)\n", "    print(f\"{i} of {len(parquet_files)}: avg_samples_per_file: {avg_rows_per_file:.0f} extrapolated_total_samples: {extrapolated_total_rows:.0f}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i, input_path in enumerate(input_root.glob(\"*.parquet\")):\n", "    output_path = output_root / input_path.name\n", "\n", "    if not output_path.exists():\n", "        print(f\"Skipping {input_path} because {output_path} does not exist.\")\n", "        continue\n", "\n", "    input_df = pandas.read_parquet(input_path)\n", "    output_df = pandas.read_parquet(output_path)\n", "\n", "\n", "\n", "    pr_result = {**sample.to_dict(), **generated_instructions.to_dict()}  # pylint: disable=no-member\n", "\n", "    print(input_df.columns)\n", "    print(output_df.columns)\n", "    break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas\n", "\n", "input_root = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\")\n", "output_root = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_triton\")\n", "\n", "columns = [\"title\", \"body\", \"pr_diff\"]\n", "\n", "def pr_diff_lengths(df):\n", "    return [len(row[\"pr_diff\"]) for _, row in df.iterrows()]\n", "\n", "def num_pr_diff_chars(df):\n", "    return sum(pr_diff_lengths)\n", "\n", "all_input_pr_diff_lengths = []\n", "all_output_pr_diff_lengths = []\n", "\n", "all_input_diff_chars = 0\n", "all_output_diff_chars = 0\n", "\n", "all_input_rows = 0\n", "all_output_rows = 0\n", "\n", "for i, input_path in enumerate(input_root.glob(\"*.parquet\")):\n", "    output_path = output_root / input_path.name\n", "\n", "    if not output_path.exists():\n", "        print(f\"Skipping {input_path} because {output_path} does not exist.\")\n", "        continue\n", "\n", "    input_df = pandas.read_parquet(input_path, columns=columns)\n", "    output_df = pandas.read_parquet(output_path, columns=columns)\n", "\n", "    print(type(output_df.to_dict(orient=\"records\")))\n", "    print(type(output_df.to_dict(orient=\"records\")[0]))\n", "    break\n", "\n", "    all_input_rows += len(input_df)\n", "    all_output_rows += len(output_df)\n", "\n", "    input_pr_diff_lengths = pr_diff_lengths(input_df)\n", "    output_pr_diff_lengths = pr_diff_lengths(output_df)\n", "\n", "    all_input_pr_diff_lengths.extend(input_pr_diff_lengths)\n", "    all_output_pr_diff_lengths.extend(output_pr_diff_lengths)\n", "    \n", "    all_input_diff_chars += sum(input_pr_diff_lengths)\n", "    all_output_diff_chars += sum(output_pr_diff_lengths)\n", "\n", "    # print(f\"output line fraction: {100 * output_diff_chars / input_diff_chars:.1f}%  output char fraction: {100 * output_diff_chars / all_input_diff_chars:.1f}%\")\n", "\n", "    if i > 100:\n", "        break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"output char fraction: {100 * all_output_diff_chars / all_input_diff_chars:.1f}%\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sum([l for l in all_input_pr_diff_lengths if l > 8192*3]) / sum(all_input_pr_diff_lengths)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "bins = np.arange(0, 100000, 100)\n", "\n", "plt.hist(all_input_pr_diff_lengths, bins=bins, label=\"input\")\n", "plt.hist(all_output_pr_diff_lengths, bins=bins, label=\"output\")\n", "\n", "plt.legend()\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate instructions\n", "import json\n", "from pathlib import Path\n", "from unidiff import PatchSet\n", "\n", "import pandas\n", "\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "from experimental.guy.pr_task_descriptions.pr_utils import (\n", "    PRSample,\n", "    generate_pr_instructions_single_turn,\n", "    get_pr_instructions_prompt,\n", ")\n", "from research.core.diff_utils import parse_git_diff_output\n", "\n", "# address = \"*************:8000\"\n", "# client = Llama3ChatClient(\"triton\", address=address, timeout=60)\n", "\n", "input_path = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\")\n", "token_lengths = {}\n", "\n", "def truncate_long_string(s):\n", "    lines = s.splitlines()\n", "    if len(lines) > 20:\n", "        return \"\\n\".join(lines[:10] + [\"...\"] + lines[-10:])\n", "    else:\n", "        return s\n", "\n", "# num_printed = 0\n", "# max_printed = 10\n", "\n", "num_added_lines = []\n", "num_removed_lines = []\n", "\n", "num_parquet_files = 100\n", "\n", "for path in list(input_path.glob(\"*.parquet\"))[:num_parquet_files]:\n", "    df = pandas.read_parquet(path)\n", "    for row in df.to_dict(orient=\"records\"):\n", "        if \"title\" in row and \"body\" in row and \"pr_diff\" in row:\n", "            try:\n", "                diff = parse_git_diff_output(row[\"pr_diff\"])\n", "                for patch_file in diff:\n", "                    for hunk in patch_file:\n", "                        num_added_lines.append(hunk.added)\n", "                        num_removed_lines.append(hunk.removed)\n", "            except:\n", "                pass\n", "\n", "            # prompt, persona = get_pr_instructions_prompt(PRSample.from_dict(row))\n", "            # print(\"\\n=========================== Prompt: =============================\")\n", "            # print(f\"persona: {persona}\")\n", "            # print(f\"prompt:\\n{truncate_long_string(prompt)}\")\n", "            # response = generate_pr_instructions_single_turn(client, prompt, persona)\n", "\n", "            # print(\"\\n=========================== Response: =============================\")\n", "            # print(\"full response:\")\n", "            # print(\"~~~~~~~~\\n\" + response.full_response + \"\\n~~~~~~~~\")\n", "            # print(\"description paragraph:\")\n", "            # print(\"~~~~~~~~\\n\" + response.description_paragraph + \"\\n~~~~~~~~\\n\")\n", "            # print(\"instructions:\")\n", "            # print(json.dumps(response.instructions, indent=2))\n", "\n", "        # if num_printed > max_printed:\n", "        #     break\n", "        # num_printed += 1\n", "    # if num_printed > max_printed:\n", "    #     break\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "print(len(num_added_lines))\n", "print(len(num_removed_lines))\n", "\n", "print(f\"Percent above 10: {sum(1 for l in num_added_lines if l > 10) / len(num_added_lines) * 100}%\")\n", "print(f\"Percent above 1: {sum(1 for l in num_added_lines if l > 1) / len(num_added_lines) * 100}%\")\n", "\n", "print(f\"Removed: Percent above 1: {sum(1 for l in num_removed_lines if l > 1) / len(num_added_lines) * 100}%\")\n", "\n", "# plt.hist(num_added_lines, bins=400)\n", "# plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count rows\n", "import json\n", "from pathlib import Path\n", "import pandas\n", "\n", "from experimental.guy.pr_task_descriptions.pr_utils import get_pr_instructions_prompt, PRSample\n", "\n", "input_path = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_triton\")\n", "token_lengths = {}\n", "\n", "def truncate_long_string(s, max_lines):\n", "    lines = s.splitlines()\n", "    if len(lines) > max_lines:\n", "        return \"\\n\".join(lines[:max_lines//2] + [\"...\"] + lines[-max_lines//2:])\n", "    else:\n", "        return s\n", "\n", "lengths = []\n", "\n", "for path in input_path.glob(\"*.parquet\"):\n", "    df = pandas.read_parquet(path)\n", "    lengths.append(len(df))\n", "    print(len(df))\n", "\n", "print(f\"Total: {sum(lengths)} Avg: {sum(lengths) / len(lengths)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Look at data\n", "import json\n", "from pathlib import Path\n", "import pandas\n", "\n", "from experimental.guy.pr_task_descriptions.pr_utils import get_pr_instructions_prompt, PRSample\n", "\n", "input_path = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_triton\")\n", "token_lengths = {}\n", "\n", "def truncate_long_string(s, max_lines):\n", "    lines = s.splitlines()\n", "    if len(lines) > max_lines:\n", "        return \"\\n\".join(lines[:max_lines//2] + [\"...\"] + lines[-max_lines//2:])\n", "    else:\n", "        return s\n", "\n", "count = 0\n", "max_printed = 10\n", "\n", "for path in input_path.glob(\"*.parquet\"):\n", "    df = pandas.read_parquet(path)\n", "    for row in df.to_dict(orient=\"records\"):\n", "        print(\"Columns:\", list(row))\n", "        prompt = row[\"prompt\"]\n", "\n", "        # The dicts we get from parquet look like this:\n", "        # {\n", "        #   'brief': None,\n", "        #   'detailed': None,\n", "        #   'main': array([\n", "        #       'Add len and is_empty to List.',\n", "        #       'Implement length and emptiness checks for List widget.',\n", "        #       'Create len() and is_empty() methods for the List widget to enable querying its size and emptiness, and write corresponding tests to validate their functionality.'],\n", "        #   ], dtype=object)\n", "        # }\n", "        instructions = {\n", "            persona: list(instructions)\n", "            for persona, instructions in row[\"instructions\"].items()\n", "            if instructions is not None\n", "        }\n", "\n", "        print(\"\\n=========================== Prompt: =============================\")\n", "        # print(f\"prompt:\\n{truncate_long_string(prompt)}\")\n", "        print(f\"prompt:\\n{prompt}\")\n", "\n", "        small_header = \"=~-~\" * 20\n", "\n", "        print(\"\\n=========================== Response: =============================\")\n", "        print(\"full response:\")\n", "        print(f\"{small_header}\\n\" + row[\"full_response\"] + f\"\\n{small_header}\")\n", "        # print(\"description paragraph:\")\n", "        # print(f\"{small_header}\\n\" + row[\"description_paragraph\"] + f\"\\n{small_header}\\n\")\n", "        print(\"instructions:\")\n", "        print(json.dumps(instructions, indent=2))\n", "\n", "        count += 1\n", "        if count >= max_printed:\n", "            break\n", "\n", "    if count >= max_printed:\n", "        break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from pathlib import Path\n", "import pandas\n", "\n", "from experimental.guy.apis.llama3_tokenizer import Tokenizer\n", "\n", "tokenizer_model_path: str = \"/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B-Instruct/tokenizer.model\"\n", "tokenizer = Tokenizer(tokenizer_model_path)\n", "tokenizer.encode(\"hello there\", eos=False, bos=False)\n", "\n", "input_path = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\")\n", "token_lengths = {}\n", "\n", "for path in input_path.glob(\"*.parquet\"):\n", "    df = pandas.read_parquet(path)\n", "    for row in df.to_dict(orient=\"records\"):\n", "        if \"title\" in row and \"body\" in row and \"pr_diff\" in row:\n", "            title = row[\"title\"]\n", "            body = row[\"body\"]\n", "            pr_diff = row[\"pr_diff\"]\n", "            text = f\"{title}\\n{body}\\n{pr_diff}\"\n", "            tokens = tokenizer.encode(text, eos=False, bos=False)\n", "            token_lengths.append(len(tokens))\n", "            print(f\"{len(tokens)} tokens (avg {round(sum(token_lengths) / len(token_lengths))} tokens/sample, max {max(token_lengths)} tokens)\")\n", "        break\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas\n", "\n", "# path = \"/mnt/efs/spark-data/user/guy/data-pipeline/history-of-pr-next-edit-location/04-21-2024/next-edit-location/part-00119-db01ec8b-97c7-42fe-968d-1093b2d53da9-c000.zstd.parquet\"\n", "# root = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_train\")\n", "root = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/pr_instructions_triton\")\n", "\n", "total = 0\n", "\n", "for path in root.glob(\"*.parquet\"):\n", "    print(path)\n", "    df = pandas.read_parquet(path)\n", "    # print(len(df))\n", "    total += len(df)\n", "\n", "print(total)\n", "\n", "# df = pandas.read_parquet(path)\n", "# for c in df.columns:\n", "#     print(c)\n", "\n", "# for row in df.to_dict(orient=\"records\"):\n", "#     print(row[\"wip_to_future_diff\"])\n", "#     break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "path = \"/mnt/efs/spark-data/user/guy/data-pipeline/pr-next-edit-location-v3/next-edit-location-split/split.parquet\"\n", "df = pandas.read_parquet(path)\n", "\n", "for f in df[df[\"split\"] == \"validation\"][\"parquet_file\"]:\n", "    print(f)\n", "    data_df = pandas.read_parquet(f)\n", "    print(len(data_df))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import pandas\n", "\n", "from base.tokenizers import create_tokenizer_by_name\n", "from research.data.spark.pipelines.stages.prs import (\n", "    CreatePrDatasetForNextEditLocationConfig,\n", "    _next_edit_location_pandas_func,\n", ")\n", "\n", "def tokenizer_factory():\n", "    return create_tokenizer_by_name(\"starcoder\")\n", "\n", "config = CreatePrDatasetForNextEditLocationConfig(\n", "    name=Path(\"create_next_edit_location_dataset_from_new_prs_dataset\"),\n", "    input=Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha/\"),\n", "    output=\"next-edit-location-new\",\n", "\n", "    pr_repo_reference_file=\"/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet\",\n", "\n", "    # These are very large repos that cause the pipeline to choke\n", "    skip_repo_names=[\"NixOS/nixpkgs\", \"microsoft/AzureTRE\"],\n", "\n", "    wip_sampler=\"wip_repo_sampler\",\n", "    max_prs_per_repo=10,\n", "    max_wip_states_per_pr=10,\n", "    max_samples_per_repo=1,\n", "\n", "    num_context_lines_in_past_to_wip_diff=5,\n", "    max_lines_per_chunk=30,\n", "\n", "    max_negative_documents_per_sample=512,\n", "    max_positive_documents_per_sample=64,\n", "\n", "    add_path_to_prompt=True,\n", "    max_query_token_length=4096,\n", "    max_document_token_length=1000,\n", "\n", "    # Leaving this here for debugging purposes\n", "    limit_num_input_parquet_files=1,\n", ")\n", "\n", "parquet_path = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha/part-00113-db01ec8b-97c7-42fe-968d-1093b2d53da9-c000.zstd.parquet\")\n", "df = pandas.read_parquet(parquet_path)\n", "\n", "df = _next_edit_location_pandas_func(df, tokenizer_factory, config)\n", "\n", "print(len(df))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.stages.common import load_dataset\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "spark = k8s_session(max_workers=100)\n", "\n", "# df = load_dataset(spark, \"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha/\")\n", "\n", "# print(df.count())\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# count number of rows for each value of repo_name\n", "counts = df.groupBy(\"repo_name\").count()\n", "\n", "# save to a csv\n", "counts.write.json(\"/mnt/efs/spark-data/user/guy/data-pipeline/prs_repo_counts\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "root = Path(\"/mnt/efs/spark-data/user/guy/data-pipeline/prs_repo_counts\")\n", "counts = []\n", "\n", "for path in root.glob(\"*.json\"):\n", "    for line in path.read_text().splitlines():\n", "        datum = json.loads(line)\n", "        counts.append(datum)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "plt.figure()\n", "plt.hist([np.log10(c[\"count\"]) for c in counts], bins=20)\n", "plt.grid()\n", "plt.xlabel(\"Number of PRs per repo (log10)\")\n", "plt.ylabel(\"Number of repos\")\n", "plt.show()\n", "\n", "for max_prs_per_repo in [10, 100, 1000, 10000]:\n", "    total_prs = sum(c[\"count\"] for c in counts)\n", "    num_prs_above_threshold = sum(max(0, c[\"count\"] - max_prs_per_repo) for c in counts)\n", "    # print(num_prs_above_threshold)\n", "    print(f\"Percent of PRs kept for max_prs_per_repo={max_prs_per_repo}:\", 100 * (total_prs - num_prs_above_threshold) / total_prs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "data = json.load(open(\"results.json\"))\n", "print(list(data.keys()))\n", "\n", "print(f\"Total succeeded files: {len(data['succeeded_files'])}\")\n", "print(f\"Total failed files: {len(data['failed_files'])}\")\n", "\n", "plt.figure()\n", "plt.hist(data[\"num_rows\"], bins=80)\n", "plt.xlabel(\"num PRs in file\")\n", "\n", "plt.figure()\n", "plt.hist(np.log10(data[\"file_sizes\"]), bins=80)\n", "plt.xlabel(\"log10(file size)\")\n", "\n", "plt.figure()\n", "# plt.plot(data[\"num_rows\"], data[\"file_sizes\"], \"+\")\n", "plt.plot(data[\"num_rows\"], np.log10(data[\"file_sizes\"]), \"+\")\n", "plt.xlabel(\"num PRs in file\")\n", "plt.ylabel(\"file size\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "import datetime\n", "print(time.strftime('%Y-%m-%d %H:%M:%S.%f'))\n", "print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "\n", "# from research.data.spark.pipelines.stages.prs import (\n", "#     RepositoryFileManager,\n", "#     CreatePrDatasetForNextEditLocationConfig,\n", "#     _process_pr_into_next_edit_location_samples,\n", "# )\n", "\n", "root = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha/\")\n", "\n", "num_rows = []\n", "file_sizes = []\n", "\n", "for parquet_file in root.glob(\"*.parquet\"):\n", "    df = pandas.read_parquet(parquet_file)\n", "    num_rows.append(len(df))\n", "    file_sizes.append(parquet_file.stat().st_size)\n", "\n", "print(\"Done\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "import pyarrow.lib\n", "\n", "root = Path(\"/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location-scratch/next-edit-location-new/\")\n", "\n", "total_files = 0\n", "total_bad = 0\n", "\n", "for parquet_file in root.glob(\"*.parquet\"):\n", "    total_files += 1\n", "    try:\n", "        df = pandas.read_parquet(parquet_file)\n", "        print(f\"Good: ({len(df)} rows) {parquet_file}\")\n", "    except pyarrow.lib.ArrowInvalid:\n", "        print(f\"Bad: {parquet_file}\")\n", "        parquet_file.rename(parquet_file.with_suffix(\".bad\"))\n", "        total_bad += 1\n", "\n", "print(f\"Total files: {total_files}\")\n", "print(f\"Total bad: {total_bad} ({total_bad / total_files * 100}%)\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import asdict\n", "import pandas\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "from base.tokenizers import Tokenizer, create_tokenizer_by_name\n", "from research.data.spark.pipelines.stages.prs import (\n", "    Repository<PERSON><PERSON><PERSON>,\n", "    CreatePrDatasetForNextEditLocationConfig,\n", "    _process_pr_into_next_edit_location_samples,\n", ")\n", "\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "\n", "repo_file_manager = RepositoryFileManager(\n", "    pr_repo_reference_parquet_file=Path(\n", "        \"/mnt/efs/spark-data/shared/gh_pr_repo_ref.parquet\"\n", "    )\n", ")\n", "\n", "prs_dataset = Path(\"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha/\")\n", "\n", "config = CreatePrDatasetForNextEditLocationConfig(\n", "    name=\"create_next_edit_location_dataset\",\n", "    input=\"joined-prs-and-repos\",\n", "    output=\"next-edit-location\",\n", "\n", "    num_context_lines_in_past_to_wip_diff=5,\n", "    max_lines_per_chunk=30,\n", "\n", "    max_negative_samples_per_batch=512,\n", "    max_positive_samples_per_batch=64,\n", "\n", "    add_path_to_prompt=True,\n", "    max_query_token_length=4096,\n", "    max_document_token_length=1000,\n", ")\n", "\n", "for path in prs_dataset.glob(\"*.parquet\"):\n", "    training_samples = []\n", "    print(f\"Processing {path}\")\n", "    prs_df = pandas.read_parquet(path)\n", "    for i, pr in enumerate(prs_df.to_dict(orient=\"records\")):\n", "        if i > 3:\n", "            break\n", "        print(f\"PR title: {pr['title']}\")\n", "        samples = list(_process_pr_into_next_edit_location_samples(tokenizer, repo_file_manager, pr, config))\n", "        print(f\"Generated {len(samples)} samples\")\n", "        training_samples.extend([asdict(sample) for sample in samples])\n", "    training_df = pandas.DataFrame(training_samples)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "from base.tokenizers import Tokenizer, create_tokenizer_by_name\n", "\n", "root = Path(\"/mnt/efs/augment/user/guy/data-pipeline/pr-next-edit-location-scratch\")\n", "stage_dir = \"next-edit-location\"\n", "\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "\n", "for path in (root / stage_dir).rglob(\"*.parquet\"):\n", "\n", "    df = pandas.read_parquet(path)\n", "    num_rows = len(df)\n", "\n", "    if num_rows == 0:\n", "        print(f\"{path}: num_rows={len(df)}\")\n", "    # print(df.columns)\n", "\n", "    # for _, row in df.iterrows():\n", "    #     print(row[\"sample_tokens\"][:10])\n", "    #     print(tokenizer.detokenize(row[\"sample_tokens\"][:10]))\n", "    #     print(tokenizer.detokenize(row[\"sample_tokens\"][-10:]))\n", "    #     break\n", "\n", "        # for tokens in row[\"_document_tokens\"]:\n", "        #     token_lengths.append(len(tokens))\n", "\n", "        # for text_ in row[\"_document_text\"]:\n", "        #     text = str(text_)\n", "        #     char_lengths.append(len(text))\n", "        #     line_lengths.append(len(text.splitlines()))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(token_lengths))\n", "print(len(char_lengths))\n", "print(len(line_lengths))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "print(np.mean(line_lengths))\n", "print(np.max(line_lengths))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots()\n", "\n", "# axes[0].hist(char_lengths, bins=40)\n", "# axes[0].set_title(\"Character length distribution\")\n", "\n", "# axes[1].hist(line_lengths, bins=40)\n", "# axes[1].set_title(\"Line length distribution\")\n", "\n", "ax.hist(token_lengths, bins=40)\n", "ax.set_title(\"Document token length distribution\")\n", "ax.axvline(512, color=\"red\")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prob_below_512 = sum(1 for l in token_lengths if l < 512) / len(token_lengths)\n", "print(prob_below_512)\n", "\n", "num_draws = 512 + 64\n", "\n", "prob_of_all_docs_below_512 = prob_below_512 ** num_draws\n", "print(prob_of_all_docs_below_512)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}