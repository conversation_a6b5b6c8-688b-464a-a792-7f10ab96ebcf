from collections import defaultdict
from typing import Iterable
import os
import json
import re
import traceback

from slack_bolt import App
from slack_bolt.adapter.socket_mode import <PERSON><PERSON><PERSON>odeHand<PERSON>

from research.llm_apis.chat_utils import Llama3ChatClient, LLaMA31VertexAIChatClient

# address = "**************:8000"
# llm_client = Llama3ChatClient(server_type="triton", address=address, timeout=60)

llm_client = LLaMA31VertexAIChatClient()

# Install the Slack app and get xoxb- token in advance
app = App(token=os.environ["SLACK_BOT_TOKEN"])


def iter_lines(stream: Iterable[str]) -> Iterable[str]:
    """Take an iterator over arbitrary strings and return an iterator over lines."""
    partial_line = ""
    split_text = "\n"
    for text in stream:
        while split_text in text:
            elems = text.split(split_text, maxsplit=1)
            assert len(elems) == 2, len(elems)
            line = partial_line + elems[0] + "\n"
            yield line
            text = elems[1]
            partial_line = ""
        partial_line += text
    yield partial_line


# TODO(guy) mention can actually appear anywhere in the message (and actually doesn't
# seem to matter to the model), so maybe we don't need this. Or maybe we should
# translate these to the actual user names.
def clean_message(message: str) -> str:
    """Remove Slack user tags from a message."""
    return re.sub(r"^<@[\S]*> ", "", message)


def get_thread_dialog(thread_replies) -> list[str]:
    for i, reply in enumerate(thread_replies):
        print(f"    reply {i}: {reply}")
    return [clean_message(message["text"]) for message in thread_replies["messages"]]


def num_markdown_code_header_lines(text: str) -> int:
    """Count the number of lines that start with ```."""
    return len([line for line in text.splitlines() if line.startswith("```")])


def fix_answer_formatting(partial_answer: str) -> str:
    """Fix formatting generated answers, including partially generated ones."""
    # Remove markdown language markers because slack doesn't render them properly
    partial_answer = re.sub(r"```\w+", r"```", partial_answer, re.MULTILINE | re.DOTALL)

    # Get partial code blocks to render nicely
    if num_markdown_code_header_lines(partial_answer) % 2 == 1:
        if partial_answer.endswith("```\n"):
            # Don't show a code block that was just opened, but has no
            # content.
            partial_answer = partial_answer[:-4]
        else:
            # We're in the middle of a code block. Close it so the
            # partial result gets rendered nicely.
            partial_answer += "```\n"

    return partial_answer


def markdown_to_slack_mrkdwn(text: str) -> str:
    """Convert Markdown generated by the model to the Slack dialect mrkdwn.

    Also fix rendering issues with Slack.
    """
    # Render bullet points because Slack won't do it
    text = text.replace("\n* ", "\n• ")
    text = re.sub(r"\*\*([^\*]*)\*\*", r"*\1*", text)
    return text


CLOCK_EMOJIS = [
    "clock1",
    # "clock130",
    "clock2",
    # "clock230",
    "clock3",
    # "clock330",
    "clock4",
    # "clock430",
    "clock5",
    # "clock530",
    "clock6",
    # "clock630",
    "clock7",
    # "clock730",
    "clock8",
    # "clock830",
    "clock9",
    # "clock930",
    "clock10",
    # "clock1030",
    "clock11",
    # "clock1130",
    "clock12",
    # "clock1230",
]


def reply_to_message(client, body, say):
    # user = body["event"]["user"]
    channel = body["event"]["channel"]
    question_ts = body["event"]["ts"]

    if "thread_ts" in body["event"]:
        thread_ts = body["event"]["thread_ts"]
    else:
        thread_ts = body["event"]["ts"]

    question = clean_message(body["event"]["text"])

    # in_progress_emoji = "eyes"
    in_progress_emoji = "eyeslooking"

    system_prompt = """\
You are a helpful chat bot built by Augment, designed to answer questions about software engineering.
If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.
If you don't know the answer to a question, please don't share false information.

You can see the questions and answers asked in the current thread. You do not have access
to other chats beyond the current one. When in a channel, you can only answer questions that you were
explicitly tagged in. In a DM you will answer questions without tagging.

You are using LLaMA 3.1 405B as the model, trained by Meta.
"""

    client.reactions_add(
        channel=channel,
        timestamp=body["event"]["ts"],
        name=in_progress_emoji,
    )

    replies = client.conversations_replies(
        channel=channel,
        ts=thread_ts,
    )
    dialog = get_thread_dialog(replies)

    try:
        answer = ""
        post_result = None

        # The dialog already contains the answer
        assert (
            dialog[-1] == question
        ), f"Dialog does not contain the question. Dialog: {dialog} Answer: {question}"
        response_was_updated = False

        for line_idx, line in enumerate(
            iter_lines(
                llm_client.generate_stream(
                    messages=dialog, system_prompt=system_prompt, max_tokens=2048
                )
            )
        ):
            # TODO(guy) this is a workaround to a problem when calling the Vertex AI
            # API for LLaMA 3.1 405B, which returns "assistant" as the beginning
            # of multi-turn responses
            if line_idx == 0 and line.startswith("assistant"):
                line = line[len("assistant") :]

            answer += line
            display_answer = fix_answer_formatting(answer)
            display_answer = markdown_to_slack_mrkdwn(display_answer)

            # Processing may have resulted in an empty answer, which Slack doesn't like
            # so skip it
            if not display_answer:
                continue

            if post_result:
                # Add content to the previous message
                in_progress_indicator = (
                    ":" + CLOCK_EMOJIS[line_idx % len(CLOCK_EMOJIS)] + ":\n"
                )
                display_answer += in_progress_indicator
                client.chat_update(
                    channel=channel,
                    ts=post_result["ts"],  # pylint: disable=unsubscriptable-object
                    text=display_answer,
                    # blocks=blocks,
                )
                response_was_updated = True
            else:
                # This is the first line
                print("response:")
                print(answer)

                # Specifying thread_ts will cause this to be a reply in thread
                post_result = client.chat_postMessage(
                    channel=channel,
                    thread_ts=question_ts,
                    text=display_answer,
                    # blocks=blocks,
                )

                print(f"post result: {post_result}")

        # Final update without the ellipsis
        if post_result and response_was_updated:
            client.chat_update(
                channel=channel,
                ts=post_result["ts"],  # pylint: disable=unsubscriptable-object
                text=fix_answer_formatting(markdown_to_slack_mrkdwn(answer)),
            )

    except Exception as exc:
        traceback.print_exc()
        print(f"Error: {exc}")
        say(f"Error: {exc}")

    client.reactions_remove(
        channel=channel,
        timestamp=body["event"]["ts"],
        name=in_progress_emoji,
    )


@app.event("message")
def handle_message_replied(client, body, say, ack):
    ack()

    print("message body:")
    print(json.dumps(body, indent=2))

    # Only reply to direct messages, not channel messages
    if not body["event"]["channel_type"] == "im":
        return

    reply_to_message(client, body, say)


@app.event("app_mention")
def handle_app_mention(client, body, say, ack):
    ack()
    print("app_mention body:")
    print(json.dumps(body, indent=2))
    reply_to_message(client, body, say)


if __name__ == "__main__":
    SocketModeHandler(app, os.environ["SLACK_APP_TOKEN"]).start()
