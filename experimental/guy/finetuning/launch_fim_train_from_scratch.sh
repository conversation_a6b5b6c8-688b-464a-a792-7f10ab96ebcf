#!/bin/bash

NAME=test5_scratch_cosine

cd ~/augment/research/gpt-neox/

python3 ../../experimental/guy/finetuning/finetune.py \
    --dataset_path /mnt/efs/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document \
    --save_dir /mnt/efs/augment/checkpoints/fim/$NAME \
    --wandb_name fim_${NAME} \
    --fim_probability 0.5 \
    --steps 50000 \
    --eval_interval 1000 \
    --num_gpus 8 \
    --lr_decay_style cosine \
    --warmup 0.05 \
    --lr 0.00016 \
    --from_scratch \
    |& tee log_fim_${NAME}.txt
