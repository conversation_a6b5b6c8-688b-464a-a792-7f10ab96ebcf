"""Finetune code models on multiple repositories.

Sequentially fine-tune code models over a list of repositories.
"""

import argparse
import os
import time
from pathlib import Path

from defs import DATASET_ROOT, EFS_PATH, REPO_PATHS

CONFIGS_DIR = Path(EFS_PATH, "configs/codegen-H")
CONFIG_FILENAME = "/tmp/train_config.yml"
TRAIN_SCRIPT_FILENAME = "/tmp/train.sh"


def train_on_repo(args, repo_path):
    """Train a model on the given repo.

    Args:
        repo_path: relative repository path
        wandb_group: experiment group in wandb

    Returns:
        The directory where the checkpoint was saved.
    """
    repo_save_dir = Path(args.save_dir, repo_path)

    if Path.exists(repo_save_dir):
        print(
            (
                f"Save directory {repo_save_dir} already exists, "
                "skipping repository {repo_path}"
            )
        )
        return repo_save_dir

    wandb_name = f"{args.wandb_name}_{repo_path}"
    eval_interval = 5

    config = f"""
data_path: null
train_data_paths: [{DATASET_ROOT}/{repo_path}.mem_text_document]
valid_data_paths: [{DATASET_ROOT}/{repo_path}.test_text_document]
test_data_paths: [{DATASET_ROOT}/{repo_path}.test_text_document]

load: /mnt/efs/augment/checkpoints/codegen-2B-multi

train_batch_size: 48
train_micro_batch_size_per_gpu: 48
gradient_accumulation_steps: 1

eval_interval: {eval_interval}
early_stopping: True
early_stopping_threshold: 0.005

train_iters: 200
lr_decay_iters: 200
warmup: 0
lr_decay_style: constant

fim_probability: {args.fim_probability}

optimizer:
    params:
        betas:
        - 0.9
        - 0.999
        eps: 1.0e-08
        # Equal to 0.1 the pre-training lr
        lr: 0.000008
    type: Adam

save: {repo_save_dir}
save_interval: {eval_interval}
#keep_last_n_checkpoints: 3
# Keep all checkpoints
keep_last_n_checkpoints: null
no_save_optim: True

wandb_name: {wandb_name}
wandb_group: {args.wandb_name}
"""

    train_script = f"""
mkdir -p {repo_save_dir}
python ./deepy.py train.py \\
        {CONFIGS_DIR}/lr/3e-4.yml \\
        {CONFIGS_DIR}/train/big_batch.f.p4.yml \\
        {CONFIGS_DIR}/model/codegen-2B.ft.yml \\
        {CONFIG_FILENAME}
"""

    with Path(CONFIG_FILENAME).open("w", encoding="utf-8") as config_file:
        config_file.write(config)

    with Path(TRAIN_SCRIPT_FILENAME).open("w", encoding="utf-8") as script_file:
        script_file.write(train_script)

    print(f"Launching finetuning on {repo_path}...")
    os.system(f"chmod +x {TRAIN_SCRIPT_FILENAME}")
    result = os.system(f"/bin/bash -c {TRAIN_SCRIPT_FILENAME}")
    if result:
        raise ValueError("Finetuning run failed, will retry")
    return repo_save_dir


def erase_optimizer_state_files(save_dir):
    """Erase the optimizer files stored as part of the checkpoint.

    These files take up a lot of space and are only needed for resuming
    training, not for inference.

    Args:
        save_dir: The root directory where checkpoints are stored.
    """
    for path in Path(save_dir).rglob("zero_pp_*"):
        path.unlink()


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    # parser.add_argument(
    #     "--baseline",
    #     action="store_true",
    #     help="evaluate a baseline model (no finetuning or memory)",
    # )
    parser.add_argument(
        "--repo_paths",
        type=str,
        default=",".join(REPO_PATHS),
        help="comma-separated repository paths to finetune on",
    )
    parser.add_argument(
        "--fim_probability",
        type=float,
        default=0.0,
        help="fill-in-the-middle transformation probability",
    )
    parser.add_argument(
        "--save_dir",
        type=str,
        default=(EFS_PATH + "/checkpoints/polycoder_finetuning/codegen-2B-multi-ft"),
        help="where to save the checkpoints",
    )
    parser.add_argument(
        "--wandb_name",
        type=str,
        default="test",
        help="wandb experiments and group name",
    )
    args = parser.parse_args()

    repo_paths = args.repo_paths.split(",")
    for repo in repo_paths:
        try:
            repo_save_dir = train_on_repo(args, repo)
            erase_optimizer_state_files(repo_save_dir)
        except ValueError:
            print("Training failed, will retry after sleeping")
            time.sleep(60)


if __name__ == "__main__":
    main()
