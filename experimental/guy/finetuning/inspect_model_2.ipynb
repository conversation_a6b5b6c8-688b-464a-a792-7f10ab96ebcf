{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml')]\n"]}, {"ename": "AssertionError", "evalue": "Check batch related parameters. train_batch_size is not equal to micro_batch_per_gpu * gradient_acc_step * world_size \n1 != 1 * 1 * 0.0", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAssertionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 116\u001b[0m\n\u001b[1;32m    113\u001b[0m model \u001b[39m=\u001b[39m GPTNeoXModel()\n\u001b[1;32m    114\u001b[0m \u001b[39m# model.load(\"/mnt/efs/augment/checkpoints/fim/test3\")\u001b[39;00m\n\u001b[1;32m    115\u001b[0m \u001b[39m# model.load(\"/mnt/efs/augment/checkpoints/fim/test4\")\u001b[39;00m\n\u001b[0;32m--> 116\u001b[0m model\u001b[39m.\u001b[39;49mload(\u001b[39m\"\u001b[39;49m\u001b[39m/mnt/efs/augment/checkpoints/fim/test5\u001b[39;49m\u001b[39m\"\u001b[39;49m)\n", "Cell \u001b[0;32mIn[6], line 33\u001b[0m, in \u001b[0;36mGPTNeoXModel.load\u001b[0;34m(self, checkpoint_path)\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mload\u001b[39m(\u001b[39mself\u001b[39m, checkpoint_path):\n\u001b[1;32m     20\u001b[0m     _overwrite_values \u001b[39m=\u001b[39m {\n\u001b[1;32m     21\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mload\u001b[39m\u001b[39m\"\u001b[39m: checkpoint_path,\n\u001b[1;32m     22\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mcheckpoint_activations\u001b[39m\u001b[39m\"\u001b[39m: \u001b[39mFalse\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     30\u001b[0m         \u001b[39m# \"memorize_mode\": \"host\",\u001b[39;00m\n\u001b[1;32m     31\u001b[0m     }\n\u001b[0;32m---> 33\u001b[0m     neox_args \u001b[39m=\u001b[39m megatron\u001b[39m.\u001b[39;49mneox_arguments\u001b[39m.\u001b[39;49mNeoXArgs\u001b[39m.\u001b[39;49mfrom_ymls(\n\u001b[1;32m     34\u001b[0m         paths_to_yml_files\u001b[39m=\u001b[39;49m[\n\u001b[1;32m     35\u001b[0m             Path(\u001b[39m\"\u001b[39;49m\u001b[39m/mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml\u001b[39;49m\u001b[39m\"\u001b[39;49m),\n\u001b[1;32m     36\u001b[0m             \u001b[39m# Path(\"/mnt/efs/augment/configs/codegen-H/train/big_batch.f.yml\"),\u001b[39;49;00m\n\u001b[1;32m     37\u001b[0m             ],\n\u001b[1;32m     38\u001b[0m         overwrite_values\u001b[39m=\u001b[39;49m_overwrite_values,\n\u001b[1;32m     39\u001b[0m     )\n\u001b[1;32m     40\u001b[0m     neox_args\u001b[39m.\u001b[39mconfigure_distributed_args()\n\u001b[1;32m     42\u001b[0m     neox_args\u001b[39m.\u001b[39mbuild_tokenizer()\n", "File \u001b[0;32m~/augment/models/gpt-neox/megatron/neox_arguments/arguments.py:219\u001b[0m, in \u001b[0;36mNeoXArgs.from_ymls\u001b[0;34m(cls, paths_to_yml_files, overwrite_values)\u001b[0m\n\u001b[1;32m    215\u001b[0m         config[k] \u001b[39m=\u001b[39m v\n\u001b[1;32m    217\u001b[0m \u001b[39m# instantiate class and return\u001b[39;00m\n\u001b[1;32m    218\u001b[0m \u001b[39m# duplicate values and unrecognized keys are again checked upon instantiation\u001b[39;00m\n\u001b[0;32m--> 219\u001b[0m \u001b[39mreturn\u001b[39;00m \u001b[39mcls\u001b[39;49m(\u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mconfig)\n", "File \u001b[0;32m<string>:215\u001b[0m, in \u001b[0;36m__init__\u001b[0;34m(self, total_model_params, embedding_model_params, memory_config, memory_size, memorize_mode, memory_invalid_query_mode, num_memory_write_heads, memory_save, memory_load, memory_train_on_gpu, memory_partition_count, memorize_files, memorize_filelist, memorize_report, distributed_backend, local_rank, rank, lazy_mpu_init, short_seq_prob, eod_mask_loss, adlr_autoresume, adlr_autoresume_interval, seed, onnx_safe, deepscale, deepscale_config, deepspeed_mpi, user_script, iteration, do_train, do_valid, do_test, global_num_gpus, text_gen_type, temperature, top_p, top_k, maximum_tokens, sample_input_file, sample_output_file, num_samples, recompute, eval_results_prefix, eval_tasks, memory_object_names, memory_object_names_file, eval_batch_size, eval_with_memories, eval_tags, use_wandb, wandb_group, wandb_team, wandb_project, wandb_host, wandb_name, wandb_init_all_ranks, git_hash, log_dir, tensorboard_dir, log_interval, log_grad_pct_zeros, log_param_norm, log_grad_norm, log_optimizer_states, log_gradient_noise_scale, gradient_noise_scale_n_batches, gradient_noise_scale_cpu_offload, pipe_parallel_size, model_parallel_size, pipe_partition_method, world_size, is_pipe_parallel, data_path, train_data_paths, test_data_paths, valid_data_paths, train_data_weights, valid_data_weights, test_data_weights, weight_by_num_documents, weighted_sampler_alpha, fim_probability, data_impl, mmap_warmup, save, config_files, load, checkpoint_validation_with_forward_pass, save_interval, no_save_optim, no_save_rng, no_load_optim, no_load_rng, finetune, batch_size, train_iters, eval_iters, keep_last_n_checkpoints, eval_interval, early_stopping, early_stopping_metric, early_stopping_threshold, split, vocab_file, merge_file, num_workers, exit_interval, attention_dropout, hidden_dropout, weight_decay, checkpoint_activations, checkpoint_num_layers, deepspeed_activation_checkpointing, contiguous_checkpointing, checkpoint_in_cpu, synchronize_each_layer, profile_backward, partition_activations, gas, clip_grad, hysteresis, dynamic_loss_scale, loss_scale, loss_scale_window, min_scale, char_level_ppl, mem_friendly_batch, train_only, tokenizer_type, padded_vocab_size, optimizer_type, use_bnb_optimizer, zero_stage, zero_reduce_scatter, zero_contiguous_gradients, zero_reduce_bucket_size, zero_allgather_bucket_size, lr, lr_decay_style, lr_decay_iters, min_lr, warmup, override_lr_scheduler, use_checkpoint_lr_scheduler, precision, num_layers, hidden_size, num_attention_heads, seq_length, max_position_embeddings, norm, layernorm_epsilon, rms_norm_epsilon, scalenorm_epsilon, pos_emb, rotary_interleave, rpe_num_buckets, rpe_max_distance, no_weight_tying, attention_config, sparsity_config, num_unique_layers, param_sharing_style, make_vocab_size_divisible_by, activation, scaled_upper_triang_masked_softmax_fusion, scaled_masked_softmax_fusion, bias_gelu_fusion, bias_dropout_fusion, fp16_lm_cross_entropy, init_method_std, apply_query_key_layer_scaling, use_cpu_initialization, attention_softmax_in_fp32, rotary_pct, rotary_emb_base, init_method, output_layer_init_method, gmlp_attn_dim, gpt_j_residual, soft_prompt_tuning, output_layer_parallelism, use_post_attn_norm, final_linear_bias, deepspeed, train_batch_size, train_micro_batch_size_per_gpu, gradient_accumulation_steps, optimizer, scheduler, fp32_allreduce, prescale_gradients, gradient_predivide_factor, sparse_gradients, fp16, amp, gradient_clipping, zero_optimization, steps_per_print, wall_clock_breakdown, dump_state, flops_profiler, zero_allow_untested_optimizer, hostfile, include, exclude, num_nodes, num_gpus, master_port, master_addr, launcher, detect_nvlink_pairs)\u001b[0m\n", "File \u001b[0;32m~/augment/models/gpt-neox/megatron/neox_arguments/arguments.py:121\u001b[0m, in \u001b[0;36mNeoXArgs.__post_init__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    114\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m    115\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m\u001b[39m__class__\u001b[39m\u001b[39m.\u001b[39m\u001b[39m__name__\u001b[39m\n\u001b[1;32m    116\u001b[0m         \u001b[39m+\u001b[39m \u001b[39m\"\u001b[39m\u001b[39m.__post_init__() NeoXArgs keys cannot be validated\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    117\u001b[0m     )\n\u001b[1;32m    119\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39menable_logging()\n\u001b[0;32m--> 121\u001b[0m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mcalculate_derived()\n\u001b[1;32m    123\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mvalidate_types():\n\u001b[1;32m    124\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m    125\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m\u001b[39m__class__\u001b[39m\u001b[39m.\u001b[39m\u001b[39m__name__\u001b[39m\n\u001b[1;32m    126\u001b[0m         \u001b[39m+\u001b[39m \u001b[39m\"\u001b[39m\u001b[39m.__post_init__() NeoXArgs types cannot be validated\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    127\u001b[0m     )\n", "File \u001b[0;32m~/augment/models/gpt-neox/megatron/neox_arguments/arguments.py:705\u001b[0m, in \u001b[0;36mNeoXArgs.calculate_derived\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    694\u001b[0m \u001b[39m# Automatically derive train_batch_size = train_micro_batch_size_per_gpu*global_num_gpus*gradient_accumulation_steps\u001b[39;00m\n\u001b[1;32m    695\u001b[0m (\n\u001b[1;32m    696\u001b[0m     train_batch_size,\n\u001b[1;32m    697\u001b[0m     train_micro_batch_size_per_gpu,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    703\u001b[0m     grad_acc\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mgradient_accumulation_steps,\n\u001b[1;32m    704\u001b[0m )\n\u001b[0;32m--> 705\u001b[0m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mcheck_batch_parameters(\n\u001b[1;32m    706\u001b[0m     dp_world_size\u001b[39m=\u001b[39;49mdp_world_size,\n\u001b[1;32m    707\u001b[0m     train_batch\u001b[39m=\u001b[39;49mtrain_batch_size,\n\u001b[1;32m    708\u001b[0m     micro_batch\u001b[39m=\u001b[39;49mtrain_micro_batch_size_per_gpu,\n\u001b[1;32m    709\u001b[0m     grad_acc\u001b[39m=\u001b[39;49mgradient_accumulation_steps,\n\u001b[1;32m    710\u001b[0m )\n\u001b[1;32m    711\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mupdate_values(\n\u001b[1;32m    712\u001b[0m     {\n\u001b[1;32m    713\u001b[0m         \u001b[39m# batch size params\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    721\u001b[0m     }\n\u001b[1;32m    722\u001b[0m )\n\u001b[1;32m    724\u001b[0m \u001b[39m# derive precision\u001b[39;00m\n", "File \u001b[0;32m~/augment/models/gpt-neox/megatron/neox_arguments/arguments.py:634\u001b[0m, in \u001b[0;36mNeoXArgs.check_batch_parameters\u001b[0;34m(dp_world_size, train_batch, micro_batch, grad_acc)\u001b[0m\n\u001b[1;32m    626\u001b[0m \u001b[39massert\u001b[39;00m (\n\u001b[1;32m    627\u001b[0m     micro_batch \u001b[39m>\u001b[39m \u001b[39m0\u001b[39m\n\u001b[1;32m    628\u001b[0m ), \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mMicro batch size per gpu: \u001b[39m\u001b[39m{\u001b[39;00mmicro_batch\u001b[39m}\u001b[39;00m\u001b[39m has to be greater than 0\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    630\u001b[0m \u001b[39massert\u001b[39;00m (\n\u001b[1;32m    631\u001b[0m     grad_acc \u001b[39m>\u001b[39m \u001b[39m0\u001b[39m\n\u001b[1;32m    632\u001b[0m ), \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mGradient accumulation steps: \u001b[39m\u001b[39m{\u001b[39;00mgrad_acc\u001b[39m}\u001b[39;00m\u001b[39m has to be greater than 0\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m--> 634\u001b[0m \u001b[39massert\u001b[39;00m train_batch \u001b[39m==\u001b[39m micro_batch \u001b[39m*\u001b[39m grad_acc \u001b[39m*\u001b[39m dp_world_size, (\n\u001b[1;32m    635\u001b[0m     \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mCheck batch related parameters. train_batch_size is not equal\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    636\u001b[0m     \u001b[39m\"\u001b[39m\u001b[39m to micro_batch_per_gpu * gradient_acc_step * world_size \u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    637\u001b[0m     \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m{\u001b[39;00mtrain_batch\u001b[39m}\u001b[39;00m\u001b[39m != \u001b[39m\u001b[39m{\u001b[39;00mmicro_batch\u001b[39m}\u001b[39;00m\u001b[39m * \u001b[39m\u001b[39m{\u001b[39;00mgrad_acc\u001b[39m}\u001b[39;00m\u001b[39m * \u001b[39m\u001b[39m{\u001b[39;00mdp_world_size\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    638\u001b[0m )\n", "\u001b[0;31mAssertionError\u001b[0m: Check batch related parameters. train_batch_size is not equal to micro_batch_per_gpu * gradient_acc_step * world_size \n1 != 1 * 1 * 0.0"]}], "source": ["from pathlib import Path\n", "\n", "import megatron\n", "import megatron.memorize\n", "import megatron.memorize.memorize_utils\n", "import megatron.text_generation_utils\n", "import megatron.training\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "\n", "\n", "# Adapted from gpt_neox.py\n", "class GPTNeoXModel:\n", "    \"\"\"Wrapper for the GPT-NeoX model.\"\"\"\n", "\n", "    def __init__(self):\n", "        self.model = None\n", "        self.neox_args = None\n", "\n", "    def load(self, checkpoint_path):\n", "        _overwrite_values = {\n", "            \"load\": checkpoint_path,\n", "            \"checkpoint_activations\": <PERSON><PERSON><PERSON>,\n", "            \"partition_activations\": <PERSON><PERSON><PERSON>,\n", "            \"no_load_optim\": True,\n", "            \"zero_optimization\": None,  # disable zero optimization (won't be used in inference, and loading zero optimizer can cause errors)\n", "            # \"memorize_mode\": \"host\",\n", "        }\n", "\n", "        neox_args = megatron.neox_arguments.NeoXArgs.from_ymls(\n", "            paths_to_yml_files=[\n", "                Path(\"/mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml\"),\n", "                # Path(\"/mnt/efs/augment/configs/codegen-H/train/big_batch.f.yml\"),\n", "                ],\n", "            overwrite_values=_overwrite_values,\n", "        )\n", "        neox_args.configure_distributed_args()\n", "\n", "        neox_args.build_tokenizer()\n", "\n", "        if neox_args.load is None:\n", "            raise ValueError(\"`load` parameter must be supplied to load a model`\")\n", "\n", "        # initialize megatron\n", "        megatron.initialize.initialize_megatron(neox_args)\n", "\n", "        # set up model and load checkpoint.\n", "        model, _, _ = megatron.training.setup_model_and_optimizer(\n", "            neox_args=neox_args,\n", "            use_cache=True,\n", "            iteration=neox_args.iteration,\n", "        )  # we use setup_model_and_optimizer instead of get_model in order to initialize deepspeed\n", "\n", "        # put the model into evaluation model - i.e., model.training will be False\n", "        model.eval()\n", "\n", "        print(\"Finished loading model\")\n", "\n", "        self.model = model\n", "        self.neox_args = neox_args\n", "\n", "        print(\"Got model:\", model)\n", "\n", "    def unload(self):\n", "        self.model = None\n", "        self.neox_args = None\n", "\n", "    def is_loaded(self):\n", "        return self.model is not None\n", "\n", "    def generate(\n", "        self,\n", "        text: str = \"\",\n", "        eos_token_id: int = None,\n", "        maximum_tokens: int = 64,\n", "        recompute: bool = False,\n", "        temperature: float = None,\n", "        top_k: int = None,\n", "        top_p: float = None,\n", "        mem_object_names: list = None,\n", "    ):\n", "        generated_texts = megatron.text_generation_utils.generate_samples_from_prompt(\n", "            neox_args=self.neox_args,\n", "            model=self.model,\n", "            text_tokens=[self.model.tokenizer.tokenize(text)],\n", "            eos_token_id=eos_token_id,\n", "            maximum_tokens=maximum_tokens,\n", "            recompute=recompute,\n", "            temperature=temperature if temperature is not None else 0.0,\n", "            top_k=top_k if top_k is not None else 0,\n", "            top_p=top_p if top_p is not None else 0,\n", "            mem_object_names=mem_object_names,\n", "        )\n", "\n", "        return generated_texts[0][\"text\"]\n", "\n", "    def memorize(\n", "        self,\n", "        text: str,\n", "    ):\n", "        mem_object_name = megatron.memorize.memorize_utils.memorize_text(\n", "            neox_args=self.neox_args,\n", "            model=self.model,\n", "            text=text,\n", "        )\n", "\n", "        return mem_object_name\n", "\n", "\n", "model = GPTNeoXModel()\n", "# model.load(\"/mnt/efs/augment/checkpoints/fim/test3\")\n", "# model.load(\"/mnt/efs/augment/checkpoints/fim/test4\")\n", "model.load(\"/mnt/efs/augment/checkpoints/fim/test5\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>> prompt:\n", "def draw_square():\n", "    \"\"\"Draw a square using the helper functions below.\"\"\"\n", "\u001b[34m|\u001b[0m\n", "    print(\"All done!\")\n", "\n", "# Helper functions\n", "\n", "def move_turtle_forward():\n", "    \"\"\"Move the turtle in the direction it is pointing.\"\"\"\n", "    x += cos(angle)\n", "    y += sin(angle)\n", "\n", "def turn_turtle_90_degress():\n", "    \"\"\"Turn 90 degrees left.\"\"\"\n", "    angle += 90\n", "\n"]}, {"ename": "NameError", "evalue": "name 'model' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 102\u001b[0m\n\u001b[1;32m     99\u001b[0m \u001b[39m# print(prompt)\u001b[39;00m\n\u001b[1;32m    100\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m{\u001b[39;00mprefix\u001b[39m}\u001b[39;00m\u001b[39m{\u001b[39;00mFore\u001b[39m.\u001b[39mBLUE\u001b[39m}\u001b[39;00m\u001b[39m|\u001b[39m\u001b[39m{\u001b[39;00mStyle\u001b[39m.\u001b[39mRESET_ALL\u001b[39m}\u001b[39;00m\u001b[39m{\u001b[39;00msuffix\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m)\n\u001b[0;32m--> 102\u001b[0m raw_result \u001b[39m=\u001b[39m model\u001b[39m.\u001b[39mgenerate(\n\u001b[1;32m    103\u001b[0m     text\u001b[39m=\u001b[39mprompt,\n\u001b[1;32m    104\u001b[0m     maximum_tokens\u001b[39m=\u001b[39m\u001b[39m128\u001b[39m,\n\u001b[1;32m    105\u001b[0m     temperature\u001b[39m=\u001b[39m\u001b[39m0.\u001b[39m,\n\u001b[1;32m    106\u001b[0m     )\n\u001b[1;32m    108\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39m\"\u001b[39m\u001b[39m>>> result:\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[1;32m    109\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39m\"\u001b[39m\u001b[39m[EOS]\u001b[39m\u001b[39m\"\u001b[39m \u001b[39min\u001b[39;00m raw_result:\n", "\u001b[0;31mNameError\u001b[0m: name 'model' is not defined"]}], "source": ["import colorama\n", "from colorama import Fore, Style\n", "\n", "def make_fim_prompt(prefix, suffix):\n", "    return f\"{suffix}[SEP]{prefix}\"\n", "\n", "samples = {\n", "    \"factorial\": \"\"\"\n", "def factorial(n):\n", "    \\\"\\\"\\\"Compute the factorial of n\\\"\\\"\\\"\n", "|    return n * factorial(n-1)\n", "<|endoftext|>\n", "\"\"\",\n", "\n", "    \"hello_world\": \"def foo:|, World!\\\")\",\n", "\n", "    \"adapter\": \"\"\"\n", "class Adapter:\n", "    def __init__(self, a, b, c, d):\n", "        self.a = a\n", "|    def get_d(self):\n", "        return self.d\n", "\"\"\",\n", "\n", "    \"adapter_before_newline\": \"\"\"\n", "class Adapter:\n", "    def __init__(self, a, b, c, d):\n", "        self.a = a|\n", "    def get_d(self):\n", "        return self.d\n", "\"\"\",\n", "\n", "    \"adapter_debug1\": \"\"\"\n", "class Adapter:\n", "    def __init__(self, a, b, c, d):\n", "        self.a = a\n", "    |def get_d(self):\n", "        return self.d\n", "\"\"\",\n", "\n", "    \"adapter_no_whitespace\": \"\"\"\n", "class Adapter:\n", "    def __init__(self, a, b, c, d):\n", "        self.a = a|def get_d(self):\n", "        return self.d\n", "\"\"\",\n", "\n", "    \"sum_to_n\": \"\"\"\n", "def sum_to_n(n):\n", "    result = 0\n", "|    return result\n", "\"\"\",\n", "\n", "    \"turtle1\": \"\"\"def draw_square():\n", "    \\\"\\\"\\\"Draw a square using the helper functions below.\\\"\\\"\\\"\n", "|\n", "    print(\"All done!\")\n", "\n", "# Helper functions\n", "\n", "def move_turtle_forward():\n", "    \\\"\\\"\\\"Move the turtle in the direction it is pointing.\\\"\\\"\\\"\n", "    x += cos(angle)\n", "    y += sin(angle)\n", "\n", "def turn_turtle_90_degress():\n", "    \\\"\\\"\\\"Turn 90 degrees left.\\\"\\\"\\\"\n", "    angle += 90\n", "\"\"\",\n", "\n", "    \"turtle2\": \"\"\"\n", "def move_turtle_forward():\n", "    \\\"\\\"\\\"Move the turtle in the direction it is pointing.\\\"\\\"\\\"\n", "    x += cos(angle)\n", "    y += sin(angle)\n", "\n", "def turn_turtle_90_degress():\n", "    \\\"\\\"\\\"Turn 90 degrees left.\\\"\\\"\\\"\n", "    angle += 90\n", "\n", "def draw_square():\n", "    \\\"\\\"\\\"Draw a square using the turtle functions.\\\"\\\"\\\"\n", "|\n", "\"\"\",\n", "}\n", "\n", "sample_name = \"turtle1\"\n", "sample = samples[sample_name]\n", "\n", "if sample.count(\"|\") != 1:\n", "    raise ValueError(\n", "        f\"Expected 1 occurance of '|' in sample, found {sample.count('|')}\")\n", "\n", "prefix, suffix = sample.split(\"|\")\n", "\n", "prompt = make_fim_prompt(prefix, suffix)\n", "\n", "print(\">>> prompt:\")\n", "# print(prompt)\n", "print(f\"{prefix}{Fore.BLUE}|{Style.RESET_ALL}{suffix}\")\n", "\n", "raw_result = model.generate(\n", "    text=prompt,\n", "    maximum_tokens=128,\n", "    temperature=0.,\n", "    )\n", "\n", "print(\">>> result:\")\n", "if \"[EOS]\" in raw_result:\n", "    result = raw_result.split(\"[EOS]\")[0]\n", "    print(f\"{prefix}{Fore.GREEN}{result}{Fore.BLUE}|{Style.RESET_ALL}{suffix}\")\n", "else:\n", "    print(\"WARNING: [EOS] not found, showing full result\")\n", "    print(raw_result)\n", "\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "class Adapter:\n", "    def __init__(self, a, b, c, d):\n", "        self.a = a\n", "    def get_d(self):\n", "        return self.d\n", "\n", "['Ċ', 'class', 'ĠAdapter', ':', 'Ċ', '    ', 'def', 'Ġ__', 'init', '__', '(', 'self', ',', 'Ġa', ',', 'Ġb', ',', 'Ġc', ',', 'Ġd', '):', 'Ċ', '        ', 'self', '.', 'a', 'Ġ=', 'Ġa', 'Ċ', '    ', 'def', 'Ġget', '_', 'd', '(', 'self', '):', 'Ċ', '        ', 'return', 'Ġself', '.', 'd', 'Ċ']\n"]}], "source": ["from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "codegen_tokenizer = CodeGenTokenizer()\n", "tokenizer = codegen_tokenizer.tokenizer\n", "\n", "sample_without_cursor = sample.replace(\"|\", \"\")\n", "print(sample_without_cursor)\n", "print(tokenizer.tokenize(sample_without_cursor))\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenizations match\n"]}], "source": ["sample_tokens = tokenizer.tokenize(sample_without_cursor)\n", "prefix_tokens = tokenizer.tokenize(prefix)\n", "suffix_tokens = tokenizer.tokenize(suffix)\n", "\n", "if prefix_tokens + suffix_tokens == sample_tokens:\n", "    print(\"Tokenizations match\")\n", "else:\n", "    print(\"Tokenizations DONT MATCH!\")\n", "\n", "# print(\"prefix:\")\n", "# print(tokenizer.tokenize(prefix))\n", "# print(\"suffix:\")\n", "# print(tokenizer.tokenize(suffix))"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["codegen_tokenizer = CodeGenTokenizer()\n", "tokenizer = codegen_tokenizer.tokenizer\n", "\n", "for sample_with_cursor in samples.values():\n", "    if sample_with_cursor.count(\"|\") != 1:\n", "        continue\n", "    sample = sample_with_cursor.replace(\"|\", \"\")\n", "    tokens = codegen_tokenizer.tokenize(sample)\n", "\n", "    detok = []\n", "    for t in tokens:\n", "        det = codegen_tokenizer.detokenize([t])\n", "        detok.append(det)\n", "\n", "    for i, d in enumerate(detok):\n", "        # print(\"i=\", i)\n", "        tokens_before = detok[:i]\n", "        tokens_after = detok[i+1:]\n", "        for j in range(len(d)):\n", "            before = d[:j]\n", "            after = d[j:]\n", "\n", "            all_before = \"\".join(tokens_before + [before])\n", "            all_after = \"\".join([after] + tokens_after)\n", "            # print(all_before)\n", "            # print(all_after)\n", "            # print(\"\")\n", "\n", "            if all_before + all_after != sample:\n", "                raise ValueError(\"Strings are not the same!\")\n", "\n", "            all_before_tokens = codegen_tokenizer.tokenize(all_before)\n", "            all_after_tokens = codegen_tokenizer.tokenize(all_after)\n", "\n", "            all_before_reconstructed = codegen_tokenizer.detokenize(all_before_tokens)\n", "            all_after_reconstructed = codegen_tokenizer.detokenize(all_after_tokens)\n", "\n", "            if all_before_reconstructed != all_before:\n", "                raise ValueError(\"before doesn't match\")\n", "            if all_after_reconstructed != all_after:\n", "                raise ValueError(\"after doesn't match\")\n", "\n", "            # all_reconstructed_tokens = all_before_tokens + all_after_tokens\n", "            # if all_reconstructed_tokens != tokens:\n", "            #     print(\"tokens:\", tokens)\n", "            #     print(\"all_reconstructed_tokens:\", all_reconstructed_tokens)\n", "            #     raise ValueError(\"Reconstructed tokens are not the same!\")\n", "    # print(\"\".join(detok))\n", "    # print(sample)\n", "    # print(tokens)\n", "\n", "    # prefix, suffix = sample.split(\"|\")\n", "    # print(prefix)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on method detokenize in module megatron.tokenizer.tokenizer:\n", "\n", "detokenize(token_ids) method of megatron.tokenizer.tokenizer.CodeGenTokenizer instance\n", "\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "36cf16204b8548560b1c020c4e8fb5b57f0e4c58016f52f2d4be01e192833930"}}}, "nbformat": 4, "nbformat_minor": 2}