"""Common definitions.

TODO this should be a yaml file.
"""

from pathlib import Path

REPO_PATHS = [
    "Java/quincy",
    "Java/spoofax",
    "Java/streamex",
    "Java/vulkanbook",
    "Java/java-tron",
    "Java/jetty.project",
    "Python/scout",
    "Python/freeseer",
    "Python/programmingbitcoin",
    "Python/python-learning",
    "Python/vision",
    "Python/viztracer",
]

AUGMENT_REPOSITORY_ROOT = Path("/home/<USER>/augment")
EFS_PATH = "/mnt/efs/augment"
DATASET_ROOT = Path(EFS_PATH, "data/processed/polycoder.v3/repos_splits_tokenized")
UNTOKENIZED_DATASET_ROOT = Path(EFS_PATH, "data/processed/polycoder.v3/repos_splits")
