#data-path: /mnt/efs/augment/data/processed/polycoder.v3/repos_splits_tokenized/Python/scout.mem_text_document
data_path: null
train_data_paths: [/mnt/efs/augment/data/processed/polycoder.v3/repos_splits_tokenized/Python/scout.mem_text_document]
valid_data_paths: [/mnt/efs/augment/data/processed/polycoder.v3/repos_splits_tokenized/Python/scout.test_text_document]
test_data_paths: [/mnt/efs/augment/data/processed/polycoder.v3/repos_splits_tokenized/Python/scout.test_text_document]

load: /mnt/efs/augment/checkpoints/codegen-2B-multi-ft

train_batch_size: 48
train_micro_batch_size_per_gpu: 48
gradient_accumulation_steps: 1

eval_interval: 5
early_stopping: true

train_iters: 80
lr_decay_iters: 80
warmup: 0
lr_decay_style: constant

optimizer:
  params:
    betas:
    - 0.9
    - 0.999
    eps: 1.0e-08
    lr: 0.000016
  type: Adam

save: /mnt/efs/augment/checkpoints/polycoder_finetuning/codegen-2B-multi-ft/scout
keep_last_n_checkpoints: 3

wandb_name: test_early_stopping
wandb_group: test
