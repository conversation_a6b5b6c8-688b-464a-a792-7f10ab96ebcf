"""Evaluate memory model on some repos.

TOD<PERSON>(guy) move defs to yaml file.
"""

import argparse
import json
import os
import sys
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional

import yaml
from defs import AUGMENT_REPOSITORY_ROOT, REPO_PATHS, UNTOKENIZED_DATASET_ROOT

# TODO(guy) move codegen.yml path to config file
EVAL_CONFIG_OVERRIDES_ROOT = Path("/mnt/efs/augment/user/guy/configs/inference")
EVAL_CONFIG_OVERRIDES_MEMORY = Path(EVAL_CONFIG_OVERRIDES_ROOT, "codegen.yml")
EVAL_CONFIG_OVERRIDES_FINETUNED = Path(
    EVAL_CONFIG_OVERRIDES_ROOT, "codegen-finetuned-no-memory.yml"
)


@dataclass
class Checkpoint:
    """A saved checkpoint."""

    path: Path
    iteration: Optional[int] = None
    eval_results: Optional[Dict] = None  # repo(str) -> results dict

    @property
    def model_path(self):
        """Path to this checkpoint's trained model."""
        return self.path.parent


def is_fully_saved(checkpoint_path):
    """Returns whether the checkpoint at the given path finished saving."""
    return (checkpoint_path / "saved").exists()


@dataclass
class TrainedModel:
    """A trained model with one or more checkpoints."""

    path: Path
    checkpoints: List[Checkpoint]
    latest: Optional[Checkpoint] = None

    def __init__(self, model_path, always_load_latest=False):
        """Populate the checkpoints.

        Args:
            model_path: Where the model lives.
            always_load_latest: Always add the latest checkpoint to the list,
                whether or not we can make sure it is fully saved. This can
                be used when loading pre-existing checkpoints.
        """
        self.path = model_path
        self.checkpoints = []

        for checkpoint_path in model_path.glob("global_step*"):
            # Check if the checkpoint is fully saved, to avoid race conditions
            # where the checkpoint is only partially saved and we try to
            # process it.
            if is_fully_saved(checkpoint_path):
                iteration = int(checkpoint_path.name.split("global_step")[-1])
                self.checkpoints.append(Checkpoint(checkpoint_path, iteration))

        latest_path = model_path / "latest"
        if latest_path.is_file():
            print("looking at latest path:", latest_path)
            with latest_path.open("r", encoding="utf8") as latest_file:
                latest_checkpoint_name = latest_file.readline().strip()
                latest_checkpoint_path = model_path / latest_checkpoint_name

                existing_checkpoints = [
                    cp for cp in self.checkpoints if cp.path == latest_checkpoint_path
                ]

                if existing_checkpoints:
                    self.latest = existing_checkpoints[0]
                elif is_fully_saved(latest_checkpoint_path) or always_load_latest:
                    self.latest = Checkpoint(latest_checkpoint_path)
                    self.checkpoints.append(self.latest)
        else:
            print(f"latest path not found for {model_path}")
        print("latest found to be:", self.latest)


def find_trained_models(args) -> List[TrainedModel]:
    """Generate a list of training runs."""
    models = []
    for latest_path in Path(args.checkpoints_path).rglob("latest"):
        model_path = latest_path.parent
        model = TrainedModel(model_path)
        models.append(model)
    return models


def get_repo_name(repo_path):
    """Return the full repository name given the relative repo path.

    Args:
        repo_path: For example, "Java/quincy"
    """
    absolute_repo_path = Path(UNTOKENIZED_DATASET_ROOT, repo_path + ".mem.jsonl")
    with absolute_repo_path.open("r", encoding="utf8") as file:
        for line in file:
            data = json.loads(line)
            return data["meta"]["repo_name"]


def get_finetuned_repo_path(args, model_path: str) -> str:
    """Returns the repository path that a model was finetuned on."""
    repo = str(model_path.relative_to(args.checkpoints_path))
    if repo not in get_repo_paths(args):
        print(
            f"Matching repository {repo} not found for fined-tuned "
            f"model {model_path}, skipping"
        )
    return repo


def generate_model_config(model_path, overrides):
    """Create a trained model's config.yml file.

    The model config is used when evaluating a model, and needs to be
    generated before we evaluate.

    Args:
        model_path: Absolute path to the model checkpoint.
        overrides: Config file containing overrides.
    """
    print(f"Generating config.yml for {model_path}")
    if not Path(model_path).is_absolute():
        raise ValueError(f"Model path must be an absolute path: {model_path}")
    if Path(model_path, "config.yml").exists():
        print(".. config file already exists, skipping.")
        return
    os.chdir(Path(AUGMENT_REPOSITORY_ROOT, "research/gpt-neox"))
    cmd = f"python jobs/inf_config.py -m {overrides} {model_path}"
    result = os.system(cmd)
    if result:
        raise ValueError("Generating model config failed")


def generate_eval_config(args, checkpoint, repos, eval_with_memories):
    """Generate the config file for evaluating a model on repos.

    Args:
        args: Command line arguments
        checkpoint: Checkpoint to evaluate on
        repos: List of repository names to eval on.
        eval_with_memories: Use memory retrieval
        iteration: Model iteration to evaluate on. If not specified, evaluate
            on the latest checkpoint.
    """
    relative_model_path = checkpoint.model_path.relative_to(args.checkpoints_path)
    config = {
        "model_path": str(args.checkpoints_path),
        "eval_with_memories": eval_with_memories,
        "models": [
            {"name": str(relative_model_path)},
        ],
        "tasks": ["gitrepo-poly-" + get_repo_name(path) for path in repos],
        "eval_tags": {},
    }

    if checkpoint.iteration is None:
        # This makes the eval code not complain when an iteration isn't
        # found (load_checkpoint() in checkpointing.py)
        config["finetune"] = True
    else:
        config["models"][0]["iteration"] = checkpoint.iteration

    config_path = Path(args.eval_config_file)
    with config_path.open("w", encoding="utf-8") as config_file:
        yaml.dump(config, config_file, default_flow_style=False)


def run_eval(args, job_path):
    """Run the evaluation based with the generated config.yml file."""
    print(f"Launching evaluation, generating results in {job_path} ...")
    if args.verbose:
        print("=" * 80)
        config_path = Path(args.eval_config_file)
        with config_path.open("r", encoding="utf8") as config_file:
            data = config_file.readlines()
            print("".join(data))
        print("=" * 80)

    cmd = f"./eval.py --job_root {job_path} " f"{args.eval_config_file}"
    print(f"> {cmd}")
    if args.dry_run:
        print("(dry run -- skipping)")
    else:
        os.chdir(Path(AUGMENT_REPOSITORY_ROOT, "research/eval"))
        result = os.system(cmd)
        if result:
            # Evaluations occassionally fail due to network errors,
            # so keep trying
            print("Evaluation failed, will retry")


def get_repo_paths(args):
    """Return a list of repo paths to evaluate on."""
    if args.repos:
        return args.repos.split(",")
    else:
        return REPO_PATHS


def evaluate_checkpoint(args, checkpoint, repo, job_subdir="finetune"):
    """Evaluate a checkpoint on the given repo.

    Args:
        args: Command line argument
        checkpoint: Checkpoint
        repo: Repository name
        job_subdir: Relative directory in which to save eval results
    """
    generate_model_config(checkpoint.model_path, EVAL_CONFIG_OVERRIDES_FINETUNED)
    generate_eval_config(
        args,
        checkpoint,
        [repo],
        eval_with_memories=False,
    )
    run_eval(args, Path(args.eval_jobs_path) / job_subdir)


def load_eval_results(args, models: List[TrainedModel]):
    """Load the evaluation results for all checkpoints.

    The results are loaded in the appropariate checkpoints
    inside models.
    """
    for path in Path(args.eval_jobs_path).rglob("*eval_results*.json"):
        with path.open("r", encoding="utf8") as file:
            data = json.load(file)
            results = data["results"]
            if len(results) != 1:
                raise ValueError(f"Expected exactly 1 result in {path}")
            iteration = data["config"]["model_args"]["iteration"]
            # try:
            #     iteration = data["config"]["model_args"]["iteration"]
            # except KeyError:
            #     iteration = None

            model_path = Path(data["config"]["model_args"]["save"])
            # found = False

            for model in models:
                if model.path == model_path:
                    for checkpoint in model.checkpoints:
                        if checkpoint.iteration == iteration:
                            if checkpoint.eval_results is not None:
                                raise ValueError(
                                    "Found multiple eval results for "
                                    f"checkpoint {checkpoint.path}"
                                )
                            checkpoint.eval_results = results
                            # found = True
                            break

            # Not an error -- checkpoint may be deleted
            # if not found:
            #     raise ValueError(
            #         f"Did not find matching checkpoint for {model_path} "
            #         f"iteration {iteration}")


def get_unevaluated_checkpoints(models: List[TrainedModel]) -> List[Checkpoint]:
    """Find all checkpoints that have not been evaluated."""
    unevaluated_checkpoints = []
    for model in models:
        for checkpoint in model.checkpoints:
            if checkpoint.eval_results is None:
                unevaluated_checkpoints.append(checkpoint)
    return unevaluated_checkpoints


def find_nonbest_checkpoints(models: List[TrainedModel]) -> List[Checkpoint]:
    """Find models that did not achieve the best score."""
    nonbest_checkpoints = []

    for model in models:
        evaled_checkpoints = [
            cp for cp in model.checkpoints if cp.eval_results is not None
        ]

        if not evaled_checkpoints:
            continue

        perplexities = []

        for checkpoint in evaled_checkpoints:
            if len(checkpoint.eval_results) > 1:
                raise ValueError()
            repo = next(iter(checkpoint.eval_results))
            perplexity = checkpoint.eval_results[repo]["token_perplexity"]
            perplexities.append(perplexity)

        best_token_perplexity = min(perplexities)

        for checkpoint in evaled_checkpoints:
            repo = next(iter(checkpoint.eval_results))
            perplexity = checkpoint.eval_results[repo]["token_perplexity"]
            if perplexity > best_token_perplexity:
                nonbest_checkpoints.append(checkpoint)

    return nonbest_checkpoints


def delete_checkpoint(args, checkpoint):
    """Safely delete a checkpoint."""
    if args.dry_run:
        return

    for pt_file in checkpoint.path.glob("*.pt"):
        pt_file.unlink()

    configs_path = checkpoint.path / "configs"

    for config_file in configs_path.glob("*.yml"):
        config_file.unlink()

    saved = checkpoint.path / "saved"
    if saved.is_file():
        saved.unlink()

    configs_path.rmdir()
    checkpoint.path.rmdir()


def delete_nonbest_checkpoints(args):
    if args.delete_nonbest_checkpoints:
        models = find_trained_models(args)
        load_eval_results(args, models)
        nonbest_checkpoints = find_nonbest_checkpoints(models)

        print(f"\nFound {len(nonbest_checkpoints)} non-best checkpoints\n")

        for checkpoint in nonbest_checkpoints:
            print(f"Deleting checkpoint {checkpoint.path}...")
            delete_checkpoint(args, checkpoint)


def continuous_eval(args):
    """Continuously evaluate models as checkpoints get generated."""
    while True:
        models = find_trained_models(args)
        load_eval_results(args, models)

        unevaluated_checkpoints = get_unevaluated_checkpoints(models)
        print(f"\nFound {len(unevaluated_checkpoints)} unevaluated checkpoints\n")

        for checkpoint in unevaluated_checkpoints:
            print(f"Evaluating checkpoint {checkpoint.path}...")
            repo = get_finetuned_repo_path(args, checkpoint.model_path)
            evaluate_checkpoint(args, checkpoint, repo)

            # Evals can take a long time, and deletion is quick, so delete
            # after every eval to make sure non-best results don't pile up
            # and fill the drive.
            delete_nonbest_checkpoints(args)

        delete_nonbest_checkpoints(args)

        print("\n(sleeping)\n")
        time.sleep(60)


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--baseline",
        action="store_true",
        help="evaluate a baseline model (no finetuning or memory)",
    )
    parser.add_argument(
        "--memory", action="store_true", help="evaluate the memory model"
    )
    parser.add_argument(
        "--finetuned", action="store_true", help="evaluate the finetuned models"
    )
    parser.add_argument(
        "--continuous",
        action="store_true",
        help=(
            "monitor the checkpoints directory and evaluate models "
            "as they are being trained."
        ),
    )
    parser.add_argument(
        "--delete_nonbest_checkpoints",
        action="store_true",
        help=(
            "delete checkpoints in a run that get worse metrics than "
            "the best checkpoint. only applies during continuous eval."
        ),
    )
    parser.add_argument(
        "--checkpoints_path",
        type=str,
        default=(
            "/mnt/efs/augment/checkpoints/polycoder_finetuning/" "codegen-2B-multi-ft"
        ),
        help="path to checkpoints to evaluate",
    )
    parser.add_argument(
        "--show_repos",
        action="store_true",
        help="list the repos that will be evaluated on",
    )
    parser.add_argument(
        "--repos",
        type=str,
        default=None,
        help=(
            "comma-separated list of repos to evaluate on. "
            "default: evaluate on all available repos."
        ),
    )
    parser.add_argument(
        "--dry_run", action="store_true", help="create the config file but don't launch"
    )
    parser.add_argument("-v", "--verbose", action="store_true", help="be verbose")
    parser.add_argument(
        "--eval_config_file",
        type=str,
        default="/tmp/eval.yml",
        help="path of generated, temporary eval config file",
    )
    parser.add_argument(
        "--eval_jobs_path",
        type=str,
        default="/mnt/efs/augment/eval/jobs",
        help="path in which to place eval logs and results",
    )
    args = parser.parse_args()

    if args.show_repos:
        print("\n".join(REPO_PATHS))
        sys.exit(0)

    # TODO(guy) move to yaml file, and consolidate with finetune_multiple_repos
    #   which also uses these paths
    baseline_model = TrainedModel(
        Path(args.checkpoints_path) / "codegen-2B-multi", always_load_latest=True
    )
    memory_model = TrainedModel(
        Path(args.checkpoints_path) / "codegen-2B.ida.batch_distract.2022-10-19",
        always_load_latest=True,
    )

    repo_paths = get_repo_paths(args)

    if args.baseline + args.memory + args.finetuned + args.continuous > 1:
        raise ValueError("Only one evaluation mode supported at a time")

    if args.baseline:
        # Eval a single baseline model on all the repos
        generate_model_config(
            baseline_model.latest.model_path, EVAL_CONFIG_OVERRIDES_FINETUNED
        )
        generate_eval_config(
            args, baseline_model.latest, repo_paths, eval_with_memories=False
        )
        run_eval(args, Path(args.eval_jobs_path) / "baseline")
    elif args.memory:
        # Eval a single memory model on all the repos
        generate_model_config(
            memory_model.latest.model_path, EVAL_CONFIG_OVERRIDES_MEMORY
        )
        generate_eval_config(
            args, memory_model.latest, repo_paths, eval_with_memories=True
        )
        run_eval(args, Path(args.eval_jobs_path) / "memory")
    elif args.finetuned:
        # Eval each finetuned model on its own repo
        finetuned_models = find_trained_models(args)
        for model in finetuned_models:
            repo = get_finetuned_repo_path(args, model.path)
            for checkpoint in model.checkpoints:
                print(f"Iteration {checkpoint.iteration} ...")
                evaluate_checkpoint(args, checkpoint, repo)
    elif args.continuous:
        continuous_eval(args)


if __name__ == "__main__":
    main()
