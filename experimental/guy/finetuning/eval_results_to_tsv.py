"""Extract evaluation results, print as a single table."""

import argparse
import json
from pathlib import Path


def main():
    """Main."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--eval_results",
        type=str,
        default=".",
        help=(
            "Path to where evaluation json files are stored. "
            "Will be searched recusively."
        ),
    )
    args = parser.parse_args()

    print("repo\ttoken_perplexity\tbyte_perplexity")

    for fname in Path(args.eval_results).rglob("*.json"):
        with Path(fname).open("r", encoding="utf8") as f:
            data = json.load(f)
            for repo in data["results"]:
                results = data["results"][repo]
                print(
                    (
                        f"{repo}\t{results['token_perplexity']}"
                        f"\t{results['byte_perplexity']}"
                    )
                )


if __name__ == "__main__":
    main()
