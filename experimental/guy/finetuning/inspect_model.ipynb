{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.9/dist-packages/tqdm/auto.py:22: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/usr/lib/python3/dist-packages/requests/__init__.py:89: RequestsDependencyWarning: urllib3 (1.26.12) or chardet (3.0.4) doesn't match a supported version!\n", "  warnings.warn(\"urllib3 ({}) or chardet ({}) doesn't match a supported \"\n", "/usr/local/lib/python3.9/dist-packages/pandas/core/computation/expressions.py:20: UserWarning: Pandas requires version '2.7.3' or newer of 'numexpr' (version '2.7.2' currently installed).\n", "  from pandas.core.computation.check import NUMEXPR_INSTALLED\n", "2023-02-17 05:03:08.666601: I tensorflow/core/platform/cpu_feature_guard.cc:193] This TensorFlow binary is optimized with oneAPI Deep Neural Network Library (oneDNN) to use the following CPU instructions in performance-critical operations:  AVX2 FMA\n", "To enable them in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2023-02-17 05:03:08.813747: E tensorflow/stream_executor/cuda/cuda_blas.cc:2981] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "2023-02-17 05:03:09.367104: W tensorflow/stream_executor/platform/default/dso_loader.cc:64] Could not load dynamic library 'libnvinfer.so.7'; dlerror: libnvinfer.so.7: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/local/nvidia/lib:/usr/local/nvidia/lib64\n", "2023-02-17 05:03:09.367191: W tensorflow/stream_executor/platform/default/dso_loader.cc:64] Could not load dynamic library 'libnvinfer_plugin.so.7'; dlerror: libnvinfer_plugin.so.7: cannot open shared object file: No such file or directory; LD_LIBRARY_PATH: /usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/local/lib:/usr/local/mpi/lib:/usr/local/mpi/lib64:/usr/local/nvidia/lib:/usr/local/nvidia/lib64\n", "2023-02-17 05:03:09.367198: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Cannot dlopen some TensorRT libraries. If you would like to use Nvidia GPU with TensorRT, please make sure the missing libraries mentioned above are installed properly.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building CodeGenTokenizer tokenizer ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Downloading: 100%|██████████| 0.99M/0.99M [00:00<00:00, 7.75MB/s]\n", "Downloading: 100%|██████████| 446k/446k [00:00<00:00, 5.17MB/s]\n", "Downloading: 100%|██████████| 1.29M/1.29M [00:00<00:00, 11.2MB/s]\n", "Downloading: 100%|██████████| 665/665 [00:00<00:00, 632kB/s]"]}, {"name": "stdout", "output_type": "stream", "text": [" > padded vocab (size: 50295) with 905 dummy tokens (new size: 51200)\n", "> initializing torch distributed ...\n", "[2023-02-17 05:03:11,987] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[9fff4fedfba1:03303] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-02-17 05:03:12,439] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=192.168.99.2, master_port=6000\n", "[2023-02-17 05:03:12,440] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-02-17 05:03:12,456] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/augment/models/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/augment/models/gpt-neox/megatron/data'\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-02-17 05:03:12,582] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=37\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: ParallelTransformerLayerPipe\n", "    27: ParallelTransformerLayerPipe\n", "    28: ParallelTransformerLayerPipe\n", "    29: ParallelTransformerLayerPipe\n", "    30: ParallelTransformerLayerPipe\n", "    31: ParallelTransformerLayerPipe\n", "    32: ParallelTransformerLayerPipe\n", "    33: ParallelTransformerLayerPipe\n", "    34: _post_transformer_block\n", "    35: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    36: <PERSON>llelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-02-17 05:03:14,504] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+98f4a6c, git-hash=98f4a6c, git-branch=main\n", "[2023-02-17 05:03:14,506] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-02-17 05:03:14,572] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-02-17 05:03:14,574] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-02-17 05:03:14,574] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-02-17 05:03:14,575] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-02-17 05:03:14,575] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2023-02-17 05:03:14,576] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-02-17 05:03:14,576] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-02-17 05:03:14,577] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-02-17 05:03:14,577] [INFO] [config.py:763:print]   disable_allgather ............ <PERSON>alse\n", "[2023-02-17 05:03:14,578] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-02-17 05:03:14,579] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-02-17 05:03:14,579] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-02-17 05:03:14,580] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-02-17 05:03:14,597] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-02-17 05:03:14,598] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-02-17 05:03:14,599] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-02-17 05:03:14,601] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-02-17 05:03:14,602] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-02-17 05:03:14,602] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-02-17 05:03:14,604] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-02-17 05:03:14,604] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-02-17 05:03:14,605] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-02-17 05:03:14,608] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-02-17 05:03:14,609] [INFO] [config.py:763:print]   optimizer_name ............... None\n", "[2023-02-17 05:03:14,610] [INFO] [config.py:763:print]   optimizer_params ............. None\n", "[2023-02-17 05:03:14,611] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-02-17 05:03:14,611] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-02-17 05:03:14,611] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-02-17 05:03:14,612] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-02-17 05:03:14,613] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2023-02-17 05:03:14,613] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-02-17 05:03:14,614] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-02-17 05:03:14,614] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-02-17 05:03:14,615] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-02-17 05:03:14,615] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-02-17 05:03:14,616] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-02-17 05:03:14,616] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-02-17 05:03:14,617] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-02-17 05:03:14,617] [INFO] [config.py:763:print]   train_batch_size ............. 12\n", "[2023-02-17 05:03:14,618] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  12\n", "[2023-02-17 05:03:14,618] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-02-17 05:03:14,619] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-02-17 05:03:14,619] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-02-17 05:03:14,621] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": true, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-02-17 05:03:14,622] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-02-17 05:03:14,622] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-02-17 05:03:14,623] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 12, \n", "    \"train_micro_batch_size_per_gpu\": 12, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu113 as PyTorch extensions root...\n", "Creating extension directory /home/<USER>/.cache/torch_extensions/py39_cu113/utils...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu113/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "[1/2] c++ -<PERSON><PERSON> -<PERSON><PERSON> flatten_unflatten.o.d -DTORCH_EXTENSION_NAME=utils -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\\\"_gcc\\\" -DPYBIND11_STDLIB=\\\"_libstdcpp\\\" -DPYBIND11_BUILD_ABI=\\\"_cxxabi1011\\\" -isystem /usr/local/lib/python3.9/dist-packages/torch/include -isystem /usr/local/lib/python3.9/dist-packages/torch/include/torch/csrc/api/include -isystem /usr/local/lib/python3.9/dist-packages/torch/include/TH -isystem /usr/local/lib/python3.9/dist-packages/torch/include/THC -isystem /usr/include/python3.9 -D_GLIBCXX_USE_CXX11_ABI=0 -fPIC -std=c++14 -c /home/<USER>/.local/lib/python3.9/site-packages/deepspeed/ops/csrc/utils/flatten_unflatten.cpp -o flatten_unflatten.o \n", "[2/2] c++ flatten_unflatten.o -shared -L/usr/local/lib/python3.9/dist-packages/torch/lib -lc10 -ltorch_cpu -ltorch -ltorch_python -o utils.so\n", "Loading extension module utils...\n", "Time to load utils op: 12.103628635406494 seconds\n", "[2023-02-17 05:03:28,169] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=12\n", "[2023-02-17 05:03:28,202] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=37 [0, 37) STAGE_PARAMS=2779683841 (2779.684M) TOTAL_PARAMS=2779683841 (2779.684M) UNIQUE_PARAMS=2779683841 (2779.684M)\n", " > number of parameters on model parallel rank 0: 2779683841\n", " > total params: 2,779,683,841\n", "Loading: /mnt/efs/augment/checkpoints/fim_test3\n", "[2023-02-17 05:03:28,255] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/fim_test3/global_step5000/mp_rank_00_model_states.pt\n", "[2023-02-17 05:03:29,928] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_00-model_00-model_states.pt\n", "[2023-02-17 05:03:31,061] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_02-model_00-model_states.pt\n", "[2023-02-17 05:03:32,055] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_03-model_00-model_states.pt\n", "[2023-02-17 05:03:33,437] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_04-model_00-model_states.pt\n", "[2023-02-17 05:03:34,392] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_05-model_00-model_states.pt\n", "[2023-02-17 05:03:35,397] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_06-model_00-model_states.pt\n", "[2023-02-17 05:03:36,825] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_07-model_00-model_states.pt\n", "[2023-02-17 05:03:37,827] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_08-model_00-model_states.pt\n", "[2023-02-17 05:03:38,904] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_09-model_00-model_states.pt\n", "[2023-02-17 05:03:40,129] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_10-model_00-model_states.pt\n", "[2023-02-17 05:03:41,189] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_11-model_00-model_states.pt\n", "[2023-02-17 05:03:42,597] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_12-model_00-model_states.pt\n", "[2023-02-17 05:03:43,800] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_13-model_00-model_states.pt\n", "[2023-02-17 05:03:45,937] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_14-model_00-model_states.pt\n", "[2023-02-17 05:03:47,246] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_15-model_00-model_states.pt\n", "[2023-02-17 05:03:48,409] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_16-model_00-model_states.pt\n", "[2023-02-17 05:03:49,453] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_17-model_00-model_states.pt\n", "[2023-02-17 05:03:50,551] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_18-model_00-model_states.pt\n", "[2023-02-17 05:03:51,701] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_19-model_00-model_states.pt\n", "[2023-02-17 05:03:54,248] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_20-model_00-model_states.pt\n", "[2023-02-17 05:03:55,260] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_21-model_00-model_states.pt\n", "[2023-02-17 05:03:56,623] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_22-model_00-model_states.pt\n", "[2023-02-17 05:03:57,683] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_23-model_00-model_states.pt\n", "[2023-02-17 05:03:58,799] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_24-model_00-model_states.pt\n", "[2023-02-17 05:03:59,884] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_25-model_00-model_states.pt\n", "[2023-02-17 05:04:01,004] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=26 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_26-model_00-model_states.pt\n", "[2023-02-17 05:04:01,872] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_27-model_00-model_states.pt\n", "[2023-02-17 05:04:03,198] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_28-model_00-model_states.pt\n", "[2023-02-17 05:04:04,399] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=29 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_29-model_00-model_states.pt\n", "[2023-02-17 05:04:05,971] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=30 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_30-model_00-model_states.pt\n", "[2023-02-17 05:04:07,140] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=31 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_31-model_00-model_states.pt\n", "[2023-02-17 05:04:08,230] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=32 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_32-model_00-model_states.pt\n", "[2023-02-17 05:04:09,334] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=33 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_33-model_00-model_states.pt\n", "[2023-02-17 05:04:09,355] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=35 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_35-model_00-model_states.pt\n", "[2023-02-17 05:04:11,567] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=36 file=/mnt/efs/augment/checkpoints/fim_test3/global_step5000/layer_36-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/fim_test3/global_step5000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/fim_test3/global_step5000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n", "Finished loading model\n", "Got model: PipelineEngine(\n", "  (module): GPT2ModelPipe(\n", "    (tied_modules): ModuleDict()\n", "    (0): EmbeddingPipe(\n", "      (word_embeddings): VocabParallelEmbedding()\n", "      (embedding_dropout): Dropout(p=0.0, inplace=False)\n", "    )\n", "    (2): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (3): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (4): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (5): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (6): ParallelTransformerLayer<PERSON>ip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (7): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (8): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (9): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (10): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (11): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (12): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (13): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (14): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (15): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (16): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (17): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (18): ParallelTransformerLayer<PERSON>ip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (19): ParallelTransformerLayer<PERSON>ip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (20): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (21): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (22): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (23): ParallelTransformerLayer<PERSON>ip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (24): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (25): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (26): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (27): ParallelTransformerLayer<PERSON>ip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (28): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (29): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (30): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (31): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (32): ParallelTransformerLayerPipe(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (33): ParallelTransformerLayerPip<PERSON>(\n", "      (input_layernorm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "      (attention): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "        (query_key_value): ColumnParallelLinear()\n", "        (rotary_emb): R<PERSON>ry<PERSON>mbedding()\n", "        (scale_mask_softmax): FusedScaleMaskSoftmax()\n", "        (attention_dropout): Dropout(p=0.0, inplace=False)\n", "        (dense): RowParallelLinear()\n", "      )\n", "      (mlp): ParallelMLP(\n", "        (dense_h_to_4h): ColumnParallelLinear()\n", "        (dense_4h_to_h): RowParallelLinear()\n", "      )\n", "    )\n", "    (35): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "      (norm): LayerNorm((2560,), eps=1e-05, elementwise_affine=True)\n", "    )\n", "    (36): ParallelLinearPipe(\n", "      (final_linear): RowParallelLinear()\n", "    )\n", "  )\n", ")\n"]}], "source": ["from pathlib import Path\n", "\n", "import megatron\n", "import megatron.memorize\n", "import megatron.memorize.memorize_utils\n", "import megatron.text_generation_utils\n", "import megatron.training\n", "\n", "\n", "# Adapted from gpt_neox.py\n", "class GPTNeoXModel:\n", "    \"\"\"Wrapper for the GPT-NeoX model.\"\"\"\n", "\n", "    def __init__(self):\n", "        self.model = None\n", "        self.neox_args = None\n", "\n", "    def load(self):\n", "        _overwrite_values = {\n", "            \"load\": \"/mnt/efs/augment/checkpoints/fim_test3\",\n", "            \"checkpoint_activations\": <PERSON><PERSON><PERSON>,\n", "            \"partition_activations\": <PERSON><PERSON><PERSON>,\n", "            \"no_load_optim\": True,\n", "            \"zero_optimization\": None,  # disable zero optimization (won't be used in inference, and loading zero optimizer can cause errors)\n", "            # \"memorize_mode\": \"host\",\n", "        }\n", "\n", "        neox_args = megatron.neox_arguments.NeoXArgs.from_ymls(\n", "            paths_to_yml_files=[\n", "                Path(\"/mnt/efs/augment/configs/codegen-H/model/codegen-2B.ft.yml\")\n", "                #Path(\"/mnt/efs/augment/checkpoints/fim_test3\")\n", "                ],\n", "            overwrite_values=_overwrite_values,\n", "        )\n", "        neox_args.configure_distributed_args()\n", "\n", "        neox_args.build_tokenizer()\n", "\n", "        if neox_args.load is None:\n", "            raise ValueError(\"`load` parameter must be supplied to load a model`\")\n", "\n", "        # initialize megatron\n", "        megatron.initialize.initialize_megatron(neox_args)\n", "\n", "        # set up model and load checkpoint.\n", "        model, _, _ = megatron.training.setup_model_and_optimizer(\n", "            neox_args=neox_args,\n", "            use_cache=True,\n", "            iteration=neox_args.iteration,\n", "        )  # we use setup_model_and_optimizer instead of get_model in order to initialize deepspeed\n", "\n", "        # put the model into evaluation model - i.e., model.training will be False\n", "        model.eval()\n", "\n", "        print(\"Finished loading model\")\n", "\n", "        self.model = model\n", "        self.neox_args = neox_args\n", "\n", "        print(\"Got model:\", model)\n", "\n", "    def unload(self):\n", "        self.model = None\n", "        self.neox_args = None\n", "\n", "    def is_loaded(self):\n", "        return self.model is not None\n", "\n", "    def generate(\n", "        self,\n", "        text: str = \"\",\n", "        eos_token_id: int = None,\n", "        maximum_tokens: int = 64,\n", "        recompute: bool = False,\n", "        temperature: float = None,\n", "        top_k: int = None,\n", "        top_p: float = None,\n", "        mem_object_names: list = None,\n", "    ):\n", "        generated_texts = megatron.text_generation_utils.generate_samples_from_prompt(\n", "            neox_args=self.neox_args,\n", "            model=self.model,\n", "            text_tokens=[self.model.tokenizer.tokenize(text)],\n", "            eos_token_id=eos_token_id,\n", "            maximum_tokens=maximum_tokens,\n", "            recompute=recompute,\n", "            temperature=temperature if temperature is not None else 0.0,\n", "            top_k=top_k if top_k is not None else 0,\n", "            top_p=top_p if top_p is not None else 0,\n", "            mem_object_names=mem_object_names,\n", "        )\n", "\n", "        return generated_texts[0][\"text\"]\n", "\n", "    def memorize(\n", "        self,\n", "        text: str,\n", "    ):\n", "        mem_object_name = megatron.memorize.memorize_utils.memorize_text(\n", "            neox_args=self.neox_args,\n", "            model=self.model,\n", "            text=text,\n", "        )\n", "\n", "        return mem_object_name\n", "\n", "\n", "model = GPTNeoXModel()\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt: )\n", "# end of foo\n", "[SEP]\n", "def foo(a, b, c, d):\n", "    return a + b + c + d\n", "\n", "def bar(values):\n", "    result = foo(\n", "len: 626\n", "===================\n", "RESULT:\n", "values[0], values[1], values[2], values[3])\n", "    return result\n", "\n", "# end of bar\n", "[SEP]\n", "def foo(a, b, c, d):\n", "    return a + b + c + d\n", "\n", "def bar(values):\n", "    result = foo(values[0], values[1], values[2], values[3])\n", "    return result\n", "\n", "# end of bar\n", "[SEP]\n", "def foo(a, b, c, d):\n", "    return a + b + c + d\n", "\n", "def bar(values):\n", "    result = foo(values[0], values[1], values[2], values[3])\n", "    return result\n", "\n", "# end of bar\n", "[SEP]\n", "def foo(a, b, c, d):\n", "    return a + b + c + d\n", "\n", "def bar(values):\n", "    result = foo(values[0], values[1], values[2], values[3])\n", "    return result\n", "\n", "# end of bar\n", "[SEP]\n", "def foo(a, b, c, d):\n", "    return a + b + c + d\n", "\n", "def bar(\n", "===================\n", "WARNING: [EOS] not found\n"]}, {"ename": "NameError", "evalue": "name 'result' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[14], line 59\u001b[0m\n\u001b[1;32m     56\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mcolorama\u001b[39;00m\n\u001b[1;32m     57\u001b[0m \u001b[39mfrom\u001b[39;00m \u001b[39mcolorama\u001b[39;00m \u001b[39mimport\u001b[39;00m <PERSON><PERSON>, <PERSON>\n\u001b[0;32m---> 59\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m{\u001b[39;00mprefix\u001b[39m}\u001b[39;00m\u001b[39m{\u001b[39;00mFore\u001b[39m.\u001b[39mGREEN\u001b[39m}\u001b[39;00m\u001b[39m{\u001b[39;00mresult\u001b[39m}\u001b[39;00m\u001b[39m{\u001b[39;00mStyle\u001b[39m.\u001b[39mRESET_ALL\u001b[39m}\u001b[39;00m\u001b[39m{\u001b[39;00msuffix\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'result' is not defined"]}], "source": ["def make_fim_prompt(prefix, suffix):\n", "    return f\"{suffix}[SEP]{prefix}\"\n", "\n", "prefix = \"\"\"\n", "def factorial(n):\n", "    \\\"\\\"\\\"Compute the factorial of n\\\"\\\"\\\"\n", "\"\"\"\n", "suffix = \"\"\"\n", "    return n * factorial(n-1)\n", "<|endoftext|>\n", "\"\"\"\n", "\n", "prefix = \"\"\"\n", "def foo(a, b, c, d):\n", "    return a + b + c + d\n", "\n", "def bar(values):\n", "    result = foo(\"\"\"\n", "suffix = \"\"\")\n", "# end of foo\n", "\"\"\"\n", "\n", "# prefix = \"def foo:\"\n", "# suffix = \", World!\\\")\"\n", "\n", "# prefix = \"\"\"class Adapter:\n", "#     def __init__(self, a, b, c, d):\n", "#         self.a = a\"\"\"\n", "# suffix = \"\"\"\n", "#     def get_d(self):\n", "#         return self.d\n", "# \"\"\"\n", "        \n", "prompt = make_fim_prompt(prefix, suffix)\n", "\n", "print(\"Prompt:\", prompt)\n", "\n", "raw_result = model.generate(\n", "    text=prompt,\n", "    maximum_tokens=128,\n", "    temperature=0.,\n", "    )\n", "\n", "# print(\"len:\", len(raw_result))\n", "\n", "print(\"===================\")\n", "print(\"RESULT:\")\n", "print(raw_result)\n", "print(\"===================\")\n", "\n", "if \"[EOS]\" in raw_result:\n", "    result = raw_result.split(\"[EOS]\")[0]\n", "else:\n", "    print(\"WARNING: [EOS] not found\")\n", "\n", "import colorama\n", "from colorama import Fore, Style\n", "\n", "print(f\"{prefix}{Fore.GREEN}{result}{Style.RESET_ALL}{suffix}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [", World!\")[SEP]def foo:\n"]}], "source": ["print(make_fim_prompt(prefix, suffix))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f9f85f796d01129d0dd105a088854619f454435301f6ffec2fea96ecbd9be4ac"}}}, "nbformat": 4, "nbformat_minor": 2}