"""Finetune code models.

Sequentially fine-tune code models over a list of repositories.
"""

import argparse
import os
from pathlib import Path

from defs import EFS_PATH

CONFIGS_DIR = Path(EFS_PATH, "configs/codegen-H")
CONFIG_FILENAME = "/tmp/train_config.yml"
TRAIN_SCRIPT_FILENAME = "/tmp/train.sh"


def train(args):
    if Path(args.save_dir).exists() and not args.dry_run:
        print((f"Save directory {args.save_dir} already exists, aborting"))
        return

    batch_size_per_gpu = 48
    total_batch_size = batch_size_per_gpu * args.num_gpus

    if args.from_scratch:
        init_config = """
load: null
init_method: small_init
output_layer_init_method: wang_init
"""
    else:
        init_config = f"""
load: {args.load}
"""

    config = f"""
data_path: {args.dataset_path}
#train_data_paths: [{args.dataset_path}]
#valid_data_paths: []
#test_data_paths: []

{init_config}

train_batch_size: {total_batch_size}
train_micro_batch_size_per_gpu: {batch_size_per_gpu}
gradient_accumulation_steps: 1

eval_interval: {args.eval_interval}
early_stopping: {args.early_stopping}
early_stopping_threshold: 0.005

train_iters: {args.steps}
lr_decay_iters: {args.steps}
warmup: {args.warmup}
lr_decay_style: {args.lr_decay_style}

fim_probability: {args.fim_probability}

optimizer:
    params:
        betas:
        - 0.9
        - 0.999
        eps: 1.0e-08
        lr: {args.lr:e}
    type: Adam

save: {args.save_dir}
save_interval: {args.eval_interval}
keep_last_n_checkpoints: {args.keep_last_n_checkpoints}
# to keep all checkpoints
#keep_last_n_checkpoints: null
no_save_optim: False
#no_save_optim: True

wandb_name: {args.wandb_name}
wandb_group: {args.wandb_name}
"""

    train_script = f"""
mkdir -p {args.save_dir}
python ./deepy.py train.py \\
        {CONFIGS_DIR}/lr/3e-4.yml \\
        {CONFIGS_DIR}/train/big_batch.f.p4.yml \\
        {CONFIGS_DIR}/model/codegen-2B.ft.yml \\
        {CONFIG_FILENAME}
"""

    with Path(CONFIG_FILENAME).open("w", encoding="utf-8") as config_file:
        config_file.write(config)
    print(f"Saved training config in {CONFIG_FILENAME}")

    with Path(TRAIN_SCRIPT_FILENAME).open("w", encoding="utf-8") as script_file:
        script_file.write(train_script)
    print(f"Saved training shell script in {TRAIN_SCRIPT_FILENAME}")

    if args.dry_run:
        print("Dry run -- not running train command")
    else:
        print(f"Launching finetuning on {args.dataset_path}...")
        os.system(f"chmod +x {TRAIN_SCRIPT_FILENAME}")
        result = os.system(f"/bin/bash -c {TRAIN_SCRIPT_FILENAME}")
        if result:
            raise ValueError("Finetuning run failed, aborting")


def erase_optimizer_state_files(save_dir):
    """Erase the optimizer files stored as part of the checkpoint.

    These files take up a lot of space and are only needed for resuming
    training, not for inference.

    Args:
        save_dir: The root directory where checkpoints are stored.
    """
    for path in Path(save_dir).rglob("zero_pp_*"):
        path.unlink()


def main():
    """Main function."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dry_run",
        action="store_true",
        help="generate needed scripts but do not execute run",
    )
    parser.add_argument(
        "--dataset_path",
        type=str,
        required=True,
        help="dataset path to finetune on (overrides --repo_paths)",
    )
    parser.add_argument(
        "--load",
        type=str,
        default="/mnt/efs/augment/checkpoints/codegen-2B-multi",
        help="checkpoint path to train from",
    )
    parser.add_argument(
        "--from_scratch",
        action="store_true",
        help=(
            "train from scratch instead of resuming from a checkpoint. "
            "overrides --load."
        ),
    )
    parser.add_argument(
        "--lr",
        type=float,
        # equal to 0.1 the pretraining lr, leads to stable finetuning
        default=0.000008,
        help="base learning rate",
    )
    parser.add_argument(
        "--warmup",
        type=float,
        default=0,
        help="fraction of steps to use for linear warmup",
    )
    parser.add_argument(
        "--lr_decay_style",
        type=str,
        default="constant",
        help="how to decay the learning rate",
    )
    parser.add_argument(
        "--steps",
        type=int,
        default=200,
        help="number of training steps",
    )
    parser.add_argument(
        "--eval_interval",
        type=int,
        default=10,
        help="steps between evaluations",
    )
    parser.add_argument(
        "--keep_last_n_checkpoints",
        type=int,
        default=1,
        help="how many checkpoints to save",
    )
    parser.add_argument(
        "--early_stopping",
        action="store_true",
        help="stop if the validation loss goes up",
    )
    parser.add_argument(
        "--fim_probability",
        type=float,
        default=0.0,
        help="fill-in-the-middle transformation probability",
    )
    parser.add_argument(
        "--save_dir",
        type=str,
        required=True,
        help="where to save the checkpoints",
    )
    parser.add_argument(
        "--wandb_name",
        type=str,
        default="test",
        help="wandb experiments and group name",
    )
    parser.add_argument(
        "--num_gpus",
        type=int,
        default=1,
        help="number of GPUs",
    )
    args = parser.parse_args()

    train(args)
    erase_optimizer_state_files(args.save_dir)


if __name__ == "__main__":
    main()
