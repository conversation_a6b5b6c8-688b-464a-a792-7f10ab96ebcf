#!/usr/bin/env python3
"""
Classify Augment Code user feedback (from a CSV) into one of sixteen categories
using the OpenAI Responses API — now parallelized with progress reporting.
"""

import os
import csv
import time
from openai import OpenAI
import concurrent.futures
import threading

# ------------------------------------------------------------------------------
# 1) Configuration
# ------------------------------------------------------------------------------

api_key = os.getenv("OPENAI_API_KEY")
if not api_key:
    raise RuntimeError("Please set the OPENAI_API_KEY environment variable")

client = OpenAI(api_key=api_key)
MODEL = "gpt-4o-mini"
INPUT_FILE = "negative_feedback_june.csv"
OUTPUT_FILE = "june_classified_feedback.csv"

CATEGORIES = {
    # Core Issue Categories
    "1": "UX issues",
    "2": "Accuracy & Correctness",
    "3": "Instruction Following",
    "4": "Incorrect File path or context",
    "5": "Error Handling & Abrupt Termination",
    "6": "Agent quality has dropped",
    "7": "Hallucinations & Lies",
    "8": "Billing, Credits & Transaction Issues",

    # Technical/Platform Specific Issues
    "9": "IntelliJ Plugin Issue",
    "10": "VSCode Plugin Issue",
    "11": "Incorrect Tool Use",
    "13": "Test Failures",
    "14": "Stuck or Looping Without Completing Task",

    "0": "Other"
}

# ------------------------------------------------------------------------------
# 2) classify_feedback remains unchanged
# ------------------------------------------------------------------------------

def classify_feedback(feedback: str) -> str:
    system_instructions = (
        "You are a classification assistant. "
        "Given a single user feedback string about an AI IDE, "
        "choose exactly one of the following categories by outputting only the category number (0-14). "
        "Do not output any other text or punctuation.\n\n"
        "CORE ISSUE CATEGORIES:\n"
        "1. UX issues: Complaints about the system hanging, freezing, or running unacceptably slowly. "
        "Examples: 'too slow', 'Taking forever to fix its own errors'\n"
        "2. Accuracy & Correctness: The AI generates wrong, incomplete, or non-compiling code, or misunderstands environment specifics. "
        "Examples: 'wrong answer', 'Created duplicate methods and did not listen', "
        "'The compile step never completes and Augment never returns', 'Don't use the loopback models but access the database directly'\n"
        "3. Instruction Following: Fails to obey explicit user instructions or to honor configuration rules. "
        "Examples: 'I told it to not make any changes to the actual code!', 'added a heap of other classes and break them', "
        "'Ignored explicit instruction', 'Do not interfere with m padding!'\n"
        "4. Incorrect File path or context: Loses track of project context, reads the wrong directory, or ignores provided files/context. "
        "Examples: 'No. You went into the wrong directory and analyzed the wrong work', "
        "'It decided to update files in a different directory that I did not ask it to update', "
        "'It should have understood where to find the code from the conversation history'\n"
        "5. Error Handling & Abrupt Termination: Tasks abort unexpectedly or error out without useful messages. "
        "Examples: 'Agent stopped abruptly without explanation', 'Internal Error?', 'stopped execution in the middle', "
        "'Execution failed for task instrumentCode does not exist'\n"
        "6. Agent quality has dropped: Features that used to work are now broken after updates. "
        "Examples: 'Agent is failing for me after the recent Remote Agent feature launch', 'Augment is no longer usable, now it is trash'\n"
        "7. Hallucinations & Lies: Claims completion or success when tasks haven't been done; 'hallucinates' behavior or code. "
        "Examples: 'your bot lies to people', 'Said error isn't fixed in the same breath but it's fixed now', "
        "'The answer was exposed to be a lie when I looked at the PR', 'HALLUCINATING'\n"
        "8. Billing, Credits & Transaction Issues: Concerns about being charged for failed operations or wasted credits/messages. "
        "Examples: 'The transaction was unsuccessful, yet the fee was charged to my account', "
        "'I just wasted 2 hours only to get back fake data. Time and credits lost', 'waste of my time and credits'\n\n"
        "TECHNICAL/PLATFORM SPECIFIC ISSUES:\n"
        "9. IntelliJ Plugin Issue: Problems specifically with the IntelliJ IDEA plugin functionality, installation, or integration\n"
        "10. VSCode Plugin Issue: Problems specifically with the Visual Studio Code plugin functionality, installation, or integration\n"
        "11. Incorrect Tool Use: The AI uses wrong tools or commands for the task at hand\n"
        "13. Test Failures: Generated code fails tests.\n"
        "14. Stuck or Looping Without Completing Task: The system gets into infinite loops or repetitive behavior without progress\n\n"
        "0. Other: Feedback that doesn't clearly fit into any of the above categories\n\n"
    )

    for attempt in range(1, 4):
        try:
            resp = client.responses.create(
                model=MODEL,
                instructions=system_instructions,
                input=feedback.strip(),
                temperature=0.0,
            )
            choice = resp.output_text.strip()
            if choice in CATEGORIES:
                return choice
            for ch in choice.split(" "):
                if ch in CATEGORIES:
                    return ch
            return "0"
        except Exception as e:
            print(f"[Warning] OpenAI API error on attempt {attempt}: {e}")
            time.sleep(2)

    return "0"


# ------------------------------------------------------------------------------
# 3) Parallelized main() with progress every 100 classifications
# ------------------------------------------------------------------------------

def main():
    # Load all rows
    with open(INPUT_FILE, newline="", encoding="utf-8") as infile:
        reader = csv.DictReader(infile)
        rows = list(reader)
        fieldnames = reader.fieldnames + ["category"]

    notes = [row.get("note", "").strip() for row in rows]
    total = len(notes)
    categories = [None] * total

    # Shared counter + lock
    counter = [0]
    lock = threading.Lock()

    def classify_and_track(idx, note):
        cat = classify_feedback(note) if note else "0"
        with lock:
            counter[0] += 1
            if counter[0] % 100 == 0 or counter[0] == total:
                print(f"--- Classified {counter[0]}/{total} feedback items ---")
        return idx, cat

    # Fire off all classification jobs
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = [
            executor.submit(classify_and_track, i, note)
            for i, note in enumerate(notes)
        ]
        # As each one finishes, store its result
        for future in concurrent.futures.as_completed(futures):
            idx, cat = future.result()
            categories[idx] = cat

    # Write out results
    with open(OUTPUT_FILE, "w", newline="", encoding="utf-8") as outfile:
        writer = csv.DictWriter(outfile, fieldnames=fieldnames)
        writer.writeheader()

        for row, cat_num in zip(rows, categories):
            row["category"] = f"{cat_num}. {CATEGORIES[cat_num]}"
            writer.writerow(row)
            preview = row.get("note", "")[:60].replace("\n", " ")
            print(f"✅ {preview} → [{cat_num}] {CATEGORIES[cat_num]}")

    print(f"\n✅ All done! Classified feedback saved to: {OUTPUT_FILE}")


if __name__ == "__main__":
    main()
