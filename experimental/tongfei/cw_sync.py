"""
Sync a file from GCS to CW.

Usage:
    python cw_sync.py $expected_cw_path
"""

import sys
from research.determined_utils import bucket_for_augment_fs_path, sync_from_gcs


def main(cw_path: str):
    bucket, path = bucket_for_augment_fs_path(cw_path)
    url = f"gs://{bucket}/{path}"
    print(f"Syncing {url} to {cw_path}")
    sync_from_gcs([url], 0)
    print("Done.")


if __name__ == "__main__":
    main(sys.argv[1])
