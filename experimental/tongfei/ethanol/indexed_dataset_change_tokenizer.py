import numpy as np
from tqdm import tqdm

from base.tokenizers import create_tokenizer_by_name
from research.data.dataset.indexed_dataset import (
    MMapIndexedDataset,
    MMapIndexedDatasetBuilder,
)


def main(
    input: str,
    output: str,
    dtype: str,
    old_tokenizer: str,
    new_tokenizer: str,
):
    old_tokenizer = create_tokenizer_by_name(old_tokenizer)
    new_tokenizer = create_tokenizer_by_name(new_tokenizer)

    ds = MMapIndexedDataset(input, skip_warmup=True)
    dtype = {"uint16": np.uint16, "uint32": np.uint32}[dtype]
    builder = MMapIndexedDatasetBuilder(output + ".bin", dtype=np.int32)
    for i in tqdm(range(len(ds))):
        x = ds[i]
        text = old_tokenizer.detokenize(x.tolist())
        tokens = new_tokenizer.tokenize_unsafe(text)
        builder.add_item(tokens)
        builder.end_document()
    builder.finalize(output + ".idx")
    print("Done.")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, help="input path")
    parser.add_argument("--output", type=str, help="output path")
    parser.add_argument("--dtype", type=str, help="uint16 or uint32", default="uint32")
    parser.add_argument(
        "--old-tokenizer",
        type=str,
        help="old tokenizer name",
    )
    parser.add_argument(
        "--new-tokenizer",
        type=str,
        help="new tokenizer name",
    )
    args = parser.parse_args()
    main(args.input, args.output, args.dtype, args.old_tokenizer, args.new_tokenizer)
