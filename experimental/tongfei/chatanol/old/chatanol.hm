import std
#import aws

local.root = "/mnt/efs/augment/user/tongfei/hm/chatanol"
# igor_dev_bucket = aws.s3(
#     global_options="--endpoint-url https://object.las1.coreweave.com",
#     bucket="igor_dev_bucket"
# )
# gs = gcloud.storage()

package augment = std.symlink(path="/home/<USER>/proj/augment")

binks_data = {BinksData:
  v1-3-merged="/mnt/efs/augment/user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl"
  v2-gemini="/mnt/efs/augment/user/yury/binks/binks-v2-gemini/repos_with_qa.jsonl"
  v2-haiku="/mnt/efs/augment/user/yury/binks/binks-v2-haiku/repos_with_qa_fixincomplete.jsonl"
  v2-haiku-v2="/mnt/efs/augment/user/yury/binks/binks-v2-haiku-v2/repos_with_qa_fixincomplete.jsonl"
  v3-gemini="/mnt/efs/augment/user/yury/binks/binks-v3/repos_with_qa_extrareffilter.jsonl"
  v3-gemini-v2="/mnt/efs/augment/user/yury/binks/binks-v3.1/repos_with_qa.jsonl"
  v4="/mnt/efs/augment/user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl"
  v5="/mnt/efs/augment/user/yury/binks/binks-v5-files-n-dirs/repos_with_qa.jsonl"
}

tokenizer = {Tokenizer: starcoder qwen25coder}

igor_dataset_id = {IgorDataset: 17-base 17-v3.1 17-multiturn-qa 18-v5}

dtype = {Tokenizer: starcoder=uint16 qwen25coder=uint32}

task get_non_tokenized_data(id=$igor_dataset_id) -> (out="dataset.fb"):
  aws --endpoint-url https://object.las1.coreweave.com \
    s3 cp --recursive s3://igor-dev-bucket/chatanol/chatanol1-$id/04_shuffled.hybrid/ $out

task tokenize_data(augment=$, tokenizer=$, dtype=$, in=$get_non_tokenized_data.out) -> (out="dataset.fb"):
  mkdir -p $out
  mkdir -p $(pwd)/task_info
  PYTHONPATH=$augment \
    python $augment/experimental/tongfei/chatanol/tokenize_stage.py \
      --input $in --output $out --tokenizer $tokenizer --dtype $dtype --task-info-location $(pwd)/task_info

task explode_data(augment=$, in=$tokenize_data.out) -> (out="dataset.fb"):
  mkdir -p $out
  mkdir -p $(pwd)/task_info
  PYTHONPATH=$augment \
    python $augment/experimental/tongfei/chatanol/explode_stage.py \
      --input $in --output $out

task export_data(augment=$, in=$explode_data.out, tokenizer=$) -> (out="dataset.fb"):
  mkdir -p $out
  PYTHONPATH=$augment \
    python $augment/experimental/tongfei/chatanol/export_stage.py \
      --input $in --output $out --tokenizer $tokenizer

task combine_data(augment=$, in=$export_data[IgorDataset:*].out) -> out:
  mkdir -p $out
  PYTHONPATH=$augment \
    python $augment/experimental/tongfei/chatanol/combine_stage.py \
      $(ls in/*/dataset.bin | sed -e 's/\.bin$//') --output $out/dataset

task split_dev_set(augment=$, in=$combine_data.out) -> out:
  mkdir -p $out
  PYTHONPATH=$augment \
    python $augment/experimental/tongfei/chatanol/split_dev_set_stage.py \
      --input $in --output $out

init_ckpt = {InitCkpt:
  starethanol6="/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000"
  starcoder-base="/mnt/efs/augment/checkpoints/starcoderbase-1b_neox/checkpoint_v2"
  ethanol-starcoder-1b="/mnt/efs/augment/user/tongfei/hm/ethanol/select_model/BaseLM=starcoder-1b/out"
  ethanol-qwen25coder-1b5="/mnt/efs/augment/user/tongfei/hm/ethanol/select_model/default/out"
  ethanol-qwen25coder-7b=""
}

component_name = {InitCkpt:
  starethanol6="neox.load_starethanol_checkpoint"
  starcoder-base="neox.load_starethanol_checkpoint"
  ethanol-starcoder-1b="research.fastbackward.retrieval_models.load_embedder_from_checkpoint"
  ethanol-qwen25coder-1b5="research.fastbackward.retrieval_models.load_embedder_from_checkpoint"
  ethanol-qwen25coder-7b="research.fastbackward.retrieval_models.load_embedder_from_checkpoint"
}

mpsize = {InitCkpt:
  starethanol6=1
  starcoder-base=1
  ethanol-starcoder-1b=1
  ethanol-qwen25coder-1b5=2
  ethanol-qwen25coder-7b=4
}

lr = {LR: 2e-5}
weight_decay = {WeightDecay: 0.1 1e-2 1e-3 1e-4 1e-5}
max_query_len = {MaxQueryLen: 4096 2048}
max_doc_len = {MaxDocLen: 1024}
max_iters = {MaxIters: 3000 2000}

monitored_metric = "mean_ap"


task train(
    augment=$,
    data=$split_dev_set.out,
    init_ckpt=$, component_name=$, mpsize=$, tokenizer=$,
    lr=$, weight_decay=$, max_query_len=$, max_doc_len=$, max_iters=$
) -> (det_id="det.id"):
  wd=$(pwd)
  jsonnet $augment/experimental/tongfei/chatanol/train_config.jsonnet \
    --tla-str det_ws="Dev" \
    --tla-str det_proj="tongfei" \
    --tla-str wandb_proj="tongfei-chatanol" \
    --tla-str wandb_run="$HYPERMAKE_JOB_NAME[$HYPERMAKE_JOB_CASE]" \
    --tla-str init_ckpt=$init_ckpt \
    --tla-str component_name=$component_name \
    --tla-code mpsize=$mpsize \
    --tla-code gold_temp=0.01 \
    --tla-code pred_temp=10.0 \
    --tla-code logit_temp=-4 \
    --tla-code max_iters=$max_iters \
    --tla-code lr=$lr \
    --tla-code weight_decay=$weight_decay \
    --tla-str train=$data/train \
    --tla-str dev=$data/valid \
    --tla-str tokenizer=$tokenizer \
    --tla-code max_query_len=$max_query_len \
    --tla-code max_doc_len=$max_doc_len \
  > train_config.json
  (
    flock 200 || exit 1
    cd $augment
    python research/fastbackward/determined/launch.py \
      -c GCP-US1 \
      -s research/fastbackward/train_retriever.py \
      $wd/train_config.json > $wd/det_submission.log
  ) 200> /tmp/det-train.lock
  det_id=$(cat det_submission.log | sed -nE 's/Created experiment ([0-9]*)/\1/p')
  echo $det_id > det.id
  echo "Waiting for Determined job $det_id to complete..."
  det experiment wait $det_id

plan Train = {
  train[InitCkpt: ethanol-starcoder-1b, Tokenizer: starcoder]
}



task select_model(augment=$, det_id=$train.det_id, monitored_metric=$) -> out:
  # Model selection
  id=$(cat $det_id)
  det trial logs -f $id > det.log
  cat det.log | sed -nE 's/.*(report_validation_metrics\(.*\))/\1/p' > validations.py.log
  cat validations.py.log | sed -nE "s/.*steps_completed=([0-9]+).*'$monitored_metric':\s([0-9.]+).*/\1\t\2/p" > metrics.tsv
  echo "Iterations and their validation scores ($monitored_metric):"
  cat metrics.tsv
  best_iteration=$(cat metrics.tsv | sort -k2 -nr | head -n1 | cut -f1)
  echo "Best iteration is $best_iteration."
  best_iteration_uuid=$(det experiment lc $id | grep " $best_iteration " | cut -d'|' -f5 | xargs)
  echo "UUID of best checkpoint is $best_iteration_uuid. Downloading..."
  (
    cd $augment
    ./research/utils/download_checkpoint.sh $best_iteration_uuid tongfei/hm/$best_iteration_uuid
  )
  echo "Copying from the checkpoint bucket to the local bucket..."
  mkdir -p $out
  cp -r /mnt/efs/augment/checkpoints/tongfei/hm/$best_iteration_uuid/* $out

inference_model = {InferenceModel:
  llama3-instruct-8b="fastforward_llama3_instruct_8b"
  claude35-sonnet="anthropic_chat"
}
seq_len = {InferenceModel:
  llama3-instruct-8b=8192
  claude35-sonnet=8192
}
inference_tokenizer = {InferenceModel:
  llama3-instruct-8b="llama3_instruct"
  claude35-sonnet="llama3_instruct"
}
prompt_formatter = {PromptFormatter:
  binks_llama3_legacy binks-claude-v8 binks-claude-v11-1
}

augment_qa_ver = {AugmentQAVer: v3 v3_1}

task consolidate_mp_model(augment=$, ckpt=$select_model.out) -> out:
  mkdir -p $out
  PYTHONPATH=$augment python $augment/research/fastbackward/scripts/consolidate_mp_retriever.py \
    --input-dir $ckpt --output-dir $out



ckpt = {Ckpt:
  trained=$consolidate_mp_model.out
  prod="/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468"
  dev="/mnt/efs/augment/user/tongfei/hm/chatanol/convert_to_ffwd_ckpt/InitCkpt=ethanol-qwen25coder-1b5&Tokenizer=qwen25coder/out"
}

scorer_type = {Ckpt:
  trained="dense_scorer_v2_fbwd"
  prod="dense_scorer_v2_ffwd"
  dev="dense_scorer_v2_ffwd"
}

query_formatter = {QueryFormatter:
  chatanol6="base:chatanol6"
  chatanol6-sts="base:chatanol6-singleturnisspecial"
}

document_formatter = {DocFormatter:
  ethanol6="base:ethanol6-embedding-with-path-key"
  chatanol6="base:chatanol6-embedding-with-path-key"
}

task augment_qa_eval(
    augment=$, augment_qa_ver=$,
    inference_model=$, seq_len=$, inference_tokenizer=$, prompt_formatter=$,
    query_formatter=$, document_formatter=$, tokenizer=$,
    scorer_type=$, ckpt=$, max_query_len=$, max_doc_len=$
) -> (det_id="det.id", metrics="metrics.json"):
  wd=$(pwd)
  jsonnet $augment/experimental/tongfei/chatanol/augment_qa_eval_config.jsonnet \
    --tla-str det_ws="Dev" \
    --tla-str det_proj="tongfei-eval" \
    --tla-str det_name="$HYPERMAKE_JOB_NAME[$HYPERMAKE_JOB_CASE]" \
    --tla-str augment_qa_ver=$augment_qa_ver \
    --tla-str inference_model=$inference_model \
    --tla-code seq_len=$seq_len \
    --tla-str inference_tokenizer=$inference_tokenizer \
    --tla-str retriever_tokenizer=$tokenizer \
    --tla-str prompt_formatter=$prompt_formatter \
    --tla-str query_formatter=$query_formatter \
    --tla-str document_formatter=$document_formatter \
    --tla-str scorer_type=$scorer_type \
    --tla-str retriever_ckpt=$ckpt \
    --tla-code retriever_query_max_tokens=$max_query_len \
    --tla-code retriever_doc_max_tokens=$max_doc_len \
  > eval_config.json
  (
    flock 200 || exit 1
    cd $augment
    python research/eval/eval.py $wd/eval_config.json > $wd/det_submission.log
  ) 200> /tmp/det-eval.lock
  det_id=$(cat det_submission.log | sed -nE 's/Created experiment ([0-9]*)/\1/p')
  echo $det_id > det.id
  echo "Waiting for Determined job $det_id to complete..."
  det experiment wait $det_id
  det trial logs -f $det_id > det.log
  sed -Ezn 's/(.*)INFO: (\{\n[^\|]*\})\n(.*)/\2/p' det.log > metrics.json



task convert_to_ffwd_ckpt(augment=$, fbwd_ckpt=$consolidate_mp_model.out) -> (out):
    mkdir -p $out
    PYTHONPATH=$augment \
      python $augment/research/tools/ckp_converter/ffb2ffw_llama_retriever.py \
      --ckpt-path $fbwd_ckpt \
      --output-path $(pwd)/$out



plan EvalProd = {
  augment_qa_eval[AugmentQAVer:v3_1, InferenceModel:claude35-sonnet, InitCkpt:ethanol-qwen25coder-1b5, Tokenizer:qwen25coder, PromptFormatter:binks-claude-v11-1, QueryFormatter: *]
  augment_qa_eval[AugmentQAVer:v3_1, InferenceModel:claude35-sonnet, Ckpt:prod, Tokenizer:starcoder, PromptFormatter:binks-claude-v11-1, QueryFormatter: *, DocFormatter: chatanol6]
}

plan Eval = {
  augment_qa_eval[AugmentQAVer:v3_1, InferenceModel:claude35-sonnet, InitCkpt:starethanol6, Tokenizer:starcoder, PromptFormatter:binks-claude-v11-1, DocFormatter: *]
  augment_qa_eval[AugmentQAVer:v3_1, InferenceModel:claude35-sonnet, InitCkpt:ethanol-qwen25coder-1b5, Tokenizer:qwen25coder, PromptFormatter:binks-claude-v11-1, DocFormatter: *]
  augment_qa_eval[AugmentQAVer:v3_1, InferenceModel:claude35-sonnet, InitCkpt:ethanol-starcoder-1b, Tokenizer:starcoder, PromptFormatter:binks-claude-v11-1, DocFormatter: *]
}


plan Run1 = {
  convert_to_ffwd_ckpt[InitCkpt:ethanol-qwen25coder-1b5, Tokenizer:qwen25coder]
}


task train5(
    augment=$,
    data="/mnt/efs/augment/user/tongfei/hm/chatanol-5/split_train_dev/default/out/",
    init_ckpt=$, component_name=$, mpsize=$, tokenizer=$,
    lr=$, weight_decay=$, max_query_len=$, max_doc_len=$, max_iters=$
) -> (det_id="det.id"):
  wd=$(pwd)
  jsonnet $augment/experimental/tongfei/chatanol/train_config.jsonnet \
    --tla-str det_ws="Dev" \
    --tla-str det_proj="tongfei" \
    --tla-str wandb_proj="tongfei-chatanol-5" \
    --tla-str wandb_run="$HYPERMAKE_JOB_NAME[$HYPERMAKE_JOB_CASE]" \
    --tla-str init_ckpt=$init_ckpt \
    --tla-str component_name=$component_name \
    --tla-code mpsize=$mpsize \
    --tla-code gold_temp=0.01 \
    --tla-code pred_temp=10.0 \
    --tla-code logit_temp=-4 \
    --tla-code max_iters=$max_iters \
    --tla-code lr=$lr \
    --tla-code weight_decay=$weight_decay \
    --tla-str train=$data/train \
    --tla-str dev=$data/valid \
    --tla-str tokenizer=$tokenizer \
    --tla-code max_query_len=$max_query_len \
    --tla-code max_doc_len=$max_doc_len \
  > train_config.json
  (
    flock 200 || exit 1
    cd $augment
    python research/fastbackward/determined/launch.py \
      -c CW-EAST4 \
      -s research/fastbackward/train_retriever.py \
      $wd/train_config.json > $wd/det_submission.log
  ) 200> /tmp/det-train.lock
  det_id=$(cat det_submission.log | sed -nE 's/Created experiment ([0-9]*)/\1/p')
  echo $det_id > det.id
  echo "Waiting for Determined job $det_id to complete..."
  det experiment wait $det_id


task select_model5(augment=$, det_id=$train5.det_id, monitored_metric={Metric: mean_ap mean_p1}) -> out:
  # Model selection
  id=$(cat $det_id)
  det trial logs -f $id > det.log
  cat det.log | sed -nE 's/.*(report_validation_metrics\(.*\))/\1/p' > validations.py.log
  cat validations.py.log | sed -nE "s/.*steps_completed=([0-9]+).*'$monitored_metric':\s([0-9.]+).*/\1\t\2/p" > metrics.tsv
  echo "Iterations and their validation scores ($monitored_metric):"
  cat metrics.tsv
  best_iteration=$(cat metrics.tsv | sort -k2 -nr | head -n1 | cut -f1)
  echo "Best iteration is $best_iteration."
  best_iteration_uuid=$(det experiment lc $id | grep " $best_iteration " | cut -d'|' -f5 | xargs)
  echo "UUID of best checkpoint is $best_iteration_uuid. Downloading..."
  (
    cd $augment
    ./research/utils/download_checkpoint.sh $best_iteration_uuid tongfei/hm/$best_iteration_uuid cw-east4
  )
  echo "Copying from the checkpoint bucket to the local bucket..."
  mkdir -p $out
  cp -r /mnt/efs/augment/checkpoints/tongfei/hm/$best_iteration_uuid/* $out


task consolidate_mp_model5(augment=$, ckpt=$select_model5.out) -> out:
  mkdir -p $out
  PYTHONPATH=$augment python $augment/research/fastbackward/scripts/consolidate_mp_retriever.py \
    --input-dir $ckpt --output-dir $out




task eval5(
    augment=$, augment_qa_ver=$,
    inference_model=$, seq_len=$, inference_tokenizer=$, prompt_formatter=$,
    query_formatter=$, document_formatter=$, tokenizer=$,
    scorer_type=$, ckpt=$consolidate_mp_model5.out, max_query_len=$, max_doc_len=$
) -> (det_id="det.id", metrics="metrics.json"):
  wd=$(pwd)
  jsonnet $augment/experimental/tongfei/chatanol/augment_qa_eval_config.jsonnet \
    --tla-str det_ws="Dev" \
    --tla-str det_proj="tongfei-eval" \
    --tla-str det_name="$HYPERMAKE_JOB_NAME[$HYPERMAKE_JOB_CASE]" \
    --tla-str augment_qa_ver=$augment_qa_ver \
    --tla-str inference_model=$inference_model \
    --tla-code seq_len=$seq_len \
    --tla-str inference_tokenizer=$inference_tokenizer \
    --tla-str retriever_tokenizer=$tokenizer \
    --tla-str prompt_formatter=$prompt_formatter \
    --tla-str query_formatter=$query_formatter \
    --tla-str document_formatter=$document_formatter \
    --tla-str scorer_type=$scorer_type \
    --tla-str retriever_ckpt=$ckpt \
    --tla-code retriever_query_max_tokens=$max_query_len \
    --tla-code retriever_doc_max_tokens=$max_doc_len \
  > eval_config.json
  (
    flock 200 || exit 1
    cd $augment
    python research/eval/eval.py $wd/eval_config.json > $wd/det_submission.log
  ) 200> /tmp/det-eval.lock
  det_id=$(cat det_submission.log | sed -nE 's/Created experiment ([0-9]*)/\1/p')
  echo $det_id > det.id
  echo "Waiting for Determined job $det_id to complete..."
  det experiment wait $det_id
  det trial logs -f $det_id > det.log
  sed -Ezn 's/(.*)INFO: (\{\n[^\|]*\})\n(.*)/\2/p' det.log > metrics.json



plan M5 = {
  consolidate_mp_model5[InitCkpt: ethanol-qwen25coder-1b5, Tokenizer:qwen25coder, Metric: *]
  eval5[
    InitCkpt: ethanol-qwen25coder-1b5, Tokenizer:qwen25coder,
    AugmentQAVer:v3_1,
    InferenceModel:claude35-sonnet,
    SeqLen:4096,
    InferenceTokenizer:starcoder,
    PromptFormatter:binks-claude-v11-1,
    QueryFormatter: chatanol6,
    DocFormatter: chatanol6,
    Tokenizer:qwen25coder,
    Metric: *
  ]
}
