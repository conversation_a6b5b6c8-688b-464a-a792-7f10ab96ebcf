import dataclasses
import gc
import json
import math
import random
import sys
import statistics
import time
import logging
import os.path
from functools import partial
from pathlib import Path
from types import SimpleNamespace
from typing import Any, Dict, Generator, Iterable, List, Mapping, Sequence

import numpy as np
import pandas as pd
from pyspark.sql import SparkSession
import torch
import torch.nn.functional as torchF
from base.prompt_format_retrieve.prompt_formatter import Retriever<PERSON>rom<PERSON><PERSON><PERSON>atter

from base.tokenizers import create_tokenizer_by_name
from base.prompt_format_retrieve import (
    DocumentRetrieverPromptInput,
    ChatRetrieverPromptInput,
    get_retrieval_prompt_formatter_by_name,
)
from research.core.model_input import ChatInput, ModelInput
from research.data.dataset.indexed_dataset import MMapIndexedDataset, make_builder
from research.data.spark import k8s_session
from research.data.spark.pipelines.pipeline_utils import ObjectDict
from research.data.spark.pipelines.stages.common import export_indexed_dataset
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import get_local_session
from research.eval.harness.factories import (
    create_model,
    create_retriever,
)
from research.retrieval.query_formatters import Ethan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>atter
from research.retrieval.chunk_formatters import get_chunk_formatter
from research.retrieval.types import Chunk, Document
from research.retrieval.utils import parse_yaml_config

from experimental.tongfei.chatanol.common import (
    chunk_to_dict,
    deserialize_retrieved_chunks,
    expand_chunk,
    select_chunk_lines,
    distill_config,
    DatasetConfig,
)
from experimental.tongfei.data.schemas import (
    PerplexityScoreInfo,
    PerplexityScoreTimerInfo,
    PerplexityScoreSecondaryInfo,
)
from experimental.tongfei.util.parquet_util import (
    map_parquet_locally_in_spark,
    map_parquet_locally_without_spark,
)

dataset_config = DatasetConfig()

query_prompt_formatter_config = {
    "name": "chatanol6",
    "max_tokens": dataset_config.seq_length - 1,
    "tokenizer_name": dataset_config.tokenizer_name,
}

key_prompt_formatter_config = {
    "name": "chatanol6-embedding-with-path-key",
    "max_tokens": dataset_config.doc_seq_length - 1,
    "add_path": True,
    "tokenizer_name": dataset_config.tokenizer_name,
}

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)


def get_spark_session(local: bool = False) -> SparkSession:
    if local:
        sess = get_local_session(
            workers=32,
            memory_per_worker_mb=16384,
            name="tongfei-distill-tokenize",
            conf={
                "spark.executor.pyspark.memory": "64G",
                "spark.executor.memory": "32G",
            },
        )
    else:
        sess = k8s_session(
            name="tongfei-distill-tokenize",
            conf={
                "spark.executor.pyspark.memory": "1024G",
                "spark.executor.memory": "64G",
            },
            max_workers=32,
        )
    sess.sparkContext.setLogLevel("INFO")
    return sess


def stage_tokenize(
    input_path: str,
    output_path: str,
    tokenizer_name: str,
    task_info_location: str,
    local: bool = False,
    dtype: np.dtype = np.uint16,
):
    dataset_config.tokenizer_name = tokenizer_name
    tokenizer = create_tokenizer_by_name(dataset_config.tokenizer_name)

    def create_prompt_formatter(
        formatter_config,
    ) -> RetrieverPromptFormatter[ChatRetrieverPromptInput]:
        cls_name, kwargs = parse_yaml_config(formatter_config)
        return get_retrieval_prompt_formatter_by_name(cls_name, tokenizer)

    def create_chunk_formatter(
        formatter_config,
    ) -> RetrieverPromptFormatter[DocumentRetrieverPromptInput]:
        cls_name, kwargs = parse_yaml_config(formatter_config)
        return get_retrieval_prompt_formatter_by_name(cls_name, tokenizer)

    spark = get_spark_session(local=local)

    def pack_prompt(
        prompt: list[int], pad_token: int, should_pad: bool = True
    ) -> bytearray:
        if should_pad:
            prompt_arr = np.pad(
                prompt,
                (0, 1 + dataset_config.seq_length - len(prompt)),
                constant_values=pad_token,
            )
        else:
            prompt_arr = np.array(prompt)

        return bytearray(prompt_arr.astype(dtype).newbyteorder("<").tobytes())

    def pack_prompts(
        question: str = "",
        answer: str = "",
        paths: str = "",
        retrieved_chunks: str = "",
        task_info_location: str = "",
        ppl_scores: Any = None,
        query_prompt_formatter_config: dict[str, Any] | None = None,
        key_prompt_formatter_config: dict[str, Any] | None = None,
    ) -> bytearray:
        assert dataset_config.known_chunk_format == 4
        logger.info(f"question: {question}")

        retrieved_chunks_list: list[Chunk] = deserialize_retrieved_chunks(
            retrieved_chunks
        )

        if len(retrieved_chunks_list) < dataset_config.retrieved_docs:
            raise ValueError(
                f"Too few retrieved chunks: {len(retrieved_chunks_list)}, expected {dataset_config.retrieved_docs}"
            )

        retrieved_chunks_list = retrieved_chunks_list[: dataset_config.retrieved_docs]
        ppl_scores = PerplexityScoreInfo.from_json(ppl_scores)

        # pull in registrations
        import research.core.prompt_formatters  # noqa: F401
        import research.retrieval.query_formatters  # noqa: F401
        import research.retrieval.chunk_formatters  # noqa: F401

        query_prompt_formatter = create_prompt_formatter(query_prompt_formatter_config)
        key_prompt_formatter = create_chunk_formatter(key_prompt_formatter_config)

        # eoq_token = query_prompt_formatter.tokenizer.vocab[b"<|ret-endofquery|>"]
        eok_token = key_prompt_formatter.tokenizer.special_tokens.end_of_key
        eod_token = key_prompt_formatter.tokenizer.special_tokens.eos
        pad_token = key_prompt_formatter.tokenizer.special_tokens.padding

        known_chunks = deserialize_retrieved_chunks(ppl_scores.secondary.chunks)
        logger.info("known_chunks deserialized")

        known_chunk_labels: list[tuple[float, float, float]] = list(
            zip(
                ppl_scores.secondary.gain,
                ppl_scores.secondary.gain_expand_left,
                ppl_scores.secondary.gain_expand_right,
            )
        )

        assert len(known_chunks) == len(ppl_scores.secondary.gain)
        assert len(known_chunks) == len(ppl_scores.secondary.gain_expand_left)
        assert len(known_chunks) == len(ppl_scores.secondary.gain_expand_right)

        assert dataset_config.known_chunk_format == 4

        # shuffle known chunks and their labels
        shuffle_idx = np.random.permutation(len(known_chunks))
        known_chunks = [known_chunks[i] for i in shuffle_idx]
        known_chunk_labels = [known_chunk_labels[i] for i in shuffle_idx]
        logger.info("known_chunks shuffled")

        # We may have to reconstruct the text of the parent doc for
        # the known chunks because we didn't save it earlier in the
        # pipeline.
        doc_text_map = {
            chunk.parent_doc.id: chunk.parent_doc.text
            for chunk in retrieved_chunks_list
            if chunk.parent_doc.text != ""
        }
        for chunk in known_chunks:
            if chunk.parent_doc.text == "":
                chunk.parent_doc.text = doc_text_map[chunk.parent_doc.id]
                assert (
                    chunk.text
                    == chunk.parent_doc.text[
                        chunk.char_offset : chunk.char_offset + chunk.length
                    ]
                ), (
                    chunk.text,
                    chunk.parent_doc.text[
                        chunk.char_offset : chunk.char_offset + chunk.length
                    ],
                )
        logger.info("doc_text_map constructed")

        assert dataset_config.known_chunk_format == 4

        def scaling_factor(chunk: Chunk):
            lines = chunk.length_in_lines
            chunk_lines = 30
            return min(1, lines / chunk_lines)

        default_expand_factor = distill_config["secondary"]["expand_factor"]
        known_chunk_labels = list(
            (
                gain / scaling_factor(chunk),
                (gain + gain_expand_left)
                / scaling_factor(
                    expand_chunk(chunk, default_expand_factor, to_left=True)
                ),
                (gain + gain_expand_right)
                / scaling_factor(
                    expand_chunk(chunk, default_expand_factor, to_left=False)
                ),
            )
            for chunk, (gain, gain_expand_left, gain_expand_right) in zip(
                known_chunks, known_chunk_labels
            )
        )

        # We need to estimate the length of the prompt, so we need to
        # first account for the question
        question_tokens_num = len(
            query_prompt_formatter.tokenizer.tokenize_safe(question)
        )
        known_chunks_tokens_num = (
            dataset_config.known_chunks_budget + 1
        )  # exceed for the initial case

        # ... and then add estimated lengths of the known chunks.
        # We stop before we exceed the budget.
        known_chunk_batches = []
        logger.info("known_chunks_tokens_num initialized")

        assert dataset_config.dataset_format == 3
        known_chunk_labels2 = []
        for chunk, label_tuple in zip(known_chunks, known_chunk_labels):
            # estimate the length
            prompt_len = (
                len(
                    query_prompt_formatter.tokenizer.tokenize_safe(
                        chunk.parent_doc.path + "\n" + chunk.text
                    )
                )
                + 2
            )  # budget for two special tokens

            if (
                known_chunks_tokens_num + prompt_len
                > dataset_config.known_chunks_budget
            ):
                known_chunk_batches.append([])
                known_chunks_tokens_num = question_tokens_num
                if (
                    known_chunks_tokens_num + prompt_len
                    > dataset_config.known_chunks_budget
                ):
                    # We can't fit this chunk even on its own with the question.
                    # Let's skip the chunk and hopefully the next one will be smaller.
                    continue

            known_chunk_labels2.append(label_tuple)
            known_chunk_batches[-1].append(chunk)
            known_chunks_tokens_num += prompt_len

        known_chunk_labels = known_chunk_labels2
        logger.info("known_chunk_labels2 appended")

        for batch in known_chunk_batches:
            assert question_tokens_num + len(batch) <= dataset_config.seq_length

        assert not hasattr(dataset_config, "add_bare_query")
        known_chunk_extra = {
            "known_chunks": [],
            "add_bare_query": True,
        }

        chunk_scores = ppl_scores.scores

        if dataset_config.use_ppl_gain:
            if ppl_scores.empty_score is not None:
                empty_score = ppl_scores.empty_score
            else:
                # This is an older dataset, so we don't have an empty_score.
                # Thankfully we can derive it.
                assert len(ppl_scores.secondary.chunk_scores) == len(
                    ppl_scores.secondary.gain
                )
                empty_score = statistics.mean(
                    sec_score - sec_gain
                    for sec_score, sec_gain in zip(
                        ppl_scores.secondary.chunk_scores,
                        ppl_scores.secondary.gain,
                    )
                )
            chunk_scores = [score - empty_score for score in chunk_scores]

        chunk_tuples = [
            (chunk, chunk_scores[i]) for i, chunk in enumerate(retrieved_chunks_list)
        ]
        logger.info("chunk_tuples constructed")

        doc_prompts = []
        for chunk, ppl_score in chunk_tuples:
            prompt_input = DocumentRetrieverPromptInput(
                text=chunk.text,
                path=chunk.parent_doc.path,
            )
            # Format the prompt
            prompt = key_prompt_formatter.format_prompt(prompt_input).tokens()
            if len(prompt) > dataset_config.doc_seq_length:
                if dataset_config.allow_doc_clipping:
                    prompt = prompt[: dataset_config.doc_seq_length]
                else:
                    raise ValueError(
                        f"Prompt too long: {len(prompt)} > {dataset_config.doc_seq_length}"
                    )

            # Encode the perplexity score into tokens.
            ppl_info_tokens = key_prompt_formatter.tokenizer.tokenize_safe(
                f"{ppl_score}"
            )

            # Format the footer of the prompt
            if dataset_config.dataset_format == 1:
                suffix = ppl_info_tokens + [eod_token]
            else:
                suffix = ppl_info_tokens + [eok_token]

            prompt.extend(suffix)

            # Check that the prompt is not too long
            if len(prompt) > dataset_config.seq_length:
                print("===================================================")
                print(key_prompt_formatter.tokenizer.detokenize(prompt))
                print("===================================================")
                raise ValueError(
                    f"{id} token length exceeds seq_len: {len(prompt)} > {dataset_config.seq_length}"
                )

            doc_prompts.append((ppl_score, prompt))
        logger.info("doc_prompts appended")

        # sort the documents in descending order of perplexity and keep the better ones
        doc_prompts = [
            prompt
            for _ppl_score, prompt in sorted(
                doc_prompts, key=lambda x: x[0], reverse=True
            )
        ][: dataset_config.retrieved_docs]
        logger.info("doc_prompts sorted")

        # optionally shuffle the docs -- if the retriever training will see the order
        # of the docs (e.g., if multiple docs are packed into each sequence) -- shuffling
        # is important.
        if dataset_config.shuffle_docs:
            random.shuffle(doc_prompts)

        # group the documents into prompts
        assert dataset_config.dataset_format == 3
        # TODO: hack to get rid of empty questions
        if len(question) == 0:
            question = "?"

        retrieval_query_prompt = query_prompt_formatter.format_prompt(
            ChatRetrieverPromptInput(
                prefix="",
                suffix="",
                path="",
                message=question,
                selected_code="",
            ),
        ).tokens()  # <eoq> already included in the query prompt formatter
        logger.info("retrieval_query_prompt formatted")

        known_chunk_query_prompts = []
        for known_chunk_batch in known_chunk_batches:
            extra = known_chunk_extra.copy()
            extra["known_chunks"] = known_chunk_batch
            prompt = query_prompt_formatter.format_prompt(
                ChatRetrieverPromptInput(
                    prefix="",
                    suffix="",
                    path="",
                    message=question,
                    selected_code="",
                )
                # TODO: this is weird, seems that it should use key_prompt_formatter instead
            ).tokens()
            known_chunk_query_prompts.append(prompt)

        # known_chunks_labels_prompt = query_prompt_formatter.tokenizer.tokenize_unsafe(
        #     json.dumps(
        #         {
        #             "known_chunk_labels": known_chunk_labels,
        #         }
        #     )
        # )
        # logger.info("known_chunks_labels_prompt formatted")

        def F(name, prompts):
            def T(x):
                return query_prompt_formatter.tokenizer.tokenize_safe(x)

            # eod_id = query_prompt_formatter.tokenizer.special_tokens.eos
            return (
                # [eod_id]
                # + T(name)
                # + [eod_id]
                # + T(str(len(prompts)))
                # + [eod_id]
                sum([prompt for prompt in prompts], [])
            )

        all_tokens = (
            F("retrieval_query", [retrieval_query_prompt])
            # + F("known_chunks_query", known_chunk_query_prompts)
            # + F("known_chunks_labels", [known_chunks_labels_prompt])
            + F("retrieved_chunks", doc_prompts)
        )
        return pack_prompt(all_tokens, pad_token, should_pad=False)

    map_parquet.apply(
        spark,
        partial(
            pack_prompts,
            query_prompt_formatter_config=query_prompt_formatter_config,
            key_prompt_formatter_config=key_prompt_formatter_config,
        ),
        input_path=input_path,
        output_path=output_path,
        output_column="prompt_tokens",
        timeout=7200,
        pass_as_kwargs=True,
        task_info_location=task_info_location,
    )
    # map_parquet_locally_without_spark(
    #     input_path=input_path,
    #     output_path=output_path,
    #     func=partial(
    #         pack_prompts,
    #         query_prompt_formatter_config=query_prompt_formatter_config,
    #         key_prompt_formatter_config=key_prompt_formatter_config,
    #     ),
    # )

    spark.stop()


if __name__ == "__main__":
    import argparse

    # Parse the arguments
    parser = argparse.ArgumentParser()

    parser.add_argument("--input", type=str, help="input path")
    parser.add_argument("--output", type=str, help="output path")
    parser.add_argument(
        "--tokenizer", type=str, help="tokenizer type", default="starcoder"
    )
    parser.add_argument("--dtype", type=str, help="uint16 or uint32", default="uint16")
    parser.add_argument(
        "--task-info-location", type=str, help="task info location", default=None
    )
    parser.add_argument("--local", action="store_true", default=False)
    args = parser.parse_args()
    args.input = os.path.abspath(os.path.realpath(args.input))
    args.output = os.path.abspath(os.path.realpath(args.output))
    dtype = {"uint16": np.uint16, "uint32": np.uint32}[args.dtype]
    print(f"Operating on {args.input}, with dtype = {dtype}")

    stage_tokenize(
        input_path=args.input,
        output_path=args.output,
        tokenizer_name=args.tokenizer,
        task_info_location=args.task_info_location,
        local=args.local,
        dtype=dtype,
    )
