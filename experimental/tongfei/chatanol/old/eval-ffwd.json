{"augment": {"gpu_count": 1}, "determined": {"metaconfig": "jobs/templates/eval-exec-v2-metaconfig.yaml", "name": "augment_qa_eval[AugmentQAVer: v3_1, InferenceModel: claude35-sonnet, InitCkpt: ethanol-qwen25coder-1b5, PromptFormatter: binks-claude-v11-1, Tokenizer: qwen25coder]", "project": "tongfei-eval", "workspace": "<PERSON>"}, "podspec": "1xH100.yaml", "system": {"experimental": {"retriever_top_k": 128}, "generation_options": {"max_generated_tokens": 8192, "temperature": 0, "top_k": 0, "top_p": 0}, "model": {"name": "anthropic_chat", "sequence_length": 8192}, "name": "chat_rag", "prompt_formatter": {"chat_history_len": 4096, "max_prompt_len": 12288, "message_len": -1, "path_len": 256, "prefix_len": 2048, "prompt_formatter_name": "binks-claude-v11-1", "retrieval_len": -1, "retrieval_len_for_user_guided": 8000, "retrieval_len_per_each_user_guided_file": 3000, "selected_code_len": -1, "suffix_len": 2048, "tokenizer_name": "llama3_instruct"}, "retriever": {"chunker": {"max_lines_per_chunk": 30, "name": "line_level"}, "document_formatter": {"add_path": true, "max_tokens": 1024, "name": "base:ethanol6-embedding-with-path-key", "tokenizer_name": "qwen25coder"}, "query_formatter": {"max_tokens": 4096, "name": "base:chatanol6", "tokenizer_name": "qwen25coder"}, "scorer": {"checkpoint_path": "/mnt/efs/augment/user/tongfei/hm/chatanol/convert_to_ffwd_ckpt/InitCkpt=ethanol-qwen25coder-1b5&Tokenizer=qwen25coder/out", "name": "dense_scorer_v2_ffwd_llama", "model_name": "qwen2_5-coder-1.5b-retriever", "tokenizer_name": "qwen25coder", "sha256": "ee4e3c3896cc94f318a2a023d2902c6843fb1487440f7200b6d233299cc896b2"}}, "verbose": false}, "task": {"dataset_path": "/mnt/efs/augment/data/processed/augment_qa/v3_1", "html_report_output_dir": "/mnt/efs/augment/public_html/augment_qa/v3_1", "name": "augment_qa"}}