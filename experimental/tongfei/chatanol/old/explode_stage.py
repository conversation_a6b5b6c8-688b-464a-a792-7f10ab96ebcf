import os
from functools import partial
from experimental.tongfei.chatanol.common import DatasetConfig
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet


dataset_config = DatasetConfig()


def explode_stage(stage5_uri, stage6_uri, small_cluster):
    def explode_prompts(batch, config=None):
        assert dataset_config.retrieved_docs % dataset_config.doc_batch_group_size == 0
        num_doc_groups = (
            dataset_config.retrieved_docs // dataset_config.doc_batch_group_size
        )

        # Note: str.len() works for lists too
        if dataset_config.dataset_format == 1:
            filtered_batch = batch[
                batch["prompt_tokens"].str.len() == num_doc_groups + 1
            ]
        else:
            filtered_batch = batch

        results = filtered_batch.explode("prompt_tokens")
        return results if len(results) > 0 else None

    print("Exploding prompts...", stage5_uri, stage6_uri)
    spark = k8s_session(
        name="igor-distill-explode",
        max_workers=32 if not small_cluster else 1,
    )
    map_parquet.apply_pandas(
        spark,
        partial(
            explode_prompts,
            config=dataset_config,
        ),
        input_path=stage5_uri,
        output_path=stage6_uri,
        output_column="prompt_tokens",
        timeout=7200,
    )


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, help="stage5 uri")
    parser.add_argument("--output", type=str, help="stage6 uri")
    args = parser.parse_args()

    args.input = os.path.abspath(os.path.realpath(args.input))
    args.output = os.path.abspath(os.path.realpath(args.output))
    explode_stage(args.input, args.output, False)
