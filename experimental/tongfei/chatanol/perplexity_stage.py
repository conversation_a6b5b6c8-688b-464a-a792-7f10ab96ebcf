import argparse
import dataclasses
from pathlib import Path

import ray
from tqdm import tqdm
from typing import Any, Dict, List, Sequence

import pandas as pd
import torch
import torch.nn.functional as F

from research.data.ray.ray_utils import AbstractRay<PERSON><PERSON>, Ray<PERSON>unner
from research.core.llama_prompt_formatters import ChatTemplateBasedPromptFormatter
from research.core.model_input import ModelInput, ChatInput
from research.core.types import Chunk, Document
from research.models import GenerativeLanguageModel
from research.models.fastbackward_models import FastBackwardLLM

from experimental.tongfei.data.binks_schemas import (
    GeneratedRetrievalTrainingInstance,
    SilverRetrievalTrainingInstance,
)


def score_chunks(
    model: GenerativeLanguageModel,
    model_input: ModelInput,
    chunk_lists: list[
        list[Chunk]
    ],  # Batch, candidate (internal list may be of length 1)
    batch_size: int = 8,
) -> list[float]:
    scores: list[float] = []
    for i in range(0, len(chunk_lists), batch_size):
        try:
            output = model.forward_pass(
                [
                    dataclasses.replace(model_input, retrieved_chunks=chunk_list)
                    for chunk_list in chunk_lists[i : (i + batch_size)]
                ]
            )
            scores += [
                -F.cross_entropy(
                    o.logits[o.target_mask],
                    o.label_tokens[o.target_mask],
                    reduction="mean",
                ).item()
                for o in output
            ]
        except torch.cuda.OutOfMemoryError:
            pass
            # TODO

    return scores


def compute_perplexity(
    sample: GeneratedRetrievalTrainingInstance,
    model: GenerativeLanguageModel,
    batch_size: int = 8,
    max_question_len: int = 512,
    max_answer_len: int = 2048,
) -> SilverRetrievalTrainingInstance:
    """
    Compute perplexity scores for a GeneratedRetrievalTrainingExample.

    Args:
        sample: The training example containing question, answer, and retrieved chunks
        model: The language model to use for scoring
        batch_size: Batch size for processing chunks
        max_question_tokens: Maximum number of chars for the question
        max_answer_tokens: Maximum number of chars for the answer

    Returns:
        A JSON string containing perplexity scores
    """
    # For debugging
    print(f"\nComputing perplexity for question: {sample.question[:50]}...")
    chat_input = ChatInput(
        history=[],
        request=sample.question[: max_question_len - 1],
    )

    model_input = ModelInput(
        retrieved_chunks=[],
        target=sample.answer[: max_answer_len - 1],
        chat_input=chat_input,
        selected_code="",
        path="",
    )

    chunk_scores = score_chunks(
        model=model,
        model_input=model_input,
        chunk_lists=[[c] for c in sample.retrieved_chunks],
        batch_size=batch_size,
    )

    return SilverRetrievalTrainingInstance(
        question=sample.question,
        chunks=sample.retrieved_chunks,
        perplexities=chunk_scores,
    )


class FBwdLLMForDistillation(FastBackwardLLM):
    def __init__(
        self,
        tokenizer_name: str,
        checkpoint_path: str,
        seq_length: int,
        model_parallel_size: int = 1,
        batch_size: int = 8,
    ):
        self.tokenizer_name = tokenizer_name
        super().__init__(
            checkpoint_path=Path(checkpoint_path),
            seq_length=seq_length,
            model_parallel_size=model_parallel_size,
            kv_cache_batch_size=batch_size,
        )
        self.load()

    def create_default_formatter(self):
        distill_prompt_template = """You are an AI programming assistant, and you only answer questions related to computer science.
        For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer.

        ### Instruction:
        {%- for chunk in retrieved_chunks %}
        Consider the following excerpt from {{chunk.parent_doc.path}}{% if chunk.header %} under header `{{chunk.header}}`{% endif %}:
        ```
        {{chunk.text}}
        ```

        {%- endfor %}

        {{message}}

        ### Response:
        """
        return ChatTemplateBasedPromptFormatter(
            template=distill_prompt_template,
            tokenizer_name=self.tokenizer_name,
            max_prompt_len=1024,
        )


class ComputePerplexityStage(
    AbstractRayActor[
        GeneratedRetrievalTrainingInstance, SilverRetrievalTrainingInstance
    ]
):
    def __init__(
        self,
        tokenizer: str,
        model_ckpt: str,
        model_mp_size: int,
        batch_size: int = 8,
    ):
        super().__init__(
            input_cls=GeneratedRetrievalTrainingInstance,
            output_cls=SilverRetrievalTrainingInstance,
        )
        self.batch_size = batch_size
        self.model = FBwdLLMForDistillation(
            tokenizer_name=tokenizer,
            checkpoint_path=model_ckpt,
            seq_length=1024,
            model_parallel_size=model_mp_size,
            batch_size=batch_size,
        )

    def process(
        self, x: GeneratedRetrievalTrainingInstance
    ) -> list[SilverRetrievalTrainingInstance]:
        out = compute_perplexity(x, self.model, batch_size=self.batch_size)
        return [out]


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--mode", type=str, default="ray", choices=["local", "ray"])
    parser.add_argument(
        "--input", type=str, help="Dataset of GeneratedRetrievalTrainingExample"
    )
    parser.add_argument(
        "--output", type=str, help="Dataset of SilverRetrievalTrainingExample"
    )
    parser.add_argument("--tokenizer", type=str, help="Tokenizer")
    parser.add_argument("--model-ckpt", type=str, help="Teacher model checkpoint")
    parser.add_argument(
        "--model-mp-size", type=int, default=1, help="Model parallel size"
    )
    parser.add_argument(
        "--batch-size", type=int, default=2, help="Batch size for scoring chunks"
    )
    parser.add_argument("--num-workers", type=int, default=1, help="Number of workers")
    parser.add_argument(
        "--gpus-per-worker", type=int, default=1, help="Number of GPUs per worker"
    )
    args = parser.parse_args()

    with RayRunner(
        actor_cls=ComputePerplexityStage,
        actor_args={
            "tokenizer": args.tokenizer,
            "model_ckpt": args.model_ckpt,
            "model_mp_size": args.model_mp_size,
            "batch_size": args.batch_size,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=8,
        num_gpu_per_worker=args.gpus_per_worker,
        local=args.mode == "local",
    ) as runner:
        runner.process_jsonl(args.input, args.output)
