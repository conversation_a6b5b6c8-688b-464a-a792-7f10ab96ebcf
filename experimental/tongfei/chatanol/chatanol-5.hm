import std
import gcloud

local.root = "/home/<USER>/exp/chatanol-5"

object gcs = gcloud.storage(root="gs://gcp-us1-user/tongfei/hm/chatanol-5")

package augment = std.symlink(path="/home/<USER>/proj/augment")


task binks1() -> (out@gcs="binks1.jsonl"):
  gcloud storage cp \
    gs://gcp-us1-user/yury/binks/binks-v1.3-merged/repos_with_qa.jsonl \
    $out

task binks2() -> (out@gcs="binks2.jsonl"):
  gcloud storage objects compose \
    gs://gcp-us1-user/yury/binks/binks-v2-gemini/repos_with_qa.jsonl \
    gs://gcp-us1-user/yury/binks/binks-v2-haiku/repos_with_qa_fixincomplete.jsonl \
    gs://gcp-us1-user/yury/binks/binks-v2-haiku-v2/repos_with_qa_fixincomplete.jsonl \
    $out

task binks3() -> (out@gcs="binks3.jsonl"):
  gcloud storage objects compose \
    gs://gcp-us1-user/yury/binks/binks-v3/repos_with_qa_extrareffilter.jsonl \
    gs://gcp-us1-user/yury/binks/binks-v3.1/repos_with_qa.jsonl \
    $out

task binks4() -> (out@gcs="binks4.jsonl"):
  gcloud storage cp \
    gs://gcp-us1-user/yury/binks/binks-v4/repos_with_qa_withanswers.jsonl \
    $out

task binks5() -> (out@gcs="binks5.jsonl"):
  gcloud storage cp \
    gs://gcp-us1-user/yury/binks/binks-v5-files-n-dirs/repos_with_qa.jsonl \
    $out

binks_data = {Binks:
  pilot="gs://gcp-us1-user/tongfei/data/binks-test.jsonl"
  1=$binks1.out
  2=$binks2.out
  3=$binks3.out
  4=$binks4.out
  5=$binks5.out
}

dtype = {Tokenizer: qwen25coder=uint32  starcoder=uint16}
chunker = {Chunker: smart_line_level}

scorer_type = {Retriever0:
  chatanol-4="dense_scorer_v2_fbwd"
}
scorer_ckpt = {Retriever0:
  chatanol-4="/mnt/efs/augment/checkpoints/chatanol/chatanol-4-fbwd"
}
scorer_tokenizer = {Retriever0:
  chatanol-4="qwen25coder"
}

teacher_model_tokenizer = {TeacherModel:
  qwen25coder-14b="qwen25coder"
  qwen25coder-32b="qwen25coder"
  qwen3-32b="qwen3"
}

student_model_tokenizer = {StudentModel:
  qwen25coder-1b5="qwen25coder"
  qwen3-0b6="qwen3"
  qwen3-1b5="qwen3"
}

teacher_model_ckpt = {TeacherModel:
  qwen25coder-14b="/mnt/efs/augment/checkpoints/qwen25-coder/14b-instruct-fb-mp4"
  qwen25coder-32b="/mnt/efs/augment/checkpoints/qwen25-coder/32b-instruct-fb-mp8"
  qwen3-32b="/mnt/efs/augment/checkpoints/qwen3/Qwen3-32B-fbw-mp8"
}
teacher_model_mp_size = {TeacherModel:
  qwen25coder-14b=4
  qwen25coder-32b=8
  qwen3-32b=8
}
query_prompt_formatter = {QueryFormatter: chatanol6}
key_prompt_formatter = {KeyFormatter: chatanol6-embedding-with-path-key}

class cw_ray(cluster_name="ray", num_workers=32, gpu_count=1, augment=$):
  def run(internal_script):
    export PYTHONPATH=$augment
    /opt/conda/bin/python -m research.data.ray.gen_ray_cluster_spec \
      --name $cluster_name \
      --replicas $num_workers \
      --gpu-type H100_NVLINK_80GB \
      --gpu-count $gpu_count \
    > ray-cluster-spec.yaml
    kubectl apply -f ray-cluster-spec.yaml
    echo "Waiting for Ray cluster head node to be ready..."
    sleep 2
    kubectl wait --for=condition=Ready pods/$cluster_name-head-pod
    ray_head_ip=$(kubectl get raycluster $cluster_name -o jsonpath='{.status.head.podIP}')
    ray_port=$(kubectl get raycluster $cluster_name -o jsonpath='{.status.endpoints.dashboard}')
    echo "RAY_ADDRESS=http://$ray_head_ip:$ray_port"
    export RAY_ADDRESS="http://$ray_head_ip:$ray_port"
    . $internal_script
    kubectl delete raycluster $cluster_name


@cw_ray(cluster_name="ray-gen-examples", num_workers=64, gpu_count=1)
task generate_examples(
  augment=$,
  binks_data@gcs=$,
  scorer_tokenizer=$, chunker=$,
  scorer_type=$, scorer_ckpt=$
) -> out@gcs:
  python $augment/research/data/ray/submit_ray.py \
      --ray-address $RAY_ADDRESS \
      -- \
      python experimental/tongfei/chatanol/gen_stage.py \
        --input $binks_data \
        --output $out \
        --tokenizer $scorer_tokenizer \
        --scorer-type $scorer_type \
        --scorer-ckpt $scorer_ckpt \
        --num-workers $num_workers


@cw_ray(cluster_name="ray-teacher-perplexities", num_workers=32, gpu_count=$teacher_model_mp_size)
task get_teacher_perplexities(
  augment=$,
  in@gcs=$generate_examples.out,
  teacher_model_tokenizer=$, teacher_model_ckpt=$, teacher_model_mp_size=$
) -> out@gcs:
  python $augment/research/data/ray/submit_ray.py \
      --ray-address $RAY_ADDRESS \
      -- \
      python experimental/tongfei/chatanol/perplexity_stage.py \
        --input $in \
        --output $out \
        --tokenizer $teacher_model_tokenizer \
        --model-ckpt $teacher_model_ckpt \
        --model-mp-size $teacher_model_mp_size \
        --num-workers $num_workers \
        --gpus-per-worker $teacher_model_mp_size \
        --batch-size 4


@cw_ray(cluster_name="tokenize", num_workers=64, gpu_count=0)
task tokenize(
  augment=$,
  in@gcs=$get_teacher_perplexities.out,
  student_model_tokenizer=$, dtype=$,
  query_prompt_formatter=$, key_prompt_formatter=$
) -> out@gcs:
  python $augment/research/data/ray/submit_ray.py \
      --ray-address $RAY_ADDRESS \
      -- \
      python experimental/tongfei/chatanol/tokenize_stage.py \
        --input $in \
        --output $out \
        --tokenizer $student_model_tokenizer \
        --dtype $dtype \
        --query-prompt-formatter $query_prompt_formatter \
        --key-prompt-formatter $key_prompt_formatter \
        --num-workers $num_workers


task export(augment=$, in@gcs=$tokenize.out) -> out@gcs:
  python $augment/experimental/tongfei/chatanol/export_stage.py \
    --input $in \
    --output $out \
    --tokenizer qwen25coder \
    --dtype uint32




plan Export = {
  export[StudentModel:qwen25coder-1b5, TeacherModel:qwen25coder-14b, Binks:1]
  export[StudentModel:qwen25coder-1b5, TeacherModel:qwen25coder-14b, Binks:2]
  export[StudentModel:qwen25coder-1b5, TeacherModel:qwen25coder-14b, Binks:3]
  export[StudentModel:qwen25coder-1b5, TeacherModel:qwen25coder-14b, Binks:4]
  export[StudentModel:qwen25coder-1b5, TeacherModel:qwen25coder-14b, Binks:5]
}
