import argparse
import asyncio
import copy
import os
import subprocess
import time
import uuid
from typing import Any

import yaml
from termcolor import colored

from research.core.constants import AUGMENT_ROOT
from research.data.ray.ray_utils import _get_files_gcs
from research.data.ray.gen_ray_cluster_spec import create_pod_template_spec
from research.infra.cfg.clusters import Clusters


def run_command(cmd: list[str], cwd: str = AUGMENT_ROOT):
    print(f"Running command: {colored(' '.join(cmd), 'yellow')}")
    subprocess.run(
        cmd,
        check=True,
        text=True,
        cwd=cwd,
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", type=str, required=True)
    parser.add_argument("--output", type=str, required=True)
    parser.add_argument("--num-workers", type=str, required=True)
    parser.add_argument("--num-gpus-per-worker", type=int, required=True)
    parser.add_argument("--job-name", type=str, required=True)
    parser.add_argument("--limit", type=int, default=1048576)
    args, remaining_args = parser.parse_known_args()

    # After "--", all arguments are passed to the entrypoint
    if "--" in remaining_args:
        idx = remaining_args.index("--")
        remaining_args = remaining_args[idx + 1 :]
        remaining_args = [r if r.startswith("--") else f'"{r}"' for r in remaining_args]
        args.entrypoint = remaining_args
    else:
        raise ValueError("Entrypoint not specified")
    print(f"Entrypoint: {' '.join(args.entrypoint)}")

    os.chdir(AUGMENT_ROOT)  # Zip has to be run from the root directory
    # Zip all files in AUGMENT_ROOT to gs://{bucket}/ray/augment-$src_zip_id.zip
    # This is because our repo is too large to be directly uploadable to Ray

    zip_id = uuid.uuid4()
    zip_gcs_src_path = f"gs://gcp-us1-user/ray/augment-src-{zip_id}.zip"
    excluded_patterns = [
        ".git/*",
        "*.ipynb",
        ".idea/*",
        "bazel-augment/*",
        "bazel-bin/*",
        "bazel-out/*",
        "*.npy",
    ]
    excluded_pattern_cli_options = [
        t for pattern in excluded_patterns for t in ["-x", pattern]
    ]
    temp_zip_src_path = f"/tmp/augment-src-{zip_id}.zip"
    temp_env_yaml_path = f"/tmp/augment-env-{zip_id}.yml"

    try:
        run_command(
            [
                "gcloud",
                "storage",
                "cp",
                f"{AUGMENT_ROOT}/research/requirements.txt",
                f"gs://gcp-us1-user/ray/augment-env-{zip_id}.txt",
            ]
        )
        run_command(
            [
                "zip",
                "-r",
                "-y",  # Store symbolic links as links, do not follow them
                temp_zip_src_path,
                ".",
            ]
            + excluded_pattern_cli_options
        )
        run_command(
            [
                "gcloud",
                "storage",
                "cp",
                temp_zip_src_path,
                zip_gcs_src_path,
            ]
        )
    finally:
        # Clean up the temporary zip file
        if os.path.exists(temp_zip_src_path):
            try:
                os.remove(temp_zip_src_path)
                print(f"Cleaned up temporary file: {temp_zip_src_path}")
            except Exception as e:
                print(
                    f"Warning: Failed to clean up temporary file {temp_zip_src_path}: {e}"
                )

    # Inspect input directory
    print(f"Input: {args.input}")
    print(f"Output: {args.output}")

    files = _get_files_gcs(args.input)
    print(f"Found {len(files)} files to process.")

    cluster = Clusters.detect_cluster()
    print(f"Detected cluster: {cluster}")

    gpu_type = {
        "gcp-us1": "nvidia-h100-mega-80gb",
        "cw-east4": "H100_NVLINK_80GB",
    }[cluster]

    pod_spec = create_pod_template_spec(
        cluster=cluster,
        cluster_name=args.job_name,
        image=Clusters.load_current().images["devpod_gpu"],
        gpu_type=gpu_type,
        gpu_count=args.num_gpus_per_worker,
        is_head=False,
    )

    def generate_job_spec(
        pod_spec: dict[str, Any], worker_id: int, input_file: str
    ) -> dict[str, Any]:
        pod_spec_copy = copy.deepcopy(pod_spec)
        pod_spec_copy["spec"]["containers"][0]["name"] = f"worker-{worker_id}"
        pod_spec_copy["spec"]["containers"][0]["workingDir"] = "/home/<USER>/augment"
        activate_gcloud_command = "gcloud auth activate-service-account --key-file $GOOGLE_APPLICATION_CREDENTIALS"
        download_env_yaml_command = " ".join(
            [
                "gcloud",
                "storage",
                "cp",
                f"gs://gcp-us1-user/ray/augment-env-{zip_id}.txt",
                f"/tmp/augment-env-{zip_id}.txt",
            ]
        )
        install_env_command = " ".join(
            [
                "pip",
                "install",
                "-r",
                f"/tmp/augment-env-{zip_id}.txt",
            ]
        )
        download_src_command = " ".join(
            [
                "gcloud",
                "storage",
                "cp",
                zip_gcs_src_path,
                temp_zip_src_path,
            ]
        )
        unzip_src_command = " ".join(
            [
                "unzip",
                temp_zip_src_path,
                "-d",
                "/home/<USER>/augment",
            ]
        )
        download_data_command = " ".join(
            ["gcloud", "storage", "cp", input_file, "/home/<USER>/input.jsonl"]
        )
        upload_data_command = " ".join(
            [
                "gcloud",
                "storage",
                "cp",
                "/home/<USER>/output/input.jsonl",
                f"{args.output}/{input_file.split('/')[-1]}",
            ]
        )
        command = []
        for t in args.entrypoint:
            if args.input in t:
                command.append("/home/<USER>/input.jsonl")
            elif args.output in t:
                command.append("/home/<USER>/output")
            else:
                command.append(t)
        job_command = " ".join(command)
        final_command = [
            "/bin/bash",
            "-c",
            "\n".join(
                [
                    activate_gcloud_command,
                    download_env_yaml_command,
                    install_env_command,
                    download_src_command,
                    unzip_src_command,
                    download_data_command,
                    "mkdir -p /home/<USER>/output",
                    job_command,
                    upload_data_command,
                ]
            ),
        ]
        pod_spec_copy["spec"]["containers"][0]["command"] = final_command
        pod_spec_copy["spec"]["restartPolicy"] = "Never"
        pod_spec_copy["spec"]["containers"][0]["env"].append(
            {
                "name": "PYTHONPATH",
                "value": "/home/<USER>/augment:/home/<USER>/augment/research/gpt-neox",
            }
        )

        job_spec = {
            "apiVersion": "batch/v1",
            "kind": "Job",
            "metadata": {
                "name": f"{args.job_name}-worker-{worker_id}",
            },
            "spec": {
                "template": pod_spec_copy,
                "backoffLimit": 0,
            },
        }
        return job_spec

    for worker_id, input_file in enumerate(files[: args.limit]):
        job_spec = generate_job_spec(pod_spec, worker_id, input_file)
        print(f"Creating job for {input_file}...")
        with open(f"/tmp/job-{worker_id}.yaml", "w") as f:
            f.write(yaml.dump(job_spec))
        run_command(["kubectl", "apply", "-f", f"/tmp/job-{worker_id}.yaml"])
