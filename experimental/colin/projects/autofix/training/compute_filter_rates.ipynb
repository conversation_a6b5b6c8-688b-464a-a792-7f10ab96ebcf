{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.utils import k8s_session\n", "import pyspark.sql.functions as F\n", "from research.utils.repo_change_utils import get_repo_change_for_single_commit\n", "\n", "PR_V2_BASE = \"/mnt/efs/spark-data/shared/pr_v2\"\n", "\n", "TRAINING_DATA_BASE = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3\"\n", "FORMAT_TRAINING_DATA_BASE = f\"{TRAINING_DATA_BASE}/format_training_data\"\n", "\n", "# === Datasets for AutoFix\n", "# Table containing metadata of intermediate commits for PRs\n", "PR_INTER_COMMITS = f\"{PR_V2_BASE}/inter_commits\"\n", "# Table containing metadata of check runs for commits\n", "PR_CHECK_RUNS = f\"{PR_V2_BASE}/check_runs\"\n", "# Table containing logs of failed check runs for commits\n", "PR_FAILED_LOGS = f\"{PR_V2_BASE}/failed_logs\"\n", "# === End of AutoFix datasets\n", "\n", "\n", "def create_spark(max_workers: int = 5):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "spark = create_spark(5)\n", "\n", "commit_metadata_df = spark.read.parquet(f\"{TRAINING_DATA_BASE}/commit_metadata\")\n", "cicd_metadata_df = spark.read.parquet(f\"{TRAINING_DATA_BASE}/cicd_metadata\")\n", "failed_logs_df = spark.read.parquet(f\"{TRAINING_DATA_BASE}/failed_logs\")\n", "grouped_failed_logs_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/grouped_failed_logs\"\n", ")\n", "failed_commit_metadata_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/failed_commit_metadata\"\n", ")\n", "failed_commit_with_passing_child_metadata_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/failed_commit_with_passing_child_metadata\"\n", ")\n", "repo_grouped_failed_commit_metadata_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_metadata\"\n", ")\n", "repo_grouped_failed_commit_with_passing_child_metadata_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_metadata\"\n", ")\n", "\n", "#repo_grouped_failed_commit_with_passing_child_contents_df = spark.read.parquet(\n", "#    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits_attempt2_3\"\n", "#)\n", "repo_grouped_failed_commit_with_passing_child_contents_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits\"\n", ")\n", "\n", "# Formatting pipeline \n", "# stage2_with_logs_df = spark.read.parquet(f\"{FORMAT_TRAINING_DATA_BASE}/stage2_with_logs\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["num_failed_logs = failed_logs_df.count()\n", "num_commits_with_failed_logs =  grouped_failed_logs_df.count()\n", "print(\n", "    f\"There are {num_failed_logs} failed logs, and {num_commits_with_failed_logs} commits with failed logs\"\n", ")\n", "\n", "# There are 202109 failed logs, and 69011 commits with failed logs"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["num_failed_log_with_commit_metadata = failed_commit_metadata_df.count()\n", "print(\n", "    f\"There are {num_failed_log_with_commit_metadata} commits with failed logs and commit metadata\"\n", ")\n", "\n", "num_failed_log_with_commit_metadata_and_passing_child = (\n", "    failed_commit_with_passing_child_metadata_df.count()\n", ")\n", "\n", "print(\n", "    f\"There are {num_failed_log_with_commit_metadata_and_passing_child} commits with failed logs, commit metadata, and passing child\"\n", ")\n", "\n", "#There are 60090 commits with failed logs and commit metadata\n", "#There are 29074 commits with failed logs, commit metadata, and passing child"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["repo_grouped_failed_commit_with_passing_child_metadata_df = repo_grouped_failed_commit_with_passing_child_metadata_df.withColumn('num_commits', F.size('commits'))\n", "num_commits = repo_grouped_failed_commit_with_passing_child_metadata_df.select(F.sum('num_commits')).collect()[0][0]\n", "print(f\"After groupping commits by repo, there are {num_commits} commits with:\\n- failed logs \\n- commit metadata\\n- and passing child\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["repo_grouped_failed_commit_with_passing_child_contents_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits\"\n", ")\n", "\n", "repo_grouped_failed_commit_with_passing_child_contents_df.limit(5).toPandas()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.types import BooleanType\n", "\n", "# filter out commits we couldn't extract\n", "repo_grouped_failed_commit_with_passing_child_contents_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits\"\n", ")\n", "num_repos_pre_filter = repo_grouped_failed_commit_with_passing_child_contents_df.count()\n", "repo_grouped_failed_commit_with_passing_child_contents_df = repo_grouped_failed_commit_with_passing_child_contents_df.filter(F.col('num_commits') != -1)\n", "#repo_grouped_failed_commit_with_passing_child_contents_df = repo_grouped_failed_commit_with_passing_child_contents_df.withColumn('num_commits_method2', F.size('commits'))\n", "#repo_grouped_failed_commit_with_passing_child_contents_num_commits_method2 = repo_grouped_failed_commit_with_passing_child_contents_df.select(F.sum('num_commits_method2')).collect()[0][0]\n", "repo_grouped_failed_commit_with_passing_child_contents_num_commits = repo_grouped_failed_commit_with_passing_child_contents_df.select(F.sum('num_commits')).collect()[0][0]\n", "num_repos_post_filter = repo_grouped_failed_commit_with_passing_child_contents_df.count()\n", "print(f\"After grabbing commit contents, there are {repo_grouped_failed_commit_with_passing_child_contents_num_commits} commits. Thils filters {num_repos_pre_filter} repos to {num_repos_post_filter} repos\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["repo_grouped_failed_commit_with_passing_child_contents_df = spark.read.parquet(\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits\"\n", ")\n", "\n", "total_num_missing_commits = repo_grouped_failed_commit_with_passing_child_contents_df.select(F.sum('num_missing_commits')).collect()[0][0]\n", "total_num_duplicate_commits = repo_grouped_failed_commit_with_passing_child_contents_df.select(F.sum('num_duplicate_commits')).collect()[0][0]\n", "\n", "print(f\"There were {total_num_missing_commits} missing commits and {total_num_duplicate_commits} duplicate commits\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["repo_grouped_failed_commit_with_passing_child_contents_df.count()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["TRAINING_DATA_BASE = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3\"\n", "\n", "repo_grouped_failed_commit_with_passing_child_metadata_path = (\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_metadata\"\n", ")\n", "repo_grouped_failed_commit_with_passing_child_metadata_df = spark.read.parquet(\n", "    repo_grouped_failed_commit_with_passing_child_metadata_path\n", ")\n", "repo_grouped_failed_commit_with_passing_child_metadata_df.count()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from pyspark.sql.types import IntegerType\n", "\n", "\n", "stage2_with_logs_df = spark.read.parquet(f\"{FORMAT_TRAINING_DATA_BASE}/stage2_with_logs\")\n", "\n", "def count_commits(logs_pkl):\n", "    logs = pickle.loads(logs_pkl)\n", "    return len([log for log in logs if log is not None])\n", "count_commits_udf = F.udf(count_commits, returnType=IntegerType())\n", "\n", "stage2_with_logs_df = stage2_with_logs_df.withColumn('num_commits', count_commits_udf(F.col('logs')))\n", "num_commits_post_filter = stage2_with_logs_df.select(F.sum('num_commits')).collect()[0][0]\n", "print(f\"After prepping logs, there are {num_commits_post_filter} commits\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "from research.core.types import Document\n", "from research.retrieval.scorers.recency_scorer import (\n", "    get_diff_line_ranges,\n", "    label_overlapping_chunks,\n", ")\n", "from research.utils import repo_change_utils\n", "from research.utils.repo_change_utils import RepoChange\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from pyspark.sql.types import IntegerType\n", "\n", "chunker = LineLevelChunker(max_lines_per_chunk=30)\n", "boundary_lines = 0\n", "\n", "def count_non_empty_fixing_changes_method1(fixing_changes_raw):\n", "    fixing_changes: list[RepoChange] = pickle.loads(fixing_changes_raw)\n", "    ct = 0\n", "    for fixing_change in fixing_changes:\n", "        if fixing_change.changed_files:\n", "            ct += 1\n", "    return ct\n", "\n", "def count_non_empty_fixing_changes_method2(fixing_changes_raw):\n", "    fixing_changes: list[RepoChange] = pickle.loads(fixing_changes_raw)\n", "    ct = 0\n", "    for fixing_change in fixing_changes:\n", "        try:\n", "            git_diff = repo_change_utils.patchset_from_repo_change(\n", "                fixing_change,\n", "                num_context_lines=0,\n", "                ignore_whitespace=True,\n", "            )\n", "            wip_files = fixing_change.after_files\n", "            wip_docs = [\n", "                Document.new(contents, path) for path, contents in wip_files.items()\n", "            ]\n", "            wip_chunks = [\n", "                chunk for doc in wip_docs for chunk in chunker.split_into_chunks(doc)\n", "            ]\n", "\n", "            diff_line_ranges = get_diff_line_ranges(\n", "                git_diff, source_range=True\n", "            )\n", "            chunk_labels = label_overlapping_chunks(\n", "                wip_chunks,\n", "                diff_line_ranges,\n", "                ignore_boundary_lines=boundary_lines,\n", "            )\n", "\n", "            positive_chunks = [\n", "                chunk for chunk, label in zip(wip_chunks, chunk_labels) if label\n", "            ]\n", "\n", "            if len(positive_chunks) > 0:\n", "                ct += 1\n", "        except AssertionError:\n", "            continue\n", "\n", "    return ct\n", "\n", "#repo_grouped_failed_commit_with_passing_child_contents_df = spark.read.parquet(\n", "#    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits_attempt2_3\"\n", "#)\n", "stage2_with_logs_df = spark.read.parquet(f\"{FORMAT_TRAINING_DATA_BASE}/stage2_with_logs\")\n", "\n", "count_non_empty_fixing_changes_method1_udf = F.udf(count_non_empty_fixing_changes_method1, returnType=IntegerType())\n", "count_non_empty_fixing_changes_method2_udf = F.udf(count_non_empty_fixing_changes_method2, returnType=IntegerType())\n", "\n", "stage2_with_logs_child_nonempty_df = stage2_with_logs_df.withColumn('num_fixing_changes_method1', count_non_empty_fixing_changes_method1_udf(F.col('fixing_changes')))\n", "stage2_with_logs_child_nonempty_df = stage2_with_logs_child_nonempty_df.withColumn('num_fixing_changes_method2', count_non_empty_fixing_changes_method2_udf(F.col('fixing_changes')))\n", "\n", "# sum over num_fixing_changes_method1\n", "num_fixing_changes_method1 = stage2_with_logs_child_nonempty_df.select(F.sum('num_fixing_changes_method1')).collect()\n", "#num_fixing_changes_method2 = stage2_with_logs_child_nonempty_df.select(F.sum('num_fixing_changes_method2')).collect()\n", "\n", "print(\n", "    f\"There are {num_fixing_changes_method1[0][0]} fixing changes with non-empty changes in method 1\"\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["print(\n", "    f\"There are {num_fixing_changes_method1[0][0]} fixing changes with non-empty changes in method 1\"\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.types import StructField, StructType, BinaryType, IntegerType\n", "import pickle\n", "\n", "def get_empty_fixing_changes(commits, fixing_changes_raw):\n", "    fixing_changes: list[RepoChange] = pickle.loads(fixing_changes_raw)\n", "    \n", "    empty_commits = []\n", "    empty_fixing_changes = []\n", "    for commit, fixing_change in zip(commits, fixing_changes):\n", "        if not fixing_change.changed_files:\n", "            empty_commits.append(commit)\n", "            empty_fixing_changes.append(fixing_change)\n", "\n", "    num_empty_commits = len(empty_commits)\n", "    return num_empty_commits, pickle.dumps((empty_commits, empty_fixing_changes))\n", "\n", "get_empty_fixing_changes_udf = F.udf(get_empty_fixing_changes, returnType=F.StructType([\n", "    StructField(\"num_empty_commits\", IntegerType()), \n", "    StructField(\"empty_commits\", BinaryType())\n", "]))\n", "\n", "stage2_with_logs_df = spark.read.parquet(f\"{FORMAT_TRAINING_DATA_BASE}/stage2_with_logs_3\")\n", "empty_commits_check_df = stage2_with_logs_df.withColumn('tmp', get_empty_fixing_changes_udf(F.col('commits'), <PERSON>.col('fixing_changes')))\n", "empty_commits_check_df = empty_commits_check_df.select(\n", "    empty_commits_check_df[\"*\"],  # Select all existing columns\n", "    empty_commits_check_df[\"tmp.num_empty_commits\"].alias(\"num_empty_commits\"),\n", "    empty_commits_check_df[\"tmp.empty_commits\"].alias(\"empty_commits\"),\n", ")\n", "empty_commits_check_df = empty_commits_check_df.drop('tmp')\n", "\n", "empty_commits_check_examples = empty_commits_check_df.limit(50).toPandas()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# ict_keys(['all_logs', 'base_ref', 'child_sha', 'committed_at', 'details_url', 'head_ref', 'merged_at', 'message', 'name', 'num_logs', 'owner', 'parent', 'pr_number', 'repo_ID', 'sha', 'title', 'workflow_run_url'])\n", "for j in range(len(empty_commits_check_examples)):\n", "    tmp = pickle.loads(empty_commits_check_examples.iloc[j]['empty_commits'])\n", "    commits = tmp[0]\n", "    for i in range(len(commits)):\n", "        print('child_sha: ', commits[i]['child_sha'])\n", "        print('Workflow run url: ', commits[i]['workflow_run_url'])\n", "        print('Details url: ', commits[i]['details_url'])\n", "        print('-----')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Scratch"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_with_populated_commits_TEST')\n", "\n", "# sum over total_commits_considered\n", "total_commits_considered = df.select(F.sum('total_commits_considered')).collect()\n", "\n", "print(\n", "    f\"There are {total_commits_considered[0][0]} commits considered\"\n", ")\n", "\n", "# sum over total_commits_filtered_due_to_exception\n", "total_commits_filtered_due_to_exception = df.select(F.sum('total_commits_filtered_due_to_exception')).collect()\n", "\n", "print(\n", "    f\"There are {total_commits_filtered_due_to_exception[0][0]} commits filtered due to exception\"\n", ")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Things we know:\n", "- We have no trouble unpacking tarballs. We only lose 30 commits out of 30_000.\n", "\n", "In repo_grouped_failed_commit_with_passing_child_with_populated_commits_TEST2, I expect \n", "- shas_processed to equal 30_000\n", "- shas_processed = num_missing_commits + num_duplicate_commits + num_missing_commits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_with_populated_commits/*.parquet')\n", "\n", "# sum over num_commits\n", "num_commits = df.select(F.sum('num_commits')).collect()[0][0]\n", "num_missing_commits = df.select(F.sum('num_missing_commits')).collect()[0][0]\n", "num_duplicate_commits = df.select(F.sum('num_duplicate_commits')).collect()[0][0]\n", "num_total_commits = num_commits + num_missing_commits + num_duplicate_commits\n", "\n", "print(\n", "    f\"There are {num_total_commits} commits\"\n", ")\n", "\n", "# I'M EXPECTED 30_000 COMMITS HERE BUT GOT NOTHING\n", "\n", "# There are 13744 commits"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["base = '/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata'\n", "df_with_large_output_size = spark.read.parquet(f'{base}/part-00475-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet')\n", "\n", "# add num_commits column\n", "df_with_large_output_size = df_with_large_output_size.withColumn('num_commits', F.size('commits'))\n", "num_commits = df_with_large_output_size.select(F.sum('num_commits')).collect()[0][0]\n", "\n", "print(\n", "    f\"There are {num_commits} commits in /mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00475-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\"\n", ")\n", "\n", "df_with_small_output_size = spark.read.parquet(f'{base}/part-00965-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet')\n", "df_with_small_output_size = df_with_small_output_size.withColumn('num_commits', F.size('commits'))\n", "num_commits = df_with_small_output_size.select(F.sum('num_commits')).collect()[0][0]\n", "\n", "print(\n", "    f\"There are {num_commits} commits in /mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00965-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\"\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_with_populated_commits/')\n", "df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "\n", "\n", "for path in glob('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_with_populated_commits/*.parquet'):\n", "    df = spark.read.parquet(path)\n", "    num_commits = df.select(F.sum('num_commits')).collect()[0][0]\n", "    print(f\"There are {num_commits} commits\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}