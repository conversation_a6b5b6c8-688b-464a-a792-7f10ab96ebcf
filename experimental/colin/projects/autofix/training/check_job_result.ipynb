{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting KUBECONFIG to /home/<USER>/.kube/config\n", "Skipping bazel build.\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "24/09/25 20:08:45 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n"]}], "source": ["from research.data.spark.utils import k8s_session\n", "import pyspark.sql.functions as F\n", "from pyspark.sql.types import IntegerType, StringType, StructField, StructType\n", "\n", "\n", "def create_spark(max_workers: int = 5):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "            \"spark.executor.memory\": \"20g\",\n", "            \"spark.executor.pyspark.memory\": \"30G\",\n", "            \"spark.driver.maxResultSize\": \"8gb\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "spark = create_spark()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> commits job"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Num input repos: 2808\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Num output repos: 2573\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Num input commits: 28957\n", "Num output commits: 17631\n", "Percentage of commits we successfully grabbed commit contents for: 0.6088683219946818\n", "Num unsuccessful output repos: 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 17:=====================================================>(572 + 7) / 579]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of fixing diffs: 12344\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["import pickle\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "from research.utils.repo_change_utils import RepoChange\n", "\n", "\n", "TRAINING_DATA_BASE = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v2\"\n", "\n", "repo_grouped_failed_commit_with_passing_child_metadata_path = (\n", "    f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_metadata\"\n", ")\n", "\n", "output_path = f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits_attempt2\"\n", "\n", "input_df = spark.read.parquet(\n", "    repo_grouped_failed_commit_with_passing_child_metadata_path\n", ")\n", "# add num commits column to input_df\n", "input_df = input_df.withColumn(\"num_commits\", F.size(F.col(\"commits\")))\n", "output_df = spark.read.parquet(output_path)\n", "\n", "print(f\"Num input repos: {input_df.count()}\")\n", "print(f\"Num output repos: {output_df.count()}\")\n", "\n", "# sum over num_commits in input_df and output_df and save it\n", "input_num_commits = input_df.select(F.sum(\"num_commits\")).collect()[0][0]\n", "output_df_successful = output_df.filter(F.col(\"num_commits\") != -1)\n", "output_num_commits = output_df_successful.select(F.sum(\"num_commits\")).collect()[0][0]\n", "print(f\"Num input commits: {input_num_commits}\")\n", "print(f\"Num output commits: {output_num_commits}\")\n", "perc_success = output_num_commits / input_num_commits\n", "print(\n", "    f\"Percentage of commits we successfully grabbed commit contents for: {perc_success}\"\n", ")\n", "\n", "output_df_unsuccessful = output_df.filter(F.col(\"num_commits\") == -1)\n", "print(f\"Num unsuccessful output repos: {output_df_unsuccessful.count()}\")\n", "\n", "# Check how many\n", "\n", "\n", "def serialize_a_diff(repo_change: RepoChange) -> str:\n", "    \"\"\"Serialize a diff to a string\n", "\n", "    Example output:\n", "    +++ experimental/colin/projects/code_execution_feedback_retriever/test_fileA.py\n", "    @@ -1,3 +1,5 @@\n", "    # This is a test file A.\n", "\n", "    +# This is a change for file A.\n", "    +\n", "    # End of test file A.\n", "    \\ No newline at end of file\n", "\n", "    +++ experimental/colin/projects/code_execution_feedback_retriever/test_fileB.py\n", "    @@ -1,3 +1,5 @@\n", "    # This is a test file B.\n", "\n", "    +# This is a change for file B.\n", "    +\n", "    # End of test file B.\n", "    \\ No newline at end of file\n", "    \"\"\"\n", "    diff = \"\"\n", "    for item in format_file_changes_with_ranges(\n", "        [c.map(lambda x: x.to_file()) for c in list(repo_change.changed_files)],\n", "        diff_context_lines=3,\n", "    ):\n", "        path = item.after_path\n", "        diff += \"-\" * 10 + \"\\n\"\n", "        diff += f\"Path: {path}\" + \"\\n\"\n", "        diff += item.text\n", "    return diff\n", "\n", "\n", "def count_rows_with_nonempty_fixing_diff(fixing_changes) -> int:\n", "    ct = 0\n", "    for fixing_change in pickle.loads(fixing_changes):\n", "        if serialize_a_diff(fixing_change) != \"\":\n", "            ct += 1\n", "    return ct\n", "\n", "\n", "count_rows_with_nonempty_fixing_diff_udf = F.udf(\n", "    count_rows_with_nonempty_fixing_diff, IntegerType()\n", ")\n", "\n", "# apply count_rows_with_nonempty_fixing_diff_udf to fixing_changes column in output_df, and then sum over the column\n", "tmp = output_df.withColumn(\n", "    \"num_fixing_diffs\",\n", "    count_rows_with_nonempty_fixing_diff_udf(<PERSON>.col(\"fixing_changes\")),\n", ")\n", "total_num_fixing_diffs = tmp.select(F.sum(\"num_fixing_diffs\")).collect()[0][0]\n", "print(f\"Total number of fixing diffs: {total_num_fixing_diffs}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}