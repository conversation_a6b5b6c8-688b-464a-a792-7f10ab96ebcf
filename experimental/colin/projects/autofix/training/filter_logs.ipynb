{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "from research.data.spark.utils import k8s_session\n", "import pyspark.sql.functions as F\n", "from research.utils.repo_change_utils import (\n", "    RepoChange,\n", ")\n", "from base.third_party_clients.vertexai_client import VertexAiClient\n", "import numpy as np\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from tqdm import tqdm\n", "import random\n", "\n", "random.seed(1)\n", "\n", "TRAINING_DATA_BASE = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v2\"\n", "\n", "\n", "def create_spark(max_workers: int = 5):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "            \"spark.executor.memory\": \"20g\",\n", "            \"spark.executor.pyspark.memory\": \"30G\",\n", "            \"spark.driver.maxResultSize\": \"8gb\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "spark = create_spark()\n", "\n", "\n", "failed_commit_with_passing_child_metadata_path = f\"{TRAINING_DATA_BASE}/repo_grouped_failed_commit_with_passing_child_with_populated_commits\"\n", "failed_commit_with_passing_child_metadata_df = spark.read.parquet(\n", "    failed_commit_with_passing_child_metadata_path\n", ")\n", "\n", "MAX_TOKS = 16_000\n", "tokenizer = StarCoder2Tokenizer()\n", "max_log_runs_to_search_per_commit = 5\n", "\n", "gemini_client = VertexAiClient(\n", "    \"system-services-dev\",\n", "    \"us-central1\",\n", "    \"gemini-1.5-flash-001\",\n", "    0.0,\n", "    1000,\n", ")\n", "\n", "system_prompt = \"\"\"\n", "You are an expert about software engineering tasked with helping software engineers with their work.\n", "You will provide helpful and thoughtful responses to any requests.\n", "\"\"\"\n", "\n", "\n", "def create_task_prompt(snippet: str, current_diff: str, child_diff: str) -> str:\n", "    return f\"\"\"\n", "Here is a snippet from the CI/CD logs for a commit in a code respository.\n", "\n", "```\n", "{snippet}\n", "```\n", "\n", "Here is a diff from the commit corresponding to the CI/CD logs:\n", "\n", "```\n", "{current_diff}\n", "```\n", "\n", "Here is a diff from the subsequent commit:\n", "\n", "```\n", "{child_diff}\n", "```\n", " \n", "If the logs mention a test failure in them,\n", "then this diff would contain the changes that fixed the test failure because the CI/CD passed for the subsequent commit.\n", "\n", "Is there a stacktrace coresponding to a test failure present in the CI/CD logs?\n", "Please answer with a single word answer of 'yes' or 'no'. Do not include any other text in your response.\n", "\"\"\"\n", "\n", "\n", "def get_gemini_response(\n", "    logs: str,\n", "    fixing_diff: str,\n", "    child_diff: str,\n", "    all_prompts: list[str],\n", "    all_responses: list[str],\n", ") -> str:\n", "    prompt = create_task_prompt(logs, fixing_diff, child_diff)\n", "    all_prompts.append(prompt)\n", "    gemini_response_raw = gemini_client.generate_response_stream(\n", "        messages=[],\n", "        system_prompt=system_prompt,\n", "        cur_message=prompt,\n", "    )\n", "    try:\n", "        gemini_response = \"\"\n", "    except Exception as e:\n", "        print(f\"Error: {e}\")\n", "        gemini_response = \"no\"\n", "\n", "    for item in gemini_response_raw:\n", "        gemini_response += item.text\n", "\n", "    assert gemini_response.strip() in [\"yes\", \"no\"], gemini_response\n", "    all_responses.append(gemini_response)\n", "    return gemini_response.strip()\n", "\n", "\n", "def is_error_logs(resp: str) -> bool:\n", "    assert resp.strip() in [\"yes\", \"no\"], resp\n", "    return resp.strip() == \"yes\"\n", "\n", "\n", "def get_gemini_response_and_check_is_error_logs(\n", "    logs: str,\n", "    fixing_diff: str,\n", "    child_diff: str,\n", "    all_prompts: list[str],\n", "    all_responses: list[str],\n", ") -> bool:\n", "    resp = get_gemini_response(\n", "        logs, fixing_diff, child_diff, all_prompts, all_responses\n", "    )\n", "    return is_error_logs(resp)\n", "\n", "\n", "@dataclass\n", "class ParsedLogsChunk:\n", "    text: str\n", "    \"\"\"The text of the parsed log\"\"\"\n", "\n", "    index: int\n", "    \"\"\"Index in raw logs that this chunk started at\"\"\"\n", "\n", "\n", "def serialize_a_diff(repo_change: RepoChange) -> str:\n", "    \"\"\"Serialize a diff to a string\n", "\n", "    Example output:\n", "    +++ experimental/colin/projects/code_execution_feedback_retriever/test_fileA.py\n", "    @@ -1,3 +1,5 @@\n", "    # This is a test file A.\n", "\n", "    +# This is a change for file A.\n", "    +\n", "    # End of test file A.\n", "    \\ No newline at end of file\n", "\n", "    +++ experimental/colin/projects/code_execution_feedback_retriever/test_fileB.py\n", "    @@ -1,3 +1,5 @@\n", "    # This is a test file B.\n", "\n", "    +# This is a change for file B.\n", "    +\n", "    # End of test file B.\n", "    \\ No newline at end of file\n", "    \"\"\"\n", "    diff = \"\"\n", "    for item in format_file_changes_with_ranges(\n", "        [c.map(lambda x: x.to_file()) for c in list(repo_change.changed_files)],\n", "        diff_context_lines=3,\n", "    ):\n", "        path = item.after_path\n", "        diff += \"-\" * 10 + \"\\n\"\n", "        diff += f\"Path: {path}\" + \"\\n\"\n", "        diff += item.text\n", "    return diff\n", "\n", "\n", "all_rows = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    failed_commit_with_passing_child_metadata_df.limit(5).show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Compute some statistics on the logs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if False:\n", "    print(\n", "        \"The total number of commit with logs where the following commit passed tests: \",\n", "        failed_commit_with_passing_child_metadata_df.count(),\n", "    )\n", "\n", "    tmp = (\n", "        failed_commit_with_passing_child_metadata_df.select(\"num_commits\")\n", "        .limit(1000)\n", "        .collect()\n", "    )\n", "    print(\n", "        \"The median number of check runs per commit: \",\n", "        np.median([int(tmp[i][\"num_commits\"]) for i in range(len(tmp))]),\n", "    )\n", "\n", "    df = failed_commit_with_passing_child_metadata_df.limit(1000).collect()\n", "    lengths_in_toks = [\n", "        len(tokenizer.tokenize_safe(random.sample(commit[\"all_logs\"], 1)[0][\"log\"]))\n", "        for row in df\n", "        for commit in row[\"commits\"]\n", "    ]\n", "    print(\n", "        \"The median number of tokens in a log file: \",\n", "        np.median(lengths_in_toks),\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Explore Gemini based filtering and keyword based filtering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "if all_rows is None:\n", "    all_rows = failed_commit_with_passing_child_metadata_df.limit(100).collect()\n", "\n", "# [(repo_ID, sha, truncated logs)]\n", "parsed_logs_objs: list[tuple[str, str, list[ParsedLogsChunk]]] = []\n", "all_prompts: list[str] = []\n", "all_responses: list[str] = []\n", "error_indices = []\n", "\n", "filtering_technique = \"llm_single_chunk\"\n", "# filtering_technique = \"llm\"\n", "# filtering_technique = \"keyword\"\n", "\n", "# Iterate through logs for each check run for the commit, looking for one with errors in it.\n", "for row in tqdm(all_rows[:5], desc=\"iterate on repos\"):\n", "    repo_ID = row[\"repo_id\"]\n", "\n", "    commits = row[\"commits\"]\n", "    fixing_changes = pickle.loads(row[\"fixing_changes\"])\n", "    breaking_changes = pickle.loads(row[\"breaking_changes\"])\n", "\n", "    for commit, fixing_change, breaking_change in tqdm(\n", "        list(zip(commits, fixing_changes, breaking_changes))[:5],\n", "        desc=f\"iterate on commits for {repo_ID}\",\n", "    ):\n", "        sha = commit[\"sha\"]\n", "\n", "        found_error_logs = False\n", "        parsed_logs: list[str] = []\n", "\n", "        # for reproducibility\n", "        random.seed(1)\n", "        candidate_logs = random.sample(\n", "            commit[\"all_logs\"],\n", "            min(len(commit[\"all_logs\"]), max_log_runs_to_search_per_commit),\n", "        )\n", "        num_log_files_searched = 0\n", "\n", "        log_idx = 0\n", "        for log_idx, logs in enumerate(candidate_logs):\n", "            num_log_files_searched += 1\n", "            # Choose your filtering technique to parse 'logs'\n", "            if filtering_technique == \"llm_single_chunk\":\n", "                # APPROACH 1: <PERSON>rab last 16_000 tokens and pass that to Gemini as one big chunk\n", "                fixing_diff = serialize_a_diff(fixing_change)\n", "                breaking_diff = serialize_a_diff(breaking_change)\n", "\n", "                if fixing_diff == \"\" or breaking_diff == \"\":\n", "                    continue\n", "\n", "                logs = tokenizer.detokenize(\n", "                    tokenizer.tokenize_safe(logs[\"log\"])[-MAX_TOKS:]\n", "                )\n", "                if get_gemini_response_and_check_is_error_logs(\n", "                    logs, fixing_diff, breaking_diff, all_prompts, all_responses\n", "                ):\n", "                    # If the log contains errors, early exit\n", "                    parsed_logs = [ParsedLogsChunk(text=logs, index=0)]\n", "                    error_indices.append(len(all_prompts) - 1)\n", "                    found_error_logs = True\n", "                    break\n", "            elif filtering_technique == \"llm\":\n", "                # APPROACH 2: Break up logs into chunks of 30 lines and pass each of the last 10 chunks\n", "                logs = logs[\"log\"]\n", "                # break up logs into 30 line chunks\n", "                logs_lines = logs.splitlines(keepends=True)\n", "                logs_chunks = [\n", "                    \"\".join(logs_lines[i : i + 30])\n", "                    for i in range(0, len(logs_lines), 30)\n", "                ]\n", "                chunk_indices = [0]\n", "                for chunk in logs_chunks[:-1]:\n", "                    chunk_indices.append(chunk_indices[-1] + len(chunk))\n", "\n", "                fixing_diff = serialize_a_diff(fixing_change)\n", "                breaking_diff = serialize_a_diff(breaking_change)\n", "\n", "                if fixing_diff == \"\" or breaking_diff == \"\":\n", "                    continue\n", "\n", "                for chunk_idx, chunk in list(zip(chunk_indices, logs_chunks))[-10:]:\n", "                    if get_gemini_response_and_check_is_error_logs(\n", "                        chunk, fixing_diff, breaking_diff, all_prompts, all_responses\n", "                    ):\n", "                        parsed_logs.append(ParsedLogsChunk(text=chunk, index=chunk_idx))\n", "                        error_indices.append(len(all_prompts) - 1)\n", "\n", "                # If the log contains errors, early exit\n", "                if found_error_logs:\n", "                    break\n", "\n", "            elif filtering_technique == \"keyword\":\n", "                logs = tokenizer.detokenize(\n", "                    tokenizer.tokenize_safe(logs[\"log\"])[-MAX_TOKS:]\n", "                )\n", "                if \"error\" in logs.lower():\n", "                    parsed_logs = [ParsedLogsChunk(text=logs, index=0)]\n", "                    found_error_logs = True\n", "                    break\n", "            else:\n", "                raise ValueError(f\"Unknown filtering technique: {filtering_technique}\")\n", "\n", "            # If the log does not contain errors, do nothing and move to the next log\n", "\n", "        if found_error_logs:\n", "            parsed_logs_objs.append((repo_ID, sha, parsed_logs))\n", "        else:\n", "            parsed_logs_objs.append(None)\n", "\n", "prompts_with_errors = [all_prompts[i] for i in error_indices]\n", "responses_with_errors = [all_responses[i] for i in error_indices]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_none = sum(map(lambda x: x is None, parsed_logs_objs))\n", "perc_non = num_none / len(parsed_logs_objs)\n", "print(f\"Percentage of commits that did not have any error logs: {perc_non}\")\n", "\n", "num_found = len(parsed_logs_objs) - num_none\n", "print(\"Total number of commits with error logs found: \", num_found)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Here is an example prompt:\")\n", "print(random.sample(all_prompts, 1)[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Here is an example prompt with errors:\")\n", "print(random.sample(prompts_with_errors, 1)[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["idx = 0\n", "non_null_parsed_logs_objs = list(filter(lambda x: x is not None, parsed_logs_objs))\n", "repo_ID, sha, parsed_logs = non_null_parsed_logs_objs[idx]\n", "\n", "print(f\"Looking at commit {sha} in repo {repo_ID}.\")\n", "print(\"Here are the error logs we found:\")\n", "\n", "for parsed_logs_chunk in parsed_logs:\n", "    print(f\"At index {parsed_logs_chunk.index}:\")\n", "    print(parsed_logs_chunk.text)\n", "    print(f\"Ends at index {parsed_logs_chunk.index + len(parsed_logs_chunk.text)}\")\n", "    print(\"-\" * 80)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}