{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting KUBECONFIG to /home/<USER>/.kube/config\n", "Skipping bazel build.\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "24/11/01 23:23:59 WARN Utils: Service 'SparkUI' could not bind on port 4040. Attempting port 4041.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13/stage0_grouped_commit_full_metadata\n", "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13/stage1_generate_problems\n", "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13/stage2_with_hydrated_logs\n", "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13/stage3_with_exploded_problems\n", "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13/stage4_with_hydrated_commits\n"]}], "source": ["from research.data.spark.utils import k8s_session\n", "import pyspark.sql.functions as F\n", "from research.utils.repo_change_utils import get_repo_change_for_single_commit\n", "from compress_pickle import dumps as compressed_dumps\n", "import compress_pickle\n", "from compress_pickle import loads as compressed_loads\n", "from pyspark.sql.types import IntegerType, FloatType, DoubleType, StringType, ArrayType, BinaryType\n", "from pyspark.sql.functions import explode\n", "from dataclasses import dataclass\n", "import pandas as pd\n", "from compress_pickle import load as compress_load\n", "from compress_pickle import loads as compressed_loads\n", "from experimental.colin.projects.autofix.training.utils import AutofixProblem\n", "from research.utils.repo_change_utils import CommitMeta\n", "import random\n", "from pyspark.sql.types import StructType\n", "from pyspark.sql.window import Window\n", "from pyspark.sql.functions import row_number, col\n", "\n", "\n", "PR_V2_BASE = \"/mnt/efs/spark-data/shared/pr_v2\"\n", "\n", "#TRAINING_DATA_BASE = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v13\"\n", "TRAINING_DATA_BASE = \"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13\"\n", "\n", "# === Datasets for AutoFix\n", "# Table containing metadata of intermediate commits for PRs\n", "PR_INTER_COMMITS = f\"{PR_V2_BASE}/inter_commits\"\n", "# Table containing metadata of check runs for commits\n", "PR_CHECK_RUNS = f\"{PR_V2_BASE}/check_runs\"\n", "# Table containing logs of failed check runs for commits\n", "PR_FAILED_LOGS = f\"{PR_V2_BASE}/failed_logs\"\n", "# === End of AutoFix datasets\n", "\n", "\n", "def create_spark(max_workers: int = 1000):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.driver.maxResultSize\": \"8g\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "            # add lots of memory\n", "            #\"spark.executor.pyspark.memory\": \"1050G\",\n", "            #\"spark.executor.memory\": \"120G\",\n", "         \n", "        },\n", "        ephemeral_storage_gb=128,\n", "    )\n", "    return spark\n", "\n", "\n", "spark = create_spark()\n", "\n", "\n", "STAGE0_PATH = f\"{TRAINING_DATA_BASE}/stage0_grouped_commit_full_metadata\"\n", "STAGE1_PATH = f\"{TRAINING_DATA_BASE}/stage1_generate_problems\"\n", "STAGE2_PATH = f\"{TRAINING_DATA_BASE}/stage2_with_hydrated_logs\"\n", "STAGE3_PATH = f\"{TRAINING_DATA_BASE}/stage3_with_exploded_problems\"\n", "STAGE4_PATH = f\"{TRAINING_DATA_BASE}/stage4_with_hydrated_commits\"\n", "\n", "print(STAGE0_PATH)\n", "print(STAGE1_PATH)\n", "print(STAGE2_PATH)\n", "print(STAGE3_PATH)\n", "print(STAGE4_PATH)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 0: Grouped commits"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["import random\n", "from pyspark.sql.types import StructType\n", "from pyspark.sql.window import Window\n", "from pyspark.sql.functions import row_number, col\n", "\n", "\n", "commit_metadata_df = spark.read.parquet(PR_INTER_COMMITS)\n", "commit_metadata_df = commit_metadata_df.withColumn(\n", "    \"repo_ID\", <PERSON><PERSON>concat(<PERSON><PERSON>col(\"owner\"), <PERSON><PERSON>lit(\"/\"), <PERSON><PERSON>col(\"name\"))\n", ")\n", "commit_metadata_df = commit_metadata_df.filter(F.size(F.col(\"parents\")) == 1)\n", "commit_metadata_df = commit_metadata_df.withColumn(\"parents\", <PERSON><PERSON>col(\"parents\")[0])\n", "commit_metadata_df = commit_metadata_df.withColumnRenamed(\"parents\", \"parent\")\n", "commit_metadata_df = commit_metadata_df.dropDuplicates([\"repo_ID\", \"sha\"])\n", "\n", "cicd_metadata_df = spark.read.parquet(PR_CHECK_RUNS)\n", "cicd_metadata_df = cicd_metadata_df.withColumn(\n", "    \"repo_ID\", <PERSON><PERSON>concat(<PERSON><PERSON>col(\"owner\"), <PERSON><PERSON>lit(\"/\"), <PERSON><PERSON>col(\"name\"))\n", ")\n", "cicd_metadata_df = cicd_metadata_df.select([col for col in cicd_metadata_df.columns if col not in ['owner', 'name', 'pr_number']])\n", "cicd_metadata_df = cicd_metadata_df.withColumnRenamed(\"commit_sha\", \"sha\")\n", "cicd_metadata_df = cicd_metadata_df.groupBy(\"repo_ID\", \"sha\").agg(\n", "    F.collect_list(F.struct(\"conclusion\", 'check_run_name')).alias(\"conclusion_list\")\n", ")\n", "cicd_metadata_df = cicd_metadata_df.dropDuplicates([\"repo_ID\", \"sha\"])\n", "\n", "failed_logs_df = spark.read.parquet(PR_FAILED_LOGS)\n", "failed_logs_df = failed_logs_df.filter(F.col(\"status_code\").isin([200, 206]))\n", "failed_logs_df = failed_logs_df.withColumn(\n", "    \"repo_ID\", <PERSON><PERSON>concat(<PERSON><PERSON>col(\"owner\"), <PERSON><PERSON>lit(\"/\"), <PERSON><PERSON>col(\"name\"))\n", ")\n", "failed_logs_df = failed_logs_df.select([col for col in failed_logs_df.columns if col not in ['owner', 'name', 'pr_number']])\n", "failed_logs_df = failed_logs_df.withColumnRenamed(\"commit_sha\", \"sha\")\n", "\n", "# update log column to empty string\n", "failed_logs_df = failed_logs_df.withColumn(\"log\", F.lit(\"\"))\n", "\n", "grouped_failed_logs_df = failed_logs_df.groupBy(\"repo_ID\", \"sha\").agg(\n", "    F.collect_list(F.struct(failed_logs_df.columns)).alias(\"all_logs\")\n", ")\n", "\n", "commit_full_metadata_df = commit_metadata_df.join(\n", "    cicd_metadata_df, on=[\"repo_ID\", \"sha\"], how=\"outer\"\n", ")\n", "commit_full_metadata_df = commit_full_metadata_df.join(\n", "    grouped_failed_logs_df, on=[\"repo_ID\", \"sha\"], how=\"outer\"\n", ")\n", "\n", "# FILTER\n", "#window_spec = Window.partitionBy([\"repo_ID\", \"sha\"]).orderBy(F.rand())\n", "#commit_full_metadata_df_with_row_num = commit_full_metadata_df.withColumn(\"row_number\", row_number().over(window_spec))\n", "#commit_full_metadata_df_top10_000 = commit_full_metadata_df_with_row_num.filter(col(\"row_number\") <= 2_500).drop(\"row_number\")\n", "# END FILTER\n", "\n", "\n", "grouped_commit_full_metadata_df = commit_full_metadata_df.groupBy(\n", "    \"repo_ID\"\n", ").agg(F.collect_list(F.struct(commit_full_metadata_df.columns)).alias(\"commit_metadata_ls\"))\n", "\n", "def compress_commits(commit_metadata_list):\n", "    return compressed_dumps(commit_metadata_list, compression=\"gzip\")\n", "\n", "grouped_commit_full_metadata_df = grouped_commit_full_metadata_df.withColumn(\n", "    \"commit_metadata_ls\",\n", "    F.udf(compress_commits, BinaryType())(F.col(\"commit_metadata_ls\")),\n", ")\n", "\n", "grouped_commit_full_metadata_df = grouped_commit_full_metadata_df.repartition(1000)\n", "grouped_commit_full_metadata_df.write.parquet(STAGE0_PATH)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze stage 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(STAGE0_PATH)\n", "\n", "def process_repo(commit_metadata_ls):\n", "    commits = compressed_loads(commit_metadata_ls, compression=\"gzip\")\n", "    return len(commits)\n", "\n", "df = df.withColumn(\n", "    \"num_problems\", F.udf(process_repo, IntegerType())(<PERSON><PERSON>col(\"commit_metadata_ls\"))\n", ")\n", "new_ct = df.select(F.sum('num_problems')).collect()[0][0]\n", "\n", "df_old = spark.read.parquet(\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v6/stage0_grouped_commit_full_metadata\")\n", "failed_logs_old_df = spark.read.parquet(\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v6/stage1_generate_problems\")\n", "df_old = df_old.withColumn(\n", "    \"num_problems\", F.udf(process_repo, IntegerType())(<PERSON><PERSON>col(\"commit_metadata_ls\"))\n", ")\n", "old_ct = df_old.select(F.sum('num_problems')).collect()[0][0]\n", "\n", "print(f\"grouped commits count | old: {old_ct}, new: {new_ct}\")\n", "\n", "print(f\"failed logs | new failed logs: {failed_logs_df.count()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 1: Generate dehydrated problems"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "from research.core.utils_for_str import compute_edit_similarity\n", "\n", "\n", "def process_repo(repo_ID, commit_metadata_ls):\n", "    commits = compressed_loads(commit_metadata_ls, compression=\"gzip\")\n", "\n", "    # --------------------------\n", "    # First create three maps:\n", "    # - parent_to_child_sha_map: maps parent sha to child sha\n", "    # - child_to_parent_sha_map: maps child sha to parent sha\n", "    # - commit_to_is_success_by_check_run_name: maps commit sha to a map of check_run_name to whether or not that run was successful.\n", "    parent_to_child_sha_map = {}\n", "    child_to_parent_sha_map = {}\n", "    commit_to_is_success_by_check_run_name = {}\n", "    for commit in commits:\n", "        assert commit['sha'] is not None\n", "        if commit['parent'] is not None:\n", "            parent_to_child_sha_map[commit['parent']] = commit['sha']\n", "            child_to_parent_sha_map[commit['sha']] = commit['parent']\n", "            commit_to_is_success_by_check_run_name[commit['sha']] = {}\n", "            if commit['conclusion_list'] is None:\n", "                continue\n", "            for check_run_info in commit['conclusion_list']:\n", "                commit_to_is_success_by_check_run_name[commit['sha']][check_run_info['check_run_name']] = check_run_info['conclusion'] == 'SUCCESS'\n", "\n", "    # --------------------------\n", "    # Next, create a map from sha to CommitMeta object\n", "    sha_to_commit_meta = {}\n", "    for commit in commits:\n", "        child_sha = parent_to_child_sha_map.get(commit['sha'], None)\n", "        if commit['parent'] is None:\n", "            continue\n", "        sha_to_commit_meta[commit['sha']] = CommitMeta(\n", "            sha=commit['sha'],\n", "            parents=[commit['parent']] if commit['parent'] is not None else [],\n", "            children=[child_sha] if child_sha is not None else [],\n", "            message=commit['message'],\n", "            repo_name=repo_ID,\n", "        )\n", "\n", "    # --------------------------\n", "    # Now, iterate through the commits and create problems\n", "    problems = []\n", "    missing_child_check_runs = 0\n", "    MAX_PROBLEMS = 1_000\n", "    for commit in commits:\n", "        sha = commit['sha']\n", "        pr_number = commit['pr_number']\n", "        # -------------------------\n", "        # Creating a problem involves several steps:\n", "        # - 1) skip commits that don't have at least one failure log\n", "        # - 2) skip commits where none of their failed check runs passed in the child commit\n", "        # - 3) grab a random failing check run that passed in the child commit and use that as the target check run.\n", "        # - 4) grab CommitMeta objects for the commit and its child\n", "        # - 5) Create an Autofix problem\n", "        # - 6) grab all ancestors of the failing commit that failed the same check run and add them to the problem.\n", "\n", "        # -------------------------\n", "        # 1) skip commits that don't have at least one failure log\n", "        if commit['all_logs'] is None:\n", "            continue\n", "\n", "        # -------------------------\n", "        # 2) skip commits where none of their failed check runs passed in the child commit\n", "        child_sha = parent_to_child_sha_map.get(sha, None)\n", "        if child_sha is None:\n", "            continue\n", "        passing_check_runs = []\n", "        for log_info in commit['all_logs']:\n", "            check_run_name = log_info['check_run_name']\n", "            if commit_to_is_success_by_check_run_name[child_sha].get(check_run_name, False):\n", "                passing_check_runs.append((check_run_name, log_info['log']))\n", "            if check_run_name not in commit_to_is_success_by_check_run_name[child_sha]:\n", "                missing_child_check_runs += 1\n", "        if len(passing_check_runs) == 0:\n", "            continue\n", "        \n", "        USE_MULTIPLE_CHECK_RUNS = False\n", "        MAX_EDITS_SIMILARITY_PERC = 1.0 # ie don't filter anything right now\n", "        if not USE_MULTIPLE_CHECK_RUNS:\n", "            candidate_check_runs = random.sample(passing_check_runs, 1)\n", "        else:\n", "            # Compute a set of check runs that share no more than MAX_EDITS_SIMILARITY_PERC % edit similarity\n", "            candidate_check_runs = []\n", "            for check_run_name, log in passing_check_runs:\n", "                for accepted_check_run_name, _ in candidate_check_runs:\n", "                    if compute_edit_similarity(check_run_name, accepted_check_run_name) > MAX_EDITS_SIMILARITY_PERC:\n", "                        break\n", "                else:\n", "                    candidate_check_runs.append((check_run_name, log))\n", "\n", "        # -------------------------\n", "        # 3) grab a random failing check run that passed in the child commit and use that as the target check run.\n", "        for target_check_run_name, log in candidate_check_runs:\n", "\n", "            # -------------------------\n", "            # 4) grab CommitMeta objects for the commit and its child\n", "            if child_sha not in sha_to_commit_meta or sha not in sha_to_commit_meta:\n", "                continue\n", "            child_commitmeta = sha_to_commit_meta[child_sha]\n", "            commit_meta = sha_to_commit_meta[sha]\n", "            \n", "            # -------------------------\n", "            # 5) Create an Autofix problem\n", "            failed_commit_info = AutofixProblem(\n", "                passing_child=child_commitmeta, \n", "                failed_commits=[commit_meta],\n", "                logs=[log],\n", "                logs_check_run_name=target_check_run_name,\n", "                logs_sha=sha,\n", "                pr_number=pr_number,\n", "            )\n", "            # -------------------------\n", "            # 6) grab all ancestors of the failing commit that failed the same check run and add them to the problem.\n", "            oldest_processed_failing_commit = failed_commit_info.failed_commits[-1].sha\n", "            while True:\n", "                parent_failing_commit = child_to_parent_sha_map.get(oldest_processed_failing_commit, None)\n", "                if (\n", "                    parent_failing_commit is None\n", "                    or parent_failing_commit not in commit_to_is_success_by_check_run_name\n", "                    or target_check_run_name not in commit_to_is_success_by_check_run_name[parent_failing_commit]\n", "                    or commit_to_is_success_by_check_run_name[parent_failing_commit][target_check_run_name]\n", "                    or parent_failing_commit not in sha_to_commit_meta\n", "                ):\n", "                    break\n", "                failed_commit_info.failed_commits.append(sha_to_commit_meta[parent_failing_commit])\n", "                oldest_processed_failing_commit = parent_failing_commit\n", "\n", "\n", "            problems.append(failed_commit_info)\n", "\n", "            if len(problems) >= MAX_PROBLEMS:\n", "                break\n", "\n", "        # -------------------------\n", "\n", "    num_problems = len(problems)\n", "    return pd.Series({\n", "        \"repo_ID\": repo_ID,\n", "        \"problems\": compressed_dumps(problems, compression=\"gzip\"),\n", "        \"num_problems\": num_problems,\n", "        \"missing_child_check_runs\": missing_child_check_runs,\n", "    })\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 1:===================>                                (369 + 631) / 1000]\r"]}], "source": ["from research.data.spark.pipelines.utils import map_parquet\n", "print(STAGE0_PATH)\n", "print(STAGE1_PATH)\n", "res = map_parquet.apply(\n", "    spark,\n", "    process_repo,\n", "    input_columns=[\"repo_ID\", \"commit_metadata_ls\"],\n", "    input_path=STAGE0_PATH,\n", "    output_path=STAGE1_PATH,\n", "    batch_size=1,\n", "    timeout=50_000,\n", "    ignore_error=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 2: Hydrate logs"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["stage1_df = spark.read.parquet(STAGE1_PATH)\n", "# drop num_problems column\n", "stage1_df = stage1_df.drop('num_problems')\n", "# convert column to list[binary]\n", "stage1_df = stage1_df.withColumn(\n", "    \"problems\", F.udf(lambda x: [compressed_dumps(problem, compression=\"gzip\") for problem in compressed_loads(x, compression=\"gzip\")], returnType=ArrayType(BinaryType()))(<PERSON>.col(\"problems\"))\n", ")\n", "# explode problems\n", "stage1_df = stage1_df.withColumn('problems', explode('problems'))\n", "# create sha and check_run_name columns\n", "stage1_df = stage1_df.withColumn(\n", "    \"sha\", F.udf(lambda x: compressed_loads(x, compression=\"gzip\").logs_sha, returnType=StringType())(F.col(\"problems\"))\n", ")\n", "stage1_df = stage1_df.withColumn(\n", "    \"check_run_name\", F.udf(lambda x: compressed_loads(x, compression=\"gzip\").logs_check_run_name, returnType=StringType())(<PERSON>.col(\"problems\"))\n", ")\n", "\n", "# fresh load of logs\n", "failed_logs_df = spark.read.parquet(PR_FAILED_LOGS)\n", "failed_logs_df = failed_logs_df.filter(F.col(\"status_code\").isin([200, 206]))\n", "failed_logs_df = failed_logs_df.withColumn(\n", "    \"repo_ID\", <PERSON><PERSON>concat(<PERSON><PERSON>col(\"owner\"), <PERSON><PERSON>lit(\"/\"), <PERSON><PERSON>col(\"name\"))\n", ")\n", "failed_logs_df = failed_logs_df.select([col for col in failed_logs_df.columns if col not in ['owner', 'name', 'pr_number']])\n", "failed_logs_df = failed_logs_df.withColumnRenamed(\"commit_sha\", \"sha\")\n", "failed_logs_df = failed_logs_df.dropDuplicates(['repo_ID', 'sha', 'check_run_name'])\n", "\n", "# hydrate exploded problems with logs and then regroup and compress by repo\n", "stage1_df = stage1_df.join(failed_logs_df, on=['repo_ID', 'sha', 'check_run_name'], how='inner')\n", "# add log to problem objects\n", "def hydrate_problems(problem, log):\n", "    problem = compressed_loads(problem, compression=\"gzip\")\n", "    problem.logs = [log]\n", "    return compressed_dumps(problem, compression=\"gzip\")\n", "stage1_df = stage1_df.withColumn(\n", "    \"problems\", F.udf(hydrate_problems, returnType=BinaryType())(<PERSON><PERSON>col(\"problems\"), <PERSON>.col(\"log\"))\n", ")\n", "stage1_df = stage1_df.select(['repo_ID', 'problems'])\n", "# regroup by repo and store problems and log column\n", "stage1_df = stage1_df.groupBy('repo_ID').agg(F.collect_list('problems').alias('problems'))\n", "def compress_repo(problems):\n", "    problems = [compressed_loads(problem, compression=\"gzip\") for problem in problems]\n", "    return compressed_dumps(problems, compression=\"gzip\")\n", "stage1_df = stage1_df.withColumn('problems', F.udf(compress_repo, returnType=BinaryType())(<PERSON><PERSON>col('problems')))\n", "\n", "# add num_problems column\n", "stage1_df = stage1_df.withColumn('num_problems', F.udf(lambda x: len(compressed_loads(x, compression=\"gzip\")), returnType=IntegerType())(<PERSON><PERSON>col('problems')))\n", "\n", "# write to disk\n", "stage1_df = stage1_df.repartition(1000)\n", "stage1_df.write.parquet(STAGE2_PATH)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/spark-data/user/colin/bugfix_localization_model/v8/stage2_with_hydrated_logs\n"]}, {"data": {"text/plain": ["159239"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["print(STAGE2_PATH)\n", "df = spark.read.parquet(STAGE2_PATH)\n", "num_problems = df.select(F.sum('num_problems')).collect()[0][0]\n", "num_problems"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 3: Explode problems to improve commit hydraration parallelism"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Total input problems: 284404\n", "Total output problems: 284404\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["#    return pd.Series({\n", "#        \"repo_ID\": repo_ID,\n", "#        \"problems\": compressed_dumps(problems, compression=\"gzip\"),\n", "#        \"num_problems\": num_problems,\n", "\n", "df = spark.read.parquet(STAGE2_PATH)\n", "total_input_problems = df.select(F.sum('num_problems')).collect()[0][0]\n", "\n", "def recompress(problems):\n", "    \"\"\"Break up problems into lists of 5\"\"\"\n", "    problems = compressed_loads(problems, compression=\"gzip\")\n", "    new_problems = []\n", "    for i in range(0, len(problems), 5):\n", "        new_problems.append(compressed_dumps(problems[i:i+5], compression=\"gzip\"))\n", "    return new_problems\n", "\n", "df = df.withColumn('problems', F.udf(recompress, returnType=ArrayType(BinaryType()))(<PERSON><PERSON>col('problems')))\n", "df = df.drop('num_problems')\n", "df = df.withColumn('problems', explode('problems'))\n", "\n", "def count_num_problems(problems):\n", "    return len(compressed_loads(problems, compression=\"gzip\"))\n", "df = df.withColumn('num_problems', F.udf(count_num_problems, returnType=IntegerType())(<PERSON><PERSON>col('problems')))\n", "\n", "total_output_problems = df.select(F.sum('num_problems')).collect()[0][0]\n", "\n", "print(f\"Total input problems: {total_input_problems}\")\n", "print(f\"Total output problems: {total_output_problems}\")\n", "\n", "#num_rows = df.count()\n", "#print(f\"Total number of rows: {num_rows}\")\n", "# answer: 34143\n", "\n", "df = df.repartition(5_000)\n", "df.write.parquet(STAGE3_PATH)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 38:==================================================>  (332 + 15) / 347]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Old total output problems: 127786\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["df_old = spark.read.parquet('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v6/stage3_with_exploded_problems')\n", "old_total_output_problems = df_old.select(F.sum('num_problems')).collect()[0][0]\n", "print(f\"Old total output problems: {old_total_output_problems}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analze dataset sizes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["STAGE4_PATH = f\"{TRAINING_DATA_BASE}/stage4_with_hydrated_commits\"\n", "df = spark.read.parquet(STAGE4_PATH + '/*.parquet')\n", "num_problems = df.select(F.sum('num_problems')).collect()[0][0]\n", "print(num_problems)\n", "\n", "df = spark.read.parquet(STAGE3_PATH)\n", "print(df.columns)\n", "print(df.select('num_problems').describe())\n", "\n", "df = spark.read.parquet(STAGE4_PATH + '/*.parquet')\n", "print(df.columns)\n", "num_missing_commits = df.select(F.sum('num_missing_commits')).collect()[0][0]\n", "num_problems = df.select(F.sum('num_problems')).collect()[0][0]\n", "max_problems = df.select(F.max('num_problems')).collect()[0][0]\n", "print(f\"num_problems: {num_problems}, num_missing_commits: {num_missing_commits}, max_problems: {max_problems}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}