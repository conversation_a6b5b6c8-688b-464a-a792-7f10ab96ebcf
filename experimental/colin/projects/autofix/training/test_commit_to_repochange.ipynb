{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "53a304a97bd44248a00b642ccd2065fb", "version_major": 2, "version_minor": 0}, "text/plain": ["building parent commit sha repo:   0%|          | 0/5765 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5647fc0a733422b8df5fcf12c5c6022", "version_major": 2, "version_minor": 0}, "text/plain": ["building parent commit sha repo:   0%|          | 0/5767 [00:00<?, ?file/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from pathlib import Path\n", "from research.utils.repo_change_utils import get_repo_change_for_single_commit\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "\n", "\n", "repo_change1 = get_repo_change_for_single_commit(\n", "    Path(\"/home/<USER>/augment\"),\n", "    commit_sha=\"1d936a2217693c76a41f310da89d37d7db307524\",\n", "    parent_commit_sha=\"80c86dc59df81cbde2255e90b42cff0437edeea2\",\n", "    max_workers=1000,\n", ")\n", "\n", "repo_change2 = get_repo_change_for_single_commit(\n", "    Path(\"/home/<USER>/augment\"),\n", "    commit_sha=\"ae7db3f1a416fdeb0c27462334815b8f514c8cd7\",\n", "    parent_commit_sha=\"1d936a2217693c76a41f310da89d37d7db307524\",\n", "    max_workers=1000,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- /dev/null\n", "+++ experimental/colin/projects/code_execution_feedback_retriever/test_fileB.py\n", "@@ -0,0 +1,3 @@\n", "+# This is a test file B.\n", "+\n", "+# End of test file B.\n", "\\ No newline at end of file\n", "\n", "--- /dev/null\n", "+++ experimental/colin/projects/code_execution_feedback_retriever/test_fileA.py\n", "@@ -0,0 +1,3 @@\n", "+# This is a test file A.\n", "+\n", "+# End of test file A.\n", "\\ No newline at end of file\n", "\n"]}], "source": ["for item in format_file_changes_with_ranges(\n", "    [c.map(lambda x: x.to_file()) for c in list(repo_change1.changed_files)],\n", "    diff_context_lines=3,\n", "):\n", "    print(item.text)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["+++ experimental/colin/projects/code_execution_feedback_retriever/test_fileA.py\n", "@@ -1,3 +1,5 @@\n", " # This is a test file A.\n", " \n", "+# This is a change for file A.\n", "+\n", " # End of test file A.\n", "\\ No newline at end of file\n", "\n", "+++ experimental/colin/projects/code_execution_feedback_retriever/test_fileB.py\n", "@@ -1,3 +1,5 @@\n", " # This is a test file B.\n", " \n", "+# This is a change for file B.\n", "+\n", " # End of test file B.\n", "\\ No newline at end of file\n", "\n"]}], "source": ["for item in format_file_changes_with_ranges(\n", "    [c.map(lambda x: x.to_file()) for c in list(repo_change2.changed_files)],\n", "    diff_context_lines=3,\n", "):\n", "    print(item.text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}