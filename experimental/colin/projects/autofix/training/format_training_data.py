from pathlib import Path
from research.data.spark.utils import k8s_session
from research.next_edits.edit_localization_stages import RetrieverConfigDict
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
import random
from base.prompt_format_next_edit.gen_prompt_formatter_test import StarCoderTokenizer
from experimental.colin.projects.autofix.training.utils import AutofixProblem
from research.data.spark.pipelines.utils import map_parquet
from pyspark.sql.types import BinaryType

import hashlib
import pickle
import pandas as pd
import pyspark.sql.functions as F
from research.utils.repo_change_utils import RepoChange
from research.next_edits.edit_localization_stages import (
    CreateRetrievalProblemsConfig,
    EditLocalizationProblem,
    EditLocalizationRetrievalProblem,
    FormatAsTokensConfig,
    create_retrieval_problems,
    format_as_tokens,
)
from typing import cast
from pyspark.sql import SparkSession
from research.next_edits.next_edits_dataset import <PERSON>Meta
from research.retrieval.types import DocumentIndex
from research.utils.repo_change_utils import CommitMeta

CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"

raven_config: RetrieverConfigDict = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        # "checkpoint_path": str(
        #    RAVEN_ROOT / "raven1b.v13-query-1pos-bs1x8x16-lr2e-05-iters2000-K128"
        # ),
        "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50",
        "tokenizer_name": "starcoder",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 2000,
    },
    "query_formatter": {
        "name": "next_edit_location_query",
        "tokenizer": "starcoder",
        "max_prompt_tokens": 8192,
        "max_instruction_tokens": 6144,
        "use_smart_header": True,
        "deduplicate_identical_paths": True,
        "truncate_instructions_tail": False,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
        "max_tokens": 999,
    },
}

autofix_config: RetrieverConfigDict = {
    "query_formatter": {
        "name": "next_edit_location_query",
        "tokenizer": "starcoder",
        "max_prompt_tokens": 16384,
        "max_instruction_tokens": 8192,
        "use_smart_header": True,
        "deduplicate_identical_paths": True,
        "truncate_instructions_tail": False,
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "tokenizer": "starcoder",
        "max_tokens": 999,
    },
}
TRAINING_DATA_BASE = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/v11"
TMP_OUTPUT_ROOT = "/mnt/efs/spark-data/temp-weekly/colin/autofix-location"

eval_repo_names_path = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/eval_repo_names.txt"

stage4_path = f"{TRAINING_DATA_BASE}/stage4_with_hydrated_commits"
stage5_with_logs_path = f"{TRAINING_DATA_BASE}/stage5_with_logs"
stage5_with_logs_500partition_path = f"{TRAINING_DATA_BASE}/stage5_with_logs_500partition"
stage6_with_retrieval_path = f"{TRAINING_DATA_BASE}/stage6_with_retrieval"
stagepre7_train_path = f"{TRAINING_DATA_BASE}/stagepre7_train"
stagepre7_eval_path = f"{TRAINING_DATA_BASE}/stagepre7_eval"
stage7_train_format_as_tokens_path = f"{TRAINING_DATA_BASE}/stage7_train_format_as_tokens"
stage7_eval_format_as_tokens_path = f"{TRAINING_DATA_BASE}/stage7_eval_format_as_tokens"
stage8_train_shuffled_path = f"{TRAINING_DATA_BASE}/stage8_train_shuffled"
stage8_eval_shuffled_path =  f"{TRAINING_DATA_BASE}/stage8_eval_shuffled"
stage9_train_indexed_dataset_path = f"{TRAINING_DATA_BASE}/stage9_train_indexed_dataset"
stage9_eval_indexed_dataset_path = f"{TRAINING_DATA_BASE}/stage9_eval_indexed_dataset"


def create_spark(max_workers: int = 5):
    spark = k8s_session(
        max_workers=max_workers,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.task.maxFailures": "10",
            # add lots of memory
            "spark.executor.pyspark.memory": "1050G",
        },
    )
    return spark


def create_gpu_spark(max_workers: int = 5):
    spark = k8s_session(
        max_workers=max_workers,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.task.maxFailures": "10",
            # add lots of memory
            "spark.executor.pyspark.memory": "1050G",
            "spark.task.cpus": "5",
            "spark.executor.cpus": "5",
        },
        gpu_type="H100_NVLINK_80GB",
        # gpu_type=["RTX_A4000", "RTX_A5000", "RTX_A6000", "A40"],
    )
    return spark

#spark = create_gpu_spark(100)
spark = create_spark(1000)

# --------
# --------
# STAGE 5-POST: REPARTITION
# --------
# --------

if False:
    print("STAGE 5-POST: REPARTITION")
    df = spark.read.parquet(stage5_with_logs_path)
    df = df.repartition(500)
    df.write.parquet(stage5_with_logs_500partition_path)

# --------
# --------
# STAGE 6: Create positive and negative chunks
# --------
# --------

def convert_to_edit_localization_problem(
    repo_id,
    problem: AutofixProblem,
) -> EditLocalizationProblem:
    pr_meta = PRMeta(
        repo_name=repo_id,
        pr_number=-1,
        title="",
        body="",
    )
    commit_meta = CommitMeta(
        sha="",
        parents=[],
        children=[],
        message="",
        repo_name=repo_id,
    )

    edit_localization_problem = EditLocalizationProblem(
        pr_meta=pr_meta,
        commit_meta=commit_meta,
        instructions=problem.logs[0],
        wip_to_future_repo_change=problem.fixing_change,
        past_to_wip_repo_change=problem.breaking_change,
    )
    return edit_localization_problem


_retriever_cached: tuple[DocumentIndex, RetrieverConfigDict] | None = None


def _get_retriever_cached(config: RetrieverConfigDict) -> DocumentIndex:
    """Get the cached retriever instance."""
    # vv Moving the import here fixed my issue vv
    from research.eval.harness.factories import create_retriever

    global _retriever_cached

    if _retriever_cached is None or _retriever_cached[1] != config:
        index = create_retriever(cast(dict, config))
        index.load()
        _retriever_cached = (index, config)
    else:
        index, _ = _retriever_cached
    return index


def create_retrieval_problems_wrapper_autofix(
    input_path: Path | str,
    output_path: Path | str,
    task_info_location: Path | str,
    config: CreateRetrievalProblemsConfig,
    spark: SparkSession,
    timing_run: bool = False,
    global_seed: int = 0,
) -> dict:
    def process_batch(df: pd.DataFrame) -> pd.DataFrame:
        output_per_repo = dict[
            str, list[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]]
        ]()

        retriever = _get_retriever_cached(config.retriever_config)

        for row in df.to_dict(orient="records"):
            new_problems: list[EditLocalizationProblem] = []
            problems = compressed_loads(row["problems"], compression="gzip")
            for problem in problems:
                edit_localization_problem = convert_to_edit_localization_problem(
                    row["repo_id"], problem
                )
                new_problems.append(edit_localization_problem)

            output_per_repo.setdefault(row["repo_id"], []).extend(
                create_retrieval_problems(
                    new_problems, retriever, config, seed=global_seed + hash(row["repo_id"])
                )
            )

        return pd.DataFrame(
            [
                {
                    "id": repo_name,
                    "repo_name": repo_name,
                    "num_problems": len(output),
                    "pickled_results": compressed_dumps(output, compression="gzip"),
                }
                for repo_name, output in output_per_repo.items()
            ]
        )

    return map_parquet.apply_pandas(
        spark_session=spark,
        pandas_func=process_batch,
        input_path=str(input_path),
        output_path=str(output_path),
        batch_size=1,
        task_info_location=str(task_info_location),
        timeout=(
            3 * 3600  # 3 hours for dense retrieval
            if config.retrieval_strategy == "retrieved"
            else 3600  # 1 hour for other strategies retrieval
        ),
        input_columns=[
            "repo_id",
            "problems"
        ],
        ignore_error=False,
        timing_run=timing_run,
        allow_resume=True
    )

if False:
    print("STAGE 6: Create positive and negative chunks")
    create_retrieval_problems_wrapper_autofix(
        input_path=stage5_with_logs_500partition_path,
        output_path=stage6_with_retrieval_path,
        task_info_location=stage6_with_retrieval_path + ".logs",
        config=CreateRetrievalProblemsConfig(
            retriever_config=raven_config,
            retrieval_strategy="retrieved",
            num_retrieved_chunks=128,
            ignore_whitespace_changes=True,
        ),
        spark=spark,
        timing_run=False,
        global_seed=0,
    )

# --------
# --------
# STAGE 7-PRE: Shuffle and hold out eval
# --------
# --------

if True:
    print("STAGE 7-PRE: Shuffle and hold out eval")
    eval_repo_names_path = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/eval_repo_names.txt"
    with open(eval_repo_names_path, "r") as f:
        eval_repo_names = f.read().splitlines()

    stage6_with_retrieval_df = spark.read.parquet(stage6_with_retrieval_path)
    stage6_with_retrieval_df = stage6_with_retrieval_df.orderBy(F.rand()).repartition(
        500
    )

    # count number of repos
    #stage6_with_retrieval_df.select(F.countDistinct("repo_name")).show()
    #stage6_with_retrieval_df.select(F.sum("num_problems")).show()
    #stage6_with_retrieval_df.limit(500).select(F.sum("num_problems")).show()

    # grab 100 repos pull them out into eval, put the rest in train 
    #stage7pre_eval_df = stage6_with_retrieval_df.limit(500)
    # or grab from list eval_repo_names
    stage7pre_eval_df = stage6_with_retrieval_df.filter(
        F.col("repo_name").isin(eval_repo_names)
    )

    stage7pre_train_df = stage6_with_retrieval_df.exceptAll(stage7pre_eval_df)
    print(stage6_with_retrieval_df.count(), stage7pre_eval_df.count(), stage7pre_train_df.count())

    stage7pre_eval_df.write.parquet(stagepre7_eval_path)
    stage7pre_train_df.write.parquet(stagepre7_train_path)

# --------
# --------
# STAGE 7: Format data
# --------
# --------

if True:
    print("STAGE 7: Format data")
    from typing import Any
    from research.utils.token_array_utils import TokenArray

    config = FormatAsTokensConfig(
        query_formatter_config=autofix_config["query_formatter"],
        document_formatter_config=autofix_config["document_formatter"],
    )
    global_seed = 0


    def process_batch(df: pd.DataFrame):
        ids_per_repo = dict[str, Any]()
        output_per_repo = dict[str, list[tuple[TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem]]]()
        for row in df.to_dict(orient="records"):
            problems: list[
                tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
            ] = compressed_loads(row["pickled_results"], compression="gzip")
            assert len(problems) == row["num_problems"]
            repo_name = row["repo_name"]
            output_per_repo.setdefault(repo_name, []).extend(
                format_as_tokens(config, problems, seed=global_seed + hash(row["id"]))
            )
            ids_per_repo.setdefault(repo_name, hashlib.sha256()).update(row["id"].encode())
        return pd.DataFrame(
            [
                {
                    "id": f"{repo_name}/{ids_per_repo[repo_name].hexdigest()}",
                    "repo_name": repo_name,
                    "num_problems": len(output),
                    "pickled_results": compressed_dumps(output, compression="gzip"),
                }
                for repo_name, output in output_per_repo.items()
            ]
        )
    
    map_parquet.apply_pandas(
        spark,
        process_batch,
        str(stagepre7_train_path),
        str(stage7_train_format_as_tokens_path),
        task_info_location=stage7_train_format_as_tokens_path + ".logs",
        batch_size=1,
        input_columns=["id", "repo_name", "num_problems", "pickled_results"],
        timeout=3600,  # 1 hour is more than enough for this stage.
        ignore_error=False,
        timing_run=False,
    )

    map_parquet.apply_pandas(
        spark,
        process_batch,
        str(stagepre7_eval_path),
        str(stage7_eval_format_as_tokens_path),
        task_info_location=stage7_eval_format_as_tokens_path + ".logs",
        batch_size=1,
        input_columns=["id", "repo_name", "num_problems", "pickled_results"],
        timeout=3600,  # 1 hour is more than enough for this stage.
        ignore_error=False,
        timing_run=False,
    )

# --------
# --------
# STAGE 8: SHUFFLE
# --------
# --------

if True:
    print("STAGE 8: SHUFFLE")
    from pyspark.sql.types import BinaryType, ArrayType

    input_output_rows = [
        # (stage7_train_format_as_tokens_path, stage8_train_shuffled_path, 1000),
        (stage7_eval_format_as_tokens_path, stage8_eval_shuffled_path, 5),
    ]
    for input_path, output_path, num_partitions in input_output_rows:
        stage7_format_as_tokens_df = spark.read.parquet(input_path)
        stage7_format_as_tokens_df = stage7_format_as_tokens_df.select(
            "repo_name", "pickled_results"
        )

        def unpack_pickle(pickled_results):
            # results type: list[tuple[TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem]]
            # map to: list[TokenArray]
            pickled_results = compressed_loads(pickled_results, compression="gzip")
            return [compressed_dumps(x[0], compression="gzip") for x in pickled_results]


        unpack_pickle_udf = F.udf(unpack_pickle, returnType=ArrayType(BinaryType()))

        stage7_format_as_tokens_df = stage7_format_as_tokens_df.withColumn(
            "pickled_results", unpack_pickle_udf(F.col("pickled_results"))
        )
        stage7_format_as_tokens_df = stage7_format_as_tokens_df.withColumn(
            "pickled_results", F.explode("pickled_results")
        )
        stage7_format_as_tokens_df = stage7_format_as_tokens_df.orderBy(F.rand()).repartition(
            num_partitions
        )
        stage7_format_as_tokens_df.write.parquet(output_path)

# --------
# --------
# STAGE 9: CREATE INDEXED DATASET
# --------
# --------

if True:
    print("STAGE 9: CREATE INDEXED DATASET")
    from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
        run_create_indexed_dataset,
    )

    input_output_rows = [
        (stage8_train_shuffled_path, stage9_train_indexed_dataset_path),
        (stage8_eval_shuffled_path, stage9_eval_indexed_dataset_path),
    ]

    for input_path, output_path in input_output_rows:
        indexed_dataset_output_path = run_create_indexed_dataset(
            input_path=input_path,
            output_path=output_path,
            tokenizer_name="starcoder",
            overwrite=True,
            num_validation_samples=0,
        )