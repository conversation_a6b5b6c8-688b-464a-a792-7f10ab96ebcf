"""<PERSON><PERSON><PERSON> to generate a configuration and a launch model training job."""

import logging
import typing
from collections.abc import Sequence
from pathlib import Path
from typing import Any, Literal

import yaml

from research.core.constants import AUGMENT_ROOT
from research.environments.providers import ClusterName
from research.fastbackward.determined.launch import launch_fb_job
from research.fastbackward.utils import (
    combine_dict,
    parse_key_value_args,
    unflatten_dict,
)

logger = logging.getLogger(__name__)

TEMPLATE = """
determined:
  description: null
  workspace: Dev
  project: autofix

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: {gpus}
  project_group: finetuning
  keep_last_n_checkpoints: 3

fastbackward_args:
  run_name: autofix-colin-{model_version}
  wandb_project: autofix

  components:
    model:
      component_name: create_dual_encoder_with_tokenizer
      tokenizer: tokenizer
    loss_fn:
      component_name: PerplexityLoss
      config:
        gold_temperature: 0.01
        pred_temperature: 1000.0
        logits_scale: 1.0
        learnable_logits_scale: True

  eval_interval: 10
  checkpoint_interval: 100

  max_epochs: -1
  train_options:
    # Total batch size should be 256 = 8 x 32
    batch_size: 1
    gradient_accumulation_steps: {grad_accumulation_steps}
    max_iters: {max_iters}
    log_interval: 1
    grad_clip: 1.0

    optimizer:
      warmup_iters: 0
      learning_rate: 2.0e-5
      min_lr: 2.0e-6
  eval_batch_size: 8

  train_data:
    path: {train_data_path}
    tokenizer_name: starcoder
    documents_per_batch: 128
    max_document_tokens: 1792
    max_positive_count: 1
    selection_method: random

  eval_data:
    path: {eval_data_path}
    tokenizer_name: starcoder
    limit: 1024
    documents_per_batch: 128
"""

ModelType = Literal["query", "tied"]

CONFIGS = {
    "query": {
        "fastbackward_args.components": {
            "model": {
                "query_model": "query_model",
                "doc_model": "doc_model",
                "freeze_document_model": True,
            },
            "query_model": {
                "$component_name": "research.fastbackward.retrieval_models.load_embedder_from_checkpoint",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50/",
                # old: "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50/",
            },
            "doc_model": {
                "$component_name": "research.fastbackward.retrieval_models.load_embedder_from_checkpoint",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/ethanol/stareth_2000s_128docactual_smart",
            },
        }
    },
    # "tied": {
    #    "fastbackward_args.components": {
    #        "model": {
    #            "query_model": "query_model",
    #            "doc_model": "query_model",
    #            "freeze_document_model": False,
    #        },
    #        "query_model": {
    #            "$component_name": "research.fastbackward.retrieval_models.load_embedder_from_checkpoint",
    #            "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50/",
    #        },
    #    }
    # },
    "8targets": {
        "fastbackward_args.train_data.max_positive_count": 8,
        "fastbackward_args.components.loss_fn.config.gold_temperature": 1,
    },
    # "8targets.rel": {
    #    "fastbackward_args.train_data.max_positive_count": 8,
    #    "fastbackward_args.train_data.positive_threshold": -1,
    #    "fastbackward_args.components.loss_fn.config.gold_temperature": 1,
    # },
}

MODEL_VERSION = "v10-1epoch"
DATA_VERSION = "v10"
VAL_DATA_PATH = f"/mnt/efs/spark-data/user/colin/bugfix_localization_model/{DATA_VERSION}/stage9_eval_indexed_dataset/dataset"
TRAIN_DATA_PATH = f"/mnt/efs/spark-data/user/colin/bugfix_localization_model/{DATA_VERSION}/stage9_train_indexed_dataset/dataset"


def main(
    model_configs: Sequence[str],
    data_train_path: Path,
    data_val_path: Path,
    cluster: ClusterName,
    script_path: Path = AUGMENT_ROOT / "research/fastbackward/train_retriever.py",
):
    batch_size = 64
    nodes = 8
    assert 8 * nodes <= batch_size
    num_samples_in_training_data = 104_000

    """Main function."""
    config = yaml.safe_load(
        TEMPLATE.format(
            gpus=8 * nodes,
            model_version=MODEL_VERSION,
            model_type=".".join(model_configs),
            data_name=data_train_path.name,
            train_data_path=data_train_path,
            eval_data_path=data_val_path,
            grad_accumulation_steps=batch_size // (8 * nodes),
            max_iters=num_samples_in_training_data // batch_size,
        )
    )
    for option in model_configs:
        config = combine_dict(config, unflatten_dict(CONFIGS[option]))

    logger.info("Running config:\n%s", yaml.dump(config))
    launch_fb_job(config, {}, script_path, cluster=cluster)


"""
Suppose you want a batch size of B. Then you set grad_accum_steps = B // num_gpus = B // (8*num_nodes).
And you want to set the num_nodes to ideally B // 8.

Example 1: B=128
- target batch size 128, so we want 128 // 8 = 16 nodes.
- 19671 samples -> 19671 // 128 = 153 iter

Example 2: B=64
- target batch size 64, so we want 64 // 8 = 8 nodes.
- 19671 samples -> 19671 // 64 = 307 iter
- 106068 samples -> 106068 // 64 = 1657 iter
"""


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    main(
        ["query", "8targets"],
        Path(TRAIN_DATA_PATH),
        Path(VAL_DATA_PATH),
        "CW",
    )
