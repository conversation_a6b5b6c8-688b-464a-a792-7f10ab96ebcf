{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting KUBECONFIG to /home/<USER>/.kube/config\n"]}], "source": ["import copy\n", "from pathlib import Path\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.data.spark.utils import k8s_session\n", "from research.next_edits.edit_localization_stages import RetrieverConfigDict\n", "from compress_pickle import dumps as compressed_dumps\n", "from compress_pickle import loads as compressed_loads\n", "import random\n", "from base.prompt_format_next_edit.gen_prompt_formatter_test import StarCoderTokenizer\n", "from experimental.colin.projects.autofix.training.utils import AutofixProblem\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from pyspark.sql.types import BinaryType\n", "\n", "import hashlib\n", "import pickle\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "from research.utils.repo_change_utils import RepoChange\n", "from research.next_edits.edit_localization_stages import (\n", "    CreateRetrievalProblemsConfig,\n", "    EditLocalizationProblem,\n", "    EditLocalizationRetrievalProblem,\n", "    FormatAsTokensConfig,\n", "    create_retrieval_problems,\n", "    format_as_tokens,\n", ")\n", "\n", "CHECKPOINT_ROOT = Path(\"/mnt/efs/augment/checkpoints\")\n", "RAVEN_ROOT = CHECKPOINT_ROOT / \"next-edit-location\"\n", "\n", "raven_config: RetrieverConfigDict = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_fbwd\",\n", "        # \"checkpoint_path\": str(\n", "        #    RAVEN_ROOT / \"raven1b.v13-query-1pos-bs1x8x16-lr2e-05-iters2000-K128\"\n", "        # ),\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50\",\n", "        \"tokenizer_name\": \"starcoder\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 2000,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"next_edit_location_query\",\n", "        \"tokenizer\": \"starcoder\",\n", "        \"max_prompt_tokens\": 8192,\n", "        \"max_instruction_tokens\": 4096,\n", "        \"use_smart_header\": True,\n", "        \"deduplicate_identical_paths\": True,\n", "        \"truncate_instructions_tail\": <PERSON>alse,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:ethanol6-embedding-with-path-key\",\n", "        \"tokenizer\": \"starcoder\",\n", "        \"max_tokens\": 999,\n", "    },\n", "}\n", "\n", "# This is a special config for `format_as_tokens` in\n", "# research/next_edits/edit_localization_stages.py. It does NOT\n", "# match RetrieverConfigDict above.\n", "autofix_config = {\n", "    \"query_formatter\": {\n", "        \"name\": \"next_edit_location_query\",\n", "        \"tokenizer\": \"starcoder\",\n", "        \"max_prompt_tokens\": 8192,\n", "        \"max_diff_tokens\": 4096,\n", "        \"max_instruction_tokens\": 4096,\n", "        \"use_smart_header\": True,\n", "        \"deduplicate_identical_paths\": True,\n", "        \"truncate_instructions_tail\": <PERSON>alse,\n", "    },\n", "    \"document_formatter\": {\"add_path\": True},\n", "}\n", "\n", "autofix_config_no_truncation_query_formatter = {**autofix_config[\"query_formatter\"]}\n", "autofix_config_no_truncation_query_formatter[\"max_prompt_tokens\"] = 2048 * 128\n", "autofix_config_no_truncation_query_formatter[\"max_diff_tokens\"] = 2048 * 64\n", "autofix_config_no_truncation_query_formatter[\"max_instruction_tokens\"] = 2048 * 64\n", "\n", "autofix_config_no_instructions_query_formatter = {**autofix_config[\"query_formatter\"]}\n", "autofix_config_no_instructions_query_formatter[\"max_instruction_tokens\"] = 0\n", "\n", "autofix_config_no_diff_query_formatter = {**autofix_config[\"query_formatter\"]}\n", "autofix_config_no_diff_query_formatter[\"max_diff_tokens\"] = 0\n", "\n", "VERSION = \"v12\"\n", "TRAINING_DATA_BASE = (\n", "    f\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/{VERSION}\"\n", ")\n", "TRAINING_DATA_BASE_GS = f\"gs://gcp-us1-user/colin/bugfix_localization_model/{VERSION}\"\n", "TMP_OUTPUT_ROOT = \"/mnt/efs/spark-data/temp-weekly/colin/autofix-location\"\n", "\n", "stage4_path = f\"{TRAINING_DATA_BASE}/stage4_with_hydrated_commits\"\n", "stage5_with_logs_path = f\"{TRAINING_DATA_BASE}/stage5_with_logs\"\n", "stage5_with_logs_500partition_path = (\n", "    f\"{TRAINING_DATA_BASE}/stage5_with_logs_500partition\"\n", ")\n", "stage6_with_retrieval_path = f\"{TRAINING_DATA_BASE}/stage6_with_retrieval\"\n", "stagepre7_train_path = f\"{TRAINING_DATA_BASE_GS}/stagepre7_train\"\n", "stagepre7_eval_path = f\"{TRAINING_DATA_BASE_GS}/stagepre7_eval\"\n", "stage7_train_format_as_tokens_path = (\n", "    f\"{TRAINING_DATA_BASE}/stage7_train_format_as_tokens\"\n", ")\n", "stage7_eval_format_as_tokens_path = f\"{TRAINING_DATA_BASE}/stage7_eval_format_as_tokens\"\n", "stage8_train_shuffled_path = f\"{TRAINING_DATA_BASE}/stage8_train_shuffled\"\n", "stage8_eval_shuffled_path = f\"{TRAINING_DATA_BASE}/stage8_eval_shuffled\"\n", "stage9_train_indexed_dataset_path = f\"{TRAINING_DATA_BASE}/stage9_train_indexed_dataset\"\n", "stage9_eval_indexed_dataset_path = f\"{TRAINING_DATA_BASE}/stage9_eval_indexed_dataset\"\n", "\n", "\n", "def create_spark(max_workers: int = 5):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            # \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            # \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            # \"spark.task.maxFailures\": \"10\",\n", "            # add lots of memory\n", "            # \"spark.executor.pyspark.memory\": \"1050G\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "def create_gpu_spark(max_workers: int = 5):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "            # add lots of memory\n", "            # \"spark.executor.pyspark.memory\": \"1050G\",\n", "            \"spark.task.cpus\": \"5\",\n", "            \"spark.executor.cpus\": \"5\",\n", "        },\n", "        # gpu_type=\"H100_NVLINK_80GB\",\n", "        gpu_type=[\"RTX_A4000\", \"RTX_A5000\", \"RTX_A6000\", \"A40\"],\n", "    )\n", "    return spark"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Skipping bazel build.\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}], "source": ["# spark = create_gpu_spark(100)\n", "spark = create_spark(1000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 5: Compute truncated logs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer = StarCoderTokenizer()\n", "MAX_TOKS = 16_000\n", "\n", "\n", "def clean_logs(log: str) -> str:\n", "    \"\"\"Clean up logs to make them more similar to .\"\"\"\n", "\n", "    log = pd.Series(log)\n", "\n", "    # Remove timestamps: Since they aren't present at inference time and are very token-expensive:\n", "    datetime_regex = r\"\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d+Z\"\n", "    log = log.str.replace(datetime_regex, \"\", regex=True).str.strip()\n", "\n", "    # Remove non-ASCII characters: These are also token-expensive, pretty common in CI logs:\n", "    log = log.str.encode(\"ascii\", \"ignore\").str.decode(\"ascii\")\n", "\n", "    # Remove ANSI escape sequences: To further reduce token usage, these are abundant for terminal coloring:\n", "    ansi_escape_regex = r\"\\x1B[@-_][0-?]*[ -/]*[@-~]\"\n", "    log = log.str.replace(ansi_escape_regex, \"\", regex=True)\n", "\n", "    log = list(log)[0]\n", "\n", "    return log\n", "\n", "\n", "def process_logs(repo_id, problems):\n", "    problems: list[AutofixProblem] = compressed_loads(problems, compression=\"gzip\")\n", "    new_problems = []\n", "    for problem in problems:\n", "        if len(problem.logs) == 0:\n", "            continue\n", "        assert len(problem.logs) == 1, len(problem.logs)\n", "        sample_log = clean_logs(problem.logs[0])\n", "        truncated_logs = tokenizer.detokenize(\n", "            tokenizer.tokenize_safe(sample_log)[-MAX_TOKS:]\n", "        )\n", "        problem.logs = [truncated_logs]\n", "        new_problems.append(problem)\n", "    return pd.Series(\n", "        {\n", "            \"repo_id\": repo_id,\n", "            \"problems\": compressed_dumps(new_problems, compression=\"gzip\"),\n", "        }\n", "    )\n", "\n", "\n", "if False:\n", "    # Only set to true if you are not using <PERSON><PERSON>'s log extractor anymore.\n", "    # Otherwise, run experimental/lior/build_log_retriever/scaled_run.py instead of running this step.\n", "    map_parquet.apply(\n", "        spark,\n", "        process_logs,\n", "        input_path=str(stage4_path),\n", "        output_path=str(stage5_with_logs_path),\n", "        task_info_location=stage5_with_logs_path + \".logs\",\n", "        batch_size=1,\n", "        input_columns=[\"repo_id\", \"problems\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(stage5_with_logs_path)\n", "df = df.repartition(500)\n", "df.write.parquet(stage5_with_logs_500partition_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 6: Create positive and negative chunks"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from typing import cast\n", "from pyspark.sql import SparkSession\n", "from research.next_edits.next_edits_dataset import PRMeta\n", "from research.retrieval.types import DocumentIndex\n", "from research.utils.repo_change_utils import CommitMeta\n", "\n", "\n", "def convert_to_edit_localization_problem(\n", "    repo_id,\n", "    problem: AutofixProblem,\n", ") -> EditLocalizationProblem:\n", "    pr_meta = PRMeta(\n", "        repo_name=repo_id,\n", "        pr_number=-1,\n", "        title=\"\",\n", "        body=\"\",\n", "    )\n", "    commit_meta = CommitMeta(\n", "        sha=\"\",\n", "        parents=[],\n", "        children=[],\n", "        message=\"\",\n", "        repo_name=repo_id,\n", "    )\n", "\n", "    edit_localization_problem = EditLocalizationProblem(\n", "        pr_meta=pr_meta,\n", "        commit_meta=commit_meta,\n", "        instructions=problem.logs[0],\n", "        wip_to_future_repo_change=problem.fixing_change,\n", "        past_to_wip_repo_change=problem.breaking_change,\n", "    )\n", "    return edit_localization_problem\n", "\n", "\n", "_retriever_cached: tuple[DocumentIndex, RetrieverConfigDict] | None = None\n", "\n", "\n", "def _get_retriever_cached(config: RetrieverConfigDict) -> DocumentIndex:\n", "    \"\"\"Get the cached retriever instance.\"\"\"\n", "    # vv Moving the import here fixed my issue vv\n", "    from research.eval.harness.factories import create_retriever\n", "\n", "    global _retriever_cached\n", "\n", "    if _retriever_cached is None or _retriever_cached[1] != config:\n", "        index = create_retriever(cast(dict, config))\n", "        index.load()\n", "        _retriever_cached = (index, config)\n", "    else:\n", "        index, _ = _retriever_cached\n", "    return index\n", "\n", "\n", "def create_retrieval_problems_wrapper_autofix(\n", "    input_path: Path | str,\n", "    output_path: Path | str,\n", "    task_info_location: Path | str,\n", "    config: CreateRetrievalProblemsConfig,\n", "    spark: SparkSession,\n", "    timing_run: bool = False,\n", "    global_seed: int = 0,\n", ") -> dict:\n", "    def process_batch(df: pd.DataFrame) -> pd.DataFrame:\n", "        output_per_repo = dict[\n", "            str, list[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]]\n", "        ]()\n", "\n", "        retriever = _get_retriever_cached(config.retriever_config)\n", "\n", "        for row in df.to_dict(orient=\"records\"):\n", "            new_problems: list[EditLocalizationProblem] = []\n", "            problems = compressed_loads(row[\"problems\"], compression=\"gzip\")\n", "            for problem in problems:\n", "                edit_localization_problem = convert_to_edit_localization_problem(\n", "                    row[\"repo_id\"], problem\n", "                )\n", "                new_problems.append(edit_localization_problem)\n", "\n", "            output_per_repo.setdefault(row[\"repo_id\"], []).extend(\n", "                create_retrieval_problems(\n", "                    new_problems,\n", "                    retriever,\n", "                    config,\n", "                    seed=global_seed + hash(row[\"repo_id\"]),\n", "                )\n", "            )\n", "\n", "        return pd.DataFrame(\n", "            [\n", "                {\n", "                    \"id\": repo_name,\n", "                    \"repo_name\": repo_name,\n", "                    \"num_problems\": len(output),\n", "                    \"pickled_results\": compressed_dumps(output, compression=\"gzip\"),\n", "                }\n", "                for repo_name, output in output_per_repo.items()\n", "            ]\n", "        )\n", "\n", "    return map_parquet.apply_pandas(\n", "        spark_session=spark,\n", "        pandas_func=process_batch,\n", "        input_path=str(input_path),\n", "        output_path=str(output_path),\n", "        batch_size=1,\n", "        task_info_location=str(task_info_location),\n", "        timeout=(\n", "            3 * 3600  # 3 hours for dense retrieval\n", "            if config.retrieval_strategy == \"retrieved\"\n", "            else 3600  # 1 hour for other strategies retrieval\n", "        ),\n", "        input_columns=[\"repo_id\", \"problems\"],\n", "        ignore_error=False,\n", "        timing_run=timing_run,\n", "        allow_resume=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["create_retrieval_problems_wrapper_autofix(\n", "    input_path=stage5_with_logs_500partition_path,\n", "    output_path=stage6_with_retrieval_path,\n", "    task_info_location=stage6_with_retrieval_path + \".logs\",\n", "    config=CreateRetrievalProblemsConfig(\n", "        retriever_config=raven_config,\n", "        retrieval_strategy=\"retrieved\",\n", "        num_retrieved_chunks=128,\n", "        ignore_whitespace_changes=True,\n", "    ),\n", "    spark=spark,\n", "    timing_run=False,\n", "    global_seed=0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(stage6_with_retrieval_path)\n", "# sum num_problems\n", "print(df.select(F.sum(\"num_problems\")).show())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import asdict\n", "\n", "stage4_with_retrieval_df = spark.read.parquet(stage6_with_retrieval_path)\n", "stage4_with_retrieval_df = stage4_with_retrieval_df.limit(5).to<PERSON>anda<PERSON>()\n", "\n", "\n", "for j in range(len(stage4_with_retrieval_df)):\n", "    res = compressed_loads(\n", "        stage4_with_retrieval_df.iloc[j][\"pickled_results\"], compression=\"gzip\"\n", "    )\n", "    print(res[0][1])\n", "    print(asdict(res[0][1]).keys())\n", "    for i in range(len(res)):\n", "        num_pos_chunks = (\n", "            len(res[i][1].positive_chunks) if res[i][1].positive_chunks else 0\n", "        )\n", "        if res[i][1].negative_chunks:\n", "            num_uniform_neg_chunks = len(res[i][1].negative_chunks[\"uniform\"])\n", "            num_retrieved_neg_chunks = len(res[i][1].negative_chunks[\"retrieved\"])\n", "        else:\n", "            num_uniform_neg_chunks = 0\n", "            num_retrieved_neg_chunks = 0\n", "        print(\n", "            \"Num positive chunks: {}, num uniform negative chunks: {}, num retrieved negative chunks: {}\".format(\n", "                num_pos_chunks, num_uniform_neg_chunks, num_retrieved_neg_chunks\n", "            )\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 7-pre: shuffle and hold out eval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RAVEN AND AUTOFIX EVAL REPO NAMES\n", "\n", "# RAVEN EVAL REPO NAMES\n", "\n", "eval_repo_names_path = (\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/raven_eval_repo_names.txt\"\n", ")\n", "\n", "if not Path(eval_repo_names_path).exists():\n", "    print(\"Creating eval repo names\")\n", "    PR_VALIDATION_DATA_PATH = (\n", "        \"/mnt/efs/spark-data/shared/gh_pr_repo_joined_sha_sorted_validation\"\n", "    )\n", "    raven_eval_data_df = spark.read.parquet(PR_VALIDATION_DATA_PATH)\n", "    repo_names = (\n", "        raven_eval_data_df.select(\"repo_name\")\n", "        .distinct()\n", "        .toPandas()[\"repo_name\"]\n", "        .tolist()\n", "    )\n", "\n", "    with open(eval_repo_names_path, \"w\") as f:\n", "        f.write(\"\\n\".join(repo_names))\n", "else:\n", "    print(\"Eval repo names already exist\")\n", "\n", "\n", "with open(eval_repo_names_path, \"r\") as f:\n", "    eval_repo_names = f.read().splitlines()\n", "\n", "# AUTOFIX EVAL REPO NAMES\n", "\n", "autofix_eval_repo_names_path = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names.txt\"\n", "if not Path(autofix_eval_repo_names_path).exists():\n", "    stage7pre_eval_df = spark.read.parquet(\n", "        \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v11/stagepre7_eval\"\n", "    )\n", "    unique_repo_names = (\n", "        stage7pre_eval_df.select(\"repo_name\")\n", "        .distinct()\n", "        .toPandas()[\"repo_name\"]\n", "        .tolist()\n", "    )\n", "\n", "    print(\"Creating autofix eval repo names\")\n", "    with open(autofix_eval_repo_names_path, \"w\") as f:\n", "        f.write(\"\\n\".join(unique_repo_names))\n", "else:\n", "    print(\"Autofix eval repo names already exist\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_repo_names_path = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names.txt\"\n", "with open(eval_repo_names_path, \"r\") as f:\n", "    eval_repo_names = f.read().splitlines()\n", "\n", "stage6_with_retrieval_df = spark.read.parquet(stage6_with_retrieval_path)\n", "stage6_with_retrieval_df = stage6_with_retrieval_df.orderBy(F.rand())\n", "\n", "# count number of repos\n", "# stage6_with_retrieval_df.select(F.countDistinct(\"repo_name\")).show()\n", "# stage6_with_retrieval_df.select(F.sum(\"num_problems\")).show()\n", "# stage6_with_retrieval_df.limit(500).select(F.sum(\"num_problems\")).show()\n", "\n", "# grab 100 repos pull them out into eval, put the rest in train\n", "# stage7pre_eval_df = stage6_with_retrieval_df.limit(500)\n", "# or grab from list eval_repo_names\n", "stage7pre_eval_df = stage6_with_retrieval_df.filter(\n", "    F.col(\"repo_name\").isin(eval_repo_names)\n", ")\n", "\n", "stage7pre_train_df = stage6_with_retrieval_df.exceptAll(stage7pre_eval_df)\n", "stage7pre_train_df = stage7pre_train_df.repartition(5000)\n", "# print out nubmer of problems in each\n", "# stage6_with_retrieval_df.select(F.sum(\"num_problems\")).show()\n", "stage7pre_eval_df.select(F.sum(\"num_problems\")).show()\n", "stage7pre_train_df.select(F.sum(\"num_problems\")).show()\n", "\n", "# randomly sample 1500 eval problems from the ~4500 problems found in the 250 hold our repos\n", "stage7pre_eval_df = stage7pre_eval_df.sample(\n", "    withReplacement=False, fraction=0.35, seed=0\n", ")\n", "stage7pre_eval_df.select(F.sum(\"num_problems\")).show()\n", "\n", "stage7pre_eval_df.write.parquet(stagepre7_eval_path)\n", "stage7pre_train_df.write.parquet(stagepre7_train_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(stage6_with_retrieval_path, stagepre7_eval_path, stagepre7_train_path)\n", "\n", "\n", "# stage6_with_retrieval_df = spark.read.parquet(stage6_with_retrieval_path)\n", "stage7pre_eval_df = spark.read.parquet(stagepre7_eval_path)\n", "# stage7pre_train_df = spark.read.parquet(stagepre7_train_path)\n", "\n", "# stage6_with_retrieval_df.select(F.sum(\"num_problems\")).show()\n", "stage7pre_eval_df.select(F.sum(\"num_problems\")).show()\n", "# stage7pre_train_df.select(F.sum(\"num_problems\")).show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stage7pre_eval_df_pd = stage7pre_eval_df.to<PERSON><PERSON>s()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Analyze sizes of logs and diffs\n", "\n", "from base.diff_utils.diff_utils import File\n", "from base.prompt_format_next_edit.common import NextEditPromptInput\n", "from base.prompt_format_next_edit.location_prompt_formatter import (\n", "    LocalizationNextEditPromptInput,\n", "    NextEditLocationQueryFormatter,\n", ")\n", "from research.next_edits.edit_localization_stages import CharRange\n", "from research.utils.repo_change_utils import (\n", "    FileTuple,\n", "    patchset_from_repo_change,\n", ")\n", "\n", "config = FormatAsTokensConfig(\n", "    query_formatter_config=autofix_config_no_truncation_query_formatter,\n", "    document_formatter_config=autofix_config[\"document_formatter\"],\n", "    min_diff_context_lines=3,\n", "    max_diff_context_lines=5,\n", "    tokenizer_name=\"starcoder\",\n", ")\n", "query_cfg_dct = dict(\n", "    # We initially set this to be the minimum number of context lines, but sample\n", "    # from the range [min, max] below.\n", "    diff_context_lines=config.min_diff_context_lines,\n", "    **config.query_formatter_config,\n", ")\n", "\n", "del query_cfg_dct[\"name\"]\n", "del query_cfg_dct[\"tokenizer\"]\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "query_formatter = NextEditLocationQueryFormatter(\n", "    tokenizer,\n", "    config=NextEditLocationQueryFormatter.Config(\n", "        **query_cfg_dct,\n", "    ),\n", ")\n", "\n", "\n", "def diff_size_in_tokens(df: pd.DataFrame):\n", "    problem_analysis_ls = []\n", "    for row in df.to_dict(orient=\"records\"):\n", "        problems: list[\n", "            tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]\n", "        ] = compressed_loads(row[\"pickled_results\"], compression=\"gzip\")\n", "        for problem, retrieval_info in problems:\n", "            file_changes = tuple(\n", "                change.map(FileTuple.to_file)\n", "                for change in problem.past_to_wip_repo_change.changed_files\n", "            )\n", "            prompt_input = LocalizationNextEditPromptInput(\n", "                current_file=File(\"\", \"\"),\n", "                edit_region=CharRange(0, 0),\n", "                instruction=\"\",\n", "                recent_changes=file_changes,\n", "            )\n", "            no_instructions_tokens = query_formatter.format_prompt(\n", "                prompt_input\n", "            ).tokens()\n", "\n", "            prompt_input = LocalizationNextEditPromptInput(\n", "                current_file=File(\"\", \"\"),\n", "                edit_region=CharRange(0, 0),\n", "                instruction=problem.instructions,\n", "                recent_changes=[],\n", "            )\n", "            only_instructions_tokens = query_formatter.format_prompt(\n", "                prompt_input\n", "            ).tokens()\n", "\n", "            problem_analysis_ls.append(\n", "                {\n", "                    \"num_diff_tokens\": len(no_instructions_tokens),\n", "                    \"num_instructions_tokens\": len(only_instructions_tokens),\n", "                    \"num_gold_chunks\": len(retrieval_info.positive_chunks),\n", "                }\n", "            )\n", "    return pd.DataFrame(problem_analysis_ls)\n", "\n", "\n", "if False:\n", "    stage7pre_eval_df_pd = stage7pre_eval_df.to<PERSON><PERSON>s()\n", "    df_analytics = diff_size_in_tokens(stage7pre_eval_df_pd)\n", "\n", "print(\"Summary stats:\")\n", "print(df_analytics.describe())\n", "print(\"\\n95th percentile:\")\n", "print(df_analytics.quantile(0.95))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 7: Format data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from typing import Any\n", "from research.utils.token_array_utils import TokenArray\n", "\n", "global_seed = 0\n", "\n", "\n", "def process_batch(df: pd.DataFrame, config):\n", "    ids_per_repo = dict[str, Any]()\n", "    output_per_repo = dict[\n", "        str,\n", "        list[\n", "            tuple[<PERSON><PERSON><PERSON><PERSON><PERSON>, EditLocalizationProblem, EditLocalizationRetrievalProblem]\n", "        ],\n", "    ]()\n", "    for row in df.to_dict(orient=\"records\"):\n", "        problems: list[\n", "            tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]\n", "        ] = compressed_loads(row[\"pickled_results\"], compression=\"gzip\")\n", "        assert len(problems) == row[\"num_problems\"]\n", "        repo_name = row[\"repo_name\"]\n", "        output_per_repo.setdefault(repo_name, []).extend(\n", "            format_as_tokens(config, problems, seed=global_seed + hash(row[\"id\"]))\n", "        )\n", "        ids_per_repo.setdefault(repo_name, hashlib.sha256()).update(row[\"id\"].encode())\n", "    return pd.DataFrame(\n", "        [\n", "            {\n", "                \"id\": f\"{repo_name}/{ids_per_repo[repo_name].hexdigest()}\",\n", "                \"repo_name\": repo_name,\n", "                \"num_problems\": len(output),\n", "                \"pickled_results\": compressed_dumps(output, compression=\"gzip\"),\n", "            }\n", "            for repo_name, output in output_per_repo.items()\n", "        ]\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting training data formatting without diff\n", "input_path= gs://gcp-us1-user/colin/bugfix_localization_model/v12/stagepre7_train, output_path= /mnt/efs/spark-data/user/colin/bugfix_localization_model/v12/stage7_train_format_as_tokens_no_diff\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 1:================================================>  (4780 + 220) / 5000]\r"]}], "source": ["from functools import partial\n", "\n", "config = FormatAsTokensConfig(\n", "    query_formatter_config=autofix_config[\"query_formatter\"],\n", "    document_formatter_config=autofix_config[\"document_formatter\"],\n", "    min_diff_context_lines=3,\n", "    max_diff_context_lines=5,\n", "    tokenizer_name=\"starcoder\",\n", ")\n", "\n", "if False:\n", "    print(\"Starting training data formatting with instructions\")\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_batch, config=config),\n", "        str(stagepre7_train_path),\n", "        str(stage7_train_format_as_tokens_path),\n", "        task_info_location=stage7_train_format_as_tokens_path + \".logs\",\n", "        batch_size=1,\n", "        input_columns=[\"id\", \"repo_name\", \"num_problems\", \"pickled_results\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "    )\n", "\n", "if False:\n", "    print(\"Starting eval data formatting with instructions\")\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_batch, config=config),\n", "        str(stagepre7_eval_path),\n", "        str(stage7_eval_format_as_tokens_path),\n", "        task_info_location=stage7_eval_format_as_tokens_path + \".logs\",\n", "        batch_size=1,\n", "        input_columns=[\"id\", \"repo_name\", \"num_problems\", \"pickled_results\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "    )\n", "\n", "# NOW NO INSTRUCTIONS\n", "\n", "config = FormatAsTokensConfig(\n", "    query_formatter_config=autofix_config_no_instructions_query_formatter,\n", "    document_formatter_config=autofix_config[\"document_formatter\"],\n", "    min_diff_context_lines=3,\n", "    max_diff_context_lines=5,\n", "    tokenizer_name=\"starcoder\",\n", ")\n", "\n", "if False:\n", "    print(\"Starting training data formatting without instructions\")\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_batch, config=config),\n", "        str(stagepre7_train_path),\n", "        str(stage7_train_format_as_tokens_path + \"_no_instructions\"),\n", "        task_info_location=stage7_train_format_as_tokens_path + \"_no_instructions.logs\",\n", "        batch_size=1,\n", "        input_columns=[\"id\", \"repo_name\", \"num_problems\", \"pickled_results\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "    )\n", "\n", "if False:\n", "    print(\"Starting eval data formatting without instructions\")\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_batch, config=config),\n", "        str(stagepre7_eval_path),\n", "        str(stage7_eval_format_as_tokens_path + \"_no_instructions\"),\n", "        task_info_location=stage7_eval_format_as_tokens_path + \"_no_instructions.logs\",\n", "        batch_size=1,\n", "        input_columns=[\"id\", \"repo_name\", \"num_problems\", \"pickled_results\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "    )\n", "\n", "# NOW NO DIFF\n", "\n", "config = FormatAsTokensConfig(\n", "    query_formatter_config=autofix_config_no_diff_query_formatter,\n", "    document_formatter_config=autofix_config[\"document_formatter\"],\n", "    min_diff_context_lines=3,\n", "    max_diff_context_lines=5,\n", "    tokenizer_name=\"starcoder\",\n", ")\n", "\n", "if True:\n", "    print(\"Starting training data formatting without diff\")\n", "    print(\n", "        f\"input_path= {stagepre7_train_path}, output_path= {stage7_train_format_as_tokens_path + '_no_diff'}\"\n", "    )\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_batch, config=config),\n", "        str(stagepre7_train_path),\n", "        str(stage7_train_format_as_tokens_path + \"_no_diff\"),\n", "        task_info_location=stage7_train_format_as_tokens_path + \"_no_diff.logs\",\n", "        batch_size=1,\n", "        input_columns=[\"id\", \"repo_name\", \"num_problems\", \"pickled_results\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "        allow_resume=True,\n", "    )\n", "\n", "if True:\n", "    print(\"Starting eval data formatting without diff\")\n", "    print(\n", "        f\"input_path={stagepre7_eval_path}, output_path={stage7_eval_format_as_tokens_path + '_no_diff'}\"\n", "    )\n", "    map_parquet.apply_pandas(\n", "        spark,\n", "        partial(process_batch, config=config),\n", "        str(stagepre7_eval_path),\n", "        str(stage7_eval_format_as_tokens_path + \"_no_diff\"),\n", "        task_info_location=stage7_eval_format_as_tokens_path + \"_no_diff.logs\",\n", "        batch_size=1,\n", "        input_columns=[\"id\", \"repo_name\", \"num_problems\", \"pickled_results\"],\n", "        timeout=3600,  # 1 hour is more than enough for this stage.\n", "        ignore_error=False,\n", "        timing_run=False,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Strange 8: Shu<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql.types import ArrayType\n", "\n", "input_output_rows = [\n", "    # (stage7_train_format_as_tokens_path, stage8_train_shuffled_path, 1000),\n", "    # (stage7_eval_format_as_tokens_path, stage8_eval_shuffled_path, 5),\n", "    # (stage7_train_format_as_tokens_path + \"_no_instructions\", stage8_train_shuffled_path + \"_no_instructions\", 1000),\n", "    # (stage7_eval_format_as_tokens_path + \"_no_instructions\", stage8_eval_shuffled_path + \"_no_instructions\", 5),\n", "    (\n", "        stage7_train_format_as_tokens_path + \"_no_diff\",\n", "        stage8_train_shuffled_path + \"_no_diff\",\n", "        1000,\n", "    ),\n", "    (\n", "        stage7_eval_format_as_tokens_path + \"_no_diff\",\n", "        stage8_eval_shuffled_path + \"_no_diff\",\n", "        5,\n", "    ),\n", "]\n", "for input_path, output_path, num_partitions in input_output_rows:\n", "    stage7_format_as_tokens_df = spark.read.parquet(input_path)\n", "    stage7_format_as_tokens_df = stage7_format_as_tokens_df.select(\n", "        \"repo_name\", \"pickled_results\"\n", "    )\n", "\n", "    def unpack_pickle(pickled_results):\n", "        # results type: list[tuple[<PERSON>kenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem]]\n", "        # map to: list[TokenArray]\n", "        pickled_results = compressed_loads(pickled_results, compression=\"gzip\")\n", "        return [compressed_dumps(x[0], compression=\"gzip\") for x in pickled_results]\n", "\n", "    unpack_pickle_udf = F.udf(unpack_pickle, returnType=ArrayType(BinaryType()))\n", "\n", "    stage7_format_as_tokens_df = stage7_format_as_tokens_df.withColumn(\n", "        \"pickled_results\", unpack_pickle_udf(<PERSON>.col(\"pickled_results\"))\n", "    )\n", "    stage7_format_as_tokens_df = stage7_format_as_tokens_df.withColumn(\n", "        \"pickled_results\", <PERSON><PERSON>explode(\"pickled_results\")\n", "    )\n", "    stage7_format_as_tokens_df = stage7_format_as_tokens_df.orderBy(\n", "        <PERSON><PERSON>rand()\n", "    ).repartition(num_partitions)\n", "    stage7_format_as_tokens_df.write.parquet(output_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Stage 9: Create indexed dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.pipelines.stages.next_edit_location_pipelines import (\n", "    run_create_indexed_dataset,\n", ")\n", "\n", "input_output_rows = [\n", "    # (stage8_train_shuffled_path, stage9_train_indexed_dataset_path),\n", "    # (stage8_eval_shuffled_path, stage9_eval_indexed_dataset_path),\n", "    # (stage8_train_shuffled_path + \"_no_instructions\", stage9_train_indexed_dataset_path + \"_no_instructions\"),\n", "    # (stage8_eval_shuffled_path + \"_no_instructions\", stage9_eval_indexed_dataset_path + \"_no_instructions\"),\n", "    (\n", "        stage8_train_shuffled_path + \"_no_diff\",\n", "        stage9_train_indexed_dataset_path + \"_no_diff\",\n", "    ),\n", "    (\n", "        stage8_eval_shuffled_path + \"_no_diff\",\n", "        stage9_eval_indexed_dataset_path + \"_no_diff\",\n", "    ),\n", "]\n", "\n", "for input_path, output_path in input_output_rows:\n", "    indexed_dataset_output_path = run_create_indexed_dataset(\n", "        input_path=input_path,\n", "        output_path=output_path,\n", "        tokenizer_name=\"starcoder\",\n", "        overwrite=True,\n", "        num_validation_samples=0,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "# load up indexed training dataset\n", "\n", "for suffix in [\"\", \"_no_instructions\", \"_no_diff\"]:\n", "    print(f\"For suffix: {suffix}\")\n", "    train_dataset = indexed_dataset.make_dataset(\n", "        stage9_train_indexed_dataset_path + f\"{suffix}/dataset\",\n", "        impl=\"mmap\",\n", "        skip_warmup=True,\n", "    )\n", "    eval_dataset = indexed_dataset.make_dataset(\n", "        stage9_eval_indexed_dataset_path + f\"{suffix}/dataset\",\n", "        impl=\"mmap\",\n", "        skip_warmup=True,\n", "    )\n", "    print(len(train_dataset))\n", "    print(len(eval_dataset))\n", "    print(\"-----\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer.detokenize(eval_dataset[0]).split(\"<|ret-endofquery|>\")[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_next_edit.gen_prompt_formatter_test import StarCoderTokenizer\n", "\n", "tokenizer = StarCoderTokenizer()\n", "for i in range(100):\n", "    print(\n", "        len(\n", "            tokenizer.tokenize_unsafe(\n", "                tokenizer.detokenize(eval_dataset[i]).split(\"<|ret-endofquery|>\")[0]\n", "            )\n", "        )\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stage 10: Convert eval dataset into eval task format"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example_data = read_jsonl_zst(\"/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst\")\n", "print(example_data[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example_data = read_jsonl_zst(\n", "    \"/mnt/efs/augment/data/eval/next_edits/prs.v7.diffs.jsonl.zst\"\n", ")\n", "example_data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example_data = read_jsonl_zst(\n", "    \"/mnt/efs/augment/data/eval/next_edits/prs.v7.files.jsonl.zst\"\n", ")\n", "example_data[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(stagepre7_eval_path)\n", "raw_eval_df = spark.read.parquet(stagepre7_eval_path).toPandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from hashlib import sha256\n", "import uuid\n", "\n", "from tqdm import tqdm\n", "from unidiff import UnidiffParseError\n", "\n", "from research.core.utils_for_file import write_json_zst, write_jsonl_zst\n", "\n", "sha_to_file = {}\n", "formatted_problems = []\n", "formatted_problems_diffs = []\n", "num_too_many_golds = 0\n", "for repo_problems in tqdm(raw_eval_df.to_dict(orient=\"records\")):\n", "    repo_diff_problems = []\n", "    repo_name = repo_problems[\"repo_name\"].replace(\".\", \"__\")\n", "    repo_name = repo_name.replace(\"/\", \"-_-\")\n", "    group_id = repo_name\n", "    i = 0\n", "    for problem_data in compressed_loads(\n", "        repo_problems[\"pickled_results\"], compression=\"gzip\"\n", "    ):\n", "        localization_problem, retrieval_problem = problem_data\n", "        localization_problem: EditLocalizationProblem\n", "        retrieval_problem: EditLocalizationRetrievalProblem\n", "\n", "        if len(retrieval_problem.positive_chunks) > 5:\n", "            num_too_many_golds += 1\n", "            continue\n", "\n", "        id = str(uuid.uuid4())\n", "        try:\n", "            past_to_wip_diff = str(\n", "                patchset_from_repo_change(\n", "                    localization_problem.past_to_wip_repo_change, num_context_lines=0\n", "                )\n", "            )\n", "            wip_to_future_diff = str(\n", "                patchset_from_repo_change(\n", "                    localization_problem.wip_to_future_repo_change, num_context_lines=0\n", "                )\n", "            )\n", "        except UnidiffParseError as e:\n", "            print(f\"Error parsing diff for {id}: {e}\")\n", "            continue\n", "\n", "        formatted_problems.append(\n", "            dict(\n", "                id=id,\n", "                instruction=localization_problem.instructions,\n", "                past_to_wip_diff=past_to_wip_diff,\n", "                wip_to_future_diff=wip_to_future_diff,\n", "                wip_files=[\n", "                    {\n", "                        \"path\": str(path),\n", "                        \"contents\": text,\n", "                    }\n", "                    for path, text in localization_problem.past_to_wip_repo_change.after_files.items()\n", "                ],\n", "            )\n", "        )\n", "\n", "        repo_diff_problems.append(\n", "            dict(\n", "                id=id,\n", "                instruction=localization_problem.instructions,\n", "                past_to_wip_diff=past_to_wip_diff,\n", "                wip_to_future_diff=wip_to_future_diff,\n", "                wip_files=[\n", "                    sha256(text.encode()).hexdigest()\n", "                    for _, text in localization_problem.past_to_wip_repo_change.after_files.items()\n", "                ],\n", "                commit_meta={\"sha\": \"\", \"message\": \"\", \"repo_name\": repo_name},\n", "                group_id=group_id,\n", "                group_sequence_id=i,\n", "            )\n", "        )\n", "\n", "        # increment group sequence id after successfully processing a row\n", "        i += 1\n", "\n", "        for (\n", "            path,\n", "            text,\n", "        ) in localization_problem.past_to_wip_repo_change.after_files.items():\n", "            sha_to_file[sha256(text.encode()).hexdigest()] = (str(path), text)\n", "    formatted_problems_diffs.append(\n", "        {\n", "            \"data\": repo_diff_problems,\n", "            \"group_id\": group_id,\n", "        }\n", "    )\n", "\n", "formatted_files = [\n", "    dict(\n", "        id=shaval,\n", "        path=path,\n", "        contents=text,\n", "    )\n", "    for shaval, (path, text) in sha_to_file.items()\n", "]\n", "\n", "print(f\"num_too_many_golds: {num_too_many_golds}\")\n", "\n", "ver_suffix = \"_filtered\"\n", "write_jsonl_zst(\n", "    f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix}.jsonl.zst\",\n", "    formatted_problems,\n", ")\n", "write_jsonl_zst(\n", "    f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix}.diffs.jsonl.zst\",\n", "    formatted_problems_diffs,\n", ")\n", "write_jsonl_zst(\n", "    f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix}.files.jsonl.zst\",\n", "    formatted_files,\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for item in read_jsonl_zst(\n", "    f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION}.jsonl.zst\"\n", "):\n", "    print(item[\"instruction\"][:1000].replace(\"\\n\", \"\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    read_jsonl_zst(\n", "        f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION}.diffs.jsonl.zst\"\n", "    )[0][\"data\"][0][\"past_to_wip_diff\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    read_jsonl_zst(\n", "        f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION}.diffs.jsonl.zst\"\n", "    )[2][\"data\"][0][\"past_to_wip_diff\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["read_jsonl_zst(f\"/mnt/efs/augment/data/eval/autofix/commits-{VERSION}.files.jsonl.zst\")[\n", "    0\n", "]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}