from research.autofix.logs_extractor import LogExtractor
from pathlib import Path
from research.utils.repo_change_utils import <PERSON>o<PERSON><PERSON><PERSON>, FileTuple
from research.core.changes import Added, Modified, Deleted

# Create dummy before and after files
before_files = {
    Path("file1.py"): "def hello():\n    print('Hello')\n",
    Path("file2.py"): "def world():\n    print('World')\n",
    Path("file3.py"): "# This file will be deleted\n",
}

after_files = {
    Path("file1.py"): "def hello():\n    print('Hello, World!')\n",
    Path("file2.py"): "def world():\n    print('World')\n",
    Path("file4.py"): "# This is a new file\n",
}

# Create changed_files list
changed_files = [
    Modified(
        FileTuple(Path("file1.py"), "def hello():\n    print('Hello')\n"),
        FileTuple(Path("file1.py"), "def hello():\n    print('Hello, World!')\n"),
    ),
    Deleted(FileTuple(Path("file3.py"), "# This file will be deleted\n")),
    Added(FileTuple(Path("file4.py"), "# This is a new file\n")),
]

dummy_repo_change = RepoChange.build(
    before_files=before_files, after_files=after_files, changed_files=changed_files
)

log_extractor = LogExtractor()
log_extractor.extract("this is a log", dummy_repo_change)
