from functools import partial
import json
from pathlib import Path
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
import pandas as pd
from research.autofix.logs_extractor import LogExtractor
from experimental.colin.projects.autofix.training.data_pipelines.utils import (
    create_stage_logger,
    save_config_to_stage_output_dir,
)
from research.data.spark.pipelines.utils import map_parquet
from unidiff.errors import UnidiffParseError
import logging
import pyspark.sql.functions as F

from research.data.spark.utils import AugmentK8sSparkSession

logger = logging.getLogger(__name__)


def log_extractor_row(problems, log_extractor: LogExtractor):
    problems = compressed_loads(problems, compression="gzip")
    for problem in problems:
        try:
            problem.logs = [
                log_extractor.extract(
                    problem.logs[0], problem.breaking_change, batch_size=4
                )
            ]
        except UnidiffParseError:
            logger.info(f"Error parsing diff for row: {problem.logs_sha}")

    return compressed_dumps(problems, compression="gzip")


def log_extractor_batch(batch):
    global cached_log_extractor
    cached_log_extractor = LogExtractor()
    batch["problems"] = [
        log_extractor_row(row["problems"], cached_log_extractor)
        for _, row in batch.iterrows()
    ]
    return batch


def stage5_extract_logs(
    spark: AugmentK8sSparkSession,
    data_path: str,
    output_path: str,
    logs_output_base: str,
    allow_resume: bool = True,
    num_partitions: int = 500,
):
    config = {
        "data_path": data_path,
        "output_path": output_path,
        "allow_resume": allow_resume,
        "logs_output_base": logs_output_base,
    }
    save_config_to_stage_output_dir(output_path, config)

    logger = create_stage_logger(__name__, output_path, logs_output_base)
    logger.info(
        f"Starting extract logs at {pd.Timestamp.now(tz='US/Pacific')}."
        f"Reading from {data_path} and writing to {output_path}."
        f"\nConfig: {json.dumps(config, indent=4)}"
    )

    output_input_partition_count_path = output_path.rstrip("/") + "_before_repartition"

    map_parquet.apply_pandas(
        spark,
        log_extractor_batch,
        input_path=data_path,
        output_path=output_input_partition_count_path,
        input_columns=["repo_id", "problems", "num_problems"],
        timeout=60 * 60 * 3,  # 3 hour is more than enough for this stage.
        ignore_error=False,
        timing_run=False,
        allow_resume=allow_resume,
    )

    df = spark.read.parquet(output_input_partition_count_path)
    # FYI: Coalesce assumes we are reducing the number of partitions (eg 5000 -> 500)
    df = df.coalesce(num_partitions)
    df.write.parquet(output_path)

    num_problems = df.select(F.sum("num_problems")).collect()[0][0]
    logger.info(
        f"Summary stats for stage5_extract_logs:\n" f"num_problems: {num_problems}\n"
    )
