

from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir, save_file
from research.data.spark.utils import AugmentK8sSparkSession
import pyspark.sql.functions as F
from pyspark.sql.types import BinaryType
from compress_pickle import dumps as compressed_dumps

import pandas as pd
import logging

logger = logging.getLogger(__name__)

def stage0_grouped_commits(
    spark: AugmentK8sSparkSession, 
    pr_inter_commits_path: str, 
    pr_check_runs_path: str, 
    pr_failed_logs_path: str, 
    stage0_path: str,
    logs_output_base: str,
):
    logger = create_stage_logger(__name__, stage0_path, logs_output_base)
    logger.info(
        f"Starting stage0_grouped_commits at {pd.Timestamp.now(tz='US/Pacific')}. Reading from {pr_inter_commits_path}"
        f" and {pr_check_runs_path} and {pr_failed_logs_path}"
        f" and writing to {stage0_path}."
    )

    config = {
        "pr_inter_commits_path": pr_inter_commits_path,
        "pr_check_runs_path": pr_check_runs_path,
        "pr_failed_logs_path": pr_failed_logs_path,
        "stage0_path": stage0_path,
        "logs_output_base": logs_output_base,
    }
    save_config_to_stage_output_dir(stage0_path, config)

    commit_metadata_df = spark.read.parquet(pr_inter_commits_path)
    commit_metadata_df = commit_metadata_df.withColumn(
        "repo_ID", F.concat(F.col("owner"), F.lit("/"), F.col("name"))
    )
    #commit_metadata_df = commit_metadata_df.filter(F.size(F.col("parents")) == 1)
    #commit_metadata_df = commit_metadata_df.withColumn("parents", F.col("parents")[0])
    #commit_metadata_df = commit_metadata_df.withColumnRenamed("parents", "parent")
    commit_metadata_df = commit_metadata_df.dropDuplicates(["repo_ID", "sha"])

    cicd_metadata_df = spark.read.parquet(pr_check_runs_path)
    cicd_metadata_df = cicd_metadata_df.withColumn(
        "repo_ID", F.concat(F.col("owner"), F.lit("/"), F.col("name"))
    )
    cicd_metadata_df = cicd_metadata_df.select([col for col in cicd_metadata_df.columns if col not in ['owner', 'name', 'pr_number']])
    cicd_metadata_df = cicd_metadata_df.withColumnRenamed("commit_sha", "sha")
    cicd_metadata_df = cicd_metadata_df.groupBy("repo_ID", "sha").agg(
        F.collect_list(F.struct("conclusion", 'check_run_name')).alias("conclusion_list")
    )
    cicd_metadata_df = cicd_metadata_df.dropDuplicates(["repo_ID", "sha"])

    failed_logs_df = spark.read.parquet(pr_failed_logs_path)
    failed_logs_df = failed_logs_df.filter(F.col("status_code").isin([200, 206]))
    failed_logs_df = failed_logs_df.withColumn(
        "repo_ID", F.concat(F.col("owner"), F.lit("/"), F.col("name"))
    )
    failed_logs_df = failed_logs_df.select([col for col in failed_logs_df.columns if col not in ['owner', 'name', 'pr_number']])
    failed_logs_df = failed_logs_df.withColumnRenamed("commit_sha", "sha")

    # update log column to empty string
    failed_logs_df = failed_logs_df.withColumn("log", F.lit(""))

    grouped_failed_logs_df = failed_logs_df.groupBy("repo_ID", "sha").agg(
        F.collect_list(F.struct(failed_logs_df.columns)).alias("all_logs")
    )

    commit_full_metadata_df = commit_metadata_df.join(
        cicd_metadata_df, on=["repo_ID", "sha"], how="outer"
    )
    commit_full_metadata_df = commit_full_metadata_df.join(
        grouped_failed_logs_df, on=["repo_ID", "sha"], how="outer"
    )

    # FILTER
    #window_spec = Window.partitionBy(["repo_ID", "sha"]).orderBy(F.rand())
    #commit_full_metadata_df_with_row_num = commit_full_metadata_df.withColumn("row_number", row_number().over(window_spec))
    #commit_full_metadata_df_top10_000 = commit_full_metadata_df_with_row_num.filter(col("row_number") <= 2_500).drop("row_number")
    # END FILTER


    grouped_commit_full_metadata_df = commit_full_metadata_df.groupBy(
        "repo_ID"
    ).agg(F.collect_list(F.struct(commit_full_metadata_df.columns)).alias("commit_metadata_ls"))

    def compress_commits(commit_metadata_list):
        return compressed_dumps(commit_metadata_list, compression="gzip")

    grouped_commit_full_metadata_df = grouped_commit_full_metadata_df.withColumn(
        "commit_metadata_ls",
        F.udf(compress_commits, BinaryType())(F.col("commit_metadata_ls")),
    )

    grouped_commit_full_metadata_df.write.parquet(stage0_path)

    logger.info(f"Finished stage0_grouped_commits at {pd.Timestamp.now(tz='US/Pacific')}. Wrote to {stage0_path}.")

    num_output_rows = grouped_commit_full_metadata_df.count()
    num_commits = commit_full_metadata_df.count()
    logger.info(
        f"Summary stats for stage0_grouped_commits:\n"
        f"num output rows // num repos: {num_output_rows}"
        f"num_commits: {num_commits}"
    )

