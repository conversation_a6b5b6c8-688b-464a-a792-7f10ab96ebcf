

import json
from research.data.spark.utils import AugmentK8sSparkSession
from pyspark.sql import functions as F
from pyspark.sql.functions import explode
from pyspark.sql.types import ArrayType, BinaryType, IntegerType
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, gcp_path_to_file_path, save_config_to_stage_output_dir
import pandas as pd
import logging

logger = logging.getLogger(__name__)


def stage3_explode_problems(
    spark: AugmentK8sSparkSession,
    stage2_path: str,
    stage3_path: str,
    logs_output_base: str
):
    config = {
        "stage2_path": stage2_path,
        "stage3_path": stage3_path,
        "logs_output_base": logs_output_base,
    }
    save_config_to_stage_output_dir(stage3_path, config)

    logger = create_stage_logger(__name__, stage3_path, logs_output_base)
    logger.info(
        f"Starting exploding problems at {pd.Timestamp.now(tz='US/Pacific')}."
        f"Reading from {stage2_path} and writing to {stage3_path}."
        f"\nConfig: {json.dumps(config, indent=4)}"
    )

    df = spark.read.parquet(stage2_path)

    logger.info("Loaded data.")
    num_input_rows = df.count()
    def recompress(problems):
        """Break up problems into lists of 5"""
        problems = compressed_loads(problems, compression="gzip")
        new_problems = []
        for i in range(0, len(problems), 5):
            new_problems.append(compressed_dumps(problems[i:i+5], compression="gzip"))
        return new_problems

    df = df.withColumn('problems', F.udf(recompress, returnType=ArrayType(BinaryType()))(F.col('problems')))
    df = df.drop('num_problems')
    df = df.withColumn('problems', explode('problems'))

    logger.info("Exploded problems.")

    def count_num_problems(problems):
        return len(compressed_loads(problems, compression="gzip"))
    df = df.withColumn('num_problems', F.udf(count_num_problems, returnType=IntegerType())(F.col('problems')))

    df = df.repartition(1000, "repo_ID")
    df.write.parquet(stage3_path)

    logger.info(f"Finished exploding problems at {pd.Timestamp.now(tz='US/Pacific')}. Wrote to {stage3_path}.")
    num_output_rows = df.count()
    num_problems = df.select(F.sum('num_problems')).collect()[0][0]
    logger.info(
        f"Summary stats for exploding problems:\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_output_rows: {num_output_rows}\n"
        f"num_problems: {num_problems}"
    )
