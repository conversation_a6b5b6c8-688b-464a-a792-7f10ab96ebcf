
import json
from compress_pickle import dumps as compressed_dumps, loads as compressed_loads
import pandas as pd
from experimental.colin.projects.autofix.training.data_pipelines.utils import save_config_to_stage_output_dir, create_stage_logger
from research.data.spark.pipelines.stages.next_edit_location_pipelines import (
    run_create_indexed_dataset,
)
import time
from research.data.spark.utils import AugmentK8sSparkSession
from pyspark.sql.types import BinaryType, ArrayType
from pyspark.sql import functions as F
from megatron.data import indexed_dataset

def stage9_shuffle_and_export(
    spark: AugmentK8sSparkSession,
    stage8_train_format_as_tokens_path: str,
    stage8_eval_format_as_tokens_path: str,
    stage9_train_indexed_dataset_path: str,
    stage9_eval_indexed_dataset_path: str,
    generate_with_everything_version: bool,
    generate_no_instructions_version: bool,
    generate_no_diff_version: bool,
    analytics_only: bool = False,
):
    dataset_variants = []
    if generate_with_everything_version:  
        dataset_variants.append("")
    if generate_no_instructions_version:
        dataset_variants.append("_no_instructions")
    if generate_no_diff_version:
        dataset_variants.append("_no_diff")

    config = {
        "stage8_train_format_as_tokens_path": stage8_train_format_as_tokens_path,
        "stage8_eval_format_as_tokens_path": stage8_eval_format_as_tokens_path,
        "stage9_train_indexed_dataset_path": stage9_train_indexed_dataset_path,
        "stage9_eval_indexed_dataset_path": stage9_eval_indexed_dataset_path,
        "generate_with_everything_version": generate_with_everything_version,
        "generate_no_instructions_version": generate_no_instructions_version,
        "generate_no_diff_version": generate_no_diff_version,
        "dataset_variants": dataset_variants,
    }
    save_config_to_stage_output_dir(stage9_train_indexed_dataset_path, config)

    logger = create_stage_logger(__name__, stage9_train_indexed_dataset_path)
    logger.info(
        f"Starting shuffle at {pd.Timestamp.now(tz='US/Pacific')}. Reading from {stage8_train_format_as_tokens_path} and"
        f"{stage8_eval_format_as_tokens_path} and writing to {stage9_train_indexed_dataset_path} and {stage9_eval_indexed_dataset_path}."
        f"Also including the variants {dataset_variants}."
        f"\nConfig: {json.dumps(config, indent=4)}"
    )

    for variant in dataset_variants:
        for input_path, output_path in [
            (stage8_train_format_as_tokens_path + variant, stage9_train_indexed_dataset_path + variant),
            (stage8_eval_format_as_tokens_path + variant, stage9_eval_indexed_dataset_path + variant),
        ]:
            logger.info(f"Processing {input_path} writing to {output_path}")
            if not analytics_only:
                input_df = spark.read.parquet(input_path)
                input_df = input_df.select(
                    "repo_name", "pickled_results"
                )

                def unpack_pickle(pickled_results):
                    """Map list[tuple[TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem]] -> list[TokenArray]."""
                    pickled_results = compressed_loads(pickled_results, compression="gzip")
                    return [compressed_dumps(x[0], compression="gzip") for x in pickled_results]


                unpack_pickle_udf = F.udf(unpack_pickle, returnType=ArrayType(BinaryType()))
                input_df = input_df.withColumn(
                    "pickled_results", unpack_pickle_udf(F.col("pickled_results"))
                )
                input_df = input_df.withColumn(
                    "pickled_results", F.explode("pickled_results")
                )
                input_df = input_df.orderBy(F.rand())
                input_df.write.parquet(output_path+"_INTERMEDIATE")

                logger.info(f"Sleeping for 3 minutes to let the shuffle finish...")
                time.sleep(60*3)
                logger.info(f"Done sleeping.")

                indexed_dataset_output_path = run_create_indexed_dataset(
                    input_path=(output_path+"_INTERMEDIATE").replace("gs://gcp-us1-spark-data", "/mnt/efs/spark-data"),
                    output_path=output_path.replace("gs://gcp-us1-spark-data", "/mnt/efs/spark-data"),
                    tokenizer_name="starcoder",
                    overwrite=True,
                    num_validation_samples=0,
                )
            else:
                logger.info(f"Skipping creating indexed dataset for {input_path} and {output_path} because analytics_only is True.")

            output_dataset = indexed_dataset.make_dataset(
                (output_path + f"/dataset").replace("gs://gcp-us1-spark-data", "/mnt/efs/spark-data"), impl="mmap", skip_warmup=True
            )

            num_examples = len(output_dataset)
            logger.info(f"Indexed dataset  at {output_path} has {num_examples} examples.")
