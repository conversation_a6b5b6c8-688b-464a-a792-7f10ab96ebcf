import json
from typing import Any
from compress_pickle import dumps as compressed_dumps, loads as compressed_loads
from tqdm import tqdm
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, run_with_timeout, save_config_to_stage_output_dir, ls_dir
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import AugmentK8sSparkSession
from research.next_edits.edit_localization_stages import FormatAsTokensConfig, format_as_tokens
import hashlib
from functools import partial
import pandas as pd
from research.next_edits.edit_localization_stages import EditLocalizationProblem, EditLocalizationRetrievalProblem
from research.utils.token_array_utils import TokenArray
import logging
import pyspark.sql.functions as F
from pyspark.sql.types import IntegerType

global_seed = 0
logger = logging.getLogger(__name__)

def process_batch_with_timeout(df: pd.DataFrame, config):
    return run_with_timeout(process_batch, args=(df, config), timeout_duration=45)


def process_batch(df: pd.DataFrame, config):
    ids_per_repo = dict[str, Any]()
    output_per_repo = dict[str, list[tuple[TokenArray, EditLocalizationProblem, EditLocalizationRetrievalProblem]]]()
    for row in df.to_dict(orient="records"):
        problems: list[
            tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]
        ] = compressed_loads(row["pickled_results"], compression="gzip")
        # assert len(problems) == row["num_problems"]
        repo_name = row["repo_name"]
        output_per_repo.setdefault(repo_name, []).extend(
            format_as_tokens(config, problems, seed=global_seed + hash(row["id"]))
        )
        ids_per_repo.setdefault(repo_name, hashlib.sha256()).update(row["id"].encode())
    return pd.DataFrame(
        [
            {
                "id": f"{repo_name}/{ids_per_repo[repo_name].hexdigest()}",
                "repo_name": repo_name,
                "num_problems": len(output),
                "pickled_results": compressed_dumps(output, compression="gzip"),
            }
            for repo_name, output in output_per_repo.items()
        ]
    )

def stage8_format_data(
    spark: AugmentK8sSparkSession,
    stage7_train_path: str,
    stage7_eval_path: str,
    stage8_train_format_as_tokens_path: str,
    stage8_eval_format_as_tokens_path: str,
    formatter_config: dict,
    min_diff_context_lines: int,
    max_diff_context_lines: int,
    tokenizer_name: str,
    generate_with_everything_version: bool,
    generate_no_instructions_version: bool,
    generate_no_diff_version: bool,
    analytics_only: bool = False,
    run_local: bool = False,
):
    config = {
        "stage7_train_path": stage7_train_path,
        "stage7_eval_path": stage7_eval_path,
        "stage8_train_format_as_tokens_path": stage8_train_format_as_tokens_path,
        "stage8_eval_format_as_tokens_path": stage8_eval_format_as_tokens_path,
        "formatter_config": formatter_config,
        "min_diff_context_lines": min_diff_context_lines,
        "max_diff_context_lines": max_diff_context_lines,
        "tokenizer_name": tokenizer_name,
        "generate_with_everything_version": generate_with_everything_version,
        "generate_no_instructions_version": generate_no_instructions_version,
        "generate_no_diff_version": generate_no_diff_version,
    }
    save_config_to_stage_output_dir(stage8_train_format_as_tokens_path, config)

    logger = create_stage_logger(__name__, stage8_train_format_as_tokens_path)
    logger.info(
        f"Starting format data at {pd.Timestamp.now(tz='US/Pacific')}."
        f"Reading from {stage7_train_path} and {stage7_eval_path} and writing to {stage8_train_format_as_tokens_path} and {stage8_eval_format_as_tokens_path}."
        f"\nConfig: {json.dumps(config, indent=4)}"
    )

    formatter_config_no_instructions_query_formatter = {**formatter_config["query_formatter"]}
    formatter_config_no_instructions_query_formatter["max_instruction_tokens"] = 0

    formatter_config_no_diff_query_formatter = {**formatter_config["query_formatter"]}
    formatter_config_no_diff_query_formatter["max_diff_tokens"] = 0

    query_formatters_to_process = []
    if generate_with_everything_version:
        query_formatters_to_process.append((formatter_config["query_formatter"], ""))
    if generate_no_instructions_version:
        query_formatters_to_process.append((formatter_config_no_instructions_query_formatter, "_no_instructions"))
    if generate_no_diff_version:
        query_formatters_to_process.append((formatter_config_no_diff_query_formatter, "_no_diff"))

    for query_formatter_config, suffix in query_formatters_to_process:
        config = FormatAsTokensConfig(
            query_formatter_config=query_formatter_config,
            document_formatter_config=formatter_config["document_formatter"],
            min_diff_context_lines=min_diff_context_lines,
            max_diff_context_lines=max_diff_context_lines,
            tokenizer_name=tokenizer_name,
            downsample_commits_hunk_threshold=-1,
            drop_instruction_rate=0.,
        )

        num_input_parquet_files = len(ls_dir(stage7_train_path))
        num_output_parquet_files = len(ls_dir(str(stage8_train_format_as_tokens_path) + suffix))

        if not analytics_only:
            if num_output_parquet_files / num_input_parquet_files > 0.975: 
                logger.info(f"More than 97.5% of eval files processed, skipping the rest because they are probably really slow.")
            else:
                logger.info(f'Starting training data formatting for suffix {suffix}. Finding that num_output_parquet_files/num_input_parquet_files = {num_output_parquet_files/num_input_parquet_files} < 0.975.')
                map_parquet.apply_pandas(
                    spark,
                    partial(process_batch_with_timeout, config=config),
                    str(stage7_train_path),
                    str(stage8_train_format_as_tokens_path) + suffix,
                    batch_size=1,
                    input_columns=["id", "repo_name", "num_problems", "pickled_results"],
                    timeout=3600 * 4,  # 4 hour is more than enough for this stage.
                    allow_resume=True,
                    ignore_error=False,
                    timing_run=False,
                )

            logger.info(f'Starting eval data formatting for suffix {suffix}')
            if run_local:
                logger.info("Running locally and there are")
                df = spark.read.parquet(stage7_eval_path)
                num_rows = df.count()
                num_unique_repos = df.select(F.countDistinct("repo_name")).collect()[0][0]
                logger.info(f"Running locally and there are {num_rows} rows.")
                logger.info(f"Running locally and there are {num_unique_repos} unique repos.")
                df = df.sample(withReplacement=False, fraction=0.25, seed=42)
                num_unique_repos_after_sample = df.select(F.countDistinct("repo_name")).collect()[0][0]
                num_rows_after_sample = df.count()
                num_problems = df.select(F.sum("num_problems")).collect()[0][0]
                logger.info(f"Running locally and there are {num_rows_after_sample} rows after sampling.")
                logger.info(f"Running locally and there are {num_unique_repos_after_sample} unique repos after sampling.")
                logger.info(f"Running locally and there are {num_problems} problems after sampling.")
                rows: list[pd.DataFrame] = []
                i = 0
                for row in tqdm(df.toLocalIterator(prefetchPartitions=True), total=num_rows_after_sample):
                    df_singlerow = pd.DataFrame([pd.Series(row.asDict())])
                    rows.append(process_batch_with_timeout(df_singlerow, config))
                    i += 1
                    if i % 50 == 0:
                        df_out = pd.concat(rows)
                        df_out.to_parquet(stage8_eval_format_as_tokens_path + suffix + f"/part{i}.zstd.parquet", compression="zstd")
                        logger.info(f"Wrote 50 rows to {stage8_eval_format_as_tokens_path + suffix + f'/part{i}.zstd.parquet'}")
                        rows = []
                if len(rows) > 0:
                    df_out = pd.concat(rows)
                    df_out.to_parquet(stage8_eval_format_as_tokens_path + suffix + f"/part{i}.zstd.parquet", compression="zstd")
            else:
                map_parquet.apply_pandas(
                    spark,
                    partial(process_batch_with_timeout, config=config),
                    str(stage7_eval_path),
                    str(stage8_eval_format_as_tokens_path) + suffix,
                    batch_size=1,
                    input_columns=["id", "repo_name", "num_problems", "pickled_results"],
                    timeout=3600 * 2,  # 2 hour is more than enough for this stage.
                    allow_resume=True,
                    ignore_error=False,
                    timing_run=False,
                )

            logger.info(f"Finished stage8_format_data at {pd.Timestamp.now(tz='US/Pacific')}.")
        else:
            logger.info(f"Skipping stage8_format_data processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

        input_train_df = spark.read.parquet(stage7_train_path)
        input_eval_df = spark.read.parquet(stage7_eval_path)

        num_input_train_rows = input_train_df.count()
        num_input_eval_rows = input_eval_df.count()
        num_input_train_problems = input_train_df.select(F.sum("num_problems")).collect()[0][0]
        num_input_eval_problems = input_eval_df.select(F.sum("num_problems")).collect()[0][0]

        train_output_df = spark.read.parquet(stage8_train_format_as_tokens_path + suffix)
        eval_output_df = spark.read.parquet(stage8_eval_format_as_tokens_path + suffix)

        num_train_rows = train_output_df.count()
        num_eval_rows = eval_output_df.count()
        num_train_problems = train_output_df.select(F.sum("num_problems")).collect()[0][0]
        num_eval_problems = eval_output_df.select(F.sum("num_problems")).collect()[0][0]
        logger.info(
            f"Summary stats for stage8_format_data:\n"
            f"num_input_train_rows: {num_input_train_rows}\n"
            f"num_input_eval_rows: {num_input_eval_rows}\n"
            f"num_input_train_problems: {num_input_train_problems}\n"
            f"num_input_eval_problems: {num_input_eval_problems}\n"
            f"num_train_rows: {num_train_rows}\n"
            f"num_eval_rows: {num_eval_rows}\n"
            f"num_train_problems: {num_train_problems}\n"
            f"num_eval_problems: {num_eval_problems}\n"
        )
