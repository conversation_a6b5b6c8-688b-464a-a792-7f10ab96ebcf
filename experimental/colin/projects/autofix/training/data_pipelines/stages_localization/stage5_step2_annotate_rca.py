from functools import partial
import json
from pathlib import Path
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
import pandas as pd
from research.data.spark.utils import AugmentK8sSparkSession
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.data.spark.pipelines.utils import map_parquet
import pyspark.sql.functions as F
from pyspark.sql.types import BinaryType

def annotate_rca_udf(repo_id, problems, root_causes_with_shas):
    if root_causes_with_shas is None:
        return pd.Series(
            {
                "repo_id": repo_id,
                "problems": problems,
                "num_problems": 0,
                "error": "root_causes_with_shas is None"
            }
        )
    if isinstance(root_causes_with_shas, str):
        return pd.Series(
            {
                "repo_id": repo_id,
                "problems": problems,
                "num_problems": 0,
                "error": f"root_causes_with_shas is str: {root_causes_with_shas}"
            }
        )
    problems = compressed_loads(problems, compression="gzip")
    root_causes_with_shas = compressed_loads(root_causes_with_shas, compression="gzip")
    logs_sha_to_rootcause = {sha: root_cause for sha, root_cause in root_causes_with_shas}
    new_problems = []
    for problem in problems:
        root_cause = logs_sha_to_rootcause.get(problem.logs_sha, None)
        if root_cause is not None:
            problem.rca = root_cause
            new_problems.append(problem)

    return pd.Series(
        {
            "repo_id": repo_id,
            "problems": compressed_dumps(new_problems, compression="gzip"),
            "num_problems": len(new_problems),
            "error": ""
        }
    )
    

def stage5_step2_annotate_rca(
    spark: AugmentK8sSparkSession,
    input_data: str,
    rca_data: str,
    output_path: str,
    logs_output_base: str,
    allow_resume: bool = True,
    analytics_only: bool = False,
):
    logger = create_stage_logger(__name__, output_path, logs_output_base)

    if not analytics_only:
        config = {
            "input_data": input_data,
            "rca_data": rca_data,
            "output_path": output_path,
            "allow_resume": allow_resume,
            "logs_output_base": logs_output_base,
        }
        save_config_to_stage_output_dir(output_path, config)

        
        logger.info(
            f"Starting extract logs at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {input_data} and {rca_data} and writing to {output_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"    
        )

        input_df = spark.read.parquet(input_data)
        rca_df = spark.read.parquet(rca_data)

        # this sha is the "logs_sha"
        rca_df = rca_df.select("repo_id", "sha", "root_cause")
        rca_df = rca_df.groupBy("repo_id").agg(
            F.collect_list(F.struct("sha", "root_cause")).alias("root_causes_with_shas")
        )
        # compress root_causes_with_shas
        rca_df = rca_df.withColumn(
            "root_causes_with_shas",
            F.udf(lambda x: compressed_dumps(x, compression="gzip") if x is not None else None, BinaryType())(F.col("root_causes_with_shas")),
        )

        joined_data = input_df.join(rca_df, on="repo_id", how="left")
        joined_data = joined_data.repartition(500)
        joined_data.write.parquet(output_path + "_INTERMEDIARY")

        logger.info(f"Finished writing intermediary data to {output_path + '_INTERMEDIARY'} at {pd.Timestamp.now(tz='US/Pacific')}.")

        map_parquet.apply(
            spark,
            annotate_rca_udf,
            input_path=output_path + "_INTERMEDIARY",
            output_path=output_path,
            input_columns=["repo_id", "problems", "root_causes_with_shas"],
            batch_size=1,
            timeout=50_000,
            ignore_error=False,
            allow_resume=allow_resume,
        )
        logger.info(f"Finished stage5_step2_annotate_rca at {pd.Timestamp.now(tz='US/Pacific')}.")
    else:
        logger.info(f"Skipping stage5_step2_annotate_rca processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    input_df = spark.read.parquet(input_data)
    rca_df = spark.read.parquet(rca_data)
    output_df = spark.read.parquet(output_path)
    num_input_rows = input_df.count()
    num_input_problems = input_df.select(F.sum('num_problems')).collect()[0][0]
    num_rca_rows = rca_df.count()
    num_output_rows = output_df.count()
    num_problems = output_df.select(F.sum('num_problems')).collect()[0][0]
    num_errors = output_df.filter(F.col('error') != '').count()
    num_none_errors = output_df.filter(F.col('error') == 'root_causes_with_shas is None').count()

    # grab some example errors that are not None
    example_errors = output_df.filter(F.col('error') != 'root_causes_with_shas is None')
    example_errors = example_errors.filter(F.col('error') != '')
    example_errors = example_errors.limit(5).toPandas()['error'].tolist()

    logger.info(
        f"Summary stats for stage5_step2_annotate_rca:\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_input_problems: {num_input_problems}\n"
        f"num_input_rca_rows: {num_rca_rows}\n"
        f"num_output_rows: {num_output_rows}\n"
        f"num_problems: {num_problems}\n"
        f"num_errors: {num_errors}\n"
        f"num_none_errors: {num_none_errors}\n"
    )
    for error in example_errors:
        logger.info(f"Example error (500 chars): {error[:500]}")

   

   
