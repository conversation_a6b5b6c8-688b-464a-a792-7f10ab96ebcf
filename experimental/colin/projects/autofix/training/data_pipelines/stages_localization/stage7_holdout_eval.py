

import json
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.data.spark.utils import AugmentK8sSparkSession
import pyspark.sql.functions as F
from pyspark.sql.types import BinaryType
import pandas as pd
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
from research.utils.repo_change_utils import Modified
from research.next_edits.edit_localization_stages import EditLocalizationProblem, EditLocalizationRetrievalProblem

def run_filter(pickled_results):
    problems: list[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]] = compressed_loads(pickled_results, compression="gzip")
    new_problems = []
    for problem in problems:
        found_bad_example = False
        for file in problem[0].wip_to_future_repo_change.changed_files:
            if isinstance(file, Modified) and file.after.code == file.before.code:
                found_bad_example = True
                break
        if not found_bad_example:
            new_problems.append(problem)
    return compressed_dumps(new_problems, compression="gzip")

def stage7_holdout_eval(
    spark: AugmentK8sSparkSession,
    stage6_with_retrieval_path: str,
    stage7_eval_path: str,
    stage7_train_path: str,
    logs_output_base: str,
    eval_repo_names_path: str,
    max_eval_examples: int,
):
    config = {
        "stage6_with_retrieval_path": stage6_with_retrieval_path,
        "stage7_eval_path": stage7_eval_path,
        "stage7_train_path": stage7_train_path,
        "logs_output_base": logs_output_base,
        "eval_repo_names_path": eval_repo_names_path,
        "max_eval_examples": max_eval_examples,
    }
    save_config_to_stage_output_dir(stage7_eval_path, config)

    logger = create_stage_logger(__name__, stage7_eval_path, logs_output_base)
    logger.info(
        f"Starting holdout eval at {pd.Timestamp.now(tz='US/Pacific')}."
        f"Reading from {stage6_with_retrieval_path} and writing to {stage7_train_path} and {stage7_eval_path}."
        f"\nConfig: {json.dumps(config, indent=4)}"
    )

    with open(eval_repo_names_path, "r") as f:
        eval_repo_names = f.read().splitlines()

    stage6_with_retrieval_df = spark.read.parquet(stage6_with_retrieval_path)

    stage7_eval_df = stage6_with_retrieval_df.filter(
        F.col("repo_name").isin(eval_repo_names)
    )

    stage7_train_df = stage6_with_retrieval_df.exceptAll(stage7_eval_df)

    # randomly sample N eval problems from the total list of problems
    stage7_eval_df = stage7_eval_df.orderBy(F.rand()).limit(max_eval_examples)

    stage7_eval_df = stage7_eval_df.withColumn(
        "pickled_results",
        F.udf(run_filter, BinaryType())("pickled_results"),
    )
    stage7_train_df = stage7_train_df.withColumn(
        "pickled_results",
        F.udf(run_filter, BinaryType())("pickled_results"),
    )
    stage7_eval_df = stage7_eval_df.withColumn(
        "num_problems",
        F.udf(lambda x: len(compressed_loads(x, compression="gzip")), returnType=F.IntegerType())("pickled_results"),
    )
    stage7_train_df = stage7_train_df.withColumn(
        "num_problems",
        F.udf(lambda x: len(compressed_loads(x, compression="gzip")), returnType=F.IntegerType())("pickled_results"),
    )

    stage7_eval_df.write.parquet(stage7_eval_path)
    stage7_train_df.write.parquet(stage7_train_path)

    num_train_problems = stage7_train_df.select(F.sum("num_problems")).collect()[0][0]
    num_eval_problems = stage7_eval_df.select(F.sum("num_problems")).collect()[0][0]
    logger.info(
        f"Summary stats for stage7_holdout_eval:\n"
        f"num_train_problems: {num_train_problems}\n"
        f"num_eval_problems: {num_eval_problems}\n"
    )
