from dataclasses import asdict
import json
from pyspark.sql import SparkSession
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.next_edits.next_edits_dataset import PRMeta
from research.utils.repo_change_utils import CommitMeta
from pathlib import Path
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
from experimental.colin.projects.autofix.training.utils import AutofixProblem
from research.data.spark.pipelines.utils import map_parquet
from pyspark.sql import functions as F

import pandas as pd
from research.next_edits.edit_localization_stages import (
    CreateRetrievalProblemsConfig,
    EditLocalizationProblem,
    EditLocalizationRetrievalProblem,
    create_retrieval_problems,
)

def rca_object_to_str(rca_object) -> str | None:
    """Converts the root cause object to a string for the instructions."""

    if rca_object is None:
        return None

    data = rca_object.asDict()['root_cause'].asDict()
    formatted_changes = ''.join(f"- at path {change['path']}: {change['change_desc']}\n" for change in data['changes'])
    summary = f"""
    Here is a summary of the root cause of the error:
    {data['root_cause_desc']}

    Here is a file-by-file description of each of the changes in the breaking diff:
    {formatted_changes}
    """ 

    return summary   

def convert_to_edit_localization_problem(
    repo_id,
    problem: AutofixProblem,
    use_rca_instructions: bool,
) -> EditLocalizationProblem:
    pr_meta = PRMeta(
        repo_name=repo_id,
        pr_number=problem.pr_number if problem.pr_number is not None else -1,
        title="",
        body="",
    )
    commit_meta = CommitMeta(
        sha=problem.passing_child.sha,
        parents=[problem.failed_commits[0].sha],
        children=[],
        message="",
        repo_name=repo_id,
        debugging_info=problem.debugging_info,
    )

    assert len(problem.logs) == 1, len(problem.logs)
    if use_rca_instructions:
        maybe_rca_str = rca_object_to_str(problem.rca)
        if maybe_rca_str is None:
            return None
        instructions = maybe_rca_str
    else:
        instructions = problem.logs[0]

    edit_localization_problem = EditLocalizationProblem(
        pr_meta=pr_meta,
        commit_meta=commit_meta,
        instructions=instructions,
        wip_to_future_repo_change=problem.fixing_change,
        past_to_wip_repo_change=problem.breaking_change,
        command_output=problem.logs[0]
    )
    return edit_localization_problem

def stage6_create_retrieval_problems(
    spark: SparkSession,
    input_path: Path | str,
    output_path: Path | str,
    logs_output_base: str,
    config: CreateRetrievalProblemsConfig,
    timing_run: bool = False,
    global_seed: int = 0,
    run_local: bool = False,
    use_rca_instructions: bool = False,
) -> dict:

    config_dict = {**asdict(config), "input_path": str(input_path), "output_path": str(output_path), "logs_output_base": logs_output_base, "use_rca_instructions": use_rca_instructions}
    save_config_to_stage_output_dir(output_path, config_dict)

    logger = create_stage_logger(__name__, output_path, logs_output_base)
    logger.info(
        f"Starting create retrieval problems at {pd.Timestamp.now(tz='US/Pacific')}."
        f"Reading from {input_path} and writing to {output_path}."
        f"\nConfig: {json.dumps(config_dict, indent=4)}"    
    )

    def process_batch(df: pd.DataFrame) -> pd.DataFrame:
        output_per_repo = dict[
            str, list[tuple[EditLocalizationProblem, EditLocalizationRetrievalProblem]]
        ]()

        for row in df.to_dict(orient="records"):
            new_problems: list[EditLocalizationProblem] = []
            problems = compressed_loads(row["problems"], compression="gzip")
            for problem in problems:
                edit_localization_problem = convert_to_edit_localization_problem(
                    row["repo_id"], problem, use_rca_instructions=use_rca_instructions
                )
                if edit_localization_problem is None:
                    continue
                new_problems.append(edit_localization_problem)

            output_per_repo.setdefault(row["repo_id"], []).extend(
                create_retrieval_problems(
                    new_problems, config, seed=global_seed + hash(row["repo_id"])
                )
            )

        return pd.DataFrame(
            [
                {
                    "id": repo_name,
                    "repo_name": repo_name,
                    "num_problems": len(output),
                    "pickled_results": compressed_dumps(output, compression="gzip"),
                }
                for repo_name, output in output_per_repo.items()
            ]
        )

    if not run_local:
        map_parquet.apply_pandas(
            spark_session=spark,
            pandas_func=process_batch,
            input_path=str(input_path),
            output_path=str(output_path),
            batch_size=1,
            timeout=(
                3 * 3600  # 3 hours for dense retrieval
                if config.retrieval_strategy == "retrieved"
                else 3600  # 1 hour for other strategies retrieval
            ),
            input_columns=[
                "repo_id",
                "problems"
            ],
            ignore_error=False,
            timing_run=timing_run,
            allow_resume=True,
        )
        output_df = spark.read.parquet(output_path)
        num_problems = output_df.select(F.sum("num_problems")).collect()[0][0]
        logger.info(
            f"Summary stats for stage6_create_retrieval_problems:\n"
            f"num_problems: {num_problems}\n"
        )
    else:
        df = spark.read.parquet(input_path).limit(100).toPandas()
        process_batch(df)
        