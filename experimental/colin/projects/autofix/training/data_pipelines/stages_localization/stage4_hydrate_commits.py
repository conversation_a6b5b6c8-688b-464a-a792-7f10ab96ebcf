from concurrent.futures import ThreadPoolExecutor
from functools import partial
import json
from multiprocessing.pool import ThreadPool
import shutil
from typing import Any, Callable, ParamSpec, TypeVar
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir, run_with_timeout
from research.data.spark.utils import AugmentK8sSparkSession

from experimental.colin.projects.autofix.training.utils import AutofixProblem
import numpy as np
from research.data.spark.utils import k8s_session
import pyspark.sql.functions as F
from research.utils.repo_change_utils import CommitMeta, iterate_repo_history
import time
import logging
from pathlib import Path
import pickle
import random
import sys
import tarfile
import tempfile
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads

from tqdm import tqdm
from research.data.spark.pipelines.utils import map_parquet
from research.data.utils.pr_v2 import get_tar_full_path
from research.utils.repo_change_utils import RepoChange
import pandas as pd
from pyspark.sql import Row
import psutil

logger = logging.getLogger(__name__)
        
def process_repo(repo_ID, problems, assign_intermediate_failed_commits_to_breaking_diff: bool, skip_modified_filenames_in_target_diff: bool):
    problems: list[AutofixProblem] = compressed_loads(problems, compression="gzip")
    parsed_problems: list[AutofixProblem] = []
    iterate_repo_history_times = []

    # on /mnt/efs/spark-data
    repo_tarball_path = get_tar_full_path(repo_ID)

    num_missing_commits = 0

    def _exit(
        parsed_problems: list[AutofixProblem],
        num_missing_commits: int,
        iterate_repo_history_times: list[float],
        e: str = "",
        file_cts: list[int] = []
    ):
        num_problems = len(parsed_problems)
        median_file_ct = np.median(file_cts) if len(file_cts) > 0 else 0.0
        return pd.Series({
            "repo_id": repo_ID,
            "problems": compressed_dumps(parsed_problems, compression="gzip"),
            "num_problems": num_problems,
            "num_missing_commits": num_missing_commits,
            "iterate_repo_history_times": compressed_dumps(iterate_repo_history_times, compression="gzip"),
            "error_message": e,
            "median_file_ct": median_file_ct
        })

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        if not Path(repo_tarball_path).exists():
            err = f"ERROR: {repo_tarball_path} does not exist"
            all_commits = set()
            for problem in problems:
                all_commits.add(problem.passing_child.sha)
                for commit in problem.failed_commits:
                    all_commits.add(commit.sha)
            return _exit(
                parsed_problems=[],
                num_missing_commits=len(all_commits),
                iterate_repo_history_times=[],
                file_cts=[],
                e=err
            )

        with tarfile.open(repo_tarball_path, "r:*") as tar:
            tar.extractall(path=temp_path)

        def materialize_iterate_repo_history(*args, **kwargs):
            return list(iterate_repo_history(*args, **kwargs))

        file_cts: list[int] = []
        for problem in tqdm(problems, desc=f"iterate on commits for {repo_ID}"):
            logger.error("Starting grabbing commit!")
            try:
                start_time = time.time()

                parent_commit_sha = CommitMeta(
                    sha=problem.failed_commits[-1].parents[0],
                    message="",
                    repo_name=repo_ID,
                    parents=(),
                    children=(problem.failed_commits[-1].sha),
                )
                changes = run_with_timeout(
                    materialize_iterate_repo_history,
                    kwargs=dict(
                        project_dir=temp_path,
                        history=[parent_commit_sha] + list(reversed(problem.failed_commits)) + [problem.passing_child],
                        is_source_file=lambda x: True,
                    ),
                    timeout_duration=60,
                )
                end_time = time.time()
                iterate_repo_history_times.append(end_time - start_time)
                if changes is None:
                    logger.error(f"Timed out after {iterate_repo_history_times[-1]:.2f} seconds on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo...")
                    return _exit(
                        parsed_problems=parsed_problems,
                        num_missing_commits=num_missing_commits,
                        iterate_repo_history_times=iterate_repo_history_times,
                        e=f"Timed out on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo...",
                        file_cts=file_cts
                    )

                if assign_intermediate_failed_commits_to_breaking_diff:
                    # make breaking diff all but last change
                    fixing_change = changes[-1]
                    breaking_change = changes[0]
                    for change in changes[1:-1]:
                        breaking_change = breaking_change.squash(change)
                else:
                    # make fixing diff all but first change
                    breaking_change = changes[0]
                    fixing_change = changes[1]
                    for change in changes[2:]:
                        fixing_change = fixing_change.squash(change)

                problem.breaking_change = breaking_change
                problem.fixing_change = fixing_change
            except Exception as e:
                logger.error(f"Failed to grab commit for reason: {e}")
                logger.error(f"Exception type: {type(e)}")
                logger.error(f"Exception args: {e.args}")
                num_missing_commits += 1
                continue

            if skip_modified_filenames_in_target_diff:
                found_changed_filename = set(fixing_change.before_files.keys()) != set(fixing_change.after_files.keys())
                if found_changed_filename:
                    logger.error(
                        f"Found changed filename (filename addition, deletion, or modification) in target diff"
                        f" for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping problem..."
                    )
                    continue

            file_cts.append(len(fixing_change.before_files))
            parsed_problems.append(problem)

            # intern pool to save memory
            for p in fixing_change.before_files:
                fixing_change.before_files.set(
                    p, sys.intern(fixing_change.before_files[p])
                )
            for p in fixing_change.after_files:
                fixing_change.after_files.set(
                    p, sys.intern(fixing_change.after_files[p])
                )
            for p in breaking_change.before_files:
                breaking_change.before_files.set(
                    p, sys.intern(breaking_change.before_files[p])
                )
            for p in breaking_change.after_files:
                breaking_change.after_files.set(
                    p, sys.intern(breaking_change.after_files[p])
                )

    return _exit(
        parsed_problems=parsed_problems,
        num_missing_commits=num_missing_commits,
        iterate_repo_history_times=iterate_repo_history_times,
        e="",
        file_cts=file_cts
    )

def safe_process_repo(repo_ID, problems, assign_intermediate_failed_commits_to_breaking_diff, skip_modified_filenames_in_target_diff):
    try:
        return process_repo(repo_ID, problems, assign_intermediate_failed_commits_to_breaking_diff=assign_intermediate_failed_commits_to_breaking_diff, skip_modified_filenames_in_target_diff=skip_modified_filenames_in_target_diff)
    except EOFError as e:
        return pd.Series(
            {
                "repo_id": repo_ID,
                "problems": compressed_dumps([], compression="gzip"),
                "num_problems": 0,
                "num_missing_commits": 0,
                "iterate_repo_history_times": compressed_dumps([], compression="gzip"),
                "error_message": str(e),
                "median_file_ct": 0.0,
            }
        )

def stage4_hydrate_commits(
    spark: AugmentK8sSparkSession,
    stage3_path: str,
    stage4_path: str,
    logs_output_base: str,
    assign_intermediate_failed_commits_to_breaking_diff: bool = True,
    skip_modified_filenames_in_target_diff: bool = False,
    allow_resume: bool = True,
    analytics_only: bool = False,
):
    assert False, "Please use V2."

    config = {
        "stage3_path": stage3_path,
        "stage4_path": stage4_path,
        "assign_intermediate_failed_commits_to_breaking_diff": assign_intermediate_failed_commits_to_breaking_diff,
        "skip_modified_filenames_in_target_diff": skip_modified_filenames_in_target_diff,
        "allow_resume": allow_resume,
        "logs_output_base": logs_output_base,
    }
    logger = create_stage_logger(__name__, stage4_path, logs_output_base)

    if not analytics_only:
        save_config_to_stage_output_dir(stage4_path, config)
        logger.info(
            f"Starting hydrate commits at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {stage3_path} and writing to {stage4_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"
        )

        num_retries = 3
        for i in range(num_retries):
            try:
                start_time = time.time()
                map_parquet.apply(
                    spark,
                    partial(
                        safe_process_repo,
                        assign_intermediate_failed_commits_to_breaking_diff=assign_intermediate_failed_commits_to_breaking_diff,
                        skip_modified_filenames_in_target_diff=skip_modified_filenames_in_target_diff
                    ),
                    input_path=stage3_path,
                    output_path=stage4_path,
                    input_columns=["repo_ID", "problems"],
                    batch_size=1,
                    timeout=50_000,
                    ignore_error=False,
                    allow_resume=allow_resume,
                )
                end_time = time.time()
                time_in_mins = (end_time - start_time) / 60
                logger.info(f"Time taken: {time_in_mins} mins")
                break
            except Exception as e:
                logger.info(f"Failed job try {i} with error: {e}")
                if i < num_retries - 1:
                    logger.info(f"Retrying job {i+1}...Sleeping 5 minutes first.")
                    time.sleep(5*60)
        logger.info(f"Finished stage 4 at {pd.Timestamp.now(tz='US/Pacific')}.")
    else:
        logger.info(f"Skipping stage 4 processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    output_df = spark.read.parquet(stage4_path)
    num_problems = output_df.select(F.sum("num_problems")).collect()[0][0]
    num_unique_repos = output_df.select(F.countDistinct("repo_id")).collect()[0][0]
    num_missing_commits = output_df.select(F.sum("num_missing_commits")).collect()[0][0]
    min_file_ct = output_df.select(F.min("median_file_ct")).collect()[0][0]
    median_file_ct = output_df.select(F.median("median_file_ct")).collect()[0][0]
    max_file_ct = output_df.select(F.max("median_file_ct")).collect()[0][0]
    perc25_file_ct = output_df.approxQuantile("median_file_ct", [0.25], 0.001)[0]
    perc75_file_ct = output_df.approxQuantile("median_file_ct", [0.75], 0.001)[0]
    perc80_file_ct = output_df.approxQuantile("median_file_ct", [0.90], 0.001)[0]
    perc90_file_ct = output_df.approxQuantile("median_file_ct", [0.90], 0.001)[0]
    num_errors = output_df.filter(F.col("error_message") != "").count()
    logger.info(
        f"Summary stats for stage4_hydrate_commits:\n"
        f"num_problems: {num_problems}\n"
        f"num_unique_repos: {num_unique_repos}\n"
        f"num_missing_commits: {num_missing_commits}\n"
        f"min_file_ct: {min_file_ct}\n"
        f"perc25_file_ct: {perc25_file_ct}\n"
        f"median_file_ct: {median_file_ct}\n"
        f"perc75_file_ct: {perc75_file_ct}\n"
        f"perc80_file_ct: {perc80_file_ct}\n"
        f"perc90_file_ct: {perc90_file_ct}\n"
        f"max_file_ct: {max_file_ct}\n"
        f"num_errors: {num_errors}\n"
    )
