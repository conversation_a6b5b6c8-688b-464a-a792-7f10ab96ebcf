from concurrent.futures import Thread<PERSON>oolExecutor
from functools import partial
import json
from multiprocessing.pool import ThreadPool
import shutil
from typing import Any, Callable, ParamSpec, TypeVar
from base.diff_utils.changes import Modified, Added, Deleted
from base.diff_utils.diff_formatter import _default_diff_filter
from experimental.colin.projects.autofix.training.data_pipelines.utils import (
    create_stage_logger, 
    save_config_to_stage_output_dir, 
    run_with_timeout, 
    get_main_branch_name,
    get_merge_base, 
    get_branch_parent_sha,
    diff_repo_changes,
)
from research.data.spark.utils import AugmentK8sSparkSession

from experimental.colin.projects.autofix.training.utils import AutofixProblem
import numpy as np
from research.data.spark.utils import k8s_session
import pyspark.sql.functions as F
from research.utils.repo_change_utils import CommitMeta, iterate_repo_history, repo_change_from_repositories
import time
import logging
from pathlib import Path
import pickle
import random
import sys
import tarfile
import tempfile
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads

from tqdm import tqdm
from research.data.spark.pipelines.utils import map_parquet
from research.data.utils.pr_v2 import get_tar_full_path
from research.utils.repo_change_utils import RepoChange
import pandas as pd
from pyspark.sql import Row
import psutil

logger = logging.getLogger(__name__)
        
def process_repo(
        repo_ID, problems, 
        skip_modified_filenames_in_target_diff: bool, 
        skip_problems_with_missing_files: bool, 
        skip_problems_with_undesirable_paths: bool,
        skip_problems_with_permissions_change_in_fixing_diff: bool
    ):
    problems: list[AutofixProblem] = compressed_loads(problems, compression="gzip")
    parsed_problems: list[AutofixProblem] = []
    iterate_repo_history_times = []

    # on /mnt/efs/spark-data
    repo_tarball_path = get_tar_full_path(repo_ID)

    def _exit(
        parsed_problems: list[AutofixProblem],
        num_problems_failed_to_hydrate: int,
        iterate_repo_history_times: list[float],
        e: str = "",
        file_cts: list[int] = [],
        num_problems_failed_to_find_main_branch_name: int = 0,
        num_problems_skip_modified_filenames_in_target_diff: int = 0,
        num_skip_problems_with_missing_files: int = 0,
        num_skip_problems_with_undesirable_paths: int = 0,
        num_skip_problems_with_permissions_change_in_fixing_diff: int = 0,
        num_problems_failed_due_to_generic_error: int = 0,

    ):
        num_problems = len(parsed_problems)
        median_file_ct = np.median(file_cts) if len(file_cts) > 0 else 0.0
        return pd.Series({
            "repo_id": repo_ID,
            "problems": compressed_dumps(parsed_problems, compression="gzip"),
            "num_problems": num_problems,
            "num_problems_failed_to_hydrate": num_problems_failed_to_hydrate,
            "iterate_repo_history_times": compressed_dumps(iterate_repo_history_times, compression="gzip"),
            "error_message": e,
            "median_file_ct": median_file_ct,
            "num_problems_failed_to_find_main_branch_name": num_problems_failed_to_find_main_branch_name,
            "num_problems_skip_modified_filenames_in_target_diff": num_problems_skip_modified_filenames_in_target_diff,
            "num_skip_problems_with_missing_files": num_skip_problems_with_missing_files,
            "num_skip_problems_with_undesirable_paths": num_skip_problems_with_undesirable_paths,
            "num_skip_problems_with_permissions_change_in_fixing_diff": num_skip_problems_with_permissions_change_in_fixing_diff,
            "num_problems_failed_due_to_generic_error": num_problems_failed_due_to_generic_error,
        })

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        if not Path(repo_tarball_path).exists():
            err = f"ERROR: {repo_tarball_path} does not exist"
            return _exit(
                parsed_problems=[],
                num_problems_failed_to_hydrate=len(problems),
                iterate_repo_history_times=[],
                file_cts=[],
                e=err
            )

        with tarfile.open(repo_tarball_path, "r:*") as tar:
            tar.extractall(path=temp_path)

        def materialize_iterate_repo_history(*args, **kwargs):
            return list(iterate_repo_history(*args, **kwargs))
        
        num_problems_skip_modified_filenames_in_target_diff = 0
        num_skip_problems_with_missing_files = 0
        num_skip_problems_with_undesirable_paths = 0
        num_skip_problems_with_permissions_change_in_fixing_diff = 0
        num_problems_failed_due_to_generic_error = 0
        file_cts: list[int] = []
        err_msg = ""
        for problem in tqdm(problems, desc=f"iterate on {len(problems)} commits for {repo_ID}"):
            logger.error("Starting grabbing commit!")
            try:
                passing_child = problem.passing_child
                parent_failing_commit = problem.failed_commits[0]

                # Two options to compute breaking_diff_initial_repo_state:
                # - Grab the after repo state of the most recent successful commit
                # - Otherwise, grab the before state of the initial commit
                if problem.most_recent_successful_commit is not None:
                    last_good_commit_meta = problem.most_recent_successful_commit
                    assert last_good_commit_meta is not None, "last_good_commit_meta is None"
                    assert last_good_commit_meta.sha is not None, "last_good_commit_meta.sha is None"
                    assert len(last_good_commit_meta.parents) > 0, "last_good_commit_meta.parents is empty"
                    parent_sha = last_good_commit_meta.parents[0]
                    last_good_commit_parent_meta = CommitMeta(
                        sha=parent_sha,
                        parents=[],
                        children=[last_good_commit_meta.sha],
                        message="",
                        repo_name=repo_ID,
                    )
                    last_good_commit_repo_change_ls = run_with_timeout(
                        materialize_iterate_repo_history,
                        kwargs=dict(
                            project_dir=temp_path,
                            history=[last_good_commit_parent_meta, last_good_commit_meta],
                            is_source_file=lambda x: True,
                        ),
                        timeout_duration=60,
                    )
                    if last_good_commit_repo_change_ls is None:
                        logger.error(f"Failed to get breaking diff {repo_ID} at last good commit {last_good_commit_meta.sha} and passing child {passing_child.sha}; Skipping repo...")
                        return _exit(
                            parsed_problems=parsed_problems,
                            num_problems_failed_to_hydrate=len(problems) - len(parsed_problems),
                            iterate_repo_history_times=iterate_repo_history_times,
                            e=f"Timed out on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo...",
                            file_cts=file_cts,
                            num_problems_skip_modified_filenames_in_target_diff=num_problems_skip_modified_filenames_in_target_diff,
                            num_skip_problems_with_missing_files=num_skip_problems_with_missing_files,
                            num_skip_problems_with_undesirable_paths=num_skip_problems_with_undesirable_paths,
                            num_skip_problems_with_permissions_change_in_fixing_diff=num_skip_problems_with_permissions_change_in_fixing_diff,
                            num_problems_failed_due_to_generic_error=num_problems_failed_due_to_generic_error,
                        )
                    breaking_diff_initial_repo_state = last_good_commit_repo_change_ls[0].after_repo()
                    problem.debugging_info = ';'.join([
                        f"Grabbed breaking diff initial repo state from last good commit",
                        f"inital_commit_sha = {problem.initial_commit.sha}", 
                        f"last_good_commit_sha = {last_good_commit_meta.sha}", 
                        f"last_good_commit_parent_sha = {last_good_commit_parent_meta.sha}",    
                        f"parent_failing_commit_sha = {parent_failing_commit.sha}",
                        f"passing_child_sha = {passing_child.sha}",            
                    ])
                else:
                    # If we don't have access a specific most recent successful commit, then we look at file state at beginning of PR
                    initial_commit_sha = problem.initial_commit.sha
                    assert initial_commit_sha is not None, "initial_commit_sha is None"
                    assert len(problem.initial_commit.parents) > 0, "problem.initial_commit.parents is empty"
                    initial_commit_parent_meta = CommitMeta(
                        sha=problem.initial_commit.parents[0],
                        parents=[],
                        children=[problem.initial_commit.sha],
                        message="",
                        repo_name=repo_ID,
                    )
                    initial_commit_repo_change_ls = run_with_timeout(
                        materialize_iterate_repo_history,
                        kwargs=dict(
                            project_dir=temp_path,
                            history=[initial_commit_parent_meta, problem.initial_commit],
                            is_source_file=lambda x: True,
                        ),
                        timeout_duration=60,
                    )
                    if initial_commit_repo_change_ls is None:
                        logger.error(f"Failed to get breaking diff {repo_ID} at initial commit {initial_commit_sha} and passing child {passing_child.sha}; Skipping repo...")
                        return _exit(
                            parsed_problems=parsed_problems,
                            num_problems_failed_to_hydrate=len(problems) - len(parsed_problems),
                            iterate_repo_history_times=iterate_repo_history_times,
                            e=f"Timed out on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo...",
                            file_cts=file_cts,
                            num_problems_skip_modified_filenames_in_target_diff=num_problems_skip_modified_filenames_in_target_diff,
                            num_skip_problems_with_missing_files=num_skip_problems_with_missing_files,
                            num_skip_problems_with_undesirable_paths=num_skip_problems_with_undesirable_paths,
                            num_skip_problems_with_permissions_change_in_fixing_diff=num_skip_problems_with_permissions_change_in_fixing_diff,
                            num_problems_failed_due_to_generic_error=num_problems_failed_due_to_generic_error,
                        )
                    breaking_diff_initial_repo_state = initial_commit_repo_change_ls[0].before_repo()
                    problem.debugging_info = ';'.join([
                        f"Grabbed breaking diff initial repo state from initial commit",
                        f"inital_commit_sha = {problem.initial_commit.sha}", 
                        f"initial_commit parent = {problem.initial_commit.parents[0]}",    
                        f"parent_failing_commit_sha = {parent_failing_commit.sha}",
                        f"passing_child_sha = {passing_child.sha}",            
                    ])

                # Now grab fixing change
                fixing_repo_change = run_with_timeout(
                    materialize_iterate_repo_history,
                    kwargs=dict(
                        project_dir=temp_path,
                        history=[parent_failing_commit, passing_child],
                        is_source_file=lambda x: True,
                    ),
                    timeout_duration=60,
                )

                if fixing_repo_change is None:
                    logger.error(f"Timed out computing breaking and fixing diffs for {repo_ID} at last good commit {last_good_commit_meta.sha} and passing child {passing_child.sha}; Skipping repo...")
                    return _exit(
                        parsed_problems=parsed_problems,
                        num_problems_failed_to_hydrate=len(problems) - len(parsed_problems),
                        iterate_repo_history_times=iterate_repo_history_times,
                        e=f"Timed out on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo...",
                        file_cts=file_cts,
                        num_problems_skip_modified_filenames_in_target_diff=num_problems_skip_modified_filenames_in_target_diff,
                        num_skip_problems_with_missing_files=num_skip_problems_with_missing_files,
                        num_skip_problems_with_undesirable_paths=num_skip_problems_with_undesirable_paths,
                        num_skip_problems_with_permissions_change_in_fixing_diff=num_skip_problems_with_permissions_change_in_fixing_diff,
                        num_problems_failed_due_to_generic_error=num_problems_failed_due_to_generic_error,
                    )

                problem.fixing_change = fixing_repo_change[0]
                problem.breaking_change = repo_change_from_repositories(
                    breaking_diff_initial_repo_state,
                    problem.fixing_change.before_repo()
                )
            except Exception as e:
                logger.error(f"Failed to grab commit for reason: {e}")
                err_msg += f"Failed to grab commit for reason: {e}\n\n"
                logger.error(f"Exception type: {type(e)}")
                logger.error(f"Exception args: {e.args}")
                num_problems_failed_due_to_generic_error += 1
                continue

            if skip_modified_filenames_in_target_diff:
                found_changed_filename = False
                for file in problem.fixing_change.changed_files:
                    if isinstance(file, Added) or isinstance(file, Deleted):
                        found_changed_filename = True
                        break
                    if isinstance(file, Modified) and file.after.path != file.before.path:
                        found_changed_filename = True
                        break

                if found_changed_filename:
                    logger.error(
                        f"Found changed filename (filename addition, deletion, or modification) in target diff"
                        f" for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping problem..."
                    )
                    num_problems_skip_modified_filenames_in_target_diff += 1
                    continue

            if skip_problems_with_missing_files:
                undecodable_fps = []
                for file in problem.fixing_change.changed_files:
                    if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == "":
                        undecodable_fps.append(file.after.path)
                for file in problem.breaking_change.changed_files:
                    if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == "":
                        undecodable_fps.append(file.after.path)
                if len(undecodable_fps) > 0:
                    logger.error(
                        f"Found undecodable file in diff for {repo_ID} at sha {problem.failed_commits[0].sha};"
                        f"Undecodable file names: {undecodable_fps}; Skipping problem..."
                    )
                    num_skip_problems_with_missing_files += 1
                    continue
            if skip_problems_with_permissions_change_in_fixing_diff:
                found_permissions_change = False
                for file in problem.breaking_change.changed_files:
                    if isinstance(file, Modified) and file.after.path == file.before.path and file.after.code == file.before.code:  
                        found_permissions_change = True
                        break
                for file in problem.fixing_change.changed_files:
                    if isinstance(file, Modified) and file.after.path == file.before.path and file.after.code == file.before.code:  
                        found_permissions_change = True
                        break
                if found_permissions_change:
                    logger.error(
                        f"Found permissions change in diff for {repo_ID} at sha {problem.failed_commits[0].sha};"
                        f"Skipping problem..."
                    )
                    num_skip_problems_with_permissions_change_in_fixing_diff += 1
                    continue

            if skip_problems_with_undesirable_paths:
                if any(not _default_diff_filter(file_tuple.after.path) for file_tuple in problem.fixing_change.changed_files):
                    logger.error(
                        f"Found undesirable path in diff for {repo_ID} at sha {problem.failed_commits[0].sha};"
                        f"Skipping problem..."
                    )
                    num_skip_problems_with_undesirable_paths += 1
                    continue

            # Okay, we now have a problem we can save! Let's add its contents to the string
            # intern pool to save memory.

            file_cts.append(len(problem.fixing_change.before_files))
            parsed_problems.append(problem)

            # intern pool to save memory
            for p in problem.fixing_change.before_files:
                problem.fixing_change.before_files.set(
                    p, sys.intern(problem.fixing_change.before_files[p])
                )
            for p in problem.fixing_change.after_files:
                problem.fixing_change.after_files.set(
                    p, sys.intern(problem.fixing_change.after_files[p])
                )
            for p in problem.breaking_change.before_files:
                problem.breaking_change.before_files.set(
                    p, sys.intern(problem.breaking_change.before_files[p])
                )
            for p in problem.breaking_change.after_files:
                problem.breaking_change.after_files.set(
                    p, sys.intern(problem.breaking_change.after_files[p])
                )

    return _exit(
        parsed_problems=parsed_problems,
        num_problems_failed_to_hydrate=len(problems) - len(parsed_problems),
        iterate_repo_history_times=iterate_repo_history_times,
        e=err_msg if len(err_msg) > 0 else "Success!",
        file_cts=file_cts,
        num_problems_skip_modified_filenames_in_target_diff=num_problems_skip_modified_filenames_in_target_diff,
        num_skip_problems_with_missing_files=num_skip_problems_with_missing_files,
        num_skip_problems_with_undesirable_paths=num_skip_problems_with_undesirable_paths,
        num_skip_problems_with_permissions_change_in_fixing_diff=num_skip_problems_with_permissions_change_in_fixing_diff,
        num_problems_failed_due_to_generic_error=num_problems_failed_due_to_generic_error,
    )

def safe_process_repo(
    repo_ID, 
    problems, 
    skip_modified_filenames_in_target_diff, 
    skip_problems_with_missing_files,
    skip_problems_with_undesirable_paths,
    skip_problems_with_permissions_change_in_fixing_diff
):
    try:
        return process_repo(
            repo_ID, problems, 
            skip_modified_filenames_in_target_diff=skip_modified_filenames_in_target_diff, 
            skip_problems_with_missing_files=skip_problems_with_missing_files,
            skip_problems_with_undesirable_paths=skip_problems_with_undesirable_paths,
            skip_problems_with_permissions_change_in_fixing_diff=skip_problems_with_permissions_change_in_fixing_diff
        )
    except EOFError:
        return pd.Series(
            {
                "repo_id": repo_ID,
                "problems": compressed_dumps([], compression="gzip"),
                "num_problems": 0,
                "num_problems_failed_to_hydrate": 0,
                "iterate_repo_history_times": compressed_dumps([], compression="gzip"),
                "error_message": "Failed to decompress problems",
                "median_file_ct": 0.0,
                "num_problems_failed_to_find_main_branch_name": 0,
                "num_problems_skip_modified_filenames_in_target_diff": 0,
                "num_skip_problems_with_missing_files": 0,
                "num_skip_problems_with_undesirable_paths": 0,
                "num_skip_problems_with_permissions_change_in_fixing_diff": 0,
                "num_problems_failed_due_to_generic_error": 0,
            }
        )

def stage4_hydrate_commits(
    spark: AugmentK8sSparkSession,
    stage3_path: str,
    stage4_path: str,
    logs_output_base: str,
    skip_modified_filenames_in_target_diff: bool = False,
    skip_problems_with_missing_files: bool = False,
    skip_problems_with_undesirable_paths: bool = False,
    skip_problems_with_permissions_change_in_fixing_diff: bool = False,
    allow_resume: bool = True,
    analytics_only: bool = False,
    run_local: bool = False,
):
    config = {
        "stage3_path": stage3_path,
        "stage4_path": stage4_path,
        "skip_modified_filenames_in_target_diff": skip_modified_filenames_in_target_diff,
        "skip_problems_with_missing_files": skip_problems_with_missing_files,
        "skip_problems_with_undesirable_paths": skip_problems_with_undesirable_paths,
        "skip_problems_with_permissions_change_in_fixing_diff": skip_problems_with_permissions_change_in_fixing_diff,
        "allow_resume": allow_resume,
        "logs_output_base": logs_output_base,
    }
    logger = create_stage_logger(__name__, stage4_path, logs_output_base)

    if not analytics_only:
        save_config_to_stage_output_dir(stage4_path, config)
        logger.info(
            f"Starting hydrate commits at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {stage3_path} and writing to {stage4_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"
        )

        num_retries = 3
        for i in range(num_retries):
            try:
                start_time = time.time()
                if run_local:
                    df = spark.read.parquet(stage3_path).limit(2).toPandas()
                    all_repo_ids = df["repo_ID"].unique()
                    logger.info(f"Running locally and there are {len(all_repo_ids)} unique repos: {all_repo_ids}.")
                    # apply safe_process_repo to each row in df
                    df = df.apply(lambda row: safe_process_repo(
                        repo_ID=row["repo_ID"],
                        problems=row["problems"],
                        skip_modified_filenames_in_target_diff=skip_modified_filenames_in_target_diff,
                        skip_problems_with_missing_files=skip_problems_with_missing_files,
                        skip_problems_with_undesirable_paths=skip_problems_with_undesirable_paths,
                        skip_problems_with_permissions_change_in_fixing_diff=skip_problems_with_permissions_change_in_fixing_diff
                    ), axis=1)
                    df.to_parquet(stage4_path + '/part0.zstd.parquet', compression="zstd")
                else:
                    map_parquet.apply(
                        spark,
                        partial(
                            safe_process_repo,
                            skip_modified_filenames_in_target_diff=skip_modified_filenames_in_target_diff,
                            skip_problems_with_missing_files=skip_problems_with_missing_files,
                            skip_problems_with_undesirable_paths=skip_problems_with_undesirable_paths,
                            skip_problems_with_permissions_change_in_fixing_diff=skip_problems_with_permissions_change_in_fixing_diff
                        ),
                        input_path=stage3_path,
                        output_path=stage4_path,
                        input_columns=["repo_ID", "problems"],
                        batch_size=1,
                        timeout=60*60*3,
                        ignore_error=False,
                        allow_resume=allow_resume,
                    )
                end_time = time.time()
                time_in_mins = (end_time - start_time) / 60
                logger.info(f"Time taken: {time_in_mins} mins")
                break
            except Exception as e:
                logger.info(f"Failed job try {i} with error: {e}")
                if i < num_retries - 1:
                    logger.info(f"Retrying job {i+1}...Sleeping 5 minutes first.")
                    time.sleep(5*60)
        logger.info(f"Finished stage 4 at {pd.Timestamp.now(tz='US/Pacific')}.")
    else:
        logger.info(f"Skipping stage 4 processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    output_df = spark.read.parquet(stage4_path)
    num_problems = output_df.select(F.sum("num_problems")).collect()[0][0]
    num_unique_repos = output_df.select(F.countDistinct("repo_id")).collect()[0][0]
    num_problems_failed_to_hydrate = output_df.select(F.sum("num_problems_failed_to_hydrate")).collect()[0][0]
    num_problems_failed_to_find_main_branch_name = output_df.select(F.sum("num_problems_failed_to_find_main_branch_name")).collect()[0][0]
    num_problems_skip_modified_filenames_in_target_diff = output_df.select(F.sum("num_problems_skip_modified_filenames_in_target_diff")).collect()[0][0]
    num_skip_problems_with_missing_files = output_df.select(F.sum("num_skip_problems_with_missing_files")).collect()[0][0]
    num_skip_problems_with_undesirable_paths = output_df.select(F.sum("num_skip_problems_with_undesirable_paths")).collect()[0][0]
    num_skip_problems_with_permissions_change_in_fixing_diff = output_df.select(F.sum("num_skip_problems_with_permissions_change_in_fixing_diff")).collect()[0][0]
    num_problems_failed_due_to_generic_error = output_df.select(F.sum("num_problems_failed_due_to_generic_error")).collect()[0][0]
    min_file_ct = output_df.select(F.min("median_file_ct")).collect()[0][0]
    median_file_ct = output_df.select(F.median("median_file_ct")).collect()[0][0]
    max_file_ct = output_df.select(F.max("median_file_ct")).collect()[0][0]
    perc25_file_ct = output_df.approxQuantile("median_file_ct", [0.25], 0.001)[0]
    perc75_file_ct = output_df.approxQuantile("median_file_ct", [0.75], 0.001)[0]
    perc80_file_ct = output_df.approxQuantile("median_file_ct", [0.90], 0.001)[0]
    perc90_file_ct = output_df.approxQuantile("median_file_ct", [0.90], 0.001)[0]
    num_errors = output_df.filter(F.col("error_message") != "").count()
    logger.info(
        f"Summary stats for stage4_hydrate_commits:\n"
        f"num_problems: {num_problems}\n"
        f"num_unique_repos: {num_unique_repos}\n"
        f"num_problems_failed_to_hydrate: {num_problems_failed_to_hydrate}\n"
        f"min_file_ct: {min_file_ct}\n"
        f"perc25_file_ct: {perc25_file_ct}\n"
        f"median_file_ct: {median_file_ct}\n"
        f"perc75_file_ct: {perc75_file_ct}\n"
        f"perc80_file_ct: {perc80_file_ct}\n"
        f"perc90_file_ct: {perc90_file_ct}\n"
        f"max_file_ct: {max_file_ct}\n"
        f"num_errors: {num_errors}\n"
        f"num_problems_failed_to_find_main_branch_name: {num_problems_failed_to_find_main_branch_name}\n"
        f"num_problems_skip_modified_filenames_in_target_diff: {num_problems_skip_modified_filenames_in_target_diff}\n"
        f"num_skip_problems_with_missing_files: {num_skip_problems_with_missing_files}\n"
        f"num_skip_problems_with_undesirable_paths: {num_skip_problems_with_undesirable_paths}\n"
        f"num_skip_problems_with_permissions_change_in_fixing_diff: {num_skip_problems_with_permissions_change_in_fixing_diff}\n"
        f"num_problems_failed_due_to_generic_error: {num_problems_failed_due_to_generic_error}\n"
    )
