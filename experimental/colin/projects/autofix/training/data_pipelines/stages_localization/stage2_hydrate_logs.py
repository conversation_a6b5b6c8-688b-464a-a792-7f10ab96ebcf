import json
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.data.spark.utils import AugmentK8sSparkSession
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
from pyspark.sql.types import IntegerType, StringType, ArrayType, BinaryType
from pyspark.sql.functions import explode
import pyspark.sql.functions as F
import pandas as pd
import logging

logger = logging.getLogger(__name__)

def stage2_hydrate_logs(
    spark: AugmentK8sSparkSession,
    stage1_path: str,
    pr_failed_logs_path: str,
    stage2_path: str,
    logs_output_base: str,
):
    config = {
        "stage1_path": stage1_path,
        "pr_failed_logs_path": pr_failed_logs_path,
        "stage2_path": stage2_path,
        "logs_output_base": logs_output_base,
    }
    save_config_to_stage_output_dir(stage2_path, config)

    logger = create_stage_logger(__name__, stage2_path, logs_output_base)

    logger.info(
        f"{'-' * 120}\n"
        f"Starting stage2_hydrate_logs at {pd.Timestamp.now(tz='US/Pacific')}. Reading from {stage1_path} and writing to {stage2_path}, grabbing logs from {pr_failed_logs_path}.\n"
        f"Config: {json.dumps(config, indent=4)}"
    )

    stage1_df = spark.read.parquet(stage1_path)
    num_input_rows = stage1_df.count()

    # drop num_problems column
    stage1_df = stage1_df.drop('num_problems')
    # convert column to list[binary]
    stage1_df = stage1_df.withColumn(
        "problems", F.udf(lambda x: [compressed_dumps(problem, compression="gzip") for problem in compressed_loads(x, compression="gzip")], returnType=ArrayType(BinaryType()))(F.col("problems"))
    )
    # explode problems
    stage1_df = stage1_df.withColumn('problems', explode('problems'))
    # create sha and check_run_name columns
    stage1_df = stage1_df.withColumn(
        "sha", F.udf(lambda x: compressed_loads(x, compression="gzip").logs_sha, returnType=StringType())(F.col("problems"))
    )
    stage1_df = stage1_df.withColumn(
        "check_run_name", F.udf(lambda x: compressed_loads(x, compression="gzip").logs_check_run_name, returnType=StringType())(F.col("problems"))
    )

    # fresh load of logs
    failed_logs_df = spark.read.parquet(pr_failed_logs_path)
    failed_logs_df = failed_logs_df.filter(F.col("status_code").isin([200, 206]))
    failed_logs_df = failed_logs_df.withColumn(
        "repo_ID", F.concat(F.col("owner"), F.lit("/"), F.col("name"))
    )
    failed_logs_df = failed_logs_df.select([col for col in failed_logs_df.columns if col not in ['owner', 'name', 'pr_number']])
    failed_logs_df = failed_logs_df.withColumnRenamed("commit_sha", "sha")
    failed_logs_df = failed_logs_df.dropDuplicates(['repo_ID', 'sha', 'check_run_name'])

    # hydrate exploded problems with logs and then regroup and compress by repo
    stage1_df = stage1_df.join(failed_logs_df, on=['repo_ID', 'sha', 'check_run_name'], how='inner')
    # add log to problem objects
    def hydrate_problems(problem, log):
        problem = compressed_loads(problem, compression="gzip")
        problem.logs = [log]
        return compressed_dumps(problem, compression="gzip")
    stage1_df = stage1_df.withColumn(
        "problems", F.udf(hydrate_problems, returnType=BinaryType())(F.col("problems"), F.col("log"))
    )
    stage1_df = stage1_df.select(['repo_ID', 'problems'])
    # regroup by repo and store problems and log column
    stage1_df = stage1_df.groupBy('repo_ID').agg(F.collect_list('problems').alias('problems'))
    def compress_repo(problems):
        problems = [compressed_loads(problem, compression="gzip") for problem in problems]
        return compressed_dumps(problems, compression="gzip")
    stage1_df = stage1_df.withColumn('problems', F.udf(compress_repo, returnType=BinaryType())(F.col('problems')))

    # add num_problems column
    stage1_df = stage1_df.withColumn('num_problems', F.udf(lambda x: len(compressed_loads(x, compression="gzip")), returnType=IntegerType())(F.col('problems')))

    # write to disk
    stage1_df.write.parquet(stage2_path)

    logger.info(f"Finished stage2_hydrate_logs at {pd.Timestamp.now(tz='US/Pacific')}. Wrote to {stage2_path}.")
    num_output_rows = stage1_df.count()
    num_problems = stage1_df.select(F.sum('num_problems')).collect()[0][0]
    logger.info(
        f"Summary stats for stage2_hydrate_logs:\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_output_rows: {num_output_rows}\n"
        f"num_problems: {num_problems}"
    )