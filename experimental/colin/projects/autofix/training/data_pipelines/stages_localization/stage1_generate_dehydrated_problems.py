from functools import partial
import json
import random
import logging
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, run_with_timeout, save_config_to_stage_output_dir
from research.core.utils_for_str import compute_edit_similarity
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import AugmentK8sSparkSession
from research.utils.repo_change_utils import CommitMeta
from experimental.colin.projects.autofix.training.utils import AutofixProblem
from compress_pickle import dumps as compressed_dumps, loads as compressed_loads
import pandas as pd
import pyspark.sql.functions as F

logger = logging.getLogger(__name__)

def process_repo(
    repo_ID, 
    commit_metadata_ls,
    use_multiple_check_runs: bool = False,
    max_edits_similarity_perc: float = 1.0,
    max_problems: int = 1_000,
):
    commits = compressed_loads(commit_metadata_ls, compression="gzip")
    commits = [commit.asDict() for commit in commits]

    commits = [commit for commit in commits if commit['parents'] is not None and len(commit['parents']) == 1]
    for commit in commits:
        commit['parent'] = commit['parents'][0]

    # --------------------------
    # First create three maps:
    # - parent_to_child_sha_map: maps parent sha to child sha
    # - child_to_parent_sha_map: maps child sha to parent sha
    # - commit_to_is_success_by_check_run_name: maps commit sha to a map of check_run_name to whether or not that run was successful.
    parent_to_child_sha_map = {}
    child_to_parent_sha_map = {}
    commit_to_is_success_by_check_run_name = {}
    for commit in commits:
        assert commit['sha'] is not None
        if commit['parent'] is not None:
            parent_to_child_sha_map[commit['parent']] = commit['sha']
            child_to_parent_sha_map[commit['sha']] = commit['parent']
            commit_to_is_success_by_check_run_name[commit['sha']] = {}
            if commit['conclusion_list'] is None:
                continue
            for check_run_info in commit['conclusion_list']:
                commit_to_is_success_by_check_run_name[commit['sha']][check_run_info['check_run_name']] = check_run_info['conclusion'] == 'SUCCESS'

    # --------------------------
    # Next, create a map from sha to CommitMeta object
    sha_to_commit_meta = {}
    for commit in commits:
        child_sha = parent_to_child_sha_map.get(commit['sha'], None)
        if commit['parent'] is None:
            continue
        sha_to_commit_meta[commit['sha']] = CommitMeta(
            sha=commit['sha'],
            parents=[commit['parent']] if commit['parent'] is not None else [],
            children=[child_sha] if child_sha is not None else [],
            message=commit['message'],
            repo_name=repo_ID,
        )

    # --------------------------
    # Now, iterate through the commits and create problems
    problems = []
    missing_child_check_runs = 0
    for commit in commits:
        sha = commit['sha']
        pr_number = commit['pr_number']
        # -------------------------
        # Creating a problem involves several steps:
        # - 1) skip commits that don't have at least one failure log
        # - 2) skip commits where none of their failed check runs passed in the child commit
        # - 3) grab a random failing check run that passed in the child commit and use that as the target check run.
        # - 4) grab CommitMeta objects for the commit and its child
        # - 5) Create an Autofix problem
        # - 6) grab all ancestors of the failing commit that failed the same check run and add them to the problem.

        # -------------------------
        # 1) skip commits that don't have at least one failure log
        if commit['all_logs'] is None:
            continue

        # -------------------------
        # 2) skip commits where none of their failed check runs passed in the child commit
        child_sha = parent_to_child_sha_map.get(sha, None)
        if child_sha is None:
            continue
        passing_check_runs = []
        for log_info in commit['all_logs']:
            check_run_name = log_info['check_run_name']
            if commit_to_is_success_by_check_run_name[child_sha].get(check_run_name, False):
                passing_check_runs.append((check_run_name, log_info['log']))
            if check_run_name not in commit_to_is_success_by_check_run_name[child_sha]:
                missing_child_check_runs += 1
        if len(passing_check_runs) == 0:
            continue

        if not use_multiple_check_runs:
            candidate_check_runs = random.sample(passing_check_runs, 1)
        else:
            # Compute a set of check runs that share no more than MAX_EDITS_SIMILARITY_PERC % edit similarity
            candidate_check_runs = []
            for check_run_name, log in passing_check_runs:
                for accepted_check_run_name, _ in candidate_check_runs:
                    if compute_edit_similarity(check_run_name, accepted_check_run_name) > max_edits_similarity_perc:
                        break
                else:
                    candidate_check_runs.append((check_run_name, log))

        # -------------------------
        # 3) grab a random failing check run that passed in the child commit and use that as the target check run.
        for target_check_run_name, log in candidate_check_runs:

            # -------------------------
            # 4) grab CommitMeta objects for the commit and its child
            if child_sha not in sha_to_commit_meta or sha not in sha_to_commit_meta:
                continue
            child_commitmeta = sha_to_commit_meta[child_sha]
            commit_meta = sha_to_commit_meta[sha]
            
            # -------------------------
            # 5) Create an Autofix problem
            failed_commit_info = AutofixProblem(
                passing_child=child_commitmeta, 
                failed_commits=[commit_meta],
                logs=[log],
                logs_check_run_name=target_check_run_name,
                logs_sha=sha,
                pr_number=pr_number,
            )
            # -------------------------
            # 6) grab all ancestors of the failing commit that failed the same check run and add them to the problem.
            oldest_processed_failing_commit = failed_commit_info.failed_commits[-1].sha
            while True:
                parent_failing_commit = child_to_parent_sha_map.get(oldest_processed_failing_commit, None)
                if (
                    parent_failing_commit is None
                    or parent_failing_commit not in commit_to_is_success_by_check_run_name
                    or target_check_run_name not in commit_to_is_success_by_check_run_name[parent_failing_commit]
                    or commit_to_is_success_by_check_run_name[parent_failing_commit][target_check_run_name]
                    or parent_failing_commit not in sha_to_commit_meta
                ):
                    break
                failed_commit_info.failed_commits.append(sha_to_commit_meta[parent_failing_commit])
                oldest_processed_failing_commit = parent_failing_commit


            problems.append(failed_commit_info)

            if len(problems) >= max_problems:
                break

        # -------------------------

    num_problems = len(problems)
    return pd.Series({
        "repo_ID": repo_ID,
        "problems": compressed_dumps(problems, compression="gzip"),
        "num_problems": num_problems,
        "missing_child_check_runs": missing_child_check_runs,
        "error": "",
    })

def process_repo_with_timeout(
    repo_ID, 
    commit_metadata_ls,
    use_multiple_check_runs: bool = False,
    max_edits_similarity_perc: float = 1.0,
    max_problems: int = 1_000,
    repo_timeout_secs: int = 300,
):
    output = run_with_timeout(
        process_repo,
        args=(
            repo_ID, 
            commit_metadata_ls,
            use_multiple_check_runs,
            max_edits_similarity_perc,
            max_problems,
        ),
        timeout_duration=repo_timeout_secs,
    )

    if output is None:
        return pd.Series({
            "repo_ID": repo_ID,
            "problems": compressed_dumps([], compression="gzip"),
            "num_problems": 0,
            "missing_child_check_runs": 0,
            "error": f"Hit timeout while processing {repo_ID} after {repo_timeout_secs} seconds.",
        })
    else:
        return output

def stage1_generate_dehydrated_problems(
    spark: AugmentK8sSparkSession,
    stage0_path: str, 
    stage1_path: str,
    logs_output_base: str,
    use_multiple_check_runs: bool = False,
    max_edits_similarity_perc: float = 1.0,
    max_problems: int = 1_000,
    batch_size: int = 1,
    repo_timeout_secs: int = 300,
    timeout: int = 50_000,
    ignore_error: bool = False,
    allow_resume: bool = False,
):
    config = {
        "stage0_path": stage0_path,
        "stage1_path": stage1_path,
        "batch_size": batch_size,
        "use_multiple_check_runs": use_multiple_check_runs,
        "max_edits_similarity_perc": max_edits_similarity_perc,
        "max_problems": max_problems,
        "timeout": timeout,
        "repo_timeout_secs": repo_timeout_secs,
        "ignore_error": ignore_error,
        "allow_resume": allow_resume,
        "logs_output_base": logs_output_base,
    }
    save_config_to_stage_output_dir(
        stage1_path,
        config,
    )

    logger = create_stage_logger(__name__, stage1_path, logs_output_base)

    logger.info(
        f"{'-' * 120}\n"
        f"Starting stage1_generate_dehydrated_problems at {pd.Timestamp.now(tz='US/Pacific')}."
        f"Reading from {stage0_path} and writing to {stage1_path}.\n"
        f"Config: {json.dumps(config, indent=4)}"
    )

    #stage0_path = [
    #    "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage0_grouped_commit_full_metadata/part-00198-9a2ec512-bea7-4060-83fc-46fa7e49adab-c000.zstd.parquet",
    #    "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage0_grouped_commit_full_metadata/part-00199-9a2ec512-bea7-4060-83fc-46fa7e49adab-c000.zstd.parquet",
    #]

    map_parquet.apply(
        spark,
        partial(
            process_repo_with_timeout,
            use_multiple_check_runs=use_multiple_check_runs,
            max_edits_similarity_perc=max_edits_similarity_perc,
            max_problems=max_problems,
            repo_timeout_secs=repo_timeout_secs,
        ),
        input_columns=["repo_ID", "commit_metadata_ls"],
        input_path=stage0_path,
        output_path=stage1_path,
        batch_size=batch_size,
        timeout=timeout,
        ignore_error=ignore_error,
        allow_resume=allow_resume,
    )

    logger.info(f"Finished stage1_generate_dehydrated_problems at {pd.Timestamp.now(tz='US/Pacific')}. Generating summary stats...")

    #input_data = spark.read.parquet(*stage0_path)
    input_data = spark.read.parquet(stage0_path)
    num_input_rows = input_data.count()

    output_data = spark.read.parquet(stage1_path)
    num_rows = output_data.count()
    num_problems = output_data.select(F.sum("num_problems")).collect()[0][0]
    missing_child_check_runs = output_data.select(F.sum("missing_child_check_runs")).collect()[0][0]
    num_errors = output_data.filter(F.col("error") != "").count()

    logger.info(
        f"Summary stats for stage1_generate_dehydrated_problems:\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_rows: {num_rows}\n"
        f"num_problems: {num_problems}\n"
        f"missing_child_check_runs: {missing_child_check_runs}\n"
        f"num_errors: {num_errors}\n"
    )

      

