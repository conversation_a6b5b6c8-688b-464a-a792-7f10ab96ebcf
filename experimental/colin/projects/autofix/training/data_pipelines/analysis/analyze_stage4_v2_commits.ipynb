{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "\n", "cpu_spark = create_cpu_spark()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "dataset_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits/'\n", "ds = cpu_spark.read.parquet(dataset_path)\n", "\n", "num_problems = ds.select(F.sum('num_problems')).collect()[0][0]\n", "print(f\"num_problems: {num_problems}\")\n", "# 94000\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# only keep rows where error message does not contain \"ERROR: \"\n", "ds = ds.filter(~<PERSON>.col('error_message').contains('ERROR: '))\n", "# count num problems\n", "num_problems = ds.select(F.sum('num_problems')).collect()[0][0]\n", "# count num missing commits\n", "num_missing_commits = ds.select(F.sum('num_missing_commits')).collect()[0][0]\n", "\n", "print(f\"num_problems: {num_problems}, num_missing_commits: {num_missing_commits}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import repo_change_to_str\n", "\n", "ds_local = ds.limit(20).toPandas()\n", "\n", "\n", "i = 1\n", "print(f\"repo_id: {ds_local.iloc[i]['repo_id']}\")\n", "problems = compressed_loads(ds_local.iloc[i]['problems'], compression='gzip')\n", "\n", "j = 1\n", "print(f\"pr number: {problems[j].pr_number}\")\n", "print(problems[j].logs)\n", "print(problems[j].logs_check_run_name)\n", "print(problems[j].passing_child.sha)\n", "print('----\\n FIXING \\n----\\n')\n", "print(repo_change_to_str(problems[j].fixing_change))\n", "print('----\\n BREAKING \\n----\\n')\n", "print(repo_change_to_str(problems[j].breaking_change))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#problems[j].breaking_change.changed_files\n", "\n", "print(problems[j].most_recent_successful_commit.sha)\n", "print(problems[j].initial_commit.sha)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(repo_change_to_str(problems[0].fixing_change))\n", "from research.utils.repo_change_utils import RepoChange, FileTuple\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "\n", "file_changes = tuple(\n", "    change.map(FileTuple.to_file)\n", "    for change in problems[0].fixing_change.changed_files\n", ")\n", "print(file_changes)\n", "diff_hunks = format_file_changes_with_ranges(\n", "    changes=file_changes,\n", "    diff_context_lines=3,\n", "    diff_filter=lambda x: True,\n", "    use_smart_header=True,\n", ")\n", "\n", "diff_str = \"\"\n", "for hunk in reversed(diff_hunks):\n", "    body_text = hunk.text\n", "    header_text = hunk.path_header(deduplicate_identical_paths=True)\n", "    diff_str += header_text + body_text\n", "\n", "print(diff_str)\n", "\n", "# _default_diff_filter("]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(repo_change_to_str(problems[0].fixing_change))\n", "from research.utils.repo_change_utils import RepoChange, FileTuple\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "\n", "file_changes = tuple(\n", "    change.map(FileTuple.to_file)\n", "    for change in problems[0].breaking_change.changed_files\n", ")\n", "print(file_changes)\n", "diff_hunks = format_file_changes_with_ranges(\n", "    changes=file_changes,\n", "    diff_context_lines=3,\n", "    diff_filter=lambda x: True,\n", "    use_smart_header=True,\n", ")\n", "\n", "diff_str = \"\"\n", "for hunk in reversed(diff_hunks):\n", "    body_text = hunk.text\n", "    header_text = hunk.path_header(deduplicate_identical_paths=True)\n", "    diff_str += header_text + body_text\n", "\n", "print(diff_str)\n", "\n", "# _default_diff_filter("]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Stage 1 output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "\n", "cpu_spark = create_cpu_spark()\n", "\n", "dataset_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage1_generate_problems'\n", "ds = cpu_spark.read.parquet(dataset_path)\n", "\n", "ds = ds.to<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "from random import shuffle as shuffled\n", "\n", "# only keep rows where num_problems > 1\n", "ds = ds[ds['num_problems'] > 1]\n", "\n", "shuffled_rows = list(ds.iterrows())\n", "shuffled(shuffled_rows)\n", "\n", "# iterate through rows\n", "for i, row in shuffled_rows[:300]:\n", "    print('----')\n", "    print(row['repo_ID'])\n", "    problems = compressed_loads(row['problems'], compression='gzip')\n", "    for problem in problems[:3]:\n", "        print(f'pr_number {problem.pr_number}, failing_commit: {problem.failed_commits[0].sha}, 1st commit: {problem.initial_commit.sha}, most recent passing commit: {problem.most_recent_successful_commit.sha if problem.most_recent_successful_commit is not None else None}')\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_correct = [True, True, True, True, True, ]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}