{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "import pyspark.sql.functions as F\n", "from tqdm import tqdm\n", "from base.diff_utils.changes import Modified\n", "from compress_pickle import loads as compressed_loads\n", "from compress_pickle import dumps as compressed_dumps\n", "from pyspark.sql.types import IntegerType\n", "\n", "from research.utils.repo_change_utils import RepoChange\n", "\n", "cpu_spark = create_cpu_spark()\n", "\n", "dataset_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_raw = cpu_spark.read.parquet(dataset_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filter for \"pandas\" in repo_id\n", "#ds = ds_raw.filter(<PERSON><PERSON>col('repo_id').contains('pandas-dev/pandas'))\n", "ds = ds_raw.filter(<PERSON><PERSON>col('num_problems') > 2)\n", "ds_local = ds.limit(10).toPandas()\n", "\n", "ds_local"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def count_problems_with_undecodable_files(problems_bytes):\n", "    problems = compressed_loads(problems_bytes, compression='gzip')\n", "    \n", "    num_problems_with_undecodable_files = 0\n", "    for problem in problems:\n", "        found_undecodable_file = False\n", "        for file in problem.fixing_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == \"\":\n", "                found_undecodable_file = True\n", "                break\n", "        for file in problem.breaking_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == \"\":\n", "                found_undecodable_file = True\n", "                break\n", "        if found_undecodable_file:\n", "            num_problems_with_undecodable_files += 1\n", "    return num_problems_with_undecodable_files\n", "\n", "ds = ds_raw.withColumn('num_problems_with_undecodable_files', F.udf(count_problems_with_undecodable_files, IntegerType())(<PERSON><PERSON>col('problems')))\n", "num_problems_with_undecodable_files = ds.select(F.sum('num_problems_with_undecodable_files')).collect()[0][0]\n", "total_problems = ds.select(F.sum('num_problems')).collect()[0][0]\n", "\n", "print(f\"Total problems: {total_problems}\")\n", "print(f\"Total problems with undecodable files: {num_problems_with_undecodable_files}\")\n", "print(f\"Percentage of problems with undecodable files: {num_problems_with_undecodable_files / total_problems}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def filter_problems_with_undecodable_files(problems_bytes):\n", "    problems = compressed_loads(problems_bytes, compression='gzip')\n", "    new_problems = []\n", "    for problem in problems:\n", "        found_undecodable_file = False\n", "        for file in problem.fixing_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == \"\":\n", "                found_undecodable_file = True\n", "                break\n", "        for file in problem.breaking_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == \"\":\n", "                found_undecodable_file = True\n", "                break\n", "        if not found_undecodable_file:\n", "            new_problems.append(problem)\n", "\n", "    return compressed_dumps(new_problems, compression='gzip')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}