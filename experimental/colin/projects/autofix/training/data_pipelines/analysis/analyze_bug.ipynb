{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark, create_gpu_spark\n", "\n", "# import process_repo from stage4_V2_hydrate_commits\n", "from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage4_V2_hydrate_commits import process_repo\n", "\n", "spark = create_cpu_spark(100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "ds = spark.read.parquet(\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits_extra_analytics/part-00890-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet',\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits_extra_analytics/part-00891-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet',\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits_extra_analytics/part-00892-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet'\n", ")\n", "\n", "# filter for num_problems_failed_due_to_generic_error > 0\n", "field = \"num_problems_failed_due_to_generic_error\"\n", "ds = ds.filter(ds[field] > 0)\n", "\n", "# get relevant repo IDs\n", "ds_repo_ids = ds.select(\"repo_id\")\n", "\n", "input_ds = spark.read.parquet(\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage3_with_exploded_problems/part-00890-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet',\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage3_with_exploded_problems/part-00891-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet',\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage3_with_exploded_problems/part-00892-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet'\n", ")\n", "# grab the rows with the same repo ID\n", "ds = ds_repo_ids.join(input_ds, \"repo_id\")\n", "\n", "print(ds.count())\n", "\n", "ds = ds.to<PERSON><PERSON><PERSON>()\n", "print(ds)\n", "\n", "for _, row in ds.iterrows():\n", "    try:\n", "        out = process_repo(\n", "            row[\"repo_id\"], \n", "            row[\"problems\"], \n", "            skip_modified_filenames_in_target_diff=True, \n", "            skip_problems_with_missing_files=True,\n", "            skip_problems_with_undesirable_paths=True\n", "        )\n", "    except Exception as e:\n", "        print(e)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Look for malformed problems"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits_fixed3'\n", "ds = spark.read.parquet(ds_path)\n", "\n", "from compress_pickle import dumps as compressed_dumps\n", "from compress_pickle import loads as compressed_loads\n", "from research.utils.repo_change_utils import Modified\n", "from pyspark.sql.types import BinaryType\n", "from pyspark.sql.types import IntegerType\n", "from pyspark.sql import functions as F\n", "\n", "def count_num_problems_with_broken_fixing_diffs(problems_bytes):\n", "    problems = compressed_loads(problems_bytes, compression='gzip')\n", "    num_problems_with_broken_diffs = 0\n", "    for problem in problems:\n", "        found_broken_file = False\n", "        for file in problem.fixing_change.changed_files:\n", "            #if isinstance(file, Modified) and file.after.code == file.before.code:\n", "            #    found_broken_file = True\n", "            #    break\n", "            if isinstance(file, Modified) and file.before.path != file.after.path:\n", "                found_broken_file = True\n", "                break\n", "            if not isinstance(file, Modified):\n", "                found_broken_file = True\n", "                break\n", "        if found_broken_file:\n", "            num_problems_with_broken_diffs += 1\n", "\n", "    return num_problems_with_broken_diffs\n", "\n", "def count_num_problems_with_broken_breaking_diffs(problems_bytes):\n", "    problems = compressed_loads(problems_bytes, compression='gzip')\n", "    num_problems_with_broken_diffs = 0\n", "    for problem in problems:\n", "        found_broken_file = False\n", "        for file in problem.breaking_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.before.path == file.after.path:\n", "                found_broken_file = True\n", "                break\n", "        if found_broken_file:\n", "            num_problems_with_broken_diffs += 1\n", "\n", "    return num_problems_with_broken_diffs\n", "\n", "ds = ds.limit(1000)\n", "#ds = ds.withColumn('num_problems_with_broken_breaking_diffs', F.udf(count_num_problems_with_broken_breaking_diffs, IntegerType())(<PERSON><PERSON>col('problems')))\n", "ds = ds.withColumn('num_problems_with_broken_fixing_diffs', F.udf(count_num_problems_with_broken_fixing_diffs, IntegerType())(<PERSON><PERSON>col('problems')))\n", "\n", "# print counts\n", "#num_problems = ds.select(F.sum('num_problems')).collect()[0][0]\n", "#num_problems_with_broken_breaking_diffs = ds.select(F.sum('num_problems_with_broken_breaking_diffs')).collect()[0][0]\n", "num_problems_with_broken_fixing_diffs = ds.select(F.sum('num_problems_with_broken_fixing_diffs')).collect()[0][0]\n", "#print(f\"num_problems: {num_problems}\")\n", "#print(f\"num_problems_with_broken_breaking_diffs: {num_problems_with_broken_breaking_diffs}\")\n", "print(f\"num_problems_with_broken_fixing_diffs: {num_problems_with_broken_fixing_diffs}\")\n", "\n", "# sample 10 rows with num_problems_with_broken_breaking_diffs > 0\n", "#ds_breaking_diff = ds.filter(ds['num_problems_with_broken_breaking_diffs'] > 0).limit(10).toPandas()\n", "#ds_fixing_diff = ds.filter(ds['num_problems_with_broken_fixing_diffs'] > 0).limit(10).toPandas()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "from research.utils.repo_change_utils import Modified, Deleted\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import repo_change_to_str\n", "study_problem = None\n", "for _, row in ds_fixing_diff.iterrows():\n", "    repo_id = row['repo_id']\n", "    problems = compressed_loads(row['problems'], compression='gzip')\n", "    for problem in problems:\n", "        unchanged_files = []\n", "        for file in problem.fixing_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code:\n", "                unchanged_files.append(file.after.path)\n", "            #if isinstance(file, Modified) and file.before.path != file.after.path:\n", "            #    unchanged_files.append(file.after.path)\n", "            #if not isinstance(file, Modified):\n", "            #    unchanged_files.append(file.after.path)\n", "            \n", "\n", "        if len(unchanged_files) > 0:\n", "            if study_problem is None:\n", "                study_problem = problem\n", "            print(f\"Repo ID {repo_id}, PR number: {problem.pr_number}\")\n", "            uri_base = f\"https://github.com/{repo_id}/commit/\"\n", "\n", "            print(problem.debugging_info.replace(';', '\\n'))\n", "            print(f\"Unchanged files: {unchanged_files}\")\n", "            print('----------------')\n", "\n", "            print('----\\n FIXING \\n----\\n')\n", "            print(repo_change_to_str(problem.fixing_change))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(study_problem.fixing_change.changed_files[0].before.path)\n", "print(study_problem.fixing_change.changed_files[0].after.path)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "from research.utils.repo_change_utils import Modified, Deleted\n", "for _, row in list(ds_breaking_diff.iterrows())[:100]:\n", "    repo_id = row['repo_id']\n", "    problems = compressed_loads(row['problems'], compression='gzip')\n", "    for problem in problems:\n", "        unchanged_files = []\n", "        for file in problem.breaking_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.before.path == file.after.path:\n", "                unchanged_files.append(file)\n", "\n", "        if len(unchanged_files) > 0:\n", "            print(f\"Repo ID {repo_id}, PR number: {problem.pr_number}\")\n", "            uri_base = f\"https://github.com/{repo_id}/commit/\"\n", "\n", "            pr_url = f\"https://github.com/{repo_id}/pull/{problem.pr_number}\"\n", "            print(problem.debugging_info.replace(';', '\\n'))\n", "            print(f\"Unchanged files:\")\n", "            for file in unchanged_files:\n", "                print(f\"{file.before.path} -> {file.after.path}\")\n", "\n", "            print(f\"All changed files: {[p.before.path if isinstance(p, Deleted) else p.after.path for p in problem.breaking_change.changed_files]}\")\n", "            print('----------------')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### look at examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "#spark = create_cpu_spark(10)\n", "\n", "# ds_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits_fixed3'\n", "ds_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage5_with_filtered_logs_fixed3/'\n", "#ds = spark.read.parquet(ds_path)\n", "\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import repo_change_to_str\n", "from compress_pickle import loads as compressed_loads\n", "from research.utils.repo_change_utils import Modified, Deleted\n", "for _, row in ds.limit(1).toPandas().iterrows():\n", "    repo_id = row['repo_id']\n", "    problems = compressed_loads(row['problems'], compression='gzip')\n", "    for problem in problems[:3]:\n", "        print('---- NEW PROBLEM ----')\n", "        print(f\"Repo ID {repo_id}, PR number: {problem.pr_number}\")\n", "        uri_base = f\"https://github.com/{repo_id}/commit/\"\n", "        pr_url = f\"https://github.com/{repo_id}/pull/{problem.pr_number}\"\n", "        print(f\"PR URL: {pr_url}\")\n", "        print(problem.debugging_info.replace(';', '\\n'))\n", "        print('----\\n FIXING \\n----\\n')\n", "        print('----------------')\n", "        print('\\n'.join([str(file.before.path) if isinstance(file, Deleted) else str(file.after.path) for file in problem.fixing_change.changed_files]))\n", "        print('----------------')\n", "        print('----\\n BREAKING \\n----\\n')\n", "        print('\\n'.join([str(file.before.path) if isinstance(file, Deleted) else str(file.after.path) for file in problem.breaking_change.changed_files]))\n", "        print('----------------')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["problem.logs[0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["332"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.core import utils_for_file as utils\n", "\n", "path= '/mnt/efs/augment/data/eval/autofix/commits-v14_fixed4.diffs.jsonl.zst'\n", "# load zst jsonl\n", "data = utils.read_jsonl_zst(path)\n", "\n", "data[0]['data'][0]['pr_meta']['pr_number']"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}