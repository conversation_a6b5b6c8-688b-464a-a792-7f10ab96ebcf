{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "import pyspark.sql.functions as F\n", "from tqdm import tqdm\n", "\n", "from research.utils.repo_change_utils import RepoChange\n", "\n", "cpu_spark = create_cpu_spark()\n", "\n", "dataset_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds_raw = cpu_spark.read.parquet(dataset_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import dumps as compressed_dumps\n", "from compress_pickle import loads as compressed_loads\n", "from research.utils.repo_change_utils import Modified\n", "from pyspark.sql.types import BinaryType\n", "from pyspark.sql.types import IntegerType\n", "\n", "def filter_problems_with_undecodable_files(problems_bytes):\n", "    problems = compressed_loads(problems_bytes, compression='gzip')\n", "    new_problems = []\n", "    for problem in problems:\n", "        found_undecodable_file = False\n", "        for file in problem.fixing_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == \"\":\n", "                found_undecodable_file = True\n", "                break\n", "        for file in problem.breaking_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.code == file.before.code and file.after.code == \"\":\n", "                found_undecodable_file = True\n", "                break\n", "        if not found_undecodable_file:\n", "            new_problems.append(problem)\n", "\n", "    return compressed_dumps(new_problems, compression='gzip')\n", "ds = ds_raw.withColumn('problems', F.udf(filter_problems_with_undecodable_files, BinaryType())(F<PERSON>col('problems')))\n", "ds_local = ds.limit(500).toPandas()\n", "\n", "ds_local"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["problem = problems = compressed_loads(ds_local.iloc[0]['problems'], compression='gzip')[0]\n", "problem.passing_child.sha"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "from research.utils.repo_change_utils import Modified\n", "\n", "sha_to_logs = {}\n", "\n", "i = 0\n", "for row in ds.toLocalIterator(prefetchPartitions=True):\n", "    i += 1\n", "    if i < 50:\n", "        continue\n", "    if i > 500:\n", "        break\n", "\n", "    print(row['repo_id'])\n", "    problems = compressed_loads(row['problems'], compression='gzip')\n", "    for problem in problems:\n", "        sha_to_logs[problem.passing_child.sha] = problem.logs\n", "        print(problem.passing_child.sha)\n", "    print('------------')\n", "\n", "    # https://github.com/ToFuProject/spectrally/commit/177541ed53fddbca061acca9bdb89ea1b28850ab"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sha_to_logs['e640f5507a2cf8546432c3e800fb5e581702df0a'][0])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}