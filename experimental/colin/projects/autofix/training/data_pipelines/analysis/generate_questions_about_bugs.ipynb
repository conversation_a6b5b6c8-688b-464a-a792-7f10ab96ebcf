{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "from compress_pickle import dumps as compressed_dumps, loads as compressed_loads\n", "from pyspark.sql import functions as F\n", "from research.utils.repo_change_utils import RepoChange\n", "from base.diff_utils.diff_formatter import (\n", "    format_file_changes_with_ranges,\n", "    format_tokenized_hunks,\n", "    tokenize_diff_hunks,\n", ")\n", "from research.utils.repo_change_utils import FileTuple\n", "from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-research-gsc\"\n", "MODEL_NAME = \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERATURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 2\n", "\n", "ANTHROPIC_CLIENT = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERATURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_cpu_spark()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds = spark.read.parquet(\"/mnt/efs/augment/user/colin/bugfix_localization_model/v12/stagepre7_eval\").limit(10).toPandas()\n", "\n", "ds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for repo_idx in range(10):\n", "    edit_localization_problems = compressed_loads(ds.iloc[repo_idx][\"pickled_results\"], compression=\"gzip\")\n", "    for example_idx in range(len(edit_localization_problems)):\n", "        if example_idx > 5:\n", "            break\n", "        edit_localization_problem = edit_localization_problems[example_idx][0]\n", "        logs = edit_localization_problem.instructions\n", "        breaking_diff = edit_localization_problem.past_to_wip_repo_change\n", "        file_changes = tuple(\n", "            change.map(FileTuple.to_file)\n", "            for change in breaking_diff.changed_files\n", "        )\n", "        diff_hunks = format_file_changes_with_ranges(\n", "            changes=file_changes,\n", "            diff_context_lines=3,\n", "            use_smart_header=True,\n", "            # TODO(arun): Support \"ignore_whitespace\" = True in diff_utils\n", "        )\n", "        diff_str = \"\"\n", "        for diff_hunk in diff_hunks:\n", "            body_text = diff_hunk.text\n", "            header_text = diff_hunk.path_header(True)\n", "            diff_str += header_text + \"\\n\"\n", "            diff_str += body_text + \"\\n\"\n", "\n", "        prompt = f\"\"\"\\\n", "        I am engineer that is trying to fix a test failure in my codebase. I have access to failure logs \n", "        as well as the changes I recently made, which likely caused the test failure. I am trying to generate\n", "        some great questions about our codebase to ask my colleagues, so that I can figure out how to fix the test failure. Can you help?\n", "\n", "        Here are the failure logs:\n", "        {logs}\n", "\n", "        Here are the changes to the codebase I recently made, which likely caused the test failure:\n", "        {diff_str}\n", "\n", "        Can you help me generate one to three great questions about our codebase to ask my colleagues, so that I can figure out how to fix the test failure?\n", "        Just respond with the questions and nothing else.\n", "        \"\"\"\n", "\n", "        try:\n", "            response = ''.join([chunk.text for chunk in ANTHROPIC_CLIENT.generate_response_stream(\n", "                cur_message=prompt,\n", "                messages=[],\n", "                system_prompt=\"You are an expert AI software engineer.\",\n", "            )])\n", "        except Exception as e:\n", "            continue\n", "\n", "        print(\"=\" * 128)\n", "        print(\"logs\")\n", "        print(\"=\" * 128)\n", "        print(logs)\n", "        print(\"=\" * 128)\n", "        print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for repo_idx in range(10):\n", "    edit_localization_problems = compressed_loads(ds.iloc[repo_idx][\"pickled_results\"], compression=\"gzip\")\n", "    for example_idx in range(len(edit_localization_problems)):\n", "        if example_idx > 5:\n", "            break\n", "        edit_localization_problem = edit_localization_problems[example_idx][0]\n", "        logs = edit_localization_problem.instructions\n", "        breaking_diff = edit_localization_problem.past_to_wip_repo_change\n", "        file_changes = tuple(\n", "            change.map(FileTuple.to_file)\n", "            for change in breaking_diff.changed_files\n", "        )\n", "        diff_hunks = format_file_changes_with_ranges(\n", "            changes=file_changes,\n", "            diff_context_lines=3,\n", "            use_smart_header=True,\n", "            # TODO(arun): Support \"ignore_whitespace\" = True in diff_utils\n", "        )\n", "        diff_str = \"\"\n", "        for diff_hunk in diff_hunks:\n", "            body_text = diff_hunk.text\n", "            header_text = diff_hunk.path_header(True)\n", "            diff_str += header_text + \"\\n\"\n", "            diff_str += body_text + \"\\n\"\n", "\n", "        prompt = f\"\"\"\\\n", "        I am engineer that is trying to fix a test failure in my codebase. I have access to failure logs \n", "        as well as the changes I recently made, which likely caused the test failure. I am trying to look more carefully at test files\n", "        that include the test failures, in order to figure out how to fix them. Can you help me identify what files to look at?\n", "\n", "        Here are the failure logs:\n", "        {logs}\n", "\n", "        Here are the changes to the codebase I recently made, which likely caused the test failure:\n", "        {diff_str}\n", "\n", "        I am trying to look more carefully at test files that include the test failures, in order to figure out how to fix them. \n", "        Can you help me identify what files to look at? Format the file names as a question like \"Can you show me '/path/to/file.py' and '/path/to/file2.rs'?\"\n", "        \"\"\"\n", "\n", "        try:\n", "            response = ''.join([chunk.text for chunk in ANTHROPIC_CLIENT.generate_response_stream(\n", "                cur_message=prompt,\n", "                messages=[],\n", "                system_prompt=\"You are an expert AI software engineer.\",\n", "            )])\n", "        except Exception as e:\n", "            continue\n", "\n", "        print(\"=\" * 128)\n", "        print(\"logs\")\n", "        print(\"=\" * 128)\n", "        print(logs)\n", "        print(\"=\" * 128)\n", "        print(response)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}