

import json
from pathlib import Path
from google.cloud import storage
import pandas as pd
import logging
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage0_grouped_commits import stage0_grouped_commits
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage1_generate_dehydrated_problems import stage1_generate_dehydrated_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage2_hydrate_logs import stage2_hydrate_logs
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage3_explode_problems import stage3_explode_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage4_hydrate_commits import stage4_hydrate_commits
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage5_extract_logs import stage5_extract_logs
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage6_create_labels import stage6_create_retrieval_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage7_holdout_eval import stage7_holdout_eval
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage8_format_data import stage8_format_data
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage9_shuffle_and_export import stage9_shuffle_and_export
from experimental.colin.projects.autofix.training.data_pipelines.utils import configure_root_logger, create_gpu_spark, create_cpu_spark, diff_dicts, file_exists, read_file, save_file
from research.next_edits.edit_localization_stages import CreateRetrievalProblemsConfig

logs_output_base = f"/mnt/efs/augment/user/colin/bugfix_localization_model_XIAOLEI_REPRO/v13-fixed"
configure_root_logger(logs_output_base)
logger = logging.getLogger(__name__)

pipeline_config = {
    "STAGE5_WITH_FILTERED_LOGS_PATH": "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model_XIAOLEI_REPRO/v13-fixed/stage5_with_filtered_logs/",
    "STAGE6_WITH_RETRIEVAL_PATH": "gs://gcp-us1-spark-data/user/colin/bugfix_localization_model_XIAOLEI_REPRO/v13-fixed/stage6_with_retrieval_problems/",

    "stage6_config": {
        "retriever_config": {
            "scorer": {
                "name": "dense_scorer_v2_fbwd",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50",
                "tokenizer_name": "starcoder",
                "cache_dir": "/tmp/augment/cache",
            },
            "chunker": {
                "name": "smart_line_level",
                "max_chunk_chars": 2000,
            },
            "query_formatter": {
                "name": "next_edit_location_query",
                "tokenizer": "starcoder",
                "max_prompt_tokens": 8192,
                "max_instruction_tokens": 4096,
                "use_smart_header": True,
                "deduplicate_identical_paths": True,
                "truncate_instructions_tail": False,
            },
            "document_formatter": {
                "name": "base:ethanol6-embedding-with-path-key",
                "tokenizer": "starcoder",
                "max_tokens": 999,
            },
        },
        "retrieval_strategy": "retrieved",
        "num_retrieved_chunks": 128,
        "ignore_whitespace_changes": True,
        "max_gold_chunks_per_problem": 1000, # TODO: update to 7 and see how much data is lost
        "ignore_problems_with_insufficient_chunks": False, #TODO: update to True and see how much data is lost
    },
}

VALID_STAGES = set([6])

def run_pipeline(run_local=False):
    logger.info(f"Pipeline config: {pipeline_config}")
    logger.info(f"Logs output base: {logs_output_base}")

    gpu_spark = create_gpu_spark(use_h100=True)
    stage6_create_retrieval_problems(
        spark=gpu_spark,
        input_path = pipeline_config["STAGE5_WITH_FILTERED_LOGS_PATH"],
        output_path=pipeline_config["STAGE6_WITH_RETRIEVAL_PATH"],
        config=CreateRetrievalProblemsConfig(
            **pipeline_config["stage6_config"],
        ),
        run_local=run_local,
    )                                                                                                                                                                                                                                                                                     

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run pipeline with specified active stages")
    parser.add_argument("--run-local", action="store_true", help="Run stages locally")
    args = parser.parse_args()
    run_local = args.run_local

    run_pipeline(run_local=run_local)