{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_path = \"/mnt/efs/augment/data/processed/autofix/v2/stage4_indexed_dataset/train\"\n", "data_path = \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v13-fixed/\"\n", "\n", "# load up indexed dataset\n", "from megatron.data import indexed_dataset\n", "# import starcoder 2 tokenizer\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "train_dataset = indexed_dataset.make_dataset(data_path, impl=\"mmap\", skip_warmup=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_examples = len(train_dataset)\n", "num_examples_with_no_change = 0\n", "for i in range(len(train_dataset)):\n", "    prompt = tokenizer.detokenize([abs(v) for v in train_dataset[i]])\n", "    if \"<|has_change|>\" not in prompt:\n", "        num_examples_with_no_change += 1\n", "\n", "print(f\"num_examples_with_no_change: {num_examples_with_no_change}\")\n", "print(f\"total_examples: {total_examples}\")\n", "print(f\"percent_no_change: {num_examples_with_no_change / total_examples}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}