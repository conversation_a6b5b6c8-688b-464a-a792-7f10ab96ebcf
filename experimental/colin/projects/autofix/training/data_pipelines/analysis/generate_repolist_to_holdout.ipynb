{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "from pyspark.sql import functions as F"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark = create_cpu_spark()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "num_problems: 227625\n", "num_unique_repos: 27974\n", "num_missing_commits: 18982\n", "min_file_ct: 0.0\n", "perc25_file_ct: 120.0\n", "median_file_ct: 392.0\n", "perc75_file_ct: 1284.5\n", "perc80_file_ct: 3475.0\n", "perc90_file_ct: 3478.0\n", "max_file_ct: 22019.0\n", "num_errors: 248\n", "\"\"\"\n", "df = spark.read.parquet(\n", "    \"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/\"\n", ")\n", "\n", "df.printSchema()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### V3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = df.filter(df[\"median_file_ct\"] > 1000)\n", "repos = df.select(\"repo_id\").distinct().orderBy(F.rand()).limit(500).toPandas()['repo_id'].tolist()\n", "\n", "with open(\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names_v3.txt\", \"w\") as f:\n", "    f.write(\"\\n\".join(repos))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(\n", "    \"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/\"\n", ")\n", "\n", "# get total num problems\n", "total_num_problems = df.select(F.sum(\"num_problems\")).collect()[0][0]\n", "print(f\"total_num_problems: {total_num_problems}\")\n", "\n", "# get num_problems with less than 25 files\n", "num_problems_with_less_than_25_files = df.filter(df[\"median_file_ct\"] < 25).select(F.sum(\"num_problems\")).collect()[0][0]\n", "print(f\"num_problems_with_less_than_25_files: {num_problems_with_less_than_25_files}\")\n", "\n", "# get num_problems with more than 250 files\n", "num_problems_with_more_than_250_files = df.filter(df[\"median_file_ct\"] > 250).select(F.sum(\"num_problems\")).collect()[0][0]\n", "print(f\"num_problems_with_more_than_250_files: {num_problems_with_more_than_250_files}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(\n", "    \"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/\"\n", ")\n", "\n", "perc2_5_file_ct = df.approxQuantile(\"median_file_ct\", [0.025], 0.001)[0]\n", "print(f\"perc2_5_file_ct: {perc2_5_file_ct}\")\n", "perc5_file_ct = df.approxQuantile(\"median_file_ct\", [0.05], 0.001)[0]\n", "print(f\"perc5_file_ct: {perc5_file_ct}\")\n", "perc10_file_ct = df.approxQuantile(\"median_file_ct\", [0.10], 0.001)[0]\n", "print(f\"perc10_file_ct: {perc10_file_ct}\")\n", "perc15_file_ct = df.approxQuantile(\"median_file_ct\", [0.15], 0.001)[0]\n", "print(f\"perc15_file_ct: {perc15_file_ct}\")\n", "perc20_file_ct = df.approxQuantile(\"median_file_ct\", [0.20], 0.001)[0]\n", "print(f\"perc20_file_ct: {perc20_file_ct}\")\n", "perc90_file_ct = df.approxQuantile(\"median_file_ct\", [0.90], 0.001)[0]\n", "print(f\"perc90_file_ct: {perc90_file_ct}\")\n", "perc95_file_ct = df.approxQuantile(\"median_file_ct\", [0.95], 0.001)[0]\n", "print(f\"perc95_file_ct: {perc95_file_ct}\")\n", "perc975_file_ct = df.approxQuantile(\"median_file_ct\", [0.975], 0.001)[0]\n", "print(f\"perc975_file_ct: {perc975_file_ct}\")\n", "perc99_file_ct = df.approxQuantile(\"median_file_ct\", [0.99], 0.001)[0]\n", "print(f\"perc99_file_ct: {perc99_file_ct}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_names = []\n", "with open(\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names_v3.txt\", \"r\") as f:\n", "    repo_names = f.read().splitlines()\n", "\n", "# remove duplicate repo_id\n", "df = df.dropDuplicates([\"repo_id\"])\n", "df = df.filter(df[\"repo_id\"].isin(repo_names))\n", "\n", "\n", "min_file_ct = df.select(F.min(\"median_file_ct\")).collect()[0][0]\n", "max_file_ct = df.select(F.max(\"median_file_ct\")).collect()[0][0]\n", "perc25_file_ct = df.approxQuantile(\"median_file_ct\", [0.25], 0.001)[0]\n", "perc50_file_ct = df.approxQuantile(\"median_file_ct\", [0.50], 0.001)[0]\n", "perc75_file_ct = df.approxQuantile(\"median_file_ct\", [0.75], 0.001)[0]\n", "\n", "print(f\"min_file_ct: {min_file_ct}\")\n", "print(f\"max_file_ct: {max_file_ct}\")\n", "print(f\"perc25_file_ct: {perc25_file_ct}\")\n", "print(f\"perc50_file_ct: {perc50_file_ct}\")\n", "print(f\"perc75_file_ct: {perc75_file_ct}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(\n", "    \"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/\"\n", ")\n", "df = df.filter(df[\"median_file_ct\"] > 1000)\n", "df = df.filter(df[\"median_file_ct\"] < 2000)\n", "repos = df.select(\"repo_id\").distinct().orderBy(F.rand()).limit(500).toPandas()['repo_id'].tolist()\n", "\n", "with open(\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names_v4.txt\", \"w\") as f:\n", "    f.write(\"\\n\".join(repos))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names_v4.txt\", \"r\") as f:\n", "    repo_names = f.read().splitlines()\n", "\n", "print(len(repo_names))\n", "print(repo_names[:5])\n", "\n", "df = spark.read.parquet(\n", "    \"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v13-fixed/stage4_with_hydrated_commits_v3/\"\n", ")\n", "df = df.filter(df[\"repo_id\"].isin(repo_names))\n", "# num problems\n", "num_problems = df.select(F.sum(\"num_problems\")).collect()[0][0]\n", "print(f\"num_problems: {num_problems}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}