{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["\n", "import os\n", "from pathlib import Path\n", "import shutil\n", "import subprocess\n", "import tempfile\n", "import time\n", "from typing import Optional\n", "import atexit\n", "import uuid\n", "\n", "from research.utils.repo_change_utils import CommitMeta, iterate_repo_history, repo_change_from_repositories\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "from research.utils.repo_change_utils import FileTuple\n", "from research.utils.repo_change_utils import RepoChange\n", "from research.retrieval.types import Document\n", "\n", "def repo_change_to_str(repo_change: RepoChange) -> str:\n", "    file_changes = tuple(\n", "        change.map(FileTuple.to_file)\n", "        for change in repo_change.changed_files\n", "    )\n", "    diff_hunks = format_file_changes_with_ranges(\n", "        changes=file_changes,\n", "        diff_context_lines=3,\n", "        diff_filter=lambda x: True,\n", "        use_smart_header=True,\n", "    )\n", "    diff_str = \"\"\n", "    for hunk in reversed(diff_hunks):\n", "        body_text = hunk.text\n", "        header_text = hunk.path_header(deduplicate_identical_paths=True)\n", "        diff_str += header_text + body_text\n", "\n", "    return diff_str\n", "\n", "def get_merge_base(repo_dir: Path, branch1: str, branch2: str) -> Optional[str]:\n", "    \"\"\"Find the merge base between two branches.\n", "    \n", "    Args:\n", "        repo_dir: Path to the git repository\n", "        branch1: First branch name or commit SHA\n", "        branch2: Second branch name or commit SHA\n", "        \n", "    Returns:\n", "        The merge base commit SHA, or None if not found\n", "    \"\"\"\n", "    try:\n", "        result = subprocess.run(\n", "            [\"git\", \"-C\", str(repo_dir), \"merge-base\", \"--all\", branch1, branch2],\n", "            check=True,\n", "            capture_output=True,\n", "            text=True\n", "        )\n", "        cmd_str = ' '.join([\"git\", \"-C\", str(repo_dir), \"merge-base\", \"--all\", branch1, branch2])\n", "        print(f\"merge_base: {result.stdout}, {cmd_str}\")\n", "        assert False\n", "        return result.stdout.strip()\n", "    except subprocess.CalledProcessError:\n", "        return None\n", "\n", "def get_branch_parent_sha(repo_dir: Path, commit_sha: str, branch_name: str) -> Optional[str]:\n", "    \"\"\"Get the parent SHA of a commit, only considering commits on the specified branch.\n", "    \n", "    Args:\n", "        repo_dir: Path to the git repository\n", "        commit_sha: The commit SHA to get the parent of\n", "        branch_name: The branch name to restrict history to\n", "        \n", "    Returns:\n", "        The parent commit SHA that's on the branch, or None if not found\n", "    \"\"\"\n", "    try:\n", "        # First verify the commit is on the branch\n", "        result = subprocess.run(\n", "            [\"git\", \"-C\", str(repo_dir), \"branch\", \"--contains\", commit_sha],\n", "            check=True,\n", "            capture_output=True,\n", "            text=True\n", "        )\n", "        if branch_name not in result.stdout:\n", "            return None\n", "            \n", "        # Get the commit log on the specific branch\n", "        result = subprocess.run(\n", "            [\n", "                \"git\", \"-C\", str(repo_dir), \"log\",\n", "                \"--first-parent\",  # Follow only first parent\n", "                branch_name,  # Stay on specific branch\n", "                \"--ancestry-path\",  # Only show commits that are ancestors\n", "                f\"{commit_sha}^..{commit_sha}\",  # Range from parent to commit\n", "                \"--format=%P\",  # Print parent hashes\n", "                \"-n\", \"1\"  # Only get one entry\n", "            ],\n", "            check=True,\n", "            capture_output=True,\n", "            text=True\n", "        )\n", "        parents = result.stdout.strip().split()\n", "        return parents[0] if parents else None\n", "        \n", "    except subprocess.CalledProcessError:\n", "        return None\n", "\n", "def repro_problem(\n", "    repo_name,\n", "    url,\n", "    fixing_child_sha,\n", "    parent_sha,\n", "    master_branch_name\n", ") -> tuple[str, str, list[Document]]:\n", "    \"\"\"Generate the breaking and fixing diff for a given problem.\n", "    \n", "    Returns:\n", "        The breaking diff as a string\n", "        The fixing diff as a string\n", "        The repo filestate right before the fixing diff as a list of Documents\n", "    \"\"\"\n", "    \n", "    tmp_dir = tempfile.mkdtemp(\"colin\")\n", "    atexit.register(lambda: shutil.rmtree(tmp_dir, ignore_errors=True))\n", "    subprocess.run(\n", "        [\n", "            \"git\",\n", "            \"clone\",\n", "            url,\n", "            tmp_dir,\n", "        ],\n", "        check=True,\n", "    )\n", "\n", "    # then fetch a specific commit\n", "    subprocess.run(\n", "        [\n", "            \"git\",\n", "            \"-C\",\n", "            tmp_dir,\n", "            \"fetch\",\n", "            \"origin\",\n", "            fixing_child_sha,\n", "        ],\n", "        check=True,\n", "    )\n", "\n", "    merge_base_sha = get_merge_base(Path(tmp_dir), parent_sha, master_branch_name)\n", "    assert merge_base_sha is not None\n", "\n", "    merge_base_parent_sha = get_branch_parent_sha(Path(tmp_dir), merge_base_sha, master_branch_name)\n", "    assert merge_base_sha is not None\n", "\n", "    fixing_child_commit_meta = CommitMeta(\n", "        sha=fixing_child_sha,\n", "        parents=[parent_sha],\n", "        children=[],\n", "        message=\"\",\n", "        repo_name=repo_name,\n", "    )\n", "    parent_commit_meta = CommitMeta(\n", "        sha=parent_sha,\n", "        parents=[],\n", "        children=[fixing_child_sha],\n", "        message=\"\",\n", "        repo_name=repo_name,\n", "    )\n", "\n", "\n", "    merge_base_commit_meta = CommitMeta(\n", "        sha=merge_base_sha,\n", "        parents=[merge_base_parent_sha],\n", "        children=[],\n", "        message=\"\",\n", "        repo_name=repo_name,\n", "    )\n", "    merge_base_parent_commit_meta = CommitMeta(\n", "        sha=merge_base_parent_sha,\n", "        parents=[],\n", "        children=[merge_base_sha],\n", "        message=\"\",\n", "        repo_name=repo_name,\n", "    )\n", "\n", "    print(f\"merge_base_sha: {merge_base_sha}\")\n", "    print(f\"merge_base_parent_sha: {merge_base_parent_sha}\")\n", "\n", "\n", "    fixing_diff_repo_change = list(iterate_repo_history(\n", "        Path(tmp_dir),\n", "        history=[parent_commit_meta, fixing_child_commit_meta],\n", "        is_source_file=lambda x: True,\n", "    ))[0]\n", "\n", "    merge_base_repo_change = list(iterate_repo_history(\n", "        Path(tmp_dir),\n", "        [merge_base_parent_commit_meta, merge_base_commit_meta],\n", "        is_source_file=lambda x: True,\n", "    ))[0]\n", "\n", "    breaking_diff_before_files = merge_base_repo_change.after_repo()\n", "    breaking_diff_after_files = fixing_diff_repo_change.before_repo()\n", "    breaking_diff_repo_change = repo_change_from_repositories(\n", "        breaking_diff_before_files,\n", "        breaking_diff_after_files,\n", "    )\n", "\n", "    breaking_diff_str = repo_change_to_str(breaking_diff_repo_change)\n", "    fixing_diff_str = repo_change_to_str(fixing_diff_repo_change)\n", "\n", "    repo_files = [\n", "        Document(\n", "            text=contents,\n", "            id=str(uuid.uuid4()),\n", "            path=path,\n", "            meta={},\n", "        )\n", "        for path, contents in fixing_diff_repo_change.before_files.items()\n", "    ]\n", "\n", "    return breaking_diff_str, fixing_diff_str, repo_files\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name = \"awslabs/graphstorm\"\n", "url = f\"https://github.com/{repo_name}\"\n", "\n", "# View the fixing commit at https://github.com/awslabs/graphstorm/commit/e640f5507a2cf8546432c3e800fb5e581702df0a\n", "fixing_child_sha = \"e640f5507a2cf8546432c3e800fb5e581702df0a\"\n", "parent_sha = \"4aa434c5609bba16a0faacd07e35582954e9955b\"\n", "master_branch_name = \"main\"\n", "logs_path = \"/mnt/efs/augment/user/colin/autofix_example1_logs_michiel.txt\"\n", "\n", "breaking_diff_str, fixing_diff_str, repo_files = repro_problem(\n", "    repo_name,\n", "    url,\n", "    fixing_child_sha,\n", "    parent_sha,\n", "    master_branch_name\n", ")\n", "\n", "with open(logs_path) as f:\n", "    logs = f.read()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(breaking_diff_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(fixing_diff_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_name = \"ManassehV2/aptarapi\"\n", "url = f\"https://github.com/{repo_name}\"\n", "\n", "# View the fixing commit at https://github.com/awslabs/graphstorm/commit/e640f5507a2cf8546432c3e800fb5e581702df0a\n", "fixing_child_sha = \"a198b5baaf9a3e11ba779ebdc654553cb179e243\"\n", "parent_sha = \"099e965977af02f7353867da7729d9c21ea0d219\"\n", "master_branch_name = \"master\"\n", "logs_path = \"/mnt/efs/augment/user/colin/autofix_example1_logs_michiel.txt\"\n", "\n", "breaking_diff_str, fixing_diff_str, repo_files = repro_problem(\n", "    repo_name,\n", "    url,\n", "    fixing_child_sha,\n", "    parent_sha,\n", "    master_branch_name\n", ")\n", "\n", "with open(logs_path) as f:\n", "    logs = f.read()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(breaking_diff_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(fixing_diff_str)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}