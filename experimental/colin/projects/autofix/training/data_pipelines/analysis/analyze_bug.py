from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark
spark = create_cpu_spark(100)
ds_path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage5_with_filtered_logs_fixed3/'
ds = spark.read.parquet(ds_path)

from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads
from research.utils.repo_change_utils import Modified
from pyspark.sql.types import BinaryType
from pyspark.sql.types import IntegerType
from pyspark.sql import functions as F

def count_num_problems_with_broken_fixing_diffs(problems_bytes):
    problems = compressed_loads(problems_bytes, compression='gzip')
    num_problems_with_broken_diffs = 0
    for problem in problems:
        found_broken_file = False
        for file in problem.fixing_change.changed_files:
            if isinstance(file, Modified) and file.after.code == file.before.code:
                found_broken_file = True
                break
            if isinstance(file, Modified) and file.before.path != file.after.path:
                found_broken_file = True
                break
            if not isinstance(file, Modified):
                found_broken_file = True
                break
        if found_broken_file:
            num_problems_with_broken_diffs += 1

    return num_problems_with_broken_diffs

def count_num_problems_with_broken_breaking_diffs(problems_bytes):
    problems = compressed_loads(problems_bytes, compression='gzip')
    num_problems_with_broken_diffs = 0
    for problem in problems:
        found_broken_file = False
        for file in problem.breaking_change.changed_files:
            if isinstance(file, Modified) and file.after.code == file.before.code and file.before.path == file.after.path:
                found_broken_file = True
                break
        if found_broken_file:
            num_problems_with_broken_diffs += 1

    return num_problems_with_broken_diffs

ds = ds.limit(1000)
ds = ds.withColumn('num_problems_with_broken_breaking_diffs', F.udf(count_num_problems_with_broken_breaking_diffs, IntegerType())(F.col('problems')))
ds = ds.withColumn('num_problems_with_broken_fixing_diffs', F.udf(count_num_problems_with_broken_fixing_diffs, IntegerType())(F.col('problems')))

# print counts
num_problems = ds.select(F.sum('num_problems')).collect()[0][0]
num_problems_with_broken_breaking_diffs = ds.select(F.sum('num_problems_with_broken_breaking_diffs')).collect()[0][0]
num_problems_with_broken_fixing_diffs = ds.select(F.sum('num_problems_with_broken_fixing_diffs')).collect()[0][0]
print(f"num_problems: {num_problems}")
print(f"num_problems_with_broken_breaking_diffs: {num_problems_with_broken_breaking_diffs}")
print(f"num_problems_with_broken_fixing_diffs: {num_problems_with_broken_fixing_diffs}")


