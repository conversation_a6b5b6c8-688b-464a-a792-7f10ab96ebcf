{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "spark = create_cpu_spark()\n", "\n", "path = 'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v15/stage5_step2_annotate_rca'\n", "df = spark.read.parquet(path)\n", "\n", "df = df.limit(5).to<PERSON><PERSON><PERSON>()\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "\n", "data = compressed_loads(df.iloc[3]['problems'], compression='gzip')[0].rca.asDict()['root_cause'].asDict()\n", "\n", "formatted_changes = ''.join(f\"- at path {change['path']}: {change['change_desc']}\\n\" for change in data['changes'])\n", "\n", "summary = f\"\"\"\n", "Here is a summary of the root cause of the error:\n", "{data['root_cause_desc']}\n", "\n", "Here is a file-by-file description of each of the changes in the breaking diff:\n", "{formatted_changes}\n", "\"\"\"\n", "\n", "\n", "print(summary)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}