{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting KUBECONFIG to /home/<USER>/.kube/config\n", "Skipping bazel build.\n", "Warning: Ignoring non-Spark config property: fs.gs.http.connect-timeout\n", "Warning: Ignoring non-Spark config property: fs.gs.http.read-timeout\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- repo_id: string (nullable = true)\n", " |-- problems: binary (nullable = true)\n", " |-- num_problems: long (nullable = true)\n", " |-- num_missing_commits: long (nullable = true)\n", " |-- iterate_repo_history_times: binary (nullable = true)\n", " |-- error_message: string (nullable = true)\n", " |-- median_file_ct: double (nullable = true)\n", "\n"]}], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark, create_gpu_spark\n", "\n", "spark = create_cpu_spark(100)\n", "\n", "from pyspark.sql import functions as F\n", "\n", "# ds = spark.read.parquet('gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits/')\n", "ds = spark.read.parquet(\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits/part-00666-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet',\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits/part-00667-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet',\n", "    'gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14/stage4_with_hydrated_commits/part-00668-3a930a95-a3e8-4d39-8703-b3d0606ae9b5-c000.zstd.parquet'\n", ")\n", "ds.printSchema()\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["24/11/23 00:11:49 WARN DAGScheduler: Broadcasting large task binary with size 1964.5 KiB\n", "24/11/23 00:12:09 WARN DAGScheduler: Broadcasting large task binary with size 1964.5 KiB\n", "24/11/23 00:12:26 WARN DAGScheduler: Broadcasting large task binary with size 1972.8 KiB\n", "24/11/23 00:12:40 WARN DAGScheduler: Broadcasting large task binary with size 1973.9 KiB\n", "24/11/23 00:12:41 WARN DAGScheduler: Broadcasting large task binary with size 1972.8 KiB\n", "24/11/23 00:12:54 WARN DAGScheduler: Broadcasting large task binary with size 1973.9 KiB\n", "24/11/23 00:12:54 WARN DAGScheduler: Broadcasting large task binary with size 1972.8 KiB\n", "24/11/23 00:13:07 WARN DAGScheduler: Broadcasting large task binary with size 1973.9 KiB\n", "24/11/23 00:13:07 WARN DAGScheduler: Broadcasting large task binary with size 1964.5 KiB\n"]}, {"name": "stdout", "output_type": "stream", "text": ["breaking_diff_min_size: 106\n", "breaking_diff_max_size: 1217285\n", "breaking_diff_25perc_size: 550.0\n", "breaking_diff_50perc_size: 2124.0\n", "breaking_diff_75perc_size: 19640.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/11/23 00:13:22 WARN DAGScheduler: Broadcasting large task binary with size 1964.5 KiB\n", "24/11/23 00:13:40 WARN DAGScheduler: Broadcasting large task binary with size 1972.8 KiB\n", "24/11/23 00:13:55 WARN DAGScheduler: Broadcasting large task binary with size 1973.9 KiB\n", "24/11/23 00:13:55 WARN DAGScheduler: Broadcasting large task binary with size 1972.8 KiB\n", "24/11/23 00:14:09 WARN DAGScheduler: Broadcasting large task binary with size 1973.9 KiB\n", "24/11/23 00:14:09 WARN DAGScheduler: Broadcasting large task binary with size 1972.8 KiB\n", "[Stage 23:=====================================================>(186 + 1) / 187]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["fixing_diff_min_size: 65\n", "fixing_diff_max_size: 61939\n", "fixing_diff_25perc_size: 157.0\n", "fixing_diff_50perc_size: 286.0\n", "fixing_diff_75perc_size: 578.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/11/23 00:14:23 WARN DAGScheduler: Broadcasting large task binary with size 1973.9 KiB\n", "                                                                                \r"]}], "source": ["from compress_pickle import loads as compressed_loads\n", "from base.tokenizers.tiktoken_starcoder_tokenizer import TiktokenStarCoderTokenizer as StarCoderTokenizer\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import repo_change_to_str\n", "from pyspark.sql.types import IntegerType\n", "import numpy as np\n", "\n", "tokenizer = StarCoderTokenizer()\n", "\n", "def compute_breaking_diff_median_size(problems):\n", "    problems = compressed_loads(problems, compression='gzip')\n", "    breaking_diff_token_cts = []\n", "    for problem in problems:\n", "        breaking_diff_token_cts.append(len(tokenizer.tokenize_safe(repo_change_to_str(problem.breaking_change))))\n", "        \n", "    return int(np.median(breaking_diff_token_cts)) if len(breaking_diff_token_cts) > 0 else -1.\n", "\n", "def compute_fixing_diff_median_size(problems):\n", "    problems = compressed_loads(problems, compression='gzip')\n", "    fixing_diff_token_cts = []\n", "    for problem in problems:\n", "        fixing_diff_token_cts.append(len(tokenizer.tokenize_safe(repo_change_to_str(problem.fixing_change))))\n", "    \n", "    return int(np.median(fixing_diff_token_cts)) if len(fixing_diff_token_cts) > 0 else -1.\n", "\n", "ds = ds.withColumn('breaking_diff_median_size', F.udf(compute_breaking_diff_median_size, IntegerType())(<PERSON><PERSON>col('problems')))\n", "ds = ds.withColumn('fixing_diff_median_size', F.udf(compute_fixing_diff_median_size, IntegerType())(<PERSON><PERSON>col('problems')))\n", "\n", "# filter if breaking_diff_median_size or fixing_diff_median_size is -1\n", "ds = ds.filter((<PERSON><PERSON>col('breaking_diff_median_size') > -0.5) & (F.col('fixing_diff_median_size') > -0.5))\n", "\n", "# print distribution of breaking_diff_median_size and fixing_diff_median_size\n", "breaking_diff_min_size = ds.select(F.min('breaking_diff_median_size')).collect()[0][0]\n", "breaking_diff_max_size = ds.select(F.max('breaking_diff_median_size')).collect()[0][0]\n", "breaking_diff_25perc_size = ds.approxQuantile('breaking_diff_median_size', [0.25], 0.001)[0]\n", "breaking_diff_50perc_size = ds.approxQuantile('breaking_diff_median_size', [0.50], 0.001)[0]\n", "breaking_diff_75perc_size = ds.approxQuantile('breaking_diff_median_size', [0.75], 0.001)[0]\n", "print(f\"breaking_diff_min_size: {breaking_diff_min_size}\")\n", "print(f\"breaking_diff_max_size: {breaking_diff_max_size}\")\n", "print(f\"breaking_diff_25perc_size: {breaking_diff_25perc_size}\")\n", "print(f\"breaking_diff_50perc_size: {breaking_diff_50perc_size}\")\n", "print(f\"breaking_diff_75perc_size: {breaking_diff_75perc_size}\")\n", "fixing_diff_min_size = ds.select(F.min('fixing_diff_median_size')).collect()[0][0]\n", "fixing_diff_max_size = ds.select(F.max('fixing_diff_median_size')).collect()[0][0]\n", "fixing_diff_25perc_size = ds.approxQuantile('fixing_diff_median_size', [0.25], 0.001)[0]\n", "fixing_diff_50perc_size = ds.approxQuantile('fixing_diff_median_size', [0.50], 0.001)[0]\n", "fixing_diff_75perc_size = ds.approxQuantile('fixing_diff_median_size', [0.75], 0.001)[0]\n", "print(f\"fixing_diff_min_size: {fixing_diff_min_size}\")\n", "print(f\"fixing_diff_max_size: {fixing_diff_max_size}\")\n", "print(f\"fixing_diff_25perc_size: {fixing_diff_25perc_size}\")\n", "print(f\"fixing_diff_50perc_size: {fixing_diff_50perc_size}\")\n", "print(f\"fixing_diff_75perc_size: {fixing_diff_75perc_size}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}