{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import dumps as compressed_dumps, loads as compressed_loads\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "from research.next_edits.edit_localization_stages import EditLocalizationProblem, EditLocalizationRetrievalProblem\n", "\n", "spark = create_cpu_spark()\n", "VERSION = \"v14\"\n", "ver_suffix = \"_fixed4\"\n", "output_suffix = \"\"\n", "TRAINING_DATA_BASE = f\"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/{VERSION}\"\n", "stage7_eval_path = f\"{TRAINING_DATA_BASE}/stage7_split_eval{ver_suffix}\"\n", "MAX_GOLD_CHUNKS_PER_PROBLEM = 5\n", "MAX_PROBLEMS_PER_REPO = 20\n", "\n", "raw_eval_df = spark.read.parquet(stage7_eval_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from hashlib import sha256\n", "import uuid\n", "\n", "from tqdm import tqdm\n", "from unidiff import UnidiffParseError\n", "\n", "from research.core.utils_for_file import write_json_zst, write_jsonl_zst\n", "from research.utils.repo_change_utils import patchset_from_repo_change\n", "from research.utils.repo_change_utils import Modified\n", "\n", "num_rows = raw_eval_df.count()\n", "print(f'num_rows: {num_rows}')\n", "\n", "sha_to_file = {}\n", "formatted_problems = []\n", "formatted_problems_diffs = []\n", "shard_idx = 0\n", "num_too_many_golds = 0\n", "pr_numbers = []\n", "\n", "num_examples_per_repo = {}\n", "max_examples = 1000\n", "\n", "for repo_problems in tqdm(raw_eval_df.toLocalIterator(prefetchPartitions=True), total=num_rows):\n", "    repo_diff_problems = []\n", "    repo_name = repo_problems['repo_name'].replace('.', '__')\n", "    repo_name = repo_name.replace('/', '-_-')\n", "    if repo_name not in num_examples_per_repo:\n", "        num_examples_per_repo[repo_name] = 0\n", "    group_id = repo_name\n", "    i = 0\n", "\n", "    if len(formatted_problems) > max_examples:\n", "        break\n", "\n", "    for problem_data in compressed_loads(repo_problems['pickled_results'], compression=\"gzip\"):\n", "        localization_problem, retrieval_problem = problem_data\n", "        localization_problem: EditLocalizationProblem\n", "        retrieval_problem: EditLocalizationRetrievalProblem\n", "\n", "        # TEMPORARY\n", "        found_permissions_change = False\n", "        for file in localization_problem.wip_to_future_repo_change.changed_files:\n", "            if isinstance(file, Modified) and file.after.path == file.before.path and file.after.code == file.before.code:\n", "                found_permissions_change = True\n", "                break\n", "        if found_permissions_change:\n", "            print(\"Skipping permissions change!\")\n", "            continue\n", "        # TEMPORARY\n", "\n", "        pr_numbers.append((repo_name, localization_problem.pr_meta.pr_number))\n", "\n", "        if num_examples_per_repo[repo_name] > MAX_PROBLEMS_PER_REPO:\n", "            break\n", "\n", "        if len(formatted_problems) > max_examples:\n", "            break\n", "\n", "        if len(formatted_problems) % 100 == 0:\n", "            print(f'num_examples: {len(formatted_problems)}')\n", "\n", "        if len(retrieval_problem.positive_chunks) > MAX_GOLD_CHUNKS_PER_PROBLEM:\n", "            num_too_many_golds += 1\n", "            continue\n", "\n", "        id = str(uuid.uuid4())\n", "        try:\n", "            past_to_wip_diff = str(patchset_from_repo_change(localization_problem.past_to_wip_repo_change, num_context_lines=0))\n", "            wip_to_future_diff = str(patchset_from_repo_change(localization_problem.wip_to_future_repo_change, num_context_lines=0))\n", "        except UnidiffParseError as e:\n", "            print(f'Error parsing diff for {id}: {e}')\n", "            continue\n", "\n", "        formatted_problems.append(\n", "            dict(\n", "                id = id,\n", "                instruction = localization_problem.instructions,\n", "                past_to_wip_diff = past_to_wip_diff,\n", "                wip_to_future_diff = wip_to_future_diff,\n", "                wip_files = [\n", "                    {\n", "                        \"path\": str(path),\n", "                        \"contents\": text,\n", "                    }\n", "                    for path, text in localization_problem.past_to_wip_repo_change.after_files.items()\n", "                ]\n", "            )\n", "        )\n", "\n", "        repo_diff_problems.append(\n", "            dict(\n", "                id = id,\n", "                instruction = localization_problem.instructions,\n", "                past_to_wip_diff = past_to_wip_diff,\n", "                wip_to_future_diff = wip_to_future_diff,\n", "                wip_files = [\n", "                    sha256(text.encode()).hexdigest()\n", "                    for _, text in localization_problem.past_to_wip_repo_change.after_files.items()\n", "                ],\n", "                commit_meta={'sha': localization_problem.commit_meta.sha, 'message': '', 'repo_name': repo_name},\n", "                pr_meta={'pr_number': localization_problem.pr_meta.pr_number, \"repo_name\": repo_name, \"title\": ''},\n", "                group_id=group_id,\n", "                group_sequence_id=i\n", "            )\n", "        )\n", "        num_examples_per_repo[repo_name] += 1\n", "\n", "        # increment group sequence id after successfully processing a row\n", "        i+=1\n", "\n", "        for path, text in localization_problem.past_to_wip_repo_change.after_files.items():\n", "            sha_to_file[sha256(text.encode()).hexdigest()] = (str(path), text)\n", "    \n", "    formatted_problems_diffs.append(\n", "        {\n", "            \"data\": repo_diff_problems,\n", "            \"group_id\": group_id,\n", "        }\n", "    )\n", "\n", "formatted_files = [\n", "    dict(\n", "        id = shaval,\n", "        path = path,\n", "        contents = text,\n", "    )\n", "    for shaval, (path, text) in sha_to_file.items()\n", "]\n", "\n", "print(f'num_too_many_golds: {num_too_many_golds}')\n", "\n", "\n", "write_jsonl_zst(f'/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix+output_suffix}.jsonl.zst', formatted_problems)\n", "write_jsonl_zst(f'/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix+output_suffix}.diffs.jsonl.zst', formatted_problems_diffs)\n", "write_jsonl_zst(f'/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix+output_suffix}.files.jsonl.zst', formatted_files)\n", "shard_idx += 1\n", "formatted_problems = []\n", "formatted_problems_diffs = []\n", "sha_to_file = {}\n", "\n", "#from collections import Counter\n", "#print(Counter(pr_numbers))\n", "\n", "print(f'num_too_many_golds: {num_too_many_golds}')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "print(Counter(pr_numbers))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}