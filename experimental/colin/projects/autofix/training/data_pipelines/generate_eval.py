from compress_pickle import dumps as compressed_dumps, loads as compressed_loads
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark
from research.next_edits.edit_localization_stages import EditLocalizationProblem, EditLocalizationRetrievalProblem

spark = create_cpu_spark()
VERSION = "v14"
ver_suffix = "_fixed4"
output_suffix = ""
TRAINING_DATA_BASE = f"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/{VERSION}"
stage7_eval_path = f"{TRAINING_DATA_BASE}/stage7_split_eval{ver_suffix}"
MAX_GOLD_CHUNKS_PER_PROBLEM = 5
MAX_PROBLEMS_PER_REPO = 20

raw_eval_df = spark.read.parquet(stage7_eval_path)

from hashlib import sha256
import uuid

from tqdm import tqdm
from unidiff import UnidiffParseError

from research.core.utils_for_file import write_json_zst, write_jsonl_zst
from research.utils.repo_change_utils import patchset_from_repo_change
from research.utils.repo_change_utils import Modified

num_rows = raw_eval_df.count()
print(f'num_rows: {num_rows}')

sha_to_file = {}
formatted_problems = []
formatted_problems_diffs = []
shard_idx = 0
num_too_many_golds = 0
pr_numbers = []

num_examples_per_repo = {}
max_examples = 1000

for repo_problems in tqdm(raw_eval_df.toLocalIterator(prefetchPartitions=True), total=num_rows):
    repo_diff_problems = []
    repo_name = repo_problems['repo_name'].replace('.', '__')
    repo_name = repo_name.replace('/', '-_-')
    if repo_name not in num_examples_per_repo:
        num_examples_per_repo[repo_name] = 0
    group_id = repo_name
    i = 0

    if len(formatted_problems) > max_examples:
        break

    for problem_data in compressed_loads(repo_problems['pickled_results'], compression="gzip"):
        localization_problem, retrieval_problem = problem_data
        localization_problem: EditLocalizationProblem
        retrieval_problem: EditLocalizationRetrievalProblem

        # TEMPORARY
        found_permissions_change = False
        for file in localization_problem.wip_to_future_repo_change.changed_files:
            if isinstance(file, Modified) and file.after.path == file.before.path and file.after.code == file.before.code:
                found_permissions_change = True
                break
        if found_permissions_change:
            print("Skipping permissions change!")
            continue
        # TEMPORARY

        pr_numbers.append((repo_name, localization_problem.pr_meta.pr_number))

        if num_examples_per_repo[repo_name] > MAX_PROBLEMS_PER_REPO:
            break

        if len(formatted_problems) > max_examples:
            break

        if len(formatted_problems) % 100 == 0:
            print(f'num_examples: {len(formatted_problems)}')

        if len(retrieval_problem.positive_chunks) > MAX_GOLD_CHUNKS_PER_PROBLEM:
            num_too_many_golds += 1
            continue

        id = str(uuid.uuid4())
        try:
            past_to_wip_diff = str(patchset_from_repo_change(localization_problem.past_to_wip_repo_change, num_context_lines=0))
            wip_to_future_diff = str(patchset_from_repo_change(localization_problem.wip_to_future_repo_change, num_context_lines=0))
        except UnidiffParseError as e:
            print(f'Error parsing diff for {id}: {e}')
            continue

        formatted_problems.append(
            dict(
                id = id,
                instruction = localization_problem.instructions,
                past_to_wip_diff = past_to_wip_diff,
                wip_to_future_diff = wip_to_future_diff,
                wip_files = [
                    {
                        "path": str(path),
                        "contents": text,
                    }
                    for path, text in localization_problem.past_to_wip_repo_change.after_files.items()
                ]
            )
        )

        repo_diff_problems.append(
            dict(
                id = id,
                instruction = localization_problem.instructions,
                past_to_wip_diff = past_to_wip_diff,
                wip_to_future_diff = wip_to_future_diff,
                wip_files = [
                    sha256(text.encode()).hexdigest()
                    for _, text in localization_problem.past_to_wip_repo_change.after_files.items()
                ],
                commit_meta={'sha': localization_problem.commit_meta.sha, 'message': '', 'repo_name': repo_name},
                pr_meta={'pr_number': localization_problem.pr_meta.pr_number, "repo_name": repo_name, "title": ''},
                group_id=group_id,
                group_sequence_id=i
            )
        )
        num_examples_per_repo[repo_name] += 1

        # increment group sequence id after successfully processing a row
        i+=1

        for path, text in localization_problem.past_to_wip_repo_change.after_files.items():
            sha_to_file[sha256(text.encode()).hexdigest()] = (str(path), text)
    
    formatted_problems_diffs.append(
        {
            "data": repo_diff_problems,
            "group_id": group_id,
        }
    )

formatted_files = [
    dict(
        id = shaval,
        path = path,
        contents = text,
    )
    for shaval, (path, text) in sha_to_file.items()
]

print(f'num_too_many_golds: {num_too_many_golds}')


write_jsonl_zst(f'/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix+output_suffix}.jsonl.zst', formatted_problems)
write_jsonl_zst(f'/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix+output_suffix}.diffs.jsonl.zst', formatted_problems_diffs)
write_jsonl_zst(f'/mnt/efs/augment/data/eval/autofix/commits-{VERSION+ver_suffix+output_suffix}.files.jsonl.zst', formatted_files)
shard_idx += 1
formatted_problems = []
formatted_problems_diffs = []
sha_to_file = {}

#from collections import Counter
#print(Counter(pr_numbers))

print(f'num_too_many_golds: {num_too_many_golds}')
