import dataclasses
import json
import multiprocessing
import os
from pathlib import Path
from typing import Callable, TypeVar, Optional
from google.cloud import storage
import logging
from deepdiff import DeepDiff
from multiprocessing.pool import ThreadPool
import subprocess
import time

from research.data.spark.utils import k8s_session
from research.utils.repo_change_utils import <PERSON>oChang<PERSON>, repo_change_from_repositories, FileTuple
from base.diff_utils.diff_formatter import format_file_changes_with_ranges
from research.utils.repo_change_utils import _run_command 

logger = logging.getLogger(__name__)

class SetEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, set):
            return list(obj)
        if dataclasses.is_dataclass(obj):
            return dataclasses.asdict(obj)
        return json.JSONEncoder.default(self, obj)

def save_file(path, data: dict):
    """Saves data to path, supporting both file storage and object storage."""

    data = json.dumps(data, indent=2, cls=SetEncoder)
    if path.startswith("gs://"):
        bucket_name, blob_name = path[5:].split("/", 1)
        write_to_gcs(bucket_name, blob_name, data)
    else:
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, "w") as f:
            f.write(data)

def read_file(path: str):
    """Reads data from path, supporting both file storage and object storage."""
    if path.startswith("gs://"):
        bucket_name, blob_name = path[5:].split("/", 1)
        return read_from_gcs(bucket_name, blob_name)
    else:
        with open(path, "r") as f:
            return f.read()
        
def ls_dir(path: str):
    """Lists the contents of a directory, supporting both file storage and object storage."""
    if path.startswith("gs://"):
        bucket_name, blob_name = path[5:].split("/", 1)
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blobs = bucket.list_blobs(prefix=blob_name)
        return [blob.name for blob in blobs]
    else:
        return os.listdir(path)

def write_to_gcs(bucket_name, blob_name, data):
    """Write data to a file in Google Cloud Storage."""
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    
    # If the data is a string, encode it to bytes
    if isinstance(data, str):
        data = data.encode('utf-8')
    
    blob.upload_from_string(data)
    
    logger.info(f"File {blob_name} uploaded to {bucket_name}.")

def read_from_gcs(bucket_name, blob_name):
    """Read data from a file in Google Cloud Storage."""
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    
    return blob.download_as_string().decode('utf-8')

def file_exists(path) -> bool:
    if path.startswith("gs://"):
        bucket_name, blob_name = path[5:].split("/", 1)
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(blob_name)
        return blob.exists()
    else:
        return os.path.exists(path)

def create_cpu_spark(max_workers: int = 100):
    spark = k8s_session(
        max_workers=max_workers,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.driver.maxResultSize": "25g",
            "spark.task.maxFailures": "10",
            # add lots of memory
            "spark.executor.pyspark.memory": "1050G",
            "spark.executor.memory": "120G",
            # temporary fix from xiaolei
            "spark.executorEnv.USE_BAZEL_VERSION": "7.4.1"
         
        },
        ephemeral_storage_gb=128,
    )
    return spark

def create_gpu_spark(max_workers: int = 100, use_h100: bool = False):
    spark = k8s_session(
        max_workers=max_workers,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.task.maxFailures": "10",
            # add lots of memory
            #"spark.executor.pyspark.memory": "1050G",
            "spark.task.cpus": "5",
            "spark.executor.cpus": "5",
            # temporary fix from xiaolei
            "spark.executorEnv.USE_BAZEL_VERSION": "7.4.1"
        },
        gpu_type="nvidia-h100-mega-80gb" if use_h100 else "nvidia-l4",
        ephemeral_storage_gb=128,
    )
    return spark

def save_config_to_stage_output_dir(
    stage_output_dir: str,
    config: dict,
):
    output_path = stage_output_dir.rstrip('/') + ".config.json"
    logger.info(f"Saving config to {output_path}")

    if file_exists(output_path):
        saved_config = json.loads(read_file(output_path))
        assert saved_config == config, f"Config has changed since last run. Please update the config file. Diff: {diff_dicts(saved_config, config)}"
        logger.info(f"Config already exists but is unchanged. Continuing with initialization of stage.")
        return

    save_file(output_path, config)

def diff_dicts(dict1, dict2) -> str:
    diff = DeepDiff(dict1, dict2, verbose_level=2)
    if diff:
        return diff.to_json()
    else:
        return "No differences found."
    
T = TypeVar('T')
def run_with_timeout(func: Callable[..., T], args=(), kwargs={}, timeout_duration=30) -> T | None:
    with ThreadPool(processes=1) as pool:
        def _func(args_and_kwargs: tuple[tuple, dict]):
            args, kwargs = args_and_kwargs
            return func(*args, **kwargs)
        
        result = pool.map_async(_func, [(args, kwargs)])
        try:
            return result.get(timeout=timeout_duration)[0]
        except (TimeoutError, multiprocessing.context.TimeoutError):
            return None
        
def gcp_path_to_file_path(path: str) -> str:
    return path.replace("gs://gcp-us1-spark-data/", "/mnt/efs/spark-data/")

def create_stage_logger(name: str, stage_output_dir: str, logs_output_base: str | None = None):
    formatter = logging.Formatter(
        fmt="{asctime} - {levelname} - {message}",
        style="{",
        datefmt="%Y-%m-%d %H:%M"
    )
    logger = logging.getLogger(name)
    logs_output_path = gcp_path_to_file_path(stage_output_dir.rstrip('/') + ".logs")
    if logs_output_base is not None:
        logs_output_path = logs_output_base + "/" + logs_output_path.split("/")[-1]
    file_handler = logging.FileHandler(filename=logs_output_path, mode="a", encoding="utf-8")
    file_handler.setFormatter(formatter)
    logger.setLevel(logging.INFO)  # Set the desired log level
    logger.addHandler(file_handler)
    return logger

def configure_root_logger(logs_output_base: str):
    # Create a formatter
    formatter = logging.Formatter(
        fmt="{asctime} - {levelname} - {message}",
        style="{",
        datefmt="%Y-%m-%d %H:%M"
    )
    # Create a file handler
    Path(logs_output_base + '/logs.txt').parent.mkdir(parents=True, exist_ok=True)
    if not file_exists(logs_output_base + "/logs.txt"):
        with open(logs_output_base + "/logs.txt", "w") as f:
            f.write("")
    file_handler = logging.FileHandler(filename=logs_output_base + "/logs.txt", mode="a", encoding="utf-8")
    file_handler.setFormatter(formatter)
    # Configure the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)  # Set the desired log level
    root_logger.addHandler(file_handler)
    # to also log to console:
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

def get_main_branch_name(repo_dir: Path) -> str:
    """Get the name of the main branch in a git repository.

    Args:
        repo_dir: Path to the git repository
        
    Returns:
        The name of the main branch, or raises an exception if not found
    """
    try:
        result = subprocess.run(
            ["git", "-C", str(repo_dir), "symbolic-ref", "refs/remotes/origin/HEAD"],
            check=True,
            capture_output=True,
            text=True
        )
        return result.stdout.strip().split("refs/remotes/origin/")[1]
    except subprocess.CalledProcessError:
        raise Exception(f"Failed to get main branch name for {repo_dir}")

def get_merge_base(repo_dir: Path, branch1: str, branch2: str) -> str:
    """Find the merge base between two branches.
    
    Args:
        repo_dir: Path to the git repository
        branch1: First branch name or commit SHA
        branch2: Second branch name or commit SHA
        
    Returns:
        The merge base commit SHA, or None if not found
    """
    result = subprocess.run(
        ["git", "-C", str(repo_dir), "merge-base", branch1, branch2],
        check=True,
        capture_output=True,
        text=True
    )
    return result.stdout.strip()

def get_branch_parent_sha(repo_dir: Path, commit_sha: str, branch_name: str) -> str:
    """Get the parent SHA of a commit, only considering commits on the specified branch.
    
    Args:
        repo_dir: Path to the git repository
        commit_sha: The commit SHA to get the parent of
        branch_name: The branch name to restrict history to
        
    Returns:
        The parent commit SHA that's on the branch, or None if not found
    """
    # First verify the commit is on the branch
    result = subprocess.run(
        ["git", "-C", str(repo_dir), "branch", "--contains", commit_sha],
        check=True,
        capture_output=True,
        text=True
    )
    if branch_name not in result.stdout:
        start_time = time.time()
        logger.error(f"Commit {commit_sha} is not on branch {branch_name}. Fetching...")
        _run_command(
            ["git", "fetch", "origin", commit_sha],
            cwd=str(repo_dir),
        )
        logger.error(f"Finished fetching commit {commit_sha} after {time.time() - start_time:.2f} seconds !")

        
    # Get the commit log on the specific branch
    result = subprocess.run(
        [
            "git", "-C", str(repo_dir), "log",
            "--first-parent",  # Follow only first parent
            branch_name,  # Stay on specific branch
            "--ancestry-path",  # Only show commits that are ancestors
            f"{commit_sha}^..{commit_sha}",  # Range from parent to commit
            "--format=%P",  # Print parent hashes
            "-n", "1"  # Only get one entry
        ],
        check=True,
        capture_output=True,
        text=True
    )
    parents = result.stdout.strip().split()
    return parents[0] if parents else None

def diff_repo_changes(
    before_repo_change: RepoChange,
    after_repo_change: RepoChange,
) -> RepoChange:
    """Compute the diff between two repo changes, NOT including the changes in the repo changes themselves.

    ie we grab the after files from the before repo change 
    and the before files from the after repo change,
    and then we diff those.
    """
    diff_before_files = before_repo_change.after_repo()
    diff_after_files = after_repo_change.before_repo()
    return repo_change_from_repositories(
        diff_before_files,
        diff_after_files,
    )

def repo_change_to_str(repo_change: RepoChange) -> str:
    file_changes = tuple(
        change.map(FileTuple.to_file)
        for change in repo_change.changed_files
    )
    diff_hunks = format_file_changes_with_ranges(
        changes=file_changes,
        diff_context_lines=3,
        use_smart_header=True,
    )
    diff_str = ""
    for hunk in reversed(diff_hunks):
        body_text = hunk.text
        header_text = hunk.path_header(deduplicate_identical_paths=True)
        diff_str += header_text + body_text

    return diff_str