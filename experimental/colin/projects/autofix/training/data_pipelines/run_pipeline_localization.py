

import json
from pathlib import Path
from google.cloud import storage
import pandas as pd
import logging
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage0_grouped_commits import stage0_grouped_commits
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage1_generate_dehydrated_problems import stage1_generate_dehydrated_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage1_V2_generate_dehydrated_problems import stage1_generate_dehydrated_problems as stage1_generate_dehydrated_problems_V2
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage2_hydrate_logs import stage2_hydrate_logs
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage3_explode_problems import stage3_explode_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage4_hydrate_commits import stage4_hydrate_commits
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage4_V2_hydrate_commits import stage4_hydrate_commits as stage4_hydrate_commits_V2
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage5_extract_logs import stage5_extract_logs
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage5_step2_annotate_rca import stage5_step2_annotate_rca
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage6_create_labels import stage6_create_retrieval_problems
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage7_holdout_eval import stage7_holdout_eval
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage8_format_data import stage8_format_data
from experimental.colin.projects.autofix.training.data_pipelines.stages_localization.stage9_shuffle_and_export import stage9_shuffle_and_export
from experimental.colin.projects.autofix.training.data_pipelines.utils import configure_root_logger, create_gpu_spark, create_cpu_spark, diff_dicts, file_exists, read_file, save_file
from research.next_edits.edit_localization_stages import CreateRetrievalProblemsConfig

VERSION = "v15"
PR_V2_BASE = "gs://gcp-us1-spark-data/shared/pr_v2"
OLD_TRAINING_DATA_BASE = f"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/v14"
TRAINING_DATA_BASE = f"gs://gcp-us1-spark-data/user/colin/bugfix_localization_model/{VERSION}"
# TRAINING_DATA_BASE = f"/mnt/efs/spark-data/user/colin/bugfix_localization_model/{VERSION}"

config_output_path = f"{TRAINING_DATA_BASE.rstrip('/')}/config.json"
logs_output_base = f"/mnt/efs/augment/user/colin/bugfix_localization_model/{VERSION}"
configure_root_logger(logs_output_base)
logger = logging.getLogger(__name__)

pipeline_config = {
    "VERSION": VERSION,
    "PR_V2_BASE": PR_V2_BASE,
    "TRAINING_DATA_BASE": TRAINING_DATA_BASE,
    
    # === Datasets for AutoFix
    # Table containing metadata of intermediate commits for PRs
    "PR_INTER_COMMITS": f"{PR_V2_BASE}/inter_commits",
    # Table containing metadata of check runs for commits
    "PR_CHECK_RUNS": f"{PR_V2_BASE}/check_runs",
    # Table containing logs of failed check runs for commits
    "PR_FAILED_LOGS": f"{PR_V2_BASE}/failed_logs",
    # === End of AutoFix datasets

    "STAGE0_CANDIDATE_PROBLEMS_PATH": f"{OLD_TRAINING_DATA_BASE}/stage0_grouped_commit_full_metadata",
    "STAGE1_GENERATE_PROBLEMS_PATH": f"{OLD_TRAINING_DATA_BASE}/stage1_generate_problems",
    "STAGE2_HYDRATE_LOGS_PATH": f"{OLD_TRAINING_DATA_BASE}/stage2_with_hydrated_logs",
    "STAGE3_EXPLODE_PROBLEMS_PATH": f"{OLD_TRAINING_DATA_BASE}/stage3_with_exploded_problems",
    "STAGE4_HYDRATE_COMMITS_PATH": f"{OLD_TRAINING_DATA_BASE}/stage4_with_hydrated_commits_fixed3",
    "STAGE5_WITH_FILTERED_LOGS_PATH": f"{OLD_TRAINING_DATA_BASE}/stage5_with_filtered_logs_fixed3",
    "STAGE5_STEP2_ANNOTATE_RCA_PATH": f"{TRAINING_DATA_BASE}/stage5_step2_annotate_rca",
    "STAGE6_WITH_RETRIEVAL_PATH": f"{TRAINING_DATA_BASE}/stage6_with_retrieval_problems",
    "STAGE7_SPLIT_EVAL_PATH": f"{TRAINING_DATA_BASE}/stage7_split_eval",
    "STAGE7_SPLIT_TRAIN_PATH": f"{TRAINING_DATA_BASE}/stage7_split_train",
    "STAGE8_TRAIN_FORMAT_AS_TOKENS_PATH": f"{TRAINING_DATA_BASE}/stage8_train_format_as_tokens",
    "STAGE8_EVAL_FORMAT_AS_TOKENS_PATH": f"{TRAINING_DATA_BASE}/stage8_eval_format_as_tokens",
    "STAGE9_TRAIN_INDEXED_DATASET_PATH": f"{TRAINING_DATA_BASE}/stage9_train_indexed_dataset",
    "STAGE9_EVAL_INDEXED_DATASET_PATH": f"{TRAINING_DATA_BASE}/stage9_eval_indexed_dataset",

    "stage_1_config": {
        "use_V2": True,
        "use_multiple_check_runs": False, # TODO: set to True and update max_edits_similarity_perc to 0.001 or something
        "max_edits_similarity_perc": 1.0, # not used if use_multiple_check_runs is False
        "max_problems": 1_000,
        "repo_timeout_secs": 300,
    },

    "stage_4_config": {
        "use_V2": True,
        "assign_intermediate_failed_commits_to_breaking_diff": True,
        "skip_modified_filenames_in_target_diff": True, # NOTE: Be aware that this is a new field I set to true
        "skip_problems_with_missing_files": True,
        "skip_problems_with_undesirable_paths": True,
        "skip_problems_with_permissions_change_in_fixing_diff": True,
    },

    "stage_5_step2_config": {
        "use_rca_instructions": True,
        "rca_data": "gs://gcp-us1-user/lior/artifacts_v2/",
    },

    "stage6_config": {
        "retriever_config": {
            "scorer": {
                "name": "dense_scorer_v2_fbwd",
                "checkpoint_path": "/mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.2_prs_2k.keepmost.filter.empty10,R1.2_v13-128.30lines,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50",
                "tokenizer_name": "starcoder",
            },
            "chunker": {
                "name": "smart_line_level",
                "max_chunk_chars": 2000,
            },
            "query_formatter": {
                "name": "next_edit_location_query",
                "tokenizer": "starcoder",
                "max_prompt_tokens": 8192,
                "max_instruction_tokens": 4096,
                "use_smart_header": True,
                "deduplicate_identical_paths": True,
                "truncate_instructions_tail": False,
            },
            "document_formatter": {
                "name": "base:ethanol6-embedding-with-path-key",
                "tokenizer": "starcoder",
                "max_tokens": 999,
            },
        },
        "retrieval_strategy": "retrieved",
        "num_retrieved_chunks": 128,
        "ignore_whitespace_changes": True,
        "max_gold_chunks_per_problem": 1000, # TODO: update to 7 and see how much data is lost
        "ignore_problems_with_insufficient_chunks": False, #TODO: update to True and see how much data is lost
    },

    "stage7_config": {
        "eval_repo_names_path": "/mnt/efs/spark-data/user/colin/bugfix_localization_model/autofix_eval_repo_names_v4.txt",
        "max_eval_examples": 10_000,
    },

    # These keys are used by multiple stages.
    "generate_with_everything_version": True,
    "generate_no_instructions_version": False,
    "generate_no_diff_version": False,

    "stage8_config": {
        # This is a special config for `format_as_tokens` in 
        # research/next_edits/edit_localization_stages.py. It does NOT
        # match RetrieverConfigDict above.
        "formatter_configs": {
            "query_formatter": {
                "name": "next_edit_location_query",
                "tokenizer": "starcoder",
                "max_prompt_tokens": 8192,
                "max_diff_tokens": 4096,
                "max_instruction_tokens": 4096,
                "use_smart_header": True,
                "deduplicate_identical_paths": True,
                "truncate_instructions_tail": False,
            },
            "document_formatter": {
                "add_path": True
            },
        },
        "min_diff_context_lines": 3,
        "max_diff_context_lines": 5,
        "tokenizer_name": "starcoder",
    },
}

VALID_STAGES = set(["0", "1", "2", "3", "4", "5", "5_step2", "6", "7", "8", "9"])

def run_pipeline(active_stages: set[int], analytics_only=False, run_local=False):
    assert active_stages.issubset(VALID_STAGES)

    logger.info(f"Active stages: {active_stages}")
    logger.info(f"Pipeline config: {pipeline_config}")
    logger.info(f"Version: {VERSION}")
    logger.info(f"Logs output base: {logs_output_base}")

    if not analytics_only:
        if not file_exists(config_output_path):
            save_file(config_output_path, pipeline_config)
        else:
            saved_config = json.loads(read_file(config_output_path))
            assert saved_config == pipeline_config, f"Pipeline config has changed since last run. Please update the config file found at {config_output_path}. Diff: {diff_dicts(saved_config, pipeline_config)}"
            logger.info(f"Pipeline config already exists but is unchanged. Continuing with initialization of pipeline.")
        
    gpu_spark = None
    cpu_spark = None

    if "0" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        assert run_local == False, "Stage 0 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage0_grouped_commits(
            cpu_spark,
            pr_inter_commits_path=pipeline_config["PR_INTER_COMMITS"],
            pr_check_runs_path=pipeline_config["PR_CHECK_RUNS"],
            pr_failed_logs_path=pipeline_config["PR_FAILED_LOGS"],
            stage0_path=pipeline_config["STAGE0_CANDIDATE_PROBLEMS_PATH"],
            logs_output_base=logs_output_base,
        )
    if "1" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        assert run_local == False, "Stage 1 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark(250)
        if pipeline_config["stage_1_config"]["use_V2"]:
            logger.info("Using V2 generate dehydrated problems")
            stage1_generate_dehydrated_problems_V2(
                cpu_spark,
                stage0_path=pipeline_config["STAGE0_CANDIDATE_PROBLEMS_PATH"],
                stage1_path=pipeline_config["STAGE1_GENERATE_PROBLEMS_PATH"],
                use_multiple_check_runs=pipeline_config["stage_1_config"]["use_multiple_check_runs"],
                max_edits_similarity_perc=pipeline_config["stage_1_config"]["max_edits_similarity_perc"],
                max_problems=pipeline_config["stage_1_config"]["max_problems"],
                repo_timeout_secs=pipeline_config["stage_1_config"]["repo_timeout_secs"],
                logs_output_base=logs_output_base,
            )
        else:
            logger.info("Using V1 generate dehydrated problems")
            stage1_generate_dehydrated_problems(
                cpu_spark,
                stage0_path=pipeline_config["STAGE0_CANDIDATE_PROBLEMS_PATH"],
                stage1_path=pipeline_config["STAGE1_GENERATE_PROBLEMS_PATH"],
                use_multiple_check_runs=pipeline_config["stage_1_config"]["use_multiple_check_runs"],
                max_edits_similarity_perc=pipeline_config["stage_1_config"]["max_edits_similarity_perc"],
                max_problems=pipeline_config["stage_1_config"]["max_problems"],
                repo_timeout_secs=pipeline_config["stage_1_config"]["repo_timeout_secs"],
                logs_output_base=logs_output_base,
            )
    if "2" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        assert run_local == False, "Stage 2 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark(250)
        stage2_hydrate_logs(
            cpu_spark,
            stage1_path=pipeline_config["STAGE1_GENERATE_PROBLEMS_PATH"],
            pr_failed_logs_path=pipeline_config["PR_FAILED_LOGS"],
            stage2_path=pipeline_config["STAGE2_HYDRATE_LOGS_PATH"],
            logs_output_base=logs_output_base,
        )
    if "3" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        assert run_local == False, "Stage 3 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage3_explode_problems(
            cpu_spark,
            stage2_path=pipeline_config["STAGE2_HYDRATE_LOGS_PATH"],
            stage3_path=pipeline_config["STAGE3_EXPLODE_PROBLEMS_PATH"],
            logs_output_base=logs_output_base,
        )
    if "4" in active_stages:
        if cpu_spark is None:
            cpu_spark = create_cpu_spark(250)
        if pipeline_config["stage_4_config"]["use_V2"]:
            logger.info("Using V2 hydrate commits")
            stage4_hydrate_commits_V2(
                cpu_spark,
                stage3_path=pipeline_config["STAGE3_EXPLODE_PROBLEMS_PATH"],
                stage4_path=pipeline_config["STAGE4_HYDRATE_COMMITS_PATH"],
                skip_modified_filenames_in_target_diff=pipeline_config["stage_4_config"]["skip_modified_filenames_in_target_diff"],
                skip_problems_with_missing_files=pipeline_config["stage_4_config"]["skip_problems_with_missing_files"],
                skip_problems_with_undesirable_paths=pipeline_config["stage_4_config"]["skip_problems_with_undesirable_paths"],
                allow_resume=True,
                logs_output_base=logs_output_base,
                analytics_only=analytics_only,
                run_local=run_local,
            )
        else:
            assert run_local == False, "Stage 4 is not supported for local runs"
            logger.info("Using V1 hydrate commits")
            stage4_hydrate_commits(
                cpu_spark,
                stage3_path=pipeline_config["STAGE3_EXPLODE_PROBLEMS_PATH"],
                stage4_path=pipeline_config["STAGE4_HYDRATE_COMMITS_PATH"],
                assign_intermediate_failed_commits_to_breaking_diff=pipeline_config["stage_4_config"]["assign_intermediate_failed_commits_to_breaking_diff"],
                skip_modified_filenames_in_target_diff=pipeline_config["stage_4_config"]["skip_modified_filenames_in_target_diff"],
                allow_resume=True,
                logs_output_base=logs_output_base,
                analytics_only=analytics_only,
            )
    if "5" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        assert run_local == False, "Stage 5 is not supported for local runs"
        if gpu_spark is None:
            gpu_spark = create_gpu_spark(max_workers=100, use_h100=True)
        stage5_extract_logs(
            gpu_spark,
            data_path=pipeline_config["STAGE4_HYDRATE_COMMITS_PATH"],
            output_path=pipeline_config["STAGE5_WITH_FILTERED_LOGS_PATH"],
            logs_output_base=logs_output_base,
            allow_resume=True,
        )
    if "5_step2" in active_stages:
        assert pipeline_config["stage_5_step2_config"]["use_rca_instructions"] == True, "Stage 5_step2 is only supported when use_rca_instructions is True"
        assert run_local == False, "Stage 5_step2 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage5_step2_annotate_rca(
            cpu_spark,
            input_data=pipeline_config["STAGE5_WITH_FILTERED_LOGS_PATH"],
            rca_data=pipeline_config["stage_5_step2_config"]["rca_data"],
            output_path=pipeline_config["STAGE5_STEP2_ANNOTATE_RCA_PATH"],
            logs_output_base=logs_output_base,
            allow_resume=True,
            analytics_only=analytics_only,
        )
    if "6" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        if gpu_spark is None:
            gpu_spark = create_gpu_spark(use_h100=True)

        use_rca_instructions = pipeline_config["stage_5_step2_config"]["use_rca_instructions"]
        if pipeline_config["stage_5_step2_config"]["use_rca_instructions"]:
            input_path = pipeline_config["STAGE5_STEP2_ANNOTATE_RCA_PATH"]
        else:
            input_path = pipeline_config["STAGE5_WITH_FILTERED_LOGS_PATH"]

        stage6_create_retrieval_problems(
            spark=gpu_spark,
            input_path = input_path,
            output_path=pipeline_config["STAGE6_WITH_RETRIEVAL_PATH"],
            logs_output_base=logs_output_base,
            use_rca_instructions=use_rca_instructions,
            config=CreateRetrievalProblemsConfig(
                **pipeline_config["stage6_config"],
            ),
            run_local=run_local,
        )
    if "7" in active_stages:
        assert analytics_only == False, "Analytics only is not supported for stage 0"
        assert run_local == False, "Stage 7 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage7_holdout_eval(
            cpu_spark,
            stage6_with_retrieval_path=pipeline_config["STAGE6_WITH_RETRIEVAL_PATH"],
            stage7_eval_path=pipeline_config["STAGE7_SPLIT_EVAL_PATH"],
            stage7_train_path=pipeline_config["STAGE7_SPLIT_TRAIN_PATH"],
            logs_output_base=logs_output_base,
            eval_repo_names_path=pipeline_config["stage7_config"]["eval_repo_names_path"],
            max_eval_examples=pipeline_config["stage7_config"]["max_eval_examples"],
        )
    if "8" in active_stages:
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage8_format_data(
            cpu_spark,
            stage7_train_path=pipeline_config["STAGE7_SPLIT_TRAIN_PATH"],
            stage7_eval_path=pipeline_config["STAGE7_SPLIT_EVAL_PATH"],
            stage8_train_format_as_tokens_path=pipeline_config["STAGE8_TRAIN_FORMAT_AS_TOKENS_PATH"],
            stage8_eval_format_as_tokens_path=pipeline_config["STAGE8_EVAL_FORMAT_AS_TOKENS_PATH"],
            formatter_config=pipeline_config["stage8_config"]["formatter_configs"],
            min_diff_context_lines=pipeline_config["stage8_config"]["min_diff_context_lines"],
            max_diff_context_lines=pipeline_config["stage8_config"]["max_diff_context_lines"],
            tokenizer_name=pipeline_config["stage8_config"]["tokenizer_name"],
            generate_with_everything_version=pipeline_config["generate_with_everything_version"],
            generate_no_instructions_version=pipeline_config["generate_no_instructions_version"],
            generate_no_diff_version=pipeline_config["generate_no_diff_version"],
            analytics_only=analytics_only,
            run_local=run_local,
        )
    if "9" in active_stages:
        assert run_local == False, "Stage 9 is not supported for local runs"
        if cpu_spark is None:
            cpu_spark = create_cpu_spark()
        stage9_shuffle_and_export(
            cpu_spark,
            stage8_train_format_as_tokens_path=pipeline_config["STAGE8_TRAIN_FORMAT_AS_TOKENS_PATH"],
            stage8_eval_format_as_tokens_path=pipeline_config["STAGE8_EVAL_FORMAT_AS_TOKENS_PATH"],
            stage9_train_indexed_dataset_path=pipeline_config["STAGE9_TRAIN_INDEXED_DATASET_PATH"],
            stage9_eval_indexed_dataset_path=pipeline_config["STAGE9_EVAL_INDEXED_DATASET_PATH"],
            generate_with_everything_version=pipeline_config["generate_with_everything_version"],
            generate_no_instructions_version=pipeline_config["generate_no_instructions_version"],
            generate_no_diff_version=pipeline_config["generate_no_diff_version"],
            analytics_only=analytics_only,
        )
                                                                                                                                                                                                                                                                                                    

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run pipeline with specified active stages")
    parser.add_argument("--stages", nargs="+", type=str, help="List of active stages to run", required=True)
    parser.add_argument("--analytics-only", action="store_true", help="Only run analytics on existing data")
    parser.add_argument("--run-local", action="store_true", help="Run stages locally")
    args = parser.parse_args()

    active_stages = set(args.stages)
    run_pipeline(active_stages=active_stages, analytics_only=args.analytics_only, run_local=args.run_local)