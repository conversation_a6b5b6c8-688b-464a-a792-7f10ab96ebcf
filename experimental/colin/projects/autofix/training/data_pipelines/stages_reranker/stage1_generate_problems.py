
from functools import partial
import json
from typing import Iterable
from research.core.changes import Modified, Unchanged
from experimental.colin.projects.autofix.training.utils import AutofixProblem
from research.data.spark.pipelines.utils import map_parquet
from research.next_edits.common_stages import PRMeta
from research.next_edits.edit_gen_sampler import EditGenProblem, EditGenSampler
from research.next_edits.next_edits_dataset import RepoChangeWithMeta
from random import Random
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark, create_stage_logger, save_config_to_stage_output_dir
from compress_pickle import loads as compressed_loads
from compress_pickle import dumps as compressed_dumps
import pandas as pd
from research.data.spark.utils import AugmentK8sSparkSession
import pyspark.sql.functions as F

# max problems per 
def create_edit_gen_problems(
    repo_id: str, 
    autofix_problems: list[AutofixProblem],
    sampler: EditGenSampler,
    max_problems_per_commit: int = 20,
    max_problems_per_repo: int = 16_000
) -> Iterable[EditGenProblem]:
    rng = Random(42)
    total_problem_ct = 0
    for problem in autofix_problems:
        candidate_subproblems = list(sampler.sample_problems_with_explicit_wip(
            rng,
            problem.breaking_change,
            problem.fixing_change,
            problem.passing_child,
            PRMeta(repo_name=repo_id, pr_number=problem.pr_number, title="", body=""),
            custom_instruction=problem.logs[0],
        ))

        if len(candidate_subproblems) > max_problems_per_commit:
            rng.shuffle(candidate_subproblems)
            candidate_subproblems = candidate_subproblems[:max_problems_per_commit]
        
        for candidate_subproblem in candidate_subproblems:
            total_problem_ct += 1
            
            if total_problem_ct > max_problems_per_repo:
                return
                
            yield candidate_subproblem

def stage1_udf(
    repo_id: str, 
    problems: bytes,
    sampler: EditGenSampler,
    max_problems_per_commit: int = 20,
    max_problems_per_repo: int = 16_000
) -> Iterable[EditGenProblem]:
    autofix_problems = compressed_loads(problems, compression="gzip")
    edit_gen_problems = list(create_edit_gen_problems(
        repo_id,
        autofix_problems,
        sampler,
        max_problems_per_commit=max_problems_per_commit,
        max_problems_per_repo=max_problems_per_repo,
    ))
    return pd.Series({
        "repo_path": repo_id,
        "pickled_results": compressed_dumps(edit_gen_problems, compression="gzip"),
        "num_problems": len(edit_gen_problems),
    })
    
def stage1_generate_problems(
    spark: AugmentK8sSparkSession,
    input_path: str,
    stage1_path: str,
    logs_output_base: str,
    max_problems_per_commit: int = 20,
    max_problems_per_repo: int = 16_000,
    random_edit_region_rate: float = 0.45,
    random_target_file_rate: float = 0.125,
    analytics_only: bool = False,
):
    config = {
        "input_path": input_path,
        "stage1_path": stage1_path,
        "logs_output_base": logs_output_base,
        "max_problems_per_commit": max_problems_per_commit,
        "max_problems_per_repo": max_problems_per_repo,
        "random_edit_region_rate": random_edit_region_rate,
        "random_target_file_rate": random_target_file_rate,
    } 
    logger = create_stage_logger(__name__, stage1_path, logs_output_base)

    if not analytics_only:
        save_config_to_stage_output_dir(stage1_path, config)

        sampler = EditGenSampler(
            random_edit_region_rate=random_edit_region_rate,
            random_target_file_rate=random_target_file_rate,
        )

        logger.info(
            f"Starting stage 1 at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {input_path} and writing to {stage1_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"
        )

        map_parquet.apply(
            spark,
            partial(
                stage1_udf,
                sampler=sampler,
                max_problems_per_commit=max_problems_per_commit,
                max_problems_per_repo=max_problems_per_repo,
            ),
            input_path=input_path,
            output_path=stage1_path,
            input_columns=["repo_id", "problems"],
            ignore_error=False,
            allow_resume=True,
            timeout=10_000,
        )
        logger.info(f"Finished stage1_generate_problems at {pd.Timestamp.now(tz='US/Pacific')}. Generating summary stats...")
    else:
        logger.info(f"Skipping stage 1 processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    # get num_problems column
    input_df = spark.read.parquet(input_path)
    output_df = spark.read.parquet(stage1_path)
    num_input_rows = input_df.count()
    num_output_rows = output_df.count()
    num_problems = output_df.select(F.sum("num_problems")).collect()[0][0]
    logger.info(
        f"Summary stats for stage1_generate_problems:\n"
        f"num_problems: {num_problems}\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_output_rows: {num_output_rows}\n"
        f"% rows processed: {num_output_rows / num_input_rows * 100.0:.2f}%\n"
    )