import json
import logging

import pandas as pd
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import dense_retrieval_stage
from research.data.spark.utils import AugmentK8sSparkSession
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.next_edits.edit_gen_stages import RetrievalConfig
from pyspark.sql import functions as F


def stage2_run_auxiliary_retrieval(
    spark: AugmentK8sSparkSession,
    stage1_path: str,
    stage2_path: str,
    logs_output_base: str,
    retrieval_config: dict,
    analytics_only: bool = False,
):
    config = {
        "input_path": stage1_path,
        "output_path": stage2_path,
        **retrieval_config,
    }

    logger = create_stage_logger(__name__, stage2_path, logs_output_base)

    if not analytics_only:
        save_config_to_stage_output_dir(stage2_path, config)
        logger.info(
            f"Starting stage 2 at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {stage1_path} and writing to {stage2_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"
        )
        dense_retrieval_stage(
            input_path=stage1_path,
            output_path=stage2_path,
            config=RetrievalConfig(
                **retrieval_config,
            ),
            spark=spark,
        )
        logger.info(f"Finished stage 2 at {pd.Timestamp.now(tz='US/Pacific')}. Generating summary stats...")
    else:
        logger.info(f"Skipping stage 2 processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    input_df = spark.read.parquet(stage1_path)
    output_df = spark.read.parquet(stage2_path)
    num_input_rows = input_df.count()
    num_output_rows = output_df.count()
    num_problems = output_df.select(F.sum("num_problems")).collect()[0][0]
    logger.info(
        f"Summary stats for stage2_retrieval:\n"
        f"num_problems: {num_problems}\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_output_rows: {num_output_rows}\n"
        f"% rows processed: {num_output_rows / num_input_rows * 100.0:.2f}%\n"
    )