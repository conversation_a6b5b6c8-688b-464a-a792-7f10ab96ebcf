import json
import logging

import pandas as pd
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import dense_retrieval_stage, format_as_tokens_stage
from research.data.spark.utils import AugmentK8sSparkSession
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import dense_retrieval_stage
from research.next_edits.edit_gen_stages import EditGenFormatterConfig, PromptConfig, RetrievalConfig
from pyspark.sql import functions as F


def stage3_format_as_tokens(
    spark: AugmentK8sSparkSession,
    stage2_path: str,
    stage3_path: str,
    logs_output_base: str,
    prompt_config: dict,
    eval_repo_names_path: list[str],
    analytics_only: bool = False,
):
    stage3_valid_path = stage3_path.rstrip('/') + "_valid"
    stage3_train_path = stage3_path.rstrip('/') + "_train"

    config = {
        "stage2_path": stage2_path,
        "stage3_path": stage3_path,
        "logs_output_base": logs_output_base,
        "stage3_valid_path": stage3_valid_path,
        "stage3_train_path": stage3_train_path,
        "prompt_config": prompt_config,
        "eval_repo_names_path": eval_repo_names_path,
    }

    logger = create_stage_logger(__name__, stage3_path, logs_output_base)

    if not analytics_only:
        save_config_to_stage_output_dir(stage3_path, config)
        logger.info(
            f"Starting stage 3 at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {stage2_path} and writing to {stage3_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"
        )
        format_as_tokens_stage(
            input_path=stage2_path,
            output_path=stage3_path,
            config=PromptConfig(
                tokenizer_name=prompt_config['tokenizer_name'],
                drop_instruction_rate=prompt_config['drop_instruction_rate'],
                max_output_tokens=prompt_config['max_output_tokens'],
                formatter_config=EditGenFormatterConfig(
                    **prompt_config['formatter_config']
                ),
            ),
            spark=spark,
        )
        logger.info(f"Finished stage 3 formatting at {pd.Timestamp.now(tz='US/Pacific')}. Splitting to train/eval...")
        
        with open(eval_repo_names_path) as f:
            eval_repo_names = [x.strip() for x in f.readlines()]
        output_df = spark.read.parquet(stage3_path)
        logger.info(f"Splitting into train and valid. Found {len(eval_repo_names)} eval repo names.")
        valid_df = output_df.filter(F.col("repo_path").isin(eval_repo_names))
        train_df = output_df.filter(~F.col("repo_path").isin(eval_repo_names))
        valid_df.write.parquet(stage3_valid_path)
        train_df.write.parquet(stage3_train_path)
        logger.info(f"Finished stage 3 (including train/eval splitting) at {pd.Timestamp.now(tz='US/Pacific')}.")
    else:
        logger.info(f"Skipping stage 3 processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    input_df = spark.read.parquet(stage2_path)
    num_input_rows = input_df.count()
    output_train_df = spark.read.parquet(stage3_train_path)
    output_valid_df = spark.read.parquet(stage3_valid_path)
    num_output_train_rows = output_train_df.count()
    num_output_valid_rows = output_valid_df.count()
    num_unique_train_repos = output_train_df.select(F.countDistinct("repo_path")).collect()[0][0]
    num_unique_valid_repos = output_valid_df.select(F.countDistinct("repo_path")).collect()[0][0]
    num_train_problems = output_train_df.select(F.sum("num_problems")).collect()[0][0]
    num_valid_problems = output_valid_df.select(F.sum("num_problems")).collect()[0][0]
    logger.info(
        f"Summary stats for stage3_retrieval:\n"
        f"num_input_rows: {num_input_rows}\n"
        f"num_output_train_rows: {num_output_train_rows}\n"
        f"num_output_valid_rows: {num_output_valid_rows}\n"
        f"num_unique_train_repos: {num_unique_train_repos}\n"
        f"num_unique_valid_repos: {num_unique_valid_repos}\n"
        f"num_train_problems: {num_train_problems}\n"
        f"num_valid_problems: {num_valid_problems}\n"
    )