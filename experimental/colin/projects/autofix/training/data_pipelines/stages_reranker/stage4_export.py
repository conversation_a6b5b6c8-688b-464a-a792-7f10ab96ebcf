import json
import numpy as np
import pandas as pd
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.tokenizer import NextEditGenSpecialTokens
from experimental.colin.projects.autofix.training.data_pipelines.utils import create_stage_logger, save_config_to_stage_output_dir
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    _rebalance_positive_negative_examples,
    load_stage3_result_from_files,
)
from random import Random
from megatron.data import indexed_dataset
from pathlib import Path
from pyspark.sql import functions as F
from research.utils.token_array_utils import (
    pad_sequences,
)
import gc


def save_results_as_datasets_explicit_eval_repo_names(
    input_path: str,
    save_path: Path,
    pad_to_length: int,
    special: NextEditGenSpecialTokens,
    positive_ratio: float,
    logger,
    repos_per_batch: int = 1000,
):
    """Save the stage3 results as indexed datasets.

    This is a variant of save_stage3_results_as_datasets in research/data/spark/pipelines/stages/next_edit_gen_pipelines.py.
    """
    save_path.mkdir(parents=True, exist_ok=True)

    train_path = (str(input_path).rstrip('/') + "_train").replace("gs://gcp-us1-spark-data", "/mnt/efs/spark-data")
    train_shards = list(Path(train_path).glob("*.parquet"))
    valid_path = (str(input_path).rstrip('/') + "_valid").replace("gs://gcp-us1-spark-data", "/mnt/efs/spark-data")
    valid_shards = list(Path(valid_path).glob("*.parquet"))

    logger.info(f"Loading train and valid data from {train_path} and {valid_path}")
    logger.info(f"Found {len(train_shards)} train shards and {len(valid_shards)} valid shards")

    for shards, output_name in [
        (train_shards, "train"),
        (valid_shards, "valid"),
    ]:
        dataset_builder = indexed_dataset.MMapIndexedDatasetBuilder(
            str(save_path / f"{output_name}.bin"),
            dtype=np.int32,
        )
        for _, results_part in enumerate(
            load_stage3_result_from_files(
                shards, repos_per_batch=repos_per_batch
            )
        ):
            problems = [
                prob for part in results_part for prob in part.tokenized_problems
            ]
            problems = _rebalance_positive_negative_examples(
                Random(42),
                problems,
                has_change_id=special.has_change,
                no_change_id=special.no_change,
                positive_ratio=positive_ratio,
            )
            for tokens in pad_sequences(
                (prob.tokens for prob in problems),
                seq_len=pad_to_length,
                pad_id=-special.padding,
            ):
                dataset_builder.add_item(tokens)
            del problems, results_part
            gc.collect()
        dataset_builder.finalize(str(save_path / f"{output_name}.idx"))


def stage4_export(
    stage3_path: str,
    stage4_path: str,
    logs_output_base: str,
    tokenizer_name: str,
    positive_ratio: float = 0.70,
    pad_to_length: int = 16384,
    analytics_only: bool = False,
):
    assert pad_to_length % 128 == 0, "pad_to_length should be a multiple of 128"
    pad_to_length += 1 # this is to account for how our training infra works. we slice
    # one token from beginning of golds and one token from end of golds, so we need an 
    # extra token to account for this.

    config = {
        "stage3_path": stage3_path,
        "stage4_path": stage4_path,
        "logs_output_base": logs_output_base,
        "positive_ratio": positive_ratio,
        "pad_to_length": pad_to_length,
    }
    logger = create_stage_logger(__name__, stage4_path, logs_output_base)
    if not analytics_only:
        tokenizer = create_tokenizer_by_name(tokenizer_name)
        special = tokenizer.special_tokens
        save_config_to_stage_output_dir(stage4_path, config)
        logger.info(
            f"Starting stage 4 at {pd.Timestamp.now(tz='US/Pacific')}."
            f"Reading from {stage3_path} and writing to {stage4_path}."
            f"\nConfig: {json.dumps(config, indent=4)}"
        )
        save_results_as_datasets_explicit_eval_repo_names(
            input_path=stage3_path,
            save_path=Path(stage4_path),
            pad_to_length=pad_to_length,
            special=special,
            logger=logger,
            positive_ratio=positive_ratio,
        )
        logger.info(f"Finished stage 4 at {pd.Timestamp.now(tz='US/Pacific')}.")
    else:
        logger.info(f"Skipping stage 4 processing and just generating summary stats at {pd.Timestamp.now(tz='US/Pacific')}.")

    
    train_output_path = str(stage4_path) + "/train"
    valid_output_path = str(stage4_path) + "/valid"
    logger.info(f"Loading train and valid data from {train_output_path} and {valid_output_path}")
    train_output_data = indexed_dataset.make_dataset(
        train_output_path,
        "mmap",
        skip_warmup=True,
    )
    valid_output_data = indexed_dataset.make_dataset(
        valid_output_path,
        "mmap",
        skip_warmup=True,
    )
    num_train_examples = len(train_output_data)
    num_valid_examples = len(valid_output_data)
    training_example_length = len(train_output_data[0])
    valid_example_length = len(valid_output_data[0])
    median_train_token_ct = np.median([len(x) for x in train_output_data])
    median_valid_token_ct = np.median([len(x) for x in valid_output_data])
    logger.info(
        f"Stage 4 summary stats:\n"
        f"num_train_examples: {num_train_examples}\n"
        f"num_valid_examples: {num_valid_examples}\n"
        f"median_train_token_ct: {median_train_token_ct}\n"
        f"median_valid_token_ct: {median_valid_token_ct}\n"
        f"example training example length: {training_example_length}\n"
        f"example valid example length: {valid_example_length}\n"
    )
