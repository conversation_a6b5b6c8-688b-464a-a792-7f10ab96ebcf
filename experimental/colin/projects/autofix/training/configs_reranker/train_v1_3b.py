# flake8: noqa
from __future__ import annotations

from pathlib import Path

from research.environments.providers import ClusterName
from experimental.jiayi.finetuning.training_config_utils import (
    ModelName,
    ModelSize,
    get_fastbackward_config,
)


def edit_gen_fb_config(
    run_summary: str,
    model_name: ModelName,
    user: str,
    model_size: ModelSize,
    data_splits_path: Path,
    sequence_length: int,
    train_iters: int,
    eval_items: int = 256 * 100,
    n_nodes: int | None = None,
    is_quick_test: bool = False,
    restore_from_checkpoint: str | None = None,
    checkpoint_path: Path | None = None,
) -> dict:
    """Get the training config dict to be submitted to FastBackward.

    Args:
        run_summary: A short string summary of the training run.
        model_name: The model name to use.
        user: The training job will be submitted under Dev/{user}.
        model_size: The model size to use.
        data_splits_path: The directory containing the indexed dataset files.
        sequence_length: The sequence length to use.
        train_iters: The number of training iterations.
        n_nodes: The number of GPU nodes to use. Each node has 8 GPUs.
        is_quick_test: Whether to use a quick test config.
        restore_from_checkpoint: The checkpoint ID to restore training from. When this\
            is set, `checkpoint_path` will be ignored.
        checkpoint_path: The path to the checkpoint directory. If None, will use the\
            default checkpoint path for the model.
    """

    wandb_project = "autofix-reranker-gen"
    training_name = f"{run_summary}-{model_name}_{model_size}"
    if is_quick_test:
        training_name += "-quicktest"

    common_overrides: dict = {
        "eval_items": eval_items,
        "eval_interval": 100,
        "log_interval": 100,
    }
    if restore_from_checkpoint is not None:
        common_overrides["checkpoint"] = restore_from_checkpoint
        common_overrides["restore_training_metadata_from_checkpoint"] = True
        # assuming we don't want this if we are not restoring from the Determined UI
        common_overrides["restore_optimizer_state_from_checkpoint"] = False
    elif checkpoint_path is not None:
        common_overrides["checkpoint"] = str(checkpoint_path)

    if is_quick_test:
        common_overrides["eval_interval"] = 40
        common_overrides["eval_items"] = 128
        train_iters = 50
        wandb_project = "quicktest"
    elif model_size in ("15b", "16b", "3b", "7b"):
        n_nodes = n_nodes or 8
        assert n_nodes in (1, 2, 4, 8, 16, 32)
        grad_accumulation_steps = 1

        """
        n_nodes = 16
        n_gpus = 8
        batch_size_per_gpu = 2
        grad_accumulation_steps = 32 // 16 = 2
        batch_size = 16 * 8 * 2 * 2 = 512
        train_iters = 303066 // 512 = 590

        lowering grad accumulation steps to 1:
        - lowers batch size to 256
        - increases train_iters to 303066 // 256 = 1190
        """

        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=n_nodes,
            batch_size_per_gpu=2,
            grad_accumulation_steps=grad_accumulation_steps,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    else:
        raise NotImplementedError(f"model_size {model_size} not supported")


def start_finetuning():
    version = "v2"
    dataset_path = Path(f"/mnt/efs/augment/data/processed/autofix/{version}/stage4_indexed_dataset/")
    model_name = "starcoder2"
    run_summary = f"autofix_reranker_{version}"
    model_size = "3b"
    sequence_length = 16384 # this should be pad_to_length - 1
    assert sequence_length % 128 == 0, "sequence_length should be a multiple of 128"
    train_iters = 1190
    training_with_fb = True
    num_eval_items = 2000
    is_quick_test = False
    cluster: ClusterName = "GCP-US1"
    checkpoint_path = "/mnt/efs/augment/checkpoints/next-edit-gen/S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_3b-fb"

    if training_with_fb:
        from research.fastbackward.determined.launch import launch_fb_job  # type: ignore

        train_config = edit_gen_fb_config(
            model_name=model_name,
            run_summary=run_summary,
            user="autofix",
            model_size=model_size,
            data_splits_path=dataset_path,
            sequence_length=sequence_length,
            train_iters=train_iters,
            eval_items=num_eval_items,
            is_quick_test=is_quick_test,
            restore_from_checkpoint=None,
            checkpoint_path=checkpoint_path,
            n_nodes=16,
        )
        launch_fb_job(
            train_config,
            cluster=cluster,
        )


if __name__ == "__main__":
    start_finetuning()
