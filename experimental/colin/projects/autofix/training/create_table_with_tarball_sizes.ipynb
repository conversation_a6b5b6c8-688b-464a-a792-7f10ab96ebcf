{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark.utils import k8s_session\n", "\n", "\n", "def create_spark(max_workers: int = 1000):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"32\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"3\",\n", "            #\"spark.executor.memory\": \"128g\",\n", "            #\"spark.task.cpus\": 10,\n", "            #\"spark.executor.cores\": 10,\n", "        },\n", "    )\n", "\n", "    return spark\n", "\n", "\n", "spark = create_spark()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "from research.data.utils.pr_v2 import get_tar_full_path\n", "from pyspark.sql.types import IntegerType\n", "import pyspark.sql.functions as F\n", "\n", "\n", "input_path = '/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata'\n", "df = spark.read.parquet(input_path)\n", "df.columns\n", "\n", "def get_size(repo_ID):\n", "    repo_tarball_path = get_tar_full_path(repo_ID)\n", "    size_in_mb = math.floor(repo_tarball_path.stat().st_size / 1024 / 1024)\n", "    return size_in_mb\n", "\n", "df = df.withColumn('repo_size', F.udf(get_size, IntegerType())(<PERSON><PERSON>col('repo_ID')))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.limit(5).<PERSON><PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print distribution of repo sizes\n", "df.select('repo_size').describe().show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["offending_inputs = [\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00186-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00374-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00376-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00389-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00400-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00412-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00442-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00479-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00483-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00491-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00580-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00664-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00762-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00871-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\"\n", "]\n", "\n", "offending_df = spark.read.parquet(*offending_inputs)\n", "\n", "offending_df = offending_df.withColumn('repo_size', F.udf(get_size, IntegerType())(<PERSON><PERSON>col('repo_ID')))\n", "\n", "\n", "offending_df.limit(5).to<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print distribution of repo sizes\n", "offending_df.select('repo_size').describe().show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get size of googleapis/google-cloud-php\n", "print(get_size('googleapis/google-cloud-php'))\n", "\n", "print(get_size('opencollective/opencollective-frontend'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_with_populated_commits_download_repos_only')\n", "df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import loads as compressed_loads\n", "import numpy as np\n", "import pyspark.sql.functions as F\n", "from pyspark.sql.types import IntegerType, FloatType, DoubleType\n", "\n", "def get_median_time(changes):\n", "    changes = compressed_loads(changes, compression=\"gzip\")\n", "    times = [delta for delta in changes]\n", "    if len(times) == 0:\n", "        return None\n", "    return float(np.median(times))\n", "\n", "df = df.withColumn('median_breaking_changes_time', F.udf(get_median_time,DoubleType())(<PERSON><PERSON>col('breaking_change_times')))\n", "df = df.withColumn('median_fixing_changes_time', F.udf(get_median_time, DoubleType())(<PERSON><PERSON>col('fixing_change_times')))\n", "\n", "# show distibution of median time for breaking changes\n", "df.select('median_breaking_changes_time').describe().show()\n", "\n", "# show distibution of median time for fixing changes\n", "df.select('median_fixing_changes_time').describe().show()\n", "\n", "df_pd = df.to<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pd.iloc[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["compressed_loads(df_pd.iloc[4]['fixing_change_times'], compression=\"gzip\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from concurrent.futures import ThreadPoolExecutor\n", "from multiprocessing.pool import ThreadPool\n", "from typing import Callable, TypeVar\n", "\n", "T = TypeVar('T')\n", "def run_with_timeout(func: Callable[..., T], args=(), kwargs={}, timeout_duration=3) -> T | None:\n", "    with ThreadPool(processes=1) as pool:\n", "        def _func(args_and_kwargs: tuple[tuple, dict]):\n", "            args, kwargs = args_and_kwargs\n", "            return func(*args, **kwargs)\n", "        \n", "        result = pool.map_async(_func, [(args, kwargs)])\n", "        try:\n", "            return result.get(timeout=timeout_duration)\n", "        except TimeoutError:\n", "            return None\n", "        \n", "def sleeper(text):\n", "    import time\n", "    while True:\n", "        i = 1*2\n", "\n", "print(run_with_timeout(sleeper, kwargs=dict(text='hi'),timeout_duration=3))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from compress_pickle import dumps as compressed_dumps\n", "from pyspark.sql.types import IntegerType, FloatType, DoubleType, StringType, ArrayType, BinaryType\n", "from pyspark.sql.functions import explode\n", "\n", "offending_inputs = [\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00186-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00374-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00376-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00389-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00400-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00412-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00442-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00479-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00483-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00491-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00580-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00664-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00762-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\",\n", "    \"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata/part-00871-6bed8569-c449-4ebc-bb8c-0b33814e553c-c000.zstd.parquet\"\n", "]\n", "df = spark.read.parquet(*offending_inputs)\n", "print(df.columns)\n", "num_rows = df.count()\n", "\n", "def breakup_commits(commits):\n", "    # breakup into sublists of 5 elements\n", "    commits_sublists = [compressed_dumps(commits[i:i+5], compression=\"gzip\") for i in range(0, len(commits), 5)]\n", "    return commits_sublists\n", "\n", "df = df.withColumn('commits', F.udf(breakup_commits, ArrayType(BinaryType()))(F<PERSON>col('commits')))\n", "df = df.select('repo_ID', explode('commits').alias('commits'))\n", "num_rows2 = df.count()\n", "\n", "print(num_rows, num_rows2)\n", "\n", "df = df.repartition(100)\n", "df.write.parquet('/mnt/efs/spark-data/user/colin/bugfix_localization_model/v3/repo_grouped_failed_commit_with_passing_child_metadata_CHUNKED_SMALL')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(*offending_inputs)\n", "df_pd = df.limit(5).to<PERSON><PERSON><PERSON>()\n", "\n", "df_pd"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}