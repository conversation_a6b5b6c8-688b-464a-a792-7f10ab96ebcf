from dataclasses import dataclass

from research.utils.repo_change_utils import CommitMeta, RepoChange


@dataclass
class AutofixProblem:
    passing_child: CommitMeta
    """The child commit that passed tests"""

    failed_commits: list[CommitMeta]
    """Ordered new to old list of failed commit"""

    logs: list[str]
    """List of all logs"""

    breaking_change: RepoChange | None = None
    """The repo change that is breaking"""

    fixing_change: RepoChange | None = None
    """The repo change that is fixing"""

    logs_check_run_name: str | None = None
    """The name of the check run that failed that we grabbed logs from"""

    logs_sha: str | None = None
    """The sha of the commit that failed that we grabbed logs from"""

    pr_number: int | None = None
    """The PR number that this problem is associated with"""

    initial_commit: CommitMeta | None = None
    """The initial commit of the PR"""

    most_recent_successful_commit: CommitMeta | None = None
    """The most recent commit in the PR that succeeded the target check run and is an ancestor of the failed commit."""

    debugging_info: str | None = None
    """A string of debugging info"""

    rca: str | None = None
    """A root cause analysis of the bug"""
