{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import logging\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import create_cpu_spark\n", "from experimental.colin.projects.autofix.training.data_pipelines.stages.stage1_generate_dehydrated_problems import stage1_generate_dehydrated_problems\n", "logger = logging.getLogger(__name__)\n", "\n", "pipeline_config = {\n", "    \"STAGE0_PATH\": f\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/v13/stage0_grouped_commit_full_metadata\",\n", "    \"STAGE1_PATH\": f\"/mnt/efs/spark-data/user/colin/bugfix_localization_model/XIAOLEI_REPRO/stage1_generate_problems\",\n", "\n", "    \"stage_1_config\": {\n", "        \"use_multiple_check_runs\": <PERSON>als<PERSON>,\n", "        \"max_edits_similarity_perc\": 1.0, # not used if use_multiple_check_runs is False\n", "        \"max_problems\": 1_000,\n", "    },\n", "}\n", "spark = create_cpu_spark()\n", "\n", "df = spark.read.parquet(pipeline_config[\"STAGE0_PATH\"])\n", "df.count()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# shuffle\n", "from pyspark.sql.functions import rand\n", "df = df.orderBy(rand()).limit(1000)\n", "df_pd = df.to<PERSON><PERSON><PERSON>()\n", "df_pd"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from experimental.colin.projects.autofix.training.data_pipelines.stages.stage1_generate_dehydrated_problems import process_repo_with_timeout\n", "\n", "\n", "output = []\n", "for _, row in df_pd.iterrows():\n", "    output.append(process_repo_with_timeout(\n", "        row[\"repo_ID\"],\n", "        row[\"commit_metadata_ls\"],\n", "        use_multiple_check_runs=pipeline_config[\"stage_1_config\"][\"use_multiple_check_runs\"],\n", "        max_edits_similarity_perc=pipeline_config[\"stage_1_config\"][\"max_edits_similarity_perc\"],\n", "        max_problems=pipeline_config[\"stage_1_config\"][\"max_problems\"],\n", "    ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list(pd.DataFrame(output)['num_problems'])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}