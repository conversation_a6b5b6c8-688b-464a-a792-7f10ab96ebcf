{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting KUBECONFIG to /home/<USER>/.kube/config\n", "Skipping bazel build.\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "                                                                                \r"]}], "source": ["from research.data.spark.utils import k8s_session\n", "import pyspark.sql.functions as F\n", "\n", "PR_V2_BASE = \"/mnt/efs/spark-data/shared/pr_v2\"\n", "\n", "# === Datasets for AutoFix\n", "# Table containing metadata of intermediate commits for PRs\n", "PR_INTER_COMMITS = f\"{PR_V2_BASE}/inter_commits\"\n", "# Table containing metadata of check runs for commits\n", "PR_CHECK_RUNS = f\"{PR_V2_BASE}/check_runs\"\n", "# Table containing logs of failed check runs for commits\n", "PR_FAILED_LOGS = f\"{PR_V2_BASE}/failed_logs\"\n", "# === End of AutoFix datasets\n", "\n", "\n", "def create_spark(max_workers: int = 100):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "spark = create_spark()\n", "\n", "commit_metadata_df = spark.read.parquet(PR_INTER_COMMITS)\n", "cicd_metadata_df = spark.read.parquet(PR_CHECK_RUNS)\n", "failed_logs_df = spark.read.parquet(PR_FAILED_LOGS)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 3:>                                                          (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------------+--------------------+---------+---------------------+--------------------+--------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|               owner|                name|pr_number|                title|            head_ref|      base_ref|           merged_at|                 sha|             message|        committed_at|             parents|\n", "+--------------------+--------------------+---------+---------------------+--------------------+--------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "|         blinker-iot|     blinker-library|      104| update codes, fix...|             dev_2.0|        master|2018-08-16T09:44:24Z|e903ef4649469d039...|update codes, hea...|2018-08-16T08:38:53Z|[3f19340754e8c9c5...|\n", "|             bitcoin|             bitcoin|    25500| refactor: Move in...|2022-06-evictionm...|        master|2022-07-07T16:54:59Z|0101d2bc3c3bcf698...|[net] Move evicti...|2022-07-06T16:13:54Z|[c741d748d4d98369...|\n", "|             rexRUBY|            NewsFeed|       12|코드 개선 및 오류수정|             profile|           dev|                null|afef165b5d1229d30...|                수정|2024-09-05T23:39:55Z|[82afd0ebb46dc78a...|\n", "|               nixos|      nixos-hardware|      864| Update repository...|  pr/imx-repo-update|        master|2024-02-13T16:36:44Z|3f0edf750227f89d7...|Update repository...|2024-02-13T13:06:34Z|[6e5cc385fc8cf5ca...|\n", "|           symbiflow|             prjxray|     1562|   Populate all parts| WIP/dnltz/new_parts|        master|2021-03-18T20:07:02Z|a6c54b0b3e480b8b0...|Remove autogenera...|2021-03-15T16:37:58Z|[7b16929e7d15ee35...|\n", "|department-of-vet...|       content-build|     1855| 15318 Mobile App ...| 15318-smart-banners|          main|2024-01-11T21:48:07Z|402e8a4ecddabdc75...|         Fixing test|2024-01-11T19:53:54Z|[31a6c3ba61da0b81...|\n", "|              kornia|           kornia-rs|      116| Ndarray deprecation |ndarray-deprecati...|          main|2024-09-01T13:10:08Z|c1b46d3075aec6828...|          last fixes|2024-08-31T23:48:31Z|[1057b5a1a67fa69d...|\n", "|       ed<PERSON><PERSON><PERSON><PERSON>|               retos|       12| Update files to d...|        feature/ehbc|          main|2024-08-12T21:30:11Z|c72469db46bf5c3ac...|Update files to d...|2024-08-12T21:29:21Z|[395c8fa5d31d302b...|\n", "|               azure|     azure-sdk-tools|     4234| Initial push of C...| CLICommandLineTests|          main|2022-09-27T20:03:00Z|7fa9c234d8d3a35d2...|Initial push of C...|2022-09-27T18:01:24Z|[a6a76939fa13024a...|\n", "|              pykeen|              pykeen|     1451|      ↔️🐇 <PERSON><PERSON>|          merge-hole|        master|                null|5f393cc0580adbbf5...|update docstr & f...|2024-09-26T16:04:13Z|[ca5bce751180a6cf...|\n", "|         mi<PERSON><PERSON><PERSON><PERSON>|              gentoo|     3781| Fork Sync: Update...|              master|        master|2024-07-10T15:07:23Z|2f8d766346ddd69c9...|dev-libs/c-stdaux...|2024-07-10T12:30:41Z|[7bd2fca8812b618a...|\n", "|  zephyrproject-rtos|              zephyr|    11603| Bluetooth: Define...|upstream-bluetoot...|        master|2018-11-28T19:52:37Z|f3af32134db4e2c01...|Bluetooth: Define...|2018-11-22T15:53:28Z|[6798a421e1c8f667...|\n", "|             webonyx|         graphql-php|     1608| Serializes `\\Unit...|serialize-native-...|        master|2024-09-10T10:35:56Z|649d647a586b4a8bb...|   fix include paths|2024-09-10T10:27:38Z|[5c1b1f6b8d60e3bf...|\n", "|              spiffe|               spire|     3682| Add CHANGELOG.md ...|           changelog|release/v1.5.3|2022-12-14T19:30:11Z|9e379124c6cdaccc5...|PR Changes\\n\\nSig...|2022-12-14T18:47:37Z|[df761d911fdca156...|\n", "|    ShadowDuckStudio|     ProjectHenchman|      208| Tutorial pop-up a...|                  UI|          main|2024-07-25T20:47:18Z|e87b683617281020c...|Tutorial option a...|2024-07-25T20:07:50Z|[63a75ca1685f8fb9...|\n", "|      gitarcode-test|rewrite-migrate-java|     1666| gitar-cleanup-019...|gitar-cleanup-019...|          main|                null|2305c49bccc91870c...|Ran Fuzzer. Time ...|2024-08-26T02:33:14Z|[a515067c353af144...|\n", "|           farizwrmn|finpro-jcwdol-013-01|      123|             Fpj00 15|            FPJ00-15|       develop|2024-07-14T15:03:09Z|66c000322cc7d70c6...|feat: add user, s...|2024-06-20T15:25:54Z|[798e6e090fb12ddd...|\n", "|      risingwavelabs|          risingwave|    10413| test: Add nexmark...|kwannoel/enable-k...|          main|2023-09-06T15:42:36Z|641f347ae6a3fdea7...|                 fix|2023-09-06T12:42:41Z|[aa65f4ea1a164331...|\n", "|            enmarche|          coalitions|      354| Desktop/causes ca...|desktop/causes-ca...|        master|2021-04-29T12:28:57Z|da77842be8c27c291...|       init carousel|2021-04-28T14:49:43Z|[142268d8e109a3e1...|\n", "|          Team-Jambo|       portfolio-api|        7|            3 swagger|           3-swagger|          main|2024-07-16T11:41:03Z|b33256957131bcc5d...| Swagger links added|2024-07-16T10:15:16Z|[de16a2caf9cf860a...|\n", "+--------------------+--------------------+---------+---------------------+--------------------+--------------+--------------------+--------------------+--------------------+--------------------+--------------------+\n", "only showing top 20 rows\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["commit_metadata_df.show()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 4:>                                                          (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["+-----------------+--------------------+---------+--------------------+--------------------+---------+----------+-----------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+-------------------+-------------------+------------------+--------------------+\n", "|            owner|                name|pr_number|          commit_sha|      check_run_name|   status|conclusion|database_id|          started_at|        completed_at|         details_url|check_suite_status|check_suite_db_id|       workflow_name| workflow_run_event|workflow_run_number|workflow_run_db_id|    workflow_run_url|\n", "+-----------------+--------------------+---------+--------------------+--------------------+---------+----------+-----------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+-------------------+-------------------+------------------+--------------------+\n", "|         gerardog|         winget-pkgs|      756|7c0a0c9db634e6054...|         Report (PR)|COMPLETED|   SKIPPED|29199875829|2024-08-24T10:50:41Z|2024-08-24T10:50:41Z|https://github.co...|         COMPLETED|      27553454711|      Spell Checking|               push|              11225|       10537928947|https://github.co...|\n", "|          rst0git|               cri-o|      267|3fce02f69d7990157...|          shellcheck|COMPLETED|   SUCCESS|29230326683|2024-08-26T01:41:33Z|2024-08-26T01:41:47Z|https://github.co...|         COMPLETED|      27582456195|              verify|       pull_request|                270|       10552050842|https://github.co...|\n", "|        bukishola|          cosmos-sdk|      254|2b4a624ddd05fb830...|             labeler|COMPLETED|   SUCCESS|30290920968|2024-09-18T01:21:25Z|2024-09-18T01:21:28Z|https://github.co...|         COMPLETED|      28518207931|Pull Request Labeler|pull_request_target|                254|       10913818537|https://github.co...|\n", "|     fzj-inm1-bda|       siibra-python|      427|0a290ecd2c677eeb4...|    unit-tests (3.8)|COMPLETED|   SUCCESS|15557500032|2023-08-02T14:37:56Z|2023-08-02T14:39:24Z|https://github.co...|         COMPLETED|      14763482167|    [test] unit test|               push|               1650|        5740210834|https://github.co...|\n", "|       timefoldai|timefold-quickstarts|      534|d37fe315171ba41dc...|build (windows-la...|COMPLETED|   SUCCESS|28098523264|2024-07-30T10:50:52Z|2024-07-30T10:59:50Z|https://github.co...|         COMPLETED|      26586247248|               Maven|       pull_request|                125|       10160993688|https://github.co...|\n", "|      honeycombio|terraform-provide...|      398|4a7973bc0da186b41...|          Formatting|COMPLETED|   SUCCESS|18788104180|2023-11-17T14:42:16Z|2023-11-17T14:42:27Z|https://github.co...|         COMPLETED|      18285313922|                  CI|               push|               1762|        6905409865|https://github.co...|\n", "|          podlove|          podlove-ui|     1138|adac8716b055622ea...|packages_player-c...|COMPLETED|   SUCCESS|19028289734|2023-11-26T08:37:33Z|2023-11-26T08:38:19Z|https://github.co...|         COMPLETED|      18499772317|              Review|       pull_request|                152|        6994475916|https://github.co...|\n", "|          nf-core|differentialabund...|      208|804d6e55f8ebc74e7...|             nf-core|COMPLETED|   SUCCESS|19063686860|2023-11-27T16:45:19Z|2023-11-27T16:46:35Z|https://github.co...|         COMPLETED|      18531805183|     nf-core linting|       pull_request|                753|        7008112343|https://github.co...|\n", "|      saveourtool|              diktat|     1754|43fadfc95f8757468...|Run diktat via CL...|COMPLETED|   SUCCESS|17556270106|2023-10-10T09:01:32Z|2023-10-10T09:02:38Z|https://github.co...|         COMPLETED|      17062999437|      Build and test|       pull_request|               5696|        6466953107|https://github.co...|\n", "|     datafuselabs|            databend|    16528|f32145de64741ad6c...|linux / sqllogic ...|COMPLETED|   SUCCESS|30698862920|2024-09-26T10:46:55Z|2024-09-26T10:47:06Z|https://github.co...|         COMPLETED|      28876150451|                 Dev|       pull_request|              18136|       11050429503|https://github.co...|\n", "|         gammasim|            simtools|      947|688d291e1968d2bed...|integrationtests ...|COMPLETED|   SUCCESS|25659736121|2024-05-31T16:20:17Z|2024-05-31T16:39:25Z|https://github.co...|         COMPLETED|      24397459827| CI-integrationtests|       pull_request|               2347|        9321221986|https://github.co...|\n", "|       tailcallhq|            tailcall|     2436|1ae323f4ef7c8463f...|Run Formatter and...|COMPLETED|   FAILURE|27452476967|2024-07-15T11:59:25Z|2024-07-15T12:00:43Z|https://github.co...|         COMPLETED|      26010566137|          autofix.ci|       pull_request|               8381|        9938938873|https://github.co...|\n", "|    opensupplyhub|     open-supply-hub|      353|79472d9f34343ead3...|check-django-code...|COMPLETED|   SUCCESS|30837525652|2024-09-30T06:50:21Z|2024-09-30T06:50:25Z|https://github.co...|         COMPLETED|      29000352423|        Code Quality|       pull_request|               2731|       11100675846|https://github.co...|\n", "|          mongodb|    mongo-php-driver|     1581|3873c1917bee0b026...|Windows Tests (8....|COMPLETED|   SUCCESS|26082237116|2024-06-11T14:39:06Z|2024-06-11T14:45:15Z|https://github.co...|         COMPLETED|      24776280724|       Windows Tests|       pull_request|                357|        9467462492|https://github.co...|\n", "|           pulumi|     pulumi-dnsimple|      315|521bf95f79ee1ff64...|      build_sdk (go)|COMPLETED|   SUCCESS|21432087335|2024-02-10T05:48:32Z|2024-02-10T05:49:38Z|https://github.co...|         COMPLETED|      20631791574|run-acceptance-tests|       pull_request|                238|        7852984738|https://github.co...|\n", "|  informalsystems|              hermes|     3906|67da88f9188307836...|integration-test ...|COMPLETED|   SUCCESS|22845673912|2024-03-19T16:52:52Z|2024-03-19T18:04:02Z|https://github.co...|         COMPLETED|      21882987450|         Integration|       pull_request|               6249|        8345643302|https://github.co...|\n", "|             sass|           sass-spec|     2012|f8c6abb515bd18fdd...|          Unit tests|COMPLETED|   SUCCESS|28878483992|2024-08-16T20:55:23Z|2024-08-16T20:56:02Z|https://github.co...|         COMPLETED|      27270491164|                  CI|               push|               1553|       10426138304|https://github.co...|\n", "|     LIT-Protocol|              js-sdk|      591|                    |                    |         |          |          0|                    |                    |                    |                  |                0|                    |                   |                  0|                 0|                    |\n", "|           fenics|               basix|      767|d16bdba6a47492f7c...|Build Python usin...|COMPLETED|   SUCCESS|20189109903|2024-01-05T08:12:46Z|2024-01-05T08:14:09Z|https://github.co...|         COMPLETED|      19531398872|            Basix CI|               push|               6835|        7419486929|https://github.co...|\n", "|cah-douglas-short|   vscode-shellcheck|      683|3f1d8e0610d1329ba...|test (macos-lates...|COMPLETED|   FAILURE|26808807690|2024-06-28T12:43:56Z|2024-06-28T12:44:40Z|https://github.co...|         COMPLETED|      25428178516|                  ci|       pull_request|               2325|        9712979964|https://github.co...|\n", "+-----------------+--------------------+---------+--------------------+--------------------+---------+----------+-----------+--------------------+--------------------+--------------------+------------------+-----------------+--------------------+-------------------+-------------------+------------------+--------------------+\n", "only showing top 20 rows\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["cicd_metadata_df.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+--------------------+--------------------+---------+--------------------+--------------------+-----------+-----------+--------------------+\n", "|               owner|                name|pr_number|          commit_sha|      check_run_name|database_id|status_code|                 log|\n", "+--------------------+--------------------+---------+--------------------+--------------------+-----------+-----------+--------------------+\n", "|          scikit-hep|             awkward|     3159|23fab58f6c9c5e163...|Run Tests (3.9, x...|26490088807|        200|2_simplify64_to64...|\n", "|             osg-htc|   osg-htc.github.io|      237|c17b090e2c726d974...| Check-Website-Links|27823610827|        200|﻿2024-07-23T19:27...|\n", "|              nodejs|              undici|     3331|b0700c32053ea9b10...|test (22, macos-l...|26511202390|        200|﻿2024-06-21T09:59...|\n", "|              opidor|           d<PERSON><PERSON><PERSON>|      350|2f8cbc078db5c7097...|          postgresql|26996518442|        200|﻿2024-07-03T13:29...|\n", "|             pytorch|                  rl|     2267|dcd825c4340f8c31a...|test-wheel-window...|26988480917|        200|﻿2024-07-03T10:35...|\n", "|nationalsecuritya...|      skills-service|     2591|034e26798e3681db5...| ui-oauth-tests (10)|26414324191|        200|﻿2024-06-19T10:12...|\n", "|nationalsecuritya...|      skills-service|     2591|1ea441d672dc9d7e9...|  ui-oauth-tests (3)|26417569782|        200|﻿2024-06-19T11:31...|\n", "|               bcgov|             sbc-pay|     1623|aab77028fd91563b5...|      testing (3.12)|27570574514|        200|﻿2024-07-17T15:23...|\n", "|              apache|              pulsar|    22949|1c5afe931537f8d14...|Pulsar CI checks ...|27078640626|        200|﻿2024-07-05T10:02...|\n", "|            zenml-io|               zenml|     2831|945027ab4cc890afa...|custom-arc-runner...|27164037551|        200|﻿2024-07-08T13:39...|\n", "|            medusajs|              medusa|     8363|e0fb7b294ebda1551...|integration-tests...|28146048847|        200|﻿2024-07-31T08:11...|\n", "|            zenml-io|               zenml|     2808|63ffd29caed9d8872...|custom-arc-runner...|26939680827|        200|﻿2024-07-02T12:17...|\n", "|            zenml-io|               zenml|     2813|5f9ce50d6b02885cf...|macos-integration...|27161289360|        200|﻿2024-07-08T12:52...|\n", "|              splunk|addonfactory-ucc-...|     1265|68737365f87311921...|          fossa-scan|26998645360|        200|dency {dependency...|\n", "|    eclipse-tractusx|item-relationship...|      754|111885fc2f651cfcf...|         owasp-check|27009327142|        200|﻿2024-07-03T17:53...|\n", "|      demergent-labs|                azle|     1849|62728c1ed10f1f51f...|basic-integration...|26480697053|        200|﻿2024-06-20T17:16...|\n", "|                 vim|                 vim|    15667|94e94821dfbe533f0...|linux (huge, clan...|30024878233|        206|term_modeless_sel...|\n", "|              DFlats|           timup.pro|      199|6fbe9392e959564cd...|      build-and-test|30643939526|        206|﻿2024-09-25T12:42...|\n", "|                d2iq|              ui-kit|     1289|e70db9150ab6b1871...|Render Docker Com...|27422076526|        206|﻿2024-07-14T10:31...|\n", "|              google|           fuzzbench|     2002|4e4e5e9d06da62d88...|Test (proj4_proj_...|28096091653|        206|﻿2024-07-30T09:57...|\n", "+--------------------+--------------------+---------+--------------------+--------------------+-----------+-----------+--------------------+\n", "only showing top 20 rows\n", "\n"]}], "source": ["failed_logs_df.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}