from concurrent.futures import ThreadPoolExecutor
from multiprocessing.pool import Thread<PERSON><PERSON>
import shutil
from typing import Any, Callable, ParamSpec, TypeVar

from experimental.colin.projects.autofix.training.utils import AutofixProblem
import numpy as np
from research.data.spark.utils import k8s_session
import pyspark.sql.functions as F
from research.utils.repo_change_utils import CommitMeta, iterate_repo_history
import time
import logging
from pathlib import Path
import pickle
import random
import sys
import tarfile
import tempfile
from compress_pickle import dumps as compressed_dumps
from compress_pickle import loads as compressed_loads

from tqdm import tqdm
from research.data.spark.pipelines.utils import map_parquet
from research.data.utils.pr_v2 import get_tar_full_path
from research.utils.repo_change_utils import RepoChange
import pandas as pd
from pyspark.sql import Row
import psutil

def create_spark(max_workers: int):
    spark = k8s_session(
        max_workers=max_workers,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "32",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.task.maxFailures": "3",
            "spark.executor.memory": "32g",
        },
        pod_anti_affinity="preferred",
        ephemeral_storage_gb=256
    )

    return spark


spark = create_spark(1000)

MAX_NUM_COMMITS_PER_REPO = None

T = TypeVar('T')
def run_with_timeout(func: Callable[..., T], args=(), kwargs={}, timeout_duration=30) -> T | None:
    with ThreadPool(processes=1) as pool:
        def _func(args_and_kwargs: tuple[tuple, dict]):
            args, kwargs = args_and_kwargs
            return func(*args, **kwargs)
        
        result = pool.map_async(_func, [(args, kwargs)])
        try:
            return result.get(timeout=timeout_duration)[0]
        except TimeoutError:
            return None
        
def process_repo(repo_ID, problems):
    problems: list[AutofixProblem] = compressed_loads(problems, compression="gzip")
    parsed_problems: list[AutofixProblem] = []
    iterate_repo_history_times = []

    # on /mnt/efs/spark-data
    repo_tarball_path = get_tar_full_path(repo_ID)

    num_missing_commits = 0

    def _exit(
        parsed_problems: list[AutofixProblem],
        num_missing_commits: int,
        iterate_repo_history_times: list[float],
        e: str = ""
    ):
        num_problems = len(parsed_problems)
        
        return pd.Series({
            "repo_id": repo_ID,
            "problems": compressed_dumps(parsed_problems, compression="gzip"),
            "num_problems": num_problems,
            "num_missing_commits": num_missing_commits,
            "iterate_repo_history_times": compressed_dumps(iterate_repo_history_times, compression="gzip"),
            "error_message": e,
        })

    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        if not Path(repo_tarball_path).exists():
            err = f"ERROR: {repo_tarball_path} does not exist"
            all_commits = set()
            for problem in problems:
                all_commits.add(problem.passing_child.sha)
                for commit in problem.failed_commits:
                    all_commits.add(commit.sha)
            return _exit(
                parsed_problems=[],
                num_missing_commits=len(all_commits),
                iterate_repo_history_times=[],
                e=err
            )

        logging.error("Starting extraction!")
        start_time = time.time()
        with tarfile.open(repo_tarball_path, "r:*") as tar:
            tar.extractall(path=temp_path)
        end_time = time.time()
        logging.error(f"Took {end_time - start_time:.2f} seconds to extract for {repo_ID}")

        def materialize_iterate_repo_history(*args, **kwargs):
            return list(iterate_repo_history(*args, **kwargs))

        for problem in tqdm(problems, desc=f"iterate on commits for {repo_ID}"):
            logging.error("Starting grabbing commit!")
            try:
                start_time = time.time()

                parent_commit_sha = CommitMeta(
                    sha=problem.failed_commits[-1].parents[0],
                    message="",
                    repo_name=repo_ID,
                    parents=(),
                    children=(problem.failed_commits[-1].sha),
                )
                changes = run_with_timeout(
                    materialize_iterate_repo_history,
                    kwargs=dict(
                        project_dir=temp_path,
                        history=[parent_commit_sha] + list(reversed(problem.failed_commits)) + [problem.passing_child],
                        is_source_file=lambda x: True,
                    ),
                    timeout_duration=60,
                )
                end_time = time.time()
                iterate_repo_history_times.append(end_time - start_time)
                if changes is None:
                    logging.error(f"Timed out after {iterate_repo_history_times[-1]:.2f} seconds on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo...")
                    return _exit(
                        parsed_problems=parsed_problems,
                        num_missing_commits=num_missing_commits,
                        iterate_repo_history_times=iterate_repo_history_times,
                        e=f"Timed out on breaking change for {repo_ID} at sha {problem.failed_commits[0].sha}; Skipping repo..."
                    )

                # TEMPORARY IMPLEMENTATION
                assert False, "please pick one"
                # make breaking diff all but last change
                #fixing_change = changes[-1]
                #breaking_change = changes[0]
                #for change in changes[1:-1]:
                #    breaking_change = breaking_change.squash(change)

                # make fixing diff all but first change
                #breaking_change = changes[0]
                #fixing_change = changes[1]
                #for change in changes[2:]:
                #    fixing_change = fixing_change.squash(change)
                # END TEMPORARY IMPLEMENTATION
                problem.breaking_change = breaking_change
                problem.fixing_change = fixing_change
                parsed_problems.append(problem)
            except Exception as e:
                logging.error(f"Failed to grab commit for reason: {e}")
                logging.error(f"Exception type: {type(e)}")
                logging.error(f"Exception args: {e.args}")
                num_missing_commits += 1
                continue

            
            start_time = time.time()
            # intern pool to save memory
            for p in fixing_change.before_files:
                fixing_change.before_files.set(
                    p, sys.intern(fixing_change.before_files[p])
                )
            for p in fixing_change.after_files:
                fixing_change.after_files.set(
                    p, sys.intern(fixing_change.after_files[p])
                )
            for p in breaking_change.before_files:
                breaking_change.before_files.set(
                    p, sys.intern(breaking_change.before_files[p])
                )
            for p in breaking_change.after_files:
                breaking_change.after_files.set(
                    p, sys.intern(breaking_change.after_files[p])
                )
            end_time = time.time()
            logging.error(f"Took {end_time - start_time:.2f} seconds to intern pool")

    return _exit(
        parsed_problems=parsed_problems,
        num_missing_commits=num_missing_commits,
        iterate_repo_history_times=iterate_repo_history_times,
        e=""
    )

def safe_process_repo(repo_ID, problems):
    try:
        return process_repo(repo_ID, problems)
    except EOFError as e:
        return pd.Series(
            {
                "repo_id": repo_ID,
                "problems": compressed_dumps([], compression="gzip"),
                "num_problems": 0,
                "num_missing_commits": 0,
                "iterate_repo_history_times": compressed_dumps([], compression="gzip"),
                "error_message": str(e),
            }
        )


# pretty print start time in Pacific Tmezone
print(f"Starting at {pd.Timestamp.now(tz='US/Pacific')}")
TRAINING_DATA_BASE = "/mnt/efs/spark-data/user/colin/bugfix_localization_model/v9"
STAGE3_PATH = f"{TRAINING_DATA_BASE}/stage3_with_exploded_problems"
STAGE4_PATH = f"{TRAINING_DATA_BASE}/stage4_with_hydrated_commits"

num_retries = 3
for i in range(num_retries):
    try:
        start_time = time.time()
        result = map_parquet.apply(
            spark,
            safe_process_repo,
            input_path=STAGE3_PATH,
            output_path=STAGE4_PATH,
            input_columns=["repo_ID", "problems"],
            batch_size=1,
            timeout=50_000,
            ignore_error=False,
            allow_resume=True,
        )
        end_time = time.time()
        time_in_mins = (end_time - start_time) / 60
        print(f"Time taken: {time_in_mins} mins")
    except Exception as e:
        print(f"Failed job try {i} with error: {e}")
        if i < num_retries - 1:
            print(f"Retrying job {i+1}...Sleeping 5 minutes first.")
            time.sleep(5*60)
