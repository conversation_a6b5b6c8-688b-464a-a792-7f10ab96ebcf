{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import uuid\n", "\n", "output_path = f\"/tmp/{str(uuid.uuid4())}\"\n", "print(output_path)\n", "logger = logging.getLogger(__name__)\n", "\n", "# Remove all existing handlers\n", "for handler in logger.handlers[:]:\n", "    logger.<PERSON><PERSON><PERSON><PERSON>(handler)\n", "\n", "logger.setLevel(logging.INFO)\n", "c_handler = logging.StreamHandler()  # Console handler\n", "f_handler = logging.FileHandler(output_path)  # File handler\n", "\n", "formatter = logging.Formatter(\"%(message)s\")\n", "c_handler.setFormatter(formatter)\n", "f_handler.setFormatter(formatter)\n", "\n", "logger.addH<PERSON>ler(c_handler)\n", "logger.addHandler(f_handler)\n", "\n", "# Disable propagation to parent loggers\n", "logger.propagate = False"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pickle\n", "import json\n", "\n", "# artifact is from https://determined.gcp-us1.r.augmentcode.com/det/experiments/3138/trials/3138/logs\n", "artifacts = (\n", "    \"/mnt/efs/augment/eval/jobs/NGyFR5LK/000__AutofixSystem_AutofixTask_artifacts.pkl\"\n", ")\n", "results = \"/mnt/efs/augment/eval/jobs/NGyFR5LK/000_results.jsonl\"\n", "input_path = \"/mnt/efs/augment/user/colin/autofix_e2e_eval/datasets/v14.pkl\"\n", "\n", "with open(artifacts, \"rb\") as f:\n", "    artifacts = pickle.load(f)\n", "\n", "with open(results, \"r\") as f:\n", "    results = json.load(f)\n", "\n", "with open(input_path, \"rb\") as f:\n", "    problems = pickle.load(f)\n", "problems = problems.to_dict(orient=\"records\")\n", "\n", "artifacts_scores_problems = list(zip(artifacts, results[\"score_items\"], problems))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_system\n", "import yaml\n", "\n", "autofix_template = yaml.safe_load(\"\"\"\n", "name: autofix\n", "use_gold_locations: False\n", "use_localization: True\n", "top_k_edit_locations: 64\n", "max_concurrent: 10\n", "context_retrieval: False\n", "\"\"\")\n", "autofix_system = create_system(autofix_template)\n", "autofix_system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_system\n", "import yaml\n", "\n", "autofix_template = yaml.safe_load(\"\"\"\n", "name: autofix\n", "use_gold_locations: True\n", "use_localization: False\n", "top_k_edit_locations: 64\n", "max_concurrent: 10\n", "context_retrieval: False\n", "\"\"\")\n", "autofix_system_with_gold = create_system(autofix_template)\n", "autofix_system_with_gold.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# grab examples with overlap scores < 0.8\n", "low_overlap_artifacts_scores_problems = [\n", "    (artifact, scores, problems)\n", "    for artifact, scores, problems in artifacts_scores_problems\n", "    if scores[\"overlap_score\"] < 0.8\n", "]\n", "logger.info(f\"Found {len(low_overlap_artifacts_scores_problems)} low overlap artifacts\")\n", "\n", "datasets = {}\n", "\n", "datasets[\"version0\"] = artifacts_scores_problems\n", "datasets[\"version1\"] = low_overlap_artifacts_scores_problems"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find examples where a gold location is completely missing\n", "from base.prompt_format_autofix.common import PromptChunkWithLines\n", "\n", "\n", "def prompt_chunk_with_lines_are_overlapping(\n", "    chunk1: PromptChunkWithLines, chunk2: PromptChunkWithLines\n", "):\n", "    if chunk1.path != chunk2.path:\n", "        return False\n", "    chunk1_start = chunk1.line_offset\n", "    chunk1_end = chunk1.line_offset + chunk1.length_in_lines\n", "    chunk2_start = chunk2.line_offset\n", "    chunk2_end = chunk2.line_offset + chunk2.length_in_lines\n", "    return max(chunk1_start, chunk2_start) < min(chunk1_end, chunk2_end)\n", "\n", "\n", "bad_localization_examples = []\n", "for artifact, scores, problems in low_overlap_artifacts_scores_problems:\n", "    gold_edit_locations = [\n", "        PromptChunkWithLines(**loc) for loc in artifact.artifacts[\"gold_edit_locations\"]\n", "    ]\n", "    if len(gold_edit_locations) > 3:\n", "        continue\n", "    candidate_edit_locations = [\n", "        PromptChunkWithLines(**loc) for loc in artifact.artifacts[\"edit_locations\"]\n", "    ]\n", "    num_locations_found = 0\n", "    for gold_loc in gold_edit_locations:\n", "        for loc in candidate_edit_locations:\n", "            # if prompt_chunk_with_lines_are_overlapping(gold_loc, loc):\n", "            #    num_locations_found += 1\n", "            #    break\n", "            if gold_loc.path == loc.path:\n", "                num_locations_found += 1\n", "                break\n", "    if num_locations_found < len(gold_edit_locations):\n", "        bad_localization_examples.append((artifact, scores, problems))\n", "\n", "logger.info(f\"Found {len(bad_localization_examples)} bad localization examples\")\n", "\n", "datasets[\"version2\"] = bad_localization_examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "import dataclasses\n", "from research.core.types import Chunk\n", "from base.prompt_format_autofix.common import PromptChunkWithLines\n", "from base.prompt_format_completion.overlap import partial_overlap_predicate\n", "from experimental.colin.projects.autofix.training.data_pipelines.utils import (\n", "    repo_change_to_str,\n", ")\n", "from research.eval.harness.systems.autofix_system import AutofixSystemInput\n", "from research.autofix.autofix_eval_static import (\n", "    repo_change_to_docs,\n", "    compute_scores,\n", ")\n", "import json\n", "from pydantic.json import pydantic_encoder\n", "from research.utils.repo_change_utils import (\n", "    RepoChange,\n", "    repo_change_from_repositories,\n", ")\n", "\n", "\n", "def diff_repo_changes(\n", "    before_repo_change: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    after_repo_change: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ") -> RepoChange:\n", "    \"\"\"Compute the diff between two repo changes, NOT including the changes in the repo changes themselves.\n", "\n", "    ie we grab the after files from the before repo change\n", "    and the before files from the after repo change,\n", "    and then we diff those.\n", "    \"\"\"\n", "    diff_before_files = before_repo_change.after_repo()\n", "    diff_after_files = after_repo_change.after_repo()\n", "    return repo_change_from_repositories(\n", "        diff_before_files,\n", "        diff_after_files,\n", "    )\n", "\n", "\n", "gather_user_feedback = False\n", "dataset_version = \"version2\"\n", "problem_idx = 21\n", "artifact, score, problem = datasets[dataset_version][problem_idx]\n", "logger.info(f\"Dataset version: {dataset_version}\")\n", "logger.info(\"Ran filter for overlap scores then for localization scores\")\n", "\n", "# logger.info(artifact.artifacts[\"model_input\"][\"command_output\"])\n", "# assert False\n", "\n", "logger.info(f\"Example {problem_idx}: overlap_score={score['overlap_score']:.2f}\")\n", "\n", "repo_name = problem[\"repo_name\"]\n", "id = problem[\"id\"]\n", "check_run_name = problem[\"check_run_name\"]\n", "sha = problem[\"sha\"]\n", "\n", "logger.info(\n", "    f\"repo_name: {repo_name}, id: {id}, check_run_name: {check_run_name}, sha: {sha}, index in dataset: {problem_idx}\"\n", ")\n", "\n", "logger.info(f\"{'-' * 200}\\nCOMMAND OUTPUT\\n{'-' * 200}\\n\")\n", "logger.info(artifact.artifacts[\"model_input\"][\"command_output\"])\n", "\n", "example_docs = repo_change_to_docs(problem[\"breaking_change\"])  # type: ignore\n", "doc_ids = frozenset([doc.id for doc in example_docs])\n", "updated_problem = AutofixSystemInput(\n", "    command_output=problem[\"log\"],  # type: ignore\n", "    breaking_change=problem[\"breaking_change\"],  # type: ignore\n", "    doc_ids=doc_ids,\n", "    fixing_change=problem[\"fixing_change\"],  # type: ignore\n", ")\n", "new_suggestion = autofix_system.generate(updated_problem)\n", "suggested_change_str_initial = repo_change_to_str(new_suggestion.suggested_change)\n", "\n", "logger.info(f\"{'-' * 200}\\nFIX PLAN BEFORE FEEDBACK\\n{'-' * 200}\\n\")\n", "fix_plan = new_suggestion.artifacts[\"fix_plan\"]\n", "fix_plan_str = fix_plan[\"fix_desc\"] + \"\\n\"\n", "for change in fix_plan[\"changes\"]:\n", "    fix_plan_str += f\" - {change['path']}: {change['change_desc']}\\n\"\n", "logger.info(fix_plan_str)\n", "\n", "logger.info(f\"{'-' * 200}\\nEDITED LOCATIONS BEFORE FEEDBACK\\n{'-' * 200}\\n\")\n", "logger.info(\n", "    f\"num_edited_locations: {len(new_suggestion.artifacts['edited_locations'])}\"\n", ")\n", "for loc in new_suggestion.artifacts[\"edited_locations\"]:\n", "    logger.info(\n", "        f\" - {loc['path']}: {loc['line_offset']}-{loc['line_offset']+loc['length_in_lines']}\"\n", "    )\n", "\n", "logger.info(f\"{'-' * 200}\\nGOLD EDIT LOCATIONS\\n{'-' * 200}\\n\")\n", "logger.info(\n", "    f\"num_gold_edit_locations: {len(artifact.artifacts['gold_edit_locations'])}\"\n", ")\n", "for loc in artifact.artifacts[\"gold_edit_locations\"]:\n", "    logger.info(\n", "        f\" - {loc['path']}: {loc['line_offset']}-{loc['line_offset']+loc['length_in_lines']}\"\n", "    )\n", "\n", "logger.info(f\"{'-' * 200}\\nGOLD FILE IN CANDIDATE SET BEFORE FEEDBACK\\n{'-' * 200}\\n\")\n", "gold_edit_locations = [\n", "    PromptChunkWithLines(**loc) for loc in artifact.artifacts[\"gold_edit_locations\"]\n", "]\n", "candidate_edit_locations = [\n", "    PromptChunkWithLines(**loc) for loc in new_suggestion.artifacts[\"edit_locations\"]\n", "]\n", "overlapped_locations = set()\n", "for gold_loc in gold_edit_locations:\n", "    for loc in candidate_edit_locations:\n", "        if gold_loc.path == loc.path:\n", "            overlapped_locations.add(loc)\n", "for loc in overlapped_locations:\n", "    logger.info(\n", "        f\" - {loc.path}: {loc.line_offset}-{loc.line_offset+loc.length_in_lines}\"\n", "    )\n", "\n", "if gather_user_feedback:\n", "    user_input = input(\"Feedback: \")\n", "    logger.info(f\"{'-' * 200}\\nUSER FEEDBACK:\\n{'-' * 200}\\n\")\n", "    logger.info(user_input)\n", "\n", "    updated_problem = dataclasses.replace(\n", "        updated_problem,\n", "        user_feedback=[user_input],\n", "        # prior_fix_plan=suggested_change_str\n", "        # prior_fix_plan=fix_plan_str\n", "        prior_fix_plan=f\"Here is the summary of my previous fix plan:\\n{fix_plan_str}\\nHere is the diff of my previous fix:\\n{suggested_change_str_initial}\\n.\",\n", "    )\n", "    new_suggestion = autofix_system_with_gold.generate(updated_problem)\n", "\n", "    logger.info(f\"{'-' * 200}\\nUPDATED FIX PLAN AFTER FEEDBACK\\n{'-' * 200}\\n\")\n", "    fix_plan = new_suggestion.artifacts[\"fix_plan\"]\n", "    logger.info(fix_plan[\"fix_desc\"])\n", "    for change in fix_plan[\"changes\"]:\n", "        logger.info(f\" - {change['path']}: {change['change_desc']}\")\n", "\n", "    logger.info(f\"{'-' * 200}\\nUPDATED EDITED LOCATIONS AFTER FEEDBACK\\n{'-' * 200}\\n\")\n", "    logger.info(\n", "        f\"num_edited_locations: {len(new_suggestion.artifacts['edited_locations'])}\"\n", "    )\n", "    for loc in new_suggestion.artifacts[\"edited_locations\"]:\n", "        logger.info(\n", "            f\" - {loc['path']}: {loc['line_offset']}-{loc['line_offset']+loc['length_in_lines']}\"\n", "        )\n", "\n", "    logger.info(\n", "        f\"{'-' * 200}\\nUPDATED GOLD EDIT LOCATIONS IN CANDIDATE SET AFTER FEEDBACK\\n{'-' * 200}\\n\"\n", "    )\n", "    gold_edit_locations = [\n", "        PromptChunkWithLines(**loc) for loc in artifact.artifacts[\"gold_edit_locations\"]\n", "    ]\n", "    candidate_edit_locations = [\n", "        PromptChunkWithLines(**loc)\n", "        for loc in new_suggestion.artifacts[\"edit_locations\"]\n", "    ]\n", "    overlapped_locations = set()\n", "    for gold_loc in gold_edit_locations:\n", "        for loc in candidate_edit_locations:\n", "            if gold_loc.path == loc.path:\n", "                overlapped_locations.add(loc)\n", "    for loc in overlapped_locations:\n", "        logger.info(\n", "            f\" - {loc.path}: {loc.line_offset}-{loc.line_offset+loc.length_in_lines}\"\n", "        )\n", "\n", "    logger.info(f\"{'-' * 200}\\nSUGGESTED FIX DIFF BEFORE FEEDBACK\\n{'-' * 200}\\n\")\n", "    logger.info(suggested_change_str_initial)\n", "\n", "    logger.info(f\"{'-' * 200}\\nSUGGESTED FIX DIFF AFTER FEEDBACK\\n{'-' * 200}\\n\")\n", "    suggested_change_str = repo_change_to_str(new_suggestion.suggested_change)\n", "    logger.info(suggested_change_str)\n", "\n", "    logger.info(f\"{'-' * 200}\\nGOLD FIX DIFF\\n{'-' * 200}\\n\")\n", "    gold_change_str = repo_change_to_str(problem[\"fixing_change\"])  # type: ignore\n", "    logger.info(gold_change_str)\n", "\n", "    logger.info(f\"{'-' * 200}\\nDIFF BETWEEN SUGGESTED FIX AND GOLD FIX\\n{'-' * 200}\\n\")\n", "\n", "    diff_change = diff_repo_changes(\n", "        new_suggestion.suggested_change, problem[\"fixing_change\"]\n", "    )\n", "    diff_change_str = repo_change_to_str(diff_change)\n", "    logger.info(diff_change_str)\n", "\n", "    logger.info(f\"{'-' * 200}\\nUSER FEEDBACK SECTION ADDED TO PROMPT\\n{'-' * 200}\\n\")\n", "    section = (\n", "        \"You previously suggested an incorrect fix.\"\n", "        + new_suggestion.artifacts[\"prompt\"]\n", "        .split(\"You previously suggested an incorrect fix.\", 1)[1]\n", "        .split(\n", "            \"Carefully review the provided information and create a detailed fix plan accordingly.\",\n", "            1,\n", "        )[0]\n", "    )\n", "    logger.info(section)\n", "\n", "    logger.info(f\"\\n{'-' * 200}\\n{'-' * 200}\\n\")\n", "else:\n", "    logger.info(f\"{'-' * 200}\\nSUGGESTED FIX DIFF BEFORE FEEDBACK\\n{'-' * 200}\\n\")\n", "    logger.info(suggested_change_str_initial)\n", "\n", "    logger.info(f\"{'-' * 200}\\nGOLD FIX DIFF\\n{'-' * 200}\\n\")\n", "    gold_change_str = repo_change_to_str(problem[\"fixing_change\"])  # type: ignore\n", "    logger.info(gold_change_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"{'-' * 200}\\nFIX PLAN PROMPT\\n{'-' * 200}\\n\")\n", "logger.info(new_suggestion.artifacts[\"prompt\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load localization model and see paths of top N results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(f\"{'-' * 200}\\nLOCALIZATION EXPERIMENT\\n{'-' * 200}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "\n", "localization_config_main = {\n", "    \"scorer\": {\n", "        \"name\": \"dense_scorer_v2_fbwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/autofix-location/v17\",\n", "        \"cache_dir\": \"/tmp/augment/localization_cache\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 2000,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"next_edit_location_query\",\n", "        \"tokenizer\": \"starcoder\",\n", "        \"max_prompt_tokens\": 8192,\n", "        \"max_diff_tokens\": 4096,\n", "        \"max_instruction_tokens\": 0,\n", "        \"max_command_output_tokens\": 4096,\n", "        \"use_smart_header\": True,\n", "        \"deduplicate_identical_paths\": True,\n", "        \"truncate_instructions_tail\": <PERSON>alse,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:ethanol6-embedding-with-path-key\",\n", "        \"tokenizer\": \"starcoder\",\n", "        \"max_tokens\": 999,\n", "    },\n", "}\n", "\n", "localization_retriever = create_retriever(localization_config_main)\n", "localization_retriever.add_docs(example_docs)\n", "\n", "context_retriever_config = {\n", "    \"scorer\": {\n", "        # \"name\": \"dense_scorer_v2_fbwd\",\n", "        # \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/tongfei/test\",\n", "        \"name\": \"dense_scorer_v2_ffwd\",\n", "        \"checkpoint_path\": \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-18.hybrid/neox/global_step1468\",\n", "        \"cache_dir\": \"/tmp/augment/chatanol_cache2\",\n", "    },\n", "    \"chunker\": {\n", "        \"name\": \"smart_line_level\",\n", "        \"max_chunk_chars\": 2000,\n", "    },\n", "    \"query_formatter\": {\n", "        \"name\": \"base:chatanol6-singleturnisspecial\",\n", "        \"tokenizer_name\": \"rogue\",\n", "        \"max_tokens\": 1024,\n", "    },\n", "    \"document_formatter\": {\n", "        \"name\": \"base:chatanol6-embedding-with-path-key\",\n", "        \"tokenizer_name\": \"rogue\",\n", "    },\n", "}\n", "\n", "context_retriever = create_retriever(context_retriever_config)\n", "context_retriever.add_docs(example_docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.retrieval.types import Document\n", "from base.ranges.range_types import CharRange\n", "from base.diff_utils.diff_utils import File\n", "from base.prompt_format_next_edit.location_prompt_formatter import (\n", "    LocalizationNextEditPromptInput,\n", ")\n", "from base.diff_utils.changes import Modified\n", "\n", "# call query to localization retriever with problem\n", "example_docs = repo_change_to_docs(problem[\"breaking_change\"])  # type: ignore\n", "doc_ids = frozenset([doc.id for doc in example_docs])\n", "recent_changes = [\n", "    Modified(\n", "        File(path=str(file.before.path), contents=file.before.code),\n", "        File(path=str(file.after.path), contents=file.after.code),\n", "    )\n", "    for file in problem[\"breaking_change\"].changed_files\n", "    if isinstance(file, Modified)\n", "]\n", "localization_model_input = LocalizationNextEditPromptInput(\n", "    current_file=File(\"\", \"\"),\n", "    edit_region=CharRange(0, 0),\n", "    instruction=\"\",\n", "    command_output=problem[\"log\"],\n", "    recent_changes=recent_changes,\n", ")\n", "\n", "logger.info(f\"{'-' * 200}\\nGOLD EDIT LOCATIONS\\n{'-' * 200}\\n\")\n", "logger.info(\n", "    f\"num_gold_edit_locations: {len(artifact.artifacts['gold_edit_locations'])}\"\n", ")\n", "for loc in artifact.artifacts[\"gold_edit_locations\"]:\n", "    logger.info(\n", "        f\" - {loc['path']}: {loc['line_offset']}-{loc['line_offset']+loc['length_in_lines']}\"\n", "    )\n", "\n", "localization_chunks, _ = localization_retriever.query(\n", "    localization_model_input, doc_ids=doc_ids, top_k=64\n", ")\n", "# compute overlapping chunks\n", "\n", "localization_chunks = [\n", "    PromptChunkWithLines(\n", "        text=chunk.text,\n", "        path=chunk.parent_doc.path,\n", "        char_start=chunk.char_offset,\n", "        char_end=chunk.char_offset + chunk.length,\n", "        line_offset=chunk.line_offset,\n", "        length_in_lines=chunk.length_in_lines,\n", "    )\n", "    for chunk in localization_chunks\n", "]\n", "\n", "overlapping_paths = set()\n", "for chunk in localization_chunks:\n", "    for loc in artifact.artifacts[\"gold_edit_locations\"]:\n", "        if chunk.path == loc[\"path\"]:\n", "            overlapping_paths.add(chunk.path)\n", "\n", "logger.info(\n", "    f\"{'-' * 200}\\nOVERLAPPING LOCALIZATION PATHS BEFORE STEERING(COUNT: {len(overlapping_paths)})\\n{'-' * 200}\\n\"\n", ")\n", "for path in overlapping_paths:\n", "    logger.info(f\" - {path}\")\n", "\n", "logger.info(\n", "    f\"{'-' * 200}\\nLOCALIZATION PATHS BEFORE STEERING BY RELEVANCY (COUNT: {len(localization_chunks)})\\n{'-' * 200}\\n\"\n", ")\n", "logger.info(\"Filtering for chunks with feature_masternode_operator.py in path\")\n", "for rank, chunk in enumerate(localization_chunks):\n", "    if \"feature_masternode_operator.py\" in chunk.path:\n", "        logger.info(f\"rank {rank}: {chunk.path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from textwrap import dedent\n", "from base.prompt_format_retrieve import ChatRetrieverPromptInput\n", "from base.third_party_clients.token_counter.token_counter_claude import (\n", "    <PERSON><PERSON><PERSON>,\n", ")\n", "from base.prompt_format_autofix.common import (\n", "    PromptChunkWithLines,\n", "    format_chunks,\n", "    format_command_output,\n", ")\n", "\n", "claude_tok_counter = ClaudeTokenCounter()\n", "\n", "breaking_change_str = repo_change_to_str(problem[\"breaking_change\"])\n", "\n", "chunk_formatted = format_chunks(\n", "    localization_chunks,\n", "    claude_tok_counter,\n", "    50000,\n", "    command_output=problem[\"log\"],\n", ")\n", "\n", "test_claude_context_retrieval = False\n", "if test_claude_context_retrieval:\n", "    prompt = dedent(\n", "        f\"\"\"\n", "    Here are some failure logs from a failed test:\n", "    {problem[\"log\"]}\n", "\n", "    Below are some code excerpts from the codebase:\n", "    {chunk_formatted}\n", "\n", "    What is one critical question you would ask about the codebase to resolve the error below that cannot be answered by the provided code excerpts?\n", "    Specify relevant files, don't specify line numbers, use identifers like classes and functions instead.\n", "    Use no prefix or suffix, respond with the question only.\n", "    \"\"\"\n", "    )\n", "else:\n", "    fix_plan_str = new_suggestion.artifacts[\"fix_plan\"][\"fix_desc\"] + \"\\n\"\n", "    for change in new_suggestion.artifacts[\"fix_plan\"][\"changes\"]:\n", "        fix_plan_str += f\" - {change['path']}: {change['change_desc']}\\n\"\n", "    user_feedback = \"I think the fix is actually to update TestMasternodeOperator to not use the node1 key\"\n", "    prompt = dedent(\n", "        f\"\"\"\n", "    Here are some failure logs from a failed test:\n", "    {problem[\"log\"]}\n", "\n", "    Here is our current plan for how to fix it:\n", "    {fix_plan_str}\n", "\n", "    Here is some feedback from a smart colleague regarding how to improve our plan for how to fix the failure:\n", "    {user_feedback}\n", "\n", "    What files, classes, or functions would you like to look at to implement the feedback?\n", "    Use no prefix or suffix, respond with the question only.\n", "    \"\"\"\n", "    )\n", "\n", "generated_query = await autofix_system.client._ask_model(\n", "    prompt=prompt,\n", "    model_name=\"sonnet\",\n", ")\n", "\n", "input = ChatRetrieverPromptInput(\n", "    prefix=\"\",\n", "    suffix=\"\",\n", "    path=\"\",\n", "    message=generated_query,\n", "    selected_code=\"\",\n", "    chat_history=[],\n", ")\n", "chunks, _ = context_retriever.query(\n", "    model_input=input,\n", "    doc_ids=doc_ids,\n", "    top_k=None,\n", ")\n", "\n", "logger.info(f\"{'-' * 200}\\nGENERATED QUERY FOR CHATANOL STEERING\\n{'-' * 200}\\n\")\n", "logger.info(generated_query)\n", "\n", "logger.info(\n", "    f\"{'-' * 200}\\nCONTEXT RETRIEVAL PATHS AFTER STEERING (COUNT: {len(chunks)})\\n{'-' * 200}\\n\"\n", ")\n", "logger.info(\"Filtering for chunks with feature_masternode_operator.py in path\")\n", "for rank, chunk in enumerate(chunks):\n", "    if \"feature_masternode_operator.py\" in chunk.path:\n", "        logger.info(f\"rank {rank}: {chunk.path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(chunks)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}