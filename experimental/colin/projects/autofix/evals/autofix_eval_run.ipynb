{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import glob\n", "import json\n", "from dataclasses import dataclass\n", "import pickle\n", "from typing import List\n", "from research.core.types import Document\n", "import yaml\n", "from base.prompt_format_next_edit.common import NextEditPromptInput\n", "from research.eval.harness.factories import create_retriever\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from base.prompt_format_next_edit.location_prompt_formatter import (\n", "    NextEditLocationQueryFormatter,\n", ")\n", "from research.core.tokenizers import get_tokenizer\n", "from research.retrieval.types import DocumentIndex\n", "from base.diff_utils.diff_utils import File\n", "\n", "\n", "from collections.abc import Sequence\n", "from base.diff_utils.changes import Changed\n", "from base.ranges.range_types import CharRange\n", "from research.core.types import Chunk\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import PromptFormatterOutput\n", "from base.prompt_format_next_edit.location_prompt_formatter import (\n", "    LocalizationNextEditPromptInput,\n", ")\n", "from base.tokenizers.tokenizer import Tokenizer\n", "\n", "\n", "@dataclass(frozen=True)\n", "class AutofixEvalExample:\n", "    logs: str\n", "    diff: Sequence[Changed[File]]\n", "    difficulty: str\n", "    gold_path: str\n", "    gold_substr: str | None\n", "    gold_char_idxs: list[list[str, int]]\n", "    repo: list[Document]\n", "\n", "\n", "def load_eval_examples(\n", "    limit_examples: list[str] | None = None,\n", ") -> List[AutofixEvalExample]:\n", "    dataset_dir = \"/home/<USER>/augment/experimental/colin/projects/autofix/evals/augment_autofix_v1/\"\n", "    example_stems = glob.glob(\"*\", root_dir=dataset_dir)\n", "    print(\"examples:\", example_stems)\n", "\n", "    if limit_examples is not None:\n", "        print(f\"Limiting examples to {limit_examples}.\")\n", "\n", "    examples = []\n", "    for example in example_stems:\n", "        full_example_dir = f\"{dataset_dir}/{example}\"\n", "        logs_path = f\"{full_example_dir}/logs.txt\"\n", "        gold_info_path = f\"{full_example_dir}/gold_info.json\"\n", "\n", "        if limit_examples is not None and not any(\n", "            [valid_substr in example for valid_substr in limit_examples]\n", "        ):\n", "            print(f\"Skipping example {example} due to limit_examples.\")\n", "            continue\n", "\n", "        with open(logs_path) as f:\n", "            logs = f.read()\n", "        with open(gold_info_path) as f:\n", "            gold_info = json.load(f)\n", "\n", "        with open(gold_info[\"repo_path\"], \"rb\") as f:\n", "            repo: list[Document] = pickle.load(f)\n", "\n", "        with open(gold_info[\"diff_path\"], \"rb\") as f:\n", "            diff: Sequence[Changed[File]] = pickle.load(f)\n", "\n", "        examples.append(\n", "            AutofixEvalExample(\n", "                logs=logs,\n", "                diff=diff,\n", "                difficulty=gold_info[\"difficulty\"],\n", "                gold_path=gold_info[\"gold_path\"],\n", "                gold_substr=gold_info.get(\"gold_substr\", None),\n", "                gold_char_idxs=gold_info.get(\"gold_char_idxs\", []),\n", "                repo=repo,\n", "            )\n", "        )\n", "\n", "    return examples\n", "\n", "\n", "@dataclass(frozen=True)\n", "class AutofixEvalOutput:\n", "    query_prompt: str\n", "    gold_path_rank: int\n", "    gold_substr_rank: int\n", "    gold_char_idxs_ranks: list[int]\n", "    chunks: list[Chunk]\n", "\n", "\n", "def eval(\n", "    retrieval_db: DocumentIndex[NextEditPromptInput], examples: List[AutofixEvalExample]\n", ") -> List[AutofixEvalOutput]:\n", "    scores: List[AutofixEvalOutput] = []\n", "    retrieval_db.remove_all_docs()\n", "\n", "    tokenizer: Tokenizer = retrieval_db.scorer.query_formatter.tokenizer\n", "\n", "    for example in examples:\n", "        print(f\"Processing example {example.gold_path}\")\n", "        print(f\"Adding {len(example.repo)} documents.\")\n", "        retrieval_db.add_docs(example.repo)\n", "        print(\"Finished adding documents.\")\n", "\n", "        model_input = LocalizationNextEditPromptInput(\n", "            current_file=File(\"\", \"\"),\n", "            edit_region=CharRange(0, 0),\n", "            instruction=f\"Here are some logs with a failed test in them. Please fix.\\n\\n{example.logs}\",\n", "            recent_changes=example.diff,\n", "        )\n", "        print(\"Formatting prompt\")\n", "        prompt: PromptFormatterOutput = (\n", "            retrieval_db.scorer.query_formatter.format_prompt(model_input)\n", "        )\n", "        prompt_str = tokenizer.detokenize(prompt.tokens())\n", "\n", "        print(\"Running query\")\n", "        chunks, _ = retrieval_db.query(model_input)\n", "\n", "        gold_path_rank = None\n", "        gold_substr_rank = None\n", "        gold_char_idxs_ranks = []\n", "        for i, chunk in enumerate(chunks):\n", "            if chunk.path == example.gold_path:\n", "                if gold_path_rank is None:\n", "                    gold_path_rank = i\n", "\n", "                if (\n", "                    example.gold_substr is not None\n", "                    and example.gold_substr in chunk.text\n", "                    and gold_substr_rank is None\n", "                ):\n", "                    gold_substr_rank = i\n", "\n", "                for path, char_idx in example.gold_char_idxs:\n", "                    if path == example.gold_path and char_idx in chunk.range:\n", "                        gold_char_idxs_ranks.append(i)\n", "\n", "        assert gold_path_rank is not None\n", "        assert gold_substr_rank is not None or len(gold_char_idxs_ranks) > 0\n", "\n", "        scores.append(\n", "            AutofixEvalOutput(\n", "                query_prompt=prompt_str,\n", "                gold_path_rank=gold_path_rank,\n", "                gold_substr_rank=gold_substr_rank,\n", "                gold_char_idxs_ranks=gold_char_idxs_ranks,\n", "                chunks=chunks,\n", "            )\n", "        )\n", "        retrieval_db.remove_all_docs()\n", "    return scores"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["debug = False\n", "\n", "\n", "def eval_viz(\n", "    examples: List[AutofixEvalExample],\n", "    modelA: List[AutofixEvalOutput],\n", "    modelB: List[AutofixEvalOutput],\n", "    modelA_name: str,\n", "    modelB_name: str,\n", "):\n", "    modelA_median_rank = np.median([o.gold_path_rank for o in modelA])\n", "    modelB_median_rank = np.median([o.gold_path_rank for o in modelB])\n", "    print(\n", "        f\"Median rank of gold path: {modelA_median_rank} for {modelA_name} and {modelB_median_rank} for {modelB_name} \"\n", "    )\n", "    modelA_median_rank = np.median(\n", "        [o.gold_substr_rank for o in modelA if o.gold_substr_rank is not None]\n", "        + [\n", "            rank\n", "            for o in modelA\n", "            for rank in o.gold_char_idxs_ranks\n", "            if len(o.gold_char_idxs_ranks) > 0\n", "        ]\n", "    )\n", "    modelB_median_rank = np.median(\n", "        [o.gold_substr_rank for o in modelB if o.gold_substr_rank is not None]\n", "        + [\n", "            rank\n", "            for o in modelB\n", "            for rank in o.gold_char_idxs_ranks\n", "            if len(o.gold_char_idxs_ranks) > 0\n", "        ]\n", "    )\n", "\n", "    if debug:\n", "        print(\"Model A gold path rank: \", [o.gold_path_rank for o in modelA])\n", "        print(\"Model B gold path rank: \", [o.gold_path_rank for o in modelB])\n", "        print(\"Model A gold location rank: \", [o.gold_substr_rank for o in modelA])\n", "        print(\"Model B gold location rank: \", [o.gold_substr_rank for o in modelB])\n", "        print(\n", "            \"Model A gold char idxs ranks: \",\n", "            [rank for o in modelA for rank in o.gold_char_idxs_ranks],\n", "        )\n", "        print(\n", "            \"Model B gold char idxs ranks: \",\n", "            [rank for o in modelB for rank in o.gold_char_idxs_ranks],\n", "        )\n", "\n", "    print(\n", "        f\"Median rank of gold location: {modelA_median_rank} for {modelA_name} and {modelB_median_rank} for {modelB_name}\"\n", "    )\n", "\n", "    toy_examples = []\n", "    easy_examples = []\n", "    for i, (example, modelA_output, modelB_output) in enumerate(\n", "        zip(examples, modelA, modelB)\n", "    ):\n", "        if example.difficulty == \"easy\":\n", "            easy_examples.append((i, example, modelA_output, modelB_output))\n", "        elif example.difficulty == \"toy\":\n", "            toy_examples.append((i, example, modelA_output, modelB_output))\n", "\n", "    print(\"-\" * 20 + \"Toy examples\" + \"-\" * 20)\n", "    for i, example, modelA_output, modelB_output in toy_examples:\n", "        print(f\"--------\\nExample {i}:\")\n", "        print(f\"Difficulty: {example.difficulty}\")\n", "        print(f\"Gold path: {example.gold_path}\")\n", "        print(\n", "            f\"Gold path rank: {modelA_output.gold_path_rank} for {modelA_name} and {modelB_output.gold_path_rank} for {modelB_name}\"\n", "        )\n", "        print(\n", "            f\"Gold substring rank: {modelA_output.gold_substr_rank} for {modelA_name} and {modelB_output.gold_substr_rank} for {modelB_name}\"\n", "        )\n", "        print(\n", "            f\"Gold char idxs ranks: {modelA_output.gold_char_idxs_ranks} for {modelA_name} and {modelB_output.gold_char_idxs_ranks} for {modelB_name}\"\n", "        )\n", "\n", "    print(\"-\" * 20 + \"Easy examples\" + \"-\" * 20)\n", "    for i, example, modelA_output, modelB_output in easy_examples:\n", "        print(f\"--------\\nExample {i}:\")\n", "        print(f\"Difficulty: {example.difficulty}\")\n", "        print(f\"Gold path: {example.gold_path}\")\n", "        print(\n", "            f\"Gold path rank: {modelA_output.gold_path_rank} for {modelA_name} and {modelB_output.gold_path_rank} for {modelB_name}\"\n", "        )\n", "        print(\n", "            f\"Gold substring rank: {modelA_output.gold_substr_rank} for {modelA_name} and {modelB_output.gold_substr_rank} for {modelB_name}\"\n", "        )\n", "        print(\n", "            f\"Gold char idxs ranks: {modelA_output.gold_char_idxs_ranks} for {modelA_name} and {modelB_output.gold_char_idxs_ranks} for {modelB_name}\"\n", "        )\n", "\n", "    if debug:\n", "        print(\"\\n\" * 5)\n", "        for i, output in enumerate(modelA):\n", "            print(\"\\n\" * 3)\n", "            print(f\"--------\\nExample {i} for {modelA_name}:\\n---------\")\n", "            print(f\"Query prompt:\\n{output.query_prompt}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Raven vs Autofix versions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["examples = load_eval_examples()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RAVEN\n", "\n", "retrieval_database_yml = \"\"\"\n", "scorer:\n", "    name: dense_scorer_v2_fbwd\n", "    checkpoint_path: /mnt/efs/augment/checkpoints/next-edit-location/raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50\n", "    tokenizer_name: starcoder\n", "    cache_dir: /tmp/augment/cache\n", "chunker:\n", "    name: smart_line_level\n", "    max_chunk_chars: 2000\n", "query_formatter:\n", "    name: next_edit_location_query\n", "    tokenizer: starcoder\n", "    use_smart_header: true\n", "    deduplicate_identical_paths: true\n", "    truncate_instructions_tail: false\n", "    max_instruction_tokens: 4096\n", "    max_prompt_tokens: 8192\n", "document_formatter:\n", "    name: base:ethanol6-embedding-with-path-key\n", "    tokenizer: starcoder\n", "    max_tokens: 999\n", "\"\"\"\n", "\n", "raven_retrieval_db: DocumentIndex[NextEditPromptInput] = create_retriever(\n", "    yaml.safe_load(retrieval_database_yml)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raven_output = eval(raven_retrieval_db, examples)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Autofix\n", "\n", "# checkpoint_path = \"/mnt/efs/augment/checkpoints/autofix-location/v5/\"\n", "# checkpoint_path = \"/mnt/efs/augment/checkpoints/autofix-location/v7/\"\n", "\n", "autofix_paths = {\n", "    \"v7\": \"/mnt/efs/augment/checkpoints/autofix-location/v7/\",\n", "    # \"v8\": \"/mnt/efs/augment/checkpoints/autofix-location/v8/\",\n", "    # \"v9\": \"/mnt/efs/augment/checkpoints/autofix-location/v9/\",\n", "}\n", "\n", "\n", "def compute_autofix_examples(\n", "    examples: List[AutofixEvalExample], model_ckpt_paths: dict[str, str]\n", "):\n", "    retrieval_database_yml = \"\"\"\n", "    scorer:\n", "        name: dense_scorer_v2_fbwd\n", "        checkpoint_path: {checkpoint_path}\n", "        tokenizer_name: starcoder\n", "        cache_dir: /tmp/augment/cache\n", "    chunker:\n", "        name: smart_line_level\n", "        max_chunk_chars: 2000\n", "    query_formatter:\n", "        name: next_edit_location_query\n", "        tokenizer: starcoder\n", "        use_smart_header: true\n", "        deduplicate_identical_paths: true\n", "        truncate_instructions_tail: false\n", "        max_instruction_tokens: 4096\n", "        max_prompt_tokens: 8192\n", "    document_formatter:\n", "        name: base:ethanol6-embedding-with-path-key\n", "        tokenizer: starcoder\n", "        max_tokens: 999\n", "    \"\"\"\n", "\n", "    outputs = {}\n", "\n", "    for checkpoint_name, checkpoint_path in model_ckpt_paths.items():\n", "        fmtted_retrieval_database_yml = retrieval_database_yml.format(\n", "            checkpoint_path=checkpoint_path\n", "        )\n", "\n", "        retrieval_db: DocumentIndex[NextEditPromptInput] = create_retriever(\n", "            yaml.safe_load(fmtted_retrieval_database_yml)\n", "        )\n", "\n", "        outputs[checkpoint_name] = eval(retrieval_db, examples)\n", "\n", "    return outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outputs = compute_autofix_examples(examples, autofix_paths)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_viz(examples, raven_output, outputs[\"v7\"], \"RAVEN\", \"Autofix v7\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eval_viz(examples, outputs[\"v8\"], outputs[\"v7\"], \"Autofix v8\", \"Autofix v7\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyze docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "docs = []\n", "with open(\n", "    \"/mnt/efs/augment/user/colin/data/autofix/augment_autofix_eval/v1/ex5_repo.pkl\",\n", "    \"rb\",\n", ") as f:\n", "    docs = pickle.load(f)\n", "    print(len(docs))\n", "\n", "pathprefixes_to_filter = [\n", "    \"node_modules/\",\n", "    \"target/\",\n", "    \".git/\",\n", "    \".env/\",\n", "    \".github\",\n", "    \".pytest_cache/\",\n", "]\n", "pathsuffixes_to_filter = [\n", "    \".pyc\",\n", "    \".class\",\n", "    \".jar\",\n", "    \".log\",\n", "    \".out\",\n", "    \".o\",\n", "    \".so\",\n", "    \".bin\",\n", "    \".png\",\n", "    \".rmeta\",\n", "]\n", "\n", "filtered_docs = []\n", "for doc in docs:\n", "    should_filter = False\n", "    for pathprefix_to_filter in pathprefixes_to_filter:\n", "        if doc.path.startswith(pathprefix_to_filter):\n", "            should_filter = True\n", "            break\n", "    for pathsuffix_to_filter in pathsuffixes_to_filter:\n", "        if doc.path.endswith(pathsuffix_to_filter):\n", "            should_filter = True\n", "            break\n", "\n", "    if not should_filter:\n", "        filtered_docs.append(doc)\n", "\n", "print(\"Num filtered docs:\", len(filtered_docs))\n", "\n", "with open(\n", "    \"/mnt/efs/augment/user/colin/data/autofix/augment_autofix_eval/v1/ex5_repo_NEW.pkl\",\n", "    \"wb\",\n", ") as f:\n", "    pickle.dump(filtered_docs, f)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}