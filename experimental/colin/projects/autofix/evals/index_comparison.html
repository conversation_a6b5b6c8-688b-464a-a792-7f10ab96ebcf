<meta charset="UTF-8">
<head>
<link rel="stylesheet" href="/arun/next_edits/styles/index.css" />
<link rel="stylesheet" href="/arun/next_edits/styles/monokai.css" />
<script src="https://unpkg.com/htmx.org@1.9.12" integrity="sha384-ujb1lZYygJmzgSwoxRggbCHcjc0rB2XoQrxeTUQyRjrOnlCoYta87iKBWq3EsdM2" crossorigin="anonymous"></script>
<style>
    .container-col {
        display: flex;
        flex-direction: column;
        gap: 20px;  /* Space between stacked elements */
    }
    .container-row {
        display: flex;
        flex-direction: row;
        gap: 20px;  /* Space between stacked elements */
    }
</style>
</head>

<body>
<h2>Examples where {{systemA}} passes hard recall but {{systemB}} does not</h2>
<div class="container-col">
    <div class="container-row">
        <table class="summary" id="top">
            {% for output, _ in target_outputs %}
            {% set group_idx = output.group_id %}
            {% set repo_name = output.repo_name %}
            <tr>
                <td><button
                    hx-get="example-{{output.group_id}}.{{output.group_sequence_id}}.html"
                    hx-target="#example-view"
                    hx-push-url="?example={{output.group_id}}.{{output.group_sequence_id}}"
                    id="example-button-{{output.group_id}}-{{output.group_sequence_id}}"
                    />{{repo_name}}/{{group_idx}}.{{output.group_sequence_id}}</button></td>
                <td>{{"%0.4f" | format(output.metrics_by_k['32'].mean_hard_recall)}}</td>
            </tr>
            {% endfor %}
            <tr id="bottom"></tr>
        </table>
        <div id="example-view"></div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const exampleId = urlParams.get('example');
        if (exampleId) {
            const [groupId, sequenceId] = exampleId.split('.');
            const buttonId = `example-button-${groupId}-${sequenceId}`;
            const button = document.getElementById(buttonId);
            if (button) {
                button.click();
            }
        }
    });
</script>
</body>
