{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "import pickle\n", "import subprocess\n", "from pathlib import Path\n", "import json\n", "\n", "on_gcp = False\n", "if on_gcp:\n", "    sys.path.append('/home/<USER>/augment')\n", "    ROOT_DIR = Path(\"/home/<USER>\")\n", "else:\n", "    ROOT_DIR = Path(\"/home/<USER>\")\n", "\n", "from research.retrieval.utils import convert_repository_to_documents\n", "from base.diff_utils.changes import Added, Changed, Deleted, Modified\n", "from base.diff_utils.diff_utils import File\n", "from research.retrieval.utils import convert_repository_to_documents\n", "from base.diff_utils.diff_formatter import format_file_changes_with_ranges"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "\n", "def load_and_save_repo(repo_path: Path, repo_output_path: Path, problem_index: str):\n", "    documents = list(convert_repository_to_documents(str(repo_path)))\n", "\n", "    pathprefixes_to_filter = ['node_modules/', 'target/', '.git/', '.env/', '.github', '.pytest_cache/']\n", "    pathsuffixes_to_filter = ['.pyc', '.class', '.jar', '.log', '.out', '.o', '.so', '.bin', '.png', '.rmeta']\n", "\n", "    filtered_docs = []\n", "    for doc in documents:\n", "        should_filter = False\n", "        for pathprefix_to_filter in pathprefixes_to_filter:\n", "            if doc.path.startswith(pathprefix_to_filter):\n", "                should_filter = True\n", "                break\n", "        for pathsuffix_to_filter in pathsuffixes_to_filter:\n", "            if doc.path.endswith(pathsuffix_to_filter):\n", "                should_filter = True\n", "                break\n", "        \n", "        if not should_filter:\n", "            filtered_docs.append(doc)\n", "\n", "    if repo_output_path.exists():\n", "        raise ValueError(f\"Output path {repo_output_path} already exists. Try {int(problem_index) + 1}.\")\n", "    with open(repo_output_path, \"wb\") as f:\n", "        pickle.dump(filtered_docs, f)\n", "    print(f\"Saved {len(filtered_docs)} documents to {repo_output_path}.\")\n", "\n", "\n", "def get_changed_files(repo_path: Path) -> list[Changed[File]]:\n", "    # Function to run git commands\n", "    def run_git_command(command):\n", "        return subprocess.check_output(command, cwd=str(repo_path), text=True)\n", "\n", "    status_output = run_git_command([\"git\", \"status\", \"--porcelain\"])\n", "    status_lines = status_output.split(\"\\n\")\n", "\n", "    added_files = []\n", "    deleted_files = []\n", "    modified_files = []\n", "\n", "    for line in status_lines:\n", "        if line:\n", "            status, file_path = line[:2], line[3:]\n", "            if status in [\"A \", \" A\", \"??\"]:\n", "                added_files.append(file_path)\n", "            elif status == \"D \":\n", "                deleted_files.append(file_path)\n", "            elif status in [\"M \", \" M\", \"MM\"]:\n", "                modified_files.append(file_path)\n", "            else:\n", "                raise ValueError(f\"Unknown status: {status} for {file_path}\")\n", "\n", "    # Load all current files\n", "    current_documents = list(convert_repository_to_documents(str(repo_path)))\n", "    fp_to_doc = {doc.path: doc for doc in current_documents}\n", "\n", "    changes = []\n", "    for fp in added_files:\n", "        changes.append(Added(after=File(path=fp, contents=fp_to_doc[fp].text)))\n", "    for fp in deleted_files:\n", "        content = run_git_command([\"git\", \"show\", f\"HEAD:{fp}\"])\n", "        changes.append(Deleted(before=File(path=fp, contents=content)))\n", "    for fp in modified_files:\n", "        content = run_git_command([\"git\", \"show\", f\"HEAD:{fp}\"])\n", "        changes.append(\n", "            Modified(\n", "                before=File(path=fp, contents=content),\n", "                after=File(path=fp, contents=fp_to_doc[fp].text),\n", "            )\n", "        )\n", "\n", "    return changes\n", "\n", "\n", "def save_breaking_diff_viz(changes: list[Changed[File]], breaking_diff_viz_path: Path, problem_index: str):\n", "    if breaking_diff_viz_path.exists():\n", "        raise ValueError(f\"Output path {breaking_diff_viz_path} already exists. Try {int(problem_index) + 1}.\")\n", "\n", "    changes_str = \"\"\n", "    for diff in format_file_changes_with_ranges(\n", "        changes,\n", "        diff_context_lines=3,\n", "        use_smart_header=False,\n", "    ):\n", "        changes_str += diff.text_with_header()\n", "\n", "    with open(breaking_diff_viz_path, \"w\") as f:\n", "        f.write(changes_str)\n", "\n", "    print(f\"Saved breaking diff to {breaking_diff_viz_path}.\")\n", "\n", "def save_breaking_diff(changes: list[Changed[File]], input_diff_path: Path, problem_index: str):\n", "    if input_diff_path.exists():\n", "        raise ValueError(f\"Output path {input_diff_path} already exists. Try {int(problem_index) + 1}.\")\n", "\n", "    with open(input_diff_path, \"wb\") as f:\n", "        pickle.dump(changes, f)\n", "\n", "    print(f\"Saved input diff to {input_diff_path}.\")\n", "\n", "def save_example(problem_index: str):\n", "     # Specify the path to the repository\n", "    repo_path = Path(f\"{ROOT_DIR}/augment2\")\n", "    mnt_output_path_base = \"/mnt/efs/augment/user/colin/data/autofix/augment_autofix_eval/v1\"\n", "    repo_output_path = Path(f\"{mnt_output_path_base}/ex{problem_index}_repo.pkl\")\n", "    input_diff_path = Path(f\"{mnt_output_path_base}/ex{problem_index}_input_changes.pkl\")\n", "    git_output_base = f\"{ROOT_DIR}/augment/experimental/colin/projects/autofix/evals/augment_autofix_v1/ex{problem_index}\"\n", "    breaking_diff_viz_path = Path(f\"{git_output_base}/ex{problem_index}_breaking_diff_viz.txt\")\n", "    logs_path = Path(f\"{git_output_base}/logs.txt\")\n", "    gold_info_path = Path(f\"{git_output_base}/gold_info.json\")\n", "\n", "    Path(git_output_base).mkdir(parents=True, exist_ok=False)\n", "\n", "    if repo_output_path.exists():\n", "        raise ValueError(f\"Output path {repo_output_path} already exists. Try {int(problem_index) + 1}.\")\n", "    if input_diff_path.exists():\n", "        raise ValueError(f\"Output path {input_diff_path} already exists. Try {int(problem_index) + 1}.\")\n", "    if breaking_diff_viz_path.exists():\n", "        raise ValueError(f\"Output path {breaking_diff_viz_path} already exists. Try {int(problem_index) + 1}.\")\n", "    if logs_path.exists():\n", "        raise ValueError(f\"Output path {logs_path} already exists. Try {int(problem_index) + 1}.\")\n", "    if gold_info_path.exists():\n", "        raise ValueError(f\"Output path {gold_info_path} already exists. Try {int(problem_index) + 1}.\")\n", "\n", "    load_and_save_repo(repo_path, repo_output_path, problem_index)\n", "    changes = get_changed_files(repo_path)\n", "    save_breaking_diff_viz(changes, breaking_diff_viz_path, problem_index)\n", "    save_breaking_diff(changes, input_diff_path, problem_index)\n", "\n", "    with open(logs_path, \"w\") as f:\n", "        pass\n", "\n", "    with open(gold_info_path, \"w\") as f:\n", "        json.dump(\n", "            {\n", "                \"gold_path\": None,\n", "                \"gold_substr\": None,\n", "                \"gold_char_idxs\": [],\n", "                \"difficulty\": None,\n", "                \"repo_path\": str(repo_output_path),\n", "                \"diff_path\": str(input_diff_path),\n", "                \"relevant_commit\": None,\n", "            },\n", "            f,\n", "            indent=2,\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Steps to save example:\n", "- Step 1: `git checkout <commit_hash>` the commit that you want to save.\n", "- Step 2: Unfold the commit into a set of working tree changes with `git reset HEAD~1`\n", "- Step 3: Make some change to the working changeset to add a bug.\n", "- Step 4: Save the results by running `save_example` below. Make sure to increment the version number first."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["problem_index = \"10\"\n", "save_example(problem_index)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Confirm size of output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "docs = []\n", "with open('/mnt/efs/augment/user/colin/data/autofix/augment_autofix_eval/v1/ex6_repo.pkl', \"rb\") as f:\n", "    docs = pickle.load(f)\n", "    print(len(docs))\n", "\n", "pathprefixes_to_filter = ['node_modules/', 'target/', '.git/', '.env/', '.github', '.pytest_cache/']\n", "pathsuffixes_to_filter = ['.pyc', '.class', '.jar', '.log', '.out', '.o', '.so', '.bin', '.png', '.rmeta']\n", "\n", "filtered_docs = []\n", "for doc in docs:\n", "    should_filter = False\n", "    for pathprefix_to_filter in pathprefixes_to_filter:\n", "        if doc.path.startswith(pathprefix_to_filter):\n", "            should_filter = True\n", "            break\n", "    for pathsuffix_to_filter in pathsuffixes_to_filter:\n", "        if doc.path.endswith(pathsuffix_to_filter):\n", "            should_filter = True\n", "            break\n", "    \n", "    if not should_filter:\n", "        filtered_docs.append(doc)\n", "\n", "\n", "unique_paths = set()\n", "for doc in filtered_docs:\n", "    unique_paths.add(doc.path.split('/')[0])\n", "unique_paths = list(unique_paths)\n", "random.shuffle(unique_paths)\n", "\n", "print(\"Num filtered docs:\", len(filtered_docs))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}