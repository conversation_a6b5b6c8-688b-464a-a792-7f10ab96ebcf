"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from datetime import date

from experimental.colin.projects.autofix.evals.nel_eval_lib import (
    DATASETS,
    MODELS,
    run_sweep,
)

if __name__ == "__main__":
    datasets = [
        "autofix-commits-v14-fixed4",
    ]
    models = [
        (model_name, MODELS[model_name])
        for model_name in [
            #"autofix-v14",
            "autofix-v13",
        ]
    ]

    run_sweep(
        {
            f"{dataset_name.split('-')[-1]}-{model_name}-{date.today()}": [
                {"model_name": model_name},
                DATASETS[dataset_name],
                model_config,
            ]
            for dataset_name in datasets
            for model_name, model_config in models
        }
    )
