<meta charset="UTF-8">
<head>
<link rel="stylesheet" href="/arun/next_edits/styles/index.css" />
<link rel="stylesheet" href="/arun/next_edits/styles/monokai.css" />
<script src="https://unpkg.com/htmx.org@1.9.12" integrity="sha384-ujb1lZYygJmzgSwoxRggbCHcjc0rB2XoQrxeTUQyRjrOnlCoYta87iKBWq3EsdM2" crossorigin="anonymous"></script>
<style>
    .container-col {
        display: flex;
        flex-direction: column;
        gap: 20px;  /* Space between stacked elements */
    }
    .container-row {
        display: flex;
        flex-direction: row;
        gap: 20px;  /* Space between stacked elements */
    }
</style>
</head>

<body>
<h3>{{system}}</h3>
<div class="container-col">
    <h2>Examples</h2>
    <div class="container-row">
        <table class="summary" id="top">
            {% for output in best_outputs %}
            {% set group_idx = output.group_id %}
            {% set repo_name = output.repo_name %}
            <tr>
                <td><button
                    hx-get="example-{{output.group_id}}.{{output.group_sequence_id}}.html"
                    hx-target="#example-view"
                    hx-push-url="?example=good.{{output.group_id}}.{{output.group_sequence_id}}"
                    id="good-example-button-{{output.group_id}}-{{output.group_sequence_id}}"
                    />{{repo_name}}/{{group_idx}}.{{output.group_sequence_id}}</button></td>
                <td>{{"%0.4f" | format(output.metrics.mean_recall)}}</td>
            </tr>
            {% endfor %}
            <tr id="bottom"></tr>
        </table>
        <div id="example-view"></div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const exampleId = urlParams.get('example');
        if (exampleId) {
            const [success_status, groupId, sequenceId] = exampleId.split('.');
            const buttonId = `${success_status}-example-button-${groupId}-${sequenceId}`;
            const button = document.getElementById(buttonId);
            if (button) {
                button.click();
            }
        }
    });
</script>
</body>
