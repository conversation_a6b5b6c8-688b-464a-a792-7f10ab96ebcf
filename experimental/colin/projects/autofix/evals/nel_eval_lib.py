"""Helper script to run all the evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

import copy
import os
import subprocess
import tempfile
from datetime import date
from pathlib import Path

import yaml

from research.core.constants import AUGMENT_ROOT
from research.fastbackward.utils import combine_dict, unflatten_dict

CHECKPOINT_ROOT = Path("/mnt/efs/augment/checkpoints")
RAVEN_ROOT = CHECKPOINT_ROOT / "next-edit-location"
AUTOFIX_ROOT = CHECKPOINT_ROOT / "autofix-location"
RERANKER_ROOT = CHECKPOINT_ROOT / "autofix-reranker"

always_localize_working_diff = False
num_diff_tokens = 2048
num_instruction_tokens = 6144
max_prompt_tokens = num_diff_tokens + num_instruction_tokens
num_min_repo_chunks = 0

raven_template: dict = yaml.safe_load(f"""
system:
    name: next_edit_location
    filter_input_ranges: False
    retriever:
        scorer:
            name: dense_scorer_v2_fbwd
            checkpoint_path: FILL-ME
            tokenizer_name: starcoder
            cache_dir: /tmp/augment/cache
        chunker:
            name: smart_line_level
            max_chunk_chars: 2000
        query_formatter:
            name: next_edit_location_query
            tokenizer: starcoder
            max_prompt_tokens: {max_prompt_tokens}
            max_diff_tokens: {num_diff_tokens}
            max_instruction_tokens: {num_instruction_tokens}
            use_smart_header: True
            deduplicate_identical_paths: True
            truncate_instructions_tail: False                              
        document_formatter:
            name: base:ethanol6-embedding-with-path-key
            tokenizer: starcoder
            max_tokens: 999

task:
    name: next_edit_location
    skip_examples_with_less_than_N_repo_chunks: {num_min_repo_chunks}
    always_localize_working_diff: {always_localize_working_diff}

podspec: 1xH100-gcp.yaml
determined:
    name: autofix-eval
    workspace: Dev
    project: autofix
    metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
""")

autofix_template: dict = yaml.safe_load(f"""
system:
    name: next_edit_location
    filter_input_ranges: False
    retriever:
        scorer:
            name: dense_scorer_v2_fbwd
            checkpoint_path: FILL-ME
            tokenizer_name: starcoder
            cache_dir: /tmp/augment/cache
        chunker:
            name: smart_line_level
            max_chunk_chars: 2000
        query_formatter:
            name: next_edit_location_query
            tokenizer: starcoder
            max_prompt_tokens: {max_prompt_tokens}
            max_diff_tokens: {num_diff_tokens}                        
            max_instruction_tokens: {num_instruction_tokens}
            use_smart_header: True
            deduplicate_identical_paths: True
            truncate_instructions_tail: False                              
        document_formatter:
            name: base:ethanol6-embedding-with-path-key
            tokenizer: starcoder
            max_tokens: 999

task:
    name: next_edit_location
    skip_examples_with_less_than_N_repo_chunks: {num_min_repo_chunks}
    always_localize_working_diff: {always_localize_working_diff}

podspec: 1xH100-gcp.yaml
determined:
    name: autofix-eval
    workspace: Dev
    project: autofix
    metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
""")

autofix_template_with_reranker: dict = yaml.safe_load(f"""
system:
    name: next_edit_reranker
    record_all_locations: True
    reranker:
        model:
            name: starcoder2_fastforward
            model_path: FILL-ME
            checkpoint_sha256: FILL-ME
        generation_options:
            max_generated_tokens: 1
        prompt_formatter:
            use_diff_based_output: True
            diff_context_lines: 12
            max_prompt_tokens: 14000
            output_instruction: False
            section_budgets:
                suffix_tks: 1000
                prefix_tks: 2200
                diff_tks: 4500
                filename_tks: 100
                instruction_tks: 6000
                retrieval_tks: 0                                      
    localizer:
        filter_input_ranges: False
        retriever:
            scorer:
                name: dense_scorer_v2_fbwd
                checkpoint_path: FILL-ME
                tokenizer_name: starcoder
                cache_dir: /tmp/augment/cache
            chunker:
                name: smart_line_level
                max_chunk_chars: 2000
            query_formatter:
                name: next_edit_location_query
                tokenizer: starcoder
                max_prompt_tokens: {max_prompt_tokens}
                max_diff_tokens: {num_diff_tokens}                        
                max_instruction_tokens: {num_instruction_tokens}
                use_smart_header: True
                deduplicate_identical_paths: True
                truncate_instructions_tail: False                              
            document_formatter:
                name: base:ethanol6-embedding-with-path-key
                tokenizer: starcoder
                max_tokens: 999

task:
    name: next_edit_location
    skip_examples_with_less_than_N_repo_chunks: {num_min_repo_chunks}
    always_localize_working_diff: {always_localize_working_diff}
    top_ks: [3, 8, 32, 64, 128]

podspec: 1xH100-gcp.yaml
determined:
    name: autofix-eval
    workspace: Dev
    project: autofix
    metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
""")

def run_eval(name: str, config: dict, local: bool = False):
    """Run the evaluation."""

    name = name.replace(" ", "-")
    config["determined"]["name"] = "AutoFixEvaluation " + name

    with tempfile.NamedTemporaryFile(mode="w", delete=False) as f:
        print(f"Running {name}: ")
        print("Saving config to file: ", f.name)
        print(yaml.dump(config))

        yaml.dump(config, f)
        f.flush()
        f.close()

        proc = subprocess.run(
            "python research/eval/eval.py "
            f"{'--local' if local else ''} "
            f"--job_root /mnt/efs/augment/eval/autofix/{name} "
            f"{f.name}",
            shell=True,
            check=False,
            capture_output=True,
            cwd=AUGMENT_ROOT,
        )
        output = proc.stdout.decode("utf-8").strip()
        print("THe output: ", output)
        url = output.splitlines()[-1].strip()
        assert url.startswith("https://"), url
        # Delete the temp file.
        os.unlink(f.name)

    return url


def get_config(base_config: dict, *deltas: dict) -> dict:
    """Get the config."""
    ret = copy.deepcopy(base_config)
    for config_delta in deltas:
        config_delta = unflatten_dict(config_delta)
        ret = combine_dict(ret, config_delta)
    return ret


MODELS = {
        "raven": {
            "system.retriever.scorer.checkpoint_path": str(
                RAVEN_ROOT / "raven1b.query.8targets.rel.S1.3,R1.2_v13-128.smart2000,Sc1.0_path_distance,T1.1_5-15lines.downsample10.instructions50"
            )
        },
        "autofix-v10-1epoch": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v10-1epoch"
            )
        },
        "autofix-v10-2epoch": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v10-2epoch"
            )
        },
        "autofix-v11": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v11"
            )
        },
        "autofix-v11_no_instructions": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v11_no_instructions"
            )
        },
        "autofix-v12": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v12"
            )
        },
        "autofix-v12_no_instructions": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v12_no_instructions"
            )
        },
        "autofix-v13": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v13"
            )
        },
        "autofix-v13-halfdata": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v13_halfdata"
            )
        },
        "autofix-v13-no-instructions": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v13_no_instructions"
            )
        },
        "autofix-v14": {
            "system.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v14"
            )
        },


        # With rerankers

        "autofix-v12_with_v2_15b_reranker": {
            "system.localizer.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v12"
            ),
            "system.reranker.model.model_path": str(
                RERANKER_ROOT / "v2" / "ffw"
            ),
            "system.reranker.model.checkpoint_sha256": "35a96baaa1650f613a2fc827b155f5767c87e8126513e72d5cb04fcb2bc5d11f"

        },
        "autofix-v12_with_v2_3b_reranker": {
            "system.localizer.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v12"
            ),
            "system.reranker.model.model_path": str(
                RERANKER_ROOT / "v2-3b" / "ffw"
            ),
            "system.reranker.model.checkpoint_sha256": "fe8c6dba0ab0b18682d327d85864508665c644f15f4e79cc477bf11c96363e10"

        },
        "autofix-v12_with_v2_3b_reranker-ne": { # finetuned from next edit base model
            "system.localizer.retriever.scorer.checkpoint_path": str(
                AUTOFIX_ROOT / "v12"
            ),
            "system.reranker.model.model_path": str(
                RERANKER_ROOT / "v2-nexteditbasemodel" / "ffw"
            ),
            "system.reranker.model.checkpoint_sha256": "475d98160ae25ddbd9f4c2882b3db786379c38207703fc06398d70e65f88340b",
        },
}

MODEL_TO_TEMPLATE = {
    "raven": raven_template,
    "autofix-v10-1epoch": autofix_template,
    "autofix-v10-2epoch": autofix_template,
    "autofix-v11": autofix_template,
    "autofix-v11_no_instructions": autofix_template,
    "autofix-v12": autofix_template,
    "autofix-v12_no_instructions": autofix_template,
    "autofix-v13": autofix_template,
    "autofix-v13-halfdata": autofix_template,
    "autofix-v13-no-instructions": autofix_template,
    "autofix-v14": autofix_template,

    # With rerankers
    "autofix-v12_with_v2_reranker": autofix_template_with_reranker,
    "autofix-v12_with_v2_3b_reranker": autofix_template_with_reranker,
    "autofix-v12_with_v2_3b_reranker-ne": autofix_template_with_reranker,
}

DATASETS = {
    "autofix-commits-v10": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v10.jsonl.zst"
    },
    "autofix-commits-v11": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v11.jsonl.zst"
    },
    "autofix-commits-v12": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v12.jsonl.zst"
    },
    "autofix-commits-v12_filtered": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v12_filtered.jsonl.zst"
    },
    "autofix-commits-v13": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v13-fixed_filtered.jsonl.zst"
    },
    "autofix-commits-v14-fixed": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v14_fixed.jsonl.zst"
    },
    "autofix-commits-v14-fixed4": {
        "task.dataset_path": "/mnt/efs/augment/data/eval/autofix/commits-v14_fixed4.jsonl.zst"
    },
    
    # EXAMPLES FROM NEXT-EDIT
    #"prs.v7": {
    #    "task.dataset_path": "/mnt/efs/augment/data/eval/next_edits/prs.v7.jsonl.zst"
    #},
    #"manual.v1": {
    #    "task.dataset_path": "/mnt/efs/augment/data/eval/next_edits/manual.v2.jsonl.zst"
    #},
}


def run_sweep(configs: dict[str, list[dict]]):
    urls = {}
    for name, config in configs.items():
        name = name + f"-W-{always_localize_working_diff}"
        name = name + f"-D-{num_diff_tokens}-I-{num_instruction_tokens}"
        name = name + f"-M-{num_min_repo_chunks}"
        model_name = config[0]["model_name"]
        config = config[1:]
        template = MODEL_TO_TEMPLATE[model_name]
        config = get_config(template, *config)
        url = run_eval(name, config)
        urls[name] = url

    print("Name URL")
    for name, url in urls.items():
        print(f"{name} {url}")


if __name__ == "__main__":
    # This is an example.
    datasets = ["manual.v2"]
    models = ["raven_v16_query"]
    run_sweep(
        {
            f"{dataset_name}-{model_name}-{date.today()}": [
                {"model_name": model_name},
                DATASETS[dataset_name],
                MODELS[model_name],
            ]
            for dataset_name in datasets
            for model_name in models
        }
    )
