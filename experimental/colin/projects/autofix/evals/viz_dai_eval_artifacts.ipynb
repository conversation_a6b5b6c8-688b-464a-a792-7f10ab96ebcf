{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from research.eval.harness.utils import read_jsonl_zst\n", "\n", "def get_path(name: str) -> str:\n", "    return f\"/mnt/efs/augment/eval/autofix/{name}/000_results.jsonl\"\n", "\n", "def viz_results(name: str, verbose=False):\n", "    res = get_path(name)\n", "    with open(res, \"r\") as f:\n", "        data = json.load(f)\n", "\n", "    print(f\"For path: {res}\")\n", "\n", "    output_data = read_jsonl_zst(f\"/mnt/efs/augment/eval/autofix/{name}/000_next_edit_location_NextEditLocationEvalTask_output.jsonl.zst\")\n", "    print(f\"Num examples evaluated: {len(output_data)}\")\n", "\n", "    print(\"MACRO METRICS (average per repo then average those averages)\")\n", "    for k, v in data['macro_metrics'].items():\n", "        print(f\"[k={k}]\", end=\"\")\n", "        for metric_name, metric_value in v.items():\n", "            if not verbose and metric_name not in [\"mean_recall\", \"mean_hard_recall\"]:\n", "                continue\n", "            print(f\"{metric_name}={metric_value:.2f}, \", end=\"\")\n", "        print(\" ||| \", end=\"\")\n", "\n", "    print('\\nMICRO METRICS')\n", "    for k, v in data['micro_metrics'].items():\n", "        print(f\"[k={k}]\", end=\"\")\n", "        for metric_name, metric_value in v.items():\n", "            if not verbose and metric_name not in [\"mean_recall\", \"mean_hard_recall\"]:\n", "                continue\n", "            print(f\"{metric_name}={metric_value:.2f}, \", end=\"\")\n", "        print(\" ||| \", end=\"\")\n", "    print('\\n')\n", "\n", "    #print('Per group metrics:')\n", "    #for k, v in data['per_group_metrics'].items():\n", "    #   print(k, v)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# autofix-commits-v10-raven-sc-2024-10-22-NO-BREAKING-DIFF-ADDED \n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86773\n", "\n", "viz_results(\"autofix-commits-v10-raven-sc-2024-10-22-NO-BREAKING-DIFF-ADDED\")\n", "\n", "# autofix-commits-v10-autofix-v10-1epoch-sc-2024-10-22-NO-BREAKING-DIFF-ADDED \n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86774\n", "\n", "viz_results(\"autofix-commits-v10-autofix-v10-1epoch-sc-2024-10-22-NO-BREAKING-DIFF-ADDED\")\n", "\n", "# autofix-commits-v10-autofix-v10-2epoch-sc-2024-10-22-NO-BREAKING-DIFF-ADDED \n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86775\n", "\n", "viz_results(\"autofix-commits-v10-autofix-v10-2epoch-sc-2024-10-22-NO-BREAKING-DIFF-ADDED\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# autofix-commits-v10-raven-sc-2024-10-22_WITH_BREAKING_DIFF\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86779/logs\n", "\n", "viz_results(\"autofix-commits-v10-raven-sc-2024-10-22_WITH_BREAKING_DIFF\")\n", "\n", "\n", "# autofix-commits-v10-autofix-v10-2epoch-sc-2024-10-22_WITH_BREAKING_DIFF\n", "# https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86781/logs?searchText=\n", "\n", "viz_results(\"autofix-commits-v10-autofix-v10-2epoch-sc-2024-10-22_WITH_BREAKING_DIFF\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths = [\n", "    \"raven-2024-10-23-W-False-D-4096-I-4096\",\n", "    \"autofix-v10-2epoch-2024-10-23-W-False-D-4096-I-4096\",\n", "    \"raven-2024-10-23-W-False-D-4096-I-10\",\n", "    \"autofix-v10-2epoch-2024-10-23-W-False-D-4096-I-10\"\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths = [\n", "    \"v11-raven-2024-10-23-W-False-D-4096-I-4096\",\n", "    \"v11-autofix-v11-2024-10-23-W-False-D-4096-I-4096\",\n", "    \"v11-autofix-v11_no_instructions-2024-10-24-W-False-D-4096-I-0\"\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths = [\n", "    \"v12-raven-2024-10-25-W-False-D-4096-I-0\",\n", "    \"v12-raven-2024-10-25-W-False-D-4096-I-4096\",\n", "    \"v12-autofix-v12_no_instructions-2024-10-25-W-False-D-4096-I-0\",\n", "    \"v12-autofix-v12-2024-10-25-W-False-D-4096-I-4096\"\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["paths = [\n", "    \"v12_filtered-raven-2024-10-30-W-False-D-2048-I-0\",\n", "    \"v12_filtered-raven-2024-10-30-W-False-D-2048-I-6144\",\n", "    \"v12_filtered-autofix-v12_no_instructions-2024-10-30-W-False-D-2048-I-0\",\n", "    \"v12_filtered-autofix-v12-2024-10-30-W-False-D-2048-I-6144\"\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-0/000_results.jsonl\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.23, mean_hard_recall=0.15,  ||| [k=8]mean_recall=0.33, mean_hard_recall=0.24,  ||| [k=32]mean_recall=0.45, mean_hard_recall=0.36,  ||| [k=64]mean_recall=0.51, mean_hard_recall=0.42,  ||| [k=128]mean_recall=0.56, mean_hard_recall=0.48,  ||| [k=256]mean_recall=0.60, mean_hard_recall=0.54,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.32, mean_hard_recall=0.22,  ||| [k=8]mean_recall=0.44, mean_hard_recall=0.33,  ||| [k=32]mean_recall=0.59, mean_hard_recall=0.47,  ||| [k=64]mean_recall=0.66, mean_hard_recall=0.55,  ||| [k=128]mean_recall=0.72, mean_hard_recall=0.62,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.69,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-6144/000_results.jsonl\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.23, mean_hard_recall=0.15,  ||| [k=8]mean_recall=0.33, mean_hard_recall=0.23,  ||| [k=32]mean_recall=0.46, mean_hard_recall=0.36,  ||| [k=64]mean_recall=0.52, mean_hard_recall=0.44,  ||| [k=128]mean_recall=0.57, mean_hard_recall=0.49,  ||| [k=256]mean_recall=0.62, mean_hard_recall=0.55,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.31, mean_hard_recall=0.22,  ||| [k=8]mean_recall=0.43, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.59, mean_hard_recall=0.47,  ||| [k=64]mean_recall=0.67, mean_hard_recall=0.56,  ||| [k=128]mean_recall=0.73, mean_hard_recall=0.63,  ||| [k=256]mean_recall=0.80, mean_hard_recall=0.70,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12_no_instructions-2024-10-31-TEST-W-False-D-2048-I-0/000_results.jsonl\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.27, mean_hard_recall=0.20,  ||| [k=8]mean_recall=0.35, mean_hard_recall=0.26,  ||| [k=32]mean_recall=0.46, mean_hard_recall=0.37,  ||| [k=64]mean_recall=0.51, mean_hard_recall=0.43,  ||| [k=128]mean_recall=0.55, mean_hard_recall=0.48,  ||| [k=256]mean_recall=0.59, mean_hard_recall=0.52,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.38, mean_hard_recall=0.28,  ||| [k=8]mean_recall=0.48, mean_hard_recall=0.36,  ||| [k=32]mean_recall=0.61, mean_hard_recall=0.50,  ||| [k=64]mean_recall=0.66, mean_hard_recall=0.55,  ||| [k=128]mean_recall=0.71, mean_hard_recall=0.61,  ||| [k=256]mean_recall=0.76, mean_hard_recall=0.66,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12-2024-10-31-TEST-W-False-D-2048-I-6144/000_results.jsonl\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.54, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.58, mean_hard_recall=0.49,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.53,  ||| [k=256]mean_recall=0.64, mean_hard_recall=0.57,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.46, mean_hard_recall=0.34,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.43,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.57,  ||| [k=64]mean_recall=0.74, mean_hard_recall=0.62,  ||| [k=128]mean_recall=0.77, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.80, mean_hard_recall=0.71,  ||| \n", "\n"]}], "source": ["paths = [\n", "    \"v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-0\",\n", "    \"v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-6144\",\n", "    \"v12_filtered-autofix-v12_no_instructions-2024-10-31-TEST-W-False-D-2048-I-0\",\n", "    \"v12_filtered-autofix-v12-2024-10-31-TEST-W-False-D-2048-I-6144\"\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12-2024-11-07-W-False-D-2048-I-6144/000_results.jsonl\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.54, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.58, mean_hard_recall=0.49,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.53,  ||| [k=256]mean_recall=0.64, mean_hard_recall=0.57,  ||| [k=512]mean_recall=0.67, mean_hard_recall=0.63,  ||| [k=1024]mean_recall=0.71, mean_hard_recall=0.68,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.46, mean_hard_recall=0.34,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.43,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.57,  ||| [k=64]mean_recall=0.74, mean_hard_recall=0.62,  ||| [k=128]mean_recall=0.77, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.80, mean_hard_recall=0.71,  ||| [k=512]mean_recall=0.85, mean_hard_recall=0.78,  ||| [k=1024]mean_recall=0.89, mean_hard_recall=0.84,  ||| \n", "\n"]}], "source": ["# RERUN V12 BUT ALSO COMPUTE K=512 and K=1024, no filtering on repo size\n", "paths = [\n", "    \"v12_filtered-autofix-v12-2024-11-07-W-False-D-2048-I-6144\",\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/v12_filtered-raven-2024-11-07-W-False-D-2048-I-0-M-2500/000_results.jsonl\n", "Num examples evaluated: 398\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.15, mean_hard_recall=0.09,  ||| [k=8]mean_recall=0.24, mean_hard_recall=0.17,  ||| [k=32]mean_recall=0.37, mean_hard_recall=0.27,  ||| [k=64]mean_recall=0.43, mean_hard_recall=0.34,  ||| [k=128]mean_recall=0.46, mean_hard_recall=0.39,  ||| [k=256]mean_recall=0.49, mean_hard_recall=0.41,  ||| [k=512]mean_recall=0.53, mean_hard_recall=0.44,  ||| [k=1024]mean_recall=0.58, mean_hard_recall=0.51,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.22, mean_hard_recall=0.14,  ||| [k=8]mean_recall=0.33, mean_hard_recall=0.22,  ||| [k=32]mean_recall=0.48, mean_hard_recall=0.35,  ||| [k=64]mean_recall=0.56, mean_hard_recall=0.44,  ||| [k=128]mean_recall=0.60, mean_hard_recall=0.49,  ||| [k=256]mean_recall=0.65, mean_hard_recall=0.52,  ||| [k=512]mean_recall=0.70, mean_hard_recall=0.56,  ||| [k=1024]mean_recall=0.76, mean_hard_recall=0.65,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-raven-2024-11-07-W-False-D-2048-I-6144-M-2500/000_results.jsonl\n", "Num examples evaluated: 398\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.14, mean_hard_recall=0.09,  ||| [k=8]mean_recall=0.23, mean_hard_recall=0.16,  ||| [k=32]mean_recall=0.33, mean_hard_recall=0.26,  ||| [k=64]mean_recall=0.39, mean_hard_recall=0.31,  ||| [k=128]mean_recall=0.44, mean_hard_recall=0.35,  ||| [k=256]mean_recall=0.49, mean_hard_recall=0.40,  ||| [k=512]mean_recall=0.55, mean_hard_recall=0.45,  ||| [k=1024]mean_recall=0.61, mean_hard_recall=0.51,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.20, mean_hard_recall=0.12,  ||| [k=8]mean_recall=0.31, mean_hard_recall=0.20,  ||| [k=32]mean_recall=0.43, mean_hard_recall=0.32,  ||| [k=64]mean_recall=0.52, mean_hard_recall=0.40,  ||| [k=128]mean_recall=0.58, mean_hard_recall=0.45,  ||| [k=256]mean_recall=0.65, mean_hard_recall=0.51,  ||| [k=512]mean_recall=0.72, mean_hard_recall=0.57,  ||| [k=1024]mean_recall=0.79, mean_hard_recall=0.66,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12_no_instructions-2024-11-07-W-False-D-2048-I-0-M-2500/000_results.jsonl\n", "Num examples evaluated: 398\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.20, mean_hard_recall=0.13,  ||| [k=8]mean_recall=0.29, mean_hard_recall=0.20,  ||| [k=32]mean_recall=0.39, mean_hard_recall=0.31,  ||| [k=64]mean_recall=0.43, mean_hard_recall=0.35,  ||| [k=128]mean_recall=0.46, mean_hard_recall=0.38,  ||| [k=256]mean_recall=0.47, mean_hard_recall=0.39,  ||| [k=512]mean_recall=0.51, mean_hard_recall=0.42,  ||| [k=1024]mean_recall=0.56, mean_hard_recall=0.48,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.28, mean_hard_recall=0.18,  ||| [k=8]mean_recall=0.40, mean_hard_recall=0.27,  ||| [k=32]mean_recall=0.52, mean_hard_recall=0.40,  ||| [k=64]mean_recall=0.57, mean_hard_recall=0.44,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.49,  ||| [k=256]mean_recall=0.63, mean_hard_recall=0.50,  ||| [k=512]mean_recall=0.67, mean_hard_recall=0.55,  ||| [k=1024]mean_recall=0.73, mean_hard_recall=0.61,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12-2024-11-07-W-False-D-2048-I-6144-M-2500/000_results.jsonl\n", "Num examples evaluated: 398\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.28, mean_hard_recall=0.18,  ||| [k=8]mean_recall=0.35, mean_hard_recall=0.24,  ||| [k=32]mean_recall=0.44, mean_hard_recall=0.35,  ||| [k=64]mean_recall=0.48, mean_hard_recall=0.40,  ||| [k=128]mean_recall=0.50, mean_hard_recall=0.42,  ||| [k=256]mean_recall=0.52, mean_hard_recall=0.43,  ||| [k=512]mean_recall=0.55, mean_hard_recall=0.47,  ||| [k=1024]mean_recall=0.62, mean_hard_recall=0.54,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.37, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.47, mean_hard_recall=0.33,  ||| [k=32]mean_recall=0.58, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.63, mean_hard_recall=0.50,  ||| [k=128]mean_recall=0.66, mean_hard_recall=0.53,  ||| [k=256]mean_recall=0.68, mean_hard_recall=0.55,  ||| [k=512]mean_recall=0.73, mean_hard_recall=0.60,  ||| [k=1024]mean_recall=0.79, mean_hard_recall=0.67,  ||| \n", "\n"]}], "source": ["# RERUN V12 BUT ALSO COMPUTE K=512 and K=1024, \n", "# but also filter out examples where repo chunks count is less than 2500\n", "paths = [\n", "    \"v12_filtered-raven-2024-11-07-W-False-D-2048-I-0-M-2500\",\n", "    \"v12_filtered-raven-2024-11-07-W-False-D-2048-I-6144-M-2500\",\n", "    \"v12_filtered-autofix-v12_no_instructions-2024-11-07-W-False-D-2048-I-0-M-2500\",\n", "    \"v12_filtered-autofix-v12-2024-11-07-W-False-D-2048-I-6144-M-2500\",\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-0/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.23, mean_hard_recall=0.15,  ||| [k=8]mean_recall=0.33, mean_hard_recall=0.24,  ||| [k=32]mean_recall=0.45, mean_hard_recall=0.36,  ||| [k=64]mean_recall=0.51, mean_hard_recall=0.42,  ||| [k=128]mean_recall=0.56, mean_hard_recall=0.48,  ||| [k=256]mean_recall=0.60, mean_hard_recall=0.54,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.32, mean_hard_recall=0.22,  ||| [k=8]mean_recall=0.44, mean_hard_recall=0.33,  ||| [k=32]mean_recall=0.59, mean_hard_recall=0.47,  ||| [k=64]mean_recall=0.66, mean_hard_recall=0.55,  ||| [k=128]mean_recall=0.72, mean_hard_recall=0.62,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.69,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-6144/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.23, mean_hard_recall=0.15,  ||| [k=8]mean_recall=0.33, mean_hard_recall=0.23,  ||| [k=32]mean_recall=0.46, mean_hard_recall=0.36,  ||| [k=64]mean_recall=0.52, mean_hard_recall=0.44,  ||| [k=128]mean_recall=0.57, mean_hard_recall=0.49,  ||| [k=256]mean_recall=0.62, mean_hard_recall=0.55,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.31, mean_hard_recall=0.22,  ||| [k=8]mean_recall=0.43, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.59, mean_hard_recall=0.47,  ||| [k=64]mean_recall=0.67, mean_hard_recall=0.56,  ||| [k=128]mean_recall=0.73, mean_hard_recall=0.63,  ||| [k=256]mean_recall=0.80, mean_hard_recall=0.70,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12_no_instructions-2024-10-31-TEST-W-False-D-2048-I-0/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.27, mean_hard_recall=0.20,  ||| [k=8]mean_recall=0.35, mean_hard_recall=0.26,  ||| [k=32]mean_recall=0.46, mean_hard_recall=0.37,  ||| [k=64]mean_recall=0.51, mean_hard_recall=0.43,  ||| [k=128]mean_recall=0.55, mean_hard_recall=0.48,  ||| [k=256]mean_recall=0.59, mean_hard_recall=0.52,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.38, mean_hard_recall=0.28,  ||| [k=8]mean_recall=0.48, mean_hard_recall=0.36,  ||| [k=32]mean_recall=0.61, mean_hard_recall=0.50,  ||| [k=64]mean_recall=0.66, mean_hard_recall=0.55,  ||| [k=128]mean_recall=0.71, mean_hard_recall=0.61,  ||| [k=256]mean_recall=0.76, mean_hard_recall=0.66,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12-2024-10-31-TEST-W-False-D-2048-I-6144/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.54, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.58, mean_hard_recall=0.49,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.53,  ||| [k=256]mean_recall=0.64, mean_hard_recall=0.57,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.46, mean_hard_recall=0.34,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.43,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.57,  ||| [k=64]mean_recall=0.74, mean_hard_recall=0.62,  ||| [k=128]mean_recall=0.77, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.80, mean_hard_recall=0.71,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12_with_v2_3b_reranker-2024-11-13-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.35, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.43, mean_hard_recall=0.32,  ||| [k=32]mean_recall=0.54, mean_hard_recall=0.45,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.47, mean_hard_recall=0.34,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.43,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.57,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12_with_v2_3b_reranker-2024-11-13-k128-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.35, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.53, mean_hard_recall=0.42,  ||| [k=64]mean_recall=0.58, mean_hard_recall=0.48,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.53,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.47, mean_hard_recall=0.34,  ||| [k=8]mean_recall=0.56, mean_hard_recall=0.42,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.55,  ||| [k=64]mean_recall=0.73, mean_hard_recall=0.61,  ||| [k=128]mean_recall=0.77, mean_hard_recall=0.67,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12_with_v2_3b_reranker-ne-2024-11-15-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.35, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.30,  ||| [k=32]mean_recall=0.54, mean_hard_recall=0.42,  ||| [k=64]mean_recall=0.58, mean_hard_recall=0.49,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.53,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.48, mean_hard_recall=0.35,  ||| [k=8]mean_recall=0.56, mean_hard_recall=0.42,  ||| [k=32]mean_recall=0.68, mean_hard_recall=0.54,  ||| [k=64]mean_recall=0.73, mean_hard_recall=0.61,  ||| [k=128]mean_recall=0.77, mean_hard_recall=0.67,  ||| \n", "\n"]}], "source": ["### <PERSON>ranker, K=32, no filtering on repo size\n", "paths = [\n", "    # baselines\n", "    \"v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-0\",\n", "    \"v12_filtered-raven-2024-10-31-TEST-W-False-D-2048-I-6144\",\n", "    \"v12_filtered-autofix-v12_no_instructions-2024-10-31-TEST-W-False-D-2048-I-0\",\n", "    \"v12_filtered-autofix-v12-2024-10-31-TEST-W-False-D-2048-I-6144\",\n", "    # reranker k32 - 15b\n", "    #\"v12_filtered-autofix-v12_with_v2_reranker-2024-11-13-W-False-D-2048-I-0-M-0\",\n", "    # TODO: add 15b reranker with instructions\n", "    # reranker k32 - 3b\n", "    #\"v12_filtered-autofix-v12_with_v2_3b_reranker-2024-11-13-W-False-D-2048-I-0-M-0\",\n", "    \"v12_filtered-autofix-v12_with_v2_3b_reranker-2024-11-13-W-False-D-2048-I-6144-M-0\",\n", "    # reranker k128 - 3b\n", "    \"v12_filtered-autofix-v12_with_v2_3b_reranker-2024-11-13-k128-W-False-D-2048-I-6144-M-0\",\n", "    # reranker k128 - 3b - next edit base model\n", "    \"v12_filtered-autofix-v12_with_v2_3b_reranker-ne-2024-11-15-W-False-D-2048-I-6144-M-0\",\n", "\n", "]\n", "for path in paths:\n", "    viz_results(path)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v12-2024-10-31-TEST-W-False-D-2048-I-6144/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.24,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.31,  ||| [k=32]mean_recall=0.54, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.58, mean_hard_recall=0.49,  ||| [k=128]mean_recall=0.61, mean_hard_recall=0.53,  ||| [k=256]mean_recall=0.64, mean_hard_recall=0.57,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.46, mean_hard_recall=0.34,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.43,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.57,  ||| [k=64]mean_recall=0.74, mean_hard_recall=0.62,  ||| [k=128]mean_recall=0.77, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.80, mean_hard_recall=0.71,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v12_filtered-autofix-v13-2024-11-17-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 1084\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.37, mean_hard_recall=0.26,  ||| [k=8]mean_recall=0.45, mean_hard_recall=0.33,  ||| [k=32]mean_recall=0.55, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.59, mean_hard_recall=0.50,  ||| [k=128]mean_recall=0.62, mean_hard_recall=0.55,  ||| [k=256]mean_recall=0.65, mean_hard_recall=0.58,  ||| [k=512]mean_recall=0.68, mean_hard_recall=0.64,  ||| [k=1024]mean_recall=0.71, mean_hard_recall=0.68,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.50, mean_hard_recall=0.36,  ||| [k=8]mean_recall=0.59, mean_hard_recall=0.45,  ||| [k=32]mean_recall=0.69, mean_hard_recall=0.58,  ||| [k=64]mean_recall=0.74, mean_hard_recall=0.62,  ||| [k=128]mean_recall=0.78, mean_hard_recall=0.68,  ||| [k=256]mean_recall=0.81, mean_hard_recall=0.72,  ||| [k=512]mean_recall=0.86, mean_hard_recall=0.79,  ||| [k=1024]mean_recall=0.89, mean_hard_recall=0.84,  ||| \n", "\n"]}], "source": ["# New v13 model on old v12 eval dataset\n", "paths = [\n", "    \"v12_filtered-autofix-v12-2024-10-31-TEST-W-False-D-2048-I-6144\",\n", "    \"v12_filtered-autofix-v13-2024-11-17-W-False-D-2048-I-6144-M-0\",\n", "]\n", "for path in paths:\n", "    viz_results(path)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/v13-autofix-v12_no_instructions-2024-11-17-W-False-D-2048-I-0-M-0/000_results.jsonl\n", "Num examples evaluated: 945\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.25, mean_hard_recall=0.20,  ||| [k=8]mean_recall=0.33, mean_hard_recall=0.29,  ||| [k=32]mean_recall=0.41, mean_hard_recall=0.37,  ||| [k=64]mean_recall=0.44, mean_hard_recall=0.40,  ||| [k=128]mean_recall=0.48, mean_hard_recall=0.43,  ||| [k=256]mean_recall=0.51, mean_hard_recall=0.47,  ||| [k=512]mean_recall=0.54, mean_hard_recall=0.50,  ||| [k=1024]mean_recall=0.59, mean_hard_recall=0.55,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.28,  ||| [k=8]mean_recall=0.45, mean_hard_recall=0.39,  ||| [k=32]mean_recall=0.55, mean_hard_recall=0.50,  ||| [k=64]mean_recall=0.60, mean_hard_recall=0.54,  ||| [k=128]mean_recall=0.65, mean_hard_recall=0.59,  ||| [k=256]mean_recall=0.69, mean_hard_recall=0.63,  ||| [k=512]mean_recall=0.74, mean_hard_recall=0.68,  ||| [k=1024]mean_recall=0.79, mean_hard_recall=0.75,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v13-autofix-v12-2024-11-17-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 945\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.33, mean_hard_recall=0.27,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.37,  ||| [k=32]mean_recall=0.50, mean_hard_recall=0.45,  ||| [k=64]mean_recall=0.53, mean_hard_recall=0.48,  ||| [k=128]mean_recall=0.56, mean_hard_recall=0.51,  ||| [k=256]mean_recall=0.58, mean_hard_recall=0.54,  ||| [k=512]mean_recall=0.62, mean_hard_recall=0.58,  ||| [k=1024]mean_recall=0.64, mean_hard_recall=0.62,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.45, mean_hard_recall=0.36,  ||| [k=8]mean_recall=0.56, mean_hard_recall=0.49,  ||| [k=32]mean_recall=0.66, mean_hard_recall=0.60,  ||| [k=64]mean_recall=0.70, mean_hard_recall=0.64,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.68,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.72,  ||| [k=512]mean_recall=0.81, mean_hard_recall=0.77,  ||| [k=1024]mean_recall=0.86, mean_hard_recall=0.82,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v13-autofix-v13-2024-11-17-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 945\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.28,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.36,  ||| [k=32]mean_recall=0.49, mean_hard_recall=0.44,  ||| [k=64]mean_recall=0.52, mean_hard_recall=0.47,  ||| [k=128]mean_recall=0.55, mean_hard_recall=0.51,  ||| [k=256]mean_recall=0.58, mean_hard_recall=0.53,  ||| [k=512]mean_recall=0.61, mean_hard_recall=0.56,  ||| [k=1024]mean_recall=0.64, mean_hard_recall=0.60,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.45, mean_hard_recall=0.37,  ||| [k=8]mean_recall=0.56, mean_hard_recall=0.48,  ||| [k=32]mean_recall=0.65, mean_hard_recall=0.58,  ||| [k=64]mean_recall=0.69, mean_hard_recall=0.63,  ||| [k=128]mean_recall=0.73, mean_hard_recall=0.68,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.71,  ||| [k=512]mean_recall=0.80, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.84, mean_hard_recall=0.80,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v13-autofix-v13-halfdata-2024-11-17-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 945\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.33, mean_hard_recall=0.27,  ||| [k=8]mean_recall=0.42, mean_hard_recall=0.36,  ||| [k=32]mean_recall=0.49, mean_hard_recall=0.44,  ||| [k=64]mean_recall=0.52, mean_hard_recall=0.47,  ||| [k=128]mean_recall=0.56, mean_hard_recall=0.51,  ||| [k=256]mean_recall=0.58, mean_hard_recall=0.53,  ||| [k=512]mean_recall=0.61, mean_hard_recall=0.57,  ||| [k=1024]mean_recall=0.64, mean_hard_recall=0.61,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.45, mean_hard_recall=0.37,  ||| [k=8]mean_recall=0.55, mean_hard_recall=0.47,  ||| [k=32]mean_recall=0.65, mean_hard_recall=0.58,  ||| [k=64]mean_recall=0.69, mean_hard_recall=0.62,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.71,  ||| [k=512]mean_recall=0.81, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.84, mean_hard_recall=0.80,  ||| \n", "\n", "For path: /mnt/efs/augment/eval/autofix/v13-autofix-v13-no-instructions-2024-11-19-W-False-D-2048-I-0-M-0/000_results.jsonl\n", "Num examples evaluated: 945\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.25, mean_hard_recall=0.20,  ||| [k=8]mean_recall=0.34, mean_hard_recall=0.29,  ||| [k=32]mean_recall=0.41, mean_hard_recall=0.36,  ||| [k=64]mean_recall=0.44, mean_hard_recall=0.39,  ||| [k=128]mean_recall=0.47, mean_hard_recall=0.42,  ||| [k=256]mean_recall=0.50, mean_hard_recall=0.45,  ||| [k=512]mean_recall=0.54, mean_hard_recall=0.49,  ||| [k=1024]mean_recall=0.58, mean_hard_recall=0.53,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.27,  ||| [k=8]mean_recall=0.45, mean_hard_recall=0.38,  ||| [k=32]mean_recall=0.55, mean_hard_recall=0.49,  ||| [k=64]mean_recall=0.60, mean_hard_recall=0.53,  ||| [k=128]mean_recall=0.64, mean_hard_recall=0.57,  ||| [k=256]mean_recall=0.68, mean_hard_recall=0.62,  ||| [k=512]mean_recall=0.73, mean_hard_recall=0.67,  ||| [k=1024]mean_recall=0.78, mean_hard_recall=0.73,  ||| \n", "\n"]}], "source": ["# v12 and v13 models on new v13 eval dataset\n", "paths = [\n", "    \"v13-autofix-v12_no_instructions-2024-11-17-W-False-D-2048-I-0-M-0\",\n", "    \"v13-autofix-v12-2024-11-17-W-False-D-2048-I-6144-M-0\",\n", "    \"v13-autofix-v13-2024-11-17-W-False-D-2048-I-6144-M-0\",\n", "    \"v13-autofix-v13-halfdata-2024-11-17-W-False-D-2048-I-6144-M-0\",\n", "    \"v13-autofix-v13-no-instructions-2024-11-19-W-False-D-2048-I-0-M-0\",\n", "]\n", "for path in paths:\n", "    viz_results(path)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/fixed-autofix-v13-2024-11-25-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 794\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.34, mean_hard_recall=0.28,  ||| [k=8]mean_recall=0.41, mean_hard_recall=0.36,  ||| [k=32]mean_recall=0.48, mean_hard_recall=0.43,  ||| [k=64]mean_recall=0.49, mean_hard_recall=0.45,  ||| [k=128]mean_recall=0.52, mean_hard_recall=0.47,  ||| [k=256]mean_recall=0.54, mean_hard_recall=0.50,  ||| [k=512]mean_recall=0.57, mean_hard_recall=0.53,  ||| [k=1024]mean_recall=0.61, mean_hard_recall=0.57,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.48, mean_hard_recall=0.38,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.49,  ||| [k=32]mean_recall=0.66, mean_hard_recall=0.59,  ||| [k=64]mean_recall=0.70, mean_hard_recall=0.63,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.78, mean_hard_recall=0.71,  ||| [k=512]mean_recall=0.82, mean_hard_recall=0.76,  ||| [k=1024]mean_recall=0.87, mean_hard_recall=0.82,  ||| \n", "\n"]}], "source": ["# v14 eval\n", "paths = [\n", "    \"fixed-autofix-v13-2024-11-25-W-False-D-2048-I-6144-M-0\",\n", "]\n", "for path in paths:\n", "    viz_results(path)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For path: /mnt/efs/augment/eval/autofix/fixed4-autofix-v13-2024-11-27-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 862\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.46, mean_hard_recall=0.38,  ||| [k=8]mean_recall=0.56, mean_hard_recall=0.49,  ||| [k=32]mean_recall=0.66, mean_hard_recall=0.59,  ||| [k=64]mean_recall=0.68, mean_hard_recall=0.63,  ||| [k=128]mean_recall=0.73, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.70,  ||| [k=512]mean_recall=0.81, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.87, mean_hard_recall=0.83,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.48, mean_hard_recall=0.40,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.50,  ||| [k=32]mean_recall=0.66, mean_hard_recall=0.60,  ||| [k=64]mean_recall=0.70, mean_hard_recall=0.64,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.68,  ||| [k=256]mean_recall=0.78, mean_hard_recall=0.72,  ||| [k=512]mean_recall=0.82, mean_hard_recall=0.77,  ||| [k=1024]mean_recall=0.87, mean_hard_recall=0.82,  ||| \n", "\n"]}, {"data": {"text/plain": ["'\\nFor path: /mnt/efs/augment/eval/autofix/fixed4-autofix-v14-2024-11-27-W-False-D-2048-I-6144-M-0/000_results.jsonl\\nNum examples evaluated: 862\\nMACRO METRICS (average per repo then average those averages)\\n[k=3]mean_recall=0.45, mean_hard_recall=0.37,  ||| [k=8]mean_recall=0.55, mean_hard_recall=0.47,  ||| [k=32]mean_recall=0.64, mean_hard_recall=0.57,  ||| [k=64]mean_recall=0.69, mean_hard_recall=0.63,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.71,  ||| [k=512]mean_recall=0.80, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.86, mean_hard_recall=0.81,  ||| \\nMICRO METRICS\\n[k=3]mean_recall=0.48, mean_hard_recall=0.40,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.49,  ||| [k=32]mean_recall=0.65, mean_hard_recall=0.59,  ||| [k=64]mean_recall=0.70, mean_hard_recall=0.65,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.68,  ||| [k=256]mean_recall=0.76, mean_hard_recall=0.72,  ||| [k=512]mean_recall=0.80, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.87, mean_hard_recall=0.81,  ||| \\n\\n'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["paths = [\n", "    #\"fixed4-autofix-v14-2024-11-27-W-False-D-2048-I-6144-M-0\",\n", "    \"fixed4-autofix-v13-2024-11-27-W-False-D-2048-I-6144-M-0\"\n", "]\n", "for path in paths:\n", "    viz_results(path)\n", "\n", "\"\"\"\n", "For path: /mnt/efs/augment/eval/autofix/fixed4-autofix-v14-2024-11-27-W-False-D-2048-I-6144-M-0/000_results.jsonl\n", "Num examples evaluated: 862\n", "MACRO METRICS (average per repo then average those averages)\n", "[k=3]mean_recall=0.45, mean_hard_recall=0.37,  ||| [k=8]mean_recall=0.55, mean_hard_recall=0.47,  ||| [k=32]mean_recall=0.64, mean_hard_recall=0.57,  ||| [k=64]mean_recall=0.69, mean_hard_recall=0.63,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.67,  ||| [k=256]mean_recall=0.77, mean_hard_recall=0.71,  ||| [k=512]mean_recall=0.80, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.86, mean_hard_recall=0.81,  ||| \n", "MICRO METRICS\n", "[k=3]mean_recall=0.48, mean_hard_recall=0.40,  ||| [k=8]mean_recall=0.57, mean_hard_recall=0.49,  ||| [k=32]mean_recall=0.65, mean_hard_recall=0.59,  ||| [k=64]mean_recall=0.70, mean_hard_recall=0.65,  ||| [k=128]mean_recall=0.74, mean_hard_recall=0.68,  ||| [k=256]mean_recall=0.76, mean_hard_recall=0.72,  ||| [k=512]mean_recall=0.80, mean_hard_recall=0.75,  ||| [k=1024]mean_recall=0.87, mean_hard_recall=0.81,  ||| \n", "\n", "\"\"\""]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}