{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Localization task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import zstandard as zstd\n", "import json\n", "\n", "data_path = \"/mnt/efs/augment/eval/autofix/v17-autofix-v17-2024-12-28-D-4096-I-0-O-4096-a149/000_next_edit_location_NextEditLocationEvalTask_output.jsonl.zst\"\n", "data = []\n", "with zstd.open(data_path, \"rt\", encoding=\"utf-8\") as f:\n", "    data = [json.loads(line) for line in f]\n", "\n", "for i in range(len(data)):\n", "    print(data[i][\"debug_info\"][\"retriever_prompt\"].split(\"<|far_prefix|>\")[0])\n", "    print(\"-\" * 40)\n", "    print(\"NEXT EXAMPLE\")\n", "    print(\"-\" * 40)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generation task"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "data_path = \"/mnt/efs/augment/eval/jobs/EvjYVzxq/colin-h100-v3__AutofixSystem_AutofixTask_artifacts.pkl\"\n", "with open(data_path, \"rb\") as f:\n", "    data = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(len(data)):\n", "    print(data[0].artifacts[\"localization_artifacts\"][\"retriever_prompt\"])\n", "    break"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}