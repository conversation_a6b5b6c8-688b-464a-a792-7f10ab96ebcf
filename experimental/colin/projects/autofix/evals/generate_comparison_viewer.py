"""Generate a data viewer based on the output from the next edit task.

Example:
python3 experimental/arun/next_edits/generate_viewer.py -ds manual.v3 -i /mnt/efs/augment/eval/next_edits/manual.v3-starethanol-2024-07-18 -p
"""

import argparse
from dataclasses import asdict
import dataclasses
import functools
import json
import logging
import pathlib
import re
from concurrent.futures import ProcessPoolExecutor
from itertools import groupby

import jinja2
import torch
from pygments import highlight
from pygments.formatters import HtmlFormatter  # pylint:disable=no-name-in-module
from pygments.lexers import get_lexer_by_name, guess_lexer, guess_lexer_for_filename
from pygments.util import ClassNotFound
from tqdm import tqdm

from base.ranges.range_types import LineRange
from research.core.next_edit_location_prompt_input import FileLocation, PathAndDiff
from research.core.utils_for_log import setup_logging
from research.eval.harness.tasks import next_edit_location_eval_task
from research.eval.harness.tasks.next_edit_location_eval_task import (
    Output,
    TaskInput,
    load_dataset,
    load_output,
)
from research.fastbackward import (
    retrieval_models,
)
from research.next_edits.next_edits_dataset import Datum

logger = logging.getLogger(__name__)


DATASETS = {
    "manual.v3": (
        "/mnt/efs/augment/data/eval/next_edits/manual.v3.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/next_edits/manual.v3.files.jsonl.zst",
    ),
    "autofix-commits-v10": (
        "/mnt/efs/augment/data/eval/autofix/commits-v10.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/autofix/commits-v10.files.jsonl.zst"
    ),
    "autofix-commits-v11": (
        "/mnt/efs/augment/data/eval/autofix/commits-v11.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/autofix/commits-v11.files.jsonl.zst"
    ),
    "autofix-commits-v12": (
        "/mnt/efs/augment/data/eval/autofix/commits-v12.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/autofix/commits-v12.files.jsonl.zst"
    ),
    "autofix-commits-v12_filtered": (
        "/mnt/efs/augment/data/eval/autofix/commits-v12_filtered.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/autofix/commits-v12_filtered.files.jsonl.zst"
    ),
    "autofix-commits-v14-fixed": {
        "/mnt/efs/augment/data/eval/autofix/commits-v14_fixed.diffs.jsonl.zst",
        "/mnt/efs/augment/data/eval/autofix/commits-v14_fixed.files.jsonl.zst",
    },
}


@functools.lru_cache
def load_template(template_path: pathlib.Path) -> jinja2.Template:
    """Load a jinja template from a local file."""
    with template_path.open() as f:
        template_str = f.read()

    return jinja2.Template(template_str)


def score_example(datum: TaskInput, output: Output):
    assert datum.prompt.label is not None

    targets_K = torch.tensor(
        [
            any(
                location.item.path == target_location.path
                and location.item.range.intersect(target_location.range) is not None
                for target_location in datum.prompt.label.locations
            )
            for location in output.scored_candidates
        ]
    )
    if not any(targets_K):
        return retrieval_models.RankingMetrics(count=0), targets_K

    scores_K = [location.score for location in output.scored_candidates]

    logits_K = torch.tensor(scores_K)
    pred_lprobs_K = torch.log_softmax(logits_K, dim=-1)
    pred_ranking_K = pred_lprobs_K.argsort(dim=-1, descending=True)

    metrics = retrieval_models.compute_ranking_metrics_from_ranking(
        pred_lprobs_K, pred_ranking_K, targets_K
    )

    return metrics, targets_K.cpu().tolist()


_HUNK_HEADER_RE = re.compile(r"^@@ -(\d+),(\d+) \+(\d+),(\d+) @@")


def render_diff(hunk: PathAndDiff, style: str = "dark"):
    pygments_style = {"dark": "monokai", "light": "default"}[style]

    # Approximate how many header lines are there.
    header_lines = 0
    for line in hunk.diff_text.splitlines():
        header_lines += 1
        if m := _HUNK_HEADER_RE.match(line):
            line_start = int(m.group(1))
            break
    else:
        line_start = 0

    lexer = get_lexer_by_name("diff")
    formatter = HtmlFormatter(
        style=pygments_style,
        linenos="inline",
        linenostart=line_start - header_lines,
    )
    return highlight(hunk.diff_text, lexer, formatter)


def get_styles(style: str = "dark"):
    pygments_style = {"dark": "monokai", "light": "default"}[style]
    formatter = HtmlFormatter(style=pygments_style, linenos="inline")
    return formatter.get_style_defs(".highlight")


def render_example(
    system: str,
    title: str,
    datum: Datum,
    output: Output,
    template: jinja2.Template,
):
    """Render a single example."""
    task_input = next_edit_location_eval_task.convert_to_task_input(datum)
    if task_input is None:
        return None
    assert task_input and task_input.prompt.label is not None

    future_hunk_location = [
        (
            hunk,
            min(
                (
                    i
                    for i, location in enumerate(output.scored_candidates[:256])
                    if location.item.intersect(
                        FileLocation(
                            hunk.path,
                            LineRange(
                                hunk.diff_hunk.source_start,
                                hunk.diff_hunk.source_start
                                + hunk.diff_hunk.source_length,
                            ),
                        )
                    )
                    is not None
                ),
                default=-1,
            ),
        )
        for hunk in task_input.prompt.future_hunks
    ]

    # Let us viz file renames.
    for i in range(len(future_hunk_location)):
        hunk = future_hunk_location[i][0]
        if hunk.path != hunk.new_path:
            # new_hunk = dataclasses.replace(hunk, path = f"{hunk.path} -> {hunk.new_path}")
            new_hunk = dataclasses.replace(hunk, path = "IGNORE FOR NOW")
            future_hunk_location[i] = (new_hunk, future_hunk_location[i][1])

    candidate_location_correct = {
        location.item: label
        for location, label in zip(
            output.scored_candidates, output.scored_candidate_labels
        )
    }

    output = dataclasses.replace(output, scored_candidates = output.scored_candidates[:256])

    return template.render(
        system=system,
        title=title,
        metrics=output.metrics_by_k['32'],
        datum=task_input,
        output=output,
        future_hunk_location=future_hunk_location,
        candidate_location_correct=candidate_location_correct,
        styles=get_styles(),
        render_diff=render_diff,
        render_location=render_location,
        label_icons={True: "✅︎", False: "❌"},
        render_location_cutoff=32,
    )


def render_location(
    datum: TaskInput, location: FileLocation, n_context: int = 0
) -> str:
    # 1. Find the relevant document in the dataset.
    document = next(
        (document for document in datum.documents if document.path == location.path),
        None,
    )
    if not document:
        return f"(missing document {location})"
    lines = document.text.splitlines(True)
    # 2. Find the relevant code in the document.
    line_start = max(0, location.range.start - n_context)

    code = "".join(lines[line_start : location.range.stop + n_context])
    # 3. Highlight the code.
    try:
        lexer = guess_lexer_for_filename(location.path, code)
    except ClassNotFound:
        lexer = guess_lexer(code)

    formatter = HtmlFormatter(
        style="monokai",
        linenos="inline",
        linenostart=line_start + 1,
        hl_lines=list(
            range(
                location.range.start - line_start + 1,
                location.range.stop - line_start + 1,
            )
        ),
    )
    return highlight(code, lexer, formatter)


def render_index(
    systemA: str,
    systemB: str,
    metrics: dict[str, dict[str, retrieval_models.RankingMetrics]],
    target_outputs: list[tuple[next_edit_location_eval_task.Output, next_edit_location_eval_task.Output]],
    top_k: int,
):
    """
    This assumes that datum and output lists match.
    """
    template = get_template(pathlib.Path(__file__).parent / "index_comparison.html")

    return template.render(
        systemA=systemA,
        systemB=systemB,
        metrics=metrics,
        target_outputs=target_outputs,
        top_k=top_k,
    )


@functools.lru_cache
def get_template(path: pathlib.Path):
    logger.info("Loading template from %s...", path)
    return load_template(path)


def save_example(
    system: str, title: str, datum: Datum, output: Output, output_path: pathlib.Path
):
    example_template = get_template(pathlib.Path(__file__).parent / "example.html")
    html = render_example(system, title, datum, output, example_template)
    if html is None:
        return
    (output_path).write_text(html)

def save_example_comparison(
    output_path, system_name_modelA, system_filename_modelA, system_name_modelB, system_filename_modelB
):
    comparison_template = get_template(pathlib.Path(__file__).parent / "example_comparison.html")
    html = comparison_template.render(
        system_name_modelA=system_name_modelA,
        system_filename_modelA=system_filename_modelA,
        system_name_modelB=system_name_modelB,
        system_filename_modelB=system_filename_modelB,
    )
    output_path.write_text(html)

def main():
    setup_logging()

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-iA",
        "--input_path_A",
        type=pathlib.Path,
        required=True,
        help="Path to one experiment output directories.",
    )
    parser.add_argument(
        "-iB",
        "--input_path_B",
        type=pathlib.Path,
        required=True,
        help="Path to another experiment output directories.",
    )
    parser.add_argument(
        "-o",
        "--output_root",
        type=pathlib.Path,
        default=pathlib.Path("/mnt/efs/augment/public_html/autofix_location/evals/"),
        help="Path to output root directory.",
    )
    parser.add_argument(
        "-ds",
        "--dataset",
        choices=DATASETS.keys(),
        help="The dataset to use.",
    )
    parser.add_argument(
        "-d",
        "--diffs_path",
        type=pathlib.Path,
        help="Path to the diffs.",
    )
    parser.add_argument(
        "-f",
        "--files_path",
        type=pathlib.Path,
        help="Path to the files.",
    )
    parser.add_argument(
        "-l",
        "--limit",
        type=int,
        help="If set, only load these many entries.",
    )
    parser.add_argument(
        "-p",
        "--parallelize",
        action="store_true",
        help="If set, parallelize the generation of examples.",
    )
    parser.add_argument(
        "--index-only",
        action="store_true",
        help="If set, only generate the index.",
    )
    parser.add_argument(
        "--top-k",
        type=int,
        default=32,
        help="If set, only generate the index.",
    )
    args = parser.parse_args()

    if args.dataset is not None:
        args.diffs_path, args.files_path = DATASETS[args.dataset]
    assert (
        args.diffs_path is not None and args.files_path is not None
    ), "You must specify either --dataset or --diffs_path and --files_path"

    logger.info("Loading dataset...")
    raw_dataset = [datum for datum in load_dataset(args.diffs_path, args.files_path)]
    for datum in raw_dataset:
        datum.group_id = datum.group_id.replace('/', '-_-')
    id_to_datum = {datum.id: datum for datum in raw_dataset}

    input_path_modelA = pathlib.Path("/mnt/efs/augment/eval/autofix/") / args.input_path_A
    input_path_modelB = pathlib.Path("/mnt/efs/augment/eval/autofix/") / args.input_path_B

    artifact_path_modelA = next(input_path_modelA.glob("*.jsonl.zst"))
    metrics_path_modelA = next(input_path_modelA.glob("*results.jsonl"))
    artifact_path_modelB = next(input_path_modelB.glob("*.jsonl.zst"))
    metrics_path_modelB = next(input_path_modelB.glob("*results.jsonl"))

    system_name_modelA = input_path_modelA.name.replace('/', '__')
    system_name_modelB = input_path_modelB.name.replace('/', '__')
    output_path = args.output_root / (system_name_modelA + "_VS_" + system_name_modelB)
    

    logger.info("Loading metrics and output for model A from %s...", artifact_path_modelA)
    logger.info("Also loading metrics and output for model B from %s...", artifact_path_modelB)
    metrics_modelA = next_edit_location_eval_task.OutputMetrics.schema().load(
        json.loads(metrics_path_modelA.read_text()),
    )
    metrics_modelB = next_edit_location_eval_task.OutputMetrics.schema().load(
        json.loads(metrics_path_modelB.read_text()),
    )
    outputs_modelA = load_output(artifact_path_modelA, limit=args.limit)
    outputs_modelB = load_output(artifact_path_modelB, limit=args.limit)
    for output in outputs_modelA:
        output.repo_name = output.repo_name.replace('/', '-_-')
        output.group_id = output.group_id.replace('/', '-_-')
    for output in outputs_modelB:
        output.repo_name = output.repo_name.replace('/', '-_-')
        output.group_id = output.group_id.replace('/', '-_-')

    # find outputs that passed model A but not model B
    id_to_outputs_modelB = {output.id: output for output in outputs_modelB}
    target_outputs = []
    for output_modelA in outputs_modelA:
        output_modelB = id_to_outputs_modelB[output_modelA.id]
        if output_modelA.metrics_by_k['32'].mean_hard_recall > 0.999 and output_modelB.metrics_by_k['32'].mean_hard_recall < 0.001:
            logger.error("Values for mean hard recall: %s, %s", output_modelA.metrics_by_k['32'].mean_hard_recall, output_modelB.metrics_by_k['32'].mean_hard_recall)
            target_outputs.append((output_modelA, output_modelB))
    logger.error("Num target_outputs: %s", len(target_outputs))

    # now we need to grab the datum corresponding to these outputs.
    dataset = [id_to_datum[output_modelA.id] for output_modelA, output_modelB in target_outputs]
    
    logger.info("Generating index...")
    output_path.mkdir(parents=True, exist_ok=True)
    logger.info(f"Writing to output dir {output_path}")
    html = render_index(system_name_modelA, system_name_modelB, metrics_modelA, target_outputs, args.top_k)
    (output_path / "index.html").write_text(html)
    logger.info(f"Wrote index to {output_path / 'index.html'}.")

    if args.index_only:
        logger.info("Skipping generating examples...")
        return

    logger.info("Generating examples...")
    futures = []
    with ProcessPoolExecutor() as executor:
        for datum, (output_modelA, output_modelB) in list(zip(dataset, target_outputs)):
            futures.append(
                executor.submit(
                    save_example,
                    system_name_modelA,
                    f"Example {system_name_modelA}/{datum.group_id}/{datum.group_sequence_id}",
                    datum,
                    output_modelA,
                    output_path
                    / f"example-{system_name_modelA}-{datum.group_id}.{datum.group_sequence_id}.html",
                )
            )
            futures.append(
                executor.submit(
                    save_example,
                    system_name_modelB,
                    f"Example {system_name_modelB}/{datum.group_id}/{datum.group_sequence_id}",
                    datum,
                    output_modelB,
                    output_path
                    / f"example-{system_name_modelB}-{datum.group_id}.{datum.group_sequence_id}.html",
                )
            )
            futures.append(
                executor.submit(
                    save_example_comparison,
                    output_path / f"example-{datum.group_id}.{datum.group_sequence_id}.html",
                    system_name_modelA,
                    f"example-{system_name_modelA}-{datum.group_id}.{datum.group_sequence_id}.html",
                    system_name_modelB,
                    f"example-{system_name_modelB}-{datum.group_id}.{datum.group_sequence_id}.html",
                )
            )

        for future in tqdm(futures, total=len(futures)):
            future.result()


if __name__ == "__main__":
    main()
