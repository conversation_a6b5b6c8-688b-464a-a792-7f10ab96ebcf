{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This will take about 40min because the chunker is slow.\n", "\n", "from research.retrieval.chunking_functions import SmartLineLevelChunker\n", "from research.eval.harness.tasks.next_edit_location_eval_task import load_dataset\n", "import numpy as np\n", "\n", "#typed_dataset = load_dataset(\n", "#    \"/mnt/efs/augment/data/eval/autofix/commits-v12.diffs.jsonl.zst\", \"/mnt/efs/augment/data/eval/autofix/commits-v12.files.jsonl.zst\"\n", "#)\n", "typed_dataset = load_dataset(\n", "    \"/mnt/efs/augment/data/eval/autofix/commits-v13-fixed_filtered.diffs.jsonl.zst\", \"/mnt/efs/augment/data/eval/autofix/commits-v13-fixed_filtered.files.jsonl.zst\"\n", ")\n", "\n", "\n", "import uuid\n", "from research.core.types import Document\n", "from research.retrieval.chunking_functions import SmartLineLevelChunker\n", "chunker = SmartLineLevelChunker(max_chunk_chars=2000)\n", "\n", "chunk_lens = []\n", "for j in range(len(typed_dataset)):\n", "    datum = typed_dataset[j]\n", "    num_chunks = 0\n", "    for i in range(len(datum.wip_files)):\n", "        doc = Document(id=uuid.uuid4(), text=datum.wip_files[i].contents, path=datum.wip_files[i].path)\n", "        num_chunks += len(chunker.split_into_chunks(doc))\n", "\n", "    print(num_chunks)\n", "    chunk_lens.append(num_chunks)\n", "\n", "print(f\"mean: {np.mean(chunk_lens)}\")\n", "print(f\"std: {np.std(chunk_lens)}\")\n", "print(f\"min: {np.min(chunk_lens)}\")\n", "print(f\"max: {np.max(chunk_lens)}\")\n", "print(f\"median: {np.median(chunk_lens)}\")\n", "print(f\"5th percentile: {np.percentile(chunk_lens, 5)}\")\n", "print(f\"25th percentile: {np.percentile(chunk_lens, 25)}\")\n", "print(f\"75th percentile: {np.percentile(chunk_lens, 75)}\")\n", "print(f\"95th percentile: {np.percentile(chunk_lens, 95)}\")\n", "print(f\"Total examples: {len(chunk_lens)}\")\n", "print(f\"Num examples above 2500 chunks: {sum(np.array(chunk_lens) > 2500)}\")\n", "print(f\"Num examples above 5000 chunks: {sum(np.array(chunk_lens) > 5000)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"mean: {np.mean(chunk_lens)}\")\n", "print(f\"std: {np.std(chunk_lens)}\")\n", "print(f\"min: {np.min(chunk_lens)}\")\n", "print(f\"max: {np.max(chunk_lens)}\")\n", "print(f\"median: {np.median(chunk_lens)}\")\n", "print(f\"1st percentile: {np.percentile(chunk_lens, 1)}\")\n", "print(f\"2.5th percentile: {np.percentile(chunk_lens, 2.5)}\")\n", "print(f\"5th percentile: {np.percentile(chunk_lens, 5)}\")\n", "print(f\"25th percentile: {np.percentile(chunk_lens, 25)}\")\n", "print(f\"75th percentile: {np.percentile(chunk_lens, 75)}\")\n", "print(f\"95th percentile: {np.percentile(chunk_lens, 95)}\")\n", "print(f\"Total examples: {len(chunk_lens)}\")\n", "print(f\"Num examples less than 500 chunks: {sum(np.array(chunk_lens) < 500)}\")\n", "print(f\"Num examples less than 1000 chunks: {sum(np.array(chunk_lens) < 1000)}\")\n", "print(f\"Num examples above 2500 chunks: {sum(np.array(chunk_lens) > 2500)}\")\n", "print(f\"Num examples above 5000 chunks: {sum(np.array(chunk_lens) > 5000)}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}