system:
  name: remote_chat
  # The model name is optional, will select default if not present
  model_name: binks-v11-c1-16
  client:
      # Set a url to run the remote system.
      url: https://dev-colin.us-central.api.augmentcode.com
  chat:
    indexing_retry_sleep_secs: 30.0
    indexing_retry_count: 25

task:
  name: augment_qa
  dataset_path: /mnt/efs/augment/data/processed/augment_qa/v3
  html_report_output_dir: /mnt/efs/augment/data/processed/augment_qa/v3

podspec: gpu-small.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: augmentqa3-binksv11-c1-16
  project: colin-eval
  workspace: Dev
