system:
  name: remote_chat
  # The model name is optional, will select default if not present
  model_name: claude-sonnet-3-5-128k-chat
  client:
      # Set a url to run the remote system.
      # url: https://dev-<USER>.us-central.api.augmentcode.com
      url: https://dev-colin.us-central.api.augmentcode.com/
  chat:
    indexing_retry_sleep_secs: 30.0
    indexing_retry_count: 25

task:
  name: augment_qa
  dataset_path: /mnt/efs/augment/data/processed/modal_labs_v0/
  html_report_output_dir: /mnt/efs/augment/public_html/modal_labs/v0

podspec: gpu-small.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: modal-labs-v0-eval-claude
  project: colin-eval
  workspace: Dev
