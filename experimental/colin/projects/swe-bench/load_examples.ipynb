{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset, load_dataset"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["dataset = load_dataset(\"princeton-nlp/SWE-bench_Verified\", split=\"test\")\n", "dataset = dataset.shuffle(seed=42)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[38;5;15mdiff --git a/example.py b/example.py\u001b[39m\n", "\u001b[38;5;15mindex 1234567..89abcdef 100644\u001b[39m\n", "\u001b[38;5;204m--- a/example.py\u001b[39m\n", "\u001b[38;5;148m+++ b/example.py\u001b[39m\n", "\u001b[38;5;245m@@ -1,5 +1,5 @@\u001b[39m\n", "\u001b[38;5;15m \u001b[39m\u001b[38;5;15mdef hello():\u001b[39m\n", "\u001b[38;5;204m-    print(\"Hello\")\u001b[39m\n", "\u001b[38;5;148m+    print(\"Hello, World!\")\u001b[39m\n", "\u001b[38;5;15m \u001b[39m\u001b[38;5;15m    return True\u001b[39m\n", "\n"]}], "source": ["import pygments\n", "from pygments.lexers import DiffLexer\n", "from pygments.formatters import Terminal256Formatter, HtmlFormatter\n", "\n", "\n", "def print_diff(diff_text: str, output_format: str = \"terminal\"):\n", "    \"\"\"Pretty print a git diff string.\n", "\n", "    Args:\n", "        diff_text: The git diff string to print\n", "        output_format: Either \"terminal\" or \"html\"\n", "    \"\"\"\n", "    if output_format == \"terminal\":\n", "        # For terminal output with colors\n", "        formatted_diff = pygments.highlight(\n", "            diff_text, DiffLexer(), Terminal256Formatter(style=\"monokai\")\n", "        )\n", "        print(formatted_diff)\n", "    else:\n", "        # For HTML output\n", "        formatted_diff = pygments.highlight(\n", "            diff_text,\n", "            Diff<PERSON><PERSON><PERSON>(),\n", "            HtmlFormatter(style=\"monokai\", linenos=\"inline\", full=True),\n", "        )\n", "        return formatted_diff\n", "\n", "\n", "# Example usage\n", "diff_text = \"\"\"\n", "diff --git a/example.py b/example.py\n", "index 1234567..89abcdef 100644\n", "--- a/example.py\n", "+++ b/example.py\n", "@@ -1,5 +1,5 @@\n", " def hello():\n", "-    print(\"Hello\")\n", "+    print(\"Hello, <PERSON>!\")\n", "     return True\n", "\"\"\"\n", "\n", "# Print to terminal with colors\n", "print_diff(diff_text, \"terminal\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Metadata: repo=django/django, instance_id=django__django-11211, base_commit=ba726067604ce5a8ca3919edf653496722b433ab\n", "--------------------------------------------------------------------------------\n", "Problem statement:\n", "Prefetch related is not working when used GFK for model that uses UUID field as PK.\n", "Description\n", "\t\n", "How to reproduce:\n", "create model with UUID as primary key\n", "class Foo(models.Model):\n", "\tid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)\n", "\t...\n", "create another model with GFK to model Foo\n", "class Bar(models.Model):\n", "\tfoo_content_type = models.ForeignKey(\n", "\t\tContentType, related_name='actor',\n", "\t\ton_delete=models.CASCADE, db_index=True\n", "\t)\n", "\tfoo_object_id = models.CharField(max_length=255, db_index=True)\n", "\tfoo = GenericForeignKey('foo_content_type', 'foo_object_id')\n", "\t...\n", "and try to get queryset with prefetch related (django orm engine return None for attribute foo):\n", "Bar.objects.all().prefetch_related('foo')\n", "Thanks a lot for your attention! Also i wanna point out some related bug report from third party library in which previously i faced with that issue, maybe it would useful – ​https://github.com/justquick/django-activity-stream/issues/245\n", "\n", "--------------------------------------------------------------------------------\n", "\n", "Gold patch: \n", "--------------------------------------------------------------------------------\n", "\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01//EN\"\n", "   \"http://www.w3.org/TR/html4/strict.dtd\">\n", "<!--\n", "generated by Pygments <https://pygments.org/>\n", "Copyright 2006-2024 by the Pygments team.\n", "Licensed under the BSD license, see LICENSE for details.\n", "-->\n", "<html>\n", "<head>\n", "  <title></title>\n", "  <meta http-equiv=\"content-type\" content=\"text/html; charset=None\">\n", "  <style type=\"text/css\">\n", "/*\n", "generated by Pygments <https://pygments.org/>\n", "Copyright 2006-2024 by the Pygments team.\n", "Licensed under the BSD license, see LICENSE for details.\n", "*/\n", "pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "body .hll { background-color: #49483e }\n", "body { background: #272822; color: #f8f8f2 }\n", "body .c { color: #959077 } /* Comment */\n", "body .err { color: #ed007e; background-color: #1e0010 } /* Error */\n", "body .esc { color: #f8f8f2 } /* Escape */\n", "body .g { color: #f8f8f2 } /* Generic */\n", "body .k { color: #66d9ef } /* Keyword */\n", "body .l { color: #ae81ff } /* Literal */\n", "body .n { color: #f8f8f2 } /* Name */\n", "body .o { color: #ff4689 } /* Operator */\n", "body .x { color: #f8f8f2 } /* Other */\n", "body .p { color: #f8f8f2 } /* Punctuation */\n", "body .ch { color: #959077 } /* Comment.Hashbang */\n", "body .cm { color: #959077 } /* Comment.Multiline */\n", "body .cp { color: #959077 } /* Comment.Preproc */\n", "body .cpf { color: #959077 } /* Comment.PreprocFile */\n", "body .c1 { color: #959077 } /* Comment.Single */\n", "body .cs { color: #959077 } /* Comment.Special */\n", "body .gd { color: #ff4689 } /* Generic.Deleted */\n", "body .ge { color: #f8f8f2; font-style: italic } /* Generic.Emph */\n", "body .ges { color: #f8f8f2; font-weight: bold; font-style: italic } /* Generic.EmphStrong */\n", "body .gr { color: #f8f8f2 } /* Generic.Error */\n", "body .gh { color: #f8f8f2 } /* Generic.Heading */\n", "body .gi { color: #a6e22e } /* Generic.Inserted */\n", "body .go { color: #66d9ef } /* Generic.Output */\n", "body .gp { color: #ff4689; font-weight: bold } /* Generic.Prompt */\n", "body .gs { color: #f8f8f2; font-weight: bold } /* Generic.Strong */\n", "body .gu { color: #959077 } /* Generic.Subheading */\n", "body .gt { color: #f8f8f2 } /* Generic.Traceback */\n", "body .kc { color: #66d9ef } /* Keyword.Constant */\n", "body .kd { color: #66d9ef } /* Keyword.Declaration */\n", "body .kn { color: #ff4689 } /* Keyword.Namespace */\n", "body .kp { color: #66d9ef } /* Keyword.Pseudo */\n", "body .kr { color: #66d9ef } /* Keyword.Reserved */\n", "body .kt { color: #66d9ef } /* Keyword.Type */\n", "body .ld { color: #e6db74 } /* Literal.Date */\n", "body .m { color: #ae81ff } /* Literal.Number */\n", "body .s { color: #e6db74 } /* Literal.String */\n", "body .na { color: #a6e22e } /* Name.Attribute */\n", "body .nb { color: #f8f8f2 } /* Name.Builtin */\n", "body .nc { color: #a6e22e } /* Name.Class */\n", "body .no { color: #66d9ef } /* Name.Constant */\n", "body .nd { color: #a6e22e } /* Name.Decorator */\n", "body .ni { color: #f8f8f2 } /* Name.Entity */\n", "body .ne { color: #a6e22e } /* Name.Exception */\n", "body .nf { color: #a6e22e } /* Name.Function */\n", "body .nl { color: #f8f8f2 } /* Name.Label */\n", "body .nn { color: #f8f8f2 } /* Name.Namespace */\n", "body .nx { color: #a6e22e } /* Name.Other */\n", "body .py { color: #f8f8f2 } /* Name.Property */\n", "body .nt { color: #ff4689 } /* Name.Tag */\n", "body .nv { color: #f8f8f2 } /* Name.Variable */\n", "body .ow { color: #ff4689 } /* Operator.Word */\n", "body .pm { color: #f8f8f2 } /* Punctuation.Marker */\n", "body .w { color: #f8f8f2 } /* Text.Whitespace */\n", "body .mb { color: #ae81ff } /* Literal.Number.Bin */\n", "body .mf { color: #ae81ff } /* Literal.Number.Float */\n", "body .mh { color: #ae81ff } /* Literal.Number.Hex */\n", "body .mi { color: #ae81ff } /* Literal.Number.Integer */\n", "body .mo { color: #ae81ff } /* Literal.Number.Oct */\n", "body .sa { color: #e6db74 } /* Literal.String.Affix */\n", "body .sb { color: #e6db74 } /* Literal.String.Backtick */\n", "body .sc { color: #e6db74 } /* Literal.String.Char */\n", "body .dl { color: #e6db74 } /* Literal.String.Delimiter */\n", "body .sd { color: #e6db74 } /* Literal.String.Doc */\n", "body .s2 { color: #e6db74 } /* Literal.String.Double */\n", "body .se { color: #ae81ff } /* Literal.String.Escape */\n", "body .sh { color: #e6db74 } /* Literal.String.Heredoc */\n", "body .si { color: #e6db74 } /* Literal.String.Interpol */\n", "body .sx { color: #e6db74 } /* Literal.String.Other */\n", "body .sr { color: #e6db74 } /* Literal.String.Regex */\n", "body .s1 { color: #e6db74 } /* Literal.String.Single */\n", "body .ss { color: #e6db74 } /* Literal.String.Symbol */\n", "body .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */\n", "body .fm { color: #a6e22e } /* Name.Function.Magic */\n", "body .vc { color: #f8f8f2 } /* Name.Variable.Class */\n", "body .vg { color: #f8f8f2 } /* Name.Variable.Global */\n", "body .vi { color: #f8f8f2 } /* Name.Variable.Instance */\n", "body .vm { color: #f8f8f2 } /* Name.Variable.Magic */\n", "body .il { color: #ae81ff } /* Literal.Number.Integer.Long */\n", "\n", "  </style>\n", "</head>\n", "<body>\n", "<h2></h2>\n", "\n", "<div class=\"highlight\"><pre><span></span><span class=\"linenos\"> 1</span><span class=\"gh\">diff --git a/django/db/models/fields/__init__.py b/django/db/models/fields/__init__.py</span>\n", "<span class=\"linenos\"> 2</span><span class=\"gd\">--- a/django/db/models/fields/__init__.py</span>\n", "<span class=\"linenos\"> 3</span><span class=\"gi\">+++ b/django/db/models/fields/__init__.py</span>\n", "<span class=\"linenos\"> 4</span><span class=\"gu\">@@ -2325,6 +2325,10 @@ def deconstruct(self):</span>\n", "<span class=\"linenos\"> 5</span><span class=\"w\"> </span>    def get_internal_type(self):\n", "<span class=\"linenos\"> 6</span><span class=\"w\"> </span>        return &quot;UUIDField&quot;\n", "<span class=\"linenos\"> 7</span><span class=\"w\"> </span>\n", "<span class=\"linenos\"> 8</span><span class=\"gi\">+    def get_prep_value(self, value):</span>\n", "<span class=\"linenos\"> 9</span><span class=\"gi\">+        value = super().get_prep_value(value)</span>\n", "<span class=\"linenos\">10</span><span class=\"gi\">+        return self.to_python(value)</span>\n", "<span class=\"linenos\">11</span><span class=\"gi\">+</span>\n", "<span class=\"linenos\">12</span><span class=\"w\"> </span>    def get_db_prep_value(self, value, connection, prepared=False):\n", "<span class=\"linenos\">13</span><span class=\"w\"> </span>        if value is None:\n", "<span class=\"linenos\">14</span><span class=\"w\"> </span>            return None\n", "</pre></div>\n", "</body>\n", "</html>\n", "\n", "Test patch: \n", "--------------------------------------------------------------------------------\n", "\n", "<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01//EN\"\n", "   \"http://www.w3.org/TR/html4/strict.dtd\">\n", "<!--\n", "generated by Pygments <https://pygments.org/>\n", "Copyright 2006-2024 by the Pygments team.\n", "Licensed under the BSD license, see LICENSE for details.\n", "-->\n", "<html>\n", "<head>\n", "  <title></title>\n", "  <meta http-equiv=\"content-type\" content=\"text/html; charset=None\">\n", "  <style type=\"text/css\">\n", "/*\n", "generated by Pygments <https://pygments.org/>\n", "Copyright 2006-2024 by the Pygments team.\n", "Licensed under the BSD license, see LICENSE for details.\n", "*/\n", "pre { line-height: 125%; }\n", "td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }\n", "td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }\n", "body .hll { background-color: #49483e }\n", "body { background: #272822; color: #f8f8f2 }\n", "body .c { color: #959077 } /* Comment */\n", "body .err { color: #ed007e; background-color: #1e0010 } /* Error */\n", "body .esc { color: #f8f8f2 } /* Escape */\n", "body .g { color: #f8f8f2 } /* Generic */\n", "body .k { color: #66d9ef } /* Keyword */\n", "body .l { color: #ae81ff } /* Literal */\n", "body .n { color: #f8f8f2 } /* Name */\n", "body .o { color: #ff4689 } /* Operator */\n", "body .x { color: #f8f8f2 } /* Other */\n", "body .p { color: #f8f8f2 } /* Punctuation */\n", "body .ch { color: #959077 } /* Comment.Hashbang */\n", "body .cm { color: #959077 } /* Comment.Multiline */\n", "body .cp { color: #959077 } /* Comment.Preproc */\n", "body .cpf { color: #959077 } /* Comment.PreprocFile */\n", "body .c1 { color: #959077 } /* Comment.Single */\n", "body .cs { color: #959077 } /* Comment.Special */\n", "body .gd { color: #ff4689 } /* Generic.Deleted */\n", "body .ge { color: #f8f8f2; font-style: italic } /* Generic.Emph */\n", "body .ges { color: #f8f8f2; font-weight: bold; font-style: italic } /* Generic.EmphStrong */\n", "body .gr { color: #f8f8f2 } /* Generic.Error */\n", "body .gh { color: #f8f8f2 } /* Generic.Heading */\n", "body .gi { color: #a6e22e } /* Generic.Inserted */\n", "body .go { color: #66d9ef } /* Generic.Output */\n", "body .gp { color: #ff4689; font-weight: bold } /* Generic.Prompt */\n", "body .gs { color: #f8f8f2; font-weight: bold } /* Generic.Strong */\n", "body .gu { color: #959077 } /* Generic.Subheading */\n", "body .gt { color: #f8f8f2 } /* Generic.Traceback */\n", "body .kc { color: #66d9ef } /* Keyword.Constant */\n", "body .kd { color: #66d9ef } /* Keyword.Declaration */\n", "body .kn { color: #ff4689 } /* Keyword.Namespace */\n", "body .kp { color: #66d9ef } /* Keyword.Pseudo */\n", "body .kr { color: #66d9ef } /* Keyword.Reserved */\n", "body .kt { color: #66d9ef } /* Keyword.Type */\n", "body .ld { color: #e6db74 } /* Literal.Date */\n", "body .m { color: #ae81ff } /* Literal.Number */\n", "body .s { color: #e6db74 } /* Literal.String */\n", "body .na { color: #a6e22e } /* Name.Attribute */\n", "body .nb { color: #f8f8f2 } /* Name.Builtin */\n", "body .nc { color: #a6e22e } /* Name.Class */\n", "body .no { color: #66d9ef } /* Name.Constant */\n", "body .nd { color: #a6e22e } /* Name.Decorator */\n", "body .ni { color: #f8f8f2 } /* Name.Entity */\n", "body .ne { color: #a6e22e } /* Name.Exception */\n", "body .nf { color: #a6e22e } /* Name.Function */\n", "body .nl { color: #f8f8f2 } /* Name.Label */\n", "body .nn { color: #f8f8f2 } /* Name.Namespace */\n", "body .nx { color: #a6e22e } /* Name.Other */\n", "body .py { color: #f8f8f2 } /* Name.Property */\n", "body .nt { color: #ff4689 } /* Name.Tag */\n", "body .nv { color: #f8f8f2 } /* Name.Variable */\n", "body .ow { color: #ff4689 } /* Operator.Word */\n", "body .pm { color: #f8f8f2 } /* Punctuation.Marker */\n", "body .w { color: #f8f8f2 } /* Text.Whitespace */\n", "body .mb { color: #ae81ff } /* Literal.Number.Bin */\n", "body .mf { color: #ae81ff } /* Literal.Number.Float */\n", "body .mh { color: #ae81ff } /* Literal.Number.Hex */\n", "body .mi { color: #ae81ff } /* Literal.Number.Integer */\n", "body .mo { color: #ae81ff } /* Literal.Number.Oct */\n", "body .sa { color: #e6db74 } /* Literal.String.Affix */\n", "body .sb { color: #e6db74 } /* Literal.String.Backtick */\n", "body .sc { color: #e6db74 } /* Literal.String.Char */\n", "body .dl { color: #e6db74 } /* Literal.String.Delimiter */\n", "body .sd { color: #e6db74 } /* Literal.String.Doc */\n", "body .s2 { color: #e6db74 } /* Literal.String.Double */\n", "body .se { color: #ae81ff } /* Literal.String.Escape */\n", "body .sh { color: #e6db74 } /* Literal.String.Heredoc */\n", "body .si { color: #e6db74 } /* Literal.String.Interpol */\n", "body .sx { color: #e6db74 } /* Literal.String.Other */\n", "body .sr { color: #e6db74 } /* Literal.String.Regex */\n", "body .s1 { color: #e6db74 } /* Literal.String.Single */\n", "body .ss { color: #e6db74 } /* Literal.String.Symbol */\n", "body .bp { color: #f8f8f2 } /* Name.Builtin.Pseudo */\n", "body .fm { color: #a6e22e } /* Name.Function.Magic */\n", "body .vc { color: #f8f8f2 } /* Name.Variable.Class */\n", "body .vg { color: #f8f8f2 } /* Name.Variable.Global */\n", "body .vi { color: #f8f8f2 } /* Name.Variable.Instance */\n", "body .vm { color: #f8f8f2 } /* Name.Variable.Magic */\n", "body .il { color: #ae81ff } /* Literal.Number.Integer.Long */\n", "\n", "  </style>\n", "</head>\n", "<body>\n", "<h2></h2>\n", "\n", "<div class=\"highlight\"><pre><span></span><span class=\"linenos\"> 1</span><span class=\"gh\">diff --git a/tests/prefetch_related/models.py b/tests/prefetch_related/models.py</span>\n", "<span class=\"linenos\"> 2</span><span class=\"gd\">--- a/tests/prefetch_related/models.py</span>\n", "<span class=\"linenos\"> 3</span><span class=\"gi\">+++ b/tests/prefetch_related/models.py</span>\n", "<span class=\"linenos\"> 4</span><span class=\"gu\">@@ -172,6 +172,11 @@ def __str__(self):</span>\n", "<span class=\"linenos\"> 5</span><span class=\"w\"> </span>        return self.tag\n", "<span class=\"linenos\"> 6</span><span class=\"w\"> </span>\n", "<span class=\"linenos\"> 7</span><span class=\"w\"> </span>\n", "<span class=\"linenos\"> 8</span><span class=\"gi\">+class Article(models.Model):</span>\n", "<span class=\"linenos\"> 9</span><span class=\"gi\">+    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)</span>\n", "<span class=\"linenos\">10</span><span class=\"gi\">+    name = models.CharField(max_length=20)</span>\n", "<span class=\"linenos\">11</span><span class=\"gi\">+</span>\n", "<span class=\"linenos\">12</span><span class=\"gi\">+</span>\n", "<span class=\"linenos\">13</span><span class=\"w\"> </span>class Bookmark(models.Model):\n", "<span class=\"linenos\">14</span><span class=\"w\"> </span>    url = models.URLField()\n", "<span class=\"linenos\">15</span><span class=\"w\"> </span>    tags = GenericRelation(TaggedItem, related_query_name=&#39;bookmarks&#39;)\n", "<span class=\"linenos\">16</span><span class=\"gu\">@@ -188,9 +193,12 @@ class Comment(models.Model):</span>\n", "<span class=\"linenos\">17</span><span class=\"w\"> </span>    comment = models.TextField()\n", "<span class=\"linenos\">18</span><span class=\"w\"> </span>\n", "<span class=\"linenos\">19</span><span class=\"w\"> </span>    # Content-object field\n", "<span class=\"linenos\">20</span><span class=\"gd\">-    content_type = models.ForeignKey(ContentType, models.CASCADE)</span>\n", "<span class=\"linenos\">21</span><span class=\"gi\">+    content_type = models.ForeignKey(ContentType, models.CASCADE, null=True)</span>\n", "<span class=\"linenos\">22</span><span class=\"w\"> </span>    object_pk = models.TextField()\n", "<span class=\"linenos\">23</span><span class=\"w\"> </span>    content_object = GenericForeignKey(ct_field=&quot;content_type&quot;, fk_field=&quot;object_pk&quot;)\n", "<span class=\"linenos\">24</span><span class=\"gi\">+    content_type_uuid = models.ForeignKey(ContentType, models.CASCADE, related_name=&#39;comments&#39;, null=True)</span>\n", "<span class=\"linenos\">25</span><span class=\"gi\">+    object_pk_uuid = models.TextField()</span>\n", "<span class=\"linenos\">26</span><span class=\"gi\">+    content_object_uuid = GenericForeignKey(ct_field=&#39;content_type_uuid&#39;, fk_field=&#39;object_pk_uuid&#39;)</span>\n", "<span class=\"linenos\">27</span><span class=\"w\"> </span>\n", "<span class=\"linenos\">28</span><span class=\"w\"> </span>    class Meta:\n", "<span class=\"linenos\">29</span><span class=\"w\"> </span>        ordering = [&#39;id&#39;]\n", "<span class=\"linenos\">30</span><span class=\"gh\">diff --git a/tests/prefetch_related/tests.py b/tests/prefetch_related/tests.py</span>\n", "<span class=\"linenos\">31</span><span class=\"gd\">--- a/tests/prefetch_related/tests.py</span>\n", "<span class=\"linenos\">32</span><span class=\"gi\">+++ b/tests/prefetch_related/tests.py</span>\n", "<span class=\"linenos\">33</span><span class=\"gu\">@@ -7,10 +7,10 @@</span>\n", "<span class=\"linenos\">34</span><span class=\"w\"> </span>from django.test.utils import CaptureQueriesContext\n", "<span class=\"linenos\">35</span><span class=\"w\"> </span>\n", "<span class=\"linenos\">36</span><span class=\"w\"> </span>from .models import (\n", "<span class=\"linenos\">37</span><span class=\"gd\">-    Author, Author<PERSON>, <PERSON><PERSON><PERSON><PERSON>, AuthorWithAge, Bio, Book, Bookmark,</span>\n", "<span class=\"linenos\">38</span><span class=\"gd\">-    BookReview, BookWithYear, Comment, Department, Employee, FavoriteAuthors,</span>\n", "<span class=\"linenos\">39</span><span class=\"gd\">-    House, LessonEntry, ModelIterableSubclass, Person, Qualification, Reader,</span>\n", "<span class=\"linenos\">40</span><span class=\"gd\">-    <PERSON>, TaggedI<PERSON>, Teacher, WordEntry,</span>\n", "<span class=\"linenos\">41</span><span class=\"gi\">+    Article, Author, Author2, <PERSON><PERSON><PERSON><PERSON>, AuthorWithAge, Bio, Book,</span>\n", "<span class=\"linenos\">42</span><span class=\"gi\">+    Bookmark, BookReview, BookWithYear, Comment, Department, Employee,</span>\n", "<span class=\"linenos\">43</span><span class=\"gi\">+    FavoriteAuthors, House, LessonEntry, ModelIterableSubclass, Person,</span>\n", "<span class=\"linenos\">44</span><span class=\"gi\">+    Qualification, Reader, Room, TaggedItem, Teacher, WordEntry,</span>\n", "<span class=\"linenos\">45</span><span class=\"w\"> </span>)\n", "<span class=\"linenos\">46</span><span class=\"w\"> </span>\n", "<span class=\"linenos\">47</span><span class=\"w\"> </span>\n", "<span class=\"linenos\">48</span><span class=\"gu\">@@ -885,6 +885,12 @@ def test_prefetch_GFK_nonint_pk(self):</span>\n", "<span class=\"linenos\">49</span><span class=\"w\"> </span>            qs = Comment.objects.prefetch_related(&#39;content_object&#39;)\n", "<span class=\"linenos\">50</span><span class=\"w\"> </span>            [c.content_object for c in qs]\n", "<span class=\"linenos\">51</span><span class=\"w\"> </span>\n", "<span class=\"linenos\">52</span><span class=\"gi\">+    def test_prefetch_GFK_uuid_pk(self):</span>\n", "<span class=\"linenos\">53</span><span class=\"gi\">+        article = Article.objects.create(name=&#39;Django&#39;)</span>\n", "<span class=\"linenos\">54</span><span class=\"gi\">+        Comment.objects.create(comment=&#39;awesome&#39;, content_object_uuid=article)</span>\n", "<span class=\"linenos\">55</span><span class=\"gi\">+        qs = Comment.objects.prefetch_related(&#39;content_object_uuid&#39;)</span>\n", "<span class=\"linenos\">56</span><span class=\"gi\">+        self.assertEqual([c.content_object_uuid for c in qs], [article])</span>\n", "<span class=\"linenos\">57</span><span class=\"gi\">+</span>\n", "<span class=\"linenos\">58</span><span class=\"w\"> </span>    def test_traverse_GFK(self):\n", "<span class=\"linenos\">59</span><span class=\"w\"> </span>        &quot;&quot;&quot;\n", "<span class=\"linenos\">60</span><span class=\"w\"> </span>        A &#39;content_object&#39; can be traversed with prefetch_related() and\n", "</pre></div>\n", "</body>\n", "</html>\n", "\n"]}], "source": ["ex = dataset[4]\n", "print(\n", "    f\"Metadata: repo={ex['repo']}, instance_id={ex['instance_id']}, base_commit={ex['base_commit']}\\n{'-' * 80}\"\n", ")\n", "problem_statement = ex[\"problem_statement\"]\n", "print(f\"Problem statement:\\n{problem_statement}\\n{'-' * 80}\\n\")\n", "print(f\"Gold patch: \\n{'-' * 80}\\n\")\n", "print(print_diff(ex[\"patch\"], \"html\"))\n", "\n", "print(f\"Test patch: \\n{'-' * 80}\\n\")\n", "print(print_diff(ex[\"test_patch\"], \"html\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}