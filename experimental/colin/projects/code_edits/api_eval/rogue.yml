system:
  experimental:
    remove_suffix: false
    retriever_top_k: 100
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1280
      max_prompt_tokens: 3816
      max_retrieved_chunk_tokens: -1
      max_suffix_tokens: 768
  name: basic_rag
  retriever:
    chunker:
      max_lines_per_chunk: 30
      name: line_level
    document_formatter:
      add_path: true
      max_tokens: 999
      name: ethanol6_document
    query_formatter:
      add_path: true
      max_tokens: 1023
      name: ethanol6_query
    scorer:
      checkpoint_path: ethanol/ethanol6-04.1
      name: ethanol

task:
  dataset: api_task_COLINS_CODEEDITS.dev
  exec: True
  hydra_block_resource_internet_access: true
  name: hydra

podspec: 1xH100.yaml

determined:
    metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
    name: droid_hydra_api_eval_rogue_baseline
    project: colin
    workspace: Dev
