{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from glob import glob\n", "import pandas as pd\n", "import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "import datetime\n", "import pytz\n", "import os\n", "from pathlib import Path\n", "import shutil\n", "# Specify the Pacific Time Zone (PST)\n", "pacific_timezone = pytz.timezone('US/Pacific')\n", "import zstandard\n", "import json\n", "import random\n", "from pathlib import Path"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading Data to Annotate\n", "\n", "Only run one of the below cells. They load data from two different tasks (Hydra, API). They both output to Hydra"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Examples from Hydra task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_dataset_dir = '/mnt/efs/augment/data/eval/hydra/datasets/all_languages_2-3lines_medium_to_hard.v1.0/'\n", "full_df = None\n", "for jsonl_fp in glob(f'{old_dataset_dir}*/*_patches.jsonl'):\n", "    new_df = pd.read_json(jsonl_fp, lines=True)\n", "    new_df['path'] = '/'.join(jsonl_fp.split('/')[-2:])\n", "    if full_df is None:\n", "        full_df = new_df\n", "    else:\n", "        full_df = pd.concat([full_df, new_df])\n", "        full_df.reset_index(drop=True, inplace=True)\n", "\n", "full_df['patch_content'] = full_df.apply(lambda row: row[\"file_content\"][row['char_start']:row['char_end']], axis=1)\n", "full_df[\"buggy_version\"] = full_df[\"patch_content\"]\n", "full_df[\"instruction\"] = \"(unedited)\"\n", "full_df = full_df.sample(frac=1).reset_index(drop=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Examples from API Task"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from shortuuid import uuid\n", "\n", "old_dataset_dir = '/mnt/efs/augment/data/eval/hydra/datasets/FineGrained/FinePython.large.v1.0/'\n", "\n", "\n", "repo_to_dir = {\n", "    \"google/pyglove\": '/mnt/efs/augment/data/eval/hydra/datasets/FineGrained/FinePython.large.v1.0/google/',\n", "    \"pydantic/pydantic\": '/mnt/efs/augment/data/eval/hydra/datasets/FineGrained/FinePython.large.v1.0/pydantic/'\n", "}\n", "repo_rename = {\n", "    'pyglove': 'google/pyglove',\n", "    'pydantic': 'pydantic/pydantic',\n", "}\n", "\n", "commit_shas = {\n", "    'google/pyglove': 'e10d48137075fb23c322ff51598b542e132f9ceb',\n", "    'pydantic/pydantic': '26f0eab8ed0aef014db5b86767d3b52b091f0af4',\n", "}\n", "\n", "dir_for_codebases = Path('/mnt/efs/augment/data/eval/hydra/repos')\n", "\n", "examples = []\n", "for fp in glob(f'{old_dataset_dir}**/*_patches.jsonl.zst'):\n", "    \n", "    with open(fp, 'rb') as compressed:\n", "        dctx = zstandard.ZstdDecompressor()\n", "        \n", "        # Decompress the file\n", "        with dctx.stream_reader(compressed) as reader:\n", "            decompressed_content = reader.read()\n", "            lines = decompressed_content.decode().split('\\n')\n", "\n", "            examples += [json.loads(str(line)) for line in lines if line]\n", "\n", "# Reformat examples from MiniPatch to Patch\n", "for example in examples:\n", "    example['repository'] = repo_rename[example['repository']]\n", "    filepath = dir_for_codebases / example['repository'] / example['filename']\n", "    with open(filepath, 'r') as f:\n", "        code = f.read()\n", "        example['file_content'] = code\n", "        example['file_name'] = example['filename']\n", "        del example['filename']\n", "        example['char_end'] = example['char_stop']\n", "        del example['char_stop']\n", "        example['patch_content'] = code[example['char_start']:example['char_end']]\n", "        example['patch_id'] = f\"{example['repository']}/{uuid()}\"\n", "        example['commit_sha'] = commit_shas[example['repository']]\n", "        example['_extra'] = {}\n", "        example['path'] = str(Path(fp).relative_to('/mnt/efs/augment/data/eval/hydra/datasets/FineGrained/FinePython.large.v1.0'))\n", "        example[\"buggy_version\"] = example[\"patch_content\"]\n", "        example[\"instruction\"] = \"(unedited)\"\n", "        del example['sub_categories']\n", "        del example['category']\n", "        del example['language']\n", "\n", "        required_keys = set([\n", "            'file_content',\n", "            'char_start',\n", "            'char_end',\n", "            'patch_content',\n", "            'patch_id',\n", "            'repository',\n", "            'commit_sha',\n", "            'file_name',\n", "            '_extra',\n", "            'path',\n", "            \"buggy_version\",\n", "            \"instruction\"\n", "        ])\n", "\n", "        assert set(example.keys()) == required_keys, set(example.keys()).symmetric_difference(required_keys)\n", "\n", "full_df = pd.DataFrame(examples)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Annotation Code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["full_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def viz_example_at_index(index):\n", "    row = full_df.iloc[index]\n", "    file_content, char_start, char_end = row[\"file_content\"], row[\"char_start\"], row[\"char_end\"]\n", "    prefix = file_content[(char_start-250):char_start]\n", "    suffix = file_content[char_end:char_end+250]\n", "    is_edited = row[\"buggy_version\"] != row[\"patch_content\"]\n", "    ctx = widgets.HTML(value=f\"\"\"\n", "<b>Data (is edited? {is_edited}):\n", "</b> \n", "\n", "<pre>{prefix}\n", "</pre>\n", "<pre style=\"color: red\">{row[\"patch_content\"]}\n", "</pre>\n", "<pre>{suffix}\n", "</pre>\n", "\n", "<hr>\n", "\"\"\", layout=widgets.Layout(width=\"50%\"))\n", "    annotation_text1 = widgets.Textarea(\n", "        value=row[\"buggy_version\"],\n", "        placeholder=row[\"buggy_version\"],\n", "        description='Annotation:',\n", "        disabled=False,\n", "        layout=widgets.Layout(height=\"400px\", width=\"100%\")\n", "    )\n", "    annotation_instruction = widgets.Textarea(\n", "        value=row['instruction'],\n", "        placeholder=row['instruction'],\n", "        description='Instruction:',\n", "        disabled=False,\n", "        layout=widgets.Layout(height=\"50px\", width=\"100%\")\n", "    )\n", "    save_button = widgets.Button(description='Save', layout=widgets.Layout(width=\"100%\"))\n", "    def save(b):\n", "        full_df.at[index, 'instruction'] = annotation_instruction.value\n", "        full_df.at[index, 'buggy_version'] = annotation_text1.value\n", "        # Get the current time in the Pacific Time Zone\n", "        current_time_pst = datetime.datetime.now(pacific_timezone)\n", "        # Format the current time as a string (optional)\n", "        current_time_pst_str = current_time_pst.strftime('%Y-%m-%d %H:%M:%S %Z')\n", "        save_button.description = f\"Save (last save at: {current_time_pst_str})\"\n", "\n", "    save_button.on_click(save)\n", "\n", "    display(widgets.HBox([\n", "        ctx, \n", "        widgets.VBox([annotation_text1, annotation_instruction, save_button], layout=widgets.Layout(width=\"50%\"))\n", "    ]))\n", "    \n", "    display(widgets.HTML(value=f'<hr><hr>'))\n", "\n", "examples = list(full_df.iterrows())\n", "index = 0\n", "\n", "# Create widgets for displaying the current item and buttons\n", "item_display = widgets.Label(value=f\"Example index: {examples[index][0]} (out of {len(examples)})\")\n", "prev_button = widgets.Button(description='Previous')\n", "next_button = widgets.Button(description='Next')\n", "    \n", "# Function to update the displayed item\n", "def update_item(index):\n", "    item_display.value = f\"Example index: {examples[index][0]} (out of {len(examples)})\"\n", "    clear_output()\n", "    display(item_display)\n", "    display(widgets.HBox([prev_button, next_button]))\n", "    viz_example_at_index(examples[index][0])\n", "\n", "\n", "# Function to handle the \"Previous\" button click\n", "def prev_button_click(b):\n", "    global index\n", "    if index > 0:\n", "        index -= 1\n", "        update_item(index)\n", "\n", "# Function to handle the \"Next\" button click\n", "def next_button_click(b):\n", "    global index\n", "    if index < len(examples) - 1:\n", "        index += 1\n", "        update_item(index)\n", "\n", "# Attach button click handlers\n", "prev_button.on_click(prev_button_click)\n", "next_button.on_click(next_button_click)\n", "\n", "# Display the widgets\n", "display(item_display)\n", "display(widgets.HBox([prev_button, next_button]))\n", "viz_example_at_index(examples[0][0])\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Save results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_df = full_df[full_df[\"patch_content\"] != full_df[\"buggy_version\"]]\n", "filtered_df = filtered_df[filtered_df['instruction'] != '(unedited)']\n", "filtered_df['_extra'] = filtered_df.apply(lambda row: {'instruction': row['instruction'], 'buggy_version': row['buggy_version']}, axis=1)\n", "print(len(filtered_df))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Target directory to save to\n", "new_dataset_dir = '/mnt/efs/augment/data/eval/hydra/datasets/api_task_COLINS_CODEEDITS.dev/'\n", "\n", "# Format data\n", "filtered_df = full_df[full_df[\"patch_content\"] != full_df[\"buggy_version\"]]\n", "filtered_df = filtered_df[filtered_df['instruction'] != '(unedited)']\n", "filtered_df['_extra'] = filtered_df.apply(lambda row: {'instruction': row['instruction'], 'buggy_version': row['buggy_version']}, axis=1)\n", "print(len(filtered_df))\n", "\n", "# Save\n", "for path, group_df in filtered_df.groupby(\"path\"):\n", "    new_path = new_dataset_dir + path\n", "    if '.zst' in new_path:\n", "        new_path = new_path.replace('.zst', '')\n", "    os.makedirs(Path(new_path).parent, exist_ok=True)\n", "    group_df.to_json(new_path, orient='records', lines=True)\n", "\n", "    old_retrieval_db_path = (old_dataset_dir + path).replace(\"_patches.jsonl\", \"_retrieval_db.jsonl\")\n", "    new_retrieval_db_path = (new_dataset_dir + path).replace(\"_patches.jsonl\", \"_retrieval_db.jsonl\")\n", "    shutil.copyfile(old_retrieval_db_path, new_retrieval_db_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Confirm saved results look good"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json \n", "\n", "# path = '/mnt/efs/augment/data/eval/hydra/datasets/repoeval_functions_new.dev/google/lightweight_mmm_patches.jsonl'\n", "path = '/mnt/efs/augment/data/eval/hydra/datasets/api_task_COLINS_CODEEDITS.dev/google/pyglove_patches.jsonl'\n", "\n", "with open(path) as f:\n", "    data = [json.loads(line) for line in f]\n", "\n", "print(data[0]['_extra']['buggy_version'])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}