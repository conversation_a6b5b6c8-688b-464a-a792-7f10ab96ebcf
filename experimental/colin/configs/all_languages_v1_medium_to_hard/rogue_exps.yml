system:
  name: basic_rag
  model:
    name: rogue
    checkpoint_path: null
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: null
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
  experimental:
    retriever_top_k: 250
    trim_on_dedent: false
    trim_on_max_lines: null
    use_fim_when_possible: true

task:
  dataset: all_languages_2-3lines_medium_to_hard.dev
  name: hydra
  hydra_block_resource_internet_access: True

podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: null
  project: colin
  workspace: Dev

interventions:
  experiment_name: rogue_on_all_languages_2-3lines_medium_to_hard.dev

  sweep_generative_models:
    keys:
      - system.model.checkpoint_path
    values:
      - [/mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken]
      - [/mnt/efs/augment/checkpoints/rogue/diffb1m_7b_alphal_fixtoken]
      - [/mnt/efs/augment/checkpoints/rogue/diffb1m_16b_alphal_fixtoken]

  sweep_retrievers:
    keys:
      - system.retriever.name
    values:
      - [null]
      - [bm25]
      - [contrieve_350m]
      - [diff_boykin]
