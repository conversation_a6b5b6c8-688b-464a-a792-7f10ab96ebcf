system:
  name: rag_with_reranker
  model:
    name: rogue
    checkpoint_path: /mnt/efs/augment/checkpoints/rogue/diffb1m_7b_alphal_fixtoken
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  experimental:
    retriever_top_k: 1000
    trim_on_dedent: false
    trim_on_max_lines: null
    remove_suffix: False
  reranker:
    name: oracle_perplexity_reranker
    batchsize: 32
    top_k: 5
    model:
      name: rogue
      checkpoint_path: /mnt/efs/augment/checkpoints/rogue/diffb1m_1b_alphal_fixtoken
      prompt:
        max_prefix_tokens: 1024
        max_suffix_tokens: 1024
        max_retrieved_chunk_tokens: -1
        max_prompt_tokens: 3816
  retriever:
    chunker: line_level
    max_chunk: 40
    max_query_lines: 10
    name: diff_boykin

task:
  dataset: repoeval_functions
  name: hydra

podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - 7b generative model, 1b reranker, 32 bsz
  project: <PERSON><PERSON>
  workspace: Dev
