system:
  name: basic_rag
  model:
    checkpoint_path: /mnt/efs/augment/checkpoints/rogue/diffb1m_7b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_prompt_tokens: 3816
      max_suffix_tokens: 1024
  experimental:
    remove_suffix: false
    retriever_top_k: 250
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: diff_boykin
    chunker:
      name: line_level
      max_lines_per_chunk: 40
    query_formatter:
      name: simple_query
      max_lines: 20

task:
  dataset: all_languages_2-3lines_medium_to_hard.v1.0
  hydra_block_resource_internet_access: true
  name: hydra

podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: With retrieval query as prefix
  project: colin
  workspace: Dev
