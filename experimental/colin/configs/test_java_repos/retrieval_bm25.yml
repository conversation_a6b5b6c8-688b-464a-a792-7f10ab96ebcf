systems:
- name: basic_rag
  model:
    name: starcoderbase
    prompt:
      always_fim_style: true
      max_prefix_tokens: 2048
      max_prompt_tokens: 7912
      max_suffix_tokens: 2048
      retrieval_layout_style: comment2
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: bm25
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
  experimental:
    retriever_top_k: 50
    trim_on_dedent: false
    trim_on_max_lines: null
    remove_suffix: False

tasks:
- dataset: all_languages_2-3lines.dev
  repos: ["jaegertracing/jaeger", "ethereum/go-ethereum", "seata/seata"]
  # dataset: java_repos.dev
  # repos: ["seata/seata"]
  name: hydra
  hydra_block_resource_internet_access: True

podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - StarcoderBase, 2048 prefix, 2048 suffix, 7912 prompt, Bm25, comment2 layout (language-specific), no trimming
  project: Eval
  workspace: Dev
