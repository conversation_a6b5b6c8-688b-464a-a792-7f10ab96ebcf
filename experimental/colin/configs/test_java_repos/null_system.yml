systems:
  - name: basic
    model:
      name: "null"
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 128

tasks:
  - name: hydra
    # dataset: java_repos_test.dev
    dataset: java_repos.dev
    hydra_block_resource_internet_access: True
    repos: ["seata/seata"]


podspec: gpu-small.yaml

determined:
  name: Hydra - test, java_repos.dev, null test
  workspace: Dev
  project: playground
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
