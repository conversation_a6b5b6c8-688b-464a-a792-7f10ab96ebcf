systems:
- name: basic_rag
  model:
    name: starcoderbase
    checkpoint_path: arun/starcoderfima-16b-largebatch-mp1
    prompt:
      always_fim_style: true
      max_prefix_tokens: 2048
      max_prompt_tokens: 7912
      max_suffix_tokens: 2048
      retrieval_layout_style: generic
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: null
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
  experimental:
    retriever_top_k: 50
    trim_on_dedent: false
    trim_on_max_lines: null
    remove_suffix: False

tasks:
- dataset: all_languages_2-3lines.dev
  repos: ["jaegertracing/jaeger", "ethereum/go-ethereum", "seata/seata"]
  #dataset: java_repos.dev
  #repos: ["seata/seata"]
  name: hydra
  hydra_block_resource_internet_access: True

podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: <PERSON><PERSON><PERSON> (Seata repo) - StarcoderFIM-alignedv1, 2048 prefix, 2048 suffix, no retrieval, no trimming
  project: Eval
  workspace: Dev
