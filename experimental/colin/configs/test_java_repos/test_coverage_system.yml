systems:
  - name: gold_system
    injected_code_path: augment/experimental/colin/configs/test_java_repos/raise_exception.txt

tasks:
  - name: hydra
    dataset: java_repos.dev
    hydra_block_resource_internet_access: True
    repos: ["seata/seata"]

podspec: A40.yaml

determined:
  name: Hydra - test, java_repos.dev, seata-only, raise exception
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
