name: comparison_system
systems:
  - name: basic_rag
    descriptive_name: bm25 model
    model:
      name: rogue
      checkpoint_path: rogue/diffb1m_7b_alphal_fimv2
      prompt:
        max_prefix_tokens: 1024
        max_suffix_tokens: 1024
        max_retrieved_chunk_tokens: -1
        max_prompt_tokens: 3816
    generation_options:
      max_generated_tokens: 280
      temperature: 0
      top_k: 0
      top_p: 0
    experimental:
      retriever_top_k: 1000
      trim_on_dedent: false
      trim_on_max_lines: null
      remove_suffix: False
    retriever:
      scorer: 
        name: bm25
      chunker: 
        name: line_level
        max_lines_per_chunk: 40
      query_formatter:
        name: simple_query
        max_lines: 10
  - name: basic_rag
    descriptive_name: diff boykin model
    model:
      name: rogue
      checkpoint_path: rogue/diffb1m_7b_alphal_fimv2
      prompt:
        max_prefix_tokens: 1024
        max_suffix_tokens: 1024
        max_retrieved_chunk_tokens: -1
        max_prompt_tokens: 3816
    generation_options:
      max_generated_tokens: 280
      temperature: 0
      top_k: 0
      top_p: 0
    experimental:
      retriever_top_k: 1000
      trim_on_dedent: false
      trim_on_max_lines: null
      remove_suffix: False
    retriever:
      scorer: 
        name: diff_boykin
      chunker: 
        name: line_level
        max_lines_per_chunk: 40
      query_formatter:
        name: simple_query
        max_lines: 10
