includes:
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/lr/4e-5.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/contrastive/init_scale_-4.yml

determined:
  name: contrastive_small # trial 3329 uses name "contrastive"
  description: null
  workspace: Dev
  project: colin

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
overrides:
  data-path: /mnt/efs/augment/data/processed/the-stack-dense-retrieval/diff_dense_retriever_bigfix_640seqlen/dense_retrieval
  seq-length: 640
  use_wandb: False
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 4
  train-iters:
  train_batch_size: 1024
  warmup: 0.0
  load: /mnt/efs/augment/checkpoints/codegen-350M-multi
