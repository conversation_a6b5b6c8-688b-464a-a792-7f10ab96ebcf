determined:
  name: "20240118-rag-droid-colin"
  description: "null"
  workspace: Dev
  project: colin  ## SET YOUR PROJECT (probably)

augment:
  podspec_path: "4xH100.yaml"
  gpu_count: 4

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  learning_rate: 3e-6
  max_iters: 0
  max_epochs: 1
  decay_lr: False
  warmup_iters: 0
  weight_decay: 0.1
  batch_size: 1
  gradient_accumulation_steps: 1
  eval_interval: 10
  eval_iters: 128
  block_size: 16384
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/
  train_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-38/train
  eval_data_path: /mnt/efs/augment/user/igor/data/droid/droid-repo-38/validation
  out_dir: /mnt/efs/augment/user/colin/det/code_edits/20240118-droid-114
  wandb_log: True
  wandb_project: colin-droid
  wandb_run_name: 20240118-rag-droid-colin

  #use_activation_checkpointing: False
  #checkpoint_optimizer_state: True
