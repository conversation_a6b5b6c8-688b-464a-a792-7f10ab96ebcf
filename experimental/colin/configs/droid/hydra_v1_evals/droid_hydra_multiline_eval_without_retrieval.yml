system:
  name: droid_code_edit
  override_selected_code: "# TODO: add missing logic.\n"
  override_instruction: "implement missing logic"
  model:
    name: fastforward_droid
    checkpoint_path: "/mnt/efs/augment/user/yuri/checkpoint_llama_iteration_252_no_dropout_jan_16_ffw"
    prompt:
      max_prefix_tokens: 1536
      max_suffix_tokens: 1024
      version: 8
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024

task:
  name: hydra
  dataset: repoeval_2-3lines
  exec: True
  hydra_block_resource_internet_access: True

podspec: 1xH100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: droid_hydra_multiline_eval_without_retrieval
  project: colin
  workspace: Dev
