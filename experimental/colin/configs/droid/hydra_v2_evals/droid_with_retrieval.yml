system:
  name: droid_repo_code_edit
  grab_instruction_and_selected_code_from_extra: True
  model:
    name: fastforward_droid_repo
    # older droid-repo ckpt
    # checkpoint_path: "/mnt/efs/augment/user/colin/code_edits/droid_repo839"
    checkpoint_path: "/mnt/efs/augment/user/colin/code_edits/droidrepo121-iter839"
    prompt:
      max_prefix_tokens: 1536
      max_suffix_tokens: 1024
      max_output_tokens: 4096
      max_instruction_tokens: 512
      max_tokens: 16384  # 8192
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 1024
  retriever:
    scorer:
      name: ethanol
      checkpoint_path: ethanol/ethanol6-16.1
    chunker:
      name: line_level
      max_lines_per_chunk: 30
    query_formatter:
      name: ethanol6_query
      max_tokens: 1023
      add_path: true
      add_suffix: true
    document_formatter:
      name: ethanol6_document
      max_tokens: 999
      add_path: true

task:
  name: hydra
  dataset: repoeval_functions_new.dev
  exec: True
  hydra_block_resource_internet_access: True

podspec: 1xH100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: droid_hydra_functionv2_eval_with_retrieval_16k
  project: colin
  workspace: Dev
