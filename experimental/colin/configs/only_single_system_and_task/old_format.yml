systems:
- name: basic_rag
  model:
    name: codegen-16b-indiana
    prompt:
      max_prefix_tokens: 442
      max_suffix_tokens: 442
      max_prompt_tokens: 1768
      fill_to_context_window: true
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  retriever:
    name: null
    chunker: line_level
    max_chunk: 20
    max_query_lines: 6
  experimental:
    remove_suffix: False
    retriever_top_k: 1000
    trim_on_dedent: true
    trim_on_max_lines: null
tasks:
- name: hydra
  dataset: repoeval_functions
  limit: 10
podspec: A40.yaml
determined:
  name: old format test
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
