system:
  name: basic_rag
  model:
    name: starcoderbase_7b
    prompt:
      always_fim_style: true
      max_prefix_tokens: 2048
      max_prompt_tokens: 7912
      max_suffix_tokens: 2048
      retrieval_layout_style: comment2
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: contrieve_350m
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
  experimental:
    retriever_top_k: 250
    trim_on_dedent: false
    trim_on_max_lines: null
    use_fim_when_possible: true

task:
  dataset: all_languages_2-4lines_medium_to_hard
  repos: ["airbnb/epoxy"]
  name: hydra
  hydra_block_resource_internet_access: True

podspec: 1xA100.yaml

determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra -  all_languages_2-4lines_medium_to_hard, contrieve350m retrieval, 7b
  project: Eval
  workspace: Dev
