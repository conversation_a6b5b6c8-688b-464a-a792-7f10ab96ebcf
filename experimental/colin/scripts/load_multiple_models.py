"""<PERSON><PERSON><PERSON> to confirm we can load multiple retrievers.

Run with following command:
CUDA_VISIBLE_DEVICES=0 CUDA_LAUNCH_BLOCKING=1 python experimental/colin/scripts/load_multiple_models.py

"""
# pylint: disable=all
# from megatron.tokenizer.tokenizer import CodeGenTokenizer

import research.models.all_models as research_models
import research.retrieval.tests.data.patchcore_test_data as patchcore_test_data
from research.retrieval.chunking_functions import (
    LineLevelChunker,
    ScopeAwareChunker,
)
from research.retrieval.file_filterer import basic_file_filterer
from research.retrieval.retrieval_database import RetrievalDatabase
from research.retrieval.scorers.dense_scorer import DenseRetrievalScorer
from research.retrieval.scorers.good_enough_bm25_scorer import (
    GoodEnoughBM25Scorer,
)
from research.retrieval.types import Document

checkpoints_root = "/mnt/efs/augment/checkpoints"

generative_models = {
    "indiana_16B_retrieve10": {
        "model": lambda: research_models.CodeGen_16B_Indiana(
            checkpoints_root, retrieval_top_k=10
        ),
    },
    "codegen_16B_retrieve10": {
        "model": lambda: research_models.CodeGen_16B_Multi(
            checkpoints_root, retrieval_top_k=10
        ),
    },
    "starcoder_16B_retrieve10": {
        "model": lambda: research_models.StarCoder_Model(
            checkpoints_root, retrieval_top_k=10
        ),
    },
}

dense_retrievers = {
    "diff_retriever_v1": {
        "yaml_files": [
            "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml",
            "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml",
            "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml",
        ],
        "overwrite_values": {
            "load": "/mnt/efs/augment/checkpoints/diffs-retriever-v1-2023-06-26",
        },
    },
    "same_file": {
        "yaml_files": [
            "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml",
            "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml",
            "/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml",
        ],
        "overwrite_values": {
            "load": "/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09",
        },
    },
}


def load_generative_model(generative_model_name: str) -> research_models.GPTNeoXModel:
    """Load the generative model."""
    model_data = generative_models[generative_model_name]
    generative_model = model_data["model"]()
    generative_model.load()

    return generative_model


def load_retrieval_models(chunker) -> dict[str, RetrievalDatabase]:
    """Returns loaded retrievers."""
    if chunker == "scope_aware":
        chunker = ScopeAwareChunker()
    elif chunker == "line_level":
        chunker = LineLevelChunker(max_lines_per_chunk=20)

    # Load retrieval models
    bm25_scorer = GoodEnoughBM25Scorer()
    dense_scorers = {
        name: DenseRetrievalScorer(
            retriever_data["yaml_files"], retriever_data["overwrite_values"]
        )
        for i, (name, retriever_data) in enumerate(dense_retrievers.items())
    }

    file_filterer = basic_file_filterer

    retriever_models = {
        "bm25": RetrievalDatabase(chunker, bm25_scorer, file_filterer),
        **{
            name: RetrievalDatabase(chunker, dense_scorer, file_filterer)
            for name, dense_scorer in dense_scorers.items()
        },
    }

    return retriever_models


docs = [
    Document(
        text=patchcore_test_data.COMMON_PY,
        id="common.py_uuid",
        path="common.py",
        meta={},
    ),
    Document(
        text=patchcore_test_data.METRICS_PY,
        id="metrics.py_uuid",
        path="common.py",
        meta={},
    ),
]
query3 = Document(
    text=patchcore_test_data.QUERY3,
    id="exact_match_uuid",
    path="test_common.py",
    meta={},
)

if __name__ == "__main__":
    generative_model = load_generative_model("indiana_16B_retrieve10")

    chunker = ScopeAwareChunker()
    retrieval_models = load_retrieval_models(chunker)

    for retriever_db_name, retriever_db in retrieval_models.items():
        retriever_db.add_docs(docs)

    for retriever_db_name, retriever_db in retrieval_models.items():
        generative_model.document_index = retriever_db

        print("-" * 80)
        print(f"Completion for retriever_db={retriever_db_name}")
        print("-" * 80)

        max_generated_tokens = generative_model.max_generated_toks

        retrieved_chunks = generative_model._get_retrieved_chunks(
            query3.text, "", True, None
        )
        print(f"Num retrieved chunks: {len(retrieved_chunks)}")

        prompt_toks = generative_model.prepare_prompt(
            max_generated_tokens, query3.text, "", retrieved_chunks, query3.path
        )

        prompt = generative_model.tokenizer.detokenize(prompt_toks).replace("\\n", "\n")
        print(f"Prompt: {prompt}")

        completion = generative_model._generate(prompt_toks, max_generated_tokens, 0)

        print(f"Completion: {completion}")
