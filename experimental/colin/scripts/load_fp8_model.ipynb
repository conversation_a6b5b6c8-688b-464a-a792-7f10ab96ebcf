{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id fb46acaf-8348-4c8a-b5b5-36a7483465d0.\n"]}], "source": ["from base.fastforward.starcoder.fwd_starcoder_fp8 import (\n", "    StarCoder,\n", "    StarcoderAttentionFactory,\n", "    generate_step_fn,\n", ")\n", "\n", "from base.fastforward.starcoder.model_specs import get_starcoder_model_spec\n", "from base.tokenizers import create_tokenizer_by_name\n", "from base.tokenizers.tokenizer import RetrievalSpecialTokens\n", "from research.models.embedding_models.fastforward import FastForwardEmbeddingModel\n", "\n", "spec = get_starcoder_model_spec(\n", "    \"starcoder-1b\", \n", "    \"/mnt/efs/augment/user/igor/checkpoints/chatanol/chatanol1-17-3.expand2-2.epoch3/neox/checkpoint_fp8/\", \n", "    \"7c914edd140bc54718b8a98e4761d94f6114694cd08f3574fd6cf24f551a71be\",\n", ")\n", "\n", "ffwd_model = generate_step_fn(\n", "    spec,\n", "    auto_capture_graphs=False,\n", ")\n", "assert isinstance(ffwd_model, StarCoder)\n", "\n", "embedding_dim = int(ffwd_model.final_projection.weight.shape[0])\n", "\n", "ffwd_model.cuda()\n", "ffwd_model.eval()\n", "ffwd_model.requires_grad_(False)\n", "\n", "attn_factory = StarcoderAttentionFactory(spec)\n", "tokenizer = create_tokenizer_by_name(\"starcoder\")\n", "assert isinstance(tokenizer.special_tokens, RetrievalSpecialTokens)\n", "\n", "query_model = FastForwardEmbeddingModel(\n", "    ffwd_model,\n", "    attn_factory,\n", "    embedding_token_id=tokenizer.special_tokens.end_of_query,\n", "    embedding_dim=embedding_dim,\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["seq = \"this is example seq\" * 10000\n", "seq_toks = tokenizer.tokenize_safe(seq)[:8191] + [tokenizer.special_tokens.end_of_query]\n", "\n", "out = query_model.embed_batch([seq_toks, seq_toks])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[23.2344,  1.4268,  1.2100,  ..., -1.3965, -0.7124, -0.7104],\n", "        [23.2344,  1.4268,  1.2100,  ..., -1.3965, -0.7124, -0.7104]],\n", "       device='cuda:0', dtype=torch.float16)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["out"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}