{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import json\n", "from research.core.utils_for_file import read_jsonl_zst, write_jsonl_zst\n", "\n", "raw_data = []\n", "\n", "\n", "with open('/home/<USER>/modal_data.jsonl') as f:\n", "    for line in f:\n", "        repo_items = json.loads(line)\n", "        raw_data.extend(repo_items)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["formatted_data = []\n", "for item in raw_data:\n", "    new_item = {\n", "        \"message\": item[\"question\"],\n", "        \"target\": \"\",\n", "        \"keywords\": item[\"expected_identifiers\"],\n", "        \"gold_paths\": item[\"paths\"],\n", "        \"repo\": item[\"repo\"].split('/')[1],\n", "    }\n", "    formatted_data.append(new_item)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["with open('/mnt/efs/augment/data/processed/modal_labs_v0/samples.json', 'w') as f:\n", "    json.dump(formatted_data, f, indent=2)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('django/django', '790f0f8868b0cde9a9bec1f0621efa53b00c87df')\n", "('pytorch/pytorch', '525fdc0f950c90d54cf69c1de5f10e741f43db76')\n", "('sympy/sympy', '583993a9b99faa3f244b1863feddf4c2480ac640')\n", "('calcom/cal.com', '3b186b19e6c0fbd1410d45dc8e610ca050b34b87')\n", "('jazzband/django-debug-toolbar', '9e30a06e418ecdd4eeb837530b86be40bb1e3d2d')\n"]}], "source": ["repos = []\n", "for item in raw_data:\n", "    repos.append((item[\"repo\"], item[\"ref\"]))\n", "for item in set(repos):\n", "    print(item)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# coresponding git paths:\n", "https://github.com/django/django.git \n", "https://github.com/pytorch/pytorch\n", "https://github.com/sympy/sympy\n", "https://github.com/calcom/cal.com\n", "https://github.com/jazzband/django-debug-toolbar\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Failed on file /home/<USER>/django-debug-toolbar/example/django-debug-toolbar.png with error 'utf-8' codec can't decode byte 0x89 in position 0: invalid start byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/example/example.db with error 'utf-8' codec can't decode byte 0xe2 in position 98: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/ru/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/ca/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/pl/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/zh_CN/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/cs/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/fr/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/sk/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/en/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/sv_SE/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/de/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/id/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/it/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/nl/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/fa/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/es/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/uk/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/pt_BR/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/he/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/pt/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/fi/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/debug_toolbar/locale/ja/LC_MESSAGES/django.mo with error 'utf-8' codec can't decode byte 0xde in position 0: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/.git/index with error 'utf-8' codec can't decode byte 0xd9 in position 11: invalid continuation byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/.git/objects/pack/pack-46eee5ec954d7f961d8bb947d215c0fcb6d77a24.pack with error 'utf-8' codec can't decode byte 0x97 in position 12: invalid start byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/.git/objects/pack/pack-46eee5ec954d7f961d8bb947d215c0fcb6d77a24.idx with error 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte. Skipping...\n", "Failed on file /home/<USER>/django-debug-toolbar/.git/objects/pack/pack-46eee5ec954d7f961d8bb947d215c0fcb6d77a24.rev with error 'utf-8' codec can't decode byte 0x90 in position 31: invalid start byte. Skipping...\n", "Processed 343 total docs, skipping 125 of them (36%)\n"]}], "source": ["# use convert_repository_to_documents to convert all repos to documents\n", "\n", "from research.retrieval.utils import convert_repository_to_documents\n", "\n", "repo_name = 'django-debug-toolbar'\n", "docs = convert_repository_to_documents(f'/home/<USER>/{repo_name}')\n", "processed_docs = []\n", "for doc in docs:\n", "    processed_docs.append({\n", "        'id': doc.id,\n", "        'text': doc.text,\n", "        'path': doc.path,\n", "        'meta': None\n", "    })\n", "\n", "write_jsonl_zst(f'/mnt/efs/augment/data/processed/modal_labs_v0/repos/{repo_name}_aug2024.jsonl.zst', processed_docs)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "data = read_jsonl_zst('/mnt/efs/augment/data/processed/augment_qa/v2/repos/augment_jul_10_2024_f5efe2e.jsonl.zst')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': '571b1517e1565aa8bc882337e0e9c030bcf5b6f694f6212574d305aff0920d21',\n", " 'text': 'models/inference/Release\\nmodels/inference/Debug\\nclients/vscode/node_modules\\nclients/vscode/webviews/node_modules\\nclients/common/webviews/node_modules\\nservices/auth/common/frontend/node_modules\\napi/node_modules\\nnode_modules\\nservices/customer/frontend/node_modules\\nservices/support/frontend/node_modules\\n.augment\\ntools/bazel_runner/web/frontend/node_modules\\ntools/deploy_runner/web/frontend/node_modules\\ntools/genie/frontend/node_modules\\ntools/bzl/lint/pyright/node_modules\\ntarget\\n',\n", " 'path': '.bazelignore',\n", " 'meta': None}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data[0]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "2.7.18"}}, "nbformat": 4, "nbformat_minor": 2}