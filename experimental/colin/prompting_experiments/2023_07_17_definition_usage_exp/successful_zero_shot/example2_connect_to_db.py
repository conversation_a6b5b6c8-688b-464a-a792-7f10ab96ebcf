def connect_to_database(url, credentials):
    """Connect to DB and return the connection.
​
    Args:
        url: Database URL
        credentials: A dict of {"user": username, "password": password}
    """
    return sqlite.connect(url, credentials)
​
def num_users(db_url, db_user, db_pass):
    """Returns the number of users in the database."""

    # Begin Copilot code (no prompt)
    conn = connect_to_database(db_url, {"user": db_user, "password": db_pass})
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM users")
    return cursor.fetchone()[0]
    # EndCopilot code (no prompt)


    # Begin StarCoerBase code (no prompt)
    # Connect to the database
    conn = connect_to_database(db_url, {"user": db_user, "password": db_pass})

    # Get the number of users
    cur = conn.cursor()
    cur.execute("SELECT COUNT(*) FROM users")
    num_users = cur.fetchone()[0]

    # Close the connection
    conn.close()

    return num_users
    # End StarCoderBase code (no prompt)

'''
Analysis:

Both Copilot and StarCoder are able to zer-shot infer how to create a
connection with connect_to_database.
'''
