def _get_client(args):
    if args.insecure:
        credentials = None
    else:
        if not args.ca_cert or not args.client_key or not args.client_cert:
            logging.error(
                "--ca-cert, --client-key, --client-cert need to be set unless --insecure is used"
            )
            sys.exit(1)
        credentials = grpc.ssl_channel_credentials(
            root_certificates=args.ca_cert.read_bytes(),
            private_key=args.client_key.read_bytes(),
            certificate_chain=args.client_cert.read_bytes(),
        )
    rpc_client = EmbedderClient.create_for_endpoint(
        args.endpoint, credentials=credentials
    )
    return rpc_client


def _calculate(rpc_client: EmbedderClient, args):
    input_tokens = [
        int(t.strip()) for t in args.input.split(args.separator) if t.strip()
    ]

    response = rpc_client.calculate_embedding([input_tokens])
    logging.info("Response Shape %s", response.shape)
    logging.info("Response %s", response)


def main():
    """Main function."""
    setup_console_logging()
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--endpoint",
        default="localhost:50051",
        help="Endpoint of the request insight service",
    )
    parser.add_argument(
        "--insecure",
        action="store_true",
        help="If true, the request will be in plaintext instead of a secure connection.",
    )
    parser.add_argument(
        "--ca-cert", type=pathlib.Path, help="Path to the ca certificate"
    )
    parser.add_argument(
        "--client-key", type=pathlib.Path, help="Path to the TLS client key"
    )
    parser.add_argument(
        "--client-cert", type=pathlib.Path, help="Path to the TLS client certificate"
    )

    subparsers = parser.add_subparsers()
    embed_parser = subparsers.add_parser("calculate")
    embed_parser.set_defaults(action="calculate")
    embed_parser.add_argument("input")
    embed_parser.add_argument("-s", "--separator", default=",")

    args = parser.parse_args()

    # Begin Copilot (zero-shot)
    rpc_client = _get_client(args)
    if args.action == "calculate":
        _calculate(rpc_client, args)
    else:
        raise ValueError("Unknown action: %s" % args.action)
    # End Copilot (zero-shot)

    # Begin StarCoderBase (zero-shot)
    rpc_client = _get_client(args)
    if args.action == "calculate":
        _calculate(rpc_client, args)
    # End StarCoderBase (zero-shot)

    # Begin StarCoderBase (few-shot with prompt1.py)
    rpc_client = _get_client(args)
    if args.action == "calculate":
        _calculate(rpc_client, args)
    # Begin StarCoderBase (few-shot with prompt1.py)

'''
Analysis:

Copilot and StarCoderBase (zero-shot and few-shot) pass.

I was impressed with StarCoderBase. This example requires that it
understand it needs a client to compute _calculate, and that it can
grab a client from _get_client. It also understands that it should only
call _calculate if the action is "calculate". 
'''