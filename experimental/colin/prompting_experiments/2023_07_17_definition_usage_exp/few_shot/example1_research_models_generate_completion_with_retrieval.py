
DocumentId=str

DEFAULT_TOP_K = 10

@dataclass
class Document:
    """Represents a named sequence of text for retrieval."""

    id: DocumentId
    text: str
    path: Optional[str] = None
    meta: dict = None

class Chunker(ABC):
    """Splits documents into chunks of text."""

    @abstractmethod
    def split_into_chunks(self, doc: Document) -> Optional[list[Chunk]]:
        """This function takes a document and returns a list of chunks.

        Returns None if splitting failed.
        """

class LineLevelChunker(Chunker):
    """Split a document into chunks based on line-level chunking."""
    def split_into_chunks(self, doc: Document) -> Optional[list[Chunk]]:

class ScopeAwareChunker(Chunker):
    """Split a document into chunks based on scope-aware chunking."""
    def split_into_chunks(self, doc: Document) -> Optional[list[Chunk]]:

class RetrievalDatabaseScorer(ABC):
    """This implements a scoring technique, which hooks into methods in RetrievalDatabase. It is mostly stateless, as RetrievalDatabase manages state."""

    @abstractmethod
    def add_doc(self, chunks: list[Chunk]) -> None:
        """Hook for adding a doc."""

    @abstractmethod
    def remove_doc(self, chunks: list[Chunk]) -> None:
        """Hook for removing a doc."""

    @abstractmethod
    def score(
        self,
        prefix: str,
        suffix: Optional[str] = None,
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Internal method to compute scores for a query, possibly unsorted."""

class DenseRetrievalScorer(RetrievalDatabaseScorer):
    """A dense scorer, to be used with RetrievalDatabase."""

class EnsembledRetrievalScorer(RetrievalDatabaseScorer):
    """This implements a scoring technique, which hooks into methods in RetrievalDatabase. It is mostly stateless, as RetrievalDatabase manages state."""

class DocumentIndex(ABC):
    """Abstract interface for exposing retrieval to client."""

    @abstractmethod
    def add_docs(self, docs: Iterable[Document]) -> None:
        """Add documents to index, and use name to reference."""

    @abstractmethod
    def remove_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Remove documents by name."""

    @abstractmethod
    def get_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Return the list of documents."""

    @abstractmethod
    def query(
        self,
        prefix: str,
        suffix: Optional[str] = None,
        query_meta: dict = None,
        doc_ids: Optional[Iterable[DocumentId]] = None,
        top_k: Optional[int] = None,
        max_prefix_tokens: Optional[int] = None,
        max_suffix_tokens: Optional[int] = None,
    ) -> tuple[list[Chunk], list[RetrievalScore]]:
        """Return list of document chunks for the given query."""

class ResearchModel:
    """A general research model interface."""
​
    @abstractmethod
    def load(self):
        """Load the model."""
​
    @abstractmethod
    def generate(
        self,
        prefix: str,
        suffix: str,
        temp: float = 0.,
        path=None,
        retrieved_chunks: Optional[list[Chunk]] = None
    ) -> str:
        """Generate a completion based on prefix and suffix, and maybe retrieved chunks."""
​
    @property
    @abstractmethod
    def tokenizer(self):
        """The tokenizer associated with this model.

        The tokenizer should have a encode(text) method that returns a
        list of integer token IDs, and a decode(tokens) method that
        returns the text string.
        """
​

class RetrievalDatabase(DocumentIndex):
    """This is a retrieval database that can be configured with different dependencies to enable retrieval experimentation."""

    def __init__(
        self,
        chunker: Chunker,
        scorer: RetrievalDatabaseScorer,
    ):
        self.documents: dict[DocumentId, DocumentRow] = {}
        self.chunker = chunker
        self.scorer = scorer

def generate_completion_with_retrieval_and_small_suffix(
        model: ResearchModel,
        retrieval_db: RetrievalDatabase,
        text: str,
        cursor_location: int,
        max_prefix_len = 15,
        max_suffix_len = 10,
        temp = 0.,
    ):
# -----------------------------------------------------------------------------------------------
    # Begin Copilot code (no prompt)
    """Generate a completion for the given prompt."""
    # Split the text into prefix and suffix.
    prefix = text[:cursor_location]
    suffix = text[cursor_location:]
    # Retrieve chunks.
    chunks, scores = retrieval_db.query(
        prefix,
        suffix,
        top_k=DEFAULT_TOP_K,
        max_prefix_tokens=max_prefix_len,
        max_suffix_tokens=max_suffix_len,
    )
    # Generate completion.
    completion = model.generate(
        prefix,
        suffix,
        temp=temp,
        retrieved_chunks=chunks,
    )
    return completion
    # End Copilot generation
# -----------------------------------------------------------------------------------------------
    # Begin StarCoderBase generation (no few-shot prompt)
    # Get the prefix and suffix
    prefix = text[:cursor_location]
    suffix = text[cursor_location:]
​
    # Get the chunks
    chunks = retrieval_db.chunker.split_into_chunks(Document(id=0, text=text))
​
    # Get the top-k retrieved chunks
    top_k_chunks, top_k_scores = retrieval_db.query(
        prefix,
        suffix,
        top_k=DEFAULT_TOP_K,
    )
​
    # Generate the completion
    completion = model.generate(
        prefix,
        suffix,
        temp=temp,
        retrieved_chunks=top_k_chunks,
    )
​
    # Return the completion
    return completion
    # End StarCoderBase generation (no few-shot prompt)
# -----------------------------------------------------------------------------------------------
    # Begin StarCoderBase generation (with few-shot prompt1.py)
    # get the prefix and suffix
    prefix = text[:cursor_location]
    suffix = text[cursor_location:]

    # get the chunks
    chunks = retrieval_db.query(
        prefix,
        suffix,
        top_k=DEFAULT_TOP_K,
        max_prefix_tokens=max_prefix_len,
        max_suffix_tokens=max_suffix_len,
    )[0]

    # generate the completion
    completion = model.generate(prefix, suffix, temp, retrieved_chunks=chunks)

    return completion
    # End StarCoderBase generation (with few-shot prompt1.py)

'''
Analysis:


The Copilot zero-shot generation looks good,
but the StarCoder zero-shot generation has problems:
- unnecessarily calls split_into_chunks
- does not pass max_prefix_tokens and max_suffix_tokens to retrieval_db.query

Notice that the few-shot prompt fixes both of these problems for StarCoder.
'''
