
DocumentId=str

DEFAULT_TOP_K = 10
APPROXIMATE_TOKENS_PER_LINE = 10

@dataclass
class Document:
    """Represents a named sequence of text for retrieval."""

    id: DocumentId
    text: str
    path: Optional[str] = None
    meta: dict = None

class Chunker(ABC):
    """Splits documents into chunks of text."""

    @abstractmethod
    def split_into_chunks(self, doc: Document) -> Optional[list[Chunk]]:
        """This function takes a document and returns a list of chunks.

        Returns None if splitting failed.
        """

class LineLevelChunker(Chunker):
    """Split a document into chunks based on line-level chunking."""
    def __init__(self, max_lines_per_chunk: int):
        self.max_lines_per_chunk = max_lines_per_chunk

class ScopeAwareChunker(Chunker):
    """Split a document into chunks based on scope-aware chunking."""
    def __init__(self, max_tokens_per_chunk: int):
        self.max_tokens_per_chunk = max_tokens_per_chunk

class RetrievalDatabaseScorer(ABC):
    """This implements a scoring technique, which hooks into methods in RetrievalDatabase. It is mostly stateless, as RetrievalDatabase manages state."""

    @abstractmethod
    def add_doc(self, chunks: list[Chunk]) -> None:
        """Hook for adding a doc."""

    @abstractmethod
    def remove_doc(self, chunks: list[Chunk]) -> None:
        """Hook for removing a doc."""

    @abstractmethod
    def score(
        self,
        prefix: str,
        suffix: Optional[str] = None,
    ) -> tuple[list[tuple[DocumentId, ChunkId]], list[RetrievalScore]]:
        """Internal method to compute scores for a query, possibly unsorted."""

class DenseRetrievalScorer(RetrievalDatabaseScorer):
    """A dense scorer, to be used with RetrievalDatabase."""

class EnsembledRetrievalScorer(RetrievalDatabaseScorer):
    """This implements a scoring technique, which hooks into methods in RetrievalDatabase. It is mostly stateless, as RetrievalDatabase manages state."""

class DocumentIndex(ABC):
    """Abstract interface for exposing retrieval to client."""

    @abstractmethod
    def add_docs(self, docs: Iterable[Document]) -> None:
        """Add documents to index, and use name to reference."""

    @abstractmethod
    def remove_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Remove documents by name."""

    @abstractmethod
    def get_docs(self, ids: Iterable[DocumentId]) -> list[Document]:
        """Return the list of documents."""

    @abstractmethod
    def query(
        self,
        prefix: str,
        suffix: Optional[str] = None,
        query_meta: dict = None,
        doc_ids_to_skip: Optional[Iterable[DocumentId]] = None,
        top_k: Optional[int] = None,
        max_prefix_tokens: Optional[int] = None,
        max_suffix_tokens: Optional[int] = None,
    ) -> tuple[list[Chunk], list[RetrievalScore]]:
        """Return list of document chunks for the given query."""

class ResearchModel:
    """A general research model interface."""
​
    @abstractmethod
    def load(self):
        """Load the model."""
​
    @abstractmethod
    def generate(
        self,
        prefix: str,
        suffix: str,
        temp: float = 0.,
        path=None,
        retrieved_chunks: Optional[list[Chunk]] = None
    ) -> str:
        """Generate a completion based on prefix and suffix, and maybe retrieved chunks."""
​
    @property
    @abstractmethod
    def tokenizer(self):
        """The tokenizer associated with this model.

        The tokenizer should have a encode(text) method that returns a
        list of integer token IDs, and a decode(tokens) method that
        returns the text string.
        """
​

class RetrievalDatabase(DocumentIndex):
    """This is a retrieval database that can be configured with different dependencies to enable retrieval experimentation."""

    def __init__(
        self,
        chunker: Chunker,
        scorer: RetrievalDatabaseScorer,
    ):
        self.documents: dict[DocumentId, DocumentRow] = {}
        self.chunker = chunker
        self.scorer = scorer

def capture_generations_for_all_chunkers_and_scorers(
    model: ResearchModel,
    prefix: str,
    suffix: str,
    docs: list[Document],
    doc_ids_to_skip: Optional[Iterable[DocumentId]] = None,
    temp: float = 0.,
    top_k: int = 10,
    max_lines_per_chunk: int = 20,
    max_prefix_tokens: Optional[int] = None,
    max_suffix_tokens: Optional[int] = None,
) -> dict[str, dict[str, str]]:
    """Capture generations retrieving with all different chunkers and scorers."""
    max_tokens_per_chunk = APPROXIMATE_TOKENS_PER_LINE * max_lines_per_chunk
    chunkers = {
        "line_level_chunker": LineLevelChunker(max_lines_per_chunk),
        "scope_aware_chunker": ScopeAwareChunker(max_tokens_per_chunk),
    }
    scorers = {
        "dense_scorer": DenseRetrievalScorer(),
        "ensembled_scorer": EnsembledRetrievalScorer(),
    }
    generations = {}

    for chunker in chunkers:
        for scorer in scorers:
            # Begin Copilot generation (zero-shot)
            retrieval_database = RetrievalDatabase(chunker, scorer)
            retrieval_database.add_docs(docs)
            generations[f"{chunker}_{scorer}"] = model.generate(
                prefix,
                suffix,
                temp=temp,
                retrieved_chunks=retrieval_database.query(
                    prefix,
                    suffix,
                    doc_ids_to_skip=doc_ids_to_skip,
                    top_k=top_k,
                    max_prefix_tokens=max_prefix_tokens,
                    max_suffix_tokens=max_suffix_tokens,
                )[0],
            )
            # End Copilot generation (zero-shot)

            # Begin StarCoderBase generation (few shot with prompt1.py)
            # Capture generations
            generations[chunker] = {}
            generations[chunker][scorer] = model.generate(
                prefix,
                suffix,
                temp=temp,
                path=None,
                retrieved_chunks=None,
            )
            # End StarCoderBase generation (few shot with prompt1.py)

    return generations

'''
Analysis:

This is a hard example, and neither model correctly solves it.

Copilot does not try to grab chunker and scorer objects from dictionaries. 
Rather, it passes their string names to RetrievalDatabase.

StarCoder's few-shot generation is much worse. It doesn't even try to instantiate a RetrievalDatabase.

'''
