def remove_duplicates(sorted_lst):
    """Remove duplicates in-place from a sorted list."""
    if not sorted_lst:
        return
    write_idx = 0
    for i in range(1, len(sorted_lst)):
        if sorted_lst[i] != sorted_lst[write_idx]:
            write_idx += 1
            sorted_lst[write_idx] = sorted_lst[i]
    del sorted_lst[write_idx+1:]

def instantiate_random_arr(valid_ints, max_len):
    """Randomly sample array of ints from specified range, without duplicates.

    Args:
        valid_ints: a list of ints to sample from.
        max_len: number of ints to sample

    Returns:
        a list of ints
    """
    # Begin Copilot (zero-shot)
    return random.sample(valid_ints, random.randint(0, max_len))
    # End Copilot

    # Begin StarCoderBase(zero-shot)
    arr = []
    for _ in range(max_len):
        arr.append(random.choice(valid_ints))
    return arr
    # End StarCoderBase (zero-shot)

    # Begin StarCoderBase (few-shot with prompt1.py)
    arr = []
    while len(arr) < max_len:
        arr.append(random.choice(valid_ints))
    remove_duplicates(arr)
    return arr
    # End StarCoderBase (few-shot with prompt1.py)

'''
Analysis:

In the zero-shot setting, both Copilot and StarCoderBase
fail to call remove_duplicates after sampling, even though the docstring specifies that
the output should be without duplicates.

In the few-shot setting, StarCoderBase correctly calls remove_duplicates after sampling,
'''
