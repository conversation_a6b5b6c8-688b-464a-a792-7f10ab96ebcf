class Bean(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255))
    bean_type = db.Column(db.SmallInteger)
    weight = db.Column(db.SmallInteger)
    purchase_date = db.Column(db.Date)
    notes = db.Column(db.Text)

    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('user.id'))

    brews = db.relationship('Brew', backref='bean', lazy='dynamic')
    roasts = db.relationship('Roast', backref='bean', lazy='dynamic')

    def bean_type_str(self):
        return BEAN_TYPES[self.bean_type]

    def remove_bean(self, commit=True):
        db.session.delete(self)
        if commit:
            db.session.commit()

    def get_aggregate_roast_stats(self):
        q = db.session.query(
            Roast,
            db.func.min(db.func.nullif(Roast.dry_end_time, 0)),
            db.func.max(db.func.nullif(Roast.dry_end_time, 0)),
            db.func.min(db.func.nullif(Roast.dry_end_temp, 0)),
            db.func.max(db.func.nullif(Roast.dry_end_temp, 0)),
            db.func.min(db.func.nullif(Roast.fc_begin_time, 0)),
            db.func.max(db.func.nullif(Roast.fc_begin_time, 0)),
            db.func.min(db.func.nullif(Roast.fc_begin_temp, 0)),
            db.func.max(db.func.nullif(Roast.fc_begin_temp, 0)),
            db.func.min(db.func.nullif(Roast.fc_end_time, 0)),
            db.func.max(db.func.nullif(Roast.fc_end_time, 0)),
            db.func.min(db.func.nullif(Roast.fc_end_temp, 0)),
            db.func.max(db.func.nullif(Roast.fc_end_temp, 0)),
            db.func.min(db.func.nullif(Roast.sc_begin_time, 0)),
            db.func.max(db.func.nullif(Roast.sc_begin_time, 0)),
            db.func.min(db.func.nullif(Roast.sc_begin_temp, 0)),
            db.func.max(db.func.nullif(Roast.sc_begin_temp, 0)),
            db.func.min(db.func.nullif(Roast.sc_end_time, 0)),
            db.func.max(db.func.nullif(Roast.sc_end_time, 0)),
            db.func.min(db.func.nullif(Roast.sc_end_temp, 0)),
            db.func.max(db.func.nullif(Roast.sc_end_temp, 0)),
            db.func.min(db.func.nullif(Roast.end_time, 0)),
            db.func.max(db.func.nullif(Roast.end_time, 0)),
            db.func.min(db.func.nullif(Roast.end_temp, 0)),
            db.func.max(db.func.nullif(Roast.end_temp, 0)),
            db.func.min(db.func.nullif(Roast.drop_temp, 0)),
            db.func.max(db.func.nullif(Roast.drop_temp, 0))
        )
        q = q.filter(Roast.bean_id == self.id).group_by(Roast.roaster_machine).all()
        return q

    @staticmethod
    def add_bean(bean, commit=True):
        db.session.add(bean)
        if commit:
            db.session.commit()


def add_bean_every_minute_and_log_all_stats():
    """Add a bean every minute and log stats for all beans."""

    # Begin Copilot (zero-shot)
    while True:
        bean = Bean(name='test', bean_type=1, weight=1, purchase_date=datetime.date.today(), notes='test')
        Bean.add_bean(bean)
        print('Added bean with id {}'.format(bean.id))
        beans = Bean.query.all()
        for bean in beans:
            print('Bean {} has {} roasts'.format(bean.id, bean.roasts.count()))
        time.sleep(60)
    # End Copilot (zero-shot)

    # Begin StarCoder (zero-shot)
    while True:
        try:
            add_bean()
            log_all_stats()
        except Exception as e:
            print(e)
        time.sleep(60)

def add_bean():
    """Add a bean to the database."""
    bean = Bean(
        name=random.choice(BEAN_NAMES),
        bean_type=random.randint(0, len(BEAN_TYPES) - 1),
        weight=random.randint(1, 100),
        purchase_date=datetime.date.today() - datetime.timedelta(days=random.randint(0, 365)),
        notes=random.choice(BEAN_NOTES)
    )
    Bean.add_bean(bean)


def log_all_stats():
    """Log stats for all beans."""
    for bean in Bean.query.all():
        log_bean_stats(bean)


def log_bean_stats(bean):
    """Log stats for a bean."""
    stats = bean.get_aggregate_roast_stats()
    for stat in stats:
        print(stat)
    # End StarCoder (zero-shot)

    # Begin StarCoder (few-shot with prompt1.py)
    while True:
        bean = Bean(name='Bean', bean_type=random.randint(0, 2), weight=random.randint(1, 100))
        Bean.add_bean(bean)
        time.sleep(60)
        for bean in Bean.query.all():
            print(bean.get_aggregate_roast_stats())
    # End StarCoder (few-shot with prompt1.py)

'''
Analysis:

This example is from https://github.com/BouncyNudibranch/bean-counter/blob/master/app/models.py.

- Copilot does not pass, as it ignores the get_aggregate_roast_stats method when
    logging stats.
- StarCoder's zero-shot generation does well, but it is not concise at all. It uses
    lots of helper functions unnecessarily. Nevertheless, it correctly understands
    how to construct a Bean, how to add it to the database with Bean.add_bean, and
    how to get logs with get_aggregate_roast_stats.
- StarCoder's few-shot generation does well! It understands how to construct a Bean,
    how to add it to the database with Bean.add_bean, and how to get logs with
    get_aggregate_roast_stats.


'''
