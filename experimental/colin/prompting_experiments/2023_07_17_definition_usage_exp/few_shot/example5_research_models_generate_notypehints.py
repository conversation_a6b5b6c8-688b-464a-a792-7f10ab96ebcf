class ResearchModel:
    """A general research model interface."""
​
    @abstractmethod
    def load(self):
        """Load the model."""
​
    @abstractmethod
    def generate(self, prefix_toks, suffix_toks, path=None, chunk_filter=None):
        """Generate a completion based on prefix and suffix."""
​
    @property
    @abstractmethod
    def tokenizer(self):
        """The tokenizer associated with this model.

        The tokenizer should have a encode(text) method that returns a
        list of integer token IDs, and a decode(tokens) method that
        returns the text string.
        """
​
​
def generate_completion(model, prefix):
    """Generate a completion for the given prefix."""

    # Begin Copilot (zero-shot)
    prefix_toks = model.tokenizer.encode(prefix)
    suffix_toks = model.generate(prefix_toks, None)
    return model.tokenizer.decode(suffix_toks)
    # End Copilot (zero-shot)

    # Begin StarCoder (zero-shot)
    # Encode the prefix.
    prefix_ids = model.tokenizer.encode(prefix)
​
    # Generate a completion.
    completion_ids = model.generate(prefix_ids)
​
    # Decode the completion and return it.
    return model.tokenizer.decode(completion_ids)
    # End StarCoder (zero-shot)

    # Begin StarCoder (few-shot with prompt1.py)
    prefix_toks = model.tokenizer.encode(prefix)
    suffix_toks = model.generate(prefix_toks)
    return model.tokenizer.decode(prefix_toks + suffix_toks)
    # End StarCoder (few-shot with prompt1.py)

'''
Analysis:

This is a straightforward example, with the twist that all type hints are removed.
- Copilot reasonably passes.
- Zero-shot StarcoderBase is close, but it doesn't pass an empty suffix to model.generate.
- Adding prompt1.py does not improve the StarcoderBase result.

'''
