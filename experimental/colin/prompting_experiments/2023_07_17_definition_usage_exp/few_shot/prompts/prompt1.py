# Examples of high quality, 100% valid Python code

# [[[Example]]]
​
import dataclasses
from typing import List
​
@dataclasses.dataclass
class Person:
    """Information about a person."""
    name: str
    age: int
    parents: List['Person'] = dataclasses.field(default_factory=list)
​
​
def get_ancestors(person: Person) -> list:
    """Returns a person's list of ancestors."""
    ret = []
    for parent in person.parents:
        ret.append(parent)
        ret.extend(get_ancestors(parent))
    return ret
​
​
def get_common_ancestor(person1, person2):
    """Returns the common ancestor of two people."""
    # Find the ancestors of each person
    ancestors1 = get_ancestors(person1)
    ancestors2 = get_ancestors(person2)
    # Find the lowest common ancestor
    lowest_common_ancestor = None
    for ancestor1 in ancestors1:
        for ancestor2 in ancestors2:
            if ancestor1 == ancestor2:
                lowest_common_ancestor = ancestor1
                break
        if lowest_common_ancestor:
            break
    return lowest_common_ancestor

# [[[Example]]]

class Timestamp(int):
    """Used to represent message times.

    Timestamps are always stored internally as centiseconds. Methods are
    provided to convert to/from centiseconds and seconds.

    Timestamp subclasses int, so __repr__ and json serialization comes for
    free.
    """

    __slots__ = ()

    @classmethod
    def from_centis(cls, x: int):
        return Timestamp(x)

    @classmethod
    def from_seconds(cls, x: Union[int, float]):
        return Timestamp(int(x * 100))

    @classmethod
    def now(cls):
        # returns system time since 1970-01-01 UTC, which are the timestamps
        # used by webdip
        return cls.from_seconds(datetime.now(timezone.utc).timestamp())

    def to_centis(self) -> int:
        return int(self)

    def to_seconds_float(self) -> float:
        return float(self / 100)

    def to_seconds_int(self) -> int:
        return self // 100

    def __add__(self, other: "Timestamp") -> "Timestamp":
        return Timestamp(self.to_centis() + other.to_centis())

    def __sub__(self, other: "Timestamp") -> "Timestamp":

@dataclasses.dataclass
class Payload:
    text: str
    header: int
    ts: Timestamp

def construct_payload(text, header, secs: int) -> Payload:
    return Payload(text, header, Timestamp.from_seconds(secs))

# [[[Example]]]

class CodeGenTokenizer(AbstractTokenizer):
    """Designed to be compatible with Salesforce CodeGen."""

    def __init__(self):
        name = "CodeGenTokenizerFast"
        super().__init__(name)

        self.tokenizer = self.create_custom_gpt2_tokenizer()

    def tokenize(self, text: str):
        return self.tokenizer.encode(text)

    def detokenize(self, token_ids):
        # clean_up_tokenization_spaces=False instructs the tokenizer to
        # not postprocess the detokenized string. By default, the tokenizer
        # will make replacements like " 's" -> "'s".
        return self.tokenizer.decode(token_ids, clean_up_tokenization_spaces=False)

def truncate_prefix_in_token_length(prefix: str, tokenizer: CodeGenTokenizer, max_len: int) -> str:
    prefix_toks = tokenizer.tokenize(prefix)
    truncated_prefix_toks = prefix_toks[-max_len:]
    truncated_prefix = tokenizer.detokenize(truncated_prefix_toks)
    return truncated_prefix

# [[[Example]]]

def qsort(arr):
    """Quicksort the given array."""
    if len(arr) <= 1:
        return arr
    else:
        return (qsort([x for x in arr[1:] if x < arr[0]])
            + [arr[0]]
            + qsort([x for x in arr[1:] if x >= arr[0]]))
​
​
def merge_sorted_lists(arr1, arr2):
    """Merge two sorted lists."""
    if len(arr1) == 0:
        return arr2
    elif len(arr2) == 0:
        return arr1
    elif arr1[0] < arr2[0]:
        return [arr1[0]] + merge_sorted_lists(arr1[1:], arr2)
    else:
        return [arr2[0]] + merge_sorted_lists(arr1, arr2[1:])
​
​
def merge_sort(arr):
    """Implement merge-sort."""
    if len(arr) <= 1:
        return arr
    else:
        mid = len(arr) // 2
        left = merge_sort(arr[:mid])
        right = merge_sort(arr[mid:])
        return merge_sorted_lists(left, right)
​
​
def test_merge_sorted_lists():
    arr1 = [1, 3, 5, 7]
    arr2 = [2, 4, 6, 8]
    merged_arr = merge_sorted_lists(arr1, arr2)
    assert merged_arr == [1, 2, 3, 4, 5, 6, 7, 8]
​
​
def test_qsort():
    arr = [1, 4, 2, 6, 9]
    sorted_arr = qsort(arr)
    assert sorted_arr == [1, 2, 4, 6, 9]

# [[[Example]]]
​
def plot_histogram(df: pandas.DataFrame, col: str, bins=20):
    """Plot a histogram of the data in the column of the given pandas DataFrame."""
    plt.hist(df[col], bins=bins)
​
​
data = [1, 4, 11, 109, 32, 131, 123, 231, 529]
plot_histogram(pandas.DataFrame(data, columns=['data']), 'data', bins=2)

# [[[Example]]]

class Cursor(_cursor):
    """A cursor that keeps a list of column name -> index mappings__. """
    def __init__(self, *args, **kwargs):
        kwargs['row_factory'] = DictRow
        super().__init__(*args, **kwargs)
        self._prefetch = True

    def cursor(self, *args, **kwargs):
        """Return a new cursor."""
        return Cursor(*args, **kwargs)

    def execute(self, query: str, vars: Optional[tuple] = None) -> None:
        """Execute a query."""
        self.index = OrderedDict()
        self._query_executed = True
        return super().execute(query, vars)

    def fetchone(self):
        """Grab one output from last query."""
        if self._prefetch:
            res = super().fetchone()
        if self._query_executed:
            self._build_index()
        if not self._prefetch:
            res = super().fetchone()
        return res

def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs) -> Connection:
    """
    Create a new database connection.

    The connection parameters can be specified as a string:

        conn = psycopg2.connect("dbname=test user=postgres password=secret")
    """
    dsn = _ext.make_dsn(dsn, **kwargs)
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
    if cursor_factory is not None:
        conn.cursor_factory = cursor_factory

    return conn

def create_test_table_with_test_user():
    conn = connect("dbname=test user=postgres")
    cur = conn.cursor()
    cur.execute("CREATE TABLE test (id serial PRIMARY KEY, num integer, data varchar);")
    cur.execute("INSERT INTO test (num, data) VALUES (%s, %s)", (100, "abc'def"))
    cur.execute("SELECT * FROM test;")
    assert cur.fetchone() == (1, 100, "abc'def")
    conn.commit()
    cur.close()
    conn.close()

def iterate_over_book_chapters(conn: Connection) -> list[str]:
    cursor = conn.cursor()
    cursor.execute(
        "SELECT book_data.chapter_name, book_metadata.book_name FROM book_data"
        "RIGHT JOIN book_metadata on book_data.id = book_metadata.book_id;"
    )
    book_to_chapters = defaultdict(list)
    while (out:= cursor.fetchone()) is not None:
        book_to_chapters[out['book_name']].append(out['chapter_name'])
    cursor.close()

# [[[Example]]]
