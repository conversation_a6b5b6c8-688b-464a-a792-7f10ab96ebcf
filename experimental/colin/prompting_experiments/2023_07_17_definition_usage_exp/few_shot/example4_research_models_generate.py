class ResearchModel:
    """A general research model interface."""
​
    @abstractmethod
    def load(self):
        """Load the model."""
​
    @abstractmethod
    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:
        """Generate a completion based on prefix and suffix."""
​
    @property
    @abstractmethod
    def tokenizer(self):
        """The tokenizer associated with this model.

        The tokenizer should have a encode(text) method that returns a
        list of integer token IDs, and a decode(tokens) method that
        returns the text string.
        """
​
​
def generate_completion(model: ResearchModel, prefix: str) -> None:
    """Generate a completion for the given prefix."""

    # Begin Copilot (zero-shot)
    prefix_toks = model.tokenizer.encode(prefix)
    suffix_toks = model.generate(prefix_toks, [])
    suffix = model.tokenizer.decode(suffix_toks)
    print(f"{prefix} {suffix}")
    # End Copilot (zero-shot)

    # StarcoderBase (zero-shot)
    prefix_toks = model.tokenizer.encode(prefix)
    suffix_toks = model.generate(prefix_toks)
    completion = model.tokenizer.decode(suffix_toks)
    print(completion)
    # End StarcoderBase (zero-shot)

    # StarCoderBase (few-shot with prompt1.py)
    prefix_toks = model.tokenizer.encode(prefix)
    suffix_toks = model.generate(prefix_toks)
    completion = model.tokenizer.decode(prefix_toks + suffix_toks)
    print(completion)
    # End StarCoderBase (few-shot with prompt1.py)


'''
Analysis:

This is a straightforward example.
- Copilot passes.
- Zero-shot StarcoderBase is close, but it doesn't pass an empty suffix to model.generate.
- Adding prompt1.py does not improve the StarcoderBase result.

'''
