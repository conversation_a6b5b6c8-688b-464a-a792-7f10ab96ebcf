def kernel_ridge_regression(X, y, power, alpha, fit_intercept=True):
    X_orig = np.copy(X)
    X = np.copy(X)

    if power >= 2:
        for i in range(2, power):
            X_newpower = X_orig ** i
        
        X = np.concatenate((X, X_newpower), axis=-1)

    rdg = Ridge(alpha = alpha, fit_intercept = fit_intercept)
    rdg.fit(X, y)
    score = rdg.score(X,y)

    return score, rdg.coef_, rdg.intercept_

def find_optimal_hyperparameters(X, y):
    """Find optimal hyperparameters (powers, alphas, whether to use intercept) for kernel ridge regression, and return optimal model."""

    # Copilot (zero-shot)
    powers = [1, 2, 3, 4, 5]
    alphas = [0.01, 0.1, 1, 10, 100]
    intercepts = [True, False]

    best_score = -np.inf
    best_power = None
    best_alpha = None
    best_intercept = None

    for power in powers:
        for alpha in alphas:
            for intercept in intercepts:
                score, _, _ = kernel_ridge_regression(X, y, power, alpha, intercept)
                if score > best_score:
                    best_score = score
                    best_power = power
                    best_alpha = alpha
                    best_intercept = intercept

    return best_power, best_alpha, best_intercept
    # End Copilot (zero-shot)

    # StarCoderBase (zero-shot)
    powers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    alphas = [1e-15, 1e-10, 1e-8, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1, 1, 10]
    intercepts = [True, False]

    best_score = 0
    best_model = None
    best_power = None
    best_alpha = None
    best_intercept = None

    for power in powers:
        for alpha in alphas:
            for intercept in intercepts:
                score, coef, intercept = kernel_ridge_regression(X, y, power, alpha, intercept)
                if score > best_score:
                    best_score = score
                    best_model = coef
                    best_power = power
                    best_alpha = alpha
                    best_intercept = intercept

    return best_score, best_model, best_power, best_alpha, best_intercept
    # End StarCoderBase (zero-shot)


    # StarCoder (few-shot)
    powers = [1, 2, 3, 4, 5]
    alphas = [1e-1, 1e-2, 1e-3, 1e-4, 1e-5, 1e-6, 1e-7, 1e-8, 1e-9, 1e-10]
    fit_intercepts = [True, False]

    best_score = 0
    best_model = None

    for power in powers:
        for alpha in alphas:
            for fit_intercept in fit_intercepts:
                score, coef, intercept = kernel_ridge_regression(X, y, power, alpha, fit_intercept)
                if score > best_score:
                    best_score = score
                    best_model = (power, alpha, fit_intercept, coef, intercept)

    return best_model
    # End StarCoder (few-shot)

'''
Analysis:

Copilot (zero-shot) passes.

StarCoderBase (zero-shot) is close but has a bug. It overwrites the for-loop iterator `interface` 
with the output from kernel_ridge_regression. 

StarCoderBase (few-shot with prompt1.py) passes! It names the for-loop iterator `fit_intercept` to avoid
this name collision.
'''