{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import download_hydra\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from research.retrieval.file_filterer import basic_file_filterer\n", "from research.retrieval.scorers.good_enough_bm25_scorer import (\n", "    GoodEnoughBM25Scorer,\n", ")\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "\n", "_,_, repoeval_files_for_retrieval_by_repo = download_hydra(\"repoeval_functions\")\n", "_,_, all_languages_files_for_retrieval_by_repo = download_hydra(\"all_languages_2-3lines_medium_to_hard.v1.0\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For repo ('CarperAI', 'trlx'), num_docs=144, num_chunks=516\n", "For repo ('maxhumber', 'redframes'), num_docs=70, num_chunks=217\n", "For repo ('lucidrains', 'imagen-pytorch'), num_docs=34, num_chunks=374\n", "For repo ('leopard-ai', 'betty'), num_docs=208, num_chunks=1073\n", "For repo ('facebookresearch', 'omnivore'), num_docs=122, num_chunks=619\n", "For repo ('deepmind', 'tracr'), num_docs=73, num_chunks=475\n", "For repo ('amazon-science', 'patchcore-inspection'), num_docs=414, num_chunks=133\n", "For repo ('google', 'lightweight_mmm'), num_docs=69, num_chunks=498\n"]}], "source": ["for repo_name, docs in repoeval_files_for_retrieval_by_repo.items():\n", "    chunker = LineLevelChunker(max_lines_per_chunk=20)\n", "    bm25_scorer = GoodE<PERSON>ughBM25Scorer()\n", "    file_filter = basic_file_filterer\n", "    retrieval_db = RetrievalDatabase(chunker, bm25_scorer, file_filter)\n", "    retrieval_db.add_docs(docs)\n", "\n", "    num_chunks = 0\n", "    for doc in docs:\n", "        chunk_ids = retrieval_db.get_chunks_ids_in_doc(doc.id)\n", "        num_chunks += len(chunk_ids)\n", "\n", "    print(f'For repo {repo_name}, num_docs={len(docs)}, num_chunks={num_chunks}')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["For repo ('pallets', 'flask'), num_docs=265, num_chunks=909\n", "For repo ('jaegertracing', 'jaeger'), num_docs=1146, num_chunks=7144\n", "For repo ('mrdoob', 'three.js'), num_docs=24680, num_chunks=101227\n", "For repo ('caddyserver', 'caddy'), num_docs=430, num_chunks=3460\n", "For repo ('moment', 'luxon'), num_docs=21761, num_chunks=88572\n", "For repo ('pydantic', 'pydantic'), num_docs=17575, num_chunks=66707\n", "For repo ('spulec', 'freezegun'), num_docs=63, num_chunks=152\n", "For repo ('ethereum', 'go-ethereum'), num_docs=1830, num_chunks=19239\n", "For repo ('google', 'mobly'), num_docs=268, num_chunks=2901\n", "For repo ('seata', 'seata'), num_docs=8861, num_chunks=20151\n"]}], "source": ["for repo_name, docs in all_languages_files_for_retrieval_by_repo.items():\n", "    chunker = LineLevelChunker(max_lines_per_chunk=20)\n", "    bm25_scorer = GoodE<PERSON>ughBM25Scorer()\n", "    file_filter = basic_file_filterer\n", "    retrieval_db = RetrievalDatabase(chunker, bm25_scorer, file_filter)\n", "    retrieval_db.add_docs(docs)\n", "\n", "    num_chunks = 0\n", "    for doc in docs:\n", "        chunk_ids = retrieval_db.get_chunks_ids_in_doc(doc.id)\n", "        num_chunks += len(chunk_ids)\n", "\n", "    print(f'For repo {repo_name}, num_docs={len(docs)}, num_chunks={num_chunks}')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}