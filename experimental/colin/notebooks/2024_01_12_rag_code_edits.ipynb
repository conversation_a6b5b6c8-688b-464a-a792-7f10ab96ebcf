{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "from research.eval.harness.systems.RAG_code_edit_system import RAGCodeEditSystem\n", "from research.retrieval.utils import convert_repository_to_documents\n", "from research.core.model_input import ModelInput"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"/home/<USER>/src/augment/research/model_server/configs/edit_model_remote_with_retrieval.yaml\") as f:\n", "    config = yaml.safe_load(f)\n", "config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rag_system = RAGCodeEditSystem.from_yaml_config(config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rag_system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["docs = convert_repository_to_documents('/home/<USER>/src/augment/research')\n", "print('-'* 20 + 'STARTING EXAMPLE DOC' + '-' * 20)\n", "print(docs[4].text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rag_system.add_docs(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''\n", "@register_system(\"reranker\")\n", "class RerankerSystem(AbstractSystem):\n", "    \"\"\"Simple LM with single retriever.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        model: GenerativeLanguageModel,\n", "        retriever: DocumentIndex,\n", "        generation_options: GenerationOptions,\n", "        experimental_config: MiscRAGSystemConfig,\n", "        reranker_config,\n", "    ):\n", "        self.model = model\n", "        self.retriever = retriever\n", "        self.generation_options = generation_options\n", "        self.experimental_config = experimental_config\n", "        self.reranker_config = reranker_config\n", "        self.__loaded = False\n", "\n", "    def load(self):\n", "        if not self.__loaded:\n", "            self.model.load()\n", "            self.retriever.load()\n", "        self.__loaded = True\n", "\n", "    def unload(self):\n", "        if self.__loaded:\n", "            self.model.unload()\n", "            self.retriever.unload()\n", "            self.__loaded = False\n", "''',\n", "    suffix=\"\",\n", "    extra={\n", "        \"selected_code\": '''\n", "    def add_docs(self, src_files: list[Document]):\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Copy add_docs from the RagSystem\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''\n", "@register_system(\"reranker\")\n", "class RerankerSystem(AbstractSystem):\n", "    \"\"\"Simple LM with single retriever.\"\"\"\n", "\n", "\n", "''',\n", "    suffix='''\n", "        self.model = model\n", "        self.retriever = retriever\n", "        self.generation_options = generation_options\n", "        self.experimental_config = experimental_config\n", "        self.reranker_config = reranker_config\n", "        self.__loaded = False\n", "\n", "    def load(self):\n", "        if not self.__loaded:\n", "            self.model.load()\n", "            self.retriever.load()\n", "        self.__loaded = True\n", "\n", "    def unload(self):\n", "        if self.__loaded:\n", "            self.model.unload()\n", "            self.retriever.unload()\n", "            self.__loaded = False\n", "''',\n", "    extra={\n", "        \"selected_code\": '''\n", "    def __init__(\n", "        self,\n", "        model,\n", "        retriever,\n", "        generation_options,\n", "        experimental_config,\n", "        reranker_config,\n", "    ):\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Add type annotations\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''''',\n", "    suffix='''''',\n", "    extra={\n", "        \"selected_code\": '''\n", "@register_system(\"reranker\")\n", "class RerankerSystem(AbstractSystem):\n", "    \"\"\"Simple LM with single retriever.\"\"\"\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Implement class\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''\n", "formatter = prompt_formatters.SimpleQueryFormatter(\n", "    max_tokens=max_tokens,\n", ")\n", "formatter.tokenizer = MockTokenizer()\n", "''',\n", "    suffix='''''',\n", "    extra={\n", "        \"selected_code\": '''\n", "got_tokens, _ = formatter.generate_prompt(\n", "    ModelInput(prefix=\"abcde\", suffix=None)\n", ")\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Fix the API usage.\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''\n", "@dataclass\n", "class MiscRAGCodeEditSystemConfig:\n", "''',\n", "    suffix='''\n", "    # Experimental config for stripping trailing newline from prompt. This is\n", "    # important for Starcoder models, where the newline is typically at the\n", "    # start of the vocab word for the tokenizer, and ending a prompt on a\n", "    # newline biases the model to produce an end-of-document token.\n", "    trim_trailing_newline_on_prefix: bool = False\n", "\n", "    # Deprecated. This has the same effect as \"not remove_suffix\", and is left\n", "    # here for backward compatibility with existing configs. When this is set,\n", "    # it will take precedence over \"remove_suffix\".\n", "    use_fim_when_possible: Optional[bool] = None\n", "''',\n", "    extra={\n", "        \"selected_code\": '''\n", "    remove_suffix: bool = False\n", "    trim_on_dedent: bool = True\n", "    retriever_top_k: int = 5\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Make these defaults more reasonable.\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''\n", "@dataclass\n", "class MiscRAGCodeEditSystemConfig:\n", "''',\n", "    suffix='''\n", "    \n", "''',\n", "    extra={\n", "        \"selected_code\": '''\n", "    remove_suffix: bool = True\n", "    trim_on_dedent: bool = True\n", "    trim_on_max_lines: Optional[int] = None\n", "    retriever_top_k: int = 25\n", "    trim_trailing_newline_on_prefix: bool = False\n", "    use_fim_when_possible: Optional[bool] = None\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Add comments describing these fields.\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model_input = ModelInput(\n", "    prefix='''\n", "@register_prompt_formatter(\"mixtral\")\n", "class PromptFormatterMixtral(AbstractPromptFormatter):\n", "    \"\"\"Prompter for RAG-finetuned  model.\"\"\"\n", "''',\n", "    suffix='''\n", "    \n", "''',\n", "    extra={\n", "        \"selected_code\": '''\n", "    # The config used to build the prompt.\n", "    max_prefix_tokens: Annotated[\n", "        int,\n", "        \"The maximum number of tokens for prefix, where -1 means infinity.\",\n", "    ] = -1\n", "    max_suffix_tokens: Annotated[\n", "        int,\n", "        \"The maximum number of tokens for suffix, where -1 means infinity.\",\n", "    ] = -1\n", "    retrieval_section_start: Annotated[\n", "        str, \"Place at the start of retrieved chunks\"\n", "    ]\n", "    retrieved_chunk_start: Annotated[\n", "        str, \"Start retrieved chunk with this token\"\n", "    ]\n", "    retrieved_chunk_body: Annotated[\n", "        str, \"Start retrieved chunk body with this token after filename\"\n", "    ]\n", "    prefix_body: Annotated[\n", "        str, \"Start prefix body with this token after filename\"\n", "    ]\n", "    max_retrieved_chunk_tokens: Annotated[\n", "        int, \"The maximum number of tokens for the retrieved chunks\"\n", "    ] = -1\n", "''',\n", "       \"lines_in_prefix_suffix\": 100,\n", "       \"instruction\": \"Add defaults special tokens that match my tokenizer.\"\n", "    }\n", ")\n", "\n", "rag_system.generate(model_input)[0]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}