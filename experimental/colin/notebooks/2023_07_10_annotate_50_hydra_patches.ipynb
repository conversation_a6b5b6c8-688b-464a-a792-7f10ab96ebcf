{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/opt/conda/lib/python3.9/site-packages/pandas/core/computation/expressions.py:20: UserWarning: Pandas requires version '2.7.3' or newer of 'numexpr' (version '2.7.2' currently installed).\n", "  from pandas.core.computation.check import NUMEXPR_INSTALLED\n"]}], "source": ["from pathlib import Path\n", "import json\n", "import pickle\n", "from pprint import pprint\n", "from research.eval import patch_lib\n", "from research.static_analysis.parsing import LineMap\n", "from collections import Counter\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "import pandas as pd\n", "import random\n", "import csv\n", "import numpy as np\n", "\n", "tokenizer = CodeGenTokenizer()\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "EXP NAME: dense_retriever_samefile\n"]}], "source": ["base_path = Path('/mnt/efs/augment/user/colin/scratch/')\n", "# project_name = 'evaluate_retrievers_2023_07_05'\n", "# project_name = 'evaluate_retrievers_2023_07_05_no_retriever'\n", "# project_name = 'evaluate_retrievers_2023_07_06'\n", "# project_name = 'evaluate_retrievers_2023_07_06_attempt3'\n", "# project_name = 'evaluate_indiana_retrievers_2023_07_07'\n", "project_name = 'evaluate_indiana_retrievers_2023_07_08_1024localctx_linelevelchunker'\n", "model_name = 'indiana_16B_retrieve10'\n", "#exp_names = [\n", "#    #'bm25', \n", "#    #'dense_retriever_diffs', \n", "#    'dense_retriever_samefile',\n", "#    #'no_retriever',\n", "#]\n", "exp_name = 'dense_retriever_samefile'\n", "ret_tok = tokenizer.vocab['<|ret-endofdoc|>']\n", "\n", "print('-' * 80)\n", "print(f'EXP NAME: {exp_name}')\n", "\n", "hydra_out_noretrieval_fp = base_path / 'evaluate_indiana_retrievers_2023_07_07' / 'no_retriever' / f'{model_name}_no_retriever.jsonl'\n", "hydra_out_fp = base_path / project_name / exp_name / f'{model_name}_{exp_name}.jsonl'\n", "completed_patches_fp = base_path / project_name / exp_name / f'{model_name}_{exp_name}_completed_patches.pkl'\n", "\n", "with open(hydra_out_fp, 'r') as f:\n", "    hydra_out = [json.loads(line) for line in f.readlines()]\n", "\n", "with open(hydra_out_noretrieval_fp, 'r') as f:\n", "    hydra_out_noretrieval = [json.loads(line) for line in f.readlines()]\n", "\n", "with open(completed_patches_fp, 'rb') as f:\n", "    completed_patches = pickle.load(f)\n", "\n", "all_patches =  [patch for patches in completed_patches.values() for patch in patches]\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["random.seed(10)\n", "shuffled_patches = sorted(all_patches, key=lambda k: random.random())\n", "\n", "column_names = tuple('prompt_id, repo_name, has_passed (with retrieval), has_passed (no retrieval), local_context_prompt + generation,  retrieval_prompt, generation'.split(', '))\n", "patch_csvs = [column_names]\n", "\n", "for i in range(50):\n", "    patch = patch_lib.Patch.from_json(shuffled_patches[i]['patch'])\n", "    repo_name = patch.repository\n", "    prompt_id = patch.patch_id\n", "    ground_truth = patch.original_patch_content\n", "    generation = patch.patch_content\n", "\n", "    prompt = tokenizer.detokenize(shuffled_patches[i]['prompt_toks'])\n", "    local_context_prompt = prompt[prompt.rindex('<|ret-endofdoc|>'):] + '# start ground truth\\n' + ground_truth + '# end ground truth\\n' \n", "    retrieval_prompt = prompt[:prompt.rindex('<|ret-endofdoc|>')]\n", "    \n", "    has_passed = [item['_extra']['result'] for item in hydra_out if item['patch_id'] == prompt_id]\n", "    has_passed_noretrieval = [item['_extra']['result'] for item in hydra_out_noretrieval if item['patch_id'] == prompt_id]\n", "\n", "    patch_csvs.append((prompt_id, repo_name, has_passed, has_passed_noretrieval, local_context_prompt, retrieval_prompt, generation))\n", "\n", "\n", "with open('/mnt/efs/augment/user/colin/scratch/50_hydra_generations.csv','w') as out:\n", "    csv_out=csv.writer(out, delimiter=',')\n", "    for row in patch_csvs:\n", "        csv_out.writerow(row)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['id', 'examples', 'schema', 'dataset', 'relations', 'name', 'annotators_per_example'])"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["with open('/mnt/efs/augment/user/colin/scratch/analyze_hydra_patches_annotations_first25annotations.json') as f:\n", "    annotations = json.load(f)\n", "\n", "annotations.keys()"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Num examples: 50\n", "Num annotated examples: 26\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>no_docstring</th>\n", "      <th>global_entity</th>\n", "      <th>local_entity_truncated</th>\n", "      <th>no_typing_of_inputs</th>\n", "      <th>niche_3rd_party_module</th>\n", "      <th>good_docstring</th>\n", "      <th>needs_suffix</th>\n", "      <th>kinda_docstring</th>\n", "      <th>array_shape_unknown</th>\n", "      <th>local_entity</th>\n", "      <th>setting_defaults</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "      <td>26.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.500000</td>\n", "      <td>3.576923</td>\n", "      <td>0.653846</td>\n", "      <td>0.576923</td>\n", "      <td>0.615385</td>\n", "      <td>0.423077</td>\n", "      <td>0.076923</td>\n", "      <td>0.192308</td>\n", "      <td>0.115385</td>\n", "      <td>0.615385</td>\n", "      <td>0.115385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.707107</td>\n", "      <td>3.214630</td>\n", "      <td>1.164210</td>\n", "      <td>0.503831</td>\n", "      <td>1.202561</td>\n", "      <td>0.577794</td>\n", "      <td>0.392232</td>\n", "      <td>0.401918</td>\n", "      <td>0.431455</td>\n", "      <td>0.982931</td>\n", "      <td>0.325813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.000000</td>\n", "      <td>0.250000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.000000</td>\n", "      <td>5.750000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.000000</td>\n", "      <td>11.000000</td>\n", "      <td>5.000000</td>\n", "      <td>1.000000</td>\n", "      <td>5.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>3.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       no_docstring  global_entity  local_entity_truncated   \n", "count     26.000000      26.000000               26.000000  \\\n", "mean       0.500000       3.576923                0.653846   \n", "std        0.707107       3.214630                1.164210   \n", "min        0.000000       0.000000                0.000000   \n", "25%        0.000000       0.250000                0.000000   \n", "50%        0.000000       3.000000                0.000000   \n", "75%        1.000000       5.750000                1.000000   \n", "max        3.000000      11.000000                5.000000   \n", "\n", "       no_typing_of_inputs  niche_3rd_party_module  good_docstring   \n", "count            26.000000               26.000000       26.000000  \\\n", "mean              0.576923                0.615385        0.423077   \n", "std               0.503831                1.202561        0.577794   \n", "min               0.000000                0.000000        0.000000   \n", "25%               0.000000                0.000000        0.000000   \n", "50%               1.000000                0.000000        0.000000   \n", "75%               1.000000                1.000000        1.000000   \n", "max               1.000000                5.000000        2.000000   \n", "\n", "       needs_suffix  kinda_docstring  array_shape_unknown  local_entity   \n", "count     26.000000        26.000000            26.000000     26.000000  \\\n", "mean       0.076923         0.192308             0.115385      0.615385   \n", "std        0.392232         0.401918             0.431455      0.982931   \n", "min        0.000000         0.000000             0.000000      0.000000   \n", "25%        0.000000         0.000000             0.000000      0.000000   \n", "50%        0.000000         0.000000             0.000000      0.000000   \n", "75%        0.000000         0.000000             0.000000      1.000000   \n", "max        2.000000         1.000000             2.000000      3.000000   \n", "\n", "       setting_defaults  \n", "count         26.000000  \n", "mean           0.115385  \n", "std            0.325813  \n", "min            0.000000  \n", "25%            0.000000  \n", "50%            0.000000  \n", "75%            0.000000  \n", "max            1.000000  "]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["from collections import defaultdict\n", "example_to_metadata = {example[\"example_id\"]: {} for example in annotations['examples']}\n", "\n", "print(f'Num examples: {len(annotations[\"examples\"])}')\n", "num_annotated_examples = 0\n", "\n", "tag_to_count_rows = []\n", "\n", "for example in annotations['examples']:\n", "    example_to_metadata[example[\"example_id\"]]['passed_with_retrieval'] = example['metadata'][\"has_passed (no retrieval)\"]\n", "    \n", "    tag_to_values = defaultdict(set)\n", "    for annotation in example['annotations']:\n", "        tag = annotation['tag']\n", "        annotated_text = annotation['value']\n", "        tag_to_values[tag].add(annotated_text)\n", "\n", "    tag_to_count = {tag: len(values) for tag, values in tag_to_values.items()}\n", "    if len(tag_to_count) > 0:\n", "        num_annotated_examples += 1\n", "        tag_to_count_rows.append({\"example_id\": example[\"example_id\"], **tag_to_count})\n", "\n", "\n", "print(f'Num annotated examples: {num_annotated_examples}')\n", "\n", "annotation_count_df = pd.DataFrame(tag_to_count_rows).replace(np. nan,0)\n", "\n", "annotation_count_df.describe()\n"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.34615384615384615"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["### Number of patches with truncated local context\n", "\n", "sum(annotation_count_df['local_entity_truncated'] > 0) / annotation_count_df['local_entity_truncated'].shape[0]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}