{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = '/mnt/efs/augment/eval/jobs/J9WMGU9H/000_gold_system_hydra.jsonl'\n", "\n", "with open(path) as f:\n", "    lines = f.readlines()\n", "    dicts = [json.loads(line) for line in lines]\n", "\n", "failures = [dct for dct in dicts if dct['_extra']['result'] != 'PASSED']\n", "for i in range(len(failures)):\n", "    if 'seata' not in failures[i]['repository']:\n", "        continue\n", "    print('ANOTHER' + ('='* 80))\n", "    print(''.join(str(failures[i]['_extra']['run_output']).splitlines(keepends=True)))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["failing_exception_res_fp = '/mnt/efs/augment/eval/jobs/YZbo6Ppd/000_gold_system_hydra.jsonl'\n", "null_res_fp = '/mnt/efs/augment/eval/jobs/U8RQg8xM/000_BASIC_hydra.jsonl'\n", "\n", "with open(failing_exception_res_fp) as f:\n", "    lines = f.readlines()\n", "    failing_exception_res = [json.loads(line) for line in lines]\n", "\n", "with open(null_res_fp) as f:\n", "    lines = f.readlines()\n", "    null_res = [json.loads(line) for line in lines]\n", "\n", "covered_patches = set([item['patch_id'] for item in failing_exception_res if item['_extra']['result'] != 'PASSED'])\n", "functional_patches = set([item['patch_id'] for item in null_res if item['_extra']['result'] != 'PASSED'])\n", "\n", "final_target_patch_ids = covered_patches.intersection(functional_patches)\n", "\n", "print(len(final_target_patch_ids))\n", "print(list(final_target_patch_ids)[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["patches_fp = '/mnt/efs/augment/data/eval/hydra/datasets/java_repos.dev/seata/seata_patches_unfiltered.jsonl'\n", "with open(patches_fp) as f:\n", "    patches = [json.loads(line) for line in f.readlines()]\n", "\n", "len_all_patches = len(patches)\n", "filtered_patches = [patch for patch in patches if patch['patch_id'] in final_target_patch_ids]\n", "len_filtered_patches = len(filtered_patches)\n", "\n", "print(f\"Went from {len_all_patches} patches to {len_filtered_patches} patches.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def should_filter_patch(patch) -> bool:\n", "    \"\"\"These are some seata-specific filters.\"\"\"\n", "    if 'Generated by the protocol buffer compiler.  DO NOT EDIT!' in patch['file_content']:\n", "        return True\n", "    elif '// Generated from E:' in patch['file_content']:\n", "        return True\n", "    elif 'Test' in patch['file_name']:\n", "        return True\n", "    elif 'Tests' in patch['file_name']:\n", "        return True\n", "    \n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_patches_fp = Path('/mnt/efs/augment/data/eval/hydra/datasets/java_repos.dev/seata/seata_patches.jsonl')\n", "assert not filtered_patches_fp.exists()\n", "\n", "with filtered_patches_fp.open(\"w\") as f:\n", "    for patch in filtered_patches:\n", "        if should_filter_patch(patch):\n", "            continue\n", "        \n", "        f.write(json.dumps(patch))\n", "        f.write('\\n')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_patches[0].keys()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["count = 0\n", "\n", "for patch in filtered_patches:\n", "    if should_filter_patch(patch):\n", "        count += 1\n", "    else:\n", "        print(colored('=' *10 + 'Next patch' + '=' *10 , 'blue'))\n", "        print(colored(patch['file_content'][(patch['char_start']-1000):patch['char_start']], 'red'))\n", "        print(colored(patch['file_content'][patch['char_start']:patch['char_end']], 'green'))\n", "        print(colored(patch['file_content'][patch['char_end']:(patch['char_end'] + 1000)], 'red'))\n", "\n", "print(f\"Found {count} patches to filter from {len(filtered_patches)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["last_n_lines_patches_fp = '/mnt/efs/augment/data/eval/hydra/datasets/java_last_2-3_lines-6-mincontext.dev/seata/seata_patches.jsonl'\n", "\n", "with open(last_n_lines_patches_fp) as f:\n", "    last_n_lines_patches = [json.loads(line) for line in f.readlines()]\n", "\n", "for patch in last_n_lines_patches:\n", "    if should_filter_patch(patch):\n", "        continue\n", "    print(colored('=' *10 + f\"Next patch: {patch['file_name']}\" + '=' *10 , 'blue'))\n", "    print(colored(patch['file_content'][(patch['char_start']-500):patch['char_start']], 'red'))\n", "    print(colored(patch['file_content'][patch['char_start']:patch['char_end']], 'green'))\n", "    print(colored(patch['file_content'][patch['char_end']:(patch['char_end'] + 500)], 'red'))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}