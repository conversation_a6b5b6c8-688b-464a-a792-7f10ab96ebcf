{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.research_models import (\n", "    CodeGen_16B_Indiana\n", ")\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.chunking_functions import ScopeAwareChunker\n", "from research.retrieval.file_filterer import basic_file_filterer\n", "from research.retrieval.scorers.dense_scorer import DenseRetrievalScorer\n", "from research.retrieval.scorers.good_enough_bm25_scorer import <PERSON><PERSON><PERSON><PERSON><PERSON>25Scorer\n", "from research.retrieval.types import Document, Chunk\n", "from research.models.model_server import add_files_to_index\n", "\n", "from pprint import pprint"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkpoints_root = \"/mnt/efs/augment/checkpoints\"\n", "\n", "print(\"Loading the model...\")\n", "model = CodeGen_16B_Indiana(checkpoints_root)\n", "#model = CodeGen_16B_Base_Model(checkpoints_root)\n", "# model = StarCoder_Model(checkpoints_root)\n", "model.load()\n", "\n", "# Build dense document index (and retriever model)\n", "\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "scorer = DenseRetrievalScorer(yaml_files, overwrite_values)\n", "bm25_scorer = GoodEnoughBM25Scorer(max_query_tokens=250)\n", "chunker = ScopeAwareChunker()\n", "file_filterer = basic_file_filterer\n", "\n", "dense_retrieval_doc_index = RetrievalDatabase(chunker, scorer, file_filterer)\n", "\n", "bm25_doc_index = RetrievalDatabase(chunker, bm25_scorer, file_filterer)\n", "\n", "# Set document index\n", "model.document_index = dense_retrieval_doc_index"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Magic number on classes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents = [\n", "    Document(\n", "        text=\"\"\"\n", "class modelA():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2s\n", "\n", "        self.magic_number = 132\n", "    \n", "    def add_vars(self):\n", "        return var1 + var2\n", "\n", "    def multiply_vars(self):\n", "        return var1 * var2\n", "\"\"\",\n", "        id=\"classA\",\n", "        path=\"classA.py\",\n", "        meta={},\n", "    ),\n", "    Document(\n", "        text=\"\"\"\n", "class modelB():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 145\n", "    \n", "    def add_vars(self):\n", "        return var1 + var2\n", "\n", "    def multiply_vars(self):\n", "        return var1 * var2\n", "\"\"\",\n", "        id=\"classB\",\n", "        path=\"classB.py\",\n", "        meta={},\n", "    ),\n", "    Document(\n", "        text=\"\"\"\n", "class modelC():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 172\n", "    \n", "    def add_vars(self):\n", "        return var1 + var2\n", "\n", "    def multiply_vars(self):\n", "        return var1 * var2        \n", "\"\"\",\n", "        id=\"classC\",\n", "        path=\"classC.py\",\n", "        meta={},\n", "    ),\n", "    Document(\n", "        text=\"\"\"\n", "@dataclass\n", "class E:\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 172\n", "    \n", "    def add_vars(self):\n", "        return var1 + var2\n", "\n", "    def multiply_vars(self):\n", "        return var1 * var2        \n", "\"\"\",\n", "        id=\"classC\",\n", "        path=\"classC.py\",\n", "        meta={},\n", "    ),\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We are able to retrieve the right class and copy magic number."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dense_retrieval_doc_index.remove_all_docs()\n", "dense_retrieval_doc_index.add_docs(documents)\n", "model.document_index = dense_retrieval_doc_index\n", "model.retrieval_top_k = 1\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "retrieved_chunks = model._get_retrieved_chunks( # type: ignore\n", "    prefix, \n", "    \"\",  # FIM turned off for now\n", "    retrieve=True, \n", "    retrieved_chunks=None\n", ")\n", "\n", "print('-'* 80)\n", "print('Retrieved_chunks:')\n", "for i, chunk in enumerate(retrieved_chunks):\n", "    print(f'Chunk {i}:')\n", "    print(chunk.text.replace('\\\\n', '\\n'))\n", "\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=retrieved_chunks, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We are able to correctly pick magic number from multiple retrieved chunks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dense_retrieval_doc_index.remove_all_docs()\n", "dense_retrieval_doc_index.add_docs(documents)\n", "model.document_index = dense_retrieval_doc_index\n", "model.retrieval_top_k = 3\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "retrieved_chunks = model._get_retrieved_chunks( # type: ignore\n", "    prefix, \n", "    \"\",  # FIM turned off for now\n", "    retrieve=True, \n", "    retrieved_chunks=None\n", ")\n", "\n", "print('-'* 80)\n", "print('Retrieved_chunks:')\n", "for i, chunk in enumerate(retrieved_chunks):\n", "    print(f'Chunk {i}:')\n", "    print(chunk.text.replace('\\\\n', '\\n'))\n", "\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=retrieved_chunks, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We are even able to find the right retrieval chunks after filling the database with unrelated chunks."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:augment.research.retrieval.retrieval_database:Starting document embedding.\n", "INFO:augment.research.retrieval.retrieval_database:Document at path classC.py already exists! Recomputing and regenerating any deleted chunks...\n", "INFO:augment.research.retrieval.retrieval_database:Finished document embedding in 0.09482765197753906.\n", "INFO:augment.research.retrieval.retrieval_database:Starting document embedding.\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/experimental/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/experimental/colin/data/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/experimental/hieu/hydra/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/experimental/igor/infra/internal/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/models/retrieval/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/models/retrieval/bm25/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/data/eval/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/data/eval/datasets/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/data/eval/datasets/utils/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/data/eval/datasets/utils/test/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/data/spark/pipelines/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/data/spark/pipelines/stages/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/eval/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/eval/tests/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/eval/tests/determined/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/gpt-neox/jobs/internal/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/gpt-neox/megatron/debug/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/gpt-neox/megatron/debug/cli/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/gpt-neox/tests/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/gpt-neox/tests/debug/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/gpt-neox/tests/evaluation/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/arithmetic/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/asdiv/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/codesearchnet/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/coqa/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/drop/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/gsm8k/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/headqa/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/hendry<PERSON>_ethics/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/hendrycks_math/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/lambada/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/logiqa/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/mutual/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/pile/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/quac/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/sat_analogies/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/triviaqa/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/truthfulqa/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/unscramble/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/datasets/wikitext/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/decontamination/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/lm_eval/tasks/repo_coder/dataset/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/scripts/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/scripts/clean_training_data/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/lm-evaluation-harness/tests/tasks/hydra/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/models/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/retrieval/legacy_retrieval_implementations/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/retrieval/libraries/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/retrieval/tests/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/static_analysis/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/static_analysis/tests/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/tokenizer/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/research/utils/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/third_party/FasterTransformer/examples/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/third_party/FasterTransformer/examples/pytorch/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Document at path /home/<USER>/src/augment/third_party/FasterTransformer/examples/pytorch/bert/bert-quantization-sparsity/processors/__init__.py has no chunks. Skipping..\n", "INFO:augment.research.retrieval.retrieval_database:Finished document embedding in 85.17502689361572.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Corpus contains 956 files with 172740 lines\n", "--------------------------------------------------------------------------------\n", "Retrieved_chunks:\n", "Chunk 0:\n", "class modelB():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 145\n", "\n", "Chunk 1:\n", "class modelA():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 132\n", "\n", "Chunk 2:\n", "class modelB():\n", "    def multiply_vars(self):\n", "        return var1 * var2\n", "\n", "--------------------------------------------------------------------------------\n", "Prompt:\n", "<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>classB.py\n", "class modelB():\n", "    def multiply_vars(self):\n", "        return var1 * var2\n", "<|ret-endofdoc|>classA.py\n", "class modelA():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 132\n", "<|ret-endofdoc|>classB.py\n", "class modelB():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 145\n", "<|ret-endofdoc|>test_models.py\n", "\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\n", "--------------------------------------------------------------------------------\n", "Result:\n", "<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>classB.py\n", "class modelB():\n", "    def multiply_vars(self):\n", "        return var1 * var2\n", "<|ret-endofdoc|>classA.py\n", "class modelA():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 132\n", "<|ret-endofdoc|>classB.py\n", "class modelB():\n", "    def __init__(self, var1, var2):\n", "        self.var1 = var1\n", "        self.var2 = var2\n", "\n", "        self.magic_number = 145\n", "<|ret-endofdoc|>test_models.py\n", "\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "assert magic_number == 145\n", "\n"]}], "source": ["dense_retrieval_doc_index.remove_all_docs()\n", "dense_retrieval_doc_index.add_docs(documents)\n", "\n", "import os\n", "repo_root = os.environ[\"HOME\"] + \"/src/augment\"\n", "add_files_to_index(dense_retrieval_doc_index, repo_root, extensions=['.py'])\n", "\n", "model.document_index = dense_retrieval_doc_index\n", "model.retrieval_top_k = 3\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "retrieved_chunks = model._get_retrieved_chunks( # type: ignore\n", "    prefix, \n", "    \"\",  # FIM turned off for now\n", "    retrieve=True, \n", "    retrieved_chunks=None\n", ")\n", "\n", "print('-'* 80)\n", "print('Retrieved_chunks:')\n", "for i, chunk in enumerate(retrieved_chunks):\n", "    print(f'Chunk {i}:')\n", "    print(chunk.text.replace('\\\\n', '\\n'))\n", "\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=retrieved_chunks, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We are even able to find the right retrieval chunks after filling the database with unrelated chunks, even after increasing number of retrieved chunks to 20."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.retrieval_top_k = 20\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "retrieved_chunks = model._get_retrieved_chunks( # type: ignore\n", "    prefix, \n", "    \"\",  # FIM turned off for now\n", "    retrieve=True, \n", "    retrieved_chunks=None\n", ")\n", "\n", "print('-'* 80)\n", "print('Retrieved_chunks:')\n", "for i, chunk in enumerate(retrieved_chunks):\n", "    print(f'Chunk {i}:')\n", "    print(chunk.text.replace('\\\\n', '\\n'))\n", "\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=retrieved_chunks, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["However, once we add extra stuff into the prefix, even if the end of the prefix is still very clear about what we want to generate."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.retrieval_top_k = 20\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "docs = [\n", "    Document(\n", "        text=patchcore_test_data.COMMON_PY,\n", "        id=\"common.py_uuid\",\n", "        path=\"common.py\",\n", "        meta={},\n", "    ),\n", "    Document(\n", "        text=patchcore_test_data.METRICS_PY,\n", "        id=\"metrics.py_uuid\",\n", "        path=\"common.py\",\n", "        meta={},\n", "    ),\n", "]\n", "query3 = Document(\n", "    text=patchcore_test_data.QUERY3,\n", "    id=\"exact_match_uuid\",\n", "    path=\"test_common.py\",\n", "    meta={},\n", ")\n", "\n", "\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "retrieved_chunks = model._get_retrieved_chunks( # type: ignore\n", "    prefix, \n", "    \"\",  # FIM turned off for now\n", "    retrieve=True, \n", "    retrieved_chunks=None\n", ")\n", "\n", "print('-'* 80)\n", "print('Retrieved_chunks:')\n", "for i, chunk in enumerate(retrieved_chunks):\n", "    print(f'Chunk {i}:')\n", "    print(chunk.text.replace('\\\\n', '\\n'))\n", "\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=retrieved_chunks, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.retrieval_top_k = 20\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "docs = [\n", "    Document(\n", "        text=patchcore_test_data.COMMON_PY,\n", "        id=\"common.py_uuid\",\n", "        path=\"common.py\",\n", "        meta={},\n", "    ),\n", "    Document(\n", "        text=patchcore_test_data.METRICS_PY,\n", "        id=\"metrics.py_uuid\",\n", "        path=\"common.py\",\n", "        meta={},\n", "    ),\n", "]\n", "query3 = Document(\n", "    text=patchcore_test_data.QUERY3,\n", "    id=\"exact_match_uuid\",\n", "    path=\"test_common.py\",\n", "    meta={},\n", ")\n", "\n", "\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "retrieved_chunks = model._get_retrieved_chunks( # type: ignore\n", "    prefix, \n", "    \"\",  # FIM turned off for now\n", "    retrieve=True, \n", "    retrieved_chunks=None\n", ")\n", "\n", "import copy\n", "retrieved_chunks = [copy.deepcopy(chunk) for chunk in retrieved_chunks]\n", "\n", "#for chunk in retrieved_chunks:\n", "#    chunk.text = chunk.text[:600]\n", "\n", "print('-'* 80)\n", "print('Retrieved_chunks:')\n", "for i, chunk in enumerate(retrieved_chunks):\n", "    print(f'Chunk {i}:')\n", "    print(chunk.text.replace('\\\\n', '\\n'))\n", "\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=retrieved_chunks, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["By the way, here is what a generation without retrieval looks like."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.retrieval_top_k = 20\n", "\n", "filename='test_models.py'\n", "prefix = \"\"\"\n", "# Instantiate model\n", "a_model = modelB()\n", "\n", "# Grab magic number\n", "magic_number = a_model.magic_number\n", "\n", "# <PERSON><PERSON><PERSON> expected magic number\n", "\"\"\"\n", "\n", "max_generated_tokens = model.max_generated_toks\n", "                \n", "prompt_toks = model.prepare_prompt(\n", "    max_generated_tokens=max_generated_tokens, \n", "    prefix=prefix, \n", "    suffix=\"\", \n", "    retrieved_chunks=None, \n", "    path=filename,\n", ")\n", "\n", "print('-'* 80)\n", "print('Prompt:')\n", "print(model.tokenizer.detokenize(prompt_toks).replace('\\\\n', '\\n'))\n", "\n", "generation = model._generate(\n", "    prompt_toks, \n", "    max_generated_tokens, \n", "    temperature=0,\n", ")\n", "\n", "print('-'* 80)\n", "print('Result:')\n", "print((model.tokenizer.detokenize(prompt_toks) + generation).replace('\\\\n', '\\n'))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}