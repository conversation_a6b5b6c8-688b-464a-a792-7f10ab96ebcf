{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Notebook Overview\n", "\n", "This notebook explores retrieval-augmented generation in the huggingface/transformers repository, with both dense retrieval and BM25-based retrieval. \n", "\n", "In the first example, we try to complete an implementation of self-attention. There are lots of implementations of self-attention in the huggingface/transformers repository, so retrieval might plausibly help here as a kind of few-shot prompt."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import time\n", "import sys\n", "import numpy as np\n", "from termcolor import colored\n", "from textwrap import dedent\n", "import torch\n", "from termcolor import colored\n", "from research.core.model_input import ModelInput\n", "from types import MethodType\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "\n", "\n", "home = os.environ['HOME']\n", "sys.path.append(f'{home}/src/augment/research')\n", "sys.path.append(f'{home}/src/augment/research/models')\n", "\n", "from models import add_files_to_index\n", "from research_models import (\n", "    CodeGen_350M_Base_Model,\n", "    CodeGen_2B_Base_Model,\n", "    CodeGen_16B_Base_Model,\n", "    StarCoder_Model,\n", "    CodeGen_16B_Indiana_Model\n", ")\n", "from retrieval.libraries.types import Document, Chunk\n", "from retrieval.retrieval_database import RetrievalDatabase\n", "from retrieval.libraries.chunking_functions import ScopeAwareChunker\n", "from retrieval.libraries.file_filterer import basic_file_filterer\n", "from retrieval.libraries.scorers.dense_scorer import DenseRetrievalScorer\n", "from retrieval.libraries.scorers.good_enough_bm25_scorer import <PERSON><PERSON><PERSON><PERSON><PERSON>25Scorer\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Load models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### LOAD MODEL\n", "\n", "checkpoints_root = \"/mnt/efs/augment/checkpoints\"\n", "\n", "print(\"Loading the model...\")\n", "#model = CodeGen_16B_Indiana_Model()\n", "model = CodeGen_16B_Base_Model(checkpoints_root)\n", "# model = StarCoder_Model(checkpoints_root)\n", "model.load()\n", "\n", "# Build dense document index (and retriever model)\n", "\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "scorer = DenseRetrievalScorer(yaml_files, overwrite_values)\n", "bm25_scorer = GoodE<PERSON>ughBM25Scorer()\n", "chunker = ScopeAwareChunker()\n", "file_filterer = basic_file_filterer\n", "\n", "dense_retrieval_doc_index = RetrievalDatabase(chunker, scorer, file_filterer)\n", "\n", "bm25_doc_index = RetrievalDatabase(chunker, bm25_scorer, file_filterer)\n", "\n", "# Set document index\n", "model.document_index = dense_retrieval_doc_index\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Add huggingface/transformers repository files into dense and bm25 retrieval indices\n", "\n", "This assumes that `hugginface/transformers` is cloned into `~/src/transformers`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### ADD FILES FROM REPO\n", "\n", "repo_root = os.environ[\"HOME\"] + \"/src/transformers\"\n", "add_files_to_index(model.document_index, path=repo_root, extensions=[\".py\"])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_root = os.environ[\"HOME\"] + \"/src/transformers\"\n", "add_files_to_index(bm25_doc_index, path=repo_root, extensions=[\".py\"])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Dense Retrieval\n", "\n", "Notice how the retrieved chunks are other self-attention implementations, evne if I remove the function name (`_attn`) from the query."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from https://github.com/huggingface/transformers/blob/main/src/transformers/models/codegen/modeling_codegen.py\n", "\n", "\n", "prompt = \"\"\"def _attn(\n", "        self,\n", "        query,\n", "        key,\n", "        value,\n", "        attention_mask=None,\n", "        head_mask=None,\n", "    ):\n", "        # compute causal mask from causal mask buffer\n", "        query_length, key_length = query.size(-2), key.size(-2)\n", "        causal_mask = self.causal_mask[:, :, key_length - query_length : key_length, :key_length]\n", "\n", "        # Keep the attention weights computation in fp32 to avoid overflow issues\n", "        query = query.to(torch.float32)\n", "        key = key.to(torch.float32)\n", "\"\"\"\n", "\n", "print(\"\\n======= Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print('=' * 80)\n", "print(\"\\n======= Generating without retrieval:\")\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieve=False, max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print(prompt+ colored(generated_text, \"green\"))\n", "print('=' * 80)\n", "\n", "print(\"\\n======= Retrieving and generating:\")\n", "print('Retrieving...')\n", "start_time = time.time()\n", "chunks, scores = model.document_index.query(ModelInput(prefix=prompt), top_k=20)\n", "end_time = time.time()\n", "print(f'Retrieved in {end_time - start_time} sec')\n", "print('Generating...')\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieved_chunks=chunks[:3], max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print('Generation:')\n", "print(prompt + colored(generated_text, \"green\"))\n", "\n", "print('Retrieved chunks (sorted from highest to lowest rank).')\n", "for i, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print('-' * 80)\n", "    print(f'Chunk idx {i} with score {score}:')\n", "    print(colored(chunk.text[:100], 'yellow'))\n", "print('-' * 80)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from https://github.com/huggingface/transformers/blob/main/src/transformers/models/codegen/modeling_codegen.py\n", "prompt = \"\"\"\n", "        # compute causal mask from causal mask buffer\n", "        query_length, key_length = query.size(-2), key.size(-2)\n", "        causal_mask = self.causal_mask[:, :, key_length - query_length : key_length, :key_length]\n", "\n", "        # Keep the attention weights computation in fp32 to avoid overflow issues\n", "        query = query.to(torch.float32)\n", "        key = key.to(torch.float32)\n", "\"\"\"\n", "\n", "print(\"\\n======= Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print('=' * 80)\n", "print(\"\\n======= Generating without retrieval:\")\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieve=False, max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print(prompt+ colored(generated_text, \"green\"))\n", "print('=' * 80)\n", "\n", "print(\"\\n======= Retrieving and generating:\")\n", "print('Retrieving...')\n", "start_time = time.time()\n", "chunks, scores = model.document_index.query(ModelInput(prefix=prompt), top_k=20)\n", "end_time = time.time()\n", "print(f'Retrieved in {end_time - start_time} sec')\n", "print('Generating...')\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieved_chunks=chunks[:3], max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print('Generation:')\n", "print(prompt + colored(generated_text, \"green\"))\n", "\n", "print('Retrieved chunks (sorted from highest to lowest rank).')\n", "for i, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print('-' * 80)\n", "    print(f'Chunk idx {i} with score {score[0]}:')\n", "    print(colored(chunk.text[:100], 'yellow'))\n", "print('-' * 80)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### BM25 Retrieval\n", "\n", "Notice how the retrieved chunks are also from other self-attention implementations, even if I remove the function name (`_attn`) from the query."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.document_index = bm25_doc_index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from https://github.com/huggingface/transformers/blob/main/src/transformers/models/codegen/modeling_codegen.py\n", "prompt = \"\"\"def _attn(\n", "        self,\n", "        query,\n", "        key,\n", "        value,\n", "        attention_mask=None,\n", "        head_mask=None,\n", "    ):\n", "        # compute causal mask from causal mask buffer\n", "        query_length, key_length = query.size(-2), key.size(-2)\n", "        causal_mask = self.causal_mask[:, :, key_length - query_length : key_length, :key_length]\n", "\n", "        # Keep the attention weights computation in fp32 to avoid overflow issues\n", "        query = query.to(torch.float32)\n", "        key = key.to(torch.float32)\n", "\"\"\"\n", "\n", "print(\"\\n======= Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print('=' * 80)\n", "print(\"\\n======= Generating without retrieval:\")\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieve=False, max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print(prompt+ colored(generated_text, \"green\"))\n", "print('=' * 80)\n", "\n", "print(\"\\n======= Retrieving and generating:\")\n", "print('Retrieving...')\n", "start_time = time.time()\n", "chunks, scores = model.document_index.query(ModelInput(prefix=prompt), top_k=20)\n", "end_time = time.time()\n", "print(f'Retrieved in {end_time - start_time} sec')\n", "print('Generating...')\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieved_chunks=chunks[:3], max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print('Generation:')\n", "print(prompt + colored(generated_text, \"green\"))\n", "\n", "print('Retrieved chunks (sorted from highest to lowest rank).')\n", "for i, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print('-' * 80)\n", "    print(f'Chunk idx {i} with score {score}:')\n", "    print(colored(chunk.text[:100], 'yellow'))\n", "print('-' * 80)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from https://github.com/huggingface/transformers/blob/main/src/transformers/models/codegen/modeling_codegen.py\n", "prompt = \"\"\"\n", "        # compute causal mask from causal mask buffer\n", "        query_length, key_length = query.size(-2), key.size(-2)\n", "        causal_mask = self.causal_mask[:, :, key_length - query_length : key_length, :key_length]\n", "\n", "        # Keep the attention weights computation in fp32 to avoid overflow issues\n", "        query = query.to(torch.float32)\n", "        key = key.to(torch.float32)\n", "\"\"\"\n", "\n", "print(\"\\n======= Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print('=' * 80)\n", "print(\"\\n======= Generating without retrieval:\")\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieve=False, max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print(prompt+ colored(generated_text, \"green\"))\n", "print('=' * 80)\n", "\n", "print(\"\\n======= Retrieving and generating:\")\n", "print('Retrieving...')\n", "start_time = time.time()\n", "chunks, scores = model.document_index.query(ModelInput(prefix=prompt), top_k=20)\n", "end_time = time.time()\n", "print(f'Retrieved in {end_time - start_time} sec')\n", "print('Generating...')\n", "start_time = time.time()\n", "generated_text = model.generate(prefix=prompt, retrieved_chunks=chunks[:3], max_generated_tokens=200)\n", "end_time = time.time()\n", "print(f'Generated in {end_time - start_time} sec')\n", "print('Generation:')\n", "print(prompt + colored(generated_text, \"green\"))\n", "\n", "print('Retrieved chunks (sorted from highest to lowest rank).')\n", "for i, (chunk, score) in enumerate(zip(chunks, scores)):\n", "    print('-' * 80)\n", "    print(f'Chunk idx {i} with score {score}:')\n", "    print(colored(chunk.text[:100], 'yellow'))\n", "print('-' * 80)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}