{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import difflib\n", "import time"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["files = []\n", "\n", "for j in range(1000):\n", "    lines = []\n", "    for i in range(10000):\n", "        lines.append(f'File {j}: This is a line on line {i}\\n')\n", "\n", "    changed_lines = []\n", "    for i in range(10000):\n", "        if i % 2 == 0:\n", "            changed_lines.append(f'File {j}: This is a line on line {i}')\n", "        else:\n", "            changed_lines.append(f'Filsfse {j}: Thsfsis is a lfsinefasfa on lisdfafane {i}\\n')\n", "    \n", "    files.append((lines, changed_lines))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.001700599988301595\n"]}], "source": ["ndiff_times = []\n", "for i in range(30):\n", "    start = time.time()\n", "    for file, changed_file in files:\n", "        difflib.ndiff(file, changed_file)\n", "    end = time.time()\n", "    ndiff_times.append(end - start)\n", "\n", "print(sum(ndiff_times)/len(ndiff_times))"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0008697112401326498\n"]}], "source": ["ndiff_times = []\n", "for i in range(30):\n", "    start = time.time()\n", "    for file, changed_file in files:\n", "        difflib.unified_diff(file, changed_file)\n", "    end = time.time()\n", "    ndiff_times.append(end - start)\n", "\n", "print(sum(ndiff_times)/len(ndiff_times))"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0009335756301879882\n"]}], "source": ["ndiff_times = []\n", "for i in range(30):\n", "    start = time.time()\n", "    for file, changed_file in files:\n", "        difflib.unified_diff(file, changed_file, n=10_000)\n", "    end = time.time()\n", "    ndiff_times.append(end - start)\n", "\n", "print(sum(ndiff_times)/len(ndiff_times))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-38-e8d8a0736532>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m     11\u001b[0m     \u001b[0mstart\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtime\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtime\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     12\u001b[0m     \u001b[0;32mfor\u001b[0m \u001b[0mfile\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mchanged_file\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mfiles\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 13\u001b[0;31m         \u001b[0mout\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mlist\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdifflib\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcontext_diff\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfile\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mchanged_file\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     14\u001b[0m         \u001b[0;31m#out = out[2:]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     15\u001b[0m         \u001b[0;31m#start_line = [i for i in range(len(out)) if out[i].startswith('---')]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/py35/lib/python3.6/difflib.py\u001b[0m in \u001b[0;36mcontext_diff\u001b[0;34m(a, b, fromfile, tofile, fromfiledate, tofiledate, n, lineterm)\u001b[0m\n\u001b[1;32m   1266\u001b[0m     \u001b[0mprefix\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minsert\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'+ '\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdelete\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'- '\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mreplace\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'! '\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mequal\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'  '\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1267\u001b[0m     \u001b[0mstarted\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mFalse\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1268\u001b[0;31m     \u001b[0;32mfor\u001b[0m \u001b[0mgroup\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mSequenceMatcher\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0ma\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget_grouped_opcodes\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mn\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1269\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mstarted\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1270\u001b[0m             \u001b[0mstarted\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/py35/lib/python3.6/difflib.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, isjunk, a, b, autojunk)\u001b[0m\n\u001b[1;32m    211\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0ma\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mb\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    212\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mautojunk\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mautojunk\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 213\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_seqs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0ma\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    214\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    215\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mset_seqs\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0ma\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/py35/lib/python3.6/difflib.py\u001b[0m in \u001b[0;36mset_seqs\u001b[0;34m(self, a, b)\u001b[0m\n\u001b[1;32m    223\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    224\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_seq1\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0ma\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 225\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_seq2\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    226\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    227\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mset_seq1\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0ma\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/py35/lib/python3.6/difflib.py\u001b[0m in \u001b[0;36mset_seq2\u001b[0;34m(self, b)\u001b[0m\n\u001b[1;32m    277\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmatching_blocks\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mopcodes\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    278\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfullbcount\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 279\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__chain_b\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    280\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    281\u001b[0m     \u001b[0;31m# For each element x in b, set b2j[x] to a list of the indices in\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/miniconda3/envs/py35/lib/python3.6/difflib.py\u001b[0m in \u001b[0;36m__chain_b\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    310\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    311\u001b[0m         \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0melt\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mb\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 312\u001b[0;31m             \u001b[0mindices\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mb2j\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msetdefault\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0melt\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    313\u001b[0m             \u001b[0mindices\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    314\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["###\n", "# This is intractiably slow\n", "# So:\n", "# ha - another surprise: \n", "# - difflib.context_diff is intractably slow, \n", "# difflib.ndiff is quite slow, \n", "# and difflib.unified_diff is pretty fast.\n", "###\n", "ndiff_times = []\n", "for i in range(30):\n", "    start = time.time()\n", "    for file, changed_file in files:\n", "        out = list(difflib.context_diff(file, changed_file))\n", "        #out = out[2:]\n", "        #start_line = [i for i in range(len(out)) if out[i].startswith('---')]\n", "        #assert len(start_line) == 1, start_line\n", "        #out = out[(start_line[0] + 1):]\n", "    end = time.time()\n", "    ndiff_times.append(end - start)\n", "\n", "print(sum(ndiff_times)/len(ndiff_times))"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["['--- \\n',\n", " '+++ \\n',\n", " '@@ -1,7 +1,7 @@\\n',\n", " '-This is line 1\\n',\n", " '+This is changed line 1\\n',\n", " ' This is line 2\\n',\n", " ' This is line 3\\n',\n", " ' This is line 4\\n',\n", " '-This is line 5\\n',\n", " '+This is changed line 5\\n',\n", " ' This is line 6\\n',\n", " ' This is line 7\\n']"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["a = [\n", "    'This is line 1\\n',\n", "    'This is line 2\\n',\n", "    'This is line 3\\n',\n", "    'This is line 4\\n',\n", "    'This is line 5\\n',\n", "    'This is line 6\\n',\n", "    'This is line 7\\n',\n", "]\n", "b = [\n", "    'This is changed line 1\\n',\n", "    'This is line 2\\n',\n", "    'This is line 3\\n',\n", "    'This is line 4\\n',\n", "    'This is changed line 5\\n',\n", "    'This is line 6\\n',\n", "    'This is line 7\\n',\n", "]\n", "\n", "\n", "list(difflib.unified_diff(a,b, n=10_000))"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["['! This is changed line 1\\n',\n", " '  This is line 2\\n',\n", " '  This is line 3\\n',\n", " '  This is line 4\\n',\n", " '! This is changed line 5\\n',\n", " '  This is line 6\\n',\n", " '  This is line 7\\n']"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["out = list(difflib.context_diff(a,b))\n", "out = out[2:]\n", "start_line = [i for i in range(len(out)) if out[i].startswith('---')]\n", "assert len(start_line) == 1, start_line\n", "out = out[(start_line[0] + 1):]\n", "out"]}], "metadata": {"kernelspec": {"display_name": "py35", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.15"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}