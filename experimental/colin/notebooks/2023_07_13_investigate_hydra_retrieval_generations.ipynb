{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "import pickle\n", "from pprint import pprint\n", "from research.eval import patch_lib\n", "from research.static_analysis.parsing import LineMap\n", "from collections import Counter\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "import pandas as pd\n", "import random\n", "import copy\n", "import csv\n", "import numpy as np\n", "\n", "tokenizer = CodeGenTokenizer()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "EXP NAME: dense_retriever_samefile\n", "dict_keys(['patch', 'prompt_toks', 'all_retrieved_chunks', 'filtered_chunks', 'filtered_chunks_afterprefix', 'prefix'])\n", "dict_keys(['file_content', 'char_start', 'char_end', 'patch_content', 'patch_id', 'repository', 'commit_sha', 'file_name', '_extra'])\n"]}], "source": ["base_path = Path('/mnt/efs/augment/user/colin/scratch/')\n", "# project_name = 'evaluate_retrievers_2023_07_05'\n", "# project_name = 'evaluate_retrievers_2023_07_05_no_retriever'\n", "# project_name = 'evaluate_retrievers_2023_07_06'\n", "# project_name = 'evaluate_retrievers_2023_07_06_attempt3'\n", "# project_name = 'evaluate_indiana_retrievers_2023_07_07'\n", "project_name = 'evaluate_indiana_retrievers_2023_07_08_1024localctx_linelevelchunker'\n", "model_name = 'indiana_16B_retrieve10'\n", "#exp_names = [\n", "#    #'bm25', \n", "#    #'dense_retriever_diffs', \n", "#    'dense_retriever_samefile',\n", "#    #'no_retriever',\n", "#]\n", "exp_name = 'dense_retriever_samefile'\n", "ret_tok = tokenizer.vocab['<|ret-endofdoc|>']\n", "\n", "print('-' * 80)\n", "print(f'EXP NAME: {exp_name}')\n", "\n", "hydra_out_noretrieval_fp = base_path / 'evaluate_indiana_retrievers_2023_07_07' / 'no_retriever' / f'{model_name}_no_retriever.jsonl'\n", "hydra_out_fp = base_path / project_name / exp_name / f'{model_name}_{exp_name}.jsonl'\n", "completed_patches_fp = base_path / project_name / exp_name / f'{model_name}_{exp_name}_completed_patches.pkl'\n", "\n", "with open(hydra_out_fp, 'r') as f:\n", "    hydra_out = [json.loads(line) for line in f.readlines()]\n", "\n", "with open(hydra_out_noretrieval_fp, 'r') as f:\n", "    hydra_out_noretrieval = [json.loads(line) for line in f.readlines()]\n", "\n", "with open(completed_patches_fp, 'rb') as f:\n", "    completed_patches = pickle.load(f)\n", "\n", "all_patches =  [patch for patches in completed_patches.values() for patch in patches]\n", "\n", "print(all_patches[0].keys())\n", "print(json.loads(all_patches[0]['patch']).keys())"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# Grab relevant patches\n", "\n", "patch_ids = [\n", "    'deepmind_tracr/105',\n", "    'deepmind_tracr/114',\n", "    'deepmind_tracr/36',\n", "    'deepmind_tracr/57',\n", "    'deepmind_tracr/58',\n", "    'deepmind_tracr/86',\n", "    'facebookresearch_omnivore/13',\n", "    'google_lightweight_mmm/2',\n", "    'google_lightweight_mmm/51',\n", "    'google_lightweight_mmm/59',\n", "    'google_lightweight_mmm/60',\n", "    'leopard-ai_betty/10',\n", "    'lucidrains_imagen-pytorch/56',\n", "    'maxhumber_redframes/33'\n", "]\n", "\n", "relevant_patches = []\n", "for patch_data in all_patches:\n", "    if json.loads(patch_data['patch'])['patch_id'] in patch_ids:\n", "        patch_data = copy.deepcopy(patch_data)\n", "        relevant_patches.append(patch_data)\n", "        patch_data['patch'] = patch_lib.Patch.from_json(patch_data['patch'])\n", "\n", "relevant_patches = sorted(relevant_patches, key=lambda x: x['patch'].patch_id)\n", "\n", "assert len(relevant_patches) == len(patch_ids), (len(relevant_patches), len(patch_ids))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["patch_ret_chunks = {\n", "    'deepmind_tracr/105':\n", "        [\n", "            'class BasisDirection',\n", "            'class VectorSpaceWithBasis',\n", "        ],\n", "    'deepmind_tracr/114':\n", "        [\n", "            'def join_vector_spaces',\n", "            'def issubspace',\n", "            \n", "        ],\n", "    'deepmind_tracr/36':\n", "        [\n", "            'class SelectorAnd',\n", "            'class Select',\n", "            'class Aggregate',\n", "        ],\n", "    'deepmind_tracr/57':\n", "        [\n", "            'def null_vector',\n", "            'def vector_from_basis_direction'\n", "        ],\n", "    'deepmind_tracr/58':\n", "        [\n", "            'class Select',\n", "            'class Comparison',\n", "            'class SelectorWidth',\n", "        ],\n", "    'deepmind_tracr/86':\n", "        [\n", "            'class VectorInBasis',\n", "            'def null_vector',\n", "            'def vector_from_basis_direction'\n", "        ],\n", "    'facebookresearch_omnivore/13':\n", "        [\n", "            \"AMBIGUOUS\",\n", "        ],\n", "    'google_lightweight_mmm/2':\n", "        [  \n", "            # See https://github.com/google/lightweight_mmm/blob/main/lightweight_mmm/lightweight_mmm.py#L383\n", "            'def fit'\n", "        ],\n", "    'google_lightweight_mmm/51':\n", "        [\n", "            # This callsite mostly requires how to construct test defaults. hard.\n", "            'def fit'\n", "        ],\n", "    'google_lightweight_mmm/59':\n", "        [\n", "            '_NAMES_TO_MODEL_TRANSFORMS =',\n", "            '_MODEL_FUNCTION =',\n", "            'MODEL_PRIORS_NAMES =',\n", "            'TRANSFORM_PRIORS_NAMES =',\n", "        ],\n", "    'google_lightweight_mmm/60':\n", "        [\n", "            # eg should retreive function properties\n", "            'model_name: str = \"hill_adstock\"',\n", "        ],\n", "    'leopard-ai_betty/10':\n", "        [\n", "            # From init\n", "            'self._backend = None',\n", "        ],\n", "    'lucidrains_imagen-pytorch/56':\n", "        [\n", "            'def get_unet',\n", "            # In correct def prepare\n", "            'Prepare all objects passed in `args` for distributed training and mixed precision, then return them in the same'\n", "        ],\n", "    'maxhumber_redframes/33':\n", "        [\n", "            'PandasDataFrame = pd.DataFrame'\n", "        ],\n", "}"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/105 tracr/craft/bases.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " s: Sequence[\"VectorInBasis\"],\n", "            axis: int = 0) -> \"VectorInBasis\":\n", "    for v in vectors[1:]:\n", "      if v.basis_directions != vectors[0].basis_directions:\n", "        raise TypeError(f\"Stacking incompatible bases: {vectors[0]} + {v}\")\n", "    return cls(vectors[0].basis_directions,\n", "               np.stack([v.magnitudes for v in vectors], axis=axis))\n", "\n", "  def project(\n", "      self, basis: Union[\"VectorSpaceWithBasis\", Sequence[BasisDirection]]\n", "  ) -> \"VectorInBasis\":\n", "    \"\"\"Projects to the basis.\"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     if isinstance(basis, VectorSpaceWithBasis):\n", "      basis = basis.basis\n", "    components = []\n", "    for direction in basis:\n", "      if direction in self.basis_directions:\n", "        components.append(\n", "            self.magnitudes[..., self.basis_directions.index(direction)])\n", "      else:\n", "        components.append(np.zeros_like(self.magnitudes[..., 0]))\n", "    return VectorInBasis(list(basis), np.stack(components, axis=-1))\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     if isinstance(basis, VectorSpaceWithBasis):\n", "      basis = basis.basis\n", "    elif isinstance(basis, Sequence):\n", "      basis = [b.name for b in basis]\n", "    else:\n", "      raise TypeError(f\"basis={basis} is not a VectorSpaceWithBasis or \"\n", "                      f\"Sequence[BasisDirection]\")\n", "    if self.basis_directions != basis:\n", "      raise TypeError(f\"Projecting incompatible bases: {self} + {basis}\")\n", "    return VectorInBasis(self.basis_directions,\n", "                         self.magnitudes @ np.take(basis, self.basis_directions,\n", "                                                   axis=-1))\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/114 tracr/craft/transformers.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", "  AttentionHead):\n", "        yield sub_block\n", "      elif isinstance(sub_block, MultiAttentionHead):\n", "        yield from sub_block.heads()\n", "      else:\n", "        raise NotImplementedError()\n", "\n", "  def as_multi(self) -> \"MultiAttentionHead\":\n", "    return self\n", "\n", "\n", "@dataclasses.dataclass\n", "class MLP(Block):\n", "  \"\"\"A transformer MLP block.\"\"\"\n", "  fst: vectorspace_fns.Linear\n", "  snd: vectorspace_fns.Linear\n", "  residual_space: Optional[bases.VectorSpaceWithBasis] = None\n", "\n", "  def __post_init__(self):\n", "    \"\"\"Typecheck subspaces.\"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     if self.residual_space is None:\n", "      self.residual_space = bases.join_vector_spaces(self.fst.input_space,\n", "                                                     self.snd.output_space)\n", "\n", "    assert self.fst.output_space == self.snd.input_space\n", "    assert self.fst.input_space.issubspace(self.residual_space)\n", "    assert self.snd.output_space.issubspace(self.residual_space)\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     if self.residual_space is None:\n", "      self.residual_space = bases.join_vector_spaces(\n", "          self.fst.input_space, self.fst.output_space,\n", "          self.snd.input_space, self.snd.output_space)\n", "\n", "    assert self.fst.input_space.issubspace(self.residual_space)\n", "    assert self.fst.output_space.issubspace(self.residual_space)\n", "    assert self.snd.input_space.issubspace(self.residual_space)\n", "    assert self.snd.output_space.issubspace(self.residual_space)\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/36 tracr/rasp/rasp_test.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " \n", "      ),\n", "  )\n", "  def test_aggregate_on_size_2_inputs(self, selector, sop, default,\n", "                                      expected_value):\n", "    # The 0, 0 input is ignored as it's overridden by the constant SOps.\n", "    self.assertEqual(\n", "        rasp.Aggregate(selector, sop, default)([0, 0]),\n", "        expected_value,\n", "    )\n", "\n", "\n", "class RaspProgramTest(parameterized.TestCase):\n", "  \"\"\"Each testcase implements and tests a RASP program.\"\"\"\n", "\n", "  def test_has_prev(self):\n", "\n", "    def has_prev(seq: rasp.SOp) -> rasp.SOp:\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "       prev_copy = rasp.SelectorAnd(\n", "          rasp.Select(seq, seq, rasp.Comparison.EQ),\n", "          rasp.Select(rasp.indices, rasp.indices, rasp.Comparison.LT),\n", "      )\n", "      return rasp.Aggregate(prev_copy, rasp.Full(1), default=0) > 0\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "       return seq.prev\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/57 tracr/compiler/rasp_to_craft_integration_test.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " direction(\n", "      bases.BasisDirection(_ONE_DIRECTION))\n", "  embedded_input = [bos_vec + one_vec]\n", "  for i, val in enumerate(input_seq):\n", "    i_vec = input_space.vector_from_basis_direction(\n", "        bases.BasisDirection(\"indices\", i))\n", "    val_vec = input_space.vector_from_basis_direction(\n", "        bases.BasisDirection(\"tokens\", val))\n", "    embedded_input.append(i_vec + val_vec + one_vec)\n", "  return bases.VectorInBasis.stack(embedded_input)\n", "\n", "\n", "def _embed_output(output_seq, output_space, categorical_output):\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "   embedded_output = []\n", "  output_label = output_space.basis[0].name\n", "  for x in output_seq:\n", "    if x is None:\n", "      out_vec = output_space.null_vector()\n", "    elif categorical_output:\n", "      out_vec = output_space.vector_from_basis_direction(\n", "          bases.BasisDirection(output_label, x))\n", "    else:\n", "      out_vec = x * output_space.vector_from_basis_direction(\n", "          output_space.basis[0])\n", "    embedded_output.append(out_vec)\n", "  return bases.VectorInBasis.stack(embedded_output)\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "   bos_vec = output_space.vector_from_basis_direction(\n", "      bases.BasisDirection(_BOS_DIRECTION))\n", "  one_vec = output_space.vector_from_basis_direction(\n", "      bases.BasisDirection(_ONE_DIRECTION))\n", "  embedded_output = [bos_vec + one_vec]\n", "  for i, val in enumerate(output_seq):\n", "    i_vec = output_space.vector_from_basis_direction(\n", "        bases.BasisDirection(\"indices\", i))\n", "    val_vec = output_space.vector_from_basis_direction(\n", "        bases.BasisDirection(\"tokens\", val))\n", "    if categorical_output:\n", "      val_vec = np.array([val_vec])\n", "    embedded_output.append(i_vec + val_vec + one_vec)\n", "  return bases.VectorInBasis.stack(embedded_output)\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/58 tracr/compiler/lib.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " the subset of RASP supported by the compiler.\"\"\"\n", "\n", "from typing import List, Sequence\n", "\n", "from tracr.rasp import rasp\n", "\n", "### Programs that work only under non-causal evaluation.\n", "\n", "\n", "def make_length() -> rasp.SOp:\n", "  \"\"\"Creates the `length` SOp using selector width primitive.\n", "\n", "  Example usage:\n", "    length = make_length()\n", "    length(\"abcdefg\")\n", "    >> [7.0, 7.0, 7.0, 7.0, 7.0, 7.0, 7.0]\n", "\n", "  Returns:\n", "    length: SOp mapping an input to a sequence, where every element\n", "      is the length of that sequence.\n", "  \"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "   all_true_selector = rasp.Select(\n", "      rasp.tokens, rasp.tokens, rasp.Comparison.TRUE).named(\"all_true_selector\")\n", "  return rasp.SelectorWidth(all_true_selector).named(\"length\")\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "   return rasp.Selector<PERSON>idth(rasp.Select(\n", "      rasp.tokens, rasp.tokens, lambda k, q: len(q))).named(\"length\")\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/86 tracr/craft/chamber/categorical_mlp.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " :\n", "    return bases.BasisDirection(hidden_name, (x.name, x.value, y.name, y.value))\n", "\n", "  def from_hidden(h):\n", "    x_name, x_value, y_name, y_value = h.value\n", "    x_dir = bases.BasisDirection(x_name, x_value)\n", "    y_dir = bases.BasisDirection(y_name, y_value)\n", "    return x_dir, y_dir\n", "\n", "  hidden_dir = []\n", "  for dir1 in input1_space.basis:\n", "    for dir2 in input2_space.basis:\n", "      hidden_dir.append(to_hidden(dir1, dir2))\n", "  hidden_space = bases.VectorSpaceWithBasis(hidden_dir)\n", "\n", "  def logical_and(direction):\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     if direction in one_space:\n", "      out = bases.VectorInBasis(hidden_space.basis,\n", "                                -np.ones(hidden_space.num_dims))\n", "    elif direction in input1_space:\n", "      dir1 = direction\n", "      out = hidden_space.null_vector()\n", "      for dir2 in input2_space.basis:\n", "        out += hidden_space.vector_from_basis_direction(to_hidden(dir1, dir2))\n", "    else:\n", "      dir2 = direction\n", "      out = hidden_space.null_vector()\n", "      for dir1 in input1_space.basis:\n", "        out += hidden_space.vector_from_basis_direction(to_hidden(dir1, dir2))\n", "    return out\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     if direction in input_space:\n", "      x_dir, y_dir = from_hidden(direction)\n", "      return operation(x_dir, y_dir)\n", "    return one_space.null_vector()\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: facebookresearch_omnivore/13 omnivision/optim/optimizer.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " all-errors\n", "\n", "import fnmatch\n", "import itertools\n", "import logging\n", "from dataclasses import dataclass\n", "from typing import Any, Dict, Iterable, List, Optional, Set, Tuple, Union\n", "\n", "import hydra\n", "import torch\n", "import torch.nn as nn\n", "from omegaconf import DictConfig, MISSING\n", "\n", "from . import LARS, OmniOptimizer\n", "\n", "\n", "def create_lars_optimizer(params, opt, **lars_params):\n", "    optim = hydra.utils.instantiate(opt, params=params)\n", "    return LARS(optim, **lars_params)\n", "\n", "\n", "def validate_param_group_params(param_groups, model):\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     parameters = [set(param_group[\"params\"]) for param_group in param_groups]\n", "    model_parameters = {parameter for _, parameter in model.named_parameters()}\n", "    for p1, p2 in itertools.permutations(parameters, 2):\n", "        assert p1.isdisjoint(p2), \"Scheduler generated param_groups should be disjoint\"\n", "    assert (\n", "        set.union(*parameters) == model_parameters\n", "    ), \"Scheduler generated param_groups include all parameters of the model\"\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     for param_group in param_groups:\n", "        for param in param_group[\"params\"]:\n", "            if param not in model.parameters():\n", "                raise ValueError(\n", "                    f\"Optimizer option for {param_group['option']} \"\n", "                    f\"parameter_name {param} does not match any parameters in the model\"\n", "                )\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/2 lightweight_mmm/plot.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " omputes contribution for each sample, time, channel.\n", "\n", "  Serves as a helper function for making predictions for each channel, time\n", "  and estimate sample. It is meant to be used in creating media baseline\n", "  contribution dataframe and visualize media attribution over spend proportion\n", "  plot.\n", "\n", "  Args:\n", "    media_mix_model: Media mix model.\n", "\n", "  Returns:\n", "    Estimation of contribution for each sample, time, channel.\n", "\n", "  Raises:\n", "    NotFittedModelError: if the model is not fitted before computation\n", "  \"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "   if not hasattr(media_mix_model, \"trace\"):\n", "    raise lightweight_mmm.NotFittedModelError(\n", "        \"Model needs to be fit first before attempting to plot its fit.\")\n", "\n", "  if media_mix_model.trace[\"media_transformed\"].ndim > 3:\n", "    # s for samples, t for time, c for media channels, g for geo\n", "    einsum_str = \"stcg, scg->stcg\"\n", "  elif media_mix_model.trace[\"media_transformed\"].ndim == 3:\n", "    # s for samples, t for time, c for media channels\n", "    einsum_str = \"stc, sc->stc\"\n", "\n", "  media_contribution = jnp.einsum(einsum_str,\n", "                                  media_mix_model.trace[\"media_transformed\"],\n", "                                  media_mix_model.trace[\"coef_media\"])\n", "  if media_mix_model.trace[\"media_transformed\"].ndim > 3:\n", "    # Aggregate media channel contribution across geos.\n", "    media_contribution = media_contribution.sum(axis=-1)\n", "  return media_contribution\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "   if not hasattr(media_mix_model, \"trace\"):\n", "    raise NotFittedModelError(\n", "        \"LightweightMMM has not been fitted and cannot run estimations. \"\n", "        \"Please first fit the model.\")\n", "  return media_mix_model.trace[\"media_contribution\"][\"media_contribution\"]\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/51 lightweight_mmm/optimize_media_test.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " License for the specific language governing permissions and\n", "# limitations under the License.\n", "\n", "\"\"\"Tests for optimize_media.\"\"\"\n", "from unittest import mock\n", "\n", "from absl.testing import absltest\n", "from absl.testing import parameterized\n", "import jax\n", "import jax.numpy as jnp\n", "import numpy as np\n", "\n", "from lightweight_mmm import lightweight_mmm\n", "from lightweight_mmm import optimize_media\n", "from lightweight_mmm import preprocessing\n", "\n", "\n", "class OptimizeMediaTest(parameterized.TestCase):\n", "\n", "  @classmethod\n", "  def setUpClass(cls):\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     super(OptimizeMediaTest, cls).setUpClass()\n", "    cls.national_mmm = lightweight_mmm.LightweightMMM()\n", "    cls.national_mmm.fit(\n", "        media=jnp.ones((50, 5)),\n", "        target=jnp.ones(50),\n", "        media_prior=jnp.ones(5) * 50,\n", "        number_warmup=2,\n", "        number_samples=2,\n", "        number_chains=1)\n", "    cls.geo_mmm = lightweight_mmm.LightweightMMM()\n", "    cls.geo_mmm.fit(\n", "        media=jnp.ones((50, 5, 3)),\n", "        target=jnp.ones((50, 3)),\n", "        media_prior=jnp.ones(5) * 50,\n", "        number_warmup=2,\n", "        number_samples=2,\n", "        number_chains=1)\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     super().setUpClass()\n", "    cls.national_mmm = lightweight_mmm.LightweightMMM()\n", "    cls.geo_mmm = lightweight_mmm.LightweightMMM()\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/59 lightweight_mmm/lightweight_mmm.py\n", "Pass? OTHER\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " it=False, repr=False, hash=False, compare=True)\n", "  _extra_features: jnp.DeviceArray = dataclasses.field(\n", "      init=False, repr=False, hash=False, compare=True)\n", "  _target: jnp.DeviceArray = dataclasses.field(\n", "      init=False, repr=False, hash=False, compare=True)\n", "  _train_media_size: int = dataclasses.field(\n", "      init=False, repr=False, hash=True, compare=False)\n", "  _mcmc: numpyro.infer.MCMC = dataclasses.field(\n", "      init=False, repr=False, hash=False, compare=False)\n", "\n", "  def __post_init__(self):\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     if self.model_name not in _NAMES_TO_MODEL_TRANSFORMS:\n", "      raise ValueError(\"Model name passed not valid. Please use any of the\"\n", "                       \"following: 'hill_adstock', 'adstock', 'carryover'.\")\n", "    self._model_function = _MODEL_FUNCTION\n", "    self._model_transform_function = _NAMES_TO_MODEL_TRANSFORMS[self.model_name]\n", "    self._prior_names = models.MODEL_PRIORS_NAMES.union(\n", "        models.TRANSFORM_PRIORS_NAMES[self.model_name])\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     self.n_media_channels = self.media.shape[0]\n", "    self.n_geos = self.media.shape[1]\n", "    self.media_names = self.media.dtype.names\n", "    self._degrees_seasonality = self.media.shape[2]\n", "    self._weekday_seasonality = self.media.shape[3]\n", "    self._media_prior = self.media[:, :, :, :, -1]\n", "    self._extra_features = self.media[:, :, :, :, :-1]\n", "    self._target = self.media[:, :, :, :, -2]\n", "    self._train_media_size = self.media.shape[4]\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/60 lightweight_mmm/lightweight_mmm.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " MMM are arrays, which contain multiple values\n", "    and cannot be evaluated with the default __eq__ method. Second, some\n", "    attributes are initially undefined and only get values after fitting a\n", "    model. The latter is dealt with within this function, and the former within\n", "    the helper function _compare_equality_for_lmmm().\n", "\n", "    Args:\n", "      other: Dataclass to compare against.\n", "\n", "    Returns:\n", "      Boolean for whether self == other; NotImplemented if other is not a\n", "      LightweightMMM.\n", "    \"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "     if not isinstance(other, LightweightMMM):\n", "      return NotImplemented\n", "\n", "    def _create_list_of_attributes_to_compare(\n", "        mmm_instance: Any) -> Sequence[str]:\n", "      all_attributes_that_can_be_compared = sorted(\n", "          [x.name for x in dataclasses.fields(mmm_instance) if x.compare])\n", "      attributes_which_have_been_instantiated = [\n", "          x for x in all_attributes_that_can_be_compared\n", "          if hasattr(mmm_instance, x)\n", "      ]\n", "      return attributes_which_have_been_instantiated\n", "\n", "    self_attributes = _create_list_of_attributes_to_compare(self)\n", "    other_attributes = _create_list_of_attributes_to_compare(other)\n", "\n", "    return all(\n", "        _compare_equality_for_lmmm(getattr(self, a1), getattr(other, a2))\n", "        for a1, a2 in itertools.zip_longest(self_attributes, other_attributes))\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "     if not isinstance(other, LightweightMMM):\n", "      return NotImplemented\n", "    is_equal = _compare_equality_for_lmmm(self, other)\n", "    if not is_equal:\n", "      return False\n", "    if self.model_name == \"hill_adstock\":\n", "      is_equal = is_equal and self.n_media_channels == other.n_media_channels\n", "    return is_equal\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: leopard-ai_betty/10 betty/problems/problem.py\n", "Pass? PASSED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " ler is not None and \"scheduler\" in state_dict:\n", "            self.scheduler.load_state_dict(state_dict[\"scheduler\"])\n", "        if self._is_default_fp16() and \"scaler\" in state_dict:\n", "            self.scaler.load_state_dict(state_dict[\"scaler\"])\n", "\n", "    def configure_distributed_training(self, dictionary):\n", "        \"\"\"\n", "        Set the configuration for distributed training.\n", "\n", "        :param dictionary: Python dictionary of distributed training provided by Engine.\n", "        :type dictionary: dict\n", "        \"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "         self._strategy = dictionary[\"strategy\"]\n", "        self._backend = dictionary[\"backend\"]\n", "        self._world_size = dictionary[\"world_size\"]\n", "        self._rank = dictionary[\"rank\"]\n", "        self._local_rank = dictionary[\"local_rank\"]\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "         self._strategy = dictionary[\"strategy\"]\n", "        self._world_size = dictionary[\"world_size\"]\n", "        self._rank = dictionary[\"rank\"]\n", "        self._local_rank = dictionary[\"local_rank\"]\n", "        self._is_distributed = self._world_size > 1\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: lucidrains_imagen-pytorch/56 imagen_pytorch/trainer.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " et_number(unet_number)\n", "\n", "        assert not exists(self.only_train_unet_number) or self.only_train_unet_number == unet_number, 'you cannot only train on one unet at a time. you will need to save the trainer into a checkpoint, and resume training on a new unet'\n", "\n", "        self.only_train_unet_number = unet_number\n", "        self.imagen.only_train_unet_number = unet_number\n", "\n", "        if not exists(unet_number):\n", "            return\n", "\n", "        self.wrap_unet(unet_number)\n", "\n", "    def wrap_unet(self, unet_number):\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "         if hasattr(self, 'one_unet_wrapped'):\n", "            return\n", "\n", "        unet = self.imagen.get_unet(unet_number)\n", "        unet_index = unet_number - 1\n", "\n", "        optimizer = getattr(self, f'optim{unet_index}')\n", "        scheduler = getattr(self, f'scheduler{unet_index}')\n", "\n", "        if self.train_dl:\n", "            self.unet_being_trained, self.train_dl, optimizer = self.accelerator.prepare(unet, self.train_dl, optimizer)\n", "        else:\n", "            self.unet_being_trained, optimizer = self.accelerator.prepare(unet, optimizer)\n", "\n", "        if exists(scheduler):\n", "            scheduler = self.accelerator.prepare(scheduler)\n", "\n", "        setattr(self, f'optim{unet_index}', optimizer)\n", "        setattr(self, f'scheduler{unet_index}', scheduler)\n", "\n", "        self.one_unet_wrapped = True\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "         self.validate_unet_number(unet_number)\n", "        unet_index = unet_number - 1\n", "\n", "        unet = getattr(self, f'unet{unet_index}')\n", "\n", "        self.unet_being_trained = unet\n", "\n", "        self.imagen.unet_being_trained = unet\n", "\n", "        self.wrap_optimizer(unet_index)\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: maxhumber_redframes/33 redframes/core.py\n", "Pass? FAILED\n", "--------------------------------------------------------------------------------\n", "Last 500 tokens of prefix:\n", " t data:\n", "            self._data = PandasDataFrame()\n", "        if isinstance(data, dict):\n", "            self._data = PandasDataFrame(data)\n", "\n", "    def __eq__(self, rhs: Any) -> bool:\n", "        \"\"\"Check if two DataFrames are equal to each other\n", "\n", "        Example:\n", "\n", "        ```python\n", "        adf = rf.DataFrame({\"foo\": [1]})\n", "        bdf = rf.DataFrame({\"bar\": [1]})\n", "        cdf = rf.DataFrame({\"foo\": [1]})\n", "        print(adf == bdf)\n", "        print(adf == cdf)\n", "        # False\n", "        # True\n", "        ```\n", "        \"\"\"\n", "\n", "--------------------------------------------------------------------------------\n", "Ground truth:\n", "         if not isinstance(rhs, DataFrame):\n", "            return False\n", "        return self._data.equals(rhs._data)\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "         return self._data.obj == rhs\n", "\n"]}], "source": ["for patch in relevant_patches:\n", "    target_patch_id = patch['patch'].patch_id\n", "    print('-' * 80)\n", "    print('Patch id:', target_patch_id, patch['patch'].file_name)\n", "    print(f\"Pass? {[patch['_extra']['result'] for patch in hydra_out if patch['patch_id'] == target_patch_id][0]}\")\n", "    print('-' * 80)\n", "    # truncated_prefix = tokenizer.detokenize(patch['prompt_toks']).rsplit('<|ret-endofdoc|>')[-1]\n", "    print(f'Last 500 tokens of prefix:\\n {patch[\"prefix\"][-500:]}')\n", "    print('-' * 80)\n", "    print(f\"Ground truth:\\n {patch['patch'].original_patch_content}\")\n", "    print('-' * 80)\n", "    print(f\"Generation:\\n {patch['patch'].patch_content}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Details on sole passing generation"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "Ground Truth:\n", "\n", "        self._strategy = dictionary[\"strategy\"]\n", "        self._backend = dictionary[\"backend\"]\n", "        self._world_size = dictionary[\"world_size\"]\n", "        self._rank = dictionary[\"rank\"]\n", "        self._local_rank = dictionary[\"local_rank\"]\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "\n", "        self._strategy = dictionary[\"strategy\"]\n", "        self._world_size = dictionary[\"world_size\"]\n", "        self._rank = dictionary[\"rank\"]\n", "        self._local_rank = dictionary[\"local_rank\"]\n", "        self._is_distributed = self._world_size > 1\n", "\n", "--------------------------------------------------------------------------------\n", "Prompt:\n", "\n", "<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>betty/engine.py\n", "\n", "        # initialize\n", "        self.initialize()\n", "\n", "    def parse_config(self):\n", "        \"\"\"\n", "        Parse EngineConfig.\n", "        \"\"\"\n", "        self.train_iters = self.config.train_iters\n", "        self.valid_step = self.config.valid_step\n", "\n", "        self.logger_type = self.config.logger_type\n", "\n", "        self._roll_back = self.config.roll_back\n", "\n", "        self._strategy = self.config.strategy\n", "        self._backend = self.config.backend\n", "\n", "        if self.config.early_stopping:\n", "            self.early_stopping = EarlyStopping(\n", "<|ret-endofdoc|>betty/engine.py\n", "# Copyright <PERSON>\n", "#\n", "# This source code is licensed under the MIT license found in the\n", "# LICENSE file in the root directory of this source tree.\n", "\n", "import time\n", "\n", "import torch\n", "import torch.distributed as dist\n", "\n", "from betty.configs import EngineConfig\n", "from betty.logging import logger\n", "from betty.misc.early_stopping import EarlyStopping\n", "from betty.utils import log_from_loss_dict\n", "\n", "\n", "class Engine:\n", "    \"\"\"\n", "    ``Engine`` handles a dataflow graph based on the user-provided hierarchical problem\n", "    dependencies. It also provides a primitive for executing multilevel optimization.\n", "<|ret-endofdoc|>betty/configs/engine_dataclass.py\n", "from dataclasses import dataclass\n", "\n", "\n", "@dataclass\n", "class EngineConfig:\n", "    \"\"\"\n", "    Configuration for ``Engine``.\n", "    \"\"\"\n", "\n", "    train_iters: int = 50000\n", "    valid_step: int = 500\n", "\n", "    # logger\n", "    logger_type: str = \"none\"\n", "\n", "    # roll back\n", "    roll_back: bool = False\n", "\n", "    # distributed training\n", "    distributed: bool = False\n", "<|ret-endofdoc|>betty/envs/env_base.py\n", "        return patched_loader\n", "\n", "    def configure_device(self, device):\n", "        \"\"\"\n", "        Set the device for the current problem.\n", "        \"\"\"\n", "        self.device = device\n", "\n", "    def configure_distributed_training(self, dictionary):\n", "        \"\"\"\n", "        Set the configuration for distributed training.\n", "\n", "        :param dictionary: Python dictionary of distributed training provided by Engine.\n", "        :type dictionary: dict\n", "        \"\"\"\n", "        self._strategy = dictionary[\"strategy\"]\n", "        self._world_size = dictionary[\"world_size\"]\n", "        self._rank = dictionary[\"rank\"]\n", "        self._local_rank = dictionary[\"local_rank\"]\n", "<|ret-endofdoc|>betty/problems/problem.py\n", ":\n", "            torch.autograd.backward(\n", "                loss,\n", "                inputs=params,\n", "                create_graph=create_graph,\n", "                retain_graph=retain_graph,\n", "            )\n", "\n", "        # indirect grad: best-response <PERSON><PERSON>\n", "        if self._config.first_order:\n", "            for idx, path in enumerate(paths):\n", "                retain_graph_implicit = False if idx == len(paths) - 1 else True\n", "                do_sync = bool(\n", "                    idx == len(paths) - 1 and self.gradient_accumulation_boundary()\n", "                )\n", "                grads = get_grads(loss, path, retain_graph_implicit, do_sync)\n", "                if not do_sync:\n", "                    self.set_grads(params, grads)\n", "\n", "    def set_grads(self, params, grads):\n", "        \"\"\"\n", "        Set gradients for trainable parameters. ``params.grad = grads``\n", "\n", "        :param params: Trainable parameters\n", "        :type params: Sequence of Tensor\n", "        :param grads: Calculated gradient\n", "        :type grads: Sequence of Tensor\n", "        \"\"\"\n", "        for param, grad in zip(params, grads):\n", "            if grad is not None:\n", "                if hasattr(param, \"grad\") and param.grad is not None:\n", "                    param.grad = param.grad + grad\n", "                else:\n", "                    param.grad = grad\n", "\n", "    def synchronize_params(self, params):\n", "        \"\"\"\n", "        synchronize parameters across distributed data-parallel processes\n", "        \"\"\"\n", "        if self._world_size > 1 and self._strategy not in [\"fsdp\", \"accelerate\"]:\n", "            for param in params:\n", "                dist.broadcast(param.data, 0)\n", "\n", "    @abc.abstractmethod\n", "    def optimizer_step(self, *args, **kwargs):\n", "        \"\"\"\n", "        Update weights as in <PERSON><PERSON><PERSON><PERSON><PERSON>'s native ``optim.step()``\n", "        \"\"\"\n", "        raise NotImplementedError\n", "\n", "    def zero_grad(self):\n", "        \"\"\"\n", "        Set gradients for trainable parameters for the current problem to 0.\n", "        Similar with <PERSON><PERSON><PERSON><PERSON><PERSON>'s ``optim.zero_grad()`` or ``module.zero_grad()``.\n", "        \"\"\"\n", "        for param in list(self.trainable_parameters()):\n", "            if hasattr(param, \"grad\"):\n", "                del param.grad\n", "\n", "    def clip_grad(self):\n", "        \"\"\"\n", "        Perform gradient clipping based on the norm provided by Config\n", "        \"\"\"\n", "        if self._strategy != \"fsdp\":\n", "            torch.nn.utils.clip_grad_norm_(\n", "                parameters=self.trainable_parameters(), max_norm=self.gradient_clipping\n", "            )\n", "        else:\n", "            self.module.clip_grad_norm_(max_norm=self.gradient_clipping)\n", "\n", "    def state_dict(self):\n", "        \"\"\"\n", "        Return all states involved in ``Problem`` with a Python dictionary. By default, it\n", "        includes ``self.module.state_dict`` and ``self.optimizer.state_dict``. Depending on users'\n", "        configurations, it may include ``self.scheuler.state_dict`` (lr scheduler) and\n", "        ``self.scaler.state_dict`` (fp16 training)\n", "        \"\"\"\n", "        state_dict = {}\n", "        state_dict[\"module\"] = self.module.state_dict()\n", "        state_dict[\"optimizer\"] = self.optimizer.state_dict()\n", "        if self.scheduler is not None:\n", "            state_dict[\"scheduler\"] = self.scheduler.state_dict()\n", "        if self._is_default_fp16():\n", "            state_dict[\"scaler\"] = self.scaler.state_dict()\n", "\n", "        return state_dict\n", "\n", "    def load_state_dict(self, state_dict):\n", "        \"\"\"Load the state for the ``Problem``\n", "\n", "        Args:\n", "            state_dict (dict): Python dictionary of Problem states.\n", "        \"\"\"\n", "        self.module.load_state_dict(state_dict[\"module\"])\n", "        self.optimizer.load_state_dict(state_dict[\"optimizer\"])\n", "        if self.scheduler is not None and \"scheduler\" in state_dict:\n", "            self.scheduler.load_state_dict(state_dict[\"scheduler\"])\n", "        if self._is_default_fp16() and \"scaler\" in state_dict:\n", "            self.scaler.load_state_dict(state_dict[\"scaler\"])\n", "\n", "    def configure_distributed_training(self, dictionary):\n", "        \"\"\"\n", "        Set the configuration for distributed training.\n", "\n", "        :param dictionary: Python dictionary of distributed training provided by Engine.\n", "        :type dictionary: dict\n", "        \"\"\"\n", "\n"]}], "source": ["target_patch_id = 'leopard-ai_betty/10'\n", "target_patch = [patch for patch in relevant_patches if patch['patch'].patch_id == target_patch_id][0]\n", "\n", "print('-'* 80)\n", "print('Ground Truth:\\n')\n", "print(target_patch['patch'].original_patch_content)\n", "print('-'* 80)\n", "print('Generation:\\n')\n", "print(target_patch['patch'].patch_content)\n", "print('-' * 80)\n", "print('Prompt:\\n')\n", "print(tokenizer.detokenize(target_patch['prompt_toks']))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Observation: \n", "leopard-ai_betty/10 because it found another example of configure_distributed_training to copy."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Investigate how often correct retrievals have at least rank of 10"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/105 tracr/craft/bases.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 2)\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/114 tracr/craft/transformers.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 2)\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/36 tracr/rasp/rasp_test.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 3)\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/57 tracr/compiler/rasp_to_craft_integration_test.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 2)\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/58 tracr/compiler/lib.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 3)\n", "--------------------------------------------------------------------------------\n", "Patch id: deepmind_tracr/86 tracr/craft/chamber/categorical_mlp.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 3)\n", "--------------------------------------------------------------------------------\n", "Patch id: facebookresearch_omnivore/13 omnivision/optim/optimizer.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 1)\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/2 lightweight_mmm/plot.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 1)\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/51 lightweight_mmm/optimize_media_test.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 1)\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/59 lightweight_mmm/lightweight_mmm.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 4)\n", "--------------------------------------------------------------------------------\n", "Patch id: google_lightweight_mmm/60 lightweight_mmm/lightweight_mmm.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 1)\n", "--------------------------------------------------------------------------------\n", "Patch id: leopard-ai_betty/10 betty/problems/problem.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 1)\n", "--------------------------------------------------------------------------------\n", "Patch id: lucidrains_imagen-pytorch/56 imagen_pytorch/trainer.py\n", "Retrieval chunks found: 0 (in prefix) and 1 (via retrieval) (out of 2)\n", "Chunk: \n", "        # default to device of unets passed in\n", "\n", "        self.to(next(self.unets.parameters()).device)\n", "\n", "    def force_unconditional_(self):\n", "        self.condition_on_text = False\n", "        self.unconditional = True\n", "\n", "        for unet in self.unets:\n", "            unet.cond_on_text = False\n", "\n", "    @property\n", "    def device(self):\n", "        return self._temp.device\n", "\n", "    def get_unet(self, unet_number):\n", "        assert 0 < unet_number <= len(self.unets)\n", "        index = unet_number - 1\n", "\n", "\n", "--------------------------------------------------------------------------------\n", "Patch id: maxhumber_redframes/33 redframes/core.py\n", "Retrieval chunks found: 0 (in prefix) and 0 (via retrieval) (out of 1)\n"]}], "source": ["from collections import defaultdict\n", "patch_id_to_correct_retrieved_chunks = defaultdict(list)\n", "\n", "for patch in relevant_patches:\n", "    target_patch_id = patch['patch'].patch_id\n", "    print('-'* 80)\n", "    print('Patch id:', target_patch_id, patch['patch'].file_name)\n", "    target_ret_chunks = set(copy.deepcopy(patch_ret_chunks[target_patch_id]))\n", "\n", "    truncated_prefix = tokenizer.detokenize(target_patch['prompt_toks']).rsplit('<|ret-endofdoc|>')[-1]\n", "    \n", "    num_prefix_chunks_found = 0\n", "    new_target_ret_chunks = set()\n", "    for target_line in target_ret_chunks:\n", "        if target_line not in truncated_prefix:\n", "            new_target_ret_chunks.add(target_line)\n", "        else:\n", "            num_prefix_chunks_found += 1\n", "    target_ret_chunks = new_target_ret_chunks\n", "\n", "    for chunk in patch['filtered_chunks']:\n", "        # new_target_ret_chunk is target_ret_chunk after removing correct chunks\n", "        new_target_ret_chunks = set()\n", "        for target_line in target_ret_chunks:\n", "            # If chunk found, then DONT add it to new target_ret_chunks\n", "            if target_line in chunk.text:\n", "                patch_id_to_correct_retrieved_chunks[target_patch_id].append(chunk.text)\n", "            elif target_line not in chunk.text:\n", "                new_target_ret_chunks.add(target_line)\n", "        target_ret_chunks = new_target_ret_chunks\n", "    \n", "    number_chunks_found = len(set(patch_ret_chunks[target_patch_id]).difference(target_ret_chunks))\n", "    print(f'Retrieval chunks found: {num_prefix_chunks_found} (in prefix) and {number_chunks_found-num_prefix_chunks_found} (via retrieval) (out of {len(patch_ret_chunks[target_patch_id])})')\n", "\n", "    for chunk_text in patch_id_to_correct_retrieved_chunks[target_patch_id]:\n", "        print('Chunk:', chunk_text)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Investigate sole retrieval with at least rank 10"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--------------------------------------------------------------------------------\n", "Ground Truth:\n", "\n", "        if hasattr(self, 'one_unet_wrapped'):\n", "            return\n", "\n", "        unet = self.imagen.get_unet(unet_number)\n", "        unet_index = unet_number - 1\n", "\n", "        optimizer = getattr(self, f'optim{unet_index}')\n", "        scheduler = getattr(self, f'scheduler{unet_index}')\n", "\n", "        if self.train_dl:\n", "            self.unet_being_trained, self.train_dl, optimizer = self.accelerator.prepare(unet, self.train_dl, optimizer)\n", "        else:\n", "            self.unet_being_trained, optimizer = self.accelerator.prepare(unet, optimizer)\n", "\n", "        if exists(scheduler):\n", "            scheduler = self.accelerator.prepare(scheduler)\n", "\n", "        setattr(self, f'optim{unet_index}', optimizer)\n", "        setattr(self, f'scheduler{unet_index}', scheduler)\n", "\n", "        self.one_unet_wrapped = True\n", "\n", "--------------------------------------------------------------------------------\n", "Generation:\n", "\n", "        self.validate_unet_number(unet_number)\n", "        unet_index = unet_number - 1\n", "\n", "        unet = getattr(self, f'unet{unet_index}')\n", "\n", "        self.unet_being_trained = unet\n", "\n", "        self.imagen.unet_being_trained = unet\n", "\n", "        self.wrap_optimizer(unet_index)\n", "\n", "--------------------------------------------------------------------------------\n", "Prompt:\n", "\n", "<|startofsequence|><|pref-repo-large|><|pref-repo-stars-high|>imagen_pytorch/elucidated_imagen.py\n", "    def one_unet_in_gpu(self, unet_number = None, unet = None):\n", "        assert exists(unet_number) ^ exists(unet)\n", "\n", "        if exists(unet_number):\n", "            unet = self.unets[unet_number - 1]\n", "\n", "        devices = [module_device(unet) for unet in self.unets]\n", "        self.unets.cpu()\n", "        unet.to(self.device)\n", "\n", "        yield\n", "\n", "        for unet, device in zip(self.unets, devices):\n", "            unet.to(device)\n", "\n", "    # overriding state dict functions\n", "\n", "    def state_dict(self, *args, **kwargs):\n", "        self.reset_unets_all_one_device()\n", "        return super().state_dict(*args, **kwargs)\n", "<|ret-endofdoc|>imagen_pytorch/elucidated_imagen.py\n", "\n", "        # default to device of unets passed in\n", "\n", "        self.to(next(self.unets.parameters()).device)\n", "\n", "    def force_unconditional_(self):\n", "        self.condition_on_text = False\n", "        self.unconditional = True\n", "\n", "        for unet in self.unets:\n", "            unet.cond_on_text = False\n", "\n", "    @property\n", "    def device(self):\n", "        return self._temp.device\n", "\n", "    def get_unet(self, unet_number):\n", "        assert 0 < unet_number <= len(self.unets)\n", "        index = unet_number - 1\n", "\n", "<|ret-endofdoc|>imagen_pytorch/trainer.py\n", "\n", "    # helper print\n", "\n", "    def print(self, msg):\n", "        if not self.is_main:\n", "            return\n", "\n", "        if not self.verbose:\n", "            return\n", "\n", "        return self.accelerator.print(msg)\n", "\n", "    # validating the unet number\n", "\n", "    def validate_unet_number(self, unet_number = None):\n", "        if self.num_unets == 1:\n", "            unet_number = default(unet_number, 1)\n", "\n", "        assert 0 < unet_number <= self.num_unets, f'unet number should be in between 1 and {self.num_unets}'\n", "        return unet_number\n", "<|ret-endofdoc|>imagen_pytorch/trainer.py\n", "et_eps,\n", "                    betas = (beta1, beta2),\n", "                    **kwargs\n", "                )\n", "\n", "            if self.use_ema:\n", "                self.ema_unets.append(EMA(unet, **ema_kwargs))\n", "\n", "            scaler = GradScaler(enabled = grad_scaler_enabled)\n", "\n", "            scheduler = warmup_scheduler = None\n", "\n", "            if exists(unet_cosine_decay_max_steps):\n", "                scheduler = CosineAnnealingLR(optimizer, T_max = unet_cosine_decay_max_steps)\n", "\n", "            if exists(unet_warmup_steps):\n", "                warmup_scheduler = warmup.LinearWarmup(optimizer, warmup_period = unet_warmup_steps)\n", "\n", "                if not exists(scheduler):\n", "                    scheduler = LambdaLR(optimizer, lr_lambda = lambda step: 1.0)\n", "\n", "            # set on object\n", "\n", "            setattr(self, f'optim{ind}', optimizer) # cannot use pytorch ModuleList for some reason with optimizers\n", "            setattr(self, f'scaler{ind}', scaler)\n", "            setattr(self, f'scheduler{ind}', scheduler)\n", "            setattr(self, f'warmup{ind}', warmup_scheduler)\n", "\n", "        # gradient clipping if needed\n", "\n", "        self.max_grad_norm = max_grad_norm\n", "\n", "        # step tracker and misc\n", "\n", "        self.register_buffer('steps', torch.tensor([0] * self.num_unets))\n", "\n", "        self.verbose = verbose\n", "\n", "        # automatic set devices based on what accelerator decided\n", "\n", "        self.imagen.to(self.device)\n", "        self.to(self.device)\n", "\n", "        # checkpointing\n", "\n", "        assert not (exists(checkpoint_path) ^ exists(checkpoint_every))\n", "        self.checkpoint_path = checkpoint_path\n", "        self.checkpoint_every = checkpoint_every\n", "        self.max_checkpoints_keep = max_checkpoints_keep\n", "\n", "        self.can_checkpoint = self.is_local_main if isinstance(checkpoint_fs, LocalFileSystem) else self.is_main\n", "\n", "        if exists(checkpoint_path) and self.can_checkpoint:\n", "            bucket = url_to_bucket(checkpoint_path)\n", "\n", "            if not self.fs.exists(bucket):\n", "                self.fs.mkdir(bucket)\n", "\n", "            self.load_from_checkpoint_folder()\n", "\n", "        # only allowing training for unet\n", "\n", "        self.only_train_unet_number = only_train_unet_number\n", "        self.prepared = False\n", "\n", "\n", "    def prepare(self):\n", "        assert not self.prepared, f'The trainer is allready prepared'\n", "        self.validate_and_set_unet_being_trained(self.only_train_unet_number)\n", "        self.prepared = True\n", "    # computed values\n", "\n", "    @property\n", "    def device(self):\n", "        return self.accelerator.device\n", "\n", "    @property\n", "    def is_distributed(self):\n", "        return not (self.accelerator.distributed_type == DistributedType.NO and self.accelerator.num_processes == 1)\n", "\n", "    @property\n", "    def is_main(self):\n", "        return self.accelerator.is_main_process\n", "\n", "    @property\n", "    def is_local_main(self):\n", "        return self.accelerator.is_local_main_process\n", "\n", "    @property\n", "    def unwrapped_unet(self):\n", "        return self.accelerator.unwrap_model(self.unet_being_trained)\n", "\n", "    # optimizer helper functions\n", "\n", "    def get_lr(self, unet_number):\n", "        self.validate_unet_number(unet_number)\n", "        unet_index = unet_number - 1\n", "\n", "        optim = getattr(self, f'optim{unet_index}')\n", "\n", "        return optim.param_groups[0]['lr']\n", "\n", "    # function for allowing only one unet from being trained at a time\n", "\n", "    def validate_and_set_unet_being_trained(self, unet_number = None):\n", "        if exists(unet_number):\n", "            self.validate_unet_number(unet_number)\n", "\n", "        assert not exists(self.only_train_unet_number) or self.only_train_unet_number == unet_number, 'you cannot only train on one unet at a time. you will need to save the trainer into a checkpoint, and resume training on a new unet'\n", "\n", "        self.only_train_unet_number = unet_number\n", "        self.imagen.only_train_unet_number = unet_number\n", "\n", "        if not exists(unet_number):\n", "            return\n", "\n", "        self.wrap_unet(unet_number)\n", "\n", "    def wrap_unet(self, unet_number):\n", "\n", "Target retrieved chunk:\n", "\n", "\n", "        # default to device of unets passed in\n", "\n", "        self.to(next(self.unets.parameters()).device)\n", "\n", "    def force_unconditional_(self):\n", "        self.condition_on_text = False\n", "        self.unconditional = True\n", "\n", "        for unet in self.unets:\n", "            unet.cond_on_text = False\n", "\n", "    @property\n", "    def device(self):\n", "        return self._temp.device\n", "\n", "    def get_unet(self, unet_number):\n", "        assert 0 < unet_number <= len(self.unets)\n", "        index = unet_number - 1\n", "\n", "\n"]}], "source": ["target_patch_id = 'lucidrains_imagen-pytorch/56'\n", "target_patch = [patch for patch in relevant_patches if patch['patch'].patch_id == target_patch_id][0]\n", "\n", "print('-'* 80)\n", "print('Ground Truth:\\n')\n", "print(target_patch['patch'].original_patch_content)\n", "print('-'* 80)\n", "print('Generation:\\n')\n", "print(target_patch['patch'].patch_content)\n", "print('-' * 80)\n", "print('Prompt:\\n')\n", "print(tokenizer.detokenize(target_patch['prompt_toks']))\n", "print('Target retrieved chunk:\\n')\n", "print(patch_id_to_correct_retrieved_chunks[target_patch_id][0])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This is a good retrieval! (But it is not sufficient to pass the test. More stuff needed to be retrieved.)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}