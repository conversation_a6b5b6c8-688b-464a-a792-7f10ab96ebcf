{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import glob\n", "import pandas as pd\n", "from git import Repo\n", "import time\n", "from pygit2 import Repository\n", "import os\n", "import sys\n", "from typing import Optional\n", "import chardet\n", "from difflib import ndiff, unified_diff\n", "from pathlib import Path\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["LANGUAGES = [\n", "    \"C\",\n", "    \"C++\",\n", "    \"Go\",\n", "    \"Java\",\n", "    \"Javascript\",\n", "    \"Python\"\n", "]\n", "\n", "LANGUAGE_TO_EXTENSION = {\n", "    \"C++\": [\".c\", \".cpp\", \".h\"],\n", "    \"Go\": [\".go\"],\n", "    \"Java\": [\".java\", \".jav\"],\n", "    \"Javascript\": [\".js\"],\n", "    \"Python\": [\".py\"],\n", "}\n", "\n", "BAD_EXTENSIONS = [\n", "    \".app\",\n", "    \".bin\",\n", "    \".bmp\",\n", "    \".bz2\",\n", "    \".class\",\n", "    \".csv\",\n", "    \".dat\",\n", "    \".db\",\n", "    \".dll\",\n", "    \".dylib\",\n", "    \".egg\",\n", "    \".eot\",\n", "    \".exe\",\n", "    \".gif\",\n", "    \".gitignore\",\n", "    \".glif\",\n", "    \".gradle\",\n", "    \".gz\",\n", "    \".ico\",\n", "    \".jar\",\n", "    \".jpeg\",\n", "    \".jpg\",\n", "    \".lo\",\n", "    \".lock\",\n", "    \".log\",\n", "    \".tlog\",\n", "    \".mp3\",\n", "    \".mp4\",\n", "    \".nar\",\n", "    \".nes\",\n", "    \".o\",\n", "    \".ogg\",\n", "    \".otf\",\n", "    \".p\",\n", "    \".pdf\",\n", "    \".png\",\n", "    \".pickle\",\n", "    \".pkl\",\n", "    \".pyc\",\n", "    \".pyd\",\n", "    \".pyo\",\n", "    \".rkt\",\n", "    \".ser\",\n", "    \".so\",\n", "    \".ss\",\n", "    \".svg\",\n", "    \".tar\",\n", "    \".tsv\",\n", "    \".ttf\",\n", "    \".umap\",\n", "    \".war\",\n", "    \".webm\",\n", "    \".woff\",\n", "    \".woff2\",\n", "    \".xcuserstate\",\n", "    \".xz\",\n", "    \".zip\",\n", "    \".zst\",\n", "]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def _decode_byte_string(buf) -> Optional[str]:\n", "    \"\"\"Return the contents as a string.\n", "\n", "    The contents is expected to contain text encoded in utf-8, but other encodings\n", "    may also work if they can be detected automatically. Note, this\n", "    determination may have false negatives, meaning that the actual contents encoding\n", "    could not accurately be determined.\n", "\n", "    Args:\n", "        buf: byte string\n", "\n", "    Returns:\n", "        Decoded string upon success, None on failure.\n", "    \"\"\"\n", "    # discerns filetype with mime and reads text from file if possible\n", "    try:\n", "        buf = buf.decode(\"UTF-8\")\n", "        return buf\n", "    except UnicodeDecodeError:\n", "        # bad encoding, try different encoding\n", "        try:\n", "            enc = chardet.detect(buf)\n", "            if enc[\"encoding\"] is None:\n", "                return\n", "            buf = buf.decode(enc[\"encoding\"])\n", "            return buf\n", "        except UnicodeDecodeError:\n", "            return\n", "    except KeyboardInterrupt:\n", "        sys.exit()\n", "\n", "def fast_ndiff(prev_lines, new_lines):\n", "    \"\"\"unified_diff is way faster than ndiff because ignores but it grabs local diffs (eg changes with nearby context)\n", "    rather than file-level diffs. By seeing the context size to 1e9\n", "    \"\"\"\n", "    diff = list(unified_diff(prev_lines, new_lines, n=1e9))\n", "    assert (\n", "        diff[0].startswith('---')\n", "        and diff[1].startswith('+++')\n", "        and diff[2].startswith('@@')\n", "    ), diff[:5]\n", "    diff = diff[3:]\n", "    return diff\n", "\n", "def guess_lang_from_fp(fname):\n", "    fname_extension = os.path.splitext(fname)[1].lower()\n", "\n", "    for lang, valid_extensions in LANGUAGE_TO_EXTENSION.items():\n", "        if fname_extension in valid_extensions:\n", "            return lang\n", "\n", "    return None\n", "\n", "def _should_ignore_file(fname):\n", "    \"\"\"Determines what kinds of files we don't need to track (e.g. non-code files).\n", "\n", "    Args:\n", "        fname: Base filename\n", "\n", "    Returns:\n", "        Boolean that is True iff the file should not be tracked.\n", "    \"\"\"\n", "    if (\n", "        \".git\" in fname\n", "        or fname[0] == \".\"\n", "        or \"LICENSE\" in fname\n", "        or \"node_modules\" in fname\n", "        or \".min.\" in fname\n", "        or os.path.splitext(fname)[1].lower() in BAD_EXTENSIONS\n", "    ):\n", "        return True\n", "    else:\n", "        return False"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/logs/logs-*.log"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "# Compute time to process each repo (including deletion time)\n", "###\n", "\n", "log_files = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/logs/logs-*.log')\n", "counts = []\n", "for log_file in log_files:\n", "    with open(log_file) as f:\n", "        for line in f.readlines():\n", "            if '<PERSON><PERSON> processed' not in line:\n", "                continue\n", "            fp = line.split('<PERSON>o processed: ', 1)[1].split(' ',1)[0][:-1]\n", "            count = line.split('chunks in ', 1)[1].split(' ', 1)[0]\n", "            counts.append({'fp': fp, 'processs_time': float(count)})\n", "\n", "pd.Data<PERSON>rame(counts).describe()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(counts)\n", "#print(df)\n", "\n", "for i in range(10):\n", "    print(df.loc[df['processs_time'] > 25].iloc[i]['fp'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "# Look at mean num_commits_processed\n", "###\n", "paths = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/metadata*.parquet')\n", "paths = list(paths)\n", "df = pd.read_parquet(paths[0])\n", "for path in paths[1:]:\n", "    df = pd.concat((df, pd.read_parquet(path)))\n", "\n", "df['num_commits_processed'].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "# Compute num computes timed out\n", "###\n", "log_paths = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v3/logs/logs-*.log')\n", "processed_repos = 0\n", "timed_out = 0\n", "for log_file in log_paths:\n", "    with open(log_file) as f:\n", "        for line in f.readlines():\n", "            if 'Processing repo' in line:\n", "                processed_repos += 1\n", "            <PERSON><PERSON> 'Timed out processing' in line:\n", "                timed_out += 1\n", "\n", "print(timed_out/processed_repos)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v4_morelogs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "# Commit level stats\n", "###\n", "fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v4_morelogs/commit_metadata_shard*.parquet')\n", "df = pd.read_parquet(fps[0])\n", "for fp in fps[1:]:\n", "    df = pd.concat((df, pd.read_parquet(fp)))\n", "\n", "# fractions of time\n", "df['tokenizer_duration/commit_process_time'] = df['tokenizer_duration']/df['commit_process_time']\n", "df['get_chunks_duration/commit_process_time'] = df['get_chunks_duration']/df['commit_process_time']\n", "df['diffing_duration/commit_process_time'] = df['diffing_duration']/df['commit_process_time']\n", "df['decoding_bytes_duration/commit_process_time'] = df['decoding_bytes_duration']/df['commit_process_time']\n", "df['ndiff_duration/commit_process_time'] = df['ndiff_duration']/df['commit_process_time']\n", "\n", "# tokens saved\n", "df['tokens_saved/tokens_tokenized'] = df['tokens_saved']/df['tokens_tokenized']\n", "\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "# Compute repo-level statistics\n", "###\n", "fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v4_morelogs/repo_metadata_shard*.parquet')\n", "df = pd.read_parquet(fps[0])\n", "for fp in fps[1:]:\n", "    df = pd.concat((df, pd.read_parquet(fp)))\n", "\n", "df['leftover_commits'] = df['total_commits'] - df['num_commits_processed']\n", "\n", "print(df.describe())"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v5_fastndiff"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          commit_idx  diffing_duration  tokenizer_duration   tokens_saved  \\\n", "count  659943.000000     659943.000000       659943.000000  659943.000000   \n", "mean        9.533578          0.187546            0.093284     595.191326   \n", "std        24.159380          0.121927            0.316872    5532.827738   \n", "min         0.000000          0.022178            0.000000       0.000000   \n", "25%         2.000000          0.152115            0.000000       0.000000   \n", "50%         5.000000          0.177837            0.000000       0.000000   \n", "75%        13.000000          0.206691            0.054857     371.000000   \n", "max       999.000000          8.674790           38.791734  856183.000000   \n", "\n", "       tokens_tokenized  ndiff_duration  decoding_bytes_duration  \\\n", "count      6.599430e+05   659943.000000            659943.000000   \n", "mean       1.876890e+03        0.001298                 0.003794   \n", "std        8.615755e+03        0.015108                 0.053780   \n", "min        0.000000e+00        0.000000                 0.000000   \n", "25%        0.000000e+00        0.000000                 0.000000   \n", "50%        0.000000e+00        0.000000                 0.000000   \n", "75%        1.240000e+03        0.000859                 0.000529   \n", "max        1.016813e+06        2.238373                 7.328176   \n", "\n", "       num_exceptions  commit_process_time  get_chunks_duration  \\\n", "count   659943.000000        659943.000000        659929.000000   \n", "mean         0.000021             0.346545             0.008095   \n", "std          0.004606             0.397076             0.037762   \n", "min          0.000000             0.034178             0.000411   \n", "25%          0.000000             0.180526             0.000710   \n", "50%          0.000000             0.227849             0.000794   \n", "75%          0.000000             0.385066             0.010036   \n", "max          1.000000            42.783255             4.278370   \n", "\n", "       tokenizer_duration/commit_process_time  \\\n", "count                           659943.000000   \n", "mean                                 0.126946   \n", "std                                  0.219132   \n", "min                                  0.000000   \n", "25%                                  0.000000   \n", "50%                                  0.000000   \n", "75%                                  0.180793   \n", "max                                  0.965323   \n", "\n", "       get_chunks_duration/commit_process_time  \\\n", "count                            659929.000000   \n", "mean                                  0.019643   \n", "std                                   0.034053   \n", "min                                   0.000089   \n", "25%                                   0.003513   \n", "50%                                   0.004659   \n", "75%                                   0.019927   \n", "max                                   0.891879   \n", "\n", "       diffing_duration/commit_process_time  \\\n", "count                         659943.000000   \n", "mean                               0.734956   \n", "std                                0.279955   \n", "min                                0.011757   \n", "25%                                0.472308   \n", "50%                                0.902685   \n", "75%                                0.964164   \n", "max                                0.999331   \n", "\n", "       decoding_bytes_duration/commit_process_time  \\\n", "count                                659943.000000   \n", "mean                                      0.004775   \n", "std                                       0.026320   \n", "min                                       0.000000   \n", "25%                                       0.000000   \n", "50%                                       0.000000   \n", "75%                                       0.001510   \n", "max                                       0.955545   \n", "\n", "       ndiff_duration/commit_process_time  tokens_saved/tokens_tokenized  \n", "count                       659943.000000                  258327.000000  \n", "mean                             0.001736                       0.485246  \n", "std                              0.006489                       0.386085  \n", "min                              0.000000                       0.000000  \n", "25%                              0.000000                       0.142604  \n", "50%                              0.000000                       0.372762  \n", "75%                              0.002574                       0.834558  \n", "max                              0.759261                       1.325700  \n"]}], "source": ["###\n", "# Commit level stats for new fast ndiffing\n", "###\n", "fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v5_fastndiff/commit_metadata_shard*.parquet')\n", "df = pd.read_parquet(fps[0])\n", "for fp in fps[1:]:\n", "    df = pd.concat((df, pd.read_parquet(fp)))\n", "\n", "# fractions of time\n", "df['tokenizer_duration/commit_process_time'] = df['tokenizer_duration']/df['commit_process_time']\n", "df['get_chunks_duration/commit_process_time'] = df['get_chunks_duration']/df['commit_process_time']\n", "df['diffing_duration/commit_process_time'] = df['diffing_duration']/df['commit_process_time']\n", "df['decoding_bytes_duration/commit_process_time'] = df['decoding_bytes_duration']/df['commit_process_time']\n", "df['ndiff_duration/commit_process_time'] = df['ndiff_duration']/df['commit_process_time']\n", "\n", "# tokens saved\n", "df['tokens_saved/tokens_tokenized'] = df['tokens_saved']/df['tokens_tokenized']\n", "\n", "print(df.describe())"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### /mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v6_pygit2_60sec_timeout/"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          commit_idx  diffing_duration  tokenizer_duration  tokens_saved  \\\n", "count  112672.000000     112672.000000       112672.000000  1.126720e+05   \n", "mean       57.588176          0.001380            0.168344  7.776626e+02   \n", "std       134.875361          0.009057            0.952101  1.698423e+04   \n", "min         0.000000          0.000144            0.000000  0.000000e+00   \n", "25%         3.000000          0.000254            0.000000  0.000000e+00   \n", "50%        11.000000          0.000338            0.000000  0.000000e+00   \n", "75%        37.000000          0.000613            0.047604  2.710000e+02   \n", "max       999.000000          0.756504           52.282403  3.584191e+06   \n", "\n", "       tokens_tokenized  patch_read_duration  num_exceptions  \\\n", "count      1.126720e+05        112672.000000        112672.0   \n", "mean       2.991806e+03             0.000474             0.0   \n", "std        2.927561e+04             0.003988             0.0   \n", "min        0.000000e+00             0.000000             0.0   \n", "25%        0.000000e+00             0.000000             0.0   \n", "50%        0.000000e+00             0.000000             0.0   \n", "75%        1.078250e+03             0.000139             0.0   \n", "max        5.080450e+06             0.381409             0.0   \n", "\n", "       commit_process_time  get_chunks_duration  \\\n", "count        112672.000000        112672.000000   \n", "mean              0.229508             0.008731   \n", "std               1.136476             0.070223   \n", "min               0.000883             0.000135   \n", "25%               0.003170             0.000210   \n", "50%               0.019448             0.000297   \n", "75%               0.113153             0.004603   \n", "max              54.156530            14.170546   \n", "\n", "       tokenizer_duration/commit_process_time  \\\n", "count                           112672.000000   \n", "mean                                 0.251672   \n", "std                                  0.380407   \n", "min                                  0.000000   \n", "25%                                  0.000000   \n", "50%                                  0.000000   \n", "75%                                  0.653010   \n", "max                                  0.996587   \n", "\n", "       get_chunks_duration/commit_process_time  \\\n", "count                            112672.000000   \n", "mean                                  0.084376   \n", "std                                   0.120889   \n", "min                                   0.000009   \n", "25%                                   0.014525   \n", "50%                                   0.046162   \n", "75%                                   0.109637   \n", "max                                   0.970163   \n", "\n", "       diffing_duration/commit_process_time  tokens_saved/tokens_tokenized  \n", "count                         112672.000000                   38599.000000  \n", "mean                               0.080264                       0.429702  \n", "std                                0.121027                       0.384196  \n", "min                                0.000005                       0.000000  \n", "25%                                0.003773                       0.100221  \n", "50%                                0.027685                       0.290011  \n", "75%                                0.125571                       0.725642  \n", "max                                0.990929                       1.240506  \n"]}], "source": ["###\n", "# Commit level stats for new fast diffing\n", "###\n", "fps = glob.glob('/mnt/efs/augment-lga1-nvme/raw_diff_dense_retrieval_v6_pygit2_60sec_timeout/commit_metadata_shard*.parquet')\n", "df = pd.read_parquet(fps[0])\n", "for fp in fps[1:]:\n", "    df = pd.concat((df, pd.read_parquet(fp)))\n", "\n", "# fractions of time\n", "df['tokenizer_duration/commit_process_time'] = df['tokenizer_duration']/df['commit_process_time']\n", "df['get_chunks_duration/commit_process_time'] = df['get_chunks_duration']/df['commit_process_time']\n", "df['diffing_duration/commit_process_time'] = df['diffing_duration']/df['commit_process_time']\n", "#df['decoding_bytes_duration/commit_process_time'] = df['decoding_bytes_duration']/df['commit_process_time']\n", "#df['ndiff_duration/commit_process_time'] = df['ndiff_duration']/df['commit_process_time']\n", "\n", "# tokens saved\n", "df['tokens_saved/tokens_tokenized'] = df['tokens_saved']/df['tokens_tokenized']\n", "\n", "print(df.describe())"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Benchmarking GitPython (git) vs PyGit2"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    499.000000\n", "mean       0.331317\n", "std        2.164524\n", "min        0.040923\n", "25%        0.079811\n", "50%        0.096969\n", "75%        0.114299\n", "max       29.853843\n", "dtype: float64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["### GitPython, process newest to oldest\n", "\n", "repo = Repo('/home/<USER>/augment')\n", "times = []\n", "\n", "for i in range(500):\n", "    #if i % 50 == 0:\n", "    #    print(f'Processed HEAD through HEAD~{i-1}')\n", "    #    print(pd.Series(times).describe())\n", "        #times = []\n", "    diff_start = time.time()\n", "    prior_commit = repo.commit(f\"HEAD~{i+1}\")\n", "    diffs_objs = prior_commit.diff(f\"HEAD~{i}\")\n", "    num_additions = 0\n", "    skipped = 0\n", "    num_modifications = 0\n", "    try:\n", "        for diff_obj in diffs_objs.iter_change_type(\"D\"):\n", "            continue\n", "        for diff_obj in diffs_objs.iter_change_type(\"R\"):\n", "            continue\n", "        for diff_obj in diffs_objs.iter_change_type(\"A\"):\n", "            num_additions += 1\n", "            current_file_path = diff_obj.b_path\n", "\n", "            fname = Path(current_file_path).name\n", "            #file_language = guess.language_name(current_file_contents)\n", "            file_language = guess_lang_from_fp(fname)\n", "            if file_language is None:\n", "                continue\n", "            if _should_ignore_file(fname):\n", "                continue\n", "\n", "\n", "            current_file_contents = _decode_byte_string(\n", "                diff_obj.b_blob.data_stream.read()\n", "            )\n", "            \n", "            prior_file_contents = []\n", "            if not prior_file_contents or not current_file_contents:\n", "                skipped += 1\n", "                continue\n", "            current_file_contents = current_file_contents.splitlines(1)\n", "            raw_difflines = [line for line in fast_ndiff(prior_file_contents, current_file_contents) if not line.startswith('-') and not line.startswith('?')]\n", "        for diff_obj in diffs_objs.iter_change_type(\"M\"):\n", "            num_modifications += 1\n", "            current_file_path = diff_obj.b_path\n", "            current_file_contents = _decode_byte_string(\n", "                diff_obj.b_blob.data_stream.read()\n", "            )\n", "            prior_file_contents = _decode_byte_string(\n", "                diff_obj.a_blob.data_stream.read()\n", "            )\n", "            if not prior_file_contents or not current_file_contents:\n", "                skipped += 1\n", "                continue\n", "            prior_file_contents = prior_file_contents.splitlines(1)\n", "            current_file_contents = current_file_contents.splitlines(1)\n", "            raw_difflines = [line for line in fast_ndiff(prior_file_contents, current_file_contents) if not line.startswith('-') and not line.startswith('?')]\n", "    except Exception as e:\n", "        continue\n", "    #print(f'num_additions: {num_additions}, num_modifications: {num_modifications}, skipped: {skipped}')\n", "\n", "    diff_end = time.time()\n", "    times.append(diff_end - diff_start)\n", "\n", "pd.Series(times).describe()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["count    500.000000\n", "mean       0.041905\n", "std        0.449763\n", "min        0.006493\n", "25%        0.008150\n", "50%        0.009660\n", "75%        0.013605\n", "max        9.603767\n", "dtype: float64"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["### pygit2, actually reading patches\n", "\n", "repo = Repository('/home/<USER>/augment')\n", "\n", "times = []\n", "\n", "for i in range(500):\n", "    #if i % 50 == 0:\n", "    #    print(f'Processed HEAD through HEAD~{i-1}')\n", "    #    print(pd.Series(times).describe())\n", "        #times = []\n", "    diff_start = time.time()\n", "    diffs_objs = repo.diff(f\"HEAD~{i+1}\", f\"HEAD~{i}\", context_lines=int(1e9))\n", "    for j, patch in enumerate(list(diffs_objs)):\n", "        if i == 0 and j == 0:\n", "                #print(patch.text)\n", "                patch.text\n", "    diff_end = time.time()\n", "    times.append(diff_end - diff_start)\n", "\n", "pd.Series(times).describe()\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Messy scratchpad from exploring pygit2 after this: venture at your own risk!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Links:\n", "# - https://www.pygit2.org/diff.html#pygit2.Repository.diff\n", "\n", "\n", "#diff = next(diffs_objs.deltas)\n", "print(diffs_objs.stats.files_changed, diffs_objs.stats.insertions, diffs_objs.stats.deletions)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(diff.status_char())\n", "\n", "print(diffs_objs.patch)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["next(diffs_objs.deltas).new_file.path"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["M\n", "['diff --git a/file2.py b/file2.py\\n', 'index 7afa47d..17a6114 100644\\n', '--- a/file2.py\\n', '+++ b/file2.py\\n', '@@ -1,99 +1,99 @@\\n', ' This is line 1.\\n', ' This is line 2.\\n', ' This is line 3.\\n', '-This is line 4.\\n', '+This is changed line 4.\\n']\n", "A\n", "diff --git a/file2.py b/file2.py\n", "new file mode 100644\n", "index 0000000..7afa47d\n", "--- /dev/null\n", "+++ b/file2.py\n", "@@ -0,0 +1,99 @@\n", "+This is line 1.\n", "+This is line 2.\n", "+This is line 3.\n", "+This is line 4.\n"]}], "source": ["repo = Repository('/mnt/efs/augment-lga1-nvme/pytest/test_repo')\n", "diffs_objs = repo.diff('HEAD~1', 'HEAD', context_lines=int(1e9))\n", "#print(len(diffs_objs), 'num patches')\n", "for patch in diffs_objs:\n", "    print(patch.delta.status_char())\n", "    print(patch.text.splitlines(1)[:10])\n", "\n", "diffs_objs = repo.diff('HEAD~2', 'HEAD~1', context_lines=int(1e9))\n", "#print(len(diffs_objs), 'num patches')\n", "for patch in diffs_objs:\n", "    print(patch.delta.status_char())\n", "    for line in patch.text.splitlines(1)[:10]:\n", "        print(line.rstrip('\\n'))\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir(patch.delta.new_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for i in range(10):\n", "    d = repo.revparse_single(f'HEAD~{i}')\n", "    print(d)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = repo.revparse_single(f'HEAD~{0}')\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "py35", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}