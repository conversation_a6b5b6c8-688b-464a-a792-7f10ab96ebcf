{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import pickle\n", "from research.eval.patch_lib import Patch\n", "from research.retrieval.types import Document\n", "import zstandard as zstd\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["patches_oldfp = '/mnt/efs/augment/data/processed/repo_coder.v1/patches/function_level_completion_patches.test.jsonl.zst'\n", "chunks_oldfp = '/mnt/efs/augment/data/processed/repo_coder.v1/repos.jsonl.zst'\n", "\n", "with zstd.open(patches_oldfp, \"rb\") as file:\n", "    raw_patches = file.read().decode(\"utf-8\").split(\"\\n\")\n", "patches: list[dict] = [\n", "    json.loads(line.strip()) for line in raw_patches if line.strip()\n", "]\n", "\n", "# Load repos for retrieval\n", "with zstd.open(chunks_oldfp, \"rb\") as file:\n", "    raw_repos_for_retrieval = file.read().decode(\"utf-8\").split(\"\\n\")\n", "repos_for_retrieval: list[dict] = [\n", "    json.loads(line.strip()) for line in raw_repos_for_retrieval if line.strip()\n", "]\n", "\n", "outdir = Path('/mnt/efs/augment/data/eval/hydra/datasets/repoeval_functions')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Write out patches\n", "\n", "patches_by_repo = {}\n", "for patch in patches:\n", "    # The .repository field looks like 'repos/CarperAI_trlx' on old dataset\n", "    # We want an identifier like ('CarperAI', 'trlx')\n", "    repo_identifier = Patch.from_json(json.dumps(patch)).repository.split('/')[1]\n", "    repo_identifier = tuple(repo_identifier.split('_',1))\n", "    patch['repository'] = '/'.join(repo_identifier)\n", "    if repo_identifier not in patches_by_repo:\n", "        patches_by_repo[repo_identifier] = []\n", "\n", "    patches_by_repo[repo_identifier].append(patch)\n", "\n", "for org_name, repo_name in patches_by_repo.keys():\n", "    (outdir / org_name).mkdir(parents=True, exist_ok=True)\n", "    with (outdir / org_name / f'{repo_name}_patches.jsonl').open('w') as f:\n", "        patches = [patch for patch in patches_by_repo[(org_name, repo_name)]]\n", "        for patch in patches:\n", "            json.dump(patch, f)\n", "            f.write('\\n')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Write out repos\n", "# ...\n", "# Argh, /mnt/efs/augment/data/processed/repo_coder.v1/repos.jsonl.zst does not have file paths??\n", "# I will grab these from elsewhere."]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}