{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Speeding up Oracle Perplexity Reranker\n", "\n", "We tried various ideas for speeding up the oracle perplexity reranker.\n", "\n", "Here is some data from below:\n", "```\n", "# BATCH_SIZE 32:\n", "# OUTPUT (no optimizations, 1024 prefix tokens and 1024 suffix tokens): 81.83secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens): 17.90secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning in _score): 14.1secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 4.76secs\n", "\n", "# BATCH_SIZE 256:\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 4.46secs\n", "\n", "# BATCH_SIZE 4:\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 5.31secs\n", "\n", "# For reference, the above runs are with 1b reranker. Let's consider 7b reranker:\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 14.62secs\n", "```\n", "\n", "Here are some takeaways\n", "- **Significant speed up:** Shrink prefix and suffix length from (2048 -> 200 tokens).\n", "- **Significant speed up:** Computing cross entropy on GPU not CPU: This significantly sped things up.\n", "- **Significant speed up:** Shrink reranker model from 7b or 16b to 1b. So we have large generative model and small reranker model.\n", "- **Moderate speed up:** Removing lots of copy.deepcopy calls when cloning ModelInput objects.\n", "- **Negligible speed up:** Increasing batch size of forward pass: This actually did not speed things up much. The reason is that the forward pass is only a small part of wall clock time.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "import time\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "from pathlib import Path\n", "from research.eval.harness.systems.basic_RAG_system_with_reranker import RAGWithRerankerSystem\n", "from research.retrieval.utils import Span\n", "\n", "config_fp = '/home/<USER>/augment/experimental/colin/notebooks/2023_09_09_profile_reranker/system_with_reranker.yml'\n", "config = yaml.safe_load(Path(config_fp).read_text())['system']\n", "\n", "sys = RAGWithRerankerSystem.from_yaml_config(config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["template_input = ModelInput(\n", "    prefix=str('p' * 10_000),\n", "    suffix=str('s' * 10_000),\n", "    path=\"blah_input.py\",\n", "    retrieved_chunks=[\n", "        Chunk(str(i), \"c\" * 400, Document(id=str(i), text=\"c\" * 100, path=f\"blah_file{i}.py\"), 0,100,0,10)\n", "        for i in range(512)\n", "    ],\n", "    extra={\n", "        \"ground_truth_span\": Span(0,5),\n", "        \"ground_truth\": \"blah blah blah blah blah blah blah blah blah blah blah blah blah blah \",\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.reranker.batchsize = 32\n", "\n", "example_input = template_input.clone()\n", "\n", "times = []\n", "for _ in range(5):\n", "    ts = time.perf_counter()\n", "    sys.reranker.rerank(example_input)\n", "    ts = time.perf_counter() - ts\n", "    times.append(ts)\n", "\n", "print(sum(times)/len(times))\n", "\n", "# OUTPUT (no optimizations, 1024 prefix tokens and 1024 suffix tokens): 81.83secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens): 17.90secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning in _score): 14.1secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 4.76secs\n", "\n", "# For reference, the above runs are with 1b reranker. Let's consider 7b reranker:\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 14.62secs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.reranker.batchsize = 256\n", "\n", "example_input = template_input.clone()\n", "\n", "times = []\n", "for _ in range(5):\n", "    ts = time.perf_counter()\n", "    sys.reranker.rerank(example_input)\n", "    ts = time.perf_counter() - ts\n", "    times.append(ts)\n", "\n", "print(sum(times)/len(times))\n", "\n", "# OUTPUT (no optimizations, 1024 prefix tokens, 1024 suffix tokens): OOM\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens): 17.73secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning in _score): 13.79secs\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 4.46secs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sys.reranker.batchsize = 4\n", "\n", "example_input = template_input.clone()\n", "\n", "times = []\n", "for _ in range(5):\n", "    ts = time.perf_counter()\n", "    sys.reranker.rerank(example_input)\n", "    ts = time.perf_counter() - ts\n", "    times.append(ts)\n", "\n", "print(sum(times)/len(times))\n", "\n", "# OUTPUT (100 prefix tokens and 100 suffix tokens + faster model input cloning + compute cross entropy on gpu): 5.31secs"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}