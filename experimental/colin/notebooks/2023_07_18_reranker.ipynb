{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "from research.models.codegen_models import CodeGen_16B_Indiana, CodeGen_350M_Multi\n", "from research.retrieval.types import Document, Chunk\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from megatron.tokenizer.tokenizer import CodeGenTokenizer\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from research.retrieval.file_filterer import basic_file_filterer\n", "from research.retrieval.scorers.dense_scorer import DenseRetrievalScorer\n", "from research.retrieval.rerankers.oracle_perplexity_reranker import OraclePerplexityReranker\n", "from research.core.model_input import ModelInput\n", "import logging"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "print(\"Loading the model...\")\n", "# model = StarCoder(checkpoints_root, variant=StarCoderVariant.STAR_CODER_BASE)\n", "model = CodeGen_16B_Indiana()\n", "model.load()\n", "\n", "# Build dense document index (and retriever model)\n", "yaml_files = [\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml\",\n", "]\n", "overwrite_values = {\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "dense_scorer = DenseRetrievalScorer(yaml_files, overwrite_values, max_query_tokens=50)\n", "chunker = LineLevelChunker(max_lines_per_chunk=20)\n", "file_filterer = basic_file_filterer\n", "doc_index = RetrievalDatabase(chunker, dense_scorer, file_filterer)\n", "reranker = OraclePerplexityReranker(model, 2)\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:augment.research.retrieval.retrieval_database:Starting document embedding.\n", "INFO:augment.research.retrieval.retrieval_database:Finished document embedding in 1.3305552005767822.\n"]}], "source": ["docs = [\n", "    Document(id=\"1\", text=\"magic_number = 1231\", path=\"a_document.py\"),\n", "    Document(id=\"2\", text=\"def run_this_func(): return 'hi'\", path=\"a_document2.py\"),\n", "    Document(id=\"3\", text=\"def wrapper(): returnrun_this_func()\", path=\"a_document3.py\")\n", "]\n", "doc_index.add_docs(docs)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["====== Prompt:\n", "\u001b[32m\n", "run_this_func():\n", "\n", "\u001b[0m\n", "\n", "====== Retrieved chunks (without rereanking):\n", "====== Chunk:\n", "\u001b[32mdef run_this_func(): return 'hi'\u001b[0m \u001b[31mwith score 2874.0\u001b[0m\n", "====== Chunk:\n", "\u001b[32mdef wrapper(): returnrun_this_func()\u001b[0m \u001b[31mwith score 2834.0\u001b[0m\n", "====== Chunk:\n", "\u001b[32mmagic_number = 1231\u001b[0m \u001b[31mwith score 2438.0\u001b[0m\n"]}], "source": ["# Create a prompt\n", "prompt = \"\"\"\n", "run_this_func():\n", "\n", "\"\"\"\n", "\n", "print(\"====== Prompt:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "# Retrieve\n", "print(\"\\n====== Retrieved chunks (without rereanking):\")\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=3)\n", "\n", "for chunk, score in zip(chunks, scores):\n", "    print(\"====== Chunk:\")\n", "    print(colored(chunk.text, \"green\"), colored(f\"with score {score}\", \"red\"))\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["====== Prefix:\n", "\u001b[32m\n", "run_this_func():\n", "\n", "\u001b[0m\n", "====== Continuation:\n", "\u001b[32m\n", "    magic_number = 1231\n", "\u001b[0m\n", "\n", "====== Retrieved chunks (with reranking):\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/src/augment/research/model_server/meta_gpt_neox_model.py:271: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  [{\"text\": torch.tensor(padded_prompts_toks, dtype=torch.int64)}]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["====== Chunk:\n", "\u001b[32mmagic_number = 1231\u001b[0m \u001b[31mwith score -1.1384931802749634\u001b[0m\n", "====== Chunk:\n", "\u001b[32mdef run_this_func(): return 'hi'\u001b[0m \u001b[31mwith score -3.1351125240325928\u001b[0m\n"]}], "source": ["continuation = \"\"\"\n", "    magic_number = 1231\n", "\"\"\"\n", "\n", "\n", "print(\"====== Prefix:\")\n", "print(colored(prompt, \"green\"))\n", "\n", "print(\"====== Continuation:\")\n", "print(colored(continuation, \"green\"))\n", "\n", "# Retrieve\n", "print(\"\\n====== Retrieved chunks (with reranking):\")\n", "chunks, scores = doc_index.query(ModelInput(prefix=prompt), top_k=3)\n", "input = ModelInput(\n", "    prefix=prompt, \n", "    target=\"\", \n", "    path='a_file.py', \n", "    retrieved_chunks=chunks\n", ")\n", "input.extra[\"ground_truth\"] = continuation\n", "reranked_input, reranked_scores = reranker.rerank(input)\n", "\n", "for chunk, score in zip(reranked_input.retrieved_chunks, reranked_scores):\n", "    print(\"====== Chunk:\")\n", "    print(colored(chunk.text, \"green\"), colored(f\"with score {score}\", \"red\"))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}