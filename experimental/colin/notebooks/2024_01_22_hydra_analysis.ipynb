{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_config = [\n", "    (\"multiline_retrieval_old_ckpt\", \"/mnt/efs/augment/eval/jobs/EpyXPYcm\"),\n", "    (\"multiline_retrieval_new_ckpt\", \"/mnt/efs/augment/eval/jobs/eQ7Z3YHV\"),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import difflib\n", "import html\n", "import json\n", "import pandas as pd\n", "import pickle\n", "\n", "from research.eval.harness.utils import read_jsonl_zst\n", "from IPython.core.display import display, HTML\n", "from collections import defaultdict\n", "from pathlib import Path\n", "from types import MethodType\n", "\n", "def read_artifacts(run_path):\n", "    matching_files = list(Path(run_path).glob(\"*_hydra.jsonl\"))\n", "    if len(matching_files) != 1:\n", "        raise ValueError(\n", "            f\"Expected 1 Hydra jsonl file under {run_path}, found {len(matching_files)}\"\n", "        )\n", "\n", "    hydra_results_path = matching_files[0]\n", "    hydra_patches_path = (\n", "        hydra_results_path.parent / (hydra_results_path.stem + \"_completed_patches.jsonl.zst\")\n", "    )\n", "\n", "    with hydra_results_path.open(\"r\") as f:\n", "        hydra_result_records = [json.loads(x) for x in f]\n", "    hydra_patch_records = read_jsonl_zst(hydra_patches_path)\n", "\n", "    if len(hydra_patch_records) != len(hydra_result_records):\n", "        raise ValueError(\"Inconsistent records!\")\n", "    \n", "    return hydra_patch_records, hydra_result_records\n", "\n", "def unpack_hydra_patch_results(a):\n", "    run_metadata = {\n", "        \"prompt\": a[\"prompt\"],\n", "        \"generation\": a[\"generation\"],\n", "        \"completion\": a[\"completion\"],\n", "        \"filename\": a[\"patch\"][\"file_name\"],\n", "        \"file_content\": a[\"patch\"][\"file_content\"],\n", "    }\n", "\n", "    if \"retrieval_metadata\" in a and \"retriever_prompt\" in a[\"retrieval_metadata\"]:\n", "        run_metadata[\"retriever_prompt\"] = a[\"retrieval_metadata\"][\"retriever_prompt\"]\n", "    elif \"retrieval\" in a and \"retriever_prompt\" in a[\"retrieval\"]:\n", "        run_metadata[\"retriever_prompt\"] = a[\"retrieval\"][\"retriever_prompt\"]\n", "    elif \"artifacts\" in a:\n", "        run_metadata[\"retriever_prompt\"] = a[\"artifacts\"][0][\"retriever_prompt\"]\n", "    else:\n", "        run_metadata[\"retriever_prompt\"] = \"\"\n", "    return run_metadata\n", "\n", "def load(run_path):\n", "    api_task_path = Path(run_path) / \"000__RAGSystem_ApiCallTask\"\n", "    if api_task_path.exists():\n", "        return load_api_task(api_task_path)\n", "\n", "    hydra_patch_records, hydra_result_records = read_artifacts(run_path)\n", "    if len(hydra_patch_records) != len(hydra_result_records):\n", "        raise ValueError(\"Inconsistent records!\")\n", "\n", "    metadata = {}\n", "    for a, b in zip(hydra_patch_records, hydra_result_records):\n", "        assert a[\"completion\"] == b[\"patch_content\"]\n", "\n", "        metadata[b[\"patch_id\"]] = unpack_hydra_patch_results(a) | {\n", "            \"ground_truth\": b[\"file_content\"][b[\"char_start\"]: b[\"char_end\"]],\n", "            \"result\": b[\"_extra\"][\"result\"],\n", "            \"run_output\": b[\"_extra\"][\"run_output\"],\n", "        }\n", "    return metadata\n", "\n", "def load_api_task(api_task_path):\n", "    hydra_patch_records = read_jsonl_zst(api_task_path / \"000_RAGSystem_ApiCallTaskcompleted_patches.jsonl.zst\")\n", "\n", "    api_result_files = sorted(e for e in (api_task_path / \"cache\").glob(\"*.json\"))\n", "    assert len(api_result_files) == len(hydra_patch_records)\n", "\n", "    metadata = {}\n", "    for hydra_patch_rec, api_result_file in zip(hydra_patch_records, api_result_files):\n", "        with open(api_result_file) as fh:\n", "            api_result_rec = json.load(fh)\n", "\n", "        patch_id = hydra_patch_rec['patch']['patch_id']\n", "        result_val = (\n", "            (\"metric@hydra-unit-test-pass\" in api_result_rec) and api_result_rec[\"metric@hydra-unit-test-pass\"]\n", "        )\n", "        metadata[patch_id] = unpack_hydra_patch_results(hydra_patch_rec) | {\n", "            \"ground_truth\": api_result_rec[\"inputs\"][\"target\"],\n", "            \"result\": \"PASSED\" if result_val else \"FAILED\",\n", "            \"run_output\": \"<not available>\",\n", "        }\n", "    return metadata\n", "\n", "\n", "def diff_runs(results, patch_id):\n", "    f\"diff_runs(results[0]['name'], results[1]['name'])\"\n", "    run1 = results[0]['data'][patch_id]\n", "    run2 = results[1]['data'][patch_id]\n", "    run_names = [res_item['name'] for res_item in results]\n", "    diff_obj = difflib.HtmlDiff()\n", "    diff_obj._legend = \"\"\n", "\n", "    markers = [\n", "        '+' if res == \"PASSED\"\n", "        else (\n", "            '-' if res == \"FAILED\" else '^'\n", "        )\n", "        for res in (run1[\"result\"], run2[\"result\"])\n", "    ]\n", "\n", "    # Adjust colors\n", "    orig_convert_flags = diff_obj._convert_flags\n", "    def _convert_flags(self,fromlist,tolist,flaglist,context,numlines):\n", "        def _swap_parity(items):\n", "            return [\n", "                item.replace('\\0+', '\\0*').replace('\\0-', f'\\0{markers[0]}').replace('\\0*', f'\\0{markers[1]}')\n", "                for item in items\n", "            ]\n", "        fromlist = _swap_parity(fromlist)\n", "        tolist = _swap_parity(tolist)\n", "        return orig_convert_flags(fromlist, tolist, flaglist, context, numlines)\n", "\n", "    diff_obj._convert_flags = MethodType(_convert_flags, diff_obj)\n", "\n", "    display(HTML(f\"\"\"\n", "    <style type=\"text/css\">\n", "        {difflib.HtmlDiff()._styles}\n", "        td {{ text-align: left; }}\n", "        :not(.jp-RenderedMarkdown).jp-RenderedHTMLCommon td {{ text-align: left; }}\n", "    </style>\n", "    \"\"\"))\n", "\n", "    display(HTML(f\"<h2>Patch {patch_id}</h2>\"))\n", "\n", "    display(HTML(\"<h3>Results</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        [run1[\"result\"]],\n", "        [run2[\"result\"]],\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "    )))\n", "\n", "    if run1[\"result\"] == run2[\"result\"]:\n", "        # No difference in the two runs, so let's showing the details.\n", "        #return\n", "        pass\n", "    \n", "    display(HTML(\"<h3>Prompt</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        run1['prompt'].splitlines(),\n", "        run2['prompt'].splitlines(),\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "    )))\n", "    display(HTML(\"<h3>Retriever Prompt</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        run1['retriever_prompt'].splitlines(),\n", "        run2['retriever_prompt'].splitlines(),\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "    )))\n", "\n", "    display(HTML(\"<h3>Completion</h3>\"))\n", "    display(HTML(diff_obj.make_table(\n", "        run1['completion'].splitlines(),\n", "        run2['completion'].splitlines(),\n", "        fromdesc=run_names[0],\n", "        todesc=run_names[1],\n", "    )))\n", "    \n", "    display(HTML(\"<h3>Ground Truth</h3>\"))\n", "    display(HTML(\n", "        \"<table class='diff' summary='Ground Truth' style='border: 1px solid black;'><tr><td>\"\n", "        + \"</tr></td><tr><td>\".join(\n", "            line.replace(\" \", \"&nbsp;\")\n", "            for line in run1['ground_truth'].splitlines()\n", "        )\n", "        + \"</td></tr></table>\"\n", "    ))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = [\n", "    {\n", "        \"data\": load(run_path),\n", "        \"name\": run_name,\n", "    }\n", "    for run_name, run_path in run_config\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summarize the Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if results[0]['data'].keys() != results[1]['data'].keys():\n", "    print(\"ERROR: key sets don't match!\")\n", "\n", "result_stats = {}\n", "for k in reversed(results[0]['data'].keys()):\n", "    kk = tuple(results[i]['data'][k][\"result\"] for i in range(len(results)))\n", "    if kk not in result_stats:\n", "        result_stats[kk] = 0\n", "    result_stats[kk] += 1\n", "\n", "pd.DataFrame([\n", "    {\n", "        results[i][\"name\"]: item[0][i]\n", "        for i in range(len(results))\n", "    } | {\n", "        \"count\": item[1]\n", "    }\n", "    for item in result_stats.items()\n", "])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Check for Known Issues"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["issue_count = 0\n", "\n", "for res in results:\n", "    for k in reversed(res['data'].keys()):\n", "        if \"Checkout your internet\" in res['data'][k]['run_output']:\n", "            print(f\"[{res['name']}, {k}]  HuggingFace error, presumed non-deterministic failure.\")\n", "            issue_count += 1\n", "        if (res['data'])[k]['result'] not in [\"PASSED\", \"FAILED\"]:\n", "            print(f\"[{res['name']}, {k}]  Result other than pass/fail: {res['data'][k]['result']}.\")\n", "            issue_count += 1\n", "\n", "if issue_count > 0:\n", "    print(f\"WARNING: {issue_count} issues found.\")\n", "else:\n", "    print(\"All OK.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Show Differences Between Runs\n", "Note: Flip the if False guard to generate the cells. You can then restore the guard to avoid accidentally."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if True:\n", "    def create_new_cell(contents):\n", "        from IPython.core.getipython import get_ipython\n", "\n", "        shell = get_ipython()\n", "\n", "        payload = dict(\n", "            source=\"set_next_input\",\n", "            text=contents,\n", "            replace=False,\n", "        )\n", "        shell.payload_manager.write_payload(payload, single=False)\n", "\n", "    for patch_id in reversed(list(results[0][\"data\"].keys())):\n", "        passfail = [res_item[\"data\"][patch_id][\"result\"] for res_item in results]\n", "        if passfail[0] != passfail[1]:\n", "            create_new_cell(\n", "                f\"# {patch_id}: {passfail[0]} {passfail[1]}\\n\"\n", "                f\"diff_runs(results, '{patch_id}')\"\n", "            )"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}