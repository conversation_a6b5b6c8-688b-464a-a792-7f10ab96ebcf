{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from termcolor import colored"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["completions_with_retrieval_fp = '/mnt/efs/augment/eval/jobs/496XzH5u/000_BASIC_RAG_hydra.jsonl'\n", "with open(completions_with_retrieval_fp) as f:\n", "    completions_with_retrieval = [json.loads(line) for line in f.readlines()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["completions_with_retrieval[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for patch in completions_with_retrieval:\n", "    file_content = patch['file_content']\n", "    char_start = patch['char_start']\n", "    char_end = patch['char_end']\n", "\n", "    patch_id = patch['patch_id']\n", "    prefix = file_content[(char_start-1000):char_start]\n", "    middle = file_content[char_start:char_end]\n", "    generation = patch['patch_content']\n", "    suffix = file_content[char_end:(char_end + 1000)]\n", "\n", "    print(colored(('='* 10) + f\"Next patch: {patch_id}\" + ('='*10),'blue'))\n", "    print(colored(prefix, 'red'))\n", "    print(colored(middle, 'green'))\n", "    print(colored(generation, 'yellow'))\n", "    print(colored(suffix, 'red'))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}