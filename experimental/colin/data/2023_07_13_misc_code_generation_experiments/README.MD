# Description

These experiments correspond to the Notion: https://www.notion.so/July-12-2023-Investigate-Hydra-Performance-8514fb085fd7449586465256eb64c869?pvs=4.

In these experiments. I investigate various capabilities.

Test descriptions:
- deepmind_tracr_54: This tests whether a model understands a complicated tree of abstractions. It is a Hydra patch.

- google_lightweight_mmm_2: This illustrates how a code patch might be hard to reproduce, even with a detailed docstring and relevant retrieval. This is a Hydra patch. Copilot failed.

- grab_permutations: This investigates whether a model can solve a nontrivial algorithms problem that can be solved with the standard module function itertools.permutations. Copilot passes.
- jax_example1: This investigates whether a model has memorized Jax. It also investigates if it can add the correct imports given a piece of code. Copilot passes.
- look_up_in_init: This investigates the wheter a model can grab definitions from an __init__ definition.
- search_google_library: This tests an edge case in Copilot where it prefers to generate `pass` for a function with a detailed function name and docstring.
- streaming_shuffle_dataset: This tests whether a model can generate a nontrivial subclass of `torch.utils.data.IterableDataset` without seeing its definition in context. It needs to shuffle a dataset differently across each epoch. Copilot passes.
- torch_nn_idiom: This tests whether a model has memorized the idiom that `nn` means `torch.nn`, without including the import in the prefix (in practice, the imports may be truncated out of the prefix.). Copilot passes.