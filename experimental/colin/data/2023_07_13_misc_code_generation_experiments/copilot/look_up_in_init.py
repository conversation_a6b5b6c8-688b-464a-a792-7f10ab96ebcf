
Name = Union[int, str]
Value = Union[int, float, bool, str, tuple]

@dataclasses.dataclass(frozen=True)
class BasisDirection:
  """Represents a basis direction (no magnitude) in a vector space.

  Attributes:
    name: a unique name for this direction.
    value: used to hold a value one-hot-encoded by this direction. e.g.,
      [BasisDirection("vs_1", True), BasisDirection("vs_1", False)] would be
      basis directions of a subspace called "vs_1" which one-hot-encodes the
      values True and False. If provided, considered part of the name for the
      purpose of disambiguating directions.
  """
  name: Name
  value: Value

  def __str__(self):
    return f"{self.name}:{self.value}"

  def __lt__(self, other: "BasisDirection") -> bool:
    try:
      return (self.name, self.value) < (other.name, other.value)
    except TypeError:
      return str(self) < str(other)


@dataclasses.dataclass
class VectorInBasis:
    """A vector (or array of vectors) in a given basis.

    When magnitudes are 1-d, this is a vector.
    When magnitudes are (n+1)-d, this is an array of vectors,
    where the -1th dimension is the basis dimension.
    """
    basis_directions: Sequence[BasisDirection]
    magnitudes: np.ndarray

    def __post_init__(self):
        """Sort basis directions."""
        if len(self.basis_directions) != self.magnitudes.shape[-1]:
            raise ValueError(
                "Last dimension of magnitudes must be the same as number "
                f"of basis directions. Was {len(self.basis_directions)} "
                f"and {self.magnitudes.shape[-1]}.")

        sort_idx = np.argsort(self.basis_directions)
        self.basis_directions = [self.basis_directions[i] for i in sort_idx]
        self.magnitudes = np.take(self.magnitudes, sort_idx, -1)
    
    def update_basis_directions(self, basis_directions: Sequence[BasisDirection]) -> None:
       """Update basis directions in-place."""
        # beginning of copilot generation
        if len(basis_directions) != self.magnitudes.shape[-1]:
              raise ValueError(
                "Last dimension of magnitudes must be the same as number "
                f"of basis directions. Was {len(basis_directions)} "
                f"and {self.magnitudes.shape[-1]}.")
        self.basis_directions = basis_directions
        # end of copilot generation
       
    def scale_vector(self, scale: float) -> None:
        # beginning of copilot generation
        """Scale vector in-place."""
        self.magnitudes *= scale
        # end of copilot generation







    