

class StreamingShuffleDataset(torch.utils.data.IterableDataset):
    """Shuffles a dataset each epoch, specifying epoch index with a setter.

    Args:
        dataset (~torch.utils.data.Dataset): dataset to shuffle
        seed (int): iterate over the underlying dataset in random order using
            this random seed
    """
    # Beginning Copilot generation
    def __init__(self, dataset, seed=0):
        super().__init__()
        self.dataset = dataset
        self.seed = seed
        self.epoch = 0

    def __iter__(self):
        g = torch.Generator()
        g.manual_seed(self.seed + self.epoch)
        yield from torch.utils.data.RandomSampler(self.dataset, generator=g)

    def __len__(self):
        return len(self.dataset)
    # Ending Copilot generation