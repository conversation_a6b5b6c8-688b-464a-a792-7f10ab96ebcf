"""Library for combinatorics."""

def get_10_permutations_with_second_element_fixed(ls):
    """Get sample of 10 permutations of list with second element fixed to its original value."""
    # Beginning Copilot Generation
    import itertools
    import random
    import copy
    ls = copy.deepcopy(ls)
    second_element = ls[1]
    ls.remove(second_element)
    permutations = list(itertools.permutations(ls))
    random.shuffle(permutations)
    permutations = permutations[:10]
    permutations = [list(permutation) for permutation in permutations]
    for permutation in permutations:
        permutation.insert(1, second_element)
    return permutations
    # End Copilot generation
