class NotFittedModelError(Exception):
  pass

@dataclasses.dataclass(unsafe_hash=True, eq=False)
class LightweightMMM:
    def fit(
        self,
        media: jnp.ndarray,
        media_prior: jnp.ndarray,
        target: jnp.ndarray,
        extra_features: Optional[jnp.ndarray] = None,
        degrees_seasonality: int = 2,
        seasonality_frequency: int = 52,
        weekday_seasonality: bool = False,
        media_names: Optional[Sequence[str]] = None,
        number_warmup: int = 1000,
        number_samples: int = 1000,
        number_chains: int = 2,
        target_accept_prob: float = .85,
        init_strategy: Callable[[Mapping[Any, Any], Any],
                                jnp.ndarray] = numpyro.infer.init_to_median,
        custom_priors: Optional[Dict[str, Prior]] = None,
        seed: Optional[int] = None) -> None:
        # TODO
        mcmc = numpyro.infer.MCMC(
            sampler=kernel,
            num_warmup=number_warmup,
            num_samples=number_samples,
            num_chains=number_chains)
        mcmc.run(
            rng_key=jax.random.PRNGKey(seed),
            media_data=jnp.array(media),
            extra_features=extra_features,
            target_data=jnp.array(target),
            media_prior=jnp.array(media_prior),
            degrees_seasonality=degrees_seasonality,
            frequency=seasonality_frequency,
            transform_function=self._model_transform_function,
            weekday_seasonality=weekday_seasonality,
            custom_priors=custom_priors)
        [...]
        self.trace = mcmc.get_samples()
        [...]


def _calculate_media_contribution(
    media_mix_model: lightweight_mmm.LightweightMMM) -> jnp.ndarray:
    """Computes contribution for each sample, time, channel.

    Serves as a helper function for making predictions for each channel, time
    and estimate sample. It is meant to be used in creating media baseline
    contribution dataframe and visualize media attribution over spend proportion
    plot.

    Args:
        media_mix_model: Media mix model.

    Returns:
        Estimation of contribution for each sample, time, channel.

    Raises:
        NotFittedModelError: if the model is not fitted before computation
    """
    # Beginning Copilot generation
    if media_mix_model.trace is None:
        raise NotFittedModelError("Model is not fitted yet.")
    # TODO
    # End Copilot generation
    