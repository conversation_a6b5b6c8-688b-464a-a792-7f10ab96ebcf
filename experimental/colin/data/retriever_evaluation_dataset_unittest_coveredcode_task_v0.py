"""This is a small list of positive pairs for the retrieval evaluation task of "unit test and covered code"."""

EXAMPLES = [
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_calling_without_setting_index",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "7-20",
                "chunk": """\
def test_calling_without_setting_index():
    query = np.arange(3 * 6, dtype=np.float32).reshape(3, 6)
    index = 2 * query

    nn_search = common.FaissNN()

    distances_before_set_index, nn_indices_before_set_index = nn_search.run(
        2, query, index
    )
    nn_search.fit(index)
    distances_after_set_index, nn_indices_after_set_index = nn_search.run(2, query)

    assert np.all(distances_before_set_index == distances_after_set_index)
    assert np.all(nn_indices_before_set_index == nn_indices_after_set_index)

""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "43-61",
                "chunk": '''\
    def _create_index(self, dimension):
        if self.on_gpu:
            return faiss.GpuIndexFlatL2(
                faiss.StandardGpuResources(), dimension, faiss.GpuIndexFlatConfig()
            )
        return faiss.IndexFlatL2(dimension)

    def fit(self, features: np.ndarray) -> None:
        """
        Adds features to the FAISS search index.

        Args:
            features: Array of size NxD.
        """
        if self.search_index:
            self.reset_index()
        self.search_index = self._create_index(features.shape[-1])
        self._train(self.search_index, features)
        self.search_index.add(features)
''',
            },
        ],
    },
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_approximate_faiss",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "23-36",
                "chunk": """\
def test_approximate_faiss():
    query = np.ones([768, 128], dtype=np.float32)
    index = 2 * query

    nn_search = common.ApproximateFaissNN()

    distances_before_set_index, nn_indices_before_set_index = nn_search.run(
        2, query, index
    )
    nn_search.fit(index)
    distances_after_set_index, nn_indices_after_set_index = nn_search.run(2, query)

    assert np.all(distances_before_set_index == distances_after_set_index)
    assert np.all(nn_indices_before_set_index == nn_indices_after_set_index)
""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "100-117",
                "chunk": """\
class ApproximateFaissNN(FaissNN):
    def _train(self, index, features):
        index.train(features)

    def _gpu_cloner_options(self):
        cloner = faiss.GpuClonerOptions()
        cloner.useFloat16 = True
        return cloner

    def _create_index(self, dimension):
        index = faiss.IndexIVFPQ(
            faiss.IndexFlatL2(dimension),
            dimension,
            512,  # n_centroids
            64,  # sub-quantizers
            8,
        )  # nbits per code
        return self._index_to_gpu(index)
""",
            },
        ],
    },
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_read_write_index",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "47-64",
                "chunk": """\
def test_read_write_index(tmpdir):
    index_filename = (tmpdir / "index").strpath
    nn_model = common.FaissNN()
    features = np.arange(3 * 6, dtype=np.float32).reshape(3, 6)
    nn_model.fit(features)
    nn_model.save(index_filename)

    loaded_nn_model = common.FaissNN()
    loaded_nn_model.load(index_filename)

    query_features = np.arange(10 * 6, dtype=np.float32).reshape(10, 6)
    assert loaded_nn_model.run(2, query_features) is not None
    assert np.all(
        loaded_nn_model.run(2, query_features)[0] == nn_model.run(2, query_features)[0]
    )
    assert np.all(
        loaded_nn_model.run(2, query_features)[1] == nn_model.run(2, query_features)[1]
    )
""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "14-97",
                "chunk": '''\
class FaissNN(object):
    def __init__(self, on_gpu: bool = False, num_workers: int = 4) -> None:
        """FAISS Nearest neighbourhood search.

        Args:
            on_gpu: If set true, nearest neighbour searches are done on GPU.
            num_workers: Number of workers to use with FAISS for similarity search.
        """
        faiss.omp_set_num_threads(num_workers)
        self.on_gpu = on_gpu
        self.search_index = None

    def _gpu_cloner_options(self):
        return faiss.GpuClonerOptions()

    def _index_to_gpu(self, index):
        if self.on_gpu:
            # For the non-gpu faiss python package, there is no GpuClonerOptions
            # so we can not make a default in the function header.
            return faiss.index_cpu_to_gpu(
                faiss.StandardGpuResources(), 0, index, self._gpu_cloner_options()
            )
        return index

    def _index_to_cpu(self, index):
        if self.on_gpu:
            return faiss.index_gpu_to_cpu(index)
        return index

    def _create_index(self, dimension):
        if self.on_gpu:
            return faiss.GpuIndexFlatL2(
                faiss.StandardGpuResources(), dimension, faiss.GpuIndexFlatConfig()
            )
        return faiss.IndexFlatL2(dimension)

    def fit(self, features: np.ndarray) -> None:
        """
        Adds features to the FAISS search index.

        Args:
            features: Array of size NxD.
        """
        if self.search_index:
            self.reset_index()
        self.search_index = self._create_index(features.shape[-1])
        self._train(self.search_index, features)
        self.search_index.add(features)

    def _train(self, _index, _features):
        pass

    def run(
        self,
        n_nearest_neighbours,
        query_features: np.ndarray,
        index_features: np.ndarray = None,
    ) -> Union[np.ndarray, np.ndarray, np.ndarray]:
        """
        Returns distances and indices of nearest neighbour search.

        Args:
            query_features: Features to retrieve.
            index_features: [optional] Index features to search in.
        """
        if index_features is None:
            return self.search_index.search(query_features, n_nearest_neighbours)

        # Build a search index just for this search.
        search_index = self._create_index(index_features.shape[-1])
        self._train(search_index, index_features)
        search_index.add(index_features)
        return search_index.search(query_features, n_nearest_neighbours)

    def save(self, filename: str) -> None:
        faiss.write_index(self._index_to_cpu(self.search_index), filename)

    def load(self, filename: str) -> None:
        self.search_index = self._index_to_gpu(faiss.read_index(filename))

    def reset_index(self):
        if self.search_index:
            self.search_index.reset()
            self.search_index = None

''',
            },
        ],
    },
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_search_without_index_raises_exception",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "39-44",
                "chunk": """\
def test_search_without_index_raises_exception():
    features = np.arange(3 * 6, dtype=np.float32).reshape(3, 6)
    nn_search = common.FaissNN(on_gpu=False, num_workers=4)
    with pytest.raises(AttributeError):
        nn_search.run(2, features)
    assert nn_search.run(2, features, features) is not None
""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "66-86",
                "chunk": '''\
def run(
        self,
        n_nearest_neighbours,
        query_features: np.ndarray,
        index_features: np.ndarray = None,
    ) -> Union[np.ndarray, np.ndarray, np.ndarray]:
        """
        Returns distances and indices of nearest neighbour search.

        Args:
            query_features: Features to retrieve.
            index_features: [optional] Index features to search in.
        """
        if index_features is None:
            return self.search_index.search(query_features, n_nearest_neighbours)

        # Build a search index just for this search.
        search_index = self._create_index(index_features.shape[-1])
        self._train(search_index, index_features)
        search_index.add(index_features)
        return search_index.search(query_features, n_nearest_neighbours)
''',
            },
        ],
    },
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_average_merger_shape",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "67-78",
                "chunk": """\
def test_average_merger_shape():
    input_features = []
    input_features.append(np.arange(2 * 3 * 4 * 5).reshape([2, 3, 4, 5]))
    input_features.append(2 * np.arange(2 * 3 * 4 * 5).reshape([2, 4, 3, 5]))

    merger = common.AverageMerger()
    output_features = merger.merge([input_features[0]])
    assert np.all(output_features.shape == (2, 3))

    merger = common.AverageMerger()
    output_features = merger.merge(input_features)
    assert np.all(output_features.shape == (2, 7))

""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "129-135",
                "chunk": """\
class AverageMerger(_BaseMerger):
    @staticmethod
    def _reduce(features):
        # NxCxWxH -> NxC
        return features.reshape([features.shape[0], features.shape[1], -1]).mean(
            axis=-1
        )
""",
            },
        ],
    },
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_average_merger_output",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "81-86",
                "chunk": """\
def test_average_merger_output():
    input_features = [np.ones([2, 3, 4, 5])]

    merger = common.AverageMerger()
    output_features = merger.merge(input_features)
    assert np.all(output_features == 1.0)
""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "129-135",
                "chunk": """\
class AverageMerger(_BaseMerger):
    @staticmethod
    def _reduce(features):
        # NxCxWxH -> NxC
        return features.reshape([features.shape[0], features.shape[1], -1]).mean(
            axis=-1
        )
""",
            },
        ],
    },
    {
        "repo": "amazon-science/patchcore-inspection",
        "commit_hash": "fcaa92f124fb1ad74a7acf56726decd4b27cbcad",
        "description": "test_concat_merger_shape",
        "chunks": [
            {
                "fp": "test/test_common.py",
                "type": "test",
                "line_numbers": "89-100",
                "chunk": """\
def test_concat_merger_shape():
    input_features = []
    input_features.append(np.arange(2 * 3 * 4 * 5).reshape([2, 3, 4, 5]))
    input_features.append(2 * np.arange(2 * 3 * 4 * 5).reshape([2, 4, 3, 5]))

    merger = common.ConcatMerger()
    output_features = merger.merge([input_features[0]])
    assert np.all(output_features.shape == (2, 3 * 4 * 5))

    merger = common.ConcatMerger()
    output_features = merger.merge(input_features)
    assert np.all(output_features.shape == (2, 3 * 4 * 5 + 4 * 3 * 5))
""",
            },
            {
                "fp": "src/patchcore/common.py",
                "type": "tested",
                "line_numbers": "138-142",
                "chunk": """\
class ConcatMerger(_BaseMerger):
    @staticmethod
    def _reduce(features):
        # NxCxWxH -> NxCWH
        return features.reshape(len(features), -1)
""",
            },
        ],
    },
]
