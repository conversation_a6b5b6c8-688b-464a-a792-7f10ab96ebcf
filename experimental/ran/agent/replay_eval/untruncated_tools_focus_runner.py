#!/usr/bin/env python3

import argparse
import json
import logging
import re
import uuid
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime

from base.prompt_format_chat.prompt_formatter import StructuredChatPromptOutput
from base.prompt_format.common import (
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatRequestImage,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
    Exchange,
    ImageFormatType,
)
from base.third_party_clients.third_party_model_client import ToolDefinition
from experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor import (
    UntruncatedToolCallData,
    UntruncatedToolUsage,
    is_untruncated_tool,
)
from experimental.ran.agent.replay_eval.model_config import ModelConfig
from experimental.ran.agent.replay_eval.utils import load_model_config
from experimental.ran.utils.ri_utils import (
    get_chat_host_request_factory,
)
from research.tools.chat_replay.replay_utils import (
    run_model_v2,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

CACHE_DIRECTORY = "/mnt/efs/augment/user/ran/untruncated_tools_eval_cache"
DETAILED_OUTPUT_DIRECTORY = (
    "/mnt/efs/augment/user/ran/untruncated_tools_eval_detailed_outputs"
)


def extract_tool_calls_from_response_nodes(
    response_nodes: List[ChatResultNode],
) -> List[Dict[str, Any]]:
    """Extract tool calls from response nodes in a simplified format.

    Args:
        response_nodes: List of ChatResultNode objects from model response

    Returns:
        List of dictionaries containing tool call information with keys:
        - tool_name: Name of the tool
        - tool_input: Input parameters to the tool
        - tool_use_id: Unique identifier for the tool use
    """
    tool_calls = []

    for node in response_nodes:
        if (
            hasattr(node, "type")
            and node.type == ChatResultNodeType.TOOL_USE
            and hasattr(node, "tool_use")
            and node.tool_use
        ):
            tool_calls.append(
                {
                    "tool_name": node.tool_use.name,
                    "tool_input": node.tool_use.input,
                    "tool_use_id": node.tool_use.tool_use_id,
                }
            )

    return tool_calls


def get_untruncated_tool_definitions(
    tool_call_data: Optional[UntruncatedToolCallData] = None,
) -> List[ToolDefinition]:
    """Get tool definitions for untruncated content tools, optionally merged with original tool definitions.

    Args:
        tool_call_data: Optional tool call data containing original request information.
                       If provided, the original tool definitions will be extracted and merged.

    Returns:
        List of ToolDefinition objects including untruncated tools and optionally original tools
    """
    # Define the untruncated tools
    view_range_tool = ToolDefinition(
        name="view-range-untruncated",
        description="View a specific range of lines from untruncated content",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "reference_id": {
                        "type": "string",
                        "description": "The reference ID of the truncated content (found in the truncation footer)",
                    },
                    "start_line": {
                        "type": "integer",
                        "description": "The starting line number (1-based, inclusive)",
                    },
                    "end_line": {
                        "type": "integer",
                        "description": "The ending line number (1-based, inclusive)",
                    },
                },
                "required": ["reference_id", "start_line", "end_line"],
            }
        ),
    )

    search_tool = ToolDefinition(
        name="search-untruncated",
        description="Search for a term within untruncated content",
        input_schema_json=json.dumps(
            {
                "type": "object",
                "properties": {
                    "reference_id": {
                        "type": "string",
                        "description": "The reference ID of the truncated content (found in the truncation footer)",
                    },
                    "search_term": {
                        "type": "string",
                        "description": "The term to search for within the content",
                    },
                    "context_lines": {
                        "type": "integer",
                        "description": "Number of context lines to include before and after matches (default: 2)",
                    },
                },
                "required": ["reference_id", "search_term"],
            }
        ),
    )

    untruncated_tools = [view_range_tool, search_tool]

    # If tool_call_data is provided, extract original tool definitions
    if tool_call_data is not None:
        original_tools = extract_original_tool_definitions(tool_call_data)
        # Merge original tools with untruncated tools, avoiding duplicates
        all_tools = original_tools.copy()

        # Add untruncated tools if they're not already present
        for tool in untruncated_tools:
            if tool.name not in {t.name for t in all_tools}:
                all_tools.append(tool)

        if len(all_tools) > len(original_tools):
            logger.info(
                f"Merged {len(original_tools)} original tools with {len(untruncated_tools)} untruncated tools "
                f"(total: {len(all_tools)} tools)"
            )
        return all_tools

    # If no tool_call_data provided, return only untruncated tools (backward compatibility)
    return untruncated_tools


def extract_original_tool_definitions(
    tool_call_data: UntruncatedToolCallData,
) -> List[ToolDefinition]:
    """Extract the original tool definitions from the tool call data.

    Args:
        tool_call_data: The tool call data containing request information

    Returns:
        List of ToolDefinition objects from the original request
    """
    original_tools = []

    try:
        # Get the tenant and request factories
        _get_chat_host_request = get_chat_host_request_factory(
            tool_call_data.tenant_name
        )

        # Get the original chat request for the first turn in the conversation
        # We need to find a request that contains the tool definitions
        chat_request = _get_chat_host_request(tool_call_data.request_id)

        if chat_request and hasattr(chat_request, "tool_definitions"):
            for tool_def in chat_request.tool_definitions:
                original_tools.append(
                    ToolDefinition(
                        name=tool_def.name,
                        description=tool_def.description,
                        input_schema_json=tool_def.input_schema_json,
                    )
                )

            logger.info(
                f"Extracted {len(original_tools)} tool definitions from original request {tool_call_data.request_id}"
            )
        else:
            logger.warning(
                f"No tool definitions found in original request {tool_call_data.request_id}"
            )

    except Exception as e:
        logger.error(
            f"Error extracting original tool definitions from {tool_call_data.request_id}: {e}"
        )

    return original_tools


def parse_xml_tool_calls(response_text: str) -> List[Dict]:
    """Parse tool calls from XML format as a fallback.

    This handles cases where the model outputs tool calls in XML format
    instead of using proper tool calling API.

    Args:
        response_text: The response text that may contain XML tool calls

    Returns:
        List of dictionaries with tool call information
    """
    tool_calls = []

    # Look for <function_calls> blocks
    function_calls_pattern = r"<function_calls>(.*?)</function_calls>"
    function_calls_matches = re.findall(
        function_calls_pattern, response_text, re.DOTALL
    )

    for function_calls_block in function_calls_matches:
        # Look for <invoke> blocks within each function_calls block
        invoke_pattern = r'<invoke name="([^"]+)">\s*<parameter name="([^"]+)">([^<]*)</parameter>\s*</invoke>'
        invoke_matches = re.findall(invoke_pattern, function_calls_block, re.DOTALL)

        for tool_name, param_name, param_value in invoke_matches:
            if is_untruncated_tool(tool_name):
                # Try to parse as JSON if it looks like JSON, otherwise treat as string
                try:
                    if param_value.strip().startswith("{"):
                        parsed_params = json.loads(param_value.strip())
                    else:
                        parsed_params = {param_name: param_value.strip()}
                except json.JSONDecodeError:
                    parsed_params = {param_name: param_value.strip()}

                tool_calls.append({"name": tool_name, "parameters": parsed_params})

    return tool_calls


@dataclass
class UntruncatedToolsEvaluationResult:
    """Comprehensive evaluation result for a single sample."""

    # Sample identification
    request_id: str
    model_name: str
    evaluation_timestamp: str

    # Original input data
    original_launch_process_output: str
    numbered_launch_process_output: str
    launch_process_input: dict
    original_untruncated_usages: List[dict]

    # Model input
    analysis_prompt: str
    baseline_prompt: str
    system_prompt: Optional[str]

    # Model response (modified prompt with selective guidance)
    response_nodes: List[ChatResultNode]
    response_text: str

    # Baseline response (original prompt without modifications)
    baseline_response_nodes: List[ChatResultNode]
    baseline_response_text: str

    # Evaluation results
    success: bool
    untruncated_tools_called: int = 0
    view_range_calls: int = 0
    search_calls: int = 0
    total_lines_requested: int = 0
    average_lines_per_request: float = 0.0

    # Additional metadata
    attempt_number: int = 1
    error_message: Optional[str] = None


@dataclass
class UntruncatedToolsEvaluationSummary:
    """Summary of all evaluations for a model."""

    model_name: str
    evaluation_timestamp: str
    total_samples: int
    successful_evaluations: int

    # Key metrics
    samples_with_no_untruncated_calls: int = 0
    average_untruncated_calls_per_sample: float = 0.0
    average_lines_per_view_range_call: float = 0.0
    search_vs_view_range_ratio: float = 0.0  # search_calls / view_range_calls

    # Baseline comparison (original behavior)
    baseline_untruncated_calls: int = 0
    baseline_average_untruncated_calls_per_sample: float = 0.0
    baseline_average_lines_per_view_range_call: float = 0.0
    baseline_search_vs_view_range_ratio: float = 0.0
    reduction_in_tool_usage_percent: float = 0.0

    # Map from request_id to detailed result
    detailed_results: Dict[str, UntruncatedToolsEvaluationResult] = field(
        default_factory=dict
    )


def _append_truncation_footer(original_output: str) -> Tuple[str, str]:
    """Append a synthetic truncation footer with a fresh reference ID if not present.

    Returns (augmented_output, reference_id). We assume the provided original_output
    is the truncated portion. Range text is heuristically described.
    """
    # Heuristic: lines are already numbered. Determine displayed line count.
    line_count = len([line for line in original_output.split("\n") if line.strip()])
    reference_id = str(uuid.uuid4())
    range_text = f"first {line_count} lines"
    footer = (
        f"[This result was truncated. Showing {range_text}. Use view-range-untruncated or "
        f"search-untruncated tools to access the full content. Reference ID: {reference_id}]"
    )
    # Avoid duplicating if appears already
    if (
        "Reference ID:" in original_output
        and "This result was truncated" in original_output
    ):
        return original_output, reference_id
    augmented = f"{original_output}\n{footer}"
    return augmented, reference_id


def create_selective_untruncated_tools_message(
    original_output: str,
) -> str:
    """Create a chat message encouraging selective use of untruncated tools,
    appending a proper truncation footer with a valid Reference ID.
    """

    # Ensure truncation footer exists with fresh reference id
    original_output_with_footer, _ = _append_truncation_footer(original_output)

    user_message = f"""{original_output_with_footer}
**Only use view-range-untruncated or search-untruncated tools if the full output is strictly necessary to continue**, such as when:
- You need to find specific error details that are clearly truncated
- You need to search for specific patterns or text that might be elsewhere in the output
- The truncated output is genuinely insufficient for the task at hand

If you do need to use these tools:
- For view-range-untruncated: Request only the specific line ranges you actually need
- For search-untruncated: Use specific search terms rather than viewing large ranges
"""

    return user_message


def parse_structured_conversation_history(
    structured_history: List[Dict[str, Any]],
) -> List[Exchange]:
    """Parse structured conversation history into Exchange objects.

    Args:
        structured_history: List of structured conversation exchanges containing
            request/response nodes, tool calls, and metadata

    Returns:
        List of Exchange objects representing the conversation history
    """
    if not structured_history:
        return []

    exchanges = []
    current_user_exchange = None

    for entry in structured_history:
        if entry.get("type") == "user_message":
            # Start a new exchange with user message
            current_user_exchange = {
                "request_message": entry.get("request_message", ""),
                "request_nodes": deserialize_chat_request_nodes(
                    entry.get("request_nodes", [])
                ),
                "request_id": entry.get("request_id"),
            }
        elif entry.get("type") == "assistant_turn" and current_user_exchange:
            # Complete the exchange with assistant response
            response_nodes = []

            # Create response nodes from the assistant turn
            if entry.get("response_text"):
                response_nodes.append(
                    ChatResultNode(
                        id=1,
                        type=ChatResultNodeType.RAW_RESPONSE,
                        content=entry.get("response_text", ""),
                        tool_use=None,
                    )
                )

            # Add tool use node if present
            tool_call = entry.get("tool_call")
            if tool_call and tool_call.get("tool_use"):
                tool_use_data = tool_call["tool_use"]
                response_nodes.append(
                    ChatResultNode(
                        id=len(response_nodes) + 1,
                        type=ChatResultNodeType.TOOL_USE,
                        content="",
                        tool_use=ChatResultToolUse(
                            tool_use_id=tool_use_data.get("tool_use_id", ""),
                            name=tool_use_data.get("name", ""),
                            input=tool_use_data.get("input", {}),
                        ),
                    )
                )

            # Create the exchange
            # Note: Exchange class expects request_message and response_text as strings or node lists
            # For structured data, we pass the nodes directly as the message content
            exchange = Exchange(
                request_message=current_user_exchange["request_nodes"],
                response_text=response_nodes,
                request_id=entry.get("request_id"),
            )
            exchanges.append(exchange)

            # If this assistant turn has no tool call, reset for next user message
            if not tool_call:
                current_user_exchange = None

    return exchanges


def deserialize_chat_request_nodes(
    serialized_nodes: List[Dict[str, Any]],
) -> List[ChatRequestNode]:
    """Deserialize request nodes from dictionary format back to ChatRequestNode objects.

    Args:
        serialized_nodes: List of serialized node dictionaries

    Returns:
        List of ChatRequestNode objects
    """
    nodes = []

    for node_data in serialized_nodes:
        node = ChatRequestNode(
            id=node_data.get("id", 0),
            type=ChatRequestNodeType(node_data.get("type", 0)),
            text_node=None,
            tool_result_node=None,
            image_node=None,
        )

        # Deserialize specific node types
        if "text_node" in node_data:
            node.text_node = ChatRequestText(content=node_data["text_node"]["content"])
        elif "tool_result_node" in node_data:
            tool_result_data = node_data["tool_result_node"]
            node.tool_result_node = ChatRequestToolResult(
                tool_use_id=tool_result_data["tool_use_id"],
                content=tool_result_data["content"],
                is_error=tool_result_data["is_error"],
                request_id=tool_result_data.get("request_id"),
            )
        elif "image_node" in node_data:
            image_data = node_data["image_node"]
            node.image_node = ChatRequestImage(
                image_data=image_data["image_data"],
                format=ImageFormatType(image_data["format"]),
            )

        nodes.append(node)

    return nodes


def parse_conversation_history(context_history: str) -> List[Exchange]:
    """Parse conversation history string into Exchange objects.

    Args:
        context_history: String containing conversation history in format:
            "User: message1\nAssistant: response1\nUser: message2\nAssistant: response2..."

    Returns:
        List of Exchange objects representing the conversation history
    """
    if not context_history:
        return []

    exchanges = []
    lines = context_history.strip().split("\n")

    current_user_msg = ""
    current_assistant_msg = ""

    for line in lines:
        line = line.strip()
        if line.startswith("User: "):
            # If we have a previous exchange, save it
            if current_user_msg and current_assistant_msg:
                exchanges.append(
                    Exchange(
                        request_message=current_user_msg,
                        response_text=current_assistant_msg,
                    )
                )
            # Start new exchange
            current_user_msg = line[6:]  # Remove "User: " prefix
            current_assistant_msg = ""
        elif line.startswith("Assistant: "):
            current_assistant_msg = line[11:]  # Remove "Assistant: " prefix
        elif current_user_msg and not current_assistant_msg:
            # Continuation of user message
            current_user_msg += "\n" + line
        elif current_assistant_msg:
            # Continuation of assistant message
            current_assistant_msg += "\n" + line

    # Add the last exchange if we have both parts
    if current_user_msg and current_assistant_msg:
        exchanges.append(
            Exchange(
                request_message=current_user_msg,
                response_text=current_assistant_msg,
            )
        )

    return exchanges


def parse_baseline_metrics_from_detailed_samples(
    samples_file: str,
) -> Dict[str, float]:
    """Parse baseline metrics from the samples file.

    NOTE: We normalize untruncated tool usage to the *initial decision* (0 or 1 per
    sample) to match the evaluation methodology, which only captures whether the
    model chose to invoke an untruncated tool on its first opportunity. Real-world
    baseline data may contain multiple consecutive untruncated tool calls for a
    single sample (follow-up range views or searches); those extra calls are now
    ignored for the primary baseline average so reduction percentages are a
    like-for-like comparison.

    Args:
        samples_file: Path to the untruncated_tools_samples.json file

    Returns:
        Dictionary containing baseline metrics:
        - baseline_average_untruncated_calls_per_sample (normalized 0/1)
        - baseline_average_lines_per_view_range_call (raw)
        - baseline_search_vs_view_range_ratio (raw)
    """
    try:
        with open(samples_file, "r") as f:
            samples_data = json.load(f)

        if not samples_data:
            logger.warning(f"No samples found in {samples_file}")
            return {
                "baseline_average_untruncated_calls_per_sample": 0.0,
                "baseline_average_lines_per_view_range_call": 0.0,
                "baseline_search_vs_view_range_ratio": 0.0,
            }

        # Raw totals (may exceed 1 per sample) retained for ratio/line metrics
        total_view_range_calls = 0
        total_search_calls = 0
        total_lines_requested = 0
        # Normalized count: 1 if any untruncated tool used in that sample else 0
        normalized_initial_decision_calls = 0

        for sample in samples_data:
            untruncated_usages = sample.get("untruncated_tool_usages", []) or []
            if untruncated_usages:
                # Treat any usage as a single initial decision
                normalized_initial_decision_calls += 1

            # Analyze each tool usage (raw) for supplementary metrics
            for usage in untruncated_usages:
                tool_name = usage.get("tool_name", "")
                tool_input = usage.get("tool_input", {})

                if tool_name == "view-range-untruncated":
                    total_view_range_calls += 1
                    # Calculate lines requested
                    start_line = tool_input.get("start_line", 0)
                    end_line = tool_input.get("end_line", 0)
                    if start_line and end_line:
                        lines_requested = end_line - start_line + 1
                        total_lines_requested += lines_requested
                elif tool_name == "search-untruncated":
                    total_search_calls += 1

        # Calculate baseline metrics
        num_samples = len(samples_data)
        baseline_average_untruncated_calls_per_sample = (
            normalized_initial_decision_calls / num_samples if num_samples > 0 else 0.0
        )

        baseline_average_lines_per_view_range_call = (
            total_lines_requested / total_view_range_calls
            if total_view_range_calls > 0
            else 0.0
        )

        baseline_search_vs_view_range_ratio = (
            total_search_calls / total_view_range_calls
            if total_view_range_calls > 0
            else 0.0
        )

        logger.info(
            f"Parsed baseline metrics from {num_samples} samples (normalized initial decision metric):"
        )
        logger.info(
            f"  - Normalized initial untruncated decision rate: {baseline_average_untruncated_calls_per_sample:.2f}"
        )
        logger.info(
            f"  - Average lines per view-range call (raw): {baseline_average_lines_per_view_range_call:.1f}"
        )
        logger.info(
            f"  - Search vs view-range ratio (raw): {baseline_search_vs_view_range_ratio:.2f}"
        )

        return {
            "baseline_average_untruncated_calls_per_sample": baseline_average_untruncated_calls_per_sample,
            "baseline_average_lines_per_view_range_call": baseline_average_lines_per_view_range_call,
            "baseline_search_vs_view_range_ratio": baseline_search_vs_view_range_ratio,
        }

    except Exception as e:
        logger.error(f"Error parsing baseline metrics from {samples_file}: {e}")
        return {
            "baseline_average_untruncated_calls_per_sample": 0.0,
            "baseline_average_lines_per_view_range_call": 0.0,
            "baseline_search_vs_view_range_ratio": 0.0,
        }


def create_detailed_evaluation_results(
    evaluation_summary: UntruncatedToolsEvaluationSummary,
) -> List[Dict]:
    """Create consolidated detailed evaluation results.

    This merges the previously separate "detailed_results_*" and
    "evaluation_results_detailed_*" outputs into a single comprehensive
    structure (one list entry per request) that contains:
      - Original sample context (launch output, context history, input)
      - Simplified evaluated untruncated tool usages
      - All evaluation metrics & prompts
      - Extracted tool call summaries for both evaluation and (if present) baseline

    Returns a list (not a dict keyed by request_id) to stay compatible with the
    original samples file style while enriching each entry with additional
    evaluation metadata.
    """
    detailed_results: List[Dict[str, Any]] = []

    for request_id, result in evaluation_summary.detailed_results.items():
        # Extract evaluated untruncated tool usages from response nodes
        evaluated_tool_usages: List[Dict[str, Any]] = []
        for node in result.response_nodes:
            if node.type == ChatResultNodeType.TOOL_USE and node.tool_use:
                tool_name = node.tool_use.name
                if is_untruncated_tool(tool_name):
                    evaluated_tool_usages.append(
                        {
                            "tool_name": tool_name,
                            "tool_input": node.tool_use.input,
                            # tool_output may not be available on every node
                            "tool_output": getattr(node, "tool_output", ""),
                            "request_id": request_id,
                            "turn_index": 1,  # Single-turn evaluation
                        }
                    )

        # Extract full tool call lists (superset fields kept for analysis)
        response_tool_calls = extract_tool_calls_from_response_nodes(
            result.response_nodes
        )

        detailed_entry = {
            "request_id": request_id,
            "launch_process_output": result.original_launch_process_output,
            "launch_process_input": result.launch_process_input,
            "untruncated_tool_usages": evaluated_tool_usages,
            "tenant_name": "evaluation",
            "output_line_count": len(result.numbered_launch_process_output.split("\n")),
            "num_untruncated_usages": len(evaluated_tool_usages),
            # Unified evaluation metadata + previously separate detailed fields
            "evaluation_metadata": {
                "model_name": result.model_name,
                "evaluation_timestamp": result.evaluation_timestamp,
                "success": result.success,
                "untruncated_tools_called": result.untruncated_tools_called,
                "view_range_calls": result.view_range_calls,
                "search_calls": result.search_calls,
                "total_lines_requested": result.total_lines_requested,
                "average_lines_per_request": result.average_lines_per_request,
                "original_untruncated_usages_count": len(
                    result.original_untruncated_usages
                ),
                "analysis_prompt": result.analysis_prompt,
                "baseline_prompt": result.baseline_prompt,
                "response_text": result.response_text,
                "baseline_response_text": result.baseline_response_text,
                "response_tool_calls": response_tool_calls,
                "baseline_response_tool_calls": result.original_untruncated_usages,
                "error_message": result.error_message,
            },
        }

        detailed_results.append(detailed_entry)

    return detailed_results


def evaluate_model_on_untruncated_tools(
    tool_call_data: UntruncatedToolCallData,
    model_config: ModelConfig,
    attempt_number: int = 1,
) -> UntruncatedToolsEvaluationResult:
    """Evaluate a model's response to launch-process output with selective untruncated tools guidance.

    Baseline generation (a second model run) is disabled; we only run the
    selective guidance version and treat historical usage statistics as the baseline.
    """
    logger.info(
        f"Evaluating model {model_config.name} on request {tool_call_data.request_id} "
        f"(attempt {attempt_number})"
    )

    # Single analysis message (baseline function now delegates here anyway)
    analysis_message = create_selective_untruncated_tools_message(
        original_output=tool_call_data.launch_process_output,
    )
    baseline_message = ""  # For exception response

    # Parse conversation history for context
    # Use structured conversation history if available, otherwise fall back to string parsing
    if (
        hasattr(tool_call_data, "structured_conversation_history")
        and tool_call_data.structured_conversation_history
    ):
        conversation_history = parse_structured_conversation_history(
            tool_call_data.structured_conversation_history
        )
    else:
        conversation_history = parse_conversation_history(
            tool_call_data.context_history
        )

    # Get tool definitions - include untruncated tools merged with original tools
    untruncated_tool_definitions = get_untruncated_tool_definitions(tool_call_data)
    tool_definitions = model_config.get_tool_definitions(untruncated_tool_definitions)

    try:
        # Run only the selective / analysis prompt
        modified_prompt_output = StructuredChatPromptOutput(
            system_prompt=model_config.system_prompt,
            chat_history=conversation_history,
            message=analysis_message,
            retrieved_chunks_in_prompt=[],
            retrieval_as_tool=False,
        )

        logger.info(f"Running evaluation (single-run) for {tool_call_data.request_id}")
        response = run_model_v2(
            prompt_output=modified_prompt_output,
            base_model_version=model_config.anthropic_model,
            client_type=model_config.client_type,
            tool_definitions=tool_definitions,
            yield_final_parameters=True,
        )

        # Parse modified response
        response_nodes = response.response_nodes
        response_text = ""
        for node in response_nodes:
            if (
                hasattr(node, "content")
                and hasattr(node, "type")
                and node.type == ChatResultNodeType.RAW_RESPONSE
            ):
                response_text += node.content

        # Analyze the response for untruncated tool usage
        untruncated_tools_called = 0
        view_range_calls = 0
        search_calls = 0
        total_lines_requested = 0

        # Check for proper tool calls first
        for node in response_nodes:
            if node.type == ChatResultNodeType.TOOL_USE and node.tool_use:
                tool_name = node.tool_use.name
                if is_untruncated_tool(tool_name):
                    untruncated_tools_called += 1

                    if tool_name == "view-range-untruncated":
                        view_range_calls += 1
                        # Calculate lines requested
                        tool_input = node.tool_use.input
                        start_line = tool_input.get("start_line", 0)
                        end_line = tool_input.get("end_line", 0)
                        if start_line and end_line:
                            lines_requested = end_line - start_line + 1
                            total_lines_requested += lines_requested
                    elif tool_name == "search-untruncated":
                        search_calls += 1

        # Fallback: check for XML-formatted tool calls in response text (rare)
        if untruncated_tools_called == 0:
            xml_tool_calls = parse_xml_tool_calls(response_text)
            for tool_call in xml_tool_calls:
                tool_name = tool_call["name"]
                if is_untruncated_tool(tool_name):
                    untruncated_tools_called += 1

                    if tool_name == "view-range-untruncated":
                        view_range_calls += 1
                        # Calculate lines requested
                        tool_params = tool_call["parameters"]
                        start_line = tool_params.get("start_line", 0)
                        end_line = tool_params.get("end_line", 0)
                        if isinstance(start_line, str):
                            start_line = int(start_line) if start_line.isdigit() else 0
                        if isinstance(end_line, str):
                            end_line = int(end_line) if end_line.isdigit() else 0
                        if start_line and end_line:
                            lines_requested = end_line - start_line + 1
                            total_lines_requested += lines_requested
                    elif tool_name == "search-untruncated":
                        search_calls += 1

        # Calculate average lines per request
        average_lines_per_request = (
            total_lines_requested / view_range_calls if view_range_calls > 0 else 0.0
        )

        # Success is defined as not calling any untruncated tools (showing restraint)
        success = untruncated_tools_called == 0

        # Convert original untruncated usages to dict format
        original_usages_dict = []
        for usage in tool_call_data.untruncated_tool_usages:
            original_usages_dict.append(
                {
                    "tool_name": usage.tool_name,
                    "tool_input": usage.tool_input,
                    "tool_output": usage.tool_output,
                    "request_id": usage.request_id,
                    "turn_index": usage.turn_index,
                }
            )

        # Populate baseline fields from authentic historical data captured in samples
        baseline_response_nodes: List[ChatResultNode] = []  # we only have raw text
        baseline_response_text = getattr(tool_call_data, "response_text", "")
        # Baseline prompt should reflect the original launch process output (without synthetic truncation footer modifications)
        baseline_message = tool_call_data.launch_process_output

        return UntruncatedToolsEvaluationResult(
            request_id=tool_call_data.request_id,
            model_name=model_config.name,
            evaluation_timestamp=datetime.now().isoformat(),
            original_launch_process_output=tool_call_data.launch_process_output,
            numbered_launch_process_output=tool_call_data.numbered_launch_process_output,
            launch_process_input=tool_call_data.launch_process_input,
            original_untruncated_usages=original_usages_dict,
            analysis_prompt=analysis_message,
            baseline_prompt=baseline_message,
            system_prompt=getattr(model_config, "system_prompt", None),
            response_nodes=response_nodes,
            response_text=response_text,
            baseline_response_nodes=baseline_response_nodes,
            baseline_response_text=baseline_response_text,
            success=success,
            untruncated_tools_called=untruncated_tools_called,
            view_range_calls=view_range_calls,
            search_calls=search_calls,
            total_lines_requested=total_lines_requested,
            average_lines_per_request=average_lines_per_request,
            attempt_number=attempt_number,
        )

    except Exception as e:
        logger.error(
            f"Error evaluating model {model_config.name} on request {tool_call_data.request_id}: {e}",
            exc_info=True,
        )
        return UntruncatedToolsEvaluationResult(
            request_id=tool_call_data.request_id,
            model_name=model_config.name,
            evaluation_timestamp=datetime.now().isoformat(),
            original_launch_process_output=tool_call_data.launch_process_output,
            numbered_launch_process_output=tool_call_data.numbered_launch_process_output,
            launch_process_input=tool_call_data.launch_process_input,
            original_untruncated_usages=[],
            analysis_prompt=analysis_message,
            baseline_prompt=baseline_message,
            system_prompt=getattr(model_config, "system_prompt", None),
            response_nodes=[],
            response_text="",
            baseline_response_nodes=[],
            baseline_response_text="",
            success=False,
            attempt_number=attempt_number,
            error_message=str(e),
        )


def evaluate_model_on_untruncated_tools_samples(
    tool_call_data_list: List[UntruncatedToolCallData],
    model_config: ModelConfig,
    baseline_metrics: Optional[Dict[str, float]] = None,
    max_samples: Optional[int] = None,
) -> UntruncatedToolsEvaluationSummary:
    """Evaluate a model on multiple untruncated tools samples.

    Args:
        tool_call_data_list: List of tool call data to evaluate
        model_config: Configuration for the model to evaluate
        baseline_metrics: Optional baseline metrics from original samples
        max_samples: Maximum number of samples to evaluate (None for all)

    Returns:
        UntruncatedToolsEvaluationSummary with aggregated results
    """
    if max_samples:
        tool_call_data_list = tool_call_data_list[:max_samples]

    logger.info(
        f"Evaluating model {model_config.name} on {len(tool_call_data_list)} samples"
    )

    detailed_results = {}
    successful_evaluations = 0
    samples_with_no_untruncated_calls = 0
    total_untruncated_calls = 0
    total_view_range_calls = 0
    total_search_calls = 0
    total_lines_requested = 0
    baseline_untruncated_calls = 0

    for i, tool_call_data in enumerate(tool_call_data_list):
        logger.info(
            f"Processing sample {i+1}/{len(tool_call_data_list)}: {tool_call_data.request_id}"
        )

        # Evaluate the sample
        result = evaluate_model_on_untruncated_tools(
            tool_call_data=tool_call_data,
            model_config=model_config,
        )

        detailed_results[tool_call_data.request_id] = result

        if not result.error_message:
            successful_evaluations += 1

            # Aggregate metrics
            total_untruncated_calls += result.untruncated_tools_called
            total_view_range_calls += result.view_range_calls
            total_search_calls += result.search_calls
            total_lines_requested += result.total_lines_requested

            if result.untruncated_tools_called == 0:
                samples_with_no_untruncated_calls += 1

        # Count number of baseline requests that made any untruncated view/search calls (original behavior)
        baseline_untruncated_calls += len(tool_call_data.untruncated_tool_usages)

    # Calculate summary metrics
    average_untruncated_calls_per_sample = (
        total_untruncated_calls / successful_evaluations
        if successful_evaluations > 0
        else 0.0
    )

    average_lines_per_view_range_call = (
        total_lines_requested / total_view_range_calls
        if total_view_range_calls > 0
        else 0.0
    )

    search_vs_view_range_ratio = (
        total_search_calls / total_view_range_calls
        if total_view_range_calls > 0
        else 0.0
    )

    # Calculate reduction in tool usage
    baseline_average = 1  # Always called tools
    current_average = average_untruncated_calls_per_sample
    reduction_percent = (
        ((baseline_average - current_average) / baseline_average * 100)
        if baseline_average > 0
        else 0.0
    )

    # Extract baseline metrics if provided
    baseline_metrics = baseline_metrics or {}
    baseline_average_untruncated_calls_per_sample = baseline_metrics.get(
        "baseline_average_untruncated_calls_per_sample", baseline_average
    )
    baseline_average_lines_per_view_range_call = baseline_metrics.get(
        "baseline_average_lines_per_view_range_call", 0.0
    )
    baseline_search_vs_view_range_ratio = baseline_metrics.get(
        "baseline_search_vs_view_range_ratio", 0.0
    )

    return UntruncatedToolsEvaluationSummary(
        model_name=model_config.name,
        evaluation_timestamp=datetime.now().isoformat(),
        total_samples=len(tool_call_data_list),
        successful_evaluations=successful_evaluations,
        samples_with_no_untruncated_calls=samples_with_no_untruncated_calls,
        average_untruncated_calls_per_sample=average_untruncated_calls_per_sample,
        average_lines_per_view_range_call=average_lines_per_view_range_call,
        search_vs_view_range_ratio=search_vs_view_range_ratio,
        baseline_untruncated_calls=baseline_untruncated_calls,
        baseline_average_untruncated_calls_per_sample=baseline_average_untruncated_calls_per_sample,
        baseline_average_lines_per_view_range_call=baseline_average_lines_per_view_range_call,
        baseline_search_vs_view_range_ratio=baseline_search_vs_view_range_ratio,
        reduction_in_tool_usage_percent=reduction_percent,
        detailed_results=detailed_results,
    )


def main():
    """Main function to run the untruncated tools focus runner from command line."""
    parser = argparse.ArgumentParser(
        description="Evaluate model performance on untruncated tools usage after launch-process commands"
    )
    parser.add_argument(
        "--samples-glob",
        type=str,
        help="Glob pattern for samples files (default: untruncated_tools_samples_*.json)",
        default="untruncated_tools_samples_*.json",
    )
    parser.add_argument(
        "--model-config",
        type=str,
        required=True,
        help="Path to model configuration file",
    )
    parser.add_argument(
        "--namespace",
        type=str,
        default="i0",
        help="Namespace for data access (default: i0)",
    )
    parser.add_argument(
        "--max-samples",
        type=int,
        default=100,
        help="Maximum number of samples to evaluate",
    )
    parser.add_argument(
        "--min-lines",
        type=int,
        default=20,
        help="Minimum number of lines in launch-process output (default: 20)",
    )
    parser.add_argument(
        "--max-turns-after-launch",
        type=int,
        default=5,
        help="Maximum number of turns to look ahead for untruncated tools (default: 5)",
    )
    parser.add_argument(
        "--output-file",
        type=str,
        default="untruncated_tools_evaluation_results.json",
        help="Output file for evaluation results (default: untruncated_tools_evaluation_results.json)",
    )
    parser.add_argument(
        "--detailed-output-dir",
        type=str,
        default=DETAILED_OUTPUT_DIRECTORY,
        help=f"Directory for detailed output files (default: {DETAILED_OUTPUT_DIRECTORY})",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    import hashlib

    # Load model configuration
    logger.info(f"Loading model configuration from {args.model_config}")
    model_config = load_model_config(args.model_config)
    logger.info(f"Loaded model: {model_config.name}")

    # Load and merge all matching samples files via glob pattern, de-duplicating by request_id
    import glob

    pattern = args.samples_glob
    matched_files = sorted(glob.glob(pattern))
    if not matched_files:
        logger.error(f"No samples files matched glob pattern: {pattern}")
        return

    logger.info(
        f"Found {len(matched_files)} samples files matching pattern {pattern}: {matched_files}"
    )

    detailed_samples: List[Dict] = []
    detailed_samples_schema_version = None
    seen_request_ids = set()
    skipped_duplicates = 0

    for file_path in matched_files:
        try:
            with open(file_path, "r") as f:
                file_samples = json.load(f)
            if not isinstance(file_samples, list):
                logger.warning(
                    f"Samples file {file_path} did not contain a list (type={type(file_samples)}); skipping"
                )
                continue
            for sample in file_samples:
                if not isinstance(sample, dict):
                    logger.warning(
                        f"Non-dict sample entry in {file_path}; skipping entry of type {type(sample)}"
                    )
                    continue
                rid = sample.get("request_id")
                if not rid:
                    logger.warning(
                        f"Sample missing request_id in file {file_path}; skipping entry"
                    )
                    continue
                if rid in seen_request_ids:
                    skipped_duplicates += 1
                    continue
                seen_request_ids.add(rid)
                detailed_samples.append(sample)
        except Exception as e:  # noqa: BLE001
            logger.error(f"Failed to load samples file {file_path}: {e}")

    if not detailed_samples:
        logger.error(
            "No valid samples loaded after processing all matched files; cannot proceed."
        )
        return

    first = detailed_samples[0]
    detailed_samples_schema_version = first.get("schema_version")
    logger.info(
        f"Loaded {len(detailed_samples)} unique samples (skipped {skipped_duplicates} duplicates); detected schema_version={detailed_samples_schema_version}"
    )

    tool_call_data_list = []
    used_detailed_samples = False

    if (
        detailed_samples_schema_version
        and detailed_samples_schema_version >= 2
        and detailed_samples
    ):
        # Deserialize directly, perform checksum validation, skip on mismatch
        logger.info(
            "Using pre-extracted detailed samples (schema_version>=2); skipping reconstruction from request IDs"
        )
        for sample in detailed_samples:
            if not isinstance(sample, dict):
                logger.warning(
                    "Skipping non-dict sample entry in detailed samples file"
                )
                continue
            checksum = sample.get("launch_output_checksum")
            launch_output = sample.get("launch_process_output", "")
            try:
                recomputed = hashlib.sha256(launch_output.encode("utf-8")).hexdigest()
            except Exception as e:  # noqa: BLE001
                logger.warning(
                    f"Failed to compute checksum for request_id={sample.get('request_id')}: {e}; skipping sample"
                )
                continue
            if checksum and checksum != recomputed:
                logger.warning(
                    f"Checksum mismatch for request_id={sample.get('request_id')} (stored={checksum}, recomputed={recomputed}); skipping sample"
                )
                continue
            if not sample.get("integrity_passed", True):
                logger.warning(
                    f"Integrity flag false for request_id={sample.get('request_id')}; skipping sample"
                )
                continue

            # Build UntruncatedToolCallData
            try:
                usages: List[UntruncatedToolUsage] = []
                for u in sample.get("untruncated_tool_usages", []) or []:
                    if not isinstance(u, dict):
                        continue
                    usages.append(
                        UntruncatedToolUsage(
                            tool_name=u.get("tool_name", ""),
                            tool_input=u.get("tool_input", {}),
                            tool_output=u.get("tool_output", ""),
                            request_id=u.get("request_id", ""),
                            turn_index=int(u.get("turn_index", 0) or 0),
                            reference_id_valid=bool(u.get("reference_id_valid", True)),
                        )
                    )
                tool_call_data_list.append(
                    UntruncatedToolCallData(
                        request_id=sample.get("request_id", ""),
                        launch_process_output=launch_output,
                        numbered_launch_process_output=sample.get(
                            "numbered_launch_process_output", ""
                        ),
                        context_history=sample.get("context_history", ""),
                        launch_process_input=sample.get("launch_process_input", {}),
                        untruncated_tool_usages=usages,
                        tenant_name=sample.get("tenant_name", ""),
                        session_id=sample.get("session_id"),
                        original_tool_definitions=sample.get(
                            "original_tool_definitions", []
                        ),
                        launch_output_checksum=checksum,
                    )
                )
            except Exception as e:  # noqa: BLE001
                logger.warning(
                    f"Failed to deserialize detailed sample request_id={sample.get('request_id')}: {e}"
                )
        used_detailed_samples = True
        logger.info(
            f"Loaded {len(tool_call_data_list)} usable detailed samples (after integrity & checksum filtering)"
        )

    if not used_detailed_samples:
        logger.error(
            "Detailed samples file missing or invalid and legacy basic samples path has been removed. Cannot proceed."
        )
        return

    if not tool_call_data_list:
        logger.error("No tool call data available for evaluation. Exiting.")
        return

    # Baseline metrics (still parsed from detailed file if available)
    baseline_metrics = None
    # Use all merged samples list (detailed_samples) for baseline metrics
    baseline_metrics = parse_baseline_metrics_from_detailed_samples(
        samples_file=matched_files[-1] if matched_files else ""
    )

    # Evaluate the model
    logger.info(f"Evaluating model {model_config.name}...")
    evaluation_summary = evaluate_model_on_untruncated_tools_samples(
        tool_call_data_list=tool_call_data_list,
        model_config=model_config,
        baseline_metrics=baseline_metrics,
        max_samples=args.max_samples,
    )

    # Save summary results (also embedded in consolidated output file)
    summary_data = {
        "model_name": evaluation_summary.model_name,
        "evaluation_timestamp": evaluation_summary.evaluation_timestamp,
        "total_samples": evaluation_summary.total_samples,
        "successful_evaluations": evaluation_summary.successful_evaluations,
        "samples_with_no_untruncated_calls": evaluation_summary.samples_with_no_untruncated_calls,
        "average_untruncated_calls_per_sample": evaluation_summary.average_untruncated_calls_per_sample,
        "average_lines_per_view_range_call": evaluation_summary.average_lines_per_view_range_call,
        "search_vs_view_range_ratio": evaluation_summary.search_vs_view_range_ratio,
        "baseline_untruncated_calls": evaluation_summary.baseline_untruncated_calls,
        "baseline_average_untruncated_calls_per_sample": evaluation_summary.baseline_average_untruncated_calls_per_sample,
        "baseline_average_lines_per_view_range_call": evaluation_summary.baseline_average_lines_per_view_range_call,
        "baseline_search_vs_view_range_ratio": evaluation_summary.baseline_search_vs_view_range_ratio,
        "reduction_in_tool_usage_percent": evaluation_summary.reduction_in_tool_usage_percent,
        "success_rate": (
            evaluation_summary.samples_with_no_untruncated_calls
            / evaluation_summary.successful_evaluations
            * 100
            if evaluation_summary.successful_evaluations > 0
            else 0.0
        ),
    }

    # Build consolidated detailed evaluation results list
    consolidated_entries = create_detailed_evaluation_results(evaluation_summary)

    # Single comprehensive output (list with a leading summary object for easy parsing)
    consolidated_output = {
        "summary": summary_data,
        "results": consolidated_entries,
    }

    consolidated_filename = f"evaluation_results_consolidated_{model_config.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    with open(consolidated_filename, "w") as f:
        json.dump(consolidated_output, f, indent=2)
    logger.info(f"Saved consolidated evaluation results to {consolidated_filename}")

    # Also save to detailed output directory as backup
    Path(args.detailed_output_dir).mkdir(parents=True, exist_ok=True)
    consolidated_backup = (
        Path(args.detailed_output_dir)
        / f"evaluation_results_consolidated_{model_config.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )
    with open(consolidated_backup, "w") as f:
        json.dump(consolidated_output, f, indent=2)
    logger.info(
        f"Saved consolidated evaluation results backup to {consolidated_backup}"
    )

    # Print summary statistics
    logger.info("=== EVALUATION SUMMARY ===")
    logger.info(f"Model: {evaluation_summary.model_name}")
    logger.info(f"Total samples: {evaluation_summary.total_samples}")
    logger.info(f"Successful evaluations: {evaluation_summary.successful_evaluations}")
    logger.info(
        f"Success rate (no untruncated calls): {summary_data['success_rate']:.1f}%"
    )

    logger.info("=== CURRENT METRICS ===")
    logger.info(
        f"Average untruncated calls per sample: {evaluation_summary.average_untruncated_calls_per_sample:.2f}"
    )
    if evaluation_summary.average_lines_per_view_range_call > 0:
        logger.info(
            f"Average lines per view-range call: {evaluation_summary.average_lines_per_view_range_call:.1f}"
        )
    if evaluation_summary.search_vs_view_range_ratio > 0:
        logger.info(
            f"Search vs view-range ratio: {evaluation_summary.search_vs_view_range_ratio:.2f}"
        )

    logger.info("=== BASELINE METRICS ===")
    logger.info(
        f"Baseline average untruncated calls per sample: {evaluation_summary.baseline_average_untruncated_calls_per_sample:.2f}"
    )
    if evaluation_summary.baseline_average_lines_per_view_range_call > 0:
        logger.info(
            f"Baseline average lines per view-range call: {evaluation_summary.baseline_average_lines_per_view_range_call:.1f}"
        )
    if evaluation_summary.baseline_search_vs_view_range_ratio > 0:
        logger.info(
            f"Baseline search vs view-range ratio: {evaluation_summary.baseline_search_vs_view_range_ratio:.2f}"
        )

    logger.info("=== COMPARISON ===")
    logger.info(
        f"Reduction in tool usage: {evaluation_summary.reduction_in_tool_usage_percent:.1f}%"
    )


if __name__ == "__main__":
    main()
