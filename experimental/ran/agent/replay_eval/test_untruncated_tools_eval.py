#!/usr/bin/env python3

"""
Simple test script to verify the untruncated tools evaluation components work correctly.
This is a lightweight test that doesn't require heavy dependencies.
"""

import json
import sys
import os

# Add the project root to Python path
sys.path.insert(0, "/home/<USER>/augment")


def test_is_untruncated_tool():
    """Test the untruncated tool detection function."""
    print("Testing is_untruncated_tool()...")

    # Simple implementation for testing
    def is_untruncated_tool(tool_name: str) -> bool:
        return tool_name in ["view-range-untruncated", "search-untruncated"]

    assert is_untruncated_tool("view-range-untruncated")
    assert is_untruncated_tool("search-untruncated")
    assert not is_untruncated_tool("launch-process")
    assert not is_untruncated_tool("view")
    assert not is_untruncated_tool("grep-search")

    print("✓ is_untruncated_tool() tests passed")


def test_file_structure():
    """Test that the files exist and have the expected structure."""
    print("Testing file structure...")

    # Check that the files exist
    sample_extractor_path = "/home/<USER>/augment/experimental/ran/agent/replay_eval/untruncated_tools_sample_extractor.py"
    focus_runner_path = "/home/<USER>/augment/experimental/ran/agent/replay_eval/untruncated_tools_focus_runner.py"

    assert os.path.exists(
        sample_extractor_path
    ), f"Sample extractor file not found: {sample_extractor_path}"
    assert os.path.exists(
        focus_runner_path
    ), f"Focus runner file not found: {focus_runner_path}"

    # Check that the files contain expected content
    with open(sample_extractor_path, "r") as f:
        sample_extractor_content = f.read()

    with open(focus_runner_path, "r") as f:
        focus_runner_content = f.read()

    # Check for key functions and classes
    assert "UntruncatedToolCallData" in sample_extractor_content
    assert "is_untruncated_tool" in sample_extractor_content
    assert (
        "extract_untruncated_tool_calls_from_conversations" in sample_extractor_content
    )
    assert "def main():" in sample_extractor_content

    assert "UntruncatedToolsEvaluationResult" in focus_runner_content
    assert "create_selective_untruncated_tools_message" in focus_runner_content
    assert "evaluate_model_on_untruncated_tools" in focus_runner_content
    assert "def main():" in focus_runner_content

    print("✓ File structure tests passed")


def run_all_tests():
    """Run all tests."""
    print("Running untruncated tools evaluation component tests...\n")

    try:
        test_is_untruncated_tool()
        test_file_structure()

        print(
            "\n🎉 All tests passed! The untruncated tools evaluation components are working correctly."
        )

        # Print usage instructions
        print("\n" + "=" * 60)
        print("USAGE INSTRUCTIONS")
        print("=" * 60)

        print("\n1. SAMPLE EXTRACTOR:")
        print(
            "   Extract samples where untruncated tools were used after launch-process:"
        )
        print(
            "   python experimental/ran/agent/replay_eval/untruncated_tools_sample_extractor.py \\"
        )
        print("     --namespace i0 \\")
        print("     --last-days 7 \\")
        print("     --limit 100 \\")
        print(
            "     --samples-file untruncated_tools_samples.json  # base name, timestamp appended automatically"
        )

        print("\n2. FOCUS RUNNER:")
        print("   Evaluate model performance with selective untruncated tools prompts:")
        print(
            "   python experimental/ran/agent/replay_eval/untruncated_tools_focus_runner.py \\"
        )
        print("     --samples-glob 'untruncated_tools_samples_*.json' \\")
        print("     --model-config path/to/model_config.json \\")
        print("     --namespace i0 \\")
        print("     --max-samples 50 \\")
        print("     --output-file evaluation_results.json")

        print("\n3. METRICS MEASURED:")
        print("   - Reduction in tool usage: % of samples with no untruncated calls")
        print("   - Line efficiency: Average lines requested per view-range call")
        print("   - Tool selection: Ratio of search vs view-range calls")

        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
