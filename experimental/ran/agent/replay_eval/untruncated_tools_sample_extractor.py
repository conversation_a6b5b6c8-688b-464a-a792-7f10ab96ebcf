#!/usr/bin/env python3

import logging
import random
import re
from datetime import timedelta, datetime
from typing import List, Tuple, Dict, Any, Set, Optional
from dataclasses import dataclass

from google.cloud import bigquery

from base.datasets.gcp_creds import get_gcp_creds
from experimental.ran.agent.analytics.conversation import Conversation
from experimental.ran.agent.replay_eval.eval_sample import <PERSON>l<PERSON><PERSON>
from experimental.ran.agent.replay_eval.launch_process_eval_functions import (
    add_line_numbers_to_text,
)
from experimental.ran.utils.ri_utils import (
    ChatRequest,
    get_chat_host_request_factory,
    get_chat_host_response_factory,
)
from services.chat_host import chat_pb2

logger = logging.getLogger(__name__)


def serialize_chat_request_nodes(
    chat_request: chat_pb2.ChatRequest | None,
) -> List[Dict[str, Any]]:
    """Serialize ChatRequestNode objects to dictionaries for JSON storage.

    Args:
        chat_request: ChatRequest containing nodes to serialize

    Returns:
        List of serialized request node dictionaries
    """
    if not chat_request or not hasattr(chat_request, "nodes"):
        return []

    serialized_nodes = []
    for node in chat_request.nodes:
        node_dict: Dict[str, Any] = {
            "id": node.id,
            "type": int(node.type),
        }

        # Serialize specific node types
        if node.HasField("text_node"):
            node_dict["text_node"] = {"content": node.text_node.content}
        elif node.HasField("tool_result_node"):
            node_dict["tool_result_node"] = {
                "tool_use_id": node.tool_result_node.tool_use_id,
                "content": node.tool_result_node.content,
                "is_error": node.tool_result_node.is_error,
                "request_id": node.tool_result_node.request_id
                if node.tool_result_node.HasField("request_id")
                else None,
            }
        elif node.HasField("image_node"):
            node_dict["image_node"] = {
                "image_data": node.image_node.image_data,
                "format": int(node.image_node.format),
            }
        elif node.HasField("ide_state_node"):
            node_dict["ide_state_node"] = {
                "workspace_folders": [
                    {
                        "repository_root": folder.repository_root,
                        "folder_root": folder.folder_root,
                    }
                    for folder in node.ide_state_node.workspace_folders
                ],
                "workspace_folders_unchanged": node.ide_state_node.workspace_folders_unchanged,
                "current_terminal": {
                    "terminal_id": node.ide_state_node.current_terminal.terminal_id,
                    "current_working_directory": node.ide_state_node.current_terminal.current_working_directory,
                }
                if node.ide_state_node.HasField("current_terminal")
                else None,
            }

        serialized_nodes.append(node_dict)

    return serialized_nodes


def serialize_tool_call(tool_call) -> Dict[str, Any] | None:
    """Serialize ToolCall object to dictionary for JSON storage.

    Args:
        tool_call: ToolCall object to serialize

    Returns:
        Serialized tool call dictionary or None
    """
    if not tool_call:
        return None

    serialized = {
        "tool_use_request_id": tool_call.tool_use_request_id,
        "tool_use": {
            "name": tool_call.tool_use.name,
            "input": tool_call.tool_use.input,
            "tool_use_id": tool_call.tool_use.tool_use_id,
        }
        if tool_call.tool_use
        else None,
        "tool_result_request_id": tool_call.tool_result_request_id,
        "tool_result": {
            "tool_use_id": tool_call.tool_result.tool_use_id,
            "content": tool_call.tool_result.content,
            "is_error": tool_call.tool_result.is_error,
            "request_id": tool_call.tool_result.request_id,
        }
        if tool_call.tool_result
        else None,
    }

    return serialized


def get_all_tenants_for_namespace(namespace: str) -> List[str]:
    """Get all tenant names for a given namespace.

    Args:
        namespace: The namespace string (e.g., "i0", "i1")

    Returns:
        List of tenant names that belong to the namespace

    Raises:
        ValueError: If the namespace is not recognized
    """
    from base.datasets.tenants import DATASET_TENANTS

    # If it's already a specific tenant name, return it as a single-item list
    if namespace in DATASET_TENANTS:
        return [namespace]

    # Map namespace to all its tenants
    namespace_tenant_mapping = {
        "i0": [
            name for name in DATASET_TENANTS.keys() if name.startswith("i0-vanguard")
        ],
        "i1": [
            name for name in DATASET_TENANTS.keys() if name.startswith("i1-vanguard")
        ],
        "dogfood": ["dogfood", "dogfood-shard"],
        "staging-shard-0": [
            "dogfood-shard"
        ],  # Only dogfood-shard is in DATASET_TENANTS for this namespace
        "aitutor": ["aitutor-pareto", "aitutor-turing", "aitutor-mercor"],
    }

    if namespace in namespace_tenant_mapping:
        tenants = namespace_tenant_mapping[namespace]
        logger.info(
            f"Found {len(tenants)} tenants for namespace '{namespace}': {tenants}"
        )
        return tenants

    # If we can't resolve it, treat it as a single tenant
    if namespace in [
        "dogfood",
        "dogfood-shard",
        "aitutor-pareto",
        "aitutor-turing",
        "aitutor-mercor",
    ] or namespace.startswith(("i0-vanguard", "i1-vanguard")):
        return [namespace]

    raise ValueError(
        f"Unknown namespace: {namespace}. Available namespaces: i0, i1, dogfood, staging-shard-0, aitutor, or specific tenant names"
    )


@dataclass
class UntruncatedToolUsage:
    """Container for untruncated tool usage data."""

    tool_name: str  # "view-range-untruncated" or "search-untruncated"
    tool_input: dict
    tool_output: str
    request_id: str
    turn_index: int  # Position within the round
    reference_id_valid: bool = True  # integrity flag (was validated during extraction)


@dataclass
class UntruncatedToolCallData:
    """Container for LaunchProcess tool call data with subsequent untruncated tool usage.

    Extended with session / integrity metadata so focus runner can consume
    directly without re-extraction.
    Now also captures the baseline assistant textual response that followed the
    (potential) untruncated tool usage in the original conversation so the
    evaluation runner can populate authentic baseline fields.
    """

    def __init__(
        self,
        request_id: str,
        launch_process_output: str,
        numbered_launch_process_output: str,
        context_history: str,
        launch_process_input: dict,
        untruncated_tool_usages: List[UntruncatedToolUsage],
        tenant_name: str = "i0-vanguard0",
        session_id: str | None = None,
        original_tool_definitions: list[dict] | None = None,
        launch_output_checksum: str | None = None,
        baseline_assistant_response: str | None = None,
        structured_conversation_history: List[Dict[str, Any]] | None = None,
    ):
        self.request_id = request_id
        self.launch_process_output = launch_process_output
        self.numbered_launch_process_output = numbered_launch_process_output
        self.context_history = context_history
        self.launch_process_input = launch_process_input
        self.untruncated_tool_usages = untruncated_tool_usages
        self.tenant_name = tenant_name
        self.session_id = session_id
        self.original_tool_definitions = original_tool_definitions or []
        self.launch_output_checksum = launch_output_checksum
        self.baseline_assistant_response = baseline_assistant_response or ""
        self.structured_conversation_history = structured_conversation_history or []


def is_untruncated_tool(tool_name: str) -> bool:
    """Check if a tool name is an untruncated content tool."""
    return tool_name in ["view-range-untruncated", "search-untruncated"]


TRUNCATION_FOOTER_REGEX = re.compile(
    # Matches the specific footer pattern with a captured reference ID
    r"\[This result was truncated\. Showing .*? Use view-range-untruncated or search-untruncated tools to access the full content\. Reference ID: ([a-zA-Z0-9\-_]+)\]"
)


def extract_reference_id_from_output(output: str) -> Optional[str]:
    """Extract reference ID from terminal output truncation footer.

    Args:
        output: Terminal output that may contain a truncation footer

    Returns:
        Reference ID if found, None otherwise
    """
    match = TRUNCATION_FOOTER_REGEX.search(output)
    if match:
        return match.group(1)
    return None


def extract_tool_definitions_from_chat_request(
    chat_req: ChatRequest | None,
) -> list[dict]:
    if chat_req is None:
        return []

    original_tool_defs = []
    for td in chat_req.tool_definitions:
        original_tool_defs.append(
            {
                "name": td.name,
                "description": getattr(td, "description", ""),
                "input_schema_json": getattr(td, "input_schema_json", ""),
            }
        )
    return original_tool_defs


def get_truncation_reference_ids_with_positions(
    conversation: Conversation,
) -> dict[str, int]:
    """Return mapping of reference_id -> (linear turn index) for truncated *-process outputs.

    Only counts outputs that actually contain the truncation footer. The turn index is
    used to enforce temporal ordering (untruncated tool must appear after truncated output).
    """
    ref_positions: dict[str, int] = {}
    linear_idx = 0
    for round in conversation.agent_rounds:
        for turn in round.agent_turns:
            if (
                turn.tool_call
                and turn.tool_call.tool_use
                and turn.tool_call.tool_use.name
                in [
                    "launch-process",
                    "LaunchProcess",
                    "launch_process",
                    "read-process",
                    "ReadProcess",
                    "read_process",
                ]
                and turn.tool_call.tool_result
                and turn.tool_call.tool_result.content
            ):
                content = turn.tool_call.tool_result.content
                ref_id = extract_reference_id_from_output(content)
                if ref_id and ref_id not in ref_positions:
                    ref_positions[ref_id] = linear_idx
            linear_idx += 1
    return ref_positions


def validate_untruncated_tool_reference_id_with_positions(
    tool_input: dict,
    valid_reference_positions: dict[str, int],
    current_linear_index: int,
) -> bool:
    """Validate that an untruncated tool's reference_id is valid and precedes this turn.

    Args:
        tool_input: Tool input containing reference_id
        valid_reference_positions: Mapping of reference_id -> turn index where truncation occurred
        current_linear_index: Linear index of the untruncated tool turn
    """
    ref_id = tool_input.get("reference_id")
    if not ref_id:
        return False
    pos = valid_reference_positions.get(ref_id)
    if pos is None:
        return False
    # Must come from an earlier turn
    return pos < current_linear_index


def validate_untruncated_tool_reference_id(
    tool_input: dict, valid_reference_ids: Set[str]
) -> bool:
    """Validate that an untruncated tool's reference_id matches a valid terminal output.

    Args:
        tool_input: The tool input containing reference_id parameter
        valid_reference_ids: Set of valid reference IDs from terminal outputs

    Returns:
        True if reference_id is valid, False otherwise
    """
    reference_id = tool_input.get("reference_id")
    if not reference_id:
        return False
    return reference_id in valid_reference_ids


def extract_untruncated_tool_calls_from_conversation(
    conversation: Conversation,
    max_turns_after_launch: int = 5,
    tenant_name: str = "i0-vanguard0",
    session_id: str = "",
) -> UntruncatedToolCallData | None:
    """Extract LaunchProcess tool call data with subsequent untruncated tool usage.

    Args:
        conversation: Conversation to extract from
        max_turns_after_launch: Maximum number of turns to look ahead for untruncated tools

    Returns:
        List of UntruncatedToolCallData objects
    """

    result = None

    # Build mapping of valid truncation reference IDs to their linear positions
    valid_reference_positions = get_truncation_reference_ids_with_positions(
        conversation
    )

    # REVERSE SCAN OPTIMIZATION:
    # Instead of scanning forward from every *-process output (most of which have no
    # untruncated follow-ups), we scan FROM THE END looking for the (rarer) untruncated
    # tool calls and then jump backwards to their originating truncated *-process output.
    found_sample_in_conversation = False

    # Flatten all turns with linear indices for efficient backward traversal
    flattened_turns: list[dict[str, Any]] = []
    _linear_idx = 0
    for _round_idx, _round in enumerate(conversation.agent_rounds):
        for _turn_idx, _turn in enumerate(_round.agent_turns):
            flattened_turns.append(
                {
                    "linear_index": _linear_idx,
                    "round_idx": _round_idx,
                    "turn_idx": _turn_idx,
                    "turn": _turn,
                }
            )
            _linear_idx += 1

    process_tool_names = {
        "launch-process",
        "LaunchProcess",
        "launch_process",
        "read-process",
        "ReadProcess",
        "read_process",
    }

    # Backward scan: prioritize untruncated tool calls first
    for idx in range(len(flattened_turns) - 1, -1, -1):
        if found_sample_in_conversation:
            break
        entry = flattened_turns[idx]
        turn = entry["turn"]

        if not (
            turn.tool_call
            and turn.tool_call.tool_use
            and is_untruncated_tool(turn.tool_call.tool_use.name)
        ):
            continue

        ref_id = (
            turn.tool_call.tool_use.input.get("reference_id")
            if turn.tool_call
            else None
        )
        if not ref_id:
            logger.warning(
                f"Untruncated tool call from {turn.request_id} has no reference_id"
            )
            continue

        # Find originating truncated *-process output (pre-computed mapping)
        origin_launch_linear_index = valid_reference_positions.get(ref_id)
        if origin_launch_linear_index is None:
            # Reference ID not tied to any truncated *-process output in this conversation
            continue
        if origin_launch_linear_index >= entry["linear_index"]:
            # Must precede the untruncated tool usage
            continue

        distance = entry["linear_index"] - origin_launch_linear_index
        if distance > max_turns_after_launch:
            # Outside the allowable window; earlier untruncated usages may still qualify
            continue

        origin_entry = flattened_turns[origin_launch_linear_index]
        origin_turn = origin_entry["turn"]
        if not (
            origin_turn.tool_call
            and origin_turn.tool_call.tool_use
            and origin_turn.tool_call.tool_use.name in process_tool_names
            and origin_turn.tool_call.tool_result
            and origin_turn.tool_call.tool_result.content
        ):
            # Should not happen; mapping only records valid truncated outputs
            continue

        launch_process_output = origin_turn.tool_call.tool_result.content
        origin_ref_id = extract_reference_id_from_output(launch_process_output)
        if origin_ref_id != ref_id:
            # Integrity mismatch (unexpected)
            logger.warning(
                f"Reference ID mismatch for request_id={origin_turn.request_id} (launch={origin_ref_id} vs usage={ref_id})"
            )
            continue

        # Collect all untruncated usages WITHIN the forward window after the origin launch
        untruncated_usages: list[UntruncatedToolUsage] = []
        last_usage_linear_index = origin_launch_linear_index
        window_end = min(
            origin_launch_linear_index + max_turns_after_launch,
            len(flattened_turns) - 1,
        )
        for j in range(origin_launch_linear_index + 1, window_end + 1):
            cand_entry = flattened_turns[j]
            cand_turn = cand_entry["turn"]
            if (
                cand_turn.tool_call
                and cand_turn.tool_call.tool_use
                and is_untruncated_tool(cand_turn.tool_call.tool_use.name)
            ):
                cand_ref = cand_turn.tool_call.tool_use.input.get("reference_id")
                if cand_ref == ref_id:
                    relative_offset = (
                        j - origin_launch_linear_index
                    )  # 1..max_turns_after_launch
                    untruncated_usages.append(
                        UntruncatedToolUsage(
                            tool_name=cand_turn.tool_call.tool_use.name,
                            tool_input=cand_turn.tool_call.tool_use.input,
                            tool_output=(
                                cand_turn.tool_call.tool_result.content
                                if cand_turn.tool_call.tool_result
                                else ""
                            ),
                            request_id=cand_turn.request_id,
                            turn_index=relative_offset,
                            reference_id_valid=True,
                        )
                    )
                    last_usage_linear_index = j

        if not untruncated_usages:
            # The triggering untruncated tool was outside the window (or filtered out)
            continue

        # Build numbered output & metadata just like original forward scan path
        import hashlib

        numbered_output = add_line_numbers_to_text(launch_process_output)

        origin_round_idx = origin_entry["round_idx"]
        origin_turn_idx = origin_entry["turn_idx"]

        # Build structured conversation history (preserving complete request/response nodes)
        structured_conversation_history = []
        for prev_round in conversation.agent_rounds[:origin_round_idx]:
            # Add user message with request nodes
            user_exchange = {
                "request_id": prev_round.user_message_request_id,
                "request_message": prev_round.user_message,
                "request_nodes": serialize_chat_request_nodes(
                    prev_round.user_message_request
                )
                if prev_round.user_message_request
                else [],
                "response_nodes": [],
                "type": "user_message",
            }
            structured_conversation_history.append(user_exchange)

            # Add each assistant turn with complete tool call data
            for prev_turn in prev_round.agent_turns:
                assistant_exchange = {
                    "request_id": prev_turn.request_id,
                    "request_message": "",
                    "request_nodes": serialize_chat_request_nodes(prev_turn.request)
                    if prev_turn.request
                    else [],
                    "response_text": prev_turn.message,
                    "tool_call": serialize_tool_call(prev_turn.tool_call)
                    if prev_turn.tool_call
                    else None,
                    "type": "assistant_turn",
                }
                structured_conversation_history.append(assistant_exchange)

        # Add current round user message
        current_round = conversation.agent_rounds[origin_round_idx]
        current_user_exchange = {
            "request_id": current_round.user_message_request_id,
            "request_message": current_round.user_message,
            "request_nodes": serialize_chat_request_nodes(
                current_round.user_message_request
            )
            if current_round.user_message_request
            else [],
            "response_nodes": [],
            "type": "user_message",
        }
        structured_conversation_history.append(current_user_exchange)

        # Add assistant turns up to and including the launch turn
        for prev_turn in current_round.agent_turns[: origin_turn_idx + 1]:
            assistant_exchange = {
                "request_id": prev_turn.request_id,
                "request_message": "",
                "request_nodes": serialize_chat_request_nodes(prev_turn.request)
                if prev_turn.request
                else [],
                "response_text": prev_turn.message,
                "tool_call": serialize_tool_call(prev_turn.tool_call)
                if prev_turn.tool_call
                else None,
                "type": "assistant_turn",
            }
            structured_conversation_history.append(assistant_exchange)

        # Build legacy context history for backward compatibility
        context_parts = []
        for prev_round in conversation.agent_rounds[:origin_round_idx]:
            context_parts.append(f"User: {prev_round.user_message}")
            for prev_turn in prev_round.agent_turns:
                context_parts.append(f"Assistant: {prev_turn.message}")
        current_round = conversation.agent_rounds[origin_round_idx]
        context_parts.append(f"User: {current_round.user_message}")
        for prev_turn in current_round.agent_turns[: origin_turn_idx + 1]:
            context_parts.append(f"Assistant: {prev_turn.message}")
        context_history = "\n\n".join(context_parts)

        launch_output_checksum = hashlib.sha256(
            launch_process_output.encode("utf-8")
        ).hexdigest()

        # Baseline assistant response (first assistant text after last untruncated usage)
        baseline_assistant_response = ""
        for k in range(last_usage_linear_index + 1, len(flattened_turns)):
            follow_turn = flattened_turns[k]["turn"]
            if follow_turn.message and not (
                follow_turn.tool_call and follow_turn.tool_call.tool_use
            ):
                baseline_assistant_response = follow_turn.message
                break

        result = UntruncatedToolCallData(
            request_id=origin_turn.request_id,
            launch_process_output=launch_process_output,
            numbered_launch_process_output=numbered_output,
            context_history=context_history,
            launch_process_input=origin_turn.tool_call.tool_use.input,
            untruncated_tool_usages=untruncated_usages,
            tenant_name=tenant_name,
            session_id=session_id,
            launch_output_checksum=launch_output_checksum,
            baseline_assistant_response=baseline_assistant_response,
            structured_conversation_history=structured_conversation_history,
        )
        logger.info(
            "(Reverse scan) Extracted LaunchProcess tool call from %s with %d untruncated tool usages (ref_id=%s, window=%d turns). Valid refs observed: %s.",
            origin_turn.request_id,
            len(untruncated_usages),
            ref_id,
            max_turns_after_launch,
            list(valid_reference_positions.keys()),
        )
        break
    return result


def extract_untruncated_tool_samples_from_request_ids(
    session_id: str,
    request_ids: List[str],
    tenant_name: str = "i0-vanguard0",
    max_turns_after_launch: int = 5,
) -> List[UntruncatedToolCallData]:
    """Extract untruncated tool call data from specific request IDs.

    Args:
        request_ids: List of request IDs to process
        tenant_name: Tenant name for data access
        max_turns_after_launch: Maximum number of turns to look ahead for untruncated tools

    Returns:
        List of UntruncatedToolCallData objects
    """
    tool_call_data = []

    _get_chat_host_request = get_chat_host_request_factory(tenant_name)
    _get_chat_host_response = get_chat_host_response_factory(tenant_name)

    for request_id in request_ids:
        try:
            logger.info(f"Processing request ID: {request_id}")

            # Get the chat host request and response
            chat_request = _get_chat_host_request(request_id)
            chat_response = _get_chat_host_response(request_id)

            if not chat_request or not chat_response:
                logger.warning(
                    f"Could not find chat request or response for {request_id}"
                )
                continue

            # Convert to conversation
            conversation = Conversation.from_chat_request(
                request_id=request_id,
                chat_request=chat_request,
                chat_response=chat_response,
                get_chat_host_request_func=_get_chat_host_request,
            )

            # Extract untruncated tool call data
            conversation_tool_call = extract_untruncated_tool_calls_from_conversation(
                conversation, max_turns_after_launch, tenant_name, session_id
            )

            if conversation_tool_call is not None:
                # Add tool definitions from chat request
                original_tool_definition = extract_tool_definitions_from_chat_request(
                    chat_request
                )
                conversation_tool_call.original_tool_definitions = (
                    original_tool_definition
                )

                tool_call_data.append(conversation_tool_call)

        except Exception as e:
            logger.error(
                f"Error processing request ID {request_id}: {e}", exc_info=True
            )
            continue

    if len(tool_call_data) > 0:
        logger.info(
            f"Extracted {len(tool_call_data)} untruncated tool call samples from {len(request_ids)} request IDs"
        )
    return tool_call_data


def create_eval_sample_from_untruncated_tool_data(
    tool_call_data: UntruncatedToolCallData,
    sample_name_suffix: str = "",
) -> EvalSample:
    """Create an EvalSample from untruncated tool call data.

    Args:
        tool_call_data: The tool call data to create sample from
        sample_name_suffix: Optional suffix for sample name

    Returns:
        EvalSample configured for untruncated tools evaluation
    """

    def eval_func(response, tool_definitions, request_id):
        # Check if any untruncated tools were called
        untruncated_tool_calls = []

        for node in response:
            # ChatResultNodeType.TOOL_USE = 5
            if node.type == 5 and hasattr(node, "tool_use") and node.tool_use:
                tool_name = node.tool_use.name
                if is_untruncated_tool(tool_name):
                    untruncated_tool_calls.append(node.tool_use)

        # Success if no untruncated tools were called (showing restraint)
        if not untruncated_tool_calls:
            return True, "Successfully avoided unnecessary untruncated tool usage"
        else:
            tool_names = [tool.name for tool in untruncated_tool_calls]
            return False, f"Called untruncated tools: {', '.join(tool_names)}"

    def stats_func(response, tool_definitions, request_id):
        stats = {
            "untruncated_tools_called": 0,
            "view_range_calls": 0,
            "search_calls": 0,
            "total_lines_requested": 0,
            "average_lines_per_request": 0,
        }

        untruncated_tool_calls = []
        total_lines = 0

        for node in response:
            # ChatResultNodeType.TOOL_USE = 5
            if node.type == 5 and hasattr(node, "tool_use") and node.tool_use:
                tool_name = node.tool_use.name
                if is_untruncated_tool(tool_name):
                    untruncated_tool_calls.append(node.tool_use)
                    stats["untruncated_tools_called"] += 1

                    if tool_name == "view-range-untruncated":
                        stats["view_range_calls"] += 1
                        # Calculate lines requested
                        tool_input = node.tool_use.input
                        start_line = tool_input.get("start_line", 0)
                        end_line = tool_input.get("end_line", 0)
                        if start_line and end_line:
                            lines_requested = end_line - start_line + 1
                            total_lines += lines_requested
                    elif tool_name == "search-untruncated":
                        stats["search_calls"] += 1

        stats["total_lines_requested"] = total_lines
        if stats["view_range_calls"] > 0:
            stats["average_lines_per_request"] = total_lines / stats["view_range_calls"]

        return stats

    sample_name = f"untruncated_tools_{tool_call_data.request_id}"
    if sample_name_suffix:
        sample_name += f"_{sample_name_suffix}"

    return EvalSample(
        request_id=tool_call_data.request_id,
        name=sample_name,
        eval_response_func=eval_func,
        gen_stats_func=stats_func,
        tenant_name=tool_call_data.tenant_name,
        category="untruncated_tools",
    )


def main():
    """Main function to run the untruncated tools sample extractor from command line."""
    import argparse
    import json

    parser = argparse.ArgumentParser(
        description="Extract samples where untruncated tools were used after launch-process commands. "
        "Uses highly optimized timestamp-based targeting: finds content-truncation events from agent_session_event table, "
        "then searches for tool usage in narrow time windows (10min before + 1min after) around each truncation event. "
        "This dramatically reduces search space from entire sessions to small 11-minute windows. "
        "Supports multi-tenant namespaces (e.g., 'i0' processes all i0-vanguard* tenants)."
    )
    parser.add_argument(
        "--last-days",
        type=int,
        default=14,
        help="Number of days to look back (default: 14)",
    )

    parser.add_argument(
        "--max-turns-after-launch",
        type=int,
        default=5,
        help="Maximum number of turns to look ahead for untruncated tools (default: 5)",
    )
    parser.add_argument(
        "--namespace",
        type=str,
        default="i0",
        help="Namespace to query. Can be a namespace (e.g., 'i0' for all i0-vanguard* tenants) "
        "or a specific tenant name (e.g., 'i0-vanguard0'). Default: 'i0'",
    )
    parser.add_argument(
        "--limit",
        type=int,
        help="Maximum number of samples to collect. Uses dynamic early stopping to halt processing as soon as this many valid samples are found.",
    )
    parser.add_argument(
        "--samples-file",
        type=str,
        default="untruncated_tools_samples.json",
        help="Base filename or path prefix for samples (timestamp will be appended: <base>_YYYYMMDD_HHMMSS.json)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging",
    )

    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    logging.basicConfig(
        level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Calculate date range
    to_date = datetime.now()
    from_date = to_date - timedelta(days=args.last_days)

    logger.info(f"Extracting untruncated tools samples from {from_date} to {to_date}")
    logger.info(f"Namespace: {args.namespace}")
    logger.info(f"Max turns after launch: {args.max_turns_after_launch}")
    logger.info(
        "Using highly optimized timestamp-based targeting: searching 10min+1min windows around truncation events"
    )
    if args.limit:
        logger.info(
            f"Sample limit: {args.limit} samples (dynamic early stopping per truncation event)"
        )

    # Get all tenants for the namespace
    tenant_names = get_all_tenants_for_namespace(args.namespace)
    logger.info(f"Processing {len(tenant_names)} tenants: {tenant_names}")

    # Helper functions for new sampling approach (enhanced ordering & logging)
    from google.cloud import bigquery as _bq

    def _get_bq_client(project_id: str):
        gcp_creds, _ = get_gcp_creds(None)
        return _bq.Client(project=project_id, credentials=gcp_creds)

    def _get_sessions_with_content_truncation(tenant_obj, start_dt, end_dt):
        """Return list of (session_id, timestamp) tuples for content-truncation events within the time window.
        This provides both session filtering and specific timestamps for targeted searching."""
        client = _get_bq_client(tenant_obj.project_id)
        query = f"""
            SELECT session_id, time
            FROM `{tenant_obj.project_id}.{tenant_obj.analytics_dataset_name}.agent_session_event`
            WHERE time BETWEEN @start AND @end
              AND session_id IS NOT NULL AND session_id != ''
              AND JSON_VALUE(sanitized_json, '$.event_name') = 'content-truncation'
              AND tenant = @tenant
            ORDER BY rand()
        """
        job_config = _bq.QueryJobConfig(
            query_parameters=[
                _bq.ScalarQueryParameter("start", "TIMESTAMP", start_dt),
                _bq.ScalarQueryParameter("end", "TIMESTAMP", end_dt),
                _bq.ScalarQueryParameter("tenant", "STRING", tenant_obj.name),
            ]
        )
        truncation_events = []
        try:
            for row in client.query(query, job_config=job_config).result():
                truncation_events.append((row.session_id, row.time))
            logger.info(
                f"Found {len(truncation_events)} content-truncation events for {tenant_obj.name}"
            )
        except Exception as e:
            logger.warning(
                f"Failed to fetch content-truncation events for {tenant_obj.name}: {e}"
            )
        return truncation_events

    def _get_requests_around_truncation_timestamp(
        tenant_obj,
        session_id,
        truncation_timestamp,
        window_seconds_before=0,
        window_seconds_after=60,
        limit=10,
    ):
        """Return request_ids in a targeted time window around a content-truncation event.

        Args:
            tenant_obj: Tenant object for BigQuery access
            session_id: Session ID to search within
            truncation_timestamp: Timestamp of the content-truncation event
            window_seconds_before: Seconds before truncation to search (for launch-process events)
            window_seconds_after: Seconds after truncation to search (for untruncated tool events)

        Returns:
            List of request_ids in the time window ordered by absolute temporal distance from truncation event (closest first)
        """
        client = _get_bq_client(tenant_obj.project_id)

        # Calculate time window
        start_time = truncation_timestamp - timedelta(seconds=window_seconds_before)
        end_time = truncation_timestamp + timedelta(seconds=window_seconds_after)

        # Order requests by absolute temporal distance from truncation event to prioritize causal (closest) request first
        query = f"""
            SELECT request_id, time
            FROM `{tenant_obj.project_id}.{tenant_obj.analytics_dataset_name}.request_metadata`
            WHERE session_id = @session_id
              AND time BETWEEN @start_time AND @end_time
              AND request_type IN ('AGENT_CHAT')
            ORDER BY ABS(TIMESTAMP_DIFF(time, @truncation_timestamp, SECOND)) ASC
            LIMIT @limit
        """
        job_config = _bq.QueryJobConfig(
            query_parameters=[
                _bq.ScalarQueryParameter("session_id", "STRING", session_id),
                _bq.ScalarQueryParameter("start_time", "TIMESTAMP", start_time),
                _bq.ScalarQueryParameter("end_time", "TIMESTAMP", end_time),
                _bq.ScalarQueryParameter(
                    "truncation_timestamp", "TIMESTAMP", truncation_timestamp
                ),
                _bq.ScalarQueryParameter("limit", "INT64", limit),
            ]
        )
        request_ids = []
        try:
            for row in client.query(query, job_config=job_config).result():
                request_ids.append(row.request_id)
            logger.debug(
                f"Found {len(request_ids)} requests in {window_seconds_before}+{window_seconds_after}sec window around truncation for session {session_id}"
            )
        except Exception as e:
            logger.debug(
                f"Failed to get requests around truncation timestamp for session {session_id}: {e}"
            )
        return request_ids

    def _get_truncation_events_with_metadata(
        tenant_obj, start_dt, end_dt, max_events_per_tenant=1000
    ):
        """Return list of (session_id, truncation_timestamp, date) tuples for content-truncation events.
        This replaces the old session-day approach with direct truncation event targeting."""

        # Get content-truncation events with timestamps
        truncation_events = _get_sessions_with_content_truncation(
            tenant_obj, start_dt, end_dt
        )

        if not truncation_events:
            logger.info(f"No content-truncation events found for {tenant_obj.name}")
            return []

        # Convert to list with date information for compatibility
        events_with_metadata = []
        for session_id, timestamp in truncation_events:
            date = timestamp.date()
            events_with_metadata.append((session_id, timestamp, date, tenant_obj.name))

        # Limit and shuffle for diversity
        if len(events_with_metadata) > max_events_per_tenant:
            import random

            random.shuffle(events_with_metadata)
            events_with_metadata = events_with_metadata[:max_events_per_tenant]

        logger.info(
            f"Found {len(events_with_metadata)} content-truncation events for {tenant_obj.name}"
        )
        return events_with_metadata

    # New optimized collection logic using timestamp-based targeting
    from base.datasets.tenants import get_tenant as _get_tenant

    all_tool_call_data = []
    processed_truncation_events = 0

    truncation_events = []
    # Iterate tenants & their truncation events
    for tenant_name in tenant_names:
        tenant_obj = _get_tenant(tenant_name)
        tenant_truncation_events = _get_truncation_events_with_metadata(
            tenant_obj, from_date, to_date
        )
        logger.info(
            f"Tenant {tenant_name}: fetched {len(tenant_truncation_events)} content-truncation events"
        )
        truncation_events += tenant_truncation_events

    random.shuffle(truncation_events)  # Randomize order for diversity
    collected_sessions_date_pairs = set()
    for (
        session_id,
        truncation_timestamp,
        truncation_date,
        tenant_name,
    ) in truncation_events:
        session_date_key = session_id + "_" + truncation_date.strftime("%Y%m%d")

        if args.limit and len(all_tool_call_data) >= args.limit:
            break

        # Skip if we've already processed this session in the same date to avoid duplicates
        if session_date_key in collected_sessions_date_pairs:
            continue

        logger.debug(
            f"Processing truncation event: tenant={tenant_name} session={session_id} timestamp={truncation_timestamp}"
        )

        # Get request IDs in targeted time window around truncation event
        tenant_obj = _get_tenant(tenant_name)
        candidate_request_ids = _get_requests_around_truncation_timestamp(
            tenant_obj, session_id, truncation_timestamp
        )

        logger.info(
            f"Truncation event tenant={tenant_name} session={session_id} returned {len(candidate_request_ids)} request_ids in time window around {truncation_timestamp}"
        )

        if not candidate_request_ids:
            processed_truncation_events += 1
            continue

        for rid in candidate_request_ids:
            # Extract tool calls for this request id
            data = extract_untruncated_tool_samples_from_request_ids(
                session_id,
                [rid],
                tenant_name=tenant_name,
                max_turns_after_launch=args.max_turns_after_launch,
            )
            if data:
                # Take first sample for this request
                all_tool_call_data.append(data[0])
                logger.info(
                    f"Collected sample from tenant={tenant_name} session={session_id} request={rid} (truncation-targeted)"
                )
                collected_sessions_date_pairs.add(session_date_key)
                break

        processed_truncation_events += 1

        if args.limit and len(all_tool_call_data) >= args.limit:
            logger.info("Early stopping after reaching sample limit")
            break

    tool_call_data = all_tool_call_data
    logger.info(
        f"Extracted {len(tool_call_data)} untruncated tool call samples from {processed_truncation_events} truncation events"
    )

    # Extract original tool definitions for each request
    # Save detailed results to file (schema_version 2)
    detailed_output_data = []
    schema_version = 2
    created_ts = datetime.now().isoformat()

    for data in tool_call_data:
        # Collect untruncated usages
        untruncated_usages_data = []
        distinct_reference_ids = set()
        for usage in data.untruncated_tool_usages:
            ref_id = usage.tool_input.get("reference_id")
            if ref_id:
                distinct_reference_ids.add(ref_id)
            untruncated_usages_data.append(
                {
                    "tool_name": usage.tool_name,
                    "tool_input": usage.tool_input,
                    "tool_output": usage.tool_output,
                    "request_id": usage.request_id,
                    "turn_index": usage.turn_index,
                    "reference_id_valid": usage.reference_id_valid,
                }
            )

        # Integrity flags
        all_reference_ids_valid = all(
            u["reference_id_valid"] for u in untruncated_usages_data
        )

        detailed_output_data.append(
            {
                "schema_version": schema_version,
                "created_timestamp": created_ts,
                "request_id": data.request_id,
                "session_id": data.session_id,
                "tenant_name": data.tenant_name,
                "launch_process_output": data.launch_process_output,
                "numbered_launch_process_output": data.numbered_launch_process_output,
                "launch_output_checksum": data.launch_output_checksum,
                "context_history": data.context_history,
                "structured_conversation_history": data.structured_conversation_history,
                "launch_process_input": data.launch_process_input,
                "original_tool_definitions": data.original_tool_definitions,
                "untruncated_tool_usages": untruncated_usages_data,
                "distinct_reference_ids": sorted(distinct_reference_ids),
                "all_reference_ids_valid": all_reference_ids_valid,
                "integrity_passed": all_reference_ids_valid,  # currently same condition
                "output_line_count": data.launch_process_output.count("\n") + 1
                if data.launch_process_output.strip()
                else 0,
                "num_untruncated_usages": len(data.untruncated_tool_usages),
                "response_text": data.baseline_assistant_response,
            }
        )

    # Append timestamp to output filename
    base_path = args.samples_file
    if base_path.endswith(".json"):
        base_base = base_path[:-5]
    else:
        base_base = base_path.rstrip("/")
    timestamped_filename = (
        f"{base_base}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    )

    with open(timestamped_filename, "w") as f:
        json.dump(detailed_output_data, f, indent=2)

    logger.info(
        f"Saved {len(detailed_output_data)} samples to {timestamped_filename} (base: {args.samples_file})"
    )

    # Basic request IDs file generation removed (redundant). All consumers should use the detailed file.

    # Print summary statistics
    if detailed_output_data:
        line_counts = [sample["output_line_count"] for sample in detailed_output_data]
        usage_counts = [
            sample["num_untruncated_usages"] for sample in detailed_output_data
        ]

        logger.info("Launch-process output line count statistics:")
        logger.info(f"  Min: {min(line_counts)}")
        logger.info(f"  Max: {max(line_counts)}")
        logger.info(f"  Average: {sum(line_counts) / len(line_counts):.1f}")

        logger.info("Untruncated tool usage statistics:")
        logger.info(f"  Min usages per sample: {min(usage_counts)}")
        logger.info(f"  Max usages per sample: {max(usage_counts)}")
        logger.info(
            f"  Average usages per sample: {sum(usage_counts) / len(usage_counts):.1f}"
        )

        # Print tenant distribution statistics
        tenant_counts = {}
        for sample in detailed_output_data:
            tenant_name = sample["tenant_name"]
            tenant_counts[tenant_name] = tenant_counts.get(tenant_name, 0) + 1

        logger.info("Tenant distribution:")
        for tenant_name, count in sorted(tenant_counts.items()):
            percentage = (count / len(detailed_output_data)) * 100
            logger.info(f"  {tenant_name}: {count} samples ({percentage:.1f}%)")

        # Print tool usage breakdown
        tool_usage_counts = {}
        for sample in detailed_output_data:
            for usage in sample["untruncated_tool_usages"]:
                tool_name = usage["tool_name"]
                tool_usage_counts[tool_name] = tool_usage_counts.get(tool_name, 0) + 1

        logger.info("Tool usage breakdown:")
        for tool_name, count in sorted(tool_usage_counts.items()):
            logger.info(f"  {tool_name}: {count} usages")


if __name__ == "__main__":
    main()
