# Untruncated Tools Evaluation System

This directory contains a complete evaluation system for measuring and optimizing the usage of untruncated content tools (`view-range-untruncated` and `search-untruncated`) after `launch-process` commands.

## Overview

The system consists of two main components that follow the established pattern of existing experimental evaluators:

1. **Sample Extractor** (`untruncated_tools_sample_extractor.py`) - Identifies cases where untruncated tools were used after launch-process commands
2. **Focus Runner** (`untruncated_tools_focus_runner.py`) - Evaluates model performance with modified prompts that encourage selective tool usage

## Components

### 1. Sample Extractor (`untruncated_tools_sample_extractor.py`)

**Purpose**: Extract conversation samples where `view-range-untruncated` OR `search-untruncated` tools were called immediately after `launch-process` commands.

**Key Features**:
- Multi-tenant support with namespace resolution (e.g., "i0" processes all i0-vanguard* tenants)
- Configurable look-ahead window for detecting untruncated tool usage
- Comprehensive statistics and tenant distribution reporting
- Command-line interface with date ranges, limits, and output options

**Usage**:
```bash
python experimental/ran/agent/replay_eval/untruncated_tools_sample_extractor.py \
  --namespace i0 \
  --last-days 7 \
  --limit 100 \
  --output-file untruncated_samples.json
```

**Key Functions**:
- `extract_untruncated_tool_calls_from_conversations()` - Main extraction logic
- `is_untruncated_tool()` - Tool detection utility
- `get_all_tenants_for_namespace()` - Multi-tenant support

### 2. Focus Runner (`untruncated_tools_focus_runner.py`)

**Purpose**: Evaluate model performance with modified prompts that encourage selective usage of untruncated tools.

**Key Features**:
- Modified prompts that encourage restraint in untruncated tool usage
- Comprehensive metrics tracking for three key measurements
- Caching support using @pickle_cache decorator for performance
- Detailed evaluation results with both summary and individual sample analysis

**Usage**:
```bash
python experimental/ran/agent/replay_eval/untruncated_tools_focus_runner.py \
  --samples-file untruncated_samples.json \
  --model-config path/to/model_config.json \
  --namespace i0 \
  --max-samples 50 \
  --output-file evaluation_results.json
```

**Key Functions**:
- `evaluate_model_on_untruncated_tools()` - Main evaluation logic
- `create_selective_untruncated_tools_message()` - Modified prompt generation
- `extract_minimal_context()` - Context optimization

## Metrics Measured

The system tracks three key metrics to evaluate the effectiveness of selective prompting:

### 1. Reduction in Tool Usage
- **Measurement**: Percentage of samples where the model makes no untruncated tool calls
- **Goal**: Increase the percentage of cases where the model can provide helpful responses using only the truncated output
- **Baseline**: Current behavior without selective prompting

### 2. Line Efficiency
- **Measurement**: Average number of lines requested per `view-range-untruncated` call
- **Goal**: When untruncated tools are used, encourage more targeted line range requests
- **Calculation**: Total lines requested / Number of view-range calls

### 3. Tool Selection Optimization
- **Measurement**: Frequency of `search-untruncated` vs `view-range-untruncated` usage
- **Goal**: Encourage use of search when looking for specific patterns rather than viewing large ranges
- **Calculation**: Ratio of search calls to total untruncated tool calls

## Data Structures

### Core Classes

- **`UntruncatedToolUsage`**: Container for individual untruncated tool usage data
- **`UntruncatedToolCallData`**: Container for launch-process output with subsequent untruncated tool usage
- **`UntruncatedToolsEvaluationResult`**: Detailed evaluation result for single samples
- **`UntruncatedToolsEvaluationSummary`**: Aggregated results across multiple samples

## Modified Prompting Strategy

The focus runner uses a modified prompt that:

1. **Emphasizes Sufficiency**: Highlights that truncated output often contains sufficient information
2. **Sets Clear Criteria**: Defines specific scenarios where untruncated tools are justified
3. **Encourages Precision**: When tools are needed, promotes targeted usage over broad exploration
4. **Maintains Context**: Includes minimal conversation context for better decision-making

## Testing

Run the test suite to verify components are working correctly:

```bash
python experimental/ran/agent/replay_eval/test_untruncated_tools_eval.py
```

## Integration

The system integrates with the existing experimental evaluation framework:
- Uses established patterns from `launch_process_sample_extractor` and `launch_process_focus_runner`
- Compatible with existing model configuration and caching systems
- Follows the same command-line interface conventions
- Outputs results in compatible JSON format for further analysis

## Next Steps

1. **Initial Data Collection**: Run sample extractor to gather baseline data
2. **Baseline Evaluation**: Establish current metrics without modified prompts
3. **Selective Evaluation**: Run focus runner with modified prompts
4. **Analysis**: Compare metrics to measure improvement
5. **Iteration**: Refine prompts based on results and repeat evaluation
