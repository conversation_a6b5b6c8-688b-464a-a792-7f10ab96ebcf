#!/usr/bin/env python3

"""
Test script to verify the new limit behavior in untruncated_tools_sample_extractor.py.
This tests that the limit is applied to final output rather than processing.
"""

import sys
import tempfile
import json
from unittest.mock import patch, MagicMock
from dataclasses import dataclass
from typing import List

# Add the project root to Python path
sys.path.insert(0, "/home/<USER>/augment")

from experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor import (
    UntruncatedToolCallData,
    UntruncatedToolUsage,
)


def create_mock_tool_call_data(request_id: str) -> UntruncatedToolCallData:
    """Create a mock UntruncatedToolCallData for testing."""
    return UntruncatedToolCallData(
        request_id=request_id,
        launch_process_output=f"output for {request_id}",
        numbered_launch_process_output=f"1: output for {request_id}",
        context_history=f"context for {request_id}",
        launch_process_input={"command": "test"},
        untruncated_tool_usages=[
            UntruncatedToolUsage(
                tool_name="view-range-untruncated",
                tool_input={"start_line": 1, "end_line": 10},
                tool_output="test output",
                request_id=request_id,
                turn_index=1,
            )
        ],
        tenant_name="test-tenant",
    )


def test_limit_applied_to_final_output():
    """Test that limit is applied to final output, not processing."""
    print("Testing limit behavior...")

    # Create mock data - simulate finding 10 samples but limiting to 3
    mock_tool_call_data = [
        create_mock_tool_call_data(f"request_{i}") for i in range(10)
    ]

    # Mock the extraction function to return our test data
    with patch(
        "experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor.extract_untruncated_tool_samples_from_request_ids"
    ) as mock_extract:
        mock_extract.return_value = mock_tool_call_data

        # Mock other dependencies
        with patch(
            "experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor.get_all_tenants_for_namespace"
        ) as mock_get_tenants:
            mock_get_tenants.return_value = ["test-tenant"]

            with patch(
                "experimental.ran.agent.analytics.big_query_utils.get_agent_conv_last_request_ids"
            ) as mock_get_request_ids:
                # Return 10 request IDs to simulate processing all requests
                mock_get_request_ids.return_value = [f"request_{i}" for i in range(10)]

                with patch("base.datasets.tenants.get_tenant") as mock_get_tenant:
                    mock_get_tenant.return_value = MagicMock()

                    # Create temporary files for output
                    with tempfile.NamedTemporaryFile(
                        mode="w", suffix=".json", delete=False
                    ) as output_file:
                        with tempfile.NamedTemporaryFile(
                            mode="w", suffix=".json", delete=False
                        ) as detailed_file:
                            # Test with limit=3
                            test_args = [
                                "test_script.py",
                                "--namespace",
                                "test-tenant",
                                "--last-days",
                                "1",
                                "--limit",
                                "3",
                                "--output-file",
                                output_file.name,
                                "--detailed-output-file",
                                detailed_file.name,
                            ]

                            with patch("sys.argv", test_args):
                                # Import and run main function
                                from experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor import (
                                    main,
                                )

                                try:
                                    main()
                                except SystemExit:
                                    pass  # argparse calls sys.exit, which is expected

                                # Verify that extract function was called with all 10 request IDs
                                # (not limited to 3 during processing)
                                assert mock_extract.called
                                call_args = mock_extract.call_args
                                request_ids_processed = call_args[1]["request_ids"]

                                # Should process all 10 request IDs, not just 3
                                assert (
                                    len(request_ids_processed) == 10
                                ), f"Expected 10 request IDs to be processed, got {len(request_ids_processed)}"

                                # Read the output file to verify final output is limited to 3
                                with open(output_file.name, "r") as f:
                                    output_data = json.load(f)

                                request_ids_in_output = output_data["request_ids"]
                                assert (
                                    len(request_ids_in_output) == 3
                                ), f"Expected 3 request IDs in final output, got {len(request_ids_in_output)}"

                                # Read detailed output to verify it also has 3 samples
                                with open(detailed_file.name, "r") as f:
                                    detailed_data = json.load(f)

                                assert (
                                    len(detailed_data) == 3
                                ), f"Expected 3 detailed samples, got {len(detailed_data)}"

                                print(
                                    "✓ Limit correctly applied to final output, not processing"
                                )

                                # Clean up
                                import os

                                os.unlink(output_file.name)
                                os.unlink(detailed_file.name)


def test_no_limit_processes_all():
    """Test that without limit, all samples are included in output."""
    print("Testing no limit behavior...")

    # Create mock data - simulate finding 5 samples
    mock_tool_call_data = [create_mock_tool_call_data(f"request_{i}") for i in range(5)]

    # Mock the extraction function to return our test data
    with patch(
        "experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor.extract_untruncated_tool_samples_from_request_ids"
    ) as mock_extract:
        mock_extract.return_value = mock_tool_call_data

        # Mock other dependencies
        with patch(
            "experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor.get_all_tenants_for_namespace"
        ) as mock_get_tenants:
            mock_get_tenants.return_value = ["test-tenant"]

            with patch(
                "experimental.ran.agent.analytics.big_query_utils.get_agent_conv_last_request_ids"
            ) as mock_get_request_ids:
                mock_get_request_ids.return_value = [f"request_{i}" for i in range(5)]

                with patch("base.datasets.tenants.get_tenant") as mock_get_tenant:
                    mock_get_tenant.return_value = MagicMock()

                    # Create temporary files for output
                    with tempfile.NamedTemporaryFile(
                        mode="w", suffix=".json", delete=False
                    ) as output_file:
                        with tempfile.NamedTemporaryFile(
                            mode="w", suffix=".json", delete=False
                        ) as detailed_file:
                            # Test without limit
                            test_args = [
                                "test_script.py",
                                "--namespace",
                                "test-tenant",
                                "--last-days",
                                "1",
                                "--output-file",
                                output_file.name,
                                "--detailed-output-file",
                                detailed_file.name,
                            ]

                            with patch("sys.argv", test_args):
                                from experimental.ran.agent.replay_eval.untruncated_tools_sample_extractor import (
                                    main,
                                )

                                try:
                                    main()
                                except SystemExit:
                                    pass

                                # Read the output file to verify all 5 samples are included
                                with open(output_file.name, "r") as f:
                                    output_data = json.load(f)

                                request_ids_in_output = output_data["request_ids"]
                                assert (
                                    len(request_ids_in_output) == 5
                                ), f"Expected 5 request IDs in output, got {len(request_ids_in_output)}"

                                print("✓ Without limit, all samples included in output")

                                # Clean up
                                import os

                                os.unlink(output_file.name)
                                os.unlink(detailed_file.name)


def run_all_tests():
    """Run all limit behavior tests."""
    print("Running limit behavior tests...\n")

    try:
        test_limit_applied_to_final_output()
        test_no_limit_processes_all()

        print("\n🎉 All limit behavior tests passed!")
        print(
            "The --limit parameter now correctly applies to final output rather than processing."
        )
        return True

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
