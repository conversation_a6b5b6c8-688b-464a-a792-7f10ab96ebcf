{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matched? True\n", "Done\n"]}], "source": ["import os\n", "\n", "import google.cloud.bigtable\n", "import google.cloud.bigtable.row_filters as row_filters\n", "\n", "# Run the bigtable emulator:\n", "#  $ gcloud beta emulators bigtable start\n", "#\n", "# Install cbt - the bigtable client:\n", "# gcloud components update\n", "# gcloud components install cbt\n", "\n", "# To inspect the contents of bigtable emualtor :\n", "# $ BIGTABLE_EMULATOR_HOST=localhost:8086 cbt -project test-project -instance test-instance read test-table\n", "os.environ[\"BIGTABLE_EMULATOR_HOST\"] = \"localhost:8086\"\n", "\n", "client = google.cloud.bigtable.Client(project=\"test-project\", admin=True)\n", "instance = client.instance(\"test-instance\")\n", "table = instance.table(\"test-table\")\n", "if not table.exists():\n", "    table.create()\n", "\n", "if \"cf1\" not in table.list_column_families():\n", "    print(\"Creating column family\")\n", "    table.column_family(\"cf1\").create()\n", "\n", "# This will delete all old versions of the cells and replace it with the new value\n", "row = table.direct_row(\"key1\")\n", "row.delete()\n", "row.set_cell(\"cf1\", \"column1\", \"value2\")\n", "row.commit()\n", "\n", "# This will do a compare and set\n", "row = table.conditional_row(\n", "    \"key1\",\n", "    filter_=row_filters.RowFilterChain(\n", "        filters=[\n", "            row_filters.FamilyNameRegexFilter(\"cf1\"),\n", "            row_filters.ColumnQualifierRegexFilter(\"column1\"),\n", "            row_filters.ExactValueFilter(\"value2\"),\n", "        ]\n", "    ),\n", ")\n", "row.delete()\n", "row.set_cell(\"cf1\", \"column1\", \"value1\")\n", "print(\"Matched?\", row.commit())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}