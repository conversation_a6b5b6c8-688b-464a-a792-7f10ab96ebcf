#!/usr/bin/env node

import inquirer from 'inquirer';
import chalk from 'chalk';
import { Command, Option } from 'commander';
import { generateChangelog } from './commands/generate-changelog.js';
import { getOctokit } from './github/client.js';

const program = new Command();

program.addOption(
  new Option('-t, --token <token>', 'GitHub token for API access')
    .env('GITHUB_TOKEN')
    .makeOptionMandatory(false)
);

program
  .name('changelog')
  .description('A CLI tool for managing changelogs')
  .version('0.0.0')
  .action(interactiveMode);

program
  .command('generate-changelog')
  .description('Generate a new changelog entry')
  .action(generateChangelog);

// Interactive mode when no command is provided
async function interactiveMode() {
  console.log(chalk.blue.bold('📝 Changelog CLI Tool\n'));

  // Initialize octokit or fail early
  await getOctokit(program.opts().token);

  const answers = await inquirer.prompt([
    {
      type: 'list',
      name: 'action',
      message: 'What would you like to do?',
      choices: [
        { name: '📝 Generate changelog', value: 'generate-changelog' },
        { name: '❌ Exit', value: 'exit' }
      ]
    }
  ]);

  switch (answers.action) {
    case 'generate-changelog':
      await generateChangelog();
      break;
    case 'exit':
      console.log(chalk.gray('Goodbye! 👋'));
      return;
  }

  // // Ask if user wants to continue
  // const continueAnswer = await inquirer.prompt([
  //   {
  //     type: 'confirm',
  //     name: 'continue',
  //     message: 'Would you like to perform another action?',
  //     default: true
  //   }
  // ]);

  // if (continueAnswer.continue) {
  //   await interactiveMode();
  // }
}

async function main() {
  try {
    await program.parseAsync(process.argv);
  } catch (error) {
    console.error(chalk.red('❌ Error:'), error.message);
    process.exit(1);
  }
}

main();
