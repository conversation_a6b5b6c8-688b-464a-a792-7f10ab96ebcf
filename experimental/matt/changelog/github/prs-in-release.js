import { getOctokit } from "./client.js";

export async function getPRsInRelease(previousRelease, thisRelease, labels, owner = "augmentcode", repo = "augment") {
    const octokit = await getOctokit();

    await verifyLabels(octokit, owner, repo, labels);

    const prevReleaseTagOrCommit = previousRelease;
    console.error(`Previous release/commit: ${prevReleaseTagOrCommit}`);

    // prevRelease might be a tag, and we want the commit hash
    const prevCommitDetails = await octokit.rest.repos.getCommit({
      owner,
      repo,
      ref: prevReleaseTagOrCommit,
    });
    const prevReleaseSha = prevCommitDetails.data.sha;

    const commitRange = `${prevReleaseSha}...${thisRelease}`;
    console.error(
      `Comparing: https://www.github.com/${owner}/${repo}/compare/${commitRange}`,
    );

    // Get commit comparison to find all commits in the range
    const commits = await getCommitsInRange(
      octokit,
      owner,
      repo,
      prevReleaseSha,
      thisRelease,
    );
    if (commits.length === 0) {
      throw new Error(
        `No commits found between ${prevReleaseTagOrCommit} (${prevReleaseSha}) and ${thisRelease}.`,
      );
    }

    console.error(
      `There are ${commits.length} commit(s) possibly related to this release.`,
    );
    console.error(`Examining ${commits.length} commit(s) for PRs...`);

    const prsInRelease = await lookupPRsInRelease(
      octokit,
      owner,
      repo,
      commits,
      labels,
    );
    if (prsInRelease.length === 0) {
      console.error(
        "No PRs found with the specified labels in the given commit range.",
      );
      return [];
    }
    return prsInRelease;
}

async function verifyLabels(
  octokit,
  owner,
  repo,
  labels,
) {
  for (const label of labels) {
    const l = await octokit.rest.issues.getLabel({
      owner,
      repo,
      name: label,
    });
    if (l.status !== 200) {
      throw new Error(`Label ${label} does not exist.`);
    }
  }
}

async function lookupPRsInRelease(
  octokit,
  owner,
  repo,
  commits,
  labels,
) {
  const prsInRelease = [];
  const prNumbers = new Set();
  for (let i = 0; i < commits.length; i++) {
    const commit = commits[i];
    console.error(
      `    - (${i + 1} of ${commits.length}) ${commit.sha.substring(0, 7)}`,
    );
    const prsForCommit =
      await octokit.rest.repos.listPullRequestsAssociatedWithCommit({
        owner,
        repo,
        commit_sha: commit.sha,
      });
    for (const pr of prsForCommit.data) {
      if (prNumbers.has(pr.number)) {
        continue;
      }

      for (const label of pr.labels) {
        if (labels.includes(label.name)) {
          console.error(
            `            - ${pr.html_url} - ${pr.title}${pr.user?.login ? ` (@${pr.user?.login})` : ""}`,
          );
          prNumbers.add(pr.number);
          prsInRelease.push(pr);
          break;
        }
      }
    }
  }
  return prsInRelease;
}

/**
 * Retrieves all commits in the range between two commits.
 * Ensures that all commits are retrieved by handling pagination and API limits.
 */
async function getCommitsInRange(
  octokit,
  owner,
  repo,
  baseCommit,
  headCommit,
) {
  const commitRange = `${baseCommit}...${headCommit}`;
  const comparison = await octokit.rest.repos.compareCommitsWithBasehead({
    owner,
    repo,
    basehead: commitRange,
  });

  // If the initial comparison has all the commits, return them
  if (comparison.data.total_commits === comparison.data.commits.length) {
    return comparison.data.commits;
  }

  // There are more commits to get (comparison API only returns the  first 250 commits)
  console.error(
    `First comparison returned ${comparison.data.commits.length} commits, but there are ${comparison.data.total_commits} total commits.`,
  );
  let commits = [...comparison.data.commits];
  const totalCommits = comparison.data.total_commits;
  let prevCommitCount = 0;

  while (commits.length < totalCommits && prevCommitCount != commits.length) {
    // Set the current commit count, so on the next loop it can check if the
    // commit count has changed.
    prevCommitCount = commits.length;

    const latestCommitInBatch = commits[0].sha;
    const commitRange = `${baseCommit}...${latestCommitInBatch}`;
    console.error(
      `Comparing: https://www.github.com/${owner}/${repo}/compare/${commitRange}`,
    );

    const latestComparison =
      await octokit.rest.repos.compareCommitsWithBasehead({
        owner,
        repo,
        basehead: commitRange,
      });

    if (latestComparison.data.commits.length > 0) {
      // The last commit will have been included from the previous comparison
      commits = [...latestComparison.data.commits.slice(0, -1), ...commits];
    } else if (latestComparison.data.commits.length === 0) {
      break;
    }
  }

  return commits;
}
