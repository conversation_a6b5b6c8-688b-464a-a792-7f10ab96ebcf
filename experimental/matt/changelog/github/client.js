import { Octokit } from "@octokit/rest";

let octokit = null;

export async function getOctokit(token = undefined) {
  if (octokit) {
    return octokit;
  }

  const authToken = token || process.env.GITHUB_TOKEN;
  if (!authToken) {
    throw new Error("GitHub token is required. Set GITHUB_TOKEN environment variable or pass token parameter.");
  }

  octokit = new Octokit({
    auth: authToken,
  });

  return octokit;
}
