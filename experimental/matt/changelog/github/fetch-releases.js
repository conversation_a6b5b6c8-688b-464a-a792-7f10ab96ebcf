import chalk from "chalk";
import { getOctokit } from "./client.js";

export async function fetchReleases(tagPrefix, isPrerelease, owner = "augmentcode", repo = "augment", limit = 10) {
  // Use environment variable if token not provided
  const octokit = await getOctokit();

  try {
    const intellijPreReleases = [];
    let page = 1;
    const perPage = 100;

    // Keep fetching pages until we have enough releases or no more pages
    while (intellijPreReleases.length < limit) {
      const releases = await octokit.rest.repos.listReleases({
        owner,
        repo,
        sort: "created",
        per_page: perPage,
        page: page,
      });

      if (releases.data.length === 0) {
        break; // No more releases
      }

      // Filter for pre-releases with tags starting with 'intellij@'
      for (const release of releases.data) {
        if (release.draft) {
          continue; // Skip draft releases
        }

        if (release.prerelease !== isPrerelease) {
          continue; // Skip releases that don't match the prerelease flag
        }

        if (!release.name.startsWith(tagPrefix)) {
          continue; // Skip releases that don't match the tag prefix
        }

        intellijPreReleases.push({
          tag_name: release.tag_name,
          name: release.name,
          published_at: release.published_at,
          html_url: release.html_url,
          target_commitish: release.target_commitish,
          body: release.body
        });

        if (intellijPreReleases.length >= limit) {
          break;
        }
      }

      page++;

      // If we got fewer results than perPage, we've reached the end
      if (releases.data.length < perPage) {
        break;
      }
    }

    return intellijPreReleases;
  } catch (error) {
    console.error(chalk.red(`❌ Error fetching releases: ${error.message}`));
    throw error;
  }
}
