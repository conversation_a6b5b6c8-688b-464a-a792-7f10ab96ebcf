{"name": "@augment-internal/changelog", "private": true, "version": "1.0.0", "description": "A CLI tool for managing changelogs", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "node --watch index.js"}, "bin": {"changelog": "./index.js"}, "keywords": ["cli", "changelog", "inquirer"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@octokit/rest": "^22.0.0", "chalk": "^5.3.0", "commander": "^11.1.0", "fs-extra": "^11.1.1", "inquirer": "^9.2.12"}, "devDependencies": {"@types/node": "^20.8.0"}, "engines": {"node": ">=18.0.0"}}