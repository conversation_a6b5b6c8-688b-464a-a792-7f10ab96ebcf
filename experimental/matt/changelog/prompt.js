export async function generatePrompt(pullRequests, version, releaseType) {
  return `
Follow these guidelines to generate release notes for the ${releaseType} release of the intellij plugin ${version}:

# IntelliJ Plugin Release Notes Procedure

## Analysis

1. **Analyze PRs**:
${pullRequests.map((pr) => `    - ${pr.html_url} ${pr.title}`).join("\n")}
2. **Check feature flags**: \`view tools/feature_flags/flags.jsonnet --search_query_regex "intellij"\`

## Content Rules (External Users Only)

- **Include**: User-visible benefits, workflow improvements, accessible features
- **Exclude**: Internal details, feature flags, staging, debugging tools, architecture changes
- **Format**: Bullet points, 1-2 lines each, parallel structure
- **Language**: "You can now..." not "We implemented..."
- **Consolidate**: Related features into single cohesive items

## Validation (For output)

- [ ] All IntelliJ directories analyzed
- [ ] No internal implementation details
- [ ] Features accessible to marketplace users
- [ ] Bullet points, under 1 minute read

## Structure

\`\`\`
# Jetbrains X.X.X Release Notes

## New Features

## Improvements

## Bug Fixes

\`\`\`

Avoid using special or non-ASCII characters like ‘•’, ‘—’, ‘“”’, etc. Use plain equivalents like '-', '*', or regular quotes.
`;
}
