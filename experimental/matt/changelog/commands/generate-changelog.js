import chalk from "chalk";
import inquirer from "inquirer";
import { fetchReleases } from "../github/fetch-releases.js";
import Separator from "inquirer/lib/objects/separator.js";
import { getPRsInRelease } from "../github/prs-in-release.js";
import { exec } from "node:child_process";
import { generatePrompt } from "../prompt.js";
import { join, resolve } from "node:path";
import { mkdtempSync, writeFileSync } from "node:fs";
import { tmpdir } from "node:os";

export async function generateChangelog() {
  console.log(chalk.blue('📝 Adding a new changelog entry\n'));

  const initialAnswers = await inquirer.prompt([
    {
      type: 'list',
      name: 'releaseType',
      message: 'What type of release is this?',
      choices: [
        "stable",
        "beta"
      ]
    },
    {
      type: 'confirm',
      name: 'isBetaPromotion',
      when: (answers) => answers.releaseType === 'stable',
      default: true,
      message: 'Are you promoting a beta release (No if you are publishing from a branch/commit)?'
    },
    {
      type: 'input',
      name: 'commitish',
      when: (answers) => {
        if (answers.releaseType === 'stable') {
          return !answers.isBetaPromotion;
        } else {
          return true;
        }
      },
      message: 'What git branch or commit are you releasing?',
      default: (answers) => answers.releaseType === 'beta' ? 'main' : null,
      validate: (input) => input.trim() !== '' || 'Branch/commit cannot be empty'
    },
    {
      type: 'list',
      name: 'betaVersionToPromote',
      message: 'What beta version are you promoting to stable?',
      when: (answers) => answers.isBetaPromotion,
      choices: async (answers) => {
        console.log(chalk.blue(`    🔍 Fetching previous IntelliJ ${answers.releaseType} releases...`));
        const releases = await fetchReleases("intellij@", true);
        const choices = releases.map((release) => {
          return {
            name: release.name,
            value: release
          };
        }).sort((a, b) => {
          return b.name.localeCompare(a.name);
        });
        // @ts-ignore
        choices.push(new Separator());
        return choices;
      }
    },
  ]);

  console.log(chalk.blue(`    🔍 Fetching latest IntelliJ ${initialAnswers.releaseType}...`));
  const previousReleases = await fetchReleases("intellij@", initialAnswers.releaseType === 'beta');
  const ordered = previousReleases.sort((a, b) => {
    return new Date(b.published_at).getTime() - new Date(a.published_at).getTime();
  });
  const previousRelease = ordered[0];

  const remainingAnswers = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'previousRelease',
      message: `Previous ${initialAnswers.releaseType} release was ${previousRelease.name}. Is this correct?`,
      default: true,
    }
  ]);

  if (!remainingAnswers.previousRelease) {
    throw new Error('Previous release was not confirmed. TODO: This should ask the user for the correct value.');
  }

  const answers = { ...initialAnswers, ...remainingAnswers };
  if (answers.betaVersionToPromote) {
    answers.commitish = answers.betaVersionToPromote.target_commitish;
    answers.newVersion = answers.betaVersionToPromote?.tag_name.replace("intellij@", "").replace("-beta", "");
  } else {
    throw new Error("TODO: Ask for new version number.");
  }
  const pullRequests = await getPRsInRelease(previousRelease.target_commitish, answers.commitish, ["intellij", "clients"]);
  console.log(`🧮 Found ${pullRequests.length} PRs related to this release.`);

  console.log(chalk.blue('🤖 Generating changelog with AI...'));

  const prompt = await generatePrompt(pullRequests, answers.newVersion, answers.releaseType);

  const tempDir = mkdtempSync(join(tmpdir(), 'changelog-'));
  const tempFile = join(tempDir, 'prompt.txt');
  writeFileSync(tempFile, prompt);

  const process = exec(
    `npx https://augment-assets.com/augment-latest.tgz --non-interactive --instruction-file ${tempFile}`
  );
  process.stdout?.on('data', (data) => {
    console.log(data.toString());
  });

  process.stderr?.on('data', (data) => {
    console.error(chalk.red(data.toString()));
  });

  await new Promise((resolve, reject) => {
    process.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Process exited with code ${code}`));
      }
    });
  });
}
