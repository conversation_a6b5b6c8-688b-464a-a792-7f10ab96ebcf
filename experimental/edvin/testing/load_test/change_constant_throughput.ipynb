{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "file_path = \"/home/<USER>/augment/tools/load_test/configs/next_edit_host_request_start_2024_10_03_end_2024_10_17.json\"\n", "new_constant_throughput = 0.2\n", "\n", "with open(file_path, \"r\") as f:\n", "    test_config = json.load(f)\n", "\n", "for user in test_config[\"users\"]:\n", "    user[\"constantThroughputConfig\"][\"throughput\"] = new_constant_throughput\n", "\n", "with open(file_path, \"w\") as f:\n", "    json.dump(test_config, f, indent=2)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}