#!/bin/bash

# Add ~/augment to PATH
export PATH=$PATH:~/augment

# Add ~/augment to PYTHONPATH
export PYTHONPATH=$PYTHONPATH:~/augment

max_realistic_timing_request_interval_ms=$((1000 * 3600)) # 1 hour

# dates are in iso format YYYY-MM-DDTHH:MM:SSZ
python ~/augment/tools/load_test/configs/scripts/generate_next_edit_load_test_data.py \
 --start-date="2024-11-18T21:00:00Z" \
 --end-date="2024-11-18T22:00:00Z" \
 --store-on-gcs \
 --use-realistic-timing \
 --max-realistic-timing-request-interval-ms=$max_realistic_timing_request_interval_ms \
# --user-limit=100 \
# --requests-per-user-limit=100 \
