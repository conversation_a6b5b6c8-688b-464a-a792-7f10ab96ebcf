{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import datetime\n", "from dataclasses import dataclass\n", "import logging\n", "from dataclasses_json import dataclass_json\n", "from google.cloud import bigquery  # type: ignore\n", "from google.api_core.retry import Retry\n", "from base.datasets.tenants import get_tenant\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher  # type: ignore\n", "from typing import Iterable\n", "from typing import Optional\n", "from tools.load_test import load_test_pb2\n", "from typing import List\n", "from google.protobuf import json_format\n", "from services.api_proxy import public_api_pb2\n", "from services.next_edit_host import next_edit_pb2\n", "from services.request_insight import request_insight_pb2\n", "from google.protobuf.json_format import MessageToJson\n", "import os\n", "from google.cloud import storage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DEFAULT_TENANT = get_tenant(\"dogfood-shard\")\n", "DEFAULT_QUERY_PAGE_RETRY = Retry(\n", "    initial=10,  # 10 seconds per page before retrying\n", "    maximum=60,  # 60 seconds max per page\n", "    multiplier=1.5,  # Backoff\n", "    timeout=60 * 5,  # 5 minutes before giving up\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_request_ids_by_user(\n", "    request_type: str, start: datetime.datetime, end: datetime.datetime\n", ") -> Iterable[bigquery.Row]:\n", "    \"\"\"\n", "    Get user request IDs and times from BigQuery, sorted by time from oldest to newest.\n", "\n", "    Args:\n", "        start_date: ISO (YYYY-MM-DDTHH:MM:SSZ) date of first request.\n", "        end_date: ISO (YYYY-MM-DDTHH:MM:SSZ) date of last request.\n", "\n", "    Returns:\n", "        An iterable of rows, each containing a user_id and an array of structs with request_id and time, sorted from oldest to newest.\n", "    \"\"\"\n", "\n", "    QUERY = \"\"\"\n", "    SELECT \n", "        user_id,\n", "        ARRAY_AGG(STRUCT(request_id, time) ORDER BY time ASC LIMIT 2) AS requests\n", "    FROM \n", "        `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata`\n", "    WHERE \n", "        request_type = @request_type\n", "        AND\n", "        tenant_id = @tenant_id\n", "        AND\n", "        (TIMESTAMP(time) BETWEEN TIMESTAMP(@start_iso) AND TIMESTAMP(@end_iso))\n", "    GROUP BY user_id\n", "    LIMIT 2\n", "    \"\"\"\n", "\n", "    PARAMS = [\n", "        bigquery.ScalarQueryParameter(\"tenant_id\", \"STRING\", DEFAULT_TENANT.tenant_id),\n", "        bigquery.ScalarQueryParameter(\"request_type\", \"STRING\", request_type),\n", "        bigquery.ScalarQueryParameter(\"start_iso\", \"DATETIME\", start.isoformat()),\n", "        bigquery.ScalarQueryParameter(\"end_iso\", \"DATETIME\", end.isoformat()),\n", "    ]\n", "\n", "    client = bigquery.Client(project=DEFAULT_TENANT.project_id)\n", "\n", "    for row in client.query(\n", "        QUERY,\n", "        retry=DEFAULT_QUERY_PAGE_RETRY,\n", "        job_config=bigquery.QueryJobConfig(\n", "            use_legacy_sql=False,\n", "            query_parameters=PARAMS,\n", "        ),\n", "    ).result():\n", "        yield row\n", "\n", "\n", "def get_request_events(\n", "    row: bigquery.Row, request_event_names: Optional[frozenset[str]] = None\n", ") -> Iterable[List[request_insight_pb2.RequestEvent]]:\n", "    request_ids = [request[\"request_id\"] for request in row.requests]\n", "    if len(request_ids) <= 0:\n", "        return []\n", "    results = GCSRequestInsightFetcher.from_tenant(DEFAULT_TENANT).get_requests(\n", "        request_ids=request_ids,\n", "        request_event_names=request_event_names,\n", "    )\n", "    for request in results:\n", "        if isinstance(request, Exception):\n", "            continue\n", "        yield request.events\n", "\n", "\n", "# three functions:\n", "# 1. get RIs by user\n", "# 2. create an iterator over the 'Request' objects for each user\n", "# 3. write the results of the iterator to a file\n", "\n", "\n", "def convert_next_edit_host_request_to_public_api_request(\n", "    next_edit_host_request: next_edit_pb2.NextEditRequest,\n", ") -> public_api_pb2.NextEditRequest:\n", "    change_type_mapping = {\n", "        next_edit_pb2.ChangeType.ADDED: public_api_pb2.ChangeType.ADDED,\n", "        next_edit_pb2.ChangeType.DELETED: public_api_pb2.ChangeType.DELETED,\n", "        next_edit_pb2.ChangeType.MODIFIED: public_api_pb2.ChangeType.MODIFIED,\n", "        next_edit_pb2.ChangeType.RENAMED: public_api_pb2.ChangeType.RENAMED,\n", "    }\n", "\n", "    diagnostic_severity_mapping = {\n", "        next_edit_pb2.DiagnosticSeverity.ERROR: public_api_pb2.DiagnosticSeverity.ERROR,\n", "        next_edit_pb2.DiagnosticSeverity.WARNING: public_api_pb2.DiagnosticSeverity.WARNING,\n", "        next_edit_pb2.DiagnosticSeverity.INFORMATION: public_api_pb2.DiagnosticSeverity.INFORMATION,\n", "        next_edit_pb2.DiagnosticSeverity.HINT: public_api_pb2.DiagnosticSeverity.HINT,\n", "    }\n", "\n", "    next_edit_mode_mapping = {\n", "        next_edit_pb2.NextEditMode.UNKNOWN_NEXT_EDIT_MODE: public_api_pb2.NextEditMode.UNKNOWN_NEXT_EDIT_MODE,\n", "        next_edit_pb2.NextEditMode.BACKGROUND: public_api_pb2.NextEditMode.BACKGROUND,\n", "        next_edit_pb2.NextEditMode.FOREGROUND: public_api_pb2.NextEditMode.FOREGROUND,\n", "        next_edit_pb2.NextEditMode.FORCED: public_api_pb2.NextEditMode.FORCED,\n", "    }\n", "\n", "    next_edit_scope_mapping = {\n", "        next_edit_pb2.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE: public_api_pb2.NextEditScope.UNKNOWN_NEXT_EDIT_SCOPE,\n", "        next_edit_pb2.NextEditScope.CURSOR: public_api_pb2.NextEditScope.CURSOR,\n", "        next_edit_pb2.NextEditScope.FILE: public_api_pb2.NextEditScope.FILE,\n", "        next_edit_pb2.NextEditScope.WORKSPACE: public_api_pb2.NextEditScope.WORKSPACE,\n", "    }\n", "\n", "    public_api_request = public_api_pb2.NextEditRequest(\n", "        model=next_edit_host_request.model_name,\n", "        sequence_id=next_edit_host_request.sequence_id,\n", "        lang=next_edit_host_request.lang,\n", "        instruction=next_edit_host_request.instruction,\n", "        blobs=public_api_pb2.Blobs(\n", "            checkpoint_id=next_edit_host_request.blobs.baseline_checkpoint_id,\n", "            added_blobs=[bytes.hex() for bytes in next_edit_host_request.blobs.added],\n", "            deleted_blobs=[\n", "                bytes.hex() for bytes in next_edit_host_request.blobs.deleted\n", "            ],\n", "        ),\n", "        recent_changes=[\n", "            public_api_pb2.ReplacementText(\n", "                blob_name=recent_change.blob_name,\n", "                path=recent_change.path,\n", "                char_start=recent_change.char_start,\n", "                char_end=recent_change.char_end,\n", "                replacement_text=recent_change.replacement_text,\n", "                present_in_blob=recent_change.present_in_blob,\n", "            )\n", "            for recent_change in next_edit_host_request.recent_changes\n", "        ],\n", "        vcs_change=public_api_pb2.VCSChange(\n", "            working_directory_changes=[\n", "                public_api_pb2.WorkingDirectoryChange(\n", "                    before_path=wdc.before_path,\n", "                    after_path=wdc.after_path,\n", "                    change_type=change_type_mapping[wdc.change_type],\n", "                    head_blob_name=wdc.head_blob_name,\n", "                    indexed_blob_name=wdc.indexed_blob_name,\n", "                    current_blob_name=wdc.current_blob_name,\n", "                )\n", "                for wdc in next_edit_host_request.vcs_change.working_directory_changes\n", "            ]\n", "        ),\n", "        path=next_edit_host_request.path,\n", "        blob_name=next_edit_host_request.blob_name,\n", "        selection_begin_char=next_edit_host_request.selection_begin_char,\n", "        selection_end_char=next_edit_host_request.selection_end_char,\n", "        prefix=next_edit_host_request.prefix,\n", "        selected_text=next_edit_host_request.selected_text,\n", "        suffix=next_edit_host_request.suffix,\n", "        diagnostics=[\n", "            public_api_pb2.Diagnostic(\n", "                location=public_api_pb2.FileLocation(\n", "                    path=diagnostic.location.path,\n", "                    line_start=diagnostic.location.line_start,\n", "                    line_end=diagnostic.location.line_end,\n", "                ),\n", "                message=diagnostic.message,\n", "                severity=diagnostic_severity_mapping[diagnostic.severity],\n", "            )\n", "            for diagnostic in next_edit_host_request.diagnostics\n", "        ],\n", "        mode=next_edit_mode_mapping[next_edit_host_request.mode],\n", "        scope=next_edit_scope_mapping[next_edit_host_request.scope],\n", "        edit_events=[\n", "            public_api_pb2.FileEditEvent(\n", "                path=granular_edit_event.path,\n", "                before_blob_name=granular_edit_event.before_blob_name,\n", "                after_blob_name=granular_edit_event.after_blob_name,\n", "                edits=[\n", "                    public_api_pb2.FileEdit(\n", "                        before_start=single_edit.before_start,\n", "                        after_start=single_edit.after_start,\n", "                        before_text=single_edit.before_text,\n", "                        after_text=single_edit.after_text,\n", "                    )\n", "                    for single_edit in granular_edit_event.edits\n", "                ],\n", "            )\n", "            for granular_edit_event in next_edit_host_request.edit_events\n", "        ],\n", "        blocked_locations=[\n", "            public_api_pb2.FileRegion(\n", "                path=blocked_location.path,\n", "                char_start=blocked_location.char_start,\n", "                char_end=blocked_location.char_end,\n", "            )\n", "            for blocked_location in next_edit_host_request.blocked_locations\n", "        ],\n", "    )\n", "\n", "    return public_api_request"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# some filters for the reqeuest\n", "start = datetime.datetime.now() - datetime.timedel<PERSON>(days=14)\n", "end = datetime.datetime.now()\n", "request_event_name = \"next_edit_host_request\"\n", "store_on_gcs = True\n", "\n", "# setup the user config path\n", "storage_client = storage.Client()\n", "bucket_name = \"augment-load-testing-configs\"  # this bucket lives in system-services-dev\n", "bucket = storage_client.bucket(bucket_name)\n", "blob_path = f\"user-configs/{request_event_name}_start_{start.strftime('%Y_%m_%d')}_end_{end.strftime('%Y_%m_%d')}\"\n", "\n", "# setup the test config file\n", "test_config_dir = os.path.expanduser(\"~/augment/tools/load_test/configs/\")\n", "load_test_config = load_test_pb2.TestConfig(\n", "    id=f\"ne_test_start_{start.strftime('%Y_%m_%d')}_end_{end.strftime('%Y_%m_%d')}\",\n", "    users=[],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_count = 0\n", "for row in get_request_ids_by_user(request_type=\"NEXT_EDIT\", start=start, end=end):\n", "    print(len(row.requests))\n", "\n", "    # skip users with less than 200 requests\n", "    if len(row.requests) < 200:\n", "        continue\n", "\n", "    user_count += 1\n", "\n", "    request_metadata = load_test_pb2.RequestsMetadata(\n", "        blob_names=[],\n", "        checkpoint_ids=[],\n", "    )\n", "\n", "    user_blob_names: set[str] = set()\n", "    user_checkpoints: set[str] = set()\n", "    user_requests: list[load_test_pb2.Request] = []\n", "\n", "    # write the requests for this user\n", "    for events in get_request_events(\n", "        row, request_event_names=frozenset({\"next_edit_host_request\"})\n", "    ):\n", "        print(events)\n", "        for event in events:\n", "            # convert the next_edit_host_request to a public_api_request\n", "            next_edit_host_request = event.next_edit_host_request.request\n", "            public_api_next_edit_request = (\n", "                convert_next_edit_host_request_to_public_api_request(\n", "                    next_edit_host_request\n", "                )\n", "            )\n", "            load_test_request = load_test_pb2.Request(\n", "                offset_millis=0,  # use constant throughput and ignore for now\n", "                next_edit=public_api_next_edit_request,\n", "            )\n", "\n", "            # record request, blobnames and the checkpoint\n", "            user_requests.append(load_test_request)\n", "            user_blob_names.update(public_api_next_edit_request.blobs.added_blobs)\n", "            user_blob_names.update(public_api_next_edit_request.blobs.deleted_blobs)\n", "            user_checkpoints.add(public_api_next_edit_request.blobs.checkpoint_id)\n", "\n", "    request_metadata.blob_names.extend(user_blob_names)\n", "    request_metadata.checkpoint_ids.extend(user_checkpoints)\n", "\n", "    user_request_config_file = f\"{row.user_id}_request-config.jsonl\"\n", "\n", "    if store_on_gcs:\n", "        # create the blob\n", "        user_request_config_path = (\n", "            f\"gs://{bucket_name}/{blob_path}/{user_request_config_file}\"\n", "        )\n", "        blob = bucket.blob(blob_path + f\"/{user_request_config_file}\")\n", "\n", "        # convert requests to json string\n", "        content = MessageToJson(request_metadata, indent=None) + \"\\n\"\n", "        content += \"\\n\".join(\n", "            MessageToJson(request, indent=None) for request in user_requests\n", "        )\n", "\n", "        # Upload the json string to the blob\n", "        blob.upload_from_string(content, content_type=\"application/json\")\n", "\n", "        print(f\"File {user_request_config_path} uploaded to GCS bucket.\")\n", "    else:\n", "        # setup the local user config dir\n", "        user_config_dir = os.path.expanduser(\n", "            f\"~/augment/tools/load_test/configs/users/{request_event_name}_start_{start.strftime('%Y_%m_%d')}_end_{end.strftime('%Y_%m_%d')}\"\n", "        )\n", "        os.makedirs(user_config_dir, exist_ok=True)\n", "\n", "        # setup the file with requests for this user\n", "        user_request_config_path = os.path.join(\n", "            user_config_dir, user_request_config_file\n", "        )\n", "\n", "        # create the user_request_config_file and write to it\n", "        with open(user_request_config_path, \"w\") as f:\n", "            f.write(MessageToJson(request_metadata, indent=None))\n", "            for request in user_requests:\n", "                f.write(\"\\n\")\n", "                f.write(MessageTo<PERSON>son(request, indent=None))\n", "\n", "    # create the user_config\n", "    user_config = load_test_pb2.UserConfig(\n", "        id=row.user_id,\n", "        request_config_path=user_request_config_path,\n", "        constant_throughput_config=load_test_pb2.UserConfig.ConstantThroughputConfig(\n", "            throughput=0.5\n", "        ),\n", "    )\n", "\n", "    # add the user config to the test config\n", "    load_test_config.users.append(user_config)\n", "\n", "\n", "# write the config file for this test\n", "base_dir = os.path.expanduser(\"~/augment/tools/load_test/configs\")\n", "\n", "user_request_config_file = f\"{request_event_name}_start_{start.strftime('%Y_%m_%d')}_end_{end.strftime('%Y_%m_%d')}.json\"\n", "os.makedirs(base_dir, exist_ok=True)\n", "test_config_path = os.path.join(base_dir, user_request_config_file)\n", "\n", "with open(test_config_path, \"w\") as f:\n", "    f.write(MessageToJson(load_test_config, indent=2))\n", "\n", "\n", "print(f\"Wrote {user_count} users to {test_config_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}