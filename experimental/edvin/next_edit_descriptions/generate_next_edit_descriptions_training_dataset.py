"""Take next-edit descriptions and turn them into an indexed dataset for training.

The dataset can be used to distill a smaller model to generate these descriptions.
"""

import argparse
import concurrent.futures
import shutil
import time
from tqdm import tqdm
from pathlib import Path

import pandas

import megatron.data.indexed_dataset as indexed_dataset
from megatron.data.indexed_dataset import TokenSeqLike

from base.tokenizers import create_tokenizer_by_name
from base.prompt_format_next_edit.description_prompt_formatter import (
    RavenDescribePromptFormatter,
)
from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name

SUCCESS_FILE = "_SUCCESS"


class DescriptionFormatter:
    def __init__(self):
        # Taken from: services/deploy/raven_edit_v2_15b_deploy.jsonnet
        tokenizer_name = "llama3_instruct"
        prompt_formatter_config = RavenDescribePromptFormatter.Config()
        chat_prompt_formatter_name = "llama3"

        self.tokenizer = create_tokenizer_by_name(tokenizer_name)
        self.description_prompt_formatter = RavenDescribePromptFormatter(
            self.tokenizer,
            config=prompt_formatter_config,
        )
        self.chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(
            chat_prompt_formatter_name,
            self.tokenizer,
        )

    def format_prompt(
        self,
        description: str,
        diff: str,
        seq_len: int,
        mask_out_input_tokens: bool = True,
    ) -> list[int]:
        structured_prompt = (
            self.description_prompt_formatter.format_input_from_diff_str(diff)
        )
        tokenized_prompt = self.chat_prompt_formatter.format_prompt(
            structured_prompt
        ).tokens

        eos = self.tokenizer.special_tokens.eos

        if mask_out_input_tokens:
            tokenized_prompt = [-token for token in tokenized_prompt]

        label_tokens = self.tokenizer.tokenize_unsafe(description) + [eos]

        sample_tokens = tokenized_prompt + label_tokens
        # print(f"Raw token length: {len(sample_tokens)}")

        if len(sample_tokens) < seq_len:
            sample_tokens += [-eos] * (seq_len - len(sample_tokens))
        else:
            sample_tokens = sample_tokens[:seq_len]

        assert len(sample_tokens) == seq_len
        return sample_tokens


def create_indexed_dataset_batches(
    output_path: Path, all_tokens: list[TokenSeqLike], vocab_size: int
):
    builder = indexed_dataset.make_builder(
        str(output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=vocab_size,
    )
    num_rows = 0

    for tokens in all_tokens:
        builder.add_item(tokens)
        builder.end_document()
        num_rows += 1

    builder.finalize(str(output_path.with_suffix(".idx")))

    # Rename the file to include the number of rows
    new_output_path = Path(str(output_path) + f"_numrows={num_rows}")
    output_path.with_suffix(".bin").rename(new_output_path.with_suffix(".bin"))
    output_path.with_suffix(".idx").rename(new_output_path.with_suffix(".idx"))


def _combine_indexed_datasets(
    combined_output_path: Path, input_paths: list[Path], vocab_size: int
):
    """Merge indexed datasets into a single one.

    Args:
        combined_output_path: The output path (with no .bin or .idx)
        input_paths: The input paths (can have .bin or .idx)
        vocab_size: The size of the token
    """
    if not input_paths:
        return

    input_paths = [path.with_suffix("") for path in sorted(input_paths)]
    combined_builder = indexed_dataset.make_builder(
        str(combined_output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=vocab_size,
    )

    for path in input_paths:
        combined_builder.merge_file_(str(path.with_suffix("")))

    combined_builder.finalize(str(combined_output_path.with_suffix(".idx")))


def get_num_rows(dataset_path: Path) -> int:
    dataset = indexed_dataset.make_dataset(
        str(dataset_path.with_suffix("")), "mmap", skip_warmup=True
    )
    return len(dataset)


def combine_indexed_dataset_batches(
    indexed_dataset_path: Path,
    batches_output_path: Path,
    num_validation_samples: int,
    vocab_size: int,
):
    train_batch_files = []
    validation_batch_files = []
    num_collected_validation_samples = 0

    for partition_file in batches_output_path.glob("*.bin"):
        if num_collected_validation_samples < num_validation_samples:
            try:
                num_rows = get_num_rows(partition_file)
            except Exception as e:  # pylint: disable=broad-except
                print(f"Failed to get num rows for {partition_file}: {e}")
                continue
            validation_batch_files.append(partition_file)
            num_collected_validation_samples += num_rows
        else:
            train_batch_files.append(partition_file)

    _combine_indexed_datasets(
        combined_output_path=indexed_dataset_path / "validation_dataset",
        input_paths=validation_batch_files,
        vocab_size=vocab_size,
    )

    _combine_indexed_datasets(
        combined_output_path=indexed_dataset_path / "dataset",
        input_paths=train_batch_files,
        vocab_size=vocab_size,
    )

    # Declare success
    (indexed_dataset_path / SUCCESS_FILE).touch()

    # Delete the batches directory
    try:
        shutil.rmtree(batches_output_path)
    except OSError:
        # NOTE: due to the potential file system issue, rm -rf may fail
        # due to `OSError: [Errno 39] Directory not empty`.
        print(f"Failed to delete {batches_output_path}, retrying...")
        time.sleep(5)
        shutil.rmtree(batches_output_path)

    # Cleanup batch folder
    if batches_output_path.exists() and batches_output_path.is_dir():
        assert (
            batches_output_path.name == "batches"
        ), f"Path does not end with 'batches': {batches_output_path}"
        shutil.rmtree(batches_output_path)


def process_parquet_file(
    batches_output_path: Path,
    parquet_file: Path,
    formatter: DescriptionFormatter,
    seq_len: int,
):
    try:
        output_path = batches_output_path / parquet_file.stem
        if output_path.with_suffix(".idx").exists():
            print(f"Output path {output_path} already exists, skipping")
            return

        df = pandas.read_parquet(parquet_file)
        all_tokens = []

        print(f"Creating indexed dataset from {parquet_file}")

        for description, diff in zip(
            list(df["descriptions"][0]), list(df["description_diff_inputs"][0])
        ):
            if not description.strip() or not diff.strip():
                continue
            tokens = formatter.format_prompt(description, diff, seq_len=seq_len)
            all_tokens.append(tokens)

        print("Calling create_indexed_dataset_batches")

        create_indexed_dataset_batches(
            output_path,
            all_tokens,
            formatter.tokenizer.vocab_size,
        )
    except Exception as e:  # pylint: disable=broad-except
        print(f"Failed to process {parquet_file}: {e}")


def main():
    parser = argparse.ArgumentParser(
        description="Generate next edit descriptions training dataset."
    )
    parser.add_argument(
        "--input-path",
        type=Path,
        default="/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions/",
        help="Path to input parquet files",
    )
    parser.add_argument(
        "--output-path",
        type=Path,
        default="/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_indexed_dataset/",
        help="Path for output indexed dataset",
    )
    parser.add_argument(
        "--seq-len", type=int, default=4097, help="Sequence length for tokenization"
    )
    parser.add_argument(
        "--num-validation-samples",
        type=int,
        default=12800,
        help="Number of samples to use for validation",
    )
    parser.add_argument(
        "--num-workers",
        type=int,
        default=32,
        help="Number of data worker processes, or 0 to avoid multiprocessing",
    )
    args = parser.parse_args()

    indexed_dataset_output_path = args.output_path
    batches_output_path = indexed_dataset_output_path / "batches"
    batches_output_path.mkdir(parents=True, exist_ok=True)

    formatter = DescriptionFormatter()

    parquet_files = list(args.input_path.glob("*.parquet"))

    with concurrent.futures.ThreadPoolExecutor(
        max_workers=args.num_workers
    ) as executor:
        futures = [
            executor.submit(
                process_parquet_file,
                batches_output_path=batches_output_path,
                parquet_file=parquet_file,
                formatter=formatter,
                seq_len=args.seq_len,
            )
            for parquet_file in parquet_files
        ]

        for _ in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(parquet_files),
            desc="Processing parquet files",
        ):
            pass

    combine_indexed_dataset_batches(
        indexed_dataset_output_path,
        batches_output_path,
        num_validation_samples=args.num_validation_samples,
        vocab_size=formatter.tokenizer.vocab_size,
    )


if __name__ == "__main__":
    main()
