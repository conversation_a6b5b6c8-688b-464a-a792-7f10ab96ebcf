{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from base.augment_client.client import AugmentClient\n", "\n", "AUGMENT_API_TOKEN = (Path.home() / \".config/augment/api_token\").read_text().strip()\n", "\n", "dogfood_client = AugmentClient(\n", "    \"https://staging-shard-0.api.augmentcode.com/\", AUGMENT_API_TOKEN\n", ")\n", "\n", "get_models_response = dogfood_client.get_models()\n", "\n", "for model in get_models_response.models:\n", "    print(model.name)\n", "\n", "\n", "model_70b = dogfood_client.client_for_model(\"binks-l3-70B-FP8-ug-chatanol1-16-3-chat\")\n", "\n", "# Is there an 8b model running somewhere else we should use?\n", "arun_client = AugmentClient(\n", "    \"https://dev-arun.us-central.api.augmentcode.com/\", AUGMENT_API_TOKEN\n", ")\n", "\n", "model_8b = arun_client.client_for_model(\"binks-8b-chat\")\n", "\n", "# model_8b.chat(selected_code=\"\", message=\"hello\", prefix=\"\", suffix=\"\", path=\"\").text\n", "model_70b.chat(selected_code=\"\", message=\"hello\", prefix=\"\", suffix=\"\", path=\"\").text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}