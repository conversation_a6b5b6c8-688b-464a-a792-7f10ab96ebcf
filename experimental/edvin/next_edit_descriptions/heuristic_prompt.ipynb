{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Iterable\n", "import pandas\n", "import pickle\n", "import re\n", "import json5\n", "\n", "from dataclasses import dataclass\n", "from base.diff_utils.str_diff import DiffSpan, precise_char_diff, precise_line_diff\n", "from base.diff_utils.str_diff import AddedSpan, DeletedSpan, ModSpan, NoopSpan\n", "\n", "# from services.next_edit_host.server.handler import (\n", "#     ScoredFileHunk,\n", "# )\n", "\n", "# from services.next_edit_host.server.next_edit_handler import (\n", "#     _split_changes_into_hunks,\n", "# )\n", "\n", "# from research.model_server.next_edits_handlers import (\n", "#     NextEditResult,\n", "#     _split_changes_into_hunks,\n", "# )\n", "\n", "from base.diff_utils.diff_utils import compute_file_diff, File\n", "from research.next_edits.edit_gen_sampler import EditGenProblem\n", "from pathlib import Path\n", "from base.ranges.range_types import CharRange\n", "from research.utils.repo_change_utils import RepoChange, CommitMeta\n", "from research.next_edits.edit_gen_sampler import EditGenOutput"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Optional\n", "\n", "\n", "@dataclass\n", "class MyResult:\n", "    existing_code: str\n", "    suggested_code: str\n", "    diff_spans: tuple[DiffSpan, ...]\n", "    truncation_char: int | None = None\n", "    change_description: str = \"\"\n", "\n", "\n", "def _split_changes_into_hunks(\n", "    change: <PERSON><PERSON><PERSON><PERSON>,\n", "    n_context_lines: int = 3,\n", ") -> Iterable[MyResult]:\n", "    \"\"\"Split a change diff into multiple hunks.\"\"\"\n", "    assert n_context_lines > 0, \"context_lines must be positive\"\n", "    line_level_hunks = precise_line_diff(\n", "        change.existing_code, change.suggested_code\n", "    ).group_into_hunks(n_context_lines - 1)\n", "\n", "    for hunk, hunk_span_before in zip(\n", "        line_level_hunks.spans, line_level_hunks.span_ranges_in_before\n", "    ):\n", "        spans = precise_char_diff(hunk.before, hunk.after).spans\n", "        yield MyResult(\n", "            existing_code=hunk.before,\n", "            suggested_code=hunk.after,\n", "            diff_spans=spans,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def heuristic_v1(result: MyR<PERSON>ult, char_limit=100) -> Optional[str]:\n", "    # Filter for the diffspans where something is happening\n", "    diff_spans = [\n", "        diff_span\n", "        for diff_span in result.diff_spans\n", "        if not isinstance(diff_span, NoopSpan)\n", "    ]\n", "\n", "    if len(diff_spans) == 0:\n", "        return \"\"  # no change => no description\n", "\n", "    # Deal with the case where we only have one diff span\n", "    if len(diff_spans) == 1:\n", "        text_to_add = diff_spans[0].after.strip()\n", "        text_to_remove = diff_spans[0].before.strip()\n", "\n", "        if isinstance(diff_spans[0], AddedSpan):\n", "            if len(text_to_add) < char_limit:\n", "                return f\"Add '{text_to_add}'\"\n", "\n", "        if isinstance(diff_spans[0], ModSpan):\n", "            if (len(text_to_remove) + len(text_to_add)) < char_limit:\n", "                return f\"Replace '{text_to_remove}' with '{text_to_add}'\"\n", "\n", "        if isinstance(diff_spans[0], DeletedSpan):\n", "            if len(text_to_remove) < char_limit:\n", "                return f\"Remove '{text_to_remove}'\"\n", "\n", "    # If all are additions, we can say \"Add ....\"\n", "    if all(isinstance(diff_span, AddedSpan) for diff_span in diff_spans):\n", "        text_to_add = \", \".join([add_span.after.strip() for add_span in diff_spans])\n", "        if len(text_to_add) < char_limit:\n", "            return f\"Add '{text_to_add}'\"\n", "\n", "    return \"No heuristic description\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def heuristic_v2(result: MyR<PERSON>ult, char_limit=40) -> Optional[str]:\n", "    # Add the things in order of the diff spans\n", "    add_string = \"\"\n", "    mod_string = \"\"\n", "    del_string = \"\"\n", "\n", "    delim_char = \"'\"\n", "\n", "    for diff_span in result.diff_spans:\n", "        if isinstance(diff_span, NoopSpan):\n", "            continue\n", "\n", "        lines_to_add = diff_span.after.split(\"\\n\")\n", "        lines_to_remove = diff_span.before.split(\"\\n\")\n", "\n", "        # Take the first line\n", "        text_to_add = lines_to_add[0].strip()\n", "        text_to_remove = lines_to_remove[0].strip()\n", "\n", "        # If it is too long,\n", "        if len(text_to_add) > char_limit:\n", "            words = text_to_add.split()\n", "            truncated_text = \"\"\n", "            for word in words:\n", "                if (\n", "                    len(truncated_text) + len(word) + 1 > char_limit - 3\n", "                ):  # +1 for space, -3 for \"...\"\n", "                    break\n", "                truncated_text += word + \" \"\n", "            text_to_add = truncated_text + \"...\"\n", "            break\n", "\n", "        if isinstance(diff_span, AddedSpan):\n", "            if add_string == \"\":\n", "                add_string += \"Add \"\n", "            else:\n", "                add_string += \", \"\n", "            add_string += f\"{delim_char}{text_to_add}{delim_char}\"\n", "\n", "        if isinstance(diff_span, ModSpan):\n", "            if mod_string == \"\":\n", "                mod_string += \"Replace \"\n", "            else:\n", "                mod_string += \", \"\n", "\n", "            mod_string += f\"{delim_char}{text_to_remove}{delim_char} \\u2192 {delim_char}{text_to_add}{delim_char}\"\n", "\n", "        if isinstance(diff_span, DeletedSpan):\n", "            if del_string == \"\":\n", "                del_string += \"Delete \"\n", "            else:\n", "                del_string += \", \"\n", "\n", "            del_string += f\"'{text_to_remove}'\"\n", "\n", "    # Create the final string\n", "    if add_string != \"\":\n", "        add_string += \". \"\n", "    if mod_string != \"\":\n", "        mod_string += \". \"\n", "    if del_string != \"\":\n", "        del_string += \".\"\n", "\n", "    # would we allow this to cross multiple lines?\n", "    # multiple spans should cross mutliple lines, so the highlight will be multiple lines\n", "    # although that might be confusing if you have to accept all the changes\n", "    description = f\"{add_string}{mod_string}{del_string}\"\n", "\n", "    if len(description) > char_limit:\n", "        # If too much to delete, we summarize with 'Remove lines.'\"\n", "        if all(isinstance(diff_span, DeletedSpan) for diff_span in result.diff_spans):\n", "            return \"Remove lines.\"\n", "        else:\n", "            return None\n", "\n", "    return description\n", "\n", "\n", "# quotes are confusing\n", "# make sure it fits on one line (with ..., or with ...)\n", "# Use unicode symbols (arrow, enter symbol, etc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["delete_start = \"Delete: \"\n", "add_start = \"Add: \"\n", "mod_start = \"Change: \"\n", "delim_char = \"\"\n", "mod_middle = \" \\u279c \"\n", "\n", "\n", "def heuristic_v3(result: MyR<PERSON>ult, char_limit=40) -> Optional[str]:\n", "    # Add the things in order of the diff spans\n", "    description = \"\"\n", "    all_delete = True\n", "    num_lines_to_delete = 0\n", "\n", "    # if we encounter multiple lines at any point, return None\n", "    # as long as we don't, we add to the description\n", "    for diff_span in result.diff_spans:\n", "        # skip starting newlines, add later to description\n", "        if isinstance(diff_span, NoopSpan):\n", "            if description == \"\":\n", "                continue\n", "            else:\n", "                num_lines = diff_span.text.count(\"\\n\")\n", "                description += \"\\n\" * num_lines\n", "            continue\n", "\n", "        lines_to_add = [\n", "            line.strip() for line in diff_span.after.split(\"\\n\") if line.strip() != \"\"\n", "        ]\n", "\n", "        if isinstance(diff_span, AddedSpan):\n", "            # we have found a span that is not Delete or Noop\n", "            all_delete = 0\n", "\n", "            # check if we should fall back to model\n", "            if len(lines_to_add) > 1 or description.count(\"\\n\") > 0:\n", "                return None  # fall back to model\n", "\n", "            if len(lines_to_add) == 1:\n", "                description += f\"{add_start}{delim_char}{lines_to_add[0]}{delim_char} \"\n", "\n", "            continue\n", "\n", "        lines_to_delete = [\n", "            line.strip() for line in diff_span.before.split(\"\\n\") if line.strip() != \"\"\n", "        ]\n", "\n", "        if isinstance(diff_span, DeletedSpan):\n", "            # check if we should fall back to model\n", "            if not all_delete and (\n", "                len(lines_to_delete) > 1 or description.count(\"\\n\") > 0\n", "            ):\n", "                return None  # fall back to model\n", "\n", "            # add delete description\n", "            for line_to_delete in lines_to_delete:\n", "                description += (\n", "                    f\"{delete_start}{delim_char}{line_to_delete}{delim_char} \"\n", "                )\n", "                num_lines_to_delete += 1 if all_delete else 0\n", "\n", "            continue\n", "\n", "        if isinstance(diff_span, ModSpan):\n", "            # we have found a span that is not Delete or Noop\n", "            all_delete = 0\n", "\n", "            if (\n", "                len(lines_to_add) > 1\n", "                or len(lines_to_delete) > 1\n", "                or description.count(\"\\n\") > 0\n", "            ):\n", "                return None  # fall back to model\n", "\n", "            # add change description\n", "            if len(lines_to_add) == 1 and len(lines_to_delete) == 1:\n", "                description += f\"{mod_start}{delim_char}{lines_to_delete[0]}{delim_char}{mod_middle}{delim_char}{lines_to_add[0]}{delim_char} \"\n", "\n", "            continue\n", "\n", "    # Make sure final description is not too long\n", "    if len(description) > char_limit:\n", "        # If too much to delete, we summarize with 'Remove lines.'\"\n", "        if all_delete:\n", "            return (\n", "                f\"Delete {num_lines_to_delete} lines\"\n", "                if num_lines_to_delete > 1\n", "                else \"Delete line\"\n", "            )\n", "        else:\n", "            return None\n", "\n", "    return description"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Testing what spans are generated in different scenarios"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# see what the char diff looks like for some different scenarios\n", "result_list = []\n", "\n", "# Scenario 0: Delete a full line\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 1: Delete a full line\")\n", "print(result)\n", "\n", "# Scenario 1: Delete adjacent lines\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 1\n", "line 2\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 2: Delete adjacent lines\")\n", "print(result)\n", "\n", "# Scenario 2: Delete separated lines\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 1\n", "line 3\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 3: Delete separated lines\")\n", "print(result)\n", "\n", "# Scenario 4: Delete a single character\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 1\n", "line 2\n", "line \n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 4: Delete a single character\")\n", "print(result)\n", "\n", "# Scenario 5: Delete separated characters on a single line\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 1\n", "line 2\n", "ine \n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 5: Delete separated characters on a single line\")\n", "print(result)\n", "\n", "# Scenario 6: Delete single character multiple lines\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 1\n", "line 2\n", "line \n", "line 4\n", "line \n", "line 6\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 6: Delete single character multiple lines\")\n", "print(result)\n", "\n", "#  Scenario 7: Add multiple newlines\n", "before = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "\n", "\n", "\n", "\n", "\"\"\"\n", "\n", "result = precise_char_diff(before, after).spans\n", "result_list.append(result)\n", "print(\"Scenario 7: Add multiple newlines\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_delete_lines_description(num_lines: int) -> str:\n", "    return \"Delete line\" if num_lines == 1 else f\"Delete {num_lines} lines\"\n", "\n", "\n", "def get_add_empty_line_description(num_lines: int) -> str:\n", "    return \"Add new line\" if num_lines == 1 else f\"Add {num_lines} new lines\"\n", "\n", "\n", "delete_start = \"Delete: \"\n", "add_start = \"Add: \"\n", "mod_start = \"Change: \"\n", "delim_char = \"\"\n", "split_char = \", \"\n", "mod_middle = \" \\u279c \"\n", "char_limit = 40\n", "\n", "\n", "def to_italic(text):\n", "    return text\n", "    # return ''.join(map(lambda char: chr(ord(char) + 119808) if 65 <= ord(char) <= 90 else char, text))\n", "\n", "\n", "def heuristic_v4(result: MyResult) -> Optional[str]:\n", "    if all(isinstance(diff_span, NoopSpan) for diff_span in result.diff_spans):\n", "        return \"\"\n", "\n", "    num_full_lines_deleted = 0  # the number of full lines deleted\n", "    is_starting_at_new_line = True  # the first span will start at a new line\n", "    only_full_lines_deleted = (\n", "        True  # we start assuming that there will only be full lines deleted\n", "    )\n", "    other_spans_exist = False  # if AddSpan or ModSpan exist, we move on to next logic\n", "    delete_description = \"\"\n", "\n", "    for diff_span in result.diff_spans:\n", "        match diff_span:\n", "            case NoopSpan():\n", "                # if the span ends with \\n, the next one will start on a new line\n", "                is_starting_at_new_line = diff_span.text.endswith(\"\\n\")\n", "\n", "            case DeletedSpan():\n", "                # count the number of whole lines deleted\n", "                if is_starting_at_new_line and diff_span.before.endswith(\"\\n\"):\n", "                    num_full_lines_deleted += diff_span.before.count(\"\\n\")\n", "                else:\n", "                    only_full_lines_deleted = False\n", "\n", "                # add the content to delete\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "                for line_to_delete in lines_to_delete:\n", "                    delete_description += split_char if delete_description != \"\" else \"\"\n", "                    delete_description += f\"{delete_start}\"\n", "                    delete_description += to_italic(\n", "                        f\"{delim_char}{line_to_delete }{delim_char}\"\n", "                    )\n", "\n", "            case AddedSpan():\n", "                other_spans_exist = True\n", "                break\n", "            case ModSpan():\n", "                other_spans_exist = True\n", "                break\n", "\n", "    # If everything was delete or noop, we can return here\n", "    if not other_spans_exist:\n", "        if only_full_lines_deleted:\n", "            return get_delete_lines_description(num_lines=num_full_lines_deleted)\n", "\n", "        if len(delete_description) > char_limit:\n", "            return None  # fall back to model if not full lines and too much to describe\n", "\n", "        return delete_description\n", "\n", "    # Add the things in order of the diff spans\n", "    description = \"\"\n", "    fall_back_to_model = False\n", "    only_empty_lines_added = True\n", "    empty_line_add_count = 0\n", "\n", "    for diff_span in result.diff_spans:\n", "        match diff_span:\n", "            case NoopSpan():\n", "                if description == \"\":\n", "                    continue\n", "                else:\n", "                    # If a noop span has a newline, any subsequent span should make us fall back to the model\n", "                    fall_back_to_model = (\n", "                        True\n", "                        if diff_span.text.count(\"\\n\") > 0 or fall_back_to_model\n", "                        else False\n", "                    )\n", "\n", "            case AddedSpan():\n", "                # If only adding empty lines, count how many we are adding\n", "                if all(diff_char == \"\\n\" for diff_char in diff_span.after):\n", "                    empty_line_add_count += len(diff_span.after)\n", "                else:\n", "                    only_empty_lines_added = False\n", "\n", "                # split lines and strip whitespace\n", "                lines_to_add = [\n", "                    line.strip()\n", "                    for line in diff_span.after.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # check if we should fall back to model\n", "                if len(lines_to_add) > 1 or fall_back_to_model:\n", "                    return None  # fall back to model\n", "\n", "                # add to description\n", "                if len(lines_to_add) == 1:\n", "                    description += split_char if description != \"\" else \"\"\n", "                    description += f\"{add_start}\"\n", "                    description += to_italic(\n", "                        f\"{delim_char}{lines_to_add[0]}{delim_char}\"\n", "                    )\n", "\n", "            case DeletedSpan():\n", "                only_empty_lines_added = False\n", "                # split lines and strip whitespace\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                if len(lines_to_delete) > 1 or fall_back_to_model:\n", "                    return None  # fall back to model\n", "\n", "                # add delete description\n", "                if len(lines_to_delete) == 1:\n", "                    description += split_char if description != \"\" else \"\"\n", "                    description += f\"{delete_start}\"\n", "                    description += to_italic(\n", "                        f\"{delim_char}{lines_to_delete[0]}{delim_char}\"\n", "                    )\n", "\n", "            case ModSpan():\n", "                only_empty_lines_added = False\n", "                # split lines and strip whitespace\n", "                lines_to_add = [\n", "                    line.strip()\n", "                    for line in diff_span.after.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # check if we should fall back to model\n", "                if (\n", "                    len(lines_to_add) > 1\n", "                    or len(lines_to_delete) > 1\n", "                    or fall_back_to_model\n", "                ):\n", "                    return None  # fall back to model\n", "\n", "                # add change description\n", "                if len(lines_to_add) == 1 and len(lines_to_delete) == 1:\n", "                    description += split_char if description != \"\" else \"\"\n", "                    description += mod_start\n", "                    description += to_italic(\n", "                        f\"{delim_char}{lines_to_delete[0]}{delim_char}\"\n", "                    )\n", "                    description += mod_middle\n", "                    description += to_italic(\n", "                        f\"{delim_char}{lines_to_add[0]}{delim_char}\"\n", "                    )\n", "\n", "    # check if we only added empty lines\n", "    if only_empty_lines_added:\n", "        return get_add_empty_line_description(num_lines=empty_line_add_count)\n", "\n", "    # Make sure final description is not too long\n", "    if len(description) > char_limit:\n", "        return None  # fall back to model\n", "\n", "    return description  # might make sense to do the italic conversion here, but could be overoptimization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def heuristic_v5(result: MyResult) -> Optional[str]:\n", "    if all(isinstance(diff_span, NoopSpan) for diff_span in result.diff_spans):\n", "        return \"\"\n", "\n", "    num_full_lines_deleted = 0  # the number of full lines deleted\n", "    is_starting_at_new_line = True  # the first span will start at a new line\n", "    only_full_lines_deleted = (\n", "        True  # we start assuming that there will only be full lines deleted\n", "    )\n", "    other_spans_exist = False  # if AddSpan or ModSpan exist, we move on to next logic\n", "    delete_description = \"\"\n", "\n", "    for diff_span in result.diff_spans:\n", "        match diff_span:\n", "            case NoopSpan():\n", "                # if the span ends with \\n, the next one will start on a new line\n", "                is_starting_at_new_line = diff_span.text.endswith(\"\\n\")\n", "\n", "            case DeletedSpan():\n", "                # count the number of whole lines deleted\n", "                if is_starting_at_new_line and diff_span.before.endswith(\"\\n\"):\n", "                    num_full_lines_deleted += diff_span.before.count(\"\\n\")\n", "                else:\n", "                    only_full_lines_deleted = False\n", "\n", "                # add the content to delete\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "                for line_to_delete in lines_to_delete:\n", "                    delete_description += split_char if delete_description != \"\" else \"\"\n", "                    delete_description += f\"{delete_start}\"\n", "                    delete_description += to_italic(\n", "                        f\"{delim_char}{line_to_delete }{delim_char}\"\n", "                    )\n", "\n", "            case AddedSpan():\n", "                other_spans_exist = True\n", "                break\n", "            case ModSpan():\n", "                other_spans_exist = True\n", "                break\n", "\n", "    # If everything was delete or noop, we can return here\n", "    if not other_spans_exist:\n", "        if only_full_lines_deleted:\n", "            return get_delete_lines_description(num_lines=num_full_lines_deleted)\n", "\n", "        if len(delete_description) > char_limit:\n", "            return None  # fall back to model if not full lines and too much to describe\n", "\n", "        return delete_description\n", "\n", "    # Add the things in order of the diff spans\n", "    description = \"\"\n", "    only_empty_lines_added = True\n", "    empty_line_add_count = 0\n", "\n", "    for diff_span in result.diff_spans:\n", "        match diff_span:\n", "            case NoopSpan():\n", "                continue\n", "            case AddedSpan():\n", "                # If only adding empty lines, count how many we are adding\n", "                if all(diff_char == \"\\n\" for diff_char in diff_span.after):\n", "                    empty_line_add_count += len(diff_span.after)\n", "                else:\n", "                    only_empty_lines_added = False\n", "\n", "                # split lines and strip whitespace\n", "                lines_to_add = [\n", "                    line.strip()\n", "                    for line in diff_span.after.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # add to description\n", "                for line_to_add in lines_to_add:\n", "                    description += split_char if description != \"\" else \"\"\n", "                    description += f\"{add_start}\"\n", "                    description += to_italic(f\"{delim_char}{line_to_add}{delim_char}\")\n", "\n", "            case DeletedSpan():\n", "                only_empty_lines_added = False\n", "                # split lines and strip whitespace\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # add delete description\n", "                for line_to_delete in lines_to_delete:\n", "                    description += split_char if description != \"\" else \"\"\n", "                    description += f\"{delete_start}\"\n", "                    description += to_italic(\n", "                        f\"{delim_char}{line_to_delete}{delim_char}\"\n", "                    )\n", "\n", "            case ModSpan():\n", "                only_empty_lines_added = False\n", "                # split lines and strip whitespace\n", "                lines_to_add = [\n", "                    line.strip()\n", "                    for line in diff_span.after.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # add change description\n", "                if len(lines_to_add) == 1 and len(lines_to_delete) == 1:\n", "                    description += split_char if description != \"\" else \"\"\n", "                    description += mod_start\n", "                    description += to_italic(\n", "                        f\"{delim_char}{lines_to_delete[0]}{delim_char}\"\n", "                    )\n", "                    description += mod_middle\n", "                    description += to_italic(\n", "                        f\"{delim_char}{lines_to_add[0]}{delim_char}\"\n", "                    )\n", "\n", "    # check if we only added empty lines\n", "    if only_empty_lines_added:\n", "        return get_add_empty_line_description(num_lines=empty_line_add_count)\n", "\n", "    # Make sure final description is not too long\n", "    if len(description) > char_limit:\n", "        return None  # fall back to model\n", "\n", "    return description  # might make sense to do the italic conversion here, but could be overoptimization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### This is the version I will put into production for now"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class HeuristicDescriptionConfig:\n", "    delete_start = \"Delete: \"\n", "    add_start = \"Add: \"\n", "    mod_start = \"Change: \"\n", "    delim_char = \"\"\n", "    split_char = \", \"\n", "    mod_middle = \" \\u279c \"\n", "    char_limit = 40\n", "\n", "    def get_delete_lines_description(self, num_lines: int) -> str:\n", "        return \"Delete line\" if num_lines == 1 else f\"Delete {num_lines} lines\"\n", "\n", "    def get_add_empty_line_description(self, num_lines: int) -> str:\n", "        return \"Add new line\" if num_lines == 1 else f\"Add {num_lines} new lines\"\n", "\n", "    def apply_style(self, text: str) -> str:\n", "        return text\n", "\n", "\n", "def _get_heuristic_description(\n", "    hunk: <PERSON><PERSON><PERSON><PERSON>, config: HeuristicDescriptionConfig\n", ") -> Optional[str]:\n", "    if all(isinstance(diff_span, NoopSpan) for diff_span in hunk.diff_spans):\n", "        return \"\"\n", "\n", "    num_full_lines_deleted = 0  # the number of full lines deleted\n", "    is_starting_at_new_line = True  # the first span will start at a new line\n", "    only_full_lines_deleted = (\n", "        True  # we start assuming that there will only be full lines deleted\n", "    )\n", "    other_spans_exist = False  # if AddSpan or ModSpan exist, we move on to next logic\n", "    delete_description = \"\"\n", "\n", "    for diff_span in hunk.diff_spans:\n", "        match diff_span:\n", "            case NoopSpan():\n", "                # if the span ends with \\n, the next one will start on a new line\n", "                is_starting_at_new_line = diff_span.text.endswith(\"\\n\")\n", "\n", "            case DeletedSpan():\n", "                # count the number of whole lines deleted\n", "                if is_starting_at_new_line and diff_span.before.endswith(\"\\n\"):\n", "                    num_full_lines_deleted += diff_span.before.count(\"\\n\")\n", "                else:\n", "                    only_full_lines_deleted = False\n", "\n", "                # add the content to delete\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "                for line_to_delete in lines_to_delete:\n", "                    delete_description += (\n", "                        config.split_char if delete_description != \"\" else \"\"\n", "                    )\n", "                    delete_description += f\"{config.delete_start}\"\n", "                    delete_description += config.apply_style(\n", "                        f\"{config.delim_char}{line_to_delete }{config.delim_char}\"\n", "                    )\n", "\n", "            case AddedSpan():\n", "                other_spans_exist = True\n", "                break\n", "            case ModSpan():\n", "                other_spans_exist = True\n", "                break\n", "\n", "    # If everything was delete or noop, we can return here\n", "    if not other_spans_exist:\n", "        if only_full_lines_deleted:\n", "            return config.get_delete_lines_description(num_lines=num_full_lines_deleted)\n", "\n", "        if len(delete_description) > config.char_limit:\n", "            return None  # fall back to model if not full lines and too much to describe\n", "\n", "        return delete_description\n", "\n", "    # Add the things in order of the diff spans\n", "    description = \"\"\n", "    only_empty_lines_added = True\n", "    empty_line_add_count = 0\n", "\n", "    for diff_span in hunk.diff_spans:\n", "        match diff_span:\n", "            case NoopSpan():\n", "                continue\n", "            case AddedSpan():\n", "                # If only adding empty lines, count how many we are adding\n", "                if all(diff_char == \"\\n\" for diff_char in diff_span.after):\n", "                    empty_line_add_count += len(diff_span.after)\n", "                else:\n", "                    only_empty_lines_added = False\n", "\n", "                # split lines and strip whitespace\n", "                lines_to_add = [\n", "                    line.strip()\n", "                    for line in diff_span.after.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # add to description\n", "                for line_to_add in lines_to_add:\n", "                    description += config.split_char if description != \"\" else \"\"\n", "                    description += f\"{config.add_start}\"\n", "                    description += config.apply_style(\n", "                        f\"{config.delim_char}{line_to_add}{config.delim_char}\"\n", "                    )\n", "\n", "            case DeletedSpan():\n", "                only_empty_lines_added = False\n", "                # split lines and strip whitespace\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # add delete description\n", "                for line_to_delete in lines_to_delete:\n", "                    description += config.split_char if description != \"\" else \"\"\n", "                    description += f\"{config.delete_start}\"\n", "                    description += config.apply_style(\n", "                        f\"{config.delim_char}{line_to_delete}{config.delim_char}\"\n", "                    )\n", "\n", "            case ModSpan():\n", "                only_empty_lines_added = False\n", "                # split lines and strip whitespace\n", "                lines_to_add = [\n", "                    line.strip()\n", "                    for line in diff_span.after.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "                lines_to_delete = [\n", "                    line.strip()\n", "                    for line in diff_span.before.split(\"\\n\")\n", "                    if line.strip() != \"\"\n", "                ]\n", "\n", "                # add change description\n", "                if len(lines_to_add) > 0 or len(lines_to_delete) > 0:\n", "                    description += config.split_char if description != \"\" else \"\"\n", "                    description += config.mod_start\n", "                    for i, line_to_delete in enumerate(lines_to_delete):\n", "                        description += config.split_char if i > 0 else \"\"\n", "                        description += config.apply_style(\n", "                            f\"{config.delim_char}{line_to_delete}{config.delim_char}\"\n", "                        )\n", "                    description += config.mod_middle\n", "                    for i, line_to_add in enumerate(lines_to_add):\n", "                        description += config.split_char if i > 0 else \"\"\n", "                        description += config.apply_style(\n", "                            f\"{config.delim_char}{line_to_add}{config.delim_char}\"\n", "                        )\n", "\n", "                # if len(lines_to_add) == 1 and len(lines_to_delete) == 1:\n", "                #     description += config.split_char if description != \"\" else \"\"\n", "                #     description += config.mod_start\n", "                #     description += config.apply_style(\n", "                #         f\"{config.delim_char}{lines_to_delete[0]}{config.delim_char}\"\n", "                #     )\n", "                #     description += config.mod_middle\n", "                #     description += config.apply_style(\n", "                #         f\"{config.delim_char}{lines_to_add[0]}{config.delim_char}\"\n", "                #     )\n", "\n", "                else:  # not sure how to show these modspans, fall back\n", "                    return None  # fall back to model\n", "\n", "    # check if we only added empty lines\n", "    if only_empty_lines_added:\n", "        return config.get_add_empty_line_description(num_lines=empty_line_add_count)\n", "\n", "    # Make sure final description is not too long\n", "    if len(description) > config.char_limit:\n", "        return None  # fall back to model\n", "\n", "    return description  # might make sense to do the italic conversion here, but could be overoptimization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with open(\"hunks_to_describe.json\", \"r\") as f:\n", "    hunk_json = json5.load(f)\n", "\n", "hunk_list = [hunk[\"hunk\"] for hunk in hunk_json[\"ne_hunks\"]]\n", "original_description_list = [\n", "    hunk[\"original_description\"] for hunk in hunk_json[\"ne_hunks\"]\n", "]\n", "\n", "print(f\"We have: {len(hunk_list)} hunks\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def process_diff(diff_string, remove_char=\"-\", keep_char=\"+\"):\n", "    # Split the string into lines\n", "    lines = diff_string.split(\"\\n\")\n", "\n", "    # Remove the header stuff\n", "    lines = lines[4:]\n", "\n", "    # Process each line\n", "    processed_lines = []\n", "    for line in lines:\n", "        if line.startswith(remove_char):\n", "            continue  # Skip lines starting with '-'\n", "        elif line.startswith(keep_char):\n", "            line = \" \" + line[1:]  # Replace '+' with space\n", "        processed_lines.append(line)\n", "\n", "    # Join the processed lines back into a string\n", "    return \"\\n\".join(processed_lines)\n", "\n", "\n", "config = HeuristicDescriptionConfig()\n", "\n", "# Print descriptions for the hunks\n", "for i, hunk in enumerate(hunk_list):\n", "    current_code = process_diff(hunk, remove_char=\"+\", keep_char=\"-\")\n", "    replacement_code = process_diff(hunk, remove_char=\"-\", keep_char=\"+\")\n", "    result = MyResult(\n", "        existing_code=current_code,\n", "        suggested_code=replacement_code,\n", "        diff_spans=tuple(),\n", "    )\n", "\n", "    for split_result in _split_changes_into_hunks(result, n_context_lines=3):\n", "        # We only want to print if there is a heuristic description\n", "        description = _get_heuristic_description(split_result, config)\n", "        if description != \"\":\n", "            print(f\"Hunk {i}:\")\n", "            print(\"-\" * 120)\n", "            print(hunk)\n", "            print(\"-\" * 120)\n", "            print(\"split_result.diff_spans:\")\n", "            print(split_result.diff_spans)\n", "            print(\"-\" * 120)\n", "            print(f\"Original description:\\n{original_description_list[i]}\")\n", "            print(\"-\" * 120)\n", "            print(f\"Heuristic description:\\n{description}\")\n", "            print(\"=\" * 120)\n", "            print(\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# come up with some more devious test cases\n", "\n", "mod_example_list = [\n", "    # Test modifying character\n", "    {\n", "        \"name\": \"test modifying character\",\n", "        \"before\": \"]\",\n", "        \"after\": \"[\",\n", "        \"expected_description\": [\n", "            f\"{mod_start}{delim_char}]{delim_char}{mod_middle}{delim_char}[{delim_char}\"\n", "        ],\n", "    },\n", "]\n", "\n", "add_example_list = [\n", "    # Test adding a line\n", "    {\n", "        \"name\": \"test adding a line\",\n", "        \"before\": \"just a single line\",\n", "        \"after\": \"just a single line\\na much better line\",\n", "        \"expected_description\": [\n", "            f\"{add_start}{delim_char}a much better line{delim_char}\"\n", "        ],\n", "    },\n", "    # Test adding two new lines (longer than char_limit)\n", "    {\n", "        \"name\": \"test adding two new lines (longer than char_limit)\",\n", "        \"before\": \"just a single line\",\n", "        \"after\": \"just a single line\\na much better line\\nan even better line\",\n", "        \"expected_description\": [None],\n", "    },\n", "    # test adding a empty line\n", "    {\n", "        \"name\": \"test adding a empty line\",\n", "        \"before\": \"\"\"\\\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "\n", "\"\"\",\n", "        \"expected_description\": [get_add_empty_line_description(num_lines=1)],\n", "    },\n", "]\n", "\n", "delete_example_list = [\n", "    # test deleting new line\n", "    {\n", "        \"name\": \"test deleting new line\",\n", "        \"before\": \"\"\"\\\n", "\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "\"\"\",\n", "        \"expected_description\": [get_delete_lines_description(num_lines=1)],\n", "    },\n", "    # Test two deletions on same line\n", "    {\n", "        \"name\": \"Test two deletions on same line\",\n", "        \"before\": \"\"\"\\\n", "11 line 11\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "1 line 1\n", "\"\"\",\n", "        \"expected_description\": [\n", "            f\"{delete_start}{delim_char}1{delim_char}{split_char}{delete_start}{delim_char}1{delim_char}\"\n", "        ],\n", "    },\n", "    # Test deletion on two adjacent lines\n", "    {\n", "        \"name\": \"Test deletion on two adjacent lines\",\n", "        \"before\": \"\"\"\\\n", "line 11\n", "line 22\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "line 1\n", "line 2\n", "\"\"\",\n", "        \"expected_description\": [\n", "            f\"{delete_start}{delim_char}1{delim_char}{split_char}{delete_start}{delim_char}2{delim_char}\"\n", "        ],\n", "    },\n", "    # Test deletion of single character\n", "    {\n", "        \"name\": \"Test deletion of single character\",\n", "        \"before\": \"Trying to delete a single character: G\",\n", "        \"after\": \"Trying to delete a single character: \",\n", "        \"expected_description\": [f\"{delete_start}{delim_char}G{delim_char}\"],\n", "    },\n", "    # Test deletion of single long line\n", "    {\n", "        \"name\": \"Test deletion of single long line\",\n", "        \"before\": \"super long line that is definitley more than whatever we want the limit to be when we set the limit to be something\\n\",\n", "        \"after\": \"\",\n", "        \"expected_description\": [get_delete_lines_description(num_lines=1)],\n", "    },\n", "    # Test multiple lines to delete\n", "    {\n", "        \"name\": \"Test multiple lines to delete\",\n", "        \"before\": \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\",\n", "        \"after\": \"\",\n", "        \"expected_description\": [get_delete_lines_description(num_lines=6)],\n", "    },\n", "]\n", "\n", "devious_example_list = [\n", "    # Test deleting line and then adding a line less than n_context_lines away\n", "    {\n", "        \"name\": \"Test deleting line and then adding a line less than n_context_lines away\",\n", "        \"before\": \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "line 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"expected_description\": [\n", "            f\"{delete_start}{delim_char}line 1{delim_char}{split_char}{add_start}{delim_char}line 4{delim_char}\"\n", "        ],\n", "    },\n", "    # Test deleting line and then adding a line more than n_context_lines away\n", "    {\n", "        \"name\": \"Test deleting line and then adding a line more than n_context_lines away\",\n", "        \"before\": \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "line 7\n", "\"\"\",\n", "        \"expected_description\": [\n", "            get_delete_lines_description(num_lines=1),\n", "            \"\",\n", "            f\"{add_start}{delim_char}line 7{delim_char}\",\n", "        ],\n", "    },\n", "    # Test deleting first line, then modifying the second and third, and then adding line 7\n", "    {\n", "        \"name\": \"Test deleting first line, then modifying the second and third, and then adding line 7\",\n", "        \"before\": \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "line 6\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "pine 2\n", "pine 3\n", "line 4\n", "line 5\n", "line 6\n", "line 7\n", "\"\"\",\n", "        \"expected_description\": [\n", "            None,\n", "            \"\",\n", "            f\"{add_start}{delim_char}line 7{delim_char}\",\n", "        ],\n", "    },\n", "    # Test change within a word on a single line\n", "    {\n", "        \"name\": \"Test change within a word on a single line\",\n", "        \"before\": \"___________________\",\n", "        \"after\": \"_+__+__+___________\",\n", "        \"expected_description\": [\n", "            f\"{add_start}{delim_char}_+__+__+{delim_char}{split_char}{delete_start}{delim_char}________{delim_char}\"\n", "        ],\n", "    },\n", "    # test surrounding text with brackets\n", "    {\n", "        \"name\": \"test surrounding text with brackets\",\n", "        \"before\": \"before I was a list\",\n", "        \"after\": \"[before I was a list]\",\n", "        \"expected_description\": [\n", "            f\"{add_start}{delim_char}[{delim_char}{split_char}{add_start}{delim_char}]{delim_char}\"\n", "        ],\n", "    },\n", "    # test changing a line and then adding another line\n", "    {\n", "        \"name\": \"test changing a line and then adding another line\",\n", "        \"before\": \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "line 1\n", "pine 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"expected_description\": [\n", "            \"\",\n", "            f\"{mod_start}{delim_char}l{delim_char}{mod_middle}{delim_char}p{delim_char}{split_char}{add_start}{delim_char}line 4{delim_char}\"\n", "            if len(\n", "                f\"{mod_start}{delim_char}l{delim_char}{mod_middle}{delim_char}p{delim_char}{split_char}{add_start}{delim_char}line 4{delim_char}\"\n", "            )\n", "            < char_limit\n", "            else None,\n", "        ],\n", "    },\n", "    {\n", "        \"before\": \"need a clue\"\n", "        * 10,  # make change larger to avoid heuristic description\n", "        \"after\": \"assert a['clue'] ==\\n\"\n", "        * 10,  # make change larger to avoid heuristic description\n", "        \"expected_description\": [\"\"],\n", "    },\n", "    {\n", "        \"before\": \"uses a clue\\n\"\n", "        * 100,  # make change larger to avoid heuristic description\n", "        \"after\": \"foo(a['clue'])\",  # make change larger to avoid heuristic description\n", "        \"expected_description\": [\"\"],\n", "    },\n", "]\n", "\n", "for example_list in [\n", "    mod_example_list,\n", "    add_example_list,\n", "    delete_example_list,\n", "    devious_example_list,\n", "]:\n", "    for i, example in enumerate(example_list):\n", "        result = MyResult(\n", "            existing_code=example[\"before\"],\n", "            suggested_code=example[\"after\"],\n", "            diff_spans=tuple(),\n", "        )\n", "\n", "        for j, split_result in enumerate(\n", "            _split_changes_into_hunks(result, n_context_lines=3)\n", "        ):\n", "            # We only want to print if there is a heuristic description\n", "            description = _get_heuristic_description(split_result, config)\n", "            print(f\"Example {i}.{j}:\")\n", "            print(\"existing:\")\n", "            print(split_result.existing_code)\n", "            print(\"suggested:\")\n", "            print(split_result.suggested_code)\n", "            print(\"-\" * 120)\n", "            print(split_result.diff_spans)\n", "            print(\"-\" * 120)\n", "            print(f\"Heuristic description:\\n{description}\")\n", "            # print(f\"Expected description:\\n{example['expected_description'][j]}\")\n", "            print(\"=\" * 120)\n", "            print(\"\\n\")\n", "            # assert description == example[\"expected_description\"][j]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Try to use Italics for the description\n", "# def to_italic(text):\n", "#     def get_italic_char(char):\n", "#         code = ord(char)\n", "#         if 65 <= code <= 90:  # Uppercase letters\n", "#             return chr(code + 119808)\n", "#         elif 97 <= code <= 122:  # Lowercase letters\n", "#             return chr(code + 119886)\n", "#         elif 48 <= code <= 57:  # Numbers\n", "#             return chr(code + 120734)\n", "#         elif char in \"!?()+-=:;\":\n", "#             return chr(ord(\"!\") + 119878 + \"!?()+-=:;\".index(char))\n", "#         else:\n", "#             return char\n", "\n", "#     return \"\".join(map(get_italic_char, text))\n", "\n", "\n", "# def to_italic(text):\n", "#     italic_chars = {\n", "#         'A': '\\u1D608', 'B': '\\u1D609', 'C': '\\u1D60A', 'D': '\\u1D60B', 'E': '\\u1D60C', 'F': '\\u1D60D', 'G': '\\u1D60E', 'H': '\\u1D60F', 'I': '\\u1D610', 'J': '\\u1D611',\n", "#         'K': '\\u1D612', 'L': '\\u1D613', 'M': '\\u1D614', 'N': '\\u1D615', 'O': '\\u1D616', 'P': '\\u1D617', 'Q': '\\u1D618', 'R': '\\u1D619', 'S': '\\u1D61A', 'T': '\\u1D61B',\n", "#         'U': '\\u1D61C', 'V': '\\u1D61D', 'W': '\\u1D61E', 'X': '\\u1D61F', 'Y': '\\u1D620', 'Z': '\\u1D621',\n", "#         'a': '\\u1D622', 'b': '\\u1D623', 'c': '\\u1D624', 'd': '\\u1D625', 'e': '\\u1D626', 'f': '\\u1D627', 'g': '\\u1D628', 'h': '\\u1D629', 'i': '\\u1D62A', 'j': '\\u1D62B',\n", "#         'k': '\\u1D62C', 'l': '\\u1D62D', 'm': '\\u1D62E', 'n': '\\u1D62F', 'o': '\\u1D630', 'p': '\\u1D631', 'q': '\\u1D632', 'r': '\\u1D633', 's': '\\u1D634', 't': '\\u1D635',\n", "#         'u': '\\u1D636', 'v': '\\u1D637', 'w': '\\u1D638', 'x': '\\u1D639', 'y': '\\u1D63A', 'z': '\\u1D63B',\n", "#         '0': '\\u1D7CE', '1': '\\u1D7CF', '2': '\\u1D7D0', '3': '\\u1D7D1', '4': '\\u1D7D2', '5': '\\u1D7D3', '6': '\\u1D7D4', '7': '\\u1D7D5', '8': '\\u1D7D6', '9': '\\u1D7D7',\n", "#         '!': '\\u1D6C5', '?': '\\u1D6CC', '(': '\\u1D6E4', ')': '\\u1D6E5', '+': '\\u1D6DC', '-': '\\u1D6DD', '=': '\\u1D6DE', ':': '\\u1D6E2', ';': '\\u1D6E3'\n", "#     }\n", "\n", "#     return ''.join(italic_chars.get(char, char) for char in text)\n", "\n", "\n", "# def to_italic(text):\n", "#     italic_chars = {\n", "#         \"A\": \"𝘈\",\n", "#         \"B\": \"𝘉\",\n", "#         \"C\": \"𝘊\",\n", "#         \"D\": \"𝘋\",\n", "#         \"E\": \"𝘌\",\n", "#         \"F\": \"𝘍\",\n", "#         \"G\": \"𝘎\",\n", "#         \"H\": \"𝘏\",\n", "#         \"I\": \"𝘐\",\n", "#         \"J\": \"𝘑\",\n", "#         \"K\": \"𝘒\",\n", "#         \"L\": \"𝘓\",\n", "#         \"M\": \"𝘔\",\n", "#         \"N\": \"𝘕\",\n", "#         \"O\": \"𝘖\",\n", "#         \"P\": \"𝘗\",\n", "#         \"Q\": \"𝘘\",\n", "#         \"R\": \"𝘙\",\n", "#         \"S\": \"𝘚\",\n", "#         \"T\": \"𝘛\",\n", "#         \"U\": \"𝘜\",\n", "#         \"V\": \"𝘝\",\n", "#         \"W\": \"𝘞\",\n", "#         \"X\": \"𝘟\",\n", "#         \"Y\": \"𝘠\",\n", "#         \"Z\": \"𝘡\",\n", "#         \"a\": \"𝘢\",\n", "#         \"b\": \"𝘣\",\n", "#         \"c\": \"𝘤\",\n", "#         \"d\": \"𝘥\",\n", "#         \"e\": \"𝘦\",\n", "#         \"f\": \"𝘧\",\n", "#         \"g\": \"𝘨\",\n", "#         \"h\": \"𝘩\",\n", "#         \"i\": \"𝘪\",\n", "#         \"j\": \"𝘫\",\n", "#         \"k\": \"𝘬\",\n", "#         \"l\": \"𝘭\",\n", "#         \"m\": \"𝘮\",\n", "#         \"n\": \"𝘯\",\n", "#         \"o\": \"𝘰\",\n", "#         \"p\": \"𝘱\",\n", "#         \"q\": \"𝘲\",\n", "#         \"r\": \"𝘳\",\n", "#         \"s\": \"𝘴\",\n", "#         \"t\": \"𝘵\",\n", "#         \"u\": \"𝘶\",\n", "#         \"v\": \"𝘷\",\n", "#         \"w\": \"𝘸\",\n", "#         \"x\": \"𝘹\",\n", "#         \"y\": \"𝘺\",\n", "#         \"z\": \"𝘻\",\n", "#     }\n", "\n", "#     return \"\".join(italic_chars.get(char, char) for char in text)\n", "\n", "\n", "# def to_italic(text):\n", "#     italic_chars = {\n", "#         \"A\": \"𝘈\",\n", "#         \"B\": \"𝘉\",\n", "#         \"C\": \"𝘊\",\n", "#         \"D\": \"𝘋\",\n", "#         \"E\": \"𝘌\",\n", "#         \"F\": \"𝘍\",\n", "#         \"G\": \"𝘎\",\n", "#         \"H\": \"𝘏\",\n", "#         \"I\": \"𝘐\",\n", "#         \"J\": \"𝘑\",\n", "#         \"K\": \"𝘒\",\n", "#         \"L\": \"𝘓\",\n", "#         \"M\": \"𝘔\",\n", "#         \"N\": \"𝘕\",\n", "#         \"O\": \"𝘖\",\n", "#         \"P\": \"𝘗\",\n", "#         \"Q\": \"𝘘\",\n", "#         \"R\": \"𝘙\",\n", "#         \"S\": \"𝘚\",\n", "#         \"T\": \"𝘛\",\n", "#         \"U\": \"𝘜\",\n", "#         \"V\": \"𝘝\",\n", "#         \"W\": \"𝘞\",\n", "#         \"X\": \"𝘟\",\n", "#         \"Y\": \"𝘠\",\n", "#         \"Z\": \"𝘡\",\n", "#         \"a\": \"𝘢\",\n", "#         \"b\": \"𝘣\",\n", "#         \"c\": \"𝘤\",\n", "#         \"d\": \"𝘥\",\n", "#         \"e\": \"𝘦\",\n", "#         \"f\": \"𝘧\",\n", "#         \"g\": \"𝘨\",\n", "#         \"h\": \"𝘩\",\n", "#         \"i\": \"𝘪\",\n", "#         \"j\": \"𝘫\",\n", "#         \"k\": \"𝘬\",\n", "#         \"l\": \"𝘭\",\n", "#         \"m\": \"𝘮\",\n", "#         \"n\": \"𝘯\",\n", "#         \"o\": \"𝘰\",\n", "#         \"p\": \"𝘱\",\n", "#         \"q\": \"𝘲\",\n", "#         \"r\": \"𝘳\",\n", "#         \"s\": \"𝘴\",\n", "#         \"t\": \"𝘵\",\n", "#         \"u\": \"𝘶\",\n", "#         \"v\": \"𝘷\",\n", "#         \"w\": \"𝘸\",\n", "#         \"x\": \"𝘹\",\n", "#         \"y\": \"𝘺\",\n", "#         \"z\": \"𝘻\",\n", "#         \"0\": \"𝟬\",\n", "#         \"1\": \"𝟭\",\n", "#         \"2\": \"𝟮\",\n", "#         \"3\": \"𝟯\",\n", "#         \"4\": \"𝟰\",\n", "#         \"5\": \"𝟱\",\n", "#         \"6\": \"𝟲\",\n", "#         \"7\": \"𝟳\",\n", "#         \"8\": \"𝟴\",\n", "#         \"9\": \"𝟵\",\n", "#         \"(\": \"(\",\n", "#         \")\": \")\",\n", "#         \"+\": \"+\",\n", "#         \"-\": \"−\",\n", "#         \"=\": \"=\",\n", "#         \":\": \":\",\n", "#         \";\": \";\",\n", "#     }\n", "\n", "#     return \"\".join(italic_chars.get(char, char) for char in text)\n", "\n", "\n", "print(to_italic(\"def to_string(): -> str:\\n 1 + 234 = 987 - 123; \"))\n", "\n", "# CAn I print it:\n", "print(\"𝘏𝘦𝘭𝘭𝘰 𝘵𝘦𝘹𝘵 𝘴𝘵𝘳𝘪𝘯𝘨 𝘮𝘺 𝘰𝘭𝘥 𝘧𝘳𝘪𝘦𝘯𝘥.\")\n", "\n", "print(\"\\u1d609\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Examine Hunk 4 in more detail\n", "hunk = hunk_list[4]\n", "\n", "current_code = process_diff(hunk, remove_char=\"+\", keep_char=\"-\")\n", "replacement_code = process_diff(hunk, remove_char=\"-\", keep_char=\"+\")\n", "result = MyResult(\n", "    existing_code=current_code,\n", "    suggested_code=replacement_code,\n", "    diff_spans=tuple(),\n", ")\n", "\n", "for split_result in _split_changes_into_hunks(result, n_context_lines=3):\n", "    # We only want to print if there is a heuristic description\n", "    for span in split_result.diff_spans:\n", "        if isinstance(span, DeletedSpan):\n", "            print(\"Deleted:\", span.deleted)\n", "        elif isinstance(span, AddedSpan):\n", "            print(\"Added:\", span.inserted)\n", "        elif isinstance(span, ModSpan):\n", "            print(\"Added:\", span.before, span.after)\n", "        else:\n", "            print(\"No change\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Examin the properties of cutting function\n", "\n", "\n", "def cut_before_non_alphanumeric(s: str) -> str:\n", "    match = re.search(r\"[^a-zA-Z0-9]\", s)\n", "    if match:\n", "        return s[: match.start()]\n", "    return s\n", "\n", "\n", "# Examples\n", "print(cut_before_non_alphanumeric(\"Hello123World!\"))  # Output: Hello123World\n", "print(cut_before_non_alphanumeric(\"abc_def-ghi\"))  # Output: abc\n", "print(cut_before_non_alphanumeric(\"123A.BC\"))  # Output: 123ABC\n", "print(cut_before_non_alphanumeric(\"No\\nSpecialChars\"))  # Output: NoSpecialChars\n", "print(cut_before_non_alphanumeric(\"With Space\"))  # Output: With\n", "print(cut_before_non_alphanumeric(\"Under_score\"))  # Output: Under"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def ends_with_alphanumeric(s: str) -> bool:\n", "#     return bool(re.search(r'[a-zA-Z0-9]$', s))\n", "\n", "\n", "def ends_with_alphanumeric(s: str) -> bool:\n", "    # Check if the last character is alphanumeric\n", "    if s == \"\":\n", "        return False\n", "\n", "    return s[-1].isalnum()\n", "\n", "\n", "# Examples\n", "print(ends_with_alphanumeric(\"Hello123\"))  # Output: True\n", "print(ends_with_alphanumeric(\"HelloWorld\\t\"))  # Output: True\n", "print(ends_with_alphanumeric(\"123\"))  # Output: True\n", "print(ends_with_alphanumeric(\"Hello!\\t\"))  # Output: False\n", "print(ends_with_alphanumeric(\"123_\"))  # Output: False\n", "print(ends_with_alphanumeric(\"\"))  # Output: False\n", "print(ends_with_alphanumeric(\"Hello World\\n\"))  # Output: False\n", "print(ends_with_alphanumeric(\"Newline\\n\"))  # Output: False\n", "print(ends_with_alphanumeric(\"Ends with space \"))  # Output: False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["list1 = [-1, 0, 1, 2, 3, 4, 5]\n", "list2 = [0, 1, 2, 3, 4, 5]\n", "list3 = [1, 2, 3, 4, 5]\n", "\n", "for i, (a, b, c) in enumerate(zip(list1, list2, list3)):\n", "    print(i, a, b, c)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_string_before_non_alphanumeric(s: str) -> str:\n", "    match = re.search(r\"[^a-zA-Z0-9]\", s)\n", "    return s[: match.start()] if match else s\n", "\n", "\n", "print(\"E\"[:-1])\n", "\n", "print(\"e\"[1:])\n", "\n", "print(\"A\"[:-1])\n", "\n", "print(get_string_before_non_alphanumeric(\"A\"))  # Output: Hello123World"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example_list = [\"-1\", \"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"+1\"]\n", "list_slice = example_list[1:-1]\n", "list_slice[0] = \"10\"\n", "print(list_slice)\n", "print(example_list)\n", "# print(list_slice is example_list)\n", "for prev, current, next in zip(\n", "    range(0, len(example_list)),\n", "    range(1, len(example_list)),\n", "    range(2, len(example_list)),\n", "):\n", "    print(prev, current, next)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-1 0 e\n", "0 e e\n", "e e e\n", "e e e\n", "e e e\n", "e e e\n", "Match found!\n", "<re.Match object; span=(0, 0), match=''>\n", "\n", "'\\n'\n"]}], "source": ["for i, (prev, current, next) in enumerate(\n", "    zip(example_list, example_list[1:], example_list[2:])\n", "):\n", "    print(prev, current, next)\n", "    example_list[i + 2] = \"e\"\n", "\n", "\n", "s = \"eIs d\"\n", "s = \" dumb \"\n", "s = \"eIs\"\n", "s = \"\\n\"\n", "\n", "match = re.match(r\"[a-zA-Z0-9]*\", s)\n", "if match:\n", "    print(\"Match found!\")\n", "    print(match)\n", "    print(s[: match.end()])\n", "else:\n", "    print(\"No match found!\")\n", "\n", "# modspan_list = [ModSpan(before='?\\tc? a?', after='\\t♺b\\n\\ta😃c😃'), NoopSpan('\\n'), ModSpan(before='\\n', after='♺')]\n", "\n", "newline = \"\\n\"\n", "print(repr(newline.removesuffix(\"\")))\n", "\n", "\n", "# problem: when the prefix is 0 characters"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def process_diff(diff_string, remove_char=\"-\", keep_char=\"+\"):\n", "#     # Split the string into lines\n", "#     lines = diff_string.split(\"\\n\")\n", "\n", "#     # Process each line\n", "#     processed_lines = []\n", "#     for line in lines:\n", "#         if line.startswith(remove_char):\n", "#             continue  # Skip lines starting with '-'\n", "#         elif line.startswith(keep_char):\n", "#             line = \" \" + line[1:]  # Replace '+' with space\n", "#         processed_lines.append(line)\n", "\n", "#     # Join the processed lines back into a string\n", "#     return \"\\n\".join(processed_lines)\n", "\n", "\n", "# current_code = process_diff(hunk_list[0], remove_char=\"+\", keep_char=\"-\")\n", "# replacement_code = process_diff(hunk_list[0], remove_char=\"-\", keep_char=\"+\")\n", "\n", "\n", "# # Print descriptions for the hunks\n", "# for i, hunk in enumerate(hunk_list):\n", "#     result = MyResult(\n", "#         existing_code=hunk[\"before\"],\n", "#         suggested_code=hunk[\"after\"],\n", "#         diff_spans=hunk[\"diff_spans\"],\n", "#     )\n", "\n", "#     print(f\"Hunk {i}:\")\n", "#     print(hunk)\n", "#     print(\"-\" * 120)\n", "#     print(f\"Original description: {original_description_list[i]}\")\n", "#     print(\"-\" * 120)\n", "#     print(heuristic_description(hunk))\n", "#     print(\"=\" * 120)\n", "#     print(\"\\n\")\n", "\n", "\n", "# print(\"Current code:\\n\")\n", "# print(current_code)\n", "# print(\"Replacement code:\\n\")\n", "# print(replacement_code)\n", "\n", "# print(\"=\" * 120)\n", "\n", "# example_hunk = NextEditResult(\n", "#     suggestion_id=\"\",\n", "#     path=\"example.py\",\n", "#     blob_name=\"\",\n", "#     char_start=0,\n", "#     char_end=len(current_code),\n", "#     existing_code=current_code,\n", "#     suggested_code=replacement_code,\n", "#     truncation_char=None,\n", "#     change_description=\"\",\n", "#     diff_spans=[],  # this is empty until the _split_changes fills it int\n", "# )\n", "\n", "# for i, result in enumerate(\n", "#     _split_changes_into_hunks(change=example_result, n_context_lines=3)\n", "# ):\n", "#     print(i)\n", "#     print(\"existing:\")\n", "#     print(result.existing_code)\n", "#     print(\"suggested:\")\n", "#     print(result.suggested_code)\n", "#     print(\"char_start:\", result.char_start, \"char_end:\", result.char_end)\n", "#     print(\"-\" * 120)\n", "#     for span in result.diff_spans:\n", "#         print(type(span))\n", "#         if isinstance(span, DeletedSpan):\n", "#             print(\"Deleted:\", span.deleted)\n", "#         elif isinstance(span, AddedSpan):\n", "#             print(\"Added:\", span.inserted)\n", "#         elif isinstance(span, ModSpan):\n", "#             print(\"Added:\", span.before, span.after)\n", "#         else:\n", "#             print(\"No change\")\n", "#     print(\"-\" * 120)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}