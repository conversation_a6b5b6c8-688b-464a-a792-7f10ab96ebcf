{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Generate descriptions for generated next-edit hunks."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "\n", "df = pandas.read_parquet(\n", "    \"/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions/part-00270-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet.partial\"\n", ")\n", "\n", "# iterate over pandas series\n", "# for desc in df[\"descriptions\"][0]:\n", "#     print(desc)\n", "\n", "for desc, diff in zip(\n", "    list(df[\"descriptions\"][0]), list(df[\"description_diff_inputs\"][0])\n", "):\n", "    print(desc)\n", "    print(diff)\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import pickle\n", "from pathlib import Path\n", "\n", "from base.diff_utils.diff_utils import File, compute_file_diff\n", "from research.next_edits.edit_gen_sampler import EditGenProblem\n", "from research.llm_apis.chat_utils import Llama3ChatClient\n", "\n", "\n", "def compute_suggested_edit_diff(problem: EditGenProblem) -> str:\n", "    current_code = problem.current_code\n", "    new_code = problem.prefix + problem.output.replacement + problem.suffix\n", "    diff = compute_file_diff(\n", "        before_file=File(path=str(problem.current_path), contents=current_code),\n", "        after_file=File(path=str(problem.current_path), contents=new_code),\n", "        use_smart_header=True,\n", "        num_context_lines=50,\n", "    )\n", "    return diff\n", "\n", "\n", "client = Llama3ChatClient(\"triton\", address=\"*************:8000\", timeout=180)\n", "\n", "# system_prompt = \"\"\"\\\n", "# You are a developer working in my codebase.\n", "# Your task is to extract relevant information from the code change below \\\n", "# and describe it using imperative verb form. The purpose of your description is \\\n", "# to help other developers quickly understand key aspects of the change in the context \\\n", "# of the codebase.\"\"\"\n", "\n", "good_examples = [\n", "    {\n", "        \"diff\": \"\"\"\\\n", "--- .github/workflows/ci.yml\n", "+++ .github/workflows/ci.yml\n", "@@ -1,30 +1,30 @@\n", " name: CI\n", " on:\n", "   pull_request:\n", " jobs:\n", "   ciJvms:\n", "     runs-on: ubuntu-20.04\n", "     strategy:\n", "       fail-fast: false\n", "       matrix:\n", "-        java: [ '1.8', '11',\n", "+        java: [ '1.8', '11', '17' ]\n", "     steps:\n", "       - uses: actions/checkout@v2.4.0\n", "         with:\n", "           fetch-depth: 100\n", "       - name: <PERSON>tch tags\n", "         run: git fetch --depth=100 origin +refs/tags/*:refs/tags/*\n", "       - uses: olafurpg/setup-scala@v13\n", "         with:\n", "           java-version: ${{ matrix.java }}\n", "       - name: <PERSON><PERSON><PERSON> <PERSON>\n", "         uses: coursier/cache-action@v6\n", "       - name: sbt ci ${{ github.ref }}\n", "         run: ./sbt ci\n", " \n", "   ci:\n", "     runs-on: ubuntu-20.04\n", "     needs: [ ciJvms ]\n", "     steps:\n", "       - name: Aggregate of lint, and all tests\n", "         run: echo \"ci passed\"\n", "\"\"\",\n", "        \"description\": \"Add Java 17 to the list of supported Java versions.\",\n", "    },\n", "    {\n", "        \"diff\": \"\"\"\\\n", "--- project/Dependencies.scala\n", "+++ project/Dependencies.scala\n", "@@ -1,38 +1,40 @@\n", " import sbt._\n", " \n", " object Dependencies {\n", " \n", "   object Versions {\n", " \n", "     val betterMonadicFor = \"0.3.1\"\n", "     val kindProjector = \"0.13.2\"\n", "+    val missinglink = \"0.2.5\"\n", "     val organizeImports = \"0.6.0\"\n", "     val sbtBuildinfo = \"0.10.0\"\n", "     val sbtDynver = \"4.1.1\"\n", "     val sbtMima = \"1.0.1\"\n", "     val sbtMissinglink = \"0.3.2\"\n", "     val sbtRewarn = \"0.1.3\"\n", "     val sbtScalafmt = \"2.4.4\"\n", "     val sbtScalafix = \"0.9.32\"\n", "     val sbtTpolecat = \"0.1.20\"\n", "     val scaluzzi = \"0.1.20\"\n", "     val silencer = \"1.7.7\"\n", " \n", "   }\n", " \n", "   val betterMonadicFor = \"com.olegpy\" %% \"better-monadic-for\" % Versions.betterMonadicFor\n", "   val kindProjector = \"org.typelevel\" %% \"kind-projector\" % Versions.kindProjector cross CrossVersion.full\n", "-  val missingl  val organizeImports = \"com.github.liancheng\" %% \"organize-imports\" % Versions.organizeImports\n", "+  val missinglink = \"com.spotify\" % \"missinglink-core\" % Versions.missinglink\n", "+  val organizeImports = \"com.github.liancheng\" %% \"organize-imports\" % Versions.organizeImports\n", "   val sbtBuildinfo = \"com.eed3si9n\" % \"sbt-buildinfo\" % Versions.sbtBuildinfo\n", "   val sbtDynver = \"com.dwijnand\" % \"sbt-dynver\" % Versions.sbtDynver\n", "   val sbtMima = \"com.typesafe\" % \"sbt-mima-plugin\" % Versions.sbtMima\n", "   val sbtMissinglink = \"ch.epfl.scala\" % \"sbt-missinglink\" % Versions.sbtMissinglink\n", "   val sbtRewarn = \"com.timushev.sbt\" % \"sbt-rewarn\" % Versions.sbtRewarn\n", "   val sbtScalafmt = \"org.scalameta\" % \"sbt-scalafmt\" % Versions.sbtScalafmt\n", "   val sbtScalafix = \"ch.epfl.scala\" % \"sbt-scalafix\" % Versions.sbtScalafix\n", "   val sbtTpolecat = \"io.github.davidgregory084\" % \"sbt-tpolecat\" % Versions.sbtTpolecat\n", "   val scaluzzi = \"com.github.vovapolu\" %% \"scaluzzi\" % Versions.scaluzzi\n", "   val silencer = \"com.github.ghik\" % \"silencer-plugin\" % Versions.silencer cross CrossVersion.full\n", "   val silencerLib = \"com.github.ghik\" % \"silencer-lib\" % Versions.silencer % Provided cross CrossVersion.full\n", " \n", " }\n", "\"\"\",\n", "        \"description\": \"Add missinglink dependency and update organizeImports.\",\n", "    },\n", "]\n", "\n", "prompt_template_new = \"\"\"\\\n", "\"\"\"\n", "\n", "prompt_template = \"\"\"\\\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below \\\n", "and describe it using imperative verb form. The purpose of your description is \\\n", "to help other developers quickly understand key aspects of the change in the context \\\n", "of the codebase.\n", "\n", "Directly start the response with the description and say nothing else.\n", "Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "\n", "Code change:\n", "```\n", "{diff_str}```\n", "\"\"\"\n", "\n", "dataset_path = Path(\n", "    \"/mnt/efs/spark-data/shared/next-edit/stage1/prv2-pr_grouped_10k/S24_10000p\"\n", ")\n", "parquet_files = list(dataset_path.glob(\"*.parquet\"))\n", "\n", "df = pandas.read_parquet(parquet_files[0])\n", "print(\"Columns in the Parquet file:\")\n", "for column in df.columns:\n", "    print(f\"- {column}\")\n", "\n", "for row in df.to_dict(orient=\"records\"):\n", "    data = pickle.loads(row[\"pickled_results\"])\n", "\n", "    for change in data:\n", "        # print(change.commit_meta.message)\n", "        # print(change.repo_change)\n", "        # print(change.selected_code)\n", "        # print(change.output.replacement)\n", "        # print()\n", "\n", "        diff = compute_suggested_edit_diff(change)\n", "\n", "        if not diff.strip():\n", "            print(\"Empty diff, skipping\")\n", "            continue\n", "\n", "        # print(\"Diff:\")\n", "        # print(diff)\n", "        # print()\n", "\n", "        prompt = prompt_template.format(diff_str=diff)\n", "\n", "        print(\"Prompt:\")\n", "        print(prompt)\n", "        print()\n", "\n", "        response = client.generate(messages=[prompt], max_tokens=256)\n", "        print(\"Generated description:\", response)\n", "        print()\n", "\n", "        print(\"=\" * 128)\n", "\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "from pathlib import Path\n", "\n", "dataset_path = Path(\n", "    \"/mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions/part-09512-18a737dc-7c25-44d0-aa7c-6674b146ea3a-c000.zstd.parquet\"\n", ")\n", "\n", "training_prompt_template = \"\"\"\\\n", "<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n", "You are a developer working in my codebase.\n", "Your task is to extract relevant information from the code change below \\\n", "and describe it using imperative verb form. The purpose of your description is \\\n", "to help other developers quickly understand key aspects of the change in the context \\\n", "of the codebase.\n", "\n", "Directly start the response with the description and say nothing else.\n", "Write no more than 10 words.\n", "Focus on the fields that are changing but don't include where they are.\n", "\n", "Code change:\n", "```\n", "{diff_str}```\n", "<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\"\"\"\n", "\n", "training_label_template = \"{description}<|eot_id|>\"\n", "\n", "df = pandas.read_parquet(dataset_path)\n", "\n", "# print(df.columns)\n", "\n", "for desc, diff in zip(\n", "    list(df[\"descriptions\"][0]), list(df[\"description_diff_inputs\"][0])\n", "):\n", "    prompt = training_prompt_template.format(diff_str=diff)\n", "    label = training_label_template.format(description=desc)\n", "\n", "    if not desc:\n", "        continue\n", "\n", "    print(prompt + label)\n", "    print()\n", "    break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import create_tokenizer_by_name\n", "from base.prompt_format_next_edit.description_prompt_formatter import (\n", "    RavenDescribePromptFormatter,\n", ")\n", "from base.prompt_format_chat import get_struct_to_tokens_prompt_formatter_by_name\n", "\n", "# Taken from: services/deploy/raven_edit_v2_15b_deploy.jsonnet\n", "tokenizer_name = \"llama3_instruct\"\n", "prompt_formatter_config = RavenDescribePromptFormatter.Config()\n", "chat_prompt_formatter_name = \"llama3\"\n", "\n", "description_tokenizer = create_tokenizer_by_name(tokenizer_name)\n", "description_prompt_formatter = RavenDescribePromptFormatter(\n", "    description_tokenizer,\n", "    config=prompt_formatter_config,\n", ")\n", "chat_prompt_formatter = get_struct_to_tokens_prompt_formatter_by_name(\n", "    chat_prompt_formatter_name,\n", "    description_tokenizer,\n", ")\n", "\n", "structured_prompt = description_prompt_formatter.format_input_from_diff_str(\n", "    \"+ hello\\n- goodbye\\n\"\n", ")\n", "tokenized_prompt = chat_prompt_formatter.format_prompt(structured_prompt)\n", "\n", "# print(structured_prompt)\n", "\n", "print(description_tokenizer.detokenize(tokenized_prompt.tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "\n", "# max_workers = 360\n", "max_workers = 5\n", "\n", "spark = k8s_session(\n", "    name=\"foo\",\n", "    efs_path=Path(\"/mnt/efs/spark-data\"),\n", "    conf={\n", "        \"spark.executor.pyspark.memory\": \"1050g\",\n", "        \"spark.task.cpus\": \"5\",\n", "    },\n", "    max_workers=max_workers,\n", "    skip_bazel_build=False,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}