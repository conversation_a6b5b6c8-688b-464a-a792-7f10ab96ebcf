determined:
  name: "llama3-8b-instruct-next-edit-descriptions-v0-instructfix-lr2e6"
  description: null
  workspace: Dev
  project: guy

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 16
  # gpu_count: 32
  # gpu_count: 64
  # gpu_count: 128
  project_group: "finetuning"

fastbackward_configs:
  - configs/llama3_8b.py

fastbackward_args:
  block_size: 4096
  decay_lr: True
  weight_decay: 0.1
  warmup_iters: 0
  train_data_path: /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_indexed_dataset_seqlen4097/dataset
  eval_data_path: /mnt/efs/spark-data/user/guy/data-pipeline/next_edit_descriptions_indexed_dataset_seqlen4097/validation_dataset
  model_vocab_size: 128256

  # learning_rate: 5e-6
  learning_rate: 2e-6
  min_lr: 1e-6
  batch_size: 4
  gradient_accumulation_steps: 1

  wandb_project: llama3-next-edit
  run_name: llama3-8b-instruct-next-edit-descriptions-v0-instructfix-lr2e6
  # checkpoint_dir: /mnt/efs/augment/checkpoints/llama3.1/fbw/Meta-Llama-3.1-8B-mp1
  checkpoint_dir: /mnt/efs/augment/checkpoints/llama3.1/fb/Meta-Llama-3.1-8B-Instruct

  ## Want these
  loss_mask_policy: negative_tokens
  visualize_logits_samples: 8
  # tokenizer_name: llama3_base
  tokenizer_name: llama3_instruct
  use_research_tokenizer: false
  checkpoint_optimizer_state: false
  log_interval: 100

  # max_iters: 8000
  # max_iters: 16000
  # max_iters: 32000
  max_iters: 64000

  max_epochs: 0
  eval_interval: 500
  eval_items: 12800
