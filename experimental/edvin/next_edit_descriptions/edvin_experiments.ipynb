{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from typing import Sequence\n", "from base.diff_utils.str_diff import (\n", "    <PERSON>ff<PERSON><PERSON>,\n", "    align_spans_to_word_boundaries,\n", "    combine_spans_on_same_line,\n", "    precise_char_diff,\n", "    precise_line_diff,\n", ")\n", "\n", "\n", "def process_change(before: str, after: str) -> list[Sequence[DiffSpan]]:\n", "    line_level_hunks = precise_line_diff(before, after).group_into_hunks(2)\n", "\n", "    combined_spans = []\n", "\n", "    for hunk, _ in zip(line_level_hunks.spans, line_level_hunks.span_ranges_in_before):\n", "        spans = precise_char_diff(hunk.before, hunk.after).spans\n", "        # print(\"spans\")\n", "        # print(spans)\n", "        aligned_spans = align_spans_to_word_boundaries(spans)\n", "        # print(\"aligned spans\")\n", "        # print(aligned_spans)\n", "        combined_span_list = combine_spans_on_same_line(aligned_spans)\n", "        combined_spans.append(combined_span_list)\n", "\n", "    return combined_spans"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["change['before']='line 1\\nline2\\nline3'\n", "change['after']='line 1\\nline3'\n", "[NoopSpan('line 1\\n')]\n", "[DeletedSpan('line2\\n')]\n", "[NoopSpan('line3')]\n", "change['before']='line 1\\nline2\\nline3'\n", "change['after']='line 1\\nline22\\nline3'\n", "[NoopSpan('line 1\\n')]\n", "[NoopSpan(''), ModSpan(before='line2', after='line22'), NoopSpan('\\n')]\n", "[NoopSpan('line3')]\n", "change['before']='\\nline1\\nline2\\nline3\\n'\n", "change['after']='\\nline1\\nline2 extra\\nline3\\n'\n", "[NoopSpan('\\nline1\\n')]\n", "[NoopSpan('line2'), AddedSpan(' extra'), NoopSpan('\\n')]\n", "[NoopSpan('line3\\n')]\n", "change['before']='\\nasdf\\n'\n", "change['after']='\\na s d f\\n'\n", "[NoopSpan('\\n')]\n", "[NoopSpan(''), ModSpan(before='asdf', after='a s d f'), NoopSpan('\\n')]\n"]}], "source": ["change_list = [\n", "    {\n", "        \"name\": \"middle line deletion\",\n", "        \"before\": \"line 1\\nline2\\nline3\",\n", "        \"after\": \"line 1\\nline3\",\n", "    },\n", "    {\n", "        \"name\": \"middle line deletion\",\n", "        \"before\": \"line 1\\nline2\\nline3\",\n", "        \"after\": \"line 1\\nline22\\nline3\",\n", "    },\n", "    {\n", "        \"name\": \"middle line addition\",\n", "        \"before\": \"\"\"\n", "line1\n", "line2\n", "line3\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line1\n", "line2 extra\n", "line3\n", "\"\"\",\n", "    },\n", "    {\n", "        \"name\": \"adding in the middle\",\n", "        \"before\": \"\"\"\n", "asdf\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "a s d f\n", "\"\"\",\n", "    },\n", "]\n", "\n", "for change in change_list:\n", "    print(f\"{change['before']=}\")\n", "    print(f\"{change['after']=}\")\n", "    for span in process_change(change[\"before\"], change[\"after\"]):\n", "        print(span)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[NoopSpan('line 1\\n')]\n", "[DeletedSpan('line2\\n')]\n", "[NoopSpan('line3')]\n", "========================================================================================================================\n", "[NoopSpan('\\nline 1\\n')]\n", "[DeletedSpan('line 2\\nline 3\\n')]\n", "[NoopSpan('line 4\\n')]\n", "========================================================================================================================\n", "[NoopSpan('\\nline 1\\n')]\n", "[NoopSpan(''), ModSpan(before='line 2', after='l'), NoopSpan('\\n'), DeletedSpan('line 3\\n')]\n", "[NoopSpan('line 4\\n')]\n", "========================================================================================================================\n", "[NoopSpan('\\nline 1\\n')]\n", "[NoopSpan('line '), DeletedSpan('2'), NoopSpan('\\n'), DeletedSpan('line 3\\n')]\n", "[NoopSpan('line 4\\n')]\n", "========================================================================================================================\n", "[NoopSpan('\\nline 1\\n')]\n", "[DeletedSpan('line'), NoopSpan(' 2\\n')]\n", "[NoopSpan('line 3\\nline 4\\n')]\n", "========================================================================================================================\n", "[NoopSpan('\\nline 1\\n')]\n", "[NoopSpan('word1'), DeletedSpan(' word2'), NoopSpan(' word3\\n')]\n", "[NoopSpan('line 3\\nline 4\\n')]\n", "========================================================================================================================\n", "[NoopSpan('\\nline 1\\n')]\n", "[NoopSpan('word1 word2'), DeletedSpan(' word3'), NoopSpan('\\n'), DeletedSpan('line '), NoopSpan('3\\n')]\n", "[NoopSpan('line 4\\n')]\n", "========================================================================================================================\n"]}], "source": ["change_list = [\n", "    {\n", "        \"name\": \"middle line deletion\",\n", "        \"before\": \"line 1\\nline2\\nline3\",\n", "        \"after\": \"line 1\\nline3\",\n", "    },\n", "    {\n", "        \"name\": \"multiple lines deletion\",\n", "        \"before\": \"\"\"\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line 1\n", "line 4\n", "\"\"\",\n", "    },\n", "    {\n", "        \"name\": \"multiple lines deletion\",\n", "        \"before\": \"\"\"\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line 1\n", "l\n", "line 4\n", "\"\"\",\n", "    },\n", "    {\n", "        \"name\": \"multiple lines deletion\",\n", "        \"before\": \"\"\"\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line 1\n", "line \n", "line 4\n", "\"\"\",\n", "    },\n", "    {\n", "        \"name\": \"multiple lines deletion\",\n", "        \"before\": \"\"\"\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line 1\n", " 2\n", "line 3\n", "line 4\n", "\"\"\",\n", "    },\n", "    {\n", "        \"name\": \"multiple lines deletion\",\n", "        \"before\": \"\"\"\n", "line 1\n", "word1 word2 word3\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line 1\n", "word1 word3\n", "line 3\n", "line 4\n", "\"\"\",\n", "    },\n", "    {\n", "        \"name\": \"multiple lines deletion\",\n", "        \"before\": \"\"\"\n", "line 1\n", "word1 word2 word3\n", "line 3\n", "line 4\n", "\"\"\",\n", "        \"after\": \"\"\"\n", "line 1\n", "word1 word2\n", "3\n", "line 4\n", "\"\"\",\n", "    },\n", "]\n", "for change in change_list:\n", "    # print(f\"{change['before']=}\")\n", "    # print(f\"{change['after']=}\")\n", "    for span in process_change(change[\"before\"], change[\"after\"]):\n", "        print(span)\n", "    print(\"=\" * 120)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'process_change' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 18\u001b[0m\n\u001b[1;32m      1\u001b[0m change_list \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m      2\u001b[0m     {\n\u001b[1;32m      3\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mname\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124madding many lines\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     13\u001b[0m     },\n\u001b[1;32m     14\u001b[0m ]\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m change \u001b[38;5;129;01min\u001b[39;00m change_list:\n\u001b[1;32m     16\u001b[0m     \u001b[38;5;66;03m# print(f\"{change['before']=}\")\u001b[39;00m\n\u001b[1;32m     17\u001b[0m     \u001b[38;5;66;03m# print(f\"{change['after']=}\")\u001b[39;00m\n\u001b[0;32m---> 18\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m span \u001b[38;5;129;01min\u001b[39;00m \u001b[43mprocess_change\u001b[49m(change[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbefore\u001b[39m\u001b[38;5;124m'\u001b[39m], change[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mafter\u001b[39m\u001b[38;5;124m'\u001b[39m]):\n\u001b[1;32m     19\u001b[0m         \u001b[38;5;28mprint\u001b[39m(span)\n\u001b[1;32m     20\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m=\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m120\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'process_change' is not defined"]}], "source": ["change_list = [\n", "    {\n", "        \"name\": \"adding many lines\",\n", "        \"before\": \"\"\"\\\n", "\"\"\",\n", "        \"after\": \"\"\"\\\n", "line 1\n", "line 2\n", "line 3\n", "line 4\n", "line 5\n", "\"\"\",\n", "    },\n", "]\n", "for change in change_list:\n", "    # print(f\"{change['before']=}\")\n", "    # print(f\"{change['after']=}\")\n", "    for span in process_change(change[\"before\"], change[\"after\"]):\n", "        print(span)\n", "    print(\"=\" * 120)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}