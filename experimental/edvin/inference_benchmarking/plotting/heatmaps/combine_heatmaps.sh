#!/bin/bash

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null
then
    echo "ImageMagick is not installed. Please install it and try again."
    exit 1
fi

# Default input directory (current directory)
input_dir="S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k"

# Default output file name
output_file="${input_dir}/combined_heatmaps.png"

# Function to print usage
print_usage() {
    echo "Usage: $0 [output_file] [input_directory]"
    echo "  output_file: Name of the output file (default: output.png)"
    echo "  input_directory: Directory containing PNG files (default: current directory)"
}

# Parse arguments
if [ "$#" -ge 1 ]; then
    if [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
        print_usage
        exit 0
    fi
    output_file="$1"
fi

if [ "$#" -ge 2 ]; then
    input_dir="$2"
fi

# Check if input directory exists
if [ ! -d "$input_dir" ]; then
    echo "Error: Input directory '$input_dir' does not exist."
    exit 1
fi

# Get all PNG files in the input directory, excluding hidden files and sorting them
readarray -t input_files < <(find "$input_dir" -maxdepth 1 -type f -name "*.png" -not -name ".*" | sort)

# Check if any PNG files were found
if [ ${#input_files[@]} -eq 0 ]; then
    echo "No PNG files found in '$input_dir'."
    exit 1
fi

# Debugging: Print out the files being processed
echo "Files being processed:"
for file in "${input_files[@]}"; do
    echo "  $file"
done

# Exclude the output file from the input files
input_files=("${input_files[@]/$output_file}")

# Debugging: Print out the files being processed after exclusion
echo "Files being processed after exclusion:"
for file in "${input_files[@]}"; do
    echo "  $file"
done

columns=2

# Use ImageMagick to create a grid of images
montage "${input_files[@]}" -tile ${columns}x -geometry +0+0 "$output_file"

echo "Images have been vertically stacked in and saved as $output_file"
