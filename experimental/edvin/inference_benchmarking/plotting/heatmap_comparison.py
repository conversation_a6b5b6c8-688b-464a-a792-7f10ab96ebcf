import os
from typing import Any
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# pick a folder for this model
model_name = "llama350m_4k"
dataset_name = "S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k"
data_folder = os.path.expanduser(
    f"~/augment/experimental/edvin/inference_benchmarking/results/{dataset_name}/{model_name}"
)

# Option to specify a baseline file
baseline_file = os.path.expanduser(
    f"~/augment/experimental/edvin/inference_benchmarking/results/{dataset_name}/no-sd-model/model=no-sd-model_speculate=0_adaptive=0.0_lol=rust_next_edit_21402_samples_50_users.json.filtered.json_benchmark_results.csv"
)

# get all the files in the folder
files = os.listdir(data_folder)

# filter out the files that are not CSV files
csv_files = [file for file in files if file.endswith(".csv")]

params_to_data: dict[tuple[float, float], Any] = {}
param_names = {"speculate": set(), "adaptive": set()}

# read the CSV files in the folder
current_param_values: dict[str, float] = {}
for file in csv_files:
    for name_part in file.split("_"):
        for param_name in param_names:
            if param_name in name_part:
                param_value = name_part.split("=")[-1]
                current_param_values[param_name] = float(param_value)
                param_names[param_name].add(float(param_value))
                break

    # load the data from the file
    data = pd.read_csv(os.path.join(data_folder, file))

    # create a new total latency column
    data["total_latency_ms"] = (
        data["generation_latency_ms"] + data["context_processing_latency_ms"]
    )

    # add the data to the dictionary
    params_to_data[
        (current_param_values["speculate"], current_param_values["adaptive"])
    ] = data

# Read baseline file if it exists
if os.path.exists(baseline_file):
    baseline_data = pd.read_csv(baseline_file)
    baseline_data["total_latency_ms"] = (
        baseline_data["generation_latency_ms"]
        + baseline_data["context_processing_latency_ms"]
    )

    # Add baseline data to params_to_data for speculate=0 and all values of adaptive
    for adaptive_value in param_names["adaptive"]:
        params_to_data[(0.0, adaptive_value)] = baseline_data

    # Add 0.0 to speculate param_names
    param_names["speculate"].add(0.0)

print(param_names)

# Create dataframes for each percentile
percentiles = [50, 90, 95]
percentile_data = {p: [] for p in percentiles}

for val1 in sorted(param_names["speculate"]):
    for val2 in sorted(param_names["adaptive"]):
        try:
            data = params_to_data[(val1, val2)]
            for p in percentiles:
                percentile_value = np.percentile(data["total_latency_ms"], p)
                percentile_data[p].append(
                    {"speculate": val1, "adaptive": val2, "latency": percentile_value}
                )
        except KeyError:
            print(f"No data for {val1}, {val2}")
            continue

# Create a figure with three subplots
fig, axes = plt.subplots(1, 3, figsize=(18, 6))
fig.suptitle(f"{model_name} - Latency Heatmaps", fontsize=16)

for i, p in enumerate(percentiles):
    df = pd.DataFrame(percentile_data[p])
    pivot_df = df.pivot(index="speculate", columns="adaptive", values="latency")

    sns.heatmap(pivot_df, ax=axes[i], cmap="YlOrRd", annot=True, fmt=".0f")
    axes[i].set_title(f"{p}th Percentile")
    axes[i].set_xlabel("Max probability threshold")
    axes[i].set_ylabel("Max number of tokens to speculate")

plt.tight_layout()
base_path = os.path.expanduser(
    f"~/augment/experimental/edvin/inference_benchmarking/plotting/heatmaps/{dataset_name}/{model_name}_heatmaps.png"
)
i = 0
save_path = base_path
while os.path.exists(save_path):
    i += 1
    save_path = base_path.replace(".png", f"_v{i}.png")

fig.savefig(save_path)
plt.close(fig)

print(f"Heatmaps saved to {save_path}")
