import os
from typing import Any
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# pick a folder for this model
model_name = "llama_350m"
data_folder = os.path.expanduser(
    f"~/augment/experimental/edvin/inference_benchmarking/results/{model_name}"
)

# get all the files in the folder
files = os.listdir(data_folder)

# filter out the files that are not CSV files
csv_files = [file for file in files if file.endswith(".csv")]

params_to_data: dict[tuple[float, float], Any] = {}
param_names = {"speculate": set(), "adaptive": set()}

# read the CSV files in the folder
current_param_values: dict[str, float] = {}
for file in csv_files:
    for name_part in file.split("_"):
        for param_name in param_names:
            if param_name in name_part:
                param_value = name_part.split("-")[-1]
                current_param_values[param_name] = float(param_value)
                param_names[param_name].add(float(param_value))
                break

    # load the data from the file
    data = pd.read_csv(os.path.join(data_folder, file))

    # create a new total latency column
    data["total_latency_ms"] = (
        data["generation_latency_ms"] + data["context_processing_latency_ms"]
    )

    # add the data to the dictionary
    params_to_data[
        (current_param_values["speculate"], current_param_values["adaptive"])
    ] = data

print(param_names)

# Calculate the figure size based on the number of subplots
distinct_param_counts = [len(param_values) for param_values in param_names.values()]
fig_width = 6 * distinct_param_counts[1]  # 6 inches per column
fig_height = 4 * distinct_param_counts[0]  # 4 inches per row

# Create the figure and axes
fig, axes = plt.subplots(
    nrows=distinct_param_counts[0],
    ncols=distinct_param_counts[1],
    figsize=(fig_width, fig_height),
)

# Determine global min and max values
global_min = min(data["total_latency_ms"].min() for data in params_to_data.values())
global_max = max(data["total_latency_ms"].max() for data in params_to_data.values())

# Create consistent bins
bins = np.linspace(global_min, global_max, 31)  # 30 bins

for i, val1 in enumerate(sorted(param_names["speculate"])):
    for j, val2 in enumerate(sorted(param_names["adaptive"])):
        # get the data for this set of params
        try:
            data = params_to_data[(val1, val2)]
        except KeyError:
            print(f"No data for {val1}, {val2}")
            continue

        # calculate percentiles
        p50 = np.percentile(data["total_latency_ms"], 50)
        p90 = np.percentile(data["total_latency_ms"], 90)
        p95 = np.percentile(data["total_latency_ms"], 95)

        ax = axes[i, j]

        # Create the histogram
        n, bins, patches = ax.hist(
            data["total_latency_ms"],
            bins=bins,
            orientation="horizontal",
            edgecolor="black",
            color="skyblue",
            alpha=0.7,
        )

        # Add percentile lines
        ax.axhline(p50, color="green", linestyle="--", label=f"P50: {p50:.2f}ms")
        ax.axhline(p90, color="orange", linestyle="--", label=f"P90: {p90:.2f}ms")
        ax.axhline(p95, color="red", linestyle="--", label=f"P95: {p95:.2f}ms")

        # Customize the plot
        ax.set_xlabel("Frequency")
        if i == 0:  # Only set ylabel for leftmost plots
            ax.set_ylabel("Total Latency (ms)")
        else:
            ax.set_ylabel("")
        ax.legend()

        # customize the axis
        axes[i, j].set_ylim(global_min, global_max)
        axes[i, j].set_title(f"Spec: {val1}, Adapt: {val2}")
        axes[i, j].set_xlabel("Total Latency (ms)")
        axes[i, j].set_ylabel("Frequency")


# Adjust the layout and display the plot
plt.tight_layout()
save_path = os.path.expanduser(
    f"~/augment/experimental/edvin/inference_benchmarking/plotting/{model_name}_results.png"
)
fig.savefig(save_path)
