#!/bin/bash
set -e

# Trap for cleanup and ensuring we exit on interrupt
trap cleanup SIGINT SIGTERM EXIT

cleanup() {
    if files_renamed; then
        echo "Renaming files back to original"
        mv "$deployed_filename" "$model_file_path"
        mv "$base_filename" "$deployed_filename"
    fi
    echo "Cleaning up and exiting..."
    exit 1
}

MODEL_NAME="raven-edit-v4-15b"
end_token=0
no_change_token=49175
pause_token=49154
output_path="/home/<USER>/augment/experimental/edvin/inference_benchmarking/results"
input_path="/home/<USER>/augment/experimental/edvin/inference_benchmarking"
input_file="next_edit_21402_samples_50_users.json.filtered.json"
dataset="S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k"
speculative_models=("starcoder230m" "starcoder450m")
files_renamed=false

deployed_filename="/home/<USER>/augment/services/deploy/raven_edit_v4_15b_deploy.jsonnet"
base_filename="/home/<USER>/augment/services/deploy/raven_edit_v4_15b_deploy_base.jsonnet"

model_index=0
for speculative_model in ${speculative_models[@]}; do
    # rename the files to get the right model deployed
    model_file_path="/home/<USER>/augment/services/deploy/raven_edit_v4_15b_deploy_${speculative_model}.jsonnet"

    # Rename the files
    mv "$deployed_filename" "$base_filename"
    mv "$model_file_path" "$deployed_filename"
    files_renamed=true

    echo "File renamed from $deployed_filename to $base_filename"
    echo "File renamed from $model_file_path to $deployed_filename"


    # make sure the right model is deployed
    echo "deploying model ${speculative_model}"
    if bazel run -c opt //services/deploy:raven_edit_v4_15b_kubecfg | grep -q "No changes"; then
        echo "No changes to raven deployment. Skipping sleep."
    else
        echo "Deployment changed. Sleeping for 200 seconds to allow the service to start..."
        sleep 200
    fi

    # rename the files back to the original
    # mv "$deployed_filename" "$model_file_path"
    # mv "$base_filename" "$deployed_filename"
    # files_renamed=false

    # longest_overlap_lm_max_tokens_to_speculate_ahead=3
    # bazel run //services/test/fake_feature_flags:util -- update --key longest_overlap_lm_max_tokens_to_speculate_ahead --json ${longest_overlap_lm_max_tokens_to_speculate_ahead}

    longest_overlap_lm_model_json="\"rust\""
    longest_overlap_lm_replace_tokens=false
    bazel run //services/test/fake_feature_flags:util -- update --key longest_overlap_lm_replace_tokens --json ${longest_overlap_lm_replace_tokens}
    for generation_state_max_tokens_to_speculate_ahead in 1; do
        for generation_state_min_speculation_probability in 0.0; do
            # must escapce the quotes to get a valid JSON string
            # for longest_overlap_lm_model_json in "\"python\"" "\"rust\""; do
                # update the dynamic feature flags
                echo "Updating feature flags..."
                bazel run //services/test/fake_feature_flags:util -- update --key generation_state_max_tokens_to_speculate_ahead --json ${generation_state_max_tokens_to_speculate_ahead}
                bazel run //services/test/fake_feature_flags:util -- update --key generation_state_min_speculation_probability --json ${generation_state_min_speculation_probability}
                bazel run //services/test/fake_feature_flags:util -- update --key longest_overlap_lm_model --json ${longest_overlap_lm_model_json}

                echo "speculative model: ${speculative_model}"
                echo "max_tokens_to_speculate_ahead: ${generation_state_max_tokens_to_speculate_ahead}"
                echo "min_speculation_probability: ${generation_state_min_speculation_probability}"

                # Strip quotes and escapes for path usage
                longest_overlap_lm_model=$(echo $longest_overlap_lm_model_json | tr -d '\\"')

                sub_path="${output_path}/${dataset}/${speculative_model}"
                mkdir -p ${sub_path}

                # output file name contains all feature flag values for this run
                outfile="model=${speculative_model}_speculate=${generation_state_max_tokens_to_speculate_ahead}_adaptive=${generation_state_min_speculation_probability}_lol=${longest_overlap_lm_model}_${input_file}_benchmark_results"

                echo "Saving to ${sub_path}/${outfile}.csv"

        # sleep 10

                bazel run //services/inference_host/client:grpc_inference_benchmarking -- \
                    --cloud GCP_US_CENTRAL1_DEV \
                    --namespace dev-$USER \
                    --tenant-name augment \
                    --auth-token-file /home/<USER>/.augment/request_token \
                    --service infer-$MODEL_NAME-svc \
                    --endpoint infer-$MODEL_NAME-svc:8001 \
                    --end_token_ids $end_token $no_change_token $pause_token \
                    --input_path "$input_path/$input_file" \
                    --output_prefix "${sub_path}/${outfile}" \
                    --max_seq_length 12288 \
                    --measure_context_processing_separately \
                    --num_runs_per_sample 1 \
                    --take_first_n_samples 500 \
                    --max_decode_tokens 300 \
                    # --request-token \

                if [ $? -eq 0 ]; then
                    echo "Finished!"
                    echo "Results written to ${sub_path}/${outfile}"
                else
                    echo "Error: The benchmarking command failed."
                fi
            # done
        done
    done
done
