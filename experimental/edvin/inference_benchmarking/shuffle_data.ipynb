{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfull shuffle\n"]}], "source": ["import json\n", "import random\n", "import os\n", "\n", "with open(\"next_edit_21402_samples_50_users.json.filtered.json\", \"r\") as f:\n", "    data = json.load(f)\n", "\n", "random.shuffle(data)\n", "\n", "with open(\"next_edit_21402_samples_50_users.json.filtered.shuffled.json\", \"w\") as f:\n", "    json.dump(data, f)\n", "\n", "if os.path.exists(\"next_edit_21402_samples_50_users.json.filtered.shuffled.json\"):\n", "    print(\"Successfull shuffle\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}