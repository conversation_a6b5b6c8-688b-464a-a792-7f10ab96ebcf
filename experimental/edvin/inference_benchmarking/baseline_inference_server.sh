#!/bin/bash
set -e

# Trap for cleanup and ensuring we exit on interrupt
trap cleanup SIGINT SIGTERM EXIT

cleanup() {
    if files_renamed; then
        echo "Renaming files back to original"
        mv "$deployed_filename" "$model_file_path"
        mv "$base_filename" "$deployed_filename"
    fi
    echo "Cleaning up and exiting..."
    exit 1
}

MODEL_NAME="raven-edit-v4-15b"
end_token=0
no_change_token=49175
pause_token=49154

output_path="/home/<USER>/augment/experimental/edvin/inference_benchmarking/results"
input_path="/home/<USER>/augment/experimental/edvin/inference_benchmarking"
input_file="next_edit_21402_samples_50_users.json.filtered.json"
dataset="S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k"
longest_overlap_lm_model_json="\"rust\""
longest_overlap_lm_replace_tokens=false
generation_state_max_tokens_to_speculate_ahead=0
generation_state_min_speculation_probability=0.0
speculative_model="no-sd-model"

# make sure the right model is deployed
if bazel run -c opt //services/deploy:raven_edit_v4_15b_kubecfg | grep -q "No changes"; then
    echo "No changes to raven deployment. Skipping sleep."
else
    echo "Deployment changed. Sleeping for 200 seconds to allow the service to start..."
    sleep 200
fi

echo "Updating feature flags..."
bazel run //services/test/fake_feature_flags:util -- update --key longest_overlap_lm_replace_tokens --json ${longest_overlap_lm_replace_tokens}
bazel run //services/test/fake_feature_flags:util -- update --key generation_state_max_tokens_to_speculate_ahead --json ${generation_state_max_tokens_to_speculate_ahead}
bazel run //services/test/fake_feature_flags:util -- update --key generation_state_min_speculation_probability --json ${generation_state_min_speculation_probability}
bazel run //services/test/fake_feature_flags:util -- update --key longest_overlap_lm_model --json ${longest_overlap_lm_model_json}

echo "speculative model: ${speculative_model}"
echo "max_tokens_to_speculate_ahead: ${generation_state_max_tokens_to_speculate_ahead}"
echo "min_speculation_probability: ${generation_state_min_speculation_probability}"

# Strip quotes and escapes for path usage
longest_overlap_lm_model=$(echo $longest_overlap_lm_model_json | tr -d '\\"')

sub_path="${output_path}/${dataset}/${speculative_model}"
mkdir -p ${sub_path}

# output file name contains all feature flag values for this run
outfile="model=${speculative_model}_speculate=${generation_state_max_tokens_to_speculate_ahead}_adaptive=${generation_state_min_speculation_probability}_lol=${longest_overlap_lm_model}_${input_file}_benchmark_results"

echo "Saving to ${sub_path}/${outfile}.csv"

bazel run //services/inference_host/client:grpc_inference_benchmarking -- \
    --cloud GCP_US_CENTRAL1_DEV \
    --namespace dev-$USER \
    --tenant-name augment \
    --auth-token-file /home/<USER>/.augment/request_token \
    --service infer-$MODEL_NAME-svc \
    --endpoint infer-$MODEL_NAME-svc:8001 \
    --end_token_ids $end_token $no_change_token $pause_token \
    --input_path "$input_path/$input_file" \
    --output_prefix "${sub_path}/${outfile}" \
    --max_seq_length 12288 \
    --measure_context_processing_separately \
    --num_runs_per_sample 1 \
    --take_first_n_samples 500 \
    --max_decode_tokens 300 \
    # --request-token \

if [ $? -eq 0 ]; then
    echo "Finished!"
    echo "Results written to ${sub_path}/${outfile}"
else
    echo "Error: The benchmarking command failed."
fi
