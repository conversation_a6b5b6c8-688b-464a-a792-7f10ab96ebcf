{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get the prompts\n", "%reload_ext autoreload\n", "%autoreload 2\n", "import sys\n", "from typing import Sequence\n", "\n", "sys.path.append(\"/home/<USER>/augment\")\n", "\n", "from datetime import datetime, timedelta\n", "from tools.load_test.configs.scripts.generate_next_edit_load_test_data import (\n", "    get_request_ids_by_user,\n", "    get_request_events_from_ids,\n", ")\n", "from base.datasets.tenants import get_tenant\n", "\n", "REQUEST_TYPE = \"NEXT_EDIT\"\n", "start_date = datetime.now() - <PERSON><PERSON><PERSON>(days=14)\n", "end_date = datetime.now()\n", "user_limit = 50\n", "requests_per_user_limit = 100\n", "DEFAULT_TENANT = get_tenant(\"dogfood-shard\")\n", "\n", "request_ids_by_user = get_request_ids_by_user(\n", "    request_type=REQUEST_TYPE,\n", "    start=start_date,\n", "    end=end_date,\n", "    user_limit=user_limit,\n", "    requests_per_user_limit=requests_per_user_limit,\n", "    tenant=DEFAULT_TENANT,\n", ")\n", "\n", "\n", "def get_generations_from_ids(request_ids: list[str]) -> list[dict]:\n", "    request_events_by_id = get_request_events_from_ids(\n", "        request_ids,\n", "        tenant=DEFAULT_TENANT,\n", "        request_events=frozenset([\"next_edit_host_response\"]),\n", "    )\n", "    all_generations = []\n", "    for request_events in request_events_by_id:\n", "        for event in request_events:\n", "            # print(event.data[EVENT_NAME].keys())\n", "            if request_generations := event.next_edit_host_response.generation:\n", "                for request_generation in request_generations:\n", "                    # if gen_prompt := generation.get(\"generationPrompt\", None):\n", "                    #     print(gen_prompt.keys())\n", "                    # if gen_output := generation.get(\"generationOutput\", None):\n", "                    #     print(gen_output.keys())\n", "                    all_generations.append(request_generation)\n", "            # if request_suggestions := event.data[EVENT_NAME].get(\"suggestions\", None):\n", "            #     for request_suggestion in request_suggestions:\n", "            #         print(request_suggestion.keys())\n", "            #         print(request_suggestion[\"result\"].keys())\n", "            #         print(request_suggestion[\"result\"][\"suggestedEdit\"].keys())\n", "\n", "    return all_generations\n", "\n", "\n", "generation_set: set[tuple[Sequence[int], Sequence[int]]] = set()\n", "user_count = 0\n", "total_samples: list[dict] = []\n", "for user_row in request_ids_by_user:\n", "    user_count += 1\n", "    request_ids = [request[\"request_id\"] for request in user_row.requests]\n", "    generations = get_generations_from_ids(request_ids)\n", "    samples = [\n", "        {\n", "            \"context\": generation.get(\"generationPrompt\", dict()).get(\"tokenIds\"),\n", "            \"label\": generation.get(\"generationOutput\", dict()).get(\"tokenIds\"),\n", "        }\n", "        for generation in generations\n", "    ]\n", "    # filtered_samples: list[dict] = []\n", "    # for sample in samples:\n", "    #     if sample[\"context\"] is None or sample[\"label\"] is None:\n", "    #         continue\n", "    #     if (sample[\"context\"], sample[\"label\"]) not in generation_set:\n", "    #         generation_set.add((sample[\"context\"], sample[\"label\"]))\n", "    #         filtered_samples.append(sample)\n", "\n", "    total_samples.extend(samples)\n", "    # outputs = [generation.get(\"generationOutput\", dict()) for generation in generations]\n", "    # for output in outputs:\n", "    # print(output)\n", "    # for sample in samples:\n", "    # print(sample.keys())\n", "    # print(sample[\"context\"])\n", "    # print(sample[\"label\"])\n", "\n", "sample_count = len(total_samples)\n", "print(f\"saved {sample_count} samples for {user_count} users\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# write the samples to a json file\n", "import json\n", "\n", "with open(f\"next_edit_{sample_count}_samples_{user_count}_users.json\", \"w\") as f:\n", "    json.dump(total_samples, f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# adapt the kubectl command to add an inference host local port forwarding\n", "k8s_context = \"gke_system-services-dev_us-central1_us-central1-dev\"  # Replace with your desired context\n", "deployment_name = \"infer-raven-edit-v3-15b\"\n", "port = \"50051\"\n", "dev_user = \"edvin\"\n", "\n", "# Construct the kubectl command with the specified context\n", "kubectl_command = f\"\"\"kubectl \\\n", " --context={k8s_context} \\\n", " port-forward deployment/{deployment_name} \\\n", " {port}:{port} \\\n", " -n dev-{dev_user}\n", "\"\"\"\n", "\n", "print(kubectl_command)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # now we need to call the benchmark inference script\n", "# import subprocess\n", "\n", "# print(sys.executable)\n", "\n", "# script_with_arguments = \"\"\"\\\n", "# /home/<USER>/augment/services/inference_host/client/benchmark_inference_host.py \\\n", "#     --endpoint localhost:50051 \\\n", "#     --input_path /home/<USER>/augment/experimental/edvin/inference_benchmarking/next_edit_samples.json \\\n", "#     --output_prefix /home/<USER>/augment/experimental/edvin/inference_benchmarking/next_edit_samples_benchmark_results \\\n", "#     --max_seq_length 256\n", "#     --end_token_ids 0 32014 32021\n", "#     --measure_context_processing_separately\n", "# \"\"\"\n", "\n", "# # Define the command you want to run\n", "# command = [\"/bin/python3.11\", script_with_arguments]\n", "\n", "# # Start the process\n", "# process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "\n", "# # If you want to wait for the process to complete and get its output:\n", "# stdout, stderr = process.communicate()\n", "\n", "# print(\"STDOUT:\", stdout.decode())\n", "# print(\"STDERR:\", stderr.decode())\n", "\n", "# # If you don't want to wait for the process to complete, you can just start it:\n", "# # process = subprocess.<PERSON><PERSON>(command)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import subprocess\n", "# import sys\n", "\n", "# # Use sys.executable to get the path of the current Python interpreter\n", "# python_path = sys.executable\n", "# print(python_path)\n", "# sys.path.append(\"/home/<USER>/augment\")\n", "\n", "# # Define the script path and arguments\n", "# script_path = (\n", "#     \"/home/<USER>/augment/services/inference_host/client/benchmark_inference_host.py\"\n", "# )\n", "# script_args = [\n", "#     \"--endpoint\",\n", "#     \"localhost:50051\",\n", "#     \"--input_path\",\n", "#     \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/next_edit_samples.json\",\n", "#     \"--output_prefix\",\n", "#     \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/next_edit_samples_benchmark_results\",\n", "#     \"--max_seq_length\",\n", "#     \"256\",\n", "#     \"--end_token_ids\",\n", "#     \"0\",\n", "#     \"32014\",\n", "#     \"32021\",\n", "#     \"--measure_context_processing_separately\",\n", "# ]\n", "\n", "# # Combine the Python path, script path, and arguments\n", "# command = [\"python3\", script_path] + script_args\n", "\n", "# # Start the process\n", "# process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "\n", "# # Wait for the process to complete and get its output\n", "# stdout, stderr = process.communicate()\n", "\n", "# # Print the output\n", "# print(\"STDOUT:\")\n", "# print(stdout.decode())\n", "# print(\"\\nSTDERR:\")\n", "# print(stderr.decode())\n", "\n", "# # Print the return code\n", "# print(f\"\\nReturn code: {process.returncode}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import subprocess\n", "# import os\n", "# from IPython.display import display, HTML\n", "\n", "\n", "# def run_inference_benchmark(prompt=\"def hello():\", model_name=\"raven-edit-v3-15b\"):\n", "#     command = [\n", "#         \"bazel\",\n", "#         \"run\",\n", "#         \"//services/inference_host/client:inference_host_util\",\n", "#         \"--\",\n", "#         \"--cloud\",\n", "#         \"GCP_US_CENTRAL1_DEV\",\n", "#         \"--namespace\",\n", "#         f\"dev-{os.environ.get('USER')}\",\n", "#         \"--tenant-name\",\n", "#         \"augment\",\n", "#         \"--request-token\",\n", "#         \"--service\",\n", "#         f\"infer-{model_name}-svc\",\n", "#         \"--endpoint\",\n", "#         f\"infer-{model_name}-svc:8001\",\n", "#         \"infer\",\n", "#         \"--prompt\",\n", "#         prompt,\n", "#         \"--tokenizer-name\",\n", "#         \"starcoder2\",\n", "#     ]\n", "\n", "#     try:\n", "#         result = subprocess.run(\n", "#             command,\n", "#             check=True,\n", "#             capture_output=True,\n", "#             text=True,\n", "#             input=\"\",\n", "#         )\n", "#         output = result.stdout\n", "#     except subprocess.CalledProcessError as e:\n", "#         output = f\"Error: {e.stderr}\"\n", "\n", "#     return output\n", "\n", "\n", "# # Example usage in the notebook:\n", "# # run_inference_benchmark(\"def fibon<PERSON>ci(n):\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the benchmark with default parameters\n", "# output = run_inference_benchmark()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import sys\n", "# import uuid\n", "\n", "# from pydantic import SecretStr\n", "\n", "# sys.path.append(\"/home/<USER>/augment\")\n", "# for path in sys.path:\n", "#     print(path)\n", "\n", "# from services.inference_host.client.inference_host_client import (\n", "#     InfererClient,\n", "#     RequestContext,\n", "#     create_inference_stub,\n", "# )\n", "\n", "# # stub = services.inference_host.client.inference_host_util.create_inference_stub()\n", "# # client = services.inference_host.client.inference_host_util.InfererClient()\n", "# infer_stub = create_inference_stub(endpoint=\"infer-raven-edit-v3-15b-svc:8001\")\n", "# client = InfererClient(\n", "#     infer_stub,\n", "# )\n", "# request_context = RequestContext.create(\n", "#     request_source=\"unknown\",\n", "#     auth_token=SecretStr(\n", "#         \"\"\n", "#     ),\n", "#     request_id=str(uuid.uuid4()),\n", "#     request_session_id=None,\n", "#     timeout=None,\n", "# )\n", "\n", "# client.infer(\n", "#     input_tokens=[1],\n", "#     max_output_length=3,\n", "#     end_token_ids=[4],\n", "#     top_k=1,\n", "#     top_p=0,\n", "#     temperature=0.0,\n", "#     random_seed=42,\n", "#     timeout_s=30.0,\n", "#     request_context=RequestContext.create(),\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # from services.lib.grpc import grpc_args_parser\n", "\n", "\n", "# with grpc_args_parser.create_client(\n", "#     args,\n", "#     lambda endpoint, credentials, options: inference_host_client.InfererClient(\n", "#         inference_host_client.create_inference_stub(\n", "#             endpoint, credentials=credentials, options=options\n", "#         )\n", "#     ),\n", "#     default_service_name=None,\n", "#     default_endpoint=None,\n", "# ) as client:\n", "#     auth_token = token_parser.get_token(args)\n", "#     request_id = None\n", "#     if args.request_id:\n", "#         request_id = args.request_id\n", "#     request_session_id = None\n", "#     if args.request_session_id:\n", "#         request_session_id = args.request_session_id\n", "\n", "#     request_context = RequestContext.create(\n", "#         auth_token=auth_token,\n", "#         request_id=request_id,\n", "#         request_session_id=request_session_id,\n", "#     )\n", "#     infer_params = {\n", "#         \"input_tokens\": input_tokens,\n", "#         \"max_output_length\": args.output_len,\n", "#         \"end_token_ids\": [tokenizer.special_tokens.eos],\n", "#         \"top_k\": 1,\n", "#         \"top_p\": 1.0,\n", "#         \"temperature\": 1.0,\n", "#         \"random_seed\": 0,\n", "#         \"request_context\": request_context,\n", "#         \"timeout_s\": 30,\n", "#     }\n", "#     if args.stream:\n", "#         output = client.infer_stream(**infer_params)\n", "#         tokens = []\n", "#         for partial_result in output:\n", "#             logging.info(\"Partial output tokens %s\", partial_result)\n", "#             tokens.extend(partial_result.output_tokens)\n", "#         logging.info(\"Stream done, output\\n%s\", tokenizer.detokenize(tokens))\n", "#     else:\n", "#         output = client.infer(**infer_params)\n", "#         logging.info(\"Output tokens %s\", output)\n", "#         logging.info(\"Output\\n%s\", tokenizer.detokenize(output.output_tokens))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}