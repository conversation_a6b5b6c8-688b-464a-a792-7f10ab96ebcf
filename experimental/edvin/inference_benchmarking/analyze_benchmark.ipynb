{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Read the data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_neural_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/non-neural_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_230m_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder-230m_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_230m_2_tokens_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder-230m_num_ahead_tokens=2_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_230m_3_tokens_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder-230m_num_ahead_tokens=3_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_230m_5_tokens_adaptive_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder_230m_speculate-5_adaptive-True_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_230m_5_tokens_adaptive_lm_fix_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder_230m_speculate-3_adaptive-True_lm-fixes-True_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_310m_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder-310m_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "starcoder_310m_2_tokens_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/starcoder-310m_num_ahead_tokens=2_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")\n", "llama_350m_fp8_benchmark_data = pd.read_csv(\n", "    \"/home/<USER>/augment/experimental/edvin/inference_benchmarking/results/llama-350m-fp8_next_edit_21402_samples_50_users_benchmark_results.csv\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Filter the data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_benchmark_data = [\n", "    non_neural_benchmark_data,\n", "    starcoder_230m_benchmark_data,\n", "    starcoder_230m_2_tokens_benchmark_data,\n", "    starcoder_230m_3_tokens_benchmark_data,\n", "    starcoder_230m_5_tokens_adaptive_benchmark_data,\n", "    starcoder_230m_5_tokens_adaptive_lm_fix_benchmark_data,\n", "    # starcoder_310m_benchmark_data,\n", "    # llama_350m_fp_benchmark_data,\n", "    # starcoder_310m_2_tokens_benchmark_data,\n", "]\n", "\n", "# filter out generation times longer than 1 minute\n", "for i, data in enumerate(all_benchmark_data):\n", "    all_benchmark_data[i] = data[data[\"generation_latency_ms\"] <= 30000]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# append new dataframes where we include the context processing time in the generation latency column\n", "new_dataframes = []\n", "for data in all_benchmark_data:\n", "    new_data = data.copy()\n", "    new_data[\"generation_latency_ms\"] = (\n", "        data[\"generation_latency_ms\"] + data[\"context_processing_latency_ms\"]\n", "    )\n", "    new_dataframes.append(new_data)\n", "\n", "all_benchmark_data.extend(new_dataframes)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import math\n", "\n", "# Assuming you have a list of custom titles\n", "# custom_titles = [\n", "#     \"Current\",\n", "#     \"Starcoder2 230m (12 layers)\",\n", "#     \"Llama 350m (6 layers)\",\n", "#     \"Current with context processing\",\n", "#     \"Starcoder with context processing\",\n", "#     \"Llama with context processing\",\n", "#     \"title\",\n", "#     \"title\",\n", "# ]  # Add your custom titles here\n", "\n", "# pick the second half with the context processing time\n", "new_all_benchmark_data = all_benchmark_data[len(all_benchmark_data) // 2 :]\n", "\n", "# Get all local variables\n", "# local_vars = locals()\n", "# print(local_vars)\n", "\n", "# Find variables that are elements of new_all_benchmark_data\n", "# benchmark_var_names = [\n", "#     var_name\n", "#     for var_name, var_value in local_vars.items()\n", "#     if isinstance(var_value, pd.DataFrame)\n", "#     and id(var_value) in [id(df) for df in all_benchmark_data]\n", "# ]\n", "\n", "# print(benchmark_var_names)\n", "\n", "# # Generate custom titles based on these variable names\n", "# custom_titles = [var_name.replace(\"_\", \" \").title() for var_name in benchmark_var_names]\n", "\n", "custom_titles = [\"current\", \"1\", \"2\", \"3\", \"5 (adapt)\", \"lm fixes\"]\n", "\n", "# If you want to add numbering:\n", "# custom_titles = [f\"{i+1}. {var_name.replace('_', ' ').title()}\"\n", "#                  for i, var_name in enumerate(benchmark_var_names)]\n", "\n", "# Print the titles to verify\n", "print(custom_titles)\n", "\n", "# If you want to keep the original numbering or add any specific formatting, you can modify the list comprehension\n", "# For example, to add numbering:\n", "# custom_titles = [f\"{i+1}. {var_name.replace('_', ' ').title()}\" for i, var_name in enumerate(all_benchmark_data.keys())]\n", "num_cols = 3\n", "\n", "# Calculate the number of rows needed\n", "n_plots = len(new_all_benchmark_data)\n", "n_cols = min(n_plots, num_cols)  # At most 3 columns\n", "n_rows = math.ceil(n_plots / num_cols)  # Calculate number of rows needed\n", "\n", "print(n_rows, n_cols)\n", "\n", "# Determine global min and max values\n", "global_min = min(data[\"generation_latency_ms\"].min() for data in new_all_benchmark_data)\n", "global_max = max(data[\"generation_latency_ms\"].max() for data in new_all_benchmark_data)\n", "\n", "# Create consistent bins\n", "bins = np.linspace(global_min, global_max, 31)  # 30 bins\n", "\n", "# Create the figure and axes\n", "fig, axes = plt.subplots(\n", "    n_rows, n_cols, figsize=(15, 5 * n_rows), sharex=True, sharey=True\n", ")\n", "\n", "# Ensure axes is always a 2D array\n", "axes = np.atleast_2d(axes)\n", "\n", "# Loop through your data and create plots\n", "for i, (data, title) in enumerate(zip(new_all_benchmark_data, custom_titles)):\n", "    if i < n_rows * n_cols:  # Ensure we don't exceed the number of created subplots\n", "        row = i // n_cols\n", "        col = i % n_cols\n", "        ax = axes[row, col]\n", "\n", "        # Calculate percentiles\n", "        p50 = np.percentile(data[\"generation_latency_ms\"], 50)\n", "        p90 = np.percentile(data[\"generation_latency_ms\"], 90)\n", "        p95 = np.percentile(data[\"generation_latency_ms\"], 95)\n", "\n", "        # Create the histogram\n", "        n, bins, patches = ax.hist(\n", "            data[\"generation_latency_ms\"],\n", "            bins=bins,\n", "            orientation=\"horizontal\",\n", "            edgecolor=\"black\",\n", "            color=\"skyblue\",\n", "            alpha=0.7,\n", "        )\n", "\n", "        # Add percentile lines\n", "        ax.axhline(p50, color=\"green\", linestyle=\"--\", label=f\"P50: {p50:.2f}ms\")\n", "        ax.axhline(p90, color=\"orange\", linestyle=\"--\", label=f\"P90: {p90:.2f}ms\")\n", "        ax.axhline(p95, color=\"red\", linestyle=\"--\", label=f\"P95: {p95:.2f}ms\")\n", "\n", "        # Customize the plot\n", "        ax.set_title(title)\n", "        ax.set_xlabel(\"Frequency\")\n", "        if col == 0:  # Only set ylabel for leftmost plots\n", "            ax.set_ylabel(\"Generation Latency (ms)\")\n", "        else:\n", "            ax.set_ylabel(\"\")\n", "        ax.legend()\n", "\n", "        # Set y-axis limits\n", "        ax.set_ylim(global_min, global_max)\n", "\n", "# Remove any unused subplots\n", "for j in range(i + 1, n_rows * n_cols):\n", "    row = j // n_cols\n", "    col = j % n_cols\n", "    fig.delaxes(axes[row, col])\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}