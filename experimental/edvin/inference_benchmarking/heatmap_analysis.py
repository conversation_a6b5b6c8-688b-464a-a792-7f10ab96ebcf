import logging
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import re

model_name = "starcoder2_230m"

# Set the path to the results folder
results_folder = Path(f"results/{model_name}")


# Function to extract speculative and adaptive numbers from filename
def extract_params(filename):
    match = re.search(r"speculate-(\d+)_adaptive-([\d.]+)", filename)
    if match:
        return int(match.group(1)), float(match.group(2))
    return None, None


# Gather data from CSV files
data = []
for file in results_folder.glob("*.csv"):
    spec_num, adaptive_num = extract_params(file.name)
    if spec_num is not None and adaptive_num is not None:
        try:
            df = pd.read_csv(file)
        except Exception as e:
            logging.error(f"Failed to read {file}: {e}")
            continue
        mean_latency = df["generation_latency_ms"].mean()
        data.append(
            {
                "speculative": spec_num,
                "adaptive": adaptive_num,
                "mean_latency": mean_latency,
            }
        )

# Create a DataFrame from the gathered data
df = pd.DataFrame(data)

# Pivot the data for plotting
pivot_df = df.pivot(index="speculative", columns="adaptive", values="mean_latency")

# Plot the results
plt.figure(figsize=(12, 8))
sns.heatmap(pivot_df, annot=True, fmt=".2f", cmap="YlOrRd")
plt.title(f"Mean Latency (ms) for {model_name}")
plt.xlabel("Adaptive Number")
plt.ylabel("Speculative Number")
plt.tight_layout()
plt.savefig(f"{model_name}_results.png")
plt.close()

# Print summary
print(pivot_df)
