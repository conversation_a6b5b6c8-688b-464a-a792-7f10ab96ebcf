# flake8: noqa
from __future__ import annotations

from pathlib import Path
import json
import yaml

from megatron.tokenizer.tokenizer import Star<PERSON><PERSON>r<PERSON><PERSON>okenizer, StarCoderTokenizer
from research.core.constants import AUGMENT_CHECKPOINTS_ROOT, AUGMENT_EFS_ROOT
from research.environments.providers import ClusterName
from experimental.jiayi.finetuning.training_config_utils import (
    ModelName,
    ModelSize,
    StarCoderModelSize,
    get_neox_config,
    get_fastbackward_config,
    sync_gcp,
    wait_for_condition,
)


def read_checkpoint_info(checkpoint_name):
    """
    Read the checkpoint info from the JSON file.
    Args:
        checkpoint_name: The name of the checkpoint.
    Returns:
        The checkpoint ID, or None if the checkpoint name is not found.
    """
    # Path to the JSON file
    file_path = Path(
        "/home/<USER>/augment/experimental/edvin/checkpoints/edvin_checkpoints.json"
    )

    # Open and read the JSON file
    with file_path.open("r") as file:
        checkpoints_data = json.load(file)

    # Check if the checkpoint name exists in the data
    if checkpoint_name in checkpoints_data:
        return checkpoints_data[checkpoint_name].get("id")
    else:
        return None


def edit_gen_fb_config(
    data_summary: str,
    model_name: ModelName,
    user: str,
    model_size: ModelSize,
    data_splits_path: Path,
    sequence_length: int,
    train_iters: int,
    eval_items: int = 256 * 100,
    n_nodes: int | None = None,
    batch_size_per_gpu: int | None = None,
    is_quick_test: bool = False,
    determined_checkpoint_id: str | None = None,
    tokenizer_name: str | None = None,
) -> dict:
    """Get the training config dict to be submitted to FastBackward.

    Args:
        run_summary: A short string summary of the training run.
        model_name: The model name to use.
        user: The training job will be submitted under Dev/{user}.
        model_size: The model size to use.
        data_splits_path: The directory containing the indexed dataset files.
        sequence_length: The sequence length to use.
        train_iters: The number of training iterations.
        n_nodes: The number of GPU nodes to use. Each node has 8 GPUs.
        is_quick_test: Whether to use a quick test config.
        restore_from_checkpoint: The checkpoint ID to restore training from. When this\
            is set, `checkpoint_path` will be ignored.
        checkpoint_path: The path to the checkpoint directory. If None, will use the\
            default checkpoint path for the model.
    """

    wandb_project = "edvin-next-edit-speculative-decoding"
    training_name = f"{data_summary}-{model_name}_{model_size}"
    if is_quick_test:
        training_name += "-quicktest"

    common_overrides: dict = {
        "eval_items": eval_items,
        "eval_interval": 500,
        "log_interval": 100,
    }
    if determined_checkpoint_id is not None:
        common_overrides["checkpoint"] = determined_checkpoint_id
        # assuming we don't want this if we are not restoring from the Determined UI
        common_overrides["restore_training_metadata_from_checkpoint"] = False
        common_overrides["restore_optimizer_state_from_checkpoint"] = False

    if is_quick_test:
        common_overrides["eval_interval"] = 40
        common_overrides["eval_items"] = 128
        train_iters = 50
        wandb_project = "quicktest"

    if (
        model_size == "310m"
        or model_size == "230m"
        or model_size == "350m"
        or model_size == "700m"
        or model_size == "450m"
    ):
        n_nodes = n_nodes or 4
        batch_size_per_gpu = batch_size_per_gpu or 4
        assert n_nodes * batch_size_per_gpu <= 32

        # effective batch size = 256
        grad_accumulation_steps = 32 // (n_nodes * batch_size_per_gpu)

        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=n_nodes,
            batch_size_per_gpu=batch_size_per_gpu,
            grad_accumulation_steps=grad_accumulation_steps,
            train_iters=train_iters,
            overrides=common_overrides,
            tokenizer_name=tokenizer_name,
        )

    if model_size == "3b":
        n_nodes = n_nodes or 4
        assert n_nodes in (1, 2, 4, 8)
        grad_accumulation_steps = 8 // n_nodes
        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=n_nodes,
            batch_size_per_gpu=4,
            grad_accumulation_steps=grad_accumulation_steps,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    if model_size == "7b":
        n_nodes = n_nodes or 4
        assert n_nodes in (1, 2, 4, 8, 16)
        grad_accumulation_steps = 16 // n_nodes
        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=n_nodes,
            batch_size_per_gpu=2,
            grad_accumulation_steps=grad_accumulation_steps,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    elif model_size in ("15b", "16b"):
        n_nodes = n_nodes or 8
        assert n_nodes in (1, 2, 4, 8, 16, 32)
        grad_accumulation_steps = 32 // n_nodes

        return get_fastbackward_config(
            model_name=model_name,
            model_size=model_size,
            training_name=training_name,
            wandb_project=wandb_project,
            user=user,
            data_split_path=data_splits_path,
            sequence_length=sequence_length,
            n_gpu=8,
            n_nodes=n_nodes,
            batch_size_per_gpu=2,
            grad_accumulation_steps=grad_accumulation_steps,
            train_iters=train_iters,
            overrides=common_overrides,
        )
    else:
        raise NotImplementedError(f"model_size {model_size} not supported")


def start_finetuning(
    model_name: ModelName,
    model_size: ModelSize,
    checkpoint_name: str,
    tokenizer_name: str,
    n_nodes: int | None = None,
    batch_size_per_gpu: int | None = None,
):
    dataset_path = Path(
        "/mnt/efs/augment/data/processed/next-edit/"
        "prv2-pr_grouped_10k/S26_16000p,R4_ethanol-K128,P18_star2_diff12_seq12k-pos_0.70-pad_12033"
    )
    wait_for_condition(
        "dataset_path", lambda: (dataset_path / "_SUCCESS").exists(), retry_secs=60
    )

    data_summary = (
        f"{checkpoint_name}-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k"
    )

    checkpoint_id = read_checkpoint_info(checkpoint_name)

    sequence_length = 12032  # this should be pad_to_length - 1
    assert sequence_length % 128 == 0, "sequence_length should be a multiple of 128"
    train_iters = 20_000
    training_with_fb = True
    is_quick_test = False
    cluster: ClusterName = "CW"

    if cluster != "CW":
        if model_name == "starcoder2":
            sync_gcp(AUGMENT_EFS_ROOT / StarCoder2Tokenizer.VocabFileDir)
        else:
            sync_gcp(AUGMENT_EFS_ROOT / StarCoderTokenizer.VocabFileDir)

    if training_with_fb:
        from research.fastbackward.determined.launch import launch_fb_job  # type: ignore

        train_config = edit_gen_fb_config(
            model_name=model_name,
            data_summary=data_summary,
            user="edvin",
            model_size=model_size,
            data_splits_path=dataset_path,
            sequence_length=sequence_length,
            train_iters=train_iters,
            is_quick_test=is_quick_test,
            determined_checkpoint_id=checkpoint_id,
            n_nodes=n_nodes,
            batch_size_per_gpu=batch_size_per_gpu,
            tokenizer_name=tokenizer_name,
        )

        # Write config to a YAML file
        config_file_path = Path(
            f"/home/<USER>/augment/experimental/edvin/training/configs/fastbackward/{model_name}_{model_size}/{data_summary}_config.yaml"
        )
        with config_file_path.open("w") as f:
            yaml.dump(train_config, f, default_flow_style=False)

        print(f"Config written to {config_file_path}")

        launch_fb_job(
            train_config,
            cluster=cluster,
        )


if __name__ == "__main__":
    # must be run from the augment repo root to correctly parse the json with checkpoint ids
    start_finetuning(
        model_name="starcoder2",
        model_size="450m",
        checkpoint_name="starcoder2_450m_4K_pt",
        tokenizer_name="StarCoder2Tokenizer",
        n_nodes=4,
        batch_size_per_gpu=4,
    )
