determined:
  description: "Pre-training of a StarCoder2-230M model for speculative decoding purposes."
  # description: "Pre-training of a StarCoder2-300M model for speculative decoding purposes."
  workspace: Dev
  project: edvin

augment:
  project_group: pretraining
  podspec_path: "8xH100.yaml"
  gpu_count: 64

fastbackward_configs:
  - configs/starcoder2_230m.py
#  - configs/starcoder2_300m.py

fastbackward_args:
  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  # We follow DeepSeekCoder paper for hyperparameters:
  # See Table 2 https://arxiv.org/pdf/2401.14196
  learning_rate: 5.3e-4
  # Total batch size is gpu_count=32 * batch_size=2 * gradient_accumulation_steps=16 = 1024
  batch_size: 1
  gradient_accumulation_steps: 16
  warmup_iters: 2000
  # See https://arxiv.org/pdf/2401.02954
  min_lr: 5.3e-5
  decay_lr: True
  # 320B tokens, one epoch over the dataset
  max_iters: 63_000  # 1024 * 63_000 = 64,512,000 samples.
  eval_interval: 5000
  block_size: 4096
  use_activation_checkpointing: True
  train_data_path: /mnt/efs/spark-data/shared/aug-stack/v1/datasets/starcoder2-4k-no-fim/merged/training
  eval_data_path: /mnt/efs/spark-data/shared/aug-stack/v1/datasets/starcoder2-4k-no-fim/merged/validation
  checkpoint_optimizer_state: True
  model_parallel_size: 1

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: starcoder2_230m_4K_pretrain
  # run_name: starcoder2_300m_4K_pretrain
  wandb_project: edvin-next-edit-speculative-decoding
