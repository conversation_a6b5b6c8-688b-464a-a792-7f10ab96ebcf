augment:
  gpu_count: 32
  podspec_path: 8xH100.yaml
  project_group: finetuning
  save_trial_best: 0
determined:
  labels:
  - llama3
  - fim-alignment
  - fastbackward
  max_restarts: 2
  perform_initial_validation: true
  project: edvin
  workspace: Dev
fastbackward_args:
  batch_size: 4
  block_size: 12032
  checkpoint: 5ceb5616-bf2b-45a4-8aa4-3b1b1d9faf52
  checkpoint_optimizer_state: true
  decay_lr: true
  eot_token_id: 0
  eval_data_path: /mnt/efs/augment/data/processed/next-edit/prv2-pr_grouped_10k/S26_16000p,R4_ethanol-K128,P18_star2_diff12_seq12k-pos_0.70-pad_12033/valid
  eval_interval: 500
  eval_items: 25600
  fim_middle_token_id: 2
  gradient_accumulation_steps: 2
  learning_rate: 1.0e-05
  log_interval: 100
  loss_mask_policy: negative_tokens
  lr_decay_iters: 20000
  max_iters: 20000
  min_lr: 1.0000000000000002e-06
  model_vocab_size: 51200
  pad_token_id: 49152
  restore_optimizer_state_from_checkpoint: false
  restore_training_metadata_from_checkpoint: false
  run_name: llama3_350m_4K_16K_fb-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-llama3_350m
  tokenizer_name: StarCoder2Tokenizer
  train_data_path: /mnt/efs/augment/data/processed/next-edit/prv2-pr_grouped_10k/S26_16000p,R4_ethanol-K128,P18_star2_diff12_seq12k-pos_0.70-pad_12033/train
  use_research_tokenizer: true
  visualize_logits_samples: 8
  wandb_project: edvin-next-edit-speculative-decoding
  warmup_iters: 200
fastbackward_configs:
- configs/llama_350m.py
