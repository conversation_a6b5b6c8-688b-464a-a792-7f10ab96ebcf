determined:
  description: "Finetuning of a StarCoder2-310m model for speculative decoding purposes."
  workspace: Dev
  labels: [model_name, "fim-alignment", "fastbackward"]
  project: edvin
  max_restarts: 2  # in case training crashes
  perform_initial_validationa: True

augment:
  project_group: finetuning
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
  - configs/starcoder2_310m.py

fastbackward_args:
  # name the run and put it in a project
  run_name: starcoder2_310m_4K+16K_pretrain_FT
  wandb_project: edvin-next-edit-speculative-decoding

  loss_mask_policy: negative_tokens
  rope_scaling_factor: 1.0
  # We follow DeepSeekoder paper for hyperparameters:
  # See Table 2 https://arxiv.org/pdf/2401.14196
  learning_rate: 5.3e-4
  # Total batch size is gpu_count=32 * batch_size=2 * gradient_accumulation_steps=16 = 1024
  batch_size: 2
  gradient_accumulation_steps: 16
  warmup_iters: 2000
  # See https://arxiv.org/pdf/2401.02954
  min_lr: 5.3e-5
  decay_lr: True
  # 320B tokens, one epoch over the dataset
  max_iters: 20_000  # 1024 * 63_000 = 64,512,000 samples.
  eval_interval: 2000
  block_size: 4096
  use_activation_checkpointing: True
  train_data_path: /mnt/efs/spark-data/shared/aug-stack/v1/datasets/starcoder2-16k-no-fim/merged/training
  eval_data_path: /mnt/efs/spark-data/shared/aug-stack/v1/datasets/starcoder2-16k-no-fim/merged/validation
  checkpoint_optimizer_state: True
  model_parallel_size: 1

  # determined knows how to deal with checkpoint ids that are stored in determined.
  checkpoint: 025fa212-335a-4545-b4c1-8352c4afbf97

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8
