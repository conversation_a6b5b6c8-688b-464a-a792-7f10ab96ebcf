# make sure you are logged into wandb -- '$ wandb login' . API key is found on the wandb website

# this determines which determined url to use
# DET_MASTER=https://determined.gcp-us1.r.augmentcode.com
DET_MASTER=https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud
cluster=CW
config_path=~/augment/experimental/edvin/training/configs/fastbackward/
config_name=starcoder2_450m/starcoder2_450m_4K_pt.yaml

# this is the config we want to run
python ~/augment/research/fastbackward/determined/launch.py \
 --config $config_path$config_name \
 --cluster $cluster
