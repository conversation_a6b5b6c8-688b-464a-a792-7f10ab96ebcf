#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status.

# Default values
default_name="llama3_700m_4K_pt-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k"
default_checkpoint="10996c24-7b8d-4db3-a2d1-1e71983ece08"
default_tp=1
default_root="/mnt/efs/augment/checkpoints/next-edit-spec-decoding"
default_calibration_data="/mnt/efs/augment/data/processed/next-edit/prv2-pr_grouped_10k/S26_16000p,R4_ethanol-K128,P18_star2_diff12_seq12k-pos_0.70-pad_12033/train"

# Use provided arguments or default values
NAME=${1:-$default_name}
CHECKPOINT=${2:-$default_checkpoint}
TP=${3:-$default_tp}
ROOT=${4:-$default_root}
CALIBRATION_DATA=${5:-$default_calibration_data}

LOCAL_DIR="${ROOT}/${NAME}-${CHECKPOINT}"
FF_DIR="${ROOT}/${NAME}--${CHECKPOINT}--FastForward"
FP8_DIR="${FF_DIR}-fp8"

echo "Processing checkpoint:"
echo "Name: $NAME"
echo "Checkpoint ID: $CHECKPOINT"
echo "Tensor Parallel: $TP"
echo "Root directory: $ROOT"
echo "Calibration data: $CALIBRATION_DATA"

# Step 1: Download the checkpoint
if [ -d "$LOCAL_DIR" ] && [ "$(ls -A "$LOCAL_DIR")" ]; then
    echo "Local directory already exists and is not empty. Skipping download."
else
    echo "Downloading checkpoint..."
    mkdir -p "$LOCAL_DIR"
    s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* "s3://dev-training-dai/$CHECKPOINT/" "$LOCAL_DIR/"

    if [ $? -ne 0 ]; then
        echo "Download failed. Exiting."
        exit 1
    fi
fi

# Step 2: Convert the checkpoint to FastForward format
if [ -d "$FF_DIR" ] && [ "$(ls -A "$FF_DIR")" ]; then
    echo "FastForward directory already exists and is not empty. Skipping conversion."
else
    echo "Converting checkpoint to FastForward format..."
    python /home/<USER>/augment/research/tools/ckp_converter/fbw2ffw_llama.py \
        --input_ckpt_dir "$LOCAL_DIR" \
        --output_ckpt_dir "$FF_DIR"

    if [ $? -ne 0 ]; then
        echo "Conversion failed. Exiting."
        exit 1
    fi
fi

# Step 3: Quantize the FastForward checkpoint
if [ -d "$FP8_DIR" ] && [ "$(ls -A "$FP8_DIR")" ]; then
    echo "FP8 directory already exists and is not empty. Skipping quantization."
else
    echo "Quantizing FastForward checkpoint..."
    # We need to calculate the SHA256 of the converted checkpoint
    if [ ! -f "$FF_DIR/info.json" ]; then
        echo "Error: info.json not found in $FF_DIR"
        exit 1
    fi

    CKPT_SHA256=$(jq -r '.manifestSha256' "$FF_DIR/info.json")

    if [ -z "$CKPT_SHA256" ]; then
        echo "Error: manifestSha256 not found in info.json"
        exit 1
    fi

    bash "$(dirname "$0")/quantize_llama_checkpoint.sh" "$FF_DIR" "$CKPT_SHA256" "$FP8_DIR" "$CALIBRATION_DATA"

    if [ $? -ne 0 ]; then
        echo "Quantization failed. Exiting."
        exit 1
    fi
fi

echo "Checkpoint processing completed successfully."
echo "Original checkpoint: $LOCAL_DIR"
echo "FastForward checkpoint: $FF_DIR"
echo "FP8 quantized checkpoint: $FP8_DIR"
