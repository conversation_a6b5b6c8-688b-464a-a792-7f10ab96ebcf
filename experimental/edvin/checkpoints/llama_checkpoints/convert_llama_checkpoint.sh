#!/bin/bash

# Default values
default_fbwd_ckpt_dir="/mnt/efs/augment/checkpoints/next-edit-spec-decoding/llama3_350m_4K_16K_fb-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-fa34a2ca-f49a-4102-84b4-cfaa372cc392"
default_ffwd_ckpt_dir="/mnt/efs/augment/checkpoints/next-edit-spec-decoding/llama3_350m_4K_16K_fb-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k--fa34a2ca-f49a-4102-84b4-cfaa372cc392--FastForward"

# Use provided arguments or default values
FBWD_CKPT_DIR=${1:-$default_fbwd_ckpt_dir}
FFWD_CKPT_DIR=${2:-$default_ffwd_ckpt_dir}

python /home/<USER>/augment/research/tools/ckp_converter/fbw2ffw_llama.py \
    --input_ckpt_dir "$FBWD_CKPT_DIR" \
    --output_ckpt_dir "$FFWD_CKPT_DIR"
