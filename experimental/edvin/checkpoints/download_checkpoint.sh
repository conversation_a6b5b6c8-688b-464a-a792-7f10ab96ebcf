#!/bin/bash

set -e

NAME=starcoder2_450m_4K_pt-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k
CHECKPOINT=9231ccae-1227-4aa1-a1c0-7176921ec806
TP=1

ROOT=/mnt/efs/augment/checkpoints/next-edit-spec-decoding
LOCAL_DIR=${ROOT}/${NAME}-${CHECKPOINT}
# FF_DIR=${ROOT}/${NAME}--${CHECKPOINT}--FastForward

echo $LOCAL_DIR
# echo $FF_DIR

mkdir -p $LOCAL_DIR

# Download the checkpoint -- requires having the s3cmd tool (installed through ~/research/research-init.sh)
s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/$CHECKPOINT/ $LOCAL_DIR/
