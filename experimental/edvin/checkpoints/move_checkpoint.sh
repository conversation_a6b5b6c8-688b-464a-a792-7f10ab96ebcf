#!/bin/bash

set -e

TP=1

NAME=llama3_700m_4K_pt
CHECKPOINT=e5df6893-c714-47dd-bd82-a27a89d57a8e
ROOT=/mnt/efs/augment/checkpoints/next-edit-spec-decoding
LOCAL_DIR=${ROOT}/${NAME}-${CHECKPOINT}
# LOCAL_DIR=${ROOT}/${NAME}-${CHECKPOINT}
# FF_DIR=${ROOT}/${NAME}--${CHECKPOINT}--FastForward

echo $LOCAL_DIR
# echo $FF_DIR

mkdir -p $LOCAL_DIR

# Download the checkpoint -- requires having the s3cmd tool and a config file (https://cloud.coreweave.com/namespaces/tenant-augment-eng/tokens/object-storage)
s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/$CHECKPOINT/ $LOCAL_DIR/

# sync the checkpoint to GCP
gsutil -m rsync -x ".*\\/code\\/.*" -r $LOCAL_DIR/ gs://determined-checkpoints-us1/gcp-us1/$CHECKPOINT/
