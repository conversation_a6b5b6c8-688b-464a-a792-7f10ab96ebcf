#!/bin/bash

# Default values
default_ckpt_path="/mnt/efs/augment/checkpoints/next-edit-spec-decoding/llama3_350m_4K_16K_fb-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k--fa34a2ca-f49a-4102-84b4-cfaa372cc392--FastForward"
default_ckpt_sha256='cf26382b9dc3ddcda093392275d6b2ef17d3c5cef3471a2321cc539f4a29021a'
default_out_path="/mnt/efs/augment/checkpoints/next-edit-spec-decoding/llama3_350m_4K_16K_fb-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k--fa34a2ca-f49a-4102-84b4-cfaa372cc392--FastForward-fp8"
default_calibration_data="/mnt/efs/augment/data/processed/next-edit/prv2-pr_grouped_10k/S26_16000p,R4_ethanol-K128,P18_star2_diff12_seq12k-pos_0.70-pad_12033/train"
default_model_size="starcoder2-350m"

# Use provided arguments or default values
ckpt_path=${1:-$default_ckpt_path}
ckpt_sha256=${2:-$default_ckpt_sha256}
out_path=${3:-$default_out_path}
calibration_data=${4:-$default_calibration_data}
model_size=${5:-default_model_size}

python /home/<USER>/augment/base/fastforward/starcoder/quantize_starcoder2.py \
    --calibration-steps 200 \
    --log-to-stdout \
    --ckpt-path "${ckpt_path}" \
    --ckpt-sha256 "${ckpt_sha256}" \
    --out-path "${out_path}" \
    --calibration-data "${calibration_data}" \
    --model-size "${model_size}" \
    # --max-seq-len 12288 \
