#!/bin/bash

# Default values
DEFAULT_INPUT_CKPT="/mnt/efs/augment/checkpoints/starcoder2_230m/starcoder2-230m-4K-no-fim-12K-finetuned-fb"
DEFAULT_OUTPUT_CKPT="/mnt/efs/augment/checkpoints/starcoder2_230m/starcoder2-230m-4K-no-fim-12K-finetuned-ffw"
DEFAULT_MODEL_NAME="starcoder2-230m"

# Parse command-line arguments
INPUT_CKPT=${1:-$DEFAULT_INPUT_CKPT}
OUTPUT_CKPT=${2:-$DEFAULT_OUTPUT_CKPT}
MODEL_NAME=${3:-$DEFAULT_MODEL_NAME}

# Print the values being used
echo "Input checkpoint: $INPUT_CKPT"
echo "Output checkpoint: $OUTPUT_CKPT"
echo "Model name: $MODEL_NAME"

# Run the conversion script
python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
    --inp-ckpt="$INPUT_CKPT" \
    --out-ckpt="$OUTPUT_CKPT" \
    --model-name="$MODEL_NAME"

# Check if the conversion was successful
if [ $? -eq 0 ]; then
    echo "Conversion completed successfully."
else
    echo "Conversion failed."
    exit 1
fi
