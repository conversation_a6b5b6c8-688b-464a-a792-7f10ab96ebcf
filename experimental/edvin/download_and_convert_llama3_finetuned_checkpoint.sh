#!/bin/bash

set -e

# NAME=llama3-base-next-edit-v1-32ksteps
# CHECKPOINT=5af5e1ac-7c8b-4ea7-948d-8d2f1b1c7683
# TP=8

# NAME=llama3-base-next-edit-v1-2M-samples-s1dot11--64k-steps
# CHECKPOINT=48c1f7bc-cd86-43c7-8713-1f8dc0ce965c
# TP=8

NAME=llama3_350m_4K_16K_fb-S26-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k
CHECKPOINT=fa34a2ca-f49a-4102-84b4-cfaa372cc392
TP=1

ROOT=/mnt/efs/augment/checkpoints/next-edit-gen
LOCAL_DIR=${ROOT}/${NAME}-${CHECKPOINT}
FF_DIR=${ROOT}/${NAME}--${CHECKPOINT}--FastForward

mkdir -p $LOCAL_DIR
s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/$CHECKPOINT/ $LOCAL_DIR/

mkdir -p $FF_DIR
python ~/augment/experimental/yuri/convert_fb_checkpoint_to_ff.py --input_ckpt_dir $LOCAL_DIR --output_ckpt_dir ${FF_DIR} -m $TP
