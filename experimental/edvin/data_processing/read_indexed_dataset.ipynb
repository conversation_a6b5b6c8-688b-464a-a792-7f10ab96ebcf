{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "from megatron.data import indexed_dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "15,864,463\n", "type(ds): <class 'numpy.uint16'>\n"]}], "source": ["# path = \"/mnt/efs/augment/data/processed/rag/dataset/test_retrieval/dataset\"\n", "# path = \"/mnt/efs/augment/data/processed/rag/longcon/4k5far1k5near/dataset\"\n", "# path = \"/mnt/efs/spark-data/shared/aug-stack/v1/datasets/starcoder2-4k-no-fim/\"\n", "\n", "path = \"/mnt/efs/spark-data/shared/aug-stack/v1/datasets/starcoder2-16k-no-fim/merged/training\"\n", "ds = indexed_dataset.make_dataset(path, impl=\"mmap\", skip_warmup=True)\n", "print(f\"{len(ds):,}\")\n", "print(f\"type(ds): {ds.dtype}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["259,939,226,255.0 tokens\n", "16,385.0 tokens per sample\n"]}], "source": ["file_size_bytes = os.path.getsize(path + \".bin\")\n", "bytes_per_token = 2  # starcoder2 uses int32\n", "total_tokens = file_size_bytes / bytes_per_token\n", "tokens_per_sample = total_tokens / len(ds)\n", "print(f\"{total_tokens:,} tokens\")\n", "print(f\"{tokens_per_sample:,} tokens per sample\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["15,492.6 iterations per epoch\n"]}], "source": ["batch_size = 1024\n", "max_iter = len(ds) / batch_size\n", "print(f\"{round(max_iter, 1):,} iterations per epoch\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}