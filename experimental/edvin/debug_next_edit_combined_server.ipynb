{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-08-12 22:20:46,817 Loading faiss with AVX2 support.\n", "2024-08-12 22:20:46,831 Successfully loaded faiss with AVX2 support.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["kube_config_path not provided and default location (/home/<USER>/.config/kube/config:/home/<USER>/.kube/config:/run/user/1000/kubeconfig.d/aug-user-edvin.conf:/home/<USER>/.config/kube/config:/home/<USER>/.kube/config:/run/user/1000/kubeconfig.d/aug-user-edvin.conf) does not exist. Using inCluster Config. This might not work.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-12 22:20:52,429 HTTP Request: GET https://api.gradio.app/gradio-messaging/en \"HTTP/1.1 200 OK\"\n", "2024-08-12 22:20:54,027 Reloaded tiktoken model from /mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-8B-Instruct/tokenizer.model\n", "2024-08-12 22:20:54,028 #words: 128256 - BOS ID: 128000 - EOS ID: 128001\n", "2024-08-12 22:20:54,888 No distributed envvars detected. Running as single-GPU. Consider running with `torchrun` instead.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-12 22:20:57,391 Auto-registering a function or class with an argument of type <class 'torch.nn.modules.module.Module'> which isn't JSON-serializable. This field will be configured via reference.\n", "/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning: Unknown type mean at Config.reduction: typing.Literal['mean', 'batchmean'] It's advised to pass the correct marshmallow type to `mm_field`.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning: Unknown type batchmean at Config.reduction: typing.Literal['mean', 'batchmean'] It's advised to pass the correct marshmallow type to `mm_field`.\n", "  warnings.warn(\n", "/opt/conda/lib/python3.11/site-packages/dataclasses_json/mm.py:288: UserWarning: Unknown type typing.Literal['mean', 'batchmean'] at Config.reduction: typing.Literal['mean', 'batchmean'] It's advised to pass the correct marshmallow type to `mm_field`.\n", "  warnings.warn(\n", "2024-08-12 22:20:57,414 Creating a Transformer model.\n", "2024-08-12 22:20:57,414 Attention config:\n", "GenericAttnSpec(hidden_dim=2048, n_heads=16, n_kv_heads=1, norm_type='layernorm', pos_embed_type='absolute', bias=True)\n", "2024-08-12 22:20:57,415 First layer FFN config:\n", "MlpSpec(hidden_dim=2048, bias=True)\n", "2024-08-12 22:20:57,415 Other layers' FFN config:\n", "MlpSpec(hidden_dim=2048, bias=True)\n", "2024-08-12 22:21:03,626 Creating a Transformer model.\n", "2024-08-12 22:21:03,628 Attention config:\n", "GenericAttnSpec(hidden_dim=2048, n_heads=16, n_kv_heads=1, norm_type='layernorm', pos_embed_type='absolute', bias=True)\n", "2024-08-12 22:21:03,628 First layer FFN config:\n", "MlpSpec(hidden_dim=2048, bias=True)\n", "2024-08-12 22:21:03,628 Other layers' FFN config:\n", "MlpSpec(hidden_dim=2048, bias=True)\n", "Embedding batches: 100%|██████████| 1/1 [00:00<00:00,  2.85it/s]\n", "2024-08-12 22:21:13,282 Loading model starcoder2_fastforward...\n", "2024-08-12 22:21:13,283 Using base.fastforward\n", "2024-08-12 22:21:13,283 Loading a StarCoder2 model starcoder2-7b onto 1 processes.\n", "2024-08-12 22:21:13,934 Checkpoint at '/mnt/efs/augment/checkpoints/next-edit-gen/S1.6_keep_most-R1.0_starethanol_fixed-P1.7_context9-100K_repos-starcoder2_7b' has expected hash.\n", "2024-08-12 22:21:15,434 Checkpoint at '/mnt/efs/augment/checkpoints/next-edit-gen/S1.6_keep_most-R1.0_starethanol_fixed-P1.7_context9-100K_repos-starcoder2_7b' has expected hash.\n", "2024-08-12 22:21:19,932 Checkpoint at '/mnt/efs/augment/checkpoints/next-edit-gen/S1.6_keep_most-R1.0_starethanol_fixed-P1.7_context9-100K_repos-starcoder2_7b' has expected hash.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id 803fc51e-bee7-4c93-a4d5-a431dbb8078c.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-12 22:21:22,258 Loaded the model in 9.0 seconds\n"]}], "source": ["from experimental.edvin.start_next_edit_combined_server import (\n", "    build_combined_system,\n", ")\n", "\n", "system = build_combined_system()\n", "system.edit_gen_system.retriever = None  # this disables retrieval for the gen system\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mimport<PERSON>b\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON>earch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconstants\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AUGMENT_EFS_ROOT\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m launch_model_server\n\u001b[1;32m      6\u001b[0m \u001b[38;5;66;03m# always reload the model_server module to avoid API endpoint errors.\u001b[39;00m\n\u001b[1;32m      7\u001b[0m importlib\u001b[38;5;241m.\u001b[39mreload(launch_model_server)\n", "File \u001b[0;32m~/augment/research/model_server/launch_model_server.py:102\u001b[0m\n\u001b[1;32m    100\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON>ness\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mabs_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AbstractSystem\n\u001b[1;32m    101\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01ma<PERSON>_token_db\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AuthTokenDatabase\n\u001b[0;32m--> 102\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mblob_list_manager\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BlobListManager\n\u001b[1;32m    103\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mchat_handlers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m handle_chat_request\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcode_instruct_handlers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m    105\u001b[0m     handle_code_edit_request,\n\u001b[1;32m    106\u001b[0m     handle_log_edit_status,\n\u001b[1;32m    107\u001b[0m )\n", "File \u001b[0;32m~/augment/research/model_server/blob_list_manager.py:22\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mthreading\u001b[39;00m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Iterable, Optional\n\u001b[0;32m---> 22\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_server_requests\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Blobs, CompletionRequest\n\u001b[1;32m     25\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mblobs_from_completion_request\u001b[39m(req: CompletionRequest) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m Blobs:\n\u001b[1;32m     26\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Extract the blobs object from a Completion Request.\"\"\"\u001b[39;00m\n", "File \u001b[0;32m~/augment/research/model_server/model_server_requests.py:15\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnext_edit_location_prompt_input\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FileLocation\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DocumentId, Scored\n\u001b[0;32m---> 15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mharness\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnext_edit_combined_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ScoredFileHunk\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnext_edits\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdiagnostics\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Diagnostic\n\u001b[1;32m     19\u001b[0m \u001b[38;5;129m@dataclass\u001b[39m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mBlobs\u001b[39;00m:\n", "File \u001b[0;32m~/augment/research/eval/harness/systems/next_edit_combined_system.py:25\u001b[0m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FileLogger\n\u001b[1;32m     21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mharness\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mabs_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     22\u001b[0m     AbstractSystem,\n\u001b[1;32m     23\u001b[0m     register_system,\n\u001b[1;32m     24\u001b[0m )\n\u001b[0;32m---> 25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mharness\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnext_edit_gen_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     26\u001b[0m     EditGenSystemInput,\n\u001b[1;32m     27\u001b[0m     EditGenSystemOutput,\n\u001b[1;32m     28\u001b[0m     NextEditGenSystem,\n\u001b[1;32m     29\u001b[0m )\n\u001b[1;32m     30\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mharness\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnext_edit_location_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     31\u001b[0m     BasicNextEditLocationSystem,\n\u001b[1;32m     32\u001b[0m     HeuristicNextEditLocationSystem,\n\u001b[1;32m     33\u001b[0m     NextEditLocationOutput,\n\u001b[1;32m     34\u001b[0m )\n\u001b[1;32m     35\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mharness\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mnext_edit_reranker_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     36\u001b[0m     NextEditRerankerOutput,\n\u001b[1;32m     37\u001b[0m     NextEditRerankerSystem,\n\u001b[1;32m     38\u001b[0m )\n", "File \u001b[0;32m~/augment/research/eval/harness/systems/next_edit_gen_system.py:36\u001b[0m\n\u001b[1;32m     34\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01meval\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msystems\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mabs_system\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m AbstractSystem, register_system\n\u001b[1;32m     35\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllm_apis\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mchat_utils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ChatClient\n\u001b[0;32m---> 36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward_llama_models\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m LLAMA_FastForwardModel\n\u001b[1;32m     37\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward_models\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m     38\u001b[0m     FastForwardModel,\n\u001b[1;32m     39\u001b[0m     GenerationCanceledError,\n\u001b[1;32m     40\u001b[0m     StarCoder2_FastForward,\n\u001b[1;32m     41\u001b[0m     StarCoder_FastForward,\n\u001b[1;32m     42\u001b[0m )\n\u001b[1;32m     43\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mresearch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmeta_model\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m GenerationOptions\n", "File \u001b[0;32m~/augment/research/models/fastforward_llama_models.py:46\u001b[0m\n\u001b[1;32m     39\u001b[0m \u001b[38;5;66;03m# TODO(guy) tear this out once we have flash attention working in research.\u001b[39;00m\n\u001b[1;32m     40\u001b[0m \u001b[38;5;66;03m# We don't have flash attention installed in research, so we need to mock it out.\u001b[39;00m\n\u001b[1;32m     41\u001b[0m \u001b[38;5;66;03m# This makes \"from third_party.flash_attention import flash_attn\" work as a noop.\u001b[39;00m\n\u001b[1;32m     42\u001b[0m \u001b[38;5;66;03m# Then we turn off flash attention after importing cached_attention.\u001b[39;00m\n\u001b[1;32m     43\u001b[0m \u001b[38;5;66;03m# sys.path.append(f\"{AUGMENT_ROOT}/research/mock\")\u001b[39;00m\n\u001b[1;32m     45\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbase\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m cached_attention, fwd_utils  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[0;32m---> 46\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbase\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllama\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m fwd_llama, fwd_llama_fp8  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     47\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbase\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllama\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m model_specs \u001b[38;5;28;01mas\u001b[39;00m llama_model_specs  \u001b[38;5;66;03m# noqa: E402\u001b[39;00m\n\u001b[1;32m     49\u001b[0m logger \u001b[38;5;241m=\u001b[39m logging\u001b[38;5;241m.\u001b[39mgetLogger(\u001b[38;5;18m__file__\u001b[39m)\n", "File \u001b[0;32m~/augment/base/fastforward/llama/fwd_llama_fp8.py:12\u001b[0m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcached_attention\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Attention\n\u001b[1;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mba<PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcheckpoints\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m save_load\n\u001b[0;32m---> 12\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbase\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfp8\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FP8Linear, FP8RmsNorm\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbase\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m WordEmbeddings\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mbase\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfastforward\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlayers_fp8\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m LlamaTransformerBlock\n", "File \u001b[0;32m~/augment/base/fastforward/fp8.py:33\u001b[0m\n\u001b[1;32m     29\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNVTE_INSTALL_PATH\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m os\u001b[38;5;241m.\u001b[39menviron:\n\u001b[1;32m     30\u001b[0m         os\u001b[38;5;241m.\u001b[39menviron[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNVTE_INSTALL_PATH\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m     31\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m../rules_python~~pip~python_pip_311_transformer_engine/site-packages\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     32\u001b[0m         )\n\u001b[0;32m---> 33\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtransformer_engine\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpytorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcpp_extensions\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mtexcpp\u001b[39;00m\n\u001b[1;32m     34\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtransformer_engine_extensions\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mtex\u001b[39;00m\n\u001b[1;32m     35\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtransformer_engine\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpytorch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mconstants\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m TE_DType\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/transformer_engine/__init__.py:6\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Copyright (c) 2022-2023, NVIDIA CORPORATION & AFFILIATES. All rights reserved.\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# See LICENSE for license information.\u001b[39;00m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;124;03m\"\"\"Top level package\"\"\"\u001b[39;00m\n\u001b[0;32m----> 6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m common\n\u001b[1;32m      8\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pytorch\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/transformer_engine/common/__init__.py:67\u001b[0m\n\u001b[1;32m     63\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m ctypes\u001b[38;5;241m.\u001b[39mCDLL(dll_path, mode\u001b[38;5;241m=\u001b[39mctypes\u001b[38;5;241m.\u001b[39mRTLD_GLOBAL)\n\u001b[1;32m     64\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m---> 67\u001b[0m _TE_LIB_CTYPES \u001b[38;5;241m=\u001b[39m \u001b[43m_load_library\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     68\u001b[0m _UB_LIB_CTYPES \u001b[38;5;241m=\u001b[39m _load_userbuffers()\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/transformer_engine/common/__init__.py:40\u001b[0m, in \u001b[0;36m_load_library\u001b[0;34m()\u001b[0m\n\u001b[1;32m     38\u001b[0m     \u001b[38;5;28;01<PERSON>raise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnsupported operating system (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msystem\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m)\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     39\u001b[0m lib_name \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlibtransformer_engine.\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m+\u001b[39m extension\n\u001b[0;32m---> 40\u001b[0m dll_path \u001b[38;5;241m=\u001b[39m \u001b[43mget_te_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     41\u001b[0m dll_path \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(dll_path, lib_name)\n\u001b[1;32m     43\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ctypes\u001b[38;5;241m.\u001b[39mCDLL(dll_path, mode\u001b[38;5;241m=\u001b[39mctypes\u001b[38;5;241m.\u001b[39mRTLD_GLOBAL)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/transformer_engine/common/__init__.py:22\u001b[0m, in \u001b[0;36mget_te_path\u001b[0;34m()\u001b[0m\n\u001b[1;32m     19\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m os\u001b[38;5;241m.\u001b[39menviron[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNVTE_INSTALL_PATH\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m     21\u001b[0m command \u001b[38;5;241m=\u001b[39m [sys\u001b[38;5;241m.\u001b[39mexecutable, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-m\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpip\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mshow\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtransformer_engine\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m---> 22\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[43msubprocess\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcapture_output\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcheck\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m     23\u001b[0m result \u001b[38;5;241m=\u001b[39m result\u001b[38;5;241m.\u001b[39mstdout\u001b[38;5;241m.\u001b[39mreplace(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\u001b[38;5;241m.\u001b[39msplit(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     24\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m result[result\u001b[38;5;241m.\u001b[39mindex(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLocation\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;241m+\u001b[39m \u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mstrip()\n", "File \u001b[0;32m/opt/conda/lib/python3.11/subprocess.py:550\u001b[0m, in \u001b[0;36mrun\u001b[0;34m(input, capture_output, timeout, check, *popenargs, **kwargs)\u001b[0m\n\u001b[1;32m    548\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m <PERSON><PERSON>(\u001b[38;5;241m*\u001b[39mpopenargs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs) \u001b[38;5;28;01mas\u001b[39;00m process:\n\u001b[1;32m    549\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 550\u001b[0m         stdout, stderr \u001b[38;5;241m=\u001b[39m \u001b[43mprocess\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcommunicate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    551\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m TimeoutExpired \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[1;32m    552\u001b[0m         process\u001b[38;5;241m.\u001b[39mkill()\n", "File \u001b[0;32m/opt/conda/lib/python3.11/subprocess.py:1209\u001b[0m, in \u001b[0;36mPopen.communicate\u001b[0;34m(self, input, timeout)\u001b[0m\n\u001b[1;32m   1206\u001b[0m     endtime \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1208\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1209\u001b[0m     stdout, stderr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_communicate\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mendtime\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1210\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[1;32m   1211\u001b[0m     \u001b[38;5;66;03m# https://bugs.python.org/issue25942\u001b[39;00m\n\u001b[1;32m   1212\u001b[0m     \u001b[38;5;66;03m# See the detailed comment in .wait().\u001b[39;00m\n\u001b[1;32m   1213\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m/opt/conda/lib/python3.11/subprocess.py:2108\u001b[0m, in \u001b[0;36mPopen._communicate\u001b[0;34m(self, input, endtime, orig_timeout)\u001b[0m\n\u001b[1;32m   2101\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timeout(endtime, orig_timeout,\n\u001b[1;32m   2102\u001b[0m                         stdout, stderr,\n\u001b[1;32m   2103\u001b[0m                         skip_check_and_raise\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m   2104\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(  \u001b[38;5;66;03m# Impossible :)\u001b[39;00m\n\u001b[1;32m   2105\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m_check_timeout(..., skip_check_and_raise=True) \u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m   2106\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mfailed to raise TimeoutExpired.\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m-> 2108\u001b[0m ready \u001b[38;5;241m=\u001b[39m \u001b[43mselector\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mselect\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   2109\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_timeout(endtime, orig_timeout, stdout, stderr)\n\u001b[1;32m   2111\u001b[0m \u001b[38;5;66;03m# XXX Rewrite these to use non-blocking I/O on the file\u001b[39;00m\n\u001b[1;32m   2112\u001b[0m \u001b[38;5;66;03m# objects; they are no longer using C stdio!\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.11/selectors.py:415\u001b[0m, in \u001b[0;36m_PollLikeSelector.select\u001b[0;34m(self, timeout)\u001b[0m\n\u001b[1;32m    413\u001b[0m ready \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m    414\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 415\u001b[0m     fd_event_list \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_selector\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpoll\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mInterruptedError\u001b[39;00m:\n\u001b[1;32m    417\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m ready\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import importlib\n", "\n", "from research.core.constants import AUGMENT_EFS_ROOT\n", "from research.model_server import launch_model_server\n", "\n", "# always reload the model_server module to avoid API endpoint errors.\n", "importlib.reload(launch_model_server)\n", "\n", "\n", "log_dir = AUGMENT_EFS_ROOT / \"user/jiayi\" / \"model_server_logs\"\n", "args = [\"--port\", \"5001\", \"--log_dir\", str(log_dir)]\n", "# Comment out the line below if you are only using this server locally and are not planning to make it available to others.\n", "args.extend([\"--host\", \"0.0.0.0\"])\n", "launch_model_server.main(\n", "    system,\n", "    input_args=args,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}