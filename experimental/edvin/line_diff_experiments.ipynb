{"cells": [{"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from base.diff_utils.str_diff import (\n", "    precise_line_diff,\n", "    precise_char_diff,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", "    DeletedSpan,\n", "    ModSpan,\n", "    AddedSpan,\n", "    NoopSpan,\n", "    <PERSON>ff<PERSON><PERSON>,\n", "    align_spans_to_word_boundaries,\n", ")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["def get_span_name(span: DiffSpan):\n", "    if isinstance(span, AddedSpan):\n", "        return \"AddedSpan\"\n", "    elif isinstance(span, DeletedSpan):\n", "        return \"DeletedSpan\"\n", "    elif isinstance(span, ModSpan):\n", "        return \"ModSpan\"\n", "    else:\n", "        return \"NoopSpan\"\n", "\n", "\n", "def pretty_print_diff(diff: StrDiff):\n", "    for span in diff.spans:\n", "        print(\n", "            f\"{get_span_name(span)}(\\n{span.before}\\n----------------------\\n{span.after}\\n\"\n", "        )\n", "        print(\"=\" * 120)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ModSpan(\n", "blobs: (request. blobs && blobsToBlobsPayload (request.blobs)) ?? {\n", "\n", "----------------------\n", "blobs: blobsToBlobsPayload (request.blobs),\n", "\n", "\n", "========================================================================================================================\n", "DeletedSpan(\n", "checkpoint_id: undefined,\n", "added_blobs: [],\n", "deleted_blobs: [],\n", "},\n", "\n", "----------------------\n", "\n", "\n", "========================================================================================================================\n"]}], "source": ["before = \"\"\"\\\n", "blobs: (request. blobs && blobsToBlobsPayload (request.blobs)) ?? {\n", "checkpoint_id: undefined,\n", "added_blobs: [],\n", "deleted_blobs: [],\n", "},\n", "\"\"\"\n", "\n", "after = \"\"\"\\\n", "blobs: blobsToBlobsPayload (request.blobs),\n", "\"\"\"\n", "\n", "line_diff = precise_line_diff(before, after)\n", "\n", "pretty_print_diff(line_diff)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NoopSpan(\n", "blobs:\n", "----------------------\n", "blobs:\n", "\n", "========================================================================================================================\n", "DeletedSpan(\n", " (request. blobs &&\n", "----------------------\n", "\n", "\n", "========================================================================================================================\n", "NoopSpan(\n", " blobsToBlobsPayload (request.blobs)\n", "----------------------\n", " blobsToBlobsPayload (request.blobs)\n", "\n", "========================================================================================================================\n", "ModSpan(\n", ") ?? {\n", "----------------------\n", ",\n", "\n", "========================================================================================================================\n", "NoopSpan(\n", "\n", "\n", "----------------------\n", "\n", "\n", "\n", "========================================================================================================================\n", "DeletedSpan(\n", "checkpoint_id: undefined,\n", "added_blobs: [],\n", "deleted_blobs: [],\n", "},\n", "\n", "----------------------\n", "\n", "\n", "========================================================================================================================\n"]}], "source": ["char_diff = precise_char_diff(before, after)\n", "pretty_print_diff(char_diff)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON>opS<PERSON>('blobs:'),\n", " DeletedSpan(' (request. blobs &&'),\n", " NoopSpan(' blobsToBlobsPayload (request.blobs)'),\n", " ModSpan(before=') ?? {', after=','),\n", " NoopSpan('\\n'),\n", " DeletedSpan('checkpoint_id: undefined,\\nadded_blobs: [],\\ndeleted_blobs: [],\\n},\\n'))"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["align_spans_to_word_boundaries(char_diff.spans)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}