{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# README\n", "\n", "You need to run `experimental/jiayi/next_edit/inspect_next_edit_hindsight.ipynb` first to generate the dataset."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.next_edit_hindsight_location_eval_task import (\n", "    convert_dataset_with_ground_truths_to_task_inputs,\n", "    NextEditHindsightLocationEvalTask,\n", "    NextEditLocationSystem,\n", ")\n", "from research.eval.harness.systems.gold_system import NextEditLocationGoldSystem\n", "from base.logging.console_logging import setup_console_logging\n", "import yaml\n", "from research.eval.harness.systems.next_edit_location_system import (\n", "    BasicNextEditLocationSystem,\n", ")\n", "from research.core.constants import AUGMENT_ROOT\n", "from pathlib import Path\n", "\n", "from research.eval.harness.utils import read_jsonl_zst\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import pytz\n", "\n", "# setup_console_logging()\n", "\n", "dataset_name = \"next_hunk-dogfood_2025_01_22_5hours_10s.pkl\"\n", "\n", "result_tmp_file = Path(f\"~/tmp/{dataset_name}.pkl\").expanduser()\n", "\n", "input_path = f\"/home/<USER>/tmp/{dataset_name}\"\n", "output_path = f\"/home/<USER>/tmp/{dataset_name.replace('.pkl', '_task_inputs.pkl')}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["convert_dataset_with_ground_truths_to_task_inputs(\n", "    input_path=input_path,\n", "    output_path=output_path,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hindsight_eval_task = NextEditHindsightLocationEvalTask(\n", "    datset_path=output_path,\n", "    limit_examples=1000,\n", "    top_ks=(1, 3, 8, 32, 256),\n", "    drop_instructions=True,\n", ")\n", "\n", "pacific_tz = pytz.timezone(\"America/Los_Angeles\")\n", "current_time = datetime.now(pacific_tz)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def init_raven_location_system() -> BasicNextEditLocationSystem:\n", "    # Load the raven config\n", "    config_path = (\n", "        AUGMENT_ROOT / \"research/model_server/configs/next_edit_location_raven.yaml\"\n", "    )\n", "    with config_path.open(encoding=\"utf8\") as f:\n", "        config = yaml.safe_load(f)\n", "\n", "    # Initialize the system using the factory method\n", "    system = BasicNextEditLocationSystem.from_yaml_config(config)\n", "\n", "    # Load the model/embeddings\n", "    system.load()\n", "\n", "    return system\n", "\n", "\n", "raven_location_system: NextEditLocationSystem = init_raven_location_system()\n", "raven_eval_output_path: Path = Path(\n", "    f\"~/tmp/raven_{raven_location_system.name}_{current_time.strftime('%Y-%m-%d_%H-%M')}_eval\"\n", ").expanduser()\n", "hindsight_eval_task.run(raven_location_system, raven_eval_output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## <PERSON><PERSON><PERSON> - commenting this out based on discussion with <PERSON><PERSON> that the ethanol system is broken.\n", "\n", "# def init_ethanol_location_system() -> BasicNextEditLocationSystem:\n", "#     # Load the ethanol config\n", "#     config_path = (\n", "#         AUGMENT_ROOT / \"research/model_server/configs/next_edit_location_ethanol.yaml\"\n", "#     )\n", "#     with config_path.open(encoding=\"utf8\") as f:\n", "#         config = yaml.safe_load(f)\n", "\n", "#     # Initialize the system using the factory method\n", "#     system = BasicNextEditLocationSystem.from_yaml_config(config)\n", "\n", "#     # Load the model/embeddings\n", "#     system.load()\n", "\n", "#     return system\n", "\n", "\n", "# ethanol_location_system: NextEditLocationSystem = init_ethanol_location_system()\n", "# ethanol_eval_output_path: Path = Path(\n", "#     f\"~/tmp/ethanol_{ethanol_location_system.name}_{current_time.strftime('%Y-%m-%d_%H-%M')}_eval\"\n", "# ).expanduser()\n", "# hindsight_eval_task.run(ethanol_location_system, ethanol_eval_output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def merge_dicts(dict_list: list[dict]) -> dict:\n", "    return {k: v for d in dict_list for k, v in d.items()}\n", "\n", "\n", "# Moogi - Ignore ehtanol system\n", "# ethanol_metrics = merge_dicts(\n", "#     read_jsonl_zst(Path(f\"{ethanol_eval_output_path}/metrics.jsonl.zst\"))\n", "# )\n", "raven_metrics = merge_dicts(\n", "    read_jsonl_zst(Path(f\"{raven_eval_output_path}/metrics.jsonl.zst\"))\n", ")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def plot_recall(\n", "    recall: list[float],\n", "    ax: plt.Axes,\n", "    cutoff: int = 256,\n", "    label: str = \"\",\n", "    points_to_label: list[int] = [1, 3, 8, 32, 256],\n", "):\n", "    ax.plot(recall[:cutoff], label=label)\n", "    for x in points_to_label:\n", "        if x > cutoff:\n", "            continue\n", "        y = recall[x - 1]\n", "        ax.annotate(\n", "            f\"{y:.3f}\",\n", "            (x, y),\n", "            xytext=(0, 10),  # 10 points above\n", "            textcoords=\"offset points\",\n", "            ha=\"center\",  # horizontal alignment\n", "            va=\"bottom\",  # vertical alignment\n", "            fontsize=10,\n", "        )\n", "\n", "\n", "def plot_metrics(metrics: dict[str, list[float]], ax: plt.Axes, cutoff: int = 256):\n", "    for k, v in metrics.items():\n", "        if \"recall\" in k:\n", "            plot_recall(v, ax, cutoff, k)\n", "\n", "    ax.grid(visible=True)\n", "    ax.set_xlabel(\"k\")\n", "    ax.set_ylabel(\"Recall\")\n", "    ax.legend()\n", "\n", "\n", "def print_metrics(\n", "    metrics: dict[str, list[float]], k_values: list[int] = [1, 3, 8, 32, 256]\n", "):\n", "    for k, v in metrics.items():\n", "        if \"recall\" in k:\n", "            continue\n", "        print(f\"{k}: {v}\")\n", "\n", "    for k in k_values:\n", "        print(\n", "            f\"k={k}: {metrics['current_file_recall'][k-1]:.4f}, {metrics['non_current_file_recall'][k-1]:.4f}\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print the metrics overview\n", "print_metrics(raven_metrics)\n", "\n", "# plot the metrics\n", "fig, ax = plt.subplots(figsize=(10, 5), ncols=1, nrows=1)\n", "ax.set_title(\"<PERSON>\")\n", "plot_metrics(raven_metrics, ax, cutoff=32)\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Moogi - Ignore ehtanol system\n", "\n", "# # print the metrics overview\n", "# print_metrics(ethanol_metrics)\n", "\n", "# # plot the metrics\n", "# fig, ax = plt.subplots(figsize=(10, 5), ncols=1, nrows=1)\n", "# ax.set_title(\"Ethan<PERSON>\")\n", "# plot_metrics(ethanol_metrics, ax, cutoff=32)\n", "# fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}