{"cells": [{"cell_type": "markdown", "id": "7fb27b941602401d91542211134fc71a", "metadata": {}, "source": ["# Hindsight Data Analysis for Completion Truncation"]}, {"cell_type": "markdown", "id": "acae54e37e7d407bbb7b55eff062a284", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": 1, "id": "9a63283cbaf04dbcab1f6479b197f3a8", "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "from typing import Any, Dict, List, Optional, Tuple\n", "\n", "# Placeholder for HindsightDatum structure (adjust as needed based on actual data format)\n", "from dataclasses import dataclass\n", "\n", "from base.datasets.hindsight_completion import (\n", "    HindsightCompletionDatum,\n", ")  # Assuming this is the correct type\n", "from research.tools.analyze_hindsight import (\n", "    read_dataset as load_hindsight_data,\n", ")  # Function to load data\n", "from research.eval.harness.metrics import (\n", "    compute_code_complete_metrics,\n", "    CodeCompleteMetrics,\n", ")  # For stats\n", "from base.prompt_format_completion.ender_prompt_formatter import (\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    EnderPromptFormatterConfig,\n", ")  # For prompt formatting\n", "from services.completion_host.single_model_server.truncate_at_parenthesis import (\n", "    truncate_at_parenthesis,\n", "    TruncationResult,\n", "    _ENABLE_TRUNCATE_AT_PARENTHESIS,\n", "    _TRUNCATE_AT_PARENTHESIS_RANDOMIZE,\n", ")  # Truncation logic\n", "from base.languages import guess_language, LanguageId\n", "import base.feature_flags as base_feature_flags\n", "\n", "\n", "@dataclass\n", "class AnalysisResult:\n", "    request_id: str\n", "    original_completion: str\n", "    ground_truth: str\n", "    could_truncate: bool\n", "    was_truncated_in_logic: bool\n", "    truncated_completion_segment: Optional[str] = None\n", "    new_prompt: Optional[str] = None\n", "    new_completion: Optional[str] = None\n", "    original_metrics: Optional[CodeCompleteMetrics] = None\n", "    new_metrics_vs_ground_truth: Optional[CodeCompleteMetrics] = None\n", "    new_metrics_vs_original_completion: Optional[CodeCompleteMetrics] = None"]}, {"cell_type": "markdown", "id": "8dd0d8092fe74a7c96281538738b07e2", "metadata": {}, "source": ["## 2. Configuration"]}, {"cell_type": "code", "execution_count": null, "id": "72eea5119410473aa328ad9291626812", "metadata": {}, "outputs": [], "source": ["OUTPUT_FILE_PATH = \"experimental/pranay/truncation_analysis_results.jsonl\"\n", "\n", "# EnderPromptFormatter configuration (adjust as needed)\n", "# ender_formatter_config = EnderPromptFormatterConfig()\n", "# ender_formatter = EnderPromptFormatter(config=ender_formatter_config, tokenizer=None) # Tokenizer might be needed for actual prompt generation"]}, {"cell_type": "markdown", "id": "8edb47106e1a46a883d545849b8ab81b", "metadata": {}, "source": ["## 3. <PERSON><PERSON> Data"]}, {"cell_type": "code", "execution_count": 2, "id": "10185d26023b46108eb7d9f57d49d2b3", "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "\n", "\n", "def load_data(file_path: str):\n", "    try:\n", "        data = read_jsonl_zst(file_path)\n", "        print(f\"Loaded {len(data)} records from {file_path}\")\n", "        return data\n", "    except Exception as e:\n", "        print(f\"Error loading data from {file_path}: {e}\")\n", "        return {}\n", "\n", "\n", "HINDSIGHT_FILE_PATH = \"/mnt/efs/augment/user/jeff/hindsight/2025-04-12-5d-v1.6/dogfood-shard/data.jsonl.zst\"\n", "hindsight_data = load_data(HINDSIGHT_FILE_PATH)"]}, {"cell_type": "code", "execution_count": null, "id": "9a07912f", "metadata": {}, "outputs": [], "source": ["def print_dict_keys(d, indent=0):\n", "    \"\"\"Print all keys and nested keys of a dictionary.\"\"\"\n", "    for key, value in d.items():\n", "        print(\"  \" * indent + str(key))\n", "        if isinstance(value, dict):\n", "            print_dict_keys(value, indent + 1)\n", "\n", "\n", "# Usage:\n", "print_dict_keys(hindsight_data[0])"]}, {"cell_type": "code", "execution_count": null, "id": "c9d1e079", "metadata": {}, "outputs": [], "source": ["inference_tokens = []\n", "\n", "response_tokens = []\n", "response_text = []\n", "for i in range(len(hindsight_data)):\n", "    token_ids = hindsight_data[i][\"completion\"][\"inference_response\"][\"token_ids\"][:-1]\n", "    tokens = hindsight_data[i][\"completion\"][\"inference_response\"][\"tokens\"][:-1]\n", "    inference_tokens.append(len(token_ids))\n", "\n", "    datum_response_tokens = hindsight_data[i][\"completion\"][\"response\"][\"tokens\"]\n", "    datum_response_text = \"\".join(datum_response_tokens)\n", "\n", "    response_tokens.append(len(datum_response_tokens))\n", "    response_text.append(len(datum_response_text))\n", "\n", "inference_total_tokens = sum(inference_tokens)\n", "reponse_total_tokens = sum(response_tokens)\n", "reponse_total_text = sum(response_text)\n", "print(f\"Total inference tokens: {inference_total_tokens}\")\n", "print(f\"Total response tokens: {reponse_total_tokens}\")\n", "print(f\"Total response text: {reponse_total_text}\")\n", "\n", "# ~4 chars per token"]}, {"cell_type": "code", "execution_count": 33, "id": "7877d621", "metadata": {}, "outputs": [], "source": ["print(response_tokens[19])\n", "print(response_text[19])"]}, {"cell_type": "code", "execution_count": null, "id": "75afb0ac", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# number of samples above 64 tokens\n", "num_samples_above_64 = sum(1 for x in inference_tokens if x > 64)\n", "print(f\"Number of samples above 64 tokens: {num_samples_above_64}\")\n", "print(\n", "    f\"Number of samples below 64 tokens: {len(inference_tokens) - num_samples_above_64}\"\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "c038ad95", "metadata": {}, "outputs": [], "source": ["token_ids = hindsight_data[19][\"completion\"][\"inference_response\"][\"token_ids\"]\n", "tokens = hindsight_data[19][\"completion\"][\"inference_response\"][\"tokens\"]\n", "response = hindsight_data[19][\"completion\"][\"response\"][\"text\"]\n", "response_tokens = hindsight_data[19][\"completion\"][\"response\"][\"tokens\"]\n", "print(tokens)\n", "print(response_tokens)\n", "print(response)"]}, {"cell_type": "markdown", "id": "8763a12b2bbd4a93a75aff182afb95dc", "metadata": {}, "source": ["## 4. Define Truncation Logic (Copied and adapted)"]}, {"cell_type": "code", "execution_count": null, "id": "7623eae2785240b9bd12b16a66d81610", "metadata": {}, "outputs": [], "source": ["def check_truncation_from_logic(\n", "    completion_text: str,\n", "    file_path: str,\n", "    session_id: str,\n", "    context: base_feature_flags.Context,\n", ") -> TruncationResult:\n", "    \"\"\"Uses the exact same logic from truncate_at_parenthesis.py to check for truncation.\"\"\"\n", "    return truncate_at_parenthesis(completion_text, file_path, session_id, context)"]}, {"cell_type": "markdown", "id": "7cdc8c89c7104fffa095e18ddfef8986", "metadata": {}, "source": ["## 5. Define Prompt Generation"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}