{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 1. Download Request Data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looking at requests that are bunched up with query:\n", "```\n", "jsonPayload.tenant_name=\"i1-vanguard6\"\n", "resource.labels.container_name=\"completion\"\n", "jsonPayload.message=~\"Generating|Completion failed: DeadlineExceededError.*\"\n", "severity=\"ERROR\"\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import logging\n", "from base.datasets.gcs_client import GCSRequestInsightFetcher\n", "from base.datasets import tenants\n", "from base.datasets.completion_dataset_gcs import _Row\n", "from base.datasets.completion_conversion import from_completion_request_proto\n", "from datetime import timezone\n", "\n", "logging.basicConfig(\n", "    format=\"%(asctime)s.%(msecs)03d [%(levelname)-8s] %(message)s\",\n", "    datefmt=\"%Y-%m-%d %H:%M:%S\",\n", "    level=logging.DEBUG,\n", ")\n", "logger = logging.getLogger(__name__)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from base.languages.language_guesser import guess_language\n", "\n", "guess_language(\"a.py\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from base.diff_utils.retriever_util_completion import (\n", "    ReplacementText,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from base.datasets.completion import CompletionRequest\n", "from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "import json\n", "from typing import Any, Sequence, Dict\n", "from research.data.rag.hindsight_common import convert_edit_events\n", "from base.prompt_format.common import PromptChunk\n", "from base.diff_utils.diff_utils import File\n", "from base.blob_names.python.blob_names import BlobName, FilePath\n", "from research.data.rag.hindsight_common import (\n", "    diff_hunk_to_prompt_chunk,\n", "    recent_chunk_to_prompt_chunk,\n", "    chunk_to_prompt_chunk,\n", ")\n", "\n", "\n", "class ResearchFileContentProvider(FileContentProvider):\n", "    \"\"\"A file retriever that uses the cache. Cache must return files in the same order as the input keys.\"\"\"\n", "\n", "    def __init__(self, cache):\n", "        self.cache = cache\n", "\n", "    def retrieve_files(\n", "        self,\n", "        paths: Dict[<PERSON><PERSON><PERSON><PERSON><PERSON>, FilePath],\n", "        expected: bool,\n", "    ) -> Dict[<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> | None]:\n", "        blob_names = paths.keys()\n", "        blob_contents = self.cache.get(blob_names)\n", "        results = {}\n", "        # We assume that the cache returns files in the same order as the input keys.\n", "        for blob_name, blob_content in zip(blob_names, blob_contents):\n", "            results[blob_name] = (\n", "                File(path=str(blob_content.path), contents=blob_content.content)\n", "                if blob_content\n", "                else None\n", "            )\n", "        return results\n", "\n", "\n", "def make_generate_retrieved_chunks_from_hindsight_problem():\n", "    from typing import Iterator, Mapping, Callable\n", "    from base.datasets.gcs_blob_cache import GCSBlobCache\n", "    from google.cloud import storage\n", "    from base.datasets.gcp_creds import get_gcp_creds\n", "    from base.datasets.tenants import get_tenant\n", "    from base.logging.secret_logging import get_safe_logger\n", "    from base.diff_utils.retriever_util_completion import (\n", "        EditEventConstructionInput,\n", "        filter_replacement_text,\n", "        get_files_before_replacement,\n", "        apply_replacements,\n", "        get_squashable_edits,\n", "    )\n", "    from research.data.rag.retrieval_utils import (\n", "        serialize_retrieved_prompt_chunks,\n", "    )\n", "    from base.diff_utils.diff_formatter import format_file_changes_with_ranges\n", "    from research.retrieval.types import Document\n", "    from research.core.model_input import ModelInput\n", "\n", "    @dataclass\n", "    class GenerateRetrievedChunksFromHindsightProblem:\n", "        \"\"\"A class to generate retrieved chunks from hindsight problems.\"\"\"\n", "\n", "        def __init__(\n", "            self,\n", "            num_retrieved_chunks: int,\n", "            tenant_name: str,\n", "            diff_context_lines: int = 3,\n", "            big_event_lines: int = 8,\n", "            use_smart_header: bool = True,\n", "            filter_duplicated_file_paths: bool = True,\n", "            max_total_changed_chars: int = 5000,\n", "        ):\n", "            self.num_retrieved_chunks = num_retrieved_chunks\n", "            self.tenant_name = tenant_name\n", "            self._diff_context_lines = diff_context_lines\n", "            self._big_event_lines = big_event_lines\n", "            self._use_smart_header = use_smart_header\n", "            self._filter_duplicated_file_paths = filter_duplicated_file_paths\n", "            self._max_total_changed_chars = max_total_changed_chars\n", "            self._safe_logger = get_safe_logger(\n", "                logger=logger, secret_logs_enabled=False\n", "            )\n", "            self._cache: GCSBlobCache | None = None\n", "            self.counter = 0\n", "\n", "        @property\n", "        def cache(self) -> GCSBlobCache:\n", "            \"\"\"The cache.\"\"\"\n", "            self.__init_cache()\n", "            assert self._cache is not None\n", "            return self._cache\n", "\n", "        def __init_cache(self):\n", "            \"\"\"Initialize the cache.\"\"\"\n", "            if self._cache is not None:\n", "                return\n", "            gcp_creds, _ = get_gcp_creds()\n", "            tenant = get_tenant(self.tenant_name)\n", "            client = storage.Client(project=tenant.project_id, credentials=gcp_creds)\n", "            bucket = client.bucket(tenant.blob_bucket_name)\n", "            self._cache = GCSBlobCache(\n", "                bucket=bucket,\n", "                bucket_prefix=tenant.blob_bucket_prefix,\n", "                max_size_bytes=1_000_000_000,  # 1GB cache size\n", "                num_threads=10,\n", "            )\n", "\n", "        def _get_current_version_of_outdated_files(\n", "            self,\n", "            replacements: Sequence[ReplacementText],\n", "            file_content_provider: FileContentProvider,\n", "        ):\n", "            replacements = filter_replacement_text(replacements)\n", "            files_before_replacements = get_files_before_replacement(\n", "                replacements, file_content_provider, expected=True\n", "            )\n", "\n", "            reconstructed_files, replacement_error_files = apply_replacements(\n", "                replacements, files_before_replacements, self._safe_logger\n", "            )\n", "\n", "            for error in replacement_error_files:\n", "                self._safe_logger.warn(f\"Ignoring error {error.error_type} for file: \")\n", "                self._safe_logger.secret_warn(\n", "                    f\"{error.file_path=}, {error.file_blob_name=}\"\n", "                )\n", "\n", "            return reconstructed_files\n", "\n", "        def process_edit_event(\n", "            self,\n", "            request: CompletionRequest,\n", "        ) -> list[PromptChunk]:\n", "            edit_event_construction_input = EditEventConstructionInput(\n", "                active_file_path=request.path,\n", "                active_file_prefix=request.prefix,\n", "                active_file_suffix=request.suffix,\n", "                recent_changes=request.recency_info.recent_changes\n", "                if request.recency_info\n", "                else [],\n", "                edit_events=convert_edit_events(\n", "                    request.edit_events if request.edit_events else []\n", "                ),\n", "            )\n", "\n", "            research_file_content_provider = ResearchFileContentProvider(\n", "                cache=self.cache,\n", "            )\n", "\n", "            # Step 1. Get the current version of all files mentioned in recent changes\n", "            logger.info(\"Start self._get_current_version_of_outdated_files\")\n", "            reconstructed_files = self._get_current_version_of_outdated_files(\n", "                edit_event_construction_input.recent_changes,\n", "                research_file_content_provider,\n", "            )\n", "            logger.info(\"End self._get_current_version_of_outdated_files\")\n", "\n", "            # This active file may not be correct, but we add it to the reconstructed files in case it is and edit events rely on it.\n", "            # If it is incorrect, the blob name will not match and it will be ignored.\n", "            reconstructed_files.append(edit_event_construction_input.active_file)\n", "\n", "            reconstructed_blob_names_to_files = {\n", "                file.blob_name: file for file in reconstructed_files\n", "            }\n", "\n", "            # Step 2. Get the current version of all files mentioned in edit events\n", "            logger.info(\"Start get_squashable_edits\")\n", "            squashable_edits = get_squashable_edits(\n", "                edit_events=edit_event_construction_input.edit_events,\n", "                reconstructed_files=reconstructed_blob_names_to_files,\n", "                file_content_provider=research_file_content_provider,\n", "                safe_logger=self._safe_logger,\n", "            )\n", "            logger.info(\"End get_squashable_edits\")\n", "\n", "            # Step 3. Convert edit events to file changes\n", "            logger.info(\"Start convert_edit_events_to_modified_files\")\n", "            recency_changes = squashable_edits.convert_edit_events_to_modified_files(\n", "                safe_logger=self._safe_logger,\n", "                is_source_file=lambda _: True,\n", "                max_total_changed_chars=self._max_total_changed_chars,\n", "                big_event_lines=self._big_event_lines,\n", "            )\n", "            logger.info(\"End convert_edit_events_to_modified_files\")\n", "\n", "            logger.info(\n", "                f\"Start format_file_changes_with_ranges for {len(recency_changes)}\"\n", "            )\n", "            all_diff_hunks = format_file_changes_with_ranges(\n", "                recency_changes,\n", "                diff_context_lines=self._diff_context_lines,\n", "                diff_filter=lambda path: path.suffix != \".ipynb\",\n", "                use_smart_header=self._use_smart_header,\n", "            )\n", "            logger.info(\"End format_file_changes_with_ranges\")\n", "\n", "            logger.info(f\"Start diff_hunk_to_prompt_chunk for {len(all_diff_hunks)}\")\n", "            all_edit_prompt_chunks = [\n", "                diff_hunk_to_prompt_chunk(diff_hunk, self._filter_duplicated_file_paths)\n", "                for group in all_diff_hunks\n", "                for diff_hunk in group\n", "            ]\n", "            logger.info(\"End diff_hunk_to_prompt_chunk\")\n", "\n", "            # Edit events are ordered from oldest to newest, so let's reverse the order to get the most recent changes first\n", "            all_edit_prompt_chunks.reverse()\n", "            edit_prompt_chunks = all_edit_prompt_chunks[: self.num_retrieved_chunks]\n", "\n", "            return edit_prompt_chunks\n", "\n", "        def process_recency_chunks(\n", "            self,\n", "            request: CompletionRequest,\n", "        ) -> list[PromptChunk]:\n", "            recent_changes = (\n", "                request.recency_info.recent_changes if request.recency_info else []\n", "            )\n", "\n", "            # Unsure if we want to limit to [:10] here, but service code in `recency_retriever.py` is, so we will for now.\n", "            recent_changes_chunks = [\n", "                recent_chunk_to_prompt_chunk(recent_change=recent_change)\n", "                for recent_change in recent_changes[:10]\n", "            ]\n", "\n", "            recent_changes_chunks = recent_changes_chunks[: self.num_retrieved_chunks]\n", "            return recent_changes_chunks\n", "\n", "        def process_single_hindsight_problem(\n", "            self, hindsight_problem: HindsightCompletionDatum\n", "        ) -> dict | None:\n", "            request = hindsight_problem.completion.request\n", "            output: dict[str, Any] = dict(\n", "                request_id=hindsight_problem.completion.request_id,\n", "                prefix=request.prefix,\n", "                prefix_begin=request.position.prefix_begin if request.position else 0,\n", "                suffix=request.suffix,\n", "                ground_truth=hindsight_problem.ground_truth,\n", "                file_path=request.path,\n", "            )\n", "\n", "            assert (\n", "                hindsight_problem.completion.inference_response is not None\n", "            ), \"Inference response is required\"\n", "            output[\"inference_response_token_ids\"] = json.dumps(\n", "                hindsight_problem.completion.inference_response.token_ids\n", "            )\n", "\n", "            blob_names = request.blob_names\n", "            blob_contents = self.cache.get(blob_names)\n", "\n", "            # Step 1: Construct the list of documents in the workspace.\n", "            documents: list[Document] = []\n", "            for blob_name, blob_content in zip(blob_names, blob_contents):\n", "                # GCS failed to find or download the blob.\n", "                if blob_content is None:\n", "                    self._safe_logger.error(\n", "                        f\"Failed to find blob name {blob_name} in request {hindsight_problem.completion.request_id}\"\n", "                    )\n", "                    return None\n", "\n", "                document = Document.new(\n", "                    text=blob_content.content,\n", "                    path=str(blob_content.path),\n", "                )\n", "                documents.append(document)\n", "\n", "            # Validate that all documents have unique paths.\n", "            # This could happen if the user has multiple workspaces open, and it is easier to filter out. This does not impact many samples.\n", "            try:\n", "                num_unique_paths = len(set(doc.path for doc in documents))\n", "                assert (\n", "                    num_unique_paths == len(documents)\n", "                ), f\"Expected unique paths, found {num_unique_paths} unique paths out of {len(documents)} documents. request_id={hindsight_problem.completion.request_id}\"\n", "            except Exception as e:\n", "                self._safe_logger.error(f\"Failed to validate documents: {e}\")\n", "                return None\n", "\n", "            doc_ids = {doc.id for doc in documents}\n", "            _ = ModelInput(\n", "                prefix=request.prefix,\n", "                suffix=request.suffix,\n", "                path=request.path,\n", "                doc_ids=doc_ids,\n", "                extra={\n", "                    \"ground_truth\": hindsight_problem.ground_truth,\n", "                },\n", "            )\n", "\n", "            # Step 3: Retrieve chunks from each retrieval database.\n", "            retrieved_chunks: list[PromptChunk] = []\n", "            retrieved_chunk_to_score: dict[PromptChunk, float | None] = {}\n", "\n", "            # Step 4: Process edit event\n", "            try:\n", "                edit_chunks = self.process_edit_event(request)\n", "            except Exception as e:\n", "                self._safe_logger.warn(f\"Failed to process edit event: {e}\")\n", "                edit_chunks = []\n", "            retrieved_chunks.extend(edit_chunks)\n", "\n", "            # Step 5: Process recency chunks\n", "            recent_changes_chunks = self.process_recency_chunks(request)\n", "            retrieved_chunks.extend(recent_changes_chunks)\n", "\n", "            output[\"retrieved_chunks\"] = serialize_retrieved_prompt_chunks(\n", "                retrieved_chunks\n", "            )\n", "            output[\"retrieved_scores\"] = json.dumps(\n", "                [\n", "                    retrieved_chunk_to_score.get(chunk, None)\n", "                    for chunk in retrieved_chunks\n", "                ]\n", "            )\n", "            output[\"recency_info\"] = (\n", "                request.recency_info.to_json() if request.recency_info else None\n", "            )\n", "\n", "            return output\n", "\n", "        def __call__(\n", "            self, hindsight_problem: HindsightCompletionDatum\n", "        ) -> Iterator[dict]:\n", "            \"\"\"Convert each FIM problem in the repo to be retrieval-augmented data samples.\n", "\n", "            Args:\n", "                repo: The repository, which is a list of files, to process.\n", "                fim_problems: The FIM problems to augment.\n", "                config: The configuration object.\n", "                tokenizer: The tokenizer to use.\n", "                retrieval_database: The retrieval database to use.\n", "\n", "            Returns:\n", "                A generator of processed rows.\n", "            \"\"\"\n", "            if self.counter % 100 == 0:\n", "                self._safe_logger.info(f\"Processed {self.counter} hindsight problems.\")\n", "            result = self.process_single_hindsight_problem(hindsight_problem)\n", "            self.counter += 1\n", "            if result is not None:\n", "                yield result\n", "\n", "    return GenerateRetrievedChunksFromHindsightProblem"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["REQUEST = (\"ef9881fe-88ef-48bd-82c6-e9bb09f4ed4e\", tenants.VANGUARD_I1_6)\n", "REQUEST = (\"5d10a966-df3f-42de-a457-2f1a51a4099c\", tenants.VANGUARD_I1_0)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import base.datasets.tenants as tenants\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "from google.cloud import storage\n", "\n", "gcp_creds, _ = get_gcp_creds()\n", "storage_client = storage.Client(project=REQUEST[1].project_id, credentials=gcp_creds)\n", "request_fetcher = GCSRequestInsightFetcher.from_tenant(REQUEST[1])\n", "\n", "request = request_fetcher.get_request(\n", "    REQUEST[0], request_event_names=frozenset({\"completion_host_request\"})\n", ").events[0]\n", "completion_request = from_completion_request_proto(\n", "    request.completion_host_request,\n", "    request.time.ToDatetime(tzinfo=timezone.utc),\n", "    REQUEST[0],\n", ")\n", "print(completion_request)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["gen_retrieved_chunks = make_generate_retrieved_chunks_from_hindsight_problem()(\n", "    32, REQUEST[1].name\n", ")\n", "retrieved_chunks = gen_retrieved_chunks.process_edit_event(completion_request)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["retrieved_chunks = gen_retrieved_chunks.process_edit_event(completion_request)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}