{"cells": [{"cell_type": "code", "execution_count": 4, "id": "102b5221", "metadata": {}, "outputs": [], "source": ["import json\n", "from research.eval.harness.utils import read_jsonl\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "# i1_file = \"/home/<USER>/augment/experimental/pranay/BQ Results May 29 2025 - I1.json\"\n", "# prod_file = \"/home/<USER>/augment/experimental/pranay/BQ Results May 29 2025 - Not I0:I1.json\"\n", "\n", "new_i1_file = (\n", "    \"/home/<USER>/augment/experimental/pranay/BQ Results Apr 1 - Jun 1 2025 - i1.json\"\n", ")\n", "new_prod_file = \"/home/<USER>/augment/experimental/pranay/BQ Results Apr 1 - Jun 1 2025 - prod.json\"\n", "\n", "# df_i1 = pd.read_json(i1_file, lines=True)\n", "# df_prod = pd.read_json(prod_file, lines=True)\n", "\n", "df_new_i1 = pd.read_json(new_i1_file, lines=True)\n", "df_new_prod = pd.read_json(new_prod_file, lines=True)\n", "\n", "# prod_file = \"/home/<USER>/augment/experimental/pranay/BQ Results June 5 2025.json\"\n", "# df_prod = pd.read_json(prod_file, lines=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "18569fca", "metadata": {}, "outputs": [], "source": ["def plot_data(df, title):\n", "    def extract_char_count_midpoint(bin_str):\n", "        \"\"\"Extract midpoint of character count bin for plotting\"\"\"\n", "        if bin_str == \"All\":\n", "            return None\n", "        start, end = map(int, bin_str.split(\"-\"))\n", "        return (start + end) / 2\n", "\n", "    df_plot = df[df[\"character_count_bin\"] != \"All\"].copy()\n", "    df_plot[\"char_count_midpoint\"] = df_plot[\"character_count_bin\"].apply(\n", "        extract_char_count_midpoint\n", "    )\n", "    df_overall = df[df[\"character_count_bin\"] == \"All\"].copy()\n", "\n", "    df_plot[\"num_requests\"] = pd.to_numeric(df_plot[\"num_requests\"])\n", "    df_overall[\"num_requests\"] = pd.to_numeric(df_overall[\"num_requests\"])\n", "\n", "    df_plot[\"percentage_of_total\"] = df_plot.groupby([\"user_agent\", \"model_name\"])[\n", "        \"num_requests\"\n", "    ].transform(lambda x: (x / x.sum()) * 100)\n", "\n", "    min_acceptance = min(df[\"acceptance_rate\"])\n", "    max_acceptance = max(df[\"acceptance_rate\"])\n", "    y_margin = (max_acceptance - min_acceptance) * 0.1\n", "    y_min = max(0, min_acceptance - y_margin)\n", "    y_max = max_acceptance + y_margin\n", "\n", "    user_agents = df_plot[\"user_agent\"].unique()\n", "\n", "    fig, axes = plt.subplots(2, len(user_agents), figsize=(15, 10))\n", "    if len(user_agents) == 1:\n", "        axes = axes.reshape(-1, 1)\n", "\n", "    for i, user_agent in enumerate(user_agents):\n", "        ax_main = axes[0, i]\n", "        ax_diff = axes[1, i]\n", "\n", "        agent_data = df_plot[df_plot[\"user_agent\"] == user_agent]\n", "        agent_overall = df_overall[df_overall[\"user_agent\"] == user_agent]\n", "\n", "        models = sorted(agent_data[\"model_name\"].unique())\n", "\n", "        for model in models:\n", "            model_data = agent_data[agent_data[\"model_name\"] == model].sort_values(\n", "                \"char_count_midpoint\"\n", "            )\n", "            marker_sizes = model_data[\"percentage_of_total\"] * 10\n", "\n", "            scatter = ax_main.scatter(\n", "                model_data[\"char_count_midpoint\"],\n", "                model_data[\"acceptance_rate\"],\n", "                s=marker_sizes,\n", "                alpha=0.7,\n", "                label=f\"{model}\",\n", "            )\n", "\n", "            ax_main.plot(\n", "                model_data[\"char_count_midpoint\"],\n", "                model_data[\"acceptance_rate\"],\n", "                color=scatter.get_facecolors()[0],\n", "                linewidth=2,\n", "                alpha=0.8,\n", "            )\n", "\n", "            for _, row in model_data.iterrows():\n", "                ax_main.annotate(\n", "                    f'{row[\"percentage_of_total\"]:.1f}%',\n", "                    (row[\"char_count_midpoint\"], row[\"acceptance_rate\"]),\n", "                    xytext=(5, 5),\n", "                    textcoords=\"offset points\",\n", "                    fontsize=8,\n", "                    alpha=0.8,\n", "                )\n", "\n", "            overall_data = agent_overall[agent_overall[\"model_name\"] == model]\n", "            if len(overall_data) > 0:\n", "                overall_rate = overall_data[\"acceptance_rate\"].iloc[0]\n", "                ax_main.axhline(\n", "                    y=overall_rate,\n", "                    linestyle=\"--\",\n", "                    alpha=0.7,\n", "                    linewidth=1.5,\n", "                    label=f\"{model} (Overall avg: {overall_rate:.3f})\",\n", "                )\n", "\n", "        if len(models) == 2:\n", "            v1_1_model = next((m for m in models if \"v1-1\" in m), models[0])\n", "            v3_2_model = next((m for m in models if \"v3-2\" in m), models[1])\n", "\n", "            v1_1_data = agent_data[agent_data[\"model_name\"] == v1_1_model].set_index(\n", "                \"char_count_midpoint\"\n", "            )\n", "            v3_2_data = agent_data[agent_data[\"model_name\"] == v3_2_model].set_index(\n", "                \"char_count_midpoint\"\n", "            )\n", "\n", "            common_bins = v1_1_data.index.intersection(v3_2_data.index)\n", "\n", "            differences = []\n", "            bin_points = []\n", "            for bin_midpoint in sorted(common_bins):\n", "                rate_v1_1 = v1_1_data.loc[bin_midpoint, \"acceptance_rate\"]\n", "                rate_v3_2 = v3_2_data.loc[bin_midpoint, \"acceptance_rate\"]\n", "                diff = rate_v3_2 - rate_v1_1\n", "                differences.append(diff)\n", "                bin_points.append(bin_midpoint)\n", "\n", "            ax_diff.plot(bin_points, differences, \"o-\", linewidth=2, markersize=6)\n", "            ax_diff.axhline(y=0, color=\"black\", linestyle=\"-\", alpha=0.3)\n", "            ax_diff.set_xlabel(\"Character Count (bin midpoint)\")\n", "            ax_diff.set_ylabel(f\"Difference\\n({v3_2_model} - {v1_1_model})\")\n", "            ax_diff.set_title(f\"Acceptance Rate Difference\\n{user_agent}\")\n", "            ax_diff.grid(True, alpha=0.3)\n", "\n", "        ax_main.set_xlabel(\"Character Count (bin midpoint)\")\n", "        ax_main.set_ylabel(\"Acceptance Rate\")\n", "        ax_main.set_title(f\"Acceptance Rate vs Character Count\\n{user_agent} - {title}\")\n", "        ax_main.legend()\n", "        ax_main.grid(True, alpha=0.3)\n", "        ax_main.set_ylim(y_min, y_max)\n", "\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 7, "id": "b144873a", "metadata": {}, "outputs": [], "source": ["plot_data(df_new_prod, \"new prod data\")"]}, {"cell_type": "code", "execution_count": 8, "id": "10d27162", "metadata": {}, "outputs": [], "source": ["plot_data(df_new_i1, \"new i1 data\")"]}, {"cell_type": "code", "execution_count": null, "id": "39a695ba", "metadata": {}, "outputs": [], "source": ["# plot_data(df_prod, \"i1 data\")"]}, {"cell_type": "code", "execution_count": null, "id": "c82876e6", "metadata": {}, "outputs": [], "source": ["# plot_data(df_prod, \"prod data\")"]}, {"cell_type": "code", "execution_count": null, "id": "bfbf96eb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}