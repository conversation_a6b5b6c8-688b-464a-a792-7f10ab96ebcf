#!/usr/bin/env python3
"""
Model Win Rate Visualization
Creates a win rate chart with Qwelden v1-1 as the baseline (0%).
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import seaborn as sns

# Set style
plt.style.use("seaborn-v0_8-whitegrid")
sns.set_palette("husl")

# Model data with internal names, win rates (EM), and EM@1 scores
models_data = [
    {"name": "Qwelden v3-2", "win_rate": 4.6, "em_at_1": 3.9, "label": "+Edit Events"},
    {"name": "Qwelden v2-1", "win_rate": 1.8, "em_at_1": 1.3, "label": "+RLDB"},
    {
        "name": "Qwelden v1",
        "win_rate": 0.0,
        "em_at_1": 0.0,
        "label": "+Better Base Model",
    },  # Baseline
    {
        "name": "Elden v4-0c",
        "win_rate": -0.2,
        "em_at_1": -1.5,
        "label": "+Smart Chunking",
    },
    {
        "name": "Elden v4",
        "win_rate": -1.4,
        "em_at_1": -1.9,
        "label": "+Better Data Curation",
    },
    {"name": "Elden v3", "win_rate": -1.5, "em_at_1": -2.1, "label": "Initial Model"},
]


def create_em_at_1_chart():
    """Create a horizontal bar chart showing EM@1 scores."""

    # Use models in original order (no sorting)
    chart_models = models_data

    # Extract data for plotting - use labels directly, handle duplicates by adding index
    model_labels = []
    em_scores = [model["em_at_1"] for model in chart_models]

    # Handle duplicate labels by adding a counter
    label_counts = {}
    for model in chart_models:
        label = model["label"]
        if label in label_counts:
            label_counts[label] += 1
            model_labels.append(f"{label} ({label_counts[label]})")
        else:
            label_counts[label] = 1
            model_labels.append(label)

    # Create color map based on EM@1 performance (same scale as win rate)
    colors = []
    for score in em_scores:
        if score > 3:
            colors.append("#2E8B57")  # Dark green for strong improvements
        elif score > 1:
            colors.append("#90EE90")  # Light green for good improvements
        elif score > -0.5:
            colors.append("#FFD700")  # Gold for marginal/baseline
        elif score > -1:
            colors.append("#FFA500")  # Orange for slight regression
        else:
            colors.append("#FF6347")  # Red for regression

    # Create the plot
    fig, ax = plt.subplots(figsize=(12, 8))

    # Create horizontal bar chart
    bars = ax.barh(
        model_labels,
        em_scores,
        color=colors,
        alpha=0.8,
        edgecolor="black",
        linewidth=0.5,
    )

    # Add value labels on bars
    for bar, score in zip(bars, em_scores):
        width = bar.get_width()
        ax.text(
            width + 0.2,
            bar.get_y() + bar.get_height() / 2,
            f"{score:.1f}%",
            ha="left",
            va="center",
            fontweight="bold",
            fontsize=10,
        )

    # Customize the plot
    ax.set_xlabel("Win Rate vs Baseline Model", fontsize=12, fontweight="bold")
    ax.set_ylabel("Model", fontsize=12, fontweight="bold")
    ax.set_title(
        "Model Win Rates Against Baseline Model (based on Exact Match)",
        fontsize=14,
        fontweight="bold",
        pad=20,
    )

    # Add vertical line at 0 (baseline)
    ax.axvline(x=0, color="black", linestyle="--", alpha=0.7, linewidth=1)

    # Set x-axis limits
    x_min, x_max = min(em_scores) - 1, max(em_scores) + 2
    ax.set_xlim(x_min, x_max)

    # Add grid
    ax.grid(True, axis="x", alpha=0.3)

    # Adjust layout
    plt.tight_layout()

    return fig, ax


if __name__ == "__main__":
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create EM@1 chart
    print("Creating EM@1 chart...")
    fig2, ax2 = create_em_at_1_chart()

    # Save the EM@1 chart
    filename2 = f"model_em_{timestamp}.png"
    fig2.savefig(filename2, dpi=300, bbox_inches="tight")
    print(f"EM@1 chart saved as: {filename2}")

    # Show the plots
    plt.show()

    # Print summary table
    print("\n" + "=" * 70)
    print("MODEL PERFORMANCE SUMMARY")
    print("=" * 70)
    print(f"{'Model':<15} {'Win Rate':<10} {'EM@1':<8} {'Key Features'}")
    print("-" * 70)

    for model in models_data:
        print(
            f"{model['name']:<15} {model['win_rate']:>+6.1f}%   {model['em_at_1']:>5.1f}%   {model['label']}"
        )
