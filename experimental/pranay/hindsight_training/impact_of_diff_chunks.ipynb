{"cells": [{"cell_type": "code", "execution_count": 1, "id": "55d78a99", "metadata": {}, "outputs": [], "source": ["def get_empty_query(model, client, start_date, end_date):\n", "    query = f\"\"\"\n", "  WITH request_data AS (\n", "    SELECT \n", "      m.request_id as request_id,\n", "      m.model_name,\n", "      SPLIT(rm.user_agent, '/')[OFFSET(0)] as user_agent,\n", "    FROM \n", "      `system-services-prod.us_staging_request_insight_analytics_dataset.human_request_metadata` rm\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.model` m\n", "    ON \n", "      rm.request_id = m.request_id\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.completion_response` c_resp\n", "    ON \n", "      rm.request_id = c_resp.request_id\n", "    JOIN \n", "      `system-services-prod.us_staging_request_insight_search_nonenterprise_dataset.completion_post_process` cpp\n", "    ON \n", "      rm.request_id = cpp.request_id\n", "    WHERE \n", "      rm.tenant = \"dogfood-shard\"\n", "      AND (m.model_name = \"{model}\")\n", "      AND SPLIT(rm.user_agent, '/')[OFFSET(0)] = '{client}'\n", "      AND m.time > TIMESTAMP(\"{start_date}\")\n", "      AND m.time <= TIMESTAMP(\"{end_date}\")\n", "  )\n", "\n", "  SELECT DISTINCT\n", "    request_id,\n", "  FROM \n", "    request_data\n", "  ORDER BY\n", "  request_id\n", "  LIMIT 20000\"\"\"\n", "    return query"]}, {"cell_type": "code", "execution_count": 15, "id": "08b897f6", "metadata": {}, "outputs": [], "source": ["def get_resolution_query(model, client, start_date, end_date):\n", "    query = f\"\"\"\n", "WITH request_data AS (\n", "  SELECT \n", "    cr.request_id as request_id,\n", "    rm.tenant as tenant,\n", "    JSON_EXTRACT_SCALAR(c_req.sanitized_json, '$.model') as model_name,\n", "    SPLIT(rm.user_agent, '/')[OFFSET(0)] as user_agent,\n", "  FROM \n", "    `system-services-prod.us_prod_request_insight_analytics_dataset.completion_resolution` cr\n", "  JOIN \n", "    `system-services-prod.us_prod_request_insight_analytics_dataset.completion_host_request` c_req\n", "  ON \n", "    cr.request_id = c_req.request_id\n", "  JOIN \n", "    `system-services-prod.us_prod_request_insight_analytics_dataset.human_request_metadata` rm\n", "  ON \n", "    cr.request_id = rm.request_id\n", "  JOIN \n", "    `system-services-prod.us_prod_request_insight_analytics_dataset.completion_host_response` c_resp\n", "  ON \n", "    cr.request_id = c_resp.request_id\n", "  WHERE \n", "    rm.shard_namespace = \"i1\"\n", "    AND JSON_EXTRACT_SCALAR(c_req.sanitized_json, '$.model') = \"{model}\"\n", "    AND SPLIT(rm.user_agent, '/')[OFFSET(0)] = '{client}'\n", "    AND cr.time > TIMESTAMP(\"{start_date}\")\n", "    AND cr.time <= TIMESTAMP(\"{end_date}\")\n", ")\n", "\n", "SELECT DISTINCT\n", "  request_id,\n", "  tenant\n", "FROM \n", "  request_data\n", "ORDER BY\n", "  request_id\n", "LIMIT 20000\"\"\"\n", "    return query"]}, {"cell_type": "code", "execution_count": 16, "id": "885cb6a9", "metadata": {}, "outputs": [], "source": ["empty_query = get_empty_query(\n", "    \"qweldenv3-2-14b\", \"Augment.vscode-augment\", \"2025-05-07\", \"2025-05-27\"\n", ")\n", "resolution_query = get_resolution_query(\n", "    \"qweldenv3-2-14b\", \"Augment.vscode-augment\", \"2025-05-07\", \"2025-05-27\"\n", ")\n", "\n", "resolution_query = get_resolution_query(\n", "    \"qweldenv3-2-14b\", \"Augment.vscode-augment\", \"2025-05-01\", \"2025-06-01\"\n", ")\n", "\n", "query = resolution_query"]}, {"cell_type": "code", "execution_count": 17, "id": "355205a3", "metadata": {}, "outputs": [], "source": ["from google.cloud import bigquery\n", "from base.datasets.completion_dataset_gcs import CompletionDataset, Filters\n", "from base.datasets import tenants\n", "from base.datasets.completion import CompletionDatum\n", "from base.datasets.gcp_creds import get_gcp_creds\n", "\n", "# Set up BigQuery client\n", "project_id = \"system-services-prod\"\n", "gcp_creds, _ = get_gcp_creds()\n", "bigquery_client = bigquery.Client(project=project_id, credentials=gcp_creds)\n", "\n", "rows = bigquery_client.query_and_wait(query)\n", "request_ids = [(row.request_id, row.tenant) for row in rows]\n", "print(f\"Found {len(request_ids)} request IDs\")"]}, {"cell_type": "code", "execution_count": 19, "id": "f3b15993", "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "\n", "tenent_to_requests = defaultdict(list)\n", "for request_id, tenant in request_ids:\n", "    tenent_to_requests[tenant].append(request_id)"]}, {"cell_type": "code", "execution_count": null, "id": "7f0d34ed", "metadata": {}, "outputs": [], "source": ["class TenantDatum:\n", "    def __init__(self, datum: CompletionDatum, tenant: str):\n", "        self.datum = datum\n", "        self.tenant = tenant\n", "        self.support_url = f\"https://support.i1.t.us-central1.prod.augmentcode.com/t/{tenant}/request/{datum.request_id}\"\n", "\n", "\n", "def get_data(tenant, request_ids):\n", "    dataset = CompletionDataset.create_data_from_gcs(\n", "        tenant=tenants.get_tenant(tenant),\n", "        filters=Filters(request_ids=request_ids),\n", "    )\n", "    dataset = list(dataset)\n", "    request_to_completion: dict[str, TenantDatum] = {\n", "        datum.request_id: TenantDatum(datum, tenant) for datum in dataset\n", "    }\n", "    print(f\"Downloaded {len(request_to_completion)} completions for {tenant}\")\n", "    return request_to_completion\n", "\n", "\n", "request_to_completion = {}\n", "for tenant, request_ids in tenent_to_requests.items():\n", "    request_to_completion.update(get_data(tenant, request_ids))\n", "\n", "# filter data if blobs < 50\n", "request_to_completion = {\n", "    request_id: datum\n", "    for request_id, datum in request_to_completion.items()\n", "    if len(datum.datum.request.blob_names) >= 50\n", "    and not datum.datum.request.path.endswith(\".ipynb\")\n", "}\n", "\n", "dataset: list[TenantDatum] = list(request_to_completion.values())\n", "\n", "print(f\"Downloaded {len(request_to_completion)} completions\")"]}, {"cell_type": "code", "execution_count": 41, "id": "af75d0c4", "metadata": {}, "outputs": [], "source": ["print(dataset[35].support_url)\n", "print(dataset[35].datum.request.edit_events)"]}, {"cell_type": "code", "execution_count": 39, "id": "6989a353", "metadata": {}, "outputs": [], "source": ["diff_section = 151682\n", "fim_prefix_section = 151659\n", "\n", "stats = {\"no_diff\": [], \"has_diff\": []}\n", "for i, tenant_datum in enumerate(dataset):\n", "    datum = tenant_datum.datum\n", "    prompt_token_ids = datum.response.prompt_token_ids\n", "    assert prompt_token_ids is not None, f\"Missing prompt tokens for {datum.request_id}\"\n", "    assert (\n", "        diff_section in prompt_token_ids\n", "    ), f\"Missing diff section for {datum.request_id}\"\n", "\n", "    diff_index = prompt_token_ids.index(diff_section)\n", "    if fim_prefix_section in prompt_token_ids[diff_index : diff_index + 5]:\n", "        stats[\"no_diff\"].append(datum)\n", "        print(i, \"no diff\")\n", "    else:\n", "        stats[\"has_diff\"].append(datum)"]}, {"cell_type": "code", "execution_count": 40, "id": "7c93118a", "metadata": {}, "outputs": [], "source": ["def acceptance_rate(datums):\n", "    safe_datums = [datum for datum in datums if datum.resolution is not None]\n", "    return len(safe_datums), sum(\n", "        datum.resolution.accepted for datum in safe_datums\n", "    ) / len(safe_datums)\n", "\n", "\n", "def avg_response_text(datums):\n", "    return sum(len(datum.response.text) for datum in datums) / len(datums)\n", "\n", "\n", "no_diff_data = stats[\"no_diff\"]\n", "has_diff_data = stats[\"has_diff\"]\n", "\n", "len_no_diff, acc_no_diff = acceptance_rate(no_diff_data)\n", "len_has_diff, acc_has_diff = acceptance_rate(has_diff_data)\n", "print(f\"Acceptance rate for no diff (n={len_no_diff}): {acc_no_diff}\")\n", "print(f\"Acceptance rate for has diff (n={len_has_diff}): {acc_has_diff}\")\n", "\n", "print(f\"Average response text for no diff: {avg_response_text(no_diff_data)}\")\n", "print(f\"Average response text for has diff: {avg_response_text(has_diff_data)}\")"]}, {"cell_type": "markdown", "id": "6899aa9f", "metadata": {}, "source": ["### Dogfood:\n", "Acceptance rate for no diff (n=662): 0.40483383685800606\n", "\n", "Acceptance rate for has diff (n=9304): 0.4312123817712812\n", "\n", "Avg chars ~60\n", "\n", "\n", "### Vanguard i1-1:\n", "Acceptance rate for no diff (n=1919): 0.3204794163626889\n", "\n", "Acceptance rate for has diff (n=8081): 0.3747061007301077\n", "\n", "Average response text for no diff: 46.419489317352784\n", "\n", "Average response text for has diff: 60.34673926494246"]}, {"cell_type": "code", "execution_count": null, "id": "08355315", "metadata": {}, "outputs": [], "source": ["# sort by user and then timestamp\n", "dataset = sorted(dataset, key=lambda x: (x.datum.user_id, x.datum.request.timestamp))"]}, {"cell_type": "code", "execution_count": null, "id": "9dbe710b", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "models = [datum.datum.response.model for datum in dataset]\n", "Counter(models)"]}, {"cell_type": "code", "execution_count": 1, "id": "a3d040ef", "metadata": {}, "outputs": [], "source": ["import concurrent.futures\n", "import time\n", "\n", "\n", "def task(x):\n", "    if x == 1:\n", "        raise ValueError(\"Error in task 2\")\n", "    time.sleep(5)\n", "    return x\n", "\n", "\n", "with concurrent.futures.ThreadPoolExecutor() as executor:\n", "    results = executor.map(task, [1, 2, 3])\n", "    # print(\"Here\")\n", "    # print(next(results))\n", "    # print(list(results))"]}, {"cell_type": "code", "execution_count": 20, "id": "d11bbecf", "metadata": {}, "outputs": [], "source": ["import concurrent.futures\n", "\n", "\n", "def task(x):\n", "    if x == 2:\n", "        raise ValueError(f\"Error in task {x}\")\n", "    time.sleep(5)\n", "    return x\n", "\n", "\n", "def fail_fast_execution(executor, func, items):\n", "    futures = [executor.submit(func, item) for item in items]\n", "\n", "    try:\n", "        for future in concurrent.futures.as_completed(futures):\n", "            result = future.result()\n", "            yield result\n", "    except Exception as e:\n", "        # Cancel all remaining futures\n", "        for future in futures:\n", "            future.cancel()\n", "        raise e\n", "\n", "\n", "with concurrent.futures.ThreadPoolExecutor() as executor:\n", "    try:\n", "        results = list(fail_fast_execution(executor, task, [1, 2, 3]))\n", "        print(\"All tasks completed:\", results)\n", "    except ValueError as e:\n", "        print(f\"Execution failed fast: {e}\")"]}, {"cell_type": "code", "execution_count": null, "id": "cfc401ce", "metadata": {}, "outputs": [], "source": ["inference_tokens = []\n", "response_tokens = []\n", "response_text = []\n", "for tenant_datum in dataset:\n", "    datum = tenant_datum.datum\n", "    assert (\n", "        datum.inference_response is not None\n", "    ), f\"Missing inference response for {datum.request_id}\"\n", "    token_ids = datum.inference_response.token_ids\n", "    tokens = datum.inference_response.tokens[:-1]\n", "    if len(tokens) == 0:\n", "        continue\n", "\n", "    inference_tokens.append(len(token_ids))\n", "\n", "    datum_response_tokens = datum.response.tokens\n", "    datum_response_text = \"\".join(datum_response_tokens)\n", "    datum_response_text = datum.response.text\n", "\n", "    response_tokens.append(len(datum_response_tokens))\n", "    response_text.append(len(datum_response_text))\n", "\n", "inference_total_tokens = sum(inference_tokens)\n", "reponse_total_tokens = sum(response_tokens)\n", "reponse_total_text = sum(response_text)\n", "print(f\"Total inference tokens: {inference_total_tokens}\")\n", "print(f\"Total response tokens: {reponse_total_tokens}\")\n", "print(f\"Total response text: {reponse_total_text}\")"]}, {"cell_type": "code", "execution_count": 45, "id": "33d7b24f", "metadata": {}, "outputs": [], "source": ["count = 0\n", "for rt in response_tokens:\n", "    if rt <= 55:\n", "        count += 1\n", "print(count, len(response_tokens))"]}, {"cell_type": "code", "execution_count": 48, "id": "b39adef9", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# number of samples above 64 tokens\n", "boundary = 5\n", "num_samples_above_64 = sum(1 for x in inference_tokens if x >= boundary)\n", "print(f\"Number of samples above {boundary} tokens: {num_samples_above_64}\")\n", "print(\n", "    f\"Number of samples below {boundary} tokens: {len(inference_tokens) - num_samples_above_64}\"\n", ")\n", "print(\n", "    f\"Percentage of samples above {boundary} tokens: {num_samples_above_64 / len(inference_tokens) * 100:.2f}%\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b231b874", "metadata": {}, "outputs": [], "source": ["def truncate_at_parenthesis_initial(completion: str):\n", "    paren_index = -1\n", "    for i in range(3, len(completion) - 1):\n", "        if (\n", "            completion[i] == \"(\"\n", "            and not completion[i - 1].isspace()\n", "            and completion[i + 1] != \")\"\n", "        ):\n", "            paren_index = i\n", "            break\n", "\n", "    if paren_index == -1:\n", "        return False, completion\n", "    else:\n", "        return True, completion[: paren_index + 1]\n", "\n", "\n", "def truncate_at_parenthesis_newline(completion: str):\n", "    paren_index = -1\n", "\n", "    for i in range(3, len(completion) - 2):\n", "        if (\n", "            completion[i] == \"(\"\n", "            and not completion[i - 1].isspace()\n", "            and completion[i + 1] == \"\\n\"\n", "        ):\n", "            paren_index = i\n", "            break\n", "\n", "    if paren_index == -1:\n", "        return False, completion\n", "    else:\n", "        return True, completion[: paren_index + 1]"]}, {"cell_type": "code", "execution_count": null, "id": "b7b8eb4d", "metadata": {}, "outputs": [], "source": ["# import color highlight stuff\n", "from colorama import Fore, Style\n", "\n", "stats = {\"total\": [], \"truncated_initial\": [], \"truncated_nl\": []}\n", "for tenant_datum in dataset:\n", "    datum = tenant_datum.datum\n", "    was_truncated_initial, completion_initial = truncate_at_parenthesis_initial(\n", "        datum.response.text\n", "    )\n", "    was_truncated_nl, completion_nl = truncate_at_parenthesis_newline(\n", "        datum.response.text\n", "    )\n", "    request_id = datum.request_id\n", "    stats[\"total\"].append(datum)\n", "    if was_truncated_initial:\n", "        stats[\"truncated_initial\"].append(datum)\n", "    if was_truncated_nl:\n", "        stats[\"truncated_nl\"].append(datum)\n", "    if not was_truncated_initial and was_truncated_nl:\n", "        print(f\"Request ID: {Fore.CYAN}{datum.request_id}{Style.RESET_ALL}\")\n", "\n", "    # if was_truncated_nl:\n", "    #     print(f\"Request ID: {Fore.CYAN}{datum.request_id}{Style.RESET_ALL}\")\n", "    #     print(f\"Prefix: {Fore.YELLOW}{datum.request.prefix[-100:]}{Style.RESET_ALL}\")\n", "    #     print(f\"Suffix: {Fore.YELLOW}{datum.request.suffix[:100]}{Style.RESET_ALL}\")\n", "    #     print(f\"Completion: {Fore.GREEN}{datum.response.text}{Style.RESET_ALL}\")\n", "    #     print(f\"Completion (initial={Fore.RED if was_truncated_initial else Fore.GREEN}{was_truncated_initial}{Style.RESET_ALL}): {Fore.BLUE}{completion_initial}{Style.RESET_ALL}\")\n", "    #     print(f\"Completion (newline={Fore.RED if was_truncated_nl else Fore.GREEN}{was_truncated_nl}{Style.RESET_ALL}): {Fore.BLUE}{completion_nl}{Style.RESET_ALL}\")\n", "\n", "    #     print(\"\\n\")\n", "    #     print(\"==============\")\n", "print({k: len(v) for k, v in stats.items()})"]}, {"cell_type": "code", "execution_count": 21, "id": "90d854bc", "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "Counter([datum.user_id for datum in stats[\"truncated_initial\"]])"]}, {"cell_type": "code", "execution_count": 22, "id": "735856f2", "metadata": {}, "outputs": [], "source": ["[datum.request_id for datum in stats[\"truncated_initial\"]]"]}, {"cell_type": "code", "execution_count": 16, "id": "d2523745", "metadata": {}, "outputs": [], "source": ["repr(stats[\"truncated_nl\"][0].response.text)"]}, {"cell_type": "code", "execution_count": 24, "id": "cb1cd22a", "metadata": {}, "outputs": [], "source": ["# print out completion and prefix/suffix\n", "for datum in stats[\"truncated_initial\"]:\n", "    print(f\"Request ID: {datum.request_id}\")\n", "    # print(f\"Prefix: {datum.request.prefix[-100:]}\")\n", "    # print(f\"Suffix: {datum.request.suffix[:100]}\")\n", "    print(f\"Completion: {datum.response.text}\")\n", "    print(\"\\n\")\n", "    print(\"==============\")"]}, {"cell_type": "code", "execution_count": null, "id": "86225045", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}