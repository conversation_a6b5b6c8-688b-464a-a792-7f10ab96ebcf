{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Viewed Content Processing Performance Tests\n", "\n", "This notebook runs timing tests for individual functions in the recency processing pipeline to identify performance bottlenecks.\n", "\n", "## Functions tested:\n", "2. `limit_viewed_content_chunk_size` - Around median 2-4ms with 10 viewed content of 5 KB\n", "3. `deduplicate_viewed_content_against_replacements` - Can be slow when the last replacement text overlaps with viewed content. Tested with 10 viewed content and 150 replacement texts (worst case) and results are median 2-3 ms."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "import structlog\n", "\n", "logging.basicConfig(level=logging.INFO)\n", "logger = structlog.get_logger(__name__)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "# Import the timing test functions\n", "from timing_test import (\n", "    test_limit_viewed_content_performance,\n", "    test_deduplicate_viewed_content_performance,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 2: limit_viewed_content_chunk_size Performance\n", "\n", "This function can be slow with large content requiring smart chunking."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing limit_viewed_content_chunk_size with 10 items of 5KB each...\n", "  Content strategy: real_code\n", "  Max chunk size: 1024\n", "  Created 10 viewed content items\n", "  Average content size: 5120 chars\n", "  Completed 10/100 runs...\n", "  Completed 20/100 runs...\n", "  Completed 30/100 runs...\n", "  Completed 40/100 runs...\n", "  Completed 50/100 runs...\n", "  Completed 60/100 runs...\n", "  Completed 70/100 runs...\n", "  Completed 80/100 runs...\n", "  Completed 90/100 runs...\n", "  Completed 100/100 runs...\n", "\n", "Timing Results for limit_viewed_content_chunk_size:\n", "  Average time: 5.773ms\n", "  Median time: 4.518ms\n", "  Min time: 4.364ms\n", "  Max time: 59.999ms\n", "  Std deviation: 7.280ms\n", "  Average result length: 10.0 chunks\n"]}], "source": ["# Test with real code content (default)\n", "chunking_results = test_limit_viewed_content_performance(\n", "    num_viewed_content=10,\n", "    content_size_kb=5,\n", "    max_chunk_size=1024,\n", "    content_strategy=\"real_code\",\n", "    num_runs=100,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing limit_viewed_content_chunk_size with 10 items of 5KB each...\n", "  Content strategy: random_text\n", "  Max chunk size: 1024\n", "  Created 10 viewed content items\n", "  Average content size: 5120 chars\n", "  Completed 10/100 runs...\n", "  Completed 20/100 runs...\n", "  Completed 30/100 runs...\n", "  Completed 40/100 runs...\n", "  Completed 50/100 runs...\n", "  Completed 60/100 runs...\n", "  Completed 70/100 runs...\n", "  Completed 80/100 runs...\n", "  Completed 90/100 runs...\n", "  Completed 100/100 runs...\n", "\n", "Timing Results for limit_viewed_content_chunk_size:\n", "  Average time: 2.848ms\n", "  Median time: 2.233ms\n", "  Min time: 2.068ms\n", "  Max time: 52.503ms\n", "  Std deviation: 5.022ms\n", "  Average result length: 10.0 chunks\n"]}], "source": ["# Test with random text content\n", "chunking_random_results = test_limit_viewed_content_performance(\n", "    num_viewed_content=10,\n", "    content_size_kb=5,\n", "    max_chunk_size=1024,\n", "    content_strategy=\"random_text\",\n", "    num_runs=100,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test 3: deduplicate_viewed_content_against_replacements Performance\n", "\n", "This function can be slow when many replacements overlap with viewed content, especially when the overlapping replacement is at a late position (forces iteration through many replacements)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test worst-case scenario (overlap at end)\n", "dedup_results = test_deduplicate_viewed_content_performance(\n", "    num_viewed_content=10, num_replacements=150, num_runs=100\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test with more replacements (even worse case)\n", "dedup_large_results = test_deduplicate_viewed_content_performance(\n", "    num_viewed_content=10, num_replacements=5000, num_runs=100\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 4}