{"cells": [{"cell_type": "code", "execution_count": null, "id": "8345a689", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "import json\n", "\n", "from colorama import Fore, Style\n", "from tqdm import tqdm\n", "\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "from experimental.pranay.utils.common_utils import (\n", "    ExecuteModel,\n", "    setup_qwen25_tokenizer,\n", "    get_common_checkpoints,\n", "    break_apart_prompt,\n", "    annotate_chunks,\n", "    to_prompt_chunk,\n", ")\n", "from experimental.pranay.viewed_content.viewed_content_utils import process_datum\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.data.rag.hindsight_common import (\n", "    EnderDataPromptFormattingConfig,\n", "    HindsightCompletionDatum,\n", ")\n", "from research.eval.harness.metrics import (\n", "    DatumTag,\n", "    MetricsAggregator,\n", "    compute_code_complete_metrics,\n", "    metrics_to_str_dict,\n", ")\n", "\n", "tokenizer: Qwen25CoderTokenizer = setup_qwen25_tokenizer()\n", "st = tokenizer.special_tokens"]}, {"cell_type": "code", "execution_count": 2, "id": "70304866", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2271\n"]}], "source": ["results = read_jsonl_zst(\n", "    \"/mnt/efs/augment/user/pranay/hindsight/2025-07-24_2025-07-29/dogfood-shard/data.jsonl.zst\"\n", ")\n", "print(len(results))"]}, {"cell_type": "code", "execution_count": null, "id": "733852f9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10/10 [00:00<00:00, 10.76it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["MAX = 10\n", "if MAX == -1:\n", "    MAX = len(results)\n", "\n", "datums: list[HindsightCompletionDatum] = []\n", "for data in tqdm(results[:MAX]):\n", "    recency_info = data[\"completion\"][\"request\"][\"recency_info\"]\n", "    assert recency_info is not None, \"Recency info is required.\"\n", "    viewed_content = recency_info.get(\"viewed_contents\", [])\n", "    if len(viewed_content) > 0:\n", "        datums.append(HindsightCompletionDatum.from_dict(data))\n", "\n", "rid_to_datum = {datum.completion.request_id: datum for datum in datums}\n", "\n", "print(len(datums))"]}, {"cell_type": "markdown", "id": "470e1d24", "metadata": {}, "source": ["## Run recency retriever without having to regenerate line/sig chunks"]}, {"cell_type": "code", "execution_count": null, "id": "d50237d9", "metadata": {}, "outputs": [], "source": ["no_viewed_content_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=8352,\n", "    input_fraction=0.25,\n", "    prefix_fraction=0.75,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=1024,\n", "    max_recency_retriever_viewed_content_tokens=0,\n", "    max_diff_retriever_tokens=2048,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")\n", "\n", "viewed_content_config = EnderDataPromptFormattingConfig(\n", "    max_content_len=8352,\n", "    input_fraction=0.25,\n", "    prefix_fraction=0.75,\n", "    max_path_tokens=50,\n", "    max_dense_signature_tokens=1024,\n", "    max_recency_retriever_tokens=1024,\n", "    max_recency_retriever_viewed_content_tokens=1024,\n", "    max_diff_retriever_tokens=2048,\n", "    include_diff_retriever=True,\n", "    max_target_tokens=256,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "de664016", "metadata": {}, "outputs": [], "source": ["PROMPT_NAME = \"VC_last_prompt\"\n", "OUTPUT_NAME = \"VC_last_output\"\n", "FILE_NAME = \"VC_last_file.json\"\n", "CONFIG = viewed_content_config\n", "\n", "rid_to_prompts = {}\n", "for datum in tqdm(datums):\n", "    rid_to_prompts[datum.completion.request_id] = process_datum(\n", "        datum, PROMPT_NAME, CONFIG, tokenizer\n", "    )"]}, {"cell_type": "markdown", "id": "9eae4a2b", "metadata": {}, "source": ["### Other"]}, {"cell_type": "code", "execution_count": 25, "id": "c5433fe5", "metadata": {}, "outputs": [], "source": ["checkpoints = get_common_checkpoints()\n", "base_fp8_checkpoint = checkpoints[\"base_fp8\"]\n", "\n", "base_fp8_model = ExecuteModel(\n", "    base_fp8_checkpoint[0],\n", "    base_fp8_checkpoint[1],\n", "    tokenizer,\n", "    use_fp8=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "74871bca", "metadata": {}, "outputs": [], "source": ["for rid in tqdm(rid_to_prompts):\n", "    VC_output = base_fp8_model.simple_call(rid_to_prompts[rid][PROMPT_NAME]).tokens\n", "    rid_to_prompts[rid][OUTPUT_NAME] = VC_output"]}, {"cell_type": "code", "execution_count": null, "id": "8ad0df8e", "metadata": {}, "outputs": [], "source": ["with open(FILE_NAME, \"w\") as f:\n", "    json.dump(rid_to_prompts, f)"]}, {"cell_type": "markdown", "id": "b7006e5a", "metadata": {}, "source": ["### Analyze the quality of the VC prompts and outputs."]}, {"cell_type": "code", "execution_count": null, "id": "f4a35fdf", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from collections import defaultdict\n", "from experimental.pranay.utils.common_utils import print_data_summary\n", "\n", "BASE = \"/mnt/efs/augment/user/pranay/viewed_content/\"\n", "files = [\"V3_2_no_VC_2k.json\", \"V3_2_VC_middle_2k.json\", \"V3_2_VC_last_2k.json\"]\n", "\n", "final_rid_to_prompt = defaultdict(dict)\n", "for file in files:\n", "    read_file = BASE + file\n", "    with open(read_file, \"r\") as f:\n", "        rid_to_prompts = json.load(f)\n", "\n", "    for rid in rid_to_prompts:\n", "        final_rid_to_prompt[rid].update(rid_to_prompts[rid])\n", "\n", "print(len(final_rid_to_prompt))\n", "\n", "# remove row if v3_2_no_VC_prompt is the same as other prompts\n", "final_rid_to_prompt_clean = {}\n", "for rid in final_rid_to_prompt:\n", "    no_VC_prompt = final_rid_to_prompt[rid][\"v3_2_no_VC_prompt\"]\n", "    VC_middle_prompt = final_rid_to_prompt[rid][\"v3_2_VC_middle_prompt\"]\n", "    VC_last_prompt = final_rid_to_prompt[rid][\"v3_2_VC_last_prompt\"]\n", "    if no_VC_prompt != VC_middle_prompt and no_VC_prompt != VC_last_prompt:\n", "        final_rid_to_prompt_clean[rid] = final_rid_to_prompt[rid]\n", "\n", "final_rid_to_prompt = final_rid_to_prompt_clean\n", "print(len(final_rid_to_prompt))"]}, {"cell_type": "code", "execution_count": 36, "id": "b3bdc08a", "metadata": {}, "outputs": [], "source": ["agg = MetricsAggregator()\n", "rows = [\"v3_2_no_VC_output\", \"v3_2_VC_middle_output\", \"v3_2_VC_last_output\"]\n", "\n", "for rid in final_rid_to_prompt:\n", "    ground_truth = final_rid_to_prompt[rid][\"ground_truth\"]\n", "    for row in rows:\n", "        output_tokens = final_rid_to_prompt[rid][row]\n", "        response_tokens, _ = base_fp8_model.extract_generation_before_stop_tokens(\n", "            output_tokens, [st.eos, st.pause]\n", "        )\n", "        response_str = tokenizer.detokenize(response_tokens)\n", "        metrics = compute_code_complete_metrics(\n", "            response_str, ground_truth\n", "        ).to_float_dict()\n", "        tags = [DatumTag(\"prompt\", row)]\n", "        agg.add(metrics, tags)\n", "\n", "aggregated = agg.compute()\n", "# metrics_to_str_dict(aggregated)"]}, {"cell_type": "code", "execution_count": null, "id": "8ddf00bc", "metadata": {}, "outputs": [], "source": ["import re\n", "from collections import defaultdict\n", "\n", "\n", "def parse_metrics_dict(metrics_dict):\n", "    \"\"\"Parse metrics dictionary into structured format\"\"\"\n", "    parsed_data = defaultdict(lambda: defaultdict(dict))\n", "\n", "    for key, value in metrics_dict.items():\n", "        # Extract components using regex\n", "        match = re.match(r\"prompt\\.(.+)\\.([^.]+)\\.([^.]+)$\", key)\n", "        if not match:\n", "            continue\n", "\n", "        model_type, metric_name, stat_type = match.groups()\n", "        parsed_data[metric_name][model_type][stat_type] = value\n", "\n", "    return parsed_data\n", "\n", "\n", "def format_avg_stderr(avg, stderr):\n", "    \"\"\"Format average and stderr as 'avg ± stderr'\"\"\"\n", "    if avg is None or stderr is None:\n", "        return \"N/A\"\n", "    return f\"{avg:.3f} ± {stderr:.3f}\"\n", "\n", "\n", "def create_metrics_table(metrics_dict):\n", "    \"\"\"Create formatted table from metrics dictionary\"\"\"\n", "    parsed_data = parse_metrics_dict(metrics_dict)\n", "\n", "    if not parsed_data:\n", "        raise ValueError(\"No valid metrics found in dictionary\")\n", "\n", "    # Get all model types\n", "    all_models = set()\n", "    for metric_data in parsed_data.values():\n", "        all_models.update(metric_data.keys())\n", "    all_models = sorted(list(all_models))\n", "\n", "    # Build table data\n", "    table_data = []\n", "    best_models = []\n", "\n", "    for metric_name in sorted(parsed_data.keys()):\n", "        row = {\"Metric\": metric_name}\n", "        metric_avgs = {}\n", "\n", "        # Add formatted values for each model\n", "        for model in all_models:\n", "            model_data = parsed_data[metric_name].get(model, {})\n", "            avg = model_data.get(\"avg\")\n", "            stderr = model_data.get(\"stderr\")\n", "\n", "            row[model] = format_avg_stderr(avg, stderr)\n", "            if avg is not None:\n", "                metric_avgs[model] = avg\n", "\n", "        # Find best model for this metric\n", "        if metric_avgs:\n", "            best_model = max(metric_avgs.keys(), key=lambda x: metric_avgs[x])\n", "            row[\"Best\"] = best_model\n", "            best_models.append(best_model)\n", "        else:\n", "            row[\"Best\"] = \"N/A\"\n", "            best_models.append(None)\n", "\n", "        table_data.append(row)\n", "\n", "    # Create DataFrame\n", "    df = pd.DataFrame(table_data)\n", "\n", "    # Set Metric as index\n", "    df.set_index(\"Metric\", inplace=True)\n", "\n", "    return df\n", "\n", "\n", "def display_metrics_table(metrics_dict, highlight_best=True):\n", "    \"\"\"Display formatted metrics table with optional highlighting\"\"\"\n", "    df = create_metrics_table(metrics_dict)\n", "\n", "    if highlight_best:\n", "\n", "        def highlight_best_values(row):\n", "            best_model = row[\"Best\"]\n", "            styles = [\"\"] * len(row)\n", "\n", "            if best_model != \"N/A\" and best_model in row.index:\n", "                best_idx = list(row.index).index(best_model)\n", "                styles[best_idx] = \"font-weight: bold; background-color: #90EE90\"\n", "\n", "            return styles\n", "\n", "        styled_df = df.style.apply(highlight_best_values, axis=1)\n", "        return styled_df\n", "\n", "    return df\n", "\n", "\n", "def print_metrics_table(metrics_dict):\n", "    \"\"\"Print metrics table to console\"\"\"\n", "    df = create_metrics_table(metrics_dict)\n", "    # print(df.to_string())\n", "    return df\n", "\n", "\n", "print_metrics_table(metrics_to_str_dict(aggregated))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}