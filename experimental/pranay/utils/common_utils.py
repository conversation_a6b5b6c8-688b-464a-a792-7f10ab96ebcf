"""
Common utility functions for Pranay's experimental code.

This module contains frequently used functions across multiple notebooks and scripts
to reduce code duplication and improve maintainability.
"""

import glob
import json
import os
import zstandard as zstd
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tu<PERSON>, cast

import numpy as np
import pandas as pd
from base.prompt_format.common import TokenList, PromptChunk
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.tokenizer import Tokenizer
from research.core.utils_for_file import read_jsonl
from base.datasets.completion import CompletionDatum
from base.datasets.tenants import DatasetTenant

from base.datasets.completion_dataset_gcs import CompletionDataset, Filters

from base.datasets import completion
from base.prompt_format.chunk_origin import Chunk<PERSON>rigin, ChunkOriginValues
from base.prompt_format_completion.ender_prompt_formatter import (
    EnderPromptFormatter,
    EnderPromptFormatterConfig,
    StatelessCachingConfig,
    TokenApportionmentConfig,
)
from base.prompt_format_completion.overlap import modified_chunks_filter
from base.prompt_format_completion.prompt_formatter import PromptInput
from base.tokenizers import tokenizer as prod_tokenizer
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderSpecialTokens
from research.core.recency_info import convert_from_datasets_recency_info
from research.data.rag.hindsight_common import EnderDataPromptFormattingConfig


import contextlib
import gc
import io
import sys

import torch

from research.models.fastforward_llama_models import LLAMA_FastForwardModel
from research.models.meta_model import GenerationOptions, RawGenerateOutput


# Common paths
BASE_DATA_DIR = Path("/mnt/efs/augment/user/pranay/hindsight/")


def setup_qwen25_tokenizer() -> Qwen25CoderTokenizer:
    """Initialize and return the Qwen25Coder tokenizer with special tokens."""
    tokenizer: Qwen25CoderTokenizer = cast(
        Qwen25CoderTokenizer, create_tokenizer_by_name("qwen25coder")
    )
    return tokenizer


def get_interactive_stop_tokens(tokenizer: Qwen25CoderTokenizer) -> List[int]:
    """Get the standard interactive stop tokens."""
    st = tokenizer.special_tokens
    return [st.pause, st.eos, st.skip]


# Data Loading Functions
def read_parquet_files(parquet_pattern: str) -> pd.DataFrame:
    """Read and concatenate multiple parquet files matching a pattern."""
    parquet_files = sorted(glob.glob(parquet_pattern))
    if not parquet_files:
        raise FileNotFoundError(
            f"No parquet files found matching pattern: {parquet_pattern}"
        )

    dfs = []
    for file in parquet_files:
        df = pd.read_parquet(file)
        dfs.append(df)

    parquet_df = pd.concat(dfs, ignore_index=True)
    print(f"Loaded {len(parquet_df)} records from {len(parquet_files)} Parquet files")
    return parquet_df


def load_results_file(jsonl_path: str | Path) -> List[Dict]:
    """Load results from a JSONL file if it exists."""
    results = []
    if os.path.exists(jsonl_path):
        results = read_jsonl(jsonl_path)
        print(f"Loaded {len(results)} results from {jsonl_path}")
    return results


def download_request_from_gcs(
    request_ids: list[str], tenant: DatasetTenant
) -> list[CompletionDatum]:
    """Download request data from GCS."""

    filters = Filters(request_ids=request_ids)
    dataset = CompletionDataset.create_data_from_gcs(tenant=tenant, filters=filters)

    return list(dataset)


# Token Processing Functions
def prefix_up_to_any_delimiter(
    lst: List[int], delimiter: List[int]
) -> Tuple[List[int], Optional[int]]:
    """Returns the prefix up to the first occurrence of any delimiter and the delimiter."""
    for i, token in enumerate(lst):
        if token in delimiter:
            return lst[:i], token
    return lst, None


def extract_tokens_between_tokens(
    tokens: List[int], start_token: int, end_token: int
) -> List[int]:
    """Extracts tokens between start and end tokens."""
    tokens = list(tokens)
    start = tokens.index(start_token) + 1
    end = tokens.index(end_token) if end_token in tokens else len(tokens)
    assert start <= end, f"start: {start}, end: {end}"
    return tokens[start:end]


def extract_prompt(sample: np.ndarray, fim_middle_token: int) -> Optional[List[int]]:
    """Extract tokens up to and including the fim_middle token."""
    sample_list = sample.tolist()
    try:
        middle_idx = sample_list.index(fim_middle_token) + 1
        return sample_list[:middle_idx]
    except ValueError:
        print("fim_middle token not found in sample")
        return None


def extract_target(
    tokens: np.ndarray, fim_middle_token: int, padding_token: int
) -> List[int]:
    """Extract target tokens after fim_middle and before padding."""
    middle_idx = np.where(tokens == fim_middle_token)[0]
    pad_idx = np.where(tokens == padding_token)[0]
    pad_idx = pad_idx[0] if len(pad_idx) > 0 else len(tokens)

    if len(middle_idx) > 0:
        start = middle_idx[0] + 1
        return list(tokens[start:pad_idx])
    else:
        raise ValueError("fim_middle not found in tokens")


def split_list(lst: List[int], delimiter: List[int]) -> List[List[int]]:
    """Split a list by a delimiter sequence."""
    result = []
    current_subsequence = []

    idx = 0
    while idx < len(lst):
        if lst[idx : idx + len(delimiter)] == delimiter:
            if current_subsequence:
                result.append(current_subsequence)
            idx += len(delimiter)
            current_subsequence = []
        else:
            current_subsequence.append(lst[idx])
            idx += 1

    if current_subsequence:  # Append the last subsequence if it's not empty
        result.append(current_subsequence)
    return result


def convert_datetime_nested(obj: Any) -> Any:
    """Convert datetime objects to timestamps, handling nested structures."""
    if isinstance(obj, dict):
        return {k: convert_datetime_nested(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_datetime_nested(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_datetime_nested(item) for item in obj)
    elif isinstance(obj, datetime):
        return int(obj.timestamp())
    return obj


# File I/O Functions
def write_jsonl_zst(data: List[Dict], output_file: Path) -> None:
    """Write data to a zstd compressed JSON lines file."""
    with zstd.open(output_file, "w", encoding="utf-8") as f:
        for item in data:
            json.dump(convert_datetime_nested(item), f)
            f.write("\n")


# Model and Checkpoint Utilities
def get_common_checkpoints() -> Dict[str, Tuple[str, str]]:
    """Return commonly used model checkpoints."""
    return {
        "base_fp16": (
            "/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd",
            "7d3f96dcc780ffdcf0c344c5edf54ef039fe0db58a1b30fddc7753ba639a76e9",
        ),
        "base_fp8": (
            "/mnt/efs/augment/checkpoints/qwencompletion/14b_elden_smart_ffwd_fp8",
            "651be22236ee5aa104c4c190eb4e6c9464527f3e825a6afd11ab9d24046dbc15",
        ),
    }


# Logging and Progress Utilities
def log_progress(
    current: int, total: int, step: int = 100, message: str = "Processing"
) -> None:
    """Log progress at regular intervals."""
    if current % step == 0 or current == total:
        percentage = (current / total) * 100 if total > 0 else 0
        print(f"{message}: {current}/{total} ({percentage:.1f}%)")


def print_data_summary(df: pd.DataFrame, name: str = "DataFrame") -> None:
    """Print a summary of dataframe contents."""
    print(f"\n{name} Summary:")
    print(f"  Shape: {df.shape}")
    print(f"  Columns: {list(df.columns)}")
    if "request_id" in df.columns:
        print(f"  Unique request_ids: {df['request_id'].nunique()}")


# Prompt Formatting Utilities
def to_prompt_chunk(retrieval_chunk: completion.RetrievedChunk) -> PromptChunk:
    """Returns a PromptChunk object."""

    return PromptChunk(
        text=retrieval_chunk.text,
        path=retrieval_chunk.path,
        char_start=retrieval_chunk.crange.start,
        char_end=retrieval_chunk.crange.stop,
        origin=retrieval_chunk.origin,
        blob_name=retrieval_chunk.blob_name,
    )


def generate_prompt(
    prefix: str,
    prefix_begin: int,
    suffix: str,
    file_path: str,
    deserialized_retrieved_chunks: list[PromptChunk],
    recency_info: completion.RecencyInfo | None,
    tokenizer: prod_tokenizer.Tokenizer,
    config: EnderDataPromptFormattingConfig,
) -> list[int]:
    if (
        config.max_recency_retriever_tokens > 0
        or config.always_filter_chunks_by_recency
    ):
        if recency_info is not None:
            deserialized_retrieved_chunks = list(
                modified_chunks_filter(
                    deserialized_retrieved_chunks,
                    convert_from_datasets_recency_info(recency_info),
                    origins=ChunkOriginValues,
                    skip_origins=[
                        ChunkOrigin.RECENCY_RETRIEVER.value,
                        ChunkOrigin.DIFF_RETRIEVER.value,
                        ChunkOrigin.RECENCY_RETRIEVER_VIEWED_CONTENT.value,
                    ],
                )
            )

    apportionment_config = TokenApportionmentConfig(
        max_content_len=config.max_content_len,
        input_fraction=config.input_fraction,
        prefix_fraction=config.prefix_fraction,
        max_path_tokens=config.max_path_tokens,
        per_retriever_max_tokens={
            "dense_signature": config.max_dense_signature_tokens,
            "recency_retriever": config.max_recency_retriever_tokens,
            "diff_retriever": config.max_diff_retriever_tokens,
            "recency_retriever_viewed_content": config.max_recency_retriever_viewed_content_tokens,
        },
    )

    assert config.include_diff_retriever, "Diff retriever required."

    component_order = (
        "path",
        "prefix",
        "retrieval",
        "signature",
        "diff",
        "nearby_prefix",
        "suffix",
    )

    ender_prompt_formatter_config = EnderPromptFormatterConfig(
        stateless_caching_config=StatelessCachingConfig(
            nearby_prefix_token_len=512,
            quantize_token_len=64,
            quantize_char_len=250,
        ),
        component_order=component_order,
        filter_visible_chunks_by_content=True,
        signature_chunk_origin="dense_signature",
    )

    prompt_formatter = EnderPromptFormatter(
        apportionment_config=apportionment_config,
        prompt_formatter_config=ender_prompt_formatter_config,
        tokenizer=tokenizer,
    )

    prompt_input = PromptInput(
        prefix=prefix,
        suffix=suffix,
        prefix_begin=prefix_begin,
        path=file_path,
        retrieved_chunks=deserialized_retrieved_chunks,
        lang=None,
    )

    formatter_output = prompt_formatter.format_prompt(
        prompt_input, config.max_target_tokens
    )
    prompt_tokens = formatter_output.tokens()
    return prompt_tokens


# Model Execution Utilities
class ExecuteModel:
    _instances = []

    @contextlib.contextmanager
    def _suppress_output(self):
        """Context manager to suppress stdout temporarily while keeping stderr."""
        stdout = sys.stdout
        output = io.StringIO()
        try:
            sys.stdout = output
            yield
        finally:
            sys.stdout = stdout

    def __init__(
        self,
        checkpoint: str,
        sha: str,
        tokenizer: Qwen25CoderTokenizer,
        use_fp8: bool = False,
    ):
        with self._suppress_output():
            self.tokenizer = tokenizer
            self.st = tokenizer.special_tokens
            self.config = {
                "name": "fastforward_qwen25coder_14b",
                "checkpoint_path": checkpoint,
                "checkpoint_sha256": sha,
                "sequence_length": 6600,
                "use_fp8": use_fp8,
            }
            from research.eval.harness.factories import create_model

            self.model = None
            self._model_creator = lambda: cast(
                LLAMA_FastForwardModel, create_model(self.config)
            )

            self.options = GenerationOptions(
                max_generated_tokens=256, stop_tokens=[self.st.eos]
            )

            ExecuteModel._instances.append(self)

    def ensure_loaded(self):
        """Ensure this model is loaded and others are unloaded."""
        with self._suppress_output():
            # Unload all other models
            for instance in ExecuteModel._instances:
                if instance is not self and instance.model is not None:
                    instance.deep_unload()

            # Load this model if needed
            if self.model is None:
                self.model = self._model_creator()
                self.model.load()

    def __call__(
        self, prompt_tokens: list[int], keep_pause_tokens: bool = False
    ) -> list[int]:
        with self._suppress_output():
            self.ensure_loaded()
            assert self.model is not None
            generated_tokens = self.model.raw_generate_tokens(
                prompt_tokens, options=self.options
            ).tokens

            original_length = len(generated_tokens)
            generated_tokens, _ = self.extract_generation_before_stop_tokens(
                generated_tokens, [self.st.eos]
            )
            generation_stopped_at_max_length = original_length == len(generated_tokens)
            if generation_stopped_at_max_length and self.st.pause is not None:
                for index in reversed(range(len(generated_tokens))):
                    if generated_tokens[index] == self.st.pause:
                        generated_tokens = generated_tokens[:index]
                        break

            if keep_pause_tokens:
                return generated_tokens

            generated_tokens = [
                token for token in generated_tokens if token != self.st.pause
            ]
            return generated_tokens

    def simple_call(self, prompt_tokens: list[int]) -> RawGenerateOutput:
        with self._suppress_output():
            self.ensure_loaded()
            assert self.model is not None
            options = GenerationOptions(
                max_generated_tokens=256, stop_tokens=[self.st.eos, self.st.pause]
            )
            return self.model.raw_generate_tokens(prompt_tokens, options=options)

    def forward_pass_for_logits(self, full_prompt: torch.Tensor) -> torch.Tensor:
        with self._suppress_output():
            self.ensure_loaded()
            assert self.model is not None
            return self.model.forward_pass_single_logits(full_prompt)

    def deep_unload(self):
        with self._suppress_output():
            if self.model is not None:
                self.model.unload()
                self.model = None
                gc.collect()
                torch.cuda.empty_cache()

    def extract_generation_before_stop_tokens(
        self, generated: list[int], stop_token_ids: list[int | None]
    ) -> tuple[list[int], int | None]:
        stop_tokens_ids_set = {
            token_id for token_id in stop_token_ids if token_id is not None
        }
        fim_stop_token_id = None
        for index in range(len(generated)):
            if generated[index] in stop_tokens_ids_set:
                fim_stop_token_id = generated[index]
                generated = generated[:index]
                break
        return generated, fim_stop_token_id


@dataclass
class MyChunk:
    """A chunk with origin and rank information for prompt analysis."""

    origin: Optional[str] = None
    """The origin (retrieval system) for the chunk, or None/UNK if unknown."""

    rank: Optional[int] = None
    """The rank of the chunk within its origin, or None/UNK if unknown."""

    path: Optional[TokenList] = None
    """The path tokens for the chunk."""

    text: Optional[TokenList] = None
    """The text tokens for the chunk."""


@dataclass
class Result:
    """Result containing raw tokens and parsed components."""

    raw_tokens: TokenList
    """All tokens in the result."""


@dataclass
class BrokenPrompt:
    """A prompt broken down into its component parts."""

    far_prefix: TokenList
    """Far prefix tokens."""

    prefix: TokenList
    """Prefix tokens."""

    suffix: TokenList
    """Suffix tokens."""

    path: TokenList
    """Path tokens."""

    retrieval_section: List[MyChunk]
    """List of chunks from the retrieval section."""

    signature_section: List[MyChunk]
    """List of chunks from the signature section."""

    diff_section: List[MyChunk]
    """List of chunks from the diff section."""

    target: Optional[Result] = None
    """Optional target result."""


# Helper function to find next occurrence of a token
def find_next_token(prompt_tokens: TokenList, target_token: int) -> Optional[int]:
    for i in range(len(prompt_tokens)):
        if prompt_tokens[i] == target_token:
            return i
    return None


# Helper function to extract tokens between two positions
def extract_tokens_range(prompt_tokens: TokenList, start: int, end: int) -> TokenList:
    return prompt_tokens[start:end] if start < end else []


def break_apart_prompt(tokenizer: Tokenizer, prompt_tokens: TokenList) -> BrokenPrompt:
    """Break apart a tokenized prompt into its component sections.

    Args:
        tokenizer: The tokenizer used to create the prompt tokens
        prompt_tokens: The tokenized prompt to break apart

    Returns:
        BrokenPrompt with all sections identified and separated
    """
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, Qwen25CoderSpecialTokens)

    # Initialize result
    broken_prompt = BrokenPrompt(
        far_prefix=[],
        prefix=[],
        suffix=[],
        path=[],
        retrieval_section=[],
        signature_section=[],
        diff_section=[],
        target=None,
    )

    def _find_next_token(target_token: int) -> Optional[int]:
        return find_next_token(prompt_tokens=prompt_tokens, target_token=target_token)

    def _extract_tokens_range(start: int, end: int) -> TokenList:
        return extract_tokens_range(prompt_tokens=prompt_tokens, start=start, end=end)

    # Path (optional)
    filename_pos_start = _find_next_token(special_tokens.filename)
    filename_pos_end = _find_next_token(special_tokens.far_prefix)
    if filename_pos_start is not None and filename_pos_end is not None:
        broken_prompt.path = _extract_tokens_range(
            filename_pos_start + 1, filename_pos_end
        )

    # far prefix (optional)
    far_prefix_pos_start = _find_next_token(special_tokens.far_prefix)
    far_prefix_pos_end = _find_next_token(special_tokens.retrieval_section)
    if far_prefix_pos_start is not None and far_prefix_pos_end is not None:
        broken_prompt.far_prefix = _extract_tokens_range(
            far_prefix_pos_start + 1, far_prefix_pos_end
        )

    # Process retrieval section
    retrieval_pos_start = _find_next_token(special_tokens.retrieval_section)
    retrieval_pos_end = _find_next_token(special_tokens.sig_begin)
    if retrieval_pos_start is not None and retrieval_pos_end is not None:
        retrieval_tokens = _extract_tokens_range(
            retrieval_pos_start + 1, retrieval_pos_end
        )
        broken_prompt.retrieval_section = _parse_retrieval_section(
            tokenizer, retrieval_tokens
        )

    # Process signature section
    sig_begin_pos = _find_next_token(special_tokens.sig_begin)
    sig_end_pos = _find_next_token(special_tokens.sig_end)
    if sig_begin_pos is not None and sig_end_pos is not None:
        # +2 to skip the newline token, -1 to skip the newline token before sig_end
        signature_tokens = _extract_tokens_range(sig_begin_pos + 2, sig_end_pos - 1)
        broken_prompt.signature_section = _parse_signature_section(
            tokenizer, signature_tokens
        )

    # Process diff section
    diff_pos_start = _find_next_token(special_tokens.diff_section)
    diff_pos_end = _find_next_token(special_tokens.fim_prefix)
    if diff_pos_start is not None and diff_pos_end is not None:
        diff_tokens = _extract_tokens_range(diff_pos_start + 1, diff_pos_end)
        broken_prompt.diff_section = _parse_diff_section(tokenizer, diff_tokens)

    # Find prefix
    prefix_pos_start = _find_next_token(special_tokens.fim_prefix)
    prefix_pos_end = _find_next_token(special_tokens.fim_suffix)
    if prefix_pos_start is not None and prefix_pos_end is not None:
        broken_prompt.prefix = _extract_tokens_range(
            prefix_pos_start + 1, prefix_pos_end
        )

    # Find suffix
    suffix_pos_start = _find_next_token(special_tokens.fim_suffix)
    suffix_pos_end = _find_next_token(special_tokens.fim_middle)
    if suffix_pos_start is not None and suffix_pos_end is not None:
        broken_prompt.suffix = _extract_tokens_range(
            suffix_pos_start + 1, suffix_pos_end
        )

    # Find target
    target_pos_start = _find_next_token(special_tokens.fim_middle)
    target_pos_end = len(prompt_tokens)
    if target_pos_start is not None:
        target_tokens = _extract_tokens_range(target_pos_start + 1, target_pos_end)
        broken_prompt.target = Result(
            raw_tokens=target_tokens,
        )

    return broken_prompt


def _parse_retrieval_section(
    tokenizer: Tokenizer, retrieval_tokens: TokenList
) -> List[MyChunk]:
    """Parse the retrieval section to extract individual chunks.

    Expected pattern: <|ret_start|><|file_sep|>path/to/file.py<|ret_body|>content...<|ret_start|>...

    Args:
        tokenizer: The Qwen25 tokenizer (we can assert this type)
        prompt_tokens: The entire prompt token sequence
        start_pos: Start index of the retrieval section in the full prompt
        end_pos: End index of the retrieval section in the full prompt

    Returns:
        List of MyChunk objects representing individual retrieval chunks
    """

    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, Qwen25CoderSpecialTokens)

    # Since we can assert Qwen25 tokenizer, we know these tokens exist
    ret_start_token = special_tokens.ret_start

    retrieval_chunks = split_list(retrieval_tokens, [ret_start_token])

    chunks = []
    for retrieval_chunk in retrieval_chunks:
        chunk = MyChunk()

        file_start_pos = find_next_token(retrieval_chunk, special_tokens.filename)
        file_end_pos = find_next_token(retrieval_chunk, special_tokens.ret_body)
        if file_start_pos is not None and file_end_pos is not None:
            chunk.path = extract_tokens_range(
                retrieval_chunk, file_start_pos + 1, file_end_pos
            )

        body_start_pos = find_next_token(retrieval_chunk, special_tokens.ret_body)
        body_end_pos = len(retrieval_chunk)
        if body_start_pos is not None:
            chunk.text = extract_tokens_range(
                retrieval_chunk, body_start_pos + 1, body_end_pos
            )

        chunks.append(chunk)

    return chunks


def _parse_signature_section(
    tokenizer: Tokenizer, signature_tokens: TokenList
) -> List[MyChunk]:
    """Parse the signature section to extract signature chunks."""

    signature_tokens_str = tokenizer.detokenize(signature_tokens)

    signature_chunks = signature_tokens_str.split("\n\n")
    chunks = []
    for signature_chunk in signature_chunks:
        first_line = signature_chunk.split("\n")[0]
        path_str = first_line.split(": ")[1]
        path = tokenizer.tokenize_safe(path_str)
        text = tokenizer.tokenize_safe(signature_chunk)
        chunks.append(MyChunk(origin="signature", path=path, text=text))

    return chunks


def _parse_diff_section(tokenizer: Tokenizer, tokens: TokenList) -> List[MyChunk]:
    """Parse the diff section to extract diff chunks.

    Not implemented yet.
    """
    return []


def annotate_chunks(
    tokenizer: Tokenizer,
    broken_prompt: BrokenPrompt,
    retrieval_chunks: List[PromptChunk],
) -> BrokenPrompt:
    """Annotate the chunks in a broken prompt with origin and rank information.

    Args:
        broken_prompt: The broken prompt with chunks to annotate
        retrieval_chunks: The original retrieval chunks with metadata

    Returns:
        Updated BrokenPrompt with annotated chunks
    """
    # Create a mapping from chunk content to metadata
    chunk_metadata = {}

    from base.prompt_format.util import prompt_chunks_by_origin

    retrieval_chunks_by_origin = prompt_chunks_by_origin(retrieval_chunks)

    for origin, chunks in retrieval_chunks_by_origin.items():
        for i, chunk in enumerate(chunks):
            chunk_metadata[chunk.text] = {
                "origin": origin,
                "rank": i,
                "path": chunk.path,
            }

    # Annotate retrieval section chunks
    for chunk in broken_prompt.retrieval_section:
        if chunk.text:
            text_key = tokenizer.detokenize(chunk.text)
            if text_key in chunk_metadata:
                metadata = chunk_metadata[text_key]
                chunk.origin = metadata["origin"]
                chunk.rank = metadata["rank"]

    for chunk in broken_prompt.signature_section:
        if chunk.text:
            text_key = tokenizer.detokenize(chunk.text)
            if text_key in chunk_metadata:
                metadata = chunk_metadata[text_key]
                chunk.rank = metadata["rank"]

    for chunk in broken_prompt.diff_section:
        if chunk.text:
            text_key = tokenizer.detokenize(chunk.text)
            if text_key in chunk_metadata:
                metadata = chunk_metadata[text_key]
                chunk.rank = metadata["rank"]

    return broken_prompt
