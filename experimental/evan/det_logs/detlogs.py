"""Simple log scrapper to pull out patch results from determined hydra runs."""
import argparse
import csv
import re
from pathlib import Path


def parse_log_line(line):
    # Remove ANSI escape sequences
    line = re.sub(r"\x1b\[[0-9;]*[a-zA-Z]", "", line)

    # Extract patch_id
    patch_id_match = re.search(r"\|\| (.+?)\s", line)
    patch_id = patch_id_match.group(1) if patch_id_match else "N/A"

    # Extract status
    status_match = re.search(r"\s([A-Z]+)", line)
    status = status_match.group(1) if status_match else "N/A"

    # Extract wall_time
    wall_time_match = re.search(r"wall_time=([0-9.]+)", line)
    wall_time = wall_time_match.group(1) if wall_time_match else "N/A"

    # Extract test_time
    test_time_match = re.search(r"test_time=([0-9.]+)", line)
    test_time = test_time_match.group(1) if test_time_match else "N/A"

    return patch_id, status, wall_time, test_time


def main(log_file_path: Path, csv_file_path: Path, exp_id: str):
    # Initialize CSV file
    with csv_file_path.open("w") as csv_f:
        csv_writer = csv.writer(csv_f)
        csv_writer.writerow(["patch_id", "status", "wall_time", "test_time"])

        # Read log file line by line
        with log_file_path.open("r") as log_file:
            for line in log_file:
                if not re.search("wall_time", line):
                    continue
                patch_id, status, wall_time, test_time = parse_log_line(line)
                print(f"{exp_id}, {patch_id}, {status}, {wall_time}, {test_time}")
                csv_writer.writerow([exp_id, patch_id, status, wall_time, test_time])

    print(f"CSV file has been created at {csv_file_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Parse a log file and output to a CSV."
    )
    parser.add_argument(
        "log_file", type=Path, help="Path to the log file to be parsed."
    )
    args = parser.parse_args()

    if not args.log_file.exists():
        print(f"Error: Log file '{args.log_file}' does not exist.")
        exit(1)

    match = re.search(r"experiment_(\d+)", args.log_file.name)
    if match:
        trial_num = int(match.group(1))

    csv_file = Path(f"{trial_num}-results.csv")
    main(args.log_file, csv_file, trial_num)
