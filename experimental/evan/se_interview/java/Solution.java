        public List<Long> findAllPorts(long id) {
            Set<Long> allChildren = new HashSet<>();
            Queue<Long> queue = new LinkedList<>();
            queue.add(id);
            while (!queue.isEmpty()) {
                long child = queue.poll();
                allChildren.add(child);
                queue.addAll(getChildren(child));
            }

            assert allChildren.size() == new HashSet<>(allChildren).size();

            List<Long> allPorts = new ArrayList<>();
            for (Record rec : Database) {
                if (rec.alive && allChildren.contains(rec.id)) {
                    allPorts.add(rec.port);
                }
            }
            return allPorts;
        }
