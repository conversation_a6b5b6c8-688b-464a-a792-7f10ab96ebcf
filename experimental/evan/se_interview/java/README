Here's a history of how I made the project. Use mvn test to run the test cases.

 2195  mkdir interview
 2198  cd interview/
 2200  wget https://dlcdn.apache.org/maven/maven-3/3.8.8/binaries/apache-maven-3.8.8-bin.tar.gz
 2201  tar xzvf apache-maven-3.8.8-bin.tar.gz
 2202  ls apache-maven-3.8.8
 2204  code ~/.bashrc # add apache-maven-3.8.8/bin to PATH
 2205  source ~/.bashrc
 2211  mvn -v
 2212  mvn archetype:generate -DgroupId=com.aug.interview -DartifactId=my-app -DarchetypeArtifactId=maven-archetype-quickstart -DinteractiveMode=false
 2373  cd my-app/
 2375  cp ~/src/augment/experimental/evan/se_interview/java/pom.xml .
 2376  cp ~/src/augment/experimental/evan/se_interview/java/Main.java src/main/java/com/aug/interview/
 2377  cp ~/src/augment/experimental/evan/se_interview/java/SystemTest.java src/test/java/com/aug/interview/
 2378  mvn test
