import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class SystemTest {

    private void init(Main.System system) {
        List<Main.Record> recs = new ArrayList<>();
        for (int i = 0; i < 8192; i++) {
            recs.add(system.fork(system.Database.get(ThreadLocalRandom.current().nextInt(system.Database.size())).id));
        }

        Collections.shuffle(recs);
        List<Main.Record> toKill = recs.subList(0, recs.size() / 8);
        for (Main.Record rec : toKill) {
            system.kill(rec.id);
        }

        assertEquals(8193, system.Database.size());
    }

    @Test
    public void testFindAllPortsRoot() {
        Main.System system = new Main.System();
        init(system);
        List<Long> testPorts = system.findAllPorts(Main.ROOT_PID);

        List<Long> allPorts = new ArrayList<>();
        for (Main.Record rec : system.Database) {
            if (rec.alive) {
                allPorts.add(rec.port);
            }
        }

        Collections.sort(testPorts);
        Collections.sort(allPorts);

        assertArrayEquals(allPorts.toArray(), testPorts.toArray());
    }

    @Test
    public void testFindAllPortsChain() {
        Main.System system = new Main.System();
        init(system);

        // Fork a chain, and kill a random entry
        Main.Record origRec = system.Database.get(ThreadLocalRandom.current().nextInt(system.Database.size()));
        Main.Record forkParent = system.fork(origRec.id);
        List<Main.Record> forkedChildren = new ArrayList<>();
        for (int i = 0; i < 16; i++) {
            forkedChildren.add(system.fork(forkParent.id));
        }

        List<Main.Record> chainRecs = new ArrayList<>();
        chainRecs.add(forkParent);
        chainRecs.addAll(forkedChildren);
        system.kill(chainRecs.get(ThreadLocalRandom.current().nextInt(chainRecs.size())).id);

        List<Long> validChainPorts = new ArrayList<>();
        for (Main.Record rec : chainRecs) {
            if (rec.alive) {
                validChainPorts.add(rec.port);
            }
        }

        // shuffle the database so there is no exploitable pattern
        Collections.shuffle(system.Database);

        // validate chain recs
        List<Long> testPorts = system.findAllPorts(chainRecs.get(0).id);
        Collections.sort(testPorts);
        Collections.sort(validChainPorts);

        assertArrayEquals(validChainPorts.toArray(), testPorts.toArray());
    }

    @Test
    public void testFindAllPortsTree() {
        Main.System system = new Main.System();
        init(system);

        // Fork a tree, and kill a random entry
        Main.Record origRec = system.Database.get(ThreadLocalRandom.current().nextInt(system.Database.size()));
        Main.Record treeParent = system.fork(origRec.id);
        Queue<Main.Record> queue = new LinkedList<>();
        List<Main.Record> treeRecs = new ArrayList<>();
        queue.add(treeParent);
        treeRecs.add(treeParent);

        for (int i = 0; i < 8; i++) {
            Main.Record node = queue.poll();
            for (int j = 0; j < 2; j++) {
                Main.Record child = system.fork(node.id);
                treeRecs.add(child);
                queue.add(child);
            }
        }

        system.kill(treeRecs.get(ThreadLocalRandom.current().nextInt(treeRecs.size())).id);

        List<Long> validTreePorts = new ArrayList<>();
        for (Main.Record rec : treeRecs) {
            if (rec.alive) {
                validTreePorts.add(rec.port);
            }
        }

        // shuffle the database so there is no exploitable pattern
        Collections.shuffle(system.Database);

        List<Long> testPorts = system.findAllPorts(treeRecs.get(0).id);
        Collections.sort(testPorts);
        Collections.sort(validTreePorts);

        // Validate tree recs
        assertArrayEquals(validTreePorts.toArray(), testPorts.toArray());
    }
}
