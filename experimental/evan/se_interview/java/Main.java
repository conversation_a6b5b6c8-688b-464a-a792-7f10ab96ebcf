import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class Main {

    public static final long ROOT_PID = 0L;
    public static final long MAX_PID = (long) Math.pow(2, 32);
    public static final long MAX_PORT = (long) Math.pow(2, 32);

    static class Record {
        long id;    // unique id
        long port;  // port id
        boolean alive; // True if record is alive, False if dead

        public Record(long id, long port, boolean alive) {
            this.id = id;
            this.port = port;
            this.alive = alive;
        }
    }

    static class System {
        List<Record> Database = new ArrayList<>();
        Map<Long, List<Long>> Relationship = new HashMap<>();

        public System() {
            Database.add(new Record(ROOT_PID, 0, true));
            Relationship.put(ROOT_PID, new ArrayList<>());
        }

        public List<Long> getChildren(long id) {
            return Relationship.get(id);
        }

        public void kill(long id) {
            for (Record rec : Database) {
                if (rec.id == id) {
                    rec.alive = false;
                }
            }
        }

        public Record fork(long parentId) {
            long childId = ThreadLocalRandom.current().nextLong(ROOT_PID + 1, MAX_PID);
            while (Relationship.containsKey(childId)) {
                childId = ThreadLocalRandom.current().nextLong(ROOT_PID + 1, MAX_PID);
            }
            long portId = ThreadLocalRandom.current().nextLong(1, MAX_PORT);

            Relationship.get(parentId).add(childId);
            Relationship.put(childId, new ArrayList<>());

            Record rec = new Record(childId, portId, true);
            Database.add(rec);
            return rec;
        }

        public List<Long> findAllPorts(long id) {
            Set<Long> allChildren = new HashSet<>();
            Queue<Long> queue = new LinkedList<>();
            queue.add(id);
            while (!queue.isEmpty()) {
                long child = queue.poll();
                allChildren.add(child);
                queue.addAll(getChildren(child));
            }

            assert allChildren.size() == new HashSet<>(allChildren).size();

            List<Long> allPorts = new ArrayList<>();
            for (Record rec : Database) {
                if (rec.alive && allChildren.contains(rec.id)) {
                    allPorts.add(rec.port);
                }
            }
            return allPorts;
        }
    }

    public static void main(String[] args) {
        // Test the code if needed.
    }
}
