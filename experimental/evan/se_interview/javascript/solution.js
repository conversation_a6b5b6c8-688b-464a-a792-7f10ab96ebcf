findAllPorts(id) {
    const allChildren = new Set();
    const queue = [id];
    while (queue.length) {
        const child = queue.shift();
        allChildren.add(child);
        queue.push(...this.getChildren(child));
    }

    const allPorts = [];
    for (let rec of this.Database) {
        if (rec.alive && allChildren.has(rec.id)) {
            allPorts.push(rec.port);
        }
    }
    return allPorts;
}
