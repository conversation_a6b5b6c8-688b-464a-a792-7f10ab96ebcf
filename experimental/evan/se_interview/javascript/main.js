class Record {
    constructor(id, port, alive) {
        this.id = id;
        this.port = port;
        this.alive = alive;
    }
}

class System {
    constructor() {
        this.ROOT_PID = 0;
        this.MAX_PID = Math.pow(2, 32);
        this.MAX_PORT = Math.pow(2, 32);

        this.Database = [new Record(this.ROOT_PID, 0, true)];
        this.Relationship = {
            [this.ROOT_PID]: []
        };
    }

    getChildren(id) {
        return this.Relationship[id] || [];
    }

    kill(id) {
        for (let rec of this.Database) {
            if (rec.id === id) {
                rec.alive = false;
            }
        }
    }

    fork(parentId) {
        let childId = Math.floor(Math.random() * (this.MAX_PID - this.ROOT_PID - 1)) + this.ROOT_PID + 1;
        while (this.Relationship.hasOwnProperty(childId)) {
            childId = Math.floor(Math.random() * (this.MAX_PID - this.ROOT_PID - 1)) + this.ROOT_PID + 1;
        }
        let portId = Math.floor(Math.random() * (this.MAX_PORT - 1)) + 1;

        this.Relationship[parentId].push(childId);
        this.Relationship[childId] = [];

        const rec = new Record(childId, portId, true);
        this.Database.push(rec);
        return rec;
    }

    findAllPorts(id) {
        // Implement this method
        const allPorts = [];




        return allPorts;
    }
}

// Example usage
const system = new System();
const rec = system.fork(system.ROOT_PID);
console.log(system.findAllPorts(system.ROOT_PID));

module.exports = {
    System: System,
    Record: Record
};
