To run the tests using Jest:

Maybe clean your system:
 sudo apt-get purge nodejs npm
 sudo apt autoremove
 sudo apt update

Install prereqs:
 mkdir interview_js
 cd interview_js
 sudo apt update
 curl -sL https://deb.nodesource.com/setup_20.x| sudo -E bash - # wait 60 secs
 sudo apt-get install -y nodejs
 npm install --save-dev jest

Copy in files from Git:
 cp ~/src/augment/experimental/evan/se_interview/javascript/*.js .
 cp ~/src/augment/experimental/evan/se_interview/javascript/package.json .

Run the test:
    npm test
