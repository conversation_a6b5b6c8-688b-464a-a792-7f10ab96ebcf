const { Record, System } = require('./main.js');  // Adjust the path accordingly

describe("System Tests", () => {
    const init = (system) => {
        const recs = [];
        for (let i = 0; i < 8192; i++) {
            recs.push(system.fork(system.Database[Math.floor(Math.random() * system.Database.length)].id));
        }

        const toKill = recs.slice(0, recs.length / 8);
        toKill.forEach(rec => system.kill(rec.id));

        expect(system.Database.length).toBe(8193);
    };

    test("testFindAllPortsRoot", () => {
        const system = new System();
        init(system);
        const testPorts = system.findAllPorts(system.ROOT_PID);

        const allPorts = system.Database.filter(rec => rec.alive).map(rec => rec.port);

        expect(testPorts.sort()).toEqual(allPorts.sort());
    });

    test("testFindAllPortsChain", () => {
        const system = new System();
        init(system);

        const origRec = system.Database[Math.floor(Math.random() * system.Database.length)];
        const forkParent = system.fork(origRec.id);
        const forkedChildren = [];
        for (let i = 0; i < 16; i++) {
            forkedChildren.push(system.fork(forkParent.id));
        }

        const chainRecs = [forkParent, ...forkedChildren];
        system.kill(chainRecs[Math.floor(Math.random() * chainRecs.length)].id);

        const validChainPorts = chainRecs.filter(rec => rec.alive).map(rec => rec.port);

        const testPorts = system.findAllPorts(chainRecs[0].id);
        expect(testPorts.sort()).toEqual(validChainPorts.sort());
    });

    test("testFindAllPortsTree", () => {
        const system = new System();
        init(system);

        const origRec = system.Database[Math.floor(Math.random() * system.Database.length)];
        const treeParent = system.fork(origRec.id);
        const queue = [treeParent];
        const treeRecs = [treeParent];

        for (let i = 0; i < 8; i++) {
            const node = queue.shift();
            for (let j = 0; j < 2; j++) {
                const child = system.fork(node.id);
                treeRecs.push(child);
                queue.push(child);
            }
        }

        system.kill(treeRecs[Math.floor(Math.random() * treeRecs.length)].id);

        const validTreePorts = treeRecs.filter(rec => rec.alive).map(rec => rec.port);

        const testPorts = system.findAllPorts(treeRecs[0].id);
        expect(testPorts.sort()).toEqual(validTreePorts.sort());
    });
});
