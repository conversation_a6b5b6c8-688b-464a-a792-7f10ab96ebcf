    def find_all_ports(self, id: pid_t) -> list[port_t]:
        """Return the list of all ports from the given process family.
        TO BE COMPLETED
        """

        all_children = []
        queue = [id]
        while queue:
            child = queue.pop()
            all_children.append(child)
            queue.extend(self.get_children(child))

        assert len(all_children) == len(set(all_children))
        all_children = set(all_children)
        all_ports = []
        for rec in self.Database:
            if rec.alive and rec.id in all_children:
                all_ports.append(rec.port)
        return all_ports
