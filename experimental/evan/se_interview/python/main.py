
import random
from dataclasses import dataclass

ROOT_PID = 0
MAX_PID = 2**32
MAX_PORT = 2**32

pid_t = int
port_t = int

@dataclass
class Record:
  id: pid_t    # unique id
  port: port_t # port id
  alive: bool  # True if record is alive, False if dead

class System:
    def __init__(self):
        self.Database: list[Record]= [Record(ROOT_PID, 0, True)]
        self.Relationship: dict[pid_t, list[pid_t]]= {ROOT_PID: []}

    def get_children(self, id: pid_t) -> list[pid_t]:
        """Return the list of direct decendents from the id"""
        return self.Relationship.get(id)

    def kill(self, id: pid_t):
        """Kill the given process"""
        for rec in self.Database:
            if rec.id == id:
                rec.alive = False

    def fork(self, parent_id: pid_t) -> Record:
        """Return a new child process forked from the given parent."""
        child_id = random.randint(ROOT_PID + 1, MAX_PID)
        while child_id in self.Relationship:
            child_id = random.randint(ROOT_PID + 1, MAX_PID)
        port_id = random.randint(1, MAX_PORT)

        self.Relationship[parent_id].append(child_id)
        self.Relationship[child_id] = []

        rec = Record(child_id, port_id, True)
        self.Database.append(rec)
        return rec

    def find_all_ports(self, id: pid_t) -> list[port_t]:
        """Return the list of all ports from the given process family.

        TO BE COMPLETED
        """
        pass
