
import random
import main

def init(system):
    # Create a random tree from the root, and kill 1/8
    recs = [system.fork(random.choice(system.Database).id) for i in range(8192)]
    to_kill = random.sample(recs, len(recs) // 8)
    for rec in to_kill:
        system.kill(rec.id)
    assert len(system.Database) == len(recs) + 1

def test_find_all_ports_root():
    system = main.System()
    init(system)
    test_ports = system.find_all_ports(main.ROOT_PID)
    all_ports = [x.port for x in system.Database if x.alive]
    assert sorted(test_ports) == sorted(all_ports)

def test_find_all_ports_chain():
    system = main.System()
    init(system)

    # Fork a chain, and kill a random entry.
    fork_parent = system.fork(random.choice(system.Database).id)
    forked_children = [system.fork(fork_parent.id) for i in range(16)]
    chain_recs = [fork_parent] + forked_children
    system.kill(random.choice(chain_recs).id)
    valid_chain_ports = [rec.port for rec in chain_recs if rec.alive]

    # shuffle the database so there is no exploitable pattern
    random.shuffle(system.Database)

    # Validate chain recs
    test_ports = system.find_all_ports(chain_recs[0].id)
    assert sorted(test_ports) == sorted(valid_chain_ports)

def test_find_all_ports_tree():
    system = main.System()
    init(system)

    # Fork a tree, and kill a random entry
    tree_parent = system.fork(random.choice(system.Database).id)
    queue = [tree_parent]
    tree_recs = [tree_parent]
    for i in range(8):
        node = queue.pop(0)
        child_nodes = [system.fork(node.id) for x in range(2)]
        tree_recs.extend(child_nodes)
        queue.extend(child_nodes)
    system.kill(random.choice(tree_recs).id)
    valid_tree_ports = [rec.port for rec in tree_recs if rec.alive]

    # shuffle the database so there is no exploitable pattern
    random.shuffle(system.Database)

    # Validate tree recs
    test_ports = system.find_all_ports(tree_recs[0].id)
    assert sorted(test_ports) == sorted(valid_tree_ports)
