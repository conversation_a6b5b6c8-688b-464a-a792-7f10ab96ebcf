"""Simple tool for doing GitHub repo searches using the GitHub API."""
import json
import os

import requests
from dotenv import load_dotenv


def get_repositories(query, per_page=100):
    """Get repositories from GitHub API using the search query."""
    page = 1
    all_repos = []

    while True:
        params = {
            "q": query,
            "sort": "stars",
            "order": "desc",
            "per_page": per_page,
            "page": page,
        }
        response = requests.get(
            GITHUB_API_URL, headers=HEADERS, params=params, timeout=10
        )

        if response.status_code == 200:
            data = json.loads(response.text)
            repos = data.get("items", [])

            if not repos:
                break

            all_repos.extend(repos)
            print(f"Found {len(repos)} repositories on page {page}")
            page += 1
        else:
            print(f"Error: {response.status_code}, {response.text}")
            break

    return all_repos


load_dotenv()
token = os.getenv("GITHUB_TOKEN")

# Set API search URL and required headers
GITHUB_API_URL = "https://api.github.com/search/repositories"
HEADERS = {
    "Accept": "application/vnd.github+json",
    "Authorization": f"token {token}",
}

# Define the search query using keywords (Bazel, Tox) and minimum stars and languages
# query = "'unit test' in:description,readme stars:>=1000 language:go  license:GPL"
SEARCH_QUERY = "stars:>=1000 language:go  license:GPL"
repositories = get_repositories(SEARCH_QUERY)
print(f"Found {len(repositories)} repositories.")
for repo in repositories:
    print(f"{repo['html_url']} - {repo['stargazers_count']} stars")
