package main

import (
	"bufio"
	"encoding/json"
	"flag"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"io/ioutil"
	"log"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
)

var TaskNum int = 0

type MetaContainer struct {
	Text string `json:"text"`
	Meta Meta   `json:"meta"`
}

type Meta struct {
	FileName           string `json:"file_name"`
	GroundTruth        string `json:"ground_truth"`
	LineNo             int    `json:"line_no"`
	RepoLanguage       string `json:"repo_language"`
	RepoName           string `json:"repo_name"`
	ContextStartLineno int    `json:"context_start_lineno"`
	TaskID             string `json:"task_id"`
}

type Block struct {
	startLine int
	endLine   int
	execCount int
}

func main() {
	// Define command-line flags
	sourceDir := flag.String("dir", ".", "Source directory path")
	coverageFile := flag.String("coverage", "", "Coverage file path")
	repoName := flag.String("repo", "", "Repo name")
	flag.Parse()

	// Validate source directory path
	if _, err := os.Stat(*sourceDir); os.IsNotExist(err) {
		log.Fatalf("Source directory does not exist: %s", *sourceDir)
	}
	// Validate coverage file path
	if _, err := os.Stat(*coverageFile); os.IsNotExist(err) {
		log.Fatalf("Coverage file does not exist: %s", *coverageFile)
	}

	coverage, err := parseCoverageFile(*coverageFile, *repoName)
	if err != nil {
		log.Fatalf("Error parsing coverage file: %v", err)
	}

	err = filepath.Walk(*sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.Printf("Error accessing path %s: %v\n", path, err)
			return nil
		}

		if !info.IsDir() && filepath.Ext(path) == ".go" {
			metaContainers := generateFunctTestSites(path, coverage, *repoName)
			for _, metaContainer := range metaContainers {
				jsonData, _ := json.MarshalIndent(metaContainer, "", "  ")
				fmt.Println(string(jsonData))
			}
		}
		return nil
	})

	if err != nil {
		log.Fatal(err)
	}
}

func parseCoverageFile(coverageFilePath string, repoName string) (map[string][]Block, error) {
	coverage := make(map[string][]Block)

	file, err := os.Open(coverageFilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		fields := strings.Fields(line)
		if len(fields) >= 3 {
			fullFilename := strings.Split(fields[0], ":")[0]
			filename := strings.TrimPrefix(fullFilename, repoName)
			coords := strings.Split(fields[0], ":")[1]
			lines := strings.Split(coords, ",")
			start, _ := strconv.Atoi(strings.Split(lines[0], ".")[0])
			end, _ := strconv.Atoi(strings.Split(lines[1], ".")[0])
			count, _ := strconv.Atoi(fields[len(fields)-1])

			coverage[filename] = append(coverage[filename], Block{
				startLine: start,
				endLine:   end,
				execCount: count,
			})
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return coverage, nil
}

func checkCoverage(coverage map[string][]Block, filename string, startLine, endLine int) bool {
	// Check if file is in coverage map
	blocks, ok := coverage[filename]

	if !ok {
		// File not found in coverage data
		log.Printf("File %s not found in coverage data\n", filename)
		return false
	}

	// print out params
	for _, block := range blocks {
		if (block.startLine <= startLine && block.endLine >= startLine) ||
			(block.startLine <= endLine && block.endLine >= endLine) ||
			(block.startLine >= startLine && block.endLine <= endLine) {
			if block.execCount == 0 {
				return false
			}
		}
	}
	return true
}

func generateFunctTestSites(filename string, coverage map[string][]Block, repoName string) []MetaContainer {
	var metaContainers []MetaContainer

	// Read the Go source file
	src, err := ioutil.ReadFile(filename)
	if err != nil {
		log.Printf("Error reading file: %s", filename)
		return nil
	}

	// Split the content into lines
	lines := strings.Split(string(src), "\n")

	// Parse the Go source code
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, filename, src, parser.ParseComments)
	if err != nil {
		log.Printf("Error parsing file: %s", filename)
		return nil
	}

	// tidy up the repo name
	dirPath, _ := filepath.Split(filename)
	elements := strings.Split(dirPath, string(filepath.Separator))
	var tidyRepo = strings.Replace(repoName, "github.com/", "repos/", 1)
	tidyRepo = strings.TrimSuffix(tidyRepo, "/")

	repoName = filepath.Join(repoName, elements[0])
	var shortRepo = path.Base(repoName)

	// Traverse the AST and extract function information
	for _, decl := range file.Decls {
		if fn, ok := decl.(*ast.FuncDecl); ok {
			// Get function name
			funcName := fn.Name.Name

			// Get function start and end lines
			endLine := fset.Position(fn.End()).Line + 1
			firstImplLine := getFirstImplLine(fset, fn)

			// Skip functions without implementation
			if firstImplLine == 0 {
				log.Println("chucking " + funcName + ": empty implmentation\n")
				continue
			}

			// Skip functions too big or too small
			maskLen := endLine - (firstImplLine - 1)
			if maskLen > 30 || maskLen < 4 {
				log.Println("chucking " + funcName + ": mask too big or too small\n")
				continue
			}

			// Skip functions with no coverage
			if !checkCoverage(coverage, filename, firstImplLine-1, endLine) {
				log.Println("chucking " + funcName + ": not fully covered\n")
				continue
			}

			// All checks have passed, we have a test site!
			groundTruth := ""
			for i := firstImplLine - 1; i < firstImplLine-1+maskLen && i < len(lines); i++ {
				groundTruth += lines[i] + "\n"
			}

			// Set text to be the entire file up to the firstImplLine -1
			text := strings.Join(lines[:firstImplLine-1], "\n")

			TaskNum++
			meta := Meta{
				RepoName:           tidyRepo,
				FileName:           filename,
				RepoLanguage:       "Go",
				TaskID:             shortRepo + "/" + strconv.Itoa(TaskNum),
				GroundTruth:        groundTruth,
				ContextStartLineno: 0,
				LineNo:             firstImplLine - 1,
			}
			metaContainer := MetaContainer{
				Text: text,
				Meta: meta,
			}
			metaContainers = append(metaContainers, metaContainer)
		}
	}

	return metaContainers
}

// getFirstLine returns the line number of the first line of code after the function declaration
func getFirstImplLine(fset *token.FileSet, fn *ast.FuncDecl) int {
	if fn.Body == nil || len(fn.Body.List) == 0 {
		return 0
	}

	firstStmt := fn.Body.List[0]
	line := fset.Position(firstStmt.Pos()).Line
	return line
}
