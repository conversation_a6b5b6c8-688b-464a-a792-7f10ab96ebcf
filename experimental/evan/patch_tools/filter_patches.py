"""Tool for looking at patches and applying patches locally."""
import argparse
from pathlib import Path
from typing import Dict, <PERSON>, <PERSON><PERSON>

from research.eval.patch_lib import Patch  # pylint: disable=no-name-in-module
from research.static_analysis.parsing import (  # pylint: disable=no-name-in-module
    LineMap,
)


class Block:
    """Simple structure that reflects Go coverage report."""

    def __init__(self, start_line, end_line, exec_count):
        self.start_line = start_line
        self.end_line = end_line
        self.exec_count = exec_count


def get_span(patch: Patch) -> Tuple[int, int]:
    line_map = LineMap(patch.file_content)
    # print(f"char span: {patch.char_start}:{patch.char_end}")
    line_start = line_map.get_line_number(patch.char_start)
    line_end = line_map.get_line_number(patch.char_end)
    # print(f"span: {line_start}:{line_end}")
    return (line_start, line_end)


def parse_coverage_file(
    coverage_file_path: Path, repo_name: str
) -> Dict[str, List[Block]]:
    coverage = {}

    with coverage_file_path.open("r") as file:
        for line in file:
            fields = line.strip().split(" ")
            if len(fields) >= 3:
                full_filename = fields[0].split(":")[0]
                filename = full_filename.replace(repo_name, "", 1)
                coords = fields[0].split(":")[1]
                lines = coords.split(",")
                start = int(lines[0].split(".")[0])
                end = int(lines[1].split(".")[0])
                count = int(fields[-1])

                if filename not in coverage:
                    coverage[filename] = []

                coverage[filename].append(Block(start, end, count))
    return coverage


def check_coverage(coverage: Dict, patch: Patch):
    # Check if file is in coverage map
    filename = f"{patch.repo_name}/{patch.file_name}"
    # print(f"filename == {filename}")
    start_line, end_line = get_span(patch)
    # print(f"coverage dict == {coverage}")
    blocks = coverage.get(filename)
    # print(f"blocks == {blocks}")

    if blocks is None:
        # File not found in coverage data
        return False

    # print out params
    for block in blocks:
        if block.start_line > end_line:
            break

        if (
            (block.start_line <= start_line and block.end_line >= start_line)
            or (block.start_line <= end_line and block.end_line >= end_line)
            or (block.start_line >= start_line and block.end_line <= end_line)
        ):
            if block.exec_count == 0:
                return False

    return True


def main():
    parser = argparse.ArgumentParser(description="Random tool for looking at patches")
    parser.add_argument("patch", type=str, help="Path to file of patches.")
    parser.add_argument("coverage", type=str, help="Path to coverage file.")
    parser.add_argument(
        "prefix",
        type=str,
        help="Prefix to clip in coverage.out, like github.com/jaegertracing/",
    )
    parser.add_argument(
        "--json", action="store_true", help="Output JSON cases pass filters"
    )

    # parser.add_argument("-v", action="store_true", help="Verbose output")
    # parser.add_argument("--apply", action="store_true", help="Apply the patch.")
    # parser.add_argument("--list", action="store_true", help="Verbose output")
    args = parser.parse_args()

    # Access the value of the 'patch' argument
    patch_file = Path(args.patch)
    cov_file = Path(args.coverage)

    # Validate patch file path
    if not patch_file.exists():
        raise FileNotFoundError(f"Patch file does not exist: {patch_file}")

    # Validate coverage file path
    if not cov_file.exists():
        raise FileNotFoundError(f"Coverage file does not exist: {cov_file}")

    # Validate we've a reasonbable prefix
    assert args.prefix.endswith("/")

    coverage = parse_coverage_file(cov_file, args.prefix)

    # Read file and process the first line
    with Path(patch_file).open("r") as patch_fh:
        for patch_line in patch_fh:
            patch = Patch.from_json(patch_line)
            is_covered = check_coverage(coverage, patch)
            if args.json:
                if is_covered:
                    print(patch.to_json())
            else:
                print(
                    f"Patch ID: {patch.patch_id} {patch.repo_name}/{patch.file_name}: {is_covered}"
                )


if __name__ == "__main__":
    main()
