"""Tool for looking at patches and applying patches locally."""
import argparse
import re
import sys
from datetime import datetime
from pathlib import Path
from typing import List

from research.eval.patch_lib import Patch  # pylint: disable=no-name-in-module


def apply_patch(patch: Patch):
    """Applies the patch_content and writes out a bak_ file name."""
    file_name = Path(patch.file_name).name
    pfile_name = "bak_" + file_name
    pfile = patch.patched_file_content

    # write out pfile
    with Path(pfile_name).open("w") as f:
        f.write(pfile)

    print(
        f"Wrote {pfile_name} with patch_content (@{patch.char_start}:{patch.char_end})"
        f" of <{patch.patch_content}>"
    )
    print(f"Udiff: f{patch.as_udiff()}")


def print_patch_details(patch: Patch, verbose: bool = True):
    print(
        f"Patch ID: {patch.patch_id}: {patch.repo_name}/{patch.file_name} {patch.char_start}:{patch.char_end}"
    )
    if verbose:
        print("Ground Truth:")
        print(patch.original_patch_content)
        if patch.patch_content:
            print("Completion Found:")
            print(patch.patch_content)


def set_ground_truth(patch: Patch):
    patch.patch_content = patch.original_patch_content
    print(patch.to_json())


def print_result(patch: Patch):
    for line in patch.run_output:
        print(f"line == {line}")


#    print(patch.run_output)


def print_dehydrated_patch(patch: Patch):
    print(patch.without_file_content().to_json())


def remove_ansi_codes(s):
    # This regular expression pattern matches ANSI escape sequences
    ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
    return ansi_escape.sub("", s)


def main():
    parser = argparse.ArgumentParser(description="Random tool for looking at patches")
    parser.add_argument("patch", type=str, help="The patch string.")
    parser.add_argument("-v", action="store_true", help="Verbose output")
    parser.add_argument("--apply", action="store_true", help="Apply the patch.")
    parser.add_argument("--gold", action="store_true", help="Set to ground truth.")
    parser.add_argument("--list", action="store_true", help="Verbose output")
    parser.add_argument(
        "--dehydrate", action="store_true", help="Output patches w/o file contents"
    )
    parser.add_argument("--add_sha", type=str, default=None, help="Add sha (optional)")
    parser.add_argument(
        "--pid",
        type=str,
        default=None,
        help="Only print out a specific patch_id (optional)",
    )
    parser.add_argument("--no_results", action="store_true", help="Don't show results")
    args = parser.parse_args()

    # Access the value of the 'patch' argument
    patch_file = args.patch

    # Read file and process the first line
    with Path(patch_file).open("r") as f:
        patch_string = f.readline()
    patch = Patch.from_json(patch_string)

    patches: List[Patch] = []
    with Path(patch_file).open("r") as patch_fh:
        for line in patch_fh:
            patch = Patch.from_json(line)
            patches.append(patch)

    if args.pid:
        for patch in patches:
            if patch.patch_id == args.pid:
                patches = [patch]
                break
        else:
            print(f"Patch ID {args.pid} not found")
            sys.exit(1)

    def extract_and_convert_date_time(string, year=2023):
        # Regular expression to match the date and time format from the given string
        pattern = (
            r"\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) \d{1,2} \d{2}:\d{2}\b"
        )

        match = re.search(pattern, string)
        if match:
            date_str = match.group(0)
            # Convert the extracted date_str into a datetime object with the specified year
            return datetime.strptime(f"{year} {date_str}", "%Y %b %d %H:%M")
        return None

    start_time = None
    for patch in patches:
        if args.apply:
            apply_patch(patch)
        elif args.gold:
            set_ground_truth(patch)
        elif args.list:
            print_patch_details(patch)
        elif args.dehydrate:
            print_dehydrated_patch(patch)
        elif args.add_sha:
            patch.commit_sha = args.add_sha
            print(patch.to_json())
        else:
            if args.v:
                print_patch_details(patch, args.no_results)
            if not args.no_results:
                if patch.run_output:
                    for line in patch.run_output.split("\n"):
                        print(line)
                else:
                    print(f"ERROR: No results for {patch.patch_id}")
            else:
                results = []
                cpu = None
                time = None
                patch_id = patch.patch_id
                status = None
                if patch.run_output is None:
                    # print to STDERR
                    print(
                        f"ERROR: No results for {patch.patch_id} in {patch_file}",
                        file=sys.stderr,
                    )
                    status = "OTHER"
                    results.append((patch_id, status, cpu, time, start_time))
                    print(f"{patch_file}, {patch_id}, {status}, , , {start_time}")
                    continue
                start_time = None
                for line in patch.run_output.split("\n"):
                    if "model name" in line:
                        cpu_match = re.search(r"model name\s*:\s*(.*)$", line)
                        if cpu_match:
                            cpu = cpu_match.group(1).strip()
                    elif "test_time" in line:
                        time_match = re.search(r"test_time=(\S+)", line)
                        if time_match:
                            time = time_match.group(1).strip()
                        patch_id_match = re.match(r"^\s*(\S+)", line)
                        if patch_id_match:
                            patch_id = patch_id_match.group(1).strip()
                        status_match = re.search(r"\S+\s+(\S+)\s+wall_time", line)
                        if status_match:
                            status = remove_ansi_codes(status_match.group(1))

                    elif start_time is None:
                        start_time = extract_and_convert_date_time(line)

                    if cpu and time:
                        results.append((patch_id, status, cpu, time, start_time))
                        print(
                            f"{patch_file}, {patch_id}, {status}, {time}, {cpu}, {start_time}"
                        )
                        # Reset time for the next pair
                        time = None
                        cpu = None
                        patch_id = None
                        status = None


if __name__ == "__main__":
    main()
