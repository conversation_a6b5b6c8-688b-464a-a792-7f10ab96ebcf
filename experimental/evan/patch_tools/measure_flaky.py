"""Runs GO unit tests repeatedly to see if any are flaky."""
import collections
import re
import subprocess


def run_tests():
    # Execute the 'go test' command
    result = subprocess.run(
        ["go", "test", "-count=1", "./..."],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        text=True,
        check=False,
    )

    # If there are failures, parse the output and return the names of the failing tests
    if result.returncode != 0:
        failing_tests = re.findall(r"--- FAIL: (\w+)", result.stdout)
        print(result.stdout)
        print(failing_tests)
        return failing_tests
    else:
        return []


def main():
    # Keep track of the number of failures for each test
    failure_counts = collections.defaultdict(int)

    # Run the test suite 100 times
    for _ in range(100):
        print(f"Running test suite {_ + 1}/100")
        failing_tests = run_tests()
        print(f"Hit {len(failing_tests)}.")
        for test in failing_tests:
            failure_counts[test] += 1

    # Print the results
    for test, count in failure_counts.items():
        print(f"Test {test} failed {count} times")


if __name__ == "__main__":
    main()
