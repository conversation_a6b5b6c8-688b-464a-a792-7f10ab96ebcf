import json
import logging
import os
import subprocess
import sys

import _gojsonnet as jsonnet
import git
import github
from git import GitCommandError, Repo
from github import Github

# Add the root directory to sys.path
root_dir = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..", "..")
)
sys.path.append(root_dir)

# Set BUILD_WORKSPACE_DIRECTORY environment variable
os.environ["BUILD_WORKSPACE_DIRECTORY"] = root_dir

# Get list of PR approvers
pr_approvers = os.environ.get("PR_APPROVERS", "").split(",")

logging.info(f"root_dir absolute path: {os.path.abspath(root_dir)}")


REPO_URL = os.environ.get("REPO_URL", "https://github.com/your-org/your-repo.git")
REPO_DIR = os.environ.get("REPO_DIR", "/home/<USER>/sandbox/tenants")
GITHUB_TOKEN = os.environ.get("GITHUB_TOKEN")


def _ensure_repo_exists():
    if not os.path.exists(REPO_DIR):
        os.makedirs(REPO_DIR, exist_ok=True)

    if not os.path.exists(os.path.join(REPO_DIR, ".git")):
        try:
            logging.info(f"Cloning repository from {REPO_URL} to {REPO_DIR}")
            Repo.clone_from(REPO_URL, REPO_DIR)
        except GitCommandError as e:
            raise Exception(f"Failed to clone repository: {str(e)}")


def _symlink_tenants_jsonnet():
    # Make ../../deploy/tenants/tenants.jsonnet a symlink pointing to
    # REPO_DIR/deploy/tenants/tenants.jsonnet
    target_path = os.path.join(REPO_DIR, "deploy/tenants/tenants.jsonnet")
    link_path = os.path.join(root_dir, "deploy/tenants/tenants.jsonnet")

    # Ensure the directory exists
    os.makedirs(os.path.dirname(link_path), exist_ok=True)

    # Remove the existing file if it exists
    if os.path.exists(link_path):
        os.remove(link_path)

    # Create the symlink
    os.symlink(target_path, link_path)


def _generate_namespaces_json():
    jsonnet_path = os.path.join(root_dir, "deploy", "tenants", "namespaces.jsonnet")
    json_path = os.path.join(root_dir, "deploy", "tenants", "namespaces.json")
    tenants_jsonnet_path = os.path.realpath(
        os.path.join(root_dir, "deploy", "tenants", "tenants.jsonnet")
    )

    # Ensure the directory exists
    os.makedirs(os.path.dirname(json_path), exist_ok=True)

    try:
        # Use -J flag to add the root directory to the Jsonnet library path
        result = subprocess.run(
            [
                "jsonnet",
                "-J",
                root_dir,
                "--ext-str",
                f"tenantsJsonnetPath={tenants_jsonnet_path}",
                jsonnet_path,
            ],
            capture_output=True,
            text=True,
            check=True,
        )
        with open(json_path, "w") as f:
            f.write(result.stdout)
        logging.info(f"Successfully generated {json_path}")
    except subprocess.CalledProcessError as e:
        logging.error(f"Error generating namespaces.json: {e}")
        raise
    except Exception as e:
        logging.error(f"Unexpected error generating namespaces.json: {e}")
        raise


def prepare_sandbox():
    """
    Prepare the sandbox environment by cloning the tenant repository,
    creating a symlink to the tenants.jsonnet file, and generating
    the namespaces.json file.

    This is not intended to be a permanent solution so we can accept
    generating the namespaces.json file once when we start up; any
    updates to namespaces.jsonnet will require a restart.
    """
    _ensure_repo_exists()
    _symlink_tenants_jsonnet()
    _generate_namespaces_json()


def _get_repo():
    _ensure_repo_exists()
    try:
        return Repo(REPO_DIR)
    except Exception as e:
        raise Exception(f"Failed to access repository at {REPO_DIR}: {str(e)}")


def _update_main_branch(repo):
    try:
        repo.remotes.origin.fetch()
        repo.git.checkout("main")
        pull_result = repo.git.pull("origin", "main")
        logging.debug(f"Pull main result: {pull_result}")
    except GitCommandError as git_error:
        logging.error(
            f"Git command error: {git_error.command}, {git_error.status}, {git_error.stderr}"
        )
        raise Exception(
            f"Failed to update main branch: Git command error - {git_error.stderr}"
        )
    except Exception as e:
        logging.error(f"Unexpected error in _update_main_branch: {str(e)}")
        raise Exception(f"Failed to update main branch: {str(e)}")


def _checkout_branch(repo, branch):
    try:
        repo.remotes.origin.fetch()

        # Check if the branch exists locally
        if branch in repo.heads:
            repo.git.checkout(branch)
        # Check if the branch exists remotely
        elif f"origin/{branch}" in repo.refs:
            repo.git.checkout(branch, b=True)
        else:
            # If the branch doesn't exist, create it based on main
            repo.git.checkout("main")
            repo.remotes.origin.pull()
            repo.git.checkout(b=branch)
            logging.info(f"Created new branch: {branch}")
    except Exception as e:
        raise Exception(f"Failed to checkout branch: {str(e)}")


def _tenant_exists(tenant_name, domain=None):
    try:
        tenants_path = os.path.join(root_dir, "deploy", "tenants", "tenants.jsonnet")

        def import_callback(dir, rel):
            full_path = os.path.join(root_dir, rel)
            if not os.path.exists(full_path):
                raise RuntimeError(f"File not found: {full_path}")
            with open(full_path, "rb") as f:
                return full_path, f.read()

        json_str = jsonnet.evaluate_file(
            tenants_path,
            import_callback=import_callback,
        )

        logging.debug(f"tenants_jsonnet: {json_str}")
        raw_tenants = json.loads(json_str)

        # Check if tenant_name exists in any of the tenant lists
        tenant_exists = any(
            any(tenant.get("name") == tenant_name for tenant in tenant_list)
            for tenant_list in raw_tenants.values()
        )

        domain_exists = False
        if domain is not None:
            domain_exists = any(
                tenant.get("domain") == domain
                for tenant_list in raw_tenants.values()
                for tenant in tenant_list
            )
    except Exception as e:
        logging.error(f"Failed to check if tenant {tenant_name} exists: {str(e)}")
        raise Exception(f"Failed to check if tenant {tenant_name} exists: {str(e)}")

    if tenant_exists:
        return True, f"Tenant {tenant_name} already exists"
    elif domain_exists:
        return True, f"Domain {domain} already in use by another tenant"
    return False, None


def _create_branch(repo, branch_name):
    try:
        new_branch = repo.create_head(branch_name)
        new_branch.checkout()
    except Exception as e:
        raise Exception(f"Failed to create branch {branch_name}: {str(e)}")


def _commit_changes(repo, file_path, commit_message):
    try:
        repo.git.add(file_path)
        repo.index.commit(commit_message)
    except Exception as e:
        raise Exception(f"Failed to commit changes: {str(e)}")
    return repo.head.commit


def _push_branch(repo, branch_name):
    try:
        try:
            pull_output = repo.git.pull(
                "origin", branch_name, with_extended_output=True
            )
            logging.debug(f"Pull output: {pull_output}")
        except git.GitCommandError as pull_error:
            if "couldn't find remote ref" in str(pull_error):
                logging.info(
                    f"Remote branch {branch_name} doesn't exist. Skipping pull."
                )
            else:
                raise

        push_output = repo.git.push("origin", branch_name, with_extended_output=True)
        logging.debug(f"Push output: {push_output}")
    except git.GitCommandError as e:
        logging.error(f"Git command error: {e.command}, {e.status}, {e.stderr}")
        raise Exception(f"Failed to push branch {branch_name}: {str(e)}")
    except Exception as e:
        logging.error(f"Unexpected error in _push_branch: {str(e)}")
        raise Exception(f"Failed to push branch {branch_name}: {str(e)}")


def _create_pull_request(pr_title, pr_body, branch_name):
    try:
        g = Github(GITHUB_TOKEN)

        # Parse the REPO_URL to get the owner and repo name
        from urllib.parse import urlparse

        parsed_url = urlparse(REPO_URL)
        if parsed_url.scheme == "ssh" or parsed_url.scheme == "":
            # Handle SSH URL (**************:owner/repo.git)
            path_parts = parsed_url.path.split(":")[-1].split("/")
        else:
            # Handle HTTPS URL
            path_parts = parsed_url.path.strip("/").split("/")

        owner, repo = path_parts[0], path_parts[1].replace(".git", "")

        repo_name = f"{owner}/{repo}"
        github_repo = g.get_repo(repo_name)
        pull_request = github_repo.create_pull(
            title=pr_title, body=pr_body, head=branch_name, base="main"
        )

        # Add approvers to the pull request
        if pr_approvers:
            pull_request.add_to_assignees(*pr_approvers)

        logging.info(f"Successfully created pull request: {pull_request.html_url}")

        return pull_request.html_url
    except github.GithubException as e:
        logging.error(f"GitHub API error: {e.status}, {e.data}")
        raise Exception(
            f"Failed to create pull request: {e.data.get('message', str(e))}"
        )
    except Exception as e:
        logging.error(f"Unexpected error in _create_pull_request: {str(e)}")
        raise Exception(f"Failed to create pull request: {str(e)}")
    return None


def _add_tenant_using_tenants_util(tenant_name, namespace, domain):
    # Save the current working directory
    original_cwd = os.getcwd()

    try:
        # Change to the root directory of the project
        os.chdir(os.environ["BUILD_WORKSPACE_DIRECTORY"])

        # Call tenants_util:add_tenant
        import argparse

        from deploy.tenants.tenants_util import add_tenant

        args = argparse.Namespace(name=tenant_name, namespace=namespace, domain=domain)
        add_tenant(args)

    finally:
        # Change back to the original directory
        os.chdir(original_cwd)


def _get_additions_from_commit(repo, commit):
    diff = repo.git.diff(
        f"{commit.parents[0].hexsha}..{commit.hexsha}",
        "--",
        "deploy/tenants/tenants.jsonnet",
    )
    # Filter - keep lines that begin with "+" but not "+++" and remove "+"
    additions = [
        line[1:]
        for line in diff.splitlines()
        if line.startswith("+") and not line.startswith("+++")
    ]
    return additions


def _add_lines_before_marker(lines, diff):
    marker = "  // ADD PROD TENANT HERE"
    marker_line = next((line for line in lines if line.startswith(marker)), None)
    if marker_line:
        marker_line = lines.index(marker_line)
        # Splice array diff into lines at marker_line
        lines[marker_line:marker_line] = [line + "\n" for line in diff]
    else:
        logging.warning("Marker not found")
    return lines


def _apply_diff_to_tenants_jsonnet(diff):
    # Apply diff to tenants.jsonnet
    with open(os.path.join(root_dir, "deploy/tenants/tenants.jsonnet"), "r") as f:
        lines = f.readlines()
    lines = _add_lines_before_marker(lines, diff)
    with open(os.path.join(root_dir, "deploy/tenants/tenants.jsonnet"), "w") as f:
        f.writelines(lines)


def add_tenant_and_create_pr(tenant_name, namespace, domain=None):
    launchbot_main = os.environ.get("LAUNCHBOT_MAIN_BRANCH", "launchbot-main")
    repo = _get_repo()

    # Ensure desired tenant/domain isn't already in launchbot-main
    _checkout_branch(repo, launchbot_main)
    exists, reason = _tenant_exists(tenant_name, domain)
    if exists:
        return False, reason

    # Create new branch off main
    _checkout_branch(repo, "main")
    branch_name = f"launchbot-add-tenant-{namespace}-{tenant_name}"
    _update_main_branch(repo)
    _create_branch(repo, branch_name)

    # Add tenant to new branch and push
    _add_tenant_using_tenants_util(tenant_name, namespace, domain)
    commit = _commit_changes(
        repo, "deploy/tenants/tenants.jsonnet", f"Add tenant: {tenant_name}"
    )
    added_lines = _get_additions_from_commit(repo, commit)
    _push_branch(repo, branch_name)

    # Create PR
    pr_title = f"Add tenant: {tenant_name}"
    pr_body = f"Add new tenant:\nName: {tenant_name}\nNamespace: {namespace}\nDomain: {domain}"
    pr = _create_pull_request(pr_title, pr_body, branch_name)

    # Apply changes from new branch to launchbot-main.
    # Can't use cherry-pick because git's merge strategies can't handle two additions at the same place.
    _checkout_branch(repo, launchbot_main)
    _apply_diff_to_tenants_jsonnet(added_lines)
    _commit_changes(
        repo, "deploy/tenants/tenants.jsonnet", f"Add tenant: {tenant_name}"
    )
    _push_branch(repo, launchbot_main)

    return True, pr
