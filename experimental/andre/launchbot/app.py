import logging
import os
import re

from flask import Flask, jsonify, render_template_string, request
from lib.git_utils import add_tenant_and_create_pr, prepare_sandbox

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    filename="app.log",  # This will log to a file instead of console
    filemode="w",
)

app = Flask(__name__)


def _validate_tenant_name(tenant_name):
    if len(tenant_name) > 16 or len(tenant_name) < 1:
        return False
    return tenant_name[0].isalpha() and all(
        c.islower() or c.isdigit() or c == "-" for c in tenant_name
    )


def _validate_domain(domain):
    if not domain:
        return False
    pattern = (
        r"^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$"
    )
    return re.match(pattern, domain, re.IGNORECASE) is not None


@app.route("/", methods=["GET"])
def index():
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Add Tenant</title>
    </head>
    <body>
        <h1>Add Tenant</h1>
        <form action="/add_tenant" method="POST">
            <label for="tenant_name">Tenant Name:</label>
            <input type="text" id="tenant_name" name="tenant_name" required><br><br>

            <label for="namespace">Namespace:</label>
            <input type="text" id="namespace" name="namespace" required><br><br>

            <label for="domain">Domain:</label>
            <input type="text" id="domain" name="domain" required><br><br>

            <input type="submit" value="Add Tenant">
        </form>
    </body>
    </html>
    """
    return render_template_string(html)


@app.route("/add_tenant", methods=["POST"])
def add_tenant_endpoint():
    data = request.form if request.form else request.json
    access_token = data.get("access_token")
    tenant_name = data.get("tenant_name")
    namespace = data.get("namespace")
    domain = data.get("domain")

    if access_token != os.environ.get("ACCESS_TOKEN"):
        logging.error(f"Invalid access token {access_token}")
        return jsonify({"error": "Invalid access token"}), 400

    if not _validate_tenant_name(tenant_name):
        logging.error(f"Tenant name {tenant_name} is not valid")
        return jsonify({"error": "Tenant name is not valid"}), 400

    if not _validate_domain(domain):
        logging.error(f"Domain {domain} is not valid")
        return jsonify({"error": "Domain is not valid"}), 400

    logging.info(
        f"Adding tenant {tenant_name} in namespace {namespace} with domain {domain}"
    )

    if not all([tenant_name, namespace]):
        logging.error("Missing required parameters")
        return jsonify({"error": "Missing required parameters"}), 400

    result, message = add_tenant_and_create_pr(tenant_name, namespace, domain)
    if result is False:
        logging.error(f"Failed to add tenant {tenant_name}: {message}")
        return jsonify({"message": message}), 400

    logging.info(f"Tenant {tenant_name} added and PR created successfully: {message}")
    return jsonify(
        {"message": "Tenant added and PR created successfully", "pr_url": message}
    ), 200


if __name__ == "__main__":
    prepare_sandbox()
    app.run(debug=True, host="0.0.0.0", port=5000)
