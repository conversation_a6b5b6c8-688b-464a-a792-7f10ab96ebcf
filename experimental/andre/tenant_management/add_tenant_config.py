#!/usr/bin/env python

import argparse
import hashlib
import os
import uuid

# Define constants for file names
basedir = os.path.dirname(os.path.abspath(__file__))
TENANTS_FILE = os.path.join(basedir, "../../../deploy/tenants/tenants.jsonnet")
KUBECFG_TEMPLATE = os.path.join(
    basedir, "../../../services/api_proxy/server/_kubecfg/{}.jsonnet"
)
NS_CONFIGS_TEMPLATE = os.path.join(
    basedir, "../../../deploy/tenants/namespace_configs/{}.jsonnet"
)


def get_tenants_string(tenant_name, tenant_url, region_str):
    """
    Generate a JSON string for a tenant configuration.  This needs to get added to the tenants.jsonnet file.

    Args:
    tenant_name (str): The name of the tenant.
    tenant_url (str): The URL of the tenant.
    region_str (str): The region string for the tenant's cloud environment.

    Returns:
    str: A JSON string representing the tenant configuration.
    """
    return f"""  {{
    namespace: '{tenant_name}',
    env: 'PROD',
    cloud: '{region_str}',
    name: '{tenant_name}',
    domain: '{tenant_url}',
  }},"""


def _read_tenants_file():
    """
    Read the tenants.jsonnet file and return the contents as an array of lines.
    """
    with open(TENANTS_FILE, "r") as f:
        return f.readlines()


def _write_tenants_file(lines):
    """
    Write the tenants.jsonnet file with the provided lines.
    """
    with open(TENANTS_FILE, "w") as f:
        f.writelines(lines)


def _find_insertion_line(lines, tenant_name):
    """
    Find the line in the tenants.jsonnet file where the new tenant configuration should be inserted.

    Returns:
    int: The line number where the new tenant configuration should be inserted, or None if the tenant already exists.
    """
    cursor = 0  # Where we need to insert
    nested = 0  # How many nested blocks we are in
    state = "init"
    for i, line in enumerate(lines):
        if state == "init":
            # if line starts with "local prodTenants", move to state "search"
            if line.startswith("local prodTenants"):
                state = "search"
                cursor = i + 1  # In case there are no prodTenants already.
            continue
        elif state == "search":
            # if line starts with "{" with optional whitespace before, move to state "insert"
            if line.lstrip().startswith("{"):
                if nested == 0:
                    cursor = i
                nested += 1
            elif line.lstrip().startswith("}"):
                nested -= 1
            elif line.lstrip().startswith("namespace:"):
                ns = line.split(":")[1].strip().strip("',")
                if ns == tenant_name:
                    print(f"Tenant {tenant_name} already exists!")
                    return None
                elif ns > tenant_name:
                    return cursor
            elif line.lstrip().startswith("]") and nested == 0:
                cursor = i
                return cursor

    return None


def output_tenants_string(tenant_name, tenants_str):
    """
    Output a JSON string for a tenant configuration.  This needs to get added to the tenants.jsonnet file.

    Args:
    tenant_name (str): The name of the tenant.
    tenants_str (str): The JSON string representing the tenant configuration.
    """
    # print("##### Add this to tenants.jsonnet: #####")
    # print(tenants_str)
    # print("########################################")
    lines = _read_tenants_file()
    cursor = _find_insertion_line(lines, tenant_name)
    # print(f"Inserting at line {cursor}")
    if cursor is None:
        return
    snippet_lines = tenants_str.split("\n")
    lines[cursor:cursor] = [line + "\n" for line in snippet_lines]
    _write_tenants_file(lines)


def _extend_api_users(tenant_name, num_api_keys, api_users):
    """
    Create a list of user labels, up to num_api_keys, using any labels provided and generating the rest.
    """
    if num_api_keys > len(api_users):
        api_users.extend(
            f"{tenant_name}{i+1}" for i in range(len(api_users), num_api_keys)
        )


def _generate_token():
    """
    Generate a random token.
    """
    return str(uuid.uuid4()).upper()


def _get_sha256(token):
    """
    Get the SHA256 hash of a token.
    """
    return hashlib.sha256(token.encode()).hexdigest()


def get_kubecfg_string(keys):
    """
    Generate a JSON string for a kubecfg configuration.  This needs to get added to
        services/api_proxy/server/_kubecfg/<tenant_name>.jsonnet
    The implementation is pretty hacky - we should really use a jsonnet library, but it isn't a
    standard library.

    Args:
    keys (dict): A dictionary of user:key pairs.

    Returns:
    str: A JSON string representing the kubecfg configuration.
    """
    ret = ""
    ret += "[\n"
    for user, key in keys.items():
        ret += f"""  {{
    user_id: '{user}',
    token_sha256: '{key}',  // pragma: allowlist secret
  }},\n"""

    ret += "]\n"
    return ret


def output_kubecfg_string(tenant_name, kubecfg_str):
    """
    Output a JSON string for a kubecfg configuration to
        services/api_proxy/server/_kubecfg/<tenant_name>.jsonnet

    Args:
    tenant_name (str): The name of the tenant.
    kubecfg_str (str): The JSON string representing the kubecfg configuration.
    """
    with open(KUBECFG_TEMPLATE.format(tenant_name), "w") as f:
        f.write(kubecfg_str)


def get_ns_configs_string(tenant_name):
    """
    Generate a JSON string for a namespace configuration.  This needs to get added to
        deploy/tenants/namespace_configs/<tenant_name>.jsonnet

    Args:
    tenant_name (str): The name of the tenant.

    Returns:
    str: A JSON string representing the namespace configuration.
    """
    return f"""local config = import 'deploy/tenants/namespace_configs/prod-defaults.jsonnet';
local apiTokens = import 'services/api_proxy/server/_kubecfg/{tenant_name}.jsonnet';

config {{
  api_tokens:: apiTokens,
  flags: config.flags,
}}
"""


def output_ns_configs_string(tenant_name, ns_configs_str):
    """
    Output a JSON string for a namespace configuration to
        deploy/tenants/namespace_configs/<tenant_name>.jsonnet

    Args:
    tenant_name (str): The name of the tenant.
    ns_configs_str (str): The JSON string representing the namespace configuration.
    """
    with open(NS_CONFIGS_TEMPLATE.format(tenant_name), "w") as f:
        f.write(ns_configs_str)


def get_token_string(tokens):
    """
    Generate a list of users and the assigned authentication token to be provided to each user.

    Args:
    tokens (dict): A dictionary of user:token pairs.

    Returns:
    str: A string of tokens and their labels.
    """
    return "\n".join(f"{token}, {user}" for user, token in tokens.items())


def output_token_string(tenant_name, token_str):
    """
    Output a list of users and the assigned authentication token to be provided to each user.

    Args:
    tenant_name (str): The name of the tenant.
    token_str (str): A string of tokens and their labels.
    """
    print("##### Share this token with users: #####")
    print(token_str)
    print("########################################")


def add_tenant(tenant_name, tenant_url, region_str, api_users):
    tenants_str = get_tenants_string(tenant_name, tenant_url, region_str)
    output_tenants_string(tenant_name, tenants_str)

    if len(api_users) > 0:
        # Generate tokens and keys.
        tokens = {}
        keys = {}
        for user in api_users:
            tokens[user] = _generate_token()
            keys[user] = _get_sha256(tokens[user])

        kubecfg_str = get_kubecfg_string(keys)
        output_kubecfg_string(tenant_name, kubecfg_str)

        token_str = get_token_string(tokens)
        output_token_string(tenant_name, token_str)

        ns_configs_str = get_ns_configs_string(tenant_name)
        output_ns_configs_string(tenant_name, ns_configs_str)


if __name__ == "__main__":
    region_map = {
        "us": "GCP_US_CENTRAL1_PROD",
        "eu": "GCP_EU_WEST4_PROD",
    }

    parser = argparse.ArgumentParser()
    parser.add_argument("tenant_name")
    parser.add_argument("tenant_url")
    parser.add_argument("-r", "--region", default="us", choices=["us", "eu"])
    parser.add_argument(
        "-n",
        "--num_api_keys",
        type=int,
        help="Number of API keys to generate.",
        default=0,
    )
    parser.add_argument(
        "-u",
        "--users",
        nargs="+",
        default=[],
        help="List of users to generate API keys for.  These are just labels.",
    )

    args = parser.parse_args()

    tenant_name = args.tenant_name
    tenant_url = args.tenant_url
    region = args.region
    num_api_keys = args.num_api_keys
    api_users = args.users

    # Ensure tenant_name less than 16 characters.
    assert len(tenant_name) <= 16, f"Tenant name {tenant_name} is too long."
    # Ensure tenant_name starts with a letter.
    assert tenant_name[
        0
    ].isalpha(), f"Tenant name {tenant_name} must start with a letter."
    region_str = region_map.get(region)

    # Assign a human readable label for each token/key pair.  If no label is specified, use tenant_name### with unique ###s.
    _extend_api_users(tenant_name, num_api_keys, api_users)

    add_tenant(tenant_name, tenant_url, region_str, api_users)
