<!-- add_customer.html -->

<!DOCTYPE html>
<html>
  <head>
    <title>Add New Customer</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f9f9f9;
      }
      form {
        max-width: 400px;
        margin: 40px auto;
        padding: 20px;
        background-color: #fff;
        border: 1px solid #ddd;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      }
      label {
        display: block;
        margin-bottom: 10px;
      }
      input[type="text"], input[type="number"] {
        width: 100%;
        height: 40px;
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ccc;
      }
      button[type="submit"] {
        background-color: #337ab7;
        color: #fff;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
      button[type="submit"]:hover {
        background-color: #23527c;
      }
    </style>
  </head>
  <body>
    <h1>Add New Customer</h1>
    <form action="/customer" method="post">
      <label for="customer-name">Customer Name:</label>
      <input type="text" id="customer-name" name="customer_name" required>

      <label for="cost-per-credit">Cost per Credit:</label>
      <input type="text" id="cost-per-credit" name="cost_per_credit" pattern="[0-9]+(\.[0-9]*)?" required>

      <label for="initial-credits">Initial Number of Credits:</label>
      <input type="number" id="initial-credits" name="initial_credits" required>

      <button type="submit">Add Customer</button>
    </form>
  </body>
</html>
