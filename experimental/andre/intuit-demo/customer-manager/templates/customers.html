<!-- customers.html -->

<!DOCTYPE html>
<html>
  <head>
    <title>{{ title }}</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        background-color: #f9f9f9;
      }
      table {
        border-collapse: collapse;
        width: 100%;
      }
      th, td {
        border: 1px solid #ddd;
        padding: 10px;
        text-align: left;
      }
      th {
        background-color: #f0f0f0;
      }
      tr:nth-child(even) {
        background-color: #f2f2f2;
      }
      h1 {
        color: #337ab7;
      }
      .add-customer-btn {
        background-color: #337ab7;
        color: #fff;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        text-decoration: none;
      }
      .add-customer-btn:hover {
        background-color: #23527c;
      }
      /* Consume button */
      .consume-btn {
        background-color: #ff9900; /* orange */
        color: #ffffff;
        border: none;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
      }
      .consume-btn:hover {
        background-color: #ff6600; /* darker orange */
      }
      /* Purchase button */
      .purchase-btn {
        background-color: #4CAF50; /* green */
        color: #ffffff;
        border: none;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
      }
      .purchase-btn:hover {
        background-color: #3e8e41; /* darker green */
      }
      .status-box {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
      }
      .error {
        background-color: #ffe6e6;
        border-color: #ff9999;
      }
      .success {
        background-color: #dff0df;
        border-color: #33cc33;
      }
    </style>
  </head>
  <body>
    <h1>{{ heading }}</h1>
    {% if status_class %}
    <div class="status-box {{ status_class }}">{{ status }}</div>
    <br>
    {% endif %}
    <p><a href="/customer/form_add" class="add-customer-btn">Add New Customer</a></p>
    <table>
      <thead>
        <tr>
          <th>Name</th>
          <th>Credits</th>
          <th>Buy Link</th>
          <th>Consume Link</th>
        </tr>
      </thead>
      <tbody>
        {% for customer in customers %}
        <tr>
          <td>{{ customer.name }}</td>
          <td>{{ customer.credits }}</td>
          <td>  <p><a href="/customer/form_purchase?customer_id={{ customer.id }}&customer_name={{ customer.name }}" class="purchase-btn">Purchase Credits</a></p> </td>
          <td><form action="{{ url_for('consume_credit') }}" method="post">
            <input type="hidden" name="customer_id" value="{{ customer.id }}">
            <input type="hidden" name="customer_name" value="{{ customer.name }}">
            <button type="submit" class="consume-btn">Consume Credit</button>
          </form></td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    <script>
      window.history.replaceState({}, document.title, "{{ url }}");
    </script>
  </body>
</html>
