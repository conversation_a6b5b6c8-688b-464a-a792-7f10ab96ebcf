# Basic Quickbooks API Example

This is a basic example of how to use the Quickbooks API,
implemented using flask.  Flask endpoints:

* `/customer` GET - returns a list of customers and information about each
* `/customer` POST - creates a new customer, along with the SKU for dev-days for that customer
* `/customer/consume` POST - consumes a credit from the customer's credit balance
* `/customer/purchase` POST - adds credits to the customer's credit balance
* `/customer/form_add` GET - renders a form to add a new customer
* `/customer/form_purchase` GET - renders a form to purchase credits for a customer

Only companies with the customer type "Augment-user" are considered customers.

## Prerequisites for using Quickbooks account (Option 1)

1. Create a [Quickbooks developer account](https://developer.intuit.com/app/developer/myapps)
1. Go to the [application dashboard](https://developer.intuit.com/app/developer/dashboard), click into the app, and choose "Keys & credentials" under Development Settings on the left side.
    1. Get the client ID (`client_id`) and client secret (`client_secret`) and update [`config.py`].
    1. Add the redirect URI (`http://localhost:8000/auth_callback`) to the list of redirect URIs.
1. Go to the [sandbox](https://developer.intuit.com/app/developer/sandbox).
    1. Get the company ID (`realm_id`) and update [`config.py`].
    1. Click into the sandbox company.  Under "Customers & leads", click "Customers".  Click "Customer types" and add a "New customer type" named "Augment-user".

Nice to do:
* Delete (make inactive) existing Customers (for ones that fail, need to go into customer and delete open charges).
* Delete (make inactive) existing Products and Services.

## Prerequisites for using Quickbooks account (Option 2)

Use Andre's login (andre@) with password at the bottom of this doc.

## First time opening the project

1. Clone the repo (`git clone https://github.com/a2chang/augment.git <your_project_dir>`)

1. Open the project in vscode

1. Add to vscode settings.json:
```json
    "python.analysis.extraPaths": [
        "./experimental/andre/intuit-demo/oauth-pythonclient",
    ],
```
1. Create a virtual environment (command palette, python : create virtual environment, venv).
    Select default python (mine was 3.9.6).
    Select dependency `experimental/andre/intuit-demo/quickbooks-python-sdk/requirements.txt` to install.
    Not sure why it complains about unable to install dependencies (still need to investigate).

1. Activate the virtual environment (command palette, python : select interpreter, choose the virtual environment).
    Also `source .venv/bin/activate`

1. Get the intuit repos for reference.
   `cd experimental/andre/intuit-demo/`
   `./clone-repos.sh`

1. Apply patch to delete code for the demo.
   `patch < demo.patch`

1. Install `generateDS` and generate model files.
   `pip3 install generateDS`
   `cd quickbooks-python-sdk/`
   `python3 util/process_xjb_xsd.py`

1. Install the requirements (`pip3 install -r requirements.txt`) if the file wasn't selected when creating the venv.


## Running the application

1. Run `python app.py`
1. Open `http://localhost:8000` in a browser.
1. A link will be presented in the console output.  Click on the link to authorise your Quickbooks account.

## Endpoint workflows

1. `/customer` GET - returns a list of customers and information about each
  Uses the `/query` endpoint to retrieve a list of companies.  The list is filtered to only include companies with the customer type "Augment-user".

1. `/customer` POST - creates a new customer, along with the SKU for dev-days for that customer
  Uses the `/customer` endpoint to create a new customer.
  Uses the `/item` endpoint to create a new SKU for dev-days for that customer.  The SKU is named after the customer name.

1. `/customer/consume` POST - consumes a credit from the customer's credit balance
  Uses a receipt to consume a credit from the customer's credit balance.

1. `/customer/purchase` POST - adds credits to the customer's credit balance
  Create a purchaseorder.  Create a bill.  There is a bug where creating the bill with a linked PO doesn't work.
  Workaround is to create without, then do a full update of the bill record with the LinkedTxn field.

  API bug - https://help.developer.intuit.com/s/question/0D54R00009ch166SAA/how-can-i-link-a-bill-to-a-purchaseorder-using-the-qbo-api

## Misc

Password to Andre's intuit account if you don't want to make your own: 1Augment!

If vscode complains about intuitlib not found, add the following to your settings.json:
```json
    "python.analysis.extraPaths": [
        "research/gpt-neox",
        "./experimental/andre/intuit-demo/oauth-pythonclient"
    ],
```
