import os
import subprocess

basedir = os.path.dirname(os.path.abspath(__file__))
# Set the input directory containing XJB and XSD files
input_dir = os.path.join(basedir, "../../QuickBooks-V3-Java-SDK/ipp-v3-java-data/src/main/xsd/")
# Set the output directory for generated Python classes
output_dir = os.path.join(basedir, "../../quickbooks-python-sdk/models/")


# Process each XSD file in the input directory
for filename in os.listdir(input_dir):
    if filename.endswith(".xsd"):
        xsd_file = os.path.join(input_dir, filename)
        xjb_file = xsd_file.replace(".xsd", ".xjb")
        output_file = os.path.join(output_dir, filename.replace(".xsd", ".py"))
        if not os.path.exists(xjb_file):
            # Process XSD file with default binding
            xjb_file = os.path.join(input_dir, "binding.xjb")
        cmd = f"generateDS -o {output_file} {xsd_file}"
        subprocess.run(cmd, shell=True)
