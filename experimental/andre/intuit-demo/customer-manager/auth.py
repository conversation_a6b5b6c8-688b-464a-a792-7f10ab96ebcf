import os
import json
import sys
import threading
from datetime import datetime, timed<PERSON>ta

from config import (
    client_id,
    client_secret,
    environment,
    realm_id,
    redirect_uri,
)

TOKEN_FILE = "quickbooks_tokens.json"

# Get the base directory of the current file
basedir = os.path.dirname(os.path.abspath(__file__))

# Add the parent directory to the system path
sys.path.insert(0, "../oauth-pythonclient")

# Import the AuthClient and Scopes from the intuitlib.client module
from intuitlib.client import AuthClient  # noqa: E402
from intuitlib.enums import Scopes  # noqa: E402

# Event to signal when the authorisation code is received
callback_event = threading.Event()

# Authorisation code received from the OAuth2.0 flow
code = None

# Instantiate AuthClient with client credentials and environment details
auth_client = AuthClient(client_id, client_secret, redirect_uri, environment)


def set_auth_code(new_code):
    """
    Set the authorisation code received from the OAuth2.0 flow.

    Args:
    new_code: The new authorisation code to be set.
    """
    global code
    code = new_code


def load_tokens():
    """
    Load the tokens from the token file.

    Returns:
    A dictionary containing the tokens or None if the file does not exist.
    """
    token_file = os.path.join(basedir, TOKEN_FILE)
    if os.path.exists(token_file):
        with open(token_file, "r") as f:
            return json.load(f)
    return None


def save_tokens(access_token, refresh_token, expires_in=None):
    """
    Save the tokens to the token file.

    Args:
    access_token: The access token to be saved.
    refresh_token: The refresh token to be saved.
    expires_in: The number of seconds the access token is valid for.
    """
    token_file = os.path.join(basedir, TOKEN_FILE)
    with open(token_file, "w") as f:
        json.dump(
            {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_at": (
                    datetime.now() + timedelta(seconds=expires_in)
                ).isoformat(),
            },
            f,
        )


def authenticate():
    """
    Authenticate the user with the QuickBooks API using OAuth2.0.

    This function attempts to refresh the access token if it exists.
    If the refresh fails, it initiates the OAuth2.0 authorisation flow.
    """

    tokens = load_tokens()
    if tokens:
        expires_at = datetime.fromisoformat(tokens["expires_at"])
        if datetime.now() < expires_at:
            auth_client.access_token = tokens["access_token"]
            auth_client.refresh_token = tokens["refresh_token"]
            return auth_client.access_token

    try:
        # Refresh access token if it exists
        auth_client.refresh(refresh_token=tokens["refresh_token"])
        save_tokens(
            auth_client.access_token, auth_client.refresh_token, auth_client.expires_in
        )
        return auth_client.access_token
    except Exception:
        pass

    # Get the authorisation URL for the OAuth2.0 flow
    url = auth_client.get_authorization_url([Scopes.ACCOUNTING])

    # Print the authorisation URL to the console for the user to visit
    print(
        f"🚀 Please visit {url} to authorise your Quickbooks account (CTRL/CMD + Click to Open)."
    )

    # Wait for the authorisation code to be received
    callback_event.wait()

    # Get the bearer token using the authorisation code
    auth_client.get_bearer_token(code, realm_id=realm_id)
    save_tokens(
        auth_client.access_token, auth_client.refresh_token, auth_client.expires_in
    )
    return auth_client.access_token


def get_auth_header():
    """
    Get the authorisation header for making API requests.

    Returns:
    A string representing the authorisation header with the access token.
    """
    return f"Bearer {auth_client.access_token}"
