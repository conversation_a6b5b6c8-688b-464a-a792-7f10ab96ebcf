# Feedback Automation System Design

## Overview

This document outlines the design for an automated feedback triage system that uses AI agents to process, label, and organise user feedback tickets from the CX (Customer Experience) team in Linear. The system aims to reduce manual triage overhead while maintaining quality and ensuring important issues surface quickly.

## Goals

- **Assist with labelling**: Provide AI-suggested labels and categorisation for human review
- **Support triage decisions**: Help humans identify patterns, duplicates, and priorities
- **Reduce manual overhead**: Automate time-consuming analysis while keeping humans in control
- **Enhance decision quality**: Provide additional context and insights for human reviewers

## System Architecture

### Core Components

#### 1. Linear Integration Layer
- **Purpose**: Leverage built-in Augment agent Linear integration
- **Built-in Capabilities**:
  - CRUD operations on tickets via Linear tool
  - State transitions and field updates
  - Natural language queries for ticket management
  - Automatic rate limiting and error handling
- **Implementation Notes**:
  - Uses existing Augment Linear tool integration
  - No custom API wrapper needed
  - Leverages agent's natural language interface

#### 2. AI Labelling Assistant
- **Purpose**: Transform raw customer feedback into well-structured, actionable tickets
- **Core Responsibilities**:
  - **Issue Identification**: Parse and understand the actual issue(s) described
  - **Multi-Issue Handling**: Create sub-tickets for independent issues (even if related)
  - **Title Optimisation**: Ensure titles are descriptive and concise (only when necessary)
  - **Description Enhancement**: Add clear write-ups at top while preserving raw user data
  - **Comprehensive Labelling**: Apply appropriate categories, product areas, and priority
  - **Status Management**: Move tickets to labelled status when complete
- **Classification Dimensions**: (defined in `labels/` directory)
  - **Category**: Bug, Feature, Improvement, Paper-cut, Quality, UX, Vulnerability, Question, Customer-love, Support
  - **Product Area**: Auth, Code-search, Model-quality, Completion, Next-edit, Instruction, Chat, Smart-paste, Agent-local, Agent-remote, Agent-CLI, Tasklists, VSCode, IntelliJ, Vim, Slack, External-context, Docsets, Website
  - **Priority**: no priority, low, medium, high (based on impact and users affected)
- **Context Sources**: Uses all available tools and data
  - Historical Linear tickets for reference and patterns
  - Codebase context when relevant to issues
  - PRs and commits for understanding recent changes
  - Documentation and other relevant sources
- **Edge Case Handling**:
  - **Vague Tickets**: Best-effort processing + clarifying questions + `needs-clarification` flag
  - **Multi-Issue Tickets**: Create sub-tickets for each independent issue
- **Output**: Well-structured, labelled tickets moved to labelled status

#### 3. Support Agent
- **Purpose**: Provide high-confidence technical solutions for CX (customer experience) support-labelled tickets
- **Scope**: Only processes CX tickets - does not handle AU (engineering) tickets
- **Core Responsibilities**:
  - **Deep Research**: Use all available tools to thoroughly understand customer issues
  - **Version Analysis**: Determine user's IDE and extension version, check if issue is fixed in newer releases
  - **Solution Development**: Provide concrete solutions, workarounds, or debugging steps
  - **Feature Flag Awareness**: Consider tenant/namespace-specific configurations
  - **Conservative Approach**: Only respond when having high confidence (80%+) in the solution
- **Research Sources**:
  - Historical CX tickets with similar issues and resolutions
  - Codebase analysis for understanding functionality and potential issues
  - Recent PRs and commits for fixes and changes
  - Documentation and troubleshooting guides
- **Response Standards**:
  - Clear step-by-step instructions with expected outcomes
  - Version upgrade guidance when issues are fixed in newer releases
  - References to similar resolved tickets and relevant documentation
  - Professional format with structured sections (Analysis, Solution, Expected Result)
- **Quality Assurance**: Skip uncertain cases rather than provide speculative solutions
- **Output**: Direct technical support responses added as Linear ticket comments

#### 4. Triage Assistant
- **Purpose**: Reduce CX ticket volume and provide clear prioritisation for human review
- **Scope**: Processes CX (customer experience) tickets but analyzes both CX and AU (engineering) tickets for context, associations, duplications, and relations
- **Core Responsibilities**:
  - **Volume Reduction**: Close duplicate CX tickets while preserving information in master tickets
  - **Historical Analysis**: Research all Linear tickets (both CX and AU teams, open/closed) to make informed duplicate/batch decisions
  - **Priority Assignment**: Automatically set Linear priority fields with tier-based system
  - **Batch Identification**: Group related tickets for efficient resolution
  - **Urgent Flagging**: Label urgent tickets for immediate human attention
  - **Cross-team Context**: Consider AU tickets that may resolve customer issues or provide engineering context
- **Processing Approach**:
  - **Daily runs** processing batches of 25 CX tickets (oldest first, FIFO)
  - **Multiple batches** with consolidated reporting when volume exceeds batch size
  - **Conservative duplicate detection** with clear decision criteria
  - **Status transitions**: labelled → Backlog (with "ai-triaged" label) for all processed CX tickets
  - **Cross-team analysis**: Research both CX and AU tickets for comprehensive context
- **Priority Tiers**:
  - **🔥 URGENT**: Critical/security issues, regressions (high priority + urgent label)
  - **⚡ HIGH**: Major features broken, business impact (high priority)
  - **📋 MEDIUM**: Minor features, workflow disruptions (medium priority)
  - **📝 LOW**: Cosmetic issues, nice-to-have features (low priority)
- **Decision Factors**: Impact severity, user frequency, business impact, resolution effort, recency, regressions
- **Output**: Comprehensive run summaries with volume reduction metrics and batching opportunities

#### 5. Monitoring and Reporting Layer
- **Purpose**: Track agent performance and provide visibility into automation
- **Responsibilities**:
  - Generate run summaries for each agent execution
  - Track processing metrics and success rates
  - Provide audit trails through ticket comments
  - Enable human review of urgent and complex cases

## Workflow Design

### Workflow Design

```
CX Team New Tickets → Labelling Agent → Support Agent → Triage Assistant → Human Review & Decision → Triaged & Assigned
```

**Process Flow**:
1. **New CX Tickets**: Tickets arrive in CX team queue (triage status)
2. **Labelling Agent**: Processes and labels all tickets (moves to labelled status)
3. **Support Agent**: Provides technical solutions for support-labelled tickets
4. **Triage Assistant**: Reduces volume through duplicate closure and prioritises remaining tickets (adds "ai-triaged" label and moves to Backlog status)
5. **Human Review**: CX team reviews urgent tickets and AI triage decisions in Backlog
6. **Final Assignment**: Humans route triaged tickets to appropriate teams

**AI Assistance Points**:
- **Ticket Processing**: Transform raw feedback into structured, actionable tickets
- **Issue Analysis**: Identify and separate independent issues within tickets
- **Content Enhancement**: Improve titles and descriptions while preserving original data
- **Comprehensive Labelling**: Apply categories, product areas, and additional helpful labels
- **Technical Support**: Provide high-confidence solutions for support-labelled tickets
- **Version Guidance**: Advise on upgrades and feature flag availability
- **Volume Reduction**: Close duplicate tickets and consolidate information into master tickets
- **Priority Assignment**: Automatically prioritise tickets using tier-based system with historical context
- **Batch Identification**: Group related tickets for efficient team resolution

**Processing Approach**:
- **Comprehensive Analysis**: Use all available tools (Linear history, codebase, PRs, docs)
- **Quality Enhancement**: Improve ticket structure and clarity
- **Context Preservation**: Always maintain original customer data
- **Multi-Issue Handling**: Create sub-tickets for independent issues

### AI Assistant Execution

**Labelling Assistant**:
- **Mode**: On-demand or scheduled
- **Trigger**: CX team tickets in triage status
- **Query**: "Process CX team tickets in triage status - analyse, enhance, and label appropriately"
- **Processing Logic**:
  - **Issue Analysis**: Identify and understand all issues described in the ticket
  - **Multi-issue Detection**: Create sub-tickets for independent issues (even if related)
  - **Title Enhancement**: Improve titles when necessary for clarity and issue identification
  - **Description Enhancement**: Add clear write-ups at top while preserving original customer data
  - **Context Research**: Use Linear history, codebase, PRs, and other sources for informed decisions
  - **Comprehensive Labelling**: Apply category, product area, and priority based on full analysis
  - **Support State Management**: Add "ai-support-pending" label for tickets categorized as "Support"
  - **Vague Handling**: Best-effort processing + clarifying questions + `needs-clarification` flag
- **Output**: Well-structured, enhanced tickets moved to labelled status
- **Change Tracking**:
  - **Per-ticket**: Standardised comment summarising all changes made
  - **Run summary**: Consolidated report of all tickets processed and actions taken

**Support Agent**:
- **Mode**: On-demand or scheduled after labelling
- **Trigger**: CX tickets with "ai-support-pending" label
- **Scope**: Only processes CX (customer experience) tickets
- **Processing Logic**:
  - **Version Detection**: Extract user's IDE and extension version from ticket details
  - **Issue Research**: Search CX Linear history, codebase, and documentation for similar issues
  - **Release Analysis**: Check if issue is fixed in newer versions or upcoming releases
  - **Feature Flag Check**: Consider tenant/namespace-specific configurations
  - **Solution Development**: Create high-confidence technical solutions with step-by-step guidance
  - **Conservative Approach**: Skip tickets when confidence is below 80%
  - **Label Management**: Remove "ai-support-pending", add "ai-support-responded" or "ai-support-skipped"
- **Response Format**: Structured technical support comments with analysis, solution, and references
- **Output**: Direct ticket responses for high-confidence cases, run summary of responses vs. skipped tickets

**Triage Assistant**:
- **Mode**: Daily scheduled runs after labelling and support agents
- **Trigger**: CX tickets in "labelled" status (processes oldest first, FIFO)
- **Scope**: Processes CX tickets but analyzes both CX and AU tickets for context
- **Processing Logic**:
  - **Batch Processing**: Handle 25 CX tickets per batch, continue until all labelled CX tickets processed
  - **Historical Research**: Query all Linear tickets (both CX and AU teams, open/closed) for each batch ticket
  - **Duplicate Detection**: Close duplicate CX tickets, enhance master tickets, preserve information
  - **Priority Assignment**: Apply tier-based system (🔥⚡📋📝) with automatic Linear field updates
  - **Batch Identification**: Group related tickets for efficient resolution
  - **Cross-team Analysis**: Consider AU tickets for engineering context and potential resolutions
  - **Status Management**: Add "ai-triaged" label and move processed CX tickets from "labelled" to "Backlog"
- **Decision Criteria**:
  - **Duplicate**: Same root cause, existing ticket active with repro steps
  - **Batch**: Related functionality, can be resolved together, existing ticket not stale
  - **Separate**: Different root cause, existing ticket stale (>30 days), or regression
- **Priority Factors**: Impact severity, user frequency, business impact, resolution effort, recency, regressions
- **Output**: Consolidated run summary covering all batches with volume reduction metrics and urgent ticket flagging

## Edge Case Workflows

### Vague Ticket Handling

**Process**:
1. AI identifies ticket lacks sufficient detail for confident processing
2. AI researches similar historical tickets and relevant context
3. AI enhances description with best-effort analysis and context
4. AI applies best-effort labels based on available information
5. AI generates specific clarifying questions for customer follow-up
6. AI sets `needs_clarification` flag and adds questions to `clarifying_questions` field
7. Ticket moves to labelled status (doesn't block workflow)

**Example Output**:
```
Enhanced Description:
Based on the description, this appears to be related to code completion functionality.
Similar issues have been reported with [specific context from historical tickets].

[Original customer message preserved below]
---
[Original customer data]

Labels: Category=Support, Product-Area=Completion, Priority=medium, Confidence=0.4
Clarifying Questions:
- Which IDE are you using (VSCode, IntelliJ, etc.)?
- What programming language were you working with?
- Can you provide the specific error message or behavior you observed?
```

### Multi-Issue Ticket Handling

**Process**:
1. AI identifies multiple independent issues in single ticket
2. AI researches each issue using historical tickets and relevant context
3. AI creates sub-tickets for each individual issue:
   - Descriptive, specific titles that clearly identify each issue
   - Enhanced descriptions with context while preserving original data
   - Appropriate labels based on comprehensive analysis
   - Link back to parent ticket
4. AI enhances original ticket description with summary and context
5. AI labels original ticket as `multi_issue_parent`
6. AI adds summary comment to original ticket listing all sub-tickets
7. Sub-tickets enter normal workflow independently

**Example Structure**:
```
Original Ticket: "Multiple issues with VSCode extension"
Enhanced Description:
This ticket contains multiple independent issues that have been separated for individual tracking:
1. Authentication issue with Google SSO (similar to recent reports)
2. Performance degradation in Python completion (may be related to recent model updates)
3. Extension stability issue on startup (new issue pattern)

[Original customer message preserved below]
---
[Original detailed customer feedback]

Sub-tickets created:
├── SUB-123: "Google SSO authentication fails in VSCode extension" [Bug, high, Auth]
├── SUB-124: "Python code completion response time degraded" [Quality, medium, Completion]
└── SUB-125: "VSCode extension crashes during startup initialization" [Bug, high, VSCode]
```

## Data Model

### Linear Integration

The system uses Linear's existing fields and functionality:

- **Labels**: Apply appropriate labels from the defined categories and product areas
- **Priority**: Use existing CX team priority field (no priority, low, medium, high)
- **Status**: Move tickets from triage → labelled → Backlog (with "ai-triaged" label)
- **Comments**: Add AI processing summary comments for change tracking
- **Relations**: Link parent tickets to sub-tickets using Linear's built-in relations
- **Description**: Enhance descriptions by adding context at the top while preserving original content
- **Title**: Improve titles when necessary for clarity and issue identification

### Change Tracking

**Per-Ticket Comments**:
Each processed ticket receives a standardised comment documenting changes:

```
🤖 AI Processing Summary:
- Enhanced title: "Login issue" → "Google SSO authentication fails in VSCode extension"
- Added context to description based on similar tickets CX-45, CX-67
- Applied labels: Bug, high priority, Auth product area
- Created 2 sub-tickets for independent issues (CX-125, CX-126)
- Added "ai-triaged" label and moved to Backlog status
```

**Run Summary Report**:
After each processing run, generate consolidated summary:

```
AI Processing Run Summary - 2024-01-15 14:30

Processed 5 tickets:

- CX-123 "Multiple extension issues"
  - created subtickets
    - CX-125 "Google SSO authentication fails in VSCode extension"
    - CX-126 "Python code completion response time degraded"
    - CX-127 "VSCode extension crashes during startup"
  - enhanced parent ticket description
  - moved to labelled status

- CX-124 "Code not working"
  - rewrote title: "Code not working" → "TypeScript autocomplete suggestions missing"
  - enhanced description with context from similar tickets
  - applied labels: Bug, medium priority, Completion
  - moved to labelled status

- CX-128 "Great new feature!"
  - applied labels: Customer-love, no priority, Chat
  - moved to labelled status

Total: 3 parent tickets processed, 3 sub-tickets created, 6 tickets labelled "ai-triaged" and moved to Backlog
```

### Duplicate Detection Data Structure

Duplicate detection is handled through Linear's built-in relations and labels:
- **Master Ticket**: Enhanced with consolidated information from duplicates
- **Duplicate Tickets**: Closed with "triage-bot-duplicate" label and linked to master
- **Relations**: Linear relations link duplicates to master tickets
- **Comments**: Closing comments explain consolidation reasoning

## Implementation Plan

### Phase 1: Foundation (Week 1-2)
- Configure Linear workspace access for agents
- Set up labels and workflow in Linear CX team (using built-in Linear tool)
- Test querying CX team tickets and filtering logic
- Build agent orchestration framework
- Validate ticket selection criteria

### Phase 2: Labelling Agent (Week 3-4)
- Implement AI labelling logic with multi-issue detection
- Create sub-ticket generation capability
- Add comprehensive labelling with category, product area, and priority
- Add monitoring and logging

### Phase 3: Support Agent (Week 5-6)
- Implement technical support response generation
- Add version analysis and upgrade recommendations
- Create conservative confidence thresholds
- Add label management for support workflow

### Phase 4: Triage Agent & Polish (Week 7-8)
- Implement duplicate detection and consolidation
- Add priority assignment with tier-based system
- Create batch processing for volume handling
- Build scheduling system and monitoring

## Quality Control

### Confidence Thresholds
- **Support Agent**: 80%+ confidence required for providing responses
- **Labelling Agent**: Best-effort processing with "needs-clarification" flag for vague tickets
- **Triage Agent**: Conservative duplicate detection with clear decision criteria

### Human Override System
- Ability to correct AI classifications
- Feedback loop to improve agent accuracy
- Manual duplicate detection override tools

### Monitoring & Alerting
- Agent execution success/failure rates
- Classification accuracy metrics
- Processing time and throughput
- Queue depth monitoring

## Technical Considerations

### Scalability
- Batch processing for volume handling (25 tickets per batch)
- Incremental processing to handle large backlogs
- Efficient historical ticket search using Linear API

### Reliability
- Idempotent operations using Linear tool
- Graceful failure handling via agent error recovery
- State recovery mechanisms
- Audit logging through agent execution logs

### Security
- Leverages Augment's built-in Linear authentication
- Rate limiting handled by Linear tool integration
- Data privacy considerations
- Access control for human reviewers

## Success Metrics

- **Processing Rate**: % of tickets successfully processed by each agent
- **Labelling Accuracy**: Human validation of AI classifications and labels
- **Support Response Rate**: % of support tickets receiving AI responses vs. skipped
- **Volume Reduction**: % reduction through duplicate detection and consolidation
- **Time to Triage**: Average time from ticket creation to Backlog status
- **Human Satisfaction**: Feedback from team using the system

## Future Enhancements

- **Predictive Prioritisation**: ML model to predict ticket priority
- **Auto-routing**: Automatically assign tickets to appropriate team members
- **Trend Analysis**: Identify emerging issues and patterns
- **Integration Expansion**: Support for other feedback channels (Slack, email)
- **Real-time Processing**: Webhook-driven immediate processing