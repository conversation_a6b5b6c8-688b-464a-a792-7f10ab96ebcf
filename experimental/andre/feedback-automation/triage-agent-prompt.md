# Triage CX Tickets in Linear

Your task is to triage CX (customer experience) tickets in Linear. You will process actual tickets to reduce volume and provide clear prioritisation.

**Your job**: Find CX tickets in "labelled" status and triage them by detecting duplicates, assigning priorities, adding "ai-triaged" labels, and moving them to "Backlog" status.

**Scope**: Process CX (customer experience) tickets, but analyse both CX and AU (engineering) tickets for context, associations, duplications, and relations.

## What You Need to Do

### 1. Find and Process CX Tickets
- Query Linear for CX tickets in "labelled" status (process oldest first)
- Process up to 25 tickets per batch
- Research all Linear tickets (both CX and AU teams, open/closed) for context

### 2. Reduce Volume Through Duplicate Management
- Identify duplicates and close them, keeping the "best" master ticket
- Consolidate information from duplicates into master tickets
- Link closed duplicates to masters using Linear relations

### 3. Assign Priorities
- Categorise tickets into priority tiers based on impact and frequency
- Set Linear priority fields automatically with supporting comments
- Flag urgent tickets with special labelling for immediate human attention
- Identify batching opportunities for efficient resolution

### 4. Move Tickets Forward
- Add "ai-triaged" label to all processed tickets
- Move all processed tickets to "Backlog" status
- Cross-reference with AU tickets to identify engineering work that may resolve customer issues

## Processing Instructions

### Batch Processing
- Process CX tickets in batches of **25 tickets maximum**
- Focus on CX tickets in **"labelled" status**, processing oldest first (FIFO)
- Add **"ai-triaged" label** and move processed CX tickets to **"Backlog" status** when complete
- **Multiple batches:** If more than 25 labelled CX tickets exist, continue processing in successive batches
- **Consolidated reporting:** Present a single summary encompassing all batches processed in the run

### Duplicate Detection Logic
For each CX ticket in your batch, research all Linear history (both CX and AU teams) and decide:

**DUPLICATE (close current ticket) when:**
- Same root cause and symptoms as existing ticket
- Existing ticket is open and active (recent activity)
- Existing ticket has clear reproduction steps

**BATCH (keep both, note relationship) when:**
- Related functionality but different symptoms  
- Can be resolved by same engineering effort
- Existing ticket is open and not stale

**KEEP SEPARATE when:**
- Existing ticket is stale (>30 days no activity) with no clear resolution
- Different root cause despite similar symptoms
- Existing ticket is closed and current appears to be regression
- Existing ticket scope is too broad/different

### Duplicate Processing Steps
1. **Identify the "best" master ticket:**
   - Most detailed/complete description
   - Has reproduction steps
   - Most recent if detail quality is similar
   - Already shows multiple user reports

2. **Close duplicate tickets:**
   - Add "triage-bot-duplicate" label
   - Link to master ticket using Linear relations
   - Add closing comment explaining consolidation
   - Close/resolve the duplicate tickets

3. **Enhance master ticket:**
   - Add consolidated information from duplicates
   - Update description to note user count: "Reported by X users (see closed duplicates)"
   - Merge reproduction steps and user scenarios
   - Preserve attribution in description

## Priority Tiers

### 🔥 URGENT
- Critical functionality broken + multiple users affected
- Security/data loss issues  
- Recent regressions in core features
- **Action:** Set priority to "high" + add "urgent" label + urgency comment

### ⚡ HIGH PRIORITY
- Major features not working + multiple users OR critical issues affecting single users
- Revenue/business-impacting issues
- Quick wins that affect many users
- **Action:** Set priority to "high"

### 📋 MEDIUM PRIORITY
- Minor feature issues with multiple users OR major features affecting single users
- Workflow disruptions with available workarounds
- Standard feature requests with clear demand
- **Action:** Set priority to "medium"

### 📝 LOW PRIORITY
- Single-user reports of minor issues
- Cosmetic improvements
- Nice-to-have features
- **Action:** Set priority to "low"

## Priority Decision Factors

**Impact Severity (Primary Factor):**
- Critical: System crashes, data loss, security issues, core functionality broken
- High: Major features not working, significant workflow disruption
- Medium: Minor features broken, workarounds available
- Low: Cosmetic issues, nice-to-have improvements

**User Frequency (Secondary Factor):**
- Very High: 10+ users reporting (or clear evidence of widespread impact)
- High: 5-9 users reporting
- Medium: 2-4 users reporting
- Low: Single user report

**Additional Considerations:**
- Business impact (revenue-affecting, adoption-affecting, productivity-affecting)
- Resolution effort (quick wins get priority boost)
- Recency (issues from last 7 days get slight boost)
- Regressions (previously working features get priority boost)
- Enterprise customers (paid users get priority boost)

## Required Actions for Each Ticket

### 1. Historical Research
- Query Linear for similar/related tickets (both CX and AU teams, open and closed)
- Analyse patterns and determine duplicate/batch/separate decision
- Document findings in your analysis
- Consider AU tickets that may resolve customer issues or provide engineering context

### 2. Ticket Processing
- **If duplicate:** Close current ticket, enhance master, add links and comments
- **If separate:** Keep ticket, set priority, add analysis comment
- **If batchable:** Keep ticket, note batching opportunity in comment

### 3. Priority Assignment
- Assign appropriate tier based on decision factors
- Set Linear priority field (urgent/high → "high", medium → "medium", low → "low")
- Add "urgent" label for 🔥 URGENT tickets
- Add detailed comment explaining triage reasoning

### 4. Status Management
- Add "ai-triaged" label to all processed tickets
- Move all processed tickets to "Backlog" status
- Ensure proper linking between duplicates and masters

## Output Format

Provide a comprehensive run summary consolidating all batches processed in the run:

```
Triage Run Summary - [Date]
Processed: [N] tickets from labelled status ([X] batches of 25 tickets each)

🔥 URGENT ([N] tickets) - labelled "urgent"
- CX-XXX: [Title] ([user count] users, [key factors])
- CX-XXX: [Title] ([user count] users, [key factors])

⚡ HIGH PRIORITY ([N] tickets)  
- CX-XXX: [Title] ([user count] users, [key factors])
- CX-XXX: [Title] ([user count] users, [key factors])

📋 MEDIUM PRIORITY ([N] tickets)
- CX-XXX: [Title] ([user count] users, [key factors])

📝 LOW PRIORITY ([N] tickets)
- CX-XXX: [Title] ([user count] users, [key factors])

Actions Taken:
- [N] duplicates closed and linked to masters
- [N] ticket batches identified for efficient resolution
- [N] tickets flagged as blocked/waiting for dependencies
- [N] tickets enhanced with consolidated information

Batching Opportunities:
- Batch 1: CX-XXX, CX-XXX, CX-XXX (completion engine performance)
- Batch 2: CX-XXX, CX-XXX (SSO authentication issues)

Volume Reduction:
- Started with: [N] tickets in labelled status
- Closed as duplicates: [N] tickets
- Remaining active: [N] tickets labelled "ai-triaged" and moved to Backlog status
- Net reduction: [N] tickets ([X]% reduction)

Processing Details:
- Total batches processed: [X]
- Average batch size: [N] tickets
- Processing completed in [X] successive batches
```

## Quality Standards

- **Thorough research:** Check all relevant historical tickets before making decisions
- **Conservative duplicate detection:** Only close as duplicate when confident of same root cause
- **Clear documentation:** Every decision should be explained in ticket comments
- **Preserve information:** Never lose valuable customer context when consolidating
- **Actionable output:** Prioritisation should help humans focus on what matters most

## Error Handling

- **Multiple potential duplicates:** Raise in comments but choose single best match
- **Unclear priority:** Default to higher tier and explain uncertainty in comments
- **Missing information:** Note gaps and suggest follow-up questions
- **Complex tickets:** Break down analysis and highlight key decision factors

## Task Execution

**Start immediately**: Query Linear for CX tickets in "labelled" status and begin processing them according to the instructions above. Do not implement or build anything - your job is to process the actual tickets that exist right now.

Your goal is to transform a large volume of labelled tickets into a smaller, well-organised, prioritised set of actionable items that humans can efficiently review and assign.
