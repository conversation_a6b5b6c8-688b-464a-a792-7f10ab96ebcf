# Automating Customer Feedback with AI Agents: A 5-Minute Case Study

## The Problem: Drowning in Customer Feedback

Everyone on my team was spending several hours daily just triaging customer feedback - and that was earlier in our growth when we had a fraction of the users we do now. Raw customer reports were often vague ("the extension doesn't work"), contained multiple unrelated issues, or were duplicates of existing problems. Our team was churning on manual categorisation and triage instead of solving actual customer problems.

**The pain points were clear:**
- High volume of tickets with inconsistent labelling
- Frequent misrouting and duplicate tickets
- Slow response times for customer support issues
- Team time spent on categorisation instead of problem-solving

## Why CLI Agents Work for This

We recently launched Augment's CLI agent - a new capability that extends our AI coding assistant beyond the IDE into full workflow automation. The key insight was that customer feedback triage isn't just about text classification - it requires the same deep contextual understanding that makes AI coding assistants effective. The CLI agent brings the same codebase awareness through our context engine, understanding not just what customers are saying, but how it relates to actual code, recent changes, and historical patterns.

More importantly, this new CLI agent has built-in tool integration with systems like Linear, GitHub, and Confluence. This means it can not only analyse tickets but actually take action - updating labels, creating sub-tickets, posting responses, and moving items through workflows. It's the difference between getting AI recommendations and having AI actually do the work.

## The Solution: A 3-Agent Pipeline

Instead of building complex ML classifiers or rule-based systems, we used Augment's CLI agent to create three specialised AI agents that work together:

### 1. Labelling Agent
**Job**: Transform messy customer feedback into clean, structured tickets
- Detects multiple issues in single tickets and splits them
- Enhances vague titles ("Login broken" → "Google SSO authentication fails in VSCode")
- Applies consistent labels (Bug/Feature, product area, priority)
- Preserves original customer voice while adding clarity

### 2. Support Agent  
**Job**: Provide technical solutions for support-labelled tickets
- Researches similar historical tickets and codebase
- Only responds with 80%+ confidence (conservative approach)
- Provides step-by-step solutions with version-specific guidance
- Skips uncertain cases rather than guessing

### 3. Triage Agent
**Job**: Reduce volume and prioritise remaining work
- Detects and closes duplicate tickets (consolidates info into master ticket)
- Assigns priority using consistent tier system (🔥 Urgent → 📝 Low)
- Groups related tickets for batch resolution
- Processes 25 tickets daily in FIFO order

## Implementation: Simple CLI Commands

The beauty is in the simplicity. Each agent runs with a single command:

```bash
# Daily labelling run
augment --non-interactive "Process CX tickets in triage status - analyse, enhance, and label appropriately"

# Support automation
augment --non-interactive "Provide technical solutions for CX tickets labelled 'Support' with high confidence"

# Daily triage run
augment --non-interactive "Process 25 oldest labelled CX tickets - detect duplicates, assign priorities, move to ai-triaged"
```

The agents use Augment's built-in Linear integration, codebase search, and historical context to make informed decisions.

## Results: Dramatic Improvement

**Time Savings:**
- Daily triage time reduced from over an hour to minutes
- Much faster response times for support issues

**Quality Improvements:**
- Significantly improved labelling accuracy and consistency
- Better duplicate detection and consolidation
- Substantial volume reduction through automated duplicate handling

**Team Impact:**
- Humans focus on complex decisions, not routine categorisation
- Consistent ticket structure and prioritisation
- Better pattern recognition for emerging issues

## Key Lessons Learned

1. **Start Conservative**: High confidence thresholds prevent bad automation
2. **Preserve Context**: Never lose the customer's original voice
3. **Human-AI Collaboration**: Augment expertise, don't replace it
4. **Iterate Quickly**: Started with one agent, evolved to three-agent pipeline

## Beyond Feedback: The Bigger Picture

This same approach works for other SDLC pain points:
- **Bug Triage**: Automatic severity assessment and component assignment
- **Release Management**: Monitor PRs and wiki pages to track feature progress and identify when features are released to users
- **Documentation**: Understand code changes and translate them into user-facing usage guides and API documentation

## Getting Started

1. **Identify your biggest manual time sink** (probably not coding!)
2. **Start with one simple automation** using existing tools
3. **Measure before and after** to prove value
4. **Iterate based on real usage** patterns

The future of software development isn't just about writing code faster—it's about automating the entire development lifecycle so humans can focus on what they do best: solving complex problems and making strategic decisions.

---

*Want to see the implementation details? Check out our [GitHub repository](link) with the full agent prompts and workflow design.*
