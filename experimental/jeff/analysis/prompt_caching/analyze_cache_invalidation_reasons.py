#!/usr/bin/env python3
"""
Script to diff tokenization fields in chat_host_request events.

Input should be from experimental/vpas/agent/chat_history_compression/analyze_cache_invalidation.py

Usage:
    python analyze_cache_invalidation_reasons.py --csv-file requests.csv --output-file results.csv
"""

import argparse
import logging

import pandas as pd

from experimental.jeff.analysis.prompt_caching.invalidation_reason_utils import (
    fetch_and_calculate_reasons_df,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    parser = argparse.ArgumentParser(
        description="Check tokenization fields in chat_host_request events"
    )
    parser.add_argument("--csv-file", required=True, help="Path to CSV file")
    parser.add_argument(
        "--limit", type=int, help="Limit the number of requests to process"
    )
    parser.add_argument("--output-file", help="Output file path for results")
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    info_columns = [
        "tenant",
        "prev_request_id",
        "request_id",
        "invalidation_type",
        "reason",
        "diff",
        "diff_length",
    ]

    aggs = {
        "prev_cache_reads": "sum",
        "curr_cache_reads": "sum",
        "prev_input_tokens": "sum",
        "curr_input_tokens": "sum",
        "extra_cost": "sum",
        "actual_cost": "sum",
        "time_gap_minutes": "mean",
        "cache_reads_lost": "sum",
        "token_change": "sum",
    }
    agg_columns = list(aggs.keys())

    df = pd.read_csv(args.csv_file, comment="#")
    if args.limit:
        df = df.head(args.limit)
    results_df = fetch_and_calculate_reasons_df(df)
    results_df = pd.merge(df, results_df, on="request_id", how="inner", validate="1:1")
    results_df = results_df.assign(
        reason=results_df["reason"].where(
            ~(
                (results_df["reason"] == "unknown")
                & (results_df["curr_cache_reads"] == 0)
            ),
            "unknown_full_cache_break",
        )
    )

    # Create comprehensive summary with count, tokens, and costs
    print("\n\n\n=== Summary ===")

    summary_columns = ["count"] + agg_columns
    summary_df = (
        results_df.assign(count=1)
        .groupby(["invalidation_type", "reason"])[summary_columns]
        .agg({"count": "sum", **aggs})
        .sort_values(by="count", ascending=False)
    )

    # Configure pandas to show all columns without truncation
    with pd.option_context(
        "display.max_columns", None, "display.width", None, "display.max_colwidth", None
    ):
        print(summary_df)

    print("\n\n\n=== Detailed Results ===")
    details = results_df[
        (results_df["reason"] == "unknown")
        & (results_df["invalidation_type"] == "alteration_beginning")
    ]
    details = details.sort_values("extra_cost", ascending=False)
    for _, row in details.head(5).iterrows():
        print("\n\n===== Metadata =====")
        print(f"prev_request_id: {row['prev_request_id']}")
        print(f"request_id: {row['request_id']}")
        print(f"tenant_name: {row['tenant']}")
        print(f"invalidation_type: {row['invalidation_type']}")
        print(f"curr_cache_reads: {row['curr_cache_reads']}")
        print(f"extra_cost: {row['extra_cost']}")
        print("===== Diff =====")
        print(f"reason: {row['reason']}")
        print(f"diff_length: {row['diff_length']}")
        # Truncate if too long
        print(f"{row['diff'][-1000:]}")
        print()  # Empty line between entries

    # Save results if output file specified
    if args.output_file:
        save_columns = info_columns + agg_columns
        results_df[save_columns].to_csv(args.output_file, index=False)
        logger.info(f"Results saved to {args.output_file}")

    return 0


if __name__ == "__main__":
    exit(main())
