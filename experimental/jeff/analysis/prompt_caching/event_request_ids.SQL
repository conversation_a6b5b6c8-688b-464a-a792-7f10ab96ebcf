DECLARE analysis_date DATE DEFAULT "2025-06-25";

CREATE TEMP FUNCTION extractAllRequestIds(json JSON)
RETURNS ARRAY<STRUCT<event_type STRING, key STRING, request_id STRING>>
LANGUAGE js
AS """
  const result = [];

  // Iterate through all top-level keys in the JSON
  for (const eventType in json) {
    if (json.hasOwnProperty(eventType)) {
      const eventData = json[eventType];

      // Check if this key has the expected structure: tracing_data.request_ids
      if (eventData &&
          typeof eventData === 'object' &&
          eventData.tracing_data &&
          eventData.tracing_data.request_ids &&
          typeof eventData.tracing_data.request_ids === 'object') {

        const requestIds = eventData.tracing_data.request_ids;
        for (const key in requestIds) {
          if (requestIds.hasOwnProperty(key)) {
            result.push({
              event_type: eventType,
              key: key,
              request_id: requestIds[key]["value"]
            });
          }
        }
      }
    }
  }

  return result;
""";

SELECT
  JSON_VALUE(sanitized_json, '$.event_name') as event_name,
  request_data.event_type,
  request_data.key,
  request_data.request_id,
  COUNT(*) as count
FROM `system-services-prod.us_prod_request_insight_analytics_dataset.agent_session_event`,
UNNEST(extractAllRequestIds(sanitized_json)) as request_data
WHERE
  TIMESTAMP_TRUNC(time, DAY) = TIMESTAMP(analysis_date)
GROUP BY event_name, request_data.event_type, request_data.key, request_data.request_id
ORDER BY event_name, request_data.event_type, request_data.key;
