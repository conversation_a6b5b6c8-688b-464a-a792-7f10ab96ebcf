#!/usr/bin/env python3
"""
<PERSON>ript to check which memory requests are worth remembering.

1. Creates a pandas DataFrame from a CSV string containing request IDs
2. Use GCSRequestInsightFetcher to fetch chat_host_request AND chat_host_response events
3. Classifies each request as worth remembering by parsing response as json
and checking worthRemembering field
4. Extracts the latest user message from each request, splits by whitespace, and ranks
the top keywords above a certain count by F1 (calculating precision + recall).

Usage:
    # Input file at ./i1-vanguard-2025-07-01/requests.csv
    python memories_checker.py --prompt-file classify_and_distill_prompt.txt --work-dir ./i1-vanguard-2025-07-01
"""

import argparse
import json
import logging
import re
import string
from dataclasses import dataclass
from pathlib import Path

import pandas as pd

from base.datasets.gcs_client import GCSRequestInsightFetcher, Request
from services.chat_host import chat_pb2

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class MemoryAnalysis:
    request_id: str
    worth_remembering: bool | None
    user_message: str | None


def _get_text(request: chat_pb2.ChatRequest) -> str | None:
    if request.message:
        return request.message
    for node in request.nodes:
        if node.type == chat_pb2.ChatRequestNodeType.TEXT and node.text_node:
            return node.text_node.content
    return None


@dataclass(frozen=True)
class ClassifyAndDistillPrompt:
    prefix: str
    suffix: str

    def extract_user_message(self, text: str) -> str | None:
        if not text.startswith(self.prefix):
            return None
        if not text.endswith(self.suffix):
            return None
        return text[len(self.prefix) : len(text) - len(self.suffix)]


def _get_classify_and_distill_prompt(
    classify_and_distill_prompt: str,
) -> ClassifyAndDistillPrompt | None:
    if not classify_and_distill_prompt:
        return None
    parts = classify_and_distill_prompt.split("{message}")
    if len(parts) != 2:
        return None
    prefix, suffix = parts
    return ClassifyAndDistillPrompt(prefix, suffix)


def extract_user_message(
    result: Request | None, classify_and_distill_prompt: ClassifyAndDistillPrompt
) -> str | None:
    """
    Extract the latest user message from a chat_host_request event.

    Args:
        result: Request result from GCSRequestInsightFetcher

    Returns:
        Latest user message or None if not found
    """
    if not result or not result.events:
        return None

    for event in result.events:
        if event.HasField("chat_host_request"):
            request = event.chat_host_request.request
            text = _get_text(request)
            if text is None:
                return None

            return classify_and_distill_prompt.extract_user_message(text)

    return None


@dataclass(frozen=True)
class ClassifyAndDistillResponse:
    explanation: str | None
    worth_remembering: bool | None
    content: str | None


def extract_classify_and_distill_response(
    result: Request | None,
) -> ClassifyAndDistillResponse | None:
    try:
        if not result or not result.events:
            return None

        # Find the chat_host_response event
        for event in result.events:
            if event.HasField("chat_host_response"):
                chat_response = event.chat_host_response
                response_text = chat_response.response.text

                try:
                    # Try to parse the response as JSON
                    response_json = json.loads(response_text)
                    worth_remembering = response_json.get("worthRemembering")
                    explanation = response_json.get("explanation")
                    content = response_json.get("content")

                    return ClassifyAndDistillResponse(
                        explanation, worth_remembering, content
                    )
                except json.JSONDecodeError:
                    # If it's not valid JSON, return None for worthRemembering
                    return None

        return None

    except Exception as e:
        logger.error(f"Error extracting worthRemembering: {str(e)}")
        return None


def fetch_data(
    df: pd.DataFrame, classify_and_distill_prompt: ClassifyAndDistillPrompt
) -> pd.DataFrame:
    result_rows = []

    # Group by tenant_name to create fetchers efficiently
    for tenant_name, group in df.groupby("tenant"):
        tenant_name_str = str(tenant_name)
        logger.info(f"Processing tenant: {tenant_name_str}")
        fetcher = GCSRequestInsightFetcher.from_tenant_name(tenant_name_str)

        # Collect all unique request IDs for this tenant
        all_request_ids = []
        for _, row in group.iterrows():
            request_id = row["request_id"]
            all_request_ids.append(request_id)

        # Remove duplicates while preserving order
        unique_request_ids = list(dict.fromkeys(all_request_ids))

        logger.info(
            f"Fetching {len(unique_request_ids)} unique requests for tenant {tenant_name_str}"
        )

        # Fetch all requests in parallel using batched call for both request and response events
        request_results: dict[str, Request] = {}
        for result in fetcher.get_requests(
            request_ids=unique_request_ids,
            request_event_names=frozenset({"chat_host_request", "chat_host_response"}),
        ):
            if isinstance(result, Exception):
                logger.error(f"Error fetching request: {result}")
                continue
            request_results[result.request_id] = result

        # Process each row with the fetched results
        for _, row in group.iterrows():
            request_id = row["request_id"]

            logger.debug(f"Processing request: {request_id}")

            result = request_results.get(request_id)

            # Extract user message and worthRemembering
            user_message = extract_user_message(result, classify_and_distill_prompt)
            response = extract_classify_and_distill_response(result)

            result_rows.append(
                {
                    "worth_remembering": response.worth_remembering
                    if response
                    else None,
                    "explanation": response.explanation if response else None,
                    "content": response.content if response else None,
                    "user_message": user_message,
                    **row.to_dict(),
                }
            )

    return pd.DataFrame(result_rows)


@dataclass
class WordAnalysis:
    words_df: pd.DataFrame
    words_agg_df: pd.DataFrame
    words_f1_df: pd.DataFrame
    bigrams_df: pd.DataFrame
    bigrams_agg_df: pd.DataFrame
    bigrams_f1_df: pd.DataFrame
    regexes_df: pd.DataFrame
    regexes_agg_df: pd.DataFrame
    regexes_f1_df: pd.DataFrame


def compute_word_analysis(
    data_df: pd.DataFrame,
    beta: float,
    regex: str,
) -> WordAnalysis:
    words = (
        data_df.user_message.str.lower()
        .str.split(pat=f"[\\s]|[{re.escape(string.punctuation)}]")
        .apply(lambda x: list(dict.fromkeys(x)))
        .rename("word")
    )
    words_df = pd.merge(
        words.explode(), data_df.worth_remembering, left_index=True, right_index=True
    )
    words_agg_df = pd.crosstab(words_df["word"], words_df["worth_remembering"])
    bigrams = words.apply(lambda x: [" ".join(y) for y in list(zip(x, x[1:]))])
    bigrams = bigrams.rename("bigram")
    bigrams_df = pd.merge(
        bigrams.explode(), data_df.worth_remembering, left_index=True, right_index=True
    )
    bigrams_agg_df = pd.crosstab(bigrams_df["bigram"], bigrams_df["worth_remembering"])

    regexes = data_df.user_message.str.contains(regex, flags=re.IGNORECASE).rename(
        "regex_match"
    )
    regexes_df = pd.merge(
        regexes, data_df.worth_remembering, left_index=True, right_index=True
    )
    regexes_agg_df = pd.crosstab(
        regexes_df["regex_match"], regexes_df["worth_remembering"]
    )

    total_positives = data_df.worth_remembering.value_counts()[True]
    words_f1_df = calc_metrics_df(words_agg_df, total_positives, beta=beta)
    bigrams_f1_df = calc_metrics_df(bigrams_agg_df, total_positives, beta=beta)
    regexes_f1_df = calc_metrics_df(regexes_agg_df, total_positives, beta=beta)

    return WordAnalysis(
        words_df,
        words_agg_df,
        words_f1_df,
        bigrams_df,
        bigrams_agg_df,
        bigrams_f1_df,
        regexes_df,
        regexes_agg_df,
        regexes_f1_df,
    )


def calc_metrics_df(df: pd.DataFrame, total_positives: int, beta=0.5):
    precision = df[True] / (df[True] + df[False])
    recall = df[True] / total_positives
    f1 = 2 * (precision * recall) / (precision + recall)
    fbeta = (1 + beta**2) * (precision * recall) / (beta**2 * precision + recall)
    return df.assign(
        precision=precision, recall=recall, f1=f1, fbeta=fbeta
    ).sort_values(by="fbeta", ascending=False)


def parse_args(args: list[str] | None = None):
    parser = argparse.ArgumentParser(
        description="Check which memory requests are worth remembering"
    )
    parser.add_argument(
        "--prompt-file", required=True, help="Path to prompt file for user messages"
    )
    parser.add_argument(
        "--work-dir",
        help="Work directory for input/output files. Input should be at requests.csv",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )
    parser.add_argument(
        "--beta", type=float, default=1 / 3, help="Beta value for F-beta score"
    )
    parser.add_argument(
        "--regex", type=str, default=".*", help="Regex to allow for worth remembering."
    )
    parser.add_argument(
        "--limit", type=int, help="Limit the number of requests to process", default=0
    )
    parser.add_argument(
        "--min-keyword-count",
        type=int,
        default=2,
        help="Minimum count threshold for keywords (default: 2)",
    )

    return parser.parse_args(args)


def main_fetch_df(args: argparse.Namespace) -> pd.DataFrame:
    work_path = Path(args.work_dir)

    with open(args.prompt_file) as f:
        classify_and_distill_prompt = _get_classify_and_distill_prompt(f.read())

    if classify_and_distill_prompt is None:
        raise RuntimeError("Failed to parse classify and distill prompt")

    df = pd.read_csv(work_path / "requests.csv", comment="#")
    if args.limit:
        df = df.head(args.limit)
    data_df = fetch_data(df, classify_and_distill_prompt)
    logger.info(
        "Parsed %d/%d user messages",
        data_df["user_message"].notna().sum(),
        len(data_df),
    )

    return data_df


def main():
    args = parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    data_df = main_fetch_df(args)

    work_path = Path(args.work_dir)

    # Print worth remembering counts
    print("\n\n\n=== Worth Remembering Counts ===")
    print(data_df["worth_remembering"].value_counts())

    analysis = compute_word_analysis(data_df, args.beta, args.regex)
    print("\n\n\n=== Words Summary ===")
    print(analysis.words_f1_df.head(40))
    print("\n\n\n=== Bigrams Summary ===")
    print(analysis.bigrams_f1_df.head(40))
    print("\n\n\n=== Regexes Summary ===")
    print("\nregex:", repr(args.regex))
    print(analysis.regexes_f1_df.head(40))

    # Save results
    logger.info(f"Saving results to {work_path / 'data.csv'}")
    data_df.to_csv(work_path / "data.csv")
    logger.info(f"Saving word analysis to {work_path / 'words_f1.csv'}")
    analysis.words_f1_df.to_csv(work_path / "words_f1.csv")
    logger.info(f"Saving bigram analysis to {work_path / 'bigrams_f1.csv'}")
    analysis.bigrams_f1_df.to_csv(work_path / "bigrams_f1.csv")
    logger.info(f"Saving regex analysis to {work_path / 'regexes_f1.csv'}")
    analysis.regexes_f1_df.to_csv(work_path / "regexes_f1.csv")

    return 0


if __name__ == "__main__":
    exit(main())
