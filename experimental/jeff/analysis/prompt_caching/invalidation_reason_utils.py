#!/usr/bin/env python3
"""
Utils to diff tokenization fields in chat_host_request events.
"""

import difflib
import logging
from dataclasses import dataclass
import re

import pandas as pd

from base.datasets.gcs_client import GCSRequestInsightFetcher, Request

logger = logging.getLogger(__name__)


def extract_tokenization_text_from_result(result: Request | None) -> str | None:
    """
    Extract tokenization text from a fetched request result.

    Args:
        result: Request result from GCSRequestInsightFetcher

    Returns:
        Tokenization text or None if not found
    """
    try:
        if not result or not result.events:
            return None

        # Find the chat_host_request event
        for event in result.events:
            if event.HasField("chat_host_request"):
                chat_request = event.chat_host_request
                if chat_request.HasField("tokenization"):
                    return chat_request.tokenization.text
                else:
                    return None

        return None

    except Exception as e:
        logger.error(f"Error extracting tokenization text: {str(e)}")
        return None


def rearrange_tokenization(text: str) -> str:
    """
    Rearrange the tokenization text to put current message in order
    """
    section = "-" * 40 + "\\n{}:\\n" + "-" * 40 + "\\n"
    current_message = section.format("current message")
    model_prefill = section.format("model response prefill")
    chat_history = section.format("chat history \\(from most recent to least recent\\)")
    any_group = "([\\s\\S]*)"
    regex = f"{any_group}{current_message}{any_group}{model_prefill}{any_group}{chat_history}{any_group}"
    match = re.fullmatch(regex, text)
    if not match:
        return text

    unescaped_section = "-" * 40 + "\n{}:\n" + "-" * 40 + "\n"
    indent_section = "".join(
        (" " * 4 + line) for line in unescaped_section.splitlines(keepends=True)
    )
    unescaped_chat_history = unescaped_section.format(
        "chat history (from most recent to least recent)"
    )
    user = indent_section.format("user") + " " * 4
    assistant = indent_section.format("assistant") + " " * 4
    return f"{match.group(1)}{unescaped_chat_history}{match.group(4)}{user}{match.group(2)}{assistant}{match.group(3)}"


@dataclass
class TokenizationComparison:
    diff: str
    diff_length: int
    reason: str


def is_equal_search(prev_text: str, current_text: str, regex: str):
    prev_match = re.search(regex, prev_text)
    current_match = re.search(regex, current_text)
    if prev_match is None or current_match is None:
        return prev_match is None and current_match is None

    return prev_match.group() == current_match.group()


def compare_tokenization(prev_text: str, current_text: str) -> TokenizationComparison:
    """
    Compare two tokenization texts and return a unified diff.

    Args:
        prev_text: Tokenization text from previous request
        current_text: Tokenization text from current request

    Returns:
        String containing the unified diff between the texts
    """
    # Generate unified diff
    diff_lines = list(
        difflib.unified_diff(
            prev_text.splitlines(keepends=True),
            current_text.splitlines(keepends=True),
            fromfile="prev_request",
            tofile="current_request",
            lineterm="",
        )
    )

    diff = "".join(diff_lines)
    if not prev_text:
        reason = "prev_request_missing"
    elif not current_text:
        reason = "current_request_missing"
    elif prev_text == current_text:
        reason = "no_change"
    elif not is_equal_search(
        prev_text,
        current_text,
        r"\n# Memories\n.*\n```[\S\s]*```\n\n# Current Task List",
    ):
        reason = "memories"
    elif not is_equal_search(
        prev_text,
        current_text,
        rf"{'-' * 40}\nsystem_prompt:\n{'-' * 40}[\S\s]*\n{'-' * 40}\n(chat history|current message)",
    ):
        reason = "unknown_system_prompt_break"
    elif "\n-    The user has the file" in diff:
        reason = "selected_code"
    elif "\n+    The user has the file" in diff:
        reason = "selected_code"
    elif "\n-The user has the file " in diff:
        reason = "selected_code"
    elif "\n+The user has the file " in diff:
        reason = "selected_code"
    elif "\n+Content: Cancelled by user." in diff:
        reason = "cancelled_by_user"
    elif re.search(
        r"\n-Content: .*(\n-.*)*\n\+Content: \[Truncated...re-run tool if you need to see output again.\]",
        diff,
    ):
        reason = "tool_truncation"
    else:
        reason = "unknown"

    return TokenizationComparison(diff, len(diff_lines), reason)


def fetch_and_calculate_reasons_df(df: pd.DataFrame):
    # Group prev_request_id and request_id by tenant_name and fetch tokenization data
    results = []

    # Group by tenant_name to create fetchers efficiently
    for tenant_name, group in df.groupby("tenant"):
        tenant_name_str = str(tenant_name)
        logger.info(f"Processing tenant: {tenant_name_str}")
        fetcher = GCSRequestInsightFetcher.from_tenant_name(tenant_name_str)

        # Collect all unique request IDs for this tenant
        all_request_ids = []
        for _, row in group.iterrows():
            prev_request_id = row["prev_request_id"]
            request_id = row["request_id"]
            all_request_ids.extend([prev_request_id, request_id])

        # Remove duplicates while preserving order
        unique_request_ids = list(dict.fromkeys(all_request_ids))

        logger.info(
            f"Fetching {len(unique_request_ids)} unique requests for tenant {tenant_name_str}"
        )

        # Fetch all requests in parallel using batched call
        request_results: dict[str, Request] = {}
        for result in fetcher.get_requests(
            request_ids=unique_request_ids,
            request_event_names=frozenset({"chat_host_request"}),
        ):
            if isinstance(result, Exception):
                logger.error(f"Error fetching request: {result}")
                continue
            request_results[result.request_id] = result

        logger.info(f"Processing {len(group)} request pairs")
        # Process each row with the fetched results
        for _, row in group.iterrows():
            prev_request_id = row["prev_request_id"]
            request_id = row["request_id"]

            logger.debug(f"Processing pair: {prev_request_id} -> {request_id}")

            # Extract tokenization text from batched results
            prev_result = request_results.get(prev_request_id)
            current_result = request_results.get(request_id)

            prev_text = extract_tokenization_text_from_result(prev_result)
            current_text = extract_tokenization_text_from_result(current_result)
            prev_text = prev_text or ""
            current_text = current_text or ""
            prev_text = rearrange_tokenization(prev_text)
            current_text = rearrange_tokenization(current_text)

            # Compare the tokenization texts
            comparison = compare_tokenization(prev_text, current_text)

            reason = comparison.reason

            # Add token columns from original CSV data
            result_row = {
                "request_id": request_id,
                "prev_text": prev_text,
                "current_text": current_text,
                "diff": comparison.diff,
                "diff_length": comparison.diff_length,
                "reason": reason,
            }
            results.append(result_row)

    # Create results DataFrame
    results_df = pd.DataFrame(results)
    return results_df
