#!/usr/bin/env python3
"""
Script to diff tokenization fields in chat_host_request events for memories.

This script:
1. Creates a pandas DataFrame from a CSV string containing request IDs
2. Uses GCSRequestInsightFetcher to fetch chat_host_request events
3. Checks if tokenization fields match between different requests

The input csv should be derived from a query in `silent_exchange_token_usage.SQL`

Usage:
    python analyze_memories_invalidation_reasons.py --csv-file requests.csv --output-file results.csv
"""

import argparse
import logging

import pandas as pd

from experimental.jeff.analysis.prompt_caching.invalidation_reason_utils import (
    fetch_and_calculate_reasons_df,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


COST: dict[str, float] = {
    "input_tokens": 3.0 / 1_000_000,
    "output_tokens": 15.0 / 1_000_000,
    "cache_read_input_tokens": 0.3 / 1_000_000,
    "cache_creation_input_tokens": 3.75 / 1_000_000,
}

CACHE_BREAK_COSTS: dict[str, float] = {
    k: v for k, v in COST.items() if k in {"cache_creation_input_tokens"}
}


def calculate_costs(row: pd.Series) -> float:
    cost = 0
    for col, price in COST.items():
        cost += row.get(col, 0) * price
    return cost


def calculate_cache_break_costs(row: pd.Series) -> float:
    cost = 0
    for col, price in CACHE_BREAK_COSTS.items():
        cost += row.get(col, 0) * price
    return cost


def main():
    parser = argparse.ArgumentParser(
        description="Check tokenization fields in chat_host_request events"
    )
    parser.add_argument("--csv-file", required=True, help="Path to CSV file")
    parser.add_argument("--output-file", help="Output file path for results")
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    info_columns = [
        "tenant",
        "prev_request_id",
        "request_id",
        "reason",
        "diff",
        "diff_length",
    ]

    agg_columns = [
        "cache_break_cost",
        "input_tokens",
        "text_output_tokens",
        "tool_output_tokens",
        "cache_read_input_tokens",
        "cache_creation_input_tokens",
        "total_output_tokens",
    ]

    df = pd.read_csv(args.csv_file, comment="#")
    df = df.rename(columns={"user_request_id": "prev_request_id"})
    df = df.assign(cache_break_cost=df.apply(calculate_cache_break_costs, axis=1))
    results_df = fetch_and_calculate_reasons_df(df)
    results_df = pd.merge(df, results_df, on="request_id", how="inner", validate="1:1")

    # Create comprehensive summary with count, tokens, and costs
    print("\n\n\n=== Summary ===")

    summary_columns = ["count"] + agg_columns
    summary_df = (
        results_df.assign(count=1)
        .groupby("reason")[summary_columns]
        .sum()
        .sort_values(by="count", ascending=False)
    )

    # Configure pandas to show all columns without truncation
    with pd.option_context(
        "display.max_columns", None, "display.width", None, "display.max_colwidth", None
    ):
        print(summary_df)

    print("\n\n\n=== Detailed Results ===")
    details = results_df[(results_df["reason"] == "unknown")]
    for _, row in details.head(5).iterrows():
        print("\n\n===== Metadata =====")
        print(f"prev_request_id: {row['prev_request_id']}")
        print(f"request_id: {row['request_id']}")
        print(f"tenant_name: {row['tenant']}")
        print("===== Diff =====")
        print(f"reason: {row['reason']}")
        # Truncate if too long
        print(f"{row['diff'][-1000:]}")
        print()  # Empty line between entries

    # Save results if output file specified
    if args.output_file:
        save_columns = info_columns + agg_columns
        results_df[save_columns].to_csv(args.output_file, index=False)
        logger.info(f"Results saved to {args.output_file}")

    return 0


if __name__ == "__main__":
    exit(main())
