-- Cost per 1M tokens for each token type (in USD)
-- input_tokens_price_per_1M = 3
-- output_tokens_price_per_1M = 15
-- cache_read_input_tokens_price_per_1M = 0.3
-- cache_creation_input_tokens_price_per_1M = 3.75

-- date_min (inclusive) - change this to analyze different dates
-- date_max (inclusive) - change this to analyze different dates
DECLARE time_min TIMESTAMP DEFAULT "2025-06-25 00:00:00";
DECLARE time_max TIMESTAMP DEFAULT "2025-06-25 23:59:59";

CREATE TEMP FUNCTION extractAllRequestIds(json JSON)
RETURNS ARRAY<STRUCT<event_type STRING, key STRING, request_id STRING, user_request_id STRING>>
LANGUAGE js
AS """
  const result = [];

  // Iterate through all top-level keys in the JSON
  for (const eventType in json) {
    if (json.hasOwnProperty(eventType)) {
      const eventData = json[eventType];

      // Check if this key has the expected structure: tracing_data.request_ids
      if (eventData &&
          typeof eventData === 'object' &&
          eventData.tracing_data &&
          eventData.tracing_data.request_ids &&
          typeof eventData.tracing_data.request_ids === 'object') {

        const requestIds = eventData.tracing_data.request_ids;

        // Extract lastUserExchangeRequestId if it exists
        const lastUserExchangeRequestId = requestIds.lastUserExchangeRequestId ?
          requestIds.lastUserExchangeRequestId["value"] : null;

        for (const key in requestIds) {
          if (requestIds.hasOwnProperty(key) && key !== 'lastUserExchangeRequestId') {
            result.push({
              event_type: eventType,
              key: key,
              request_id: requestIds[key]["value"],
              user_request_id: lastUserExchangeRequestId
            });
          }
        }
      }
    }
  }

  return result;
""";

CREATE TEMP FUNCTION extractClientType(user_agent STRING)
RETURNS STRING
LANGUAGE js
AS """
  if (!user_agent) {
    return null;
  }

  const match = user_agent.match(/^([^\\/]+)/);
  return match ? match[1] : null;
""";

WITH silent_requests AS (
  SELECT
    tenant,
    tenant_id,
    shard_namespace,
    request_id,
    time,
    BOOL(JSON_EXTRACT(sanitized_json, '$.request.silent')) as silent
  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.chat_host_request`
  WHERE
    time BETWEEN time_min AND time_max
),

request_metadata AS (
  SELECT
    request_id,
    STRING_AGG(DISTINCT extractClientType(user_agent), ', ' ORDER BY extractClientType(user_agent)) as client_type
  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.request_metadata`
  WHERE
    time BETWEEN time_min AND time_max
  GROUP BY request_id
),

event_request_ids AS (
  SELECT
    request_data.request_id,
    STRING_AGG(DISTINCT JSON_VALUE(sanitized_json, '$.event_name'), ', ' ORDER BY JSON_VALUE(sanitized_json, '$.event_name')) as event_name,
    STRING_AGG(DISTINCT request_data.event_type, ', ' ORDER BY request_data.event_type) as event_type,
    STRING_AGG(DISTINCT request_data.key, ', ' ORDER BY request_data.key) as key,
    STRING_AGG(DISTINCT request_data.user_request_id, ', ' ORDER BY request_data.user_request_id) as user_request_id
  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.agent_session_event`,
  UNNEST(extractAllRequestIds(sanitized_json)) as request_data
  WHERE
    time BETWEEN time_min AND time_max
  GROUP BY request_data.request_id
),

classified_silent_requests AS (
  SELECT
    sr.tenant,
    sr.tenant_id,
    sr.shard_namespace,
    sr.request_id,
    sr.time,
    sr.silent,
    rm.client_type,
    eri.user_request_id,
    COALESCE(
      CONCAT(eri.event_type, '.', eri.key),
      'unclassified'
    ) as classification
  FROM silent_requests sr
  LEFT JOIN event_request_ids eri
    ON sr.request_id = eri.request_id
  LEFT JOIN request_metadata rm
    ON sr.request_id = rm.request_id
),

token_usage AS (
  SELECT
    request_id,
    SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.input_tokens')), 0)) as input_tokens,
    SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.text_output_tokens')), 0)) as text_output_tokens,
    SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.tool_output_tokens')), 0)) as tool_output_tokens,
    SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.cache_read_input_tokens')), 0)) as cache_read_input_tokens,
    SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.cache_creation_input_tokens')), 0)) as cache_creation_input_tokens,
    SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.text_output_tokens')), 0)) + SUM(COALESCE(INT64(JSON_QUERY(sanitized_json, '$.tool_output_tokens')), 0)) AS total_output_tokens
  FROM `system-services-prod.us_prod_request_insight_analytics_dataset.prompt_cache_usage`
  WHERE
    time BETWEEN time_min AND time_max
    AND (
      REGEXP_CONTAINS(JSON_VALUE(sanitized_json, '$.model_caller'), r'^(chat-host-claude-.*sonnet.*-agent)$') OR
      JSON_VALUE(sanitized_json, '$.model_caller') IS NULL OR
      JSON_VALUE(sanitized_json, '$.model_caller') = '' OR
      JSON_VALUE(sanitized_json, '$.model_caller') = 'agent-codebase-retrieval'
    )
  GROUP BY request_id
),

combined_data AS (
  SELECT
    csr.tenant,
    csr.tenant_id,
    csr.shard_namespace,
    csr.request_id,
    csr.classification,
    csr.silent,
    csr.time,
    csr.client_type,
    csr.user_request_id,
    COALESCE(tu.input_tokens, 0) as input_tokens,
    COALESCE(tu.text_output_tokens, 0) as text_output_tokens,
    COALESCE(tu.tool_output_tokens, 0) as tool_output_tokens,
    COALESCE(tu.cache_read_input_tokens, 0) as cache_read_input_tokens,
    COALESCE(tu.cache_creation_input_tokens, 0) as cache_creation_input_tokens,
    COALESCE(tu.total_output_tokens, 0) as total_output_tokens,
  FROM classified_silent_requests csr
  LEFT JOIN token_usage tu ON csr.request_id = tu.request_id
)

SELECT
  classification,
  silent,
  client_type,
  cache_read_input_tokens > 0 as cached,
  COUNT(*) as request_count,
  SUM(input_tokens) as total_input_tokens,
  SUM(total_output_tokens) as total_output_tokens,
  SUM(cache_read_input_tokens) as total_cache_read_input_tokens,
  SUM(cache_creation_input_tokens) as total_cache_creation_input_tokens,
  -- Cost calculations (prices per token)
  SUM(input_tokens) * (3.0 / 1000000) AS input_token_cost,
  SUM(total_output_tokens) * (15.0 / 1000000) AS total_output_token_cost,
  SUM(cache_read_input_tokens) * (0.3 / 1000000) AS cache_read_input_token_cost,
  SUM(cache_creation_input_tokens) * (3.75 / 1000000) AS cache_creation_input_token_cost,
  -- Total cost
  (
    SUM(input_tokens) * (3.0 / 1000000) +
    SUM(total_output_tokens) * (15.0 / 1000000) +
    SUM(cache_read_input_tokens) * (0.3 / 1000000) +
    SUM(cache_creation_input_tokens) * (3.75 / 1000000)
  ) AS total_cost
FROM combined_data
GROUP BY classification, silent, client_type, cached
ORDER BY classification, silent, client_type, cached;

-- Temporary query to see cost per hour by silent
-- SELECT
--   silent,
--   TIMESTAMP_TRUNC(time, HOUR) hour,
--   COUNT(*) as request_count,
--   SUM(input_tokens) as total_input_tokens,
--   SUM(total_output_tokens) as total_output_tokens,
--   SUM(cache_read_input_tokens) as total_cache_read_input_tokens,
--   SUM(cache_creation_input_tokens) as total_cache_creation_input_tokens,
--   SUM(input_tokens) * (3.0 / 1000000) AS input_token_cost,
--   SUM(total_output_tokens) * (15.0 / 1000000) AS total_output_token_cost,
--   SUM(cache_read_input_tokens) * (0.3 / 1000000) AS cache_read_input_token_cost,
--   SUM(cache_creation_input_tokens) * (3.75 / 1000000) AS cache_creation_input_token_cost,
--   (
--     SUM(input_tokens) * (3.0 / 1000000) +
--     SUM(total_output_tokens) * (15.0 / 1000000) +
--     SUM(cache_read_input_tokens) * (0.3 / 1000000) +
--     SUM(cache_creation_input_tokens) * (3.75 / 1000000)
--   ) AS total_cost
-- FROM combined_data
-- GROUP BY hour, silent
-- ORDER BY hour, silent;

-- Temporary query to see tenants for unclassified silent requests
-- SELECT
--   tenant,
--   tenant_id,
--   shard_namespace,
--   client_type,
--   COUNT(*) as request_count,
--   COUNT(DISTINCT request_id) as unique_requests
-- FROM combined_data
-- WHERE
--   classification = 'unclassified'
--   AND silent = true
-- GROUP BY tenant, tenant_id, shard_namespace, client_type
-- ORDER BY request_count DESC;
