#!/usr/bin/env python3
"""
Synthetic Dataset Generation Script

This script generates a large-scale synthetic dataset for reposcale testing by:
1. Downloading Chromium source code as base data
2. Uploading blobs via API proxy calls
3. Generating N=2000 synthetic checkpoints with permuted blobs
4. Making RegisterWorkingSet and CreateAnnIndexForCheckpoint calls
5. Monitoring indexing completion

Based on patterns from research/data/collection/github/controllers/git_clone_worker.py
"""

import argparse
import logging
import os
import random
import shutil
import subprocess
import sys
import time
import uuid
from contextlib import suppress
from pathlib import Path
from typing import Iterator, Optional, Sequence, Union

import yaml
import grpc

# Import Augment client libraries
from base.augment_client.client import AugmentClient, UploadContent, BlobsJson
from base.datasets.replay_utils import get_augment_client

# Import working set client libraries
from services.lib.grpc import grpc_args_parser, token_parser
from services.content_manager.client.content_manager_client import ContentManagerClient
from services.working_set.client.client import GrpcWorkingSetClient
from services.lib.request_context.request_context import RequestContext
from base.blob_names.blob_names_pb2 import Blobs

# Default timeout for git operations
DEFAULT_EXECUTE_TIMEOUT = 1800

class SyntheticDatasetGenerator:
    """Main class for generating synthetic datasets."""
    
    def __init__(self, args: argparse.Namespace):
        """Initialize the generator with configuration."""
        self.config = self._load_config(args.config)
        self._setup_logging()
        self.augment_client = self._create_augment_client()
        self.args = args
        self.file_path_to_blob_name = {}
        self.request_context = self._create_request_context()

    def _load_config(self, config_path: str) -> dict:
        """Load configuration from YAML file."""
        # Handle Bazel runfiles - look for config relative to script location
        if not os.path.exists(config_path):
            script_dir = Path(__file__).parent
            config_path = str(script_dir / config_path)

        with open(config_path, 'r') as f:
            return yaml.safe_load(f)

    def _setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=getattr(logging, self.config['logging']['level']),
            format=self.config['logging']['format']
        )
        self.logger = logging.getLogger(__name__)

    def _create_augment_client(self) -> AugmentClient:
        """Create and configure Augment client."""
        return get_augment_client(
            api_token_path=self.config['api']['token_path'],
            url=self.config['api']['endpoint'],
            timeout=self.config['api']['timeout']
        )

    def _create_request_context(self) -> RequestContext:
        """Create request context for working set operations."""
        # For now, create a simple request context without auth token
        # In production, you would get the token using token_parser.get_token()
        auth_token = token_parser.get_token(self.args)
        return RequestContext.create(auth_token=auth_token)

    def _execute_command(
        self,
        cmd: Union[str, Sequence[str]],
        timeout: Union[int, float, None] = DEFAULT_EXECUTE_TIMEOUT,
        check: bool = True,
        cwd: Optional[str] = None,
    ) -> str:
        """Execute a shell command with retry logic."""
        if isinstance(cmd, str):
            cmd_line = cmd
        else:
            cmd_line = " ".join(cmd)

        self.logger.info(f"Executing: {cmd_line}")
        p = subprocess.run(
            cmd, timeout=timeout, check=check, 
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, cwd=cwd
        )
        return p.stdout.decode("utf-8")

    def download_repository(self, output_dir: Path) -> Path:
        """Download repository to specified directory."""
        repo_url = self.config['repository']['url']
        repo_name = self.config['repository']['name']
        clone_depth = self.config['repository']['clone_depth']

        repo_dir = output_dir / repo_name

        if repo_dir.exists():
            self.logger.info(f"Repository already exists at {repo_dir}")
            return repo_dir

        self.logger.info(f"Cloning {repo_url} to {repo_dir}")

        # Use git clone with depth limit to manage size
        cmd = [
            "git", "-c", "core.askPass=true", "clone",
            f"--depth={clone_depth}",
            "--single-branch",
            repo_url, str(repo_dir)
        ]

        self._execute_command(cmd)
        self.logger.info(f"Successfully cloned repository to {repo_dir}")
        return repo_dir

    def iterate_text_files(
        self,
        repo_dir: Path,
    ) -> Iterator[str]:
        """Iterate through text files in repository using native Python."""

        # Walk through all files in the repository directory
        for root, dirs, files in os.walk(repo_dir):
            # Skip .git directory and other hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]

            for file_name in files:
                file_path = Path(root) / file_name

                # Skip hidden files
                if file_name.startswith('.'):
                    continue

                # Get relative path from repo root
                try:
                    relative_path = file_path.relative_to(repo_dir)
                    obj_path = str(relative_path)
                except ValueError:
                    continue

                yield obj_path

    def extract_file_content(self, file_path: Path) -> Optional[str]:
        """Extract and process file content using native Python."""
        try:
            file_size = file_path.stat().st_size
            max_file_size = self.config['file_processing']['max_file_size']
            if file_size > max_file_size:
                self.logger.warning(f"Skipping large file {file_path} ({file_size} bytes > {max_file_size} bytes)")
                return None

            # Try to read as text with UTF-8 first
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                return content
        except UnicodeDecodeError:
            # Try with different encodings
            for encoding in ['latin-1', 'cp1252', 'iso-8859-1']:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        return content
                except (UnicodeDecodeError, LookupError):
                    continue

            # If all text encodings fail, log and skip
            self.logger.warning(f"Could not decode file {file_path}")
            return None
        except (OSError, IOError) as e:
            self.logger.warning(f"Could not read file {file_path}: {e}")
            return None

    def process_files(
        self,
        previous_checkpoint_id: Optional[str],
        root_dir: Path,
        text_files: list[str],
        mutate: bool = False,
        index: bool = False,
    ) -> list[str]:
        """Process files in batches and create checkpoints."""
        batch_size = self.config["file_processing"]["batch_size"]
        checkpoint_frequency = self.config["file_processing"]["checkpoint_frequency"]
        mutation_size = self.config["synthetic_data"]["mutation_size"]
        transformation_keys = self.config["file_processing"]["transformation_keys"]

        uploaded_blobs = []
        stale_blobs = []

        checkpoint_ids = []

        # Process files in batches
        for i in range(0, len(text_files), batch_size):
            batch = text_files[i : i + batch_size]
            batch_file_paths = []
            upload_batch = []

            for obj_path in batch:
                file_path = root_dir / obj_path
                content = self.extract_file_content(file_path)
                if mutate:
                    content = self.mutate_blob_content(content, mutation_size)

                if content is not None:
                    upload_batch.append(
                        UploadContent(content=content, path_name=obj_path)
                    )
                    batch_file_paths.append(obj_path)  # Track this file path

            if upload_batch:
                self.logger.info(f"Uploading batch of {len(upload_batch)} files...")
                try:
                    blob_names = self.augment_client.batch_upload(upload_batch)
                    self.logger.info(f"Successfully uploaded {len(blob_names)} blobs")

                    # Update file path to blob name mapping
                    for obj_path, blob_name in zip(batch_file_paths, blob_names):
                        prev_blob_name = self.file_path_to_blob_name.get(obj_path)
                        self.file_path_to_blob_name[obj_path] = blob_name
                        if prev_blob_name:
                            stale_blobs.append(prev_blob_name)
                        uploaded_blobs.append(blob_name)

                except Exception as e:
                    self.logger.error(
                        f"All retry attempts failed, giving up on this batch. Error: {e}"
                    )

            # Check if we should create a checkpoint
            if len(uploaded_blobs) >= checkpoint_frequency or i + batch_size >= len(
                text_files
            ):
                self.logger.info(
                    f"Creating checkpoint after processing {len(uploaded_blobs)} files..."
                )

                checkpoint_id = self.create_checkpoint(
                    previous_checkpoint_id, uploaded_blobs, stale_blobs
                )
                self.logger.info(f"Checkpoint created with ID: {checkpoint_id}")

                if index:
                    self.create_ann_index(
                        checkpoint_id=checkpoint_id,
                        transformation_keys=transformation_keys,
                    )
                    self.poll_ann_index_completion(
                        checkpoint_id=checkpoint_id,
                        transformation_keys=transformation_keys,
                    )

                # Reset for next checkpoint
                previous_checkpoint_id = checkpoint_id
                uploaded_blobs = []
                stale_blobs = []
                checkpoint_ids.append(checkpoint_id)

        return checkpoint_ids

    def mutate_blob_content(self, original_content: Optional[str], mutation_size: int) -> Optional[str]:
        """Create a mutated version of blob content by appending random data."""
        if not original_content:
            return original_content

        # Generate random data
        random_data = ''.join(random.choices(
            'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789\n\t ',
            k=mutation_size
        ))

        # Append random data to the end of the file
        mutated_content = (
            original_content +
            f"\n// SYNTHETIC_MUTATION_{uuid.uuid4().hex[:8]}\n" +
            random_data +
            "\n// END_SYNTHETIC_MUTATION\n"
        )

        return mutated_content

    def create_checkpoint(self, checkpoint_id: Optional[str], added_blobs: list[str], deleted_blobs: list[str]) -> str:
        """Create a checkpoint with the given blobs."""
        self.logger.info(f"Creating checkpoint with previous checkpoint {checkpoint_id}, {len(added_blobs)} added blobs, and {len(deleted_blobs)} deleted blobs")
        try:
            blobs_json = BlobsJson(
                checkpoint_id=checkpoint_id,
                added_blobs=added_blobs,
                deleted_blobs=deleted_blobs
            )
            checkpoint_id = self.augment_client.checkpoint_blobs(blobs_json)
            return checkpoint_id
        except Exception as e:
            self.logger.error(f"Failed to create checkpoint: {e}")
            raise e

    def create_ann_index(self, checkpoint_id: str, transformation_keys: list[str]) -> bool:
        """Create an ANN index for the checkpoint and wait for completion."""
        self.logger.info(f"Creating ANN index for checkpoint {checkpoint_id}")

        # Step 1: Trigger ANN index creation
        with grpc_args_parser.create_client(
            self.args,
            GrpcWorkingSetClient.create_for_endpoint,
            default_service_name="working-set-svc",
            default_endpoint="working-set-svc:50051",
        ) as client:
            try:
                self.logger.info(f"Triggering ANN index creation for {len(transformation_keys)} transformation keys")

                for transformation_key in transformation_keys:
                    self.logger.info(f"Creating ANN index for checkpoint {checkpoint_id} with transformation key {transformation_key}")
                    client.create_ann_index_for_checkpoint(
                        checkpoint_id=checkpoint_id,
                        transformation_key=transformation_key,
                        request_context=self.request_context,
                        timeout=30.0
                    )
                self.logger.info(f"Successfully triggered ANN index creation for checkpoint {checkpoint_id}")

            except Exception as e:
                self.logger.error(f"Failed to create ANN index for {checkpoint_id}: {e}")
                raise e

        # Step 2: Poll for completion
        self.logger.info(f"Waiting for ANN index completion for checkpoint {checkpoint_id}")
        success = self.poll_ann_index_completion(checkpoint_id, transformation_keys)

        if success:
            self.logger.info(f"ANN indexing completed successfully for checkpoint {checkpoint_id}")
        else:
            self.logger.warning(f"ANN indexing did not complete within timeout for checkpoint {checkpoint_id}")

        return success

    def get_best_ann_index(self, checkpoint_id: str, transformation_key: str) -> bool:
        """Check if ANN index is available for a specific transformation key.

        Args:
            checkpoint_id: The checkpoint ID to check
            transformation_key: The transformation key to check

        Returns:
            True if ANN index is available, False otherwise
        """
        with grpc_args_parser.create_client(
            self.args,
            ContentManagerClient.create_for_endpoint,
            default_service_name="content-manager-svc",
            default_endpoint="content-manager-svc:50051",
        ) as content_manager_client:
            try:
                response = content_manager_client.get_best_ann_index(
                    transformation_key=transformation_key,
                    checkpoint_id=checkpoint_id,
                    request_context=self.request_context,
                    timeout=30.0
                )

                if response and response.index_id:
                    self.logger.debug(
                        f"ANN index found for checkpoint {checkpoint_id}, "
                        f"transformation key {transformation_key}: {response.index_id}"
                    )
                    return True
                else:
                    return False

            except grpc.RpcError as e:
                if e.code() == grpc.StatusCode.NOT_FOUND: # type: ignore
                    # This is expected when indexing is not complete yet
                    self.logger.debug(
                        f"ANN index not yet available for checkpoint {checkpoint_id}, "
                        f"transformation key {transformation_key}"
                    )
                    return False
                else:
                    # Unexpected error - log and continue
                    self.logger.warning(
                        f"Unexpected error checking ANN index for checkpoint {checkpoint_id}, "
                        f"transformation key {transformation_key}: {e}"
                    )
                    return False
            except Exception as e:
                # Other unexpected errors
                self.logger.warning(
                    f"Error checking ANN index for checkpoint {checkpoint_id}, "
                    f"transformation key {transformation_key}: {e}"
                )
                return False

    def poll_ann_index_completion(self, checkpoint_id: str, transformation_keys: list[str]) -> bool:
        """Poll for ANN index completion for all transformation keys.

        Uses exponential backoff: polls at 10s, 20s, 40s, 80s, etc., doubling each time.
        Performs 10 polling attempts total.

        Args:
            checkpoint_id: The checkpoint ID to check
            transformation_keys: List of transformation keys to check

        Returns:
            True if all ANN indexes are complete, False if max attempts reached
        """
        max_attempts = 10
        initial_poll_interval = 10  # Start with 10 seconds
        start_time = time.time()

        self.logger.info(
            f"Polling for ANN index completion for checkpoint {checkpoint_id} "
            f"with {len(transformation_keys)} transformation keys"
        )
        self.logger.info(
            f"Will poll up to {max_attempts} times with exponential backoff "
            f"starting at {initial_poll_interval} seconds"
        )

        # Initialize variables outside the loop to avoid unbound variable issues
        completed_keys = []
        pending_keys = []
        current_poll_interval = initial_poll_interval

        for attempt in range(max_attempts):
            completed_keys = []
            pending_keys = []

            for transformation_key in transformation_keys:
                if self.get_best_ann_index(checkpoint_id, transformation_key):
                    completed_keys.append(transformation_key)
                else:
                    pending_keys.append(transformation_key)

            # Check if all indexes are complete
            if len(completed_keys) == len(transformation_keys):
                elapsed_time = time.time() - start_time
                self.logger.info(
                    f"All ANN indexes completed for checkpoint {checkpoint_id} "
                    f"after {elapsed_time:.1f} seconds"
                )
                return True

            # Log progress
            elapsed_time = time.time() - start_time
            self.logger.info(
                f"ANN indexing progress for checkpoint {checkpoint_id}: "
                f"{len(completed_keys)}/{len(transformation_keys)} complete "
                f"after {elapsed_time:.1f}s (attempt {attempt + 1}/{max_attempts})"
            )
            if pending_keys:
                self.logger.info(f"Still waiting for transformation keys: {pending_keys}")

            # Wait before next poll (skip sleep on last attempt)
            if attempt < max_attempts - 1:
                self.logger.info(f"Waiting {current_poll_interval} seconds before next poll...")
                time.sleep(current_poll_interval)
                # Double the poll interval for next attempt (exponential backoff)
                current_poll_interval *= 2

        # Max attempts reached
        elapsed_time = time.time() - start_time
        self.logger.warning(
            f"Max attempts ({max_attempts}) reached after {elapsed_time:.1f} seconds. "
            f"ANN indexing incomplete for checkpoint {checkpoint_id}"
        )
        self.logger.warning(
            f"Completed: {len(completed_keys)}/{len(transformation_keys)} transformation keys"
        )
        return False


    def run_synthetic_dataset_generation(self):
        """Main method to run the complete synthetic dataset generation process."""
        self.logger.info("Starting synthetic dataset generation...")

        # Create temporary directory
        root_dir = Path(self.config['storage']['temp_dir'])
        root_dir.mkdir(parents=True, exist_ok=True)

        try:
            # Step 1: Download repository
            self.logger.info("Step 1: Downloading Github repository...")
            for i in range(self.config['repository']['copies']):
                output_dir = root_dir / f"repo_{i}"
                self.download_repository(output_dir)

            # Step 2: Initial upload with checkpoints every N files
            self.logger.info(
                "Step 2: Processing repository files and uploading blobs with periodic checkpoints..."
            )
            # Get all text files from the repository
            text_files = list(self.iterate_text_files(root_dir))
            self.logger.info(f"Found {len(text_files)} files to process for initial upload")

            initial_checkpoint_ids = self.process_files(
                previous_checkpoint_id=None,
                root_dir=root_dir,
                text_files=text_files,
                mutate=False,
                index=True  # Enable indexing for the initial checkpoint
            )
            self.logger.info(f"Initial checkpoints created: {initial_checkpoint_ids}")

            # Step 3: Generate additional synthetic checkpoints with mutations
            self.logger.info("Step 3: Generating additional synthetic checkpoints with mutations...")
            initial_checkpoint_id = initial_checkpoint_ids[-1] if initial_checkpoint_ids else None

            if initial_checkpoint_id is None:
                self.logger.error(
                    "No initial checkpoint created, cannot proceed with synthetic data generation"
                )
                return

            previous_checkpoint_id = initial_checkpoint_id
            # Generate additional synthetic checkpoints with mutations
            for i in range(self.config['synthetic_data']['num_iterations']):
                self.logger.info(f"Creating synthetic checkpoint {i + 1} with mutations...")

                # Select a subset of files to mutate
                num_to_mutate = int(
                    len(self.file_path_to_blob_name) *
                    self.config['synthetic_data']['blob_mutation_percentage']
                )
                all_files = list(self.file_path_to_blob_name.keys())
                files_to_mutate = random.sample(all_files, min(len(all_files), num_to_mutate))

                synthetic_checkpoint_ids = self.process_files(
                    previous_checkpoint_id=previous_checkpoint_id,
                    root_dir=root_dir,
                    text_files=files_to_mutate,
                    mutate=True,
                    index=True  # Enable indexing for synthetic checkpoints
                )

                if synthetic_checkpoint_ids:
                    checkpoint_id = synthetic_checkpoint_ids[-1]
                    self.logger.info(f"Synthetic checkpoint {i + 1} created with ID: {checkpoint_id}")
                    previous_checkpoint_id = checkpoint_id
                else:
                    self.logger.warning(f"Failed to create synthetic checkpoint {i + 1}")
                    break

        finally:
            # Cleanup temporary files if configured
            if not self.config['storage']['keep_temp_files']:
                self.logger.info("Cleaning up temporary files...")
                with suppress(Exception):
                    shutil.rmtree(root_dir)

def main():
    """Main entry point for the synthetic dataset generation script."""
    parser = argparse.ArgumentParser(
        description="Generate synthetic datasets for reposcale testing"
    )
    grpc_args_parser.add_endpoint_args(parser)
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file (default: config.yaml)"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Override logging level"
    )

    token_parser.add_token_args(parser)
    args = parser.parse_args()

    try:
        # Create generator instance
        generator = SyntheticDatasetGenerator(args)
        if args.log_level:
            generator.config['logging']['level'] = args.log_level
            generator._setup_logging()

        # Run the generation process
        generator.run_synthetic_dataset_generation()

    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
