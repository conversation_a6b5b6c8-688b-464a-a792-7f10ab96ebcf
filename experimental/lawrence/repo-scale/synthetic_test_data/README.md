# Synthetic Dataset Generation Script

This script generates large-scale synthetic datasets for reposcale testing by downloading Chromium source code and creating thousands of synthetic checkpoints with mutated blobs.

## Overview

The script performs the following steps:

1. **Download Repository**: Clones the specified repository the specified number of times 
2. **Process and Upload Files**: Extracts text files, processes them, and uploads as blobs via API proxy
3. **Generate Synthetic Checkpoints**: Creates checkpoints by randomly mutating subsets of blobs
4. **Register Working Sets**: Makes RegisterWorkingSet calls for each checkpoint using gRPC
5. **Create ANN Indexes**: Triggers CreateAnnIndexForCheckpoint for each checkpoint via gRPC
6. **Monitor Indexing**: Polls for indexing completion using ContentManager gRPC client

## Configuration

Edit `config.yaml` to customize:

- **Repository settings**: URL, clone depth, size limits, number of copies
- **File processing**: Batch sizes, checkpoint frequency
- **Synthetic data**: Number of checkpoints, mutation parameters, blob mutation percentage
- **API Proxy settings**: Endpoint, authentication token, timeouts
- **Working set**: Endpoint, transformation keys, indexing wait times
- **Storage**: Temporary directory settings, cleanup options

## Prerequisites

1. **Bazel Environment**: This script is designed to run with Bazel and requires Augment dependencies
2. **Authentication**: Ensure you have a valid API token for gRPC services
3. **gRPC Services**: Access to working-set-svc and content-manager-svc endpoints
4. **Disk Space**: Ensure sufficient space for Chromium repository (~several GB)

## Usage

### Basic Usage Against Dev Environment
```bash
bazel run //experimental/lawrence/repo-scale/synthetic_test_data:generate_synthetic_data -- --config config.yaml --namespace "dev-$USER" --cloud GCP_US_CENTRAL1_DEV --tenant-name augment
```
