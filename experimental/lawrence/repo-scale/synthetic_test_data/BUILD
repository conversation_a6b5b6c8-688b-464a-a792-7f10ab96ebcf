load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "generate_synthetic_data",
    srcs = ["generate_synthetic_data.py"],
    data = [
        "config.yaml",
    ],
    main = "generate_synthetic_data.py",
    visibility = ["//visibility:public"],
    deps = [
        "//base/blob_names:blob_names_py_proto",
        "//base/datasets:replay_utils",
        "//services/content_manager/client",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
        "//services/lib/request_context:request_context_py",
        "//services/working_set/client:client_py",
        requirement("pyyaml"),
        requirement("grpcio"),
    ],
)
