# Synthetic Dataset Generation Configuration

# Repository Configuration
repository:
  url: "https://github.com/chromium/chromium.git"
  name: "chromium"
  clone_depth: 1  # Limit git history depth
  copies: 1

# File Processing Configuration
file_processing:
  batch_size: 100  # Reduced from 100 to avoid SSL timeout issues
  max_file_size: 10240  # 10KB
  checkpoint_frequency: 1000  # Create checkpoint every N files during initial upload
  transformation_keys:
    - "dr-methanol-0416-4-1024char"
    - "dr-starethanol6-16-1-proj512-30line-1024char-v3"

# Synthetic Dataset Configuration
synthetic_data:
  num_iterations: 5
  blob_mutation_percentage: 0.1  # 10% of blobs mutated per checkpoint
  mutation_size: 100  # Range of random data size appended in bytes
  
# API Configuration
api:
  endpoint: "https://dev-lawrence.us-central.api.augmentcode.com"  # Default dev endpoint
  token_path: "~/.config/augment/api_token"
  timeout: 300  # Increased from 60 to 300 seconds for large uploads

# Storage Configuration
storage:
  temp_dir: "/tmp/synthetic_dataset"
  keep_temp_files: false
  blob_cache_size: 10000

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
