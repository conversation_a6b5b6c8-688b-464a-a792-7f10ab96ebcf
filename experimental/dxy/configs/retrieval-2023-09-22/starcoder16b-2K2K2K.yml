# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-2023-09-22/starcoder16b-2K2K2K.yml
#

system:
  name: basic
  model:
    name: starcoderbase_16b
    prompt:
      max_prefix_tokens: 2048
      max_suffix_tokens: 2048
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 5864
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0

task:
  name: hydra
  dataset: repoeval_functions

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: 1xA100.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra - RepoEval Functions, StarCoder16B - 2K2K2K, Basic, No Retrieval
  workspace: Dev
  project: <PERSON><PERSON><PERSON>-<PERSON>
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
