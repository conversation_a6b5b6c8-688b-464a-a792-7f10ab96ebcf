# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/retrieval-2023-09-22/rogue-bm25-scopechunk.yml
#

system:
  name: basic_rag
  model:
    checkpoint_path: rogue/diffb1m_16b_alphal_fimv2
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_suffix_tokens: 1024
      max_retrieved_chunk_tokens: -1
      max_prompt_tokens: 3816
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  retriever:
    name: bm25
    chunker:
      name: scope_aware
      max_lines_per_chunk: 40
      parse_errored_root: true
    query_formatter:
      name: simple_query
      max_lines: 20
    max_query_lines: 20
  experimental:
    retriever_top_k: 256
    trim_on_dedent: false
    trim_on_max_lines: null
    remove_suffix: false

task:
  name: hydra
  dataset: repoeval_functions

# Podspec - set the default podspec for all checkpoints
# See gpt-neox/jobs/templates/podspecs/ for additional options
# Use the following for small models (<=2B)
# podspec: gpu-small.yaml
# Use the following for larger models (>=2B)
podspec: A40.yaml

# Determined
# name, workspace, project control location and display in the determined UI.
#
# IF YOU DO NOT WANT TO EXECUTE CODE:
# To disable execution through hydra, choose the batch-eval.yaml below

determined:
  name: Hydra - RepoEval Functions, Rogue - 1K1K4K, Basic, BM25 + ScopeAware Chunker
  workspace: Dev
  project: Xuanyi-Dong
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
