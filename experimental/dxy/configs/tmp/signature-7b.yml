# Launch Commands:
# - python research/eval/eval.py --v2 experimental/dxy/configs/tmp/signature-7b.yml
# - python research/eval/harness/launch_harness.py experimental/dxy/configs/tmp/signature-7b.yml --output /mnt/efs/augment/user/dxy/results-2023-10 --prefix signature-7b

system:
    name: signature_sys
    model:
      name: starcoder_fastforward
      model_path: sig-fim/signature-v1.3-starcoder7B-1000K_python
    generation_options:
      max_generated_tokens: 200 # max tokens in actual output (excluding query tokens)
    sig_prompt_formatter:
      max_total_tks: 4400 # 3800 ctx tokens
      min_suffix_tks: 1000
      min_prefix_tks: 2000
      max_middle_tks: 600 # max internally reserved tokens (including query tokens)
    fim_mode: evaluation
    print_prompt: True

task:
  name: api
  dataset: finegrained-python.large


podspec: 1xA100.yaml

determined:
  name: Hydra - Finegrained-Python, signature-v1.3-starcoder7B-1000K_python
  workspace: Dev
  project: Eval
  # relative to research/gpt-neox
  # metaconfig: jobs/templates/batch-eval.yaml
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
