determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - StarcoderBase, 1024 prefix, 7912 prompt, BM25, 40-line chunk
  project: Eval
  workspace: Dev
podspec: 1xA100.yaml
systems:
- experimental:
    trim_trailing_newline_on_prefix: true
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    name: starcoderbase_7b
    prompt:
      max_prefix_tokens: 1024
      max_prompt_tokens: 7912
      max_suffix_tokens: 0
      retrieval_layout_style: comment
  name: basic_rag
  retriever:
    chunker: line_level
    max_chunk: 40
    name: null
tasks:
- exec: true
  name: hydra
