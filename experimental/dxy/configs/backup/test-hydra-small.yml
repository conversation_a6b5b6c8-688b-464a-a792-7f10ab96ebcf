# python research/eval/harness/launch_harness.py experimental/dxy/configs/backup/test-hydra-small.yml --output ~/cache/results/ --prefix test-small-x

system:
  name: basic_rag
  experimental:
    remove_suffix: false
    retriever_top_k: 256
    trim_on_dedent: false
    trim_on_max_lines: null
  generation_options:
    max_generated_tokens: 280
    temperature: 0
    top_k: 0
    top_p: 0
  model:
    checkpoint_path: /mnt/efs/augment/checkpoints/rogue/diffb1m_7b_alphal_fixtoken
    name: rogue
    prompt:
      max_prefix_tokens: 1024
      max_prompt_tokens: 3816
      max_suffix_tokens: 1024
  retriever:
    chunker: line_level
    max_chunk: 40
    max_query_lines: 20
    name: bm25

task:
  name: hydra
  dataset: finegrained-python.small
