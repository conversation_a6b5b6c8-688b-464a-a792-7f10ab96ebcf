{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ids = tokenizer.tokenize_unsafe(\"ý\")\n", "print(ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ids = tokenizer.tokenize_unsafe(\"董宣毅\")\n", "print(ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# \"董\" -> b'\\xe8\\x91\\xa3' (UTF-8)\n", "\n", "# b'\\xe8\\x91\\xa3' -> 14557 (\"èĳ\") (DS-Vocab)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core.llama_prompt_formatters import DeepSeekCoderInstructTokenizer\n", "\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.de<PERSON><PERSON><PERSON>([1601]))\n", "print(tokenizer.tokenize(\"Ãº\"))\n", "print(tokenizer.detokenize(tokenizer.tokenize(\"Ãº\")) == \"Ãº\")\n", "print(len(tokenizer.vocab))\n", "print(len(set(tokenizer.vocab.keys())))\n", "print(len(set(tokenizer.vocab.values())))\n", "inv_vocab = {v: k for k, v in tokenizer.vocab.items()}\n", "print(inv_vocab[1601])\n", "print(tokenizer.tokenize(inv_vocab[1601]))\n", "print(tokenizer.tokenize(tokenizer.detok<PERSON>ze([1601])))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.tokenize(\"0 0\"))\n", "print(tokenizer.tokenize(\"0 0 0 0 0 0\"))\n", "print(tokenizer.tokenize(\"how are u?\"))\n", "print(tokenizer.tokenize(\"fail to compile the code\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tokens = tokenizer.tokenize(\"void quicksort(std::vector<int> & i)\")\n", "# print(tokens)\n", "# for token in tokens:\n", "#     print(f\"token_id={token}, token={tokenizer.detokenize([token])}\")\n", "print(tokenizer.detokenize([0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.tokenize(\"<|EOT|>\"))\n", "print(tokenizer.tokenize(\"<｜end▁of▁sentence｜>\"))\n", "print(tokenizer.tokenize(\"9\"))\n", "print(tokenizer.tokenize(\"99\"))\n", "print(tokenizer.tokenize(\"99000\"))\n", "print(tokenizer.tokenize(\"0 0\"))\n", "print(tokenizer.tokenize(\"<｜fim▁begin｜>\"))\n", "print(tokenizer.tokenize(\"<｜fim▁hole｜>\"))\n", "print(tokenizer.tokenize(\"<｜fim▁end｜>\"))\n", "print(tokenizer.tokenize(\"I love you\"))\n", "print(tokenizer.tokenize(\"      print()\"))\n", "print(tokenizer.tokenize(\" \"))\n", "print(tokenizer.tokenize(\"  \"))\n", "print(tokenizer.tokenize(\"   \"))\n", "print(tokenizer.tokenize(\"<|far_suffix|>\"))\n", "print(tokenizer.vocab_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for index in range(24):\n", "    token = 31999 + index\n", "    print(f\"token={token} {tokenizer.detokenize([token])}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "\n", "tokenizer_v2 = AutoTokenizer.from_pretrained(\n", "    \"deepseek-ai/deepseek-coder-6.7b-base\", trust_remote_code=True\n", ")\n", "print(tokenizer_v2.encode(\"\\t0 0\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()\n", "print(tokenizer.tokenize_unsafe(\"0\"))\n", "print(tokenizer.tokenize_unsafe(\"0 \"))\n", "print(tokenizer.tokenize_unsafe(\"0 0\"))\n", "print(tokenizer.tokenize_unsafe(\"0 0 0\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.tokenize_safe(\"<|far_suffix|>\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(tokenizer._raw_basic_vocab))\n", "print(len(tokenizer._raw_special_tokens))\n", "print(len(tokenizer.vocab))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(max(tokenizer._raw_basic_vocab.values()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers import data_gym\n", "\n", "decoder = data_gym.DataGymDecoder()\n", "decode_data_gym = decoder.decode\n", "x1 = decode_data_gym(\"ý\")\n", "print(x1)\n", "x2 = decode_data_gym(\"Ãº\")\n", "print(x2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer._raw_basic_vocab[b\"\\xfd\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for token, id in tokenizer._raw_special_tokens.items():\n", "    cur_bytes = token.encode()\n", "    if cur_bytes in tokenizer._raw_basic_vocab:\n", "        print(\"-\" * 32)\n", "        print(f\"id={id}\")\n", "        print(f\"{decode_data_gym(token)} vs. {token.encode()}\")\n", "        print(\n", "            f\"The token {token} is already in the vocab as {tokenizer._raw_basic_vocab[cur_bytes]}. \"\n", "        )\n", "    print(f\"{token} {id}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer._raw_basic_vocab"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.de<PERSON><PERSON><PERSON>([1601]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.yuri.edits.systems.droid import ModelInput, DroidFormatter\n", "\n", "prefix = \"import pathlib\\n\"\n", "selected_code = r\"\"\"file = pathlib.Path(\"foo\")\n", "for x in file.open():\n", "\"\"\"\n", "suffix = \"    print(x)\\n\"\n", "instruction = \"fix bugs\"\n", "\n", "formatter = DroidFormatter()\n", "text, _ = formatter.prepare_prompt_text(\n", "    model_input=ModelInput(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        extra={\n", "            \"instruction\": instruction,\n", "            \"selected_code\": selected_code,\n", "        },\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}