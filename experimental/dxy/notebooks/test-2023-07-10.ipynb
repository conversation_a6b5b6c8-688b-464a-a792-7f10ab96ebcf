{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.models.core import StarCoderBase\n", "from research.models.core import StopCriteria, GenerationOptions\n", "from research.core.model_input import ModelInput\n", "\n", "\n", "sample = \"\"\"\n", "'''Read a file and print its contents.'''\n", "\n", "def main():\n", "<FILL-HERE>\n", "\n", "if __name__ == '__main__':\n", "    main()\n", "\"\"\"\n", "\n", "if \"<FILL-HERE>\" in sample:\n", "    prefix, suffix = sample.split(\"<FILL-HERE>\")\n", "else:\n", "    prefix = sample\n", "    suffix = \"\"\n", "\n", "model = StarCoderBase(checkpoint_path=pathlib.Path('/home/<USER>/checkpoints/starcoderbase_neox'))\n", "model.load()\n", "print(model)\n", "\n", "stop_criteria = StopCriteria(stop_texts=[\"for\"], check_stopping_condition_every=1)\n", "options = GenerationOptions(max_generated_tokens=512, stop_criteria=stop_criteria)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = model.generate(ModelInput(prefix, suffix), options)\n", "print(\"\\nresult:\")\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokens = model.tokenizer.tokenize(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.tokenizer.tokenize('for') in tokens"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}