{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["import os\n", "import collections\n", "import pathlib\n", "from typing import cast\n", "from research.models.core import Remote_Model\n", "from research.core.model_input import ModelInput\n", "from research.models.core import StopCriteria, GenerationOptions\n", "\n", "models = collections.OrderedDict()\n", "models[\"StarCoder Base\"] = Remote_Model(\"http://216.153.49.205:5001\")\n", "models[\"StarCoder Plus\"] = Remote_Model(\"http://216.153.49.205:5002\")\n", "models[\"StarCoder Ours\"] = Remote_Model(\"http://216.153.49.205:5004\")\n", "models[\"llama2_13b\"] = Remote_Model(\"http://216.153.49.205:5003\")\n", "# models[\"llama2_70b\"] = Remote_Model(\"http://216.153.49.205:5003\")\n", "base_keys = [\"StarCoder Base\", \"StarCoder Plus\", \"StarCoder Ours\"]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/go/structure_2.go\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/infer_preconditions.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/remove_duplicates.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_4.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_multi_function_2.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_class_1.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_class_2.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_3.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_4.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_5.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_script.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_trailing_return_with_var.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_multi_function_1.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_noop.py\n", "In total, there are 41 tests and skipped 14 tests.\n", "There are 6 with cpp extension.\n", "There are 3 with go extension.\n", "There are 18 with py extension.\n", "Loaded 18 tests.\n", "[00] [#char:   4] fim_leading_spaces.py               {'tags': [], 'max_generated_tokens': 128}\n", "[01] [#char:  57] fim_trailing_return.py              {'tags': [], 'max_generated_tokens': 128}\n", "[02] [#char: 284] chunk_request.py                    {'tags': ['arithmetic', 'instruction-following']}\n", "[03] [#char:  19] fim_argparse.py                     {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[04] [#char: 245] fim_dataclass_attrs.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[05] [#char:  14] fim_dataclass_field.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[06] [#char:  92] fim_dataclass_methods.py            {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[07] [#char:  49] fim_docstring_middle_fix.py         {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[08] [#char:  70] fim_multi_node_missing.py           {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[09] [#char: 108] resources_1.py                      {'tags': ['resource-usage']}\n", "[10] [#char:  70] structure_1.py                      {'tags': ['structs', 'infer-usage']}\n", "[11] [#char: 136] structure_2.py                      {'tags': ['structs', 'algorithm', 'recursion']}\n", "[12] [#char: 152] structure_3.py                      {'tags': ['struct', 'algorithm', 'time']}\n", "[13] [#char:  70] structure_5.py                      {'tags': ['structs', 'infer-usage']}\n", "[14] [#char:  22] structure_6.py                      {'tags': ['struct', 'infill', 'string-formation']}\n", "[15] [#char:  86] structure_7.py                      {'tags': ['struct', 'string-formation']}\n", "[16] [#char:  87] testing_1.py                        {'tags': ['testing', 'test-setup']}\n", "[17] [#char: 118] testing_2.py                        {'tags': ['testing', 'test-data']}\n"]}], "source": ["from experimental.dxy.code.few_shot_helper import FewshotTemplate, dict_by_any\n", "from experimental.dxy.code.vulcan_helper import display_output\n", "from experimental.dxy.code.vulcan_helper import load_tests\n", "from experimental.dxy.code.vulcan_helper import VulcanTest\n", "\n", "\n", "def run_norm(\n", "    model_key: str, test: VulcanTest, temperature=0.0, display: bool = False\n", ") -> str:\n", "    input = ModelInput(prefix=test.prefix, suffix=test.suffix)\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    result = models[model_key].generate(input, options)\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=f\"{model_key}, t={temperature:.1f}, max_tokens={max_generated_tokens}\",\n", "        )\n", "    return result\n", "\n", "\n", "def convert_v0(x: VulcanTest, separator: str = \"<FILL-IN-THE-MIDDLE>\"):\n", "    return {\n", "        \"code\": f\"{x.prefix}{separator}{x.suffix}\",\n", "        \"Content to replace <FILL-IN-THE-MIDDLE>\": x.expected,\n", "    }\n", "\n", "\n", "template_v0 = FewshotTemplate(\n", "    input_keys=[\"code\"],\n", "    output_keys=[\"Content to replace <FILL-IN-THE-MIDDLE>\"],\n", "    ex_sep=\"\\n\",\n", "    transfer_fn=convert_v0,\n", "    task_prefix=(\n", "        \"Given a piece of code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>.\"\n", "        \" Please help write a concise, brief and smart completion for its potential middle content.\"\n", "        \" Note that the middle might be empty.\\n\"\n", "    ),\n", ")\n", "template = template_v0\n", "# template = FewshotTemplate(\n", "#     input_keys=[\"prefix\", \"suffix\"],\n", "#     output_keys=[\"expected\"],\n", "#     key_rename_map={\"expected\": \"Expected Middle Content\"},\n", "#     ex_sep=\"\\n\",\n", "#     task_prefix=(\n", "#         \"Given the prefix and the suffix of a code file, please\"\n", "#         \" help write a concise, brief and smart completion for its potential middle content.\"\n", "#         \" Note that the middle might be empty.\"\n", "#     ),\n", "# )\n", "\n", "\n", "def run_fewshot(\n", "    model_key: str,\n", "    test: VulcanTest,\n", "    examples: list[VulcanTest],\n", "    temperature=0.0,\n", "    display: bool = False,\n", "    debug: bool = False,\n", "):\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    prompt = template.get_prompt(test, examples)\n", "    if debug:\n", "        print(f\"Prompt:\\n{prompt}\")\n", "    input = ModelInput(prefix=prompt)\n", "    raw_result = models[model_key].generate(input, options)\n", "    result = template.parse_response(raw_result)\n", "    result = list(result.values())[0]\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=f\"{model_key}, t={temperature:.1f}, max_tokens={max_generated_tokens}\",\n", "        )\n", "    return raw_result, result\n", "\n", "\n", "# NOTE: Set test_pattern below to load a specific test.\n", "TESTS = load_tests(ignore_empty_suffix=True, target_ext=\"py\")\n", "print(f\"Loaded {len(TESTS)} tests.\")\n", "for idx, test in enumerate(TESTS):\n", "    print(\n", "        f\"[{idx:02d}] [#char: {len(test.expected):3d}] {os.path.basename(test.filename):35s} {test.meta}\"\n", "    )"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "def run_all_models(index: int, select: int = 2, ignore_base: bool = False):\n", "    test = TESTS[index]\n", "    display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "    for key in base_keys:\n", "        if not ignore_base:\n", "            _ = run_norm(key, test, display=True)\n", "    total = len(TESTS)\n", "    selected_indexes = random.sample([i for i in range(total) if i != index], select)\n", "    selected_tests = [TESTS[idx] for idx in selected_indexes]\n", "    _ = run_fewshot(\"llama2_13b\", test, selected_tests, display=True, debug=False)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \u001b[0m\u001b[40m\u001b[31mresults = [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\u001b[32m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \u001b[0m\u001b[40m\u001b[31mresults = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[32m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \u001b[0m\u001b[40m\u001b[31mresults = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2!= 0:\n", "            results.append(i)\u001b[0m\u001b[32m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \u001b[0m\u001b[40m\u001b[31m\"\"\"Returns a list of odd integers between lower and upper, inclusive.\"\"\"\n", "    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2!= 0:\n", "            results.append(i)\u001b[0m\u001b[32m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_trailing_return.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \u001b[0m\u001b[40m\u001b[31mresults = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[32m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(1)"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: chunk_request.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_count': chunk_count,\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | llama2_13b, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31mfor chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\n", "    return requests\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(2)"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_argparse.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m    default=False,\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31mhelp=\"whether to return single-line completions\",\n", ")\n", "\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(3)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"Where the data starts in the source.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"Where the data ends in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data that is not used by the core.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index of the first character in the datum.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index of the last character in the datum.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        if self.char_end < self.char_start:\n", "            raise ValueError(\n", "                f\"Datum.char_end ({self.char_end}) must be greater than or equal to Datum.char_start ({self.char_start}).\"\n", "            )\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"Where the data starts in the source.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"Where the data ends in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data that can be used by the renderer.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31mchar_end: int = 0\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(4)"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m, compare=True\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\n", "@dataclass\n", "class Token:\n", "    \"\"\"Represents a token.\"\"\"\n", "\n", "    text: str = \"\"\n", "    \"\"\"The token text.\"\"\"\n", "    start: int = 0\n", "    \"\"\"Character offset for the token.\"\"\"\n", "    end: int = 0\n", "    \"\"\"Character offset for the token.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m, compare=False\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31mdef __post_init__(self):\n", "        self.char_end = len(self.source)\n", "\n", "    def __repr__(self):\n", "        return f\"Datum(source='{self.source}', char_start={self.char_start}, char_end={self.char_end}, _extra={self._extra})\"\n", "\n", "    def __str__(self):\n", "        return self.source[self.char_start:self.char_end]\n", "\n", "    def __len__(self):\n", "        return self.char_end - self.char_start\n", "\n", "    def __getitem__(self, index):\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(5, ignore_base=False, select=3)"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\n", "    def __str__(self):\n", "        return f\"Patch(file_content={self.file_content}, char_start={self.char_start}, char_end={self.char_end}, patch_content={self.patch_content})\"\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31mresult.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\n", "    def __repr__(self):\n", "        return f\"Patch(file_content='{self.file_content}', char_start={self.char_start}, char_end={self.char-end}, patch_content='{self.patch_content}', patch_id='{self.patch_id}')\"\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(6, ignore_base=False, select=3)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_docstring_middle_fix.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"Get \u001b[0m\u001b[40m\u001b[31mGet the odd integers between `lower` and `upper`.\u001b[0m\u001b[32m and `upper`.\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring_middle_fix.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"Get \u001b[0m\u001b[40m\u001b[31ma list of odd integers between `lower`\u001b[0m\u001b[32m and `upper`.\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring_middle_fix.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"Get \u001b[0m\u001b[40m\u001b[31ma list of odd integers between `lower`\u001b[0m\u001b[32m and `upper`.\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring_middle_fix.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"Get \u001b[0m\u001b[40m\u001b[31ma list of odd integers between `lower`\u001b[0m\u001b[32m and `upper`.\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_docstring_middle_fix.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mdef get_odd_ints(lower, upper):\n", "    \"\"\"Get \u001b[0m\u001b[40m\u001b[31mreturn [i for i in range(lower, upper) if i % 2 == 1]\u001b[0m\u001b[32m and `upper`.\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["run_all_models(7, ignore_base=False, select=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xmodel = models[\"llama2_70b\"]\n", "\n", "# input = ModelInput(\n", "#     prefix=\"def hello_world\", suffix=\"if __name__ == '__main__':\\n    hello_world()\"\n", "# )\n", "# option = GenerationOptions(temperature=0.0, max_generated_tokens=256)\n", "\n", "# for key, model in models.items():\n", "#     if key.startswith('llama2'):\n", "#         xinput = input.rebind(suffix='')\n", "#     else:\n", "#         xinput = input\n", "#     result = model.generate(input, option)\n", "#     print(result)\n", "#     print('----')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}