{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.31.0\n", "LLAMA2Pretrain70B_HuggingFace(\n", "  _abc_impl = <_abc._abc_data object at 0x7fa721ad1680>,\n", "  checkpoint_path = PosixPath('/home/<USER>/checkpoints/llama-2-70b-hf'),\n", "  prompt_formatter = BasicPromptFormatter(\n", "    _max_prompt_tokens = 9223372036854775807,\n", "    preamble = '',\n", "    tokenizer = <megatron.tokenizer.tokenizer.CodeGenTokenizer object at 0x7fa721aa2370>))\n", "huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bba7d885b7674456a59428a91910e1b8", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/15 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import pathlib\n", "os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'\n", "import transformers\n", "from research.models.core import get_model\n", "\n", "print(transformers.__version__)\n", "# ckp_dir = pathlib.Path(\"/mnt/efs/augment/checkpoints/llama/llama-2-13b-hf\")\n", "\n", "ckp_dir = pathlib.Path(\"/home/<USER>/checkpoints/llama-2-70b-hf\")\n", "model = get_model(\"llama2-70b-pretrain-hf\", checkpoint_path=ckp_dir)\n", "\n", "print(model)\n", "model.load()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.models.core import StopCriteria, GenerationOptions\n", "from research.core.model_input import ModelInput\n", "\n", "prompt = r\"\"\"\n", "Try your best to coherently and concisely complete the following function\n", "The codes star by the 10 repetitive char of >.\n", "After you finish the completion, please output 10 repetitive char of < indicating finish.\n", "Here is an example:\n", ">>>>>>>>>>\n", "def hello_world():\n", "    print(\"Hello World!\")\n", "\n", "class Pen:\n", "    def __init__(self, length: float):\n", "        self._length = length\n", "<<<<<<<<<<\n", "\n", "Ok, now let's start.\n", "\n", ">>>>>>>>>>\n", "from dataclasses import dataclass\n", "\n", "@dataclass\n", "class Turtle:\n", "    '''The turtle can move forward, backward, and turn 90 degrees.'''\n", "\n", "    def __init__(\n", "\"\"\"\n", "result = model.generate(\n", "    ModelInput(prefix=prompt), GenerationOptions(temperature=0.0, max_generated_tokens=1024)\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       self,\n", "        x: float = 0,\n", "        y: float = 0,\n", "        heading: float = 0,\n", "        pen: Pen = Pen(length=1),\n", "    ):\n", "        self.x = x\n", "        self.y = y\n", "        self.heading = heading\n", "        self.pen = pen\n", "\n", "    def forward(self, distance: float) -> None:\n", "        self.x += distance * math.cos(math.radians(self.heading))\n", "        self.y += distance * math.sin(math.radians(self.heading))\n", "\n", "    def backward(self, distance: float) -> None:\n", "        self.forward(-distance)\n", "\n", "    def turn_left(self, angle: float) -> None:\n", "        self.heading -= angle\n", "\n", "    def turn_right(self, angle: float) -> None:\n", "        self.turn_left(-angle)\n", "\n", "    def draw_line(self, distance: float) -> None:\n", "        self.forward(distance)\n", "        self.pen.length += distance\n", "\n", "    def draw_square(self, side: float) -> None:\n", "        self.forward(side)\n", "        self.turn_right(90)\n", "        self.forward(side)\n", "        self.turn_right(90)\n", "        self.forward(side)\n", "        self.turn_right(90)\n", "        self.forward(side)\n", "\n", "    def draw_triangle(self, side: float) -> None:\n", "        self.forward(side)\n", "        self.turn_right(120)\n", "        self.forward(side)\n", "        self.turn_right(120)\n", "        self.forward(side)\n", "\n", "    def draw_circle(self, radius: float) -> None:\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "        self.turn_right(90)\n", "        self.forward(radius)\n", "\n"]}], "source": ["print(result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}