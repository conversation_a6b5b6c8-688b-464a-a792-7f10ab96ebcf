{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "import requests\n", "import json\n", "import numpy as np\n", "\n", "from research.core.model_input import ModelInput\n", "from research.core.utils_for_str import show_completion\n", "from research.core.llama_prompt_formatters import CodeLlamaCompletionFormatter, CodeLlamaFIMFormatter, CodeLlamaChatFormatter\n", "from research.models.meta_model import (\n", "    ExtraGenerationOutputs,\n", "    GenerationOptions,\n", "    GenerativeLanguageModel\n", ")\n", "from research.models.remote_models import RemoteLLAMACPP"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks import HumanEval\n", "\n", "task = HumanEval()\n", "model_standard = RemoteLLAMACPP(\"http://127.0.0.1:8034\", mode=\"completion\")\n", "model_python = RemoteLLAMACPP(\"http://127.0.0.1:8035\", mode=\"completion\")\n", "model_chat = RemoteLLAMACPP(\"http://127.0.0.1:8036\", mode=\"chat\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["instruct = \"Give me a Linux command to revert my last 3 git commit\"\n", "result = model_chat.generate(ModelInput(prefix=instruct), GenerationOptions(temperature=0, max_generated_tokens=256))\n", "print(result)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["limit = 10\n", "pass_per_example = []\n", "for idx, (inputs, _) in enumerate(task):\n", "    generation = model_python.generate(inputs, GenerationOptions(temperature=0, max_generated_tokens=256))\n", "    results = task.execute(inputs, generation)\n", "    # show_completion(inputs.prefix, inputs.suffix, generation)\n", "\n", "    pass_per_example.append(results['pass'])\n", "    pass_rate = np.mean(pass_per_example, dtype=np.float32).item()*100\n", "    total_pass = np.sum(pass_per_example).item()\n", "    print(f\"\\n{idx:3d}/{len(task)}-th example: pass={pass_per_example[-1]}, pass-rate={pass_rate:.2f}%, total-passes={total_pass}.\\n\")\n", "    # if idx >= limit:\n", "    #     break"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}