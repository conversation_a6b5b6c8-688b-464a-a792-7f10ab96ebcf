"""Convert the hindsight dataset into triplet with datum, qwen_v1_1 result, qwen_v2_1 result.

python experimental/dxy/notebooks/2025-02-04/dump_results.py
"""

import json
import pathlib

import tqdm

from base.datasets.hindsight_completion import HindsightCompletionDatum
from research.core.utils_for_str import (
    get_first_n_lines,
    get_last_n_lines,
    show_completion,
)
from research.eval.harness.systems.abs_system import CompletionResult
from research.eval.harness.systems.remote_completion_system import (
    RemoteCompletionSystem,
)
from research.eval.harness.tasks.hindsight import convert_hindsight_datum_to_model_input
from research.tools.analyze_hindsight import (
    compute_metrics,
    get_datum_tags,
    get_run_request_id,
    make_df,
    read_data,
    read_dataset,
    read_records,
    read_runs,
)

# Initialize the client
system_v1 = RemoteCompletionSystem.from_yaml_config(
    {
        "client": {"url": "https://staging-shard-0.api.augmentcode.com"},
        "model_name": "qweldenv1-1-14b",
    }
)
system_v2 = RemoteCompletionSystem.from_yaml_config(
    {
        "client": {"url": "https://staging-shard-0.api.augmentcode.com"},
        "model_name": "qweldenv2-1-14b",
    }
)
system_v1.load()
system_v2.load()

# Read the dataset
records_path = "/mnt/efs/augment/data/eval/hindsight/2025-01-15-14d-v1.4/dogfood-shard/data.jsonl.zst"
id2datum = read_dataset(records_path)
request_ids = sorted(list(id2datum.keys()))
print(f"There are {len(request_ids)} request IDs in {records_path}.")


# Dump the results
save_dir = pathlib.Path("/mnt/efs/augment/user/dxy/datasets/compare-rldb-2025-0205")
assert save_dir.exists()
for request_id in tqdm.tqdm(request_ids):
    cur_save_path = save_dir / f"{request_id}.json"
    if cur_save_path.exists():
        continue
    datum = id2datum[request_id]
    model_input = convert_hindsight_datum_to_model_input(datum)
    result_v1 = system_v1.generate(model_input)
    result_v2 = system_v2.generate(model_input)
    cur_data = {
        "datum": HindsightCompletionDatum.schema().dump(datum),
        "result_v1": CompletionResult.schema().dump(result_v1),
        "result_v2": CompletionResult.schema().dump(result_v2),
    }

    cur_save_path.write_text(json.dumps(cur_data))
