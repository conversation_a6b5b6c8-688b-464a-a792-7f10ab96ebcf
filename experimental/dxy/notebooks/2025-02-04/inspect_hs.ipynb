{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This notebook is to manually inspect the SFT model vs. RLDB model over the hindsight dataset.\n", "\n", "I already dumpped the results to `/mnt/efs/augment/user/dxy/datasets/compare-rldb-2025-0205`, so that this notebook only needs to load the results and inspect them."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import random\n", "import json\n", "import pathlib\n", "import editdistance\n", "import collections\n", "from research.core.utils_for_str import (\n", "    get_first_n_lines,\n", "    get_last_n_lines,\n", "    show_completion,\n", ")\n", "from base.datasets import replay_utils\n", "from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "from research.tools.analyze_hindsight import (\n", "    make_df,\n", "    read_data,\n", "    read_records,\n", "    read_dataset,\n", "    read_runs,\n", "    compute_metrics,\n", "    get_datum_tags,\n", "    get_run_request_id,\n", ")\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.tasks.hindsight import convert_hindsight_datum_to_model_input\n", "from research.eval.harness.systems.remote_completion_system import (\n", "    RemoteCompletionSystem,\n", ")\n", "\n", "random.seed(88)\n", "\n", "\n", "def compute_edit_similarity(str1: str, str2: str) -> int:\n", "    \"\"\"Return edit similarity between two strings, normalized to [0 - 5]\"\"\"\n", "    levenstein_distance = editdistance.eval(str1, str2)\n", "    max_chars = max(len(str1), len(str2))\n", "    # Add 1 to handle empty string cases\n", "    similarity = 1 - levenstein_distance / (max_chars + 1)\n", "    return int(similarity * 5)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 6850 json files in /mnt/efs/augment/user/dxy/datasets/compare-rldb-2025-0205.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 6850/6850 [00:24<00:00, 280.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Skipped 4972 / 6850 due to identical results.\n", "similarity:  0: 918 requests\n", "similarity:  1: 232 requests\n", "similarity:  2: 259 requests\n", "similarity:  3: 229 requests\n", "similarity:  4: 240 requests\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["data_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/compare-rldb-2025-0205\")\n", "\n", "all_json_files = list(data_dir.glob(\"*.json\"))\n", "print(f\"There are {len(all_json_files)} json files in {data_dir}.\")\n", "skipped_due_to_identical = 0\n", "request2data = {}\n", "sim2data = collections.defaultdict(list)\n", "\n", "for json_file in tqdm.tqdm(all_json_files):\n", "    request_id = json_file.stem\n", "    data = json.loads(json_file.read_text())\n", "    result_v1 = data[\"result_v1\"]\n", "    result_v2 = data[\"result_v2\"]\n", "    if result_v1[\"generated_text\"] == result_v2[\"generated_text\"]:\n", "        skipped_due_to_identical += 1\n", "        continue\n", "    data = {\n", "        \"datum\": HindsightCompletionDatum.schema().load(data[\"datum\"]),\n", "        \"result_v1\": result_v1[\"generated_text\"],\n", "        \"result_v2\": result_v2[\"generated_text\"],\n", "    }\n", "    sim2data[\n", "        compute_edit_similarity(\n", "            result_v1[\"generated_text\"], result_v2[\"generated_text\"]\n", "        )\n", "    ].append(data)\n", "    request2data[request_id] = data\n", "print(\n", "    f\"Skipped {skipped_due_to_identical} / {len(all_json_files)} due to identical results.\"\n", ")\n", "for sim in sorted(list(sim2data.keys())):\n", "    print(f\"similarity: {sim:2d}: {len(sim2data[sim])} requests\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def show_comparison(\n", "    datum: HindsightCompletionDatum,\n", "    result_v1: str,\n", "    result_v2: str,\n", "):\n", "    print(\"# Hindsight: \" + \"-\" * 32)\n", "    show_completion(\n", "        datum.completion.request.prefix,\n", "        datum.completion.request.suffix,\n", "        datum.ground_truth,\n", "        max_lines=6,\n", "    )\n", "\n", "    print(\"\\n\" * 3)\n", "    print(\"# qweldenv1-1-14b: \" + \"-\" * 32)\n", "    show_completion(\n", "        datum.completion.request.prefix,\n", "        datum.completion.request.suffix,\n", "        result_v1,\n", "        max_lines=6,\n", "    )\n", "\n", "    print(\"\\n\" * 3)\n", "    print(\"# qweldenv2-1-14b: \" + \"-\" * 32)\n", "    show_completion(\n", "        datum.completion.request.prefix,\n", "        datum.completion.request.suffix,\n", "        result_v2,\n", "        max_lines=6,\n", "    )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 232 examples with similarity=1.\n"]}], "source": ["# Check examples if sim2data key == 1\n", "\n", "similarity = 1\n", "xlist = sim2data[similarity]\n", "print(f\"There are {len(xlist)} examples with {similarity=}.\")\n", "random.shuffle(xlist)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92mexport function getPrimaryButtonType(\n", "  codeblockMetadata: ICodeblockMetadata | undefined,\n", "  canTriggerSmartPaste: boolean,\n", "  targetPathAttemptedAndFailed: boolean,\n", "): CodeblockActionType {\n", "  c\u001b[0m\u001b[47m\u001b[30monsole.log(\"[getPrimaryButtonType] metadata = \", codeblockMetadata);\n", "  console.log(\"[getPrimaryButtonType] canTriggerSmartPaste = \", canTriggerSmartPaste);\n", "  console.log(\"[getPrimaryButtonType] targetPathAttemptedAndFailed = \", targetPathAttemptedAndFailed);\u001b[0m\u001b[94m\n", "  const hasCodepath = !!codeblockMetadata?.relPath;\n", "  const isFromNotebook = hasCodepath && isNotebook(codeblockMetadata?.relPath ?? \"\");\n", "  if (canTriggerSmartPaste && !isFromNotebook) {\n", "    return CodeblockActionType.smartPaste;\n", "  } else if (hasCodepath && targetPathAttemptedAndFailed) {\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92mexport function getPrimaryButtonType(\n", "  codeblockMetadata: ICodeblockMetadata | undefined,\n", "  canTriggerSmartPaste: boolean,\n", "  targetPathAttemptedAndFailed: boolean,\n", "): CodeblockActionType {\n", "  c\u001b[0m\u001b[47m\u001b[30monst isExcerpt = codeblockMetadata?.isExcerpt ?? false;\u001b[0m\u001b[94m\n", "  const hasCodepath = !!codeblockMetadata?.relPath;\n", "  const isFromNotebook = hasCodepath && isNotebook(codeblockMetadata?.relPath ?? \"\");\n", "  if (canTriggerSmartPaste && !isFromNotebook) {\n", "    return CodeblockActionType.smartPaste;\n", "  } else if (hasCodepath && targetPathAttemptedAndFailed) {\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92mexport function getPrimaryButtonType(\n", "  codeblockMetadata: ICodeblockMetadata | undefined,\n", "  canTriggerSmartPaste: boolean,\n", "  targetPathAttemptedAndFailed: boolean,\n", "): CodeblockActionType {\n", "  c\u001b[0m\u001b[47m\u001b[30monsole.log(\"getPrimaryButtonType\", codeblockMetadata, canTriggerSmartPaste, targetPathAttemptedAndFailed);\u001b[0m\u001b[94m\n", "  const hasCodepath = !!codeblockMetadata?.relPath;\n", "  const isFromNotebook = hasCodepath && isNotebook(codeblockMetadata?.relPath ?? \"\");\n", "  if (canTriggerSmartPaste && !isFromNotebook) {\n", "    return CodeblockActionType.smartPaste;\n", "  } else if (hasCodepath && targetPathAttemptedAndFailed) {\n", "\u001b[0m"]}], "source": ["index = 0\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m        tool_input = {\n", "            \"model_input\": model_input,\n", "        }\n", "        answer = self.agent.run(tool_input)\n", "        tool_call_logger = self.agent.tool_call_logger\n", "        \u001b[0m\u001b[47m\u001b[30mprint(tool_call_logger.get_string_representation())\u001b[0m\u001b[94m\n", "        extra_output = ExtraGenerationOutputs(\n", "            additional_info={\"output_state\": tool_call_logger.to_dict()},\n", "        )\n", "        chat_result = ChatResult(\n", "            generated_text=answer or \"\",\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m        tool_input = {\n", "            \"model_input\": model_input,\n", "        }\n", "        answer = self.agent.run(tool_input)\n", "        tool_call_logger = self.agent.tool_call_logger\n", "        \u001b[0m\u001b[47m\u001b[30mtool_call_logger.log_tool_call(tool_input, answer)\u001b[0m\u001b[94m\n", "        extra_output = ExtraGenerationOutputs(\n", "            additional_info={\"output_state\": tool_call_logger.to_dict()},\n", "        )\n", "        chat_result = ChatResult(\n", "            generated_text=answer or \"\",\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m        tool_input = {\n", "            \"model_input\": model_input,\n", "        }\n", "        answer = self.agent.run(tool_input)\n", "        tool_call_logger = self.agent.tool_call_logger\n", "        \u001b[0m\u001b[47m\u001b[30mif self.verbose:\n", "            logger.info(f\"Tool call logger: {tool_call_logger.to_dict()}\")\u001b[0m\u001b[94m\n", "        extra_output = ExtraGenerationOutputs(\n", "            additional_info={\"output_state\": tool_call_logger.to_dict()},\n", "        )\n", "        chat_result = ChatResult(\n", "            generated_text=answer or \"\",\n", "\u001b[0m"]}], "source": ["index = 2\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m        action=\"store_true\",\n", "        default=False,\n", "        help=\"Use the anthropic API instead of the Vertex API\",\n", "    )\n", "\n", "    parser.add_argument(\u001b[0m\u001b[47m\u001b[30m\n", "        \"--max-retries\",\n", "        type=int,\n", "        default=2,\n", "        help=\"Max retries for the chat API\",\n", "    \u001b[0m\u001b[94m)\n", "\n", "    args = parser.parse_args()\n", "\n", "    token = args.auth_token_file.read_text(\"utf8\").strip()\n", "\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m        action=\"store_true\",\n", "        default=False,\n", "        help=\"Use the anthropic API instead of the Vertex API\",\n", "    )\n", "\n", "    parser.add_argument(\u001b[0m\u001b[47m\u001b[30m\n", "        \"--notion-export-root\",\n", "        type=Path,\n", "        default=\"/mnt/efs/augment/agent/notion_export\",\n", "        help=\"Root directory for Notion exports\",\n", "    \u001b[0m\u001b[94m)\n", "\n", "    args = parser.parse_args()\n", "\n", "    token = args.auth_token_file.read_text(\"utf8\").strip()\n", "\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m        action=\"store_true\",\n", "        default=False,\n", "        help=\"Use the anthropic API instead of the Vertex API\",\n", "    )\n", "\n", "    parser.add_argument(\u001b[0m\u001b[47m\u001b[30m\n", "        \"--use-vertex-direct\",\n", "        action=\"store_true\",\n", "        default=False,\n", "        help=\"Use the Vertex API instead of the Anthropic API\",\n", "    \u001b[0m\u001b[94m)\n", "\n", "    args = parser.parse_args()\n", "\n", "    token = args.auth_token_file.read_text(\"utf8\").strip()\n", "\n", "\u001b[0m"]}], "source": ["index = 3\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m        )\n", "\n", "        current_message_str += \"As a reminder, the user query is:\\n\"\n", "        current_message_str += format_chat_input(chat_input, chat_history_len=2000)\n", "\n", "        print(dia\u001b[0m\u001b[47m\u001b[30mlog_history.get_messages_for_llm_client()\u001b[0m\u001b[94m)\n", "\n", "        dialog_history.add_user_prompt(current_message_str)\n", "        return dialog_history\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m        )\n", "\n", "        current_message_str += \"As a reminder, the user query is:\\n\"\n", "        current_message_str += format_chat_input(chat_input, chat_history_len=2000)\n", "\n", "        print(dia\u001b[0m\u001b[47m\u001b[30mlog_history.get_messages_for_llm_client()\u001b[0m\u001b[94m)\n", "\n", "        dialog_history.add_user_prompt(current_message_str)\n", "        return dialog_history\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m        )\n", "\n", "        current_message_str += \"As a reminder, the user query is:\\n\"\n", "        current_message_str += format_chat_input(chat_input, chat_history_len=2000)\n", "\n", "        print(dia\u001b[0m\u001b[47m\u001b[30mlog_history\u001b[0m\u001b[94m)\n", "\n", "        dialog_history.add_user_prompt(current_message_str)\n", "        return dialog_history\n", "\u001b[0m"]}], "source": ["index = 4\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 259 examples with similarity=2.\n"]}], "source": ["# Check examples if sim2data key == 2\n", "\n", "similarity = 2\n", "xlist = sim2data[similarity]\n", "print(f\"There are {len(xlist)} examples with {similarity=}.\")\n", "random.shuffle(xlist)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m * We return null to cause the buildStore(...args) to throw\n", " * to abort creating a derived store\n", " */\n", "function getAtomState(store: Store, atom: AnyAtom): AtomState {\n", "  const ensureAtomState = store[STORE_METHODS]![3]\n", "  return ensureAtomState(\u001b[0m\u001b[47m\u001b[30matom\u001b[0m\u001b[94m)\n", "}\n", "\n", "it('fires after recomputeDependents and before atom listeners', async function test() {\n", "  const store = createStore()\n", "  const a = atom({} as { v?: number })\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m * We return null to cause the buildStore(...args) to throw\n", " * to abort creating a derived store\n", " */\n", "function getAtomState(store: Store, atom: AnyAtom): AtomState {\n", "  const ensureAtomState = store[STORE_METHODS]![3]\n", "  return ensureAtomState(\u001b[0m\u001b[47m\u001b[30matom, store\u001b[0m\u001b[94m)\n", "}\n", "\n", "it('fires after recomputeDependents and before atom listeners', async function test() {\n", "  const store = createStore()\n", "  const a = atom({} as { v?: number })\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m * We return null to cause the buildStore(...args) to throw\n", " * to abort creating a derived store\n", " */\n", "function getAtomState(store: Store, atom: AnyAtom): AtomState {\n", "  const ensureAtomState = store[STORE_METHODS]![3]\n", "  return ensureAtomState(\u001b[0m\u001b[47m\u001b[30matom\u001b[0m\u001b[94m)\n", "}\n", "\n", "it('fires after recomputeDependents and before atom listeners', async function test() {\n", "  const store = createStore()\n", "  const a = atom({} as { v?: number })\n", "\u001b[0m"]}], "source": ["index = 2\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m    public async init(): Promise<void> {\n", "    }\n", "\n", "    public async sendSelection(): Promise<void> {\n", "        await chrome.runtime.sendMessage({\n", "            type: Ch\u001b[0m\u001b[47m\u001b[30mromeMessageType.contentScriptSelectionUpdate\u001b[0m\u001b[94m,\n", "            selection: window.getSelection()?.toString() ?? \"\"\n", "\n", "        });\n", "    }\n", "}\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m    public async init(): Promise<void> {\n", "    }\n", "\n", "    public async sendSelection(): Promise<void> {\n", "        await chrome.runtime.sendMessage({\n", "            type: Ch\u001b[0m\u001b[47m\u001b[30mromeMessageType.Selection\u001b[0m\u001b[94m,\n", "            selection: window.getSelection()?.toString() ?? \"\"\n", "\n", "        });\n", "    }\n", "}\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m    public async init(): Promise<void> {\n", "    }\n", "\n", "    public async sendSelection(): Promise<void> {\n", "        await chrome.runtime.sendMessage({\n", "            type: Ch\u001b[0m\u001b[47m\u001b[30mromeMessageType.SEND_SELECTION\u001b[0m\u001b[94m,\n", "            selection: window.getSelection()?.toString() ?? \"\"\n", "\n", "        });\n", "    }\n", "}\n", "\u001b[0m"]}], "source": ["index = 3\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 229 examples with similarity=3.\n"]}], "source": ["# Check examples if sim2data key == 3\n", "\n", "similarity = 3\n", "xlist = sim2data[similarity]\n", "print(f\"There are {len(xlist)} examples with {similarity=}.\")\n", "random.shuffle(xlist)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m    this.props.componentDidCatch?.(error, _errorInfo)\n", "  }\n", "\n", "  render(): ReactNode {\n", "    if (this.state.hasError) {\n", "      return createElement('div'\u001b[0m\u001b[47m\u001b[30m, {}, 'error'\u001b[0m\u001b[94m)\n", "    }\n", "    return this.props.children\n", "  }\n", "}\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m    this.props.componentDidCatch?.(error, _errorInfo)\n", "  }\n", "\n", "  render(): ReactNode {\n", "    if (this.state.hasError) {\n", "      return createElement('div'\u001b[0m\u001b[47m\u001b[30m, null, 'error'\u001b[0m\u001b[94m)\n", "    }\n", "    return this.props.children\n", "  }\n", "}\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m    this.props.componentDidCatch?.(error, _errorInfo)\n", "  }\n", "\n", "  render(): ReactNode {\n", "    if (this.state.hasError) {\n", "      return createElement('div'\u001b[0m\u001b[47m\u001b[30m, {}, 'error'\u001b[0m\u001b[94m)\n", "    }\n", "    return this.props.children\n", "  }\n", "}\n", "\u001b[0m"]}], "source": ["index = 4\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 240 examples with similarity=4.\n"]}], "source": ["# Check examples if sim2data key == 4\n", "\n", "similarity = 4\n", "xlist = sim2data[similarity]\n", "print(f\"There are {len(xlist)} examples with {similarity=}.\")\n", "random.shuffle(xlist)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92mUSE_SLACK_TOKEN_APPORTIONMENT = base.feature_flags.BoolFlag(\n", "    \"slackbot_use_slack_token_apportionment\", False\n", ")\n", "\n", "\n", "_default_token_apportionent = C\u001b[0m\u001b[47m\u001b[30mhatTokenApportionment\u001b[0m\u001b[94m(\n", "    chat_history_len=1024 * 6,\n", "    retrieval_len=1024 * 6,\n", "    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens\n", "    max_glean_len=1024 * 5,\n", "    max_glean_doc_len=1024,\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92mUSE_SLACK_TOKEN_APPORTIONMENT = base.feature_flags.BoolFlag(\n", "    \"slackbot_use_slack_token_apportionment\", False\n", ")\n", "\n", "\n", "_default_token_apportionent = C\u001b[0m\u001b[47m\u001b[30mhatPromptFormatter.ChatTokenApportionment\u001b[0m\u001b[94m(\n", "    chat_history_len=1024 * 6,\n", "    retrieval_len=1024 * 6,\n", "    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens\n", "    max_glean_len=1024 * 5,\n", "    max_glean_doc_len=1024,\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92mUSE_SLACK_TOKEN_APPORTIONMENT = base.feature_flags.BoolFlag(\n", "    \"slackbot_use_slack_token_apportionment\", False\n", ")\n", "\n", "\n", "_default_token_apportionent = C\u001b[0m\u001b[47m\u001b[30mhatPromptFormatter.TokenApportionment\u001b[0m\u001b[94m(\n", "    chat_history_len=1024 * 6,\n", "    retrieval_len=1024 * 6,\n", "    max_prompt_len=8192 - 2048,  # 2048 represents the max output tokens\n", "    max_glean_len=1024 * 5,\n", "    max_glean_doc_len=1024,\n", "\u001b[0m"]}], "source": ["index = 0\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m                this._logger.info(\n", "                    `Blob ${expectedBlobName} was uploaded but folder ${folderId} is not tracked. Ignoring.`\n", "                );\n", "                continue;\n", "            }\n", "            \u001b[0m\u001b[47m\u001b[30mfolder.blobStatusStore.updateBlobName(expectedBlobName, actualBlobName);\u001b[0m\u001b[94m\n", "        }\n", "    }\n", "\n", "    // TODO: Complete\n", "    _upload(\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m                this._logger.info(\n", "                    `Blob ${expectedBlobName} was uploaded but folder ${folderId} is not tracked. Ignoring.`\n", "                );\n", "                continue;\n", "            }\n", "            \u001b[0m\u001b[47m\u001b[30mfolder.blobStatusStore.updateBlobName(expectedBlobName, actualBlobName);\u001b[0m\u001b[94m\n", "        }\n", "    }\n", "\n", "    // TODO: Complete\n", "    _upload(\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m                this._logger.info(\n", "                    `Blob ${expectedBlobName} was uploaded but folder ${folderId} is not tracked. Ignoring.`\n", "                );\n", "                continue;\n", "            }\n", "            \u001b[0m\u001b[47m\u001b[30mfolder.blobStatusStore.renameBlob(expectedBlobName, actualBlobName);\u001b[0m\u001b[94m\n", "        }\n", "    }\n", "\n", "    // TODO: Complete\n", "    _upload(\n", "\u001b[0m"]}], "source": ["index = 1\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Hindsight: --------------------------------\n", "\u001b[92m            const fileExists: boolean =\n", "                await this._fuzzyFsSearcher.validatePath(maybeResolvedPathName);\n", "            if (fileExists) {\n", "                const details = pathNameToFileDetails(maybeResolvedPathName);\n", "                if (!details.fileType) {\n", "                    const stat = \u001b[0m\u001b[47m\u001b[30mawait statFile(pathNameToAbsPath(maybeResolvedPathName));\n", "                    details.fileType = stat.type;\u001b[0m\u001b[94m\n", "                }\n", "            }\n", "        }\n", "\n", "        // If for some reason we cannot find a valid candidate path,\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv1-1-14b: --------------------------------\n", "\u001b[92m            const fileExists: boolean =\n", "                await this._fuzzyFsSearcher.validatePath(maybeResolvedPathName);\n", "            if (fileExists) {\n", "                const details = pathNameToFileDetails(maybeResolvedPathName);\n", "                if (!details.fileType) {\n", "                    const stat = \u001b[0m\u001b[47m\u001b[30mawait this._fuzzyFsSearcher.stat(details.pathName);\n", "                    details.fileType = stat?.type;\u001b[0m\u001b[94m\n", "                }\n", "            }\n", "        }\n", "\n", "        // If for some reason we cannot find a valid candidate path,\n", "\u001b[0m\n", "\n", "\n", "\n", "# qweldenv2-1-14b: --------------------------------\n", "\u001b[92m            const fileExists: boolean =\n", "                await this._fuzzyFsSearcher.validatePath(maybeResolvedPathName);\n", "            if (fileExists) {\n", "                const details = pathNameToFileDetails(maybeResolvedPathName);\n", "                if (!details.fileType) {\n", "                    const stat = \u001b[0m\u001b[47m\u001b[30mawait this._fuzzyFsSearcher.stat(details.pathName);\n", "                    details.fileType = stat?.fileType;\u001b[0m\u001b[94m\n", "                }\n", "            }\n", "        }\n", "\n", "        // If for some reason we cannot find a valid candidate path,\n", "\u001b[0m"]}], "source": ["index = 7\n", "show_comparison(\n", "    xlist[index][\"datum\"], xlist[index][\"result_v1\"], xlist[index][\"result_v2\"]\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}