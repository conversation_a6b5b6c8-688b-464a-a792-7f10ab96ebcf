{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import pathlib\n", "from research.core.types import Chunk, ChunkId, Document\n", "from research.retrieval.chunking_functions import (\n", "    LineLevelChunker,\n", "    ScopeAwareChunker,\n", ")\n", "\n", "notebook_path = os.path.dirname(\n", "    os.path.realpath(get_ipython().getoutput(\"echo $PWD\")[0])\n", ")\n", "print(notebook_path)\n", "research_dir = pathlib.Path(notebook_path).parent.parent.parent / \"research\"\n", "print(research_dir)\n", "\n", "file_path = research_dir / \"core\" / \"llama_prompt_formatters.py\"\n", "doc = Document(text=file_path.read_text(), id=str(file_path), path=\"sample.py\", meta={})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["scope_aware_chunker = ScopeAwareChunker(max_lines_per_chunk=30)\n", "chunks = scope_aware_chunker.split_into_chunks(doc) or []\n", "print(f\"There are {len(chunks)} chunks.\")\n", "for chunk in chunks:\n", "    print(chunk)\n", "    print(\"\\n\" + \"-\" * 100 + \"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}