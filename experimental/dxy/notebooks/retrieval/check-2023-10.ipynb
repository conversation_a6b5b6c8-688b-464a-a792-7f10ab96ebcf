{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['inputs', 'prompt', 'completion', 'retrieved_chunks', 'category', 'sub_categories', 'metric@is-syntax-correct', 'metric@text-similarity'])\n"]}], "source": ["import pathlib\n", "from research.eval.harness.utils import read_json\n", "\n", "data = read_json(pathlib.Path(\"/mnt/efs/augment/eval/jobs/4BynX4qh/000__SignatureSystem_ApiCallTask/cache/1046-1053.json\"))\n", "print(data.keys())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}