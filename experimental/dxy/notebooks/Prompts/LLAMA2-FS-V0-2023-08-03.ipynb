{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All avaliable models: ['codegen_16b_indiana', 'codegen_16b_multi', 'codegen_2b_fim', 'codegen_2b_indiana', 'codegen_2b_multi', 'codegen_350m_multi', 'llama2_13b_pretrain_hf', 'llama2_70b_pretrain_hf', 'llama2_7b_chat_meta', 'llama2_7b_pretrain_hf', 'llama2_7b_pretrain_meta', 'null', 'openai', 'remote', 'starcoderbase', 'starcoderbase_1b', 'starcoderbase_3b', 'starcoderbase_7b', 'starcoderbase_hf', 'starcoderplus', 'starcoderplus_hf', 'starllama_200m_wide', 'starllama_350m', 'wizardcoder_hf']\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "48f67067bc3c4e4880f0e364149ea39d", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n", "import collections\n", "import pathlib\n", "from typing import cast, Optional, List\n", "from termcolor import colored\n", "from research.models.core import list_models, get_model, RemoteModel\n", "from research.core.model_input import ModelInput\n", "from research.models.core import GenerativeLanguageModel\n", "from research.models.core import StopCriteria, GenerationOptions\n", "\n", "print(f\"All avaliable models: {list_models()}\")\n", "\n", "xmodel = get_model(\n", "    \"llama2_13b_pretrain_hf\",\n", "    checkpoint_path=pathlib.Path(\"/home/<USER>/checkpoints/llama-2-13b-hf\"),\n", ")\n", "xmodel.load()\n", "\n", "# models = collections.OrderedDict()\n", "# models[\"StarCoder Base\"] = RemoteModel(\"http://216.153.49.205:5001\")\n", "# models[\"StarCoder Plus\"] = RemoteModel(\"http://216.153.49.205:5002\")\n", "# models[\"StarCoder Ours\"] = RemoteModel(\"http://216.153.49.205:5004\")\n", "# models[\"llama2_13b\"] = RemoteModel(\"http://216.153.49.205:5003\")\n", "# # models[\"llama2_70b\"] = Remote_Model(\"http://216.153.49.205:5003\")\n", "# base_keys = [\"StarCoder Base\", \"StarCoder Plus\", \"StarCoder Ours\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ignore go/structure_2.go\n", "Ignore py/infer_preconditions.py\n", "Ignore py/remove_duplicates.py\n", "Ignore py/fim_multi_function_2.py\n", "Ignore py/fim_real_class_1.py\n", "Ignore py/fim_real_class_2.py\n", "Ignore py/fim_real_ex_4.py\n", "Ignore py/fim_real_ex_5.py\n", "Ignore py/fim_script.py\n", "Ignore py/fim_trailing_return_with_var.py\n", "Ignore py/fim_demo1.py\n", "Ignore py/fim_demo2.py\n", "Ignore py/fim_real_ex_3.py\n", "Ignore py/structure_4.py\n", "Ignore py/archived/fim_leading_spaces.py\n", "Ignore py/archived/fim_multi_function_1.py\n", "Ignore py/archived/fim_noop.py\n", "Ignore py/archived/fim_trailing_return.py\n", "In total, there are 45 tests and skipped 18 tests.\n", "There are 6 with cpp extension.\n", "There are 3 with go extension.\n", "There are 18 with py extension.\n", "Loaded 18 tests.\n", "[00] [#char: 284] chunk_request.py                    {'tags': ['arithmetic', 'instruction-following'], 'label': 'imple-func-via-doc', 'CoT': 'Complete the algorithm function based on its function name and docstring.'}\n", "[01] [#char: 180] example4_research_models_generate.py {'tags': ['fim'], 'label': 'complete-func-via-ref', 'CoT': 'complete the function based on the class defination'}\n", "[02] [#char:  19] fim_argparse.py                     {'tags': ['fim'], 'label': 'argparse', 'CoT': 'The code completion should be about how to define argument with argparse.', 'max_generated_tokens': 128}\n", "[03] [#char: 245] fim_dataclass_attrs.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[04] [#char:  14] fim_dataclass_field.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[05] [#char:  92] fim_dataclass_methods.py            {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[06] [#char:  49] fim_docstring_middle_fix.py         {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[07] [#char:  70] fim_multi_node_missing.py           {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[08] [#char: 219] remove_duplicates_with_suffix.py    {'tags': ['algorithm'], 'label': 'imple-func-via-doc', 'CoT': 'Complete the algorithm function based on its function name and docstring.'}\n", "[09] [#char: 108] resources_1.py                      {'tags': ['resource-usage']}\n", "[10] [#char:  70] structure_1.py                      {'tags': ['struct', 'infer-usage'], 'label': 'complete-class-via-ref', 'CoT': 'complete the class defination based on how it is used in other functions.'}\n", "[11] [#char: 136] structure_2.py                      {'tags': ['struct', 'algorithm', 'recursion'], 'label': 'complete-class-via-ref', 'CoT': 'complete the class defination based on how it is used in other functions.'}\n", "[12] [#char: 152] structure_3.py                      {'tags': ['struct', 'algorithm', 'time']}\n", "[13] [#char:  70] structure_5.py                      {'tags': ['struct', 'infer-usage']}\n", "[14] [#char:  22] structure_6.py                      {'tags': ['struct', 'infill', 'string-formation']}\n", "[15] [#char:  86] structure_7.py                      {'tags': ['struct', 'string-formation']}\n", "[16] [#char:  87] testing_1.py                        {'tags': ['testing', 'test-setup']}\n", "[17] [#char: 118] testing_2.py                        {'tags': ['testing', 'test-data']}\n", "----------------------------------------------------------------------------------------------------\n", "imple-func-via-doc : 2 tests\n", "complete-func-via-ref : 1 tests\n", "argparse : 1 tests\n", "None : 12 tests\n", "complete-class-via-ref : 2 tests\n", "----------------------------------------------------------------------------------------------------\n", "Use the saved cache results from /home/<USER>/checkpoints/cache/prompts and baseline\n", "Find cache /home/<USER>/checkpoints/cache/prompts/chunk_request.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/example4_research_models_generate.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_argparse.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_attrs.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_field.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_methods.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_docstring_middle_fix.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_multi_node_missing.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/remove_duplicates_with_suffix.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/resources_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_2.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_3.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_5.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_6.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_7.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/testing_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/testing_2.py-baseline and loading...\n", "EXAMPLAR_TEST_ROOT: /home/<USER>/src/augment/experimental/dxy/exemplars\n", "----------------------------------------------------------------------------------------------------\n", "argparse : 1 tests\n", "complete-class-via-ref : 1 tests\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["import random\n", "from copy import deepcopy\n", "from experimental.dxy.code.few_shot_helper import FewshotTemplate, dict_by_any\n", "from experimental.dxy.code.vulcan_helper import display_output\n", "from experimental.dxy.code.vulcan_helper import display_cache\n", "from experimental.dxy.code.vulcan_helper import load_tests\n", "from experimental.dxy.code.vulcan_helper import load_examplar_tests\n", "from experimental.dxy.code.vulcan_helper import convert_test_by_label\n", "from experimental.dxy.code.vulcan_helper import VulcanTest\n", "from experimental.dxy.code.file_helper import pickle_load, pickle_save\n", "from experimental.dxy.code.str_helper import trim_result_if_meet_tail_of_prefix\n", "from experimental.dxy.code.str_helper import trim_result_if_meet_head_of_suffix\n", "\n", "\n", "CACHE_SAVE_DIR = \"/home/<USER>/checkpoints/cache/prompts\"\n", "all_tests = load_tests(cache_folder=CACHE_SAVE_DIR, cache_suffix=\"baseline\")\n", "all_examplars = load_examplar_tests()\n", "examplars_by_label = convert_test_by_label(all_examplars, all_in_none=True)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LLAMA2Pretrain13B_HuggingFace(\n", "  checkpoint_path = /home/<USER>/checkpoints/llama-2-13b-hf,\n", "  model = LlamaForCausalLM(\n", "  (model): LlamaM<PERSON>l(\n", "    (embed_tokens): Embedding(32000, 5120, padding_idx=0)\n", "    (layers): ModuleList(\n", "      (0-39): 40 x LlamaDecoderLayer(\n", "        (self_attn): LlamaAttention(\n", "          (q_proj): Linear(in_features=5120, out_features=5120, bias=False)\n", "          (k_proj): Linear(in_features=5120, out_features=5120, bias=False)\n", "          (v_proj): Linear(in_features=5120, out_features=5120, bias=False)\n", "          (o_proj): Linear(in_features=5120, out_features=5120, bias=False)\n", "          (rotary_emb): LlamaRotaryEmbedding()\n", "        )\n", "        (mlp): LlamaMLP(\n", "          (gate_proj): Linear(in_features=5120, out_features=13824, bias=False)\n", "          (up_proj): Linear(in_features=5120, out_features=13824, bias=False)\n", "          (down_proj): Linear(in_features=13824, out_features=5120, bias=False)\n", "          (act_fn): SiLUActivation()\n", "        )\n", "        (input_layernorm): LlamaRMSNorm()\n", "        (post_attention_layernorm): LlamaRMSNorm()\n", "      )\n", "    )\n", "    (norm): LlamaRMSNorm()\n", "  )\n", "  (lm_head): Linear(in_features=5120, out_features=32000, bias=False)\n", "),\n", "  _seq_length = 4096,\n", "  prompt_formatter =   BasicPromptFormatter(\n", "    preamble = ,\n", "    tokenizer = <augment.research.models.meta_hugging_face_model.HuggingFaceTokenizer object at 0x7f6e184a1cd0>,\n", "    _max_prompt_tokens = 9223372036854775807),\n", "  log_dir = None)\n"]}], "source": ["print(xmodel)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def convert_v0(x: VulcanTest, separator: str = \"<FILL-IN-THE-MIDDLE>\"):\n", "    return {\n", "        \"code\": f\"{x.prefix}{separator}{x.suffix}\",\n", "        \"Content to replace <FILL-IN-THE-MIDDLE>\": x.expected,\n", "    }\n", "\n", "\n", "template_v0 = FewshotTemplate(\n", "    input_keys=[\"code\"],\n", "    output_keys=[\"Content to replace <FILL-IN-THE-MIDDLE>\"],\n", "    ex_sep=\"\\n\",\n", "    transfer_fn=convert_v0,\n", "    task_prefix=(\n", "        \"Given a piece of Python code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>.\"\n", "        \" Please help write a concise, coherent and smart completion for its potential middle content.\"\n", "        \" Think step by step and try to finish the completion based on function signature, type annotation, docstring and other useful info in the context.\"\n", "        \" Please be cautious about the leading space or indent.\"\n", "        \" Note that the middle content might be empty.\\n\"\n", "    ),\n", ")\n", "template = template_v0\n", "\n", "def show_prompt_length(model: GenerativeLanguageModel, prompt: str):\n", "    prompt_tokens = model.tokenizer.tokenize(prompt)\n", "    print(f'The prompt #tokens: {len(prompt_tokens)}')\n", "\n", "def run_basic(\n", "    model: GenerativeLanguageModel,\n", "    prompt: str,\n", "    temperature: float = 0.0,\n", "    max_generated_tokens: int = 256,\n", "    debug: bool = True\n", ") -> str:\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    raw_results = model.generate(ModelInput(prefix=prompt), options)\n", "    return raw_results\n", "\n", "\n", "def run_single(\n", "    model: GenerativeLanguageModel,\n", "    test: VulcanTest,\n", "    examples: Optional[List[VulcanTest]]=None,\n", "    temperature: float=0.0,\n", "    max_select: int = 2,\n", "    display: bool = False,\n", "    debug: bool = False,\n", ") -> tuple[str, str, dict]:\n", "    # input = ModelInput(prefix=test.prefix, suffix=test.suffix)\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    # Find the examples\n", "    # if \"label\" in test.meta:\n", "    #     label = test.meta[\"label\"]\n", "    # else:\n", "    #     label = None\n", "    # if label is not None and (len(examplars_by_label[label]) > 0):\n", "    #     examples = examplars_by_label[label][:max_select]\n", "    # else:\n", "    if not examples:\n", "        random_selected = random.sample(all_tests, max_select + 1)\n", "        random_selected = [x for x in random_selected if x.filename != test.filename]\n", "        examples = deepcopy(random_selected[:max_select])\n", "    else:\n", "        examples = [x for x in examples if x.filename != test.filename]\n", "    selected_filenames = [os.path.basename(x.filename) for x in examples]\n", "    print(f\"Selected {len(examples)} examples: {selected_filenames}.\")\n", "    prompt = template.get_prompt(test, examples)\n", "    if debug:\n", "        print(\"-\" * 1000)\n", "        print(f\"Prompt:\\n{prompt}\")\n", "    input = ModelInput(prefix=prompt)\n", "\n", "    raw_result = model.generate(input, options)\n", "    result_v0 = template.parse_response(raw_result)\n", "    result_v1 = list(result_v0.values())[0]\n", "    result_v2 = trim_result_if_meet_tail_of_prefix(result_v1, prefix=test.prefix)\n", "    result_v3 = trim_result_if_meet_head_of_suffix(result_v2, suffix=test.suffix)\n", "    result = result_v3\n", "    extra_header = (\n", "        f\"{model.name}, t={temperature:.1f}, max_tokens={max_generated_tokens}\"\n", "    )\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=extra_header,\n", "        )\n", "    return (\n", "        result,\n", "        extra_header,\n", "        {\n", "            \"prompt\": prompt,\n", "            \"result\": result,\n", "            \"examples\": examples,\n", "            \"raw_result\": raw_result,\n", "            \"result_v0\": result_v0,\n", "            \"result_v1\": result_v1,\n", "            \"result_v2\": result_v2,\n", "            \"result_v3\": result_v3,\n", "            \"test.prefix\": test.prefix,\n", "            \"test.suffix\": test.suffix,\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: chunk_request.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_count': chunk_count,\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['testing_1.py', 'fim_dataclass_field.py'].\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | llama-2-13b-hf, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["test = all_tests[0]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(f\"{prefix} {suffix}\")\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(f'Generated completion: {suffix}')\n", "\n", "def load_model(model_name: str) -> ResearchModel:\n", "    \"\"\"Load the model with the given name.\"\"\"\n", "    if model_name == 'gpt2':\n", "        return GPT2Model()\n", "    else:\n", "        raise ValueError(f'Unknown model name: {model_name}')\n", "\n", "if __name__ == '__main__':\n", "    model = load_model('gpt2')\n", "    model.load()\n", "    generate_completion(model, 'The ')\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(suffix)\n", "\n", "def main():\n", "    \"\"\"Main entry point.\"\"\"\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument('model', type=str, help='The model to use.')\n", "    parser.add_argument('prefix', type=str, help='The prefix to use.')\n", "    args = parser.parse_args()\n", "\n", "    model = ResearchModel.load(args.model)\n", "    generate_completion(model, args.prefix)\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(suffix)\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['fim_docstring_middle_fix.py', 'testing_1.py'].\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | llama-2-13b-hf, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    model.load()\n", "    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.tokenizer.encode(suffix)\n", "    completions = model.generate(prefix_toks, suffix_toks)\n", "    for completion in completions:\n", "        print(model.tokenizer.decode(completion))\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "The prompt #tokens: 688\n"]}], "source": ["test = all_tests[1]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)\n", "show_prompt_length(xmodel, extra['prompt'])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_1.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: list['Person'] = dataclasses.field(default_factory=list)\n", "\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: List[Person] = dataclasses.field(default_factory=list)\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: List[Person] = dataclasses.field(default_factory=list)\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: list[Person] = dataclasses.field(default_factory=list)\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['fim_dataclass_attrs.py', 'fim_docstring_middle_fix.py'].\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | llama-2-13b-hf, t=0.1, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: List[Person] = field(default_factory=list, repr=True, init=True)\n", "    \"\"\"The parents of the person.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "The prompt #tokens: 570\n"]}], "source": ["test = all_tests[10]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, _extra = run_single(xmodel, test, temperature=0.1, display=True)\n", "show_prompt_length(xmodel, _extra['prompt'])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index of the start of the data.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Additional data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index where the data starts.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index where the data ends.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data that can be used by the data source.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['structure_6.py', 'resources_1.py'].\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | llama-2-13b-hf, t=0.1, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_end: int = 5\n", "    \"\"\"The number of characters to read from the source.\"\"\"\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "The prompt #tokens: 527\n"]}], "source": ["test = all_tests[3]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, _extra = run_single(xmodel, test, temperature=0.1, display=True)\n", "show_prompt_length(xmodel, _extra['prompt'])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\n", "    def __str__(self):\n", "        return f\"Patch(file_content={self.file_content!r}, char_start={self.char_start}, char_end={self.char_end}, patch_content={self.patch_content!r}, patch_id={self.patch_id!r})\"\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['structure_3.py', 'fim_dataclass_attrs.py'].\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | llama-2-13b-hf, t=0.1, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\n", "    def __str__(self):\n", "        return f\"{self.file_content[self.char_start:self.char_end]}\"\n", "\n", "    def __repr__(self):\n", "        return f\"Patch(file_content={self.file_content}, char_start={self.char_start}, char_end={self.char_end}, patch_content={self.patch_content}, patch_id={self.patch_id})\"\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "The prompt #tokens: 977\n"]}], "source": ["test = all_tests[5]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, _extra = run_single(xmodel, test, temperature=0.1, display=True)\n", "show_prompt_length(xmodel, _extra['prompt'])"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    last = sorted_lst[0]\n", "    for item in sorted_lst[1:]:\n", "        if item!= last:\n", "            last = item\n", "        else:\n", "            sorted_lst.remove(item)\n", "\n", "\n", "if __name__ == '__main__':\n", "    main()\n", "\n", "# last line of code\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    last = sorted_lst[0]\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i]!= last:\n", "            last = sorted_lst[i]\n", "            sorted_lst[i] = last\n", "        else:\n", "            del sorted_lst[i]\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "#\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    last = sorted_lst[0]\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i]!= last:\n", "            last = sorted_lst[i]\n", "        else:\n", "            del sorted_lst[i]\n", "            i -= 1\n", "\n", "def main():\n", "    \"\"\"Main function.\"\"\"\n", "    lst = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]\n", "    print(lst)\n", "    remove_duplicates(lst)\n", "    print(lst)\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['fim_docstring_middle_fix.py', 'structure_2.py'].\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | llama-2-13b-hf, t=0.1, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    i = 0\n", "    while i < len(sorted_lst):\n", "        if sorted_lst[i] != sorted_lst[i + 1]:\n", "            sorted_lst[i + 1] = sorted_lst[i]\n", "            i += 1\n", "        else:\n", "            del sorted_lst[i]\n", "    return sorted_lst\n", "\n", "\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "The prompt #tokens: 454\n"]}], "source": ["test = all_tests[8]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, _extra = run_single(xmodel, test, temperature=0.1, display=True)\n", "show_prompt_length(xmodel, _extra['prompt'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# IGNORE THE FOLLOWING CODE"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_argparse.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m    default=False,\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples: ['structure_3.py', 'structure_5.py'].\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | llama-2-13b-hf, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m    help=\"whether to return single-line completions\",\n", ")\n", "\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "The prompt #tokens: 614\n"]}], "source": ["test = all_tests[2]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)\n", "show_prompt_length(xmodel, extra['prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(extra['result_v0']['Content to replace <FILL-IN-THE-MIDDLE>'])\n", "# print(extra['prompt'])\n", "# print(extra['result_v1'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = all_tests[3]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)\n", "show_prompt_length(xmodel, extra['prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = all_tests[4]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)\n", "show_prompt_length(xmodel, extra['prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = all_tests[5]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)\n", "show_prompt_length(xmodel, extra['prompt'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = r'''Given a piece of Python code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>. Please help write a concise, coherent and smart completion for its potential middle content. Think step by step and try to finish the completion based on function signature, type annotation, docstring and other useful info in the context. Please be cautious about the leading space or indent. Note that the middle content might be empty.\n", "\n", "[[[[ Exemplar-1 ]]]]\n", ">>>>> Code <<<<<\n", "import copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = <FILL-IN-THE-MIDDLE>\n", "        return result\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<\n", "copy.copy(self)\n", "        result.char_start += amount\n", "        result.char_end += amount\n", "\n", "[[[[ Exemplar-2 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person'] = dataclasses.field(<FILL-IN-THE-MIDDLE>\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<\n", "default_factory=list)\n", "\n", "[[[[ Exemplar-3 ]]]]\n", ">>>>> Code <<<<<\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True<FILL-IN-THE-MIDDLE>)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<'''\n", "\n", "xresult = run_basic(xmodel, prompt, temperature=0.1)\n", "print(xresult)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["test = all_tests[6]\n", "# display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "display_cache(test)\n", "_, _, extra = run_single(xmodel, test, display=True)\n", "show_prompt_length(xmodel, extra['prompt'])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}