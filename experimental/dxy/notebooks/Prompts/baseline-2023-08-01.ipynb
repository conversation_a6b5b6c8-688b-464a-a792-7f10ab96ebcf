{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import collections\n", "import pathlib\n", "from typing import cast\n", "from research.models.all_models import RemoteModel\n", "from research.core.model_input import ModelInput\n", "from research.models.all_models import StopCriteria, GenerationOptions\n", "\n", "models = collections.OrderedDict()\n", "models[\"StarCoder Base\"] = RemoteModel(\"http://216.153.49.205:5001\")\n", "models[\"StarCoder Plus\"] = RemoteModel(\"http://216.153.49.205:5002\")\n", "models[\"StarCoder Ours\"] = RemoteModel(\"http://216.153.49.205:5004\")\n", "# models[\"llama2_13b\"] = RemoteModel(\"http://216.153.49.205:5003\")\n", "# models[\"llama2_70b\"] = RemoteModel(\"http://216.153.49.205:5003\")\n", "base_keys = [\"StarCoder Base\", \"StarCoder Plus\", \"StarCoder Ours\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/go/structure_2.go\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/infer_preconditions.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/remove_duplicates.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_multi_function_2.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_class_1.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_class_2.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_3.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_4.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_5.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_script.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_trailing_return_with_var.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_4.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_leading_spaces.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_multi_function_1.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_noop.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_trailing_return.py\n", "In total, there are 43 tests and skipped 16 tests.\n", "There are 6 with cpp extension.\n", "There are 3 with go extension.\n", "There are 18 with py extension.\n", "Loaded 18 tests.\n", "[00] [#char: 284] chunk_request.py                    {'tags': ['arithmetic', 'instruction-following']}\n", "[01] [#char: 180] example4_research_models_generate.py {'tags': ['fim'], 'label': 'complete-func-via-ref', 'CoT': 'complete the function based on the class defination'}\n", "[02] [#char:  19] fim_argparse.py                     {'tags': ['fim'], 'CoT': 'The code completion should be about how to define argument with argparse.', 'max_generated_tokens': 128}\n", "[03] [#char: 245] fim_dataclass_attrs.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[04] [#char:  14] fim_dataclass_field.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[05] [#char:  92] fim_dataclass_methods.py            {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[06] [#char:  49] fim_docstring_middle_fix.py         {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[07] [#char:  70] fim_multi_node_missing.py           {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[08] [#char: 219] remove_duplicates_with_suffix.py    {'tags': ['algorithm'], 'label': 'imple-func-via-doc', 'CoT': 'Complete the algorithm function based on its function name and docstring.'}\n", "[09] [#char: 108] resources_1.py                      {'tags': ['resource-usage']}\n", "[10] [#char:  70] structure_1.py                      {'tags': ['struct', 'infer-usage'], 'label': 'complete-class-via-ref', 'CoT': 'complete the class defination based on how it is used in other functions.'}\n", "[11] [#char: 136] structure_2.py                      {'tags': ['struct', 'algorithm', 'recursion']}\n", "[12] [#char: 152] structure_3.py                      {'tags': ['struct', 'algorithm', 'time']}\n", "[13] [#char:  70] structure_5.py                      {'tags': ['struct', 'infer-usage']}\n", "[14] [#char:  22] structure_6.py                      {'tags': ['struct', 'infill', 'string-formation']}\n", "[15] [#char:  86] structure_7.py                      {'tags': ['struct', 'string-formation']}\n", "[16] [#char:  87] testing_1.py                        {'tags': ['testing', 'test-setup']}\n", "[17] [#char: 118] testing_2.py                        {'tags': ['testing', 'test-data']}\n"]}], "source": ["from copy import deepcopy\n", "from experimental.dxy.code.few_shot_helper import FewshotTemplate, dict_by_any\n", "from experimental.dxy.code.vulcan_helper import display_output\n", "from experimental.dxy.code.vulcan_helper import display_cache\n", "from experimental.dxy.code.vulcan_helper import load_tests\n", "from experimental.dxy.code.vulcan_helper import VulcanTest\n", "from experimental.dxy.code.file_helper import pickle_load, pickle_save\n", "\n", "all_tests = load_tests()\n", "CACHE_SAVE_DIR = '/home/<USER>/checkpoints/cache/prompts'\n", "\n", "def run_norm(\n", "    model_key: str, test: VulcanTest, temperature=0.0, display: bool = False\n", ") -> tuple[str, str]:\n", "    input = ModelInput(prefix=test.prefix, suffix=test.suffix)\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    result = models[model_key].generate(input, options)\n", "    extra_header = f\"{model_key}, t={temperature:.1f}, max_tokens={max_generated_tokens}\"\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=extra_header,\n", "        )\n", "    return result, extra_header\n", "\n", "def run_all_models(index: int, save: bool = True) -> VulcanTest:\n", "    test = deepcopy(all_tests[index])\n", "    if False:\n", "        display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "    for key in base_keys:\n", "        result, extra_header = run_norm(key, test, display=False)\n", "        test.meta['results'][key] = result\n", "        test.meta['extra_headers'][key] = extra_header\n", "    if save:\n", "        filename = os.path.basename(test.filename)\n", "        pickle_save(test, os.path.join(CACHE_SAVE_DIR, f'{filename}-baseline'))\n", "    return test"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluate index=8 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/remove_duplicates_with_suffix.py\n"]}], "source": ["for index in range(len(all_tests)):\n", "    print(f'Evaluate index={index} : {all_tests[index].filename}')\n", "    test = run_all_models(index)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: chunk_request.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_count': chunk_count,\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["def show_cache_by_index(index: int):\n", "    test = all_tests[index]\n", "    filename = os.path.basename(test.filename)\n", "    display_cache(os.path.join(CACHE_SAVE_DIR, f\"{filename}-baseline\"))\n", "\n", "show_cache_by_index(0)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(f\"{prefix} {suffix}\")\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(f'Generated completion: {suffix}')\n", "\n", "def load_model(model_name: str) -> ResearchModel:\n", "    \"\"\"Load the model with the given name.\"\"\"\n", "    if model_name == 'gpt2':\n", "        return GPT2Model()\n", "    else:\n", "        raise ValueError(f'Unknown model name: {model_name}')\n", "\n", "if __name__ == '__main__':\n", "    model = load_model('gpt2')\n", "    model.load()\n", "    generate_completion(model, 'The ')\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(suffix)\n", "\n", "def main():\n", "    \"\"\"Main entry point.\"\"\"\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument('model', type=str, help='The model to use.')\n", "    parser.add_argument('prefix', type=str, help='The prefix to use.')\n", "    args = parser.parse_args()\n", "\n", "    model = ResearchModel.load(args.model)\n", "    generate_completion(model, args.prefix)\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(suffix)\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(1)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_argparse.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m    default=False,\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(2)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index of the start of the data.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Additional data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index where the data starts.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index where the data ends.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data that can be used by the data source.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(3)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m, compare=True\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m, compare=False\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m, compare=False\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_field.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True\u001b[0m\u001b[40m\u001b[31m, compare=False\u001b[0m\u001b[32m)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(4)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\n", "\n", "    def __str__(self):\n", "        return f\"Patch(file_content={self.file_content!r}, char_start={self.char_start}, char_end={self.char_end}, patch_content={self.patch_content!r}, patch_id={self.patch_id!r})\"\n", "\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_methods.py ✓ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "        return result\u001b[0m\u001b[32m\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(5)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_multi_node_missing.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "\u001b[0m\u001b[32m        return result\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_node_missing.py ✓ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "\u001b[0m\u001b[32m        return result\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_node_missing.py ✓ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "\u001b[0m\u001b[32m        return result\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_multi_node_missing.py ✓ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport copy\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "@dataclass\n", "class Patch:\n", "    \"\"\"Represents a patch to a file using its character span.\"\"\"\n", "\n", "    file_content: str = \"\"\n", "    \"\"\"The file contents being modified. text being patched. Assumes UTF-8 encoding.\"\"\"\n", "    char_start: int = 0\n", "    \"\"\"Character offset for the patch.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"character offset for the patch.\"\"\"\n", "    patch_content: str = \"\"\n", "    \"\"\"the patch content.\"\"\"\n", "    patch_id: str = \"\"\n", "\n", "    @classmethod\n", "    def for_span(\n", "        cls,\n", "        file_content: str,\n", "        char_start: int,\n", "        char_end: int,\n", "        **kwargs,\n", "    ):\n", "        \"\"\"create a patch for the region [char_start, char_end) in file contents.\"\"\"\n", "        return cls(\n", "            file_content=file_content,\n", "            char_start=char_start,\n", "            char_end=char_end,\n", "            patch_content=file_content[char_start:char_end],\n", "            **kwargs,\n", "        )\n", "\n", "    def shift(self, amount: int) -> \"Patch\":\n", "        result = copy.copy(self)\n", "\u001b[0m\u001b[40m\u001b[31m        result.char_start += amount\n", "        result.char_end += amount\n", "\u001b[0m\u001b[32m        return result\n", "\n", "    @classmethod\n", "    def empty(cls, file_content: str, **kwargs):\n", "        \"\"\"Create an empty Patch for a file.\"\"\"\n", "        return Patch(\n", "            file_content=file_content,\n", "            char_start=0,\n", "            char_end=0,\n", "            patch_content=\"\",\n", "            **kwargs,\n", "        )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(6)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: resources_1.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[31m    file = open(fname)\n", "    for i, line in enumerate(file):\n", "        if i == line_no:\n", "            return line\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[31m    file = open(fname)\n", "    for i, line in enumerate(file):\n", "        if i == line_no:\n", "            return line.strip()\n", "\n", "if __name__ == \"__main__\":\n", "    main()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    file = open(fname)\n", "    for i, line in enumerate(file):\n", "        if i == line_no:\n", "            return line\n", "    file.close()\n", "\n", "    return None\n", "\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: resources_1.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_line_at_pct(fname, pct):\n", "    file = open(fname)\n", "    lines = sum(1 for line in file)\n", "    file.close()\n", "\n", "    line_no = int(pct * lines)\n", "\u001b[0m\u001b[40m\u001b[31m    file = open(fname)\n", "    for i, line in enumerate(file):\n", "        if i == line_no:\n", "            return line\n", "        else:\n", "            continue\n", "    file.close()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(7)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    last = sorted_lst[0]\n", "    for item in sorted_lst[1:]:\n", "        if item!= last:\n", "            last = item\n", "        else:\n", "            sorted_lst.remove(item)\n", "\n", "\n", "if __name__ == '__main__':\n", "    main()\n", "\n", "# last line of code\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    last = sorted_lst[0]\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i]!= last:\n", "            last = sorted_lst[i]\n", "            sorted_lst[i] = last\n", "        else:\n", "            del sorted_lst[i]\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "#\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: remove_duplicates_with_suffix.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "\u001b[0m\u001b[40m\u001b[31m    last = sorted_lst[0]\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i]!= last:\n", "            last = sorted_lst[i]\n", "        else:\n", "            del sorted_lst[i]\n", "            i -= 1\n", "\n", "def main():\n", "    \"\"\"Main function.\"\"\"\n", "    lst = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]\n", "    print(lst)\n", "    remove_duplicates(lst)\n", "    print(lst)\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m # EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(8)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_2.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors(parent))\n", "    return ancestors\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v2(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v2(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v3(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v3(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v4(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v4(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v5(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v5(parent))\n", "    return an\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.extend(get_ancestors(parent))\n", "    ancestors.append(person)\n", "    return ancestors\n", "\n", "\n", "def main():\n", "    person = Person('<PERSON>', 30, [\n", "        Person('<PERSON>', 25, [\n", "            Person('<PERSON>', 20, []),\n", "            Person('<PERSON>', 20, []),\n", "        ]),\n", "        Person('<PERSON>', 35, []),\n", "    ])\n", "    print(get_ancestors(person))\n", "\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.extend(get_ancestors(parent))\n", "    ancestors.append(person)\n", "    return ancestors\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(9)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_2.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors(parent))\n", "    return ancestors\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v2(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v2(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v3(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v3(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v4(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v4(parent))\n", "    return ancestors\n", "\n", "\n", "def get_ancestors_v5(person: Person):\n", "    ancestors = []\n", "    for parent in person.parents:\n", "        ancestors.append(parent)\n", "        ancestors.extend(get_ancestors_v5(parent))\n", "    return an\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.extend(get_ancestors(parent))\n", "    ancestors.append(person)\n", "    return ancestors\n", "\n", "\n", "def main():\n", "    person = Person('<PERSON>', 30, [\n", "        Person('<PERSON>', 25, [\n", "            Person('<PERSON>', 20, []),\n", "            Person('<PERSON>', 20, []),\n", "        ]),\n", "        Person('<PERSON>', 35, []),\n", "    ])\n", "    print(get_ancestors(person))\n", "\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_2.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    parents: list['Person']\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ancestors = []\n", "\u001b[0m\u001b[40m\u001b[31m    for parent in person.parents:\n", "        ancestors.extend(get_ancestors(parent))\n", "    ancestors.append(person)\n", "    return ancestors\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(10)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_3.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[31mif datetime.date(now.year, alice.dob.month, alice.dob.day) < now:\n", "    age = (now.year - alice.dob.year)\n", "else:\n", "    age = (now.year - alice.dob.year) - 1\n", "\u001b[0m\u001b[32mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[31mage = (now - alice.dob).days / 365\n", "\u001b[0m\u001b[32mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[31mage = now.year - alice.dob.year\n", "\u001b[0m\u001b[32mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_3.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "\n", "alice = Person('<PERSON>', datetime.date.fromisoformat('1989-04-01'), 'Engineer')\n", "now = datetime.date.today()\n", "\u001b[0m\u001b[40m\u001b[31mage = now.year - alice.dob.year - ((now.month, now.day) < (alice.dob.month, alice.dob.day))\n", "\u001b[0m\u001b[32mprint(f\"<PERSON> is {age} years old!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(11)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_5.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: list['Person'] = dataclasses.field(default_factory=list)\n", "\u001b[0m\u001b[32m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m\n", "\u001b[0m\u001b[32m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parent: Person = None\n", "\u001b[0m\u001b[32m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_5.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m\n", "@dataclasses.dataclass\n", "class Student(Person):\n", "    school: str\n", "    year: int\u001b[0m\u001b[32m\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(12)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_6.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[31mis a {self.profession}\u001b[0m\u001b[32m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[31mis a {self.profession}\u001b[0m\u001b[32m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[31mis a {self.profession}\u001b[0m\u001b[32m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_6.py ✓ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} \u001b[0m\u001b[40m\u001b[31mis a {self.profession}\u001b[0m\u001b[32m and is {self.dob.year} years old\"\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(13)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_7.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[31m        return f\"{self.name} is a {self.profession} and is {self.dob.year} years old\"\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[31m        return f\"{self.name} is a {self.profession} and was born on {self.dob}\"\n", "\n", "if __name__ == \"__main__\":\n", "    p = Person(\"<PERSON>\", datetime.date(1990, 1, 1), \"engineer\")\n", "    print(p)\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[31m        return f\"{self.name} {self.dob} {self.profession}\"\n", "\n", "if __name__ == \"__main__\":\n", "    p = Person(\"<PERSON>\", datetime.date(1990, 1, 1), \"Programmer\")\n", "    print(p)\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_7.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "\u001b[0m\u001b[40m\u001b[31m        return f'{self.name} ({self.dob}) is a {self.profession}'\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(14)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: testing_1.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[31m    persons = [p1, p2, p3]\n", "    sort_by_age(persons)\n", "    assert persons == [p3, p2, p1]\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[31m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p2, p3, p1]\n", "\n", "\n", "if __name__ == '__main__':\n", "    test_sort_by_age()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[31m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p2, p3, p1]\n", "\n", "\n", "if __name__ == '__main__':\n", "    test_sort_by_age()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_1.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "    p1 = Person('<PERSON>', 42, '<PERSON>')\n", "    p2 = Person('<PERSON>', 22, 'Marketer')\n", "    p3 = Person('<PERSON>', 12, '')\n", "\u001b[0m\u001b[40m\u001b[31m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p2, p1]\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(15)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: testing_2.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[31m    p1 = Person(\"<PERSON>\", 42, \"<PERSON>\")\n", "    p2 = Person(\"<PERSON>\", 22, \"Marketer\")\n", "    p3 = Person(\"<PERSON>\", 12, \"\")\n", "\u001b[0m\u001b[32m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\n", "\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[31m    p1 = Person(\"<PERSON>\", 20, \"Programmer\")\n", "    p2 = Person(\"<PERSON>\", 21, \"Programmer\")\n", "    p3 = Person(\"<PERSON>\", 22, \"Programmer\")\n", "\u001b[0m\u001b[32m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\n", "\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[31m    p1 = Person(\"<PERSON>\", 30, \"programmer\")\n", "    p2 = Person(\"<PERSON>\", 20, \"programmer\")\n", "    p3 = Person(\"<PERSON>\", 35, \"programmer\")\n", "\u001b[0m\u001b[32m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\n", "\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: testing_2.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "import dataclasses\n", "\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "    profession: str\n", "\n", "\n", "def sort_by_age(people):\n", "    people.sort(key=lambda x: x.age)\n", "\n", "\n", "def test_sort_by_age():\n", "\u001b[0m\u001b[40m\u001b[31m    p1 = Person('<PERSON>', 20, 'Teacher')\n", "    p2 = Person('<PERSON>', 18, 'Teacher')\n", "    p3 = Person('<PERSON>', 20, 'Teacher')\u001b[0m\u001b[32m    people = [p1, p2, p3]\n", "    sort_by_age(people)\n", "    assert people == [p3, p1, p2]\n", "\n", "\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(16)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}