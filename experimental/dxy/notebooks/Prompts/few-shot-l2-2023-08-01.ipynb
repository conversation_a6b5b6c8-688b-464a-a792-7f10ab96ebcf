{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All avaliable models: ['codegen_16b_indiana', 'codegen_16b_multi', 'codegen_2b_fim', 'codegen_2b_indiana', 'codegen_2b_multi', 'codegen_350m_multi', 'llama2_13b_pretrain_hf', 'llama2_70b_pretrain_hf', 'llama2_7b_chat_meta', 'llama2_7b_pretrain_hf', 'llama2_7b_pretrain_meta', 'null', 'openai', 'remote', 'starcoderbase', 'starcoderbase_1b', 'starcoderbase_3b', 'starcoderbase_7b', 'starcoderbase_hf', 'starcoderplus', 'starcoderplus_hf', 'starllama_200m_wide', 'starllama_350m', 'wizardcoder_hf']\n"]}], "source": ["import os\n", "import collections\n", "import pathlib\n", "from typing import cast\n", "from termcolor import colored\n", "from research.models.core import list_models, get_model, RemoteModel\n", "from research.core.model_input import ModelInput\n", "from research.models.core import StopCriteria, GenerationOptions\n", "\n", "print(f'All avaliable models: {list_models()}')\n", "\n", "models = collections.OrderedDict()\n", "models[\"StarCoder Base\"] = RemoteModel(\"http://216.153.49.205:5001\")\n", "models[\"StarCoder Plus\"] = RemoteModel(\"http://216.153.49.205:5002\")\n", "models[\"StarCoder Ours\"] = RemoteModel(\"http://216.153.49.205:5004\")\n", "models[\"llama2_13b\"] = RemoteModel(\"http://216.153.49.205:5003\")\n", "# models[\"llama2_70b\"] = Remote_Model(\"http://216.153.49.205:5003\")\n", "base_keys = [\"StarCoder Base\", \"StarCoder Plus\", \"StarCoder Ours\"]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dc52b194430b42dcab9f0db5d3b0b93f", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["xmodel = get_model(\"llama2_13b_pretrain_hf\", checkpoint_path=pathlib.Path('/home/<USER>/checkpoints/llama-2-13b-hf'))\n", "xmodel.load()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ignore go/structure_2.go\n", "Ignore py/infer_preconditions.py\n", "Ignore py/remove_duplicates.py\n", "Ignore py/fim_multi_function_2.py\n", "Ignore py/fim_real_class_1.py\n", "Ignore py/fim_real_class_2.py\n", "Ignore py/fim_real_ex_4.py\n", "Ignore py/fim_real_ex_5.py\n", "Ignore py/fim_script.py\n", "Ignore py/fim_trailing_return_with_var.py\n", "Ignore py/fim_demo1.py\n", "Ignore py/fim_demo2.py\n", "Ignore py/fim_real_ex_3.py\n", "Ignore py/structure_4.py\n", "Ignore py/archived/fim_leading_spaces.py\n", "Ignore py/archived/fim_multi_function_1.py\n", "Ignore py/archived/fim_noop.py\n", "Ignore py/archived/fim_trailing_return.py\n", "In total, there are 45 tests and skipped 18 tests.\n", "There are 6 with cpp extension.\n", "There are 3 with go extension.\n", "There are 18 with py extension.\n", "Loaded 18 tests.\n", "[00] [#char: 284] chunk_request.py                    {'tags': ['arithmetic', 'instruction-following'], 'label': 'imple-func-via-doc', 'CoT': 'Complete the algorithm function based on its function name and docstring.'}\n", "[01] [#char: 180] example4_research_models_generate.py {'tags': ['fim'], 'label': 'complete-func-via-ref', 'CoT': 'complete the function based on the class defination'}\n", "[02] [#char:  19] fim_argparse.py                     {'tags': ['fim'], 'label': 'argparse', 'CoT': 'The code completion should be about how to define argument with argparse.', 'max_generated_tokens': 128}\n", "[03] [#char: 245] fim_dataclass_attrs.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[04] [#char:  14] fim_dataclass_field.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[05] [#char:  92] fim_dataclass_methods.py            {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[06] [#char:  49] fim_docstring_middle_fix.py         {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[07] [#char:  70] fim_multi_node_missing.py           {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[08] [#char: 219] remove_duplicates_with_suffix.py    {'tags': ['algorithm'], 'label': 'imple-func-via-doc', 'CoT': 'Complete the algorithm function based on its function name and docstring.'}\n", "[09] [#char: 108] resources_1.py                      {'tags': ['resource-usage']}\n", "[10] [#char:  70] structure_1.py                      {'tags': ['struct', 'infer-usage'], 'label': 'complete-class-via-ref', 'CoT': 'complete the class defination based on how it is used in other functions.'}\n", "[11] [#char: 136] structure_2.py                      {'tags': ['struct', 'algorithm', 'recursion'], 'label': 'complete-class-via-ref', 'CoT': 'complete the class defination based on how it is used in other functions.'}\n", "[12] [#char: 152] structure_3.py                      {'tags': ['struct', 'algorithm', 'time']}\n", "[13] [#char:  70] structure_5.py                      {'tags': ['struct', 'infer-usage']}\n", "[14] [#char:  22] structure_6.py                      {'tags': ['struct', 'infill', 'string-formation']}\n", "[15] [#char:  86] structure_7.py                      {'tags': ['struct', 'string-formation']}\n", "[16] [#char:  87] testing_1.py                        {'tags': ['testing', 'test-setup']}\n", "[17] [#char: 118] testing_2.py                        {'tags': ['testing', 'test-data']}\n", "----------------------------------------------------------------------------------------------------\n", "imple-func-via-doc : 2 tests\n", "complete-func-via-ref : 1 tests\n", "argparse : 1 tests\n", "None : 12 tests\n", "complete-class-via-ref : 2 tests\n", "----------------------------------------------------------------------------------------------------\n", "Use the saved cache results from /home/<USER>/checkpoints/cache/prompts and baseline\n", "Find cache /home/<USER>/checkpoints/cache/prompts/chunk_request.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/example4_research_models_generate.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_argparse.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_attrs.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_field.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_methods.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_docstring_middle_fix.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_multi_node_missing.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/remove_duplicates_with_suffix.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/resources_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_2.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_3.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_5.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_6.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_7.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/testing_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/testing_2.py-baseline and loading...\n", "EXAMPLAR_TEST_ROOT: /home/<USER>/src/augment/experimental/dxy/exemplars\n", "----------------------------------------------------------------------------------------------------\n", "argparse : 1 tests\n", "complete-class-via-ref : 1 tests\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["import random\n", "from copy import deepcopy\n", "from experimental.dxy.code.few_shot_helper import FewshotTemplate, dict_by_any\n", "from experimental.dxy.code.vulcan_helper import display_output\n", "from experimental.dxy.code.vulcan_helper import display_cache\n", "from experimental.dxy.code.vulcan_helper import load_tests\n", "from experimental.dxy.code.vulcan_helper import load_examplar_tests\n", "from experimental.dxy.code.vulcan_helper import convert_test_by_label\n", "from experimental.dxy.code.vulcan_helper import VulcanTest\n", "from experimental.dxy.code.file_helper import pickle_load, pickle_save\n", "from experimental.dxy.code.str_helper import trim_result_if_meet_tail_of_prefix\n", "from experimental.dxy.code.str_helper import trim_result_if_meet_head_of_suffix\n", "\n", "\n", "CACHE_SAVE_DIR = \"/home/<USER>/checkpoints/cache/prompts\"\n", "all_tests = load_tests(cache_folder=CACHE_SAVE_DIR, cache_suffix=\"baseline\")\n", "all_examplars = load_examplar_tests()\n", "examplars_by_label = convert_test_by_label(all_examplars, all_in_none=True)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def convert_v0(x: VulcanTest, separator: str = \"<FILL-IN-THE-MIDDLE>\"):\n", "    return {\n", "        \"code\": f\"{x.prefix}{separator}{x.suffix}\",\n", "        \"Content to replace <FILL-IN-THE-MIDDLE>\": x.expected,\n", "    }\n", "\n", "\n", "template_v0 = FewshotTemplate(\n", "    input_keys=[\"code\"],\n", "    output_keys=[\"Content to replace <FILL-IN-THE-MIDDLE>\"],\n", "    ex_sep=\"\\n\",\n", "    transfer_fn=convert_v0,\n", "    task_prefix=(\n", "        \"Given a piece of Python code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>.\"\n", "        \" Please help write a concise, coherent and smart completion for its potential middle content.\"\n", "        \" Please be cautious about the leading space or indent.\"\n", "        \" Note that the middle content might be empty.\\n\"\n", "    ),\n", ")\n", "template = template_v0\n", "\n", "\n", "def run_basic(\n", "    prompt: str,\n", "    model_key_or_model=\"llama2_13b\",\n", "    temperature: float = 0.0,\n", "    max_generated_tokens: int = 256,\n", ") -> str:\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    if isinstance(model_key_or_model, str):\n", "        model = models[model_key_or_model]\n", "    else:\n", "        model = model_key_or_model\n", "    raw_results = model.generate(ModelInput(prefix=prompt), options)\n", "    return raw_results\n", "\n", "\n", "def run_single(\n", "    model_key: str,\n", "    test: VulcanTest,\n", "    temperature=0.0,\n", "    max_select: int = 2,\n", "    display: bool = False,\n", "    debug: bool = False,\n", ") -> tuple[str, str, dict]:\n", "    # input = ModelInput(prefix=test.prefix, suffix=test.suffix)\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    # Find the examples\n", "    # if \"label\" in test.meta:\n", "    #     label = test.meta[\"label\"]\n", "    # else:\n", "    #     label = None\n", "    # if label is not None and (len(examplars_by_label[label]) > 0):\n", "    #     examples = examplars_by_label[label][:max_select]\n", "    # else:\n", "    if True:\n", "        random_selected = random.sample(all_tests, max_select + 1)\n", "        random_selected = [x for x in random_selected if x.filename != test.filename]\n", "        examples = random_selected[:max_select]\n", "    print(f\"Selected {len(examples)} examples.\")\n", "    prompt = template.get_prompt(test, examples)\n", "    if debug:\n", "        print(\"-\" * 1000)\n", "        print(f\"Prompt:\\n{prompt}\")\n", "    input = ModelInput(prefix=prompt)\n", "\n", "    raw_result = models[model_key].generate(input, options)\n", "    result_v0 = template.parse_response(raw_result)\n", "    result_v1 = list(result_v0.values())[0]\n", "    result_v2 = trim_result_if_meet_tail_of_prefix(result_v1, prefix=test.prefix)\n", "    result_v3 = trim_result_if_meet_head_of_suffix(result_v2, suffix=test.suffix)\n", "    result = result_v3\n", "    extra_header = (\n", "        f\"{model_key}, t={temperature:.1f}, max_tokens={max_generated_tokens}\"\n", "    )\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=extra_header,\n", "        )\n", "    return (\n", "        result,\n", "        extra_header,\n", "        {\n", "            \"prompt\": prompt,\n", "            \"result\": result,\n", "            \"raw_result\": raw_result,\n", "            \"result_v0\": result_v0,\n", "            \"result_v1\": result_v1,\n", "            \"result_v2\": result_v2,\n", "            \"result_v3\": result_v3,\n", "            \"test.prefix\": test.prefix,\n", "            \"test.suffix\": test.suffix,\n", "        },\n", "    )\n", "\n", "\n", "def run_single_with_save(\n", "    index: int, model_key: str = \"llama2_13b\", display: bool = True, save: bool = False\n", ") -> tuple[VulcanTest, dict]:\n", "    test = deepcopy(all_tests[index])\n", "    if display:\n", "        print(f\"{test.filename}\\n{test.meta}\")\n", "        # display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "        display_cache(test)\n", "    result, extra_header, extra = run_single(model_key, test, display=display)\n", "\n", "    test.meta[\"results\"][model_key] = result\n", "    test.meta[\"extra_headers\"][model_key] = extra_header\n", "    if save:\n", "        filename = os.path.basename(test.filename)\n", "        pickle_save(test, os.path.join(CACHE_SAVE_DIR, f\"{filename}-baseline\"))\n", "    return test, extra"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["py/structure_1.py\n", "{'tags': ['struct', 'infer-usage'], 'label': 'complete-class-via-ref', 'CoT': 'complete the class defination based on how it is used in other functions.', 'results': OrderedDict([('StarCoder Base', '    parents: List[Person] = dataclasses.field(default_factory=list)'), ('StarCoder Plus', '    parents: List[Person] = dataclasses.field(default_factory=list)'), ('StarCoder Ours', '    parents: list[Person] = dataclasses.field(default_factory=list)')]), 'extra_headers': OrderedDict([('StarCoder Base', 'StarCoder Base, t=0.0, max_tokens=256'), ('StarCoder Plus', 'StarCoder Plus, t=0.0, max_tokens=256'), ('StarCoder Ours', 'StarCoder Ours, t=0.0, max_tokens=256')])}\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: list['Person'] = dataclasses.field(default_factory=list)\n", "\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: List[Person] = dataclasses.field(default_factory=list)\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: List[Person] = dataclasses.field(default_factory=list)\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: list[Person] = dataclasses.field(default_factory=list)\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples.\n", "========================================================================================\n", "EXAMPLE: structure_1.py ✓ | llama2_13b, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31m    parents: list['Person'] = dataclasses.field(default_factory=list)\n", "\n", "\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["_, extra = run_single_with_save(10, display=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["py/chunk_request.py\n", "{'tags': ['arithmetic', 'instruction-following'], 'label': 'imple-func-via-doc', 'CoT': 'Complete the algorithm function based on its function name and docstring.', 'results': OrderedDict([('StarCoder Base', \"    for chunk_idx in range(chunk_count):\\n        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\\n        requests.append({\\n            'chunk_idx': chunk_idx,\\n            'chunk_count': chunk_count,\\n            'chunk_payload': chunk_payload,\\n        })\\n\"), ('StarCoder Plus', \"    for chunk_idx in range(chunk_count):\\n        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\\n        requests.append({\\n            'chunk_idx': chunk_idx,\\n            'chunk_count': chunk_count,\\n            'chunk_payload': chunk_payload,\\n        })\\n\"), ('StarCoder Ours', \"    for chunk_idx in range(chunk_count):\\n        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\\n        requests.append({\\n            'chunk_idx': chunk_idx,\\n            'chunk_count': chunk_count,\\n            'chunk_payload': chunk_payload,\\n        })\")]), 'extra_headers': OrderedDict([('StarCoder Base', 'StarCoder Base, t=0.0, max_tokens=256'), ('StarCoder Plus', 'StarCoder Plus, t=0.0, max_tokens=256'), ('StarCoder Ours', 'StarCoder Ours, t=0.0, max_tokens=256')])}\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_count': chunk_count,\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples.\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | llama2_13b, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size:(chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\n", "    return requests\n", "\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["_, extra = run_single_with_save(0, display=True)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    parents: List[Person] = []\n", "\n", "\n"]}], "source": ["# print(extra['result_v2'])\n", "result = r'''    parents: List[Person] = []\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get-ancestors(parent))\n", "    return ret\n", "'''\n", "\n", "suffix = r'''def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret'''\n", "\n", "def trim_result_if_meet_head_of_suffix(\n", "    result: str, suffix: str, min_matched_lines: int = 3\n", ") -> str:\n", "    suffix_lines = suffix.split(\"\\n\")\n", "    result_lines = result.split(\"\\n\")\n", "    for index in range(len(result_lines)):\n", "        matched_lines = 0\n", "        for j, suffix_line in enumerate(suffix_lines):\n", "            if result_lines[index + j] != suffix_line:\n", "                break\n", "            if suffix_line != '':\n", "                matched_lines += 1\n", "        if matched_lines >= min_matched_lines:\n", "            return '\\n'.join(result_lines[:index])\n", "    return result\n", "\n", "xx = trim_result_if_meet_head_of_suffix(result, suffix)\n", "print(xx)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Given a piece of Python code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>. Please help write a concise, coherent and smart completion for its potential middle content. Please be cautious about the leading space or indent. Note that the middle content might be empty.\n", "[[[[ Exemplar-1 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "from pathlib import Any, Path\n", "\n", "\n", "@dataclasses.dataclass\n", "class Logger:\n", "    \"\"\"A class used for logging purpose.\"\"\"\n", "<FILL-IN-THE-MIDDLE>    def reset(self, log_dir, seed, create_model_dir=True):\n", "        \"\"\"Create a summary writer logging to log_dir.\"\"\"\n", "        self.seed = int(seed)\n", "        self.log_dir = Path(log_dir)\n", "        self.model_dir = Path(log_dir) / \"checkpoint\"\n", "        self.log_dir.mkdir(parents=True, exist_ok=True)\n", "        if create_model_dir:\n", "            self.model_dir.mkdir(parents=True, exist_ok=True)\n", "        self.logger_file = open(self.logger_path, \"w\")\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<\n", "    seed: int\n", "    log_dir: Path\n", "    logger_file: Any\n", "\n", "[[[[ Exemplar-2 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "<FILL-IN-THE-MIDDLE>\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<\n", "\n"]}], "source": ["print(extra['prompt'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["prompt = r'''Given a piece of Python code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>. Please help write a concise, coherent and smart completion for its potential middle content. Please be cautious about the leading space or indent. Note that the middle content might be empty.\n", "[[[[ Exemplar-1 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "from pathlib import Any, Path\n", "\n", "\n", "@dataclasses.dataclass\n", "class Logger:\n", "    \"\"\"A class used for logging purpose.\"\"\"\n", "<FILL-IN-THE-MIDDLE>    def reset(self, log_dir, seed, create_model_dir=True):\n", "        \"\"\"Create a summary writer logging to log_dir.\"\"\"\n", "        self.seed = int(seed)\n", "        self.log_dir = Path(log_dir)\n", "        self.model_dir = Path(log_dir) / \"checkpoint\"\n", "        self.log_dir.mkdir(parents=True, exist_ok=True)\n", "        if create_model_dir:\n", "            self.model_dir.mkdir(parents=True, exist_ok=True)\n", "        self.logger_files.append(open(self.logger_path, \"w\"))\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<\n", "    seed: int\n", "    log_dir: Path\n", "    logger_files: list[Any] = dataclasses.field(default_factory=list)\n", "\n", "\n", "[[[[ Exemplar-2 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "<FILL-IN-THE-MIDDLE>\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<'''\n", "\n", "raw_results = run_basic(prompt, xmodel)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "    parents: list[Person] = dataclasses.field(default_factory=list)\n", "\n", "\n", "[[[[ Exemplar-3 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "<FILL-IN-THE-MIDDLE>\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\n", ">>>>> Content To Replace <Fill-In-The-Middle> <<<<<\n", "    parents: list[Person] = dataclasses.field(default_factory=list)\n", "\n", "\n", "[[[[ Exemplar-4 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "<FILL-IN-THE-MIDDLE>\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "   \n"]}], "source": ["print(raw_results)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["py/fim_dataclass_attrs.py\n", "{'tags': ['fim'], 'max_generated_tokens': 128, 'results': OrderedDict([('StarCoder Base', '\\n    char_start: int = 0\\n    \"\"\"The character index of the start of the data.\"\"\"\\n\\n    char_end: int = 0\\n    \"\"\"The character index of the end of the data.\"\"\"\\n\\n    _extra: Dict[str, Any] = field(default_factory=dict)\\n    \"\"\"Additional data.\"\"\"\\n'), ('StarCoder Plus', '\\n    char_end: int = 0\\n    \"\"\"The character index of the end of the data.\"\"\"\\n\\n    _extra: Dict[str, Any] = field(default_factory=dict)\\n    \"\"\"Extra data.\"\"\"'), ('StarCoder Ours', '\\n    char_start: int = 0\\n    \"\"\"The character index where the data starts.\"\"\"\\n\\n    char_end: int = 0\\n    \"\"\"The character index where the data ends.\"\"\"\\n\\n    _extra: Dict[str, Any] = field(default_factory=dict)\\n    \"\"\"Extra data that can be used by the data source.\"\"\"')]), 'extra_headers': OrderedDict([('StarCoder Base', 'StarCoder Base, t=0.0, max_tokens=140'), ('StarCoder Plus', 'StarCoder Plus, t=0.0, max_tokens=140'), ('StarCoder Ours', 'StarCoder Ours, t=0.0, max_tokens=140')])}\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index of the start of the data.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Additional data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character index where the data starts.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character index where the data ends.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data that can be used by the data source.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples.\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    char_end: int = 0\n", "    \"\"\"The number of characters in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Any extra data.\"\"\"\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The number of characters in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["_, extra = run_single_with_save(3, display=True)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 11 prefix lines\n", "    char_end: int = 0\n", "    \"\"\"The number of characters in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Any extra data.\"\"\"\n"]}], "source": ["prefix=r'''from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "'''\n", "\n", "result = r'''    char_end: int = 0\n", "    \"\"\"The number of characters in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Any extra data.\"\"\"\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The number of characters in the source.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"'''\n", "\n", "\n", "\n", "\n", "xresult = trim_result_if_meet_tail_of_prefix(result, prefix)\n", "print(xresult)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["[10, 9, 8, 7, 6, 5, 4, 3, 2, 1, 0]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["list(range(10, -1, -1))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["prompt = extra[\"prompt\"]\n", "# print(prompt)\n", "# print(extra.keys())\n", "# print(extra['raw_result'])\n", "prompt = r'''\n", "Given a piece of Python code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>. Please help write a concise, coherent and smart completion for its potential middle content. Please be cautious about the leading space or indent. Note that the middle content might be empty.\n", "[[[[ Exemplar-1 ]]]]\n", ">>>>> Code <<<<<\n", "import dataclasses\n", "import datetime\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    dob: datetime.date\n", "    profession: str\n", "\n", "    def __str__(self):\n", "        return f\"{self.name} <FILL-IN-THE-MIDDLE> and is {self.dob.year} years old\"\n", "\n", ">>>>> <Fill-In-The-Middle> <<<<<\n", "is a {self.profession}\n", "[[[[ Exemplar-2 ]]]]\n", ">>>>> Code <<<<<\n", "\n", "def remove_duplicates(sorted_lst):\n", "    \"\"\"Remove duplicates in-place from a sorted list.\"\"\"\n", "    if not sorted_lst:\n", "        return\n", "<FILL-IN-THE-MIDDLE> # EOF\n", "\n", ">>>>> <Fill-In-The-Middle> <<<<<\n", "write_idx = 0\n", "    for i in range(1, len(sorted_lst)):\n", "        if sorted_lst[i] != sorted_lst[write_idx]:\n", "            write_idx += 1\n", "            sorted_lst[write_idx] = sorted_lst[i]\n", "    del sorted_lst[write_idx+1:]\n", "\n", "[[[[ Exemplar-3 ]]]]\n", ">>>>> Code <<<<<\n", "from dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "<FILL-IN-THE-MIDDLE>\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\n", ">>>>> <Fill-In-The-Middle> <<<<<\n", "'''\n", "raw_results = run_basic(prompt, xmodel)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    char_end: int = 0\n", "    \"\"\"The number of characters in the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Any extra data.\"\"\"\n", "\n", "    def __post_init__(self):\n", "        if self.char_end < 0:\n", "            raise ValueError(\"char_end must be non-negative\")\n", "\n", "    def __str__(self):\n", "        return f\"{self.source} {self.char_end} chars\"\n", "\n", "[[[[ Exemplar-4 ]]]]\n", ">>>>> Code <<<<<\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\n", ">>>>> <Fill-In-The-Middle> <<<<<\n", "    char_end: int = 0\n", "    \"\"\"The number of characters in the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Any extra data.\"\"\"\n", "\n", "    def __post_init\n"]}], "source": ["print(raw_results)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\n", "\n", "    return requests\n", "\n", "[[[[ Exemplar-4 ]]]]\n", ">>>>> Code <<<<<\n", "import os\n", "\n", "\n", "def get_file_content(file_path: str) -> str:\n", "    \"\"\"Get the content of a file.\"\"\"\n", "    with open(file_path, \"r\") as f:\n", "        return f.read()\n", "\n", "\n", "def get_file_content_with_patches(file_path: str) -> str:\n", "    \"\"\"Get the content of a file with patches applied.\"\"\"\n", "    file_content = get_file_content(file_path)\n", "    patches = get_patches(file_path)\n", "    for patch in patches:\n", "        file_content = patch.apply(\n"]}], "source": ["raw_outputs = run_basic(prompt)\n", "print(raw_outputs)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Content to replace <FILL-IN-THE-MIDDLE>': 'chunk_idx = 0\\n    chunk_payload = payload[:chunk_size]\\n    requests.append({\\n        \"chunk_idx\": chunk_idx,\\n        \"chunk_count\": chunk_count,\\n        \"chunk_payload\": chunk_payload,\\n    })\\n    chunk_idx += 1\\n    if chunk_idx < chunk_count:\\n        chunk_payload = payload[chunk_idx * chunk_size:]\\n        requests.append({\\n            \"chunk_idx\": chunk_idx,\\n            \"chunk_count\": chunk_count,\\n            \"chunk_payload\": chunk_payload,\\n        })\\n\\n    return requests'}\n"]}], "source": ["# template.parse_response(raw_outputs)\n", "# pattern = template.output_template.format(key=\".*\", val=\"\")\n", "# print(pattern)\n", "print(template.parse_response(raw_outputs))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["return requests\n", "\n", "[[[[ Exemplar-4 ]]]]\n", ">>>>> Code <<<<<\n", "import os\n", "\n", "\n", "def get_file_contents(filename):\n", "    \"\"\"Return the contents of a file.\"\"\"\n", "    with open(filename, \"r\") as f:\n", "        return f.read()\n", "\n", "\n", "def get_file_contents_with_encoding(filename, encoding):\n", "    \"\"\"Return the contents of a file.\"\"\"\n", "    with open(filename, \"r\", encoding=encoding) as f:\n", "        return f.read()\n", "\n", "\n", "def get_file_contents_with_encoding_and_encoding_errors(filename, encoding):\n", "    \"\"\"Return the contents of a file.\"\"\"\n", "    with open(filename, \"r\", encoding=encoding) as f:\n", "        return f.read()\n", "\n", "\n", "def get_file_contents_with_encoding_and_errors(filename, encoding):\n", "    \"\"\"Return the contents of a file.\"\"\"\n", "    with open(filename, \"r\", encoding=encoding) as f:\n", "        return f.read()\n", "\n", "\n", "def get_file_contents_with_errors(filename):\n"]}], "source": ["print(raw_outputs.strip)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/src/augment/research/eval/vulcan/testdata/py/example4_research_models_generate.py\n", "{'tags': ['fim'], 'label': 'complete-func-via-ref', 'CoT': 'complete the function based on the class defination', 'results': OrderedDict([('StarCoder Base', '    prefix_toks = model.tokenizer.encode(prefix)\\n    suffix_toks = model.generate(prefix_toks, [])\\n    suffix = model.tokenizer.decode(suffix_toks)\\n    print(f\\'Generated completion: {suffix}\\')\\n\\ndef load_model(model_name: str) -> ResearchModel:\\n    \"\"\"Load the model with the given name.\"\"\"\\n    if model_name == \\'gpt2\\':\\n        return GPT2Model()\\n    else:\\n        raise ValueError(f\\'Unknown model name: {model_name}\\')\\n\\nif __name__ == \\'__main__\\':\\n    model = load_model(\\'gpt2\\')\\n    model.load()\\n    generate_completion(model, \\'The \\')'), ('StarCoder Plus', '    prefix_toks = model.tokenizer.encode(prefix)\\n    suffix_toks = model.generate(prefix_toks, [])\\n    suffix = model.tokenizer.decode(suffix_toks)\\n    print(suffix)\\n\\ndef main():\\n    \"\"\"Main entry point.\"\"\"\\n    parser = argparse.ArgumentParser()\\n    parser.add_argument(\\'model\\', type=str, help=\\'The model to use.\\')\\n    parser.add_argument(\\'prefix\\', type=str, help=\\'The prefix to use.\\')\\n    args = parser.parse_args()\\n\\n    model = ResearchModel.load(args.model)\\n    generate_completion(model, args.prefix)\\n\\nif __name__ == \\'__main__\\':\\n    main()'), ('StarCoder Ours', '    prefix_toks = model.tokenizer.encode(prefix)\\n    suffix_toks = model.generate(prefix_toks, [])\\n    suffix = model.tokenizer.decode(suffix_toks)\\n    print(suffix)')]), 'extra_headers': OrderedDict([('StarCoder Base', 'StarCoder Base, t=0.0, max_tokens=256'), ('StarCoder Plus', 'StarCoder Plus, t=0.0, max_tokens=256'), ('StarCoder Ours', 'StarCoder Ours, t=0.0, max_tokens=256')])}\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(f\"{prefix} {suffix}\")\n", "\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(f'Generated completion: {suffix}')\n", "\n", "def load_model(model_name: str) -> ResearchModel:\n", "    \"\"\"Load the model with the given name.\"\"\"\n", "    if model_name == 'gpt2':\n", "        return GPT2Model()\n", "    else:\n", "        raise ValueError(f'Unknown model name: {model_name}')\n", "\n", "if __name__ == '__main__':\n", "    model = load_model('gpt2')\n", "    model.load()\n", "    generate_completion(model, 'The ')\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(suffix)\n", "\n", "def main():\n", "    \"\"\"Main entry point.\"\"\"\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument('model', type=str, help='The model to use.')\n", "    parser.add_argument('prefix', type=str, help='The prefix to use.')\n", "    args = parser.parse_args()\n", "\n", "    model = ResearchModel.load(args.model)\n", "    generate_completion(model, args.prefix)\n", "\n", "if __name__ == '__main__':\n", "    main()\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.generate(prefix_toks, [])\n", "    suffix = model.tokenizer.decode(suffix_toks)\n", "    print(suffix)\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 2 examples.\n", "========================================================================================\n", "EXAMPLE: example4_research_models_generate.py ✗ | llama2_13b, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "class ResearchModel:\n", "    \"\"\"A general research model interface.\"\"\"\n", "    @abstractmethod\n", "    def load(self):\n", "        \"\"\"Load the model.\"\"\"\n", "\n", "    @abstractmethod\n", "    def generate(self, prefix_toks: list[int], suffix_toks: list[int], path=None, chunk_filter=None) -> list[int]:\n", "        \"\"\"Generate a completion based on prefix and suffix.\"\"\"\n", "\n", "    @property\n", "    @abstractmethod\n", "    def tokenizer(self):\n", "        \"\"\"The tokenizer associated with this model.\n", "\n", "        The tokenizer should have a encode(text) method that returns a\n", "        list of integer token IDs, and a decode(tokens) method that\n", "        returns the text string.\n", "        \"\"\"\n", "\n", "def generate_completion(model: ResearchModel, prefix: str) -> None:\n", "    \"\"\"Generate a completion for the given prefix.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31mmodel.load()\n", "    prefix_toks = model.tokenizer.encode(prefix)\n", "    suffix_toks = model.tokenizer.encode(prefix)\n", "    completions = model.generate(prefix_toks, suffix_toks)\n", "    for completion in completions:\n", "        print(model.tokenizer.decode(completion))\u001b[0m\u001b[32m\n", "# EOF\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["_ = run_single_with_save(1, display=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_argparse.py\n", "{'tags': ['fim'], 'label': 'argparse', 'CoT': 'The code completion should be about how to define argument with argpar<PERSON>.', 'max_generated_tokens': 128, 'results': OrderedDict([('StarCoder Base', ''), ('StarCoder Plus', ''), ('StarCoder Ours', '')]), 'extra_headers': OrderedDict([('StarCoder Base', 'StarCoder Base, t=0.0, max_tokens=140'), ('StarCoder Plus', 'StarCoder Plus, t=0.0, max_tokens=140'), ('StarCoder Ours', 'StarCoder Ours, t=0.0, max_tokens=140')])}\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m    default=False,\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "Selected 1 examples.\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | llama2_13b, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31mhelp=\"whether to return single-line completions\",\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["_ = run_single_with_save(2, display=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}