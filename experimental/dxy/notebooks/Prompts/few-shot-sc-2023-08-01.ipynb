{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import os\n", "import collections\n", "import pathlib\n", "from typing import cast\n", "from research.models.core import Remote_Model\n", "from research.core.model_input import ModelInput\n", "from research.models.core import StopCriteria, GenerationOptions\n", "\n", "models = collections.OrderedDict()\n", "models[\"StarCoder Base\"] = Remote_Model(\"http://216.153.49.205:5001\")\n", "models[\"StarCoder Plus\"] = Remote_Model(\"http://216.153.49.205:5002\")\n", "models[\"StarCoder Ours\"] = Remote_Model(\"http://216.153.49.205:5004\")\n", "models[\"llama2_13b\"] = Remote_Model(\"http://216.153.49.205:5003\")\n", "# models[\"llama2_70b\"] = Remote_Model(\"http://216.153.49.205:5003\")\n", "base_keys = [\"StarCoder Base\", \"StarCoder Plus\", \"StarCoder Ours\"]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/go/structure_2.go\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/infer_preconditions.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/remove_duplicates.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_multi_function_2.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_class_1.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_class_2.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_3.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_4.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_real_ex_5.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_script.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_trailing_return_with_var.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_4.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_leading_spaces.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_multi_function_1.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_noop.py\n", "Ignore /home/<USER>/src/augment/research/eval/vulcan/testdata/py/archived/fim_trailing_return.py\n", "In total, there are 42 tests and skipped 16 tests.\n", "There are 6 with cpp extension.\n", "There are 3 with go extension.\n", "There are 17 with py extension.\n", "Loaded 17 tests.\n", "[00] [#char: 284] chunk_request.py                    {'tags': ['arithmetic', 'instruction-following']}\n", "[01] [#char: 180] example4_research_models_generate.py {'tags': ['fim'], 'label': 'complete-via-ref'}\n", "[02] [#char:  19] fim_argparse.py                     {'tags': ['fim'], 'CoT': 'The code completion should be about how to define argument with argparse.', 'max_generated_tokens': 128}\n", "[03] [#char: 245] fim_dataclass_attrs.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[04] [#char:  14] fim_dataclass_field.py              {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[05] [#char:  92] fim_dataclass_methods.py            {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[06] [#char:  49] fim_docstring_middle_fix.py         {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[07] [#char:  70] fim_multi_node_missing.py           {'tags': ['fim'], 'max_generated_tokens': 128}\n", "[08] [#char: 108] resources_1.py                      {'tags': ['resource-usage']}\n", "[09] [#char:  70] structure_1.py                      {'tags': ['struct', 'infer-usage']}\n", "[10] [#char: 136] structure_2.py                      {'tags': ['struct', 'algorithm', 'recursion']}\n", "[11] [#char: 152] structure_3.py                      {'tags': ['struct', 'algorithm', 'time']}\n", "[12] [#char:  70] structure_5.py                      {'tags': ['struct', 'infer-usage']}\n", "[13] [#char:  22] structure_6.py                      {'tags': ['struct', 'infill', 'string-formation']}\n", "[14] [#char:  86] structure_7.py                      {'tags': ['struct', 'string-formation']}\n", "[15] [#char:  87] testing_1.py                        {'tags': ['testing', 'test-setup']}\n", "[16] [#char: 118] testing_2.py                        {'tags': ['testing', 'test-data']}\n", "Use the saved cache results from /home/<USER>/checkpoints/cache/prompts and baseline\n", "Find cache /home/<USER>/checkpoints/cache/prompts/chunk_request.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/example4_research_models_generate.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_argparse.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_attrs.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_field.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_dataclass_methods.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_docstring_middle_fix.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/fim_multi_node_missing.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/resources_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_2.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_3.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_5.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_6.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/structure_7.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/testing_1.py-baseline and loading...\n", "Find cache /home/<USER>/checkpoints/cache/prompts/testing_2.py-baseline and loading...\n"]}], "source": ["from copy import deepcopy\n", "from experimental.dxy.code.few_shot_helper import FewshotTemplate, dict_by_any\n", "from experimental.dxy.code.vulcan_helper import display_output\n", "from experimental.dxy.code.vulcan_helper import display_cache\n", "from experimental.dxy.code.vulcan_helper import load_tests\n", "from experimental.dxy.code.vulcan_helper import VulcanTest\n", "from experimental.dxy.code.file_helper import pickle_load, pickle_save\n", "\n", "\n", "CACHE_SAVE_DIR = '/home/<USER>/checkpoints/cache/prompts'\n", "all_tests = load_tests(cache_folder=CACHE_SAVE_DIR, cache_suffix='baseline')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "def run_norm(\n", "    model_key: str, test: VulcanTest, temperature=0.0, display: bool = False\n", ") -> tuple[str, str]:\n", "    input = ModelInput(prefix=test.prefix, suffix=test.suffix)\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    result = models[model_key].generate(input, options)\n", "    extra_header = f\"{model_key}, t={temperature:.1f}, max_tokens={max_generated_tokens}\"\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=extra_header,\n", "        )\n", "    return result, extra_header\n", "\n", "def run_all_models(index: int, save: bool = True) -> VulcanTest:\n", "    test = deepcopy(all_tests[index])\n", "    if False:\n", "        display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "    for key in base_keys:\n", "        result, extra_header = run_norm(key, test, display=False)\n", "        test.meta['results'][key] = result\n", "        test.meta['extra_headers'][key] = extra_header\n", "    if save:\n", "        filename = os.path.basename(test.filename)\n", "        pickle_save(test, os.path.join(CACHE_SAVE_DIR, f'{filename}-baseline'))\n", "    return test"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluate index=0 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/chunk_request.py\n", "Evaluate index=1 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_argparse.py\n", "Evaluate index=2 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_dataclass_attrs.py\n", "Evaluate index=3 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_dataclass_field.py\n", "Evaluate index=4 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_dataclass_methods.py\n", "Evaluate index=5 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_docstring_middle_fix.py\n", "Evaluate index=6 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/fim_multi_node_missing.py\n", "Evaluate index=7 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/resources_1.py\n", "Evaluate index=8 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_1.py\n", "Evaluate index=9 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_2.py\n", "Evaluate index=10 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_3.py\n", "Evaluate index=11 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_5.py\n", "Evaluate index=12 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_6.py\n", "Evaluate index=13 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/structure_7.py\n", "Evaluate index=14 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/testing_1.py\n", "Evaluate index=15 : /home/<USER>/src/augment/research/eval/vulcan/testdata/py/testing_2.py\n"]}], "source": ["for index in range(len(all_tests)):\n", "    print(f'Evaluate index={index} : {all_tests[index].filename}')\n", "    test = run_all_models(index)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: chunk_request.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size: (chunk_idx + 1) * chunk_size]\n", "        requests.append({\n", "            'chunk_count': chunk_count,\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_payload': chunk_payload,\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Base, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Plus, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        start = chunk_idx * chunk_size\n", "        end = start + chunk_size\n", "        requests.append({\n", "            'chunk_idx': chunk_idx,\n", "            'chunk_count': chunk_count,\n", "            'chunk_payload': payload[start:end],\n", "        })\n", "\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: chunk_request.py ✗ | StarCoder Ours, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport math\n", "\n", "\n", "def make_chunked_requests(payload, chunk_size):\n", "    \"\"\"Chunk a payload into `chunk_size` portions and make requests for them.\n", "\n", "    Args:\n", "        payload: the payload to chunk and send.\n", "\n", "    Returns:\n", "        A list of requests; each request is a dictionary with keys:\n", "            chunk_idx: the index of the current chunk.\n", "            chunk_count: how many chunks are required in total.\n", "            chunk_payload: the chunk payload.\n", "    \"\"\"\n", "    requests = []\n", "    chunk_count = math.ceil(len(payload) / chunk_size)\n", "\u001b[0m\u001b[40m\u001b[31m    for chunk_idx in range(chunk_count):\n", "        chunk_payload = payload[chunk_idx * chunk_size : (chunk_idx + 1) * chunk_size]\n", "        requests.append(\n", "            {\n", "                \"chunk_idx\": chunk_idx,\n", "                \"chunk_count\": chunk_count,\n", "                \"chunk_payload\": chunk_payload,\n", "            }\n", "        )\u001b[0m\u001b[32m    return requests\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["def show_cache_by_index(index: int):\n", "    test = all_tests[index]\n", "    filename = os.path.basename(test.filename)\n", "    display_cache(os.path.join(CACHE_SAVE_DIR, f\"{filename}-baseline\"))\n", "\n", "show_cache_by_index(0)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_argparse.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m    default=False,\n", "\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_argparse.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34<PERSON><PERSON><PERSON> argparse\n", "\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\n", "    \"--model\",\n", "    type=str,\n", "    default=\"my_model\",\n", "    help=\"which model to serve\",\n", ")\n", "parser.add_argument(\n", "    \"--disable_completions\",\n", "    action=\"store_true\",\n", "\u001b[0m\u001b[40m\u001b[31m\u001b[0m\u001b[32m    help=\"disable completion requests\",\n", ")\n", "parser.add_argument(\n", "    \"--single_line_completions\",\n", "    action=\"store_true\",\n", "    help=\"whether to return single-line completions\",\n", ")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(1)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✓ | Ground Truth\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m    char_start: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    char_end: int = 0\n", "    \"\"\"Character offset for the blob.\"\"\"\n", "    _extra: Mapping = field(default_factory=dict, repr=True, init=True)\n", "    \"\"\"A handy field to store extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Base, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The character offset of the start of the data.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The character offset of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Plus, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_end: int = 0\n", "    \"\"\"The character index of the end of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra data.\"\"\"\n", "\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: fim_dataclass_attrs.py ✗ | StarCoder Ours, t=0.0, max_tokens=140\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mfrom dataclasses import dataclass, field\n", "from typing import *\n", "\n", "\n", "@dataclass\n", "class Datum:\n", "    \"\"\"Represents a blob of data.\"\"\"\n", "\n", "    source: str = \"\"\n", "    \"\"\"Where the data comes from.\"\"\"\n", "\u001b[0m\u001b[40m\u001b[31m\n", "    char_start: int = 0\n", "    \"\"\"The starting character of the data.\"\"\"\n", "\n", "    char_end: int = 0\n", "    \"\"\"The ending character of the data.\"\"\"\n", "\n", "    _extra: Dict[str, Any] = field(default_factory=dict)\n", "    \"\"\"Extra information about the data.\"\"\"\u001b[0m\u001b[32m\n", "\n", "def example_datum():\n", "    return Datum(\n", "        source=\"example\",\n", "        char_end=5,\n", "        _extra={\"a\": 1},\n", "    )\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["show_cache_by_index(2)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def convert_v0(x: VulcanTest, separator: str = \"<FILL-IN-THE-MIDDLE>\"):\n", "    return {\n", "        \"code\": f\"{x.prefix}{separator}{x.suffix}\",\n", "        \"Content to replace <FILL-IN-THE-MIDDLE>\": x.expected,\n", "    }\n", "\n", "\n", "template_v0 = FewshotTemplate(\n", "    input_keys=[\"code\"],\n", "    output_keys=[\"Content to replace <FILL-IN-THE-MIDDLE>\"],\n", "    ex_sep=\"\\n\",\n", "    transfer_fn=convert_v0,\n", "    task_prefix=(\n", "        \"Given a piece of code, where the middle content is missing and marked by <FILL-IN-THE-MIDDLE>.\"\n", "        \" Please help write a concise, brief and smart completion for its potential middle content.\"\n", "        \" Note that the middle might be empty.\\n\"\n", "    ),\n", ")\n", "template = template_v0\n", "# template = FewshotTemplate(\n", "#     input_keys=[\"prefix\", \"suffix\"],\n", "#     output_keys=[\"expected\"],\n", "#     key_rename_map={\"expected\": \"Expected Middle Content\"},\n", "#     ex_sep=\"\\n\",\n", "#     task_prefix=(\n", "#         \"Given the prefix and the suffix of a code file, please\"\n", "#         \" help write a concise, brief and smart completion for its potential middle content.\"\n", "#         \" Note that the middle might be empty.\"\n", "#     ),\n", "# )\n", "\n", "\n", "def run_fewshot(\n", "    model_key: str,\n", "    test: VulcanTest,\n", "    examples: list[VulcanTest],\n", "    temperature=0.0,\n", "    display: bool = False,\n", "    debug: bool = False,\n", "):\n", "    max_generated_tokens = 256\n", "    if \"max_generated_tokens\" in test.meta:\n", "        max_generated_tokens = cast(float, test.meta[\"max_generated_tokens\"])\n", "        max_generated_tokens = int(max_generated_tokens * 1.1)\n", "    options = GenerationOptions(\n", "        temperature=temperature, max_generated_tokens=max_generated_tokens\n", "    )\n", "    prompt = template.get_prompt(test, examples)\n", "    if debug:\n", "        print(f\"Prompt:\\n{prompt}\")\n", "    input = ModelInput(prefix=prompt)\n", "    raw_result = models[model_key].generate(input, options)\n", "    result = template.parse_response(raw_result)\n", "    result = list(result.values())[0]\n", "    if display:\n", "        display_output(\n", "            test,\n", "            result,\n", "            extra_header=f\"{model_key}, t={temperature:.1f}, max_tokens={max_generated_tokens}\",\n", "        )\n", "    return raw_result, result\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: structure_1.py ✗ | llama2_13b, t=0.0, max_tokens=256\n", "----------------------------------------------------------------------------------------\n", "\u001b[34mimport dataclasses\n", "\n", "@dataclasses.dataclass\n", "class Person:\n", "    name: str\n", "    age: int\n", "\u001b[0m\u001b[40m\u001b[31mfor parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\u001b[0m\u001b[32m\n", "\n", "def get_ancestors(person: Person):\n", "    ret = []\n", "    for parent in person.parents:\n", "        ret.append(parent)\n", "        ret.extend(get_ancestors(parent))\n", "    return ret\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}, {"data": {"text/plain": ["(\"   for parent in person.parents:\\n        ret.append(parent)\\n        ret.extend(get_ancestors(parent))\\n    return ret\\n\\n[[[[ Exemplar-4 ]]]]\\n>>>>> Code <<<<<\\nimport dataclasses\\n\\<EMAIL>\\nclass Person:\\n    name: str\\n    age: int\\n    parents: list['Person']\\n\\n\\ndef get_ancestors(person: Person):\\n    ancestors = []\\n<FILL-IN-THE-MIDDLE>\\n\\n>>>>> Content To Replace <Fill-In-The-Middle> <<<<<\\n    for parent in person.parents:\\n        ancestors.append(parent)\\n        ancestors.extend(get_ancestors(parent))\\n    return ancestors\\n\\n[[[[ Exemplar-5 ]]]]\\n>>>>> Code <<<<<\\nimport dataclasses\\n\\<EMAIL>\\nclass Person:\\n    name: str\\n    age: int\\n    parents: list['Person']\\n\\n\\ndef get_ancestors(person: Person):\\n    ancestors = []\",\n", " 'for parent in person.parents:\\n        ret.append(parent)\\n        ret.extend(get_ancestors(parent))\\n    return ret')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# run_fewshot(\"StarCoder Base\", all_tests[9], [all_tests[10], all_tests[11]], display=True)\n", "run_fewshot(\"llama2_13b\", all_tests[9], [all_tests[10], all_tests[11]], display=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(TESTS[0].filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "def run_all_models(index: int, select: int = 2, ignore_base: bool = False):\n", "    test = TESTS[index]\n", "    display_output(test, model_output=None, extra_header=\"Ground Truth\")\n", "    for key in base_keys:\n", "        if not ignore_base:\n", "            _ = run_norm(key, test, display=True)\n", "    total = len(TESTS)\n", "    selected_indexes = random.sample([i for i in range(total) if i != index], select)\n", "    selected_tests = [TESTS[idx] for idx in selected_indexes]\n", "    _ = run_fewshot(\"llama2_13b\", test, selected_tests, display=True, debug=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(5, ignore_base=False, select=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(6, ignore_base=False, select=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["run_all_models(7, ignore_base=False, select=3)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xmodel = models[\"llama2_70b\"]\n", "\n", "# input = ModelInput(\n", "#     prefix=\"def hello_world\", suffix=\"if __name__ == '__main__':\\n    hello_world()\"\n", "# )\n", "# option = GenerationOptions(temperature=0.0, max_generated_tokens=256)\n", "\n", "# for key, model in models.items():\n", "#     if key.startswith('llama2'):\n", "#         xinput = input.rebind(suffix='')\n", "#     else:\n", "#         xinput = input\n", "#     result = model.generate(input, option)\n", "#     print(result)\n", "#     print('----')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}