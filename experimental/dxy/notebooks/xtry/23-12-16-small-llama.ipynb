{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "model_path=/mnt/efs/augment/user/carl/checkpoints/llama-350M-1Ttokens-starcoder/tokenizer.model is not a file.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 7\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01m<PERSON>earch\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodels\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mllama2_models\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m LLAMA2Model\n\u001b[1;32m      4\u001b[0m model \u001b[38;5;241m=\u001b[39m LLAMA2Model(\n\u001b[1;32m      5\u001b[0m     pathlib\u001b[38;5;241m.\u001b[39mPath(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/mnt/efs/augment/user/carl/checkpoints/llama-350M-1Ttokens-starcoder\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      6\u001b[0m )\n\u001b[0;32m----> 7\u001b[0m \u001b[43mmodel\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/src/augment/research/models/llama2_models.py:188\u001b[0m, in \u001b[0;36mLLAMA2Model.load\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    186\u001b[0m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgeneration_mode\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m    187\u001b[0m model_args: ModelArgs \u001b[38;5;241m=\u001b[39m ModelArgs(\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mparams)\n\u001b[0;32m--> 188\u001b[0m tokenizer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_tokenizer\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    189\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvocab_size\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m params \u001b[38;5;129;01mor\u001b[39;00m params[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvocab_size\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m<\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[1;32m    190\u001b[0m     \u001b[38;5;66;03m# Infer vocab size from tokenizer.\u001b[39;00m\n\u001b[1;32m    191\u001b[0m     model_args\u001b[38;5;241m.\u001b[39mvocab_size \u001b[38;5;241m=\u001b[39m tokenizer\u001b[38;5;241m.\u001b[39mn_words  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n", "File \u001b[0;32m~/src/augment/research/models/llama2_models.py:160\u001b[0m, in \u001b[0;36mLLAMA2Model.load_tokenizer\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    158\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcheckpoint_path \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    159\u001b[0m _tokenizer_path \u001b[38;5;241m=\u001b[39m pathlib\u001b[38;5;241m.\u001b[39mPath(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcheckpoint_path) \u001b[38;5;241m/\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtokenizer.model\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 160\u001b[0m tokenizer \u001b[38;5;241m=\u001b[39m \u001b[43mget_tokenizer\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mLlama2Tokenizer\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvocab_file\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m_tokenizer_path\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    161\u001b[0m \u001b[38;5;66;03m# Reset the tokenizer to be the same as the loaded model.\u001b[39;00m\n\u001b[1;32m    162\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprompt_formatter\u001b[38;5;241m.\u001b[39mrebind(bos_id\u001b[38;5;241m=\u001b[39mtokenizer\u001b[38;5;241m.\u001b[39mbos_id, tokenizer\u001b[38;5;241m=\u001b[39mtokenizer)  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n", "File \u001b[0;32m~/src/augment/research/gpt-neox/megatron/tokenizer/tokenizer.py:1254\u001b[0m, in \u001b[0;36mget_tokenizer\u001b[0;34m(tokenizer_type, vocab_file, merge_file)\u001b[0m\n\u001b[1;32m   1252\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m Llama2Tokenizer()\n\u001b[1;32m   1253\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1254\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mLlama2Tokenizer\u001b[49m\u001b[43m(\u001b[49m\u001b[43mvocab_file\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1255\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m tokenizer_type\u001b[38;5;241m.\u001b[39mlower() \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCodeLLaMATokenizer\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mlower():\n\u001b[1;32m   1256\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m vocab_file \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m:\n", "File \u001b[0;32m~/src/augment/research/gpt-neox/megatron/tokenizer/tokenizer.py:864\u001b[0m, in \u001b[0;36mLlama2Tokenizer.__init__\u001b[0;34m(self, model_path)\u001b[0m\n\u001b[1;32m    862\u001b[0m \u001b[38;5;66;03m# reload tokenizer\u001b[39;00m\n\u001b[1;32m    863\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m Path(model_path)\u001b[38;5;241m.\u001b[39mis_file():\n\u001b[0;32m--> 864\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel_path=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m is not a file.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    865\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msp_model \u001b[38;5;241m=\u001b[39m spm\u001b[38;5;241m.\u001b[39mSentencePieceProcessor(model_file\u001b[38;5;241m=\u001b[39mmodel_path)  \u001b[38;5;66;03m# type: ignore\u001b[39;00m\n\u001b[1;32m    866\u001b[0m logging\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReloaded SentencePiece model from \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mValueError\u001b[0m: model_path=/mnt/efs/augment/user/carl/checkpoints/llama-350M-1Ttokens-starcoder/tokenizer.model is not a file."]}], "source": ["import pathlib\n", "from research.models.llama2_models import LLAMA2Model\n", "\n", "model = LLAMA2Model(\n", "    pathlib.Path(\"/mnt/efs/augment/user/carl/checkpoints/llama-350M-1Ttokens-starcoder\")\n", ")\n", "model.load()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}