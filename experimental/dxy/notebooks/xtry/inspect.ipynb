{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "\n", "all_data = read_jsonl_zst(\"/mnt/efs/augment/data/eval/hindsight/2024-03-15-v0.5/dogfood/data.jsonl.zst\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4354\n", "dict_keys(['completion', 'ground_truth'])\n", "dict_keys(['request_id', 'user_id', 'request', 'response', 'resolution', 'feedback'])\n", "18\n"]}], "source": ["print(len(all_data))\n", "x = all_data[0]\n", "print(x.keys())\n", "print(x[\"completion\"].keys())\n", "users = [x[\"completion\"][\"user_id\"] for x in all_data]\n", "print(len(set(users)))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'jacqueline', 'xia<PERSON>i', 'dion', '<EMAIL>', 'zhewei', 'mlm', 'carl', 'liam', 'joel', 'a<PERSON><PERSON><PERSON>', 'ran', 'guy', 'marc<PERSON><EMAIL>', 'jiayi', 'msdejong', 'z<PERSON><PERSON>@augmentcode.com', 'markus', 'igoros'}\n"]}], "source": ["print(set(users))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}