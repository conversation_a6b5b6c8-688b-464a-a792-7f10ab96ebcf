{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file\n", "from research.eval.harness.tasks.api_call_task import detailed_error_analysis\n", "\n", "cache_dir = pathlib.Path(\"/mnt/efs/augment/eval/jobs/hnE92mKm/dxy-8-a40__RAGSystem_ApiCallTask/cache\")\n", "all_results = []\n", "for json_file in cache_dir.glob(\"*-*.json\"):\n", "    cur_result = utils_for_file.read_json(json_file)\n", "    all_results.append(cur_result)\n", "print(f\"Loaded {len(all_results)} results\")\n", "report = detailed_error_analysis(all_results)\n", "print(report)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.models.core import HuggingFaceModel\n", "\n", "# ckp_dir = '/mnt/efs/augment/checkpoints/llama/llama-2-13b-hf'\n", "ckp_dir = '/home/<USER>/checkpoints/llama-2-13b-hf'\n", "model = HuggingFaceModel(pathlib.Path(ckp_dir))\n", "print(model)\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.core import StopCriteria, GenerationOptions\n", "from research.core.model_input import ModelInput\n", "\n", "model.generate(ModelInput(prefix=\"1 2 3\"), GenerationOptions(max_generated_tokens=120))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["type(model.tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = model.cuda()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xx = AutoTokenizer.from_pretrained('/mnt/efs/augment/checkpoints/starcoderbase/')"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}