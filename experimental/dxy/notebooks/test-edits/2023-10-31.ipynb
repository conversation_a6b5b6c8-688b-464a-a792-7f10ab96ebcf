{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# This notebook is used to quickly test a few representative edit demos"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Case #1\n", "\n", "Possible Instructions:\n", "- use pathlib library to open file\n", "- add missing import"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Case #2\n", "\n", "Possible Instructions:\n", "- delete these comments\n", "- Use single quotes instead of double quotes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Prepare Data for Edit.\"\"\"\n", "import logging\n", "import typing\n", "\n", "# import tenacity\n", "import openai\n", "from openai import openai_object\n", "\n", "# from openai import error as openai_error\n", "\n", "OpenAIChatModels = typing.Literal[\"gpt-4\", \"gpt-3.5-turbo\"]\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "# @tenacity.retry(\n", "#     retry=tenacity.retry_if_exception_type(openai_error.APIError),\n", "#     reraise=True,\n", "#     wait=tenacity.wait_random(1.0, 2.0),\n", "#     stop=tenacity.stop_after_attempt(3),\n", "#     before=tenacity.before_log(logger, logging.INFO),\n", "# )\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = \"gpt-4\",\n", ") -> str:\n", "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = \"user\" if idx == 0 else \"assistant\"\n", "        request_messages.append({\"role\": role, \"content\": message})\n", "    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "    )\n", "    response = typing.cast(openai_object.OpenAIObject, response)\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Case #3\n", "\n", "Possible Instructions:\n", "- rename load_data to temp_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import logging\n", "import pathlib\n", "\n", "import torch\n", "import tqdm\n", "\n", "from experimental.dxy.exps.system_lib import get_system\n", "from research.core.model_input import ModelInput\n", "from research.core.types import CharRange, Document\n", "from research.core.utils import Timer\n", "from research.eval.dataset_generation_lib import (\n", "    advanced_snippet_generators as patch_generate_lib,\n", ")\n", "from research.retrieval.utils import Span\n", "from research.static_analysis.experimental_parsing import (\n", "    AugmentedParsedFile,\n", "    AugmentedTSNode,\n", ")\n", "\n", "\n", "def parse_cmd():\n", "    \"\"\"Parse command line args.\"\"\"\n", "    parser = argparse.ArgumentParser()\n", "\n", "    # System configuration\n", "    parser.add_argument(\n", "        \"--data_dir\",\n", "        type=str,\n", "        required=True,\n", "        help=\".\",\n", "    )\n", "    parser.add_argument(\n", "        \"--source_doc_name\",\n", "        type=str,\n", "        required=True,\n", "        help=\"\",\n", "    )\n", "    parser.add_argument(\n", "        \"--target_doc_name\",\n", "        type=str,\n", "        required=True,\n", "        help=\"\",\n", "    )\n", "    parser.add_argument(\n", "        \"--system_name\",\n", "        type=str,\n", "        required=True,\n", "        nargs=\"+\",\n", "        help=\"\",\n", "    )\n", "\n", "    return parser.parse_args()\n", "\n", "\n", "def prepare_to_start(output_dir: pathlib.Path):\n", "    \"\"\"Prepare the output directory and the logging file.\"\"\"\n", "    log_file = output_dir / (\n", "        pathlib.Path(__file__).stem\n", "        + \"-\"\n", "        + Timer.time_string().replace(\"[\", \"\").replace(\"]\", \"\").replace(\" \", \"-\")\n", "        + \".log\"\n", "    )\n", "    logger = logging.Logger(__name__)\n", "    handlers = (logging.FileHandler(log_file), logging.StreamHandler())\n", "    formatter = logging.Formatter(\n", "        \"%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s\"\n", "    )\n", "    for handler in handlers:\n", "        handler.setLevel(logging.INFO)\n", "        handler.set<PERSON><PERSON><PERSON><PERSON>(formatter)\n", "        logger.add<PERSON><PERSON><PERSON>(handler)\n", "    logger.info(f\"The output log file is : {log_file}\")\n", "    return logger\n", "\n", "\n", "def main(args):\n", "    \"\"\"The real main function.\"\"\"\n", "    args = parse_cmd()\n", "    output_dir = pathlib.Path(args.data_dir)\n", "    if not output_dir.exists():\n", "        raise ValueError(f\"{output_dir} does not exist.\")\n", "\n", "    real_output_dir: pathlib.Path = output_dir / (\n", "        args.target_doc_name + \"-\" + \"-\".join(args.system_name)\n", "    )\n", "    real_output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    logger = prepare_to_start(real_output_dir)\n", "    logger.info(f\"Args: {args}\")\n", "    logger.info(f\"Save everything into {real_output_dir}\")\n", "\n", "    load_data = torch.load(output_dir / args.source_doc_name)\n", "    doc_dicts, image_name = load_data[\"documents\"], load_data[\"image_name\"]\n", "    repo_for_system: list[Document] = load_data[\"repo_for_system\"]\n", "\n", "    augmented_docs: list[AugmentedParsedFile] = []\n", "    for doc_dict in doc_dicts:\n", "        augmented_docs.append(AugmentedParsedFile.from_dict(doc_dict))\n", "    logger.info(\n", "        f\"Load {len(doc_dicts)} documents with\"\n", "        f\" {patch_generate_lib.get_number_of_nodes(augmented_docs)} nodes from {args.source_doc_name}\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Case #4\n", "\n", "- use string format() instead of f-string"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    # Create system(s)\n", "    systems = [get_system(name) for name in args.system_name]\n", "    for idx, system in enumerate(systems):\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.repr\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(str(system) + \"\\n\")\n", "        with (real_output_dir / f\"system-{idx}-of-{len(systems)}.json\").open(\n", "            \"w\"\n", "        ) as xfile:\n", "            xfile.write(system.ui_to_json())\n", "    for system in systems:\n", "        system.load()\n", "        system.add_docs(repo_for_system)\n", "    logger.info(f\"Finish loading the system, output_dir={real_output_dir}\")\n", "    logger.info(f\"Add the repo with {len(repo_for_system)} documents.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Case #5\n", "\n", "Possible Instructions:\n", "- In the following code, make the for loop enumerate\n", "- enumerate this for loop\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for sample in samples:\n", "    instruction = sample[\"message\"]\n", "    lines_before = sample[\"old_contents\"].splitlines(keepends=True)\n", "    lines_after = sample[\"new_contents\"].splitlines(keepends=True)\n", "    result = differ.compare(lines_before, lines_after)\n", "    print(\"\\n=\" * 80)\n", "    print(f\"[{i}] instruction: {instruction}\")\n", "    for line in result:\n", "        if line.startswith(\"-\"):\n", "            print(Fore.RED + line, end=\"\")\n", "        elif line.startswith(\"+\"):\n", "            print(Fore.GREEN + line, end=\"\")\n", "        else:\n", "            print(line, end=\"\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}