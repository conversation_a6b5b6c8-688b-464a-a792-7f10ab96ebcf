{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import docker\n", "import pathlib\n", "import tempfile\n", "import tarfile\n", "import dataclasses\n", "import xml.etree.ElementTree as xml_et\n", "from typing import Optional\n", "from research.static_analysis.common import LanguageID\n", "from research.fim.completion_sites import get_supported_extensions\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "\n", "\n", "@dataclasses.dataclass\n", "class CoverageElement:\n", "    filename: str\n", "    converage_rate: float\n", "    line2hit: dict\n", "\n", "\n", "def parse_pytest_coverage_xml(tree: xml_et.ElementTree) -> list[CoverageElement]:\n", "    # Parse the XML file\n", "    root = tree.getroot()\n", "\n", "    all_elements = []\n", "\n", "    # Loop through each 'class' element in the XML\n", "    for class_element in root.findall(\".//class\"):\n", "        # Extract filename\n", "        filename = class_element.get(\"filename\")\n", "        if filename is None:\n", "            continue\n", "\n", "        # Find the 'lines' element and calculate coverage\n", "        lines_element = class_element.find(\"lines\")\n", "        if lines_element is not None:\n", "            covered_lines, total_lines = 0, 0\n", "            line2hit = dict()\n", "\n", "            for line in lines_element:\n", "                if isinstance(line, xml_et.Element):\n", "                    number = line.get(\"number\")\n", "                    hits = line.get(\"hits\")\n", "                    if number is not None and hits is not None:\n", "                        total_lines += 1\n", "                        number, hits = int(number), int(hits)\n", "                        line2hit[number] = hits\n", "                        if hits > 0:\n", "                            covered_lines += 1\n", "\n", "            if total_lines > 0:\n", "                converage_rate = (covered_lines / total_lines) * 100\n", "            else:\n", "                converage_rate = 0.0\n", "\n", "            all_elements.append(CoverageElement(filename, converage_rate, line2hit))\n", "\n", "    return all_elements\n", "\n", "\n", "def parse_docs_from_docker_image(\n", "    image_name: str,\n", "    language: LanguageID = \"python\",\n", "    minimum_file_size_bytes: int = 0,\n", ") -> tuple[list[ParsedFile], list[CoverageElement]]:\n", "    docker_client = docker.from_env()\n", "\n", "    full_image_name = f\"au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/exec-eval/{image_name}\"\n", "    docker_client.images.pull(full_image_name)\n", "    container = docker_client.containers.create(full_image_name)\n", "    source_dir: str = \"/code\"\n", "    with tempfile.TemporaryDirectory() as target_dir:\n", "        target_dir = pathlib.Path(target_dir)\n", "        print(f\"Created temporary directory {target_dir}\")\n", "\n", "        # Create a tarball of the folder\n", "        bits, stat = container.get_archive(source_dir)\n", "\n", "        tar_filepath = pathlib.Path(\"code.tar\").resolve()\n", "        print(f\"Temporarily save the tarball into {tar_filepath}\")\n", "        # Save the tarball to disk\n", "        with tar_filepath.open(\"wb\") as xfile:\n", "            for chunk in bits:\n", "                xfile.write(chunk)\n", "\n", "        # Extract the tarball to the destination directory\n", "        with tarfile.open(str(tar_filepath), \"r\") as tar:\n", "            tar.extractall(path=str(target_dir))\n", "\n", "        # Remove the temporary tarball\n", "        os.remove(str(tar_filepath))\n", "\n", "        # Clean up: remove the container\n", "        container.remove()\n", "\n", "        # Try to load the coverage information\n", "        coverage_path = target_dir / \"code\" / \".hydra\" / \"coverage.xml\"\n", "        if coverage_path.exists():\n", "            coverages = parse_pytest_coverage_xml(xml_et.parse(str(coverage_path)))\n", "            print(\n", "                f\"Find the coverage xml file: {coverage_path} with {len(coverages)} coveraged files.\"\n", "            )\n", "        else:\n", "            print(f\"Did not find the coverage xml file: {coverage_path}.\")\n", "            coverages = []\n", "\n", "        # Collect all the documents.\n", "        avaliable_doc_paths = []\n", "        for path in (target_dir / \"code\").rglob(\"*\"):\n", "            if path.stat().st_size < minimum_file_size_bytes:\n", "                continue\n", "            if path.is_dir():\n", "                continue\n", "            if path.suffix in get_supported_extensions():\n", "                avaliable_doc_paths.append(path)\n", "        print(f\"There are {len(avaliable_doc_paths)} avaliable documents.\")\n", "        docs = []\n", "        for doc_path in avaliable_doc_paths:\n", "            relative_path = doc_path.relative_to(target_dir / \"code\")\n", "            current_doc = ParsedFile.parse(\n", "                relative_path, language, doc_path.read_text()\n", "            )\n", "            docs.append(current_doc)\n", "    return docs, coverages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents, coverages = parse_docs_from_docker_image(\"google/pyglove:v1.0\")\n", "filename2doc: dict[str, ParsedFile] = {}\n", "filename2coverage: dict[str, CoverageElement] = {}\n", "\n", "for doc in documents:\n", "    filename2doc[doc.scope_tree.name] = doc\n", "\n", "for coverage in coverages:\n", "    filename2coverage[coverage.filename] = coverage"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import tree_sitter as ts\n", "\n", "\n", "def find_node_by_types(\n", "    root_node: ts.<PERSON>, types: tuple[str, ...] = (\"call\",)\n", ") -> list[ts.<PERSON><PERSON>]:\n", "    target_nodes = []\n", "    unique_types = set()\n", "    queue = collections.deque([root_node])\n", "    while queue:\n", "        node = queue.popleft()\n", "        if node.type in types:\n", "            target_nodes.append(node)\n", "        unique_types.add(node.type)\n", "        queue.extend(node.children)\n", "    unique_types = sorted(list(unique_types))\n", "    # print(f\"unique types: {unique_types}\")\n", "    # print(f\"Find {len(target_nodes)} nodes.\")\n", "    return target_nodes\n", "\n", "\n", "filtered_documents: list[ParsedFile] = []\n", "for doc in documents:\n", "    filename = doc.scope_tree.name\n", "    if \"test\" in filename:\n", "        continue\n", "    if (\n", "        filename in filename2coverage\n", "        and filename2coverage[filename].converage_rate <= 0.0\n", "    ):\n", "        print(f\"Did not find any coverage information for {filename}\")\n", "        continue\n", "    filtered_documents.append(doc)\n", "print(f\"After filtering, there are {len(filtered_documents)} documents.\")\n", "\n", "filename2nodes: dict[str, list[ts.Node]] = {}\n", "for cur_doc in filtered_documents:\n", "    cur_nodes = find_node_by_types(cur_doc.ts_tree.root_node)\n", "    if len(cur_nodes):\n", "        filename2nodes[cur_doc.scope_tree.name] = cur_nodes\n", "    else:\n", "        print(f\"Did not find any relevant nodes for {cur_doc}\")\n", "\n", "total_nodes = sum(len(node_list) for node_list in filename2nodes.values())\n", "print(f\"There are {total_nodes} total number of nodes.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.common import decode_bytes\n", "xkeys = list(filename2nodes.keys())\n", "print(filename2nodes[xkeys[11]][2].parent)\n", "print(filename2nodes[xkeys[11]][2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents[111].scope_tree.name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "from termcolor import colored\n", "import tree_sitter as ts\n", "from research.static_analysis.common import decode_bytes\n", "from research.static_analysis.parsing import (\n", "    ScopeOrSpan,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    SrcScope,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "colors = [\"red\", \"green\", \"blue\", \"light_yellow\", \"cyan\"]\n", "color_index = 0\n", "\n", "\n", "def get_color():\n", "    global color_index\n", "    color = colors[color_index % len(colors)]\n", "    color_index += 1\n", "    return color\n", "\n", "\n", "def print_code_via_scope(scope: ScopeOrSpan, as_stub: bool = False):\n", "    \"\"\"Print out its source code using a DFS traversal.\n", "\n", "    This is equivalent to print out `SrcScope.dfs_code()` when `as_stub` is False.\n", "    \"\"\"\n", "    if isinstance(scope, SrcScope):\n", "        # We omit the docstr if print as stub\n", "        prefix_doc = scope.prefix if as_stub else scope.prefix_with_doc()\n", "        print(colored(prefix_doc.code, color=get_color()), end=\"\")\n", "        if as_stub and scope.kind == \"function\":\n", "            print(\"...\")\n", "        else:\n", "            for child in scope.children:\n", "                print_code_via_scope(child, as_stub)\n", "        print(colored(scope.suffix.code, color=get_color()), end=\"\")\n", "    else:\n", "        print(colored(scope.code, color=get_color()), end=\"\")\n", "\n", "\n", "def dfs_print_via_ts(node: ts.Node):\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    for child in node.children:\n", "        dfs_print_via_ts(child)\n", "\n", "\n", "def bfs_print_via_ts(root_node: ts.Node):\n", "    queue = collections.deque([root_node])\n", "\n", "    while queue:\n", "        node = queue.popleft()\n", "\n", "        breaker = \"-\" * 8\n", "        print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "        print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4)\n", "\n", "        # Enqueue child nodes for next level traversal\n", "        queue.extend(node.children)\n", "\n", "\n", "def customized_find(root_node: ts.Node, types: tuple[str, ...] = (\"call\",)):\n", "    target_nodes = []\n", "    unique_types = set()\n", "    queue = collections.deque([root_node])\n", "    while queue:\n", "        node = queue.popleft()\n", "        if node.type in types:\n", "            target_nodes.append(node)\n", "        unique_types.add(node.type)\n", "        queue.extend(node.children)\n", "    unique_types = sorted(list(unique_types))\n", "    print(f\"unique types: {unique_types}\")\n", "    print(f\"Find {len(target_nodes)} nodes.\")\n", "    return target_nodes\n", "\n", "\n", "# This should equal to the original source code\n", "# print_code()\n", "# print(color_index)\n", "# dfs_print_via_ts(documents[2].ts_tree.root_node)\n", "# print(color_index)\n", "\n", "target_nodes = customized_find(\n", "    documents[2].ts_tree.root_node, types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", ")\n", "\n", "\n", "for node in target_nodes:\n", "    if node.type == \"def\":\n", "        node = node.parent\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents[2].bmap.tsnode_to_crange(documents[2].ts_tree.root_node.children[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in documents[0].scope_tree.get_all_spans():\n", "    print(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(documents[0].ts_tree)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval import patch_lib\n", "from research.environments import cw_docker\n", "from research.eval.dataset_generation_lib.basic_patch_generators import fim_sample\n", "from research.eval.patch_lib import create_patch_from_files\n", "\n", "\n", "def show_patch(patch: patch_lib.Patch):\n", "    for key, value in vars(patch).items():\n", "        print(f\"{key} : {value}\")\n", "\n", "\n", "repo_path = Path(\"/mnt/efs/augment/user/dxy/repos/google/pyglove/v0.4.2\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["183\n"]}], "source": ["repo_sha = \"870ed11798703e56e9c9523af5b52bd92ec320a4\"\n", "all_patches = list(\n", "    fim_sample(\n", "        repo_root=repo_path,\n", "        repository_name=\"pyglove\",\n", "        organization_name=\"google\",\n", "        commit_sha=repo_sha,\n", "        lang=\"python\",\n", "        random_seed=None,\n", "    )\n", ")\n", "print(len(all_patches))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["_extra : {}\n", "file_content : # Copyright 2023 The PyGlove Authors\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "#      http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License.\n", "\"\"\"Train MNIST.\n", "\n", "This is a basic working ML program which trains MNIST.\n", "The code is modified from the pytorch mnist example:\n", "https://github.com/pytorch/examples/blob/main/mnist/main.py\n", "\"\"\"\n", "from absl import app\n", "import pyglove as pg\n", "import torch\n", "from torch import nn\n", "from torch import optim\n", "from torch.nn import functional as F\n", "from torch.optim.lr_scheduler import StepLR\n", "from torchvision import datasets\n", "from torchvision import transforms\n", "\n", "\n", "class Net(nn.Module):\n", "  \"\"\"Nerual architecture for MNIST.\"\"\"\n", "\n", "  def __init__(self):\n", "    super(Net, self).__init__()\n", "    filters1 = pg.oneof([32, 64], name='filters1')\n", "    self.conv1 = nn.Conv2d(1, filters1, 3, 1)\n", "    self.conv2 = nn.Conv2d(filters1, filters1 * 2, 3, 1)\n", "    self.dropout1 = nn.Dropout(0.25)\n", "    self.dropout2 = nn.Dropout(0.5)\n", "    fc1_input_dims = filters1 * 2 * 144\n", "    fc1_output_dims = pg.oneof([64, 128, 256], name='fc1_dims')\n", "    self.fc1 = nn.Linear(fc1_input_dims, fc1_output_dims)\n", "    self.fc2 = nn.Linear(fc1_output_dims, 10)\n", "\n", "  def forward(self, x):\n", "    activation = pg.oneof([<PERSON><PERSON>, <PERSON><PERSON>sigmo<PERSON>], name='activation')\n", "    x = self.conv1(x)\n", "    x = activation(x)\n", "    x = self.conv2(x)\n", "    x = activation(x)\n", "    x = F.max_pool2d(x, 2)\n", "    x = self.dropout1(x)\n", "    x = torch.flatten(x, 1)\n", "    x = self.fc1(x)\n", "    x = activation(x)\n", "    x = self.dropout2(x)\n", "    x = self.fc2(x)\n", "    output = F.log_softmax(x, dim=1)\n", "    return output\n", "\n", "\n", "def train(model, device, train_loader, optimizer, epoch,\n", "          dry_run=False, log_interval=100):\n", "  \"\"\"Train model.\"\"\"\n", "  model.train()\n", "  for batch_idx, (data, target) in enumerate(train_loader):\n", "    data, target = data.to(device), target.to(device)\n", "    optimizer.zero_grad()\n", "    output = model(data)\n", "    loss = F.nll_loss(output, target)\n", "    loss.backward()\n", "    optimizer.step()\n", "    if batch_idx % log_interval == 0:\n", "      print('Train Epoch: {} [{}/{} ({:.0f}%)]\\tLoss: {:.6f}'.format(\n", "          epoch, batch_idx * len(data), len(train_loader.dataset),\n", "          100. * batch_idx / len(train_loader), loss.item()))\n", "    if dry_run:\n", "      break\n", "\n", "\n", "def test(model, device, test_loader):\n", "  \"\"\"Test model.\"\"\"\n", "  model.eval()\n", "  test_loss = 0\n", "  correct = 0\n", "  with torch.no_grad():\n", "    for data, target in test_loader:\n", "      data, target = data.to(device), target.to(device)\n", "      output = model(data)\n", "      test_loss += F.nll_loss(output, target, reduction='sum').item()\n", "      pred = output.argmax(dim=1, keepdim=True)\n", "      correct += pred.eq(target.view_as(pred)).sum().item()\n", "\n", "  test_loss /= len(test_loader.dataset)\n", "  accuracy = correct / len(test_loader.dataset)\n", "  print('\\nTest set: Average loss: {:.4f}, Accuracy: {}/{} ({:.0f}%)\\n'.format(\n", "      test_loss, correct, len(test_loader.dataset),\n", "      100. * accuracy))\n", "  return accuracy\n", "\n", "\n", "def train_and_eval(lr=1.0, batch_size=64, epochs=1, dry_run=False) -> float:\n", "  \"\"\"Train model and test model.\"\"\"\n", "  transform = transforms.Compose([\n", "      transforms.To<PERSON><PERSON><PERSON>(),\n", "      transforms.Normalize((0.1307,), (0.3081,))\n", "  ])\n", "  dataset1 = datasets.MNIST('.', train=True, download=True, transform=transform)\n", "  dataset2 = datasets.MNIST('.', train=False, transform=transform)\n", "  train_loader = torch.utils.data.DataLoader(dataset1, batch_size=batch_size)\n", "  test_loader = torch.utils.data.DataLoader(dataset2, batch_size=128)\n", "\n", "  device = torch.device('cpu')\n", "  model = Net().to(device)\n", "  optimizer = optim.<PERSON>del<PERSON>(model.parameters(), lr=lr)\n", "\n", "  scheduler = StepLR(optimizer, step_size=1, gamma=0.7)\n", "  accuracy = 0.0\n", "  for epoch in range(1, epochs + 1):\n", "    train(model, device, train_loader, optimizer, epoch, dry_run=dry_run)\n", "    if dry_run:\n", "      break\n", "    accuracy = test(model, device, test_loader)\n", "    scheduler.step()\n", "  return accuracy\n", "\n", "\n", "def main(argv):\n", "  if len(argv) > 1:\n", "    raise app.UsageError('Too many command-line arguments.')\n", "\n", "  def inspect_search_space():\n", "    _ = train_and_eval(dry_run=True)\n", "  search_space = pg.hyper.trace(inspect_search_space, require_hyper_name=True)\n", "  search_algorithm = pg.evolution.regularized_evolution()\n", "\n", "  for automl_context, feedback in pg.sample(\n", "      search_space, search_algorithm, num_examples=5):\n", "    with automl_context():\n", "      print('Starting trial #%d with parameters %s.'\n", "            % (feedback.id, {k: search_space.evaluate(v)\n", "                             for k, v in search_space.hyper_dict.items()}))\n", "      accuracy = train_and_eval(epochs=1)\n", "      feedback(accuracy)\n", "\n", "\n", "if __name__ == '__main__':\n", "  app.run(main)\n", "\n", "char_start : 4301\n", "char_end : 4504\n", "patch_content : raise app.UsageError('Too many command-line arguments.')\n", "\n", "  def inspect_search_space():\n", "    _ = train_and_eval(dry_run=True)\n", "  search_space = pg.hyper.trace(inspect_search_space, require_hyper_name=True)\n", "file_name : examples/automl/mnist/pytorch/mnist_tune_eagerly.py\n", "repository : google/pyglove\n", "commit_sha : 870ed11798703e56e9c9523af5b52bd92ec320a4\n", "patch_id : google/pyglove/12345678\n"]}], "source": ["patch = all_patches[6]\n", "patch.repository = \"google/pyglove\"\n", "patch.commit_sha = repo_sha\n", "patch.patch_id = f\"{patch.repository}/12345678\"\n", "show_patch(patch)\n", "pg_patch = patch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import HydraTask\n", "\n", "task = HydraTask()\n", "hydra_task_patch = task._ex_patches[1]"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:augment.research.eval.hydra.driver:Hydra driver pod name prefix: hydra-driver-7nhdwn79-local-pod\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using docker images lookup file: /home/<USER>/src/augment/research/eval/hydra/supported_repos.yaml.\n", "There are 85 image-tag pairs available on the CW docker registry.\n"]}], "source": ["from research.eval import hydra\n", "\n", "HYDRA_SOFT_TIMEOUT_SECS = 600\n", "HYDRA_HARD_TIMEOUT_SECS = 1800\n", "hydra_driver = hydra.Driver(\n", "    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,\n", "    hydra_block_resource_internet_access=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = hydra_driver.dispatch(hydra_task_patch.to_json())"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:SUCCESS: created ConfigMap hydra-driver-7nhdwn79-local-cfg-6sgwxy25.\n"]}, {"ename": "ValueError", "evalue": "A patch is using repository 'google/pyglove' but that repo isn't found in the docker images lookup file, meaning we can't find the right container. Please check the dataset included_repos.yaml.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/src/augment/research/eval/hydra/driver.py:250\u001b[0m, in \u001b[0;36mDriver.get_docker_image_name\u001b[0;34m(self, repo_name)\u001b[0m\n\u001b[1;32m    249\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 250\u001b[0m     repo_version \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mrepo_image_versions[repo_name\u001b[39m.\u001b[39;49mlower()]\n\u001b[1;32m    251\u001b[0m     repo_image \u001b[39m=\u001b[39m \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m{\u001b[39;00mrepo_name\u001b[39m.\u001b[39mlower()\u001b[39m}\u001b[39;00m\u001b[39m:\u001b[39m\u001b[39m{\u001b[39;00mrepo_version\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'google/pyglove'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[11], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m pg_patch\u001b[39m.\u001b[39mfile_name \u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39mpyglove/ext/evolution/neat.py\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m----> 2\u001b[0m results \u001b[39m=\u001b[39m hydra_driver\u001b[39m.\u001b[39;49mdispatch(pg_patch\u001b[39m.\u001b[39;49mto_json())\n", "File \u001b[0;32m~/src/augment/research/eval/hydra/driver.py:280\u001b[0m, in \u001b[0;36mDriver.dispatch\u001b[0;34m(self, patch, image_name)\u001b[0m\n\u001b[1;32m    278\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_dispatch_semaphore\u001b[39m.\u001b[39macquire()\n\u001b[1;32m    279\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[0;32m--> 280\u001b[0m     results \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_dispatch(patch_json, image_name)\n\u001b[1;32m    281\u001b[0m \u001b[39mfinally\u001b[39;00m:\n\u001b[1;32m    282\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_dispatch_semaphore\u001b[39m.\u001b[39mrelease()\n", "File \u001b[0;32m~/src/augment/research/eval/hydra/driver.py:313\u001b[0m, in \u001b[0;36mDriver._dispatch\u001b[0;34m(self, patch_json, image_name)\u001b[0m\n\u001b[1;32m    311\u001b[0m patch \u001b[39m=\u001b[39m Patch\u001b[39m.\u001b[39mfrom_json(patch_json)\n\u001b[1;32m    312\u001b[0m \u001b[39mif\u001b[39;00m image_name \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[0;32m--> 313\u001b[0m     image_name \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mget_docker_image_name(patch\u001b[39m.\u001b[39;49mrepository)\n\u001b[1;32m    315\u001b[0m container \u001b[39m=\u001b[39m k8s_utils\u001b[39m.\u001b[39mcreate_container(\n\u001b[1;32m    316\u001b[0m     mounts\u001b[39m=\u001b[39mmounts,\n\u001b[1;32m    317\u001b[0m     image\u001b[39m=\u001b[39mimage_name,\n\u001b[1;32m    318\u001b[0m     image_pull_policy\u001b[39m=\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mAlways\u001b[39m\u001b[39m\"\u001b[39m,\n\u001b[1;32m    319\u001b[0m     command\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mget_command(pod_name\u001b[39m=\u001b[39mpod_name),\n\u001b[1;32m    320\u001b[0m )\n\u001b[1;32m    322\u001b[0m pod_body \u001b[39m=\u001b[39m k8s_utils\u001b[39m.\u001b[39mcreate_pod(\n\u001b[1;32m    323\u001b[0m     name\u001b[39m=\u001b[39mpod_name,\n\u001b[1;32m    324\u001b[0m     containers\u001b[39m=\u001b[39m[container],\n\u001b[1;32m    325\u001b[0m     volumes\u001b[39m=\u001b[39mvolumes,\n\u001b[1;32m    326\u001b[0m     hydra_block_resource_internet_access\u001b[39m=\u001b[39m\u001b[39mself\u001b[39m\u001b[39m.\u001b[39mhydra_block_resource_internet_access,\n\u001b[1;32m    327\u001b[0m )\n", "File \u001b[0;32m~/src/augment/research/eval/hydra/driver.py:254\u001b[0m, in \u001b[0;36mDriver.get_docker_image_name\u001b[0;34m(self, repo_name)\u001b[0m\n\u001b[1;32m    252\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39m{\u001b[39;00mcw_docker\u001b[39m.\u001b[39mAUGMENT_DOCKER_REGISTRY\u001b[39m}\u001b[39;00m\u001b[39m/\u001b[39m\u001b[39m{\u001b[39;00mconstants\u001b[39m.\u001b[39mHYDRA_IMAGE_PREFIX\u001b[39m}\u001b[39;00m\u001b[39m/\u001b[39m\u001b[39m{\u001b[39;00mrepo_image\u001b[39m}\u001b[39;00m\u001b[39m\"\u001b[39m\n\u001b[1;32m    253\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mKeyError\u001b[39;00m \u001b[39mas\u001b[39;00m exc:\n\u001b[0;32m--> 254\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m    255\u001b[0m         \u001b[39mf\u001b[39m\u001b[39m\"\u001b[39m\u001b[39mA patch is using repository \u001b[39m\u001b[39m'\u001b[39m\u001b[39m{\u001b[39;00mrepo_name\u001b[39m}\u001b[39;00m\u001b[39m'\u001b[39m\u001b[39m but that repo isn\u001b[39m\u001b[39m'\u001b[39m\u001b[39mt found in the docker \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    256\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mimages lookup file, meaning we can\u001b[39m\u001b[39m'\u001b[39m\u001b[39mt find the right container. Please check the \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    257\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mdataset included_repos.yaml.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m    258\u001b[0m     ) \u001b[39mfrom\u001b[39;00m \u001b[39mexc\u001b[39;00m\n", "\u001b[0;31mValueError\u001b[0m: A patch is using repository 'google/pyglove' but that repo isn't found in the docker images lookup file, meaning we can't find the right container. Please check the dataset included_repos.yaml."]}], "source": ["pg_patch.file_name = \"pyglove/ext/evolution/neat.py\"\n", "results = hydra_driver.dispatch(pg_patch.to_json(), \"google/pyglove:v1.0\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}