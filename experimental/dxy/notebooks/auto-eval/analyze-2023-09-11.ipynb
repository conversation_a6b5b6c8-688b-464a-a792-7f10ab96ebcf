{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "import pathlib\n", "\n", "import tqdm\n", "\n", "from research.core import utils_for_str as str_lib\n", "from research.core.ui_sugar import ui_load_from_json_file, ui_save_to_json_file\n", "from research.core.utils import Timer\n", "from research.eval.harness.factories import create_retriever\n", "from research.eval.harness.tasks import HydraTask, AugmentedHydraTask\n", "\n", "from research.eval.hydra.driver import Driver\n", "from research.models import GenerationOptions\n", "from research.models.all_models import get_model\n", "from research.models.remote_models import RemoteLLAMACPP\n", "from experimental.dxy.exps.xlib import (\n", "    BasicPostProcessor,\n", "    BasicPreProcessor,\n", "    GeneralizedSystem,\n", "    RAGPreProcessor,\n", "    TrimCompletion,\n", ")\n", "from research.core import model_input as data_lib\n", "from research.core import utils_for_str as str_lib\n", "from research.eval.harness import tasks as task_lib\n", "\n", "\n", "def show_completion(input: data_lib.ModelInput, completion: str | None = None, max_lines: int = 1000):\n", "    prefix = str_lib.get_last_n_lines(input.prefix, max_lines)\n", "    suffix = str_lib.get_first_n_lines(input.suffix, max_lines)\n", "    if completion is None:\n", "        completion = input.target\n", "    assert completion is not None\n", "    str_lib.show_completion(prefix, suffix, completion)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["task = AugmentedHydraTask()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 216/216 [00:18<00:00, 11.73it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 112 failed data.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["result_dir = pathlib.Path(\"/home/<USER>/cache/results-v2/aughydra-sota-2k2k4k-bm25-fimsc-16b\")\n", "all_paths = []\n", "results = []\n", "for xpath in result_dir.glob(\"*-final.json\"):\n", "    all_paths.append(xpath)\n", "\n", "for xpath in tqdm.tqdm(all_paths, total=len(all_paths)):\n", "    cur_data = ui_load_from_json_file(xpath)\n", "    results.append(cur_data)\n", "\n", "failed_data = [cur_data for cur_data in results if not cur_data['pass']]\n", "print(f\"There are {len(failed_data)} failed data.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 0\n", "\n", "show_completion(failed_data[index][\"inputs\"], completion=failed_data[index][\"completion\"], max_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 1\n", "\n", "show_completion(failed_data[index][\"inputs\"], completion=failed_data[index][\"completion\"], max_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 4\n", "\n", "show_completion(failed_data[index][\"inputs\"], completion=failed_data[index][\"completion\"], max_lines=20)\n", "# show_completion(failed_data[index][\"inputs\"], completion=None, max_lines=20)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[92m    return new_value, children, metadata\n", "\n", "  def set_metadata(\n", "      self, key: str, value: Any, cloneable: bool = False) -> 'DNA':\n", "    \"\"\"Set metadata associated with a key.\n", "\n", "    Metadata associated with the DNA will be persisted and carried over across\n", "    processes, which is different the `userdata`. (See `set_userdata` for more\n", "    details.)\n", "\n", "    Args:\n", "      key: Key for the metadata.\n", "      value: Value for the metadata.\n", "      cloneable: If True, the key/value will be propagated during clone.\n", "\n", "    Returns:\n", "      Self.\n", "    \"\"\"\n", "    self.metadata.rebind(\n", "        {key: value}\u001b[0m\u001b[47m\u001b[30m, allow_partial=True, cloneable=\u001b[0m\u001b[94mTrue)\n", "    if cloneable:\n", "      self._cloneable_metadata_keys.add(key)\n", "    return self\n", "\n", "  def set_userdata(\n", "      self, key: str, value: Any, cloneable: bool = False) -> 'DNA':\n", "    \"\"\"Sets user data associated with a key.\n", "\n", "    User data associated with the DNA will live only within current process,\n", "    and is not carried over during serialization/deserialization, which is\n", "    different from DNA metadata. (See `set_metadata` for more details.)\n", "\n", "    Args:\n", "      key: Key of the user data.\n", "      value: Value of the user data.\n", "      cloneable: If True, the key/value will be carry over to the cloned DNA.\n", "\n", "    Returns:\n", "      Self.\u001b[0m"]}], "source": ["index = 13\n", "\n", "show_completion(failed_data[index][\"inputs\"], completion=failed_data[index][\"completion\"], max_lines=20)\n", "# show_completion(failed_data[index][\"inputs\"], completion=None, max_lines=20)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}