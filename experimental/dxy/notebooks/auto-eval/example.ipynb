{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import logging\n", "import termcolor\n", "import tqdm\n", "import tree_sitter as ts\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "from experimental.dxy.exps.generate_patches_api import (\n", "    parse_docs_from_docker_image,\n", "    CoverageElement,\n", ")\n", "from research.static_analysis.common import LanguageID, decode_bytes\n", "from experimental.dxy.exps.patch_data_lib import (\n", "    CoverageElement,\n", "    filter_docs_by_node_lines,\n", "    filter_docs_by_node_tokens,\n", "    find_ts_parent_in_certain_type_range,\n", "    parsedfile_to_dict,\n", "    ts_node_to_dict,\n", "    count_nodes_for_docs,\n", "    doc_w_nodes_to_dict,\n", ")\n", "from research.core import utils_for_str\n", "from research.core.utils import Timer\n", "from research.core.types import CharRange\n", "\n", "\n", "def filter_doc_nodes_by_coverage(\n", "    documents: list[ParsedFile], filename2coverage: dict[str, CoverageElement]\n", ") -> list[tuple[<PERSON>rse<PERSON><PERSON><PERSON>, list[ts.Node]]]:\n", "    doc_nodes_pairs: list[tuple[ParsedFile, list[ts.Node]]] = []\n", "    total_valid_nodes: int = 0\n", "    for index, doc in tqdm.tqdm(enumerate(documents)):\n", "        filtered_nodes__by_coverage = []\n", "        for node in doc.ts_nodes:\n", "            if doc.scope_tree.name not in filename2coverage:\n", "                continue\n", "            coverage_ele = filename2coverage[doc.scope_tree.name]\n", "            covered_by_unit_test = False\n", "            crange = doc.bmap.tsnode_to_crange(node)\n", "            line_start = doc.lmap.get_line_number(crange.start)\n", "            line_end = doc.lmap.get_line_number(crange.stop - 1)\n", "            for line in range(line_start, line_end + 1):\n", "                # lmap.get_line_number starts from 0-index but coverage_ele used 1-index for the line number\n", "                line = line + 1\n", "                if line in coverage_ele.line2hit and coverage_ele.line2hit[line] > 0:\n", "                    covered_by_unit_test = True\n", "            if covered_by_unit_test:\n", "                filtered_nodes__by_coverage.append(node)\n", "        print(\n", "            f\"[{index:4d}/{len(documents):4d}] {doc.scope_tree.name:20s}\"\n", "            f\" : {len(doc.ts_nodes):3d} nodes -> {len(filtered_nodes__by_coverage):3d} nodes\"\n", "        )\n", "        # Collect the useful data\n", "        if filtered_nodes__by_coverage:\n", "            doc_nodes_pairs.append((doc, filtered_nodes__by_coverage))\n", "        total_valid_nodes += len(filtered_nodes__by_coverage)\n", "    print(f\"Left {len(doc_nodes_pairs)} documents with {total_valid_nodes} nodes.\")\n", "    return doc_nodes_pairs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents, coverages = parse_docs_from_docker_image(\"google/pyglove:v1.0\")\n", "filename2doc: dict[str, ParsedFile] = {}\n", "filename2coverage: dict[str, CoverageElement] = {}\n", "\n", "for doc in documents:\n", "    filename2doc[doc.scope_tree.name] = doc\n", "\n", "for coverage in coverages:\n", "    filename2coverage[coverage.filename] = coverage\n", "\n", "doc_w_nodes = filter_doc_nodes_by_coverage(documents, filename2coverage)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Show API Calls"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Find 81 documents with 5879 nodes.\n", "After filter_docs_by_node_lines, 81 docs -> 67 docs with 759 nodes.\n", "\u001b[32mclass GreaterThan(BinaryOperator):\n", "  \"\"\"Greater than operator.\"\"\"\n", "\n", "  ORDER = 2\n", "  OPERATOR_STR = '>'\n", "  OPERATOR_FN = lambda cls, x, y: x > y\n", "\n", "\u001b[0m\u001b[40m\u001b[97mclass Equals(BinaryOperator):\n", "  \"\"\"Equals operation.\"\"\"\n", "\n", "  ORDER = 3\n", "  OPERATOR_STR = '=='\n", "  OPERATOR_FN = lambda cls, x, y: x == y\u001b[0m\u001b[33mclass NotEquals(BinaryOperator):\n", "  \"\"\"Not Equals operator.\"\"\"\n", "\n", "  ORDER = 3\n", "  OPERATOR_STR = '!='\n", "  OPERATOR_FN = lambda cls, x, y: x != y\u001b[0m\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[32m    # No detour in current thread.\n", "    return _global_detour_context.call_new(\n", "        _global_detour_context.get_original_new(cls),\n", "        cls, *args, **kwargs)\n", "\n", "  if inspect.isclass(dest_cls_or_fn):\n", "    dest_cls = dest_cls_or_fn\n", "    \u001b[0m\u001b[40m\u001b[97minstance = _global_detour_context.call_new(\n", "        _global_detour_context.get_original_new(dest_cls),\n", "        dest_cls, *args, **kwargs)\u001b[0m\u001b[33m\n", "\n", "    # NOTE(daiyip): when an overridden `__new__` returns an instance whose\n", "    # class is not strictly the user class, `__init__` will not be called\n", "    # by Python runtime. We can detect such case and invoke `__init__`\n", "    # manually.\n", "    if not isinstance(instance, cls):\n", "      instance.__init__(*args, **kwargs)\u001b[0m\n", "\n", "\n", "----- Context within the file : pyglove/core/detouring/class_detour.py-----\n", "\n", "class _DetourContext:\n", "  '''Context that sets/gets detoured class mappings under current thread.'''\n", "\n", "  _DETOUR_STACK_KEY = 'detour_stack'\n", "  _DETOUR_MAPPING_KEY = 'detour_map'\n", "  _NEW_CALL_STACK = 'new_stack'\n", "  \n", "  def call_new(self, new_method, cls, *args, **kwargs):\n", "    '''Call __new__ method with correctly handling super.__new__.'''\n", "    try:\n", "      self._new_stack.append(new_method)\n", "      if new_method is object.__new__:\n", "        return object.__new__(cls)\n", "      else:\n", "        return new_method(cls, *args, **kwargs)\n", "    finally:\n", "      self._new_stack.pop(-1)\n", "\n", "\n"]}], "source": ["# Show the API calls\n", "def show_code(prefix: str, suffix: str, middle: str):\n", "    print(termcolor.colored(utils_for_str.get_last_n_lines(prefix, 8), color=\"green\"), end=\"\")\n", "    print(termcolor.colored(middle, color=\"white\", on_color=\"on_black\"), end=\"\")\n", "    print(termcolor.colored(utils_for_str.get_first_n_lines(suffix, 8), color=\"yellow\"), end=\"\\n\")\n", "\n", "def show_node(doc_dict: dict, index: int):\n", "    node = doc_dict[\"ts_node_dicts\"][index]\n", "    code: str = doc_dict[\"code\"]\n", "    crange: CharRange = node[\"node.char_range\"]\n", "    prefix: str = code[: crange.start]\n", "    suffix: str = code[crange.stop :]\n", "    middle: str = code[crange.start : crange.stop]\n", "    print(termcolor.colored(utils_for_str.get_last_n_lines(prefix, 8), color=\"green\"), end=\"\")\n", "    print(termcolor.colored(middle, color=\"white\", on_color=\"on_black\"), end=\"\")\n", "    print(termcolor.colored(utils_for_str.get_first_n_lines(suffix, 8), color=\"yellow\"), end=\"\\n\")\n", "\n", "def find_api_calls(\n", "    doc_w_nodes: list[tuple[ParsedFile, list[ts.Node]]]\n", ") -> list[tuple[<PERSON>rse<PERSON><PERSON><PERSON>, list[ts.Node]]]:\n", "    valid_types_for_api_call = (\n", "        \"call\",\n", "        \"pair\",\n", "        \"assignment\",\n", "        \"binary_operator\",\n", "        \"return_statement\",\n", "        \"expression_statement\",\n", "        \"dictionary\",\n", "        \"argument_list\",\n", "    )\n", "\n", "    doc_nodes_pairs: list[tuple[ParsedFile, list[ts.Node]]] = []\n", "    total_valid_nodes: int = 0\n", "    for index, (doc, nodes) in enumerate(doc_w_nodes):\n", "        target_nodes: list[ts.Node] = []\n", "        for node in doc.ts_nodes:\n", "            if node.type != \"call\":\n", "                continue\n", "            cur_node = find_ts_parent_in_certain_type_range(\n", "                node, valid_types_for_api_call\n", "            )\n", "            if cur_node is None:\n", "                raise ValueError(\n", "                    f\"node={node} did not successfully call find_ts_parent_in_certain_type_range\"\n", "                )\n", "            target_nodes.append(cur_node)\n", "        if target_nodes:\n", "            doc_nodes_pairs.append((doc, target_nodes))\n", "            total_valid_nodes += len(target_nodes)\n", "    print(f\"Find {len(doc_nodes_pairs)} documents with {total_valid_nodes} nodes.\")\n", "    return doc_nodes_pairs\n", "\n", "\n", "api_doc_w_nodes = find_api_calls(doc_w_nodes)\n", "api_doc_dicts = doc_w_nodes_to_dict(api_doc_w_nodes)\n", "api_doc_dicts = filter_docs_by_node_lines(api_doc_dicts, min_lines=3, max_lines=8)\n", "\n", "\n", "show_code(prefix=r'''class GreaterThan(BinaryOperator):\n", "  \"\"\"Greater than operator.\"\"\"\n", "\n", "  ORDER = 2\n", "  OPERATOR_STR = '>'\n", "  OPERATOR_FN = lambda cls, x, y: x > y\n", "\n", "''',\n", "\n", "\n", "middle=r'''class Equals(BinaryOperator):\n", "  \"\"\"Equals operation.\"\"\"\n", "\n", "  ORDER = 3\n", "  OPERATOR_STR = '=='\n", "  OPERATOR_FN = lambda cls, x, y: x == y''',\n", "\n", "\n", "suffix=r'''class NotEquals(BinaryOperator):\n", "  \"\"\"Not Equals operator.\"\"\"\n", "\n", "  ORDER = 3\n", "  OPERATOR_STR = '!='\n", "  OPERATOR_FN = lambda cls, x, y: x != y''')\n", "\n", "print('\\n')\n", "print('\\n')\n", "print('\\n')\n", "\n", "show_node(api_doc_dicts[0], 1)\n", "print('\\n')\n", "context = r\"\"\"class _DetourContext:\n", "  '''Context that sets/gets detoured class mappings under current thread.'''\n", "\n", "  _DETOUR_STACK_KEY = 'detour_stack'\n", "  _DETOUR_MAPPING_KEY = 'detour_map'\n", "  _NEW_CALL_STACK = 'new_stack'\n", "  \n", "  def call_new(self, new_method, cls, *args, **kwargs):\n", "    '''Call __new__ method with correctly handling super.__new__.'''\n", "    try:\n", "      self._new_stack.append(new_method)\n", "      if new_method is object.__new__:\n", "        return object.__new__(cls)\n", "      else:\n", "        return new_method(cls, *args, **kwargs)\n", "    finally:\n", "      self._new_stack.pop(-1)\"\"\"\n", "print(f\"----- Context within the file : {api_doc_dicts[0]['scope_tree'].name}-----\\n\")\n", "print(context)\n", "print('\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Show String-like Snippets"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Find 26 documents with 123 nodes.\n", "After filter_docs_by_node_lines, 26 docs -> 5 docs with 6 nodes.\n", "\u001b[32m      field_updates: Updates made to the subtree. Key path is relative to\n", "        current object.\n", "    \"\"\"\n", "\n", "  @property\n", "  @abc.abstractmethod\n", "  def _subscribes_field_updates(self) -> bool:\n", "    \u001b[0m\u001b[40m\u001b[97m\"\"\"Returns True if current object subscribes field updates in `on_change`.\n", "\n", "    NOTE(daiyip): When it returns False, we don't need to compute field updates\n", "    for this object, but simply invoke onchange with empty fields.\n", "    \"\"\"\u001b[0m\u001b[33m\n", "\n", "  #\n", "  # Protected helper methods.\n", "  #\n", "\n", "  def _set_raw_attr(self, name: str, value: Any) -> 'Symbolic':\n", "    \"\"\"Set raw property without trigger __setattr__.\"\"\"\u001b[0m\n"]}], "source": ["# Find the comments or docstring\n", "# Similarity Matching\n", "def is_comment_type(node: ts.Node):\n", "    node_text = decode_bytes(node.text)\n", "    if node.type == \"block\" and (\n", "        node_text.startswith('\"' * 3) and node_text.endswith('\"' * 3) or node_text.startswith(\"'\" * 3) and node_text.endswith(\"'\" * 3)\n", "    ):\n", "        return True\n", "    else:\n", "        return False\n", "\n", "def find_comments(\n", "    doc_w_nodes: list[tuple[ParsedFile, list[ts.Node]]]\n", ") -> list[tuple[<PERSON>rse<PERSON><PERSON><PERSON>, list[ts.Node]]]:\n", "\n", "    doc_nodes_pairs: list[tuple[ParsedFile, list[ts.Node]]] = []\n", "    total_valid_nodes: int = 0\n", "    for index, (doc, nodes) in enumerate(doc_w_nodes):\n", "        target_nodes: list[ts.Node] = []\n", "        for node in doc.ts_nodes:\n", "            if is_comment_type(node):\n", "                target_nodes.append(node)\n", "        if target_nodes:\n", "            doc_nodes_pairs.append((doc, target_nodes))\n", "            total_valid_nodes += len(target_nodes)\n", "    print(f\"Find {len(doc_nodes_pairs)} documents with {total_valid_nodes} nodes.\")\n", "    return doc_nodes_pairs\n", "\n", "comment_doc_w_nodes = find_comments(doc_w_nodes)\n", "comment_doc_w_nodes = doc_w_nodes_to_dict(comment_doc_w_nodes)\n", "comment_doc_w_nodes = filter_docs_by_node_lines(comment_doc_w_nodes, min_lines=3, max_lines=8)\n", "show_node(comment_doc_w_nodes[1], 0)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Find 88 documents with 17901 nodes.\n", "{'node.char_range': IntRange(3116, 3119)}\n", "\u001b[32mas_sealed = symbolic.as_sealed\n", "\n", "# Symbolic types.\n", "Symbolic = symbolic.Symbolic\n", "PureSymbolic = symbolic.PureSymbolic\n", "\n", "Dict = symbolic.Dict\n", "dict = Dict\u001b[0m\u001b[40m\u001b[97m   \u001b[0m\u001b[33m# pylint: disable=redefined-builtin\n", "\n", "List = symbolic.List\n", "list = List   # pylint: disable=redefined-builtin\n", "\n", "Object = symbolic.Object\n", "ClassWrapper = symbolic.ClassWrapper\n", "Functor = symbolic.Functor\u001b[0m\n", "\n", "\n"]}], "source": ["# Find the comments or docstring\n", "# Exact Matching\n", "def is_empty_char(x):\n", "    return x in (\" \", \"\\t\", \"\\n\")\n", "\n", "def find_leftest_empty_index(code: str, index: int):\n", "    while index >= 0:\n", "        if is_empty_char(code[index]):\n", "            index -= 1\n", "        else:\n", "            break\n", "    return index + 1\n", "\n", "def find_empty_string(\n", "    doc_w_nodes: list[tuple[ParsedFile, list[ts.Node]]]\n", ") -> list[dict]:\n", "    \n", "    doc_dict_list: list[dict] = []\n", "    total_valid_nodes: int = 0\n", "    for index, (doc, nodes) in enumerate(doc_w_nodes):\n", "        doc_dict = parsedfile_to_dict(doc)\n", "        target_nodes: list[dict] = []\n", "        for node in doc.ts_nodes:\n", "            if node.children is None or len(node.children) == 0:\n", "                crange = doc.bmap.tsnode_to_crange(node)\n", "                if crange.start == 0 or not is_empty_char(doc.code[crange.start-1]):\n", "                    continue\n", "                leftest_index = find_leftest_empty_index(doc.code, crange.start-1)\n", "                empty_string = doc.code[leftest_index: crange.start-1]\n", "                if \" \" in empty_string:\n", "                    node_dict = dict()\n", "                    node_dict[\"node.char_range\"] = CharRange(start=leftest_index, stop=crange.start)\n", "                    target_nodes.append(node_dict)\n", "        if target_nodes:\n", "            doc_dict[\"ts_node_dicts\"] = target_nodes\n", "            doc_dict_list.append(doc_dict)\n", "            total_valid_nodes += len(target_nodes)\n", "    print(f\"Find {len(doc_dict_list)} documents with {total_valid_nodes} nodes.\")\n", "    return doc_dict_list\n", "\n", "empty_doc_w_nodes = find_empty_string(doc_w_nodes)\n", "# empty_doc_w_nodes = filter_docs_by_node_lines(empty_doc_w_nodes, min_lines=3, max_lines=8)\n", "print(empty_doc_w_nodes[0][\"ts_node_dicts\"][0])\n", "show_node(empty_doc_w_nodes[0], 0)\n", "print('\\n')"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Result Analysis\n", "API Calls with reference found in the repo:\n", "\tPASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240\n", "API Calls with reference found in the same file:\n", "\tPASS =  34 / 128 (vs.  32 / 128), Syntax Error = 32 / 240\n", "API Calls (others):\n", "\tPASS =  17 / 64  (vs.  16 /  64), Syntax Error = 32 / 240\n", "String-like completion\n", "\tSimilarity = 54% (vs. 49%), Syntax Error = 32 / 240\n", "Empty string-like completion\n", "\t Exact Match = 39 / 50 (vs. 29 / 50), Syntax Error = 32 / 240\n", "\n", "\n", "\n", "\n", "Breakdown by Structural Categorization\n", "\tAPI Calls with reference found in the repo:\n", "\t\tCursor-Loc (Beining of a line): PASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240\n", "\t\tCursor-Loc (Middle of a line): PASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240\n", "\n", "\n", "\n", "\n", "....\n", "Breakdown by Repo Name\n", "\tAPI Calls with reference found in the repo:\n", "\t\t Pydantic PASS = 128 / 240 (vs. baseline = 128 / 240)\n", "\t\t ...\n"]}], "source": ["import functools\n", "import json\n", "import logging\n", "import threading\n", "from collections import defaultdict\n", "from dataclasses import asdict, dataclass\n", "from glob import glob\n", "from pathlib import Path\n", "from pprint import pformat\n", "from typing import Optional, Union\n", "\n", "import shortuuid\n", "from tqdm import tqdm\n", "\n", "from research.core.ui_sugar import UISugar\n", "from research.core.artifacts import collect_artifacts\n", "from research.core.model_input import ModelInput\n", "from research.core.types import Chunk, Document\n", "from research.core.utils_for_str import extract_colored_parts\n", "from research.eval import hydra, patch_lib\n", "from research.eval.harness import types, utils\n", "from research.eval.harness.systems import AbstractSystem\n", "from research.eval.harness.tasks.abs_task import AbstractTask, DocsType\n", "from research.eval.hydra.driver import Driver\n", "from research.retrieval.utils import Span\n", "\n", "# class AbstractCategory(UISugar):\n", "\n", "#     name: str\n", "#     \"\"\"The concrete category name.\"\"\"\n", "\n", "# class APICallCategory(AbstractCategory):\n", "    \n", "\n", "# class AugmentedHydraTask(AbstractTask):\n", "#     \"\"\"The Hydra Task.\"\"\"\n", "\n", "#     version: str = \"1.0\"\n", "\n", "#     DEFAULT_DATASET = \"repoeval_functions\"\n", "#     DATASETS_BASEDIR = \"/mnt/efs/augment/data/eval/hydra/datasets/\"\n", "\n", "#     def __init__(self):\n", "#         self.dataset_name = dataset\n", "#         self.dataset_path = dataset_path\n", "#         self.name = experiment_name\n", "\n", "\n", "print(\"Result Analysis\")\n", "print(\"API Calls with reference found in the repo:\")\n", "print(\"\\tPASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240\")\n", "print(\"API Calls with reference found in the same file:\")\n", "print(\"\\tPASS =  34 / 128 (vs.  32 / 128), Syntax Error = 32 / 240\")\n", "print(\"API Calls (others):\")\n", "print(\"\\tPASS =  17 / 64  (vs.  16 /  64), Syntax Error = 32 / 240\")\n", "\n", "print(\"String-like completion\")\n", "print(\"\\tSimilarity = 54% (vs. 49%), Syntax Error = 32 / 240\")\n", "\n", "print(\"Empty string-like completion\")\n", "print(\"\\t Exact Match = 39 / 50 (vs. 29 / 50), Syntax Error = 32 / 240\")\n", "\n", "print(\"\\n\\n\\n\")\n", "\n", "print(\"Breakdown by Structural Categorization\")\n", "print(\"\\tAPI Calls with reference found in the repo:\")\n", "print(\"\\t\\tCursor-Loc (Beining of a line): PASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240\")\n", "print(\"\\t\\tCursor-Loc (Middle of a line): PASS = 128 / 240 (vs. 128 / 240), Syntax Error = 32 / 240\")\n", "\n", "print(\"\\n\\n\\n\")\n", "print(\"....\")\n", "\n", "print(\"Breakdown by Repo Name\")\n", "print(\"\\tAPI Calls with reference found in the repo:\")\n", "print(\"\\t\\t Pydantic PASS = 128 / 240 (vs. baseline = 128 / 240)\")\n", "print(\"\\t\\t ...\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}