{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import logging\n", "import tree_sitter as ts\n", "from pathlib import Path\n", "from research.static_analysis.parsing import ScopeTreeParser\n", "from research.static_analysis.usage_analysis import ParsedFile, UsageIndex\n", "import collections\n", "from termcolor import colored\n", "import tree_sitter as ts\n", "from research.static_analysis.common import decode_bytes\n", "from research.static_analysis.parsing import (\n", "    ScopeOrSpan,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    SrcScope,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from experimental.dxy.exps import patch_generate_lib\n", "from research.static_analysis.experimental_parsing import AugmentedParsedFile\n", "\n", "\n", "global_logger = logging.Logger(\"notebook\")\n", "# Clear previous handlers\n", "for handler in global_logger.handlers[:]:\n", "    global_logger.removeHandler(handler)\n", "# Create a console handler (ch) and set the level to DEBUG\n", "ch = logging.StreamH<PERSON>ler()\n", "# Create a formatter\n", "formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')\n", "# Add formatter to ch\n", "ch.<PERSON><PERSON><PERSON><PERSON><PERSON>(formatter)\n", "# Add ch to logger\n", "global_logger.addHandler(ch)\n", "# Set the logging level\n", "global_logger.setLevel(logging.INFO)\n", "global_logger.info(\"Hello World\")\n", "\n", "\n", "colors = [\"red\", \"green\", \"blue\", \"light_yellow\", \"cyan\"]\n", "color_index = 0\n", "\n", "\n", "def get_color():\n", "    global color_index\n", "    color = colors[color_index % len(colors)]\n", "    color_index += 1\n", "    return color\n", "\n", "\n", "def customized_find(root_node: ts.Node):\n", "    target_nodes = []\n", "    unique_types = set()\n", "    queue: collections.deque[tuple[ts.Node, int]] = collections.deque([(root_node, 0)])\n", "    while queue:\n", "        (node, depth) = queue.popleft()\n", "        target_nodes.append((node, depth))\n", "        unique_types.add(node.type)\n", "        for cur_node in node.children:\n", "            queue.append((cur_node, depth + 1))\n", "    unique_types = sorted(list(unique_types))\n", "    print(f\"unique types: {unique_types}\")\n", "    print(f\"Find {len(target_nodes)} nodes.\")\n", "    return target_nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "@pg.members([\n", "    ('seed', pg.typing.Int().noneable(), 'Random seed.')\n", "])\n", "class RandomScalar(base.<PERSON>ala<PERSON>):\n", "  '''Base class for random operation for computing scheduled value.'''\n", "\n", "  def _on_bound(self):\n", "    self._random = random if self.seed is None else random.Random(self.seed)\n", "\n", "  def call(self, step: int):\n", "    return self.next_value()\n", "\n", "  @abc.abstractmethod\n", "  def next_value(self) -> Union[int, float]:\n", "    '''Return next value..'''\n", "\"\"\"\n", "\n", "# ex_file = \"\"\"\n", "# @pg.members([])\n", "# class A:\n", "#   '''Class doc string.'''\n", "\n", "#   x: int = -1\n", "#   '''Attribute x.'''\n", "# \"\"\"\n", "\n", "# doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "# color_index = 0\n", "# target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# # , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "# for index, (node, depth) in enumerate(target_nodes):\n", "#     breaker = \"-\" * 8\n", "#     print(\n", "#         breaker\n", "#         + f\" : [{index:05d}] {node.type:16s} : depth={depth} : {node.start_byte} - {node.end_byte}\"\n", "#     )\n", "#     print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "#     print(breaker * 4)\n", "\n", "\n", "doc = AugmentedParsedFile.from_code_str(ex_file)\n", "\n", "[final_doc] = patch_generate_lib.find_all_api_calls([doc], logger=global_logger)\n", "for index, node in enumerate(final_doc.ts_nodes):\n", "    crange = final_doc.tsnode_to_crange(node)\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : [{index:05d}] {node.type:16s} : crange = {crange}\")\n", "    text = doc.doc.code[crange.start: crange.stop]\n", "    print(colored(text, color=get_color()), end=\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "class NewModel(AbstractModel):\n", "\n", "    def __init__(self, checkpoint: str):\n", "        self.checkpoint = checkpoint\n", "    \n", "    def load(self):\n", "        self.model = torch.load(self.checkpoint)\n", "\"\"\"\n", "\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for node in target_nodes:\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    if node.type in (\"parameters\", \"argument_list\", \"call\"):\n", "        print(breaker * 4 + f\" :: parents :: {node.parent.type}\")\n", "        print(colored(decode_bytes(node.parent.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")\n", "        crange = doc.bmap.tsnode_to_crange(node.parent)\n", "        parsed_text = doc.code[crange.start : crange.stop]\n", "        print(colored(parsed_text, color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "class NewModel(AbstractModel):\n", "    '''xxxxx.'''\n", "\n", "    def __init__(self, checkpoint: str):\n", "        self.checkpoint = checkpoint\n", "    \n", "    def load(self):\n", "        self.model = torch.load(self.checkpoint)\n", "\"\"\"\n", "\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for node in target_nodes:\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    if node.type in (\"parameters\", \"argument_list\", \"call\"):\n", "        print(breaker * 4 + f\" :: parents :: {node.parent.type}\")\n", "        print(colored(decode_bytes(node.parent.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")\n", "        crange = doc.bmap.tsnode_to_crange(node.parent)\n", "        parsed_text = doc.code[crange.start : crange.stop]\n", "        print(colored(parsed_text, color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "def parse_cmd():\n", "    '''Parse command line args.'''\n", "    parser = argparse.ArgumentParser()\n", "\n", "    # System configuration\n", "    parser.add_argument(\n", "        \"--image_name\",\n", "        type=str,\n", "        required=True,\n", "        help=\"The docker image name.\",\n", "    )\n", "    parser.add_argument(\n", "        \"--output_dir\",\n", "        type=str,\n", "        required=True,\n", "        help=\"The output directory.\",\n", "    )\n", "    return parser.parse_args()\n", "\"\"\"\n", "\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for node in target_nodes:\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    if node.type in (\"parameters\", \"argument_list\", \"call\"):\n", "        print(breaker * 4 + f\" :: parents :: {node.parent.type}\")\n", "        print(colored(decode_bytes(node.parent.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")\n", "        crange = doc.bmap.tsnode_to_crange(node.parent)\n", "        parsed_text = doc.code[crange.start : crange.stop]\n", "        print(colored(parsed_text, color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from research.eval.patch_lib import Patch\n", "\n", "source_path = \"/home/<USER>/cache/data-patches/google_pyglove_v1.0/filter-stage-2-target-docs.torch\"\n", "doc_dicts = torch.load(source_path)\n", "doc = doc_dicts[0]\n", "nodes = doc[\"ts_node_dicts\"]\n", "print(list(doc.keys()))\n", "print(list(nodes[0].keys()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval import hydra\n", "\n", "HYDRA_SOFT_TIMEOUT_SECS = 600\n", "HYDRA_HARD_TIMEOUT_SECS = 1800\n", "hydra_driver = hydra.Driver(\n", "    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,\n", "    hydra_block_resource_internet_access=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.patch_lib import Patch\n", "from research.static_analysis.common import decode_bytes\n", "\n", "patch = Patch(\n", "    char_start=nodes[0][\"node.char_range\"].start,\n", "    char_end=nodes[0][\"node.char_range\"].stop,\n", "    file_content=doc[\"code\"],\n", "    file_name=doc[\"scope_tree\"].name,\n", "    patch_content=decode_bytes(nodes[0][\"node.text\"]),\n", "    commit_sha=\"870ed11798703e56e9c9523af5b52bd92ec320a4\",\n", "    patch_id=\"google/pyglove/12345678\",\n", ")\n", "results = hydra_driver.dispatch(patch, image_name=doc[\"image_name\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(doc[\"image_name\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import hashlib\n", "\n", "def normalize_code(code: str):\n", "    # Convert to lowercase\n", "    code = code.lower()\n", "    # Remove comments (assuming comments start with #)\n", "    lines = [line.split('#')[0].strip() for line in code.split('\\n')]\n", "    # Remove the empty lines and extra whitespaces\n", "    lines = [' '.join(x.split()) for x in lines if x]\n", "    # Only keep a-z 0-9\n", "    code = \"\".join(lines)\n", "    return re.sub(r'[^a-z0-9]', '', code.lower())\n", "\n", "def get_md5(text):\n", "    return hashlib.md5(text.encode()).hexdigest()\n", "\n", "# Your code snippets\n", "snippets = [\n", "    \"for i in range(10): print(i)\",  # Original\n", "    \"for i in range(10):print(i)\",  # No space\n", "    \"for i in range( 10 ) : print ( i )\",  # Extra spaces\n", "    \"# This is a comment\\nfor i in range(10): print(i)\",  # With comment\n", "    \"for j in range(10): print(j)\",  # Different variable\n", "]\n", "\n", "for code in snippets:\n", "    print(f\"{code:60s} vs. {normalize_code(code)} vs. {get_md5(normalize_code(code))}\")\n", "\n", "# # Normalize and hash the code snippets\n", "# hashed_snippets = {get_md5(normalize_code(snippet)) for snippet in snippets}\n", "\n", "# # If length is less than the original, duplicates were removed\n", "# if len(hashed_snippets) < len(snippets):\n", "#     print(f\"Found {len(snippets) - len(hashed_snippets)} duplicates.\")\n", "\n", "# # Display unique snippets\n", "# unique_snippets = set()\n", "# for snippet in snippets:\n", "#     hash_value = get_md5(normalize_code(snippet))\n", "#     if hash_value in hashed_snippets:\n", "#         unique_snippets.add(snippet)\n", "#         hashed_snippets.remove(hash_value)\n", "\n", "# print(\"Unique Snippets:\")\n", "# for snippet in unique_snippets:\n", "#     print(snippet)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import ast\n", "\n", "def simplify_tree(tree):\n", "    \"\"\"Simplify an AST by keeping only the type of each node, while maintaining specific keywords and function names.\"\"\"\n", "    if isinstance(tree, ast.AST):\n", "        node_type = type(tree).__name__\n", "\n", "        # Special handling for function and keyword names\n", "        if isinstance(tree, ast.Call) and hasattr(tree.func, 'id'):\n", "            node_type = tree.func.id\n", "        elif isinstance(tree, ast.keyword):\n", "            node_type = tree.arg\n", "        elif isinstance(tree, ast.For):\n", "            node_type = 'for'\n", "\n", "        return {node_type: [simplify_tree(child) for field, child in ast.iter_fields(tree) if isinstance(child, (ast.AST, list))]}\n", "    elif isinstance(tree, list):\n", "        return [simplify_tree(child) for child in tree]\n", "    else:\n", "        return None\n", "\n", "def ast_similar(tree1, tree2):\n", "    return simplify_tree(tree1) == simplify_tree(tree2)\n", "\n", "# Parse the code snippets into ASTs\n", "tree1 = ast.parse(\"for i in range(10): print(i)\").body[0]\n", "tree2 = ast.parse(\"for j in range(10): print(j)\").body[0]\n", "\n", "# Compare the simplified ASTs\n", "if ast_similar(tree1, tree2):\n", "    print(\"The code snippets are structurally similar.\")\n", "else:\n", "    print(\"The code snippets are different.\")\n", "\n", "print(\"\\n\")\n", "simplify_tree(tree1)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['documents', 'image_name'])\n", "Load 28 documents\n"]}], "source": ["import collections\n", "import logging\n", "import torch\n", "import tree_sitter as ts\n", "from pathlib import Path\n", "from research.static_analysis.parsing import ScopeTreeParser\n", "from research.static_analysis.usage_analysis import ParsedFile, UsageIndex\n", "import collections\n", "from termcolor import colored\n", "import tree_sitter as ts\n", "from research.static_analysis.common import decode_bytes\n", "from research.static_analysis.parsing import (\n", "    ScopeOrSpan,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    SrcScope,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "# from experimental.dxy.exps import patch_generate_lib\n", "from research.static_analysis.experimental_parsing import AugmentedParsedFile, AugmentedTSNode\n", "\n", "\n", "# xpath = \"/mnt/efs/augment/user/dxy/data-patches/google_pyglove_v1.0/api-raw-ge2lines_or_ge3paren-sys-basic-sc-1b-basic-sc-3b-basic-sc-7b/all-results.torch\"\n", "xpath = \"/mnt/efs/augment/user/dxy/data-patches-v2/google_pyglove_v1.0/api-raw-debug-hydra-hydra/api-raw-debug-hydra-hydra.torch\"\n", "\n", "all_data = torch.load(xpath)\n", "print(all_data.keys())\n", "docs = all_data[\"documents\"]\n", "docs = [AugmentedParsedFile.from_dict(x) for x in docs]\n", "print(f\"Load {len(docs)} documents\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'raw_label': 'api_call:class_head', 'image_name': 'google/pyglove:v1.0', 'text.#tokens': 100, 'hydra-patch_is_assert0=1': {'result_str': 'google/pyglove:v1.0/00_28-00_4hydra-patch_is_assert0=1                          \\x1b[31mFAILED\\x1b[0m     wall_time=14.40     test_time=14.40', 'status': 'complete'}, 'hydra-patch_is_empty': {'result_str': 'google/pyglove:v1.0/00_28-00_4hydra-patch_is_empty                              \\x1b[31mFAILED\\x1b[0m     wall_time=13.91     test_time=13.91', 'status': 'complete'}, 'hydra-patch_to_pass': {'result_str': 'google/pyglove:v1.0/00_28-00_4hydra-patch_to_pass                               \\x1b[32mPASSED\\x1b[0m     wall_time=13.71     test_time=13.71', 'status': 'complete'}}\n"]}], "source": ["print(docs[0].ts_nodes[0].extra)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "def create_field(\n", "    maybe_field: Union[<PERSON>, <PERSON>ple],   # pylint: disable=g-bare-generic\n", "    auto_typing: bool = True,\n", "    accept_value_as_annotation: bool = True\n", ") -> Field:\n", "  \"\"\"Creates ``Field`` from its equivalence.\n", "\n", "  Args:\n", "    maybe_field: a ``Field`` object or its equivalence, which is a tuple of\n", "      2 - 4 elements:\n", "      `(<key>, <value>, [description], [metadata])`.\n", "      `key` can be a KeySpec subclass object or string. `value` can be a\n", "      ValueSpec subclass object or equivalent value. (see\n", "      ``ValueSpec.from_value`` method). `description` is the description of this\n", "      field. It can be optional when this field overrides the default value of a\n", "      field defined in parent schema. `metadata` is an optional field which is a\n", "      dict of user objects.\n", "    auto_typing: If True, infer value spec from Python annotations. Otherwise,\n", "      ``pg.typing.Any()`` will be used.\n", "    accept_value_as_annotation: If True, allow default values to be used as\n", "      annotations when creating the value spec.\n", "\n", "  Returns:\n", "    A ``Field`` object.\n", "  \"\"\"\n", "  if isinstance(maybe_field, Field):\n", "    return maybe_field\n", "\n", "  if not isinstance(maybe_field, tuple):\n", "    raise TypeError(\n", "        f'Field definition should be tuples with 2 to 4 elements. '\n", "        f'Encountered: {maybe_field}.')\n", "\n", "  if len(maybe_field) == 4:\n", "    maybe_key_spec, maybe_value_spec, description, field_metadata = maybe_field\n", "  elif len(maybe_field) == 3:\n", "    maybe_key_spec, maybe_value_spec, description = maybe_field\n", "    field_metadata = {}\n", "  elif len(maybe_field) == 2:\n", "    maybe_key_spec, maybe_value_spec = maybe_field\n", "    description = None\n", "    field_metadata = {}\n", "  else:\n", "    raise TypeError(\n", "        f'Field definition should be tuples with 2 to 4 elements. '\n", "        f'Encountered: {maybe_field}.')\n", "\n", "  if isinstance(maybe_key_spec, (str, KeySpec)):\n", "    key = maybe_key_spec\n", "  else:\n", "    raise TypeError(\n", "        f'The 1st element of field definition should be of '\n", "        f'<class \\'str\\'> or KeySpec. Encountered: {maybe_key_spec}.')\n", "  value = ValueSpec.from_annotation(\n", "      maybe_value_spec,\n", "      auto_typing=auto_typing,\n", "      accept_value_as_annotation=accept_value_as_annotation)\n", "  if (description is not None and\n", "      not isinstance(description, str)):\n", "    raise TypeError(f'Description (the 3rd element) of field definition '\n", "                    f'should be text type. Encountered: {description}')\n", "  if not isinstance(field_metadata, dict):\n", "    raise TypeError(f'Metadata (the 4th element) of field definition '\n", "                    f'should be a dict of objects. '\n", "                    f'Encountered: {field_metadata}')\n", "  return Field(key, value, description, field_metadata)\n", "----------------------------------------------------------------------------------------------------\n", "def __init__(\n", "      self,\n", "      key_spec: Union[KeySpec, str],\n", "      value_spec: ValueSpec,\n", "      description: Optional[str] = None,\n", "      metadata: Optional[Dict[str, Any]] = None):\n", "    \"\"\"Con<PERSON><PERSON>ctor.\n", "\n", "    Args:\n", "      key_spec: Key specification of the field. Can be a string or a KeySpec\n", "        instance.\n", "      value_spec: Value specification of the field.\n", "      description: Description of the field.\n", "      metadata: A dict of objects as metadata for the field.\n", "\n", "    Raises:\n", "      ValueError: metadata is not a dict.\n", "    \"\"\"\n", "    if isinstance(key_spec, str):\n", "      key_spec = KeySpec.from_str(key_spec)\n", "    assert isinstance(key_spec, KeySpec), key_spec\n", "    self._key = key_spec\n", "    self._value = value_spec\n", "    self._description = description\n", "\n", "    if metadata and not isinstance(metadata, dict):\n", "      raise ValueError('metadata must be a dict.')\n", "    self._metadata = metadata or {}\n", "----------------------------------------------------------------------------------------------------\n", "def apply(\n", "      self,\n", "      value: Any,\n", "      allow_partial: bool = False,\n", "      transform_fn: Optional[Callable[\n", "          [object_utils.KeyPath, 'Field', Any], Any]] = None,\n", "      root_path: Optional[object_utils.KeyPath] = None) -> Any:\n", "    \"\"\"Apply current field to a value, which validate and complete the value.\n", "\n", "    Args:\n", "      value: Value to validate against this spec.\n", "      allow_partial: Whether partial value is allowed. This is for dict or\n", "        nested dict values.\n", "      transform_fn: Function to transform applied value into final value.\n", "      root_path: Key path for root.\n", "\n", "    Returns:\n", "      final value.\n", "      When allow_partial is set to False (default), only fully qualified value\n", "      is acceptable. When allow_partial is set to True, missing fields will\n", "      be placeheld using MISSING_VALUE.\n", "\n", "    Raises:\n", "      KeyError: if additional key is found in value, or required key is missing\n", "        and allow_partial is set to False.\n", "      TypeError: if type of value is not the same as spec required.\n", "      ValueError: if value is not acceptable, or value is MISSING_VALUE while\n", "        allow_partial is set to False.\n", "    \"\"\"\n", "    value = self._value.apply(value, allow_partial, transform_fn, root_path)\n", "    if transform_fn:\n", "      value = transform_fn(root_path, self, value)\n", "    return value\n", "----------------------------------------------------------------------------------------------------\n", "def __init__(\n", "      self,\n", "      fields: List[Field],\n", "      name: Optional[str] = None,\n", "      base_schema_list: Optional[List['Schema']] = None,\n", "      description: Optional[str] = None,\n", "      *,\n", "      allow_nonconst_keys: bool = False,\n", "      metadata: Optional[Dict[str, Any]] = None):\n", "    \"\"\"Con<PERSON><PERSON>ctor.\n", "\n", "    Args:\n", "      fields: A list of Field as the definition of the schema. The order of the\n", "        fields will be preserved.\n", "      name: Optional name of this schema. Useful for debugging.\n", "      base_schema_list: List of schema used as base. When present, fields\n", "        from these schema will be copied to this schema. Fields from the\n", "        latter schema will override those from the former ones.\n", "      description: Optional str as the description for the schema.\n", "      allow_nonconst_keys: Whether immediate fields can use non-const keys.\n", "      metadata: Optional dict of user objects as schema-level metadata.\n", "\n", "    Raises:\n", "      TypeError: Argument `fields` is not a list.\n", "      KeyError: If a field name contains characters ('.') which is not\n", "        allowed, or a field name from `fields` already exists in parent\n", "        schema.\n", "      ValueError: When failed to create ValueSpec from `fields`.\n", "        It could be an unsupported value type, default value doesn't conform\n", "        with value specification, etc.\n", "    \"\"\"\n", "    if not isinstance(fields, list):\n", "      raise TypeError(\n", "          f\"Argument 'fields' must be a list. Encountered: {fields}.\"\n", "      )\n", "\n", "    self._name = name\n", "    self._allow_nonconst_keys = allow_nonconst_keys\n", "    self._fields = {f.key: f for f in fields}\n", "    self._description = description\n", "    self._metadata = metadata or {}\n", "\n", "    self._dynamic_field = None\n", "    for f in fields:\n", "      if not f.key.is_const:\n", "        self._dynamic_field = f\n", "        break\n", "\n", "    if base_schema_list:\n", "      # Extend base schema from the nearest ancestor to the farthest.\n", "      for base in reversed(base_schema_list):\n", "        self.extend(base)\n", "\n", "    if not allow_nonconst_keys and self._dynamic_field is not None:\n", "      raise ValueError(\n", "          f'NonConstKey is not allowed in schema. '\n", "          f'Encountered \\'{self._dynamic_field.key}\\'.')\n", "----------------------------------------------------------------------------------------------------\n", "def resolve(\n", "      self, keys: Iterable[str]\n", "  ) -> Tuple[Dict[KeySpec, List[str]], List[str]]:\n", "    \"\"\"Resolve keys by grouping them by their matched fields.\n", "\n", "    Args:\n", "      keys: A list of string keys.\n", "\n", "    Returns:\n", "      A tuple of matched key results and unmatched keys.\n", "        Matched key results are an ordered dict of KeySpec to matched keys,\n", "        in field declaration order.\n", "        Unmatched keys are strings from input.\n", "    \"\"\"\n", "    keys = list(keys)\n", "    input_keyset = set(keys)\n", "    nonconst_key_specs = [k for k in self._fields.keys() if not k.is_const]\n", "    nonconst_keys = {k: [] for k in nonconst_key_specs}\n", "    unmatched_keys = []\n", "    keys_by_key_spec = dict()\n", "\n", "    for key in keys:\n", "      if key not in self._fields:\n", "        matched_nonconst_keys = False\n", "        for key_spec in nonconst_key_specs:\n", "          if key_spec.match(key):\n", "            nonconst_keys[key_spec].append(key)\n", "            matched_nonconst_keys = True\n", "            break\n", "        if not matched_nonconst_keys:\n", "          unmatched_keys.append(key)\n", "\n", "    for key_spec in self._fields.keys():\n", "      keys = []\n", "      if not key_spec.is_const:\n", "        keys = nonconst_keys.get(key_spec, [])\n", "      elif key_spec in input_keyset:\n", "        keys.append(str(key_spec))\n", "      keys_by_key_spec[key_spec] = keys\n", "\n", "    return (keys_by_key_spec, unmatched_keys)\n", "----------------------------------------------------------------------------------------------------\n", "def apply(\n", "      self,\n", "      dict_obj: Dict[str, Any],\n", "      allow_partial: bool = False,\n", "      child_transform: Optional[Callable[\n", "          [object_utils.<PERSON><PERSON><PERSON>, <PERSON>, Any], Any]] = None,\n", "      root_path: Optional[object_utils.KeyPath] = None,\n", "  ) -> Dict[str, Any]:  # pyformat: disable\n", "    # pyformat: disable\n", "    \"\"\"Apply this schema to a dict object, validate and transform it.\n", "\n", "    Args:\n", "      dict_obj: JSON dict type that (maybe) conform to the schema.\n", "      allow_partial: Whether allow partial object to be created.\n", "      child_transform: Function to transform child node values in dict_obj into\n", "        their final values. Transform function is called on leaf nodes first,\n", "        then on their containers, recursively.\n", "        The signature of transform_fn is: `(path, field, value) -> new_value`\n", "        Argument `path` is a KeyPath object to the field. Argument `field` is\n", "        on which Field the value should apply. Argument `value` is the value\n", "        from input that matches a Field from the schema, with child fields\n", "        already transformed by this function.\n", "        There are possible values for these two arguments::\n", "\n", "          ------------------------------------------------------------\n", "                                  |   field       | value\n", "          ------------------------------------------------------------\n", "          The value with          |               |\n", "          applicable Field is     |   Not None    | Not MISSING_VALUE\n", "          found in schema.        |               |\n", "          value.                  |               |\n", "          ------------------------------------------------------------\n", "          The value is            |               |\n", "          not present for a       |   Not None    | MISSING_VALUE\n", "          key defined in schema.  |               |\n", "          ------------------------------------------------------------\n", "\n", "        Return value will be inserted to the parent dict under path, unless\n", "        return value is MISSING_VALUE.\n", "      root_path: KeyPath of root element of dict_obj.\n", "\n", "    Returns:\n", "      A dict filled by the schema with transformed values.\n", "\n", "    Raises:\n", "      KeyError: Key is not allowed in schema.\n", "      TypeError: Type of dict values are not aligned with schema.\n", "      ValueError: Value of dict values are not aligned with schema.\n", "    \"\"\"  # pyformat: enable\n", "    matched_keys, unmatched_keys = self.resolve(dict_obj.keys())\n", "    if unmatched_keys:\n", "      raise KeyError(\n", "          f'Keys {unmatched_keys} are not allowed in Schema. '\n", "          f'(parent=\\'{root_path}\\')')\n", "\n", "    for key_spec, keys in matched_keys.items():\n", "      field = self._fields[key_spec]\n", "      # For missing const keys, we add to keys collection to add missing value.\n", "      if key_spec.is_const and key_spec not in keys:\n", "        keys.append(str(key_spec))\n", "      for key in keys:\n", "        if dict_obj:\n", "          value = dict_obj.get(key, object_utils.MISSING_VALUE)\n", "        else:\n", "          value = object_utils.MISSING_VALUE\n", "        # NOTE(daiyip): field.default_value may be MISSING_VALUE too\n", "        # or partial.\n", "        if object_utils.MISSING_VALUE == value:\n", "          value = copy.deepcopy(field.default_value)\n", "\n", "        child_path = object_utils.KeyPath(key, root_path)\n", "        new_value = field.apply(\n", "            value, allow_partial, child_transform, child_path)\n", "\n", "        # NOTE(daiyip): `pg.Dict.__getitem__`` has special logics in handling\n", "        # `pg.Contextual`` values. Therefore, we user `dict.__getitem__()`` to\n", "        # avoid triggering side effect.\n", "        if (key not in dict_obj\n", "            or dict.__getitem__(dict_obj, key) is not new_value):\n", "          # NOTE(daiyip): minimize call to __setitem__ when possible.\n", "          # Custom like symbolic dict may trigger additional logic\n", "          # when __setitem__ is called.\n", "          dict_obj[key] = new_value\n", "    return dict_obj\n", "----------------------------------------------------------------------------------------------------\n", "def validate(self,\n", "               dict_obj: Dict[str, Any],\n", "               allow_partial: bool = False,\n", "               root_path: Optional[object_utils.KeyPath] = None) -> None:\n", "    \"\"\"Validates whether dict object is conformed with the schema.\"\"\"\n", "    self.apply(\n", "        copy.deepcopy(dict_obj),\n", "        allow_partial=allow_partial,\n", "        root_path=root_path)\n", "----------------------------------------------------------------------------------------------------\n", "def get(self,\n", "          key: Union[str, KeySpec],\n", "          default: Optional[Field] = None\n", "          ) -> Optional[Field]:\n", "    \"\"\"Returns field by key with default value if not found.\"\"\"\n", "    return self._fields.get(key, default)\n", "----------------------------------------------------------------------------------------------------\n", "def format(\n", "      self,\n", "      compact: bool = False,\n", "      verbose: bool = True,\n", "      root_indent: int = 0,\n", "      cls_name: Optional[str] = None,\n", "      bracket_type: object_utils.BracketType = object_utils.BracketType.ROUND,\n", "      **kwargs) -> str:\n", "    \"\"\"Format current Schema into nicely printed string.\"\"\"\n", "    if cls_name is None:\n", "      cls_name = '<PERSON><PERSON><PERSON>'\n", "\n", "    def _indent(text, indent):\n", "      return ' ' * 2 * indent + text\n", "\n", "    def _format_child(child):\n", "      return child.format(\n", "          compact=compact,\n", "          verbose=verbose,\n", "          root_indent=root_indent + 1,\n", "          **kwargs)\n", "\n", "    open_bracket, close_bracket = object_utils.bracket_chars(bracket_type)\n", "    if compact:\n", "      s = [f'{cls_name}{open_bracket}']\n", "      s.append(', '.join([\n", "          f'{f.key}={_format_child(f.value)}'\n", "          for f in self.fields.values()\n", "      ]))\n", "      s.append(close_bracket)\n", "    else:\n", "      s = [f'{cls_name}{open_bracket}\\n']\n", "      last_field_show_description = False\n", "      for i, f in enumerate(self.fields.values()):\n", "        this_field_show_description = verbose and f.description\n", "        if i != 0:\n", "          s.append(',\\n')\n", "          if last_field_show_description or this_field_show_description:\n", "            s.append('\\n')\n", "        if this_field_show_description:\n", "          s.append(_indent(f'# {f.description}\\n', root_indent + 1))\n", "        last_field_show_description = this_field_show_description\n", "        s.append(\n", "            _indent(f'{f.key} = {_format_child(f.value)}', root_indent + 1))\n", "      s.append('\\n')\n", "      s.append(_indent(close_bracket, root_indent))\n", "    return ''.join(s)\n", "----------------------------------------------------------------------------------------------------\n", "return <PERSON><PERSON><PERSON>(\n", "      fields=[create_field(maybe_field) for maybe_field in fields],\n", "      name=name,\n", "      base_schema_list=base_schema_list,\n", "      allow_nonconst_keys=allow_nonconst_keys,\n", "      metadata=metadata,\n", "      description=description,\n", "  )\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def is_const(self) -> bool:\n", "    \"\"\"Returns whether current key is const.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def match(self, key: Any) -> bool:\n", "    \"\"\"Returns whether current key specification can match a key.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def extend(self, base: 'KeySpec') -> 'KeySpec':\n", "    \"\"\"Extend base key specification and returns self.\n", "\n", "    NOTE(daiyip): When a ``Field`` extends a base Field (from a base schema),\n", "    it calls ``extend`` on both its ``KeySpec`` and ``ValueSpec``.\n", "    ``KeySpec.extend`` is to determine whether the ``Field`` key is allowed to\n", "    be extended, and ``ValueSpec.extend`` is to determine the final\n", "    ``ValueSpec`` after extension.\n", "\n", "    Args:\n", "      base: A base ``KeySpec`` object.\n", "\n", "    Returns:\n", "      An ``KeySpec`` object derived from this key spec by extending the base.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@classmethod\n", "  def from_str(cls, key: str) -> 'KeySpec':\n", "    \"\"\"Get a concrete ValueSpec from annotation.\"\"\"\n", "    del key\n", "    assert False, '<PERSON>ridden in `key_specs.py`.'\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def module(self) -> types.ModuleType:\n", "    \"\"\"Returns the module where the name is being referenced.\"\"\"\n", "    return self._module\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def name(self) -> str:\n", "    \"\"\"Returns the name of the type reference.\"\"\"\n", "    return self._name\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def qualname(self) -> str:\n", "    \"\"\"Returns the qualified name of the reference.\"\"\"\n", "    return f'{self.module.__name__}.{self.name}'\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def resolved(self) -> bool:\n", "    \"\"\"Returns True if the symbol for the name is resolved..\"\"\"\n", "    return hasattr(self.module, self.name)\n", "----------------------------------------------------------------------------------------------------\n", "@functools.cached_property\n", "  def cls(self) -> Type[Any]:\n", "    \"\"\"Returns the resolved reference class..\"\"\"\n", "    reference = getattr(self.module, self.name, None)\n", "    if reference is None:\n", "      raise TypeError(\n", "          f'{self.name!r} does not exist in module {self.module.__name__!r}'\n", "      )\n", "    elif not inspect.isclass(reference):\n", "      raise TypeError(\n", "          f'{self.name!r} from module {self.module.__name__!r} is not a class.'\n", "      )\n", "    return reference\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def value_type(self) -> Union[\n", "      Type[Any],\n", "      Tuple[Type[Any], ...]]:  # pyformat: disable\n", "    \"\"\"Returns acceptable (resolved) value type(s).\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def forward_refs(self) -> Set[ForwardRef]:\n", "    \"\"\"Returns forward referenes used by the value spec.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def noneable(self) -> 'ValueSpec':\n", "    \"\"\"Marks none-able and returns `self`.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def is_noneable(self) -> bool:\n", "    \"\"\"Returns True if current value spec accepts None.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def set_default(self,\n", "                  default: Any,\n", "                  use_default_apply: bool = True) -> 'ValueSpec':\n", "    \"\"\"Sets the default value and returns `self`.\n", "\n", "    Args:\n", "      default: Default value.\n", "      use_default_apply: If True, invoke `apply` to the value, otherwise use\n", "        default value as is.\n", "\n", "    Returns:\n", "      ValueSpec itself.\n", "\n", "    Raises:\n", "      ValueError: If default value cannot be applied when use_default_apply\n", "        is set to True.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def default(self) -> Any:\n", "    \"\"\"Returns the default value.\n", "\n", "    If no default is provided, MISSING_VALUE will be returned for non-dict\n", "    types. For Dict type, a dict that may contains nested MISSING_VALUE\n", "    will be returned.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def has_default(self) -> bool:\n", "    \"\"\"Returns True if the default value is provided.\"\"\"\n", "    return self.default != object_utils.MISSING_VALUE\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def freeze(\n", "      self,\n", "      permanent_value: Any = object_utils.MISSING_VALUE,\n", "      apply_before_use: bool = True) -> 'ValueSpec':\n", "    \"\"\"Sets the default value using a permanent value and freezes current spec.\n", "\n", "    A frozen value spec will not accept any value that is not the default\n", "    value. A frozen value spec is useful when a subclass fixes the value of a\n", "    symoblic attribute and want to prevent it from being modified.\n", "\n", "    Args:\n", "      permanent_value: A permanent value used for current spec.\n", "        If MISSING_VALUE, freeze the value spec with current default value.\n", "      apply_before_use: If True, invoke `apply` on permanent value\n", "        when permanent_value is provided, otherwise use it as is.\n", "\n", "    Returns:\n", "      ValueSpec itself.\n", "\n", "    Raises:\n", "      ValueError if current default value is MISSING_VALUE and the permanent\n", "        value is not specified.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def frozen(self) -> bool:\n", "    \"\"\"Returns True if current value spec is frozen.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def annotation(self) -> Any:\n", "    \"\"\"Returns PyType annotation. MISSING_VALUE if annotation is absent.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  @abc.abstractmethod\n", "  def user_validator(\n", "      self) -> Optional[Callable[[Any], None]]:\n", "    \"\"\"Returns a user validator which is used for custom validation logic.\"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def is_compatible(self, other: 'ValueSpec') -> bool:\n", "    \"\"\"Returns True if values acceptable to `other` is acceptable to this spec.\n", "\n", "    Args:\n", "      other: Other value spec.\n", "\n", "    Returns:\n", "      True if values that is applicable to the other value spec can be applied\n", "        to current spec. Otherwise False.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def extend(self, base: 'ValueSpec') -> 'ValueSpec':\n", "    \"\"\"Extends a base spec with current spec's rules.\n", "\n", "    Args:\n", "      base: Base ValueSpec to extend.\n", "\n", "    Returns:\n", "      ValueSpec itself.\n", "\n", "    Raises:\n", "      TypeError: When this value spec cannot extend from base.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@abc.abstractmethod\n", "  def apply(\n", "      self,\n", "      value: Any,\n", "      allow_partial: bool = False,\n", "      child_transform: Optional[Callable[\n", "          [object_utils.KeyPath, 'Field', Any], Any]] = None,\n", "      root_path: Optional[object_utils.KeyPath] = None) -> Any:\n", "    \"\"\"Validates, completes and transforms the input value.\n", "\n", "    Here is the procedure of ``apply``::\n", "\n", "       (1). Choose the default value if the input value is ``MISSING_VALUE``\n", "       (2). Check whether the input value is None.\n", "         (2.a) Input value is None and ``value_spec.is_noneable()`` is False,\n", "               raises Error.\n", "         (2.b) Input value is not None or ``value_spec.is_noneable()`` is True,\n", "               goto step (3).\n", "       (3). Run ``value_spec.custom_apply`` if the input value is a\n", "            ``CustomTyping`` instance.\n", "         (3.a). If ``value_spec.custom_apply`` returns a value that indicates to\n", "                proceed with standard apply, goto step (4).\n", "         (3.b). <PERSON><PERSON> goto step (6)\n", "       (4). Check the input value type against the ``value_spec.value_type``.\n", "         (4.a). If their value type matches, go to step (5)\n", "         (4.b). Else if there is a converter registered between input value type\n", "                and the value spec's value type, perform the conversion, and go\n", "                to step (5). (see pg.typing.register_converter)\n", "         (4.c)  Otherwise raises type mismatch.\n", "       (5). Perform type-specific and user validation and transformation.\n", "            For complex types such as Dict, List, Tuple, call `child_spec.apply`\n", "            recursively on the child fields.\n", "       (6). Perform user transform and returns final value\n", "            (invoked at Field.apply.)\n", "\n", "    Args:\n", "      value: Input value to apply.\n", "      allow_partial: If True, partial value is allowed. This is useful for\n", "        container types (dict, list, tuple).\n", "      child_transform: Function to transform child node values into final\n", "        values.\n", "        (NOTE: This transform will not be performed on current value. Instead\n", "        transform on current value is done by Field.apply, which has adequate\n", "        information to call transform with both KeySpec and ValueSpec).\n", "      root_path: Key path of current node.\n", "\n", "    Returns:\n", "      Final value:\n", "\n", "        * When allow_partial is set to False (default), only input value that\n", "          has no missing values can be applied.\n", "        * When allow_partial is set to True, missing fields will be placeheld\n", "          using MISSING_VALUE.\n", "\n", "    Raises:\n", "      KeyError: If additional key is found in value, or required key is missing\n", "        and allow_partial is set to False.\n", "      TypeError: If type of value is not the same as spec required.\n", "      ValueError: If value is not acceptable, or value is MISSING_VALUE while\n", "        allow_partial is set to False.\n", "    \"\"\"\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def type_resolved(self) -> bool:\n", "    \"\"\"Returns True if all forward references are resolved.\"\"\"\n", "    return not any(not ref.resolved for ref in self.forward_refs)\n", "----------------------------------------------------------------------------------------------------\n", "@classmethod\n", "  def from_annotation(\n", "      cls,\n", "      annotation: Any,\n", "      auto_typing=False,\n", "      accept_value_as_annotation=False) -> 'ValueSpec':\n", "    \"\"\"Gets a concrete ValueSpec from annotation.\"\"\"\n", "    del annotation\n", "    assert False, 'Overridden in `annotation_conversion.py`.'\n", "----------------------------------------------------------------------------------------------------\n", "@classmethod\n", "  def from_annotation(\n", "      cls,\n", "      key: Union[str, KeySpec],\n", "      annotation: Any,\n", "      description: Optional[str] = None,\n", "      metadata: Optional[Dict[str, Any]] = None,\n", "      auto_typing=True) -> 'Field':\n", "    \"\"\"Gets a Field from annotation.\"\"\"\n", "    del key, annotation, description, metadata, auto_typing\n", "    assert False, 'Overridden in `annotation_conversion.py`.'\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def description(self) -> Optional[str]:\n", "    \"\"\"Description of this field.\"\"\"\n", "    return self._description\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def key(self) -> KeySpec:\n", "    \"\"\"Key specification of this field.\"\"\"\n", "    return self._key\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def value(self) -> ValueSpec:\n", "    \"\"\"Value specification of this field.\"\"\"\n", "    return self._value\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def annotation(self) -> Any:\n", "    \"\"\"Type annotation for this field.\"\"\"\n", "    return self._value.annotation\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def default_value(self) -> Any:\n", "    \"\"\"Returns the default value.\"\"\"\n", "    return self._value.default\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def frozen(self) -> bool:\n", "    \"\"\"Returns True if current field's value is frozen.\"\"\"\n", "    return self._value.frozen\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def dynamic_field(self) -> Optional[Field]:\n", "    \"\"\"Returns the field that matches multiple keys if any.\"\"\"\n", "    return self._dynamic_field\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def name(self) -> Optional[str]:\n", "    \"\"\"Name of this schema.\"\"\"\n", "    return self._name\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def allow_nonconst_keys(self) -> bool:\n", "    \"\"\"Returns whether to allow non-const keys.\"\"\"\n", "    return self._allow_nonconst_keys\n", "----------------------------------------------------------------------------------------------------\n", "@property\n", "  def fields(self) -> Dict[KeySpec, Field]:\n", "    \"\"\"Returns fields of this schema.\"\"\"\n", "    return self._fields\n", "----------------------------------------------------------------------------------------------------\n", "value = ValueSpec.from_annotation(\n", "      maybe_value_spec,\n", "      auto_typing=auto_typing,\n", "      accept_value_as_annotation=accept_value_as_annotation)\n", "----------------------------------------------------------------------------------------------------\n", "return self.to_json_dict(\n", "        fields=dict(\n", "            key_spec=self._key,\n", "            value_spec=self._value,\n", "            description=self._description,\n", "            metadata=self._metadata,\n", "        ),\n", "        **kwargs,\n", "    )\n", "----------------------------------------------------------------------------------------------------\n", "def _merge_field(\n", "        path,\n", "        parent_field: Field,\n", "        child_field: Field) -> Field:\n", "      \"\"\"Merge function on field with the same key.\"\"\"\n", "      if parent_field != object_utils.MISSING_VALUE:\n", "        if object_utils.MISSING_VALUE == child_field:\n", "          if (not self._allow_nonconst_keys and not parent_field.key.is_const):\n", "            hints = object_utils.kvlist_str([\n", "                ('base', object_utils.quote_if_str(base.name), None),\n", "                ('path', path, None)\n", "            ])\n", "            raise ValueError(\n", "                f'Non-const key {parent_field.key} is not allowed to be '\n", "                f'added to the schema. ({hints})')\n", "          return copy.deepcopy(parent_field)\n", "        else:\n", "          try:\n", "            child_field.extend(parent_field)\n", "          except Exception as e:  # pylint: disable=broad-except\n", "            hints = object_utils.kvlist_str([\n", "                ('base', object_utils.quote_if_str(base.name), None),\n", "                ('path', path, None)\n", "            ])\n", "            raise e.__class__(f'{e} ({hints})').with_traceback(\n", "                sys.exc_info()[2])\n", "      return child_field\n", "----------------------------------------------------------------------------------------------------\n", "self.apply(\n", "        copy.deepcopy(dict_obj),\n", "        allow_partial=allow_partial,\n", "        root_path=root_path)\n", "----------------------------------------------------------------------------------------------------\n", "return self.to_json_dict(\n", "        fields=dict(\n", "            fields=list(self._fields.values()),\n", "            name=self._name,\n", "            description=self._description,\n", "            allow_nonconst_keys=self._allow_nonconst_keys,\n", "            metadata=self._metadata,\n", "        ),\n", "        **kwargs,\n", "    )\n", "----------------------------------------------------------------------------------------------------\n", "details = object_utils.kvlist_str([\n", "        ('module', self.module.__name__, None),\n", "        ('name', self.name, None),\n", "    ])\n", "----------------------------------------------------------------------------------------------------\n", "metadata = object_utils.format(\n", "        self._metadata,\n", "        compact=compact,\n", "        verbose=verbose,\n", "        root_indent=root_indent + 1,\n", "        **kwargs)\n", "----------------------------------------------------------------------------------------------------\n", "attr_str = object_utils.kvlist_str([\n", "        ('key', self._key, None),\n", "        ('value', self._value.format(\n", "            compact=compact,\n", "            verbose=verbose,\n", "            root_indent=root_indent + 1,\n", "            **kwargs), None),\n", "        ('description', object_utils.quote_if_str(description), None),\n", "        ('metadata', metadata, '{}')\n", "    ])\n", "----------------------------------------------------------------------------------------------------\n", "return child.format(\n", "          compact=compact,\n", "          verbose=verbose,\n", "          root_indent=root_indent + 1,\n", "          **kwargs)\n", "----------------------------------------------------------------------------------------------------\n", "s.append(', '.join([\n", "          f'{f.key}={_format_child(f.value)}'\n", "          for f in self.fields.values()\n", "      ]))\n", "----------------------------------------------------------------------------------------------------\n", "normalized_fields.append(tuple([k] + list(v)))\n", "----------------------------------------------------------------------------------------------------\n", "new_value = field.apply(\n", "            value, allow_partial, child_transform, child_path)\n", "----------------------------------------------------------------------------------------------------\n", "s.append(\n", "            _indent(f'{f.key} = {_format_child(f.value)}', root_indent + 1))\n", "----------------------------------------------------------------------------------------------------\n", "hints = object_utils.kvlist_str([\n", "                ('base', object_utils.quote_if_str(base.name), None),\n", "                ('path', path, None)\n", "            ])\n"]}], "source": ["doc = None\n", "for x in docs:\n", "    if x.doc.scope_tree.name == \"pyglove/core/typing/class_schema.py\":\n", "        doc = x\n", "assert doc is not None\n", "for idx, node in enumerate(doc.ts_nodes):\n", "    print(\"-\" * 100)\n", "    text = doc.get_text_of_node(node)\n", "    print(text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}