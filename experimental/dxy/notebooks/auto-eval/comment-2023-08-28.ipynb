{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import docker\n", "import pathlib\n", "import tempfile\n", "import tarfile\n", "import dataclasses\n", "import xml.etree.ElementTree as xml_et\n", "from typing import Optional\n", "from research.static_analysis.common import LanguageID\n", "from research.fim.completion_sites import get_supported_extensions\n", "from research.static_analysis.usage_analysis import ParsedFile\n", "from experimental.dxy.exps.generate_patches import parse_docs_from_docker_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import tree_sitter as ts\n", "\n", "\n", "def find_node_by_types(\n", "    root_node: ts.<PERSON>, types: tuple[str, ...] = (\"call\",)\n", ") -> list[ts.<PERSON><PERSON>]:\n", "    target_nodes = []\n", "    unique_types = set()\n", "    queue = collections.deque([root_node])\n", "    while queue:\n", "        node = queue.popleft()\n", "        if node.type in types:\n", "            target_nodes.append(node)\n", "        unique_types.add(node.type)\n", "        queue.extend(node.children)\n", "    unique_types = sorted(list(unique_types))\n", "    # print(f\"unique types: {unique_types}\")\n", "    # print(f\"Find {len(target_nodes)} nodes.\")\n", "    return target_nodes\n", "\n", "\n", "filtered_documents: list[ParsedFile] = []\n", "for doc in documents:\n", "    filename = doc.scope_tree.name\n", "    if \"test\" in filename:\n", "        continue\n", "    if (\n", "        filename in filename2coverage\n", "        and filename2coverage[filename].converage_rate <= 0.0\n", "    ):\n", "        print(f\"Did not find any coverage information for {filename}\")\n", "        continue\n", "    filtered_documents.append(doc)\n", "print(f\"After filtering, there are {len(filtered_documents)} documents.\")\n", "\n", "filename2nodes: dict[str, list[ts.Node]] = {}\n", "for cur_doc in filtered_documents:\n", "    cur_nodes = find_node_by_types(cur_doc.ts_tree.root_node)\n", "    if len(cur_nodes):\n", "        filename2nodes[cur_doc.scope_tree.name] = cur_nodes\n", "    else:\n", "        print(f\"Did not find any relevant nodes for {cur_doc}\")\n", "\n", "total_nodes = sum(len(node_list) for node_list in filename2nodes.values())\n", "print(f\"There are {total_nodes} total number of nodes.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.static_analysis.common import decode_bytes\n", "xkeys = list(filename2nodes.keys())\n", "print(filename2nodes[xkeys[11]][2].parent)\n", "print(filename2nodes[xkeys[11]][2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents[111].scope_tree.name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "from termcolor import colored\n", "import tree_sitter as ts\n", "from research.static_analysis.common import decode_bytes\n", "from research.static_analysis.parsing import (\n", "    ScopeOrSpan,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    SrcScope,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "colors = [\"red\", \"green\", \"blue\", \"light_yellow\", \"cyan\"]\n", "color_index = 0\n", "\n", "\n", "def get_color():\n", "    global color_index\n", "    color = colors[color_index % len(colors)]\n", "    color_index += 1\n", "    return color\n", "\n", "\n", "def print_code_via_scope(scope: ScopeOrSpan, as_stub: bool = False):\n", "    \"\"\"Print out its source code using a DFS traversal.\n", "\n", "    This is equivalent to print out `SrcScope.dfs_code()` when `as_stub` is False.\n", "    \"\"\"\n", "    if isinstance(scope, SrcScope):\n", "        # We omit the docstr if print as stub\n", "        prefix_doc = scope.prefix if as_stub else scope.prefix_with_doc()\n", "        print(colored(prefix_doc.code, color=get_color()), end=\"\")\n", "        if as_stub and scope.kind == \"function\":\n", "            print(\"...\")\n", "        else:\n", "            for child in scope.children:\n", "                print_code_via_scope(child, as_stub)\n", "        print(colored(scope.suffix.code, color=get_color()), end=\"\")\n", "    else:\n", "        print(colored(scope.code, color=get_color()), end=\"\")\n", "\n", "\n", "def dfs_print_via_ts(node: ts.Node):\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    for child in node.children:\n", "        dfs_print_via_ts(child)\n", "\n", "\n", "def bfs_print_via_ts(root_node: ts.Node):\n", "    queue = collections.deque([root_node])\n", "\n", "    while queue:\n", "        node = queue.popleft()\n", "\n", "        breaker = \"-\" * 8\n", "        print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "        print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4)\n", "\n", "        # Enqueue child nodes for next level traversal\n", "        queue.extend(node.children)\n", "\n", "\n", "def customized_find(root_node: ts.Node, types: tuple[str, ...] = (\"call\",)):\n", "    target_nodes = []\n", "    unique_types = set()\n", "    queue = collections.deque([root_node])\n", "    while queue:\n", "        node = queue.popleft()\n", "        if node.type in types:\n", "            target_nodes.append(node)\n", "        unique_types.add(node.type)\n", "        queue.extend(node.children)\n", "    unique_types = sorted(list(unique_types))\n", "    print(f\"unique types: {unique_types}\")\n", "    print(f\"Find {len(target_nodes)} nodes.\")\n", "    return target_nodes\n", "\n", "\n", "# This should equal to the original source code\n", "# print_code()\n", "# print(color_index)\n", "# dfs_print_via_ts(documents[2].ts_tree.root_node)\n", "# print(color_index)\n", "\n", "target_nodes = customized_find(\n", "    documents[2].ts_tree.root_node, types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", ")\n", "\n", "\n", "for node in target_nodes:\n", "    if node.type == \"def\":\n", "        node = node.parent\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["documents[2].bmap.tsnode_to_crange(documents[2].ts_tree.root_node.children[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in documents[0].scope_tree.get_all_spans():\n", "    print(x)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(documents[0].ts_tree)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval import patch_lib\n", "from research.environments import cw_docker\n", "from research.eval.dataset_generation_lib.basic_patch_generators import fim_sample\n", "from research.eval.patch_lib import create_patch_from_files\n", "\n", "\n", "def show_patch(patch: patch_lib.Patch):\n", "    for key, value in vars(patch).items():\n", "        print(f\"{key} : {value}\")\n", "\n", "\n", "repo_path = Path(\"/mnt/efs/augment/user/dxy/repos/google/pyglove/v0.4.2\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_sha = \"870ed11798703e56e9c9523af5b52bd92ec320a4\"\n", "all_patches = list(\n", "    fim_sample(\n", "        repo_root=repo_path,\n", "        repository_name=\"pyglove\",\n", "        organization_name=\"google\",\n", "        commit_sha=repo_sha,\n", "        lang=\"python\",\n", "        random_seed=None,\n", "    )\n", ")\n", "print(len(all_patches))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["patch = all_patches[6]\n", "patch.repository = \"google/pyglove\"\n", "patch.commit_sha = repo_sha\n", "patch.patch_id = f\"{patch.repository}/12345678\"\n", "show_patch(patch)\n", "pg_patch = patch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import HydraTask\n", "\n", "task = HydraTask()\n", "hydra_task_patch = task._ex_patches[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval import hydra\n", "\n", "HYDRA_SOFT_TIMEOUT_SECS = 600\n", "HYDRA_HARD_TIMEOUT_SECS = 1800\n", "hydra_driver = hydra.Driver(\n", "    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,\n", "    hydra_block_resource_internet_access=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = hydra_driver.dispatch(hydra_task_patch.to_json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pg_patch.file_name = \"pyglove/ext/evolution/neat.py\"\n", "results = hydra_driver.dispatch(pg_patch.to_json(), \"google/pyglove:v1.0\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}