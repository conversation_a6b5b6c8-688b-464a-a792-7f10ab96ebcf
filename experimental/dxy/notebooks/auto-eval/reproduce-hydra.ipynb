{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 8/8 [00:00<00:00, 57358.00it/s]\n", "INFO:augment.research.eval.harness.tasks.hydra_task:Creating random job id for hydra driver: eval-sln4k2sb\n", "INFO:augment.research.eval.hydra.driver:There are 85 tagged images available on the CW docker registry.\n", "INFO:augment.research.eval.hydra.driver:Hydra driver pod name prefix: hydra-driver-4c6t44vp-local-pod\n", "INFO:augment.research.eval.hydra.driver:Using docker images lookup file: /home/<USER>/src/augment/research/eval/hydra/supported_repos.yaml.\n"]}], "source": ["import termcolor\n", "import tree_sitter as ts\n", "from research.eval.harness.tasks import HydraTask\n", "from research.eval.patch_lib import Path\n", "\n", "task = HydraTask()\n", "driver = task._cache_driver\n", "inputs, _ = task[0]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:SUCCESS: created ConfigMap hydra-driver-4c6t44vp-local-cfg-6lftekjd.\n", "INFO:augment.research.eval.hydra.driver:image name: carperai/trlx:2\n", "INFO:root:SUCCESS: created Pod hydra-driver-4c6t44vp-local-pod-7qsy6bfe.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/0                                                                 \u001b[32mPASSED\u001b[0m     wall_time=7.54      test_time=7.54\n"]}], "source": ["results = driver.dispatch(inputs.extra[\"patch\"])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["print(inputs.extra[\"patch\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}