{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "import tree_sitter as ts\n", "from pathlib import Path\n", "from research.static_analysis.parsing import ScopeTreeParser\n", "from research.static_analysis.usage_analysis import ParsedFile, UsageIndex\n", "import collections\n", "from termcolor import colored\n", "import tree_sitter as ts\n", "from research.static_analysis.common import decode_bytes\n", "from research.static_analysis.parsing import (\n", "    ScopeOrSpan,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    SrcScope,\n", "    <PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "colors = [\"red\", \"green\", \"blue\", \"light_yellow\", \"cyan\"]\n", "color_index = 0\n", "\n", "\n", "def get_color():\n", "    global color_index\n", "    color = colors[color_index % len(colors)]\n", "    color_index += 1\n", "    return color\n", "\n", "\n", "def customized_find(root_node: ts.Node):\n", "    target_nodes = []\n", "    unique_types = set()\n", "    queue: collections.deque[tuple[ts.Node, int]] = collections.deque([(root_node, 0)])\n", "    while queue:\n", "        (node, depth) = queue.popleft()\n", "        target_nodes.append((node, depth))\n", "        unique_types.add(node.type)\n", "        for cur_node in node.children:\n", "            queue.append((cur_node, depth + 1))\n", "    unique_types = sorted(list(unique_types))\n", "    print(f\"unique types: {unique_types}\")\n", "    print(f\"Find {len(target_nodes)} nodes.\")\n", "    return target_nodes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "from file1 import double, Foo\n", "\n", "x = 1 + A(5).bar(4)\n", "double(x)\n", "\n", "y = {\"x\": A(7).bar(2), \"y\": double(2)}\n", "\n", "class NewModel(AbstractModel):\n", "    '''xxxxx.'''\n", "\n", "if True:\n", "    # Hello World, this is\n", "    # xxx :-)\n", "\n", "def main(bar: A):\n", "    return double(bar.foo())\n", "\"\"\"\n", "# ex_file = r\"\"\"y = {\"x\": A(7).bar(2), \"y\": double(2)}\"\"\"\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for index, (node, depth) in enumerate(target_nodes):\n", "    breaker = \"-\" * 8\n", "    print(\n", "        breaker\n", "        + f\" : [{index:05d}] {node.type:16s} : depth={depth} : {node.start_byte} - {node.end_byte}\"\n", "    )\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "class NewModel(AbstractModel):\n", "\n", "    def __init__(self, checkpoint: str):\n", "        self.checkpoint = checkpoint\n", "    \n", "    def load(self):\n", "        self.model = torch.load(self.checkpoint)\n", "\"\"\"\n", "\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for node in target_nodes:\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    if node.type in (\"parameters\", \"argument_list\", \"call\"):\n", "        print(breaker * 4 + f\" :: parents :: {node.parent.type}\")\n", "        print(colored(decode_bytes(node.parent.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")\n", "        crange = doc.bmap.tsnode_to_crange(node.parent)\n", "        parsed_text = doc.code[crange.start : crange.stop]\n", "        print(colored(parsed_text, color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ex_file = \"\"\"\n", "class NewModel(AbstractModel):\n", "    '''xxxxx.'''\n", "\n", "    def __init__(self, checkpoint: str):\n", "        self.checkpoint = checkpoint\n", "    \n", "    def load(self):\n", "        self.model = torch.load(self.checkpoint)\n", "\"\"\"\n", "\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for node in target_nodes:\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    if node.type in (\"parameters\", \"argument_list\", \"call\"):\n", "        print(breaker * 4 + f\" :: parents :: {node.parent.type}\")\n", "        print(colored(decode_bytes(node.parent.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")\n", "        crange = doc.bmap.tsnode_to_crange(node.parent)\n", "        parsed_text = doc.code[crange.start : crange.stop]\n", "        print(colored(parsed_text, color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'ParsedFile' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 22\u001b[0m\n\u001b[1;32m      1\u001b[0m ex_file \u001b[39m=\u001b[39m \u001b[39m\"\"\"\u001b[39m\n\u001b[1;32m      2\u001b[0m \u001b[39mdef parse_cmd():\u001b[39m\n\u001b[1;32m      3\u001b[0m \u001b[39m    \u001b[39m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m\u001b[39mParse command line args.\u001b[39m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m\u001b[39m'\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[39m    return parser.parse_args()\u001b[39m\n\u001b[1;32m     20\u001b[0m \u001b[39m\"\"\"\u001b[39m\n\u001b[0;32m---> 22\u001b[0m doc \u001b[39m=\u001b[39m ParsedFile\u001b[39m.\u001b[39mparse(Path(\u001b[39m\"\u001b[39m\u001b[39mfile2.py\u001b[39m\u001b[39m\"\u001b[39m), \u001b[39m\"\u001b[39m\u001b[39mpython\u001b[39m\u001b[39m\"\u001b[39m, ex_file)\n\u001b[1;32m     24\u001b[0m color_index \u001b[39m=\u001b[39m \u001b[39m0\u001b[39m\n\u001b[1;32m     25\u001b[0m target_nodes \u001b[39m=\u001b[39m customized_find(doc\u001b[39m.\u001b[39mts_tree\u001b[39m.\u001b[39mroot_node)\n", "\u001b[0;31mNameError\u001b[0m: name 'ParsedFile' is not defined"]}], "source": ["ex_file = \"\"\"\n", "def parse_cmd():\n", "    '''Parse command line args.'''\n", "    parser = argparse.ArgumentParser()\n", "\n", "    # System configuration\n", "    parser.add_argument(\n", "        \"--image_name\",\n", "        type=str,\n", "        required=True,\n", "        help=\"The docker image name.\",\n", "    )\n", "    parser.add_argument(\n", "        \"--output_dir\",\n", "        type=str,\n", "        required=True,\n", "        help=\"The output directory.\",\n", "    )\n", "    return parser.parse_args()\n", "\"\"\"\n", "\n", "doc = ParsedFile.parse(Path(\"file2.py\"), \"python\", ex_file)\n", "\n", "color_index = 0\n", "target_nodes = customized_find(doc.ts_tree.root_node)\n", "\n", "# , types=(\"!=\", \"call\", \"def\", \"dictionary\")\n", "for node in target_nodes:\n", "    breaker = \"-\" * 8\n", "    print(breaker + f\" : {node.type:16s} : {node.start_byte} - {node.end_byte}\")\n", "    print(colored(decode_bytes(node.text), color=get_color()), end=\"\\n\")\n", "    print(breaker * 4)\n", "    if node.type in (\"parameters\", \"argument_list\", \"call\"):\n", "        print(breaker * 4 + f\" :: parents :: {node.parent.type}\")\n", "        print(colored(decode_bytes(node.parent.text), color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")\n", "        crange = doc.bmap.tsnode_to_crange(node.parent)\n", "        parsed_text = doc.code[crange.start : crange.stop]\n", "        print(colored(parsed_text, color=get_color()), end=\"\\n\")\n", "        print(breaker * 4 + \"--------------\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['path', 'lang', 'code', 'code_bytes', 'scope_tree', 'bmap', 'lmap', 'ts_node_dicts', 'image_name']\n", "['node.type', 'node.text', 'node.start_point', 'node.end_point', 'node.start_byte', 'node.end_byte', 'node.has_parent', 'node.text.token_len', 'node.char_range']\n"]}], "source": ["import torch\n", "from research.eval.patch_lib import Patch\n", "\n", "source_path = \"/home/<USER>/cache/data-patches/google_pyglove_v1.0/filter-stage-2-target-docs.torch\"\n", "doc_dicts = torch.load(source_path)\n", "doc = doc_dicts[0]\n", "nodes = doc[\"ts_node_dicts\"]\n", "print(list(doc.keys()))\n", "print(list(nodes[0].keys()))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:augment.research.eval.hydra.driver:There are 85 tagged images available on the CW docker registry.\n", "INFO:augment.research.eval.hydra.driver:Hydra driver pod name prefix: hydra-driver-6tz9zojp-local-pod\n", "INFO:augment.research.eval.hydra.driver:Using docker images lookup file: /home/<USER>/src/augment/research/eval/hydra/supported_repos.yaml.\n"]}], "source": ["from research.eval import hydra\n", "\n", "HYDRA_SOFT_TIMEOUT_SECS = 600\n", "HYDRA_HARD_TIMEOUT_SECS = 1800\n", "hydra_driver = hydra.Driver(\n", "    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,\n", "    hydra_block_resource_internet_access=True,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:SUCCESS: created ConfigMap hydra-driver-6tz9zojp-local-cfg-5jwyb7un.\n", "INFO:augment.research.eval.hydra.driver:image name: google/pyglove:v1.0\n", "INFO:root:SUCCESS: created Pod hydra-driver-6tz9zojp-local-pod-63i57sqj.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["google/pyglove/12345678                                                         \u001b[32mPASSED\u001b[0m     wall_time=20.12     test_time=20.12\n"]}, {"data": {"text/plain": ["{'config_map': 'hydra-driver-6tz9zojp-local-cfg-5jwyb7un',\n", " 'patch_json': '{\"file_content\": \"# Copyright 2019 The PyGlove Authors\\\\n#\\\\n# Licensed under the Apache License, Version 2.0 (the \\\\\"License\\\\\");\\\\n# you may not use this file except in compliance with the License.\\\\n# You may obtain a copy of the License at\\\\n#\\\\n#      http://www.apache.org/licenses/LICENSE-2.0\\\\n#\\\\n# Unless required by applicable law or agreed to in writing, software\\\\n# distributed under the License is distributed on an \\\\\"AS IS\\\\\" BASIS,\\\\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\\\\n# See the License for the specific language governing permissions and\\\\n# limitations under the License.\\\\n\\\\\"\\\\\"\\\\\"Class detour.\\\\\"\\\\\"\\\\\"\\\\n\\\\nimport contextlib\\\\nimport inspect\\\\nimport threading\\\\nimport types\\\\nfrom typing import Any, Dict, Sequence, Tuple, Type, Union\\\\n\\\\n\\\\<EMAIL>\\\\ndef detour(\\\\n    mappings: Sequence[Tuple[\\\\n        Type[Any],                               # Source class\\\\n        Union[Type[Any], types.FunctionType]     # Target class or function\\\\n    ]]):\\\\n  \\\\\"\\\\\"\\\\\"Context manager for detouring object creation.\\\\n\\\\n  At times, we want to replace an object of a class to an object of a different\\\\n  class. Usually, we do so by passing the object as a function argument using\\\\n  dependency injection. However, it\\'s not always possible to expose those\\\\n  internal objects as parameters to the class, as we cannot predict what needs\\\\n  to be customized in future. Also, exposing too many arguments will hurt\\\\n  usability, it\\'s big burden to figure out 20 arguments of a function for a user\\\\n  to get started.\\\\n\\\\n  `pg.detour` provides another option for object replacement in Python, which\\\\n  creates a context in which some source classes can be detoured to specified\\\\n  destination classes or functions. For example, the code snippet below will\\\\n  detour instantation of class A to class B, and vice-versa::\\\\n\\\\n    class A:\\\\n      pass\\\\n\\\\n    class B:\\\\n      pass\\\\n\\\\n    # Exchange class A and class B.\\\\n    with pg.detour([(A, B), (B, A)]):\\\\n      a = A()   # a is a B object.\\\\n      b = B()   # b is an A object.\\\\n\\\\n  Detour destination can be a function, which allows users to intercept the\\\\n  arguments passed to the class constructor. For example::\\\\n\\\\n    class Foo:\\\\n      def __init__(self, value):\\\\n        self.value = value\\\\n\\\\n    class Bar:\\\\n      def __init__(self, value):\\\\n        self.value = value\\\\n\\\\n    def detoured_foo(cls, value):\\\\n      # cls is the original class before detour.\\\\n      return Bar(value + 1)\\\\n\\\\n    with pg.detour([(Foo, detoured_foo)]):\\\\n      f = Foo(1)   # f will be Bar(2).\\\\n\\\\n  Detour can be nested. The outer scope mappings take precedence over the\\\\n  mappings from the inner loop, allowing users to change object creation\\\\n  behaviors from the outside. For example, the following code will detour\\\\n  class A to class C::\\\\n\\\\n    with pg.detour([(A, C)]):\\\\n      with pg.detour([A, B]):\\\\n        a = A()   # a is a C object.\\\\n\\\\n  Detour is transisive across the inner and outer scope. For example, the code\\\\n  below will detour class A to class C through B::\\\\n\\\\n    with pg.detour([(B, C)]):\\\\n      a1 = A()     # a1 is an A object.\\\\n      with pg.detour([A, B]):\\\\n        a2 = A()    # a2 is a C object. (A -> B -> C)\\\\n\\\\n  Detour is thread-sfe.\\\\n\\\\n  Args:\\\\n    mappings: A sequence of tuple (src_cls, dest_cls_or_fn) as mappings for the\\\\n      detour - \\'src_cls\\' is the source class to be detoured, while\\\\n      \\'dest_cls_or_fn\\' is the destination class or function. When it\\'s a class,\\\\n      its `__init__` method should have the same signature as the `__init__` of\\\\n      the original class. When it\\'s a function, it should accept a positional\\\\n      argument `cls`, for passing the original class that is being detoured,\\\\n      followed by all the arguments that the original class should accept. For\\\\n      example, a class with `__init__(self, x, *args, y, **kwargs)` can be\\\\n      detoured to a function with signature `(cls, x, *args, y, **kwargs)`.\\\\n\\\\n  Yields:\\\\n    Resolved detour mappings.\\\\n\\\\n  Raises:\\\\n    TypeError: If the first item in each mapping is not a class, or the second\\\\n      item in each mapping is neither a class nor a function.\\\\n  \\\\\"\\\\\"\\\\\"\\\\n    # Placeholder for Google-internal usage instrumentation.\\\\n\\\\n  for src, dest in mappings:\\\\n    if not inspect.isclass(src):\\\\n      raise TypeError(f\\'Detour source {src!r} is not a class.\\')\\\\n    if not inspect.isclass(dest) and not inspect.isfunction(dest):\\\\n      raise TypeError(\\\\n          f\\'Detour destination {dest!r} is not a class or a function.\\')\\\\n\\\\n  try:\\\\n    yield _global_detour_context.enter_scope(mappings)\\\\n  finally:\\\\n    _global_detour_context.leave_scope()\\\\n\\\\n\\\\ndef current_mappings() -> Dict[Type[Any], Union[Type[Any], types.FunctionType]]:\\\\n  \\\\\"\\\\\"\\\\\"Returns detour mappings under current scope.\\\\\"\\\\\"\\\\\"\\\\n  return _global_detour_context.current_mappings\\\\n\\\\n\\\\ndef undetoured_new(cls, *args, **kwargs) -> Any:\\\\n  \\\\\"\\\\\"\\\\\"Create a new instance of cls without detouring.\\\\n\\\\n  If cls.__init__ creates sub-objects, creation of sub-objects\\\\n  maybe detoured based on current context. For example::\\\\n\\\\n    class A:\\\\n\\\\n      def __init__(self, x):\\\\n        if x < 0:\\\\n          self.child = A(x)\\\\n        else:\\\\n          self.x = x\\\\n\\\\n    with pg.detour([A, B]):\\\\n      a = A(-1)\\\\n      assert isinstance(a, A)\\\\n      assert isinstance(a.child, B)\\\\n\\\\n  Args:\\\\n    cls: The class whose instance will be created.\\\\n    *args: Positional arguments to be passed to class __init__ method.\\\\n    **kwargs: Keyword arguments to be passed to class __init__ method.\\\\n\\\\n  Returns:\\\\n    A instance of `cls`.\\\\n  \\\\\"\\\\\"\\\\\"\\\\n  new_method = _global_detour_context.get_original_new(cls)\\\\n  if new_method is object.__new__:\\\\n    instance = new_method(cls)\\\\n  else:\\\\n    instance = new_method(cls, *args, **kwargs)\\\\n  instance.__init__(*args, **kwargs)\\\\n  return instance\\\\n\\\\n\\\\nclass _DetourContext:\\\\n  \\\\\"\\\\\"\\\\\"Context that sets/gets detoured class mappings under current thread.\\\\\"\\\\\"\\\\\"\\\\n\\\\n  _DETOUR_STACK_KEY = \\'detour_stack\\'\\\\n  _DETOUR_MAPPING_KEY = \\'detour_map\\'\\\\n  _NEW_CALL_STACK = \\'new_stack\\'\\\\n\\\\n  def __init__(self):\\\\n    self._tls = threading.local()\\\\n    self._original_new = dict()\\\\n\\\\n  @property\\\\n  def _detour_stack(self):\\\\n    detour_stack = getattr(self._tls, self._DETOUR_STACK_KEY, None)\\\\n    if detour_stack is None:\\\\n      detour_stack = []\\\\n      setattr(self._tls, self._DETOUR_STACK_KEY, detour_stack)\\\\n    return detour_stack\\\\n\\\\n  @property\\\\n  def current_mappings(\\\\n      self) -> Dict[Type[Any], Union[Type[Any], types.FunctionType]]:\\\\n    if self._detour_stack:\\\\n      return self._detour_stack[-1]\\\\n    return dict()\\\\n\\\\n  def enter_scope(\\\\n      self,\\\\n      mappings: Sequence[Tuple[\\\\n          Type[Any],                            # Source class\\\\n          Union[Type[Any], types.FunctionType]  # Target class or function\\\\n      ]]) -> Dict[Type[Any], Union[Type[Any], types.FunctionType]]:\\\\n    \\\\\"\\\\\"\\\\\"Enter a new scope.\\\\\"\\\\\"\\\\\"\\\\n    # Create a copy of current mapping so we can modify.\\\\n    cur_mappings = dict(self.current_mappings)\\\\n\\\\n    # Compute transisive mappings between current scope and new scope.\\\\n    # 1) If a source cls exists in current mappings, use the existing\\\\n    #    destination. E.g:\\\\n    #    ```\\\\n    #    with pg.detour([(A, B)]):\\\\n    #      with pg.detour([(A, C]]):\\\\n    #        A()   # should produce B.\\\\n    #\\\\n    # 2) If a destination cls exists in current mapping, and source class does\\\\n    #    not exist in current mappings, inserts a mapping from the source class\\\\n    #    to the destination of the target class in current mapping. E.g:\\\\n    #    ```\\\\n    #    with pg.detour([(B, C)]):\\\\n    #      with pg.detour([(A, B)]):\\\\n    #        A()   # should produce C.\\\\n    #\\\\n    # 3) Otherwise insert the new mapping. E.g:\\\\n    #    ```\\\\n    #    with pg.detour([(A, B)]):\\\\n    #      with pg.detour([(C, D)]):\\\\n    #        C()   # should produce D.\\\\n    new_mappings = []\\\\n    for src, dest in mappings:\\\\n      if src not in cur_mappings:\\\\n        if dest in cur_mappings:\\\\n          new_mappings.append((src, cur_mappings[dest]))\\\\n        else:\\\\n          new_mappings.append((src, dest))\\\\n\\\\n    for src, dest in new_mappings:\\\\n      if src not in self._original_new:\\\\n        self._original_new[src] = src.__new__\\\\n        setattr(src, \\'__new__\\', _maybe_detoured_new)\\\\n      cur_mappings[src] = dest\\\\n    self._detour_stack.append(cur_mappings)\\\\n    return cur_mappings\\\\n\\\\n  def leave_scope(self):\\\\n    \\\\\"\\\\\"\\\\\"Leave current detour scope.\\\\\"\\\\\"\\\\\"\\\\n    assert self._detour_stack\\\\n    self._detour_stack.pop(-1)\\\\n\\\\n  def get_destination(self, src_cls):\\\\n    return self.current_mappings.get(src_cls, None)\\\\n\\\\n  def get_original_new(self, src_cls):\\\\n    \\\\\"\\\\\"\\\\\"Returns the original new method of source cls.\\\\\"\\\\\"\\\\\"\\\\n    if not _is_detoured_new(src_cls.__new__):\\\\n      orig_new = src_cls.__new__\\\\n    else:\\\\n      # NOTE(daiyip): there are usually 3 patterns in implementing __new__.\\\\n      # 1) call super.__new__ to return an instance.\\\\n      # 2) explicitly call object.__new__ to return an instance.\\\\n      # 3) return an instance from another class.\\\\n      #\\\\n      # The following code aims to support case #1 by mimicing the call\\\\n      # convention of super.__new__ without access to the super object.\\\\n      # We implement this by maintaining a call history of `__new__` method\\\\n      # returned by `get_original_new` for each top-most call to\\\\n      # `_maybe_detour_new`. Based on the history, we always return the next\\\\n      # __new__ along the inheritance hierarchy. For example, for code:\\\\n      #\\\\n      # ```\\\\n      #   class A:\\\\n      #     def __new__(cls, *args, **kwargs):\\\\n      #       return super(A, cls).__new__(cls, *args, **kwargs)\\\\n      #\\\\n      #   class B:\\\\n      #     def __new__(cls, *args, **kwargs):\\\\n      #       return super(A, cls).__new__(cls, *args, **kwargs)\\\\n      #\\\\n      #   class C(A, B):\\\\n      #     pass\\\\n      # ```\\\\n      # when we detour A and B to other classes, their `__new__` method will be\\\\n      # replaced with `_maybe_detoured_new`. As we create an object of C, it\\\\n      # will call `C.__new__`, which inherits the `_maybe_detoured_new` assigned\\\\n      # to `A.__new__`. `_maybe_detoured_new` calls `get_original_new` on class\\\\n      # C, which should return the original `A.__new__`. It then executes\\\\n      # `super(A, cls).__new__`, which triggers `_maybe_detoured_new` method\\\\n      # again assigned to `B.__new__`. In such case, we cannot differentiate the\\\\n      # first call to `_maybe_detoured_new` (C.__new__) from this call, since\\\\n      # both take class C as the cls argument. However, by assuming that nested\\\\n      # `_maybe_detoured_new` call should always reflect the `super.__new__`\\\\n      # call convention, we can store the call history for these invoked __new__\\\\n      # methods, and return the one that is one-step closer to `object.__new__`.\\\\n      # This may not work for the most complicated __new__ customization, but\\\\n      # should work well for most __new__ implementations.\\\\n      orig_new = self._original_new.get(src_cls, object.__new__)\\\\n      if orig_new is object.__new__ or orig_new in self._new_stack:\\\\n        for base in src_cls.__bases__:\\\\n          base_new = self.get_original_new(base)\\\\n          if base_new is not object.__new__ and base_new not in self._new_stack:\\\\n            orig_new = base_new\\\\n            break\\\\n    return orig_new\\\\n\\\\n  @property\\\\n  def _new_stack(self):\\\\n    \\\\\"\\\\\"\\\\\"Returns the stack of new methods in current thread.\\\\\"\\\\\"\\\\\"\\\\n    stack = getattr(self._tls, self._NEW_CALL_STACK, None)\\\\n    if stack is None:\\\\n      stack = []\\\\n      setattr(self._tls, self._NEW_CALL_STACK, stack)\\\\n    return stack\\\\n\\\\n  def call_new(self, new_method, cls, *args, **kwargs):\\\\n    \\\\\"\\\\\"\\\\\"Call __new__ method with correctly handling super.__new__.\\\\\"\\\\\"\\\\\"\\\\n    try:\\\\n      self._new_stack.append(new_method)\\\\n      if new_method is object.__new__:\\\\n        return object.__new__(cls)\\\\n      else:\\\\n        return new_method(cls, *args, **kwargs)\\\\n    finally:\\\\n      self._new_stack.pop(-1)\\\\n\\\\n# Global detour context.\\\\n_global_detour_context = _DetourContext()\\\\n\\\\n\\\\n@staticmethod   # This decorator is required for Python2.\\\\ndef _maybe_detoured_new(cls, *args, **kwargs):\\\\n  \\\\\"\\\\\"\\\\\"A __new__ method to replace user class\\' __new__ for detour.\\\\\"\\\\\"\\\\\"\\\\n  dest_cls_or_fn = _global_detour_context.get_destination(cls)\\\\n  if dest_cls_or_fn is None:\\\\n    # No detour in current thread.\\\\n    return _global_detour_context.call_new(\\\\n        _global_detour_context.get_original_new(cls),\\\\n        cls, *args, **kwargs)\\\\n\\\\n  if inspect.isclass(dest_cls_or_fn):\\\\n    dest_cls = dest_cls_or_fn\\\\n    instance = _global_detour_context.call_new(\\\\n        _global_detour_context.get_original_new(dest_cls),\\\\n        dest_cls, *args, **kwargs)\\\\n\\\\n    # NOTE(daiyip): when an overridden `__new__` returns an instance whose\\\\n    # class is not strictly the user class, `__init__` will not be called\\\\n    # by Python runtime. We can detect such case and invoke `__init__`\\\\n    # manually.\\\\n    if not isinstance(instance, cls):\\\\n      instance.__init__(*args, **kwargs)\\\\n    return instance\\\\n  else:\\\\n    # NOTE(daiyip): when function is used as detour destination, we handle it\\\\n    # specially to allow instances of the source class to be created in the\\\\n    # function. E.g.\\\\n    #\\\\n    # ```\\\\n    # def create_foo(value):\\\\n    #   return Foo(value + 1)\\\\n    #\\\\n    # with pg.detour([(Foo, create_foo)]):\\\\n    #    Foo(1)\\\\n    # ```\\\\n    try:\\\\n      _global_detour_context.current_mappings[cls] = cls\\\\n      return dest_cls_or_fn(cls, *args, **kwargs)\\\\n    finally:\\\\n      _global_detour_context.current_mappings[cls] = dest_cls_or_fn\\\\n\\\\n\\\\ndef _is_detoured_new(method):\\\\n  \\\\\"\\\\\"\\\\\"Returns if a method is detoured new.\\\\\"\\\\\"\\\\\"\\\\n  return method is getattr(_maybe_detoured_new, \\'__func__\\')\\\\n\", \"char_start\": 11974, \"char_end\": 12097, \"patch_content\": \"return _global_detour_context.call_new(\\\\n        _global_detour_context.get_original_new(cls),\\\\n        cls, *args, **kwargs)\", \"patch_id\": \"google/pyglove/12345678\", \"repository\": \"\", \"commit_sha\": \"870ed11798703e56e9c9523af5b52bd92ec320a4\", \"file_name\": \"pyglove/core/detouring/class_detour.py\", \"_extra\": {}}',\n", " 'result_str': 'google/pyglove/12345678                                                         \\x1b[32mPASSED\\x1b[0m     wall_time=20.12     test_time=20.12',\n", " 'run_output': 'total 0\\ndrwxrwxrwx 3 <USER> <GROUP> 101 Sep  1 03:09 .\\ndrwxr-xr-x 1 <USER> <GROUP>  46 Sep  1 03:09 ..\\ndrwxr-xr-x 2 <USER> <GROUP>  48 Sep  1 03:09 ..2023_09_01_03_09_26.458826744\\nlrwxrwxrwx 1 root root  31 Sep  1 03:09 ..data -> ..2023_09_01_03_09_26.458826744\\nlrwxrwxrwx 1 root root  17 Sep  1 03:09 patch.json -> ..data/patch.json\\nlrwxrwxrwx 1 root root  23 Sep  1 03:09 patch_and_run.py -> ..data/patch_and_run.py\\nrepo_name=google\\nfile_name=pyglove/core/detouring/class_detour.py\\nRunning test harness...\\n#!/bin/bash\\nset -e\\n\\npytest -n auto\\n============================= test session starts ==============================\\nplatform linux -- Python 3.10.12, pytest-7.4.0, pluggy-1.3.0\\nrootdir: /code\\nplugins: cov-4.1.0, xdist-3.3.1\\ncreated: 16/16 workers\\n16 workers [1347 items]\\n\\n........................................................................ [  5%]\\n........................................................................ [ 10%]\\n........................................................................ [ 16%]\\n........................................................................ [ 21%]\\n........................................................................ [ 26%]\\n........................................................................ [ 32%]\\n........................................................................ [ 37%]\\n........................................................................ [ 42%]\\n........................................................................ [ 48%]\\n...........................................s............................ [ 53%]\\n........................................................................ [ 58%]\\n........................................................................ [ 64%]\\n......................................................................... [ 69%]\\n........................................................................ [ 74%]\\n........................................................................ [ 80%]\\n........................................................................ [ 85%]\\n......................................................................... [ 90%]\\n........................................................................ [ 96%]\\n.................................................                        [100%]\\n======================= 1346 passed, 1 skipped in 19.76s =======================\\nStarting now...\\nRunning command: cat .hydra/run.sh; .hydra/run.sh\\ngoogle/pyglove/12345678                                                         \\x1b[32mPASSED\\x1b[0m     wall_time=20.12     test_time=20.12     \\n'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.eval.patch_lib import Patch\n", "from research.static_analysis.common import decode_bytes\n", "\n", "patch = Patch(\n", "    char_start=nodes[0][\"node.char_range\"].start,\n", "    char_end=nodes[0][\"node.char_range\"].stop,\n", "    file_content=doc[\"code\"],\n", "    file_name=doc[\"scope_tree\"].name,\n", "    patch_content=decode_bytes(nodes[0][\"node.text\"]),\n", "    commit_sha=\"870ed11798703e56e9c9523af5b52bd92ec320a4\",\n", "    patch_id=\"google/pyglove/12345678\",\n", ")\n", "results = hydra_driver.dispatch(patch, image_name=doc[\"image_name\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["google/pyglove:v1.0\n"]}], "source": ["print(doc[\"image_name\"])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["for i in range(10): print(i)                                 vs. foriinrange10printi vs. 4cedb61722092fff5965a85e5f861223\n", "for i in range(10):print(i)                                  vs. foriinrange10printi vs. 4cedb61722092fff5965a85e5f861223\n", "for i in range( 10 ) : print ( i )                           vs. foriinrange10printi vs. 4cedb61722092fff5965a85e5f861223\n", "# This is a comment\n", "for i in range(10): print(i)             vs. foriinrange10printi vs. 4cedb61722092fff5965a85e5f861223\n", "for j in range(10): print(j)                                 vs. forjinrange10printj vs. e1f31ab855f64727eb99b0150ed6ad35\n"]}], "source": ["import re\n", "import hashlib\n", "\n", "def normalize_code(code: str):\n", "    # Convert to lowercase\n", "    code = code.lower()\n", "    # Remove comments (assuming comments start with #)\n", "    lines = [line.split('#')[0].strip() for line in code.split('\\n')]\n", "    # Remove the empty lines and extra whitespaces\n", "    lines = [' '.join(x.split()) for x in lines if x]\n", "    # Only keep a-z 0-9\n", "    code = \"\".join(lines)\n", "    return re.sub(r'[^a-z0-9]', '', code.lower())\n", "\n", "def get_md5(text):\n", "    return hashlib.md5(text.encode()).hexdigest()\n", "\n", "# Your code snippets\n", "snippets = [\n", "    \"for i in range(10): print(i)\",  # Original\n", "    \"for i in range(10):print(i)\",  # No space\n", "    \"for i in range( 10 ) : print ( i )\",  # Extra spaces\n", "    \"# This is a comment\\nfor i in range(10): print(i)\",  # With comment\n", "    \"for j in range(10): print(j)\",  # Different variable\n", "]\n", "\n", "for code in snippets:\n", "    print(f\"{code:60s} vs. {normalize_code(code)} vs. {get_md5(normalize_code(code))}\")\n", "\n", "# # Normalize and hash the code snippets\n", "# hashed_snippets = {get_md5(normalize_code(snippet)) for snippet in snippets}\n", "\n", "# # If length is less than the original, duplicates were removed\n", "# if len(hashed_snippets) < len(snippets):\n", "#     print(f\"Found {len(snippets) - len(hashed_snippets)} duplicates.\")\n", "\n", "# # Display unique snippets\n", "# unique_snippets = set()\n", "# for snippet in snippets:\n", "#     hash_value = get_md5(normalize_code(snippet))\n", "#     if hash_value in hashed_snippets:\n", "#         unique_snippets.add(snippet)\n", "#         hashed_snippets.remove(hash_value)\n", "\n", "# print(\"Unique Snippets:\")\n", "# for snippet in unique_snippets:\n", "#     print(snippet)\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The code snippets are structurally similar.\n", "\n", "\n"]}, {"data": {"text/plain": ["{'for': [{'Name': [{'Store': []}]},\n", "  {'range': [{'Name': [{'Load': []}]}, [{'Constant': []}], []]},\n", "  [{'Expr': [{'print': [{'Name': [{'Load': []}]},\n", "       [{'Name': [{'Load': []}]}],\n", "       []]}]}],\n", "  []]}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["import ast\n", "\n", "def simplify_tree(tree):\n", "    \"\"\"Simplify an AST by keeping only the type of each node, while maintaining specific keywords and function names.\"\"\"\n", "    if isinstance(tree, ast.AST):\n", "        node_type = type(tree).__name__\n", "\n", "        # Special handling for function and keyword names\n", "        if isinstance(tree, ast.Call) and hasattr(tree.func, 'id'):\n", "            node_type = tree.func.id\n", "        elif isinstance(tree, ast.keyword):\n", "            node_type = tree.arg\n", "        elif isinstance(tree, ast.For):\n", "            node_type = 'for'\n", "\n", "        return {node_type: [simplify_tree(child) for field, child in ast.iter_fields(tree) if isinstance(child, (ast.AST, list))]}\n", "    elif isinstance(tree, list):\n", "        return [simplify_tree(child) for child in tree]\n", "    else:\n", "        return None\n", "\n", "def ast_similar(tree1, tree2):\n", "    return simplify_tree(tree1) == simplify_tree(tree2)\n", "\n", "# Parse the code snippets into ASTs\n", "tree1 = ast.parse(\"for i in range(10): print(i)\").body[0]\n", "tree2 = ast.parse(\"for j in range(10): print(j)\").body[0]\n", "\n", "# Compare the simplified ASTs\n", "if ast_similar(tree1, tree2):\n", "    print(\"The code snippets are structurally similar.\")\n", "else:\n", "    print(\"The code snippets are different.\")\n", "\n", "print(\"\\n\")\n", "simplify_tree(tree1)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n"]}], "source": ["import abc\n", "import functools\n", "\n", "class A:\n", "\n", "    @functools.cache\n", "    @abc.abstractmethod\n", "    def tokenizer(self) -> int:\n", "        \"\"\".\"\"\"\n", "\n", "class B(A):\n", "\n", "    @functools.cache\n", "    def tokenizer(self) -> int:\n", "        return 1\n", "\n", "x = B()\n", "print(x.tokenizer())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}