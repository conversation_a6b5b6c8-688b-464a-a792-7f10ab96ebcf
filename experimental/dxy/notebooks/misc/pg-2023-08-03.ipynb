{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pyglove as pg"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "`pg.Object.__init__` is a PyGlove managed method. For setting up the class initialization logic, please override `_on_bound()` or `_on_init()`. If you do have a need to override `__init__` and know the implications, please decorate your overridden method with `@pg.explicit_method_override`.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[39m# The pg.Object now explicitly throw errors if the user override __init__\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m \u001b[39mclass\u001b[39;00m \u001b[39mA\u001b[39;00m(pg\u001b[39m.\u001b[39mObject):\n\u001b[1;32m      5\u001b[0m     \u001b[39mdef\u001b[39;00m \u001b[39m__init__\u001b[39m(\u001b[39mself\u001b[39m):\n\u001b[1;32m      6\u001b[0m         \u001b[39mpass\u001b[39;00m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/abc.py:106\u001b[0m, in \u001b[0;36mABCMeta.__new__\u001b[0;34m(mcls, name, bases, namespace, **kwargs)\u001b[0m\n\u001b[1;32m    105\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m__new__\u001b[39m(mcls, name, bases, namespace, \u001b[39m*\u001b[39m\u001b[39m*\u001b[39mkwargs):\n\u001b[0;32m--> 106\u001b[0m     \u001b[39mcls\u001b[39m \u001b[39m=\u001b[39m \u001b[39msuper\u001b[39;49m()\u001b[39m.\u001b[39;49m\u001b[39m__new__\u001b[39;49m(mcls, name, bases, namespace, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwargs)\n\u001b[1;32m    107\u001b[0m     _abc_init(\u001b[39mcls\u001b[39m)\n\u001b[1;32m    108\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mcls\u001b[39m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyglove/core/symbolic/object.py:277\u001b[0m, in \u001b[0;36mObject.__init_subclass__\u001b[0;34m(cls, user_cls)\u001b[0m\n\u001b[1;32m    259\u001b[0m \u001b[39m@classmethod\u001b[39m\n\u001b[1;32m    260\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m__init_subclass__\u001b[39m(\u001b[39mcls\u001b[39m, user_cls\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m):\n\u001b[1;32m    261\u001b[0m \u001b[39m  \u001b[39m\u001b[39m\"\"\"Initializes subclass.\u001b[39;00m\n\u001b[1;32m    262\u001b[0m \n\u001b[1;32m    263\u001b[0m \u001b[39m  `pg.Object` allows child classes to explicit call\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    275\u001b[0m \u001b[39m    user_cls: The source class that calls this class method.\u001b[39;00m\n\u001b[1;32m    276\u001b[0m \u001b[39m  \"\"\"\u001b[39;00m\n\u001b[0;32m--> 277\u001b[0m   object_utils\u001b[39m.\u001b[39;49mensure_explicit_method_override(\n\u001b[1;32m    278\u001b[0m       \u001b[39mcls\u001b[39;49m\u001b[39m.\u001b[39;49m\u001b[39m__init__\u001b[39;49m,\n\u001b[1;32m    279\u001b[0m       (\n\u001b[1;32m    280\u001b[0m           \u001b[39m'\u001b[39;49m\u001b[39m`pg.Object.__init__` is a PyGlove managed method. For setting up \u001b[39;49m\u001b[39m'\u001b[39;49m\n\u001b[1;32m    281\u001b[0m           \u001b[39m'\u001b[39;49m\u001b[39mthe class initialization logic, please override `_on_bound()` or \u001b[39;49m\u001b[39m'\u001b[39;49m\n\u001b[1;32m    282\u001b[0m           \u001b[39m'\u001b[39;49m\u001b[39m`_on_init()`. If you do have a need to override `__init__` and \u001b[39;49m\u001b[39m'\u001b[39;49m\n\u001b[1;32m    283\u001b[0m           \u001b[39m'\u001b[39;49m\u001b[39mknow the implications, please decorate your overridden method \u001b[39;49m\u001b[39m'\u001b[39;49m\n\u001b[1;32m    284\u001b[0m           \u001b[39m'\u001b[39;49m\u001b[39mwith `@pg.explicit_method_override`.\u001b[39;49m\u001b[39m'\u001b[39;49m\n\u001b[1;32m    285\u001b[0m       ))\n\u001b[1;32m    286\u001b[0m   \u001b[39msuper\u001b[39m()\u001b[39m.\u001b[39m__init_subclass__()\n\u001b[1;32m    288\u001b[0m   user_cls \u001b[39m=\u001b[39m user_cls \u001b[39mor\u001b[39;00m \u001b[39mcls\u001b[39m\n", "File \u001b[0;32m/opt/conda/lib/python3.9/site-packages/pyglove/core/object_utils/common_traits.py:448\u001b[0m, in \u001b[0;36mensure_explicit_method_override\u001b[0;34m(method, error_message)\u001b[0m\n\u001b[1;32m    444\u001b[0m \u001b[39mif\u001b[39;00m error_message \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[1;32m    445\u001b[0m   error_message \u001b[39m=\u001b[39m (\n\u001b[1;32m    446\u001b[0m       \u001b[39mf\u001b[39m\u001b[39m'\u001b[39m\u001b[39m{\u001b[39;00mmethod\u001b[39m}\u001b[39;00m\u001b[39m is a PyGlove managed method. If you do need to override \u001b[39m\u001b[39m'\u001b[39m\n\u001b[1;32m    447\u001b[0m       \u001b[39m'\u001b[39m\u001b[39mit, please decorate the method with `@pg.explicit_method_override`.\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m--> 448\u001b[0m \u001b[39mraise\u001b[39;00m \u001b[39mTypeError\u001b[39;00m(error_message)\n", "\u001b[0;31mTypeError\u001b[0m: `pg.Object.__init__` is a PyGlove managed method. For setting up the class initialization logic, please override `_on_bound()` or `_on_init()`. If you do have a need to override `__init__` and know the implications, please decorate your overridden method with `@pg.explicit_method_override`."]}], "source": ["# The pg.Object now explicitly throw errors if the user override __init__\n", "\n", "class A(pg.Object):\n", "\n", "    def __init__(self):\n", "        pass"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}