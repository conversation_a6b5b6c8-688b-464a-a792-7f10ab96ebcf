{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\n", "from research.core.all_prompt_formatters import get_prompt_formatter"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LLAMAPromptFormatter(preamble = ,\n", "  tokenizer = <megatron.tokenizer.tokenizer.Llama2Tokenizer object at 0x7f9f274c7550>,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  bos_id = None)\n"]}], "source": ["formatter = get_prompt_formatter(\"llama\")\n", "print(formatter)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LLAMAPromptFormatter(preamble = ,\n", "  tokenizer = <megatron.tokenizer.tokenizer.Llama2Tokenizer object at 0x7f9f2c80c160>,\n", "  _max_prompt_tokens = 9223372036854775807,\n", "  bos_id = None)\n"]}], "source": ["formatter = get_prompt_formatter(\"llama2\")\n", "print(formatter)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}