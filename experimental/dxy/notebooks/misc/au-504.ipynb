{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import os\n", "os.environ['CUDA_VISIBLE_DEVICES'] = \"0\"\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "from research.core.model_input import ModelInput\n", "from research.models.all_models import get_model, GenerationOptions\n", "from research.core.all_prompt_formatters import get_prompt_formatter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = get_model(\"starcoderbase_1b\")\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs = ModelInput(prefix=\"def hello world():\", suffix=\"\\n\")\n", "outputs = model.generate(inputs, options=GenerationOptions(max_generated_tokens=16))\n", "print(outputs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.prompt_formatter.tokenizer = get_prompt_formatter(\"basic\").tokenizer\n", "inputs = ModelInput(prefix=\"def hello world():\", suffix=\"\\n\")\n", "outputs = model.generate(inputs, options=GenerationOptions(max_generated_tokens=16))\n", "print(outputs)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}