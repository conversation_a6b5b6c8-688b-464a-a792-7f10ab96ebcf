{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Object of type A is not JSON serializable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[2], line 15\u001b[0m\n\u001b[1;32m     12\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39ma \u001b[39m=\u001b[39m x\n\u001b[1;32m     14\u001b[0m a \u001b[39m=\u001b[39m A(\u001b[39mNone\u001b[39;00m)\n\u001b[0;32m---> 15\u001b[0m json_str \u001b[39m=\u001b[39m json\u001b[39m.\u001b[39;49mdumps(a)\n\u001b[1;32m     16\u001b[0m \u001b[39mprint\u001b[39m(json)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/__init__.py:231\u001b[0m, in \u001b[0;36mdumps\u001b[0;34m(obj, skipkeys, ensure_ascii, check_circular, allow_nan, cls, indent, separators, default, sort_keys, **kw)\u001b[0m\n\u001b[1;32m    226\u001b[0m \u001b[39m# cached encoder\u001b[39;00m\n\u001b[1;32m    227\u001b[0m \u001b[39mif\u001b[39;00m (\u001b[39mnot\u001b[39;00m skipkeys \u001b[39mand\u001b[39;00m ensure_ascii \u001b[39mand\u001b[39;00m\n\u001b[1;32m    228\u001b[0m     check_circular \u001b[39mand\u001b[39;00m allow_nan \u001b[39mand\u001b[39;00m\n\u001b[1;32m    229\u001b[0m     \u001b[39mcls\u001b[39m \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m indent \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m separators \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m\n\u001b[1;32m    230\u001b[0m     default \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m \u001b[39mand\u001b[39;00m \u001b[39mnot\u001b[39;00m sort_keys \u001b[39mand\u001b[39;00m \u001b[39mnot\u001b[39;00m kw):\n\u001b[0;32m--> 231\u001b[0m     \u001b[39mreturn\u001b[39;00m _default_encoder\u001b[39m.\u001b[39;49mencode(obj)\n\u001b[1;32m    232\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mcls\u001b[39m \u001b[39mis\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n\u001b[1;32m    233\u001b[0m     \u001b[39mcls\u001b[39m \u001b[39m=\u001b[39m JSONEncoder\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/encoder.py:199\u001b[0m, in \u001b[0;36mJSONEncoder.encode\u001b[0;34m(self, o)\u001b[0m\n\u001b[1;32m    195\u001b[0m         \u001b[39mreturn\u001b[39;00m encode_basestring(o)\n\u001b[1;32m    196\u001b[0m \u001b[39m# This doesn't pass the iterator directly to ''.join() because the\u001b[39;00m\n\u001b[1;32m    197\u001b[0m \u001b[39m# exceptions aren't as detailed.  The list call should be roughly\u001b[39;00m\n\u001b[1;32m    198\u001b[0m \u001b[39m# equivalent to the PySequence_Fast that ''.join() would do.\u001b[39;00m\n\u001b[0;32m--> 199\u001b[0m chunks \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49miterencode(o, _one_shot\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m)\n\u001b[1;32m    200\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39misinstance\u001b[39m(chunks, (\u001b[39mlist\u001b[39m, \u001b[39mtuple\u001b[39m)):\n\u001b[1;32m    201\u001b[0m     chunks \u001b[39m=\u001b[39m \u001b[39mlist\u001b[39m(chunks)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/encoder.py:257\u001b[0m, in \u001b[0;36mJSONEncoder.iterencode\u001b[0;34m(self, o, _one_shot)\u001b[0m\n\u001b[1;32m    252\u001b[0m \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    253\u001b[0m     _iterencode \u001b[39m=\u001b[39m _make_iterencode(\n\u001b[1;32m    254\u001b[0m         markers, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mdefault, _encoder, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mindent, floatstr,\n\u001b[1;32m    255\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mkey_separator, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mitem_separator, \u001b[39mself\u001b[39m\u001b[39m.\u001b[39msort_keys,\n\u001b[1;32m    256\u001b[0m         \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mskipkeys, _one_shot)\n\u001b[0;32m--> 257\u001b[0m \u001b[39mreturn\u001b[39;00m _iterencode(o, \u001b[39m0\u001b[39;49m)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/json/encoder.py:179\u001b[0m, in \u001b[0;36mJSONEncoder.default\u001b[0;34m(self, o)\u001b[0m\n\u001b[1;32m    160\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mdefault\u001b[39m(\u001b[39mself\u001b[39m, o):\n\u001b[1;32m    161\u001b[0m \u001b[39m    \u001b[39m\u001b[39m\"\"\"Implement this method in a subclass such that it returns\u001b[39;00m\n\u001b[1;32m    162\u001b[0m \u001b[39m    a serializable object for ``o``, or calls the base implementation\u001b[39;00m\n\u001b[1;32m    163\u001b[0m \u001b[39m    (to raise a ``TypeError``).\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    177\u001b[0m \n\u001b[1;32m    178\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 179\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mTypeError\u001b[39;00m(\u001b[39mf\u001b[39m\u001b[39m'\u001b[39m\u001b[39mObject of type \u001b[39m\u001b[39m{\u001b[39;00mo\u001b[39m.\u001b[39m\u001b[39m__class__\u001b[39m\u001b[39m.\u001b[39m\u001b[39m__name__\u001b[39m\u001b[39m}\u001b[39;00m\u001b[39m \u001b[39m\u001b[39m'\u001b[39m\n\u001b[1;32m    180\u001b[0m                     \u001b[39mf\u001b[39m\u001b[39m'\u001b[39m\u001b[39mis not JSON serializable\u001b[39m\u001b[39m'\u001b[39m)\n", "\u001b[0;31mTypeError\u001b[0m: Object of type A is not JSON serializable"]}], "source": ["import json\n", "from research.core.ui_sugar import UISugar\n", "\n", "class A(UISugar):\n", "    b: ...\n", "    def __init__(self, x):\n", "        self.b = x\n", "\n", "class B(UISugar):\n", "    a: ...\n", "    def __init__(self, x):\n", "        self.a = x\n", "\n", "a = A(None)\n", "json_str = json.dumps(a)\n", "print(json)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'setting': 'new_value'}\n", "{'setting': 'default_value'}\n"]}], "source": ["from contextlib import contextmanager\n", "\n", "config = {\n", "    'setting': 'default_value'\n", "}\n", "\n", "@contextmanager\n", "def override_config(key, value):\n", "    original_value = config.get(key)\n", "    config[key] = value\n", "    try:\n", "        yield\n", "    finally:\n", "        if original_value is None:\n", "            config.pop(key)\n", "        else:\n", "            config[key] = original_value\n", "\n", "# Using the context manager\n", "with override_config('setting', 'new_value'):\n", "    print(config)  # {'setting': 'new_value'}\n", "print(config)  # {'setting': 'default_value'}\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating instance:\n", "__init__ method called!\n", "MyClass(value=Initialized)\n", "\n", "Serialized Data: {'class_name': 'MyClass', 'attributes': {'value': 'Initialized'}}\n", "\n", "Loading instance from serialized data:\n"]}, {"ename": "TypeError", "evalue": "object.__new__(): not enough arguments", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 46\u001b[0m\n\u001b[1;32m     44\u001b[0m \u001b[39m# Deserialize the object\u001b[39;00m\n\u001b[1;32m     45\u001b[0m \u001b[39mprint\u001b[39m(\u001b[39m\"\u001b[39m\u001b[39m\\n\u001b[39;00m\u001b[39mLoading instance from serialized data:\u001b[39m\u001b[39m\"\u001b[39m)\n\u001b[0;32m---> 46\u001b[0m loaded_obj \u001b[39m=\u001b[39m Serializer\u001b[39m.\u001b[39;49mloads(serialized_data)\n\u001b[1;32m     47\u001b[0m \u001b[39mprint\u001b[39m(loaded_obj)\n", "Cell \u001b[0;32mIn[4], line 18\u001b[0m, in \u001b[0;36mSerializer.loads\u001b[0;34m(data)\u001b[0m\n\u001b[1;32m     15\u001b[0m \u001b[39mcls\u001b[39m \u001b[39m=\u001b[39m \u001b[39mglobals\u001b[39m()[data[\u001b[39m\"\u001b[39m\u001b[39mclass_name\u001b[39m\u001b[39m\"\u001b[39m]]\n\u001b[1;32m     17\u001b[0m \u001b[39m# Create a new instance without calling __init__\u001b[39;00m\n\u001b[0;32m---> 18\u001b[0m obj \u001b[39m=\u001b[39m \u001b[39mcls\u001b[39;49m\u001b[39m.\u001b[39;49m\u001b[39m__new__\u001b[39;49m()\n\u001b[1;32m     20\u001b[0m \u001b[39m# Set the attributes on the object\u001b[39;00m\n\u001b[1;32m     21\u001b[0m obj\u001b[39m.\u001b[39m\u001b[39m__dict__\u001b[39m\u001b[39m.\u001b[39mupdate(data[\u001b[39m\"\u001b[39m\u001b[39mattributes\u001b[39m\u001b[39m\"\u001b[39m])\n", "\u001b[0;31mTypeError\u001b[0m: object.__new__(): not enough arguments"]}], "source": ["class Serializer:\n", "\n", "    @staticmethod\n", "    def dumps(obj):\n", "        # Capture class name and object dictionary\n", "        data = {\n", "            \"class_name\": obj.__class__.__name__,\n", "            \"attributes\": obj.__dict__\n", "        }\n", "        # For simplicity, we just return the dict. In reality, this would be a string or bytes.\n", "        return data\n", "\n", "    @staticmethod\n", "    def loads(data):\n", "        # Fetch the class from the global scope\n", "        cls = globals()[data[\"class_name\"]]\n", "        \n", "        # Create a new instance without calling __init__\n", "        obj = cls.__new__(cls)\n", "        \n", "        # Set the attributes on the object\n", "        obj.__dict__.update(data[\"attributes\"])\n", "        \n", "        return obj\n", "\n", "\n", "class MyClass:\n", "    def __init__(self):\n", "        print(\"__init__ method called!\")\n", "        self.value = \"Initialized\"\n", "\n", "    def __repr__(self):\n", "        return f\"MyClass(value={self.value})\"\n", "\n", "\n", "# Create an instance\n", "print(\"Creating instance:\")\n", "obj = MyClass()\n", "print(obj)\n", "\n", "# Serialize the object\n", "serialized_data = Serializer.dumps(obj)\n", "print(\"\\nSerialized Data:\", serialized_data)\n", "\n", "# Deserialize the object\n", "print(\"\\nLoading instance from serialized data:\")\n", "loaded_obj = Serializer.loads(serialized_data)\n", "print(loaded_obj)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}