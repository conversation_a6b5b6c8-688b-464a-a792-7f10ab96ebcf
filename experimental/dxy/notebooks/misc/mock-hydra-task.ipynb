{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import yaml\n", "\n", "from research.eval.harness import tasks\n", "from research.eval.hydra import driver\n", "from research.eval.tests.hydra import mock_k8s_utils\n", "from mock import patch\n", "\n", "\n", "def mock_cw_docker():\n", "    \"\"\"The default mock interface to cw_docker.\n", "\n", "    This isn't expected to be modified much, so it's expected most tests will\n", "    just need this fixture.\n", "    \"\"\"\n", "    # The driver validates that the supported_repos file points to valid\n", "    # images, so we have to mock this here.\n", "    supported = pathlib.Path(\n", "        \"/home/<USER>/src/augment/research/eval/hydra/supported_repos.yaml\"\n", "    )\n", "    return mock_k8s_utils.MockCwDocker(yaml.safe_load(supported.read_text()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task = tasks.HydraTask()\n", "mock_k8 = mock_k8s_utils.MockK8(\"running\")\n", "with patch(\"augment.research.eval.hydra.driver.k8s_utils\", new=mock_k8), patch(\n", "    \"augment.research.eval.hydra.driver.cw_docker\", new=mock_cw_docker()\n", "):\n", "    # Override the cache driver to be a fake one.\n", "    task._cache_driver = driver.Driver()\n", "    for index, (inputs, _) in enumerate(task):\n", "        target_completion = inputs.target\n", "        if target_completion is None:\n", "            raise ValueError(f\"{task.name}[{index}] should have non-empty target.\")\n", "        results = task.execute(inputs, target_completion)\n", "        assert results[\"pass\"], f\"{task.name}[{index}] should pass.\""]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello_World\n", "File_Name\n", "File_Name\n"]}], "source": ["import re\n", "\n", "def sanitize_filename(filename):\n", "    # Remove any character that is not alphanumeric, an underscore, or a hyphen\n", "    sanitized = re.sub(r'[^\\w\\-_]', '_', filename)\n", "    \n", "    # Replace spaces with underscores\n", "    sanitized = sanitized.replace(' ', '_')\n", "    \n", "    # Remove any leading or trailing underscores or hyphens\n", "    sanitized = sanitized.strip('_-')\n", "    \n", "    return sanitized\n", "\n", "# Test the function\n", "print(sanitize_filename(\"Hello World!\"))  # Output should be \"Hello_World_\"\n", "print(sanitize_filename(\"File/Name\"))  # Output should be \"File_Name\"\n", "print(sanitize_filename(\"File*Name\"))  # Output should be \"File_Name\"\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}