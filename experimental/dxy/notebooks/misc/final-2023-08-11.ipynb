{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This is A\n"]}], "source": ["from typing import final\n", "\n", "\n", "class A:\n", "\n", "    def __call__(self):\n", "        self.__show()\n", "\n", "    @final\n", "    def __show(self):\n", "        print(\"This is A\")\n", "\n", "\n", "class B(A):\n", "\n", "    def __show(self):\n", "        print(\"This is B\")\n", "\n", "x = B()\n", "x()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["This is B\n"]}], "source": ["from typing import final\n", "\n", "\n", "class A:\n", "\n", "    def __call__(self):\n", "        self._show()\n", "\n", "    def _show(self):\n", "        print(\"This is A\")\n", "\n", "\n", "class B(A):\n", "\n", "    def _show(self):\n", "        print(\"This is B\")\n", "\n", "x = B()\n", "x()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}