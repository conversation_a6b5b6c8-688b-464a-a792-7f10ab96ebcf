{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import download_hydra\n", "\n", "patches_by_repo, files_for_retrieval_by_repo = download_hydra(\"repoeval_functions\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import HydraTask\n", "\n", "task = HydraTask()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_ground_truths = []\n", "for input, repo in task:\n", "    ground_truth = input.extra[\"ground_truth\"]\n", "    all_ground_truths.append(ground_truth)\n", "print(len(all_ground_truths))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import torch\n", "\n", "total = 455\n", "result_dir_by_name = {\n", "    \"sota6k-diff_boykin\": \"/mnt/efs/augment/user/dxy/results/sota6k-diff_boykin-basesc-16b/\",\n", "    \"sota6k-bm25\": \"/mnt/efs/augment/user/dxy/results/sota6k-bm25-basesc-16b/\",\n", "}\n", "results_by_name = {}\n", "for key, cur_dir in result_dir_by_name.items():\n", "    all_results = []\n", "    for index in range(total):\n", "        cur_path = os.path.join(cur_dir, f\"{index:05d}.ckp\")\n", "        cur_result = torch.load(cur_path)\n", "        all_results.append(cur_result)\n", "    results_by_name[key] = all_results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.all_models import get_formatter_by_model_name\n", "from research.eval.harness.tasks.hydra_task import HydraTask\n", "task = HydraTask(\"repoeval_functions\")\n", "formatter = get_formatter_by_model_name(\"starcoderbase-16b\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indexes_bm25_beats_diff = []\n", "indexes_diff_beats_bm25 = []\n", "for i in range(total):\n", "    bm25 = results_by_name[\"sota6k-bm25\"][i][\"results\"][\"pass\"]\n", "    diff_boykin = results_by_name[\"sota6k-diff_boykin\"][i][\"results\"][\"pass\"]\n", "    if bm25 and not diff_boykin:\n", "        # print(f\"{i:3d} : bm25 wins but diff_boykin loses\")\n", "        indexes_bm25_beats_diff.append(i)\n", "    elif not bm25 and diff_boykin:\n", "        # print(f\"{i:3d} : bm25 loses but diff_boykin wins\")\n", "        indexes_diff_beats_bm25.append(i)\n", "print(f\"bm25 wins but diff_boy<PERSON> loses : {len(indexes_bm25_beats_diff)} : {indexes_bm25_beats_diff}\")\n", "print(f\"diff_boykin wins but bm25 loses : {len(indexes_diff_beats_bm25)} : {indexes_diff_beats_bm25}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"!!BM25!!\")\n", "for index in indexes_bm25_beats_diff:\n", "    inputs, repo = task[index]\n", "    print(inputs.path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"!!DIFF-Boykin!!\")\n", "for index in indexes_diff_beats_bm25:\n", "    inputs, repo = task[index]\n", "    print(inputs.path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "\n", "def show_completion(prefix: str, suffix: str, middle: str):\n", "    print(colored(prefix, color=\"blue\"), end=\"\")\n", "    print(colored(middle, color=\"green\"), end=\"\")\n", "    print(colored(suffix, color=\"blue\"), end=\"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_by_name[\"sota6k-diff_boykin\"][index]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results_by_name[\"sota6k-bm25\"][index]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\".\"\"\"\n", "from __future__ import annotations\n", "\n", "def is_new_line(prefix: str) -> bool:\n", "    last_newline_idx = prefix.rfind(\"\\n\")\n", "    if last_newline_idx >= 0:\n", "        last_line = prefix[last_newline_idx:]\n", "    else:\n", "        last_line = prefix\n", "\n", "    is_last_line_just_whitespace = len(last_line.strip()) == 0\n", "    return is_last_line_just_whitespace\n", "\n", "\n", "def is_suffix_new_line(suffix: str) -> bool:\n", "    return suffix.startswith(\"\\n\") or suffix.strip() == \"\"\n", "\n", "\n", "def get_plot_data_for_task(task: AbstractTask, keep_indexes: list[int] | None = None):\n", "    prefix_tokens, suffix_tokens, middle_tokens = [], [], []\n", "    prefix_lines, suffix_lines, middle_lines = [], [], []\n", "    completion_starts_new_line = []\n", "    completion_ends_new_line = []\n", "    for index, (inputs, repo) in enumerate(task):\n", "        if keep_indexes and index not in keep_indexes:\n", "            continue\n", "        p_l = len(formatter.tokenizer.tokenize(inputs.prefix))\n", "        s_l = len(formatter.tokenizer.tokenize(inputs.suffix))\n", "        if inputs.target is None:\n", "            raise ValueError(f\"Target is None at {index}\")\n", "        m_l = len(formatter.tokenizer.tokenize(inputs.target))\n", "        # Count the number of tokens\n", "        prefix_tokens.append(p_l)\n", "        suffix_tokens.append(s_l)\n", "        middle_tokens.append(m_l)\n", "        # Count the number of lines\n", "        prefix_lines.append(inputs.prefix.count(\"\\n\"))\n", "        suffix_lines.append(inputs.suffix.count(\"\\n\"))\n", "        middle_lines.append(inputs.target.count(\"\\n\"))\n", "        # Count misc\n", "        completion_starts_new_line.append(is_new_line(inputs.prefix))\n", "        completion_ends_new_line.append(is_suffix_new_line(inputs.suffix))\n", "\n", "    line_edges = [0, 1, 2, 3, 4, 8, 16, 64, 128, 256]\n", "    data = [\n", "        {\n", "            \"title\": \"Prefix <PERSON>kens\",\n", "            \"value\": prefix_tokens,\n", "            \"bin_edges\": [0, 10, 50, 100, 500, 1000, 2000],\n", "        },\n", "        {\n", "            \"title\": \"Prefix Lines\",\n", "            \"value\": prefix_lines,\n", "            \"bin_edges\": line_edges,\n", "        },\n", "        {\n", "            \"title\": \"Suffix <PERSON>\",\n", "            \"value\": suffix_tokens,\n", "            \"bin_edges\": [0, 10, 50, 100, 500, 1000, 2000],\n", "        },\n", "        {\n", "            \"title\": \"Suffix Lines\",\n", "            \"value\": suffix_lines,\n", "            \"bin_edges\": line_edges,\n", "        },\n", "        {\n", "            \"title\": \"Middle Tokens\",\n", "            \"value\": middle_tokens,\n", "            \"bin_edges\": [0, 16, 32, 64, 128, 256, 512],\n", "        },\n", "        {\n", "            \"title\": \"Middle Lines\",\n", "            \"value\": middle_lines,\n", "            \"bin_edges\": line_edges,\n", "        },\n", "        {\n", "            \"title\": \"Completion Starts New Line\",\n", "            \"value\": [int(x) for x in completion_starts_new_line],\n", "            \"bin_edges\": [0, 1, 2],\n", "            \"override_tick_labels\": [\"False\", \"True\"],\n", "        },\n", "        {\n", "            \"title\": \"Completion Ends New Line\",\n", "            \"value\": [int(x) for x in completion_ends_new_line],\n", "            \"bin_edges\": [0, 1, 2],\n", "            \"override_tick_labels\": [\"False\", \"True\"],\n", "        },\n", "    ]\n", "    return data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from __future__ import annotations\n", "from typing import cast\n", "import numpy as np\n", "from matplotlib.axes import Axes\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "def plot_sub_histograms(\n", "    data: list[dict], per_figsize: tuple[int, int], title: str | None = None\n", "):\n", "    \"\"\"\n", "    Plot multiple sub-histograms based on a list of dictionaries.\n", "\n", "    :param data: List of dictionaries where each dictionary has 'title' and 'value' keys.\n", "    \"\"\"\n", "    n = len(data)\n", "\n", "    # Creating subplots\n", "    if n % 2 == 0:\n", "        fig, axes = plt.subplots(\n", "            n // 2,\n", "            2,\n", "            figsize=(per_figsize[0] * 2, per_figsize[1] * n // 2),\n", "            facecolor=\"lightblue\",\n", "        )\n", "        axes = [*axes.flatten()]\n", "    else:\n", "        fig, axes = plt.subplots(\n", "            n, 1, figsize=(per_figsize[0], per_figsize[1] * n), facecolor=\"lightblue\"\n", "        )\n", "\n", "    # In case there's only one dictionary, we wrap axes in a list for consistent indexing\n", "    if n == 1:\n", "        axes = [axes]\n", "\n", "    for i, d in enumerate(data):\n", "        # axes[i].hist(d['value'], bins=d.get(\"bin_edges\", 10))\n", "        cur_axes = cast(Axes, axes[i])\n", "        bins = d.get(\"bin_edges\", 10)\n", "        override_tick_labels = d.get(\"override_tick_labels\", None)\n", "        if isinstance(bins, int):\n", "            cur_axes.hist(d[\"value\"], bins=bins)\n", "        elif isinstance(bins, (list, tuple)):\n", "            # Customized plot of bars\n", "            if override_tick_labels is None:\n", "                bin_indices = np.digitize(d[\"value\"], bins) - 1\n", "                unique, counts = np.unique(bin_indices, return_counts=True)\n", "\n", "                # Create labels while handling the edge case\n", "                tick_labels = [\n", "                    f\"[{bins[j]}, {bins[j+1]})\" if j + 1 < len(bins) else f\">={bins[j]}\"\n", "                    for j in unique\n", "                ]\n", "\n", "                # Display bars with equal width\n", "                bars = cur_axes.bar(\n", "                    unique, counts, align=\"center\", tick_label=tick_labels\n", "                )\n", "            else:\n", "                counts, _ = np.histogram(d[\"value\"], bins=bins)\n", "                assert len(counts) == len(override_tick_labels)\n", "                bars = cur_axes.bar(\n", "                    range(len(counts)),\n", "                    counts,\n", "                    align=\"center\",\n", "                    tick_label=override_tick_labels,\n", "                )\n", "            # Add the real number value on top of each bar\n", "            for bar in bars:\n", "                height = bar.get_height()\n", "                cur_axes.text(\n", "                    bar.get_x() + bar.get_width() / 2,\n", "                    height + 0.01 * max(counts),\n", "                    str(int(height)),\n", "                    ha=\"center\",\n", "                    va=\"bottom\",\n", "                )\n", "        else:\n", "            raise TypeError(f\"Invalid type : {type(bins)}\")\n", "\n", "        cur_axes.set_title(d[\"title\"])\n", "        cur_axes.set_xlabel(f\"Value Range ({len(d['value'])} in total)\")\n", "        cur_axes.set_ylabel(\"#Example\")\n", "        cur_axes.grid(True, which=\"both\", linestyle=\"--\", linewidth=0.5)\n", "    if title:\n", "        fig.suptitle(\n", "            title.replace(\"_\", \" \").capitalize(), fontsize=16, fontweight=\"bold\"\n", "        )\n", "    plt.subplots_adjust(wspace=0.5, hspace=0.5)\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_name = \"repoeval_functions\"\n", "plot_sub_histograms(\n", "    get_plot_data_for_task(task, indexes_diff_beats_bm25), per_figsize=(8, 5), title=task_name\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["task_name = \"repoeval_functions\"\n", "plot_sub_histograms(\n", "    get_plot_data_for_task(task, indexes_bm25_beats_diff), per_figsize=(8, 5), title=task_name\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}