{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Run `hydra` with Fake Code Completion"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Creates a `hydra Driver`."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import getpass\n", "from research.eval import hydra\n", "\n", "HYDRA_DRIVER_NAME = f\"{getpass.getuser()}-dev-hydra-notebook-example\"\n", "HYDRA_LOCAL_TIMEOUT_SECS = 30\n", "HYDRA_GLOBAL_TIMEOUT_SECS = 180\n", "HYDRA_NUM_PATCHES_LIMIT = 10\n", "\n", "driver = hydra.Driver(\n", "    driver_name=HYDRA_DRIVER_NAME,\n", "    local_timeout_secs=HYDRA_LOCAL_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_GLOBAL_TIMEOUT_SECS,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load data to generate completions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load 46 repo patches.\n"]}], "source": ["import pathlib\n", "from research.eval import patch_lib\n", "\n", "patches_path = \"/mnt/efs/augment/data/eval/hydra/datasets/repoeval_functions/CarperAI/trlx_patches.jsonl\"\n", "with pathlib.Path(patches_path).open() as f:\n", "    repo_patches = [patch_lib.Patch.from_json(line) for line in f.readlines()]\n", "print(f\"Load {len(repo_patches)} repo patches.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There is one Hydra patch:\n", "\n", "\u001b[34mimport random\n", "import sys\n", "from abc import abstractmethod, abstractstaticmethod\n", "from typing import Any, Callable, Dict, Iterable\n", "\n", "from torch.utils.data import DataLoader, Dataset\n", "\n", "from trlx.data import GeneralElement, RLElement\n", "\n", "# specifies a dictionary of architectures\n", "_DATAPIPELINE: Dict[str, any] = {}  # registry\n", "\n", "\n", "def register_datapipeline(name):\n", "    \"\"\"Decorator used register a CARP architecture\n", "    Args:\n", "        name: Name of the architecture\n", "    \"\"\"\n", "\n", "\u001b[0m\u001b[32m    def register_class(cls, name):\n", "        _DATAPIPELINE[name] = cls\n", "        setattr(sys.modules[__name__], name, cls)\n", "        return cls\n", "\n", "    if isinstance(name, str):\n", "        name = name.lower()\n", "        return lambda c: register_class(c, name)\n", "\n", "    cls = name\n", "    name = cls.__name__\n", "    register_class(cls, name.lower())\n", "\n", "    return cls\n", "\u001b[0m\u001b[34m\n", "\n", "@register_datapipeline\n", "class BasePipeline(Dataset):\n", "    def __init__(self, path: str = \"dataset\"):\n", "        super().__init__()\n", "\n", "    @abstractmethod\n", "    def __getitem__(self, index: int) -> GeneralElement:\n", "        pass\n", "\n", "    @abstractmethod\n", "    def __len__(self) -> int:\n", "        pass\n", "\n", "    @abstractmethod\n", "    def create_loader(\n", "        self,\n", "        batch_size: int,\n", "        shuffle: bool,\n", "        prep_fn: Callable = None,\n", "        num_workers: int = 0,\n", "    ) -> DataLoader:\n", "        \"\"\"\n", "        Create a dataloader for the pipeline\n", "\n", "        :param prep_fn: Typically a tokenizer. Applied to GeneralElement after collation.\n", "        \"\"\"\n", "        pass\n", "\n", "\n", "class BaseRolloutStore(Dataset):\n", "    def __init__(self, capacity=-1):\n", "        self.history: Iterable[Any] = None\n", "        self.capacity = capacity\n", "\n", "    @abstractmethod\n", "    def push(self, exps: Iterable[Any]):\n", "        \"\"\"\n", "        Push experiences to rollout storage\n", "        \"\"\"\n", "        pass\n", "\n", "    def __getitem__(self, index: int) -> RLElement:\n", "        return self.history[index]\n", "\n", "    def __len__(self) -> int:\n", "        return len(self.history)\n", "\n", "    @abstractmethod\n", "    def create_loader(\n", "        self,\n", "        batch_size: int,\n", "        shuffle: bool,\n", "        prep_fn: Callable = None,\n", "        num_workers: int = 0,\n", "    ) -> DataLoader:\n", "        \"\"\"\n", "        Create a dataloader for the rollout store\n", "\n", "        :param prep_fn: Applied to RLElement after collation (typically tokenizer)\n", "        :type prep_fn: Callable\n", "        \"\"\"\n", "        pass\n", "\u001b[0m\n"]}], "source": ["from termcolor import colored\n", "\n", "index = 0\n", "\n", "patch = repo_patches[index]\n", "prefix = patch.file_content[: patch.char_start]\n", "suffix = patch.file_content[patch.char_end :]\n", "completion = patch.file_content[patch.char_start: patch.char_end]\n", "print(f'There is one Hydra patch:\\n')\n", "print(colored(prefix, color=\"blue\"), end=\"\")\n", "print(colored(completion, color=\"green\"), end=\"\")\n", "print(colored(suffix, color=\"blue\"), end=\"\\n\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:SUCCESS: created ConfigMap hydra-augment-dev-hydra-notebook-example-local-cfg-qfgbxlhx.\n", "INFO:root:SUCCESS: created Pod hydra-augment-dev-hydra-notebook-example-local-pod-fgecqyfc.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/0                                   \u001b[32mPASSED\u001b[0m     wall_time=5.25      test_time=5.25\n"]}], "source": ["# Sending the completion to the Hydra `Driver` to run and evaluate for PASSED/FAILED via `driver.dispatch`.\n", "patch = patch.with_patch_content(completion)\n", "results = driver.dispatch(patch)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The avaliable keys in results: ['config_map', 'patch_json', 'result_str', 'run_output']\n", "CarperAI_trlx/0                                   \u001b[32mPASSED\u001b[0m     wall_time=5.25      test_time=5.25\n"]}], "source": ["print(f'The avaliable keys in results: {list(results.keys())}')\n", "print(results['result_str'])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}