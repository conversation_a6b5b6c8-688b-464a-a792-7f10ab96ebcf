{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.ui_sugar import UISugar\n", "\n", "class A(UISugar):\n", "    b: ...\n", "    def __init__(self, x):\n", "        self.b = x\n", "\n", "class B(UISugar):\n", "    a: ...\n", "    def __init__(self, x):\n", "        self.a = x\n", "\n", "a = A(None)\n", "b = B(a)\n", "a.b = b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(a)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from contextlib import contextmanager\n", "\n", "config = {\n", "    'setting': 'default_value'\n", "}\n", "\n", "@contextmanager\n", "def override_config(key, value):\n", "    original_value = config.get(key)\n", "    config[key] = value\n", "    try:\n", "        yield\n", "    finally:\n", "        if original_value is None:\n", "            config.pop(key)\n", "        else:\n", "            config[key] = original_value\n", "\n", "# Using the context manager\n", "with override_config('setting', 'new_value'):\n", "    print(config)  # {'setting': 'new_value'}\n", "print(config)  # {'setting': 'default_value'}\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}