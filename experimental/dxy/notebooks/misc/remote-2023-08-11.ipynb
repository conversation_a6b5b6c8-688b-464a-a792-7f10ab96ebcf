{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.all_models import RemoteModel\n", "from research.core.model_input import ModelInput\n", "from research.models import GenerationOptions\n", "\n", "model = RemoteModel(\"http://216.153.49.205:5000\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input = ModelInput(prefix=\"def hello_world(\", suffix=\"')\")\n", "options = GenerationOptions(temperature=0.0, max_generated_tokens=128)\n", "result = model.generate(input, options)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}