{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to play with WizardCoder\n", "\n", "**Step 1**: run `bash research/utils/launch-llama.cpp.sh --build` to build LLaMA CPP on your local machine\n", "\n", "**Step 2**: run `bash research/utils/launch-llama.cpp.sh --wizardcoder 8086` to launch a LLaMA CPP-based WizardCoder model, the number `8086` is the port number which you can change to what ever port."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.models.remote_models import (\n", "    GenerationOptions,\n", "    CodeLLaMA_LLaMACPP_Model,\n", ")\n", "from research.core.llama_prompt_formatters import WizardCoderChatFormatter\n", "from research.core.model_input import ModelInput\n", "\n", "model = CodeLLaMA_LLaMACPP_Model(url=\"http://127.0.0.1:8086\")\n", "model.prompt_formatter = WizardCoderChatFormatter()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```python\n", "print(\"Hello, <PERSON>!\")\n", "``` \n"]}], "source": ["prompt: str = \"Write a python code of hello world\"\n", "\n", "response = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    options=GenerationOptions(max_generated_tokens=512),\n", ")\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}