{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from research.eval import patch_lib\n", "from research.environments import cw_docker\n", "from research.eval.hydra import (\n", "    constants,\n", "    cov_utils,\n", "    suggest_impl,\n", "    visualize_utils,\n", ")\n", "from research.eval.patch_lib import create_patch_from_files\n", "\n", "def show_patch(patch: patch_lib.Patch):\n", "    for key, value in vars(patch).items():\n", "        print(f\"{key} : {value}\")\n", "\n", "\n", "repo_path = Path(\"/mnt/efs/augment/user/dxy/repos/pyglove/v0.4.2\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_sha = \"870ed11798703e56e9c9523af5b52bd92ec320a4\"\n", "lang = \"py\"\n", "use_coverage = False\n", "all_patches = list(\n", "    suggest_impl.fim_sample(\n", "        repo_path=repo_path,\n", "        code_path=Path(\"pyglove\"),\n", "        lang=\"python\",\n", "        minimum_file_size_bytes=0,\n", "        random_seed=None,\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["patch = all_patches[6]\n", "patch.repository = \"google/pyglove\"\n", "patch.commit_sha = repo_sha\n", "patch.patch_id = f\"{patch.repository}/12345678\"\n", "show_patch(patch)\n", "pg_patch = patch"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.hydra_task import HydraTask\n", "\n", "task = HydraTask()\n", "hydra_task_patch = task._ex_patches[1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval import hydra\n", "\n", "HYDRA_SOFT_TIMEOUT_SECS = 600\n", "HYDRA_HARD_TIMEOUT_SECS = 1800\n", "hydra_driver = hydra.Driver(\n", "    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,\n", "    hydra_block_resource_internet_access=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = hydra_driver.dispatch(hydra_task_patch.to_json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pg_patch.file_name = \"pyglove/ext/evolution/neat.py\"\n", "results = hydra_driver.dispatch(pg_patch.to_json())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}