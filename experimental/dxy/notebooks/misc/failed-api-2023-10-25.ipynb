{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1053 json files under /mnt/efs/augment/eval/jobs/G5au6jhe/cache\n", "There are 1053 json files under /mnt/efs/augment/eval/jobs/G5au6jhe/cache\n", "There are 13 datapoints mismatched for /mnt/efs/augment/eval/jobs/G5au6jhe vs. /mnt/efs/augment/eval/jobs/mcTPNvDR\n"]}], "source": ["import pathlib\n", "import json\n", "\n", "folder_1 = \"/mnt/efs/augment/eval/jobs/G5au6jhe\"\n", "folder_2 = \"/mnt/efs/augment/eval/jobs/mcTPNvDR\"\n", "\n", "json_pathes_1 = sorted(\n", "    list(\n", "        (pathlib.Path(folder_1) / \"000__RAGSystem_ApiCallTask\" / \"cache\").glob(\"*.json\")\n", "    )\n", ")\n", "json_pathes_2 = sorted(\n", "    list(\n", "        (pathlib.Path(folder_2) / \"000__RAGSystem_ApiCallTask\" / \"cache\").glob(\"*.json\")\n", "    )\n", ")\n", "print(f\"There are {len(json_pathes_1)} json files under {folder_1}/cache\")\n", "print(f\"There are {len(json_pathes_2)} json files under {folder_1}/cache\")\n", "\n", "failed_matches = []\n", "for path_1, path_2 in zip(json_pathes_1, json_pathes_2):\n", "    assert path_1.name == path_2.name\n", "    data_1 = json.load(path_1.open())\n", "    data_2 = json.load(path_2.open())\n", "    xkey = \"metric@hydra-unit-test-pass\"\n", "    if (\n", "        xkey in data_1\n", "        and data_1[xkey] != data_2[xkey]\n", "        and data_1[\"completion\"] == data_2[\"completion\"]\n", "    ):\n", "        failed_matches.append((data_1, data_2))\n", "print(\n", "    f\"There are {len(failed_matches)} datapoints mismatched for {folder_1} vs. {folder_2}\"\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "from research.core import utils_for_str\n", "\n", "\n", "def show_single_data(data_1):\n", "    print(\"Data point 1: completion vs. target\")\n", "    prefix = utils_for_str.get_last_n_lines(data_1[\"inputs\"][\"prefix\"], 6)\n", "    suffix = utils_for_str.get_first_n_lines(data_1[\"inputs\"][\"suffix\"], 6)\n", "    completion = data_1[\"completion\"]\n", "    target = data_1[\"inputs\"][\"target\"]\n", "    print(termcolor.colored(prefix, \"green\"), end=\"\")\n", "    print(termcolor.colored(completion, \"red\"), end=\"\")\n", "    print(termcolor.colored(suffix, \"blue\"), end=\"\\n\")\n", "    print(\"-\" * 128)\n", "    print(termcolor.colored(prefix, \"green\"), end=\"\")\n", "    print(termcolor.colored(target, \"red\"), end=\"\")\n", "    print(termcolor.colored(suffix, \"blue\"), end=\"\\n\")\n", "    print(\"-\" * 128 + \"\\n\")\n", "\n", "\n", "def show_nested_keys(x: dict, indent=0):\n", "    \"\"\"Show nested keys.\"\"\"\n", "    for k, v in x.items():\n", "        print(\"  \" * indent + k, end=\"\\n\")\n", "        if isinstance(v, dict):\n", "            show_nested_keys(v, indent + 2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import getpass\n", "from research.eval import hydra\n", "\n", "HYDRA_DRIVER_NAME = f\"{getpass.getuser()}-dev-hydra-notebook-example-x\"\n", "driver = hydra.Driver(max_parallel_runs=1, max_containers_per_pod=1)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/typing/value_specs.py-49238-49392                          \u001b[32mPASSED\u001b[0m     wall_time=17.60     test_time=17.60     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n", "_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/typing/value_specs.py-49238-49392                          \u001b[31mFAILED\u001b[0m     wall_time=17.33     test_time=17.33     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n", "_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/typing/value_specs.py-49238-49392                          \u001b[32mPASSED\u001b[0m     wall_time=18.28     test_time=18.28     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n", "_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/typing/value_specs.py-49238-49392                          \u001b[32mPASSED\u001b[0m     wall_time=11.31     test_time=11.31     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n", "_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/typing/value_specs.py-49238-49392                          \u001b[32mPASSED\u001b[0m     wall_time=11.46     test_time=11.46     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n"]}], "source": ["import json\n", "from research.eval import patch_lib\n", "\n", "\n", "def eval_fn(data: dict):\n", "    data[\"inputs\"][\"extra\"]\n", "    patch_json = json.loads(data[\"metric-auxiliary@raw-results\"][\"patch_json\"])\n", "    patch = patch_lib.Patch(**patch_json)\n", "    # print(patch)\n", "    image_name = (\n", "        \"google/pyglove:v1.0\"\n", "        if \"pyglove\" in patch.patch_id\n", "        else \"pydantic/pydantic:v1.0\"\n", "    )\n", "    results = driver.dispatch(patch, image_name)\n", "    return results\n", "\n", "\n", "all_results = []\n", "for idx in range(10):\n", "    results = eval_fn(failed_matches[0][0])\n", "    all_results.append(results)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n", "_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=37.56     test_time=37.56     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=33.91     test_time=33.91     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=42.36     test_time=42.36     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=17.64     test_time=17.64     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=18.97     test_time=18.97     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=17.67     test_time=17.67     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n", "_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=17.61     test_time=17.61     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=18.29     test_time=18.29     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=80.94     test_time=80.94     \n"]}, {"name": "stderr", "output_type": "stream", "text": ["_process_queue thread starting max_containers_per_pod: 1 max_queue_latency_sec: 10.0\n", "_process_queue thread exiting\n"]}, {"name": "stdout", "output_type": "stream", "text": ["pyglove/pyglove/core/symbolic/base.py-66634-66820                               \u001b[32mPASSED\u001b[0m     wall_time=45.80     test_time=45.80     \n"]}], "source": ["all_results = []\n", "for idx in range(10):\n", "    results = eval_fn(failed_matches[2][0])\n", "    all_results.append(results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}