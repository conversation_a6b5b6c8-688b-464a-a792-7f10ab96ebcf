{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "import os\n", "os.environ['CUDA_VISIBLE_DEVICES'] = \"5\"\n", "os.environ['AUGMENT_CHECKPOINTS_ROOT'] = \"/home/<USER>/checkpoints\"\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How the remote models are launched?\n", "\n", "```\n", "# The baseline remote model\n", "CUDA_VISIBLE_DEVICES=0 python launch_model_server.py --model starcoderbase --max_generated_tokens 512 --port 5001\n", "\n", "# Our FIM-tuned model\n", "CUDA_VISIBLE_DEVICES=1 python launch_model_server.py --model starcoderbase_16b_fim_aligned --max_generated_tokens 512 --port 5002\n", "\n", "# Plus Retrieval\n", "CUDA_VISIBLE_DEVICES=2 python launch_model_server.py \\\n", "    --model starcoderbase_16b_fim_aligned \\\n", "    --max_generated_tokens 512 \\\n", "    --retriever diff_boykin \\\n", "    --retrieval_path $AUGMENT_SRC_PATH/research \\\n", "    --port 5003\n", "```"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "\n", "def show_completion(prefix: str, suffix: str, middle: str):\n", "    print(colored(prefix, color=\"blue\"), end=\"\")\n", "    print(colored(middle, color=\"green\"), end=\"\")\n", "    print(colored(suffix, color=\"blue\"), end=\"\\n\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:faiss.loader:Loading faiss with AVX2 support.\n", "INFO:faiss.loader:Could not load library with AVX2 support due to:\n", "ModuleNotFoundError(\"No module named 'faiss.swigfaiss_avx2'\")\n", "INFO:faiss.loader:Loading faiss.\n", "INFO:faiss.loader:Successfully loaded faiss.\n", "INFO:torch.distributed.nn.jit.instantiator:Created a temporary directory at /tmp/tmp4vxbrh6w\n", "INFO:torch.distributed.nn.jit.instantiator:Writing /tmp/tmp4vxbrh6w/_remote_module_non_scriptable.py\n", "INFO:numexpr.utils:Note: NumExpr detected 48 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n"]}], "source": ["from __future__ import annotations\n", "\n", "import abc\n", "import copy\n", "import pyglove as pg\n", "from typing import Any\n", "from research.core.abstract_prompt_formatter import AbstractPromptFormatter\n", "from research.core.model_input import ModelInput\n", "from research.core.ui_sugar import UISugar\n", "from research.eval.harness.tasks.hydra_task import HydraTask\n", "from research.eval.harness.systems.abs_system import AbstractSystem\n", "from research.models import (\n", "    GenerativeLanguageModel,\n", "    GenerationOptions,\n", "    ExtraGenerationOutputs,\n", ")\n", "from research.models.all_models import get_formatter_by_model_name\n", "from research.retrieval.types import Chunk, Document, DocumentIndex\n", "from research.eval.harness.systems.libraries.retrieval_chunks_filterer import (\n", "    filter_localctx_and_ground_truth_from_retrievals,\n", ")\n", "from research.eval.harness.systems.libraries.completion_trimmers import (\n", "    trim_on_dedent,\n", ")\n", "\n", "\n", "class AbstractPreProcessor(UISugar):\n", "    \"\"\"The Abstract PreProcessor for the system.\"\"\"\n", "\n", "    @abc.abstractmethod\n", "    def __call__(\n", "        self,\n", "        model_input: ModelInput,\n", "        retriever: DocumentIndex | None = None,\n", "        prompt_formatter: AbstractPromptFormatter | None = None,\n", "    ) -> ModelInput:\n", "        \"\"\".\"\"\"\n", "\n", "\n", "class BasicPreProcessor(AbstractPreProcessor):\n", "    \"\"\"The Basic PostProcessor for the system.\"\"\"\n", "\n", "    def __call__(\n", "        self,\n", "        model_input: ModelInput,\n", "        retriever: DocumentIndex | None = None,\n", "        prompt_formatter: AbstractPromptFormatter | None = None,\n", "    ) -> ModelInput:\n", "        return model_input\n", "\n", "\n", "class RAGPreProcessor(BasicPreProcessor):\n", "    \"\"\".\"\"\"\n", "\n", "    retriever_top_k: int = 25\n", "\n", "    def __call__(\n", "        self,\n", "        model_input: ModelInput,\n", "        retriever: DocumentIndex | None = None,\n", "        prompt_formatter: AbstractPromptFormatter | None = None,\n", "    ) -> ModelInput:\n", "        model_input = copy.deepcopy(model_input)\n", "        if retriever is None or prompt_formatter is None:\n", "            raise ValueError(f\"The retriever or prompt_formatter is None.\")\n", "        model_input.retrieved_chunks, _ = retriever.query(\n", "            ModelInput(\n", "                prefix=model_input.prefix,\n", "                suffix=None,\n", "                path=model_input.path,\n", "            ),\n", "            top_k=self.retriever_top_k,\n", "        )\n", "        model_input, _ = filter_localctx_and_ground_truth_from_retrievals(\n", "            model_input, prompt_formatter\n", "        )\n", "        return model_input\n", "\n", "\n", "class AbstractPostProcessor(UISugar):\n", "    \"\"\"The Abstract PostProcessor for the system.\"\"\"\n", "\n", "    @abc.abstractmethod\n", "    def __call__(self, completion: str, model_input: ModelInput | None = None) -> str:\n", "        \"\"\".\"\"\"\n", "\n", "\n", "class BasicPostProcessor(AbstractPostProcessor):\n", "    \"\"\"The Basic PostProcessor for the system.\"\"\"\n", "\n", "    def __call__(self, completion: str, model_input: ModelInput | None = None) -> str:\n", "        return completion\n", "\n", "\n", "class TrimCompletion(AbstractPostProcessor):\n", "    def __call__(self, completion: str, model_input: ModelInput | None = None) -> str:\n", "        return trim_on_dedent(completion)\n", "\n", "\n", "class GeneralizedSystem(AbstractSystem[ModelInput]):\n", "    \"\"\"A Generalized Single Model-based System.\"\"\"\n", "\n", "    model: GenerativeLanguageModel\n", "    \"\"\"The LM model used to make prediction.\"\"\"\n", "\n", "    retriever: DocumentIndex | None = None\n", "    \"\"\"The retriever to retrieve the relevant code snippet.\"\"\"\n", "\n", "    preprocessor: AbstractPreProcessor\n", "\n", "    postprocessor: AbstractPostProcessor\n", "\n", "    generation_options: GenerationOptions\n", "\n", "    def __init__(\n", "        self,\n", "        model: GenerativeLanguageModel,\n", "        retriever: DocumentIndex | None = None,\n", "        preprocessor: AbstractPreProcessor = BasicPreProcessor(),\n", "        postprocessor: AbstractPostProcessor = BasicPostProcessor(),\n", "        generation_options: GenerationOptions = GenerationOptions(\n", "            max_generated_tokens=64\n", "        ),\n", "    ):\n", "        super().__init__()\n", "        self.model = model\n", "        self.retriever = retriever\n", "        self.preprocessor = preprocessor\n", "        self.postprocessor = postprocessor\n", "        self.generation_options = generation_options\n", "        self.__loaded = False\n", "\n", "    def load(self):\n", "        if not self.__loaded:\n", "            self.model.load()\n", "            if hasattr(self.retriever, \"scorer\"):\n", "                self.retriever.scorer.load()  # type: ignore\n", "        self.__loaded = True\n", "\n", "    def unload(self):\n", "        if self.__loaded:\n", "            self.model.unload()\n", "            if hasattr(self.retriever, \"scorer\"):\n", "                self.retriever.scorer.unload()  # type: ignore\n", "            self.__loaded = False\n", "\n", "    def add_docs(self, src_files: list[Document]):\n", "        if self.retriever is not None:\n", "            self.retriever.add_docs(src_files)\n", "\n", "    def clear_retriever(self):\n", "        \"\"\"Clear any stored documents from the retriever.\"\"\"\n", "        if self.retriever is not None:\n", "            self.retriever.remove_all_docs()\n", "\n", "    def generate(\n", "        self,\n", "        model_input: ModelInput,\n", "    ) -> tuple[str, list[int], list[Chunk]]:\n", "        \"\"\"Generate a completion.\"\"\"\n", "        model_input = self.preprocessor(\n", "            model_input, self.retriever, self.model.prompt_formatter\n", "        )\n", "        extra_out = ExtraGenerationOutputs()\n", "        generation = self.model.generate(\n", "            model_input, self.generation_options, extra_out\n", "        )\n", "        prompt_toks = extra_out.prompt_tokens\n", "        # generation = self._completion_postprocessing(generation)\n", "        return generation, prompt_toks, model_input.retrieved_chunks"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 8/8 [00:00<00:00, 63550.06it/s]\n", "INFO:augment.research.eval.harness.tasks.hydra_task:Creating random job id for hydra driver: eval-fvl75mzs\n"]}], "source": ["task = HydraTask()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_retriever\n", "from research.models.all_models import (\n", "    get_model,\n", "    GenerationOptions,\n", "    ExtraGenerationOutputs,\n", ")\n", "\n", "retriever_config = {\n", "    \"name\": \"diff_boykin\",\n", "    \"chunker\": \"line_level\",\n", "    \"max_chunk\": 40,\n", "    \"max_query_lines\": 10,\n", "}\n", "retriever = create_retriever(retriever_config)\n", "# model = get_model(\"remote\", url=\"http://**************:5002\")\n", "model = get_model(\"starcoderbase\")\n", "basic_system = GeneralizedSystem(\n", "    model, None, preprocessor=BasicPreProcessor(), postprocessor=BasicPostProcessor()\n", ")\n", "rag_system = GeneralizedSystem(\n", "    model, retriever, preprocessor=RAGPreProcessor(), postprocessor=TrimCompletion()\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.from_ymls() [PosixPath('/home/<USER>/checkpoints/starcoderbase_neox/config.yml')]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:NeoXArgs.calculate_derived() Total number of GPUs determined to be: 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building HFGPT2Tokenizer tokenizer ...\n", " > padded vocab (size: 49153) with 2047 dummy tokens (new size: 51200)\n", "Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "> initializing torch distributed ...\n", "[2023-08-17 01:29:33,157] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2023-08-17 01:29:33,315] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=**************, master_port=6001\n", "[2023-08-17 01:29:33,317] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[dxy-a40-dev:48140] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:1 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:1 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:2 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:2 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:3 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:3 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:4 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:4 with 1 nodes.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2023-08-17 01:29:33,326] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/src/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/src/augment/research/gpt-neox/megatron/data'\n", "building GPT2 model ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:5 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:5 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:6 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:6 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:7 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:7 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:8 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:8 with 1 nodes.\n", "INFO:torch.distributed.distributed_c10d:Added key: store_based_barrier_key:9 to store for rank: 0\n", "INFO:torch.distributed.distributed_c10d:Rank 0: Completed store-based barrier for key:store_based_barrier_key:9 with 1 nodes.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2023-08-17 01:29:33,412] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=45\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: ParallelTransformerLayerPipe\n", "    23: ParallelTransformerLayerPipe\n", "    24: ParallelTransformerLayerPipe\n", "    25: ParallelTransformerLayerPipe\n", "    26: ParallelTransformerLayerPipe\n", "    27: ParallelTransformerLayerPipe\n", "    28: ParallelTransformerLayerPipe\n", "    29: ParallelTransformerLayerPipe\n", "    30: ParallelTransformerLayerPipe\n", "    31: ParallelTransformerLayerPipe\n", "    32: ParallelTransformerLayerPipe\n", "    33: ParallelTransformerLayerPipe\n", "    34: ParallelTransformerLayerPipe\n", "    35: ParallelTransformerLayerPipe\n", "    36: ParallelTransformerLayerPipe\n", "    37: ParallelTransformerLayerPipe\n", "    38: ParallelTransformerLayerPipe\n", "    39: ParallelTransformerLayerPipe\n", "    40: ParallelTransformerLayerPipe\n", "    41: ParallelTransformerLayerPipe\n", "    42: _post_transformer_block\n", "    43: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    44: ParallelLinearPipe\n", "  loss: partial\n", "DeepSpeed is enabled.\n", "[2023-08-17 01:29:34,091] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+ea3711b, git-hash=ea3711b, git-branch=HEAD\n", "[2023-08-17 01:29:34,092] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n", "[2023-08-17 01:29:34,249] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2023-08-17 01:29:34,250] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2023-08-17 01:29:34,251] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2023-08-17 01:29:34,251] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2023-08-17 01:29:34,252] [INFO] [config.py:763:print]   amp_enabled .................. <PERSON>alse\n", "[2023-08-17 01:29:34,252] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2023-08-17 01:29:34,252] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2023-08-17 01:29:34,253] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2023-08-17 01:29:34,253] [INFO] [config.py:763:print]   disable_allgather ............ False\n", "[2023-08-17 01:29:34,254] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2023-08-17 01:29:34,254] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4294967296, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2023-08-17 01:29:34,255] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2023-08-17 01:29:34,255] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2023-08-17 01:29:34,255] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2023-08-17 01:29:34,256] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2023-08-17 01:29:34,256] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2023-08-17 01:29:34,257] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2023-08-17 01:29:34,257] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2023-08-17 01:29:34,257] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2023-08-17 01:29:34,258] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4294967296\n", "[2023-08-17 01:29:34,258] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2023-08-17 01:29:34,259] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2023-08-17 01:29:34,259] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2023-08-17 01:29:34,259] [INFO] [config.py:763:print]   optimizer_name ............... None\n", "[2023-08-17 01:29:34,260] [INFO] [config.py:763:print]   optimizer_params ............. None\n", "[2023-08-17 01:29:34,261] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2023-08-17 01:29:34,262] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2023-08-17 01:29:34,262] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2023-08-17 01:29:34,262] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2023-08-17 01:29:34,263] [INFO] [config.py:763:print]   prescale_gradients ........... <PERSON>alse\n", "[2023-08-17 01:29:34,263] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2023-08-17 01:29:34,264] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2023-08-17 01:29:34,264] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2023-08-17 01:29:34,265] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2023-08-17 01:29:34,265] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2023-08-17 01:29:34,265] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2023-08-17 01:29:34,266] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2023-08-17 01:29:34,267] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2023-08-17 01:29:34,267] [INFO] [config.py:763:print]   train_batch_size ............. 1\n", "[2023-08-17 01:29:34,269] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  1\n", "[2023-08-17 01:29:34,269] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2023-08-17 01:29:34,270] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2023-08-17 01:29:34,270] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2023-08-17 01:29:34,271] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2023-08-17 01:29:34,272] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2023-08-17 01:29:34,272] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2023-08-17 01:29:34,272] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 1, \n", "    \"train_micro_batch_size_per_gpu\": 1, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/distributed/distributed_c10d.py:552: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n", "Using /home/<USER>/.cache/torch_extensions/py39_cu118 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py39_cu118/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.26479339599609375 seconds\n", "[2023-08-17 01:29:34,874] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=1\n", "[2023-08-17 01:29:34,915] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=45 [0, 45) STAGE_PARAMS=15844612097 (15844.612M) TOTAL_PARAMS=15844612097 (15844.612M) UNIQUE_PARAMS=15844612097 (15844.612M)\n", " > number of parameters on model parallel rank 0: 15844612097\n", " > total params: 15,844,612,097\n", " > embedding params: 629,145,600\n", "Loading: /home/<USER>/checkpoints/starcoderbase_neox\n", "[2023-08-17 01:29:34,957] [INFO] [engine.py:1551:_load_checkpoint] rank: 0 loading checkpoint: /home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/mp_rank_00_model_states.pt\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2023-08-17 01:29:35,365] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_00-model_00-model_states.pt\n", "[2023-08-17 01:29:35,847] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_02-model_00-model_states.pt\n", "[2023-08-17 01:29:36,323] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_03-model_00-model_states.pt\n", "[2023-08-17 01:29:36,774] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_04-model_00-model_states.pt\n", "[2023-08-17 01:29:37,249] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_05-model_00-model_states.pt\n", "[2023-08-17 01:29:37,697] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_06-model_00-model_states.pt\n", "[2023-08-17 01:29:38,176] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_07-model_00-model_states.pt\n", "[2023-08-17 01:29:38,623] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_08-model_00-model_states.pt\n", "[2023-08-17 01:29:39,089] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_09-model_00-model_states.pt\n", "[2023-08-17 01:29:39,546] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_10-model_00-model_states.pt\n", "[2023-08-17 01:29:40,024] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_11-model_00-model_states.pt\n", "[2023-08-17 01:29:40,487] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_12-model_00-model_states.pt\n", "[2023-08-17 01:29:40,919] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_13-model_00-model_states.pt\n", "[2023-08-17 01:29:41,397] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_14-model_00-model_states.pt\n", "[2023-08-17 01:29:41,860] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_15-model_00-model_states.pt\n", "[2023-08-17 01:29:42,351] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_16-model_00-model_states.pt\n", "[2023-08-17 01:29:42,819] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_17-model_00-model_states.pt\n", "[2023-08-17 01:29:43,264] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_18-model_00-model_states.pt\n", "[2023-08-17 01:29:43,714] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_19-model_00-model_states.pt\n", "[2023-08-17 01:29:44,187] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_20-model_00-model_states.pt\n", "[2023-08-17 01:29:44,655] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_21-model_00-model_states.pt\n", "[2023-08-17 01:29:45,120] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=22 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_22-model_00-model_states.pt\n", "[2023-08-17 01:29:45,577] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_23-model_00-model_states.pt\n", "[2023-08-17 01:29:46,038] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_24-model_00-model_states.pt\n", "[2023-08-17 01:29:46,490] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=25 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_25-model_00-model_states.pt\n", "[2023-08-17 01:29:46,961] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=26 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_26-model_00-model_states.pt\n", "[2023-08-17 01:29:47,455] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=27 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_27-model_00-model_states.pt\n", "[2023-08-17 01:29:48,003] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=28 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_28-model_00-model_states.pt\n", "[2023-08-17 01:29:48,564] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=29 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_29-model_00-model_states.pt\n", "[2023-08-17 01:29:49,121] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=30 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_30-model_00-model_states.pt\n", "[2023-08-17 01:29:49,676] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=31 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_31-model_00-model_states.pt\n", "[2023-08-17 01:29:50,222] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=32 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_32-model_00-model_states.pt\n", "[2023-08-17 01:29:50,785] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=33 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_33-model_00-model_states.pt\n", "[2023-08-17 01:29:51,347] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=34 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_34-model_00-model_states.pt\n", "[2023-08-17 01:29:51,908] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=35 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_35-model_00-model_states.pt\n", "[2023-08-17 01:29:52,478] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=36 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_36-model_00-model_states.pt\n", "[2023-08-17 01:29:53,033] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=37 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_37-model_00-model_states.pt\n", "[2023-08-17 01:29:53,593] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=38 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_38-model_00-model_states.pt\n", "[2023-08-17 01:29:54,141] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=39 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_39-model_00-model_states.pt\n", "[2023-08-17 01:29:54,684] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=40 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_40-model_00-model_states.pt\n", "[2023-08-17 01:29:55,225] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=41 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_41-model_00-model_states.pt\n", "[2023-08-17 01:29:55,227] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=43 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_43-model_00-model_states.pt\n", "[2023-08-17 01:29:55,649] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=44 file=/home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/layer_44-model_00-model_states.pt\n", "checkpoint_name: /home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/mp_rank_00_model_states.pt\n", " > could not find arguments in the checkpoint for validation...\n", "  successfully loaded /home/<USER>/checkpoints/starcoderbase_neox/checkpoint-mps1/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Model loaded in 22.9 seconds\n"]}], "source": ["basic_system.load()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Finish the generation in 3.8 seconds\n"]}], "source": ["inputs, repo = task[0]\n", "\n", "outputs = basic_system.generate(inputs)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:SUCCESS: created ConfigMap hydra-driver-3dgx3tjz-local-cfg-7hakkqhk.\n", "INFO:root:SUCCESS: created Pod hydra-driver-3dgx3tjz-local-pod-5dgoiczg.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/0                                   \u001b[31mFAILED\u001b[0m     wall_time=10.06     test_time=10.06\n"]}], "source": ["completion = outputs[0]\n", "# show_completion(inputs.prefix, inputs.suffix, completion)\n", "results = task.execute(inputs, completion)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'pass': False}\n"]}], "source": ["print(results)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:SUCCESS: created ConfigMap hydra-driver-3dgx3tjz-local-cfg-j6yzj4bp.\n", "INFO:root:SUCCESS: created Pod hydra-driver-3dgx3tjz-local-pod-7ugvdi5a.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["CarperAI_trlx/0                                   \u001b[31mFAILED\u001b[0m     wall_time=11.79     test_time=11.79\n"]}], "source": ["results = task.execute(inputs, completion)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "config_map\n", "hydra-driver-3dgx3tjz-local-cfg-j6yzj4bp\n", "----------------------------------------------------------------------------------------------------\n", "patch_json\n", "{\"file_content\": \"import random\\nimport sys\\nfrom abc import abstractmethod, abstractstaticmethod\\nfrom typing import Any, Callable, Dict, Iterable\\n\\nfrom torch.utils.data import DataLoader, Dataset\\n\\nfrom trlx.data import GeneralElement, RLElement\\n\\n# specifies a dictionary of architectures\\n_DATAPIPELINE: Dict[str, any] = {}  # registry\\n\\n\\ndef register_datapipeline(name):\\n    \\\"\\\"\\\"Decorator used register a CARP architecture\\n    Args:\\n        name: Name of the architecture\\n    \\\"\\\"\\\"\\n\\n    def register_class(cls, name):\\n        _DATAPIPELINE[name] = cls\\n        setattr(sys.modules[__name__], name, cls)\\n        return cls\\n\\n    if isinstance(name, str):\\n        name = name.lower()\\n        return lambda c: register_class(c, name)\\n\\n    cls = name\\n    name = cls.__name__\\n    register_class(cls, name.lower())\\n\\n    return cls\\n\\n\\n@register_datapipeline\\nclass BasePipeline(Dataset):\\n    def __init__(self, path: str = \\\"dataset\\\"):\\n        super().__init__()\\n\\n    @abstractmethod\\n    def __getitem__(self, index: int) -> GeneralElement:\\n        pass\\n\\n    @abstractmethod\\n    def __len__(self) -> int:\\n        pass\\n\\n    @abstractmethod\\n    def create_loader(\\n        self,\\n        batch_size: int,\\n        shuffle: bool,\\n        prep_fn: Callable = None,\\n        num_workers: int = 0,\\n    ) -> DataLoader:\\n        \\\"\\\"\\\"\\n        Create a dataloader for the pipeline\\n\\n        :param prep_fn: Typically a tokenizer. Applied to GeneralElement after collation.\\n        \\\"\\\"\\\"\\n        pass\\n\\n\\nclass BaseRolloutStore(Dataset):\\n    def __init__(self, capacity=-1):\\n        self.history: Iterable[Any] = None\\n        self.capacity = capacity\\n\\n    @abstractmethod\\n    def push(self, exps: Iterable[Any]):\\n        \\\"\\\"\\\"\\n        Push experiences to rollout storage\\n        \\\"\\\"\\\"\\n        pass\\n\\n    def __getitem__(self, index: int) -> RLElement:\\n        return self.history[index]\\n\\n    def __len__(self) -> int:\\n        return len(self.history)\\n\\n    @abstractmethod\\n    def create_loader(\\n        self,\\n        batch_size: int,\\n        shuffle: bool,\\n        prep_fn: Callable = None,\\n        num_workers: int = 0,\\n    ) -> DataLoader:\\n        \\\"\\\"\\\"\\n        Create a dataloader for the rollout store\\n\\n        :param prep_fn: Applied to RLElement after collation (typically tokenizer)\\n        :type prep_fn: Callable\\n        \\\"\\\"\\\"\\n        pass\\n\", \"char_start\": 460, \"char_end\": 800, \"patch_content\": \"    def decorator(cls):\\n        _DATAPIPELINE[name] = cls\\n        return cls\\n\\n    return decorator\\n\\n\\ndef get_datapipeline(name):\\n    \\\"\\\"\\\"Returns a CARP architecture by name\\n    Args:\\n        name: Name of the architecture\\n    \\\"\\\"\\\"\\n    if name not in _DATAPIPELINE\", \"patch_id\": \"CarperAI_trlx/0\", \"repository\": \"CarperAI/trlx\", \"commit_sha\": \"\", \"file_name\": \"trlx/pipeline/__init__.py\", \"_extra\": {}}\n", "----------------------------------------------------------------------------------------------------\n", "result_str\n", "CarperAI_trlx/0                                   \u001b[31mFAILED\u001b[0m     wall_time=11.79     test_time=11.79\n", "----------------------------------------------------------------------------------------------------\n", "run_output\n", "total 0\n", "drwxrwxrwx 3 <USER> <GROUP> 101 Aug 17 01:30 .\n", "drwxr-xr-x 1 <USER> <GROUP>  57 Aug 17 01:30 ..\n", "drwxr-xr-x 2 <USER> <GROUP>  48 Aug 17 01:30 ..2023_08_17_01_30_45.073030592\n", "lrwxrwxrwx 1 root root  31 Aug 17 01:30 ..data -> ..2023_08_17_01_30_45.073030592\n", "lrwxrwxrwx 1 root root  17 Aug 17 01:30 patch.json -> ..data/patch.json\n", "lrwxrwxrwx 1 root root  23 Aug 17 01:30 patch_and_run.py -> ..data/patch_and_run.py\n", "repo_name=CarperAI_trlx\n", "file_name=trlx/pipeline/__init__.py\n", "Running pytest...\n", "----------------------------------------\n", "#!/bin/bash\n", "\n", "set -e\n", "python -m pytest -x -k \"not test_models and not test_hf_attr_getters and not test_parse_delta_kwargs\"\n", "\n", "============================= test session starts ==============================\n", "platform linux -- Python 3.9.17, pytest-7.4.0, pluggy-1.2.0\n", "rootdir: /code\n", "plugins: cov-4.1.0, xdist-3.3.1, torchtyping-0.1.4, typeguard-4.0.0\n", "collected 0 items / 1 error\n", "\n", "==================================== ERRORS ====================================\n", "____________________ ERROR collecting tests/test_configs.py ____________________\n", "/home/<USER>/.local/lib/python3.9/site-packages/_pytest/python.py:617: in _importtestmodule\n", "    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)\n", "/home/<USER>/.local/lib/python3.9/site-packages/_pytest/pathlib.py:565: in import_path\n", "    importlib.import_module(module_name)\n", "/usr/local/lib/python3.9/importlib/__init__.py:127: in import_module\n", "    return _bootstrap._gcd_import(name[level:], package, level)\n", "<frozen importlib._bootstrap>:1030: in _gcd_import\n", "    ???\n", "<frozen importlib._bootstrap>:1007: in _find_and_load\n", "    ???\n", "<frozen importlib._bootstrap>:986: in _find_and_load_unlocked\n", "    ???\n", "<frozen importlib._bootstrap>:680: in _load_unlocked\n", "    ???\n", "/home/<USER>/.local/lib/python3.9/site-packages/_pytest/assertion/rewrite.py:178: in exec_module\n", "    exec(co, module.__dict__)\n", "tests/test_configs.py:4: in <module>\n", "    from trlx.data.configs import TRLConfig\n", "trlx/__init__.py:1: in <module>\n", "    from .trlx import train\n", "trlx/trlx.py:7: in <module>\n", "    from trlx.utils.loading import get_pipeline, get_trainer\n", "trlx/utils/loading.py:4: in <module>\n", "    from trlx.pipeline import _DATAPIPELINE\n", "E     File \"/code/trlx/pipeline/__init__.py\", line 32\n", "E       if name not in _DATAPIPELINE\n", "E                                   ^\n", "E   SyntaxError: invalid syntax\n", "------------------------------- Captured stdout --------------------------------\n", "/home/<USER>/.local/lib/python3.9/site-packages/bitsandbytes/libbitsandbytes_cpu.so: undefined symbol: cadam32bit_grad_fp32\n", "[2023-08-17 01:30:58,517] [INFO] [real_accelerator.py:133:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "=============================== warnings summary ===============================\n", "../home/<USER>/.local/lib/python3.9/site-packages/bitsandbytes/cextension.py:34\n", "  /home/<USER>/.local/lib/python3.9/site-packages/bitsandbytes/cextension.py:34: UserWarning: The installed version of bitsandbytes was compiled without GPU support. 8-bit optimizers, 8-bit multiplication, and GPU quantization are unavailable.\n", "    warn(\"The installed version of bitsandbytes was compiled without GPU support. \"\n", "\n", "-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n", "=========================== short test summary info ============================\n", "ERROR tests/test_configs.py\n", "!!!!!!!!!!!!!!!!!!!!!!!!!! stopping after 1 failures !!!!!!!!!!!!!!!!!!!!!!!!!!!\n", "========================= 1 warning, 1 error in 6.32s ==========================\n", "\n", "----------------------------------------\n", "\n", "CarperAI_trlx/0                                   \u001b[31mFAILED\u001b[0m     wall_time=11.79     test_time=11.79     \n", "\n"]}], "source": ["for key in results:\n", "    print(\"-\" * 100)\n", "    print(key)\n", "    print(results[key])"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["['FAILED']"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["import re\n", "\n", "def extract_colored_parts(s: str) -> list[str]:\n", "    # The regular expression matches the ANSI start sequence, captures the colored text, and then matches the ANSI end sequence.\n", "    pattern = re.compile(r'\\033\\[\\d+m(.*?)\\033\\[0m')\n", "    matches = pattern.findall(s)\n", "    return matches[0]\n", "\n", "extract_colored_parts(results['result_str'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(basic_system)\n", "basic_system.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input, repo = task[0]\n", "extra_output = ExtraGenerationOutputs()\n", "outputs = model.generate(\n", "    input, GenerationOptions(temperature=0, max_generated_tokens=256), extra_output\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_completion(input.prefix, input.suffix, outputs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_ground_truths = []\n", "for input, repo in task:\n", "    ground_truth = input.extra[\"ground_truth\"]\n", "    all_ground_truths.append(ground_truth)\n", "print(len(all_ground_truths))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}