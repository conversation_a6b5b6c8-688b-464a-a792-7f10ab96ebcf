{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "from base.languages.language_guesser import guess_language\n", "from base.languages.unit_test_guesser import is_unit_test, pattern_list_by_langid\n", "\n", "# path = \"foo.test.ts\"\n", "# path = 'a/unit-test/x.py'\n", "path = 'src/commands/generateTest/generateTestAction.ts'\n", "lang = guess_language(path)\n", "\n", "patterns = pattern_list_by_langid[lang]\n", "for pattern in patterns:\n", "    print(f\"Pattern: {pattern}\")\n", "    print(f\"Matches: {re.match(pattern, path)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(type(patterns[0]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pattern = r\"(^|.*/)[a-zA-Z0-9]+Test[A-Z0-9]+(\\.ts|\\.tsx)$\"\n", "pattern = r\"(^|.*/)[a-zA-Z0-9]+Test[A-Z0-9]+(.ts|.tsx)$\"\n", "pattern = r\"(^|.*/)[a-zA-Z0-9]+Test[A-Za-z]+(.ts|.tsx)$\"\n", "path = 'src/commands/generateTest/generateTestAction.ts'\n", "print(f\"Path: {path}\")\n", "print(f\"Path: {path.encode()}\")\n", "print(f\"Pattern: {pattern}\")\n", "print(f\"Matches: {re.match(pattern, path)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.cceval import CCEval\n", "\n", "task = CCEval()\n", "print(f\"There are {len(task)} samples in the task\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["is_unit_tests = 0\n", "not_unit_tests_but_has_teststr = []\n", "for index in range(len(task)):\n", "    sample, _ = task[index]\n", "    # print(f\"Sample {index}: {sample}\")\n", "    if is_unit_test(sample.path):\n", "        is_unit_tests += 1\n", "    elif \"test\" in sample.path or \"Test\" in sample.path or \"TEST\" in sample.path:\n", "        not_unit_tests_but_has_teststr.append(sample.path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Found {is_unit_tests} / {len(task)} unit tests.\")\n", "print(f\"Captured {is_unit_tests} / {len(not_unit_tests_but_has_teststr) + is_unit_tests} unit tests.\")\n", "print(f\"There are probably {len(not_unit_tests_but_has_teststr)} / {len(not_unit_tests_but_has_teststr) + is_unit_tests} outliers\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(not_unit_tests_but_has_teststr[0])\n", "print(not_unit_tests_but_has_teststr[1])\n", "print(not_unit_tests_but_has_teststr[2])\n", "print(not_unit_tests_but_has_teststr[3])\n", "print(not_unit_tests_but_has_teststr[10])\n", "print(not_unit_tests_but_has_teststr[21])\n", "print(not_unit_tests_but_has_teststr[35])\n", "print(not_unit_tests_but_has_teststr[-1])\n", "print(not_unit_tests_but_has_teststr[-10])\n", "print(not_unit_tests_but_has_teststr[-20])\n", "print(not_unit_tests_but_has_teststr[-25])\n", "print(not_unit_tests_but_has_teststr[-30])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}