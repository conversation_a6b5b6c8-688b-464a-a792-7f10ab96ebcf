{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Protopyting `research_models` and `hydra`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "import sys\n", "import threading\n", "import zstandard as zstd\n", "from pathlib import Path\n", "\n", "from research.eval import hydra\n", "from research.eval import patch_lib\n", "from research import core\n", "from research.models import core as models"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load `research_models`'s checkpoint"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["checkpoints_root = \"/mnt/efs/augment/checkpoints\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["model = models.StarCoderBase(checkpoints_root)\n", "model.load()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Load data to generate completions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["INP_PATH = \"/mnt/efs/augment/data/processed/repo_coder.v1/patches/function_level_completion_patches.test.jsonl.zst\"\n", "\n", "with zstd.open(INP_PATH, \"rb\") as file:\n", "    ALL_LINES = file.read().decode(\"utf-8\").split(\"\\n\")\n", "ALL_LINES = [line.strip() for line in ALL_LINES if line.strip()]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["patch_json = ALL_LINES[0]\n", "patch = patch_lib.Patch.from_json(patch_json)\n", "print(f\"\\rGenerate completion for {patch.patch_id}.\")\n", "prefix = patch.file_content[:patch.char_start]\n", "suffix = patch.file_content[patch.char_end:]\n", "gen_limit = 128\n", "len_limit = model.seq_length - gen_limit\n", "\n", "input = core.ModelInput(prefix=prefix, suffix=suffix)\n", "\n", "print(input)\n", "\n", "completion = model.generate(\n", "        input,\n", "        models.GenerationOptions(temperature=0., max_generated_tokens=128))\n", "print(completion)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "print(colored(input.prefix, color=\"blue\"), end=\"\")\n", "print(colored(completion, color=\"green\"), end=\"\")\n", "print(colored(input.suffix, color=\"blue\"), end=\"\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Creates a `hydra Driver`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["HYDRA_DRIVER_NAME = \"hieu-dev-research-model\"\n", "HYDRA_LOCAL_TIMEOUT_SECS = 30\n", "HYDRA_GLOBAL_TIMEOUT_SECS = 180\n", "HYDRA_NUM_PATCHES_LIMIT = 10\n", "\n", "driver = hydra.Driver(\n", "    driver_name=HYDRA_DRIVER_NAME,\n", "    local_timeout_secs=HYDRA_LOCAL_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_GLOBAL_TIMEOUT_SECS,\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Now's time for business!\n", "\n", "In each step of the following `for` loop:\n", "\n", "  * The research model receives a `patch_json` and generates a `completion`.\n", "\n", "  * The `completion` is sent to the Hydra `Driver` to run and evaluate for PASSED/FAILED via `driver.dispatch`.\n", "    \n", "    - Note that all the `dispatch` are done in parallel to utilize all the parallel features of Hydra's driver.\n", "\n", "    - But the completion generations are done sequentially, since we don't expect `model` to handle multiple requests at once (or do we?)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["driver.reset()\n", "\n", "dispatch_threads = []\n", "\n", "for idx, patch_json in enumerate(ALL_LINES[:HYDRA_NUM_PATCHES_LIMIT]):\n", "    patch = patch_lib.Patch.from_json(patch_json)\n", "    print(f\"{idx:04d} Generate completion for {patch.patch_id}.\")\n", "    prefix = patch.file_content[:patch.char_start]\n", "    suffix = patch.file_content[patch.char_end:]\n", "    gen_limit = 128\n", "    len_limit = model.seq_length - gen_limit\n", "    prefix = prefix[-len_limit:] if len(prefix) > len_limit else prefix\n", "    completion = model.generate(\n", "        core.ModelInput(prefix=prefix),\n", "        models.GenerationOptions(temperature=0., max_generated_tokens=gen_limit))\n", "    if completion is None:\n", "        print(colored(prefix, color=\"blue\"))\n", "    # print(f\"completion length: {len(completion)}\\n\")\n", "    patch = patch.with_patch_content(completion)\n", "\n", "    thread = threading.Thread(target=driver.dispatch, args=(patch,))\n", "    # thread.start()\n", "    dispatch_threads.append(thread)\n", "\n", "\n", "# for thread in dispatch_threads:\n", "#     thread.join()\n", "# driver.wait_until_finish()\n", "# driver.collect_result()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["completion = model.generate(\n", "        core.ModelInput(prefix=prefix[:50]),\n", "        models.GenerationOptions(temperature=0., max_generated_tokens=gen_limit))\n", "print(completion)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}