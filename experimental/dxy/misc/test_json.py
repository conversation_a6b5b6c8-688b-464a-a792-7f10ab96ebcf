#!/usr/bin/env python
# python experimental/dxy/misc/test_json.py
import json
from dataclasses import dataclass

from dataclasses_json import dataclass_json

from base.fastforward import cached_attention


@dataclass_json
@dataclass
class ModelArch:
    """Model architecture parameters."""

    arch_type: str

    # number of layers
    num_layers: int

    # size of the vocab
    vocab_size: int

    # dimension of the embeddings layer
    emb_dim: int

    # number of heads
    num_heads: int

    # dimension per head
    head_dim: int

    # rotary embedding percentage
    rotary_pct: float = 0

    # For RMS norm.
    norm_eps: float = 1e-5

    # Used for GQA / MQA models.
    num_queries_per_head: int = 1

    # MLP's hidden_dim must be divisible by this number.
    mlp_dim_divisible_by: int = 256

    # MLP's hidden_dim are expanded this number.
    ffn_dim_multiplier: float = 1.0

    # Tensor parallelism in attention layers. None means to not use.
    attn_split_head_mode: cached_attention.SplitHeadModes = (
        cached_attention.SplitHeadModes.NO_SPLIT
    )


if __name__ == "__main__":
    model_arch = {
        "arch_type": "LLAMA",
        "num_layers": 24,
        "vocab_size": 32256,
        "emb_dim": 2048,
        "num_heads": 16,
        "head_dim": 128,
        "num_queries_per_head": 1,
        "mlp_dim_divisible_by": 128,
        "ffn_dim_multiplier": 1.0,
        "attn_split_head_mode": 1,
    }
    json_str = json.dumps(model_arch, indent=2)

    x = ModelArch.from_json(json_str)  # type: ignore
    print(x)
    print("-")
