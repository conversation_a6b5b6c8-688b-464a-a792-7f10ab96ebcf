"""
torchrun --nproc_per_node=2 test_rpc.py
"""
import os

import torch.distributed.rpc as rpc


def worker_task(msg):
    real_rank = rpc.get_worker_info().id
    print(f"Worker {real_rank} received message: {msg}")
    # Perform some task here and return a response
    response = f"Worker {real_rank} has completed the task."
    return response


def server_task(rank, world_size):
    # Server sends a message to all workers to perform some task
    futures = []
    for worker_rank in range(0, world_size):
        worker_name = f"worker{worker_rank}"
        # Server sends an asynchronous RPC to each worker
        fut = rpc.rpc_async(
            worker_name, worker_task, args=(f"Execute this task-{worker_rank}",)
        )
        futures.append(fut)

    # Collect all responses from workers
    for fut in futures:
        response = fut.wait()
        print(response)


def run_worker(rank, world_size):
    rpc.init_rpc(name=f"worker{rank}", rank=rank, world_size=world_size)

    if rank == 0:
        server_task(rank, world_size)
    else:
        # Worker processes simply wait for the server to send them tasks
        pass

    # Finish up RPC
    rpc.shutdown()


if __name__ == "__main__":
    rank = int(os.environ["RANK"])
    world_size = int(os.environ["WORLD_SIZE"])
    run_worker(rank, world_size)
