"""Test multiple processes.

python process.py
DXY_NUM_PROCESS=1 DXY_PROCESS_INDEX=0 python process.py
DXY_NUM_PROCESS=2 DXY_PROCESS_INDEX=0 python process.py ; DXY_NUM_PROCESS=2 DXY_PROCESS_INDEX=1 python process.py
"""


def main():
    import os
    import time

    import torch

    num_process = os.environ.get("DXY_NUM_PROCESS", None)
    process_index = os.environ.get("DXY_PROCESS_INDEX", None)
    os.environ["CUDA_VISIBLE_DEVICES"] = str(process_index)
    print(f"num_process={num_process}, process_index={process_index}")
    tensor_per_shard = torch.randint(0, 10, (4,))
    print(f"tensor_per_shard={tensor_per_shard}")
    tensor_per_shard = tensor_per_shard.cuda()
    # all gather
    tensors = torch.distributions.all_gather(tensor_per_shard)
    time.sleep(60)


if __name__ == "__main__":
    main()
