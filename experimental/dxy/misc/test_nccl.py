import torch.distributed as dist


def init_process(rank, world_size):
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    # Perform some dummy operation
    tensor = torch.zeros(1).cuda(rank)
    dist.all_reduce(tensor)
    print(f"Rank {rank}/{world_size} has tensor {tensor}")


if __name__ == "__main__":
    world_size = 4
    for rank in range(world_size):
        init_process(rank, world_size)
