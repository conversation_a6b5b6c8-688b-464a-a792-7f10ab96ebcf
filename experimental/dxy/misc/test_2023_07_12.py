import argparse
import os
import pdb
from collections import OrderedDict
from textwrap import dedent

import numpy as np
import torch
from termcolor import colored

from research.models.all_models import (
    CodeGen_2B_Multi,
    CodeGen_16B_Indiana,
    CodeGen_16B_Multi,
    CodeGen_350M_Multi,
    OpenAI_Model,
    RemoteModel,
    StarCoder_Base,
    StarCoder_Model,
)
from research.models.model_server import add_files_to_index
from research.retrieval.chunking_functions import (
    LineLevelChunker,
    ScopeAwareChunker,
)
from research.retrieval.scorers.good_enough_bm25_scorer import (
    GoodEnoughBM25Scorer,
)
from research.retrieval.types import Chunk, Document
from research.retrieval.retrieval_database import RetrievalDatabase


def get_model(name: str, retrieval_top_k: int = 3):
    if name in ("starcoder_base_neox", "sc_base"):
        model = StarCoder_Base("/home/<USER>/ckps", retrieval_top_k)
    elif name in ("starcoder_base_ori", "sc_base_ori"):
        model = StarCoder_Model("/mnt/efs/augment/checkpoints", retrieval_top_k)
    else:
        raise ValueError(f"Unknown name: {name}")
    return model


if __name__ == "__main__":
    parser = argparse.ArgumentParser("Test different models with simple examples")
    parser.add_argument(
        "--name",
        type=str,
        choices=["sc_base", "starcoder_base_neox", "starcoder_base_ori", "sc_base_ori"],
        help="The model names.",
    )
    args = parser.parse_args()
    model = get_model(args.name)

    model.load()

    prompt = dedent(
        """
        def hello_world():
    """
    )
    print(f"Model Name: {args.name}\n")
    generated_text = model.generate(prefix=prompt, temperature=0)
    print(colored(prompt, color="red"), end="")
    print(colored(generated_text, color="green"))

    import pdb

    pdb.set_trace()

    print("-")
