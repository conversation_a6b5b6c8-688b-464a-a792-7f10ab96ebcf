"""Simple script to profile <PERSON>AM<PERSON>.

Usage:

python profile_llama.py --model_size llama-350m --q_len 512 --kv_len 4096 --fp8
python profile_llama.py --model_size llama-350m --q_len 512 --kv_len 4096 --fp8 --cuda_graphs

To obtain NSYS profile:

nsys profile --output="/mnt/efs/augment/user/dxy/profile/llama3/Q32-KV4096-4GPUs" --force-overwrite=true \
    python experimental/dxy/profile_llama/llama3.py --q_len 32 --kv_len 4096 --gpus 4

nsys profile --output="/mnt/efs/augment/user/dxy/profile/llama3-proxy/Q32-KV4096-4GPUs" --force-overwrite=true \
    python experimental/dxy/profile_llama/llama3.py --proxy --q_len 32 --kv_len 4096 --gpus 4

nsys profile --output="/mnt/efs/augment/user/dxy/profile/llama3-proxy/Q32-KV4096-1GPUs-CG" --force-overwrite=true --cuda-graph-trace=node \
    python experimental/dxy/profile_llama/llama3.py --proxy --cuda_graphs --q_len 32 --kv_len 4096 --gpus 1

nsys profile --output="/mnt/efs/augment/user/dxy/profile/llama3-proxy/Q32-KV4096-2GPUs-CG" --force-overwrite=true --cuda-graph-trace=node --gpu-metrics-device=all \
    python experimental/dxy/profile_llama/llama3.py --proxy --q_len 32 --kv_len 4096 --gpus 2
"""

import argparse
import sys
import random
import torch

from base.fastforward import (
    fwd,
    fwd_llama,
    fwd_llama_fp8,
    model_specs,
)
from research.core import utils_for_log
from base.fastforward.torch_utils import cuda_timeit


def run_profile(
    step_fn: fwd.ForwardStepFn,
    attention_factory: fwd_llama.LlamaAttentionFactory,
    q_len: int,
    kv_len: int,
    vocab_size: int,
):
    attn = attention_factory(q_len + kv_len)

    # prefill cache
    remaining_prefill = kv_len
    prefilled = 0
    while remaining_prefill > q_len:
        tokens = [random.randint(0, vocab_size - 1) for _ in range(q_len)]
        torch.cuda.nvtx.range_push("measure_this")
        step_fn(tokens, attn)
        torch.cuda.nvtx.range_pop()
        remaining_prefill -= q_len
        prefilled += q_len

    tokens = [0] * q_len

    def do_step():
        attn.reset(to_position=prefilled)  # noqa
        _ = step_fn(tokens, attn)  # noqa

    median_latency_ms, _ = cuda_timeit(do_step, warmup_steps=5)
    print(
        'dtype="fp8"\t'
        f"q={q_len:<7d}"
        f"kv={kv_len:<7d}"
        f"median_latency_ms={median_latency_ms:0.5f}",
        flush=True,
    )

    torch.cuda.synchronize()
    del step_fn, attn
    torch.cuda.empty_cache()


def main():
    parser = argparse.ArgumentParser(description="Simple script to profile llama3.")
    parser.add_argument(
        "--cuda_graphs", dest="cuda_graphs", default=False, action="store_true"
    )
    parser.add_argument("--proxy", dest="proxy", default=False, action="store_true")
    parser.add_argument("--q_len", type=int, required=True)
    parser.add_argument("--kv_len", type=int, required=True)
    parser.add_argument("--gpus", type=int, required=True)

    parser.add_argument(
        "--output_csv",
        type=argparse.FileType("w"),
        default=sys.stdout,
        help="Path to output csv.",
    )
    args = parser.parse_args()

    print("=" * 80, flush=True)
    model_spec = model_specs.get_llama_model_spec(
        model_name="llama3-70b",
        checkpoint_path="/mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-Instruct-ff-fp8",
    )
    if args.proxy:
        model_spec.num_layers = 5

    print(f"model_spec={model_spec}", flush=True)
    print(f"{utils_for_log.time_string()} Start loading model.", flush=True)
    step_fn = fwd_llama_fp8.generate_step_fn(
        model_spec,
        load_checkpoint_weights=True,
        auto_capture_graphs=args.cuda_graphs,
        batch_sizes=[args.q_len],
        num_processes=args.gpus,
    )
    attention_factory = fwd_llama.LlamaAttentionFactory(
        ms=model_spec, num_processes=args.gpus
    )
    print(f"{utils_for_log.time_string()} Finish loading model.", flush=True)

    run_profile(
        step_fn,
        attention_factory,
        args.q_len,
        args.kv_len,
        model_spec.vocab_size,
    )


if __name__ == "__main__":
    main()
