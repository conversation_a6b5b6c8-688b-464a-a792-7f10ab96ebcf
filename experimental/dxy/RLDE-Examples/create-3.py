import pathlib

import matplotlib.pyplot as plt
import numpy as np

# Labels for the points with multi-line labels
labels = [
    "SFT",
    "SFT (2x PARAMS)",
    "SFT (2x PARAMS + 10x DATA)",
    "SFT (2x PARAMS + 20x DATA)",
    "Reinforcement Learning from Developer Behavior (RLDB)",
    # "SFT (2x PARAMS + 10x DATA) + RL from Developer Environment",
]

# Token accuracies for each label
token_accuracies = [0.8109, 0.8201, 0.8314, 0.8315, 0.8437]

# Reverse the order of labels and accuracies
labels = labels[::-1]
token_accuracies = token_accuracies[::-1]

# Custom colors (as requested)
custom_colors = ["#9ae2dc", "#fec68f", "#feb5a5", "#c6e59b", "#fe7e5f"]
custom_colors = custom_colors[::-1]  # Reverse to match bar order

# Create the plot
fig, ax = plt.subplots(figsize=(8, 5))

# Plot horizontal bars with the custom colors
y_positions = np.arange(len(labels))  # Y positions for the bars
ax.barh(
    y_positions, token_accuracies, color=custom_colors, edgecolor="black", height=0.6
)

# Annotate each bar with the label, placing it slightly more to the right
for i, (label, accuracy) in enumerate(zip(labels, token_accuracies)):
    ax.text(
        0.805,  # Adjust the position slightly to the right
        i,
        label,
        va="center",
        ha="left",
        color="black",  # Black text for contrast
        fontsize=10,
        weight="bold",
    )

# Set labels and title
ax.set_xlabel("Accuracy", fontsize=13)
ax.set_title("Code Completion Results On Augment Internal Benchmark", fontsize=13)

# Adjust y-axis to show the bars in reversed order
ax.set_yticks(y_positions)
ax.set_yticklabels([])  # Remove y-axis labels since they're displayed on the bars

# Adjust x-axis limits for better spacing
ax.set_xlim([0.8, 0.85])

# Display gridlines for better readability
ax.grid(axis="x", linestyle="--", alpha=0.7)

# Adjust layout
plt.tight_layout()

# Save the plot as a PNG image
save_path = pathlib.Path.home() / "Desktop" / "code_completion_results.png"
save_path = save_path.absolute()
assert save_path.parent.exists()
print(f"Save plot to {save_path}.")
fig.savefig(save_path, dpi=300, bbox_inches="tight")


# Display the plot
plt.show()
plt.show()
