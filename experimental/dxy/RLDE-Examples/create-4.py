import pathlib

import matplotlib.pyplot as plt
import numpy as np

# Labels for the points with multi-line labels
labels = [
    "SFT",
    "SFT (2x PARAMS)",
    "SFT (2x PARAMS + 10x DATA)",
    "SFT (2x PARAMS + 20x DATA)",
    "SFT (2x PARAMS + 10x DATA) + RLDB",
]

# Token accuracies for each label
token_accuracies = [0.8109, 0.8201, 0.8314, 0.8315, 0.8441]

# Reverse the order of labels and accuracies
labels = labels[::-1]
token_accuracies = token_accuracies[::-1]

# Custom colors
custom_colors = ["#9ae2dc", "#fec68f", "#feb5a5", "#c6e59b", "#feb5a5"]
custom_colors = custom_colors[::-1]  # Reverse to match bar order

# Color for the extended part of the last bar
extension_color = "#fe7e5f"

# Create the plot
fig, ax = plt.subplots(figsize=(8, 5))
y_positions = np.arange(len(labels)) * 0.6  # Decrease spacing to make bars closer

# Plot horizontal bars with the custom colors
# Disable format from VSCode
# pylint: disable=line-too-long
for i, (label, accuracy, color) in enumerate(
    zip(labels, token_accuracies, custom_colors)
):
    if i == 0:  # Special handling for the last bar
        base_accuracy = token_accuracies[2]
        extension_width = accuracy - base_accuracy
        ax.barh(y_positions[i], base_accuracy, color=custom_colors[2], edgecolor="black", height=0.5)
        ax.barh(y_positions[i], extension_width, left=base_accuracy, color=extension_color, edgecolor="black", height=0.5)
        ax.text(base_accuracy + extension_width / 2, y_positions[i], "RLDB", va="center", ha="center", color="black", fontsize=10, weight="bold")
        ax.text(0.805, y_positions[i], "SFT (2x params + 10x data)", va="center", ha="left", color="black", fontsize=10, weight="bold")
    else:
        ax.barh(y_positions[i], accuracy, color=color, edgecolor="black", height=0.5)
        ax.text(0.805, y_positions[i], label, va="center", ha="left", color="black", fontsize=10, weight="bold")
# pylint: enable=line-too-long

# Set labels and title
# ax.set_xlabel("Token Accuracy", fontsize=14)
ax.set_xlabel("Accuracy", fontsize=14)
ax.set_title("Code Completion Results On Augment Internal Benchmark", fontsize=14)

# Adjust y-axis to show the bars in reversed order
ax.set_yticks(y_positions)
ax.set_yticklabels([])  # Remove y-axis labels since they're displayed on the bars

# Adjust x-axis limits for better spacing
ax.set_xlim([0.8, 0.85])

# Display gridlines for better readability
ax.grid(axis="x", linestyle="--", alpha=0.2)

# Adjust layout
plt.tight_layout()

# Save the plot as a PNG image
save_path = pathlib.Path.home() / "Desktop" / "code_completion_results.png"
save_path = save_path.absolute()
assert save_path.parent.exists()
print(f"Save plot to {save_path}.")
fig.savefig(save_path, dpi=300, bbox_inches="tight")

# Display the plot
plt.show()