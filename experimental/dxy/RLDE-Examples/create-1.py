import matplotlib.pyplot as plt
import numpy as np

# Labels for the bars
labels = [
    "SFT",
    "SFT (2x params)",
    "SFT (2x params, 10x data)",
    "SFT (2x params, 20x data)",
    "SFT (2x params, 10x data) + RLDE",
]

# Token accuracies for each label (replace these values with actual data if available)
token_accuracies = [0.8109, 0.8201, 0.8314, 0.8315, 0.8437]
colors = ["skyblue", "lightgreen", "salmon", "plum", "orange"]

# Positions of the bars on the x-axis
x = np.arange(len(labels))

# Width of each bar
width = 0.6

# Create a figure and a set of subplots
fig, ax = plt.subplots(figsize=(10, 6))

# Create the bars
rects = ax.bar(x, token_accuracies, width, color=colors)

# Add labels, title, and custom x-axis tick labels
ax.set_ylabel("Token Accuracy", fontsize=14)
ax.set_ylim([0.80, 0.85])  # Set y-axis limits from 0.80 to 0.85
ax.set_xticks(x)
ax.set_xticklabels(labels, rotation=60, fontsize=12)
ax.set_xticklabels(labels)
ax.set_title("Code Completion Results Using Augment Internal Benchmark", fontsize=16)

# Adjust layout to make room for the x-axis labels
plt.tight_layout()

# Display the plot
plt.show()


# Add the token accuracy values on top of each bar
# for rect in rects:
#     height = rect.get_height()
#     ax.annotate(
#         f"{height:.2%}",
#         xy=(rect.get_x() + rect.get_width() / 2, height),
#         xytext=(0, 3),  # Offset text vertically by 3 points
#         textcoords="offset points",
#         ha="center",
#         va="bottom",
#     )
