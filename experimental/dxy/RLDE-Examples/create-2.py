import matplotlib.pyplot as plt
import numpy as np

# Labels for the points with multi-line labels
labels = [
    "SFT",
    "SFT\n(2x params)",
    "SFT\n(2x params + 10x data)",
    "SFT\n(2x params + 20x data)",
    "SFT\n(2x params + 10x data)\n+ RL from Developer Environment",
]

# Token accuracies for each label
token_accuracies = [0.8109, 0.8201, 0.8314, 0.8315, 0.8437]
locations = [(0, -10), (-10, -40), (-70, 5), (-50, -30), (-200, 5)]

# Positions of the points on the x-axis
x = np.linspace(0, 4, len(labels))  # Evenly spaced positions

# Create a figure and a set of subplots
fig, ax = plt.subplots(figsize=(6, 10))

# Plot the points connected by lines
ax.plot(x, token_accuracies, marker="o", linestyle="-", color="b")

# Annotate the labels near the points with left alignment
for i, txt in enumerate(labels):
    ax.annotate(
        txt,
        (x[i], token_accuracies[i]),
        textcoords="offset points",
        xytext=locations[i],
        ha="left",
        fontsize=12,
    )

# Add labels and title
ax.set_ylabel("Token Accuracy", fontsize=14)
ax.set_ylim([0.81, 0.85])  # Set y-axis limits from 0.80 to 0.85
ax.set_title("Code Completion Results\nOn Augment Internal Benchmark", fontsize=14)

# Remove x-axis ticks and labels
ax.set_xticks([])

# Adjust layout to make room for the annotations
plt.tight_layout()

# Display the plot
plt.show()
