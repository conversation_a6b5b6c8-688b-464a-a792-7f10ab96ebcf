"""
This script converts a PDF file into a cropped animated GIF.

Usage:
python pdf2gif.py examples.pdf examples.gif 730 210 2270 1485

python experimental/dxy/RLDE-Examples/pdf2gif.py '/Users/<USER>/Downloads/RLDB-Examples.pdf' /Users/<USER>/Downloads/RLDB-Examples.gif 740 5 2250 1680
"""

import argparse
import pathlib

from pdf2image import convert_from_path
from PIL import Image


def pdf_to_cropped_gif(
    pdf_path: pathlib.Path,
    top_left: tuple[int, int],
    bottom_right: tuple[int, int],
    gif_path: pathlib.Path,
    poppler_path: pathlib.Path,
    dpi: int = 200,
    duration: int = 500,
):
    """
    Converts a PDF into a cropped animated GIF.

    :param pdf_path: Path to the input PDF file.
    :param top_left: Tuple (x1, y1) for the top-left corner of the crop area.
    :param bottom_right: Tuple (x2, y2) for the bottom-right corner of the crop area.
    :param gif_path: Path to save the output GIF file.
    :param dpi: Resolution for converting PDF to images.
    :param duration: Duration between frames in the GIF (in milliseconds).
    """
    # Convert PDF to images
    pages: list[Image.Image] = convert_from_path(
        pdf_path, dpi=dpi, poppler_path=poppler_path
    )

    frames = []
    for idx, page in enumerate(pages):
        # Crop the image
        left = top_left[0]
        upper = top_left[1]
        right = bottom_right[0]
        lower = bottom_right[1]
        cropped_image = page.crop((left, upper, right, lower))
        frames.append(cropped_image)
        width, height = page.size
        print(f"The {idx}-th frame size: {width}x{height}")
    print(f"Cropped {len(frames)} frames")

    # Save as animated GIF
    frames[0].save(
        gif_path,
        save_all=True,
        append_images=frames[1:],
        duration=duration,
        loop=0,  # 0 means infinite loop
    )
    print(f"Animated GIF saved at {gif_path}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Convert a PDF to a cropped animated GIF."
    )
    parser.add_argument(
        "pdf_path", type=pathlib.Path, help="Path to the input PDF file."
    )
    parser.add_argument(
        "gif_path", type=pathlib.Path, help="Path to save the output GIF file."
    )
    parser.add_argument("x1", type=int, help="X coordinate of the top-left corner.")
    parser.add_argument("y1", type=int, help="Y coordinate of the top-left corner.")
    parser.add_argument("x2", type=int, help="X coordinate of the bottom-right corner.")
    parser.add_argument("y2", type=int, help="Y coordinate of the bottom-right corner.")
    parser.add_argument(
        "--dpi",
        type=int,
        default=300,
        help="Resolution for converting PDF to images (default: 300).",
    )
    parser.add_argument(
        "--duration",
        type=int,
        default=1500,
        help="Duration between frames in the GIF in milliseconds (default: 500).",
    )
    parser.add_argument(
        "--poppler-path",
        type=pathlib.Path,
        default=pathlib.Path("/opt/homebrew/bin/"),
        help="Path to the Poppler binaries (if required).",
    )

    args = parser.parse_args()

    # Adjust the coordinates if necessary
    top_left = (args.x1, args.y1)
    bottom_right = (args.x2, args.y2)

    # Call the function
    pdf_to_cropped_gif(
        pdf_path=args.pdf_path,
        top_left=top_left,
        bottom_right=bottom_right,
        gif_path=args.gif_path,
        dpi=args.dpi,
        duration=args.duration,
        poppler_path=args.poppler_path,
    )
