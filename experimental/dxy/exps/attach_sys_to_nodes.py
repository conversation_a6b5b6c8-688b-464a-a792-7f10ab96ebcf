"""Attach the systems' outputs into each node.

AUGMENT_CHECKPOINTS_ROOT=/home/<USER>/checkpoints \
    CUDA_VISIBLE_DEVICES=0 python experimental/dxy/exps/attach_sys_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/pydantic_pydantic_v1.0/ \
    --source_doc_name api-raw-debug.torch \
    --target_doc_name api-raw-debug-sys \
    --system_name basic-sc-1b basic-sc-3b basic-sc-7b

AUGMENT_CHECKPOINTS_ROOT=/home/<USER>/checkpoints \
    CUDA_VISIBLE_DEVICES=0 python experimental/dxy/exps/attach_sys_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/google_pyglove_v1.0/ \
    --source_doc_name api-raw-ge2lines_or_ge3paren.torch \
    --target_doc_name api-raw-ge2lines_or_ge3paren-sys \
    --system_name basic-sc-1b basic-sc-3b basic-sc-7b

AUGMENT_CHECKPOINTS_ROOT=/home/<USER>/checkpoints \
    CUDA_VISIBLE_DEVICES=1 python experimental/dxy/exps/attach_sys_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/pydantic_pydantic_v1.0/ \
    --source_doc_name api-raw-ge2lines_or_ge3paren.torch \
    --target_doc_name api-raw-ge2lines_or_ge3paren-sys \
    --system_name basic-sc-1b basic-sc-3b basic-sc-7b

AUGMENT_CHECKPOINTS_ROOT=/home/<USER>/checkpoints \
    CUDA_VISIBLE_DEVICES=2 python experimental/dxy/exps/attach_sys_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/thealgorithms_python_v1.0/ \
    --source_doc_name api-raw-ge2lines_or_ge3paren.torch \
    --target_doc_name api-raw-ge2lines_or_ge3paren-sys \
    --system_name basic-sc-1b basic-sc-3b basic-sc-7b
"""

import argparse
import logging
import pathlib

import torch
import tqdm

from experimental.dxy.exps.system_lib import get_system
from research.core.model_input import ModelInput
from research.core.types import CharRange, Document
from research.core.utils import Timer
from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.retrieval.utils import Span
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    AugmentedTSNode,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--data_dir",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--source_doc_name",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--target_doc_name",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--system_name",
        type=str,
        required=True,
        nargs="+",
        help="",
    )

    return parser.parse_args()


def prepare_to_start(output_dir: pathlib.Path):
    """Prepare the output directory and the logging file."""
    log_file = output_dir / (
        pathlib.Path(__file__).stem
        + "-"
        + Timer.time_string().replace("[", "").replace("]", "").replace(" ", "-")
        + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output log file is : {log_file}")
    return logger


def main(args):
    """The real main function."""
    args = parse_cmd()
    output_dir = pathlib.Path(args.data_dir)
    if not output_dir.exists():
        raise ValueError(f"{output_dir} does not exist.")

    real_output_dir: pathlib.Path = output_dir / (
        args.target_doc_name + "-" + "-".join(args.system_name)
    )
    real_output_dir.mkdir(parents=True, exist_ok=True)

    logger = prepare_to_start(real_output_dir)
    logger.info(f"Args: {args}")
    logger.info(f"Save everything into {real_output_dir}")

    load_data = torch.load(output_dir / args.source_doc_name)
    doc_dicts, image_name = load_data["documents"], load_data["image_name"]
    repo_for_system: list[Document] = load_data["repo_for_system"]

    augmented_docs: list[AugmentedParsedFile] = []
    for doc_dict in doc_dicts:
        augmented_docs.append(AugmentedParsedFile.from_dict(doc_dict))
    logger.info(
        f"Load {len(doc_dicts)} documents with"
        f" {patch_generate_lib.get_number_of_nodes(augmented_docs)} nodes from {args.source_doc_name}"
    )

    # Create system(s)
    systems = [get_system(name) for name in args.system_name]
    for idx, system in enumerate(systems):
        with (real_output_dir / f"system-{idx}-of-{len(systems)}.repr").open(
            "w"
        ) as xfile:
            xfile.write(str(system) + "\n")
        with (real_output_dir / f"system-{idx}-of-{len(systems)}.json").open(
            "w"
        ) as xfile:
            xfile.write(system.ui_to_json())
    for system in systems:
        system.load()
        system.add_docs(repo_for_system)
    logger.info(f"Finish loading the system, output_dir={real_output_dir}")
    logger.info(f"Add the repo with {len(repo_for_system)} documents.")

    cache_dir = real_output_dir / "cache"
    cache_dir.mkdir(exist_ok=True, parents=True)
    progress_bar = tqdm.tqdm(
        total=patch_generate_lib.get_number_of_nodes(augmented_docs)
    )
    final_docs = []
    for idx_doc, doc in enumerate(augmented_docs):
        cache_file = cache_dir / f"nodes-{idx_doc}-{len(doc_dicts)}.torch"
        if cache_file.exists():
            logger.info(f"Find a cache file {cache_file}")
            cache_doc_data = torch.load(cache_file)
            # Do the necessary check
            cache_doc = AugmentedParsedFile.from_dict(cache_doc_data)
            if len(doc.ts_nodes) != len(cache_doc.ts_nodes):
                raise ValueError(f"{len(doc.ts_nodes)} vs. {len(cache_doc.ts_nodes)}")
            for idx_node, (cache_node, node) in enumerate(
                zip(cache_doc.ts_nodes, doc.ts_nodes)
            ):
                if cache_node.type != node.type:
                    raise TypeError(
                        f"[{idx_node}-th node]: {cache_node.type} vs. {node.type}"
                    )
            doc = cache_doc  # replace doc with the cache document
        for node in doc.ts_nodes:
            code: str = doc.code
            crange: CharRange = doc.tsnode_to_crange(node)
            prefix: str = code[: crange.start]
            suffix: str = code[crange.stop :]
            middle: str = code[crange.start : crange.stop]
            # show_completion(prefix, suffix, middle)
            model_input = ModelInput(
                prefix=prefix,
                suffix=suffix,
                target=middle,
                path=doc.scope_tree.name,
                extra={"ground_truth_span": Span(crange.start, crange.stop)},
            )
            for name, system in zip(args.system_name, systems):
                xkey = f"sys-generation-by-{name}"
                if xkey not in node.extra:
                    generation = system.generate(model_input).generated_text
                    node.extra[xkey] = generation
            progress_bar.update()
        # Cache the results and append to final_docs
        try:
            doc.cache_nodes_to_dir(cache_dir)
        except UnicodeEncodeError as e:
            logger.info(f"Skip cache the {idx_doc}-th doc due to {e}")
        torch.save(doc.to_dict(), cache_file)
        final_docs.append(doc)
    progress_bar.close()

    target_file = real_output_dir / "all-results.torch"
    torch.save(
        {
            "documents": [doc.to_dict() for doc in final_docs],
            "image_name": image_name,
        },
        target_file,
    )
    logger.info(f"Save the final file into {target_file}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
