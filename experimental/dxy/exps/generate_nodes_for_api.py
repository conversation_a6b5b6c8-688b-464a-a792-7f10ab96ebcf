"""Generate nodes that represent code snippets of the API call.

python experimental/dxy/exps/generate_nodes_for_api.py \
    --image_name google/pyglove:v1.0 \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v2

python experimental/dxy/exps/generate_nodes_for_api.py \
    --image_name pydantic/pydantic:v1.0 \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v2

python experimental/dxy/exps/generate_nodes_for_api.py \
    --image_name thealgorithms/python:v1.0 \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v2
"""
import argparse
import logging
import pathlib
import random
from typing import Callable

import torch
import tqdm

from research.core import utils_for_str
from research.core.types import Document
from research.core.utils import Timer
from research.data.eval.repo_lib.coverage import CoverageInfo
from research.data.eval.repo_lib.repo import parse_docs_from_docker_image
from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.models.all_models import get_formatter_by_model_name
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    AugmentedTSNode,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--image_name",
        type=str,
        required=True,
        help="The docker image name.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="The output directory.",
    )
    return parser.parse_args()


def prepare_to_start(image_name: str, output_dir: str):
    """Prepare the output directory and the logging file."""

    real_output_dir: pathlib.Path = pathlib.Path(
        output_dir
    ) / utils_for_str.sanitize_filename(image_name)
    real_output_dir.mkdir(parents=True, exist_ok=True)
    log_file = real_output_dir / (
        pathlib.Path(__file__).stem
        + "-"
        + Timer.time_string().replace("[", "").replace("]", "").replace(" ", "-")
        + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output directory is : {real_output_dir}")
    logger.info(f"The output log file is : {log_file}")
    return real_output_dir, logger


def save_within_lambda(
    docs: list[AugmentedParsedFile],
    lambda_expr: Callable[[AugmentedParsedFile, AugmentedTSNode], bool],
    lambda_name: str,
    image_name: str,
    repo_for_system: list[Document],
    output_path: pathlib.Path,
    logger: logging.Logger,
):
    """Save the documents and skip if no documents left."""
    final_docs = patch_generate_lib.keep_docs_by_lambda_expr(
        docs,
        lambda_expr=lambda_expr,
        lambda_name=lambda_name,
        logger=logger,
    )
    if patch_generate_lib.get_number_of_nodes(final_docs):
        torch.save(
            {
                "documents": [doc.to_dict() for doc in final_docs],
                "repo_for_system": repo_for_system,
                "image_name": image_name,
            },
            output_path,
        )
        logger.info(f"After keep docs with {lambda_name}, save into {output_path}")
    else:
        logger.info(f"After keep docs with {lambda_name}, there are no document left.")


def load_documents_from_file(path: pathlib.Path) -> list[AugmentedParsedFile]:
    load_data = torch.load(path)
    documents = load_data["documents"]
    documents = [AugmentedParsedFile.from_dict(x) for x in documents]
    return documents


def main(args):
    """The real main function."""
    output_dir, logger = prepare_to_start(args.image_name, args.output_dir)
    documents, coverages, sha = parse_docs_from_docker_image(
        args.image_name, language="python"
    )

    logger.info(f"Find {len(documents)} raw documents for python, SHA={sha}.")
    augmented_docs: list[AugmentedParsedFile] = []
    for doc in tqdm.tqdm(documents):
        augmented_docs.append(AugmentedParsedFile(doc))

    repo_for_system = [doc.to_document() for doc in augmented_docs]
    # Save into a cache file
    cache_file = output_dir / "raw-doc-cov-for-api.torch"
    torch.save(
        {
            "documents": [doc.to_dict() for doc in augmented_docs],
            "repo_for_system": repo_for_system,
            "coverages": coverages,
            "image_name": args.image_name,
        },
        cache_file,
    )
    filename2coverage: dict[str, CoverageInfo] = {}
    logger.info(f"Save the raw documents into {cache_file}.")

    for coverage in coverages:
        filename2coverage[coverage.filename] = coverage
    logger.info(
        f"There are {len(augmented_docs)} files with {len(documents)} documents."
    )
    logger.info(
        f"There are {len(filename2coverage)} files having coverage information."
    )

    # Filter out documents
    filtered_documents = patch_generate_lib.remove_docs_by_filename(
        augmented_docs,
        exclude_filename_substrs=(
            "_test",
            "test_",
            "tests",
        ),
        exclude_filename_parts=("docs", "examples"),
        logger=logger,
    )
    filtered_documents = patch_generate_lib.filter_docs_via_coverage(
        filtered_documents, filename2coverage=filename2coverage, logger=logger
    )

    # Find all the apis
    docs_w_api_nodes = patch_generate_lib.find_all_api_calls(filtered_documents, logger)

    # Filter out the nodes without coverage info
    docs_w_api_nodes = patch_generate_lib.keep_nodes_w_coverage(
        docs_w_api_nodes, filename2coverage, logger=logger
    )
    # Filter out the nodes with specific strings.
    docs_w_api_nodes = patch_generate_lib.remove_nodes_starts_with_any(
        docs_w_api_nodes,
        ("super().__init__()", "raise "),
        logger=logger,
    )
    # Filter out the duplicated nodes
    docs_w_api_nodes = patch_generate_lib.remove_nodes_for_duplication(
        docs_w_api_nodes, logger=logger
    )

    # Attach the number of #token info
    for doc in tqdm.tqdm(docs_w_api_nodes, desc="Attach the image name"):
        for node in doc.ts_nodes:
            node.extra["image_name"] = args.image_name
    formatter = get_formatter_by_model_name("starcoderbase")
    docs_w_api_nodes = patch_generate_lib.attach_number_of_tokens(
        docs_w_api_nodes, formatter=formatter
    )

    # Filter the number of tokens.
    docs_w_api_nodes = patch_generate_lib.keep_docs_by_lambda_expr(
        docs_w_api_nodes,
        lambda_expr=lambda _, node: 8 <= node.extra["text.#tokens"] <= 2048,
        lambda_name="#tokens in [8, 2048]",
        logger=logger,
    )
    patch_generate_lib.show_statistics_of_nodes(docs_w_api_nodes, logger)

    # Cache and save
    cache_dir = output_dir / "target_nodes-api"
    for doc in tqdm.tqdm(docs_w_api_nodes, desc="Cache the nodes"):
        doc.cache_nodes_to_dir(cache_dir)
    cache_filepath = output_dir / "target_nodes-api.torch"
    torch.save(
        {
            "documents": [doc.to_dict() for doc in docs_w_api_nodes],
            "repo_for_system": repo_for_system,
            "image_name": args.image_name,
        },
        cache_filepath,
    )
    docs_from_saved_torch_file = load_documents_from_file(cache_filepath)
    for doc in tqdm.tqdm(docs_from_saved_torch_file, desc="Cache the nodes :-)"):
        doc.cache_nodes_to_dir(output_dir / "target_nodes-api-double-check")

    lamda_line_fn = lambda doc, node: len(doc.get_text_of_node(node).split("\n")) == 1
    save_within_lambda(
        docs_w_api_nodes,
        lambda_expr=lamda_line_fn,
        lambda_name="#lines == 1",
        image_name=args.image_name,
        repo_for_system=repo_for_system,
        output_path=output_dir / "api-raw-1line.torch",
        logger=logger,
    )
    lamda_line_fn = lambda doc, node: len(doc.get_text_of_node(node).split("\n")) == 2
    save_within_lambda(
        docs_w_api_nodes,
        lambda_expr=lamda_line_fn,
        lambda_name="#lines == 2",
        image_name=args.image_name,
        repo_for_system=repo_for_system,
        output_path=output_dir / "api-raw-2line.torch",
        logger=logger,
    )
    lamda_line_fn = lambda doc, node: len(doc.get_text_of_node(node).split("\n")) >= 3
    save_within_lambda(
        docs_w_api_nodes,
        lambda_expr=lamda_line_fn,
        lambda_name="#lines >= 3",
        image_name=args.image_name,
        repo_for_system=repo_for_system,
        output_path=output_dir / "api-raw-ge3lines.torch",
        logger=logger,
    )
    lamda_line_fn = lambda doc, node: doc.get_text_of_node(node).count("(") >= 3
    save_within_lambda(
        docs_w_api_nodes,
        lambda_expr=lamda_line_fn,
        lambda_name="#left parenthesis >= 3",
        image_name=args.image_name,
        repo_for_system=repo_for_system,
        output_path=output_dir / "api-raw-ge3paren.torch",
        logger=logger,
    )
    lamda_line_fn = lambda doc, node: (
        doc.get_text_of_node(node).count("(") >= 3
        or (2 <= len(doc.get_text_of_node(node).split("\n")) <= 10)
    )
    save_within_lambda(
        docs_w_api_nodes,
        lambda_expr=lamda_line_fn,
        lambda_name="#left parenthesis >= 3 or #lines >= 2",
        image_name=args.image_name,
        repo_for_system=repo_for_system,
        output_path=output_dir / "api-raw-ge2lines_or_ge3paren.torch",
        logger=logger,
    )
    # Keep a small one to debug
    lamda_line_fn = lambda doc, node: (
        (2 <= len(doc.get_text_of_node(node).split("\n")) <= 10)
        and random.random() < 0.05
    )
    save_within_lambda(
        docs_w_api_nodes,
        lambda_expr=lamda_line_fn,
        lambda_name="#left parenthesis >= 3 or #lines >= 2",
        image_name=args.image_name,
        repo_for_system=repo_for_system,
        output_path=output_dir / "api-raw-debug.torch",
        logger=logger,
    )
    logger.info(f"Recap the output directory is : {output_dir}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
