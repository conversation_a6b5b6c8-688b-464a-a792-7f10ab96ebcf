"""...

python experimental/dxy/exps/sample_patches.py \
    --input_file /mnt/efs/augment/user/dxy/data-patches-v3/google_pyglove-v1.0/api-raw-ge2lines_or_ge3paren-final-nodes/filtered-results.torch \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v3/google_pyglove-v1.0/api-raw-ge2lines_or_ge3paren-final-patches/ \
    --sample_type api

python experimental/dxy/exps/filter_nodes_by_sys_hydra.py \
    --input_dir /mnt/efs/augment/user/dxy/data-patches-v3/pydantic_pydantic-v1.0/ \
    --basename api-raw-ge2lines_or_ge3paren

python experimental/dxy/exps/filter_nodes_by_sys_hydra.py \
    --input_dir /mnt/efs/augment/user/dxy/data-patches-v3/thealgorithms_python-v1.0/ \
    --basename api-raw-ge2lines_or_ge3paren
"""
from __future__ import annotations

import argparse
import collections
import logging
import pathlib
import re
from typing import Any

import torch

from research.core import utils_for_str
from research.core.types import CharRange
from research.core.utils import Timer
from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.eval.dataset_generation_lib.advanced_snippet_generators import (
    get_number_of_nodes,
)
from research.eval.generation import execution
from research.eval.hydra.driver import Driver
from research.eval.patch_lib import Patch
from research.models.all_models import get_formatter_by_model_name
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    AugmentedTSNode,
    SimpleNode,
    StructuralAnalysis,
    StructuralSampler,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--input_file",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--sample_type",
        type=str,
        choices=("api", "str"),
        required=True,
        help=".",
    )
    return parser.parse_args()


def prepare_to_start(output_dir: pathlib.Path) -> logging.Logger:
    """Prepare the output directory and the logging file."""
    output_dir.mkdir(exist_ok=True, parents=True)
    log_file = output_dir / utils_for_str.sanitize_filename(
        pathlib.Path(__file__).stem + Timer.time_string() + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output log file is : {log_file}")
    return logger


def load_documents_from_file(path: pathlib.Path) -> list[AugmentedParsedFile]:
    load_data = torch.load(path)
    documents = load_data["documents"]
    documents = [AugmentedParsedFile.from_dict(x) for x in documents]
    return documents


def sample_api(
    docs: list[AugmentedParsedFile], logger: logging.Logger
) -> list[SimpleNode]:
    """Sample nodes for API calls."""
    sampler = StructuralSampler()
    simple_nodes = []
    for doc in docs:
        for node in doc.ts_nodes:
            raw_label = node.extra["raw_label"]
            if raw_label in ("api_call:func_head", "api_call:class_head"):
                simple_nodes.append(sampler.sample_arguments(node, doc))
            elif raw_label == "api_call:simple":
                simple_nodes.append(sampler.identical(node, doc))
                simple_nodes.append(sampler.sample_over_surrounded_spaces(node, doc))
                # Overly sample for symbol_level_random
                simple_nodes.append(sampler.symbol_level_random(node, doc))
                simple_nodes.append(sampler.symbol_level_random(node, doc))
                simple_nodes.append(sampler.symbol_level_random(node, doc))
                simple_nodes.append(sampler.parenthesis_level_random(node, doc))
            else:
                raise ValueError("Unknown raw_label: {raw_label}")
    logger.info(f"Finish sample api nodes : {len(simple_nodes)}")
    return simple_nodes


def dedupe_nodes(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, AugmentedParsedFile],
    logger: logging.Logger,
) -> list[SimpleNode]:
    node_by_unique_key: dict[Any, SimpleNode] = dict()
    for node in nodes:
        key = (node.filename, node.crange.start, node.crange.stop)
        node_by_unique_key[key] = node
    unique_nodes: list[SimpleNode] = list(node_by_unique_key.values())

    def _normalize_code(code: str):
        # Convert to lowercase
        code = code.lower()
        # Remove comments (assuming comments start with #)
        lines = [line.split("#")[0].strip() for line in code.split("\n")]
        # Remove the empty lines and extra whitespaces
        lines = [" ".join(x.split()) for x in lines if x]
        # Only keep a-z 0-9
        code = "".join(lines)
        return re.sub(r"[^a-z0-9]", "", code.lower())

    node_by_unique_key: dict[Any, SimpleNode] = dict()
    for node in unique_nodes:
        key_code = doc_by_filename[node.filename].code[
            node.crange.start : node.crange.stop
        ]
        node_by_unique_key[_normalize_code(key_code)] = node
    unique_nodes_v2: list[SimpleNode] = list(node_by_unique_key.values())
    logger.info(
        f"After dedupe, we reduced {len(nodes)} nodes to {len(unique_nodes)} unique nodes to {len(unique_nodes_v2)} unique nodes"
    )
    return unique_nodes_v2


def show_status(documents: list[AugmentedParsedFile], logger: logging.Logger):
    all_node_extra_keys = set()
    for doc in documents:
        for node in doc.ts_nodes:
            for key in node.extra.keys():
                all_node_extra_keys.add(key)
    logger.info(
        f"In total, there are {len(documents)} docs with {get_number_of_nodes(documents)} nodes."
    )
    logger.info(f"All node extra keys: {all_node_extra_keys}")

    # Show the statistic
    counters = {
        "left_cursor_loc": collections.Counter(),
        "right_cursor_loc": collections.Counter(),
        "completion_type": collections.Counter(),
    }
    for doc in documents:
        for node in doc.ts_nodes:
            crange = doc.tsnode_to_crange(node)
            prefix = doc.code[: crange.start]
            middle = doc.code[crange.start : crange.stop]
            suffix = doc.code[crange.stop :]
            left_cursor_loc = StructuralAnalysis.get_cursor_loc(prefix, middle + suffix)
            right_cursor_loc = StructuralAnalysis.get_cursor_loc(
                prefix + middle, suffix
            )
            completion_type = StructuralAnalysis.get_completion_typ(middle)
            counters["left_cursor_loc"][left_cursor_loc] += 1
            counters["right_cursor_loc"][right_cursor_loc] += 1
            counters["completion_type"][completion_type] += 1
    logger.info(f"left cursor loc: {counters['left_cursor_loc']}")
    logger.info(f"right cursor loc: {counters['right_cursor_loc']}")
    logger.info(f"completion type: {counters['completion_type']}")


def show_status_v2(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, AugmentedParsedFile],
    logger: logging.Logger,
):
    # Show the statistic
    counters = {
        "left_cursor_loc": collections.Counter(),
        "right_cursor_loc": collections.Counter(),
        "completion_type": collections.Counter(),
        "raw_label": collections.Counter(),
    }
    for node in nodes:
        code = doc_by_filename[node.filename].code
        crange = node.crange
        prefix = code[: crange.start]
        middle = code[crange.start : crange.stop]
        suffix = code[crange.stop :]
        left_cursor_loc = StructuralAnalysis.get_cursor_loc(prefix, middle + suffix)
        right_cursor_loc = StructuralAnalysis.get_cursor_loc(prefix + middle, suffix)
        completion_type = StructuralAnalysis.get_completion_typ(middle)
        counters["left_cursor_loc"][left_cursor_loc] += 1
        counters["right_cursor_loc"][right_cursor_loc] += 1
        counters["completion_type"][completion_type] += 1
        counters["raw_label"][node.extra["raw_label"]] += 1
    logger.info(f"left cursor loc: {counters['left_cursor_loc']}")
    logger.info(f"right cursor loc: {counters['right_cursor_loc']}")
    logger.info(f"completion type: {counters['completion_type']}")
    logger.info(f"raw_label: {counters['raw_label']}")


def main(args):
    """The real main function."""
    args = parse_cmd()
    output_dir = pathlib.Path(args.output_dir)

    logger = prepare_to_start(output_dir)
    logger.info(f"Args: {args}")
    logger.info(f"input_file: {args.input_file}")
    logger.info(f"output_dir: {output_dir}")

    input_file = pathlib.Path(args.input_file)
    documents = load_documents_from_file(input_file)
    doc_by_filename = dict()
    for doc in documents:
        doc_by_filename[doc.scope_tree.name] = doc

    show_status(documents, logger)
    if args.sample_type == "api":
        nodes = sample_api(documents, logger=logger)
    else:
        raise ValueError(f"Unknown sample type: {args.sample_type}")

    formatter = get_formatter_by_model_name("starcoderbase")
    for node in nodes:
        code = doc_by_filename[node.filename].code
        text = code[node.crange.start : node.crange.stop]
        token_length = len(formatter.tokenizer.tokenize(text))
        node.extra["text.#tokens"] = token_length
        node.extra["#lines"] = len(text.split("\n"))

    # Filter the nodes
    if args.sample_type == "api":
        final_nodes: list[SimpleNode] = []
        for node in nodes:
            if node.crange.stop <= node.crange.start:
                continue
            token_length = node.extra["text.#tokens"]
            if not (8 <= token_length <= 2048):
                continue
            cur_code = doc_by_filename[node.filename].code
            cur_text = cur_code[node.crange.start : node.crange.stop]
            # Do not want the warnings.warn case
            if cur_text.strip().startswith("warnings.warn"):
                continue
            final_nodes.append(node)
    else:
        raise ValueError(f"Unknown sample type: {args.sample_type}")
    logger.info("-" * 100)
    logger.info(f"Final node: {final_nodes}")
    unique_nodes = dedupe_nodes(
        nodes=final_nodes, doc_by_filename=doc_by_filename, logger=logger
    )
    show_status_v2(unique_nodes, doc_by_filename, logger)
    patch_generate_lib.show_statistics_of_simple_nodes(unique_nodes, logger)

    output_file = output_dir / "final-patch-nodes.torch"
    raw_load_data = torch.load(input_file)
    torch.save(
        {
            "unique_nodes": unique_nodes,
            "documents": [x.to_dict() for x in documents],
            "repo_for_system": raw_load_data["repo_for_system"],
        },
        output_file,
    )
    for node in unique_nodes:
        doc = doc_by_filename[node.filename]
        node.cache_nodes_to_dir(output_dir / "cache", doc)
    logger.info(f"Recap your output_file: {output_file}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
