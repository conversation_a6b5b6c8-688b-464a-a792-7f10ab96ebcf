"""Build the final Augmented Hydra Task Dataset."""
from __future__ import annotations

import argparse
import collections
import logging
import pathlib
import random
import re
from typing import Any

import torch

from research.core import utils_for_str
from research.core.types import Document
from research.core.utils import Timer
from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    CompletionType,
    CursorInLine,
    SimpleNode,
    StructuralAnalysis,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--limit",
        type=int,
        required=True,
        nargs="+",
        help="The maximum number of final nodes",
    )
    return parser.parse_args()


def prepare_to_start(output_dir: pathlib.Path) -> logging.Logger:
    """Prepare the output directory and the logging file."""
    output_dir.mkdir(exist_ok=True, parents=True)
    log_file = output_dir / utils_for_str.sanitize_filename(
        pathlib.Path(__file__).stem + Timer.time_string() + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output log file is : {log_file}")
    return logger


def show_status_fn(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, AugmentedParsedFile],
    logger: logging.Logger,
):
    # Show the statistic
    counters = {
        "left_cursor_loc": collections.Counter(),
        "right_cursor_loc": collections.Counter(),
        "completion_type": collections.Counter(),
        "raw_label": collections.Counter(),
    }
    for node in nodes:
        code = doc_by_filename[node.filename].code
        crange = node.crange
        prefix = code[: crange.start]
        middle = code[crange.start : crange.stop]
        suffix = code[crange.stop :]
        left_cursor_loc = StructuralAnalysis.get_cursor_loc(prefix, middle + suffix)
        right_cursor_loc = StructuralAnalysis.get_cursor_loc(prefix + middle, suffix)
        completion_type = StructuralAnalysis.get_completion_typ(middle)
        counters["left_cursor_loc"][left_cursor_loc] += 1
        counters["right_cursor_loc"][right_cursor_loc] += 1
        counters["completion_type"][completion_type] += 1

        counters["raw_label"][node.extra["raw_label"]] += 1
    logger.info(f"left cursor loc: {counters['left_cursor_loc']}")
    logger.info(f"right cursor loc: {counters['right_cursor_loc']}")
    logger.info(f"completion type: {counters['completion_type']}")
    logger.info(f"raw_label: {counters['raw_label']}")


def merge_nodes(
    nodes: list[SimpleNode], nodes_v2: list[SimpleNode]
) -> list[SimpleNode]:
    """Merge two nodes's information."""
    for idx, (node, new_node) in enumerate(zip(nodes, nodes_v2)):
        if node.filename != new_node.filename:
            raise ValueError(
                f"The {idx}-th node has different range {node} vs. {new_node}."
            )
        diff_keys = set(new_node.extra.keys()) - set(node.extra.keys())
        for xkey in diff_keys:
            node.extra[xkey] = new_node.extra[xkey]
    return nodes


def chech_eq(list_a, list_b):
    assert len(list_a) == len(list_b)
    for x, y in zip(list_a, list_b):
        assert type(x) == type(y)


def load_data_from_dir(
    input_dir: pathlib.Path,
) -> tuple[list[SimpleNode], list[AugmentedParsedFile], list[Document]]:
    # Load from Hydra results
    raw_data_path = input_dir / "final-patch-nodes.torch"
    raw_data = torch.load(raw_data_path)
    nodes, repo_for_system = raw_data["unique_nodes"], raw_data["repo_for_system"]
    docs = [AugmentedParsedFile.from_dict(x) for x in raw_data["documents"]]

    # Load the hydra result
    raw_data_path_v2 = input_dir / "hydra-default" / "all-results.torch"
    raw_data_v2 = torch.load(raw_data_path_v2)
    nodes_v2, repo_v2 = raw_data_v2["unique_nodes"], raw_data_v2["repo_for_system"]
    docs_v2 = [AugmentedParsedFile.from_dict(x) for x in raw_data_v2["documents"]]
    chech_eq(repo_for_system, repo_v2)
    chech_eq(docs, docs_v2)
    nodes = merge_nodes(nodes, nodes_v2)

    # Load the system-default
    raw_data_path_v2 = input_dir / "system-default" / "trim-sc-3b" / "all-results.torch"
    raw_data_v2 = torch.load(raw_data_path_v2)
    nodes_v2, repo_v2 = raw_data_v2["unique_nodes"], raw_data_v2["repo_for_system"]
    docs_v2 = [AugmentedParsedFile.from_dict(x) for x in raw_data_v2["documents"]]
    chech_eq(repo_for_system, repo_v2)
    chech_eq(docs, docs_v2)
    nodes = merge_nodes(nodes, nodes_v2)
    return nodes, docs, repo_for_system


def _get_pass_result_from_dict(xdict: dict) -> bool | None:
    if "result_str" not in xdict:
        return False
    result_str = xdict["result_str"]
    if result_str == "":
        return None
    parts = utils_for_str.extract_colored_parts(result_str)
    if len(parts) == 1:
        part = parts[0]
    else:
        return None
    if part == "PASSED":
        return True
    elif part in "FAILED":
        return False
    elif part in ("TIMEOUT", "OTHERS"):
        return None
    else:
        raise ValueError(f"{part}  <== {result_str}")


def dedupe_nodes_fn(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, AugmentedParsedFile],
    logger: logging.Logger,
) -> list[SimpleNode]:
    node_by_unique_key: dict[Any, SimpleNode] = dict()
    for node in nodes:
        key = (node.filename, node.crange.start, node.crange.stop)
        node_by_unique_key[key] = node
    unique_nodes: list[SimpleNode] = list(node_by_unique_key.values())

    def _normalize_code(code: str):
        # Convert to lowercase
        code = code.lower()
        # Remove comments (assuming comments start with #)
        lines = [line.split("#")[0].strip() for line in code.split("\n")]
        # Remove the empty lines and extra whitespaces
        lines = [" ".join(x.split()) for x in lines if x]
        # Only keep a-z 0-9
        code = "".join(lines)
        return re.sub(r"[^a-z0-9]", "", code.lower())

    node_by_unique_key: dict[Any, SimpleNode] = dict()
    for node in unique_nodes:
        key_code = doc_by_filename[node.filename].code[
            node.crange.start : node.crange.stop
        ]
        node_by_unique_key[_normalize_code(key_code)] = node
    unique_nodes_v2: list[SimpleNode] = list(node_by_unique_key.values())
    logger.info(
        f"After dedupe, we reduced {len(nodes)} nodes to {len(unique_nodes)} unique nodes to {len(unique_nodes_v2)} unique nodes"
    )
    return unique_nodes


def filter_nodes_fn(nodes: list[SimpleNode], logger: logging.Logger):
    key_to_fail = "hydra-simple-patch_is_assert0=1"
    key_is_empty = "hydra-simple-patch_is_empty"
    key_to_pass = "hydra-simple-patch_to_pass"

    new_nodes, pass_by_3b = [], 0
    for node in nodes:
        if not (
            (node.extra[key_to_fail]["status"] == "complete")
            and (node.extra[key_is_empty]["status"] == "complete")
            and (node.extra[key_to_pass]["status"] == "complete")
        ):
            continue
        if _get_pass_result_from_dict(node.extra[key_to_pass]) != True:
            continue
        if _get_pass_result_from_dict(node.extra[key_to_fail]):
            continue
        if _get_pass_result_from_dict(node.extra[key_is_empty]):
            continue
        if _get_pass_result_from_dict(node.extra["hydra-by-sys-trim-sc-3b"]):
            pass_by_3b += 1
            continue
        new_nodes.append(node)
        # target_completion = doc.get_text_of_node(node)
        # if node.extra["sys-generation-by-basic-sc-3b"].startswith(
        #     target_completion
        # ):
    logger.info(f"Filter out {pass_by_3b} nodes that 3B passed.")
    logger.info(
        f"After filtering, we reduced from {len(nodes)} nodes to {len(new_nodes)} nodes."
    )
    return new_nodes


def balanced_subsample(
    nodes: list[SimpleNode],
    doc_by_filename: dict[str, AugmentedParsedFile],
    limit: int,
    logger: logging.Logger,
) -> list[SimpleNode]:
    """."""

    def _get_left_cursor(idx: int):
        crange = nodes[idx].crange
        code = doc_by_filename[nodes[idx].filename].code
        prefix = code[: crange.start]
        middle = code[crange.start : crange.stop]
        suffix = code[crange.stop :]
        return StructuralAnalysis.get_cursor_loc(prefix, middle + suffix)

    def _get_right_cursor(idx: int):
        crange = nodes[idx].crange
        code = doc_by_filename[nodes[idx].filename].code
        prefix = code[: crange.start]
        middle = code[crange.start : crange.stop]
        suffix = code[crange.stop :]
        return StructuralAnalysis.get_cursor_loc(prefix + middle, suffix)

    def _get_type(idx: int):
        crange = nodes[idx].crange
        code = doc_by_filename[nodes[idx].filename].code
        middle = code[crange.start : crange.stop]
        return StructuralAnalysis.get_completion_typ(middle)

    def _get_tokens(idx: int):
        extra = nodes[idx].extra
        if "text.#tokens" in extra:
            return extra["text.#tokens"]
        else:
            return -1

    def _get_lines(idx: int):
        extra = nodes[idx].extra
        if "#lines" in extra:
            return extra["#lines"]
        else:
            return -1

    all_indexes = list(range(len(nodes)))

    final_indexes = []
    conditions = [
        (
            "left.end & #lines > 1",
            lambda i: _get_left_cursor(i) == CursorInLine.End and _get_lines(i) > 1,
            0.2,
        ),
        (
            "left.start & #lines > 1",
            lambda i: _get_left_cursor(i) == CursorInLine.Start and _get_lines(i) > 1,
            0.2,
        ),
        (
            "left.middle & #lines > 1",
            lambda i: _get_left_cursor(i) == CursorInLine.Middle and _get_lines(i) > 1,
            0.3,
        ),
        ("tokens > 128", lambda i: _get_tokens(i) > 128, 0.2),
        ("right.start", lambda i: _get_right_cursor(i) == CursorInLine.Start, 0.3),
        (
            "right.middle",
            lambda i: _get_right_cursor(i) == CursorInLine.Middle,
            0.3,
        ),
        ("right.end", lambda i: _get_right_cursor(i) == CursorInLine.End, 0.3),
        ("type.empty", lambda i: _get_type(i) == CompletionType.EmptyLike, 0.1),
        ("type.single", lambda i: _get_type(i) == CompletionType.SingleLine, 0.2),
        ("type.multi", lambda i: _get_type(i) == CompletionType.MultiLines, 0.6),
    ]
    for attr, lambda_fn, percentage in conditions:
        expected_num = int(limit * percentage)
        current_num = sum([lambda_fn(i) for i in final_indexes])
        missing_num = expected_num - current_num
        candidates = [i for i in all_indexes if lambda_fn(i) and i not in final_indexes]
        select_num = max(0, min(missing_num, len(candidates)))
        logger.info(
            f"{attr:15s} : [{len(final_indexes):4d}] : expect {expected_num:4d} nodes and currently we have {current_num:4d} nodes"
            f" over {len(final_indexes):4d} nodes, and miss {missing_num:4d} nodes. Will try to select {select_num:4d} nodes."
        )
        if select_num:
            selected_indexes = random.sample(candidates, select_num)
            logger.info(f"\t\tsampled {len(selected_indexes)} nodes")
            final_indexes.extend(selected_indexes)
    final_nodes = []
    for index in set(final_indexes):
        final_nodes.append(nodes[index])
    return final_nodes


def main(args):
    """The real main function."""
    args = parse_cmd()
    input_dir = pathlib.Path(args.input_dir)
    output_dir = pathlib.Path(args.output_dir)

    logger = prepare_to_start(output_dir)
    logger.info(f"Args: {args}")
    logger.info(f"input_dir: {input_dir}")
    logger.info(f"output_dir: {output_dir}")

    nodes, docs, repo_for_system = load_data_from_dir(input_dir)
    doc_by_filename = {doc.scope_tree.name: doc for doc in docs}

    # Apply filtering
    nodes_v1 = filter_nodes_fn(nodes, logger)
    logger.info("-" * 100)
    logger.info("After filtering :: ")
    show_status_fn(nodes_v1, doc_by_filename, logger)
    patch_generate_lib.show_statistics_of_simple_nodes(nodes_v1, logger)

    # Apply dedup
    logger.info("\n\n")
    nodes_v2 = dedupe_nodes_fn(nodes_v1, doc_by_filename, logger)
    logger.info("-" * 100)
    logger.info("After dedup :: ")
    show_status_fn(nodes_v2, doc_by_filename, logger)
    patch_generate_lib.show_statistics_of_simple_nodes(nodes_v2, logger)

    # Apply balanced_subsample
    logger.info("\n\n")
    for limit in args.limit:
        logger.info(f"Re-balance sub-sample with limit={limit} :: ")
        final_nodes = balanced_subsample(nodes_v2, doc_by_filename, limit, logger)

        show_status_fn(final_nodes, doc_by_filename, logger)
        patch_generate_lib.show_statistics_of_simple_nodes(final_nodes, logger)

        output_file = output_dir / f"{limit:03d}-results.torch"
        torch.save(
            {
                "nodes": final_nodes,
                "docs": [doc.to_dict() for doc in docs],
                "repo_for_system": repo_for_system,
            },
            output_file,
        )
        cache_dir = output_dir / f"{limit:03d}-cache"
        cache_dir.mkdir(parents=True, exist_ok=True)
        for idx, node in enumerate(final_nodes):
            try:
                node.cache_nodes_to_dir(cache_dir, doc_by_filename[node.filename])
            except UnicodeEncodeError as e:
                logger.info(f"Skip cache the {idx}/{len(nodes)}-th node due to {e}")
        logger.info(f"Save the cache into {cache_dir}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
