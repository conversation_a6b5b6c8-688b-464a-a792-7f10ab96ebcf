"""...

python experimental/dxy/exps/attach_hydra_to_patches.py \
    --source_file /mnt/efs/augment/user/dxy/data-patches-v4/pydantic_pydantic-v1.0/api-raw-ge2lines_or_ge3paren-final-patches/final-patch-nodes.torch \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v4/pydantic_pydantic-v1.0/api-raw-ge2lines_or_ge3paren-final-patches/hydra-default \
    --max_parallelism 256
"""
import argparse
import logging
import pathlib
from concurrent import futures

import torch
import tqdm

from research.core import utils_for_str
from research.core.utils import Timer
from research.eval.generation import execution
from research.eval.hydra.driver import Driver
from research.eval.patch_lib import Patch
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    SimpleNode,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--source_file",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--max_parallelism",
        type=int,
        default=256,
        help="The maximum parallelism",
    )
    parser.add_argument(
        "--debug_dir",
        type=str,
        default="/mnt/efs/augment/user/dxy/debug-logs",
        help="",
    )
    return parser.parse_args()


def prepare_to_start(output_dir: pathlib.Path) -> logging.Logger:
    """Prepare the output directory and the logging file."""
    output_dir.mkdir(exist_ok=True, parents=True)
    log_file = output_dir / utils_for_str.sanitize_filename(
        pathlib.Path(__file__).stem + "-hydra-" + Timer.time_string() + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output log file is : {log_file}")
    return logger


def safe_try_and_debug(patch: Patch, driver: Driver, debug_dir: pathlib.Path) -> dict:
    """Safely try a patch and try to debug if anything failed."""
    image_name: str = patch._extra["image_name"]
    debug_file = debug_dir / utils_for_str.sanitize_filename(patch.patch_id)
    result_str = None
    try:
        results = driver.dispatch(patch, image_name)
        result_str = results["result_str"]
        status = "complete"
    except execution.TimeoutException:
        status = "timed out"
        patch._extra["fail-status"] = status
        debug_file.write_text(patch.to_json(indent=4))
    except BaseException as e:
        status = f"failed: {e}"
        patch._extra["fail-status"] = status
        debug_file.write_text(patch.to_json(indent=4))
    return {"result_str": result_str, "status": status}


def attach_hydra(
    node: SimpleNode,
    id_prefix: str,
    key_prefix: str,
    code: str,
    driver: Driver,
    debug_dir: pathlib.Path,
):
    image_name: str = node.image_name
    patch_id = f"{image_name}/{id_prefix}-{utils_for_str.get_random_str(4)}"
    # Must failed results
    xkey = f"{key_prefix}-patch_is_assert0=1"
    if xkey not in node.extra:
        patch_to_fail = Patch(
            patch_id=patch_id + xkey,
            file_name=node.filename,
            char_start=node.crange.start,
            char_end=node.crange.stop,
            patch_content="assert 0 == 1",
            _extra={"image_name": image_name},
        )
        results = safe_try_and_debug(patch_to_fail, driver, debug_dir=debug_dir)
        node.extra[xkey] = results
    # Empty patch
    xkey = f"{key_prefix}-patch_is_empty"
    if xkey not in node.extra:
        patch_is_empty = Patch(
            patch_id=patch_id + xkey,
            file_name=node.filename,
            char_start=node.crange.start,
            char_end=node.crange.stop,
            patch_content="",
            _extra={"image_name": image_name},
        )
        results = safe_try_and_debug(patch_is_empty, driver, debug_dir=debug_dir)
        node.extra[xkey] = results
    # Must success
    xkey = f"{key_prefix}-patch_to_pass"
    if xkey not in node.extra:
        patch_to_pass = Patch(
            patch_id=patch_id + xkey,
            file_name=node.filename,
            char_start=node.crange.start,
            char_end=node.crange.stop,
            patch_content=code[node.crange.start : node.crange.stop],
            _extra={"image_name": image_name},
        )
        results = safe_try_and_debug(patch_to_pass, driver, debug_dir=debug_dir)
        node.extra[xkey] = results
    return True


def main(args):
    """The real main function."""
    args = parse_cmd()
    output_dir: pathlib.Path = pathlib.Path(args.output_dir)
    logger = prepare_to_start(output_dir)
    logger.info(f"Args: {args}")

    load_data = torch.load(args.source_file)
    logger.info(f"There are {list(load_data.keys())} in the raw data.")
    repo_for_sys = load_data["repo_for_system"]
    unique_nodes: list[SimpleNode] = load_data["unique_nodes"]
    documents = [AugmentedParsedFile.from_dict(x) for x in load_data["documents"]]
    doc_by_filename = {doc.scope_tree.name: doc for doc in documents}
    logger.info(f"Load {len(unique_nodes)} unique patches.")

    cache_dir: pathlib.Path = output_dir / "cache"
    cache_dir.mkdir(parents=True, exist_ok=True)
    debug_dir: pathlib.Path = output_dir / f"{args.debug_dir}"
    debug_dir.mkdir(parents=True, exist_ok=True)

    driver = Driver(
        local_timeout_secs=600,
        global_timeout_secs=900,
        hydra_block_resource_internet_access=False,
        driver_name=f"driver-dxy-{utils_for_str.get_random_str()}",
        max_parallel_runs=0,
    )

    future_by_index = dict()
    # import pdb; pdb.set_trace()
    # Using ThreadPoolExecutor to fetch the URLs concurrently
    with futures.ThreadPoolExecutor(max_workers=args.max_parallelism) as executor:
        for idx_node, node in enumerate(unique_nodes):
            code = doc_by_filename[node.filename].code
            future = executor.submit(
                attach_hydra,
                node=node,
                id_prefix=f"{idx_node:02d}-{len(unique_nodes)}",
                key_prefix="hydra-simple",
                code=code,
                driver=driver,
                debug_dir=debug_dir,
            )
            future_by_index[idx_node] = future
        logger.info(f"Collected {len(future_by_index)} nodes to be evaluated")

        for idx, future in tqdm.tqdm(
            future_by_index.items(), total=len(future_by_index)
        ):
            try:
                _ = future.result(timeout=1800.0)
            except futures.TimeoutError:
                logger.info(f"[!!!] {idx}/{len(future_by_index)}-th node is timeout.")
            except Exception as e:
                logger.info(
                    f"[!!!] {idx}/{len(future_by_index)}-th node raised an exception: {e}"
                )

    target_file = output_dir / "all-results.torch"
    torch.save(
        {
            "unique_nodes": unique_nodes,
            "repo_for_system": repo_for_sys,
            "documents": [x.to_dict() for x in documents],
        },
        target_file,
    )
    logger.info(f"Save the final file into {target_file}")
    for idx, node in enumerate(unique_nodes):
        try:
            node.cache_nodes_to_dir(cache_dir, doc_by_filename[node.filename])
        except UnicodeEncodeError as e:
            logger.info(f"Skip cache the {idx}/{len(unique_nodes)}-th node due to {e}")
    driver.close()


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
