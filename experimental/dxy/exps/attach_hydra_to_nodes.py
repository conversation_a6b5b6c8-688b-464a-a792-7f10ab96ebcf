"""...

python experimental/dxy/exps/attach_hydra_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/google_pyglove_v1.0/ \
    --source_doc_name api-raw-debug.torch \
    --target_doc_name api-raw-debug-hydra

python experimental/dxy/exps/attach_hydra_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/pydantic_pydantic_v1.0/ \
    --source_doc_name api-raw-debug.torch \
    --target_doc_name api-raw-debug-hydra

python experimental/dxy/exps/attach_hydra_to_nodes.py \
    --data_dir /mnt/efs/augment/user/dxy/data-patches-v2/thealgorithms_python_v1.0/ \
    --source_doc_name api-raw-debug.torch \
    --target_doc_name api-raw-debug-hydra
"""
import argparse
import logging
import pathlib

import torch
import tqdm

from research.core import utils_for_str
from research.core.model_input import ModelInput
from research.core.types import Char<PERSON>ang<PERSON>
from research.core.utils import Timer
from research.eval.dataset_generation_lib.advanced_snippet_generators import (
    get_number_of_nodes,
)
from research.eval.generation import execution
from research.eval.hydra.driver import Driver
from research.eval.patch_lib import Patch
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    AugmentedTSNode,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--data_dir",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--source_doc_name",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--target_doc_name",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--debug_dir",
        type=str,
        default="/mnt/efs/augment/user/dxy/debug-logs",
        help="",
    )
    return parser.parse_args()


def prepare_to_start(output_dir: pathlib.Path) -> logging.Logger:
    """Prepare the output directory and the logging file."""
    output_dir.mkdir(exist_ok=True, parents=True)
    log_file = output_dir / utils_for_str.sanitize_filename(
        pathlib.Path(__file__).stem + "-hydra-" + Timer.time_string() + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output log file is : {log_file}")
    return logger


def safe_try_and_debug(
    patch: Patch, driver: Driver, debug_dir: pathlib.Path, timeout: float = 60.0
) -> dict:
    """Safely try a patch and try to debug if anything failed."""
    image_name: str = patch._extra["image_name"]
    debug_file = debug_dir / utils_for_str.sanitize_filename(patch.patch_id)
    result_str = None
    try:
        with execution.time_limit(timeout):
            results = driver.dispatch(patch, image_name)
        result_str = results["result_str"]
        status = "complete"
    except execution.TimeoutException:
        status = "timed out"
        patch._extra["fail-status"] = status
        debug_file.write_text(patch.to_json(indent=4))
    except BaseException as e:
        status = f"failed: {e}"
        patch._extra["fail-status"] = status
        debug_file.write_text(patch.to_json(indent=4))
    return {"result_str": result_str, "status": status}


def main(args):
    """The real main function."""
    args = parse_cmd()
    output_dir: pathlib.Path = (
        pathlib.Path(args.data_dir) / f"{args.target_doc_name}-hydra"
    )
    logger = prepare_to_start(output_dir)
    logger.info(f"Args: {args}")

    load_data = torch.load(pathlib.Path(args.data_dir) / args.source_doc_name)
    doc_dicts, image_name = load_data["documents"], load_data["image_name"]

    augmented_docs: list[AugmentedParsedFile] = []
    for doc_dict in doc_dicts:
        augmented_docs.append(AugmentedParsedFile.from_dict(doc_dict))
    logger.info(
        f"Load {len(doc_dicts)} documents with"
        f" {get_number_of_nodes(augmented_docs)} nodes from {args.source_doc_name}"
    )
    logger.info(f"Image Name: {image_name}")

    cache_dir: pathlib.Path = output_dir / "cache"
    cache_dir.mkdir(parents=True, exist_ok=True)
    debug_dir: pathlib.Path = output_dir / f"{args.debug_dir}"
    debug_dir.mkdir(parents=True, exist_ok=True)

    logger.info(f"Finish loading the system, output_dir={output_dir}")
    progress_bar = tqdm.tqdm(total=get_number_of_nodes(augmented_docs))
    driver = Driver(
        local_timeout_secs=600,
        global_timeout_secs=900,
        hydra_block_resource_internet_access=False,
    )
    final_docs = []
    for idx_doc, doc in enumerate(augmented_docs):
        cache_file = cache_dir / f"nodes-{idx_doc}-{len(doc_dicts)}.torch"
        if cache_file.exists():
            logger.info(f"Find a cache file {cache_file}")
            cache_doc_data = torch.load(cache_file)
            # Do the necessary check
            cache_doc = AugmentedParsedFile.from_dict(cache_doc_data)
            if len(doc.ts_nodes) != len(cache_doc.ts_nodes):
                raise ValueError(f"{len(doc.ts_nodes)} vs. {len(cache_doc.ts_nodes)}")
            for idx_node, (cache_node, node) in enumerate(
                zip(cache_doc.ts_nodes, doc.ts_nodes)
            ):
                if cache_node.type != node.type:
                    raise TypeError(
                        f"[{idx_node}-th node]: {cache_node.type} vs. {node.type}"
                    )
            doc = cache_doc  # replace doc with the cache document
        for idx_node, node in enumerate(doc.ts_nodes):
            code: str = doc.code
            crange: CharRange = doc.tsnode_to_crange(node)
            patch_id = f"{image_name}/{idx_doc:02d}_{len(augmented_docs)}-{idx_node:02d}_{len(doc.ts_nodes)}"
            # Must failed results
            xkey = "hydra-patch_to_fail"
            if xkey not in node.extra:
                patch_to_fail = Patch(
                    patch_id=patch_id + xkey,
                    file_name=doc.scope_tree.name,
                    char_start=crange.start,
                    char_end=crange.stop,
                    patch_content="assert 0 == 1",
                    _extra={"image_name": image_name},
                )
                results = safe_try_and_debug(patch_to_fail, driver, debug_dir=debug_dir)
                node.extra[xkey] = results
            # Empty patch
            xkey = "hydra-patch_is_empty"
            if xkey not in node.extra:
                patch_is_empty = Patch(
                    patch_id=patch_id + xkey,
                    file_name=doc.scope_tree.name,
                    char_start=crange.start,
                    char_end=crange.stop,
                    patch_content="",
                    _extra={"image_name": image_name},
                )
                results = safe_try_and_debug(
                    patch_is_empty, driver, debug_dir=debug_dir
                )
                node.extra[xkey] = results
            # Must success
            xkey = "hydra-patch_to_pass"
            if xkey not in node.extra:
                patch_to_pass = Patch(
                    patch_id=patch_id + xkey,
                    file_name=doc.scope_tree.name,
                    char_start=crange.start,
                    char_end=crange.stop,
                    patch_content=code[crange.start : crange.stop],
                    _extra={"image_name": image_name},
                )
                results = safe_try_and_debug(patch_to_pass, driver, debug_dir=debug_dir)
                node.extra[xkey] = results
            progress_bar.update()
        doc.cache_nodes_to_dir(cache_dir)
        final_docs.append(doc)
        torch.save(doc.to_dict(), cache_file)
    progress_bar.close()

    target_file = output_dir / f"{args.target_doc_name}-hydra.torch"
    torch.save(
        {
            "documents": [doc.to_dict() for doc in final_docs],
            "image_name": image_name,
        },
        target_file,
    )
    logger.info(f"Save the final file into {target_file}")
    driver.close()


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
