"""Attach the systems' outputs into each node.
"""
import argparse
import pathlib
import threading

import torch
import tqdm

from experimental.dxy.exps.system_lib import get_system
from research.core import utils_for_log, utils_for_str
from research.core.model_input import ModelInput
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.eval import hydra as hydra_lib
from research.eval import patch_lib
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    SimpleNode,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--source_file",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--system_name",
        type=str,
        required=True,
        help="",
    )

    return parser.parse_args()


def main(args):
    """The real main function."""
    args = parse_cmd()
    output_dir = pathlib.Path(args.output_dir) / args.system_name
    output_dir.mkdir(exist_ok=True, parents=True)
    output_log = (
        output_dir
        / f"{utils_for_str.sanitize_filename(utils_for_log.time_string())}.log"
    )
    logger = utils_for_log.create_logger(__file__, output_log)

    logger.info(f"Args: {args}")
    logger.info(f"Save everything into {output_dir}")

    # Load data
    load_data = torch.load(args.source_file)
    logger.info(f"There are {list(load_data.keys())} in the raw data.")
    repo_for_sys = load_data["repo_for_system"]
    unique_nodes: list[SimpleNode] = load_data["unique_nodes"]
    documents = [AugmentedParsedFile.from_dict(x) for x in load_data["documents"]]
    doc_by_filename = {doc.scope_tree.name: doc for doc in documents}
    logger.info(
        f"Load {len(unique_nodes)} unique patches from {len(documents)} documents."
    )

    # Create system(s)
    system_name: str = args.system_name
    system = get_system(system_name)
    with (output_dir / f"system.repr").open("w") as xfile:
        xfile.write(str(system) + "\n")
    system.load()
    system.add_docs(repo_for_sys)
    logger.info(f"Finish loading the system, output_dir={output_dir}")
    logger.info(f"Add the repo with {len(repo_for_sys)} documents.")

    cache_dir = output_dir / "cache"
    cache_dir.mkdir(exist_ok=True, parents=True)

    def __evaluate_fn(
        node: SimpleNode,
        completion: str,
        driver: hydra_lib.Driver,
        pbar: tqdm.tqdm,
    ):
        patch = patch_lib.Patch(
            patch_id=f"{node.image_name}/{utils_for_str.get_random_str(6)}",
            file_name=node.filename,
            char_start=node.crange.start,
            char_end=node.crange.stop,
            patch_content=completion,
            _extra={"image_name": node.image_name},
        )
        try:
            results = driver.dispatch(patch, node.image_name)
            result_str = results["result_str"]
            status = "complete"
        except BaseException as e:
            result_str = ""
            status = f"failed: {e}"
        node.extra[f"hydra-by-sys-{system_name}"] = {
            "result_str": result_str,
            "status": status,
        }
        pbar.update(1)

    bar_for_eval = tqdm.tqdm(
        total=len(unique_nodes), desc="Evaluate Result", position=0, leave=True
    )
    bar_for_sysg = tqdm.tqdm(
        total=len(unique_nodes), desc="System Generation", position=0, leave=True
    )
    threads = []
    with hydra_lib.Driver(
        driver_name=f"eval-augmented-hydra-{utils_for_str.get_random_str(5)}",
        local_timeout_secs=900,
        global_timeout_secs=1800,
        hydra_block_resource_internet_access=False,
        max_parallel_runs=16,
    ) as driver:
        for idx_node, node in enumerate(unique_nodes):
            code = doc_by_filename[node.filename].code
            crange: CharRange = node.crange
            prefix: str = code[: crange.start]
            suffix: str = code[crange.stop :]
            middle: str = code[crange.start : crange.stop]
            # show_completion(prefix, suffix, middle)
            model_input = ModelInput(
                prefix=prefix,
                suffix=suffix,
                target=middle,
                path=node.filename,
                extra={"ground_truth_span": CharRange(crange.start, crange.stop)},
            )
            xkey = f"sys-generation-by-{system_name}"
            generation = system.generate(model_input).generated_text
            node.extra[xkey] = generation
            bar_for_sysg.update()
            # Run Hydra
            threads.append(
                threading.Thread(
                    target=__evaluate_fn,
                    args=(node, generation, driver, bar_for_eval),
                )
            )
            threads[-1].start()
        # Wait for all the evaluation threads to be finished
        for thread in threads:
            thread.join()
        bar_for_eval.close()
        bar_for_sysg.close()

    target_file = output_dir / "all-results.torch"
    torch.save(
        {
            "unique_nodes": unique_nodes,
            "repo_for_system": repo_for_sys,
            "documents": [x.to_dict() for x in documents],
        },
        target_file,
    )
    logger.info(f"Save the final file into {target_file}")
    for idx, node in enumerate(unique_nodes):
        try:
            node.cache_nodes_to_dir(cache_dir, doc_by_filename[node.filename])
        except UnicodeEncodeError as e:
            logger.info(f"Skip cache the {idx}/{len(unique_nodes)}-th node due to {e}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
