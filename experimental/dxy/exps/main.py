"""Main entry.

CUDA_VISIBLE_DEVICES=0 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --system=basic \
    --output_dir ~/cache/results \
    --output_name basic-basesc-16b


CUDA_VISIBLE_DEVICES=1 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --system=basic \
    --output_dir ~/cache/results \
    --output_name basic-fimsc-16b


CUDA_VISIBLE_DEVICES=1 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --system=basic-v2 \
    --max_prompt_tokens=7300 \
    --output_dir ~/cache/results \
    --output_name basicv2-7k-fimsc-16b


CUDA_VISIBLE_DEVICES=2 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --system=basic-v2 \
    --max_prompt_tokens=7300 \
    --output_dir ~/cache/results \
    --output_name basicv2-7k-basesc-16b


CUDA_VISIBLE_DEVICES=2 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --system=sota \
    --max_prompt_tokens=4096 \
    --output_dir ~/cache/results \
    --output_name sota4k-fimsc-16b


CUDA_VISIBLE_DEVICES=3 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --system=sota \
    --max_prompt_tokens=6144 \
    --output_dir ~/cache/results \
    --output_name sota6k-fimsc-16b


CUDA_VISIBLE_DEVICES=4 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --retrieval=bm25 \
    --system=sota \
    --max_prompt_tokens=6144 \
    --output_dir ~/cache/results \
    --output_name sota6k-bm25-fimsc-16b


CUDA_VISIBLE_DEVICES=2 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --retrieval=bm25 \
    --system=sota \
    --max_prompt_tokens=7200 \
    --retry=5 \
    --temperature=0.2 \
    --top_k=25 \
    --top_p=0.95 \
    --output_dir ~/cache/results \
    --output_name sota7k-bm25-basesc-16b-try5_t0.2_k25_p95


CUDA_VISIBLE_DEVICES=3 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --retrieval=bm25 \
    --system=sota \
    --max_prompt_tokens=7200 \
    --retry=5 \
    --temperature=0.2 \
    --top_k=25 \
    --top_p=0.95 \
    --output_dir ~/cache/results \
    --output_name sota7k-bm25-fimsc-16b-try5_t0.2_k25_p95


CUDA_VISIBLE_DEVICES=4 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --retrieval=diff_boykin \
    --system=sota \
    --max_prompt_tokens=7200 \
    --output_dir ~/cache/results \
    --output_name sota7k-diff_boykin-fimsc-16b


CUDA_VISIBLE_DEVICES=5 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --retrieval=diff_boykin \
    --system=sota \
    --max_prompt_tokens=7200 \
    --output_dir ~/cache/results \
    --output_name sota7k-diff_boykin-basesc-16b


CUDA_VISIBLE_DEVICES=5 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --retrieval=bm25 \
    --system=sota \
    --max_prompt_tokens=7200 \
    --output_dir ~/cache/results \
    --output_name sota7k-bm25-fimsc-16b


CUDA_VISIBLE_DEVICES=4 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --retrieval=diff_boykin \
    --system=sota \
    --max_prompt_tokens=5000 \
    --output_dir /mnt/efs/augment/user/dxy/results \
    --output_name sota5k-diff_boykin-basesc-16b


CUDA_VISIBLE_DEVICES=5 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --retrieval=diff_boykin \
    --system=sota \
    --max_prompt_tokens=6000 \
    --output_dir /mnt/efs/augment/user/dxy/results \
    --output_name sota6k-diff_boykin-basesc-16b


CUDA_VISIBLE_DEVICES=5 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --retrieval=diff_boykin \
    --system=sota \
    --max_prefix_tokens=1024 \
    --max_suffix_tokens=2048 \
    --max_prompt_tokens=7100 \
    --output_dir /mnt/efs/augment/user/dxy/results \
    --output_name sota-1k2k4k-diff_boykin-basesc-16b


CUDA_VISIBLE_DEVICES=4 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase \
    --retrieval=bm25 \
    --system=sota \
    --max_prefix_tokens=1024 \
    --max_suffix_tokens=2048 \
    --max_prompt_tokens=7100 \
    --output_dir /mnt/efs/augment/user/dxy/results \
    --output_name sota-1k2k4k-bm25-basesc-16b

python experimental/dxy/exps/main.py \
    --model=fim@http://127.0.0.1:8080 \
    --system=llama \
    --max_prefix_tokens=2048 \
    --max_suffix_tokens=2048 \
    --max_prompt_tokens=4200 \
    --output_dir ~/cache/results-llama \
    --output_name llama-fim-34b-base-4k


CUDA_VISIBLE_DEVICES=0 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_7b \
    --system=basic \
    --output_dir ~/cache/results \
    --output_name aughydra-basic-7b


CUDA_VISIBLE_DEVICES=1 python \
    experimental/dxy/exps/main.py \
    --model=starcoderbase_16b_fim_aligned \
    --retrieval=bm25 \
    --system=sota \
    --max_prefix_tokens=2048 \
    --max_suffix_tokens=2048 \
    --max_prompt_tokens=4200 \
    --output_dir ~/cache/results \
    --output_name aughydra-sota-2k2k4k-bm25-fimsc-16b
"""
import argparse
import logging
import pathlib
from concurrent import futures

import numpy as np
import tqdm

from experimental.dxy.exps.xlib import (
    BasicPostProcessor,
    BasicPreProcessor,
    GeneralizedSystem,
    RAGPreProcessor,
    TrimCompletion,
)
from research.core import utils_for_str
from research.core.ui_sugar import ui_load_from_json_file, ui_save_to_json_file
from research.core.utils import Timer
from research.eval.harness.factories import create_retriever
from research.eval.harness.tasks import HydraTask, HydraTaskV2
from research.eval.hydra.driver import Driver
from research.models import GenerationOptions
from research.models.all_models import get_model
from research.models.remote_models import RemoteLLAMACPP


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--model",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--retrieval",
        type=str,
        choices=("bm25", "diff_boykin"),
        default="bm25",
        help="",
    )
    parser.add_argument(
        "--model_ckp",
        type=str,
        help="",
    )
    parser.add_argument(
        "--retry",
        type=int,
        default=1,
        help="",
    )
    parser.add_argument(
        "--system",
        choices=("basic", "basic-v2", "sota", "llama"),
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--max_prompt_tokens",
        type=int,
        default=7168,
        help="",
    )
    parser.add_argument(
        "--temperature",
        type=float,
        default=0.0,
        help="",
    )
    parser.add_argument(
        "--top_k",
        type=int,
        default=0,
        help="",
    )
    parser.add_argument(
        "--max_prefix_tokens",
        type=int,
        default=1024,
        help="",
    )
    parser.add_argument(
        "--max_suffix_tokens",
        type=int,
        default=1024,
        help="",
    )
    parser.add_argument(
        "--top_p",
        type=float,
        default=0.0,
        help="",
    )
    parser.add_argument(
        "--output_dir",
        required=True,
        type=str,
        help="",
    )
    parser.add_argument(
        "--output_name",
        required=True,
        type=str,
        help="",
    )
    args = parser.parse_args()
    return args


def main():
    args = parse_cmd()

    # task = HydraTask()
    task = HydraTaskV2()
    if args.retrieval == "diff_boykin":
        retriever_config = {
            "name": "diff_boykin",
            "chunker": "line_level",
            "max_chunk": 40,
            "max_query_lines": 10,
        }
    elif args.retrieval == "bm25":
        retriever_config = {
            "name": "bm25",
            "chunker": "line_level",
            "max_chunk": 40,
            "max_query_lines": 10,
        }
    else:
        raise ValueError(f"Unknown args.retrieval: {args.retrieval}")
    retriever = create_retriever(retriever_config)
    generation_options = GenerationOptions(
        temperature=args.temperature,
        top_k=args.top_k,
        top_p=args.top_p,
        max_generated_tokens=512,
    )
    if args.model_ckp:
        model = get_model(args.model, checkpoint_path=pathlib.Path(args.model_ckp))
    elif "@" not in args.model:
        model = get_model(args.model)
    else:
        mode, url = args.model.split("@")
        model = RemoteLLAMACPP(url=url, mode=mode)

    if args.system == "llama":
        model.prompt_formatter.max_prefix_tokens = args.max_prefix_tokens
        model.prompt_formatter.max_suffix_tokens = args.max_suffix_tokens
        model.prompt_formatter.max_prompt_tokens = args.max_prompt_tokens
        system = GeneralizedSystem(
            model,
            None,
            preprocessor=BasicPreProcessor(),
            postprocessor=BasicPostProcessor(),
            generation_options=generation_options,
        )
    elif args.system == "basic":
        model.prompt_formatter.max_prefix_tokens = args.max_prefix_tokens
        model.prompt_formatter.max_suffix_tokens = args.max_suffix_tokens
        model.prompt_formatter.max_prompt_tokens = args.max_prompt_tokens
        model.prompt_formatter.max_retrieved_chunk_tokens = (
            args.max_prompt_tokens - args.max_prefix_tokens - args.max_suffix_tokens
        )
        model.prompt_formatter.always_fim_style = True
        model.prompt_formatter.retrieval_layout_style = "comment2"
        system = GeneralizedSystem(
            model,
            None,
            preprocessor=BasicPreProcessor(),
            postprocessor=TrimCompletion(),
            generation_options=generation_options,
        )
    elif args.system == "basic-v2":
        model.prompt_formatter.max_prefix_tokens = args.max_prefix_tokens
        model.prompt_formatter.max_prompt_tokens = args.max_prompt_tokens
        model.prompt_formatter.max_suffix_tokens = (
            args.max_prompt_tokens - args.max_prefix_tokens
        )
        model.prompt_formatter.max_retrieved_chunk_tokens = 0
        model.prompt_formatter.always_fim_style = True
        model.prompt_formatter.retrieval_layout_style = "comment2"
        system = GeneralizedSystem(
            model,
            None,
            preprocessor=BasicPreProcessor(),
            postprocessor=TrimCompletion(),
            generation_options=generation_options,
        )
    elif args.system == "sota":
        model.prompt_formatter.max_prefix_tokens = args.max_prefix_tokens
        model.prompt_formatter.max_suffix_tokens = args.max_suffix_tokens
        model.prompt_formatter.max_prompt_tokens = args.max_prompt_tokens
        model.prompt_formatter.max_retrieved_chunk_tokens = (
            args.max_prompt_tokens - args.max_prefix_tokens - args.max_suffix_tokens
        )
        model.prompt_formatter.always_fim_style = True
        model.prompt_formatter.retrieval_layout_style = "comment2"
        system = GeneralizedSystem(
            model,
            retriever,
            preprocessor=RAGPreProcessor(),
            postprocessor=TrimCompletion(),
            generation_options=generation_options,
        )
    else:
        raise ValueError(f"Unknown system version: {args.system}")

    output_dir: pathlib.Path = pathlib.Path(args.output_dir) / args.output_name
    output_dir.mkdir(parents=True, exist_ok=True)
    log_file = output_dir / utils_for_str.sanitize_filename(
        Timer.time_string() + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    with (output_dir / "system.repr").open("w") as f:
        f.write(f"{system}\n")
    with (output_dir / "system.json").open("w") as f:
        f.write(f"{system.ui_to_json()}\n")
    system.load()
    logger.info("-" * 100)
    logger.info("-" * 100)
    logger.info("-" * 100)
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Args: {args}")
    logger.info(f"The {task.__class__} task has {len(task)} elements.")

    all_results = []
    last_repo = None
    for index, (inputs, repo) in tqdm.tqdm(
        enumerate(task), desc="System Generation", total=len(task)
    ):
        if repo != last_repo:
            logger.info(f"The {index}/{len(task)}-th: clean docs and add the new repo")
            system.clear_retriever()
            system.add_docs(repo)
        # Try to load the cache result
        cache_file = save_path = (
            output_dir / f"{task.name}-{index:05d}-{len(task):05d}-wo-exec.json"
        )
        if cache_file.exists():
            cache_data = ui_load_from_json_file(cache_file)
            if not isinstance(cache_data, dict):
                raise TypeError(
                    f"cache_data should be dict instead of {type(cache_data)}"
                )
            completion = cache_data["completion"]
            cache_index, prompt = cache_data["index"], cache_data["prompt"]
            assert cache_index == index
        else:
            completion_result = system.generate(inputs)
            completion = completion_result.generated_text
            prompt = system.model.tokenizer.detokenize(completion_result.prompt_tokens)
            ui_save_to_json_file(
                {
                    "index": index,
                    "inputs": inputs,
                    "prompt": prompt,
                    "completion": completion,
                    "retrieved_chunks": completion_result.retrieved_chunks,
                },
                cache_file,
            )
        all_results.append((index, inputs, completion))
        last_repo = repo

    task._cache_driver = Driver(
        local_timeout_secs=600,
        global_timeout_secs=900,
        hydra_block_resource_internet_access=False,
        max_parallel_runs=0,
    )
    with futures.ThreadPoolExecutor(max_workers=32) as executor:
        future_by_index = dict()
        for idx, inputs, completion in all_results:
            future = executor.submit(
                task.execute, model_input=inputs, generation=completion
            )
            future_by_index[idx] = future
            # future_gt = executor.submit(
            #     task.execute, model_input=inputs, generation=inputs.target
            # )
        logger.info(f"Collected {len(future_by_index)} nodes to be evaluated")

        all_passes = []
        for idx, future in tqdm.tqdm(
            future_by_index.items(), total=len(future_by_index)
        ):
            results, xpass = None, False
            try:
                results = future.result(timeout=900.0)
                xpass = results["pass"]
            except futures.TimeoutError:
                logger.info(
                    f"[!!!] {idx}/{len(future_by_index)}-th example is timeout."
                )
            except Exception as e:
                logger.info(
                    f"[!!!] {idx}/{len(future_by_index)}-th example raised an exception: {e}"
                )
            cache_file = (
                output_dir / f"{task.name}-{idx:05d}-{len(task):05d}-wo-exec.json"
            )
            final_file = (
                output_dir / f"{task.name}-{idx:05d}-{len(task):05d}-final.json"
            )
            cache_data = ui_load_from_json_file(cache_file)
            if not isinstance(cache_data, dict):
                raise TypeError(
                    f"cache_data should be dict instead of {type(cache_data)}"
                )
            cache_data["pass"] = xpass
            cache_data["results"] = results
            ui_save_to_json_file(cache_data, final_file)
            all_passes.append(xpass)
            logger.info(
                f"Pass={all_passes[-1]}, total pass={np.sum(all_passes).item():2d},"
                f" average pass rate={np.mean(all_passes, dtype=np.float32).item()*100:.2f}%"
            )


if __name__ == "__main__":
    main()
