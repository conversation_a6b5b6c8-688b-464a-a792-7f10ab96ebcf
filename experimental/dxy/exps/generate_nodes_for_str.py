"""Generate nodes that represent code snippets of meaning strings, such as docstring, comments, etc.

python experimental/dxy/exps/generate_nodes_for_str.py \
    --image_name google/pyglove:v1.0 \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v2

python experimental/dxy/exps/generate_nodes_for_str.py \
    --image_name pydantic/pydantic:v1.0 \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v2

python experimental/dxy/exps/generate_nodes_for_str.py \
    --image_name thealgorithms/python:v1.0 \
    --output_dir /mnt/efs/augment/user/dxy/data-patches-v2
"""
import argparse
import logging
import pathlib
import random
from typing import Callable

import torch
import tqdm

from research.core import utils_for_str
from research.core.utils import Timer
from research.data.eval.repo_lib.repo import parse_docs_from_docker_image
from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.models.all_models import get_formatter_by_model_name
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    AugmentedTSNode,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--image_name",
        type=str,
        required=True,
        help="The docker image name.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="The output directory.",
    )
    return parser.parse_args()


def prepare_to_start(image_name: str, output_dir: str):
    """Prepare the output directory and the logging file."""

    real_output_dir: pathlib.Path = pathlib.Path(
        output_dir
    ) / utils_for_str.sanitize_filename(image_name)
    real_output_dir.mkdir(parents=True, exist_ok=True)
    log_file = real_output_dir / (
        pathlib.Path(__file__).stem
        + "-"
        + Timer.time_string().replace("[", "").replace("]", "").replace(" ", "-")
        + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output directory is : {real_output_dir}")
    logger.info(f"The output log file is : {log_file}")
    return real_output_dir, logger


def save_within_lambda(
    docs: list[AugmentedParsedFile],
    lambda_expr: Callable[[AugmentedParsedFile, AugmentedTSNode], bool],
    lambda_name: str,
    image_name: str,
    output_path: pathlib.Path,
    logger: logging.Logger,
):
    """Save the documents and skip if no documents left."""
    final_docs = patch_generate_lib.keep_docs_by_lambda_expr(
        docs,
        lambda_expr=lambda_expr,
        lambda_name=lambda_name,
        logger=logger,
    )
    if patch_generate_lib.get_number_of_nodes(final_docs):
        torch.save(
            {
                "documents": [doc.to_dict() for doc in final_docs],
                "image_name": image_name,
            },
            output_path,
        )
        logger.info(f"After keep docs with {lambda_name}, save into {output_path}")
    else:
        logger.info(f"After keep docs with {lambda_name}, there are no document left.")


def main(args):
    """The real main function."""
    output_dir, logger = prepare_to_start(args.image_name, args.output_dir)
    documents, _, sha = parse_docs_from_docker_image(args.image_name, language="python")

    logger.info(f"Find {len(documents)} raw documents for python, SHA={sha}.")
    augmented_docs: list[AugmentedParsedFile] = []
    for doc in tqdm.tqdm(documents):
        augmented_docs.append(AugmentedParsedFile(doc))
    logger.info(
        f"There are {len(augmented_docs)} files with {len(documents)} documents."
    )

    # Filter out documents
    filtered_documents = patch_generate_lib.remove_docs_by_filename(
        augmented_docs,
        exclude_filename_substrs=(
            "_test",
            "test_",
            "tests",
        ),
        exclude_filename_parts=("docs",),
        logger=logger,
    )

    # Find all the comments
    docs_w_nodes = patch_generate_lib.find_all_meaningful_strs(
        filtered_documents, logger
    )

    # Filter out the duplicated nodes
    docs_w_nodes = patch_generate_lib.remove_nodes_for_duplication(
        docs_w_nodes, logger=logger
    )

    # Attach the number of #token info
    for doc in tqdm.tqdm(docs_w_nodes, desc="Attach the image name"):
        for node in doc.ts_nodes:
            node.extra["image_name"] = args.image_name
    formatter = get_formatter_by_model_name("starcoderbase")
    docs_w_nodes = patch_generate_lib.attach_number_of_tokens(
        docs_w_nodes, formatter=formatter
    )

    # Filter the number of tokens.
    docs_w_nodes = patch_generate_lib.keep_docs_by_lambda_expr(
        docs_w_nodes,
        lambda_expr=lambda _, node: 32 <= node.extra["text.#tokens"] <= 2048,
        lambda_name="#tokens in [32, 2048]",
        logger=logger,
    )
    docs_w_nodes = patch_generate_lib.keep_docs_by_lambda_expr(
        docs_w_nodes,
        lambda_expr=lambda doc, node: 1
        <= len(doc.get_text_of_node(node).split("\n"))
        <= 16,
        lambda_name="#lines in [1, 16]",
        logger=logger,
    )

    # Filter too many empty lines
    def _num_of_lines(
        doc: AugmentedParsedFile, node: AugmentedTSNode, count_empty: bool = True
    ):
        lines = doc.get_text_of_node(node).split("\n")
        lines = [x.replace("#", "") for x in lines]
        lines = [x.replace("'", "") for x in lines]
        lines = [x.replace('"', "") for x in lines]
        lines = [x.replace(" ", "") for x in lines]
        if count_empty:
            return sum(x == "" for x in lines)
        else:
            return sum(x != "" for x in lines)

    docs_w_nodes = patch_generate_lib.keep_docs_by_lambda_expr(
        docs_w_nodes,
        lambda_expr=lambda doc, node: _num_of_lines(doc, node, count_empty=True) <= 3,
        lambda_name="#empty lines <= 3",
        logger=logger,
    )
    docs_w_nodes = patch_generate_lib.keep_docs_by_lambda_expr(
        docs_w_nodes,
        lambda_expr=lambda doc, node: _num_of_lines(doc, node, count_empty=False) >= 2
        or node.extra["raw_label"] == "meaningful_str:argparse_argument_help",
        lambda_name="#non-empty lines >= 2",
        logger=logger,
    )

    cache_dir = output_dir / "target_nodes-strs"
    for doc in tqdm.tqdm(docs_w_nodes, desc="Cache the nodes"):
        doc.cache_nodes_to_dir(cache_dir)

    patch_generate_lib.show_statistics_of_nodes(docs_w_nodes, logger)

    # Save
    def _filter_fn(
        doc: AugmentedParsedFile, node: AugmentedTSNode, use_rand: bool = False
    ):
        if node.extra["raw_label"] == "meaningful_str:argparse_argument_help":
            return True
        elif node.extra["raw_label"] == "meaningful_str:class_docstr":
            return True if not use_rand else random.random() < 0.05
        elif 4 <= _num_of_lines(doc, node, count_empty=False) <= 8:
            return True if not use_rand else random.random() < 0.05
        else:
            return False

    save_within_lambda(
        docs_w_nodes,
        lambda_expr=_filter_fn,
        lambda_name="customization",
        image_name=args.image_name,
        output_path=output_dir / "meaningful-str-raw-v0.torch",
        logger=logger,
    )
    # Keep a small one to debug
    save_within_lambda(
        docs_w_nodes,
        lambda_expr=lambda doc, node: _filter_fn(doc, node, use_rand=True),
        lambda_name="Random Select",
        image_name=args.image_name,
        output_path=output_dir / "meaningful-str-raw-debug.torch",
        logger=logger,
    )
    logger.info(f"Recap the output directory is : {output_dir}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
