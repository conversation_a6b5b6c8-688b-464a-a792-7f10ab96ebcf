"""...

python experimental/dxy/exps/filter_nodes_by_sys_hydra.py \
    --input_dir /mnt/efs/augment/user/dxy/data-patches-v3/google_pyglove-v1.0/ \
    --basename api-raw-ge2lines_or_ge3paren

python experimental/dxy/exps/filter_nodes_by_sys_hydra.py \
    --input_dir /mnt/efs/augment/user/dxy/data-patches-v3/pydantic_pydantic-v1.0/ \
    --basename api-raw-ge2lines_or_ge3paren

python experimental/dxy/exps/filter_nodes_by_sys_hydra.py \
    --input_dir /mnt/efs/augment/user/dxy/data-patches-v3/thealgorithms_python-v1.0/ \
    --basename api-raw-ge2lines_or_ge3paren
"""
from __future__ import annotations

import argparse
import collections
import logging
import pathlib
from typing import Any

import torch

from research.core import utils_for_str
from research.core.types import Char<PERSON>ange
from research.core.utils import Timer
from research.eval.dataset_generation_lib import (
    advanced_snippet_generators as patch_generate_lib,
)
from research.eval.dataset_generation_lib.advanced_snippet_generators import (
    get_number_of_nodes,
)
from research.eval.generation import execution
from research.eval.hydra.driver import Driver
from research.eval.patch_lib import Patch
from research.static_analysis.experimental_parsing import (
    AugmentedParsedFile,
    AugmentedTSNode,
    StructuralAnalysis,
)


def parse_cmd():
    """Parse command line args."""
    parser = argparse.ArgumentParser()

    # System configuration
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help=".",
    )
    parser.add_argument(
        "--basename",
        type=str,
        required=True,
        help=".",
    )
    return parser.parse_args()


def prepare_to_start(output_dir: pathlib.Path) -> logging.Logger:
    """Prepare the output directory and the logging file."""
    output_dir.mkdir(exist_ok=True, parents=True)
    log_file = output_dir / utils_for_str.sanitize_filename(
        pathlib.Path(__file__).stem + Timer.time_string() + ".log"
    )
    logger = logging.Logger(__name__)
    handlers = (logging.FileHandler(log_file), logging.StreamHandler())
    formatter = logging.Formatter(
        "%(asctime)s - %(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s"
    )
    for handler in handlers:
        handler.setLevel(logging.INFO)
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    logger.info(f"The output log file is : {log_file}")
    return logger


def load_documents_from_file(path: pathlib.Path) -> list[AugmentedParsedFile]:
    load_data = torch.load(path)
    documents = load_data["documents"]
    documents = [AugmentedParsedFile.from_dict(x) for x in documents]
    return documents


def merge_document_nodes_extra(
    list_of_documents: list[list[AugmentedParsedFile]],
) -> list[AugmentedParsedFile]:
    docs = list_of_documents[0]
    for idx, new_docs in enumerate(list_of_documents[1:]):
        if len(docs) != len(new_docs):
            raise ValueError(
                f"The {idx+2}-th docs has different length {len(new_docs)}"
                f" than the first one {len(docs)}"
            )
        for idx_doc, (doc, new_doc) in enumerate(zip(docs, new_docs)):
            if len(doc.ts_nodes) != len(new_doc.ts_nodes):
                raise ValueError(
                    f"The {idx+2}-th docs's {idx_doc}-th doc has different ts_node"
                    f" length {len(doc.ts_nodes)} than the first"
                    f" one {len(new_doc.ts_nodes)}."
                )
            if doc.scope_tree.name != new_doc.scope_tree.name:
                raise ValueError(f"{doc.scope_tree.name} vs. {new_doc.scope_tree.name}")
            for idx_node, (node, new_node) in enumerate(
                zip(doc.ts_nodes, new_doc.ts_nodes)
            ):
                if doc.tsnode_to_crange(node) != new_doc.tsnode_to_crange(new_node):
                    raise ValueError(
                        f"The {idx+2}-th docs's {idx_doc}-th doc's {idx_node} node"
                        f" has different range {node} vs. {new_node}."
                    )
                diff_keys = set(new_node.extra.keys()) - set(node.extra.keys())
                for xkey in diff_keys:
                    node.extra[xkey] = new_node.extra[xkey]
    return docs


def check_generation(
    docs: list[AugmentedParsedFile], sys_name: str, logger: logging.Logger
):
    """Check the system's generation."""
    exact_match, startswith, contain, total = 0, 0, 0, 0
    for doc in docs:
        for node in doc.ts_nodes:
            expected_str = doc.get_text_of_node(node)
            generation = node.extra[sys_name]
            if expected_str == generation:
                exact_match += 1
            elif generation.startswith(expected_str):
                startswith += 1
            elif expected_str in generation:
                contain += 1
            total += 1
    logger.info(
        f"{sys_name} : exact_match = {exact_match} ({exact_match/total*100:.1f}%)"
        f" startswith = {startswith} ({startswith/total*100:.1f}%)"
        f" contain = {contain} ({contain/total*100:.1f}%)"
        f" total = {total}"
    )


def _get_pass_result_from_dict(xdict: dict) -> bool | None:
    if "result_str" not in xdict:
        return False
    result_str = xdict["result_str"]
    if result_str == "":
        return None
    parts = utils_for_str.extract_colored_parts(result_str)
    if len(parts) == 1:
        part = parts[0]
    else:
        return None
    if part == "PASSED":
        return True
    elif part in "FAILED":
        return False
    elif part in ("TIMEOUT", "OTHERS"):
        return None
    else:
        raise ValueError(f"{part}  <== {result_str}")


def check_hydra(docs: list[AugmentedParsedFile], logger: logging.Logger):
    """Check the system's hydra result."""

    key_to_fail = "hydra-patch_is_assert0=1"
    key_is_empty = "hydra-patch_is_empty"
    key_to_pass = "hydra-patch_to_pass"

    def _get_metric(xlist: list[bool]) -> str:
        value, total = sum(xlist), len(xlist)
        return f"{value/total*100:.2f} ({value:3d}/{total})"

    rates_to_fail = []
    rates_is_empty = []
    rates_to_pass = []

    for doc in docs:
        for node in doc.ts_nodes:
            if not (
                (node.extra[key_to_fail]["status"] == "complete")
                and (node.extra[key_is_empty]["status"] == "complete")
                and (node.extra[key_to_pass]["status"] == "complete")
            ):
                continue
            res = _get_pass_result_from_dict(node.extra[key_to_fail])
            if res is None:
                continue
            rates_to_fail.append(res)
            res = _get_pass_result_from_dict(node.extra[key_is_empty])
            if res is None:
                continue
            rates_is_empty.append(res)
            res = _get_pass_result_from_dict(node.extra[key_to_pass])
            if res is None:
                continue
            rates_to_pass.append(res)

    logger.info(
        f"Hydra : to_fail = {_get_metric(rates_to_fail)},"
        f" is_empty = {_get_metric(rates_to_fail)},"
        f" to_pass = {_get_metric(rates_to_pass)}"
    )


def show_status(documents: list[AugmentedParsedFile], logger: logging.Logger):
    all_node_extra_keys = set()
    for doc in documents:
        for node in doc.ts_nodes:
            for key in node.extra.keys():
                all_node_extra_keys.add(key)
    logger.info(
        f"In total, there are {len(documents)} docs with {get_number_of_nodes(documents)} nodes."
    )
    logger.info(f"All node extra keys: {all_node_extra_keys}")

    sys_names = (
        "sys-generation-by-rogue-6k",
        "sys-generation-by-fimv2-16b-4kl",
        "sys-generation-by-basic-sc-7b",
        "sys-generation-by-basic-sc-3b",
        # "sys-generation-by-basic-sc-1b",
    )

    for sys_name in sys_names:
        assert sys_name in all_node_extra_keys, f"{sys_name} vs {all_node_extra_keys}"
        check_generation(documents, sys_name=sys_name, logger=logger)
    check_hydra(documents, logger)

    # Show the statistic
    counters = {
        "left_cursor_loc": collections.Counter(),
        "right_cursor_loc": collections.Counter(),
        "completion_type": collections.Counter(),
    }
    for doc in documents:
        for node in doc.ts_nodes:
            crange = doc.tsnode_to_crange(node)
            prefix = doc.code[: crange.start]
            middle = doc.code[crange.start : crange.stop]
            suffix = doc.code[crange.stop :]
            left_cursor_loc = StructuralAnalysis.get_cursor_loc(prefix, middle + suffix)
            right_cursor_loc = StructuralAnalysis.get_cursor_loc(
                prefix + middle, suffix
            )
            completion_type = StructuralAnalysis.get_completion_typ(middle)
            counters["left_cursor_loc"][left_cursor_loc] += 1
            counters["right_cursor_loc"][right_cursor_loc] += 1
            counters["completion_type"][completion_type] += 1
    logger.info(f"left cursor loc: {counters['left_cursor_loc']}")
    logger.info(f"right cursor loc: {counters['right_cursor_loc']}")
    logger.info(f"completion type: {counters['completion_type']}")


def main(args):
    """The real main function."""
    args = parse_cmd()
    input_dir = pathlib.Path(args.input_dir)
    output_dir = input_dir / f"{args.basename}-final-nodes"

    logger = prepare_to_start(output_dir)
    logger.info(f"Args: {args}")
    logger.info(f"output_dir: {output_dir}")

    documents = load_documents_from_file(input_dir / f"{args.basename}.torch")

    documents = merge_document_nodes_extra(
        [
            documents,
            load_documents_from_file(
                input_dir / f"{args.basename}-sys-rogue-6k" / "all-results.torch"
            ),
            load_documents_from_file(
                input_dir / f"{args.basename}-sys-fimv2-16b-4kl" / "all-results.torch"
            ),
            load_documents_from_file(
                input_dir / f"{args.basename}-sys-basic-sc-7b" / "all-results.torch"
            ),
            load_documents_from_file(
                input_dir / f"{args.basename}-sys-basic-sc-3b" / "all-results.torch"
            ),
            load_documents_from_file(
                input_dir / f"{args.basename}-hydra-hydra" / "all-results.torch"
            ),
        ]
    )

    raw_load_data = torch.load(input_dir / f"{args.basename}.torch")
    show_status(documents, logger)
    output_file = output_dir / "merged-results.torch"
    torch.save(
        {
            "documents": [doc.to_dict() for doc in documents],
            "repo_for_system": raw_load_data["repo_for_system"],
        },
        output_file,
    )

    # Filtering
    key_to_fail = "hydra-patch_is_assert0=1"
    key_is_empty = "hydra-patch_is_empty"
    key_to_pass = "hydra-patch_to_pass"
    filtered_docs = []
    for doc in documents:
        new_nodes = []
        for node in doc.ts_nodes:
            if not (
                (node.extra[key_to_fail]["status"] == "complete")
                and (node.extra[key_is_empty]["status"] == "complete")
                and (node.extra[key_to_pass]["status"] == "complete")
            ):
                continue
            if _get_pass_result_from_dict(node.extra[key_to_pass]) != True:
                continue
            if _get_pass_result_from_dict(node.extra[key_to_fail]):
                continue
            if _get_pass_result_from_dict(node.extra[key_is_empty]):
                continue
            target_completion = doc.get_text_of_node(node)
            if node.extra["sys-generation-by-basic-sc-3b"].startswith(
                target_completion
            ):
                continue
            new_nodes.append(node)
        if new_nodes:
            filtered_docs.append(AugmentedParsedFile(doc.doc, new_nodes, doc.type))

    logger.info("-" * 100)
    logger.info("-" * 100)

    show_status(filtered_docs, logger)

    output_file = output_dir / "filtered-results.torch"
    torch.save(
        {
            "documents": [doc.to_dict() for doc in filtered_docs],
            "repo_for_system": raw_load_data["repo_for_system"],
        },
        output_file,
    )
    cache_dir = output_dir / "cache"
    cache_dir.mkdir(parents=True, exist_ok=True)
    for idx_doc, doc in enumerate(filtered_docs):
        try:
            doc.cache_nodes_to_dir(cache_dir)
        except UnicodeEncodeError as e:
            logger.info(
                f"Skip cache the {idx_doc}/{len(filtered_docs)}-th doc due to {e}"
            )
    logger.info(f"Save the cache into {cache_dir}")


if __name__ == "__main__":
    args = parse_cmd()
    main(args)
