{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Elden V4.3's (partial) training data is at `/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers import StarCoder2Tokenizer\n", "\n", "datapath = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset\"\n", "\n", "# Load this dataset via IndexedDataset.\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)\n", "print(f\"The dataset has {len(dataset)} records.\")\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "# Show some special tokens\n", "print(f\"{special_tokens.eos = }\")\n", "print(f\"{special_tokens.pause = }\")\n", "print(f\"{special_tokens.skip = }\")\n", "print(f\"{special_tokens.padding = }\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_prompt_target(tokens: list[int], tokenizer) -> tuple[list[int], list[int]]:\n", "    \"\"\"Get the prompt and target from the tokens.\"\"\"\n", "    # Remove the padding tokens.\n", "    tokens = tokens[: tokens.index(special_tokens.padding)]\n", "    fim_middle_token = tokenizer.special_tokens.fim_middle\n", "    assert fim_middle_token in tokens, \"The fim_middle token is not in the tokens.\"\n", "    index = tokens.index(fim_middle_token)\n", "    prompt_tokens = tokens[: index + 1]\n", "    target_tokens = tokens[index + 1 :]\n", "    return prompt_tokens, target_tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 3\n", "prompt_tokens, target_tokens = get_prompt_target(dataset[index].tolist(), tokenizer)\n", "print(tokenizer.detokenize(prompt_tokens))\n", "print(\"\\n\" + \"-\" * 100 + \"\\nTarget:\\n\")\n", "print(tokenizer.detokenize(target_tokens))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The Elden-V4.3's FBW checkpoint is at: `/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1` without model parallelism,"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'fastbackward', 'checkpoint_path': '/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1', 'model_parallel_size': 1, 'override_tokenizer': 'starcoder2', 'seq_length': 7936}\n", "<base.tokenizers.starcoder2_tokenizer.StarCoder2Tokenizer object at 0x7f4161a09f10>\n"]}], "source": ["# This is an example code to load it.\n", "from research.eval.harness import factories\n", "\n", "model_config = {\n", "    \"name\": \"fastbackward\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp1\",\n", "    \"model_parallel_size\": 1,\n", "    \"override_tokenizer\": \"starcoder2\",\n", "    \"seq_length\": 7936,\n", "}\n", "# The prompt formatter is incorrect so that please only use the token-based interface.\n", "model = factories.create_model(model_config)\n", "# Check if the tokenizer is correct -> this is important\n", "print(model.tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.meta_model import GenerationOptions\n", "\n", "generation_options = GenerationOptions(temperature=1.0, top_p = 0.9, max_generated_tokens=64)\n", "results = model.raw_generate_tokens(prompt_tokens, options=generation_options)\n", "print(tokenizer.detokenize(results.tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = model.raw_generate_tokens(prompt_tokens, options=generation_options)\n", "print(tokenizer.detokenize(results.tokens))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}