"""A wrapper of the CUTLASS linear layer."""

import torch
import cutlass
import cutlass.op as cutlass_op


class CutlassFp8Linear(torch.nn.Module):
    """A wrapper of the CUTLASS linear layer."""

    def __init__(
        self,
        in_features: int,
        out_features: int,
    ):
        super().__init__()
        self.plan = cutlass_op.Gemm(
            element=torch.float8_e4m3fn,
            element_accumulator=torch.float32,
            layout_A=cutlass.LayoutType.RowMajor,
            layout_B=cutlass.LayoutType.ColumnMajor,
            layout_C=cutlass.LayoutType.ColumnMajor,
        )
        self.in_features = in_features
        self.out_features = out_features
        self.weights = None
        self.cached_C = torch.zeros(
            8192 * 2, out_features, dtype=torch.float8_e4m3fn, device="cuda"
        )
        self.cached_D = torch.zeros(
            8192 * 2, out_features, dtype=torch.float8_e4m3fn, device="cuda"
        )

    def forward(self, inputs: torch.Tensor) -> torch.Tensor:
        assert self.weights != None
        return self.plan.run(
            A=inputs,
            B=self.weights,
            C=self.cached_C[: inputs.shape[0]],
            D=self.cached_D[: inputs.shape[0]],
            alpha=1.0,
            beta=0.0,
        )
