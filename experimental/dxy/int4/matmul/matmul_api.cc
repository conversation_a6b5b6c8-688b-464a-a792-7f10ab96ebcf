/*
 * C++ APIs
 */

#include <string>
#include "pybind11/pybind11.h"
#include "pybind11/stl.h"

namespace cutie {
namespace matmul {

template<typename T>
void dispatch(
    const uint64_t a,
    const uint64_t b,
    const uint64_t c,
    const int m,
    const int n,
    const int k
);


void entry(
    const uint64_t a,
    const uint64_t b,
    const uint64_t c,
    const int m,
    const int n,
    const int k,
    const std::string& dtype_str);


}  // namespace matmul
}  // namespace cutie


PYBIND11_MODULE(matmul_api, m) {
    m.def("matmul", &cutie::matmul::entry);
}
