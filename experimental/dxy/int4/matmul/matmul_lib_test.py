"""Tests for MatMul.

bazel test :matmul_lib_test \
  --test_output="all" \
  --test_arg="-k test_correctness" \
  --test_arg="-s" \
"""

import pytest
import torch

from base.fastforward.torch_utils import cuda_timeit
from experimental.hieu.cutie.matmul import matmul_lib

_DTYPE_DICT: dict[torch.dtype, str] = {
    torch.float32: "fp32",
    torch.float16: "fp16",
    torch.bfloat16: "bf16",
}


@pytest.mark.parametrize(
    "shape, dtype",
    [
        pytest.param((32,), torch.float32, id="fp32/32"),
        pytest.param((64,), torch.float32, id="fp32/64"),
        pytest.param((128,), torch.float32, id="fp32/128"),
        pytest.param((256,), torch.float32, id="fp32/256"),
        pytest.param((64, 32, 32), torch.float32, id="fp32/64-32-32"),
        pytest.param((64, 64, 32), torch.float32, id="fp32/64-64-32"),
        pytest.param((128, 128, 32), torch.float32, id="fp32/128-128-32"),
        pytest.param((32, 32, 1024), torch.float32, id="fp32/32-32-1024"),
        pytest.param((32,), torch.float16, id="fp16/32"),
        pytest.param((64,), torch.float16, id="fp16/64"),
        pytest.param((128,), torch.float16, id="fp16/128"),
        pytest.param((256,), torch.float16, id="fp16/256"),
        pytest.param((64, 32, 32), torch.float16, id="fp16/64-32-32"),
        pytest.param((64, 64, 32), torch.float16, id="fp16/64-64-32"),
        pytest.param((128, 128, 32), torch.float16, id="fp16/128-128-32"),
        pytest.param((32, 32, 1024), torch.float16, id="fp16/32-32-1024"),
        pytest.param((32,), torch.bfloat16, id="bf16/32"),
        pytest.param((64,), torch.bfloat16, id="bf16/64"),
        pytest.param((128,), torch.bfloat16, id="bf16/128"),
        pytest.param((256,), torch.bfloat16, id="bf16/256"),
        pytest.param((64, 32, 32), torch.bfloat16, id="bf16/64-32-32"),
        pytest.param((64, 64, 32), torch.bfloat16, id="bf16/64-64-32"),
        pytest.param((128, 128, 32), torch.bfloat16, id="bf16/128-128-32"),
        pytest.param((32, 32, 1024), torch.bfloat16, id="bf16/32-32-1024"),
    ],
)
def test_correctness(shape: tuple[int], dtype: torch.dtype):
    torch.manual_seed(31415)

    torch.set_printoptions(precision=2, sci_mode=True, linewidth=300, threshold=1000)

    if len(shape) == 1:
        m = n = k = shape[0]
    elif len(shape) == 3:
        m, n, k = shape

    x = torch.zeros(m, k, dtype=dtype, device="cuda")
    y = torch.zeros(n, k, dtype=dtype, device="cuda")

    x.uniform_(-0.2, 0.2)
    y.uniform_(-0.2, 0.2)

    # x.fill_(1.)
    # y.fill_(1.)

    ref = torch.matmul(x, y.T)

    # a, b, c, d = 0, 4, 0, 4
    # print()
    # print(x[a:b, c:d])
    # print(y[a:b, c:d])
    # print(ref[a:b, c:d], flush=True)

    out = matmul_lib.matmul(x, y)
    # print("", flush=True)
    # print(out[a:b, c:d], flush=True)

    assert list(out.size()) == list(ref.size())
    if dtype == torch.float32:
        torch.testing.assert_close(out, ref, atol=1e-3, rtol=1e-3)
    elif dtype == torch.float16:
        torch.testing.assert_close(out, ref, atol=1e-2, rtol=1e-2)
    elif dtype == torch.bfloat16:
        torch.testing.assert_close(out, ref, atol=1e-2, rtol=1e-2)


@pytest.mark.parametrize(
    "shape, dtype",
    [
        pytest.param((2**6,), torch.float32, id="fp32/64"),
        pytest.param((2**7,), torch.float32, id="fp32/128"),
        pytest.param((2**8,), torch.float32, id="fp32/256"),
        pytest.param((2**9,), torch.float32, id="fp32/512"),
        pytest.param((2**10,), torch.float32, id="fp32/1024"),
        pytest.param((2**11,), torch.float32, id="fp32/2048"),
        pytest.param((2**12,), torch.float32, id="fp32/4096"),
        pytest.param((2**13,), torch.float32, id="fp32/8192"),
        pytest.param((2**14,), torch.float32, id="fp32/16384"),
        pytest.param((2**15,), torch.float32, id="fp32/32768"),
        pytest.param((2**6,), torch.float16, id="fp16/64"),
        pytest.param((2**7,), torch.float16, id="fp16/128"),
        pytest.param((2**8,), torch.float16, id="fp16/256"),
        pytest.param((2**9,), torch.float16, id="fp16/512"),
        pytest.param((2**10,), torch.float16, id="fp16/1024"),
        pytest.param((2**11,), torch.float16, id="fp16/2048"),
        pytest.param((2**12,), torch.float16, id="fp16/4096"),
        pytest.param((2**13,), torch.float16, id="fp16/8192"),
        pytest.param((2**14,), torch.float16, id="fp16/16384"),
        pytest.param((2**6,), torch.bfloat16, id="bf16/64"),
        pytest.param((2**7,), torch.bfloat16, id="bf16/128"),
        pytest.param((2**8,), torch.bfloat16, id="bf16/256"),
        pytest.param((2**9,), torch.bfloat16, id="bf16/512"),
        pytest.param((2**10,), torch.bfloat16, id="bf16/1024"),
        pytest.param((2**11,), torch.bfloat16, id="bf16/2048"),
        pytest.param((2**12,), torch.bfloat16, id="bf16/4096"),
        pytest.param((2**13,), torch.bfloat16, id="bf16/8192"),
        pytest.param((2**14,), torch.bfloat16, id="bf16/16384"),
    ],
)
def test_runtime(shape: tuple[int], dtype: torch.dtype):
    torch.manual_seed(hash(f"runtime-{dtype}-{shape}"))

    if len(shape) == 1:
        m = n = k = shape[0]
    elif len(shape) == 3:
        m, n, k = shape

    def _tflops(t_ms: float) -> float:
        return m * n * k * 2. / (t_ms * 1e-3) / 1e12

    x = torch.zeros(m, k, dtype=dtype, device="cuda")
    y = torch.zeros(n, k, dtype=dtype, device="cuda")

    x.uniform_(-0.1, 0.1)
    y.uniform_(-0.1, 0.1)

    # verify correctness before measuring
    ref = torch.matmul(x, y.T)
    out = matmul_lib.matmul(x, y)
    if dtype == torch.float32:
        torch.testing.assert_close(out, ref, atol=1e-3, rtol=1e-3)
    elif dtype == torch.float16:
        pass
        # torch.testing.assert_close(out, ref, atol=1e-2, rtol=1e-2)
    elif dtype == torch.bfloat16:
        torch.testing.assert_close(out, ref, atol=1e-2, rtol=1e-2)

    def _torch_step():
        torch.matmul(x, y.T)

    def _cutie_step():
        matmul_lib.matmul(x, y)

    median_latency_ms_cutie, _ = cuda_timeit(_cutie_step, repeats=5)
    median_latency_ms_torch, _ = cuda_timeit(_torch_step, repeats=5)

    tflops_cutie = _tflops(median_latency_ms_cutie)
    tflops_torch = _tflops(median_latency_ms_torch)

    print(
        "\x1b[32;20m\n"
        f"{_DTYPE_DICT[dtype]:<6}"
        f"m={m:<6d}"
        f"n={n:<6d}"
        f"k={k:<6d}"
        f"torch_ms={median_latency_ms_torch:<10.2f}"
        f"cutie_ms={median_latency_ms_cutie:<10.2f}"
        f"torch_tflops={tflops_torch:<8.1f}"
        f"cutie_tflops={tflops_cutie:<8.1f}"
        "\x1b[0m",
        flush=True,
    )
