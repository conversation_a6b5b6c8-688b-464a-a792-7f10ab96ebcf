"""Matrix transpose library."""
# pylint: disable=no-name-in-module

import torch

from experimental.hieu.cutie.matmul import matmul_api  # type: ignore


def matmul(x: torch.Tensor, y: torch.Tensor) -> torch.Tensor:
    (m, k), n = x.size(), y.size(0)
    result = torch.empty(m, n, dtype=x.dtype, device=x.device)

    dtype_str = {
        torch.float32: "fp32",
        torch.float16: "fp16",
        torch.bfloat16: "bf16",
    }[x.dtype]

    matmul_api.matmul(x.data_ptr(), y.data_ptr(), result.data_ptr(), m, n, k, dtype_str)

    return result
