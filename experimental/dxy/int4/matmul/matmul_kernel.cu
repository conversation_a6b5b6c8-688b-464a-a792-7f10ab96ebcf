/*
 * Matrix transpose kernel.
 */

#include <cstdio>

#include "cutlass/cluster_launch.hpp"
#include "cute/tensor.hpp"
#include "cute/arch/cluster_sm90.hpp"

#include "cuda.h"

// #define _MATMUL_KERNEL_DEBUG

#ifndef DEBUG_START
  #define DEBUG_START  printf("\033[33m");
#endif

#ifndef DEBUG_FINAL
  #define DEBUG_FINAL  printf("\033[0m");
#endif

#ifdef _MATMUL_KERNEL_DEBUG
  #define _LOG_THR 0
  #define _LOG_CTA 0
#endif

namespace cutie {
namespace matmul {

using namespace cute;

void set_kernel_smem_size(void const* kernel_ptr, const int smem_size) {
    if (smem_size >= (48 << 10)) {
        cudaError_t result = cudaFuncSetAttribute(
            kernel_ptr,
            cudaFuncAttributeMaxDynamicSharedMemorySize,
            smem_size);
        if (result != cudaSuccess) {
            result = cudaGetLastError();  // to clear the error bit
            std::cout << "cudaFuncSetAttribute() returned error: "
                      << cudaGetErrorString(result);
        }
    }
}


namespace ampere {

template <
    typename T,
    int Alignment,
    class SmemLayoutA,
    class SmemLayoutB>
struct SharedStorage {
    cute::array_aligned<T, cute::cosize_v<SmemLayoutA>, Alignment> smem_a;
    cute::array_aligned<T, cute::cosize_v<SmemLayoutB>, Alignment> smem_b;
};


template <
    typename T,
    typename SmemT,
    class SharedStorage,
    class TiledCopy,
    class TiledMma,
    class SmemLayoutA,
    class SmemLayoutB,
    class TensorA,
    class TensorB,
    class TensorC>
__global__ void kernel(TensorA A, TensorB B, TensorC C) {
    constexpr int BLK_M = size<0>(SmemLayoutA{});
    constexpr int BLK_N = size<0>(SmemLayoutB{});
    constexpr int BLK_K = size<1>(SmemLayoutB{});

    const int thr_idx = threadIdx.x;
    const int cta_x = blockIdx.x;
    const int cta_y = blockIdx.y;

    extern __shared__ char shared_memory[];
    SharedStorage &shared_storage = *reinterpret_cast<SharedStorage*>(shared_memory);

    auto sA = make_tensor(make_smem_ptr(shared_storage.smem_a.data()), SmemLayoutA{});
    auto sB = make_tensor(make_smem_ptr(shared_storage.smem_b.data()), SmemLayoutB{});

    auto gA = local_tile(A, Shape<Int<BLK_M>, Int<BLK_K>>{}, make_coord(cta_x, _));
    auto gB = local_tile(B, Shape<Int<BLK_N>, Int<BLK_K>>{}, make_coord(cta_y, _));
    auto gC = local_tile(C, Shape<Int<BLK_M>, Int<BLK_N>>{}, make_coord(cta_x, cta_y));

    // #ifdef _MATMUL_KERNEL_DEBUG
    // if (thread(0)) {
    //     fill(sA, 8.);
    //     fill(sB, 9.);
    // }
    // #endif

    auto tiled_cpy = TiledCopy{};
    auto thr_cpy = tiled_cpy.get_slice(thr_idx);

    auto tiled_mma = TiledMma{};
    auto thr_mma = tiled_mma.get_slice(thr_idx);
    auto tAsA = thr_mma.partition_A(sA);
    auto tBsB = thr_mma.partition_B(sB);
    auto tCgC = thr_mma.partition_C(gC);

    auto rA = make_fragment_like(tAsA);
    auto rB = make_fragment_like(tBsB);
    auto rC = partition_fragment_C(tiled_mma, shape(gC));
    clear(rC);

    using CopyAtomS2R = Copy_Atom<UniversalCopy<SmemT>, SmemT>;
    auto tiled_cpy_s2r_a = make_tiled_copy_A(CopyAtomS2R{}, tiled_mma);
    auto tiled_cpy_s2r_b = make_tiled_copy_B(CopyAtomS2R{}, tiled_mma);

    auto thr_cpy_a = tiled_cpy_s2r_a.get_slice(thr_idx);
    auto thr_cpy_b = tiled_cpy_s2r_b.get_slice(thr_idx);

    for (int k = 0; k < size<2>(gA); ++k) {
        // TODO(hieu): for large inputs, these are the most expensive step!
        copy(tiled_cpy, thr_cpy.partition_S(gA(_, _, k)), thr_cpy.partition_D(sA));
        copy(tiled_cpy, thr_cpy.partition_S(gB(_, _, k)), thr_cpy.partition_D(sB));
        cp_async_wait<0>();

        // TODO(hieu): these copies are probably expensive. consider Swizzle.
        copy(tiled_cpy_s2r_a, thr_cpy_a.partition_S(sA), thr_cpy_a.retile_D(rA));
        copy(tiled_cpy_s2r_b, thr_cpy_b.partition_S(sB), thr_cpy_b.retile_D(rB));
        gemm(tiled_mma, rA, rB, rC);
    }
    copy(rC, tCgC);

    #ifdef _MATMUL_KERNEL_DEBUG
    if (thread(_LOG_THR, _LOG_CTA)) {
        DEBUG_START;
        printf("\n----------------------------------------\n");
        print("gA: ");  print(gA); print("\n");
        print("sA: ");  print(sA); print("\n");
        print("rA: ");  print(rA); print("\n");

        printf("\n----------------------------------------\n");
        print("gB: ");  print(gB); print("\n");
        print("sB: ");  print(sB); print("\n");
        print("rB: ");  print(rB); print("\n");

        // printf("\n----------------------------------------\n");
        // print("partition_gA: "); print(thr_cpy.partition_S(gA(_, _, 0))); print("\n");
        // print("partition_sA: "); print(thr_cpy.partition_D(sA));          print("\n");

        // printf("\n----------------------------------------\n");
        // print("partition_gB: "); print(thr_cpy.partition_S(gB(_, _, 0))); print("\n");
        // print("partition_sB: "); print(thr_cpy.partition_D(sB));          print("\n");

        printf("\n----------------------------------------\n");
        print("gC: ");   print(gC);   print("\n");
        print("tCgC: "); print(tCgC); print("\n");
        print("rC: ");   print(rC);   print("\n");

        printf("\n----------------------------------------\n");
        print("gA_copy: "); print(thr_cpy.partition_S(gA(_, _, 0))); print("\n");
        print("sA_copy: "); print(thr_cpy.partition_D(sA)); print("\n");
        DEBUG_FINAL;
    }
    #endif
}

}  // namespace ampere


namespace hopper {

template <
    class ElemA,
    class ElemB,
    class SmemLayoutA,
    class SmemLayoutB,
    int Alignment = 32>
struct SharedStorage {
    cute::array_aligned<ElemA, cute::cosize_v<SmemLayoutA>, Alignment> smem_a;
    cute::array_aligned<ElemB, cute::cosize_v<SmemLayoutB>, Alignment> smem_b;
    uint64_t tma_mbar[1];
};

template <
    typename T,
    class SharedStorage,
    class TiledMma,
    class SmemLayoutA,
    class SmemLayoutB,
    class TmaA,
    class TmaB,
    class TmaC,
    class TensorA,
    class TensorB,
    class TensorC>
__global__ void kernel(
    const TensorA A,
    const TensorB B,
    const TensorC C,
    const __grid_constant__ TmaA tma_a,
    const __grid_constant__ TmaB tma_b,
    const __grid_constant__ TmaC tma_c) {

    static_assert(size<1>(SmemLayoutA{}) == size<1>(SmemLayoutB{}));
    using BLK_M = Int<size<0>(SmemLayoutA{})>;
    using BLK_N = Int<size<0>(SmemLayoutB{})>;
    using BLK_K = Int<size<1>(SmemLayoutA{})>;

    const int thr_idx = threadIdx.x;
    const int cta_x = blockIdx.x;
    const int cta_y = blockIdx.y;

    extern __shared__ char shared_memory[];
    SharedStorage &shared_storage = *reinterpret_cast<SharedStorage*>(shared_memory);
    uint64_t *tma_mbar = shared_storage.tma_mbar;

    auto sA = make_tensor(make_smem_ptr(shared_storage.smem_a.data()), SmemLayoutA{});
    auto sB = make_tensor(make_smem_ptr(shared_storage.smem_b.data()), SmemLayoutB{});

    constexpr int tma_transaction_bytes_a = size(sA) * sizeof_bits_v<T> / 8;
    constexpr int tma_transaction_bytes_b = size(sB) * sizeof_bits_v<T> / 8;

    #ifdef _MATMUL_KERNEL_DEBUG
    if (thread(_LOG_THR, _LOG_CTA)) {
        fill(sA, T(5.));
        fill(sB, T(7.));
    }
    #endif

    auto gA = local_tile(
        tma_a.get_tma_tensor(shape(A)), Shape<BLK_M, BLK_K>{}, make_coord(cta_x, _));

    auto gB = local_tile(
        tma_b.get_tma_tensor(shape(B)), Shape<BLK_N, BLK_K>{}, make_coord(cta_y, _));

    auto gC = local_tile(C, Shape<BLK_M, BLK_N>{}, make_coord(cta_x, cta_y));

    auto thr_tma_a = tma_a.get_slice(0);
    auto thr_tma_b = tma_b.get_slice(0);

    const bool warp_predicate = cutlass::canonical_warp_idx_sync() == 0;
    const bool lane_predicate = cute::elect_one_sync();

    auto tma_copy = [&](const int k) -> void{
        if (warp_predicate and lane_predicate) {
            cute::initialize_barrier(tma_mbar[0], 1);
            cute::set_barrier_transaction_bytes(
                tma_mbar[0], tma_transaction_bytes_a + tma_transaction_bytes_b);

            cute::copy(
                tma_a.with(tma_mbar[0]),
                thr_tma_a.partition_S(gA(_, _, k)),
                thr_tma_a.partition_D(sA));

            cute::copy(
                tma_b.with(tma_mbar[0]),
                thr_tma_b.partition_S(gB(_, _, k)),
                thr_tma_b.partition_D(sB));
        }
        __syncthreads();
        cute::wait_barrier(tma_mbar[0], 0);
    };

    auto tiled_mma = TiledMma{};
    auto thr_mma = tiled_mma.get_slice(thr_idx);
    auto tCsA = thr_mma.partition_A(sA);
    auto tCsB = thr_mma.partition_B(sB);

    auto rA = thr_mma.make_fragment_A(tCsA);
    auto rB = thr_mma.make_fragment_B(tCsB);
    clear(rA);
    clear(rB);

    auto tCgC = thr_mma.partition_C(gC);
    auto rC = partition_fragment_C(tiled_mma, Shape<BLK_M, BLK_N>{});
    clear(rC);

    auto thr_gemm = [&](void) -> void {
        cute::warpgroup_fence_operand(rC);
        cute::warpgroup_arrive();

        cute::gemm(tiled_mma, rA, rB, rC);
        cute::warpgroup_commit_batch();

        cute::warpgroup_wait<0>();
        cute::warpgroup_fence_operand(rC);
    };

    // TOOD(hieu): implement pipelining
    const int num_stages = size<1>(A) / BLK_K::value;
    for (int k = 0; k < num_stages; ++k) {
        tma_copy(k);
        thr_gemm();
    }
    copy(rC, tCgC);

    #ifdef _MATMUL_KERNEL_DEBUG
    if (thread(_LOG_THR, _LOG_CTA)) {
        printf("\n");

        DEBUG_START;
        printf("\n----------------------------------------\n");
        printf("tma_transaction_bytes_a=%d\n", tma_transaction_bytes_a);
        printf("tma_transaction_bytes_b=%d\n", tma_transaction_bytes_b);

        printf("\n----------------------------------------\n");
        print("gA: "); print(gA); print("\n");
        print("gA_partition: ");  print(thr_tma_a.partition_S(gA(_, _, 0))); print("\n");
        print("sA: "); print(sA); print("\n");
        print("rA: "); print(rA); print("\n");

        printf("\n----------------------------------------\n");
        print("gC: ");   print(gC);   print("\n");
        print("rC: ");   print(rC);   print("\n");
        print("tCgC: "); print(tCgC); print("\n");

        DEBUG_FINAL;
    }
    #endif
}  // hopper::kernel

}  // namespace hopper


template<
    typename T,
    typename SmemT = T,
    int BLK_M = 16,
    int BLK_N = 16,
    int BLK_K = 16,
    bool SM90 = false>
void dispatch(
    const uint64_t a,
    const uint64_t b,
    const uint64_t c,
    const int m,
    const int n,
    const int k) {

    auto ceil_div = [](uint32_t x, uint32_t y) -> uint32_t { return (x + y - 1) / y; };

    const T* a_ptr = reinterpret_cast<const T*>(a);
    const T* b_ptr = reinterpret_cast<const T*>(b);
    T* c_ptr = reinterpret_cast<T*>(c);

    auto mA = make_tensor(
        make_gmem_ptr(a_ptr), make_layout(make_shape(m, k), GenRowMajor{})
    );
    auto mB = make_tensor(
        make_gmem_ptr(b_ptr), make_layout(make_shape(n, k), GenRowMajor{})
    );
    auto mC = make_tensor(
        make_gmem_ptr(c_ptr), make_layout(make_shape(m, n), GenRowMajor{})
    );

    if constexpr (SM90) {
        // TiledMma
        auto mma_atom = cute::GMMA::ss_op_selector<
            T,
            T,
            float,
            Shape<Int<BLK_M * 2>, Int<BLK_N>, Int<BLK_K>>,
            cute::GMMA::Major::K,
            cute::GMMA::Major::K>();
        using TiledMma = decltype(
            make_tiled_mma(mma_atom, Layout<Shape<_2, _1, _1>>{})
        );

        using SmemAtomLayout = cute::GMMA::Layout_K_SW128_Atom<T>;
        using SmemLayoutA = decltype(
            tile_to_shape(SmemAtomLayout{}, Shape<Int<BLK_M>, Int<BLK_K>>{})
        );
        using SmemLayoutB = decltype(
            tile_to_shape(SmemAtomLayout{}, Shape<Int<BLK_N>, Int<BLK_K>>{})
        );
        using SmemLayoutC = Layout<
            Shape<Int<BLK_M>, Int<BLK_N>>,
            Stride<Int<BLK_N>, _1>
        >;

        // TMA
        auto tma_a = make_tma_copy(SM90_TMA_LOAD{}, mA, SmemLayoutA{});
        auto tma_b = make_tma_copy(SM90_TMA_LOAD{}, mB, SmemLayoutB{});
        auto tma_c = make_tma_copy(SM90_TMA_STORE{}, mC, SmemLayoutC{});

        #ifdef _MATMUL_KERNEL_DEBUG
        DEBUG_START;
        printf("\n--------------------\n");
        print("TiledMma: ");   print(TiledMma{});             printf("\n");
        print("tile_shape: "); print(tile_shape(TiledMma{})); printf("\n");

        printf("\n--------------------\n");
        print("tma_c: "); print(tma_c); printf("\n");
        DEBUG_FINAL;
        #endif

        // kernel launch
        using SharedStorage = hopper::SharedStorage<
            typename TiledMma::Atom::ValTypeA,
            typename TiledMma::Atom::ValTypeB,
            SmemLayoutA,
            SmemLayoutB>;
        constexpr int smem_size = int(sizeof(SharedStorage));
        dim3 grd_dim{ceil_div(m, BLK_M), ceil_div(n, BLK_N)};
        dim3 cta_dim{size_v<typename TiledMma::Atom::ThrID>};
        cutlass::ClusterLaunchParams launch_params{grd_dim, cta_dim, 1, smem_size};

        void const* kernel_ptr = (void const*)hopper::kernel<
            T,
            SharedStorage,
            TiledMma,
            SmemLayoutA,
            SmemLayoutB,
            decltype(tma_a),
            decltype(tma_b),
            decltype(tma_c),
            decltype(mA),
            decltype(mB),
            decltype(mC)>;
        set_kernel_smem_size(kernel_ptr, smem_size);

        cutlass::Status status = cutlass::launch_kernel_on_cluster(
            launch_params, kernel_ptr, mA, mB, mC, tma_a, tma_b, tma_c);
        cudaDeviceSynchronize();
        if (status != cutlass::Status::kSuccess) {
            printf("%s\n", cutlassGetStatusString(status));
        }

    } else {
        assert(m % BLK_M == 0);
        assert(n % BLK_N == 0);
        assert(k % BLK_K == 0);

        // TiledMma
        using MmaAtom = std::conditional_t<
            is_same_v<T, float>,
            MMA_Traits<SM80_16x8x8_F32TF32TF32F32_TN>,
            std::conditional_t<
                is_same_v<T, half_t>,
                MMA_Traits<SM80_16x8x16_F32F16F16F32_TN>,
                MMA_Traits<SM80_16x8x16_F32BF16BF16F32_TN>
            >
        >;

        // static_assert(BLK_M % size<0>(typename MmaAtom::Shape_MNK{}) == 0);
        // static_assert(BLK_N % size<1>(typename MmaAtom::Shape_MNK{}) == 0);
        // static_assert(BLK_K % size<2>(typename MmaAtom::Shape_MNK{}) == 0);

        using TiledMma = decltype(
            make_tiled_mma(
                MmaAtom{},
                Layout<Shape<_1, _1, _1>>{},
                Tile<Int<BLK_M>, Int<BLK_N>, Int<BLK_K>>{}
            )
        );

        #ifdef _MATMUL_KERNEL_DEBUG
        DEBUG_START;
        printf("\n--------------------\n");
        print("TiledMma: ");   print(TiledMma{});             printf("\n");
        print("tile_shape: "); print(tile_shape(TiledMma{})); printf("\n");
        DEBUG_FINAL;
        #endif

        using SmemLayoutA = decltype(
            make_layout(Shape<Int<BLK_M>, Int<BLK_K>>{}, GenRowMajor{})
        );
        using SmemLayoutB = decltype(
            make_layout(Shape<Int<BLK_N>, Int<BLK_K>>{}, GenRowMajor{})
        );

        using ThrLayoutMMA = typename TiledMma::ThrLayoutVMNK;
        static constexpr int CTA_SIZE = size_v<ThrLayoutMMA>;

        // TiledCopy based on TiledMma, as we need ThrLayout
        using CopyType = std::conditional_t<is_same_v<T, float>, uint128_t, uint64_t>;
        static constexpr int VEC_SIZE = sizeof_bits_v<CopyType> / sizeof_bits_v<T>;

        #ifdef _MATMUL_KERNEL_DEBUG
        DEBUG_START;
        printf("\nCTA_SIZE=%d\n", CTA_SIZE);
        printf("\nVEC_SIZE=%d\n", VEC_SIZE);
        DEBUG_FINAL;
        #endif

        using CopyAtom = Copy_Atom<SM80_CP_ASYNC_CACHEALWAYS<CopyType>, SmemT>;
        using ThrLayoutCPY = decltype(
            make_layout(
                Shape<Int<CTA_SIZE / VEC_SIZE>, Int<VEC_SIZE>>{},
                GenRowMajor{})
        );
        using VecLayoutCPY = Layout<Shape<_1, Int<VEC_SIZE>>>;
        using TiledCopy = decltype(
            make_tiled_copy(CopyAtom{}, ThrLayoutCPY{}, VecLayoutCPY{})
        );

        // kernel launch
        using SharedStorage = ampere::SharedStorage<
            SmemT, 32, SmemLayoutA, SmemLayoutB>;
        constexpr int smem_size = int(sizeof(SharedStorage));
        dim3 grd_dim{ceil_div(m, BLK_M), ceil_div(n, BLK_N)};
        dim3 cta_dim{CTA_SIZE};
        cutlass::ClusterLaunchParams launch_params{grd_dim, cta_dim, 1, smem_size};

        void const* kernel_ptr = (void const*)ampere::kernel<
            T,
            SmemT,
            SharedStorage,
            TiledCopy,
            TiledMma,
            SmemLayoutA,
            SmemLayoutB,
            decltype(mA),
            decltype(mB),
            decltype(mC)>;

        set_kernel_smem_size(kernel_ptr, smem_size);

        cutlass::Status status = cutlass::launch_kernel_on_cluster(
            launch_params, kernel_ptr, mA, mB, mC);
        cudaDeviceSynchronize();
        if (status != cutlass::Status::kSuccess) {
            printf("%s\n", cutlassGetStatusString(status));
        }

        #ifdef _MATMUL_KERNEL_DEBUG
        DEBUG_START;
        printf("---> C++ DONE.\n");
        DEBUG_FINAL;
        #endif
    }
}


void entry(
    const uint64_t a,
    const uint64_t b,
    const uint64_t c,
    const int m,
    const int n,
    const int k,
    const std::string& dtype_str) {

    if (dtype_str == "fp16") {
        dispatch<half_t, half_t, 64, 256, 128, true>(a, b, c, m, n, k);
    }
    else if (dtype_str == "bf16") {
        dispatch<bfloat16_t, bfloat16_t, 64, 256, 128, true>(a, b, c, m, n, k);
    }

    // if (1) {


    //     // else if (dtype_str == "bf16") {
    //     //     dispatch<bfloat16_t, bfloat16_t, 32, 32, 16>(a, b, c, m, n, k);
    //     // }
    // }

    // TODO(hieu): support smaller inputs
    // else {  // Ampere
    //     if (dtype_str == "fp32") {
    //         // dispatch<float, tfloat32_t, 32, 32, 16>(a, b, c, m, n, k);
    //         dispatch<float, tfloat32_t, 64, 64, 16>(a, b, c, m, n, k);
    //     } else if (dtype_str == "fp16") {
    //         // dispatch<half_t, half_t, 32, 32, 16>(a, b, c, m, n, k);
    //         dispatch<half_t, half_t, 64, 64, 16>(a, b, c, m, n, k);
    //     } else if (dtype_str == "bf16") {
    //         // dispatch<bfloat16_t, bfloat16_t, 32, 32, 16>(a, b, c, m, n, k);
    //         dispatch<bfloat16_t, bfloat16_t, 64, 64, 16>(a, b, c, m, n, k);
    //     }
    // }
}


}  // namespace matmul
}  // namespace cutie
