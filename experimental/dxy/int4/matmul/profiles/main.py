"""Tests for MatMul.

bazel test :matmul_lib_test \
  --test_output="all" \
  --test_arg="-k test_correctness" \
  --test_arg="-s" \
"""

import torch

from base.fastforward.torch_utils import cuda_timeit
from experimental.hieu.cutie.matmul import matmul_lib


_DTYPE_DICT: dict[torch.dtype, str] = {
    torch.float32: "fp32",
    torch.float16: "fp16",
    torch.bfloat16: "bf16",
}


def test_runtime(shape: tuple[int], dtype: torch.dtype):
    torch.manual_seed(31415)

    if len(shape) == 1:
        m = n = k = shape[0]
    elif len(shape) == 3:
        m, n, k = shape

    x = torch.zeros(m, k, dtype=dtype, device="cuda")
    y = torch.zeros(n, k, dtype=dtype, device="cuda")

    x.uniform_(-0.1, 0.1)
    y.uniform_(-0.1, 0.1)

    def _torch_step():
        torch.matmul(x, y.T)

    def _cutie_step():
        matmul_lib.matmul(x, y)

    median_latency_ms_cutie, _ = cuda_timeit(_cutie_step)
    median_latency_ms_torch, _ = cuda_timeit(_torch_step)

    print(
        "\x1b[32;20m\n"
        f"m={m:<6d}"
        f"n={n:<6d}"
        f"k={k:<6d}"
        f"dtype={_DTYPE_DICT[dtype]:<9}"
        f"torch_ms={median_latency_ms_torch:<9.5f}",
        f"cutie_ms={median_latency_ms_cutie:<9.5f}",
        "\x1b[0m",
        flush=True,
    )


def main():
    test_runtime(shape=(4096,), dtype=torch.float16)


if __name__ == "__main__":
    main()
