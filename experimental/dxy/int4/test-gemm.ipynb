{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["device_cc=90\n"]}], "source": ["import cutlass\n", "import cutlass.op as cutlass_op\n", "import torch\n", "from cutlass.backend.utils.device import device_cc\n", "\n", "print(f\"device_cc={device_cc()}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "using namespace cute;\n", "\n", "using CollectiveEpilogue =\n", "  typename cutlass::epilogue::collective::CollectiveBuilder<\n", "    cutlass::arch::Sm90, cutlass::arch::OpClassTensorOp,\n", "    cute::<PERSON><PERSON><PERSON><cute::_128, cute::_128, cute::_128>,\n", "    cute::<PERSON><PERSON><PERSON><cute::_1,cute::_1,cute::_1>,\n", "    cutlass::epilogue::collective::EpilogueTileAuto,\n", "    float, float,\n", "    cutlass::float_e4m3_t, cutlass::layout::ColumnMajor, 1,\n", "    cutlass::float_e4m3_t, cutlass::layout::ColumnMajor, 1,\n", "    cutlass::epilogue::NoSmemWarpSpecialized\n", "  >::CollectiveOp;\n", "\n", "using CollectiveMainloop =\n", "  typename cutlass::gemm::collective::CollectiveBuilder<\n", "    cutlass::arch::Sm90, cutlass::arch::OpClassTensorOp,\n", "    cutlass::float_e4m3_t, cutlass::layout::RowMajor, 8,\n", "    cutlass::float_e4m3_t, cutlass::layout::<PERSON><PERSON><PERSON>Major, 8,\n", "    float,\n", "    cute::<PERSON><PERSON><PERSON><cute::_128, cute::_128, cute::_128>,\n", "    cute::<PERSON><PERSON><PERSON><cute::_1,cute::_1,cute::_1>,\n", "    cutlass::gemm::collective::StageCountAutoCarveout<static_cast<int>(sizeof(typename CollectiveEpilogue::SharedStorage))>,\n", "    cutlass::gemm::KernelCpAsyncWarpSpecializedCooperative\n", "  >::CollectiveOp;\n", "\n", "// Gemm operator cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem\n", "using cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem_base = cutlass::gemm::kernel::GemmUniversal<\n", "    Shape<int,int,int,int>,\n", "    CollectiveMainloop,\n", "    CollectiveEpilogue,\n", "    cutlass::gemm::PersistentScheduler\n", ">;\n", "\n", "// Define named type\n", "struct cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem_type :\n", "  public cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem_base { };\n", "\n"]}, {"data": {"text/plain": ["<cutlass.backend.gemm_operation.GemmArguments3x at 0x7fe9add079d0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["plan = cutlass_op.Gemm(\n", "    element=torch.float8_e4m3fn,\n", "    element_accumulator=torch.float32,\n", "    layout_A=cutlass.LayoutType.RowMajor,\n", "    layout_B=cutlass.LayoutType.ColumnMajor,\n", "    layout_C=cutlass.LayoutType.ColumnMajor,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["<cutlass.backend.gemm_operation.GemmArguments3x at 0x7fe9adc0c690>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["m, k, n = 128, 256, 512\n", "A = torch.ones((m, k)).to(torch.float8_e4m3fn).to(\"cuda\")\n", "B = torch.ones((k, n)).to(torch.float8_e4m3fn).to(\"cuda\")\n", "C = torch.ones((m, n)).to(torch.float8_e4m3fn).to(\"cuda\")\n", "D = torch.ones((m, n)).to(torch.float8_e4m3fn).to(\"cuda\")\n", "\n", "plan.run(A, B, C=D, D=D, alpha=2.0, beta=0.0, print_module=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 1., 1.,  ..., 1., 1., 1.],\n", "        [1., 1., 1.,  ..., 1., 1., 1.],\n", "        [1., 1., 1.,  ..., 1., 1., 1.],\n", "        ...,\n", "        [1., 1., 1.,  ..., 1., 1., 1.],\n", "        [1., 1., 1.,  ..., 1., 1., 1.],\n", "        [1., 1., 1.,  ..., 1., 1., 1.]], device='cuda:0',\n", "       dtype=torch.float8_e4m3fn)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.empty(m, n,\n", "            dtype=torch.float8_e4m3fn,\n", "            device=\"cuda\",\n", "        )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "using namespace cute;\n", "\n", "using CollectiveEpilogue =\n", "  typename cutlass::epilogue::collective::CollectiveBuilder<\n", "    cutlass::arch::Sm90, cutlass::arch::OpClassTensorOp,\n", "    cute::<PERSON><PERSON><PERSON><cute::_128, cute::_128, cute::_128>,\n", "    cute::<PERSON><PERSON><PERSON><cute::_1,cute::_1,cute::_1>,\n", "    cutlass::epilogue::collective::EpilogueTileAuto,\n", "    float, float,\n", "    cutlass::float_e4m3_t, cutlass::layout::ColumnMajor, 1,\n", "    cutlass::float_e4m3_t, cutlass::layout::ColumnMajor, 1,\n", "    cutlass::epilogue::NoSmemWarpSpecialized\n", "  >::CollectiveOp;\n", "\n", "using CollectiveMainloop =\n", "  typename cutlass::gemm::collective::CollectiveBuilder<\n", "    cutlass::arch::Sm90, cutlass::arch::OpClassTensorOp,\n", "    cutlass::float_e4m3_t, cutlass::layout::RowMajor, 8,\n", "    cutlass::float_e4m3_t, cutlass::layout::<PERSON><PERSON><PERSON>Major, 8,\n", "    float,\n", "    cute::<PERSON><PERSON><PERSON><cute::_128, cute::_128, cute::_128>,\n", "    cute::<PERSON><PERSON><PERSON><cute::_1,cute::_1,cute::_1>,\n", "    cutlass::gemm::collective::StageCountAutoCarveout<static_cast<int>(sizeof(typename CollectiveEpilogue::SharedStorage))>,\n", "    cutlass::gemm::KernelCpAsyncWarpSpecializedCooperative\n", "  >::CollectiveOp;\n", "\n", "// Gemm operator cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem\n", "using cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem_base = cutlass::gemm::kernel::GemmUniversal<\n", "    Shape<int,int,int,int>,\n", "    CollectiveMainloop,\n", "    CollectiveEpilogue,\n", "    cutlass::gemm::PersistentScheduler\n", ">;\n", "\n", "// Define named type\n", "struct cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem_type :\n", "  public cutlass3x_sm90_tensorop_s64x128x32gemm_e4m3_e4m3_f32_e4m3_e4m3_128x128x128_1x1x1_0_tnn_align8_cpasync_warpspecialized_cooperative_epi_nosmem_base { };\n", "\n"]}, {"data": {"text/plain": ["<cutlass.backend.gemm_operation.GemmArguments3x at 0x7febf743c310>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["plan.run(A, B, C=C, D=D, alpha=2.0, beta=0.0, print_module=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}