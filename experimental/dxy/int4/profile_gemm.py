"""Simple script to profile matrix multiplies in FP8 vs FP16.

python experimental/dxy/int4/profile_gemm.py -o /mnt/efs/augment/user/dxy/profile/compare-gemm.csv
"""

import csv
import logging
import sys
import typing

import torch
import torch.nn as nn
import tqdm
import argparse
from base.fastforward import fp8
from base.fastforward.torch_utils import cuda_timeit
from experimental.dxy.int4.linear import CutlassFp8Linear


def profile(
    linear: typing.Union[nn.Linear, fp8.FP8Linear, CutlassFp8Linear],
    inputs: torch.Tensor,
    output_fp8_meta=None,
):
    if output_fp8_meta:
        assert isinstance(linear, fp8.FP8Linear)
        op = lambda: linear.forward(  # noqa: E731
            inputs,
            output_fp8_meta=output_fp8_meta,
        )
    else:
        op = lambda: linear.forward(inputs)  # noqa: E731
    return cuda_timeit(op)


def main():
    logging.basicConfig(level=logging.INFO)

    parser = argparse.ArgumentParser(description="Simple script to profile GEMM.")
    parser.add_argument(
        "--output-csv",
        "-o",
        type=argparse.FileType("w"),
        default=sys.stdout,
        help="Path to output csv",
    )
    args = parser.parse_args()

    writer = csv.writer(args.output_csv)
    writer.writerow(
        [
            "hidden-dim",
            "batch-dim",
            "fp16-linear",
            "fp8-linear(fp16 -> fp16)",
            "fp8-linear(fp8 -> fp16)",
            "fp8-linear(fp8 -> fp8)",
            "fp8-cutlass-linear(fp8 -> fp8)",
        ]
    )

    max_batch_dim = 8192 * 2

    for hidden_dim in tqdm.tqdm([2048, 2816, 4096, 6144], desc="hidden dim"):
        linear = torch.nn.Linear(
            hidden_dim, hidden_dim, bias=False, dtype=torch.float16, device="cuda"
        )
        inputs = torch.randn(
            max_batch_dim, hidden_dim, dtype=torch.float16, device="cuda"
        )
        fp8_linear = fp8.convert_linear_to_fp8(
            linear,
            inputs.abs().amax(),
            inputs.abs().amax(),  # Don't care about output scale, so re-use the input
            preserve_input=True,
        )
        fp8_inputs = fp8.to_fp8(inputs, fp8_linear.input_fp8_meta, preserve_input=True)
        fp8_cutlass_linear = CutlassFp8Linear(hidden_dim, hidden_dim)
        fp8_cutlass_linear.weights = fp8_linear.weight.detach().to(torch.float8_e4m3fn)
        fp8_cutlass_inputs = fp8_inputs.detach().to(torch.float8_e4m3fn)  # type: ignore
        output_fp8_meta = fp8_linear.input_fp8_meta

        for batch_dim in tqdm.tqdm(
            [8, 32, 128, 256, 512, 1024, 2048, 4096, 8192, 8192 * 2], desc="batch dim"
        ):
            writer.writerow(
                [
                    hidden_dim,
                    batch_dim,
                    profile(linear, inputs[:batch_dim]),
                    profile(fp8_linear, inputs[:batch_dim]),
                    profile(fp8_linear, fp8_inputs[:batch_dim]),
                    profile(
                        fp8_linear,
                        fp8_inputs[:batch_dim],
                        output_fp8_meta=output_fp8_meta,
                    ),
                    profile(fp8_cutlass_linear, fp8_cutlass_inputs[:batch_dim]),
                ]
            )


if __name__ == "__main__":
    main()
