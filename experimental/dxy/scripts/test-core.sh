#!/bin/bash
# bash experimental/dxy/scripts/test-core.sh

PYTHONPATH=/home/<USER>/src/:/home/<USER>/src/augment:/home/<USER>/src/augment/research/models:/home/<USER>/src/augment/research/gpt-neox/:/home/<USER>/src/augment/research/lm-evaluation-harness
PYTHONPATH=${PYTHONPATH}:/home/<USER>/.local/lib/python3.9/site-packages:/opt/conda/lib/python3.9/site-packages

# Reformat before the tests
pre-commit run --hook-stage manual --files research/core/* research/core/tests/*

# use --slow to test unit test marked as slow by pytest
PYTHONPATH=${PYTHONPATH} pytest \
	-W ignore::DeprecationWarning -W ignore::UserWarning \
	research/core/tests/*.py
