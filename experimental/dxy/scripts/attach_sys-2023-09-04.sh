#!/bin/bash
# bash experimental/dxy/scripts/attach_sys-2023-09-04.sh 0 basic-sc-3b
# bash experimental/dxy/scripts/attach_sys-2023-09-04.sh 0 basic-sc-7b
# bash experimental/dxy/scripts/attach_sys-2023-09-04.sh 0 basic-sc-16b
# bash experimental/dxy/scripts/attach_sys-2023-09-04.sh 0 basic-fimv2-16b
set -e
GPU=$1
sys=$2

CUDA_VISIBLE_DEVICES=${GPU} python experimental/dxy/exps/attach_sys_to_patches.py \
    --data_dir ~/cache/data-patches/google_pyglove_v1.0/ \
    --source_doc_name api-raw-2lines.torch \
    --target_doc_name api-raw-2lines-sys \
    --system_name ${sys}

CUDA_VISIBLE_DEVICES=${GPU} python experimental/dxy/exps/attach_sys_to_patches.py \
    --data_dir ~/cache/data-patches/google_pyglove_v1.0/ \
    --source_doc_name api-raw-ge3lines.torch \
    --target_doc_name api-raw-ge3lines-sys \
    --system_name ${sys}

CUDA_VISIBLE_DEVICES=${GPU} python experimental/dxy/exps/attach_sys_to_patches.py \
    --data_dir ~/cache/data-patches/pydantic_pydantic_v1.0/ \
    --source_doc_name api-raw-ge3lines.torch \
    --target_doc_name api-raw-ge3lines-sys \
    --system_name ${sys}

CUDA_VISIBLE_DEVICES=${GPU} python experimental/dxy/exps/attach_sys_to_patches.py \
    --data_dir ~/cache/data-patches/thealgorithms_python_v1.0/ \
    --source_doc_name api-raw-ge3lines.torch \
    --target_doc_name api-raw-ge3lines-sys \
    --system_name ${sys}
