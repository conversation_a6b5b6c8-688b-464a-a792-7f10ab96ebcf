#!/bin/bash
# bash experimental/dxy/scripts/tmps/convert-starcoder-ckp-2023-07-17.sh
set -e
echo script name: $0
echo $# arguments

root_dir="/home/<USER>/ckps/starcoderplus_neox"

python research/utils/merge_or_split_checkpoint.py \
	--srcdir ${root_dir}/checkpoint \
	--dstdir ${root_dir}/checkpoint-mps1 \
	--config ${root_dir}/config.yml \
	--model-parallel-size 2 \
	--mode merge

rm ${root_dir}/latest
touch ${root_dir}/latest
echo "checkpoint-mps1" > ${root_dir}/latest
cp ${root_dir}/checkpoint/mp_rank_00_model_states.pt ${root_dir}/checkpoint-mps1/
