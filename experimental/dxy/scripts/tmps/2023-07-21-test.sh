#!/bin/bash
# bash experimental/dxy/scripts/tmps/2023-07-21-test.sh

PYTHONPATH=/home/<USER>/src/:/home/<USER>/src/augment:/home/<USER>/src/augment/research/models:/home/<USER>/src/augment/research/gpt-neox/:/home/<USER>/src/augment/research/lm-evaluation-harness
PYTHONPATH=${PYTHONPATH}:/home/<USER>/.local/lib/python3.9/site-packages:/opt/conda/lib/python3.9/site-packages

# use --slow to test unit test marked as slow by pytest
PYTHONPATH=${PYTHONPATH} pytest \
	-W ignore::DeprecationWarning -W ignore::UserWarning \
	research/core/tests/*.py research/models/tests/test_*
