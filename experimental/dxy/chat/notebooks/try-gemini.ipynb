{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.11/site-packages/google/auth/_default.py:76: UserWarning: Your application has authenticated using end user credentials from Google Cloud SDK without a quota project. You might receive a \"quota exceeded\" or \"API not enabled\" error. See the following page for troubleshooting: https://cloud.google.com/docs/authentication/adc-troubleshooting/user-creds. \n", "  warnings.warn(_CLOUD_SDK_CREDENTIALS_WARNING)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Hello! 👋 \n", "\n", "What can I do for you today? 😄 \n", "\n"]}], "source": ["import vertexai\n", "from vertexai.generative_models import (\n", "    ChatSession,\n", "    GenerationConfig,\n", "    GenerativeModel,\n", "    SafetySetting,\n", ")\n", "from vertexai import generative_models\n", "\n", "project_id = \"system-services-dev\"\n", "location = \"us-west1\"\n", "vertexai.init(project=project_id, location=location)\n", "\n", "generation_config = GenerationConfig(\n", "    candidate_count=1,\n", "    temperature=0.7,\n", "    top_p=0.95,\n", "    # top_k=64,\n", "    max_output_tokens=8192,\n", "    response_mime_type=\"text/plain\",\n", ")\n", "GOOGLE_MODEL = GenerativeModel(\n", "    model_name=\"gemini-1.5-pro\",\n", "    generation_config=generation_config,\n", ")\n", "responses = GOOGLE_MODEL.generate_content(\n", "    \"hello\", stream=False, generation_config=generation_config\n", ")\n", "print(responses.text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}