{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.llm_apis.chat_utils import OpenAIAPIClient\n", "\n", "deepseek_address = \"*************:8000\"\n", "client = OpenAIAPIClient(address=deepseek_address)\n", "\n", "prompt = \"Write hello world in haskell\"\n", "\n", "# Without streaming\n", "response = client.generate(messages=[prompt], max_tokens=256)\n", "print(response)\n", "\n", "print(\"--------------------------------------------------------------------------------\")\n", "\n", "# With streaming\n", "response = \"\"\n", "for response_elem in client.generate_stream(messages=[prompt], max_tokens=256):\n", "    print(response_elem, end=\"\")\n", "    response += response_elem"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}