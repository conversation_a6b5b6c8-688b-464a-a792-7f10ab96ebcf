{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Create the Training Dataset"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chosed tokenizer Vocab Size = 32000\n", "    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The validatation dataset has 1393 records.\n"]}], "source": ["# Create the index dataset\n", "import pathlib\n", "import tqdm\n", "import json\n", "from augment.research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "import random\n", "import torch\n", "import tqdm\n", "import numpy as np\n", "from research.core import utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData\n", "from augment.experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "\n", "tokenizer = Llama2Tokenizer()\n", "print(f\"Chosed tokenizer Vocab Size = {tokenizer.vocab_size}\")\n", "\n", "# valid_dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/xtry-rules/math-valid\")\n", "valid_dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/xtry-rules/structure-math-valid\")\n", "print(f\"The validatation dataset has {len(valid_dataset)} records.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["149\n", "[1, 29871, 13, 5160, 373, 278, 3030, 310, 278, 1494, 5132, 770, 322, 848, 1990, 29892, 3113, 1234, 278, 1404, 29915, 29879, 1139, 29889, 13, 13, 28956, 13, 1542, 29900, 517, 29929, 353, 19229, 29889, 24938, 284, 29961, 29900, 29892, 29871, 29896, 29892, 29871, 29906, 29892, 29871, 29941, 29892, 29871, 29946, 29892, 29871, 29945, 29892, 29871, 29953, 29892, 29871, 29955, 29892, 29871, 29947, 29892, 29871, 29929, 29962, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 10951, 277, 29901, 13, 1678, 9995, 29909, 13615, 338, 263, 18761, 310, 263, 995, 322, 263, 2602, 1213, 15945, 13, 13, 1678, 995, 29901, 5167, 29900, 517, 29929, 13, 1678, 2602, 29901, 938, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 2431, 8003, 2528, 654, 29901, 13, 1678, 9995, 29909, 770, 304, 7344, 278, 6124, 472, 1269, 2602, 15945, 29908, 13, 13, 1678, 2175, 29901, 10951, 277, 13, 1678, 9995, 1576, 2175, 13615, 995, 1213, 15945, 13, 13, 1678, 1492, 29901, 10951, 277, 13, 1678, 9995, 1576, 1492, 13615, 995, 1213, 15945, 13, 13, 1678, 8677, 29901, 19229, 29889, 24938, 284, 29961, 29900, 29892, 29871, 29896, 29962, 13, 1678, 9995, 1576, 5684, 8988, 995, 1213, 15945, 13, 13, 1678, 1121, 29901, 5167, 29900, 517, 29929, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 9681, 29901, 13, 1678, 13340, 29901, 19229, 29889, 1293, 29961, 14991, 277, 29962, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 2431, 8003, 6857, 666, 1414, 29901, 13, 1678, 2175, 29901, 10951, 277, 13, 1678, 1492, 29901, 9681, 13, 1678, 1121, 29901, 9681, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 3462, 654, 10740, 29901, 13, 1678, 10970, 29901, 19229, 29889, 23215, 552, 29961, 4557, 29892, 9681, 29962, 13, 1678, 8792, 29901, 19229, 29889, 1293, 29961, 5894, 8003, 2528, 654, 29962, 13, 1678, 1962, 29901, 9681, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 9683, 666, 1414, 10740, 29901, 13, 1678, 10970, 29901, 19229, 29889, 23215, 552, 29961, 4557, 29892, 9681, 29962, 13, 1678, 8792, 29901, 19229, 29889, 1293, 29961, 5894, 8003, 6857, 666, 1414, 29962, 13, 1678, 1962, 29901, 9681, 13, 28956, 29871, 13, 16492, 29901, 24481, 278, 1121, 310, 29871, 29947, 29906, 29900, 29896, 29953, 29896, 29947, 29900, 334, 29871, 29929, 29947, 29947, 29900, 29955, 29947, 29953, 29946, 29889, 13, 22550, 29901, 9683, 666, 1414, 10740, 29898, 13, 29871, 10970, 7607, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29955, 4638, 511, 9681, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 29897, 2314, 511, 13, 29871, 8792, 11759, 5894, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29955, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29929, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29906, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29929, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29941, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29929, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29896, 29900, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29896, 29896, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29946, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29929, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 29900, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29896, 29896, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29945, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29929, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 29906, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29953, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29929, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29896, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 29896, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29896, 29906, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29896, 29941, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29896, 29946, 29897, 2314, 511, 2431, 8003, 6857, 666, 1414, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29955, 511, 1492, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 4638, 511, 1121, 29922, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29929, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29896, 29900, 511, 10951, 277, 29898, 1767, 29922, 29953, 29892, 2602, 29922, 29896, 29896, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29896, 29906, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 29941, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29896, 29946, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29896, 29945, 4638, 876, 1402, 13, 29871, 1962, 29922, 4557, 29898, 13, 1678, 13340, 11759, 14991, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 29945, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29896, 29946, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29896, 29941, 511, 10951, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29896, 29906, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 29896, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29896, 29900, 511, 10951, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29929, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 4638, 13, 29871, 1723, 13, 29897, 13, 578, 278, 1121, 338, 29871, 29947, 29896, 29900, 29941, 29947, 29946, 29941, 29945, 29945, 29929, 29906, 29941, 29929, 29945, 29906, 29900, 2, 1, 29871, 13, 5160, 373, 278, 3030, 310, 278, 1494, 5132, 770, 322, 848, 1990, 29892, 3113, 1234, 278, 1404, 29915, 29879, 1139, 29889, 13, 13, 28956, 13, 1542, 29900, 517, 29929, 353, 19229, 29889, 24938, 284, 29961, 29900, 29892, 29871, 29896, 29892, 29871, 29906, 29892, 29871, 29941, 29892, 29871, 29946, 29892, 29871, 29945, 29892, 29871, 29953, 29892, 29871, 29955, 29892, 29871, 29947, 29892, 29871, 29929, 29962, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 10951, 277, 29901, 13, 1678, 9995, 29909, 13615, 338, 263, 18761, 310, 263, 995, 322, 263, 2602, 1213, 15945, 13, 13, 1678, 995, 29901, 5167, 29900, 517, 29929, 13, 1678, 2602, 29901, 938, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 2431, 8003, 2528, 654, 29901, 13, 1678, 9995, 29909, 770, 304, 7344, 278, 6124, 472, 1269, 2602, 15945, 29908, 13, 13, 1678, 2175, 29901, 10951, 277, 13, 1678, 9995, 1576, 2175, 13615, 995, 1213, 15945, 13, 13, 1678, 1492, 29901, 10951, 277, 13, 1678, 9995, 1576, 1492, 13615, 995, 1213, 15945, 13, 13, 1678, 8677, 29901, 19229, 29889, 24938, 284, 29961, 29900, 29892, 29871, 29896, 29962, 13, 1678, 9995, 1576, 5684, 8988, 995, 1213, 15945, 13, 13, 1678, 1121, 29901, 5167, 29900, 517, 29929, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 9681, 29901, 13, 1678, 13340, 29901, 19229, 29889, 1293, 29961, 14991, 277, 29962, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 2431, 8003, 6857, 666, 1414, 29901, 13, 1678, 2175, 29901, 10951, 277, 13, 1678, 1492, 29901, 9681, 13, 1678, 1121, 29901, 9681, 13, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 3462, 654, 10740, 29901, 13, 1678, 10970, 29901, 19229, 29889, 23215, 552, 29961, 4557, 29892, 9681, 29962, 13, 1678, 8792, 29901, 19229, 29889, 1293, 29961, 5894, 8003, 2528, 654, 29962, 13, 1678, 1962, 29901, 9681, 13, 13, 29992, 1272, 13203, 29889, 1272, 1990, 13, 1990, 9683, 666, 1414, 10740, 29901, 13, 1678, 10970, 29901, 19229, 29889, 23215, 552, 29961, 4557, 29892, 9681, 29962, 13, 1678, 8792, 29901, 19229, 29889, 1293, 29961, 5894, 8003, 6857, 666, 1414, 29962, 13, 1678, 1962, 29901, 9681, 13, 28956, 29871, 13, 16492, 29901, 24481, 278, 1121, 310, 29871, 29945, 29946, 29941, 29946, 29945, 29941, 29896, 29945, 718, 29871, 29929, 29945, 29929, 29941, 29946, 29896, 29947, 29900, 29889, 13, 22550, 29901, 3462, 654, 10740, 29898, 13, 29871, 10970, 7607, 4557, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29955, 4638, 511, 9681, 29898, 7501, 1169, 11759, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 10951, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 29897, 2314, 511, 13, 29871, 8792, 11759, 5894, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29900, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29900, 511, 8677, 29922, 29900, 29892, 1121, 29922, 29945, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29896, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29947, 29892, 2602, 29922, 29896, 511, 8677, 29922, 29900, 29892, 1121, 29922, 29929, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29906, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29906, 511, 8677, 29922, 29900, 29892, 1121, 29922, 29946, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29941, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29941, 511, 8677, 29922, 29900, 29892, 1121, 29922, 29929, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29946, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29946, 511, 8677, 29922, 29900, 29892, 1121, 29922, 29955, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29941, 29892, 2602, 29922, 29945, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29945, 511, 8677, 29922, 29896, 29892, 1121, 29922, 29906, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29953, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29953, 511, 8677, 29922, 29896, 29892, 1121, 29922, 29900, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29955, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29955, 511, 8677, 29922, 29896, 29892, 1121, 29922, 29945, 511, 2431, 8003, 2528, 654, 29898, 1563, 29922, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29947, 511, 1492, 29922, 14991, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29947, 511, 8677, 29922, 29896, 29892, 1121, 29922, 29896, 29897, 1402, 13, 29871, 1962, 29922, 4557, 29898, 13, 1678, 13340, 11759, 14991, 277, 29898, 1767, 29922, 29896, 29892, 2602, 29922, 29947, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29955, 511, 10951, 277, 29898, 1767, 29922, 29900, 29892, 2602, 29922, 29953, 511, 10951, 277, 29898, 1767, 29922, 29906, 29892, 2602, 29922, 29945, 511, 10951, 277, 29898, 1767, 29922, 29955, 29892, 2602, 29922, 29946, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29941, 511, 10951, 277, 29898, 1767, 29922, 29946, 29892, 2602, 29922, 29906, 511, 10951, 277, 29898, 1767, 29922, 29929, 29892, 2602, 29922, 29896, 511, 10951, 277, 29898, 1767, 29922, 29945, 29892, 2602, 29922, 29900, 4638, 13, 29871, 1723, 13, 29897, 13, 578, 278, 1121, 338, 29871, 29896, 29945, 29900, 29906, 29955, 29929, 29946, 29929, 29945, 2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, -2, 2]\n"]}], "source": ["x = valid_dataset[4]\n", "print(np.sum(x < 0))\n", "print(x.tolist())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Base on the context of the following Python class and dataclass, please answer the user's question.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "``` \n", "Question: reasoning the result of 82016180 * 98807864.\n", "Answer: MultiplicationRule(\n", "  inputs=(Number(digits=[Digit(value=0, position=0), <PERSON>git(value=8, position=1), <PERSON><PERSON>(value=1, position=2), <PERSON><PERSON>(value=6, position=3), <PERSON><PERSON>(value=1, position=4), <PERSON>git(value=0, position=5), <PERSON><PERSON>(value=2, position=6), <PERSON><PERSON>(value=8, position=7)]), Number(digits=[<PERSON>git(value=4, position=0), <PERSON><PERSON>(value=6, position=1), <PERSON><PERSON>(value=8, position=2), <PERSON><PERSON>(value=7, position=3), <PERSON><PERSON>(value=0, position=4), <PERSON><PERSON>(value=8, position=5), <PERSON><PERSON>(value=8, position=6), <PERSON><PERSON>(value=9, position=7)])),\n", "  procedure=[PerPositionMultiplication(left=Digit(value=0, position=0), right=Number(digits=[Digit(value=4, position=0), <PERSON>git(value=6, position=1), <PERSON>git(value=8, position=2), <PERSON>git(value=7, position=3), <PERSON>git(value=0, position=4), <PERSON><PERSON>(value=8, position=5), <PERSON>git(value=8, position=6), <PERSON>git(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), <PERSON><PERSON>(value=0, position=1), <PERSON>git(value=0, position=2), <PERSON>git(value=0, position=3), <PERSON>git(value=0, position=4), <PERSON>git(value=0, position=5), <PERSON>git(value=0, position=6), <PERSON>git(value=0, position=7)])), PerPositionMultiplication(left=Digit(value=8, position=1), right=Number(digits=[Digit(value=4, position=0), <PERSON>git(value=6, position=1), <PERSON>git(value=8, position=2), <PERSON>git(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), <PERSON>git(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=2, position=1), Digit(value=1, position=2), Digit(value=9, position=3), Digit(value=2, position=4), Digit(value=6, position=5), Digit(value=4, position=6), Digit(value=0, position=7), Digit(value=9, position=8), Digit(value=7, position=9)])), PerPositionMultiplication(left=Digit(value=1, position=2), right=Number(digits=[Digit(value=4, position=0), Digit(value=6, position=1), Digit(value=8, position=2), Digit(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), Digit(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=0, position=1), Digit(value=4, position=2), Digit(value=6, position=3), Digit(value=8, position=4), Digit(value=7, position=5), Digit(value=0, position=6), Digit(value=8, position=7), Digit(value=8, position=8), Digit(value=9, position=9)])), PerPositionMultiplication(left=Digit(value=6, position=3), right=Number(digits=[Digit(value=4, position=0), Digit(value=6, position=1), Digit(value=8, position=2), Digit(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), Digit(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=0, position=1), Digit(value=0, position=2), Digit(value=4, position=3), Digit(value=8, position=4), Digit(value=1, position=5), Digit(value=7, position=6), Digit(value=4, position=7), Digit(value=8, position=8), Digit(value=2, position=9), Digit(value=9, position=10), Digit(value=5, position=11)])), PerPositionMultiplication(left=Digit(value=1, position=4), right=Number(digits=[Digit(value=4, position=0), Digit(value=6, position=1), Digit(value=8, position=2), Digit(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), Digit(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=0, position=1), Digit(value=0, position=2), Digit(value=0, position=3), Digit(value=4, position=4), Digit(value=6, position=5), Digit(value=8, position=6), Digit(value=7, position=7), Digit(value=0, position=8), Digit(value=8, position=9), Digit(value=8, position=10), Digit(value=9, position=11)])), PerPositionMultiplication(left=Digit(value=0, position=5), right=Number(digits=[Digit(value=4, position=0), Digit(value=6, position=1), Digit(value=8, position=2), Digit(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), Digit(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=0, position=1), Digit(value=0, position=2), Digit(value=0, position=3), Digit(value=0, position=4), Digit(value=0, position=5), Digit(value=0, position=6), Digit(value=0, position=7), Digit(value=0, position=8), Digit(value=0, position=9), Digit(value=0, position=10), Digit(value=0, position=11), Digit(value=0, position=12)])), PerPositionMultiplication(left=Digit(value=2, position=6), right=Number(digits=[Digit(value=4, position=0), Digit(value=6, position=1), Digit(value=8, position=2), Digit(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), Digit(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=0, position=1), Digit(value=0, position=2), Digit(value=0, position=3), Digit(value=0, position=4), Digit(value=0, position=5), Digit(value=8, position=6), Digit(value=2, position=7), Digit(value=7, position=8), Digit(value=5, position=9), Digit(value=1, position=10), Digit(value=6, position=11), Digit(value=7, position=12), Digit(value=9, position=13), Digit(value=1, position=14)])), PerPositionMultiplication(left=Digit(value=8, position=7), right=Number(digits=[Digit(value=4, position=0), Digit(value=6, position=1), Digit(value=8, position=2), Digit(value=7, position=3), Digit(value=0, position=4), Digit(value=8, position=5), Digit(value=8, position=6), Digit(value=9, position=7)]), result=Number(digits=[Digit(value=0, position=0), Digit(value=0, position=1), Digit(value=0, position=2), Digit(value=0, position=3), Digit(value=0, position=4), Digit(value=0, position=5), Digit(value=0, position=6), Digit(value=2, position=7), Digit(value=1, position=8), Digit(value=9, position=9), Digit(value=2, position=10), Digit(value=6, position=11), Digit(value=4, position=12), Digit(value=0, position=13), Digit(value=9, position=14), Digit(value=7, position=15)]))],\n", "  output=Number(\n", "    digits=[Digit(value=8, position=15), <PERSON><PERSON>(value=1, position=14), <PERSON><PERSON>(value=0, position=13), <PERSON><PERSON>(value=3, position=12), <PERSON><PERSON>(value=8, position=11), <PERSON><PERSON>(value=4, position=10), <PERSON><PERSON>(value=3, position=9), <PERSON><PERSON>(value=5, position=8), <PERSON><PERSON>(value=5, position=7), <PERSON><PERSON>(value=9, position=6), <PERSON><PERSON>(value=2, position=5), <PERSON><PERSON>(value=3, position=4), <PERSON><PERSON>(value=9, position=3), <PERSON><PERSON>(value=5, position=2), <PERSON><PERSON>(value=2, position=1), <PERSON><PERSON>(value=0, position=0)]\n", "  )\n", ")\n", "so the result is 8103843559239520 \n", "Base on the context of the following Python class and dataclass, please answer the user's question.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "``` \n", "Question: reasoning the result of 54345315 + 95934180.\n", "Answer: AdditionRule(\n", "  inputs=(Number(digits=[Digit(value=5, position=0), <PERSON>git(value=1, position=1), <PERSON><PERSON>(value=3, position=2), <PERSON><PERSON>(value=5, position=3), <PERSON><PERSON>(value=4, position=4), <PERSON>git(value=3, position=5), <PERSON><PERSON>(value=4, position=6), <PERSON><PERSON>(value=5, position=7)]), Number(digits=[<PERSON>git(value=0, position=0), <PERSON><PERSON>(value=8, position=1), <PERSON><PERSON>(value=1, position=2), <PERSON><PERSON>(value=4, position=3), <PERSON><PERSON>(value=3, position=4), <PERSON><PERSON>(value=9, position=5), <PERSON><PERSON>(value=5, position=6), <PERSON><PERSON>(value=9, position=7)])),\n", "  procedure=[PerPositionAddition(left=Digit(value=5, position=0), right=Digit(value=0, position=0), carry=0, result=5), PerPositionAddition(left=Digit(value=1, position=1), right=Digit(value=8, position=1), carry=0, result=9), PerPositionAddition(left=Digit(value=3, position=2), right=Digit(value=1, position=2), carry=0, result=4), PerPositionAddition(left=Digit(value=5, position=3), right=Digit(value=4, position=3), carry=0, result=9), PerPositionAddition(left=Digit(value=4, position=4), right=Digit(value=3, position=4), carry=0, result=7), PerPositionAddition(left=Digit(value=3, position=5), right=Digit(value=9, position=5), carry=1, result=2), PerPositionAddition(left=Digit(value=4, position=6), right=Digit(value=5, position=6), carry=1, result=0), PerPositionAddition(left=Digit(value=5, position=7), right=Digit(value=9, position=7), carry=1, result=5), PerPositionAddition(left=Digit(value=0, position=8), right=Digit(value=0, position=8), carry=1, result=1)],\n", "  output=Number(\n", "    digits=[Digit(value=1, position=8), <PERSON><PERSON>(value=5, position=7), <PERSON><PERSON>(value=0, position=6), <PERSON><PERSON>(value=2, position=5), <PERSON><PERSON>(value=7, position=4), <PERSON><PERSON>(value=9, position=3), <PERSON><PERSON>(value=4, position=2), <PERSON><PERSON>(value=9, position=1), <PERSON><PERSON>(value=5, position=0)]\n", "  )\n", ")\n", "so the result is 150279495\n"]}], "source": ["print(tokenizer.detokenize(np.abs(x).tolist()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import typing\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "\n", "def pack_sequences(\n", "    examples: list[list[int]],\n", "    seq_len: int,\n", "    pad_id: int,\n", ") -> list[list[int]]:\n", "    \"\"\"Pack shorter examples into longer ones.\"\"\"\n", "    final_sequences: list[list[int]] = []\n", "    paddings, skip_over_length = [], 0\n", "    current_batch: typing.Optional[list[int]] = None\n", "    for ex in examples:\n", "        if len(ex) >= seq_len:\n", "            skip_over_length += 1\n", "            continue\n", "        if current_batch is None:\n", "            current_batch = ex\n", "            continue\n", "        elif len(current_batch) > seq_len:\n", "            raise ValueError(\"Impossible\")\n", "        elif len(current_batch) + len(ex) > seq_len:\n", "            paddings.append(seq_len - len(current_batch))\n", "            current_batch.extend([pad_id] * (seq_len - len(current_batch)))\n", "            final_sequences.append(current_batch)\n", "            current_batch = None\n", "        else:\n", "            current_batch.extend(ex)\n", "    if current_batch is not None:\n", "        paddings.append(seq_len - len(current_batch))\n", "        current_batch.extend([pad_id] * (seq_len - len(current_batch)))\n", "        final_sequences.append(current_batch)\n", "    print(f\"Skip {skip_over_length} examples due to over-{seq_len}-tokens.\")\n", "    print(\n", "        f\"Packed {len(examples)} examples into {len(final_sequences)} {seq_len}-length-sequences.\"\n", "    )\n", "    print(f\"On average, the number of paddings is {np.mean(paddings):.2f}.\")\n", "    return final_sequences\n", "\n", "\n", "def build_whole_dataset(\n", "    dataset,\n", "    root_dir: str,\n", "    base_name: str,\n", "    repeats: int = 1,\n", "    seq_len: int = 4096,\n", "    mask_input: bool = False,\n", "):\n", "    random.shuffle(raw_examples)\n", "    # Split the raw training / validation dataset\n", "    print(f\"There are {len(raw_examples)} examples in total.\")\n", "\n", "    examples = []\n", "    for index in range(len(dataset)):\n", "        tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "            repeated_examples.append(tensor)\n", "    print(f\"#repeated_examples = {len(repeated_examples)} with repeats={repeats}\")\n", "    random.shuffle(repeated_examples)\n", "\n", "    sequences = pack_sequences(repeated_examples, seq_len, -tokenizer.eos_id)\n", "    random.shuffle(sequences)\n", "\n", "    print(f\"There are {len(sequences)} sequences.\")\n", "\n", "    mask_suffix = \"onlytgt\" if mask_input else \"full\"\n", "    output_path = (\n", "        pathlib.Path(root_dir) / f\"{base_name}_r{repeats}n_s{seq_len}_{mask_suffix}\"\n", "    )\n", "\n", "    output_dir = output_path.parent\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    # Build the train dataset.\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sequence in sequences:\n", "        # Make the total sequence length to be 4096 + 1\n", "        builder.add_item(torch.Tensor(sequence + [tokenizer.eos_id]))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Built the {base_name} dataset at {output_path}.\")\n", "\n", "\n", "for mask_input in (<PERSON><PERSON><PERSON>, <PERSON>):\n", "    build_whole_dataset(\n", "        train_examples,\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13\",\n", "        \"train3v\",\n", "        mask_input=mask_input,\n", "    )\n", "    print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "    build_whole_dataset(\n", "        valid_examples,\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13\",\n", "        \"valid3v\",\n", "        mask_input=mask_input,\n", "    )\n", "    print(\"\\n\" + \"-\" * 100 + \"\\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}