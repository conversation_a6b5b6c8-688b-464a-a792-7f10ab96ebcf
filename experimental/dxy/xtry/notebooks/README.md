# XTRY

## Simple Math

```bash
#
torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=1e-5 --decay_lr=False --weight_decay=1.0 \
 --max_iters=2000 --batch_size=8 \
 --eval_interval=100 --eval_iters=32 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/xtry/0_to_1e18/simple-train \
 --eval_data_path="0_to_1e9@/mnt/efs/augment/user/dxy/datasets/xtry/0_to_1e9/simple-valid;0_to_1e18@/mnt/efs/augment/user/dxy/datasets/xtry/0_to_1e18/simple-valid;1e9_to_1e18@/mnt/efs/augment/user/dxy/datasets/xtry/1e9_to_1e18/simple-valid" \
 --out_dir=/mnt/efs/augment/user/dxy/logs/dxy.xtry/ \
 --wandb_log=True --wandb_project=dxy-exps \
 --wandb_group=SIMPLE --wandb_run_name=CL-BASE7B-SIMPLE_0_to_1e18-step_2000-WD1_0-b8-lr_00001
```

## Structure Math

```bash
torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False --weight_decay=1.0 \
 --max_iters=2000 --batch_size=8 \
 --eval_interval=100 --eval_iters=32 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-train-1e18-r1 \
 --eval_data_path="0_to_1e9@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e9-r1;1e9_to_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e9_to_1e18-r1;0_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e18-r1;1e9_to_1e27@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e9_to_1e27-r1" \
 --out_dir=/mnt/efs/augment/user/dxy/logs/dxy.xtry/ \
 --wandb_log=True --wandb_project=dxy-exps \
 --wandb_group=STRUCTURE --wandb_run_name=CL-BASE7B-STRUCTURE-1e18-step_2000-WD1_0-b8-lr_2e-6


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=True --warmup_iters=100 --lr_decay_iters=500 --min_lr=2e-7 --weight_decay=1.0 \
 --max_iters=500 --batch_size=8 \
 --eval_interval=25 --eval_iters=16 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-train-1e9-r1 \
 --eval_data_path="0_to_1e9@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e9-r1;1e9_to_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e9_to_1e18-r1;0_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e18-r1;1e18_to_1e27@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e18_to_1e27-r1" \
 --out_dir=/mnt/efs/augment/user/dxy/logs/dxy.xtry.v3/ \
 --wandb_log=True --wandb_project=dxy-exps \
 --wandb_group=STRUCTURE --wandb_run_name=CL-BASE7B-STRUCTURE-1e9-step_500-WD1_0-b8-lr_2e-6_cosine


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=1e-6 --decay_lr=True --warmup_iters=50 --lr_decay_iters=500 --min_lr=1e-7 --weight_decay=1.0 \
 --max_iters=500 --batch_size=8 \
 --eval_interval=25 --eval_iters=16 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/augstruct-train-1e18-r1 \
 --eval_data_path="0_to_1e9@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e9-r1;1e9_to_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e9_to_1e18-r1;0_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e18-r1;1e18_to_1e27@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e18_to_1e27-r1" \
 --out_dir=/mnt/efs/augment/user/dxy/logs/dxy.xtry.11.05/ \
 --wandb_log=True --wandb_project=dxy-exps \
 --wandb_group=STRUCTURE --wandb_run_name=CL-BASE7B-AUGSTR-1e18-S500_WUP50-WD1_0-b8-lr_1e-6_cosine


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=True --warmup_iters=100 --lr_decay_iters=500 --min_lr=2e-7 --weight_decay=1.0 \
 --max_iters=500 --batch_size=8 \
 --eval_interval=25 --eval_iters=16 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/augstruct-train-1e9-r1 \
 --eval_data_path="0_to_1e9@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e9-r1;1e9_to_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e9_to_1e18-r1;0_1e18@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-0_1e18-r1;1e18_to_1e27@/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05/structure-valid-1e18_to_1e27-r1" \
 --out_dir=/mnt/efs/augment/user/dxy/logs/dxy.xtry.v3/ \
 --wandb_log=True --wandb_project=dxy-exps \
 --wandb_group=STRUCTURE --wandb_run_name=CL-BASE7B-AUGSTR-1e9-step_500-WD1_0-b8-lr_2e-6_cosine
```
