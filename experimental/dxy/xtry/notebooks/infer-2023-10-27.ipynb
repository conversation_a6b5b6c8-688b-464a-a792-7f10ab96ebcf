{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Socket error: [Errno 98] Address already in use; Port 6000 is in use on 0.0.0.0. Checking 6001...\n", "Socket error: [Errno 98] Address already in use; Port 6001 is in use on 0.0.0.0. Checking 6002...\n", "> initializing model parallel with size 1\n", "> initializing ddp with size 1\n", "> initializing pipeline with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.9/site-packages/torch/__init__.py:614: UserWarning: torch.set_default_tensor_type() is deprecated as of PyTorch 2.1, please use torch.set_default_dtype() and torch.set_default_device() as alternatives. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:451.)\n", "  _C._set_default_tensor_type(t)\n"]}], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"6\"\n", "import pathlib\n", "import tqdm\n", "import json\n", "import torch\n", "from research.models.llama2_models import LLAMA2Model\n", "\n", "model = LLAMA2Model(\n", "    checkpoint_path=pathlib.Path(\n", "        # \"/mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-Data_s4k_onlytgt-5000s-lr_00001/ckp_in_llama_format\"\n", "        # \"/mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-data2v1r_full-s10000-b8-lr_000002/ckp_in_llama_format\"\n", "        # \"/mnt/efs/augment/user/dxy/logs/dxy.xtry.v3/CL-BASE7B-AUGSTR-1e18-step_2000-WD1_0-b8-lr_000002/best_ckp_for_1e9_to_1e27_in_llama_format\"\n", "        \"/mnt/efs/augment/user/dxy/logs/dxy.xtry.v3/CL-BASE7B-AUGSTR-1e18-step_500-WD1_0-b8-lr_2e-6_cosine/best_ckp_for_1e18_to_1e27_in_llama_format\",\n", "    )\n", ")\n", "model.load()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "from research.models.meta_model import GenerationOptions\n", "from experimental.dxy.xtry.arithmetic import (\n", "    Number,\n", "    PerPositionAddition,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    AdditionRule,\n", "    MultiplicationRule,\n", "    Reference,\n", "    get_number,\n", "    convert_to_digits,\n", ")\n", "from experimental.dxy.xtry.dataclasses_ext import format_dataclass\n", "from experimental.dxy.xtry.create_dataset import structure_template, create_structure\n", "from research.eval.generation.execution import sandbox_execute\n", "\n", "tokenizer = Llama2Tokenizer()\n", "\n", "\n", "def check_string_match(a: str, b: str) -> float:\n", "    \"\"\"Check the char-level matching rate.\"\"\"\n", "    max_len = max(len(a), len(b))\n", "    min_len = min(len(a), len(b))\n", "    correct = 0.0\n", "    for i in range(min_len, max_len):\n", "        if a[i] == b[i]:\n", "            correct += 1\n", "    return correct / max_len\n", "\n", "\n", "def infer_additive_fn(x, y) -> tuple[str, str, str]:\n", "    print(termcolor.colored(f\"{x} + {y} = {x + y}\", \"green\"))\n", "    inputs, targets = create_structure(x, y)\n", "    tokens = [tokenizer.bos_id] + tokenizer.tokenize(inputs)\n", "    output = model.raw_generate(\n", "        tokens, options=GenerationOptions(max_generated_tokens=3000, top_k=0)\n", "    )\n", "    code = inputs + output\n", "    _, code_result = sandbox_execute(code, 2.0)\n", "    return targets, output, code_result"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m639464510 + 139588203 = 779052713\u001b[0m\n", "879052713\n", "\n"]}], "source": ["x = 639464510\n", "y = 139588203\n", "targets, output, results = infer_additive_fn(x, y)\n", "print(results)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m611114510 + 121118203 = 732232713\u001b[0m\n", "732232713\n", "\n"]}], "source": ["x = 611114510\n", "y = 121118203\n", "targets, output, results = infer_additive_fn(x, y)\n", "print(results)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" \n", "# convert 639464510 into a variable input_left as Number\n", "#\n", "input_left = Number(digits=[(0, 0), (1, 1), (5, 2), (4, 3), (6, 4), (4, 5), (9, 6), (3, 7), (6, 8), (0, 9)])\n", "# convert 139588203 into a variable input_right as Number\n", "#\n", "input_right = Number(digits=[(3, 0), (0, 1), (2, 2), (8, 3), (8, 4), (5, 5), (9, 6), (3, 7), (1, 8), (0, 9)])\n", "# compute the results\n", "results = AdditionRule(\n", "  input_left=input_left,\n", "  input_right=input_right,\n", "  procedure=[PerPositionAddition(left=0, right=3, position=0, carry_for_next_pos=0, result=3), PerPositionAddition(left=1, right=0, position=1, carry_for_next_pos=0, result=1), PerPositionAddition(left=5, right=2, position=2, carry_for_next_pos=0, result=7), PerPositionAddition(left=4, right=8, position=3, carry_for_next_pos=1, result=2), PerPositionAddition(left=6, right=8, position=4, carry_for_next_pos=1, result=5), PerPositionAddition(left=4, right=5, position=5, carry_for_next_pos=1, result=0), PerPositionAddition(left=9, right=9, position=6, carry_for_next_pos=1, result=9), PerPositionAddition(left=3, right=3, position=7, carry_for_next_pos=1, result=7), PerPositionAddition(left=6, right=1, position=8, carry_for_next_pos=0, result=8), PerPositionAddition(left=0, right=0, position=9, carry_for_next_pos=0, result=0)],\n", "  output=Number(digits=[(3, 0), (1, 1), (7, 2), (2, 3), (5, 4), (0, 5), (9, 6), (7, 7), (8, 8)])\n", ")\n", "print(get_number(results.output))\n", "\n"]}], "source": ["print(output)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "# convert 639464510 into a variable KVitaTCMSU as Number\n", "#\n", "KVitaTCMSU = Number(digits=[(0, 0), (1, 1), (5, 2), (4, 3), (6, 4), (4, 5), (9, 6), (3, 7), (6, 8)])\n", "# convert 139588203 into a variable og as Number\n", "#\n", "og = Number(digits=[(3, 0), (0, 1), (2, 2), (8, 3), (8, 4), (5, 5), (9, 6), (3, 7), (1, 8)])\n", "# compute the result\n", "result = AdditionRule(\n", "  input_left=KVitaTCMSU,\n", "  input_right=og,\n", "  procedure=[PerPositionAddition(left=0, right=3, position=0, carry_for_next_pos=0, result=3), PerPositionAddition(left=1, right=0, position=1, carry_for_next_pos=0, result=1), PerPositionAddition(left=5, right=2, position=2, carry_for_next_pos=0, result=7), PerPositionAddition(left=4, right=8, position=3, carry_for_next_pos=1, result=2), PerPositionAddition(left=6, right=8, position=4, carry_for_next_pos=1, result=5), PerPositionAddition(left=4, right=5, position=5, carry_for_next_pos=1, result=0), PerPositionAddition(left=9, right=9, position=6, carry_for_next_pos=1, result=9), PerPositionAddition(left=3, right=3, position=7, carry_for_next_pos=0, result=7), PerPositionAddition(left=6, right=1, position=8, carry_for_next_pos=0, result=7)],\n", "  output=Number(digits=[(3, 0), (1, 1), (7, 2), (2, 3), (5, 4), (0, 5), (9, 6), (7, 7), (7, 8)])\n", ")\n", "print(get_number(result.output))\n", "# expect the result to be 779052713\n", "\n"]}], "source": ["print(targets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs, targets = create_structure(6, 1002)\n", "_, code_result = sandbox_execute(inputs + targets, 2.0)\n", "print(code_result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(targets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x, y = 218531256137, 15316\n", "targets, output, results = infer_additive_fn(x, y)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "targets, output, results = infer_additive_fn(\n", "    random.randint(1, int(1e22)), random.randint(1, int(1e22))\n", ")\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "targets, output, results = infer_additive_fn(\n", "    random.randint(1, int(1e22)), random.randint(1, int(1e22))\n", ")\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tokens = tokenizer.tokenize(\"0100000001\")\n", "# for token in tokens:\n", "#     print(tokenizer.detokenize([token]))\n", "# print(tokens)\n", "xset = set()\n", "for token, text in tokenizer.inv_vocab.items():\n", "    if text.isdigit():\n", "        xset.add(text)\n", "print(sorted(list(xset)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs, targets = create_structure(\"8359639508934984613376\", \"6259748604334932180609\")\n", "print(inputs + targets)\n", "_, results = sandbox_execute(inputs + targets, 1.0)\n", "print(results)\n", "print(10999999999999999596477620176 + 999999999601477620176)\n", "print(len(tokenizer.tokenize(inputs + targets)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["targets, output, results = infer_additive_fn(\n", "    1111111111111111111111, 1111111111111111111111111\n", ")\n", "print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["targets, output, results = infer_additive_fn(1000035, 12003215)\n", "print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 0~1e18\n", "targets, output, results = infer_additive_fn(\n", "    10000000000000000000000035, 1200000000000000763215\n", ")\n", "print(output)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# random.randint(1, int(1e18))\n", "# 10% 1e17, 1e17 ~ 1e18\n", "#\n", "# 0~1e18\n", "//// 0~1e27\n", "# convert 10000000000000000000000035 into a variable input_left as Number\n", "#\n", "input_left = Number(digits=[(5, 0), (3, 1), (0, 2), (0, 3), (0, 4), (0, 5), (0, 6), (0, 7), (0, 8), (0, 9), (0, 10), (0, 11), (0, 12), (0, 13), (0, 14), (0, 15), (0, 16), (0, 17), (0, 18), (0, 19), (0, 20), (0, 21), (0, 22), (0, 23), (0, 24), (0, 25), (0, 26)])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# convert 1111111111111111111111111111111111"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 10999999999999999596477620176\n", "#         999999999601477620176\n", "inputs, targets = create_structure(\"12415\", \"531\")\n", "print(inputs + targets)\n", "_, results = sandbox_execute(inputs + targets, 1.0)\n", "print(results)\n", "print(10999999999999999596477620176 + 999999999601477620176)\n", "print(len(tokenizer.tokenize(inputs + targets)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(\"1111111111111111111111\"))\n", "print(len(\"1111111111111111111111111\"))\n", "print(len(\"2222222222222222222222222\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 10999999999999999596477620176\n", "#         999999999601477620176\n", "inputs, targets = create_structure(\n", "    \"10999999999999999596477620176\", \"999999999601477620176\"\n", ")\n", "print(inputs + targets)\n", "_, results = sandbox_execute(inputs + targets, 1.0)\n", "print(results)\n", "print(10999999999999999596477620176 + 999999999601477620176)\n", "print(len(tokenizer.tokenize(inputs + targets)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs, targets = create_structure(\"124\", \"512\")\n", "print(inputs + targets)\n", "_, results = sandbox_execute(inputs + targets, 1.0)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = Number(convert_to_digits(\"9999999999999999583154900968\"))\n", "print(get_number(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_left = Number(\n", "    digits=[\n", "        (8, 0),\n", "        (0, 1),\n", "        (2, 2),\n", "        (9, 3),\n", "        (1, 4),\n", "        (7, 5),\n", "        (2, 6),\n", "        (2, 7),\n", "        (3, 8),\n", "        (3, 9),\n", "        (1, 10),\n", "        (0, 11),\n", "        (0, 12),\n", "        (0, 13),\n", "        (0, 14),\n", "        (0, 15),\n", "        (0, 16),\n", "        (0, 17),\n", "        (0, 18),\n", "        (0, 19),\n", "        (0, 20),\n", "        (0, 21),\n", "    ]\n", ")\n", "input_right = Number(\n", "    digits=[\n", "        (8, 0),\n", "        (6, 1),\n", "        (9, 2),\n", "        (0, 3),\n", "        (0, 4),\n", "        (9, 5),\n", "        (4, 6),\n", "        (5, 7),\n", "        (1, 8),\n", "        (3, 9),\n", "        (8, 10),\n", "        (5, 11),\n", "        (9, 12),\n", "        (9, 13),\n", "        (9, 14),\n", "        (9, 15),\n", "        (9, 16),\n", "        (9, 17),\n", "        (9, 18),\n", "        (9, 19),\n", "        (9, 20),\n", "        (0, 21),\n", "    ]\n", ")\n", "\n", "print(get_number(input_left))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(\"099999999584477620176\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.pad_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs, targets = create_structure(104214, 9421)\n", "tokens = tokenizer.tokenize(inputs) + tokenizer.tokenize(targets)\n", "code = tokenizer.detokenize(tokens)\n", "passed, stdout = sandbox_execute(code, timeout=2.0)\n", "print(stdout)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["104214 + 9421"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Response:\n", "\n", "# Convert the inputs to Number\n", "input_left = Number(digits=[(4, 0), (1, 1), (2, 2), (4, 3), (0, 4), (1, 5), (0, 6)])\n", "input_right = Number(digits=[(4, 0), (1, 1), (2, 2), (4, 3), (0, 4), (1, 5), (0, 6)])\n", "# Compute the results\n", "results = AdditionRule(\n", "    input_left=input_left,\n", "    input_right=input_right,\n", "    procedure=[\n", "        PerPositionAddition(\n", "            left=4, right=1, position=0, carry_for_next_pos=0, result=5\n", "        ),\n", "        PerPositionAddition(\n", "            left=1, right=2, position=1, carry_for_next_pos=0, result=3\n", "        ),\n", "        PerPositionAddition(\n", "            left=2, right=4, position=2, carry_for_next_pos=0, result=6\n", "        ),\n", "        PerPositionAddition(\n", "            left=4, right=9, position=3, carry_for_next_pos=1, result=3\n", "        ),\n", "        PerPositionAddition(\n", "            left=0, right=0, position=4, carry_for_next_pos=0, result=1\n", "        ),\n", "        PerPositionAddition(\n", "            left=1, right=0, position=5, carry_for_next_pos=0, result=1\n", "        ),\n", "        PerPositionAddition(\n", "            left=0, right=0, position=6, carry_for_next_pos=0, result=0\n", "        ),\n", "    ],\n", "    output=Number(digits=[(5, 0), (3, 1), (6, 2), (3, 3), (1, 4), (1, 5)]),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results, output = infer_additive_fn(4, 6)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results, output = infer_multiplicative_fn(1002, 875421)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = AdditionRule.calculate_output(1002, 875421)\n", "print(format_dataclass(x))\n", "print(get_number(x.output))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x.input_left = Reference(\"input_left\")\n", "print(format_dataclass(x))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(results[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results, output = infer_additive_fn(112_401_240_012, 31_875_429)\n", "print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}