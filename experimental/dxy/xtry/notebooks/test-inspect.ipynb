{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import re\n", "import tqdm\n", "import numpy as np\n", "\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "\n", "tokenizer = Llama2Tokenizer()\n", "\n", "def extract_content(input_string):\n", "    pattern = r'compute(.*?) by'\n", "    matches = re.findall(pattern, input_string, re.DOTALL)\n", "    \n", "    # Strip each match of leading and trailing whitespaces\n", "    return [match.strip() for match in matches]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n"]}], "source": ["dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/xtry.code/augstruct-train-1e18-r1\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "import typing\n", "import dataclasses\n", "\n", "Type0to9 = int  # should be 0~9\n", "TypeDigit = typing.Tuple[Type0to9, int]  # value and position\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: int\n", "    right: int\n", "    position: int\n", "    carry_for_next_pos: int\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[TypeDigit]\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    input_left: Number\n", "    input_right: Number\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "def get_number(obj: typing.Union[int, str, Number]) -> str:\n", "    if isinstance(obj, Number):\n", "        output_digits = sorted(obj.digits, key=lambda x: -x[-1])\n", "        return \"\".join(map(str, [digit[0] for digit in output_digits]))\n", "    else:\n", "        return str(int(obj))\n", "\n", "# convert 2612 into Number named as i8JaimmA\n", " i8JaimmA = Number(digits=[(2, 0), (1, 1), (6, 2), (2, 3)]) \n", "import typing\n", "import dataclasses\n", "\n", "Type0to9 = int  # should be 0~9\n", "TypeDigit = typing.Tuple[Type0to9, int]  # value and position\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: int\n", "    right: int\n", "    position: int\n", "    carry_for_next_pos: int\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[TypeDigit]\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    input_left: Number\n", "    input_right: Number\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "def get_number(obj: typing.Union[int, str, Number]) -> str:\n", "    if isinstance(obj, Number):\n", "        output_digits = sorted(obj.digits, key=lambda x: -x[-1])\n", "        return \"\".join(map(str, [digit[0] for digit in output_digits]))\n", "    else:\n", "        return str(int(obj))\n", "\n", "# convert 50149061078477671095 into Number named as pniMnZCi\n", " pniMnZCi = Number(\n", "  digits=[(5, 0), (9, 1), (0, 2), (1, 3), (7, 4), (6, 5), (7, 6), (7, 7), (4, 8), (8, 9), (7, 10), (0, 11), (1, 12), (6, 13), (0, 14), (9, 15), (4, 16), (1, 17), (0, 18), (5, 19)]\n", ") \n", "import typing\n", "import dataclasses\n", "\n", "Type0to9 = int  # should be 0~9\n", "TypeDigit = typing.Tuple[Type0to9, int]  # value and position\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: int\n", "    right: int\n", "    position: int\n", "    carry_for_next_pos: int\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[TypeDigit]\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    input_left: Number\n", "    input_right: Number\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "def get_number(obj: typing.Union[int, str, Number]) -> str:\n", "    if isinstance(obj, Number):\n", "        output_digits = sorted(obj.digits, key=lambda x: -x[-1])\n", "        return \"\".join(map(str, [digit[0] for digit in output_digits]))\n", "    else:\n", "        return str(int(obj))\n", "\n", "# convert 83087734488015834441 into Number named as M7WHzAMApl\n", " M7WHzAMApl = Number(\n", "  digits=[(1, 0), (4, 1), (4, 2), (4, 3), (3, 4), (8, 5), (5, 6), (1, 7), (0, 8), (8, 9), (8, 10), (4, 11), (4, 12), (3, 13), (7, 14), (7, 15), (8, 16), (0, 17), (3, 18), (8, 19)]\n", ") \n", "import typing\n", "import dataclasses\n", "\n", "Type0to9 = int  # should be 0~9\n", "TypeDigit = typing.Tuple[Type0to9, int]  # value and position\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: int\n", "    right: int\n", "    position: int\n", "    carry_for_next_pos: int\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[TypeDigit]\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    input_left: Number\n", "    input_right: Number\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "def get_number(obj: typing.Union[int, str, Number]) -> str:\n", "    if isinstance(obj, Number):\n", "        output_digits = sorted(obj.digits, key=lambda x: -x[-1])\n", "        return \"\".join(map(str, [digit[0] for digit in output_digits]))\n", "    else:\n", "        return str(int(obj))\n", "\n", "# convert 53710264000424378498776149 into Number named as UNKF4nfXct\n", " UNKF4nfXct = Number(\n", "  digits=[(9, 0), (4, 1), (1, 2), (6, 3), (7, 4), (7, 5), (8, 6), (9, 7), (4, 8), (8, 9), (7, 10), (3, 11), (4, 12), (2, 13), (4, 14), (0, 15), (0, 16), (0, 17), (4, 18), (6, 19), (2, 20), (0, 21), (1, 22), (7, 23), (3, 24), (5, 25)]\n", ") \n", "import typing\n", "import dataclasses\n", "\n", "Type0to9 = int  # should be 0~9\n", "TypeDigit = typing.Tuple[Type0to9, int]  # value and position\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: int\n", "    right: int\n", "    position: int\n", "    carry_for_next_pos: int\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[TypeDigit]\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    input_left: Number\n", "    input_right: Number\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "def get_number(obj: typing.Union[int, str, Number]) -> str:\n", "    if isinstance(obj, Number):\n", "        output_digits = sorted(obj.digits, key=lambda x: -x[-1])\n", "        return \"\".join(map(str, [digit[0] for digit in output_digits]))\n", "    else:\n", "        return str(int(obj))\n", "\n", "# compute 207 + 874461258 by utilizing AdditionRule\n", " \n", "# convert 207 and 874461258 to Number\n", "input_left = Number(digits=[(7, 0), (0, 1), (2, 2), (0, 3), (0, 4), (0, 5), (0, 6), (0, 7), (0, 8), (0, 9)])\n", "input_right = Number(digits=[(8, 0), (5, 1), (2, 2), (1, 3), (6, 4), (4, 5), (4, 6), (7, 7), (8, 8), (0, 9)])\n", "# Compute the results\n", "results = AdditionRule(\n", "  input_left=input_left,\n", "  input_right=input_right,\n", "  procedure=[PerPositionAddition(left=7, right=8, position=0, carry_for_next_pos=1, result=5), PerPositionAddition(left=0, right=5, position=1, carry_for_next_pos=0, result=6), PerPositionAddition(left=2, right=2, position=2, carry_for_next_pos=0, result=4), PerPositionAddition(left=0, right=1, position=3, carry_for_next_pos=0, result=1), PerPositionAddition(left=0, right=6, position=4, carry_for_next_pos=0, result=6), PerPositionAddition(left=0, right=4, position=5, carry_for_next_pos=0, result=4), PerPositionAddition(left=0, right=4, position=6, carry_for_next_pos=0, result=4), PerPositionAddition(left=0, right=7, position=7, carry_for_next_pos=0, result=7), PerPositionAddition(left=0, right=8, position=8, carry_for_next_pos=0, result=8), PerPositionAddition(left=0, right=0, position=9, carry_for_next_pos=0, result=0)],\n", "  output=Number(\n", "    digits=[(5, 0), (6, 1), (4, 2), (1, 3), (6, 4), (4, 5), (4, 6), (7, 7), (8, 8)]\n", "  )\n", ")\n", "print(get_number(results.output))\n", " \n", "import typing\n", "import dataclasses\n", "\n", "Type0to9 = int  # should be 0~9\n", "TypeDigit = typing.Tuple[Type0to9, int]  # value and position\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: int\n", "    right: int\n", "    position: int\n", "    carry_for_next_pos: int\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[TypeDigit]\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    input_left: Number\n", "    input_right: Number\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "def get_number(obj: typing.Union[int, str, Number]) -> str:\n", "    if isinstance(obj, Number):\n", "        output_digits = sorted(obj.digits, key=lambda x: -x[-1])\n", "        return \"\".join(map(str, [digit[0] for digit in output_digits]))\n", "    else:\n", "        return str(int(obj))\n", "\n", "# compute 9607557679 + 9731144775 by utilizing AdditionRule\n", " \n", "# convert 9607557679 and 9731144775 to Number\n", "input_left = Number(digits=[(9, 0), (7, 1), (6, 2), (7, 3), (5, 4), (5, 5), (7, 6), (0, 7), (6, 8), (9, 9), (0, 10)])\n", "input_right = Number(digits=[(5, 0), (7, 1), (7, 2), (4, 3), (4, 4), (1, 5), (1, 6), (3, 7), (7, 8), (9, 9), (0, 10)])\n", "# Compute the results\n", "results = AdditionRule(\n", "  input_left=input_left,\n", "  input_right=input_right,\n", "  procedure=[PerPositionAddition(left=9, right=5, position=0, carry_for_next_pos=1, result=4), PerPositionAddition(left=7, right=7, position=1, carry_for_next_pos=1, result=5), PerPositionAddition(left=6, right=7, position=2, carry_for_next_pos=1, result=4), PerPositionAddition(left=7, right=4, position=3, carry_for_next_pos=1, result=2), PerPositionAddition(left=5, right=4, position=4, carry_for_next_pos=1, result=0), PerPositionAddition(left=5, right=1, position=5, carry_for_next_pos=0, result=7), PerPositionAddition(left=7, right=1, position=6, carry_for_next_pos=0, result=8), PerPositionAddition(left=0, right=3, position=7, carry_for_next_pos=0, result=3), PerPositionAddition(left=6, right=7, position=8, carry_for_next_pos=1, result=3), PerPositionAddition(left=9, right=9, position=9, carry_for_next_pos=1, result=9), PerPositionAddition(\n", "    left=0,\n", "    right=0,\n", "    position=10,\n", "    carry_for_next_pos=0,\n", "    result=1\n", "  )],\n", "  output=Number(\n", "    digits=[(4, 0), (5, 1), (4, 2), (2, 3), (0, 4), (7, 5), (8, 6), (3, 7), (3, 8), (9, 9), (1, 10)]\n", "  )\n", ")\n", "print(get_number(results.output))\n", "\n"]}], "source": ["index = 13\n", "example = np.abs(dataset[index]).tolist()\n", "print(tokenizer.detok<PERSON>ze(example))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["valid_dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/xtry.code/structure-valid-0_1e9-r1\")\n", "raw_valid_dataset = []\n", "for i in tqdm.tqdm(range(len(valid_dataset)), total=len(valid_dataset)):\n", "    example: list[int] = np.abs(valid_dataset[i]).tolist()\n", "    text: str = tokenizer.detokenize(example)\n", "    for inst in extract_content(text):\n", "        raw_valid_dataset.append(inst)\n", "print(f\"Get {len(list(set(raw_valid_dataset)))} / {len(raw_valid_dataset)} raw data.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# i = 10\n", "# example: list[int] = np.abs(valid_dataset[i]).tolist()\n", "# text: str = tokenizer.detokenize(example)\n", "# print(text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/xtry.code/augstruct-train-1e18-r1\")\n", "raw_train_dataset = []\n", "for i in tqdm.tqdm(range(len(train_dataset)), total=len(train_dataset)):\n", "    example: list[int] = np.abs(train_dataset[i]).tolist()\n", "    text: str = tokenizer.detokenize(example)\n", "    for inst in extract_content(text):\n", "        raw_train_dataset.append(inst)\n", "print(f\"Get {len(list(set(raw_train_dataset)))} / {len(raw_train_dataset)} raw data.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["A = set(raw_valid_dataset)\n", "B = set(raw_train_dataset)\n", "# Test whether A and <PERSON> has intersection\n", "intersection = A & B\n", "print(f\"#intersection: {len(intersection)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}