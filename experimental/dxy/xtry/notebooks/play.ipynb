{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19 + 1512 = 1531\n", "19 + 1512 = 1531\n", "0 * 0 = 0\n", "0 * 0 = 0\n", "19 * 1512 = 28728\n", "19 * 1512 = 28728\n", "2010 * 30 = 60300\n", "2010 * 30 = 60300\n"]}], "source": ["import typing\n", "import dataclasses\n", "from experimental.dxy.xtry.arithmetic import (\n", "    Number,\n", "    PerPositionAddition,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    AdditionRule,\n", "    MultiplicationRule,\n", "    get_number,\n", ")\n", "from experimental.dxy.xtry.dataclasses_ext import format_dataclass\n", "from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "\n", "tokenizer = Llama2Tokenizer()\n", "# Example usage:\n", "# left_number = \"1353218095823195611\"\n", "# right_number = \"235893215321576\"\n", "left_number, right_number = \"19\", \"1512\"\n", "addition_rule = AdditionRule.calculate_output(left_number, right_number)\n", "print(f\"{left_number} + {right_number} = {get_number(addition_rule.output)}\")\n", "print(f\"{left_number} + {right_number} = {int(left_number)+int(right_number)}\")\n", "\n", "left_number = \"0\"\n", "right_number = \"0\"\n", "multiplication_rule = MultiplicationRule.calculate_output(left_number, right_number)\n", "print(f\"{left_number} * {right_number} = {multiplication_rule.get_number()}\")\n", "print(f\"{left_number} * {right_number} = {int(left_number) * int(right_number)}\")\n", "\n", "left_number = \"19\"\n", "right_number = \"1512\"\n", "multiplication_rule = MultiplicationRule.calculate_output(left_number, right_number)\n", "print(f\"{left_number} * {right_number} = {multiplication_rule.get_number()}\")\n", "print(f\"{left_number} * {right_number} = {int(left_number) * int(right_number)}\")\n", "\n", "\n", "left_number = \"2010\"\n", "right_number = \"30\"\n", "multiplication_rule = MultiplicationRule.calculate_output(left_number, right_number)\n", "print(f\"{left_number} * {right_number} = {multiplication_rule.get_number()}\")\n", "print(f\"{left_number} * {right_number} = {int(left_number) * int(right_number)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdditionRule(\n", "  input_left=Number(digits=[(4, 0), (0, 1), (3, 2), (1, 3), (9, 4), (8, 5), (7, 6), (4, 7)]),\n", "  input_right=Number(\n", "    digits=[(1, 0), (6, 1), (0, 2), (5, 3), (0, 4), (4, 5), (3, 6), (4, 7), (1, 8), (8, 9)]\n", "  ),\n", "  procedure=[PerPositionAddition(left=4, right=1, position=0, carry_for_next_pos=0, result=5), PerPositionAddition(left=0, right=6, position=1, carry_for_next_pos=0, result=6), PerPositionAddition(left=3, right=0, position=2, carry_for_next_pos=0, result=3), PerPositionAddition(left=1, right=5, position=3, carry_for_next_pos=0, result=6), PerPositionAddition(left=9, right=0, position=4, carry_for_next_pos=0, result=9), PerPositionAddition(left=8, right=4, position=5, carry_for_next_pos=1, result=2), PerPositionAddition(left=7, right=3, position=6, carry_for_next_pos=1, result=1), PerPositionAddition(left=4, right=4, position=7, carry_for_next_pos=0, result=9), PerPositionAddition(left=0, right=1, position=8, carry_for_next_pos=0, result=1), PerPositionAddition(left=0, right=8, position=9, carry_for_next_pos=0, result=8)],\n", "  output=Number(\n", "    digits=[(5, 0), (6, 1), (3, 2), (6, 3), (9, 4), (2, 5), (1, 6), (9, 7), (1, 8), (8, 9)]\n", "  )\n", ")\n"]}], "source": ["left_number, right_number = \"47891304\", \"8143405061\"\n", "addition_rule = AdditionRule.calculate_output(left_number, right_number)\n", "print(format_dataclass(addition_rule))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdditionRule(\n", "  input_left=Number(digits=[(0, 0)]),\n", "  input_right=Number(digits=[(0, 0)]),\n", "  procedure=[PerPositionAddition(left=0, right=0, position=0, carry_for_next_pos=0, result=0)],\n", "  output=Number(digits=[(0, 0)])\n", ")\n"]}], "source": ["left_number, right_number = \"0\", \"0\"\n", "addition_rule = AdditionRule.calculate_output(left_number, right_number)\n", "print(format_dataclass(addition_rule))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AdditionRule(\n", "  input_left=Number(digits=[(9, 0), (1, 1), (0, 2), (0, 3), (0, 4)]),\n", "  input_right=Number(digits=[(2, 0), (1, 1), (5, 2), (1, 3), (0, 4)]),\n", "  procedure=[PerPositionAddition(left=9, right=2, position=0, carry_for_next_pos=1, result=1), PerPositionAddition(left=1, right=1, position=1, carry_for_next_pos=0, result=3), PerPositionAddition(left=0, right=5, position=2, carry_for_next_pos=0, result=5), PerPositionAddition(left=0, right=1, position=3, carry_for_next_pos=0, result=1), PerPositionAddition(left=0, right=0, position=4, carry_for_next_pos=0, result=0)],\n", "  output=Number(digits=[(1, 0), (3, 1), (5, 2), (1, 3)])\n", ")\n"]}], "source": ["print(format_dataclass(addition_rule))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MultiplicationRule(\n", "  input_left=Number(digits=[(0, 0), (1, 1), (0, 2), (2, 3)]),\n", "  input_right=Number(digits=[(0, 0), (3, 1)]),\n", "  procedure=[PerPosMul(position=0, temp_results=(0,)), PerPosMul(position=1, temp_results=(0, 0)), PerPosMul(position=2, temp_results=(3, 0)), PerPosMul(position=3, temp_results=(0, 0)), PerPosMul(position=4, temp_results=(6,))],\n", "  output=Number(digits=[(0, 0), (0, 1), (3, 2), (0, 3), (6, 4)])\n", ")\n"]}], "source": ["print(format_dataclass(multiplication_rule))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 20 * 20 -> 400 -> 4000 -> 20000"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[29871, 31171]\n"]}], "source": ["print(tokenizer.tokenize(\"☺\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = r\"\"\"AdditionRule(\n", "    input_left=Number(digits=[(9, 0), (1, 1), (0, 2), (0, 3), (0, 4)]),\n", "    input_right=Number(digits=[(2, 0), (1, 1), (5, 2), (1, 3), (0, 4)]),\n", "    procedure=[\n", "        PerPositionAddition(\n", "            left=9, right=0, position=0, carry_for_next_pos=1, result=1\n", "        ),\n", "        PerPositionAddition(\n", "            left=1, right=1, position=1, carry_for_next_pos=0, result=3\n", "        ),\n", "        PerPositionAddition(\n", "            left=0, right=2, position=2, carry_for_next_pos=0, result=5\n", "        ),\n", "        PerPositionAddition(\n", "            left=0, right=3, position=3, carry_for_next_pos=0, result=1\n", "        ),\n", "        PerPositionAddition(\n", "            left=0, right=4, position=4, carry_for_next_pos=0, result=0\n", "        ),\n", "    ],\n", "    output=Number(digits=[(1, 0), (3, 1), (5, 2), (1, 3)]),\n", ")\"\"\"\n", "print(len(tokenizer.tokenize(x)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["multiplication_rule = MultiplicationRule.calculate_output(\n", "    \"1000000000000000013287555072\", \"1000000000000000013287555072\"\n", ")\n", "print(multiplication_rule.get_number())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(str(multiplication_rule))\n", "print(repr(multiplication_rule))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import black\n", "\n", "\n", "def format_dataclass(obj: typing.Any):\n", "    code_str = str(obj)\n", "    return black.format_str(code_str, mode=black.FileMode())\n", "\n", "\n", "print(format_dataclass(multiplication_rule))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["temp1 = format_dataclass(multiplication_rule)\n", "temp2 = eval(temp1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(temp1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["MultiplicationRule(\n", "    inputs=(\n", "        Number(digits=[Digit(value=9, position=0), Digit(value=1, position=1)]),\n", "        Number(\n", "            digits=[\n", "                Digit(value=2, position=0),\n", "                Digit(value=1, position=1),\n", "                Digit(value=5, position=2),\n", "                Digit(value=1, position=3),\n", "            ]\n", "        ),\n", "    ),\n", "    procedure=[\n", "        PerPositionMultiplication(\n", "            left=Digit(value=9, position=0),\n", "            right=Number(\n", "                digits=[\n", "                    Digit(value=2, position=0),\n", "                    Digit(value=1, position=1),\n", "                    Digit(value=5, position=2),\n", "                    Digit(value=1, position=3),\n", "                ]\n", "            ),\n", "            result=Number(\n", "                digits=[\n", "                    Digit(value=8, position=0),\n", "                    Digit(value=0, position=1),\n", "                    Digit(value=6, position=2),\n", "                    Digit(value=3, position=3),\n", "                    Digit(value=1, position=4),\n", "                ]\n", "            ),\n", "        ),\n", "        PerPositionMultiplication(\n", "            left=Digit(value=1, position=1),\n", "            right=Number(\n", "                digits=[\n", "                    Digit(value=2, position=0),\n", "                    Digit(value=1, position=1),\n", "                    Digit(value=5, position=2),\n", "                    Digit(value=1, position=3),\n", "                ]\n", "            ),\n", "            result=Number(\n", "                digits=[\n", "                    Digit(value=0, position=0),\n", "                    Digit(value=2, position=1),\n", "                    Digit(value=1, position=2),\n", "                    Digit(value=5, position=3),\n", "                    Digit(value=1, position=4),\n", "                ]\n", "            ),\n", "        ),\n", "    ],\n", "    output=Number(\n", "        digits=[\n", "            Digit(value=8, position=0),\n", "            Digit(value=2, position=1),\n", "            Digit(value=7, position=2),\n", "            Digit(value=8, position=3),\n", "            Digit(value=2, position=4),\n", "        ]\n", "    ),\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}