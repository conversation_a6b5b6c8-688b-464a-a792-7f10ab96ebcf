"""The arithmetic simulator."""
import collections
import dataclasses
import typing

Type0to9 = int  # should be 0~9
TypeDigit = typing.Tuple[Type0to9, int]  # value and position


@dataclasses.dataclass
class PerPositionAddition:
    """A class to maintain the addition at each position"""

    left: int
    """The left digit value."""

    right: int
    """The right digit value."""

    position: int
    """The position of the addition of two digits."""

    carry_for_next_pos: int
    """The additional carried value to the next position, value is 0 or 1."""

    result: Type0to9


@dataclasses.dataclass
class Number:
    """A number is a list of digits, whose positions are from 0 to N-1."""

    digits: typing.List[TypeDigit]

    def purify(self) -> "Number":
        """Remove leading zeros from the number."""
        digits = sorted(self.digits, key=lambda x: x[-1])
        while len(digits) > 1 and digits[-1][0] == 0:
            digits.pop()
        return Number(digits)

    @classmethod
    def create_from_str(cls, number: str):
        return cls(
            [(int(digit), position) for position, digit in enumerate(reversed(number))]
        )


@dataclasses.dataclass
class AdditionRule:
    input_left: Number
    input_right: Number
    procedure: typing.List[PerPositionAddition]
    output: Number

    @classmethod
    def calculate_output(
        cls,
        left_number: typing.Union[int, str, Number],
        right_number: typing.Union[int, str, Number],
    ) -> "AdditionRule":
        # Convert the input numbers to lists of digits
        left_digits = convert_to_digits(left_number)
        right_digits = convert_to_digits(right_number)

        # Ensure the input numbers have the same number of digits by adding leading zeros if necessary
        # +1 is to handle the potential additional carry
        max_len = max(len(left_digits), len(right_digits)) + 1
        left_digits += [(0, i) for i in range(len(left_digits), max_len)]
        right_digits += [(0, i) for i in range(len(right_digits), max_len)]
        # Perform addition per position
        procedure: typing.List[PerPositionAddition] = []
        carry = 0
        for left_digit, right_digit in zip(left_digits, right_digits):
            assert left_digit[-1] == right_digit[-1]
            procedure.append(
                PerPositionAddition(
                    left_digit[0],
                    right_digit[0],
                    position=left_digit[-1],
                    carry_for_next_pos=(left_digit[0] + right_digit[0] + carry) // 10,
                    result=(left_digit[0] + right_digit[0] + carry) % 10,
                )
            )
            carry = procedure[-1].carry_for_next_pos
        # Create the output number from the result digits
        output_digits = [(step.result, step.position) for step in procedure]
        # Remove the leading zero for the procedure.
        while (
            len(procedure) > 1
            and procedure[-1].left == 0
            and procedure[-1].right == 0
            and procedure[-1].carry_for_next_pos == 0
        ):
            procedure.pop()

        return cls(
            input_left=Number(left_digits).purify(),
            input_right=Number(right_digits).purify(),
            procedure=procedure,
            output=Number(output_digits).purify(),
        )


class Reference:
    def __init__(self, reference: str) -> None:
        self.reference = reference

    def __repr__(self) -> str:
        return str(self.reference)


@dataclasses.dataclass
class PerPosMul:
    position: int
    temp_results: tuple[int, ...]


@dataclasses.dataclass
class MultiplicationRule:
    input_left: Number
    input_right: Number
    procedure: typing.List[PerPosMul]
    output: Number

    def get_number(self) -> str:
        output_digits = sorted(self.output.digits, key=lambda x: -x[-1])
        return "".join(map(str, [digit[0] for digit in output_digits]))

    @classmethod
    def calculate_output(
        cls,
        left_number: typing.Union[int, str, Number],
        right_number: typing.Union[int, str, Number],
    ) -> "MultiplicationRule":
        # Convert the input numbers to lists of digits
        left_digits = convert_to_digits(left_number)
        right_digits = convert_to_digits(right_number)

        # Perform multiplication per position
        value_per_position: dict[int, list[int]] = collections.defaultdict(list)
        for left_digit in left_digits:
            for right_digit in right_digits:
                pos = left_digit[-1] + right_digit[-1]
                value_per_position[pos].append(left_digit[0] * right_digit[0])
        procedure: typing.List[PerPosMul] = []
        for pos in sorted(list(value_per_position.keys())):
            procedure.append(PerPosMul(pos, tuple(value_per_position[pos])))
        # First, calculate the sums of the values in value_per_position
        # and handle carrying
        results_with_carry = collections.defaultdict(int)
        for step in procedure:
            results_with_carry[step.position] = sum(step.temp_results)

        # Now handle any carrying that needs to occur
        carry, position = 0, 0
        final_digits = []
        for position in range(
            max(results_with_carry) + 1
        ):  # Iterate through each position
            total = results_with_carry[position] + carry
            digit = total % 10
            carry = total // 10
            final_digits.append((digit, position))
        while carry > 0:
            position += 1
            digit = carry % 10
            carry //= 10
            final_digits.append((digit, position))
        return cls(
            input_left=Number(left_digits),
            input_right=Number(right_digits),
            procedure=procedure,
            output=Number(final_digits).purify(),
        )


def convert_to_digits(number: typing.Union[int, str, Number]) -> typing.List[TypeDigit]:
    """Convert a number to a list of digits."""
    if isinstance(number, (int, str)):
        number = Number(
            [
                (int(digit), position)
                for position, digit in enumerate(reversed(str(number)))
            ]
        )
    return number.purify().digits


def get_number(obj: typing.Union[int, str, Number]) -> str:
    """Convert a number to a string."""
    if isinstance(obj, Number):
        output_digits = sorted(obj.digits, key=lambda x: -x[-1])
        return "".join(map(str, [digit[0] for digit in output_digits]))
    else:
        return str(int(obj))
