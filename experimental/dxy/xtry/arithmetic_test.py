"""The arithmetic simulator test.

pytest experimental/dxy/xtry/arithmetic_test.py
"""
import random
import unittest

from parameterized import parameterized

from experimental.dxy.xtry.arithmetic import (  # noqa: F401 pylint: disable=unused-import
    AdditionRule,
    MultiplicationRule,
    Number,
    PerPositionAddition,
    PerPosMul,
    convert_to_digits,
    get_number,
)
from experimental.dxy.xtry.dataclasses_ext import format_dataclass


class ArithmeticTest(unittest.TestCase):
    """The arithmetic simulator test."""

    def test_digits(self):
        assert convert_to_digits(21) == [(1, 0), (2, 1)]
        assert convert_to_digits(0) == [(0, 0)]
        assert convert_to_digits(321) == [(1, 0), (2, 1), (3, 2)]
        assert Number([(0, 0), (0, 1)]).purify() == Number([(0, 0)])

    @parameterized.expand([10, int(1e10), int(1e20), int(1e30)])
    def test_addition(self, max_num: int):
        """Unit test for the addition function."""
        pairs, max_tries = set(), 100
        for _ in range(max_tries):
            a = random.randint(0, max_num)
            b = random.randint(0, max_num)
            pairs.add((a, b))
            pairs.add((b, a))
        pairs.add((0, 0))
        pairs.add((max_num, max_num))
        for a, b in pairs:
            result = AdditionRule.calculate_output(str(a), str(b))
            self.assertEqual(get_number(result.output), str(a + b))
            format_str = format_dataclass(result)
            self.assertEqual(get_number(eval(format_str).output), str(a + b))

    @parameterized.expand([10, int(1e10), int(1e20), int(1e30)])
    def test_multiplication(self, max_num: int):
        """Unit test for the multiplication function."""
        pairs, max_tries = set(), 100
        for _ in range(max_tries):
            a = random.randint(0, max_num)
            b = random.randint(0, max_num)
            pairs.add((a, b))
            pairs.add((b, a))
        pairs.add((0, 0))
        pairs.add((max_num, max_num))
        for a, b in pairs:
            result = MultiplicationRule.calculate_output(str(a), str(b))
            self.assertEqual(result.get_number(), str(a * b))
            format_str = format_dataclass(result)
            self.assertEqual(eval(format_str).get_number(), str(a * b))
