"""Extension for the dataclass."""
import dataclasses
import inspect
import typing

import black


def get_content_before_class_symbol(cls: typing.Type) -> str:
    """Get the class decorator."""
    source_lines, _ = inspect.getsourcelines(cls)
    class_head = f"class {cls.__name__}"
    index = None
    for i, source_line in enumerate(source_lines):
        if source_line.strip().startswith(class_head):
            index = i
            break
    if index is None:
        raise ValueError(f"Does not find class head for {''.join(source_lines)}")
    else:
        return "".join(source_lines[:index])


def get_class_string(cls: typing.Type) -> str:
    """Get the class string."""
    class_string_by_cls: typing.Dict[typing.Type, str] = {}
    python_builtin_classes = (
        int,
        str,
        float,
        bool,
        list,
    )
    indent_str = "  "

    def process_class(cur_cls: typing.Type) -> None:
        if (cur_cls in class_string_by_cls) or (cur_cls in python_builtin_classes):
            return
        class_string_by_cls[cur_cls] = ""
        if inspect.isclass(cur_cls):
            decorators = get_content_before_class_symbol(cur_cls)
            cur_class_strs = []
            if decorators:
                cur_class_strs.append(decorators)
            cur_class_strs.append(f"class {cur_cls.__name__}:\n")
            # docstr = inspect.getdoc(cur_cls)
            # if docstr is not None:
            #     cur_class_strs.append(docstr + "\n")
            for field in dataclasses.fields(cur_cls):
                cur_class_strs.append(
                    f"{indent_str}{field.name}: {field.type.__name__}\n"
                )
            class_string_by_cls[cur_cls] = "".join(cur_class_strs)
            for field in dataclasses.fields(cur_cls):
                process_class(field.type)

    process_class(cls)
    return "\n\n".join([v for _, v in class_string_by_cls.items()])


# Helper function to add indent
def _add_indent(s: str, num_spaces: int) -> str:
    return "".join(
        " " * num_spaces + line if line else line for line in s.splitlines(True)
    )


def _is_simple_value(obj: typing.Any, max_chars_to_collapse: int) -> bool:
    return len(repr(obj)) <= max_chars_to_collapse


def _rstrip_per_line(s: str):
    return "\n".join([line.rstrip() for line in s.splitlines()])


def format_dataclass(
    obj: typing.Any,
    indent: int = 0,
    *,
    max_chars_to_collapse: int = 88,
    visited_ids: typing.Optional[typing.Set[int]] = None,
) -> str:
    # Avoid cyclic references
    obj_id = id(obj)
    if visited_ids is None:
        visited_ids = set()
    elif obj_id in visited_ids:
        return f"<cyclic reference to {obj.__class__.__name__} with id {obj_id}>"
    # Base case: if it's not a dataclass or a list/tuple, use repr
    if not (dataclasses.is_dataclass(obj) or isinstance(obj, (list, tuple))):
        return repr(obj)
    elif _is_simple_value(obj, max_chars_to_collapse):
        return repr(obj)
    visited_ids.add(obj_id)

    # Format list or tuple
    if isinstance(obj, (list, tuple)):
        element_strings = [
            repr(e) if _is_simple_value(e, max_chars_to_collapse) else format_dataclass(e, indent + 2, visited_ids=visited_ids)
            for e in obj
        ]
        if len(obj) > 1 and not all(_is_simple_value(e, max_chars_to_collapse) for e in obj):
            elements_str = (",\n" + " " * (indent + 2)).join(element_strings)
            elements_str = "\n" + " " * (indent + 2) + elements_str
        else:
            elements_str = ", ".join(element_strings)
        formatted_str = (
            f"[{elements_str}]" if isinstance(obj, list) else f"({elements_str})"
        )
    # Format dataclass
    else:
        class_name = obj.__class__.__name__
        field_strings = [
            f"{field.name}={format_dataclass(getattr(obj, field.name), indent, visited_ids=visited_ids)}"
            for field in dataclasses.fields(obj)
        ]
        # Combine all fields using comma and space
        inner = ",\n".join(field_strings)
        formatted_str = (
            f'{class_name}(\n{_add_indent(inner, indent + 2)}\n{" " * indent})'
        )

    # Remove the current object from visited to allow re-visiting in recursive calls
    visited_ids.remove(obj_id)
    return formatted_str


def format_dataclass_with_black(obj: typing.Any):
    """Format a dataclass with black, which is very slow."""
    code_str = str(obj)
    return black.format_str(code_str, mode=black.FileMode())
