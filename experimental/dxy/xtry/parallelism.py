"""The parallism library."""
import abc
import multiprocessing
import typing

import tqdm


class ParallelProcessor(abc.ABC):
    """The parallel processor."""

    def __init__(self, num_processes: typing.Optional[int] = None):
        if num_processes is None:
            num_processes = multiprocessing.cpu_count()
        self.num_processes = num_processes
        assert self.num_processes > 0, "num_processes must be positive."

    def initialize(self):
        """Initialize some shared variables within a process."""
        pass

    @abc.abstractmethod
    def process(self, example: dict[str, typing.Any]) -> dict[str, typing.Any]:
        """Process a single example."""

    def parallel_process(
        self,
        examples: typing.List[dict[str, typing.Any]],
        chunksize: typing.Optional[int] = 1,
        desc: str = "parallel",
    ) -> typing.List[dict[str, typing.Any]]:
        """Use multiple processes to process a list of examples."""
        results = []
        with multiprocessing.Pool(
            self.num_processes, initializer=self.initialize
        ) as pool:
            imap_iterator = pool.imap_unordered(
                self.process, examples, chunksize=chunksize
            )
            for result in tqdm.tqdm(imap_iterator, total=len(examples), desc=desc):
                results.append(result)
        return results

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(num_processes={self.num_processes})"
