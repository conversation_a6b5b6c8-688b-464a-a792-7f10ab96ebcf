"""Evaluation.

CUDA_VISIBLE_DEVICES=4 python experimental/dxy/xtry/eval.py --output_dir /home/<USER>/logs/math-add --eval randomdigits-9-1000

CUDA_VISIBLE_DEVICES=5 python experimental/dxy/xtry/eval.py --output_dir /home/<USER>/logs/math-add --eval randomdigits-18-1000
"""
import argparse
import pathlib
import random
import typing

import numpy as np
import torch

from experimental.dxy.xtry.create_dataset import create_structure
from research.core import utils_for_file, utils_for_log
from research.eval.generation.execution import sandbox_execute
from research.models.llama2_models import LLAMA2Model
from research.models.meta_model import GenerationOptions


def get_random_num(num_digits: int, num: int) -> set[int]:
    zero2nine = list(str(_) for _ in range(10))
    unique_set: set[int] = set()
    while len(unique_set) < num:
        cur_num_digits = random.randint(1, num_digits)
        cur_digits = [random.choice(zero2nine) for _ in range(cur_num_digits)]
        unique_set.add(int("".join(cur_digits)))
    return unique_set


def load_dataset(
    eval: str, data_dir: typing.Union[str, pathlib.Path]
) -> list[tuple[int, int]]:
    """Load the dataset."""
    data_dir = pathlib.Path(data_dir)
    data_dir.mkdir(exist_ok=True, parents=True)
    eval_path = data_dir / f"{eval}.torch"
    if eval_path.exists():
        print(f"Loading dataset from {eval_path}")
        data = torch.load(eval_path)
        return data["pairs"]

    if eval.startswith("randomdigits"):
        _, num_digits, num = eval.split("-")
        num_digits, num = int(num_digits), int(num)
        unique_numbers = list(get_random_num(num_digits, num))
        random.shuffle(unique_numbers)
        unique_pairs = set()
        while len(unique_pairs) < num:
            x = random.choice(unique_numbers)
            y = random.choice(unique_numbers)
            unique_pairs.add((x, y))
        unique_pairs = list(unique_pairs)
        torch.save({"unique_numbers": unique_numbers, "pairs": unique_pairs}, eval_path)
        return unique_pairs
    else:
        raise ValueError(f"Unknown evaluation set: {eval}")


def infer_additive_fn(model, x: int, y: int) -> tuple[str, str, str]:
    # print(termcolor.colored(f"{x} + {y} = {x + y}", "green"))
    inputs, targets = create_structure(x, y)
    tokens = [model.tokenizer.bos_id] + model.tokenizer.tokenize(inputs)
    output = model.raw_generate(
        tokens, options=GenerationOptions(max_generated_tokens=2048, top_k=0)
    )
    code = inputs + output
    _, code_result = sandbox_execute(code, 2.0)
    return targets, output, code_result


def main(eval: str, output_dir: str):
    """The main function."""
    dataset = load_dataset(eval, pathlib.Path(output_dir) / "data")

    # model_ckp_name = "AUGSTR-1e18"
    model_ckp_name = "AUGSTR-1e18-v2"
    ckp_path_by_name = {
        "AUGSTR-1e18": "/mnt/efs/augment/user/dxy/logs/dxy.xtry.v3/CL-BASE7B-AUGSTR-1e18-step_500-WD1_0-b8-lr_2e-6_cosine/best_ckp_for_1e18_to_1e27_in_llama_format",
        "AUGSTR-1e18-v2": "/mnt/efs/augment/user/dxy/logs/dxy.xtry.11.05/CL-BASE7B-AUGSTR-1e18-S500_WUP50-WD1_0-b8-lr_1e-6_cosine/best_ckp_for_0_1e18_in_llama_format",
    }
    log_dir = pathlib.Path(output_dir) / model_ckp_name
    log_dir.mkdir(exist_ok=True, parents=True)
    logger = utils_for_log.create_logger(__file__, log_dir / f"{eval}.log")
    model = LLAMA2Model(checkpoint_path=pathlib.Path(ckp_path_by_name[model_ckp_name]))
    logger.info(f"Created the model:\n{model}")
    logger.info(f"Evaluation set: {eval}")
    logger.info(f"Total evaluation samples: {len(dataset)}")
    model.load()
    save_dir = log_dir / f"results-{eval}"
    save_dir.mkdir(exist_ok=True, parents=True)
    corrects = []
    for idx, (x, y) in enumerate(dataset):
        targets, output, code_result = infer_additive_fn(model, x, y)
        try:
            correct = int(code_result) == (x + y)
        except ValueError:
            correct = False
        corrects.append(correct)
        utils_for_file.write_json(
            save_dir / f"{idx:04d}-{len(dataset):04d}.json",
            {
                "x": x,
                "y": y,
                "targets": targets,
                "output": output,
                "code_result": code_result,
                "correct": correct,
            },
            indent=2,
        )
        accuracy = np.mean(corrects)
        logger.info(
            f"{idx:04d}/{len(dataset):04d} : accuracy={accuracy*100:.1f}%"
            f", correct={correct}, x={x}, y={y}, x+y={x+y}, output={code_result.strip()}"
        )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="The output directory.",
    )
    parser.add_argument(
        "--eval",
        type=str,
        required=True,
        help="The evaluation set.",
    )
    args = parser.parse_args()
    main(args.eval, args.output_dir)
