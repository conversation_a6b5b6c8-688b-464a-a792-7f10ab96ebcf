"""Create the datasets.

python experimental/dxy/xtry/create_dataset.py
"""
import collections
import copy
import pathlib
import random
import string
import typing

import numpy as np
import torch
from megatron.data.indexed_dataset import MMapIndexedDatasetBuilder
from megatron.tokenizer.tokenizer import Llama2Tokenizer

from experimental.dxy.edits.util_lib import pack_sequences
from experimental.dxy.xtry.arithmetic import (
    AdditionRule,
    Number,
    Reference,
    convert_to_digits,
)
from experimental.dxy.xtry.dataclasses_ext import format_dataclass
from experimental.dxy.xtry.parallelism import ParallelProcessor
from research.core.utils_for_log import create_logger

structure_template = r'''
import typing
import dataclasses

Type0to9 = int
TypeDigit = typing.Tuple[Type0to9, int]


@dataclasses.dataclass
class PerPositionAddition:
    """A class to maintain the addition at each position"""

    left: int
    right: int
    position: int
    carry_for_next_pos: int
    result: Type0to9


@dataclasses.dataclass
class Number:
    digits: typing.List[TypeDigit]

@dataclasses.dataclass
class AdditionRule:
    input_left: Number
    input_right: Number
    procedure: typing.List[PerPositionAddition]
    output: Number

def get_number(obj: typing.Union[int, str, Number]) -> str:
    if isinstance(obj, Number):
        output_digits = sorted(obj.digits, key=lambda x: -x[-1])
        return "".join(map(str, [digit[0] for digit in output_digits]))
    else:
        return str(int(obj))

{instruction}
'''

convert_template = r"""
import typing
import dataclasses

TypeDigit = typing.Tuple[Type0to9, int]

@dataclasses.dataclass
class Number:
    digits: typing.List[TypeDigit]

{instruction}
"""


simple_template = (
    r"""Below is an instruction that describes a task. Write a response that appropriately completes the request.

### Instruction:
{instruction}
""",
    r"""### Response:
{response}
""",
)


def generate_random_word(length):
    if length < 1:
        return ""  # Return an empty string if the length is less than 1

    # Generate the first character, ensuring it's a letter
    first_char = random.choice(string.ascii_letters)

    # Generate the rest of the string, which can include digits
    rest_of_word = "".join(
        random.choice(string.ascii_letters + string.digits) for _ in range(length - 1)
    )

    # Combine the first character and the rest of the string
    random_word = first_char + rest_of_word
    return random_word


def create_structure(
    x: typing.Union[int, str], y: typing.Union[int, str]
) -> typing.Tuple[str, str]:
    """Create the structure inputs."""
    add = AdditionRule.calculate_output(x, y)
    add_w_ref = copy.deepcopy(add)
    # Randomly generate the variable names
    ref_left = (
        generate_random_word(random.randint(1, 10))
        if random.randint(1, 10) == 1
        else "input_left"
    )
    ref_right = (
        generate_random_word(random.randint(1, 10))
        if random.randint(1, 10) == 1
        else "input_right"
    )
    add_w_ref.input_left = Reference(ref_left)  # type: ignore
    add_w_ref.input_right = Reference(ref_right)  # type: ignore
    response = f"""
# convert {x} into a variable {ref_left} as Number
#
{ref_left} = {add.input_left}
# convert {y} into a variable {ref_right} as Number
#
{ref_right} = {add.input_right}
# compute the result
result = {format_dataclass(add_w_ref)}
print(get_number(result.output))
# expect the result to be {int(x) + int(y)}
"""
    return (
        structure_template.format(
            instruction=f"# compute {x} + {y} by utilizing {add.__class__.__name__}"
        ),
        response,
    )


class SimpleFormatter(ParallelProcessor):
    """Format the simple dataset."""

    tokenizer: Llama2Tokenizer

    def initialize(self):
        SimpleFormatter.tokenizer = Llama2Tokenizer()

    def process(self, example: dict[str, typing.Any]) -> dict[str, typing.Any]:
        x, y = example["x"], example["y"]
        solution = (
            simple_template[0].format(instruction=f"compute {x} + {y}"),
            simple_template[1].format(response=f"{x} + {y} = {x + y}"),
        )
        tokens = (
            [self.tokenizer.bos_id]
            + [-x for x in self.tokenizer.tokenize(solution[0])]
            + self.tokenizer.tokenize(solution[1])
            + [self.tokenizer.eos_id]
        )
        return {"tokens": tokens}


class StructureFormatter(ParallelProcessor):
    """Format the structure dataset."""

    tokenizer: Llama2Tokenizer

    def initialize(self):
        StructureFormatter.tokenizer = Llama2Tokenizer()

    def process(self, example: dict[str, typing.Any]) -> dict[str, typing.Any]:
        x, y = example["x"], example["y"]
        inputs, target = create_structure(x, y)
        tokens = (
            [self.tokenizer.bos_id]
            + [-x for x in self.tokenizer.tokenize(inputs)]
            + self.tokenizer.tokenize(target)
            + [self.tokenizer.eos_id]
        )
        return {"tokens": tokens}


class ConvertionFormatter(ParallelProcessor):
    """Format the structure dataset."""

    tokenizer: Llama2Tokenizer

    def initialize(self):
        ConvertionFormatter.tokenizer = Llama2Tokenizer()

    def process(self, example: dict[str, typing.Any]) -> dict[str, typing.Any]:
        x = example["x"]
        var_name = generate_random_word(random.randint(1, 10))
        solution = (
            convert_template.format(
                instruction=f"# convert {x} into a variable {var_name} as Number"
            ),
            f"#\n{var_name} = {format_dataclass(Number(convert_to_digits(x)))}\n",
        )
        tokens = (
            [self.tokenizer.bos_id]
            + [-x for x in self.tokenizer.tokenize(solution[0])]
            + self.tokenizer.tokenize(solution[1])
            + [self.tokenizer.eos_id]
        )
        return {"tokens": tokens}


def check_statistics(dataset: list[list[int]], logger):
    lengths = [len(p) for p in dataset]
    logger.info(f"The average length of the dataset is {np.mean(lengths)}")
    logger.info(f"The maximum length of the dataset is {np.max(lengths)}")
    logger.info(f"The minimum length of the dataset is {np.min(lengths)}")


def create_dataset(
    pairs_and_formatters: list[
        tuple[list[tuple[int, int]], typing.Type[ParallelProcessor]]
    ],
    repeats: int,
    save_path: pathlib.Path,
):
    """Create the datasets."""
    logger = create_logger(__file__)
    tokenizer = Llama2Tokenizer()
    logger.info(
        f"{len(pairs_and_formatters)} formatters,"
        f" repeats={repeats}, save_path={save_path}"
    )
    # Build the dataset.
    dataset: list[list[int]] = []
    for pairs, formatter_cls in pairs_and_formatters:
        formatter = formatter_cls()
        temp_results = formatter.parallel_process(
            [{"x": x, "y": y} for (x, y) in pairs],
            chunksize=10,
            desc=f"{formatter} on {len(pairs)}",
        )
        for temp_res in temp_results:
            for value in temp_res.values():
                dataset.append(value)  # type: ignore
    check_statistics(dataset, logger)
    final_dataset: list[list[int]] = []
    for _ in range(repeats):
        for x in dataset:
            final_dataset.append(x)
    random.shuffle(final_dataset)
    check_statistics(final_dataset, logger)
    sequences, _ = pack_sequences(final_dataset, 4096, -tokenizer.eos_id)
    check_statistics(final_dataset, logger)
    # if skipped:
    #     long_examples = [x for x in final_dataset if len(x) > 4096]
    #     xxxx = tokenizer.detokenize([abs(x) for x in long_examples[0]])
    #     import pdb; pdb.set_trace()
    #     print("-")
    cur_builder = MMapIndexedDatasetBuilder(
        save_path.with_suffix(".bin"), dtype=np.int32
    )
    for sequence in sequences:
        cur_builder.add_item(sequence + [-tokenizer.eos_id])
        cur_builder.end_document()
    cur_builder.finalize(save_path.with_suffix(".idx"))
    logger.info(f"Save {len(sequences)} sequences to {save_path}")
    logger.info("\n\n\n")


def sample_unique_int(total: int, num_digits: int) -> set[int]:
    zero2nine = list(str(_) for _ in range(10))
    unique_set: set[int] = set()
    for idx in range(100):
        unique_set.add(idx)
    while len(unique_set) < total:
        cur_num_digits = random.randint(1, num_digits)
        cur_digits = [random.choice(zero2nine) for _ in range(cur_num_digits)]
        unique_set.add(int("".join(cur_digits)))
    # Count the statistics of the number of digits.
    digit_counts = collections.defaultdict(lambda: 0)
    for x in unique_set:
        digit_counts[len(str(x))] += 1
    for key in sorted(digit_counts.keys()):
        print(f"The number of digits in {key} is {digit_counts[key]}")
    return unique_set


def pick_random_pairs(
    raw_pairs: list[tuple[int, int]],
    num: int,
    xrange: tuple[int, int],
    save_path: typing.Optional[pathlib.Path] = None,
) -> tuple[list[tuple[int, int]], list[tuple[int, int]]]:
    """Pick random pairs from the given list of pairs."""
    selected_pairs, remaining_pairs = [], []
    for x in raw_pairs:
        if (
            len(selected_pairs) < num
            and xrange[0] <= x[0] <= xrange[1]
            and xrange[0] <= x[1] <= xrange[1]
        ):
            selected_pairs.append(x)
        else:
            remaining_pairs.append(x)
    selected_pairs = list(set(selected_pairs))
    random.shuffle(selected_pairs)
    random.shuffle(remaining_pairs)
    print(
        f"Picked {len(selected_pairs)}/{num} random pairs from {len(raw_pairs)} raw pairs with xrange: {xrange}."
    )
    if save_path is not None:
        torch.save(selected_pairs, save_path)
    return selected_pairs, remaining_pairs


if __name__ == "__main__":
    output_dir = pathlib.Path("/mnt/efs/augment/user/dxy/datasets/xtry.code.11.05")
    output_dir.mkdir(exist_ok=True, parents=True)
    unique_numers = sample_unique_int(3000, 30)
    raw_pairs = set([(i, j) for i in unique_numers for j in unique_numers])
    raw_output_dir = output_dir / "raw_number_pairs"
    raw_output_dir.mkdir(exist_ok=True, parents=True)
    torch.save(raw_pairs, raw_output_dir / "all-pairs.torch")
    torch.save(unique_numers, raw_output_dir / "all-numbers.torch")

    raw_pairs = list(raw_pairs)
    random.shuffle(raw_pairs)
    print(f"Save {len(raw_pairs)} unique raw pairs to {raw_output_dir}")
    torch.save(raw_pairs, raw_output_dir / "all.torch")

    pairs_0_to_1e9_n100k, raw_pairs = pick_random_pairs(
        raw_pairs, 100_000, (0, int(1e9)), raw_output_dir / "pairs_0_to_1e9_n100k.torch"
    )
    pairs_0_to_1e9_n2k, raw_pairs = pick_random_pairs(
        raw_pairs, 2_000, (0, int(1e9)), raw_output_dir / "pairs_0_to_1e9_n2k.torch"
    )
    pairs_0_to_1e18_n100k, raw_pairs = pick_random_pairs(
        raw_pairs,
        100_000,
        (0, int(1e18)),
        raw_output_dir / "pairs_0_to_1e18_n100k.torch",
    )
    pairs_0_to_1e18_n2k, raw_pairs = pick_random_pairs(
        raw_pairs, 2_000, (0, int(1e18)), raw_output_dir / "pairs_0_to_1e18_n2k.torch"
    )
    pairs_1e9_to_1e18_n2k, raw_pairs = pick_random_pairs(
        raw_pairs,
        2_000,
        (int(1e9), int(1e18)),
        raw_output_dir / "pairs_1e9_to_1e18_n2k.torch",
    )
    pairs_0_to_1e27_n100k, raw_pairs = pick_random_pairs(
        raw_pairs,
        100_000,
        (int(0), int(1e27)),
        raw_output_dir / "pairs_0_to_1e27_n100k.torch",
    )
    pairs_1e18_to_1e27_n2k, raw_pairs = pick_random_pairs(
        raw_pairs,
        2_000,
        (int(1e18), int(1e27)),
        raw_output_dir / "pairs_1e18_to_1e27_n2k.torch",
    )

    create_dataset(
        [(pairs_0_to_1e9_n2k, StructureFormatter)],
        repeats=1,
        save_path=output_dir / "structure-valid-0_1e9-r1",
    )
    create_dataset(
        [(pairs_0_to_1e18_n2k, StructureFormatter)],
        repeats=1,
        save_path=output_dir / "structure-valid-0_1e18-r1",
    )
    create_dataset(
        [(pairs_1e9_to_1e18_n2k, StructureFormatter)],
        repeats=1,
        save_path=output_dir / "structure-valid-1e9_to_1e18-r1",
    )
    create_dataset(
        [(pairs_1e18_to_1e27_n2k, StructureFormatter)],
        repeats=1,
        save_path=output_dir / "structure-valid-1e18_to_1e27-r1",
    )

    create_dataset(
        [(pairs_0_to_1e9_n100k, StructureFormatter)],
        repeats=1,
        save_path=output_dir / "structure-train-1e9-r1",
    )
    create_dataset(
        [
            (pairs_0_to_1e9_n100k, StructureFormatter),
            (pairs_0_to_1e27_n100k, ConvertionFormatter),
        ],
        repeats=1,
        save_path=output_dir / "augstruct-train-1e9-r1",
    )
    create_dataset(
        [(pairs_0_to_1e18_n100k, StructureFormatter)],
        repeats=1,
        save_path=output_dir / "structure-train-1e18-r1",
    )
    create_dataset(
        [
            (pairs_0_to_1e18_n100k, StructureFormatter),
            (pairs_0_to_1e27_n100k, ConvertionFormatter),
        ],
        repeats=1,
        save_path=output_dir / "augstruct-train-1e18-r1",
    )

    # Create the Simple Dataset
    create_dataset(
        [(pairs_0_to_1e9_n100k, SimpleFormatter)],
        repeats=5,
        save_path=output_dir / "simple-train-1e9-r5",
    )
    create_dataset(
        [(pairs_0_to_1e9_n2k, SimpleFormatter)],
        repeats=5,
        save_path=output_dir / "simple-valid-0_1e9-r5",
    )
    create_dataset(
        [(pairs_0_to_1e18_n100k, SimpleFormatter)],
        repeats=5,
        save_path=output_dir / "simple-train-1e18-r5",
    )
    create_dataset(
        [(pairs_0_to_1e18_n2k, SimpleFormatter)],
        repeats=5,
        save_path=output_dir / "simple-valid-0_1e18-r5",
    )
