#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/dsv2d-sim-bs2048-s2k-lr5e6to5e7-v2.yml
#
# Global batch size should be 128 * 4 * 16 / 4 = 2048 to speed up experiments.
#
# Take about X hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/83777/logs
#
# It fails with out-of-memory error, we need to fix it!
#
# bash research/utils/download_checkpoint.sh 68323f6f-4713-493e-ba8d-a97b313e6d6b dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-V2-mp4
# PATH: /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-V2-mp4
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#   --i /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-V2-mp4 \
#   --o /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-V2-mp2 \
#   --mp 2
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#   --i /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-LR5e6to5e7-mp4 \
#   --o /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-LR5e6to5e7-mp2 \
#   --mp 2
#
# Convert from FBW to FFW

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/deepseek_coder_v2_lite_dmoe.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 100004
  eot_token_id: 100001
  pad_token_id: 100001
  gradient_accumulation_steps: 16
  batch_size: 4
  max_iters: 2000
  warmup_iters: 100
  lr_decay_iters: 2000
  block_size: 7936  # 7680 + 256
  min_lr: 5.0e-7
  learning_rate: 5.0e-6
  decay_lr: True
  log_interval: 1
  eval_log_interval: 200
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DSCV2LiteBaseDMoE-SIME-BS2048S8K-MIDTRAIN5e6-mp4
  model_parallel_size: 4
  data_sampler_rand_seed: 888  # a different random seed!
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-dscv2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-dscv2/validation_dataset
  model_vocab_size: 102400
  checkpoint_optimizer_state: False

  tokenizer_name: deepseek_coder_v2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: DSCV2LiteBaseDMoE-XSIME-BS1536-S2K-LR5e6to5e7-V2
  wandb_project: dxy-rogue
