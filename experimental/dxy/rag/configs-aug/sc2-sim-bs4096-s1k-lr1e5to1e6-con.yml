#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/sc2-sim-bs4096-s1k-lr1e5to1e6-con.yml
#
# Global batch size should be 128 * 8 * 4 / 2 = 2048 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84368
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh 12039e02-72ac-4159-ae3e-a1dce6d815ff dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6
#
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6-mp1 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6-mp4 \
#     --mp 4
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6-ffw" \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --max-seq-len 7936 \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --model-size "starcoder2-15b" \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 8
  batch_size: 8
  max_iters: 1000
  warmup_iters: 0
  lr_decay_iters: 1000
  block_size: 7936
  min_lr: 2.0e-7
  learning_rate: 2.0e-6
  decay_lr: True
  log_interval: 1
  eval_log_interval: 200
  eval_interval: 200
  # checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  # checkpoint: e9eba4c2-2310-41e0-b32f-f594d3db0a84 # From https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84130/checkpoints
  # checkpoint: 53b85143-87b8-4368-b9d8-b4223cb8ea0c # From https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84190/checkpoints
  # checkpoint: d664d87d-608b-4d5b-bcc9-1ea06b397f27 # From https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84430/checkpoints
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M9000
  model_parallel_size: 2
  data_sampler_rand_seed: 9410 # 9408  # a different random seed!
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-sc2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: True
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False
  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: SC2-15B-XSIME-BS4096-S1K-Resume9000
  wandb_project: dxy-rogue
