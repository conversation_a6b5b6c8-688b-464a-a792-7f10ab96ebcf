#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/dsv2d-sim-bs1536-s9k-lr1e5to1e6.yml
#
# Global batch size should be 128 * 6 * 8 / 4 = 1536 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/
#
# It fails with out-of-memory error, we need to fix it!
#
# bash research/utils/download_checkpoint.sh a12fe0b4-fbf7-4ed5-92dd-d4e1a8cf6ad1 dxy/dscv2-elden/DSCV2LiteBaseDMoE-SIME-BS1536S9K-LR1e5to1e6
# PATH: /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp4
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#   --i /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp4 \
#   --o /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp1 \
#   --mp 1
#
# Convert from FBW to FFW

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/deepseek_coder_v2_lite_dmoe.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 100004
  eot_token_id: 100001
  pad_token_id: 100001
  gradient_accumulation_steps: 8
  batch_size: 6
  max_iters: 9000
  warmup_iters: 200
  lr_decay_iters: 9000
  block_size: 7936  # 7680 + 256
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 300
  eval_interval: 300
  checkpoint: /mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Base-FBW-dense-mp4
  model_parallel_size: 4
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-dscv2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-dscv2/validation_dataset
  model_vocab_size: 102400
  checkpoint_optimizer_state: False

  tokenizer_name: deepseek_coder_v2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: DSCV2LiteBaseDMoE-XSIME-BS1536-S9K-LR1e5to1e6
  wandb_project: dxy-rogue
