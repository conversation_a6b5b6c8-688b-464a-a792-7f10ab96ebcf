#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/dsv2d-sim-bs2048-s8k-midtrain.yml
#
# Global batch size should be 256 * 8 * 4 / 4 = 2048 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/82767
#
# It fails with out-of-memory error, we need to fix it!
#
# bash research/utils/download_checkpoint.sh a12fe0b4-fbf7-4ed5-92dd-d4e1a8cf6ad1 dxy/dscv2-elden/DSCV2LiteBaseDMoE-SIME-BS1536S9K-LR1e5to1e6
# PATH: /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp4
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#   --i /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp4 \
#   --o /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp1 \
#   --mp 1
#
# Convert from FBW to FFW

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 256

fastbackward_configs:
 - configs/deepseek_coder_v2_lite_dmoe.py

fastbackward_args:
  loss_mask_policy: fim_all_tokens
  fim_middle_token_id: 100004
  eot_token_id: 100001
  pad_token_id: 100001
  gradient_accumulation_steps: 8
  batch_size: 4
  max_iters: 8000
  warmup_iters: 100
  lr_decay_iters: 8000
  block_size: 7936  # 7680 + 256
  min_lr: 5.0e-6
  learning_rate: 5.0e-6
  decay_lr: False
  log_interval: 1
  eval_log_interval: 300
  eval_interval: 300
  checkpoint: /mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Base-FBW-dense-mp4
  model_parallel_size: 4
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-dscv2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-dscv2/validation_dataset
  model_vocab_size: 102400
  checkpoint_optimizer_state: False

  tokenizer_name: deepseek_coder_v2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: DSCV2LiteBaseDMoE-XSIME-BS2048-S8K-LR5e6MIDTRAIN
  wandb_project: dxy-rogue
