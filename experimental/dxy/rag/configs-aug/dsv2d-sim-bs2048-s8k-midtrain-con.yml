#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/dsv2d-sim-bs2048-s8k-midtrain-con.yml
#
# Global batch size should be 128 * 16 * 4 / 4 = 2048 to speed up experiments.
#
# Link:
# - https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/82947
# - https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/83436
#
# It fails with out-of-memory error, we need to fix it!
#
# bash research/utils/download_checkpoint.sh dc49763d-8571-4813-9ad3-aaaeacfc5672 dxy/dscv2-elden/DSCV2LiteBaseDMoE-SIME-BS2048S8K-MIDTRAIN5e6-mp4
#
# PATH: /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp4
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#   --i /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp4 \
#   --o /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp1 \
#   --mp 1
#
# Convert from FBW to FFW

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/deepseek_coder_v2_lite_dmoe.py

fastbackward_args:
  loss_mask_policy: fim_all_tokens
  fim_middle_token_id: 100004
  eot_token_id: 100001
  pad_token_id: 100001
  gradient_accumulation_steps: 16
  batch_size: 4
  max_iters: 8000
  warmup_iters: 100
  lr_decay_iters: 8000
  block_size: 7936  # 7680 + 256
  min_lr: 5.0e-6
  learning_rate: 5.0e-6
  decay_lr: False
  log_interval: 1
  eval_log_interval: 300
  eval_interval: 300
  model_parallel_size: 4
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-dscv2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-dscv2/validation_dataset
  model_vocab_size: 102400
  checkpoint_optimizer_state: False
  # This checkpoint just trained for 300 iters, and we will resume training from this checkpoint.
  # checkpoint: 2e66f282-2f67-454d-90d0-0141137c8744 # latest checkpoint id from https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/82899/checkpoints
  checkpoint: 2c0da4f0-982e-4143-bbc9-a9ddc736d9ee  # latest checkpoint id from https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/82947/checkpoints
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: True

  tokenizer_name: deepseek_coder_v2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: DSCV2LiteBaseDMoE-XSIME-BS2048-S8K-LR5e6MIDTRAIN-Resume2700
  wandb_project: dxy-rogue
