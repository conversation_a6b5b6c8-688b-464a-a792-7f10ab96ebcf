#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/sc2-sim-bs64-s200-rlhf.yml -s research/fastbackward/train_preference_token.py
#
# Global batch size should be 64 * 2 * 1 / 2 = 64 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/82771
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh 189cf596-8f04-483f-9dba-c7d842447dcc dxy/sc2-simple-elden/REWARD-SC2-15B-SIM-V0
# bash research/utils/download_checkpoint.sh fd9cfdeb-599d-4652-902d-6713c5875104 dxy/sc2-simple-elden/REWARD-SC2-15B-SIM-V1
#

# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/REWARD-SC2-15B-SIM-V1 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/REWARD-SC2-15B-SIM-V1-mp1 \
#     --mp 1
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6-ffw" \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --max-seq-len 7936 \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --model-size "starcoder2-15b" \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  # loss_mask_policy: fim
  loss_mask_policy: reward
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 1
  batch_size: 2
  max_iters: 1000
  warmup_iters: 0
  lr_decay_iters: 1000
  block_size: 7940  # 7936
  min_lr: 1.0e-5
  learning_rate: 1.0e-5
  decay_lr: False
  log_interval: 1
  eval_log_interval: 100
  eval_interval: 50
  # checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  # checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M5400
  checkpoint: b73a0648-06a7-4e8d-bd1f-1b92ee507aad # https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84430/checkpoints
  model_parallel_size: 2
  train_data_path: /mnt/efs/augment/user/dxy/hindsight/offline-rl-240904/train
  eval_data_path: /mnt/efs/augment/user/dxy/hindsight/offline-rl-240904/valid
  model_vocab_size: 49408  # 49176
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  # run_name: SC2-15B-XSIME-BS256-S200-RLHF-V0
  run_name: SC2-15B-XSIME-BS64-S1000-REWARD-LR1e-5-V1
  wandb_project: dxy-rlhf
