"""Test a StarCoder2 model.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/rag/profile/test-sc2-model.py \
    --ckp_dir /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M9000-mp1

python experimental/dxy/rag/profile/test-sc2-model.py \
    --ckp_dir /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M9000-mp2 \
    --use_inference

StarCoder 2 model:
- 40 * 8000 * 4 * 128 * 2 = 327.68M (KV cache)
- logits = 8000 * 49176 * 4 = 1.5 GB
"""

import argparse
import json
import pathlib
import time

import torch

from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from research.eval.harness.systems.abs_system import CompletionResult
from research.fastbackward import inference
from research.fastbackward.model import Model<PERSON>rgs


def get_model(model_args: ModelArgs, use_inference: bool, ckp_dir: pathlib.Path):
    if use_inference:
        start_time = time.time()
        num_gpus = torch.cuda.device_count()
        print(f"Detected {num_gpus} GPUs.")
        model_runner = inference.ParallelTransformerRunner(num_gpus)
        model_runner.load_model(model_args, ckp_dir)
        print(f"Finish loading the model in {time.time() - start_time}s.")
        return model_runner
    else:
        master_port = inference.find_available_local_port()
        print(f"We find the master port: {master_port}")
        inference._parallel_init(0, 1, "127.0.0.1", master_port)
        print("Start to create the model.")
        model = inference.Transformer(model_args).bfloat16()
        start_time = time.time()
        state_dict = torch.load(ckp_dir / "consolidated.00.pth", map_location="cpu")
        print(f"Finish loading the model in {time.time() - start_time}s.")
        model.load_state_dict(state_dict, strict=True)
        model.to(device=torch.device("cuda"), dtype=torch.bfloat16)
        return model


def parse_json_file(path: pathlib.Path):
    with path.open("r", encoding="utf-8") as f:
        xdata = json.load(f)
        output_argmax = CompletionResult.schema().load(xdata["output_argmax"])
        return {
            "prompt_tokens": output_argmax.prompt_tokens,
            "generated_tokens": output_argmax.generated_tokens,
        }


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--use_inference",
        action="store_true",
        default=False,
        help="Whether to use the inference runner",
    )
    parser.add_argument(
        "--ckp_dir",
        type=pathlib.Path,
        default=None,
        help="The checkpoint directory",
    )
    args = parser.parse_args()
    param_path = args.ckp_dir / "params.json"
    with param_path.open("r") as f:
        params = json.loads(f.read())
    params["max_generation_batch_size"] = 1
    params["use_sequence_parallel"] = False
    print(f"params:\n{params}")
    model_args = ModelArgs.schema().load(params)
    model_args.max_seq_len = 8192
    print(f"The model args:\n{model_args}")

    xdata = parse_json_file(
        pathlib.Path(
            "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto/token-data-w-skip-pause/0283-0393.json"
        )
    )
    prompt_tokens = xdata["prompt_tokens"]
    print(f"The prompt tokens: {prompt_tokens}")
    model_runner = get_model(
        model_args, use_inference=args.use_inference, ckp_dir=args.ckp_dir
    )

    cached_generated_tokens = xdata["generated_tokens"]
    # This GPU memory usage is incorrect......
    print(f"GPU Memory Usage: {torch.cuda.memory_allocated() / 1e9} GB")

    tokens_th = torch.full((1, 10000), 0, dtype=torch.long, device="cuda")
    tokens_th[0, : len(prompt_tokens)] = torch.tensor(prompt_tokens, dtype=torch.long, device="cuda")  # fmt: off
    prev_pos = 0
    generated_tokens = []
    for cur_pos in range(len(prompt_tokens), len(prompt_tokens) + 32):
        logits = model_runner.generate(tokens_th[:, prev_pos:cur_pos], prev_pos)
        next_token = torch.argmax(logits[:, -1], dim=-1)
        next_token = next_token.reshape(-1)
        tokens_th[:, cur_pos] = next_token
        prev_pos = cur_pos
        generated_tokens.append(next_token.item())
    print(f"The generated tokens: {generated_tokens}")

    tokenizer = StarCoder2Tokenizer()
    cached_output_str = tokenizer.detokenize(cached_generated_tokens)
    output_str = tokenizer.detokenize(generated_tokens)
    print(f"The cached output string:\n{cached_output_str}")
    print("-" * 128)
    print(f"The output string:\n{output_str}")
    if isinstance(model_runner, inference.ParallelTransformerRunner):
        model_runner.unload_model()


if __name__ == "__main__":
    main()
