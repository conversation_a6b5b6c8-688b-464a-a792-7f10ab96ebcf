{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "print(torch.cuda.device_count())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.cceval import CCEvalOutput\n", "\n", "good_run = \"/mnt/efs/augment/eval/jobs/KtYvJmgN/000_fastbackward_CCEval_completed_patches.jsonl.zst\"\n", "bad_run = \"/mnt/efs/augment/eval/jobs/Cyd9GAqj/000_fastbackward_CCEval_completed_patches.jsonl.zst\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["good_data_lst = [CCEvalOutput(**x) for x in read_jsonl_zst(good_run)]\n", "bad_data_lst = [CCEvalOutput(**x) for x in read_jsonl_zst(bad_run)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indexes = []\n", "for index, (good_data, bad_data) in enumerate(zip(good_data_lst, bad_data_lst)):\n", "    assert good_data.prefix == bad_data.prefix\n", "    assert good_data.suffix == bad_data.suffix\n", "    assert good_data.ground_truth == bad_data.ground_truth\n", "    if good_data.generation != bad_data.generation:\n", "        indexes.append(index)\n", "print(\n", "    f\"There are {len(indexes)}/{len(good_data_lst)} examples with different generations.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = indexes[8]\n", "print(f\"{index=}\")\n", "good_data, bad_data = good_data_lst[index], bad_data_lst[index]\n", "assert good_data.prompt == bad_data.prompt\n", "print(\"-\" * 100)\n", "print(good_data.ground_truth)\n", "print(\"-\" * 100)\n", "print(good_data.generation)\n", "print(\"-\" * 100)\n", "print(bad_data.generation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "from experimental.dxy.rag.rlhf.shared_lib import create_system\n", "\n", "system = create_system()\n", "system.load()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from research.models.meta_model import GenerationOptions\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "# print(tokenizer.detokenize(good_data_lst[index].generation))\n", "# print(\"-\" * 100)\n", "# print(tokenizer.detokenize(bad_data_lst[index].generation))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(system.generation_options)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["prompt_tokens = tokenizer.tokenize(good_data.prompt)\n", "raw_generated_output = system.model.raw_generate_tokens(\n", "    prompt_tokens, system.generation_options\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(raw_generated_output.tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.special_tokens.pause)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(system.config.fim_gen_mode)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from research.eval.harness.systems.libraries.completion_fim_handling import (\n", "#     fim_postprocess_generation,\n", "# )\n", "# import copy\n", "\n", "# special_tokens = system.tokenizer.special_tokens\n", "\n", "# processed_tokens, _ = fim_postprocess_generation(\n", "#     generated_tokens=copy.deepcopy(raw_generated_output.tokens),\n", "#     eod_id=special_tokens.eos,\n", "#     skip_id=special_tokens.skip,\n", "#     pause_id=special_tokens.pause,\n", "#     fim_gen_mode=system.config.fim_gen_mode,\n", "# )\n", "# print(processed_tokens)\n", "# all_tokens = prompt_tokens + processed_tokens\n", "# final_text = tokenizer.detokenize(all_tokens)\n", "# prompt_text = tokenizer.detokenize(prompt_tokens)\n", "# generated_text = final_text[len(prompt_text) :]\n", "# print(generated_text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}