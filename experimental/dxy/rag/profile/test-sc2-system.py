"""Test a StarCoder2 model.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/rag/profile/test-sc2-system.py

StarCoder 2 model:
- 40 * 8000 * 4 * 128 * 2 = 327.68M (KV cache)
- logits = 8000 * 49176 * 4 = 1.5 GB
"""

# fmt: off
import json
import pathlib

from base.datasets.hindsight_completion_dataset import HindsightCompletionDatum
from experimental.dxy.rag.rlhf.local_content_manager import LocalContentManager
from experimental.dxy.rag.rlhf.shared_lib import create_system
from research.core.model_input import ModelInput
from research.eval.harness.systems.abs_system import CompletionResult


def parse_json_file(path: pathlib.Path):
    with path.open("r", encoding="utf-8") as f:
        xdata = json.load(f)
        datum: HindsightCompletionDatum = HindsightCompletionDatum.schema().load(
            xdata["datum"]
        )
        output_argmax = CompletionResult.schema().load(xdata["output_argmax"])
        return {
            "datum": datum,
            "blob_names": xdata["blob_names"],
            "prompt_tokens": output_argmax.prompt_tokens,
            "generated_tokens": output_argmax.generated_tokens,
        }


def parse_json_file_to_model_input(path: pathlib.Path):
    xdata = parse_json_file(path)
    datum = xdata["datum"]
    blob_names = xdata["blob_names"]
    generated_tokens = xdata["generated_tokens"]
    return ModelInput(
        prefix=datum.completion.request.prefix,
        suffix=datum.completion.request.suffix,
        path=datum.completion.request.path,
        target=None,
        doc_ids=blob_names,
        recency_info=None,
    ), generated_tokens


def main():
    input_folder = pathlib.Path(
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto"
    )
    blob_manager = LocalContentManager()
    blob_manager.load(input_folder / "local-blobs")
    # This is a system with a SC2 model + 2 dense retrievers (all are FBW models)
    system = create_system()
    # After the load, it takes 24370 MiB on GPU0 and 16539 MiB on GPU1
    system.load()
    # After the processing 917 + 2422 batch of chunks for the embedding model,
    # The GPU memory usage is 35302 MiB and 16539 MiB
    system.add_docs(blob_manager.to_documents())
    # After this generate, it takes 55212 MiB on GPU0 and 34976 MiB on GPU1.
    model_input_1, oo_1 = parse_json_file_to_model_input(input_folder / "token-data-w-skip-pause/0283-0393.json")
    outputs_1 = system.generate(model_input_1)
    outputs_1.generated_text
    # After this generate, it takes 67210 MiB on GPU0 and 45530 MiB on GPU1.
    model_input_2, oo_2 = parse_json_file_to_model_input(input_folder / "token-data-w-skip-pause/0291-0393.json")
    outputs_2 = system.generate(model_input_2)
    import pdb
    pdb.set_trace()
    del outputs_1, outputs_2, oo_1, oo_2


if __name__ == "__main__":
    main()
