# (RLDB): reinforcement learning from developer behaviors.
#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/reward-db-v4-bs1024e2.yml -s research/fastbackward/rlhf/train_sft.py -c GCP-US1
# Global batch size should be 128 * 2 * 8 / 2 = 1024 to speed up experiments.
#
# Take about 12 hours to complete
# E1: https://determined.cw-east4.r.augmentcode.com/det/experiments/87/trials/87/logs
# E2: https://determined.cw-east4.r.augmentcode.com/det/experiments/86/trials/86/logs
#
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh dxy/sc2-elden/Reward-05to10v2-BS512E3-LR5E6to5E7-mp2 gcp
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-0801to1024-BS512E3-LR5E6to5E7-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-0801to1024-BS512E3-LR5E6to5E7-mp4 \
#     --mp 4
#
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp1 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-ffw \
#     --model-name="starcoder2-15b"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  gradient_accumulation_steps: 8
  batch_size: 2
  block_size: 7936

  max_iters: 0
  max_epochs: 2
  warmup_iters: 0
  lr_decay_iters: 0  # so that it will be the same as max_iters

  min_lr: 5.0e-7
  learning_rate: 5.0e-6
  decay_lr: True
  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-20241117
  run_name: REWARD-DB-SC2-FV4-0801to1117-BS1024E2-LR5E6to5E7
  sft_collate_strategy: user_behavior

  log_interval: 1
  eval_log_interval: 100
  eval_interval: 50
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2
  model_parallel_size: 2
  eval_data_path: Nov18W1_L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122-L0;Nov18W1_L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122-L1;Nov18W1_L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122-L2;Nov18W1_L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122-L3;Nov18W1_L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122-L4;Nov18W1_L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122-L5
  model_vocab_size: 49280  # 49176
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-reward
