#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-aug/llama31-sim-bs1024-s16k-lr5e6.yml
#
# Global batch size should be 64 * 1 * 8 / 8 = 64 to speed up experiments.
# In total, the tokens that model seen will be 1K * 16K * 16K = 256B
#
# Memory Usage:
# - model weights and gradients per GPU: 70B * 4 = 280 GB in total, if mp=8, then 280B / 8 = 35 GB, if mp=16, then 280B / 16 = 17.5 GB.
# - optimizer states: 70B * 12 = 280 GB, 128 GPUs are used, then 280B / 128 = 2.2 GB
# - activation: 8192 * 16384 * 80 = 10.7 GB -> if batch size is 2 then 21 GB, if sequence parallel is used, then 21 GB / 8 = 2.6 GB.
#
# Take about xx hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/83553
# bash research/utils/download_checkpoint.sh a0e15b54-87d9-4afc-b7a0-41d18334f534 dxy/llama31-simple-elden/LLAMA31-70B-SIM-BS1024S16-M3500
#

determined:
  description: null
  workspace: Dev
  project: Xuanyi-<PERSON>
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/llama31_70b.py

fastbackward_args:
  # loss_mask_policy: fim_all_tokens
  # fim_middle_token_id: 128014
  # eot_token_id: 128001
  # pad_token_id: 128001
  gradient_accumulation_steps: 8
  batch_size: 1
  max_iters: 16000
  warmup_iters: 100
  lr_decay_iters: 16000
  block_size: 16384
  min_lr: 5.0e-6
  learning_rate: 5.0e-6
  decay_lr: True
  log_interval: 1
  eval_log_interval: 500
  eval_interval: 500
  checkpoint: /mnt/efs/augment/checkpoints/llama3.1/fbw/Meta-Llama-3.1-70B-mp8
  model_parallel_size: 8
  # checkpoint: /mnt/efs/augment/checkpoints/llama3.1/fbw/Meta-Llama-3.1-70B-mp16
  # model_parallel_size: 16
  use_sequence_parallel: True
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/validation_dataset
  model_vocab_size: 128256
  checkpoint_optimizer_state: False

  tokenizer_name: llama3_base
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: LLAMA31-70B-SIM-BS1024-S16K-LR5e6
  wandb_project: dxy-rogue
