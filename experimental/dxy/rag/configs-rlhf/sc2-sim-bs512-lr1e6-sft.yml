#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/sc2-sim-bs512-lr1e6-sft.yml
#
# Global batch size should be 64 * 4 * 4 / 2 = 512 to speed up experiments.
#
# Take about 180 hours to complete on 128 GPUs.
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84657
#
#
# bash research/utils/download_checkpoint.sh 1534b921-1242-48dd-a71a-ac5357d595cb dxy/sc2-simple-elden/SC2-15B-SIM-FMV5-SFTVendor-mp2
# bash research/utils/download_checkpoint.sh ec67b130-903c-4c91-84d7-f92625051b45 dxy/sc2-simple-elden/SC2-15B-SIM-FMV5-SFTDogFood-mp2
# bash research/utils/download_checkpoint.sh 624bbb43-f2e0-43ae-ad19-bdef567fdc34 dxy/sc2-simple-elden/SC2-15B-SIM-FMV5-SFTDogFoodS200-mp2
# bash research/utils/download_checkpoint.sh c33d76fb-d547-413a-ac1b-2f4f0add16fb dxy/sc2-simple-elden/SC2-15B-SIM-FMV5-DPO-0.1-6.0-mp2
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp1 \
#     --mp 1
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-FMV5-DPO-0.1-6.0-mp2 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-FMV5-DPO-0.1-6.0-ffw \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --calibration-steps 400 \
#     --log-to-stdout \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0" \
#     -s a2254fc7be41bc966435e4533d9b3520a982742b10d8c3ae052274ab67c5028f \
#     -d /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0913_v2 /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0913_v2

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 8
  batch_size: 4
  max_iters: 200
  warmup_iters: 0
  lr_decay_iters: 200
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-6
  decay_lr: False
  log_interval: 1
  eval_log_interval: 2
  eval_interval: 2
  # This is a super great checkpoint from https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84837/overview
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp2
  # checkpoint: /mnt/efs/augment/checkpoints/star2/elden_0619_90k_0619_sc2_pfretsignpfsuf_rdrop030_1k5b6kstep
  model_parallel_size: 2
  data_sampler_rand_seed: 9408  # Random Seed
  # train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden_v2.0_6k-sc2/dataset
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_vendor_simple_elden_s8k
  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_vendor_simple_elden_s8k;/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_dogfood_simple_elden_s8k
  # eval_data_path: 0814@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden_v2.0_6k-sc2/validation_dataset;cceval@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0915_v2_s6k;hs06@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0915_v2_s6k
  eval_data_path: cceval@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0915_v2_s6k;hs06@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0915_v2_s6k
  model_vocab_size: 49176
  checkpoint_optimizer_state: True
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False
  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: SC2-15B-SIM-S6K-BS1024-FMV5-LR1e6-SFTDogFood
  wandb_project: dxy-rag-v2
