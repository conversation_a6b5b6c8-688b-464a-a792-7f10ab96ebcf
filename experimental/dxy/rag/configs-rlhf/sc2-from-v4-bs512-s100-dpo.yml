#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/sc2-from-v4-bs512-s100-dpo.yml -s research/fastbackward/train_dpo.py -c CW
#
# Global batch size should be 64 * 2 * 8 / 2 = 512 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84861
#
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh 06e66075-fe70-4019-b1a9-9a93f08e4de2 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HD
# bash research/utils/download_checkpoint.sh c85330de-8ace-4f98-a11a-5ba9bf034188 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-mp2
# bash research/utils/download_checkpoint.sh 78df94dd-765b-4bcd-bcad-d6ecf31da345 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp2

# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp4 \
#     --mp 4

# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-ffw \
#     --model-name="starcoder2-15b"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  gradient_accumulation_steps: 16
  batch_size: 2
  max_iters: 100
  warmup_iters: 0
  lr_decay_iters: 100
  block_size: 7936  # 7936

  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True

  rl_dpo_ce_loss_coeff: 6.0
  rl_dpo_beta: 0.1
  rlhf_checkpoint_interval: 50
  run_name: DPO-SC2-15B-FromV4-BS512-S100-LR1E5-0.1-6.0

  log_interval: 1
  eval_interval: 1
  eval_log_interval: 50
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp4
  rl_ref_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp4
  model_parallel_size: 4
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data/dpo-train
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data/dpo-valid
  rl_llm_eval_data_path: hs09_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/hindsight09_elden_v4_s6k;cceval_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/cceval_elden_v4_s6k
  model_vocab_size: 49176
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-rlhf
