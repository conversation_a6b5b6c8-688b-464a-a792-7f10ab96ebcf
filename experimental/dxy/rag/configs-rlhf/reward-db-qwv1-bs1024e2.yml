# (RLDB): reinforcement learning from developer behaviors.
#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/reward-db-qwv1-bs1024e2.yml -s research/fastbackward/rlhf/train_sft.py -c GCP-US1
# Global batch size should be 128 * 2 * 8 / 2 = 1024 to speed up experiments.
#
# Take about 12 hours to complete
# E2: https://determined.cw-east4.r.augmentcode.com/det/experiments/97
# E3: https://determined.cw-east4.r.augmentcode.com/det/experiments/95
# E4: https://determined.cw-east4.r.augmentcode.com/det/experiments/96
# E2 (Dropout Attn 0.2): https://determined.cw-east4.r.augmentcode.com/det/experiments/99
# E4 (Dropout Attn 0.2): https://determined.cw-east4.r.augmentcode.com/det/experiments/104
# E2-XL0to6: https://determined.cw-east4.r.augmentcode.com/det/experiments/105
# E1-XL0to6 (0801to1130): https://determined.cw-east4.r.augmentcode.com/det/experiments/112
# E2-XL0to6 (0801to1130, Dropout Attn 0.1): https://determined.cw-east4.r.augmentcode.com/det/experiments/113
# REWARD-DB-QWENV1-0801to1207-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/146/trials/146/checkpoints
# REWARD-DB-QWENV1-XX0801to1130-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/157
# REWARD-DB-QWENV1-XX0801to1207-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/155
# REWARD-DB-QWENV1-XX0801to1229-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/202
# REWARD-DB-QWENV1-XX0801to1231-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/206
# REWARD-DB-QWENV1-0801to1231-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/208
# REWARD-DB-QWENV1-240801-250119-BS1024E2-LR5E6to5E7-DA0.1:
#   - https://determined.cw-east4.r.augmentcode.com/det/experiments/253
#
#
# Download Checkpoints
#
# bash research/utils/download_checkpoint.sh 02874616-c3cc-46fc-a270-a5aaf51f0135 dxy/qwen-elden/Reward-0801to1126-BS1024E2-LR5E6to5E7-mp2 cw-east4
# bash research/utils/download_checkpoint.sh dd43f4fa-4041-42cb-816a-cfc1db462348 dxy/qwen-elden/Reward-0801to1207-BS1024E2-LR5E6to5E7-DA0.1-mp2 cw-east4
# bash research/utils/download_checkpoint.sh 9a7bd5dc-a48b-446c-9c64-e5a02f6c1941 dxy/qwen-elden/Reward-XX0801to1207-BS1024E2-LR5E6to5E7-DA0.1-mp2 cw-east4
# bash research/utils/download_checkpoint.sh f4d539fb-6127-42e9-b00f-5a8569eb1636 dxy/qwen-elden/Reward-XX0801to1229-BS1024E2-LR5E6to5E7-DA0.1-mp2 cw-east4
# bash research/utils/download_checkpoint.sh dc52f87b-cac2-4a75-afcb-db6c744264c3 dxy/qwen-elden/Reward-XX0801to1231-BS1024E2-LR5E6to5E7-DA0.1-mp2 cw-east4
# bash research/utils/download_checkpoint.sh 7093634f-d9b4-4aa1-ac99-20b8e9581c89 dxy/qwen-elden/Reward-0801to1231-BS1024E2-LR5E6to5E7-DA0.1-mp2 cw-east4
# bash research/utils/download_checkpoint.sh 4ea3d0e2-742f-4ec3-9d0e-9569d1a8c24c dxy/qwen-elden/Reward-240801-250119-BS1024E2-LR5E6to5E7-DA0.1-mp2 cw-east4
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-XX0801to1231-BS1024E2-LR5E6to5E7-DA0.1-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-XX0801to1231-BS1024E2-LR5E6to5E7-DA0.1-mp4 \
#     --mp 4
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-240801-250119-BS1024E2-LR5E6to5E7-DA0.1-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-240801-250119-BS1024E2-LR5E6to5E7-DA0.1-mp4 \
#     --mp 4
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/qwen25coder_14b.py

fastbackward_args:
  gradient_accumulation_steps: 8
  batch_size: 2
  block_size: 7936

  max_iters: 0
  max_epochs: 2
  warmup_iters: 0
  lr_decay_iters: 0  # so that it will be the same as max_iters

  min_lr: 5.0e-7
  learning_rate: 5.0e-6
  decay_lr: True
  sft_dropout_attn: 0.1
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1126-QWEN
  # run_name: REWARD-DB-QWENV1-0801to1126-BS1024E2-LR5E6to5E7-DA0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1126-QWEN;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train
  # run_name: REWARD-DB-QWENV1-XX0801to1126-BS1024E2-LR5E6to5E7-DA0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1130-QWEN;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train
  # run_name: REWARD-DB-QWENV1-XX0801to1130-BS1024E2-LR5E6to5E7-DA0.1
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1207-QWEN
  # run_name: REWARD-DB-QWENV1-0801to1207-BS1024E2-LR5E6to5E7-DA0.1
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1207-QWEN;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train
  # run_name: REWARD-DB-QWENV1-XX0801to1207-BS1024E2-LR5E6to5E7-DA0.1
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train
  # run_name: REWARD-DB-QWENV1-XX0801to1231-BS1024E2-LR5E6to5E7-DA0.1
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN
  # run_name: REWARD-DB-QWENV1-0801to1231-BS1024E2-LR5E6to5E7-DA0.1
  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN
  run_name: REWARD-DB-QWENV1-240801-250119-BS1024E2-LR5E6to5E7-DA0.1
  sft_collate_strategy: user_behavior

  log_interval: 1
  eval_log_interval: 100
  eval_interval: 50
  checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/14b_elden_smart_fb_mp2
  model_parallel_size: 2
  # eval_data_path: Nov27_28L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L0;Nov27_28L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L1;Nov27_28L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L2;Nov27_28L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L3;Nov27_28L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L4;Nov27_28L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L5;Nov27_28skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-skip
  # eval_data_path: Nov27_28L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L0;Nov27_28L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L1;Nov27_28L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L2;Nov27_28L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L3;Nov27_28L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L4;Nov27_28L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-L5;Nov27_28skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-skip;ccL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-valid;sftL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-valid
  # eval_data_path: Dec08_09L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L0;Dec08_09L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L1;Dec08_09L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L2;Dec08_09L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L3;Dec08_09L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L4;Dec08_09L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L5;Dec08_09skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-skip;ccL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-valid;sftL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-valid
  # eval_data_path: Dec10_11L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L0;Dec10_11L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L1;Dec10_11L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L2;Dec10_11L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L3;Dec10_11L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L4;Dec10_11L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L5;Dec10_11skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-skip;ccL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-valid;sftL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-valid
  eval_data_path: Dec08_09L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L0;Dec08_09L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L1;Dec08_09L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L2;Dec08_09L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L3;Dec08_09L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L4;Dec08_09L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-L5;Dec08_09skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN-skip;Dec10_11L0@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L0;Dec10_11L1@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L1;Dec10_11L2@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L2;Dec10_11L3@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L3;Dec10_11L4@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L4;Dec10_11L5@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-L5;Dec10_11skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN-skip;ccL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-valid;sftL6@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-valid
  model_vocab_size: 152064
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: qwen25coder
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-reward
