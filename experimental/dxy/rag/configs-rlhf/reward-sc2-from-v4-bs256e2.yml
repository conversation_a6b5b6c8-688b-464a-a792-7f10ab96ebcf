#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/reward-sc2-from-v4-bs256e2.yml -s research/fastbackward/rlhf/train_sft.py -c CW
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/reward-sc2-from-v4-bs256e2.yml -s research/fastbackward/rlhf/train_sft.py -c GCP-US1
# Global batch size should be 64 * 2 * 4 / 2 = 256 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86139
#
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh 06e66075-fe70-4019-b1a9-9a93f08e4de2 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HD
# bash research/utils/download_checkpoint.sh c85330de-8ace-4f98-a11a-5ba9bf034188 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-mp2
# bash research/utils/download_checkpoint.sh 78df94dd-765b-4bcd-bcad-d6ecf31da345 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp2

# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp1 \
#     --mp 1

# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp1 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-ffw \
#     --model-name="starcoder2-15b"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  # loss_mask_policy: fim
  # loss_mask_policy: reward
  # fim_middle_token_id: 2
  # eot_token_id: 0
  # pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 2
  block_size: 7936

  max_iters: 0
  max_epochs: 2
  warmup_iters: 0
  lr_decay_iters: 0  # so that it will be the same as max_iters
  # min_lr: 1.0e-6
  # learning_rate: 1.0e-5
  # decay_lr: True
  # run_name: REWARD-SC2-15B-FromV4-BS256E2-LR1E5to1E6
  # sft_collate_strategy: reward_by_is_positive
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  run_name: REWARD-SC2-15B-FV4-05to10v1-BS256E2-LR1E5to1E6
  sft_collate_strategy: reward_by_is_positive

  log_interval: 1
  eval_log_interval: 100
  eval_interval: 50
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2
  model_parallel_size: 2
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2;/mnt/efs/augment/user/dxy/datasets/rlhf/0915-reject-v2/
  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202410-v1
  eval_data_path: 2024Sep2Oct@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202409-202410;2024Oct2Nov@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202410-202411-v2
  rl_llm_eval_data_path: hs09_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/hindsight09_elden_v4_s6k;cceval_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/cceval_elden_v4_s6k
  model_vocab_size: 49280  # 49176
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-rlhf
