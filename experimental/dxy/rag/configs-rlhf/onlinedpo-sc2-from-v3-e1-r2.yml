#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/onlinedpo-sc2-from-v3-e1-r2.yml -s research/fastbackward/rlhf/train_online_rl.py -c GCP-US1
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/onlinedpo-sc2-from-v3-e1-r2.yml -s research/fastbackward/rlhf/train_online_rl.py -c CW
# Global batch size should be 128 * 2 * 1 * (16 * 2) / 4 = 512 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84861
#
# From https://determined.gcp-us1.r.augmentcode.com/det/experiments/371/trials/371/checkpoints?sortDesc=false&sortKey=SORT_BY_STATE
# bash research/utils/download_checkpoint.sh a9feccd6-8058-47f3-8e5b-ecc06d477934 dxy/sc2-elden-rlhf/IPO-OfflineS0-FV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S70-mp4 gcp
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp4 \
#     --mp 4

# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-elden-rlhf/IPO-OfflineS0-FV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S70-mp4 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-elden-rlhf/IPO-OfflineS0-FV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S70-ffw \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --calibration-steps 600 \
#     --log-to-stdout \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden-rlhf/IPO-OfflineS0-FV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S70-ffw" \
#     --ckpt-sha256 "52bb07e5f2683060322b70b3cd95766809b3181ec38d9003ba9df0905270a78a" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden-rlhf/IPO-OfflineS0-FV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S70-ffw-fp8" \
#     --calibration-data "/mnt/efs/augment/data/processed/rag/dataset/elden_0619_90k_0619_sc2_pfretsignpfsuf_rdrop030/dataset"
#

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  gradient_accumulation_steps: 4
  batch_size: 1

  max_epochs: 1
  max_iters: 0
  warmup_iters: 50
  lr_decay_iters: 0
  block_size: 7936  # 7936

  min_lr: 2.0e-7
  learning_rate: 2.0e-6
  decay_lr: True

  # # Parameters following https://arxiv.org/pdf/2407.21783
  # rl_dpo_ce_loss_coeff: 0.2
  # rl_dpop_lambda: 0.0
  # dpo_use_ipo: False
  # rl_dpo_beta: 0.1
  # dpo_label_smoothing: 0.0
  # dpo_max_generation_steps: 96
  # dpo_num_pairs_per_prompt: 64
  # dpo_online_rl: False
  # rl_sync_ref_weights_per_iter: 30
  # run_name: DPO-OfflineS30-FV3-B64P64E1-W50LR2E6to2E7-beta0.1-ce0.2-smooth0.0

  rl_dpo_ce_loss_coeff: 0.0
  rl_dpop_lambda: 0.0
  rl_use_ipo: True
  rl_dpo_beta: 0.1
  rl_label_smoothing: 0.0
  rl_max_generation_steps: 96
  rl_num_pairs_per_prompt: 64
  rl_online_rl: False
  rl_sync_ref_weights_per_iter: 0
  run_name: IPO-OfflineS0-DATAto241001-V3RV3-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0

  log_interval: 1
  eval_interval: 2
  rl_ckp_save_interval: 10
  eval_log_interval: 100
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp4
  rl_ref_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp4
  # checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp4
  # rl_ref_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp4
  # rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-05to10v3-BS512E3-LR5E6to5E7-S850-mp4
  # rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-2024AUGto1017Excl1014-BS512E3-LR5E6to5E7-mp4
  rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-0801to1024-BS512E3-LR5E6to5E7-mp4
  model_parallel_size: 4
  use_sequence_parallel: False
  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202410-v2-dedup-prompt
  eval_data_path: 2024Oct2Nov@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202410-202411-v2-dedup-prompt
  rl_llm_eval_data_path: hs09_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/hindsight09_elden_v4_s6k;cceval_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/cceval_elden_v4_s6k
  model_vocab_size: 49280  # reuse the reward model's vocab size
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-rlhf-2
