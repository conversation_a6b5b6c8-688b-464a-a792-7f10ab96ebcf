#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/reward-sc2-from-v4-bs512e3.yml -s research/fastbackward/rlhf/train_sft.py -c GCP-US1
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/reward-sc2-from-v4-bs512e3.yml -s research/fastbackward/rlhf/train_sft.py -c CW
# Global batch size should be 64 * 2 * 8 / 2 = 512 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/86139
#
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh dcdedf78-5b7c-4223-9eb5-0588de3e84e5 dxy/sc2-elden/Reward-05to10v2-BS512E3-LR5E6to5E7-mp2 gcp
# bash research/utils/download_checkpoint.sh ec48da14-ff95-4afb-b418-ba5328c71b96 dxy/sc2-elden/Reward-05to10v3-BS512E3-LR5E6to5E7-S850-mp2 gcp
# bash research/utils/download_checkpoint.sh 9f7c4066-72f6-4278-9b28-7f271960ec0f dxy/sc2-elden/Reward-2024AUGto1017Excl1014-BS512E3-LR5E6to5E7-mp2 gcp
# bash research/utils/download_checkpoint.sh 731285f3-43d9-487f-af8d-16371e8b9c49 dxy/sc2-elden/Reward-0801to1024-BS512E3-LR5E6to5E7-mp2 gcp
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-0801to1024-BS512E3-LR5E6to5E7-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-elden/Reward-0801to1024-BS512E3-LR5E6to5E7-mp4 \
#     --mp 4
#
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp1 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-ffw \
#     --model-name="starcoder2-15b"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  # loss_mask_policy: fim
  # loss_mask_policy: reward
  # fim_middle_token_id: 2
  # eot_token_id: 0
  # pad_token_id: 49152
  gradient_accumulation_steps: 8
  batch_size: 2
  block_size: 7936

  max_iters: 0
  max_epochs: 2
  warmup_iters: 0
  lr_decay_iters: 0  # so that it will be the same as max_iters

  min_lr: 5.0e-7
  learning_rate: 5.0e-6
  decay_lr: True
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202410-v3
  # run_name: REWARD-SC2-15B-FV3-05to10v3-BS512E3-LR5E6to5E7
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241014-augment/
  # run_name: REWARD-SC2-15B-FV3-2024AUGto1014Augm-BS512E2-LR5E6to5E7
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241014-noaugment/
  # run_name: REWARD-SC2-15B-FV3-2024AUGto1014NoA-BS512E3-LR5E6to5E7
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241017-exclude1014-augment
  # run_name: REWARD-SC2-15B-FV3-2024AUGto1017Excl1014-Augm-BS512E1-LR5E6to5E7
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241001-20241024-augment
  # run_name: REWARD-SC2-15B-FV3-1001to1024-BS512E2-LR5E6to5E7
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240801-20241024-augment
  # run_name: REWARD-SC2-15B-FV3-0801to1024-BS512E2-LR5E6to5E7
  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240701-20241007-augment
  run_name: REWARD-SC2-15B-FV3-0701to1007-BS512E2-LR5E6to5E7
  sft_collate_strategy: reward_by_is_positive

  log_interval: 1
  eval_log_interval: 100
  eval_interval: 50
  # checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-mp2
  model_parallel_size: 2
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2;/mnt/efs/augment/user/dxy/datasets/rlhf/0915-reject-v2/
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202409
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202410-v3
  # eval_data_path: 2024Sep2Oct@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202409-202410;2024Oct2Nov@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202410-202411-v2;2024Oct14@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241014-noaugment;2024Oct17@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241017-noaugment
  # rl_llm_eval_data_path: hs09_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/hindsight09_elden_v4_s6k;cceval_eldenv4@/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/cceval_elden_v4_s6k
  eval_data_path: 1025-1026-w-skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241025-20241026-noaug-with-skip;1025-1026-wo-skip@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241025-20241026-noaug-without-skip
  model_vocab_size: 49280  # 49176
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-reward
