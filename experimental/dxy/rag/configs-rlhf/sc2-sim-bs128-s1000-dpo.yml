#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/sc2-sim-bs128-s1000-dpo.yml -s research/fastbackward/train_dpo.py
#
# Global batch size should be 64 * 1 * 8 / 4 = 64 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84861
#
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh 06e66075-fe70-4019-b1a9-9a93f08e4de2 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HD
# bash research/utils/download_checkpoint.sh c85330de-8ace-4f98-a11a-5ba9bf034188 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-mp2
# bash research/utils/download_checkpoint.sh 78df94dd-765b-4bcd-bcad-d6ecf31da345 dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp2

# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp1 \
#     --mp 1

# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-mp2 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-ffw \
#     --model-name="starcoder2-15b"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  # loss_mask_policy: fim
  # loss_mask_policy: reward
  # fim_middle_token_id: 2
  # eot_token_id: 0
  # pad_token_id: 49152
  gradient_accumulation_steps: 8
  batch_size: 1
  max_iters: 1000
  warmup_iters: 0
  lr_decay_iters: 1000
  block_size: 7940  # 7936

  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True

  rl_dpo_ce_loss_coeff: 6.0
  rl_dpo_beta: 0.1
  rlhf_checkpoint_interval: 50
  run_name: SC2-15B-SIMV2-BS64-S1000-LR1E5-DPO-0.1-6.0

  log_interval: 1
  eval_log_interval: 100
  eval_interval: 50
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp4
  rl_ref_checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp4
  model_parallel_size: 4
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data/dpo-train
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data/dpo-valid
  model_vocab_size: 49176
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-rlhf
