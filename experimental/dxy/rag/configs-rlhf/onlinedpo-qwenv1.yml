#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-rlhf/onlinedpo-qwenv1.yml -s research/fastbackward/rlhf/train_online_rl.py -c GCP-US1
# Global batch size should be 128 * 4 * 1 / 4 = 128, then 128 * 64 * 2 = 16K samples per batch.
#                             256 * 8 * 1 / 4 = 512, then 512 * 64 * 2 = 64K samples per batch
#                             512 * 8 * 1 / 4 = 1024, then 1024 * 64 * 2 = 128K samples per batch
#
# IPO-OffS0-VANG1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/118
# IPO-OffS0-VDog1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/131
# IPO-OffS0-VDCC1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/133
# IPO-OffS0-CCSFT1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.gcp-us1.r.augmentcode.com/det/experiments/2182
# IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/220
# IPO-OffS0-VDCC-R6R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/219
# IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P128E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/221
# IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B256P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
# - https://determined.cw-east4.r.augmentcode.com/det/experiments/228
#
# From https://determined.cw-east4.r.augmentcode.com/det/experiments/118/trials/118/checkpoints?sortDesc=false&sortKey=SORT_BY_STATE
# bash research/utils/download_checkpoint.sh 1e5e6eb7-7e58-4499-8599-93f8592ffa02 dxy/qwen-elden-rlhf/IPO-OffS0-VANG1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0-S120-mp4 cw-east4
# bash research/utils/download_checkpoint.sh a9a249d3-1191-4540-a162-2c166ac0cfa9 dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R6R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-S200-mp4 cw-east4
# bash research/utils/download_checkpoint.sh 06808ea2-5dfc-4762-939c-4e5bdc7d899f dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-S570-mp4 cw-east4
# bash research/utils/download_checkpoint.sh 27933c30-053f-4224-b384-8ff2c31ad696 dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P128E1-W50LR2E6to2E7-beta0.1-S300-mp4 cw-east4
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P128E1-W50LR2E6to2E7-beta0.1-S300-mp4 \
#     --o /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P128E1-W50LR2E6to2E7-beta0.1-S300-mp2 \
#     --mp 2
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_llama.py \
#     -i /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P128E1-W50LR2E6to2E7-beta0.1-S300-mp4 \
#     -o /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P128E1-W50LR2E6to2E7-beta0.1-S300-ffw
#
# Quantize the FFW checkpoint from BF16 to FP8
# python research/tools/quantization/quantize_llama.py \
#     --calibration-steps 200 --log-to-stdout \
#     --ckpt-path /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-S570-ffw \
#     --ckpt-sha256 0fbc33513af1bb64715fc3ba016113d141287aa97848cb3e6a09e00c5dce0f7c \
#     --out-path /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-S570-ffw-fp8 \
#     --calibration-data /mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/validation_dataset --max-seq-len 32768 -m qwen2_5-14b
#
#
# SHA of FP8: 81db92d6fc61fa75dba83e74f81212cfb3673b0f104c543b87dced75a57c47b6
# scp -r /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-S200-ffw-fp8 gcp1:/mnt/efs/augment/checkpoints/completion/qwelden-v2.1-fp8
# SHA of FP8: e09adc9d1d4895ad7ae5d791407f3c60a8a3cddee33d8315ba315588717c9554
# scp -r /mnt/efs/augment/checkpoints/dxy/qwen-elden-rlhf/IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-S570-ffw-fp8 gcp1:/mnt/efs/augment/checkpoints/completion/qwelden-v2.1-fp8
#
#
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 256  # 128 -> 256

fastbackward_configs:
 - configs/qwen25coder_14b.py

fastbackward_args:
  gradient_accumulation_steps: 8  # 4 -> 8
  batch_size: 1

  max_epochs: 1
  max_iters: 0
  warmup_iters: 50
  lr_decay_iters: 0
  block_size: 7936  # 7936

  min_lr: 2.0e-7
  learning_rate: 2.0e-6
  decay_lr: True

  # # Parameters following https://arxiv.org/pdf/2407.21783
  # rl_dpo_ce_loss_coeff: 0.2
  # rl_dpop_lambda: 0.0
  # dpo_use_ipo: False
  # rl_dpo_beta: 0.1
  # dpo_label_smoothing: 0.0
  # dpo_max_generation_steps: 96
  # dpo_num_pairs_per_prompt: 64
  # dpo_online_rl: False
  # rl_sync_ref_weights_per_iter: 30
  # run_name: DPO-OfflineS30-FV3-B64P64E1-W50LR2E6to2E7-beta0.1-ce0.2-smooth0.0

  rl_dpo_ce_loss_coeff: 0.0
  rl_dpop_lambda: 0.0
  rl_use_ipo: True
  rl_dpo_beta: 0.1
  rl_label_smoothing: 0.0
  rl_max_generation_steps: 96
  rl_num_pairs_per_prompt: 64
  rl_online_rl: False
  rl_sync_ref_weights_per_iter: 0
  rl_eval_interval: 50

  train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN-innocent-prompt-all;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train-5k-prompt
  run_name: IPO-OffS0-VDCC-R5R240801to250119-QWENV1-B512P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN-innocent-prompt-vanguard;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train-5k-prompt
  # run_name: IPO-OffS0-VVCC-R5R240801to250119-QWENV1-B512P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN-innocent-prompt-all;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train-5k-prompt
  # run_name: IPO-OffS0-VDCC-R5R0801to1231-QWENV1-B512P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train-5k-prompt;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train-100K
  # run_name: IPO-OffS0-CCSFT1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN-prompt-all;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train-5k-prompt;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train-5k-prompt
  # run_name: IPO-OffS0-VDCS1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN-prompt-all;/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train-5k-prompt
  # run_name: IPO-OffS0-VDCC1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN-prompt-all
  # run_name: IPO-OffS0-VDog1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0
  # train_data_path: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN-prompt-vanguard
  # run_name: IPO-OffS0-VANG1001to1130-QWENV1-B128P64E1-W50LR2E6to2E7-beta0.1-ce0.0-smooth0.0

  log_interval: 1
  eval_interval: 2
  rl_ckp_save_interval: 10
  eval_log_interval: 100
  checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/14b_elden_smart_fb_mp4
  rl_ref_checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/14b_elden_smart_fb_mp4
  # rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-0801to1126-BS1024E2-LR5E6to5E7-mp4
  # rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-XX0801to1231-BS1024E2-LR5E6to5E7-DA0.1-mp4
  # rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-0801to1231-BS1024E2-LR5E6to5E7-DA0.1-mp4
  rl_reward_checkpoint: /mnt/efs/augment/checkpoints/dxy/qwen-elden/Reward-240801-250119-BS1024E2-LR5E6to5E7-DA0.1-mp4
  model_parallel_size: 4
  use_sequence_parallel: False
  eval_data_path: 1127to1128@/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN-prompt-all
  rl_llm_eval_data_path: hs06@/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/qwenv1_hs06_s6k;hs09@/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/qwenv1_hs09_s6k;hs10@/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/qwenv1_hs10_s6k;cceval@/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/qwenv1_cceval_s6k
  model_vocab_size: 152064  # the reward model's vocab size
  checkpoint_optimizer_state: False
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False

  tokenizer_name: qwen25coder
  use_research_tokenizer: False
  visualize_logits_samples: 4

  wandb_project: dxy-rlhf-3
