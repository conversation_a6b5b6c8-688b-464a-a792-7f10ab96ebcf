#
# Baseline dataset + V1 unit-test dataset.
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs/star2_7b_btestv1_bs512_s5k.yml
# Global batch size should be 64 * 2 * 4 = 512 to speed up experiments.
#
# Take about 6~7 hours to complete
# Total training samples: 6.5M (we only see 2.5M samples in the dataset)
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/71859
# bash research/utils/download_checkpoint.sh f03ac1f2-1289-493d-a67c-f5872653d005 dxy/sc2-rogue/7B-base+v1-bs512s5k
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 2
  max_iters: 5000
  warmup_iters: 100
  lr_decay_iters: 5000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/dxy/baseline-4k-eth6_morelang3/dataset;/mnt/efs/augment/data/processed/rag/dataset/dxy/unit-test-v1/dataset
  eval_data_path: base@/mnt/efs/augment/data/processed/rag/dataset/dxy/baseline-4k-eth6_morelang3/validation_dataset;test_v0@/mnt/efs/augment/data/processed/rag/dataset/dxy/unit-test-v0_bigger_span-4k/validation_dataset;test_v1@/mnt/efs/augment/data/processed/rag/dataset/dxy/unit-test-v1/validation_dataset
  model_vocab_size: 51200
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: star2_7b-4k-base+v1-bs512-iter5k
  wandb_project: dxy-rogue
