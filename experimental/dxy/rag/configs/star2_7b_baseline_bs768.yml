#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs/star2_7b_baseline_bs768.yml
# Global batch size is 64 * 3 * 4 = 768 to speed up experiments.
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 3
  max_iters: 3000
  warmup_iters: 100
  lr_decay_iters: 3000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/dxy/baseline-4k-eth6_morelang3/dataset
  eval_data_path: /mnt/efs/augment/data/processed/rag/dataset/dxy/baseline-4k-eth6_morelang3/validation_dataset;/mnt/efs/augment/data/processed/rag/dataset/dxy/unit-test-v0_bigger_span-4k/validation_dataset
  model_vocab_size: 51200
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: star2_7b_baseline-4k-eth6_morelang3_bs768-iter3k
  wandb_project: dxy-rogue
