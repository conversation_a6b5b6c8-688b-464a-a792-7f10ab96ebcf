#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs/star2_7b_baseline_bs4k_s1k.yml
# Global batch size is 32 * 2 * 64 = 4096, and we train 1K steps
#
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/73299
# bash research/utils/download_checkpoint.sh e2cca754-1da5-487e-84bb-41ec21bf665c dxy/sc2-rogue/7B-baseline-bs4ks1k
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 32

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 64
  batch_size: 2
  max_iters: 1000
  warmup_iters: 100
  lr_decay_iters: 1000
  block_size: 4096
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/dxy/baseline-4k-eth6_morelang3/dataset
  eval_data_path: base@/mnt/efs/augment/data/processed/rag/dataset/dxy/baseline-4k-eth6_morelang3/validation_dataset;test_v0@/mnt/efs/augment/data/processed/rag/dataset/dxy/unit-test-v0_bigger_span-4k/validation_dataset;test_v1@/mnt/efs/augment/data/processed/rag/dataset/dxy/unit-test-v1/validation_dataset
  model_vocab_size: 51200
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: star2_7b_baseline-4k-eth6_morelang3_bs4k-iter1k
  wandb_project: dxy-rogue
