"""Download the all data including blobs for the hindsight.

2024-05 ~ 2024-06
python experimental/dxy/rag/scripts/download_hindsight.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood

2024-06 ~ 2024-07
python experimental/dxy/rag/scripts/download_hindsight.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-mercor \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard

2024-06-2024-07/aitutor-mercor should be skipped since it has 0 blobs.

2024-07 ~ 2024-08
python experimental/dxy/rag/scripts/download_hindsight.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-mercor \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard
"""

# fmt: off
import argparse
import json
import pathlib

import zstandard as zstd

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDataset,
    HindsightCompletionDatum,
)
from base.datasets.tenants import get_tenant
from experimental.dxy.rag.rlhf.local_content_manager import (
    LocalContentManager,
    PathAndContent,
)
from research.core import utils_for_log


def process_hindsight(input_folder: pathlib.Path, num_bucket: int):
    """Process the hindsight data."""
    hindsight_path = input_folder / "data.jsonl.zst"
    assert hindsight_path.exists()
    with zstd.open(hindsight_path, "r", encoding="utf-8") as f:
        data = HindsightCompletionDataset.load_data(f)
    print(f"{utils_for_log.time_string()} Load {len(data)} examples from {hindsight_path}")  # fmt: off
    tenant_name = input_folder.name
    print(f"{utils_for_log.time_string()} The tenant name is {tenant_name}")
    blobs = HindsightCompletionDataset.create_blobs_from_gcs(
        get_tenant(tenant_name),
        service_account_file=None,
    )

    blob_manager = LocalContentManager()
    dataset = HindsightCompletionDataset(data=data, blobs=blobs)

    all_datum = []
    for index, (datum, blobs) in enumerate(dataset):
        blob_names = blobs.keys()
        all_datum.append(
            {
                "datum": HindsightCompletionDatum.schema().dump(datum),
                "blob_names": list(blob_names),
            }
        )
        for k, blob in blobs.items():
            blob_manager.put(k, PathAndContent(path=str(blob.path), content=blob.content))
        print(f"Process {index:4d}/{len(data):4d} examples, {len(blob_manager):4d} blobs in total.")

    print(f"{utils_for_log.time_string()} Finish processing {len(all_datum)} examples.")
    datum_path = input_folder / "local-datum.jsonl.zst"
    with zstd.open(datum_path, "w", encoding="utf-8") as f:
        for datum in all_datum:
            json.dump(datum, f)
            f.write("\n")
    print(f"{utils_for_log.time_string()} Save {len(all_datum)} datum into {datum_path}.")
    blob_path = input_folder / "local-blobs"
    blob_manager.dump(blob_path, bucket=num_bucket)
    print(f"{utils_for_log.time_string()} Save {len(blob_manager)} blobs into {blob_path}.")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=pathlib.Path,
        default=None,
        nargs="+",
        required=True,
        help="The input file",
    )
    parser.add_argument(
        "--num_bucket",
        type=int,
        default=1,
        help="The number of bucket to dump the blobs.",
    )
    args = parser.parse_args()
    for input_folder in args.input:
        print(f"{utils_for_log.time_string()} Processing {input_folder}")
        assert input_folder.exists()
        process_hindsight(input_folder, args.num_bucket)


if __name__ == "__main__":
    main()
