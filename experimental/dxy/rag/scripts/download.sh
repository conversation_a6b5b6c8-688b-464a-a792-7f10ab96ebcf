#!/bin/bash
# bash experimental/dxy/rag/scripts/download.sh 2024-05 2024-10
set -e

if [ "$#" -ne 2 ] ;then
  echo "Input illegal number of parameters " $#
  exit 1
fi
# start="2024-06"
# end="2024-07"
start=$1
end=$2


# tenants="dogfood dogfood-shard aitutor-pareto aitutor-turing aitutor-mercor"
# tenants="aitutor-turing dogfood dogfood-shard"
# tenants="dogfood"
tenants="dogfood dogfood-shard aitutor-pareto aitutor-turing i0-vanguard0"
for tenant in $tenants; do
    python research/tools/export_datasets/export_hindsight_completions.py \
        --timestamp_begin "${start}-01T00:00:00+00:00" \
        --timestamp_end "${end}-01T00:00:00+00:00" \
        --vendor ${tenant} \
        --sample_limit 0 \
        --user_event_limit 0 \
        --context_size 30 \
        --output_dir "/mnt/efs/augment/user/dxy/hindsight/${start}-${end}"
done
