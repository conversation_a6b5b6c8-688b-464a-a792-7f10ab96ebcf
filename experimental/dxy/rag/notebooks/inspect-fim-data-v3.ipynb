{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["24/06/28 06:36:14 WARN Utils: Your hostname, dxy-8-a40 resolves to a loopback address: *********; using ************** instead (on interface enp5s0)\n", "24/06/28 06:36:14 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}], "source": ["from research.data.spark import get_session\n", "from research.data.spark import k8s_session\n", "from research.data.spark.utils import AugmentK8sSparkSession\n", "\n", "# spark_conf = {\n", "#     \"spark.executor.pyspark.memory\": \"64G\",\n", "#     \"spark.executor.memory\": \"32G\",\n", "#     \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "#     \"spark.task.cpus\": \"2\",\n", "# }\n", "\n", "# spark: AugmentK8sSparkSession = k8s_session(\n", "#     max_workers=512,\n", "#     conf=spark_conf,\n", "# )\n", "\n", "spark = get_session()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["24/06/28 06:36:17 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}], "source": ["# data_file = spark.read.parquet(\"s3a://dxy-dev-bucket/ragdata/limit-repo-100-normal/fims/\")\n", "data = spark.read.parquet(\"s3a://dxy-dev-bucket/ragdata/limit-repo-32000-normal/fims\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = data.limit(1000)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- max_stars_repo_name: string (nullable = true)\n", " |-- file_list: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- alphanum_fraction: double (nullable = true)\n", " |    |    |-- avg_line_length: double (nullable = true)\n", " |    |    |-- content: string (nullable = true)\n", " |    |    |-- ext: string (nullable = true)\n", " |    |    |-- hexsha: string (nullable = true)\n", " |    |    |-- langpart: string (nullable = true)\n", " |    |    |-- max_forks_repo_licenses: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- max_forks_repo_name: string (nullable = true)\n", " |    |    |-- max_forks_repo_path: string (nullable = true)\n", " |    |    |-- max_issues_repo_licenses: array (nullable = true)\n", " |    |    |    |-- element: string (containsNull = true)\n", " |    |    |-- max_issues_repo_name: string (nullable = true)\n", " |    |    |-- max_issues_repo_path: string (nullable = true)\n", " |    |    |-- max_line_length: long (nullable = true)\n", " |    |    |-- max_stars_repo_head_hexsha: string (nullable = true)\n", " |    |    |-- max_stars_repo_name: string (nullable = true)\n", " |    |    |-- max_stars_repo_path: string (nullable = true)\n", " |    |    |-- size: long (nullable = true)\n", " |-- max_size_lang: struct (nullable = true)\n", " |    |-- langpart: string (nullable = true)\n", " |    |-- total_size: long (nullable = true)\n", " |-- total_size: long (nullable = true)\n", " |-- fim_problems: binary (nullable = true)\n", " |-- num_fim_problems: long (nullable = true)\n", " |-- import_count: integer (nullable = true)\n", " |-- total_count: integer (nullable = true)\n", "\n"]}], "source": ["import pickle\n", "from research.fim.fim_sampling import FimProblem\n", "from pyspark.sql.functions import udf\n", "from pyspark.sql.types import IntegerType, StructType, StructField\n", "\n", "def analyze_fim_problems(fim_problems: bytes) -> tuple:\n", "    fim_problems = pickle.loads(fim_problems)\n", "    count = 0\n", "    for fim_problem in fim_problems:\n", "        if \"import\" in fim_problem.middle_node_type:\n", "            count += 1\n", "    return count, len(fim_problems)\n", "\n", "schema = StructType([\n", "    StructField(\"import_count\", IntegerType(), False),\n", "    StructField(\"total_count\", IntegerType(), False)\n", "])\n", "\n", "analyze_fim_problems_udf = udf(analyze_fim_problems, schema)\n", "\n", "# Apply the UDF to the 'fim_problems' column\n", "data = data.withColumn(\"analysis_result\", analyze_fim_problems_udf(data[\"fim_problems\"]))\n", "\n", "# Split the tuple into separate columns\n", "data = data.withColumn(\"import_count\", data[\"analysis_result\"].getItem(\"import_count\"))\n", "data = data.withColumn(\"total_count\", data[\"analysis_result\"].getItem(\"total_count\"))\n", "\n", "# Drop the intermediate column\n", "data = data.drop(\"analysis_result\")\n", "\n", "data.printSchema()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- import_count: integer (nullable = true)\n", " |-- total_count: integer (nullable = true)\n", "\n"]}], "source": ["result_data = data.select(\"import_count\", \"total_count\")\n", "result_data.printSchema()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Current mem limits: -1 of max -1\n", "\n", "Setting mem limits to 107374182400 of max 107374182400\n", "\n", "Current mem limits: 107374182400 of max 107374182400                            \n", "\n", "Current mem limits: 107374182400 of max 107374182400                (0 + 1) / 4]\n", "\n", "Current mem limits: 107374182400 of max 107374182400                (1 + 1) / 4]\n", "\n", "Current mem limits: 107374182400 of max 107374182400                (2 + 1) / 4]\n", "\n", "Current mem limits: 107374182400 of max 107374182400                            \n", "\n", "Current mem limits: 107374182400 of max 107374182400               (0 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (1 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (2 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (3 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (4 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (5 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (6 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (7 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (8 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400               (9 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400              (10 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400              (11 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400              (12 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400              (13 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400              (14 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400              (15 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400==>           (16 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400=====>        (17 + 1) / 20]\n", "\n", "Current mem limits: 107374182400 of max 107374182400========>     (18 + 1) / 20]\n", "\n", "                                                                                \r"]}], "source": ["result_data_pd = result_data.toPandas()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>import_count</th>\n", "      <th>total_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>74</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>338</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>151</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>6389</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>995</th>\n", "      <td>0</td>\n", "      <td>76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>996</th>\n", "      <td>0</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>997</th>\n", "      <td>0</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>1</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>999</th>\n", "      <td>0</td>\n", "      <td>122</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1000 rows × 2 columns</p>\n", "</div>"], "text/plain": ["     import_count  total_count\n", "0               3          159\n", "1               0           74\n", "2               0          338\n", "3               2          151\n", "4               2         6389\n", "..            ...          ...\n", "995             0           76\n", "996             0           17\n", "997             0           31\n", "998             1           60\n", "999             0          122\n", "\n", "[1000 rows x 2 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result_data_pd"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sum of import_count: 3883\n", "Sum of total_count: 343788\n"]}], "source": ["# Calculate the sum of the columns\n", "import_count_sum = result_data_pd['import_count'].sum()\n", "total_count_sum = result_data_pd['total_count'].sum()\n", "\n", "print(f\"Sum of import_count: {import_count_sum}\")\n", "print(f\"Sum of total_count: {total_count_sum}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.011294751416570678\n"]}], "source": ["print(3883 / 343788)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}