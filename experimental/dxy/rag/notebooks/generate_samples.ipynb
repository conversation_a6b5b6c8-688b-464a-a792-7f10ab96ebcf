{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n"]}], "source": ["\"\"\"Notebook to generate Rogue samples.\"\"\"\n", "from functools import partial\n", "from types import SimpleNamespace\n", "from datetime import datetime\n", "from pathlib import Path\n", "from dataclasses import asdict\n", "\n", "\n", "from pyspark.sql import functions as F\n", "\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.data.spark import k8s_session\n", "from research.data.spark.pipelines.utils import map_parquet\n", "from research.data.rag.rogue import process_partition_pandas, RogueSampleConfig\n", "from research.data.rag.utils import (\n", "    filter_by_repo_size,\n", "    inspect_samples,\n", "    save_config_s3,\n", ")\n", "from research.utils.generate_fim_data import FimDataProcessor\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["PROMPT_COLUMN = \"prompt_tokens\"\n", "REPO_LANG_COLUMN = \"max_size_lang\"\n", "REPO_LANG_SUBCOL = \"langpart\"\n", "\n", "# These languages names are for the stack\n", "# REPO_LANGUAGES = [\n", "#     \"c\",\n", "#     \"c++\",\n", "#     \"go\",\n", "#     \"java\",\n", "#     \"javascript\",\n", "#     \"python\",\n", "#     \"rust\",\n", "#     \"typescript\",\n", "#     \"c-sharp\",\n", "#     \"ruby\",\n", "#     \"php\",\n", "#     \"tsx\",\n", "#     \"jsx\",\n", "#     \"css\",\n", "#     \"shell\",\n", "#     \"scala\",\n", "#     \"ruby\",\n", "#     \"lua\",\n", "#     \"kotlin\",\n", "# ]\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\"\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["STAGE1_URI : s3a://augment-temporary/dxy/gen_unit_test/2024-05-24_07-25-07/step_1_repos/\n", "STAGE2_URI : s3a://augment-temporary/dxy/gen_unit_test/2024-05-24_07-25-07/step_2_samples/\n"]}], "source": ["config = RogueSampleConfig(\n", "    input=\"s3a://the-stack-processed/by-repo-3\",\n", "    output=\"s3a://augment-temporary/dxy/gensamples/test/\",\n", "    # output=\"s3a://michiel-dev-bucket/ragdata/eth6_4m_morelang4/\",\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    limit_repos=1000,\n", "    repo_min_size=500000,\n", "    repo_max_size=100000000,\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=500,\n", "    random_seed=74912,\n", "    num_retrieved_chunks=40,\n", "    scorer_config={\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "    },\n", "    chunker_config={\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "        \"include_scope_annotation\": <PERSON>als<PERSON>,\n", "    },\n", "    query_config={\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "    document_config={\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", ")\n", "\n", "now = datetime.now()\n", "formatted_time = now.strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "STAGE1_URI = f\"s3a://augment-temporary/dxy/gen_unit_test/{formatted_time}/step_1_repos/\"\n", "STAGE2_URI = f\"s3a://augment-temporary/dxy/gen_unit_test/{formatted_time}/step_2_samples/\"\n", "print(f\"STAGE1_URI : {STAGE1_URI}\")\n", "print(f\"STAGE2_URI : {STAGE2_URI}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:Cannot find EFS mount point for region \"las1\";Will try to use other regions\n", "WARNING:root:Cannot find EFS mount point any regions. Will try all known mount locations on workers.\n", "INFO:root:Found EFS mount at /mnt/efs/spark-data\n", "INFO:root:Using EFS drive viofs-aug-cw-las1-spark-data at /mnt/efs/spark-data for package sharing\n", "INFO:root:Template file: /tmp/tmpia2f1a0m/spark-template-13cd0ba9-dae0-4ccd-9d5c-4c313429973c.yaml\n", "INFO:root:apiVersion: v1\n", "kind: Pod\n", "metadata:\n", "  labels:\n", "    function: spark-executor\n", "  name: example-pod\n", "spec:\n", "  affinity:\n", "    nodeAffinity:\n", "      requiredDuringSchedulingIgnoredDuringExecution:\n", "        nodeSelectorTerms:\n", "        - matchExpressions:\n", "          - key: topology.kubernetes.io/region\n", "            operator: In\n", "            values:\n", "            - LAS1\n", "          - key: node.coreweave.cloud/cpu\n", "            operator: In\n", "            values:\n", "            - intel-xeon-icelake\n", "            - intel-xeon-scalable\n", "            - intel-xeon-v4\n", "  containers:\n", "  - image: ignored-by:spark\n", "    imagePullPolicy: Always\n", "    name: spark-executor\n", "    resources:\n", "      limits:\n", "        cpu: '6'\n", "      requests:\n", "        cpu: '6'\n", "        ephemeral-storage: 50Gi\n", "        memory: 120Gi\n", "    volumeMounts:\n", "    - mountPath: /mnt/efs/spark-data\n", "      name: aug-cw-las1-spark-data\n", "    - mountPath: /mnt/efs/augment\n", "      name: viofs-aug-cw-las1\n", "    - mountPath: /mnt/efs/augment-nvme\n", "      name: viofs-aug-cw-las1-nvme\n", "    - mountPath: /mnt/efs/spark-python\n", "      name: viofs-aug-cw-las1-spark-python\n", "    - mountPath: /mnt/efs/hdc\n", "      name: viofs-aug-cw-las1-hdc\n", "    - mountPath: /mnt/ephemeral/disk\n", "      name: local-drive\n", "    - mountPath: /mnt/ephemeral/ram\n", "      name: local-ramdisk\n", "  volumes:\n", "  - name: aug-cw-las1-spark-data\n", "    persistentVolumeClaim:\n", "      claimName: aug-cw-las1-spark-data\n", "  - name: viofs-aug-cw-las1\n", "    persistentVolumeClaim:\n", "      claimName: aug-cw-las1\n", "  - name: viofs-aug-cw-las1-nvme\n", "    persistentVolumeClaim:\n", "      claimName: aug-cw-las1-nvme\n", "  - name: viofs-aug-cw-las1-spark-python\n", "    persistentVolumeClaim:\n", "      claimName: aug-cw-las1-spark-python\n", "  - name: viofs-aug-cw-las1-hdc\n", "    persistentVolumeClaim:\n", "      claimName: aug-cw-las1-hdc\n", "  - emptyDir:\n", "      sizeLimit: 500Gi\n", "    name: local-drive\n", "  - emptyDir:\n", "      medium: Memory\n", "      sizeLimit: 500Mi\n", "    name: local-ramdisk\n", "\n", "WARNING:root:Skipping bazel build.\n", "INFO:root:Copy user base - Execution time: 0.9981 seconds\n", "INFO:root:Copy research - Execution time: 7.7680 seconds\n", "INFO:root:Copy base - Execution time: 1.6896 seconds\n", "INFO:root:Copy experimental - Execution time: 8.5030 seconds\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "INFO:root:Create spark session - Execution time: 32.4434 seconds\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing retrieval samples\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/05/24 07:28:37 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["+-----------------------+-------+\n", "|max_size_lang[langpart]|  count|\n", "+-----------------------+-------+\n", "|             javascript|1357807|\n", "|                 python|1252135|\n", "|                    php| 792097|\n", "|                   html| 770129|\n", "|                   java| 662576|\n", "|             typescript| 378622|\n", "|                     go| 366317|\n", "|                c-sharp| 353222|\n", "|                    c++| 284970|\n", "|                      c| 271766|\n", "|                   ruby| 224832|\n", "|                    css| 142759|\n", "|                  shell| 132085|\n", "|                   rust| 129251|\n", "|                  swift| 115634|\n", "|             dockerfile|  69179|\n", "|                   dart|  63214|\n", "|                  scala|  51371|\n", "|                    jsx|  46639|\n", "|                    lua|  37156|\n", "|                    sql|  23981|\n", "|                   perl|  19810|\n", "|                   diff|  10083|\n", "|               assembly|   5687|\n", "|                   cuda|   3045|\n", "|          systemverilog|   1867|\n", "|                 cython|   1793|\n", "|               cucumber|   1196|\n", "|                graphql|    497|\n", "|                verilog|      1|\n", "+-----------------------+-------+\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 4:====================================================>(2386 + 7) / 2393]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing 135868 repos\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# This just does filtering then stores results to parquet files for later processing.\n", "# Almost entirely IO bound by write caching on CoreWeave side.\n", "# That is all spark job will finish writing in 5min but\n", "# will need another 15m for CW to flush their write cache on shared drives or object stores\n", "\n", "# Note that we fail one partition at a time, so\n", "# if you want more grainular failures,\n", "# you an create more partitions.\n", "\n", "spark = k8s_session(max_workers=100)\n", "print(\"Processing retrieval samples\")\n", "df = spark.read.parquet(config.input)\n", "\n", "# Report statistics on repo languages\n", "top_languages = (df\n", "                 .groupBy(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])\n", "                 .count()\n", "                 .orderBy(<PERSON><PERSON>desc(\"count\"))\n", "                 .limit(100))\n", "\n", "# Show the result\n", "top_languages.show(100)\n", "\n", "# Filter for language of main repo being language we want to train on\n", "if hasattr(config, \"repo_languages\"):\n", "    df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages))\n", "\n", "df = filter_by_repo_size(\n", "    df,\n", "    min_size=getattr(config, \"repo_min_size\", None),\n", "    max_size=getattr(config, \"repo_max_size\", None),\n", ")\n", "\n", "print(f\"Processing {df.count()} repos\", flush=True)\n", "\n", "limit_repos = config.limit_repos\n", "df = df.limit(limit_repos)\n", "\n", "num_partitions = max(limit_repos // 50, 20)\n", "\n", "# df = df.repartition(num_partitions)\n", "# df.write.parquet(STAGE1_URI, mode=\"overwrite\")\n", "# spark.stop()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import re\n", "import typing\n", "from pyspark.sql.types import StructType, StructField, ArrayType, IntegerType, StringType\n", "\n", "def count_filtered_files(file_list: list[dict[str, typing.Any]], config: RogueSampleConfig) -> int:\n", "    \"\"\"Filter files based on size, language, and path pattern.\n", "\n", "    Args:\n", "        file_list: A list of files, where each file is a dictionary with keys 'size', 'language', and 'path'.\n", "        config: The configuration object with filtering criteria.\n", "\n", "    Returns:\n", "        A list of filtered files that meet the criteria.\n", "    \"\"\"\n", "    filtered_files = []\n", "    for file in file_list:\n", "        if config.small_filter_char_threshold is not None and file['size'] < config.small_filter_char_threshold:\n", "            continue\n", "        if file['language'] not in config.sample_languages:\n", "            continue\n", "        if config.file_path_regex_pattern is not None and not re.match(config.file_path_regex_pattern, file['path']):\n", "            continue\n", "        filtered_files.append(file)\n", "    return len(filtered_files)\n", "\n", "\n", "filter_udf = F.udf(lambda file_list: count_filtered_files(file_list, config),\n", "                   returnType=IntegerType())\n", "filtered_df = df.withColumn(\"num_filtered_file_list\", filter_udf(F.col(\"file_list\")))\n", "result_df = filtered_df.filter(<PERSON>.col(\"num_filtered_file_list\") > 0).drop(\"file_list\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["24/05/24 07:44:38 WARN TaskSetManager: Lost task 3.0 in stage 7.0 (TID 4793) (************ executor 103): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/tmp/ipykernel_136627/1108539774.py\", line 27, in <lambda>\n", "  File \"/tmp/ipykernel_136627/1108539774.py\", line 19, in count_filtered_files\n", "  File \"/opt/spark/python/lib/pyspark.zip/pyspark/sql/types.py\", line 2170, in __getitem__\n", "    raise ValueError(item)\n", "ValueError: language\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:561)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:94)\n", "\tat org.apache.spark.sql.execution.python.PythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:75)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:514)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenExec$$anon$1.hasNext(WholeStageCodegenExec.scala:760)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.shuffle.sort.BypassMergeSortShuffleWriter.write(BypassMergeSortShuffleWriter.java:140)\n", "\tat org.apache.spark.shuffle.ShuffleWriteProcessor.write(ShuffleWriteProcessor.scala:59)\n", "\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:101)\n", "\tat org.apache.spark.scheduler.ShuffleMapTask.runTask(ShuffleMapTask.scala:53)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:161)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:139)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:554)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:1529)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:557)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)\n", "\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)\n", "\tat java.base/java.lang.Thread.run(Thread.java:829)\n", "\n", "24/05/24 07:44:46 ERROR TaskSetManager: Task 1 in stage 7.0 failed 4 times; aborting job\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 134.0 in stage 7.0 (TID 4966) (************ executor 101): TaskKilled (Stage cancelled)\n"]}, {"ename": "PythonException", "evalue": "\n  An exception was thrown from the Python worker. Please see the stack trace below.\nTraceback (most recent call last):\n  File \"/tmp/ipykernel_136627/1108539774.py\", line 27, in <lambda>\n  File \"/tmp/ipykernel_136627/1108539774.py\", line 19, in count_filtered_files\n  File \"/opt/spark/python/lib/pyspark.zip/pyspark/sql/types.py\", line 2170, in __getitem__\n    raise ValueError(item)\nValueError: language\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPythonException\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[17], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mProcessing \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mresult_df\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcount\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m repos\u001b[39m\u001b[38;5;124m\"\u001b[39m, flush\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/pyspark/sql/dataframe.py:1195\u001b[0m, in \u001b[0;36mDataFrame.count\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1172\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcount\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mint\u001b[39m:\n\u001b[1;32m   1173\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Returns the number of rows in this :class:`DataFrame`.\u001b[39;00m\n\u001b[1;32m   1174\u001b[0m \n\u001b[1;32m   1175\u001b[0m \u001b[38;5;124;03m    .. versionadded:: 1.3.0\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1193\u001b[0m \u001b[38;5;124;03m    3\u001b[39;00m\n\u001b[1;32m   1194\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1195\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mint\u001b[39m(\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_jdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcount\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/py4j/java_gateway.py:1322\u001b[0m, in \u001b[0;36mJavaMember.__call__\u001b[0;34m(self, *args)\u001b[0m\n\u001b[1;32m   1316\u001b[0m command \u001b[38;5;241m=\u001b[39m proto\u001b[38;5;241m.\u001b[39mCALL_COMMAND_NAME \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1317\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcommand_header \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1318\u001b[0m     args_command \u001b[38;5;241m+\u001b[39m\\\n\u001b[1;32m   1319\u001b[0m     proto\u001b[38;5;241m.\u001b[39mEND_COMMAND_PART\n\u001b[1;32m   1321\u001b[0m answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgateway_client\u001b[38;5;241m.\u001b[39msend_command(command)\n\u001b[0;32m-> 1322\u001b[0m return_value \u001b[38;5;241m=\u001b[39m \u001b[43mget_return_value\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1323\u001b[0m \u001b[43m    \u001b[49m\u001b[43manswer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgateway_client\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtarget_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1325\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m temp_arg \u001b[38;5;129;01min\u001b[39;00m temp_args:\n\u001b[1;32m   1326\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(temp_arg, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_detach\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n", "File \u001b[0;32m/opt/conda/lib/python3.11/site-packages/pyspark/errors/exceptions/captured.py:175\u001b[0m, in \u001b[0;36mcapture_sql_exception.<locals>.deco\u001b[0;34m(*a, **kw)\u001b[0m\n\u001b[1;32m    171\u001b[0m converted \u001b[38;5;241m=\u001b[39m convert_exception(e\u001b[38;5;241m.\u001b[39mjava_exception)\n\u001b[1;32m    172\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(converted, UnknownException):\n\u001b[1;32m    173\u001b[0m     \u001b[38;5;66;03m# Hide where the exception came from that shows a non-Pythonic\u001b[39;00m\n\u001b[1;32m    174\u001b[0m     \u001b[38;5;66;03m# JVM exception message.\u001b[39;00m\n\u001b[0;32m--> 175\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m converted \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m    176\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    177\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[0;31mPythonException\u001b[0m: \n  An exception was thrown from the Python worker. Please see the stack trace below.\nTraceback (most recent call last):\n  File \"/tmp/ipykernel_136627/1108539774.py\", line 27, in <lambda>\n  File \"/tmp/ipykernel_136627/1108539774.py\", line 19, in count_filtered_files\n  File \"/opt/spark/python/lib/pyspark.zip/pyspark/sql/types.py\", line 2170, in __getitem__\n    raise ValueError(item)\nValueError: language\n"]}, {"name": "stderr", "output_type": "stream", "text": ["24/05/24 07:44:46 WARN TaskSetManager: Lost task 115.0 in stage 7.0 (TID 4931) (************ executor 101): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 19.2 in stage 7.0 (TID 4921) (************ executor 103): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 102.0 in stage 7.0 (TID 4912) (************ executor 138): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 103.0 in stage 7.0 (TID 4913) (************ executor 138): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 104.0 in stage 7.0 (TID 4914) (************ executor 138): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 21.1 in stage 7.0 (TID 4910) (************ executor 138): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 132.0 in stage 7.0 (TID 4963) (10.140.149.102 executor 133): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 131.0 in stage 7.0 (TID 4962) (10.140.149.102 executor 133): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 28.0 in stage 7.0 (TID 4818) (10.144.2.124 executor 106): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 81.2 in stage 7.0 (TID 4965) (10.144.154.177 executor 115): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 7.2 in stage 7.0 (TID 4923) (************ executor 101): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:46 WARN TaskSetManager: Lost task 75.1 in stage 7.0 (TID 4941) (10.147.10.57 executor 126): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 70.1 in stage 7.0 (TID 4925) (************ executor 103): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 8.1 in stage 7.0 (TID 4883) (10.144.154.149 executor 116): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 0.2 in stage 7.0 (TID 4961) (10.147.177.194 executor 111): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 10.0 in stage 7.0 (TID 4800) (10.144.2.92 executor 105): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 18.1 in stage 7.0 (TID 4899) (10.141.18.8 executor 104): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 14.0 in stage 7.0 (TID 4804) (10.144.2.92 executor 105): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 99.0 in stage 7.0 (TID 4906) (10.146.40.50 executor 110): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 96.0 in stage 7.0 (TID 4903) (10.146.40.50 executor 110): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 98.0 in stage 7.0 (TID 4905) (10.146.40.50 executor 110): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 97.0 in stage 7.0 (TID 4904) (10.146.40.50 executor 110): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 20.1 in stage 7.0 (TID 4902) (10.146.40.50 executor 110): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 42.0 in stage 7.0 (TID 4832) (10.147.177.194 executor 111): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 12.0 in stage 7.0 (TID 4802) (10.144.2.92 executor 105): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 66.1 in stage 7.0 (TID 4900) (10.140.49.125 executor 102): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 44.0 in stage 7.0 (TID 4834) (10.147.177.194 executor 111): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 68.1 in stage 7.0 (TID 4926) (10.147.10.47 executor 129): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 114.0 in stage 7.0 (TID 4930) (10.147.10.47 executor 129): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 113.0 in stage 7.0 (TID 4929) (10.147.10.47 executor 129): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 112.0 in stage 7.0 (TID 4928) (10.147.10.47 executor 129): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 111.0 in stage 7.0 (TID 4927) (10.147.10.47 executor 129): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 23.1 in stage 7.0 (TID 4901) (10.140.49.125 executor 102): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 87.0 in stage 7.0 (TID 4890) (10.147.10.6 executor 131): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 85.0 in stage 7.0 (TID 4888) (10.147.10.6 executor 131): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 67.1 in stage 7.0 (TID 4909) (10.140.49.125 executor 102): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 36.0 in stage 7.0 (TID 4826) (10.146.40.29 executor 108): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:47 WARN TaskSetManager: Lost task 38.0 in stage 7.0 (TID 4828) (10.146.40.29 executor 108): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 116.0 in stage 7.0 (TID 4935) (10.144.2.124 executor 106): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 122.0 in stage 7.0 (TID 4947) (10.146.80.57 executor 137): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 124.0 in stage 7.0 (TID 4949) (10.146.80.57 executor 137): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 121.0 in stage 7.0 (TID 4946) (10.146.80.57 executor 137): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 123.0 in stage 7.0 (TID 4948) (10.146.80.57 executor 137): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 69.1 in stage 7.0 (TID 4945) (10.146.80.57 executor 137): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 34.2 in stage 7.0 (TID 4940) (************ executor 101): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 74.0 in stage 7.0 (TID 4870) (10.146.40.6 executor 109): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 109.0 in stage 7.0 (TID 4919) (10.147.177.201 executor 113): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 108.0 in stage 7.0 (TID 4918) (10.147.177.201 executor 113): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 106.0 in stage 7.0 (TID 4916) (10.147.177.201 executor 113): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 105.0 in stage 7.0 (TID 4915) (10.147.177.201 executor 113): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 107.0 in stage 7.0 (TID 4917) (10.147.177.201 executor 113): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 16.1 in stage 7.0 (TID 4908) (10.141.18.8 executor 104): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 117.0 in stage 7.0 (TID 4936) (10.144.154.129 executor 114): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 30.1 in stage 7.0 (TID 4939) (10.141.18.8 executor 104): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 62.0 in stage 7.0 (TID 4852) (10.144.2.126 executor 107): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 60.0 in stage 7.0 (TID 4850) (10.144.2.126 executor 107): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 63.0 in stage 7.0 (TID 4853) (10.144.2.126 executor 107): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 64.0 in stage 7.0 (TID 4854) (10.144.2.126 executor 107): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 61.0 in stage 7.0 (TID 4851) (10.144.2.126 executor 107): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 35.0 in stage 7.0 (TID 4825) (10.146.40.29 executor 108): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 4.1 in stage 7.0 (TID 4884) (10.144.154.149 executor 116): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 83.0 in stage 7.0 (TID 4886) (10.144.154.149 executor 116): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 39.0 in stage 7.0 (TID 4829) (10.146.40.29 executor 108): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 37.0 in stage 7.0 (TID 4827) (10.146.40.29 executor 108): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 82.0 in stage 7.0 (TID 4885) (10.144.154.149 executor 116): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 128.0 in stage 7.0 (TID 4958) (10.140.149.76 executor 136): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 127.0 in stage 7.0 (TID 4957) (10.140.149.76 executor 136): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 119.1 in stage 7.0 (TID 4955) (10.140.149.76 executor 136): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 129.0 in stage 7.0 (TID 4959) (10.140.149.76 executor 136): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 126.0 in stage 7.0 (TID 4956) (10.140.149.76 executor 136): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 84.0 in stage 7.0 (TID 4887) (10.144.154.149 executor 116): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 100.0 in stage 7.0 (TID 4907) (10.141.18.8 executor 104): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 41.0 in stage 7.0 (TID 4831) (10.147.177.194 executor 111): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 43.0 in stage 7.0 (TID 4833) (10.147.177.194 executor 111): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 33.0 in stage 7.0 (TID 4823) (10.144.154.129 executor 114): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 89.0 in stage 7.0 (TID 4892) (10.147.10.6 executor 131): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 48.0 in stage 7.0 (TID 4838) (10.144.154.177 executor 115): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 77.0 in stage 7.0 (TID 4875) (10.147.177.200 executor 112): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 110.0 in stage 7.0 (TID 4920) (************ executor 103): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 118.0 in stage 7.0 (TID 4938) (10.144.154.129 executor 114): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:48 WARN TaskSetManager: Lost task 133.0 in stage 7.0 (TID 4964) (10.141.18.8 executor 104): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 53.0 in stage 7.0 (TID 4843) (10.147.10.57 executor 126): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 31.0 in stage 7.0 (TID 4821) (10.144.154.129 executor 114): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 94.0 in stage 7.0 (TID 4897) (10.140.149.88 executor 134): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 92.0 in stage 7.0 (TID 4895) (10.140.149.88 executor 134): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 91.0 in stage 7.0 (TID 4894) (10.140.149.88 executor 134): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 93.0 in stage 7.0 (TID 4896) (10.140.149.88 executor 134): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 90.0 in stage 7.0 (TID 4893) (10.140.149.88 executor 134): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 51.0 in stage 7.0 (TID 4841) (10.147.10.57 executor 126): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 130.0 in stage 7.0 (TID 4960) (10.140.49.125 executor 102): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 65.2 in stage 7.0 (TID 4932) (10.144.2.92 executor 105): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 50.1 in stage 7.0 (TID 4954) (10.147.10.57 executor 126): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 29.0 in stage 7.0 (TID 4819) (10.144.2.124 executor 106): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 120.0 in stage 7.0 (TID 4943) (10.140.49.125 executor 102): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 6.2 in stage 7.0 (TID 4952) (************ executor 103): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 2.2 in stage 7.0 (TID 4944) (************ executor 103): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 13.2 in stage 7.0 (TID 4951) (************ executor 101): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 72.0 in stage 7.0 (TID 4868) (10.146.40.6 executor 109): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 71.0 in stage 7.0 (TID 4867) (10.146.40.6 executor 109): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 27.0 in stage 7.0 (TID 4817) (10.144.2.124 executor 106): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 24.1 in stage 7.0 (TID 4866) (10.146.40.6 executor 109): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 73.0 in stage 7.0 (TID 4869) (10.146.40.6 executor 109): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 80.1 in stage 7.0 (TID 4953) (10.147.10.57 executor 126): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 11.1 in stage 7.0 (TID 4933) (10.144.2.92 executor 105): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 125.0 in stage 7.0 (TID 4950) (10.144.154.129 executor 114): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 47.0 in stage 7.0 (TID 4837) (10.144.154.177 executor 115): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 25.0 in stage 7.0 (TID 4815) (10.144.2.124 executor 106): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 49.0 in stage 7.0 (TID 4839) (10.144.154.177 executor 115): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 45.0 in stage 7.0 (TID 4835) (10.144.154.177 executor 115): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 3.2 in stage 7.0 (TID 4873) (10.147.177.200 executor 112): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 78.0 in stage 7.0 (TID 4876) (10.147.177.200 executor 112): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 79.0 in stage 7.0 (TID 4877) (10.147.177.200 executor 112): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 76.0 in stage 7.0 (TID 4874) (10.147.177.200 executor 112): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 55.0 in stage 7.0 (TID 4845) (10.140.149.102 executor 133): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 59.0 in stage 7.0 (TID 4849) (10.140.149.102 executor 133): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 86.0 in stage 7.0 (TID 4889) (10.147.10.6 executor 131): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:49 WARN TaskSetManager: Lost task 88.0 in stage 7.0 (TID 4891) (10.147.10.6 executor 131): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:50 WARN TaskSetManager: Lost task 57.0 in stage 7.0 (TID 4847) (10.140.149.102 executor 133): TaskKilled (Stage cancelled)\n", "24/05/24 07:44:50 WARN TaskSetManager: Lost task 101.0 in stage 7.0 (TID 4911) (************ executor 138): TaskKilled (Stage cancelled)\n"]}], "source": ["print(f\"Processing {result_df.count()} repos\", flush=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initial condition set to True (no filter)\n", "condition = F.lit(True)\n", "\n", "# Filter tiny files\n", "if config.small_filter_char_threshold is not None:\n", "        condition = condition & (<PERSON>.col(\"file_list.SIZE_COLUMN\") >= config.small_filter_char_threshold)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# With that we estimate just over 20min per parquet file.\n", "# At 100 workers and 2000 files that is about 10 hours of work\n", "# Setting timeout to 1h to be safe\n", "# Do a timing run to see where my timeouts need to be and have some notion of memory usage\n", "# The GPU part takes less than half of the total time so GPU type probably doesn't matter.\n", "# It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.\n", "# We increase it a bit here\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"50G\",\n", "    \"spark.executor.memory\": \"30G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"256\",\n", "}\n", "\n", "spark_conf[\"spark.task.cpus\"] = \"5\"\n", "max_workers = min(64, limit_repos)\n", "spark = k8s_session(\n", "    max_workers=max_workers,\n", "    conf=spark_conf,\n", "    gpu_type=\"RTX_A5000\",\n", ")\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark,\n", "    partial(process_partition_pandas, config=config),\n", "    input_path=STAGE1_URI,\n", "    output_path=config.output,\n", "    timeout=3600,  # one hour timeout\n", "    batch_size=min(100, limit_repos // max_workers),\n", "    drop_original_columns=True,\n", "    ignore_error=True,\n", ")\n", "spark.stop()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"result\", result)\n", "stderr = result[\"task_info\"][\"stderr\"]\n", "print(\"stderr\", stderr)\n", "stdout = result[\"task_info\"][\"stdout\"]\n", "print(\"stdout\", stdout[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for line in stderr[0].split(\"\\n\"):\n", "    print(line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["path = config.output\n", "path = path.split(\"s3a://\")[1]\n", "bucket_name, file_name = path.split(\"/\", 1)\n", "file_name += \"config.json\"\n", "save_config_s3(config=asdict(config), bucket_name=bucket_name, file_name=file_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inspect output samples\n", "df = inspect_samples(config.output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Report statistics on generated samples by language\n", "spark = k8s_session(max_workers=100)\n", "\n", "files = map_parquet.list_files(\n", "    spark,\n", "    config.output,\n", "    suffix=\"parquet\",\n", "    include_path=False,\n", ")\n", "read_paths = [config.output + filepath for filepath in files]\n", "df = spark.read.parquet(*read_paths)\n", "\n", "def get_file_extension(file_path):\n", "    return Path(file_path).suffix[1:]\n", "\n", "# Guess languages from file_path\n", "df = df.withColumn(\"extension\", F.udf(get_file_extension)(F.col(\"file_path\")))\n", "\n", "# Count for each language\n", "df.groupBy(\"extension\").count().show(100)\n", "\n", "spark.stop()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}