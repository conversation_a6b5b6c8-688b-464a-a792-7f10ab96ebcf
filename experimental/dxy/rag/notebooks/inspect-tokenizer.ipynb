{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Create a huggingface tokenizer\n", "from transformers import AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Base\")\n", "# tokenizer = AutoTokenizer.from_pretrained(\"/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Instruct\")\n", "# tokenizer = AutoTokenizer.from_pretrained(\"/mnt/efs/augment/checkpoints/llama3/hf/Meta-Llama-3-8B-Instruct\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100001\n"]}], "source": ["print(tokenizer.eos_token_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\" \", add_special_tokens=False))\n", "print(tokenizer.encode(\"  \", add_special_tokens=False))\n", "print(tokenizer.encode(\"   \", add_special_tokens=False))\n", "print(tokenizer.encode(\"    \", add_special_tokens=False))\n", "print(tokenizer.encode(\" 1\", add_special_tokens=False))\n", "print(tokenizer.encode(\" 10\", add_special_tokens=False))\n", "print(tokenizer.encode(\"100\", add_special_tokens=False))\n", "print(tokenizer.encode(\"1000\", add_special_tokens=False))\n", "print(tokenizer.encode(\"\\n\", add_special_tokens=False))\n", "print(tokenizer.encode(\"\\n\\n\", add_special_tokens=False))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\"123\", add_special_tokens=False))\n", "print(tokenizer.encode(\"123\", add_special_tokens=True))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\"<｜end▁of▁sentence｜>\", add_special_tokens=False))\n", "print(tokenizer.encode(\"<｜end▁of▁sentence｜>\", add_special_tokens=True))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\"董宣毅\", add_special_tokens=False))\n", "print(tokenizer.encode(\"董宣毅\", add_special_tokens=True))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\"<|skip|>\", add_special_tokens=False))\n", "print(tokenizer.encode(\"<|pause|>\", add_special_tokens=False))\n", "print(tokenizer.encode(\"i love u\", add_special_tokens=False))\n", "print(tokenizer.encode(\"<|filename|>src/file.py\", add_special_tokens=False))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}