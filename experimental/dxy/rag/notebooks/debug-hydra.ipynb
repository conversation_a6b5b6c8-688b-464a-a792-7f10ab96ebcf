{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'rogue2_statelesscache', 'checkpoint_path': '/mnt/efs/augment/checkpoints/dxy/sc2-3b-rogue/baseline-4k-eth6_morelang3-bs512s5k', 'prompt': {'max_prefix_tokens': 1030, 'max_suffix_tokens': 768, 'max_retrieved_chunk_tokens': -1, 'max_prompt_tokens': 3838, 'max_filename_tokens': 50, 'component_order': ['prefix', 'suffix', 'retrieval', 'nearby_prefix'], 'context_quant_token_len': 50, 'nearby_prefix_token_len': 250, 'nearby_prefix_token_overlap': 0, 'nearby_suffix_token_len': 0, 'nearby_suffix_token_overlap': 0, 'use_far_prefix_token': True, 'prepend_bos_token': False}, 'model_parallel_size': 1}\n"]}], "source": ["import os\n", "import pathlib\n", "from research.eval.harness.factories import create_system\n", "from experimental.dxy.rogue.exps.model_configs import model_config_dict\n", "from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict\n", "\n", "system_config = {\n", "    \"name\": \"basic_rag\",\n", "    \"model\": model_config_dict[\"roguesl_4k\"],\n", "    \"generation_options\": {\"max_generated_tokens\": 280},\n", "    \"retriever\": retriever_config_dict[\"ethanol616\"],\n", "    \"experimental\": {\n", "        \"remove_suffix\": False,\n", "        \"trim_on_dedent\": <PERSON>alse,\n", "        \"retriever_top_k\": 25,\n", "    },\n", "    \"fim_gen_mode\": \"evaluation\",\n", "}\n", "system_config[\"model\"][\"checkpoint_path\"] = (\n", "    \"/mnt/efs/augment/checkpoints/dxy/sc2-3b-rogue/baseline-4k-eth6_morelang3-bs512s5k\"\n", ")\n", "system_config[\"model\"][\"model_parallel_size\"] = 1\n", "system = create_system(system_config)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:hydra.driver:The CW docker registry has 91 tagged images.\n", "INFO:hydra.driver:Hydra driver pod name prefix: hydra-eval-augmented-hydra-test-local-pod\n", "INFO:hydra.driver:Using docker images lookup file: /home/<USER>/src/augment/research/eval/hydra/supported_repos.yaml.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["category2counts: Counter({'api_call': 701, 'meaningful_str': 200, 'interface': 152})\n", "Find 441 documents in the corpus.\n"]}], "source": ["import research.eval.harness.metrics as metrics\n", "from research.eval.harness.tasks.api_call_task import (\n", "    ApiCallTask,\n", "    hydra_lib,\n", "    SUPPORTED_DATASET2DIR,\n", "    HYDRA_SOFT_TIMEOUT_SECS,\n", "    HYDRA_HARD_TIMEOUT_SECS,\n", ")\n", "\n", "driver = hydra_lib.Driver(\n", "    driver_name=\"eval-augmented-hydra-test\",\n", "    local_timeout_secs=HYDRA_SOFT_TIMEOUT_SECS,\n", "    global_timeout_secs=HYDRA_HARD_TIMEOUT_SECS,\n", "    hydra_block_resource_internet_access=False,\n", ")\n", "task = ApiCallTask(\n", "    SUPPORTED_DATASET2DIR[\"finegrained-python.large\"],\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> initializing model parallel with size 1\n", "> initializing ddp with size 1\n", "> initializing pipeline with size 1\n", "NeoXArgs.from_ymls() [PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml'), PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml'), PosixPath('/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/train/350M-12000.yml')]\n", "NeoXArgs.configure_distributed_args() using world size: 1 and model-parallel size: 1 \n", "> building CodeGenTokenizer tokenizer ...\n", " > padded vocab (size: 50328) with 872 dummy tokens (new size: 51200)\n", "> initializing torch distributed ...\n", "[2024-06-02 06:51:01,636] [INFO] [distributed.py:36:init_distributed] Not using the DeepSpeed or torch.distributed launchers, attempting to detect MPI environment...\n", "[2024-06-02 06:51:01,754] [INFO] [distributed.py:83:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=1, master_addr=**************, master_port=6000\n", "[2024-06-02 06:51:01,755] [INFO] [distributed.py:46:init_distributed] Initializing torch distributed with backend: nccl\n", "> initializing model parallel with size 1\n", "MPU DP: [0]\n", "MPU PP: [0]\n", "MPU MP: [0]\n", "> setting random seeds to 1234 ...\n", "[2024-06-02 06:51:01,757] [INFO] [checkpointing.py:223:model_parallel_cuda_manual_seed] > initializing model parallel cuda seeds on global rank 0, model parallel rank 0, and data parallel rank 0 with model parallel seed: 3952 and data parallel seed: 1234\n", "make: Entering directory '/home/<USER>/src/augment/research/gpt-neox/megatron/data'\n", "make: Nothing to be done for 'default'.\n", "make: Leaving directory '/home/<USER>/src/augment/research/gpt-neox/megatron/data'\n", "building GPT2 model ...\n", "SEED_LAYERS=False BASE_SEED=1234 SEED_FN=None\n", "Using topology: {ProcessCoord(pipe=0, data=0, model=0): 0}\n", "[2024-06-02 06:51:01,799] [INFO] [module.py:363:_partition_layers] Partitioning pipeline stages with method type:transformer|mlp\n", "stage=0 layers=25\n", "     0: EmbeddingPipe\n", "     1: _pre_transformer_block\n", "     2: ParallelTransformerLayerPipe\n", "     3: ParallelTransformerLayerPipe\n", "     4: ParallelTransformerLayerPipe\n", "     5: ParallelTransformerLayerPipe\n", "     6: ParallelTransformerLayerPipe\n", "     7: ParallelTransformerLayerPipe\n", "     8: ParallelTransformerLayerPipe\n", "     9: ParallelTransformerLayerPipe\n", "    10: ParallelTransformerLayerPipe\n", "    11: ParallelTransformerLayerPipe\n", "    12: ParallelTransformerLayerPipe\n", "    13: ParallelTransformerLayerPipe\n", "    14: ParallelTransformerLayerPipe\n", "    15: ParallelTransformerLayerPipe\n", "    16: ParallelTransformerLayerPipe\n", "    17: ParallelTransformerLayerPipe\n", "    18: ParallelTransformerLayerPipe\n", "    19: ParallelTransformerLayerPipe\n", "    20: ParallelTransformerLayerPipe\n", "    21: ParallelTransformerLayerPipe\n", "    22: _post_transformer_block\n", "    23: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    24: ContrastiveRetrievalHead\n", "  loss: contrastive_loss\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[dxy-8-a40:89754] mca_base_component_repository_open: unable to open mca_op_avx: /usr/local/openmpi-4.1.0/lib/openmpi/mca_op_avx.so: undefined symbol: ompi_op_base_module_t_class (ignored)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Parameters:\n", "    0.dummy requires_grad=True\n", "    0.word_embeddings.weight requires_grad=True\n", "    2.input_layernorm.weight requires_grad=True\n", "    2.input_layernorm.bias requires_grad=True\n", "    2.attention.query_key_value.weight requires_grad=True\n", "    2.attention.query_key_value.bias requires_grad=True\n", "    2.attention.dense.weight requires_grad=True\n", "    2.attention.dense.bias requires_grad=True\n", "    2.mlp.dense_h_to_4h.weight requires_grad=True\n", "    2.mlp.dense_h_to_4h.bias requires_grad=True\n", "    2.mlp.dense_4h_to_h.weight requires_grad=True\n", "    2.mlp.dense_4h_to_h.bias requires_grad=True\n", "    3.input_layernorm.weight requires_grad=True\n", "    3.input_layernorm.bias requires_grad=True\n", "    3.attention.query_key_value.weight requires_grad=True\n", "    3.attention.query_key_value.bias requires_grad=True\n", "    3.attention.dense.weight requires_grad=True\n", "    3.attention.dense.bias requires_grad=True\n", "    3.mlp.dense_h_to_4h.weight requires_grad=True\n", "    3.mlp.dense_h_to_4h.bias requires_grad=True\n", "    3.mlp.dense_4h_to_h.weight requires_grad=True\n", "    3.mlp.dense_4h_to_h.bias requires_grad=True\n", "    4.input_layernorm.weight requires_grad=True\n", "    4.input_layernorm.bias requires_grad=True\n", "    4.attention.query_key_value.weight requires_grad=True\n", "    4.attention.query_key_value.bias requires_grad=True\n", "    4.attention.dense.weight requires_grad=True\n", "    4.attention.dense.bias requires_grad=True\n", "    4.mlp.dense_h_to_4h.weight requires_grad=True\n", "    4.mlp.dense_h_to_4h.bias requires_grad=True\n", "    4.mlp.dense_4h_to_h.weight requires_grad=True\n", "    4.mlp.dense_4h_to_h.bias requires_grad=True\n", "    5.input_layernorm.weight requires_grad=True\n", "    5.input_layernorm.bias requires_grad=True\n", "    5.attention.query_key_value.weight requires_grad=True\n", "    5.attention.query_key_value.bias requires_grad=True\n", "    5.attention.dense.weight requires_grad=True\n", "    5.attention.dense.bias requires_grad=True\n", "    5.mlp.dense_h_to_4h.weight requires_grad=True\n", "    5.mlp.dense_h_to_4h.bias requires_grad=True\n", "    5.mlp.dense_4h_to_h.weight requires_grad=True\n", "    5.mlp.dense_4h_to_h.bias requires_grad=True\n", "    6.input_layernorm.weight requires_grad=True\n", "    6.input_layernorm.bias requires_grad=True\n", "    6.attention.query_key_value.weight requires_grad=True\n", "    6.attention.query_key_value.bias requires_grad=True\n", "    6.attention.dense.weight requires_grad=True\n", "    6.attention.dense.bias requires_grad=True\n", "    6.mlp.dense_h_to_4h.weight requires_grad=True\n", "    6.mlp.dense_h_to_4h.bias requires_grad=True\n", "    6.mlp.dense_4h_to_h.weight requires_grad=True\n", "    6.mlp.dense_4h_to_h.bias requires_grad=True\n", "    7.input_layernorm.weight requires_grad=True\n", "    7.input_layernorm.bias requires_grad=True\n", "    7.attention.query_key_value.weight requires_grad=True\n", "    7.attention.query_key_value.bias requires_grad=True\n", "    7.attention.dense.weight requires_grad=True\n", "    7.attention.dense.bias requires_grad=True\n", "    7.mlp.dense_h_to_4h.weight requires_grad=True\n", "    7.mlp.dense_h_to_4h.bias requires_grad=True\n", "    7.mlp.dense_4h_to_h.weight requires_grad=True\n", "    7.mlp.dense_4h_to_h.bias requires_grad=True\n", "    8.input_layernorm.weight requires_grad=True\n", "    8.input_layernorm.bias requires_grad=True\n", "    8.attention.query_key_value.weight requires_grad=True\n", "    8.attention.query_key_value.bias requires_grad=True\n", "    8.attention.dense.weight requires_grad=True\n", "    8.attention.dense.bias requires_grad=True\n", "    8.mlp.dense_h_to_4h.weight requires_grad=True\n", "    8.mlp.dense_h_to_4h.bias requires_grad=True\n", "    8.mlp.dense_4h_to_h.weight requires_grad=True\n", "    8.mlp.dense_4h_to_h.bias requires_grad=True\n", "    9.input_layernorm.weight requires_grad=True\n", "    9.input_layernorm.bias requires_grad=True\n", "    9.attention.query_key_value.weight requires_grad=True\n", "    9.attention.query_key_value.bias requires_grad=True\n", "    9.attention.dense.weight requires_grad=True\n", "    9.attention.dense.bias requires_grad=True\n", "    9.mlp.dense_h_to_4h.weight requires_grad=True\n", "    9.mlp.dense_h_to_4h.bias requires_grad=True\n", "    9.mlp.dense_4h_to_h.weight requires_grad=True\n", "    9.mlp.dense_4h_to_h.bias requires_grad=True\n", "    10.input_layernorm.weight requires_grad=True\n", "    10.input_layernorm.bias requires_grad=True\n", "    10.attention.query_key_value.weight requires_grad=True\n", "    10.attention.query_key_value.bias requires_grad=True\n", "    10.attention.dense.weight requires_grad=True\n", "    10.attention.dense.bias requires_grad=True\n", "    10.mlp.dense_h_to_4h.weight requires_grad=True\n", "    10.mlp.dense_h_to_4h.bias requires_grad=True\n", "    10.mlp.dense_4h_to_h.weight requires_grad=True\n", "    10.mlp.dense_4h_to_h.bias requires_grad=True\n", "    11.input_layernorm.weight requires_grad=True\n", "    11.input_layernorm.bias requires_grad=True\n", "    11.attention.query_key_value.weight requires_grad=True\n", "    11.attention.query_key_value.bias requires_grad=True\n", "    11.attention.dense.weight requires_grad=True\n", "    11.attention.dense.bias requires_grad=True\n", "    11.mlp.dense_h_to_4h.weight requires_grad=True\n", "    11.mlp.dense_h_to_4h.bias requires_grad=True\n", "    11.mlp.dense_4h_to_h.weight requires_grad=True\n", "    11.mlp.dense_4h_to_h.bias requires_grad=True\n", "    12.input_layernorm.weight requires_grad=True\n", "    12.input_layernorm.bias requires_grad=True\n", "    12.attention.query_key_value.weight requires_grad=True\n", "    12.attention.query_key_value.bias requires_grad=True\n", "    12.attention.dense.weight requires_grad=True\n", "    12.attention.dense.bias requires_grad=True\n", "    12.mlp.dense_h_to_4h.weight requires_grad=True\n", "    12.mlp.dense_h_to_4h.bias requires_grad=True\n", "    12.mlp.dense_4h_to_h.weight requires_grad=True\n", "    12.mlp.dense_4h_to_h.bias requires_grad=True\n", "    13.input_layernorm.weight requires_grad=True\n", "    13.input_layernorm.bias requires_grad=True\n", "    13.attention.query_key_value.weight requires_grad=True\n", "    13.attention.query_key_value.bias requires_grad=True\n", "    13.attention.dense.weight requires_grad=True\n", "    13.attention.dense.bias requires_grad=True\n", "    13.mlp.dense_h_to_4h.weight requires_grad=True\n", "    13.mlp.dense_h_to_4h.bias requires_grad=True\n", "    13.mlp.dense_4h_to_h.weight requires_grad=True\n", "    13.mlp.dense_4h_to_h.bias requires_grad=True\n", "    14.input_layernorm.weight requires_grad=True\n", "    14.input_layernorm.bias requires_grad=True\n", "    14.attention.query_key_value.weight requires_grad=True\n", "    14.attention.query_key_value.bias requires_grad=True\n", "    14.attention.dense.weight requires_grad=True\n", "    14.attention.dense.bias requires_grad=True\n", "    14.mlp.dense_h_to_4h.weight requires_grad=True\n", "    14.mlp.dense_h_to_4h.bias requires_grad=True\n", "    14.mlp.dense_4h_to_h.weight requires_grad=True\n", "    14.mlp.dense_4h_to_h.bias requires_grad=True\n", "    15.input_layernorm.weight requires_grad=True\n", "    15.input_layernorm.bias requires_grad=True\n", "    15.attention.query_key_value.weight requires_grad=True\n", "    15.attention.query_key_value.bias requires_grad=True\n", "    15.attention.dense.weight requires_grad=True\n", "    15.attention.dense.bias requires_grad=True\n", "    15.mlp.dense_h_to_4h.weight requires_grad=True\n", "    15.mlp.dense_h_to_4h.bias requires_grad=True\n", "    15.mlp.dense_4h_to_h.weight requires_grad=True\n", "    15.mlp.dense_4h_to_h.bias requires_grad=True\n", "    16.input_layernorm.weight requires_grad=True\n", "    16.input_layernorm.bias requires_grad=True\n", "    16.attention.query_key_value.weight requires_grad=True\n", "    16.attention.query_key_value.bias requires_grad=True\n", "    16.attention.dense.weight requires_grad=True\n", "    16.attention.dense.bias requires_grad=True\n", "    16.mlp.dense_h_to_4h.weight requires_grad=True\n", "    16.mlp.dense_h_to_4h.bias requires_grad=True\n", "    16.mlp.dense_4h_to_h.weight requires_grad=True\n", "    16.mlp.dense_4h_to_h.bias requires_grad=True\n", "    17.input_layernorm.weight requires_grad=True\n", "    17.input_layernorm.bias requires_grad=True\n", "    17.attention.query_key_value.weight requires_grad=True\n", "    17.attention.query_key_value.bias requires_grad=True\n", "    17.attention.dense.weight requires_grad=True\n", "    17.attention.dense.bias requires_grad=True\n", "    17.mlp.dense_h_to_4h.weight requires_grad=True\n", "    17.mlp.dense_h_to_4h.bias requires_grad=True\n", "    17.mlp.dense_4h_to_h.weight requires_grad=True\n", "    17.mlp.dense_4h_to_h.bias requires_grad=True\n", "    18.input_layernorm.weight requires_grad=True\n", "    18.input_layernorm.bias requires_grad=True\n", "    18.attention.query_key_value.weight requires_grad=True\n", "    18.attention.query_key_value.bias requires_grad=True\n", "    18.attention.dense.weight requires_grad=True\n", "    18.attention.dense.bias requires_grad=True\n", "    18.mlp.dense_h_to_4h.weight requires_grad=True\n", "    18.mlp.dense_h_to_4h.bias requires_grad=True\n", "    18.mlp.dense_4h_to_h.weight requires_grad=True\n", "    18.mlp.dense_4h_to_h.bias requires_grad=True\n", "    19.input_layernorm.weight requires_grad=True\n", "    19.input_layernorm.bias requires_grad=True\n", "    19.attention.query_key_value.weight requires_grad=True\n", "    19.attention.query_key_value.bias requires_grad=True\n", "    19.attention.dense.weight requires_grad=True\n", "    19.attention.dense.bias requires_grad=True\n", "    19.mlp.dense_h_to_4h.weight requires_grad=True\n", "    19.mlp.dense_h_to_4h.bias requires_grad=True\n", "    19.mlp.dense_4h_to_h.weight requires_grad=True\n", "    19.mlp.dense_4h_to_h.bias requires_grad=True\n", "    20.input_layernorm.weight requires_grad=True\n", "    20.input_layernorm.bias requires_grad=True\n", "    20.attention.query_key_value.weight requires_grad=True\n", "    20.attention.query_key_value.bias requires_grad=True\n", "    20.attention.dense.weight requires_grad=True\n", "    20.attention.dense.bias requires_grad=True\n", "    20.mlp.dense_h_to_4h.weight requires_grad=True\n", "    20.mlp.dense_h_to_4h.bias requires_grad=True\n", "    20.mlp.dense_4h_to_h.weight requires_grad=True\n", "    20.mlp.dense_4h_to_h.bias requires_grad=True\n", "    21.input_layernorm.weight requires_grad=True\n", "    21.input_layernorm.bias requires_grad=True\n", "    21.attention.query_key_value.weight requires_grad=True\n", "    21.attention.query_key_value.bias requires_grad=True\n", "    21.attention.dense.weight requires_grad=True\n", "    21.attention.dense.bias requires_grad=True\n", "    21.mlp.dense_h_to_4h.weight requires_grad=True\n", "    21.mlp.dense_h_to_4h.bias requires_grad=True\n", "    21.mlp.dense_4h_to_h.weight requires_grad=True\n", "    21.mlp.dense_4h_to_h.bias requires_grad=True\n", "    23.norm.weight requires_grad=True\n", "    23.norm.bias requires_grad=True\n", "    24.log_logit_scale requires_grad=True\n", "DeepSpeed is enabled.\n", "[2024-06-02 06:51:02,207] [INFO] [logging.py:60:log_dist] [Rank 0] DeepSpeed info: version=0.3.15+40617c2, git-hash=40617c2, git-branch=HEAD\n", "[2024-06-02 06:51:02,208] [WARNING] [config.py:77:_sanity_check] DeepSpeedConfig: cpu_offload is deprecated. Please use offload_optimizer.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/usr/local/local_user_base/lib/python3.11/site-packages/torch/distributed/distributed_c10d.py:761: UserWarning: torch.distributed.distributed_c10d._get_global_rank is deprecated please use torch.distributed.distributed_c10d.get_global_rank instead\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[2024-06-02 06:51:03,159] [INFO] [config.py:759:print] DeepSpeedEngine configuration:\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   activation_checkpointing_config  {\n", "    \"partition_activations\": false, \n", "    \"contiguous_memory_optimization\": false, \n", "    \"cpu_checkpointing\": false, \n", "    \"number_checkpoints\": null, \n", "    \"synchronize_checkpoint_boundary\": false, \n", "    \"profile\": false\n", "}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   allreduce_always_fp32 ........ False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   amp_enabled .................. False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   amp_params ................... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   checkpoint_tag_validation_enabled  True\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   checkpoint_tag_validation_fail  False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   disable_allgather ............ False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   dump_state ................... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   dynamic_loss_scale_args ...... {'init_scale': 4096, 'scale_window': 1000, 'delayed_shift': 2, 'min_scale': 1}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   elasticity_enabled ........... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   flops_profiler_config ........ {\n", "    \"enabled\": false, \n", "    \"profile_step\": 1, \n", "    \"module_depth\": -1, \n", "    \"top_modules\": 3, \n", "    \"detailed\": true\n", "}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   fp16_enabled ................. True\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   fp16_type .................... fp16\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   global_rank .................. 0\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   gradient_accumulation_steps .. 1\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   gradient_clipping ............ 1.0\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   gradient_predivide_factor .... 1.0\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   initial_dynamic_scale ........ 4096\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   loss_scale ................... 0\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   memory_breakdown ............. False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   optimizer_legacy_fusion ...... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   optimizer_name ............... adam\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   optimizer_params ............. {'betas': [0.9, 0.95], 'eps': 1e-08, 'lr': 0.0003}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   pld_enabled .................. False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   pld_params ................... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   precision .................... torch.float16\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   prescale_gradients ........... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   scheduler_name ............... None\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   scheduler_params ............. None\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   sparse_attention ............. None\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   sparse_gradients_enabled ..... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   steps_per_print .............. 10\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   tensorboard_enabled .......... False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   tensorboard_job_name ......... DeepSpeedJobName\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   tensorboard_output_path ...... \n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   train_batch_size ............. 12\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   train_micro_batch_size_per_gpu  12\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   wall_clock_breakdown ......... True\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   world_size ................... 1\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   zero_allow_untested_optimizer  False\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   zero_config .................. {\n", "    \"stage\": 0, \n", "    \"contiguous_gradients\": false, \n", "    \"reduce_scatter\": true, \n", "    \"reduce_bucket_size\": 5.000000e+08, \n", "    \"allgather_partitions\": true, \n", "    \"allgather_bucket_size\": 5.000000e+08, \n", "    \"overlap_comm\": false, \n", "    \"load_from_fp32_weights\": true, \n", "    \"elastic_checkpoint\": false, \n", "    \"offload_param\": null, \n", "    \"offload_optimizer\": null, \n", "    \"sub_group_size\": 1.000000e+12, \n", "    \"prefetch_bucket_size\": 5.000000e+07, \n", "    \"param_persistence_threshold\": 1.000000e+05, \n", "    \"max_live_parameters\": 1.000000e+09, \n", "    \"max_reuse_distance\": 1.000000e+09, \n", "    \"gather_fp16_weights_on_model_save\": false\n", "}\n", "[2024-06-02 06:51:03,160] [INFO] [config.py:763:print]   zero_enabled ................. False\n", "[2024-06-02 06:51:03,161] [INFO] [config.py:763:print]   zero_optimization_stage ...... 0\n", "[2024-06-02 06:51:03,161] [INFO] [config.py:765:print]   json = {\n", "    \"train_batch_size\": 12, \n", "    \"train_micro_batch_size_per_gpu\": 12, \n", "    \"optimizer\": {\n", "        \"params\": {\n", "            \"betas\": [0.9, 0.95], \n", "            \"eps\": 1e-08, \n", "            \"lr\": 0.0003\n", "        }, \n", "        \"type\": \"Adam\"\n", "    }, \n", "    \"fp16\": {\n", "        \"enabled\": true, \n", "        \"hysteresis\": 2, \n", "        \"initial_scale_power\": 12, \n", "        \"loss_scale\": 0, \n", "        \"loss_scale_window\": 1000, \n", "        \"min_loss_scale\": 1\n", "    }, \n", "    \"gradient_clipping\": 1.0, \n", "    \"zero_optimization\": {\n", "        \"stage\": 0, \n", "        \"allgather_partitions\": true, \n", "        \"allgather_bucket_size\": 5.000000e+08, \n", "        \"overlap_comm\": false, \n", "        \"reduce_scatter\": true, \n", "        \"reduce_bucket_size\": 5.000000e+08, \n", "        \"contiguous_gradients\": false, \n", "        \"cpu_offload\": false, \n", "        \"elastic_checkpoint\": false\n", "    }, \n", "    \"wall_clock_breakdown\": true\n", "}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Using /home/<USER>/.cache/torch_extensions/py311_cu121 as PyTorch extensions root...\n", "Emitting ninja build file /home/<USER>/.cache/torch_extensions/py311_cu121/utils/build.ninja...\n", "Building extension module utils...\n", "Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "Loading extension module utils...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ninja: no work to do.\n", "Time to load utils op: 0.09398055076599121 seconds\n", "[2024-06-02 06:51:03,997] [INFO] [engine.py:84:__init__] CONFIG: micro_batches=1 micro_batch_size=12\n", "[2024-06-02 06:51:04,045] [INFO] [engine.py:141:__init__] RANK=0 STAGE=0 LAYERS=25 [0, 25) STAGE_PARAMS=304314370 (304.314M) TOTAL_PARAMS=304314370 (304.314M) UNIQUE_PARAMS=304314370 (304.314M)\n", " > number of parameters on model parallel rank 0: 304314370\n", "Warning: did not find final_linear layer, cannot calculate embedding params\n", " > total params: 304,314,370\n", " > embedding params: 0\n", "Loading: /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1\n", "[2024-06-02 06:51:04,084] [INFO] [engine.py:1555:_load_checkpoint] rank: 0 loading checkpoint: /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/mp_rank_00_model_states.pt\n", "[2024-06-02 06:51:04,263] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=0 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_00-model_00-model_states.pt\n", "[2024-06-02 06:51:04,319] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=2 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_02-model_00-model_states.pt\n", "[2024-06-02 06:51:04,363] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=3 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_03-model_00-model_states.pt\n", "[2024-06-02 06:51:04,407] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=4 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_04-model_00-model_states.pt\n", "[2024-06-02 06:51:04,454] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=5 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_05-model_00-model_states.pt\n", "[2024-06-02 06:51:04,498] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=6 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_06-model_00-model_states.pt\n", "[2024-06-02 06:51:04,542] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=7 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_07-model_00-model_states.pt\n", "[2024-06-02 06:51:04,585] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=8 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_08-model_00-model_states.pt\n", "[2024-06-02 06:51:04,628] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=9 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_09-model_00-model_states.pt\n", "[2024-06-02 06:51:04,665] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=10 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_10-model_00-model_states.pt\n", "[2024-06-02 06:51:04,710] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=11 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_11-model_00-model_states.pt\n", "[2024-06-02 06:51:04,744] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=12 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_12-model_00-model_states.pt\n", "[2024-06-02 06:51:04,793] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=13 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_13-model_00-model_states.pt\n", "[2024-06-02 06:51:04,836] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=14 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_14-model_00-model_states.pt\n", "[2024-06-02 06:51:04,887] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=15 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_15-model_00-model_states.pt\n", "[2024-06-02 06:51:04,919] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=16 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_16-model_00-model_states.pt\n", "[2024-06-02 06:51:04,951] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=17 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_17-model_00-model_states.pt\n", "[2024-06-02 06:51:04,996] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=18 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_18-model_00-model_states.pt\n", "[2024-06-02 06:51:05,048] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=19 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_19-model_00-model_states.pt\n", "[2024-06-02 06:51:05,078] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=20 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_20-model_00-model_states.pt\n", "[2024-06-02 06:51:05,108] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=21 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_21-model_00-model_states.pt\n", "[2024-06-02 06:51:05,109] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=23 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_23-model_00-model_states.pt\n", "[2024-06-02 06:51:05,109] [INFO] [module.py:576:load_state_dir] RANK=0 Loaded layer=24 file=/mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/layer_24-model_00-model_states.pt\n", "checkpoint_name: /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/mp_rank_00_model_states.pt\n", " > validated currently set args with arguments in the checkpoint ...\n", "  successfully loaded /mnt/efs/augment/checkpoints/ethanol/ethanol6-16.1/global_step2000/mp_rank_00_model_states.pt\n", "Loading checkpoint and starting from iteration 0\n"]}], "source": ["system.load()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.basic_RAG_system:Adding 190 documents.\n", "Indexing:   0%|          | 0/190 [00:00<?, ?doc/s]/home/<USER>/src/augment/research/gpt-neox/megatron/mpu/data.py:51: UserWarning: The torch.cuda.*DtypeTensor constructors are no longer recommended. It's best to use methods such as torch.tensor(data, dtype=*, device='cuda') to create tensors. (Triggered internally at ../torch/csrc/tensor/python_tensor.cpp:83.)\n", "  sizes_cuda = torch.cuda.LongTensor(sizes)\n", "Indexing: 100%|██████████| 190/190 [00:23<00:00,  8.04doc/s]\n", "INFO:research.eval.harness.systems.basic_RAG_system:There are now 190 total docs.\n", "WARNING:research.eval.harness.systems.basic_RAG_system:No doc_ids provided, using all doc_ids.\n", "/usr/local/local_user_base/lib/python3.11/site-packages/torch/overrides.py:110: UserWarning: 'has_cuda' is deprecated, please use 'torch.backends.cuda.is_built()'\n", "  torch.has_cuda,\n", "/usr/local/local_user_base/lib/python3.11/site-packages/torch/overrides.py:111: UserWarning: 'has_cudnn' is deprecated, please use 'torch.backends.cudnn.is_available()'\n", "  torch.has_cudnn,\n", "/usr/local/local_user_base/lib/python3.11/site-packages/torch/overrides.py:117: UserWarning: 'has_mps' is deprecated, please use 'torch.backends.mps.is_built()'\n", "  torch.has_mps,\n", "/usr/local/local_user_base/lib/python3.11/site-packages/torch/overrides.py:118: UserWarning: 'has_mkldnn' is deprecated, please use 'torch.backends.mkldnn.is_available()'\n", "  torch.has_mkldnn,\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'metric@is-syntax-correct': True, 'metric@text-similarity': 0.4852817128825721, 'metric@token-accuracy': 0.6875, 'metric@log-likelihood': -1.1357742547988892}\n"]}], "source": ["index = 512\n", "xdata = task[index]\n", "system.clear_retriever()\n", "system.add_docs(xdata.repository)\n", "\n", "\n", "completion = system.generate(xdata.model_input)\n", "forward_metrics = metrics.safe_forward_metrics(system, xdata.model_input, completion)\n", "prompt = system.get_model().tokenizer.detokenize(completion.prompt_tokens)\n", "results = task.metric(\n", "    xdata.model_input,\n", "    xdata.patch,\n", "    tag=xdata.tag,\n", "    image_name=xdata.image_name,\n", "    completion=completion.generated_text,\n", "    driver=driver,\n", "    forward_metrics=forward_metrics,\n", ")\n", "print(results)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}