{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "from research.data.spark.utils import AugmentK8sSparkSession\n", "\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"64G\",\n", "    \"spark.executor.memory\": \"32G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "    \"spark.task.cpus\": \"2\",\n", "}\n", "\n", "spark: AugmentK8sSparkSession = k8s_session(\n", "    max_workers=512,\n", "    conf=spark_conf,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_all = spark.read.parquet(\"s3a://dxy-dev-bucket/ragdata/limit-repo-20000-v2/fims/\")\n", "data_1file = spark.read.parquet(\n", "    \"s3a://dxy-dev-bucket/ragdata/limit-repo-20000-v2/fims/part-00395-21e639d5-db6f-4964-8fa3-13e2cb7ae82e-c000.zstd.parquet\"\n", ")\n", "print(f\"There are {data_all.count()} rows in data_all.\")\n", "print(f\"There are {data_1file.count()} rows in data_1file.\")\n", "batch_data = data_1file.toPandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.rag import rogue_stages\n", "from experimental.dxy.rag.exps.utils_rag import _repo_with_fim_to_retrieval_fn\n", "\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\",\n", "]\n", "RETRIEVAL_LANGUAGES = (\n", "    REPO_LANGUAGES + [\"sql\", \"markdown\"] + additional_retrieval_languages\n", ")\n", "config_retrieval = rogue_stages.RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=40,\n", "    scorer_config={\n", "        \"name\": \"ethanol\",\n", "        \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "    },\n", "    chunker_config={\n", "        \"name\": \"line_level\",\n", "        \"max_lines_per_chunk\": 30,\n", "        \"include_scope_annotation\": <PERSON>als<PERSON>,\n", "    },\n", "    query_config={\n", "        \"name\": \"ethanol6_query\",\n", "        \"max_tokens\": 1023,\n", "        \"add_path\": True,\n", "    },\n", "    document_config={\n", "        \"name\": \"ethanol6_document\",\n", "        \"max_tokens\": 999,\n", "        \"add_path\": True,\n", "    },\n", "    random_seed=74912,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_log\n", "\n", "all_samples = []\n", "for index, sample in enumerate(_repo_with_fim_to_retrieval_fn(batch_data, config_retrieval)):\n", "    all_samples.append(sample)\n", "    print(f\"{utils_for_log.time_string()}: processed {index} sample.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# batch_data.head()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}