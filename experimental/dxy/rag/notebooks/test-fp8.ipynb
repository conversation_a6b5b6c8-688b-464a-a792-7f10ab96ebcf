{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "class FP8Linear(nn.Module):\n", "    \"\"\"An FP8 version of nn.Linear.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        in_features: int = 5,\n", "        out_features: int = 10,\n", "        device: torch.device | str = \"cuda\",\n", "        dtype: torch.dtype = torch.float16,\n", "    ):\n", "        super().__init__()\n", "        assert (\n", "            in_features % 16 == 0\n", "        ), \"FP8 multiplication requires in features be a multiple of 16.\"\n", "        assert (\n", "            out_features % 8 == 0\n", "        ), \"FP8 multiplication requires out features be a multiple of 8.\"\n", "        self.in_features = in_features\n", "        self.out_features = out_features\n", "\n", "        self.weight = nn.Parameter(\n", "            torch.randint(low=0, high=100, size=(out_features, in_features), dtype=torch.uint8, device=device),\n", "            requires_grad=False,\n", "        )"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OrderedDict([('weight', tensor([[87, 58, 35, 43, 19, 73, 55, 30, 11, 15, 70, 72, 15, 99, 69,  2],\n", "        [10, 40, 61, 38, 23,  5, 97, 52, 58, 15, 68, 37, 92, 13, 13,  2],\n", "        [ 6, 26, 17, 81, 91, 58, 31,  7,  6, 86, 94, 59, 37,  4, 95, 72],\n", "        [94, 51, 50, 87, 99, 77, 79, 75, 19, 28, 25,  7, 90, 31, 18, 91],\n", "        [25, 82, 19, 89, 99, 71, 57, 28, 29, 82,  0, 83, 80, 17, 16, 65],\n", "        [90, 77, 83, 21, 58, 45, 96, 19, 51, 37, 48, 58,  8, 24,  4, 90],\n", "        [15, 13, 69, 32, 12, 97, 52, 31, 66,  5, 47,  0, 74, 27, 66, 72],\n", "        [89, 26, 45, 69, 11, 83, 62, 21, 73, 56, 89, 77, 11, 44, 98, 75]],\n", "       device='cuda:0', dtype=torch.uint8))])\n"]}, {"data": {"text/plain": ["<All keys matched successfully>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["x = FP8Linear(in_features=16, out_features=8)\n", "state_dict = x.state_dict()\n", "print(state_dict)\n", "\n", "new_state_dict = {\"weight\": state_dict[\"weight\"].to(torch.bfloat16)}\n", "x.load_state_dict(new_state_dict, strict=True)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parameter containing:\n", "tensor([[23, 99, 40, 65, 15, 64, 32, 41,  5, 48, 91, 80, 91, 56, 68, 70],\n", "        [62, 22, 42, 52, 66, 47, 44, 24, 59, 43, 84, 46, 56, 81, 62, 95],\n", "        [42,  9, 13, 67, 52, 42, 66, 21, 66,  9, 29, 87, 57, 19, 96,  4],\n", "        [93, 98, 51, 23, 11, 32, 85,  1,  7, 70, 52, 13, 72, 45, 43, 83],\n", "        [59, 10,  1, 55, 36, 50, 51, 60, 17, 63, 98, 67, 92, 62,  6, 46],\n", "        [28, 51, 69, 32, 99, 60, 48, 81, 45, 75, 74, 99, 26, 65, 57, 19],\n", "        [ 9, 94, 13, 12, 98,  8, 23, 33, 29, 66, 18, 47, 50, 65, 33, 39],\n", "        [16,  9, 90, 58,  2, 87,  6,  9, 12, 14, 10, 56, 21, 26, 67, 34]],\n", "       device='cuda:0', dtype=torch.uint8)\n"]}], "source": ["print(x.weight)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Max fp16: : 65504.0, min fp16: 6.103515625e-05\n", "Max bf16: : 3.3895313892515355e+38, min bf16: 1.1754943508222875e-38\n"]}], "source": ["import torch\n", "\n", "max_fp16 = torch.finfo(torch.float16).max\n", "min_fp16 = torch.finfo(torch.float16).tiny\n", "print(f\"Max fp16: : {max_fp16}, min fp16: {min_fp16}\")\n", "\n", "max_bf16 = torch.finfo(torch.bfloat16).max\n", "min_bf16 = torch.finfo(torch.bfloat16).tiny\n", "print(f\"Max bf16: : {max_bf16}, min bf16: {min_bf16}\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0031280517578125\n"]}], "source": ["x = torch.zeros(1, dtype=torch.bfloat16)\n", "x[:] = 0.00313\n", "print(x.item())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0046875\n", "0.00469970703125\n", "tensor([0.1504], dtype=torch.bfloat16)\n"]}], "source": ["x = torch.zeros(1, dtype=torch.bfloat16)\n", "print(0.15 / 32)\n", "x[:] = 0.15 / 32\n", "print(x.item())\n", "print(x * 32)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}