{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Follow research/notebooks/replay_requests.ipynb to setup environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.datasets.completion_dataset import CompletionDataset\n", "\n", "# == Configure your variables here ==\n", "# Which tenant to grab data from.\n", "TENANT_NAME = \"dogfood\"\n", "ids_to_replay = [\n", "    \"2497dfef-1460-473a-b0b7-eb86958bfb48\",\n", "]\n", "DATASET_FILTERS = CompletionDataset.Filters(request_ids=ids_to_replay)\n", "\n", "# Ordering by request id will effectively give you data in a random order.\n", "DATASET_ORDER_BY: CompletionDataset.OrderBy = \"request_id\"\n", "DATASET_LIMIT = 100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Actually get the dataset.\n", "from base.datasets import tenants\n", "from base.datasets.completion import CompletionDatum\n", "from research.utils.inspect_indexed_dataset import print_green, print_yellow\n", "\n", "dataset = CompletionDataset.create(\n", "    tenants.get_tenant(TENANT_NAME),\n", "    filters=DATASET_FILTERS,\n", "    order_by=DATASET_ORDER_BY,\n", "    limit=DATASET_LIMIT,\n", ")\n", "id_to_completion = {c.request_id: c for c in dataset.get_completions()}\n", "\n", "# sort the completions following the order of ids_to_show\n", "completions = list[CompletionDatum]()\n", "for req_id in ids_to_replay:\n", "    if req_id in id_to_completion:\n", "        completions.append(id_to_completion[req_id])\n", "    else:\n", "        print_yellow(f\"Request not found: {req_id}\")\n", "print_green(f\"Retrieved {len(completions)} completions.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.eval.harness.factories import create_system\n", "from experimental.dxy.rag.exps.launch_rag_eval import get_system_config\n", "\n", "system_config = get_system_config(\n", "    \"roguesl_4k\",\n", "    \"ethanol616\",\n", "    pathlib.Path(\"/mnt/efs/augment/checkpoints/dxy/sc2-rogue/7b-baseline-bs512s5k\"),\n", "    num_gpus=1,\n", ")\n", "system = create_system(system_config)\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.tools.replay_requests import to_model_input\n", "\n", "model_input = to_model_input(completions[0])\n", "response = system.generate(model_input)\n", "print(response.generated_text)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}