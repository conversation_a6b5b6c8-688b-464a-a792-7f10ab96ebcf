{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    # if str(relative_path) == \"tools/bazel_runner/review_edit_bot/test_data/test_file.txt\":\n", "    #     import pdb\n", "\n", "    #     pdb.set_trace()\n", "    if language is None:\n", "        continue\n", "    print(str(relative_path))\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", "    SiblingCorruptionNodesPicker,\n", ")\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "SAMPLE_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "# sampler = CSTFimSampler(\n", "#     pick_whole_node_rate=1.0,\n", "#     pick_extra_spaces_when_whole_node=0.0,\n", "#     corruption_nodes_picker=DefaultCorruptionNodesPicker(corruption_expansion_rate=0.4),\n", "# )\n", "# sampler = CSTFimSampler(\n", "#     pick_whole_node_rate=1.0,\n", "#     pick_extra_spaces_when_whole_node=0.0,\n", "#     corruption_nodes_picker=NullCorruptionNodesPicker(),\n", "# )\n", "sampler = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    corruption_nodes_picker=SiblingCorruptionNodesPicker(\n", "        corruption_expansion_rate=0.6,\n", "        corruption_all_available_siblings_rate=0.4,\n", "        any_required_key_word=(\"assert\",),\n", "    ),\n", ")\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 186 FIM problems.\n", "There are 186 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_retrieved_chunks_from_fim,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.exps.utils_fim import get_node_weight_assert_v1\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "# customized_get_node_weight = None\n", "customized_get_node_weight = get_node_weight_assert_v1\n", "\n", "fim_problems = generate_fim_samples_from_repo(\n", "    repo, config_fim, sampler, customized_get_node_weight\n", ")\n", "print(f\"There are {len(fim_problems)} FIM problems.\")\n", "\n", "samples = list(\n", "    generate_retrieved_chunks_from_fim(\n", "        repo,\n", "        fim_problems,\n", "        config=config_retrieval,\n", "        retrieval_database=NullRetrievalDatabase(),\n", "        tokenizer=tokenizer,\n", "    )\n", ")\n", "print(f\"There are {len(samples)} final samples.\")"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=eadb9f04-0a76-4e81-a5ca-5d0960c8f36b, last_request_id=214ab2bb-6e34-4f3c-93e1-8a7a521b361d)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='roguesl-v2-16b-seth616-rec', suggested_prefix_char_count=5184, suggested_suffix_char_count=5184, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='<PERSON>', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=5e00457f-357b-4fa0-be66-33f729c30987, last_request_id=24c4a690-9967-4d01-9fae-21ea7ad55f5d)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='star2sl-16b-seth616-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=57c5d9f1-5c2d-4d0f-9506-b210c5b04489, last_request_id=7bb34f2d-fa8c-454b-b7c1-c95da41f70da)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='elden-15b-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='Rust', vscode_name='rust', extensions=['.rs'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2 (Signature)\n"]}], "source": ["import copy\n", "from experimental.dxy.rag.exps.utils_system import build_model_input, load_all_systems\n", "from research.core.types import Document\n", "from termcolor import colored\n", "from research.data.rag.rogue_stages import RetrievalAugmentedSample\n", "from experimental.dxy.rag.exps.utils import shift_middle_to_prefix\n", "\n", "systems = load_all_systems()\n", "\n", "\n", "def compare_models(\n", "    systems: dict, sample: RetrievalAugmentedSample, doc_by_id: dict[str, Document], shift_middle: int = 0\n", "):\n", "    # show_truncated_completion(sample, 15, 5)\n", "    if shift_middle > 0:\n", "        sample = shift_middle_to_prefix(sample, shift_middle)\n", "    print(\"-\" * 32)\n", "    updated_doc_by_id = copy.deepcopy(doc_by_id)\n", "    file_path = sample[\"file_path\"]\n", "    updated_doc_by_id.pop(file_path)\n", "    updated_doc_by_id[file_path] = Document.new(\n", "        sample[\"prefix\"] + sample[\"suffix\"], sample[\"file_path\"]\n", "    )\n", "    updated_all_docs = list(updated_doc_by_id.values())\n", "    for name, system in systems.items():\n", "        print(f\"System: {name}\")\n", "        model_input = build_model_input(sample)\n", "        system.clear_retriever()\n", "        system.add_docs(updated_all_docs)\n", "        result = system.generate(model_input)\n", "        if result.extra_output and result.extra_output.additional_info and \"request_ids\" in result.extra_output.additional_info:\n", "            request_ids: list[str] = result.extra_output.additional_info[\"request_ids\"]\n", "        else:\n", "            request_ids: list[str] = []\n", "        for iq, request_id in enumerate(request_ids):\n", "            print(f\"Request ID {iq}: {request_id}\")\n", "        print(colored(result.generated_text, color=\"red\", on_color=\"on_white\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for idx, sample in enumerate(samples):\n", "#     if \"research/infra/lib/registry/blobpool_test.go\" in sample[\"file_path\"]:\n", "#         print(f\"index = {idx}\")\n", "#         show_truncated_completion(sample, 10, 10, no_special_token=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["I tested 12 examples (50% is incorrect):\n", "- 0 correct\n", "- 1 correct\n", "- 2 wrong\n", "- 3 wrong\n", "- 4 correct\n", "- 5 correct\n", "- 6 wrong\n", "- 7 correct\n", "- 8 wrong\n", "- 9 correct\n", "- 10 wrong\n", "- 138 wrong"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/infra/lib/registry/blobpool_test.go\n", "\n", "\u001b[92m\t})\n", "\tt.<PERSON>(\"tally-d0-1-wrongsize\", func(t *testing.T) {\n", "\t\tif err := bp1.<PERSON><PERSON>(\"d0\", 30, 1); err == nil {\n", "\t\t\t<PERSON><PERSON>(\"<PERSON><PERSON>() err: got nil, want an error.\")\n", "\t\t} else if got, want := err.<PERSON><PERSON><PERSON>(), \"size mismatch 30 != 3: cannot tally d0\"; got != want {\n", "\t\t\t<PERSON><PERSON>(\"UnTally() err: got %v, want %v.\", got, want)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 1, 3, map[string]int{\"d0\": 3})\n", "\t})\n", "\t\u001b[0m\u001b[47m\u001b[30mt.Run(\"untally-d0-1-wrongsize\", func(t *testing.T) {\n", "\t\tif err := bp1.UnTally(\"d0\", 30, 1); err == nil {\n", "\t\t\t<PERSON><PERSON>(\"UnTally() err: got nil, want an error.\")\n", "\t\t} else if got, want := err.<PERSON><PERSON><PERSON>(), \"size mismatch 30 != 3: cannot untally d0\"; got != want {\n", "\t\t\t<PERSON><PERSON>(\"UnTally() err: got %v, want %v.\", got, want)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 1, 3, map[string]int{\"d0\": 3})\n", "\t})\n", "\tt.<PERSON>(\"tally-d1-10\", func(t *testing.T) {\n", "\t\tif err := bp1.<PERSON><PERSON>(\"d1\", 42, 10); err != nil {\n", "\t\t\t<PERSON><PERSON>(\"Tall<PERSON>() err: got %v, want nil.\", err)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 2, 45, map[string]int{\"d0\": 3, \"d1\": 10})\n", "\t})\n", "\n", "\tt.<PERSON>(\"tally-another-pool\", func(t *testing.T) {\n", "\t\tbp2 := NewBlobPool()\n", "\t\tbp2.<PERSON><PERSON>(\"d1\", 42, 20)\n", "\t\tbp2.<PERSON><PERSON>(\"d2\", 7, 11)\n", "\t\tif err := bp1.Tally<PERSON>ool(bp2); err != nil {\n", "\t\t\t<PERSON><PERSON>(\"TallyPool() err: got %v, want nil.\", err)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 3, 52, map[string]int{\"d0\": 3, \"d1\": 30, \"d2\": 11})\n", "\t})\u001b[0m\u001b[94m\n", "\tt.<PERSON>(\"untally-another-pool\", func(t *testing.T) {\n", "\t\tbp3 := NewBlobPool()\n", "\t\tbp3.<PERSON><PERSON>(\"d2\", 7, 11)\n", "\t\tif err := bp1.UnTallyPool(bp3); err != nil {\n", "\t\t\t<PERSON><PERSON>(\"UnTallyPool() err: got %v, want nil.\", err)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 3, 52, map[string]int{\"d0\": 3, \"d1\": 30, \"d2\": 0})\n", "\t})\n", "\tt.<PERSON>(\"orphans\", func(t *testing.T) {\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 8421e3fd-5e81-40d2-bc28-71ecc139e577\n", "Request ID 1: 36493e45-bba9-4ff0-beab-cbcc1f0faeda\n", "Request ID 2: 97af2760-f0ce-4f13-8b42-ebbffc5bcadb\n", "Request ID 3: e6883189-7d36-4b41-9a04-f368c007a713\n", "\u001b[107m\u001b[31mt.Run(\"tally-another-pool\", func(t *testing.T) {\n", "\t\tbp2 := NewBlobPool()\n", "\t\tbp2.<PERSON><PERSON>(\"d1\", 30, 1)\n", "\t\tif err := bp1.Tally<PERSON>ool(bp2); err != nil {\n", "\t\t\t<PERSON><PERSON>(\"TallyPool() err: got %v, want nil.\", err)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 3, 52, map[string]int{\"d0\": 3, \"d1\": 1})\n", "\t})\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.5 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: ea879732-884b-4d70-82d9-44f1e83a3f6f\n", "Request ID 1: e1bfcc99-a8a4-4cd9-963e-b11ecdd778f5\n", "Request ID 2: 5ace17a5-e9be-48ea-a925-87cdb11d72b1\n", "\u001b[107m\u001b[31mt.Run(\"tally-d1-30\", func(t *testing.T) {\n", "\t\tif err := bp1.<PERSON><PERSON>(\"d1\", 7, 30); err != nil {\n", "\t\t\t<PERSON><PERSON>(\"Tall<PERSON>() err: got %v, want nil.\", err)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 2, 40, map[string]int{\"d0\": 3, \"d1\": 30})\n", "\t})\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.2 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: ba7845b0-57a3-4b25-8873-5b758181991e\n", "Request ID 1: d3078a99-aeda-4610-b3e4-4ac396fa0bc8\n", "Request ID 2: be778cf2-6be4-4c03-a58a-cb1179d8b352\n", "Request ID 3: 238dd038-ed41-410b-ae95-ddc7f1670769\n", "Request ID 4: 1d93ec03-02ee-4ec3-89a6-f3b47bde48f7\n", "Request ID 5: 1324d543-d3c0-4282-ab0c-69a21f4be3d0\n", "Request ID 6: 8c72095f-c959-4ef6-bf1c-39bd514426d6\n", "\u001b[107m\u001b[31mt.Run(\"untally-d0-3-wrongsize\", func(t *testing.T) {\n", "\t\tif err := bp1.UnTally(\"d0\", 30, 3); err == nil {\n", "\t\t\t<PERSON><PERSON>(\"UnTally() err: got nil, want an error.\")\n", "\t\t} else if got, want := err.<PERSON><PERSON><PERSON>(), \"size mismatch 30 != 3: cannot untally d0\"; got != want {\n", "\t\t\t<PERSON><PERSON>(\"UnTally() err: got %v, want %v.\", got, want)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 1, 3, map[string]int{\"d0\": 3})\n", "\t})\n", "\tt.<PERSON>(\"tally-d1-30\", func(t *testing.T) {\n", "\t\tif err := bp1.<PERSON><PERSON>(\"d1\", 30, 30); err != nil {\n", "\t\t\t<PERSON><PERSON>(\"Tall<PERSON>() err: got %v, want nil.\", err)\n", "\t\t}\n", "\t\tassertBP(t, bp1, 2, 33, map[string]int{\"d0\": 3, \"d1\": 30})\n", "\t})\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[138], 10, 10)\n", "compare_models(systems, samples[138], doc_by_id)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: tools/genie/expiration/expiration_watcher_test.py\n", "\n", "\u001b[92m    with patch(\n", "        \"tools.genie.expiration.expiration_watcher._now\",\n", "        lambda: datetime(2024, 1, 1, 0, 0, 0, tzinfo=timezone.utc),\n", "    ):\n", "        yield\n", "\n", "\n", "def test_is_expired(mock_now):\n", "    assert _is_expired(\"2023-12-31T23:59:59Z\")\n", "    \u001b[0m\u001b[47m\u001b[30massert _is_expired(\"2024-01-01T00:00:00Z\")\n", "    assert not _is_expired(\"2024-01-01T00:00:01Z\")\u001b[0m\u001b[94m\n", "\n", "\n", "def test_role_binding_expiration(mock_now):\n", "    mock_rbac_client = MagicMock()\n", "    expiration_watcher = ExpirationWatcher(\n", "        watch_interval_seconds=60, rbac_client=mock_rbac_client\n", "    )\n", "\n", "    # Test that a RoleBidning is not deleted if it is not expired.\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 9.0 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 82c32a22-dff4-43eb-802a-a579180a52a5\n", "Request ID 1: cc28c65f-2a7f-4602-bd2b-eab08edd091f\n", "\u001b[107m\u001b[31massert not _is_expired(\"2024-01-01T00:00:01Z\")\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 86975d01-7b8e-4d03-a9b3-05d8c5c3365b\n", "Request ID 1: 8bb47a6a-9c40-4827-8137-c2f75076f12d\n", "\u001b[107m\u001b[31massert not _is_expired(\"2024-01-01T00:00:01Z\")\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.5 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 1df54913-c334-4190-971d-e6f7b3e5cce7\n", "Request ID 1: d504ebb7-77ef-4012-96dc-5dc025cf54a5\n", "\u001b[107m\u001b[31massert not _is_expired(\"2024-01-01T00:00:01Z\")\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[0], 10, 10)\n", "compare_models(systems, samples[0], doc_by_id)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/ci/notify_slack_bot_test.py\n", "\n", "\u001b[92m        commit_message=\"test\",\n", "        author_name=\"author\",\n", "        author_email=\"email\",\n", "        repo_owner=\"augmentcode\",\n", "        repo_name=\"augment\",\n", "    )\n", "    proto = commit_summary.to_proto()\n", "    assert proto.sha == \"1234567890\"\n", "    assert proto.commit_message == \"test\"\n", "    \u001b[0m\u001b[47m\u001b[30massert (\n", "        proto.commit_url == \"https://github.com/augmentcode/augment/commit/1234567890\"\n", "    )\u001b[0m\u001b[94m\n", "    assert proto.author_name == \"author\"\n", "    assert proto.author_email == \"email\"\n", "    assert proto.repo_owner == \"augmentcode\"\n", "    assert proto.repo_name == \"augment\"\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 9.1 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 91b5b075-11cf-40f9-beb9-afcbc665236c\n", "Request ID 1: 00fe475d-2e66-4a43-a1b6-44823a8bb3cf\n", "\u001b[107m\u001b[31massert proto.commit_url == \"https://github.com/augmentcode/augment/commit/1234567890\"\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: ed94a6eb-ded5-4906-9fdb-1fe93bee83d1\n", "Request ID 1: e5a888c8-b3d0-48e4-b424-c3840dd6d90e\n", "\u001b[107m\u001b[31massert proto.commit_url == \"https://github.com/augmentcode/augment/commit/1234567890\"\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['d2e3f7d9d44f69272a9814023ac749226a76f9ab43b7b060189dcca67865069f']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['d2e3f7d9d44f69272a9814023ac749226a76f9ab43b7b060189dcca67865069f']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['d2e3f7d9d44f69272a9814023ac749226a76f9ab43b7b060189dcca67865069f']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: c1ef9154-60c1-41b4-8e35-7a17887686a0\n", "Request ID 1: 833899a9-2454-4a3f-8941-3e38313401cd\n", "\u001b[107m\u001b[31massert proto.commit_url == \"https://github.com/augmentcode/augment/commit/1234567890\"\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[1], 10, 10)\n", "compare_models(systems, samples[1], doc_by_id)"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/server/bazel_runner_server_gcp_lib_manual_test.py\n", "\n", "\u001b[92m        event_time=datetime.datetime.now(),\n", "    )\n", "\n", "    events = list(\n", "        build_event_persistence.get_build_events(\n", "            job_id=job_id, min_sequence_number=2000\n", "        )\n", "    )\n", "    assert len(events) == 3\n", "    \u001b[0m\u001b[47m\u001b[30massert events[0][0] == 3000\n", "    assert events[1][0] == 5000\n", "    assert events[2][0] == 70000\u001b[0m\u001b[94m\n", "\n", "\n", "def test_create_to_done(persistence):\n", "    test_spec = bazel_runner_pb2.BazelRunnerExecutionGroupSpec()\n", "    run_id = uuid.uuid4()\n", "    persistence.create_run(\n", "        run_id=run_id, test_execution=test_spec, requestor=\"user1\", tags=[\"manual\"]\n", "    )\n", "\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: a40d00d8-a825-40af-b8a6-9a9386628bc5\n", "Request ID 1: b55188ef-ea87-42fe-9227-88b72db271ef\n", "\u001b[107m\u001b[31massert events[0][0] == 5000\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: e8387ac1-afc1-4bc8-8f6c-595868613641\n", "Request ID 1: 53aa13b8-8ae4-44bb-bd65-1c8212ed4baa\n", "\u001b[107m\u001b[31massert events[0][0] == 5000\n", "    assert events[1][0] == 70000\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['11a57607eaffca890495c216c3be955deb9a46fe69025f6c18c2c4768ea9dd44']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['11a57607eaffca890495c216c3be955deb9a46fe69025f6c18c2c4768ea9dd44']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['11a57607eaffca890495c216c3be955deb9a46fe69025f6c18c2c4768ea9dd44']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: f01f491d-0115-4ad5-94fb-6938ae66f605\n", "Request ID 1: f652757a-7fcb-457e-9a54-3710c9813029\n", "\u001b[107m\u001b[31massert events[0][0] == 3000\n", "    assert events[1][0] == 5000\n", "    assert events[2][0] == 70000\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[2], 10, 10)\n", "compare_models(systems, samples[2], doc_by_id)"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/review_edit_bot/text_test.py\n", "\n", "\u001b[92m        \"tools/bazel_runner/review_edit_bot/test_data/test_file.txt\"\n", "    ).read_text(encoding=\"utf-8\")\n", "    prompt = extractor.get_prompt(file_content, comment=comment)\n", "    assert prompt is not None\n", "    print(\"prompt:\", prompt)\n", "    assert (\n", "        prompt.prefix\n", "        == '        name = \"tempo\",\\n        chartname = \"tempo\",\\n        repo_url = \"https://grafana.github.io/helm-charts\",\\n        version = \"v1.3.1\",\\n    )\\n\\n'\n", "    )\n", "    \u001b[0m\u001b[47m\u001b[30massert prompt.suffix == \"\"\n", "    assert (\n", "        prompt.selected_code\n", "        == '    helm_chart(\\n        name = \"keda\",\\n        chartname = \"keda\",\\n        repo_url = \"https://kedacore.github.io/charts\",\\n        version = \"2.12.0\",\\n    )'\n", "    )\u001b[0m\u001b[94m\n", "\n", "\n", "def test_singleline_comment():\n", "    extractor = ParentCommentReviewEditPromptExtractor()\n", "\n", "    comment = github_pb2.Comment(\n", "        body=\"Please remove\",\n", "        diff_hunk='@@ -11,7 +11,7 @@ def setup_helm():\\n         name = \"cert-manager\",\\n         chartname = \"cert-manager\",\\n         repo_url = \"https://charts.jetstack.io\",\\n-        version = \"v1.11.0\",\\n+        version = \"v1.13.3\",',\n", "        line=14,\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.9 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 61e23dc6-9d9d-4650-a5e7-ba72fc321edc\n", "Request ID 1: f295812b-5e0b-478d-b9df-e5e4b66cefee\n", "\u001b[107m\u001b[31massert prompt.suffix == \"\"\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 5347724e-a80e-4463-b680-02b43fb0328f\n", "Request ID 1: 57ebecd3-8c30-4583-81b8-0d8fceda785d\n", "Request ID 2: f9a31af9-2db4-4f74-b1d7-f1e20235e03a\n", "\u001b[107m\u001b[31massert prompt.suffix == \"\"\n", "    assert (\n", "        prompt.selected_code\n", "        == \"\"\"    )\n", "\n", "|>    helm_chart(\n", "|>        name = \"keda\",\n", "|>        chartname = \"keda\",\n", "|>        repo_url = \"https://kedacore.github.io/charts\",\n", "|>        version = \"2.12.0\",\n", "|>    )\n", "\"\"\"\n", "    )\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 2a4a8307-b7a1-49fa-a614-2a377dece5e1\n", "Request ID 1: 6c40107a-cafa-418e-92ed-b64335868668\n", "Request ID 2: b1d37acd-0287-415c-8ed0-f1fc6f0c90ae\n", "Request ID 3: 47996ca8-fde5-4ba4-a06b-017c937b1bba\n", "\u001b[107m\u001b[31massert prompt.suffix == \"\"\n", "    assert (\n", "        prompt.selected_code\n", "        == \"\"\"    )\n", "\n", "|>    helm_chart(\n", "|>        name = \"keda\",\n", "|>        chartname = \"keda\",\n", "|>        repo_url = \"https://kedacore.github.io/charts\",\n", "|>        version = \"2.12.0\",\n", "|>    )\n", "\"\"\"\n", "    )\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[3], 10, 10)\n", "compare_models(systems, samples[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/review_edit_bot/git_client_test.py\n", "\n", "\u001b[92m            blob_id=\"8f20528c69f382c4a758dbe7c3c3884d99d420ce\",\n", "        ),\n", "    ]\n", "\n", "\n", "def test_get_file_contents(test_repo):\n", "    state = GitClientStateImpl(test_repo)\n", "    file_list = list(state.get_file_list())\n", "    assert len(file_list) == 2\n", "    \u001b[0m\u001b[47m\u001b[30massert state.get_file_contents(file_list[0].path) == b\"Hello, world!\"\n", "    assert state.get_file_contents(file_list[1].path) == b\"Another file!\"\n", "\u001b[0m\u001b[94m\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: a80f2f89-e139-4c71-9394-3c36002fde98\n", "Request ID 1: 902a487a-1a66-4380-95eb-c4a2b97e58e3\n", "\u001b[107m\u001b[31m  assert state.get_file_contents(file_list[1].path) == b\"Another file!\"\n", "\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: f1c414ba-ee7c-40a4-b920-7e88b17a6cc7\n", "Request ID 1: deeeada3-5d3b-4845-b396-1996f01dde9f\n", "\u001b[107m\u001b[31m  assert state.get_file_contents(file_list[1].path) == b\"Another file!\"\n", "\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['28ced0dd5f0840ad5448c9461ef3a2ec72c809cb46f9716afa52044b2f6f7744']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['28ced0dd5f0840ad5448c9461ef3a2ec72c809cb46f9716afa52044b2f6f7744']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['28ced0dd5f0840ad5448c9461ef3a2ec72c809cb46f9716afa52044b2f6f7744']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 094d3257-765b-4fde-86a5-153610035e96\n", "Request ID 1: 46a87cb5-bfdf-4fe2-9951-5083ae2b93e6\n", "\u001b[107m\u001b[31m  assert state.get_file_contents(file_list[1].path) == b\"Another file!\"\n", "\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[4], 10, 10)\n", "compare_models(systems, samples[4], doc_by_id, shift_middle=len(\n", "    \"\"\"assert state.get_file_contents(file_list[0].path) == b\"Hello, world!\"\\n  \"\"\"\n", "))"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/update_versions_test.py\n", "\n", "\u001b[92m            in str(context.exception)\n", "        )\n", "\n", "    def test_invalid_version_override(self):\n", "        with self.assertRaises(AssertionError) as context:\n", "            get_new_version(\n", "                version_override=\"0.0.1\",\n", "                channel=\"prerelease\",\n", "            )\n", "        \u001b[0m\u001b[47m\u001b[30mself.assertTrue(\n", "            \"The provided VERSION_OVERRIDE '0.0.1' does not meet the criteria for a 'prerelease'\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\u001b[94m        with self.assertRaises(AssertionError) as context:\n", "            get_new_version(\n", "                version_override=\"0.0.0\",\n", "                channel=\"stable\",\n", "            )\n", "        self.assertTrue(\n", "            \"The provided VERSION_OVERRIDE '0.0.0' does not meet the criteria for a 'stable'\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 92493d73-0dd0-4b4b-a0c2-60c364d478e4\n", "Request ID 1: e043d9aa-a3d2-4793-85e0-7988f689a942\n", "\u001b[107m\u001b[31mself.assertTrue(\n", "            \"The provided VERSION_OVERRIDE '0.0.1' does not meet the criteria for a 'prerelease'\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: b8653f43-385d-42e6-90f4-14e7b70331d7\n", "Request ID 1: 0b09875a-2e65-45a6-beaa-6de889e1e05b\n", "\u001b[107m\u001b[31mself.assertTrue(\n", "            \"The provided VERSION_OVERRIDE '0.0.1' does not meet the criteria for a 'prerelease'\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['691fba419f03203cab856ab5af8fae4c15e0cce9bc5b848f28ea3e5d28137bf9']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['691fba419f03203cab856ab5af8fae4c15e0cce9bc5b848f28ea3e5d28137bf9']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['691fba419f03203cab856ab5af8fae4c15e0cce9bc5b848f28ea3e5d28137bf9']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 120778c5-2507-4f75-89d7-a69be2b30397\n", "Request ID 1: f535cc00-21e2-445e-8dd9-341fb92286d0\n", "\u001b[107m\u001b[31mself.assertTrue(\n", "            \"The provided VERSION_OVERRIDE '0.0.1' does not meet the criteria for a 'prerelease'\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[5], 10, 10)\n", "compare_models(systems, samples[5], doc_by_id)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/update_versions_test.py\n", "\n", "\u001b[92m                        {\n", "                            \"version\": \"0.0.0\",\n", "                        },\n", "                        {\n", "                            \"version\": \"1.0.1\",\n", "                        },\n", "                    ],\n", "                ),\n", "            )\n", "        \u001b[0m\u001b[47m\u001b[30mself.assertTrue(\n", "            \"The prerelease version should be > the stable version: stable (1.0.1), prerelease (0.1.0)\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\u001b[94m        with self.assertR<PERSON>es(SystemError) as context:\n", "            get_new_version(\n", "                channel=\"stable\",\n", "                stable_release_ref=\"0.1.0\",\n", "                raw_extension_data=json.dumps(\n", "                    [\n", "                        {\n", "                            \"version\": \"0.1.0\",\n", "                        },\n", "                        {\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.9 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: d485440b-d360-45d5-89ba-96ffa5397547\n", "Request ID 1: 3a9f9770-7f84-4940-babe-e08a8e8ca325\n", "\u001b[107m\u001b[31mself.assertTrue(\n", "            \"The prerelease version is invalid: 1.0.1\" in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.7 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: ef5992ed-21d9-4510-95d0-1d630727df9a\n", "Request ID 1: c6957164-6688-456a-a12b-d9c66ad7eabd\n", "\u001b[107m\u001b[31mself.assertTrue(\n", "            \"The stable version is invalid: 1.0.1\" in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['8b4491ceb731e0d0f58b0273a57c1d88ebf572ec86fadabff031e569ea4dbb7d']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.1 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['8b4491ceb731e0d0f58b0273a57c1d88ebf572ec86fadabff031e569ea4dbb7d']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['8b4491ceb731e0d0f58b0273a57c1d88ebf572ec86fadabff031e569ea4dbb7d']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 9525abb7-a3ab-493e-a612-92cde96da4e7\n", "Request ID 1: 2a2f3e2f-92f6-4187-aba1-052c6161e774\n", "\u001b[107m\u001b[31mself.assertTrue(\n", "            \"The prerelease version should be > the stable version: stable (1.0.1), prerelease (0.0.0)\"\n", "            in str(context.exception)\n", "        )\n", "\n", "\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[6], 10, 10)\n", "compare_models(systems, samples[6], doc_by_id)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/vcs/watcher/__tests__/vcs-repo-watcher.test.ts\n", "\n", "\u001b[92m            const commitChanges = await vcsRepoWatcher.collectCommitChanges();\n", "            expect(commitChanges).toMatchSnapshot();\n", "            expect(commitChanges.length).toBe(2);\n", "            expect(commitChanges[0].files.length).toBe(commit1Changes.output.length - 2); // -2 because of the commit hash and the empty line\n", "            expect(commitChanges[1].files.length).toBe(commit2Changes.output.length - 2);\n", "            assertAllCommandsWereUsedInOrder(commitChangesCommandsMock);\n", "        });\n", "    });\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mdescribe(\"#collectWorkingDirectoryChanges\", () => {\n", "        it(\"should collect changes\", async () => {\n", "            for (const command of workingDirectoryChangesCommandsMock) {\n", "                commandExecutor.withCommandStdout(command.command, command.stdout);\n", "            }\n", "            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();\n", "            expect(workingDirectoryChanges).toMatchSnapshot();\n", "            assertAllCommandsWereUsedInOrder(workingDirectoryChangesCommandsMock);\n", "        });\n", "    });\u001b[0m\u001b[94m\n", "\n", "    describe(\"#collectBufferChanges\", () => {\n", "        it(\"should collect changes\", async () => {\n", "            for (const command of bufferChangesCommandsMock) {\n", "                commandExecutor.withCommandStdout(command.command, command.stdout);\n", "            }\n", "            fileChangeWatcher.withPathsWithBufferChanges([\"foo.js\", \"src/old.js\"]);\n", "            const bufferChanges = await vcsRepoWatcher.collectBufferChanges();\n", "            expect(bufferChanges).toMatchSnapshot();\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 4a6c7267-6f57-4268-b159-526ed73f3c8f\n", "Request ID 1: 34049497-752a-4e30-9fab-a4700fa6665d\n", "Request ID 2: c5cabdad-a28a-4c80-9f6f-a0c8c514cc99\n", "Request ID 3: a9e46aca-42db-43d3-96ce-3c88c1e9a701\n", "Request ID 4: 0f64af5f-b9e7-49b8-aeed-4a764b3923b6\n", "Request ID 5: 6ff4c3f9-81d5-410f-a8d8-a24f1feb3d90\n", "Request ID 6: 86d9706c-99aa-4a81-a81b-a2f4a7de1269\n", "\u001b[107m\u001b[31mdescribe(\"#collectWorkingDirectoryChanges\", () => {\n", "        it(\"should collect changes\", async () => {\n", "            for (const command of workingDirectoryChangesCommandsMock) {\n", "                commandExecutor.withCommandStdout(command.command, command.stdout);\n", "            }\n", "            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();\n", "            expect(workingDirectoryChanges).toMatchSnapshot();\n", "            assertAllCommandsWereUsedInOrder(workingDirectoryChangesCommandsMock);\n", "        });\n", "    });\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.7 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 4d4da62a-111d-4495-9018-e04a93935f9e\n", "Request ID 1: 09cdb0ef-29ec-44a8-8650-5afff08bd649\n", "Request ID 2: 52715adb-691e-4880-863e-a55faf30f008\n", "Request ID 3: 93915c46-6401-40f8-b40c-b23f484906b5\n", "Request ID 4: 616842ad-a181-4cea-bdcd-2fe8764e839b\n", "Request ID 5: 496d31ec-f3e4-4ae9-930d-3afea72ef8bc\n", "\u001b[107m\u001b[31mdescribe(\"#collectWorkingDirectoryChanges\", () => {\n", "        it(\"should collect changes\", async () => {\n", "            for (const command of workingDirectoryChangesCommandsMock) {\n", "                commandExecutor.withCommandStdout(command.command, command.stdout);\n", "            }\n", "            const wdChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();\n", "            expect(wdChanges).toMatchSnapshot();\n", "            assertAllCommandsWereUsedInOrder(workingDirectoryChangesCommandsMock);\n", "        });\n", "    });\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['544f5d44c6fa72251e1fd641a2e569b700e05d6569835a7054e4e86298f9ee97']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: c4d4af2f-0a79-4a28-b8c1-29af40074591\n", "Request ID 1: b0348bbd-09b8-43e9-818f-95d62720d5da\n", "Request ID 2: 52cb779e-83c9-471d-a68f-5f9d63682ace\n", "Request ID 3: ec01e7f4-9879-40dd-bd09-98a8c8cd4fbd\n", "Request ID 4: e64331d7-fbef-4672-80a8-e873cd129c70\n", "Request ID 5: 4235c85a-95a6-4e15-a321-ba74894ffb83\n", "Request ID 6: 3a5a6c5a-2d00-4530-96e0-0c99f2ef42c8\n", "\u001b[107m\u001b[31mdescribe(\"#collectWorkingDirectoryChanges\", () => {\n", "        it(\"should collect changes\", async () => {\n", "            for (const command of workingDirectoryChangesCommandsMock) {\n", "                commandExecutor.withCommandStdout(command.command, command.stdout);\n", "            }\n", "            fileChangeWatcher.withPathsWithBufferChanges([]);\n", "            const workingDirectoryChanges = await vcsRepoWatcher.collectWorkingDirectoryChanges();\n", "            expect(workingDirectoryChanges).toMatchSnapshot();\n", "            assertAllCommandsWereUsedInOrder(workingDirectoryChangesCommandsMock);\n", "        });\n", "    });\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[7], 10, 10)\n", "compare_models(systems, samples[7], doc_by_id)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/next-edit/background-edits.test.ts\n", "\n", "\u001b[92m\n", "            // Allow event loop to run\n", "            const promise = new Promise((resolve) => setImmediate(resolve));\n", "            jest.advanceTimersByTimeAsync(1);\n", "            await promise;\n", "\n", "            expect(kit.backgroundNextEdits.currentGeneration).toBeUndefined();\n", "        });\n", "\n", "        \u001b[0m\u001b[47m\u001b[30mit(\"ignore event when no active editor open\", async () => {\n", "            const doc = new MutableTextDocument(Uri.file(\"/example/b\"), \"abc\");\n", "            workspace.textDocumentChanged.fire(doc.delete(0, 1));\n", "\n", "            // This is a bit of a nebulous assertion as we are checking that\n", "            // nothing is returned.\n", "            jest.advanceTimersByTime(BackgroundNextEdits.backgroundGenerationDelayMs);\n", "\n", "            // Allow event loop to run\n", "            const promise = new Promise((resolve) => setImmediate(resolve));\n", "            jest.advanceTimersByTimeAsync(1);\n", "            await promise;\n", "\n", "            expect(kit.backgroundNextEdits.currentGeneration).toBeUndefined();\n", "        });\n", "\n", "\u001b[0m\u001b[94m        it(\"ignore changes for non-active text editor\", async () => {\n", "            const doc1 = new TextDocument(Uri.file(\"/example/a\"), \"a\");\n", "            const doc2 = new MutableTextDocument(Uri.file(\"/example/b\"), \"abc\");\n", "            // Set active editor to doc1\n", "            publishActiveTextEditorChange(new TextEditor(doc1));\n", "\n", "            // Trigger event for doc2\n", "            workspace.textDocumentChanged.fire(doc2.delete(0, 1));\n", "\n", "            // This is a bit of a nebulous assertion as we are checking that\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 9.5 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 56822e94-94ab-4f98-b528-776850d8f2f3\n", "Request ID 1: 73a58f3a-f3e7-485d-8b16-69e18875ddd4\n", "Request ID 2: 99456d46-e2eb-4085-ad58-0aa27bcc711e\n", "Request ID 3: 6bf1b080-6784-41fb-ada9-6bb9f0f9bc13\n", "Request ID 4: 02c96a6b-63b1-448e-a8c1-5cca7de15c93\n", "Request ID 5: 60bc55a8-1aef-4cf7-8681-e059476e0b8c\n", "Request ID 6: 2e27209f-8dd2-40ea-87cd-98622d719077\n", "Request ID 7: a46d1f91-5b14-4fd3-92dd-88e74a37fe87\n", "\u001b[107m\u001b[31mit(\"ignore output document changes\", async () => {\n", "            const outputDoc = new TextDocument(Uri.parse(\"output:///relative/a\"), \"a\");\n", "            const editor = new TextEditor(outputDoc);\n", "            window.activeTextEditor = editor;\n", "\n", "            // Trigger event\n", "            workspace.textDocumentChanged.fire(\n", "                new TextDocumentChangeEvent(\n", "                    new TextDocument(Uri.file(\"/example/path\"), \"mock document text\"),\n", "                    []\n", "                )\n", "            );\n", "\n", "            // This is a bit of a nebulous assertion as we are checking that\n", "            // nothing is returned.\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 01be6a16-d666-43bf-8b29-cafcd792a583\n", "Request ID 1: 118ded01-9abe-4c25-9e4c-0434d9328743\n", "Request ID 2: 7dabcec3-381f-4fa1-905f-c0ed42fea66d\n", "Request ID 3: f0e22cfd-6be4-4feb-a0e7-9abc6276296e\n", "Request ID 4: c3def3e9-b752-44f7-af7c-76151d85c4ce\n", "Request ID 5: 977ed38a-7763-4fa5-b742-0c34bd5f6eae\n", "Request ID 6: 608bec38-1ce0-4b6b-93e6-d093dbdb4fee\n", "Request ID 7: fda35751-e710-4eaf-a886-fbe3bc97eee8\n", "\u001b[107m\u001b[31mit(\"ignore output document changes\", async () => {\n", "            const outputDoc = new TextDocument(Uri.parse(\"output:///relative/a\"), \"a\");\n", "            const editor = new TextEditor(outputDoc);\n", "            window.activeTextEditor = editor;\n", "\n", "            // Trigger event\n", "            workspace.textDocumentChanged.fire(outputDoc.delete(0, 1));\n", "\n", "            // This is a bit of a nebulous assertion as we are checking that\n", "            // nothing is returned.\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 30.2 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['528374a258215d04100860bada0dc68f7e4151969f93ba8a6c554b9415c445d0']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 7839217c-746b-4e69-b84f-b8656a07e965\n", "Request ID 1: 2d06d57c-2ed3-4af6-bdee-a6e323dc489a\n", "Request ID 2: baae832a-2ad5-411e-9377-0b1c98dd8483\n", "Request ID 3: f2c045df-ff72-46b0-8597-f22f1b3b97ce\n", "Request ID 4: d81c3633-c559-4064-a00d-d85033790bf0\n", "Request ID 5: fcf49d6a-157f-459f-b29c-ac279eb3eb6d\n", "Request ID 6: 31f202dd-b886-4834-88ed-11e4c7f319bd\n", "Request ID 7: c370336b-e4f2-454a-b3a8-7fa867185743\n", "\u001b[107m\u001b[31mit(\"ignore changes for non-file text document\", async () => {\n", "            const doc1 = new TextDocument(Uri.file(\"/example/a\"), \"a\");\n", "            const doc2 = new MutableTextDocument(Uri.file(\"/example/b\"), \"abc\");\n", "            // Set active editor to doc1\n", "            publishActiveTextEditorChange(new TextEditor(doc1));\n", "\n", "            // Trigger event for doc2\n", "            workspace.textDocumentChanged.fire(doc2.delete(0, 1));\n", "\n", "            // This is a bit of a nebulous assertion as we are checking that\n", "            // nothing is returned.\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[8], 10, 10)\n", "compare_models(systems, samples[8], doc_by_id)"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/statusbar/status-bar-manager.test.ts\n", "\n", "\u001b[92m\n", "        new StatusBarManager();\n", "\n", "        assertState(mockStatusBar, initialState);\n", "        expect(mockStatusBar.name).toEqual(\"Augment\");\n", "        expect(mockStatusBar.command).toEqual(ShowActions.commandID);\n", "        expect(mockStatusBar.show).toHaveBeenCalled();\n", "    });\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mtest(\"update state\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "                background: new ThemeColor(\"statusBarItem.errorBackground\"),\n", "                foreground: new ThemeColor(\"statusBarItem.errorForeground\"),\n", "            },\n", "        };\n", "\n", "        const manager = new StatusBarManager();\n", "        manager.setState(state);\n", "        assertState(mockStatusBar, state);\n", "    });\n", "\n", "\u001b[0m\u001b[94m    test(\"reset state\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 9.1 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 1743727f-671b-4d5f-a1bc-cef10bad5f4e\n", "Request ID 1: d74428e9-6549-45dd-8985-19505e9bdc47\n", "Request ID 2: d02eb93e-834c-4e53-a123-fc7934302c28\n", "Request ID 3: dd30521d-7560-458e-b628-36e3632e3eea\n", "Request ID 4: 7efc5ddd-f867-445c-8d9d-3ca2bfd48432\n", "Request ID 5: 0f6a4875-7937-4671-9afd-c7de3fe2cb50\n", "Request ID 6: f94339fa-3e34-4d16-ba66-74e0cd2c8774\n", "Request ID 7: 9a99131d-68c5-455c-9326-8ba36fb56303\n", "\u001b[107m\u001b[31mtest(\"setState\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "                background: new ThemeColor(\"statusBarItem.errorBackground\"),\n", "                foreground: new ThemeColor(\"statusBarItem.errorForeground\"),\n", "            },\n", "        };\n", "\n", "        const manager = new StatusBarManager();\n", "        manager.setState(state);\n", "        assertState(mockStatusBar, state);\n", "    });\n", "\n", "\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 8408a088-15d7-41a7-9678-16e3a363890c\n", "Request ID 1: cdda8e51-3f15-49dc-abf0-204c210a8abf\n", "Request ID 2: aa233e30-15f7-4551-b967-76ea02b0e751\n", "Request ID 3: eb6b4c20-4bb8-4c1b-95fb-e71365b31cdb\n", "Request ID 4: 8e0d370b-86c7-4328-a4a8-8ffb6b001306\n", "Request ID 5: 4aa8a4f6-65b3-474f-a27f-741355ac82df\n", "\u001b[107m\u001b[31mtest(\"set state\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "                background: new ThemeColor(\"statusBarItem.errorBackground\"),\n", "                foreground: new ThemeColor(\"statusBarItem.errorForeground\"),\n", "            },\n", "        };\n", "\n", "        const manager = new StatusBarManager();\n", "        manager.setState(state);\n", "        assertState(mockStatusBar, state);\n", "    });\n", "\n", "\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 25.2 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 05433e9e-7a43-462e-aeef-10568359c884\n", "Request ID 1: cf64ce21-2f37-4b17-be32-d04bf2f7960a\n", "Request ID 2: d31fb071-0fde-46be-8597-09a60f3d4e07\n", "Request ID 3: 971e9e44-9f72-4cf5-9617-5b35b8393b2d\n", "Request ID 4: e0c1da12-1fb1-49fe-a303-b0fcd84cc115\n", "Request ID 5: de57b22e-35a0-42f7-bdde-593c13c92dbb\n", "Request ID 6: 5d67b58f-268c-4042-8622-417da1268a95\n", "\u001b[107m\u001b[31mtest(\"set state\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "                background: new ThemeColor(\"statusBarItem.errorBackground\"),\n", "                foreground: new ThemeColor(\"statusBarItem.errorForeground\"),\n", "            },\n", "        };\n", "\n", "        const manager = new StatusBarManager();\n", "        manager.setState(state);\n", "        assertState(mockStatusBar, state);\n", "    });\n", "\n", "\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[9], 10, 10)\n", "compare_models(systems, samples[9], doc_by_id)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/completions/supress-deleted-completionts.test.ts\n", "\n", "\u001b[92m        // We won't show a completion equal to what we just deleted.\n", "        kit.mutateTrackAndVerify(document.delete(3, 1), \"1479\");\n", "        await kit.assertCompletion(document, 3, \"8\", false);\n", "\n", "        // Switching to a new document means we'll complete what we would not have in the same document.\n", "        document = kit.resetDocumentAndTracking(\"1479\");\n", "        await kit.assertCompletion(document, 3, \"8\", true);\n", "    });\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mtest(`forward deletion for same completion`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "\n", "        // Delete a character to the right of the previsious completion\n", "        // Emulate a forward deletion (i.e. a deletion using the `del` key)\n", "        kit.mutateTrackAndVerify(document.delete(4, 1), \"12346789\");\n", "        await kit.assertCompletion(document, 4, \"a\", false);\n", "    });\n", "\n", "    test(`forward deletion for same completion plus removed character`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "\n", "        // Delete a character to the right of the previsious completion\n", "        // Emulate a forward deletion (i.e. a deletion using the `del` key)\n", "        kit.mutateTrackAndVerify(document.delete(4, 1), \"12346789\");\n", "        await kit.assertCompletion(document, 4, \"a5\", false);\n", "    });\n", "\n", "    test(`forward deletion for with different completion text`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "\n", "        // Delete a character to the right of the previsious completion\n", "        // Emulate a forward deletion (i.e. a deletion using the `del` key)\n", "        kit.mutateTrackAndVerify(document.delete(4, 1), \"12346789\");\n", "        await kit.assertCompletion(document, 4, \"b\", true);\n", "    });\u001b[0m\u001b[94m\n", "\n", "    test(`ignore deletion in a different range`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "\n", "        kit.mutateTrackAndVerify(document.delete(6, 1), \"12345689\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "    });\n", "\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 9.0 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: d83c5025-c122-4088-9c8a-51b926718991\n", "Request ID 1: 7b72296b-1f55-449f-b4e1-a0f56c488852\n", "Request ID 2: d30eca8f-874d-4276-a756-552008450c93\n", "Request ID 3: d5216895-5263-4bea-88f3-220a894f79ab\n", "Request ID 4: c260b74d-c5d5-4c14-b074-889c2d9570e7\n", "\u001b[107m\u001b[31mtest(`ignore deletion in a different document`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"5\", true);\n", "\n", "        kit.mutateTrackAndVerify(document.delete(4, 1), \" \"12346789\");\n", "        await kit.assertCompletion(document, 4, \"5\", false);\n", "    });\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2851 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 0c39b327-d6e2-4768-9916-80d2cfec9c4b\n", "Request ID 1: b553a6cd-f742-4cfa-97d6-b3001b0c35be\n", "Request ID 2: 3308196f-b08f-43ff-abab-06fd5e2ec8f5\n", "Request ID 3: 15ab76aa-e0be-47d6-8321-43a4a0974fb9\n", "Request ID 4: a0dcc4a7-a66b-4d3d-a636-1a3f6e6ce114\n", "Request ID 5: 96763b1a-2128-45a0-987b-15f69cca0033\n", "\u001b[107m\u001b[31mtest(`ignore deletion in a different document`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "\n", "        const newDocument = new MutableTextDocument(Uri.file(\"file2.py\"), \"123456789\");\n", "        kit.mutateTrackAndVerify(newDocument.delete(4, 1), \"12346789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "    });\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2576 documents in 8.5 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 9d2ff020-9263-4b3d-b295-f95acf2501a3\n", "Request ID 1: 4d38d9b3-a400-47da-8f7c-621bce05e860\n", "Request ID 2: bedc1a13-dcf4-4058-8a56-df87bd6654ed\n", "Request ID 3: 572b0d22-e133-45a4-9c7f-f489d10c2993\n", "Request ID 4: 7668d263-163c-4076-9fb2-d3c0f2c952fa\n", "\u001b[107m\u001b[31mtest(`ignore deletion in a different document`, async () => {\n", "        const document = kit.resetDocumentAndTracking(\"123456789\");\n", "        await kit.assertCompletion(document, 4, \"a\", true);\n", "\n", "        const newDocument = new MutableTextDocument(Uri.file(\"file2.py\"), \"12345689\");\n", "        await kit.assertCompletion(newDocument, 4, \"a\", true);\n", "    });\u001b[0m\n"]}], "source": ["show_truncated_completion(samples[10], 10, 10)\n", "compare_models(systems, samples[10], doc_by_id)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}