{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# StarCoder-2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "dataset.dtype = <class 'numpy.uint16'>\n", "0\n", "49152\n", "2\n"]}], "source": ["import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "datapath_path = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset\"\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath_path, skip_warmup=True)\n", "print(f\"{dataset.dtype = }\")\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "print(tokenizer.special_tokens.eos)\n", "print(tokenizer.special_tokens.padding)\n", "print(tokenizer.special_tokens.fim_middle)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 2\n", "# Find the index of the first pad token.\n", "tokens = dataset[index].tolist()\n", "print(tokens)\n", "# tokens = tokens[: tokens.index(tokenizer.special_tokens.padding)]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokens[:10])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# spark load data\n", "from research.data.spark import k8s_session\n", "\n", "spark = k8s_session()\n", "prompt_data_dir = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/prompt-normal/sc2\"\n", "prompt_data = spark.read.parquet(prompt_data_dir)\n", "prompt_data_10 = prompt_data.limit(10)\n", "prompt_data_10_list = prompt_data_10.toPandas().to_dict(orient='records')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "bytes_data = prompt_data_10_list[0][\"prompt_tokens\"]\n", "byte_array = bytearray(bytes_data)\n", "\n", "# Convert the bytearray back to a numpy array\n", "recovered_tokens = np.frombuffer(byte_array, dtype=np.uint16).newbyteorder(\"<\")\n", "\n", "# Convert the numpy array back to a list\n", "tokens_list = recovered_tokens.tolist()\n", "print(f\"{tokens_list = }\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# prompt_data_10_pd\n", "tokenizer.detokenize([6, 0, 9, 17, 52, 0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# DeepSeek-V2-Coder-<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "dataset.dtype = <class 'numpy.int32'>\n"]}], "source": ["import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer\n", "\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/dscv2/dataset\"\n", "datapath = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/dscv2/dataset\"\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)\n", "print(f\"{dataset.dtype = }\")\n", "tokenizer = DeepSeekCoderV2Tokenizer()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["100001\n", "100001\n", "100004\n", "100036\n"]}], "source": ["print(tokenizer.special_tokens.eos)\n", "print(tokenizer.special_tokens.padding)\n", "print(tokenizer.special_tokens.fim_middle)\n", "print(tokenizer.vocab_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 3\n", "# Find the index of the first pad token.\n", "tokens = dataset[index].tolist()\n", "print(tokens)\n", "tokens = tokens[: tokens.index(tokenizer.special_tokens.padding)]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# spark load data\n", "from research.data.spark import k8s_session\n", "\n", "spark = k8s_session()\n", "prompt_data_dir = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/prompt-unittest-default/dscv2\"\n", "prompt_data = spark.read.parquet(prompt_data_dir)\n", "prompt_data_10 = prompt_data.limit(10)\n", "prompt_data_10_list = prompt_data_10.toPandas().to_dict(orient='records')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "bytes_data = prompt_data_10_list[0][\"prompt_tokens\"]\n", "byte_array = bytearray(bytes_data)\n", "\n", "# Convert the bytearray back to a numpy array\n", "recovered_tokens = np.frombuffer(byte_array, dtype=np.int32).newbyteorder(\"<\")\n", "\n", "# Convert the numpy array back to a list\n", "tokens_list = recovered_tokens.tolist()\n", "print(f\"{tokens_list = }\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(type(prompt_data_10_list[0][\"prompt_tokens\"]))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}