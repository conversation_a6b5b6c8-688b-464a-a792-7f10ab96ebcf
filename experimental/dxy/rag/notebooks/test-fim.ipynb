{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue import RogueSampleConfig\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "\n", "REPO_LANGUAGES = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"go\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "    \"c-sharp\",\n", "    \"ruby\",\n", "    \"php\",\n", "    \"tsx\",\n", "    \"jsx\",\n", "    \"css\",\n", "    \"shell\",\n", "    \"scala\",\n", "    \"ruby\",\n", "    \"lua\",\n", "    \"kotlin\",\n", "]\n", "REPO_LANGUAGES_TEMP_FOR_UNIT_TEST = [\n", "    \"c\",\n", "    \"c++\",\n", "    \"java\",\n", "    \"javascript\",\n", "    \"python\",\n", "    \"rust\",\n", "    \"typescript\",\n", "]\n", "\n", "additional_sample_languages = [\n", "    \"sql\",\n", "    \"markdown\",\n", "]\n", "SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages\n", "\n", "additional_retrieval_languages = [\n", "    \"cuda\",\n", "    \"svelte\",\n", "    \"protocol-buffer\",\n", "    \"dart\",\n", "    \"html\",\n", "    \"makefile\",\n", "    \"dockerfile\",\n", "    \"text\",\n", "    \"yaml\",\n", "    \"json\",\n", "    \"xml\",\n", "    \"jsonnet\",\n", "]\n", "RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages\n", "\n", "\n", "def get_config(limit_repos: int = 1000):\n", "    # file_path_regex_pattern = get_unit_test_re_pattern(reverse=True)\n", "    output_name = \"normal\"\n", "\n", "    config = RogueSampleConfig(\n", "        input=\"s3a://the-stack-processed/by-repo-3\",\n", "        output=f\"s3a://dxy-dev-bucket/ragdata/limit-repo-{limit_repos}/{output_name}\",\n", "        fim_version=FimDataProcessor.VERSION,\n", "        repo_languages=REPO_LANGUAGES,\n", "        sample_languages=SAMPLE_LANGUAGES,\n", "        retrieval_languages=RETRIEVAL_LANGUAGES,\n", "        file_path_regex_pattern=None,\n", "        limit_repos=limit_repos,\n", "        repo_min_size=500000,\n", "        repo_max_size=100000000,\n", "        every_n_lines=100,\n", "        max_problems_per_file=5,\n", "        small_downsampled_probability=0.1,\n", "        small_downsample_char_threshold=1500,\n", "        small_filter_char_threshold=50,\n", "        random_seed=74912,\n", "        num_retrieved_chunks=40,\n", "        scorer_config={\n", "            \"name\": \"ethanol\",\n", "            \"checkpoint_path\": \"ethanol/ethanol6-04.1\",\n", "        },\n", "        chunker_config={\n", "            \"name\": \"line_level\",\n", "            \"max_lines_per_chunk\": 30,\n", "            \"include_scope_annotation\": <PERSON>als<PERSON>,\n", "        },\n", "        query_config={\n", "            \"name\": \"ethanol6_query\",\n", "            \"max_tokens\": 1023,\n", "            \"add_path\": True,\n", "        },\n", "        document_config={\n", "            \"name\": \"ethanol6_document\",\n", "            \"max_tokens\": 999,\n", "            \"add_path\": True,\n", "        },\n", "    )\n", "    return config"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The file has 1106 lines.\n", "There are 5 samples\n"]}], "source": ["from research.fim import fim_sampling\n", "from research.data.rag.rogue import _file_to_samples\n", "from experimental.dxy.rogue.notebooks.exp_utils import load_case_1, load_case_2, customized_node_weight\n", "\n", "\n", "# test_file_path, test_file_content = load_case_1()\n", "test_file_path, test_file_content = load_case_2()\n", "\n", "config = get_config()\n", "config.random_seed = 12\n", "# sampler = fim_sampling.CSTFimSampler()\n", "sampler = fim_sampling.CSTFimSampler(\n", "    node_size_soft_limit=64,  # disable the soft limit\n", "    pick_whole_node_rate=0.9,\n", "    corruption_expansion_rate=0.2,\n", "    empty_completion_rate=0.02,\n", ")\n", "logs = []\n", "samples = _file_to_samples(\n", "    test_file_content,\n", "    file_id=\"test001\",\n", "    file_path=test_file_path,\n", "    config=config,\n", "    sampler=sampler,\n", "    get_node_weight=customized_node_weight,\n", "    logs=logs,\n", ")\n", "print(f\"The file has {len(test_file_content.splitlines())} lines.\")\n", "print(f\"There are {len(samples)} samples\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["b'\\n    example_input = PromptInput(\\n        path=\"src/example.py\",\\n        prefix=\"def aggregate(a,b):\\\\n\",\\n        suffix=\"\",\\n        prefix_begin=0,\\n        retrieved_chunks=(),\\n    )<|pause|>\\n    prompt = _format(example_input)<|pause|>\\n\\n    expected_prompt = \"\".join(\\n        [\\n            \"<filename>src/example.py\\\\n\",\\n            \"<|retrieval_section|>\",\\n            \"<|sig-begin|>\\\\n\",\\n            \"\\\\n<|sig-end|>\",\\n            \"<fim_prefix>def aggregate(a,b):\\\\n\",\\n            \"<fim_suffix>\",\\n            \"<fim_middle>\",\\n        ]\\n    )<|pause|>\\n    assert prompt == expected_prompt<|pause|>\\n\\n\\ndef test_format_prompt_empty_prefix_and_suffix():\\n    \"\"\"Verifies the formatting with an empty prefix and suffix.\\n\\n    This can e.g. happen for a completion in an empty file.\\n    \"\"\"\\n    test_prompt_input = PromptInput(\\n        path=\"src/example.py\",\\n        prefix=\"\",\\n        suffix=\"\",\\n        prefix_begin=0,\\n        retrieved_chunks=(),\\n    )\\n    prompt = _format(test_prompt_input)\\n\\n    expected_prompt = \"\".join(\\n        [\\n            \"<filename>src/example.py\\\\n\",\\n            \"<|retrieval_section|>\",\\n            \"<|sig-begin|>\\\\n\",\\n            \"\\\\n<|sig-end|>\",\\n            \"<fim_prefix>\",\\n            \"<fim_suffix>\",\\n            \"<fim_middle>\",\\n        ]\\n    )\\n    assert prompt == expected_prompt<|pause|>\\n\\n\\ndef test_format_prompt_limited_path():\\n    \"\"\"Verifies the formatting with an empty prefix and suffix.\\n\\n    This can e.g. happen for a completion in an empty file.\\n    \"\"\"\\n    test_prompt_input = PromptInput(\\n        path=\"src/example.py\",\\n        prefix=\"\",\\n        suffix=\"\",\\n        prefix_begin=0,\\n        retrieved_chunks=(\\n            PromptChunk(\\n                text=\"# You can aggregate\\\\n# with a maxing\\\\n# function.\\\\n\",\\n                path=\"src/bar.py\",\\n                origin=\"dense_retriever\",\\n            ),\\n        ),\\n    )\\n    prompt = _format(\\n        test_prompt_input,\\n        apportionment_config=TokenApportionmentConfig(\\n            max_content_len=128,\\n            input_fraction=0.7,\\n            prefix_fraction=0.75,\\n            max_path_tokens=3,\\n        ),\\n    )\\n\\n    expected_prompt = \"\".join(\\n        [\\n            \"<filename>example.py\\\\n\",\\n            \"<|retrieval_section|>\",\\n            \"<|ret-start|><filename>bar.py\",\\n            \"<|ret-body|># You can aggregate\\\\n# with a maxing\\\\n# function.\\\\n\",\\n            \"<|sig-begin|>\\\\n\",\\n            \"\\\\n<|sig-end|>\",\\n            \"<fim_prefix>\",\\n            \"<fim_suffix>\",\\n            \"<fim_middle>\",\\n        ]\\n    )\\n    assert prompt == expected_prompt\\n'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["samples[0].show_middle().encode()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}