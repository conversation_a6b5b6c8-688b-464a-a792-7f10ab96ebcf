{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["candidate_range=0:1860\n", "There are 337 target candidates\n", "There are 42 types:\n", "{'\"', 'typed_parameter', 'module', 'float', 'keyword_argument', 'comparison_operator', 'decorated_definition', '=', '(', '[', 'comment', 'import', '.', 'dotted_name', 'expression_statement', 'def', 'attribute', 'function_definition', ',', '==', 'integer', 'from', 'decorator', ':', 'import_from_statement', 'identifier', 'type', 'assert', 'assignment', 'assert_statement', 'import_statement', 'pattern_list', 'parameters', '@', 'call', 'list', 'string', 'block', ')', 'subscript', ']', 'argument_list'}\n"]}], "source": ["import pathlib\n", "from research.fim import fim_sampling\n", "from research.data.rag.rogue import _file_to_samples\n", "from experimental.dxy.rogue.notebooks.exp_utils import load_case_1, load_case_2\n", "import tree_sitter as ts\n", "from base.static_analysis.common import guess_lang_from_fp\n", "\n", "from base.ranges import ByteRange, CharRange, LineMap\n", "from base.static_analysis.common import MappedCode, decode_bytes, shorten_str\n", "from base.static_analysis.parsing import SrcSpan, TsNodeType, TsParsedFile\n", "from research.core.model_input import ModelInput\n", "from research.fim.fim_sampling import _get_nodes_in_brange\n", "\n", "test_file_path, test_file_content = load_case_1()\n", "\n", "lang = guess_lang_from_fp(test_file_path)\n", "assert lang is not None\n", "pfile = TsParsedFile.parse(path=pathlib.Path(test_file_path), lang=lang, code=test_file_content)\n", "\n", "candidate_range = CharRange(0, len(pfile.code))\n", "print(f\"candidate_range={candidate_range}\")\n", "brange = pfile.bmap.crange_to_brange(candidate_range)\n", "target_candidates = _get_nodes_in_brange(pfile.ts_tree.root_node, brange)\n", "print(f\"There are {len(target_candidates)} target candidates\")\n", "\n", "all_types = set()\n", "for node in target_candidates:\n", "    all_types.add(node.type)\n", "print(f\"There are {len(all_types)} types:\\n{all_types}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from research.eval.dataset_generation_lib.finegrained_patch_generators import get_ts_node_parent_type, get_ts_node_index_in_siblings, get_ts_node_grandpa_type, find_ts_parent_if_same_range\n", "from research.static_analysis.common import decode_bytes\n", "    \n", "\n", "def _is_func_node(node: ts.Node) -> bool:\n", "    return node.type in (\"decorated_definition\", \"function_definition\")\n", "\n", "def _is_docstr(node: ts.Node) -> bool:\n", "    if node.type != \"string\":\n", "        return False\n", "    node_text = decode_bytes(node.text)\n", "    if not node_text.startswith((\"'''\", '\"\"\"', 'r\"\"\"', \"r'''\")):\n", "        return False\n", "    if node.parent is None:\n", "        return False\n", "    node = node.parent\n", "    node = find_ts_parent_if_same_range(node)\n", "    if (\n", "        get_ts_node_parent_type(node) == \"block\"\n", "        and get_ts_node_index_in_siblings(node) == 0\n", "        and get_ts_node_grandpa_type(node) == \"function_definition\"\n", "    ):\n", "        return True\n", "    elif (\n", "        node.type == \"block\" and get_ts_node_parent_type(node) == \"function_definition\"\n", "    ):\n", "        return True\n", "    else:\n", "        return False\n", "\n", "def _is_normal_statement(node: ts.Node) -> bool:\n", "    \"\"\"Whether a statement is a normal statement.\"\"\"\n", "    # We did not include \"assignment\" as it will usually also a \"expression_statement\".\n", "    return node.type in (\"expression_statement\", \"assert_statement\")\n", "\n", "def _is_literal(node: ts.Node) -> bool:\n", "    if node.type not in (\"string\", \"integer\", \"float\"):\n", "        return False\n", "    if node.parent is None:\n", "        return False\n", "    if node.parent.type == \"array\":\n", "        return False\n", "    return True\n", "    \n", "\n", "def customized_node_weight(node: ts.Node) -> float:\n", "    \"\"\"Return the weight of the given node, as it will later be multiplied by the score in range of (1e).\"\"\"\n", "    num_bytes = node.start_byte - node.end_byte\n", "    num_lines = decode_bytes(node.text).count(\"\\n\")\n", "    if num_bytes >= 2500:\n", "        return 0.0\n", "    standard_weight, tiny_weight = 10., 1.\n", "    reduced_weight = 3.\n", "    # Entire functions\n", "    if _is_func_node(node):\n", "        if num_lines < 10:\n", "            return tiny_weight\n", "        elif num_lines < 160:\n", "            return standard_weight\n", "        else:\n", "            return reduced_weight\n", "    elif _is_docstr(node):\n", "        return standard_weight\n", "    elif _is_normal_statement(node):\n", "        return reduced_weight  # since the number of lines are too many\n", "    elif _is_literal(node):\n", "        return tiny_weight\n", "    else:\n", "        return 0.0"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["base/fastforward/llama/fwd_llama_fp8_test.py\n"]}], "source": ["print(test_file_path)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mtype=decorated_definition, start=1230, end=1859\u001b[0m\n", "\u001b[<EMAIL>(\n", "    \"prompt\",\n", "    [\n", "        pytest.param([1], id=\"single_token\"),\n", "        pytest.param([1, 2, 3], id=\"dummy\"),\n", "        pytest.param(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\"),\n", "    ],\n", ")\n", "def test_batched_equals_sequential(llama_350m_fp8_fixture, prompt: Sequence[int]):\n", "    r\"\"\"Ensure that the batched and sequential models produce the same outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_fixture\n", "    step_fn = fwd_utils.pad_and_step(step_fn, [8])\n", "    fwd_model_test_utils.check_batched_equals_sequential(\n", "        step_fn, attn_factory, prompt, extra_kv_len=8\n", "    )\n", "    x = 1.0\n", "    assert x == 1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=function_definition, start=1444, end=1859\u001b[0m\n", "\u001b[31mdef test_batched_equals_sequential(llama_350m_fp8_fixture, prompt: Sequence[int]):\n", "    r\"\"\"Ensure that the batched and sequential models produce the same outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_fixture\n", "    step_fn = fwd_utils.pad_and_step(step_fn, [8])\n", "    fwd_model_test_utils.check_batched_equals_sequential(\n", "        step_fn, attn_factory, prompt, extra_kv_len=8\n", "    )\n", "    x = 1.0\n", "    assert x == 1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=block, start=1531, end=1859\u001b[0m\n", "\u001b[31mr\"\"\"Ensure that the batched and sequential models produce the same outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_fixture\n", "    step_fn = fwd_utils.pad_and_step(step_fn, [8])\n", "    fwd_model_test_utils.check_batched_equals_sequential(\n", "        step_fn, attn_factory, prompt, extra_kv_len=8\n", "    )\n", "    x = 1.0\n", "    assert x == 1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assert_statement, start=1846, end=1859\u001b[0m\n", "\u001b[31massert x == 1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=comparison_operator, start=1853, end=1859\u001b[0m\n", "\u001b[31mx == 1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1858, end=1859\u001b[0m\n", "\u001b[31m1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype===, start=1855, end=1857\u001b[0m\n", "\u001b[31m==\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1853, end=1854\u001b[0m\n", "\u001b[31mx\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assert, start=1846, end=1852\u001b[0m\n", "\u001b[31massert\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=1834, end=1841\u001b[0m\n", "\u001b[31mx = 1.0\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=1834, end=1841\u001b[0m\n", "\u001b[31mx = 1.0\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=float, start=1838, end=1841\u001b[0m\n", "\u001b[31m1.0\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1836, end=1837\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1834, end=1835\u001b[0m\n", "\u001b[31mx\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=1716, end=1829\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_batched_equals_sequential(\n", "        step_fn, attn_factory, prompt, extra_kv_len=8\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=1716, end=1829\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_batched_equals_sequential(\n", "        step_fn, attn_factory, prompt, extra_kv_len=8\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1768, end=1829\u001b[0m\n", "\u001b[31m(\n", "        step_fn, attn_factory, prompt, extra_kv_len=8\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1828, end=1829\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=1809, end=1823\u001b[0m\n", "\u001b[31mextra_kv_len=8\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1822, end=1823\u001b[0m\n", "\u001b[31m8\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1821, end=1822\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1809, end=1821\u001b[0m\n", "\u001b[31mextra_kv_len\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1807, end=1808\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1801, end=1807\u001b[0m\n", "\u001b[31mprompt\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1799, end=1800\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1787, end=1799\u001b[0m\n", "\u001b[31mattn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1785, end=1786\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1778, end=1785\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1768, end=1769\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1716, end=1768\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_batched_equals_sequential\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1737, end=1768\u001b[0m\n", "\u001b[31mcheck_batched_equals_sequential\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1736, end=1737\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1716, end=1736\u001b[0m\n", "\u001b[31mfwd_model_test_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=1665, end=1711\u001b[0m\n", "\u001b[31mstep_fn = fwd_utils.pad_and_step(step_fn, [8])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=1665, end=1711\u001b[0m\n", "\u001b[31mstep_fn = fwd_utils.pad_and_step(step_fn, [8])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=1675, end=1711\u001b[0m\n", "\u001b[31mfwd_utils.pad_and_step(step_fn, [8])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1697, end=1711\u001b[0m\n", "\u001b[31m(step_fn, [8])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1710, end=1711\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=list, start=1707, end=1710\u001b[0m\n", "\u001b[31m[8]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=], start=1709, end=1710\u001b[0m\n", "\u001b[31m]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1708, end=1709\u001b[0m\n", "\u001b[31m8\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=[, start=1707, end=1708\u001b[0m\n", "\u001b[31m[\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1705, end=1706\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1698, end=1705\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1697, end=1698\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1675, end=1697\u001b[0m\n", "\u001b[31mfwd_utils.pad_and_step\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1685, end=1697\u001b[0m\n", "\u001b[31mpad_and_step\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1684, end=1685\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1675, end=1684\u001b[0m\n", "\u001b[31mfwd_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1673, end=1674\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1665, end=1672\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=1614, end=1660\u001b[0m\n", "\u001b[31mstep_fn, attn_factory = llama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=1614, end=1660\u001b[0m\n", "\u001b[31mstep_fn, attn_factory = llama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1638, end=1660\u001b[0m\n", "\u001b[31mllama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1636, end=1637\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=pattern_list, start=1614, end=1635\u001b[0m\n", "\u001b[31mstep_fn, attn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1623, end=1635\u001b[0m\n", "\u001b[31mattn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1621, end=1622\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1614, end=1621\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=1531, end=1609\u001b[0m\n", "\u001b[31mr\"\"\"Ensure that the batched and sequential models produce the same outputs.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=1531, end=1609\u001b[0m\n", "\u001b[31mr\"\"\"Ensure that the batched and sequential models produce the same outputs.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1606, end=1609\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1531, end=1535\u001b[0m\n", "\u001b[31mr\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=:, start=1525, end=1526\u001b[0m\n", "\u001b[31m:\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=parameters, start=1478, end=1525\u001b[0m\n", "\u001b[31m(llama_350m_fp8_fixture, prompt: Sequence[int])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1524, end=1525\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=typed_parameter, start=1503, end=1524\u001b[0m\n", "\u001b[31mprompt: Sequence[int]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=type, start=1511, end=1524\u001b[0m\n", "\u001b[31mSequence[int]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=subscript, start=1511, end=1524\u001b[0m\n", "\u001b[31mSequence[int]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=], start=1523, end=1524\u001b[0m\n", "\u001b[31m]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1520, end=1523\u001b[0m\n", "\u001b[31mint\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=[, start=1519, end=1520\u001b[0m\n", "\u001b[31m[\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1511, end=1519\u001b[0m\n", "\u001b[31mSequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=:, start=1509, end=1510\u001b[0m\n", "\u001b[31m:\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1503, end=1509\u001b[0m\n", "\u001b[31mprompt\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1501, end=1502\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1479, end=1501\u001b[0m\n", "\u001b[31mllama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1478, end=1479\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1448, end=1478\u001b[0m\n", "\u001b[31mtest_batched_equals_sequential\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=def, start=1444, end=1447\u001b[0m\n", "\u001b[31mdef\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=decorator, start=1230, end=1443\u001b[0m\n", "\u001b[<EMAIL>(\n", "    \"prompt\",\n", "    [\n", "        pytest.param([1], id=\"single_token\"),\n", "        pytest.param([1, 2, 3], id=\"dummy\"),\n", "        pytest.param(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\"),\n", "    ],\n", ")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=1231, end=1443\u001b[0m\n", "\u001b[31mpytest.mark.parametrize(\n", "    \"prompt\",\n", "    [\n", "        pytest.param([1], id=\"single_token\"),\n", "        pytest.param([1, 2, 3], id=\"dummy\"),\n", "        pytest.param(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\"),\n", "    ],\n", ")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1254, end=1443\u001b[0m\n", "\u001b[31m(\n", "    \"prompt\",\n", "    [\n", "        pytest.param([1], id=\"single_token\"),\n", "        pytest.param([1, 2, 3], id=\"dummy\"),\n", "        pytest.param(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\"),\n", "    ],\n", ")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1442, end=1443\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1440, end=1441\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=list, start=1274, end=1440\u001b[0m\n", "\u001b[31m[\n", "        pytest.param([1], id=\"single_token\"),\n", "        pytest.param([1, 2, 3], id=\"dummy\"),\n", "        pytest.param(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\"),\n", "    ]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=], start=1439, end=1440\u001b[0m\n", "\u001b[31m]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1433, end=1434\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=1375, end=1433\u001b[0m\n", "\u001b[31mpytest.param(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1387, end=1433\u001b[0m\n", "\u001b[31m(_LLAMA_350M_PROMPT_TOKS, id=\"known_sequence\")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1432, end=1433\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=1413, end=1432\u001b[0m\n", "\u001b[31mid=\"known_sequence\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=1416, end=1432\u001b[0m\n", "\u001b[31m\"known_sequence\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1431, end=1432\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1416, end=1417\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1415, end=1416\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1413, end=1415\u001b[0m\n", "\u001b[31mid\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1411, end=1412\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1388, end=1411\u001b[0m\n", "\u001b[31m_LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1387, end=1388\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1375, end=1387\u001b[0m\n", "\u001b[31mpytest.param\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1382, end=1387\u001b[0m\n", "\u001b[31mparam\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1381, end=1382\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1375, end=1381\u001b[0m\n", "\u001b[31mpytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1365, end=1366\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=1330, end=1365\u001b[0m\n", "\u001b[31mpytest.param([1, 2, 3], id=\"dummy\")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1342, end=1365\u001b[0m\n", "\u001b[31m([1, 2, 3], id=\"dummy\")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1364, end=1365\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=1354, end=1364\u001b[0m\n", "\u001b[31mid=\"dummy\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=1357, end=1364\u001b[0m\n", "\u001b[31m\"dummy\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1363, end=1364\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1357, end=1358\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1356, end=1357\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1354, end=1356\u001b[0m\n", "\u001b[31mid\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1352, end=1353\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=list, start=1343, end=1352\u001b[0m\n", "\u001b[31m[1, 2, 3]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=], start=1351, end=1352\u001b[0m\n", "\u001b[31m]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1350, end=1351\u001b[0m\n", "\u001b[31m3\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1348, end=1349\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1347, end=1348\u001b[0m\n", "\u001b[31m2\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1345, end=1346\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1344, end=1345\u001b[0m\n", "\u001b[31m1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=[, start=1343, end=1344\u001b[0m\n", "\u001b[31m[\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1342, end=1343\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1330, end=1342\u001b[0m\n", "\u001b[31mpytest.param\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1337, end=1342\u001b[0m\n", "\u001b[31mparam\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1336, end=1337\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1330, end=1336\u001b[0m\n", "\u001b[31mpytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1320, end=1321\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=1284, end=1320\u001b[0m\n", "\u001b[31mpytest.param([1], id=\"single_token\")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1296, end=1320\u001b[0m\n", "\u001b[31m([1], id=\"single_token\")\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1319, end=1320\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=1302, end=1319\u001b[0m\n", "\u001b[31mid=\"single_token\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=1305, end=1319\u001b[0m\n", "\u001b[31m\"single_token\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1318, end=1319\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1305, end=1306\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1304, end=1305\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1302, end=1304\u001b[0m\n", "\u001b[31mid\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1300, end=1301\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=list, start=1297, end=1300\u001b[0m\n", "\u001b[31m[1]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=], start=1299, end=1300\u001b[0m\n", "\u001b[31m]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1298, end=1299\u001b[0m\n", "\u001b[31m1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=[, start=1297, end=1298\u001b[0m\n", "\u001b[31m[\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1296, end=1297\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1284, end=1296\u001b[0m\n", "\u001b[31mpytest.param\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1291, end=1296\u001b[0m\n", "\u001b[31mparam\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1290, end=1291\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1284, end=1290\u001b[0m\n", "\u001b[31mpytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=[, start=1274, end=1275\u001b[0m\n", "\u001b[31m[\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1268, end=1269\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=1260, end=1268\u001b[0m\n", "\u001b[31m\"prompt\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1267, end=1268\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=1260, end=1261\u001b[0m\n", "\u001b[31m\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1254, end=1255\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1231, end=1254\u001b[0m\n", "\u001b[31mpytest.mark.parametrize\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1243, end=1254\u001b[0m\n", "\u001b[31mparametrize\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1242, end=1243\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=1231, end=1242\u001b[0m\n", "\u001b[31mpytest.mark\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1238, end=1242\u001b[0m\n", "\u001b[31mmark\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=1237, end=1238\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1231, end=1237\u001b[0m\n", "\u001b[31mpytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=@, start=1230, end=1231\u001b[0m\n", "\u001b[31m@\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=function_definition, start=776, end=1227\u001b[0m\n", "\u001b[31mdef test_generate_cuda_graph(llama_350m_fp8_graphed_fixture):\n", "    \"\"\"Ensure that the model generates the correct outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_graphed_fixture\n", "    fwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=128,  # to account for graph capturing\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=block, start=842, end=1227\u001b[0m\n", "\u001b[31m\"\"\"Ensure that the model generates the correct outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_graphed_fixture\n", "    fwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=128,  # to account for graph capturing\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=964, end=1227\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=128,  # to account for graph capturing\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=964, end=1227\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=128,  # to account for graph capturing\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=1025, end=1227\u001b[0m\n", "\u001b[31m(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=128,  # to account for graph capturing\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=1226, end=1227\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=comment, start=1189, end=1221\u001b[0m\n", "\u001b[31m# to account for graph capturing\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1186, end=1187\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=1170, end=1186\u001b[0m\n", "\u001b[31mextra_kv_len=128\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1183, end=1186\u001b[0m\n", "\u001b[31m128\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1182, end=1183\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1170, end=1182\u001b[0m\n", "\u001b[31mextra_kv_len\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1160, end=1161\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=1140, end=1160\u001b[0m\n", "\u001b[31mallowed_mismatches=1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=1159, end=1160\u001b[0m\n", "\u001b[31m1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=1158, end=1159\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1140, end=1158\u001b[0m\n", "\u001b[31mallowed_mismatches\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1130, end=1131\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1107, end=1130\u001b[0m\n", "\u001b[31m_LLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1097, end=1098\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1074, end=1097\u001b[0m\n", "\u001b[31m_LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1064, end=1065\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1052, end=1064\u001b[0m\n", "\u001b[31mattn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=1042, end=1043\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=1035, end=1042\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=1025, end=1026\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=964, end=1025\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_if_model_generates_target_sequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=985, end=1025\u001b[0m\n", "\u001b[31mcheck_if_model_generates_target_sequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=984, end=985\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=964, end=984\u001b[0m\n", "\u001b[31mfwd_model_test_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=905, end=959\u001b[0m\n", "\u001b[31mstep_fn, attn_factory = llama_350m_fp8_graphed_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=905, end=959\u001b[0m\n", "\u001b[31mstep_fn, attn_factory = llama_350m_fp8_graphed_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=929, end=959\u001b[0m\n", "\u001b[31mllama_350m_fp8_graphed_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=927, end=928\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=pattern_list, start=905, end=926\u001b[0m\n", "\u001b[31mstep_fn, attn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=914, end=926\u001b[0m\n", "\u001b[31mattn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=912, end=913\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=905, end=912\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=842, end=900\u001b[0m\n", "\u001b[31m\"\"\"Ensure that the model generates the correct outputs.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=842, end=900\u001b[0m\n", "\u001b[31m\"\"\"Ensure that the model generates the correct outputs.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=897, end=900\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=842, end=845\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=:, start=836, end=837\u001b[0m\n", "\u001b[31m:\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=parameters, start=804, end=836\u001b[0m\n", "\u001b[31m(llama_350m_fp8_graphed_fixture)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=835, end=836\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=805, end=835\u001b[0m\n", "\u001b[31mllama_350m_fp8_graphed_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=804, end=805\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=780, end=804\u001b[0m\n", "\u001b[31mtest_generate_cuda_graph\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=def, start=776, end=779\u001b[0m\n", "\u001b[31mdef\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=function_definition, start=311, end=773\u001b[0m\n", "\u001b[31mdef test_generate(llama_350m_fp8_fixture):\n", "    \"\"\"Ensure that the model generates the correct outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_fixture\n", "    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])\n", "    fwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=16,\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=block, start=358, end=773\u001b[0m\n", "\u001b[31m\"\"\"Ensure that the model generates the correct outputs.\"\"\"\n", "    step_fn, attn_factory = llama_350m_fp8_fixture\n", "    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])\n", "    fwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=16,\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=545, end=773\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=16,\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=545, end=773\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_if_model_generates_target_sequence(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=16,\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=606, end=773\u001b[0m\n", "\u001b[31m(\n", "        step_fn,\n", "        attn_factory,\n", "        _LLAMA_350M_PROMPT_TOKS,\n", "        _LLAMA_350M_OUTPUT_TOKS,\n", "        allowed_mismatches=1,\n", "        extra_kv_len=16,\n", "    )\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=772, end=773\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=766, end=767\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=751, end=766\u001b[0m\n", "\u001b[31mextra_kv_len=16\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=764, end=766\u001b[0m\n", "\u001b[31m16\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=763, end=764\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=751, end=763\u001b[0m\n", "\u001b[31mextra_kv_len\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=741, end=742\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=721, end=741\u001b[0m\n", "\u001b[31mallowed_mismatches=1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=740, end=741\u001b[0m\n", "\u001b[31m1\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=739, end=740\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=721, end=739\u001b[0m\n", "\u001b[31mallowed_mismatches\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=711, end=712\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=688, end=711\u001b[0m\n", "\u001b[31m_LLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=678, end=679\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=655, end=678\u001b[0m\n", "\u001b[31m_LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=645, end=646\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=633, end=645\u001b[0m\n", "\u001b[31mattn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=623, end=624\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=616, end=623\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=606, end=607\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=545, end=606\u001b[0m\n", "\u001b[31mfwd_model_test_utils.check_if_model_generates_target_sequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=566, end=606\u001b[0m\n", "\u001b[31mcheck_if_model_generates_target_sequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=565, end=566\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=545, end=565\u001b[0m\n", "\u001b[31mfwd_model_test_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=472, end=540\u001b[0m\n", "\u001b[31mstep_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=472, end=540\u001b[0m\n", "\u001b[31mstep_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=call, start=482, end=540\u001b[0m\n", "\u001b[31mfwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=argument_list, start=510, end=540\u001b[0m\n", "\u001b[31m(step_fn, round_sizes=[8, 16])\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=539, end=540\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=keyword_argument, start=520, end=539\u001b[0m\n", "\u001b[31mround_sizes=[8, 16]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=list, start=532, end=539\u001b[0m\n", "\u001b[31m[8, 16]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=], start=538, end=539\u001b[0m\n", "\u001b[31m]\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=536, end=538\u001b[0m\n", "\u001b[31m16\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=534, end=535\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=integer, start=533, end=534\u001b[0m\n", "\u001b[31m8\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=[, start=532, end=533\u001b[0m\n", "\u001b[31m[\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=531, end=532\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=520, end=531\u001b[0m\n", "\u001b[31mround_sizes\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=518, end=519\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=511, end=518\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=510, end=511\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=482, end=510\u001b[0m\n", "\u001b[31mfwd_utils.PaddedStepFunction\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=492, end=510\u001b[0m\n", "\u001b[31mPaddedStepFunction\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=491, end=492\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=482, end=491\u001b[0m\n", "\u001b[31mfwd_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=480, end=481\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=472, end=479\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=421, end=467\u001b[0m\n", "\u001b[31mstep_fn, attn_factory = llama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=421, end=467\u001b[0m\n", "\u001b[31mstep_fn, attn_factory = llama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=445, end=467\u001b[0m\n", "\u001b[31mllama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=443, end=444\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=pattern_list, start=421, end=442\u001b[0m\n", "\u001b[31mstep_fn, attn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=430, end=442\u001b[0m\n", "\u001b[31mattn_factory\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=428, end=429\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=421, end=428\u001b[0m\n", "\u001b[31mstep_fn\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=358, end=416\u001b[0m\n", "\u001b[31m\"\"\"Ensure that the model generates the correct outputs.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=358, end=416\u001b[0m\n", "\u001b[31m\"\"\"Ensure that the model generates the correct outputs.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=413, end=416\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=358, end=361\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=:, start=352, end=353\u001b[0m\n", "\u001b[31m:\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=parameters, start=328, end=352\u001b[0m\n", "\u001b[31m(llama_350m_fp8_fixture)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=), start=351, end=352\u001b[0m\n", "\u001b[31m)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=329, end=351\u001b[0m\n", "\u001b[31mllama_350m_fp8_fixture\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=(, start=328, end=329\u001b[0m\n", "\u001b[31m(\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=315, end=328\u001b[0m\n", "\u001b[31mtest_generate\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=def, start=311, end=314\u001b[0m\n", "\u001b[31mdef\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=250, end=308\u001b[0m\n", "\u001b[31m_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=250, end=308\u001b[0m\n", "\u001b[31m_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=276, end=308\u001b[0m\n", "\u001b[31mfwd_llama.LLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=286, end=308\u001b[0m\n", "\u001b[31mLLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=285, end=286\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=276, end=285\u001b[0m\n", "\u001b[31mfwd_llama\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=274, end=275\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=250, end=273\u001b[0m\n", "\u001b[31m_LLAMA_350M_OUTPUT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=191, end=249\u001b[0m\n", "\u001b[31m_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=assignment, start=191, end=249\u001b[0m\n", "\u001b[31m_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=attribute, start=217, end=249\u001b[0m\n", "\u001b[31mfwd_llama.LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=227, end=249\u001b[0m\n", "\u001b[31mLLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=226, end=227\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=217, end=226\u001b[0m\n", "\u001b[31mfwd_llama\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype==, start=215, end=216\u001b[0m\n", "\u001b[31m=\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=191, end=214\u001b[0m\n", "\u001b[31m_LLAMA_350M_PROMPT_TOKS\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import_from_statement, start=145, end=189\u001b[0m\n", "\u001b[31mfrom base.fastforward.llama import fwd_llama\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=180, end=189\u001b[0m\n", "\u001b[31mfwd_llama\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=180, end=189\u001b[0m\n", "\u001b[31mfwd_llama\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import, start=173, end=179\u001b[0m\n", "\u001b[31mimport\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=150, end=172\u001b[0m\n", "\u001b[31mbase.fastforward.llama\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=167, end=172\u001b[0m\n", "\u001b[31m<PERSON>ma\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=166, end=167\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=155, end=166\u001b[0m\n", "\u001b[31mfastforward\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=154, end=155\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=150, end=154\u001b[0m\n", "\u001b[31mbase\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=from, start=145, end=149\u001b[0m\n", "\u001b[31mfrom\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import_from_statement, start=84, end=144\u001b[0m\n", "\u001b[31mfrom base.fastforward import fwd_model_test_utils, fwd_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=135, end=144\u001b[0m\n", "\u001b[31mfwd_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=135, end=144\u001b[0m\n", "\u001b[31mfwd_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=,, start=133, end=134\u001b[0m\n", "\u001b[31m,\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=113, end=133\u001b[0m\n", "\u001b[31mfwd_model_test_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=113, end=133\u001b[0m\n", "\u001b[31mfwd_model_test_utils\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import, start=106, end=112\u001b[0m\n", "\u001b[31mimport\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=89, end=105\u001b[0m\n", "\u001b[31mbase.fastforward\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=94, end=105\u001b[0m\n", "\u001b[31mfastforward\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=., start=93, end=94\u001b[0m\n", "\u001b[31m.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=89, end=93\u001b[0m\n", "\u001b[31mbase\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=from, start=84, end=88\u001b[0m\n", "\u001b[31mfrom\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import_statement, start=69, end=82\u001b[0m\n", "\u001b[31mimport pytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=76, end=82\u001b[0m\n", "\u001b[31mpytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=76, end=82\u001b[0m\n", "\u001b[31mpytest\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import, start=69, end=75\u001b[0m\n", "\u001b[31mimport\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import_from_statement, start=40, end=67\u001b[0m\n", "\u001b[31mfrom typing import Sequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=59, end=67\u001b[0m\n", "\u001b[31mSequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=59, end=67\u001b[0m\n", "\u001b[31mSequence\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=import, start=52, end=58\u001b[0m\n", "\u001b[31mimport\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=dotted_name, start=45, end=51\u001b[0m\n", "\u001b[31mtyping\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=identifier, start=45, end=51\u001b[0m\n", "\u001b[31mtyping\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=from, start=40, end=44\u001b[0m\n", "\u001b[31mfrom\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=expression_statement, start=0, end=38\u001b[0m\n", "\u001b[31m\"\"\"Smoke test LLAMA models in e4m3.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=string, start=0, end=38\u001b[0m\n", "\u001b[31m\"\"\"Smoke test LLAMA models in e4m3.\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=35, end=38\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[32mtype=\", start=0, end=3\u001b[0m\n", "\u001b[31m\"\"\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["import termcolor\n", "\n", "for node in target_candidates[1:]:\n", "    print(termcolor.colored(f\"type={node.type}, start={node.start_byte}, end={node.end_byte}\", color=\"green\"))\n", "    print(termcolor.colored(decode_bytes(node.text), color=\"red\"))\n", "    print(\"-\" * 100)\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n"]}], "source": ["x = 'r\"\"\"xxx\"\"\"'\n", "print(x.startswith(('r\"\"\"', \"1\")))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}