{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "from research.data.spark.utils import AugmentK8sSparkSession\n", "from datetime import datetime\n", "\n", "time_stamp = datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "root_url = f\"s3a://augment-temporary/dxy/playground/{time_stamp}\"\n", "\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"64G\",\n", "    \"spark.executor.memory\": \"32G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "    \"spark.task.cpus\": \"2\",\n", "}\n", "\n", "spark: AugmentK8sSparkSession = k8s_session(\n", "    max_workers=512,\n", "    conf=spark_conf,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.rag.exps.utils import load_repos_from_stack\n", "\n", "stack_df, top_languages = load_repos_from_stack(\n", "    spark,\n", "    repo_min_size=500000,\n", "    repo_max_size=100000000,\n", "    repo_languages=[\"python\"],\n", "    limit_repos=2000,\n", "    random_seed=888,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(top_languages)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["random_repo_url = f\"{root_url}/random_500_repos\"\n", "stack_df.write.parquet(random_repo_url, mode=\"overwrite\")\n", "print(f\"Write into {random_repo_url}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tree_sitter as ts\n", "from base.static_analysis.parsing import TsParsedFile\n", "\n", "\n", "def customized_get_node_weight(node: ts.<PERSON>, pfile: TsParsedFile) -> float:\n", "    text = pfile.code[node.start_byte : node.end_byte].lower()\n", "    num_chars = len(text)\n", "    num_lines = text.count(\"\\n\")\n", "    if num_chars > 2500 or num_lines > 100:\n", "        return 0.0\n", "    if node.type == \"comment\":\n", "        return 0.0\n", "    if text.startswith(\"assert\"):\n", "        return 1.0\n", "    else:\n", "        return 0.0\n", "    # has_test_identifier = \"raise\" in text or \"assert\" in text or \"check\" in text\n", "\n", "    # has_number = any(c.isdigit() for c in text)\n", "    # has_string = any(c == '\"' or c == \"'\" for c in text)\n", "    # scale = 0\n", "    # if num_lines <= 2:\n", "    #     scale = 0.4\n", "    # elif num_lines <= 6:\n", "    #     scale = 1.0\n", "    # elif num_lines <= 12:\n", "    #     scale = 0.6\n", "    # else:\n", "    #     scale = 0.4\n", "\n", "    # if has_test_identifier:\n", "    #     if has_number and not has_string:\n", "    #         weight = 5.0\n", "    #     elif has_number:\n", "    #         weight = 2.0\n", "    #     elif has_string:\n", "    #         weight = 0.8\n", "    #     else:\n", "    #         weight = 0.2\n", "    # else:\n", "    #     weight = 0.0\n", "    # return weight * scale"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import CSTFimSampler, DefaultCorruptionNodesPicker\n", "\n", "REPO_LANGUAGES = [\"python\"]\n", "SAMPLE_LANGUAGES = [\"python\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "# random_repo_fim_url = f\"{root_url}/random_500_repos_fim\"\n", "random_repo_url = f\"{root_url}/random_500_repos\"\n", "root_url = \"s3a://augment-temporary/dxy/playground/2024-06-17_17-42-36/\"\n", "random_repo_fim_samples_url = f\"{root_url}/random_500_repos_samples_fim\"\n", "sampler = CSTFimSampler(\n", "    pick_whole_node_rate=0.7,\n", "    corruption_nodes_picker=DefaultCorruptionNodesPicker(corruption_expansion_rate=0.4),\n", ")\n", "\n", "job_results = repo_to_fim_samples_wrapper(\n", "    random_repo_url,\n", "    random_repo_fim_samples_url,\n", "    spark=spark,\n", "    config_fim=FIMSampleConfig(\n", "        fim_version=FimDataProcessor.VERSION,\n", "        repo_languages=REPO_LANGUAGES,\n", "        sample_languages=SAMPLE_LANGUAGES,\n", "        retrieval_languages=RETRIEVAL_LANGUAGES,\n", "        only_keep_unit_test_file=True,\n", "        every_n_lines=200,\n", "        max_problems_per_file=5,\n", "        small_downsampled_probability=0.1,\n", "        small_downsample_char_threshold=1500,\n", "        small_filter_char_threshold=50,\n", "    ),\n", "    config_retrieval=RogueRetrievalConfig(\n", "        repo_languages=REPO_LANGUAGES,\n", "        sample_languages=SAMPLE_LANGUAGES,\n", "        retrieval_languages=RETRIEVAL_LANGUAGES,\n", "        num_retrieved_chunks=0,\n", "    ),\n", "    sampler=sampler,\n", "    get_node_weight=customized_get_node_weight,\n", ")\n", "print(f\"Write into {random_repo_fim_samples_url}\")\n", "# Convert it to a list of dict\n", "samples_as_dict = (\n", "    spark.read.parquet(random_repo_fim_samples_url).toPandas().to_dict(orient=\"records\")\n", ")\n", "print(f\"There are {len(samples_as_dict)} repositories.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "from research.data.spark.utils import AugmentK8sSparkSession\n", "from datetime import datetime\n", "\n", "time_stamp = datetime.now().strftime(\"%Y-%m-%d_%H-%M-%S\")\n", "root_url = f\"s3a://augment-temporary/dxy/playground/{time_stamp}\"\n", "\n", "spark_conf = {\n", "    \"spark.executor.pyspark.memory\": \"64G\",\n", "    \"spark.executor.memory\": \"32G\",\n", "    \"spark.sql.parquet.columnarReaderBatchSize\": \"64\",\n", "    \"spark.task.cpus\": \"2\",\n", "}\n", "\n", "spark: AugmentK8sSparkSession = k8s_session(\n", "    max_workers=512,\n", "    conf=spark_conf,\n", ")\n", "\n", "random_repo_fim_samples_url = \"s3a://augment-temporary/dxy/playground/2024-06-17_17-42-36/random_500_repos_samples_fim\"\n", "samples_as_dict = (\n", "    spark.read.parquet(random_repo_fim_samples_url).toPandas().to_dict(orient=\"records\")\n", ")\n", "print(f\"There are {len(samples_as_dict)} repositories.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from research.core.types import Document\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "\n", "all_samples = []\n", "for repo_w_samples in samples_as_dict:\n", "    all_docs = pickle.loads(repo_w_samples[\"all_docs\"])\n", "    all_docs = [Document.new(doc.text, doc.path) for doc in all_docs]\n", "    for sample in repo_w_samples[\"samples\"]:\n", "        sample = sample.asDict()\n", "        sample[\"all_docs\"] = all_docs\n", "        all_samples.append(sample)\n", "print(f\"There are {len(all_samples)} samples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the remote system\n", "from research.eval.harness.systems.remote_completion_system import (\n", "    RemoteCompletionSystem,\n", ")\n", "from research.core.types import CharRange\n", "from research.core.model_input import ModelInput\n", "from research.eval.harness.systems.remote_lib import (\n", "    AugmentClientConfig,\n", "    RemoteCompletionConfig,\n", "    RemoteRetrieverConfig,\n", ")\n", "\n", "\n", "def build_model_input(data: dict):\n", "    return ModelInput(\n", "        prefix=data[\"prefix\"],\n", "        suffix=data[\"suffix\"],\n", "        path=data[\"file_path\"],\n", "        extra={\n", "            \"ground_truth_span\": <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "                data[\"middle_char_start\"], data[\"middle_char_end\"]\n", "            )\n", "        },\n", "    )\n", "\n", "\n", "systems = dict()\n", "systems[\"StarCoder-1\"] = RemoteCompletionSystem.from_yaml_config(\n", "    {\n", "        \"client\": {\"url\": \"https://dogfood.api.augmentcode.com\"},\n", "        \"retriever\": {\"warn_on_indexing_timeout\": True},\n", "        \"model_name\": \"roguesl-v2-16b-seth616-rec\",\n", "    }\n", ")\n", "\n", "systems[\"StarCoder-2\"] = RemoteCompletionSystem.from_yaml_config(\n", "    {\n", "        \"client\": {\"url\": \"https://dogfood.api.augmentcode.com\"},\n", "        \"retriever\": {\"warn_on_indexing_timeout\": True},\n", "        \"model_name\": \"star2sl-16b-seth616-rec\",\n", "    }\n", ")\n", "\n", "systems[\"StarCoder-2 (Signature)\"] = RemoteCompletionSystem.from_yaml_config(\n", "    {\n", "        \"client\": {\"url\": \"https://dogfood.api.augmentcode.com\"},\n", "        \"retriever\": {\"warn_on_indexing_timeout\": True, \"wait_indexing_retry_count\": 4},\n", "        \"completion\": {\"warn_on_unknown_blobs\": True},\n", "        \"model_name\": \"elden-15b-rec\",\n", "    }\n", ")\n", "for name, system in systems.items():\n", "    system.load()\n", "    print(f\"Finish loading the system {name}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.rag.exps.utils import shift_middle_to_prefix\n", "from termcolor import colored\n", "\n", "\n", "def compare_models(systems: dict, sample: dict, shift_middle: int = 0):\n", "    # show_truncated_completion(sample, 15, 5)\n", "    print(\"-\" * 32)\n", "    for name, system in systems.items():\n", "        print(f\"System: {name}\")\n", "        sample = shift_middle_to_prefix(sample, shift_middle)\n", "        model_input = build_model_input(sample)\n", "        system.clear_retriever()\n", "        system.add_docs(sample[\"all_docs\"])\n", "        result = system.generate(model_input)\n", "        print(colored(result.generated_text, color=\"red\", on_color=\"on_white\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[2]\n", "sample = shift_middle_to_prefix(sample, 0)\n", "show_truncated_completion(sample, 15, 5)\n", "# compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[2]\n", "sample = shift_middle_to_prefix(sample, 4)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[4]\n", "sample = shift_middle_to_prefix(sample, 9)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[6]\n", "sample = shift_middle_to_prefix(sample, 32)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[14]\n", "sample = shift_middle_to_prefix(sample, 48)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[24]\n", "sample = shift_middle_to_prefix(sample, 0)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[320]\n", "sample = shift_middle_to_prefix(sample, 8)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = all_samples[923]\n", "sample = shift_middle_to_prefix(sample, 8)\n", "show_truncated_completion(sample, 15, 5)\n", "compare_models(systems, sample)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}