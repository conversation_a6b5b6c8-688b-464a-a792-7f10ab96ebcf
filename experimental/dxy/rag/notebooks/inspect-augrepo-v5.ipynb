{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 2870 files in the repository.\n"]}], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "import random\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    if language is None:\n", "        continue\n", "    # print(str(relative_path))\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    # if \"research/fastbackward/tests/test_checkpointing_utils.py\" not in str(relative_path):\n", "    #     continue\n", "    # if \"research/data/tests/collection/test_stackexchange.py\" not in str(relative_path):\n", "    #     continue\n", "    # if \"research/data/tests/synthetic_code_edit/test_seeds.py\" not in str(\n", "    #     relative_path\n", "    # ) and \"clients/vscode/src/__tests__/completions/supress-deleted-completionts.test.ts\" not in str(\n", "    #     relative_path\n", "    # ):\n", "    #     continue\n", "    # if \"clients/vscode/src/__tests__/completions/supress-deleted-completionts.test.ts\" not in str(\n", "    #     relative_path\n", "    # ):\n", "    #     continue\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")\n", "\n", "rng = random.Random(45)\n", "rng.shuffle(repo)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", ")\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "SAMPLE_LANGUAGES = [\"python\", \"typescript\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=50,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=104,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1404 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_fim_samples_from_repo_with_multiple_configs,\n", "    generate_retrieved_chunks_from_fim,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from research.core.artifacts import collect_artifacts\n", "from research.fim.fim_sampling_experimental import (\n", "    DefaultUnitTestCorruptionNodesPicker,\n", "    LiteralUnitTestCorruptionNodesPicker,\n", ")\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "node_picker = DefaultUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.3,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.3,\n", ")\n", "sampler = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker,\n", ")\n", "\n", "node_picker_literal = LiteralUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.5,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.0,\n", "    edit_similarity_threshold=0.3,\n", "    max_num_lines_per_node=10,\n", "    max_num_char_per_node=400,\n", ")\n", "sampler_literal = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker_literal,\n", ")\n", "samples = list(\n", "    generate_retrieved_chunks_from_fim(\n", "        repo,\n", "        generate_fim_samples_from_repo_with_multiple_configs(\n", "            repo,\n", "            [\n", "                (config_fim, sampler, node_picker.get_node_weight),\n", "                (config_fim, sampler_literal, node_picker_literal.get_node_weight),\n", "            ],\n", "        ),\n", "        config=config_retrieval,\n", "        retrieval_database=NullRetrievalDatabase(),\n", "        tokenizer=tokenizer,\n", "    )\n", ")\n", "\n", "print(f\"There are {len(samples)} final samples.\")\n", "rng.shuffle(samples)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=8e1b8b40-b2b6-4652-afce-91de42968ee1, last_request_id=37629249-f32b-496d-8f27-ce1b2e19d4c4)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='roguesl-v2-16b-seth616-rec', suggested_prefix_char_count=5184, suggested_suffix_char_count=5184, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=718f63cb-2867-4e18-8e6d-86d57bbec23b, last_request_id=45a85e09-cc01-4293-bf95-1b162b191b28)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='star2sl-16b-seth616-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=d0902f77-ea45-4029-a705-f69704614f35, last_request_id=75c75762-0671-4623-9fe8-816a91093f27)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='elden-15b-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='<PERSON>', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Go', vscode_name='go', extensions=['.go'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2 (Signature)\n"]}], "source": ["from experimental.dxy.rag.exps.utils_system import (\n", "    build_model_input,\n", "    load_all_systems,\n", "    compare_models,\n", ")\n", "\n", "systems = load_all_systems()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/collections/typesafe_dict_test.py\n", "\n", "\u001b[92m            \"base\": BaseType(),\n", "            \"derived\": DerivedType(),\n", "        }\n", "    )\n", "\n", "    assert dct.get_with_type(\"a\", int) == 1\n", "    assert dct.get_with_type(\"b\", str) == \"hello\"\n", "    assert dct.get_with_type(\"c\", float) == 1.0\n", "    assert dct.get_with_type(\"d\", list) == [1, 2, 3]\n", "    \u001b[0m\u001b[47m\u001b[30massert dct.get_with_type(\"e\", dict) == {\"a\": 1, \"b\": 2}\n", "    assert dct.get_with_type(\"f\", type(None)) is None\n", "    # Here, we are checking that the following calls don't exception.\n", "    assert dct.get_with_type(\"base\", BaseType) is not None\n", "    assert dct.get_with_type(\"derived\", DerivedType) is not None\n", "    assert dct.get_with_type(\"derived\", BaseType) is not None\u001b[0m\u001b[94m\n", "\n", "\n", "def test_get_with_optional_type():\n", "    \"\"\"Test getting `None` if the default hints `None` is allowed.\"\"\"\n", "    # Asserting how Python handles `Optional[T]` for an abundance of caution.\n", "    assert typing.Optional[int] == typing.Union[int, type(None)]\n", "\n", "    dct = TypesafeDict({\"a\": None})\n", "\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[0], 10, 10)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/datasets/hindsight_completion_test.py\n", "\n", "\u001b[92m\n", "@pytest.fixture\n", "def datum() -> HindsightCompletionDatum:\n", "    return HindsightCompletionDatum(\n", "        completion=CompletionDatum(\n", "            request_id=\"test-request-id\",\n", "            user_id=\"test-user-id\",\n", "            request=CompletionRequest(\n", "                prefix=\"test-prefix\",\n", "                \u001b[0m\u001b[47m\u001b[30msuffix=\"test-suffix\",\n", "                path=\"test-path\",\n", "                blob_names=[\"test-blob-name-2\"],\n", "                output_len=10,\n", "                timestamp=datetime(2024, 1, 1, tzinfo=timezone.utc),\n", "                position=CompletionPosition(\n", "                    prefix_begin=0,\n", "                    cursor_position=len(\"test-prefix\"),\n", "                    suffix_end=len(\"test-prefix\") + len(\"test-suffix\"),\n", "                    blob_name=\"test-blob-name\",\n", "                    original_prefix_length=len(\"test-prefix\"),\n", "                    original_suffix_length=len(\"test-suffix\"),\n", "                ),\n", "            )\u001b[0m\u001b[94m,\n", "            response=CompletionResponse(\n", "                text=\"test-text\",\n", "                model=\"test-model\",\n", "                skipped_suffix=\"test-skipped-suffix\",\n", "                suffix_replacement_text=\"test-suffix-replacement-text\",\n", "                unknown_blob_names=[\"test-unknown-blob-name\"],\n", "                retrieved_chunks=[\n", "                    RetrievedChunk(\n", "                        text=\"test-retrieval-chunk-text\",\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[1], 10, 10)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/caching/lru_cache_test.py\n", "\n", "\u001b[92m        thread.join()\n", "\n", "\n", "def test_lookup_stats_are_valid(cache: LRUCache[str, int, ...]):\n", "    stats = Stats()\n", "    cache.set_lookup_listener(stats.on_lookup)\n", "\n", "    stats.reset()\n", "    assert cache.get([\"key1\", \"missing1\", \"missing2\"]) == [1, None, None]\n", "    \u001b[0m\u001b[47m\u001b[30massert stats.lookup_stats\n", "    assert stats.lookup_stats.hits_count == 0\n", "    assert stats.lookup_stats.misses_count == 3\n", "\n", "    stats.reset()\n", "    assert cache.get([\"key1\", \"missing1\", \"missing2\"]) == [1, None, None]\u001b[0m\u001b[94m\n", "    assert stats.lookup_stats\n", "    assert stats.lookup_stats.hits_count == 1\n", "    assert stats.lookup_stats.misses_count == 2\n", "\n", "\n", "def test_lookup_stats_when_cache_missing_keys_are_valid(\n", "    cache_with_missing_keys: LRUCache[str, int, ...],\n", "):\n", "    cache = cache_with_missing_keys\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[2], 10, 10)\n", "\n", "def test_lookup_stats_are_valid(cache: LRUCache[str, int, ...]):\n", "\n", "\n", "    for thre ...\n", "        stats = Stats()\n", "        cache.set_lookup_listener(stats.on_lookup)\n", "\n", "        stats.reset()\n", "        assert cache.get([\"key1\", \"missing1\", \"missing2\"]) == [1, None, None]\n", "        assert stats.lookup_stats\n", "        assert stats.lookup_stats.hits_count == 0\n", "        assert stats.lookup_stats.misses_count == 3\n", "\n", "        stats.reset()\n", "        assert cache.get([\"key1\", \"missing1\", \"missing2\"]) == [1, None, None]\n", "        assert stats.lookup_stats\n", "        assert stats.lookup_stats.hits_count == 1\n", "        assert stats.lookup_stats.misses_count == 2\n", "\n", "    for xx in ...."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/prompt_format_completion/rogue_sl_prompt_formatter_test.py\n", "\n", "\u001b[92m            stateless_config=stateless_config,\n", "            component_order=component_order,\n", "        )\n", "\n", "\n", "def test_roguesl_prompt_formatter_token_quant():\n", "    \"\"\"Test of quantization logic works.\"\"\"\n", "    prefix = \"def aggregate(a,b):\\n\"\n", "    suffix = \"\\nreturn aggregated_output\\n\"\n", "    \u001b[0m\u001b[47m\u001b[30mexample_input = PromptInput(\n", "        path=\"src/example.py\",\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        prefix_begin=0,\n", "        retrieved_chunks=(),\n", "    )\n", "\n", "    stateless_config = StatelessCachingConfig(\n", "        nearby_prefix_token_len=5,\n", "        nearby_prefix_token_overlap=1,\n", "        quantize_token_len=5,\n", "    )\n", "\n", "    component_order = (\"path\", \"prefix\", \"suffix\", \"retrieval\", \"nearby_prefix\")\n", "\n", "    prompt = _format(\n", "        example_input,\n", "        stateless_config=stateless_config,\n", "        component_order=component_order,\n", "    )\n", "\n", "    expected_prompt = \"\".join(\n", "        [\n", "            \"<filename>src/example.py\\n\",\n", "            \"<fim_prefix>def aggregate(a,b\",\n", "            \"<fim_suffix>\\nreturn aggregated_output\\n\",\n", "            \"<|retrieval_section|>\",\n", "            \"<|nearby_prefix|>b):\\n\",\n", "            \"<fim_middle>\",\n", "        ]\n", "    )\n", "    assert prompt == expected_prompt\n", "\n", "\u001b[0m\u001b[94m\n", "def test_roguesl_prompt_formatter_char_quant():\n", "    \"\"\"Test of outer character quantization logic.\"\"\"\n", "    prefix = \"def aggregate(a,b):\\n\"\n", "    suffix = \"\\nreturn aggregated_output\\n\"\n", "    example_input = PromptInput(\n", "        path=\"src/example.py\",\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        prefix_begin=4,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[3], 10, 10)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/embedder_host/server/embedder_handler_test.py\n", "\n", "\u001b[92m        max_round_size=10,\n", "        max_seq_length=10,\n", "    )\n", "    tokens1 = list(range(10))\n", "    tokens2 = list(range(10, 20))\n", "\n", "    response = handler.calculate_embedding([tokens1, tokens2], request_id=\"test_rid\")\n", "    assert response.shape == (2, model.emb_dim)\n", "    # We rely on the fact that the null model uses the first token as the embedding\n", "    \u001b[0m\u001b[47m\u001b[30mnp.testing.assert_array_equal(response[0], np.full((model.emb_dim,), 0))\n", "    np.testing.assert_array_equal(response[1], np.full((model.emb_dim,), 10))\n", "\n", "\n", "def test_locking_embedder_handler_fwd_model():\n", "    \"\"\"Tests the embedder handler with the fwd testmodel.\"\"\"\n", "    model_spec = fwd_testmodel.get_dummy_modelspec()\n", "    step_fn = fwd_testmodel.generate_step_fn(model_spec)\n", "    max_length = 128\n", "    emb_model = embedder_model.EmbedderModelFFWD(\n", "        model_name=\"testmodel\",\n", "        step_fn=step_fn,\n", "        attn_factory=fwd_testmodel.get_attention_factory(\n", "            model_spec, ignore_first_n=128\n", "        ),\n", "        max_length=max_length,\n", "        round_sizes=[max_length],\n", "    )\n", "    handler = embedder_handler.LockingEmbedderHandler(\n", "        \"testmodel\",\n", "        model=emb_model,\n", "        max_round_size=max_length,\n", "        max_seq_length=max_length,\n", "    )\n", "    response = handler.calculate_embedding(\n", "        [[1, 2, 3, 4, 5, 6, 7, 8, 9]], request_id=\"test_rid\"\n", "    )\n", "    assert response.shape == (1, model_spec.vocab_size)\u001b[0m\u001b[94m\n", "\n", "\n", "def test_locking_embedder_handler_ethanol(ethanol6_model):\n", "    \"\"\"Tests the embedder handler with a real model.\"\"\"\n", "    step_fn, attn_factory, model_spec = ethanol6_model\n", "    max_length = 128\n", "    embedder = embedder_model.EmbedderModelFFWD(\n", "        model_name=\"testmodel\",\n", "        step_fn=step_fn,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[4], 10, 10)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/fastforward/codegen/fwd_codegen_test.py\n", "\n", "\u001b[92m        rtol=rtol,\n", "        atol=atol,\n", "    )\n", "\n", "    torch.testing.assert_close(\n", "        torch.argmax(logits_with_paddings[:-num_paddings], dim=-1),\n", "        torch.argmax(logits_no_paddings, dim=-1),\n", "    )\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mtorch.testing.assert_close(\n", "        attn_with_paddings._mc_attn._positions[0][:-1],\n", "        attn_no_paddings._mc_attn._positions[0][:-1],\n", "        rtol=rtol,\n", "        atol=atol,\n", "    )\n", "\n", "    torch.testing.assert_close(\n", "        attn_with_paddings._mc_attn._tokens,\n", "        attn_no_paddings._mc_attn._tokens,\n", "        rtol=rtol,\n", "        atol=atol,\n", "    )\n", "\n", "    torch.testing.assert_close(\n", "        attn_with_paddings._mc_attn._kv_by_device[0][0][:, :-1, :, :, :],\n", "        attn_no_paddings._mc_attn._kv_by_device[0][0][:, :-1, :, :, :],\n", "        rtol=rtol,\n", "        atol=atol,\n", "    )\n", "\n", "    torch.testing.assert_close(\n", "        attn_with_paddings._mc_attn._kv_by_device[0][1][:, :-1, :, :, :],\n", "        attn_no_paddings._mc_attn._kv_by_device[0][1][:, :-1, :, :, :],\n", "        rtol=rtol,\n", "        atol=atol,\n", "    )\u001b[0m\u001b[94m\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[5], 10, 10)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: tools/genie/expiration/expiration_watcher_test.py\n", "\n", "\u001b[92m    expired_role_binding = MagicMock()\n", "    expired_role_binding.metadata.annotations = {\"expires_at\": \"2023-12-31T23:59:59Z\"}\n", "    expired_role_binding.metadata.name = \"test-role-binding\"\n", "    expired_role_binding.metadata.namespace = \"test-namespace\"\n", "\n", "    mock_rbac_client.list_role_binding_for_all_namespaces.return_value.items = [\n", "        expired_role_binding\n", "    ]\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mexpiration_watcher._check_role_bindings()\n", "    mock_rbac_client.delete_namespaced_role_binding.assert_called_once_with(\n", "        name=expired_role_binding.metadata.name,\n", "        namespace=expired_role_binding.metadata.namespace,\n", "    )\n", "\n", "\n", "def test_cluster_role_binding_expiration(mock_now):\n", "    mock_rbac_client = MagicMock()\n", "    expiration_watcher = ExpirationWatcher(\n", "        watch_interval_seconds=60, rbac_client=mock_rbac_client\n", "    )\n", "\n", "    # Test that a ClusterRoleBinding is not deleted if it is not expired.\n", "    unexpired_cluster_role_binding = MagicMock()\n", "    unexpired_cluster_role_binding.metadata.annotations = {\n", "        \"expires_at\": \"2024-01-01T00:00:01Z\"\n", "    }\n", "    unexpired_cluster_role_binding.metadata.name = \"test-cluster-role-binding\"\n", "\n", "    mock_rbac_client.list_cluster_role_binding.return_value.items = [\n", "        unexpired_cluster_role_binding\n", "    ]\n", "\n", "    expiration_watcher._check_cluster_role_bindings()\n", "    mock_rbac_client.delete_cluster_role_binding.assert_not_called()\n", "\n", "    # Test that a ClusterRoleBinding is deleted if it is expired.\n", "    expired_cluster_role_binding = MagicMock()\n", "    expired_cluster_role_binding.metadata.annotations = {\n", "        \"expires_at\": \"2023-12-31T23:59:59Z\"\n", "    }\n", "    expired_cluster_role_binding.metadata.name = \"test-cluster-role-binding\"\n", "\n", "    mock_rbac_client.list_cluster_role_binding.return_value.items = [\n", "        expired_cluster_role_binding\n", "    ]\n", "\n", "    expiration_watcher._check_cluster_role_bindings()\n", "    mock_rbac_client.delete_cluster_role_binding.assert_called_once_with(\n", "        name=expired_cluster_role_binding.metadata.name\n", "    )\u001b[0m\u001b[94m\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[6], 10, 10)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/augment-api.test.ts\n", "\n", "\u001b[92m\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "\n", "        await expect(server.memorize(\"/example/path/file.txt\", \"\", \"\", [])).rejects.toThrowError(\n", "            \"Augment API URL must start with 'http://' or 'https://'\"\n", "        );\n", "    });\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mtest(\"no-completion-url\", async () => {\n", "        const config = generateMockConfig({\n", "            advanced: {\n", "                completionURL: \"\",\n", "            },\n", "        });\n", "        workspace.getConfiguration.mockReturnValueOnce(config);\n", "\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "\n", "        await expect(server.memorize(\"/example/path/file.txt\", \"\", \"\", [])).rejects.toThrowError(\n", "            \"Please configure Augment API URL\"\n", "        );\n", "    });\n", "\n", "    test(\"handle-trailing-slashes\", async () => {\n", "        const config = generateMockConfig({\n", "            advanced: {\n", "                completionURL: \"http://api.augmentcode.com/\",\n", "            },\n", "        });\n", "        workspace.getConfiguration.mockReturnValueOnce(config);\n", "\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/memorize\", {\n", "            mem_object_name: \"a\",\n", "        });\n", "\n", "        const result = await server.memorize(\"quicksort.py\", \"def quicksort(\", \"blob-name\", []);\n", "        expect(result.blobName).toBe(\"a\");\n", "    });\n", "\n", "    test(\"response-not-json\", async () => {\n", "        const kit = new AugmentAPItestKit();\n", "        await kit.testCompletionAPIError(\"this is a not a json document\");\n", "    });\n", "\n", "    test(\"blobs-sorted\", async () => {\n", "        // setup\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "        const unsortedBlobs = {\n", "            checkpointId: undefined,\n", "            addedBlobs: [\"d\", \"c\", \"b\", \"a\"],\n", "            deletedBlobs: [\"4\", \"2\", \"1\"],\n", "        };\n", "\n", "        // completions\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/completion\", {\n", "            completions: [\"hello\"],\n", "            unknown_blob_names: [],\n", "        });\n", "        await server.complete(\n", "            server.createRequestId(),\n", "            \"def quicksort(\",\n", "            \"\",\n", "            \"quicksort.py\",\n", "            \"quickSortBlobName\",\n", "            { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },\n", "            \"python\",\n", "            unsortedBlobs\n", "        );\n", "        let call = fetchSandbox.lastCall()!;\n", "        let body = JSON.parse(call[1]!.body!.toString()!);\n", "        expect(body).toHaveProperty(\"blobs\", {\n", "            checkpoint_id: null,\n", "            added_blobs: [\"a\", \"b\", \"c\", \"d\"],\n", "            deleted_blobs: [\"1\", \"2\", \"4\"],\n", "        });\n", "\n", "        // edits\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/edit\", {\n", "            modified_code: \"hello\",\n", "            checkpoint_not_found: false,\n", "        });\n", "        await server.editCode(\n", "            server.createRequestId(),\n", "            \"write something cool\",\n", "            \"def quicksort(\",\n", "            \"\",\n", "            \"\",\n", "            \"quicksort.py\",\n", "            \"quickSortBlobName\",\n", "            0,\n", "            0,\n", "            \"python\",\n", "            unsortedBlobs\n", "        );\n", "        call = fetchSandbox.lastCall()!;\n", "        body = JSON.parse(call[1]!.body!.toString()!);\n", "        expect(body).toHaveProperty(\"blobs\", {\n", "            checkpoint_id: null,\n", "            added_blobs: [\"a\", \"b\", \"c\", \"d\"],\n", "            deleted_blobs: [\"1\", \"2\", \"4\"],\n", "        });\n", "\n", "        // checkpoints\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/checkpoint-blobs\", {\n", "            new_checkpoint_id: \"1234\",\n", "        });\n", "        await server.checkpointBlobs(unsortedBlobs);\n", "        call = fetchSandbox.lastCall()!;\n", "        body = JSON.parse(call[1]!.body!.toString()!);\n", "        expect(body).toHaveProperty(\"blobs\", {\n", "            checkpoint_id: null,\n", "            added_blobs: [\"a\", \"b\", \"c\", \"d\"],\n", "            deleted_blobs: [\"1\", \"2\", \"4\"],\n", "        });\n", "    });\n", "    test(\"standalone report-error\", async () => {\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/report-error\", {});\n", "\n", "        const result = await server.reportError(\n", "            \"1234567890\",\n", "            \"my error message\",\n", "            \"my stack trace\",\n", "            [{ key: \"source\", value: \"unit test\" }]\n", "        );\n", "        expect(result).toBeUndefined();\n", "        const call = fetchSandbox.lastCall()!;\n", "        expect(call[1]!.headers).toHaveProperty(\"x-request-id\");\n", "        const body = JSON.parse(call[1]!.body!.toString()!);\n", "        expect(body).toHaveProperty(\"original_request_id\", \"1234567890\");\n", "        expect(body).toHaveProperty(\"sanitized_message\", \"my error message\");\n", "        expect(body).toHaveProperty(\"stack_trace\", \"my stack trace\");\n", "        expect(body).toHaveProperty(\"diagnostics\", [{ key: \"source\", value: \"unit test\" }]);\n", "    });\n", "    test(\"report-error called on completion error\", async () => {\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/report-error\", {});\n", "        fetchSandbox.postOnce(\n", "            \"http://api.augmentcode.com/completion\",\n", "            Promise.reject(<PERSON><PERSON><PERSON>(\"my completion error\"))\n", "        );\n", "\n", "        const requestId = server.createRequestId();\n", "        await expect(\n", "            server.complete(\n", "                requestId,\n", "                \"\",\n", "                \"\",\n", "                \"\",\n", "                \"\",\n", "                { prefixBegin: 0, cursorPosition: 0, suffixEnd: 0 },\n", "                \"\",\n", "                { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] }\n", "            )\n", "        ).rejects.toThrowError();\n", "        const call = fetchSandbox.lastCall()!;\n", "        expect(call[1]!.headers).toHaveProperty(\"x-request-id\");\n", "        const body = JSON.parse(call[1]!.body!.toString()!);\n", "        expect(body).toHaveProperty(\"original_request_id\", requestId);\n", "        expect(body).toHaveProperty(\n", "            \"sanitized_message\",\n", "            \"completion call failed with APIStatus unavailable\"\n", "        );\n", "        expect(body[\"diagnostics\"]).toContainEqual({\n", "            key: \"message\",\n", "            value: \"my completion error\",\n", "        });\n", "    });\n", "\n", "    test(\"client-metrics\", async () => {\n", "        const kit = new AugmentAPItestKit();\n", "        const server = kit.createAPIServer();\n", "        const metrics: Array<ClientMetric> = [\n", "            {\n", "                client_metric: \"example-metric\",\n", "                value: 123,\n", "            },\n", "        ];\n", "        fetchSandbox.postOnce(\"http://api.augmentcode.com/client-metrics\", {\n", "            metrics,\n", "        });\n", "\n", "        await server.clientMetrics(metrics);\n", "\n", "        const call = fetchSandbox.lastCall()!;\n", "        expect(call[1]!.headers).toHaveProperty(\"x-request-id\");\n", "        const body = JSON.parse(call[1]!.body!.toString()!);\n", "        expect(body).toHaveProperty(\"metrics\", metrics);\n", "    });\u001b[0m\u001b[94m\n", "});\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[7], 10, 10)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/chat_host/server/chat_handler_test.py\n", "\n", "\u001b[92m    _, request_context, _ = _run_chat(chat_handler, chat_request)\n", "\n", "    chat_prompt_input = ChatPromptInput(\"src/foo.py\", \"a\", \"b\", \"c\", \"d\", [], 0, 2, [])\n", "    chat_prompt_output = chat_handler.prompt_formatter.format_prompt(chat_prompt_input)\n", "\n", "    expected_InfererClient_kwargs = {\n", "        \"input_tokens\": chat_prompt_output.tokens,\n", "        \"max_output_length\": 256,\n", "        \"end_token_ids\": {chat_handler.tokenizer.special_tokens.eos},\n", "        \u001b[0m\u001b[47m\u001b[30m\"max_results\": 1,\n", "        \"top_k\": 11,\u001b[0m\u001b[94m\n", "        \"top_p\": 0.9,\n", "        \"temperature\": 0.5,\n", "        \"request_context\": request_context,\n", "        \"timeout_s\": 33.0,\n", "        \"sequence_id\": 0,\n", "    }\n", "\n", "    # This is needed, because seed is randomly sampled in _infer*\n", "    actual_kwargs = chat_handler.client.infer.call_args.kwargs\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[8], 10, 10)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/fastforward/cuda_graphs_test.py\n", "\n", "\u001b[92m\n", "\n", "def test_data_ptr_equality():\n", "    x = torch.zeros((8, 2), dtype=torch.float16, device=\"cuda\")\n", "    y = torch.zeros((8, 2), dtype=torch.float16, device=\"cuda\")\n", "    assert x.data_ptr() == x.data_ptr()\n", "    assert x.data_ptr() != y.data_ptr()\n", "    assert x.data_ptr() is not y.data_ptr()\n", "    assert x.data_ptr() == x[:2].data_ptr()\n", "    \u001b[0m\u001b[47m\u001b[30massert x.data_ptr() is not x[:2].data_ptr()\n", "    assert x.data_ptr() == x[0].data_ptr()\n", "    assert x.data_ptr() is not x[0].data_ptr()\n", "    assert x.data_ptr() == x[0, 0].data_ptr()\n", "    assert x.data_ptr() is not x[0, 0].data_ptr()\n", "    assert x.data_ptr() != x.clone().data_ptr()\n", "    assert x.data_ptr() is not x.clone().data_ptr()\n", "    assert x.clone().data_ptr() != x.clone().data_ptr()\n", "\n", "    x_copy = x.clone()\n", "    # behavior in hash maps\n", "    d = {\n", "        x.data_ptr(): 1,\n", "        x_copy.data_ptr(): 2,\n", "        x.data_ptr(): 3,\n", "    }\u001b[0m\u001b[94m\n", "    assert len(d) == 2\n", "    assert d[x.data_ptr()] == 3\n", "    with pytest.raises(KeyError):\n", "        # note that this KeyError depends on the previous\n", "        # copy of x to not have been garbage collected.\n", "        x_copy_2 = x.clone()\n", "        _ = d[x_copy_2.data_ptr()]\n", "\n", "    assert d[x[0].data_ptr()] == 3\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[9], 10, 10)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/fastforward/cached_attention_multigpu_test.py\n", "\n", "\u001b[92m        return result\n", "\n", "    return fn\n", "\n", "\n", "@pytest.mark.parametrize(\n", "    \"split_head_mode, multiquery\",\n", "    (\n", "        pytest.param(SplitHeadModes.NO_SPLIT, False, id=\"0\"),\n", "        \u001b[0m\u001b[47m\u001b[30mpytest.param(SplitHeadModes.NO_SPLIT, True, id=\"1\"),\n", "        pytest.param(SplitHeadModes.KV_HEADS, False, id=\"2\"),\n", "        pytest.param(SplitHeadModes.Q_PER_HEADS, True, id=\"3\")\u001b[0m\u001b[94m,\n", "    ),\n", ")\n", "def test_cached_attention_multigpu(split_head_mode: SplitHeadModes, multiquery: bool):\n", "    \"\"\"Test that cached attention produces identical results when running on 2 GPUs.\"\"\"\n", "    seq_len = 128\n", "    max_len = 1024\n", "    if multiquery:\n", "        queries_per_head = 16\n", "        num_heads = 1\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[10], 10, 10)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/augment-api.test.ts\n", "\n", "\u001b[92mimport {\n", "    APIServer,\n", "    APIServerImplWithErrorReporting,\n", "    BackModelInfo,\n", "    ClientMetric,\n", "    Model,\n", "} from \"../augment-api\";\n", "import { mockGetModelsResult } from \"../__mocks__/mock-modelinfo\";\n", "\n", "import { expect, test, afterEach } from \"@jest/globals\";\n", "\u001b[0m\u001b[47m\u001b[30mimport { generateMockConfig } from \"../__mocks__/mock-augment-config\";\u001b[0m\u001b[94m\n", "import { workspace, ExtensionContext, resetMockWorkspace } from \"../__mocks__/vscode-mocks\";\n", "import { AuthSessionStore } from \"../auth/auth-session-store\";\n", "\n", "class AugmentAPItestKit {\n", "    readonly configListener: AugmentConfigListener;\n", "    readonly auth: AuthSessionStore;\n", "\n", "    constructor() {\n", "        this.configListener = new AugmentConfigListener();\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[11], 10, 10)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/utils/keybindings.test.ts\n", "\n", "\u001b[92m                    },\n", "                });\n", "\n", "                keybindingWatcher = new KeybindingWatcher(mockConfigListener.config);\n", "                expect(\n", "                    keybindingWatcher.getKeybindingForCommand(\"vscode-augment.exampleCommand\")\n", "                ).toBeNull();\n", "            });\n", "\n", "            \u001b[0m\u001b[47m\u001b[30mtest(\"if no keybinding is found\", () => {\n", "                getUserDirectorySpy.mockReturnValue(null);\n", "                getAugmentExtensionPackageJsonSpy.mockReturnValue({\n", "                    contributes: {\n", "                        keybindings: [],\n", "                    },\n", "                });\n", "\n", "                keybindingWatcher = new KeybindingWatcher(mockConfigListener.config);\n", "                expect(keybindingWatcher.getKeybindingForCommand(\"nonexistent.command\")).toBeNull();\n", "            });\u001b[0m\u001b[94m\n", "        });\n", "\n", "        test(\"sets up a watcher for user keybindings\", () => {\n", "            jest.spyOn(os, \"platform\").mockReturnValue(\"win32\");\n", "            jest.spyOn(fs, \"watch\").mockReturnValue({\n", "                close: jest.fn(),\n", "            } as unknown as fs.<PERSON><PERSON><PERSON><PERSON>);\n", "            getUserDirectorySpy.mockReturnValue(\"user-directory\");\n", "            getAugmentExtensionPackageJsonSpy.mockReturnValue({\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[12], 10, 10)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/webview-panels/next-edit-panel.test.ts\n", "\n", "\u001b[92m                },\n", "            });\n", "\n", "            expect(commands.executeCommand).toHaveBeenCalledWith(\n", "                \"vscode.open\",\n", "                vscode.Uri.file(\"/home/<USER>/project/example-repo/sudir/example/file.ts\")\n", "            );\n", "        });\n", "\n", "        \u001b[0m\u001b[47m\u001b[30mit(\"should open file in different tab\", async () => {\n", "            const panel = kit.panel;\n", "\n", "            expect(panel.webview._onDidReceiveListeners.length).toBe(2);\n", "            await panel.webview._onDidReceiveListeners[0]({\n", "                type: WebViewMessageType.openFile,\n", "                data: {\n", "                    repoRoot: \"/home/<USER>/project/example-repo\",\n", "                    pathName: \"/home/<USER>/Code/settings.json\",\n", "                    differentTab: true,\n", "                },\n", "            });\n", "\n", "            expect(commands.executeCommand).toHaveBeenCalledWith(\n", "                \"vscode.openWith\",\n", "                vscode.Uri.file(\"/home/<USER>/Code/settings.json\"),\n", "                \"default\",\n", "                vscode.ViewColumn.Beside\n", "            );\n", "        });\n", "\n", "        it(\"should open file in different tab 2\", async () => {\n", "            const panel = kit.panel;\n", "\n", "            panel.viewColumn = vscode.ViewColumn.Two;\n", "\n", "            expect(panel.webview._onDidReceiveListeners.length).toBe(2);\n", "            await panel.webview._onDidReceiveListeners[0]({\n", "                type: WebViewMessageType.openFile,\n", "                data: {\n", "                    repoRoot: \"/home/<USER>/project/example-repo\",\n", "                    pathName: \"/home/<USER>/Code/settings.json\",\n", "                    differentTab: true,\n", "                },\n", "            });\n", "\n", "            expect(commands.executeCommand).toHaveBeenCalledWith(\n", "                \"vscode.openWith\",\n", "                vscode.Uri.file(\"/home/<USER>/Code/settings.json\"),\n", "                \"default\",\n", "                vscode.ViewColumn.One\n", "            );\n", "        });\n", "\n", "        it(\"should open file in different tab 1\", async () => {\n", "            const panel = kit.panel;\n", "\n", "            panel.viewColumn = vscode.ViewColumn.One;\n", "\n", "            expect(panel.webview._onDidReceiveListeners.length).toBe(2);\n", "            await panel.webview._onDidReceiveListeners[0]({\n", "                type: WebViewMessageType.openFile,\n", "                data: {\n", "                    repoRoot: \"/home/<USER>/project/example-repo\",\n", "                    pathName: \"/home/<USER>/Code/settings.json\",\n", "                    differentTab: true,\n", "                },\n", "            });\n", "\n", "            expect(commands.executeCommand).toHaveBeenCalledWith(\n", "                \"vscode.openWith\",\n", "                vscode.Uri.file(\"/home/<USER>/Code/settings.json\"),\n", "                \"default\",\n", "                vscode.ViewColumn.Two\n", "            );\n", "        });\n", "\n", "        it(\"should load config\", async () => {\n", "            const panel = kit.panel;\n", "            panel.webview.postMessage = jest.fn().mockImplementation(() => {\n", "                return Promise.resolve();\n", "            });\n", "\n", "            expect(panel.webview._onDidReceiveListeners.length).toBe(2);\n", "            await panel.webview._onDidReceiveListeners[0]({\n", "                type: WebViewMessageType.nextEditLoaded,\n", "            });\n", "\n", "            const wantMessage: WebViewMessage = {\n", "                type: WebViewMessageType.nextEditInitialize,\n", "                data: undefined,\n", "            };\n", "            expect(panel.webview.postMessage).toHaveBeenCalledTimes(1);\n", "            expect(panel.webview.postMessage).toHaveBeenCalledWith(wantMessage);\n", "        });\n", "    \u001b[0m\u001b[94m});\n", "});\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[13], 10, 10)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/workspace/open-file-manager.test.ts\n", "\n", "\u001b[92m                repoId,\n", "                re<PERSON><PERSON><PERSON>,\n", "                document,\n", "                idx * rangeSize,\n", "                to<PERSON><PERSON><PERSON>,\n", "                toReplace.length\n", "            );\n", "\n", "            const chunkInfo = kit.getRecentChunkInfo(openFileManager, true);\n", "            \u001b[0m\u001b[47m\u001b[30mexpect(chunkInfo.length).toBeGreaterThan(0);\n", "            expect(chunkInfo.length).toBeLessThanOrEqual(maxTotalChunks * 2);\u001b[0m\u001b[94m\n", "            chunkCountHWM = Math.max(chunkCountHWM, chunkInfo.length);\n", "        }\n", "        expect(chunkCountHWM).toBeLessThanOrEqual(maxTotalChunks * 2);\n", "    });\n", "\n", "    // Verifies that the blob manager doesn't accumulate an unbounded number of \"recent\" chunks\n", "    // from multiple files.\n", "    test(\"gc chunks, multi file\", async () => {\n", "        const documentSize = 10;\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[14], 10, 10)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/gpt-neox/tests/model/test_attention.py\n", "\n", "\u001b[92m    )\n", "    # pylint: enable=unexpected-keyword-arg\n", "    if mask_type != \"causal\":\n", "        # set to something other than causal, as flash attn uses this to\n", "        # determine causal attn mode.\n", "        args.attn_mask_mode = \"other\"\n", "\n", "    init_method, output_layer_init_method = get_init_methods(args)\n", "    attn_mask_func = gpt2_model.gpt2_attention_mask_func\n", "    \u001b[0m\u001b[47m\u001b[30mmodule = transformer.ParallelSelfAttention(\n", "        args, attn_mask_func, init_method, output_layer_init_method, layer_number=0\n", "    )\n", "    args_with_flash = dataclasses.replace(args, flash_attention=True)  # copy\n", "    assert args_with_flash.flash_attention\n", "    module_with_flash = transformer.ParallelSelfAttention(\n", "        args_with_flash,\n", "        attn_mask_func,\n", "        init_method,\n", "        output_layer_init_method,\n", "        layer_number=0,\n", "    )\n", "\n", "    device = torch.cuda.current_device()\n", "    prng = torch.Generator(device=device)\n", "    prng.manual_seed(1337)\u001b[0m\u001b[94m\n", "\n", "    def fresh_qkv(batch: int, num_heads: int, seq_len: int, qkv_dim: int, dtype=dtype):\n", "        qkv_shape = (\n", "            seq_len,\n", "            batch,\n", "            num_heads,\n", "            qkv_dim,\n", "        )  # watch out: untypical qkv_shape to be compatible with megatron\n", "        return torch.randn(generator=prng, dtype=dtype, size=qkv_shape, device=device)\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[15], 10, 10)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/completions/pending-completions.test.ts\n", "\n", "\u001b[92m        expect(got!.completions.length).toBe(1);\n", "        expect(got!.completions[0]).toEqual(\n", "            new AugmentCompletion(completionText, \"\", \"\", {\n", "                startOffset: cursorPos,\n", "                endOffset: cursorPos,\n", "            })\n", "        );\n", "\n", "        const request2 = kit.createCompletionRequest(document);\n", "        \u001b[0m\u001b[47m\u001b[30mrequest2.completions = [\n", "            new AugmentCompletion(completionText, \"\", \"\", {\n", "                startOffset: cursorPos,\n", "                endOffset: cursorPos,\n", "            }),\n", "            new AugmentCompletion(completionText, \"\", \"\", {\n", "                startOffset: cursorPos,\n", "                endOffset: cursorPos,\n", "            }),\n", "        ];\n", "        kit._completionEventEmitter.fire(request2);\n", "\n", "        const got2 = await kit.pendingCompletions.getPendingCompletion(\n", "            document,\n", "            new Position(0, cursorPos)\n", "        );\n", "        expect(got2).toBeUndefined();\u001b[0m\u001b[94m\n", "    });\n", "\n", "    test(\"ignore text document change when there is no pending completion\", async () => {\n", "        // Create and open a file with the given contents. Choose an arbitrary point as our\n", "        // cursor position within the file.\n", "        const fileContents = \"0123456789\";\n", "        const document = kit.createAndOpenFile(\"/some-file.py\", fileContents);\n", "        const cursorPos = Math.floor(fileContents.length / 2);\n", "\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[16], 10, 10)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/retrieval/tests/test_prompt_formatters.py\n", "\n", "\u001b[92m        # Trimming with SOS + path\n", "\n", "        for max_tokens, expect_tokens in {\n", "            1: [101],\n", "            3: [101, 24, 25],\n", "            4: [101, 24, 25, 26],\n", "            5: [101, 24, 25, 26, 100],\n", "            6: [101, 24, 25, 26, 100, 5],\n", "            7: [101, 24, 25, 26, 100, 4, 5],\n", "            \u001b[0m\u001b[47m\u001b[30m8: [101, 24, 25, 26, 100, 3, 4, 5],\n", "            9: [101, 24, 25, 26, 100, 2, 3, 4, 5],\n", "            10: [101, 24, 25, 26, 100, 1, 2, 3, 4, 5],\u001b[0m\u001b[94m\n", "            11: [101, 24, 25, 26, 100, 1, 2, 3, 4, 5],\n", "        }.items():\n", "            formatter = query_formatters.SimpleQueryFormatter(\n", "                max_tokens=max_tokens,\n", "                add_sos=True,\n", "                add_path=True,\n", "            )\n", "            formatter.tokenizer = MockTokenizer()\n", "            got_tokens, _ = formatter.prepare_prompt(\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[17], 10, 10)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/auth/central/server/tenant_map_test.py\n", "\n", "\u001b[92m\n", "from services.auth.central.server import auth_pb2\n", "from services.tenant_watcher import tenant_watcher_pb2\n", "from services.tenant_watcher.client.client import TenantsClient\n", "\n", "\n", "@pytest.fixture\n", "def tenant_map(dao_factory):\n", "    tenant = tenant_watcher_pb2.Tenant(\n", "        \u001b[0m\u001b[47m\u001b[30mid=\"augment-1234567890\",\n", "        name=\"Augment\",\n", "        shard_namespace=\"augment\",\n", "        cloud=\"CLOUD_PROD\",\n", "        auth_configuration=tenant_watcher_pb2.AuthConfiguration(\n", "            domain=\"augmentcode.com\",\n", "        ),\u001b[0m\u001b[94m\n", "    )\n", "    tenant_watcher_client = MagicMock(TenantsClient)\n", "    tenant_watcher_client.get_tenants.return_value = [tenant]\n", "    yield TenantMap(\n", "        dao_factory=dao_factory,\n", "        tenant_watcher_client=tenant_watcher_client,\n", "        api_proxy_hostname_domain=\"us-central.api.augmentcode.com\",\n", "    )\n", "\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[18], 10, 10)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/request_insight/bigquery_exporter/bigquery_persistence_test.py\n", "\n", "\u001b[92m        request_insight_pb2.TenantInfo(tenant_name=\"test_tenant\", tenant_id=\"123\")\n", "    )\n", "    event.infer_request.CopyFrom(infer_request)\n", "    completion_request_row = bigquery_persistence._completion_request_row(\n", "        \"test-request\", event\n", "    )\n", "\n", "    expected_json = {\n", "        \"model\": \"test-model\",\n", "        \u001b[0m\u001b[47m\u001b[30m\"top_k\": 1,\n", "        \"top_p\": 0.5,\n", "        \"temperature\": 0.1,\n", "        \"max_tokens\": 10,\n", "        \"session_id\": \"test-session\",\n", "        \"lang\": \"test-lang\",\n", "        \"user_agent\": \"test-user-agent\",\n", "        \"sequence_id\": 100,\u001b[0m\u001b[94m\n", "    }\n", "    assert completion_request_row == {\n", "        \"request_id\": \"test-request\",\n", "        \"tenant\": \"test_tenant\",\n", "        \"tenant_id\": \"123\",\n", "        \"shard_namespace\": \"test_namespace\",\n", "        \"time\": now,\n", "        \"sanitized_json\": expected_json,\n", "        \"session_id\": \"test-session\",\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[19], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pretty(d, indent=0):\n", "    for key, value in d.items():\n", "        print(\"\\t\" * indent + str(key))\n", "        if isinstance(value, dict):\n", "            pretty(value, indent + 1)\n", "        else:\n", "            print(\"\\t\" * (indent + 1) + f\"{str(value).encode()}\")\n", "    print(\"-\" * 100 + \"\\n\")\n", "\n", "\n", "for x in artifacts[:100]:\n", "    pretty(x, 2)    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}