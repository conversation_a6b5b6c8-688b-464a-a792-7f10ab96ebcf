{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Follow research/notebooks/replay_requests.ipynb to setup environment."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import zstandard as zstd\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionDataset,\n", "    HindsightCompletionDatum,\n", ")\n", "\n", "export_path = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-05-01-v1.0/dogfood/data.jsonl.zst\"\n", ")\n", "with zstd.open(export_path, \"r\", encoding=\"utf-8\") as f:\n", "    data = HindsightCompletionDataset.load_data(f)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from base.datasets.tenants import get_tenant\n", "\n", "tenant_name = \"dogfood\"\n", "# service_account_file = pathlib.Path(\n", "#     \"/mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json\"\n", "# )\n", "service_account_file = None\n", "blobs = HindsightCompletionDataset.create_blobs_from_gcs(\n", "    get_tenant(tenant_name),\n", "    service_account_file=service_account_file,\n", ")\n", "dataset = HindsightCompletionDataset(data=data, blobs=blobs)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def convert_hindsight_datum(\n", "    datum: HindsightCompletionDatum,\n", ") -> dict:\n", "    request = datum.completion.request\n", "    blob_names = list(request.blob_names)\n", "\n", "    # base/datasets removes the blob_name of the current file, so add it back.\n", "    if (\n", "        request.position is not None\n", "        and request.position.blob_name\n", "        and request.position.blob_name not in blob_names\n", "    ):\n", "        blob_names.append(request.position.blob_name)\n", "\n", "    recency_info = datum.completion.request.recency_info\n", "    return {\n", "        \"prefix\": request.prefix,\n", "        \"suffix\": request.suffix,\n", "        \"target\": datum.ground_truth,\n", "        \"path\": request.path,\n", "        \"doc_ids\": blob_names,\n", "        \"recency_info\": recency_info,\n", "        \"response\": datum.completion.response,\n", "    }"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-08-25 07:01:38\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 3337 missing entries.\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-08-25 07:02:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 3337 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-08-25 07:02:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 3337 keys (total size 29455808).\u001b[0m\n", "\u001b[2m2024-08-25 07:02:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 29455808 units to insert 3337 entries.\u001b[0m\n", "\u001b[2m2024-08-25 07:02:12\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n"]}], "source": ["datum, blobs = next(iter(dataset))\n", "cur_data = convert_hindsight_datum(datum)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[92m\n", "    line_flags = compute_line_change_flags(system_input.selected_code, generated_text)\n", "\n", "    model_raw_output = system.prompt_formatter.tokenizer.detokenize(system_output.output_tokens)\n", "\n", "    logging.info(f\"Response ({request_id=}):\\n{model\u001b[0m\u001b[47m\u001b[30m_raw_output\u001b[0m\u001b[94m}\")\n", "\n", "    current_app.requests_handled += 1\n", "\n", "    return jsonify(response)\n", "\n", "\u001b[0m"]}], "source": ["from research.core.utils_for_str import show_completion\n", "\n", "show_completion(cur_data[\"prefix\"], cur_data[\"suffix\"], cur_data[\"target\"], max_lines=6)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CompletionResponse(text='_raw_output', model='roguesl-v2-16b-seth616-rec', skipped_suffix='', suffix_replacement_text='', unknown_blob_names=[], retrieved_chunks=[RetrievedChunk(text='        system_input, system_output\\n    ):\\n        logging.info(\"Filtering out background edit.\")\\n        generated_text = system_input.selected_code\\n    else:\\n        generated_text = system_output.replacement\\n\\n    change_prob_text = f\"prob_changed={system_output.prob_changed:.1%}\"\\n    confidence_text = f\"change_confidence={system_output.change_confidence:.1%}\"\\n    logging.info(\\n        f\"changed={system_output.changed}, {change_prob_text}, {confidence_text}\"\\n    )\\n\\n    line_flags = compute_line_change_flags(system_input.selected_code, generated_text)\\n\\n    model_raw_output = system.prompt_formatter.tokenizer.detokenize(system_output.output_tokens)\\n\\n    logging.info(f\"Response ({request_id=}):\\\\n{response}\")\\n\\n    current_app.requests_handled += 1\\n\\n    return jsonify(response)\\n\\n\\ndef _should_filter_background_edit(\\n    sys_input: EditGenSystemInput, sys_output: EditGenSystemOutput\\n):\\n    \"\"\"Whether the model output should be filtered out for a background edit request.\"\"\"\\n    if not sys_output.changed:\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(51880, 52894)), RetrievedChunk(text='                \"system_spec\": current_app.system_config_str,\\n                \"artifacts\": artifacts,\\n            }\\n        )\\n        if request_content.blobs:\\n            blob_set = current_app.blob_list_mgr.return_flat_blob_list(\\n                request_content.blobs\\n            )\\n            json_data[\"blobs\"] = tuple(blob_set) if blob_set is not None else None\\n\\n        # Log the user id as well\\n        # Save all the necessary information into a json file\\n        save_code_edit_data_json(\\n            json_data,\\n            get_request_id(),\\n            \"-Call\",\\n            global_log_dir=GLOBAL_NEXT_EDIT_LOG_DIR,\\n        )\\n\\n    current_app.requests_handled += 1\\n\\n    logging.info(f\"Response ({request_id=}):\\\\n{str(response)}\")\\n\\n    return jsonify(response)\\n\\n\\nlang_guesser = default_language_guesser()\\n\\n\\ndef should_index_path_for_retrieval(\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(59644, 60496)), RetrievedChunk(text='            system_output.generated_text,\\n            system_output.extra_output.additional_info,\\n        )\\n        elapsed = time.time() - start\\n\\n        logging.debug(f\"Response generation took {elapsed:.2f} seconds\")\\n\\n        response = ChatResponse(\\n            text=generated_text,\\n        )\\n\\n        json_data = asdict(request_content)\\n        json_data.update(**misc_dict)\\n        json_data.update(\\n            {\\n                \"response\": generated_text,\\n                \"timestamp\": datetime.now().isoformat(),\\n                \"time_cost_s\": time.time() - start_time,\\n                \"user_id\": get_user_id(),\\n                \"system_spec\": current_app.system_config_str,\\n            }\\n        )\\n        if request_content.blobs:\\n            blob_set = current_app.blob_list_mgr.return_flat_blob_list(\\n                request_content.blobs\\n            )\\n            json_data[\"blobs\"] = tuple(blob_set) if blob_set is not None else None\\n\\n        # Log the user id as well\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(55830, 56812)), RetrievedChunk(text='            missing_files = file_changes.missing_files\\n            missing_diffs = file_changes.missing_diffs\\n            logging.warning(\\n                f\"\\\\n{len(missing_files)} missing file blobs: {missing_files[:5]}\\\\n\"\\n                f\"{len(missing_diffs)} missing diff blobs: {missing_diffs[:5]}\"\\n            )\\n            unknown_blob_names += missing_files\\n            unknown_blob_names += missing_diffs\\n            short_response = CodeEditResponse(\\n                text=request_content.selected_text,\\n                unknown_blob_names=list(set(unknown_blob_names)),\\n                line_change_flags=[],\\n            )\\n            return jsonify(short_response)\\n        file_changes = tuple(file_changes)\\n\\n    system_input = EditGenSystemInput(\\n        path=request_content.path or \"\",\\n        prefix=request_content.prefix,\\n        suffix=request_content.suffix,\\n        selected_code=request_content.selected_text,\\n        instruction=request_content.instruction,\\n        recent_file_changes=file_changes,\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(48845, 49864)), RetrievedChunk(text='            unknown_blob_names=unknown_blob_names,\\n        )\\n\\n        json_data = asdict(request_content)\\n\\n        json_data.update(**misc_dict)\\n        json_data.update(\\n            {\\n                \"response\": generated_text,\\n                \"sender_ip\": request.remote_addr,\\n                \"timestamp\": datetime.now().isoformat(),\\n                \"time_cost_s\": time.time() - start_time,\\n            }\\n        )\\n\\n        json_data[\"blobs\"] = doc_ids\\n\\n        # Log the user id as well\\n        json_data[\"user_id\"] = get_user_id()\\n        json_data[\"system_spec\"] = current_app.system_config_str\\n        # Save all the necessary information into a json file\\n        save_code_edit_data_json(\\n            json_data,\\n            get_request_id(),\\n            \"-Call\",\\n            request_content.completion_url,\\n        )\\n\\n        current_app.requests_handled += 1\\n\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(41984, 42852)), RetrievedChunk(text='            else:\\n                logging.warning(\"blob_set is None.\")\\n        if unknown_blob_names:\\n            logging.warning(f\"Found {len(unknown_blob_names)} missing files.\")\\n            if isinstance(system, NextEditGenSystem):\\n                logging.warning(\\n                    \"NextEditGenSystem currently cannot handle missing files.\"\\n                )\\n                short_response = CodeEditResponse(\\n                    text=request_content.selected_text,\\n                    unknown_blob_names=unknown_blob_names,\\n                )\\n                return jsonify(short_response)\\n\\n        logging.info(\"Generating...\")\\n        start = time.time()\\n\\n        system_output = system.generate(system_input)\\n\\n        logging.info(f\"Generation took {time.time() - start:.2f} seconds\")\\n\\n        generated_text, misc_dict = (\\n            system_output.generated_text,\\n            system_output.extra_output.additional_info,\\n        )\\n\\n        response = CodeEditResponse(\\n            text=generated_text,\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(40972, 41984)), RetrievedChunk(text='    # guard the generation code using the completion lock\\n    with (\\n        current_app.system_lock,\\n        _setup_logging(\\n            \"next_edit_gen\", session_id, request_id, skip_logging=not user_requested\\n        ) as logger,\\n    ):\\n        try:\\n            system_output = system.generate(\\n                system_input,\\n                should_cancel=lambda: user_queue.should_cancel(user_key, request_id),\\n                file_logger=logger,\\n            )\\n        except GenerationCanceledError:\\n            logging.warning(\"Generation canceled for request ID: %s.\", get_request_id())\\n            short_response = CodeEditResponse(\\n                text=\"[[EditGen Request Canceled]]\",\\n                unknown_blob_names=[],\\n                line_change_flags=[],\\n                canceled=True,\\n            )\\n            return jsonify(short_response)\\n        finally:\\n            user_queue.finish_request(user_key, request_id)\\n\\n    if not user_requested and _should_filter_background_edit(\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(50883, 51880)), RetrievedChunk(text='    response = NextEditCombinedResponse(\\n        changed_file_regions=system_output.changed_file_regions,\\n        unknown_blob_names=unknown_blob_names,\\n        canceled=False,\\n    )\\n    logging.info(f\"Response ({request_id=}):\\\\n{str(response)}\")\\n\\n    current_app.requests_handled += 1\\n\\n    return jsonify(response)\\n\\n\\n@api_proxy_bp.route(\"/next_edit_gen\", methods=[\"POST\"])\\n@ensure_authenticated\\ndef handle_next_edit_gen_request():\\n    \"\"\"Handles a next edit generation request.\"\"\"\\n\\n    if current_app.args.disable_completions:\\n        abort(400, \"Server does not support completions.\")\\n\\n    system = current_app.system\\n    if isinstance(system, NextEditCombinedSystem):\\n        system = system.edit_gen_system\\n    if not isinstance(system, NextEditGenSystem):\\n        abort(400, \"Server does not support next edit generation.\")\\n\\n    # Prepare request\\n    request_content: CodeEditRequest = parse_json_request(\\n        request.get_json(),\\n        CodeEditRequest,  # type: ignore\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(46841, 47821)), RetrievedChunk(text='            logger.info(f\"Raw model output:\\\\n{full_response}\")\\n        except BaseException as e:\\n            logger.info(f\"Fail to write cache file ({cache_file_path}) due to {e}\")\\n        # If it is the debug mode, return more information.\\n        if debug:\\n            return jsonify(\\n                {\\n                    \"response\": response,\\n                    \"modified_code\": \"\",\\n                    \"status\": \"success\",\\n                    **debug_json_data,\\n                }\\n            )\\n        else:\\n            return jsonify(\\n                {\"response\": response, \"modified_code\": \"\", \"status\": \"success\"}\\n            )\\n    except BaseException as e:\\n        print(f\"Fail to call model.generate due to {e}\")\\n        return jsonify({\"response\": \"\", \"status\": f\"failed due to {e}\"})\\n\\n\\<EMAIL>(\"/log_status\", methods=[\"POST\"])\\ndef log_status():\\n    \"\"\"Logging the status.\\n\\n    - request_id: str\\n    - instruction: str\\n    \"\"\"\\n    data = request.get_json()\\n', origin='dense_retriever', blob_name='140466c240eb0fdfe2c2660278c46946d20ec7fc25c8c9026079399d59925356', path='experimental/dxy/demo/launch_flask.py', crange=IntRange(22760, 23734)), RetrievedChunk(text='            f\"... {len(response_for_logging[\\'unknown_blob_names\\'])} unknown blob names ...\"\\n        )\\n        logging.info(json.dumps(response_for_logging, indent=2))\\n        logging.debug(f\"Response generation took {elapsed:.2f} seconds\")\\n\\n        current_app.requests_handled += 1\\n        logging.debug(f\"Completion requests handled: {current_app.requests_handled}\")\\n\\n        return jsonify(response)\\n\\n\\ndef save_code_edit_data_json(\\n    json_data: dict[str, Any],\\n    request_id: Optional[str] = None,\\n    suffix: str = \"\",\\n    completion_url: Optional[str] = None,\\n    global_log_dir: Path = GLOBAL_EDIT_LOG_DIR,\\n):\\n    \"\"\"Save the request json data to a file.\\n\\n    We are currently saving code edit requests / feedback into individual json files.\\n    We likely would need to revisit the method when we scale up the number of requests.\\n    \"\"\"\\n    filename = f\"{request_id}{suffix}.json\"\\n    # Save the logs into different sub-folders based on the completion url.\\n    if completion_url is None:\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(36195, 37193)), RetrievedChunk(text='                top_k=8,\\n            )\\n            retrieval_message = (\\n                f\"Here are some reference code snippets in my repository.\"\\n                f\"\\\\n{build_retrieval_message(retrieved_chunks)}\"\\n            )\\n            logger.info(f\"Retrieval message: {retrieval_message}\")\\n            dialog.messages.append(retrieval_message)\\n            dialog.messages.append(\"Sure, I have kept them in my mind.\")\\n            dialog.messages.append(last_message)\\n            response = model.generate(\\n                ModelInput(extra={\"dialog\": dialog}),\\n                options=GenerationOptions(max_generated_tokens=512),\\n            )\\n            response = utils_for_str.trim_string(response, [\"\\\\nUser: \"])\\n        else:\\n            raise TypeError(f\"Unknown version: {self.version}\")\\n        return response\\n\\n\\nGLOBAL_EDIT_MODEL = EditModel()\\nGLOBAL_EXPLAIN_MODEL = ExplainModel()\\nGLOBAL_CHAT_MODEL = ChatModel()\\nGLOBAL_DIALOG = Dialog(messages=[])\\n\\n\\ndef shorten(string: str):\\n    return string.lower().strip()\\n\\n', origin='dense_retriever', blob_name='140466c240eb0fdfe2c2660278c46946d20ec7fc25c8c9026079399d59925356', path='experimental/dxy/demo/launch_flask.py', crange=IntRange(15837, 16861)), RetrievedChunk(text='        debug_json_data = {}\\n        try:\\n            debug_json_data = {\\n                \"request_id\": request_id,\\n                \"selected_code\": selected_code,\\n                \"prefix\": original_prefix,\\n                \"suffix\": original_suffix,\\n                \"instruction\": instruction,\\n                \"response\": response,\\n                \"full_response\": full_response,\\n                \"prompt\": prompt,\\n                \"lines_in_prefix_suffix\": lines_in_prefix_suffix,\\n                \"language\": language,\\n                \"thread_id\": thread_id,\\n                \"sender_ip\": request.remote_addr,\\n                \"time_cost(seconds)\": time_cost,\\n            }\\n            utils_for_file.write_json(\\n                cache_file_path,\\n                debug_json_data,\\n                indent=2,\\n            )\\n            logger.info(f\"Time cost for model inference: {time_cost*1000:.1f} ms\")\\n            logger.info(f\"Cache file path: {cache_file_path}\")\\n', origin='dense_retriever', blob_name='140466c240eb0fdfe2c2660278c46946d20ec7fc25c8c9026079399d59925356', path='experimental/dxy/demo/launch_flask.py', crange=IntRange(21798, 22760)), RetrievedChunk(text='\\n    prompt = whole_file_prompts.format_prompt(code, edit_scope.file_name)\\n\\n    response_text = generate_via_openai(\\n        [prompt],\\n        num_completion=1,\\n        model=\"gpt-4-1106-preview\",\\n        max_tokens=4096,\\n    )\\n\\n    result = {\\n        \"success\": False,\\n        \"new_code\": code,\\n        \"code_edit_data\": dataclasses.asdict(edit_scope),\\n        \"response1\": response_text[0],\\n        \"prompt1\": prompt,\\n        \"old_code\": \"\",\\n    }\\n    response = whole_file_prompts.parse_response(response_text)\\n    result.update(\\n        {\\n            \"response\": response,\\n            \"instruction\": response[\"instruction\"],\\n            \"prompt\": prompt,\\n        }\\n    )\\n    result[\"success\"] = response[\"success\"]\\n    return result\\n\\n\\n', origin='dense_retriever', blob_name='8c8783d00ca41d82595076939d2b1c4190f486bc1c742367a0488b3d335963dc', path='experimental/igor/experiments/2023-12-13_code_edit/synth/stages/code_edit_pipeline.py', crange=IntRange(17663, 18402)), RetrievedChunk(text='    user_queue.add_request(user_key, request_id)\\n\\n    # The inference host server is not thread-safe when we do caching, so we\\n    # guard the generation code using the completion lock\\n    with (\\n        current_app.system_lock,\\n        _setup_logging(\"next_edit_combined\", session_id, request_id) as file_logger,\\n    ):\\n        try:\\n            system_output = system.generate(\\n                system_input,\\n                should_cancel=lambda: user_queue.should_cancel(user_key, request_id),\\n                file_logger=file_logger,\\n            )\\n        except GenerationCanceledError:\\n            logging.warning(\"Generation canceled for request ID: %s.\", get_request_id())\\n            short_response = NextEditCombinedResponse(\\n                changed_file_regions=[],\\n                unknown_blob_names=unknown_blob_names,\\n                canceled=True,\\n            )\\n            return jsonify(short_response)\\n        finally:\\n            user_queue.finish_request(user_key, request_id)\\n\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(45845, 46841)), RetrievedChunk(text='        text = model.generate(model_input, generation_options, extra_outputs)\\n        result = CompletionResult(\\n            generated_text=text,\\n            prompt_tokens=extra_outputs.prompt_tokens or [],\\n            retrieved_chunks=[],\\n            extra_output=extra_outputs,\\n        )\\n        prompt = escape_control_chars(model.tokenizer.detokenize(result.prompt_tokens))\\n        logging.info(\"Full prompt:\")\\n        logging.info(\\n            highlight_special_tokens(prompt, model.tokenizer.all_special_tokens())\\n        )\\n        if self.log_dir:\\n            self.log_dir.mkdir(exist_ok=True, parents=True)\\n            (self.log_dir / \"generation_options.txt\").write_text(\\n                json.dumps(asdict(generation_options), indent=2)\\n            )\\n            (self.log_dir / \"full_prompt.txt\").write_text(prompt)\\n            (self.log_dir / \"output.txt\").write_text(text)\\n        return result\\n\\n    def generate(\\n        self,\\n        model_input: ModelInput,\\n    ) -> CompletionResult:\\n', origin='dense_retriever', blob_name='9576ab1079883c80751e2664aa0162123db74a14003e5d51362b2e10f271f50e', path='research/model_server/model_server_system.py', crange=IntRange(7723, 8723)), RetrievedChunk(text='\\n    version: str = \"v0\"\\n\\n    def __init__(self, version: str = \"v0\") -> None:\\n        self.version = version\\n\\n    def __call__(\\n        self, dialog: Dialog, selected_code: str, logger: logging.Logger\\n    ) -> str:\\n        dialog = copy.deepcopy(dialog)\\n        if self.version == \"v0\" or selected_code == \"\":  # ignore the selected code\\n            model = models[\"llama_instruct\"]\\n            model.prompt_formatter = CodeLlamaChatFormatter()\\n            dialog.system_prompt = r\"\"\"You are an expert, respectful, and professional coding copilot, named as AugmentCode.\\nPlease always answer as helpfully as possible.\\nIf a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.\\nIf you don\\'t know the answer to a question, please don\\'t share false information.\"\"\"\\n            response = model.generate(\\n                ModelInput(extra={\"dialog\": dialog}),\\n                options=GenerationOptions(max_generated_tokens=512),\\n            )\\n', origin='dense_retriever', blob_name='140466c240eb0fdfe2c2660278c46946d20ec7fc25c8c9026079399d59925356', path='experimental/dxy/demo/launch_flask.py', crange=IntRange(13828, 14832)), RetrievedChunk(text='        # empty completion when that happens.\\n        if current_app.last_request_was_replacement:\\n            current_app.last_request_was_replacement = False\\n            logging.info(\"skipping completion request that followed replacement\")\\n            response = CompletionResponse(\\n                text=\"\",\\n                completions=[],\\n                completion_items=[],\\n                unknown_blob_names=[],\\n                suggested_prefix_char_count=max_prefix_len,\\n                suggested_suffix_char_count=max_suffix_len,\\n                completion_timeout_ms=current_app.args.completion_timeout_ms,\\n            )\\n            return jsonify(asdict(response))\\n\\n        logging.info(\"generating...\")\\n        start = time.time()\\n        completion = system.generate(model_input)\\n        elapsed = time.time() - start\\n\\n        # TODO(guy) Needs better typing. We need to detokenize the returned\\n        # prompt, and for that we need access to the tokenizer. So we reach\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(33270, 34253)), RetrievedChunk(text='        full_prompt = self.model.tokenizer.detokenize(extra_outputs.prompt_tokens)\\n\\n        # logger.info(f\"{\\'-\\' * 20} FULL PROMPT {\\'-\\' * 20}\\\\n{full_prompt}{\\'-\\' * 80}\")\\n\\n        misc_output: dict[str, JSONSerializableType] = {\\n            \"full_response\": full_response,\\n            \"prompt\": full_prompt,\\n            \"actual_prefix_in_prompt\": model_input.prefix,\\n            \"actual_suffix_in_prompt\": model_input.suffix,\\n            \"num_input_tokens\": len(extra_outputs.prompt_tokens),\\n            \"num_output_tokens\": extra_outputs.additional_info[\"num_output_tokens\"],\\n        }\\n\\n        extra_output = ExtraGenerationOutputs(additional_info=misc_output)\\n\\n        result = CodeEditResult(\\n            generated_text=updated_code,\\n            prompt_tokens=extra_outputs.prompt_tokens,\\n            retrieved_chunks=model_input.retrieved_chunks,\\n            extra_output=extra_output,\\n        )\\n\\n        return result\\n\\n    def forward_pass(\\n        self, inputs: list[ResearchEditPromptInput]\\n', origin='dense_retriever', blob_name='9e03ccf72bf1ccc98aeda987bbb38e036eeccef37085caa9aee6d10d0522e2cf', path='research/eval/harness/systems/droid_repo_code_edit_system.py', crange=IntRange(3898, 4895)), RetrievedChunk(text='            log_likelihood=forward_metrics.log_likelihood,\\n            token_accuracy=forward_metrics.token_accuracy,\\n        )\\n        return PinocchioOutput(\\n            request_id=datum.request_id,\\n            path=datum.request.path,\\n            prefix=model_input.prefix,\\n            suffix=model_input.suffix,\\n            datum=datum,\\n            prompt=system.get_model().tokenizer.detokenize(result.prompt_tokens),\\n            generation=result.generated_text,\\n            ground_truth=datum.response.text,\\n            metrics=scores,\\n        )\\n\\n    def run(\\n        self,\\n        system: CodeCompleteSystem,\\n        output_path: Union[str, Path],\\n        output_prefix: str = \"\",\\n    ) -> dict:\\n        \"\"\"Run the task with the given system and save the results into output_path/output_prefix_xxx.\"\"\"\\n\\n        output_path = Path(output_path)\\n        output_path.mkdir(exist_ok=True, parents=True)\\n        logger.info(f\"Save results into {output_path}\")\\n\\n        results = list[PinocchioOutput]()\\n', origin='dense_retriever', blob_name='f24c155dec90aaf4516cd2766e2f726418ff9f90d94bbf85fa77cc29170327d0', path='research/eval/harness/tasks/pinocchio.py', crange=IntRange(10270, 11275)), RetrievedChunk(text=\"}\\n\\nexport function parseResponse(rawResponse: string): Partial<{response: string; modifiedCode: string; language: string;}> {\\n    // Here we always only care about the 1st code block.\\n    return {\\n        response: rawResponse,\\n        modifiedCode: rawResponse.split('```')[1] || rawResponse,\\n    };\\n}\\n\\nfunction handleError(e) {\\n    console.error(e);\\n    vscode.window.showErrorMessage(`API error ${e.message}`);\\n    return { rawResponse: `API error ${e.message}`, code: 'API error' };\\n}\\n\", origin='dense_retriever', blob_name='fd442a39e539182f159eb01d1a7031ca20af1d2b8c20700cf4d3b4beedf37763', path='experimental/yangguang/vscode-ui-prototype/src/lib/api/index.ts', crange=IntRange(4770, 5259)), RetrievedChunk(text='            debug_json_data = {\\n                \"request_id\": request_id,\\n                \"selected_code\": selected_code,\\n                \"prefix\": original_prefix,\\n                \"suffix\": original_suffix,\\n                \"instruction\": instruction,\\n                \"response\": response,\\n                \"lines_in_prefix_suffix\": lines_in_prefix_suffix,\\n                \"language\": language,\\n                \"thread_id\": thread_id,\\n                \"sender_ip\": request.remote_addr,\\n                \"time_cost(seconds)\": time_cost,\\n            }\\n            utils_for_file.write_json(\\n                cache_file_path,\\n                debug_json_data,\\n                indent=2,\\n            )\\n            logger.info(f\"Time cost for model inference: {time_cost*1000:.1f} ms\")\\n            logger.info(f\"Cache file path: {cache_file_path}\")\\n            logger.info(f\"Model output:\\\\n{response}\")\\n        except BaseException as e:  # pylint: disable=broad-except\\n', origin='dense_retriever', blob_name='2433ca81204da578ebc2750009d774fcddd612c340ee6615ba73545b8bc0ec50', path='experimental/igor/experiments/2023-12-13_code_edit/tools/serve.py', crange=IntRange(4941, 5900)), RetrievedChunk(text='        min_spaces_for_modified = count_min_indentation(cleaned_generated_text)\\n        extra_identation = max(min_spaces_for_original - min_spaces_for_modified, 0)\\n        if extra_identation > 0:\\n            cleaned_generated_lines = []\\n            for xline in cleaned_generated_text.splitlines(keepends=True):\\n                cleaned_generated_lines.append(\" \" * extra_identation + xline)\\n            cleaned_generated_text = \"\".join(cleaned_generated_lines)\\n\\n        cleaned_generated_text = self._post_processing(cleaned_generated_text)\\n\\n        logger.debug(\"Postprocessed response:\")\\n        logger.debug(escape_control_chars(cleaned_generated_text))\\n\\n        return CompletionResult(\\n            generated_text=cleaned_generated_text,\\n            prompt_tokens=extra_outputs.prompt_tokens or [],\\n            retrieved_chunks=model_input.retrieved_chunks,\\n            extra_output=extra_outputs,\\n        )\\n\\n    def get_model(self) -> GenerativeLanguageModel:\\n        return self.model\\n\\n    @classmethod\\n', origin='dense_retriever', blob_name='7ba3e260903f71e1cf048598470c90d461df34d808374553bec7188f460f5a68', path='research/eval/harness/systems/code_edit_system_with_retrieval.py', crange=IntRange(9885, 10896)), RetrievedChunk(text='        full_prompt = self.model.tokenizer.detokenize(inner_extra_outputs.prompt_tokens)\\n\\n        misc_output = {\\n            \"full_response\": full_response,\\n            \"prompt\": full_prompt,\\n            \"num_input_tokens\": len(inner_extra_outputs.prompt_tokens),\\n            \"num_output_tokens\": inner_extra_outputs.additional_info[\\n                \"num_output_tokens\"\\n            ],\\n        }\\n\\n        logger.info(f\"{\\'-\\' * 20} FULL PROMPT {\\'-\\' * 20}\\\\n{full_prompt}{\\'-\\' * 80}\")\\n\\n        result = CodeEditResult(\\n            generated_text=updated_code,\\n            prompt_tokens=inner_extra_outputs.prompt_tokens,\\n            retrieved_chunks=[],\\n            extra_output=ExtraGenerationOutputs(additional_info=misc_output),\\n        )\\n\\n        return result\\n\\n    def forward_pass(\\n        self, inputs: list[ResearchEditPromptInput]\\n    ) -> list[ModelForwardOutput]:\\n        # Right now, our generative model expects a ModelInput\\n        inputs_legacy = [\\n', origin='dense_retriever', blob_name='f67d68910388fda7fb793a4399468a62bcf5b24a4f4c9201b1bf5de569266e5f', path='research/eval/harness/systems/droid_code_edit_system.py', crange=IntRange(2848, 3807)), RetrievedChunk(text='                \"prefix\": original_prefix,\\n                \"suffix\": original_suffix,\\n                \"instruction\": instruction,\\n                \"response\": response,\\n                \"lines_in_prefix_suffix\": lines_in_prefix_suffix,\\n                \"language\": language,\\n                \"thread_id\": thread_id,\\n                \"sender_ip\": request.remote_addr,\\n                \"time_cost(seconds)\": time_cost,\\n            }\\n            utils_for_file.write_json(\\n                cache_file_path,\\n                debug_json_data,\\n                indent=2,\\n            )\\n            logger.info(f\"Time cost for model inference: {time_cost*1000:.1f} ms\")\\n            logger.info(f\"Cache file path: {cache_file_path}\")\\n            logger.info(f\"Model output:\\\\n{response}\")\\n        except BaseException as e:\\n            logger.info(f\"Fail to write cache file ({cache_file_path}) due to {e}\")\\n        # If it is the debug mode, return more information.\\n        if debug:\\n            return jsonify(\\n                {\\n', origin='dense_retriever', blob_name='088c624c048baa1aa3db0f55df309b00505742122bbf99e969dcdc5714cab887', path='experimental/dxy/edits/notebooks/random/launch_ft.py', crange=IntRange(7789, 8802)), RetrievedChunk(text='            # we prepend the has_change token to output to pretend it was generated\\n            output_tokens = [tkn.has_change_id] + output_tokens\\n        # now parse the output\\n\\n        decoded = self.prompt_formatter.decode_output_tokens(\\n            output_tokens, sys_input.selected_code\\n        )\\n\\n        if file_logger:\\n            file_logger.log(\"prompt_tokens.txt\", tkn.detokenize(original_prompt_tokens))\\n            prob_text = f\"(prob_changed={prob_changed:.2%})\\\\n\"\\n            file_logger.log(\\n                \"output_tokens.txt\", prob_text + tkn.detokenize(output_tokens)\\n            )\\n            file_logger.log(\"replacement.txt\", decoded.replacement)\\n        output = EditGenSystemOutput(\\n            decoded.replacement,\\n            changed=decoded.changed,\\n            prompt_tokens=prompt_tokens,\\n            prob_changed=prob_changed,\\n        )\\n        self._generate_cache[sys_input] = output, file_logger\\n        return output\\n\\n    def retrieve_chunks(\\n        self,\\n        current_code: str,\\n', origin='dense_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(7819, 8838)), RetrievedChunk(text='    prefix_text = shorten_str(datum.request.prefix, prefix_len, omit_mode=\"left\")\\n    # add suffix\\n    suffix_text = shorten_str(datum.request.suffix, suffix_len, omit_mode=\"right\")\\n    output_sections: list[str] = [\\n        f\"Request id: {datum.request_id}\",\\n        f\"File path: {datum.request.path}\",\\n        show_header(\"Prefix\"),\\n        prefix_text,\\n        show_header(\\n            f\"Original prediction ({datum.response.model})\",\\n            width=10,\\n        ),\\n        datum.response.text,\\n        show_header(\"Replayed prediction\", width=10),\\n        result.generated_text,\\n        show_header(\"Suffix\"),\\n        suffix_text,\\n    ]\\n    output_text = \"\\\\n\".join(output_sections)\\n    output_ui = widgets.HTML(code_html(output_text))\\n\\n    titles = [\"Output\"]\\n    children: list[widgets.Widget] = [output_ui]\\n\\n    add_info = result.extra_output.additional_info\\n    add_keys = sorted(add_info.keys())\\n    # make a tab entry for each member\\n    titles.extend(add_keys)\\n', origin='dense_retriever', blob_name='568aa771a8629dfb0a4a1db2688236875ff68c49a3acf8e4cf46f7ea724715f6', path='research/tools/replay_requests.py', crange=IntRange(3897, 4870)), RetrievedChunk(text='    try:\\n        request_content: CodeEditAnnotationRequest = parse_json_request(\\n            request.get_json(),\\n            CodeEditAnnotationRequest,  # type: ignore\\n        )\\n    except TypeError as e:\\n        logging.error(f\"Fail to parse request due to {e}\")\\n        return jsonify({\"status\": \"incorrect inputs\"})\\n\\n    logging.info(\"request to log edit status:\")\\n    _print_request(request_content)\\n\\n    json_data = asdict(request_content)\\n    json_data.update(\\n        {\\n            \"sender_ip\": request.remote_addr,\\n            \"timestamp\": datetime.now().isoformat(),\\n        }\\n    )\\n    save_code_edit_data_json(\\n        json_data,\\n        request_content.request_id,\\n        \"-Feedback\",\\n        request_content.completion_url,\\n    )\\n    return jsonify({\"status\": \"success\"})\\n\\n\\n@api_proxy_bp.route(\"/edit\", methods=[\"POST\"])\\n@ensure_authenticated\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(38214, 39072)), RetrievedChunk(text='            logger.info(f\"Fail to write cache file ({cache_file_path}) due to {e}\")\\n        # If it is the debug mode, return more information.\\n        if debug:\\n            return jsonify(\\n                {\\n                    \"response\": response,\\n                    \"modified_code\": \"\",\\n                    \"status\": \"success\",\\n                    **debug_json_data,\\n                }\\n            )\\n        else:\\n            return jsonify(\\n                {\"response\": response, \"modified_code\": \"\", \"status\": \"success\"}\\n            )\\n    except BaseException as e:  # pylint: disable=broad-except\\n        import traceback\\n\\n        traceback.print_exc()\\n        print(f\"Fail to call model.generate due to {e}\")\\n        return jsonify({\"response\": \"\", \"status\": f\"failed due to {e}\"})\\n\\n\\<EMAIL>(\"/log_status\", methods=[\"POST\"])\\ndef log_status():\\n    \"\"\"Logging the status.\\n\\n    - request_id: str\\n    - instruction: str\\n    \"\"\"\\n', origin='dense_retriever', blob_name='2433ca81204da578ebc2750009d774fcddd612c340ee6615ba73545b8bc0ec50', path='experimental/igor/experiments/2023-12-13_code_edit/tools/serve.py', crange=IntRange(5900, 6834)), RetrievedChunk(text='            generated_text=generation,\\n            prompt_tokens=extra_outputs.prompt_tokens or [],\\n            retrieved_chunks=model_input.retrieved_chunks,\\n            extra_output=extra_outputs,\\n        )\\n\\n        prompt = escape_control_chars(\\n            self.model.tokenizer.detokenize(completion.prompt_tokens)\\n        )\\n        logger.info(\"Full prompt:\")\\n        # TODO(dxy): this func prints empty string, check the reason.\\n        logger.info(\\n            highlight_special_tokens(prompt, self.model.tokenizer.all_special_tokens())\\n        )\\n        logger.info(\"\\\\n\" + (\"-\" * 80))  # pylint: disable=logging-not-lazy\\n        logger.info(\"Original response:\")\\n        logger.info(escape_control_chars(completion.generated_text))\\n        logger.info(\"\\\\n\" + (\"-\" * 80))  # pylint: disable=logging-not-lazy\\n\\n        if is_context_free:\\n            cleaned_generated_text = extract_the_last_markdown_block(\\n                completion.generated_text\\n            )\\n            if cleaned_generated_text is None:\\n', origin='dense_retriever', blob_name='7ba3e260903f71e1cf048598470c90d461df34d808374553bec7188f460f5a68', path='research/eval/harness/systems/code_edit_system_with_retrieval.py', crange=IntRange(7869, 8886)), RetrievedChunk(text='\\ndef process_edit_scope(edit_scope, use_mistral: bool = False, mix_name: str = \"mix1\"):\\n    code = edit_scope.updated_code\\n    mix = mixes[mix_name]\\n\\n    # Request 1: Generate the \"old\" code\\n    prompt1 = format_prompt1(code, mix)\\n\\n    if use_mistral:\\n        response1_text = generate_via_mistral(prompt1, \"mistral-medium\", 4096)\\n    else:\\n        response1_text = generate_via_openai(\\n            [prompt1],\\n            num_completion=1,\\n            model=\"gpt-4-1106-preview\",\\n            max_tokens=4096,\\n        )\\n    response1 = parse_response1(response1_text)\\n\\n    result = {\\n        \"success\": False,\\n        \"new_code\": code,\\n        \"code_edit_data\": dataclasses.asdict(edit_scope),\\n        \"response1\": response1_text[0],\\n        \"prompt1\": prompt1,\\n        \"old_code\": response1[\"code\"] if \"code\" in response1 else None,\\n        \"backward_edit\": (\\n            response1[\"instruction\"] if \"instruction\" in response1 else None\\n        ),\\n    }\\n', origin='dense_retriever', blob_name='8c8783d00ca41d82595076939d2b1c4190f486bc1c742367a0488b3d335963dc', path='experimental/igor/experiments/2023-12-13_code_edit/synth/stages/code_edit_pipeline.py', crange=IntRange(15009, 15963)), RetrievedChunk(text='        # Save all the necessary information into a json file\\n        save_code_edit_data_json(\\n            json_data,\\n            request.headers.get(\"X-Request-Id\"),\\n            \"-Call\",\\n            global_log_dir=GLOBAL_CHAT_LOG_DIR,\\n        )\\n\\n        current_app.requests_handled += 1\\n\\n        return jsonify(response)\\n\\n\\n@api_proxy_bp.route(\"/next_edit_loc\", methods=[\"POST\"])\\n@ensure_authenticated\\ndef handle_next_edit_location_request():\\n    \"\"\"Handle next edit request.\"\"\"\\n\\n    start_time = time.time()\\n    system = current_app.system\\n    if isinstance(system, NextEditCombinedSystem):\\n        system = system.edit_location_system\\n    assert isinstance(system, BasicNextEditLocationSystem)\\n\\n    request_content: NextEditRequest = parse_json_request(\\n        request.get_json(),\\n        NextEditRequest,  # type: ignore\\n    )\\n\\n    logging.info(\"Next edit Request:\")\\n', origin='dense_retriever', blob_name='8385f5bf855428422636bbe4d058f5baaad1aab8f3812992ee05648ee1da41f4', path='research/model_server/launch_model_server.py', crange=IntRange(56812, 57685)), RetrievedChunk(text='                original_prefix_length=len(proto.request.prefix),\\n                original_suffix_length=len(proto.request.suffix),\\n            )\\n        else:\\n            position = None\\n\\n        return EditRequest(\\n            prefix=prefix,\\n            selected_text=proto.request.selected_text,\\n            instruction=proto.request.instruction,\\n            suffix=suffix,\\n            path=proto.request.path,\\n            lang=proto.request.lang,\\n            blob_names=blob_names,\\n            timestamp=timestamp,\\n            position=position,\\n        )\\n\\n    def _build_response(\\n        self,\\n        response_proto: request_insight_pb2.RIEditResponse,\\n        request_proto: request_insight_pb2.RIEditRequest,\\n        timestamp: datetime,\\n    ) -> EditResponse:\\n        return EditResponse(\\n            timestamp=timestamp,\\n            text=response_proto.response.text,\\n            model_name=request_proto.request.model_name,\\n            unknown_blob_names=list(response_proto.response.unknown_blob_names),\\n', origin='dense_retriever', blob_name='044367b47878093b667f6d4fbbbd0f427154e0c9c038c91c1e694a310a2163b4', path='base/datasets/edit_dataset.py', crange=IntRange(11647, 12664)), RetrievedChunk(text='le_changes={file_change_str}\",\\n        ]\\n        return \"\\\\n\".join(lines)\\n\\n\\n@dataclass\\nclass EditGenSystemOutput(EditGenOutput):\\n    \"\"\"The output of the next edit generation system.\"\"\"\\n\\n    replacement: str\\n    \"\"\"The suggested replacement text.\"\"\"\\n\\n    changed: bool\\n    \"\"\"True if the model suggested a change, False otherwise.\"\"\"\\n\\n    prompt_tokens: TokenSeq\\n    \"\"\"The full prompt tokens.\"\"\"\\n\\n    output_tokens: TokenSeq\\n    \"\"\"The raw output tokens.\"\"\"\\n\\n    prob_changed: float | None\\n    \"\"\"The probability of the model suggesting a change.\\n\\n    This is None if the input has specified `must_change=True`.\\n    \"\"\"\\n\\n    change_confidence: float\\n    \"\"\"The probability of the predicted diff conditioned on having a change.\\n\\n    Multiplying this with `prob_changed` gives the joint probability of the full\\n    model output.\\n    \"\"\"\\n\\n\\n@register_system(\"next_edit_gen\")\\n@dataclass\\nclass NextEditGenSystem(AbstractSystem[EditGenSystemInput, EditGenSystemOutput]):\\n    \"\"\"A suggested edit generation system that can suggest n', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(3297, 11547)), RetrievedChunk(text='ext edits.\"\"\"\\n\\n    model: FastForwardModel\\n    generation_options: GenerationOptions\\n    retriever: DocumentIndex | None\\n    prompt_formatter: EditGenPromptFormatter\\n    max_retrieved_chunks: int = 30\\n\\n    def __post_init__(self):\\n        self._loaded = False\\n        self._generate_cache: LRUCache[\\n            EditGenSystemInput, tuple[EditGenSystemOutput, FileLogger | None]\\n        ] = LRUCache(maxsize=500)\\n        \"\"\"Cache system output by input.\"\"\"\\n        self._model_cache: LRUCache[FrozenRawModelInput, _CachedModelOutput] = LRUCache(\\n            maxsize=500\\n        )\\n        \"\"\"Cache raw model output by raw input.\"\"\"\\n\\n    def generate(\\n        self,\\n        sys_input: EditGenSystemInput,\\n        should_cancel: Callable[[], bool] = lambda: False,\\n        file_logger: FileLogger | None = None,\\n    ) -> EditGenSystemOutput:\\n        if sys_input in self._generate_cache:\\n            cached_output, old_logger = self._generate_cache[sys_input]\\n            if file_logger and old_logger:\\n                file_logg', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='er.log(\"original_logs.txt\", str(old_logger.log_dir))\\n        if should_cancel():\\n            raise GenerationCanceledError()\\n\\n        current_code = sys_input.prefix + sys_input.selected_code + sys_input.suffix\\n        selected_range = CharRange(\\n            len(sys_input.prefix),\\n            len(sys_input.prefix) + len(sys_input.selected_code),\\n        )\\n        doc_ids = sys_input.doc_ids\\n        assert doc_ids is not None\\n\\n        ret_chunks = self.retrieve_chunks(\\n            current_code, selected_range, sys_input.path, doc_ids=doc_ids\\n        )\\n\\n        if should_cancel():\\n            raise GenerationCanceledError()\\n\\n        prompt_input = EditGenPromptInput(\\n            current_path=sys_input.path,\\n            current_code=current_code,\\n            edit_region=selected_range,\\n            instruction=sys_input.instruction,\\n            recent_changes=sys_input.recent_file_changes,\\n            retrieval_chunks=ret_chunks,\\n        )\\n        prompt_tokens = self.prompt_formatter.format_input_prompt(prompt_i', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='nput)\\n        original_prompt_tokens = prompt_tokens\\n        tkn = self.prompt_formatter.tokenizer\\n\\n        if sys_input.must_change:\\n            # we add the has_change token to the prompt to force a change\\n            prompt_tokens.append(tkn.has_change_id)\\n\\n        raw_input = FrozenRawModelInput(\\n            tuple(prompt_tokens),\\n            max_generated_tokens=self.generation_options.max_generated_tokens,\\n        )\\n        cached_output = self._model_cache.get(raw_input)\\n        if cached_output is None:\\n            raw_output = self.model.raw_generate_tokens(\\n                prompt_tokens, self.generation_options, should_cancel=should_cancel\\n            )\\n            output_tokens = raw_output.tokens\\n            token_probs = raw_output.token_probs()\\n            if sys_input.must_change:\\n                prob_changed = None\\n                change_confidence = math.prod(token_probs)\\n            else:\\n                first_tk_probs = torch.softmax(raw_output.logits[0], dim=-1)\\n                prob_changed', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text=' = float(first_tk_probs[tkn.has_change_id].item())\\n                change_confidence = math.prod(token_probs[1:])\\n            cached_output = _CachedModelOutput(\\n                output_tokens=tuple(output_tokens),\\n                prob_changed=prob_changed,\\n                change_confidence=change_confidence,\\n            )\\n            self._model_cache[raw_input] = cached_output\\n        else:\\n            output_tokens = list(cached_output.output_tokens)\\n            prob_changed = cached_output.prob_changed\\n            change_confidence = cached_output.change_confidence\\n\\n        if sys_input.must_change:\\n            # we prepend the has_change token to output to pretend it was generated\\n            output_tokens = [tkn.has_change_id] + output_tokens\\n        # now parse the output\\n\\n        decoded = self.prompt_formatter.decode_output_tokens(\\n            output_tokens, sys_input.selected_code\\n        )\\n\\n        if file_logger:\\n            file_logger.log(\"prompt_tokens.txt\", tkn.detokenize(original_prompt_tokens', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='))\\n            prob_text = f\"({prob_changed=:.1%}, {change_confidence=:.1%})\\\\n\"\\n            file_logger.log(\\n                \"output_tokens.txt\", prob_text + tkn.detokenize(output_tokens)\\n            )\\n            file_logger.log(\"replacement.txt\", decoded.replacement)\\n        output = EditGenSystemOutput(\\n            decoded.replacement,\\n            changed=decoded.changed,\\n            prompt_tokens=prompt_tokens,\\n            output_tokens=output_tokens,\\n            prob_changed=prob_changed,\\n            change_confidence=cached_output.change_confidence,\\n        )\\n        self._generate_cache[sys_input] = output, file_logger\\n        return output\\n\\n    def retrieve_chunks(\\n        self,\\n        current_code: str,\\n        selected_range: CharRange,\\n        current_path: str,\\n        doc_ids: Iterable[DocumentId],\\n    ) -> list[Chunk]:\\n        if self.retriever is None:\\n            return []\\n        prefix, suffix = EditGenPromptFormatter.format_selected_code(\\n            current_code, selected_range, use_diff_', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='based_output=False\\n        )\\n        model_input = ModelInput(\\n            prefix=prefix,\\n            suffix=suffix,\\n            path=current_path,\\n        )\\n        chunks, _ = self.retriever.query(\\n            model_input, doc_ids=doc_ids, top_k=self.max_retrieved_chunks\\n        )\\n        return chunks\\n\\n    def add_docs(self, src_files: Iterable[Document]):\\n        if self.retriever is None:\\n            return\\n        indexed_ids = self.retriever.get_doc_ids()\\n        files = [f for f in src_files if f.id not in indexed_ids]\\n        self.retriever.add_docs(files)\\n\\n    def load(self):\\n        if self._loaded:\\n            return\\n        self.model.load()\\n        if self.retriever:\\n            self.retriever.load()\\n        self._loaded = True\\n\\n    def unload(self):\\n        if not self._loaded:\\n            return\\n        self.model.unload()\\n        if self.retriever:\\n            self.retriever.unload()\\n        self._loaded = False\\n\\n    def clear_retriever(self):\\n        if self.retriever:\\n            self.retri', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='ever.remove_all_docs()\\n\\n    def get_doc_ids(self) -> AbstractSet[DocumentId]:\\n        if self.retriever is None:\\n            return set()\\n        return self.retriever.get_doc_ids()\\n\\n    def get_model(self) -> FastForwardModel:\\n        return self.model\\n\\n    @classmethod\\n    def from_yaml_config(cls, config: dict) -> NextEditGenSystem:\\n        \"\"\"Returns a System object constructed using a config dictionary.\"\"\"\\n        from research.eval.harness import factories\\n\\n        model = factories.create_model(config[\"model\"])\\n        assert isinstance(model, FastForwardModel)\\n\\n        generation_options = GenerationOptions(**config[\"generation_options\"])\\n        retriever = factories.create_retriever(config[\"retriever\"])\\n        prompt_formatter = EditGenPromptFormatter(**config[\"prompt_formatter\"])\\n\\n        return NextEditGenSystem(\\n            model=model,\\n            generation_options=generation_options,\\n            retriever=retriever,\\n            prompt_formatter=prompt_formatter,\\n        )\\n\\n\\n@dataclass(frozen=', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='True)\\nclass FrozenRawModelInput:\\n    \"\"\"A fully immutable raw model input suitable for caching.\"\"\"\\n\\n    full_prompt: NTuple[int]\\n    \"\"\"The full prompt tokens.\"\"\"\\n\\n    max_generated_tokens: int | None\\n    \"\"\"The maximum number of tokens to generate.\"\"\"\\n\\n\\n@dataclass(frozen=True)\\nclass _CachedModelOutput:\\n    output_tokens: NTuple[int]\\n    prob_changed: float | None\\n    change_confidence: float\\n', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547)), RetrievedChunk(text='\"\"\"A system for generating next edits.\"\"\"\\n\\nfrom __future__ import annotations\\n\\nimport math\\nfrom dataclasses import dataclass\\nfrom typing import AbstractSet, Callable, FrozenSet, Iterable, Literal\\n\\nimport torch\\nfrom cachetools import LRUCache\\n\\nfrom base.ranges.range_types import CharRange\\nfrom base.static_analysis.common import shorten_str\\nfrom research.core.changes import Changed\\nfrom research.core.diff_utils import File\\nfrom research.core.model_input import ModelInput\\nfrom research.core.types import Chunk, Document, DocumentId, NTuple\\nfrom research.core.utils import FileLogger\\nfrom research.eval.harness.systems.abs_system import AbstractSystem, register_system\\nfrom research.fim.fim_prompt import TokenSeq\\nfrom research.models.fastforward_models import FastForwardModel, GenerationCanceledError\\nfrom research.models.meta_model import GenerationOptions\\nfrom research.next_edits.edit_gen_formatters import (\\n    DiffStr,\\n    EditGenPromptFormatter,\\n    EditGenPromptInput,\\n)\\nfrom research.next_edits.edit_gen_sampler ', origin='recency_retriever', blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(0, 1012))], timestamp=datetime.datetime(2024, 5, 10, 21, 26, 16, 251287, tzinfo=datetime.timezone(datetime.timedelta(0), 'UTC')), tokens=['_', 'raw', '_', 'output'], token_log_probs=[-0.0004992485046386719, -0.002079010009765625, -2.1576881408691406e-05, -0.0002491474151611328], prompt_tokens=['<filename>', 'research', '/', 'model', '_', 'server', '/', 'launch', '_', 'model', '_', 'server', '.', 'py', '\\n', '<|far_prefix|>', ':', '\\n       ', ' abort', '(', '4', '0', '0', ',', ' \"', 'Server', ' does', ' not', ' support', ' next', ' edit', ' generation', '.\")', '\\n\\n   ', ' #', ' Prepare', ' request', '\\n   ', ' request', '_', 'content', ':', ' Code', 'Edit', 'Request', ' =', ' parse', '_', 'json', '_', 'request', '(', '\\n       ', ' request', '.', 'get', '_', 'json', '(),', '\\n       ', ' Code', 'Edit', 'Request', ',', ' ', ' #', ' type', ':', ' ignore', '\\n   ', ' )', '\\n\\n   ', ' #', ' whether', ' the', ' user', ' has', ' manually', ' requested', ' the', ' change', '\\n   ', ' #', ' TODO', '(', 'ji', 'ay', 'i', '):', ' currently', ' we', ' infer', ' it', ' from', ' the', ' expression', ' below', ',', ' but', ' we', ' may', ' want', '\\n   ', ' #', ' to', ' send', ' this', ' flag', ' from', ' the', ' frontend', ' in', ' the', ' future', '.', '\\n   ', ' user', '_', 'requested', ' =', ' request', '_', 'content', '.', 'must', '_', 'change', ' or', ' bool', '(', '\\n       ', ' request', '_', 'content', '.', 'instruction', '.', 'strip', '()', '\\n   ', ' )', '\\n\\n   ', ' if', ' request', '_', 'content', '.', 'blobs', ':', '\\n       ', ' blob', '_', 'set', ' =', ' current', '_', 'app', '.', 'blob', '_', 'list', '_', 'mgr', '.', 'return', '_', 'flat', '_', 'blob', '_', 'list', '(', '\\n           ', ' request', '_', 'content', '.', 'blobs', '\\n       ', ' )', '\\n       ', ' doc', '_', 'ids', ' =', ' tuple', '(', 'blob', '_', 'set', ')', ' if', ' blob', '_', 'set', ' is', ' not', ' None', ' else', ' []', '\\n   ', ' else', ':', '\\n       ', ' doc', '_', 'ids', ' =', ' []', '\\n   ', ' if', ' not', ' doc', '_', 'ids', ':', '\\n       ', ' logging', '.', 'warning', '(\"', 'No', ' blob', ' list', ' found', ' in', ' the', ' request', '.\")', '\\n\\n   ', ' file', '_', 'store', ' =', ' get', '_', 'file', '_', 'store', '()', '\\n   ', ' unknown', '_', 'blob', '_', 'names', ' =', ' file', '_', 'store', '.', 'find', '_', 'missing', '(', 'doc', '_', 'ids', ')', '\\n\\n   ', ' if', ' isinstance', '(', 'request', '_', 'content', '.', 'git', '_', 'file', '_', 'dif', 'fs', ',', ' str', '):', '\\n       ', ' file', '_', 'changes', ' =', ' Diff', 'Str', '(', 'request', '_', 'content', '.', 'git', '_', 'file', '_', 'dif', 'fs', ')', '\\n   ', ' else', ':', '\\n       ', ' file', '_', 'changes', ' =', ' _', 're', 'construct', '_', 'file', '_', 'changes', '(', '\\n           ', ' request', '_', 'content', '.', 'git', '_', 'file', '_', 'dif', 'fs', ',', ' file', '_', 'store', '\\n       ', ' )', '\\n       ', ' if', ' isinstance', '(', 'file', '_', 'changes', ',', ' Missing', 'Blob', 's', '):', '\\n           ', ' missing', '_', 'files', ' =', ' file', '_', 'changes', '.', 'missing', '_', 'files', '\\n           ', ' missing', '_', 'dif', 'fs', ' =', ' file', '_', 'changes', '.', 'missing', '_', 'dif', 'fs', '\\n           ', ' logging', '.', 'warning', '(', '\\n               ', ' f', '\"\\\\', 'n', '{', 'len', '(', 'missing', '_', 'files', ')}', ' missing', ' file', ' blobs', ':', ' {', 'missing', '_', 'files', '[:', '5', ']', '}\\\\', 'n', '\"', '\\n               ', ' f', '\"{', 'len', '(', 'missing', '_', 'dif', 'fs', ')}', ' missing', ' diff', ' blobs', ':', ' {', 'missing', '_', 'dif', 'fs', '[:', '5', ']}\"', '\\n           ', ' )', '\\n           ', ' unknown', '_', 'blob', '_', 'names', ' +=', ' missing', '_', 'files', '\\n           ', ' unknown', '_', 'blob', '_', 'names', ' +=', ' missing', '_', 'dif', 'fs', '\\n           ', ' short', '_', 'response', ' =', ' Code', 'Edit', 'Response', '(', '\\n               ', ' text', '=', 'request', '_', 'content', '.', 'selected', '_', 'text', ',', '\\n               ', ' unknown', '_', 'blob', '_', 'names', '=', 'list', '(', 'set', '(', 'unknown', '_', 'blob', '_', 'names', ')),', '\\n               ', ' line', '_', 'change', '_', 'flags', '=[],', '\\n           ', ' )', '\\n           ', ' return', ' jsonify', '(', 'short', '_', 'response', ')', '\\n       ', ' file', '_', 'changes', ' =', ' tuple', '(', 'file', '_', 'changes', ')', '\\n\\n   ', ' system', '_', 'input', ' =', ' Edit', 'Gen', 'System', 'Input', '(', '\\n       ', ' path', '=', 'request', '_', 'content', '.', 'path', ' or', ' \"\",', '\\n       ', ' prefix', '=', 'request', '_', 'content', '.', 'prefix', ',', '\\n       ', ' suffix', '=', 'request', '_', 'content', '.', 'suffix', ',', '\\n       ', ' selected', '_', 'code', '=', 'request', '_', 'content', '.', 'selected', '_', 'text', ',', '\\n       ', ' instruction', '=', 'request', '_', 'content', '.', 'instruction', ',', '\\n       ', ' recent', '_', 'file', '_', 'changes', '=', 'file', '_', 'changes', ',', '\\n       ', ' doc', '_', 'ids', '=', 'frozen', 'set', '(', 'doc', '_', 'ids', '),', '\\n       ', ' must', '_', 'change', '=', 'request', '_', 'content', '.', 'must', '_', 'change', ',', '\\n   ', ' )', '\\n   ', ' session', '_', 'id', ' =', ' get', '_', 'session', '_', 'id', '()', '\\n   ', ' request', '_', 'id', ' =', ' get', '_', 'request', '_', 'id', '()', '\\n\\n   ', ' #', ' user', ' requests', ' and', ' background', ' requests', ' shouldn', \"'t\", ' cancel', ' each', ' other', '\\n   ', ' if', ' user', '_', 'requested', ':', '\\n       ', ' user', '_', 'key', ' =', ' (\"', 'user', '_', 'edit', '_', 'gen', '\",', ' session', '_', 'id', ')', '\\n   ', ' else', ':', '\\n       ', ' user', '_', 'key', ' =', ' (\"', 'background', '_', 'edit', '_', 'gen', '\",', ' session', '_', 'id', ')', '\\n\\n   ', ' if', ' user', '_', 'requested', ':', '\\n       ', ' logging', '.', 'info', '(\"', '~', '=', ' \"', ' *', ' ', '2', '0', ' +', ' \"', 'Next', ' Edit', ' Gen', ' Request', '\"', ' +', ' \"', ' ~', '=\"', ' *', ' ', '2', '0', ')', '\\n       ', ' _', 'print', '_', 'request', '(', 'request', '_', 'content', ')', '\\n       ', ' logging', '.', 'debug', '(', 'f', '\"', 'System', ' input', ':', ' {', 'system', '_', 'input', '.', 'summary', '()', '}\")', '\\n   ', ' else', ':', '\\n       ', ' logging', '.', 'info', '(\"', 'Background', ' Edit', ' Gen', ' Request', ' received', '.\\\\', 'n', '(', 'Request', ' details', ' omitted', ')\")', '\\n\\n   ', ' #', ' put', ' a', ' new', ' request', ' into', ' the', ' queue', ',', ' potentially', ' ev', 'ict', ' old', ' requests', '\\n   ', ' user', '_', 'queue', ' =', ' current', '_', 'app', '.', 'user', '_', 'request', '_', 'queue', '\\n   ', ' user', '_', 'queue', '.', 'add', '_', 'request', '(', 'user', '_', 'key', ',', ' request', '_', 'id', ')', '\\n\\n   ', ' if', ' unknown', '_', 'blob', '_', 'names', ':', '\\n       ', ' logging', '.', 'warning', '(', 'f', '\"', 'Found', ' {', 'len', '(', 'unknown', '_', 'blob', '_', 'names', ')}', ' missing', ' files', '.\")', '\\n\\n   ', ' #', ' The', ' inference', ' host', ' server', ' is', ' not', ' thread', '-', 'safe', ' when', ' we', ' do', ' caching', ',', ' so', ' we', '\\n   ', ' #', ' guard', ' the', ' generation', ' code', ' using', ' the', ' completion', ' lock', '\\n   ', ' with', ' (', '\\n       ', ' current', '_', 'app', '.', 'system', '_', 'lock', ',', '\\n       ', ' _', 'setup', '_', 'logging', '(', '\\n           ', ' \"', 'next', '_', 'edit', '_', 'gen', '\",', ' session', '_', 'id', ',', ' request', '_', 'id', ',', ' skip', '_', 'logging', '=', 'not', ' user', '_', 'requested', '\\n       ', ' )', ' as', ' logger', ',', '\\n   ', ' ):', '\\n       ', ' try', ':', '\\n           ', ' system', '_', 'output', ' =', ' system', '.', 'generate', '(', '\\n               ', ' system', '_', 'input', ',', '\\n               ', ' should', '_', 'cancel', '=', 'lambda', ':', ' user', '_', 'queue', '.', 'should', '_', 'cancel', '(', 'user', '_', 'key', ',', ' request', '_', 'id', '),', '\\n               ', ' file', '_', 'logger', '=', 'logger', ',', '\\n           ', ' )', '\\n       ', ' except', ' Generation', 'Canceled', 'Error', ':', '\\n           ', ' logging', '.', 'warning', '(\"', 'Generation', ' canceled', ' for', ' request', ' ID', ':', ' %', 's', '.\",', ' get', '_', 'request', '_', 'id', '())', '\\n           ', ' short', '_', 'response', ' =', ' Code', 'Edit', 'Response', '(', '\\n               ', ' text', '=\"', '[[', 'Edit', 'Gen', ' Request', ' C', 'anceled', ']]', '\",', '\\n               ', ' unknown', '_', 'blob', '_', 'names', '=[],', '\\n               ', ' line', '<fim_suffix>', '}\")', '\\n\\n   ', ' current', '_', 'app', '.', 'requests', '_', 'handled', ' +=', ' ', '1', '\\n\\n   ', ' return', ' jsonify', '(', 'response', ')', '\\n\\n', '\\n', 'def', ' _', 'should', '_', 'filter', '_', 'background', '_', 'edit', '(', '\\n   ', ' sys', '_', 'input', ':', ' Edit', 'Gen', 'System', 'Input', ',', ' sys', '_', 'output', ':', ' Edit', 'Gen', 'System', 'Output', '\\n', '):', '\\n   ', ' \"\"\"', 'Whether', ' the', ' model', ' output', ' should', ' be', ' filtered', ' out', ' for', ' a', ' background', ' edit', ' request', '.\"\"\"', '\\n   ', ' if', ' not', ' sys', '_', 'output', '.', 'changed', ':', '\\n       ', ' #', ' no', ' need', ' to', ' filter', ' if', ' there', \"'s\", ' no', ' change', '\\n       ', ' return', ' False', '\\n   ', ' if', ' equal', '_', 'mod', 'ular', '_', 'spaces', '(', 'sys', '_', 'input', '.', 'selected', '_', 'code', ',', ' sys', '_', 'output', '.', 'replacement', '):', '\\n       ', ' #', ' filter', ' out', ' pure', ' whitespace', ' changes', '\\n       ', ' return', ' True', '\\n   ', ' prob', '_', 'changed', ' =', ' sys', '_', 'output', '.', 'prob', '_', 'changed', '\\n   ', ' assert', ' prob', '_', 'changed', ' is', ' not', ' None', '\\n   ', ' if', ' prob', '_', 'changed', ' *', ' sys', '_', 'output', '.', 'change', '_', 'confidence', ' <', ' ', '0', '.', '5', ':', '\\n       ', ' #', ' filter', ' out', ' low', '-', 'probability', ' predictions', '\\n       ', ' return', ' True', '\\n   ', ' return', ' False', '\\n\\n', '\\n', '@', 'dataclass', '\\n', 'class', ' Missing', 'Blob', 's', ':', '\\n   ', ' missing', '_', 'files', ':', ' list', '[', 'Document', 'Id', ']', '\\n   ', ' missing', '_', 'dif', 'fs', ':', ' list', '[', 'Document', 'Id', ']', '\\n\\n', '\\n', 'def', ' _', 're', 'construct', '_', 'file', '_', 'changes', '(', '\\n   ', ' git', '_', 'file', '_', 'dif', 'fs', ':', ' list', '[', 'Git', 'Diff', 'FileInfo', '],', ' file', '_', 'store', ':', ' File', 'Store', '\\n', ')', ' ->', ' list', '[', 'Changed', '[', 'File', ']]', ' |', ' Missing', 'Blob', 's', ':', '\\n   ', ' \"\"\"', 'Re', 'construct', ' the', ' diff', ' str', ' from', ' a', ' list', ' of', ' Git', 'Diff', 'FileInfo', '.\"\"\"', '\\n   ', ' missing', '_', 'diff', '_', 'blobs', ' =', ' list', '[', 'Document', 'Id', ']()', '\\n   ', ' missing', '_', 'file', '_', 'blobs', ' =', ' list', '[', 'Document', 'Id', ']()', '\\n   ', ' current', '_', 'files', ' =', ' list', '[', 'File', ']()', '\\n   ', ' file', '_', 'dif', 'fs', ' =', ' list', '[', 'str', ']()', '\\n   ', ' for', ' diff', '_', 'info', ' in', ' git', '_', 'file', '_', 'dif', 'fs', ':', '\\n       ', ' document', ' =', ' file', '_', 'store', '.', 'get', '_', 'document', '(', 'diff', '_', 'info', '.', 'file', '_', 'blob', '_', 'name', ')', '\\n       ', ' diff', '_', 'contents', ' =', ' file', '_', 'store', '.', 'get', '_', 'file', '_', 'contents', '(', 'diff', '_', 'info', '.', 'content', '_', 'blob', '_', 'name', ')', '\\n       ', ' if', ' document', ' is', ' None', ':', '\\n           ', ' missing', '_', 'file', '_', 'blobs', '.', 'append', '(', 'diff', '_', 'info', '.', 'file', '_', 'blob', '_', 'name', ')', '\\n       ', ' else', ':', '\\n           ', ' assert', ' document', '.', 'path', ' is', ' not', ' None', '\\n           ', ' current', '_', 'files', '.', 'append', '(', 'File', '(', 'document', '.', 'path', ',', ' document', '.', 'text', '))', '\\n       ', ' if', ' diff', '_', 'contents', ' is', ' None', ':', '\\n           ', ' missing', '_', 'diff', '_', 'blobs', '.', 'append', '(', 'diff', '_', 'info', '.', 'content', '_', 'blob', '_', 'name', ')', '\\n       ', ' else', ':', '\\n           ', ' file', '_', 'dif', 'fs', '.', 'append', '(', 'diff', '_', 'contents', ')', '\\n   ', ' if', ' missing', '_', 'file', '_', 'blobs', ' or', ' missing', '_', 'diff', '_', 'blobs', ':', '\\n       ', ' return', ' Missing', 'Blob', 's', '(', 'missing', '_', 'file', '_', 'blobs', ',', ' missing', '_', 'diff', '_', 'blobs', ')', '\\n\\n   ', ' patch', 'set', ' =', ' parse', '_', 'git', '_', 'diff', '_', 'output', '(\"\\\\', 'n', '\".', 'join', '(', 'file', '_', 'dif', 'fs', '),', ' remove', '_', 'timestamps', '=', 'True', ')', '\\n   ', ' repo', ' =', ' Repository', '(', 'current', '_', 'files', ')', '\\n   ', ' prev', '_', 'repo', ' =', ' apply', '_', 'diff', '(', 'repo', ',', ' patch', 'set', ',', ' reverse', '=', 'True', ')', '\\n   ', ' repo', '_', 'change', ' =', ' repo', '_', 'change', '_', 'from', '_', 'patch', 'set', '(', 'prev', '_', 'repo', ',', ' patch', 'set', ')', '\\n   ', ' file', '_', 'changes', ' =', ' [', '\\n       ', ' change', '.', 'map', '(', 'lambda', ' x', ':', ' File', '(', 'str', '(', 'x', '.', 'path', '),', ' x', '.', 'code', '))', '\\n       ', ' for', ' change', ' in', ' repo', '_', 'change', '.', 'changed', '_', 'files', '\\n   ', ' ]', '\\n   ', ' return', ' file', '_', 'changes', '\\n\\n', '\\n', '@', 'api', '_', 'proxy', '_', 'bp', '.', 'route', '(\"/', 'chat', '\",', ' methods', '=[\"', 'POST', '\"])', '\\n', '@', 'ensure', '_', 'authenticated', '\\n', 'def', ' chat', '():', '\\n   ', ' \"\"\"', 'Handle', ' chat', ' request', '.\"\"\"', '\\n\\n   ', ' start', '_', 'time', ' =', ' time', '.', 'time', '()', '\\n   ', ' system', ' =', ' current', '_', 'app', '.', 'system', '\\n\\n   ', ' #', ' Prepare', ' request', '\\n   ', ' request', '_', 'content', ':', ' Chat', 'Request', ' =', ' parse', '_', 'json', '_', 'request', '(', '\\n       ', ' request', '.', 'get', '_', 'json', '(),', '\\n       ', ' Chat', 'Request', ',', ' ', ' #', ' type', ':', ' ignore', '\\n   ', ' )', '\\n\\n   ', ' logging', '.', 'info', '(\"', 'Chat', ' Request', ':\")', '\\n   ', ' _', 'print', '_', 'request', '(', 'request', '_', 'content', ',', ' skip', '_', 'keys', '=', '(\"', 'blobs', '\",', '))', '\\n   ', ' system', '_', 'input', ' =', ' Model', 'Input', '(', '\\n       ', ' prefix', '=', 'request', '_', 'content', '.', 'prefix', ' or', ' \"\",', '\\n       ', ' suffix', '=', 'request', '_', 'content', '.', 'suffix', ' or', ' \"\",', '\\n       ', ' path', '=', 'request', '_', '<|retrieval_section|>', '<|ret-start|>', '<filename>', 'research', '/', 'eval', '/', 'h', 'arness', '/', 'systems', '/', 'next', '_', 'edit', '_', 'gen', '_', 'system', '.', 'py', '<|ret-body|>', 'er', '.', 'log', '(\"', 'original', '_', 'logs', '.', 'txt', '\",', ' str', '(', 'old', '_', 'logger', '.', 'log', '_', 'dir', '))', '\\n       ', ' if', ' should', '_', 'cancel', '():', '\\n           ', ' raise', ' Generation', 'Canceled', 'Error', '()', '\\n\\n       ', ' current', '_', 'code', ' =', ' sys', '_', 'input', '.', 'prefix', ' +', ' sys', '_', 'input', '.', 'selected', '_', 'code', ' +', ' sys', '_', 'input', '.', 'suffix', '\\n       ', ' selected', '_', 'range', ' =', ' Char', 'Range', '(', '\\n           ', ' len', '(', 'sys', '_', 'input', '.', 'prefix', '),', '\\n           ', ' len', '(', 'sys', '_', 'input', '.', 'prefix', ')', ' +', ' len', '(', 'sys', '_', 'input', '.', 'selected', '_', 'code', '),', '\\n       ', ' )', '\\n       ', ' doc', '_', 'ids', ' =', ' sys', '_', 'input', '.', 'doc', '_', 'ids', '\\n       ', ' assert', ' doc', '_', 'ids', ' is', ' not', ' None', '\\n\\n       ', ' ret', '_', 'chunks', ' =', ' self', '.', 'retrieve', '_', 'chunks', '(', '\\n           ', ' current', '_', 'code', ',', ' selected', '_', 'range', ',', ' sys', '_', 'input', '.', 'path', ',', ' doc', '_', 'ids', '=', 'doc', '_', 'ids', '\\n       ', ' )', '\\n\\n       ', ' if', ' should', '_', 'cancel', '():', '\\n           ', ' raise', ' Generation', 'Canceled', 'Error', '()', '\\n\\n       ', ' prompt', '_', 'input', ' =', ' Edit', 'Gen', 'Prompt', 'Input', '(', '\\n           ', ' current', '_', 'path', '=', 'sys', '_', 'input', '.', 'path', ',', '\\n           ', ' current', '_', 'code', '=', 'current', '_', 'code', ',', '\\n           ', ' edit', '_', 'region', '=', 'selected', '_', 'range', ',', '\\n           ', ' instruction', '=', 'sys', '_', 'input', '.', 'instruction', ',', '\\n           ', ' recent', '_', 'changes', '=', 'sys', '_', 'input', '.', 'recent', '_', 'file', '_', 'changes', ',', '\\n           ', ' retrieval', '_', 'chunks', '=', 'ret', '_', 'chunks', ',', '\\n       ', ' )', '\\n       ', ' prompt', '_', 'tokens', ' =', ' self', '.', 'prompt', '_', 'formatter', '.', 'format', '_', 'input', '_', 'prompt', '(', 'prompt', '_', 'i', '<|ret-start|>', '<filename>', 'research', '/', 'eval', '/', 'h', 'arness', '/', 'systems', '/', 'next', '_', 'edit', '_', 'gen', '_', 'system', '.', 'py', '<|ret-body|>', 'ext', ' edits', '.\"\"\"', '\\n\\n   ', ' model', ':', ' Fast', 'Forward', 'Model', '\\n   ', ' generation', '_', 'options', ':', ' Generation', 'Options', '\\n   ', ' retrie', 'ver', ':', ' Document', 'Index', ' |', ' None', '\\n   ', ' prompt', '_', 'formatter', ':', ' Edit', 'Gen', 'Prompt', 'Formatter', '\\n   ', ' max', '_', 'retrie', 'ved', '_', 'chunks', ':', ' int', ' =', ' ', '3', '0', '\\n\\n   ', ' def', ' __', 'post', '_', 'init', '__(', 'self', '):', '\\n       ', ' self', '._', 'loaded', ' =', ' False', '\\n       ', ' self', '._', 'generate', '_', 'cache', ':', ' L', 'RU', 'Cache', '[', '\\n           ', ' Edit', 'Gen', 'System', 'Input', ',', ' tuple', '[', 'Edit', 'Gen', 'System', 'Output', ',', ' File', 'Logger', ' |', ' None', ']', '\\n       ', ' ]', ' =', ' L', 'RU', 'Cache', '(', 'max', 'size', '=', '5', '0', '0', ')', '\\n       ', ' \"\"\"', 'Cache', ' system', ' output', ' by', ' input', '.\"\"\"', '\\n       ', ' self', '._', 'model', '_', 'cache', ':', ' L', 'RU', 'Cache', '[', 'Frozen', 'Raw', 'Model', 'Input', ',', ' _', 'Cached', 'Model', 'Output', ']', ' =', ' L', 'RU', 'Cache', '(', '\\n           ', ' max', 'size', '=', '5', '0', '0', '\\n       ', ' )', '\\n       ', ' \"\"\"', 'Cache', ' raw', ' model', ' output', ' by', ' raw', ' input', '.\"\"\"', '\\n\\n   ', ' def', ' generate', '(', '\\n       ', ' self', ',', '\\n       ', ' sys', '_', 'input', ':', ' Edit', 'Gen', 'System', 'Input', ',', '\\n       ', ' should', '_', 'cancel', ':', ' Callable', '[[', '],', ' bool', ']', ' =', ' lambda', ':', ' False', ',', '\\n       ', ' file', '_', 'logger', ':', ' File', 'Logger', ' |', ' None', ' =', ' None', ',', '\\n   ', ' )', ' ->', ' Edit', 'Gen', 'System', 'Output', ':', '\\n       ', ' if', ' sys', '_', 'input', ' in', ' self', '._', 'generate', '_', 'cache', ':', '\\n           ', ' cached', '_', 'output', ',', ' old', '_', 'logger', ' =', ' self', '._', 'generate', '_', 'cache', '[', 'sys', '_', 'input', ']', '\\n           ', ' if', ' file', '_', 'logger', ' and', ' old', '_', 'logger', ':', '\\n               ', ' file', '_', 'log', 'g', '<|ret-start|>', '<filename>', 'research', '/', 'eval', '/', 'h', 'arness', '/', 'systems', '/', 'next', '_', 'edit', '_', 'gen', '_', 'system', '.', 'py', '<|ret-body|>', 'le', '_', 'changes', '={', 'file', '_', 'change', '_', 'str', '}\",', '\\n       ', ' ]', '\\n       ', ' return', ' \"\\\\', 'n', '\".', 'join', '(', 'lines', ')', '\\n\\n', '\\n', '@', 'dataclass', '\\n', 'class', ' Edit', 'Gen', 'System', 'Output', '(', 'Edit', 'Gen', 'Output', '):', '\\n   ', ' \"\"\"', 'The', ' output', ' of', ' the', ' next', ' edit', ' generation', ' system', '.\"\"\"', '\\n\\n   ', ' replacement', ':', ' str', '\\n   ', ' \"\"\"', 'The', ' suggested', ' replacement', ' text', '.\"\"\"', '\\n\\n   ', ' changed', ':', ' bool', '\\n   ', ' \"\"\"', 'True', ' if', ' the', ' model', ' suggested', ' a', ' change', ',', ' False', ' otherwise', '.\"\"\"', '\\n\\n   ', ' prompt', '_', 'tokens', ':', ' Token', 'Seq', '\\n   ', ' \"\"\"', 'The', ' full', ' prompt', ' tokens', '.\"\"\"', '\\n\\n   ', ' output', '_', 'tokens', ':', ' Token', 'Seq', '\\n   ', ' \"\"\"', 'The', ' raw', ' output', ' tokens', '.\"\"\"', '\\n\\n   ', ' prob', '_', 'changed', ':', ' float', ' |', ' None', '\\n   ', ' \"\"\"', 'The', ' probability', ' of', ' the', ' model', ' suggesting', ' a', ' change', '.', '\\n\\n   ', ' This', ' is', ' None', ' if', ' the', ' input', ' has', ' specified', ' `', 'must', '_', 'change', '=', 'True', '`.', '\\n   ', ' \"\"\"', '\\n\\n   ', ' change', '_', 'confidence', ':', ' float', '\\n   ', ' \"\"\"', 'The', ' probability', ' of', ' the', ' predicted', ' diff', ' condition', 'ed', ' on', ' having', ' a', ' change', '.', '\\n\\n   ', ' Multiply', 'ing', ' this', ' with', ' `', 'prob', '_', 'changed', '`', ' gives', ' the', ' joint', ' probability', ' of', ' the', ' full', '\\n   ', ' model', ' output', '.', '\\n   ', ' \"\"\"', '\\n\\n', '\\n', '@', 'register', '_', 'system', '(\"', 'next', '_', 'edit', '_', 'gen', '\")', '\\n', '@', 'dataclass', '\\n', 'class', ' Next', 'Edit', 'Gen', 'System', '(', 'Abstract', 'System', '[', 'Edit', 'Gen', 'System', 'Input', ',', ' Edit', 'Gen', 'System', 'Output', ']):', '\\n   ', ' \"\"\"', 'A', ' suggested', ' edit', ' generation', ' system', ' that', ' can', ' suggest', ' n', '<|ret-start|>', '<filename>', 'experimental', '/', 'd', 'xy', '/', 'demo', '/', 'launch', '_', 'flask', '.', 'py', '<|ret-body|>', '           ', ' logger', '.', 'info', '(', 'f', '\"', 'Raw', ' model', ' output', ':\\\\', 'n', '{', 'full', '_', 'response', '}\")', '\\n       ', ' except', ' Base', 'Exception', ' as', ' e', ':', '\\n           ', ' logger', '.', 'info', '(', 'f', '\"', 'Fail', ' to', ' write', ' cache', ' file', ' ({', 'cache', '_', 'file', '_', 'path', '})', ' due', ' to', ' {', 'e', '}\")', '\\n       ', ' #', ' If', ' it', ' is', ' the', ' debug', ' mode', ',', ' return', ' more', ' information', '.', '\\n       ', ' if', ' debug', ':', '\\n           ', ' return', ' jsonify', '(', '\\n               ', ' {', '\\n                   ', ' \"', 'response', '\":', ' response', ',', '\\n                   ', ' \"', 'modified', '_', 'code', '\":', ' \"\",', '\\n                   ', ' \"', 'status', '\":', ' \"', 'success', '\",', '\\n                   ', ' **', 'debug', '_', 'json', '_', 'data', ',', '\\n               ', ' }', '\\n           ', ' )', '\\n       ', ' else', ':', '\\n           ', ' return', ' jsonify', '(', '\\n               ', ' {\"', 'response', '\":', ' response', ',', ' \"', 'modified', '_', 'code', '\":', ' \"\",', ' \"', 'status', '\":', ' \"', 'success', '\"}', '\\n           ', ' )', '\\n   ', ' except', ' Base', 'Exception', ' as', ' e', ':', '\\n       ', ' print', '(', 'f', '\"', 'Fail', ' to', ' call', ' model', '.', 'generate', ' due', ' to', ' {', 'e', '}\")', '\\n       ', ' return', ' jsonify', '({\"', 'response', '\":', ' \"\",', ' \"', 'status', '\":', ' f', '\"', 'failed', ' due', ' to', ' {', 'e', '}\"', '})', '\\n\\n', '\\n', '@', 'app', '.', 'route', '(\"/', 'log', '_', 'status', '\",', ' methods', '=[\"', 'POST', '\"])', '\\n', 'def', ' log', '_', 'status', '():', '\\n   ', ' \"\"\"', 'Logging', ' the', ' status', '.', '\\n\\n   ', ' -', ' request', '_', 'id', ':', ' str', '\\n   ', ' -', ' instruction', ':', ' str', '\\n   ', ' \"\"\"', '\\n   ', ' data', ' =', ' request', '.', 'get', '_', 'json', '()', '\\n', '<|ret-start|>', '<filename>', 'research', '/', 'model', '_', 'server', '/', 'launch', '_', 'model', '_', 'server', '.', 'py', '<|ret-body|>', '   ', ' response', ' =', ' Next', 'Edit', 'Combined', 'Response', '(', '\\n       ', ' changed', '_', 'file', '_', 'regions', '=', 'system', '_', 'output', '.', 'changed', '_', 'file', '_', 'regions', ',', '\\n       ', ' unknown', '_', 'blob', '_', 'names', '=', 'unknown', '_', 'blob', '_', 'names', ',', '\\n       ', ' canceled', '=', 'False', ',', '\\n   ', ' )', '\\n   ', ' logging', '.', 'info', '(', 'f', '\"', 'Response', ' ({', 'request', '_', 'id', '=}', '):\\\\', 'n', '{', 'str', '(', 'response', ')}', '\")', '\\n\\n   ', ' current', '_', 'app', '.', 'requests', '_', 'handled', ' +=', ' ', '1', '\\n\\n   ', ' return', ' jsonify', '(', 'response', ')', '\\n\\n', '\\n', '@', 'api', '_', 'proxy', '_', 'bp', '.', 'route', '(\"/', 'next', '_', 'edit', '_', 'gen', '\",', ' methods', '=[\"', 'POST', '\"])', '\\n', '@', 'ensure', '_', 'authenticated', '\\n', 'def', ' handle', '_', 'next', '_', 'edit', '_', 'gen', '_', 'request', '():', '\\n   ', ' \"\"\"', 'Handles', ' a', ' next', ' edit', ' generation', ' request', '.\"\"\"', '\\n\\n   ', ' if', ' current', '_', 'app', '.', 'args', '.', 'disable', '_', 'comp', 'letions', ':', '\\n       ', ' abort', '(', '4', '0', '0', ',', ' \"', 'Server', ' does', ' not', ' support', ' complet', 'ions', '.\")', '\\n\\n   ', ' system', ' =', ' current', '_', 'app', '.', 'system', '\\n   ', ' if', ' isinstance', '(', 'system', ',', ' Next', 'Edit', 'Combined', 'System', '):', '\\n       ', ' system', ' =', ' system', '.', 'edit', '_', 'gen', '_', 'system', '\\n   ', ' if', ' not', ' isinstance', '(', 'system', ',', ' Next', 'Edit', 'Gen', 'System', '):', '\\n       ', ' abort', '(', '4', '0', '0', ',', ' \"', 'Server', ' does', ' not', ' support', ' next', ' edit', ' generation', '.\")', '\\n\\n   ', ' #', ' Prepare', ' request', '\\n   ', ' request', '_', 'content', ':', ' Code', 'Edit', 'Request', ' =', ' parse', '_', 'json', '_', 'request', '(', '\\n       ', ' request', '.', 'get', '_', 'json', '(),', '\\n       ', ' Code', 'Edit', 'Request', ',', ' ', ' #', ' type', ':', ' ignore', '\\n', '<|ret-start|>', '<filename>', 'research', '/', 'model', '_', 'server', '/', 'launch', '_', 'model', '_', 'server', '.', 'py', '<|ret-body|>', '           ', ' else', ':', '\\n               ', ' logging', '.', 'warning', '(\"', 'blob', '_', 'set', ' is', ' None', '.\")', '\\n       ', ' if', ' unknown', '_', 'blob', '_', 'names', ':', '\\n           ', ' logging', '.', 'warning', '(', 'f', '\"', 'Found', ' {', 'len', '(', 'unknown', '_', 'blob', '_', 'names', ')}', ' missing', ' files', '.\")', '\\n           ', ' if', ' isinstance', '(', 'system', ',', ' Next', 'Edit', 'Gen', 'System', '):', '\\n               ', ' logging', '.', 'warning', '(', '\\n                   ', ' \"', 'Next', 'Edit', 'Gen', 'System', ' currently', ' cannot', ' handle', ' missing', ' files', '.\"', '\\n               ', ' )', '\\n               ', ' short', '_', 'response', ' =', ' Code', 'Edit', 'Response', '(', '\\n                   ', ' text', '=', 'request', '_', 'content', '.', 'selected', '_', 'text', ',', '\\n                   ', ' unknown', '_', 'blob', '_', 'names', '=', 'unknown', '_', 'blob', '_', 'names', ',', '\\n               ', ' )', '\\n               ', ' return', ' jsonify', '(', 'short', '_', 'response', ')', '\\n\\n       ', ' logging', '.', 'info', '(\"', 'Generating', '...\")', '\\n       ', ' start', ' =', ' time', '.', 'time', '()', '\\n\\n       ', ' system', '_', 'output', ' =', ' system', '.', 'generate', '(', 'system', '_', 'input', ')', '\\n\\n       ', ' logging', '.', 'info', '(', 'f', '\"', 'Generation', ' took', ' {', 'time', '.', 'time', '()', ' -', ' start', ':.', '2', 'f', '}', ' seconds', '\")', '\\n\\n       ', ' generated', '_', 'text', ',', ' misc', '_', 'dict', ' =', ' (', '\\n           ', ' system', '_', 'output', '.', 'generated', '_', 'text', ',', '\\n           ', ' system', '_', 'output', '.', 'extra', '_', 'output', '.', 'additional', '_', 'info', ',', '\\n       ', ' )', '\\n\\n       ', ' response', ' =', ' Code', 'Edit', 'Response', '(', '\\n           ', ' text', '=', 'generated', '_', 'text', ',', '\\n', '<|ret-start|>', '<filename>', 'research', '/', 'model', '_', 'server', '/', 'launch', '_', 'model', '_', 'server', '.', 'py', '<|ret-body|>', '           ', ' unknown', '_', 'blob', '_', 'names', '=', 'unknown', '_', 'blob', '_', 'names', ',', '\\n       ', ' )', '\\n\\n       ', ' json', '_', 'data', ' =', ' as', 'dict', '(', 'request', '_', 'content', ')', '\\n\\n       ', ' json', '_', 'data', '.', 'update', '(**', 'misc', '_', 'dict', ')', '\\n       ', ' json', '_', 'data', '.', 'update', '(', '\\n           ', ' {', '\\n               ', ' \"', 'response', '\":', ' generated', '_', 'text', ',', '\\n               ', ' \"', 'sender', '_', 'ip', '\":', ' request', '.', 'remote', '_', 'addr', ',', '\\n               ', ' \"', 'timestamp', '\":', ' datetime', '.', 'now', '().', 'iso', 'format', '(),', '\\n               ', ' \"', 'time', '_', 'cost', '_', 's', '\":', ' time', '.', 'time', '()', ' -', ' start', '_', 'time', ',', '\\n           ', ' }', '\\n       ', ' )', '\\n\\n       ', ' json', '_', 'data', '[\"', 'blobs', '\"]', ' =', ' doc', '_', 'ids', '\\n\\n       ', ' #', ' Log', ' the', ' user', ' id', ' as', ' well', '\\n       ', ' json', '_', 'data', '[\"', 'user', '_', 'id', '\"]', ' =', ' get', '_', 'user', '_', 'id', '()', '\\n       ', ' json', '_', 'data', '[\"', 'system', '_', 'spec', '\"]', ' =', ' current', '_', 'app', '.', 'system', '_', 'config', '_', 'str', '\\n       ', ' #', ' Save', ' all', ' the', ' necessary', ' information', ' into', ' a', ' json', ' file', '\\n       ', ' save', '_', 'code', '_', 'edit', '_', 'data', '_', 'json', '(', '\\n           ', ' json', '_', 'data', ',', '\\n           ', ' get', '_', 'request', '_', 'id', '(),', '\\n           ', ' \"-', 'Call', '\",', '\\n           ', ' request', '_', 'content', '.', 'completion', '_', 'url', ',', '\\n       ', ' )', '\\n\\n       ', ' current', '_', 'app', '.', 'requests', '_', 'handled', ' +=', ' ', '1', '\\n\\n', '<|ret-start|>', '<filename>', 'research', '/', 'model', '_', 'server', '/', 'launch', '_', 'model', '_', 'server', '.', 'py', '<|ret-body|>', '               ', ' \"', 'system', '_', 'spec', '\":', ' current', '_', 'app', '.', 'system', '_', 'config', '_', 'str', ',', '\\n               ', ' \"', 'artifacts', '\":', ' artifacts', ',', '\\n           ', ' }', '\\n       ', ' )', '\\n       ', ' if', ' request', '_', 'content', '.', 'blobs', ':', '\\n           ', ' blob', '_', 'set', ' =', ' current', '_', 'app', '.', 'blob', '_', 'list', '_', 'mgr', '.', 'return', '_', 'flat', '_', 'blob', '_', 'list', '(', '\\n               ', ' request', '_', 'content', '.', 'blobs', '\\n           ', ' )', '\\n           ', ' json', '_', 'data', '[\"', 'blobs', '\"]', ' =', ' tuple', '(', 'blob', '_', 'set', ')', ' if', ' blob', '_', 'set', ' is', ' not', ' None', ' else', ' None', '\\n\\n       ', ' #', ' Log', ' the', ' user', ' id', ' as', ' well', '\\n       ', ' #', ' Save', ' all', ' the', ' necessary', ' information', ' into', ' a', ' json', ' file', '\\n       ', ' save', '_', 'code', '_', 'edit', '_', 'data', '_', 'json', '(', '\\n           ', ' json', '_', 'data', ',', '\\n           ', ' get', '_', 'request', '_', 'id', '(),', '\\n           ', ' \"-', 'Call', '\",', '\\n           ', ' global', '_', 'log', '_', 'dir', '=', 'GLOBAL', '_', 'NEXT', '_', 'EDIT', '_', 'LOG', '_', 'DIR', ',', '\\n       ', ' )', '\\n\\n   ', ' current', '_', 'app', '.', 'requests', '_', 'handled', ' +=', ' ', '1', '\\n\\n   ', ' logging', '.', 'info', '(', 'f', '\"', 'Response', ' ({', 'request', '_', 'id', '=}', '):\\\\', 'n', '{', 'str', '(', 'response', ')}', '\")', '\\n\\n   ', ' return', ' jsonify', '(', 'response', ')', '\\n\\n', '\\n', 'lang', '_', 'guess', 'er', ' =', ' default', '_', 'language', '_', 'guess', 'er', '()', '\\n\\n', '\\n', 'def', ' should', '_', 'index', '_', 'path', '_', 'for', '_', 'retrie', 'val', '(', '\\n', '<|ret-start|>', '<filename>', 'research', '/', 'model', '_', 'server', '/', 'launch', '_', 'model', '_', 'server', '.', 'py', '<|ret-body|>', '       ', ' system', '_', 'input', ',', ' system', '_', 'output', '\\n   ', ' ):', '\\n       ', ' logging', '.', 'info', '(\"', 'Filtering', ' out', ' background', ' edit', '.\")', '\\n       ', ' generated', '_', 'text', ' =', ' system', '_', 'input', '.', 'selected', '_', 'code', '\\n   ', ' else', ':', '\\n       ', ' generated', '_', 'text', ' =', ' system', '_', 'output', '.', 'replacement', '\\n\\n   ', ' change', '_', 'prob', '_', 'text', ' =', ' f', '\"', 'prob', '_', 'changed', '={', 'system', '_', 'output', '.', 'prob', '_', 'changed', ':.', '1', '%', '}\"', '\\n   ', ' confidence', '_', 'text', ' =', ' f', '\"', 'change', '_', 'confidence', '={', 'system', '_', 'output', '.', 'change', '_', 'confidence', ':.', '1', '%', '}\"', '\\n   ', ' logging', '.', 'info', '(', '\\n       ', ' f', '\"', 'changed', '={', 'system', '_', 'output', '.', 'changed', '},', ' {', 'change', '_', 'prob', '_', 'text', '},', ' {', 'confidence', '_', 'text', '}\"', '\\n   ', ' )', '\\n\\n   ', ' line', '_', 'flags', ' =', ' compute', '_', 'line', '_', 'change', '_', 'flags', '(', 'system', '_', 'input', '.', 'selected', '_', 'code', ',', ' generated', '_', 'text', ')', '\\n\\n   ', ' model', '_', 'raw', '_', 'output', ' =', ' system', '.', 'prompt', '_', 'formatter', '.', 'tokenizer', '.', 'de', 'tokenize', '(', 'system', '_', 'output', '.', 'output', '_', 'tokens', ')', '\\n\\n   ', ' logging', '.', 'info', '(', 'f', '\"', 'Response', ' ({', 'request', '_', 'id', '=}', '):\\\\', 'n', '{', 'response', '}\")', '\\n\\n   ', ' current', '_', 'app', '.', 'requests', '_', 'handled', ' +=', ' ', '1', '\\n\\n   ', ' return', ' jsonify', '(', 'response', ')', '\\n\\n', '\\n', 'def', ' _', 'should', '_', 'filter', '_', 'background', '_', 'edit', '(', '\\n   ', ' sys', '_', 'input', ':', ' Edit', 'Gen', 'System', 'Input', ',', ' sys', '_', 'output', ':', ' Edit', 'Gen', 'System', 'Output', '\\n', '):', '\\n   ', ' \"\"\"', 'Whether', ' the', ' model', ' output', ' should', ' be', ' filtered', ' out', ' for', ' a', ' background', ' edit', ' request', '.\"\"\"', '\\n   ', ' if', ' not', ' sys', '_', 'output', '.', 'changed', ':', '\\n', '<fim_prefix>', '_', 'change', '_', 'flags', '=[],', '\\n               ', ' canceled', '=', 'True', ',', '\\n           ', ' )', '\\n           ', ' return', ' jsonify', '(', 'short', '_', 'response', ')', '\\n       ', ' finally', ':', '\\n           ', ' user', '_', 'queue', '.', 'finish', '_', 'request', '(', 'user', '_', 'key', ',', ' request', '_', 'id', ')', '\\n\\n   ', ' if', ' not', ' user', '_', 'requested', ' and', ' _', 'should', '_', 'filter', '_', 'background', '_', 'edit', '(', '\\n       ', ' system', '_', 'input', ',', ' system', '_', 'output', '\\n   ', ' ):', '\\n       ', ' logging', '.', 'info', '(\"', 'Filtering', ' out', ' background', ' edit', '.\")', '\\n       ', ' generated', '_', 'text', ' =', ' system', '_', 'input', '.', 'selected', '_', 'code', '\\n   ', ' else', ':', '\\n       ', ' generated', '_', 'text', ' =', ' system', '_', 'output', '.', 'replacement', '\\n\\n   ', ' change', '_', 'prob', '_', 'text', ' =', ' f', '\"', 'prob', '_', 'changed', '={', 'system', '_', 'output', '.', 'prob', '_', 'changed', ':.', '1', '%', '}\"', '\\n   ', ' confidence', '_', 'text', ' =', ' f', '\"', 'change', '_', 'confidence', '={', 'system', '_', 'output', '.', 'change', '_', 'confidence', ':.', '1', '%', '}\"', '\\n   ', ' logging', '.', 'info', '(', '\\n       ', ' f', '\"', 'changed', '={', 'system', '_', 'output', '.', 'changed', '},', ' {', 'change', '_', 'prob', '_', 'text', '},', ' {', 'confidence', '_', 'text', '}\"', '\\n   ', ' )', '\\n\\n   ', ' line', '_', 'flags', ' =', ' compute', '_', 'line', '_', 'change', '_', 'flags', '(', 'system', '_', 'input', '.', 'selected', '_', 'code', ',', ' generated', '_', 'text', ')', '\\n\\n   ', ' model', '_', 'raw', '_', 'output', ' =', ' system', '.', 'prompt', '_', 'formatter', '.', 'tokenizer', '.', 'de', 'tokenize', '(', 'system', '_', 'output', '.', 'output', '_', 'tokens', ')', '\\n\\n   ', ' logging', '.', 'info', '(', 'f', '\"', 'Response', ' ({', 'request', '_', 'id', '=}', '):\\\\', 'n', '{', 'model', '<fim_middle>'], token_ids=[81, 1294, 81, 2024])\n"]}], "source": ["print(cur_data[\"response\"])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RecencyInfo(tab_switch_events=[], git_diff_info=[], recent_changes=[ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(3297, 11547), replacement_text='le_changes={file_change_str}\",\\n        ]\\n        return \"\\\\n\".join(lines)\\n\\n\\n@dataclass\\nclass EditGenSystemOutput(EditGenOutput):\\n    \"\"\"The output of the next edit generation system.\"\"\"\\n\\n    replacement: str\\n    \"\"\"The suggested replacement text.\"\"\"\\n\\n    changed: bool\\n    \"\"\"True if the model suggested a change, False otherwise.\"\"\"\\n\\n    prompt_tokens: TokenSeq\\n    \"\"\"The full prompt tokens.\"\"\"\\n\\n    output_tokens: TokenSeq\\n    \"\"\"The raw output tokens.\"\"\"\\n\\n    prob_changed: float | None\\n    \"\"\"The probability of the model suggesting a change.\\n\\n    This is None if the input has specified `must_change=True`.\\n    \"\"\"\\n\\n    change_confidence: float\\n    \"\"\"The probability of the predicted diff conditioned on having a change.\\n\\n    Multiplying this with `prob_changed` gives the joint probability of the full\\n    model output.\\n    \"\"\"\\n\\n\\n@register_system(\"next_edit_gen\")\\n@dataclass\\nclass NextEditGenSystem(AbstractSystem[EditGenSystemInput, EditGenSystemOutput]):\\n    \"\"\"A suggested edit generation system that can suggest n', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='ext edits.\"\"\"\\n\\n    model: FastForwardModel\\n    generation_options: GenerationOptions\\n    retriever: DocumentIndex | None\\n    prompt_formatter: EditGenPromptFormatter\\n    max_retrieved_chunks: int = 30\\n\\n    def __post_init__(self):\\n        self._loaded = False\\n        self._generate_cache: LRUCache[\\n            EditGenSystemInput, tuple[EditGenSystemOutput, FileLogger | None]\\n        ] = LRUCache(maxsize=500)\\n        \"\"\"Cache system output by input.\"\"\"\\n        self._model_cache: LRUCache[FrozenRawModelInput, _CachedModelOutput] = LRUCache(\\n            maxsize=500\\n        )\\n        \"\"\"Cache raw model output by raw input.\"\"\"\\n\\n    def generate(\\n        self,\\n        sys_input: EditGenSystemInput,\\n        should_cancel: Callable[[], bool] = lambda: False,\\n        file_logger: FileLogger | None = None,\\n    ) -> EditGenSystemOutput:\\n        if sys_input in self._generate_cache:\\n            cached_output, old_logger = self._generate_cache[sys_input]\\n            if file_logger and old_logger:\\n                file_logg', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='er.log(\"original_logs.txt\", str(old_logger.log_dir))\\n        if should_cancel():\\n            raise GenerationCanceledError()\\n\\n        current_code = sys_input.prefix + sys_input.selected_code + sys_input.suffix\\n        selected_range = CharRange(\\n            len(sys_input.prefix),\\n            len(sys_input.prefix) + len(sys_input.selected_code),\\n        )\\n        doc_ids = sys_input.doc_ids\\n        assert doc_ids is not None\\n\\n        ret_chunks = self.retrieve_chunks(\\n            current_code, selected_range, sys_input.path, doc_ids=doc_ids\\n        )\\n\\n        if should_cancel():\\n            raise GenerationCanceledError()\\n\\n        prompt_input = EditGenPromptInput(\\n            current_path=sys_input.path,\\n            current_code=current_code,\\n            edit_region=selected_range,\\n            instruction=sys_input.instruction,\\n            recent_changes=sys_input.recent_file_changes,\\n            retrieval_chunks=ret_chunks,\\n        )\\n        prompt_tokens = self.prompt_formatter.format_input_prompt(prompt_i', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='nput)\\n        original_prompt_tokens = prompt_tokens\\n        tkn = self.prompt_formatter.tokenizer\\n\\n        if sys_input.must_change:\\n            # we add the has_change token to the prompt to force a change\\n            prompt_tokens.append(tkn.has_change_id)\\n\\n        raw_input = FrozenRawModelInput(\\n            tuple(prompt_tokens),\\n            max_generated_tokens=self.generation_options.max_generated_tokens,\\n        )\\n        cached_output = self._model_cache.get(raw_input)\\n        if cached_output is None:\\n            raw_output = self.model.raw_generate_tokens(\\n                prompt_tokens, self.generation_options, should_cancel=should_cancel\\n            )\\n            output_tokens = raw_output.tokens\\n            token_probs = raw_output.token_probs()\\n            if sys_input.must_change:\\n                prob_changed = None\\n                change_confidence = math.prod(token_probs)\\n            else:\\n                first_tk_probs = torch.softmax(raw_output.logits[0], dim=-1)\\n                prob_changed', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text=' = float(first_tk_probs[tkn.has_change_id].item())\\n                change_confidence = math.prod(token_probs[1:])\\n            cached_output = _CachedModelOutput(\\n                output_tokens=tuple(output_tokens),\\n                prob_changed=prob_changed,\\n                change_confidence=change_confidence,\\n            )\\n            self._model_cache[raw_input] = cached_output\\n        else:\\n            output_tokens = list(cached_output.output_tokens)\\n            prob_changed = cached_output.prob_changed\\n            change_confidence = cached_output.change_confidence\\n\\n        if sys_input.must_change:\\n            # we prepend the has_change token to output to pretend it was generated\\n            output_tokens = [tkn.has_change_id] + output_tokens\\n        # now parse the output\\n\\n        decoded = self.prompt_formatter.decode_output_tokens(\\n            output_tokens, sys_input.selected_code\\n        )\\n\\n        if file_logger:\\n            file_logger.log(\"prompt_tokens.txt\", tkn.detokenize(original_prompt_tokens', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='))\\n            prob_text = f\"({prob_changed=:.1%}, {change_confidence=:.1%})\\\\n\"\\n            file_logger.log(\\n                \"output_tokens.txt\", prob_text + tkn.detokenize(output_tokens)\\n            )\\n            file_logger.log(\"replacement.txt\", decoded.replacement)\\n        output = EditGenSystemOutput(\\n            decoded.replacement,\\n            changed=decoded.changed,\\n            prompt_tokens=prompt_tokens,\\n            output_tokens=output_tokens,\\n            prob_changed=prob_changed,\\n            change_confidence=cached_output.change_confidence,\\n        )\\n        self._generate_cache[sys_input] = output, file_logger\\n        return output\\n\\n    def retrieve_chunks(\\n        self,\\n        current_code: str,\\n        selected_range: CharRange,\\n        current_path: str,\\n        doc_ids: Iterable[DocumentId],\\n    ) -> list[Chunk]:\\n        if self.retriever is None:\\n            return []\\n        prefix, suffix = EditGenPromptFormatter.format_selected_code(\\n            current_code, selected_range, use_diff_', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='based_output=False\\n        )\\n        model_input = ModelInput(\\n            prefix=prefix,\\n            suffix=suffix,\\n            path=current_path,\\n        )\\n        chunks, _ = self.retriever.query(\\n            model_input, doc_ids=doc_ids, top_k=self.max_retrieved_chunks\\n        )\\n        return chunks\\n\\n    def add_docs(self, src_files: Iterable[Document]):\\n        if self.retriever is None:\\n            return\\n        indexed_ids = self.retriever.get_doc_ids()\\n        files = [f for f in src_files if f.id not in indexed_ids]\\n        self.retriever.add_docs(files)\\n\\n    def load(self):\\n        if self._loaded:\\n            return\\n        self.model.load()\\n        if self.retriever:\\n            self.retriever.load()\\n        self._loaded = True\\n\\n    def unload(self):\\n        if not self._loaded:\\n            return\\n        self.model.unload()\\n        if self.retriever:\\n            self.retriever.unload()\\n        self._loaded = False\\n\\n    def clear_retriever(self):\\n        if self.retriever:\\n            self.retri', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='ever.remove_all_docs()\\n\\n    def get_doc_ids(self) -> AbstractSet[DocumentId]:\\n        if self.retriever is None:\\n            return set()\\n        return self.retriever.get_doc_ids()\\n\\n    def get_model(self) -> FastForwardModel:\\n        return self.model\\n\\n    @classmethod\\n    def from_yaml_config(cls, config: dict) -> NextEditGenSystem:\\n        \"\"\"Returns a System object constructed using a config dictionary.\"\"\"\\n        from research.eval.harness import factories\\n\\n        model = factories.create_model(config[\"model\"])\\n        assert isinstance(model, FastForwardModel)\\n\\n        generation_options = GenerationOptions(**config[\"generation_options\"])\\n        retriever = factories.create_retriever(config[\"retriever\"])\\n        prompt_formatter = EditGenPromptFormatter(**config[\"prompt_formatter\"])\\n\\n        return NextEditGenSystem(\\n            model=model,\\n            generation_options=generation_options,\\n            retriever=retriever,\\n            prompt_formatter=prompt_formatter,\\n        )\\n\\n\\n@dataclass(frozen=', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(11547, 11547), replacement_text='True)\\nclass FrozenRawModelInput:\\n    \"\"\"A fully immutable raw model input suitable for caching.\"\"\"\\n\\n    full_prompt: NTuple[int]\\n    \"\"\"The full prompt tokens.\"\"\"\\n\\n    max_generated_tokens: int | None\\n    \"\"\"The maximum number of tokens to generate.\"\"\"\\n\\n\\n@dataclass(frozen=True)\\nclass _CachedModelOutput:\\n    output_tokens: NTuple[int]\\n    prob_changed: float | None\\n    change_confidence: float\\n', present_in_blob=False), ReplacementText(blob_name='822292230fa7a4f85b2c351b1714072b1cde3f8718fc29215e3d55955d3b6858', path='research/eval/harness/systems/next_edit_gen_system.py', crange=IntRange(0, 1012), replacement_text='\"\"\"A system for generating next edits.\"\"\"\\n\\nfrom __future__ import annotations\\n\\nimport math\\nfrom dataclasses import dataclass\\nfrom typing import AbstractSet, Callable, FrozenSet, Iterable, Literal\\n\\nimport torch\\nfrom cachetools import LRUCache\\n\\nfrom base.ranges.range_types import CharRange\\nfrom base.static_analysis.common import shorten_str\\nfrom research.core.changes import Changed\\nfrom research.core.diff_utils import File\\nfrom research.core.model_input import ModelInput\\nfrom research.core.types import Chunk, Document, DocumentId, NTuple\\nfrom research.core.utils import FileLogger\\nfrom research.eval.harness.systems.abs_system import AbstractSystem, register_system\\nfrom research.fim.fim_prompt import TokenSeq\\nfrom research.models.fastforward_models import FastForwardModel, GenerationCanceledError\\nfrom research.models.meta_model import GenerationOptions\\nfrom research.next_edits.edit_gen_formatters import (\\n    DiffStr,\\n    EditGenPromptFormatter,\\n    EditGenPromptInput,\\n)\\nfrom research.next_edits.edit_gen_sampler ', present_in_blob=False), ReplacementText(blob_name='deda9902fa88d68e41d52f2486cd63abbdfec1ce6dc1b8c7c7502f5b67ad4663', path='research/models/fastforward_models.py', crange=IntRange(2895, 3495), replacement_text='ption):\\n    \"\"\"Indicates that the current model generation was canceled.\"\"\"\\n\\n\\n@dataclass\\nclass RawGenerateOutput:\\n    \"\"\"The output of raw_generate_tokens.\"\"\"\\n\\n    tokens: list[int]\\n    \"\"\"The generated tokens.\"\"\"\\n\\n    logits: list[torch.Tensor]\\n    \"\"\"The logits of each generated token.\"\"\"\\n\\n    def token_probs(self) -> list[float]:\\n        \"\"\"The probability of each generated token.\"\"\"\\n        output_logits = torch.stack(self.logits)\\n        tokens_tensor = torch.tensor(self.tokens, device=output_logits.device)\\n        token_probs = (\\n            torch.softmax(output_logits, dim=-1)\\n            .gather(dim=-1, index=tokens_tensor.unsqueeze(-1))\\n            .squeeze(-1)\\n        )\\n        return token_probs.float().tolist()\\n\\n\\nclass FastForwardModel(GenerativeLanguageModel):\\n    \"\"\"A model that uses the inference host for inference.\"\"\"\\n\\n    stop_tokens: Optional[Collection[int]] = None\\n    \"\"\"A list of tokens to stop generation on.\\n\\n    If None, will stop generating on tokenizer.eod_id.\\n    \"\"\"\\n\\n    token_boost', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(0, 11201), replacement_text='\"\"\"A NextEditCombinedSystem combines a NextEditLocationSystem and NextEditGenSystem.\"\"\"\\n\\nfrom __future__ import annotations\\n\\nimport logging\\nimport re\\nfrom dataclasses import dataclass\\nfrom typing import AbstractSet, Callable, Sequence\\n\\nfrom base.prompt_format.next_edit_location.prompt_formatter import DiffHunk\\nfrom base.static_analysis.common import check_not_none, shorten_str\\nfrom research.core.next_edit_location_prompt_input import (\\n    FileLocation,\\n    ResearchNextEditLocationPromptInput,\\n)\\nfrom research.core.types import Document, DocumentId, NTuple\\nfrom research.core.utils import FileLogger\\nfrom research.eval.harness.systems.abs_system import (\\n    AbstractSystem,\\n    register_system,\\n)\\nfrom research.eval.harness.systems.next_edit_gen_system import (\\n    EditGenSystemInput,\\n    NextEditGenSystem,\\n)\\nfrom research.eval.harness.systems.next_edit_location_system import (\\n    BasicNextEditLocationSystem,\\n)\\nfrom research.models.fastforward_models import GenerationCanceledError\\nfrom research.next_edits.edit_g', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='en_formatters import DiffStr\\n\\n\\n@dataclass\\nclass NextEditCombinedSystemInput:\\n    \"\"\"The input to NextEditCombined system.\"\"\"\\n\\n    instruction: str\\n    \"\"\"The user\\'s instruction about the ongoing task/changes.\"\"\"\\n\\n    recent_file_changes_str: DiffStr\\n    \"\"\"The changes made to the files since the last commit, as a diff string.\"\"\"\\n\\n    recent_file_changes_hunks: NTuple[DiffHunk]\\n    \"\"\"The changes made to the files since the last commit, as DiffHunks.\\n\\n    TODO: this contains the same info as `recent_file_changes_str` and will be removed\\n    in a future refactoring.\\n    \"\"\"\\n\\n    doc_ids: frozenset[DocumentId]\\n    \"\"\"The sequence of document ids that are available for retrieval.\"\"\"\\n\\n    blocked_locations: frozenset[FileLocation]\\n    \"\"\"A set of file locations that should be blocked from being returned.\"\"\"\\n\\n    def summary(self, max_str_len: int = 200):\\n        \"\"\"Return a shortened summary of the input fields.\"\"\"\\n\\n        def shorten(s: str) -> str:\\n            return repr(shorten_str(s, max_len=max_str_len))\\n\\n ', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='       lines = [\\n            f\"instruction: {shorten(self.instruction)}\",\\n            f\"num_doc_ids: {len(self.doc_ids)}\",\\n            f\"blocked_locations: {len(self.blocked_locations)}\",\\n            f\"recent_changes: {shorten(self.recent_file_changes_str.diff_text)}\",\\n        ]\\n        return \"\\\\n\".join(lines)\\n\\n\\n@dataclass\\nclass NextEditCombinedSystemOutput:\\n    \"\"\"The output of NextEditCombined system.\"\"\"\\n\\n    changed_file_regions: Sequence[ScoredFileHunk]\\n    \"\"\"A list of changed file regions annotated with scores.\\n\\n    The scores are predicted by the next edit location model.\\n    \"\"\"\\n\\n\\n@dataclass\\nclass ScoredFileHunk:\\n    \"\"\"A scored region of a changed file.\"\"\"\\n\\n    file_location: FileLocation\\n    \"\"\"The file region of the change.\"\"\"\\n\\n    original_code: str\\n    \"\"\"The original code in the region.\"\"\"\\n\\n    updated_code: str\\n    \"\"\"The updated code in the region.\"\"\"\\n\\n    localization_score: float\\n    \"\"\"The score of this region according to the location model.\"\"\"\\n\\n    editing_score: float\\n    \"\"\"The score of', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text=' this region according to the generation model.\"\"\"\\n\\n\\n@register_system(\"next_edit_combined\")\\n@dataclass\\nclass NextEditCombinedSystem(\\n    AbstractSystem[NextEditCombinedSystemInput, NextEditCombinedSystemOutput]\\n):\\n    \"\"\"A system that runs NextEditGen on the results from NextEditLocation.\"\"\"\\n\\n    edit_location_system: BasicNextEditLocationSystem\\n    \"\"\"The next edit location system.\"\"\"\\n\\n    edit_gen_system: NextEditGenSystem\\n    \"\"\"The next edit generation system.\"\"\"\\n\\n    max_changes_to_return: int\\n    \"\"\"The max number of `ChangedFileRegion`s to return in each generate call.\"\"\"\\n\\n    max_changes_to_attempt: int\\n    \"\"\"The max number of times that we can call the next edit generation system.\\n\\n    When this limit is hit, even if we have not found `max_changes_to_return` changes,\\n    we will return the changes we have found so far.\\n    \"\"\"\\n\\n    def __post_init__(self):\\n        if self.max_changes_to_return > self.max_changes_to_attempt:\\n            raise ValueError(\\n                f\"{self.max_changes_to_return=', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='} cannot be greater than \"\\n                f\"{self.max_changes_to_attempt=}\"\\n            )\\n\\n    def generate(\\n        self,\\n        sys_input: NextEditCombinedSystemInput,\\n        should_cancel: Callable[[], bool] = lambda: False,\\n        file_logger: FileLogger | None = None,\\n    ) -> NextEditCombinedSystemOutput:\\n        if self.max_changes_to_attempt < 0:\\n            raise ValueError(f\"{self.max_changes_to_attempt=} cannot be negative.\")\\n        if self.max_changes_to_return < 0:\\n            raise ValueError(f\"{self.max_changes_to_return=} cannot be negative.\")\\n\\n        location_sys_input = ResearchNextEditLocationPromptInput(\\n            instruction=sys_input.instruction,\\n            recent_changes=sys_input.recent_file_changes_hunks,\\n            doc_ids=sys_input.doc_ids,\\n            top_k=self.max_changes_to_attempt + 1 + len(sys_input.blocked_locations),\\n        )\\n        if file_logger:\\n            location_logger = FileLogger(file_logger.log_dir / \"localization\")\\n        else:\\n            location_lo', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='gger = None\\n        location_output = self.edit_location_system.generate(\\n            location_sys_input, location_logger\\n        )\\n\\n        candidate_locations = location_output.candidate_locations\\n        # filter out blocked locations from the candidate list\\n        candidate_locations = [\\n            loc\\n            for loc in candidate_locations\\n            if loc.item not in sys_input.blocked_locations\\n        ]\\n\\n        candidate_locations.sort(key=lambda x: x.score, reverse=True)\\n        if len(candidate_locations) < self.max_changes_to_attempt:\\n            logging.warning(\\n                f\"Only found {len(candidate_locations)} changes, but \"\\n                f\"max_changes_to_attempt is {self.max_changes_to_attempt}.\"\\n            )\\n\\n        all_docs = self.edit_location_system.get_documents(sys_input.doc_ids)\\n        path_to_doc = {doc.path: doc for doc in all_docs}\\n\\n        changes_found = list[ScoredFileHunk]()\\n        locations_rejected = list[ScoredFileHunk]()\\n\\n        for i, scored_loc in enumera', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='te(\\n            candidate_locations[: self.max_changes_to_attempt]\\n        ):\\n            if should_cancel():\\n                raise GenerationCanceledError()\\n            file_loc = scored_loc.item\\n            file_doc = path_to_doc[file_loc.path]\\n            file_lines = file_doc.text.splitlines(keepends=True)\\n            prefix_lines = file_lines[: file_loc.range.start]\\n            selected_lines = file_lines[file_loc.range.to_slice()]\\n            suffix_lines = file_lines[file_loc.range.stop :]\\n\\n            gen_sys_input = EditGenSystemInput(\\n                path=file_loc.path,\\n                prefix=\"\".join(prefix_lines),\\n                selected_code=\"\".join(selected_lines),\\n                suffix=\"\".join(suffix_lines),\\n                instruction=sys_input.instruction,\\n                recent_file_changes=sys_input.recent_file_changes_str,\\n                doc_ids=sys_input.doc_ids,\\n                must_change=False,\\n            )\\n            if file_logger:\\n                gen_logger = FileLogger(file_log', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='ger.log_dir / f\"generation-{i}\")\\n            else:\\n                gen_logger = None\\n            edit_gen_output = self.edit_gen_system.generate(\\n                gen_sys_input, should_cancel=should_cancel, file_logger=gen_logger\\n            )\\n            change_found = ScoredFileHunk(\\n                file_location=file_loc,\\n                original_code=\"\".join(selected_lines),\\n                updated_code=edit_gen_output.replacement,\\n                localization_score=scored_loc.score,\\n                editing_score=check_not_none(edit_gen_output.prob_changed),\\n            )\\n            if equal_modular_spaces(\\n                change_found.updated_code, change_found.original_code\\n            ):\\n                if edit_gen_output.changed:\\n                    # we reject any pure-whitespace changes\\n                    logging.warning(\\n                        f\"Rejecting pure whitespace change: {file_loc.path}\"\\n                    )\\n                locations_rejected.append(change_found)\\n            else:\\n      ', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='          changes_found.append(change_found)\\n                if len(changes_found) >= self.max_changes_to_return:\\n                    break\\n\\n        assert len(changes_found) <= self.max_changes_to_return\\n\\n        # sort by editing model\\'s score\\n        changes_found.sort(key=lambda x: x.editing_score, reverse=True)\\n\\n        def get_detailed_log_text():\\n            blocked_locations_text = \"\\\\n\".join(\\n                f\"path={loc.path}, lrange={loc.range}\"\\n                for loc in sys_input.blocked_locations\\n            )\\n            candidate_locations_text = \"\\\\n\".join(\\n                f\"path={loc.path}, lrange={loc.range}, score={score:.3g}\"\\n                for loc, score in candidate_locations\\n            )\\n            changes_found_text = \"\\\\n\".join(\\n                f\"path={scored.file_location.path}, lrange={scored.file_location.range}\"\\n                f\"edit_score={scored.editing_score:.3g}, \"\\n                f\"loc_score={scored.localization_score:.3g}, \"\\n                for scored in changes_found\\n     ', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11201), replacement_text='       )\\n            locations_rejected_text = \"\\\\n\".join(\\n                f\"path={scored.file_location.path}, lrange={scored.file_location.range}\"\\n                f\"edit_score={scored.editing_score:.3g}, \"\\n                f\"loc_score={scored.localization_score:.3g}, \"\\n                for scored in locations_rejected\\n            )\\n            SEP = \"~=\" * 40\\n            return (\\n                f\"blocked_locations:\\\\n{blocked_locations_text}\\\\n\"\\n                f\"{SEP}\\\\n\"\\n                f\"candidate_locations:\\\\n{candidate_locations_text}\\\\n\"\\n                f\"{SEP}\\\\n\"\\n                f\"changes_found:\\\\n{changes_found_text}\"\\n                f\"{SEP}\\\\n\"\\n                f\"locations_rejected:\\\\n{locations_rejected_text}\"\\n            )\\n\\n        if file_logger:\\n            file_logger.log(\"combined_results.txt\", get_detailed_log_text())\\n\\n        return NextEditCombinedSystemOutput(changes_found)\\n\\n    def add_docs(self, src_files: Sequence[Document]):\\n        if len(src_files) > 1:\\n            logging.info(\"Indexing the lo', present_in_blob=False), ReplacementText(blob_name='871604bc48b3339441313ea76b62d32f9d15b76c62c5cd9cc6f6c72e9c11400a', path='research/eval/harness/systems/next_edit_combined_system.py', crange=IntRange(11201, 11202), replacement_text='cation system...\")\\n        self.edit_location_system.add_docs(src_files)\\n        if len(src_files) > 1:\\n            logging.info(\"Indexing the generation system...\")\\n        self.edit_gen_system.add_docs(src_files)\\n\\n    def load(self):\\n        self.edit_location_system.load()\\n        self.edit_gen_system.load()\\n\\n    def unload(self):\\n        self.edit_location_system.unload()\\n        self.edit_gen_system.unload()\\n\\n    def clear_retriever(self):\\n        self.edit_location_system.clear_retriever()\\n        self.edit_gen_system.clear_retriever()\\n\\n    def get_doc_ids(self) -> AbstractSet[DocumentId]:\\n        return self.edit_location_system.get_doc_ids()\\n\\n    def get_model(self):\\n        raise NotImplementedError(\"Which model to return here?\")\\n\\n\\ndef equal_modular_spaces(text1: str, text2: str) -> bool:\\n    \"\"\"Return True if the two strings are equal except for whitespaces.\"\"\"\\n    text1 = re.sub(r\"\\\\s+\", \"\", text1)\\n    text2 = re.sub(r\"\\\\s+\", \"\", text2)\\n    return text1 == text2\\n', present_in_blob=False), ReplacementText(blob_name='633b95df8f8ca4419778b1ee043c9ba4ebaa97c254b44ab389f5d3d7c4544eac', path='experimental/jiayi/scripts/start_next_edit_gen_server.py', crange=IntRange(976, 2000), replacement_text='\": \"ethanol6_document\",\\n        \"max_tokens\": 999,\\n        \"add_path\": True,\\n    },\\n}\\n\\n\\ndef build_system():\\n    model = StarCoder_FastForward(\\n        AUGMENT_CHECKPOINTS_ROOT\\n        / \"next-edit-gen/S1.3_keep_most-R1.0-P1.3-100K_repos-starcoder7b\"\\n    )\\n    use_diff_based_output = True\\n\\n    retriever = factories.create_retriever(dict(ethanol_config))\\n    prompt_formatter = EditGenPromptFormatter(\\n        StarCoderTokenizer(), use_diff_based_output=use_diff_based_output\\n    )\\n\\n    return NextEditGenSystem(\\n        model,\\n        generation_options=GenerationOptions(max_generated_tokens=500),\\n        retriever=retriever,\\n        prompt_formatter=prompt_formatter,\\n    )\\n\\n\\ndef start_server():\\n    log_dir = (\\n        AUGMENT_EFS_ROOT\\n        / \"user/jiayi\"\\n        / \"model_server_logs\"\\n        / \"next_edit_combined_server_logs\"\\n    )\\n\\n    system = build_system()\\n    system.load()\\n    main(\\n        system,\\n        input_args=(\\n            \"--port\",\\n            \"5000\",\\n            \"--host\",\\n            \"0.0.0.0\",\\n', present_in_blob=True), ReplacementText(blob_name='3a2d58a5257c14aa8d70d27fa261b92c6d6138d9e4238d9dc22e3cb58ec2ff5d', path='experimental/jiayi/scripts/debug_next_edit_combined_server.ipynb', crange=IntRange(0, 997), replacement_text='%load_ext autoreload\\n%autoreload 2\\n\\nfrom experimental.jiayi.scripts.start_next_edit_combined_server import (\\n    build_combined_system,\\n)\\n\\nsystem = build_combined_system()\\n# system.edit_gen_system.retriever = None  # this disables retrieval for the gen system\\nsystem.load()\\n\\nfrom research.eval.harness.systems.next_edit_combined_system import (\\n    NextEditCombinedSystem,\\n)\\n\\n# below clears all system caches\\nsystem: NextEditCombinedSystem\\nsystem.edit_location_system._generate_cache.clear()\\nsystem.edit_gen_system._generate_cache.clear()\\nsystem.max_changes_to_attempt = 20\\n\\nimport importlib\\n\\nfrom research.core.constants import AUGMENT_EFS_ROOT\\nfrom research.model_server import launch_model_server\\n\\n# always reload the model_server module to avoid API endpoint errors.\\nimportlib.reload(launch_model_server)\\n\\n\\nlog_dir = AUGMENT_EFS_ROOT / \"user/jiayi\" / \"model_server_logs\"\\nlaunch_model_server.main(\\n    system,\\n    input_args=(\"--port\", \"5001\", \"--host\", \"0.0.0.0\", \"--log_dir\", str(log_dir)),\\n)', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(0, 9241), replacement_text='\"\"\"A next edit location system that can suggest next edits.\"\"\"\\n\\nfrom collections import defaultdict\\nfrom typing import AbstractSet, Iterable, Sequence\\n\\nfrom cachetools import LRUCache\\nfrom unidiff.constants import RE_HUNK_HEADER\\n\\nfrom base.prompt_format.next_edit_location.prompt_formatter import DiffHunk\\nfrom base.ranges.range_types import LineRange\\nfrom base.static_analysis.common import groupby\\nfrom research.core.model_input import ModelInput\\nfrom research.core.next_edit_location_prompt_input import (\\n    FileLocation,\\n    NextEditLocationOutput,\\n    ResearchNextEditLocationPromptInput,\\n)\\nfrom research.core.types import Chunk, Document, DocumentId, Scored\\nfrom research.core.utils import FileLogger\\nfrom research.eval.harness import factories\\nfrom research.eval.harness.systems.abs_system import (\\n    NextEditLocationSystem,\\n    register_system,\\n)\\nfrom research.models.meta_model import GenerativeLanguageModel\\nfrom research.retrieval.types import DocumentIndex\\n\\n\\n@register_system(\"next_edit_location\")\\nclass Basi', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='cNextEditLocationSystem(NextEditLocationSystem):\\n    \"\"\"A next edit location system that generates next edit locations.\"\"\"\\n\\n    def __init__(\\n        self,\\n        retriever: DocumentIndex,\\n        group_by_path: bool = False,\\n        filter_input_ranges: bool = False,\\n    ):\\n        \"\"\"Create a new next edit location system.\\n\\n        Args:\\n            retriever: The retriever to use for retrieving chunks.\\n            group_by_path: Whether to group retrieved chunks by file. If True, we will\\n                call the retriever once per modified file and aggregate scores.\\n                If False, we will call it once with the entire diff.\\n            filter_input_ranges: Whether to filter out retrieved chunks that intersect\\n                with a recently-changed hunk in the input.\\n        \"\"\"\\n        self._retriever = retriever\\n        self.__loaded = False\\n        self._group_by_path = group_by_path\\n        self._filter_input_ranges = filter_input_ranges\\n        self._generate_cache: LRUCache[\\n            Re', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='searchNextEditLocationPromptInput,\\n            tuple[NextEditLocationOutput, FileLogger | None],\\n        ] = LRUCache(maxsize=500)\\n\\n    def generate(\\n        self,\\n        model_input: ResearchNextEditLocationPromptInput,\\n        file_logger: FileLogger | None = None,\\n    ) -> NextEditLocationOutput:\\n        if model_input in self._generate_cache:\\n            cached_output, old_logger = self._generate_cache[model_input]\\n            if file_logger and old_logger:\\n                file_logger.log(\"original_logs.txt\", str(old_logger.log_dir))\\n            return cached_output\\n\\n        if self._group_by_path:\\n            # Query the retriever once per modified file.\\n            # Technically, we should only ever have a single hunk per file, but older\\n            # versions of the dataset had multiple hunks per file; also it makes the\\n            # code simpler to keep the hunks as a list.\\n            # Also note that the `DiffHunk`s include all diffs in a single file (and\\n            # include the diff header).\\n   ', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='         subqueries = groupby(model_input.recent_changes, lambda hunk: hunk.path)\\n        else:\\n            # It\\'s ok that the path is an empty string here -- the next edit location\\n            # prompt formatters don\\'t use it.\\n            subqueries = {\"\": model_input.recent_changes}\\n\\n        # Aggregate scores for each chunk over the subqueries.\\n        chunk_scores = defaultdict[Chunk, list[float]](list)\\n        retriever_queries = list[str]()\\n        for path, hunks in subqueries.items():\\n            # Currently our retrievers require the input to be ModelInput.\\n            # This is a legacy type we want to move away from but haven\\'t yet.\\n            model_input_legacy_type = ModelInput(\\n                path=path,\\n                prefix=\"\\\\n\".join(map(lambda hunk: hunk.diff_text, hunks)),\\n                suffix=\"\",\\n                retrieved_chunks=[],\\n                target=None,\\n                extra={},\\n            )\\n            retriever_queries.append(\\n                f\"Path: {path}\\\\n{model_input_lega', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='cy_type.prefix}\\\\n\" + \"~=\" * 50,\\n            )\\n\\n            chunks, scores = self._retriever.query(\\n                model_input_legacy_type,\\n                doc_ids=model_input.doc_ids,\\n                top_k=model_input.top_k,\\n            )\\n\\n            for chunk, score in zip(chunks, scores):\\n                chunk_scores[chunk].append(score)\\n        if file_logger:\\n            file_logger.log(\"retriever_queries.txt\", \"\\\\n\".join(retriever_queries))\\n            blob_hash = hash(model_input.doc_ids)\\n            file_logger.log(\\n                \"num_blobs.txt\",\\n                f\"{blob_hash=}\\\\n({len(model_input.doc_ids)} blobs in total)\",\\n            )\\n\\n        # Aggregate scores for each chunk.\\n        candidate_locations = [\\n            # NOTE(arun): When aggregating scores across locations, we currently just\\n            # take their average. Scores are *not* calibrated across queries, so this\\n            # isn\\'t necessarily a meanigful aggregation.\\n            Scored(\\n                FileLocation(chunk.path, chu', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='nk.line_range),\\n                sum(scores) / len(subqueries),\\n            )\\n            for chunk, scores in chunk_scores.items()\\n            if chunk.path\\n        ]\\n\\n        if self._filter_input_ranges:\\n            # Filter out chunks that intersect with a recently-changed hunk.\\n            # Some retrievers prefer the same chunks we gave it as input, which hurts\\n            # the ranking of other chunks. Of course, by filtering out these chunks, we\\n            # cannot suggest locations within already-modified chunks, which hurts\\n            # recall.\\n            # Note that if this ever becomes a performance issue, we can use\\n            # the intervaltree library to perform more efficient intersections\\n            # or simply ignore cases with too many hunks.\\n            target_ranges_by_path = {\\n                hunk.path: {lrange for lrange in self._get_hunk_ranges(hunk)}\\n                for hunk in model_input.recent_changes\\n            }\\n            candidate_locations = [\\n                loc\\n       ', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='         for loc in candidate_locations\\n                if not any(\\n                    loc.item.range.intersect(target_range) is not None\\n                    for target_range in target_ranges_by_path.get(loc.item.path, [])\\n                )\\n            ]\\n\\n        # Finally, sort descending by scores\\n        candidate_locations.sort(key=lambda x: x.score, reverse=True)\\n\\n        unindexed_blob_names = sorted(\\n            set(model_input.doc_ids) - set(self._retriever.get_doc_ids())\\n        )\\n\\n        if file_logger:\\n            file_logger.log(\\n                \"candidate_locations.txt\",\\n                \"\\\\n\".join(\\n                    f\"path={loc.item.path}:{loc.item.range}, score={loc.score:.3g}\"\\n                    for loc in candidate_locations\\n                ),\\n            )\\n\\n            file_logger.log(\\n                \"missing_blobs.txt\",\\n                \"\\\\n\".join(unindexed_blob_names),\\n            )\\n\\n        output = NextEditLocationOutput(\\n            candidate_locations=candidate_locations,\\n           ', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text=' unknown_blob_names=unindexed_blob_names,\\n        )\\n        self._generate_cache[model_input] = output, file_logger\\n        return output\\n\\n    def get_documents(self, doc_ids: Iterable[DocumentId]) -> list[Document]:\\n        \"\"\"Return all documents indexed by the retriever.\"\"\"\\n        return self._retriever.get_docs(doc_ids)\\n\\n    def _get_hunk_ranges(self, hunk: DiffHunk) -> Iterable[LineRange]:\\n        \"\"\"Get the line range of the hunk.\"\"\"\\n        for line in hunk.diff_text.splitlines():\\n            match = RE_HUNK_HEADER.match(line)\\n            if match:\\n                groups = match.groups()\\n                # Note that diff line ranges are 1-based unless a new file is added\\n                # in which case they start from 0.\\n                yield LineRange(\\n                    start=max(0, int(groups[2]) - 1),\\n                    stop=max(1, int(groups[2]) + int(groups[3] or 0) - 1),\\n                )\\n\\n    @classmethod\\n    def from_yaml_config(cls, config: dict):\\n        retriever = factories.create_retrie', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='ver(config[\"retriever\"])\\n        kwargs = {}\\n        if \"group_by_path\" in config:\\n            kwargs[\"group_by_path\"] = config[\"group_by_path\"]\\n        if \"filter_input_ranges\" in config:\\n            kwargs[\"filter_input_ranges\"] = config[\"filter_input_ranges\"]\\n        if \"top_k\" in config:\\n            kwargs[\"top_k\"] = config[\"top_k\"]\\n        return cls(retriever=retriever, **kwargs)\\n\\n    def load(self):\\n        \"\"\"Load the model.\"\"\"\\n        if not self.__loaded:\\n            self._retriever.load()\\n        self.__loaded = True\\n\\n    def unload(self):\\n        \"\"\"Unload the model.\"\"\"\\n        if self.__loaded:\\n            self._retriever.unload()\\n            self.__loaded = False\\n\\n    def get_model(self) -> GenerativeLanguageModel:\\n        raise NotImplementedError()\\n\\n    def add_docs(self, src_files: Sequence[Document]):\\n        self._retriever.add_docs(src_files)\\n\\n    def get_doc_ids(self) -> AbstractSet[DocumentId]:\\n        return self._retriever.get_doc_ids()\\n\\n    def clear_retriever(self):\\n        self._ret', present_in_blob=True), ReplacementText(blob_name='4939f5cf03320bca4d43f1bafb686ae8dbe449a30e69130656d4cc004901c07d', path='research/eval/harness/systems/next_edit_location_system.py', crange=IntRange(9241, 9241), replacement_text='riever.remove_all_docs()\\n', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(94, 7122), replacement_text='from dataclasses import dataclass\\nimport datetime\\nimport pickle\\nimport shutil\\nimport time\\nimport warnings\\nfrom contextlib import contextmanager\\nfrom pathlib import Path\\nfrom subprocess import check_output\\nfrom typing import Any, Callable, Iterable, Optional, Sequence, TypeVar\\n\\nimport psutil\\nfrom typing_extensions import TypeVarTuple\\n\\nfrom base.static_analysis.common import assert_eq, assert_str_eq, check_not_none\\nfrom research.core.ui_sugar import UISugar\\n\\n# NOTE(Jiayi): We re-export these symbols from base as part of a staged refactor.\\n# this makes the linters happy\\n__reexported__ = [\\n    assert_eq,\\n    assert_str_eq,\\n    check_not_none,\\n]\\n\\n\\nclass Timer(UISugar):\\n    \"\"\"A Timer class to measure the duration of events.\"\"\"\\n\\n    def __init__(self):\\n        self.start = None\\n        self.end = None\\n\\n    def start_event(self) -> \"Timer\":\\n        self.start = datetime.datetime.now()\\n        return self\\n\\n    def end_event(self) -> \"Timer\":\\n        if self.start is None:\\n            print(\"Event has not started yet.', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(7122, 7122), replacement_text=' Cannot end.\")\\n        else:\\n            self.end = datetime.datetime.now()\\n        return self\\n\\n    def get_event_duration(self) -> Optional[float]:\\n        if self.start is None or self.end is None:\\n            return None\\n        duration = self.end - self.start\\n        return duration.total_seconds()\\n\\n    @classmethod\\n    def seconds2str(cls, total_seconds: float) -> str:\\n        hours, remainder = divmod(total_seconds, 3600)\\n        minutes, seconds = divmod(remainder, 60)\\n        return f\"{int(hours)} hours {int(minutes)} mins {int(seconds)} seconds\"\\n\\n    @classmethod\\n    def time_string(cls):\\n        \"\"\"Get a string showing the current time.\"\"\"\\n        ISOTIMEFORMAT = \"%Y-%m-%d %X\"\\n        string = \"[{:}]\".format(time.strftime(ISOTIMEFORMAT, time.gmtime(time.time())))\\n        return string\\n\\n\\n@contextmanager\\ndef time_block(\\n    name,\\n    log_func: Callable[[str], None] = print,\\n    stats: Optional[dict[str, float]] = None,\\n):\\n    \"\"\"A context manager to time the enclosed block.\\n\\n    Args:\\n        name: ', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(7122, 7122), replacement_text='the name of the timer to use.\\n        stats: an optional dictionary to store the timing results.\\n            If not set, the timing results are printed to stdout.\\n    \"\"\"\\n    start = time.time()\\n    yield\\n    end = time.time()\\n    if stats is not None:\\n        stats[name] = end - start\\n    else:\\n        log_func(f\"{name} - Execution time: {end - start:.4f} seconds\")\\n\\n\\nAT = TypeVar(\"AT\")\\nATs = TypeVarTuple(\"ATs\")\\n\\n\\nclass PickleCache:\\n    \"\"\"A simple cache class implemented using Pickle.\\n\\n    Use `cache.cached_call` to make cached function calls.\\n    \"\"\"\\n\\n    def __init__(self, cache_dir: Path):\\n        self.cache_dir = cache_dir\\n\\n    # flake8: noqa\\n    def cached_call(\\n        self,\\n        label: Path | str,\\n        f: Callable[[*ATs], AT],\\n        args: \"tuple[*ATs]\",\\n    ) -> AT:\\n        \"\"\"Make a funtion call whose result will get cached using the given label.\\n\\n        For example, to add caching to a statement\\n        ```\\n        y = f(x1, x2, ...)\\n        ```\\n        You can do\\n        ```\\n        cache ', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(7122, 7122), replacement_text='= PickleCache(cache_dir)\\n        y = cache.cached_call(\"y.pkl\", f, (x1, x2,...))\\n\\n        And the result will be cached to `cache_dir/y.pkl`.\\n        ```\\n        \"\"\"\\n        path = self.get_cache_file(label)\\n        if not path.exists():\\n            value = f(*args)\\n            path.parent.mkdir(parents=True, exist_ok=True)\\n            print(f\"[PickleCache] Saving to cache: \\'{path}\\'\")\\n            with path.open(\"wb\") as fs:\\n                pickle.dump(value, fs)\\n            return value\\n        else:\\n            print(f\"[PickleCache] Loading from cache: \\'{path}\\'\")\\n            with path.open(\"rb\") as fs:\\n                return pickle.load(fs)\\n\\n    def get_cache_file(self, label: str | Path) -> Path:\\n        \"\"\"Get the path of the cached file with the given label.\"\"\"\\n        if isinstance(label, str):\\n            label = Path(label)\\n        if not label.suffix:\\n            label = label.with_suffix(\".pkl\")\\n        return self.cache_dir / label\\n\\n    def is_cached(self, label: str | Path) -> bool:\\n        \"\"\"Chec', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(7122, 7122), replacement_text='k if the given path has been cached.\"\"\"\\n        return self.get_cache_file(label).exists()\\n\\n    def read_from_cache(self, label: Path | str) -> Any:\\n        \"\"\"Read the value from the cache under the given path.\"\"\"\\n        path = self.get_cache_file(label)\\n        if not path.exists():\\n            raise FileNotFoundError(f\"File not found: \\'{path}\\'\")\\n        with path.open(\"rb\") as fs:\\n            print(f\"[PickleCache] Loading from cache: \\'{path}\\'\")\\n            return pickle.load(fs)\\n\\n    def add(self, label: Path | str, value: Any):\\n        \"\"\"Add the given value to the cache under the given path.\"\"\"\\n        path = self.get_cache_file(label)\\n        path.parent.mkdir(parents=True, exist_ok=True)\\n        print(f\"[PickleCache] Saving to cache: \\'{path}\\'\")\\n        with path.open(\"wb\") as fs:\\n            pickle.dump(value, fs)\\n\\n    def remove(self, label: Path | str):\\n        \"\"\"Remove the given path from the cache.\"\"\"\\n        path = self.get_cache_file(label)\\n        if path.exists():\\n            path.unlink()\\n  ', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(7122, 7122), replacement_text='      else:\\n            warnings.warn(f\"[PickleCache] File not found: \\'{path}\\'\")\\n\\n    def clear(self):\\n        \"\"\"Clear all cached data.\"\"\"\\n        if self.cache_dir.exists():\\n            warnings.warn(f\"Clearing cache: at: {self.cache_dir}\")\\n            shutil.rmtree(self.cache_dir)\\n        else:\\n            warnings.warn(f\"No cache found at: {self.cache_dir}, skip clearing.\")\\n\\n\\ndef find_mounted_drive(drive_name: str) -> Path:\\n    \"\"\"Find the path of the mounted drive with the given name.\\n\\n    This function is only available on linux.\\n    Use the linux `mount` command to find the path of the mounted drive.\\n\\n    Args:\\n        drive_name: The name of the drive to find.\\n\\n    Returns:\\n        The path of the mounted drive.\\n    \"\"\"\\n    mount_output = check_output([\"mount\"]).decode(\"utf-8\")\\n    for line in mount_output.splitlines():\\n        parts = line.split()\\n        if len(parts) < 3:\\n            continue\\n        if line.split()[0] == drive_name:\\n            return Path(line.split()[2])\\n    raise ValueError(f\"D', present_in_blob=True), ReplacementText(blob_name='bab2d2c80f1bb04430469b8470b21c04cc46a035a66e8afc9a19b16fcce13a07', path='research/core/utils.py', crange=IntRange(7122, 7262), replacement_text='rive \\'{drive_name}\\' not found.\")\\n\\n\\nT = TypeVar(\"T\")\\n\\n\\ndef as_chunks(xs: Sequence[T], chunk_size: int) -> Iterable[Sequence[T]]:\\n    \"\"\"Yield chunks of the given iterable.\\n\\n    Args:\\n        xs: The iterable to chunk.\\n        chunk_size: The size of each chunk.\\n\\n    Yields:\\n        A chunk of the given iterable.\\n    \"\"\"\\n    for i in range(0, len(xs), chunk_size):\\n        yield xs[i : i + chunk_size]\\n\\n\\ndef display_memory_info():\\n    mem = psutil.virtual_memory()\\n    total_memory = mem.total\\n    available_memory = mem.available\\n    used_memory = mem.used\\n\\n    print(f\"Total Memory: {total_memory / (1024 ** 3):.2f} GB\")\\n    print(f\"Available Memory: {available_memory / (1024 ** 3):.2f} GB\")\\n    print(f\"Used Memory: {used_memory / (1024 ** 3):.2f} GB\")\\n\\n@dataclass(frozen=True)\\nclass FileLogger:\\n    \"\"\"A logger that writes to files under a given directory.\"\"\"\\n\\n    log_dir: Path\\n\\n    def log(self, filename: str, text: str):\\n        \"\"\"Log text to a file under `self.log_dir`.\"\"\"\\n        if not self.log_dir.exists():\\n ', present_in_blob=True)])\n"]}], "source": ["print(cur_data[\"recency_info\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}