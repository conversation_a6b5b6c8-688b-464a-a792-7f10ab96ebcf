{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "import argparse\n", "import collections\n", "import pathlib\n", "import random\n", "\n", "import numpy as np\n", "import tqdm\n", "from datasets import Dataset as HFDataset\n", "\n", "from base.datasets import completion, tenants\n", "from base.datasets.completion_dataset import CompletionDataset, CompletionDatum\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from base.tokenizers.tokenizer import RagSpecialTokens\n", "from experimental.dxy.rag.rlhf.shared_lib import traverse_date_range\n", "from research.core import utils_for_file\n", "\n", "\n", "def obtain_data_fn(\n", "    date_ranges: list[str],\n", "    input_dir: pathlib.Path,\n", "):\n", "    \"\"\"Process the data as a dataset.\"\"\"\n", "    assert input_dir.exists(), input_dir\n", "    all_date_ranges = []\n", "    for cur_date_range in date_ranges:\n", "        start_date, end_date = cur_date_range.split(\"-\")\n", "        all_date_ranges.extend(traverse_date_range(start_date, end_date))\n", "    print(f\"There are {len(all_date_ranges)} date ranges.\")\n", "    accept_reject_files: list[pathlib.Path] = []\n", "    for date, xstart, xend in all_date_ranges:\n", "        accept_reject_dir = input_dir / f\"{date}-accept-reject\"\n", "        if accept_reject_dir.exists():\n", "            accept_reject_files.extend(list(accept_reject_dir.glob(\"*-samples.jsonl\")))\n", "        print(f\"{date}: {accept_reject_dir}\")\n", "    assert len(accept_reject_files) > 0, \"Can not find accept/reject files.\"\n", "    print(f\"There are {len(accept_reject_files)} accept/reject files\")\n", "    all_data: list[dict] = []\n", "    for accept_reject_file in tqdm.tqdm(\n", "        accept_reject_files, desc=\"Load accept/reject files\"\n", "    ):\n", "        all_data.extend(utils_for_file.read_jsonl(accept_reject_file))\n", "    return all_data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 26 date ranges.\n", "20241015: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241015-accept-reject\n", "20241016: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241016-accept-reject\n", "20241017: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241017-accept-reject\n", "20241018: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241018-accept-reject\n", "20241019: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241019-accept-reject\n", "20241020: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241020-accept-reject\n", "20241021: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241021-accept-reject\n", "20241022: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241022-accept-reject\n", "20241023: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241023-accept-reject\n", "20241024: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241024-accept-reject\n", "20241025: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241025-accept-reject\n", "20241026: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241026-accept-reject\n", "20241027: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241027-accept-reject\n", "20241028: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241028-accept-reject\n", "20241029: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241029-accept-reject\n", "20241030: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241030-accept-reject\n", "20241031: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241031-accept-reject\n", "20241101: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241101-accept-reject\n", "20241102: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241102-accept-reject\n", "20241103: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241103-accept-reject\n", "20241104: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241104-accept-reject\n", "20241105: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241105-accept-reject\n", "20241106: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241106-accept-reject\n", "20241107: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241107-accept-reject\n", "20241108: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241108-accept-reject\n", "20241109: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031/20241109-accept-reject\n", "There are 81 accept/reject files\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Load accept/reject files: 100%|██████████| 81/81 [02:06<00:00,  1.56s/it]\n"]}], "source": ["examples = obtain_data_fn(\n", "    [\"20241015-20241109\"],\n", "    pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_examples = []\n", "for x in examples:\n", "    if x[\"response_model\"] == \"eldenv7-0-15b\" and x[\"accepted\"]:\n", "        filtered_examples.append(x)\n", "\n", "print(f\"There are {len(filtered_examples)} examples.\")\n", "\n", "random.shuffle(filtered_examples)\n", "\n", "for x in filtered_examples[:10]:\n", "    print(x)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}