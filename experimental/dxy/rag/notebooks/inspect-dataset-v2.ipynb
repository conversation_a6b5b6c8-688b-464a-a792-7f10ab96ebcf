{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The dataset_v1 has 33190371 records.\n"]}], "source": ["import pathlib\n", "import megatron.data.indexed_dataset as indexed_dataset\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "\n", "# path = \"/mnt/efs/augment/data/processed/next-edit/gh_pr_train_repartitioned/S1.11.1_6000p_2000f,R1.1_no_retrieval_synth_instruct,P1.10.1_llama3base_context12/train\"\n", "# path = \"/mnt/efs/augment/data/processed/rag/dataset/elden_0619_90k_0619_sc2_pfretsignpfsuf_rdrop030/dataset\"\n", "path = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden-sc2/dataset\"\n", "dataset = indexed_dataset.MMapIndexedDataset(path, skip_warmup=True)\n", "print(f\"The dataset_v1 has {len(dataset)} records.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 7937 tokens\n"]}], "source": ["index = 0\n", "# Find the index of the first pad token.\n", "tokens = dataset[index].tolist()\n", "print(f\"There are {len(tokens)} tokens\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["128032\n"]}], "source": ["import numpy as np\n", "\n", "print(np.abs(tokens).max())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Special tokens have been added in the vocabulary, make sure the associated word embeddings are fine-tuned or trained.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["32040\n"]}], "source": ["from research.core.tokenizers import DeepSeekCoderBaseTokenizer\n", "\n", "tokenizer = DeepSeekCoderBaseTokenizer()\n", "print(tokenizer.vocab_size)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}