{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 2866 files in the repository.\n"]}], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "import random\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    if language is None:\n", "        continue\n", "    # print(str(relative_path))\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    # if \"research/fastbackward/tests/test_checkpointing_utils.py\" not in str(relative_path):\n", "    #     continue\n", "    # if \"research/data/tests/collection/test_stackexchange.py\" not in str(relative_path):\n", "    #     continue\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")\n", "\n", "rng = random.Random(45)\n", "rng.shuffle(repo)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", "    SiblingCorruptionNodesPicker,\n", ")\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "SAMPLE_LANGUAGES = [\"python\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=103,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collect 410 artifacts\n", "There are 177 v1 final samples.\n", "There are 410 v2 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_retrieved_chunks_from_fim,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.exps.utils_fim import (\n", "    get_node_weight_assert_v1,\n", "    get_node_weight_literal_v1,\n", ")\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from research.core.artifacts import collect_artifacts\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "sampler_v1 = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=SiblingCorruptionNodesPicker(\n", "        no_corruption_expansion_rate=0.2,\n", "        random_corrupt_available_siblings_rate=0.3,\n", "        corrupt_all_available_siblings_rate=0.2,\n", "        possibly_corrupt_ancestor_rate=0.3,\n", "        check_any_required_key_word=(\"assert\",),\n", "        check_similar_literal_type=False,\n", "    ),\n", ")\n", "\n", "samples_v1 = list(\n", "    generate_retrieved_chunks_from_fim(\n", "        repo,\n", "        generate_fim_samples_from_repo(\n", "            repo, config_fim, sampler_v1, get_node_weight_assert_v1\n", "        ),\n", "        config=config_retrieval,\n", "        retrieval_database=NullRetrievalDatabase(),\n", "        tokenizer=tokenizer,\n", "    )\n", ")\n", "\n", "sampler_v2 = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=SiblingCorruptionNodesPicker(\n", "        no_corruption_expansion_rate=0.2,\n", "        random_corrupt_available_siblings_rate=0.3,\n", "        corrupt_all_available_siblings_rate=0.2,\n", "        possibly_corrupt_ancestor_rate=0.3,\n", "        check_any_required_key_word=(),\n", "        check_similar_literal_type=True,\n", "    ),\n", ")\n", "with collect_artifacts() as collector:\n", "    samples_v2 = list(\n", "        generate_retrieved_chunks_from_fim(\n", "            repo,\n", "            generate_fim_samples_from_repo(\n", "                repo, config_fim, sampler_v2, get_node_weight_literal_v1\n", "            ),\n", "            config=config_retrieval,\n", "            retrieval_database=NullRetrievalDatabase(),\n", "            tokenizer=tokenizer,\n", "        )\n", "    )\n", "    artifacts = collector.get_artifacts()\n", "    print(f\"Collect {len(artifacts)} artifacts\")\n", "print(f\"There are {len(samples_v1)} v1 final samples.\")\n", "print(f\"There are {len(samples_v2)} v2 final samples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(artifacts[0])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/prompt_format/util_test.py\n", "\n", "\u001b[92m    head_tokens = head_n(tokens, 2)\n", "    assert head_tokens == [3, 5]\n", "\n", "\n", "def test_head_n_equal():\n", "    \"\"\"Test if head_n successfully truncates when n is equal to token len.\"\"\"\n", "    tokens = [3, 5, 2, 4]\n", "    head_tokens = head_n(tokens, 4)\n", "    assert head_tokens == [3, 5, 2, 4]\n", "\n", "\n", "def test_head_n_larger():\n", "    \"\"\"Test if head_n successfully truncates when n is larger than token len.\"\"\"\n", "    tokens = [3, 5, 2, 4]\n", "    head_tokens = head_n(tokens, 6)\n", "    assert head_tokens == [3, 5, 2, 4]\n", "\n", "\n", "def test_prompt_chunks_as_dict():\n", "    \"\"\"Test prompt_chunks_as_dict.\"\"\"\n", "    chunks = [\n", "        PromptChunk(text=\"foo\", \u001b[0m\u001b[47m\u001b[30mpath=\"foo.py\", origin=\"\"\u001b[0m\u001b[94m),\n", "        PromptChunk(text=\"bar\", path=\"bar.py\", origin=\"\"),\n", "        PromptChunk(text=\"xyz\", path=\"xyz.py\", origin=\"dense_retriever\"),\n", "        PromptChunk(text=\"abc\", path=\"abc.py\", origin=\"signature_retriever\"),\n", "    ]\n", "\n", "    chunks_dict = prompt_chunks_as_dict(chunks)\n", "    assert chunks_dict == {\n", "        \"\": [chunks[0], chunks[1]],\n", "        \"dense_retriever\": [chunks[2]],\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples_v2[0], 22, 10)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=280d0973-5ecc-48f1-9c24-5a6530ddaa91, last_request_id=1ad16b93-7464-41cb-aa6e-847f6dee3a50)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='roguesl-v2-16b-seth616-rec', suggested_prefix_char_count=5184, suggested_suffix_char_count=5184, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=b91e8548-66ae-49bd-88b1-f2f29dc6fa8e, last_request_id=183cacda-8d8e-4c9a-8e1e-584ab33628fc)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='star2sl-16b-seth616-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='Rust', vscode_name='rust', extensions=['.rs'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=8660a534-89b7-4587-9248-830fb97dca22, last_request_id=5fe8fe61-91b8-402f-a273-446c8387f161)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='elden-15b-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='Racket', vscode_name='racket', extensions=['.rkt'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2 (Signature)\n"]}], "source": ["import copy\n", "from experimental.dxy.rag.exps.utils_system import (\n", "    build_model_input,\n", "    load_all_systems,\n", "    compare_models,\n", ")\n", "from research.core.types import Document\n", "from termcolor import colored\n", "from research.data.rag.rogue_stages import RetrievalAugmentedSample\n", "from experimental.dxy.rag.exps.utils import shift_middle_to_prefix\n", "\n", "systems = load_all_systems()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- v1[0] correct\n", "- v2[0] wrong -- need the testdata folder structure\n", "- v2[1] wrong\n", "- v2[2] correct\n", "- v2[4] wrong"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[0], 10, 10)\n", "compare_models(systems, samples_v1[0], doc_by_id)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2866 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/eval/harness/metrics_test.py\n", "\n", "\u001b[92m        (\"abc\", [\"abcd\"], 0.0),\n", "        (\"a\\nb\\nc\", [\"a\\nb\\nc\"], 1.0),\n", "        (\"a\\nb\\nc\", [\"a\\nb\\nc\\nd\"], 0.0),\n", "        (\"\", [\"\"], 1.0),\n", "        (\"\", [\"abc\"], 0.0),\n", "        (\"a\\nb\\nc\", [\"a\\nb\\nc\\nd\", \"a\\nb\\nc\"], 1.0),\n", "        (\"a\\nb\\nc\", [\"a\\nb\\nc\\nd\", \"abc\"], 0.0),\n", "        (\"\", [\"\", \"\"], 1.0),\n", "        (\"abc\", [\"abc\", \"abc\"], 1.0),\n", "        \u001b[0m\u001b[47m\u001b[30m(\"a\\nb\\nc\", [\"a\\nb\\nc\", \"a\\nb\\nc\"], 1.0)\u001b[0m\u001b[94m,\n", "    ],\n", ")\n", "def test_compute_exact_match(target, predictions, expected):\n", "    assert compute_exact_match(target, predictions) == expected\n", "\n", "\n", "@pytest.mark.parametrize(\n", "    \"target, predictions, expected\",\n", "    [\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2589 documents in 8.4 seconds, filtered 0 by id, 1 by size, 276 by extension ({'.md': 218, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2866 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: b39053e6-62ec-4a5e-94ea-4bbccdb8bf28\n", "Request ID 1: 84484061-c908-4769-b03d-94797c4e9c94\n", "\u001b[107m\u001b[31m(\"a\\nb\\nc\", [\"a\\nb\\nc\", \"a\\nb\\nc\"], 1.0)\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2589 documents in 8.2 seconds, filtered 0 by id, 1 by size, 276 by extension ({'.md': 218, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2866 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: b0f5f5ee-f3b1-496b-99aa-b62a8acdd9f0\n", "Request ID 1: f5ecccde-cb6e-444a-8089-82917d5e78f1\n", "\u001b[107m\u001b[31m(\"a\\nb\\nc\", [\"a\\nb\\nc\", \"a\\nb\\nc\"], 1.0)\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2589 documents in 8.7 seconds, filtered 0 by id, 1 by size, 276 by extension ({'.md': 218, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 65249550-c858-4e66-b1a6-f6ab9a7a38b8\n", "Request ID 1: 39f1e966-d32d-47bf-a4c8-829dcfc812f6\n", "\u001b[107m\u001b[31m(\"a\\nb\\nc\", [\"a\\nb\\nc\", \"a\\nb\\nc\"], 1.0)\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[2], 10, 10)\n", "compare_models(systems, samples_v2[2], doc_by_id)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/request_insight/request_insight_proto_test.py\n", "\n", "\u001b[92m    response2 = request_insight_pb2.RetrievalResponse(\n", "        retrieval_type=request_insight_pb2.RetrievalType.DENSE,\n", "        embeddings_prompt=[\n", "            request_insight_pb2.Token(\n", "                token_id=2,\n", "                text=\"bar\",\n", "                log_probs=0.0,\n", "            )\n", "        ],\n", "        \u001b[0m\u001b[47m\u001b[30mretrieved_chunks=[\n", "            request_insight_pb2.RetrievalChunk(\n", "                blob_name=\"bar.py\",\n", "                chunk_index=0,\n", "                char_offset=0,\n", "            ),\n", "        ]\u001b[0m\u001b[94m,\n", "    )\n", "    response3 = request_insight_pb2.RetrievalResponse(\n", "        retrieval_type=request_insight_pb2.RetrievalType.SIGNATURE,\n", "        embeddings_prompt=[\n", "            request_insight_pb2.Token(\n", "                token_id=3,\n", "                text=\"baz\",\n", "                log_probs=0.0,\n", "            )\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples_v2[13], 10, 10)\n", "# compare_models(systems, samples_v2[1], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[2], 10, 10)\n", "compare_models(systems, samples_v2[2], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[3], 10, 10)\n", "compare_models(systems, samples_v2[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[4], 10, 10)\n", "compare_models(systems, samples_v2[4], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[3], 10, 10)\n", "compare_models(systems, samples_v1[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[4], 10, 10)\n", "compare_models(systems, samples_v1[4], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[5], 10, 10)\n", "compare_models(systems, samples_v1[5], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[5], 10, 10)\n", "compare_models(systems, samples_v2[5], doc_by_id)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["667\n"]}], "source": ["x = \"\"\"test(\"set state\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "                background: new ThemeColor(\"statusBarItem.errorBackground\"),\n", "                foreground: new ThemeColor(\"statusBarItem.errorForeground\"),\n", "            },\n", "        };\n", "\n", "        const manager = new StatusBarManager();\n", "        manager.setState(state);\n", "        assertState(mockStatusBar, state);\n", "    });\"\"\"\n", "print(len(x))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}