{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 2852 files in the repository.\n"]}], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "import random\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    # if str(relative_path) == \"tools/bazel_runner/review_edit_bot/test_data/test_file.txt\":\n", "    #     import pdb\n", "\n", "    #     pdb.set_trace()\n", "    if language is None:\n", "        continue\n", "    # print(str(relative_path))\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")\n", "\n", "rng = random.<PERSON>(42)\n", "rng.shuffle(repo)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", "    SiblingCorruptionNodesPicker,\n", ")\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "SAMPLE_LANGUAGES = [\"python\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=200,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=94,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 166 v1 final samples.\n", "There are 30 v2 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_retrieved_chunks_from_fim,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.exps.utils_fim import (\n", "    get_node_weight_assert_v1,\n", "    get_node_weight_literal_v1,\n", ")\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "sampler_v1 = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=SiblingCorruptionNodesPicker(\n", "        no_corruption_expansion_rate=0.2,\n", "        random_corrupt_available_siblings_rate=0.3,\n", "        corrupt_all_available_siblings_rate=0.2,\n", "        possibly_corrupt_ancestor_rate=0.3,\n", "        check_any_required_key_word=(\"assert\",),\n", "        check_same_node_type=False,\n", "    ),\n", ")\n", "\n", "samples_v1 = list(\n", "    generate_retrieved_chunks_from_fim(\n", "        repo,\n", "        generate_fim_samples_from_repo(\n", "            repo, config_fim, sampler_v1, get_node_weight_assert_v1\n", "        ),\n", "        config=config_retrieval,\n", "        retrieval_database=NullRetrievalDatabase(),\n", "        tokenizer=tokenizer,\n", "    )\n", ")\n", "\n", "sampler_v2 = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=SiblingCorruptionNodesPicker(\n", "        no_corruption_expansion_rate=0.2,\n", "        random_corrupt_available_siblings_rate=0.3,\n", "        corrupt_all_available_siblings_rate=0.2,\n", "        possibly_corrupt_ancestor_rate=0.3,\n", "        check_any_required_key_word=(),\n", "        check_same_node_type=True,\n", "    ),\n", ")\n", "\n", "samples_v2 = list(\n", "    generate_retrieved_chunks_from_fim(\n", "        repo,\n", "        generate_fim_samples_from_repo(\n", "            repo, config_fim, sampler_v2, get_node_weight_literal_v1\n", "        ),\n", "        config=config_retrieval,\n", "        retrieval_database=NullRetrievalDatabase(),\n", "        tokenizer=tokenizer,\n", "    )\n", ")\n", "print(f\"There are {len(samples_v1)} v1 final samples.\")\n", "print(f\"There are {len(samples_v2)} v2 final samples.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=839dedca-c9cb-4583-b608-219f674c7fc7, last_request_id=62f483d0-dc70-44b3-8059-8b65409bc82c)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='roguesl-v2-16b-seth616-rec', suggested_prefix_char_count=5184, suggested_suffix_char_count=5184, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=3e63cb0e-213f-414a-b433-ba454c70b6a3, last_request_id=7cf1a766-3702-4361-bac7-d5e3d7c190e4)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='star2sl-16b-seth616-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Go', vscode_name='go', extensions=['.go']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Remote client: AugmentClient(url=https://dogfood.api.augmentcode.com, request_session_id=0ac8cb02-bbf1-40db-87f4-cf4b43d7af0f, last_request_id=38183ee8-ba7f-4af1-879d-6792a728a882)\n", "INFO:research.eval.harness.systems.remote_completion_system:Using remote model: Model(name='elden-15b-rec', suggested_prefix_char_count=6912, suggested_suffix_char_count=6912, max_memorize_size_bytes=131072)\n", "INFO:research.eval.harness.systems.remote_completion_system:Supported languages: [Language(name='Kotlin', vscode_name='kotlin', extensions=['.kt', '.kts']), Language(name='C++', vscode_name='cpp', extensions=['.cpp', '.c++', '.cc', '.cp', '.cxx', '.h', '.h++', '.hh', '.hpp', '.hxx', '.inc', '.inl', '.ipp', '.tcc', '.tpp']), Language(name='Svelte', vscode_name='svelte', extensions=['.svelte']), Language(name='Java', vscode_name='java', extensions=['.java']), Language(name='Python', vscode_name='python', extensions=['.py', '.bzl', '.cgi', '.fcgi', '.gyp', '.lmi', '.pyde', '.pyp', '.pyt', '.pyw', '.rpy', '.tac', '.wsgi', '.xpy', '.ipynb']), Language(name='perl', vscode_name='perl', extensions=['.pl']), Language(name='Rust', vscode_name='rust', extensions=['.rs']), Language(name='R', vscode_name='r', extensions=['.r', '.rds', '.rdata', '.rda']), Language(name='Racket', vscode_name='racket', extensions=['.rkt']), Language(name='TypeScript JSX', vscode_name='typescriptreact', extensions=['.tsx']), Language(name='HTML', vscode_name='html', extensions=['.html', '.htm', '.xhtml', '.svelte', '.astro']), Language(name='JavaScript', vscode_name='javascript', extensions=['.js', '._js', '.bones', '.es', '.es6', '.frag', '.gs', '.jake', '.jsb', '.jscad', '.jsfl', '.jsm', '.jss', '.njs', '.pac', '.sjs', '.ssjs', '.sublime-build', '.sublime-commands', '.sublime-completions', '.sublime-keymap', '.sublime-macro', '.sublime-menu', '.sublime-mousemap', '.sublime-project', '.sublime-settings', '.sublime-theme', '.sublime-workspace', '.sublime_metrics', '.sublime_session', '.xsjs', '.xsjslib']), Language(name='Shell', vscode_name='shellscript', extensions=['.sh']), Language(name='JavaScript JSX', vscode_name='javascriptreact', extensions=['.jsx']), Language(name='Scala', vscode_name='scala', extensions=['.scala', '.sc']), Language(name='Swift', vscode_name='swift', extensions=['.swift', '.SWIFT']), Language(name='C#', vscode_name='csharp', extensions=['.cs']), Language(name='C', vscode_name='c', extensions=['.c', '.cats', '.h', '.idc', '.w']), Language(name='Astro', vscode_name='astro', extensions=['.astro']), Language(name='Ruby', vscode_name='ruby', extensions=['.rb']), Language(name='PHP', vscode_name='php', extensions=['.php', '.php3', '.php4', '.php5', '.phps']), Language(name='Lua', vscode_name='lua', extensions=['.lua']), Language(name='TypeScript', vscode_name='typescript', extensions=['.ts', '.tsx']), Language(name='Go', vscode_name='go', extensions=['.go'])]\n", "INFO:research.eval.harness.systems.remote_completion_system:Feature flags: {'enable_chat': True, 'enable_code_edits': True, 'checkpoint_blobs_v2': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish loading the system StarCoder-2 (Signature)\n"]}], "source": ["import copy\n", "from experimental.dxy.rag.exps.utils_system import (\n", "    build_model_input,\n", "    load_all_systems,\n", "    compare_models,\n", ")\n", "from research.core.types import Document\n", "from termcolor import colored\n", "from research.data.rag.rogue_stages import RetrievalAugmentedSample\n", "from experimental.dxy.rag.exps.utils import shift_middle_to_prefix\n", "\n", "systems = load_all_systems()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- v1[0] correct\n", "- v2[0] wrong -- need the testdata folder structure\n", "- v2[1] wrong\n", "- v2[2] correct\n", "- v2[4] wrong"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: services/edit_host/server/edit_request_insight_builder_test.py\n", "\n", "\u001b[92m        request=edit_request,\n", "        request_context=request_context,\n", "        auth_info=None,\n", "    )\n", "\n", "    assert len(ri_publisher.publish_request_insight.call_args.args) == 1\n", "    request = ri_publisher.publish_request_insight.call_args[0][0]\n", "    print(2, request)\n", "    assert request.request_id == request_context.request_id\n", "    \u001b[0m\u001b[47m\u001b[30massert len(request.events) == 1\n", "    assert request.events[0].edit_host_request == request_insight_pb2.RIEditRequest(\n", "        request=edit_request,\n", "        tokenization=request_insight_pb2.Tokenization(\n", "            token_ids=[4563, 445, 6388, 439],\n", "            text=\"void quicksort\",\n", "            offsets=[0, 4, 7, 11],\n", "            log_probs=[],\n", "        ),\n", "    )\u001b[0m\u001b[94m\n", "\n", "\n", "def test_record_response():\n", "    \"\"\"Tests that record_response correctly submit events.\"\"\"\n", "    ri_publisher = MagicMock()\n", "    ri_publisher.update_request_info_request = MagicMock(\n", "        side_effect=_test_update_request\n", "    )\n", "    ri_builder = EditRequestInsightBuilder(ri_publisher)\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: f5a0d2fb-a5e1-44b1-9bce-147883408d91\n", "Request ID 1: 05239ad6-44ae-4224-acf3-3edc165d1117\n", "Request ID 2: 5ce6e05e-2078-4dd7-9ad5-ceb98640a522\n", "Request ID 3: c769ec08-d39f-44f5-a45c-40700f76456b\n", "\u001b[107m\u001b[31massert len(request.events) == 1\n", "    assert request.events[0].edit_host_request == request_insight_pb2.RIEditRequest(\n", "        request=edit_request,\n", "        tokenization=request_insight_pb2.Tokenization(\n", "            token_ids=[4563, 445, 6388, 439],\n", "            text=\"void quicksort\",\n", "            offsets=[0, 4, 7, 11],\n", "            log_probs=[],\n", "        ),\n", "    )\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 33f2f5c3-07eb-4bbd-b6d8-fe11cebb232d\n", "Request ID 1: 24ee17d8-4f44-4926-bc1f-a0ee73724810\n", "\u001b[107m\u001b[31massert len(request.events) == 1\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 3405dd89-f09f-4b7c-8f38-067cce218d4b\n", "Request ID 1: 40a98325-41ec-42dd-a481-b2e1f4138938\n", "Request ID 2: 56377eb2-b653-4845-845b-873f65fdcbe9\n", "Request ID 3: abb4cc95-3dee-4ad6-b98a-228301a2ccbb\n", "Request ID 4: 6e72cf52-357b-40b9-93d9-ca26d089c219\n", "Request ID 5: 82ed21af-de62-458c-85e5-a3cd9a7bae15\n", "\u001b[107m\u001b[31massert len(request.events) == 1\n", "    assert request.events[0].edit_host_request == request_insight_pb2.RIEditRequest(\n", "        request=edit_request,\n", "        tokenization=request_insight_pb2.Tokenization(\n", "            token_ids=[4563, 445, 6388, 439],\n", "            text=\"void quicksort\",\n", "            offsets=[0, 4, 7, 11],\n", "            log_probs=[],\n", "        ),\n", "    )\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v1[0], 10, 10)\n", "compare_models(systems, samples_v1[0], doc_by_id)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/core/tests/prod_adapters/test_tokenization.py\n", "\n", "\u001b[92m        create_prod_tokenizer(tokenizer_name_prod),\n", "        tokenizer_name_prod,\n", "    )\n", "\n", "    tokens_research = tokenizer_research.tokenize(expression)\n", "    tokens_prod = tokenizer_prod.tokenize(expression)\n", "\n", "    assert tokens_research == tokens_prod\n", "\n", "\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\n", "    \"filename\",\n", "    [\n", "        # Test file containing a mix of code, English and Asian characters.\n", "        \"textConversion.js\",\n", "    ],\n", ")\u001b[0m\u001b[94m\n", "@pytest.mark.parametrize(\n", "    \"tokenizer_name_research,tokenizer_name_prod\", RESEARCH_PROD_NAME_PAIRS\n", ")\n", "def test_tokenizer_equivalence_from_file(\n", "    tokenizer_name_research: str, tokenizer_name_prod: str, filename: str\n", "):\n", "    \"\"\"Test that research and production tokenizers produce equivalent tokens.\"\"\"\n", "    tokenizer_research = create_research_tokenizer(tokenizer_name_research)\n", "    tokenizer_prod = ResearchTokenizerWrapper(\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 9262cf4d-bd8b-4834-a13f-d2f4a35a8fb7\n", "Request ID 1: 6fc23faf-8e7e-4661-a720-289ed6ffd970\n", "\u001b[107m\u001b[<EMAIL>(\"filename\", [\"deepseek_coder_base.py\", \"deepseek_coder_instruct.py\"])\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.5 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 667343e8-456a-47e0-9ae1-e0c78ecdff25\n", "Request ID 1: 6b9ba87f-218d-4bd2-84fd-decd00d9d462\n", "\u001b[107m\u001b[<EMAIL>(\n", "    \"filename\",\n", "    [\n", "        \"test_tokenizer_equivalence_from_file_0.py\",\n", "        \"test_tokenizer_equivalence_from_file_1.py\",\n", "    ],\n", ")\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['c2deae36b43d4762256edf810e31dbd1cacf583afc3de414d050e33f8db1bb0b']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 30.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['c2deae36b43d4762256edf810e31dbd1cacf583afc3de414d050e33f8db1bb0b']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['c2deae36b43d4762256edf810e31dbd1cacf583afc3de414d050e33f8db1bb0b']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['c2deae36b43d4762256edf810e31dbd1cacf583afc3de414d050e33f8db1bb0b']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 860f68f1-48d1-41f0-b7b6-b8d06c74d09f\n", "Request ID 1: fbfbd7e8-6e06-4125-9ddc-0cbaa0cea857\n", "Request ID 2: 694306c6-eacc-47c6-a145-947cbd860a89\n", "\u001b[107m\u001b[<EMAIL>(\n", "    \"filename\",\n", "    [\n", "        \"simple_python.py\",\n", "        \"simple_python_with_comments.py\",\n", "        \"simple_python_with_comments_and_whitespace.py\",\n", "    ],\n", ")\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[0], 10, 10)\n", "compare_models(systems, samples_v2[0], doc_by_id)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: services/completion_host/single_model_server/single_round_handler_test.py\n", "\n", "\u001b[92m    )\n", "    codegen_kit.content_manager_client.find_missing.return_value = missing_blobs\n", "\n", "    to_check = [\"valid-0\", \"valid-1\"] + missing_blobs + nonindexed_blobs\n", "    result = codegen_kit.find_missing(blob_names=to_check)\n", "\n", "    assert set(result.missing_blob_names) == set(missing_blobs)\n", "    assert set(result.nonindexed_blob_names) == set(nonindexed_blobs)\n", "\n", "\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\n", "    \"use_filter, output, log_probs, expected\",\n", "    [\n", "        (False, [1, 2, 3], [0.1, 0.2, 0.3], \"<fim_prefix><fim_middle><fim_suffix>\"),\n", "        (True, [1, 2, 3], [0.1, 0.2, 0.3], \"processed completion\"),\n", "    ],\n", ")\u001b[0m\u001b[94m\n", "@patch(\"services.completion_host.single_model_server.post_processing.LowQualityFilter\")\n", "def test_handler_use_low_quality_filter(\n", "    mock_filter: <PERSON><PERSON>,\n", "    use_filter: bool,\n", "    output: list[int],\n", "    log_probs: list[float],\n", "    expected: str,\n", "):\n", "    mock_filter.return_value = mock_instance = MagicMock(return_value=(True, 0.95))\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.9 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: ecec61d2-ffd0-4b37-aeee-1ac5295a65e1\n", "Request ID 1: 327ffe1d-740a-44d0-b6ff-e104a4a4cda7\n", "Request ID 2: 9ff8c20e-ecaf-49a6-ae37-96f0c4245042\n", "\u001b[107m\u001b[<EMAIL>(\n", "    argnames=[\"use_filter\", \"output\", \"log_probs\", \"expected\"],\n", "    argvalues=[\n", "        (True, [1, 2, 3], [0.1, 0.2, 0.3], \"processed completion\"),\n", "        (<PERSON>alse, [1, 2, 3], [0.1, 0.2, 0.3], \"1 2 3\"),\n", "    ],\n", ")\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 5c499859-b3da-4590-9aae-9747178f4963\n", "Request ID 1: 8816205f-3cc2-457b-b1c5-b46ac310a2a3\n", "Request ID 2: 2d1efe14-05fd-469f-adaa-f53e43d2fc7e\n", "Request ID 3: 771fbd62-2a2a-48d7-834c-d1e164fd157d\n", "Request ID 4: 7b669dc4-977e-4b14-bf84-4a6ef1dce1d0\n", "\u001b[107m\u001b[<EMAIL>(\n", "    argnames=[\"use_filter\", \"output\", \"log_probs\", \"expected\"],\n", "    argvalues=[\n", "        (True, [1, 2, 3, 4, 5], [0, 0, 0, 0, 1], \"processed completion\"),\n", "        (False, [1, 2, 3, 4, 5], [0, 0, 0, 0, 1], \"assert a['clue'] ==\"),\n", "    ],\n", ")\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 30.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['e8381b54ddb4456095bfab683fce08c39b9f5f13d4ed61aeaadc940c4b792619']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 41920782-ff60-4372-abbd-86aa9694a793\n", "Request ID 1: b9336705-2dd2-4417-a24e-f291acbf1406\n", "Request ID 2: 37168be2-93bc-4d89-a0bf-126506fdebf5\n", "Request ID 3: e0dc7fc3-ca7e-4e1e-814b-fce9443463e7\n", "Request ID 4: e55475c5-a15c-4449-8c8c-e2117c8790f7\n", "Request ID 5: 6d411d92-9223-40c1-8154-11dd4324d9c9\n", "Request ID 6: 1f9628c3-f7ab-4e84-b6d8-92aa2604ab00\n", "\u001b[107m\u001b[<EMAIL>(\n", "    argnames=[\"use_filter\", \"output\", \"log_probs\", \"expected\"],\n", "    argvalues=[\n", "        (True, [1, 2, 3, 4, 5], [0, 0, 0, 0, 1], \"processed completion\"),\n", "        (True, [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], \"\"),\n", "        (False, [1, 2, 3, 4, 5], [0, 0, 0, 0, 1], \"processed completion\"),\n", "        (False, [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], \"\"),\n", "    ],\n", ")\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[1], 10, 10)\n", "compare_models(systems, samples_v2[1], doc_by_id)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/fastbackward/tests/test_rotary.py\n", "\n", "\u001b[92m) -> tuple[torch.Tensor, torch.Tensor]:\n", "    x_ = torch.view_as_complex(x.float().reshape(*x.shape[:-1], -1, 2))\n", "    freqs_cis = reshape_for_broadcast(freqs_cis, x_)\n", "    xq_out = torch.view_as_real(x_ * freqs_cis).flatten(3)\n", "    return xq_out.type_as(x)\n", "\n", "\n", "@pytest.mark.parametrize(\"batch_size\", [1, 4])\n", "@pytest.mark.parametrize(\"nheads\", [1, 16])\n", "@pytest.mark.parametrize(\"headdim\", [32, 128])\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\"scaling_factor\", [1.0, 4.0])\u001b[0m\u001b[94m\n", "@pytest.mark.parametrize(\"start_pos\", [0, 100])\n", "@pytest.mark.parametrize(\"seqlen\", [128, 4096])\n", "# NOTE: the github-CI GPUs aren't new enough to work with bfloat and torch.compile\n", "@pytest.mark.parametrize(\n", "    \"dtype\",\n", "    [\n", "        pytest.param(torch.bfloat16, marks=pytest.mark.skip_in_ci),\n", "        pytest.param(torch.float32),\n", "    ],\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.9 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: d59718fa-7da5-4b80-9fc3-9dcfc0e4e52e\n", "Request ID 1: cf131658-cf75-45ce-9efc-f674522bba64\n", "\u001b[107m\u001b[<EMAIL>(\"scaling_factor\", [1.0, 4.0])\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 6cd25da0-164a-49e7-bc71-9ad8c73ef64f\n", "Request ID 1: 0ff48a56-9c82-45b3-84cb-2b57912d11ad\n", "\u001b[107m\u001b[<EMAIL>(\"scaling_factor\", [1.0, 4.0])\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 9.1 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 358a1d37-eb1d-4534-88b1-3f910f57455b\n", "Request ID 1: 9ce761a0-c81f-4474-8a72-9cfef3f10e66\n", "\u001b[107m\u001b[<EMAIL>(\"scaling_factor\", [1.0, 4.0])\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[2], 10, 10)\n", "compare_models(systems, samples_v2[2], doc_by_id)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/data/tests/collection/test_tokenize_with_mask.py\n", "\n", "\u001b[92m    assert trim_padding(original + [eos, padding], special_tokens) == original + [eos]\n", "    assert trim_padding(\n", "        original + [eos, padding, padding], special_tokens\n", "    ) == original + [eos]\n", "\n", "\n", "OUTPUT_COL = \"packed_samples\"\n", "\n", "\n", "# Each doc is between 10 and 20 tokens.\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\n", "    \"tokenizer_name\",\n", "    [\n", "        \"deepseek_coder_base\",\n", "        \"starcoder\",\n", "    ],\n", ")\u001b[0m\u001b[94m\n", "@pytest.mark.parametrize(\n", "    \"sequence_length, add_one_token, max_tail_pad, expected_samples\",\n", "    [\n", "        # Very short sequences should still assemble back to the right strings.\n", "        (1, <PERSON><PERSON><PERSON>, 0, None),\n", "        # Seq length 10+1 because that's right below the starcoder actual token length\n", "        # So each doc needs to be split.\n", "        # Scenario 1, no dense packing (max_tail_pad = full length) each doc should be split\n", "        # into 2 samples and padded to the end.  They should not be packed together to minimize\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.9 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 49511d79-c3f6-455d-bcff-7915609d3e09\n", "\u001b[107m\u001b[31m\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.7 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 94366de1-fca6-47d8-8389-45b02572a8f4\n", "\u001b[107m\u001b[31m\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['3ec4f989e09ac84c340eb407321d5c91a80bff1bfc0f8e79a577dd89f730e74d']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 30.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['3ec4f989e09ac84c340eb407321d5c91a80bff1bfc0f8e79a577dd89f730e74d']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['3ec4f989e09ac84c340eb407321d5c91a80bff1bfc0f8e79a577dd89f730e74d']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: fce6e758-47a7-4ddd-b532-18f223af08c5\n", "Request ID 1: d9d6bf77-8d97-4fe8-a815-adf4dcba5b65\n", "\u001b[107m\u001b[<EMAIL>(\n", "    \"tokenizer_name\",\n", "    [\n", "        \"deepseek_coder_base\",\n", "        \"starcoder\",\n", "    ],\n", ")\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[3], 10, 10)\n", "compare_models(systems, samples_v2[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/data/tests/spark/pipelines/stages/test_fim.py\n", "\n", "\u001b[92m        \"content\": code,\n", "        \"max_stars_repo_path\": \"test.py\",\n", "        \"max_stars_repo_name\": \"test\",\n", "    }\n", "\n", "\n", "class TestSampleFimAndTokenize:\n", "    \"\"\"Tests for sample_fim_and_tokenize.\"\"\"\n", "\n", "    \u001b[0m\u001b[47m\u001b[<EMAIL>.skip_in_ci(reason=\"works locally but not in CI\")\n", "\u001b[0m\u001b[94m    @pytest.mark.parametrize(\n", "        \"input_code, expected_output\",\n", "        [\n", "            (\n", "                _SMALL_FILE,\n", "                # pylint:disable-next=line-too-long\n", "                \"<fim_prefix>\\nimport foo<fim_suffix><fim_middle>\\n\\ndef bar():\\n    return foo\\n<|endoftext|>\",\n", "            ),\n", "        ],\n", "    )\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.9 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: b5efda72-eb21-4432-a78c-2d64e31325c5\n", "Request ID 1: 097033d0-ae37-4b80-bf87-31f24e86ddb6\n", "\u001b[107m\u001b[<EMAIL>.skip_in_ci(reason=\"works locally but not in CI\")\n", "\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.7 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 1fa25a28-fb6a-4dbb-b92f-5ecb594037c7\n", "Request ID 1: c2e3921c-9252-4aae-bef8-b66bc41607c7\n", "\u001b[107m\u001b[<EMAIL>\n", "    def spark_session(self) -> SparkSession:\n", "        return SparkSession.builder.master(\"local[*]\").appName(\"test\").getOrCreate()\n", "\n", "\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['5b07b4a3b9dae8d2e747a0fe28ee8e09e29da8a93b8c84a7dc70f57463136c4e']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 30.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['5b07b4a3b9dae8d2e747a0fe28ee8e09e29da8a93b8c84a7dc70f57463136c4e']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['5b07b4a3b9dae8d2e747a0fe28ee8e09e29da8a93b8c84a7dc70f57463136c4e']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: ed663c65-3065-4046-a69f-2e657b5fed58\n", "Request ID 1: 233be2c7-88e8-4d7d-844c-578309bdaba9\n", "\u001b[107m\u001b[<EMAIL>(scope=\"class\")\n", "    def spark_session(self):\n", "        return SparkSession.builder.master(\"local[*]\").appName(\"test\").getOrCreate()\n", "\n", "\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[4], 10, 10)\n", "compare_models(systems, samples_v2[4], doc_by_id)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: services/lib/request_context/request_context_test.py\n", "\n", "\u001b[92m\n", "    context = MagicMock()\n", "    context.invocation_metadata.return_value = [\n", "        Metadata(\"x-request-id\", \"fb9dda31-7bf6-4300-a28b-ebdfc916c351\"),\n", "        Metadata(\"x-request-session-id\", \"3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a\"),\n", "    ]\n", "\n", "    rc = RequestContext.from_grpc_context(context)\n", "    assert rc.request_id == \"fb9dda31-7bf6-4300-a28b-ebdfc916c351\"\n", "    \u001b[0m\u001b[47m\u001b[30massert rc.request_session_id == \"3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a\"\n", "    assert rc.auth_token is None\u001b[0m\u001b[94m\n", "\n", "\n", "def test_to_metadata():\n", "    \"\"\"Tests that RequestContext.to_metadata() returns the correct metadata\"\"\"\n", "    rc = RequestContext(\n", "        \"fb9dda31-7bf6-4300-a28b-ebdfc916c351\",\n", "        \"3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a\",\n", "        \"client\",\n", "        pydantic.SecretStr(\"token\"),\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 9.2 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 1851c4aa-b925-49d8-bc7f-2f3e1e1b6e53\n", "Request ID 1: 4a9559cf-08e9-4c8d-b6a2-c7ec87b47f42\n", "\u001b[107m\u001b[31massert rc.request_session_id == \"3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a\"\n", "    assert rc.auth_token is None\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.2 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 8faa81de-4d31-4dac-a649-866fee916251\n", "Request ID 1: 8ac3e289-74c1-4edc-a309-2941cc6e2e5c\n", "\u001b[107m\u001b[31massert rc.request_session_id == \"3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a\"\n", "    assert rc.auth_token is None\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 882b197a-8b44-4a74-ac6e-3ee255927d6b\n", "Request ID 1: 273d7d4a-4d83-4b4b-96fb-c18757a9b97e\n", "\u001b[107m\u001b[31massert rc.request_session_id == \"3c79ad44-ee61-4db3-8e3a-cd8c4bcf022a\"\n", "    assert rc.auth_token is None\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v1[3], 10, 10)\n", "compare_models(systems, samples_v1[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/review_edit_bot/git_client_test.py\n", "\n", "\u001b[92m            blob_id=\"8f20528c69f382c4a758dbe7c3c3884d99d420ce\",\n", "        ),\n", "    ]\n", "\n", "\n", "def test_get_file_contents(test_repo):\n", "    state = GitClientStateImpl(test_repo)\n", "    file_list = list(state.get_file_list())\n", "    assert len(file_list) == 2\n", "    \u001b[0m\u001b[47m\u001b[30massert state.get_file_contents(file_list[0].path) == b\"Hello, world!\"\u001b[0m\u001b[94m\n", "    assert state.get_file_contents(file_list[1].path) == b\"Another file!\"\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.5 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: b93193ea-80a1-4a63-b00e-6a148f262e5f\n", "Request ID 1: 40e3853d-6aa2-4a74-8382-40d70643aae3\n", "\u001b[107m\u001b[31massert state.get_file_contents(file_list[0].path) == b\"Hello, world!\"\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.7 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 8bf678b2-a993-416c-9041-c901e2932c31\n", "Request ID 1: 4c5d1bea-bf6b-4a40-957e-b0fdcb1400c8\n", "\u001b[107m\u001b[31massert state.get_file_contents(file_list[0].path) == b\"Hello, world!\"\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.8 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 9041b986-1221-4f8a-9ae4-364abb647650\n", "Request ID 1: 1e93a5fa-c85c-4ad1-a46b-3e2cc2941c42\n", "\u001b[107m\u001b[31massert state.get_file_contents(file_list[0].path) == b\"Hello, world!\"\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v1[4], 10, 10)\n", "compare_models(systems, samples_v1[4], doc_by_id)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: research/data/spark/infra/tests/test_spark_base_config.py\n", "\n", "\u001b[92m        assert base_config.gpu_count == 0\n", "        assert base_config.final_gpu_type == []\n", "\n", "        assert base_config.compute_region == \"las1\"\n", "        assert base_config.s3_region == \"las1\"\n", "\n", "        assert base_config.pip_install is None\n", "        assert base_config.dynamic_deps is None\n", "        assert base_config.py_files is None\n", "        \u001b[0m\u001b[47m\u001b[30massert base_config.files is None\n", "\n", "        assert base_config.mnt_path == \"/mnt/efs/augment/\"\n", "        assert base_config.image is None\n", "        assert base_config.env_vars is None\n", "\n", "    def test_invalid_executors(self):\n", "        with pytest.raises(\n", "            ValueError,\n", "            match=\"Min executors cannot be greater than max executors\",\n", "        ):\n", "            SparkBaseConfig(min_executors=3, max_executors=2)\u001b[0m\u001b[94m\n", "\n", "    def test_gpu(self):\n", "        base_config = SparkBaseConfig(gpu_type=\"RTX_A5000\", gpu_count=2)\n", "\n", "        assert base_config.gpu_type == \"RTX_A5000\"\n", "        assert base_config.gpu_count == 2\n", "        assert base_config.final_gpu_type == [\"RTX_A5000\"]\n", "\n", "    def test_no_gpu_type(self):\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.6 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: bb67ab4f-8790-4f4c-bb2f-fc6a883ad7c8\n", "Request ID 1: 5cae7d30-2f4f-487a-96bf-1a3cf34cc2ad\n", "\u001b[107m\u001b[31massert base_config.files is None\n", "        assert base_config.mnt_path == DEFAULT_MNT_PATH\n", "        assert base_config.image is None\n", "        assert base_config.env_vars is None\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.7 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 9835231a-7c88-4153-a859-e67686fb762a\n", "Request ID 1: 170fca55-063a-43d2-b6d9-3d07966cdd32\n", "\u001b[107m\u001b[31massert base_config.files is None\n", "\n", "        assert base_config.mnt_path == \"/mnt/efs\"\n", "        assert base_config.image is None\n", "        assert base_config.env_vars is None\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:research.eval.harness.systems.remote_lib:Nonindexed blob names: ['747a6db08bf485f9cc4429f4917e7d4826994d7ee9c0bde5295d488a8e58878e']\n", "INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 30.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (1)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['747a6db08bf485f9cc4429f4917e7d4826994d7ee9c0bde5295d488a8e58878e']\n", "WARNING:research.eval.harness.systems.remote_lib:Unknown memory names: ['747a6db08bf485f9cc4429f4917e7d4826994d7ee9c0bde5295d488a8e58878e']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 7679fc76-b7d2-4bf1-9428-32018e7881d5\n", "Request ID 1: 48d281fa-0dc8-40c4-a953-c923124be718\n", "\u001b[107m\u001b[31massert base_config.files is None\n", "\n", "        assert base_config.mnt_path == \"/mnt/spark\"\n", "        assert base_config.image is None\n", "        assert base_config.env_vars is None\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v1[5], 10, 10)\n", "compare_models(systems, samples_v1[5], doc_by_id)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["File Path: services/request_insight/blob_exporter/blob_exporter_test.py\n", "\n", "\u001b[92m\n", "    assert list(blob_exporter._cache.keys()) == [\n", "        ExportObjectName(name=\"blob-a\".encode().hex(), tenant_id=\"tenant1\")\n", "    ]\n", "    assert len(checkpoint_exporter._cache) == 0\n", "    assert persistence.exists.call_count == 2\n", "    assert content_manager_client.batch_download_all.call_count == 1\n", "    assert persistence.write.call_count == 1\n", "\n", "\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\"event_type\", EVENT_TYPES)\u001b[0m\u001b[94m\n", "@pytest.mark.parametrize(\"use_blobs_added\", [True, False])\n", "def test_process_write_error(\n", "    content_manager_client,\n", "    token_exchange_client,\n", "    persistence,\n", "    event_type,\n", "    use_blobs_added,\n", "):\n", "    \"\"\"Test the blob exporter when there is an error in the download.\"\"\"\n", "\u001b[0m--------------------------------\n", "System: StarCoder-1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 68.4 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: e4d07506-e3e0-492f-837c-e8f258f5ce92\n", "Request ID 1: 00e902f4-66c6-436d-bf51-4beb9af9e154\n", "\u001b[107m\u001b[<EMAIL>(\"event_type\", EVENT_TYPES)\u001b[0m\n", "System: StarCoder-2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 9.1 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n", "INFO:research.eval.harness.systems.remote_completion_system:Adding 2852 documents.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: dd55a7cd-b910-4893-928c-ddae497d7fb8\n", "Request ID 1: 6a6dd6bb-4bc4-4d93-846d-784f430e06fd\n", "\u001b[107m\u001b[<EMAIL>(\"event_type\", EVENT_TYPES)\u001b[0m\n", "System: StarCoder-2 (Signature)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:research.eval.harness.systems.remote_completion_system:Indexed 2577 documents in 8.3 seconds, filtered 0 by id, 1 by size, 274 by extension ({'.md': 216, '.css': 40, '.sql': 18}), not indexed due to timeout (0)\n", "WARNING:research.eval.harness.systems.remote_completion_system:No doc_ids provided, using all blob_names.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Request ID 0: 49d105f0-9696-4b2c-acb0-6e861df92a2b\n", "Request ID 1: f1e62257-45d6-4379-a50b-904dbe6c82a9\n", "\u001b[107m\u001b[<EMAIL>(\"event_type\", EVENT_TYPES)\u001b[0m\n"]}], "source": ["show_truncated_completion(samples_v2[5], 10, 10)\n", "compare_models(systems, samples_v2[5], doc_by_id)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}