"""python experimental/dxy/rogue/notebooks/test_fim_vis1.py"""

from research.data.rag.rogue import RogueSampleConfig
from research.utils.generate_fim_data import FimDataProcessor
from research.fim import fim_sampling
from research.data.rag.rogue import _file_to_samples
from experimental.dxy.rogue.notebooks.exp_utils import (
    load_case_1,
    load_case_2,
    customized_node_weight,
)

REPO_LANGUAGES = [
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "ruby",
    "lua",
    "kotlin",
]
REPO_LANGUAGES_TEMP_FOR_UNIT_TEST = [
    "c",
    "c++",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
]

additional_sample_languages = [
    "sql",
    "markdown",
]
SAMPLE_LANGUAGES = REPO_LANGUAGES + additional_sample_languages

additional_retrieval_languages = [
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
]
RETRIEVAL_LANGUAGES = SAMPLE_LANGUAGES + additional_retrieval_languages


def get_config(limit_repos: int = 1000):
    # file_path_regex_pattern = get_unit_test_re_pattern(reverse=True)
    output_name = "normal"

    config = RogueSampleConfig(
        input="s3a://the-stack-processed/by-repo-3",
        output=f"s3a://dxy-dev-bucket/ragdata/limit-repo-{limit_repos}/{output_name}",
        fim_version=FimDataProcessor.VERSION,
        repo_languages=REPO_LANGUAGES,
        sample_languages=SAMPLE_LANGUAGES,
        retrieval_languages=RETRIEVAL_LANGUAGES,
        file_path_regex_pattern=None,
        limit_repos=limit_repos,
        repo_min_size=500000,
        repo_max_size=100000000,
        every_n_lines=100,
        max_problems_per_file=5,
        small_downsampled_probability=0.1,
        small_downsample_char_threshold=1500,
        small_filter_char_threshold=50,
        random_seed=74912,
        num_retrieved_chunks=40,
        scorer_config={
            "name": "ethanol",
            "checkpoint_path": "ethanol/ethanol6-04.1",
        },
        chunker_config={
            "name": "line_level",
            "max_lines_per_chunk": 30,
            "include_scope_annotation": False,
        },
        query_config={
            "name": "ethanol6_query",
            "max_tokens": 1023,
            "add_path": True,
        },
        document_config={
            "name": "ethanol6_document",
            "max_tokens": 999,
            "add_path": True,
        },
    )
    return config


# test_file_path, test_file_content = load_case_1()
test_file_path, test_file_content = load_case_2()

config = get_config()
config.random_seed = 12
# sampler = fim_sampling.CSTFimSampler()
sampler = fim_sampling.CSTFimSampler(
    node_size_soft_limit=64,  # disable the soft limit
    pick_whole_node_rate=0.9,
    corruption_expansion_rate=0.2,
    empty_completion_rate=0.02,
)
logs = []
samples = _file_to_samples(
    test_file_content,
    file_id="test001",
    file_path=test_file_path,
    config=config,
    sampler=sampler,
    get_node_weight=customized_node_weight,
    logs=logs,
)
print(f"The file has {len(test_file_content.splitlines())} lines.")
print(f"There are {len(samples)} samples")
import pdb

pdb.set_trace()
print(samples[0])
