{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.factories import create_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["config_elden_dsv2_fbw = {\n", "    \"name\": \"fastbackward\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/dxy/dscv2-elden/DLITE-MIX-BS1536S9K-LR1e5to1e6-mp1\",\n", "    \"override_prompt_formatter\": {\n", "        \"name\": \"ender\",\n", "    },\n", "    \"override_tokenizer\": \"deepseek_coder_v2\",\n", "    \"model_parallel_size\": 1,\n", "    \"seq_length\": 6142 + 512 + 2,\n", "    \"prompt\": {\n", "        \"max_prefix_tokens\": 1024,\n", "        \"max_suffix_tokens\": 512,\n", "        \"max_signature_tokens\": 1024,\n", "        \"max_retrieved_chunk_tokens\": -1,\n", "        \"max_prompt_tokens\": 6144,\n", "        \"component_order\": [\n", "            \"prefix\",\n", "            \"retrieval\",\n", "            \"signature\",\n", "            \"nearby_prefix\",\n", "            \"suffix\",\n", "        ],\n", "        \"context_quant_token_len\": 64,\n", "        \"nearby_prefix_token_len\": 512,\n", "        \"nearby_prefix_token_overlap\": 0,\n", "        \"nearby_suffix_token_len\": 0,\n", "        \"nearby_suffix_token_overlap\": 0,\n", "    },\n", "}\n", "model = create_model(config_elden_dsv2_fbw)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}