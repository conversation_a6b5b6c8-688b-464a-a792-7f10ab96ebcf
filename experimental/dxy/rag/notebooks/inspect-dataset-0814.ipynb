{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The dataset has 33190371 records.\n", "First record has 7937 tokens.\n"]}], "source": ["import pathlib\n", "import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer\n", "\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702-temp/mix-dataset-dscv2/dataset\"\n", "datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden-sc2/dataset\"\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-hindsight/dataset\"\n", "# Load this dataset via IndexedDataset.\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)\n", "print(f\"The dataset has {len(dataset)} records.\")\n", "print(f\"First record has {len(dataset[0])} tokens.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer = DeepSeekCoderV2Tokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "\n", "print(f\"{special_tokens.eos = }\")\n", "print(f\"{special_tokens.padding = }\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 3\n", "# Find the index of the first pad token.\n", "tokens = dataset[index].tolist()\n", "tokens = tokens[: tokens.index(special_tokens.padding)]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers import Llama3BaseTokenizer\n", "\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/validation_dataset\"\n", "datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/dataset\"\n", "\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)\n", "print(f\"The dataset has {len(dataset)} records.\")\n", "tokenizer = Llama3BaseTokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "\n", "index = 3\n", "# Find the index of the first pad token.\n", "tokens = dataset[index].tolist()\n", "tokens = tokens[: tokens.index(special_tokens.padding)]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}