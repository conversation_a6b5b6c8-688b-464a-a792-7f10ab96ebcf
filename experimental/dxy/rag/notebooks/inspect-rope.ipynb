{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "from base.fastforward import positional_embeddings, positional_embeddings_test_utils\n", "from base.fastforward.positional_embeddings import get_yarn_temperature_scaling_factor"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["extrapolation_ramp_mask: tensor([1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "        1.0000, 1.0000, 0.9231, 0.8462, 0.7692, 0.6923, 0.6154, 0.5385, 0.4615,\n", "        0.3846, 0.3077, 0.2308, 0.1538, 0.0769, 0.0000, 0.0000, 0.0000, 0.0000,\n", "        0.0000, 0.0000, 0.0000, 0.0000, 0.0000], device='cuda:0')\n", "freqs: tensor([1.0000e+00, 7.4989e-01, 5.6234e-01, 4.2170e-01, 3.1623e-01, 2.3714e-01,\n", "        1.7783e-01, 1.3335e-01, 1.0000e-01, 7.4989e-02, 5.6234e-02, 3.9007e-02,\n", "        2.6879e-02, 1.8378e-02, 1.2448e-02, 8.3345e-03, 5.5000e-03, 3.5620e-03,\n", "        2.2494e-03, 1.3705e-03, 7.9057e-04, 4.1499e-04, 1.7783e-04, 3.3338e-05,\n", "        2.5000e-05, 1.8747e-05, 1.4059e-05, 1.0542e-05, 7.9057e-06, 5.9284e-06,\n", "        4.4457e-06, 3.3338e-06], device='cuda:0')\n", "freqs[0]: tensor([0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "        0., 0., 0., 0., 0., 0., 0., 0.], device='cuda:0')\n", "freqs[1]: tensor([1.0000e+00, 7.4989e-01, 5.6234e-01, 4.2170e-01, 3.1623e-01, 2.3714e-01,\n", "        1.7783e-01, 1.3335e-01, 1.0000e-01, 7.4989e-02, 5.6234e-02, 3.9007e-02,\n", "        2.6879e-02, 1.8378e-02, 1.2448e-02, 8.3345e-03, 5.5000e-03, 3.5620e-03,\n", "        2.2494e-03, 1.3705e-03, 7.9057e-04, 4.1499e-04, 1.7783e-04, 3.3338e-05,\n", "        2.5000e-05, 1.8747e-05, 1.4059e-05, 1.0542e-05, 7.9057e-06, 5.9284e-06,\n", "        4.4457e-06, 3.3338e-06], device='cuda:0')\n", "temperature: 1.2608037774058554\n", "freqs.cos(): tensor([[ 1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000],\n", "        [ 0.5403,  0.7318,  0.8460,  0.9124,  0.9504,  0.9720,  0.9842,  0.9911,\n", "          0.9950,  0.9972,  0.9984,  0.9992,  0.9996,  0.9998,  0.9999,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000],\n", "        [-0.4161,  0.0709,  0.4315,  0.6649,  0.8066,  0.8896,  0.9374,  0.9646,\n", "          0.9801,  0.9888,  0.9937,  0.9970,  0.9986,  0.9993,  0.9997,  0.9999,\n", "          0.9999,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000],\n", "        [-0.9900, -0.6279, -0.1160,  0.3010,  0.5828,  0.7574,  0.8610,  0.9210,\n", "          0.9553,  0.9748,  0.9858,  0.9932,  0.9968,  0.9985,  0.9993,  0.9997,\n", "          0.9999,  0.9999,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000],\n", "        [-0.6536, -0.9899, -0.6277, -0.1157,  0.3011,  0.5829,  0.7575,  0.8611,\n", "          0.9211,  0.9553,  0.9748,  0.9879,  0.9942,  0.9973,  0.9988,  0.9994,\n", "          0.9998,  0.9999,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,\n", "          1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000,  1.0000]],\n", "       device='cuda:0')\n"]}], "source": ["fused_rope = positional_embeddings.FusedRotaryEmbedding(\n", "    head_dim=64 * 3,\n", "    max_seq_len=5,\n", "    config=positional_embeddings.RotaryConfig(\n", "        rotary_ratio=1 / 3.0 + 1e-7,\n", "        rotary_theta=10000,\n", "        max_position_embeddings=163840,\n", "        rotary_scaling_factor=40.0,\n", "        extended_config=positional_embeddings.ExtendedYaRNConfig(\n", "            unscaled_max_position_embeddings=4096,\n", "            beta_fast=32,\n", "            beta_slow=1,\n", "            mscale=0.707,\n", "        ),\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.2608037774058554\n", "<PERSON>.<PERSON><PERSON>([5, 32])\n"]}], "source": ["print(get_yarn_temperature_scaling_factor(40.0, 0.707))\n", "print(fused_rope.freqs_cos.shape)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["torch.allclose(fused_rope.freqs_cos[1],\n", "               torch.tensor([0.6812, 0.9226, 1.0667, 1.1504, 1.1983, 1.2255, 1.2409, 1.2496, 1.2545,\n", "        1.2573, 1.2588, 1.2598, 1.2603, 1.2606, 1.2607, 1.2608, 1.2608, 1.2608,\n", "        1.2608, 1.2608, 1.2608, 1.2608, 1.2608, 1.2608, 1.2608, 1.2608, 1.2608,\n", "        1.2608, 1.2608, 1.2608, 1.2608, 1.2608], device='cuda'), rtol=1e-3, atol=1e-3)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0., 0.,\n", "        0., 0., 0., 0., 0., 0., 0., 0.], device='cuda:0')"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["fused_rope.freqs_sin[0]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([0.5403, 0.7318, 0.8460, 0.9124, 0.9504, 0.9720, 0.9842, 0.9911, 0.9950,\n", "        0.9972, 0.9984, 0.9992, 0.9996, 0.9998, 0.9999, 1.0000, 1.0000, 1.0000,\n", "        1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000, 1.0000,\n", "        1.0000, 1.0000, 1.0000, 1.0000, 1.0000], device='cuda:0')\n"]}], "source": ["print(fused_rope.freqs_cos[1] / get_yarn_temperature_scaling_factor(40.0, 0.707))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.allclose(\n", "    fused_rope.freqs_cos[0], torch.zeros_like(fused_rope.freqs_cos[0]) + 1.2608\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(fused_rope.freqs_sin[0])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}