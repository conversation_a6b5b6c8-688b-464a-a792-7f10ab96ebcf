{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1 files in the repository.\n"]}], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "import random\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    if language is None:\n", "        continue\n", "    # print(str(relative_path))\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    if \"research/fastbackward/tests/test_checkpointing_utils.py\" not in str(relative_path):\n", "        continue\n", "    # if \"research/data/tests/collection/test_stackexchange.py\" not in str(relative_path):\n", "    #     continue\n", "    # if \"research/data/tests/synthetic_code_edit/test_seeds.py\" not in str(\n", "    #     relative_path\n", "    # ) and \"clients/vscode/src/__tests__/completions/supress-deleted-completionts.test.ts\" not in str(\n", "    #     relative_path\n", "    # ):\n", "    #     continue\n", "    # if \"clients/vscode/src/__tests__/completions/supress-deleted-completionts.test.ts\" not in str(\n", "    #     relative_path\n", "    # ):\n", "    #     continue\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")\n", "\n", "rng = random.Random(45)\n", "rng.shuffle(repo)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", ")\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "SAMPLE_LANGUAGES = [\"python\", \"typescript\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=50,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=104,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collect 36 artifacts\n", "There are 3 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_fim_samples_from_repo_with_multiple_configs,\n", "    generate_retrieved_chunks_from_fim,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from research.core.artifacts import collect_artifacts\n", "from research.fim.fim_sampling_experimental import DefaultUnitTestCorruptionNodesPicker, LiteralUnitTestCorruptionNodesPicker\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "node_picker = DefaultUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.3,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.3,\n", ")\n", "sampler = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker,\n", ")\n", "\n", "node_picker_literal = LiteralUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.5,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.0,\n", "    edit_similarity_threshold=0.3,\n", "    max_num_lines_per_node=10,\n", "    max_num_char_per_node=400,\n", ")\n", "sampler_literal = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker_literal,\n", ")\n", "# generate_fim_samples_from_repo(\n", "#     repo, config_fim, sampler, node_picker.get_node_weight\n", "# ),\n", "with collect_artifacts() as collector:\n", "    samples = list(\n", "        generate_retrieved_chunks_from_fim(\n", "            repo,\n", "            generate_fim_samples_from_repo_with_multiple_configs(repo, [\n", "                # (config_fim, sampler, node_picker.get_node_weight),\n", "                (config_fim, sampler_literal, node_picker_literal.get_node_weight),\n", "            ]),\n", "            config=config_retrieval,\n", "            retrieval_database=NullRetrievalDatabase(),\n", "            tokenizer=tokenizer,\n", "        )\n", "    )\n", "    artifacts = collector.get_artifacts()\n", "    print(f\"Collect {len(artifacts)} artifacts\")\n", "print(f\"There are {len(samples)} final samples.\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/fastbackward/tests/test_checkpointing_utils.py\n", "\n", "\u001b[92m        )\n", "    if model_args.pos_embed_type == \"absolute\":\n", "        state_dict[\"pos_embeddings.weight\"] = torch.rand(\n", "            model_args.max_seq_len, model_args.dim\n", "        )\n", "    return state_dict\n", "\n", "\n", "@pytest.mark.parametrize(\"n_kv_heads\", [0, 1, 2])\n", "@pytest.mark.parametrize(\"pos_embed_type\", [\"absolute\", \"rope\"])\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\"norm_type\", [\"layernorm\", \"rmsnorm\"])\n", "@pytest.mark.parametrize(\"bias\", [\"attn_mlp\", \"none\"])\u001b[0m\u001b[94m\n", "@pytest.mark.parametrize(\"ffn_type\", [\"glu\", \"mlp\"])\n", "@pytest.mark.parametrize(\"world_size\", [1, 2, 4])\n", "def test_split_and_merge_consolidated_checkpoint(\n", "    world_size: int,\n", "    ffn_type: str,\n", "    bias: str,\n", "    norm_type: str,\n", "    pos_embed_type: str,\n", "    n_kv_heads: int,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[0], 10, 10)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/fastbackward/tests/test_checkpointing_utils.py\n", "\n", "\u001b[92m                f\"Merged shape: {merged_state_dict[name].shape}\"\n", "            ),\n", "        )\n", "\n", "\n", "@pytest.mark.parametrize(\"n_kv_heads\", [0, 1, 2])\n", "@pytest.mark.parametrize(\"pos_embed_type\", [\"absolute\", \"rope\"])\n", "@pytest.mark.parametrize(\"norm_type\", [\"layernorm\", \"rmsnorm\"])\n", "@pytest.mark.parametrize(\"bias\", [\"attn_mlp\", \"none\"])\n", "@pytest.mark.parametrize(\"ffn_type\", [\"glu\", \"mlp\"])\n", "\u001b[0m\u001b[47m\u001b[<EMAIL>(\"world_size\", [1, 2, 4])\u001b[0m\u001b[94m\n", "def test_merge_and_split_checkpoints(\n", "    world_size: int,\n", "    ffn_type: str,\n", "    bias: str,\n", "    norm_type: str,\n", "    pos_embed_type: str,\n", "    n_kv_heads: int,\n", "):\n", "    \"\"\"Round-trip test for checkpoint merging and splitting.\"\"\"\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[1], 10, 10)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/fastbackward/tests/test_checkpointing_utils.py\n", "\n", "\u001b[92m        \"layers.0.attention_norm.weight\": torch.rand(model_args.dim),\n", "        \"layers.0.attention.wq.weight\": torch.rand(model_args.dim, model_args.dim),\n", "        \"layers.0.attention.wk.weight\": torch.rand(model_args.dim, model_args.dim),\n", "        \"layers.0.attention.wv.weight\": torch.rand(model_args.dim, model_args.dim),\n", "        \"layers.0.attention.wo.weight\": torch.rand(model_args.dim, model_args.dim),\n", "        \"layers.0.ffn_norm.weight\": torch.rand(model_args.dim),\n", "        \"layers.0.feed_forward.w1.weight\": torch.rand(model_args.dim, model_args.dim),\n", "        \"layers.0.feed_forward.w2.weight\": torch.rand(model_args.dim, model_args.dim),\n", "    }\n", "    \u001b[0m\u001b[47m\u001b[30mif model_args.ffn_type == \"glu\":\n", "        state_dict[\"layers.0.feed_forward.w3.weight\"] = torch.rand(\n", "            model_args.dim, model_args.dim\n", "        )\n", "    if model_args.bias in [\"attn_mlp\"]:\n", "        state_dict.update(\n", "            {\n", "                \"layers.0.attention.wq.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.attention.wk.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.attention.wv.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.attention.wo.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.feed_forward.w1.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.feed_forward.w2.bias\": torch.rand(model_args.dim),\n", "            }\n", "        )\n", "        if model_args.ffn_type == \"glu\":\n", "            state_dict[\"layers.0.feed_forward.w3.bias\"] = torch.rand(model_args.dim)\n", "\u001b[0m\u001b[94m    if model_args.norm_type == \"layernorm\":\n", "        state_dict.update(\n", "            {\n", "                \"norm.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.attention_norm.bias\": torch.rand(model_args.dim),\n", "                \"layers.0.ffn_norm.bias\": torch.rand(model_args.dim),\n", "            }\n", "        )\n", "    if model_args.pos_embed_type == \"absolute\":\n", "        state_dict[\"pos_embeddings.weight\"] = torch.rand(\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[2], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[2], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[3], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[4], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[5], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[6], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[7], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[8], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def pretty(d, indent=0):\n", "    for key, value in d.items():\n", "        print(\"\\t\" * indent + str(key))\n", "        if isinstance(value, dict):\n", "            pretty(value, indent + 1)\n", "        else:\n", "            print(\"\\t\" * (indent + 1) + f\"{str(value).encode()}\")\n", "    print(\"-\" * 100 + \"\\n\")\n", "\n", "\n", "for x in artifacts[:100]:\n", "    pretty(x, 2)    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[0], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[1], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[2], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[3], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[4], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples[5], 10, 10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "from experimental.dxy.rag.exps.utils_system import (\n", "    build_model_input,\n", "    load_all_systems,\n", "    compare_models,\n", ")\n", "from research.core.types import Document\n", "from termcolor import colored\n", "from research.data.rag.rogue_stages import RetrievalAugmentedSample\n", "from experimental.dxy.rag.exps.utils import shift_middle_to_prefix\n", "\n", "systems = load_all_systems()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- v1[0] correct\n", "- v2[0] wrong -- need the testdata folder structure\n", "- v2[1] wrong\n", "- v2[2] correct\n", "- v2[4] wrong"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[0], 10, 10)\n", "compare_models(systems, samples_v1[0], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[2], 10, 10)\n", "compare_models(systems, samples_v2[2], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[13], 10, 10)\n", "# compare_models(systems, samples_v2[1], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[2], 10, 10)\n", "compare_models(systems, samples_v2[2], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[3], 10, 10)\n", "compare_models(systems, samples_v2[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[4], 10, 10)\n", "compare_models(systems, samples_v2[4], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[3], 10, 10)\n", "compare_models(systems, samples_v1[3], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[4], 10, 10)\n", "compare_models(systems, samples_v1[4], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v1[5], 10, 10)\n", "compare_models(systems, samples_v1[5], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["show_truncated_completion(samples_v2[5], 10, 10)\n", "compare_models(systems, samples_v2[5], doc_by_id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = \"\"\"test(\"set state\", () => {\n", "        const mockStatusBar: StatusBarItem = newMockStatusBar();\n", "\n", "        window.createStatusBarItem = jest.fn().mockReturnValue(mockStatusBar);\n", "\n", "        const state: StateDefinition = {\n", "            priority: StatePriority.high,\n", "            tooltip: \"Example tooltip\",\n", "            icon: AugmentIcons.error,\n", "            colors: {\n", "                background: new ThemeColor(\"statusBarItem.errorBackground\"),\n", "                foreground: new ThemeColor(\"statusBarItem.errorForeground\"),\n", "            },\n", "        };\n", "\n", "        const manager = new StatusBarManager();\n", "        manager.setState(state);\n", "        assertState(mockStatusBar, state);\n", "    });\"\"\"\n", "print(len(x))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}