{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import base.tokenizers as prod_tokenizers\n", "\n", "tokenizers = dict()\n", "tokenizers[\"sc2\"] = prod_tokenizers.StarCoder2Tokenizer()\n", "tokenizers[\"llama3\"] = prod_tokenizers.Llama3BaseTokenizer()\n", "tokenizers[\"dsc\"] = prod_tokenizers.DeepSeekCoderBaseTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key, tokenizer in tokenizers.items():\n", "    print(f\"Tokenizer {tokenizer.__class__.__name__} has {tokenizer.vocab_size} tokens.\")\n", "    print(f\"fim_middle_id = {tokenizer.fim_middle_id}\")\n", "    print(f\"eot_token_id = {tokenizer.eod_id}\")\n", "    print(f\"pad_token_id = {tokenizer.pad_id}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}