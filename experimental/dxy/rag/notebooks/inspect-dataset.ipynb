{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The dataset_v1 has 5070660 records.\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The dataset_v2 has 933088 records.\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The dataset_v3 has 4368537 records.\n"]}], "source": ["import pathlib\n", "import megatron.data.indexed_dataset as indexed_dataset\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "\n", "datapath_v1 = \"/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v1-5M/dataset\"\n", "datapath_v2 = \"/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/unit-test-1M/dataset\"\n", "datapath_v3 = \"/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/dataset\"\n", "\n", "# Load this dataset via IndexedDataset.\n", "dataset_v1 = indexed_dataset.MMapIndexedDataset(datapath_v1, skip_warmup=True)\n", "print(f\"The dataset_v1 has {len(dataset_v1)} records.\")\n", "\n", "dataset_v2 = indexed_dataset.MMapIndexedDataset(datapath_v2, skip_warmup=True)\n", "print(f\"The dataset_v2 has {len(dataset_v2)} records.\")\n", "\n", "dataset_v3 = indexed_dataset.MMapIndexedDataset(datapath_v3, skip_warmup=True)\n", "print(f\"The dataset_v3 has {len(dataset_v3)} records.\")\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<file_sep>hihope_neptune-oh_hid/00_src/v0.1/foundation/graphic/ui/test/unittest/dfx/event_injector_unit_test.cpp\n", "<|far_prefix|> of the License at\n", " *\n", " *     http://www.apache.org/licenses/LICENSE-2.0\n", " *\n", " * Unless required by applicable law or agreed to in writing, software\n", " * distributed under the License is distributed on an \"AS IS\" BASIS,\n", " * WITHOUT WAR<PERSON>NTIES OR CONDITIONS OF ANY KIND, either express or implied.\n", " * See the License for the specific language governing permissions and\n", " * limitations under the License.\n", " */\n", "\n", "#include \"dfx/event_injector.h\"\n", "\n", "#if ENABLE_DEBUG\n", "#include \"common/graphic_startup.h\"\n", "#include \"common/screen.h\"\n", "#include \"components/root_view.h\"\n", "#include \"common/task_manager.h\"\n", "#include \"common/input_device_manager.h\"\n", "#include \"core/render_manager.h\"\n", "#include \"dock/screen_device_proxy.h\"\n", "#include \"components/ui_label_button.h\"\n", "#include \"components/ui_scroll_view.h\"\n", "#include <climits>\n", "#include \"gfx_utils/file.h\"\n", "#include \"font/ui_font.h\"\n", "#include <gtest/gtest.h>\n", "#include \"graphic_config.h\"\n", "#include \"gfx_utils/graphic_log.h\"\n", "#include \"imgdecode/cache_manager.h\"\n", "#include \"layout/grid_layout.h\"\n", "#include <pthread.h>\n", "#include <unistd.h>\n", "#include \"window/window.h\"\n", "\n", "using namespace testing::ext;\n", "\n", "enum TestEventInjectorFlag {\n", "    FLAG0,\n", "    FLAG1\n", "};\n", "\n", "namespace {\n", "uint8_t REGISTER_POINT_FLAG = FLAG0;\n", "uint8_t REGISTER_KEY_FLAG = FLAG0;\n", "uint8_t UNREGISTER_POINT_FLAG = FLAG0;\n", "uint8_t UNREGISTER_KEY_FLAG = FLAG0;\n", "uint8_t CLICK_FLAG = FLAG0;\n", "uint8_t LONG_PRESS_FLAG = FLAG0;\n", "uint8_t DRAG_FLAG = FLAG0;\n", "uint8_t KEY_FLAG = FLAG0;\n", "uint8_t MAX_LOOP = 200;\n", "}\n", "\n", "namespace OHOS {\n", "class TestEventInjectorView : public UIView, public RootView::OnKeyActListener {\n", "public:\n", "    bool OnLongPressEvent(const LongPressEvent& event) override\n", "    {\n", "        LONG_PRESS_FLAG = FLAG1;\n", "        return true;\n", "    }\n", "\n", "    bool OnClickEvent(const ClickEvent& event) override\n", "    {\n", "        CLICK_FLAG = FLAG1;\n", "        return true;\n", "    }\n", "\n", "    bool OnDragEvent(const DragEvent& event) override\n", "    {\n", "        DRAG_FLAG = FLAG1;\n", "        return true;\n", "    }\n", "\n", "    bool OnKeyAct(UIView& view, const KeyEvent& event) override\n", "    {\n", "        KEY_FLAG = FLAG1;\n", "        return true;\n", "    }\n", "};\n", "\n", "class EventInjectorTest : public testing::Test {\n", "public:\n", "    static void SetUpTestCase(void);\n", "    static void TearDownTestCase(void);\n", "    static void TestApp();\n", "    static void SetUpTestview(TestEventInjectorView* testView, bool touchable, bool draggable);\n", "    static void* MainTask(void* args);\n", "    static void DeleteChildren(UIView* view);\n", "\n", "    static pthread_t mainTaskThread_;\n", "    static bool isRepeat_;\n", "    static  RootView* rootView_;\n", "    static GridLayout* layout_;\n", "    static TestEventInjectorView* clickView_;\n", "    static TestEventInjectorView* dragView_;\n", "    static TestEventInjectorView* longPressView_;\n", "    static TestEventInjectorView* keyView_;\n", "    static Window* window_;\n", "};\n", "\n", "pthread_t EventInjectorTest::mainTaskThread_ = -1;\n", "bool EventInjectorTest::isRepeat_ = true;\n", "RootView* EventInjectorTest::rootView_ = nullptr;\n", "GridLayout* EventInjectorTest::layout_ = nullptr;\n", "TestEventInjectorView* EventInjectorTest::clickView_ = nullptr;\n", "TestEventInjectorView* EventInjectorTest::dragView_ = nullptr;\n", "TestEventInjectorView* EventInjectorTest::longPressView_ = nullptr;\n", "TestEventInjectorView* EventInjectorTest::keyView_ = nullptr;\n", "Window* EventInjectorTest::window_ = nullptr;\n", "\n", "static void InitHal()\n", "{\n", "    ScreenDevice* display = new ScreenDevice();\n", "    ScreenDeviceProxy::GetInstance()->SetDevice(display);\n", "    ScreenDeviceProxy::GetInstance()->SetScreenSize(HORIZONTAL_RESOLUTION, VERTICAL_RESOLUTION);\n", "}\n", "\n", "void EventInjectorTest::SetUpTestCase(void)\n", "<pr_base><pr_file><file_sep>hihope_neptune-oh_hid/00_src/v0.1/foundation/graphic/ui/test/uitest/test_event_injector/ui_test_event_injector.cpp<pr_base_code>    UIKit_Event_Injector_Key_Event_004();\n", "\n", "    SetUpScrollView();\n", "    UIKit_Event_Injector_Up_to_Down_005();\n", "    UIKit_Event_Injector_Down_to_Up_006();\n", "    UIKit_Event_Injector_Left_to_Right_007();\n", "    UIKit_Event_Injector_Right_to_Left_008();\n", "    UIKit_Event_Injector_ULeft_to_LRight_009();\n", "    UIKit_Event_Injector_LRight_to_ULeft_010();\n", "    IncreaseDragTime();\n", "    DecreaseDragTime();\n", "    DragTimeDisplay();\n", "\n", "    layout_->LayoutChildren();\n", "    return container_;\n", "}\n", "\n", "void UITestEventInjector::UIKit_Event_Injector_Click_Event_001()\n", "{\n", "    clickBtn_ = new UILabelButton();\n", "    InnerTest(\"模拟点击事件 \", true, false, false, \"click\", clickBtn_, clickTestView_);\n", "}\n", "\n", "void UITestEventInjector::UIKit_Event_Injector_Drag_Event_002()\n", "{\n", "    dragBtn_ = new UILabelButton();\n", "    InnerTest(\"模拟滑动事件 \", true, true, false, \"drag\", dragBtn_, dragTestView_);\n", "}\n", "\n", "void UITestEventInjector::UIKit_Event_Injector_Long_Press_Event_003()\n", "<pr_file><file_sep>hihope_neptune-oh_hid/00_src/v0.1/foundation/graphic/ui/test/uitest/test_event_injector/ui_test_event_injector.h<pr_base_code>class TestEventInjectorView;\n", "class UITestEventInjector : public UITest, public UIView::OnClickListener {\n", "public:\n", "    UITestEventInjector() {}\n", "    ~UITestEventInjector() {}\n", "    void SetUp() override;\n", "    void TearDown() override;\n", "    UIView* GetTestView() override;\n", "    bool OnClick(UIView& view, const ClickEvent& event) override;\n", "\n", "    void UIKit_Event_Injector_Click_Event_001();\n", "    void UIKit_Event_Injector_Drag_Event_002();\n", "    void UIKit_Event_Injector_Long_Press_Event_003();\n", "    void UIKit_Event_Injector_Key_Event_004();\n", "    void UIKit_Event_Injector_Up_to_Down_005();\n", "    void UIKit_Event_Injector_Down_to_Up_006();\n", "    void UIKit_Event_Injector_Left_to_Right_007();\n", "    void UIKit_Event_Injector_Right_to_Left_008();\n", "    void UIKit_Event_Injector_ULeft_to_LRight_009();\n", "    void UIKit_Event_Injector_LRight_to_ULeft_010();\n", "\n", "private:\n", "    UIScrollView* container_ = nullptr;\n", "    GridLayout* layout_ = nullptr;\n", "    UILabelButton* clickBtn_ = nullptr;\n", "    UILabelButton* dragBtn_ = nullptr;\n", "    UILabelButton* longPressBtn_ = nullptr;\n", "    UILabelButton* keyBtn_ = nullptr;\n", "    UILabelButton* upToDownBtn_ = nullptr;\n", "    UILabelButton* downToUpBtn_ = nullptr;\n", "<pr_file><file_sep>hihope_neptune-oh_hid/00_src/v0.1/foundation/graphic/ui/test/unittest/events/event_bubble_unit_test.cpp<pr_base_code>    GraphicStartUp::Init();\n", "    InitHal();\n", "    TestApp();\n", "}\n", "\n", "void EventBubbleTest::TestApp()\n", "{\n", "    rootView_ = RootView::GetInstance();\n", "    rootView_->SetTouchable(true);\n", "    rootView_->SetPosition(0, 0, Screen::GetInstance().GetWidth(), Screen::GetInstance().GetHeight());\n", "    layout_ = new GridLayout();\n", "    layout_->SetPosition(0, 0, Screen::GetInstance().GetWidth(), Screen::GetInstance().GetHeight());\n", "    rootView_->Add(layout_);\n", "    layout_->SetLayoutDirection(LAYOUT_VER);\n", "    layout_->SetRows(6); /* 6:rows */\n", "    layout_->SetCols(1);\n", "\n", "    clickView_ = new TestEventBubbleView();\n", "    SetUpTestview(clickView_, true, false);\n", "\n", "    longPressView_ = new TestEventBubbleView();\n", "    SetUpTestview(longPressView_, true, false);\n", "\n", "    dragView_ = new TestEventBubbleView();\n", "    SetUpTestview(dragView_, true, true);\n", "\n", "    unTouchView_ = new TestEventBubbleView();\n", "    SetUpTestview(unTouchView_, false, false);\n", "\n", "    keyView_ = new TestEventBubbleView();\n", "<fim_prefix>{\n", "    GraphicStartUp::Init();\n", "    InitHal();\n", "    TestApp();\n", "    if (pthread_create(&mainTaskThread_, nullptr, MainTask, nullptr) != 0) {\n", "        return;\n", "    }\n", "}\n", "\n", "void* EventInjectorTest::MainTask(void* args)\n", "{\n", "    while (isRepeat_) {\n", "        /* Periodically call TaskHandler(). It could be done in a timer interrupt or an OS task too. */\n", "        OHOS::TaskManager::GetInstance()->TaskHandler();\n", "        usleep(1000 * 10); /* 1000 * 10:10ms Just to let the system breathe */\n", "    }\n", "    return nullptr;\n", "}\n", "\n", "void EventInjectorTest::TestApp()\n", "{\n", "    WindowConfig config = {};\n", "    config.rect.SetRect(0, 0, Screen::GetInstance().GetWidth() - 1, Screen::GetInstance().GetHeight() - 1);\n", "    window_ = Window::CreateWindow(config);\n", "    if (window_ == nullptr) {\n", "        GRAPHIC_LOGE(\"Create window false!\");\n", "        return;\n", "    }\n", "    window_->BindRootView(RootView::GetInstance());\n", "\n", "    if (EventInjector::GetInstance()->RegisterEventInjector(EventDataType::POINT_TYPE)) {\n", "        REGISTER_POINT_FLAG = FLAG1;\n", "    }\n", "    if (EventInjector::GetInstance()->RegisterEventInjector(EventDataType::KEY_TYPE)) {\n", "        REGISTER_KEY_FLAG = FLAG1;\n", "    }\n", "    EventInjector::GetInstance()->SetWindowId(window_->GetWindowId());\n", "    rootView_ = RootView::GetInstance();\n", "    rootView_->SetPosition(0, 0, Screen::GetInstance().GetWidth(), Screen::GetInstance().GetHeight());\n", "    layout_ = new GridLayout();\n", "    layout_->SetPosition(0, 0, Screen::GetInstance().GetWidth(), Screen::GetInstance().GetHeight());\n", "    rootView_->Add(layout_);\n", "    layout_->SetLayoutDirection(LAYOUT_VER);\n", "    layout_->SetRows(4); /* 4:rows */\n", "    layout_->SetCols(1);\n", "    clickView_ = new TestEventInjectorView();\n", "    <fim_suffix>\n", "\n", "    dragView_ = new TestEventInjectorView();\n", "    SetUpTestview(dragView_, true, true);\n", "\n", "    keyView_ = new TestEventInjectorView();\n", "    RootView::GetInstance()->SetOnKeyActListener(keyView_);\n", "    SetUpTestview(keyView_, true, false);\n", "\n", "    layout_->LayoutChildren();\n", "    rootView_->Invalidate();\n", "}\n", "\n", "void EventInjectorTest::SetUpTestview(TestEventInjectorView* testView, bool touchable, bool draggable)\n", "{\n", "    layout_->Add(testView);\n", "    testView->Resize(HORIZONTAL_RESOLUTION, VERTICAL_RESOLUTION / 5); /* 5:ratio */\n", "    testView->SetTouchable(touchable);\n", "    testView->SetDraggable(draggable);\n", "}\n", "\n", "void EventInjectorTest::DeleteChildren(UIView* view)\n", "{\n", "    if (view == nullptr) {\n", "        return;\n", "    }\n", "    while (view != nullptr) {\n", "        UIView* tempView = view;\n", "        view = view->GetNextSibling();\n", "        if (tempView->IsViewGroup()) {\n", "            DeleteChildren(static_cast<UIViewGroup*>(tempView)->GetChildrenHead());\n", "        }\n", "        if (tempView->GetParent()) {\n", "            static_cast<UIViewGroup*>(tempView->GetParent())->Remove(tempView);\n", "        }\n", "        delete tempView;\n", "    }\n", "}\n", "\n", "void EventInjectorTest::TearDownTestCase(void)\n", "{\n", "    isRepeat_ = false;\n", "    pthread_join(mainTaskThread_, nullptr);\n", "    Window::DestoryWindow(window_);\n", "    DeleteChildren(layout_);\n", "    layout_ = nullptr;\n", "\n", "    EventInjector::GetInstance()->UnregisterEventInjector(EventDataType::POINT_TYPE);\n", "    EventInjector::GetInstance()->UnregisterEventInjector(EventDataType::KEY_TYPE);\n", "}\n", "\n", "/**\n", " * @tc.name: Graphic_EventInjectorTest_Test_RegisterEventInjector_001\n", " * @tc.desc: Verify RegisterEventInjector function, equal.\n", " * @tc.type: FUNC\n", " * @tc.require: SR000F74SS\n", " */\n", "HWTEST_F(EventInjectorTest, Graphic_EventInjectorTest_Test_RegisterEventInjector_001, TestSize.Level0)\n", "{\n", "    EXPECT_<fim_middle>SetUpTestview(clickView_, true, false);<|pause|>\n", "\n", "    longPressView_ = new TestEventInjectorView();<|pause|>\n", "    SetUpTestview(longPressView_, true, false);<|endoftext|>\n"]}], "source": ["index = 2\n", "# Find the index of the first pad token.\n", "tokens = dataset_v2[index].tolist()\n", "tokens = tokens[: tokens.index(tokenizer.pad_id)]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["49152"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}