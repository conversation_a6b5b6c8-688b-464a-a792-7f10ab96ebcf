{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The dataset has 4265 records.\n", "First record has 7937 tokens.\n"]}], "source": ["import pathlib\n", "import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_v2.0-sc2/dataset\"\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden_v2.0-sc2/dataset\"\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0913_v2\"\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0913_v2\"\n", "datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-tutor-hindsight/0913-simple_elden_v2\"\n", "# Load this dataset via IndexedDataset.\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)\n", "print(f\"The dataset has {len(dataset)} records.\")\n", "print(f\"First record has {len(dataset[0])} tokens.\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["special_tokens.eos = 0\n", "special_tokens.padding = 49152\n"]}], "source": ["tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "\n", "print(f\"{special_tokens.eos = }\")\n", "print(f\"{special_tokens.padding = }\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Retrieved Chunks Start:\n", "file path: src/client/components/session/session.jsx\n", "Name='type-tab'>\n", "        <span className='mg1r'>Try <b>Shift + Backspace</b>?</span>\n", "        <CloseOutlined\n", "          onClick={this.handleDismissDelKeyTip}\n", "          className='pointer'\n", "        />\n", "      </div>\n", "    )\n", "  }\n", "\n", "  handleSplitVert = () => {\n", "    window.store.splitTerminalSftp = 'vertical'\n", "  }\n", "\n", "  handleSplitHori = () => {\n", "    window.store.splitTerminalSftp = 'horizontal'\n", "  }\n", "\n", "  renderToggles = (shouldHaveSpliter) => {\n", "    if (!shouldHaveSpliter) {\n", "      return null\n", "    }\n", "    return [\n", "      <BorderVerticleOutlined\n", "        className='pointer iblock spliter-ver'\n", "        onClick={this.handleSplitVert}\n", "        key='split1'\n", "      />,\n", "      <BorderHorizontalOutlined\n", "        className='pointer iblock spliter-hori mg1x'\n", "        onClick={this.handleSplitHori}\n", "        key='split2'\n", "      />\n", "    ]\n", "  }\n", "\n", "  renderControl = () => {\n", "    const { splitDirection, terminals, sftpPathFollowSsh } = this.state\n", "    const { props } = this\n", "    const { pane, enableSsh, type } = props.tab\n", "    if (type === terminalRdpType) {\n", "      re\n", "file path: src/client/components/session/sessions.jsx\n", "     />\n", "          </div>\n", "        )\n", "      }\n", "      return (\n", "        <div className={cls} key={id}>\n", "          <Session\n", "            {...sessProps}\n", "          />\n", "        </div>\n", "      )\n", "    })\n", "  }\n", "\n", "  renderTabs = () => {\n", "    const {\n", "      store,\n", "      config\n", "    } = this.props\n", "    const {\n", "      tabs,\n", "      currentTabId\n", "    } = this.state\n", "    const tabsProps = {\n", "      currentTabId,\n", "      config,\n", "      ...pick(store, [\n", "        'height',\n", "        'width',\n", "        'activeTerminalId',\n", "        'isMaximized',\n", "        'splitTerminalSftp'\n", "      ]),\n", "      tabs,\n", "      ...pick(this, [\n", "        'setTabs',\n", "        'onChangeTabId',\n", "        'onDuplicateTab',\n", "        'reloadTab',\n", "        'delTab',\n", "        'addTab',\n", "        'editTab'\n", "      ])\n", "    }\n", "    return (\n", "      <Tabs\n", "        key='main-tabs'\n", "        {...tabsProps}\n", "      />\n", "    )\n", "  }\n", "\n", "  renderSessionsWrap = () => {\n", "    const { leftSidebarWidth, openedSideBar } = this.props.store\n", "    const w = leftSidebarWidth + 42\n", "    const ptp = openedSideBar\n", "      ? {\n", "          className: 'sessio\n", "file path: src/client/components/session/session.jsx\n", "/terminal'\n", "import Sftp from '../sftp/sftp-entry'\n", "import RdpSession from '../rdp/rdp-session'\n", "import {\n", "  BorderVerticle<PERSON>utlined,\n", "  BorderHorizontalOutlined,\n", "  CloseSquareFilled,\n", "  SearchOutlined,\n", "  FullscreenOutlined,\n", "  PaperClipOutlined,\n", "  CloseOutlined\n", "} from '@ant-design/icons'\n", "import {\n", "  <PERSON><PERSON><PERSON>\n", "} from 'antd'\n", "import { last, findIndex, pick } from 'lodash-es'\n", "import generate from '../../common/uid'\n", "import copy from 'json-deep-copy'\n", "import classnames from 'classnames'\n", "import {\n", "  quickCommandBoxHeight,\n", "  terminalSplitDirectionMap,\n", "  termControlHeight,\n", "  paneMap,\n", "  footerHeight,\n", "  terminalActions,\n", "  connectionMap,\n", "  terminalRdpType\n", "} from '../../common/constants'\n", "import ResizeWrap from '../common/resize-wrap'\n", "import safeName from '../../common/safe-name'\n", "import TerminalInfoContent from '../terminal-info/content'\n", "import uid from '../../common/id-with-stamp'\n", "import postMessage from '../../common/post-msg'\n", "import './session.styl'\n", "import classNames from 'classnames'\n", "\n", "const rebuildPosition = terminals => {\n", "  const \n", "file path: src/client/components/session/session.jsx\n", "    this.editTab(update)\n", "  }\n", "\n", "  setSessionState = data => {\n", "    this.setState(data)\n", "    if (data.pid) {\n", "      this.editTab({\n", "        pid: data.pid\n", "      })\n", "    }\n", "  }\n", "\n", "  handleSplit = (e, id) => {\n", "    let terminals = copy(this.state.terminals)\n", "    let index = findIndex(terminals, t => t.id === id)\n", "    if (index === -1) {\n", "      index = terminals.length\n", "    } else {\n", "      index = index + 1\n", "    }\n", "    terminals.push({\n", "      id: uid(),\n", "      position: terminals[index - 1].position + 5\n", "    })\n", "    terminals = rebuildPosition(terminals)\n", "    this.setState({\n", "      terminals\n", "    }, this.updateTab)\n", "  }\n", "\n", "  updateTab = () => {\n", "    const terminals = copy(this.state.terminals)\n", "    this.editTab(\n", "      {\n", "        sessionId: this.state.sessionId,\n", "        terminals\n", "      }\n", "    )\n", "  }\n", "\n", "\n", "file path: src/client/components/terminal/index.jsx\n", "  }\n", "\n", "  render () {\n", "    const { id, loading } = this.state\n", "    const { height, width, left, top, position, id: pid, activeSplitId } = this.props\n", "    const cls = classnames('term-wrap', {\n", "      'not-first-term': !!position\n", "    }, 'tw-' + pid, {\n", "      'terminal-not-active': activeSplitId !== pid\n", "    })\n", "    const prps1 = {\n", "      className: cls,\n", "      style: {\n", "        height,\n", "        width,\n", "        left,\n", "        top,\n", "        zIndex: position / 10\n", "      },\n", "      onDrop: this.onDrop\n", "    }\n", "    // const fileProps = {\n", "    //   type: 'file',\n", "    //   multiple: true,\n", "    //   id: `${id}-file-sel`,\n", "    //   className: 'hide'\n", "    // }\n", "    const prps2 = {\n", "      className: 'absolute term-wrap-1',\n", "      style: {\n", "        left: '10px',\n", "        top: '10px',\n", "        right: 0,\n", "        bottom: 0\n", "      }\n", "    }\n", "    const prps3 = {\n", "      id,\n", "      className: 'absolute term-wrap-2',\n", "      style: {\n", "\n", "file path: src/client/components/session/sessions.jsx\n", "    const {\n", "      store,\n", "      config\n", "    } = this.props\n", "    const {\n", "      tabs,\n", "      currentTabId\n", "    } = this.state\n", "    const tabsProps = {\n", "      currentTabId,\n", "      config,\n", "      ...pick(store, [\n", "        'height',\n", "        'width',\n", "        'activeTerminalId',\n", "        'isMaximized',\n", "        'splitTerminalSftp'\n", "      ]),\n", "      tabs,\n", "      ...pick(this, [\n", "        'setTabs',\n", "        'onChangeTabId',\n", "        'onDuplicateTab',\n", "        'reloadTab',\n", "        'delTab',\n", "        'addTab',\n", "        'editTab'\n", "      ])\n", "    }\n", "    return (\n", "      <Tabs\n", "        key='main-tabs'\n", "        {...tabsProps}\n", "      />\n", "    )\n", "  }\n", "\n", "  renderSessionsWrap = () => {\n", "    const { leftSidebarWidth, openedSideBar } = this.props.store\n", "    const w = leftSidebarWidth + 42\n", "\n", "file path: src/client/components/session/session.jsx\n", "  delSplit = (splitId = this.state.activeSplitId) => {\n", "    const { terminals } = this.state\n", "    let newTerms = terminals.filter(t => t.id !== splitId)\n", "    if (!newTerms.length) {\n", "      return this.props.delTab(\n", "        this.props.tab.id\n", "      )\n", "    }\n", "    newTerms = rebuildPosition(newTerms)\n", "    const newActiveId = getPrevTerminal(newTerms).id\n", "    this.setState({\n", "      terminals: newTerms,\n", "      activeSplitId: newActiveId\n", "    }, this.updateTab)\n", "    window.store.focus()\n", "  }\n", "\n", "  handleChangeDirection = () => {\n", "    const { splitDirection } = this.state\n", "    this.setState({\n", "      splitDirection: splitDirection === terminalSplitDirectionMap.horizontal\n", "        ? terminalSplitDirectionMap.vertical\n", "        : terminalSplitDirectionMap.horizontal\n", "    })\n", "  }\n", "\n", "  setActive = activeSplitId => {\n", "    const up = {\n", "      activeSplitId\n", "    }\n", "    this.setState(up)\n", "  }\n", "\n", "  computePosition = (index) => {\n", "    const len = this.state.terminals.length || 1\n", "    const windowWidth = this.getWidth()\n", "    const { splitDirection } = this.state\n", "    const isHori = splitDirection === terminalSplitDirectionMap.horizontal\n", "    const heightAll = this.computeHeight()\n", "    const width = isHori\n", "\n", "file path: src/client/components/session/session.jsx\n", "\n", "const rebuildPosition = terminals => {\n", "  const indexs = terminals.map(t => t.position).sort((a, b) => a - b)\n", "  const indexMap = indexs.reduce((prev, pos, index) => {\n", "    return {\n", "      ...prev,\n", "      [pos]: index * 10\n", "    }\n", "  }, {})\n", "  return terminals.map(t => {\n", "    return {\n", "      ...t,\n", "      position: indexMap[t.position]\n", "    }\n", "  })\n", "}\n", "\n", "const getPrevTerminal = terminals => {\n", "  return last(terminals)\n", "}\n", "\n", "const { prefix } = window\n", "const e = prefix('ssh')\n", "const m = prefix('menu')\n", "\n", "export default class SessionWrapper extends Component {\n", "  constructor (props) {\n", "    super(props)\n", "    const id = uid()\n", "    const {\n", "      terminals = [\n", "        {\n", "          id,\n", "          position: 0\n", "        }\n", "      ]\n", "    } = props.tab\n", "    const activeSplitId = terminals[0].id\n", "    this.state = {\n", "      pid: null,\n", "\n", "file path: src/client/components/session/sessions.jsx\n", "          'appPath',\n", "          'topMenuHeight',\n", "          'rightSidebarWidth',\n", "          'leftSidebarWidth',\n", "          'pinned',\n", "          'openedSideBar'\n", "        ]),\n", "        config,\n", "        ...pick(this, [\n", "          'onChangeTabId',\n", "          'onDuplicateTab',\n", "          'reloadTab',\n", "          'delTab',\n", "          'addTab',\n", "          'editTab'\n", "        ])\n", "      }\n", "      if (type === terminalWebType) {\n", "        const webProps = {\n", "          tab\n", "        }\n", "        return (\n", "          <div className={cls} key={id}>\n", "            <WebSession\n", "              {...webProps}\n", "            />\n", "          </div>\n", "        )\n", "      }\n", "      return (\n", "        <div className={cls} key={id}>\n", "          <Session\n", "            {...sessProps}\n", "          />\n", "        </div>\n", "      )\n", "    })\n", "  }\n", "\n", "  renderTabs = () => {\n", "\n", "file path: src/client/components/session/sessions.jsx\n", "import { Component } from '../common/react-subx'\n", "import Session from './session'\n", "import WebSession from '../web/web-session.jsx'\n", "import { findIndex, pick } from 'lodash-es'\n", "import classNames from 'classnames'\n", "import generate from '../../common/uid'\n", "import copy from 'json-deep-copy'\n", "import wait from '../../common/wait'\n", "import Tabs from '../tabs'\n", "import {\n", "  commonActions,\n", "  tabActions,\n", "  termInitId,\n", "  paneMap,\n", "  statusMap,\n", "  terminalWebType\n", "} from '../../common/constants'\n", "import newTerm, { updateCount } from '../../common/new-terminal'\n", "import postMsg from '../../common/post-msg'\n", "import TermSearch from '../terminal/term-search'\n", "import Footer from '../footer/footer-entry'\n", "import QuickCommandsFooterBox from '../quick-commands/quick-commands-box'\n", "import LogoElem from '../common/logo-elem'\n", "import { Button } from 'antd'\n", "import toSimpleObj from '../../common/to-simple-obj'\n", "import { shortcutExtend } from '../shortcuts/shortcut-handler.js'\n", "\n", "const { prefix } = window\n", "const e = prefix('tabs')\n", "const c = prefix('control')\n", "\n", "class Sessions extends Component {\n", "  state = {\n", "    tabs: [],\n", "    currentTabId: ''\n", "  }\n", "\n", "  componentDidMount () {\n", "    this.watch()\n", "    this.initShortcuts()\n", "\n", "file path: src/client/components/session/sessions.jsx\n", "        </Button>\n", "        <div className='pd3'>\n", "          <LogoElem />\n", "        </div>\n", "      </div>\n", "    )\n", "  }\n", "\n", "  renderSessions () {\n", "    const {\n", "      store, config\n", "    } = this.props\n", "    const {\n", "      currentTabId,\n", "      tabs\n", "    } = this.state\n", "    if (!tabs.length) {\n", "      return this.renderNoSession()\n", "    }\n", "    return tabs.map((tab) => {\n", "      const { id, type } = tab\n", "      const cls = classNames(\n", "        `session-wrap session-${id}`,\n", "        {\n", "          'session-current': id === currentTabId\n", "        }\n", "      )\n", "      const sessProps = {\n", "        currentTabId,\n", "        tab: to<PERSON><PERSON><PERSON><PERSON>bj(tab),\n", "        ...pick(store, [\n", "          'resolutions',\n", "          'hideDelKeyTip',\n", "          'fileOperation',\n", "          'file',\n", "          'height',\n", "          'width',\n", "          'activeTerminalId',\n", "          'pinnedQuickCommandBar',\n", "          'tabsHeight',\n", "\n", "file path: src/client/components/session/sessions.jsx\n", "    const ptp = openedSideBar\n", "      ? {\n", "          className: 'sessions',\n", "          style: {\n", "            marginLeft: `${w}px`\n", "          }\n", "        }\n", "      : {\n", "          className: 'sessions'\n", "        }\n", "    return (\n", "      <div\n", "        {...ptp}\n", "        key='main-sess'\n", "      >\n", "        {this.renderSessions()}\n", "      </div>\n", "    )\n", "  }\n", "\n", "  render () {\n", "    const { store, config } = this.props\n", "    const currentTab = this.getCurrentTab()\n", "    const termProps = {\n", "      currentTab,\n", "      store,\n", "      config\n", "    }\n", "    return [\n", "      this.renderTabs(),\n", "      this.renderSessionsWrap(),\n", "      <TermSearch\n", "        key='TermSearch'\n", "        {...termProps}\n", "      />,\n", "      <QuickCommandsFooterBox\n", "        key='QuickCommandsFooterBox'\n", "        store={store}\n", "      />,\n", "      <Footer\n", "\n", "file path: src/client/components/session/session.jsx\n", "    return (\n", "      <Tooltip title={title} placement='bottomLeft'>\n", "        <FullscreenOutlined\n", "          className='mg1r icon-info font16 iblock pointer spliter term-fullscreen-control1'\n", "          onClick={this.handleFullscreen}\n", "        />\n", "      </Tooltip>\n", "    )\n", "  }\n", "\n", "  renderDelTip = (isSsh) => {\n", "    if (!isSsh || this.props.hideDelKeyTip || !this.state.delKeyPressed) {\n", "      return null\n", "    }\n", "    return (\n", "      <div className='type-tab'>\n", "        <span className='mg1r'>Try <b>Shift + Backspace</b>?</span>\n", "        <CloseOutlined\n", "          onClick={this.handleDismissDelKeyTip}\n", "          className='pointer'\n", "        />\n", "      </div>\n", "    )\n", "  }\n", "\n", "  handleSplitVert = () => {\n", "    window.store.splitTerminalSftp = 'vertical'\n", "  }\n", "\n", "  handleSplitHori = () => {\n", "    window.store.splitTerminalSftp = 'horizontal'\n", "  }\n", "\n", "  renderToggles = (shouldHaveSpliter) => {\n", "    if (!shouldHaveSpliter) {\n", "      return null\n", "    }\n", "    return [\n", "      <BorderVerticleOutlined\n", "        className='pointer iblock spliter-ver'\n", "\n", "file path: src/client/components/session/session.jsx\n", "      ? windowWidth / len\n", "      : windowWidth\n", "    const height = isHori\n", "      ? heightAll\n", "      : heightAll / len\n", "    const left = isHori\n", "      ? index * width\n", "      : 0\n", "    const top = isHori\n", "      ? 0\n", "      : index * height\n", "    return {\n", "      height,\n", "      width,\n", "      left,\n", "      top\n", "    }\n", "  }\n", "\n", "  getWidth = () => {\n", "    const {\n", "      infoPanelPinned,\n", "      showInfo\n", "    } = this.state\n", "    const { rightSidebarWidth, width, leftSidebarWidth, pinned, openedSideBar } = this.props\n", "    const rt = infoPanelPinned && showInfo ? rightSidebarWidth : 0\n", "    const lt = pinned && openedSideBar ? leftSidebarWidth : 0\n", "    return width - rt - lt - 42\n", "  }\n", "\n", "  getWidthSftp = () => {\n", "    const { width, leftSidebarWidth, pinned, openedSideBar } = this.props\n", "    const lt = pinned && openedSideBar ? leftSidebarWidth : 0\n", "    return width - lt - 42\n", "  }\n", "\n", "  renderTerminals = () => {\n", "    const {\n", "      terminals,\n", "      activeSplitId,\n", "\n", "file path: src/client/components/session/session.jsx\n", "      enableSftp: false,\n", "      cwd: '',\n", "      sftpPathFollowSsh: !!props.config.sftpPathFollowSsh,\n", "      splitDirection: terminalSplitDirectionMap.horizontal,\n", "      activeSplitId,\n", "      infoPanelPinned: false,\n", "      key: Math.random(),\n", "      sessionOptions: null,\n", "      sessionId: generate(),\n", "      terminals: terminals.slice(0, 1),\n", "      del<PERSON>eyPressed: false,\n", "      showInfo: false,\n", "      infoPanelProps: {}\n", "    }\n", "  }\n", "\n", "  componentDidMount () {\n", "    this.updateTab()\n", "    // this.initEvent()\n", "  }\n", "\n", "  componentWillUnmount () {\n", "    clearTimeout(this.backspaceKeyPressedTimer)\n", "  }\n", "\n", "  onDelKeyPressed = () => {\n", "    this.setState({\n", "      <PERSON><PERSON><PERSON><PERSON><PERSON>: true\n", "    })\n", "    this.backspaceKeyPressedTimer = setTimeout(() => {\n", "      this.setState({\n", "        <PERSON><PERSON>eyPressed: false\n", "      })\n", "    }, 5000)\n", "  }\n", "\n", "  handleChangeDelMode = (backspaceMode) => {\n", "    this.setState({\n", "      backspaceMode\n", "    })\n", "\n", "file path: src/client/components/session/session.jsx\n", "      splitDirection,\n", "      sessionOptions,\n", "      sessionId,\n", "      sftpPathFollowSsh\n", "    } = this.state\n", "    const {\n", "      pane, type\n", "    } = this.props.tab\n", "    if (type === terminalRdpType) {\n", "      const rdpProps = {\n", "        tab: this.props.tab,\n", "        sessionId,\n", "        ...pick(this.props, [\n", "          'resolutions',\n", "          'height',\n", "          'width',\n", "          'tabsHeight',\n", "          'leftSidebarWidth',\n", "          'pinned',\n", "          'openedSideBar',\n", "          'delTab',\n", "          'config',\n", "          'editTab'\n", "        ]),\n", "        ...pick(\n", "          this,\n", "          [\n", "            'setSessionState'\n", "          ])\n", "      }\n", "      return (\n", "        <RdpSession\n", "          {...rdpProps}\n", "        />\n", "      )\n", "    }\n", "    const cls = pane === paneMap.terminal\n", "      ? 'terms-box'\n", "      : 'terms-box hide'\n", "    const height = this.computeHeight()\n", "\n", "file path: src/client/components/session/session.jsx\n", "      height,\n", "      sessionId,\n", "      pane,\n", "      ...this.props,\n", "      width: this.getWidthSftp()\n", "    }\n", "    return (\n", "      <div className={cls}>\n", "        <Sftp\n", "          {...exts}\n", "        />\n", "      </div>\n", "    )\n", "  }\n", "\n", "  handleFullscreen = () => {\n", "    window.store.toggleTermFullscreen(true)\n", "  }\n", "\n", "  handleOpenSearch = () => {\n", "    postMessage({\n", "      action: terminalActions.openTerminalSearch,\n", "      activeSplitId: this.state.activeSplitId\n", "    })\n", "  }\n", "\n", "  renderSearchIcon = () => {\n", "    const title = e('search')\n", "    return (\n", "      <Tooltip title={title} placement='bottomLeft'>\n", "        <SearchOutlined\n", "          className='mg1r icon-info font16 iblock pointer spliter'\n", "          onClick={this.handleOpenSearch}\n", "        />\n", "      </Tooltip>\n", "    )\n", "  }\n", "\n", "  fullscreenIcon = () => {\n", "    const title = e('fullscreen')\n", "\n", "file path: src/client/components/session/session.jsx\n", "              return (\n", "                <Term\n", "                  key={t.id}\n", "                  logName={logName}\n", "                  sessionId={sessionId}\n", "                  terminalIndex={index}\n", "                  sessionOptions={sessionOptions}\n", "                  {...pops}\n", "                />\n", "              )\n", "            })\n", "          }\n", "        </ResizeWrap>\n", "      </div>\n", "    )\n", "  }\n", "\n", "  renderSftp = () => {\n", "    const {\n", "      sessionOptions,\n", "      sessionId,\n", "      pid,\n", "      enableSftp,\n", "      sftpPathFollowSsh,\n", "      cwd\n", "    } = this.state\n", "    const { pane, type } = this.props.tab\n", "    if (type === terminalRdpType) {\n", "      return null\n", "    }\n", "    const height = this.computeHeight()\n", "    const cls = pane === paneMap.terminal\n", "      ? 'hide'\n", "      : ''\n", "    const exts = {\n", "      sftpPathFollowSsh,\n", "      cwd,\n", "      pid,\n", "      enableSftp,\n", "      sessionOptions,\n", "\n", "file path: src/client/components/session/session.jsx\n", "    const { tab } = this.props\n", "    const width = this.getWidth()\n", "    const themeConfig = copy(window.store.getThemeConfig())\n", "    return (\n", "      <div\n", "        className={cls}\n", "        style={{\n", "          width,\n", "          height\n", "        }}\n", "      >\n", "        <ResizeWrap\n", "          direction={splitDirection}\n", "          tab={tab}\n", "        >\n", "          {\n", "            terminals.map((t, index) => {\n", "              const logName = safeName(`${tab.title ? tab.title + '_' : ''}${tab.host ? tab.host + '_' : ''}${t.id}`)\n", "              const pops = {\n", "                ...this.props,\n", "                ...t,\n", "                activeSplitId,\n", "                sftpPathFollowSsh,\n", "                themeConfig,\n", "                pane,\n", "                ...pick(\n", "                  this,\n", "                  [\n", "                    'setActive',\n", "                    'handleSplit',\n", "                    'delSplit',\n", "                    'setSessionState',\n", "                    'handleShowInfo',\n", "                    'onChangePane',\n", "                    'hideInfoPanel',\n", "                    'setCwd',\n", "                    'onDelKeyPressed'\n", "                  ]),\n", "                ...this.computePosition(t.position / 10)\n", "              }\n", "\n", "file path: src/client/components/session/session.jsx\n", "/**\n", " * terminal/sftp wrapper\n", " */\n", "import { Component } from 'react'\n", "import Term from '../terminal'\n", "import Sftp from '../sftp/sftp-entry'\n", "import RdpSession from '../rdp/rdp-session'\n", "import {\n", "  BorderVerticle<PERSON>utlined,\n", "  BorderHorizontalOutlined,\n", "  CloseSquareFilled,\n", "  SearchOutlined,\n", "  FullscreenOutlined,\n", "  PaperClipOutlined,\n", "  CloseOutlined\n", "} from '@ant-design/icons'\n", "import {\n", "  <PERSON><PERSON><PERSON>\n", "} from 'antd'\n", "import { last, findIndex, pick } from 'lodash-es'\n", "import generate from '../../common/uid'\n", "import copy from 'json-deep-copy'\n", "import classnames from 'classnames'\n", "import {\n", "  quickCommandBoxHeight,\n", "  terminalSplitDirectionMap,\n", "  termControlHeight,\n", "  paneMap,\n", "  footerHeight,\n", "  terminalActions,\n", "  connectionMap,\n", "  terminalRdpType\n", "} from '../../common/constants'\n", "import ResizeWrap from '../common/resize-wrap'\n", "import safeName from '../../common/safe-name'\n", "import TerminalInfoContent from '../terminal-info/content'\n", "import uid from '../../common/id-with-stamp'\n", "import postMessage from '../../common/post-msg'\n", "import './session.styl'\n", "import classNames from 'classnames'\n", "\n", "file path: src/client/components/session/session.jsx\n", "                }\n", "                <Tooltip\n", "                  title={`${e('split')}`}\n", "                  placement='bottomLeft'\n", "                >\n", "                  <Icon1\n", "                    className={cls1}\n", "                    onClick={this.handleSplit}\n", "                  />\n", "                </Tooltip>\n", "                <Tooltip\n", "                  title={e('changeDirection')}\n", "                  placement='bottomLeft'\n", "                >\n", "                  <Icon2\n", "                    className={cls2}\n", "                    onClick={this.handleChangeDirection}\n", "                  />\n", "                </Tooltip>\n", "              </div>\n", "              )\n", "            : null\n", "        }\n", "      </div>\n", "    )\n", "  }\n", "\n", "  renderContent = () => {\n", "    const { splitTerminalSftp } = this.props\n", "    const cls = classNames(\n", "      'session-split-wrap',\n", "      splitTerminalSftp\n", "    )\n", "    return (\n", "      <div className={cls}>\n", "      \n", "      </div>\n", "    )\n", "  }\n", "\n", "\n", "\n", "# Retrieved Chunks Finish.\n", "# Signature Chunks Start:\n", "from: src/client/components/session/session.jsx/SessionWrapper\n", "const exts = {\n", "      sftpPathFollowSsh,\n", "      cwd,\n", "      pid,\n", "      enableSftp,\n", "      sessionOptions,\n", "      height,\n", "      sessionId,\n", "      pane,\n", "      ...this.props,\n", "      width: this.getWidthSftp()\n", " [...]\n", "\n", "from: src/client/components/session/session.jsx/SessionWrapper\n", "renderTerminals = () => {\n", "    const {\n", "      terminals,\n", "      activeSplitId,\n", "      splitDirection,\n", "      sessionOptions,\n", "      sessionId,\n", "      sftpPathFollowSsh\n", "    } = this.state\n", "    const {\n", "      pa[...]\n", "\n", "from: src/client/components/terminal/index.jsx\n", "class Term extends Component {\n", "attributes: terminalConfigProps = ...; initAttachAddon = ...; getValue = ...; checkConfigChange = ...; const { name, type } = k; const prev = ...; const curr = ...; timers = {}; initEvt = ...; const { id } = ...; const dom = ...; zoom = ...; const { term } = this; isActiveTerminal = ...; clearShortcut = ...; copyShortcut = ...; const sel = ...; searchShortcut = ...; pasteSelectedShortcut = ...; pasteShortcut = ...; showNormalBufferShortcut = ...; handleEvent = ...; const {\n", "      keyword,\n", "      options,\n", "      action,\n", "      encode,\n", "      saveTerminalLogToFile,\n", "      addTimeStampToTermLog,\n", "      type,\n", "      cmd,\n", "      activeSplitId,\n", "      pid,\n", "      toAll,\n", "      inputOnly,\n", "      zoomValue\n", "    } = ...; const { id: propSplitId } = ...; const { pid: statePid } = ...; onDrop = ...; const files = ...; onSelection = ...; copySelectionToClipboard = ...; const txt = ...; tryInsertSelected = ...; webLinkHandler = ...; onzmodemRetract = ...; writeBanner = ...; onReceiveZmodemSession = ...; const savePath = ...[...]\n", "methods: componentDidMount; componentDidUpdate; componentWillUnmount; parse; render\n", "  constructor (props) {}\n", "}\n", "\n", "from: src/client/components/session/sessions.jsx/Sessions\n", "renderNoSession = () => {\n", "    const props = {\n", "      style: {\n", "        height: this.props.store.height + 'px'\n", "      }\n", "    }\n", "    return (\n", "      <div className='no-sessions electerm-logo-bg' {...props}>\n", " [...]\n", "\n", "from: src/client/components/session/session.jsx/SessionWrapper\n", "renderControl = () => {\n", "    const { splitDirection, terminals, sftpPathFollowSsh } = this.state\n", "    const { props } = this\n", "    const { pane, enableSsh, type } = props.tab\n", "    if (type === terminalRdpT[...]\n", "\n", "from: src/client/components/session/session.jsx\n", "export default class SessionWrapper extends Component {\n", "attributes: onDelKeyPressed = ...; handleChangeDelMode = ...; handleDismissDelKeyTip = ...; setCwd = ...; handleShowInfo = ...; toggleInfoPinned = ...; toggleCheckSftpPathFollowSsh = ...; hideInfoPanel = ...; computeHeight = ...; const {\n", "      <PERSON><PERSON><PERSON><PERSON>CommandBar,\n", "      tabsHeight\n", "    } = ...; editTab = ...; const {\n", "      tab,\n", "      editTab\n", "    } = ...; onChangePane = ...; const update = ...; setSessionState = ...; handleSplit = ...; let terminals = ...; let index = ...; updateTab = ...; delSplit = ...; const { terminals } = ...; let newTerms = ...; const newActiveId = ...; handleChangeDirection = ...; const { splitDirection } = ...; setActive = ...; const up = ...; computePosition = ...; const len = ...; const windowWidth = ...; const isHori = ...; const heightAll = ...; const width = ...; const height = ...; const left = ...; const top = ...; getWidth = ...; const {\n", "      infoPanelPinned,\n", "      showInfo\n", "    } = ...; const { rightSidebarWidth, width, leftSidebarWidth, pinned, openedSideBar } = [...]\n", "methods: componentDidMount; componentWillUnmount; render\n", "  constructor (props) {}\n", "}\n", "\n", "from: src/client/components/session/session.jsx/SessionWrapper\n", "renderContent = () => {\n", "    const { splitTerminalSftp } = this.props\n", "    const cls = classNames(\n", "      'session-split-wrap',\n", "      splitTerminalSftp\n", "    )\n", "    return (\n", "      <div className={cls}>\n", "    [...]\n", "# Signature Chunks Finish.\n", "<file_sep>file path: src/client/components/session/session.jsx\n", "<fim_prefix>return null\n", "    }\n", "    return [\n", "      <BorderVerticleOutlined\n", "        className='pointer iblock spliter-ver'\n", "        onClick={this.handleSplitVert}\n", "        key='split1'\n", "      />,\n", "      <BorderHorizontalOutlined\n", "        className='pointer iblock spliter-hori mg1x'\n", "        onClick={this.handleSplitHori}\n", "        key='split2'\n", "      />\n", "    ]\n", "  }\n", "\n", "  renderControl = () => {\n", "    const { splitDirection, terminals, sftpPathFollowSsh } = this.state\n", "    const { props } = this\n", "    const { pane, enableSsh, type } = props.tab\n", "    if (type === terminalRdpType) {\n", "      return null\n", "    }\n", "    const termType = props.tab?.type\n", "    const isSsh = props.tab.authType\n", "    const isLocal = !isSsh && (termType === connectionMap.local || !termType)\n", "    const isHori = splitDirection === terminalSplitDirectionMap.horizontal\n", "    const cls1 = 'mg1r icon-split pointer iblock spliter'\n", "    const cls2 = 'icon-direction pointer iblock spliter'\n", "    const Icon1 = isHori\n", "      ? BorderHorizontalOutlined\n", "      : BorderVerticleOutlined\n", "    const Icon2 = !isHori\n", "      ? BorderHorizontalOutlined\n", "      : BorderVerticleOutlined\n", "    const hide = terminals.length < 2\n", "    const types = [\n", "      paneMap.terminal,\n", "      paneMap.fileManager\n", "    ]\n", "    const controls = [\n", "      isSsh ? paneMap.ssh : paneMap.terminal\n", "    ]\n", "    if (isSsh || isLocal) {\n", "      controls.push(isSsh ? paneMap.sftp : paneMap.fileManager)\n", "    }\n", "    const checkTxt = e('sftpPathFollowSsh') + ' [Beta]'\n", "    const checkProps = {\n", "      onClick: this.toggleCheckSftpPathFollowSsh,\n", "      className: classnames(\n", "        'sftp-follow-ssh-icon',\n", "        {\n", "          active: sftpPathFollowSsh\n", "        }\n", "      )\n", "    }\n", "    const simpleMapper = {\n", "      [paneMap.terminal]: 'T',\n", "      [paneMap.fileManager]: 'F',\n", "      [paneMap.ssh]: 'T'\n", "    }\n", "    const shouldHaveSpliter = (isSsh && enableSsh) || isLocal\n", "    return (\n", "      <div\n", "        className='terminal-control fix'\n", "      >\n", "        <div className='term-sftp-tabs fleft'>\n", "          {\n", "            controls.map((type, i) => {\n", "              const cls = classnames(\n", "                'type-tab',\n", "                type,\n", "                {\n", "                  active: types[i] === pane\n", "                }\n", "              )\n", "              return (\n", "                <span\n", "                  className={cls}\n", "                  key={type + '_' + i}\n", "                  onClick={() => this.onChangePane(types[i])}\n", "                >\n", "                  <span className='type-tab-txt'>\n", "                    <span className='w500'>{e(type)}</span>\n", "                    <span className='l500'>{simpleMapper[type]}</span>\n", "                    <span className='type-tab-line' />\n", "                  </span>\n", "                </span>\n", "              )\n", "            })\n", "          }\n", "        </div>\n", "        {\n", "          shouldHaveSpliter\n", "            ? (\n", "              <Tooltip title={checkTxt}>\n", "                <span {...checkProps}>\n", "                  <PaperClipOutlined />\n", "                </span>\n", "              </Tooltip>\n", "              )\n", "            : null\n", "        }\n", "        {\n", "          this.renderToggles(shouldHaveSpliter)\n", "        }\n", "        {\n", "          this.renderDelTip(pane === paneMap.terminal)\n", "        }\n", "        {\n", "          pane === paneMap.terminal\n", "            ? (\n", "              <div className='fright term-controls'>\n", "                {this.fullscreenIcon()}\n", "                {this.renderSearchIcon()}\n", "                {\n", "                  hide\n", "                    ? null\n", "                    : (\n", "                      <CloseSquareFilled\n", "                        className='mg1r icon-trash font16 iblock pointer spliter'\n", "                        onClick={() => this.delSplit()}\n", "                        title={m('del')}\n", "                      />\n", "                      )\n", "                }\n", "                <Tooltip\n", "                  title={`${e('split')}`}\n", "                  placement='bottomLeft'\n", "                >\n", "                  <Icon1\n", "                    className={cls1}\n", "                    onClick={this.handleSplit}\n", "                  />\n", "                </Tooltip>\n", "                <Tooltip\n", "                  title={e('changeDirection')}\n", "                  placement='bottomLeft'\n", "                >\n", "                  <Icon2\n", "                    className={cls2}\n", "                    onClick={this.handleChangeDirection}\n", "                  />\n", "                </Tooltip>\n", "              </div>\n", "              )\n", "            : null\n", "        }\n", "      </div>\n", "    )\n", "  }\n", "\n", "  renderContent = () => {\n", "    const { splitTerminalSftp } = this.props\n", "    const cls = classNames(\n", "      'session-split-wrap',\n", "      splitTerminalSftp\n", "    )\n", "    return (\n", "      <div className={cls}>\n", "        <div className='<fim_suffix>'>\n", "        </div>\n", "      </div>\n", "    )\n", "  }\n", "\n", "  render () {\n", "    const {\n", "      splitDirection,\n", "      infoPanelProps,\n", "      showInfo,\n", "      infoPanelPinned\n", "    } = this.state\n", "    const { pane } = this.props.tab\n", "    const infoProps = {\n", "      infoPanelPinned,\n", "      ...pick(this.props.config, ['host', 'port', 'saveTerminalLogToFile', 'terminalInfos']),\n", "      ...infoPanelProps,\n", "      appPath: this.props.appPath,\n", "      rightSidebarWidth: this.props.rightSidebarWidth,\n", "      showInfo,\n", "      tabsHeight: this.props.tabsHeight,\n", "      topMenuHeight: this.props.topMenuHeight,\n", "      toggleInfoPinned: this.toggleInfoPinned,\n", "      hideInfoPanel: this.hideInfoPanel\n", "    }\n", "    const cls = classnames(\n", "      'term-sftp-box',\n", "      pane,\n", "      splitDirection,\n", "      {\n", "        'is-transporting': this.props.tab.isTransporting\n", "      },\n", "      {\n", "        'disable-ssh': this.props.tab.enableSsh === false\n", "      }\n", "    )\n", "    return (\n", "      <div\n", "        className={cls}\n", "        id={`is-${this.props.tab.id}`}\n", "      >\n", "        {this.renderControl()}\n", "        {this.renderTerminals()}\n", "        {this.renderSftp()}\n", "        <TerminalInfoContent\n", "          {...infoProps}\n", "        />\n", "      </div>\n", "    )\n", "  }\n", "}\n", "<fim_middle>session-split-term<|skip|><|skip|><|pause|>\n", "          {this.renderTerminals()}<|endoftext|>\n"]}], "source": ["from experimental.dxy.rag.rlhf.shared_lib import extract_tokens_before_stop_tokens\n", "\n", "tokens = dataset[6].tolist()\n", "tokens = extract_tokens_before_stop_tokens(tokens, [special_tokens.padding])\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import megatron.data.indexed_dataset as indexed_dataset\n", "from base.tokenizers import Llama3BaseTokenizer\n", "\n", "# datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/validation_dataset\"\n", "datapath = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden_16k-llama3/dataset\"\n", "\n", "dataset = indexed_dataset.MMapIndexedDataset(datapath, skip_warmup=True)\n", "print(f\"The dataset has {len(dataset)} records.\")\n", "tokenizer = Llama3BaseTokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "\n", "index = 3\n", "# Find the index of the first pad token.\n", "tokens = dataset[index].tolist()\n", "tokens = tokens[: tokens.index(special_tokens.padding)]\n", "print(tokenizer.de<PERSON><PERSON>ze(tokens))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}