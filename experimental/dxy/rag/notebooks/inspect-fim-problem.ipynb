{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 2849 files in the repository.\n"]}], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "import random\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "# augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "augment_root = pathlib.Path(\"/home/<USER>/src/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    if language is None:\n", "        continue\n", "    # print(str(relative_path))\n", "    # if \"research/data/tests/collection/test_stackexchange.py\" not in str(relative_path):\n", "    #     continue\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")\n", "\n", "rng = random.Random(45)\n", "rng.shuffle(repo)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1372 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", ")\n", "from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_fim_samples_from_repo_with_multiple_samplers,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from research.core.artifacts import collect_artifacts\n", "from research.fim.fim_sampling_experimental import (\n", "    DefaultUnitTestCorruptionNodesPicker,\n", "    LiteralUnitTestCorruptionNodesPicker,\n", ")\n", "\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\", \"c\", \"c++\", \"rust\", \"java\"]\n", "SAMPLE_LANGUAGES = [\"python\", \"typescript\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=50,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=104,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")\n", "\n", "\n", "node_picker = DefaultUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.4,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.3,\n", ")\n", "sampler = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker,\n", ")\n", "\n", "node_picker_literal = LiteralUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.5,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.0,\n", "    edit_similarity_threshold=0.3,\n", "    max_num_lines_per_node=10,\n", "    max_num_char_per_node=400,\n", ")\n", "sampler_literal = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker_literal,\n", ")\n", "fim_problems = generate_fim_samples_from_repo_with_multiple_samplers(\n", "    repo,\n", "    config_fim,\n", "    [\n", "        (sampler, node_picker.get_node_weight),\n", "        (sampler_literal, node_picker_literal.get_node_weight),\n", "    ],\n", ")\n", "print(f\"There are {len(fim_problems)} final samples.\")\n", "rng.shuffle(fim_problems)\n", "fim_problems_for_unittest = fim_problems"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# # Serialize the documents and fim_problems\n", "# import pathlib\n", "# import pickle\n", "\n", "# folder = pathlib.Path(\"/mnt/efs/augment/data/eval/fim_problems\")\n", "# all_docs = list(doc_by_id.values())\n", "\n", "# pickle.dump(\n", "#     {\n", "#         \"all_docs\": all_docs,\n", "#         \"fim_problems\": fim_problems,\n", "#         \"tag\": \"unittest\",\n", "#     },\n", "#     (folder / \"augment-unittest-3cfc5cc1d.pkl\").open(\"wb\"),\n", "# )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1140 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", ")\n", "from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_fim_samples_from_repo_with_multiple_samplers,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from research.core.artifacts import collect_artifacts\n", "from research.fim.fim_sampling_experimental import (\n", "    DefaultUnitTestCorruptionNodesPicker,\n", "    LiteralUnitTestCorruptionNodesPicker,\n", ")\n", "\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\", \"c\", \"c++\", \"rust\", \"java\"]\n", "SAMPLE_LANGUAGES = [\"python\", \"typescript\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=50,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=104,\n", ")\n", "\n", "sampler = CSTFimSampler()\n", "\n", "fim_problems = generate_fim_samples_from_repo_with_multiple_samplers(\n", "    repo,\n", "    config_fim,\n", "    [\n", "        (sampler, None),\n", "    ],\n", ")\n", "print(f\"There are {len(fim_problems)} final samples.\")\n", "rng.shuffle(fim_problems)\n", "\n", "fim_problems_basic = fim_problems"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 99 samples with no middle spans.\n"]}], "source": ["count = 0\n", "for fim_problem in fim_problems_basic + fim_problems_for_unittest:\n", "    if len(fim_problem.middle_spans) == 0:\n", "        count += 1\n", "print(f\"There are {count} samples with no middle spans.\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1145 basic samples.\n", "There are 1374 unittest samples.\n"]}], "source": ["import pickle\n", "from research.fim.fim_sampling import FimProblem\n", "print(f\"There are {len(fim_problems_basic)} basic samples.\")\n", "print(f\"There are {len(fim_problems_for_unittest)} unittest samples.\")\n", "\n", "fim_problems_w_tag: list[tuple[FimProblem, str]] = [\n", "    (problem, \"basic\") for problem in fim_problems_basic\n", "] + [\n", "    (problem, \"unittest\") for problem in fim_problems_for_unittest\n", "]\n", "\n", "folder = pathlib.Path(\"/mnt/efs/augment/data/eval/fim_problems\")\n", "all_docs = list(doc_by_id.values())\n", "\n", "pickle.dump(\n", "    {\n", "        \"all_docs\": all_docs,\n", "        \"fim_problems_w_tag\": fim_problems_w_tag,\n", "    },\n", "    (folder / \"augment-ae6ecc4f0.pkl\").open(\"wb\"),\n", ")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:tasks.fimeval:Using default repo list: ('augment-ae6ecc4f0.pkl',)\n", "INFO:tasks.fimeval:Loaded the 0-th repo: augment-ae6ecc4f0.pkl with 2519 problems\n"]}, {"name": "stdout", "output_type": "stream", "text": ["This task has 2519 examples\n"]}], "source": ["from research.eval.harness.tasks.fimeval import FIMEval\n", "\n", "task = FIMEval()\n", "print(f\"This task has {len(task)} examples\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["docs, fim_problems_w_tag = task._repos[0]\n", "fim_problem, tag = fim_problems_w_tag[246]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FimProblem(file=clients/vscode/src/__tests__/completions/manual-completion.test.ts, middle=8889:8931):\n", "-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~Prefix-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~\n", "[...]e<CompletionResult> => {\n", "                publishTextDocumentChange(new TextDocumentChangeEvent(kit.document, [], undefined));\n", "                return kit.apiCompletionResult;\n", "            }\n", "        );\n", "\n", "        await kit.manualCompletion.trigger();\n", "        expect(kit.activeEditor!.edit).toHaveBeenCalledTimes(1);\n", "        expect(kit.editBuilder.insert).toHaveBeenCalledTimes(1);\n", "    });\n", "\n", "    test(\"trigger and complete for text document change in output panel\", async () => {\n", "        kit.apiServer.complete = jest.fn(\n", "            async (\n", "                _requestId: string,\n", "                _prefix: string,\n", "                _suffix: string,\n", "                _path: string,\n", "                _blobName: string | undefined,\n", "                _completionLocation: any,\n", "                _language: string,\n", "                _context: any\n", "            ): Promise<CompletionResult> => {\n", "                publishTextDocumentChange(\n", "                    new TextDocumentChangeEvent(\n", "                        new TextDocument\n", "-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~Middle (arguments, n_middles=1)-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~\n", "(Uri.parse(\"output:///example/file\"), \"\"),\n", "-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~Suffix-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~-~\n", "\n", "                        [\n", "                            TextDocumentContentChangeEvent.forInsert(\n", "                                kit.document,\n", "                                kit.document.positionAt(0),\n", "                                0,\n", "                                \"Hello\"\n", "                            ),\n", "                        ],\n", "                        undefined\n", "                    )\n", "                );\n", "                return kit.apiCompletionResult;\n", "            }\n", "        );\n", "\n", "        await kit.manualCompletion.trigger();\n", "        expect(kit.activeEditor!.edit).toHaveBeenCalledTimes(1);\n", "        expect(kit.editBuilder.insert).toHaveBeenCalledTimes(1);\n", "    });\n", "\n", "    test(\"insert and select completion\", async () => {\n", "        await kit.manualCompletion.trigger();\n", "        expect(kit.activeEditor!.edit).toHaveBeenCalledTimes(1);\n", "        expect(kit.editBuilder.insert).toHaveBeenCalledTimes(1);\n", "        expect(kit.editBuilder.insert.mock.calls[0]).toEqual([\n", "            kit.position,\n", "          [...]\n"]}], "source": ["print(fim_problem.show())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}