{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 2871 files in the repository.\n"]}], "source": ["import typing\n", "import pathlib\n", "import hashlib\n", "import random\n", "from research.data.rag.rogue_stages import repo_file_key_type\n", "from base.static_analysis.common import guess_lang_from_fp\n", "from research.core.types import Document\n", "\n", "augment_root = pathlib.Path(\"/home/<USER>/augment\")\n", "repo: typing.Sequence[typing.Mapping[repo_file_key_type, typing.Any]] = list()\n", "\n", "\n", "def calculate_hexsha(content: str):\n", "    sha1 = hashlib.sha1()\n", "    sha1.update(content.encode(\"utf-8\"))\n", "    return sha1.hexdigest()\n", "\n", "\n", "# traverse all files under augment_root\n", "for file_path in augment_root.rglob(\"*\"):\n", "    relative_path = file_path.relative_to(augment_root)\n", "    if file_path.is_dir():\n", "        continue\n", "    if (\n", "        str(relative_path).startswith(\".\")\n", "        or str(relative_path).startswith(\"third_party\")\n", "        or str(relative_path).startswith(\"augment_research.egg-info\")\n", "        or \"__pycache__\" in str(relative_path)\n", "        or \".yml\" in str(relative_path)\n", "        or \".txt\" in str(relative_path)\n", "        or \".json\" in str(relative_path)\n", "    ):\n", "        continue\n", "    language = guess_lang_from_fp(str(relative_path))\n", "    if str(relative_path).startswith(\"experimental\"):\n", "        continue\n", "    if language is None:\n", "        continue\n", "    # print(str(relative_path))\n", "    # if \"research/data/tests/collection/test_stackexchange.py\" not in str(relative_path):\n", "    #     continue\n", "    content = file_path.read_text()\n", "    data: typing.Mapping[repo_file_key_type, typing.Any] = {\n", "        \"langpart\": language,\n", "        \"max_stars_repo_path\": str(relative_path),\n", "        \"hexsha\": calculate_hexsha(content),\n", "        \"content\": content,\n", "        \"size\": len(content),\n", "    }\n", "    repo.append(data)\n", "\n", "doc_by_id = {\n", "    doc[\"max_stars_repo_path\"]: Document.new(doc[\"content\"], doc[\"max_stars_repo_path\"])\n", "    for doc in repo\n", "}\n", "print(f\"There are {len(repo)} files in the repository.\")\n", "\n", "rng = random.Random(45)\n", "rng.shuffle(repo)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.data.rag.rogue_stages import FIMSampleConfig, RogueRetrievalConfig\n", "from experimental.dxy.rag.exps.rogue_extended import repo_to_fim_samples_wrapper\n", "from experimental.dxy.rag.exps.utils import ALL_LANGUAGES_in_STACK\n", "from research.utils.generate_fim_data import FimDataProcessor\n", "from research.fim.fim_sampling import (\n", "    CSTFimSampler,\n", "    DefaultCorruptionNodesPicker,\n", "    NullCorruptionNodesPicker,\n", ")\n", "\n", "REPO_LANGUAGES = [\"python\", \"go\", \"typescript\"]\n", "SAMPLE_LANGUAGES = [\"python\", \"typescript\"]\n", "RETRIEVAL_LANGUAGES = ALL_LANGUAGES_in_STACK\n", "\n", "\n", "config_fim = FIMSampleConfig(\n", "    fim_version=FimDataProcessor.VERSION,\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    only_keep_unit_test_file=True,\n", "    every_n_lines=50,\n", "    max_problems_per_file=5,\n", "    small_downsampled_probability=0.1,\n", "    small_downsample_char_threshold=1500,\n", "    small_filter_char_threshold=50,\n", "    random_seed=104,\n", ")\n", "config_retrieval = RogueRetrievalConfig(\n", "    repo_languages=REPO_LANGUAGES,\n", "    sample_languages=SAMPLE_LANGUAGES,\n", "    retrieval_languages=RETRIEVAL_LANGUAGES,\n", "    num_retrieved_chunks=0,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1361 final samples.\n"]}], "source": ["from research.data.rag.rogue_stages import (\n", "    generate_fim_samples_from_repo,\n", "    generate_fim_samples_from_repo_with_multiple_configs,\n", "    generate_retrieved_chunks_from_fim,\n", ")\n", "from research.retrieval.null_retrieval_database import NullRetrievalDatabase\n", "from megatron.tokenizer.tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.exps.utils import show_truncated_completion\n", "from base.languages.unit_test_guesser import is_unit_test\n", "from research.core.artifacts import collect_artifacts\n", "from research.fim.fim_sampling_experimental import (\n", "    DefaultUnitTestCorruptionNodesPicker,\n", "    LiteralUnitTestCorruptionNodesPicker,\n", ")\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "node_picker = DefaultUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.4,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.3,\n", ")\n", "sampler = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker,\n", ")\n", "\n", "node_picker_literal = LiteralUnitTestCorruptionNodesPicker(\n", "    no_corruption_expansion_rate=0.2,\n", "    random_corrupt_available_siblings_rate=0.5,\n", "    corrupt_all_available_siblings_rate=0.2,\n", "    possibly_corrupt_ancestor_rate=0.0,\n", "    edit_similarity_threshold=0.3,\n", "    max_num_lines_per_node=10,\n", "    max_num_char_per_node=400,\n", ")\n", "sampler_literal = CSTFimSampler(\n", "    pick_whole_node_rate=1.0,\n", "    pick_extra_spaces_when_whole_node=0.0,\n", "    empty_completion_rate=0.01,\n", "    corruption_nodes_picker=node_picker_literal,\n", ")\n", "# with collect_artifacts() as collector:\n", "samples = list(\n", "        generate_retrieved_chunks_from_fim(\n", "            repo,\n", "            generate_fim_samples_from_repo_with_multiple_configs(\n", "                repo,\n", "                [\n", "                    (config_fim, sampler, node_picker.get_node_weight),\n", "                    (config_fim, sampler_literal, node_picker_literal.get_node_weight),\n", "                ],\n", "            ),\n", "            config=config_retrieval,\n", "            retrieval_database=NullRetrievalDatabase(),\n", "            tokenizer=tokenizer,\n", "        )\n", "    )\n", "    # artifacts = collector.get_artifacts()\n", "    # print(f\"Collect {len(artifacts)} artifacts\")\n", "\n", "print(f\"There are {len(samples)} final samples.\")\n", "rng.shuffle(samples)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/model_server/tests/test_model_server.py\n", "\n", "\u001b[92m    )\n", "\n", "    assert not should_index_path_for_retrieval(\n", "        path=Path(\"other/foo/bar.py\"),\n", "        language_set=language_set,\n", "        include_dirs=include_dirs,\n", "        exclude_dirs=None,\n", "    )\n", "\n", "    \u001b[0m\u001b[47m\u001b[30massert not should_index_path_for_retrieval(\n", "        path=Path(\"research/foo/bar.go\"),\n", "        language_set=language_set,\n", "        include_dirs=None,\n", "        exclude_dirs=None,\n", "    )<|pause|>\n", "\n", "    assert not should_index_path_for_retrieval(\n", "        path=Path(\"research/foo/bar.py\"),\n", "        language_set=language_set,\n", "        include_dirs=None,\n", "        exclude_dirs=[\"research\"],\n", "    )<|pause|>\n", "\n", "    assert not should_index_path_for_retrieval(\n", "        path=Path(\"research/foo/bar.py\"),\n", "        language_set=language_set,\n", "        include_dirs=include_dirs,\n", "        exclude_dirs=[\"research\"],\n", "    )<|endoftext|>\u001b[0m\u001b[94m\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[0], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/fastforward/batching_test.py\n", "\n", "\u001b[92m        round_.sort_and_extend_requests_for_multirequest_attention(\n", "            num_requests=4,\n", "            max_small_request_size=2,\n", "        )\n", "    )\n", "    assert sorted_round.is_multirequest_sorted\n", "    assert not sorted_round.is_padded\n", "    assert len(sorted_round.tokens) == 6\n", "    assert sorted_round.tokens == [1, 2, 3, 4, 5, 6]\n", "    \u001b[0m\u001b[47m\u001b[30massert len(sorted_round.requests_in_round) == 4<|pause|>\n", "    # Manually checking:<|pause|>\n", "    # - Longer request moved to the front, followed by shorter request<|pause|>\n", "    # - Followed by two dummy requests<|pause|>\n", "    reqs = sorted_round.requests_in_round<|pause|>\n", "    assert reqs[0].num_tokens == 4<|pause|>\n", "    assert reqs[0].cache_idx == 2<|endoftext|>\u001b[0m\u001b[94m\n", "    assert reqs[0].round_start_idx == 0\n", "    assert reqs[1].num_tokens == 2\n", "    assert reqs[1].cache_idx == 1\n", "    assert reqs[1].round_start_idx == 4\n", "    for i in [2, 3]:\n", "        assert reqs[i].num_tokens == 0\n", "        assert reqs[i].cache_idx == 0\n", "        assert reqs[i].round_start_idx == 6\n", "    assert old_to_new_idxs == [1, 0]\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[1], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/content_manager/test/content_manager_test.py\n", "\n", "\u001b[92m    (new_blob_name, existed) = content_manager_client.upload(\n", "        content, path=small_file.name, request_context=request_context\n", "    )\n", "    assert not existed\n", "    assert new_blob_name == blob_name\n", "\n", "    response = content_manager_client.find_missing(\n", "        [blob_name], request_context=request_context\n", "    )\n", "    \u001b[0m\u001b[47m\u001b[30massert blob_name not in response\n", "\n", "    response = content_manager_client.find_missing(\n", "        [blob_name], request_context=request_context\n", "    )\n", "    assert blob_name not in response<|endoftext|>\u001b[0m\u001b[94m\n", "\n", "\n", "def test_upload_twice(\n", "    content_manager_client: ContentManagerClient,\n", "    small_file: Path,\n", "    request_context: RequestContext,\n", "):\n", "    \"\"\"Tests that the same blob can be uploaded twice.\"\"\"\n", "    content = small_file.read_bytes()\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[2], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/content_manager/client/content_cache_test.py\n", "\n", "\u001b[92m    cache.set_insert_listener(stats.on_insert)\n", "    cache.set_lookup_listener(stats.on_lookup)\n", "\n", "    # 1. Get a key.\n", "    stats.reset()\n", "    assert cache.get(\n", "        [(\"123\", <PERSON><PERSON><PERSON>(\"key1\"))], RequestContext.create(), auth_info\n", "    ) == [1]\n", "    assert stats.lookup_stats is not None\n", "    \u001b[0m\u001b[47m\u001b[30massert stats.lookup_stats.hits_count == 0<|pause|>\n", "    assert stats.lookup_stats.misses_count == 1<|pause|>\n", "    assert stats.insertion_stats is not None<|pause|>\n", "    assert stats.insertion_stats.insertion_count == 1<|pause|>\n", "    assert stats.insertion_stats.skip_count == 0<|endoftext|>\u001b[0m\u001b[94m\n", "    assert stats.insertion_stats.eviction_count == 0\n", "    # NOTE(arun): Since Python 3.11 there are some memory optimization which mean we\n", "    # use less memory.\n", "    assert stats.insertion_stats.cache_size <= 1 * ENTRY_SIZE\n", "    assert stats.insertion_stats.entries == 1\n", "\n", "    # 2. Try to get the key again.\n", "    stats.reset()\n", "    assert cache.get(\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[3], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/completion_host/single_model_server/retrieval_collector_test.py\n", "\n", "\u001b[92m):\n", "    return list(\n", "        modified_chunks_filter(\n", "            chunks_to_filter, completion_pb2.RecencyInfo(recent_changes=chunks_to_keep)\n", "        )\n", "    )\n", "\n", "\n", "def test_filter_overlapping_sanity():\n", "    \u001b[0m\u001b[47m\u001b[30mblob1_chunk_factory = ChunkFactory(\"blob1\")<|endoftext|>\u001b[0m\u001b[94m\n", "    blob2_chunk_factory = ChunkFactory(\"blob2\")\n", "    blob3_chunk_factory = ChunkFactory(\"blob2\")\n", "    blob1_chunk_0_10 = blob1_chunk_factory.to_chunk(0, 10)\n", "    blob2_chunk_5_15 = blob2_chunk_factory.to_chunk(5, 15)\n", "    blob3_chunk_12_20 = blob3_chunk_factory.to_chunk(12, 20)\n", "\n", "    # sanity 2\n", "    assert run_modified_chunks_filter(\n", "        [\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[4], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/datasets/edit_test.py\n", "\n", "\u001b[92m            {\n", "                \"request_id\": expected.request_id,\n", "                \"user_id\": \"test-user\",\n", "                \"user_agent\": \"test-agent\",\n", "                \"request_timestamp\": expected.request.timestamp,\n", "                \"request_json\": MessageToDict(request),\n", "                \"response_timestamp\": expected.response.timestamp,\n", "                \"response_json\": MessageToDict(response),\n", "                \"resolution_json\": MessageToDict(resolution) if resolution else None,\n", "                \u001b[0m\u001b[47m\u001b[30m\"resolution_timestamp\": expected.resolution.timestamp,<|endoftext|>\u001b[0m\u001b[94m\n", "            },\n", "        ],\n", "        1,\n", "        cache,\n", "        checkpoint_cache,\n", "        reconstruct_request_files=True,\n", "    )\n", "\n", "    (actual,) = list(dataset.get_entries())\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[5], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/embedder_host/server/embedder_model_test.py\n", "\n", "\u001b[92m\n", "    round = round_from_token_lists([tokens])\n", "    embeddings = model.process_round(round)\n", "    assert len(embeddings) == 1\n", "    assert len(embeddings[0]) == emb_dim\n", "\n", "    round = round_from_token_lists([[1, 2, 3], [4, 5, 6]])\n", "    embeddings = model.process_round(round)\n", "    assert len(embeddings) == 2\n", "    \u001b[0m\u001b[47m\u001b[30massert len(embeddings[0]) == emb_dim\n", "    assert len(embeddings[1]) == emb_dim<|endoftext|>\u001b[0m\u001b[94m\n", "\n", "\n", "def test_process_round_raises_on_large_round():\n", "    \"\"\"Tests that process_round raises ValueError when a round is too large.\"\"\"\n", "    model = test_model()\n", "    max_length = model._max_length\n", "    with pytest.raises(ValueError):\n", "        model.process_round(round_from_token_lists([list(range(max_length + 1))]))\n", "\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[6], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/fastforward/cached_attention_test.py\n", "\n", "\u001b[92m\n", "    assert ref_cache_positions == out_cache_positions\n", "    assert ref_cache_tokens == out_cache_tokens\n", "\n", "\n", "@pytest.mark.parametrize(\n", "    \"num_tokens, num_heads, head_dim, dtype\",\n", "    [\n", "        pytest.param(10, 2, 8, torch.float16, id=\"dummy-fp16\"),\n", "        \u001b[0m\u001b[47m\u001b[30mpytest.param(21, 3, 16, torch.float16, id=\"med-fp16\"),<|pause|>\n", "        pytest.param(7, 11, 8, torch.float16, id=\"odd-fp16\"),<|pause|>\n", "        pytest.param(11, 2, 8, torch.bfloat16, id=\"dummy-bf16\"),<|pause|>\n", "        pytest.param(22, 3, 16, torch.bfloat16, id=\"med-bf16\")<|endoftext|>\u001b[0m\u001b[94m,\n", "        pytest.param(9, 11, 8, torch.bfloat16, id=\"odd-bf16\"),\n", "    ],\n", ")\n", "@pytest.mark.parametrize(\"attn_impl\", cached_attention.ALL_ATTN_IMPLS)\n", "def test_cache_attention_correctness(\n", "    num_heads: int,\n", "    head_dim: int,\n", "    num_tokens: int,\n", "    dtype: torch.dtype,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[7], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/server/test_runner_pb2_grpc.py\n", "\n", "\u001b[92m                    servicer.GetTestInfo,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetTestInfoRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetTestInfoResponse.SerializeToString,\n", "            ),\n", "            'GetCommandLog': grpc.unary_stream_rpc_method_handler(\n", "                    servicer.GetCommandLog,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetCommandLogRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetCommandLogResponse.SerializeToString,\n", "            ),\n", "            \u001b[0m\u001b[47m\u001b[30m'GetTestCases': grpc.unary_unary_rpc_method_handler(<|pause|>\n", "                    servicer.GetTestCases,<|pause|>\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetTestCasesRequest.FromString,<|pause|>\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetTestCasesResponse.SerializeToString,\n", "            ),<|pause|>\n", "            'GetArchiveData': grpc.unary_stream_rpc_method_handler(\n", "                    servicer.GetArchiveData,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetArchiveDataRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetArchiveDataResponse.SerializeToString,\n", "            ),<|pause|>\n", "            'GetBuildEvents': grpc.unary_stream_rpc_method_handler(\n", "                    servicer.GetBuildEvents,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetBuildEventsRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetBuildEventsResponse.SerializeToString,\n", "            ),<|pause|>\n", "            'GetPendingRuns': grpc.unary_unary_rpc_method_handler(\n", "                    servicer.GetPending<PERSON><PERSON>,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetPendingRunsRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetPendingRunsResponse.SerializeToString,\n", "            )<|endoftext|>\u001b[0m\u001b[94m,\n", "            'SearchRuns': grpc.unary_stream_rpc_method_handler(\n", "                    servicer.<PERSON><PERSON><PERSON>,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.SearchRunsRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.SearchRunsResponse.SerializeToString,\n", "            ),\n", "            'GetRuns': grpc.unary_stream_rpc_method_handler(\n", "                    servicer.<PERSON><PERSON><PERSON>,\n", "                    request_deserializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetRunsRequest.FromString,\n", "                    response_serializer=tools_dot_bazel__runner_dot_server_dot_test__runner__pb2.GetRunsResponse.SerializeToString,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[8], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/request_insight/annotation_exporter/annotation_exporter_test.py\n", "\n", "\u001b[92m                \"skipped_suffix\": \"\",\n", "                \"suffix_replacement_text\": \"\",\n", "            },\n", "            \"feedback\": {\n", "                \"rating\": \"POSITIVE\",\n", "                \"note\": \"best completion ever\",\n", "            },\n", "        },\n", "    }\n", "    \u001b[0m\u001b[47m\u001b[30massert exporter.session.post.call_args_list[1][0][0] == webhook_url<|pause|>\n", "    assert exporter.session.post.call_args_list[1][1][\"json\"] == {\n", "        \"payload\": {\n", "            \"event_type\": \"edit\",\n", "            \"user_id\": \"user2\",\n", "            \"request_id\": str(messages[3].update_request_info_request.request_id),\n", "            \"request\": {\n", "                \"path\": \"test-path\",\n", "                \"prefix\": \"\",\n", "                \"selected_text\": \"\",\n", "                \"suffix\": \"\",\n", "                \"position\": {\n", "                    \"prefix_begin\": 0,\n", "                    \"suffix_end\": 0,\n", "                },\n", "                \"instruction\": \"\",\n", "                \"file_count\": 0,\n", "            },\n", "            \"response\": {\n", "                \"text\": \"new code\",\n", "            },\n", "            \"feedback\": {\n", "                \"is_accepted\": True,\n", "                \"annotated_text\": \"best edit ever\",\n", "                \"annotated_instruction\": \"fix my code\",\n", "            },\n", "        },\n", "    }<|pause|>\n", "    assert exporter.session.post.call_args_list[2][0][0] == webhook_url<|pause|>\n", "    assert exporter.session.post.call_args_list[2][1][\"json\"] == {\n", "        \"payload\": {\n", "            \"event_type\": \"preference_sample\",\n", "            \"request_id\": str(messages[4].update_request_info_request.request_id),\n", "            \"scores\": {\"score1\": 1, \"score2\": 2},\n", "            \"feedback\": \"some feedback\",\n", "            \"request_a\": {\n", "                \"event_type\": \"chat\",\n", "                \"request_id\": test_uuid1,\n", "                \"user_id\": \"user3\",\n", "                \"request\": {\n", "                    \"path\": \"test-path\",\n", "                    \"prefix\": \"\",\n", "                    \"selected_code\": \"\",\n", "                    \"suffix\": \"\",\n", "                    \"message\": \"\",\n", "                    \"chat_history\": [],\n", "                },\n", "                \"response\": {\n", "                    \"text\": \"new code\",\n", "                },\n", "            },\n", "            \"request_b\": {\n", "                \"event_type\": \"chat\",\n", "                \"request_id\": test_uuid2,\n", "                \"user_id\": \"user3\",\n", "                \"request\": {\n", "                    \"path\": \"test-path\",\n", "                    \"prefix\": \"\",\n", "                    \"selected_code\": \"\",\n", "                    \"suffix\": \"\",\n", "                    \"message\": \"\",\n", "                    \"chat_history\": [],\n", "                },\n", "                \"response\": {\n", "                    \"text\": \"new code\",\n", "                },\n", "            },\n", "        },\n", "    }<|pause|>\n", "    assert bigtable_reader.get_request_info.call_count == 4<|pause|>\n", "\n", "\n", "@pytest.mark.parametrize(\"export\", [False, True])\n", "def test_annotation_exporter_edit_with_missing_response(\n", "    export,\n", "):\n", "    \"\"\"Test the edge case where an edit request/response is missing.\"\"\"\n", "    bigtable_reader = MagicMock(spec=BigTableReader)\n", "    token_exchange_client = MagicMock(spec=TokenExchangeClient)\n", "    token_exchange_client.get_signed_token_for_service.return_value = (\n", "        pydantic.SecretStr(\"test-token\")\n", "    )\n", "    webhook_url = \"https://test.augmentcode.com\"\n", "    exporter = AnnotationExporter(\n", "        bigtable_reader,\n", "        token_exchange_client,\n", "        webhook_url,\n", "        webhook_auth_file=None,\n", "        export_failed_edits_after_minutes=100,\n", "    )\n", "    exporter.session = MagicMock()\n", "    exporter.session.post.return_value.status_code = 200\n", "\n", "    # Old events should be exported, new ones should not\n", "    age = 200 if export else 1\n", "    event_time = timestamp_pb2.Timestamp()\n", "    event_time.FromDatetime(datetime.datetime.now() - datetime.timedelta(minutes=age))\n", "    messages = [\n", "        _create_event_message(\n", "            edit_resolution=request_insight_pb2.EditResolution(\n", "                is_accepted=True,\n", "                annotated_text=\"best edit ever\",\n", "                annotated_instruction=\"fix my code\",\n", "            ),\n", "            time=event_time,\n", "        ),\n", "    ]\n", "    print(messages)\n", "\n", "    bigtable_reader.get_request_info = MagicMock(\n", "        side_effect=[\n", "            [\n", "                request_insight_pb2.RequestInfo(\n", "                    request_metadata=request_insight_pb2.RequestMetadata(\n", "                        user_id=\"user2\",\n", "                    ),\n", "                ),\n", "            ],\n", "        ]\n", "    )\n", "\n", "    if not export:\n", "        with pytest.raises(MissingEventDataException):\n", "            exporter.export(messages)\n", "        # We should not export this edit\n", "        assert exporter.session.post.call_count == 0\n", "        return\n", "\n", "    # We should export this edit\n", "    exporter.export(messages)\n", "    assert exporter.session.post.call_count == 1\n", "    print(exporter.session.post.call_args_list[0])\n", "    assert exporter.session.post.call_args_list[0][0][0] == webhook_url\n", "    assert exporter.session.post.call_args_list[0][1][\"json\"] == {\n", "        \"payload\": {\n", "            \"event_type\": \"edit\",\n", "            \"user_id\": \"user2\",\n", "            \"request_id\": str(messages[0].update_request_info_request.request_id),\n", "            \"feedback\": {\n", "                \"is_accepted\": True,\n", "                \"annotated_text\": \"best edit ever\",\n", "                \"annotated_instruction\": \"fix my code\",\n", "            },\n", "        },\n", "    }\n", "    assert bigtable_reader.get_request_info.call_count == 1<|endoftext|>\u001b[0m\u001b[94m\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[9], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: services/content_manager/test/content_manager_test.py\n", "\n", "\u001b[92m        print(\"Upload transformed content\")\n", "        content_manager_client.upload_transformed_content_from_files(\n", "            blob_name=blob_name,\n", "            transformation_key=\"test2\",\n", "            files=[(\"1\", small_file)],\n", "            receipt_handle=response.receipt_handle,\n", "            request_context=request_context,\n", "        )\n", "\n", "        \u001b[0m\u001b[47m\u001b[30mdownload_content, metadata = content_manager_client.download_all(\n", "            blob_name=blob_name,\n", "            transformation_key=\"test2\",\n", "            sub_key=\"1\",\n", "            request_context=request_context,\n", "        )<|pause|>\n", "        assert content == download_content<|endoftext|>\u001b[0m\u001b[94m\n", "        assert metadata == {}\n", "    finally:\n", "        content_manager_deploy.kubectl.delete(transformation_key_config)\n", "\n", "\n", "def test_dynamic_transformation_keys_reupload(\n", "    content_manager_deploy,\n", "    content_manager_client: ContentManagerClient,\n", "    small_file: Path,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[10], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/fastforward/multirequest_flash_attention_test.py\n", "\n", "\u001b[92m    cumulative_seqlens,\n", "):\n", "    mrfa_fn = mrfa\n", "    if is_graphed:\n", "        mrfa_fn = cuda_graphs.GraphedFunction(\n", "            mrfa,\n", "            batch_sizes=[round_size],\n", "            argument_specs=[\n", "                cuda_graphs.ArgumentSpec(batch_dim=0),\n", "                \u001b[0m\u001b[47m\u001b[30mcuda_graphs.ArgumentSpec(batch_dim=None),<|pause|>\n", "                cuda_graphs.ArgumentSpec(batch_dim=None),<|pause|>\n", "                cuda_graphs.ArgumentSpec(batch_dim=None),<|pause|>\n", "                cuda_graphs.ArgumentSpec(batch_dim=0),<|pause|>\n", "                cuda_graphs.ArgumentSpec(batch_dim=0),<|endoftext|>\u001b[0m\u001b[94m\n", "            ],\n", "        )\n", "    result = mrfa_fn(q, k_cache, v_cache, cumulative_seqlens, cache_idxs, cache_seqlens)\n", "    ref = _reference_batch_flash_attn(q, k_cache, v_cache, cache_seqlens, cache_idxs)\n", "\n", "    torch.testing.assert_close(result, ref, atol=1e-3, rtol=1e-3)\n", "\n", "\n", "@pytest.mark.parametrize(\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[11], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/workspace/change-tracker.test.ts\n", "\n", "\u001b[92m            kit.apply(113, 3, 1);\n", "            kit.apply(116, 1, 2);\n", "            kit.apply(testCase.start, testCase.charsDeleted, testCase.charsInserted);\n", "            kit.verifyEdits(testCase.expected);\n", "        }\n", "    );\n", "});\n", "\n", "describe(\"changeTracker chunking\", () => {\n", "    \u001b[0m\u001b[47m\u001b[30mtest(\"empty\", () => {\n", "        const changeTracker = new ChangeTracker();\n", "        expect(changeTracker.getChunks(100, 100).length).toBe(0);\n", "    });<|pause|>\n", "\n", "    test(\"one chunk\", () => {\n", "        const documentSize = 1000;\n", "        const kit = new ChangeTrackerTestKit(documentSize);\n", "        kit.apply(500, 5, 0);\n", "        kit.verifyChunks(80, documentSize, [\n", "            { start: 460, origLength: 85, origStart: 460, length: 80 },\n", "        ]);\n", "    });<|pause|>\n", "\n", "    // \"two edits\" tests chunking over a document with two edits.<|pause|>\n", "    test.each([\n", "        {\n", "            maxChunkSize: 60,\n", "            expected: [\n", "                { start: 480, length: 60, origStart: 480, origLength: 45 },\n", "                { start: 585, length: 60, origStart: 570, origLength: 35 },\n", "            ],\n", "        },\n", "        {\n", "            maxChunkSize: 80,\n", "            expected: [\n", "                { start: 470, length: 80, origStart: 470, origLength: 65 },\n", "                { start: 575, length: 80, origStart: 560, origLength: 55 },\n", "            ],\n", "        },\n", "        {\n", "            maxChunkSize: 100,\n", "            expected: [\n", "                { start: 460, length: 100, origStart: 460, origLength: 85 },\n", "                { start: 565, length: 100, origStart: 550, origLength: 75 },\n", "            ],\n", "        },\n", "        {\n", "            maxChunkSize: 110,\n", "            expected: [\n", "                { start: 455, length: 110, origStart: 455, origLength: 95 },\n", "                { start: 565, length: 110, origStart: 550, origLength: 85 },\n", "            ],\n", "        },\n", "        {\n", "            maxChunkSize: 160,\n", "            expected: [{ start: 485, origLength: 120, origStart: 485, length: 160 }],\n", "        },\n", "    ])(\"two edits($maxChunkSize)\", (testCase: { maxChunkSize: number; expected: RawEdit[] }) => {\n", "        const documentSize = 1000;\n", "        const kit = new ChangeTrackerTestKit(documentSize);\n", "        kit.apply(500, 5, 20);\n", "        kit.apply(600, 5, 30);\n", "        kit.verifyChunks(testCase.maxChunkSize, documentSize, testCase.expected);\n", "    });<|pause|>\n", "\n", "    // \"edit larger than chunk size\" tests chunking over a document with an edit that is larger\n", "<|endoftext|>\u001b[0m\u001b[94m    // than the maximum chunk size.\n", "    test(\"edit larger than chunk size\", () => {\n", "        const documentSize = 1000;\n", "        const kit = new ChangeTrackerTestKit(documentSize);\n", "        kit.apply(480, 5, 10);\n", "        kit.apply(500, 12, 317);\n", "        kit.verifyChunks(100, documentSize, [\n", "            { start: 480, length: 100, origStart: 480, origLength: 27 },\n", "            { start: 580, length: 100, origStart: 507, origLength: 0 },\n", "            { start: 680, length: 100, origStart: 507, origLength: 0 },\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[12], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/completions/completions-model.test.ts\n", "\n", "\u001b[92m                _path: string,\n", "                _blobName: string | undefined,\n", "                _completionLocation: CompletionLocation | undefined,\n", "                _language: string,\n", "                _context: Blobs\n", "            ): Promise<CompletionResult> => {\n", "                const item = {\n", "                    text: \"basic completion example\",\n", "                    suffixReplacementText: \"Goodbye\\n\",\n", "                    \u001b[0m\u001b[47m\u001b[30mskippedSuffix: \"Hello\\n\",\n", "                }<|endoftext|>\u001b[0m\u001b[94m;\n", "                return {\n", "                    completionItems: [item],\n", "                    unknownBlobNames: [],\n", "                    checkpointNotFound: false,\n", "                };\n", "            }\n", "        );\n", "\n", "        const result = await kit.generateCompletion(kit.document, kit.topOfDocument);\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[13], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/retrieval/tests/test_recency_scorer.py\n", "\n", "\u001b[92m        scorer = self._setup_scorer()\n", "\n", "        # Add a document with 2 chunks\n", "        text3 = \"d\\ne\\nf\\n\"\n", "        path3 = \"c.txt\"\n", "        doc3 = Document(id=DocumentId(3), text=text3, path=path3)\n", "\n", "        id3_1 = ChunkId(\"3_1\")\n", "        text3_1 = \"d\\n\"\n", "        \u001b[0m\u001b[47m\u001b[30mchunk3_1 = Chunk(\n", "            id=id3_1,\n", "            text=text3_1,\n", "            parent_doc=doc3,\n", "            char_offset=0,\n", "            length=len(text3_1),\n", "            line_offset=0,\n", "            length_in_lines=1,\n", "        )<|pause|>\n", "\n", "        id3_2 = ChunkId(\"3_2\")<|pause|>\n", "        text3_2 = \"e\\nf\\n\"<|pause|>\n", "        chunk3_2 = Chunk(\n", "            id=id3_2,\n", "            text=text3_2,\n", "            parent_doc=doc3,\n", "            char_offset=len(text3_1),\n", "            length=len(text3_2),\n", "            line_offset=1,\n", "            length_in_lines=2,\n", "        )<|pause|>\n", "\n", "        assert chunk3_1.text + chunk3_2.text == doc3.text<|pause|>\n", "        self.id_to_chunk[id3_1] = chunk3_1<|pause|>\n", "        self.id_to_chunk[id3_2] = chunk3_2<|pause|>\n", "\n", "        scorer.add_doc([chunk3_1, chunk3_2])<|pause|>\n", "\n", "        # Pretend we added the last line in this file<|pause|>\n", "        diff_text = git_diff(\n", "            before_text=\"d\\ne\\n\",\n", "            before_path=path3,\n", "            after_text=\"d\\ne\\nf\\n\",\n", "            after_path=path3,\n", "            git_options=\"-U0\",\n", "        )<|pause|>\n", "        diff_patchset = PatchSet(diff_text)<|pause|>\n", "        recency_extra = RecencyExtra(client_git_diff_patchset=diff_patchset)<|pause|>\n", "        extra = {RECENCY_EXTRA_KEY: recency_extra}<|pause|>\n", "\n", "        model_input = ModelInput(extra=extra)<|pause|>\n", "        ids, scores = scorer.score(model_input)<|pause|>\n", "        assert len(ids) == 4<|pause|>\n", "\n", "        sorted_chunks = self._get_sorted_chunks(ids, scores)<|pause|>\n", "        assert len(sorted_chunks) == 4<|pause|>\n", "        assert sorted_chunks[0] == chunk3_2<|pause|>\n", "        assert sorted_chunks[1] == self.chunk1<|pause|>\n", "        assert sorted_chunks[2] == self.chunk2<|pause|>\n", "        assert sorted_chunks[3] == chunk3_1<|endoftext|>\u001b[0m\u001b[94m\n", "\n", "\n", "class TestGitDiffProcessing(unittest.TestCase):\n", "    \"\"\"Test git-diff processing.\"\"\"\n", "\n", "    def _get_single_hunk(self, before_text: str, after_text: str, path=\"hello.txt\"):\n", "        diff_text: str = git_diff(\n", "            before_text=before_text,\n", "            before_path=path,\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[14], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: tools/bazel_runner/server/bazel_runner_server_gcp_lib_manual_test.py\n", "\n", "\u001b[92m    build_event_persistence.store(\n", "        job_id=job_id,\n", "        invocation_id=uuid.uuid4(),\n", "        sequence_number=5000,\n", "        build_event=build_event_stream_pb2.BuildEvent(),\n", "        test_summary=test_summary_pb2.TestSummary(),\n", "        event_time=datetime.datetime.now(),\n", "    )\n", "\n", "    \u001b[0m\u001b[47m\u001b[30mbuild_event_persistence.store(<|pause|>\n", "        job_id=job_id,<|pause|>\n", "        invocation_id=uuid.uuid4(),\n", "        sequence_number=70000,\n", "        build_event=build_event_stream_pb2.BuildEvent(),\n", "        test_summary=test_summary_pb2.TestSummary(),\n", "        event_time=datetime.datetime.now(),\n", "    )<|endoftext|>\u001b[0m\u001b[94m\n", "\n", "    events = list(\n", "        build_event_persistence.get_build_events(\n", "            job_id=job_id, min_sequence_number=2000\n", "        )\n", "    )\n", "    assert len(events) == 3\n", "    assert events[0][0] == 3000\n", "    assert events[1][0] == 5000\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[15], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/retrieval/tests/test_oracle_perplexity_reranker.py\n", "\n", "\u001b[92m\n", "\n", "@parameterized.expand([\"starcoderbase_1b\"])\n", "def test_perplexity_reranker(model_name):\n", "    doc_index, reranker = load_model(model_name)\n", "\n", "    magic_number_id = \"1\"\n", "    docs = [\n", "        Document(id=magic_number_id, text=\"magic_number = 1231\", path=\"a_document.py\"),\n", "        \u001b[0m\u001b[47m\u001b[30mDocument(\n", "            id=\"2\", text=\"def run_this_func(): return 'hi'\", path=\"a_document2.py\"\n", "        )<|endoftext|>\u001b[0m\u001b[94m,\n", "        Document(\n", "            id=\"3\", text=\"def wrapper(): return run_this_func()\", path=\"a_document3.py\"\n", "        ),\n", "    ]\n", "    doc_index.add_docs(docs)\n", "\n", "    prompt = \"run_this_func():\"\n", "\n", "    # Magic number should be lowest rank before reranking\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[16], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/utils/webviews/messaging.test.ts\n", "\n", "\u001b[92m        it(\"returns an object with a requestId property that matches the input requestId\", () => {\n", "            const response: WebViewMessage = {\n", "                type: WebViewMessageType.chatUserMessage,\n", "                data: { text: \"hello\", chatHistory: [] },\n", "            };\n", "            const wrappedResponse = wrapResponse(\"mock-request-id\", response);\n", "            expect(wrappedResponse.requestId).toBe(\"mock-request-id\");\n", "        });\n", "\n", "        \u001b[0m\u001b[47m\u001b[30mit(\"returns an object with a baseMsg property that is the original response\", () => {<|pause|>\n", "            const response: WebViewMessage = {\n", "                type: WebViewMessageType.chatUserMessage,\n", "                data: { text: \"hello\", chatHistory: [] },\n", "            };<|pause|>\n", "            const wrappedResponse = wrapResponse(\"mock-request-id\", response);<|pause|>\n", "            expect(wrappedResponse.baseMsg).toBe(response);\n", "        });\n", "    }<|endoftext|>\u001b[0m\u001b[94m);\n", "\n", "    describe(\"AsyncMsgHandler\", () => {\n", "        let handlers: ((msg: AsyncWebViewMessage<WebViewMessage>) => boolean)[] = [];\n", "        const registrar = jest.fn((h: (msg: AsyncWebViewMessage<WebViewMessage>) => boolean) => {\n", "            const wrappedH = jest.fn(h);\n", "            handlers.push(wrappedH);\n", "            return () => {\n", "                handlers.filter((_h) => _h !== wrappedH);\n", "            };\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[17], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/workspace/blob-watcher.test.ts\n", "\n", "\u001b[92m        kit.addFiles(sampleFiles);\n", "        const pathNotifier = await kit.createPathNotifier();\n", "        await kit.verifyContext(pathNotifier, Array.from(pathNameSet));\n", "\n", "        const deletePath = \"giraffe/dog/partridge.py\";\n", "        expect(sampleFiles.keys()).toContain(deletePath);\n", "        kit.deleteFile(deletePath);\n", "        pathNameSet.delete(deletePath);\n", "\n", "        \u001b[0m\u001b[47m\u001b[30mawait kit.verifyContext(pathNotifier, Array.from(pathNameSet));\n", "    <|endoftext|>\u001b[0m\u001b[94m});\n", "});\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[18], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/datasets/hindsight_completion_test.py\n", "\n", "\u001b[92m        ),\n", "        ground_truth=\"test-ground-truth\",\n", "    )\n", "\n", "\n", "@pytest.fixture\n", "def completion_event() -> CompletionRequestIdIssuedEvent:\n", "    return CompletionRequestIdIssuedEvent(\n", "        session_id=\"test-session-id\",\n", "        \u001b[0m\u001b[47m\u001b[30muser_id=\"test-user-id\",<|pause|>\n", "        tenant=\"test-tenant\",<|pause|>\n", "        time=datetime(2024, 1, 1, tzinfo=timezone.utc),<|pause|>\n", "        file_path=\"test-path\",<|endoftext|>\u001b[0m\u001b[94m\n", "        request_id=\"test-request-id\",\n", "    )\n", "\n", "\n", "@pytest.fixture\n", "def text_edit_event(datum) -> TextEditEvent:\n", "    cursor = len(datum.completion.request.prefix)\n", "    text_edit = TextEditEvent(\n", "        session_id=\"test-session-id\",\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[19], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/workspace/blobs-checkpoint-manager.test.ts\n", "\n", "\u001b[92m        }\n", "        expect(blobs.checkpointId).toEqual(\"0x3\");\n", "    });\n", "\n", "    test(\"Handle feature flag changes\", async () => {\n", "        // First, run a normal checkpoint\n", "        const checkpointThreshold = 10;\n", "        let checkpointManager = new BlobsCheckpointManagerTestKit(checkpointThreshold);\n", "        function* responseGen(): Generator<CheckpointBlobsResult, void, undefined> {\n", "            \u001b[0m\u001b[47m\u001b[30myield { newCheckpointId: \"0x1\" };<|pause|>\n", "            yield { newCheckpointId: \"0x2\" };<|endoftext|>\u001b[0m\u001b[94m\n", "            yield { newCheckpointId: \"0x3\" };\n", "        }\n", "        checkpointManager.mockApiServer.checkpointResponse = responseGen();\n", "\n", "        // A checkpoint is launched as a side effect of updating blob state.\n", "        const blobs1 = checkpointManager.getContext();\n", "        let blobNames = genBlobNames(checkpointThreshold);\n", "        for (const item of blobNames) {\n", "            checkpointManager.updateBlob(\"test\", undefined, item);\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[20], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: base/logging/struct_logging_test.py\n", "\n", "\u001b[92m    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):\n", "        setup_struct_logging()\n", "\n", "        log = structlog.get_logger(\"log\")\n", "        log.info(\"Hello\", user=\"bob\")\n", "    assert not stdout.getvalue()\n", "    output = stderr.getvalue()\n", "    data = json.loads(output)\n", "    assert \"time\" in data\n", "    \u001b[0m\u001b[47m\u001b[30massert \"message\" in data<|pause|>\n", "    assert data[\"message\"] == \"Hello\"<|pause|>\n", "    assert data[\"severity\"] == \"INFO\"<|pause|>\n", "    assert \"logging.googleapis.com/sourceLocation\" in data\n", "<|endoftext|>\u001b[0m\u001b[94m    # TODO figure out user should be rendered\n", "\n", "\n", "def test_struct_logging_structlog_with_context():\n", "    \"\"\"Contains a basic test with structlog module usage.\"\"\"\n", "    stderr = io.StringIO()\n", "    stdout = io.StringIO()\n", "    with contextlib.redirect_stderr(stderr), contextlib.redirect_stdout(stdout):\n", "        setup_struct_logging()\n", "\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[21], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/data/collection/github/tests/test_gh_archive_scan.py\n", "\n", "\u001b[92m        for line in bf:\n", "            data.append(json.loads(line))\n", "\n", "    cleanup()\n", "    # First take a note how many repos are missing\n", "    n_missing = count_missing(data)\n", "    repo_list = {entry[2]: f\"{entry[0]}/{entry[1]}\" for entry in data}\n", "    n_reported = process_gh_archive.insert_repos(repo_list, source=TEST_SOURCE)\n", "    # Check if all rows are now in repo table\n", "    \u001b[0m\u001b[47m\u001b[30massert count_missing(data) == 0, \"There are still missing rows after insert!\"<|pause|>\n", "    # check if reported insertion count matches up with missing count<|pause|>\n", "    assert (\n", "        n_reported == n_missing\n", "    ), \"Reported insertion count does not match missing repo count\"<|pause|>\n", "    # check if actual new repos match with missing count<|pause|>\n", "    assert (\n", "        count_inserted() == n_missing\n", "    ), \"New records in table does not match missing repo count\"<|endoftext|>\u001b[0m\u001b[94m\n", "    cleanup()\n", "\n", "\n", "def test_single_segment(segment=\"2015-01-02-7\"):\n", "    \"\"\"Process one single segment and check correctness.\"\"\"\n", "    cleanup()\n", "    n_reported = process_gh_archive.process_segment(segment, source=TEST_SOURCE)\n", "    n_inserted = count_inserted()\n", "    # Check the total insertion count (by summing up the reported insertion count from\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[22], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/vscode/src/__tests__/command-manager-init.test.ts\n", "\n", "\u001b[92m    for (const command of commands) {\n", "        if (command.type === CommandType.private) {\n", "            continue;\n", "        }\n", "\n", "        test(`check ${command.commandID} for package.json title`, async () => {\n", "            const pkgCmd = pkgCommands[command.commandID];\n", "            if (pkgCmd === undefined) {\n", "                expect(command.title).toBeDefined();\n", "                \u001b[0m\u001b[47m\u001b[30mexpect(command.titleString).toBeDefined();\n", "                expect(command.titleString!.trim().length).toBeGreaterThan(0);\n", "            }<|endoftext|>\u001b[0m\u001b[94m else {\n", "                expect(pkgCmd).toBeDefined();\n", "                expect(pkgCmd.title).toBeDefined();\n", "                expect(pkgCmd.title.trim().length).toBeGreaterThan(0);\n", "            }\n", "        });\n", "    }\n", "});\n", "\n", "// Ensure all commands in command palette have appropriate when clause\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[23], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: clients/intellij/update_versions_test.py\n", "\n", "\u001b[92m    def test_eq(self):\n", "        self.assertTrue(Se<PERSON>ver(0, 0, 0) == Semver(0, 0, 0))\n", "        self.assertTrue(Se<PERSON>ver(0, 0, 1) == <PERSON><PERSON>ver(0, 0, 1))\n", "        self.assertTrue(Se<PERSON>ver(0, 1, 0) == <PERSON><PERSON>ver(0, 1, 0))\n", "        self.assertTrue(Se<PERSON>ver(1, 0, 0) == <PERSON>mver(1, 0, 0))\n", "        self.assertTrue(<PERSON><PERSON><PERSON>(1, 2, 3) == <PERSON><PERSON><PERSON>(1, 2, 3))\n", "\n", "    def test_increment(self):\n", "        self.assertEqual(Semver(0, 0, 0).increment(\"patch\"), Semver(0, 0, 1))\n", "        \u001b[0m\u001b[47m\u001b[30mself.assertEqual(Semver(0, 0, 0).increment(\"minor\"), Se<PERSON><PERSON>(0, 1, 0))<|endoftext|>\u001b[0m\u001b[94m\n", "        self.assertEqual(Semver(0, 0, 0).increment(\"major\"), Semver(1, 0, 0))\n", "\n", "    def test_increment_err(self):\n", "        with self.assertRaises(SystemError) as context:\n", "            Semver(0, 0, 0).increment(\"other\")\n", "\n", "        self.assertTrue(\"Unknown update type\" in str(context.exception))\n", "\n", "\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[24], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/eval/tests/hydra/mock_k8s_utils.py\n", "\n", "\u001b[92m\n", "    def pod_events(self, name: str) -> CoreV1EventList:\n", "        return CoreV1EventList(items=[])\n", "\n", "    def pod_status(self, name: str) -> V1PodStatus:\n", "        \"\"\"Returns the status of a running Pod.\"\"\"\n", "        term_json = json.dumps(\n", "            {\n", "                \"result_str\": \"\\033[32mPASSED\\033[0m\",\n", "                \u001b[0m\u001b[47m\u001b[30m\"patch_id\": \"PATCH_ID\",\n", "                \"result\": \"PASSED\",\n", "                \"wall_time\": \"WALL_TIME\",\n", "                \"test_time\": \"TEST_TIME\",\n", "            }<|endoftext|>\u001b[0m\u001b[94m\n", "        )\n", "        container_statuses = [\n", "            V1ContainerStatus(\n", "                name=\"mock-container\",\n", "                image=\"image\",\n", "                image_id=\"image_id\",\n", "                ready=True,\n", "                restart_count=0,\n", "                state=V1ContainerState(\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[25], 10, 10, no_special_token=False)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File Path: research/gpt-neox/tests/memorize/test_memory_config.py\n", "\n", "\u001b[92m    assert_mem_cfg(\n", "        MemoryConfig.from_dict({\"save\": \"layer_out\"}),\n", "        query=MemoryConfig.Query.NONE,\n", "        save=MemoryConfig.Save.LAYER_OUT,\n", "        train_batch_distract=False,\n", "        version=2,\n", "    )\n", "\n", "    # query and save and train_batch_distract\n", "    \u001b[0m\u001b[47m\u001b[30massert_mem_cfg(<|pause|>\n", "        MemoryConfig.from_dict(\n", "            {\"query\": \"prelayer\", \"save\": \"layer_out\", \"train_batch_distract\": True}\n", "        ),<|pause|>\n", "        query=MemoryConfig.Query.PRELAYER,<|pause|>\n", "        save=MemoryConfig.Save.LAYER_OUT,<|pause|>\n", "        train_batch_distract=True,<|pause|>\n", "        version=2,\n", "    )<|endoftext|>\u001b[0m\u001b[94m\n", "\u001b[0m"]}], "source": ["show_truncated_completion(samples[26], 10, 10, no_special_token=False)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}