{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"KUBECONFIG\"] = os.path.expanduser(\"/home/<USER>/.kube/config\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:research.data.spark.infra.k8s_utils:Found 1 valid k8s config paths: ['/home/<USER>/.kube/config'].\n", "INFO:research.data.spark.infra.k8s_utils:k8s config /home/<USER>/.kube/config will be used.\n", "WARNING:research.data.spark.infra.k8s_utils:Setting KUBECONFIG to /home/<USER>/.kube/config\n", "INFO:research.data.spark.utils:Set default region for Coreweave to las1\n", "WARNING:research.data.spark.utils:Skipping bazel build.\n", "INFO:research.data.spark.infra.efs_utils:Cannot find EFS mount point for region \"las1\" using known drives;Will try to use other regions\n", "INFO:research.data.spark.infra.efs_utils:Trying all known mount locations\n", "INFO:research.data.spark.infra.efs_utils:Found EFS mount at /mnt/efs/spark-data\n", "INFO:research.data.spark.infra.efs_utils:Using EFS drive aug-cw-las1-spark-data at /mnt/efs/spark-data for package sharing\n", "INFO:research.data.spark.infra.copy_deps:Start to copy dependencies to /mnt/efs/spark-data/python_env/2024-09-06/dxy-1h100/1e074861-6a6a-4d3b-888b-e6f46ddf7ce4\n", "INFO:research.data.spark.infra.copy_deps:Copy user_base - Execution time: 3.3649 seconds\n", "INFO:research.data.spark.infra.copy_deps:Copy base - Execution time: 0.2294 seconds\n", "INFO:research.data.spark.infra.copy_deps:Copy experimental - Execution time: 0.4331 seconds\n", "INFO:research.data.spark.infra.copy_deps:Copy research - Execution time: 0.5808 seconds\n", "INFO:research.data.spark.utils:Template file: /tmp/tmpgmff1t5v/spark-template-73154a6d-f5a6-4657-a535-f73b6ac505d3.yaml\n", "INFO:research.data.spark.utils:Setting up s3 secrets and endpoint in region las1.\n", "INFO:research.data.spark.utils:Creating spark session on k8s://https://k8s.ord1.coreweave.com:443 with name dxy-1h100\n", "INFO:research.data.spark.utils:Creating spark session with config: \n", "INFO:research.data.spark.utils:  spark.driver.maxResultSize = 4g\n", "INFO:research.data.spark.utils:  spark.driver.memory = 20g\n", "INFO:research.data.spark.utils:  spark.driver.memoryOverhead = 2g\n", "INFO:research.data.spark.utils:  spark.dynamicAllocation.enabled = true\n", "INFO:research.data.spark.utils:  spark.executor.cores = 1\n", "INFO:research.data.spark.utils:  spark.executor.instances = 128\n", "INFO:research.data.spark.utils:  spark.executor.memory = 32G\n", "INFO:research.data.spark.utils:  spark.executor.memoryOverhead = 5g\n", "INFO:research.data.spark.utils:  spark.executor.pyspark.memory = 64G\n", "INFO:research.data.spark.utils:  spark.hadoop.fs.gs.impl = com.google.cloud.hadoop.fs.gcs.GoogleHadoopFileSystem\n", "INFO:research.data.spark.utils:  spark.hadoop.fs.s3a.impl = org.apache.hadoop.fs.s3a.S3AFileSystem\n", "INFO:research.data.spark.utils:  spark.hadoop.fs.s3a.path.style.access = true\n", "INFO:research.data.spark.utils:  spark.hadoop.google.cloud.auth.service.account.enable = false\n", "INFO:research.data.spark.utils:  spark.serializer = org.apache.spark.serializer.KryoSerializer\n", "INFO:research.data.spark.utils:  spark.sql.debug.maxToStringFields = 4096\n", "INFO:research.data.spark.utils:  spark.sql.execution.arrow.maxRecordsPerBatch = 500\n", "INFO:research.data.spark.utils:  spark.sql.files.maxPartitionBytes = 64m\n", "INFO:research.data.spark.utils:  spark.sql.parquet.compression.codec = zstd\n", "INFO:research.data.spark.utils:  spark.sql.repl.eagerEval.enabled = true\n", "INFO:research.data.spark.utils:  spark.sql.repl.eagerEval.maxRows = 10\n", "INFO:research.data.spark.utils:  spark.sql.repl.eagerEval.truncate = 100\n", "INFO:research.data.spark.utils:  spark.worker.cleanup.appDataTtl = 7200\n", "INFO:research.data.spark.utils:  spark.worker.cleanup.enabled = true\n", "INFO:research.data.spark.utils:  spark.worker.cleanup.interval = 3600\n", "INFO:research.data.spark.utils:  spark.executorEnv.TOKENIZERS_PARALLELISM = false\n", "INFO:research.data.spark.utils:  spark.python.useForkServer = false\n", "INFO:research.data.spark.utils:  spark.sql.catalog.spark_catalog = org.apache.spark.sql.delta.catalog.DeltaCatalog\n", "INFO:research.data.spark.utils:  spark.sql.extensions = io.delta.sql.DeltaSparkSessionExtension\n", "INFO:research.data.spark.utils:  spark.sql.parquet.columnarReaderBatchSize = 20\n", "INFO:research.data.spark.utils:  spark.hadoop.fs.s3a.access.key = *****\n", "INFO:research.data.spark.utils:  spark.hadoop.fs.s3a.secret.key = *****\n", "INFO:research.data.spark.utils:  spark.hadoop.fs.s3a.endpoint = https://object.las1.coreweave.com\n", "INFO:research.data.spark.utils:  spark.kubernetes.context = coreweave\n", "INFO:research.data.spark.utils:  spark.kubernetes.driver.master = k8s://https://k8s.ord1.coreweave.com:443\n", "INFO:research.data.spark.utils:  spark.kubernetes.namespace = tenant-augment-eng\n", "INFO:research.data.spark.utils:  spark.kubernetes.authenticate.driver.serviceAccountName = spark-sa\n", "INFO:research.data.spark.utils:  spark.kubernetes.container.image.pullPolicy = Always\n", "INFO:research.data.spark.utils:  spark.driver.host = **************\n", "INFO:research.data.spark.utils:  spark.kubernetes.container.image = au-docker-reg.tenant-augment-eng.ord1.ingress.coreweave.cloud/infra/augment_spark_python:ubuntu22.04-py-3.11.7-spark-3.4.3-22\n", "INFO:research.data.spark.utils:  spark.dynamicAllocation.shuffleTracking.enabled = true\n", "INFO:research.data.spark.utils:  spark.dynamicAllocation.minExecutors = 0\n", "INFO:research.data.spark.utils:  spark.dynamicAllocation.maxExecutors = 128\n", "INFO:research.data.spark.utils:  spark.dynamicAllocation.executorIdleTimeout = 600\n", "INFO:research.data.spark.utils:  spark.executorEnv.PYTHONUSERBASE = /mnt/ephemeral/disk/deps\n", "INFO:research.data.spark.utils:  spark.driverEnv.PYTHONUSERBASE = /mnt/ephemeral/disk/deps\n", "INFO:research.data.spark.utils:  spark.kubernetes.executor.podTemplateFile = /tmp/tmpgmff1t5v/spark-template-73154a6d-f5a6-4657-a535-f73b6ac505d3.yaml\n", "INFO:research.data.spark.utils:  spark.task.cpus = 1\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n", "INFO:research.data.spark.utils:Create spark session - Execution time: 33.5999 seconds\n"]}], "source": ["from research.data.spark import k8s_session\n", "\n", "spark_cpu = k8s_session(\n", "    max_workers=128,\n", "    conf={\n", "        \"spark.executor.pyspark.memory\": \"64G\",\n", "        \"spark.executor.memory\": \"32G\",\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"20\",\n", "        \"spark.task.cpus\": \"1\",\n", "        \"spark.executor.cores\": \"1\",\n", "    },\n", "    ephemeral_storage_gb=128,\n", ")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 20 repositories.\n"]}], "source": ["root_url = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814-rerunfail\"\n", "\n", "input_url = os.path.join(root_url, \"fim-normal\")\n", "output_url = os.path.join(root_url, \"fim-normal-post-analysis\")\n", "# fim_data = spark_cpu.read.parquet(os.path.join(input_url, \"*zstd.parquet\"))\n", "fim_data = spark_cpu.read.parquet(\n", "    os.path.join(\n", "        input_url, \"part-05968-1f7606d5-abd7-4b01-b836-a9bc276641f5-c000.zstd.parquet\"\n", "    )\n", ")\n", "print(f\"There are {fim_data.count()} repositories.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}], "source": ["from typing import Any, Iterator, Mapping, Sequence\n", "from research.data.rag import constants, common\n", "from research.data.spark.pipelines.utils import map_parquet\n", "\n", "\n", "def count_file_list(file_list: Sequence[Mapping[str, Any]]) -> Iterator[dict]:\n", "    \"\"\"Count the number of files.\"\"\"\n", "    all_num_lines = []\n", "    for file in file_list:\n", "        # Only add files of desired languages to be retrieved\n", "        if file[constants.FILE_LANG_COLUMN] not in constants.RETRIEVAL_LANGUAGES:\n", "            continue\n", "        num_lines = file[constants.CONTENT_COLUMN].count(\"\\n\")\n", "        all_num_lines.append(num_lines)\n", "    yield {\"num_lines\": tuple(all_num_lines), \"num_files\": len(file_list)}"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Decorate a function: <function count_file_list at 0x7fdf89c3aac0>.\n", "INFO:root:Final job statistics:                                                 \n", "INFO:root:\tsuccess: 825\n", "INFO:root:Total files processed: 825\n", "INFO:root:Task info can be found at /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/task_info/debug/2024-09-06-20-25-05-cd514ff55dec4824be12b38c9be6f936\n"]}], "source": ["GLOBAL_TASK_INFO_URI = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/task_info\"\n", "\n", "result = map_parquet.apply_pandas(\n", "    spark_cpu,\n", "    map_parquet.chain_processors(\n", "        [\n", "            map_parquet.allow_unused_args()(count_file_list),\n", "        ]\n", "    ),\n", "    input_path=input_url,\n", "    output_path=output_url,\n", "    timeout=60 * 900,  # 15-hours timeout\n", "    batch_size=20,\n", "    task_info_location=os.path.join(GLOBAL_TASK_INFO_URI, \"debug\"),\n", "    ignore_error=True,\n", "    allow_resume=False,\n", "    # profile=True,  # -> this is for debug\n", "    # ignore_error=False,\n", "    # timing_run=True,\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["count_data = spark_cpu.read.parquet(os.path.join(output_url, \"*zstd.parquet\"))\n", "# Convert it to normal python data\n", "count_data_df = count_data.toPandas()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean of num_files: 439.99732392652965\n", "Variance of num_files: 951163.1631116805\n"]}], "source": ["import numpy as np\n", "\n", "num_lines_list_of_lists = count_data_df[\"num_lines\"].tolist()\n", "num_files_array = count_data_df[\"num_files\"].to_numpy()\n", "\n", "# Calculate the mean and variance\n", "mean_num_files = np.mean(num_files_array)\n", "variance_num_files = np.var(num_files_array)\n", "\n", "print(\"Mean of num_files:\", mean_num_files)\n", "print(\"Variance of num_files:\", variance_num_files)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["num_lines_array.min()=5413, num_lines_array.max()=3938187, num_lines_array.mean()=64508.62705266999, num_lines_array.std()=114215.50500196483\n"]}], "source": ["num_lines_array = np.array([np.sum(x) for x in num_lines_list_of_lists])\n", "print(\n", "    f\"{num_lines_array.min()=}, {num_lines_array.max()=}, {num_lines_array.mean()=}, {num_lines_array.std()=}\"\n", ")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["num_files_array.min()=2, num_files_array.max()=32107, num_files_array.mean()=439.99732392652965, num_files_array.std()=975.2759420347046\n"]}], "source": ["print(\n", "    f\"{num_files_array.min()=}, {num_files_array.max()=}, {num_files_array.mean()=}, {num_files_array.std()=}\"\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i=0\n", "i=512\n"]}], "source": ["for i in range(0, 513, 512):\n", "    print(f\"{i=}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}