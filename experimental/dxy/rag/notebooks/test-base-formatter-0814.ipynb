{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from base import tokenizers\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_completion import PromptInput\n", "from base.prompt_format_completion.simple_elden_prompt_formatter import (\n", "    TokenApportionment,\n", "    SimpleEldenPromptFormatter,\n", "    SimpleEldenPromptFormatterConfig,\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def _get_prompt_formatter(\n", "    config: SimpleEldenPromptFormatterConfig | None = None,\n", "    tokenizer_name: str = \"deepseek_coder_v2\",\n", "):\n", "    tokenizer = tokenizers.create_tokenizer_by_name(tokenizer_name)\n", "    if config is None:\n", "        config = SimpleEldenPromptFormatterConfig(\n", "            max_prompt_length=2048,\n", "            token_config=TokenApportionment(\n", "                path_len=20,\n", "                prefix_len=512,\n", "                suffix_len=512,\n", "                retrieval_len=128,\n", "            ),\n", "            per_retriever_max_tokens={\n", "                \"signature_retriever\": 100,\n", "                \"dense_retriever\": 100,\n", "            },\n", "        )\n", "    prompter = SimpleEldenPromptFormatter(config, tokenizer)\n", "    return prompter, tokenizer\n", "\n", "\n", "def _format(\n", "    prompt_input: PromptInput,\n", "    config: SimpleEldenPromptFormatterConfig | None = None,\n", "    max_output_token_count: int = 64,\n", "    tokenizer_name: str = \"deepseek_coder_v2\",\n", "):\n", "    prompter, tokenizer = _get_prompt_formatter(\n", "        config=config,\n", "        tokenizer_name=tokenizer_name,\n", "    )\n", "    prompt_tokens = prompter.format_prompt(\n", "        prompt_input, max_output_token_count=max_output_token_count\n", "    ).tokens()\n", "    prompt = tokenizer.detokenize(prompt_tokens)\n", "    logging.info(\"Generated %d tokens for prompt\", len(prompt_tokens))\n", "    logging.info(\"Prompt: %s\", prompt)\n", "    return prompt"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:root:Token allocation: TokenApportionment(path_len=20, prefix_len=512, retrieval_len=768, suffix_len=512)\n", "INFO:root:Used 34 sentinel tokens, 10 path tokens\n", "INFO:root:Used 42 / 100 tokens to add all 2 retrieval chunks.\n", "INFO:root:Used 0 / 100 signature chunk tokens\n", "INFO:root:Used 49 / 100 tokens to add all 2 retrieval chunks.\n", "INFO:root:Used 49 / 100 recency chunk tokens\n", "INFO:root:Used 52 / 1899 tokens to add all 1 retrieval chunks.\n", "INFO:root:Used 52 / 1899 other chunk tokens\n", "INFO:root:Used 201 / 2048 tokens for prompt; reserving 64 for output\n", "INFO:root:Generated 201 tokens for prompt\n", "INFO:root:Prompt: <｜begin▁of▁sentence｜><｜fim▁begin｜># Retrieved Chunks Start:\n", "file path: src/recent.py\n", "# A recent chunksrc/bar.py\n", "# You can aggregate\n", "# with a maxing\n", "# function.\n", "file path: src/recent.py\n", "# A recent chunkfile path: src/recent.py\n", "# A recent chunksrc/bar.py\n", "# You can aggregate\n", "# with a maxing\n", "# function.\n", "src/foo.py\n", "# You can aggregate\n", "# with a pooling function.\n", "# Retrieved Chunks Finish.\n", "# Signature Chunks Start:\n", "# src/foo.py\n", "class Foo:\n", "  def do_foo(self): pass\n", "\n", "# src/bar.py\n", "class Bar:\n", "  def do_bar(self): pass\n", "# Signature Chunks Finish.\n", "file path: src/example.py\n", "def aggregate(a,b):\n", "<｜fim▁hole｜>\n", "return aggregated_output\n", "<｜fim▁end｜>\n"]}], "source": ["example_input = PromptInput(\n", "    path=\"src/example.py\",\n", "    prefix=\"def aggregate(a,b):\\n\",\n", "    suffix=\"\\nreturn aggregated_output\\n\",\n", "    prefix_begin=0,\n", "    retrieved_chunks=(\n", "        PromptChunk(\n", "            text=\"# src/bar.py\\nclass Bar:\\n  def do_bar(self): pass\",\n", "            path=\"src/bar.py\",\n", "            unique_id=\"1\",\n", "            origin=\"signature_retriever\",\n", "        ),\n", "        PromptChunk(\n", "            text=\"# src/foo.py\\nclass Foo:\\n  def do_foo(self): pass\",\n", "            path=\"src/foo.py\",\n", "            unique_id=\"2\",\n", "            origin=\"signature_retriever\",\n", "        ),\n", "        PromptChunk(\n", "            text=\"# You can aggregate\\n# with a maxing\\n# function.\\n\",\n", "            path=\"src/bar.py\",\n", "            unique_id=\"3\",\n", "            origin=\"dense_retriever\",\n", "        ),\n", "        PromptChunk(\n", "            text=\"# You can aggregate\\n# with a pooling function.\",\n", "            path=\"src/foo.py\",\n", "            unique_id=\"4\",\n", "            origin=\"\",  # intentionally unspecified.\n", "        ),\n", "        PromptChunk(\n", "            text=\"# A recent chunk\",\n", "            path=\"src/recent.py\",\n", "            unique_id=\"5\",\n", "            origin=\"recency_retriever\",\n", "        ),\n", "        # This chunk was retrieved by both the recency and dense retrievers,\n", "        # and it should only show up once\n", "        PromptChunk(\n", "            text=\"# You can aggregate\\n# with a maxing\\n# function.\\n\",\n", "            path=\"src/bar.py\",\n", "            unique_id=\"3\",\n", "            origin=\"recency_retriever\",\n", "        ),\n", "    ),\n", ")\n", "prompt = _format(\n", "    example_input,\n", "    config=SimpleEldenPromptFormatterConfig(\n", "        max_prompt_length=2048,\n", "        token_config=TokenApportionment(\n", "            path_len=20,\n", "            prefix_len=512,\n", "            suffix_len=512,\n", "            retrieval_len=768,\n", "        ),\n", "        per_retriever_max_tokens={\n", "            \"signature_retriever\": 100,\n", "            \"dense_retriever\": 200,\n", "            \"recency_retriever\": 100,\n", "        },\n", "    ),\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}