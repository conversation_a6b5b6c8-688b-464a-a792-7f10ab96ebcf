"""Smoke test LLAMA models in e4m3."""

from typing import Sequence

import pytest

from base.fastforward import fwd_model_test_utils, fwd_utils
from base.fastforward.llama import fwd_llama

_LLAMA_350M_PROMPT_TOKS = fwd_llama.LLAMA_350M_PROMPT_TOKS
_LLAMA_350M_OUTPUT_TOKS = fwd_llama.LLAMA_350M_OUTPUT_TOKS


def test_generate(llama_350m_fp8_fixture):
    """Ensure that the model generates the correct outputs."""
    step_fn, attn_factory = llama_350m_fp8_fixture
    step_fn = fwd_utils.PaddedStepFunction(step_fn, round_sizes=[8, 16])
    fwd_model_test_utils.check_if_model_generates_target_sequence(
        step_fn,
        attn_factory,
        _LLAMA_350M_PROMPT_TOKS,
        _LLAMA_350M_OUTPUT_TOKS,
        allowed_mismatches=1,
        extra_kv_len=16,
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="single_token"),
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_batched_equals_sequential(llama_350m_fp8_fixture, prompt: Sequence[int]):
    """Ensure that the batched and sequential models produce the same outputs."""
    step_fn, attn_factory = llama_350m_fp8_fixture
    step_fn = fwd_utils.pad_and_step(step_fn, [8])
    fwd_model_test_utils.check_batched_equals_sequential(
        step_fn, attn_factory, prompt, extra_kv_len=8
    )


@pytest.mark.parametrize(
    "prompt",
    [
        pytest.param([1], id="single_token"),
        pytest.param([1, 2, 3], id="dummy"),
        pytest.param(_LLAMA_350M_PROMPT_TOKS, id="known_sequence"),
    ],
)
def test_batched_equals_sequential_cuda_graph(llama_350m_fp8_graphed_fixture, prompt):
    """Ensure that the batched and sequential models produce the same outputs."""
