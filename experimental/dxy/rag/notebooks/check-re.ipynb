{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Matching paths:\n", "augment/base/static_analysis/signature_utils_test.py\n", "augment/base/static_analysis/test_signature_utils.py\n", "tests/pyright/pyright_example.py\n"]}], "source": ["import re\n", "\n", "# pattern = r\".*/test.*\\.(c|cpp|java|js|py|rs|ts)$\"\n", "# pattern = r\".*[/\\\\](test.*|.*_test)\\.(c|cpp|java|js|py|rs|ts)$\"\n", "pattern = r\".*(tests[/\\\\].*|.*_test|.*[/\\\\]test.*)\\.(c|cpp|java|js|py|rs|ts)$\"\n", "reverse_pattern = r\"^(?!.*(tests[/\\\\].*|.*_test|.*[/\\\\]test.*)\\.(c|cpp|java|js|py|rs|ts)$).*$\"\n", "\n", "unit_test_paths = [\n", "    \"augment/base/static_analysis/signature_utils_test.py\",\n", "    \"augment/base/static_analysis/test_signature_utils.py\",\n", "    \"tests/pyright/pyright_example.py\",\n", "]\n", "\n", "non_unit_test_paths = [\n", "    \"augment/base/static_analysis/signature_utils.py\",\n", "    \"augment/base/static_analysis/signature_utils.py\",\n", "    \"pyright/pyright_example.py\",\n", "]\n", "\n", "# Check which paths match the pattern\n", "matching_paths = [path for path in unit_test_paths + non_unit_test_paths if re.match(pattern, path)]\n", "print(\"Matching paths:\")\n", "for path in matching_paths:\n", "    print(path)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["543\n"]}], "source": ["x = \"\"\"\n", "import os\n", "\n", "import pytest\n", "\n", "pytestmark = pytest.mark.skipif(not os.getenv('TEST_PLUGIN'), reason='Test only with `TEST_PLUGIN` env var set.')\n", "\n", "\n", "def test_plugin_usage():\n", "    from pydantic import BaseModel\n", "\n", "    class MyModel(BaseModel):\n", "        x: int\n", "        y: str\n", "\n", "    m = MyModel(x='10', y='hello')\n", "    assert m.x == 10\n", "    assert m.y == 'hello'\n", "\n", "    from example_plugin import log\n", "\n", "    assert log == [\n", "        \"on_enter args=({'x': '10', 'y': 'hello'},) kwargs={'self_instance': MyModel()}\",\n", "        \"on_success result=x=10 y='hello'\",\n", "    ]\"\"\"\n", "print(len(x))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}