"""Check if there is any duplication in the dataset.

python experimental/dxy/rag/data_balance/check_data_duplication.py
"""

import copy

import numpy as np
import tqdm
from megatron.data import indexed_dataset

from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.shared_lib import (
    extract_tokens_before_stop_tokens,
    parse_dataset_fim_sequence,
)
from research.eval.harness.systems.libraries.completion_fim_handling import (
    fim_postprocess_generation,
)
from research.models.meta_model import FimGenMode


def normalize_code_lines(code: str) -> str:
    # Remove space and newline and "\r"
    code = code.replace(" ", "").replace("\n", "").replace("\r", "")
    return code


if __name__ == "__main__":
    tokenizer = StarCoder2Tokenizer()
    special_tokens = tokenizer.special_tokens
    dataset = indexed_dataset.MMapIndexedDataset(
        "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-sc2/dataset",
        skip_warmup=True,
    )
    total = len(dataset)
    copied = []
    for index in tqdm.tqdm(range(min(total, 10000))):
        prompt_tokens, target_tokens = parse_dataset_fim_sequence(dataset[0], tokenizer)

        # processed_tokens, _ = fim_postprocess_generation(
        #     generated_tokens=copy.deepcopy(target_tokens),
        #     eod_id=special_tokens.eos,
        #     skip_id=special_tokens.skip,
        #     pause_id=special_tokens.pause,
        #     fim_gen_mode=FimGenMode.evaluation,
        # )
        processed_tokens = extract_tokens_before_stop_tokens(
            target_tokens,
            [
                special_tokens.pause,
                special_tokens.skip,
                special_tokens.eos,
                special_tokens.padding,
            ],
        )
        prompt_text = tokenizer.detokenize(prompt_tokens)
        generated_text = tokenizer.detokenize(processed_tokens)
        # copied.append(generated_text in prompt_text)
        copied.append(
            normalize_code_lines(generated_text) in normalize_code_lines(prompt_text)
        )
    print(
        f"There are {np.sum(copied)}/{len(copied)} examples that copied from the prompt text."
    )
