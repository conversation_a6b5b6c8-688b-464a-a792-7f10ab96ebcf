"""Shared functionality for RLHF."""

import copy
import enum
import json
import os
import pathlib
import re
import typing
from datetime import datetime, timedelta, timezone

import numpy as np
import torch
import tqdm
from megatron.data import indexed_dataset

from base.fastforward.fwd_utils import get_checkpoint_sha
from base.tokenizers.tokenizer import RagSpecialTokens, Tokenizer
from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict
from research.core.utils_for_file import read_jsonl_zst
from research.core.utils_for_str import extract_the_last_markdown_block
from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem
from research.eval.harness.tasks.hindsight import HindsightOutput
from research.models.fastforward_models import StarCoder2_FastForward


class AgentType(enum.Enum):
    VSCODE = 1
    IntelliJ = 2


AGENT_FIXING_SKIP: dict[AgentType, tuple[int, int, int]] = {
    AgentType.VSCODE: (0, 292, 0)
}


class UserAgent:
    """A user agent."""

    def __init__(
        self,
        user_agent: str | None,
        agent_type: AgentType | None = None,
        version: tuple[int, int, int] | None = None,
    ):
        if user_agent is None:
            assert agent_type is not None
            assert version is not None
            self.agent_type: AgentType = agent_type
            self.version = version
        else:
            self.user_agent = user_agent
            (self.agent_type, self.version) = self.get_augment_ext_version(user_agent)

    @staticmethod
    def get_augment_ext_version(
        user_agent: str,
    ) -> tuple[AgentType, tuple[int, int, int]]:
        if user_agent.startswith("Augment.vscode-augment/"):
            agent_type = AgentType.VSCODE
        elif user_agent.startswith("augment.intellij/"):
            agent_type = AgentType.IntelliJ
        else:
            raise ValueError(f"Invalid user_agent: {user_agent}")
        # example user_agent:
        # Augment.vscode-augment/0.230.0 (linux; x64; 6.4.1) vscode/1.75.1
        version_str = user_agent.split(" ")[0]
        version = version_str.split("/")[-1]
        # This should be x.y.z, validate its format with re.
        if re.match(r"^\d+\.\d+\.\d+$", version) is not None:
            return agent_type, tuple(map(int, version.split(".")))
        else:
            # This is usually the prototype version.
            return agent_type, (-1, -1, -1)

    def __repr__(self) -> str:
        return f"UserAgent({self.version} | {self.user_agent})"

    def __eq__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        if isinstance(other, UserAgent):
            return self.version == other.version
        elif isinstance(other, tuple):
            return self.version == other
        else:
            raise ValueError(f"Invalid other: {other}")

    def __lt__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        if isinstance(other, UserAgent):
            return self.version < other.version
        elif isinstance(other, tuple):
            return self.version < other
        else:
            raise ValueError(f"Invalid other: {other}")

    def __gt__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        if isinstance(other, UserAgent):
            return self.version > other.version
        elif isinstance(other, tuple):
            return self.version > other
        else:
            raise ValueError(f"Invalid other: {other}")

    def __ge__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        if isinstance(other, UserAgent):
            return self.version >= other.version
        elif isinstance(other, tuple):
            return self.version >= other
        else:
            raise ValueError(f"Invalid other: {other}")

    def __le__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        if isinstance(other, UserAgent):
            return self.version <= other.version
        elif isinstance(other, tuple):
            return self.version <= other
        else:
            raise ValueError(f"Invalid other: {other}")


def traverse_date_range(
    start_date: str, end_date: str
) -> list[tuple[str, datetime, datetime]]:
    """Traverse a date range and generate a list of date pairs."""
    utc = timezone.utc
    # Parse the input date strings to datetime objects
    start_date_dt = datetime.strptime(start_date, "%Y%m%d").replace(tzinfo=utc)
    end_date_dt = datetime.strptime(end_date, "%Y%m%d").replace(tzinfo=utc)

    # Ensure start_date < end_date
    if start_date_dt > end_date_dt:
        raise ValueError("start_date must be earlier than or equal to end_date")

    # Initialize the result list
    date_ranges: list[tuple[str, datetime, datetime]] = []

    # Traverse through the range and generate date pairs
    current_date = start_date_dt
    while current_date <= end_date_dt:
        next_date = current_date + timedelta(days=1)
        # Append the current date and the range (current_date, current_date + 1)
        date_ranges.append((current_date.strftime("%Y%m%d"), current_date, next_date))
        # Move to the next day
        current_date = next_date

    return date_ranges


def str2datetime(x: str) -> datetime:
    return datetime.fromisoformat(x)


def str2datetimefloat(x: str) -> float:
    return datetime.fromisoformat(x).timestamp()


def inspect_skip_tokens(
    all_data: list[dict],
    model2skip: dict[str, int],
    missing_model_skip_token: int | None = None,
):
    num_skip = 0
    num_skip_w_accept = 0
    num_accept = 0
    for x in all_data:
        skip_token = model2skip.get(x["response_model"], missing_model_skip_token)
        if skip_token is None:
            raise ValueError(f"Can not find skip token for {x['response_model']}")
        if skip_token in x["generated_token_ids"]:
            num_skip += 1
            if x["accepted"]:
                num_skip_w_accept += 1
        if x["accepted"]:
            num_accept += 1
    print(f"There are {num_accept} / {len(all_data)} accepted data.")
    print(f"There are {num_skip} / {len(all_data)} data with skip token.")
    print(f"There are {num_skip_w_accept} / {num_skip} accepted data with skip token.")


def create_hindsight_eval_tokens(eval_path: str) -> typing.Generator[dict[str, typing.Any], None, None]:
    """Create the HF datasets for the hindsight data."""
    assert os.path.exists(eval_path), eval_path
    hindsight_run_results = [HindsightOutput(**x) for x in read_jsonl_zst(eval_path)]
    for x in tqdm.tqdm(hindsight_run_results):
        yield {
            "request_id": x.request_id,
            "prefix": x.prefix,
            "suffix": x.suffix,
            "path": x.path,
            "prompt_tokens": x.prompt_tokens,
            "ground_truth": x.ground_truth,
            "generation": x.generation,
            "raw_obj": x,
        }


def create_system(name: str = "SC2_Ender_V3_S6K") -> ProdEldenSystem:
    """Create a system."""
    if name == "SC2_Ender_V3_S6K":
        num_gpus = torch.cuda.device_count()
        assert num_gpus == 1
        ckp_dir = "/mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v3-ffw"
        model_config = {
            "name": "starcoder2_fastforward",
            "model_path": ckp_dir,
            "checkpoint_path": ckp_dir,
            "checkpoint_sha256": get_checkpoint_sha(pathlib.Path(ckp_dir)),
            # "seq_length": 6044 + 256,
        }
        max_generated_tokens = 256
        tokenizer_name = "starcoder2"
        system = ProdEldenSystem.from_yaml_config(
            {
                "tokenizer": tokenizer_name,
                "model": model_config,
                "generation_options": {"max_generated_tokens": max_generated_tokens},
                "dense_retriever": retriever_config_dict["sethanol"],
                "signature_retriever": retriever_config_dict["methanol0416_v2"],
                "formatter_config": {
                    "name": "ender",
                    "apportionment_config": None,
                    "prompt_formatter_config": {
                        "stateless_caching_config": {
                            "nearby_prefix_token_len": 512,
                            "quantize_token_len": 64,
                            "quantize_char_len": 250,
                        },
                        "component_order": [
                            "path",
                            "prefix",
                            "retrieval",
                            "signature",
                            "nearby_prefix",
                            "suffix",
                        ],
                        "signature_chunk_origin": "signature_retriever",
                        "filter_visible_chunks_by_content": True,
                        "token_budget": {
                            "max_prompt_length": 6048,
                            "path_len": 50,
                            "prefix_len": 1024,
                            "suffix_len": 512,
                            "retrieval_len": 6048,
                            "per_retriever_max_tokens": {
                                "signature_retriever": 1024,
                                "recency_retriever": 1024,
                            },
                        },
                    },
                },
                "config": {
                    "line_chunk_retriever_top_k": 32,
                    "sig_chunk_retriever_top_k": 32,
                    "fim_gen_mode": "evaluation",
                },
            }
        )
    elif name == "SC2_SimpleElden_S7K_D18M":
        num_gpus = torch.cuda.device_count()
        assert num_gpus in (1, 2)
        print(f"The number of GPUs are {num_gpus}.")
        model_config = {
            "name": "fastbackward",
            "checkpoint_path": f"/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M9000-mp{num_gpus}",
            "seq_length": 7936,
            "model_parallel_size": num_gpus,
        }
        max_generated_tokens = 256
        tokenizer_name = "starcoder2"
        system = ProdEldenSystem.from_yaml_config(
            {
                "tokenizer": tokenizer_name,
                "model": model_config,
                "generation_options": {"max_generated_tokens": max_generated_tokens},
                "dense_retriever": retriever_config_dict["sethanol"],
                "signature_retriever": retriever_config_dict["methanol0416_v2"],
                "formatter_config": {
                    "name": "simple_elden",
                    "prompt_formatter_config": {
                        "max_prompt_length": 7680,
                        "token_config": {
                            "path_len": 50,
                            "prefix_len": 1024,
                            "suffix_len": 512,
                            "retrieval_len": 7680,
                        },
                        "per_retriever_max_tokens": {
                            "signature_retriever": 1024,
                            "recency_retriever": 1024,
                            "dense_retriever": 7680 - 1024,
                        },
                    },
                },
                "config": {
                    "line_chunk_retriever_top_k": 32,
                    "sig_chunk_retriever_top_k": 32,
                    "fim_gen_mode": "evaluation",
                },
            }
        )
    elif name == "SC2_SEV2_INIT":
        from research.models import fastforward_models  # noqa

        num_gpus = torch.cuda.device_count()
        assert num_gpus == 1
        model_config = {
            "name": "starcoder2_fastforward_fp8",
            "model_path": "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0",
            "checkpoint_sha256": "9fa9c4e2acfd0fddec87cf5eeca065687092b317ad889c84a7cd725d7f30d6b5",
        }
        max_generated_tokens = 256
        tokenizer_name = "starcoder2"
        system = ProdEldenSystem.from_yaml_config(
            {
                "tokenizer": tokenizer_name,
                "model": model_config,
                "generation_options": {"max_generated_tokens": max_generated_tokens},
                "dense_retriever": retriever_config_dict["sethanol"],
                "signature_retriever": retriever_config_dict["methanol0416_v2"],
                "formatter_config": {
                    "name": "simple_elden",
                    "prompt_formatter_config": {
                        "max_prompt_length": 7680,
                        "token_config": {
                            "path_len": 50,
                            "prefix_len": 1024,
                            "suffix_len": 512,
                            "retrieval_len": 7680,
                        },
                        "per_retriever_max_tokens": {
                            "signature_retriever": 1024,
                            "recency_retriever": 1024,
                            "dense_retriever": 7680 - 1024,
                        },
                    },
                },
                "config": {
                    "line_chunk_retriever_top_k": 32,
                    "sig_chunk_retriever_top_k": 32,
                    "fim_gen_mode": "evaluation",
                },
            }
        )
    else:
        raise ValueError(f"Unknown system {name}")
    return system


def extract_tokens_before_stop_tokens(
    tokens: list[int], stop_token_ids: list[int]
) -> list[int]:
    """Extract the tokens before stop tokens."""
    tokens = tokens.copy()
    for index in range(len(tokens)):
        if tokens[index] in stop_token_ids:
            tokens = tokens[:index]
            break
    return tokens


def remove_tokens_fn(tokens: list[int], tokens_to_remove: list[int]):
    """Remove the tokens."""
    new_tokens = []
    for token in tokens:
        if token not in tokens_to_remove:
            new_tokens.append(token)
    return new_tokens


def random_drop_tokens_fn(tokens: list[int], rate_per_token: float = 0.05):
    new_tokens = []
    for token in tokens:
        if np.random.random() < rate_per_token:
            continue
        new_tokens.append(token)
    return new_tokens


def create_sft_dataset(
    all_data: list[dict], output_path: pathlib.Path, seq: int, tokenizer: Tokenizer
):
    print(f"{output_path=}")
    assert output_path.parent.exists()
    num_exceed = 0
    builder = indexed_dataset.make_builder(
        str(output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=tokenizer.vocab_size,
    )
    for data in all_data:
        tokens = (
            data["prompt_tokens"]
            + data["positive_tokens"]
            + [tokenizer.special_tokens.eos]
        )
        if len(tokens) >= seq:
            tokens = tokens[:seq]
            num_exceed += 1
        else:
            tokens = tokens + [tokenizer.special_tokens.padding] * (seq - len(tokens))
        assert len(tokens) == seq
        builder.add_item(tokens)
    builder.finalize(str(output_path.with_suffix(".idx")))
    print(f"There are {num_exceed} examples exceeding {seq}.")
    print(f"Save {len(all_data)} examples to {output_path}.")


def parse_dataset_fim_sequence(
    sequence: np.ndarray, tokenizer: Tokenizer
) -> tuple[list[int], list[int]]:
    """Parse the dataset sequence."""
    sepcial_tokens = tokenizer.special_tokens
    assert isinstance(sepcial_tokens, RagSpecialTokens)
    tokens = extract_tokens_before_stop_tokens(sequence.tolist(), [sepcial_tokens.eos])
    assert tokens.count(sepcial_tokens.fim_middle) == 1
    index = tokens.index(sepcial_tokens.fim_middle)
    return tokens[: index + 1], tokens[index + 1 :]


def parse_useful_field_from_simple_elden_v1_tokens(
    tokens: list[int], tokenizer: Tokenizer
) -> dict[str, list[int]]:
    """Parse the useful field from simple elden v1 tokens.

    The prefix_tokens is pure and does not contain fim_prefix.
    The suffix_tokens is pure and does not contain fim_suffix and end with fim_middle.
    """
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, RagSpecialTokens)
    sig_end = tokenizer.tokenize_safe("\n# Signature Chunks Finish.\n")
    assert tokens.count(special_tokens.fim_suffix) == 1
    index_fim_suffix = tokens.index(special_tokens.fim_suffix)
    context_prefix_tokens_as_str = " ".join(str(x) for x in tokens[:index_fim_suffix])
    sig_end_as_str = " ".join(str(x) for x in sig_end)
    assert context_prefix_tokens_as_str.count(sig_end_as_str) == 1
    # split the context_prefix_tokens_as_str into two parts, one is end of sig_end_as_str and others
    part_1, part_2 = context_prefix_tokens_as_str.split(sig_end_as_str)
    part_1 += sig_end_as_str
    context_tokens = [int(x) for x in part_1.split(" ") if x]
    assert context_tokens[0] == special_tokens.fim_prefix
    context_tokens = context_tokens[1:]
    path_prefix_tokens = [int(x) for x in part_2.split(" ") if x]
    path_prefix_text = tokenizer.detokenize(path_prefix_tokens)
    assert "\n" in path_prefix_text, path_prefix_text
    path_text, prefix_text = path_prefix_text.split("\n", 1)
    path_tokens = tokenizer.tokenize_safe(path_text) + [special_tokens.newline]
    prefix_tokens = tokenizer.tokenize_safe(prefix_text)
    suffix_tokens = tokens[index_fim_suffix:]
    assert suffix_tokens[0] == special_tokens.fim_suffix
    assert suffix_tokens[-1] == special_tokens.fim_middle
    suffix_tokens = suffix_tokens[1:-1]
    return {
        "context_tokens": context_tokens,
        "path_tokens": path_tokens,
        "prefix_tokens": prefix_tokens,
        "suffix_tokens": suffix_tokens,
    }


def change_prompt_tokens_from_v1_to_v2(tokens: list[int], tokenizer: Tokenizer):
    """Change the prompt tokens from v1 to v2."""
    fields = parse_useful_field_from_simple_elden_v1_tokens(tokens, tokenizer)
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, RagSpecialTokens)
    return (
        fields["context_tokens"]
        + [special_tokens.filename]
        + fields["path_tokens"]
        + [special_tokens.fim_prefix]
        + fields["prefix_tokens"]
        + [special_tokens.fim_suffix]
        + fields["suffix_tokens"]
        + [special_tokens.fim_middle]
    )


def parse_json_data_from_api_response(response: str):
    """Parse the json data from api response."""
    parsed_response = extract_the_last_markdown_block(response)
    if parsed_response is None or not parsed_response.strip():
        raise ValueError(f"Failed to parse the response: {response}")
    return json.loads(parsed_response)


def handle_skip_and_detokenize(
    token_ids: typing.Sequence[int],
    tokenizer: Tokenizer,
    suffix: str,
    closing_strings: typing.Sequence[str] = (
        "'''",
        '"""',
        "'",
        '"',
        "`",
        "]",
        "}",
        ")",
        ">",
    ),
) -> dict[str, str]:
    special_tokens = tokenizer.special_tokens
    assert isinstance(special_tokens, RagSpecialTokens)
    skip_token = special_tokens.skip
    # All skipped suffix characters
    skipped_text = []
    # all characters including detokenized sequence and inserted suffix
    text = []
    # Position to start searching for next skip token
    start = 0
    # Total number of skipped characters
    n_skipped = 0
    # Keep searching for skip token
    while start < len(token_ids):
        try:
            next_skip = token_ids.index(skip_token, start)
        # No more skip tokens
        except ValueError:
            text.append(tokenizer.detokenize(token_ids[start:]))
            break

        text.append(tokenizer.detokenize(token_ids[start:next_skip]))
        # Identify the portion of string skipped
        for i in range(n_skipped, len(suffix)):
            if not suffix[i].isspace():
                # index of first char after whitespaces
                ws_tail = i
                break
        else:
            # there is only white space.  Cannot match skip to suffix.
            break
        for closing in closing_strings:
            if suffix[ws_tail : ws_tail + len(closing)] == closing:
                skipped_chars = suffix[n_skipped : ws_tail + len(closing)]
                text.append(skipped_chars)
                skipped_text.append(skipped_chars)
                n_skipped += len(skipped_chars)
                break
        else:  # be conservative and stop the generation here
            break
        start = next_skip + 1
    replacement_text = "".join(text[1:])
    skipped_suffix = "".join(skipped_text)

    return {
        "text": text[0],
        "skipped_suffix": skipped_suffix,
        "suffix_replacement_text": replacement_text,
    }


def handle_skip_and_concat_with_suffix(
    token_ids: typing.Sequence[int], tokenizer: Tokenizer, suffix: str
):
    if not token_ids:
        return suffix
    token_ids = copy.deepcopy(token_ids)
    suffix = copy.deepcopy(suffix)
    result = handle_skip_and_detokenize(token_ids, tokenizer, suffix)
    length_to_skip = len(result["skipped_suffix"])
    if length_to_skip > 0:
        text = (
            result["text"] + result["suffix_replacement_text"] + suffix[length_to_skip:]
        )
    else:
        assert len(result["suffix_replacement_text"]) == 0
        text = result["text"] + suffix
    return text
