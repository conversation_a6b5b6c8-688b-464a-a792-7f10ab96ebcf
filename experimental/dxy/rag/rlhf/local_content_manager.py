"""The local content manager."""

import json
import pathlib
import typing

import tqdm
import zstandard as zstd

from research.core.types import Document


class PathAndContent(typing.NamedTuple):
    """A named tuple for a blob's path and contents."""

    path: str
    content: str


class LocalContentManager:
    """The local content manager."""

    def __init__(self):
        self._cache: dict[str, PathAndContent] = {}

    def __len__(self):
        return len(self._cache)

    def to_documents(self) -> list[Document]:
        """Convert the content manager to a list of documents."""
        return [
            Document(id=k, text=v.content, path=v.path) for k, v in self._cache.items()
        ]

    def put(self, blob_name: str, path_and_content: PathAndContent):
        """Put the content for the given blob name."""
        if blob_name not in self._cache:
            self._cache[blob_name] = path_and_content
        else:
            assert self._cache[blob_name] == path_and_content

    def get(self, blob_name: str) -> PathAndContent | None:
        """Get the content for the given blob name."""
        return self._cache.get(blob_name)

    def dump(self, data_dir: str | pathlib.Path, bucket: int = 1):
        xdir = pathlib.Path(data_dir)
        xdir.mkdir(parents=False, exist_ok=True)
        print(f"Dumping {len(self._cache)} blobs to {xdir}")
        assert bucket > 0, f"{bucket=}"
        # Compute the bucket index for each blob.
        buckets = [[] for _ in range(bucket)]
        for k, v in self._cache.items():
            # convert the k str into an index
            index = self.hash(k) % bucket
            buckets[index].append((k, v))
        for index, bucket_data in enumerate(buckets):
            xpath = xdir / f"bucket_{index}.jsonl.zst"
            with zstd.open(xpath, "w", encoding="utf-8") as f:
                for k, v in bucket_data:
                    json.dump({"blob_name": k, "path": v.path, "content": v.content}, f)
                    f.write("\n")

    def load(self, data_dir: str | pathlib.Path):
        """Load the content from the given data directory."""
        xdir = pathlib.Path(data_dir)
        bucket_files = sorted(xdir.glob("bucket_*.jsonl.zst"))
        print(f"Loading {len(bucket_files)} buckets from {xdir}")
        for bucket_file in bucket_files:
            print(f"Loading {bucket_file}")
            all_lines = []
            with zstd.open(bucket_file, "r", encoding="utf-8") as f:
                for line in f:
                    all_lines.append(line)
            for line in tqdm.tqdm(
                all_lines, total=len(all_lines), desc="Local Content Manager loading"
            ):
                data = json.loads(line)
                blob_name = data["blob_name"]
                path_and_content = PathAndContent(
                    path=data["path"], content=data["content"]
                )
                if blob_name not in self._cache:
                    self._cache[blob_name] = path_and_content
                else:
                    assert self._cache[blob_name] == path_and_content

    @classmethod
    def hash(cls, blob_name: str) -> int:
        """Get the hash of the given blob name."""
        return abs(hash(blob_name))
