"""Get all the request IDs between 20240801 to 20241231 data for RLHF.

python experimental/dxy/rag/rlhf/note-250102/inspect-request-ids.py
"""

import os
import tqdm
from datasets import Dataset as HFDataset

dataset_rids_path = "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN-rids"
assert os.path.exists(dataset_rids_path)

dataset_rids = HFDataset.load_from_disk(dataset_rids_path)
request_ids: list[str] = []

for data in tqdm.tqdm(dataset_rids, total=len(dataset_rids)):
    rid = "".join([chr(x) for x in data["request_ids"]])
    request_ids.append(rid)
print(f"There are {len(request_ids)} request IDs in {dataset_rids_path}")
