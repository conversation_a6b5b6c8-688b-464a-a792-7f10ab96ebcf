# [Reinforcement Learning from Developer Behaviors: A breakthrough in code generation quality](https://www.augmentcode.com/blog/reinforcement-learning-from-developer-behaviors)

If you want to reproduce the results for RLDB models (such as [QWEN Elden v2.1](https://github.com/augmentcode/augment/pull/17731)), you can follow the following steps.

**Step 1**: Dump the data in a certain period of time such as from 01 Aug 2024 to 12 Jan 2025:
```
python experimental/dxy/rag/rlhf/data/dump-feedback.py --date_range 20240801-20250112
python experimental/dxy/rag/rlhf/data/dump-hindsight.py --sample_limit 0 --user_event_limit 0 --context_size 30 --date_range 20240801-20250112
python experimental/dxy/rag/rlhf/data/dump-user-events.py --date_range 20240801-20250112
```

**Step 2**: Process the data with filtering, aggregation, classification, re-organizing logics.

The following command will process the data between 01 Aug 2024 to 31 Dec 2024 into the reward model training data, which is used to train QWEN v2.1.
```
python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20240801-20241231 --output_file 20240801-1231-QWEN
```

**Step 3**: Process the data via **Step 2** into the RL training data, an example command line is:

```
python experimental/dxy/rag/rlhf/data/create-prompt.py \
    --keep_dogfood \
    --input_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN \
    --output_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN-innocent-prompt-all
```

**Step 4**: Train the reward model, see docstr at [experimental/dxy/rag/configs-rlhf/reward-db-qwv1-bs1024e2.yml](https://github.com/augmentcode/augment/blob/main/experimental/dxy/rag/configs-rlhf/reward-db-qwv1-bs1024e2.yml).

**Step 5**: Apply RL training on top of the SFT model, see docstr at [experimental/dxy/rag/configs-rlhf/onlinedpo-qwenv1.yml](https://github.com/augmentcode/augment/blob/main/experimental/dxy/rag/configs-rlhf/onlinedpo-qwenv1.yml).
