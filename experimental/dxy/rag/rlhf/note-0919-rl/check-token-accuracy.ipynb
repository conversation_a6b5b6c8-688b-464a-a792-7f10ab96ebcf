{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from datasets import Dataset as HFDataset\n", "\n", "cceval_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/cceval_elden_v4_s6k\"\n", ")\n", "hs09_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/hindsight09_elden_v4_s6k\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.fastforward.fwd_utils import get_checkpoint_sha\n", "from research.models.meta_model import GenerationOptions\n", "from research.models.fastforward_models import StarCoder2_FastForward\n", "\n", "\n", "elden_v4_ffw_ckp_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/checkpoints/dxy/sc2-elden/elden-v4-ffw\"\n", ")\n", "model = StarCoder2_FastForward(\n", "    model_path=elden_v4_ffw_ckp_dir,\n", "    checkpoint_path=elden_v4_ffw_ckp_dir,\n", "    checkpoint_sha256=get_checkpoint_sha(elden_v4_ffw_ckp_dir),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "\n", "\n", "def evaluate_dataset(dataset, model, max_seq_len: int = 7936, max_items: int = 1000):\n", "    token_accuracies: list[float] = []\n", "    target_lengths: list[int] = []\n", "    losses: list[float] = []\n", "    for index in range(len(dataset)):\n", "        xdata = dataset[index]\n", "        prompt_tokens = xdata[\"prompt_tokens\"]\n", "        target_tokens = xdata[\"target_tokens\"]\n", "        cur_tokens = prompt_tokens + target_tokens\n", "        target_lengths.append(len(target_tokens))\n", "        if len(cur_tokens) > max_seq_len + 1:\n", "            cur_tokens = cur_tokens[: max_seq_len + 1]\n", "            cur_mask = [True] * len(prompt_tokens) + [False] * (\n", "                max_seq_len + 1 - len(prompt_tokens)\n", "            )\n", "        else:\n", "            cur_mask = (\n", "                [True] * len(prompt_tokens)\n", "                + [False] * len(target_tokens)\n", "                + [True] * (max_seq_len + 1 - len(cur_tokens))\n", "            )\n", "            cur_tokens += [model.tokenizer.special_tokens.padding] * (\n", "                max_seq_len + 1 - len(cur_tokens)\n", "            )\n", "        input_tensor = torch.tensor(cur_tokens[:-1], dtype=torch.long, device=\"cuda\")\n", "        label_tensor = torch.tensor(cur_tokens[1:], dtype=torch.long, device=\"cuda\")\n", "        input_mask = torch.tensor(cur_mask[1:], dtype=torch.bool, device=\"cuda\")\n", "        logits = model.forward_pass_single_logits(input_tensor)\n", "        pred_tokens = logits.argmax(dim=-1)\n", "        correct_tokens = (pred_tokens == label_tensor) & (~input_mask)\n", "        token_accuracy = correct_tokens.float().sum() / (\n", "            (~input_mask).float().sum() + 1e-6\n", "        )\n", "        token_accuracy = token_accuracy.item() * 100\n", "        token_accuracies.append(token_accuracy)\n", "        # Compute the loss\n", "        masked_label_tensor = torch.where(~input_mask, label_tensor, -1)\n", "        loss = torch.nn.functional.cross_entropy(\n", "            logits, masked_label_tensor, ignore_index=-1\n", "        ).item()\n", "        losses.append(loss)\n", "        print(\n", "            f\"{index=:04d} : {token_accuracy=:.2f}%, {loss=:.3f}\"\n", "            f\"\\n\\t\\t\\tmean token accuracy={np.mean(token_accuracies):.2f}%,\"\n", "            f\" mean loss={np.mean(losses):.3f}, mean target length={np.mean(target_lengths):.2f}\"\n", "        )\n", "        if index >= max_items:\n", "            break"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evaluate_dataset(hs09_dataset, model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["evaluate_dataset(cceval_dataset, model)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}