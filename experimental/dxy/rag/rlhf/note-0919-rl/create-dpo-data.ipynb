{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}], "source": ["import argparse\n", "import concurrent.futures as futures\n", "import copy\n", "import json\n", "import pathlib\n", "import tqdm\n", "from experimental.dxy.rag.rlhf.shared_lib import extract_tokens_before_stop_tokens\n", "from research.core import utils_for_log\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "def parse_json_file(path: pathlib.Path):\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        xdata = json.load(f)\n", "        for xdict in xdata:\n", "            prompt_tokens = json.loads(xdict[\"prompt_tokens\"])\n", "            target_tokens = json.loads(xdict[\"target_tokens\"])\n", "            candidates: list[list[int]] = json.loads(xdict[\"candidates\"])\n", "            target_score = xdict[\"reward@target_score\"]\n", "            candidates_score = json.loads(xdict[\"reward@candidates_score\"])\n", "            assert len(candidates) == len(candidates_score)\n", "            yield {\n", "                \"prompt_tokens\": prompt_tokens,\n", "                \"target_tokens\": target_tokens,\n", "                \"target_score\": target_score,\n", "                \"candidates\": candidates,\n", "                \"candidates_score\": candidates_score,\n", "            }"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["5000it [10:42,  7.78it/s]\n"]}], "source": ["import pathlib\n", "\n", "raw_dpo_data_dir = pathlib.Path(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data/scores-1000K-in-json\"\n", ")\n", "all_data = []\n", "for json_file in tqdm.tqdm(raw_dpo_data_dir.glob(\"*.json\")):\n", "    for x in parse_json_file(json_file):\n", "        all_data.append(x)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mean_diff_score=0.18615022571812634, np.median(diff_scores)=0.00 np.std(diff_scores)=0.9187620216799892\n", "num_target_score_larger_than_others=4861\n", "170730/999179 > mean_diff_score=0.18615022571812634\n"]}], "source": ["import numpy as np\n", "\n", "diff_scores = []\n", "num_target_score_larger_than_others = 0\n", "for data in all_data:\n", "    diff_scores.append(max(data[\"candidates_score\"]) - min(data[\"candidates_score\"]))\n", "    if data[\"target_score\"] >= max(data[\"candidates_score\"]):\n", "        num_target_score_larger_than_others += 1\n", "mean_diff_score = np.mean(diff_scores)\n", "print(f\"{mean_diff_score=}, {np.median(diff_scores)=:.2f} {np.std(diff_scores)=}\")\n", "print(f\"{num_target_score_larger_than_others=}\")\n", "\n", "num = 0\n", "for diff_score in diff_scores:\n", "    if diff_score > mean_diff_score:\n", "        num += 1\n", "print(f\"{num}/{len(diff_scores)} > {mean_diff_score=}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["170730/999179\n"]}], "source": ["filtered_data = []\n", "for data in all_data:\n", "    diff_score = max(data[\"candidates_score\"]) - min(data[\"candidates_score\"])\n", "    if diff_score > mean_diff_score:\n", "        filtered_data.append(data)\n", "print(f\"{len(filtered_data)}/{len(all_data)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset as HFDataset\n", "\n", "\n", "special_tokens = tokenizer.special_tokens\n", "all_prompt_tokens = []\n", "all_pos_tokens = []\n", "all_neg_tokens = []\n", "\n", "for data in filtered_data:\n", "    prompt_tokens = data[\"prompt_tokens\"]\n", "    candidate_w_score = list(zip(data[\"candidates\"], data[\"candidates_score\"]))\n", "    candidate_w_score.sort(key=lambda x: x[1], reverse=True)\n", "    pos_tokens = candidate_w_score[0][0]\n", "    neg_tokens = candidate_w_score[-1][0]\n", "    if len(pos_tokens) < 3 or len(neg_tokens) < 3:\n", "        continue\n", "    if pos_tokens[-1] in [special_tokens.eos, special_tokens.padding]:\n", "        pos_tokens = pos_tokens[:-1]\n", "    if neg_tokens[-1] in [special_tokens.eos, special_tokens.padding]:\n", "        neg_tokens = neg_tokens[:-1]\n", "    all_prompt_tokens.append(prompt_tokens)\n", "    all_pos_tokens.append(pos_tokens)\n", "    all_neg_tokens.append(neg_tokens)\n", "print(f\"There are {len(all_prompt_tokens)} examples\")\n", "\n", "\n", "dataset = HFDataset.from_dict(\n", "    {\n", "        \"prompt_tokens\": all_prompt_tokens,\n", "        \"pos_tokens\": all_pos_tokens,\n", "        \"neg_tokens\": all_neg_tokens,\n", "    }\n", ")\n", "dataset = dataset.train_test_split(test_size=0.1)\n", "train_dataset = dataset[\"train\"]\n", "valid_dataset = dataset[\"test\"]\n", "output_dir = \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data/\"\n", "train_dataset.save_to_disk(f\"{output_dir}/dpo-train\")\n", "valid_dataset.save_to_disk(f\"{output_dir}/dpo-valid\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"{pos_tokens=}\")\n", "print(f\"{candidate_w_score[0][1]=}\")\n", "\n", "print(f\"{neg_tokens=}\")\n", "print(f\"{candidate_w_score[-1][1]=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(tokenizer.detokenize(prompt_tokens))\n", "print(tokenizer.detokenize(pos_tokens))\n", "print(tokenizer.detokenize(neg_tokens))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}