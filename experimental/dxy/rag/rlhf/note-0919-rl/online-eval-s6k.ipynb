{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "import tqdm\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:datasets:PyTorch version 2.3.0+cu121 available.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 7435 HindsightOutput elements.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 7435/7435 [00:00<00:00, 91853.02it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 2 examples.\n", "save_dir=PosixPath('/mnt/efs/augment/user/dxy/datasets/rlhf/20240930')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2ec069e956e144a3b39cc0b2c921d985", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/7435 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create Elden V4 on Hindsight-09\n", "import pathlib\n", "from datasets import Dataset as HFDataset\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "\n", "hs_09_run_result_path = \"/mnt/efs/augment/eval/jobs/3sT3nELb/000_starcoder2_fastforward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "hs_09_run_results = [\n", "    HindsightOutput(**x) for x in read_jsonl_zst(hs_09_run_result_path)\n", "]\n", "print(f\"There are {len(hs_09_run_results)} HindsightOutput elements.\")\n", "\n", "hs_09_run_data = {\"prompt_tokens\": [], \"target_tokens\": []}\n", "for x in tqdm.tqdm(hs_09_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    hs_09_run_data[\"prompt_tokens\"].append(x.prompt_tokens)\n", "    hs_09_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth))\n", "    # hs_09_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth) + [tokenizer.special_tokens.eos])\n", "print(f\"There are {len(hs_09_run_data)} examples.\")\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/\")\n", "save_dir.mkdir(parents=False, exist_ok=True)\n", "print(f\"{save_dir=}\")\n", "hf_dataset = HFDataset.from_dict(hs_09_run_data)\n", "hf_dataset.save_to_disk(save_dir / \"hindsight09_elden_v4_s6k\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create CCEval Data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 9711 CCEvalOutput elements.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 9711/9711 [00:00<00:00, 126096.74it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 2 examples.\n", "save_dir=PosixPath('/mnt/efs/augment/user/dxy/datasets/rlhf/20240930')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e820e849c3048eba0dca075aabd3031", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/9711 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pathlib\n", "from datasets import Dataset as HFDataset\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.cceval import CCEvalOutput\n", "\n", "cceval_run_result_path = \"/mnt/efs/augment/eval/jobs/oAHrPkTr/000_starcoder2_fastforward_CCEval_completed_patches.jsonl.zst\"\n", "cceval_run_results = [CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)]\n", "print(f\"There are {len(cceval_run_results)} CCEvalOutput elements.\")\n", "\n", "cceval_run_data = {\"prompt_tokens\": [], \"target_tokens\": []}\n", "for x in tqdm.tqdm(cceval_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    cceval_run_data[\"prompt_tokens\"].append(x.prompt_tokens)\n", "    cceval_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth))\n", "    # cceval_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth) + [tokenizer.special_tokens.eos])\n", "print(f\"There are {len(cceval_run_data)} examples.\")\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/20240930/\")\n", "save_dir.mkdir(parents=False, exist_ok=True)\n", "print(f\"{save_dir=}\")\n", "cceval_dataset = HFDataset.from_dict(cceval_run_data)\n", "cceval_dataset.save_to_disk(save_dir / \"cceval_elden_v4_s6k\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Hindsight Eval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "\n", "hindsight_run_result_path = \"/mnt/efs/augment/eval/jobs/EYDpMPWU/000_starcoder2_fastforward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "hindsight_run_results = [\n", "    HindsightOutput(**x) for x in read_jsonl_zst(hindsight_run_result_path)\n", "]\n", "print(f\"There are {len(hindsight_run_results)} HindsightOutput elements.\")\n", "\n", "hindsight_run_data = []\n", "for x in tqdm.tqdm(hindsight_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    hindsight_run_data.append(\n", "        {\n", "            \"prompt_tokens\": x.prompt_tokens,\n", "            \"positive_tokens\": tokenizer.tokenize_safe(x.ground_truth),\n", "        }\n", "    )\n", "print(f\"There are {len(hindsight_run_data)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from experimental.dxy.rag.rlhf.shared_lib import create_sft_dataset\n", "\n", "output = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/hindsight06_elden_s6k\"\n", ")\n", "create_sft_dataset(hindsight_run_data, output, 7936 + 1, tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from megatron.data import indexed_dataset\n", "\n", "\n", "cceval_dataset = indexed_dataset.MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/cceval_elden_s6k\",\n", "    skip_warmup=True,\n", ")\n", "max_token = []\n", "for x in cceval_dataset:\n", "    max_token.append(x.max())\n", "print(f\"CCEval's max token ID is {np.max(max_token).item()}\")\n", "\n", "\n", "hindsight_dataset = indexed_dataset.MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/hindsight06_elden_s6k\",\n", "    skip_warmup=True,\n", ")\n", "max_token = []\n", "for x in hindsight_dataset:\n", "    max_token.append(x.max())\n", "print(f\"<PERSON>ndsight's max token ID is {np.max(max_token).item()}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}