{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from datasets import Dataset as HFDataset\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202410-202411-v2\"\n", ")\n", "print(f\"There are {len(dataset)} items in dataset.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "num_target_tokens = []\n", "for index in range(len(dataset)):\n", "    data = dataset[index]\n", "    num_target_tokens.append(len(data[\"target_tokens\"]))\n", "num_target_tokens = np.array(num_target_tokens)\n", "print(f\"Mean: {np.mean(num_target_tokens)}, Std: {np.std(num_target_tokens)}\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["data = dataset[-10]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data[\"prompt_tokens\"])\n", "print(data[\"target_tokens\"])\n", "print(data[\"is_positive\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(tokenizer.detokenize(data[\"prompt_tokens\"]))\n", "print(tokenizer.detokenize(data[\"target_tokens\"]))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}