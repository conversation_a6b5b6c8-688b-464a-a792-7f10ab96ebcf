{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}], "source": ["import argparse\n", "import concurrent.futures as futures\n", "import copy\n", "import json\n", "import pathlib\n", "import tqdm\n", "\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionDatum,\n", ")\n", "from base.prompt_format.common import PromptChunk\n", "from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict\n", "from experimental.dxy.rag.rlhf.local_content_manager import (\n", "    LocalContentManager,\n", ")\n", "from experimental.dxy.rag.rlhf.shared_lib import extract_tokens_before_stop_tokens\n", "from research.core import utils_for_log\n", "from research.core.model_input import ModelInput\n", "from research.core.recency_info import convert_from_datasets_recency_info\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "def parse_json_file(path: pathlib.Path):\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        xdata = json.load(f)\n", "        ground_truth_tokens_w_pause_skip = xdata[\"ground_truth_tokens_w_pause_skip\"]\n", "        datum = HindsightCompletionDatum.schema().load(xdata[\"datum\"])\n", "        prompt_tokens = xdata[\"prompt_tokens\"]\n", "        return {\n", "            \"datum\": datum,\n", "            \"prompt_tokens\": prompt_tokens,\n", "            \"ground_truth_tokens_w_pause_skip\": ground_truth_tokens_w_pause_skip,\n", "        }"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 4273 items in /mnt/efs/augment/user/dxy/hindsight/sft-202405-202409/.\n", "2024-09-19-at-16:34:24 there are 4273 items.\n"]}], "source": ["input_folders = [\n", "    \"/mnt/efs/augment/user/dxy/hindsight/sft-202405-202409/\",\n", "]\n", "all_json_files = []\n", "for input_folder in input_folders:\n", "    target_folder = pathlib.Path(input_folder) / \"token-data-w-skip-pause\"\n", "    cur_json_files = list(target_folder.glob(\"*-*.json\"))\n", "    print(f\"There are {len(cur_json_files)} items in {input_folder}.\")\n", "    all_json_files.extend(cur_json_files)\n", "print(f\"{utils_for_log.time_string()} there are {len(all_json_files)} items.\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Submit jobs:   0%|          | 0/4273 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["Submit jobs: 100%|██████████| 4273/4273 [00:11<00:00, 369.50it/s]\n", "Decompress: 100%|██████████| 4273/4273 [03:19<00:00, 21.42it/s] \n"]}], "source": ["all_data = []\n", "with futures.ThreadPoolExecutor(max_workers=32) as executor:\n", "    all_jobs = []\n", "    for index, json_file in tqdm.tqdm(\n", "        enumerate(all_json_files), total=len(all_json_files), desc=\"Submit jobs\"\n", "    ):\n", "        all_jobs.append(executor.submit(parse_json_file, json_file))\n", "    for job in tqdm.tqdm(\n", "        futures.as_completed(all_jobs), total=len(all_json_files), desc=\"Decompress\"\n", "    ):\n", "        all_data.append(job.result())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["218/4273 examples have skip.\n", "293/4273 examples have pause.\n", "0/4273 examples have match nostrict.\n", "Kept 2653/4273 examples.\n"]}], "source": ["num_skip = 0\n", "num_pause = 0\n", "num_match_nostrict = 0\n", "keep_data = []\n", "for data in all_data:\n", "    ground_truth_tokens_w_pause_skip = data[\"ground_truth_tokens_w_pause_skip\"]\n", "    if tokenizer.special_tokens.skip in ground_truth_tokens_w_pause_skip:\n", "        num_skip += 1\n", "    if tokenizer.special_tokens.pause in ground_truth_tokens_w_pause_skip:\n", "        num_pause += 1\n", "    pos_tokens = ground_truth_tokens_w_pause_skip\n", "    if len(pos_tokens) < 4:\n", "        continue\n", "    keep_data.append(\n", "        {\n", "            \"prompt_tokens\": data[\"prompt_tokens\"],\n", "            \"positive_tokens\": pos_tokens,\n", "        }\n", "    )\n", "print(f\"{num_skip}/{len(all_data)} examples have skip.\")\n", "print(f\"{num_pause}/{len(all_data)} examples have pause.\")\n", "print(f\"{num_match_nostrict}/{len(all_data)} examples have match nostrict.\")\n", "print(f\"Kept {len(keep_data)}/{len(all_data)} examples.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["f\n"]}], "source": ["# print(tokenizer.detokenize(keep_data[0][\"prompt_tokens\"][-100:]))\n", "print(tokenizer.detokenize(keep_data[0][\"positive_tokens\"]))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 0 examples exceeding 7937.\n", "Save 2653 examples to /mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_202405_202409_elden_s6k.\n"]}], "source": ["from megatron.data import indexed_dataset\n", "\n", "output_path = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_202405_202409_elden_s6k\"\n", ")\n", "\n", "seq = 7936 + 1\n", "num_exceed = 0\n", "\n", "builder = indexed_dataset.make_builder(\n", "    str(output_path.with_suffix(\".bin\")),\n", "    impl=\"mmap\",\n", "    vocab_size=tokenizer.vocab_size,\n", ")\n", "for data in keep_data:\n", "    tokens = (\n", "        data[\"prompt_tokens\"] + data[\"positive_tokens\"] + [tokenizer.special_tokens.eos]\n", "    )\n", "    if len(tokens) >= seq:\n", "        tokens = tokens[:seq]\n", "        num_exceed += 1\n", "    else:\n", "        tokens = tokens + [tokenizer.special_tokens.padding] * (seq - len(tokens))\n", "    assert len(tokens) == seq\n", "    builder.add_item(tokens)\n", "builder.finalize(str(output_path.with_suffix(\".idx\")))\n", "print(f\"There are {num_exceed} examples exceeding {seq}.\")\n", "print(f\"Save {len(keep_data)} examples to {output_path}.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}