"""Create the rlhf dataset for completion models.

python experimental/dxy/rag/rlhf/create-rlhf-dataset.py
"""

import json
import pathlib
from datetime import datetime, timezone

import torch
import tqdm
import zstandard as zstd
from datasets import Dataset as HFDataset

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDatum,
)
from base.prompt_format.common import PromptChunk
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.shared_lib import (
    change_prompt_tokens_from_v1_to_v2,
    extract_tokens_before_stop_tokens,
    random_drop_tokens_fn,
    remove_tokens_fn,
)
from research.core import utils_for_log
from research.core.model_input import ModelInput
from research.core.recency_info import convert_from_datasets_recency_info
from research.eval.harness.systems.abs_system import CompletionResult
from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem


def filter_data(all_data: list[dict], tokenizer: StarCoder2Tokenizer):
    num_skip = 0
    num_pause = 0
    num_match_argmax = 0
    num_match_random1 = 0
    num_match_random2 = 0
    num_match_nostrict = 0
    special_tokens = tokenizer.special_tokens
    keep_data = []
    for index, data in enumerate(all_data):
        generated_tokens = extract_tokens_before_stop_tokens(
            data["output_argmax"].generated_tokens,
            [special_tokens.eos, special_tokens.padding],
        )
        output_random_1_tokens = data["output_random_1_tokens"]
        output_random_2_tokens = data["output_random_2_tokens"]
        ground_truth_tokens_w_pause_skip = data["ground_truth_tokens_w_pause_skip"]
        if special_tokens.skip in ground_truth_tokens_w_pause_skip:
            num_skip += 1
        if special_tokens.pause in ground_truth_tokens_w_pause_skip:
            num_pause += 1
        if ground_truth_tokens_w_pause_skip == generated_tokens:
            num_match_argmax += 1
        if ground_truth_tokens_w_pause_skip == output_random_1_tokens:
            num_match_random1 += 1
        if ground_truth_tokens_w_pause_skip == output_random_2_tokens:
            num_match_random2 += 1
        if (
            tokenizer.detokenize(generated_tokens).strip()
            == tokenizer.detokenize(ground_truth_tokens_w_pause_skip).strip()
        ):
            num_match_nostrict += 1
        # Start to filter out the data.
        pos_tokens = ground_truth_tokens_w_pause_skip
        if ground_truth_tokens_w_pause_skip != generated_tokens:
            neg_tokens = generated_tokens
        elif ground_truth_tokens_w_pause_skip != output_random_1_tokens:
            neg_tokens = output_random_1_tokens
        elif ground_truth_tokens_w_pause_skip != output_random_2_tokens:
            neg_tokens = output_random_2_tokens
        elif special_tokens.skip in ground_truth_tokens_w_pause_skip:
            neg_tokens = random_drop_tokens_fn(generated_tokens)
            if neg_tokens == generated_tokens:
                neg_tokens = remove_tokens_fn(generated_tokens, [special_tokens.skip])
        else:
            continue
        assert pos_tokens != neg_tokens
        prompt_tokens = data["output_argmax"].prompt_tokens
        try:
            new_prompt_tokens = change_prompt_tokens_from_v1_to_v2(
                prompt_tokens, tokenizer
            )
        except Exception:
            print(
                f"Failed to convert prompt tokens from v1 to v2 for {index}-th example."
            )
            continue
        keep_data.append(
            {
                "prompt_tokens": new_prompt_tokens,
                "positive_tokens": pos_tokens,
                "negative_tokens": neg_tokens,
                "user_id": data["user_id"],
                "timestamp": data["timestamp"],
            }
        )
    print(f"{num_skip}/{len(all_data)} examples have skip.")
    print(f"{num_pause}/{len(all_data)} examples have pause.")
    print(f"{num_match_argmax}/{len(all_data)} examples have match argmax.")
    print(f"{num_match_random1}/{len(all_data)} examples have match random1.")
    print(f"{num_match_random2}/{len(all_data)} examples have match random2.")
    print(f"{num_match_nostrict}/{len(all_data)} examples have match nostrict.")
    print(f"Kept {len(keep_data)}/{len(all_data)} examples.")
    num_skip, num_pause = 0, 0
    for data in keep_data:
        if tokenizer.special_tokens.skip in data["positive_tokens"]:
            num_skip += 1
        if tokenizer.special_tokens.pause in data["positive_tokens"]:
            num_pause += 1
        assert data["positive_tokens"] != data["negative_tokens"]
    print(f"{num_skip}/{len(keep_data)} examples have skip.")
    print(f"{num_pause}/{len(keep_data)} examples have pause.")
    return keep_data


def parse_json_file(path: pathlib.Path):
    with open(path, "r", encoding="utf-8") as f:
        xdata = json.load(f)
        output_random_1_tokens = xdata["output_random_1_tokens"]
        output_random_2_tokens = xdata["output_random_2_tokens"]
        ground_truth_tokens_w_pause_skip = xdata["ground_truth_tokens_w_pause_skip"]
        # datum = HindsightCompletionDatum.schema().load(xdata["datum"])
        output_argmax = CompletionResult.schema().load(xdata["output_argmax"])
        # blob_names = xdata["blob_names"]
        output_random_1_tokens = extract_tokens_before_stop_tokens(
            output_random_1_tokens,
            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],
        )
        output_random_2_tokens = extract_tokens_before_stop_tokens(
            output_random_2_tokens,
            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],
        )
        return {
            # "datum": datum,
            "user_id": xdata["datum"]["completion"]["user_id"],
            "timestamp": datetime.fromtimestamp(
                xdata["datum"]["completion"]["request"]["timestamp"], tz=timezone.utc
            ).isoformat(),
            # "blob_names": blob_names,
            "output_argmax": output_argmax,
            "ground_truth_tokens_w_pause_skip": ground_truth_tokens_w_pause_skip,
            "output_random_1_tokens": output_random_1_tokens,
            "output_random_2_tokens": output_random_2_tokens,
        }


def keep_data_by_date(all_data: list[dict], dates: list[str]):
    results: list[dict] = []
    for data in all_data:
        stay = False
        for date in dates:
            if data["timestamp"].startswith(date):
                stay = True
                break
        if stay:
            results.append(data)
    print(f"Kept {len(results)}/{len(all_data)} examples by keeping {dates=}.")
    return results


def remove_data_by_date(all_data: list[dict], dates: list[str]):
    results: list[dict] = []
    for data in all_data:
        remove = False
        for date in dates:
            if data["timestamp"].startswith(date):
                remove = True
        if not remove:
            results.append(data)
    print(f"Kept {len(results)}/{len(all_data)} examples by removing {dates=}.")
    return results


if __name__ == "__main__":
    tokenizer = StarCoder2Tokenizer()

    input_folders = [
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard",
        "/mnt/efs/augment/user/dxy/hindsight/2024-08-2024-09/aitutor-turing",
    ]
    all_json_files = []
    for input_folder in input_folders:
        target_folder = pathlib.Path(input_folder) / "token-data-w-skip-pause"
        cur_json_files = list(target_folder.glob("*-*.json"))
        print(f"There are {len(cur_json_files)} items in {input_folder}.")
        all_json_files.extend(cur_json_files)
    print(f"{utils_for_log.time_string()} there are {len(all_json_files)} items.")

    all_data = []
    # with futures.ThreadPoolExecutor(max_workers=128) as executor:
    #     all_jobs = []
    #     for index, json_file in tqdm.tqdm(
    #         enumerate(all_json_files), total=len(all_json_files), desc="Submit jobs"
    #     ):
    #         all_jobs.append(executor.submit(parse_json_file, json_file))
    #     for job in tqdm.tqdm(
    #         futures.as_completed(all_jobs), total=len(all_json_files), desc="Decompress"
    #     ):
    #         all_data.append(job.result())
    for json_file in tqdm.tqdm(
        all_json_files, total=len(all_json_files), desc="Decompress"
    ):
        all_data.append(parse_json_file(json_file))

    # Filter the data
    ketp_data = filter_data(all_data, tokenizer)

    eval_dates_small = ["2024-05-01", "2024-05-15", "2024-06-01", "2024-06-15"]
    eval_dates_large = eval_dates_small + [
        "2024-07-01",
        "2024-07-15",
        "2024-08-01",
        "2024-08-15",
    ]
    eval_folder_0912_small = (
        "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-eval-small-v2"
    )
    eval_data_small = keep_data_by_date(ketp_data, eval_dates_small)
    eval_dataset_small = HFDataset.from_dict(
        {
            "prompt_tokens": [x["prompt_tokens"] for x in eval_data_small],
            "pos_tokens": [x["positive_tokens"] for x in eval_data_small],
            "neg_tokens": [x["negative_tokens"] for x in eval_data_small],
        }
    )
    eval_dataset_small.save_to_disk(eval_folder_0912_small)
    print(f"Save {len(eval_dataset_small)} items into {eval_folder_0912_small}.")

    eval_folder_0912_large = (
        "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-eval-large-v2"
    )
    eval_data_large = keep_data_by_date(ketp_data, eval_dates_large)
    eval_dataset_large = HFDataset.from_dict(
        {
            "prompt_tokens": [x["prompt_tokens"] for x in eval_data_large],
            "pos_tokens": [x["positive_tokens"] for x in eval_data_large],
            "neg_tokens": [x["negative_tokens"] for x in eval_data_large],
        }
    )
    eval_dataset_large.save_to_disk(eval_folder_0912_large)
    print(f"Save {len(eval_dataset_large)} items into {eval_folder_0912_large}.")

    train_folder_0912 = "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2"
    train_data = remove_data_by_date(ketp_data, eval_dates_large)
    train_dataset = HFDataset.from_dict(
        {
            "prompt_tokens": [x["prompt_tokens"] for x in train_data],
            "pos_tokens": [x["positive_tokens"] for x in train_data],
            "neg_tokens": [x["negative_tokens"] for x in train_data],
        }
    )
    train_dataset.save_to_disk(train_folder_0912)
    print(f"Save {len(train_dataset)} items into {train_folder_0912}.")
