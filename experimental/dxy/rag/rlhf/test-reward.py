"""Test the reward model.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/rag/rlhf/test-reward.py
"""

import numpy as np
from datasets import Dataset as HFDataset

from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.reward_system_lib import (
    RewardSystemAbsoluteScorer,
    RewardSystemZeroShotAPI,
)
from research.core import utils_for_log

if __name__ == "__main__":
    train_dataset = HFDataset.load_from_disk(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2"
    )
    valid_dataset = HFDataset.load_from_disk(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-eval-large-v2"
    )

    tokenizer = StarCoder2Tokenizer()
    # system = RewardSystemAbsoluteScorer(
    #     "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HD-ffw",
    #     model_name="starcoder2_fastforward",
    #     tokenizer=tokenizer,
    # )
    # system = RewardSystemAbsoluteScorer(
    #     "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HONLY-ffw-fp8",
    #     model_name="starcoder2_fp8_fastforward",
    #     tokenizer=tokenizer,
    # )
    system = RewardSystemAbsoluteScorer(
        "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8",
        model_name="starcoder2_fp8_fastforward",
        tokenizer=tokenizer,
    )
    system.load()
    # system = RewardSystemZeroShotAPI(tokenizer, debug=True)
    correctness = []
    for index in range(len(valid_dataset)):
        xdata = valid_dataset[index]
        prompt_tokens = xdata["prompt_tokens"]
        pos_tokens = xdata["pos_tokens"]
        neg_tokens = xdata["neg_tokens"]
        reward = system.compare(prompt_tokens, pos_tokens, neg_tokens)
        pos_larger_than_neg = reward > 0
        correctness.append(pos_larger_than_neg)
        print(
            f"{index}/{len(valid_dataset)}: {reward:.2f} {np.mean(correctness).item()*100:.2f}%"
        )
