"""Download the all data including blobs for the hindsight.

2024-05 ~ 2024-06
python experimental/dxy/rag/rlhf/analyze_chunks.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood


2024-06 ~ 2024-07
python experimental/dxy/rag/rlhf/analyze_chunks.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-mercor \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard

2024-07 ~ 2024-08
python experimental/dxy/rag/rlhf/analyze_chunks.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-mercor \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard
"""

import argparse
import collections
import concurrent.futures as futures
import json
import pathlib

import tqdm
import zstandard as zstd

from base.datasets.hindsight_completion_dataset import HindsightCompletionDatum
from research.core import utils_for_log


def decompress_line(line: str):
    """Decompress a line."""
    data = json.loads(line)
    assert "datum" in data and "blob_names" in data
    return {
        "datum": HindsightCompletionDatum.schema().load(data["datum"]),
        "blob_names": data["blob_names"],
    }


def decompress(all_lines: list[str], max_threads: int = 128) -> list[dict]:
    """Decompress the data via multiple threads."""
    results = []
    total = len(all_lines)
    with futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        all_jobs = []
        for line in tqdm.tqdm(all_lines, total=total, desc="Submit jobs"):
            all_jobs.append(executor.submit(decompress_line, line))
        for job in tqdm.tqdm(
            futures.as_completed(all_jobs), total=total, desc="Decompress"
        ):
            results.append(job.result())
    return results


def count_chunk_fn(datum: HindsightCompletionDatum) -> dict[str, int]:
    """Get the recency chunks."""
    origin2num = collections.defaultdict(lambda: 0)
    for chunk in datum.completion.response.retrieved_chunks:
        origin2num[chunk.origin] += 1
    if datum.completion.request.recency_info is not None:
        origin2num["has_recency_info"] += 1
    return dict(origin2num)


def process_hindsight(input_folder: pathlib.Path):
    """Process the hindsight data."""

    local_datum_path = input_folder / "local-datum.jsonl.zst"
    assert local_datum_path.exists()
    print(f"{utils_for_log.time_string()} Processing {input_folder}")
    all_lines = []
    with zstd.open(local_datum_path, "r", encoding="utf-8") as f:
        for line in f:
            all_lines.append(line)
    print(
        f"{utils_for_log.time_string()} Load {len(all_lines)} lines from {local_datum_path}."
    )
    all_datum = decompress(all_lines)
    print(f"{utils_for_log.time_string()} Finish decompress {len(all_datum)} lines")

    examples_w_chunks = collections.defaultdict(lambda: 0)
    for datum_docids in tqdm.tqdm(all_datum, desc="Analyze chunks"):
        datum: HindsightCompletionDatum = datum_docids["datum"]
        chunk_counts = count_chunk_fn(datum)
        for chunk_origin in chunk_counts.keys():
            examples_w_chunks[chunk_origin] += 1
    print(f"{utils_for_log.time_string()} Finish processing {len(all_datum)} examples.")
    for origin, num in examples_w_chunks.items():
        print(f"{utils_for_log.time_string()} {origin:20s} : {num:4d} examples.")
    print("\n\n")


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=pathlib.Path,
        default=None,
        nargs="+",
        required=True,
        help="The input file",
    )
    args = parser.parse_args()
    for input_folder in args.input:
        assert input_folder.exists()
        process_hindsight(input_folder)


if __name__ == "__main__":
    main()
