"""Data pipeline for DPO data.

python experimental/dxy/rag/rlhf/spark_dpo_data.py
"""

import argparse
import json
import logging
import os
import pathlib
import time
from typing import Iterator

import torch

from base.fastforward.fwd_utils import get_ckp_sha
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from research.core.utils_for_log import time_string
from research.core.utils_for_str import get_first_n_lines
from research.data.rag import common
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.models.meta_model import GenerationOptions

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

ROOT_URL = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/dpo-data"


def int2abbrv(x: int) -> str:
    return f"{x//1000}K"


class GenerateCandidatesViaLLM:
    """Generate candidates via LLM."""

    def __init__(
        self,
        llm_ckp: str,
        temprature: float = 1.0,
        top_p: float = 0.99,
        max_trials: int = 5,
        tokenizer: StarCoder2Tokenizer | None = None,
    ):
        self.llm_ckp = pathlib.Path(llm_ckp)
        # self.reward_ckp = pathlib.Path(reward_ckp)
        assert self.llm_ckp.exists()
        # assert self.reward_ckp.exists()
        self.temprature = temprature
        self.top_p = top_p
        self.max_trials = max_trials
        self.max_generated_tokens = 256
        self.tokenizer = tokenizer
        assert self.tokenizer is not None
        self._llm = None
        # self._reward = None
        self._unpack_fn = None

    def _lazy_init_model(self):
        # assert torch.cuda.is_available()
        # for i in range(torch.cuda.device_count()):
        #     logger.info(f"GPU {i}: {torch.cuda.get_device_name(i)}")
        # Imports are here to avoid CUDA re-initialization RuntimeError.
        from research.eval.harness.factories import create_model

        if self._llm is None:
            self._llm = create_model(
                {
                    "name": "fastbackward",
                    "checkpoint_path": self.llm_ckp,
                    "seq_length": 7936,
                    "model_parallel_size": 1,
                }
            )
            self._llm.load()
        # Enforce to use the FP8 model for efficiency.
        # from research.models.fastforward_models import StarCoder2FP8_FastForward

        # logger.info("Imported StarCoder2FP8_FastForward!")
        # for i in range(1000):
        #     time.sleep(5)
        #     logger.info(f"Waiting {i} for 5 seconds...")
        assert self.tokenizer is not None
        # if self._llm is None:
        #     self._llm = StarCoder2FP8_FastForward(
        #         self.llm_ckp,
        #         self.llm_ckp,
        #         checkpoint_sha256=get_ckp_sha(self.llm_ckp),
        #     )

        # if self._reward is None:
        #     self._reward = StarCoder2FP8_FastForward(
        #         self.llm_ckp,
        #         self.llm_ckp,
        #         checkpoint_sha256=get_ckp_sha(self.llm_ckp),
        #     )
        if self._unpack_fn is None:
            self._unpack_fn = common.unpack_unpad_tokens_fn(self.tokenizer)

    def __call__(
        self,
        prompt_tokens: bytearray,
    ) -> Iterator[dict]:
        self._lazy_init_model()
        assert self._unpack_fn is not None
        assert self.tokenizer is not None
        assert self._llm is not None
        # assert self._reward is not None
        special_tokens = self.tokenizer.special_tokens
        unpacked_data = next(self._unpack_fn(prompt_tokens))  # type: ignore
        tokens: list[int] = unpacked_data["prompt_tokens"]
        assert isinstance(tokens, list)
        assert tokens.count(special_tokens.fim_middle) == 1
        fim_middle_index = tokens.index(special_tokens.fim_middle)
        ptokens = tokens[: fim_middle_index + 1]
        target_tokens = tokens[fim_middle_index + 1 :]
        if target_tokens[-1] == special_tokens.eos:
            target_tokens = target_tokens[:-1]
        # Run number of trials.
        # trials: list[tuple[list[int], float]] = []
        candidates: list[list[int]] = []
        for idx in range(self.max_trials):
            if idx == 0:
                options = GenerationOptions(
                    max_generated_tokens=self.max_generated_tokens,
                    temperature=0,
                    top_p=None,
                    stop_tokens=[special_tokens.eos, special_tokens.padding],
                )
            else:
                options = GenerationOptions(
                    max_generated_tokens=self.max_generated_tokens,
                    temperature=self.temprature,
                    top_p=self.top_p,
                    stop_tokens=[special_tokens.eos, special_tokens.padding],
                )
            llm_output = self._llm.raw_generate_tokens(
                ptokens,
                options,
            )
            candidates.append(llm_output.tokens)
            # cur_out_tokens = llm_output.tokens
            # if cur_out_tokens[-1] == special_tokens.eos:
            #     cur_out_tokens = cur_out_tokens[:-1]
            # # Run the reward model.
            # outputs = self._reward.raw_generate_tokens(
            #     ptokens + cur_out_tokens,
            #     GenerationOptions(temperature=0.0, max_generated_tokens=1),
            # )
            # token_prob = outputs.logits[0].softmax(dim=-1)
            # good_score = token_prob[self.tokenizer.special_tokens.good].item()  # type: ignore
            # bad_score = token_prob[self.tokenizer.special_tokens.bad].item()  # type: ignore
            # reward = good_score - bad_score
            # trials.append((cur_out_tokens, reward))
            # # logging
            # first_line = get_first_n_lines(
            #     self.tokenizer.detokenize(cur_out_tokens), n=1
            # )
            logger.info(
                f"Trial {idx}/{self.max_trials}: {len(llm_output.tokens)=} tokens"
            )
        # trials.sort(key=lambda x: x[1], reverse=True)
        # trials_as_dict = [
        #     {"output_tokens": trial[0], "reward": trial[1]} for trial in trials
        # ]
        yield {
            "prompt_tokens": json.dumps(ptokens),
            "target_tokens": json.dumps(target_tokens),
            "candidates": json.dumps(candidates),
        }


class ScoreCandidates:
    """Score candidates."""

    def __init__(
        self, reward_ckp: str, identifier: str, tokenizer: StarCoder2Tokenizer
    ):
        self.reward_ckp = pathlib.Path(reward_ckp)
        assert self.reward_ckp.exists()
        self._reward = None
        self.identifier = identifier
        self.tokenizer = tokenizer

    def _lazy_init_model(self):
        from research.eval.harness.factories import create_model

        if self._reward is None:
            self._reward = create_model(
                {
                    "name": "fastbackward",
                    "checkpoint_path": self.reward_ckp,
                    "seq_length": 7936,
                    "model_parallel_size": 1,
                }
            )
            self._reward.load()

    def score(
        self, prompt_tokens: list[int], candidate_tokens: list[int]
    ) -> tuple[float, bool, str]:
        """Compute the reward of the candidate tokens."""
        special_tokens = self.tokenizer.special_tokens
        cur_out_tokens = candidate_tokens
        if cur_out_tokens and cur_out_tokens[-1] in (
            special_tokens.eos,
            special_tokens.padding,
        ):
            cur_out_tokens = cur_out_tokens[:-1]
        assert self._reward is not None
        outputs = self._reward.raw_generate_tokens(
            prompt_tokens + candidate_tokens,
            GenerationOptions(temperature=0.0, max_generated_tokens=1),
        )
        if len(outputs.logits) == 0:
            return -100, False, f"Failed to score the candidate: {candidate_tokens}."
        token_prob = outputs.logits[0].softmax(dim=-1)
        good_score = token_prob[self.tokenizer.special_tokens.good].item()  # type: ignore
        bad_score = token_prob[self.tokenizer.special_tokens.bad].item()  # type: ignore
        reward = good_score - bad_score
        return reward, True, ""

    def __call__(
        self,
        prompt_tokens: str,
        target_tokens: str,
        candidates: str,
    ) -> Iterator[dict]:
        self._lazy_init_model()
        assert self._reward is not None
        deserialized_prompt_tokens = json.loads(prompt_tokens)
        assert isinstance(deserialized_prompt_tokens, list)
        deserialized_target_tokens = json.loads(target_tokens)
        assert isinstance(deserialized_target_tokens, list)
        deserialized_candidates = json.loads(candidates)
        target_score, success, msg = self.score(
            deserialized_prompt_tokens, deserialized_target_tokens
        )
        if not success:
            logger.info(f"{time_string()} {msg}")
        candidates_score: list[float] = []
        logging_str = ""
        for candidate_tokens in deserialized_candidates:
            cur_score, success, msg = self.score(
                deserialized_prompt_tokens, candidate_tokens
            )
            candidates_score.append(cur_score)
            logging_str += f"({len(candidate_tokens)} tokens, {cur_score:.3f}, {success=}, {msg=})\n"
        logger.info(
            f"{time_string()} Prompt {len(deserialized_prompt_tokens)=} tokens, "
            f"{len(deserialized_target_tokens)=} tokens ({target_score:.3f})\n"
            f"There are {len(deserialized_candidates)=} candidates.\n"
            f"{logging_str}"
        )
        yield {
            "prompt_tokens": prompt_tokens,
            "target_tokens": target_tokens,
            "candidates": candidates,
            f"{self.identifier}@target_score": target_score,
            f"{self.identifier}@candidates_score": json.dumps(candidates_score),
        }


def prepare_data(limit: int = 1_000_000):
    logger.info(f"Prepare data for {limit}")
    input_url = "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-prompts-simple_elden_v2.0-sc2"

    output_url = os.path.join(ROOT_URL, f"selected-{int2abbrv(limit)}")
    start_time = time.time()
    batch = 5
    spark = k8s_session(
        name="prepare-dpo-input-data",
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "256G",
            "spark.executor.memory": "256G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": str(batch),
        },
        # gpu_type=["H100_NVLINK_80GB"],
        ephemeral_storage_gb=128,
    )

    if os.path.exists(output_url):
        print(f"{time_string()} Selected data already exists: {output_url}")
    else:
        input_data = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
        logger.info(f"{time_string()} Input {input_url} has {input_data.count()} rows.")
        random_seed = 888
        fraction = max(0.01, min(1.0, float(limit) / input_data.count()))
        logger.info(f"Selecting {fraction * 100:.1f}% of data.")
        selected_data = (
            input_data.select("prompt_tokens")
            .sample(withReplacement=False, fraction=fraction, seed=random_seed)
            .repartition(max(10, limit // 200))
        )
        # Write out the selected data.
        selected_data.write.parquet(output_url, mode="overwrite")
        logger.info(f"{time_string()} Selected data saved to {output_url}.")
    selected_data = spark.read.parquet(os.path.join(output_url, "*zstd.parquet"))
    logger.info(f"{time_string()} Selected data has {selected_data.count()} rows.")
    spark.stop()
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


def generate_candidates(llm: str, limit: int = 1_000_000):
    """Generate multiple candidate outputs for each prompt."""
    input_url = os.path.join(ROOT_URL, f"selected-{int2abbrv(limit)}")
    output_url = os.path.join(ROOT_URL, f"candidates-{int2abbrv(limit)}")
    task_info_url = os.path.join(ROOT_URL, "task_info")
    start_time = time.time()

    tokenizer = StarCoder2Tokenizer()

    spark = k8s_session(
        name="process-dpo",
        max_workers=128,
        conf={
            "spark.executor.pyspark.memory": "128G",
            "spark.executor.memory": "128G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "5",
        },
        gpu_type=["H100_NVLINK_80GB"],
        ephemeral_storage_gb=128,
    )
    assert os.path.exists(input_url)
    inputs = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
    logger.info(f"{time_string()} Input {input_url} has {inputs.count()} rows.")

    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                GenerateCandidatesViaLLM(
                    llm,
                    tokenizer=tokenizer,
                ).__call__
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=7200,  # 2 hours timeout
        batch_size=16,
        task_info_location=task_info_url,
        ignore_error=False,
        allow_resume=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        # timing_run=True,
    )
    spark.stop()
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])


def generate_scores(reward_model: str, identifier: str, limit: int = 1_000_000):
    """Generate scores for each candidate."""
    # input_url = os.path.join(ROOT_URL, f"candidates-{int2abbrv(limit)}")
    # output_url = os.path.join(ROOT_URL, f"scores-{int2abbrv(limit)}")
    input_url = os.path.join(ROOT_URL, f"scores-{int2abbrv(limit)}")
    output_url = os.path.join(ROOT_URL, f"scores-{identifier}-{int2abbrv(limit)}")
    task_info_url = os.path.join(ROOT_URL, "task_info")
    start_time = time.time()
    print(f"{time_string()} Start processing {input_url}")
    print(f"{time_string()} Output to {output_url}")

    spark = k8s_session(
        name="process-dpo",
        max_workers=96,
        conf={
            "spark.executor.pyspark.memory": "128G",
            "spark.executor.memory": "128G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "5",
        },
        gpu_type=["H100_NVLINK_80GB"],
        ephemeral_storage_gb=128,
    )
    assert os.path.exists(input_url)
    inputs = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
    logger.info(f"{time_string()} Input {input_url} has {inputs.count()} rows.")

    tokenizer = StarCoder2Tokenizer()
    result = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                map_parquet.passthrough_feature(bound=False)(
                    map_parquet.allow_unused_args()(
                        ScoreCandidates(
                            reward_model,
                            identifier,
                            tokenizer=tokenizer,
                        ).__call__
                    )
                )
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=7200,  # 2 hours timeout
        batch_size=16,
        task_info_location=task_info_url,
        ignore_error=False,
        allow_resume=True,
        # profile=True,  # -> this is for debug
        # ignore_error=False,
        timing_run=True,
    )
    spark.stop()
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")
    print(result["status_count"])
    print(result["task_info"]["stderr"][0])


def post_process_data(limit: int):
    spark = k8s_session(
        name="process-dpo",
        max_workers=32,
        conf={
            "spark.executor.pyspark.memory": "64G",
            "spark.executor.memory": "32G",
            "spark.driver.maxResultSize": "16g",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
        },
    )
    input_url = os.path.join(ROOT_URL, f"scores-{int2abbrv(limit)}")
    assert os.path.exists(input_url)
    inputs_df = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
    total = inputs_df.count()
    logger.info(f"{time_string()} Input {input_url} has {total} rows.")
    inputs_df.printSchema()

    all_parquet_files = map_parquet.list_files(
        spark, input_url, suffix="parquet", include_path=True
    )
    logger.info(
        f"{time_string()} Input {input_url} has {len(all_parquet_files)} files."
    )
    output_url = os.path.join(ROOT_URL, f"scores-{int2abbrv(limit)}-in-json")
    pathlib.Path(output_url).mkdir(parents=False, exist_ok=True)
    for idx, parquet_file in enumerate(all_parquet_files):
        cur_parquet = spark.read.parquet(parquet_file)
        cur_df = cur_parquet.toPandas().to_dict(orient="records")
        cur_filename = parquet_file.split("/")[-1].split(".")[0]
        cur_filepath = os.path.join(output_url, f"{cur_filename}.json")
        assert not os.path.exists(cur_filepath)
        with open(cur_filepath, "w") as f:
            json.dump(cur_df, f, indent=2)
        logger.info(f"Save {idx:5d}/{len(all_parquet_files)} into {cur_filepath}")
    spark.stop()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--llm",
        type=str,
        # default="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0",
        default="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp1",
    )
    parser.add_argument(
        "--reward_model",
        type=str,
        # default="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXREJ-mp1",
        default="/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-mp1",
    )
    parser.add_argument(
        "--reward_label",
        type=str,
        default="mixaj",
    )
    parser.add_argument(
        "--limit",
        type=int,
        default=1_000_000,
    )
    args = parser.parse_args()
    # prepare_data(args.limit)
    # generate_candidates(args.llm, args.limit)
    generate_scores(args.reward_model, args.reward_label, args.limit)
    # process_data(args.llm, args.reward_model, args.limit)
    post_process_data(args.limit)
