{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import concurrent.futures as futures\n", "import copy\n", "import json\n", "import pathlib\n", "\n", "import torch\n", "import tqdm\n", "import zstandard as zstd\n", "\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionDatum,\n", ")\n", "from base.prompt_format.common import PromptChunk\n", "from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict\n", "from experimental.dxy.rag.rlhf.shared_lib import create_system\n", "from experimental.dxy.rag.rlhf.local_content_manager import (\n", "    LocalContentManager,\n", ")\n", "from research.core import utils_for_log\n", "from research.core.model_input import ModelInput\n", "from research.core.recency_info import convert_from_datasets_recency_info\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem\n", "\n", "\n", "def deserialize_single_line(line: str):\n", "    \"\"\"Decompress a line.\"\"\"\n", "    data = json.loads(line)\n", "    assert \"datum\" in data and \"blob_names\" in data\n", "    return {\n", "        \"datum\": HindsightCompletionDatum.schema().load(data[\"datum\"]),\n", "        \"blob_names\": data[\"blob_names\"],\n", "    }\n", "\n", "\n", "def deserialize(\n", "    all_lines: list[str], max_threads: int = 128, deserialize_fn=None\n", ") -> list[dict]:\n", "    \"\"\"Deserialize the data via multiple threads.\"\"\"\n", "    results = []\n", "    total = len(all_lines)\n", "    if deserialize_fn is None:\n", "        deserialize_fn = deserialize_single_line\n", "    with futures.ThreadPoolExecutor(max_workers=max_threads) as executor:\n", "        all_jobs = []\n", "        for line in tqdm.tqdm(all_lines, total=total, desc=\"Submit jobs\"):\n", "            all_jobs.append(executor.submit(deserialize_fn, line))\n", "        for job in tqdm.tqdm(\n", "            futures.as_completed(all_jobs), total=total, desc=\"Decompress\"\n", "        ):\n", "            results.append(job.result())\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system = create_system()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system = create_system()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_folder = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto\"\n", ")\n", "local_datum_path = input_folder / \"local-datum.jsonl.zst\"\n", "assert local_datum_path.exists()\n", "print(f\"{utils_for_log.time_string()} Processing {input_folder}\")\n", "all_lines = []\n", "with zstd.open(local_datum_path, \"r\", encoding=\"utf-8\") as f:\n", "    for line in f:\n", "        all_lines.append(line)\n", "print(f\"{utils_for_log.time_string()} Load {len(all_lines)} lines from {local_datum_path}.\")  # fmt: off\n", "all_datum = deserialize(all_lines)\n", "print(f\"{utils_for_log.time_string()} Finish decompress {len(all_datum)} lines\")\n", "blob_manager = LocalContentManager()\n", "blob_manager.load(input_folder / \"local-blobs\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 0\n", "\n", "datum_docids = all_datum[index]\n", "datum: HindsightCompletionDatum = datum_docids[\"datum\"]\n", "blob_names: list[str] = datum_docids[\"blob_names\"]\n", "if datum.completion.request.recency_info is None:\n", "    recency_info = None\n", "else:\n", "    recency_info = convert_from_datasets_recency_info(\n", "        datum.completion.request.recency_info\n", "    )\n", "model_input = ModelInput(\n", "    prefix=datum.completion.request.prefix,\n", "    suffix=datum.completion.request.suffix,\n", "    path=datum.completion.request.path,\n", "    target=None,\n", "    doc_ids=blob_names,\n", "    recency_info=recency_info,\n", ")\n", "gen_options_argmax = copy.deepcopy(system.generation_options)\n", "system.generation_options = gen_options_argmax\n", "output_argmax = system.generate(model_input)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_match = 0\n", "num_match_random = 0\n", "num_has_skip_tokens = 0\n", "num_pause_tokens = 0\n", "for datum in all_datum:\n", "    if datum[\"datum\"].ground_truth == datum[\"output_argmax\"].generated_text:\n", "        num_match += 1\n", "    if datum[\"datum\"].ground_truth == datum[\"output_random\"].generated_text:\n", "        num_match_random += 1\n", "    assert datum[\"output_argmax\"].generated_tokens is not None\n", "    if tokenizer.special_tokens.skip in datum[\"output_argmax\"].generated_tokens:\n", "        num_has_skip_tokens += 1\n", "    if tokenizer.special_tokens.pause in datum[\"output_argmax\"].generated_tokens:\n", "        num_pause_tokens += 1\n", "print(f\"Result==GT: {num_match}/{len(all_datum)}\")\n", "print(f\"Result==GT (random): {num_match_random}/{len(all_datum)}\")\n", "print(f\"Has skip tokens: {num_has_skip_tokens}/{len(all_datum)}\")\n", "print(f\"Has pause tokens: {num_pause_tokens}/{len(all_datum)}\")\n", "\n", "# print(all_datum[0][\"datum\"].ground_truth)\n", "# print(all_datum[0][\"output_argmax\"].generated_text)\n", "# print(all_datum[0][\"output_random\"].generated_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset as HFDataset\n", "\n", "dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/offline-rl-240903/train\"\n", ")\n", "print(len(dataset))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = dataset[1]\n", "print(data.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data[\"pos_tokens\"])\n", "print(data[\"neg_tokens\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}