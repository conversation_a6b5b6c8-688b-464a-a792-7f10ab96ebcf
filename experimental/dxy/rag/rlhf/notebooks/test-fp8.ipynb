{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.cceval import CCEvalOutput\n", "\n", "cceval_run_result_path = \"/mnt/efs/augment/eval/jobs/Hs5XiJry/000_starcoder2_fastforward_CCEval_completed_patches.jsonl.zst\"\n", "cceval_run_results = [CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)]\n", "print(f\"There are {len(cceval_run_results)} CCEvalOutput elements.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.rag.rlhf.shared_lib import create_system\n", "\n", "system = create_system(\"SC2_SEV2_INIT\")\n", "system.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt_tokens = cceval_run_results[0].prompt_tokens\n", "print(prompt_tokens)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from research.models.meta_model import GenerationOptions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(prompt_tokens))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["outputs = system.model.raw_generate_tokens(\n", "    prompt_tokens, GenerationOptions(max_generated_tokens=64)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["outputs.tokens"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}