{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "anthropic_api_key_file = pathlib.Path.home() / \".config\" / \"ANTHROPIC_API_KEY\"\n", "assert anthropic_api_key_file.exists()\n", "anthropic_api_key = anthropic_api_key_file.read_text().strip()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset as HFDataset\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from experimental.dxy.rag.rlhf.shared_lib import (\n", "    parse_useful_field_from_simple_elden_v1_tokens,\n", "    handle_skip_and_concat_with_suffix,\n", "    handle_skip_and_detokenize,\n", ")\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens\n", "\n", "valid_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/0914-eval-large\"\n", ")\n", "\n", "index = 38\n", "xdata = valid_dataset[index]\n", "prompt_tokens = xdata[\"prompt_tokens\"]\n", "pos_tokens = xdata[\"pos_tokens\"]\n", "neg_tokens = xdata[\"neg_tokens\"]\n", "field_dict = parse_useful_field_from_simple_elden_v1_tokens(prompt_tokens, tokenizer)\n", "context_tokens = field_dict[\"context_tokens\"]\n", "path_tokens = field_dict[\"path_tokens\"]\n", "prefix_tokens = field_dict[\"prefix_tokens\"]\n", "suffix_tokens = field_dict[\"suffix_tokens\"]\n", "prefix = tokenizer.detokenize(prefix_tokens)\n", "suffix = tokenizer.detokenize(suffix_tokens)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["language<|skip|><|skip|> == \"python\"\n", "\")).withColumn(\n", "    \"is_first\", F.row_number().over(window_spec) == 1\n", ")\n", "\n", "df.mapInPandas(tokenize, schema=\"struct<packed_samples array<int>>\").repartition(\n", "    100\n", ").write.mode(\"overwrite\").parquet(f\"{BASE_PATH}/tokenized\")\n", "\n", "spark.stop()\n", "\n"]}], "source": ["print(tokenizer.detokenize(pos_tokens))\n", "print(suffix)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["language\") == \"python\").withColumn(\n", "    \"is_first\", F.row_number().over(window_spec) == 1\n", ")\n", "\n", "df.mapInPandas(tokenize, schema=\"struct<packed_samples array<int>>\").repartition(\n", "    100\n", ").write.mode(\"overwrite\").parquet(f\"{BASE_PATH}/tokenized\")\n", "\n", "spark.stop()\n", "\n"]}], "source": ["print(handle_skip_and_concat_with_suffix(pos_tokens, tokenizer, suffix))"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["xdict = handle_skip_and_detokenize(pos_tokens, tokenizer, suffix)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\")\n", "\") == \"python\"\n"]}], "source": ["print(xdict[\"skipped_suffix\"])\n", "print(xdict[\"suffix_replacement_text\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import typing\n", "import numpy as np\n", "from research.core.utils_for_str import get_last_n_lines, get_first_n_lines\n", "from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient\n", "from experimental.dxy.rag.rlhf.shared_lib import parse_json_data_from_api_response\n", "\n", "\n", "class RewardSystemZeroShotAPI:\n", "    \"\"\"Reward system using absolute scorer.\"\"\"\n", "\n", "    def __init__(self, tokenizer: StarCoder2Tokenizer, temperature: float = 1.0):\n", "        self.tokenizer = tokenizer\n", "        self.max_lines = 32\n", "        self.temperature = temperature\n", "        self.system_prompt = \"\"\"You are an excellent engineer working at large tech companies, such as Google and Meta.\n", "You will be asked to compare two code snippets in a large codebase to see which one is better.\n", "Please do reasonable assessment and then summarize in one line for which option is better.\n", "To help you decide, I will also provide you with some related context in the codebase for you.\n", "\"\"\"\n", "        self.max_trials = 10\n", "        self.model = AnthropicDirectClient(\n", "            api_key=anthropic_api_key,\n", "            model_name=\"claude-3-5-sonnet-20240620\",\n", "            temperature=temperature,\n", "            max_output_tokens=2048,\n", "        )\n", "\n", "    def _get_response(\n", "        self, messages: list[tuple[str, str]], current_message: str\n", "    ) -> str:\n", "        answer = \"\"\n", "        for streamed_response in self.model.generate_response_stream(\n", "            messages, self.system_prompt, current_message\n", "        ):\n", "            answer += streamed_response.text\n", "        return answer\n", "\n", "    def raw_compare(\n", "        self,\n", "        prompt_tokens: list[int],\n", "        candidate_tokens_1: list[int],\n", "        candidate_tokens_2: list[int],\n", "    ) -> dict[str, typing.Any]:\n", "        field_dict = parse_useful_field_from_simple_elden_v1_tokens(\n", "            prompt_tokens, tokenizer\n", "        )\n", "        context_text = tokenizer.detokenize(field_dict[\"context_tokens\"])\n", "        message_1 = f\"Here are the related context:\\n{context_text}\"\n", "        response_1 = \"Awesome, I got it. These retrieved code chunks are coming from the same codebase, is that right?\"\n", "        prefix = tokenizer.detokenize(field_dict[\"prefix_tokens\"])\n", "        suffix = tokenizer.detokenize(field_dict[\"suffix_tokens\"])\n", "        prefix_last_n_line = get_last_n_lines(prefix, self.max_lines)\n", "        suffix_first_n_line = get_first_n_lines(suffix, self.max_lines)\n", "        path = tokenizer.detokenize(field_dict[\"path_tokens\"])\n", "        code_1 = (\n", "            prefix_last_n_line\n", "            + tokenizer.detokenize(candidate_tokens_1)\n", "            + suffix_first_n_line\n", "        )\n", "        code_2 = (\n", "            prefix_last_n_line\n", "            + tokenizer.detokenize(candidate_tokens_2)\n", "            + suffix_first_n_line\n", "        )\n", "        message_2 = f\"\"\"Yes, correct.\n", "This is the first candidate code snippet.\n", "{path}\n", "```\n", "{code_1}\n", "```\"\"\"\n", "        response_2 = \"Got it, what is the second candidate code snippet that you want me to compare?\"\n", "        current_message = f\"\"\"\n", "{path}\n", "```\n", "{code_2}\n", "```\n", "\n", "Which one is better?\"\"\"\n", "        messages = [(message_1, response_1), (message_2, response_2)]\n", "        answer = self._get_response(messages, current_message)\n", "        message_ask_for_json = \"\"\"Awesome! Please output in the json format with two fields, one is a str key for 'reason'.\n", "The other is also a str key for 'winner' and the value should be 'first' if you think the first candidate is better, and 'second' if you think the second candidate is better.\"\"\"\n", "        json_answer = self._get_response(\n", "            messages + [(current_message, answer)], message_ask_for_json\n", "        )\n", "        return {\n", "            \"messages\": messages,\n", "            \"current_message\": current_message,\n", "            \"answer\": answer,\n", "            \"json_answer\": json_answer,\n", "        }\n", "\n", "    def compare(\n", "        self,\n", "        prompt_tokens: list[int],\n", "        candidate_tokens_1: list[int],\n", "        candidate_tokens_2: list[int],\n", "    ) -> float:\n", "        trials: list[float] = []\n", "        for _ in range(self.max_trials):\n", "            raw_answer = self.raw_compare(\n", "                prompt_tokens, candidate_tokens_1, candidate_tokens_2\n", "            )\n", "            try:\n", "                json_answer = raw_answer[\"json_answer\"]\n", "                answer = parse_json_data_from_api_response(json_answer)\n", "                if answer[\"winner\"].lower() == \"first\":\n", "                    trials.append(1.0)\n", "                elif answer[\"winner\"].lower() == \"second\":\n", "                    trials.append(-1.0)\n", "                else:\n", "                    raise ValueError(f\"Invalid answer: {answer}\")\n", "            except Exception:\n", "                continue\n", "            print(f\"trials ({len(trials)}): {trials}\")\n", "        return np.array(trials).mean().item()\n", "\n", "\n", "system = RewardSystemZeroShotAPI(tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xdata = system.raw_compare(prompt_tokens, pos_tokens, neg_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(parse_json_data_from_api_response(xdata[\"json_answer\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# answer = \"\"\n", "# for streamed_response in model.generate_response_stream(\n", "#     xdata[\"messages\"], system_prompt, xdata[\"current_message\"]\n", "# ):\n", "#     answer += streamed_response.text\n", "# print(answer)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}