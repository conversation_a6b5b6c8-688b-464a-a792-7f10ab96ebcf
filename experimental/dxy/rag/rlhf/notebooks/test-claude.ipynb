{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "import tqdm\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.cceval import CCEval\n", "\n", "cceval_task = CCEval()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["item, docs = cceval_task[16]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(item)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["def put_xml_numbers(lines: list[str], start_line_offset: int = 0):\n", "    for i, line in enumerate(lines):\n", "        cur_number = i + 1 + start_line_offset\n", "        yield f\"<line number={cur_number}>{line.rstrip()}</line number={cur_number}>\\n\"\n", "\n", "\n", "def format_xml_code(\n", "    prefix: str,\n", "    suffix: str,\n", ") -> str:\n", "    prefix_lines = prefix.splitlines(keepends=True)\n", "    suffix_lines = suffix.splitlines(keepends=True)\n", "\n", "    prefix_lines_n_1 = prefix_lines[:-1]\n", "    prefix_n_xml = \"\".join(put_xml_numbers(prefix_lines_n_1))\n", "    middle_line = prefix_lines[-1] + \"<|missing-content|>\" + suffix_lines[0]\n", "    selected_code_n_xml = \"\".join(put_xml_numbers([middle_line], len(prefix_lines_n_1)))\n", "    suffix_n_xml = \"\".join(put_xml_numbers(suffix_lines[1:], len(prefix_lines)))\n", "    xml_code = f\"\"\"{prefix_n_xml}<highlighted_code>\\n{selected_code_n_xml}</highlighted_code>\\n{suffix_n_xml}\"\"\"\n", "\n", "    return xml_code\n", "\n", "\n", "def format_message(\n", "    file_path: str,\n", "    prefix: str,\n", "    suffix: str,\n", ") -> str:\n", "    xml_code = format_xml_code(prefix=prefix, suffix=suffix)\n", "\n", "    return f\"\"\"I have opened a file `{file_path}` and highlighted one line of the code (enclosed in <highlighted_code> tag):\n", "<file path=\"{file_path}\">\n", "{xml_code.rstrip()}\n", "</file>\n", "\n", "<instruction>\n", "Please fill in the content of <|missing-content|>, which can be a single line or multiple lines.\n", "</instruction>\n", "\n", "You should make edit in a way that keeps the code style, spacing and indentation consistent and reasonable!\n", "You should focus on changing ONLY the highlighted region.\n", "You should let the content of <|missing-content|> be coherent with its prefix and suffix.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(format_message(item.path, item.prefix, item.suffix))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient\n", "import pathlib\n", "\n", "anthropic_api_key_file = pathlib.Path.home() / \".config\" / \"ANTHROPIC_API_KEY\"\n", "assert anthropic_api_key_file.exists()\n", "anthropic_api_key = anthropic_api_key_file.read_text().strip()\n", "\n", "\n", "model = AnthropicDirectClient(\n", "    api_key=anthropic_api_key,\n", "    model_name=\"claude-3-5-sonnet-20240620\",\n", "    temperature=0.1,\n", "    max_output_tokens=2048,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["current_message = format_message(item.path, item.prefix, item.suffix)\n", "system_prompt = \"\"\"You are highly advanced and intelligent AI code assistant. Your task is to carefully apply changes to a file based on the conversation history and the user's instructions.\"\"\"\n", "\n", "formatted_messages = []\n", "formatted_messages.append(\n", "    {\n", "        \"role\": \"user\",\n", "        \"content\": current_message,\n", "    }\n", ")\n", "formatted_messages.append(\n", "    {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"\"\"Based on the context and the surrounding code, it appears that we need to iterate over some configuration items to create Receive instances. The most likely attribute to iterate over would be the bot IDs or configurations. Let's fill in the missing content:\n", "\n", "<highlighted_code>\n", "<line number=94>        for _ in Config.\"\"\",\n", "    }\n", ")\n", "\n", "response = model.client.messages.create(\n", "    model=\"claude-3-5-sonnet-20240620\",\n", "    max_tokens=1024,\n", "    messages=formatted_messages,\n", "    system=system_prompt,\n", "    temperature=0,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(response.content[0].text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = failed_index[3]\n", "x = cceval_run_results[index]\n", "print(index)\n", "\n", "assert x.prompt_tokens.count(special_tokens.fim_prefix) == 1\n", "index = x.prompt_tokens.index(special_tokens.fim_prefix)\n", "context_tokens = x.prompt_tokens[:index]\n", "print(tokenizer.detokenize(context_tokens))\n", "print(x.generation)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}