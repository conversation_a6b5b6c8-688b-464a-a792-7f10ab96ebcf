{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "import tqdm\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create CCEval Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.cceval import CCEvalOutput\n", "\n", "cceval_run_result_path = \"/mnt/efs/augment/eval/jobs/XF5bncnT/000_starcoder2_fastforward_CCEval_completed_patches.jsonl.zst\"\n", "cceval_run_results = [CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)]\n", "print(f\"There are {len(cceval_run_results)} CCEvalOutput elements.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cceval_run_data = []\n", "for x in tqdm.tqdm(cceval_run_results):\n", "    cceval_run_data.append(\n", "        {\n", "            \"prompt_tokens\": tokenizer.tokenize_unsafe(x.prompt),\n", "            \"positive_tokens\": tokenizer.tokenize_safe(x.ground_truth),\n", "        }\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from experimental.dxy.rag.rlhf.shared_lib import create_sft_dataset\n", "\n", "output = pathlib.Path(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0915_v2_s6k\"\n", ")\n", "create_sft_dataset(cceval_run_data, output, 7936 + 1, tokenizer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Hindsight Eval"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "\n", "hindsight_run_result_path = \"/mnt/efs/augment/eval/jobs/Jk3JgRJH/000_starcoder2_fastforward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "hindsight_run_results = [\n", "    HindsightOutput(**x) for x in read_jsonl_zst(hindsight_run_result_path)\n", "]\n", "print(f\"There are {len(hindsight_run_results)} HindsightOutput elements.\")\n", "special_tokens = tokenizer.special_tokens\n", "useful_special_tokens = [\n", "    tokenizer.special_tokens.eos,\n", "    tokenizer.special_tokens.padding,\n", "    tokenizer.special_tokens.fim_prefix,\n", "    tokenizer.special_tokens.fim_middle,\n", "    tokenizer.special_tokens.fim_suffix,\n", "]\n", "\n", "hindsight_run_data, num_fail = [], 0\n", "for x in tqdm.tqdm(hindsight_run_results):\n", "    prompt_tokens = tokenizer.tokenize_unsafe(x.prompt)\n", "    new_prompt_tokens = []\n", "    for token in prompt_tokens:\n", "        if token in useful_special_tokens:\n", "            new_prompt_tokens.append(token)\n", "        else:\n", "            new_prompt_tokens.extend(\n", "                tokenizer.tokenize_safe(tokenizer.detokenize([token]))\n", "            )\n", "    if new_prompt_tokens.count(special_tokens.fim_prefix) != 1:\n", "        num_fail += 1\n", "        continue\n", "    if new_prompt_tokens.count(special_tokens.fim_middle) != 1:\n", "        num_fail += 1\n", "        continue\n", "    if new_prompt_tokens.count(special_tokens.fim_suffix) != 1:\n", "        num_fail += 1\n", "        continue\n", "    hindsight_run_data.append(\n", "        {\n", "            \"prompt_tokens\": new_prompt_tokens,\n", "            \"positive_tokens\": tokenizer.tokenize_safe(x.ground_truth),\n", "        }\n", "    )\n", "print(f\"There are {num_fail} failures and {len(hindsight_run_data)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from experimental.dxy.rag.rlhf.shared_lib import create_sft_dataset\n", "\n", "output = pathlib.Path(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0915_v2_s6k\"\n", ")\n", "create_sft_dataset(hindsight_run_data, output, 7936 + 1, tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from megatron.data import indexed_dataset\n", "\n", "\n", "cceval_dataset = indexed_dataset.MMapIndexedDataset(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval\",\n", "    skip_warmup=True,\n", ")\n", "max_token = []\n", "for x in cceval_dataset:\n", "    max_token.append(x.max())\n", "print(f\"CCEval's max token ID is {np.max(max_token).item()}\")\n", "\n", "\n", "hindsight_dataset = indexed_dataset.MMapIndexedDataset(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06\",\n", "    skip_warmup=True,\n", ")\n", "max_token = []\n", "for x in hindsight_dataset:\n", "    max_token.append(x.max())\n", "print(f\"<PERSON>ndsight's max token ID is {np.max(max_token).item()}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}