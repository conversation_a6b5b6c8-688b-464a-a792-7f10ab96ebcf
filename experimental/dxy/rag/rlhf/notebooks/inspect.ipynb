{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}], "source": ["import pathlib\n", "import json\n", "from research.core import utils_for_log\n", "import zstandard as zstd\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionDatum,\n", ")\n", "from experimental.dxy.rag.rlhf.dump_tokens import deserialize\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "\n", "\n", "def deserialize_single_line(line: str):\n", "    \"\"\"Decompress a line.\"\"\"\n", "    data = json.loads(line)\n", "    assert \"datum\" in data and \"blob_names\" in data\n", "    assert \"output_argmax\" in data and \"output_random\" in data\n", "    return {\n", "        \"datum\": HindsightCompletionDatum.schema().load(data[\"datum\"]),\n", "        \"blob_names\": data[\"blob_names\"],\n", "        \"output_argmax\": CompletionResult.schema().load(data[\"output_argmax\"]),\n", "        \"output_random\": CompletionResult.schema().load(data[\"output_random\"]),\n", "    }"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-08-30-at-09:03:01 Load 339 lines from /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto/local-datum-w-output.jsonl.zst.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Submit jobs: 100%|██████████| 339/339 [01:12<00:00,  4.69it/s]\n", "Decompress: 100%|██████████| 339/339 [00:05<00:00, 61.28it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["2024-08-30-at-09:04:24 Finish decompress 339 lines\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["local_datum_path = \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto/local-datum-w-output.jsonl.zst\"\n", "all_lines = []\n", "with zstd.open(local_datum_path, \"r\", encoding=\"utf-8\") as f:\n", "    for line in f:\n", "        all_lines.append(line)\n", "print(f\"{utils_for_log.time_string()} Load {len(all_lines)} lines from {local_datum_path}.\")  # fmt: off\n", "all_datum = deserialize(all_lines, deserialize_fn=deserialize_single_line)\n", "print(f\"{utils_for_log.time_string()} Finish decompress {len(all_datum)} lines\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Result==GT: 154/339\n", "Result==GT (random): 124/339\n", "Has skip tokens: 15/339\n", "Has pause tokens: 40/339\n"]}], "source": ["num_match = 0\n", "num_match_random = 0\n", "num_has_skip_tokens = 0\n", "num_pause_tokens = 0\n", "for datum in all_datum:\n", "    if datum[\"datum\"].ground_truth == datum[\"output_argmax\"].generated_text:\n", "        num_match += 1\n", "    if datum[\"datum\"].ground_truth == datum[\"output_random\"].generated_text:\n", "        num_match_random += 1\n", "    assert datum[\"output_argmax\"].generated_tokens is not None\n", "    if tokenizer.special_tokens.skip in datum[\"output_argmax\"].generated_tokens:\n", "        num_has_skip_tokens += 1\n", "    if tokenizer.special_tokens.pause in datum[\"output_argmax\"].generated_tokens:\n", "        num_pause_tokens += 1\n", "print(f\"Result==GT: {num_match}/{len(all_datum)}\")\n", "print(f\"Result==GT (random): {num_match_random}/{len(all_datum)}\")\n", "print(f\"Has skip tokens: {num_has_skip_tokens}/{len(all_datum)}\")\n", "print(f\"Has pause tokens: {num_pause_tokens}/{len(all_datum)}\")\n", "\n", "# print(all_datum[0][\"datum\"].ground_truth)\n", "# print(all_datum[0][\"output_argmax\"].generated_text)\n", "# print(all_datum[0][\"output_random\"].generated_text)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["({\n", "      title: 'Credentials Required',\n", "      content: 'Credentials Required',\n", "      onOk: () => {\n", "        this.remoteInit()\n", "      }\n", "    })\n"]}], "source": []}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14432\n"]}], "source": ["from datasets import Dataset as HFDataset\n", "\n", "dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/offline-rl-240903/train\"\n", ")\n", "print(len(dataset))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['prompt_tokens', 'pos_tokens', 'neg_tokens'])\n"]}], "source": ["data = dataset[1]\n", "print(data.keys())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[51, 390, 100, 24430, 100, 697, 100, 1016, 299, 244, 53]\n", "[51, 942, 100, 697, 299, 1686, 310, 581, 100, 24484, 5532, 299, 649, 51, 942, 100, 24484, 5532, 100, 1005, 51, 870, 100, 897, 45, 8657, 366, 3330, 50, 61, 678, 310, 581, 100, 24484, 5532, 100, 1279, 299, 2379, 51, 13428, 45, 942, 100, 24484, 5532, 46, 310, 1217, 2095, 45, 942, 100, 24484, 5532, 100, 1279, 1388, 3519, 6957, 630, 244, 54, 310, 649, 51, 942, 100, 697, 299, 581, 100, 24484, 5532, 100, 1279, 1388, 3519, 15576, 53, 11210, 125, 58, 104, 15576, 53, 98]\n"]}], "source": ["print(data[\"pos_tokens\"])\n", "print(data[\"neg_tokens\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}