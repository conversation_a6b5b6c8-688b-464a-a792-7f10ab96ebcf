{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset as HFDataset\n", "\n", "train_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/offline-rl-240904/train\"\n", ")\n", "valid_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/hindsight/offline-rl-240904/valid\"\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# This is an example code to load it.\n", "from research.eval.harness import factories\n", "from research.models.meta_model import GenerationOptions\n", "\n", "model_config = {\n", "    \"name\": \"fastbackward\",\n", "    # \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/REWARD-SC2-15B-SIM-V0-mp1\",\n", "    \"checkpoint_path\": \"/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/REWARD-SC2-15B-SIM-V1-mp1\",\n", "    \"model_parallel_size\": 1,\n", "    \"override_tokenizer\": \"starcoder2\",\n", "    \"seq_length\": 7936,\n", "}\n", "# The prompt formatter is incorrect so that please only use the token-based interface.\n", "model = factories.create_model(model_config)\n", "# Check if the tokenizer is correct -> this is important\n", "print(model.tokenizer)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["model.load()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["tokenizer = model.tokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"There are {len(valid_dataset)} validation examples.\")\n", "index = 1\n", "xdata = valid_dataset[1]\n", "prompt_tokens = xdata[\"prompt_tokens\"]\n", "pos_tokens = xdata[\"pos_tokens\"]\n", "neg_tokens = xdata[\"neg_tokens\"]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "correctness = []\n", "for index in range(len(valid_dataset)):\n", "    xdata = valid_dataset[index]\n", "    prompt_tokens = xdata[\"prompt_tokens\"]\n", "    pos_tokens = xdata[\"pos_tokens\"]\n", "    neg_tokens = xdata[\"neg_tokens\"]\n", "    outputs_pos = model.raw_generate_tokens(\n", "        prompt_tokens + pos_tokens,\n", "        GenerationOptions(temperature=0.0, max_generated_tokens=1),\n", "    )\n", "    outputs_neg = model.raw_generate_tokens(\n", "        prompt_tokens + neg_tokens,\n", "        GenerationOptions(temperature=0.0, max_generated_tokens=1),\n", "    )\n", "    token_prob_pos = outputs_pos.logits[0].softmax(dim=-1)\n", "    token_prob_neg = outputs_neg.logits[0].softmax(dim=-1)\n", "    reward_pos = (\n", "        token_prob_pos[tokenizer.special_tokens.good]\n", "        - token_prob_pos[tokenizer.special_tokens.bad]\n", "    )\n", "    reward_neg = (\n", "        token_prob_neg[tokenizer.special_tokens.good]\n", "        - token_prob_neg[tokenizer.special_tokens.bad]\n", "    )\n", "    pos_larger_than_neg = reward_pos.item() > reward_neg.item()\n", "    correctness.append(pos_larger_than_neg)\n", "    print(\n", "        f\"{index}/{len(valid_dataset)}: {reward_pos=} {reward_neg=} {np.mean(correctness).item()}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(outputs.logits[0].shape)\n", "# token_probs = outputs.logits[0].softmax(dim=-1)\n", "# prob_good = token_probs[tokenizer.special_tokens.good]\n", "# prob_bad = token_probs[tokenizer.special_tokens.bad]\n", "# reward = prob_good - prob_bad\n", "# print(f\"{prob_good=} - {prob_bad=} = {reward}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}