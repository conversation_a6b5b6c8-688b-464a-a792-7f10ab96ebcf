{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create CCEval Data"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 200 CCEvalOutput elements.\n", "There are 200 CCEvalOutput elements.\n"]}], "source": ["from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.cceval import CCEvalOutput\n", "\n", "cceval_run_result_path = \"/mnt/efs/augment/eval/jobs/eTLoxxN8/000_starcoder2_fp8_fastforward_CCEval_completed_patches.jsonl.zst\"\n", "cceval_results_rs = [CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)]\n", "print(f\"There are {len(cceval_results_rs)} CCEvalOutput elements.\")\n", "\n", "cceval_run_result_path = \"/mnt/efs/augment/eval/jobs/KNhRWaZE/000_starcoder2_fp8_fastforward_CCEval_completed_patches.jsonl.zst\"\n", "cceval_results_norm = [\n", "    CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)\n", "]\n", "print(f\"There are {len(cceval_results_norm)} CCEvalOutput elements.\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 68 failed examples, EM=66.00%.\n", "There are 58 failed examples, EM=71.00%.\n", "There are 70 indexes that are different.\n"]}], "source": ["def compute_exact_match(x: CCEvalOutput) -> bool:\n", "    return x.ground_truth == x.generation\n", "\n", "\n", "def get_em_and_return_failed_index(results: list[CCEvalOutput]) -> list[int]:\n", "    failed_indexes = []\n", "    for index, xdata in enumerate(results):\n", "        if not compute_exact_match(xdata):\n", "            failed_indexes.append(index)\n", "    ratio = len(failed_indexes) / len(results)\n", "    print(f\"There are {len(failed_indexes)} failed examples, EM={(1-ratio)*100:.2f}%.\")\n", "    return failed_indexes\n", "\n", "\n", "findexes_rs = get_em_and_return_failed_index(cceval_results_rs)\n", "findexes_norm = get_em_and_return_failed_index(cceval_results_norm)\n", "join_indexes = list(set(findexes_rs).union(set(findexes_norm)))\n", "print(f\"There are {len(join_indexes)} indexes that are different.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create LLM and Reward Model"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating model with config: {'name': 'starcoder2_fp8_fastforward', 'checkpoint_path': '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8', 'checkpoint_sha256': '77e2c896ca728f3998b84bc6ba6c54e8ad6edb2fffadd547e269c097e5ec27e8', 'model_path': '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8'}\n"]}], "source": ["from experimental.dxy.rag.rlhf.reward_system_lib import (\n", "    create_ffw_fp8_model,\n", "    RewardSystemAbsoluteScorer,\n", ")\n", "\n", "llm = create_ffw_fp8_model()\n", "reward = RewardSystemAbsoluteScorer(\n", "    \"/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8\",\n", "    model_name=\"starcoder2_fp8_fastforward\",\n", "    tokenizer=tokenizer,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:/home/<USER>/augment/research/models/fastforward_models.py:Loading model starcoder2_fp8_fastforward...\n", "INFO:root:Using base.fastforward\n", "INFO:root:Loading a StarCoder2 model starcoder2-15b onto 1 processes.\n", "INFO:root:create emb.\n", "INFO:root:create finalnorm.\n", "INFO:root:create score_proj.\n", "INFO:root:Checkpoint at '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0' has expected hash.\n", "INFO:root:Checkpoint at '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0' has expected hash.\n", "INFO:root:Checkpoint at '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0' has expected hash.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id a963e8b0-5212-4ec2-8d53-8bca9ce5daf8.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:/home/<USER>/augment/research/models/fastforward_models.py:Loaded the model in 12.4 seconds\n"]}], "source": ["llm.load()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:/home/<USER>/augment/research/models/fastforward_models.py:Loading model starcoder2_fp8_fastforward...\n", "INFO:root:Using base.fastforward\n", "INFO:root:Loading a StarCoder2 model starcoder2-15b onto 1 processes.\n", "INFO:root:create emb.\n", "INFO:root:create finalnorm.\n", "INFO:root:create score_proj.\n", "INFO:root:Checkpoint at '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8' has expected hash.\n", "INFO:root:Checkpoint at '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8' has expected hash.\n", "INFO:root:Checkpoint at '/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8' has expected hash.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id e3ab9288-eeb2-48e3-97f3-af07b0c3b8f4.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:/home/<USER>/augment/research/models/fastforward_models.py:Loaded the model in 12.1 seconds\n"]}], "source": ["reward.load()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["from research.core.utils_for_str import show_completion, get_diff_str"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Index: 14/200\n", "---------------------------------------------------------------- ground truth\n", "\u001b[92m                critic_info = self._update_critic(batch)              \n", "\n", "                torch_util.add_torch_dict(critic_info, train_info)\n", "\n", "        torch_util.\u001b[0m\u001b[47m\u001b[30mscale_torch_dict(1.0 / num_batches, train_info)\u001b[0m\u001b[94m\n", "\n", "        actor_batch = {\n", "            \"norm_obs\": self._exp_buffer.get_data(\"norm_obs\"),\n", "            \"norm_action\": self._exp_buffer.get_data(\"norm_action\"),\n", "\u001b[0m---------------------------------------------------------------- reject sample\n", "\u001b[92m                critic_info = self._update_critic(batch)              \n", "\n", "                torch_util.add_torch_dict(critic_info, train_info)\n", "\n", "        torch_util.\u001b[0m\u001b[47m\u001b[30mscale_torch_dict(1.0 / (self._critic_update_epoch * num_batches), train_info)\u001b[0m\u001b[94m\n", "\n", "        actor_batch = {\n", "            \"norm_obs\": self._exp_buffer.get_data(\"norm_obs\"),\n", "            \"norm_action\": self._exp_buffer.get_data(\"norm_action\"),\n", "\u001b[0m---------------------------------------------------------------- greedy sample\n", "\u001b[92m                critic_info = self._update_critic(batch)              \n", "\n", "                torch_util.add_torch_dict(critic_info, train_info)\n", "\n", "        torch_util.\u001b[0m\u001b[47m\u001b[30mscale_torch_dict(1.0 / (self._critic_update_epoch * num_batches), train_info)\u001b[0m\u001b[94m\n", "\n", "        actor_batch = {\n", "            \"norm_obs\": self._exp_buffer.get_data(\"norm_obs\"),\n", "            \"norm_action\": self._exp_buffer.get_data(\"norm_action\"),\n", "\u001b[0m----------------------------------------------------------------\n", "Diff of greedy sample vs. ground truth:\n", "--- \n", "+++ \n", "@@ -1 +1 @@\n", "-scale_torch_dict(1.0 / num_batches, train_info)+scale_torch_dict(1.0 / (self._critic_update_epoch * num_batches), train_info)\n", "----------------------------------------------------------------\n", "Diff of reject sample vs. ground truth:\n", "--- \n", "+++ \n", "@@ -1 +1 @@\n", "-scale_torch_dict(1.0 / num_batches, train_info)+scale_torch_dict(1.0 / (self._critic_update_epoch * num_batches), train_info)\n"]}], "source": ["index = join_indexes[4]\n", "print(f\"Index: {index}/{len(cceval_results_norm)}\")\n", "# result = cceval_results_norm[index]\n", "result_rs = cceval_results_rs[index]\n", "result_nm = cceval_results_norm[index]\n", "result = result_rs\n", "print(\"-\" * 64 + \" ground truth\")\n", "show_completion(result.prefix, result.suffix, result.ground_truth, max_lines=5)\n", "print(\"-\" * 64 + \" reject sample\")\n", "show_completion(result.prefix, result.suffix, result.generation, max_lines=5)\n", "print(\"-\" * 64 + \" greedy sample\")\n", "show_completion(result_nm.prefix, result_nm.suffix, result_nm.generation, max_lines=5)\n", "print(\"-\" * 64)\n", "print(\"Diff of greedy sample vs. ground truth:\")\n", "print(get_diff_str(result_nm.ground_truth, result_nm.generation))\n", "print(\"-\" * 64)\n", "print(\"Diff of reject sample vs. ground truth:\")\n", "print(get_diff_str(result_rs.ground_truth, result_rs.generation))"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Same as greedy sample: options=GenerationOptions(temperature=1.0, top_k=None, top_p=0.97, max_generated_tokens=64, stop_criteria=None, stop_tokens=[0, 49153]).\n", "Same as greedy sample: options=GenerationOptions(temperature=1.0, top_k=None, top_p=0.97, max_generated_tokens=64, stop_criteria=None, stop_tokens=[0, 49153]).\n", "Trial 0: 0.8772 greedy : scale_torch_dict(1.0 / (self._critic_update_epoch * num_batches), train_info)\n", "Trial 1: 0.8745 random : scale_torch_dict(1.0 / num_batches, train_info)\n", "Trial 2: 0.8745 random : scale_torch_dict(float(num_batches), train_info)\n", "Trial 3: 0.8691 random : scale_torch_dict(1.0 / self._critic_update_epoch, train_info)\n", "Trial 4: 0.8691 random : scale_torch_dict(1.0 / self._critic_update_epoch, train_info)\n", "Trial 5: 0.8691 random : scale_torch_dict(1.0 / self._critic_update_epoch, train_info)\n", "Trial 6: 0.8672 random : scale_torch_dict(1.0 / (num_batches * self._critic_update_epoch), train_info)\n", "Trial 7: 0.4688 random : scale_torch_dict(1.0/self._critic_update_epoch, train_info)<|pause|>\n", "\n", "        # scale the gradient update according to the number of Batches<|pause|>\n", "        torch_util.scale_torch_dict(1.0/num_batches, train_info)<|pause|>\n", "scale_torch_dict(1.0 / num_batches, train_info)\n"]}], "source": ["from research.models.meta_model import GenerationOptions\n", "\n", "special_tokens = tokenizer.special_tokens\n", "stop_tokens = [\n", "    # special_tokens.pause,\n", "    special_tokens.eos,\n", "    special_tokens.skip,\n", "]\n", "\n", "trials: list[tuple[list[int], float, str]] = []\n", "for i in range(10):\n", "    prompt_tokens = result.prompt_tokens\n", "    options = GenerationOptions(\n", "        temperature=1.0,\n", "        top_p=0.97,\n", "        max_generated_tokens=64,\n", "        stop_tokens=stop_tokens,\n", "    )\n", "    if i == 0:\n", "        options.temperature = 0\n", "        options.top_p = None\n", "    outputs = llm.raw_generate_tokens(\n", "        prompt_tokens=prompt_tokens,\n", "        options=options,\n", "        should_cancel=lambda: False,\n", "    )\n", "    tokens = outputs.tokens\n", "    if tokens[-1] in stop_tokens:\n", "        tokens = tokens[:-1]\n", "    if i == 0:\n", "        score = reward.score(prompt_tokens, outputs.tokens)\n", "        score += 0.01\n", "        trials.append((tokens, score, \"greedy\"))\n", "    else:\n", "        if tokens != trials[0][0]:\n", "            score = reward.score(prompt_tokens, outputs.tokens)\n", "            trials.append((tokens, score, \"random\"))\n", "        else:\n", "            print(f\"Same as greedy sample: {options=}.\")\n", "trials.sort(key=lambda x: x[1], reverse=True)\n", "\n", "for i in range(len(trials)):\n", "    print(\n", "        f\"Trial {i}: {trials[i][1]:.4f} {trials[i][2]} : {tokenizer.detokenize(trials[i][0])}\"\n", "    )\n", "\n", "top_1_tokens = trials[0][0]\n", "if top_1_tokens[-1] in (special_tokens.eos, special_tokens.padding):\n", "    top_1_tokens = top_1_tokens[:-1]\n", "top_1_genstr = tokenizer.detokenize(top_1_tokens)\n", "print(result.ground_truth)\n", "# diff_str = get_diff_str(result.ground_truth, top_1_genstr)\n", "# print(diff_str)\n", "# show_completion(result.prefix, result.suffix, top_1_genstr, max_lines=20)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}