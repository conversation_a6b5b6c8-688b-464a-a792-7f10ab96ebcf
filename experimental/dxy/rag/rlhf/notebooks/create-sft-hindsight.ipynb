{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 128 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 128 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}], "source": ["import argparse\n", "import concurrent.futures as futures\n", "import copy\n", "import json\n", "import pathlib\n", "\n", "import torch\n", "import tqdm\n", "import zstandard as zstd\n", "\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionDatum,\n", ")\n", "from base.prompt_format.common import PromptChunk\n", "from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict\n", "from experimental.dxy.rag.rlhf.local_content_manager import (\n", "    LocalContentManager,\n", ")\n", "from experimental.dxy.rag.rlhf.shared_lib import extract_tokens_before_stop_tokens\n", "from research.core import utils_for_log\n", "from research.core.model_input import ModelInput\n", "from research.core.recency_info import convert_from_datasets_recency_info\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "def parse_json_file(path: pathlib.Path):\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        xdata = json.load(f)\n", "        output_random_1_tokens = xdata[\"output_random_1_tokens\"]\n", "        output_random_2_tokens = xdata[\"output_random_2_tokens\"]\n", "        ground_truth_tokens_w_pause_skip = xdata[\"ground_truth_tokens_w_pause_skip\"]\n", "        datum = HindsightCompletionDatum.schema().load(xdata[\"datum\"])\n", "        output_argmax = CompletionResult.schema().load(xdata[\"output_argmax\"])\n", "        blob_names = xdata[\"blob_names\"]\n", "        output_random_1_tokens = extract_tokens_before_stop_tokens(\n", "            output_random_1_tokens,\n", "            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],\n", "        )\n", "        output_random_2_tokens = extract_tokens_before_stop_tokens(\n", "            output_random_2_tokens,\n", "            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],\n", "        )\n", "        return {\n", "            \"datum\": datum,\n", "            \"blob_names\": blob_names,\n", "            \"output_argmax\": output_argmax,\n", "            \"ground_truth_tokens_w_pause_skip\": ground_truth_tokens_w_pause_skip,\n", "            \"output_random_1_tokens\": output_random_1_tokens,\n", "            \"output_random_2_tokens\": output_random_2_tokens,\n", "        }"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 339 items in /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto.\n", "2024-09-12-at-17:58:14 there are 339 items.\n"]}], "source": ["input_folders = [\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto\",\n", "    # \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing\",\n", "    # \"/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto\",\n", "    # \"/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing\",\n", "]\n", "all_json_files = []\n", "for input_folder in input_folders:\n", "    target_folder = pathlib.Path(input_folder) / \"token-data-w-skip-pause\"\n", "    cur_json_files = list(target_folder.glob(\"*-*.json\"))\n", "    print(f\"There are {len(cur_json_files)} items in {input_folder}.\")\n", "    all_json_files.extend(cur_json_files)\n", "print(f\"{utils_for_log.time_string()} there are {len(all_json_files)} items.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Submit jobs: 100%|██████████| 339/339 [00:15<00:00, 21.67it/s]\n", "Decompress: 100%|██████████| 339/339 [00:31<00:00, 10.71it/s] \n"]}], "source": ["all_data = []\n", "# for json_file in tqdm.tqdm(all_json_files):\n", "#     all_data.append(parse_json_file(json_file))\n", "\n", "with futures.ThreadPoolExecutor(max_workers=32) as executor:\n", "    all_jobs = []\n", "    for index, json_file in tqdm.tqdm(\n", "        enumerate(all_json_files), total=len(all_json_files), desc=\"Submit jobs\"\n", "    ):\n", "        all_jobs.append(executor.submit(parse_json_file, json_file))\n", "    for job in tqdm.tqdm(\n", "        futures.as_completed(all_jobs), total=len(all_json_files), desc=\"Decompress\"\n", "    ):\n", "        all_data.append(job.result())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["HindsightCompletionDatum(completion=CompletionDatum(request_id='c46311e3-d382-419a-8b5e-1ee86bc3b85c', user_id='xudong-zhao-pareto-contractor', request=CompletionRequest(prefix=\"   const { props } = this\\n    const { pane, enableSsh, type } = props.tab\\n    if (type === terminalRdpType) {\\n      return null\\n    }\\n    const termType = props.tab?.type\\n    const isSsh = props.tab.authType\\n    const isLocal = !isSsh && (termType === connectionMap.local || !termType)\\n    const cls1 = 'mg1r icon-split pointer iblock spliter'\\n    const cls2 = 'icon-direction pointer iblock spliter'\\n    const types = [\\n      paneMap.terminal,\\n      paneMap.fileManager\\n    ]\\n    const controls = [\\n      isSsh ? paneMap.ssh : paneMap.terminal\\n    ]\\n    if (isSsh || isLocal) {\\n      controls.push(isSsh ? paneMap.sftp : paneMap.fileManager)\\n    }\\n    const checkTxt = e('sftpPathFollowSsh') + ' [Beta]'\\n    const checkProps = {\\n      onClick: this.toggleCheckSftpPathFollowSsh,\\n      className: classnames(\\n        'sftp-follow-ssh-icon',\\n        {\\n          active: sftpPathFollowSsh\\n        }\\n      )\\n    }\\n    const simpleMapper = {\\n      [paneMap.terminal]: 'T',\\n      [paneMap.fileManager]: 'F',\\n      [paneMap.ssh]: 'T'\\n    }\\n    const shouldHaveSpliter = (isSsh && enableSsh) || isLocal\\n    return (\\n      <div\\n        className='terminal-control fix'\\n      >\\n        <div className='term-sftp-tabs fleft'>\\n          {\\n            controls.map((type, i) => {\\n              const cls = classnames(\\n                'type-tab',\\n                type,\\n                {\\n                  active: types[i] === pane\\n                }\\n              )\\n              return (\\n                <span\\n                  className={cls}\\n                  key={type + '_' + i}\\n                  onClick={() => this.onChangePane(types[i])}\\n                >\\n                  <span className='type-tab-txt'>\\n                    <span className='w500'>{e(type)}</span>\\n                    <span className='l500'>{simpleMapper[type]}</span>\\n                    <span className='type-tab-line' />\\n                  </span>\\n                </span>\\n              )\\n            })\\n          }\\n        </div>\\n        {\\n          shouldHaveSpliter\\n            ? (\\n              <Tooltip title={checkTxt}>\\n                <span {...checkProps}>\\n                  <PaperClipOutlined />\\n                </span>\\n              </Tooltip>\\n              )\\n            : null\\n        }\\n        {\\n          this.renderDelTip(pane === paneMap.terminal)\\n        }\\n        {\\n          pane === paneMap.terminal\\n            ? (\\n              <div className='fright term-controls'>\\n                {this.fullscreenIcon()}\\n                {this.renderSearchIcon()}\\n                <BorderVerticleOutlined\\n                  className={cls1}\\n                  onClick={this.handleSplitVert}\\n                />\\n                <BorderHorizontalOutlined\\n                  className={cls2}\\n                  onClick={this.handleSplitHori}\\n                />\\n              </div>\\n              )\\n            : null\\n        }\\n      </div>\\n    )\\n  }\\n\\n  renderContent = () => {\\n    const { splitTerminalSftp } = this.props\\n    if (!splitTerminalSftp) {\\n      return (\\n        <div>\\n          {this.renderTerminal()}\\n          {this.renderSftp()}\\n        </div>\\n      )\\n    }\\n    const cls = classNames(\\n      'session-split-wrap',\\n      splitTerminalSftp\\n    )\\n    const {\\n      termPos,\\n      handlePos,\\n      sftpPos\\n    } = this.computePosition()\\n    return (\\n      <div className={cls}>\\n        <div className='session-split-term'>\\n          {this.renderTerminal(termPos)}\\n        </div>\\n        <div className='session-split-handle' {...handlePos} />\\n        <div className='session-split-sftp'>\\n          {this.renderSftp(sftpPos)}\\n        </div>\\n      </div>\\n    )\\n  }\\n\\n  render () {\\n    const {\\n      splitDirection,\\n      infoPanelProps,\\n      showInfo,\\n      infoPanelPinned\\n    } = this.state\\n    const { pane } = this.props.tab\\n    const infoProps = {\\n      infoPanelPinned,\\n      ...pick(this.props.config, ['host', 'port', 'saveTerminalLogToFile', 'terminalInfos']),\\n      ...infoPanelProps,\\n      appPath: this.props.appPath,\\n      rightSidebarWidth: this.props.rightSidebarWidth,\\n      showInfo,\\n      tabsHeight: this.props.tabsHeight,\\n      topMenuHeight: this.props.topMenuHeight,\\n      toggleInfoPinned: this.toggleInfoPinned,\\n      hideInfoPanel: this.hideInfoPanel\\n    }\\n    const cls = classnames(\\n      'term-sftp-box',\\n      pane,\\n      splitDirection,\\n      {\\n        'is-transporting': this.props.tab.isTransporting\\n      },\\n      {\\n        'disable-ssh': this.props.tab.enableSsh === false\\n      }\\n    )\\n    const allProps = {\\n      className: cls,\\n      id: `is-${this.props.tab.id}`,\\n      style: {\\n        width: this\", suffix='\\n      }\\n    }\\n    return (\\n      <div\\n        {...allProps}\\n      >\\n        {this.renderControl()}\\n        {this.renderContent()}\\n        <TerminalInfoContent\\n          {...infoProps}\\n        />\\n      </div>\\n    )\\n  }\\n}\\n', path='src/client/components/session/session.jsx', blob_names=['0001cc2623d432e450b4e2a1909528b41e3b6d255e27c5201b486e5bd5e701d0', '002526c798eae6b6f1e5564a5ec531ca94519dc5b15378212858dcbf35503b57', '01efa5eec46e01c387353fc818a9f249e1c96ae373772bbcf0fc754984a14026', '028d257467ab2d7706d1e1ada0697fe753f64cd8651cea3b99d7e949024e130e', '052dcef1d0f4992ce06a3cdf99d761e3efeca4f6624fe13373144f5d631dabef', '0564ba4dd5779148312e29c94e72d7fa787296d88e2cb8de4acd5df04022120b', '05da090a8b713348416639202ca6b41d42a4abfc14ee41fc42e93bd09714f4ef', '05fed37f133539910c82b395157c2075aa312ac1c460bd0bff3b6a024bae0a26', '073472d462ae124303aeb573ef8f7de5daf0590ea90fc88c4f115e04a5baaac0', '07b1e880bdf47f67b8b05673b9095ed14258084bc623fc14d03bf8363da2a8b4', '084683ad9655ee723fdf6ce315c441955ef1a627270042675071ebe12f980262', '084a62255f980af2174a780cdea0e2651d912c0560fa7f7bc85b274186ca9657', '088bd41027d7bcf3080bc6adcaf032d15d7922ce21f0e1e1f1e8ccfe4951ab9d', '08e2218a264d8d2a0339f727b75ada57a5708f9e5ea37481ce09e70c71655c91', '08e5098cc91a27ee79a155e58c6f5c0393475a40649fab3918cddb2ce40a106e', '08f5cfe4131ea41025a1b1b857c1420a54a42057efddeb8525d91bf3ba71571a', '099c34cecb130369ce1326a4ee59209c23555be716db23f0b4fae8f32ff5e45b', '09fe86f259470f72cb4ee199009fdf5eaeceea6e4b90902a0271787c1f9866ac', '0ae39d4a477496f80506d1f249562a1073dbde73b07e452b3a7fbf6926a105d8', '0b44eb813f8be1f4f241c290d13d68a42b62c4934dc54bf063dc2f12bbfdef4d', '0bbce525cf77ddb2b4ab0dbaa403962f66a38e6f5ae5072c9405ca1bc0b209cd', '0c745235596085c09e5b5f9fa3bdadf72c0ac619f6b6bd0d27548cd07218774a', '0caef9eb88fc331b4e1350c100307a45e4a6e3ed6bcc073e7c6bdf011f4e0845', '0e0cd0063ba07e6434b7f9fa5075077eeb808fd2fc664e06d70cef70c9b82f1e', '0e3bb66077c5c6404dc8796d2aa2edc6320dddea094ec3503cba17ab72bced92', '0f8846f52b5e5c33b5b802f37c5502d3ceb00d786d40f8ac589bb7678feb3cdf', '108946a0b7141dca23771b8ef0c912e4a8d091e7edf890d54c0e19ee9298a069', '10c19e43a0ce284b2965984173e0ed9714876fa312c21c345624e57032b22f44', '10e77fb18232b6b17d7c60df4a2524e891b87337efecc113fca79f18e392748e', '10fb127d295921025140236eb64172f9553b0f48d246f49e946e6fa5d10d47bd', '1126f9d2c574cc8bcc0c1d84fe786996bd5a98fc9a57d46a5f74b5f87a7034ba', '11621672442a0620693a16f2293b0806bc3a41e07ee9aa548683c9dd14d7bab8', '1267f9e1ce1db2a19f92bc37f30b9a679202a1f47778711074c3089ecf74d3d4', '12cafd42f811852d8f46b74cf42f7ea69b64560ba7adb1075343cf941ffbecfb', '13a5168e30441da3cdde6cc8d51672e4733ea276889000349241a0bb86004059', '140c03bb6ff811e15549efd7c3727ab586ec87d8509a680d4964f4a4c41e8d6c', '154135a6317d0840ec9742fad5bd38ebb5e3a4e8ff86aa7ec6d658d7cfd321cf', '170ca86ee8e26f5df14f34fcdafecd5e467777d3933547b0b6eb6c8ca7e6ef31', '1777002496dba52eec044b5ea33fb157d842abf98b383c5f4c4bf54f07ad154d', '17e1b2c975c048b765a1060b5f112d4ef185a48311fcd4f7a330e6bdeedd48bf', '1894ba61056a081a6324f413924a5c72a1b67c6bbb267061a69560ef607ced0d', '19cc22a44e8a3f808dd61c700955558fb3a44c63d4af8c2f7057887a8e111eb5', '1aa33ee1b2c1bcf959664d809f48ffd214b77cc6b427a1e7892581a716d718dd', '1ab051e34083955b94bf4c748f6611df38db9f32a1051ea57a2a44a126b1a9e5', '1ae26031bfdf366f0142669af459db249535bfae87942e805216cb7b1f1c131f', '1ae593ab9619282ab1a2cdbb5926699681333894007644228c85eba9072eaecb', '1c2081350c5545eefac26971bf4395b64435592eb055e81bc139e097a06ebd99', '1c80d346b6200602c572ce2389f6bb48fba4932a68725e697624e4d5e86e90c2', '1cdb0ed2e09ed5079d751bca91af6b6779c3b5924f4730175f150154f868845e', '1d427c8113d481cd4bbff61dc6cd93684936bf4dbd434455cd0bf93389b06ed5', '1e9f4bbdeb6cd7ef44470c830f3da6c4240fca131581bc25c3fe741bb82e3c22', '1eb350bf19f43ad289aa3cd7f25361d65b39bf41ccf9294f306804127b564379', '1fd77e5451facaff509bee58f6a4813a8bea3bec408d80457b9a62a445030b80', '201e2ee34be2150e9de0caf02a46a725ec44f16d7b664975ef06103d2ecf5c81', '2075129edc8d299771fbd135b2722414647e3964a423ea4787a16509e9ff51dc', '20a93697cd1dea3b041e8f98e80693763120959e747d0eb9d91550b269cadec6', '2132762ab51a71821dc80042a73ef028588f9c2446343a1ce03515a69e7b2375', '2147afc53443841f0642e9ec7e39be525d8804c83a174f8e37be5816152a8358', '21d0dbb99eb9b6f0c431c25c3882b9a117f9e4e8846a77f12a0379210b95c2db', '2274b6a5b80bc3e3a68082263dfecafd929078e702886dfb91a80743fdc714ce', '243239cdaf6779673dec956d70a877fff74ca7f6b58c0034034c6083b2f54c69', '2470d1a12a512a8f38496da1d3900be6feeba106e108e7da1329436b5f612ab9', '248eace11ed73c8ec8a7150940dcb35444987d9e51a9d6dc231f7c76ba24cd03', '2563c0335b0dee90d7c06c1df120af250b290dc8fbdd65231501a77ec79408c6', '2578335b0e065d01bfa28edbd115453a39712dd12e925d04db84a670dedd699f', '2617b6d8230d32809ad1ef3924fee5ba1827b70792c6c3f5fbbe0c0d4c11c88e', '262a9a01cf4d75a2bff9a4022455b9bc6fcd954d02ee4823af86f2eb0f414176', '26c32b5aa7562dd0786e172ce289a1dafcdd073a1f5c649550de405b209737bf', '26d4ae2acc6bb569247754ea92923783169e2607063afa82dda9b8c500932851', '26e775461c03fd5c683c33036a662211c3c4215f3d04ec52af6995319e9fd8d1', '26eca60a5debc412d31ba6209754213c83c63c9ef31d2199065a4f8d4e6ea513', '2887e61aa94f8623da6ca427ddea0a6ac686620630ed7b6a7bbad075e0126c14', '28bddfab3d9a8e569536ed3ce337a0dce861a2f1a9df9283d99fdb4ef68d8c6d', '28fd40a19b6bcb9cb780b2689795d9cb024d38ccdbbfc141f43f9bfb2905ec47', '291d371039c9243042c6036bd4d4b69a535b7a07bbe42b17d5ccd19b1c1aa4de', '294453aa7d6a3f3ed51f3d7cf616f1a014018e55fcbff18a70068b38ce671cd0', '29e322ea8e42eb526d319a01f9375e850ce483de7de0324f7c3703267d52f661', '2a746b2ebf7582779a7742ef61bd849d816419129c741a418f2dbb1e1e4c0e2d', '2b36232c5452a9ca96d483ab3616efeb272991cd76975c988143b8cfe9ffb693', '2b5a38f696be234daef0d28597c891fd3a21c91ef9c7e5920ae6b62d85ac0ccb', '2b7fda5234d7f75867a9d40ac4f38ea8e29da6de3fe11498060773d14658d67e', '2dc76259ad6afab4bc833870dba3f1b1ec3af26e4f047e6535c977be8a25d2e9', '2e6955daf55fc0361ed20dd3acab0142293921b810bec84499146c0cda8ab67b', '2f50572ccb18b166d2447264b9ceea4c44f0ae6b317825fe645b1d84669a95c1', '2f8c7ee84b7b8dcbcf1ff9e76589da8ef3d66b74ddda3d687906b1b71395bb62', '2fd34419b62644bf79d3c4460277afea75164e2963f9fd8b7fffc1f516f95691', '31447e228c7ffa31af3b93d76c0305a191bc462ae169074dd0ee7c3a680dd023', '32022c8d17530f80caafd518b6e34f2a36f46957e020b00c40c848ab210c9050', '3518e4b03edc645c9827b23bc4c47c402dbddead089c77b7b3eeb143395a6502', '352ce68df716cf6b280802fe313f753949d29b7c6f361f8eee77949e526d2b76', '35e09ce50368a487738323be8ef8b85ec26c7160f053f8dc30880db9e14ef787', '36882b670660b9bcf18211d98b7f675b89d38f134a3e3e668bad76f2c5b2bb6a', '36ee62c4980a1e3bb118c02f08e9e577418d085a5838333f7e7f17943b7509be', '36f6796930a003cf9ccab674ac72a9027a10c3b8babe8f0707a56e2c16b079f8', '36faf9ffdfe9c717501f4208fde8e2bb439fc214ba35989ca575bb49bbf46bee', '370d62473c00fb9b48ec59587660412d5e9add3ea8871e664ea3f1e40d4331e0', '3890bd465fcb60e06aa9b22d2393f6f501fff2937e29678b121e72946005b3bf', '38e7d15f09af5bbed554e2039166de000308b5012c25f49786d220eb98a78294', '395672710682fbc17ed63f81006472af289342423ef6acb61bd05860a76b9b25', '39c103062114c11bb4e3d3c52b95e3dd8b6016bd2ff78ab57e36aa3161c9781e', '3b5f74f0dc99e1fd92f7ade582396156be66ceeccfda4ee242ebfaa5d309552c', '3b8b26881ec2858e3db00f7cb43835f87c38d763c2d5f39b012d63200fa54ca0', '3c0c44c0205425f10f03fed6a39058b2646933e3ffdc5dea367f3de9b5935ef2', '3c3345a100e152700b2ad5f07df82ed707a8089a618e4f0f8c414becfe8dccc0', '3cd6fe3115b5c6a241339c36ffa2218fc47cba8830f771d59d35a4ffc45ace7f', '3cdc917f9830ac682b5bd245a059ca624d91c0a8765efa94169ce28f77f889e6', '3cf1df9d3d5a022671c302b6afdc50188cd0dccc66bd2d31ab0b57daefdc1de7', '3d3e94d9faabd84a28a3d9e0efadf83dab312092d7fef348f43b96add8c39535', '3d860a3c6f48328b7c8e9a23d7779c53db136aa16a9f3a4e2aaeedf59209feac', '3e2e56ba208b701d0a9a06959e1009827020c81c750119347ce0cf9758603f7f', '3e471b6bb3f1aedb930089c62e7665fd34030142cd407c0723050f74d3ee7189', '3ef07a4311fc0ca64834f9661abb93a288fbe9a6a2dfa5db5dba311a92ca2f7a', '4026d22afe96ed845e004a181418509f08a9516c601a68929b1838c27f03faca', '40dd75de751d2c1fb4e66738d62452f1f7fd7a018720f3f4894f847618aa4db5', '417b1ac77e13ad5dc489321a469dfb079857df78e6c62a5c16b55d7d412e8e45', '427cc9b7cfa3f15474dd57ec16082bf46729e7e027f4bfaa34c009df6164dc31', '429757830084ff7d4b210ed32ea5789363278d3e0cc2788ab1b846a61f331f1a', '43562d3a40ffc9ad6c0a63ea582f50889be4860a5ecfca038b246143544a4e3d', '43ab8b735c65eef233177504fefab20b6c5a3a7977c7666d04c699b1aeb8082b', '446c069973d5869a67d8d068b4169606e086eadb22a3ef80864fef78b4ac39b5', '44d7979b584c4057a1bf3bc3d5b1e08821cc14076e1f01933ceb53010113e7d9', '44eaabd41113deac784e3f1dbb6c8f6152612212532e29a30643570355c3312e', '45312d75b8eed9a6041c4a25d7c16413f7b517e27903e6a502c855c05d36491f', '4618ea1b3c9dd7a5be9d23be83c9a331d90731e984773e9e65f56e8d43dafc99', '478ef3ceb97b3ec7affce5d8b82d273af1420e76fd58d19eff2f85a53029eca0', '47a004c572956442f41bcb93487d23f33db7d9a5abda1e03b9bb34fda3869a7e', '48c0c381fce5adda970f2fc375a4a3bf16424c5e15cde378eea0153a0665e39d', '49f5bdffc201c0ff9c63532c5c358ede6fbf16b5323dc6784098249f645b5889', '4aa48f9a744872269b8399156311ec7ca07e83c536e825ba5b866e2cb20a7fd1', '4c3fbc213e85d3084c6f75efbecdac40a5b9ca32f5810e81296ccab7b04fca1f', '4cbcf2207fe917847392c731291ce3d8b0bbcc261db0cab0022831cc5377b228', '4d57eea48ed302911728b3bcf19b9780ff4d3f8f9885a3285f3ea6c6dc402e29', '4ee1d07e5cb2d83c4ee7aef22e70b2649a296ab0b6e641a25fabcc2afd6c26cb', '4f1a0317d3a0f4196967f2482b1c80cc953f77cafedc6064b78dc327c617ba31', '4f6d4ddcc0c7a14cd72dc61d44992bebd5dfce2bc90bb4490e516b6a77150641', '4fb7221186ac98670bdcdaf297c5ee50a2f5b870595cbd36be1fbcf4fbb8a05b', '4ff2a3274f5bd2a5efd72fa321f036281775681e38a9505edf19bb304394cd8c', '50bef03fe04ff947fb330c91dd42ca13e2487c0ef9da664ec965b47da920a3d4', '5127b16b733b515c8b533089bfe9954f23658c81a6d51cb83e10ef76f9b43768', '51d0960eb1be735962a906a66b36762da73cd1bd058ec7d1223a3c232b2ac0af', '520298e250a2a66894f981bd1bcc331d13aa9da0668f628fb11ddd6a0c83f51f', '523a888a01e724b7a9017bc70479632a4b6fb36c950867b40a1ed4a5e785dc24', '539b4321d6ba54534f4ffbcccafa827c7bf4b52e69ef516f89f560ede2f28561', '53f594325fbe1a185b96a785ea68ceb58177bd8af0862e56e40b8638fd1458e7', '54733c253ddf5aefc6367b7360fcca13c757e049f5029d3c4cc60f482d4a4c71', '556cf1ac1900c1b65593d31d95c158691f58b056484359ebf418a460a4e81b0d', '5596cbaba774f9a22af6a961712f52f8bdbb8b4bd8dbe0261a8cb4e16af68dce', '5599dbc481fd17dbcb0bcd570e480daf75f79dffd19071f472fbf68e36e95518', '56de05c4586da2178b128ba7864b4beebe1328d6e462d517023541ee04dd9e48', '56e4b126ff23d3328914a73abb9eeafe6c27fb4402f29a5754b1a22cb674d0b0', '57f3c37547fc6e9e9f077b2886c4f835f96dcabb9399e08db0bec6bc1461f4ed', '57f45a7f59ee1240fd199209ff9cb0b8822e0fec753d0b53e709a8ae16f5c6b5', '59ac952487b8c9fb8b2abb5fc7b1676d4def548146c19d93545e7af3327dd754', '5a91913c40e56e7345a968f29e59cf3d7256f41dabd854eb69ff0d5f870fea44', '5af192c72f0b186c051e7a4afd563ae748a2d08fa396e364d93d7e37c560b1cd', '5afe0e22831dc6e8ba3c43580c795906d9e3f36766ab9ed6861389c890897df4', '5ba661ce4f41e480923bf2312777a120c3974a79ce2d4b8badcb1ae332bd3003', '5d263facede25e8e3e6d94ea78b67d23510ca4e771055faf90f4ef847afe6b32', '5ee3d886f8093fac28bff7087602a81e0f52b62d1ef8a96e37e65581d3faa7a4', '5f2ef143dd8727820aca1a7ab8d18d881ae9bd0067b048235d966438fe5e616b', '5f65ea749575cc9126a8b7fff07a5acb1deb5afbfd331a0e414120ba6baeaf8a', '5fab26896decc97ee7da7e8ec756e89b749f15960179ad586613f27f618b6468', '6020ea15890a948e4abffa2c8b82ca0ae25464cbe0ec824b16ab67caf2f3bc7f', '60cf901289a01e13378a2702a48fe8951d5fa0c6db9aa1014645c29d2377e5c5', '612d8d3721d6b7113cfe38fef3463e912bdd9bf8e42a26a9e32458dcaa32b981', '61481287132d6120331de73f93ce0e782345651b887128d46295431cc3285dbc', '617e419a258247619b0daca647de8b53feb4905b3162aef93fd9f967da1a49f1', '61ba151f2dfb2be26f643b3d5b581c1657f0dceb3fb2be3e9f725bfd336b3d6b', '62a07a8891d298fcc5f725223fab8d863f17ad6f9c16494aefc15bbcf30305a8', '633447c734eb51313c2bc0a6a813c29576095463c103e0e55a982220e83c9cbd', '64554ae86eeda617ae0d19d6b1828d3841ef1d3679b118e9a3775e3c79a54724', '654476d7889c45f3ff6abcd6457d6812bdc92bbd66198066e4fc016357c5d183', '65cfa8652aaf19649b9ecfde89d97e617f6d6f430e56c2a98b243eda0f15dd33', '67c344f9e1dd73d8120ec8e2580f87ddef55588e7393ec95a485946d62f5aded', '67fde7b40dbff81d4b7a1b77a99892784cd492e75a8de031733877cfa0691be2', '6831216db8bf21737d83a81d8483090d914bb7b9e2a37aa816551319df25f1eb', '68d243e8838abdc5ad067e11032301ea20b565d68ee060a9183e0a34e8cd9e0e', '6a035adeb3073bec7db6f1d79c4bec21c025d885b9be70b9b243ebc4724f8c8c', '6a28eb12b06637ca6054fed89b9acc75c1c1223170da267fed59ac2d6f175f9c', '6c4f27495490f5ca88dab8e3169e5d919b55f94bfdb4c886685e0452efc93171', '6cb8470c46997f57d68d0a35bd18fb346791bc27e0353e4966de55e733a7e347', '6da87cac69bfa0fe3eeae8ea3a2ddfeb4fe892a8c7344960545b66f9c652b7de', '6f79c77d374d6badf25fe2f82959c1d8052e768bb230682c484d430d8b77f027', '704bbd028d1f3939d31eabdf0be5b0f838602223ffaa89e1eedf0356e04c0308', '717ae6a2ae0a67fcc68ac78064a076ff902891026a2023ce79603c85bbf38d3c', '71c973e7f07b208b8078f6f614176091e48eebefec283227afd9d582875e26c2', '7207ddd6bf22249f4fec1bdd6308a7e41a17e58f2171de18b13f2931aa95b5ca', '72818e9ab9b941564bf59122f5265f9b3804569bbe71ced9614d3a0d514f54ba', '74b2e6c68ac68445722eeeb4201a6bd09cfaa1aaa17ac7b426637078e70fa68d', '7513a82508530d6634981f535dadf4678e8b97431ede3d586932bb06f2d602e7', '76620fc3772aed3b0503d8b64f35f10f10bd974bdf6fc9c47bc1513a304ae0ee', '76a0618cdb9f379b2f65f7f237fef34b13eedee2419ffcd8621ba51c9476a22c', '76eb9a55dfada4d961b8ac0ad53b2cf8a4b2fcc7cc87fe3ff3e7d4ef0ec64eb8', '780e0aa490cbc75787b7f6b8ddb79f306e83226bb01b5f6fde0358cc66ac05df', '7875a89e4d9a7bd3a6dad39e36201b0de17bfc9eaf156eeac24d7cf409fb33f6', '79d5c86c19d84f9da0e16ea218d9f84d645e0b5856d9f24e22624719a1e1ffb7', '7a072a46e5c198473979c22cd42751f3a269bbacbc46ed59262e6a43c1ff5908', '7b0c26301040b03ea305214751325f0668f3e1e763b1ffe63ab7fe9378daa493', '7b331e23a02877f6176f7a41dba1636d0d0b7a1e32ed034a7478dd916891cfce', '7df30777aac14d199756db68c60c81a36de4f21639ff914be558febdd86b1d88', '7e6ee71d6c9d26ce722bd0d59012a79834eea84a3679b3e9da9853b3bde96d1f', '7f3539b948a95173569c6652c5d6fa320195911172b12a7517eda2248e336de6', '7f63ce47c56d2f6224d51ee371f1b3c1f144962192a553c0703e143d92fb159d', '7f6c9981d53532cc9997a27bc90e7b2cb39288e02fe8b8d97893de98921963dc', '80f05cb6daeead8160b9410f153244b1658d4199a1d0608f3fd030156bb0263e', '81162d3e52cfda411ddf4d6a37d1df66710cee90cd5fd598b193ad1df17438fd', '838f3110f6efdec754b173ca4c284243a033f3329c911fab61539537a181c2e5', '83d22128eb496fcb9748b8763897a3ea4fc5b77beb51505c747e510ca381fa85', '83ec068e04f2593d80367d4b809f24a91ba097dd2476bfc024a2e9b873abc58c', '83fa3524651ecbcc53346e8a9f3e65737cc058405a203cd2c852466c554d6e24', '8466f2d3c0597dfab805b9da35ab31eac51e6b436a0138d91ae39049c4f7c412', '848dbea5849cbbac3a5a4fea1b764ca8c0125927f0396511d5ddb635553c54cb', '84eec8e8c77a5630a36b6e4c66edfcd571d6684c5857b80d9adecc90d68b93e1', '859aac63f57793b649a9a95cbea3c08282dc92415d71b6cbb629d2b3eaa4e12d', '85fed4c85eeef48380da95db6d02daad4eb085acd5b0ee60d29806499f924f06', '876a601e6d464762997d16da5577e54d7f5ec5e74d16ac0b04186eeb3ebf85c0', '8784a6d6f226b00f5e424fd8ef37576c68e4aa217a3afc231950a1195f5a39fb', '8794e411052dc5ff228457cddfb58bb4b17e893d7f737cadaebcd4b88a2d894f', '87ddfea48b9e0b74c632b867e9db93706d8535bd33312e67b92654fccb9a47b0', '88861ceb3ac66fe08f35e41acd9d88d5db220cb05bbe1296688f77646ac86725', '89ca7f7e46a061bc1c30d629f13ffe697a0da3c98041e591e6c64421deeb806a', '8a95725a802ce065062aa849ab35518122ee070b3cd4f4a736e8a55c1f689a9e', '8c0443fd452a45dbdc4c51c01954877480f1f08771513858067b14ea389b2cfc', '8ce4b5a2b4951c326d5d733d304acc17a9c079849cfbbf6d0ded9516dfca6ef0', '8de528080c58942da07d227ed7a71415f5dd48dd56a178d5faa956c43da72cec', '8de8b3673fb8497daf3cf85c68b28742db9ec3077ddcc5eff1e591e2f04465ce', '8dfe39f75a9f81aa78b36e1d2c92f24be64fd04a5274adbf2091842caeca62f2', '8e5fdf601f083862e08977f51d5fd853d498634d5d7b50b1f28a57aa9296f45e', '8f28422f4392bd5f3769b6756b749dd57f585cc28cf39833964766cd1ee747b0', '8f47ba36ff52b01e6378f06b95a28152842c28567acbd4606a0083a1a0bab7c1', '90ffa1a7cedde18535acda1c3b064057f9f727acb26902fc7756ab3350dbeae3', '912f6aa68619e8b7f77e17dc0bc67f03ecc4421c55bc9edba5407dfc6aac0893', '9139220baf2c66ff4adcc39eb9a89eb948abbbe89628450f7ab3a67c79771646', '919662ebd1796bf38dcdd04bcead2eedcfbcd2898924f981685e921971efe748', '91eaa5358d04a158a7a42dafeb3a46f6122c8476bd57a328a453dc742ea25944', '92a5fcb58305c1143be391c15ac6a69ac49ac7caa3ce71b6e11da170ec14745e', '92c1729169dd6d91da225216a129cbd4304eec27c72d53f3bcdd818ae90076f6', '931723c944678b2fe31ccd7bdf6513dfb53252fe49bff2aa4e630c96180bb2cb', '934106c30aab0e5f03865fb4e66478939f3c20e95b755420b50ce40cdb1fa329', '94645d60598946e411df81c1099a0db4687dd6a5f14d2f5fe2f3803845509884', '94b88899e5510096eae7c6ed248f943183c1b82454aba11406d3b4995ba1d919', '94ceb998190b04e5009b009094b2f568da44532e2f802e6349ef0c77d814e11c', '958d1a968e566f10a7611a67aad28b50260d1fb7c890731cfc108075a762fcc8', '95a6df2100eb0f324368c6a41328e7cf78fa26ef0039cccecb1fd4ee1a810ee3', '974a553f1fb204be08a9030e88965f2623ecb72c73a1f32b6ab5819435250bb4', '9764b3fdece92836771eeeb652e89614e0b3e952405358bf3c2d29252e78ab82', '97c022a8f58915af4f14de7fc87e26e751eefc06dce2a73c93872ac17385a4c6', '9830c044e22933865e55770fed4d363a0c8d4935ebe331631f10bacaa057ae3f', '9837163475172b6339cd1628e06c66e73f62b8e53818ab332f1cf8fa720dce03', '98bdb5bbf580c391c1312fdb60674d349ab628a7f883c0f5dda277f65baf201c', '99cf8e7bd4fd90922a07191c87b8edd65626e656073eb39694cd0f1191b9c013', '9a62f4747bd3a24817c860982bf5bf238d2a24a7790e5f05990b01bf9a6d89b6', '9af6fe8e713631426f0a36ae53511a2edbad7ff896e58668b6c0ec4b99a5d0f7', '9b0c82816c1913956be5fbd583d7f0e2f8074916b55b21b012f791cb64010477', '9db697148be93aea1212afe9d50f2620abcbf431ad618c2427cafd67299555f2', '9de1d8908c13d18286d8735dd84698a07d466a649fcb50cf8bfb6cbabbce3810', '9e09ab6e48521e3cf12fda589dea2ba4eb3384dd649c502d5d3857dfeb8b356d', '9f52b883459570b37bb876d4da782bdd1ad5eb8103e34a974cb57e9c2e319c9d', 'a005d633782dd7bb58f827b1e2c08de0c44b8b067b125629ed7c37993812dbca', 'a03c0f610b62d153bfba90037424ae7db68a531b8de0bb06e62f409c85db9dbb', 'a08214f5f55d30dd7e631cb0054f3b070cc13eed8090843ba194842738897bdc', 'a134b50cf49539e9cbec522ad36acbf7f6f2c33b97eb1d63a15227e331809765', 'a3d4a854e4c9f80414b3aa1b3ed552322fa9dff1667b7324a97a2a105225cddc', 'a45845c8a56369abf08c5ff36da4c7ff6bb1fa681a366fae0c530b3b710feb00', 'a5545490dfef5c08b177fb920cb3ae6e7915c55cf2f307da962a68c64a38fd69', 'a57da9cd05fbbd6dc0cd9288feef2a97fe3a13bd3674dca76cdc2ed8022d605c', 'a5c1b27f21091fbe9ea8f49c8ca0ccbb348f2e4242b88f4b98325c5f8c7b77ad', 'a5f3237b2876da6367c31a5a15b228e191fe5a42057c266789694eb6b1819ca6', 'a617c33034a91ea7f05a974ca53ff8c7952f8847af92200c8107412b17730d12', 'a637d1632d837f1694374a4d7d1c52e7f0f82f2d273b07f4c85353444a756ccd', 'a68c236ef57f125600ed595f7a636794ba38902dec9c9498a96f84fe40af4102', 'a7103127678a695f96e568e323f542a58ff812fa00e1805a916faacda9950342', 'a7a3b1b80611d2fc482c76618ea51e30fa7b84b2492be8d19458c7ccf89db7db', 'a7df1d3c0241f8d46ef30ba96ec9552d632dcc19d37bacc35efcd5d4d6420d43', 'a7fbdaa3b06c4b7de69efffc4a1ba2580ba03518bacdf5eb049fd612c7d6d25e', 'a90da206ee3cdc3f21ea22307f22efa3c4e4dd1094f8357d64af4e9faed27d92', 'aa11b9c60050a2cd2135e7f77d5002cb10ad0a46b7424e630a7ac5609f0011b8', 'aa3e4896761d554401838d15327f9e1e6d611f7831721b31b1357dee5ba9b0d3', 'aa7efcf3731ef4206acf01f0ed1bbd078136100b0f9073a085fcd2dcb354fc3c', 'aace9b6c226487c3737bf83bb073d4397bf598673c00739b5be5a61def3b4868', 'ab9b33647d7b174ccb32b778f4c62369936f99575cc0e184ab2b1bcfc613bdb2', 'acd5ce559893265035b2c963f06248ec580c61fc89ee6cde970ae4ec4d7971b1', 'ad1837c6ad5d9417a01261a3b29ef931586f7afe2673650e66002a669c8abde8', 'ad4619647f3f1c82898b9e200dc2053c7641b116a4e054703026b134a6a60cf3', 'adb78f6012a7a7ba673e5b3d72f87892cf97f31a280b3f0d4539594824961088', 'ae897da1ec18539cc6879b7339a17ddafeee630f4853ba4fed9a91eecdd04db0', 'af3c78bae3b999bab76449c83be4ea3503a8f0191519fc78d114e898e7f7f42e', 'af534c46751b914199facd499ee35c63fc61446992aca3663a4c168f14f7bb7d', 'af9592107d5b4b97309412944fa6b35bc6984ab03198c6391713a50ca3b9bc50', 'afd75407b1a5ad2b20b48efaa9566f293c849f9c6fc6038fa25531d176b30254', 'b07f99e55cd7e712327e636fdde758259a5698b3e6894fec2b5c97bf12ff361c', 'b0c9e17d5d1f04ea543a6527315500cb2dbc248359d9bc99f9d7dfff5a8344b2', 'b17a7baef0f6cc05771befb7766d167c1fc11b8da0d4ba4089813d9b91f44584', 'b28c71fafffac0b96778c90ad10461073d84d9ce19a8d2fd29cd1cac03a6685b', 'b31aee27c99ce55ec977cde3b02a4b365470009c4e7fd8684df502a2e60240b6', 'b362c7f9d9dd68224d185892e919f8726881e5506fa0c6a51661ca74497931b4', 'b3d380db38d093251433abd4dd3af84f425366e83c9400fec83a3f571b0e9f25', 'b3d763e49f646ba9349c266544065c85402963e8c61c2f8b10c6dd1bc2ddbfe6', 'b42e0a0d00d928d27fd1e5f001761f6bcc2f4d501c2d74d4b2a7269c04c2144c', 'b4683910c46857e8bcca7aca1767ded4dca0af5d61da34dfb844478aba045573', 'b4855c56ebcc9fed0ac7377747e25bcc2030bbd41f4d2a44d62cee5082a44fb3', 'b54b6d96e43ac99ffccc17c24459ddc6ad80b980ffad3f6fefcd0a55e6da49bb', 'b58d76618ecbbcab3f6bc9b3c722f930ba9d5254a805bedecac1fd4564d00c76', 'b59f84b76c3dd01dd1cd83f6a5cc96522e38935fc68401fd69a0a7f3d52011a5', 'b5d0eca967126aadb9cf7e4003469e586f6ff2b0d92c23514c10df1eb3a6384b', 'b6b85f7e92c35d3eebdc39721a07ba99a1159d5e83c29ec692785cf2ee10381e', 'b6f636d966e83673f0d90512d04c027b6e34c6e4e259a9d682b7824ef8edf62e', 'b7210c91a38efbc2a80a235daa8000ebbc237b85a5319abf6e1fe6cf00938208', 'b743e74b9efb89a63cbabf56bdaa7ab624bd99258146a862dca9f301af73216c', 'b7a1e548865ae551d8e392eaf10b97c85ec507bee4d6ff47b02ca39d224a2d2a', 'b8c3c227a2d15a6a41a977e7fdc1742e1d1e2dcbb61c71baf6fefc0a0e909fbc', 'b8f444111d70b06f238521cae9f3de4f4659067ffbb5adbca0b697409e128e32', 'b97a035a5e30ff58bc4c30458eab3215a5ece25f5257d39e952fef185cff5203', 'b9ce81a6721212e9a4f28012904d7d538b2e1c0a4e7285253786ed03bf9d2717', 'ba28b1bea31860822e39b90fe365a392fa58521fb6bcafe0b098b4fd7557017c', 'ba6ef3ae1a3a68715d2fb3e2714b9b9fcc38600920713999ac8b1e28a849a1ff', 'bae0213dfd1c602fe418e5c229471cce488a974839bc18972740c86ef0c0fe6a', 'bb4fb9d44bee5384b58a4bf1faba35d165f36e64ccea4249a32a1773b3d9af99', 'bb8103b05aedfbc3095cdc07aee22219ebed24172506a094a5943276beda3e3a', 'bbb7653232acbf551f79f2f1d02c80862b5da30b3a32fd279c94134d784cf95d', 'bcf14ee79f4dbc040bd363620bfcd1a2a28abe35e19bf996a87e67c9c19545a4', 'bd75c869908cf5d9d493645b514f1167d3c8f35f28aeaeb38c60f3ecb2ebb5c9', 'bf1a51961ba8c06fa29b4af763891f7bbff878361190171164c356eaf5909f8c', 'bf4d445493c21a1f191744ce1b82ffa4304ed517643f5c939721033e8d991fcb', 'bf8b02ad2cdc7bd51228a052a1ed16b704d1dee68681c1452c870450a0660d07', 'bf8e4e19c9e2241497a859f71a271f341f54fcd58939fe7d76abf1a490a8a636', 'c093669ce82e731ebd6961440925ce8c99a28d8bd9e8f8bbed155329a7281a8e', 'c235f25eff7e57dac262f9d4f3618f7af1d5742b5f2fc04c2ca3a895716bcf25', 'c3ab39fbd71cd0e88e0672b6f4c8dd50eb40ee0f6d92b8de30a0aadb72d70afc', 'c3e505fd82f9cd6f631ac28ae92b7c1fa0293315032bc66ad778f86ecb4f9d15', 'c437ed990df0812a6505e4a4379bab5fca7f4085ec13acc1b85802fa441c8cc3', 'c55ec5534628ee6dccc5f04d6457f5071e9eaa8413df0578ef0f036873d29268', 'c567b8b0a00ac14e320d60a7a679b40198c91c95fd265228da94155c6ab022b0', 'c61b4eacc84a3c507ac6a77977c447a6dff9b06c010b8e67000daffe220ebf7c', 'c7e21a3fcbff2569b091d56d98398b8e4f22d172ff67d0dba69a25525cec7154', 'c84c75ba1a6d2e07b306e010ecdcdd3d54f172e421c111d8af7f77a1d56ba98b', 'c8cbebff52170a1fba1545c9248d9a5c9d2ebbc7359038750b8707f30d2dd5ea', 'c8ef483f1621bd329ee0ab4ba5f0740fa18fec125007f1deb3af140ebc2bbbb9', 'c91d828d01a8531dcf47423c3625ef66eb359a9004fcd1034b11c2f8f14f6ba9', 'c9de68f7a4b5d872fd98ea8f1e3d724f30e2e233f11c03cfba7048d707b041dc', 'cafe95cfdcc0c781c895cee7c9c04cebef6b73a28e2756b5f275aad72a6ff0a1', 'cbd675a4b514c4fcfcc539b3209f0d7f0f1cf805766f46ef97d678ee554ae0dc', 'cc2906e853d81dfac6349b5d0c8f3ed8b4d64e34da56ce3ed5c09a1aaa394c7b', 'cc4c0b4fdc27a235dc2c739f62362b16582596ebff48730e6b6acbd55766bd0b', 'cc67e655505e2b710f506e1f922ce77a362cc681341ea896f1c64992525529d6', 'ce93ac76dad61b4eaeec9646ca8f1134aa0a7c7b57f4431813a3c41743176ca8', 'd01e2c63e9dfb6d6f49418f78e4fb3f4e12ed53fa618e4e0062a9be3c047805b', 'd040c254dbe34d5016f5e0ad9de94302d161a911e90dcc696bf78a1fd00da246', 'd1683eeb46313c89d0310fd061ff035c7aed355cc75502f2bf6a9adbc65d8939', 'd182ed939116667dce8e7742ef06b1997801a213470ddb09d7a21507cf747d5a', 'd47842ebd87c8b5644372a9262f5b0f6b364815ab7e798419f2a2b577282d056', 'd51d0a659b5be6b6e0e2812938b84392ab4b367308d03e45aacb948761d81dba', 'd646aae4af827a91d22fec399d9b62feeb6b7e2228189fd02a399c2a95d75db1', 'd6d6513b29d72b98a5978ece598d62e2734ee89554e2aa5fceee3bf4e590e701', 'd74d2a780100e2d37053e1ffb378f2f23735540a770d8ef945f9eb7116445e5c', 'd74e13105ec3f7a3cb6c0a6f6b18193e6b3de0a500614f534d53ca11df213c5e', 'd7ad41a8d5025000b9045ecf23afa513e1496b1459746ceea4c6491fcfa0e453', 'd81cb499dd34ecc6fd92b911a3d95cdfcfe1d650879a63e42b9a2478743695df', 'd98e042d8df136b2ff202fe8b142fe30ce9a68ed9136c15dd8b3112932c1e2b0', 'd9bd8c5520c111b9ce0e79335bf2607beca5ac85f60f75dfc12319a5db55a900', 'da6691c6dbfb29b64cedd66ad6bd8962c8c273bc0f146982896b6beedf7079fc', 'db4ddfda8e2f72cbb7095ac5ca7bd721886b44e65899792fc27329a40cc3b818', 'dbb9e8321f10e2c2548aff16dc73133ad446c64c93ad1572a13664d77574434f', 'dbff67367c26f56edd9eb281aee60f76834eb46466ee58adc28d89f2b9f9e3d0', 'dc0c05474f8a16a2904d2ce04b1fe1c062dd29cc280c483689205d8bedb3558e', 'dc33e7caba0e71f687a97976e8139e9fe8fa8265d49205daccf5e53a67098b08', 'dd3a0ff8263833a31ba4d1c520edb1fe590e0c26ac27cda9e9c31d4738979e40', 'ddb6c3c2cdac6f6303c08d8a0871b8c2d5f493f4854630ca56bd7727d7188220', 'dde42e432c2a0de214df7f20f51e5f698b1b58b8a19a802d43646a277e06cc66', 'ddff35985580f72bf31a7744320927f8f50dd0f0fe78e500afbbddca2afd1efa', 'dea65da1623fe8117ac4162da1db686c9d585b8e61c696e5a6595e62bb37ceac', 'dfec1f6e49a4bf4df0eb1ce46b809d486f31bc3237441a2a4d52b770d58cc01b', 'e078ea0d9cf5c6819d923127b756170b5f6cc26f7ea71eec8aa9db363051b281', 'e082ac845b2f3a645789f2bcfb68dc7b1f01f7b14b8c27795b60721019f45213', 'e09cf23659c6a0ef882462d938b8f9f689e9596be27f2dd30b729621e504c17f', 'e0dd7d83729c6daa5e0b54cf20fd46e0c054b4a0bf0cd8b8de3a6a03d1aa26e6', 'e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', 'e374d860a3173b98dc42eec347157f15ac74048e7033506a0d7b4d2c2ba5a318', 'e41b7f0113c313d205857babdc804c0b569c62f29164786550a79b6e12a5325e', 'e475605debaf99da23b632fcc54c26edbc420a2b5feb60eb981db2bdca90d6ea', 'e55500cfad16ddf6e10d9b111fd2c30f8f37b633da81e9e1b04bb40e52532ebb', 'e70b78149b18d85ff7c083c826eb706bf18bf4573869e8ffa47b5887ac1ed1b5', 'e8b1a68363b0c56957b36cc4a87abc3182a74f4fdeced91108a8897e9c871951', 'e8efd691187853c6ae108eba53841e60f52a33299a7b20a956a137475e20a5b4', 'e96c866717de46246661ec7f382d2162677d04b0256aee238d151b61f0ad1b05', 'ea62c40ceed6dfe2221d453c48d1b75b2e07ef06a38a32b31a756ad7860f2e3b', 'eabf56d2b2127ffbdeaf8f7a3bd57e98bc5d78ca52a0a6b31ad7b3131d25e086', 'eac8701e312e303b658d8f9a2ab1fd9620ec620a4c6e244b1c0bea51602bde35', 'ebcc8fc668e07240938a6c14ede3896a0a69a1b120e7c17d6201bb8f8bf7891d', 'ec44a2124aa48a271815743aa8fd845f7659d74bd3099f68b280fe17a822a122', 'ec6da9d643b328376bb64a9a021f84f1d70167bf19c2a8b659912c8ea61f06eb', 'ede6c4323af8c7758bd0ca5cbc2b7c00d32a92df6e2de0f965df27651c6abdbc', 'ee15e3c97cecdc121ad35a831e151842bf49fa0541386132d0530fa8d122f800', 'eea66c030e791bbbf444c04fabca34f67a00497d6f1a6afb96b384a052f40460', 'f11144d0ed8ffcf6339a37f88c687c0b5b510be7af8d78ea09aff291325ad4cc', 'f13ae6bdb1905d0d5688b3f0ce5b3e9378c6abd3e20081e6e7304f5251991553', 'f177b5ac226041a0f755b369d5a9b72b63e9c31851f210f322c062976fb2cebf', 'f29293d519d8eb4e87836af860ad4f0160261c7f92fe8d5ffe825f5c3954bf31', 'f2d84e96a1d6a3587f73c7da809ead062903e12e4a7b0fc8f23e1c469f9cef76', 'f3848a3e50a14fc32a19c0d86547a262affd23b9362107b428d6bbe9b6d31adf', 'f47397fc42b952b8ebe2e8d72569b671b98a5473f6f52abf2333e12b35586b3e', 'f4835f9f85754243a97aae226d94b5ea1821c43f8fffff23df35fe3df38442a0', 'f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', 'f6136ac0adfe01b69b8679f993c3f408949abd940a31fae2e7d59349d58b218b', 'f8346c9596d26ba146f56b78a19633952da4e31c31f4001c12cb78589a14c449', 'f8494b9073c0eb9edd88fbf9c0688169e1e9ee0d776ae3152284f53c1b0dfa90', 'f940dc40d848d80f5f6ac8120405c47ab7a29861fc90359d0e22bd1a168f78d5', 'f94314df8a1310ea64bd9b9ae3e2f7a68b68e4f3a2922c9a133e22b596e34e47', 'f97db87cac67949427396689f67667f4ee7399599263dd8cc8398f1d0e374129', 'f9a061ffc0674e930416b69b472f3dc254e63d05cf5f937666fc112668390a4d', 'fa313ce0fc699f879c758d98fbe434caa684e0e3c59af64288ea5cad2587b339', 'faad63c945411ff4d4f2e374e2bbdd37a0b247540a69043bf5bf198158bf1adc', 'fb793831236ecdfde853dcc31a8ea3904967db5964a29a60e7a8bb0b4c0dab9b', 'fbb2a8c539ba4e8c1f76af246c992cb36306e738898415bb4e69ef31b84a12fc', 'fbfe90ed1339e15d31d31d72733bf063f8200145f8a3eb25e6ecd61e29a4ea98', 'ff9072b4d388bc4acad6d3378fa3bfedac95192713aeb41f62d3df4374325bb1', 'ffa5885dffe772a0f408c8e410c674be2bcaa47b5f84116c43409bd39ebf32ed', 'ffbd323e0e454750d93be4c223a2c304401965073684aa650e29f4a7def524e3', 'fffec7614505df99cdc4c1bd865a4c2abec7b54b7c5d3a5899ef10a00ab417c9'], output_len=64, timestamp=datetime.datetime(2024, 5, 15, 11, 53, 34, 405676, tzinfo=datetime.timezone(datetime.timedelta(0), 'UTC')), position=CompletionPosition(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', prefix_begin=11387, cursor_position=15995, suffix_end=16126, original_prefix_length=4608, original_suffix_length=221), recency_info=RecencyInfo(tab_switch_events=[], git_diff_info=[], recent_changes=[ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(1027, 16036), replacement_text=\"import classNames from 'classnames'\\n\\nconst rebuildPosition = terminals => {\\n  const indexs = terminals.map(t => t.position).sort((a, b) => a - b)\\n  const indexMap = indexs.reduce((prev, pos, index) => {\\n    return {\\n      ...prev,\\n      [pos]: index * 10\\n    }\\n  }, {})\\n  return terminals.map(t => {\\n    return {\\n      ...t,\\n      position: indexMap[t.position]\\n    }\\n  })\\n}\\n\\nconst getPrevTerminal = terminals => {\\n  return last(terminals)\\n}\\n\\nconst { prefix } = window\\nconst e = prefix('ssh')\\nconst m = prefix('menu')\\n\\nexport default class SessionWrapper extends Component {\\n  constructor (props) {\\n    super(props)\\n    const id = uid()\\n    const {\\n      terminals = [\\n        {\\n          id,\\n          position: 0\\n        }\\n      ]\\n    } = props.tab\\n    const activeSplitId = terminals[0].id\\n    this.state = {\\n      id: uid(),\\n      pid: null,\\n      enableSftp: false,\\n      cwd: '',\\n      sftpPathFollowSsh: !!props.config.sftpPathFollowSsh,\\n      // splitDirection: terminalSplitDirectionMap.horizontal,\\n      activeSpli\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text='tId,\\n      infoPanelPinned: false,\\n      key: Math.random(),\\n      sessionOptions: null,\\n      sessionId: generate(),\\n      delKeyPressed: false,\\n      showInfo: false,\\n      infoPanelProps: {}\\n    }\\n  }\\n\\n  componentDidMount () {\\n    this.updateTab()\\n    // this.initEvent()\\n  }\\n\\n  componentWillUnmount () {\\n    clearTimeout(this.backspaceKeyPressedTimer)\\n  }\\n\\n  onDelKeyPressed = () => {\\n    this.setState({\\n      delKeyPressed: true\\n    })\\n    this.backspaceKeyPressedTimer = setTimeout(() => {\\n      this.setState({\\n        delKeyPressed: false\\n      })\\n    }, 5000)\\n  }\\n\\n  handleChangeDelMode = (backspaceMode) => {\\n    this.setState({\\n      backspaceMode\\n    })\\n  }\\n\\n  handleDismissDelKeyTip = () => {\\n    window.store.dismissDelKeyTip()\\n  }\\n\\n  setCwd = (cwd, tid) => {\\n    this.setState(old => {\\n      return {\\n        cwd,\\n        terminals: old.terminals.map(t => {\\n          if (t.id === tid) {\\n            return {\\n              ...t,\\n              cwd\\n            }\\n          }\\n          return t\\n        })\\n     ', present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=' }\\n    })\\n  }\\n\\n  handleShowInfo = (infoPanelProps) => {\\n    this.setState({\\n      showInfo: true,\\n      infoPanelProps\\n    })\\n  }\\n\\n  toggleInfoPinned = () => {\\n    this.setState({\\n      infoPanelPinned: !this.state.infoPanelPinned\\n    })\\n  }\\n\\n  toggleCheckSftpPathFollowSsh = () => {\\n    this.setState({\\n      sftpPathFollowSsh: !this.state.sftpPathFollowSsh\\n    })\\n  }\\n\\n  hideInfoPanel = () => {\\n    this.setState({\\n      showInfo: false\\n    })\\n  }\\n\\n  computeHeight = () => {\\n    const {\\n      pinnedQuickCommandBar,\\n      tabsHeight\\n    } = this.props\\n    return this.props.height -\\n      tabsHeight -\\n      footerHeight -\\n      termControlHeight -\\n      (pinnedQuickCommandBar ? quickCommandBoxHeight : 0)\\n  }\\n\\n  editTab = (up) => {\\n    const {\\n      tab,\\n      editTab\\n    } = this.props\\n    editTab(\\n      tab.id,\\n      up\\n    )\\n  }\\n\\n  onChangePane = pane => {\\n    const update = {\\n      pane\\n    }\\n    if (pane === paneMap.fileManager) {\\n      this.setState({\\n        enableSftp: true\\n      })\\n    }\\n    this.editTab(u', present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text='pdate)\\n  }\\n\\n  setSessionState = data => {\\n    this.setState(data)\\n    if (data.pid) {\\n      this.editTab({\\n        pid: data.pid\\n      })\\n    }\\n  }\\n\\n  // handleSplit = (e, id) => {\\n  //   let terminals = copy(this.state.terminals)\\n  //   let index = findIndex(terminals, t => t.id === id)\\n  //   if (index === -1) {\\n  //     index = terminals.length\\n  //   } else {\\n  //     index = index + 1\\n  //   }\\n  //   terminals.push({\\n  //     id: uid(),\\n  //     position: terminals[index - 1].position + 5\\n  //   })\\n  //   terminals = rebuildPosition(terminals)\\n  //   this.setState({\\n  //     terminals\\n  //   }, this.updateTab)\\n  // }\\n\\n  updateTab = () => {\\n    const terminals = copy(this.state.terminals)\\n    this.editTab(\\n      {\\n        sessionId: this.state.sessionId,\\n        terminals\\n      }\\n    )\\n  }\\n\\n  delSplit = () => {\\n    return this.props.delTab(\\n      this.props.tab.id\\n    )\\n  }\\n\\n  // handleChangeDirection = () => {\\n  //   const { splitDirection } = this.state\\n  //   this.setState({\\n  //     splitDirection: sp', present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"litDirection === terminalSplitDirectionMap.horizontal\\n  //       ? terminalSplitDirectionMap.vertical\\n  //       : terminalSplitDirectionMap.horizontal\\n  //   })\\n  // }\\n\\n  setActive = activeSplitId => {\\n    const up = {\\n      activeSplitId\\n    }\\n    this.setState(up)\\n  }\\n\\n  computePosition = () => {\\n    const {\\n      splitTerminalSftp\\n    } = this.props\\n    const allowedHeight = this.computeHeight()\\n    const allowedWidth = this.getWidth()\\n    const handleSize = 4\\n    if (splitTerminalSftp === terminalSplitDirectionMap.vertical) {\\n      const h = (allowedHeight - handleSize) / 2\\n      return {\\n        termPos: {\\n          left: 0,\\n          top: 0,\\n          width: allowedWidth + 'px',\\n          height: h + 'px'\\n        },\\n        sftpPos: {\\n          left: 0,\\n          top: h + handleSize + 'px',\\n          width: allowedWidth + 'px',\\n          height: h + 'px'\\n        },\\n        handlePos: {\\n          style: {\\n            left: 0,\\n            top: h + 'px',\\n            width: allowedWidth + 'px',\\n           \", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\" height: handleSize + 'px'\\n          }\\n        }\\n      }\\n    } else {\\n      const w = (allowedWidth - handleSize) / 2\\n      return {\\n        termPos: {\\n          left: 0,\\n          top: 0,\\n          width: w + 'px',\\n          height: allowedHeight + 'px'\\n        },\\n        sftpPos: {\\n          left: w + handleSize + 'px',\\n          top: 0,\\n          width: w + 'px',\\n          height: allowedHeight + 'px'\\n        },\\n        handlePos: {\\n          style: {\\n            left: w + 'px',\\n            top: 0,\\n            width: handleSize + 'px',\\n            height: allowedHeight + 'px'\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  getWidth = () => {\\n    const {\\n      infoPanelPinned,\\n      showInfo\\n    } = this.state\\n    const { rightSidebarWidth, width, leftSidebarWidth, pinned, openedSideBar } = this.props\\n    const rt = infoPanelPinned && showInfo ? rightSidebarWidth : 0\\n    const lt = pinned && openedSideBar ? leftSidebarWidth : 0\\n    return width - rt - lt - 42\\n  }\\n\\n  getWidthSftp = () => {\\n    const { width, left\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"SidebarWidth, pinned, openedSideBar } = this.props\\n    const lt = pinned && openedSideBar ? leftSidebarWidth : 0\\n    return width - lt - 42\\n  }\\n\\n  renderTerminal = (termPos) => {\\n    const {\\n      activeSplitId,\\n      id,\\n      sessionOptions,\\n      sessionId,\\n      sftpPathFollowSsh\\n    } = this.state\\n    const {\\n      pane, type\\n    } = this.props.tab\\n    if (type === terminalRdpType) {\\n      const rdpProps = {\\n        tab: this.props.tab,\\n        sessionId,\\n        ...pick(this.props, [\\n          'resolutions',\\n          'height',\\n          'width',\\n          'tabsHeight',\\n          'leftSidebarWidth',\\n          'pinned',\\n          'openedSideBar',\\n          'delTab',\\n          'config',\\n          'editTab'\\n        ]),\\n        ...pick(\\n          this,\\n          [\\n            'setSessionState'\\n          ])\\n      }\\n      return (\\n        <RdpSession\\n          {...rdpProps}\\n        />\\n      )\\n    }\\n    const { splitTerminalSftp } = this.props\\n    const cls = pane === paneMap.terminal && !splitTerminalSftp\\n   \", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"   ? 'terms-box'\\n      : 'terms-box hide'\\n    const height = this.computeHeight()\\n    const { tab } = this.props\\n    const width = this.getWidth()\\n    const themeConfig = copy(window.store.getThemeConfig())\\n    const logName = safeName(`${tab.title ? tab.title + '_' : ''}${tab.host ? tab.host + '_' : ''}${id}`)\\n    const pops = {\\n      ...this.props,\\n      activeSplitId,\\n      sftpPathFollowSsh,\\n      themeConfig,\\n      pane,\\n      ...pick(\\n        this,\\n        [\\n          'setActive',\\n          // 'handleSplit',\\n          'delSplit',\\n          'setSessionState',\\n          'handleShowInfo',\\n          'onChangePane',\\n          'hideInfoPanel',\\n          'setCwd',\\n          'onDelKeyPressed'\\n        ])\\n    }\\n    return (\\n      <div\\n        className={cls}\\n        style={termPos}\\n      >\\n        <Term\\n          logName={logName}\\n          sessionId={sessionId}\\n          sessionOptions={sessionOptions}\\n          {...pops}\\n        />\\n      </div>\\n    )\\n  }\\n\\n  renderSftp = (sftpPos) => {\\n    const {\\n      sessionO\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"ptions,\\n      sessionId,\\n      pid,\\n      enableSftp,\\n      sftpPathFollowSsh,\\n      cwd\\n    } = this.state\\n    const { splitTerminalSftp } = this.props\\n    const { pane, type } = this.props.tab\\n    if (type === terminalRdpType) {\\n      return null\\n    }\\n    const height = this.computeHeight()\\n    const cls = pane === paneMap.terminal && !splitTerminalSftp\\n      ? 'hide'\\n      : ''\\n    const exts = {\\n      sftpPathFollowSsh,\\n      cwd,\\n      pid,\\n      enableSftp,\\n      sessionOptions,\\n      sessionId,\\n      pane,\\n      ...this.props\\n    }\\n    return (\\n      <div\\n        className={cls}\\n        style={sftpPos}\\n      >\\n        <Sftp\\n          {...exts}\\n        />\\n      </div>\\n    )\\n  }\\n\\n  handleFullscreen = () => {\\n    window.store.toggleTermFullscreen(true)\\n  }\\n\\n  handleOpenSearch = () => {\\n    postMessage({\\n      action: terminalActions.openTerminalSearch,\\n      activeSplitId: this.state.activeSplitId\\n    })\\n  }\\n\\n  renderSearchIcon = () => {\\n    const title = e('search')\\n    return (\\n      <Tooltip title={ti\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"tle} placement='bottomLeft'>\\n        <SearchOutlined\\n          className='mg1r icon-info font16 iblock pointer spliter'\\n          onClick={this.handleOpenSearch}\\n        />\\n      </Tooltip>\\n    )\\n  }\\n\\n  fullscreenIcon = () => {\\n    const title = e('fullscreen')\\n    return (\\n      <Tooltip title={title} placement='bottomLeft'>\\n        <FullscreenOutlined\\n          className='mg1r icon-info font16 iblock pointer spliter term-fullscreen-control1'\\n          onClick={this.handleFullscreen}\\n        />\\n      </Tooltip>\\n    )\\n  }\\n\\n  renderDelTip = (isSsh) => {\\n    if (!isSsh || this.props.hideDelKeyTip || !this.state.delKeyPressed) {\\n      return null\\n    }\\n    return (\\n      <div className='type-tab'>\\n        <span className='mg1r'>Try <b>Shift + Backspace</b>?</span>\\n        <CloseOutlined\\n          onClick={this.handleDismissDelKeyTip}\\n          className='pointer'\\n        />\\n      </div>\\n    )\\n  }\\n\\n  handleSplitVert = () => {\\n    window.store.splitTerminalSftp = 'vertical'\\n  }\\n\\n  handleSplitHori = () => {\\n    win\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"dow.store.splitTerminalSftp = 'horizontal'\\n  }\\n\\n  renderControl = () => {\\n    const { sftpPathFollowSsh } = this.state\\n    const { props } = this\\n    const { pane, enableSsh, type } = props.tab\\n    if (type === terminalRdpType) {\\n      return null\\n    }\\n    const termType = props.tab?.type\\n    const isSsh = props.tab.authType\\n    const isLocal = !isSsh && (termType === connectionMap.local || !termType)\\n    const cls1 = 'mg1r icon-split pointer iblock spliter'\\n    const cls2 = 'icon-direction pointer iblock spliter'\\n    const types = [\\n      paneMap.terminal,\\n      paneMap.fileManager\\n    ]\\n    const controls = [\\n      isSsh ? paneMap.ssh : paneMap.terminal\\n    ]\\n    if (isSsh || isLocal) {\\n      controls.push(isSsh ? paneMap.sftp : paneMap.fileManager)\\n    }\\n    const checkTxt = e('sftpPathFollowSsh') + ' [Beta]'\\n    const checkProps = {\\n      onClick: this.toggleCheckSftpPathFollowSsh,\\n      className: classnames(\\n        'sftp-follow-ssh-icon',\\n        {\\n          active: sftpPathFollowSsh\\n        }\\n      )\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"\\n    }\\n    const simpleMapper = {\\n      [paneMap.terminal]: 'T',\\n      [paneMap.fileManager]: 'F',\\n      [paneMap.ssh]: 'T'\\n    }\\n    const shouldHaveSpliter = (isSsh && enableSsh) || isLocal\\n    return (\\n      <div\\n        className='terminal-control fix'\\n      >\\n        <div className='term-sftp-tabs fleft'>\\n          {\\n            controls.map((type, i) => {\\n              const cls = classnames(\\n                'type-tab',\\n                type,\\n                {\\n                  active: types[i] === pane\\n                }\\n              )\\n              return (\\n                <span\\n                  className={cls}\\n                  key={type + '_' + i}\\n                  onClick={() => this.onChangePane(types[i])}\\n                >\\n                  <span className='type-tab-txt'>\\n                    <span className='w500'>{e(type)}</span>\\n                    <span className='l500'>{simpleMapper[type]}</span>\\n                    <span className='type-tab-line' />\\n                  </span>\\n                \", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"</span>\\n              )\\n            })\\n          }\\n        </div>\\n        {\\n          shouldHaveSpliter\\n            ? (\\n              <Tooltip title={checkTxt}>\\n                <span {...checkProps}>\\n                  <PaperClipOutlined />\\n                </span>\\n              </Tooltip>\\n              )\\n            : null\\n        }\\n        {\\n          this.renderDelTip(pane === paneMap.terminal)\\n        }\\n        {\\n          pane === paneMap.terminal\\n            ? (\\n              <div className='fright term-controls'>\\n                {this.fullscreenIcon()}\\n                {this.renderSearchIcon()}\\n                <BorderVerticleOutlined\\n                  className={cls1}\\n                  onClick={this.handleSplitVert}\\n                />\\n                <BorderHorizontalOutlined\\n                  className={cls2}\\n                  onClick={this.handleSplitHori}\\n                />\\n              </div>\\n              )\\n            : null\\n        }\\n      </div>\\n    )\\n  }\\n\\n  renderContent = () => {\\n    const { sp\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16036), replacement_text=\"litTerminalSftp } = this.props\\n    if (!splitTerminalSftp) {\\n      return (\\n        <div>\\n          {this.renderTerminal()}\\n          {this.renderSftp()}\\n        </div>\\n      )\\n    }\\n    const cls = classNames(\\n      'session-split-wrap',\\n      splitTerminalSftp\\n    )\\n    const {\\n      termPos,\\n      handlePos,\\n      sftpPos\\n    } = this.computePosition()\\n    return (\\n      <div className={cls}>\\n        <div className='session-split-term'>\\n          {this.renderTerminal(termPos)}\\n        </div>\\n        <div className='session-split-handle' {...handlePos} />\\n        <div className='session-split-sftp'>\\n          {this.renderSftp(sftpPos)}\\n        </div>\\n      </div>\\n    )\\n  }\\n\\n  render () {\\n    const {\\n      splitDirection,\\n      infoPanelProps,\\n      showInfo,\\n      infoPanelPinned\\n    } = this.state\\n    const { pane } = this.props.tab\\n    const infoProps = {\\n      infoPanelPinned,\\n      ...pick(this.props.config, ['host', 'port', 'saveTerminalLogToFile', 'terminalInfos']),\\n      ...infoPanelProps,\\n      appP\", present_in_blob=False), ReplacementText(blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(16036, 16126), replacement_text=\"ath: this.props.appPath,\\n      rightSidebarWidth: this.props.rightSidebarWidth,\\n      showInfo,\\n      tabsHeight: this.props.tabsHeight,\\n      topMenuHeight: this.props.topMenuHeight,\\n      toggleInfoPinned: this.toggleInfoPinned,\\n      hideInfoPanel: this.hideInfoPanel\\n    }\\n    const cls = classnames(\\n      'term-sftp-box',\\n      pane,\\n      splitDirection,\\n      {\\n        'is-transporting': this.props.tab.isTransporting\\n      },\\n      {\\n        'disable-ssh': this.props.tab.enableSsh === false\\n      }\\n    )\\n    const allProps = {\\n      className: cls,\\n      id: `is-${this.props.tab.id}`,\\n      style: {\\n        width: this\\n      }\\n    }\\n    return (\\n      <div\\n        {...allProps}\\n      >\\n        {this.renderControl()}\\n        {this.renderContent()}\\n        <TerminalInfoContent\\n          {...infoProps}\\n        />\\n      </div>\\n    )\\n  }\\n}\\n\", present_in_blob=False), ReplacementText(blob_name='cc4c0b4fdc27a235dc2c739f62362b16582596ebff48730e6b6acbd55766bd0b', path='src/client/components/sftp/sftp-entry.jsx', crange=IntRange(25704, 26728), replacement_text=\"/div>\\n        </Spin>\\n      </div>\\n    )\\n  }\\n\\n  renderSections () {\\n    if (!this.isActive()) {\\n      return null\\n    }\\n    const arr = [\\n      typeMap.local,\\n      typeMap.remote\\n    ]\\n    const {\\n      height, width\\n    } = this.props\\n    const shouldRenderRemote = this.shouldRenderRemote()\\n    if (!shouldRenderRemote) {\\n      return (\\n        this.renderSection(arr[0], {\\n          width,\\n          left: 0,\\n          top: 0,\\n          height\\n        }, width)\\n      )\\n    }\\n    return arr.map((t, i) => {\\n      const style = {\\n        width: width / 2,\\n        left: i * width / 2,\\n        top: 0,\\n        height\\n      }\\n      return this.renderSection(t, style, width / 2)\\n    })\\n  }\\n\\n  render () {\\n    const { height } = this.props\\n    const {\\n      id\\n    } = this.state\\n    const all = {\\n      className: 'sftp-wrap overhide relative',\\n      id: `id-${id}`,\\n      style: { height }\\n    }\\n    return (\\n      <div\\n        {...all}\\n      >\\n        {\\n          this.renderSections()\\n        }\\n      </div>\\n    )\\n  }\\n}\\n\", present_in_blob=True), ReplacementText(blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(18380, 21604), replacement_text=\"  // split = () => {\\n  //   this.props.handleSplit(null, this.props.id)\\n  // }\\n\\n  onContextAction = e => {\\n    const {\\n      action,\\n      id,\\n      args = [],\\n      func\\n    } = e.data || {}\\n    if (\\n      action !== commonActions.clickContextMenu ||\\n      id !== this.uid ||\\n      !this[func]\\n    ) {\\n      return false\\n    }\\n    window.removeEventListener('message', this.onContextAction)\\n    this[func](...args)\\n  }\\n\\n  onContextMenu = e => {\\n    e.preventDefault()\\n    if (this.state.loading) {\\n      return\\n    }\\n    if (this.props.config.pasteWhenContextMenu) {\\n      return this.onPaste()\\n    }\\n    const items = this.renderContext()\\n    this.uid = generate()\\n    window.store.openContextMenu({\\n      id: this.uid,\\n      items,\\n      pos: computePos(e)\\n    })\\n    window.addEventListener('message', this.onContextAction)\\n  }\\n\\n  onCopy = () => {\\n    const selected = this.term.getSelection()\\n    copy(selected)\\n    this.term.focus()\\n  }\\n\\n  // onSelectAll = () => {\\n  //   this.term.selectAll()\\n  // }\\n\\n  onClear = () =\", present_in_blob=True), ReplacementText(blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(21604, 21604), replacement_text=\"> {\\n    this.term.clear()\\n    this.term.focus()\\n    this.notifyOnData('')\\n  }\\n\\n  isRemote = () => {\\n    return this.props.tab?.host &&\\n    this.props.tab?.type !== terminalSshConfigType\\n  }\\n\\n  onPaste = async () => {\\n    let selected = await readClipboardAsync()\\n    if (isWin && this.isRemote()) {\\n      selected = selected.replace(/\\\\r\\\\n/g, '\\\\n')\\n    }\\n    this.term.paste(selected || '')\\n    this.term.focus()\\n  }\\n\\n  toggleSearch = () => {\\n    window.store.toggleTerminalSearch()\\n  }\\n\\n  onSearchResultsChange = ({ resultIndex, resultCount }) => {\\n    window.store.storeAssign({\\n      termSearchMatchCount: resultCount,\\n      termSearchMatchIndex: resultIndex\\n    })\\n  }\\n\\n  searchPrev = (searchInput, options) => {\\n    this.searchAddon.findPrevious(\\n      searchInput, options\\n    )\\n  }\\n\\n  searchNext = (searchInput, options) => {\\n    this.searchAddon.findNext(\\n      searchInput, options\\n    )\\n  }\\n\\n  renderContext = () => {\\n    const hasSlected = this.term.hasSelection()\\n    const copyed = true\\n    const copyShortcut = \", present_in_blob=True), ReplacementText(blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(21604, 21604), replacement_text=\"this.getShortcut('terminal_copy')\\n    const pasteShortcut = this.getShortcut('terminal_paste')\\n    const clearShortcut = this.getShortcut('terminal_clear')\\n    // const selectAllShortcut = this.getShortcut('terminal_selectAll')\\n    const searchShortcut = this.getShortcut('terminal_search')\\n    return [\\n      {\\n        func: 'onCopy',\\n        icon: 'CopyOutlined',\\n        text: m('copy'),\\n        disabled: !hasSlected,\\n        subText: copyShortcut\\n      },\\n      {\\n        func: 'onPaste',\\n        icon: 'SwitcherOutlined',\\n        text: m('paste'),\\n        disabled: !copyed,\\n        subText: pasteShortcut\\n      },\\n      {\\n        func: 'onClear',\\n        icon: 'ReloadOutlined',\\n        text: e('clear'),\\n        subText: clearShortcut\\n      },\\n      // {\\n      //   func: 'onSelectAll',\\n      //   icon: 'SelectOutlined',\\n      //   text: e('selectAll'),\\n      //   subText: selectAllShortcut\\n      // },\\n      {\\n        func: 'toggleSearch',\\n        icon: 'SearchOutlined',\\n        text: e('search'),\\n        subTex\", present_in_blob=True), ReplacementText(blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(21604, 22476), replacement_text='t: searchShortcut\\n      } // ,\\n      // {\\n      //   func: \\'split\\',\\n      //   icon: \\'BorderHorizontalOutlined\\',\\n      //   text: e(\\'split\\')\\n      // }\\n    ]\\n  }\\n\\n  notifyOnData = debounce(() => {\\n    postMessage({\\n      action: \\'terminal-receive-data\\',\\n      tabId: this.props.tab.id\\n    })\\n  }, 1000)\\n\\n  parse (rawText) {\\n    let result = \\'\\'\\n    const len = rawText.length\\n    for (let i = 0; i < len; i++) {\\n      if (rawText[i] === \\'\\\\b\\') {\\n        result = result.slice(0, -1)\\n      } else {\\n        result += rawText[i]\\n      }\\n    }\\n    return result\\n  }\\n\\n  // onKey = ({ key }) => {\\n  //   if (key === \\'\\\\r\\') {\\n  //     this.getCmd()\\n  //   }\\n  // }\\n\\n  getCmd = () => {\\n    const str = this.serializeAddon.serialize()\\n    const arr = strip(str).split(/ +/)\\n    const len = arr.length\\n    return arr[len - 1]\\n  }\\n\\n  getCwd = () => {\\n    if (\\n      this.props.sftpPathFollowSsh &&\\n      this.term.buffer.active.type !== \\'alternate\\'\\n    ) {\\n      const cmd = `\\\\recho \"${cwdId}$PWD\"\\\\r`\\n      this.term.cwdId = cwdId\\n      ', present_in_blob=True), ReplacementText(blob_name='1aa33ee1b2c1bcf959664d809f48ffd214b77cc6b427a1e7892581a716d718dd', path='src/client/components/session/sessions.jsx', crange=IntRange(8509, 9533), replacement_text=\"s}\\n            />\\n          </div>\\n        )\\n      }\\n      return (\\n        <div className={cls} key={id}>\\n          <Session\\n            {...sessProps}\\n          />\\n        </div>\\n      )\\n    })\\n  }\\n\\n  renderTabs = () => {\\n    const {\\n      store,\\n      config\\n    } = this.props\\n    const {\\n      tabs,\\n      currentTabId\\n    } = this.state\\n    const tabsProps = {\\n      currentTabId,\\n      config,\\n      ...pick(store, [\\n        'height',\\n        'width',\\n        'activeTerminalId',\\n        'isMaximized',\\n        'splitTerminalSftp'\\n      ]),\\n      tabs,\\n      ...pick(this, [\\n        'setTabs',\\n        'onChangeTabId',\\n        'onDuplicateTab',\\n        'reloadTab',\\n        'delTab',\\n        'addTab',\\n        'editTab'\\n      ])\\n    }\\n    return (\\n      <Tabs\\n        key='main-tabs'\\n        {...tabsProps}\\n      />\\n    )\\n  }\\n\\n  renderSessionsWrap = () => {\\n    const { leftSidebarWidth, openedSideBar } = this.props.store\\n    const w = leftSidebarWidth + 42\\n    const ptp = openedSideBar\\n      ? {\\n          classNam\", present_in_blob=True)])), response=CompletionResponse(text='.getWidth()', model='roguesl-v2-16b-seth6-16-1-p512', skipped_suffix='', suffix_replacement_text='', unknown_blob_names=[], retrieved_chunks=[RetrievedChunk(text=\"    } = this.state\\n    const { pane } = this.props.tab\\n    const infoProps = {\\n      infoPanelPinned,\\n      ...pick(this.props.config, ['host', 'port', 'saveTerminalLogToFile', 'terminalInfos']),\\n      ...infoPanelProps,\\n      appPath: this.props.appPath,\\n      rightSidebarWidth: this.props.rightSidebarWidth,\\n      showInfo,\\n      tabsHeight: this.props.tabsHeight,\\n      topMenuHeight: this.props.topMenuHeight,\\n      toggleInfoPinned: this.toggleInfoPinned,\\n      hideInfoPanel: this.hideInfoPanel\\n    }\\n    const cls = classnames(\\n      'term-sftp-box',\\n      pane,\\n      splitDirection,\\n      {\\n        'is-transporting': this.props.tab.isTransporting\\n      },\\n      {\\n        'disable-ssh': this.props.tab.enableSsh === false\\n      }\\n    )\\n    return (\\n      <div\\n        className={cls}\\n        id={`is-${this.props.tab.id}`}\\n      >\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(15132, 15974)), RetrievedChunk(text=\"\\n  getWidthSftp = () => {\\n    const { width, leftSidebarWidth, pinned, openedSideBar } = this.props\\n    const lt = pinned && openedSideBar ? leftSidebarWidth : 0\\n    return width - lt - 42\\n  }\\n\\n  renderTerminal = (termPos) => {\\n    const {\\n      activeSplitId,\\n      id,\\n      sessionOptions,\\n      sessionId,\\n      sftpPathFollowSsh\\n    } = this.state\\n    const {\\n      pane, type\\n    } = this.props.tab\\n    if (type === terminalRdpType) {\\n      const rdpProps = {\\n        tab: this.props.tab,\\n        sessionId,\\n        ...pick(this.props, [\\n          'resolutions',\\n          'height',\\n          'width',\\n          'tabsHeight',\\n          'leftSidebarWidth',\\n          'pinned',\\n          'openedSideBar',\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(7122, 7831)), RetrievedChunk(text=\"          'delTab',\\n          'config',\\n          'editTab'\\n        ]),\\n        ...pick(\\n          this,\\n          [\\n            'setSessionState'\\n          ])\\n      }\\n      return (\\n        <RdpSession\\n          {...rdpProps}\\n        />\\n      )\\n    }\\n    const { splitTerminalSftp } = this.props\\n    const cls = pane === paneMap.terminal && !splitTerminalSftp\\n      ? 'terms-box'\\n      : 'terms-box hide'\\n    const height = this.computeHeight()\\n    const { tab } = this.props\\n    const width = this.getWidth()\\n    const themeConfig = copy(window.store.getThemeConfig())\\n    const logName = safeName(`${tab.title ? tab.title + '_' : ''}${tab.host ? tab.host + '_' : ''}${id}`)\\n    const pops = {\\n      ...this.props,\\n      activeSplitId,\\n      sftpPathFollowSsh,\\n      themeConfig,\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(7831, 8613)), RetrievedChunk(text=\"\\nconst { prefix } = window\\nconst e = prefix('ssh')\\nconst m = prefix('menu')\\n\\nexport default class SessionWrapper extends Component {\\n  constructor (props) {\\n    super(props)\\n    const id = uid()\\n    const {\\n      terminals = [\\n        {\\n          id,\\n          position: 0\\n        }\\n      ]\\n    } = props.tab\\n    const activeSplitId = terminals[0].id\\n    this.state = {\\n      id: uid(),\\n      pid: null,\\n      enableSftp: false,\\n      cwd: '',\\n      sftpPathFollowSsh: !!props.config.sftpPathFollowSsh,\\n      // splitDirection: terminalSplitDirectionMap.horizontal,\\n      activeSplitId,\\n      infoPanelPinned: false,\\n      key: Math.random(),\\n      sessionOptions: null,\\n      sessionId: generate(),\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(1469, 2169)), RetrievedChunk(text=\"        height\\n      }\\n      return this.renderSection(t, style, width / 2)\\n    })\\n  }\\n\\n  render () {\\n    const { height } = this.props\\n    const {\\n      id\\n    } = this.state\\n    const all = {\\n      className: 'sftp-wrap overhide relative',\\n      id: `id-${id}`,\\n      style: { height }\\n    }\\n    return (\\n      <div\\n        {...all}\\n      >\\n        {\\n          this.renderSections()\\n        }\\n      </div>\\n    )\\n  }\\n}\\n\", origin='dense_retriever', blob_name='cc4c0b4fdc27a235dc2c739f62362b16582596ebff48730e6b6acbd55766bd0b', path='src/client/components/sftp/sftp-entry.jsx', crange=IntRange(26308, 26728)), RetrievedChunk(text=\"      store, config\\n    } = this.props\\n    const {\\n      currentTabId,\\n      tabs\\n    } = this.state\\n    if (!tabs.length) {\\n      return this.renderNoSession()\\n    }\\n    return tabs.map((tab) => {\\n      const { id, type } = tab\\n      const cls = classNames(\\n        `session-wrap session-${id}`,\\n        {\\n          'session-current': id === currentTabId\\n        }\\n      )\\n      const sessProps = {\\n        currentTabId,\\n        tab: toSimpleObj(tab),\\n        ...pick(store, [\\n          'resolutions',\\n          'hideDelKeyTip',\\n          'fileOperation',\\n          'file',\\n          'height',\\n          'width',\\n          'activeTerminalId',\\n          'pinnedQuickCommandBar',\\n          'tabsHeight',\\n\", origin='dense_retriever', blob_name='1aa33ee1b2c1bcf959664d809f48ffd214b77cc6b427a1e7892581a716d718dd', path='src/client/components/session/sessions.jsx', crange=IntRange(7246, 7949)), RetrievedChunk(text=\"  renderSftp = (sftpPos) => {\\n    const {\\n      sessionOptions,\\n      sessionId,\\n      pid,\\n      enableSftp,\\n      sftpPathFollowSsh,\\n      cwd\\n    } = this.state\\n    const { splitTerminalSftp } = this.props\\n    const { pane, type } = this.props.tab\\n    if (type === terminalRdpType) {\\n      return null\\n    }\\n    const height = this.computeHeight()\\n    const cls = pane === paneMap.terminal && !splitTerminalSftp\\n      ? 'hide'\\n      : ''\\n    const exts = {\\n      sftpPathFollowSsh,\\n      cwd,\\n      pid,\\n      enableSftp,\\n      sessionOptions,\\n      sessionId,\\n      pane,\\n      ...this.props\\n    }\\n    return (\\n      <div\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(9163, 9789)), RetrievedChunk(text=\"        <div className={cls} key={id}>\\n          <Session\\n            {...sessProps}\\n          />\\n        </div>\\n      )\\n    })\\n  }\\n\\n  renderTabs = () => {\\n    const {\\n      store,\\n      config\\n    } = this.props\\n    const {\\n      tabs,\\n      currentTabId\\n    } = this.state\\n    const tabsProps = {\\n      currentTabId,\\n      config,\\n      ...pick(store, [\\n        'height',\\n        'width',\\n        'activeTerminalId',\\n        'isMaximized',\\n        'splitTerminalSftp'\\n      ]),\\n      tabs,\\n      ...pick(this, [\\n\", origin='dense_retriever', blob_name='1aa33ee1b2c1bcf959664d809f48ffd214b77cc6b427a1e7892581a716d718dd', path='src/client/components/session/sessions.jsx', crange=IntRange(8577, 9091)), RetrievedChunk(text=\"  }\\n\\n  // getPwd = async () => {\\n  //   const { sessionId, config } = this.props\\n  //   const { pid } = this.state\\n  //   const prps = {\\n  //     host: config.host,\\n  //     port: config.port,\\n  //     pid,\\n  //     sessionId\\n  //   }\\n  //   const result = await runCmds(prps, ['pwd'])\\n  //     .catch(window.store.onError)\\n  //   return result ? result[0].trim() : ''\\n  // }\\n\\n  switchEncoding = encode => {\\n    this.encode = encode\\n    this.attachAddon.decoder = new TextDecoder(encode)\\n  }\\n\\n  render () {\\n    const { id, loading } = this.state\\n    const { height, width, left, top, position, id: pid, activeSplitId } = this.props\\n    const cls = classnames('term-wrap', {\\n      'not-first-term': !!position\\n    }, 'tw-' + pid, {\\n      'terminal-not-active': activeSplitId !== pid\\n    })\\n    const prps1 = {\\n\", origin='dense_retriever', blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(32541, 33350)), RetrievedChunk(text=\"        className: cls\\n      }\\n    })\\n  }\\n\\n  positionProps = [\\n    'width',\\n    'left'\\n  ]\\n\\n  saveOldStyle = () => {\\n    const { properties, splitHandles } = this.state\\n    const ids = [\\n      ...properties,\\n      ...splitHandles\\n    ]\\n    const { type, id } = this.props\\n    const parentWidth = document.querySelector(\\n      `#id-${id} .tw-${type} .sftp-table`\\n    ).clientWidth\\n    this.oldStyles = ids.reduce((prev, { id, name }) => {\\n      const sel = `.session-current .tw-${type} .sftp-file-table-header .shi-${name || id}`\\n      return {\\n        ...prev,\\n        [name || id]: {\\n          style: pick(\\n            document.querySelector(sel)?.style || {},\\n            this.positionProps\\n          ),\\n          parentWidth\\n\", origin='dense_retriever', blob_name='370d62473c00fb9b48ec59587660412d5e9add3ea8871e664ea3f1e40d4331e0', path='src/client/components/sftp/list-table-ui.jsx', crange=IntRange(7358, 8087)), RetrievedChunk(text=\"  render () {\\n    const {\\n      currentTabId\\n    } = this.props\\n    const { isLast } = this.props\\n    const { tab, terminalOnData } = this.state\\n    const {\\n      id,\\n      isEditting,\\n      status,\\n      isTransporting,\\n      sshTunnelResults\\n    } = tab\\n    const active = id === currentTabId\\n    const cls = classnames(\\n      `tab-${id}`,\\n      'tab',\\n      { active },\\n      {\\n        'tab-last': isLast\\n      },\\n      status,\\n      {\\n        'is-terminal-active': terminalOnData\\n      },\\n      {\\n        'is-transporting': isTransporting\\n      }\\n    )\\n    const title = createName(tab)\\n\", origin='dense_retriever', blob_name='a5c1b27f21091fbe9ea8f49c8ca0ccbb348f2e4242b88f4b98325c5f8c7b77ad', path='src/client/components/tabs/tab.jsx', crange=IntRange(9220, 9811)), RetrievedChunk(text=\"  }\\n\\n  computeProps = () => {\\n    const {\\n      height,\\n      width,\\n      tabsHeight,\\n      leftSidebarWidth,\\n      pinned,\\n      openedSideBar\\n    } = this.props\\n    return {\\n      width: width - (pinned && openedSideBar ? leftSidebarWidth : 0),\\n      height: height - tabsHeight\\n    }\\n  }\\n\\n  remoteInit = async (term = this.term) => {\\n    this.setState({\\n      loading: true\\n    })\\n    const { config } = this.props\\n    const {\\n      host,\\n      port,\\n      tokenElecterm,\\n      server = ''\\n    } = config\\n    const { sessionId, id } = this.props\\n    const tab = deepCopy(this.props.tab || {})\\n\", origin='dense_retriever', blob_name='7207ddd6bf22249f4fec1bdd6308a7e41a17e58f2171de18b13f2931aa95b5ca', path='src/client/components/rdp/rdp-session.jsx', crange=IntRange(1322, 1919)), RetrievedChunk(text=\"      className: cls,\\n      style: {\\n        height,\\n        width,\\n        left,\\n        top,\\n        zIndex: position / 10\\n      },\\n      onDrop: this.onDrop\\n    }\\n    // const fileProps = {\\n    //   type: 'file',\\n    //   multiple: true,\\n    //   id: `${id}-file-sel`,\\n    //   className: 'hide'\\n    // }\\n    const prps2 = {\\n      className: 'absolute term-wrap-1',\\n      style: {\\n        left: '10px',\\n        top: '10px',\\n        right: 0,\\n        bottom: 0\\n      }\\n    }\\n    const prps3 = {\\n      id,\\n      className: 'absolute term-wrap-2',\\n      style: {\\n        left: 0,\\n\", origin='dense_retriever', blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(33350, 33930)), RetrievedChunk(text=\"          height: allowedHeight + 'px'\\n        },\\n        sftpPos: {\\n          left: w + handleSize + 'px',\\n          top: 0,\\n          width: w + 'px',\\n          height: allowedHeight + 'px'\\n        },\\n        handlePos: {\\n          style: {\\n            left: w + 'px',\\n            top: 0,\\n            width: handleSize + 'px',\\n            height: allowedHeight + 'px'\\n          }\\n        }\\n      }\\n    }\\n  }\\n\\n  getWidth = () => {\\n    const {\\n      infoPanelPinned,\\n      showInfo\\n    } = this.state\\n    const { rightSidebarWidth, width, leftSidebarWidth, pinned, openedSideBar } = this.props\\n    const rt = infoPanelPinned && showInfo ? rightSidebarWidth : 0\\n    const lt = pinned && openedSideBar ? leftSidebarWidth : 0\\n    return width - rt - lt - 42\\n  }\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(6363, 7122)), RetrievedChunk(text=\"          'appPath',\\n          'topMenuHeight',\\n          'rightSidebarWidth',\\n          'leftSidebarWidth',\\n          'pinned',\\n          'openedSideBar'\\n        ]),\\n        config,\\n        ...pick(this, [\\n          'onChangeTabId',\\n          'onDuplicateTab',\\n          'reloadTab',\\n          'delTab',\\n          'addTab',\\n          'editTab'\\n        ])\\n      }\\n      if (type === terminalWebType) {\\n        const webProps = {\\n          tab\\n        }\\n        return (\\n          <div className={cls} key={id}>\\n            <WebSession\\n              {...webProps}\\n            />\\n          </div>\\n        )\\n      }\\n      return (\\n\", origin='dense_retriever', blob_name='1aa33ee1b2c1bcf959664d809f48ffd214b77cc6b427a1e7892581a716d718dd', path='src/client/components/session/sessions.jsx', crange=IntRange(7949, 8577)), RetrievedChunk(text='      {\\n        negotiationMandatory: false,\\n        // terminalWidth: initOptions.cols,\\n        // terminalHeight: initOptions.rows,\\n        timeout: initOptions.readyTimeout,\\n        sendTimeout: initOptions.readyTimeout,\\n        socketConnectOptions: shellOpts\\n      }\\n    )\\n    await connection.connect(params)\\n    this.port = connection.shell(shellOpts)\\n    this.channel = connection\\n    if (this.isTest) {\\n      this.kill()\\n      return true\\n    }\\n    global.sessions[this.initOptions.sessionId] = {\\n      id: this.initOptions.sessionId,\\n      sftps: {},\\n      terminals: {\\n        [this.pid]: this\\n      }\\n    }\\n  }\\n\\n  resize = (cols, rows) => {\\n    this.channel.opts.terminalWidth = cols\\n    this.channel.opts.terminalHeight = rows\\n  }\\n\\n', origin='dense_retriever', blob_name='bb4fb9d44bee5384b58a4bf1faba35d165f36e64ccea4249a32a1773b3d9af99', path='src/app/server/session-telnet.js', crange=IntRange(639, 1384)), RetrievedChunk(text=\"      return null\\n    }\\n\\n    const pops = {\\n      onClick: props.hideInfoPanel,\\n      className: 'pointer font20 hide-info-panel-wrap'\\n    }\\n    const pops2 = {\\n      onClick: this.togglePin,\\n      className: 'pointer font20 toggle-info-panel-wrap mg1l'\\n    }\\n    const pops1 = {\\n      className: classNames(\\n        'info-panel-wrap',\\n        {\\n          'info-panel-wrap-pin': props.infoPanelPinned\\n        }\\n      ),\\n      id: 'info-panel-wrap',\\n      draggable: false,\\n      style: {\\n        width: props.rightSidebarWidth,\\n        top: props.topMenuHeight + props.tabsHeight + termControlHeight\\n      }\\n    }\\n    return (\\n      <div\\n        {...pops1}\\n      >\\n        <div\\n\", origin='dense_retriever', blob_name='ebcc8fc668e07240938a6c14ede3896a0a69a1b120e7c17d6201bb8f8bf7891d', path='src/client/components/terminal-info/content.jsx', crange=IntRange(2088, 2766)), RetrievedChunk(text=\"      )\\n    }\\n    const cls = classNames(\\n      'session-split-wrap',\\n      splitTerminalSftp\\n    )\\n    const {\\n      termPos,\\n      handlePos,\\n      sftpPos\\n    } = this.computePosition()\\n    return (\\n      <div className={cls}>\\n        <div className='session-split-term'>\\n          {this.renderTerminal(termPos)}\\n        </div>\\n        <div className='session-split-handle' {...handlePos} />\\n        <div className='session-split-sftp'>\\n          {this.renderSftp(sftpPos)}\\n        </div>\\n      </div>\\n    )\\n  }\\n\\n  render () {\\n    const {\\n      splitDirection,\\n      infoPanelProps,\\n      showInfo,\\n      infoPanelPinned\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(14508, 15132)), RetrievedChunk(text=\"    if (config.useSystemTitleBar) {\\n      return this.renderContentInner()\\n    }\\n    return (\\n      <AppDrag>\\n        {this.renderContentInner()}\\n      </AppDrag>\\n    )\\n  }\\n\\n  renderContentInner () {\\n    const { tabs = [], width } = this.props\\n    const len = tabs.length\\n    const tabsWidthAll = tabMargin * len + 10 + this.tabsWidth()\\n    const overflow = this.isOverflow()\\n    const left = overflow\\n      ? '100%'\\n      : tabsWidthAll\\n    const w1 = isMacJs ? 30 : windowControlWidth\\n    const style = {\\n      width: width - w1 - 136\\n    }\\n    return (\\n      <div\\n        className='tabs-inner'\\n        style={style}\\n      >\\n        <div\\n          style={{\\n            left\\n\", origin='dense_retriever', blob_name='780e0aa490cbc75787b7f6b8ddb79f306e83226bb01b5f6fde0358cc66ac05df', path='src/client/components/tabs/index.jsx', crange=IntRange(5655, 6332)), RetrievedChunk(text='  delSplit = () => {\\n    return this.props.delTab(\\n      this.props.tab.id\\n    )\\n  }\\n\\n  // handleChangeDirection = () => {\\n  //   const { splitDirection } = this.state\\n  //   this.setState({\\n  //     splitDirection: splitDirection === terminalSplitDirectionMap.horizontal\\n  //       ? terminalSplitDirectionMap.vertical\\n  //       : terminalSplitDirectionMap.horizontal\\n  //   })\\n  // }\\n\\n  setActive = activeSplitId => {\\n    const up = {\\n      activeSplitId\\n    }\\n    this.setState(up)\\n  }\\n\\n  computePosition = () => {\\n    const {\\n      splitTerminalSftp\\n    } = this.props\\n    const allowedHeight = this.computeHeight()\\n    const allowedWidth = this.getWidth()\\n    const handleSize = 4\\n    if (splitTerminalSftp === terminalSplitDirectionMap.vertical) {\\n', origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(4905, 5660)), RetrievedChunk(text=\"        'setTabs',\\n        'onChangeTabId',\\n        'onDuplicateTab',\\n        'reloadTab',\\n        'delTab',\\n        'addTab',\\n        'editTab'\\n      ])\\n    }\\n    return (\\n      <Tabs\\n        key='main-tabs'\\n        {...tabsProps}\\n      />\\n    )\\n  }\\n\\n  renderSessionsWrap = () => {\\n    const { leftSidebarWidth, openedSideBar } = this.props.store\\n    const w = leftSidebarWidth + 42\\n    const ptp = openedSideBar\\n      ? {\\n          className: 'sessions',\\n          style: {\\n            marginLeft: `${w}px`\\n          }\\n        }\\n      : {\\n          className: 'sessions'\\n        }\\n\", origin='dense_retriever', blob_name='1aa33ee1b2c1bcf959664d809f48ffd214b77cc6b427a1e7892581a716d718dd', path='src/client/components/session/sessions.jsx', crange=IntRange(9091, 9674)), RetrievedChunk(text=\"      onKeyDown: this.handleCanvasEvent,\\n      onKeyUp: this.handleCanvasEvent,\\n      onWheel: this.handleCanvasEvent,\\n      onContextMenu: this.handleCanvasEvent,\\n      tabIndex: 0\\n    }\\n    return (\\n      <Spin spinning={loading}>\\n        <div\\n          {...rdpProps}\\n          className='rdp-session-wrap pd1'\\n        >\\n          {this.renderControl()}\\n          <canvas\\n            {...canvasProps}\\n            id={'canvas_' + this.props.tab.id}\\n          />\\n        </div>\\n      </Spin>\\n    )\\n  }\\n}\\n\", origin='dense_retriever', blob_name='7207ddd6bf22249f4fec1bdd6308a7e41a17e58f2171de18b13f2931aa95b5ca', path='src/client/components/rdp/rdp-session.jsx', crange=IntRange(10038, 10542)), RetrievedChunk(text=\"  handleSplitVert = () => {\\n    window.store.splitTerminalSftp = 'vertical'\\n  }\\n\\n  handleSplitHori = () => {\\n    window.store.splitTerminalSftp = 'horizontal'\\n  }\\n\\n  renderControl = () => {\\n    const { sftpPathFollowSsh } = this.state\\n    const { props } = this\\n    const { pane, enableSsh, type } = props.tab\\n    if (type === terminalRdpType) {\\n      return null\\n    }\\n    const termType = props.tab?.type\\n    const isSsh = props.tab.authType\\n    const isLocal = !isSsh && (termType === connectionMap.local || !termType)\\n    const cls1 = 'mg1r icon-split pointer iblock spliter'\\n    const cls2 = 'icon-direction pointer iblock spliter'\\n    const types = [\\n      paneMap.terminal,\\n      paneMap.fileManager\\n    ]\\n    const controls = [\\n      isSsh ? paneMap.ssh : paneMap.terminal\\n    ]\\n    if (isSsh || isLocal) {\\n      controls.push(isSsh ? paneMap.sftp : paneMap.fileManager)\\n    }\\n\", origin='dense_retriever', blob_name='e31c2a63ebdce1ff2ade4d0c0ab311b0469b2a11bdcdc375bf8afb7774d3de08', path='src/client/components/session/session.jsx', crange=IntRange(11151, 12036)), RetrievedChunk(text=\"    const {\\n      type,\\n      term: terminalType\\n    } = tab\\n    const opts = clone({\\n      term: terminalType || config.terminalType,\\n      sessionId,\\n      tabId: id,\\n      srcTabId: tab.id,\\n      termType: type,\\n      ...tab\\n    })\\n    let pid = await createTerm(opts)\\n      .catch(err => {\\n        const text = err.message\\n        handleErr({ message: text })\\n      })\\n    pid = pid || ''\\n    this.setState({\\n      loading: false\\n    })\\n    if (!pid) {\\n      this.setStatus(statusMap.error)\\n      return\\n    }\\n    this.setStatus(statusMap.success)\\n    this.pid = pid\\n    const hs = server\\n      ? server.replace(/https?:\\\\/\\\\//, '')\\n      : `${host}:${port}`\\n\", origin='dense_retriever', blob_name='7207ddd6bf22249f4fec1bdd6308a7e41a17e58f2171de18b13f2931aa95b5ca', path='src/client/components/rdp/rdp-session.jsx', crange=IntRange(1919, 2580)), RetrievedChunk(text=\"\\n  componentDidMount () {\\n    this.initTerminal()\\n    this.initEvt()\\n    if (this.props.tab.enableSsh === false) {\\n      ;(\\n        document.querySelector('.session-current .term-sftp-tabs .type-tab.sftp') ||\\n        document.querySelector('.session-current .term-sftp-tabs .type-tab.fileManager')\\n      ).click()\\n    }\\n  }\\n\\n  componentDidUpdate (prevProps) {\\n    const shouldChange = (\\n      prevProps.currentTabId !== this.props.currentTabId &&\\n      this.props.tab.id === this.props.currentTabId &&\\n      this.props.pane === paneMap.terminal\\n    ) || (\\n      this.props.pane !== prevProps.pane &&\\n      this.props.pane === paneMap.terminal\\n    )\\n    const names = [\\n      'width',\\n      'height',\\n      'left',\\n      'top'\\n    ]\\n    if (\\n      !isEqual(\\n        pick(this.props, names),\\n\", origin='dense_retriever', blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(2429, 3219)), RetrievedChunk(text=\"      )\\n    })\\n  }\\n\\n  batchInput = (cmd) => {\\n    this.attachAddon._sendData(cmd + '\\\\r')\\n  }\\n\\n  onResizeTerminal = size => {\\n    const { cols, rows } = size\\n    resizeTerm(this.pid, this.props.sessionId, cols, rows)\\n  }\\n\\n  handleCancel = () => {\\n    const { id } = this.props.tab\\n    this.props.delTab(id)\\n  }\\n\\n  handleShowInfo = () => {\\n    const { id, sessionId, logName } = this.props\\n    const { pid } = this.state\\n    const infoProps = {\\n      logName,\\n      id,\\n      pid,\\n      sessionId,\\n      isRemote: this.isRemote(),\\n      isActive: this.isActiveTerminal()\\n    }\\n    this.props.handleShowInfo(infoProps)\\n\", origin='dense_retriever', blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(31925, 32541)), RetrievedChunk(text=\"    } = this.props.store\\n    const showBatchOp = showModal === modals.batchOps\\n    const pops = {\\n      open: showBatchOp,\\n      onClose: this.handleCancel,\\n      className: 'setting-wrap',\\n      width: innerWidth - sidebarWidth,\\n      zIndex: 888,\\n      placement: 'left',\\n      styles: {\\n        header: {\\n          display: 'none'\\n        }\\n      }\\n    }\\n    return (\\n      <Drawer\\n        {...pops}\\n      >\\n        <div id='batch-op-wrap'>\\n          {this.renderClose()}\\n          <div className='setting-wrap-content'>\\n            <div className='pd3 setting-wrap-inner'>\\n              {showBatchOp ? this.renderContent() : null}\\n            </div>\\n          </div>\\n        </div>\\n      </Drawer>\\n    )\\n  }\\n\", origin='dense_retriever', blob_name='08e2218a264d8d2a0339f727b75ada57a5708f9e5ea37481ce09e70c71655c91', path='src/client/components/batch-op/batch-op.jsx', crange=IntRange(14311, 15023)), RetrievedChunk(text=\"/**\\n * terminal/sftp/serial class\\n */\\nconst _ = require('lodash')\\nconst log = require('../common/log')\\nconst rdp = require('node-rdpjs-2')\\nconst { TerminalBase } = require('./session-base')\\nconst { isDev } = require('../common/runtime-constants')\\n\\nclass TerminalRdp extends TerminalBase {\\n  init = async () => {\\n    global.sessions[this.initOptions.sessionId] = {\\n      id: this.initOptions.sessionId,\\n      terminals: {\\n        [this.pid]: this\\n      }\\n    }\\n    return Promise.resolve(this)\\n  }\\n\\n  start = async (width, height) => {\\n    if (this.isRunning) {\\n      return\\n    }\\n    this.isRunning = true\\n    if (this.channel) {\\n      this.channel.close()\\n      delete this.channel\\n    }\\n    const {\\n\", origin='dense_retriever', blob_name='92a5fcb58305c1143be391c15ac6a69ac49ac7caa3ce71b6e11da170ec14745e', path='src/app/server/session-rdp.js', crange=IntRange(0, 701)), RetrievedChunk(text='  constructor (props) {\\n    const id = `rdp-reso-${props.tab.host}`\\n    const resObj = ls.getItemJSON(id, resolutions[0])\\n    super(props)\\n    this.state = {\\n      loading: false,\\n      bitmapProps: {},\\n      aspectRatio: 4 / 3,\\n      ...resObj\\n    }\\n  }\\n\\n  componentDidMount () {\\n    this.remoteInit()\\n  }\\n\\n  componentWillUnmount () {\\n    this.socket && this.socket.close()\\n    delete this.socket\\n  }\\n\\n  runInitScript = () => {\\n\\n  }\\n\\n  setStatus = status => {\\n    const id = this.props.tab?.id\\n    this.props.editTab(id, {\\n      status\\n    })\\n', origin='dense_retriever', blob_name='7207ddd6bf22249f4fec1bdd6308a7e41a17e58f2171de18b13f2931aa95b5ca', path='src/client/components/rdp/rdp-session.jsx', crange=IntRange(778, 1322)), RetrievedChunk(text=\"    document.body.removeEventListener('mouseup', this.handleMouseup)\\n    document.body.removeEventListener('mousemove', this.handleMousemove)\\n  }\\n\\n  handleMousemove = (e) => {\\n    const {\\n      clientX\\n    } = e\\n    const el = document.getElementById('info-panel-wrap')\\n    let nw = this.clientX - clientX + this.props.rightSidebarWidth\\n    if (nw < 400) {\\n      nw = 400\\n    } else if (nw > 1000) {\\n      nw = 1000\\n    }\\n    el.style.width = nw + 'px'\\n  }\\n\\n  killProcess = async (id) => {\\n    const {\\n      pid,\\n      sessionId\\n    } = this.props\\n    const cmd = `kill ${id}`\\n    runCmd(pid, sessionId, cmd)\\n  }\\n\\n  render () {\\n    const { props, state } = this\\n    if (!props.showInfo) {\\n\", origin='dense_retriever', blob_name='ebcc8fc668e07240938a6c14ede3896a0a69a1b120e7c17d6201bb8f8bf7891d', path='src/client/components/terminal-info/content.jsx', crange=IntRange(1399, 2088)), RetrievedChunk(text=\"import { getLocalFileInfo } from '../sftp/file-read.js'\\nimport { SerializeAddon } from 'xterm-addon-serialize'\\nimport strip from '@electerm/strip-ansi'\\nimport { formatBytes } from '../../common/byte-format.js'\\nimport * as fs from './fs.js'\\n\\nconst { prefix } = window\\nconst e = prefix('ssh')\\nconst m = prefix('menu')\\n\\nconst computePos = (e) => {\\n  return {\\n    left: e.clientX,\\n    top: e.clientY\\n  }\\n}\\n\\nclass Term extends Component {\\n  constructor (props) {\\n    super(props)\\n    this.state = {\\n      pid: '',\\n      id: props.id || 'id' + generate(),\\n      loading: false,\\n      saveTerminalLogToFile: !!this.props.config.saveTerminalLogToFile,\\n      addTimeStampToTermLog: !!this.props.config.addTimeStampToTermLog,\\n      passType: 'password',\\n      lines: []\\n    }\\n  }\\n\", origin='dense_retriever', blob_name='f5348f5bdb5809ec9ecb4fa1ad01fe873d982c7beeec430988a57cec4aeb6ff0', path='src/client/components/terminal/index.jsx', crange=IntRange(1659, 2429)), RetrievedChunk(text=\"          })\\n        sshTunnelResults.push(result)\\n      }\\n    }\\n    this.ws.s({\\n      update: {\\n        sshTunnelResults\\n      },\\n      action: 'ssh-tunnel-result',\\n      tabId: this.initOptions.srcTabId\\n    })\\n    return new Promise((resolve, reject) => {\\n      this.conn.shell(\\n        shellWindow,\\n        shellOpts,\\n        (err, channel) => {\\n          if (err) {\\n            return reject(err)\\n          }\\n          this.channel = channel\\n          global.sessions[initOptions.sessionId] = {\\n            conn: this.conn,\\n            id: initOptions.sessionId,\\n            shellOpts,\\n            sftps: {},\\n            terminals: {\\n              [this.pid]: this\\n            }\\n          }\\n          resolve(this)\\n\", origin='dense_retriever', blob_name='c9de68f7a4b5d872fd98ea8f1e3d724f30e2e233f11c03cfba7048d707b041dc', path='src/app/server/session-ssh.js', crange=IntRange(9599, 10318))], timestamp=datetime.datetime(2024, 5, 15, 11, 53, 34, 573573, tzinfo=datetime.timezone(datetime.timedelta(0), 'UTC')), tokens=['.', 'getWidth', '()'], token_log_probs=[-5.221366882324219e-05, -0.109619140625, -0.2349853515625], prompt_tokens=['<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'session', '.', 'jsx', '\\n', '<|far_prefix|>', 'ops', ' }', ' =', ' this', '\\n   ', ' const', ' {', ' pane', ',', ' enable', 'Ssh', ',', ' type', ' }', ' =', ' props', '.', 'tab', '\\n   ', ' if', ' (', 'type', ' ===', ' terminal', 'R', 'dp', 'Type', ')', ' {', '\\n     ', ' return', ' null', '\\n   ', ' }', '\\n   ', ' const', ' term', 'Type', ' =', ' props', '.', 'tab', '?.', 'type', '\\n   ', ' const', ' is', 'Ssh', ' =', ' props', '.', 'tab', '.', 'auth', 'Type', '\\n   ', ' const', ' is', 'Local', ' =', ' !', 'is', 'Ssh', ' &&', ' (', 'term', 'Type', ' ===', ' connection', 'Map', '.', 'local', ' ||', ' !', 'term', 'Type', ')', '\\n   ', ' const', ' cls', '1', ' =', \" '\", 'mg', '1', 'r', ' icon', '-', 'split', ' pointer', ' i', 'block', ' spl', 'iter', \"'\", '\\n   ', ' const', ' cls', '2', ' =', \" '\", 'icon', '-', 'direction', ' pointer', ' i', 'block', ' spl', 'iter', \"'\", '\\n   ', ' const', ' types', ' =', ' [', '\\n     ', ' pane', 'Map', '.', 'terminal', ',', '\\n     ', ' pane', 'Map', '.', 'file', 'Manager', '\\n   ', ' ]', '\\n   ', ' const', ' controls', ' =', ' [', '\\n     ', ' is', 'Ssh', ' ?', ' pane', 'Map', '.', 'ssh', ' :', ' pane', 'Map', '.', 'terminal', '\\n   ', ' ]', '\\n   ', ' if', ' (', 'is', 'Ssh', ' ||', ' is', 'Local', ')', ' {', '\\n     ', ' controls', '.', 'push', '(', 'is', 'Ssh', ' ?', ' pane', 'Map', '.', 'sf', 'tp', ' :', ' pane', 'Map', '.', 'file', 'Manager', ')', '\\n   ', ' }', '\\n   ', ' const', ' check', 'Txt', ' =', ' e', \"('\", 'sf', 'tp', 'Path', 'Follow', 'Ssh', \"')\", ' +', \" '\", ' [', 'Beta', \"]'\", '\\n   ', ' const', ' check', 'Props', ' =', ' {', '\\n     ', ' onClick', ':', ' this', '.', 'toggle', 'Check', 'S', 'ftp', 'Path', 'Follow', 'Ssh', ',', '\\n     ', ' className', ':', ' class', 'names', '(', '\\n       ', \" '\", 'sf', 'tp', '-', 'follow', '-', 'ssh', '-', 'icon', \"',\", '\\n       ', ' {', '\\n         ', ' active', ':', ' s', 'ftp', 'Path', 'Follow', 'Ssh', '\\n       ', ' }', '\\n     ', ' )', '\\n   ', ' }', '\\n   ', ' const', ' simple', 'Mapper', ' =', ' {', '\\n     ', ' [', 'pane', 'Map', '.', 'terminal', ']:', \" '\", 'T', \"',\", '\\n     ', ' [', 'pane', 'Map', '.', 'file', 'Manager', ']:', \" '\", 'F', \"',\", '\\n     ', ' [', 'pane', 'Map', '.', 'ssh', ']:', \" '\", 'T', \"'\", '\\n   ', ' }', '\\n   ', ' const', ' should', 'Have', 'Spl', 'iter', ' =', ' (', 'is', 'Ssh', ' &&', ' enable', 'Ssh', ')', ' ||', ' is', 'Local', '\\n   ', ' return', ' (', '\\n     ', ' <', 'div', '\\n       ', ' className', \"='\", 'terminal', '-', 'control', ' fix', \"'\", '\\n     ', ' >', '\\n       ', ' <', 'div', ' className', \"='\", 'term', '-', 'sf', 'tp', '-', 'tabs', ' f', 'left', \"'>\", '\\n         ', ' {', '\\n           ', ' controls', '.', 'map', '((', 'type', ',', ' i', ')', ' =>', ' {', '\\n             ', ' const', ' cls', ' =', ' class', 'names', '(', '\\n               ', \" '\", 'type', '-', 'tab', \"',\", '\\n               ', ' type', ',', '\\n               ', ' {', '\\n                 ', ' active', ':', ' types', '[', 'i', ']', ' ===', ' pane', '\\n               ', ' }', '\\n             ', ' )', '\\n             ', ' return', ' (', '\\n               ', ' <', 'span', '\\n                 ', ' className', '={', 'cls', '}', '\\n                 ', ' key', '={', 'type', ' +', \" '_'\", ' +', ' i', '}', '\\n                 ', ' onClick', '={()', ' =>', ' this', '.', 'onChange', 'Pane', '(', 'types', '[', 'i', '])', '}', '\\n               ', ' >', '\\n                 ', ' <', 'span', ' className', \"='\", 'type', '-', 'tab', '-', 'txt', \"'>\", '\\n                   ', ' <', 'span', ' className', \"='\", 'w', '5', '0', '0', \"'>\", '{', 'e', '(', 'type', ')}</', 'span', '>', '\\n                   ', ' <', 'span', ' className', \"='\", 'l', '5', '0', '0', \"'>\", '{', 'simple', 'Mapper', '[', 'type', ']', '}</', 'span', '>', '\\n                   ', ' <', 'span', ' className', \"='\", 'type', '-', 'tab', '-', 'line', \"'\", ' />', '\\n                 ', ' </', 'span', '>', '\\n               ', ' </', 'span', '>', '\\n             ', ' )', '\\n           ', ' })', '\\n         ', ' }', '\\n       ', ' </', 'div', '>', '\\n       ', ' {', '\\n         ', ' should', 'Have', 'Spl', 'iter', '\\n           ', ' ?', ' (', '\\n             ', ' <', 'Tooltip', ' title', '={', 'check', 'Txt', '}>', '\\n               ', ' <', 'span', ' {...', 'check', 'Props', '}>', '\\n                 ', ' <', 'Paper', 'Clip', 'Outlined', ' />', '\\n               ', ' </', 'span', '>', '\\n             ', ' </', 'Tooltip', '>', '\\n             ', ' )', '\\n           ', ' :', ' null', '\\n       ', ' }', '\\n       ', ' {', '\\n         ', ' this', '.', 'render', 'Del', 'Tip', '(', 'pane', ' ===', ' pane', 'Map', '.', 'terminal', ')', '\\n       ', ' }', '\\n       ', ' {', '\\n         ', ' pane', ' ===', ' pane', 'Map', '.', 'terminal', '\\n           ', ' ?', ' (', '\\n             ', ' <', 'div', ' className', \"='\", 'f', 'right', ' term', '-', 'controls', \"'>\", '\\n               ', ' {', 'this', '.', 'fullscreen', 'Icon', '()}', '\\n               ', ' {', 'this', '.', 'render', 'Search', 'Icon', '()}', '\\n               ', ' <', 'Border', 'Vert', 'icle', 'Outlined', '\\n                 ', ' className', '={', 'cls', '1', '}', '\\n                 ', ' onClick', '={', 'this', '.', 'handle', 'Split', 'Vert', '}', '\\n               ', ' />', '\\n               ', ' <', 'Border', 'Horizontal', 'Outlined', '\\n                 ', ' className', '={', 'cls', '2', '}', '\\n                 ', ' onClick', '={', 'this', '.', 'handle', 'Split', 'H', 'ori', '}', '\\n               ', ' />', '\\n             ', ' </', 'div', '>', '\\n             ', ' )', '\\n           ', ' :', ' null', '\\n       ', ' }', '\\n     ', ' </', 'div', '>', '\\n   ', ' )', '\\n ', ' }', '\\n\\n ', ' render', 'Content', ' =', ' ()', ' =>', ' {', '\\n   ', ' const', ' {', ' split', 'Terminal', 'S', 'ftp', ' }', ' =', ' this', '.', 'props', '\\n   ', ' if', ' (!', 'split', 'Terminal', 'S', 'ftp', ')', ' {', '\\n     ', ' return', ' (', '\\n       ', ' <', 'div', '>', '\\n         ', ' {', 'this', '.', 'render', 'Terminal', '()}', '\\n         ', ' {', 'this', '.', 'render', 'S', 'ftp', '()}', '\\n       ', ' </', 'div', '>', '\\n     ', ' )', '\\n   ', ' }', '\\n   ', ' const', ' cls', ' =', ' classNames', '(', '\\n     ', \" '\", 'session', '-', 'split', '-', 'wrap', \"',\", '\\n     ', ' split', 'Terminal', 'S', 'ftp', '\\n   ', ' )', '\\n   ', ' const', ' {', '\\n     ', ' term', 'Pos', ',', '\\n     ', ' handle', 'Pos', ',', '\\n     ', ' s', 'ftp', 'Pos', '\\n   ', ' }', ' =', ' this', '.', 'compute', 'Position', '()', '\\n   ', ' return', ' (', '\\n     ', ' <', 'div', ' className', '={', 'cls', '}>', '\\n       ', ' <', 'div', ' className', \"='\", 'session', '-', 'split', '-', 'term', \"'>\", '\\n         ', ' {', 'this', '.', 'render', 'Terminal', '(', 'term', 'Pos', ')}', '\\n       ', ' </', 'div', '>', '\\n       ', ' <', 'div', ' className', \"='\", 'session', '-', 'split', '-', 'handle', \"'\", ' {...', 'handle', 'Pos', '}', ' />', '\\n       ', ' <', 'div', ' className', \"='\", 'session', '-', 'split', '-', 'sf', 'tp', \"'>\", '\\n         ', ' {', 'this', '.', 'render', 'S', 'ftp', '(', 'sf', 'tp', 'Pos', ')}', '\\n       ', ' </', 'div', '>', '\\n     ', ' </', 'div', '>', '\\n   ', ' )', '\\n ', ' }', '\\n\\n ', ' render', ' ()', ' {', '\\n   ', ' const', ' {', '\\n     ', ' split', 'Direction', ',', '\\n     ', ' info', 'Panel', 'Props', ',', '\\n     ', ' show', 'Info', ',', '\\n     ', ' info', 'Panel', 'Pin', 'ned', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' {', ' pane', ' }', ' =', ' this', '.', 'props', '.', 'tab', '\\n   ', ' const', ' info', 'Props', ' =', ' {', '\\n     ', ' info', '<fim_suffix>', '\\n     ', ' }', '\\n   ', ' }', '\\n   ', ' return', ' (', '\\n     ', ' <', 'div', '\\n       ', ' {...', 'all', 'Props', '}', '\\n     ', ' >', '\\n       ', ' {', 'this', '.', 'render', 'Control', '()}', '\\n       ', ' {', 'this', '.', 'render', 'Content', '()}', '\\n       ', ' <', 'Terminal', 'Info', 'Content', '\\n         ', ' {...', 'info', 'Props', '}', '\\n       ', ' />', '\\n     ', ' </', 'div', '>', '\\n   ', ' )', '\\n ', ' }', '\\n', '}', '\\n', '<|retrieval_section|>', '<|ret-start|>', '<filename>', 'src', '/', 'app', '/', 'server', '/', 'session', '-', 'tel', 'net', '.', 'js', '<|ret-body|>', '     ', ' {', '\\n       ', ' neg', 'otiation', 'Mandatory', ':', ' false', ',', '\\n       ', ' //', ' terminal', 'Width', ':', ' init', 'Options', '.', 'cols', ',', '\\n       ', ' //', ' terminal', 'Height', ':', ' init', 'Options', '.', 'rows', ',', '\\n       ', ' timeout', ':', ' init', 'Options', '.', 'ready', 'Timeout', ',', '\\n       ', ' send', 'Timeout', ':', ' init', 'Options', '.', 'ready', 'Timeout', ',', '\\n       ', ' socket', 'Connect', 'Options', ':', ' shell', 'Opts', '\\n     ', ' }', '\\n   ', ' )', '\\n   ', ' await', ' connection', '.', 'connect', '(', 'params', ')', '\\n   ', ' this', '.', 'port', ' =', ' connection', '.', 'shell', '(', 'shell', 'Opts', ')', '\\n   ', ' this', '.', 'channel', ' =', ' connection', '\\n   ', ' if', ' (', 'this', '.', 'is', 'Test', ')', ' {', '\\n     ', ' this', '.', 'kill', '()', '\\n     ', ' return', ' true', '\\n   ', ' }', '\\n   ', ' global', '.', 'sessions', '[', 'this', '.', 'init', 'Options', '.', 'sessionId', ']', ' =', ' {', '\\n     ', ' id', ':', ' this', '.', 'init', 'Options', '.', 'sessionId', ',', '\\n     ', ' s', 'ftp', 's', ':', ' {},', '\\n     ', ' terminal', 's', ':', ' {', '\\n       ', ' [', 'this', '.', 'pid', ']:', ' this', '\\n     ', ' }', '\\n   ', ' }', '\\n ', ' }', '\\n\\n ', ' resize', ' =', ' (', 'cols', ',', ' rows', ')', ' =>', ' {', '\\n   ', ' this', '.', 'channel', '.', 'opts', '.', 'terminal', 'Width', ' =', ' cols', '\\n   ', ' this', '.', 'channel', '.', 'opts', '.', 'terminal', 'Height', ' =', ' rows', '\\n ', ' }', '\\n\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'sessions', '.', 'jsx', '<|ret-body|>', '         ', \" '\", 'app', 'Path', \"',\", '\\n         ', \" '\", 'top', 'Menu', 'Height', \"',\", '\\n         ', \" '\", 'right', 'Sidebar', 'Width', \"',\", '\\n         ', \" '\", 'left', 'Sidebar', 'Width', \"',\", '\\n         ', \" '\", 'pinned', \"',\", '\\n         ', \" '\", 'opened', 'Side', 'Bar', \"'\", '\\n       ', ' ]),', '\\n       ', ' config', ',', '\\n       ', ' ...', 'pick', '(', 'this', ',', ' [', '\\n         ', \" '\", 'onChange', 'Tab', 'Id', \"',\", '\\n         ', \" '\", 'on', 'Duplicate', 'Tab', \"',\", '\\n         ', \" '\", 'reload', 'Tab', \"',\", '\\n         ', \" '\", 'del', 'Tab', \"',\", '\\n         ', \" '\", 'add', 'Tab', \"',\", '\\n         ', \" '\", 'edit', 'Tab', \"'\", '\\n       ', ' ])', '\\n     ', ' }', '\\n     ', ' if', ' (', 'type', ' ===', ' terminal', 'Web', 'Type', ')', ' {', '\\n       ', ' const', ' web', 'Props', ' =', ' {', '\\n         ', ' tab', '\\n       ', ' }', '\\n       ', ' return', ' (', '\\n         ', ' <', 'div', ' className', '={', 'cls', '}', ' key', '={', 'id', '}>', '\\n           ', ' <', 'Web', 'Session', '\\n             ', ' {...', 'web', 'Props', '}', '\\n           ', ' />', '\\n         ', ' </', 'div', '>', '\\n       ', ' )', '\\n     ', ' }', '\\n     ', ' return', ' (', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'session', '.', 'jsx', '<|ret-body|>', '         ', ' height', ':', ' allowed', 'Height', ' +', \" '\", 'px', \"'\", '\\n       ', ' },', '\\n       ', ' s', 'ftp', 'Pos', ':', ' {', '\\n         ', ' left', ':', ' w', ' +', ' handle', 'Size', ' +', \" '\", 'px', \"',\", '\\n         ', ' top', ':', ' ', '0', ',', '\\n         ', ' width', ':', ' w', ' +', \" '\", 'px', \"',\", '\\n         ', ' height', ':', ' allowed', 'Height', ' +', \" '\", 'px', \"'\", '\\n       ', ' },', '\\n       ', ' handle', 'Pos', ':', ' {', '\\n         ', ' style', ':', ' {', '\\n           ', ' left', ':', ' w', ' +', \" '\", 'px', \"',\", '\\n           ', ' top', ':', ' ', '0', ',', '\\n           ', ' width', ':', ' handle', 'Size', ' +', \" '\", 'px', \"',\", '\\n           ', ' height', ':', ' allowed', 'Height', ' +', \" '\", 'px', \"'\", '\\n         ', ' }', '\\n       ', ' }', '\\n     ', ' }', '\\n   ', ' }', '\\n ', ' }', '\\n\\n ', ' get', 'Width', ' =', ' ()', ' =>', ' {', '\\n   ', ' const', ' {', '\\n     ', ' info', 'Panel', 'Pin', 'ned', ',', '\\n     ', ' show', 'Info', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' {', ' right', 'Sidebar', 'Width', ',', ' width', ',', ' left', 'Sidebar', 'Width', ',', ' pinned', ',', ' opened', 'Side', 'Bar', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' rt', ' =', ' info', 'Panel', 'Pin', 'ned', ' &&', ' show', 'Info', ' ?', ' right', 'Sidebar', 'Width', ' :', ' ', '0', '\\n   ', ' const', ' lt', ' =', ' pinned', ' &&', ' opened', 'Side', 'Bar', ' ?', ' left', 'Sidebar', 'Width', ' :', ' ', '0', '\\n   ', ' return', ' width', ' -', ' rt', ' -', ' lt', ' -', ' ', '4', '2', '\\n ', ' }', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'terminal', '/', 'index', '.', 'jsx', '<|ret-body|>', '     ', ' className', ':', ' cls', ',', '\\n     ', ' style', ':', ' {', '\\n       ', ' height', ',', '\\n       ', ' width', ',', '\\n       ', ' left', ',', '\\n       ', ' top', ',', '\\n       ', ' z', 'Index', ':', ' position', ' /', ' ', '1', '0', '\\n     ', ' },', '\\n     ', ' on', 'Drop', ':', ' this', '.', 'on', 'Drop', '\\n   ', ' }', '\\n   ', ' //', ' const', ' file', 'Props', ' =', ' {', '\\n   ', ' //', '  ', ' type', ':', \" '\", 'file', \"',\", '\\n   ', ' //', '  ', ' multiple', ':', ' true', ',', '\\n   ', ' //', '  ', ' id', ':', ' `${', 'id', '}-', 'file', '-', 'sel', '`,', '\\n   ', ' //', '  ', ' className', ':', \" '\", 'hide', \"'\", '\\n   ', ' //', ' }', '\\n   ', ' const', ' pr', 'ps', '2', ' =', ' {', '\\n     ', ' className', ':', \" '\", 'absolute', ' term', '-', 'wrap', '-', '1', \"',\", '\\n     ', ' style', ':', ' {', '\\n       ', ' left', ':', \" '\", '1', '0', 'px', \"',\", '\\n       ', ' top', ':', \" '\", '1', '0', 'px', \"',\", '\\n       ', ' right', ':', ' ', '0', ',', '\\n       ', ' bottom', ':', ' ', '0', '\\n     ', ' }', '\\n   ', ' }', '\\n   ', ' const', ' pr', 'ps', '3', ' =', ' {', '\\n     ', ' id', ',', '\\n     ', ' className', ':', \" '\", 'absolute', ' term', '-', 'wrap', '-', '2', \"',\", '\\n     ', ' style', ':', ' {', '\\n       ', ' left', ':', ' ', '0', ',', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'rdp', '/', 'rdp', '-', 'session', '.', 'jsx', '<|ret-body|>', ' ', ' }', '\\n\\n ', ' compute', 'Props', ' =', ' ()', ' =>', ' {', '\\n   ', ' const', ' {', '\\n     ', ' height', ',', '\\n     ', ' width', ',', '\\n     ', ' tabs', 'Height', ',', '\\n     ', ' left', 'Sidebar', 'Width', ',', '\\n     ', ' pinned', ',', '\\n     ', ' opened', 'Side', 'Bar', '\\n   ', ' }', ' =', ' this', '.', 'props', '\\n   ', ' return', ' {', '\\n     ', ' width', ':', ' width', ' -', ' (', 'pinned', ' &&', ' opened', 'Side', 'Bar', ' ?', ' left', 'Sidebar', 'Width', ' :', ' ', '0', '),', '\\n     ', ' height', ':', ' height', ' -', ' tabs', 'Height', '\\n   ', ' }', '\\n ', ' }', '\\n\\n ', ' remote', 'Init', ' =', ' async', ' (', 'term', ' =', ' this', '.', 'term', ')', ' =>', ' {', '\\n   ', ' this', '.', 'setState', '({', '\\n     ', ' loading', ':', ' true', '\\n   ', ' })', '\\n   ', ' const', ' {', ' config', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', '\\n     ', ' host', ',', '\\n     ', ' port', ',', '\\n     ', ' token', 'Elect', 'erm', ',', '\\n     ', ' server', ' =', \" ''\", '\\n   ', ' }', ' =', ' config', '\\n   ', ' const', ' {', ' sessionId', ',', ' id', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' tab', ' =', ' deep', 'Copy', '(', 'this', '.', 'props', '.', 'tab', ' ||', ' {})', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'tabs', '/', 'tab', '.', 'jsx', '<|ret-body|>', ' ', ' render', ' ()', ' {', '\\n   ', ' const', ' {', '\\n     ', ' current', 'Tab', 'Id', '\\n   ', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', ' is', 'Last', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', ' tab', ',', ' terminal', 'On', 'Data', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' {', '\\n     ', ' id', ',', '\\n     ', ' is', 'Edit', 'ting', ',', '\\n     ', ' status', ',', '\\n     ', ' is', 'Transport', 'ing', ',', '\\n     ', ' ssh', 'Tunnel', 'Results', '\\n   ', ' }', ' =', ' tab', '\\n   ', ' const', ' active', ' =', ' id', ' ===', ' current', 'Tab', 'Id', '\\n   ', ' const', ' cls', ' =', ' class', 'names', '(', '\\n     ', ' `', 'tab', '-${', 'id', '}`,', '\\n     ', \" '\", 'tab', \"',\", '\\n     ', ' {', ' active', ' },', '\\n     ', ' {', '\\n       ', \" '\", 'tab', '-', 'last', \"':\", ' is', 'Last', '\\n     ', ' },', '\\n     ', ' status', ',', '\\n     ', ' {', '\\n       ', \" '\", 'is', '-', 'terminal', '-', 'active', \"':\", ' terminal', 'On', 'Data', '\\n     ', ' },', '\\n     ', ' {', '\\n       ', \" '\", 'is', '-', 'transport', 'ing', \"':\", ' is', 'Transport', 'ing', '\\n     ', ' }', '\\n   ', ' )', '\\n   ', ' const', ' title', ' =', ' create', 'Name', '(', 'tab', ')', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'sf', 'tp', '/', 'list', '-', 'table', '-', 'ui', '.', 'jsx', '<|ret-body|>', '       ', ' className', ':', ' cls', '\\n     ', ' }', '\\n   ', ' })', '\\n ', ' }', '\\n\\n ', ' position', 'Props', ' =', ' [', '\\n   ', \" '\", 'width', \"',\", '\\n   ', \" '\", 'left', \"'\", '\\n ', ' ]', '\\n\\n ', ' save', 'Old', 'Style', ' =', ' ()', ' =>', ' {', '\\n   ', ' const', ' {', ' properties', ',', ' split', 'Handles', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' ids', ' =', ' [', '\\n     ', ' ...', 'properties', ',', '\\n     ', ' ...', 'split', 'Handles', '\\n   ', ' ]', '\\n   ', ' const', ' {', ' type', ',', ' id', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' parent', 'Width', ' =', ' document', '.', 'querySelector', '(', '\\n     ', ' `#', 'id', '-${', 'id', '}', ' .', 'tw', '-${', 'type', '}', ' .', 'sf', 'tp', '-', 'table', '`', '\\n   ', ' ).', 'client', 'Width', '\\n   ', ' this', '.', 'old', 'Styles', ' =', ' ids', '.', 'reduce', '((', 'prev', ',', ' {', ' id', ',', ' name', ' })', ' =>', ' {', '\\n     ', ' const', ' sel', ' =', ' `.', 'session', '-', 'current', ' .', 'tw', '-${', 'type', '}', ' .', 'sf', 'tp', '-', 'file', '-', 'table', '-', 'header', ' .', 'shi', '-${', 'name', ' ||', ' id', '}`', '\\n     ', ' return', ' {', '\\n       ', ' ...', 'prev', ',', '\\n       ', ' [', 'name', ' ||', ' id', ']:', ' {', '\\n         ', ' style', ':', ' pick', '(', '\\n           ', ' document', '.', 'querySelector', '(', 'sel', ')?.', 'style', ' ||', ' {},', '\\n           ', ' this', '.', 'position', 'Props', '\\n         ', ' ),', '\\n         ', ' parent', 'Width', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'terminal', '/', 'index', '.', 'jsx', '<|ret-body|>', ' ', ' }', '\\n\\n ', ' //', ' get', 'Pwd', ' =', ' async', ' ()', ' =>', ' {', '\\n ', ' //', '  ', ' const', ' {', ' sessionId', ',', ' config', ' }', ' =', ' this', '.', 'props', '\\n ', ' //', '  ', ' const', ' {', ' pid', ' }', ' =', ' this', '.', 'state', '\\n ', ' //', '  ', ' const', ' pr', 'ps', ' =', ' {', '\\n ', ' //', '    ', ' host', ':', ' config', '.', 'host', ',', '\\n ', ' //', '    ', ' port', ':', ' config', '.', 'port', ',', '\\n ', ' //', '    ', ' pid', ',', '\\n ', ' //', '    ', ' sessionId', '\\n ', ' //', '  ', ' }', '\\n ', ' //', '  ', ' const', ' result', ' =', ' await', ' run', 'Cmd', 's', '(', 'pr', 'ps', ',', \" ['\", 'pwd', \"'])\", '\\n ', ' //', '    ', ' .', 'catch', '(', 'window', '.', 'store', '.', 'onError', ')', '\\n ', ' //', '  ', ' return', ' result', ' ?', ' result', '[', '0', '].', 'trim', '()', ' :', \" ''\", '\\n ', ' //', ' }', '\\n\\n ', ' switch', 'Encoding', ' =', ' encode', ' =>', ' {', '\\n   ', ' this', '.', 'encode', ' =', ' encode', '\\n   ', ' this', '.', 'attach', 'Addon', '.', 'decoder', ' =', ' new', ' Text', 'Decoder', '(', 'encode', ')', '\\n ', ' }', '\\n\\n ', ' render', ' ()', ' {', '\\n   ', ' const', ' {', ' id', ',', ' loading', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' {', ' height', ',', ' width', ',', ' left', ',', ' top', ',', ' position', ',', ' id', ':', ' pid', ',', ' active', 'Split', 'Id', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' cls', ' =', ' class', 'names', \"('\", 'term', '-', 'wrap', \"',\", ' {', '\\n     ', \" '\", 'not', '-', 'first', '-', 'term', \"':\", ' !!', 'position', '\\n   ', ' },', \" '\", 'tw', \"-'\", ' +', ' pid', ',', ' {', '\\n     ', \" '\", 'terminal', '-', 'not', '-', 'active', \"':\", ' active', 'Split', 'Id', ' !==', ' pid', '\\n   ', ' })', '\\n   ', ' const', ' pr', 'ps', '1', ' =', ' {', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'sessions', '.', 'jsx', '<|ret-body|>', '       ', ' <', 'div', ' className', '={', 'cls', '}', ' key', '={', 'id', '}>', '\\n         ', ' <', 'Session', '\\n           ', ' {...', 'sess', 'Props', '}', '\\n         ', ' />', '\\n       ', ' </', 'div', '>', '\\n     ', ' )', '\\n   ', ' })', '\\n ', ' }', '\\n\\n ', ' render', 'Tabs', ' =', ' ()', ' =>', ' {', '\\n   ', ' const', ' {', '\\n     ', ' store', ',', '\\n     ', ' config', '\\n   ', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', '\\n     ', ' tabs', ',', '\\n     ', ' current', 'Tab', 'Id', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' tabs', 'Props', ' =', ' {', '\\n     ', ' current', 'Tab', 'Id', ',', '\\n     ', ' config', ',', '\\n     ', ' ...', 'pick', '(', 'store', ',', ' [', '\\n       ', \" '\", 'height', \"',\", '\\n       ', \" '\", 'width', \"',\", '\\n       ', \" '\", 'active', 'Terminal', 'Id', \"',\", '\\n       ', \" '\", 'is', 'Max', 'im', 'ized', \"',\", '\\n       ', \" '\", 'split', 'Terminal', 'S', 'ftp', \"'\", '\\n     ', ' ]),', '\\n     ', ' tabs', ',', '\\n     ', ' ...', 'pick', '(', 'this', ',', ' [', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'session', '.', 'jsx', '<|ret-body|>', ' ', ' render', 'S', 'ftp', ' =', ' (', 'sf', 'tp', 'Pos', ')', ' =>', ' {', '\\n   ', ' const', ' {', '\\n     ', ' session', 'Options', ',', '\\n     ', ' sessionId', ',', '\\n     ', ' pid', ',', '\\n     ', ' enable', 'S', 'ftp', ',', '\\n     ', ' s', 'ftp', 'Path', 'Follow', 'Ssh', ',', '\\n     ', ' cwd', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' {', ' split', 'Terminal', 'S', 'ftp', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', ' pane', ',', ' type', ' }', ' =', ' this', '.', 'props', '.', 'tab', '\\n   ', ' if', ' (', 'type', ' ===', ' terminal', 'R', 'dp', 'Type', ')', ' {', '\\n     ', ' return', ' null', '\\n   ', ' }', '\\n   ', ' const', ' height', ' =', ' this', '.', 'compute', 'Height', '()', '\\n   ', ' const', ' cls', ' =', ' pane', ' ===', ' pane', 'Map', '.', 'terminal', ' &&', ' !', 'split', 'Terminal', 'S', 'ftp', '\\n     ', ' ?', \" '\", 'hide', \"'\", '\\n     ', ' :', \" ''\", '\\n   ', ' const', ' ext', 's', ' =', ' {', '\\n     ', ' s', 'ftp', 'Path', 'Follow', 'Ssh', ',', '\\n     ', ' cwd', ',', '\\n     ', ' pid', ',', '\\n     ', ' enable', 'S', 'ftp', ',', '\\n     ', ' session', 'Options', ',', '\\n     ', ' sessionId', ',', '\\n     ', ' pane', ',', '\\n     ', ' ...', 'this', '.', 'props', '\\n   ', ' }', '\\n   ', ' return', ' (', '\\n     ', ' <', 'div', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'sessions', '.', 'jsx', '<|ret-body|>', '     ', ' store', ',', ' config', '\\n   ', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', '\\n     ', ' current', 'Tab', 'Id', ',', '\\n     ', ' tabs', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' if', ' (!', 'tabs', '.', 'length', ')', ' {', '\\n     ', ' return', ' this', '.', 'render', 'No', 'Session', '()', '\\n   ', ' }', '\\n   ', ' return', ' tabs', '.', 'map', '((', 'tab', ')', ' =>', ' {', '\\n     ', ' const', ' {', ' id', ',', ' type', ' }', ' =', ' tab', '\\n     ', ' const', ' cls', ' =', ' classNames', '(', '\\n       ', ' `', 'session', '-', 'wrap', ' session', '-${', 'id', '}`,', '\\n       ', ' {', '\\n         ', \" '\", 'session', '-', 'current', \"':\", ' id', ' ===', ' current', 'Tab', 'Id', '\\n       ', ' }', '\\n     ', ' )', '\\n     ', ' const', ' sess', 'Props', ' =', ' {', '\\n       ', ' current', 'Tab', 'Id', ',', '\\n       ', ' tab', ':', ' to', 'Simple', 'Obj', '(', 'tab', '),', '\\n       ', ' ...', 'pick', '(', 'store', ',', ' [', '\\n         ', \" '\", 'resolution', 's', \"',\", '\\n         ', \" '\", 'hide', 'Del', 'Key', 'Tip', \"',\", '\\n         ', \" '\", 'file', 'Operation', \"',\", '\\n         ', \" '\", 'file', \"',\", '\\n         ', \" '\", 'height', \"',\", '\\n         ', \" '\", 'width', \"',\", '\\n         ', \" '\", 'active', 'Terminal', 'Id', \"',\", '\\n         ', \" '\", 'pinned', 'Quick', 'Command', 'Bar', \"',\", '\\n         ', \" '\", 'tabs', 'Height', \"',\", '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'sf', 'tp', '/', 'sf', 'tp', '-', 'entry', '.', 'jsx', '<|ret-body|>', '       ', ' height', '\\n     ', ' }', '\\n     ', ' return', ' this', '.', 'render', 'Section', '(', 't', ',', ' style', ',', ' width', ' /', ' ', '2', ')', '\\n   ', ' })', '\\n ', ' }', '\\n\\n ', ' render', ' ()', ' {', '\\n   ', ' const', ' {', ' height', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' {', '\\n     ', ' id', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' all', ' =', ' {', '\\n     ', ' className', ':', \" '\", 'sf', 'tp', '-', 'wrap', ' over', 'hide', ' relative', \"',\", '\\n     ', ' id', ':', ' `', 'id', '-${', 'id', '}`,', '\\n     ', ' style', ':', ' {', ' height', ' }', '\\n   ', ' }', '\\n   ', ' return', ' (', '\\n     ', ' <', 'div', '\\n       ', ' {...', 'all', '}', '\\n     ', ' >', '\\n       ', ' {', '\\n         ', ' this', '.', 'render', 'Sections', '()', '\\n       ', ' }', '\\n     ', ' </', 'div', '>', '\\n   ', ' )', '\\n ', ' }', '\\n', '}', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'session', '.', 'jsx', '<|ret-body|>', '\\n', 'const', ' {', ' prefix', ' }', ' =', ' window', '\\n', 'const', ' e', ' =', ' prefix', \"('\", 'ssh', \"')\", '\\n', 'const', ' m', ' =', ' prefix', \"('\", 'menu', \"')\", '\\n', '\\n', 'export', ' default', ' class', ' Session', 'Wrapper', ' extends', ' Component', ' {', '\\n ', ' constructor', ' (', 'props', ')', ' {', '\\n   ', ' super', '(', 'props', ')', '\\n   ', ' const', ' id', ' =', ' uid', '()', '\\n   ', ' const', ' {', '\\n     ', ' terminal', 's', ' =', ' [', '\\n       ', ' {', '\\n         ', ' id', ',', '\\n         ', ' position', ':', ' ', '0', '\\n       ', ' }', '\\n     ', ' ]', '\\n   ', ' }', ' =', ' props', '.', 'tab', '\\n   ', ' const', ' active', 'Split', 'Id', ' =', ' terminal', 's', '[', '0', '].', 'id', '\\n   ', ' this', '.', 'state', ' =', ' {', '\\n     ', ' id', ':', ' uid', '(),', '\\n     ', ' pid', ':', ' null', ',', '\\n     ', ' enable', 'S', 'ftp', ':', ' false', ',', '\\n     ', ' cwd', ':', \" '',\", '\\n     ', ' s', 'ftp', 'Path', 'Follow', 'Ssh', ':', ' !!', 'props', '.', 'config', '.', 'sf', 'tp', 'Path', 'Follow', 'Ssh', ',', '\\n     ', ' //', ' split', 'Direction', ':', ' terminal', 'Split', 'Direction', 'Map', '.', 'horizontal', ',', '\\n     ', ' active', 'Split', 'Id', ',', '\\n     ', ' info', 'Panel', 'Pin', 'ned', ':', ' false', ',', '\\n     ', ' key', ':', ' Math', '.', 'random', '(),', '\\n     ', ' session', 'Options', ':', ' null', ',', '\\n     ', ' sessionId', ':', ' generate', '(),', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'session', '.', 'jsx', '<|ret-body|>', '         ', \" '\", 'del', 'Tab', \"',\", '\\n         ', \" '\", 'config', \"',\", '\\n         ', \" '\", 'edit', 'Tab', \"'\", '\\n       ', ' ]),', '\\n       ', ' ...', 'pick', '(', '\\n         ', ' this', ',', '\\n         ', ' [', '\\n           ', \" '\", 'set', 'Session', 'State', \"'\", '\\n         ', ' ])', '\\n     ', ' }', '\\n     ', ' return', ' (', '\\n       ', ' <', 'R', 'dp', 'Session', '\\n         ', ' {...', 'rdp', 'Props', '}', '\\n       ', ' />', '\\n     ', ' )', '\\n   ', ' }', '\\n   ', ' const', ' {', ' split', 'Terminal', 'S', 'ftp', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' cls', ' =', ' pane', ' ===', ' pane', 'Map', '.', 'terminal', ' &&', ' !', 'split', 'Terminal', 'S', 'ftp', '\\n     ', ' ?', \" '\", 'terms', '-', 'box', \"'\", '\\n     ', ' :', \" '\", 'terms', '-', 'box', ' hide', \"'\", '\\n   ', ' const', ' height', ' =', ' this', '.', 'compute', 'Height', '()', '\\n   ', ' const', ' {', ' tab', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' width', ' =', ' this', '.', 'getWidth', '()', '\\n   ', ' const', ' theme', 'Config', ' =', ' copy', '(', 'window', '.', 'store', '.', 'get', 'Theme', 'Config', '())', '\\n   ', ' const', ' log', 'Name', ' =', ' safe', 'Name', '(`${', 'tab', '.', 'title', ' ?', ' tab', '.', 'title', ' +', \" '_'\", ' :', \" ''\", '}${', 'tab', '.', 'host', ' ?', ' tab', '.', 'host', ' +', \" '_'\", ' :', \" ''\", '}${', 'id', '}`)', '\\n   ', ' const', ' p', 'ops', ' =', ' {', '\\n     ', ' ...', 'this', '.', 'props', ',', '\\n     ', ' active', 'Split', 'Id', ',', '\\n     ', ' s', 'ftp', 'Path', 'Follow', 'Ssh', ',', '\\n     ', ' theme', 'Config', ',', '\\n', '<|ret-start|>', '<filename>', 'src', '/', 'client', '/', 'components', '/', 'session', '/', 'session', '.', 'jsx', '<|ret-body|>', '\\n ', ' get', 'Width', 'S', 'ftp', ' =', ' ()', ' =>', ' {', '\\n   ', ' const', ' {', ' width', ',', ' left', 'Sidebar', 'Width', ',', ' pinned', ',', ' opened', 'Side', 'Bar', ' }', ' =', ' this', '.', 'props', '\\n   ', ' const', ' lt', ' =', ' pinned', ' &&', ' opened', 'Side', 'Bar', ' ?', ' left', 'Sidebar', 'Width', ' :', ' ', '0', '\\n   ', ' return', ' width', ' -', ' lt', ' -', ' ', '4', '2', '\\n ', ' }', '\\n\\n ', ' render', 'Terminal', ' =', ' (', 'term', 'Pos', ')', ' =>', ' {', '\\n   ', ' const', ' {', '\\n     ', ' active', 'Split', 'Id', ',', '\\n     ', ' id', ',', '\\n     ', ' session', 'Options', ',', '\\n     ', ' sessionId', ',', '\\n     ', ' s', 'ftp', 'Path', 'Follow', 'Ssh', '\\n   ', ' }', ' =', ' this', '.', 'state', '\\n   ', ' const', ' {', '\\n     ', ' pane', ',', ' type', '\\n   ', ' }', ' =', ' this', '.', 'props', '.', 'tab', '\\n   ', ' if', ' (', 'type', ' ===', ' terminal', 'R', 'dp', 'Type', ')', ' {', '\\n     ', ' const', ' r', 'dp', 'Props', ' =', ' {', '\\n       ', ' tab', ':', ' this', '.', 'props', '.', 'tab', ',', '\\n       ', ' sessionId', ',', '\\n       ', ' ...', 'pick', '(', 'this', '.', 'props', ',', ' [', '\\n         ', \" '\", 'resolution', 's', \"',\", '\\n         ', \" '\", 'height', \"',\", '\\n         ', \" '\", 'width', \"',\", '\\n         ', \" '\", 'tabs', 'Height', \"',\", '\\n         ', \" '\", 'left', 'Sidebar', 'Width', \"',\", '\\n         ', \" '\", 'pinned', \"',\", '\\n         ', \" '\", 'opened', 'Side', 'Bar', \"',\", '\\n', '<fim_prefix>', 'Panel', 'Pin', 'ned', ',', '\\n     ', ' ...', 'pick', '(', 'this', '.', 'props', '.', 'config', ',', \" ['\", 'host', \"',\", \" '\", 'port', \"',\", \" '\", 'save', 'Terminal', 'Log', 'ToFile', \"',\", \" '\", 'terminal', 'Infos', \"']),\", '\\n     ', ' ...', 'info', 'Panel', 'Props', ',', '\\n     ', ' app', 'Path', ':', ' this', '.', 'props', '.', 'app', 'Path', ',', '\\n     ', ' right', 'Sidebar', 'Width', ':', ' this', '.', 'props', '.', 'right', 'Sidebar', 'Width', ',', '\\n     ', ' show', 'Info', ',', '\\n     ', ' tabs', 'Height', ':', ' this', '.', 'props', '.', 'tabs', 'Height', ',', '\\n     ', ' top', 'Menu', 'Height', ':', ' this', '.', 'props', '.', 'top', 'Menu', 'Height', ',', '\\n     ', ' toggle', 'Info', 'Pin', 'ned', ':', ' this', '.', 'toggle', 'Info', 'Pin', 'ned', ',', '\\n     ', ' hide', 'Info', 'Panel', ':', ' this', '.', 'hide', 'Info', 'Panel', '\\n   ', ' }', '\\n   ', ' const', ' cls', ' =', ' class', 'names', '(', '\\n     ', \" '\", 'term', '-', 'sf', 'tp', '-', 'box', \"',\", '\\n     ', ' pane', ',', '\\n     ', ' split', 'Direction', ',', '\\n     ', ' {', '\\n       ', \" '\", 'is', '-', 'transport', 'ing', \"':\", ' this', '.', 'props', '.', 'tab', '.', 'is', 'Transport', 'ing', '\\n     ', ' },', '\\n     ', ' {', '\\n       ', \" '\", 'disable', '-', 'ssh', \"':\", ' this', '.', 'props', '.', 'tab', '.', 'enable', 'Ssh', ' ===', ' false', '\\n     ', ' }', '\\n   ', ' )', '\\n   ', ' const', ' all', 'Props', ' =', ' {', '\\n     ', ' className', ':', ' cls', ',', '\\n     ', ' id', ':', ' `', 'is', '-${', 'this', '.', 'props', '.', 'tab', '.', 'id', '}`,', '\\n     ', ' style', ':', ' {', '\\n       ', ' width', ':', ' this', '<fim_middle>'], token_ids=[32, 21422, 346]), resolution=CompletionResolution(accepted=False, timestamp=datetime.datetime(2024, 5, 15, 11, 53, 34, 865000, tzinfo=datetime.timezone(datetime.timedelta(0), 'UTC'))), feedback=None), ground_truth=\".getWidth() + 'px',\\n        height: this.computeHeight() + 'px'\")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["all_data[0][\"datum\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_skip = 0\n", "num_pause = 0\n", "num_match_argmax = 0\n", "num_match_random1 = 0\n", "num_match_random2 = 0\n", "num_match_nostrict = 0\n", "keep_data = []\n", "for data in all_data:\n", "    generated_tokens = extract_tokens_before_stop_tokens(\n", "        data[\"output_argmax\"].generated_tokens,\n", "        [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],\n", "    )\n", "    output_random_1_tokens = data[\"output_random_1_tokens\"]\n", "    output_random_2_tokens = data[\"output_random_2_tokens\"]\n", "    ground_truth_tokens_w_pause_skip = data[\"ground_truth_tokens_w_pause_skip\"]\n", "    if tokenizer.special_tokens.skip in ground_truth_tokens_w_pause_skip:\n", "        num_skip += 1\n", "    if tokenizer.special_tokens.pause in ground_truth_tokens_w_pause_skip:\n", "        num_pause += 1\n", "    if ground_truth_tokens_w_pause_skip == generated_tokens:\n", "        num_match_argmax += 1\n", "    if ground_truth_tokens_w_pause_skip == output_random_1_tokens:\n", "        num_match_random1 += 1\n", "    if ground_truth_tokens_w_pause_skip == output_random_2_tokens:\n", "        num_match_random2 += 1\n", "    if (\n", "        tokenizer.detokenize(generated_tokens).strip()\n", "        == tokenizer.detokenize(ground_truth_tokens_w_pause_skip).strip()\n", "    ):\n", "        num_match_nostrict += 1\n", "    # Start to filter out the data.\n", "    if tokenizer.special_tokens.skip in ground_truth_tokens_w_pause_skip:\n", "        continue\n", "    pos_tokens = ground_truth_tokens_w_pause_skip\n", "    if ground_truth_tokens_w_pause_skip != generated_tokens:\n", "        neg_tokens = generated_tokens\n", "    elif ground_truth_tokens_w_pause_skip != output_random_1_tokens:\n", "        neg_tokens = output_random_1_tokens\n", "    elif ground_truth_tokens_w_pause_skip != output_random_2_tokens:\n", "        neg_tokens = output_random_2_tokens\n", "    else:\n", "        continue\n", "    assert pos_tokens != neg_tokens\n", "    keep_data.append(\n", "        {\n", "            \"prompt_tokens\": data[\"output_argmax\"].prompt_tokens,\n", "            \"positive_tokens\": pos_tokens,\n", "            \"negative_tokens\": neg_tokens,\n", "        }\n", "    )\n", "print(f\"{num_skip}/{len(all_data)} examples have skip.\")\n", "print(f\"{num_pause}/{len(all_data)} examples have pause.\")\n", "print(f\"{num_match_argmax}/{len(all_data)} examples have match argmax.\")\n", "print(f\"{num_match_random1}/{len(all_data)} examples have match random1.\")\n", "print(f\"{num_match_random2}/{len(all_data)} examples have match random2.\")\n", "print(f\"{num_match_nostrict}/{len(all_data)} examples have match nostrict.\")\n", "print(f\"Kept {len(keep_data)}/{len(all_data)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.detokenize(keep_data[0][\"prompt_tokens\"][-100:]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "output_path = pathlib.Path(\n", "    \"/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-hindsight/dataset\"\n", ")\n", "\n", "seq = 7936 + 1\n", "num_exceed = 0\n", "\n", "builder = indexed_dataset.make_builder(\n", "    str(output_path.with_suffix(\".bin\")),\n", "    impl=\"mmap\",\n", "    vocab_size=tokenizer.vocab_size,\n", ")\n", "for data in keep_data:\n", "    tokens = (\n", "        data[\"prompt_tokens\"] + data[\"positive_tokens\"] + [tokenizer.special_tokens.eos]\n", "    )\n", "    if len(tokens) >= seq:\n", "        tokens = tokens[:seq]\n", "        num_exceed += 1\n", "    else:\n", "        tokens = tokens + [tokenizer.special_tokens.padding] * (seq - len(tokens))\n", "    assert len(tokens) == seq\n", "    builder.add_item(tokens)\n", "builder.finalize(str(output_path.with_suffix(\".idx\")))\n", "print(f\"There are {num_exceed} examples exceeding {seq}.\")\n", "print(f\"Save {len(keep_data)} examples to {output_path}.\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}