{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_file\n", "from research.core.utils_for_str import show_completion\n", "\n", "# show_completion(prompt_input.prefix, prompt_input.suffix, x['response_text'], max_lines=100)\n", "\n", "dogfood_v1 = utils_for_file.read_jsonl(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/raw-reject-202405-202409-dogfood/raw-data-20000.jsonl\"\n", ")\n", "dogfood_v2 = utils_for_file.read_jsonl(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/raw-reject-202405-202409-dogfood-shard/raw-data-20000.jsonl\"\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from base.prompt_format_completion.simple_elden_prompt_formatter import (\n", "    SimpleEldenPromptFormatter,\n", "    SimpleEldenPromptFormatterConfig,\n", "    TokenApportionment,\n", ")\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "prompt_formatter = SimpleEldenPromptFormatter(\n", "    SimpleEldenPromptFormatterConfig(\n", "        max_prompt_length=7680,\n", "        token_config=TokenApportionment(\n", "            path_len=50,\n", "            prefix_len=1024,\n", "            suffix_len=512,\n", "            retrieval_len=7680,\n", "        ),\n", "        per_retriever_max_tokens={\n", "            \"signature_retriever\": 1024,\n", "            \"recency_retriever\": 1024,\n", "            \"dense_retriever\": 7680 - 1024,\n", "        },\n", "        version=\"v2.0\",\n", "    ),\n", "    tokenizer,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "\n", "num_target_tokens = []\n", "all_prompt_tokens = []\n", "all_pos_tokens = []\n", "all_neg_tokens = []\n", "\n", "for x in tqdm.tqdm(dogfood_v1 + dogfood_v2, desc=\"tokenize\"):\n", "    pi_dict = x[\"prompt_input\"]\n", "    prompt_input = PromptInput(\n", "        prefix=pi_dict[\"prefix\"],\n", "        suffix=pi_dict[\"suffix\"],\n", "        prefix_begin=0,\n", "        path=pi_dict[\"path\"],\n", "        retrieved_chunks=[\n", "            PromptChunk.schema().load(y) for y in pi_dict[\"retrieved_chunks\"]\n", "        ],\n", "        lang=None,\n", "    )\n", "    prompt_output = prompt_formatter.format_prompt(prompt_input, 0)\n", "    target_tokens = tokenizer.tokenize_safe(x[\"response_text\"])\n", "    num_target_tokens.append(len(target_tokens))\n", "    all_prompt_tokens.append(prompt_output.tokens())\n", "    all_pos_tokens.append([-1])\n", "    all_neg_tokens.append(target_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "print(\n", "    f\"{np.mean(num_target_tokens)=}, {np.std(num_target_tokens)=}, {np.min(num_target_tokens)=}, {np.max(num_target_tokens)=}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset as HFDataset\n", "\n", "rlhf_train_path = \"/mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2\"\n", "rlhf_train_data = HFDataset.load_from_disk(rlhf_train_path)\n", "\n", "prompt_tokens = rlhf_train_data[0][\"prompt_tokens\"]\n", "pos_tokens = rlhf_train_data[0][\"pos_tokens\"]\n", "neg_tokens = rlhf_train_data[0][\"neg_tokens\"]\n", "\n", "print(tokenizer.detokenize(prompt_tokens))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}