{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 26052 training examples and 1727 validation examples.\n", "Each item has keys train_dataset[0].keys()=dict_keys(['prompt_tokens', 'pos_tokens', 'neg_tokens'])\n"]}], "source": ["from datasets import Dataset as HFDataset\n", "\n", "train_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/0914-train\"\n", ")\n", "eval_dataset = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/0914-eval-large\"\n", ")\n", "print(\n", "    f\"There are {len(train_dataset)} training examples and {len(eval_dataset)} validation examples.\"\n", ")\n", "\n", "print(f\"Each item has keys {train_dataset[0].keys()=}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fastbackward.reward_aux_funcs import build_pairwise_reward_tokens\n", "\n", "# # Example Data\n", "data = train_dataset[19]\n", "# print(data[\"prompt_tokens\"])\n", "# print(data[\"pos_tokens\"])\n", "# print(data[\"neg_tokens\"])\n", "\n", "prompt_tokens, target_tokens = build_pairwise_reward_tokens(\n", "    data, tokenizer, shuffle=True\n", ")\n", "print(tokenizer.detokenize(prompt_tokens))\n", "print(tokenizer.detokenize(target_tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import tqdm\n", "\n", "num_pause, num_skip = 0, 0\n", "prompt_lens, target_lens = [], []\n", "total = len(train_dataset)\n", "for index in tqdm.tqdm(range(total), total=total):\n", "    data = train_dataset[index]\n", "    if special_tokens.pause in data[\"pos_tokens\"]:\n", "        num_pause += 1\n", "    if special_tokens.skip in data[\"pos_tokens\"]:\n", "        num_skip += 1\n", "    prompt_lens.append(len(data[\"prompt_tokens\"]))\n", "    target_lens.append(len(data[\"pos_tokens\"]))\n", "    target_lens.append(len(data[\"neg_tokens\"]))\n", "print(f\"There are {num_pause}/{total} examples with pause tokens\")\n", "print(f\"There are {num_skip}/{total} examples with skip tokens.\")\n", "print(f\"Prompt tokens: {np.max(prompt_lens)=}\")\n", "print(f\"Target tokens: {np.max(target_lens)=}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["prompt_tokens = data[\"prompt_tokens\"]"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}