{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import argparse\n", "from sklearn.model_selection import train_test_split\n", "from pathlib import Path\n", "\n", "from base.datasets import tenants\n", "from research.core.data_paths import canonicalize_path\n", "from base.datasets.completion_dataset import CompletionDataset\n", "from datetime import datetime\n", "from base.datasets import completion\n", "import tqdm"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2024-09-16 23:59:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 10 missing entries.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 10 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 10 keys (total size 7001968).\u001b[0m\n", "\u001b[2m2024-09-16 23:59:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 7001968 units to insert 10 entries.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:13\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRetrieving 5 missing entries.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mFound 5 missing entries; 0 still missing.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mTrying to inserting 5 keys (total size 51808).\u001b[0m\n", "\u001b[2m2024-09-16 23:59:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mNeed 51808 units to insert 5 entries.\u001b[0m\n", "\u001b[2m2024-09-16 23:59:14\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mEvicted 0 entries to free 0 units.\u001b[0m\n"]}], "source": ["start_date = datetime.fromisoformat(\"2024-05-01T00:00:00+00:00\")\n", "end_date = datetime.fromisoformat(\"2024-09-01T00:00:00+00:00\")\n", "\n", "limit = 10\n", "TENANT_NAME = \"dogfood\"\n", "\n", "completion_filters = CompletionDataset.Filters(\n", "    model_name=None,\n", "    timestamp_begin=start_date,\n", "    timestamp_end=end_date,\n", "    accepted_completion=False,\n", ")\n", "completions = CompletionDataset.create(\n", "    tenant=tenants.get_tenant(TENANT_NAME),\n", "    filters=completion_filters,\n", "    limit=limit,\n", "    order_by=None,\n", "    page_size=8192,\n", ")\n", "\n", "datum_list = []\n", "for x in tqdm.tqdm(\n", "    completions.get_completions(), desc=\"Get data\", total=completions.num_rows\n", "):\n", "    datum_list.append(x)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from base.prompt_format.common import PromptChunk\n", "\n", "chunks: list[PromptChunk] = []\n", "for x in data.response.retrieved_chunks:\n", "    unique_id = f\"{x.blob_name}:{x.crange.start}-{x.crange.stop}\"\n", "    chunks.append(\n", "        PromptChunk(\n", "            text=x.text,\n", "            path=x.path,\n", "            unique_id=unique_id,\n", "            origin=x.origin,\n", "            char_start=x.crange.start,\n", "            char_end=x.crange.stop,\n", "            blob_name=x.blob_name,\n", "        )\n", "    )\n", "prompt_input = PromptInput(\n", "    prefix=data.request.prefix,\n", "    suffix=data.request.suffix,\n", "    prefix_begin=0,\n", "    path=data.request.path,\n", "    retrieved_chunks=chunks,\n", "    lang=None,\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["<base.prompt_format.common.PromptFormatterOutput at 0x7f7128a098d0>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from base.prompt_format_completion.simple_elden_prompt_formatter import (\n", "    TokenApportionment,\n", "    SimpleEldenPromptFormatterConfig,\n", "    SimpleEldenPromptFormatter,\n", ")\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "prompt_formatter = SimpleEldenPromptFormatter(\n", "    SimpleEldenPromptFormatterConfig(\n", "        max_prompt_length=7680,\n", "        token_config=TokenApportionment(\n", "            path_len=50,\n", "            prefix_len=1024,\n", "            suffix_len=512,\n", "            retrieval_len=7680,\n", "        ),\n", "        per_retriever_max_tokens={\n", "            \"signature_retriever\": 1024,\n", "            \"recency_retriever\": 1024,\n", "            \"dense_retriever\": 7680 - 1024,\n", "        },\n", "    ),\n", "    tokenizer,\n", ")\n", "\n", "prompt_formatter.format_prompt(prompt_input, 0)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}