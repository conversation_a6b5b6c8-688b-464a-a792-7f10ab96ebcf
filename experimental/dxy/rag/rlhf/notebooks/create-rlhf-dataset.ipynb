{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import concurrent.futures as futures\n", "import copy\n", "import json\n", "import pathlib\n", "\n", "import torch\n", "import tqdm\n", "import zstandard as zstd\n", "\n", "from base.datasets.hindsight_completion_dataset import (\n", "    HindsightCompletionDatum,\n", ")\n", "from base.prompt_format.common import PromptChunk\n", "from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict\n", "from experimental.dxy.rag.rlhf.local_content_manager import (\n", "    LocalContentManager,\n", ")\n", "from experimental.dxy.rag.rlhf.shared_lib import extract_tokens_before_stop_tokens\n", "from research.core import utils_for_log\n", "from research.core.model_input import ModelInput\n", "from research.core.recency_info import convert_from_datasets_recency_info\n", "from research.eval.harness.systems.abs_system import CompletionResult\n", "from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "\n", "def parse_json_file(path: pathlib.Path):\n", "    with open(path, \"r\", encoding=\"utf-8\") as f:\n", "        xdata = json.load(f)\n", "        output_random_1_tokens = xdata[\"output_random_1_tokens\"]\n", "        output_random_2_tokens = xdata[\"output_random_2_tokens\"]\n", "        ground_truth_tokens_w_pause_skip = xdata[\"ground_truth_tokens_w_pause_skip\"]\n", "        datum = HindsightCompletionDatum.schema().load(xdata[\"datum\"])\n", "        output_argmax = CompletionResult.schema().load(xdata[\"output_argmax\"])\n", "        blob_names = xdata[\"blob_names\"]\n", "        output_random_1_tokens = extract_tokens_before_stop_tokens(\n", "            output_random_1_tokens,\n", "            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],\n", "        )\n", "        output_random_2_tokens = extract_tokens_before_stop_tokens(\n", "            output_random_2_tokens,\n", "            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],\n", "        )\n", "        return {\n", "            \"datum\": datum,\n", "            \"blob_names\": blob_names,\n", "            \"output_argmax\": output_argmax,\n", "            \"ground_truth_tokens_w_pause_skip\": ground_truth_tokens_w_pause_skip,\n", "            \"output_random_1_tokens\": output_random_1_tokens,\n", "            \"output_random_2_tokens\": output_random_2_tokens,\n", "        }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["input_folders = [\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard\",\n", "    \"/mnt/efs/augment/user/dxy/hindsight/2024-08-2024-09/aitutor-turing\",\n", "]\n", "all_json_files = []\n", "for input_folder in input_folders:\n", "    target_folder = pathlib.Path(input_folder) / \"token-data-w-skip-pause\"\n", "    cur_json_files = list(target_folder.glob(\"*-*.json\"))\n", "    print(f\"There are {len(cur_json_files)} items in {input_folder}.\")\n", "    all_json_files.extend(cur_json_files)\n", "print(f\"{utils_for_log.time_string()} there are {len(all_json_files)} items.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_data = []\n", "with futures.ThreadPoolExecutor(max_workers=64) as executor:\n", "    all_jobs = []\n", "    for index, json_file in tqdm.tqdm(\n", "        enumerate(all_json_files), total=len(all_json_files), desc=\"Submit jobs\"\n", "    ):\n", "        all_jobs.append(executor.submit(parse_json_file, json_file))\n", "    for job in tqdm.tqdm(\n", "        futures.as_completed(all_jobs), total=len(all_json_files), desc=\"Decompress\"\n", "    ):\n", "        all_data.append(job.result())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}