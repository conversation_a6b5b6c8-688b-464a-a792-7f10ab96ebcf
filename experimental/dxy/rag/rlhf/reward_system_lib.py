"""Reward System."""

import json
import logging
import pathlib
import random
import typing

import numpy as np
import torch

from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.shared_lib import (
    handle_skip_and_concat_with_suffix,
    parse_json_data_from_api_response,
    parse_useful_field_from_simple_elden_v1_tokens,
)
from research.core.utils_for_str import get_first_n_lines, get_last_n_lines
from research.eval.harness import factories
from research.models.fastforward_models import StarCoder2FP8_FastForward
from research.models.meta_model import GenerationOptions

logger = logging.getLogger(__name__)


def create_ffw_fp8_model(
    ffw_ckp: str = "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0",
) -> StarCoder2FP8_FastForward:
    """Create a FastForward model."""
    num_gpus = torch.cuda.device_count()
    assert num_gpus == 1
    info_path = pathlib.Path(ffw_ckp) / "info.json"
    assert info_path.exists()
    with open(info_path, "r") as file:
        xdata = json.load(file)
        manifest_sha256 = xdata.get("manifestSha256")
        assert manifest_sha256 is not None, f"Check {info_path}"
    ffw_ckp_dir = pathlib.Path(ffw_ckp)
    model = StarCoder2FP8_FastForward(
        model_path=ffw_ckp_dir,
        checkpoint_path=ffw_ckp_dir,
        checkpoint_sha256=manifest_sha256,
    )
    return model


class RewardSystemAbsoluteScorer:
    """Reward system using absolute scorer."""

    def __init__(self, ffw_ckp: str, model_name: str, tokenizer: StarCoder2Tokenizer):
        self.tokenizer = tokenizer
        assert pathlib.Path(ffw_ckp).exists()
        # Get the SHA
        info_path = pathlib.Path(ffw_ckp) / "info.json"
        assert info_path.exists()
        with open(info_path, "r") as file:
            xdata = json.load(file)
            manifest_sha256 = xdata.get("manifestSha256")
            assert manifest_sha256 is not None, f"Check {info_path}"
        num_gpus = torch.cuda.device_count()
        assert num_gpus == 1
        self.model_config = {
            "name": model_name,
            "checkpoint_path": ffw_ckp,
            "checkpoint_sha256": manifest_sha256,
            "model_path": ffw_ckp,
        }
        self.model = factories.create_model(self.model_config)
        self.seq_length = self.model.seq_length

    def load(self):
        """Load the model."""
        self.model.load()

    @torch.inference_mode()
    def score(
        self,
        prompt_tokens: list[int],
        candidate_tokens: list[int],
    ) -> float:
        """Compare the reward of candidate_tokens."""
        tokens = prompt_tokens + candidate_tokens
        if len(tokens) + 1 > self.seq_length:
            logger.warning(
                f"{len(tokens) + 1=}, {self.seq_length=}, {len(tokens) + 1 > self.seq_length=}"
            )
            tokens = tokens[: self.seq_length - 1]
        outputs = self.model.raw_generate_tokens(
            tokens,
            GenerationOptions(temperature=0.0, max_generated_tokens=1),
        )
        token_prob = outputs.logits[0].softmax(dim=-1)
        good_s = token_prob[self.tokenizer.special_tokens.good].item()
        bad_s = token_prob[self.tokenizer.special_tokens.bad].item()
        return good_s - bad_s

    @torch.inference_mode()
    def compare(
        self,
        prompt_tokens: list[int],
        candidate_tokens_1: list[int],
        candidate_tokens_2: list[int],
    ) -> float:
        """Compare the reward of candidate_tokens_1 and candidate_tokens_2."""
        score_1 = self.score(prompt_tokens, candidate_tokens_1)
        score_2 = self.score(prompt_tokens, candidate_tokens_2)
        return score_1 - score_2


class RewardSystemZeroShotAPI:
    """Reward system using zero-shot Anthropic API."""

    def __init__(
        self,
        tokenizer: StarCoder2Tokenizer,
        temperature: float = 0.5,
        debug: bool = False,
    ):
        self.tokenizer = tokenizer
        self.max_lines = 64
        self.temperature = temperature
        self.system_prompt = """You are an excellent engineer working at large tech companies, such as Google and Meta.
You will be asked to compare two code snippets in a large codebase to see which one is better.
Please do reasonable assessment and then summarize in one line for which option is better. Here are the requirements:
- To help you decide, the user will prodive some related context in the codebase for you, please take them for your consider, such as did the candidate code align with the signature in the context or not.
    Is the usage of the candidate code similar to examples in the context.
- To evaluate which one is better and can win, please focus primarily on their difference as most parts between them will be the same.
- If they are so similar to each other, and all are correct, then the candidate that aligns better with the examples in context wins.
- When you try to reason to compare, please repeat the difference between these two candidate code snippets firstly.
"""
        self.max_successful_trials = 5
        self.max_trials = 10
        anthropic_api_key_file = pathlib.Path.home() / ".config" / "ANTHROPIC_API_KEY"
        assert anthropic_api_key_file.exists()
        anthropic_api_key = anthropic_api_key_file.read_text().strip()
        self.model = AnthropicDirectClient(
            api_key=anthropic_api_key,
            model_name="claude-3-5-sonnet-20240620",
            temperature=temperature,
            max_output_tokens=1024,
        )
        self.random_order = True
        self.debug = debug

    def _get_response(
        self, messages: list[tuple[str, str]], current_message: str
    ) -> str:
        answer = ""
        for streamed_response in self.model.generate_response_stream(
            messages, self.system_prompt, current_message
        ):
            answer += streamed_response.text
        return answer

    def raw_compare(
        self,
        prompt_tokens: list[int],
        candidate_tokens_1: list[int],
        candidate_tokens_2: list[int],
    ) -> dict[str, typing.Any]:
        tokenizer = self.tokenizer
        field_dict = parse_useful_field_from_simple_elden_v1_tokens(
            prompt_tokens, tokenizer
        )
        context_text = tokenizer.detokenize(field_dict["context_tokens"])
        message_1 = f"Here are the related context:\n{context_text}"
        response_1 = "Awesome, I got it. These retrieved code chunks are coming from the same codebase, is that right?"
        prefix = tokenizer.detokenize(field_dict["prefix_tokens"])
        suffix = tokenizer.detokenize(field_dict["suffix_tokens"])
        prefix_last_n_line = get_last_n_lines(prefix, self.max_lines)
        suffix_first_n_line = get_first_n_lines(suffix, self.max_lines)
        path = tokenizer.detokenize(field_dict["path_tokens"])
        # code_1 = (
        #     prefix_last_n_line
        #     + tokenizer.detokenize(candidate_tokens_1)
        #     + suffix_first_n_line
        # )
        # code_2 = (
        #     prefix_last_n_line
        #     + tokenizer.detokenize(candidate_tokens_2)
        #     + suffix_first_n_line
        # )
        code_1 = prefix_last_n_line + handle_skip_and_concat_with_suffix(
            candidate_tokens_1, tokenizer, suffix_first_n_line
        )
        code_2 = prefix_last_n_line + handle_skip_and_concat_with_suffix(
            candidate_tokens_2, tokenizer, suffix_first_n_line
        )
        message_2 = f"""Yes, correct.
This is the first candidate code snippet.
{path}
```
{code_1}
```"""
        response_2 = "Got it, what is the second candidate code snippet that you want me to compare?"
        current_message = f"""
This is the second candidate code snippet.

{path}
```
{code_2}
```

Which one is better?"""
        messages = [(message_1, response_1), (message_2, response_2)]
        answer = self._get_response(messages, current_message)
        message_ask_for_json = """Awesome! Please output in the json format with two fields, one is a str key for 'reason'.
The other is also a str key for 'winner' and the value should be 'first' if you think the first candidate is better, and 'second' if you think the second candidate is better."""
        json_answer = self._get_response(
            messages + [(current_message, answer)], message_ask_for_json
        )
        return {
            "messages": messages,
            "current_message": current_message,
            "answer": answer,
            "json_answer": json_answer,
        }

    def compare(
        self,
        prompt_tokens: list[int],
        candidate_tokens_1: list[int],
        candidate_tokens_2: list[int],
    ) -> float:
        trials: list[float] = []
        for idx in range(self.max_trials):
            if self.random_order and random.random() < 0.5:
                raw_answer = self.raw_compare(
                    prompt_tokens, candidate_tokens_1, candidate_tokens_2
                )
                score = 1.0
            else:
                raw_answer = self.raw_compare(
                    prompt_tokens, candidate_tokens_2, candidate_tokens_1
                )
                score = -1.0
            try:
                # DEBUG MESSAGE
                if self.debug and idx == 0:
                    print(raw_answer["messages"][1][0])
                    print("-" * 100)
                    print(raw_answer["current_message"])
                    print("-" * 100)
                    print(raw_answer["answer"])
                    print("-" * 100)
                    print(raw_answer["json_answer"])
                    print("-" * 100)
                    print(f"default score = {score}")
                json_answer = raw_answer["json_answer"]
                answer = parse_json_data_from_api_response(json_answer)
                if answer["winner"].lower() == "first":
                    trials.append(score)
                elif answer["winner"].lower() == "second":
                    trials.append(-score)
                else:
                    raise ValueError(f"Invalid answer: {answer}")
                if len(trials) >= self.max_successful_trials:
                    break
            except Exception:
                continue
        print(f"trials ({len(trials)}): {trials}")
        return np.array(trials).mean().item()
