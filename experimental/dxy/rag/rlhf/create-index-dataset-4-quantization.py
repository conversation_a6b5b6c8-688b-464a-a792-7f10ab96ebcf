"""The script to create the index dataset for quantization.

python experimental/dxy/rag/rlhf/create-index-dataset-4-quantization.py


python base/fastforward/starcoder/quantize_starcoder2.py \
    --calibration-steps 800 \
    --log-to-stdout \
    --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HD-ffw" \
    --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HONLY-ffw-fp8" \
    -s c7cb9b50e039bc04c1f3f904f515247a730495bb5067fa41d05544b4d296a4d4 \
    -d /mnt/efs/augment/user/dxy/datasets/rlhf/0915-indexd-honly-v2/dataset

python base/fastforward/starcoder/quantize_starcoder2.py \
    --calibration-steps 800 \
    --log-to-stdout \
    --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw" \
    --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIX-ffw-fp8" \
    -s 171251cd8024aaf16cd8dac08894a2affd4df26aefcc321bcd08e6020512723e \
    -d /mnt/efs/augment/user/dxy/datasets/rlhf/0915-indexd-hmix-v2/dataset
"""

import pathlib

import tqdm
from datasets import Dataset as HFDataset
from datasets import concatenate_datasets
from megatron.data import indexed_dataset

from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from research.fastbackward.rlhf.reward_aux_funcs import build_singular_reward_tokens


def create_hf_dataset(version: str = "v1"):
    if version == "v1":
        dataset = HFDataset.load_from_disk(
            "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2"
        )
    elif version == "v2":
        dataset_1 = HFDataset.load_from_disk(
            "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-train-v2"
        )
        dataset_2 = HFDataset.load_from_disk(
            "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-reject-v2"
        )
        dataset = concatenate_datasets([dataset_1, dataset_2])
    else:
        raise ValueError(f"Unknown version {version}.")
    return dataset


def build_sequence(
    prompt_tokens: list[int],
    target_tokens: list[int],
    eos: int,
    padding: int,
    seqlen: int,
) -> list[int]:
    """Builds the x and y input tensors for Transformer."""
    target_tokens.append(eos)
    tokens = prompt_tokens + target_tokens
    if len(tokens) >= seqlen:
        tokens = tokens[:seqlen]
    else:
        tokens = tokens + [padding] * (seqlen - len(tokens))
    return tokens


def main():
    # dataset = create_hf_dataset(version="v1")
    # output_path = pathlib.Path(
    #     "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-indexd-honly-v2/dataset"
    # )
    dataset = create_hf_dataset(version="v2")
    output_path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/0915-indexd-hmix-v2/dataset"
    )
    print(f"There are {len(dataset)} items in dataset.")

    tokenizer = StarCoder2Tokenizer()
    builder = indexed_dataset.make_builder(
        str(output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=tokenizer.vocab_size,
    )
    for index in tqdm.tqdm(range(len(dataset)), total=len(dataset)):
        data = dataset[index]
        pos_data, neg_data = build_singular_reward_tokens(data, tokenizer, seqlen=7940)
        if pos_data is not None:
            tokens = build_sequence(
                pos_data[0],
                pos_data[1],
                tokenizer.special_tokens.eos,
                tokenizer.special_tokens.padding,
                7940,
            )
            builder.add_item(tokens)
        tokens = build_sequence(
            neg_data[0],
            neg_data[1],
            tokenizer.special_tokens.eos,
            tokenizer.special_tokens.padding,
            7940,
        )
        builder.add_item(tokens)
    builder.finalize(str(output_path.with_suffix(".idx")))
    print(f"Save {len(dataset)} examples to {output_path}.")


if __name__ == "__main__":
    main()
