class UserAgent:
    """A user agent."""

    def __init__(
        self,
        user_agent: str | None,
        agent_type: AgentType | None = None,
        version: tuple[int, int, int] | None = None,
    ):
        if user_agent is None:
            assert agent_type is not None
            assert version is not None
            self.agent_type: AgentType = agent_type
            self.version = version
        else:
            self.user_agent = user_agent
            (self.agent_type, self.version) = self.get_augment_ext_version(user_agent)

    @staticmethod
    def get_augment_ext_version(
        user_agent: str,
    ) -> tuple[AgentType, tuple[int, int, int]]:
        if user_agent.startswith("Augment.vscode-augment/"):
            agent_type = AgentType.VSCODE
        elif user_agent.startswith("augment.intellij/"):
            agent_type = AgentType.IntelliJ
        else:
            raise ValueError(f"Invalid user_agent: {user_agent}")
        # example user_agent:
        # Augment.vscode-augment/0.230.0 (linux; x64; 6.4.1) vscode/1.75.1
        version_str = user_agent.split(" ")[0]
        version = version_str.split("/")[-1]
        # This should be x.y.z, validate its format with re.
        if re.match(r"^\d+\.\d+\.\d+$", version) is not None:
            return agent_type, tuple(map(int, version.split(".")))
        else:
            # This is usually the prototype version.
            return agent_type, (-1, -1, -1)

    def __repr__(self) -> str:
        return f"UserAgent({self.version} | {self.user_agent})"

    def __eq__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        """Equality."""
        if isinstance(other, UserAgent):
            return self.version == other.version
        elif isinstance(other, tuple):
            return self.version == other
        else:
            raise ValueError(f"Invalid other: {other}")

    def __lt__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        raise NotImplementedError()

    def __gt__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        raise NotImplementedError()

    def __ge__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        raise NotImplementedError()

    def __le__(self, other: "UserAgent | tuple[int, int, int]") -> bool:
        raise NotImplementedError()
