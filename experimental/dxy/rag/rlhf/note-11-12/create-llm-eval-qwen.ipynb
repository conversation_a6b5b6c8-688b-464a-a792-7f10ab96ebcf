{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "import tqdm\n", "\n", "logging.basicConfig(level=logging.INFO)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "tokenizer = Qwen25CoderTokenizer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:numexpr.utils:Note: detected 208 virtual cores but NumExpr set to maximum of 64, check \"NUMEXPR_MAX_THREADS\" environment variable.\n", "INFO:numexpr.utils:Note: NumExpr detected 208 cores but \"NUMEXPR_MAX_THREADS\" not set, so enforcing safe limit of 8.\n", "INFO:numexpr.utils:NumExpr defaulting to 8 threads.\n", "INFO:datasets:PyTorch version 2.3.0+cu121 available.\n", "INFO:faiss.loader:Loading faiss with AVX512 support.\n", "INFO:faiss.loader:Successfully loaded faiss with AVX512 support.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 7435 HindsightOutput elements.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 7435/7435 [00:00<00:00, 51244.10it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 2 examples.\n", "save_dir=PosixPath('/mnt/efs/augment/user/dxy/datasets/rlhf/20241201')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e6ea50019c9b4ad4a23710bdc0fc40a0", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/7435 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create QWEN results on Hindsight-09\n", "import pathlib\n", "from datasets import Dataset as HFDataset\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "\n", "hs_09_run_result_path = \"/mnt/efs/augment/eval/jobs/fZZUh4Cd/000_fastbackward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "hs_09_run_results = [\n", "    HindsightOutput(**x) for x in read_jsonl_zst(hs_09_run_result_path)\n", "]\n", "print(f\"There are {len(hs_09_run_results)} HindsightOutput elements.\")\n", "\n", "hs_09_run_data = {\"prompt_tokens\": [], \"target_tokens\": []}\n", "for x in tqdm.tqdm(hs_09_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    assert len(x.prompt_tokens) < 7900\n", "    hs_09_run_data[\"prompt_tokens\"].append(x.prompt_tokens)\n", "    hs_09_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth))\n", "    # hs_09_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth) + [tokenizer.special_tokens.eos])\n", "print(f\"There are {len(hs_09_run_data)} examples.\")\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/\")\n", "save_dir.mkdir(parents=False, exist_ok=True)\n", "print(f\"{save_dir=}\")\n", "hf_dataset = HFDataset.from_dict(hs_09_run_data)\n", "hf_dataset.save_to_disk(save_dir / \"qwenv1_hs09_s6k\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 6557 HindsightOutput elements.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 6557/6557 [00:00<00:00, 67080.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 2 examples.\n", "save_dir=PosixPath('/mnt/efs/augment/user/dxy/datasets/rlhf/20241201')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d9670ae63bac4cb4b944c0b6d630903a", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/6557 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create QWEN results on Hindsight-06\n", "import pathlib\n", "from datasets import Dataset as HFDataset\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "\n", "hs_06_run_result_path = \"/mnt/efs/augment/eval/jobs/BPm3ybYP/000_fastbackward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "hs_06_run_results = [\n", "    HindsightOutput(**x) for x in read_jsonl_zst(hs_06_run_result_path)\n", "]\n", "print(f\"There are {len(hs_06_run_results)} HindsightOutput elements.\")\n", "\n", "hs_06_run_data = {\"prompt_tokens\": [], \"target_tokens\": []}\n", "for x in tqdm.tqdm(hs_06_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    assert len(x.prompt_tokens) < 7900\n", "    hs_06_run_data[\"prompt_tokens\"].append(x.prompt_tokens)\n", "    hs_06_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth))\n", "print(f\"There are {len(hs_06_run_data)} examples.\")\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/\")\n", "save_dir.mkdir(parents=False, exist_ok=True)\n", "print(f\"{save_dir=}\")\n", "hf_dataset = HFDataset.from_dict(hs_06_run_data)\n", "hf_dataset.save_to_disk(save_dir / \"qwenv1_hs06_s6k\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 6923 HindsightOutput elements.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 6923/6923 [00:00<00:00, 73684.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 2 examples.\n", "save_dir=PosixPath('/mnt/efs/augment/user/dxy/datasets/rlhf/20241201')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0df9d23781644de69c45af7d4f2fb4a4", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/6923 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create QWEN results on Hindsight-10\n", "import pathlib\n", "from datasets import Dataset as HFDataset\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "\n", "hs_10_run_result_path = \"/mnt/efs/augment/eval/jobs/LWE7RHhH/000_fastbackward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "hs_10_run_results = [\n", "    HindsightOutput(**x) for x in read_jsonl_zst(hs_10_run_result_path)\n", "]\n", "print(f\"There are {len(hs_10_run_results)} HindsightOutput elements.\")\n", "\n", "hs_10_run_data = {\"prompt_tokens\": [], \"target_tokens\": []}\n", "for x in tqdm.tqdm(hs_10_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    assert len(x.prompt_tokens) < 7900\n", "    hs_10_run_data[\"prompt_tokens\"].append(x.prompt_tokens)\n", "    hs_10_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth))\n", "print(f\"There are {len(hs_10_run_data)} examples.\")\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/\")\n", "save_dir.mkdir(parents=False, exist_ok=True)\n", "print(f\"{save_dir=}\")\n", "hf_dataset = HFDataset.from_dict(hs_10_run_data)\n", "hf_dataset.save_to_disk(save_dir / \"qwenv1_hs10_s6k\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 9711 CCEvalOutput elements.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 9711/9711 [00:00<00:00, 81626.99it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 2 examples.\n", "save_dir=PosixPath('/mnt/efs/augment/user/dxy/datasets/rlhf/20241201')\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3b8104ebd191421c9359b588ddc4f31a", "version_major": 2, "version_minor": 0}, "text/plain": ["Saving the dataset (0/1 shards):   0%|          | 0/9711 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create QWEN results on the CCEval Data\n", "import pathlib\n", "from datasets import Dataset as HFDataset\n", "from research.core.utils_for_file import read_jsonl_zst\n", "from research.eval.harness.tasks.cceval import CCEvalOutput\n", "\n", "cceval_run_result_path = \"/mnt/efs/augment/eval/jobs/krWgUP3W/000_fastbackward_CCEval_completed_patches.jsonl.zst\"\n", "cceval_run_results = [CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)]\n", "print(f\"There are {len(cceval_run_results)} CCEvalOutput elements.\")\n", "\n", "cceval_run_data = {\"prompt_tokens\": [], \"target_tokens\": []}\n", "for x in tqdm.tqdm(cceval_run_results):\n", "    assert len(x.prompt_tokens) > 10\n", "    assert len(x.prompt_tokens) < 7000\n", "    cceval_run_data[\"prompt_tokens\"].append(x.prompt_tokens)\n", "    cceval_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth))\n", "    # cceval_run_data[\"target_tokens\"].append(tokenizer.tokenize_safe(x.ground_truth) + [tokenizer.special_tokens.eos])\n", "print(f\"There are {len(cceval_run_data)} examples.\")\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/20241201/\")\n", "save_dir.mkdir(parents=False, exist_ok=True)\n", "print(f\"{save_dir=}\")\n", "cceval_dataset = HFDataset.from_dict(cceval_run_data)\n", "cceval_dataset.save_to_disk(save_dir / \"qwenv1_cceval_s6k\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}