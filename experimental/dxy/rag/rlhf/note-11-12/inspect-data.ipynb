{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import collections\n", "import pathlib\n", "import random\n", "\n", "import numpy as np\n", "import tqdm\n", "from datasets import Dataset as HFDataset\n", "\n", "from base.datasets import completion, tenants\n", "from base.datasets.completion_dataset import CompletionDataset, CompletionDatum\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from base.tokenizers.tokenizer import RagSpecialTokens\n", "from experimental.dxy.rag.rlhf.shared_lib import (\n", "    traverse_date_range,\n", "    inspect_skip_tokens,\n", "    UserAgent,\n", "    AgentType,\n", "    AGENT_FIXING_SKIP,\n", ")\n", "from research.core import utils_for_file\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_data_fn(\n", "    date_ranges: list[str],\n", "    input_dir: pathlib.Path,\n", "):\n", "    \"\"\"Process the data as a dataset.\"\"\"\n", "    assert input_dir.exists(), input_dir\n", "    all_date_ranges = []\n", "    for cur_date_range in date_ranges:\n", "        start_date, end_date = cur_date_range.split(\"-\")\n", "        all_date_ranges.extend(traverse_date_range(start_date, end_date))\n", "    print(f\"There are {len(all_date_ranges)} date ranges.\")\n", "    accept_reject_files: list[pathlib.Path] = []\n", "    for date, xstart, xend in all_date_ranges:\n", "        accept_reject_dir = input_dir / f\"{date}-accept-reject\"\n", "        if accept_reject_dir.exists():\n", "            accept_reject_files.extend(list(accept_reject_dir.glob(\"*-samples.jsonl\")))\n", "        print(f\"{date}: {accept_reject_dir}\")\n", "    assert len(accept_reject_files) > 0, \"Can not find accept/reject files.\"\n", "    print(f\"There are {len(accept_reject_files)} accept/reject files\")\n", "    all_data: list[dict] = []\n", "    for accept_reject_file in tqdm.tqdm(\n", "        accept_reject_files, desc=\"Load accept/reject files\"\n", "    ):\n", "        all_data.extend(utils_for_file.read_jsonl(accept_reject_file))\n", "    return all_data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 14 date ranges.\n", "20241101: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241101-accept-reject\n", "20241102: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241102-accept-reject\n", "20241103: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241103-accept-reject\n", "20241104: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241104-accept-reject\n", "20241105: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241105-accept-reject\n", "20241106: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241106-accept-reject\n", "20241107: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241107-accept-reject\n", "20241108: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241108-accept-reject\n", "20241109: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241109-accept-reject\n", "20241110: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241110-accept-reject\n", "20241111: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241111-accept-reject\n", "20241112: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241112-accept-reject\n", "20241113: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241113-accept-reject\n", "20241114: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241114-accept-reject\n", "There are 42 accept/reject files\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Load accept/reject files: 100%|██████████| 42/42 [01:56<00:00,  2.77s/it]\n"]}], "source": ["all_data = load_data_fn(\n", "    date_ranges=[\"20241101-20241114\"],\n", "    input_dir=pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114\"),\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 167894 / 168033 data with valid version.\n"]}], "source": ["filtered_data = []\n", "for x in all_data:\n", "    x[\"user_agent_obj\"] = UserAgent(x[\"user_agent\"])\n", "    if x[\"user_agent_obj\"].version != (-1, -1, -1):\n", "        filtered_data.append(x)\n", "print(f\"There are {len(filtered_data)} / {len(all_data)} data with valid version.\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 15306 / 168033 data with IntelliJ.\n", "----------------------------------------------------------------------------------------------------\n", "There are 4990 / 15306 accepted data.\n", "There are 433 / 15306 data with skip token.\n", "There are 189 / 433 accepted data with skip token.\n", "----------------------------------------------------------------------------------------------------\n", "There are 53761 / 152588 accepted data.\n", "There are 6471 / 152588 data with skip token.\n", "There are 63 / 6471 accepted data with skip token.\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["all_data_w_intellij = []\n", "all_data_w_vscode = []\n", "for x in filtered_data:\n", "    if x[\"user_agent_obj\"].agent_type == AgentType.IntelliJ:\n", "        all_data_w_intellij.append(x)\n", "    elif x[\"user_agent_obj\"].agent_type == AgentType.VSCODE:\n", "        all_data_w_vscode.append(x)\n", "print(f\"There are {len(all_data_w_intellij)} / {len(all_data)} data with IntelliJ.\")\n", "print(\"-\" * 100)\n", "inspect_skip_tokens(all_data_w_intellij, special_tokens.skip)\n", "print(\"-\" * 100)\n", "inspect_skip_tokens(all_data_w_vscode, special_tokens.skip)\n", "print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 433 / 15306 data with skip token.\n", "There are 189 / 433 accepted data with skip token.\n"]}], "source": ["num_skip = 0\n", "num_skip_w_accept = 0\n", "# cur_all_data = all_data\n", "cur_all_data = all_data_w_intellij\n", "for x in cur_all_data:\n", "    if special_tokens.skip in x[\"generated_token_ids\"]:\n", "        num_skip += 1\n", "        if x[\"accepted\"]:\n", "            num_skip_w_accept += 1\n", "print(f\"There are {num_skip} / {len(cur_all_data)} data with skip token.\")\n", "print(f\"There are {num_skip_w_accept} / {num_skip} accepted data with skip token.\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["unique models: {'eldenv7-0-15b', 'eldenv4-0c-15b', 'eldenv5-1-15b', 'eldenv4-0f-15b'}\n", "agents: ['Augment.vscode-augment/0.282.1 (dar<PERSON>; arm64; 24.0.0) vscode/1.92.2', 'Augment.vscode-augment/0.288.1 (dar<PERSON>; arm64; 24.1.0) vscode/1.90.2', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.289.0 (dar<PERSON>; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (dar<PERSON>; x64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 23.2.0) cursor/1.91.1', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.5.0) vscode/1.95.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.95.0', 'augment.intellij/1.0.0-snapshot IntelliJ IDEA/2024.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.1.0) vscode/1.93.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.217.0 (darwin; arm64; 24.0.0) vscode/1.91.1', 'augment.intellij/0.52.0 PyCharm/2024.1.5', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.1.100+) vscode/1.91.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.26100) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.282.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.19.17-coreweave) vscode/1.95.0', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.10.9-200.fc40.x86_64) vscode/1.94.2', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.286.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 24.0.0) vscode/1.95.1', 'augment.intellij/0.55.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'augment.intellij/0.51.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.282.0 (win32; x64; 10.0.22631) vscode/1.94.0', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.5.0) vscode/1.95.0', 'augment.intellij/0.56.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.93.0', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.19045) cursor/1.93.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 23.4.0) cursor/1.93.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; arm64; 6.8.0-48-generic) vscode/1.93.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.279.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.0-122-generic) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.92.0', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.94.2', 'augment.intellij/0.57.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.92.0', 'Augment.vscode-augment/0.293.0 (darwin; arm64; 23.6.0) vscode-insiders/1.94.0-insider', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.4.0) cursor/1.93.1', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.92.0', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 22.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.2.0) cursor/1.91.1', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.293.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.1.100+) vscode/1.92.0', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.277.1 (win32; x64; 10.0.19045) cursor/1.93.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.277.1 (darwin; x64; 19.6.0) cursor/1.93.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.8.0-40-generic) vscode/1.95.1', 'augment.intellij/0.61.0 PyCharm/2024.2.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.281.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.277.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.19045) vscode/1.95.1', 'Augment.vscode-augment/0.279.99-joel-new-reject.1730398004.3d1d4a562b (darwin; arm64; 23.6.0) code-oss/1.95.0', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.5.0-1023-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.4.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.5.13-650-3434-22042-coreweave-1) vscode/1.93.1', 'Augment.vscode-augment/0.273.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.262.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.93.1', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.4.0-196-generic) vscode/1.88.0', 'augment.intellij/0.56.0 PyCharm/2024.2.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 22.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1066-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 21.4.0) vscode/1.88.1', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.6.0) vscode/1.94.1', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 24.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 5.15.153.1-microsoft-standard-WSL2) vscode/1.86.2', 'Augment.vscode-augment/0.290.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.281.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.279.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.92.0', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1066-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode-insiders/1.96.0-insider', 'augment.intellij/0.64.0 IntelliJ IDEA/2024.2.4', 'augment.intellij/0.51.0 JetBrains Rider/2024.2.7', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'augment.intellij/0.61.0 WebStorm/2024.3', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.19.17-coreweave) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.8.0-47-generic) vscode/1.92.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 4.18.0-477.70.1.el8_8.x86_64) vscode/1.95.2', 'Augment.vscode-augment/0.279.0 (darwin; x64; 22.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'augment.intellij/0.61.0 PyCharm/2024.3', 'augment.intellij/0.51.0 WebStorm/2024.2.3', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.2.0) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.19.17-coreweave) vscode/1.94.2', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.292.0 (darwin; arm64; 23.6.0) vscode-insiders/1.96.0-insider', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.5.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1066-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.6.0) vscode/1.92.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.6.0) vscode/1.95.2', 'augment.intellij/0.56.0 WebStorm/2024.3 EAP', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.5.0) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.279.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 24.0.0) vscode/1.92.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.10.0-33-cloud-amd64) vscode/1.95.1', 'Augment.vscode-augment/0.282.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-1017-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.281.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.153.1-microsoft-standard-WSL2) vscode/1.86.2', 'augment.intellij/0.61.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.278.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.281.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.279.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.94.2', 'augment.intellij/0.39.0 IntelliJ IDEA/2024.1.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.8.0-1016-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.243.2 (darwin; arm64; 23.5.0) cursor/1.93.1', 'augment.intellij/0.61.0 IntelliJ IDEA/2024.2.2', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.8.0-47-generic) vscode/1.94.0', 'Augment.vscode-augment/0.277.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-48-generic) vscode/1.94.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.1 (linux; x64; 4.18.0-477.70.1.el8_8.x86_64) vscode/1.95.2', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.293.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.292.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.278.0 (darwin; arm64; 23.5.0) vscode/1.94.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.11.5-200.fc40.x86_64) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 22.3.0) vscode/1.94.0', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.289.0 (darwin; x64; 23.6.0) cursor/1.93.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.1.100+) vscode/1.92.0', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'augment.intellij/0.57.0 GoLand/2024.2.3', 'Augment.vscode-augment/0.281.0 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.19045) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.153.1-microsoft-standard-WSL2) vscode/1.95.0', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.278.0 (darwin; arm64; 23.6.0) vscode-insiders/1.95.0-insider', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.0', 'Augment.vscode-augment/0.291.0 (darwin; arm64; 24.0.0) vscode/1.95.2', 'Augment.vscode-augment/0.278.0 (linux; x64; 6.8.0-40-generic) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 24.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.277.0 (darwin; x64; 23.5.0) vscode/1.88.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.284.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 24.0.0) vscode/1.95.1', 'augment.intellij/0.56.0 IntelliJ IDEA/2024.2.3', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.8.0-48-generic) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; x64; 4.18.0-477.70.1.el8_8.x86_64) vscode/1.95.1', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.19045) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.286.0 (win32; x64; 10.0.22631) vscode/1.95.2', 'Augment.vscode-augment/0.285.0 (win32; x64; 10.0.22631) vscode/1.94.0', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.17763) vscode/1.95.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.279.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.94.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'augment.intellij/0.61.0 WebStorm/2024.2.3', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.0-122-generic) vscode/1.95.1', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.8.5-301.fc40.x86_64) vscode-insiders/1.93.0-insider', 'Augment.vscode-augment/0.291.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 23.6.0) vscode/1.92.2', 'Augment.vscode-augment/0.279.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 24.0.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'augment.intellij/0.56.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.22631) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.0.0) vscode/1.90.0', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.93.1', 'augment.intellij/0.61.0 WebStorm/2024.2.4', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.0.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.19045) vscode/1.92.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.283.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.94.2', 'augment.intellij/0.58.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.1.100+) vscode/1.82.3', 'Augment.vscode-augment/0.281.0 (linux; x64; 6.1.100+) vscode/1.82.3', 'Augment.vscode-augment/0.282.1 (linux; x64; 4.18.0-477.70.1.el8_8.x86_64) vscode/1.95.1', 'augment.intellij/0.61.0 RubyMine/2024.2.4', 'augment.intellij/0.62.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.292.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.95.1', 'augment.intellij/0.39.0 IntelliJ IDEA/2024.1.3', 'Augment.vscode-augment/0.279.0 (linux; x64; 6.1.100+) vscode/1.86.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.5.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 24.2.0) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 24.0.0) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 22.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.4.0) cursor/1.93.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.0 (linux; x64; 4.18.0-513.18.1.el8_9.x86_64) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.22631) vscode/1.95.2', 'Augment.vscode-augment/0.292.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.5.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.5.0) vscode/1.94.2', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.1.0) vscode/1.95.0', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.92.1', 'Augment.vscode-augment/0.282.1 (darwin; x64; 22.3.0) vscode/1.95.2', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.279.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.0.0) vscode/1.95.0', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-48-generic) vscode/1.95.1', 'Augment.vscode-augment/0.250.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.1', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 24.0.0) vscode/1.95.2', 'augment.intellij/0.56.0 RubyMine/2024.2.4', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.264.1 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 24.0.0) vscode/1.92.2', 'augment.intellij/0.39.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.19045) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'augment.intellij/0.61.0 IntelliJ IDEA/2024.3', 'Augment.vscode-augment/0.277.1 (darwin; x64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.252.1 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.6.0) vscode-insiders/1.95.0-insider', 'augment.intellij/0.56.0 WebStorm/2024.3 RC', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.92.1', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (linux; x64; 5.15.0-124-generic) vscode/1.85.2', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 24.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-47-generic) vscode/1.92.2', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1066-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.291.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.1.0) cursor/1.93.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.291.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.292.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.4.0) vscode/1.95.2', 'augment.intellij/0.0.47 IntelliJ IDEA/2024.1.4', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.0-122-generic) vscode/1.85.2', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.4.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.92.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.0', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (darwin; x64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 22.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.92.0', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 24.2.0) vscode/1.95.2', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.5.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.26100) vscode/1.95.2', 'Augment.vscode-augment/0.281.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.0.0) vscode/1.95.0', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.291.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.292.0 (darwin; arm64; 24.1.0) vscode/1.95.2', 'augment.intellij/0.56.0 WebStorm/2024.2.4', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'augment.intellij/0.59.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.6.0) cursor/1.93.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.291.0 (darwin; arm64; 23.5.0) vscode/1.95.2', 'augment.intellij/0.56.0 PyCharm/2024.2.3', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.280.0 (win32; x64; 10.0.22631) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (win32; x64; 10.0.17763) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'augment.intellij/0.48.0 PyCharm/2024.1.5', 'Augment.vscode-augment/0.292.0 (darwin; arm64; 23.4.0) vscode/1.95.2', 'Augment.vscode-augment/0.279.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1066-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.278.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.5.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.2.0) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.5.0) vscode/1.95.1', 'Augment.vscode-augment/0.293.0 (darwin; arm64; 24.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.291.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.19.17-coreweave) vscode/1.95.0', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (win32; x64; 10.0.22631) vscode/1.94.2', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.293.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (win32; x64; 10.0.22631) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.26100) cursor/1.93.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.272.1 (win32; x64; 10.0.22631) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.82.3', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.10.12-200.fc40.x86_64) vscode/1.95.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.8.0-48-generic) vscode/1.95.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/1.1.1 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 5.15.0-122-generic) vscode/1.95.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.285.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-48-generic) vscode/1.95.2', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.5.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (win32; x64; 10.0.19045) vscode/1.95.0', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.1.0) vscode/1.95.0', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.290.0 (darwin; arm64; 23.5.0) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.288.1 (darwin; x64; 24.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 24.2.0) vscode/1.93.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.4.0) vscode/1.95.0', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.11.7-arch1-1) vscode/1.95.2', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.91.1', 'augment.intellij/0.39.0 JetBrains Rider/2024.2.7', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.22631) vscode/1.95.0', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1066-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.153.1-microsoft-standard-WSL2) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.11.3-arch1-1) vscode/1.91.1', 'Augment.vscode-augment/0.262.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.93.1', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'augment.intellij/0.51.0 WebStorm/2024.3 EAP', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.0.0) vscode/1.95.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.5.0-1023-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.1.0) vscode/1.94.0', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1071-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-48-generic) vscode/1.95.1', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.259.0 (win32; x64; 10.0.19045) vscode/1.92.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.286.0 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (darwin; x64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.293.0 (darwin; arm64; 24.0.0) vscode/1.95.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.291.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.19.17-coreweave) vscode/1.94.2', 'augment.intellij/0.58.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.4.0) vscode/1.95.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.1.0) vscode/1.95.2', 'augment.intellij/0.0.70 JetBrains Rider/2024.2', 'Augment.vscode-augment/0.292.0 (win32; x64; 10.0.22631) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.277.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.0.0) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 24.0.0) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.91.1', 'augment.intellij/0.51.0 RubyMine/2024.2.4', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.1.0) vscode/1.85.2', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.279.0 (darwin; arm64; 23.6.0) vscode/1.94.1', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 24.0.0) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.5.0) vscode/1.95.2', 'augment.intellij/0.39.0 RubyMine/2024.2.3', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.1.0) vscode/1.93.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.6.0) vscode/1.92.2', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'augment.intellij/0.61.0 CLion/2024.2.3', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 24.2.0) vscode/1.95.2', 'augment.intellij/0.56.0 JetBrains Rider/2024.2.7', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-40-generic) vscode/1.95.1', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.6.0) vscode/1.95.1', 'augment.intellij/0.56.0 WebStorm/2024.2.3', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.82.3', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 22.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.278.0 (linux; x64; 6.8.0-48-generic) vscode/1.95.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 24.1.0) vscode/1.95.0', 'Augment.vscode-augment/0.293.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.288.1 (linux; x64; 5.15.0-124-generic) vscode/1.85.2', 'Augment.vscode-augment/0.219.0 (darwin; arm64; 23.4.0) vscode/1.92.0', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.6.0) cursor/1.93.1', 'augment.intellij/0.56.0 JetBrains Rider/2024.2.5', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.291.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.293.0 (darwin; arm64; 23.3.0) vscode/1.89.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 22.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.285.0 (linux; x64; 6.8.0-40-generic) vscode/1.95.1', 'Augment.vscode-augment/0.281.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (win32; x64; 10.0.22621) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.1.0) vscode/1.94.0', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.94.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-1016-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.5.0) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.0.0) vscode/1.95.2', 'Augment.vscode-augment/0.243.2 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.10.9-200.fc40.x86_64) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.5.0) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.288.1 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.279.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.91.1', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.22631) cursor/1.93.1', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.19.17-coreweave) vscode/1.94.2', 'augment.intellij/0.39.0 PyCharm/2024.1.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.2.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 24.0.0) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'augment.intellij/0.62.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.6.0) vscode/1.95.0', 'Augment.vscode-augment/0.287.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.89.1', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.0.0) vscode/1.95.2', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 22.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.1 (darwin; x64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.293.0 (linux; x64; 6.1.100+) vscode/1.91.1', 'Augment.vscode-augment/0.278.0 (win32; x64; 10.0.22631) vscode/1.95.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.8.0-40-generic) vscode/1.95.1', 'Augment.vscode-augment/0.278.0 (darwin; arm64; 24.0.0) vscode/1.95.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.289.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.86.2', 'Augment.vscode-augment/0.282.1 (win32; x64; 10.0.26100) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.0.0) vscode/1.90.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.4.0) vscode/1.92.2', 'Augment.vscode-augment/0.282.0 (win32; x64; 10.0.22621) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 23.6.0) vscode/1.94.0', 'augment.intellij/0.61.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.282.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.0', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.0.0) vscode/1.92.2', 'augment.intellij/0.55.0 PyCharm/2024.2.3', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.1.0) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 24.1.0) vscode/1.95.1', 'augment.intellij/0.60.0 PyCharm/2024.2.4', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.15.0-1067-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.293.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'augment.intellij/0.39.0 GoLand/2024.1.2', 'Augment.vscode-augment/0.291.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.280.0 (linux; x64; 5.19.17-coreweave) vscode/1.95.0', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.287.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.0.3141592 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.1', 'augment.intellij/0.51.0 PyCharm/2024.2.3', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 24.0.0) vscode/1.95.1', 'Augment.vscode-augment/0.287.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.285.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.8.0-47-generic) vscode/1.92.2', 'Augment.vscode-augment/0.293.0 (darwin; arm64; 23.5.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.2.0-39-generic) vscode/1.95.1', 'augment.intellij/0.51.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.0.3141592 (darwin; arm64; 23.6.0) vscode/1.93.1', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.258.0 (win32; x64; 10.0.26100) vscode/1.95.0', 'augment.intellij/0.60.0 IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 24.2.0) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.293.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.94.0', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.92.0', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'Augment.vscode-augment/0.279.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.287.0 (win32; x64; 10.0.22631) vscode/1.95.1', 'augment.intellij/0.51.0 WebStorm/2024.2.4', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.5.0) vscode/1.94.2', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.4.0) vscode/1.89.1', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.6.0) vscode/1.90.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.8.0-1017-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.1.100+) vscode/1.94.0', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.5.0-1023-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.276.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 6.1.100+) vscode/1.82.3', 'augment.intellij/0.39.0 WebStorm/2024.2.0.1', 'augment.intellij/0.61.0 GoLand/2024.3 RC', 'Augment.vscode-augment/0.277.0 (linux; x64; 6.8.0-40-generic) vscode/1.94.2', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.1.100+) vscode/1.95.0', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 24.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.1.100+) vscode/1.91.1', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.6.0) vscode-insiders/1.96.0-insider', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.6.0) vscode/1.95.2', 'Augment.vscode-augment/0.288.0 (win32; x64; 10.0.22621) vscode/1.95.0', 'Augment.vscode-augment/0.279.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.280.0 (linux; x64; 6.1.100+) vscode/1.93.1', 'augment.intellij/0.39.0 IntelliJ IDEA/2024.1.4', 'Augment.vscode-augment/0.279.0 (win32; x64; 10.0.22631) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.289.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.283.0 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.282.1 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.282.0 (darwin; arm64; 23.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.266.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.153.1-microsoft-standard-WSL2) vscode/1.95.2', 'Augment.vscode-augment/0.284.0 (linux; x64; 6.8.0-1015-gcp) vscode/1.92.0', 'Augment.vscode-augment/0.281.0 (darwin; x64; 22.6.0) vscode/1.94.0', 'augment.intellij/1.0.0-snapshot IntelliJ IDEA/2024.2.4', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 24.0.0) vscode/1.95.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.4.0) vscode/1.95.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.292.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.277.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1068-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.4.0) cursor/1.93.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.1.100+) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (linux; x64; 5.10.0-33-cloud-amd64) vscode/1.95.1', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 23.6.0) vscode/1.95.1', 'Augment.vscode-augment/0.277.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.272.1 (darwin; arm64; 23.4.0) vscode/1.92.2', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 24.1.0) vscode/1.95.2', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 24.0.0) vscode/1.95.1', 'Augment.vscode-augment/0.277.1 (linux; x64; 5.15.0-124-generic) vscode/1.85.2', 'Augment.vscode-augment/0.290.0 (linux; x64; 5.15.0-1065-gcp) vscode/1.95.2', 'Augment.vscode-augment/0.282.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.1', 'Augment.vscode-augment/0.288.0 (linux; x64; 6.1.100+) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.277.1 (win32; x64; 10.0.22631) vscode/1.95.1', 'Augment.vscode-augment/0.285.0 (linux; x64; 5.15.0-1062-gcp) vscode/1.94.2', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 22.6.0) vscode/1.94.2', 'Augment.vscode-augment/0.284.0 (linux; x64; 5.15.0-1070-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.282.0 (darwin; x64; 23.5.0) vscode/1.88.0', 'Augment.vscode-augment/0.282.1 (linux; x64; 6.8.0-48-generic) vscode/1.95.1', 'Augment.vscode-augment/0.283.0 (linux; x64; 6.8.0-1016-gcp) vscode/1.95.0', 'Augment.vscode-augment/0.282.1 (darwin; arm64; 23.6.0) vscode/1.92.2', 'augment.intellij/0.51.0 CLion/2024.2.3', 'Augment.vscode-augment/0.288.0 (darwin; arm64; 23.4.0) vscode/1.94.2', 'Augment.vscode-augment/0.283.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.91.1', 'Augment.vscode-augment/0.284.0 (darwin; arm64; 24.1.0) vscode/1.94.2', 'Augment.vscode-augment/0.286.0 (linux; x64; 5.15.0-1069-gcp) vscode/1.92.1', 'Augment.vscode-augment/0.290.0 (linux; x64; 6.1.100+) vscode/1.94.2', 'Augment.vscode-augment/0.288.1 (darwin; arm64; 24.1.0) cursor/1.93.1', 'Augment.vscode-augment/0.277.1 (darwin; arm64; 23.6.0) vscode/1.94.0', 'Augment.vscode-augment/0.281.0 (darwin; arm64; 23.5.0) vscode/1.92.2', 'Augment.vscode-augment/0.284.0 (win32; x64; 10.0.19044) vscode/1.95.0']\n"]}], "source": ["all_models = list([x[\"response_model\"] for x in all_data])\n", "agents = list(set([x[\"user_agent\"] for x in all_data]))\n", "print(f\"unique models: {set(all_models)}\")\n", "print(f\"agents: {agents}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["num_ok_agents: 7115 / 167894, ratio: 0.04237792893134954\n"]}], "source": ["num_ok_agents = 0\n", "data_w_ok_agent = []\n", "for x in filtered_data:\n", "    if (x[\"user_agent_obj\"].agent_type.value == AgentType.VSCODE.value) and (\n", "        x[\"user_agent_obj\"].version[1] >= 292\n", "    ):\n", "        num_ok_agents += 1\n", "        data_w_ok_agent.append(x)\n", "print(\n", "    f\"num_ok_agents: {num_ok_agents} / {len(filtered_data)}, ratio: {num_ok_agents / len(filtered_data)}\"\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 433 / 15306 data with skip token.\n", "There are 189 / 433 accepted data with skip token.\n", "----------------------------------------------------------------------------------------------------\n", "There are 135 / 7115 data with skip token.\n", "There are 38 / 135 accepted data with skip token.\n"]}], "source": ["inspect_skip_tokens(all_data_w_intellij, special_tokens.skip)\n", "print(\"-\" * 100)\n", "inspect_skip_tokens(data_w_ok_agent, special_tokens.skip)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "True\n"]}], "source": ["# x[\"user_agent_obj\"].version\n", "print(x[\"user_agent_obj\"].agent_type.value)\n", "print(x[\"user_agent_obj\"].agent_type.value == AgentType.VSCODE.value)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}