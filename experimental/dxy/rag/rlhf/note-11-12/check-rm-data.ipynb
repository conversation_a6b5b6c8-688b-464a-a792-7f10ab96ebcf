{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.rag.rlhf.data.load_data_lib import (\n", "    load_data_as_dataset,\n", "    UserEventData,\n", "    SC2_ELDEN_MODELS,\n", ")\n", "from experimental.dxy.rag.rlhf.shared_lib import str2datetimefloat\n", "import pathlib\n", "import collections\n", "\n", "all_user_data = load_data_as_dataset(\n", "    date_ranges=[\"20241117-20241119\"],\n", "    input_dir=pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["user_data_v1 = all_user_data.filter_ar_by_user_agent()\n", "print(\"-\" * 100)\n", "user_data_v2 = user_data_v1.filter_ar_by_duplication_request_id()\n", "print(\"-\" * 100)\n", "user_data_v3 = user_data_v2.filter_hs_by_missing_request_id_and_identical_response()\n", "print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["source2num = collections.defaultdict(int)\n", "converted_data: list[dict] = []\n", "for data in user_data_v3.ar_data:\n", "    for cx in user_data_v3.convert_ar_data_to_reward_data(data):\n", "        converted_data.append(cx)\n", "        source2num[cx[\"source\"]] += 1\n", "for source, num in source2num.items():\n", "    print(f\"{source}: {num}\")\n", "print(f\"There are {len(converted_data)} converted data.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# index = ok_request_id.values().__iter__().__next__()\n", "# print(f\"index: {index}\")\n", "# cur_ar_data = user_data_v3.ar_data[index]\n", "# cur_ar_request_id = cur_ar_data[\"request_id\"]\n", "# cur_ar_timestamp = cur_ar_data[\"timestamp\"]\n", "# cur_ue_index = user_data_v3.request_id_to_index_ue[cur_ar_request_id][0]\n", "# cur_ar_time = user_data_v3.ue_data[cur_ue_index][\"time\"]\n", "# print(f\"cur_ar_request_id: {cur_ar_request_id}\")\n", "# print(f\"cur_ar_timestamp: {cur_ar_timestamp}\")\n", "# print(f\"cur_ar_timestamp (float): {str2datetimefloat(cur_ar_timestamp)}\")\n", "# print(f\"cur_ar_time: {cur_ar_time}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}