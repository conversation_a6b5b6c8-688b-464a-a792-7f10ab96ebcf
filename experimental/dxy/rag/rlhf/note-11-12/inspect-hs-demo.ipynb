{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 6557/6557 [00:00<00:00, 1086736.92it/s]\n", "100%|██████████| 6557/6557 [00:00<00:00, 1040995.17it/s]\n"]}], "source": ["from research.core import utils_for_str, utils_for_file\n", "from research.eval.harness.tasks.hindsight import HindsightOutput\n", "from experimental.dxy.rag.rlhf.shared_lib import create_hindsight_eval_tokens\n", "\n", "hs_results_v3 = list(\n", "    create_hindsight_eval_tokens(\n", "        \"/mnt/efs/augment/eval/jobs/SCfooXJT/000_starcoder2_fastforward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "    )\n", ")\n", "hs_results_v7 = list(\n", "    create_hindsight_eval_tokens(\n", "        \"/mnt/efs/augment/eval/jobs/g45iedDt/000_starcoder2_fastforward_HindsightCompletionTask_completed_patches.jsonl.zst\"\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1371 / 6557 diffs.\n"]}], "source": ["indexes_diff = []\n", "for index in range(len(hs_results_v3)):\n", "    if (\n", "        utils_for_str.get_first_n_lines(\n", "            hs_results_v3[index][\"raw_obj\"].generation, 1\n", "        ).strip()\n", "        != utils_for_str.get_first_n_lines(\n", "            hs_results_v7[index][\"raw_obj\"].generation, 1\n", "        ).strip()\n", "    ):\n", "        indexes_diff.append(index)\n", "print(f\"There are {len(indexes_diff)} / {len(hs_results_v3)} diffs.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "Path:  services/inference_host/server/continuous_batching/generation_state.py\n", "Path:  services/inference_host/server/continuous_batching/generation_state.py\n", "----------------------------------------------------------------------------------------------------\n", "Request ID:  2be9a3de-15da-4948-96e9-fb2a40e2baa2\n", "Request ID:  2be9a3de-15da-4948-96e9-fb2a40e2baa2\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[92m\n", "    def _num_tokens_generated(self, model_id: ModelID) -> int:\n", "        # We add 1 to the end index because once we process a token we are able\n", "        # to generate the next token.\n", "        return self.num_processed_tokens[model_id] + 1 - self.prompt_length\n", "\n", "    def _get_target_tokens(\n", "        self, model_id: ModelID, next_num_tokens: int\n", "    ) -> Sequence[int] | None:\n", "        if model_id != self.main_model_id:\n", "            return None\n", "        if self.target_tokens is None:\n", "            return None\n", "        target_token_start = self._num_tokens_generated(model_id)\n", "        return self.target_tokens[  # TODO\u001b[0m\u001b[47m\u001b[30m(<PERSON><PERSON><PERSON>): this is a hack to make the code work\u001b[0m\u001b[94m\n", "            target_token_start : target_token_start + next_num_tokens\n", "        ]\n", "\n", "    def is_completed(self, model_id: ModelID | None = None) -> bool:\n", "        \"\"\"Returns whether the generation is done.\n", "\n", "        Args:\n", "            model_id (Optional[ModelID], optional): The model to check.\n", "                If not provided, the main model is used.\n", "                `model_id` is supposed to be provided only when we call\n", "                method from within the class!\n", "\n", "        Returns:\n", "            bool: Whether the generation is done.\n", "\u001b[0m----------------------------------------------------------------------------------------------------\n", "\u001b[92m\n", "    def _num_tokens_generated(self, model_id: ModelID) -> int:\n", "        # We add 1 to the end index because once we process a token we are able\n", "        # to generate the next token.\n", "        return self.num_processed_tokens[model_id] + 1 - self.prompt_length\n", "\n", "    def _get_target_tokens(\n", "        self, model_id: ModelID, next_num_tokens: int\n", "    ) -> Sequence[int] | None:\n", "        if model_id != self.main_model_id:\n", "            return None\n", "        if self.target_tokens is None:\n", "            return None\n", "        target_token_start = self._num_tokens_generated(model_id)\n", "        return self.target_tokens[  # TODO\u001b[0m\u001b[47m\u001b[30m(<PERSON><PERSON><PERSON>): check if this is correct\u001b[0m\u001b[94m\n", "            target_token_start : target_token_start + next_num_tokens\n", "        ]\n", "\n", "    def is_completed(self, model_id: ModelID | None = None) -> bool:\n", "        \"\"\"Returns whether the generation is done.\n", "\n", "        Args:\n", "            model_id (Optional[ModelID], optional): The model to check.\n", "                If not provided, the main model is used.\n", "                `model_id` is supposed to be provided only when we call\n", "                method from within the class!\n", "\n", "        Returns:\n", "            bool: Whether the generation is done.\n", "\u001b[0m----------------------------------------------------------------------------------------------------\n", "\u001b[92m\n", "    def _num_tokens_generated(self, model_id: ModelID) -> int:\n", "        # We add 1 to the end index because once we process a token we are able\n", "        # to generate the next token.\n", "        return self.num_processed_tokens[model_id] + 1 - self.prompt_length\n", "\n", "    def _get_target_tokens(\n", "        self, model_id: ModelID, next_num_tokens: int\n", "    ) -> Sequence[int] | None:\n", "        if model_id != self.main_model_id:\n", "            return None\n", "        if self.target_tokens is None:\n", "            return None\n", "        target_token_start = self._num_tokens_generated(model_id)\n", "        return self.target_tokens[  # TODO\u001b[0m\u001b[47m\u001b[30m(markus): what happens if we reach the end of the sequence? Do we need to append an EOS token?\u001b[0m\u001b[94m\n", "            target_token_start : target_token_start + next_num_tokens\n", "        ]\n", "\n", "    def is_completed(self, model_id: ModelID | None = None) -> bool:\n", "        \"\"\"Returns whether the generation is done.\n", "\n", "        Args:\n", "            model_id (Optional[ModelID], optional): The model to check.\n", "                If not provided, the main model is used.\n", "                `model_id` is supposed to be provided only when we call\n", "                method from within the class!\n", "\n", "        Returns:\n", "            bool: Whether the generation is done.\n", "\u001b[0m"]}], "source": ["# index = indexes_diff[2]\n", "index = indexes_diff[377]\n", "data_a = hs_results_v3[index][\"raw_obj\"]\n", "data_b = hs_results_v7[index][\"raw_obj\"]\n", "\n", "print(\"-\" * 100)\n", "print(\"Path: \", data_a.path)\n", "print(\"Path: \", data_b.path)\n", "print(\"-\" * 100)\n", "print(\"Request ID: \", data_a.request_id)\n", "print(\"Request ID: \", data_b.request_id)\n", "print(\"-\" * 100)\n", "utils_for_str.show_completion(\n", "    data_a.prefix,\n", "    data_a.suffix,\n", "    data_a.generation,\n", "    max_lines=15,\n", ")\n", "print(\"-\" * 100)\n", "utils_for_str.show_completion(\n", "    data_b.prefix,\n", "    data_b.suffix,\n", "    data_b.generation,\n", "    max_lines=15,\n", ")\n", "print(\"-\" * 100)\n", "utils_for_str.show_completion(\n", "    data_b.prefix,\n", "    data_b.suffix,\n", "    data_b.ground_truth,\n", "    max_lines=15,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}