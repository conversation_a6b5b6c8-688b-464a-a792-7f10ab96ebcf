{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import tqdm\n", "import collections\n", "from datasets import Dataset as HFDataset\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "\n", "# sc2_tokenizer = StarCoder2Tokenizer()\n", "# sc2_special_tokens = sc2_tokenizer.special_tokens\n", "qwen_tokenizer = Qwen25CoderTokenizer()\n", "qwen_special_tokens = qwen_tokenizer.special_tokens\n", "\n", "\n", "# Check the labels\n", "def check_labels(dataset: HFDataset, special_tokens):\n", "    label2num = collections.defaultdict(int)\n", "    str2num = collections.defaultdict(int)\n", "    source_model2num = collections.defaultdict(int)\n", "    for data in tqdm.tqdm(dataset, total=len(dataset), desc=\"Count labels\"):\n", "        assert isinstance(data, dict)\n", "        target_tokens = data[\"target_tokens\"]\n", "        assert len(data[\"labels\"]) == 1\n", "        label = data[\"labels\"][0]\n", "        label2num[label] += 1\n", "        if target_tokens[-1] == special_tokens.eos:\n", "            str2num[\"eos\"] += 1\n", "        elif target_tokens[-1] == special_tokens.pause:\n", "            str2num[\"pause\"] += 1\n", "        else:\n", "            str2num[\"not_eos_pause\"] += 1\n", "        if \"response_models\" in data:\n", "            response_model = data[\"response_models\"]\n", "            response_model = [chr(x) for x in response_model]\n", "            response_model = \"\".join(response_model)\n", "            source_model2num[response_model] += 1\n", "    print(\"-\" * 32)\n", "    for label in sorted(list(label2num.keys())):\n", "        num = label2num[label]\n", "        print(f\"{label=}: {num:6d}\")\n", "    print(\"-\" * 32)\n", "    for str_ in sorted(list(str2num.keys())):\n", "        num = str2num[str_]\n", "        print(f\"{str_:15s}: {num:6d}\")\n", "    print(\"-\" * 32)\n", "    for model in sorted(list(source_model2num.keys())):\n", "        num = source_model2num[model]\n", "        print(f\"{model:15s}: {num:6d}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check HuggingFace Dataset\n", "# dataset_train = HFDataset.load_from_disk(\n", "#     \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-20241117\"\n", "# )\n", "# dataset_valid = HFDataset.load_from_disk(\n", "#     \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241118-20241122\"\n", "# )\n", "dataset_train = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1130-QWEN\"\n", ")\n", "dataset_valid = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN\"\n", ")\n", "dataset_cceval = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train\"\n", ")\n", "dataset_sft = HFDataset.load_from_disk(\n", "    \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/sft-train\"\n", ")\n", "print(f\"There are {len(dataset_train)} train items.\")\n", "print(f\"There are {len(dataset_valid)} valid items.\")\n", "print(f\"There are {len(dataset_cceval)} cceval train items.\")\n", "print(f\"There are {len(dataset_sft)} sft train items.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------------------------------------------------TRAIN------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Count labels: 100%|██████████| 20364/20364 [00:22<00:00, 914.29it/s] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["--------------------------------\n", "label=0:   4520\n", "label=1:   1060\n", "label=2:   1152\n", "label=3:   1193\n", "label=4:  10129\n", "label=5:   2310\n", "--------------------------------\n", "eos            :  17532\n", "not_eos_pause  :    165\n", "pause          :   2667\n", "--------------------------------\n", "eldenv4-0c-15b :  12134\n", "eldenv7-0-15b  :    408\n", "qweldenv1-14b  :   7822\n", "------------------------------------------------VALID------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Count labels: 100%|██████████| 861432/861432 [14:50<00:00, 967.35it/s] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["--------------------------------\n", "label=0: 167458\n", "label=1:  46368\n", "label=2:  35864\n", "label=3:  86531\n", "label=4: 408655\n", "label=5: 116556\n", "--------------------------------\n", "eos            : 733832\n", "not_eos_pause  :  11226\n", "pause          : 116374\n", "--------------------------------\n", "eldenv3-15b    : 108450\n", "eldenv4-0c-15b : 373118\n", "eldenv4-0f-15b :  42150\n", "eldenv4-1-15b  :    583\n", "eldenv4-15b    :  56643\n", "eldenv4-2-15b  :    138\n", "eldenv4-3-15b  : 158493\n", "eldenv4-4b-15b :  27278\n", "eldenv6-1-15b  :      6\n", "eldenv7-0-15b  :  68877\n", "qweldenv1-14b  :  25696\n", "------------------------------------------------CCEVAL------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Count labels: 100%|██████████| 9711/9711 [00:07<00:00, 1275.67it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["--------------------------------\n", "label=6:   9711\n", "--------------------------------\n", "eos            :   9711\n", "--------------------------------\n", "------------------------------------------------ SFT ------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Count labels: 100%|██████████| 99938/99938 [01:41<00:00, 984.72it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["--------------------------------\n", "label=6:  99938\n", "--------------------------------\n", "eos            :  87559\n", "pause          :  12379\n", "--------------------------------\n", "------------------------------------------------------------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["print(\"-\" * 48 + \"TRAIN\" + \"-\" * 48)\n", "check_labels(dataset_valid, qwen_special_tokens)\n", "print(\"-\" * 48 + \"VALID\" + \"-\" * 48)\n", "check_labels(dataset_train, qwen_special_tokens)\n", "print(\"-\" * 48 + \"CCEVAL\" + \"-\" * 48)\n", "check_labels(dataset_cceval, qwen_special_tokens)\n", "print(\"-\" * 48 + \" SFT \" + \"-\" * 48)\n", "check_labels(dataset_sft, qwen_special_tokens)\n", "print(\"-\" * 48 + \"------\" + \"-\" * 48)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset as HFDataset\n", "from datasets import concatenate_datasets\n", "\n", "train_datasets = [\n", "    HFDataset.load_from_disk(\n", "        \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1124-QWEN\"\n", "    ),\n", "    HFDataset.load_from_disk(\n", "        \"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/cceval-train\"\n", "    ),\n", "]\n", "final_dataset = concatenate_datasets(train_datasets)\n", "print(f\"There are {len(final_dataset)} examples\")\n", "print(final_dataset[0].keys())\n", "print(final_dataset[-1].keys())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}