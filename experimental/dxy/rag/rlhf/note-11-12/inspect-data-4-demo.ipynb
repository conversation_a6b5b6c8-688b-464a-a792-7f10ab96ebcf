{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import collections\n", "import pathlib\n", "import random\n", "\n", "import numpy as np\n", "import tqdm\n", "from datasets import Dataset as HFDataset\n", "\n", "from base.datasets import completion, tenants\n", "from base.datasets.completion_dataset import CompletionDataset, CompletionDatum\n", "from base.prompt_format.common import PromptChunk\n", "from base.prompt_format_completion.prompt_formatter import PromptInput\n", "from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from base.tokenizers.tokenizer import RagSpecialTokens\n", "from experimental.dxy.rag.rlhf.shared_lib import (\n", "    traverse_date_range,\n", "    inspect_skip_tokens,\n", "    UserAgent,\n", "    AgentType,\n", "    AGENT_FIXING_SKIP,\n", ")\n", "from research.core import utils_for_file\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "special_tokens = tokenizer.special_tokens"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def load_data_fn(\n", "    date_ranges: list[str],\n", "    input_dir: pathlib.Path,\n", "):\n", "    \"\"\"Process the data as a dataset.\"\"\"\n", "    assert input_dir.exists(), input_dir\n", "    all_date_ranges = []\n", "    for cur_date_range in date_ranges:\n", "        start_date, end_date = cur_date_range.split(\"-\")\n", "        all_date_ranges.extend(traverse_date_range(start_date, end_date))\n", "    print(f\"There are {len(all_date_ranges)} date ranges.\")\n", "    accept_reject_files: list[pathlib.Path] = []\n", "    for date, xstart, xend in all_date_ranges:\n", "        accept_reject_dir = input_dir / f\"{date}-accept-reject\"\n", "        if accept_reject_dir.exists():\n", "            accept_reject_files.extend(list(accept_reject_dir.glob(\"*-samples.jsonl\")))\n", "        print(f\"{date}: {accept_reject_dir}\")\n", "    assert len(accept_reject_files) > 0, \"Can not find accept/reject files.\"\n", "    print(f\"There are {len(accept_reject_files)} accept/reject files\")\n", "    all_data: list[dict] = []\n", "    for accept_reject_file in tqdm.tqdm(\n", "        accept_reject_files, desc=\"Load accept/reject files\"\n", "    ):\n", "        all_data.extend(utils_for_file.read_jsonl(accept_reject_file))\n", "    return all_data"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 9 date ranges.\n", "20241001: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241001-accept-reject\n", "20241002: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241002-accept-reject\n", "20241003: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241003-accept-reject\n", "20241004: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241004-accept-reject\n", "20241110: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241110-accept-reject\n", "20241111: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241111-accept-reject\n", "20241112: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241112-accept-reject\n", "20241113: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241113-accept-reject\n", "20241114: /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114/20241114-accept-reject\n", "There are 30 accept/reject files\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Load accept/reject files: 100%|██████████| 30/30 [01:00<00:00,  2.03s/it]\n"]}], "source": ["all_data = load_data_fn(\n", "    date_ranges=[\"20241001-20241004\", \"20241110-20241114\"],\n", "    input_dir=pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114\"),\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 15985 / 112759 examples (V3).\n", "There are 18957 / 112759 examples (V7).\n"]}], "source": ["data_v3, data_v7 = [], []\n", "for x in all_data:\n", "    x[\"user_agent_obj\"] = UserAgent(x[\"user_agent\"])\n", "    if x[\"user_agent_obj\"].version == (-1, -1, -1):\n", "        continue\n", "    if x[\"response_model\"] == \"eldenv7-0-15b\":\n", "        data_v7.append(x)\n", "    elif x[\"response_model\"] == \"eldenv3-15b\":\n", "        data_v3.append(x)\n", "print(f\"There are {len(data_v3)} / {len(all_data)} examples (V3).\")\n", "print(f\"There are {len(data_v7)} / {len(all_data)} examples (V7).\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 1: 12155/15985 (ratio=76.04%) vs. 13985/18957 (ratio=73.77%)\n", " 2:  1388/15985 (ratio=8.68%) vs.  1904/18957 (ratio=10.04%)\n", " 3:   896/15985 (ratio=5.61%) vs.   951/18957 (ratio=5.02%)\n", " 4:   451/15985 (ratio=2.82%) vs.   618/18957 (ratio=3.26%)\n", " 5:   350/15985 (ratio=2.19%) vs.   445/18957 (ratio=2.35%)\n", " 6:   178/15985 (ratio=1.11%) vs.   251/18957 (ratio=1.32%)\n", " 7:   160/15985 (ratio=1.00%) vs.   221/18957 (ratio=1.17%)\n", " 8:   106/15985 (ratio=0.66%) vs.   147/18957 (ratio=0.78%)\n", " 9:    85/15985 (ratio=0.53%) vs.   140/18957 (ratio=0.74%)\n", "10:    61/15985 (ratio=0.38%) vs.    79/18957 (ratio=0.42%)\n", "11:    46/15985 (ratio=0.29%) vs.    65/18957 (ratio=0.34%)\n", "12:    27/15985 (ratio=0.17%) vs.    55/18957 (ratio=0.29%)\n", "13:    25/15985 (ratio=0.16%) vs.    32/18957 (ratio=0.17%)\n", "14:    21/15985 (ratio=0.13%) vs.    21/18957 (ratio=0.11%)\n", "15:    17/15985 (ratio=0.11%) vs.    12/18957 (ratio=0.06%)\n", "16:    11/15985 (ratio=0.07%) vs.     9/18957 (ratio=0.05%)\n", "17:     2/15985 (ratio=0.01%) vs.    11/18957 (ratio=0.06%)\n", "18:     1/15985 (ratio=0.01%) vs.     3/18957 (ratio=0.02%)\n", "19:     1/15985 (ratio=0.01%) vs.     4/18957 (ratio=0.02%)\n", "20:     0/15985 (ratio=0.00%) vs.     1/18957 (ratio=0.01%)\n", "21:     2/15985 (ratio=0.01%) vs.     0/18957 (ratio=0.00%)\n", "23:     1/15985 (ratio=0.01%) vs.     1/18957 (ratio=0.01%)\n", "26:     0/15985 (ratio=0.00%) vs.     1/18957 (ratio=0.01%)\n", "28:     1/15985 (ratio=0.01%) vs.     1/18957 (ratio=0.01%)\n"]}], "source": ["from collections import defaultdict\n", "\n", "data_by_numlines_v3 = defaultdict(list)\n", "data_by_numlines_v7 = defaultdict(list)\n", "for x in data_v3:\n", "    response = tokenizer.detokenize(x[\"generated_token_ids\"])\n", "    num_lines = len(response.splitlines())\n", "    data_by_numlines_v3[num_lines].append(x)\n", "for x in data_v7:\n", "    response = tokenizer.detokenize(x[\"generated_token_ids\"])\n", "    num_lines = len(response.splitlines())\n", "    data_by_numlines_v7[num_lines].append(x)\n", "all_keys = list(set(list(data_by_numlines_v3.keys()) + list(data_by_numlines_v7.keys())))\n", "all_keys.sort()\n", "for numlines in all_keys:\n", "    v3_ratio = len(data_by_numlines_v3[numlines]) / len(data_v3)\n", "    v3_str = f\"{len(data_by_numlines_v3[numlines]):5d}/{len(data_v3):5d} (ratio={v3_ratio:.2%})\"\n", "    v7_ratio = len(data_by_numlines_v7[numlines]) / len(data_v7)\n", "    v7_str = f\"{len(data_by_numlines_v7[numlines]):5d}/{len(data_v7):5d} (ratio={v7_ratio:.2%})\"\n", "    print(f\"{numlines:2d}: {v3_str} vs. {v7_str}\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 95 Python examples.\n", "Path:  research/prism/next_edit/common.py\n", "Request ID:  5057e14a-35be-4a29-8967-71cb49fc4e4b\n", "\u001b[92m    \"\"\"Create a closure that retrieves next edit responses for a given request ID.\"\"\"\n", "\n", "    @lru_cache(maxsize=1024)\n", "    def _get_next_edit_responses(request_id: str) -> list[dict]:\n", "        \"\"\"Get the next edit host response events for a given request ID.\"\"\"\n", "        for fetcher in fetchers:\n", "            prefix = f\"352a91ac7d4283558ccfbc094a527746/request/{request_id}/\"\n", "            event_blobs = list(bucket.list_blobs(prefix=prefix))\n", "            rst = [\n", "                blob_to_json(blob)[RESPONSE_EVENT_NAME]\n", "                for blob in event_blobs\n", "                if blob.name.split(\"/\")[-2] == camel_to_snake(RESPONSE_EVENT_NAME)\n", "            ]\n", "\n", "            events \u001b[0m\u001b[47m\u001b[30m= fetcher.get_request_events(\n", "                tenant_id=\"352a91ac7d4283558ccfbc094a527746\",\n", "                request_id=request_id,\n", "                request_event_names={camel_to_snake(RESPONSE_EVENT_NAME)},\n", "            )\u001b[0m\u001b[94m\n", "\n", "\n", "            if rst:\n", "                return rst\n", "        return []\n", "\n", "    return _get_next_edit_responses\n", "\u001b[0m"]}], "source": ["# Show the response and near prefix/suffix\n", "from research.core import utils_for_str\n", "\n", "index = 20\n", "python_data = [x for x in data_by_numlines_v7[5] if x[\"prompt_input\"][\"path\"].endswith(\".py\")]\n", "print(f\"There are {len(python_data)} Python examples.\")\n", "data = python_data[index]\n", "response = tokenizer.detokenize(data[\"generated_token_ids\"])\n", "\n", "print(\"Path: \", data[\"prompt_input\"][\"path\"])\n", "print(\"Request ID: \", data[\"request_id\"])\n", "utils_for_str.show_completion(\n", "    data[\"prompt_input\"][\"prefix\"],\n", "    data[\"prompt_input\"][\"suffix\"],\n", "    response,\n", "    max_lines=15,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}