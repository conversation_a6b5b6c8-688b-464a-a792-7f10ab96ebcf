"""Analyze the dumped tokens for the hindsight dataset.

python experimental/dxy/rag/rlhf/analyze_tokens.py
"""

import concurrent.futures as futures
import json
import pathlib

import numpy as np
import tqdm
import zstandard as zstd

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDatum,
)
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from research.core import utils_for_log
from research.eval.harness.systems.abs_system import <PERSON>mpletionResult


def trim_by_eos(tokens: list[int], eos: int):
    index = tokens.index(eos)
    return tokens[:index]


def extract_data(
    datum: HindsightCompletionDatum,
    output_argmax: CompletionResult,
    output_random: CompletionResult,
    tokenizer: StarCoder2Tokenizer,
) -> dict | None:
    """Extract the data."""
    assert output_argmax.generated_tokens is not None
    assert output_random.generated_tokens is not None
    if (tokenizer.special_tokens.skip in output_argmax.generated_tokens) or (
        tokenizer.special_tokens.skip in output_random.generated_tokens
    ):
        return None
    if tokenizer.special_tokens.eos not in output_argmax.generated_tokens:
        return None
    if tokenizer.special_tokens.eos not in output_random.generated_tokens:
        return None
    if datum.ground_truth != output_argmax.generated_text:
        ground_truth = datum.ground_truth
        imperfect_str = output_argmax.generated_text
        original_imperfect_tokens = trim_by_eos(
            output_argmax.generated_tokens, tokenizer.special_tokens.eos
        )
    elif datum.ground_truth != output_random.generated_text:
        ground_truth = datum.ground_truth
        imperfect_str = output_random.generated_text
        original_imperfect_tokens = trim_by_eos(
            output_random.generated_tokens, tokenizer.special_tokens.eos
        )
    else:
        return None
    positive_tokens = tokenizer.tokenize_safe(ground_truth)
    negative_tokens = tokenizer.tokenize_safe(imperfect_str)
    return {
        "prompt_tokens": output_argmax.prompt_tokens,
        "positive_tokens": positive_tokens,
        "negative_tokens": negative_tokens,
        "original_imperfect_tokens": original_imperfect_tokens,
    }


def process_single_line(
    line: str, tokenizer: StarCoder2Tokenizer, dump_path: str
) -> bool:
    """Decompress a line."""
    data = json.loads(line)
    assert "datum" in data and "blob_names" in data
    assert "output_argmax" in data and "output_random" in data
    datum = HindsightCompletionDatum.schema().load(data["datum"])
    output_argmax = CompletionResult.schema().load(data["output_argmax"])
    output_random = CompletionResult.schema().load(data["output_random"])
    # return {
    #     "datum": datum,
    #     "blob_names": data["blob_names"],
    #     "output_argmax": output_argmax,
    #     "output_random": output_random,
    # }
    result = extract_data(
        datum,
        output_argmax,
        output_random,
        tokenizer,
    )
    if result is not None:
        with open(dump_path, "w", encoding="utf-8") as f:
            json.dump(result, f)
            f.write("\n")
        return True
    else:
        return False


def deserialize(all_lines: list[str], max_threads: int, output_dir: str):
    """Deserialize the data via multiple threads."""
    pathlib.Path(output_dir).mkdir(parents=False, exist_ok=True)
    results = []
    total = len(all_lines)
    tokenizer = StarCoder2Tokenizer()
    print(f"Process {total} examples into {output_dir}")
    with futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        all_jobs = []
        for index, line in tqdm.tqdm(
            enumerate(all_lines), total=total, desc="Submit jobs"
        ):
            dump_path = f"{output_dir}/{index:04d}-{total:04d}.json"
            # process_single_line(line, tokenizer, dump_path)
            all_jobs.append(
                executor.submit(process_single_line, line, tokenizer, dump_path)
            )
        for job in tqdm.tqdm(
            futures.as_completed(all_jobs), total=total, desc="Decompress"
        ):
            results.append(job.result())
    return results


def analyze(input_folder: str) -> int:
    local_datum_path = f"{input_folder}/local-datum-w-output.jsonl.zst"
    all_lines = []
    with zstd.open(local_datum_path, "r", encoding="utf-8") as f:
        for line in f:
            all_lines.append(line)
    print(
        f"{utils_for_log.time_string()} Load {len(all_lines)} lines from {local_datum_path}."
    )
    output_dir = f"{input_folder}/token-data-noskip"
    all_results = deserialize(all_lines, max_threads=256, output_dir=output_dir)
    print(f"{utils_for_log.time_string()} Finish decompress {len(all_results)} lines")
    total = np.sum(all_results).item()
    print(f"Kept {total} pair of data :).\n")
    return total


def main():
    inputs = [
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-mercor",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard",
    ]
    total = 0
    for input_folder in inputs:
        cur_total = analyze(input_folder)
        total += cur_total
    print(f"{utils_for_log.time_string()} kept {total} examples.")


if __name__ == "__main__":
    main()
