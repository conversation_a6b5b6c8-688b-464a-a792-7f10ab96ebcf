"""Create the skip and pause tokens for the hindsight data.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto
CUDA_VISIBLE_DEVICES=1 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing
CUDA_VISIBLE_DEVICES=2 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood

CUDA_VISIBLE_DEVICES=3 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto
CUDA_VISIBLE_DEVICES=0 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing
CUDA_VISIBLE_DEVICES=1 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood
CUDA_VISIBLE_DEVICES=2 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard

CUDA_VISIBLE_DEVICES=3 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing
CUDA_VISIBLE_DEVICES=4 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood
CUDA_VISIBLE_DEVICES=5 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard

CUDA_VISIBLE_DEVICES=1 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-08-2024-09/aitutor-turing
CUDA_VISIBLE_DEVICES=7 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-08-2024-09/dogfood
CUDA_VISIBLE_DEVICES=6 python experimental/dxy/rag/rlhf/create-skip-pause-tokens.py --input /mnt/efs/augment/user/dxy/hindsight/2024-08-2024-09/dogfood-shard
"""

import argparse
import concurrent.futures as futures
import copy
import json
import pathlib

import tqdm
import zstandard as zstd

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDatum,
)
from base.prompt_format.common import PromptChunk
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.local_content_manager import (
    LocalContentManager,
)
from experimental.dxy.rag.rlhf.shared_lib import (
    create_system,
    extract_tokens_before_stop_tokens,
)
from research.core import utils_for_log
from research.core.model_input import ModelInput
from research.core.recency_info import convert_from_datasets_recency_info
from research.eval.harness.systems.abs_system import CompletionResult
from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem
from research.models.meta_model import GenerationOptions


def deserialize_single_line(line: str):
    """Decompress a line."""
    data = json.loads(line)
    assert "datum" in data and "blob_names" in data
    return {
        "datum": HindsightCompletionDatum.schema().load(data["datum"]),
        "blob_names": data["blob_names"],
    }


def deserialize(
    all_lines: list[str], max_threads: int = 128, deserialize_fn=None
) -> list[dict]:
    """Deserialize the data via multiple threads."""
    results = []
    total = len(all_lines)
    if deserialize_fn is None:
        deserialize_fn = deserialize_single_line
    with futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        all_jobs = []
        for line in tqdm.tqdm(all_lines, total=total, desc="Submit jobs"):
            all_jobs.append(executor.submit(deserialize_fn, line))
        for job in tqdm.tqdm(
            futures.as_completed(all_jobs), total=total, desc="Decompress"
        ):
            results.append(job.result())
    return results


def deduplicate(all_datum: list[dict]) -> list[dict]:
    """Deduplicate the data."""
    dedup_datum = []
    for datum_docids in tqdm.tqdm(all_datum, desc="Deduplicate"):
        datum = datum_docids["datum"]
        assert isinstance(datum, HindsightCompletionDatum)
        if datum.completion.request_id not in {
            d["datum"].completion.request_id for d in dedup_datum
        }:
            dedup_datum.append(datum_docids)
    print(
        f"{utils_for_log.time_string()} Finish deduplication from {len(all_datum)} to {len(dedup_datum)} examples."
    )
    return dedup_datum


def get_recency_chunks(datum: HindsightCompletionDatum) -> list[PromptChunk]:
    """Get the recency chunks."""
    chunks = []
    for chunk in datum.completion.response.retrieved_chunks:
        if chunk.origin == "recency_retriever":
            chunks.append(
                PromptChunk(
                    text=chunk.text,
                    path=chunk.path,
                    unique_id=None,
                    origin=chunk.origin,
                    char_start=chunk.crange.start,
                    char_end=chunk.crange.stop,
                    blob_name=chunk.blob_name,
                )
            )
    return chunks


def split_list_by_value(lst, delimiter):
    result, current = [], []
    for value in lst:
        if value == delimiter:
            if current:  # This check prevents adding empty sublists
                result.append(current)
                current = []
        else:
            current.append(value)
    if current:  # Add the last collected group if not empty
        result.append(current)
    return result


# fmt: off
def parse_ground_truth_with_pause(ground_truth: str, tokenizer: StarCoder2Tokenizer, prompt_tokens: list[int], model) -> list[int]:
    """Parse the ground truth to tokens (with pause tokens). No EOD at the end."""
    lines = ground_truth.splitlines()
    tokens_per_line = [tokenizer.tokenize_safe(line) for line in lines]
    tokens: list[int] = []
    debug_tokens = []
    for idx, line_tokens in enumerate(tokens_per_line):
        if idx + 1 == len(tokens_per_line):  # last line
            tokens.extend(line_tokens)
        else:
            if len(prompt_tokens + tokens) + 1 > model.seq_length:
                tokens.extend(line_tokens)
                print(f"{utils_for_log.time_string()} Stop parsing ground truth due to over length.")
                break
            output = model.raw_generate_tokens(
                prompt_tokens=prompt_tokens + tokens,
                options=GenerationOptions(temperature=0.0, max_generated_tokens=1),
            )
            output_tokens = output.tokens
            output_prob = output.logits[0].softmax(dim=-1).cpu().numpy()
            debug_tokens.append((prompt_tokens + tokens, output_tokens, output_prob))
            assert len(output_tokens) == 1, f"{output_tokens=}"
            # NOTE(Xuanyi): a bit more aggressive to use less pause tokens
            if (output_tokens[0] == tokenizer.special_tokens.pause and output_prob[tokenizer.special_tokens.pause] >= 0.4):
                tokens.extend(line_tokens + [tokenizer.special_tokens.pause])
            else:
                tokens.extend(line_tokens)
            tokens.append(tokenizer.special_tokens.newline)
    # Repack tokens, as we split lines by "\n" -- a newline token, but it might merge with other char together as a new token
    tokens_split_by_pause = split_list_by_value(tokens, tokenizer.special_tokens.pause)
    new_tokens = []
    for idx, cur_tokens in enumerate(tokens_split_by_pause):
        cur_new_tokens = tokenizer.tokenize_safe(tokenizer.detokenize(cur_tokens))
        if idx + 1 == len(tokens_split_by_pause):  # last line
            new_tokens.extend(cur_new_tokens)
        else:
            new_tokens.extend(cur_new_tokens + [tokenizer.special_tokens.pause])
    return tokens
# fmt: on


def parse_ground_truth_with_skip(
    ground_truth_tokens_w_pause: list[int],
    tokenizer: StarCoder2Tokenizer,
    prompt_tokens: list[int],
    suffix: str,
    model,
) -> list[int]:
    """Parse the ground truth to tokens (with skip tokens). No EOD at the end."""
    if not suffix:
        return ground_truth_tokens_w_pause
    # closing_strings: Sequence[str] = ("'''", '"""', "'", '"', "`", "]", "}", ")", ">")
    closing_strings = ("'", '"', "`", "]", "}", ")", ">")
    if suffix[0] not in closing_strings:
        return ground_truth_tokens_w_pause
    if len(prompt_tokens + ground_truth_tokens_w_pause) + 1 > model.seq_length:
        return ground_truth_tokens_w_pause
    output_tokens = model.raw_generate_tokens(
        prompt_tokens=prompt_tokens + ground_truth_tokens_w_pause,
        options=GenerationOptions(temperature=0.0, max_generated_tokens=1),
    ).tokens
    assert len(output_tokens) == 1, f"{output_tokens=}"
    if output_tokens[0] != tokenizer.special_tokens.skip:
        return ground_truth_tokens_w_pause
    # Try to generate the skip tokens
    output_tokens = model.raw_generate_tokens(
        prompt_tokens=prompt_tokens + ground_truth_tokens_w_pause,
        options=GenerationOptions(
            temperature=0.0,
            max_generated_tokens=256,
            stop_tokens=[
                tokenizer.special_tokens.eos,
                tokenizer.special_tokens.padding,
            ],
        ),
    ).tokens
    output_tokens = extract_tokens_before_stop_tokens(
        output_tokens, [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding]
    )
    return ground_truth_tokens_w_pause + output_tokens


def process_hindsight(input_folder: pathlib.Path, system: ProdEldenSystem):
    """Process the hindsight data."""

    local_datum_path = input_folder / "local-datum.jsonl.zst"
    assert local_datum_path.exists()
    print(f"{utils_for_log.time_string()} Processing {input_folder}")
    all_lines = []
    with zstd.open(local_datum_path, "r", encoding="utf-8") as f:
        for line in f:
            all_lines.append(line)
    print(f"{utils_for_log.time_string()} Load {len(all_lines)} lines from {local_datum_path}.")  # fmt: off
    all_datum = deserialize(all_lines)
    print(f"{utils_for_log.time_string()} Finish decompress {len(all_datum)} lines")  # fmt: off
    all_datum = deduplicate(all_datum)
    assert len(all_datum)
    blob_manager = LocalContentManager()
    blob_manager.load(input_folder / "local-blobs")

    save_dir = input_folder / "token-data-w-skip-pause"
    save_dir.mkdir(exist_ok=True, parents=False)

    # Load all the documents
    system.clear_retriever()
    system.add_docs(blob_manager.to_documents())
    gen_options_argmax = copy.deepcopy(system.generation_options)
    gen_options_random = copy.deepcopy(system.generation_options)
    gen_options_random.temperature = 1.1
    gen_options_random.top_p = 0.75

    # Special tokens
    tokenizer = system.tokenizer
    assert isinstance(tokenizer, StarCoder2Tokenizer)
    # pause_token = tokenizer.special_tokens.pause
    # skip_token = tokenizer.special_tokens.skip

    for idx, datum_docids in enumerate(all_datum):
        datum: HindsightCompletionDatum = datum_docids["datum"]
        ground_truth = datum.ground_truth
        raw_ground_truth_tokens = tokenizer.tokenize_safe(ground_truth)
        if len(raw_ground_truth_tokens) > 512:
            print(f"Skip {idx} due to long ground truth ({len(raw_ground_truth_tokens)}).")  # fmt: off
            continue
        save_path = save_dir / f"{idx:04d}-{len(all_datum):04d}.json"
        if save_path.exists():
            print(f"Skip {idx} due to existing file.")
            continue
        blob_names: list[str] = datum_docids["blob_names"]
        # recency_chunks = get_recency_chunks(datum)
        if datum.completion.request.recency_info is None:
            recency_info = None
        else:
            recency_info = convert_from_datasets_recency_info(datum.completion.request.recency_info)  # fmt: off

        # Build a model input
        model_input = ModelInput(
            prefix=datum.completion.request.prefix,
            suffix=datum.completion.request.suffix,
            path=datum.completion.request.path,
            target=None,
            doc_ids=blob_names,
            recency_info=recency_info,
        )
        system.generation_options = gen_options_argmax
        output_argmax = system.generate(model_input)
        assert output_argmax.generated_tokens is not None
        prompt_tokens = output_argmax.prompt_tokens
        ground_truth_tokens_w_pause = parse_ground_truth_with_pause(ground_truth, tokenizer, prompt_tokens, system.model)  # fmt: off
        ground_truth_tokens_w_pause_skip = parse_ground_truth_with_skip(
            ground_truth_tokens_w_pause,
            tokenizer,
            prompt_tokens,
            datum.completion.request.suffix,
            system.model,
        )
        # Collect the outputs
        # fmt: off
        output_random_1 = system.model.raw_generate_tokens(prompt_tokens, gen_options_random)
        output_random_2 = system.model.raw_generate_tokens(prompt_tokens, gen_options_random)
        data_to_dump = {
            "datum": HindsightCompletionDatum.schema().dump(datum),
            "blob_names": blob_names,
            "output_argmax": CompletionResult.schema().dump(output_argmax),
            "ground_truth_tokens_w_pause_skip": ground_truth_tokens_w_pause_skip,
            "output_random_1_tokens": output_random_1.tokens,
            "output_random_2_tokens": output_random_2.tokens,
        }
        with save_path.open("w", encoding="utf-8") as f:
            json.dump(data_to_dump, f, indent=2)
        print(f"{utils_for_log.time_string()} Process {idx:4d}/{len(all_datum):4d}-th example ({len(blob_names)} blobs) into {save_path}.")
        # fmt: on
    print(f"{utils_for_log.time_string()} Finish processing {len(all_datum)} examples.")  # fmt: off


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=pathlib.Path,
        default=None,
        required=True,
        help="The input file folder",
    )
    args = parser.parse_args()
    system = create_system()
    system.load()
    process_hindsight(args.input, system)


if __name__ == "__main__":
    main()
