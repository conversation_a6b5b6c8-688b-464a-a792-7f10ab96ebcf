"""Create the rlhf dataset for completion models.

python experimental/dxy/rag/rlhf/create-offline-sft-dataset.py

/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-tutor-hindsight/0913-simple_elden_v2
"""

import argparse
import concurrent.futures as futures
import copy
import json
import pathlib
from datetime import datetime, timezone

import numpy as np
import torch
import tqdm
import zstandard as zstd
from datasets import Dataset as HFDataset
from megatron.data import indexed_dataset

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDatum,
)
from base.prompt_format.common import PromptChunk
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict
from experimental.dxy.rag.rlhf.local_content_manager import (
    LocalContentManager,
)
from experimental.dxy.rag.rlhf.shared_lib import (
    change_prompt_tokens_from_v1_to_v2,
    extract_tokens_before_stop_tokens,
    random_drop_tokens_fn,
    remove_tokens_fn,
)
from research.core import utils_for_log
from research.core.model_input import ModelInput
from research.core.recency_info import convert_from_datasets_recency_info
from research.eval.harness.systems.abs_system import CompletionResult
from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem


def parse_json_file(path: pathlib.Path):
    with open(path, "r", encoding="utf-8") as f:
        xdata = json.load(f)
        output_random_1_tokens = xdata["output_random_1_tokens"]
        output_random_2_tokens = xdata["output_random_2_tokens"]
        ground_truth_tokens_w_pause_skip = xdata["ground_truth_tokens_w_pause_skip"]
        # datum = HindsightCompletionDatum.schema().load(xdata["datum"])
        output_argmax = CompletionResult.schema().load(xdata["output_argmax"])
        # blob_names = xdata["blob_names"]
        output_random_1_tokens = extract_tokens_before_stop_tokens(
            output_random_1_tokens,
            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],
        )
        output_random_2_tokens = extract_tokens_before_stop_tokens(
            output_random_2_tokens,
            [tokenizer.special_tokens.eos, tokenizer.special_tokens.padding],
        )
        return {
            # "datum": datum,
            "user_id": xdata["datum"]["completion"]["user_id"],
            "timestamp": datetime.fromtimestamp(
                xdata["datum"]["completion"]["request"]["timestamp"], tz=timezone.utc
            ).isoformat(),
            # "blob_names": blob_names,
            "output_argmax": output_argmax,
            "ground_truth_tokens_w_pause_skip": ground_truth_tokens_w_pause_skip,
            "output_random_1_tokens": output_random_1_tokens,
            "output_random_2_tokens": output_random_2_tokens,
        }


if __name__ == "__main__":
    tokenizer = StarCoder2Tokenizer()

    input_folders = [
        # "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto",
        # "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing",
        # "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto",
        # "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing",
        # "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing",
        # "/mnt/efs/augment/user/dxy/hindsight/2024-08-2024-09/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard",
    ]
    all_json_files = []
    for input_folder in input_folders:
        target_folder = pathlib.Path(input_folder) / "token-data-w-skip-pause"
        cur_json_files = list(target_folder.glob("*-*.json"))
        print(f"There are {len(cur_json_files)} items in {input_folder}.")
        all_json_files.extend(cur_json_files)
    print(f"{utils_for_log.time_string()} there are {len(all_json_files)} items.")

    all_data = []
    for json_file in tqdm.tqdm(
        all_json_files, total=len(all_json_files), desc="Decompress"
    ):
        all_data.append(parse_json_file(json_file))

    prompt_lens, target_lengs = [], []
    clean_data = []
    for data in all_data:
        ground_truth_tokens_w_pause_skip = data["ground_truth_tokens_w_pause_skip"]
        prompt_tokens = data["output_argmax"].prompt_tokens
        new_prompt_tokens = change_prompt_tokens_from_v1_to_v2(prompt_tokens, tokenizer)
        prompt_lens.append(len(new_prompt_tokens))
        target_lengs.append(len(ground_truth_tokens_w_pause_skip))
        clean_data.append(
            {
                "prompt_tokens": new_prompt_tokens,
                "positive_tokens": ground_truth_tokens_w_pause_skip,
            }
        )
    print(
        f"The average prompt length is {np.mean(prompt_lens)} with std {np.std(prompt_lens)}."
    )
    print(
        f"The average target length is {np.mean(target_lengs)} with std {np.std(target_lengs)}."
    )

    # Create the indexed dataset.
    # output_path = pathlib.Path(
    #     "/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-tutor-hindsight/0913-simple_elden_v2_s12k"
    # )
    # output_path = pathlib.Path(
    #     "/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_vendor_simple_elden_s8k"
    # )
    output_path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/20240919/sft_dogfood_simple_elden_s8k"
    )
    print(f"Plan to save into {output_path}")

    seq = 7936 + 1
    num_exceed = 0

    builder = indexed_dataset.make_builder(
        str(output_path.with_suffix(".bin")),
        impl="mmap",
        vocab_size=tokenizer.vocab_size,
    )
    for data in clean_data:
        tokens = (
            data["prompt_tokens"]
            + data["positive_tokens"]
            + [tokenizer.special_tokens.eos]
        )
        if len(tokens) >= seq:
            tokens = tokens[:seq]
            num_exceed += 1
        else:
            tokens = tokens + [tokenizer.special_tokens.padding] * (seq - len(tokens))
        assert len(tokens) == seq
        builder.add_item(tokens)
    builder.finalize(str(output_path.with_suffix(".idx")))
    print(f"There are {num_exceed} examples exceeding {seq}.")
    print(f"Save {len(clean_data)} examples to {output_path}.")
