"""Create the reward model's training dataset for the completion models.

# Current Data Range: 20240801 ~ 20241201

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20240801-20241126 --output_file 20240801-1126-QWEN
- take over 2 hours to process
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1126-QWEN (830K items)

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20240801-20241207 --output_file 20240801-1207-QWEN
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1207-QWEN (924K items)
- There are 4514/10770 data with label=3 and skip token.
- There are 6256/10770 data with label=4 and skip token.

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20240801-20241229 --output_file 20240801-1229-QWEN
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1229-QWEN (1120K items)
- There are 8627/20237 data with label=3 and skip token.
- There are 11610/20237 data with label=4 and skip token.

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20240801-20241231 --output_file 20240801-1231-QWEN
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN (1130K items)
There are  226921 / 1130339 data with label 0.
There are   60588 / 1130339 data with label 1.
There are   50881 / 1130339 data with label 2.
There are  104584 / 1130339 data with label 3.
There are  538977 / 1130339 data with label 4.
There are  148388 / 1130339 data with label 5.
Skip-token-data: 8828/20649 label=3, 11821/20649 label=4


python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20240801-20250119 --output_file 240801-250119-QWEN
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN
There are  252011 / 1301104 data with label 0.
There are   65796 / 1301104 data with label 1.
There are   56953 / 1301104 data with label 2.
There are  132957 / 1301104 data with label 3.
There are  624080 / 1301104 data with label 4.
There are  169307 / 1301104 data with label 5.
Skip-token-data: 12396/29084 label=3, 16688/29084 label=4

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20241127-20241128 --output_file 20241127-1128-QWEN --split_dataset
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241127-1128-QWEN
    There are  4511/20350 data with label 0.
    There are  1058/20350 data with label 1.
    There are  1148/20350 data with label 2.
    There are  1193/20350 data with label 3.
    There are 10132/20350 data with label 4.
    There are  2308/20350 data with label 5.
    Skip-token-data: 412/975 label=3, 563/975 label=4.

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20241208-20241209 --output_file 20241208-1209-QWEN --split_dataset
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241208-1209-QWEN
    There are    3092 /   13151 data with label 0.
    There are     626 /   13151 data with label 1.
    There are     747 /   13151 data with label 2.
    There are     829 /   13151 data with label 3.
    There are    6225 /   13151 data with label 4.
    There are    1632 /   13151 data with label 5.
    Skip-token-data: 253/614 label=3, 361/614 label=4.

python experimental/dxy/rag/rlhf/data/create-reward-data-qwen.py --date_range 20241210-20241211 --output_file 20241210-1211-QWEN --split_dataset
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241210-1211-QWEN
    There are    5283 /   25864 data with label 0.
    There are    1628 /   25864 data with label 1.
    There are    1312 /   25864 data with label 2.
    There are    1824 /   25864 data with label 3.
    There are   12815 /   25864 data with label 4.
    There are    3002 /   25864 data with label 5.
    Skip-token-data: 394/962 label=3, 568/962 label=4.
"""

import argparse
import collections
import gc
import pathlib
import random

import numpy as np
import tqdm
from datasets import Dataset as HFDataset

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.data.load_data_lib import (
    MODEL2SKIP as MODEL2SKIP,
)
from experimental.dxy.rag.rlhf.data.load_data_lib import (
    MODEL2TOKENIZER,
    QWEN_ELDEN_MODELS,
    SC2_ELDEN_MODELS,
    SC2_SIMPLE_ELDEN_MODELS,
    load_data_as_dataset,
)

sc2_tokenizer = StarCoder2Tokenizer()
sc2_special_tokens = sc2_tokenizer.special_tokens
qwen_tokenizer = Qwen25CoderTokenizer()
qwen_special_tokens = qwen_tokenizer.special_tokens


def assert_all_is_normal_sc2_tokens(tokens: list[int]):
    for token in tokens:
        if token >= 49152:
            raise ValueError(f"{token} is not a normal SC2 token.")


def convert_sc2_prompt_to_qwen(tokens: list[int]) -> list[int]:
    """Convert the SC2 prompt tokens to QWEN prompt tokens."""
    # The structure is:
    # file path
    # far_prefix
    # retrieval section
    # signature section
    # prefix -> suffix -> fim_middle_token
    assert tokens.count(sc2_special_tokens.far_prefix) == 1
    assert tokens.count(sc2_special_tokens.fim_prefix) == 1
    assert tokens.count(sc2_special_tokens.fim_suffix) == 1
    assert tokens.count(sc2_special_tokens.fim_middle) == 1
    assert tokens.count(sc2_special_tokens.sig_begin) == 1
    assert tokens.count(sc2_special_tokens.sig_end) == 1
    assert tokens.count(sc2_special_tokens.retrieval_section) == 1
    ret_section_index = tokens.index(sc2_special_tokens.retrieval_section)
    far_prefix_index = tokens.index(sc2_special_tokens.far_prefix)
    fim_prefix_index = tokens.index(sc2_special_tokens.fim_prefix)
    fim_suffix_index = tokens.index(sc2_special_tokens.fim_suffix)
    fim_middle_index = tokens.index(sc2_special_tokens.fim_middle)
    sig_begin_index = tokens.index(sc2_special_tokens.sig_begin)
    sig_end_index = tokens.index(sc2_special_tokens.sig_end)
    assert tokens[0] == sc2_special_tokens.filename
    if not (
        far_prefix_index
        < sig_begin_index
        < sig_end_index
        < fim_prefix_index
        < fim_suffix_index
        < fim_middle_index
    ):
        raise ValueError("The tokens are not in the right order.")
    assert fim_middle_index + 1 == len(tokens)
    qwen_file_path_tokens = qwen_tokenizer.tokenize_safe(
        sc2_tokenizer.detokenize(tokens[1:far_prefix_index])
    )
    qwen_fim_prefix_tokens = qwen_tokenizer.tokenize_safe(
        sc2_tokenizer.detokenize(tokens[fim_prefix_index + 1 : fim_suffix_index])
    )
    qwen_fim_suffix_tokens = qwen_tokenizer.tokenize_safe(
        sc2_tokenizer.detokenize(tokens[fim_suffix_index + 1 : fim_middle_index])
    )
    qwen_filepath_far_prefix = (
        [qwen_special_tokens.filename]
        + qwen_file_path_tokens
        + [qwen_special_tokens.far_prefix]
        + qwen_tokenizer.tokenize_safe(
            sc2_tokenizer.detokenize(tokens[far_prefix_index + 1 : ret_section_index])
        )
    )
    qwen_fim_body = (
        [qwen_special_tokens.fim_prefix]
        + qwen_fim_prefix_tokens
        + [qwen_special_tokens.fim_suffix]
        + qwen_fim_suffix_tokens
        + [qwen_special_tokens.fim_middle]
    )
    qwen_sig_body = (
        [qwen_special_tokens.sig_begin]
        + qwen_tokenizer.tokenize_safe(
            sc2_tokenizer.detokenize(tokens[sig_begin_index + 1 : sig_end_index])
        )
        + [qwen_special_tokens.sig_end]
    )
    # Try to parse the retrieval section
    assert (
        tokens.count(sc2_special_tokens.filename)
        == tokens.count(sc2_special_tokens.ret_start) + 1
    )
    assert tokens.count(sc2_special_tokens.ret_start) == tokens.count(
        sc2_special_tokens.ret_body
    )
    sc2_retrieval_tokens = tokens[ret_section_index + 1 : sig_begin_index]
    assert sc2_retrieval_tokens.count(sc2_special_tokens.ret_start) == tokens.count(
        sc2_special_tokens.ret_start
    )
    indexes_ret_start, indexes_filenames, indexes_ret_body = [], [], []
    for index, token in enumerate(sc2_retrieval_tokens):
        if token == sc2_special_tokens.ret_start:
            indexes_ret_start.append(index)
        elif token == sc2_special_tokens.filename:
            indexes_filenames.append(index)
        elif token == sc2_special_tokens.ret_body:
            indexes_ret_body.append(index)
        else:
            pass
    assert len(indexes_ret_start) == len(indexes_filenames) == len(indexes_ret_body)
    qwen_ret_body_tokens = [qwen_special_tokens.retrieval_section]
    for index, (index_ret_start, index_filename, index_ret_body) in enumerate(
        zip(indexes_ret_start, indexes_filenames, indexes_ret_body)
    ):
        assert index_ret_start + 1 == index_filename
        cur_file_path = sc2_tokenizer.detokenize(
            sc2_retrieval_tokens[index_filename + 1 : index_ret_body]
        )
        cur_file_path_tokens = qwen_tokenizer.tokenize_safe(cur_file_path)
        if index + 1 == len(indexes_ret_start):  # last one
            cur_sc2_body_tokens = sc2_retrieval_tokens[index_ret_body + 1 :]
        else:
            cur_sc2_body_tokens = sc2_retrieval_tokens[
                index_ret_body + 1 : indexes_ret_start[index + 1]
            ]
        cur_qwen_ret_chunk = [
            qwen_special_tokens.ret_start,
            qwen_special_tokens.filename,
            *cur_file_path_tokens,
            qwen_special_tokens.ret_body,
            *qwen_tokenizer.tokenize_safe(
                sc2_tokenizer.detokenize(cur_sc2_body_tokens)
            ),
        ]
        qwen_ret_body_tokens.extend(cur_qwen_ret_chunk)
    qwen_all = (
        qwen_filepath_far_prefix + qwen_ret_body_tokens + qwen_sig_body + qwen_fim_body
    )
    assert qwen_all.count(qwen_special_tokens.far_prefix) == 1
    assert qwen_all.count(qwen_special_tokens.fim_prefix) == 1
    assert qwen_all.count(qwen_special_tokens.fim_suffix) == 1
    assert qwen_all.count(qwen_special_tokens.fim_middle) == 1
    assert qwen_all.count(qwen_special_tokens.sig_begin) == 1
    assert qwen_all.count(qwen_special_tokens.sig_end) == 1
    assert qwen_all.count(qwen_special_tokens.retrieval_section) == 1, qwen_all.count(
        qwen_special_tokens.retrieval_section
    )
    return qwen_all


def convert_sc2_target_to_qwen(ori_target_tokens: list[int]) -> list[int]:
    """Convert the SC2 target tokens to QWEN target tokens."""
    if ori_target_tokens[-1] == sc2_special_tokens.eos:
        target_tokens = ori_target_tokens[:-1]
        end_with = "eos"
    elif ori_target_tokens[-1] == sc2_special_tokens.pause:
        target_tokens = ori_target_tokens[:-1]
        end_with = "pause"
    else:
        target_tokens = ori_target_tokens
        end_with = "none"
    # Now, let's handle the skip tokens
    if sc2_special_tokens.skip in target_tokens:
        skip_indexes = [
            index
            for index, token in enumerate(target_tokens)
            if token == sc2_special_tokens.skip
        ]
        qwen_parts: list[int] = []
        for idx, skip_index in enumerate(skip_indexes):
            if idx == 0:
                qwen_parts.extend(
                    qwen_tokenizer.tokenize_safe(
                        sc2_tokenizer.detokenize(target_tokens[:skip_index])
                    )
                )
            else:
                qwen_parts.extend(
                    qwen_tokenizer.tokenize_safe(
                        sc2_tokenizer.detokenize(
                            target_tokens[skip_indexes[idx - 1] + 1 : skip_index]
                        )
                    )
                )
            qwen_parts.append(qwen_special_tokens.skip)
            if idx == len(skip_indexes) - 1:  # last one
                qwen_parts.extend(
                    qwen_tokenizer.tokenize_safe(
                        sc2_tokenizer.detokenize(target_tokens[skip_index + 1 :])
                    )
                )
        qwen_target_tokens = qwen_parts
    else:
        assert_all_is_normal_sc2_tokens(target_tokens)
        qwen_target_tokens = qwen_tokenizer.tokenize_safe(
            sc2_tokenizer.detokenize(target_tokens)
        )
    if end_with == "eos":
        qwen_target_tokens.append(qwen_tokenizer.special_tokens.eos)
    elif end_with == "pause":
        qwen_target_tokens.append(qwen_tokenizer.special_tokens.pause)
    elif end_with == "none":
        pass
    else:
        raise ValueError(f"Unknown end_with: {end_with}")
    assert qwen_tokenizer.detokenize(qwen_target_tokens) == sc2_tokenizer.detokenize(
        ori_target_tokens
    )
    return qwen_target_tokens


def convert_sc2_to_qwen(data: dict) -> dict | None:
    """Convert the SC2 data to QWEN data."""
    response_model = data["raw_data"]["shared_info"]["response_model"]
    if response_model in QWEN_ELDEN_MODELS:
        return data
    elif response_model in SC2_SIMPLE_ELDEN_MODELS:
        # target_tokens = data["target_tokens"]
        # qwen_target_tokens = convert_sc2_target_to_qwen(target_tokens)
        # import pdb
        # pdb.set_trace()
        # print("-")
        return None
    elif response_model in SC2_ELDEN_MODELS:
        prompt_tokens = data["prompt_tokens"]
        target_tokens = data["target_tokens"]
        qwen_prompt_tokens = convert_sc2_prompt_to_qwen(prompt_tokens)
        qwen_target_tokens = convert_sc2_target_to_qwen(target_tokens)
        return {
            "prompt_tokens": qwen_prompt_tokens,
            "target_tokens": qwen_target_tokens,
            "label": data["label"],
            "raw_data": data["raw_data"],
        }
    else:
        raise ValueError(f"Can not find the model {response_model}")


def str2int_list(s: str) -> list[int]:
    return [ord(x) for x in s]


def inspect_skip_tokens(all_data: list[dict]):
    num_skip = 0
    label2num = collections.defaultdict(int)
    label2num_w_skip = collections.defaultdict(int)
    for x in all_data:
        response_model = x["raw_data"]["shared_info"]["response_model"]
        skip_token = qwen_special_tokens.skip
        if skip_token is None:
            raise ValueError(f"Can not find skip token for {response_model}")
        if skip_token in x["target_tokens"]:
            num_skip += 1
            label2num_w_skip[x["label"]] += 1
        label2num[x["label"]] += 1
    print("-" * 32)
    print(f"There are {num_skip:7d} / {len(all_data):7d} data with skip token.")
    for label, num in label2num.items():
        print(
            f"[Overall Dist] there are {num:7d} / {len(all_data):7d} data with {label=}."
        )
    print("-" * 32)
    for label, num in label2num_w_skip.items():
        print(
            f"[Skip Token Dist] there are {num:7d} / {num_skip:7d} data with {label=}."
        )
    print("-" * 32)


def num_tokens_to_bucket(num: int) -> int:
    if num <= 6:
        return 6
    elif num == 7:
        return 7
    elif 8 <= num <= 29:
        return (num // 2) * 2
    else:
        return num


def dedup_data_comprehensive(all_data: list[dict], num_per_key: int) -> list[dict]:
    """A pretty aggressive deduplication approach."""
    data_by_special_keys = collections.defaultdict(list)
    all_token_buckets = set()
    for x in tqdm.tqdm(all_data, desc="analyze"):
        shared_info = x["raw_data"]["shared_info"]
        date, hours = shared_info["timestamp"].split(" ")
        key = (
            shared_info["user_id"],
            date,
            int(hours[:2]) // 6,  # every six hours
            shared_info["file_path"],
            shared_info["num_lines_in_prefix"] // 3,  # Every 3 lines
            x["label"],  # label
            num_tokens_to_bucket(len(x["target_tokens"])),
        )
        data_by_special_keys[key].append(x)
        all_token_buckets.add(key[-1])

    print(f"There are {len(data_by_special_keys)}/{len(all_data)} unique keys.")
    print(f"There are {len(all_token_buckets)} token buckets: {all_token_buckets}")
    length_per_key = []
    for key, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_comprehensive] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    sampled_data: list[dict] = []
    for key, data_list in data_by_special_keys.items():
        if key[-1] > 64:
            cur_num_per_key = num_per_key * 3
        elif key[-1] > 24:
            cur_num_per_key = num_per_key * 2
        else:
            cur_num_per_key = num_per_key
        sampled_data.extend(
            random.sample(data_list, min(cur_num_per_key, len(data_list)))
        )
    print(
        f"[dedup_data_comprehensive] Sampled {len(sampled_data)} data by {num_per_key=}."
    )
    return sampled_data


def dedup_data_simple(all_data: list[dict]) -> list[dict]:
    """Simple deduplication based on the number of tokens."""
    data_by_special_keys = collections.defaultdict(list)
    for x in tqdm.tqdm(all_data, desc="analyze"):
        if x["accepted"] is None:
            key = (x["accepted"], len(x["ground_truth_tokens_ids"]))
        else:
            key = (x["accepted"], len(x["generated_token_ids"]))
        data_by_special_keys[key].append(x)
    print(f"There are {len(data_by_special_keys)}/{len(all_data)} unique keys.")
    length_per_key = []
    for key, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_simple] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    num_per_key = (
        max(length_per_key.mean().item(), np.median(length_per_key).item()) * 2.5
    )
    num_per_key = int(num_per_key) + 1
    sampled_data: list[dict] = []
    for key, data_list in data_by_special_keys.items():
        sampled_data.extend(random.sample(data_list, min(num_per_key, len(data_list))))
    print(f"[dedup_data_simple] Sampled {len(sampled_data)} data by {num_per_key=}.")
    return sampled_data


def analyze_tokens(
    all_prompt_tokens: list[list[int]],
    all_target_tokens: list[list[int]],
    all_labels: list[list[int]],
):
    all_prompt_lengths, all_target_lengths, all_token_lengths = [], [], []
    for prompt_tokens, target_tokens in zip(all_prompt_tokens, all_target_tokens):
        all_prompt_lengths.append(len(prompt_tokens))
        all_target_lengths.append(len(target_tokens))
        all_token_lengths.append(len(prompt_tokens) + len(target_tokens))
    all_prompt_lengths = np.array(all_prompt_lengths)
    all_target_lengths = np.array(all_target_lengths)
    all_token_lengths = np.array(all_token_lengths)
    print(
        f"Prompt: {all_prompt_lengths.min()}~{all_prompt_lengths.max()}"
        f" mean={all_prompt_lengths.mean()},"
        f" std={all_prompt_lengths.std()}"
    )
    print(
        f"Target: {all_target_lengths.min()}~{all_target_lengths.max()}"
        f" mean={all_target_lengths.mean()}, std={all_target_lengths.std()}"
    )
    print(
        f"Prompt + Target: {all_token_lengths.min()}~{all_token_lengths.max()}"
        f" mean={all_token_lengths.mean()}, std={all_token_lengths.std()}"
    )
    unique_labels: list[int] = sorted(list(set([x[0] for x in all_labels])))
    for unique_label in unique_labels:
        print(
            f"There are {all_labels.count([unique_label]):7d} / {len(all_labels):7d} data with label {unique_label}."
        )


def count_num_by_fn(list_of_data: list[dict], fn) -> dict[str, int]:
    key2num = collections.defaultdict(int)
    for data in list_of_data:
        key2num[fn(data)] += 1
    return key2num


def process_data_as_dataset(
    date_ranges: list[str],
    input_dir: pathlib.Path,
    output_dir: pathlib.Path,
    num_per_key: int,
    split_dataset: bool = False,
):
    """Process the data as a dataset."""
    assert not output_dir.exists(), f"{output_dir} already exists."
    all_user_data = load_data_as_dataset(date_ranges=date_ranges, input_dir=input_dir)

    all_user_data_after_fix_skip = all_user_data.filter_ar_by_user_agent()
    final_user_data = all_user_data_after_fix_skip.filter_ar_by_duplication_request_id().filter_hs_by_missing_request_id_and_identical_response()
    print("-" * 100)
    converted_data: list[dict] = []
    for data in tqdm.tqdm(
        final_user_data.ar_data, desc="Convert", total=len(final_user_data.ar_data)
    ):
        for cx in final_user_data.convert_ar_data_to_reward_data(data):
            converted_data.append(cx)
    source2num = count_num_by_fn(converted_data, lambda x: x["source"])
    for source, num in source2num.items():
        print(f"{source}: {num}")
    print(f"There are {len(converted_data)} converted data.")

    # Augment and convert the data into learning signals
    new_all_data = []
    for data in converted_data:
        prompt_tokens = data["prompt_tokens"]
        target_tokens = data["target_tokens"]
        source = data["source"]
        tokenizer = MODEL2TOKENIZER[data["shared_info"]["response_model"]]
        special_tokens = tokenizer.special_tokens
        # print(f"{source}: {prompt_tokens} -> {target_tokens}")
        # raw@accept-then-other : 0
        # hindsight : 1
        # raw@accept-then-edit : 2
        # raw@accept : 3
        # raw@reject, raw@reject-partial, and raw@reject-all : 4
        # augmented rejection: 5
        if source == "raw@accept-then-other":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 0,
                    "raw_data": data,
                }
            )
        elif source == "hindsight":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 1,
                    "raw_data": data,
                }
            )
        elif source == "raw@accept-then-edit":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 2,
                    "raw_data": data,
                }
            )
        elif source == "raw@accept":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 3,
                    "raw_data": data,
                }
            )
        # Too few samples
        # elif source == "raw@reject-partial" and len(target_tokens) >= 8:
        #     new_all_data.append({"prompt_tokens": prompt_tokens, "target_tokens": target_tokens, "label": 4, "raw_data": data})
        elif source in ("raw@reject", "raw@reject-partial", "raw@reject-all"):
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 4,
                    "raw_data": data,
                }
            )
        else:
            raise ValueError(f"Unknown source: {source}")
        # In the accept case
        if (
            source in ("raw@accept-then-other", "raw@accept-then-edit", "raw@accept")
            and len(target_tokens) > 10
            and special_tokens.skip not in target_tokens
        ):
            index_of_partial = random.randint(0, len(target_tokens) - 1)
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens[:index_of_partial]
                    + [special_tokens.eos],
                    "label": 5,
                    "raw_data": data,
                }
            )
    print("-" * 100)
    # Analyze the data by model name
    model2num = count_num_by_fn(
        new_all_data,
        lambda x: x["raw_data"]["shared_info"]["response_model"],
    )
    for model, num in model2num.items():
        print(f"{model:15s}: {num:6d}")

    # Convert the data to QWEN and filter with some necessary conditions
    qwen_all_data = []
    for new_all_data_item in tqdm.tqdm(
        new_all_data, desc="Convert to QWEN", total=len(new_all_data)
    ):
        # eldenv7-0-15b's results with skip tokens are not trustable.
        response_model = new_all_data_item["raw_data"]["shared_info"]["response_model"]
        if (
            response_model == "eldenv7-0-15b"
            and MODEL2SKIP[response_model] in new_all_data_item["target_tokens"]
        ):
            continue
        qwen_item = convert_sc2_to_qwen(new_all_data_item)
        if qwen_item is None:
            continue
        qwen_all_data.append(qwen_item)
    print(f"There are {len(qwen_all_data)}/{len(new_all_data)} QWEN data.")

    del new_all_data
    label2num = count_num_by_fn(qwen_all_data, lambda x: x["label"])
    for label in sorted(label2num.keys()):
        num = label2num[label]
        print(f"{label=}: {num}")
    print(f"There are {len(qwen_all_data)} converted data.")
    print("-" * 100)

    # Deduplicate
    targets2num_before_dedup = count_num_by_fn(
        qwen_all_data, lambda x: len(x["target_tokens"])
    )
    all_data_after_dedup = dedup_data_comprehensive(qwen_all_data, num_per_key)
    targets2num_after_dedup = count_num_by_fn(
        all_data_after_dedup, lambda x: len(x["target_tokens"])
    )
    for target_len in sorted(targets2num_before_dedup.keys()):
        print(
            f"{target_len=:3d}: {targets2num_before_dedup[target_len]:6d} -> {targets2num_after_dedup[target_len]:6d}"
        )

    # Analyze the conflict
    key2all4conflict: dict[str, list[dict]] = collections.defaultdict(list)
    for data in all_data_after_dedup:
        key2all4conflict[str(data["prompt_tokens"] + data["target_tokens"])].append(
            data
        )
    ratio_with_conflict = len(key2all4conflict) / len(all_data_after_dedup)
    print(
        f"There are {len(key2all4conflict)} / {len(all_data_after_dedup)} = ({ratio_with_conflict:.2%}) unique prompt+target."
    )
    all_data_after_dedup_no_conflict = []
    for key, all_data_per_key in key2all4conflict.items():
        if len(all_data_per_key) == 1:
            all_data_after_dedup_no_conflict.append(all_data_per_key[0])
    print("-" * 100)
    # Analyze the skip tokens
    inspect_skip_tokens(all_data_after_dedup_no_conflict)
    print("-" * 100)
    # Analyze the data by model name
    model2num = count_num_by_fn(
        all_data_after_dedup_no_conflict,
        lambda x: x["raw_data"]["shared_info"]["response_model"],
    )
    for model, num in model2num.items():
        print(f"{model:10s}: {num:6d}")
    # Start to save data
    # tokenizer = StarCoder2Tokenizer()
    parent_output_dir = output_dir.parent
    print(f"Try to save data into {output_dir}.")
    parent_output_dir.mkdir(exist_ok=True, parents=False)
    output_dir.mkdir(exist_ok=True, parents=False)
    all_prompt_tokens, all_target_tokens, all_labels = [], [], []
    all_response_models, all_user_ids, all_dates = [], [], []
    all_file_paths = []
    all_tenant_names = []
    all_request_ids = []
    unique_labels = set()
    for data in all_data_after_dedup_no_conflict:
        all_prompt_tokens.append(data["prompt_tokens"])
        all_target_tokens.append(data["target_tokens"])
        all_labels.append([data["label"]])  # from 0 to 6
        all_response_models.append(
            str2int_list(data["raw_data"]["shared_info"]["response_model"])
        )
        all_user_ids.append(str2int_list(data["raw_data"]["shared_info"]["user_id"]))
        all_dates.append(str2int_list(data["raw_data"]["shared_info"]["timestamp"]))
        all_file_paths.append(
            str2int_list(data["raw_data"]["shared_info"]["file_path"])
        )
        all_tenant_names.append(
            str2int_list(data["raw_data"]["shared_info"]["tenant_name"])
        )
        all_request_ids.append(
            str2int_list(data["raw_data"]["shared_info"]["request_id"])
        )
        unique_labels.add(data["label"])
    analyze_tokens(all_prompt_tokens, all_target_tokens, all_labels)
    dataset = HFDataset.from_dict(
        {
            "prompt_tokens": all_prompt_tokens,
            "target_tokens": all_target_tokens,
            "labels": all_labels,
            "user_ids": all_user_ids,
            "dates": all_dates,
            "file_paths": all_file_paths,
            "response_models": all_response_models,
            "tenant_names": all_tenant_names,
            "request_ids": all_request_ids,
        }
    )
    dataset.save_to_disk(str(output_dir))
    print(f"Save {len(dataset)} items into {output_dir}.")
    # Save RequestID Only
    dataset_rids = HFDataset.from_dict(
        {
            "request_ids": all_request_ids,
        }
    )
    dataset_rids.save_to_disk(f"{str(output_dir)}-rids")
    print(f"Save {len(dataset_rids)} items into {str(output_dir)}-rids.")
    # dataset = HFDataset.from_dict(
    #     {
    #         "prompt_tokens": all_prompt_tokens,
    #         "target_tokens": all_target_tokens,
    #         "labels": all_labels,
    #     }
    # )
    # dataset.save_to_disk(f"{str(output_dir)}-no-meta")
    print("-" * 100)
    data_with_skip_tokens: list[dict] = []
    num_by_label_w_skip = collections.defaultdict(list)
    for data in all_data_after_dedup_no_conflict:
        if qwen_special_tokens.skip in data["target_tokens"]:
            data_with_skip_tokens.append(data)
            num_by_label_w_skip[data["label"]].append(data)
    for label in sorted(list(unique_labels)):
        print(
            f"There are {len(num_by_label_w_skip[label])}/{len(data_with_skip_tokens)} data with {label=} and skip token."
        )
    gc.collect()
    # Save data per label and skip token
    if split_dataset:
        label2data = collections.defaultdict(list)
        for data in all_data_after_dedup_no_conflict:
            label2data[data["label"]].append(data)
        for label in sorted(list(unique_labels)):
            label_output_dir = parent_output_dir / f"{output_dir.name}-L{label}"
            label_output_dir.mkdir(exist_ok=True, parents=False)
            label_data = label2data[label]
            label_dataset = HFDataset.from_dict(
                {
                    "prompt_tokens": [x["prompt_tokens"] for x in label_data],
                    "target_tokens": [x["target_tokens"] for x in label_data],
                    "labels": [[label] for _ in label_data],
                    "user_ids": [
                        str2int_list(x["raw_data"]["shared_info"]["user_id"])
                        for x in label_data
                    ],
                    "dates": [
                        str2int_list(x["raw_data"]["shared_info"]["timestamp"])
                        for x in label_data
                    ],
                    "response_models": [
                        str2int_list(x["raw_data"]["shared_info"]["response_model"])
                        for x in label_data
                    ],
                }
            )
            print(
                f"Saving {len(label_dataset)} with {label=} items into {label_output_dir}."
            )
            label_dataset.save_to_disk(str(label_output_dir))
        # save the data where skip_token exist
        skip_token_output_dir = parent_output_dir / f"{output_dir.name}-skip"
        skip_token_output_dir.mkdir(exist_ok=True, parents=False)
        skip_token_dataset = HFDataset.from_dict(
            {
                "prompt_tokens": [x["prompt_tokens"] for x in data_with_skip_tokens],
                "target_tokens": [x["target_tokens"] for x in data_with_skip_tokens],
                "labels": [[x["label"]] for x in data_with_skip_tokens],
                "user_ids": [
                    str2int_list(x["raw_data"]["shared_info"]["user_id"])
                    for x in data_with_skip_tokens
                ],
                "dates": [
                    str2int_list(x["raw_data"]["shared_info"]["timestamp"])
                    for x in data_with_skip_tokens
                ],
                "response_models": [
                    str2int_list(x["raw_data"]["shared_info"]["response_model"])
                    for x in data_with_skip_tokens
                ],
            }
        )
        print(f"Saving {len(skip_token_dataset)} items into {skip_token_output_dir}.")
        skip_token_dataset.save_to_disk(str(skip_token_output_dir))
    print("-" * 100)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--input_dir",
        type=pathlib.Path,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114",
    )
    parser.add_argument(
        "--output_dir",
        type=pathlib.Path,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        required=True,
        default=None,
    )
    parser.add_argument(
        "--split_dataset",
        action="store_true",
        help="Whether to split the dataset into multiple files (default: False).",
        default=False,
    )
    args = parser.parse_args()
    args.output_dir.mkdir(exist_ok=True, parents=False)
    assert args.output_file is not None
    assert "/" not in args.output_file
    process_data_as_dataset(
        date_ranges=args.date_range,
        input_dir=args.input_dir,
        output_dir=args.output_dir / args.output_file,
        num_per_key=1,
        split_dataset=args.split_dataset,
    )
