"""Create the RLHF model's training dataset for the completion models.

python experimental/dxy/rag/rlhf/data/create-prompt-from-sft.py
"""

import pathlib
import random

import tqdm
from datasets import Dataset as HFDataset
from megatron.data import indexed_dataset

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from research.core.utils_for_file import read_jsonl_zst
from research.eval.harness.tasks.cceval import CCEvalOutput

tokenizer = Qwen25CoderTokenizer()
special_tokens = tokenizer.special_tokens
fim_token = special_tokens.fim_middle


def main_cceval():
    root_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/"
    )

    cceval_run_result_path = "/mnt/efs/augment/eval/jobs/krWgUP3W/000_fastbackward_CCEval_completed_patches.jsonl.zst"
    cceval_run_results: list[CCEvalOutput] = [
        CCEvalOutput(**x) for x in read_jsonl_zst(cceval_run_result_path)
    ]
    print(f"There are {len(cceval_run_results)} CCEvalOutput elements.")

    cceval_train_data = {"prompt_tokens": [], "target_tokens": [], "labels": []}
    cceval_valid_data = {"prompt_tokens": [], "target_tokens": [], "labels": []}
    num_incorrect_examples = 0
    for x in tqdm.tqdm(cceval_run_results):
        prompt_tokens = x.prompt_tokens
        target_tokens = tokenizer.tokenize_safe(x.ground_truth) + [special_tokens.eos]
        assert 10 < len(prompt_tokens) + len(target_tokens) < 7000
        cceval_train_data["prompt_tokens"].append(prompt_tokens)
        cceval_train_data["target_tokens"].append(target_tokens)
        cceval_train_data["labels"].append([6])  # 5 + 1
        if x.metrics["exact_match_1_line"]:  # type: ignore
            continue
        cceval_valid_data["prompt_tokens"].append(prompt_tokens)
        cceval_valid_data["target_tokens"].append(target_tokens)
        cceval_valid_data["labels"].append([6])  # 5 + 1
        num_incorrect_examples += 1
    print(f"There are {num_incorrect_examples} / {len(cceval_run_results)} examples.")

    cceval_train_output_dir = root_dir / "cceval-train"
    cceval_train_output_dir.mkdir(exist_ok=True, parents=False)
    cceval_train_dataset = HFDataset.from_dict(cceval_train_data)
    print(f"Saving {len(cceval_train_dataset)} items into {cceval_train_output_dir}.")
    cceval_train_dataset.save_to_disk(str(cceval_train_output_dir))

    # split a smaller training dataset with only 5K examples and only prompt tokens
    cceval_train_data_small = {"prompt_tokens": []}
    indexes = random.sample(range(len(cceval_train_data["prompt_tokens"])), 5000)
    for index in indexes:
        cceval_train_data_small["prompt_tokens"].append(
            cceval_train_data["prompt_tokens"][index]
        )
    cceval_train_data_small_output_dir = root_dir / "cceval-train-5k-prompt"
    cceval_train_data_small_output_dir.mkdir(exist_ok=True, parents=False)
    cceval_train_data_small_dataset = HFDataset.from_dict(cceval_train_data_small)
    print(
        f"Saving {len(cceval_train_data_small_dataset)} items into {cceval_train_data_small_output_dir}."
    )
    cceval_train_data_small_dataset.save_to_disk(
        str(cceval_train_data_small_output_dir)
    )

    cceval_valid_output_dir = root_dir / "cceval-valid"
    cceval_valid_output_dir.mkdir(exist_ok=True, parents=False)
    cceval_valid_dataset = HFDataset.from_dict(cceval_valid_data)
    print(f"Saving {len(cceval_valid_dataset)} items into {cceval_valid_output_dir}.")
    cceval_valid_dataset.save_to_disk(str(cceval_valid_output_dir))


def main_sft():
    root_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/"
    )
    # create indexted dataset from /mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/dataset
    train_index_dataset = indexed_dataset.MMapIndexedDataset(
        "/mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/dataset",
        skip_warmup=True,
    )
    print(f"The dataset has {len(train_index_dataset)} train records.")
    valid_index_dataset = indexed_dataset.MMapIndexedDataset(
        "/mnt/efs/spark-data/user/michiel/elden/qwen/dataset/qwen25coder/validation_dataset",
        skip_warmup=True,
    )
    print(f"The dataset has {len(valid_index_dataset)} valid records.")

    train_num, valid_num = 100_000, 10_000
    train_indexes = random.sample(list(range(len(train_index_dataset))), train_num)
    valid_indexes = random.sample(list(range(len(valid_index_dataset))), valid_num)
    train_data = {"prompt_tokens": [], "target_tokens": [], "labels": []}
    valid_data = {"prompt_tokens": [], "target_tokens": [], "labels": []}
    for index in tqdm.tqdm(train_indexes, total=len(train_indexes)):
        data = train_index_dataset[index].tolist()
        assert data.count(fim_token) == 1
        if not (
            data.count(special_tokens.eos) == 1 or data.count(special_tokens.pause) > 0
        ):
            continue
        if data.count(special_tokens.eos) == 1:
            stop_index = data.index(special_tokens.eos)
        else:
            stop_index = data.index(special_tokens.pause)
        prompt_tokens = data[: data.index(fim_token) + 1]
        target_tokens = data[data.index(fim_token) + 1 : stop_index + 1]
        train_data["prompt_tokens"].append(prompt_tokens)
        train_data["target_tokens"].append(target_tokens)
        train_data["labels"].append([6])  # 5 + 1
    for index in tqdm.tqdm(valid_indexes, total=len(valid_indexes)):
        data = valid_index_dataset[index].tolist()
        assert data.count(fim_token) == 1
        if not (
            data.count(special_tokens.eos) == 1 or data.count(special_tokens.pause) > 0
        ):
            continue
        if data.count(special_tokens.eos) == 1:
            stop_index = data.index(special_tokens.eos)
        else:
            stop_index = data.index(special_tokens.pause)
        prompt_tokens = data[: data.index(fim_token) + 1]
        target_tokens = data[data.index(fim_token) + 1 : stop_index + 1]
        valid_data["prompt_tokens"].append(prompt_tokens)
        valid_data["target_tokens"].append(target_tokens)
        valid_data["labels"].append([6])  # 5 + 1

    train_dataset = HFDataset.from_dict(train_data)
    valid_dataset = HFDataset.from_dict(valid_data)
    train_output_dir = root_dir / "sft-train-100K"
    train_output_dir.mkdir(exist_ok=True, parents=False)
    # make a smaller train dataset with only 5K examples and only prompt tokens
    train_data_small = {"prompt_tokens": []}
    indexes = random.sample(range(len(train_data["prompt_tokens"])), 5000)
    for index in indexes:
        train_data_small["prompt_tokens"].append(train_data["prompt_tokens"][index])
    train_data_small_output_dir = root_dir / "sft-train-5k-prompt"
    train_data_small_output_dir.mkdir(exist_ok=True, parents=False)
    train_data_small_dataset = HFDataset.from_dict(train_data_small)
    print(
        f"Saving {len(train_data_small_dataset)} items into {train_data_small_output_dir}."
    )
    train_data_small_dataset.save_to_disk(str(train_data_small_output_dir))
    valid_output_dir = root_dir / "sft-valid-10K"
    valid_output_dir.mkdir(exist_ok=True, parents=False)
    train_dataset.save_to_disk(str(train_output_dir))
    valid_dataset.save_to_disk(str(valid_output_dir))
    print(f"Saving {len(train_dataset)} items into {train_output_dir}.")
    print(f"Saving {len(valid_dataset)} items into {valid_output_dir}.")


if __name__ == "__main__":
    # main_cceval()
    main_sft()
