"""Export hindsight completion samples from our vendors and from dogfood.

In order to run this in a dev container, the following is needed:

1. Authenticate with Google:
```bash
gcloud auth login
gcloud auth application-default login
```
2. Generate the proto library files (do periodically):
```bash
bazel run //tools/generate_proto_typestubs
```

python experimental/dxy/rag/rlhf/data/dump-hindsight.py --sample_limit 0 --user_event_limit 0 --context_size 30 --date_range 20240801-20241114
python experimental/dxy/rag/rlhf/data/dump-hindsight.py --sample_limit 0 --user_event_limit 0 --context_size 30 --date_range 20250116-20250119
"""

import argparse
import logging
from datetime import timedelta
from pathlib import Path

from base.datasets import completion, tenants
from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDatum,
    HindsightCompletionProcessArgs,
    HindsightCompletionQueryArgs,
    NoCompletionEventError,
    query_and_process,
)
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion.prompt_formatter import PromptInput
from experimental.dxy.rag.rlhf.shared_lib import traverse_date_range
from research.core import utils_for_file, utils_for_log


def convert_datum_to_prompt_input(hindsight_data: HindsightCompletionDatum):
    data = hindsight_data.completion
    # assert data.resolution is not None
    # chunks: list[PromptChunk] = []
    # for x in data.response.retrieved_chunks:
    #     unique_id = f"{x.blob_name}:{x.crange.start}-{x.crange.stop}"
    #     chunks.append(
    #         PromptChunk(
    #             text=x.text,
    #             path=x.path,
    #             unique_id=unique_id,
    #             origin=x.origin,
    #             char_start=x.crange.start,
    #             char_end=x.crange.stop,
    #             blob_name=x.blob_name,
    #         )
    #     )
    prompt_input = PromptInput(
        prefix=data.request.prefix,
        suffix=data.request.suffix,
        prefix_begin=0,
        path=data.request.path,
        retrieved_chunks=[],  # chunks
        lang=None,
    )
    if data.feedback is None:
        feedback = None
    else:
        feedback = completion.CompletionFeedback.schema().dump(data.feedback)
    if data.inference_response is None:
        inference_token_ids = None
        assert False, "inference_response is None"
    else:
        inference_token_ids = data.inference_response.token_ids
    if data.resolution is None:
        resolution = None
    else:
        resolution = completion.CompletionResolution.schema().dump(data.resolution)
    if data.request.edit_events is None:
        edit_events = None
    else:
        edit_events = [
            completion.GranularEditEvent.schema().dump(x)
            for x in data.request.edit_events
        ]
    return {
        "prompt_input": {
            "prefix": prompt_input.prefix,
            "suffix": prompt_input.suffix,
            "prefix_begin": prompt_input.prefix_begin,
            "path": prompt_input.path,
            # "retrieved_chunks": [PromptChunk.schema().dump(x) for x in chunks],
            "lang": prompt_input.lang,
        },
        "request_id": data.request_id,
        "user_id": data.user_id,
        # "prompt_tokens": data.response.prompt_tokens,
        "prompt_token_ids": data.response.prompt_token_ids,
        # "generated_tokens": data.response.tokens,
        "generated_token_ids": data.response.token_ids,
        # "generated_token_log_probs": data.response.token_log_probs,
        "ground_truth": hindsight_data.ground_truth,
        "timestamp": str(data.response.timestamp),
        "response_text": data.response.text,
        "response_model": data.response.model,
        "resolution": resolution,
        "feedback": feedback,
        "inference_token_ids": inference_token_ids,
        "user_agent": data.user_agent,
        "edit_events": edit_events,
    }


def main():
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s"
    )

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output_dir",
        type=Path,
        default=Path("/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114"),
        help="The output directory for the exported data",
    )
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--compressed_zst",
        action="store_true",
        default=True,
        help="Whether to compress the output. True will save as data.jsonl.zst",
    )
    parser.add_argument(
        "--sample_limit",
        type=int,
        default=20_000,
        help="The number of samples to export. Non-positive for no limit.",
    )
    parser.add_argument(
        "--user_event_limit",
        type=int,
        default=2_000_000,
        help="Limit on the number of user events to query. Non-positive for no limit.",
    )
    parser.add_argument(
        "--time_limit_s",
        type=int,
        default=3_600,
        help="Time limit in seconds to watch for events after a request.",
    )
    parser.add_argument(
        "--max_interruption_chars",
        type=int,
        default=0,
        help="The max total number of characters across all interruptions to text edits to a "
        "range. These samples will be filtered out if exceeded.",
    )
    parser.add_argument(
        "--min_blobs",
        type=int,
        default=50,
        help="The minimum number of blobs to consider a completion. "
        "This is used to filter out low-quality examples.",
    )
    parser.add_argument(
        "--allow_overlaps",
        action="store_true",
        default=False,
        help="If False, text edits that touch a range, but are not fully contained by it, "
        "will invalidate the sample. These samples will be filtered out.",
    )
    parser.add_argument(
        "--allow_empty_ground_truth",
        action="store_true",
        default=False,
        help="If False, samples with empty ground truth will be filtered out.",
    )
    parser.add_argument(
        "--context_size",
        type=int,
        default=-1,
        help="The number of characters to include in the context range on either side of the "
        "range. If negative, no context is included *and* change trimming is turned off. "
        "This is used to normalize content changes, e.g. 'ab' -> 'abcd' becomes '' to 'cd'",
    )

    args = parser.parse_args()

    all_date_ranges = []
    for cur_date_range in args.date_range:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    tenant_names = ["dogfood", "dogfood-shard", "i0-vanguard0", "aitutor-turing"]

    root_out_dir = args.output_dir
    assert root_out_dir.exists()
    print(f"There are {len(all_date_ranges)} date ranges.")
    for date, xstart, xend in all_date_ranges:
        print(f"{date}: {xstart} - {xend}")
    for index, (date, xstart, xend) in enumerate(all_date_ranges):
        output_dir = root_out_dir / f"{date}-hindsight"

        print(
            f"{utils_for_log.time_string()} Process {index:03d}/{len(all_date_ranges):03d}: {date} into {output_dir}"
        )
        for tenant_name in tenant_names:
            tenant = tenants.get_tenant(tenant_name)

            user_event_limit = (
                args.user_event_limit if args.user_event_limit > 0 else None
            )
            sample_limit = args.sample_limit if args.sample_limit > 0 else None

            query_args = HindsightCompletionQueryArgs(
                user_event_limit=user_event_limit,
                sample_limit=sample_limit,
            )
            process_args = HindsightCompletionProcessArgs(
                time_limit=timedelta(seconds=args.time_limit_s),
                max_interruption_chars=args.max_interruption_chars,
                allow_overlaps=args.allow_overlaps,
                allow_empty_ground_truth=args.allow_empty_ground_truth,
                min_blobs=args.min_blobs,
                context_size=args.context_size if args.context_size >= 0 else None,
            )
            print(f"tenant_name: {tenant_name}")
            print(f"query_args: {query_args}")
            print(f"process_args: {process_args}")
            try:
                query_results, results = query_and_process(
                    tenant,
                    xstart,
                    xend,
                    query_args=query_args,
                    process_args=process_args,
                )
            except NoCompletionEventError:
                print(
                    "No completion events found. Fix your query, or we are missing data. Skipping the rest of processing."
                )
                continue
            all_data: list[HindsightCompletionDatum] = results.data
            datum_list: list[dict] = []
            for x in all_data:
                x_json = convert_datum_to_prompt_input(x)
                datum_list.append(x_json)
            if len(datum_list) == 0:
                print(f"Skip {date} {tenant_name} as there is no data.")
                continue
            output_dir.mkdir(exist_ok=True, parents=False)
            output_path = (
                output_dir / f"{tenant_name}-raw-data-{len(datum_list)}-samples.jsonl"
            )
            utils_for_file.write_jsonl(output_path, datum_list)
            print(f"Save {len(datum_list)} items into {output_path}.")


if __name__ == "__main__":
    main()
