"""Create the reward model's training dataset for the completion models.

# Current Data Range: 20240801 ~ 20241122

python experimental/dxy/rag/rlhf/data/create-reward-data.py --date_range 20240801-20241117 --output_file 20240801-20241117
- /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-20241117 (750210 items)

python experimental/dxy/rag/rlhf/data/create-reward-data.py --date_range 20241118-20241122 --output_file 20241118-20241122
"""

import argparse
import collections
import pathlib
import random

import numpy as np
import tqdm
from datasets import Dataset as HFDataset

from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.data.load_data_lib import (
    MODEL2SKIP as MODEL2SKIP,
)
from experimental.dxy.rag.rlhf.data.load_data_lib import (
    MODEL2TOKENIZER,
    SC2_ELDEN_MODELS,
    load_data_as_dataset,
)


def str2int_list(s: str) -> list[int]:
    return [ord(x) for x in s]


def inspect_skip_tokens(all_data: list[dict], model2skip: dict[str, int]):
    num_skip = 0
    label2num = collections.defaultdict(int)
    label2num_w_skip = collections.defaultdict(int)
    for x in all_data:
        response_model = x["raw_data"]["shared_info"]["response_model"]
        skip_token = model2skip.get(response_model, None)
        if skip_token is None:
            raise ValueError(f"Can not find skip token for {response_model}")
        if skip_token in x["target_tokens"]:
            num_skip += 1
            label2num_w_skip[x["label"]] += 1
        label2num[x["label"]] += 1
    print("-" * 32)
    print(f"There are {num_skip:6d} / {len(all_data):6d} data with skip token.")
    for label, num in label2num.items():
        print(
            f"[Overall Dist] there are {num:6d} / {len(all_data)} data with {label=}."
        )
    print("-" * 32)
    for label, num in label2num_w_skip.items():
        print(f"[Skip Token Dist] there are {num:6d} / {num_skip} data with {label=}.")
    print("-" * 32)


def num_tokens_to_bucket(num: int) -> int:
    if num <= 6:
        return 6
    elif num == 7:
        return 7
    elif 8 <= num <= 29:
        return (num // 2) * 2
    else:
        return num


def dedup_data_comprehensive(all_data: list[dict], num_per_key: int) -> list[dict]:
    """A pretty aggressive deduplication approach."""
    data_by_special_keys = collections.defaultdict(list)
    all_token_buckets = set()
    for x in tqdm.tqdm(all_data, desc="analyze"):
        shared_info = x["raw_data"]["shared_info"]
        date, hours = shared_info["timestamp"].split(" ")
        key = (
            shared_info["user_id"],
            date,
            int(hours[:2]) // 6,  # every six hours
            shared_info["file_path"],
            shared_info["num_lines_in_prefix"] // 3,  # Every 3 lines
            x["label"],  # label
            num_tokens_to_bucket(len(x["target_tokens"])),
        )
        data_by_special_keys[key].append(x)
        all_token_buckets.add(key[-1])

    print(f"There are {len(data_by_special_keys)}/{len(all_data)} unique keys.")
    print(f"There are {len(all_token_buckets)} token buckets: {all_token_buckets}")
    length_per_key = []
    for key, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_comprehensive] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    sampled_data: list[dict] = []
    for key, data_list in data_by_special_keys.items():
        if key[-1] > 64:
            cur_num_per_key = num_per_key * 3
        elif key[-1] > 24:
            cur_num_per_key = num_per_key * 2
        else:
            cur_num_per_key = num_per_key
        sampled_data.extend(
            random.sample(data_list, min(cur_num_per_key, len(data_list)))
        )
    print(
        f"[dedup_data_comprehensive] Sampled {len(sampled_data)} data by {num_per_key=}."
    )
    return sampled_data


def dedup_data_simple(all_data: list[dict]) -> list[dict]:
    """Simple deduplication based on the number of tokens."""
    data_by_special_keys = collections.defaultdict(list)
    for x in tqdm.tqdm(all_data, desc="analyze"):
        if x["accepted"] is None:
            key = (x["accepted"], len(x["ground_truth_tokens_ids"]))
        else:
            key = (x["accepted"], len(x["generated_token_ids"]))
        data_by_special_keys[key].append(x)
    print(f"There are {len(data_by_special_keys)}/{len(all_data)} unique keys.")
    length_per_key = []
    for key, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_simple] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    num_per_key = (
        max(length_per_key.mean().item(), np.median(length_per_key).item()) * 2.5
    )
    num_per_key = int(num_per_key) + 1
    sampled_data: list[dict] = []
    for key, data_list in data_by_special_keys.items():
        sampled_data.extend(random.sample(data_list, min(num_per_key, len(data_list))))
    print(f"[dedup_data_simple] Sampled {len(sampled_data)} data by {num_per_key=}.")
    return sampled_data


def analyze_tokens(
    all_prompt_tokens: list[list[int]],
    all_target_tokens: list[list[int]],
    all_labels: list[list[int]],
):
    all_prompt_lengths, all_target_lengths, all_token_lengths = [], [], []
    for prompt_tokens, target_tokens in zip(all_prompt_tokens, all_target_tokens):
        all_prompt_lengths.append(len(prompt_tokens))
        all_target_lengths.append(len(target_tokens))
        all_token_lengths.append(len(prompt_tokens) + len(target_tokens))
    all_prompt_lengths = np.array(all_prompt_lengths)
    all_target_lengths = np.array(all_target_lengths)
    all_token_lengths = np.array(all_token_lengths)
    print(
        f"Prompt: {all_prompt_lengths.min()}~{all_prompt_lengths.max()}"
        f" mean={all_prompt_lengths.mean()},"
        f" std={all_prompt_lengths.std()}"
    )
    print(
        f"Target: {all_target_lengths.min()}~{all_target_lengths.max()}"
        f" mean={all_target_lengths.mean()}, std={all_target_lengths.std()}"
    )
    print(
        f"Prompt + Target: {all_token_lengths.min()}~{all_token_lengths.max()}"
        f" mean={all_token_lengths.mean()}, std={all_token_lengths.std()}"
    )
    unique_labels: list[int] = sorted(list(set([x[0] for x in all_labels])))
    for unique_label in unique_labels:
        print(
            f"There are {all_labels.count([unique_label]):5d}/{len(all_labels):5d} data with label {unique_label}."
        )


def count_num_by_fn(list_of_data: list[dict], fn) -> dict[str, int]:
    key2num = collections.defaultdict(int)
    for data in list_of_data:
        key2num[fn(data)] += 1
    return key2num


def process_data_as_dataset(
    date_ranges: list[str],
    input_dir: pathlib.Path,
    output_dir: pathlib.Path,
    num_per_key: int,
):
    """Process the data as a dataset."""
    assert not output_dir.exists(), f"{output_dir} already exists."
    all_user_data = load_data_as_dataset(date_ranges=date_ranges, input_dir=input_dir)

    all_user_data_after_fix_skip = all_user_data.filter_ar_by_user_agent()
    final_user_data = all_user_data_after_fix_skip.filter_ar_by_duplication_request_id().filter_hs_by_missing_request_id_and_identical_response()
    print("-" * 100)
    converted_data: list[dict] = []
    for data in tqdm.tqdm(
        final_user_data.ar_data, desc="Convert", total=len(final_user_data.ar_data)
    ):
        for cx in final_user_data.convert_ar_data_to_reward_data(data):
            converted_data.append(cx)
    source2num = count_num_by_fn(converted_data, lambda x: x["source"])
    for source, num in source2num.items():
        print(f"{source}: {num}")
    print(f"There are {len(converted_data)} converted data.")

    # Augment and convert the data into learning signals
    new_all_data = []
    for data in converted_data:
        prompt_tokens = data["prompt_tokens"]
        target_tokens = data["target_tokens"]
        source = data["source"]
        tokenizer = MODEL2TOKENIZER[data["shared_info"]["response_model"]]
        special_tokens = tokenizer.special_tokens
        # print(f"{source}: {prompt_tokens} -> {target_tokens}")
        # raw@accept-then-other : 0
        # hindsight : 1
        # raw@accept-then-edit : 2
        # raw@accept : 3
        # raw@reject, raw@reject-partial, and raw@reject-all : 4
        # augmented rejection: 5
        if source == "raw@accept-then-other":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 0,
                    "raw_data": data,
                }
            )
        elif source == "hindsight":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 1,
                    "raw_data": data,
                }
            )
        elif source == "raw@accept-then-edit":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 2,
                    "raw_data": data,
                }
            )
        elif source == "raw@accept":
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 3,
                    "raw_data": data,
                }
            )
        # Too few samples
        # elif source == "raw@reject-partial" and len(target_tokens) >= 8:
        #     new_all_data.append({"prompt_tokens": prompt_tokens, "target_tokens": target_tokens, "label": 4, "raw_data": data})
        elif source in ("raw@reject", "raw@reject-partial", "raw@reject-all"):
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens,
                    "label": 4,
                    "raw_data": data,
                }
            )
        else:
            raise ValueError(f"Unknown source: {source}")
        # In the accept case
        if (
            source in ("raw@accept-then-other", "raw@accept-then-edit", "raw@accept")
            and len(target_tokens) > 10
            and special_tokens.skip not in target_tokens
        ):
            index_of_partial = random.randint(0, len(target_tokens) - 1)
            new_all_data.append(
                {
                    "prompt_tokens": prompt_tokens,
                    "target_tokens": target_tokens[:index_of_partial]
                    + [special_tokens.eos],
                    "label": 5,
                    "raw_data": data,
                }
            )
    print("-" * 100)
    # Filter by model name
    num_new_all_data_before_filter = len(new_all_data)
    new_all_data = [
        x
        for x in new_all_data
        if x["raw_data"]["shared_info"]["response_model"] in SC2_ELDEN_MODELS
    ]
    print(f"There are {len(new_all_data)}/{num_new_all_data_before_filter} SC2 models.")
    print("-" * 100)
    label2num = count_num_by_fn(new_all_data, lambda x: x["label"])
    for label in sorted(label2num.keys()):
        num = label2num[label]
        print(f"{label=}: {num}")
    print(f"There are {len(new_all_data)} converted data.")
    print("-" * 100)

    # Deduplicate
    targets2num_before_dedup = count_num_by_fn(
        new_all_data, lambda x: len(x["target_tokens"])
    )
    all_data_after_dedup = dedup_data_comprehensive(new_all_data, num_per_key)
    targets2num_after_dedup = count_num_by_fn(
        all_data_after_dedup, lambda x: len(x["target_tokens"])
    )
    for target_len in sorted(targets2num_before_dedup.keys()):
        print(
            f"{target_len=:3d}: {targets2num_before_dedup[target_len]:6d} -> {targets2num_after_dedup[target_len]:6d}"
        )

    # Analyze the conflict
    key2all4conflict: dict[str, list[dict]] = collections.defaultdict(list)
    for data in all_data_after_dedup:
        key2all4conflict[str(data["prompt_tokens"] + data["target_tokens"])].append(
            data
        )
    ratio_with_conflict = len(key2all4conflict) / len(all_data_after_dedup)
    print(
        f"There are {len(key2all4conflict)} / {len(all_data_after_dedup)} = ({ratio_with_conflict:.2%}) unique prompt+target."
    )
    all_data_after_dedup_no_conflict = []
    for key, all_data_per_key in key2all4conflict.items():
        if len(all_data_per_key) == 1:
            all_data_after_dedup_no_conflict.append(all_data_per_key[0])
    print("-" * 100)
    # Analyze the skip tokens
    inspect_skip_tokens(all_data_after_dedup_no_conflict, MODEL2SKIP)
    print("-" * 100)
    # Analyze the data by model name
    model2num = count_num_by_fn(
        all_data_after_dedup_no_conflict,
        lambda x: x["raw_data"]["shared_info"]["response_model"],
    )
    for model, num in model2num.items():
        print(f"{model:10s}: {num:6d}")
    # Start to save data
    # tokenizer = StarCoder2Tokenizer()
    parent_output_dir = output_dir.parent
    print(f"Try to save data into {output_dir}.")
    parent_output_dir.mkdir(exist_ok=True, parents=False)
    output_dir.mkdir(exist_ok=True, parents=False)
    all_prompt_tokens, all_target_tokens, all_labels = [], [], []
    all_response_models = []
    unique_labels = set()
    for data in all_data_after_dedup_no_conflict:
        all_prompt_tokens.append(data["prompt_tokens"])
        all_target_tokens.append(data["target_tokens"])
        all_labels.append([data["label"]])  # from 0 to 6
        all_response_models.append(
            str2int_list(data["raw_data"]["shared_info"]["response_model"])
        )
        unique_labels.add(data["label"])
    analyze_tokens(all_prompt_tokens, all_target_tokens, all_labels)
    dataset = HFDataset.from_dict(
        {
            "prompt_tokens": all_prompt_tokens,
            "target_tokens": all_target_tokens,
            "labels": all_labels,
            "response_models": all_response_models,
        }
    )
    dataset.save_to_disk(str(output_dir))
    print(f"Save {len(dataset)} items into {output_dir}.")
    print("-" * 100)
    # Save data per label
    label2data = collections.defaultdict(list)
    for data in all_data_after_dedup_no_conflict:
        label2data[data["label"]].append(data)
    for label in sorted(list(unique_labels)):
        label_output_dir = parent_output_dir / f"{output_dir.name}-L{label}"
        label_output_dir.mkdir(exist_ok=True, parents=False)
        label_data = label2data[label]
        label_dataset = HFDataset.from_dict(
            {
                "prompt_tokens": [x["prompt_tokens"] for x in label_data],
                "target_tokens": [x["target_tokens"] for x in label_data],
                "labels": [[label] for _ in label_data],
                "response_models": [
                    str2int_list(x["raw_data"]["shared_info"]["response_model"])
                    for x in label_data
                ],
            }
        )
        print(
            f"Saving {len(label_dataset)} with {label=} items into {label_output_dir}."
        )
        label_dataset.save_to_disk(str(label_output_dir))
    print("-" * 100)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--input_dir",
        type=pathlib.Path,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114",
    )
    parser.add_argument(
        "--output_dir",
        type=pathlib.Path,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata",
    )
    parser.add_argument(
        "--output_file",
        type=str,
        required=True,
        default=None,
    )
    args = parser.parse_args()
    args.output_dir.mkdir(exist_ok=True, parents=False)
    assert args.output_file is not None
    assert "/" not in args.output_file
    process_data_as_dataset(
        date_ranges=args.date_range,
        input_dir=args.input_dir,
        output_dir=args.output_dir / args.output_file,
        num_per_key=1,
    )
