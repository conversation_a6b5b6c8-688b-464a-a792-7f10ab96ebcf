"""Compute the statistics for the rlhf dataset.

python experimental/dxy/rag/rlhf/data/compute-statistic.py --date_range 20241201-20241231
python experimental/dxy/rag/rlhf/data/compute-statistic.py --date_range 20250107-20250119
"""

import argparse
import collections
import pathlib

import numpy as np
import tqdm

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from experimental.dxy.rag.rlhf.shared_lib import traverse_date_range
from research.core import utils_for_file

ELDEN_MODELS: list[str] = [
    # "eldenv2-15b",
    # "eldenv3-15b",
    # "eldenv4-15b",
    # "eldenv4-1-15b",
    # "eldenv5-1-15b",
    # "eldenv4-2-15b",
    # "eldenv4-3-15b",
    # "eldenv4-4a-15b",
    # "eldenv4-0b-15b",
    # "eldenv4-0c-15b",
    # "eldenv4-0d-15b",
    # "eldenv4-0e-15b",
    # "eldenv4-0f-15b",
    # "eldenv6-15b",
    # "eldenv6-1-15b",
    # "eldenv7-0-15b",
    # "qweldenv1-14b",
    "qweldenv1-1-14b",
    "qweldenv2-14b",
    "qweldenv2-1-14b",
]


def show_statistic(
    data_per_model: dict[str, list[dict]],
    tokenizer: StarCoder2Tokenizer | Qwen25CoderTokenizer,
    only_dogfood: bool = False,
):
    filtered_model_names = set()
    num_per_tenant = collections.defaultdict(int)
    for model_name, data_list in data_per_model.items():
        if model_name not in ELDEN_MODELS:
            filtered_model_names.add(model_name)
            continue
        # Compute the accepted ratio for each model w.r.t. the number of chars and completions
        num_chars = 0
        num_accepted_chars = 0
        num_completions = 0
        num_accepted_completions = 0
        accepted_chars: list[int] = []
        accepted_lines: list[int] = []
        for data in data_list:
            if only_dogfood and "dogfood" not in data["tenant_name"]:
                continue
            num_completions += 1
            num_per_tenant[data["tenant_name"]] += 1
            cur_text = tokenizer.detokenize(data["generated_token_ids"])
            cur_chars = len(cur_text)
            num_chars += cur_chars
            if data["accepted"]:
                num_accepted_chars += cur_chars
                num_accepted_completions += 1
                accepted_chars.append(cur_chars)
                accepted_lines.append(cur_text.count("\n") + 1)
        if num_completions == 0:
            filtered_model_names.add(model_name)
            continue
        print(
            f"{model_name:14s}: {num_completions:6d} samples,"
            f" {num_accepted_completions/(num_completions+1e-7):5.2%} accepted completions."
            f" {num_accepted_chars/(num_chars+1e-7):5.2%} accepted chars."
            f" {np.mean(accepted_chars):.1f} chars per accepted completion,"
            f" {np.mean(accepted_lines):.1f} lines per accepted completion."
        )
    print(
        f"There are {len(filtered_model_names)} filtered model names:\n{filtered_model_names}"
    )
    print(f"{num_per_tenant=}")


def analyze_fn(
    date_ranges: list[str],
    input_dir: pathlib.Path,
):
    """Process the data as a dataset."""
    assert input_dir.exists(), input_dir
    all_date_ranges = []
    for cur_date_range in date_ranges:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    print(f"There are {len(all_date_ranges)} date ranges.")
    accept_reject_files: list[pathlib.Path] = []
    for date, xstart, xend in all_date_ranges:
        accept_reject_dir = input_dir / f"{date}-accept-reject"
        if accept_reject_dir.exists():
            accept_reject_files.extend(list(accept_reject_dir.glob("*-samples.jsonl")))
        print(f"{date}: {accept_reject_dir}")
    assert len(accept_reject_files) > 0, "Can not find accept/reject files."
    print(f"There are {len(accept_reject_files)} accept/reject files")
    all_data: list[dict] = []
    for accept_reject_file in tqdm.tqdm(
        accept_reject_files, desc="Load accept/reject files"
    ):
        tenant_name = accept_reject_file.name.split("-raw-data")[0]
        for data in utils_for_file.read_jsonl(accept_reject_file):
            data["tenant_name"] = tenant_name
            all_data.append(data)
    tokenizer = Qwen25CoderTokenizer()
    # tokenizer = StarCoder2Tokenizer()
    # special_tokens = tokenizer.special_tokens
    # skip_token_id = special_tokens.skip
    data_per_model: dict[str, list[dict]] = collections.defaultdict(list)
    for data in all_data:
        # if skip_token_id in data["generated_token_ids"]:
        #     continue  # Skip the data with skip token
        data_per_model[data["response_model"]].append(data)
    print("-" * 100)
    show_statistic(data_per_model, tokenizer, only_dogfood=True)
    print("\n" + "-" * 100 + "\n")
    show_statistic(data_per_model, tokenizer, only_dogfood=False)
    print("-" * 100)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--input_dir",
        type=pathlib.Path,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114",
    )
    args = parser.parse_args()
    analyze_fn(
        date_ranges=args.date_range,
        input_dir=args.input_dir,
    )
