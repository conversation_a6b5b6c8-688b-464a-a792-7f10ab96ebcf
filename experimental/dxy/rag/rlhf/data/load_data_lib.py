"""A library to load the real developer data."""

import collections
import copy
import pathlib
import pickle
import random
from typing import Iterator

import numpy as np
import tqdm
from datasets import Dataset as HFDataset

from base.datasets.user_event_lib import CompletionRequestIdIssuedEvent, TextEditEvent
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion.prompt_formatter import PromptInput
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.tokenizers.tokenizer import RagSpecialTokens
from experimental.dxy.rag.rlhf.shared_lib import (
    AGENT_FIXING_SKIP,
    AgentType,
    UserAgent,
    inspect_skip_tokens,
    str2datetimefloat,
    traverse_date_range,
)
from research.core import utils_for_file

SC2_SIMPLE_ELDEN_MODELS: list[str] = [
    "eldenv5-1-15b",
]

SC2_ELDEN_MODELS: list[str] = [
    "eldenv2-15b",
    "eldenv3-15b",
    "eldenv4-15b",
    "eldenv4-1-15b",
    "eldenv4-2-15b",
    "eldenv4-3-15b",
    "eldenv4-4a-15b",
    "eldenv4-4b-15b",
    "eldenv4-0b-15b",
    "eldenv4-0c-15b",
    "eldenv4-0d-15b",
    "eldenv4-0e-15b",
    "eldenv4-0f-15b",
    "eldenv5-1-15b",
    "eldenv6-15b",
    "eldenv6-1-15b",
    "eldenv7-0-15b",
]

QWEN_ELDEN_MODELS: list[str] = [
    "qweldenv1-14b",
    "qweldenv1-1-14b",
    "qweldenv2-14b",
    "qweldenv2-1-14b",
]

sc2_tokenizer = StarCoder2Tokenizer()
sc2_special_tokens = sc2_tokenizer.special_tokens
qwen25_tokenizer = Qwen25CoderTokenizer()
qwen25_special_tokens = qwen25_tokenizer.special_tokens
MODEL2SKIP: dict[str, int] = {}
for model_name in SC2_ELDEN_MODELS:
    MODEL2SKIP[model_name] = sc2_special_tokens.skip
for model_name in QWEN_ELDEN_MODELS:
    MODEL2SKIP[model_name] = qwen25_special_tokens.skip
MODEL2TOKENIZER: dict[str, StarCoder2Tokenizer | Qwen25CoderTokenizer] = {}
for model_name in SC2_ELDEN_MODELS:
    MODEL2TOKENIZER[model_name] = sc2_tokenizer
for model_name in QWEN_ELDEN_MODELS:
    MODEL2TOKENIZER[model_name] = qwen25_tokenizer


def find_index_ue_larger_or_equal_time(
    sorted_user_events: list[dict], xtime: float
) -> int:
    """Using the binary search to find the first element in self.ue_data that has time >= xtime."""
    left = 0
    right = len(sorted_user_events) - 1
    while left <= right:
        mid = (left + right) // 2
        if sorted_user_events[mid]["time"] < xtime:
            left = mid + 1
        else:
            right = mid - 1
    return left


def find_index_ue_larger_than_time(sorted_user_events: list[dict], xtime: float) -> int:
    """Using the binary search to find the first element in self.ue_data that has time > xtime."""
    left = 0
    right = len(sorted_user_events) - 1
    while left <= right:
        mid = (left + right) // 2
        if sorted_user_events[mid]["time"] <= xtime:
            left = mid + 1
        else:
            right = mid - 1
    return left


def compute_overlap(
    file_path: str,
    prefix: str,
    suffix: str,
    completion: str,
    user_event: list[TextEditEvent],
) -> int:
    """Compute the overlap number of chars between the user event and the completion."""
    del suffix
    num_chars_in_prefix = len(prefix)
    num_overlap_chars_typed_by_user = 0
    current_cursor = num_chars_in_prefix
    for event in user_event:
        if event.file_path != file_path:
            return num_overlap_chars_typed_by_user
        for content_change in event.content_changes:
            # print(f"{num_chars_in_prefix=}, {current_cursor=}\ncompletion: {completion.encode('utf-8')}")
            if (
                content_change.crange.start
                == content_change.crange.stop
                == current_cursor
            ):
                # this is to insert a text
                text = content_change.text
                pending_text = completion[current_cursor - num_chars_in_prefix :]
                if text == pending_text[: len(text)]:
                    num_overlap_chars_typed_by_user += len(text)
                    current_cursor += len(text)
                else:
                    return num_overlap_chars_typed_by_user
            elif (
                content_change.crange.stop == current_cursor
                and content_change.text == ""
            ):
                # this is to delete a text
                num_chars_to_delete = (
                    content_change.crange.stop - content_change.crange.start
                )
                num_overlap_chars_typed_by_user -= num_chars_to_delete
                if num_overlap_chars_typed_by_user < 0:
                    return max(num_overlap_chars_typed_by_user, 0)
                current_cursor -= num_chars_to_delete
            elif content_change.crange.stop == current_cursor:
                # this is to replace a text
                num_chars_to_replace = (
                    content_change.crange.stop - content_change.crange.start
                )
                replace_start_in_completion = (
                    current_cursor - num_chars_in_prefix
                ) - num_chars_to_replace
                if replace_start_in_completion < 0:
                    return max(num_overlap_chars_typed_by_user, 0)
                replace_end_in_completion = replace_start_in_completion + len(
                    content_change.text
                )
                if (
                    completion[replace_start_in_completion:replace_end_in_completion]
                    == content_change.text
                ):
                    pass
                else:
                    return max(num_overlap_chars_typed_by_user, 0)
            else:
                raise ValueError(f"Unexpected content_change: {content_change}")
            # Cursor is out of the range
            if current_cursor < num_chars_in_prefix:
                return max(num_overlap_chars_typed_by_user, 0)
    return num_overlap_chars_typed_by_user


class UserEventData:
    """The User Event Data."""

    def __init__(self, ar_data: list[dict], hs_data: list[dict], ue_data: list[dict]):
        self.ar_data = ar_data
        self.hs_data = hs_data
        self.raw_ue_data = ue_data
        # sort user_event_data_by_time
        self.sorted_ue_data_per_user: dict[str, list[dict]] = collections.defaultdict(
            list
        )
        for x in ue_data:
            self.sorted_ue_data_per_user[x["user_id"]].append(x)
        for _, x in self.sorted_ue_data_per_user.items():
            x.sort(key=lambda x: x["time"])
        total_num_events = sum([len(x) for x in self.sorted_ue_data_per_user.values()])
        print("-" * 32 + " UserEventData INIT " + "-" * 32)
        print(f"There are {len(self.ar_data):8d} accept/reject data")
        print(f"There are {len(self.hs_data):8d} hindsight data")
        print(f"There are {total_num_events:8d} user event data")
        # Check UE DATA
        user_rid_to_index_ue: dict[tuple[str, str], list[int]] = (
            collections.defaultdict(list)
        )
        num_of_text_edit_event = 0
        num_of_completion_request_id_issued_event = 0
        # for user_id, ue_data_list in self.sorted_ue_data_per_user.items():
        #     for index, x in enumerate(ue_data_list):
        #         user_rid_to_index_ue[(user_id, x["request_id"])].append(index)
        for user_id, ue_data_list in self.sorted_ue_data_per_user.items():
            for index, x in enumerate(ue_data_list):
                if "content_changes" in x:  # TextEditEvent
                    num_of_text_edit_event += 1
                elif "request_id" in x:  # CompletionRequestIdIssuedEvent
                    num_of_completion_request_id_issued_event += 1
                    user_rid_to_index_ue[(user_id, x["request_id"])].append(index)
                else:
                    raise ValueError(f"Unknown event type: {x}")
        print(f"There are {num_of_text_edit_event:8d} TextEditEvent.")
        print(
            f"There are {num_of_completion_request_id_issued_event:8d} CompletionRequestIdIssuedEvent."
        )
        print(
            f"There are {len(user_rid_to_index_ue):8d} / {total_num_events:8d} unique request IDs (UserEvent)."
        )
        self.user_rid_to_index_ue = user_rid_to_index_ue
        # Check if AR_DATA's request_id is in UE_DATA
        missing_request_id = []
        duplicated_request_id = []
        ok_request_id = set()
        for idx, x in enumerate(self.ar_data):
            user_id = x["user_id"]
            request_id = x["request_id"]
            if (user_id, request_id) not in self.user_rid_to_index_ue:
                missing_request_id.append(request_id)
            elif len(self.user_rid_to_index_ue[(user_id, request_id)]) > 1:
                duplicated_request_id.append(request_id)
            else:
                ok_request_id.add(request_id)
        self.ar_request_id_in_ue = ok_request_id
        print(
            f"There are {len(ok_request_id):8d}/{len(self.ar_data)} AR requests can cross-valid with UE."
        )
        print(
            f"There are {len(duplicated_request_id):8d}/{len(self.ar_data)} AR requests are duplicated in UE."
        )
        print(
            f"There are {len(missing_request_id):8d}/{len(self.ar_data)} AR requests are missing in UE."
        )
        request_id_to_index_hs: dict[str, list[int]] = collections.defaultdict(list)
        for index, x in enumerate(self.hs_data):
            request_id_to_index_hs[x["request_id"]].append(index)
        self.request_id_to_index_hs = request_id_to_index_hs

    def convert_ar_data_to_reward_data(self, data: dict) -> Iterator[dict]:
        """Convert the accept/reject data to the reward data."""
        ar_request_id = data["request_id"]
        ar_user_id = data["user_id"]
        ar_timestamp = data["timestamp"]
        ar_resolution_timestamp = data["resolution"]["timestamp"]
        response_model = data["response_model"]
        tokenizer = MODEL2TOKENIZER[response_model]
        special_tokens = tokenizer.special_tokens
        num_chars_in_prefix = len(data["prompt_input"]["prefix"])
        file_path = data["prompt_input"]["path"]
        response_text = data["response_text"]
        source_file = data["source_file"]
        tenant_name = pathlib.Path(source_file).name.split("-raw-data")[0]
        shared_info = {
            "request_id": ar_request_id,
            "user_id": ar_user_id,
            "response_model": response_model,
            "response_text": response_text,
            "file_path": file_path,
            "timestamp": ar_timestamp,
            "resolution_timestamp": ar_resolution_timestamp,
            "user_agent": data["user_agent"],
            "num_lines_in_prefix": len(data["prompt_input"]["prefix"].splitlines()),
            "num_chars_in_prefix": num_chars_in_prefix,
            "tenant_name": tenant_name,
        }
        # Check the hindsight result
        if ar_request_id in self.request_id_to_index_hs:
            hs_index = self.request_id_to_index_hs[ar_request_id][0]
            hs_data = self.hs_data[hs_index]
            ground_truth_tokens_ids = tokenizer.tokenize_safe(hs_data["ground_truth"])
            if (
                len(ground_truth_tokens_ids) < 95
            ):  # to avoid the length bias from hindsight vs. the rest of data distribution
                yield {
                    "prompt_tokens": hs_data["prompt_token_ids"],
                    "target_tokens": ground_truth_tokens_ids + [special_tokens.eos],
                    "generated_token_ids": hs_data["generated_token_ids"],
                    "shared_info": shared_info,
                    "source": "hindsight",
                }

        accepted = data["accepted"]
        assert accepted is not None
        default_converted_data = {
            "prompt_tokens": data["prompt_token_ids"],
            "target_tokens": data["inference_token_ids"],
            "generated_token_ids": data["generated_token_ids"],
            "shared_info": shared_info,
            "source": "raw@accept" if accepted else "raw@reject",
        }
        # Skip behavior is a bit tricky and thus skip to handle
        if (ar_request_id not in self.ar_request_id_in_ue) or (
            special_tokens.skip in data["inference_token_ids"]
        ):
            # The normal case of accept and reject
            yield default_converted_data
        else:
            # Handle user text change event.
            sorted_ue_data_list = self.sorted_ue_data_per_user[ar_user_id]
            ue_indexes = self.user_rid_to_index_ue[(ar_user_id, ar_request_id)]
            assert (
                len(ue_indexes) == 1
            ), f"{ar_request_id} has {len(ue_indexes)} UE data."
            # The AR Request Time aligned in UE Data
            ar_request_time_aue = sorted_ue_data_list[ue_indexes[0]]["time"]
            ue_index = find_index_ue_larger_or_equal_time(
                sorted_ue_data_list, ar_request_time_aue
            )
            # Find the next 50 events in a minute.
            try:
                future_user_events = sorted_ue_data_list[ue_index : ue_index + 100]
                assert len(future_user_events) > 0, "future_user_events is empty."
                assert (
                    "request_id" in future_user_events[0]
                ), f"{future_user_events[0]} is not UserEvent."
                assert (
                    ar_request_id == future_user_events[0]["request_id"]
                ), f"{future_user_events[0]} is not the same request ID as {ar_request_id}."
                session_id = future_user_events[0]["session_id"]
                future_user_events = [
                    x for x in future_user_events if x["session_id"] == session_id
                ]
                assert (
                    len(future_user_events) > 0
                ), "future_user_events is empty after filtering session_id."
                final_future_user_events = [
                    x
                    for x in future_user_events
                    if x["time"] <= ar_request_time_aue + 60
                ]
                assert (
                    len(final_future_user_events) > 0
                ), "future_user_events is empty after filtering time (<= 1 min)."
            except Exception:
                final_future_user_events = None
                # print(f"Error: {e}, fall back to default.")
                yield default_converted_data
            if accepted:
                accept_then_edit_data = copy.deepcopy(default_converted_data)
                accept_then_edit_data["source"] = "raw@accept-then-edit"
                accept_then_other_data = copy.deepcopy(default_converted_data)
                accept_then_other_data["source"] = "raw@accept-then-other"
                try:
                    assert isinstance(final_future_user_events, list)
                    # print(f"ar request time: {ar_request_time_aue}, ar feedback time: {ar_resolution_timestamp}")
                    # Only keep text edit events.
                    final_future_user_events = [
                        x for x in final_future_user_events if "content_changes" in x
                    ]
                    assert (
                        len(final_future_user_events) > 0
                    ), "final_future_user_events is empty after filtering content_changes when accept=True."
                    future_ue_index = find_index_ue_larger_than_time(
                        final_future_user_events, ar_resolution_timestamp
                    )
                    if future_ue_index >= len(final_future_user_events):
                        yield accept_then_other_data
                        return
                    next_user_event_after_accept = final_future_user_events[
                        future_ue_index
                    ]
                    next_ue_content_changes = next_user_event_after_accept[
                        "content_changes"
                    ]
                    assert (
                        len(next_ue_content_changes) >= 1
                    ), f"{future_ue_index}/{len(final_future_user_events)} has no content changes."
                    if next_user_event_after_accept["file_path"] != file_path:
                        yield accept_then_other_data
                        return
                    next_user_event_after_accept_start = next_ue_content_changes[0][
                        "crange"
                    ]["start"]
                    completion_start = num_chars_in_prefix
                    completion_end = completion_start + len(response_text)
                    # print(f"completion_start: {completion_start}, completion_end: {completion_end}")
                    if (
                        next_user_event_after_accept_start >= completion_start
                        and next_user_event_after_accept_start < completion_end
                    ):
                        yield accept_then_edit_data
                        return
                    else:
                        yield accept_then_other_data
                        return
                except Exception as _:
                    # print(f"Error: {e}, fall back to default.")
                    yield default_converted_data
            else:
                reject_all_data = copy.deepcopy(default_converted_data)
                reject_all_data["source"] = "raw@reject-all"
                reject_partial_data = copy.deepcopy(default_converted_data)
                reject_partial_data["source"] = "raw@reject-partial"  # over half
                try:
                    assert isinstance(final_future_user_events, list)
                    final_future_user_events = [
                        x for x in final_future_user_events if "content_changes" in x
                    ]
                    if len(final_future_user_events) == 0:
                        yield reject_all_data
                        return
                    future_ue_index = find_index_ue_larger_or_equal_time(
                        final_future_user_events, ar_resolution_timestamp
                    )
                    num_overlap_chars = compute_overlap(
                        file_path=data["prompt_input"]["path"],
                        prefix=data["prompt_input"]["prefix"],
                        suffix=data["prompt_input"]["suffix"],
                        completion=response_text,
                        user_event=[
                            TextEditEvent.schema().load(x)
                            for x in final_future_user_events[:future_ue_index]
                        ],
                    )
                    if num_overlap_chars < len(response_text) * 0.5:
                        yield reject_all_data
                    else:
                        yield reject_partial_data
                except Exception:
                    # print(f"Error: {e}, fall back to default.")
                    yield default_converted_data

    def filter_ar_by_user_agent(self) -> "UserEventData":
        """Filter out data based on user agent and skip_tokens."""
        print(">" * 10 + " Before Filter " + "<" * 10)
        inspect_skip_tokens(self.ar_data, MODEL2SKIP, missing_model_skip_token=None)
        print("-" * 100)

        missing_model: set[str] = set()
        filtered_ar_data: list[dict] = []
        skip_data_due_to_skip = 0
        skip_data_due_to_skip_after_20241118 = 0
        total_date_w_skip_after_20241118 = 0
        total_data_after_20241118 = 0
        for x in self.ar_data:
            user_agent = UserAgent(x["user_agent"])
            response_model = x["response_model"]
            if (response_model not in SC2_ELDEN_MODELS) and (
                response_model not in QWEN_ELDEN_MODELS
            ):
                missing_model.add(response_model)
                continue
            if str2datetimefloat(x["timestamp"]) >= str2datetimefloat("2024-11-18"):
                total_data_after_20241118 += 1
            skip_token = MODEL2SKIP[response_model]
            # There is a bug (https://linear.app/augmentcode/issue/AU-5141/review-skip-behavior-due-to-low-user-acceptance-rates)
            # that we need to filter out the data with AGENT_FIXING_SKIP version
            if (skip_token in x["generated_token_ids"]) and str2datetimefloat(
                x["timestamp"]
            ) >= str2datetimefloat("2024-11-18"):
                total_date_w_skip_after_20241118 += 1
            if (
                (skip_token in x["generated_token_ids"])
                and (user_agent.agent_type == AgentType.VSCODE)
                and (user_agent.version < AGENT_FIXING_SKIP[AgentType.VSCODE])
            ):
                skip_data_due_to_skip += 1
                if str2datetimefloat(x["timestamp"]) >= str2datetimefloat("2024-11-18"):
                    skip_data_due_to_skip_after_20241118 += 1
                continue

            filtered_ar_data.append(x)
        print(f"There are {len(missing_model)} missing models: {missing_model}")
        print(
            f"Skip {skip_data_due_to_skip:7d} / {len(self.ar_data):7d} data due to skip token."
        )
        print(
            f"Skip {skip_data_due_to_skip_after_20241118:7d} / {total_date_w_skip_after_20241118:7d} / {total_data_after_20241118:7d} data due to skip token after 2024-11-18."
        )
        print(">" * 10 + " After Filter " + "<" * 10)
        inspect_skip_tokens(filtered_ar_data, MODEL2SKIP, missing_model_skip_token=None)
        print("-" * 100)
        return UserEventData(filtered_ar_data, self.hs_data, self.raw_ue_data)

    def filter_ar_by_duplication_request_id(self) -> "UserEventData":
        """Filter out the data with duplicated request id."""
        request_id_to_index_ar: dict[str, list[int]] = collections.defaultdict(list)
        for index, x in enumerate(self.ar_data):
            request_id_to_index_ar[x["request_id"]].append(index)
        new_ar_data: list[dict] = []
        for index in request_id_to_index_ar.values():
            if len(index) == 1:
                new_ar_data.append(self.ar_data[index[0]])
        return UserEventData(new_ar_data, self.hs_data, self.raw_ue_data)

    def filter_hs_by_missing_request_id_and_identical_response(self) -> "UserEventData":
        """Filter out the data with missing request id and identical response."""
        request_id_to_index_hs: dict[str, list[int]] = collections.defaultdict(list)
        for index, x in enumerate(self.hs_data):
            request_id_to_index_hs[x["request_id"]].append(index)
        hs_data_v1: list[dict] = []
        for index in request_id_to_index_hs.values():
            if len(index) == 1:
                hs_data_v1.append(self.hs_data[index[0]])
        hs_data_v2: list[dict] = []
        for x in hs_data_v1:
            # if x["response_text"].rstrip() != x["ground_truth"].rstrip():
            if x["response_text"].strip() != x["ground_truth"].strip():
                hs_data_v2.append(x)
        return UserEventData(self.ar_data, hs_data_v2, self.raw_ue_data)

    def dump(self, output_path: pathlib.Path):
        """Dump the data via pickle."""
        parent_folder = output_path.parent
        assert parent_folder.exists()
        with output_path.open("wb") as f:
            pickle.dump(self, f)

    @staticmethod
    def load(input_path: pathlib.Path) -> "UserEventData":
        """Load the data via pickle."""
        with input_path.open("rb") as f:
            return pickle.load(f)


def load_data_as_dataset(
    date_ranges: list[str],
    input_dir: pathlib.Path,
) -> UserEventData:
    """Process the data as a dataset."""
    assert input_dir.exists(), input_dir
    all_date_ranges = []
    for cur_date_range in date_ranges:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    print(f"There are {len(all_date_ranges)} date ranges.")
    accept_reject_files: list[pathlib.Path] = []
    hindsight_files: list[pathlib.Path] = []
    user_event_files: list[pathlib.Path] = []
    for date, xstart, xend in all_date_ranges:
        accept_reject_dir = input_dir / f"{date}-accept-reject"
        if accept_reject_dir.exists():
            accept_reject_files.extend(list(accept_reject_dir.glob("*-samples.jsonl")))
        hindsight_dir = input_dir / f"{date}-hindsight"
        if hindsight_dir.exists():
            hindsight_files.extend(list(hindsight_dir.glob("*-samples.jsonl")))
        user_event_dir = input_dir / f"{date}-user-event"
        if user_event_dir.exists():
            user_event_files.extend(list(user_event_dir.glob("*-samples.jsonl")))
        print(f"{date}: {accept_reject_dir}")
    assert len(accept_reject_files) > 0, "Can not find accept/reject files."
    assert len(hindsight_files) > 0, "Can not find hindsight files."
    assert len(user_event_files) > 0, "Can not find user event files."
    print(f"There are {len(accept_reject_files)} accept/reject files")
    print(f"There are {len(hindsight_files)} hindsight files")
    all_ar_data: list[dict] = []
    for accept_reject_file in tqdm.tqdm(
        accept_reject_files, desc="Load accept/reject files"
    ):
        for data in utils_for_file.read_jsonl(accept_reject_file):
            data["source_file"] = str(accept_reject_file)
            all_ar_data.append(data)
    all_hs_data: list[dict] = []
    all_ue_data: list[dict] = []
    for user_event_file in tqdm.tqdm(user_event_files, desc="Load user event files"):
        for data in utils_for_file.read_jsonl(user_event_file):
            all_ue_data.append(data)
    for hindsight_file in tqdm.tqdm(hindsight_files, desc="Load hindsight files"):
        for data in utils_for_file.read_jsonl(hindsight_file):
            all_hs_data.append(data)
    return UserEventData(all_ar_data, all_hs_data, all_ue_data)
