"""Create the rlhf dataset for completion models.

Be careful about data before 2024-08-06: https://www.notion.so/2024-08-13-Acceptance-Increase-283cf387c0d94ee5a654833072daab7f#b2aa9e41f1b24f33b097c68bf23b9d36

python experimental/dxy/rag/rlhf/create-rlhf-dataset-via-accept-reject.py --stage dump --date_range 20240601-20240630
python experimental/dxy/rag/rlhf/create-rlhf-dataset-via-accept-reject.py --stage dump --date_range 20240801-20241029

Stage 1: output to /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012

python experimental/dxy/rag/rlhf/create-rlhf-dataset-via-accept-reject.py --stage process --date_range 20240801-20241024 \
    --skip_hindsight --merge_skip_and_no_skip --output_file 20240801-20241024-augment

python experimental/dxy/rag/rlhf/create-rlhf-dataset-via-accept-reject.py --stage process --date_range 20240701-20241007 \
    --skip_hindsight --merge_skip_and_no_skip --output_file 20240701-20241007-augment

python experimental/dxy/rag/rlhf/create-rlhf-dataset-via-accept-reject.py --stage process --date_range 20241001-20241024 \
    --skip_hindsight --merge_skip_and_no_skip --output_file 20241001-20241024-augment

python experimental/dxy/rag/rlhf/create-rlhf-dataset-via-accept-reject.py --stage process --date_range 20241025-20241026 \
    --no_augment --remove_conflict_signals --skip_hindsight --output_file 20241025-20241026-noaug

Stage 2: output to /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata
- 136K+ items in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241001-20241024-augment
- 365K+ items in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240801-20241024-augment (119K accept vs. 245K reject)
- 331K+ items in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240701-20241007-augment (107K accept vs. 223K reject)
- 167K+ items (1K+ conflicts) in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241014-noaugment (83K accept vs. 83K reject)
- 233K+ items (4K+ conflicts) in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241014-augment (83K accept vs. 153K reject)
- 182K+ items (2K+ conflicts) in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241001-augment
- 251K+ items (4K+ conflicts) in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241017-exclude1014-augment (90K accept vs. 166K reject)
- 265K+ items (4K+ conflicts) in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20240501-20241020-augment (169K accept vs. 95K reject)
- 4707 items in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241014-noaugment
- 5335 items in /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1012-to-rmdata/20241017-noaugment
"""

import argparse
import collections
import pathlib
import random

import numpy as np
import tqdm
from datasets import Dataset as HFDataset

from base.datasets import completion, tenants
from base.datasets.completion_dataset import CompletionDataset, CompletionDatum
from base.prompt_format.common import PromptChunk
from base.prompt_format_completion.prompt_formatter import PromptInput
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.tokenizers.tokenizer import RagSpecialTokens
from experimental.dxy.rag.rlhf.shared_lib import traverse_date_range
from research.core import utils_for_file

ELDEN_MODELS: list[str] = [
    "eldenv2-15b",
    "eldenv3-15b",
    "eldenv4-15b",
    "eldenv4-1-15b",
    "eldenv4-2-15b",
    "eldenv4-3-15b",
    "eldenv4-0b-15b",
    "eldenv4-0c-15b",
    "eldenv4-0d-15b",
    "eldenv4-4a-15b",
    "eldenv4-stateful",
    "eldenv6-15b",
    "eldenv6-1-15b",
    "eldenv7-0-15b",
]


def convert_datum_to_prompt_input(data: CompletionDatum):
    assert data.resolution is not None
    # chunks: list[PromptChunk] = []
    # for x in data.response.retrieved_chunks:
    #     unique_id = f"{x.blob_name}:{x.crange.start}-{x.crange.stop}"
    #     chunks.append(
    #         PromptChunk(
    #             text=x.text,
    #             path=x.path,
    #             unique_id=unique_id,
    #             origin=x.origin,
    #             char_start=x.crange.start,
    #             char_end=x.crange.stop,
    #             blob_name=x.blob_name,
    #         )
    #     )
    prompt_input = PromptInput(
        prefix=data.request.prefix,
        suffix=data.request.suffix,
        prefix_begin=0,
        path=data.request.path,
        retrieved_chunks=[],
        # retrieved_chunks=chunks,
        lang=None,
    )
    if data.feedback is None:
        feedback = None
    else:
        feedback = completion.CompletionFeedback.schema().dump(data.feedback)
    if data.inference_response is None:
        inference_token_ids = None
        assert False, "inference_response is None"
    else:
        inference_token_ids = data.inference_response.token_ids
    assert data.user_agent is not None
    resolution = completion.CompletionResolution.schema().dump(data.resolution)
    return {
        "prompt_input": {
            "prefix": prompt_input.prefix,
            "suffix": prompt_input.suffix,
            "prefix_begin": prompt_input.prefix_begin,
            "path": prompt_input.path,
            # "retrieved_chunks": [PromptChunk.schema().dump(x) for x in chunks],
            "lang": prompt_input.lang,
        },
        "request_id": data.request_id,
        "user_id": data.user_id,
        # "prompt_tokens": data.response.prompt_tokens,
        "prompt_token_ids": data.response.prompt_token_ids,
        # "generated_tokens": data.response.tokens,
        "generated_token_ids": data.response.token_ids,
        # "generated_token_log_probs": data.response.token_log_probs,
        "timestamp": str(data.response.timestamp),
        "response_text": data.response.text,
        "response_model": data.response.model,
        "resolution": resolution,
        "accepted": data.resolution.accepted,
        "feedback": feedback,
        "inference_token_ids": inference_token_ids,
        "user_agent": data.user_agent,
    }


def dump_raw_data(
    date_ranges: list[str],
    tenant_names: list[str],
    limit: int = 50_000,
    root_out_dir: pathlib.Path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal"
    ),
):
    # Start from Augest 1, the accept/reject calculation is changed: https://www.notion.so/2024-08-13-Acceptance-Increase-283cf387c0d94ee5a654833072daab7f
    all_date_ranges = []
    for cur_date_range in date_ranges:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    print(f"There are {len(all_date_ranges)} date ranges.")
    for date, xstart, xend in all_date_ranges:
        print(f"{date}: {xstart} - {xend}")
    for index, (date, xstart, xend) in enumerate(all_date_ranges):
        output_dir = root_out_dir / f"{date}-accept-reject"
        print(f"Process {index:03d}/{len(all_date_ranges)}: {date}")
        for tenant_name in tenant_names:
            datum_list: list[dict] = []
            for accept in (True, False):
                completion_filters = CompletionDataset.Filters(
                    model_names=ELDEN_MODELS,
                    min_completion_length=1,
                    timestamp_begin=xstart,
                    timestamp_end=xend,
                    accepted_completion=accept,
                )
                completions = CompletionDataset.create(
                    tenant=tenants.get_tenant(tenant_name),
                    filters=completion_filters,
                    limit=limit,
                    order_by=None,
                    page_size=8192,
                    queue_size=50,
                )
                print(
                    f"Query {completions.num_rows}/{limit=} with {accept=} completions from {tenant_name}."
                )
                for x in tqdm.tqdm(
                    completions.get_completions(),
                    desc=f"Get {date} {tenant_name} {accept=} data",
                    total=completions.num_rows,
                    smoothing=0.1,
                ):
                    x_json = convert_datum_to_prompt_input(x)
                    datum_list.append(x_json)
            if len(datum_list) == 0:
                print(f"Skip {date} {tenant_name} as there is no data.")
                continue
            output_dir.mkdir(exist_ok=True, parents=False)
            output_path = (
                output_dir / f"{tenant_name}-raw-data-{len(datum_list)}-samples.jsonl"
            )
            utils_for_file.write_jsonl(output_path, datum_list)


def num_tokens_to_bucket(num: int) -> int:
    if num <= 7:
        return 7
    elif 8 <= num <= 29:
        return (num // 2) * 2
    else:
        return num


def dedup_data_comprehensive(all_data: list[dict], num_per_key: int) -> list[dict]:
    """A pretty aggressive deduplication approach."""
    data_by_special_keys = collections.defaultdict(list)
    all_token_buckets = set()
    for x in tqdm.tqdm(all_data, desc="analyze"):
        if x["is_from_hindsight"]:
            key = (
                x["user_id"],
                x["timestamp"].split(" ")[0],  # date
                x["prompt_input"]["path"],  # file path
                len(x["prompt_input"]["prefix"].splitlines()) // 3,  # Every 3 lines
                num_tokens_to_bucket(len(x["ground_truth_tokens_ids"])),
            )
        else:
            key = (
                x["user_id"],
                x["timestamp"].split(" ")[0],  # date
                x["prompt_input"]["path"],  # file path
                x["accepted"],  # accept or reject
                len(x["prompt_input"]["prefix"].splitlines()) // 3,  # Every 3 lines
                num_tokens_to_bucket(len(x["generated_token_ids"])),
            )
        data_by_special_keys[key].append(x)
        all_token_buckets.add(key[-1])
    print(f"There are {len(data_by_special_keys)}/{len(all_data)} unique keys.")
    print(f"There are {len(all_token_buckets)} token buckets: {all_token_buckets}")
    length_per_key = []
    for key, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_comprehensive] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    sampled_data: list[dict] = []
    for key, data_list in data_by_special_keys.items():
        if key[-1] > 72:
            cur_num_per_key = num_per_key * 3
        elif key[-1] > 28:
            cur_num_per_key = num_per_key * 2
        else:
            cur_num_per_key = num_per_key
        sampled_data.extend(
            random.sample(data_list, min(cur_num_per_key, len(data_list)))
        )
    print(
        f"[dedup_data_comprehensive] Sampled {len(sampled_data)} data by {num_per_key=}."
    )
    return sampled_data


def dedup_data_simple(all_data: list[dict]) -> list[dict]:
    """Simple deduplication based on the number of tokens."""
    data_by_special_keys = collections.defaultdict(list)
    for x in tqdm.tqdm(all_data, desc="analyze"):
        if x["accepted"] is None:
            key = (x["accepted"], len(x["ground_truth_tokens_ids"]))
        else:
            key = (x["accepted"], len(x["generated_token_ids"]))
        data_by_special_keys[key].append(x)
    print(f"There are {len(data_by_special_keys)}/{len(all_data)} unique keys.")
    length_per_key = []
    for key, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_simple] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    num_per_key = (
        max(length_per_key.mean().item(), np.median(length_per_key).item()) * 2.5
    )
    num_per_key = int(num_per_key) + 1
    sampled_data: list[dict] = []
    for key, data_list in data_by_special_keys.items():
        sampled_data.extend(random.sample(data_list, min(num_per_key, len(data_list))))
    print(f"[dedup_data_simple] Sampled {len(sampled_data)} data by {num_per_key=}.")
    return sampled_data


def split_data_via_skip(
    all_data: list[dict], skip_token: int
) -> tuple[list[dict], list[dict]]:
    """Split the data into two parts: with skip token and without skip token."""
    data_with_skip: list[dict] = []
    data_without_skip: list[dict] = []
    for x in tqdm.tqdm(all_data, desc="split"):
        if skip_token in x["generated_token_ids"]:
            data_with_skip.append(x)
        else:
            data_without_skip.append(x)
    print(
        f"There are {len(data_with_skip):4d}/{len(all_data):4d} data with skip token."
    )
    print(
        f"There are {len(data_without_skip):4d}/{len(all_data):4d} data without skip token."
    )
    num_accepted_with_skip = len([x for x in data_with_skip if x["accepted"]])
    num_rejected_with_skip = len([x for x in data_with_skip if not x["accepted"]])
    print(
        f"There are {num_accepted_with_skip:4d} accepted data vs."
        f"{num_rejected_with_skip:4d} rejected data with skip token."
    )
    return data_with_skip, data_without_skip


def process_data_list_for_dataset(
    all_data: list[dict],
    special_tokens: RagSpecialTokens,
    num_per_key: int,
    augment: bool,
    remove_conflict_signals: bool,
) -> tuple[list[list[int]], list[list[int]], list[list[int]]]:
    """Process the data list to build the dataset."""
    # Analyze the data
    num_feedback_indexes = []
    for index in range(len(all_data)):
        if all_data[index]["feedback"] is not None:
            num_feedback_indexes.append(index)
    # Analyze the data again by the number of tokens
    num_tokens_per_data_before_dedup = collections.defaultdict(int)
    for data in tqdm.tqdm(all_data, desc="analyze"):
        cur_num_tokens = (
            len(data["generated_token_ids"])
            if data["accepted"] is not None
            else len(data["ground_truth_tokens_ids"])
        )
        num_tokens_per_data_before_dedup[cur_num_tokens] += 1
    # num_tokens_keys = sorted(list(num_tokens_per_data_before_dedup.keys()))
    # for key in num_tokens_keys:
    #     print(f"There are {num_tokens_per_data[key]:3d} data with {key:3d} tokens.")
    # No-feedback is provided for all data!!!!
    print(f"There are {len(num_feedback_indexes)}/{len(all_data)} data have feedback.")
    sampled_data_v1 = dedup_data_comprehensive(all_data, num_per_key)
    print(f"There are {len(sampled_data_v1)} data after sample {num_per_key=}.")
    # Analyze the data again by the number of tokens
    num_tokens_per_data_after_v1dedup = collections.defaultdict(int)
    for data in tqdm.tqdm(sampled_data_v1, desc="analyze"):
        cur_num_tokens = (
            len(data["generated_token_ids"])
            if not data["is_from_hindsight"]
            else len(data["ground_truth_tokens_ids"])
        )
        num_tokens_per_data_after_v1dedup[cur_num_tokens] += 1
    sampled_data_v2 = dedup_data_simple(sampled_data_v1)
    num_tokens_per_data_after_v2dedup = collections.defaultdict(int)
    for data in tqdm.tqdm(sampled_data_v2, desc="analyze"):
        cur_num_tokens = (
            len(data["generated_token_ids"])
            if not data["is_from_hindsight"]
            else len(data["ground_truth_tokens_ids"])
        )
        num_tokens_per_data_after_v2dedup[cur_num_tokens] += 1
    num_tokens_keys = sorted(list(num_tokens_per_data_after_v2dedup.keys()))
    for key in num_tokens_keys:
        print(
            f"There are {num_tokens_per_data_after_v2dedup[key]:3d}/{num_tokens_per_data_after_v1dedup[key]:3d}/{num_tokens_per_data_before_dedup[key]:3d} data with {key:3d} tokens."
        )

    all_prompt_tokens: list[list[int]] = []
    all_target_tokens: list[list[int]] = []
    all_signals: list[list[int]] = []
    num_hs, num_fail, num_skip_in_target, num_empty = 0, 0, 0, 0
    for data in tqdm.tqdm(
        sampled_data_v2,
        total=len(sampled_data_v2),
        desc="build dataset",
    ):
        prompt_tokens = data["prompt_token_ids"]
        is_from_hindsight = data["is_from_hindsight"]
        if is_from_hindsight:
            # NOTE(Xuanyi): whether we should use eos token or not here is questionable,
            # as it can not handle skip behaviors
            target_tokens = data["ground_truth_tokens_ids"] + [special_tokens.eos]
            accepted = True
        else:
            # target_tokens = data["generated_token_ids"]
            target_tokens = data["inference_token_ids"]
            assert len(data["inference_token_ids"]) in (
                len(data["generated_token_ids"]),
                len(data["generated_token_ids"]) + 1,
            )
            accepted = data["accepted"]
        if prompt_tokens is None or len(prompt_tokens) == 0 or len(target_tokens) == 0:
            print(f"Prompt/Target tokens is empty for {data['request_id']}.")
            num_fail += 1
            continue
        if special_tokens.pause in target_tokens:
            assert (
                special_tokens.pause not in target_tokens[:-1]
            ), f"{data['request_id']} should not contain pause token in the target."
        if is_from_hindsight:
            assert (
                special_tokens.skip not in target_tokens
            ), f"{data['request_id']} should not contain skip token in the target."
        if special_tokens.skip in target_tokens:
            num_skip_in_target += 1
        assert accepted is not None

        # Build the dataset
        if len(target_tokens) == 0:
            num_empty += 1
        elif len(target_tokens) <= 10:
            all_prompt_tokens.append(prompt_tokens)
            all_target_tokens.append(target_tokens)
            all_signals.append([int(accepted)])
        # These logic is no longer needed, as we switch to use inference_token_ids
        # elif len(target_tokens) in (64, 96):
        #     # We used to set the number of maximum output tokens to be 64 or 96.
        #     # Thus, in such cases, we can not figure out whether the target is finished or not. -> so that we do not add the eos token.
        #     all_prompt_tokens.append(prompt_tokens)
        #     all_target_tokens.append(target_tokens)
        #     all_signals.append([int(accepted)])
        else:
            # We consider correct, if the target tokens are finished.
            all_prompt_tokens.append(prompt_tokens)
            all_target_tokens.append(target_tokens)
            all_signals.append([int(accepted)])
            # if accepted and augment:  # Augment the data to be partially of the response
            if accepted and augment:  # Augment the data to be partially of the response
                index_of_partial = random.randint(0, len(target_tokens) - 1)
                all_prompt_tokens.append(prompt_tokens)
                all_target_tokens.append(
                    target_tokens[:index_of_partial] + [special_tokens.eos]
                )
                all_signals.append([int(False)])
    num_accepted = len([x for x in all_signals if x[0] == 1])
    num_rejected = len([x for x in all_signals if x[0] == 0])
    print(f"There are {num_accepted}/{len(all_signals)} accepted data.")
    print(f"There are {num_rejected}/{len(all_signals)} rejected data.")
    print(
        f"There are {num_hs} hindsight, {num_fail} failures, {num_empty} empties, and {len(all_prompt_tokens)} examples."
    )
    print(f"There are {num_skip_in_target} examples having the skip token in target.")
    # Analyze the data again by the number of tokens
    all_prompt_lengths, all_target_lengths, all_token_lengths = [], [], []
    for prompt_tokens, target_tokens in zip(all_prompt_tokens, all_target_tokens):
        all_prompt_lengths.append(len(prompt_tokens))
        all_target_lengths.append(len(target_tokens))
        all_token_lengths.append(len(prompt_tokens) + len(target_tokens))
    all_prompt_lengths = np.array(all_prompt_lengths)
    all_target_lengths = np.array(all_target_lengths)
    all_token_lengths = np.array(all_token_lengths)
    print(
        f"Prompt: {all_prompt_lengths.min()}~{all_prompt_lengths.max()} mean={all_prompt_lengths.mean()}, std={all_prompt_lengths.std()}"
    )
    print(
        f"Target: {all_target_lengths.min()}~{all_target_lengths.max()} mean={all_target_lengths.mean()}, std={all_target_lengths.std()}"
    )
    print(
        f"Prompt + Target: {all_token_lengths.min()}~{all_token_lengths.max()} mean={all_token_lengths.mean()}, std={all_token_lengths.std()}"
    )

    # Try to deduplicate the prompt tokens + target tokens
    key2all: dict[str, list[tuple[list[int], list[int], list[int]]]] = (
        collections.defaultdict(list)
    )
    for prompt_tokens, target_tokens, signal in zip(
        all_prompt_tokens, all_target_tokens, all_signals
    ):
        key2all[str(prompt_tokens + target_tokens)].append(
            (prompt_tokens, target_tokens, signal)
        )
    num_conflict_signals = 0
    for key, all_data_per_key in key2all.items():
        if len(all_data_per_key) > 1:
            num_conflict_signals += 1
    print(
        f"There are {len(key2all)} unique prompt+target, {num_conflict_signals} pairs have conflict signals."
    )
    if remove_conflict_signals:
        new_all_prompt_tokens, new_all_target_tokens, new_all_signals = [], [], []
        for prompt_tokens, target_tokens, signal in zip(
            all_prompt_tokens, all_target_tokens, all_signals
        ):
            if len(key2all[str(prompt_tokens + target_tokens)]) == 1:
                new_all_prompt_tokens.append(prompt_tokens)
                new_all_target_tokens.append(target_tokens)
                new_all_signals.append(signal)
    else:
        new_all_prompt_tokens, new_all_target_tokens, new_all_signals = (
            all_prompt_tokens,
            all_target_tokens,
            all_signals,
        )
    print(
        f"After removing conflict signals ({remove_conflict_signals=}): {len(new_all_prompt_tokens)}/{len(all_prompt_tokens)} items."
    )
    return new_all_prompt_tokens, new_all_target_tokens, new_all_signals


def analyze_tokens(
    all_prompt_tokens: list[list[int]],
    all_target_tokens: list[list[int]],
    all_signals: list[list[int]],
):
    all_prompt_lengths, all_target_lengths, all_token_lengths = [], [], []
    for prompt_tokens, target_tokens in zip(all_prompt_tokens, all_target_tokens):
        all_prompt_lengths.append(len(prompt_tokens))
        all_target_lengths.append(len(target_tokens))
        all_token_lengths.append(len(prompt_tokens) + len(target_tokens))
    all_prompt_lengths = np.array(all_prompt_lengths)
    all_target_lengths = np.array(all_target_lengths)
    all_token_lengths = np.array(all_token_lengths)
    print(
        f"Prompt: {all_prompt_lengths.min()}~{all_prompt_lengths.max()}"
        f" mean={all_prompt_lengths.mean()},"
        f" std={all_prompt_lengths.std()}"
    )
    print(
        f"Target: {all_target_lengths.min()}~{all_target_lengths.max()}"
        f" mean={all_target_lengths.mean()}, std={all_target_lengths.std()}"
    )
    print(
        f"Prompt + Target: {all_token_lengths.min()}~{all_token_lengths.max()}"
        f" mean={all_token_lengths.mean()}, std={all_token_lengths.std()}"
    )
    num_accepted = len([x for x in all_signals if x[0] == 1])
    num_rejected = len([x for x in all_signals if x[0] == 0])
    print(f"There are {num_accepted}/{len(all_signals)} accepted data.")
    print(f"There are {num_rejected}/{len(all_signals)} rejected data.")


def process_data_as_dataset(
    date_ranges: list[str],
    input_dir: pathlib.Path,
    output_dir: pathlib.Path,
    num_per_key: int,
    augment: bool,
    remove_conflict_signals: bool,
    skip_hindsight: bool = True,
    merge_skip_and_no_skip: bool = True,
):
    """Process the data as a dataset."""
    assert not output_dir.exists(), f"{output_dir} already exists."
    tokenizer = StarCoder2Tokenizer()
    special_tokens = tokenizer.special_tokens
    assert input_dir.exists(), input_dir
    all_date_ranges = []
    for cur_date_range in date_ranges:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    print(f"There are {len(all_date_ranges)} date ranges.")
    accept_reject_files: list[pathlib.Path] = []
    hindsight_files: list[pathlib.Path] = []
    for date, xstart, xend in all_date_ranges:
        accept_reject_dir = input_dir / f"{date}-accept-reject"
        if accept_reject_dir.exists():
            accept_reject_files.extend(list(accept_reject_dir.glob("*-samples.jsonl")))
        hindsight_dir = input_dir / f"{date}-hindsight"
        if hindsight_dir.exists():
            hindsight_files.extend(list(hindsight_dir.glob("*-samples.jsonl")))
        print(f"{date}: {accept_reject_dir}")
    assert len(accept_reject_files) > 0, "Can not find accept/reject files."
    if len(hindsight_files) == 0:
        print("Can not find hindsight files.")
    print(f"There are {len(accept_reject_files)} accept/reject files")
    print(f"There are {len(hindsight_files)} hindsight files")
    print(f"Config: {augment=} {remove_conflict_signals=} {num_per_key=}")
    all_data: list[dict] = []
    for accept_reject_file in tqdm.tqdm(
        accept_reject_files, desc="Load accept/reject files"
    ):
        for data in utils_for_file.read_jsonl(accept_reject_file):
            data["is_from_hindsight"] = False
            all_data.append(data)
    # Load the hindsight data
    if not skip_hindsight:
        num_hindsight = 0
        drop_due_to_model, drop_due_to_skip, drop_due_to_long = 0, 0, 0
        drop_due_to_identical, drop_due_to_short = 0, 0
        invalid_models = set()
        for hindsight_file in tqdm.tqdm(
            hindsight_files, total=len(hindsight_files), desc="Load hindsight files"
        ):
            for data in utils_for_file.read_jsonl(hindsight_file):
                if data["response_model"] not in ELDEN_MODELS:
                    drop_due_to_model += 1
                    invalid_models.add(data["response_model"])
                    continue
                if (
                    data["generated_token_ids"] == []
                    or special_tokens.skip in data["generated_token_ids"]
                ):
                    drop_due_to_skip += 1
                    continue
                ground_truth_tokens_ids = tokenizer.tokenize_safe(data["ground_truth"])
                if len(ground_truth_tokens_ids) > 256:
                    drop_due_to_long += 1
                    continue
                if data["generated_token_ids"] == ground_truth_tokens_ids:
                    drop_due_to_identical += 1
                    continue
                if len(ground_truth_tokens_ids) < 6:
                    drop_due_to_short += 1
                    continue
                data["ground_truth_tokens_ids"] = ground_truth_tokens_ids
                data["accepted"] = None
                data["is_from_hindsight"] = True
                num_hindsight += 1
                all_data.append(data)
        print(
            f"There are {num_hindsight} hindsight data"
            f", dropped {drop_due_to_model} due to model"
            f", dropped {drop_due_to_skip} due to skip"
            f", dropped {drop_due_to_long} due to long"
            f", dropped {drop_due_to_short} due to short"
            f", dropped {drop_due_to_identical} due to identical."
        )
        print(f"Invalid models: {invalid_models}.")
    else:
        print("Skip hindsight data.")

    all_data_w_skip, all_data_wo_skip = split_data_via_skip(
        all_data, special_tokens.skip
    )

    all_prompt_tokens_w_skip, all_target_tokens_w_skip, all_signals_w_skip = (
        process_data_list_for_dataset(
            all_data_w_skip,
            special_tokens,
            num_per_key,
            augment,
            remove_conflict_signals,
        )
    )
    all_prompt_tokens_wo_skip, all_target_tokens_wo_skip, all_signals_wo_skip = (
        process_data_list_for_dataset(
            all_data_wo_skip,
            special_tokens,
            num_per_key,
            augment,
            remove_conflict_signals,
        )
    )
    if merge_skip_and_no_skip:
        all_prompt_tokens = all_prompt_tokens_w_skip + all_prompt_tokens_wo_skip
        all_target_tokens = all_target_tokens_w_skip + all_target_tokens_wo_skip
        all_signals = all_signals_w_skip + all_signals_wo_skip
        print("-" * 40 + " Merged " + "-" * 40)
        analyze_tokens(all_prompt_tokens, all_target_tokens, all_signals)
        print("-" * 80)
        output_dir.mkdir(exist_ok=True, parents=False)
        print(f"Try to write {len(all_prompt_tokens)} into {output_dir}")
        dataset = HFDataset.from_dict(
            {
                "prompt_tokens": all_prompt_tokens,
                "target_tokens": all_target_tokens,
                "is_positive": all_signals,
            }
        )
        print("Create HFDataset done.")
        dataset.save_to_disk(str(output_dir))
        print(f"Save {len(dataset)} items into {output_dir}.")
    else:
        print("-" * 40 + " Split by with skip tokens " + "-" * 40)
        analyze_tokens(
            all_prompt_tokens_w_skip, all_target_tokens_w_skip, all_signals_w_skip
        )
        output_dir_w_skip = output_dir.parent / f"{output_dir.name}-with-skip"
        output_dir_w_skip.mkdir(exist_ok=True, parents=False)
        dataset = HFDataset.from_dict(
            {
                "prompt_tokens": all_prompt_tokens_w_skip,
                "target_tokens": all_target_tokens_w_skip,
                "is_positive": all_signals_w_skip,
            }
        )
        dataset.save_to_disk(output_dir_w_skip)
        print(f"Save {len(dataset)} items into {output_dir_w_skip}.")
        print("-" * 40 + " Split by without skip tokens " + "-" * 40)
        analyze_tokens(
            all_prompt_tokens_wo_skip, all_target_tokens_wo_skip, all_signals_wo_skip
        )
        output_dir_wo_skip = output_dir.parent / f"{output_dir.name}-without-skip"
        output_dir_wo_skip.mkdir(exist_ok=True, parents=False)
        dataset = HFDataset.from_dict(
            {
                "prompt_tokens": all_prompt_tokens_wo_skip,
                "target_tokens": all_target_tokens_wo_skip,
                "is_positive": all_signals_wo_skip,
            }
        )
        dataset.save_to_disk(output_dir_wo_skip)
        print(f"Save {len(dataset)} items into {output_dir_wo_skip}.")
        print("-" * 80)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--stage",
        type=str,
        choices=["dump", "process"],
        required=True,
    )
    parser.add_argument(
        "--output_file",
        type=str,
        default=None,
    )
    parser.add_argument(
        "--remove_conflict_signals",
        action="store_true",
        default=False,
        help="Whether to remove the conflict signals.",
    )
    parser.add_argument(
        "--no_augment",
        action="store_true",
        default=False,
        help="Whether to augment the data.",
    )
    parser.add_argument(
        "--skip_hindsight",
        action="store_true",
        default=False,
        help="Whether to skip the hindsight data.",
    )
    parser.add_argument(
        "--merge_skip_and_no_skip",
        action="store_true",
        default=False,
        help="Whether to skip the hindsight data.",
    )
    args = parser.parse_args()

    root_out_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031"
    )
    processed_out_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1031-to-rmdata"
    )
    if args.stage == "dump":
        dump_raw_data(
            date_ranges=args.date_range,
            # tenant_names=["dogfood", "dogfood-shard", "aitutor-turing"],
            tenant_names=["dogfood", "dogfood-shard", "i0-vanguard0", "aitutor-turing"],
            limit=100_000,
            root_out_dir=root_out_dir,
        )
    elif args.stage == "process":
        processed_out_dir.mkdir(exist_ok=True, parents=False)
        assert args.output_file is not None
        assert "/" not in args.output_file
        process_data_as_dataset(
            date_ranges=args.date_range,
            input_dir=root_out_dir,
            output_dir=processed_out_dir / args.output_file,
            num_per_key=1,
            augment=not args.no_augment,
            remove_conflict_signals=args.remove_conflict_signals,
            skip_hindsight=args.skip_hindsight,
            merge_skip_and_no_skip=args.merge_skip_and_no_skip,
        )
    else:
        raise ValueError(f"Unknown stage: {args.stage}")
