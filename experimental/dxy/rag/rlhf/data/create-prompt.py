"""Create the RLHF model's training dataset for the completion models.

python experimental/dxy/rag/rlhf/data/create-prompt.py \
    --input_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-Q<PERSON><PERSON> \
    --output_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN-prompt-vanguard

python experimental/dxy/rag/rlhf/data/create-prompt.py \
    --keep_dogfood \
    --input_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN \
    --output_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20241001-1130-QWEN-prompt-all

python experimental/dxy/rag/rlhf/data/create-prompt.py \
    --keep_dogfood \
    --input_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN \
    --output_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/20240801-1231-QWEN-innocent-prompt-all

python experimental/dxy/rag/rlhf/data/create-prompt.py \
    --keep_dogfood \
    --input_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN \
    --output_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN-innocent-prompt-all

python experimental/dxy/rag/rlhf/data/create-prompt.py \
    --input_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN \
    --output_dir /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114-to-rmdata/240801-250119-QWEN-innocent-prompt-vanguard
"""

import argparse
import collections
import copy
import pathlib
import random

import numpy as np
import pandas as pd
import tqdm
from datasets import Dataset as HFDataset

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer

# NON_PERMISSIVE_DATA_PATH = (
#     "/mnt/efs/spark-data/shared/license_detector/non_permissive_requests/"
# )
NON_PERMISSIVE_DATA_PATH = (
    "/mnt/efs/augment/user/dxy/datasets/rlhf/non_permissive_rids-20250119/"
)

qwen_tokenizer = Qwen25CoderTokenizer()
qwen_special_tokens = qwen_tokenizer.special_tokens


def num_tokens_to_bucket(num: int) -> int:
    if num <= 6:
        return 6
    elif num == 7:
        return 7
    elif 8 <= num <= 29:
        return (num // 2) * 2
    else:
        return num


def dedup_data_simple(
    all_data_list: list[dict],
) -> list[dict]:
    """Simple deduplication based on the number of tokens."""
    data_by_special_keys = collections.defaultdict(list)
    for cur_data in tqdm.tqdm(all_data_list, desc="analyze"):
        # prompt_tokens,  = cur_data["prompt_tokens"], cur_data["target_tokens"]
        target_tokens = cur_data["target_tokens"]
        data_by_special_keys[len(target_tokens)].append(cur_data)
    print(f"There are {len(data_by_special_keys)}/{len(all_data_list)} unique keys.")
    keys = sorted(list(data_by_special_keys.keys()))
    for key in keys:
        data_list = data_by_special_keys[key]
        print(f"There are {len(data_list)} data with {key:02d} tokens.")
    length_per_key = []
    for _, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_simple] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    num_per_key = max(length_per_key.mean().item(), np.median(length_per_key).item())
    num_per_key = int(num_per_key) + 1
    sampled_data: list[dict] = []
    for _, data_list in data_by_special_keys.items():
        sampled_data.extend(random.sample(data_list, min(num_per_key, len(data_list))))
    print(f"[dedup_data_simple] Sampled {len(sampled_data)} data by {num_per_key=}.")
    return sampled_data


def convert_reward_data_to_prompt_data(
    input_dir: pathlib.Path,
    output_dir: pathlib.Path,
    keep_dogfood: bool,
    non_permissive_request_ids: set[str],
):
    """Process the data as a dataset."""
    assert not output_dir.exists(), f"{output_dir} already exists."
    assert input_dir.exists(), f"{input_dir} does not exist."
    reward_hf_data = HFDataset.load_from_disk(input_dir)
    print(f"Load {len(reward_hf_data)} items from {input_dir}.")
    dogfood_data, vanguard_data = [], []
    num_by_label = collections.defaultdict(int)
    num_filter_by_license = 0
    for data in tqdm.tqdm(reward_hf_data, desc="Split"):
        assert isinstance(data, dict)
        assert len(data["labels"]) == 1
        tenant_name = "".join([chr(x) for x in data["tenant_names"]])
        user_id = "".join([chr(x) for x in data["user_ids"]])
        file_path = "".join([chr(x) for x in data["file_paths"]])
        timestamp = "".join([chr(x) for x in data["dates"]])
        request_id = "".join([chr(x) for x in data["request_ids"]])
        new_data = {
            "prompt_tokens": data["prompt_tokens"],
            "target_tokens": data["target_tokens"],
            "label": data["labels"][0],
            "tenant_name": tenant_name,
            "user_id": user_id,
            "file_path": file_path,
            "timestamp": timestamp,
            "request_id": request_id,
        }
        if request_id in non_permissive_request_ids:
            num_filter_by_license += 1
            continue
        num_by_label[data["labels"][0]] += 1
        if tenant_name in ("aitutor-turing", "i0-vanguard0"):
            vanguard_data.append(new_data)
        elif tenant_name in ("dogfood", "dogfood-shard"):
            dogfood_data.append(new_data)
        else:
            raise ValueError(f"Unknown tenant name: {tenant_name}")
    print(f"Filter {num_filter_by_license} data by license.")
    print(f"There are {len(dogfood_data)} dogfood data.")
    print(f"There are {len(vanguard_data)} vanguard data.")
    if keep_dogfood:
        all_data = dogfood_data + vanguard_data
    else:
        all_data = vanguard_data
    print(f"There are {len(all_data)} data in total ({keep_dogfood=}).")

    # Split skip and non-skip
    data_w_skip, data_wo_skip = [], []
    for data in tqdm.tqdm(all_data, desc="Split skip and non-skip"):
        if qwen_special_tokens.skip in data["target_tokens"]:
            data_w_skip.append(data)
        else:
            data_wo_skip.append(data)
    print(f"There are {len(data_w_skip)} data with skip token.")
    print(f"There are {len(data_wo_skip)} data without skip token.")

    # Group the data by unique key
    data_w_skip_by_unique_key = collections.defaultdict(list)
    data_wo_skip_by_unique_key = collections.defaultdict(list)
    for data in tqdm.tqdm(all_data, desc="Group"):
        timestamp = data["timestamp"]
        (yy, mm, dd), hour = (
            timestamp.split(" ")[0].split("-"),
            timestamp.split(" ")[1].split(":")[0],
        )
        date_as_int = (int(yy), int(mm), int(dd))
        if date_as_int < (2024, 11, 1):
            key = (
                data["user_id"],
                yy,
                mm,
                data["file_path"],
                num_tokens_to_bucket(len(data["target_tokens"])),
            )
        else:
            key = (
                data["user_id"],
                yy,
                mm,
                dd,
                int(hour) // 12,  # every 12 hours
                data["file_path"],
                data["label"],
                num_tokens_to_bucket(len(data["target_tokens"])),
            )
        if qwen_special_tokens.skip in data["target_tokens"]:
            data_w_skip_by_unique_key[key].append(data)
        elif data["label"] not in (1, 2, 4):
            data_wo_skip_by_unique_key[key].append(data)
    print(f"There are {len(data_w_skip_by_unique_key)} unique keys with skip.")
    print(f"There are {len(data_wo_skip_by_unique_key)} unique keys without skip.")

    data_after_dedup_v1: list[dict] = []
    for key, data_list in tqdm.tqdm(
        data_wo_skip_by_unique_key.items(), desc="Dedup without skip"
    ):
        data_after_dedup_v1.extend(random.sample(data_list, min(1, len(data_list))))
    data_after_dedup_v2 = dedup_data_simple(data_after_dedup_v1)
    data_after_dedup_v3 = copy.deepcopy(data_after_dedup_v2)
    for key, data_list in tqdm.tqdm(
        data_w_skip_by_unique_key.items(),
        desc="Keep all the data with skip as it is too small",
    ):
        data_after_dedup_v3.extend(data_list)
    print(f"There are {len(data_after_dedup_v1)} data after dedup (v1).")
    print(f"There are {len(data_after_dedup_v2)} data after dedup (v2).")
    print(f"There are {len(data_after_dedup_v3)} data after dedup (v3 = v2 + skip).")
    # Save to the output directory
    output_dir.mkdir(exist_ok=True, parents=False)
    dataset = HFDataset.from_dict(
        {"prompt_tokens": [x["prompt_tokens"] for x in data_after_dedup_v3]}
    )
    dataset.save_to_disk(output_dir)
    print(f"Save {len(dataset)} items into {output_dir}.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_dir",
        type=pathlib.Path,
        default=None,
        required=True,
    )
    parser.add_argument(
        "--output_dir",
        type=pathlib.Path,
        default=None,
        required=True,
    )
    parser.add_argument(
        "--keep_dogfood",
        action="store_true",
        default=False,
    )
    args = parser.parse_args()
    # Load non permissive data
    df_non_permissive = pd.read_parquet(NON_PERMISSIVE_DATA_PATH)
    non_permissive_request_ids = set(df_non_permissive["request_id"].tolist())
    print(f"There are {len(non_permissive_request_ids)} non-permissive request IDs.")
    convert_reward_data_to_prompt_data(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        keep_dogfood=args.keep_dogfood,
        non_permissive_request_ids=non_permissive_request_ids,
    )
