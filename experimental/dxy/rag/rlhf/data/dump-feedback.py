"""Create the rlhf dataset for completion models.

Be careful about data before 2024-08-06: https://www.notion.so/2024-08-13-Acceptance-Increase-283cf387c0d94ee5a654833072daab7f#b2aa9e41f1b24f33b097c68bf23b9d36

# Usage

python experimental/dxy/rag/rlhf/data/dump-feedback.py --date_range 20240601-20240630
python experimental/dxy/rag/rlhf/data/dump-feedback.py --date_range 20240801-20241029
python experimental/dxy/rag/rlhf/data/dump-feedback.py --date_range 20250116-20250119

The default output to /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114
"""

import argparse
import pathlib

import tqdm

from base.datasets import completion, tenants
from base.datasets.completion_dataset import CompletionDataset, CompletionDatum
from base.prompt_format_completion.prompt_formatter import PromptInput
from experimental.dxy.rag.rlhf.shared_lib import traverse_date_range
from research.core import utils_for_file

ELDEN_MODELS: list[str] = [
    "eldenv2-15b",
    "eldenv3-15b",
    "eldenv4-15b",
    "eldenv4-1-15b",
    "eldenv4-2-15b",
    "eldenv4-3-15b",
    "eldenv4-4a-15b",
    "eldenv4-0b-15b",
    "eldenv4-0c-15b",
    "eldenv4-0d-15b",
    "eldenv4-0e-15b",
    "eldenv4-0f-15b",
    "eldenv6-15b",
    "eldenv6-1-15b",
    "eldenv7-0-15b",
]


def convert_datum_to_prompt_input(data: CompletionDatum):
    assert data.resolution is not None
    # chunks: list[PromptChunk] = []
    # for x in data.response.retrieved_chunks:
    #     unique_id = f"{x.blob_name}:{x.crange.start}-{x.crange.stop}"
    #     chunks.append(
    #         PromptChunk(
    #             text=x.text,
    #             path=x.path,
    #             unique_id=unique_id,
    #             origin=x.origin,
    #             char_start=x.crange.start,
    #             char_end=x.crange.stop,
    #             blob_name=x.blob_name,
    #         )
    #     )
    prompt_input = PromptInput(
        prefix=data.request.prefix,
        suffix=data.request.suffix,
        prefix_begin=0,
        path=data.request.path,
        retrieved_chunks=[],
        # retrieved_chunks=chunks,
        lang=None,
    )
    if data.feedback is None:
        feedback = None
    else:
        feedback = completion.CompletionFeedback.schema().dump(data.feedback)
    if data.inference_response is None:
        inference_token_ids = None
        assert False, "inference_response is None"
    else:
        inference_token_ids = data.inference_response.token_ids
    if data.request.edit_events is None:
        edit_events = None
    else:
        edit_events = [
            completion.GranularEditEvent.schema().dump(x)
            for x in data.request.edit_events
        ]
    assert data.user_agent is not None
    resolution = completion.CompletionResolution.schema().dump(data.resolution)
    return {
        "prompt_input": {
            "prefix": prompt_input.prefix,
            "suffix": prompt_input.suffix,
            "prefix_begin": prompt_input.prefix_begin,
            "path": prompt_input.path,
            # "retrieved_chunks": [PromptChunk.schema().dump(x) for x in chunks],
            "lang": prompt_input.lang,
        },
        "request_id": data.request_id,
        "user_id": data.user_id,
        # "prompt_tokens": data.response.prompt_tokens,
        "prompt_token_ids": data.response.prompt_token_ids,
        # "generated_tokens": data.response.tokens,
        "generated_token_ids": data.response.token_ids,
        # "generated_token_log_probs": data.response.token_log_probs,
        "timestamp": str(data.response.timestamp),
        "response_text": data.response.text,
        "response_model": data.response.model,
        "resolution": resolution,
        "accepted": data.resolution.accepted,
        "feedback": feedback,
        "inference_token_ids": inference_token_ids,
        "edit_events": edit_events,
        "user_agent": data.user_agent,
    }


def dump_raw_data(
    date_ranges: list[str],
    tenant_names: list[str],
    limit: int = 50_000,
    root_out_dir: pathlib.Path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal"
    ),
):
    # Start from Augest 1, the accept/reject calculation is changed: https://www.notion.so/2024-08-13-Acceptance-Increase-283cf387c0d94ee5a654833072daab7f
    all_date_ranges = []
    for cur_date_range in date_ranges:
        start_date, end_date = cur_date_range.split("-")
        all_date_ranges.extend(traverse_date_range(start_date, end_date))
    print(f"There are {len(all_date_ranges)} date ranges.")
    for date, xstart, xend in all_date_ranges:
        print(f"{date}: {xstart} - {xend}")
    for index, (date, xstart, xend) in enumerate(all_date_ranges):
        output_dir = root_out_dir / f"{date}-accept-reject"
        print(f"Process {index:03d}/{len(all_date_ranges)}: {date}")
        for tenant_name in tenant_names:
            datum_list: list[dict] = []
            for accept in (True, False):
                completion_filters = CompletionDataset.Filters(
                    # model_names=ELDEN_MODELS,
                    model_names=None,
                    min_completion_length=1,
                    timestamp_begin=xstart,
                    timestamp_end=xend,
                    accepted_completion=accept,
                )
                completions = CompletionDataset.create(
                    tenant=tenants.get_tenant(tenant_name),
                    filters=completion_filters,
                    limit=limit,
                    order_by=None,
                    page_size=8192,
                    queue_size=50,
                )
                print(
                    f"Query {completions.num_rows}/{limit=} with {accept=} completions from {tenant_name}."
                )
                for x in tqdm.tqdm(
                    completions.get_completions(),
                    desc=f"Get {date} {tenant_name} {accept=} data",
                    total=completions.num_rows,
                    smoothing=0.1,
                ):
                    x_json = convert_datum_to_prompt_input(x)
                    datum_list.append(x_json)
            if len(datum_list) == 0:
                print(f"Skip {date} {tenant_name} as there is no data.")
                continue
            output_dir.mkdir(exist_ok=True, parents=False)
            output_path = (
                output_dir / f"{tenant_name}-raw-data-{len(datum_list)}-samples.jsonl"
            )
            utils_for_file.write_jsonl(output_path, datum_list)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--date_range",
        type=str,
        required=True,
        help="The date range to process, e.g., 20240801-20240802, we accept multiple ranges.",
        nargs="+",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="/mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-1114",
    )
    args = parser.parse_args()

    dump_raw_data(
        date_ranges=args.date_range,
        # tenant_names=["dogfood", "dogfood-shard", "aitutor-turing"],
        tenant_names=["dogfood", "dogfood-shard", "i0-vanguard0", "aitutor-turing"],
        limit=100_000,
        root_out_dir=pathlib.Path(args.output_dir),
    )
