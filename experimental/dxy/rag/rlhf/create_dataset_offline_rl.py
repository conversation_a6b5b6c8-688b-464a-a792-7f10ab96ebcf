"""Create the offline RLHF dataset for completion models.

python experimental/dxy/rag/rlhf/create_dataset_offline_rl.py
"""

import json
import pathlib

import tqdm
from datasets import Dataset as HFDataset

from research.core import utils_for_log


def load_data_generator(all_paths: list[pathlib.Path]):
    """Extract the data."""
    for data_path in all_paths:
        data = json.loads(data_path.read_text())
        yield data


def main():
    inputs = [
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-pareto",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-mercor",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood",
        "/mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard",
    ]
    all_data_path: list[pathlib.Path] = []
    for input_folder in inputs:
        token_dir = pathlib.Path(f"{input_folder}/token-data-noskip")
        assert token_dir.exists()
        all_data_path.extend(list(token_dir.glob("*-*.json")))
    print(f"{utils_for_log.time_string()} there are {len(all_data_path)} items.")

    skip_due_to_long_pos_res = 0
    skip_due_to_long_pos_seq = 0
    skip_due_to_long_neg_seq = 0
    all_prompt_tokens, all_pos_tokens, all_neg_tokens = [], [], []
    for data in tqdm.tqdm(
        load_data_generator(all_data_path), total=len(all_data_path), desc="Loading"
    ):
        if len(data["positive_tokens"]) > 256:
            skip_due_to_long_pos_res += 1
            continue
        if len(data["prompt_tokens"]) + len(data["positive_tokens"]) + 2 > 7940:
            skip_due_to_long_pos_seq += 1
            continue
        if len(data["prompt_tokens"]) + len(data["positive_tokens"]) + 2 > 7940:
            skip_due_to_long_neg_seq += 1
            continue
        all_prompt_tokens.append(data["prompt_tokens"])
        all_pos_tokens.append(data["positive_tokens"])
        all_neg_tokens.append(data["negative_tokens"])
    print(f"{utils_for_log.time_string()} {skip_due_to_long_pos_res=}")
    print(f"{utils_for_log.time_string()} {skip_due_to_long_pos_seq=}")
    print(f"{utils_for_log.time_string()} {skip_due_to_long_neg_seq=}")
    dataset = HFDataset.from_dict(
        {
            "prompt_tokens": all_prompt_tokens,
            "pos_tokens": all_pos_tokens,
            "neg_tokens": all_neg_tokens,
        }
    )
    output_dir = "/mnt/efs/augment/user/dxy/hindsight/offline-rl-240904"
    # dataset.save_to_disk(output_dir)
    train_test_split = dataset.train_test_split(test_size=0.1)
    train_dataset = train_test_split["train"]
    valid_dataset = train_test_split["test"]
    train_dataset.save_to_disk(f"{output_dir}/train")
    valid_dataset.save_to_disk(f"{output_dir}/valid")
    print(f"{utils_for_log.time_string()} finish split")
    print(f"Save {len(train_dataset)} items into {output_dir}/train.")
    print(f"Save {len(valid_dataset)} items into {output_dir}/valid.")


if __name__ == "__main__":
    main()
