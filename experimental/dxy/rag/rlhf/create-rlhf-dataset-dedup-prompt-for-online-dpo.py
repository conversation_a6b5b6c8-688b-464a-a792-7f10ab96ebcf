"""Create the online-dpo dataset for completion models.

python experimental/dxy/rag/rlhf/create-rlhf-dataset-dedup-prompt-for-online-dpo.py \
    --input /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202410-v2 \
    --output /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202405-202410-v2-dedup-prompt

202180 items in "202405-202410-v2" -> 52845 items in "202405-202410-v2-dedup-prompt"

python experimental/dxy/rag/rlhf/create-rlhf-dataset-dedup-prompt-for-online-dpo.py \
    --input /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202410-202411-v2 \
    --output /mnt/efs/augment/user/dxy/datasets/rlhf/rawsignal-to-rmdata/202410-202411-v2-dedup-prompt

13869 items in "202410-202411-v2" -> 4452 items in "202410-202411-v2-dedup-prompt"
"""

import argparse
import collections
import pathlib
import random

import numpy as np
import tqdm
from datasets import Dataset as HFDataset


def dedup_data_simple(
    prompt_tokens_and_target_tokens: list[tuple[list[int], list[int]]],
) -> list[tuple[list[int], list[int]]]:
    """Simple deduplication based on the number of tokens."""
    data_by_special_keys = collections.defaultdict(list)
    for prompt_tokens, target_tokens in tqdm.tqdm(
        prompt_tokens_and_target_tokens, desc="analyze"
    ):
        data_by_special_keys[len(target_tokens)].append((prompt_tokens, target_tokens))
    print(
        f"There are {len(data_by_special_keys)}/{len(prompt_tokens_and_target_tokens)} unique keys."
    )
    for key, data_list in data_by_special_keys.items():
        print(f"There are {len(data_list)} data with {key:02d} tokens.")
    length_per_key = []
    for _, data_list in data_by_special_keys.items():
        length_per_key.append(len(data_list))
    length_per_key = np.array(length_per_key)
    print(
        f"[dedup_data_simple] Length Per Key: {length_per_key.min()}~{length_per_key.max()} mean={length_per_key.mean()}, std={length_per_key.std()}"
    )
    num_per_key = max(length_per_key.mean().item(), np.median(length_per_key).item())
    num_per_key = int(num_per_key) + 1
    sampled_data: list[tuple[list[int], list[int]]] = []
    for _, data_list in data_by_special_keys.items():
        sampled_data.extend(random.sample(data_list, min(num_per_key, len(data_list))))
    print(f"[dedup_data_simple] Sampled {len(sampled_data)} data by {num_per_key=}.")
    return sampled_data


def dedupe_prompt_for_online_dpo(input_dir: pathlib.Path, output_dir: pathlib.Path):
    print(f"input_dir: {input_dir}")
    assert input_dir.exists()
    dataset = HFDataset.load_from_disk(str(input_dir))
    print(f"There are {len(dataset)} items in {input_dir}.")

    key2tokens: dict[str, tuple[list[int], list[int]]] = {}
    for index in tqdm.tqdm(range(len(dataset)), total=len(dataset), desc="dedup"):
        prompt_tokens = dataset[index]["prompt_tokens"]
        target_tokens = dataset[index]["target_tokens"]
        key2tokens[str(prompt_tokens)] = (prompt_tokens, target_tokens)
    # all_prompt_tokens = list(key2tokens.values())
    # random.shuffle(all_prompt_tokens)
    print(f"There are {len(key2tokens)} items after deduplication.")
    # Try to diversely sample the prompt tokens
    all_tokens: list[tuple[list[int], list[int]]] = list(key2tokens.values())
    dedup_tokens = dedup_data_simple(all_tokens)
    all_prompt_tokens = [x[0] for x in dedup_tokens]
    print(f"There are {len(all_prompt_tokens)} items after deduplication.")
    num_prompt_tokens = np.array([len(x) for x in all_prompt_tokens])
    print(
        f"Prompt: {num_prompt_tokens.min()}~{num_prompt_tokens.max()} mean={num_prompt_tokens.mean()}, std={num_prompt_tokens.std()}"
    )
    # Save to the output directory
    output_dir.mkdir(exist_ok=True, parents=False)
    dataset = HFDataset.from_dict(
        {
            "prompt_tokens": all_prompt_tokens,
        }
    )
    dataset.save_to_disk(output_dir)
    print(f"Save {len(dataset)} items into {output_dir}.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=pathlib.Path,
        required=True,
        help="The input file folder",
    )
    parser.add_argument(
        "--output",
        type=pathlib.Path,
        required=True,
        help="The output file folder",
    )
    args = parser.parse_args()
    dedupe_prompt_for_online_dpo(args.input, args.output)
