"""Download the all data including blobs for the hindsight.

2024-05 ~ 2024-06
CUDA_VISIBLE_DEVICES=0,1 python experimental/dxy/rag/rlhf/dump_tokens.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-05-2024-06/dogfood


2024-06 ~ 2024-07
python experimental/dxy/rag/rlhf/dump_tokens.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/aitutor-mercor \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood \
            /mnt/efs/augment/user/dxy/hindsight/2024-06-2024-07/dogfood-shard

2024-06-2024-07/aitutor-mercor should be skipped since it has 0 blobs.

2024-07 ~ 2024-08
CUDA_VISIBLE_DEVICES=0,1 python experimental/dxy/rag/rlhf/dump_tokens.py \
    --input /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-pareto \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-turing \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/aitutor-mercor \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood \
            /mnt/efs/augment/user/dxy/hindsight/2024-07-2024-08/dogfood-shard
"""

import argparse
import concurrent.futures as futures
import copy
import json
import pathlib

import torch
import tqdm
import zstandard as zstd

from base.datasets.hindsight_completion_dataset import (
    HindsightCompletionDatum,
)
from base.prompt_format.common import PromptChunk
from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict
from experimental.dxy.rag.rlhf.local_content_manager import (
    LocalContentManager,
)
from research.core import utils_for_log
from research.core.model_input import ModelInput
from research.core.recency_info import convert_from_datasets_recency_info
from research.eval.harness.systems.abs_system import CompletionResult
from research.eval.harness.systems.elden_system_w_prod_formatter import ProdEldenSystem

# from research.eval.harness.systems.recency_lib import (
#     recent_chunk_to_prod_retrieval_chunk,
# )


def create_system(name: str = "SC2_SimpleElden_S6K_D3MT_FBW") -> ProdEldenSystem:
    """Create a system."""
    if name == "SC2_SimpleElden_S6K_D3MT_FBW":
        num_gpus = torch.cuda.device_count()
        assert num_gpus in (1, 2, 4)
        print(f"The number of GPUs are {num_gpus}.")
        model_config = {
            "name": "fastbackward",
            "checkpoint_path": f"/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6-mp{num_gpus}",
            "seq_length": 7936,
            "model_parallel_size": num_gpus,
        }
        max_generated_tokens = 256
        tokenizer_name = "starcoder2"
        system = ProdEldenSystem.from_yaml_config(
            {
                "tokenizer": tokenizer_name,
                "model": model_config,
                "generation_options": {"max_generated_tokens": max_generated_tokens},
                "dense_retriever": retriever_config_dict["sethanol"],
                "signature_retriever": retriever_config_dict["methanol0416_v2"],
                "formatter_config": {
                    "name": "simple_elden",
                    "prompt_formatter_config": {
                        "max_prompt_length": 7680,
                        "token_config": {
                            "path_len": 50,
                            "prefix_len": 1024,
                            "suffix_len": 512,
                            "retrieval_len": 7680,
                        },
                        "per_retriever_max_tokens": {
                            "signature_retriever": 1024,
                            "dense_retriever": 7680,
                        },
                    },
                },
                "config": {
                    "line_chunk_retriever_top_k": 32,
                    "sig_chunk_retriever_top_k": 32,
                    "fim_gen_mode": "evaluation",
                },
            }
        )
    else:
        raise ValueError(f"Unknown system {name}")
    return system


def deserialize_single_line(line: str):
    """Decompress a line."""
    data = json.loads(line)
    assert "datum" in data and "blob_names" in data
    return {
        "datum": HindsightCompletionDatum.schema().load(data["datum"]),
        "blob_names": data["blob_names"],
    }


def deserialize(
    all_lines: list[str], max_threads: int = 128, deserialize_fn=None
) -> list[dict]:
    """Deserialize the data via multiple threads."""
    results = []
    total = len(all_lines)
    if deserialize_fn is None:
        deserialize_fn = deserialize_single_line
    with futures.ThreadPoolExecutor(max_workers=max_threads) as executor:
        all_jobs = []
        for line in tqdm.tqdm(all_lines, total=total, desc="Submit jobs"):
            all_jobs.append(executor.submit(deserialize_fn, line))
        for job in tqdm.tqdm(
            futures.as_completed(all_jobs), total=total, desc="Decompress"
        ):
            results.append(job.result())
    return results


def get_recency_chunks(datum: HindsightCompletionDatum) -> list[PromptChunk]:
    """Get the recency chunks."""
    chunks = []
    for chunk in datum.completion.response.retrieved_chunks:
        if chunk.origin == "recency_retriever":
            chunks.append(
                PromptChunk(
                    text=chunk.text,
                    path=chunk.path,
                    unique_id=None,
                    origin=chunk.origin,
                    char_start=chunk.crange.start,
                    char_end=chunk.crange.stop,
                    blob_name=chunk.blob_name,
                )
            )
    return chunks


def process_hindsight(input_folder: pathlib.Path, system: ProdEldenSystem):
    """Process the hindsight data."""

    local_datum_path = input_folder / "local-datum.jsonl.zst"
    assert local_datum_path.exists()
    print(f"{utils_for_log.time_string()} Processing {input_folder}")
    all_lines = []
    with zstd.open(local_datum_path, "r", encoding="utf-8") as f:
        for line in f:
            all_lines.append(line)
    print(f"{utils_for_log.time_string()} Load {len(all_lines)} lines from {local_datum_path}.")  # fmt: off
    all_datum = deserialize(all_lines)
    print(f"{utils_for_log.time_string()} Finish decompress {len(all_datum)} lines")
    blob_manager = LocalContentManager()
    blob_manager.load(input_folder / "local-blobs")

    # Load all the documents
    system.clear_retriever()
    system.add_docs(blob_manager.to_documents())
    gen_options_argmax = copy.deepcopy(system.generation_options)
    gen_options_random = copy.deepcopy(system.generation_options)
    gen_options_random.temperature = 1.0
    gen_options_random.top_p = 0.9

    outputs = []
    for idx, datum_docids in enumerate(all_datum):
        datum: HindsightCompletionDatum = datum_docids["datum"]
        blob_names: list[str] = datum_docids["blob_names"]
        # recency_chunks = get_recency_chunks(datum)
        if datum.completion.request.recency_info is None:
            recency_info = None
        else:
            recency_info = convert_from_datasets_recency_info(
                datum.completion.request.recency_info
            )
        # Build a model input
        model_input = ModelInput(
            prefix=datum.completion.request.prefix,
            suffix=datum.completion.request.suffix,
            path=datum.completion.request.path,
            target=None,
            doc_ids=blob_names,
            recency_info=recency_info,
        )
        system.generation_options = gen_options_argmax
        output_argmax = system.generate(model_input)
        system.generation_options = gen_options_random
        output_random = system.generate(model_input)
        # Collect the outputs
        outputs.append(
            {
                "datum": HindsightCompletionDatum.schema().dump(datum),
                "blob_names": blob_names,
                "output_argmax": CompletionResult.schema().dump(output_argmax),
                "output_random": CompletionResult.schema().dump(output_random),
            }
        )
        print(f"{utils_for_log.time_string()} Process {idx:4d}/{len(all_datum):4d} examples.")  # fmt: off
    print(f"{utils_for_log.time_string()} Finish processing {len(outputs)} examples.")  # fmt: off
    datum_path = input_folder / "local-datum-w-output.jsonl.zst"
    with zstd.open(datum_path, "w", encoding="utf-8") as f:
        for output in outputs:
            json.dump(output, f)
            f.write("\n")
    print(f"{utils_for_log.time_string()} Save {len(outputs)} datum into {datum_path}.")  # fmt: off


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input",
        type=pathlib.Path,
        default=None,
        nargs="+",
        required=True,
        help="The input file",
    )
    args = parser.parse_args()
    system = create_system()
    system.load()
    for input_folder in args.input:
        assert input_folder.exists()
        process_hindsight(input_folder, system)


if __name__ == "__main__":
    main()
