from experimental.michiel.configs.eval.retriever_configs import (
    ethanol604,
    ethanol616,
    ethanol616_fb,
    methanol0416,
)

ethanol616_ffw_config = {
    "scorer": {
        "name": "dense_scorer_v2_ffwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/stareth_repro_ffw/global_step2000",
    },
    "chunker": {
        "name": "line_level",
        # "max_lines_per_chunk": 30,
        "max_lines_per_chunk": 40,
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
}

sethanol_config = dict(
    chunker={
        "max_lines_per_chunk": 40,
        "name": "line_level",
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "prefix_ratio": 0.9,
        "tokenizer_name": "StarCoderTokenizer",
    },
    scorer={
        "name": "dense_scorer_v2_fbwd_neox",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/",
    },
)

sethanol_smart_config = dict(
    chunker={
        "name": "smart_line_level",
        "max_chunk_chars": 768,
    },
    document_formatter={
        "add_path": True,
        "add_prefix": False,
        "add_suffix": False,
        "max_tokens": 999,
        "name": "ethanol6_document",
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "prefix_ratio": 0.9,
        "tokenizer_name": "StarCoderTokenizer",
    },
    scorer={
        "name": "dense_scorer_v2_fbwd_neox",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/star_ethanol/starethanol6_16.1_mean_proj_512_2000/global_step2000/",
    },
)

methanol0416_v2_config = {
    "chunker": {"name": "signature"},
    "document_formatter": {
        "add_path": False,
        "name": "simple_document",
        "max_tokens": 999,
        "tokenizer_name": "StarCoderTokenizer",
    },
    "query_formatter": {
        "add_path": True,
        "add_suffix": True,
        "max_lines": -1,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    "scorer": {
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
}

smart_dense_retriever = {
    "scorer": {
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/ethanol/stareth_2000s_128docactual_smartnoheader",
    },
    "chunker": {
        "name": "smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    "query_formatter": {
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    "document_formatter": {
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
}


retriever_config_dict = {
    "ethanol604": ethanol604.config,
    "ethanol616": ethanol616.config,
    "ethanol616_fb": ethanol616_fb.config,
    "ethanol616_ffw": ethanol616_ffw_config,
    "sethanol": sethanol_config,
    "sethanol_smart": sethanol_smart_config,
    "smart_dense_retriever": smart_dense_retriever,
    "methanol0416": methanol0416.config,
    "methanol0416_v2": methanol0416_v2_config,
}
