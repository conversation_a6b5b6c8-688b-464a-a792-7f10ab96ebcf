"""Unit tests for utils.

pytest experimental/dxy/rogue/exps/utils_test.py
"""

import re
from experimental.dxy.rag.exps.utils import get_unit_test_re_pattern


def test_get_unit_test_re_pattern():
    """Test the regex pattern for unit test file path."""
    pattern = get_unit_test_re_pattern(reverse=False)
    unit_test_paths = [
        "augment/base/static_analysis/signature_utils_test.py",
        "augment/base/static_analysis/test_signature_utils.py",
        "tests/pyright/pyright_example.py",
        "tests/pyright/pyright_example_test.go",
        "augment/base/static_analysis/test_signature_utils.rs",
        "test.py",
    ]

    non_unit_test_paths = [
        "augment/base/static_analysis/signature_utils.c",
        "augment/base/static_analysis/signature_utils.cpp",
        "pyright/pyright_example.ts",
    ]
    for path in unit_test_paths:
        assert bool(re.match(pattern, path))
    for path in non_unit_test_paths:
        assert not bool(re.match(pattern, path))

    pattern = get_unit_test_re_pattern(reverse=True)
    for path in unit_test_paths:
        assert not bool(re.match(pattern, path))
    for path in non_unit_test_paths:
        assert bool(re.match(pattern, path))
