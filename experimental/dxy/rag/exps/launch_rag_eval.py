"""Launch evals for the basic rag system.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/rag/exps/launch_rag_eval.py \
    --system online --model roguesl_8k --ckp_path dxy/sc2-rogue/7B-8K-baseline-bs1ks1k \
    --exp_name 7B-Baseline --num_gpus 1 --tasks fimeval --local

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --system basic --model roguesl_8k --ckp_path dxy/sc2-rogue/7B-8K-baseline-bs1ks1k \
    --exp_name 7B-Baseline --num_gpus 1 \
    --tasks fimeval cceval hindsight multilang functions 23lines api

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --system basic --model roguesl_8k --ckp_path dxy/sc2-rogue/15B-8K-baseline-bs1ks1k \
    --exp_name 15B-Baseline --num_gpus 2 \
    --tasks fimeval cceval hindsight multilang functions 23lines api

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --system basic --model roguesl_8k --ckp_path dxy/sc2-rogue/15B-8K-baseline-bs256-s2k \
    --exp_name 15B-Baseline-BS256-S2K --num_gpus 2 \
    --tasks fimeval cceval hindsight

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --system basic --model roguesl_8k --ckp_path dxy/sc2-rogue/15B-8K-baseline-bs2ks2k-acc16 \
    --exp_name 5B-Baseline-BS2K-S2K-ACC16 --num_gpus 2 \
    --tasks fimeval cceval hindsight

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --system online --model roguesl_8k --ckp_path dxy/sc2-rogue/15B-8K-baseline-bs1ks1k \
    --exp_name 15B-Baseline --num_gpus 2 \
    --tasks fimeval cceval hindsight multilang functions 23lines api

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --model roguesl_8k --ckp_path dxy/sc2-rogue/15B-8K-base+test-bs1ks1k \
    --exp_name 15B-Base+Test --num_gpus 2 \
    --tasks fimeval cceval hindsight multilang functions 23lines api

python experimental/dxy/rag/exps/launch_rag_eval.py \
    --model roguesl_8k --ckp_path dxy/sc2-rogue/15B-8K-base+test-bs2ks2k \
    --exp_name 15B-Base+Test-BS2KS2K --num_gpus 2 \
    --tasks fimeval cceval hindsight multilang functions 23lines api
"""

import argparse
import copy
import os
import pathlib

import yaml

from experimental.dxy.rag.exps.model_configs import model_config_dict
from experimental.michiel.configs.eval.retriever_configs import retriever_config_dict
from research.eval.eval import __file__ as eval_file
from research.eval.eval_lib import run_local
from research.utils.inspect_indexed_dataset import print_green


def execute_eval(
    determined_name: str,
    system_config: dict,
    task_config: dict,
    pod_spec: str,
    determined_overrides: dict | None = None,
    additional_overrides: dict | None = None,
    local: bool = False,
):
    all_config = {
        "system": system_config,
        "task": task_config,
        "podspec": pod_spec,
        "determined": {
            "name": determined_name,
            "workspace": "Dev",
            "project": "Xuanyi-Eval",
            "metaconfig": "jobs/templates/eval-exec-v2-metaconfig.yaml",
        },
    }
    all_config["determined"].update(determined_overrides or {})
    all_config.update(additional_overrides or {})

    # convert the config to yaml string
    config_str = yaml.dump(all_config, indent=2)
    print_green("Launching eval with the following config:")
    print(config_str)
    config_path = pathlib.Path("/tmp/current_eval_config.yml")
    config_path.write_text(config_str)
    cmd = f"python3 {eval_file} --skip_bazel {config_path}"
    if local:
        run_local(
            config_files=[str(config_path)],
            checkpoint_path="",
            job_root="/home/<USER>/eval_jobs",
        )
    else:
        print_green(f"Executing command: {cmd}")
        os.system(cmd)


def get_system_config(
    system: str, model: str, retriever: str, ckp_path: pathlib.Path, num_gpus: int = 1
) -> dict:
    model_config = copy.deepcopy(model_config_dict[model])
    retriever_config = retriever_config_dict[retriever]

    assert model_config["checkpoint_path"] is None
    # if args.ckp_path.absolute() and not args.ckp_path.exists():
    #     raise ValueError(f"Checkpoint path {args.ckp_path} does not exist.")
    model_config["checkpoint_path"] = str(ckp_path)
    model_config["model_parallel_size"] = num_gpus

    if system == "basic":
        system_config = {
            "name": "basic_rag",
            "model": model_config,
            "generation_options": {
                "max_generated_tokens": model_config["seq_length"]
                - model_config["prompt"]["max_prompt_tokens"]
            },
            "retriever": retriever_config,
            "experimental": {
                "remove_suffix": False,
                "trim_on_dedent": False,
                "retriever_top_k": 25,
            },
            "fim_gen_mode": "evaluation",
        }
    elif system == "online":
        system_config = {
            "name": "online_rag",
            "model": model_config,
            "generation_options": {
                "max_generated_tokens": model_config["seq_length"]
                - model_config["prompt"]["max_prompt_tokens"]
            },
            "retriever": retriever_config,
            "retriever_top_k": 25,
            "tokens_per_retrievestep": 8,
            "fim_gen_mode": "evaluation",
        }
    else:
        raise ValueError(f"Unknown system {system}")

    return system_config


def main(args: argparse.Namespace):
    system_config = get_system_config(
        args.system, args.model, args.retriever, args.ckp_path, args.num_gpus
    )

    tasks = {
        "cceval": {"name": "cceval"},
        "multilang": {
            "name": "hydra",
            "dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
            "hydra_block_resource_internet_access": True,
        },
        "functions": {"name": "hydra", "dataset": "repoeval_functions"},
        "23lines": {"name": "hydra", "dataset": "repoeval_2-3lines"},
        "api": {"name": "api", "dataset": "finegrained-python.large"},
        "hindsight": {
            "name": "hindsight",
            "dataset": "2024-04-25-v0.7",
            "tenant_name": "dogfood",
            "service_account_file": "/mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json",
        },
        "cceval-unit-test": {
            "name": "cceval",
            "unit_test_only": True,
        },
        "fimeval": {"name": "fimeval"},
    }

    for key, task_config in tasks.items():
        if args.tasks and key not in args.tasks:
            continue

        if task_config["name"] in ("cceval", "hindsight", "fimeval"):
            # pod_spec = f"{args.num_gpus}xH100.yaml"
            pod_spec = f"{args.num_gpus}xA100.yaml"
        else:
            pod_spec = f"{args.num_gpus}xA100.yaml"
        if task_config["name"] == "functions":
            system_config["experimental"]["trim_on_dedent"] = True

        retriever_name = args.retriever

        determined_name = f"{args.exp_name} - {key} - {args.system} - {args.ckp_path.name} - {retriever_name} "

        print(determined_name)
        augment_args = {
            "gpu_count": args.num_gpus,
            "project_group": "finetuning",
        }
        if task_config["name"] == "hindsight":
            augment_args["dai_gcp_service_accounts"] = [
                {
                    "secret": "aug-prod-cw-ri-importer",  # pragma: allowlist secret
                    "mountpoint": "/mnt/augment/secrets/cw-ri-importer",
                }
            ]
        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
            additional_overrides={"augment": augment_args},
            local=args.local,
        )


if __name__ == "__main__":
    # Parse args
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--system",
        type=str,
        default="basic",
        choices=("basic", "online"),
        help="The system indicator.",
    )
    parser.add_argument(
        "--model",
        type=str,
        default="roguesl_4k",
        help="The key in the model dict.",
    )
    parser.add_argument(
        "--retriever",
        type=str,
        default="ethanol616",
        help="The key in the retriever dict.",
    )
    parser.add_argument(
        "--ckp_path",
        type=pathlib.Path,
        required=True,
        help="The checkpoint path.",
    )
    parser.add_argument(
        "--exp_name",
        type=str,
        required=True,
        help="The experiment name.",
    )
    parser.add_argument(
        "--num_gpus",
        type=int,
        default=1,
        help="The number of GPUs to use.",
    )
    parser.add_argument(
        "--tasks",
        nargs="+",
        required=True,
        help="List of specific task names to run.",
    )
    parser.add_argument(
        "--local",
        action="store_true",
        default=False,
        help="Run evaluations outside determined.ai cluster",
    )
    parsed_args = parser.parse_args()

    main(parsed_args)
