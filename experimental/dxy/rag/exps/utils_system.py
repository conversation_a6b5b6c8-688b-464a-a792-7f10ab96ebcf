"""Utilities for replaying requests."""

import copy

from termcolor import colored

from experimental.dxy.rag.exps.utils import shift_middle_to_prefix
from research.core.model_input import ModelInput
from research.core.types import <PERSON><PERSON><PERSON><PERSON><PERSON>, Document
from research.data.rag.rogue_stages import RetrievalAugmentedSample
from research.eval.harness.systems.remote_completion_system import (
    RemoteCompletionSystem,
)


def build_model_input(data: RetrievalAugmentedSample):
    return ModelInput(
        prefix=data["prefix"],
        suffix=data["suffix"],
        path=data["file_path"],
        extra={
            "ground_truth_span": CharRange(
                data["middle_char_start"], data["middle_char_end"]
            )
        },
    )


def load_all_systems():
    systems = dict()
    systems["StarCoder-1"] = RemoteCompletionSystem.from_yaml_config(
        {
            "client": {"url": "https://dogfood.api.augmentcode.com"},
            "retriever": {"warn_on_indexing_timeout": True},
            "completion": {"max_completion_steps": 8},
            "model_name": "roguesl-v2-16b-seth616-rec",
        }
    )

    systems["StarCoder-2"] = RemoteCompletionSystem.from_yaml_config(
        {
            "client": {"url": "https://dogfood.api.augmentcode.com"},
            "retriever": {"warn_on_indexing_timeout": True},
            "completion": {"max_completion_steps": 8},
            "model_name": "star2sl-16b-seth616-rec",
        }
    )

    systems["StarCoder-2 (Signature)"] = RemoteCompletionSystem.from_yaml_config(
        {
            "client": {"url": "https://dogfood.api.augmentcode.com"},
            "retriever": {
                "warn_on_indexing_timeout": True,
                "wait_indexing_retry_count": 4,
            },
            "completion": {"warn_on_unknown_blobs": True, "max_completion_steps": 8},
            "model_name": "elden-15b-rec",
        }
    )
    for name, system in systems.items():
        system.load()
        print(f"Finish loading the system {name}")
    return systems


def compare_models(
    systems: dict,
    sample: RetrievalAugmentedSample,
    doc_by_id: dict[str, Document],
    shift_middle: int = 0,
):
    # show_truncated_completion(sample, 15, 5)
    if shift_middle > 0:
        sample = shift_middle_to_prefix(sample, shift_middle)
    print("-" * 32)
    updated_doc_by_id = copy.deepcopy(doc_by_id)
    file_path = sample["file_path"]
    updated_doc_by_id.pop(file_path)
    updated_doc_by_id[file_path] = Document.new(
        sample["prefix"] + sample["suffix"], sample["file_path"]
    )
    updated_all_docs = list(updated_doc_by_id.values())
    for name, system in systems.items():
        print(f"System: {name}")
        model_input = build_model_input(sample)
        system.clear_retriever()
        system.add_docs(updated_all_docs)
        result = system.generate(model_input)
        if (
            result.extra_output
            and result.extra_output.additional_info
            and "request_ids" in result.extra_output.additional_info
        ):
            request_ids: list[str] = result.extra_output.additional_info["request_ids"]
        else:
            request_ids: list[str] = []
        for iq, request_id in enumerate(request_ids):
            print(f"Request ID {iq}: {request_id}")
        print(colored(result.generated_text, color="red", on_color="on_white"))
