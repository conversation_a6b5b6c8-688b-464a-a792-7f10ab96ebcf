"""Utilities for experiments."""

import copy
import re

import pandas as pd
from pyspark.sql import dataframe as sparkDF
from pyspark.sql import functions as sparkF

from research.core import utils_for_log
from research.core.utils_for_str import (
    get_first_n_lines,
    get_last_n_lines,
    show_completion,
)
from research.data.rag import rogue, spark_utils
from research.data.rag.rogue_stages import FIMSampleConfig, RetrievalAugmentedSample
from research.data.rag.utils import filter_by_repo_size
from research.data.spark import k8s_session
from research.data.spark.utils import AugmentK8sSparkSession

ALL_LANGUAGES_in_STACK = [
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "ruby",
    "lua",
    "kotlin",
    "sql",
    "markdown",
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
]

SIZE_COLUMN = "size"
PATH_COLUMN = "max_stars_repo_path"
PROMPT_COLUMN = "prompt_tokens"
REPO_LANG_COLUMN = "max_size_lang"
REPO_LANG_SUBCOL = "langpart"
SAMPLES_COLUMN = "prompt_tokens"
FILE_LANG_COLUMN = "langpart"


def inverse_regex(pattern: str) -> str:
    """
    Given a regular expression pattern, returns an inverse pattern.

    Args:
    - pattern (str): The original regular expression pattern.

    Returns:
    - str: The inverse regular expression pattern.
    """
    # Ensure the pattern matches the entire string by anchoring it with `^` and `$`
    anchored_pattern = f"^(?!{pattern}).*$"
    return anchored_pattern


def get_unit_test_re_pattern(reverse: bool = False):
    """Get the regex pattern for unit test file path.

    - C: .c
    - C++: .cpp, .cc, .cxx
    - C# (C-Sharp): .cs
    - Go: .go
    - Java: .java
    - JavaScript: .js
    - Python: .py
    - Rust: .rs
    - TypeScript: .ts

    """
    pattern = r".*(tests[/\\].*|.*_test\..*|.*[/\\]test.*|test.*)\.(c|cpp|cc|cxx|cs|go|java|js|py|rs|ts)$"
    reverse_pattern = inverse_regex(pattern)
    if reverse:
        return reverse_pattern
    else:
        return pattern


def load_repos_from_stack(
    spark: AugmentK8sSparkSession,
    repo_min_size: int | None = None,
    repo_max_size: int | None = None,
    repo_languages: list[str] | None = None,
    limit_repos: int | None = None,
    random_seed: int | None = None,
) -> tuple[sparkDF.DataFrame, pd.DataFrame]:
    df = spark.read.parquet("s3a://the-stack-processed/by-repo-3")

    # Report statistics on repo languages
    top_languages = (
        df.groupBy(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])
        .count()
        .orderBy(sparkF.desc("count"))
        .limit(100)
    ).toPandas()

    # Filter for language of main repo being language we want to train on
    if repo_languages is not None:
        df = df.filter(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(repo_languages))

    df = filter_by_repo_size(
        df,
        min_size=repo_min_size,
        max_size=repo_max_size,
    )
    if limit_repos is not None:
        fraction = limit_repos * 1.0 / df.count()
        df_random_n = df.sample(
            withReplacement=False, fraction=fraction, seed=random_seed
        ).limit(limit_repos)

        num_partitions = max(limit_repos // 50, 20)
        df_random_n = df_random_n.repartition(num_partitions)
    else:
        df_random_n = df
    return df_random_n, top_languages


def generate_repos(
    input_url: str,
    output_url: str,
    config: FIMSampleConfig,
    repo_min_size: int | None = None,
    repo_max_size: int | None = None,
    limit_repos: int = 100,
) -> tuple[pd.DataFrame, str]:
    """Generate the repositories."""
    # This just does filtering then stores results to parquet files for later processing.
    # Almost entirely IO bound by write caching on CoreWeave side.
    # That is all spark job will finish writing in 5min but
    # will need another 15m for CW to flush their write cache on shared drives or object stores

    # Note that we fail one partition at a time, so
    # if you want more grainular failures,
    # you an create more partitions.

    spark = k8s_session(max_workers=100)
    print(f"{utils_for_log.time_string()} Processing retrieval samples")
    df = spark.read.parquet(input_url)

    # Report statistics on repo languages
    top_languages = (
        df.groupBy(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])
        .count()
        .orderBy(sparkF.desc("count"))
        .limit(100)
    )
    top_languages = top_languages.toPandas()

    # Filter for language of main repo being language we want to train on
    if hasattr(config, "repo_languages"):
        df = df.filter(
            df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages)
        )

    df = filter_by_repo_size(
        df,
        min_size=getattr(config, "repo_min_size", None),
        max_size=getattr(config, "repo_max_size", None),
    )
    total_repos = df.count()
    df_filtered = spark_utils.filter_repos_by_config(
        df,
        config,
        repo_min_size=repo_min_size,
        repo_max_size=repo_max_size,
        size_column=SIZE_COLUMN,
        path_column=PATH_COLUMN,
        file_lang_column=FILE_LANG_COLUMN,
    )
    total_filtered_repos = df_filtered.count()
    if total_filtered_repos == 0:
        raise ValueError("No available repos found")
    print(
        f"{utils_for_log.time_string()} Processing {total_filtered_repos:9d} / {total_repos:9d} repos",
        flush=True,
    )

    fraction = limit_repos * 1.0 / df.count()
    df_random_n = df_filtered.sample(
        withReplacement=False, fraction=fraction, seed=config.random_seed
    ).limit(limit_repos)

    num_partitions = max(limit_repos // 50, 20)
    df_random_n = df_random_n.repartition(num_partitions)
    df_random_n.write.parquet(output_url, mode="overwrite")

    df_random_n = spark.read.parquet(output_url)
    total_df_random_n = df_random_n.count()
    print(
        f"{utils_for_log.time_string()} Processing {total_df_random_n} repos",
        flush=True,
    )
    spark.stop()
    return top_languages, output_url


def show_truncated_completion(
    data: dict | RetrievalAugmentedSample,
    plines: int = 10,
    slines: int = 10,
    no_special_token: bool = True,
):
    """Show a colorful code completion results."""
    path = data["file_path"]
    prefix = data["prefix"]
    suffix = data["suffix"]
    middle = data["middle"]
    print(f"File Path: {path}\n")
    if no_special_token:
        pattern = re.compile(
            "|".join(
                re.escape(key) for key in ["<|pause|>", "<|skip|>", "<|endoftext|>"]
            )
        )
        middle = re.sub(pattern, "", middle)
    show_completion(
        get_last_n_lines(prefix, plines), get_first_n_lines(suffix, slines), middle
    )


def shift_middle_to_prefix(
    data: RetrievalAugmentedSample, num_shift_chars: int = 0
) -> RetrievalAugmentedSample:
    """Shift middle to prefix."""
    data = copy.deepcopy(data)
    num_shift_chars = min(num_shift_chars, len(data["middle"]))
    shift_chars, new_middle = (
        data["middle"][:num_shift_chars],
        data["middle"][num_shift_chars:],
    )
    data["prefix"] = data["prefix"] + shift_chars
    data["middle"] = new_middle
    return data
