"""Process the rogue pipeline."""

import pickle
import typing

import pandas as pd
import tree_sitter as ts
from megatron.tokenizer.tokenizer import StarCoder2Tokenizer

from research.data.rag.retrieval_utils import serialize_retrieved_chunks
from research.data.rag.rogue_stages import (
    FIMSampleConfig,
    RogueRetrievalConfig,
    generate_fim_samples_from_repo,
    generate_retrieved_chunks_from_fim,
)
from research.data.spark.pipelines.utils import map_parquet
from research.fim.fim_sampling import CSTFimSampler
from research.retrieval.null_retrieval_database import NullRetrievalDatabase
from research.retrieval.types import Document
from research.static_analysis.parsing import TsParsedFile


def repo_to_fim_problems_wrapper(
    input_path: str,
    output_path: str,
    spark,
    config: FIMSampleConfig,
    sampler: CSTFimSampler,
    get_node_weight: typing.Callable[[ts.Node, TsParsedFile], float] | None = None,
) -> dict[str, typing.Any]:
    """Convert the entire repo into retrieval-augmented FiM samples."""

    def _process_fn(batch: pd.DataFrame) -> typing.Iterator[pd.Series]:
        for _, repo_data in batch.iterrows():
            fim_problems = generate_fim_samples_from_repo(
                repo_data.file_list,
                config=config,
                sampler=sampler,
                get_node_weight=get_node_weight,
            )
            new_file_list = repo_data.file_list
            # Drop some keys that are useless but cause schema trouble.
            for file in new_file_list:
                file.pop("max_forks_count")
                file.pop("max_forks_repo_forks_event_max_datetime")
                file.pop("max_forks_repo_forks_event_min_datetime")
                file.pop("max_issues_count")
                file.pop("max_issues_repo_issues_event_max_datetime")
                file.pop("max_issues_repo_issues_event_min_datetime")
            repo_data["file_list"] = new_file_list
            repo_data["fim_problems"] = pickle.dumps(fim_problems)
            yield repo_data

    result = map_parquet.apply_pandas(
        spark,
        _process_fn,
        input_path=input_path,
        output_path=output_path,
        timeout=300,  # one hour timeout
        batch_size=16,
        # drop_original_columns=True,
        ignore_error=True,
    )
    return result


def repo_to_fim_samples_wrapper(
    input_path: str,
    output_path: str,
    spark,
    config_fim: FIMSampleConfig,
    config_retrieval: RogueRetrievalConfig,
    sampler: CSTFimSampler,
    get_node_weight: typing.Callable[[ts.Node, TsParsedFile], float] | None = None,
) -> dict[str, typing.Any]:
    """Convert the entire repo into FiM samples."""

    tokenizer = StarCoder2Tokenizer()

    def _process_fn(batch: pd.DataFrame) -> typing.Iterator[pd.Series]:
        for _, repo_data in batch.iterrows():
            all_docs = []
            for file in repo_data.file_list:
                # Only add files of desired languages to be retrieved
                if (
                    config_retrieval.retrieval_languages
                    and file["langpart"] not in config_retrieval.retrieval_languages
                ):
                    continue
                all_docs.append(
                    Document(
                        id=file["hexsha"],
                        text=file["content"],
                        path=file["max_stars_repo_path"],
                    )
                )
            all_docs = pickle.dumps(all_docs)
            fim_problems = generate_fim_samples_from_repo(
                repo_data.file_list,
                config=config_fim,
                sampler=sampler,
                get_node_weight=get_node_weight,
            )
            samples = generate_retrieved_chunks_from_fim(
                repo_data.file_list,
                fim_problems,
                config=config_retrieval,
                retrieval_database=NullRetrievalDatabase(),
                tokenizer=tokenizer,
            )
            converted_samples = []
            for sample in samples:
                assert not isinstance(sample["retrieved_chunks"], str)
                sample["retrieved_chunks"] = serialize_retrieved_chunks(
                    sample["retrieved_chunks"]
                )
                converted_samples.append(dict(sample))
            if converted_samples:
                yield pd.Series({"all_docs": all_docs, "samples": converted_samples})

    result = map_parquet.apply_pandas(
        spark,
        _process_fn,
        input_path=input_path,
        output_path=output_path,
        timeout=300,  # one hour timeout
        batch_size=16,
        # drop_original_columns=True,
        ignore_error=True,
    )
    return result
