"""Launch evals for the basic rag system.

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier research@elden_6k_ffw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-ffw \
    --exp_name SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-ffw --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier research@research_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp2 \
    --exp_name SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-fbw --num_gpus 2 --gpu H100 \
    --tasks cceval-1k

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_6k_ffw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6-ffw \
    --exp_name Simple-Elden-SC2-BS1536-S2K-LR1e5to1e6-FFW --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS1536-S2K-LR1e5to1e6 \
    --exp_name Simple-Elden-SC2-BS1536-S2K-LR1e5to1e6-FBW --num_gpus 2 --gpu H100 \
    --tasks cceval-1k

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M9000-mp1 \
    --exp_name Simple-Elden-SC2-BS2048S10K-M9000-FBW --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 cceval-1k

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M3600-mp1 \
    --exp_name Simple-Elden-SC2-R2BS2048S16K-M3600-FBW --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 cceval-1k

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_v2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp1 \
    --exp_name SimpleEldenV2-SC2-R2BS2048S16K-M7200-FBW --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@dscv2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-LR5e6to5e7-mp1 \
    --exp_name Simple-Elden-DSCV2-BS1536-S2K-LR1e5to1e6-FFW --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_v2_8k_ffw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw \
    --exp_name SimpleEldenV2-SC2-R2BS2048S16K-M7200-S8K-FFW --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_v2_8k_ffwfp8 --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0 \
    --exp_name SimpleEldenV2-SC2-R2BS2048S16K-M7200-S8K-FFWFP8 --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_v2_6k_ffwfp8 --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0 \
    --exp_name SimpleEldenV2-SC2-R2BS2048S16K-M7200-S6K-FFWFP8 --num_gpus 1 --gpu H100 \
    --tasks cceval-200

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prodreward@sc2_simple_elden_v2_8k_ffwfp8 --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0 \
    --exp_name EldenV5.1-S8K-Reward5-FFWFP8 --num_gpus 1 --gpu H100 \
    --tasks cceval-200

# Use Reward Model
CUDA_VISIBLE_DEVICES=1 python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prodreward@sc2_simple_elden_v2_8k_ffwfp8 --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0 \
    --exp_name EldenV5.1-S8K-Reward5-FFWFP8 --num_gpus 1 --gpu H100 \
    --tasks cceval --local

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_simple_elden_v2_6k_ffwfp8 --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-EldenV5-1-1-ffw-fp8 \
    --exp_name EldenV5.1.1-S6K-FFWFP8 --num_gpus 1 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@dscv2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-LR5e6to5e7-mp2 \
    --exp_name Simple-Elden-DSCV2-BS1536-S2K-LR1e5to1e6-FFW --num_gpus 2 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@dscv2_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/dscv2-elden/DSCV2LiteBaseDMoE-ELDEN-BS1536S2K-LR1e5to1e6-mp2 \
    --exp_name Elden-DSCV2-BS1536-S2K-LR1e5to1e6-FBW --num_gpus 2 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@sc2_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4KS1K-LR1e6-ResumeV3-mp2 \
    --exp_name Elden-SC2-Elden-V4.3-FBW --num_gpus 2 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@dscv2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseSMoE-SIM-BS1536S2K-LR5e6to5e7-mp2 \
    --exp_name SimElden-DSCV2-Sparse-BS1536-S2K-LR1e5to1e6-FBW --num_gpus 2 --gpu H100 \
    --tasks fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier prod@dscv2_simple_elden_6k_fbw --ckp_path /mnt/efs/augment/checkpoints/dxy/dscv2-simple-elden/DSCV2LiteBaseDMoE-SIM-BS1536S2K-V2-mp2 \
    --exp_name SimElden-DSCV2-D-BS1536-S2K-V2-FBW --num_gpus 2 --gpu H100 \
    --tasks cceval-1k fimeval cceval hindsight hs-06 multilang functions 23lines api

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier dev@eldenv5-1-15b --ckp_path "" --exp_name Elden-V5.1-DEV --gpu A5000 \
    --tasks cceval hindsight

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier dogfood@eldenv3-15b --ckp_path "" \
    --exp_name dogfood-eldenv3-15b --gpu A5000 \
    --tasks cceval hindsight multilang fimeval

python experimental/dxy/rag/exps/launch_elden_eval.py \
    --identifier dogfood@eldenv4-2-15b --ckp_path "" \
    --exp_name dogfood-eldenv4-2-15b --gpu A5000 \
    --tasks cceval hindsight multilang fimeval
"""

import argparse
import copy
import json
import os
import pathlib

import yaml

from experimental.dxy.rag.exps.model_configs import model_config_dict
from experimental.dxy.rag.exps.retriever_configs import retriever_config_dict
from research.eval.eval import __file__ as eval_file
from research.eval.eval_lib import run_local
from research.utils.inspect_indexed_dataset import print_green


def get_ckp_sha(ckp_path: pathlib.Path) -> str:
    info_path = pathlib.Path(ckp_path) / "info.json"
    assert info_path.exists()
    # Run cat .../info.json | jq '.manifestSha256'
    with open(info_path, "r") as file:
        xdata = json.load(file)
        manifest_sha256 = xdata.get("manifestSha256")
        assert manifest_sha256 is not None, f"Check {info_path}"
    return manifest_sha256


def execute_eval(
    determined_name: str,
    system_config: dict,
    task_config: dict,
    pod_spec: str,
    determined_overrides: dict | None = None,
    additional_overrides: dict | None = None,
    local: bool = False,
):
    all_config = {
        "system": system_config,
        "task": task_config,
        "podspec": pod_spec,
        "determined": {
            "name": determined_name,
            "workspace": "Dev",
            "project": "Xuanyi-Eval",
            "metaconfig": "jobs/templates/eval-exec-v2-metaconfig.yaml",
        },
    }
    all_config["determined"].update(determined_overrides or {})
    all_config.update(additional_overrides or {})

    # convert the config to yaml string
    config_str = yaml.dump(all_config, indent=2)
    print_green("Launching eval with the following config:")
    print(config_str)
    config_path = pathlib.Path("/tmp/current_eval_config.yml")
    config_path.write_text(config_str)
    cmd = f"python3 {eval_file} --skip_bazel {config_path}"
    if local:
        run_local(
            config_files=[str(config_path)],
            checkpoint_path="",
            job_root="/home/<USER>/eval_jobs",
        )
    else:
        print_green(f"Executing command: {cmd}")
        os.system(cmd)


def get_system_config(
    identifier: str,
    ckp_path: pathlib.Path,
    dense_retriever: str = "sethanol",  # "ethanol616_ffw",
    # dense_retriever: str = "sethanol_smart",
    sig_retriever: str = "methanol0416_v2",
    num_gpus: int = 1,
) -> dict:
    meta, model_name = identifier.split("@")
    if meta in ("dev", "dogfood"):
        print("This is a remote model config and does not need ckp.")
        remote = meta
        if remote == "dev":
            client_url = "https://dev-dxy.us-central.api.augmentcode.com/"
        elif remote == "dogfood":
            client_url = "https://dogfood.api.augmentcode.com/"
        else:
            raise ValueError(f"Unknown remote {remote}")
        system_config = {
            "name": "remote_completion",
            "model_name": model_name,
            "client": {
                "url": client_url,
                "timeout": 60,
                "retry_count": 5,
                "retry_sleep": 5.0,
            },
            "retriever": {
                "disable_extension_filtering": True,
                "wait_indexing_retry_count": 256,
                "wait_indexing_retry_sleep_secs": 4.0,
            },
            "completion": {
                "retry_count": 8,
            },
            "tokenizer_name": "StarCoder2Tokenizer",
        }
    elif meta == "research":
        model_config = copy.deepcopy(model_config_dict[model_name])
        dense_retriever_config = retriever_config_dict[dense_retriever]
        sig_retriever_config = retriever_config_dict[sig_retriever]
        seq_length = model_config["seq_length"]
        if "checkpoint_path" in model_config:
            assert model_config["checkpoint_path"] is None
            model_config["checkpoint_path"] = str(ckp_path)
            model_config["model_parallel_size"] = num_gpus
        elif "model_path" in model_config:
            # This is a FastForward model
            assert model_config["model_path"] is None
            model_config["model_path"] = str(ckp_path)
            if model_config["name"] == "starcoder2_fastforward":
                model_config["checkpoint_path"] = str(ckp_path)
            model_config.pop("seq_length")
        else:
            raise ValueError(f"Unknown model {model_config}")
        if model_name.endswith("ffw") or model_name.endswith("ffwfp8"):
            # Checkout SHA365
            info_path = pathlib.Path(model_config["checkpoint_path"]) / "info.json"
            assert info_path.exists()
            # Run cat .../info.json | jq '.manifestSha256'
            with open(info_path, "r") as file:
                xdata = json.load(file)
                manifest_sha256 = xdata.get("manifestSha256")
                assert manifest_sha256 is not None, f"Check {info_path}"
            model_config["checkpoint_sha256"] = manifest_sha256

        system_config = {
            "name": "elden",
            "model": model_config,
            "generation_options": {
                "max_generated_tokens": seq_length
                - model_config["prompt"]["max_prompt_tokens"]
            },
            "dense_retriever": dense_retriever_config,
            "signature_retriever": sig_retriever_config,
            "experimental": {
                "remove_suffix": False,
                "trim_on_dedent": False,
                "retriever_top_k": 32,
                "signature_retriever_top_k": 32,
            },
            "fim_gen_mode": "evaluation",
        }
    elif meta == "prod":
        model_config = copy.deepcopy(model_config_dict[model_name])
        dense_retriever_config = retriever_config_dict[dense_retriever]
        sig_retriever_config = retriever_config_dict[sig_retriever]
        seq_length = model_config["seq_length"]
        if "checkpoint_path" in model_config:
            assert model_config["checkpoint_path"] is None
            model_config["checkpoint_path"] = str(ckp_path)
            model_config["model_parallel_size"] = num_gpus
        elif "model_path" in model_config:
            # This is a FastForward model
            assert model_config["model_path"] is None
            model_config["model_path"] = str(ckp_path)
            model_config["checkpoint_path"] = str(ckp_path)
            model_config.pop("seq_length")
        else:
            raise ValueError(f"Unknown model {model_config}")
        if model_name.endswith("ffw") or model_name.endswith("ffwfp8"):
            # Checkout SHA365
            info_path = pathlib.Path(model_config["checkpoint_path"]) / "info.json"
            assert info_path.exists()
            # Run cat .../info.json | jq '.manifestSha256'
            with open(info_path, "r") as file:
                xdata = json.load(file)
                manifest_sha256 = xdata.get("manifestSha256")
                assert manifest_sha256 is not None, f"Check {info_path}"
            model_config["checkpoint_sha256"] = manifest_sha256
        # fmt: off
        if "max_prompt_length" in model_config["formatter_config"]["prompt_formatter_config"]:
            max_generated_tokens = seq_length - model_config["formatter_config"]["prompt_formatter_config"]["max_prompt_length"]
        elif "apportionment_config" in model_config["formatter_config"]:
            max_generated_tokens = model_config.pop("max_generated_tokens")
        else:
            raise ValueError(f"Unknown model {model_config}")
        # fmt: on
        system_config = {
            "name": "prod_elden",
            "model": model_config,
            "generation_options": {"max_generated_tokens": max_generated_tokens},
            "dense_retriever": dense_retriever_config,
            "signature_retriever": sig_retriever_config,
            "config": {
                "line_chunk_retriever_top_k": 64,
                "sig_chunk_retriever_top_k": 64,
                "fim_gen_mode": "evaluation",
            },
        }
        system_config["tokenizer"] = model_config.pop("tokenizer")
        system_config["formatter_config"] = model_config.pop("formatter_config")
    elif meta == "prodreward":
        model_config = copy.deepcopy(model_config_dict[model_name])
        name = model_config.get("name", None)
        assert (
            name == "starcoder2_fp8_fastforward"
        ), "Must use FP8 model to reduce memory usage."
        dense_retriever_config = retriever_config_dict[dense_retriever]
        sig_retriever_config = retriever_config_dict[sig_retriever]
        seq_length = model_config["seq_length"]
        assert "model_path" in model_config and model_config["model_path"] is None
        model_config["model_path"] = str(ckp_path)
        model_config["checkpoint_path"] = str(ckp_path)
        model_config.pop("seq_length")
        model_config["checkpoint_sha256"] = get_ckp_sha(
            pathlib.Path(model_config["checkpoint_path"])
        )
        max_generated_tokens = (
            seq_length
            - model_config["formatter_config"]["prompt_formatter_config"][
                "max_prompt_length"
            ]
        )
        # reward_model_path = "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-HONLY-ffw-fp8"
        reward_model_path = "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden-rlhf/ABS-15B-SIMV2-MIXAJ-ffw"
        system_config = {
            "name": "prod_elden_w_reward",
            "model": model_config,
            "reward_model": {
                "name": "starcoder2_fastforward",
                "checkpoint_path": reward_model_path,
                "checkpoint_sha256": get_ckp_sha(pathlib.Path(reward_model_path)),
                "model_path": reward_model_path,
            },
            "generation_options": {
                "max_generated_tokens": max_generated_tokens,
                "temperature": 1.0,
                "top_p": 0.95,
            },
            "dense_retriever": dense_retriever_config,
            "signature_retriever": sig_retriever_config,
            "num_trails": 5,
            "config": {
                "line_chunk_retriever_top_k": 64,
                "sig_chunk_retriever_top_k": 64,
                "fim_gen_mode": "evaluation",
            },
        }
        system_config["tokenizer"] = model_config.pop("tokenizer")
        system_config["formatter_config"] = model_config.pop("formatter_config")
    else:
        raise ValueError(f"Unknown identifier: {identifier}")
    return system_config


def main(args: argparse.Namespace):
    system_config = get_system_config(
        args.identifier, ckp_path=args.ckp_path, num_gpus=args.num_gpus
    )

    tasks = {
        "cceval": {"name": "cceval"},
        "cceval-1k": {"name": "cceval", "limit": 1000},
        "cceval-200": {"name": "cceval", "limit": 200},
        "multilang": {
            "name": "hydra",
            "dataset": "all_languages_2-3lines_medium_to_hard.v1.0",
            "hydra_block_resource_internet_access": True,
        },
        "functions": {"name": "hydra", "dataset": "repoeval_functions"},
        "23lines": {"name": "hydra", "dataset": "repoeval_2-3lines"},
        "api": {"name": "api", "dataset": "finegrained-python.large"},
        "hindsight": {
            "name": "hindsight",
            "dataset": "2024-04-25-v0.7",
            "tenant_name": "dogfood",
            "blob_limit": 9000,
            "service_account_file": "/mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json",
        },
        "hs-06": {
            "name": "hindsight",
            "dataset": "2024-06-01-v1.0",
            "tenant_name": "dogfood",
            "blob_limit": 9000,
            "service_account_file": "/mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json",
        },
        "cceval-unit-test": {
            "name": "cceval",
            "unit_test_only": True,
        },
        "fimeval": {"name": "fimeval"},
    }

    for key, task_config in tasks.items():
        if args.tasks and key not in args.tasks:
            continue
        if task_config["name"] in ("cceval", "hindsight", "fimeval"):
            pod_spec = f"{args.num_gpus}x{args.gpu}.yaml"
        else:
            pod_spec = f"{args.num_gpus}x{args.gpu}.yaml"
        if "A100" not in pod_spec and "H100" not in pod_spec:
            assert args.num_gpus == 1
            pod_spec = f"{args.gpu}.yaml"
        if task_config["name"] == "functions":
            system_config["experimental"]["trim_on_dedent"] = True

        determined_name = (
            f"{args.exp_name} - {key} - {args.identifier} - {args.ckp_path.name}"
        )

        print(determined_name)
        augment_args = {
            "gpu_count": args.num_gpus,
            "project_group": "finetuning",
        }
        if task_config["name"] == "hindsight":
            augment_args["dai_gcp_service_accounts"] = [
                {
                    "secret": "aug-prod-cw-ri-importer",  # pragma: allowlist secret
                    "mountpoint": "/mnt/augment/secrets/cw-ri-importer",
                }
            ]
        execute_eval(
            determined_name=determined_name,
            system_config=system_config,
            task_config=task_config,
            pod_spec=pod_spec,
            additional_overrides={"augment": augment_args},
            local=args.local,
        )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--identifier",
        type=str,
        default=None,
        required=True,
        help="The identifier to point out the system and model. m",
    )
    parser.add_argument(
        "--ckp_path",
        type=pathlib.Path,
        required=True,
        help="The checkpoint path.",
    )
    parser.add_argument(
        "--exp_name",
        type=str,
        required=True,
        help="The experiment name.",
    )
    parser.add_argument(
        "--num_gpus",
        type=int,
        default=1,
        help="The number of GPUs to use.",
    )
    parser.add_argument(
        "--gpu",
        type=str,
        choices=("H100", "A100", "A5000"),
        default="H100",
        help="The GPU type to use.",
    )
    parser.add_argument(
        "--tasks",
        nargs="+",
        required=True,
        help="List of specific task names to run.",
    )
    parser.add_argument(
        "--local",
        action="store_true",
        default=False,
        help="Run evaluations outside determined.ai cluster",
    )
    parsed_args = parser.parse_args()

    main(parsed_args)
