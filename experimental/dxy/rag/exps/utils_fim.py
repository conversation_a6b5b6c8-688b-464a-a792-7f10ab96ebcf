"""A set of utilities for experiments."""

import pathlib

import tree_sitter as ts

from base.static_analysis.parsing import TsParsedFile
from research.core.constants import AUGMENT_ROOT
from research.eval.dataset_generation_lib.finegrained_patch_generators import (
    find_ts_parent_if_same_range,
    get_ts_node_grandpa_type,
    get_ts_node_index_in_siblings,
    get_ts_node_parent_type,
)
from research.fim.fim_sampling import check_as_similar_literal_v1, has_literal
from research.static_analysis.common import decode_bytes, get_ts_node_sibling


def load_case_1():
    test_file_path = "base/fastforward/llama/fwd_llama_fp8_test.py"
    test_file_content = pathlib.Path(AUGMENT_ROOT / test_file_path).read_text()
    return test_file_path, test_file_content


def load_case_2():
    test_file_path = "base/prompt_format_completion/ender_prompt_formatter_test.py"
    test_file_content = pathlib.Path(AUGMENT_ROOT / test_file_path).read_text()
    return test_file_path, test_file_content


def _is_func_node(node: ts.Node) -> bool:
    return node.type in ("decorated_definition", "function_definition")


def _is_func_name(node: ts.Node) -> bool:
    return (
        node.type == "identifier"
        and get_ts_node_parent_type(node) == "function_definition"
    )


def _is_docstr(node: ts.Node) -> bool:
    if node.type != "string":
        return False
    node_text = decode_bytes(node.text)
    if not node_text.startswith(("'''", '"""', 'r"""', "r'''")):
        return False
    if node.parent is None:
        return False
    node = node.parent
    node = find_ts_parent_if_same_range(node)
    if (
        get_ts_node_parent_type(node) == "block"
        and get_ts_node_index_in_siblings(node) == 0
        and get_ts_node_grandpa_type(node) == "function_definition"
    ):
        return True
    elif (
        node.type == "block" and get_ts_node_parent_type(node) == "function_definition"
    ):
        return True
    else:
        return False


def _is_normal_statement(node: ts.Node) -> bool:
    """Whether a statement is a normal statement."""
    # We did not include "assignment" as it will usually also a "expression_statement".
    return node.type in ("expression_statement", "assert_statement")


def _is_literal(node: ts.Node) -> bool:
    if node.type not in ("string", "integer", "float", "true", "false", "none"):
        return False
    if node.parent is None:
        return False
    # if node.parent.type == "array":
    #     return False
    return True


def _has_literal(node: ts.Node | None) -> bool:
    if node is None:
        return False
    if _is_literal(node):
        return True
    for child in node.children:
        if _has_literal(child):
            return True
    return False


def customized_node_weight(node: ts.Node) -> float:
    """Return the weight of the given node, as it will later be multiplied by the score in range of (1e)."""
    num_bytes = node.start_byte - node.end_byte
    num_lines = decode_bytes(node.text).count("\n")
    if num_bytes >= 2500:
        return 0.0
    standard_weight, tiny_weight = 10.0, 0.5
    reduced_weight = 3.0
    # Entire functions
    if _is_func_name(node):
        return tiny_weight
    elif _is_func_node(node):
        if num_lines < 10:
            return tiny_weight
        elif num_lines < 160:
            return standard_weight
        else:
            return reduced_weight
    elif _is_docstr(node):
        return standard_weight
    elif _is_normal_statement(node):
        return reduced_weight  # since the number of lines are too many
    elif _is_literal(node):
        return tiny_weight
    else:
        return 0.0


def _check_sibling_has_text(node: ts.Node, sibling_offset: int, text: str) -> bool:
    """Check the sibling has the given text."""
    sibling = get_ts_node_sibling(node, sibling_offset, skip_empty=True)
    if sibling is None:
        return False
    return text.lower() in decode_bytes(sibling.text).lower()


def _has_text_in_all_3_siblings(node: ts.Node, text: str) -> bool:
    """Check the sibling has the given text."""
    for offset in range(-1, 2):
        sibling = get_ts_node_sibling(
            node, offset, skip_empty=True, skip_punctuation=True, skip_comment=True
        )
        if sibling is None:
            return False
        if not text.lower() in decode_bytes(sibling.text).lower():
            return False
    return True


def get_node_weight_assert_v1(node: ts.Node, pfile: TsParsedFile) -> float:
    """Upweight the assert related nodes."""
    if node.type in (
        "module",
        "function_definition",
        "function_declaration",
        "decorated_definition",
        "block",
    ):
        return 0.0
    if find_ts_parent_if_same_range(node) != node:
        return 0.0
    text = decode_bytes(node.text).lower()
    if len(text) == 0:
        return 0.0
    num_lines = text.count("\n")
    if num_lines > 20:
        return 0.0
    if _has_text_in_all_3_siblings(node, "assert"):
        return 1.0
    else:
        return 0.0


def _has_ancestor_in_types(node: ts.Node | None, types: tuple[str]) -> bool:
    """Check that whether the ancestor has the given type."""
    if node is None:
        return False
    if node.type in types:
        return True
    else:
        return _has_ancestor_in_types(node.parent, types)


def get_node_weight_literal_v1(node: ts.Node, pfile: TsParsedFile) -> float:
    """Check whether the node is a generic literal node."""
    text = decode_bytes(node.text).lower()
    num_lines = text.count("\n")
    if node.type in (
        "module",
        "function_definition",
        "function_declaration",
        "decorated_definition",
        "block",
    ):
        return 0.0
    if len(text) <= 1 or num_lines > 10:
        return 0.0
    if not has_literal(node):
        return 0.0
    check_results = []
    for offset in range(-1, 2):
        sibling = get_ts_node_sibling(
            node, offset, skip_empty=True, skip_punctuation=True, skip_comment=True
        )
        check_result = check_as_similar_literal_v1(sibling, node)
        check_results.append(check_result)
    weight = (
        float(len(text) > 10)
        + float(num_lines >= 2)
        + float(num_lines >= 5)
        - float(len(text) > 100)
    )
    weight = min(weight, 0.5)
    if check_results[0] and check_results[1]:
        return weight
    # elif check_results[1] and check_results[2]:
    #     return weight
    else:
        return 0.0
