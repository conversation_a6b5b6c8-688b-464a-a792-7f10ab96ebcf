"""Utilities for experiments."""

import functools
import pickle
import typing

import pandas as pd
import tree_sitter as ts
from megatron.tokenizer.tokenizer import StarCoder2Tokenizer

# from pyspark.sql import dataframe as sparkDF
from pyspark.sql import functions as sparkF

import research.fim.fim_sampling as fim_sampling
from research.core import utils_for_log
from research.data.rag import rogue_stages, spark_utils
from research.data.rag.retrieval_utils import serialize_retrieved_chunks
from research.data.rag.rogue_stages import (
    generate_fim_samples_from_repo,
    generate_retrieved_chunks_from_fim,
)
from research.data.rag.utils import filter_by_repo_size
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.utils import AugmentK8sSparkSession
from research.static_analysis.parsing import TsParsedFile

ALL_LANGUAGES_in_STACK = [
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "ruby",
    "lua",
    "kotlin",
    "sql",
    "markdown",
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
]

SIZE_COLUMN = "size"
PATH_COLUMN = "max_stars_repo_path"
PROMPT_COLUMN = "prompt_tokens"
REPO_LANG_COLUMN = "max_size_lang"
REPO_LANG_SUBCOL = "langpart"
SAMPLES_COLUMN = "prompt_tokens"
FILE_LANG_COLUMN = "langpart"


def generate_repos(
    input_url: str,
    output_url: str,
    config: rogue_stages.FIMSampleConfig,
    repo_min_size: int | None = None,
    repo_max_size: int | None = None,
    limit_repos: int = 100,
) -> tuple[pd.DataFrame, pd.DataFrame, str]:
    """Generate the repositories."""
    # This just does filtering then stores results to parquet files for later processing.
    # Almost entirely IO bound by write caching on CoreWeave side.
    # That is all spark job will finish writing in 5min but
    # will need another 15m for CW to flush their write cache on shared drives or object stores

    # Note that we fail one partition at a time, so
    # if you want more grainular failures,
    # you an create more partitions.

    spark = k8s_session(max_workers=100)
    print(
        f"{utils_for_log.time_string()} process all repos from {input_url} with a limit of {limit_repos}"
    )
    df = spark.read.parquet(input_url)

    # Report statistics on repo languages
    top_languages = (
        df.groupBy(df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])
        .count()
        .orderBy(sparkF.desc("count"))
        .limit(100)
    ).toPandas()

    # Filter for language of main repo being language we want to train on
    if hasattr(config, "repo_languages"):
        df = df.filter(
            df[REPO_LANG_COLUMN][REPO_LANG_SUBCOL].isin(config.repo_languages)
        )

    df = filter_by_repo_size(
        df,
        min_size=getattr(config, "repo_min_size", None),
        max_size=getattr(config, "repo_max_size", None),
    )
    total_repos = df.count()
    df_post_filter = spark_utils.filter_repos_by_config(
        df,
        config,
        repo_min_size=repo_min_size,
        repo_max_size=repo_max_size,
        size_column=SIZE_COLUMN,
        path_column=PATH_COLUMN,
        file_lang_column=FILE_LANG_COLUMN,
    )
    num_kept_repos = df_post_filter.count()
    if num_kept_repos == 0:
        raise ValueError("No available repos found")
    print(
        f"{utils_for_log.time_string()} Processing {num_kept_repos:9d} / {total_repos:9d} repos",
        flush=True,
    )

    fraction = float(limit_repos) / num_kept_repos
    fraction = min(max(fraction * 2, fraction + 0.1), 1.0)
    df_random_n = df_post_filter.sample(
        withReplacement=False, fraction=fraction, seed=config.random_seed
    ).limit(limit_repos)

    num_partitions = max(limit_repos // 50, 20)
    df_random_n = df_random_n.repartition(num_partitions)
    df_random_n.write.parquet(output_url, mode="overwrite")

    df_random_n = spark.read.parquet(output_url)
    total_df_random_n = df_random_n.count()
    final_top_languages = (
        df_random_n.groupBy(df_random_n[REPO_LANG_COLUMN][REPO_LANG_SUBCOL])
        .count()
        .orderBy(sparkF.desc("count"))
        .limit(100)
    ).toPandas()
    print(
        f"{utils_for_log.time_string()} Processing {total_df_random_n} repos",
        flush=True,
    )
    spark.stop()
    print(f"{utils_for_log.time_string()} Finish generating repos into {output_url}")
    return top_languages, final_top_languages, output_url


def repo_to_fim_problems_wrapper(
    input_path: str,
    output_path: str,
    spark: AugmentK8sSparkSession,
    config: rogue_stages.FIMSampleConfig,
    sampler_get_node_weight: typing.Sequence[
        typing.Tuple[
            fim_sampling.CSTFimSampler,
            typing.Callable[[ts.Node, TsParsedFile], float] | None,
        ]
    ],
) -> dict[str, typing.Any]:
    """Convert the entire repo into retrieval-augmented FiM samples."""

    def _process_fn(batch: pd.DataFrame) -> typing.Iterator[pd.Series]:
        for _, repo_data in batch.iterrows():
            fim_problems: list[fim_sampling.FimProblem] = []
            for sampler, get_node_weight in sampler_get_node_weight:
                fim_problems.extend(
                    generate_fim_samples_from_repo(
                        repo_data.file_list,
                        config,
                        sampler=sampler,
                        get_node_weight=get_node_weight,
                    )
                )
            new_file_list = repo_data.file_list
            # Drop some keys that are useless but cause schema trouble.
            for file in new_file_list:
                file.pop("max_forks_count")
                file.pop("max_forks_repo_forks_event_max_datetime")
                file.pop("max_forks_repo_forks_event_min_datetime")
                file.pop("max_issues_count")
                file.pop("max_stars_count")
                file.pop("max_stars_repo_licenses")
                file.pop("max_stars_repo_stars_event_min_datetime")
                file.pop("max_stars_repo_stars_event_max_datetime")
                file.pop("max_issues_repo_issues_event_max_datetime")
                file.pop("max_issues_repo_issues_event_min_datetime")
            repo_data["file_list"] = new_file_list
            repo_data["fim_problems"] = pickle.dumps(fim_problems)
            repo_data["num_fim_problems"] = len(fim_problems)
            if len(fim_problems):
                yield repo_data

    result = map_parquet.apply_pandas(
        spark,
        _process_fn,
        input_path=input_path,
        output_path=output_path,
        timeout=600,  # 10 minutes timeout
        batch_size=16,
        # drop_original_columns=True,
        ignore_error=True,
    )
    return result


def _repo_with_fim_to_retrieval_fn(
    batch: pd.DataFrame, config: rogue_stages.RogueRetrievalConfig
) -> typing.Iterator[pd.Series]:
    """This is to process a batch of data."""
    from research.eval.harness.factories import create_retriever  # pylint: disable=unused-import,import-outside-toplevel # noqa: F401

    if config.multi_retriever_configs is None:
        retriever_config = {
            "scorer": config.scorer_config,
            "chunker": config.chunker_config,
            "query_formatter": config.query_config,
            "document_formatter": config.document_config,
        }
    else:
        retriever_config = {
            "name": "multi_retriever",
            "retrievers": config.multi_retriever_configs,
        }
        if config.multi_retriever_weights is not None:
            retriever_config["database_weights"] = config.multi_retriever_weights

    retrieval_database = create_retriever(retriever_config)

    retrieval_database.load()
    tokenizer = StarCoder2Tokenizer()
    for _, repo_data in batch.iterrows():
        fim_problems = pickle.loads(repo_data.fim_problems)
        for sample in generate_retrieved_chunks_from_fim(
            repo_data.file_list,
            fim_problems,
            config=config,
            retrieval_database=retrieval_database,
            tokenizer=tokenizer,
        ):
            retrieved_chunks = sample["retrieved_chunks"]
            assert not isinstance(retrieved_chunks, str)
            sample["retrieved_chunks"] = serialize_retrieved_chunks(retrieved_chunks)
            yield pd.Series(dict(sample))


def repo_with_fim_problems_to_retrieval_wrapper(
    input_path: str,
    output_path: str,
    spark: AugmentK8sSparkSession,
    config: rogue_stages.RogueRetrievalConfig,
    batch_size: int = 128,
) -> dict[str, typing.Any]:
    """Convert the entire repo into retrieval-augmented FiM samples."""

    result = map_parquet.apply_pandas(
        spark,
        functools.partial(_repo_with_fim_to_retrieval_fn, config=config),
        input_path=input_path,
        output_path=output_path,
        timeout=3600,  # one hour timeout
        batch_size=batch_size,
        # drop_original_columns=True,
        ignore_error=True,
    )
    return result


def generate_retrieval_augmented_samples(
    input_url: str, output_url: str, config: rogue_stages.RogueRetrievalConfig
):
    spark_conf = {
        "spark.executor.pyspark.memory": "64G",
        "spark.executor.memory": "60G",
        "spark.sql.parquet.columnarReaderBatchSize": "256",
        "spark.rpc.lookupTimeout": "240",
        "spark.task.cpus": "5",
    }

    spark: AugmentK8sSparkSession = k8s_session(
        max_workers=256,
        conf=spark_conf,
        gpu_type="RTX_A5000",
        gpu_count=1,
    )
    repo_with_fim_problems_to_retrieval_wrapper(
        input_url,
        output_url,
        spark,
        config,
        batch_size=256,
    )
    df = spark.read.parquet(output_url)
    total_samples = df.count()
    spark.stop()
    print(
        f"{utils_for_log.time_string()}: finish generating {total_samples} retrieval augmented samples into {output_url}"
    )
