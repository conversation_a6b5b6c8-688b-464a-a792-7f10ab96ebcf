"""Notebook to generate Rogue samples.

# If we use "c", "c++", "c-sharp", "go", "java", "javascript", "python", "rust", "typescript"
# and filter the repo that does not have any unit test files, we get 110561 / 167154 repos.

# 50K repos -> 1.4M unit test samples?
python experimental/dxy/rogue/exps/gen-unit-test-samples.py --stage repos samples --limit_repos 50000
"""

import argparse
import dataclasses
from datetime import datetime
from functools import partial


from research.fim import fim_sampling
from research.core import utils_for_log
from research.data.rag.rogue import (
    RogueSampleConfig,
    make_process_partition_fn,
)
from research.data.rag.utils import filter_by_repo_size, inspect_samples, save_config_s3
from research.data.spark import k8s_session
from research.data.spark.pipelines.utils import map_parquet
from research.utils.generate_fim_data import FimDataProcessor

from experimental.dxy.rag.exps.utils import generate_repos, get_unit_test_re_pattern

# These languages names are for the stack
REPO_LANGUAGES = [
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "ruby",
    "lua",
    "kotlin",
]

REPO_LANGUAGES_TEMP_FOR_UNIT_TEST = [
    "c",
    "c++",
    "c-sharp",
    "java",
    "javascript",
    "python",
    "rust",
    "go",
    "typescript",
]

SAMPLE_LANGUAGES = REPO_LANGUAGES_TEMP_FOR_UNIT_TEST

additional_retrieval_languages = [
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
]
RETRIEVAL_LANGUAGES = (
    REPO_LANGUAGES + ["sql", "markdown"] + additional_retrieval_languages
)


def get_config(limit_repos: int = 1000):
    file_path_regex_pattern = get_unit_test_re_pattern(reverse=False)
    output_name = "only_unit_test"

    config = RogueSampleConfig(
        input="s3a://the-stack-processed/by-repo-3",
        output=f"s3a://dxy-dev-bucket/ragdata/limit-repo-{limit_repos}/{output_name}",
        # output="s3a://michiel-dev-bucket/ragdata/eth6_4m_morelang4/",
        fim_version=FimDataProcessor.VERSION,
        repo_languages=REPO_LANGUAGES_TEMP_FOR_UNIT_TEST,
        sample_languages=SAMPLE_LANGUAGES,
        retrieval_languages=RETRIEVAL_LANGUAGES,
        file_path_regex_pattern=file_path_regex_pattern,
        limit_repos=limit_repos,
        repo_min_size=500000,
        repo_max_size=100000000,
        every_n_lines=200,
        max_problems_per_file=5,
        small_downsampled_probability=0.1,
        small_downsample_char_threshold=1500,
        small_filter_char_threshold=50,
        random_seed=74912,
        num_retrieved_chunks=40,
        scorer_config={
            "name": "ethanol",
            "checkpoint_path": "ethanol/ethanol6-04.1",
        },
        chunker_config={
            "name": "line_level",
            "max_lines_per_chunk": 30,
            "include_scope_annotation": False,
        },
        query_config={
            "name": "ethanol6_query",
            "max_tokens": 1023,
            "add_path": True,
        },
        document_config={
            "name": "ethanol6_document",
            "max_tokens": 999,
            "add_path": True,
        },
    )
    return config


def generate_samples(
    config: RogueSampleConfig,
    STAGE1_URI: str,
    sampler: fim_sampling.CSTFimSampler | None = None,
):
    """Generate samples."""
    # With that we estimate just over 20min per parquet file.
    # At 100 workers and 2000 files that is about 10 hours of work
    # Setting timeout to 1h to be safe
    # Do a timing run to see where my timeouts need to be and have some notion of memory usage
    # The GPU part takes less than half of the total time so GPU type probably doesn't matter.
    # It got to 3G memory usage after 1 batch and needs 7 batches, so mem is tight.
    # We increase it a bit here
    spark_conf = {
        "spark.executor.pyspark.memory": "50G",
        "spark.executor.memory": "30G",
        "spark.sql.parquet.columnarReaderBatchSize": "256",
    }

    spark_conf["spark.task.cpus"] = "5"
    max_workers = min(64, config.limit_repos)
    spark = k8s_session(
        max_workers=max_workers,
        conf=spark_conf,
        gpu_type="RTX_A5000",
    )

    result = map_parquet.apply_pandas(
        spark,
        make_process_partition_fn(config),
        input_path=STAGE1_URI,
        output_path=config.output,
        timeout=3600,  # one hour timeout
        batch_size=min(100, config.limit_repos // max_workers),
        # drop_original_columns=True,
        ignore_error=True,
    )
    spark.stop()
    print("result", result)
    stderr = result["task_info"]["stderr"]
    print("stderr", stderr)
    stdout = result["task_info"]["stdout"]
    print("stdout", stdout[0])
    # Save the config into S3
    path = config.output.split("s3a://")[1]
    bucket_name, file_name = path.split("/", 1)
    file_name += "config.json"
    save_config_s3(
        config=dataclasses.asdict(config), bucket_name=bucket_name, file_name=file_name
    )
    return result


if __name__ == "__main__":
    # Prase an args to use args.stage to decide which stage to run

    parser = argparse.ArgumentParser(description="Generate samples.")
    parser.add_argument(
        "--stage", type=str, choices=["repos", "samples"], nargs="*", required=True
    )
    parser.add_argument(
        "--limit_repos",
        type=int,
        help="The number of repos to process",
        required=True,
    )
    args = parser.parse_args()

    config = get_config(limit_repos=args.limit_repos)
    print(config)
    now = datetime.now()
    formatted_time = now.strftime("%Y-%m-%d_%H-%M-%S")
    temp_url = (
        f"s3a://augment-temporary/dxy/gen_unit_test/{formatted_time}/step_1_repos/"
    )
    print(f"Temporary URL : {temp_url}")
    print(f"Config.output : {config.output}")

    # Iterate over stages
    for stage in args.stage:
        print(f"{utils_for_log.time_string()}: start the stage of {stage}")
        if stage == "repos":
            generate_repos(config=config, temp_url=temp_url)
        elif stage == "samples":
            sampler = fim_sampling.CSTFimSampler(
                node_size_soft_limit=800,
                len_insert_lower=300,
                corruption_nodes_picker=fim_sampling.DefaultCorruptionNodesPicker(
                    corruption_expansion_rate=0.4
                ),
            )
            result = generate_samples(
                config=config, STAGE1_URI=temp_url, sampler=sampler
            )
            print("result", result)
            stderr = result["task_info"]["stderr"]
            print("stderr", stderr)
            stdout = result["task_info"]["stdout"]
            print("stdout", stdout[0])
            for line in stderr[0].split("\n"):
                print(line)
        else:
            raise ValueError(f"Unknown stage {stage}")
        print(f"{utils_for_log.time_string()}: finish the stage of {stage}")
