"""Scripts to generate the final dataset for training.

>> 5.07 million samples.
python experimental/dxy/rag/exps/gen-dataset.py \
    --input s3a://michiel-dev-bucket/ragdata/eth6_morelang3/ \
    --output /mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v1-5M

>> 4.36 million samples.
python experimental/dxy/rag/exps/gen-dataset.py \
    --input s3a://dxy-dev-bucket/ragdata/limit-repo-32000-normal/retrieval/ \
    --output /mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M

python experimental/dxy/rag/exps/gen-dataset.py \
    --tokenizer deepseek-coder-base \
    --input s3a://dxy-dev-bucket/ragdata/limit-repo-32000-normal/retrieval/ \
    --output /mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M-DSCB

>> Over 0.93 million unit test samples.
python experimental/dxy/rag/exps/gen-dataset.py \
    --tokenizer deepseek-coder-base \
    --input s3a://dxy-dev-bucket/ragdata/limit-repo-20000-v2/retrieval/ \
    --output /mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/unit-test-1M-DSCB
"""

import argparse
import dataclasses
import json
import pathlib
from datetime import datetime
from functools import partial

from megatron.tokenizer.tokenizer import StarCoder2Tokenizer, DeepSeekCoderBaseTokenizer

from experimental.dxy.rag.exps.utils import PROMPT_COLUMN, SAMPLES_COLUMN
from research.core import utils_for_log
from research.data.rag import rogue
from research.data.rag import utils as rag_utils
from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet


def get_config(
    input: str, output: str, tokenizer_name: str
) -> rogue.RogueSLDataPromptFormattingConfig:
    """Get the rogue prompt formatter config."""
    if tokenizer_name == "starcoder2":
        prepend_bos_token = False
    elif tokenizer_name == "deepseek-coder-base":
        prepend_bos_token = True
    else:
        raise ValueError(f"Unknown tokenizer name: {tokenizer_name}")

    config = rogue.RogueSLDataPromptFormattingConfig(
        input=input,
        output=output,
        tokenizer_name=tokenizer_name,
        num_validation_samples=50000,
        seq_len=8192 + 1,
        component_order=["prefix", "retrieval", "nearby_prefix", "suffix"],
        max_prefix_tokens=1024,
        max_suffix_tokens=512,
        max_retrieved_chunk_tokens=-1,
        max_filename_tokens=50,
        max_target_tokens=256,
        max_prompt_tokens=6142,
        random_seed=74912,
        data_augmentation_rate=0.5,
        dense_retrieval_dropout_rate=0.3,
        max_prompt_token_range=(3072, 7678),
        context_quant_token_len=64,
        # context_quant_token_len=0,
        nearby_prefix_token_len=512,
        # nearby_prefix_token_len=0,
        nearby_prefix_token_overlap=0,
        nearby_suffix_token_len=0,
        nearby_suffix_token_overlap=0,
        use_far_prefix_token=True,
        prepend_bos_token=prepend_bos_token,
    )
    return config


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate the entire dataset.")
    parser.add_argument("--input", type=str, required=True)
    parser.add_argument("--output", type=str, required=True)
    parser.add_argument(
        "--tokenizer",
        type=str,
        choices=["starcoder2", "deepseek-coder-base"],
        required=True,
    )
    args = parser.parse_args()

    config = get_config(
        input=args.input, output=args.output, tokenizer_name=args.tokenizer
    )
    print(f"{utils_for_log.time_string()}: config: {config}")

    if config.tokenizer_name == "starcoder2":
        tokenizer = StarCoder2Tokenizer()
    elif config.tokenizer_name == "deepseek-coder-base":
        tokenizer = DeepSeekCoderBaseTokenizer()
    else:
        raise ValueError(f"Unknown tokenizer name: {config.tokenizer_name}")

    now = datetime.now()
    formatted_time = now.strftime("%Y-%m-%d_%H-%M-%S")
    stage1_uri = (
        f"s3a://augment-temporary/dxy/gen_dataset/{formatted_time}/step_1_samples/"
    )
    stage2_uri = (
        f"s3a://augment-temporary/dxy/gen_dataset/{formatted_time}/step_2_packs/"
    )
    print(f"Stage 1 URI : {stage1_uri}")
    print(f"Stage 2 URI : {stage2_uri}")
    print(f"Config.output : {config.output}")

    spark = k8s_session()

    input_columns = [
        "prefix",
        "middle",
        "suffix",
        "suffix_offset",
        "middle_char_start",
        "middle_char_end",
        "file_path",
        "retrieved_chunks",
    ]
    result1 = map_parquet.apply(
        spark,
        partial(
            rogue.generate_prompt_sl,
            tokenizer=tokenizer,
            config=config,
        ),
        input_path=config.input,
        output_path=stage1_uri,
        input_columns=input_columns,
        output_column=PROMPT_COLUMN,
        ignore_error=True,
    )
    print(f"{utils_for_log.time_string()}: finish stage 1")
    print(result1["task_info"].stdout[1])

    result2 = map_parquet.apply(
        spark,
        partial(rag_utils.pad_pack_tokens, seq_len=config.seq_len, tokenizer=tokenizer),
        input_path=stage1_uri,
        output_path=stage2_uri,
        input_columns=[PROMPT_COLUMN],
        output_column=SAMPLES_COLUMN,
    )
    print(f"{utils_for_log.time_string()}: finish stage 2")

    spark.sparkContext.setJobDescription("Creating indexed dataset")
    df = spark.read.parquet(stage2_uri).select(SAMPLES_COLUMN)
    spark.sparkContext.setJobDescription("Shuffling dataset")
    df = rag_utils.repartition_and_shuffle(config.random_seed, df)
    export_indexed_dataset_helper(
        df,
        vocab_size=tokenizer.vocab_size,
        samples_column=SAMPLES_COLUMN,
        num_validation_samples=config.num_validation_samples,
        indexed_dataset_path=pathlib.Path(config.output),
    )
    with open(config.output + "config.json", "w") as f:
        json.dump(dataclasses.asdict(config), f, indent=4)
