"""Functions for Rogue data processing."""

import random
from dataclasses import dataclass
from types import SimpleNamespace
from typing import Callable, Iterable, Optional, Sequence, Tuple, Union

import pandas as pd
import tree_sitter as ts
from megatron.tokenizer.tokenizer import (
    DeepSeekCoderBaseTokenizer,
    LLama3BaseTokenizer,
    StarCoder2Tokenizer,
    StarCoderTokenizer,
)

from experimental.dxy.rag.exps.rogue_stages import (
    FIMSampleConfig,
    RogueRetrievalConfig,
    process_repo_retrieval,
    process_repo_sample_fim,
)
from research.core.model_input import ModelInput
from research.core.prompt_formatters import (
    PromptFormatterRogue,
    PromptFormatterRogueSLCache,
)
from research.data.rag.retrieval_utils import (
    deserialize_retrieved_chunks,
    serialize_retrieved_chunks,
)
from research.data.rag.rogue import RogueSampleConfig
from research.fim.fim_sampling import CSTFimSampler
from research.retrieval import utils as rutils
from research.static_analysis.parsing import TsParsedFile

PATH_COLUMN = "max_stars_repo_path"
ID_COLUMN = "hexsha"
CONTENT_COLUMN = "content"
SIZE_COLUMN = "size"
FILE_LANG_COLUMN = "langpart"


# @dataclass
# class RogueSampleConfig:
#     """Configuration for sampling the Rogue dataset."""

#     input: str
#     output: str
#     fim_version: str

#     # Sampling languages
#     repo_languages: Sequence[str]
#     sample_languages: Sequence[str]
#     retrieval_languages: Sequence[str]

#     # Sampling parameters
#     limit_repos: int
#     repo_min_size: int
#     repo_max_size: int
#     every_n_lines: int
#     max_problems_per_file: int

#     # File filtering
#     small_filter_char_threshold: Optional[int] = None
#     small_downsample_char_threshold: Optional[int] = None
#     small_downsampled_probability: Optional[float] = None
#     file_path_regex_pattern: Optional[str] = None

#     random_seed: int = 74912

#     # Retrieval
#     num_retrieved_chunks: int = 40
#     scorer_config: Optional[dict[str, Any]] = None
#     chunker_config: Optional[dict[str, Any]] = None
#     query_config: Optional[dict[str, Any]] = None
#     document_config: Optional[dict[str, Any]] = None
#     multi_retriever_configs: Optional[dict[str, Any]] = None
#     multi_retriever_weights: Optional[dict[str, int]] = None

#     def __post_init__(self):
#         if self.multi_retriever_configs is None and self.scorer_config is None:
#             raise ValueError("Must provide either scorer or multi_retriever_configs")


def overarching_config_to_fim_config(config: RogueSampleConfig) -> FIMSampleConfig:
    """Convert a RogueSampleConfig to a FIMSampleConfig."""
    return FIMSampleConfig(
        fim_version=config.fim_version,
        repo_languages=config.repo_languages,
        sample_languages=config.sample_languages,
        retrieval_languages=config.retrieval_languages,
        # File filtering
        small_filter_char_threshold=config.small_filter_char_threshold,
        small_downsample_char_threshold=config.small_downsample_char_threshold,
        small_downsampled_probability=config.small_downsampled_probability,
        file_path_regex_pattern=config.file_path_regex_pattern,
        # FIM sampling parameters
        every_n_lines=config.every_n_lines,
        max_problems_per_file=config.max_problems_per_file,
        # Post-FIM filtering
        post_fim_filter_empty_prefix=True,
        # Random seed
        random_seed=config.random_seed,
    )


def overarching_config_to_retrieval_config(
    config: RogueSampleConfig,
) -> RogueRetrievalConfig:
    """Convert a RogueSampleConfig to a RogueRetrievalConfig."""
    return RogueRetrievalConfig(
        repo_languages=config.repo_languages,
        sample_languages=config.sample_languages,
        retrieval_languages=config.retrieval_languages,
        # Retrieval
        num_retrieved_chunks=config.num_retrieved_chunks,
        scorer_config=config.scorer_config,
        chunker_config=config.chunker_config,
        query_config=config.query_config,
        document_config=config.document_config,
        multi_retriever_configs=config.multi_retriever_configs,
        multi_retriever_weights=config.multi_retriever_weights,
        # Random seed
        random_seed=config.random_seed,
    )


# This processes one partition of the dataset.
# now we know that batch sizes really isn't that much a deal.
# most of the memory is used by treesitter for its leaks


def process_partition_pandas(
    batch: pd.DataFrame,
    config: RogueSampleConfig,
    sampler: CSTFimSampler | None = None,
    get_node_weight: Callable[[ts.Node, TsParsedFile], float] | None = None,
) -> Iterable[pd.Series]:
    """Process a single partition of the dataset.

    Args:
        batch: A single partition of the dataset.
        config: The configuration object.
        sampler: The FIM sampler to use, if none, create a default one.

    Returns:
        A generator of processed rows.
    """
    # Temporary to ensure registry has access to ethanol retriever
    from research.eval.harness.factories import create_retriever  # pylint: disable=unused-import,import-outside-toplevel # noqa: F401

    if config.multi_retriever_configs is None:
        retriever_config = {
            "scorer": config.scorer_config,
            "chunker": config.chunker_config,
            "query_formatter": config.query_config,
            "document_formatter": config.document_config,
        }
    else:
        retriever_config = {
            "name": "multi_retriever",
            "retrievers": config.multi_retriever_configs,
        }
        if config.multi_retriever_weights is not None:
            retriever_config["database_weights"] = config.multi_retriever_weights

    retrieval_database = create_retriever(retriever_config)

    retrieval_database.load()

    if sampler is None:
        sampler = CSTFimSampler(rng=random.Random(config.random_seed))

    tokenizer = StarCoderTokenizer()

    for files in batch.file_list:
        fim_problems = process_repo_sample_fim(
            files,
            config=overarching_config_to_fim_config(config),
            sampler=sampler,
            get_node_weight=get_node_weight,
        )
        for sample in process_repo_retrieval(
            files,
            fim_problems,
            config=overarching_config_to_retrieval_config(config),
            retrieval_database=retrieval_database,
            tokenizer=tokenizer,
        ):
            sample["retrieved_chunks"] = serialize_retrieved_chunks(
                sample["retrieved_chunks"]
            )
            yield pd.Series(sample)


# TODO(michiel) add correctness test
def generate_prompt(
    prefix: str,
    middle: str,
    suffix: str,
    suffix_offset: int,
    middle_char_start: int,
    middle_char_end: int,
    file_path: str,
    retrieved_chunk_str: str,
    tokenizer: Union[DeepSeekCoderBaseTokenizer, StarCoderTokenizer],
    config: SimpleNamespace,
) -> list[int]:
    """Construct a token prompt."""

    seed = (
        config.random_seed
        + int.from_bytes((file_path).encode(), "little")
        + middle_char_start
    )
    random.seed(seed)

    # retrieval dropout
    if config.retrieval_dropout > 0 and random.random() < config.retrieval_dropout:
        max_retrieved_chunk_tokens = 0
    else:
        max_retrieved_chunk_tokens = config.max_retrieved_chunk_tokens

    prompt_formatter = PromptFormatterRogue(
        max_prefix_tokens=config.max_prefix_tokens,
        max_suffix_tokens=config.max_suffix_tokens,
        max_retrieved_chunk_tokens=max_retrieved_chunk_tokens,
        max_filename_tokens=config.max_filename_tokens,
        preamble=config.preamble,
        # TODO(michiel) Use seperator token and make formatter use at beginning and end
        prepend_path_to_retrieved=config.prepend_path_to_retrieved,
        prefix_after_suffix=config.prefix_after_suffix,
        add_retrieval_after_context=config.add_retrieval_after_context,
        only_truncate_true_prefix=config.only_truncate_true_prefix,
        always_use_suffix_token=config.always_use_suffix_token,
        nearby_prefix_char_len=config.nearby_prefix_char_len,
        prefix_char_offset=config.prefix_char_offset,
        prepend_bos_token=getattr(config, "prepend_bos_token", False),
    )
    # Make it so we don't load new tokenizer for every row even though we build prompt formatter
    prompt_formatter.tokenizer = tokenizer
    prompt_formatter.max_prompt_tokens = config.max_prompt_tokens

    # Randomly sample prompt formatter settings

    # 50% of the time use standard settings

    # Else
    # 20%, use empty suffix
    # 20%, use no retrievals (independent of empty suffix)

    # 25%, sample max prompt size uniformly over [500, max]
    # 25%, sample size of prefix from [1, max prompt size]
    # 25%, sample size of suffix from [1, max prompt size]

    retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunk_str)

    # Remove chunks that overlap with middle
    filtered_chunks = rutils.filter_overlap_chunks(
        file_path,
        rutils.Span(middle_char_start, middle_char_end),
        retrieved_chunks,
    )

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=filtered_chunks,
        path=file_path,
    )

    # TODO(michiel) Add option for sampling different prompt styles
    _, metadata = prompt_formatter.prepare_prompt(model_input)
    # Remove chunks that overlap with prefix or suffix
    new_filtered_chunks = rutils.filter_overlap_chunks(
        file_path,
        rutils.Span(
            middle_char_start - metadata["num_prefix_chars_post_truncation"],
            middle_char_end,
        ),
        filtered_chunks,
    )
    if metadata["num_suffix_chars_post_truncation"] > 0:
        new_filtered_chunks = rutils.filter_overlap_chunks(
            file_path,
            rutils.Span(
                middle_char_end,
                middle_char_end
                + max(metadata["num_suffix_chars_post_truncation"] - suffix_offset, 0),
            ),
            new_filtered_chunks,
        )

    model_input.retrieved_chunks = new_filtered_chunks
    prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)

    # In the data generation pipeline, the 'middle' section is preprocessed separately.
    # To save time, instead of redoing the entire step with a custom tokenizer,
    # we allow re-using data processed by the StarCoder tokenizer.
    # In this case, we only need to replace the Starcoder special tokens.
    middle = middle.replace("<|pause|>", prompt_formatter.tokenizer.pause_token)
    middle = middle.replace("<|skip|>", prompt_formatter.tokenizer.skip_token)
    middle = middle.replace("<|endoftext|>", prompt_formatter.tokenizer.eod_token)

    target_tokens = prompt_formatter.tokenizer.tokenize(middle)

    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens += target_tokens

    return prompt_tokens


@dataclass
class RogueSLDataPromptFormattingConfig:
    input: str | Sequence[str]
    output: str

    seq_len: int
    num_validation_samples: int
    tokenizer_name: str

    component_order: Sequence[str]
    max_prefix_tokens: int
    max_suffix_tokens: int
    max_retrieved_chunk_tokens: int
    max_filename_tokens: int
    max_prompt_tokens: int
    max_target_tokens: int
    prepend_bos_token: bool

    # Data augmentation
    random_seed: int = 74912
    data_augmentation_rate: float = 0.0
    dense_retrieval_dropout_rate: float = 0.0
    max_prompt_token_range: Optional[Tuple[int, int]] = None

    # Stateless caching
    context_quant_token_len: int = 0
    nearby_prefix_token_len: int = 0
    nearby_prefix_token_overlap: int = 0
    nearby_suffix_token_len: int = 0
    nearby_suffix_token_overlap: int = 0
    use_far_prefix_token: bool = False
    use_far_suffix_token: bool = False

    # Misc
    max_scope_path_tokens: int = 0


def generate_prompt_sl(
    prefix: str,
    middle: str,
    suffix: str,
    suffix_offset: int,
    middle_char_start: int,
    middle_char_end: int,
    file_path: str,
    retrieved_chunk_str: str,
    tokenizer: Union[
        DeepSeekCoderBaseTokenizer,
        StarCoderTokenizer,
        StarCoder2Tokenizer,
        LLama3BaseTokenizer,
    ],
    config: RogueSLDataPromptFormattingConfig,
) -> list[int]:
    """Construct a token prompt."""

    seed = (
        config.random_seed
        + int.from_bytes((file_path).encode(), "little")
        + middle_char_start
    )
    random.seed(seed)

    max_retrieved_chunk_tokens = config.max_retrieved_chunk_tokens
    max_prompt_tokens = config.max_prompt_tokens

    # Sample whether to perform data augmentation at all
    if random.random() < config.data_augmentation_rate:
        # Randomly set dense retrieval tokens to 0 so model can operate without.
        if random.random() < config.dense_retrieval_dropout_rate:
            max_retrieved_chunk_tokens = 0

        if config.max_prompt_token_range is not None:
            max_prompt_tokens = random.randint(*config.max_prompt_token_range)

    prompt_formatter = PromptFormatterRogueSLCache(
        max_prefix_tokens=config.max_prefix_tokens,
        max_suffix_tokens=config.max_suffix_tokens,
        max_retrieved_chunk_tokens=max_retrieved_chunk_tokens,
        max_filename_tokens=config.max_filename_tokens,
        max_scope_path_tokens=config.max_scope_path_tokens,
        component_order=config.component_order,
        context_quant_token_len=config.context_quant_token_len,
        nearby_prefix_token_len=config.nearby_prefix_token_len,
        nearby_prefix_token_overlap=config.nearby_prefix_token_overlap,
        nearby_suffix_token_len=config.nearby_suffix_token_len,
        nearby_suffix_token_overlap=config.nearby_suffix_token_overlap,
        use_far_prefix_token=config.use_far_prefix_token,
        use_far_suffix_token=config.use_far_suffix_token,
        prepend_bos_token=getattr(config, "prepend_bos_token", False),
    )
    # Make it so we don't load new tokenizer for every row even though we build prompt formatter
    prompt_formatter.tokenizer = tokenizer
    prompt_formatter.max_prompt_tokens = max_prompt_tokens

    # Randomly sample prompt formatter settings
    retrieved_chunks = deserialize_retrieved_chunks(retrieved_chunk_str)

    # Remove chunks that overlap with middle
    filtered_chunks = rutils.filter_overlap_chunks(
        file_path,
        rutils.Span(middle_char_start, middle_char_end),
        retrieved_chunks,
    )

    model_input = ModelInput(
        prefix=prefix,
        suffix=suffix,
        retrieved_chunks=filtered_chunks,
        path=file_path,
    )

    # TODO(michiel) Add option for sampling different prompt styles
    _, metadata = prompt_formatter.prepare_prompt(model_input)
    # Remove chunks that overlap with prefix or suffix
    new_filtered_chunks = rutils.filter_overlap_chunks(
        file_path,
        rutils.Span(
            middle_char_start - metadata["num_prefix_chars_post_truncation"],
            middle_char_end,
        ),
        filtered_chunks,
    )
    if metadata["num_suffix_chars_post_truncation"] > 0:
        new_filtered_chunks = rutils.filter_overlap_chunks(
            file_path,
            rutils.Span(
                middle_char_end,
                middle_char_end
                + max(metadata["num_suffix_chars_post_truncation"] - suffix_offset, 0),
            ),
            new_filtered_chunks,
        )

    model_input.retrieved_chunks = new_filtered_chunks
    prompt_tokens, _ = prompt_formatter.prepare_prompt(model_input)

    # In the data generation pipeline, the 'middle' section is preprocessed separately.
    # To save time, instead of redoing the entire step with a custom tokenizer,
    # we allow re-using data processed by the StarCoder tokenizer.
    # In this case, we only need to replace the Starcoder special tokens.
    middle = middle.replace("<|pause|>", prompt_formatter.tokenizer.pause_token)
    middle = middle.replace("<|skip|>", prompt_formatter.tokenizer.skip_token)
    middle = middle.replace("<|endoftext|>", prompt_formatter.tokenizer.eod_token)

    target_tokens = prompt_formatter.tokenizer.tokenize(middle)

    target_tokens = target_tokens[: config.max_target_tokens]
    prompt_tokens += target_tokens

    return prompt_tokens
