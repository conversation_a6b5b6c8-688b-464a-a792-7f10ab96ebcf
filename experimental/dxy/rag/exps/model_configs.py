"""All model configurations for <PERSON>."""

config_rogue_sl_4k = {
    "name": "rogue2_statelesscache",
    "checkpoint_path": None,
    "seq_length": 4096,
    "prompt": {
        "max_prefix_tokens": 1030,
        "max_suffix_tokens": 768,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 3838,
        "max_filename_tokens": 50,
        "component_order": [
            "prefix",
            "suffix",
            "retrieval",
            "nearby_prefix",
        ],
        "context_quant_token_len": 50,
        "nearby_prefix_token_len": 250,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
        "prepend_bos_token": False,
    },
}

config_rogue_sl_8k = {
    "name": "rogue2_statelesscache",
    "checkpoint_path": None,
    "seq_length": 6142 + 512 + 2,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6142,
        "max_filename_tokens": 50,
        "component_order": ["prefix", "retrieval", "nearby_prefix", "suffix"],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
        "use_far_prefix_token": True,
        "prepend_bos_token": False,
    },
}

config_elden_6k = {
    "name": "elden_fb",
    "checkpoint_path": None,
    "model_parallel_size": 2,
    "seq_length": 6142 + 512 + 2,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 1024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

config_michiel_elden_6k = {
    "name": "elden_fb",
    # "checkpoint_path": "star2/elden_0619_90k_0619_sc2_pfretsignpfsuf_rdrop030_1k5b6kstep",
    "checkpoint_path": None,
    "model_parallel_size": 2,
    "seq_length": 6600,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 1024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

config_elden_6k_ffw = {
    "name": "starcoder2_fastforward",
    "override_prompt_formatter": {
        "name": "ender",
    },
    "tokenizer_name": "starcoder2tokenizer",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 6142 + 512 + 2,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 1024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

config_research_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "override_prompt_formatter": {
        "name": "ender",
    },
    "override_tokenizer": "starcoder2",
    # "model_parallel_size": 2,
    "seq_length": 6142 + 512 + 2,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 1024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

config_elden_dsv2_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "override_prompt_formatter": {
        "name": "ender",
    },
    "override_tokenizer": "deepseek_coder_v2",
    "model_parallel_size": 1,
    "seq_length": 6142 + 512 + 2,
    "prompt": {
        "max_prefix_tokens": 1024,
        "max_suffix_tokens": 512,
        "max_signature_tokens": 1024,
        "max_retrieved_chunk_tokens": -1,
        "max_prompt_tokens": 6144,
        "component_order": [
            "prefix",
            "retrieval",
            "signature",
            "nearby_prefix",
            "suffix",
        ],
        "context_quant_token_len": 64,
        "nearby_prefix_token_len": 512,
        "nearby_prefix_token_overlap": 0,
        "nearby_suffix_token_len": 0,
        "nearby_suffix_token_overlap": 0,
    },
}

config_sc2_simple_elden_6k_ffw = {
    "name": "starcoder2_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 6048 + 256,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 6048,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 6048,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 6048 - 1024,
            },
        },
    },
}

config_sc2_v2_simple_elden_8k_ffw = {
    "name": "starcoder2_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 7936,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 7680,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 7680,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 7680 - 1024,
            },
            "version": "v2.0",
        },
    },
}

config_sc2_v2_simple_elden_6k_ffw = {
    "name": "starcoder2_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 6048 + 256,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 6048,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 6048,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 6048 - 1024,
            },
            "version": "v2.0",
        },
    },
}

config_sc2_v2_simple_elden_6k_ffwfp8 = {
    "name": "starcoder2_fp8_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 6044 + 256,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 6044,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 6044,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 6044 - 1024,
            },
            "version": "v2.0",
        },
    },
}

config_sc2_v2_simple_elden_8k_ffwfp8 = {
    "name": "starcoder2_fp8_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 7936,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 7680,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 7680,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 7680 - 1024,
            },
            "version": "v2.0",
        },
    },
}

config_sc2_simple_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "seq_length": 7936,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 7680,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 7680,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 7680 - 1024,
            },
        },
    },
}

config_sc2_v2_simple_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "seq_length": 7936,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 7680,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 7680,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 7680 - 1024,
            },
            "version": "v2.0",
        },
    },
}

config_dscv2_simple_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "seq_length": 7936,
    "tokenizer": "deepseek_coder_v2",
    "formatter_config": {
        "name": "simple_elden",
        "prompt_formatter_config": {
            "max_prompt_length": 7680,
            "token_config": {
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 7680,
            },
            "per_retriever_max_tokens": {
                "signature_retriever": 1024,
                "recency_retriever": 1024,
                "dense_retriever": 7680 - 1024,
            },
        },
    },
}

config_dscv2_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "seq_length": 6144 + 256 + 1,
    "tokenizer": "deepseek_coder_v2",
    "max_generated_tokens": 256,
    "formatter_config": {
        "name": "ender",
        "apportionment_config": {
            "max_content_len": 6144 + 256 + 1,
            "input_fraction": 4 / 12,
            "prefix_fraction": 3 / 4,
            "max_path_tokens": 50,
            "per_retriever_max_tokens": {
                "dense_signature": 1024,
                "recency_retriever": 1024,
            },
        },
        "prompt_formatter_config": {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
                "quantize_token_len": 64,
                "quantize_char_len": 250,
            },
            "component_order": [
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            "signature_chunk_origin": "dense_signature",
        },
    },
}

config_sc2_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "seq_length": 6144 + 256 + 1,
    "tokenizer": "starcoder2",
    "max_generated_tokens": 256,
    "formatter_config": {
        "name": "ender",
        "apportionment_config": {
            "max_content_len": 6144 + 256 + 1,
            "input_fraction": 4 / 12,
            "prefix_fraction": 3 / 4,
            "max_path_tokens": 50,
            "per_retriever_max_tokens": {
                "dense_signature": 1024,
                "recency_retriever": 1024,
            },
        },
        "prompt_formatter_config": {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
                "quantize_token_len": 64,
                "quantize_char_len": 250,
            },
            "component_order": [
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            "signature_chunk_origin": "dense_signature",
        },
    },
}

config_sc2_elden_4k_ffw = {
    "name": "starcoder2_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 4096 + 256,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "ender",
        "apportionment_config": None,
        "prompt_formatter_config": {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
                "quantize_token_len": 64,
                "quantize_char_len": 250,
            },
            "component_order": [
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            "signature_chunk_origin": "signature_retriever",
            "filter_visible_chunks_by_content": True,
            "token_budget": {
                "max_prompt_length": 4096,
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 4096,
                "per_retriever_max_tokens": {
                    "signature_retriever": 1024,
                    "recency_retriever": 1024,
                },
            },
        },
    },
}

config_sc2_elden_6k_ffw = {
    "name": "starcoder2_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 6048 + 256,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "ender",
        "apportionment_config": None,
        "prompt_formatter_config": {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
                "quantize_token_len": 64,
                "quantize_char_len": 250,
            },
            "component_order": [
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            "signature_chunk_origin": "signature_retriever",
            "filter_visible_chunks_by_content": True,
            "token_budget": {
                "max_prompt_length": 6048,
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 6048,
                "per_retriever_max_tokens": {
                    "signature_retriever": 1024,
                    "recency_retriever": 1024,
                },
            },
        },
    },
}

config_sc2_elden_6k_ffwfp8 = {
    "name": "starcoder2_fp8_fastforward",
    "model_path": None,
    # "model_parallel_size": 2,
    "seq_length": 6048 + 256,
    "tokenizer": "starcoder2",
    "formatter_config": {
        "name": "ender",
        "apportionment_config": None,
        "prompt_formatter_config": {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
                "quantize_token_len": 64,
                "quantize_char_len": 250,
            },
            "component_order": [
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            "signature_chunk_origin": "signature_retriever",
            "filter_visible_chunks_by_content": True,
            "token_budget": {
                "max_prompt_length": 6048,
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 6048,
                "per_retriever_max_tokens": {
                    "signature_retriever": 1024,
                    "recency_retriever": 1024,
                },
            },
        },
    },
}


# config_qwen_14b_elden_6k_fbw = {
#     "name": "elden_qwen_fb",
#     "checkpoint_path": "qwencompletion/14b_elden_smart_fb",
#     "model_parallel_size": 2,
#     "seq_length": 6600,
#     "prompt": {
#         "max_prefix_tokens": 1024,
#         "max_suffix_tokens": 512,
#         "max_signature_tokens": 1024,
#         "max_retrieved_chunk_tokens": -1,
#         "max_prompt_tokens": 6144,
#         "component_order": [
#             "prefix",
#             "retrieval",
#             "signature",
#             "nearby_prefix",
#             "suffix",
#         ],
#         "context_quant_token_len": 64,
#         "nearby_prefix_token_len": 512,
#         "nearby_prefix_token_overlap": 0,
#         "nearby_suffix_token_len": 0,
#         "nearby_suffix_token_overlap": 0,
#     },
# }

config_qwen_elden_6k_fbw = {
    "name": "fastbackward",
    "checkpoint_path": None,
    "seq_length": 6144 + 256 + 1,
    "tokenizer": "qwen25coder",
    "max_generated_tokens": 256,
    "formatter_config": {
        "name": "ender",
        "apportionment_config": {
            "max_content_len": 6144 + 256 + 1,
            "input_fraction": 4 / 12,
            "prefix_fraction": 3 / 4,
            "max_path_tokens": 50,
            "per_retriever_max_tokens": {
                "dense_signature": 1024,
                "recency_retriever": 1024,
            },
        },
        "prompt_formatter_config": {
            "stateless_caching_config": {
                "nearby_prefix_token_len": 512,
                "quantize_token_len": 64,
                "quantize_char_len": 250,
            },
            "component_order": [
                "path",
                "prefix",
                "retrieval",
                "signature",
                "nearby_prefix",
                "suffix",
            ],
            "signature_chunk_origin": "dense_signature",
            "filter_visible_chunks_by_content": True,
            "token_budget": {
                "max_prompt_length": 4096,
                "path_len": 50,
                "prefix_len": 1024,
                "suffix_len": 512,
                "retrieval_len": 4096,
                "per_retriever_max_tokens": {
                    "signature_retriever": 1024,
                    "recency_retriever": 1024,
                },
            },
        },
    },
}

model_config_dict = {
    # "roguesl_4k": config_rogue_sl_4k,
    # "roguesl_8k": config_rogue_sl_8k,
    # "elden_6k": config_elden_6k,
    # "michiel_elden_6k": config_michiel_elden_6k,
    # "elden_6k_ffw": config_elden_6k_ffw,
    # "elden_dsv2_fbw": config_elden_dsv2_fbw,
    # "research_elden_6k_fbw": config_research_elden_6k_fbw,
    # "sc2_elden_6k_fbw": config_sc2_elden_6k_fbw,
    "sc2_elden_6k_ffw": config_sc2_elden_6k_ffw,
    "sc2_elden_6k_ffwfp8": config_sc2_elden_6k_ffwfp8,
    "sc2_elden_4k_ffw": config_sc2_elden_4k_ffw,
    "sc2_simple_elden_6k_ffw": config_sc2_simple_elden_6k_ffw,
    "sc2_simple_elden_6k_fbw": config_sc2_simple_elden_6k_fbw,
    "sc2_simple_elden_v2_6k_fbw": config_sc2_v2_simple_elden_6k_fbw,
    "sc2_simple_elden_v2_6k_ffw": config_sc2_v2_simple_elden_6k_ffw,
    "sc2_simple_elden_v2_8k_ffw": config_sc2_v2_simple_elden_8k_ffw,
    "sc2_simple_elden_v2_6k_ffwfp8": config_sc2_v2_simple_elden_6k_ffwfp8,
    "sc2_simple_elden_v2_8k_ffwfp8": config_sc2_v2_simple_elden_8k_ffwfp8,
    "dscv2_simple_elden_6k_fbw": config_dscv2_simple_elden_6k_fbw,
    "dscv2_elden_6k_fbw": config_dscv2_elden_6k_fbw,
    "qwen_elden_6k_fbw": config_qwen_elden_6k_fbw,
}
