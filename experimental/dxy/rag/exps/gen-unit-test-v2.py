"""Notebook to generate Rogue samples.

# In total, there 6483605 repos in s3a://the-stack-processed/by-repo-3
# After filtering out repos that does not have any unit test files, we get 1684642 / 6483605 repos.
# After filtering out repo size not in [500000, 100000000], there are only 138185 repos. A lot of small repos!

# 100 repos -> 5195 FIM samples.

python experimental/dxy/rag/exps/gen-unit-test-v2.py --stage repos fim --limit_repos 100
python experimental/dxy/rag/exps/gen-unit-test-v2.py --stage retrieval --limit_repos 100
Expect to be located at:
- s3://dxy-dev-bucket/ragdata/limit-repo-100-v2/fims/
- s3://dxy-dev-bucket/ragdata/limit-repo-100-v2/retrieval/

# 20K repos -> 986670 retrieved-augmented FIM samples.

python experimental/dxy/rag/exps/gen-unit-test-v2.py --stage repos fim retrieval --limit_repos 20000
Expect to be located at:
- s3://dxy-dev-bucket/ragdata/limit-repo-20000-v2/retrieval/
"""

import argparse
import logging
from datetime import datetime

from pyspark.sql import functions as sparkF

from experimental.dxy.rag.exps import utils_rag
from research.core import utils_for_log
from research.data.rag import rogue_stages
from research.data.spark import k8s_session
from research.data.spark.utils import AugmentK8sSparkSession
from research.fim import fim_sampling
from research.fim.fim_sampling_for_tests import (
    DefaultUnitTestCorruptionNodesPicker,
    LiteralUnitTestCorruptionNodesPicker,
)
from research.utils.generate_fim_data import FimDataProcessor

# These languages names are for the stack
REPO_LANGUAGES = [
    "c",
    "c++",
    "go",
    "java",
    "javascript",
    "python",
    "rust",
    "typescript",
    "c-sharp",
    "ruby",
    "php",
    "tsx",
    "jsx",
    "css",
    "shell",
    "scala",
    "ruby",
    "lua",
    "kotlin",
]

SAMPLE_LANGUAGES = REPO_LANGUAGES

additional_retrieval_languages = [
    "cuda",
    "svelte",
    "protocol-buffer",
    "dart",
    "html",
    "makefile",
    "dockerfile",
    "text",
    "yaml",
    "json",
    "xml",
    "jsonnet",
]
RETRIEVAL_LANGUAGES = (
    REPO_LANGUAGES + ["sql", "markdown"] + additional_retrieval_languages
)


def generate_fims(
    input_url: str, output_url: str, config: rogue_stages.FIMSampleConfig
):
    """Generate the FIM samples."""
    node_picker = DefaultUnitTestCorruptionNodesPicker(
        no_corruption_expansion_rate=0.2,
        random_corrupt_available_siblings_rate=0.4,
        corrupt_all_available_siblings_rate=0.2,
        possibly_corrupt_ancestor_rate=0.3,
    )
    sampler = fim_sampling.CSTFimSampler(
        pick_whole_node_rate=1.0,
        pick_extra_spaces_when_whole_node=0.0,
        empty_completion_rate=0.01,
        corruption_nodes_picker=node_picker,
    )

    node_picker_literal = LiteralUnitTestCorruptionNodesPicker(
        no_corruption_expansion_rate=0.2,
        random_corrupt_available_siblings_rate=0.5,
        corrupt_all_available_siblings_rate=0.2,
        possibly_corrupt_ancestor_rate=0.0,
        edit_similarity_threshold=0.3,
        max_num_lines_per_node=10,
        max_num_char_per_node=400,
    )
    sampler_literal = fim_sampling.CSTFimSampler(
        pick_whole_node_rate=1.0,
        pick_extra_spaces_when_whole_node=0.0,
        empty_completion_rate=0.01,
        corruption_nodes_picker=node_picker_literal,
    )
    spark_conf = {
        "spark.executor.pyspark.memory": "64G",
        "spark.executor.memory": "32G",
        "spark.sql.parquet.columnarReaderBatchSize": "64",
        "spark.task.cpus": "2",
    }

    spark: AugmentK8sSparkSession = k8s_session(
        max_workers=512,
        conf=spark_conf,
    )
    utils_rag.repo_to_fim_problems_wrapper(
        input_url,
        output_url,
        spark,
        config,
        sampler_get_node_weight=[
            (sampler, node_picker.get_node_weight),
            (sampler_literal, node_picker_literal.get_node_weight),
        ],
    )
    # sum up the num_fim_problems field
    df = spark.read.parquet(output_url)
    df.agg(sparkF.sum("num_fim_problems")).show()
    spark.stop()
    print(
        f"{utils_for_log.time_string()}: finish generating FIM samples into {urls['fim_url']}"
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Generate samples.")
    parser.add_argument(
        "--stage",
        type=str,
        choices=["repos", "fim", "retrieval"],
        nargs="*",
        required=True,
    )
    parser.add_argument(
        "--limit_repos",
        type=int,
        help="The number of repos to process",
        required=True,
    )
    args = parser.parse_args()
    # Set the default logging level to INFO
    logging.basicConfig(level=logging.INFO)

    formatted_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    config_fim = rogue_stages.FIMSampleConfig(
        fim_version=FimDataProcessor.VERSION,
        repo_languages=REPO_LANGUAGES,
        sample_languages=SAMPLE_LANGUAGES,
        retrieval_languages=RETRIEVAL_LANGUAGES,
        only_keep_unit_test_file=True,
        every_n_lines=50,
        max_problems_per_file=5,
        small_downsampled_probability=0.1,
        small_downsample_char_threshold=1500,
        small_filter_char_threshold=50,
        random_seed=104,
    )
    config_retrieval = rogue_stages.RogueRetrievalConfig(
        repo_languages=REPO_LANGUAGES,
        sample_languages=SAMPLE_LANGUAGES,
        retrieval_languages=RETRIEVAL_LANGUAGES,
        num_retrieved_chunks=40,
        scorer_config={
            "name": "ethanol",
            "checkpoint_path": "ethanol/ethanol6-04.1",
        },
        chunker_config={
            "name": "line_level",
            "max_lines_per_chunk": 30,
            "include_scope_annotation": False,
        },
        query_config={
            "name": "ethanol6_query",
            "max_tokens": 1023,
            "add_path": True,
        },
        document_config={
            "name": "ethanol6_document",
            "max_tokens": 999,
            "add_path": True,
        },
        random_seed=74912,
    )
    urls = {
        "raw_stack": "s3a://the-stack-processed/by-repo-3",
        "repo_url": f"s3a://augment-temporary/dxy/generate-unit-test/{formatted_time}/repos/",
        "fim_url": f"s3a://dxy-dev-bucket/ragdata/limit-repo-{args.limit_repos}-unittest/fims/",
        "retrieval_url": f"s3a://dxy-dev-bucket/ragdata/limit-repo-{args.limit_repos}-unittest/retrieval/",
        "dataset_url": f"s3a://dxy-dev-bucket/ragdata/limit-repo-{args.limit_repos}-unittest/dataset/",
    }
    # Iterate over stages
    for stage in args.stage:
        print(f"{utils_for_log.time_string()}: start the stage of {stage}")
        if stage == "repos":
            top_languages, final_top_languages, _ = utils_rag.generate_repos(
                input_url=urls["raw_stack"],
                output_url=urls["repo_url"],
                config=config_fim,
                repo_min_size=500000,
                repo_max_size=100000000,
                limit_repos=args.limit_repos,
            )
            print(f"Original top languages: {top_languages}")
            print(f"Final top languages: {final_top_languages}")
        elif stage == "fim":
            generate_fims(
                input_url=urls["repo_url"],
                output_url=urls["fim_url"],
                config=config_fim,
            )
        elif stage == "retrieval":
            utils_rag.generate_retrieval_augmented_samples(
                input_url=urls["fim_url"],
                output_url=urls["retrieval_url"],
                config=config_retrieval,
            )
        else:
            raise ValueError(f"Unknown stage {stage}")
        print(f"{utils_for_log.time_string()}: finish the stage of {stage}")
