#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-v2/sc2-15b-8k-base+test-dynamicbs2k-s2k.yml
#
# Global batch size should be 64 * 2 * 32 / 2 = 2048 to speed up experiments.
#
# Take about 48 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/74968
#
# bash research/utils/download_checkpoint.sh 1a183e39-69d2-423d-a49e-1b450557fc18 dxy/sc2-rogue/15B-8K-base+test-bs2ks2k
# python research/tools/ckp_converter/starcoder2_fbw_to_ffw.py --inp-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-rogue/15B-8K-base+test-bs2ks2k" --out-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-rogue/15B-8K-base+test-bs2ks2k-ffw"
#
determined:
  description: null
  workspace: Dev
  project: <PERSON>anyi-<PERSON>
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 0
  gradient_accumulation_steps_factory: StepWiseInt([(200, 16), (2000, 32)])
  batch_size: 2
  max_iters: 1000
  warmup_iters: 100
  lr_decay_iters: 1000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/dataset;/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/unit-test-1M/dataset
  eval_data_path: normal@/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/validation_dataset;unittest@/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/unit-test-1M/validation_dataset
  model_vocab_size: 49408
  checkpoint_optimizer_state: False
  use_sequence_parallel: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: sc2-15b-8k-base+test-dynamicbs2k-s2k
  wandb_project: dxy-rogue
