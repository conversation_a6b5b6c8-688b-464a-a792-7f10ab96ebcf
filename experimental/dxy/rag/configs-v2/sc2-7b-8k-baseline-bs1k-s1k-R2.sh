#!/bin/bash
# bash ../../experimental/dxy/rag/configs-v2/sc2-7b-8k-baseline-bs1k-s1k-R2.sh
# Example Usage:

torchrun --nproc_per_node=8 train.py \
    configs/starcoder2_7b.py \
    --out_dir=$HOME/fastbackward_outputs \
    --loss_mask_policy=fim \
    --fim_middle_token_id=2 \
    --eot_token_id=0 \
    --pad_token_id=49152 \
    --gradient_accumulation_steps=0 \
    --gradient_accumulation_steps_factory="ConstantInt(16)" \
    --batch_size=2 \
    --max_iters=1000 \
    --warmup_iters=100 \
    --lr_decay_iters=1000 \
    --block_size=7936 \
    --min_lr=1.0e-6 \
    --learning_rate=1.0e-5 \
    --decay_lr=True \
    --log_interval=5 \
    --eval_log_interval=500 \
    --eval_interval=200 \
    --checkpoint=/mnt/efs/augment/checkpoints/starcoder2-7b-fb \
    --train_data_path=/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/dataset \
    --eval_data_path="normal@/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/validation_dataset;unittest@/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/unit-test-1M/validation_dataset" \
    --model_vocab_size=49408 \
    --checkpoint_optimizer_state=False \
    --tokenizer_name=StarCoder2Tokenizer \
    --use_research_tokenizer=True \
    --visualize_logits_samples=8 \
    --run_name=test \
    --wandb_project=dxy-rogue
