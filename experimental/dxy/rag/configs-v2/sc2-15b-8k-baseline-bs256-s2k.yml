#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-v2/sc2-15b-8k-baseline-bs256-s2k.yml
#
# Global batch size should be 64 * 2 * 4 / 2 = 256 to speed up experiments.
#
# Take about 48 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/76730
# bash research/utils/download_checkpoint.sh 0b3e2700-72a5-4cf5-a953-7fa434f9a647 dxy/sc2-rogue/15B-8K-baseline-bs256-s2k
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 2
  max_iters: 2000
  warmup_iters: 100
  lr_decay_iters: 2000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  train_data_path: /mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/dataset
  eval_data_path: normal@/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/basic-v2-5M/validation_dataset;unittest@/mnt/efs/augment/data/processed/rag/dataset/dxy/2024JUN-V2/unit-test-1M/validation_dataset
  model_vocab_size: 49408
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: sc2-15b-8k-baseline-bs256-s2k
  wandb_project: dxy-rogue
