#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-sep/sc2-sim-bs2048-s16k-lr2e6to1e7.yml
#
# Global batch size should be 128 * 8 * 4 / 2 = 2048 to speed up experiments.
# Global batch size should be 256 * 4 * 4 / 2 = 2048 to speed up experiments.
#
# Take about 180 hours to complete on 128 GPUs.
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84657
#
#
# bash research/utils/download_checkpoint.sh ae8b3cdc-2be0-420d-b38a-a3834dbf0655 dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp2
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M3600 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M3600-mp1 \
#     --mp 1
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M3600-mp1 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M3600-ffw \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --max-seq-len 7936 \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --model-size "starcoder2-15b" \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw-fp8" \
#     -d /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden-sc2/dataset

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 8
  max_iters: 16000
  warmup_iters: 0
  lr_decay_iters: 16000
  block_size: 7936
  min_lr: 1.0e-7
  learning_rate: 2.0e-6
  decay_lr: True
  log_interval: 1
  eval_log_interval: 300
  eval_interval: 300
  # This is a super great checkpoint
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-BS2048S10K-M9000-mp2
  model_parallel_size: 2
  data_sampler_rand_seed: 9408
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden-sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-tutor-hindsight/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0814/mix-dataset-simple_elden-sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: True
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False
  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: SC2-15B-XSIME-BS2048-S16K-LR2e6to1e7-R2
  wandb_project: dxy-rogue
