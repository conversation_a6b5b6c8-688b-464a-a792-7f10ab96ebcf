#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-sep/sc2-simv2s12k-bs1024-s16k-lr2e6to1e7.yml
#
# Global batch size should be 128 * 4 * 4 / 2 = 1024 to speed up experiments.
#
# Take about 180 hours to complete on 128 GPUs.
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84657
#
#
# bash research/utils/download_checkpoint.sh dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp2
#
# python research/tools/ckp_converter/fbw2fbw_mp.py \
#     --i /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp2 \
#     --o /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp1 \
#     --mp 1
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp1 \
#     --out-ckpt /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --calibration-steps 400 \
#     --log-to-stdout \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-ffw-fp8-v0" \
#     -s a2254fc7be41bc966435e4533d9b3520a982742b10d8c3ae052274ab67c5028f \
#     -d /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0913_v2 /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0913_v2

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 4
  max_iters: 16000
  warmup_iters: 0
  lr_decay_iters: 16000
  block_size: 12288
  min_lr: 1.0e-7
  learning_rate: 2.0e-6
  decay_lr: True
  log_interval: 1
  eval_log_interval: 400
  eval_interval: 400
  # This is a super great checkpoint from https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/84837/overview
  checkpoint: /mnt/efs/augment/checkpoints/dxy/sc2-simple-elden/SC2-15B-SIM-R2-BS2048S16K-M7200-mp2
  model_parallel_size: 2
  data_sampler_rand_seed: 9408  # Random Seed
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden_v2.0_12k-sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/sft-tutor-hindsight/0913-simple_elden_v2_s12k
  eval_data_path: 0814@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/mix-dataset-simple_elden_v2.0_12k-sc2/validation_dataset;cceval@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/cceval_0913_v2_s12k;hs06@/mnt/efs/spark-data/user/dxy/elden/0814_120k_0906/online_eval/hindsight06_0913_v2_s12k
  model_vocab_size: 49176
  checkpoint_optimizer_state: True
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: False
  tokenizer_name: starcoder2
  use_research_tokenizer: False
  visualize_logits_samples: 4

  run_name: SC2-15B-SIMV2S12K-BS1024-S16K-LR2e6to1e7
  wandb_project: dxy-rag-v2
