#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-o-v2/dsv2s-bs2k-s9k-lr1e5to1e6.yml
#
# Global batch size should be 128 * 8 * 4 / 2 = 2048 to speed up experiments.
#
# Take about xx hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/81587/logs
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/81752
# bash research/utils/download_checkpoint.sh
#
# Convert from FBW to FFW

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/deepseek_coder_v2_lite_smoe.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 100004
  eot_token_id: 100001
  pad_token_id: 100001
  gradient_accumulation_steps: 4
  batch_size: 8
  max_iters: 9000
  warmup_iters: 200
  lr_decay_iters: 9000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Base-FBW-sparse-mp2
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/dscv2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/dscv2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/dscv2/dataset
  eval_data_path: normal@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/dscv2/validation_dataset;utdefault@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/dscv2/validation_dataset;utliteral@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/dscv2/validation_dataset
  model_vocab_size: 102400
  checkpoint_optimizer_state: False

  tokenizer_name: deepseek_coder_v2
  use_research_tokenizer: False
  visualize_logits_samples: 8

  run_name: DSCV2LiteBaseSMoE-MIX-BS2K-S9K-LR1e5to1e6
  wandb_project: dxy-rogue
