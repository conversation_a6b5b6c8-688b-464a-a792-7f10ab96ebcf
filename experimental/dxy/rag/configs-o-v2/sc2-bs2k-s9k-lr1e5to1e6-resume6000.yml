#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-o-v2/sc2-bs2k-s9k-lr1e5to1e6-resume6000.yml
#
# Global batch size should be 128 * 8 * 4 / 2 = 2048 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/81290
# bash research/utils/download_checkpoint.sh a7b6b3ff-0341-4a4f-857d-481925e111d2 dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-MID8200
# bash research/utils/download_checkpoint.sh d0633ec8-c011-449c-b37e-7924570c5d91 dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-FINAL
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-FINAL" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-ffw" \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --calibration-steps 400 \
#     --log-to-stdout \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-ffw" \
#     --ckpt-sha256 "e693998bb0fcb76f23af809d03f47d361c33263e7fa586fddbccf99ac1c75966" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 8
  max_iters: 9000
  warmup_iters: 200
  lr_decay_iters: 9000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset
  eval_data_path: normal@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/validation_dataset;utdefault@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/validation_dataset;utliteral@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False
  checkpoint: 23781ddd-906c-46d4-ab89-a38664187917  # latest checkpoint id from https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/81141/checkpoints
  restore_optimizer_state_from_checkpoint: False
  restore_training_metadata_from_checkpoint: True

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-resume6000
  wandb_project: dxy-rogue
