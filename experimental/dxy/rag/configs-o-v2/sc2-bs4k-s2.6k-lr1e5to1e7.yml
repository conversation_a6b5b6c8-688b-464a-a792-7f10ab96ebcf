#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-o-v2/sc2-bs4k-s2.6k-lr1e5to1e7.yml
#
# Global batch size should be 64 * 8 * 16 / 2 = 4096 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/79727
# bash research/utils/download_checkpoint.sh 4a12935b-ab5d-4c58-b9e5-ace7b88f49b2 dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e7
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e7" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e7-ffw" \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --max-seq-len 7936 \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --model-size "starcoder2-15b" \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e7-ffw" \
#     --ckpt-sha256 "67d630be68283bab5d2c55ae7125d17262e3a575aec5f1a8bfad51159a7722ab" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e7-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 64

fastbackward_configs:
 - configs/starcoder2_15b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 16
  batch_size: 8
  max_iters: 2600
  warmup_iters: 200
  lr_decay_iters: 2600
  block_size: 7936
  min_lr: 1.0e-7
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-15b-fb-mp2
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset
  eval_data_path: normal@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/validation_dataset;utdefault@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/validation_dataset;utliteral@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-15B-MIX-BS4K-S2600-LR1e5to1e7
  wandb_project: dxy-rogue
