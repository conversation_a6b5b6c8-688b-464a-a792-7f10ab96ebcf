#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-o-v2/dsc-bs512-s5k-lr1e-5.yml
#
# Global batch size should be 128 * 2 * 8 / 4 = 512 to speed up experiments.
#
# Take about hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/78803
# bash research/utils/download_checkpoint.sh 59dae8f9-b4d2-4939-80b2-9b8bf767cbef dxy/dsc-elden/33B-8K-baseline-bs512s5k
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 32017
  eot_token_id: 32014
  pad_token_id: 32014
  gradient_accumulation_steps: 8
  batch_size: 2
  max_iters: 5000
  warmup_iters: 200
  lr_decay_iters: 5000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-base
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/dsc/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/dsc/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/dsc/dataset
  eval_data_path: normal@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/dsc/validation_dataset;utdefault@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/dsc/validation_dataset;utliteral@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/dsc/validation_dataset
  checkpoint_optimizer_state: False

  tokenizer_name: DeepSeekCoderBaseTokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: DSC-33B-MIX-BS512-S5K-LR1e-5
  wandb_project: dxy-rogue
