#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-o-v2/sc2-7b-bs2048s9k-lr1e5to1e6.yml -c CW
#
# Global batch size should be 128 * 4 * 4 / 1 = 2048 to speed up experiments.
#
# Take about 52 hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/80356/logs
# The training fucking crashed in the middle.............
# bash research/utils/download_checkpoint.sh 4d9b9dc7-1b5e-4e5c-9263-4bed0c6575a8 dxy/sc2-elden-correct/SC2-15B-MIX-BS2K-S9K-LR1e5to1e6-MID5400
#
# Convert from FBW to FFW
# python research/tools/ckp_converter/fbw2ffw_starcoder2.py \
#     --inp-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e6" \
#     --out-ckpt="/mnt/efs/augment/checkpoints/dxy/sc2-elden-correct/SC2-15B-MIX-BS4K-S2600-LR1e5to1e6-ffw" \
#     --model-name="starcoder2-15b"
#
# Quantize the FFW checkpoint from BF16 to FP8
# python base/fastforward/starcoder/quantize_starcoder2.py \
#     --max-seq-len 7936 \
#     --calibration-steps 300 \
#     --log-to-stdout \
#     --model-size "starcoder2-15b" \
#     --ckpt-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw" \
#     --out-path "/mnt/efs/augment/checkpoints/dxy/sc2-elden/15B-8K-baseline-bs2ks5k-ffw-fp8" \
#     -d "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset" "/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset"

determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 4
  max_iters: 9000
  warmup_iters: 200
  lr_decay_iters: 9000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  use_sequence_parallel: True
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset
  # eval_data_path: normal@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/validation_dataset;utdefault@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/validation_dataset;utliteral@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/validation_dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-7B-MIX-BS2048S9K-LR1e5to1e6
  wandb_project: dxy-rogue
