#
# python research/fastbackward/determined/launch.py experimental/dxy/rag/configs-o-v2/llama3-bs512-s5k-lr1e-5.yml
#
# Global batch size should be 128 * 2 * 16 / 8 = 512 to speed up experiments.
#
# Take about hours to complete
# Link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/
# bash research/utils/download_checkpoint.sh
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 128

fastbackward_configs:
 - configs/llama3_70b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 128014
  eot_token_id: 128001
  pad_token_id: 128001
  gradient_accumulation_steps: 16
  batch_size: 2
  max_iters: 5000
  warmup_iters: 200
  lr_decay_iters: 5000
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 5
  eval_log_interval: 500
  eval_interval: 200
  checkpoint_dir: /mnt/efs/augment/checkpoints/llama3/Meta-Llama-3-70B-fb/
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/llama3/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/llama3/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/llama3/dataset
  eval_data_path: normal@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/llama3/validation_dataset;utdefault@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/llama3/validation_dataset;utliteral@/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/llama3/validation_dataset
  checkpoint_optimizer_state: False

  tokenizer_name: llama3_base
  use_research_tokenizer: False
  visualize_logits_samples: 8

  run_name: LLAMA3-70B-MIX-BS512-S5K-LR1e-5
  wandb_project: dxy-rogue
