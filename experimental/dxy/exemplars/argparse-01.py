# ---
# tags: [fim]
# description: Test whether the model generates code beyond its current scope.
# # We allow the model to generate lots of tokens to test problems.
# label: argparse
# CoT: The code completion should be about how to define argument with argparse.
# max_generated_tokens: 128
# ---
import argparse


def get_args():
    parser = argparse.ArgumentParser(description="Download & preprocess neox datasets")
    parser.add_argument(
        "dataset",
        # BEGIN
        nargs="?",
        default="enron",
        help="name of dataset to download.",
        # END
        choices=DATASET_CHOICES,
    )
    parser.add_argument(
        "-t",
        "--tokenizer",
        default="GPT2BPETokenizer",
        choices=TOKENIZER_CHOICES,
        help=f'Type of tokenizer to use - choose from {", ".join(TOKENIZER_CHOICES)}',
    )
    parser.add_argument(
        "-d",
        "--data-dir",
        default=get_default_data_dir(),
        help="Directory to which to download datasets / tokenizer files - defaults to ./data",
    )
    parser.add_argument(
        "-v", "--vocab-file", default=None, help="Tokenizer vocab file (if required)"
    )
    parser.add_argument(
        "-m", "--merge-file", default=None, help="Tokenizer merge file (if required)"
    )
    return parser.parse_args()
