# ---
# tags: [struct, infer-usage]
# label: complete-class-via-ref
# CoT: complete the class defination based on how it is used in other functions.
# description: Infer data structure based on usage.
# ---
import dataclasses
from pathlib import Any, Path


@dataclasses.dataclass
class Logger:
    """A class used for logging purpose."""

    # BEGIN
    seed: int
    log_dir: Path
    logger_file: Any
    # END
    def reset(self, log_dir, seed, create_model_dir=True):
        """Create a summary writer logging to log_dir."""
        self.seed = int(seed)
        self.log_dir = Path(log_dir)
        self.model_dir = Path(log_dir) / "checkpoint"
        self.log_dir.mkdir(parents=True, exist_ok=True)
        if create_model_dir:
            self.model_dir.mkdir(parents=True, exist_ok=True)
        self.logger_file = open(self.logger_path, "w")
