"传说中的去掉边框用下边这一句 
"set go= 
""设置配色，这里选择的是desert，也有其他方案，在vim中输入:color 在敲tab键可以查看 
color desert 
"设置背景色，每种配色有两种方案，一个light、一个dark 
"set background=light 
""打开语法高亮 
syntax enable
set background=dark
"colorscheme solarized
colorscheme desert
let g:solarized_termcolors=256

if has('gui_running')
    set background=light
else
    set background=dark
endif
"显示行号 
"set number 
""设置缩进有三个取值cindent(c风格)、smartindent(智能模式，其实不觉得有什么智能)、autoindent(简单的与上一行保持一致) 
set cindent 
"在windows版本中vim的退格键模式默认与vi兼容，与我们的使用习惯不太符合，下边这条可以改过来
"set backspace=indent,eol,start 
""用空格键替换制表符 
"set expandtab 
"制表符占4个空格 
"set tabstop=4 
""默认缩进4个空格大小 
"set shiftwidth=4 
"增量式搜索 
"set incsearch 
""高亮搜索 
set hlsearch
set statusline=[%F]%y%r%m%*%=[Line:%l/%L,Column:%c][%p%%]
set laststatus=2
set ruler
"有时中文会显示乱码，用一下几条命令解决 
"let &termencoding=&encoding 
"set fileencodings=utf-8,gbk 
""很多插件都会要求的配置检测文件类型 
:filetype on 
:filetype plugin on 
:filetype indent on 
"下边这个很有用可以根据不同的文件类型执行不同的命令 
""例如：如果是c/c++类型 
:autocmd FileType c,cpp,cuda :set foldmethod=syntax 
:autocmd FileType c,cpp,cuda :set number 
:autocmd FileType c,cpp,cuda :set cindent 
:autocmd FileType c,cpp,cuda :set expandtab
:autocmd FileType c,cpp,cuda :set tabstop=4
:autocmd FileType c,cpp,cuda :set shiftwidth=4
"例如：如果是python类型 
:autocmd FileType python :set number 
:autocmd FileType python :set foldmethod=syntax 
:autocmd FileType python :set paste 
:autocmd FileType python :set expandtab 
:autocmd FileType python :set tabstop=2
:autocmd FileType python :set shiftwidth=2
"例如：如果是python类型 
:autocmd FileType matlab :set number 
:autocmd FileType matlab :set foldmethod=syntax 
:autocmd FileType matlab :set paste 
:autocmd FileType matlab :set expandtab 
:autocmd FileType matlab :set tabstop=2
:autocmd FileType matlab :set shiftwidth=2
