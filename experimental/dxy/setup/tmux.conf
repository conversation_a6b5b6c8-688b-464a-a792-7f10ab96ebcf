# remap prefix from 'C-b' to 'C-a'
unbind C-b
set-option -g prefix C-q
bind-key C-q send-prefix

# reload config file (change file location to your the tmux.conf you want to use)
bind r source-file ~/.tmux.conf

# switch panes using Alt-arrow without prefix
bind -n M-Left select-pane -L
bind -n M-Right select-pane -R
bind -n M-Up select-pane -U
bind -n M-Down select-pane -D

# Enable mouse control (clickable windows, panes, resizable panes)
# set -g mouse on

# don't rename windows automatically
set-option -g allow-rename off

# change the path
set -g status-left '#{pane_current_path}'
# bind '"' split-window -c "#{pane_current_path}"
# bind % split-window -h -c "#{pane_current_path}"
# bind c new-window -c "#{pane_current_path}"
