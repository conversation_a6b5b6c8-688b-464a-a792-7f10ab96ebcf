PS1='\[\e[1;35m\][\[\e[1;33m\]\u@\h \[\e[1;31m\]\w\[\e[1;35m\]]\[\e[1;36m\]\$ \[\e[0m\]'

# Command History Settings
export HISTTIMEFORMAT='%F, %T '
HISTSIZE=10000
HISTFILESIZE=10000
shopt -s histappend
export HISTCONTROL=ignoredups:erasedups  # no duplicate entries
# Save and reload the history after each command finishes
export PROMPT_COMMAND="history -a; history -c; history -r; $PROMPT_COMMAND"
# Ingore history/ls/pwd
export HISTIGNORE="history:ls:pwd:"
# Search the history to complete the command
if [[ $- == *i* ]]
then
        bind '"\e[A": history-search-backward'
        bind '"\e[B": history-search-forward'
fi

# Customization
alias connect='~/src/augment/deploy/dev/dev_container/connect.sh'
