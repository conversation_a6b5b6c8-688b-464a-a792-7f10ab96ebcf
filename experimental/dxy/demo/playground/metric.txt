"""."""
from __future__ import annotations
import collections
import dataclasses
import functools
import pathlib
import queue
import threading
from typing import Any, Callable, Dict, Literal, Set, Union

import numpy as np
import tqdm
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from augment.research.core import model_input as data_lib
from augment.research.core import types as type_lib
from augment.research.core import utils_for_log
from augment.research.core import utils_for_str as str_lib
from augment.research.eval import hydra as hydra_lib
from augment.research.eval import patch_lib
from augment.research.eval.harness import utils as harness_utils
from augment.research.eval.harness.systems import abs_system
from augment.research.eval.harness.tasks import abs_task
from augment.research.static_analysis import experimental_parsing as exp_parse_lib
from augment.research.static_analysis import parsing as parse_lib

CoarseCategory = Literal[
    "default", "api_call", "interface", "meaningful_str", "empty_like_str"
]

@dataclasses.dataclass
class MiniPatch:
    char_start: int
    """The char start position of the target completion."""

    char_stop: int
    """The char stop position of the target completion."""

    filename: str
    """The file name."""

    repository: str
    """The repository name, will be mapped to the concrete docker image name via a separate dict."""

    category: CoarseCategory
    """A coarse category."""

    sub_categories: list[str]
    """A list of sub-categories under the coarse category, and typical pattern is category@xxx."""


class SupportedMetrics:
    MetricType = Literal["syntax", "hydra_unit_test", "exact_match", "text_similarity"]

    Category2MetricTypes: dict[CoarseCategory, tuple[MetricType, ...]] = {
        "default": ("syntax", "hydra_unit_test", "exact_match"),
        "api_call": ("syntax", "hydra_unit_test", "exact_match"),
        "interface": ("syntax", "hydra_unit_test", "exact_match"),
        "meaningful_str": ("syntax", "text_similarity"),
        "empty_like_str": ("syntax", "exact_match"),
    }

    def __init__(self, corpus: list[str]) -> None:
        self.corpus = corpus
        self.vectorizer = TfidfVectorizer()
        self.tfidf_corpus = self.vectorizer.fit_transform(corpus)

    def __call__(
        self,
        inputs: data_lib.ModelInput,
        completion: str,
        category: CoarseCategory,
        driver: hydra_lib.Driver | None = None,
    ) -> dict:
        all_results = {}
        target_str = inputs.target
        for metric_type in self.Category2MetricTypes[category]:
            if metric_type == "syntax":
                cur_results = self.calculate_syntax_error(inputs, completion)
            elif metric_type == "hydra_unit_test":
                if driver is None:
                    raise ValueError("Must provide the driver for hydra_unit_test.")
                image_name: str = inputs.extra["image_name"]
                mini_patch: MiniPatch = inputs.extra["mini_patch"]
                cur_results = self.calculate_hydra_unit_test(
                    mini_patch, completion, image_name, driver
                )
            elif metric_type == "exact_match":
                if target_str is None:
                    raise ValueError("Target can not be None for exact_match.")
                cur_results = self.calculate_exact_match(target_str, completion)
            elif metric_type == "text_similarity":
                if target_str is None:
                    raise ValueError("Target can not be None for text_similarity.")
                cur_results = self.calculate_text_similarity(target_str, completion)
            else:
                raise TypeError(f"The metric type is's recognized: {metric_type}")
            for key, value in cur_results.items():
                if key in all_results:
                    raise KeyError(
                        f"Find duplicated key {key} vs. {all_results.keys()}"
                    )
                all_results[key] = value
        return all_results
