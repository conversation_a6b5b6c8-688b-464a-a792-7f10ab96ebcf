
if __name__ == "__main__":
    retriever = create_retriever(retriever_config)
    generation_options = GenerationOptions(
        temperature=args.temperature,
        top_k=args.top_k,
        top_p=args.top_p,
        max_generated_tokens=512,
    )
    if args.model_ckp:
        model = get_model(args.model, checkpoint_path=pathlib.Path(args.model_ckp))
    elif "@" not in args.model:
        model = get_model(args.model)
    else:
        mode, url = args.model.split("@")
        model = RemoteLLAMACPP(url=url, mode=mode)
