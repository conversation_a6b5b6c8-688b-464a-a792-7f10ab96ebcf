"""The post-processing approach used in the Edit Demo."""
import re
import typing

from research.core import utils_for_str


def clean_markdown(text: str) -> typing.Tuple[str, bool]:
    pattern = r"```(?:python|json|yaml|c\+\+|bash|javascript)?(?:\r\n| \n|\n)(.*?)```"
    match = re.search(pattern, text, re.DOTALL)
    if match:
        text = match.group(1)
        print(f"Matching the pattern: {pattern}")
        return text, True
    else:
        return text, False


def post_process_for_edit(
    completion: str,
    start_symbols: str,
    end_symbols: str,
    prefix: typing.Optional[str] = None,
    suffix: typing.Optional[str] = None,
) -> str:
    """Post-process the completion."""
    start_symbols, end_symbols = re.escape(start_symbols), re.escape(
        end_symbols.rstrip("\n")
    )
    # Find the code within the special symbols
    pattern = rf"{start_symbols}(.*?){end_symbols}"
    match = re.search(pattern, completion, re.DOTALL)
    if match:
        print(f"Match the pattern: : {pattern}")
        return match.group(1)
    # Find the code within backticks
    potential_response, matched = clean_markdown(completion)
    if matched:
        print(rf"Match the pattern: : {pattern}")
        response = potential_response
        response, success_del = utils_for_str.delete_prefix_suffix_from_content(
            response, prefix=prefix, suffix=suffix
        )
        if not success_del[0] and not success_del[1]:
            response = utils_for_str.delete_lines_from_content(
                response,
                start=0 if prefix is None else len(prefix.splitlines(keepends=True)),
                end=0 if suffix is None else len(suffix.splitlines(keepends=True)),
            )
        elif not success_del[0]:
            response = utils_for_str.delete_lines_from_content(
                response,
                start=0 if prefix is None else len(prefix.splitlines(keepends=True)),
                end=0,
            )
        elif not success_del[1]:
            response = utils_for_str.delete_lines_from_content(
                response,
                start=0,
                end=0 if suffix is None else len(suffix.splitlines(keepends=True)),
            )
        return response
    # Find the codes after the start symbols
    pattern = rf"{start_symbols}(.*?)"
    match = re.search(pattern, completion, re.DOTALL)
    if match:
        print(rf"Match the pattern: : {pattern}")
        response = match.group(1)
        response, success_del = utils_for_str.delete_prefix_suffix_from_content(
            response, prefix=prefix, suffix=None
        )
        return response
    return completion
