"""Launch a FLASK server for the Model Parallelism Run to support editing.

# 13-B
CUDA_VISIBLE_DEVICES=0,1 torchrun --nproc_per_node 2 experimental/dxy/demo/launch_flask_for_mp_edit.py --port 5005 \
    --edit_ckp /mnt/efs/augment/user/dxy/logs/edit.pack.new/CL-BASE13B-MixALL3v_PACK_OTarget-step_4000-WD1_0-b2x8x2-lr_3e-6_cosine/best_ckp_for_all_in_llama_format/

# 34-B
torchrun --nproc_per_node 4 experimental/dxy/demo/launch_flask_for_mp_edit.py --port 5005 \
    --edit_ckp /mnt/efs/augment/user/dxy/logs/edit.pack.new/CL-BASE34B-MixALL3v_PACK_OTarget-step_4000-WD1_0-b4x8x1-lr_3e-6_cosine/best_ckp_for_all_in_llama_format/

torchrun --nproc_per_node 4 experimental/dxy/demo/launch_flask_for_mp_edit.py --port 5004 \
    --edit_ckp /mnt/efs/augment/user/dxy/logs/edit.pack.new/CL-BASE34B-MixALL3v_PACK_Full-S4K_WUP100-WD1_0-b4x8x1-lr_1e-6_cosine/best_ckp_for_all_in_llama_format/
"""
import argparse
import logging
import os
import pathlib
import typing

import torch.distributed.rpc as rpc
from flask import Flask, jsonify, request

from research.core import utils_for_log, utils_for_str
from research.models.llama2_models import LLAMA2Model
from research.models.meta_model import GenerationOptions

app = Flask(__name__)
logger = utils_for_log.create_logger(__name__, log_level=logging.INFO)
current_directory = pathlib.Path(__file__).parent
logger.info(f"The Current Directory: {current_directory}")
augment_directory = current_directory.parent.parent.parent
logger.info(f"The Augment Directory: {augment_directory}")


def prepare_prompt(
    prefix: str, suffix: str, selected_code: str, instruction: str
) -> str:
    prefix_last_few_lines = utils_for_str.get_last_n_lines(prefix, 60)
    suffix_first_few_lines = utils_for_str.get_first_n_lines(suffix, 60)
    input_template = "<INST>{instruction}<SELECTED>{selected_code}<SUFFIX>{suffix}<PREFIX>{prefix}<UPDATED>"
    prompt = input_template.format(
        prefix=prefix_last_few_lines,
        suffix=suffix_first_few_lines,
        selected_code=selected_code,
        instruction=instruction,
    )
    return prompt


global_model = None


def model_inference(tokens: typing.List[int]) -> tuple[int, str]:
    rank = rpc.get_worker_info().id
    # logger.info(f"[Rank {rank}] received tokens: {tokens}")
    response = global_model.raw_generate(
        tokens, options=GenerationOptions(max_generated_tokens=2048, top_k=0)
    )
    return (rank, response)


@app.route("/edit", methods=["POST"])
def edit():
    """The edit communication func.

    Required fields in the edit mode:
    - selected_code: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    selected_code = data.get("selected_code", None)
    instruction = data.get("instruction", None)
    prefix = data.get("prefix", None)
    suffix = data.get("suffix", None)
    selection_range = data.get("selection_range", None)
    logger.info(f"The selection range: {selection_range}")
    if selected_code is None or instruction is None or prefix is None or suffix is None:
        logger.info("Did not find selected_code or instruction or prefix or suffix")
        return jsonify({"response": "", "status": "missing-message-input"})
    try:
        instruction = instruction.strip()
        logger.info(f"instruction: {instruction}")
        logger.info(f"Selected code:\n{selected_code}")
        logger.info("-" * 100)
        prompt = prepare_prompt(
            prefix=prefix,
            suffix=suffix,
            selected_code=selected_code,
            instruction=instruction,
        )
        tokens = global_model.tokenizer.tokenize(prompt)
        # Send the inference task to the workers
        world_size = os.environ.get("WORLD_SIZE")
        assert world_size is not None
        futures, response_by_rank = [], dict()
        for worker_rank in range(int(world_size)):
            worker_name = f"worker{worker_rank}"
            # Server sends an asynchronous RPC to each worker
            fut = rpc.rpc_async(worker_name, model_inference, args=(tokens,))
            futures.append(fut)
        # Collect all responses from workers
        for fut in futures:
            rank, response = fut.wait()
            response_by_rank[rank] = response
        response = None
        for rank, res in response_by_rank.items():
            logger.info(f"[Rank {rank}] produced response: {res}")
            response = res
        assert response is not None
        logger.info("-" * 100)
        all_parts = utils_for_str.extract_code_within_backticks(
            response, keep_end_new_line_break=True
        )
        if len(all_parts) > 0:
            modified_code = all_parts[0]
            logger.info(
                f"Find backticks quoted codes, and thus use it: {modified_code.encode()}"
            )
        else:
            logger.info("Did not find backticks quoted codes and use all.")
            modified_code = selected_code
        return jsonify(
            {
                "response": modified_code,
                "modified_code": modified_code,
                "status": "success",
            }
        )
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--edit_ckp",
        type=str,
        default="",
        help="The Edit Model Checkpoint.",
    )
    parser.add_argument(
        "--template_version",
        type=str,
        choices=("v1", "v2"),
        default="v1",
        help="The version of the template to use.",
    )
    parser.add_argument(
        "--port",
        type=int,
        default="5000",
        help="The port to send request to this server.",
    )
    args = parser.parse_args()
    logger.info(f"args: {args}")
    logger.info(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
    global global_model
    global_model = LLAMA2Model(checkpoint_path=pathlib.Path(args.edit_ckp))
    global_model.load()
    local_rank = os.environ.get("LOCAL_RANK")
    world_size = os.environ.get("WORLD_SIZE")
    logger.info(f"The local rank: {local_rank}, the world size: {world_size}")
    assert local_rank is not None and world_size is not None
    local_rank, world_size = int(local_rank), int(world_size)
    # Setup the RPC server.
    rpc.init_rpc(
        name=f"worker{local_rank}",
        rank=local_rank,
        world_size=world_size,
    )
    if local_rank == 0:
        app.run(
            host="0.0.0.0",
            processes=1,
            threaded=True,
            debug=False,
            port=args.port,
        )
    else:
        pass
    rpc.shutdown()


if __name__ == "__main__":
    main()
