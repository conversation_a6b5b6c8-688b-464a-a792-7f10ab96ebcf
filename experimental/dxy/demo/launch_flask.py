"""Launch a FLASK server for chat and edit demo.

[Launch LLAMA.CPP servers]
- CUDA_VISIBLE_DEVICES=3 bash research/utils/launch-llama.cpp.sh --base 8080
- CUDA_VISIBLE_DEVICES=4 bash research/utils/launch-llama.cpp.sh --python 8082
- CUDA_VISIBLE_DEVICES=5 bash research/utils/launch-llama.cpp.sh --instruct 8084

- CUDA_VISIBLE_DEVICES=6 bash research/utils/launch-llama.cpp.sh --wizardcoder 8086
- CUDA_VISIBLE_DEVICES=0 bash research/utils/launch-llama.cpp.sh --deepseek_coder_instruct33b 8087
- CUDA_VISIBLE_DEVICES=1 bash research/utils/launch-llama.cpp.sh --deepseek_coder_instruct33b 8088
- CUDA_VISIBLE_DEVICES=3 bash research/utils/launch-llama.cpp.sh --deepseek_llm_chat33b 8089
- CUDA_VISIBLE_DEVICES=4 bash research/utils/launch-llama.cpp.sh --deepseek_llm_chat33b 8090

[Launch Edit Server]
python experimental/dxy/demo/launch_flask.py --port 5005
python experimental/dxy/demo/launch_flask.py --port 5005 --model wizardcoder
python experimental/dxy/demo/launch_flask.py --port 5005 --model deepseek_coder_instruct
python experimental/dxy/demo/launch_flask.py --port 5005 --model deepseek_llm_chat
CUDA_VISIBLE_DEVICES=2,3 python experimental/dxy/demo/launch_flask.py --port 5005 --model local
"""

import argparse
import copy
import hashlib
import logging
import os
import pathlib
import random
import socket
import threading
import time
import typing

from flask import Flask, jsonify, request

from experimental.dxy.demo.post_processing import clean_markdown, post_process_for_edit
from research.core import constants, utils_for_file, utils_for_log, utils_for_str
from research.core.llama_prompt_formatters import (
    CodeLlamaChatFormatter,
    Dialog,
    WizardCoderChatFormatter,
)
from research.core.model_input import ModelInput
from research.core.types import Chunk, Document
from research.models.llama2_models import DeepSeekLLMChat
from research.models.meta_model import GenerationOptions
from research.models.remote_models import (
    CodeLLaMA_LLaMACPP_Model,
    DeepSeekCoderInstruct_LLaMACPP_Model,
    DeepSeekLLMChat_LLaMACPP_Model,
    Mistral_Model,
    OpenAI_Model,
    RemoteLLAMACPP,
    WizardCoder_LLaMACPP_Model,
)
from research.retrieval.chunking_functions import LineLevelChunker
from research.retrieval.file_filterer import basic_file_filterer
from research.retrieval.query_formatters import SimpleQueryFormatter
from research.retrieval.scorers.good_enough_bm25_scorer import (
    GoodEnoughBM25Scorer,
)
from research.retrieval.retrieval_database import RetrievalDatabase


def doc_to_id(text: str) -> str:
    digester = hashlib.sha256()
    digester.update(bytearray(text, "utf8"))
    return digester.hexdigest()


def get_docs_from_files(path: pathlib.Path, extensions: list[str]) -> list[Document]:
    """Add files from given dir to index."""
    docs = []
    num_files = 0
    num_lines = 0
    print(f"Processing corpus at {path}, extensions: {extensions}")
    for extension in extensions:
        if not extension.startswith("."):
            raise ValueError(f"Extension doesn't start with .: {extension}")
        pattern = f"*{extension}"
        for filepath in pathlib.Path(path).rglob(pattern):
            num_files += 1
            with filepath.open("r", encoding="utf8") as file:
                text = file.read()
                num_lines += len(text.split("\n"))
                doc = Document(
                    text=text,
                    id=doc_to_id(text),
                    path=str(filepath.relative_to(path)),
                )
                docs.append(doc)
    print(f"Corpus contains {num_files} files with {num_lines} lines")
    return docs


app = Flask(__name__)
GLOBAL_EDIT_LOG_DIR = pathlib.Path(
    "/mnt/efs/augment/user/dxy/datasets/edit.raw/dogfood_edit_logs"
)
GLOBAL_EDIT_LOG_DIR.mkdir(exist_ok=True, parents=True)
LOGGER_SAVE_DIR = GLOBAL_EDIT_LOG_DIR / "loggers"
LOGGER_SAVE_DIR.mkdir(exist_ok=True, parents=True)
logger = utils_for_log.create_logger(
    __file__,
    log_file=LOGGER_SAVE_DIR
    / f"{pathlib.Path(__file__).stem}-{utils_for_log.time_string()}.log",
    log_level=logging.INFO,
)
logger.info(f"__file__: {__file__}")
current_directory = pathlib.Path(__file__).parent
logger.info(f"The Current Directory: {current_directory}")
augment_directory = current_directory.parent.parent.parent
logger.info(f"The Augment Directory: {augment_directory}")
research_directory = augment_directory / "research"
logger.info(f"The Augment Research Directory: {research_directory}")
assert research_directory.exists(), f"{research_directory} must exist"
logger.info(f"The Edit Log Directory: {GLOBAL_EDIT_LOG_DIR}")


models = {
    "llama_base": CodeLLaMA_LLaMACPP_Model(url="http://127.0.0.1:8080"),
    "llama_python": CodeLLaMA_LLaMACPP_Model(url="http://127.0.0.1:8082"),
    "llama_instruct": CodeLLaMA_LLaMACPP_Model(url="http://127.0.0.1:8084"),
    "wizardcoder": [
        WizardCoder_LLaMACPP_Model(url="http://127.0.0.1:8086"),
    ],
    "deepseek_coder_instruct": [
        DeepSeekCoderInstruct_LLaMACPP_Model(url="http://127.0.0.1:8087"),
        DeepSeekCoderInstruct_LLaMACPP_Model(url="http://127.0.0.1:8088"),
    ],
    "deepseek_llm_chat": [
        DeepSeekLLMChat_LLaMACPP_Model(url="http://127.0.0.1:8089"),
        DeepSeekLLMChat_LLaMACPP_Model(url="http://127.0.0.1:8090"),
    ],
    "openai": [
        OpenAI_Model(
            api_key=os.getenv("OPENAI_API_KEY"), openai_model_name="gpt-3.5-turbo"
        ),
    ],
    "mistral": [
        Mistral_Model(
            api_key=os.getenv("MISTRAL_API_KEY"), mistral_model_name="mistral-medium"
        ),
    ],
}
chunker = LineLevelChunker(max_lines_per_chunk=15)
query_formatter = SimpleQueryFormatter(max_lines=10, add_sos=False)
scorer = GoodEnoughBM25Scorer(query_formatter)
GLOBAL_RETRIEVER = RetrievalDatabase(chunker, scorer, basic_file_filterer)

# documents = get_docs_from_files(research_directory, [".py"])
# GLOBAL_RETRIEVER.add_docs(documents)


def build_retrieval_message(retrieved_chunks: list[Chunk]) -> str:
    all_strings = []
    for idx, chunk in enumerate(retrieved_chunks):
        # if chunk.parent_doc.path:
        #     header = f"Code Snippet #{idx} from the file path {chunk.parent_doc.path}"
        # else:
        #     header = f"Code Snippet #{idx}"
        # all_strings.append(f"{header}\n```\n{chunk.text}\n```")
        # all_strings.append(f"```\n{chunk.text}\n```")
        if chunk.parent_doc.path:
            header = f"Code Snippet from the file path {chunk.parent_doc.path}"
        else:
            header = "Code Snippet"
        all_strings.append(f"{header}\n```\n{chunk.text}\n```")
    return "\n\n".join(all_strings)


def count_leading_spaces(original_line):
    """Counts the number of spaces at the beginning of a line"""
    count = 0
    for char in original_line:
        if char == " ":
            count += 1
        else:
            break
    return count


def count_min_indentation(code: str) -> int:
    # Adjust indentation
    min_spaces = None
    for original_line in code.splitlines(keepends=True):
        current_indent = count_leading_spaces(original_line)
        if min_spaces is None:
            min_spaces = current_indent
        else:
            min_spaces = min(min_spaces, current_indent)
    if min_spaces is None:
        return 0
    else:
        return min_spaces


def cleanup(original_code: str, modified_code: str):
    """Fix common artifacts of models' generations."""
    # Remove Markdown formatting
    modified_code, _ = clean_markdown(modified_code)
    min_spaces_for_original = count_min_indentation(original_code)
    min_spaces_for_modified = count_min_indentation(modified_code)
    extra_identation = max(min_spaces_for_original - min_spaces_for_modified, 0)
    print(f"minimal spaces: {min_spaces_for_original} vs. {min_spaces_for_modified}")
    modified_lines = []
    for modified_line in modified_code.splitlines(keepends=True):
        modified_lines.append(" " * extra_identation + modified_line)
    modified_code = "".join(modified_lines)
    return modified_code


class EditModel:
    """The edit model with different versions of prompts."""

    def __init__(self) -> None:
        self.models: list[typing.Union[DeepSeekLLMChat, RemoteLLAMACPP]] = []

    def init(
        self,
        model_name: typing.Literal[
            "wizardcoder",
            "deepseek_coder_instruct",
            "deepseek_llm_chat",
            "local",
            "mistral",
        ],
    ):
        if model_name == "local":
            self.models.append(
                DeepSeekLLMChat(
                    checkpoint_path=constants.AUGMENT_CHECKPOINTS_ROOT
                    / "deepseek"
                    / "deepseek-llm-67b-chat"
                )
            )
            self.models[0].load()
            return
        for model in models[model_name]:
            s = None
            try:
                if hasattr(model, "url"):
                    if "://" in model.url:
                        _, url = model.url.split("://")
                        host, port = url.split(":")
                    else:
                        host, port = model.url.split(":")
                    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    s.settimeout(1.0)
                    s.connect((host, int(port)))
                self.models.append(model)
            except (ValueError, socket.error):
                print(f"{model.url} is not available")
            finally:
                if s is not None:
                    s.close()
        print(f"Avaliable DeepSeek models: {self.models}")
        assert len(self.models), "No model is available"

    def __call__(
        self,
        selected_code: str,
        instruction: str,
        prefix: typing.Optional[str] = None,
        suffix: typing.Optional[str] = None,
        lines_in_prefix_suffix: int = 0,
        top_k: int = 0,
        top_p: float = 0.0,
        temperature: float = 0.0,
    ) -> typing.Tuple[str, str, str]:
        model = random.choice(self.models)
        if lines_in_prefix_suffix <= 0:
            prefix, suffix = None, None
        if prefix is None or suffix is None:
            full_prompt = f"""{instruction}

Adhere strictly to these guidelines in your response:

- Execute the provided instructions with precision and accuracy, ensuring no errors.
- Your modifications should be based directly on the provided code.
- Preserve the original code's indentation and trailing whitespace in your modifications.
- Start your response directly with the modified code, without any additional text, headers, comments, suggestions, or formatting.

Code
```
{selected_code}```
"""
            full_response = model.generate(
                ModelInput(prefix=full_prompt),
                options=GenerationOptions(
                    max_generated_tokens=512,
                    top_k=top_k,
                    top_p=top_p,
                    temperature=temperature,
                ),
            )
            response = cleanup(selected_code, full_response)
        else:
            last_n_prefix_lines = utils_for_str.get_last_n_lines(
                prefix, lines_in_prefix_suffix
            )
            first_n_suffix_lines = utils_for_str.get_first_n_lines(
                suffix, lines_in_prefix_suffix
            )
            start_symbols, end_symbols = "@" * 9 + "\n", "&" * 9 + "\n"
            full_prompt = f"""Full Code:
```
{last_n_prefix_lines}{start_symbols}{selected_code}{end_symbols}{first_n_suffix_lines}
```

Edit Request: {instruction}

Please follow these instructions carefully and restrictly:
- The edit request is only applied to the code within these 9-times-repeated characters {start_symbols.strip()} and {end_symbols.strip()}.
- Preserve those consequent special 9-times-repeated characters as a whole.
- Keep the codes outside of those special 9-times-repeated characters exactly identical.
- Completely remove parts that are no longer needed; do not simply comment them out.
- Preserve the original indentation and style.
- In case of unclear instructions, maintain the original code.
- Do not output any additional text, headers, comments, suggestions, formatting, or explanations.
"""
            full_response = model.generate(
                ModelInput(prefix=full_prompt),
                options=GenerationOptions(
                    max_generated_tokens=1024,
                    top_k=top_k,
                    top_p=top_p,
                    temperature=temperature,
                ),
            )
            response = post_process_for_edit(
                full_response,
                start_symbols,
                end_symbols,
                last_n_prefix_lines,
                first_n_suffix_lines,
            )
        return response, full_response, full_prompt


class ExplainModel:
    """The explain model with different versions of prompts."""

    def __call__(self, selected_code: str, message: str) -> str:
        model = models["wizardcoder"]
        model.prompt_formatter = WizardCoderChatFormatter()
        if not message:
            prompt = f"""You are an expert and respectful Python and TypeScript developer, named as AugmentCode.
Think step by step to make a beautiful and coherent explaination for the following codes:
```
{selected_code}
```"""
        else:
            prompt = f"""You are an expert and respectful Python and TypeScript developer, named as AugmentCode.
Exactly follow "{message}" to explain the following codes:
```
{selected_code}
```"""
        response = model.generate(
            ModelInput(prefix=prompt),
            options=GenerationOptions(max_generated_tokens=512),
        )
        return response


class ChatModel:
    """The chat model with different versions of prompts."""

    version: str = "v0"

    def __init__(self, version: str = "v0") -> None:
        self.version = version

    def __call__(
        self, dialog: Dialog, selected_code: str, logger: logging.Logger
    ) -> str:
        dialog = copy.deepcopy(dialog)
        if self.version == "v0" or selected_code == "":  # ignore the selected code
            model = models["llama_instruct"]
            model.prompt_formatter = CodeLlamaChatFormatter()
            dialog.system_prompt = r"""You are an expert, respectful, and professional coding copilot, named as AugmentCode.
Please always answer as helpfully as possible.
If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.
If you don't know the answer to a question, please don't share false information."""
            response = model.generate(
                ModelInput(extra={"dialog": dialog}),
                options=GenerationOptions(max_generated_tokens=512),
            )
        elif self.version == "v1":
            model = models["wizardcoder"]
            model.prompt_formatter = WizardCoderChatFormatter(
                question_prefix="\n\nUser: ", answer_prefix="\n\nAssistant: "
            )
            dialog.messages = [
                """You are an expert, respectful, and professional coding copilot, named as AugmentCode.
Please always answer as helpfully as possible.
I may provide you some useful code snippets which are noisy and messay.
If you find some parts are helpful, please extract the minimal useful part and combine with my instruction/questions to use, instead of copy and paste the entire things.""",
                """Sure, I will follow your order.""",
            ] + dialog.messages
            last_message = dialog.messages.pop()
            retrieved_chunks, _ = GLOBAL_RETRIEVER.query(
                ModelInput(
                    prefix=selected_code,
                    suffix="",
                    path="",
                ),
                top_k=8,
            )
            retrieval_message = (
                f"Here are some reference code snippets in my repository."
                f"\n{build_retrieval_message(retrieved_chunks)}"
            )
            logger.info(f"Retrieval message: {retrieval_message}")
            dialog.messages.append(retrieval_message)
            dialog.messages.append("Sure, I have kept them in my mind.")
            dialog.messages.append(last_message)
            response = model.generate(
                ModelInput(extra={"dialog": dialog}),
                options=GenerationOptions(max_generated_tokens=512),
            )
            response = utils_for_str.trim_string(response, ["\nUser: "])
        else:
            raise TypeError(f"Unknown version: {self.version}")
        return response


GLOBAL_EDIT_MODEL = EditModel()
GLOBAL_EXPLAIN_MODEL = ExplainModel()
GLOBAL_CHAT_MODEL = ChatModel()
GLOBAL_DIALOG = Dialog(messages=[])


def shorten(string: str):
    return string.lower().strip()


@app.route("/chat", methods=["POST"])
def chat():
    """The chat communication func.

    Required fields in the chat mode:
    - message: str
    - [In Future] selected_code: str

    Usage:
    - See details in research/utils/launch-llama.cpp.sh about how to build and launch llama.cpp models.
    - If u type "/history", it will show all the dialog information.
    - If u type "/clear", it will remote all the history messages.
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    message = data.get("message", "").strip()
    selected_code = data.get("selected_code", "")
    if message == "":
        logger.info("Did not find the message")
        return jsonify({"response": "", "status": "missing-message-input"})
    if shorten(message) == "/history":
        history_str = "\n".join([f"- {x}" for x in GLOBAL_DIALOG.messages])
        logger.info(f"Show all the history message: {history_str}")
        return jsonify({"response": history_str, "status": "show-history"})
    elif shorten(message) == "/clear":
        logger.info("Clear all the history messages.")
        GLOBAL_DIALOG.messages = []
        return jsonify(
            {
                "response": "All history messages have been cleared.",
                "status": "clear-history",
            }
        )

    GLOBAL_DIALOG.messages.append(message)
    try:
        response = GLOBAL_CHAT_MODEL(GLOBAL_DIALOG, selected_code, logger)
        GLOBAL_DIALOG.messages.append(response)
        return jsonify({"response": response, "status": "success"})
    except BaseException as e:
        GLOBAL_DIALOG.messages.pop()
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/edit", methods=["POST"])
def edit():
    """The edit communication func.

    Required fields in the chat mode:
    - selected_code: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    # As defined here https://github.com/augmentcode/augment/blob/main/clients/vscode/src/augment-api.ts#L140
    # our code edit feature accepts these inputs:
    #   - instruction: string;
    #   - selected_code: string;
    #   - prefix: string;
    #   - suffix: string;
    #   - top_k?: number;
    #   - top_p?: number;
    #   - temperature?: number;
    #   - lang?: string;
    #   - lines_in_prefix_suffix?: number;
    #
    request_id = data.get("request_id", None)
    selected_code = data.get("selected_code", None)
    instruction = data.get("instruction", None)
    original_prefix = data.get("prefix", None)
    original_suffix = data.get("suffix", None)
    top_k = data.get("top_k", 0)
    top_p = data.get("top_p", 0.0)
    temperature = data.get("temperature", 0)
    lines_in_prefix_suffix = data.get("lines_in_prefix_suffix", 0)
    language = data.get("lang", None)
    debug = data.get("debug", False)
    # Normalize top_k, top_p, temperature
    try:
        top_k = int(float(top_k))
    except (ValueError, TypeError):
        top_k = 0
    try:
        top_p = float(top_p)
    except (ValueError, TypeError):
        top_p = 0.0
    try:
        temperature = float(temperature)
    except (ValueError, TypeError):
        temperature = 0.0
    try:
        lines_in_prefix_suffix = int(float(lines_in_prefix_suffix))
    except (ValueError, TypeError):
        lines_in_prefix_suffix = 0

    thread_id = threading.get_ident()

    if selected_code is None or instruction is None:
        logger.info("Did not find selected_code or instruction")
        return jsonify({"response": "", "status": "missing-message-input"})

    try:
        instruction = instruction.strip()
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(
            "Generation hyper-paramters:\n"
            + f"top_k = {top_k}\n"
            + f"top_p = {top_p}\n"
            + f"temperature = {temperature}\n"
            + f"lines_in_prefix_suffix = {lines_in_prefix_suffix}"
        )
        if lines_in_prefix_suffix <= 0:
            prefix, suffix = None, None
        else:
            prefix, suffix = original_prefix, original_suffix
        start_time = time.time()
        response, full_response, prompt = GLOBAL_EDIT_MODEL(
            selected_code,
            instruction,
            prefix=prefix,
            suffix=suffix,
            lines_in_prefix_suffix=lines_in_prefix_suffix,
            top_k=top_k,
            top_p=top_p,
            temperature=temperature,
        )
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        time_cost = time.time() - start_time
        debug_json_data = {}
        try:
            debug_json_data = {
                "request_id": request_id,
                "selected_code": selected_code,
                "prefix": original_prefix,
                "suffix": original_suffix,
                "instruction": instruction,
                "response": response,
                "full_response": full_response,
                "prompt": prompt,
                "lines_in_prefix_suffix": lines_in_prefix_suffix,
                "language": language,
                "thread_id": thread_id,
                "sender_ip": request.remote_addr,
                "time_cost(seconds)": time_cost,
            }
            utils_for_file.write_json(
                cache_file_path,
                debug_json_data,
                indent=2,
            )
            logger.info(f"Time cost for model inference: {time_cost*1000:.1f} ms")
            logger.info(f"Cache file path: {cache_file_path}")
            logger.info(f"Raw model output:\n{full_response}")
        except BaseException as e:
            logger.info(f"Fail to write cache file ({cache_file_path}) due to {e}")
        # If it is the debug mode, return more information.
        if debug:
            return jsonify(
                {
                    "response": response,
                    "modified_code": "",
                    "status": "success",
                    **debug_json_data,
                }
            )
        else:
            return jsonify(
                {"response": response, "modified_code": "", "status": "success"}
            )
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/log_status", methods=["POST"])
def log_status():
    """Logging the status.

    - request_id: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")

    request_id = data.get("request_id", None)
    instruction = data.get("instruction", None)
    human_annotated_text = data.get("human_annotated_text", None)
    human_annotated_instruction = data.get("human_annotated_instruction", None)
    status = data.get("status", None)
    debug = data.get("debug", False)

    if request_id is None or instruction is None:
        logger.info("Did not find the request_id or instruction")
        return jsonify({"status": "incorrect inputs"})

    try:
        logger.info(f"instruction: {instruction}")
        logger.info(f"request_id: {request_id}")
        logger.info(f"status: {status}")
        thread_id = threading.get_ident()
        # Cache the results to a file
        cache_file_path = GLOBAL_EDIT_LOG_DIR / (
            "FEEDBACK-"
            + utils_for_log.time_string().replace(":", "-")
            + f"-TID{thread_id}"
            + f"-{utils_for_str.get_random_str(8)}"
            + ".json"
        )
        debug_json_data = {}
        try:
            debug_json_data = {
                "instruction": instruction,
                "request_id": request_id,
                "thread_id": thread_id,
                "human_annotated_text": human_annotated_text,
                "human_annotated_instruction": human_annotated_instruction,
                "status": status,
                "sender_ip": request.remote_addr,
            }
            utils_for_file.write_json(
                cache_file_path,
                debug_json_data,
                indent=2,
            )
            logger.info(f"Cache file path: {cache_file_path}")
        except BaseException as e:
            logger.info(f"Fail to write cache file ({cache_file_path}) due to {e}")
        # If it is the debug mode, return more information.
        if debug:
            return jsonify({"status": "success", **debug_json_data})
        else:
            return jsonify({"status": "success"})
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/explain", methods=["POST"])
def explain():
    """The explain communication func.

    Required fields in the chat mode:
    - selected_code: str
    - message: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    selected_code = data.get("selected_code", None)
    message: str = data.get("message", "").strip()
    logger.info(f"selected_code: {selected_code}")
    logger.info(f"message: {message}")

    if selected_code is None:
        logger.info("Did not find selected_code")
        return jsonify({"response": "", "status": "missing-message-input"})
    try:
        response = GLOBAL_EXPLAIN_MODEL(selected_code, message)
        return jsonify({"response": response, "status": "success"})
    except BaseException as e:
        logger.info(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        choices=(
            "wizardcoder",
            "deepseek_coder_instruct",
            "deepseek_llm_chat",
            "local",
            "openai",
            "mistral",
        ),
        default="deepseek_coder_instruct",
        help="The core LLM used for inference.",
    )
    parser.add_argument(
        "--port",
        type=str,
        default="5005",
        help="The port to send request to this server.",
    )
    args = parser.parse_args()
    GLOBAL_EDIT_MODEL.init(args.model)
    app.run(host="0.0.0.0", processes=1, threaded=True, debug=False, port=args.port)
