"""Interactive chat demo backend by LLAMA models.

python experimental/dxy/demo/chat.py

Usage:
- See details in research/utils/launch-llama.cpp.sh about how to build and launch llama.cpp models.
- If u type "/exit", u will exist the program.
- If u type "/history", it will show all the dialog information.
- If u type "/clear", it will remote all the history messages.
"""
import argparse

from termcolor import colored

from research.core.llama_prompt_formatters import Dialog
from research.core.model_input import ModelInput
from research.models.meta_model import GenerationOptions
from research.models.remote_models import CodeLLaMA_LLaMACPP_Model

LOCAL_HOST_URL = "http://127.0.0.1"

SYSTEM_PROMPT = r"""You are a helpful, respectful and honest assistant. Always answer as helpfully as possible.
If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.
If you don't know the answer to a question, please don't share false information."""


def shorten(string: str):
    return string.lower().strip()


def main(port: str):
    model = CodeLLaMA_LLaMACPP_Model(url=f"{LOCAL_HOST_URL}:{port}", mode="chat")
    dialog = Dialog(messages=[], system_prompt=SYSTEM_PROMPT)

    while True:
        # 1. Wait for the user to type an input
        user_input = input("User: ")

        if shorten(user_input) == "/exit":
            print("Exiting the chat program.")
            break
        elif shorten(user_input) == "/history":
            print(dialog)
            continue
        elif shorten(user_input) == "/clear":
            dialog.messages = []
            continue
        dialog.messages.append(user_input)

        # 2. Let the model generate responses
        try:
            response = model.generate(
                ModelInput(extra={"dialog": dialog}),
                options=GenerationOptions(max_generated_tokens=256),
            )
        except BaseException as e:
            print(f"Fail to call model.generate due to {e}")
            continue

        # 3. Show the processed input to the terminal
        response = colored(response, color="green", on_color="on_white")
        print(f"Assistant: {response}")
        dialog.messages.append(response)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--port",
        type=str,
        default="8080",
        help="The port to send request to in your localhost (http://127.0.0.1)",
    )
    args = parser.parse_args()
    main(args.port)
