"""Launch a FLASK server for the edit demo only!

Some nice checkpoints:
- /mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-data2v1r_full-s10000-b8-lr_000002/ckp_in_llama_format
- /mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-data2v1r_onlytgt-s6000-b8-lr_000002/ckp_in_llama_format
- /mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-data3v1r_onlytgt-s8000-b8-lr_000002/ckp_in_llama_format

# Python: fine-tuned only on the target string
CUDA_VISIBLE_DEVICES=7 python experimental/dxy/demo/launch_flask_for_edit.py --port 5000 --template_version v2 \
    --edit_ckp /mnt/efs/augment/user/dxy/logs/edit.pack/CL-BASE7B-MixALL2v_PACK_OTarget-step_4000-WD1_0-b8-lr_00002/best_ckp_for_python_in_llama_format

# All-Language: fine-tuned only on the target string
CUDA_VISIBLE_DEVICES=5 python experimental/dxy/demo/launch_flask_for_edit.py --port 5001 --template_version v2 \
    --edit_ckp /mnt/efs/augment/user/dxy/logs/edit.pack.new/CL-BASE7B-MixALL3v_PACK_OTarget-step_4000-WD1_0-b1x8x4-lr_3e-6_cosine/best_ckp_for_all_in_llama_format
"""
import argparse
import logging
import pathlib
import random
import typing

from flask import Flask, jsonify, request

from experimental.dxy.edits.prompter import EditPromptTemplate
from research.core import utils_for_log, utils_for_str
from research.models.llama2_models import LLAMA2Model
from research.models.meta_model import GenerationOptions

app = Flask(__name__)
logger = utils_for_log.create_logger(__name__, log_level=logging.INFO)
current_directory = pathlib.Path(__file__).parent
logger.info(f"The Current Directory: {current_directory}")
augment_directory = current_directory.parent.parent.parent
logger.info(f"The Augment Directory: {augment_directory}")


class EditModel:
    """The edit model with different versions of prompts."""

    ckp_dir: str = ""
    template_version: str = "v1"
    model: typing.Optional[LLAMA2Model] = None

    def init(self):
        """Initialize the model."""
        self.model = LLAMA2Model(checkpoint_path=pathlib.Path(self.ckp_dir))
        self.model.load()
        print("Finish the initialization.")

    def __call__(
        self,
        prefix: str,
        suffix: str,
        selected_code: str,
        instruction: str,
        logger: logging.Logger,
    ) -> str:
        if self.model is None:
            raise TypeError("self.model is None.")
        if self.template_version == "v1":
            template = random.choice(EditPromptTemplate.get_input_templates())
            prefix_last_few_lines = utils_for_str.get_last_n_lines(prefix, 15)
            suffix_first_few_lines = utils_for_str.get_first_n_lines(suffix, 15)
            raw_prompt = template.format(
                prefix=prefix_last_few_lines,
                suffix=suffix_first_few_lines,
                selected_code=selected_code,
                instruction=instruction,
            )
            B_INST, E_INST = "[INST]", "[/INST]"
            input_prompt = f"{B_INST}\n{raw_prompt}\n{E_INST}"
            tokenizer = self.model.tokenizer
            tokens = [tokenizer.bos_id] + tokenizer.tokenize(input_prompt)  # type: ignore
        elif self.template_version == "v2":
            prefix_last_few_lines = utils_for_str.get_last_n_lines(prefix, 60)
            suffix_first_few_lines = utils_for_str.get_first_n_lines(suffix, 60)
            input_template = "<INST>{instruction}<SELECTED>{selected_code}<SUFFIX>{suffix}<PREFIX>{prefix}<UPDATED>"
            prompt = input_template.format(
                prefix=prefix_last_few_lines,
                suffix=suffix_first_few_lines,
                selected_code=selected_code,
                instruction=instruction,
            )
            tokens = self.model.tokenizer.tokenize(prompt)
        else:
            raise TypeError(f"Invalid template version: {self.template_version}.")
        output = self.model.raw_generate(
            tokens, options=GenerationOptions(max_generated_tokens=2048, top_k=0)
        )
        return output


GLOBAL_EDIT_MODEL = EditModel()


@app.route("/chat", methods=["POST"])
def chat():
    """The chat communication func."""
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    try:
        return jsonify({"response": "Not Supported Yet", "status": "success"})
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/edit", methods=["POST"])
def edit():
    """The edit communication func.

    Required fields in the edit mode:
    - selected_code: str
    - instruction: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    selected_code = data.get("selected_code", None)
    instruction = data.get("instruction", None)
    prefix = data.get("prefix", None)
    suffix = data.get("suffix", None)
    selection_range = data.get("selection_range", None)
    logger.info(f"The selection range: {selection_range}")
    if selected_code is None or instruction is None or prefix is None or suffix is None:
        logger.info("Did not find selected_code or instruction or prefix or suffix")
        return jsonify({"response": "", "status": "missing-message-input"})
    try:
        instruction = instruction.strip()
        logger.info(f"instruction: {instruction}")
        logger.info(f"Selected code:\n{selected_code}")
        logger.info("-" * 100)
        response = GLOBAL_EDIT_MODEL(
            prefix=prefix,
            suffix=suffix,
            selected_code=selected_code,
            instruction=instruction,
            logger=logger,
        )
        logger.info(f"Original output:\n{response}")
        logger.info("-" * 100)
        all_parts = utils_for_str.extract_code_within_backticks(
            response, keep_end_new_line_break=True
        )
        if len(all_parts) > 0:
            modified_code = all_parts[0]
            logger.info(
                f"Find backticks quoted codes, and thus use it: {modified_code.encode()}"
            )
        else:
            logger.info("Did not find backticks quoted codes and use all.")
            modified_code = selected_code
        return jsonify(
            {"response": response, "modified_code": modified_code, "status": "success"}
        )
    except BaseException as e:
        print(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


@app.route("/explain", methods=["POST"])
def explain():
    """The explain communication func.

    Required fields in the chat mode:
    - selected_code: str
    - message: str
    """
    data = request.get_json()
    logger.info(f"Receive data with keys: {data.keys()}")
    selected_code = data.get("selected_code", None)
    message: str = data.get("message", "").strip()
    logger.info(f"selected_code: {selected_code}")
    logger.info(f"message: {message}")

    if selected_code is None:
        logger.info("Did not find selected_code")
        return jsonify({"response": "", "status": "missing-message-input"})
    try:
        return jsonify({"response": "Not Supported Yet", "status": "success"})
    except BaseException as e:
        logger.info(f"Fail to call model.generate due to {e}")
        return jsonify({"response": "", "status": f"failed due to {e}"})


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--edit_ckp",
        type=str,
        default="",
        help="The Edit Model Checkpoint.",
    )
    parser.add_argument(
        "--template_version",
        type=str,
        choices=("v1", "v2"),
        default="v1",
        help="The version of the template to use.",
    )
    parser.add_argument(
        "--port",
        type=str,
        default="5000",
        help="The port to send request to this server.",
    )
    args = parser.parse_args()
    GLOBAL_EDIT_MODEL.ckp_dir = args.edit_ckp
    GLOBAL_EDIT_MODEL.template_version = args.template_version
    GLOBAL_EDIT_MODEL.init()
    app.run(host="0.0.0.0", processes=1, threaded=True, debug=False, port=args.port)
