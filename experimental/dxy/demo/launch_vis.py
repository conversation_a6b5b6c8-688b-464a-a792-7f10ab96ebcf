"""Launch a simple HTTP server for visualization.

python experimental/dxy/demo/launch_vis.py --port 8001 --directory /mnt/efs/augment/user/dxy/visualization
python experimental/dxy/demo/launch_vis.py --port 8000 --directory /mnt/efs/augment/user/dxy/visualization --local


python research/tools/export_edit_data/export_html.py \
    --input_dir /mnt/efs/hdc/code-edit-logs/aitutor-pareto-export/ \
    --output_dir /mnt/efs/hdc/code-edit-logs/htmls/aitutor-pareto/


python research/tools/export_edit_data/export_html.py \
    --input_dir /mnt/efs/hdc/code-edit-logs/aitutor-turing-export/ \
    --output_dir /mnt/efs/hdc/code-edit-logs/htmls/aitutor-turing/

ln -s /mnt/efs/hdc/code-edit-logs/htmls /home/<USER>/vis-temp/8000/htmls

python experimental/dxy/demo/launch_vis.py --port 8000 --directory /home/<USER>/vis-temp/8000 --local
python experimental/dxy/demo/launch_vis.py --port 8001 --directory /home/<USER>/vis-temp/8001 --local
"""
import argparse
import http.server
import os
import socketserver

import requests


def get_external_ip():
    try:
        response = requests.get("https://api.ipify.org")
        return response.text
    except requests.RequestException:
        return "Unavailable"


def run_server_with_external_access(port: int, directory: str):
    if not os.path.exists(directory):
        raise ValueError(f"Directory {directory} does not exist.")
    os.chdir(directory)
    handler = http.server.SimpleHTTPRequestHandler

    with socketserver.TCPServer(("", port), handler) as httpd:
        external_ip = get_external_ip()
        print(f"External IP: {external_ip}")
        print(f"Serving HTTP on 0.0.0.0 port {port} (http://0.0.0.0:{port}/)...")
        print(f"Serving HTTP on http://{external_ip}:{port}/")
        httpd.serve_forever()


def run_server_with_local_access(port: int, directory: str):
    if not os.path.exists(directory):
        raise ValueError(f"Directory {directory} does not exist.")
    os.chdir(directory)
    handler = http.server.SimpleHTTPRequestHandler

    # Bind the server to localhost (127.0.0.1) to restrict access to local machine
    with socketserver.TCPServer(("0.0.0.0", port), handler) as httpd:
        print(f"Serving HTTP on 0.0.0.0 port {port} (http://0.0.0.0:{port}/)...")
        httpd.serve_forever()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Simple HTTP Server")
    parser.add_argument("--port", type=int, default=8000, help="Port number")
    parser.add_argument("--directory", default=".", help="Directory to serve")
    parser.add_argument(
        "--local", action="store_true", help="Run server with local access"
    )

    args = parser.parse_args()
    if args.local:
        run_server_with_local_access(args.port, args.directory)
    else:
        run_server_with_external_access(args.port, args.directory)
