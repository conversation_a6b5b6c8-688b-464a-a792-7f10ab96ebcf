{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from termcolor import colored\n", "\n", "URL = \"http://**************:5000\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chat Demo\n", "messages = [\n", "    \"Show me what are u capable of?\",\n", "    \"Give me some trip suggestion to visit Mountain View, CA, USA.\",\n", "]\n", "\n", "for message in messages:\n", "    data = {\"message\": message}\n", "    response = requests.post(f\"{URL}/chat\", json=data, timeout=60.0)\n", "    answer = response.json()[\"response\"]\n", "    print(f\"User: {colored(message, color='green', on_color='on_white')}\")\n", "    print(f\"Assistant: {colored(answer, color='red', on_color='on_white')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Edit and explanation case\n", "selected_code = r\"\"\"always_fim_style: bool = False\n", "\n", "    suffix_first: bool = False\"\"\"\n", "\n", "instruction = \"Change False to True\"\n", "\n", "response = requests.post(\n", "    f\"{URL}/edit\",\n", "    json={\"selected_code\": selected_code, \"instruction\": instruction},\n", "    timeout=60.0,\n", ")\n", "modified_code = response.json()[\"response\"]\n", "print(f\"{colored('Modified code:', color='red')}\\n{modified_code}\")\n", "print(\"\\n\")\n", "\n", "response = requests.post(\n", "    f\"{URL}/explain\",\n", "    json={\n", "        \"selected_code\": selected_code,\n", "        \"instruction\": instruction,\n", "        \"modified_code\": modified_code,\n", "    },\n", "    timeout=60.0,\n", ")\n", "explain = response.json()[\"response\"]\n", "print(f\"Explanation:\\n{colored(explain, color='red', on_color='on_white')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Edit and explanation case\n", "selected_code = r\"\"\"import logging\n", "import argparse\n", "from flask import Flask, request, jsonify\n", "\n", "from research.core import utils_for_log, utils_for_str\n", "from research.core.llama_prompt_formatters import Dialog, CodeLlamaPromptFormatter\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "from research.models.remote_models import CodeLLaMA_LLaMACPP_Model\n", "\n", "...\"\"\"\n", "\n", "instruction = \"The isort library raised this error info 'Imports are incorrectly sorted and/or formatted.' for the following codes, try your best to fix its order issue.\"\n", "\n", "response = requests.post(\n", "    f\"{URL}/edit\",\n", "    json={\"selected_code\": selected_code, \"instruction\": instruction},\n", "    timeout=60.0,\n", ")\n", "modified_code = response.json()[\"response\"]\n", "print(f\"{colored('Modified code:', color='red')}\\n{modified_code}\")\n", "print(\"\\n\")\n", "\n", "response = requests.post(\n", "    f\"{URL}/explain\",\n", "    json={\n", "        \"selected_code\": selected_code,\n", "        \"instruction\": instruction,\n", "        \"modified_code\": modified_code,\n", "    },\n", "    timeout=60.0,\n", ")\n", "explain = response.json()[\"response\"]\n", "print(f\"Explanation:\\n{colored(explain, color='red', on_color='on_white')}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import hashlib\n", "from research.core.types import Document\n", "from research.retrieval.chunking_functions import LineLevelChunker\n", "from research.retrieval.prompt_formatters import SimpleQueryFormatter\n", "from research.retrieval.scorers.good_enough_bm25_scorer import (\n", "    GoodEnoughBM25Scorer,\n", ")\n", "from research.retrieval.retrieval_database import RetrievalDatabase\n", "from research.retrieval.file_filterer import basic_file_filterer\n", "\n", "\n", "def doc_to_id(text: str) -> str:\n", "    digester = hashlib.sha256()\n", "    digester.update(bytearray(text, \"utf8\"))\n", "    return digester.hexdigest()\n", "\n", "\n", "def get_docs_from_files(path: pathlib.Path, extensions: list[str]) -> list[Document]:\n", "    \"\"\"Add files from given dir to index.\"\"\"\n", "    docs = []\n", "    num_files = 0\n", "    num_lines = 0\n", "    print(f\"Processing corpus at {path}, extensions: {extensions}\")\n", "    for extension in extensions:\n", "        if not extension.startswith(\".\"):\n", "            raise ValueError(f\"Extension doesn't start with .: {extension}\")\n", "        pattern = f\"*{extension}\"\n", "        for filepath in pathlib.Path(path).rglob(pattern):\n", "            num_files += 1\n", "            with filepath.open(\"r\", encoding=\"utf8\") as file:\n", "                text = file.read()\n", "                num_lines += len(text.split(\"\\n\"))\n", "                doc = Document(\n", "                    text=text,\n", "                    id=doc_to_id(text),\n", "                    path=str(filepath.relative_to(path)),\n", "                )\n", "                docs.append(doc)\n", "    print(f\"Corpus contains {num_files} files with {num_lines} lines\")\n", "    return docs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.model_input import ModelInput\n", "chunker = LineLevelChunker(max_lines_per_chunk=25)\n", "query_formatter = SimpleQueryFormatter(max_lines=10, add_sos=False)\n", "scorer = GoodEnoughBM25Scorer(query_formatter)\n", "retriever = RetrievalDatabase(chunker, scorer, basic_file_filterer)\n", "\n", "docs = get_docs_from_files(pathlib.Path(\"/home/<USER>/src/augment\"), [\".py\"])\n", "retriever.add_docs(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["retrieved_chunks, _ = retriever.query(\n", "                ModelInput(\n", "                    prefix=\"the optional_map function\",\n", "                    suffix=\"\",\n", "                    path=\"\",\n", "                ),\n", "                top_k=3,\n", "            )\n", "for chunk in retrieved_chunks:\n", "    print(\"-\" * 100)\n", "    print(chunk.text)\n", "    chunk.parent_doc.path"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}