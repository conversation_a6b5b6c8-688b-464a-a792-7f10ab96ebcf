{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor in fp32: tensor([-1.1016, -1.3594]), s_fp32: -2.4609375\n", "\n", "2Sum algorithm:\n", "s in torch.bfloat16: -2.46875, error in torch.bfloat16: 0.0078125\n", "s_bf16 + error in fp32: -2.4609375\n", "\n", "Fast2Sum algorithm:\n", "s in torch.bfloat16: -2.46875, error in torch.bfloat16: 0.0078125\n", "s_bf16 + error in fp32: -2.4609375\n"]}], "source": ["import torch\n", "\n", "t_bf16 = torch.randn(2, dtype=torch.float32).bfloat16()\n", "t_fp32 = t_bf16.float()\n", "s_fp32 = t_fp32[0] + t_fp32[1]\n", "print(f\"tensor in fp32: {t_fp32}, s_fp32: {s_fp32}\\n\")\n", "\n", "# the 2Sum algorithm\n", "s_bf16 = t_bf16[0] + t_bf16[1]\n", "a_p = s_bf16 - t_bf16[1]\n", "b_p = s_bf16 - a_p\n", "delta_a = t_bf16[0] - a_p\n", "delta_b = t_bf16[1] - b_p\n", "error = delta_a + delta_b\n", "print(\"2Sum algorithm:\")\n", "print(f\"s in {s_bf16.dtype}: {s_bf16}, error in {error.dtype}: {error}\")\n", "print(f\"s_bf16 + error in fp32: {s_bf16.float() + error.float()}\\n\")\n", "\n", "# the Fast2Sum\n", "s_bf16 = t_bf16[0] + t_bf16[1]\n", "a, b = s_bf16.max(), s_bf16.min()\n", "z = s_bf16 - a\n", "error = b - z\n", "print(\"Fast2Sum algorithm:\")\n", "print(f\"s in {s_bf16.dtype}: {s_bf16}, error in {error.dtype}: {error}\")\n", "print(f\"s_bf16 + error in fp32: {s_bf16.float() + error.float()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = \"\"\"\n", "import os\n", "\n", "import pytest\n", "\n", "pytestmark = pytest.mark.skipif(not os.getenv('TEST_PLUGIN'), reason='Test only with `TEST_PLUGIN` env var set.')\n", "\n", "\n", "def test_plugin_usage():\n", "    from pydantic import BaseModel\n", "\n", "    class MyModel(BaseModel):\n", "        x: int\n", "        y: str\n", "\n", "    m = MyModel(x='10', y='hello')\n", "    assert m.x == 10\n", "    assert m.y == 'hello'\n", "\n", "    from example_plugin import log\n", "\n", "    assert log == [\n", "        \"on_enter args=({'x': '10', 'y': 'hello'},) kwargs={'self_instance': MyModel()}\",\n", "        \"on_success result=x=10 y='hello'\",\n", "    ]\"\"\"\n", "print(len(x))"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}