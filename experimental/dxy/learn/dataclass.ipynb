{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["The default api for DataClassJsonMixin will fail unexpectedly when the dataclass has a field with a union type. See details in the following code.\n", "The obj1's `x` field is of type `B`, and after serialization and deserialization with the default api, `obj2`'s `x` field is of type `A`."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X(x=B(a=1, b=2, c=3))\n", "{'x': {'a': 1, 'b': 2, 'c': 3}}\n", "X(x=A(a=1, c=3))\n"]}], "source": ["import dataclasses\n", "from dataclasses_json import DataClassJsonMixin\n", "\n", "@dataclasses.dataclass\n", "class A(DataClassJsonMixin):\n", "    a: int\n", "    c: int\n", "\n", "@dataclasses.dataclass\n", "class B(DataClassJsonMixin):\n", "    a: int\n", "    b: int\n", "    c: int\n", "\n", "@dataclasses.dataclass\n", "class X(DataClassJsonMixin):\n", "    x: A | B | None = None\n", "\n", "obj1 = X(x=B(a=1, b=2, c=3))\n", "print(obj1)\n", "json_dict = obj1.to_dict()\n", "print(json_dict)\n", "\n", "obj2 = X.from_dict(json_dict)\n", "print(obj2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we use the \"schema\" interface, the result will be correct as it also records the type of the field."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X(x=B(a=1, b=2, c=3))\n", "{'x': {'a': 1, 'b': 2, 'c': 3, '__type': 'B'}}\n", "X(x=B(a=1, b=2, c=3))\n"]}], "source": ["obj1 = X(x=B(a=1, b=2, c=3))\n", "print(obj1)\n", "json_dict = X.schema().dump(obj1)\n", "print(json_dict)\n", "\n", "obj2 = X.schema().load(json_dict)\n", "print(obj2)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}