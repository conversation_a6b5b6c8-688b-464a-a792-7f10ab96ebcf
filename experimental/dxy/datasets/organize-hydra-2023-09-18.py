"""Re-organize all the raw data into the HydraTask format.

python organize-hydra-2023-09-18.py
python experimental/dxy/datasets/organize-hydra-2023-09-18.py
"""

from __future__ import annotations

import dataclasses
import pathlib
import random

import torch

from research.eval.harness.tasks import hydra_task_v2
from research.eval.harness.utils import write_json, write_jsonl_zst
from research.static_analysis.experimental_parsing import SimpleNode


def chech_eq_and_return(list_a, list_b):
    assert len(list_a) == len(list_b)
    for x, y in zip(list_a, list_b):
        assert type(x) == type(y)
        if isinstance(x, (str, int, float)):
            assert x == y
    return list_a


def main(size: str = "small", version: str = "v1.0"):
    """The real main function."""

    root_output_dir = pathlib.Path(
        "/mnt/efs/augment/data/eval/hydra/datasets/FineGrained/"
    )

    output_dir = root_output_dir / f"FinePython.{size}.{version}"

    abbr_org_repo_path = [
        (
            "pyglove",
            "google",
            "pyglove",
            pathlib.Path(
                "/mnt/efs/augment/user/dxy/data-patches-v5/google_pyglove-v1.0"
            ),
        ),
        (
            "pydantic",
            "pydantic",
            "pydantic",
            pathlib.Path(
                "/mnt/efs/augment/user/dxy/data-patches-v5/pydantic_pydantic-v1.0"
            ),
        ),
    ]
    meta_info = []
    for abbr, organization, repo, xdir in abbr_org_repo_path:
        if size == "small":
            num_api, num_interface, num_mstr = 60, 30, 20
        elif size == "medium":
            num_api, num_interface, num_mstr = 200, 60, 40
        elif size == "large":
            num_api, num_interface, num_mstr = 600, 200, 100
        else:
            raise TypeError(f"Unknown size : {size}")
        api_data_path = (
            xdir
            / "data-for-api-final-nodes"
            / "dataset"
            / f"{num_api:03d}-results.torch"
        )
        interface_data_path = (
            xdir
            / "data-for-interface-final-nodes"
            / "dataset"
            / f"{num_interface:03d}-results.torch"
        )
        meanstr_data_path = xdir / "data-for-meaningfulstr-post-sample.torch"
        assert api_data_path.exists()
        assert interface_data_path.exists(), interface_data_path
        assert meanstr_data_path.exists(), meanstr_data_path
        api_raw_data = torch.load(api_data_path)
        interface_raw_data = torch.load(interface_data_path)
        meanstr_raw_data = torch.load(meanstr_data_path)
        # Check image names
        image_name = api_raw_data["image_name"]
        nodes_for_api: list[SimpleNode] = api_raw_data["nodes"]
        for node in nodes_for_api:
            assert node.image_name == image_name
        nodes_for_interface: list[SimpleNode] = interface_raw_data["nodes"]
        for node in nodes_for_interface:
            assert node.image_name == image_name
        # Build the meta information
        meta_info.append(
            dict(
                repo_abbreviation=abbr,
                organization=organization,
                repository=repo,
                docker_image_name=image_name,
            )
        )
        # Save the retrieval DB
        repo_for_system = chech_eq_and_return(
            api_raw_data["repo_for_system"], interface_raw_data["repo_for_system"]
        )
        target_retrieval_db_dir: pathlib.Path = output_dir / organization

        target_retrieval_db_dir.mkdir(parents=True, exist_ok=True)
        target_retrieval_db_path = (
            target_retrieval_db_dir / f"{repo}_retrieval_db.jsonl"
        )
        write_jsonl_zst(
            target_retrieval_db_path,
            [dataclasses.asdict(doc) for doc in repo_for_system],
        )
        print(f"Save {len(repo_for_system)} into {target_retrieval_db_path}")

        # Save the patches
        # Save the patches: (1) collect nodes for api
        all_nodes = []
        nodes_for_meaningfulstr = meanstr_raw_data["nodes"]
        random.shuffle(nodes_for_meaningfulstr)
        for node in nodes_for_api:
            if node.pipeline_history[-1] == "sample_arguments":
                sub_categories = ["api_call@arguments"]
            elif node.pipeline_history[-1] in (
                "identical",
                "sample_over_surrounded_spaces",
                "sample_over_leading_newline",
            ):
                sub_categories = ["api_call@missing_func_name"]
            elif node.pipeline_history[-1] == "sample_random_symbols":
                sub_categories = ["api_call@unknown"]
            else:
                raise ValueError(f"Unknown pipeline: {node.pipeline_history}")

            mini_patch = hydra_task_v2.MiniPatch(
                char_start=node.crange.start,
                char_stop=node.crange.stop,
                filename=node.filename,
                repository=abbr,
            )
            hydra_tag = hydra_task_v2.HydraTag(
                language="python",
                category="api_call",
                sub_categories=sub_categories,
            )
            all_nodes.append(
                {**dataclasses.asdict(mini_patch), **dataclasses.asdict(hydra_tag)}
            )
        # Save the patches: (2) collect nodes for interface
        for node in nodes_for_interface:
            if node.pipeline_history[0] == "interface:func_head":
                sub_categories = ["interface@func_head"]
            elif node.pipeline_history[0] == "interface:class_head":
                sub_categories = ["interface@class_head"]
            else:
                raise ValueError(f"Unknown pipeline: {node.pipeline_history}")
            mini_patch = hydra_task_v2.MiniPatch(
                char_start=node.crange.start,
                char_stop=node.crange.stop,
                filename=node.filename,
                repository=abbr,
            )
            hydra_tag = hydra_task_v2.HydraTag(
                language="python",
                category="interface",
                sub_categories=sub_categories,
            )
            all_nodes.append(
                {**dataclasses.asdict(mini_patch), **dataclasses.asdict(hydra_tag)}
            )
        # Save the patches: (3) collect meaningful string
        for node in nodes_for_meaningfulstr[:num_mstr]:
            sub_categories = [node.pipeline_history[0].replace(":", "@")]
            assert len(node.pipeline_history) == 2
            if node.pipeline_history[-1] == "sample_args_in_docstr":
                sub_categories.append("meaningful_str@args_in_func_docstr")
            elif node.pipeline_history[-1] == "sample_space_in_comment":
                sub_categories.append("meaningful_str@comments")
            elif node.pipeline_history[-1] == "sample_char_in_class_docstr":
                sub_categories.append("meaningful_str@class_docstr")
            else:
                raise ValueError(f"Unknown pipeline: {node.pipeline_history}")
            mini_patch = hydra_task_v2.MiniPatch(
                char_start=node.crange.start,
                char_stop=node.crange.stop,
                filename=node.filename,
                repository=abbr,
            )
            hydra_tag = hydra_task_v2.HydraTag(
                language="python",
                category="meaningful_str",
                sub_categories=sub_categories,
            )
            all_nodes.append(
                {**dataclasses.asdict(mini_patch), **dataclasses.asdict(hydra_tag)}
            )

        # Save the patches
        target_patches_dir: pathlib.Path = output_dir / organization
        target_patches_dir.mkdir(parents=True, exist_ok=True)
        target_patches_path = target_patches_dir / f"{repo}_patches.jsonl"
        write_jsonl_zst(
            target_patches_path,
            all_nodes,
        )
        print(f"Save {len(all_nodes)}patches into {target_patches_path}")
    write_json(output_dir / "meta.json", meta_info, indent=2)


if __name__ == "__main__":
    main("small")
    main("medium")
    main("large")
