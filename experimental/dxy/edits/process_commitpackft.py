"""Process the CommitPack FT data.

python process_commitpackft.py
"""
import dataclasses
import pathlib

import tqdm

from experimental.dxy.edits import data_type
from research.core import utils_for_file


def main():
    repo_data_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/edit.raw/commitpackft"
    )
    assert repo_data_dir.exists()
    print("Processing commitpackft data...")
    data_dir = repo_data_dir / "data"
    assert data_dir.exists

    output_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/edit.processed/commitpackft"
    )
    output_dir.mkdir(exist_ok=True, parents=True)

    # load all the sub folders
    sub_folders = []
    for sub_folder in data_dir.iterdir():
        if sub_folder.is_dir():
            sub_folders.append(sub_folder)
    print(f"Found {len(sub_folders)} sub folders")
    all_data: list[data_type.EditData] = []
    skipped = 0
    for sub_folder in tqdm.tqdm(sub_folders, total=len(sub_folders)):
        data_jsonl_path = sub_folder / "data.jsonl"
        data_list: list[data_type.EditData] = []
        for x in utils_for_file.read_jsonl(data_jsonl_path):
            edit_data = data_type.convert_commitpackft_to_edit(x)
            if edit_data is None:
                skipped += 1
            else:
                data_list.append(edit_data)
        # Save the data
        target_file = output_dir / f"{sub_folder.name}.jsonl.zst"
        utils_for_file.write_jsonl_zst(
            target_file, [dataclasses.asdict(x) for x in data_list]
        )
        print(f"Saved {len(data_list)} examples into {target_file}")
        all_data.extend(data_list)
    output_path = output_dir.with_suffix(".all.jsonl.zst")
    utils_for_file.write_jsonl_zst(
        output_path, [dataclasses.asdict(x) for x in all_data]
    )
    print(f"Saved {len(all_data)} examples into {output_path}")


if __name__ == "__main__":
    main()
