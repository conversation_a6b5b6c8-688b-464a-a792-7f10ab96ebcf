"""Prepare Data for Edit.

python analyze_data.py --save /home/<USER>/cache/results-10-04
python experimental/dxy/edits/analyze_data.py --save /home/<USER>/cache/results-10-04
"""
import argparse
import pathlib
import random
import typing
from concurrent.futures import ThreadPoolExecutor

import matplotlib
import matplotlib.markers as markers
import matplotlib.pyplot as plt
import numpy as np
import pyglove as pg
import tqdm
from scipy.stats import gaussian_kde

from experimental.dxy.edits import data_type

matplotlib.use("Agg")


def visualize_fn(
    data: typing.List[data_type.EditData], save_dir: pathlib.Path, filesuffix: str = ""
):
    """Visualize the edit data."""
    print(f"There are {len(data)} data fed into the visualize_fn function.")
    deletes, adds = [], []
    for edit_data in data:
        delete, add = edit_data.get_diffs()
        deletes.append(delete)
        adds.append(add)
    plt.scatter(
        deletes, adds, color="blue", marker=markers.MarkerStyle("o"), label="diffs"
    )
    plt.title("Distribution of Diff")
    plt.xlabel("#deleted lines")
    plt.ylabel("#added lines")
    plt.legend()
    plt.grid(True)
    save_path = save_dir / f"line-diff-dist{filesuffix}.pdf"
    plt.savefig(save_path, format="pdf")
    print(f"Save into {save_path}")
    plt.close()

    plt.hexbin(deletes, adds, gridsize=50, cmap="Blues")
    plt.title("Hexbin Plot of Data Distribution")
    plt.xlabel("#deleted lines")
    plt.ylabel("#added lines")
    save_path = save_dir / f"line-diff-hexbin{filesuffix}.pdf"
    plt.savefig(save_path, format="pdf")
    print(f"Save into {save_path}")
    plt.close()

    # Create a grid to evaluate KDE
    x, y = np.array(deletes), np.array(adds)
    x_grid, y_grid = np.mgrid[x.min() : x.max() : 100j, y.min() : y.max() : 100j]
    positions = np.vstack([x_grid.ravel(), y_grid.ravel()])
    values = np.vstack([x, y])
    # Perform KDE
    kernel = gaussian_kde(values)
    z = kernel(positions).reshape(x_grid.shape)
    # Plot the estimated density in 3D
    fig = plt.figure()
    ax = fig.add_subplot(111, projection="3d")
    ax.plot_surface(x_grid, y_grid, z, cmap="Blues")
    ax.set_xlabel("#deleted lines")
    ax.set_ylabel("#added lines")
    ax.set_zlabel("Density")
    plt.title("Kernel Density Estimation in 3D")
    save_path = save_dir / f"line-diff-3D-KDE{filesuffix}.pdf"
    plt.savefig(save_path, format="pdf")
    print(f"Save into {save_path}")
    plt.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--save",
        type=str,
        required=True,
        help="",
    )
    args = parser.parse_args()

    save_dir = pathlib.Path(args.save)
    data_dir = pathlib.Path("/home/<USER>/data/edit-2023-10-04")
    json_files = sorted(list(data_dir.glob("*-*.json")), key=lambda x: str(x))
    json_files = [str(x) for x in json_files]
    json_files = random.sample(json_files, 1000)
    print(f"There are {len(json_files)} files in total.")
    pbar = tqdm.tqdm(total=len(json_files))

    def update(*_):
        pbar.update()

    futures = []
    with ThreadPoolExecutor(max_workers=32) as executor:
        for chunk in json_files:
            future = executor.submit(pg.load, chunk)
            future.add_done_callback(update)
            futures.append(future)
    pbar.close()

    all_data: typing.List[data_type.EditData] = [future.result() for future in futures]
    print(f"There are {len(all_data)} examples in total.")

    visualize_fn(all_data, save_dir, "-all")
    visualize_fn(
        data_type.remove_editdata_via_fn(
            all_data, lambda x: x.get_diffs()[0] >= 50 or x.get_diffs()[1] >= 50
        ),
        save_dir,
        "-le50",
    )
    visualize_fn(
        data_type.remove_editdata_via_fn(
            all_data, lambda x: x.get_diffs()[0] >= 10 or x.get_diffs()[1] >= 10
        ),
        save_dir,
        "-le10",
    )
