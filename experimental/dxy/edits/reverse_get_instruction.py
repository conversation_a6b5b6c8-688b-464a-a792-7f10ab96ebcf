"""Helper function to reversely generate the instructions."""
from termcolor import colored

import experimental.dxy.edits.api_lib as api_lib
import experimental.dxy.edits.data_type as data_type
import research.core.utils_for_str as str_lib

system_prompt = r"""You are helpful and super smart programing expert that can solve any any coding-related problems reasonable and correctly."""

typical_instructions = [
    "put a nice docstring to well explain this class",
    "use promise chain instead of async",
    "extend it to else condition",
    "fix errors",
    "move the accesstoken to url as a parameter of token=",
    "make this line more readable",
    "generate floats, not only ints",
    "add a param to choose upper/lower",
    "change all command names to snake case",
    "replace True by False",
    "this function seems will never trigger the default branch of executeChat, fix it",
    "refactor this function by using optional_map func",
    "simplify the cache control",
    "console log all the ids of each file",
    "Refactor the code. Change config.foo to CONFIG['foo']",
    "change Optional[str] to str | None",
    "write test train split using df pandas",
    "instead of hard code options in the quick pick array, randomly generate 10 numbers as options",
    "write a short pytorch module that implements linear regression, have comments",
    "polish it with concise codes",
]
typical_instruct_str = "\n".join(f"- {x}" for x in typical_instructions)


def build_messages_v0(
    selected_code: str, modified_code: str, prefix: str, suffix: str
) -> list[str]:
    messages = []
    messages.append(
        """Let's imagine a scenario: there is an AI assistant helping users to write the Python code.
The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code."""
    )
    messages.append("Ok, I understand this scenario, what do you want me to do?")
    messages.append(
        f"""Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.
Can you guess what is the user's original instruction?

Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:
{typical_instruct_str}

When you guess his instruction, please follow these requirements:
- (1) try your best to simulate his tone;
- (2) produce the instruction vague but not too vague naturely like a human, for example,
-- (2.1) "Add the import for urlquote from django.utils.http" can be simplified as "add the import of urlquote";
- (3) produce the instruction short and brief;
- (4) Note that an empty target is "", and if the updated version is a new line with empty content, they are different because the AI assistant adds a new line;
- (5) in your output, please quote the instruction by ```` and ````."""
    )
    messages.append(
        "Sure, I will guess, what is the entire codes, and the target piece of codes?"
    )
    prefix_last_15_lines = str_lib.get_last_n_lines(prefix, 15)
    messages.append(
        f"""Here is the preceding part of the target piece of codes:
```
{prefix_last_15_lines}```"""
    )
    messages.append(
        "Got it, what is the target piece of codes and its possible succeeding part?"
    )
    if selected_code:
        messages.append(
            f"""Here is the target piece of codes:
```
{selected_code}```"""
        )
    else:
        messages.append(
            """The target piece of code is empty, and thus the instruction will be about add something."""
        )
    messages.append("Got it, what is its succeeding part and the final updated codes?")
    suffix_first_15_lines = str_lib.get_first_n_lines(suffix, 15)
    messages.append(
        f"""Here is the succeeding part of the target piece of codes:
```
{suffix_first_15_lines}
```"""
    )
    messages.append("Gotcha, what is the updated version?")
    messages.append(
        f"""Here is the updated version
```
{modified_code}```
"""
    )
    return messages


def predict_edit_data_v0(
    x: data_type.EditData,
    model: api_lib.OpenAIChatModels = "gpt-3.5-turbo-1106",
    debug: bool = True,
) -> str:
    selected_code: str = x.get_selected_code()
    modified_code: str = x.get_modified_code()
    lrange = x.get_lrange()
    prefix = x.get_prefix_via_lrange(lrange=lrange)
    prefix_last_15_lines = str_lib.get_last_n_lines(prefix, 15)
    suffix = x.get_suffix_via_lrange(lrange=lrange)
    suffix_first_15_lines = str_lib.get_first_n_lines(suffix, 15)
    messages = build_messages_v0(
        selected_code, modified_code, prefix=prefix, suffix=suffix
    )
    instructions = api_lib.generate_response_via_chat(
        messages, system_prompt, model=model
    )
    if debug:
        print("-" * 100)
        print(f"There are {len(messages)} messages.")
        for index, message in enumerate(messages):
            print(f"[the {index:02d}-th message] {message}")
        print("-" * 30 + " Selected Code " + "-" * 30)
        print(prefix_last_15_lines, end="")
        if selected_code:
            print(colored(selected_code, color="blue"), end="")
        else:
            print(colored("<<|empty-place-holder|>>", color="blue"), end="")
        print(suffix_first_15_lines, end="")
        print("-" * 100)
        print("-" * 30 + " Modified Code " + "-" * 30)
        print(prefix_last_15_lines, end="")
        print(colored(modified_code, color="red"), end="")
        print(suffix_first_15_lines, end="")
        print("-" * 100)
        print(colored(instructions[0], color="blue"))
        print("-" * 100)
    return instructions[0]


def predict_edit_data_v1(
    x: data_type.EditData,
    model: api_lib.OpenAIChatModels = "gpt-3.5-turbo-1106",
    debug: bool = True,
) -> str:
    """The version 1.0 of predicting the instruction of an edit data."""
    codes = x.get_diff_code_in_context(15, 15)
    messages = []
    messages.append(
        """Let's imagine a scenario: there is an AI assistant helping users to write the Python code.
The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code."""
    )
    messages.append("Ok, I understand this scenario, what do you want me to do?")
    messages.append(
        f"""Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.
Can you guess what is the user's original instruction? By the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:
{typical_instruct_str}

When you guess his instruction, please follow these requirements:
- (1) try your best to simulate his tone;
- (2) produce the instruction vague but not too vague naturely like a human, for example,
-- (2.1) "Add the import for urlquote from django.utils.http" can be simplified as "add the import of urlquote";
- (3) produce the instruction short and brief;
- (4) in your output, please quote the instruction by ```` and ````."""
    )
    messages.append("Sure, I will guess, show me the codes.")
    messages.append(
        f"""Here are all the codes with a prefix of `[ ]`, `[-]`, `[+]` at the head of each line, where:
- `[ ]` indicates the AI assistant does not change that line.
- `[-]` indicates the AI assistant deletes that line.
- `[+]` indicates the AI assistant adds that line.

```
{codes}```"""
    )
    instructions = api_lib.generate_response_via_chat(
        messages, system_prompt, model=model
    )
    if debug:
        print("-" * 100)
        print(f"There are {len(messages)} messages.")
        for index, message in enumerate(messages):
            print(f"[the {index:02d}-th message] {message}")
        print("-" * 100)
        print(codes)
        print("-" * 100)
        print(colored(instructions[0], color="blue"))
        print("-" * 100)
    return instructions[0]
