"""Prepare data for the edit modeling.

python experimental/dxy/edits/prepare_data.py --mode check --date 2023-10-22
python experimental/dxy/edits/prepare_data.py --mode download --date 2023-10-22
python experimental/dxy/edits/prepare_data.py --mode filter_df --date 2023-10-22
python experimental/dxy/edits/prepare_data.py --mode convert --date 2023-10-22
[Not Ready] python experimental/dxy/edits/prepare_data.py --mode filter_json --date 2023-10-22

Info (updated on 22 Oct 2023):
- [Python] There are 9,949,948 joined hunks, and left 1.53M after dedup and filter length.
"""
import argparse
import dataclasses
import hashlib
import json
import pathlib
import typing

import numpy as np
import pandas as pd
import pyspark.sql.functions as F
import pyspark.sql.pandas.functions as FF
import tqdm
from pyspark.sql.types import IntegerType, StringType, StructField, StructType

from experimental.dxy.edits import data_type
from research.core import utils_for_dataclass, utils_for_file, utils_for_log
from research.data.spark import k8s_session


def create_spark(max_workers: int = 100):
    spark = k8s_session(
        max_workers=max_workers,
        conf={
            "spark.sql.parquet.columnarReaderBatchSize": "64",
            "spark.sql.execution.arrow.maxRecordsPerBatch": "128",
            "spark.task.maxFailures": "10",
        },
    )
    return spark


def check_raw_data(date: str, root_s3_dir: str = "s3a://dxy-dev-bucket/edit"):
    """Check the raw data."""
    spark = create_spark()
    print(f"The current date: {date}")
    # hunk_raw_data_dir = "s3a://augment-github/hunk/converted/"
    hunk_raw_data_dir = "s3a://augment-github/hunk/delta/"
    print(f"Read from {hunk_raw_data_dir}")
    hunks_df = (
        spark.read.format("delta")
        .option("mode", "DROPMALFORMED")
        .load(hunk_raw_data_dir)
        .withColumn(
            "total_length",
            F.expr(
                "aggregate(transform(lines, x -> length(x.content)), 0, (a, x) -> a + x)"
            ),
        )
    )
    hunks_df.printSchema()
    print(f"The hunk data has {hunks_df.count()/1e6:7.2f}M examples.")

    deduped_hunks_df = hunks_df.filter(hunks_df.total_length < 1e6).dropDuplicates(
        ["old_file_sha", "old_start", "old_lines"]
    )
    # deduped_hunk_path = f"{root_s3_dir}/raw_deduped2v_hunks_{date}/"
    # print(f"Try to dump into {deduped_hunk_path}")
    # deduped_hunks_df.write.mode("overwrite").parquet(deduped_hunk_path)
    print(
        f"The hunk data has {deduped_hunks_df.count()/1e6:7.2f}M examples after deduplication."
    )

    files_df = spark.read.parquet(
        "s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/"
    )
    files_df.printSchema()
    print(f"The file data has {files_df.count()/1e6:7.2f}M examples.")
    deduped_files_df = files_df.dropDuplicates(["max_stars_repo_path"])
    print(
        f"The file data has {deduped_files_df.count()/1e6:7.2f}M examples after deduplication."
    )


def download_raw_data(date: str, root_s3_dir: str = "s3a://dxy-dev-bucket/edit"):
    """Download the raw data from the S3 bucket."""
    logger = utils_for_log.create_logger(__file__)
    spark = create_spark()

    # hunk_raw_data_dir = "s3a://augment-github/hunk/delta/"
    # print(f"Read from {hunk_raw_data_dir}")
    # hunks_df = spark.read.format("delta").load(hunk_raw_data_dir)
    hunk_raw_data_dir = "s3a://augment-github/hunk/filtered_python/"
    print(f"Read from {hunk_raw_data_dir}")
    hunks_df = spark.read.parquet(hunk_raw_data_dir)

    hunks_df = hunks_df.withColumn(
        "total_length",
        F.expr(
            "aggregate(transform(lines, x -> length(x.content)), 0, (a, x) -> a + x)"
        ),
    )
    logger.info(f"Load the hunk data from {hunk_raw_data_dir}.")

    files_df = spark.read.parquet(
        "s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/"
    )
    files_df.printSchema()

    old_files = files_df.filter(F.length("content") < 1e6).select(
        F.col("id").alias("old_file_sha"),
        F.col("max_stars_repo_name").alias("repo"),
        F.col("max_stars_repo_path").alias("old_path_name"),
        F.col("content").alias("old_content"),
    )
    logger.info("The schema for the old_files variable:")
    old_files.printSchema()

    path_1st_join = f"{root_s3_dir}/joined_hunks_x1st_{date}/"
    df_1st_join = hunks_df.join(old_files, "old_file_sha")
    logger.info(f"Try to dump into {path_1st_join}.")
    df_1st_join.write.mode("overwrite").parquet(path_1st_join)
    df_1st_join = spark.read.parquet(path_1st_join)
    logger.info(f"The df_1st_join DF has {df_1st_join.count()} examples")

    new_files = files_df.select(
        F.col("id").alias("new_file_sha"),
        F.col("max_stars_repo_path").alias("new_path_name"),
        F.col("content").alias("new_content"),
    ).filter(F.length("new_content") < 1e6)

    new_files = files_df.select(
        F.col("id").alias("new_file_sha"),
        F.col("max_stars_repo_path").alias("new_path_name"),
        F.col("content").alias("new_content"),
    ).filter(F.length("new_content") < 1e6)

    df_joined = df_1st_join.join(new_files, "new_file_sha")
    s3final_path = f"{root_s3_dir}/joined_hunks_final_{date}/"
    logger.info(f"Try to dump into {s3final_path}.")
    df_joined.write.mode("overwrite").parquet(s3final_path)
    df_joined = spark.read.parquet(s3final_path)
    logger.info(f"Have saved {df_joined.count()} examples into {s3final_path}")

    # # Count the distinct commit_sha
    # num_distinct_commit_sha = df.select("commit_sha").distinct().count()
    # logger.info(f"There are {num_distinct_commit_sha} distinct commit_sha.")

    # Drop the duplicated hunks based on old_file_sha, old_start, old_lines.
    deduped_join_hunk = df_joined.filter(df_joined.total_length < 5e5).dropDuplicates(
        ["old_file_sha", "old_start", "old_lines"]
    )
    s3final_path = f"{root_s3_dir}/joined_hunks_deduped_{date}/"
    deduped_join_hunk.write.mode("overwrite").parquet(s3final_path)
    deduped_join_hunk = spark.read.parquet(s3final_path)
    logger.info(
        f"Save {deduped_join_hunk.count()/1e6:.2f}M deduped enriched hunks into {s3final_path}"
    )


@FF.pandas_udf(
    StructType(
        [
            StructField("json_str", StringType(), True),
            StructField("del", IntegerType(), True),
            StructField("add", IntegerType(), True),
            StructField("hash_of_lines", StringType(), True),
        ]
    ),
    FF.PandasUDFType.SCALAR,
)
def convert_to_json(
    repo: pd.Series,
    commit_sha: pd.Series,
    new_path_name: pd.Series,
    lines: pd.Series,
    old_content: pd.Series,
    new_content: pd.Series,
    old_start: pd.Series,
    old_lines: pd.Series,
    new_start: pd.Series,
    new_lines: pd.Series,
) -> pd.DataFrame:
    """Convert the dataframe to JSON."""

    def _rows2lines(xlines: typing.List[pd.Series]) -> typing.List[data_type.Line]:
        return [data_type.Line(line["content"], line["origin"]) for line in xlines]

    json_str_list, del_list, add_list, hash_of_lines = (
        [],
        [],
        [],
        [],
    )
    for idx in range(len(repo)):
        data = data_type.EditData(
            repo=repo.iloc[idx],
            commit_sha=commit_sha.iloc[idx],
            file_path=new_path_name.iloc[idx],
            lines=_rows2lines(lines.iloc[idx]),
            old_file_content=old_content.iloc[idx],
            new_file_content=new_content.iloc[idx],
            old_hunk_range=(
                int(old_start.iloc[idx]),
                int(old_start.iloc[idx] + old_lines.iloc[idx]),
            ),
            new_hunk_range=(
                int(new_start.iloc[idx]),
                int(new_start.iloc[idx] + new_lines.iloc[idx]),
            ),
        )
        del_diff, add_diff = data.get_diffs()

        data_dict = dataclasses.asdict(data)
        json_str_list.append(json.dumps(data_dict))
        del_list.append(del_diff)
        add_list.append(add_diff)
        content = (
            "".join([x.content for x in data.lines])
            .replace("\n", "")
            .replace(" ", "")
            .replace("\t", "")
            .replace("\r", "")
            .lower()
        )
        hash_of_lines.append(hashlib.sha256(content.encode()).hexdigest())
    return pd.DataFrame(
        {
            "json_str": json_str_list,
            "del": del_list,
            "add": add_list,
            "hash_of_lines": hash_of_lines,
        }
    )


def filter_df_data(
    df_path: str,
    date: str,
    root_s3_dir: str = "s3a://dxy-dev-bucket/edit",
):
    """Filter the spark data and save into the target directory."""
    logger = utils_for_log.create_logger(__file__)
    spark = create_spark()

    logger.info(f"Load the source data from {df_path}.")
    df = spark.read.parquet(df_path).repartition(1000)
    df.printSchema()

    # Filter the data by file suffix and char length.
    filtered_df = df.filter(F.col("new_path_name").endswith(".py")).filter(
        df.total_length > 8
    )

    # Apply the transformation and filter
    final_df = filtered_df.withColumn(
        "convert_output",
        convert_to_json(
            F.col("repo"),
            F.col("commit_sha"),
            F.col("new_path_name"),
            F.col("lines"),
            F.col("old_content"),
            F.col("new_content"),
            F.col("old_start"),
            F.col("old_lines"),
            F.col("new_start"),
            F.col("new_lines"),
        ),
    ).select("convert_output")
    final_df.printSchema()

    final_df = (
        final_df.withColumn("json_str", F.col("convert_output.json_str"))
        .withColumn("del", F.col("convert_output.del"))
        .withColumn("add", F.col("convert_output.add"))
        .withColumn("hash_of_lines", F.col("convert_output.hash_of_lines"))
        .drop("convert_output")
    )
    final_df = (
        final_df.filter(F.length("json_str") < 1e6)
        .filter(F.col("del") <= 100)
        .filter(F.col("add") <= 100)
    )
    final_df.printSchema()
    final_df = final_df.dropDuplicates(["hash_of_lines"])

    cache_dir = f"{root_s3_dir}/final-edit-data-{date}/cache-all"
    logger.info(f"Try to save the final examples into {cache_dir}.")
    final_df.write.mode("overwrite").parquet(cache_dir)
    final_df = spark.read.parquet(cache_dir)
    logger.info(f"Finish the writting procedure ({final_df.count()} examples).")

    for delete_num in range(11):
        cur_save_dir = f"{root_s3_dir}/final-edit-data-{date}/del_{delete_num:02d}_{delete_num:02d}"
        cur_result_df = final_df.filter(
            (F.col("del") == delete_num) & (F.col("add") <= 40)
        )
        cur_result_df.write.mode("overwrite").parquet(cur_save_dir)
        cur_result_df = spark.read.parquet(cur_save_dir)
        logger.info(f"Save {cur_result_df.count():6d} into {cur_save_dir}")

    # Create in between 11~20
    cur_save_dir = f"{root_s3_dir}/final-edit-data-{date}/del_11_20"
    cur_result_df = final_df.filter(
        (11 <= F.col("del")) & (F.col("del") <= 20) & (F.col("add") <= 40)
    )
    cur_result_df.write.mode("overwrite").parquet(cur_save_dir)
    cur_result_df = spark.read.parquet(cur_save_dir)
    logger.info(f"Save {cur_result_df.count():6d} into {cur_save_dir}")
    # Create in between 21~30
    cur_save_dir = f"{root_s3_dir}/final-edit-data-{date}/del_21_30"
    cur_result_df = final_df.filter(
        (21 <= F.col("del")) & (F.col("del") <= 30) & (F.col("add") <= 40)
    )
    cur_result_df.write.mode("overwrite").parquet(cur_save_dir)
    cur_result_df = spark.read.parquet(cur_save_dir)
    logger.info(f"Save {cur_result_df.count():6d} into {cur_save_dir}")


def convert_df_into_json(
    target_dir: str,
    date: str,
    root_s3_dir: str = "s3a://dxy-dev-bucket/edit",
    batch_size: int = 20000,
):
    """Convert the cached Dataframe into simple json files."""
    logger = utils_for_log.create_logger(__file__)
    suffixes = [
        "del_00_00",
        "del_01_01",
        "del_02_02",
        "del_03_03",
        "del_04_04",
        "del_05_05",
        "del_06_06",
        "del_07_07",
        "del_08_08",
        "del_09_09",
        "del_10_10",
        "del_11_20",
        "del_21_30",
    ]
    spark = create_spark()
    for suffix in suffixes:
        cur_source_dir = f"{root_s3_dir}/final-edit-data-{date}/{suffix}"
        cur_target_dir = pathlib.Path(f"{target_dir}/json-{date}/raw-cache-{suffix}")
        cur_target_dir.mkdir(exist_ok=True, parents=True)
        df = spark.read.parquet(cur_source_dir)
        cur_total = df.count()
        logger.info(f"Load {cur_total} examples from {cur_source_dir}")

        index, cur_batch = 0, []
        for row in tqdm.tqdm(
            df.toLocalIterator(), total=cur_total, desc=f"{suffix} ({cur_total})"
        ):
            cur_batch.append(row.json_str)
            index += 1
            if index % batch_size == 0 or index == cur_total:
                cur_target_file = (
                    cur_target_dir
                    / f"{index-batch_size:07d}-to-{index:07d}-in-{cur_total:07d}.jsonl.zst"
                )
                utils_for_file.write_jsonl_zst(cur_target_file, cur_batch)
                logger.info(f"Save {len(cur_batch)} examples into {cur_target_file}")
                cur_batch = []
        logger.info(f"Save {index} examples into {cur_target_dir}")


def finally_filter_json_files(
    root_dir: str,
    date: str,
    batch_size: int = 80000,
):
    """Convert the cached Dataframe into simple json files."""
    final_root_dir = pathlib.Path(f"{root_dir}/json-{date}")
    assert final_root_dir.exists(), final_root_dir
    logger = utils_for_log.create_logger(
        __file__, final_root_dir / f"log-{utils_for_log.time_string()}.log"
    )
    suffixes = [
        "del_00_00",
        "del_01_01",
        "del_02_02",
        "del_03_03",
        "del_04_04",
        "del_05_05",
        "del_06_06",
        "del_07_07",
        "del_08_08",
        "del_09_09",
        "del_10_10",
        "del_11_20",
        "del_21_30",
    ]
    for suffix in suffixes:
        cur_source_dir = final_root_dir / f"raw-cache-{suffix}"
        assert cur_source_dir.exists(), cur_source_dir

        jsonl_files = sorted(
            list(cur_source_dir.glob("*-to-*-in-*.jsonl.zst")), key=lambda x: str(x)
        )
        jsonl_files = [str(x) for x in jsonl_files]
        logger.info(f"There are {len(jsonl_files)} files in total.")
        cur_target_dir = final_root_dir / f"final-filtered-cache-{suffix}"
        cur_target_dir.mkdir(exist_ok=True, parents=True)

        index, skipped, cur_batch = 0, 0, []
        total_json_files = 0
        for index, jsonl_file in tqdm.tqdm(
            enumerate(jsonl_files), total=len(jsonl_files), desc=f"load {suffix}"
        ):
            batch_of_json_str = utils_for_file.read_jsonl_zst(jsonl_file)
            total_json_files += len(batch_of_json_str)
            for ijjx, json_str in enumerate(batch_of_json_str):
                edit_data = utils_for_dataclass.create_from_json(
                    data_type.EditData, json_str
                )
                # TODO(dxy): the filtering logic is too restrict to use.
                # Filtering logic
                if edit_data.contain_empty_in_relative_lrange():
                    skipped += 1
                    continue
                chars_per_line = [len(x.content) for x in edit_data.lines]
                if (
                    np.average(chars_per_line) <= 2
                    or np.average(chars_per_line) >= 1000
                ):
                    skipped += 1
                    continue
                cur_batch.append(json_str)
                index += 1
                # Save the batch
                if index % batch_size == 0 or (
                    index + 1 == len(jsonl_files) and ijjx + 1 == len(batch_of_json_str)
                ):
                    cur_target_file = (
                        cur_target_dir
                        / f"{index-batch_size:07d}-to-{index:07d}-in-unknown.jsonl.zst"
                    )
                    utils_for_file.write_jsonl_zst(cur_target_file, cur_batch)
                    logger.info(
                        f"Save {len(cur_batch)} examples ({skipped} skipped) into {cur_target_file}"
                    )
                    cur_batch = []
        logger.info(f"Save {index}/{total_json_files} examples into {cur_target_dir}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--mode",
        type=str,
        required=True,
        choices=("check", "download", "filter_df", "convert", "filter_json"),
        help="The running mode.",
    )
    parser.add_argument(
        "--date",
        type=str,
        required=True,
        help="A date string used in target directory name.",
    )
    args = parser.parse_args()

    if args.mode == "check":
        check_raw_data(args.date)
    elif args.mode == "download":
        download_raw_data(args.date)
    elif args.mode == "filter_df":
        filter_df_data(
            df_path=f"s3a://dxy-dev-bucket/edit/joined_hunks_deduped_{args.date}",
            date=args.date,
        )
    elif args.mode == "convert":
        convert_df_into_json(
            target_dir="/mnt/efs/augment/user/dxy/datasets/edit/",
            date=args.date,
        )
    elif args.mode == "filter_json":
        finally_filter_json_files(
            root_dir="/mnt/efs/augment/user/dxy/datasets/edit/",
            date=args.date,
        )
    else:
        raise ValueError(f"The unknown mode: {args.mode}")
