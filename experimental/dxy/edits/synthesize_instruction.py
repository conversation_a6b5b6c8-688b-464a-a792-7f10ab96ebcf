"""Process Data for Edit.

python experimental/dxy/edits/synthesize_instruction.py --identifier del_00_00-per40
python experimental/dxy/edits/synthesize_instruction.py --identifier del_01_01-per40
python experimental/dxy/edits/synthesize_instruction.py --identifier del_02_02-per40
python experimental/dxy/edits/synthesize_instruction.py --identifier del_03_03-per40
python experimental/dxy/edits/synthesize_instruction.py --identifier del_04_04-per40

python experimental/dxy/edits/synthesize_instruction.py --identifier del_11_20-per10
python experimental/dxy/edits/synthesize_instruction.py --identifier del_21_30-per10
"""
import argparse
import dataclasses
import json
import pathlib

import tqdm

from experimental.dxy.edits import data_type
from experimental.dxy.edits.reverse_get_instruction import (
    predict_edit_data_v0 as predict_edit_data,
)
from research.core import utils_for_log

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--identifier",
        type=str,
        required=True,
        choices=(
            "del_00_00-per40",
            "del_01_01-per40",
            "del_02_02-per40",
            "del_03_03-per40",
            "del_04_04-per40",
            "del_05_05-per40",
            "del_06_06-per40",
            "del_07_07-per40",
            "del_08_08-per40",
            "del_09_09-per40",
            "del_10_10-per40",
            "del_11_20-per10",
            "del_21_30-per10",
        ),
        help="The identifier string.",
    )
    parser.add_argument(
        "--input_dir",
        type=str,
        default="/home/<USER>/datasets/edit/organized-basic-2023-10-13",
        help="The input directory.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="/home/<USER>/datasets/edit/organized-basic-instruct-2023-10-13",
        help="The output directory.",
    )
    args = parser.parse_args()

    input_file = pathlib.Path(f"{args.input_dir}/manager-{args.identifier}.jsonl.zst")
    assert input_file.exists(), input_file
    manager = data_type.EditDataManager.read_from_jsonl(str(input_file))
    output_dir = pathlib.Path(args.output_dir)
    print(f"The output directory is {output_dir}")
    output_dir.mkdir(exist_ok=True, parents=True)
    output_file = output_dir / f"instruct-{args.identifier}.json"

    # Try to restore the old results if we can.
    cache_record_by_index = {}
    if output_file.exists():
        with output_file.open("r", encoding="utf-8") as f:
            for idx, line in enumerate(f):
                try:
                    record = json.loads(line)
                    cache_record_by_index[record["index"]] = record
                    if idx != record["index"]:
                        print(
                            f"The {idx}-th line gets a record for the index = {record['index']}"
                        )
                except json.decoder.JSONDecodeError:
                    print(f"failed to parse line: {line}")
        print(f"There are {len(cache_record_by_index)} cached examples.")
        backup_dir = output_dir / ".backup"
        backup_dir.mkdir(exist_ok=True, parents=True)
        backup_path = backup_dir / f"{output_file.name}.{utils_for_log.time_string()}"
        # Move the output_file to backup_dir.
        output_file.rename(backup_path)
        print(f"Move {output_file} to {backup_path}")

    with output_file.open("w", encoding="utf-8") as f:
        for idx, edit_data in tqdm.tqdm(
            enumerate(manager.edit_data_list),
            total=len(manager.edit_data_list),
            desc=args.identifier,
        ):
            if idx in cache_record_by_index:
                record = cache_record_by_index[idx]
            else:
                instruction = predict_edit_data(edit_data, debug=False)
                # try:
                #     instruction = predict_edit_data(edit_data, debug=False)
                # except:
                #     import pdb; pdb.set_trace()
                #     print("-")
                record = {
                    "index": idx,
                    "edit_data": dataclasses.asdict(edit_data),
                    "instruction": instruction,
                    "input_file": str(input_file),
                }
            json.dump(record, f)
            f.write("\n")
            f.flush()
    print(f"Write {len(manager.edit_data_list)} examples into {output_file}")
