"""Test the FastForwardDeepSeekCoderInstruct33B model.

bazel run -c opt //base:install

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/edits/misc/test_ff_ds_33b.py
"""

import pathlib
import time

# from research.core.llama_prompt_formatters import DeepSeekCoderInstructFormatter
# from research.core.model_input import ModelInput
# from research.models.fastforward_llama_models import FastForwardDeepSeekCoderInstruct33B
from research.core.utils_for_log import time_string
from research.models.fastforward_llama_models import FastForwardDroid1B16K
from research.models.meta_model import GenerationOptions


def get_prompt_file(prompt_file_path: str) -> str:
    xpath = pathlib.Path(prompt_file_path)
    if not xpath.exists():
        raise FileNotFoundError(f"Prompt file {prompt_file_path} does not exist.")
    with xpath.open() as f:
        return f.read()


def main():
    print(f"{time_string()}: loading model")
    # model = FastForwardDeepSeekCoderInstruct33B()
    # model.prompt_formatter = DeepSeekCoderInstructFormatter(
    #     DEFAULT_SYSTEM_PROMPT="", PROMPT_TEMPLATE="{system_prompt}{instruction}"
    # )
    model = FastForwardDroid1B16K()
    model.load()
    print(f"{time_string()}: finish loading model")
    while True:
        time.sleep(0.1)
        prompt_path = input("Enter the file path that contains the prompt: ")
        if not pathlib.Path(prompt_path).exists():
            print(f"Did not find {prompt_path} and continue")
        prompt = get_prompt_file(prompt_path)
        prompt_tokens = model.tokenizer.tokenize(prompt)
        results = model.raw_generate(
            prompt_tokens, GenerationOptions(max_generated_tokens=512)
        )
        print(f"{time_string()}: finish the generation")
        print(f"Result:\n{results}")


if __name__ == "__main__":
    main()
