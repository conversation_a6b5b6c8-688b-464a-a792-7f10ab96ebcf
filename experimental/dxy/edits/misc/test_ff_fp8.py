"""Test the FastForwardDeepSeekCoderInstruct33B model.

bazel run -c opt //base:install

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/edits/misc/test_ff_fp8.py
"""

from research.core.model_input import ModelInput
from research.core.utils_for_log import time_string
from research.models.fastforward_llama_models import FastForwardDroid1B16KFP8
from research.models.meta_model import ExtraGenerationOutputs, GenerationOptions


def main():
    print(f"{time_string()}: loading model")
    model = FastForwardDroid1B16KFP8()
    model.load()
    print(f"{time_string()}: finish loading model")
    prefix = r"""import copy
import dataclasses
import random
import typing

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from experimental.dxy.edits.pipelines import Pipeline
from research.core import utils_for_str
from research.data.synthetic_code_edit.types import CodeEditData, Instruction
from research.data.synthetic_code_edit.util_lib import (
    create_code_with_leading_brackets,
    create_diff_with_leading_brackets,
)


@dataclasses.dataclass
class GenerateInstructionV0(Pipeline):
    '''The pipeline to generate the instruction via the selected code, updated code, and the reverse instruction.'''

    num_samples_from_seed: int = 2
    max_context_lines: int = 40

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "selected_code",
        "suffix",
        "updated_code",
        "instructions",
    )"""
    suffix = r"""prefix_lines = data.prefix.splitlines(keepends=True)
    suffix_lines = data.suffix.splitlines(keepends=True)
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    assert data.updated_code is not None
    result_str = util_lib.create_diff_with_leading_brackets(
        prefix=utils_for_str.get_last_n_lines(data.prefix, max_num_prefix_lines),
        suffix=utils_for_str.get_first_n_lines(data.suffix, max_num_suffix_lines),
        selected_code="",
        updated_code=data.updated_code,
        marker_for_select="-",
        marker_for_update="x",
    )"""

    outputs = model.generate(
        ModelInput(
            prefix=prefix,
            suffix=suffix,
            extra={
                "selected_code": "print('hello word')",
                "instruction": "replace print with log",
            },
        ),
        options=GenerationOptions(max_generated_tokens=512),
        extra_outputs=ExtraGenerationOutputs(),
    )
    print(f"{time_string()}: finish the generation")
    print(f"outputs:\n{outputs}")


if __name__ == "__main__":
    main()
