# Experiments

```bash
# Batch Size = 8 will use 75507MiB / 81559MiB
# CodeLLaMA-Python
torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False \
 --max_iters=8000 --batch_size=8 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train3v_r1n_s4096_onlytgt \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid3v_r1n_s4096_onlytgt \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-Python7B-data3v1r_onlytgt-s8000-b8-lr_000002


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False \
 --max_iters=8000 --batch_size=8 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train3v_r1n_s4096_full \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid3v_r1n_s4096_full \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-Python7B-data3v1r_full-s8000-b8-lr_000002


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False \
 --max_iters=8000 --batch_size=8 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train3v_r1n_s4096_full \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid3v_r1n_s4096_full \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-7B-data3v1r_full-s8000-b8-lr_000002
```

## Misc-V1.0

```bash
# CodeLLaMA
torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=2e-5 --decay_lr=False \
 --max_iters=3000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r3n \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r3n \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-7B-data2v-3000s-lr_00002

torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=3e-5 --decay_lr=False \
 --max_iters=5000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r1n_s4096_onlytgt \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_onlytgt \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-7B-Data_s4k_onlytgt-5000s-lr_00003

# CodeLLaMA-Python
 torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=2e-5 --decay_lr=False \
 --max_iters=3000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r3n \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r3n \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-Python7B-data2v-3000s-lr_00002


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=3e-5 --decay_lr=False \
 --max_iters=5000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r1n_s4096_onlytgt \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_onlytgt \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-Python7B-Data_s4k_onlytgt-5000s-lr_00003


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False \
 --max_iters=5000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r1n_s4096_onlytgt \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_onlytgt \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-Python7B-Data_s4k_onlytgt-5000s-lr_000002


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False \
 --max_iters=5000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r1n_s4096_full \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_full \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-Python7B-Data_s4k_full-5000s-lr_000002

# Wizard
torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32001 --rope_theta=1000000.0 \
 --learning_rate=2e-6 --decay_lr=False \
 --max_iters=5000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r1n_s4096_full \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_full \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=WizardCoder-Python7B-Data_s4k_full-5000s-lr_000002
```

## Misc-V2.0

```bash
#
# Fine-tuning the WizardCoder model on 9603 train sequences and 506 valid sequences.
# Roughly take 50G per device on H100
torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32001 --rope_theta=1000000.0 \
 --learning_rate=3e-5 --decay_lr=False \
 --max_iters=3000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-7B-V1.0 \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-10/train_a9v \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-10/valid_a9v \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/L7B-train-9M-3Ksteps-lr_00003-wizardcoder \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=L7B-train-9M-3Ksteps-lr_00003-wizardcoder


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=3e-5 --decay_lr=False \
 --max_iters=3000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-10/train_a9v \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-10/valid_a9v \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/L7B-codellama-train-9M-3Ksteps-lr_00003 \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=L7B-codellama-train-9M-3Ksteps-lr_00003


torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32000 --rope_theta=1000000.0 \
 --learning_rate=3e-5 --decay_lr=False \
 --max_iters=3000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-10/train_a9v \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-10/valid_a9v \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/L7B-codellamapython-train-9M-3Ksteps-lr_00003 \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=L7B-codellamapython-train-9M-3Ksteps-lr_00003

```
