<｜begin▁of▁sentence｜>Path:
Instruction: Use a consistent string format in this file
Prefix:
```
    utils._log(
        service=service_config.get_service(),
        level="event",
        cluster=service_config.get_cluster(),
        instance=service_config.get_instance(),
        component="deploy",
        line=line,
    )

    utils._log_audit(
        action=desired_state,
        service=service_config.get_service(),
        cluster=service_config.get_cluster(),
        instance=service_config.get_instance(),
    )


def issue_state_change_for_service(service_config, force_bounce, desired_state):
    ref_mutator = make_mutate_refs_func(
        service_config=service_config,
        force_bounce=force_bounce,
        desired_state=desired_state,
    )
    remote_git.create_remote_refs(
        utils.get_git_url(service_config.get_service()), ref_mutator
    )
    log_event(service_config=service_config, desired_state=desired_state)


def print_marathon_message(desired_state):
    if desired_state == "start":
        paasta_print(
            "This service will soon be gracefully started/restarted, replacing old instances according "
            "to the bounce method chosen in soa-configs. "
        )
    elif desired_state == "stop":
        paasta_print(
            "This service will be gracefully stopped soon. It will be started back up again on the next deploy.\n"
            "To stop this service permanently. Set this in the soa-configs definition:\n"
            "\n"
            "    instances: 0\n"
        )


def print_flink_message(desired_state):
    if desired_state == "start":
        paasta_print("'Start' will tell Flink operator to start the cluster.")
    elif desired_state == "stop":
        paasta_print(
            "'Stop' will put Flink cluster in stopping mode, it may"
            "take some time before shutdown is completed."
        )


def confirm_to_continue(cluster_service_instances, desired_state):
    paasta_print(f"You are about to {desired_state} the following instances:")
    paasta_print(
        "Either --instances or --clusters not specified. Asking for confirmation."
    )
    i_count = 0
    for cluster, services_instances in cluster_service_instances:
        for service, instances in services_instances.items():
            for instance in instances.keys():
                paasta_print(f"cluster = {cluster}, instance = {instance}")
                i_count += 1
    if sys.stdin.isatty():
        return choice.Binary(
            f"Are you sure you want to {desired_state} these {i_count} instances?",
            False,
        ).ask()
    return True


REMOTE_REFS: Dict[str, List[str]] = {}


def get_remote_refs(service, soa_dir):
    if service not in REMOTE_REFS:
        REMOTE_REFS[service] = remote_git.list_remote_refs(
            utils.get_git_url(service, soa_dir)
        )
    return REMOTE_REFS[service]


def paasta_start_or_stop(args, desired_state):
    """Requests a change of state to start or stop given branches of a service."""
    soa_dir = args.soa_dir

    pargs = apply_args_filters(args)
    if len(pargs) == 0:
        return 1

    affected_services = {
        s for service_list in pargs.values() for s in service_list.keys()
    }
    if len(affected_services) > 1:
        paasta_print(
            PaastaColors.red("Warning: trying to start/stop/restart multiple services:")
        )


```

Suffix:
```
        if sys.stdin.isatty():
            confirm = choice.Binary("Are you sure you want to continue?", False).ask()
        else:
            confirm = False
        if not confirm:
            paasta_print()
            paasta_print("exiting")
            return 1

    invalid_deploy_groups = []
    marathon_message_printed = False
    affected_flinks = []

    if args.clusters is None or args.instances is None:
        if confirm_to_continue(pargs.items(), desired_state) is False:
            paasta_print()
            paasta_print("exiting")
            return 1

    for cluster, services_instances in pargs.items():
        for service, instances in services_instances.items():
            for instance in instances.keys():
                service_config = get_instance_config(
                    service=service,
                    cluster=cluster,
                    instance=instance,
                    soa_dir=soa_dir,
                    load_deployments=False,
                )
                if isinstance(service_config, FlinkDeploymentConfig):
                    affected_flinks.append(service_config)
                    continue

                try:
                    remote_refs = get_remote_refs(service, soa_dir)
                except remote_git.LSRemoteException as e:
                    msg = (
                        f"Error talking to the git server: {str(e)}\n"
                        "This PaaSTA command requires access to the git server to operate.\n"
                        "The git server may be down or not reachable from here.\n"
                        "Try again from somewhere where the git server can be reached, "
                        "like your developer environment."
                    )
                    paasta_print(msg)
                    return 1

                deploy_group = service_config.get_deploy_group()
                (deploy_tag, _) = get_latest_deployment_tag(remote_refs, deploy_group)

                if deploy_tag not in remote_refs:
                    invalid_deploy_groups.append(deploy_group)
                else:
                    force_bounce = utils.format_timestamp(datetime.datetime.utcnow())
                    if (
                        isinstance(service_config, MarathonServiceConfig)
                        and not marathon_message_printed
                    ):
                        print_marathon_message(desired_state)
                        marathon_message_printed = True

                    issue_state_change_for_service(
                        service_config=service_config,
                        force_bounce=force_bounce,
                        desired_state=desired_state,
                    )

    return_val = 0

    # TODO: Refactor to discover if set_state is available for given
    #       instance_type in API
    if affected_flinks:
        print_flink_message(desired_state)
        csi = defaultdict(lambda: defaultdict(list))
        for service_config in affected_flinks:
            csi[service_config.cluster][service_config.service].append(
                service_config.instance
            )

        system_paasta_config = load_system_paasta_config()
        for cluster, services_instances in csi.items():
            client = get_paasta_api_client(cluster, system_paasta_config)
            if not client:
                paasta_print("Cannot get a paasta-api client")
                exit(1)

            for service, instances in services_instances.items():
                for instance in instances:
                    try:
                        client.service.instance_set_state(
                            service=service,
                            instance=instance,
                            desired_state=desired_state,
                        ).result()
                    except HTTPError as exc:
                        paasta_print(exc.response.text)
                        return exc.status_code

                return_val = 0

    if invalid_deploy_groups:

```

Selected Code:
```
        for cluster, services_instances in pargs.items():
            paasta_print("Cluster %s:" % cluster)
            for service, instances in services_instances.items():
                paasta_print("    Service %s:" % service)
                paasta_print("        Instances %s" % ",".join(instances.keys()))

```

Updated Code:
```
