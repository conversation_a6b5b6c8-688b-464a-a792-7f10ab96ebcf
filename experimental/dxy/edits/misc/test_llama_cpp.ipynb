{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.models.meta_model import GenerationOptions, ModelInput\n", "from research.models.remote_models import DeepSeekCoderInstruct_LLaMACPP_Model\n", "\n", "model = DeepSeekCoderInstruct_LLaMACPP_Model(\n", "    url=\"http://127.0.0.1:8086\", seq_length=8192\n", ")\n", "model.prompt_formatter.DEFAULT_SYSTEM_PROMPT = \"\"  # type: ignore\n", "model.prompt_formatter.PROMPT_TEMPLATE = \"{system_prompt}{instruction}\"  # type: ignore\n", "model.load()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["prompt = r\"\"\"You are an AI programming assistant, and you only answer questions related to computer science. For politically sensitive questions, security and\n", " privacy issues, and other non-computer science questions, you will refuse to answer.\n", "\n", "### Instruction:\n", "Full Code:\n", "```\n", "]\n", "# ResponseFormat = typing.Literal[\"json_object\"]\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def _prepare_chat_request_argument(\n", "@@@@@@@@@\n", "    messages: list[str],\n", "    system_prompt: typing.Optional[str] = None,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = \"gpt-3.5-turbo-1106\",\n", "    seed: typing.Optional[int] = None,\n", "    num_completion: int = 1,\n", "    use_json: bool = False,\n", "&&&&&&&&&\n", "):\n", "    request_messages: list[ChatCompletionMessageParam] = []\n", "    if system_prompt is not None:\n", "        request_messages.append(\n", "            ChatCompletionSystemMessageParam(content=system_prompt, role=\"system\")\n", "        )\n", "\n", "```\n", "\n", "Edit Request: remove type annotations\n", "\n", "Please follow these instructions carefully and restrictly:\n", "- The edit request is only applied to the code within these 9-times-repeated characters @@@@@@@@@ and &&&&&&&&&.\n", "- Keep the codes outside of @@@@@@@@@ and &&&&&&&&& exactly identical.\n", "- Preserve the original indentation and style.\n", "- Do not output any additional text, headers, comments, suggestions, formatting, or explanations.\n", "- Preserve @@@@@@@@@ and &&&&&&&&& in the response at its original location.\n", "\n", "### Response:\"\"\""]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "```\n", "]\n", "# ResponseFormat = typing.Literal[\"json_object\"]\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def _prepare_chat_request_argument(\n", "    messages,\n", "    system_prompt=None,\n", "    temperature=0.2,\n", "    max_tokens=256,\n", "    model=\"gpt-3.5-turbo-1106\",\n", "    seed=None,\n", "    num_completion=1,\n", "    use_json=False,\n", "):\n", "    request_messages = []\n", "    if system_prompt is not None:\n", "        request_messages.append(\n", "            ChatCompletionSystemMessageParam(content=system_prompt, role=\"system\")\n", "        )\n", "```\n", "\n"]}], "source": ["output = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    options=GenerationOptions(temperature=0, max_generated_tokens=1024),\n", ")\n", "print(output)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}