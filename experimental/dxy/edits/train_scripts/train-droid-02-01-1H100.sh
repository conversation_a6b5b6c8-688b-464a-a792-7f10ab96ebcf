#!/bin/bash
#
# ./launch_pod.py --cluster CW create dxy-xh100 --gpu-type H100_NVLINK_80GB --gpu-count 8 --memory 960 --cpu-count 96
#
# Example Usage:
# bash experimental/dxy/edits/train_scripts/train-droid-01-24-1H100.sh
#
set -e

# Data Parallelism Size = 8, Model Parallelism Size = 1
# LR=5e-6, BS=2 -> GBS=16, Epochs=1
torchrun --nproc_per_node=8 train.py \
  configs/deepseek_coder_instruct_1.3b.py \
  --learning_rate=5e-6 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 \
  --batch_size=2 --gradient_accumulation_steps=1 \
  --block_size=8192 \
  --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.Vendor36/train-S8192 \
  --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct/ \
  --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.24/ \
  --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 \
  --wandb_run_name=Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6

python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6/checkpoint_llama_iteration_791/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6-ffw

# LR=5e-6, BS=1 -> GBS=8, Epochs=1
torchrun --nproc_per_node=8 train.py \
  configs/deepseek_coder_instruct_1.3b.py \
  --learning_rate=5e-6 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 \
  --batch_size=1 --gradient_accumulation_steps=1 \
  --block_size=8192 \
  --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.Vendor36/train-S8192 \
  --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct/ \
  --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.24/ \
  --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 \
  --wandb_run_name=Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x1x1-lr_constant_5e-6

python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x1x1-lr_constant_5e-6/checkpoint_llama_iteration_1584/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x1x1-lr_constant_5e-6-ffw


# LR=5e-6, BS=2 -> GBS=16, Epochs=2
torchrun --nproc_per_node=8 train.py \
  configs/deepseek_coder_instruct_1.3b.py \
  --learning_rate=5e-6 --max_iters=0 --max_epochs=2 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 \
  --batch_size=2 --gradient_accumulation_steps=1 \
  --block_size=8192 \
  --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.Vendor36/train-S8192 \
  --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct/ \
  --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.24/ \
  --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 \
  --wandb_run_name=Droid.38.39.Vendor36-S8K_E2-WD1_0-b1x8x2x1-lr_constant_5e-6

python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E2-WD1_0-b1x8x2x1-lr_constant_5e-6/checkpoint_llama_iteration_1584/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E2-WD1_0-b1x8x2x1-lr_constant_5e-6-ffw


# LR=1e-5, BS=2 -> GBS=16, Epochs=1
torchrun --nproc_per_node=8 train.py \
  configs/deepseek_coder_instruct_1.3b.py \
  --learning_rate=1e-5 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 \
  --batch_size=2 --gradient_accumulation_steps=1 \
  --block_size=8192 \
  --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.Vendor36/train-S8192 \
  --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct/ \
  --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.24/ \
  --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 \
  --wandb_run_name=Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x2x1-lr_constant_1e-5

python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x2x1-lr_constant_1e-5/checkpoint_llama_iteration_791/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid.38.39.Vendor36-S8K_E1-WD1_0-b1x8x2x1-lr_constant_1e-5-ffw


# LR=5e-6, BS=2 -> GBS=16, Epochs=1
torchrun --nproc_per_node=8 train.py \
  configs/deepseek_coder_instruct_1.3b.py \
  --learning_rate=5e-6 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 \
  --batch_size=2 --gradient_accumulation_steps=1 \
  --block_size=16384 \
  --train_data_path=/mnt/efs/augment/user/dxy/datasets/igor-droid-repo-48/train \
  --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-1.3b-instruct/ \
  --out_dir=/mnt/efs/augment/user/dxy/logs/edit.02.01/ \
  --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 \
  --wandb_run_name=Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6


# Make it as ffw
python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_2e-6/checkpoint_llama_iteration_419/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_2e-6/ffw

python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6/checkpoint_llama_iteration_419/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6/ffw


# Make it as FP8
cd base/fastforward/
CUDA_VISIBLE_DEVICES=0 python quantize_llama.py \
    --max-seq-len 16384 \
    --calibration-steps 256 \
    --log-to-stdout \
    --model-size "deepseek-coder-1.3b" \
    --ckpt-path "/mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6/ffw" \
    --out-path "/mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_5e-6/ffw-fp8" \
    --calibration-data "/mnt/efs/augment/user/dxy/datasets/igor-droid-repo-48/train"


python experimental/yuri/export_droid_to_ffw.py \
  -m 1 \
  -i /mnt/efs/augment/user/dxy/logs/edit.02.01/igor-droid-142-1B/ \
  -o /mnt/efs/augment/user/dxy/logs/edit.02.01/igor-droid-142-1B/ffw


CUDA_VISIBLE_DEVICES=0 python quantize_llama.py \
    --max-seq-len 16384 \
    --calibration-steps 256 \
    --log-to-stdout \
    --model-size "deepseek-coder-1.3b" \
    --ckpt-path "/mnt/efs/augment/user/dxy/logs/edit.02.01/igor-droid-142-1B/ffw" \
    --out-path "/mnt/efs/augment/user/dxy/logs/edit.02.01/igor-droid-142-1B/ffw-fp8" \
    --calibration-data "/mnt/efs/augment/user/dxy/datasets/igor-droid-repo-48/train"


cd base/fastforward/
CUDA_VISIBLE_DEVICES=0 python quantize_llama.py \
    --max-seq-len 16384 \
    --calibration-steps 256 \
    --log-to-stdout \
    --model-size "deepseek-coder-1.3b" \
    --ckpt-path "/mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_2e-6/ffw" \
    --out-path "/mnt/efs/augment/user/dxy/logs/edit.02.01/Droid.48-S16K_E1-WD1_0-b1x8x2x1-lr_constant_2e-6/ffw-fp8" \
    --calibration-data "/mnt/efs/augment/user/dxy/datasets/igor-droid-repo-48/train"


## Train the 33B model
# cd research/fastbackward/
# python determined/launch.py ../../experimental/dxy/edits/train_scripts/droid-33B-01-24-3839V.yaml

python experimental/yuri/export_droid_to_ffw.py \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_1e-5/checkpoint_llama_iteration_791 \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_1e-5-ffw

python experimental/yuri/export_droid_to_ffw.py \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_2e-5/checkpoint_llama_iteration_791 \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_2e-5-ffw

python experimental/yuri/export_droid_to_ffw.py \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_5e-6/checkpoint_llama_iteration_791 \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_5e-6-ffw

python experimental/yuri/export_droid_to_ffw.py \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E2-b4x8x2x1-lr_constant_1e-5/checkpoint_llama_iteration_1584 \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E2-b4x8x2x1-lr_constant_1e-5-ffw

python experimental/yuri/export_droid_to_ffw.py \
  -i /mnt/efs/augment/user/dxy/logs/edit.01.25/Droid33B.38.39.PR.Vendor54-S8K_E1-b4x8x2x1-lr_constant_1e-5/checkpoint_llama_iteration_1331 \
  -o /mnt/efs/augment/user/dxy/logs/edit.01.25/Droid33B.38.39.PR.Vendor54-S8K_E1-b4x8x2x1-lr_constant_1e-5-ffw
