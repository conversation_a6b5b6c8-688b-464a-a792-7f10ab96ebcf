#
# python determined/launch.py ../../experimental/dxy/edits/train_scripts/droid-33B-01-25-3839PrVe.yaml
#
determined:
  name: "Droid33B.38.39.PR.Vendor54-S8K_E1-b4x8x4x1-lr_constant_5e-6"
  description: null
  workspace: Dev
  project: <PERSON><PERSON><PERSON>-<PERSON>

augment:
  podspec_path: "8xH100.yaml"
  gpu_count: 32

fastbackward_configs:
 - configs/deepseek_base_33b.py

fastbackward_args:
  out_dir: /mnt/efs/augment/user/dxy/logs/edit.01.25/
  learning_rate: 5e-6
  decay_lr: False
  max_iters: 0
  max_epochs: 1
  warmup_iters: 0
  weight_decay: 1.0
  batch_size: 4
  gradient_accumulation_steps: 1
  block_size: 8192
  train_data_path: /mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.PR.Vendor54/train-S8192
  hf_checkpoint_dir: /mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/
  wandb_log: True
  wandb_project: edit-exps-v2
  wandb_run_name: Droid33B.38.39.PR.Vendor54-S8K_E1-b4x8x4x1-lr_constant_5e-6
