"""Load the 89 code edit examples.

python generate_89.py
python experimental/dxy/edits/scripts/generate_89.py
"""
import collections
import dataclasses
import hashlib
import pathlib
import random

import research.core.constants as constants
import research.core.utils_for_file as utils_for_file
import research.core.utils_for_str as utils_for_str
from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.types import Document
from research.data.synthetic_code_edit import util_lib

RepoType = dict[str, set[tuple[str, str]]]
random.seed(888)


def load_edit_eval_tests(
    data_dir: pathlib.Path = constants.AUGMENT_ROOT
    / "research"
    / "eval"
    / "edit"
    / "data",
    lines_in_prefix_suffix: int = 100,
) -> tuple[list[ResearchEditPromptInput], RepoType]:
    samples: list[ResearchEditPromptInput] = []
    # repo_url to a set of (file_name, content)
    repos: RepoType = collections.defaultdict(set)
    categories = filter(lambda d: d.is_dir(), data_dir.iterdir())
    for category_dir in categories:
        for index, file in enumerate(category_dir.glob("*.txt")):
            code_edit_data = util_lib.load_from_seed_data_format(file)
            assert code_edit_data.instructions is not None
            for instruction in code_edit_data.instructions:
                if (
                    "Randomly adjust the spaces after commas".lower()
                    in instruction.text.lower()
                    or "irregular and inconsis" in instruction.text.lower()
                ):
                    print(f"Skip {instruction.text}.")
                    continue
                if lines_in_prefix_suffix <= 0:
                    prefix, suffix = "", ""
                else:
                    prefix = utils_for_str.get_last_n_lines(
                        code_edit_data.prefix, lines_in_prefix_suffix
                    )
                    suffix = utils_for_str.get_first_n_lines(
                        code_edit_data.suffix, lines_in_prefix_suffix
                    )
                assert code_edit_data.selected_code is not None
                assert code_edit_data.updated_code is not None
                repo_url = code_edit_data.repo_url
                file_name = code_edit_data.file_name
                if not repo_url.startswith("https:"):
                    repo_url = f"FakeRepoUrl:{category_dir.name}/{index}_{file.name}"
                if file_name == "N/A":
                    file_name = f"FakeFileName:{category_dir.name}/{file.name}"
                if repo_url in repos:
                    repo_url = f"C{random.randint(1, 100)}@{repo_url}"
                assert repo_url not in repos, f"{repo_url}"
                repos[repo_url].add(
                    (
                        file_name,
                        code_edit_data.prefix
                        + code_edit_data.selected_code
                        + code_edit_data.suffix,
                    )
                )
                sample = ResearchEditPromptInput(
                    path=file_name,
                    prefix=prefix,
                    selected_code=code_edit_data.selected_code,
                    suffix=suffix,
                    instruction=instruction.text,
                    prefix_begin=len(code_edit_data.prefix) - len(prefix),
                    suffix_end=len(code_edit_data.prefix)
                    + len(code_edit_data.selected_code)
                    + len(suffix),
                    retrieved_chunks=(),
                    updated_code=code_edit_data.updated_code,
                    extra={"category": category_dir.name, "repo_url": repo_url},
                )
                samples.append(sample)
    return samples, repos


def main():
    examples, repos = load_edit_eval_tests()
    print(f"Loaded {len(examples)} examples from {len(repos)} repositories.")
    unique_files = 0
    for repo in repos:
        unique_files += len(repos[repo])
        if len(repos[repo]) > 1:
            print(f"Repo {repo} has {len(repos[repo])} files")
    print(f"Loaded {unique_files} from {len(repos)} repositories")
    # Save the samples into this data folder.
    target_dir = pathlib.Path(
        "/mnt/efs/augment/data/eval/code_edit_eval/clean_basic_task"
    )
    assert target_dir.exists()
    # Save the examples into this data folder.
    example_dir = target_dir / "examples"
    example_dir.mkdir(parents=False, exist_ok=True)
    for index, x in enumerate(examples):
        save_path = example_dir / f"2023-02-13-I{index:03d}-{len(examples):03d}.json"
        utils_for_file.write_json(save_path, dataclasses.asdict(x), indent=2)
    # Save the repo data into this data folder.
    repo_dir = target_dir / "repos"
    repo_dir.mkdir(parents=False, exist_ok=True)
    for index, (repo_url, list_doc) in enumerate(repos.items()):
        save_path = repo_dir / f"2023-02-13-R{index:03d}-{len(repos):03d}.json"
        info = {"repo_url": repo_url, "docs": []}
        for file_path, content in list_doc:
            doc = Document(
                id=hashlib.sha256(content.encode("utf8")).hexdigest(),
                text=content,
                path=file_path,
            )
            info["docs"].append(dataclasses.asdict(doc))
        utils_for_file.write_json(save_path, info, indent=2)
    print(f"Save everything into {target_dir}.")


if __name__ == "__main__":
    main()
