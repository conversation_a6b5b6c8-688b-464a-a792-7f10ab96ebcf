#!/bin/bash
# bash experimental/dxy/edits/scripts/organize.sh
set -e

INPUT_DIR="/mnt/efs/augment/user/dxy/datasets/edit/json-2023-10-13"
OUTPUT_DIR="/home/<USER>/datasets/edit/organized-basic-2023-10-13/"

for i in {0..10}; do
  echo "Handle the delete range for $i - $i"
  python experimental/dxy/edits/organize_data.py \
    --input_dir ${INPUT_DIR} --output_dir ${OUTPUT_DIR} \
    --deleted_range "$i $i" --max_per_add 40
done

python experimental/dxy/edits/organize_data.py \
    --input_dir ${INPUT_DIR} --output_dir ${OUTPUT_DIR} \
    --deleted_range "11 20" --max_per_add 10

python experimental/dxy/edits/organize_data.py \
    --input_dir ${INPUT_DIR} --output_dir ${OUTPUT_DIR} \
    --deleted_range "21 30" --max_per_add 10
