"""Load the PR-based code edit evaluation examples.

python generate_pr_eval.py
python experimental/dxy/edits/scripts/generate_pr_eval.py --stage prepare
python experimental/dxy/edits/scripts/generate_pr_eval.py --stage generate
"""
import argparse
import dataclasses
import hashlib
import os
import pathlib
import random
import subprocess

import research.core.utils_for_file as utils_for_file
import research.core.utils_for_log as utils_for_log
from base.languages import guess_language
from research.core.edit_prompt_input import ResearchEditPromptInput
from research.core.types import Document

RepoType = dict[str, set[tuple[str, str]]]
random.seed(888)


def download_and_checkout(repo_url: str, commit_hash: str, target_dir: str):
    try:
        subprocess.run(["git", "clone", repo_url, target_dir], check=True)
        subprocess.run(["git", "checkout", commit_hash], check=True, cwd=target_dir)
        print(
            f"Repository {repo_url} successfully cloned and checked out at {commit_hash}"
        )
    except subprocess.CalledProcessError as e:
        print(f"An error occurred: {e}")


def get_info_from_dict(sample: dict, index: int, raw_repo_dir: pathlib.Path):
    commit_html_url = sample["commit_html_url"]
    sha_of_commit_html_url = str(hashlib.sha256(commit_html_url.encode()).hexdigest())[
        :6
    ]
    repo_url, commit = commit_html_url.split("/commit/")
    repo_url = f"{repo_url}.git"
    info = {
        "repo_url": repo_url,
        "commit": commit,
        "repo_dir": str(raw_repo_dir / f"{index:03d}--{sha_of_commit_html_url}"),
    }
    return info, sha_of_commit_html_url


def prepare(samples: list[dict], output_dir: pathlib.Path):
    raw_repo_dir = output_dir / "raw_repos"
    raw_repo_dir.mkdir(parents=False, exist_ok=True)
    info_per_sample: list[dict] = []
    for index, sample in enumerate(samples):
        info, sha_of_commit_html_url = get_info_from_dict(sample, index, raw_repo_dir)
        utils_for_file.write_json(
            raw_repo_dir / f"{index:03d}--{sha_of_commit_html_url}.json", info, indent=2
        )
        info_per_sample.append(info)
    logger = utils_for_log.create_logger(__file__)
    for index, info in enumerate(info_per_sample):
        repo_dir = info["repo_dir"]
        if pathlib.Path(repo_dir).exists():
            print(f"Find existing directory: {repo_dir}, skip.")
            continue
        logger.info(
            f"Process {index:03d}/{len(samples)} : {info['repo_url']} into {repo_dir}"
        )
        download_and_checkout(info["repo_url"], info["commit"], repo_dir)
    logger.info(f"Save all the repo into {raw_repo_dir}.")


def _quick_fix_code(prefix, old_code, suffix, expected_content):
    assert expected_content.startswith(prefix)
    if expected_content.startswith(prefix + "\n" + old_code):
        prefix = prefix + "\n"
    elif expected_content.startswith(prefix + old_code):
        prefix = prefix
    else:
        raise ValueError("Can not figure out the prefix")
    if expected_content == prefix + old_code + "\n" + suffix:
        suffix = "\n" + suffix
    elif expected_content == prefix + old_code + "\n" + suffix + "\n":
        suffix = "\n" + suffix + "\n"
    else:
        raise ValueError("Can not figure out the suffix")
    return prefix, suffix


def generate(samples: list[dict], output_dir: pathlib.Path):
    logger = utils_for_log.create_logger(__file__)
    logger.info(f"The output directory is : {output_dir}")
    raw_repo_dir = output_dir / "raw_repos"
    info_per_sample = []
    for index, sample in enumerate(samples):
        info, _ = get_info_from_dict(sample, index, raw_repo_dir)
        info["instruction"] = sample["manual_instruction"]
        info["path"] = sample["path"]
        info["old_code"] = sample["old_code"]
        info["new_code"] = sample["new_code"]
        # Check the content
        real_path = f"{info['repo_dir']}/{info['path']}"
        assert pathlib.Path(real_path).exists()
        content = pathlib.Path(real_path).read_text()
        prefix, suffix = _quick_fix_code(
            sample["prefix"], info["old_code"], sample["suffix"], content
        )
        info["prefix"] = prefix
        info["suffix"] = suffix
        assert (
            info["prefix"] + info["old_code"] + info["suffix"] == content
        ), f"{index}/{len(samples)} {real_path} failed"
        info_per_sample.append(info)

    print("Finished all the necessary check.")

    repo_dir = output_dir / "repos"
    repo_dir.mkdir(parents=False, exist_ok=True)
    example_dir = output_dir / "examples"
    example_dir.mkdir(parents=False, exist_ok=True)
    for index, info in enumerate(info_per_sample):
        file_name = info["path"]
        original_repo_dir = info["repo_dir"]
        prefix = info["prefix"]
        suffix = info["suffix"]
        selected_code = info["old_code"]
        updated_code = info["new_code"]
        # Create the example data and save into the example directory.
        example = ResearchEditPromptInput(
            path=file_name,
            prefix=prefix,
            selected_code=selected_code,
            suffix=suffix,
            instruction=info["instruction"],
            prefix_begin=len(prefix) - len(prefix),
            suffix_end=len(prefix) + len(selected_code) + len(suffix),
            retrieved_chunks=(),
            updated_code=updated_code,
            extra={"category": "PR", "repo_url": str(original_repo_dir)},
        )
        save_path = (
            example_dir / f"2023-02-13-I{index:03d}-{len(info_per_sample):03d}.json"
        )
        utils_for_file.write_json(save_path, dataclasses.asdict(example), indent=2)
        # Create the repo data and save into the repo directory.
        all_valid_files: list[str] = []
        for file_path in utils_for_file.fina_all_files(original_repo_dir):
            if utils_for_file.is_hidden_file(file_path):
                continue
            if guess_language(file_path) is None:
                continue
            if os.path.getsize(f"{original_repo_dir}/{file_path}") > 1e6:
                continue
            all_valid_files.append(file_path)
        logger.info(
            f"Process {index:03d}/{len(samples)} :\n"
            f"\tRepo URL: {info['repo_url']}\n"
            f"\tTotal files: {len(all_valid_files)}\n"
            f"\tFile name: {file_name}"
        )
        repo_info = {"repo_url": str(original_repo_dir), "docs": []}
        for file_path in all_valid_files:
            content = pathlib.Path(f"{original_repo_dir}/{file_path}").read_text()
            doc = Document(
                id=hashlib.sha256(content.encode("utf8")).hexdigest(),
                text=content,
                path=file_path,
            )
            repo_info["docs"].append(dataclasses.asdict(doc))
        save_path = (
            f"{repo_dir}/2023-02-13-R{index:03d}-{len(info_per_sample):03d}.json"
        )
        utils_for_file.write_json(save_path, repo_info, indent=2)
        logger.info(
            f"Finished {index:03d}/{len(samples)} with {len(repo_info['docs'])} docs into {save_path}."
        )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Prepare PR Eval Dataset.")
    parser.add_argument(
        "--stage", type=str, choices=["prepare", "generate"], required=True
    )
    parser.add_argument(
        "--input",
        type=pathlib.Path,
        default="/mnt/efs/augment/user/dxy/datasets/edit.raw/edit-pr-data/pr_suggested_edits_eval-v2.1-annotated.json",
    )
    parser.add_argument(
        "--output",
        type=pathlib.Path,
        default="/mnt/efs/augment/data/eval/code_edit_eval/pr_eval_2024FEB22",
    )
    args = parser.parse_args()
    # Check the input file is available.
    assert args.input.exists()
    pr_eval_data = utils_for_file.read_json(args.input)
    if args.stage == "prepare":
        prepare(pr_eval_data, args.output)
    else:
        generate(pr_eval_data, args.output)
