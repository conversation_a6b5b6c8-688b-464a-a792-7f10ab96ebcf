"""Generate the reverse instruction for the seed data.

python generate_all.py
python experimental/dxy/edits/scripts/generate_all.py -d /home/<USER>/cache/vis/12-14-NEWX -n 50 -c Formatting-Cleaning
python experimental/dxy/edits/scripts/generate_all.py -d /home/<USER>/cache/vis/12-14-NEWX -n 50 -c Refactoring
python experimental/dxy/edits/scripts/generate_all.py -d /home/<USER>/cache/vis/12-14-NEWX -n 50 -c Bug-Fixing
"""
import argparse
import copy
import dataclasses
import pathlib
import typing
from multiprocessing import Pool

import tqdm

from experimental.dxy.edits.pipelines.simple_instruction import generate_instruction
from experimental.dxy.edits.pipelines.simple_selected_code_v2 import (
    generate_inverse_instruction_plus_selected_code,
)
from research.core import utils_for_dataclass, utils_for_file, utils_for_log
from research.data.synthetic_code_edit import api_lib, types

per_category_inverse_instructions = {
    "Formatting": [
        "pollute its syntax by replacing key-value pair as key = value",
        "Mess up the spaces after commas , and around plus signs + in the code",
        "remove the type annotations for the arguments",
        "use percent formatting for string",
        "rename variable A as variable B",
        "change all variable names to non-descriptive single letters",
        "refactor the main function into smaller functions",
        "reorder the imports randomly",
        "Remove proper indentation from the code",
    ],
    "Bug": [
        "introduce a moderate-level bug",
        "Return the wrong result from the function",
        "introduce a subtle bug in the for loop",
        "remove the try catch handling",
    ],
    "Refactoring": [
        "replace the function call with inline condition",
        "inline check_data func and delete",
        "integrate the A function into B function or C function",
        "refactor the main function into smaller functions",
        "Replace a logical chunk of code with pseudocode",
        "Replace some chunks of code with TODOs",
        "Rewrite without using numpy",
        "replace print with logging",
    ],
    "Normal-change": [
        "remove the docstring",
        "simplify the docstring",
        "polish the docstring",
        "remove all the comments",
        "replace a function as TODO",
    ],
}
all_inverse_instructions: typing.List[str] = []
for category, instructions in per_category_inverse_instructions.items():
    all_inverse_instructions.extend(instructions)


# def generate_fn(
#     index: int,
#     sample: types.CodeEditData,
#     category: typing.Optional[str],
#     save_dir: pathlib.Path,
#     html_dir: pathlib.Path,
# ) -> tuple[bool, float]:
#     """Generate the data."""
#     if category is not None:
#         instructions = per_category_inverse_instructions[category]
#     else:
#         instructions = all_inverse_instructions
#     gpt_wrapper = api_lib.GptWrapper()
#     try:
#         data_v1 = generate_inverse_instruction_plus_selected_code(
#             sample,
#             instructions,
#             num_examples=4,
#             num_context_lines=6,
#             model="gpt-4-0613",
#         )
#         save_path_json = save_dir / f"{index:04d}-{category}.json"
#         utils_for_file.write_json(save_path_json, dataclasses.asdict(data_v1), indent=2)
#         print(f"Save into {save_path_json}")
#         data_v2 = generate_instruction(
#             data_v1, num_context_lines=6, model="gpt-4-0613"
#         )
#         save_path_json = (
#             save_dir_json_r2 / f"{index:03d}-{len(raw_github_data):03d}.json"
#         )
#         utils_for_file.write_json(save_path_json, dataclasses.asdict(data_v2), indent=2)
#         # Save into the html file.
#         save_path_html = save_dir_html / f"{index:03d}-{len(raw_github_data):03d}.html"
#         save_path_html.write_text(util_lib.get_html_str(data_v2))
#         print(f"Save into {save_path_html}")
#         passed += 1
#     except Exception as e:
#         print(e)
#         print(f"Failed to generate the {index}-th sample")
#         failed += 1

# def main(
#     target_samples: typing.List[types.CodeEditData],
#     category: typing.Optional[str],
#     process: int,
#     save_dir: pathlib.Path,
#     html_dir: pathlib.Path,
# ):
#     """The main function."""
#     if process == -1:
#         for index, sample in enumerate(target_samples):
#             generate_fn(index, sample, category, save_dir, html_dir)
#     else:
#         with Pool(args.num_processes) as pool:

#             def func(args):
#                 generate_fn(*args)

#             all_arguments = []
#             for index, sample in enumerate(target_samples):
#                 all_arguments.append((index, sample, category, save_dir, html_dir))
#             _ = list(
#                 tqdm.tqdm(
#                     pool.imap(func, all_arguments),
#                     total=len(target_samples),
#                 )
#             )


# if __name__ == "__main__":
#     parser = argparse.ArgumentParser(description="Generate synthetic data.")
#     parser.add_argument(
#         "--num",
#         type=int,
#         default=10,
#         help="The maximum number of samples to generate.",
#     )
#     parser.add_argument(
#         "--processes",
#         type=int,
#         default=-1,
#         help="The number of processes used to generate the data.",
#     )
#     parser.add_argument(
#         "--category",
#         default=None,
#         help="The category to generate the data",
#     )
#     parser.add_argument(
#         "--directory",
#         default=None,
#         required=True,
#         help="Directory to save all the results",
#     )

#     args = parser.parse_args()

#     raw_github_data = [
#         utils_for_dataclass.create_from_dict(types.CodeEditData, x)
#         for x in utils_for_file.read_jsonl_zst(
#             "/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst"
#         )
#     ]

#     save_dir = pathlib.Path(args.directory) / "raw_data"
#     save_dir.mkdir(exist_ok=True, parents=True)
#     html_dir = pathlib.Path(args.directory) / "htmls"
#     html_dir.mkdir(exist_ok=True, parents=True)
#     log_file = save_dir / f"generate_all-{utils_for_log.time_string()}.log"
#     logger = utils_for_log.create_logger(__file__, log_file)
#     logger.info(f"The output directory is : {save_dir}")
#     logger.info(f"The output log file is : {log_file}")
#     target_samples = raw_github_data[: args.num]
#     logger.info(
#         f"The number of samples to generate is : {args.num}/{len(raw_github_data)}"
#     )

#     main(target_samples, args.category, args.processes, save_dir, html_dir)
