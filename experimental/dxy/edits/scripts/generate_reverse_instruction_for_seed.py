"""Generate the reverse instruction for the seed data.

python generate_reverse_instruction_for_seed.py
"""
import dataclasses
import pathlib

import tqdm

from experimental.dxy.edits.pipelines.generate_reverse_instruction import (
    GenerateReverseInstructionZeroShot,
)
from experimental.dxy.edits.sample_lib import SimpleRawEditScope
from research.core import utils_for_dataclass, utils_for_file

seeds = [
    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)
    for x in utils_for_file.read_json(
        "/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-seeds_via_icoder_addseed.json"
    )
]

save_dir = pathlib.Path(
    "/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.aug_seed/"
)
# augmented_seeds = []
# for json_file in save_dir.glob("*-*.json"):
#     augmented_seeds.append(utils_for_file.read_json(json_file))
# print(f"Load {len(augmented_seeds)} augmented seeds.")
# utils_for_file.write_json(
#     "/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-augmented_seeds_via_icoder_addseed.json",
#     augmented_seeds,
#     indent=2
# )

save_dir.mkdir(exist_ok=True, parents=True)
print(f"Logging into {save_dir}")

pipeline = GenerateReverseInstructionZeroShot(num_of_completion=1)
for index, data in enumerate(tqdm.tqdm(seeds, desc="Generate Reverse Instruction")):
    answers, messages = pipeline.generate(data)
    utils_for_file.write_json(
        save_dir / f"{index:03d}-{len(seeds):03d}.json",
        dataclasses.asdict(answers[0]),
        indent=2,
    )
