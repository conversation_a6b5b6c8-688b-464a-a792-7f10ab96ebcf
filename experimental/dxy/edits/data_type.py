"""The foundamental data types used in the edit project."""
import copy
import dataclasses
import hashlib
import json
import re
import typing

import termcolor
import tqdm
import zstandard as zstd

from research.core import utils_for_dataclass

LineOriginType = typing.Literal[" ", "+", "-"]


@dataclasses.dataclass
class Line:
    content: str
    origin: LineOriginType


@dataclasses.dataclass
class SimpleEditData:
    """The simple edit data class."""

    prefix: typing.Optional[str] = None
    suffix: typing.Optional[str] = None
    selected_code: typing.Optional[str] = None
    modified_code: typing.Optional[str] = None
    instruction: typing.Optional[str] = None


@dataclasses.dataclass
class EditData:
    """The edit data class.

    Note that the lines may contain the following characters:
    - " " : the line is unchanged.
    - "+" : the line is added.
    - "-" : the line is deleted.
    There is no guarantee that for the order of these 3 characters.
    """

    repo: str
    """The repository name."""

    commit_sha: str
    """The commit SHA string."""

    file_path: str
    """The file path."""

    lines: typing.List[Line]
    """The lines in the hunk."""

    old_file_content: str
    """The full content in the old file."""

    new_file_content: str
    """The full content in the new file."""

    old_hunk_range: typing.Tuple[int, int]
    """The line range of the hunk in the old file content, where the index starting from 1."""

    new_hunk_range: typing.Tuple[int, int]
    """The line range of the hunk in the new file content, where the index starting from 1."""

    language: str = ""
    """The program language."""

    instruction: str = ""
    """The edit instruction data."""

    def show_old_hunk(self):
        all_old_lines = self.old_file_content.splitlines(keepends=True)
        start, stop = (
            self.old_hunk_range[0] - 1,
            self.old_hunk_range[1] - 1,
        )
        hunk_lines = all_old_lines[start:stop]
        for idx, line in enumerate(hunk_lines):
            pure_line = line.rstrip("\n")
            print(f"[{idx:2d}] {pure_line}")

    def show_new_hunk(self):
        all_new_lines = self.new_file_content.splitlines(keepends=True)
        start, stop = (
            self.new_hunk_range[0] - 1,
            self.new_hunk_range[1] - 1,
        )
        hunk_lines = all_new_lines[start:stop]
        for idx, line in enumerate(hunk_lines):
            pure_line = line.rstrip("\n")
            print(f"[{idx:2d}] {pure_line}")

    def show_lines(self):
        show_lines(self.lines)

    def get_relative_lrange(self) -> typing.Tuple[int, int]:
        """The `lines` may have leading and tail " " origin, and get the range without them."""
        start, stop = 0, len(self.lines)
        for line in self.lines:
            if line.origin == " ":
                start += 1
            else:
                break
        for line in reversed(self.lines):
            if line.origin == " ":
                stop -= 1
            else:
                break
        return start, stop

    def contain_empty_in_relative_lrange(self):
        """Check whether the relative line range contains empty lines."""
        start, stop = self.get_relative_lrange()
        for index in range(start, stop):
            if self.lines[index].origin == " ":
                return True
        return False

    def get_diffs(self) -> typing.Tuple[int, int]:
        delete, add = 0, 0
        for index in range(*self.get_relative_lrange()):
            if self.lines[index].origin in ("-", " "):
                delete += 1
            if self.lines[index].origin in ("+", " "):
                add += 1
        return delete, add

    def get_selected_code(self) -> str:
        """Get the selected code."""
        lines = []
        for index in range(*self.get_relative_lrange()):
            if self.lines[index].origin in ("-", " "):
                lines.append(self.lines[index].content)
        return "".join(lines)

    def get_modified_code(self) -> str:
        """Get the modified code."""
        lines = []
        for index in range(*self.get_relative_lrange()):
            if self.lines[index].origin in ("+", " "):
                lines.append(self.lines[index].content)
        return "".join(lines)

    def get_diff_code(self) -> str:
        """Get the diff code."""
        lines = []
        for index in range(*self.get_relative_lrange()):
            lines.append(f"[{self.lines[index].origin}] {self.lines[index].content}")
        return "".join(lines)

    def get_lrange(self) -> typing.Tuple[int, int]:
        """Get the line range of the modified code in the new file content, where index starting 0."""
        start, stop = self.new_hunk_range
        for line in self.lines:
            if line.origin == " ":
                start += 1
            else:
                break
        for line in reversed(self.lines):
            if line.origin == " ":
                stop -= 1
            else:
                break
        return start - 1, stop - 1

    def get_diff_code_in_context(self, prefix_lines: int, suffix_lines: int) -> str:
        """Get the diff code in the context of the prefix and suffix lines."""
        lrange = self.get_lrange()
        relative_lrange = self.get_relative_lrange()
        lines = self.new_file_content.splitlines(keepends=True)

        partial_lines = []
        # Append the prefix lines.
        for index in range(max(0, lrange[0] - prefix_lines), lrange[0]):
            partial_lines.append(f"[ ] {lines[index]}")
        # Append the real diff parts.
        for index in range(relative_lrange[0], relative_lrange[1]):
            partial_lines.append(
                f"[{self.lines[index].origin}] {self.lines[index].content}"
            )
        # Append the suffix lines.
        for index in range(lrange[1], min(len(lines), lrange[1] + suffix_lines)):
            partial_lines.append(f"[ ] {lines[index]}")
        return "".join(partial_lines)

    def get_content_via_lrange(self, lrange: typing.Tuple[int, int]) -> str:
        lines = self.new_file_content.splitlines(keepends=True)
        partial_lines = lines[lrange[0] : lrange[1]]
        return "".join(partial_lines)

    def get_prefix_via_lrange(self, lrange: typing.Tuple[int, int]) -> str:
        lines = self.new_file_content.splitlines(keepends=True)
        partial_lines = lines[: lrange[0]]
        return "".join(partial_lines)

    def get_suffix_via_lrange(self, lrange: typing.Tuple[int, int]) -> str:
        lines = self.new_file_content.splitlines(keepends=True)
        partial_lines = lines[lrange[1] :]
        return "".join(partial_lines)

    def validate_is_consecutive_edit(self) -> bool:
        """Validate the edit data."""
        deletes, adds = [], []
        for index, line in enumerate(self.lines):
            if line.origin == "-":
                deletes.append(index)
            elif line.origin == "+":
                adds.append(index)
        if deletes and not is_consecutive(deletes):
            return False
        if adds and not is_consecutive(adds):
            return False
        if not deletes and not adds:
            return False
        return True


@dataclasses.dataclass
class EditDataManager:
    """The manager for the edit data."""

    expected_del_range: typing.Tuple[int, int]
    """The expected number of deleted lines."""

    edit_data_list: typing.List[EditData]
    """The list of EditData."""

    max_data_per_add: int = 100
    """The maximum number of data for each #adds."""

    def __post_init__(self):
        """Initialize the manager."""
        self._del_add_to_indexes: typing.Dict[
            typing.Tuple[int, int], typing.List[int]
        ] = {}
        self._unique_paths: typing.Dict[str, int] = {}
        self._unique_code_hash: typing.Dict[str, int] = {}
        self._unique_path_code: typing.Dict[str, int] = {}
        for index, edit_data in enumerate(self.edit_data_list):
            deletes, adds = edit_data.get_diffs()
            if (deletes, adds) not in self._del_add_to_indexes:
                self._del_add_to_indexes[(deletes, adds)] = []
            self._del_add_to_indexes[(deletes, adds)].append(index)
            xpath = self._get_ed_path(edit_data)
            xhash = self._get_ed_code_hash(edit_data)
            self._unique_paths[xpath] = index
            self._unique_code_hash[xhash] = index
            self._unique_path_code[f"{xpath}.{xhash}"] = index

    def _get_ed_path(self, x: EditData):
        return f"{x.repo}@{x.file_path}"

    def _get_ed_code_hash(self, x: EditData) -> str:
        lines = [a.content for a in x.lines]
        lines = "".join(lines).lower()
        matches = re.finditer(r"[a-z=+\-*x]+", lines, re.I)
        lines = "".join(match.group() for match in matches)
        return hashlib.sha256(lines.encode()).hexdigest()

    def get_data_list(self, deletes: int, adds: int) -> typing.List[EditData]:
        """Get the data list for the given #deletes and #adds."""
        if (deletes, adds) not in self._del_add_to_indexes:
            return []
        return [
            self.edit_data_list[index]
            for index in self._del_add_to_indexes[(deletes, adds)]
        ]

    def append(
        self,
        edit_data: EditData,
        ignore_dup_file: bool = False,
        ignore_dup_code: bool = False,
        ignore_dup_path_code: bool = False,
        verbose: bool = True,
    ) -> bool:
        deletes, adds = edit_data.get_diffs()
        if not (self.expected_del_range[0] <= deletes <= self.expected_del_range[1]):
            raise ValueError(
                f"Expect #deletes in {self.expected_del_range}, but got {deletes}"
            )
        del_add_key = (deletes, adds)
        if del_add_key not in self._del_add_to_indexes:
            self._del_add_to_indexes[del_add_key] = []
        # Check whether we have the duplication
        cxpath = self._get_ed_path(edit_data)
        cxhash = self._get_ed_code_hash(edit_data)
        if (
            self.max_data_per_add >= 0
            and len(self._del_add_to_indexes[del_add_key]) >= self.max_data_per_add
        ):
            if verbose:
                number = len(self._del_add_to_indexes[del_add_key])
                print(
                    "The maximum number of data for {(deletes, adds)}"
                    f" : {number} vs. {self.max_data_per_add}."
                )
            return False
        elif ignore_dup_file and cxpath in self._unique_paths:
            if verbose:
                print(f"Ignore due to duplicate path: {cxpath}.")
            return False
        elif ignore_dup_code and cxhash in self._unique_code_hash:
            if verbose:
                print(f"Ignore due to duplicate code: {cxhash}.")
            return False
        elif ignore_dup_path_code and f"{cxpath}.{cxhash}" in self._unique_path_code:
            if verbose:
                print(f"Ignore due to duplicate: {cxpath} + {cxhash}.")
            return False
        else:
            cur_index = len(self.edit_data_list)
            self.edit_data_list.append(edit_data)
            self._del_add_to_indexes[del_add_key].append(cur_index)
            self._unique_paths[cxpath] = cur_index
            self._unique_code_hash[cxhash] = cur_index
            self._unique_path_code[f"{cxpath}.{cxhash}"] = cur_index
            return True

    def show_count(self):
        print(
            f"There are {len(self.edit_data_list)} examples and"
            f" {len(self._unique_paths)} unique files"
            f" for this manager[{self.expected_del_range}]."
        )
        counts = []
        for (deletes, adds), indexes in self._del_add_to_indexes.items():
            counts.append((deletes, adds, len(indexes)))
        counts = sorted(counts)
        for deletes, adds, num_indexes in counts:
            print(f"deleted={deletes:02d} & added={adds:02d}: count = {num_indexes:3d}")

    def __len__(self):
        return len(self.edit_data_list)

    def save_to_jsonl(self, output_file: str):
        """Save the edit data into jsonl."""
        with zstd.open(output_file, "w", encoding="utf-8") as f:
            json.dump(
                {
                    "expected_del_range": self.expected_del_range,
                    "max_data_per_add": self.max_data_per_add,
                },
                f,
            )
            f.write("\n")
            for data in tqdm.tqdm(
                self.edit_data_list, total=len(self), desc="dump to jsonl"
            ):
                json.dump(dataclasses.asdict(data), f)
                f.write("\n")
                f.flush()

    @classmethod
    def read_from_jsonl(cls, input_file: str) -> "EditDataManager":
        with zstd.open(input_file, "r") as f:
            datas = [json.loads(line) for line in f]
        assert len(datas) >= 1
        expected_del_range = datas[0]["expected_del_range"]
        max_data_per_add = datas[0]["max_data_per_add"]
        manager = EditDataManager(
            expected_del_range=expected_del_range,
            edit_data_list=[],
            max_data_per_add=max_data_per_add,
        )
        for data in tqdm.tqdm(datas[1:], total=len(datas) - 1, desc="load from jsonl"):
            manager.append(utils_for_dataclass.create_from_dict(EditData, data))
        return manager


def simple_git_diff(
    old_contents: str, new_contents: str
) -> tuple[tuple[int, int], tuple[int, int]]:
    old_lines = old_contents.splitlines(keepends=True)
    new_lines = new_contents.splitlines(keepends=True)
    old_l, old_r = 0, len(old_lines) - 1
    new_l, new_r = 0, len(new_lines) - 1

    # Find the starting line where the difference begins
    while old_l <= old_r and new_l <= new_r and old_lines[old_l] == new_lines[new_l]:
        old_l += 1
        new_l += 1

    # Find the ending line where the difference ends
    while old_l <= old_r and new_l <= new_r and old_lines[old_r] == new_lines[new_r]:
        old_r -= 1
        new_r -= 1

    # Return the ranges of differences for old and new contents
    return ((old_l, old_r), (new_l, new_r))


def convert_commitpackft_to_edit(data: dict) -> typing.Optional[EditData]:
    """Convert the commitpackft to the edit data type.

    Raw Data: https://huggingface.co/datasets/bigcode/commitpackft.
    """
    if data["old_contents"] == data["new_contents"]:
        return None
    old_lines = data["old_contents"].splitlines(keepends=True)
    new_lines = data["new_contents"].splitlines(keepends=True)
    old_diff, new_diff = simple_git_diff(data["old_contents"], data["new_contents"])
    lines: list[Line] = []
    for idx in range(old_diff[0], old_diff[1] + 1):
        lines.append(Line(old_lines[idx], "-"))
    for idx in range(new_diff[0], new_diff[1] + 1):
        lines.append(Line(new_lines[idx], "+"))

    edit_data = EditData(
        repo=data["repos"],
        commit_sha=data["commit"],
        file_path=data["new_file"],
        lines=lines,
        old_file_content=data["old_contents"],
        new_file_content=data["new_contents"],
        old_hunk_range=(old_diff[0] + 1, old_diff[1] + 2),
        new_hunk_range=(new_diff[0] + 1, new_diff[1] + 2),
        language=data["lang"],
        instruction=data["subject"],
    )
    return edit_data


def is_consecutive(lst: typing.List[int]):
    return all(b - a == 1 for a, b in zip(lst, lst[1:]))


def show_lines(lines: typing.List[Line]):
    for line in lines:
        pure_line = f"[{line.origin}] " + line.content
        if line.origin == " ":
            print(pure_line, end="")
        elif line.origin == "+":
            print(termcolor.colored(pure_line, color="green"), end="")
        elif line.origin == "-":
            print(termcolor.colored(pure_line, color="red"), end="")
        else:
            raise ValueError(f"Invalid line origin: {line.origin}")


def remove_editdata_via_fn(
    edit_data: typing.List[EditData], fn: typing.Callable[[EditData], bool]
) -> typing.List[EditData]:
    final_data: typing.List[EditData] = []
    for x in edit_data:
        if not fn(x):
            final_data.append(x)
    return final_data


def keep_editdata_via_fn(
    edit_data: typing.List[EditData], fn: typing.Callable[[EditData], bool]
) -> typing.List[EditData]:
    final_data: typing.List[EditData] = []
    for x in edit_data:
        if fn(x):
            final_data.append(x)
    return final_data


def get_lines_and_delete_line_indexes(
    data: EditData,
) -> typing.Tuple[typing.List[str], typing.Tuple[int, ...]]:
    data = copy.deepcopy(data)
    old_content = data.old_file_content
    lines = old_content.splitlines(keepends=True)
    start = data.old_hunk_range[0] - 1
    indexes: typing.List[int] = []
    for _, hunk_line in enumerate(data.lines):
        if hunk_line.origin == "-":
            indexes.append(start)
        if hunk_line.origin in (" ", "-"):
            start += 1
    return lines, tuple(indexes)
