"""Organize Data for Edit.

python experimental/dxy/edits/organize_data.py \
    --input_dir /mnt/efs/augment/user/dxy/datasets/edit/ \
    --output_dir /home/<USER>/datasets/edit/organized/ \
    --deleted_range "8 8" --max_per_add 100

python experimental/dxy/edits/organize_data.py \
    --input_dir /mnt/efs/augment/user/dxy/datasets/edit/ \
    --output_dir /home/<USER>/datasets/edit/organized/ \
    --deleted_range "11 20" --max_per_add 20

bash experimental/dxy/edits/scripts/organize.sh
"""
import argparse
import pathlib
import typing

import tqdm

from experimental.dxy.edits import data_type
from research.core import utils_for_dataclass, utils_for_file


def create_edit_data_manager(
    source_dir: str, delete_range: typing.Tuple[int, int], max_per_add: int
):
    """Create the edit data manager."""
    manager = data_type.EditDataManager(
        expected_del_range=delete_range, edit_data_list=[], max_data_per_add=max_per_add
    )
    data_dir = pathlib.Path(
        f"{source_dir}/raw-cache-del_{delete_range[0]:02d}_{delete_range[1]:02d}"
    )
    assert data_dir.exists(), data_dir
    jsonl_files = sorted(
        list(data_dir.glob("*-to-*-in-*.jsonl.zst")), key=lambda x: str(x)
    )
    jsonl_files = [str(x) for x in jsonl_files]
    print(f"There are {len(jsonl_files)} files in total.")
    for jsonl_file in tqdm.tqdm(jsonl_files):
        batch_of_json_str = utils_for_file.read_jsonl_zst(jsonl_file)
        print(f"There are {len(batch_of_json_str)} elements from {jsonl_file}.")
        successes = []
        for json_str in batch_of_json_str:
            edit_data = utils_for_dataclass.create_from_json(
                data_type.EditData, json_str
            )
            success = manager.append(
                edit_data,
                ignore_dup_file=False,
                ignore_dup_code=False,
                ignore_dup_path_code=True,
                verbose=False,
            )
            successes.append(success)
        print(
            f"Successfully insert {sum(successes)} / {len(batch_of_json_str)} data."
            f" Current manager size: {len(manager)}."
        )
        if not any(successes):
            break
    return manager


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="The input directory",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="The output directory",
    )
    parser.add_argument(
        "--deleted_range",
        type=str,
        required=True,
        help="",
    )
    parser.add_argument(
        "--max_per_add",
        type=int,
        required=True,
        help="",
    )
    args = parser.parse_args()

    input_dir = pathlib.Path(args.input_dir)
    assert input_dir.exists()
    output_dir = pathlib.Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    print(f"output_dir: {output_dir}", flush=True)
    deleted_ranges = args.deleted_range.split(" ")
    assert len(deleted_ranges) == 2
    deleted_ranges = (int(deleted_ranges[0]), int(deleted_ranges[1]))
    manager = create_edit_data_manager(str(input_dir), deleted_ranges, args.max_per_add)
    manager.show_count()
    if args.max_per_add < 0:
        output_file = (
            output_dir
            / f"fullmanager-del_{deleted_ranges[0]:02d}_{deleted_ranges[1]:02d}-len({manager}).jsonl.zst"
        )
    else:
        output_file = (
            output_dir
            / f"manager-del_{deleted_ranges[0]:02d}_{deleted_ranges[1]:02d}-per{args.max_per_add}.jsonl.zst"
        )
    print(f"Try to write the manager into {output_file}", flush=True)
    manager.save_to_jsonl(str(output_file))
    print(f"Finish the saving of the manager into {output_file}", flush=True)
