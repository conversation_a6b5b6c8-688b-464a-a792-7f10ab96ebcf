"""Util functions for code edit."""
import collections
import copy
import pathlib
import random
import typing

import numpy as np
import tqdm

from base.ranges.range_types import LineRange
from experimental.dxy.edits.sample_lib import SimpleRawEditScope
from research.core import constants, utils_for_file, utils_for_str
from research.eval.generation.execution import sandbox_execute
from research.static_analysis.usage_analysis import ParsedFile


def pack_sequences(
    examples: list[list[int]],
    seq_len: int,
    pad_id: int,
) -> tuple[list[list[int]], int]:
    """Pack shorter examples into longer ones."""
    final_sequences: list[list[int]] = []
    paddings, skip_over_length = [], 0
    current_batch: typing.Optional[list[int]] = None
    for ex in examples:
        if len(ex) >= seq_len:
            skip_over_length += 1
            continue
        if current_batch is None:
            current_batch = list(copy.deepcopy(ex))
            continue
        elif len(current_batch) > seq_len:
            raise ValueError("Impossible")
        elif len(current_batch) + len(ex) > seq_len:
            paddings.append(seq_len - len(current_batch))
            current_batch.extend([pad_id] * (seq_len - len(current_batch)))
            final_sequences.append(current_batch)
            # Reset the current batch
            current_batch = list(copy.deepcopy(ex))
        else:
            current_batch.extend(ex)
    if current_batch is not None:
        paddings.append(seq_len - len(current_batch))
        current_batch.extend([pad_id] * (seq_len - len(current_batch)))
        final_sequences.append(current_batch)
    print(f"Skip {skip_over_length} examples due to over-{seq_len}-tokens.")
    print(
        f"Packed {len(examples)} examples into {len(final_sequences)} {seq_len}-length-sequences."
    )
    print(f"On average, the number of paddings is {np.mean(paddings):.2f}.")
    return final_sequences, skip_over_length


def pad_sequences(
    examples: list[list[int]],
    seq_len: int,
    pad_id: int,
) -> tuple[list[list[int]], int]:
    """Pad shorter examples into longer ones."""
    final_sequences: list[list[int]] = []
    paddings, skip_over_length = [], 0
    for ex in examples:
        if len(ex) >= seq_len:
            skip_over_length += 1
            continue
        final_sequences.append(copy.deepcopy(ex) + [pad_id] * (seq_len - len(ex)))
        paddings.append(seq_len - len(ex))
    print(f"Skip {skip_over_length} examples due to over-{seq_len}-tokens.")
    print(
        f"Packed {len(examples)} examples into {len(final_sequences)} {seq_len}-length-sequences."
    )
    print(f"On average, the number of paddings is {np.mean(paddings):.2f}.")
    return final_sequences, skip_over_length


def load_instruct_coder_data(
    data_path: pathlib.Path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/additional_seed.json"
    ),
) -> list[SimpleRawEditScope]:
    """Load the Instruct Coder data."""
    data_list = utils_for_file.read_json(data_path)
    results: list[SimpleRawEditScope] = []
    for index, data in tqdm.tqdm(
        enumerate(data_list), total=len(data_list), desc="InstructCoder Load"
    ):
        miscs = {}
        for key in data:
            if key not in ("instruction", "input", "output"):
                miscs[key] = data[key]
        instruction = data["instruction"]
        input_str, output_str = data["input"], data["output"]
        input_lines, output_lines = input_str.splitlines(
            keepends=True
        ), output_str.splitlines(keepends=True)
        p_l, s_l = utils_for_str.get_shared_prefix_suffix_lines(input_str, output_str)
        data = SimpleRawEditScope(
            repo="InstructCoder",
            file_name=f"data_path.stem+{index}",
            content=input_str,
            scope=LineRange(p_l, len(input_lines) - s_l),
            middle_is_selected_code=True,
            misc={
                "updated_code": "".join(output_lines[p_l : len(output_lines) - s_l]),
                "instruction": instruction,
                **miscs,
            },
        )
        results.append(data)
    return results


def load_edit_eval_tests(
    data_dir: pathlib.Path = constants.AUGMENT_ROOT
    / "research"
    / "eval"
    / "edit"
    / "data",
) -> list[SimpleRawEditScope]:
    """The edit eval tests."""
    source_files = list(data_dir.glob("*.txt"))
    source_files = sorted(source_files)
    print(f"Loaded {len(source_files)} source files from {data_dir}")
    all_data: list[SimpleRawEditScope] = []
    for source_file in tqdm.tqdm(source_files, total=len(source_files)):
        source_code = source_file.read_text()
        python_file = source_file.with_suffix(".py")
        assert python_file.exists()
        python_content = python_file.read_text()
        _, _, cur_edit_data = sandbox_execute(python_content, 1.0, ["edits"])
        cur_edit_data = cur_edit_data["edits"]
        # source_lines = source_code.splitlines(keepends=True)
        for edit_data in cur_edit_data:
            lrange = edit_data["lrange"]
            instructions = edit_data["instruction"]
            assert isinstance(
                instructions, list
            ), f"type(instructions): {type(instructions)}"
            response = edit_data["response"]
            refuse_edit = edit_data.get("refuse_edit", False)
            for instruction in instructions:
                assert 0 <= lrange[0] <= lrange[1]
                data = SimpleRawEditScope(
                    repo="",
                    file_name=str(source_file),
                    content=source_code,
                    scope=LineRange(lrange[0], lrange[1]),
                    middle_is_selected_code=True,
                    misc={
                        "updated_code": response,
                        "instruction": instruction,
                        "refuse_edit": refuse_edit,
                    },
                )
                if "misc" in edit_data:
                    for k, v in edit_data["misc"].items():
                        assert k not in data.misc
                        data.misc[k] = v
                all_data.append(data)
        # print(
        #     f"Finish processing {source_file} ({len(all_data)} edit examples in total)"
        # )
    print(f"Loaded {len(all_data)} examples.")
    return all_data


def load_repo_data(
    data_dir: pathlib.Path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/edit.raw/cache-github-file"
    ),
) -> list[tuple[str, ParsedFile]]:
    """Load the repo data."""
    assert data_dir.exists()
    print(f"Loading repo data from {data_dir}...")
    org_dirs = list(data_dir.iterdir())
    all_data_list: list[tuple[str, ParsedFile]] = []
    for org_dir in tqdm.tqdm(org_dirs, total=len(org_dirs)):
        repo_files = list(org_dir.glob("*.jsonl.zst"))
        for repo_file in repo_files:
            cur_data_list = utils_for_file.read_jsonl_zst(repo_file)
            repo_name = repo_file.name.split(".jsonl.zst")[0]
            for cur_data in cur_data_list:
                all_data_list.append(
                    (
                        f"{org_dir.name}/{repo_name}",
                        ParsedFile.parse(
                            path=pathlib.Path(cur_data["file_path"].split(":")[-1]),
                            lang=cur_data["lang"],
                            code=cur_data["content"],
                        ),
                    )
                )
    print(f"Loaded {len(all_data_list)} examples.")
    return all_data_list


def sample_w_constraint(
    samples: list[SimpleRawEditScope],
    constraints: dict[
        str, typing.Tuple[typing.Callable[[SimpleRawEditScope], bool], int]
    ],
) -> list[SimpleRawEditScope]:
    """"""
    cur_num = collections.defaultdict(int)
    samples = copy.deepcopy(samples)
    random.shuffle(samples)
    final_samples: list[SimpleRawEditScope] = []
    for sample in samples:
        include_sample = True
        for key, (func, max_num) in constraints.items():
            if func(sample) and cur_num[key] >= max_num:
                include_sample = False
                break
        if include_sample:
            final_samples.append(sample)
            for key, (func, max_num) in constraints.items():
                if func(sample):
                    cur_num[key] += 1
    return final_samples


# if __name__ == "__main__":
#     # data = load_repo_data()
#     data = load_instruct_coder_data()
#     import pdb

#     pdb.set_trace()
#     print("-")
