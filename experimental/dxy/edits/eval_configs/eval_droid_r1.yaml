# At the augment root directory:
#
# python research/eval/eval.py --local experimental/dxy/edits/eval_configs/eval_droid_r1.yaml
#
# or run on determined without --local.
system:
  name: remote_edit

  # We explicitly set this since otherwise we will select the default
  # model, which is not an edit model.
  model_name: droid-33B-FP8-R1-edit

  client:
    # Set a url to run the remote system.
    # url: https://dogfood.api.augmentcode.com
    url: https://dev-dxy.us-central.api.augmentcode.com

    # If not running on determined, the client searchs for the API token
    # in $AUGMENT_TOKEN or ~/.config/augment/api_token
    # To generate your own API token, see
    # https://www.notion.so/Runbook-How-to-generate-API-tokens-a7ede88059604149867f03c2cf6f434b
    # api_token_env_var: AUGMENT_TOKEN
    # api_token_path: ~/.config/augment/api_token

    # These control global retry settings for the client.
    # Likely something is wrong if you need to change these, but if your pods
    # are restarting often, you could try increasing retry_sleep or retry_count.
    # timeout: 60
    # retry_count: 2
    # retry_sleep: 0.1

  retriever:
    # Supported languages will be reported back as [] for edit hosts, so just
    # allow all file types.
    disable_extension_filtering: True

  edit:
    # Currently there is a hard limit of 1536 tokens (avg 3 char/token) for
    # selected text, but this will be gone with the new dynamic prompt budget.
    max_selected_text_chars: 4608


task:
  name: edit_eval_task

podspec: cpu-only.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: clean_basic_task, droid-33B-FP8-R1-edit, remote, dev
  project: Xuanyi-Dong
  workspace: Dev
