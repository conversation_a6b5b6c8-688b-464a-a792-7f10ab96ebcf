"""The edit prompt template."""
import functools
import random
import typing

from megatron.tokenizer.tokenizer import AbstractTokenizer

from experimental.dxy.edits.data_type import EditData
from research.core import utils_for_str


class EditPromptTemplate:
    """The edit prompt template."""

    @classmethod
    @functools.cache
    def get_input_templates(cls):
        template_01 = """Here are some codes:
```
{prefix}************************
{selected_code}************************
{suffix}```
please suggest modifications to (optionally empty) codes enclosed by consequent `*` that align with this instruction: '{instruction}'"""
        template_02 = """Considering to update the code snippets between the consequent `-` (which may be empty), here are the full codes:
```
{prefix}--------------------------
{selected_code}--------------------------
{suffix}```
Based on the direction: '{instruction}', how should it be revised?"""
        template_03 = """Within this code block:
```
{prefix}++++++++++++++++++++++++++
{selected_code}++++++++++++++++++++++++++
{suffix}```
Identify and modify the segment between the + signs following the guideline: '{instruction}'."""
        template_04 = """Given the below codes:
```
{prefix}//////////////////////////
{selected_code}//////////////////////////
{suffix}```
The portion within the `//` symbols needs adjustment. Instruction: '{instruction}'. What's your recommendation?"""
        template_05 = """Review the enclosed code segment between `=` in this code:
```
{prefix}==========================
{selected_code}==========================
{suffix}```
With the provided instruction: '{instruction}', how would you alter it?"""
        template_06 = """Please evaluate the code snippet enclosed by `#` symbols below:
```
{prefix}##########################
{selected_code}##########################
{suffix}```
And make changes as per this directive: '{instruction}'."""
        template_07 = """Take a look at the codes and focus on the part between `@` symbols:
```
{prefix}@@@@@@
{selected_code}@@@@@@
{suffix}```
How should it be refined based on the instruction: '{instruction}'?"""
        template_08 = """The code below contains a segment enclosed by `^`:
```
{prefix}^^^^^^^^^^^^^^^^^^
{selected_code}^^^^^^^^^^^^^^^^^^
{suffix}```
Please suggest updates in line with: '{instruction}'."""

        return (
            template_01,
            template_02,
            template_03,
            template_04,
            template_05,
            template_06,
            template_07,
            template_08,
        )

    @classmethod
    @functools.cache
    def get_target_templates(cls):
        template_01 = """Here is the updated version:
```
{modified_code}```"""
        template_02 = """The modified codes should be :
```
{modified_code}```"""
        template_03 = """The revised codes are
```
{modified_code}```"""
        template_04 = """The updated codes are
```
{modified_code}```"""
        template_05 = """Taking your instructions into consideration, here's the altered code:
```
{modified_code}```"""
        template_06 = """I've made the necessary modifications:
```
{modified_code}```"""
        template_07 = """Your code has been updated as follows:
```
{modified_code}```"""
        template_08 = """Here's the code with the suggested changes:
```
{modified_code}```"""
        template_09 = """The adjusted code now looks like this:
```
{modified_code}```"""
        template_10 = """Here are the changes applied to your code:
```
{modified_code}```"""
        return (
            template_01,
            template_02,
            template_03,
            template_04,
            template_05,
            template_06,
            template_07,
            template_08,
            template_09,
            template_10,
        )

    @classmethod
    @functools.cache
    def get_num_templates(cls) -> typing.Tuple[int, int]:
        return len(cls.get_input_templates()), len(cls.get_target_templates())

    @classmethod
    def create_prompt(
        cls,
        edit: EditData,
        instruction: str,
        tokenizer: AbstractTokenizer,
        input_template_index: typing.Optional[int] = None,
        target_template_index: typing.Optional[int] = None,
    ) -> tuple[list[int], list[int]]:
        """Create the prompt for the edit.

        Args:
            edit (EditData): The edit data.
            instruction (str): The instruction.
            tokenizer (AbstractTokenizer): The tokenizer.

        Returns:
            prompt (list[int]): The prompt tokens.
            target (list[int]): The target tokens.
        """
        old_code = edit.get_selected_code()
        new_code = edit.get_modified_code()
        lrange = edit.get_lrange()
        prefix = edit.get_prefix_via_lrange(lrange=lrange)
        prefix_last_15_lines = utils_for_str.get_last_n_lines(prefix, 15)
        suffix = edit.get_suffix_via_lrange(lrange=lrange)
        suffix_first_15_lines = utils_for_str.get_first_n_lines(suffix, 15)
        B_INST, E_INST = "[INST]", "[/INST]"

        all_input_templates = cls.get_input_templates()
        if input_template_index is None:
            input_template = random.choice(all_input_templates)
        else:
            input_template = all_input_templates[input_template_index]
        all_target_templates = cls.get_target_templates()
        if target_template_index is None:
            target_template = random.choice(all_target_templates)
        else:
            target_template = all_target_templates[target_template_index]
        prompt = input_template.format(
            prefix=prefix_last_15_lines,
            suffix=suffix_first_15_lines,
            selected_code=old_code,
            instruction=instruction,
        )
        target = target_template.format(modified_code=new_code)
        return (
            tokenizer.tokenize(f"{B_INST}\n{prompt}\n{E_INST}"),
            tokenizer.tokenize(target),
        )

    @classmethod
    def create_prompt_v2(
        cls,
        edit: EditData,
        tokenizer: AbstractTokenizer,
        num_of_context_lines: int = 60,
    ) -> tuple[list[int], list[int]]:
        """Create the prompt for the edit.

        Args:
            edit (EditData): The edit data.
            tokenizer (AbstractTokenizer): The tokenizer.
            num_of_context_lines (int): The number of context lines.

        Returns:
            prompt (list[int]): The prompt tokens.
            target (list[int]): The target tokens.
        """
        assert edit.instruction != ""
        old_code = edit.get_selected_code()
        new_code = edit.get_modified_code()
        lrange = edit.get_lrange()
        prefix = edit.get_prefix_via_lrange(lrange=lrange)
        prefix_last_few_lines = utils_for_str.get_last_n_lines(
            prefix, num_of_context_lines
        )
        suffix = edit.get_suffix_via_lrange(lrange=lrange)
        suffix_first_few_lines = utils_for_str.get_first_n_lines(
            suffix, num_of_context_lines
        )
        input_template = "<INST>{instruction}<SELECTED>{selected_code}<SUFFIX>{suffix}<PREFIX>{prefix}<UPDATED>"

        prompt = input_template.format(
            prefix=prefix_last_few_lines,
            suffix=suffix_first_few_lines,
            selected_code=old_code,
            instruction=edit.instruction,
        )
        target_template = r"""```
{modified_code}```"""
        target = target_template.format(modified_code=new_code)
        return (
            tokenizer.tokenize(prompt),
            tokenizer.tokenize(target),
        )
