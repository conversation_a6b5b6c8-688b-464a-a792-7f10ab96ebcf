# CUDA_VISIBLE_DEVICES=0 python research/model_server/launch_model_server.py \
#    --system_yaml_config experimental/dxy/edits/server_configs/droid_v1_33b.yaml \
#    --host 0.0.0.0 --port 5001
#
# CUDA_VISIBLE_DEVICES=1 python eval.py -i ./data -s RemoteFlaskCodeEditSystem -o ./result-droid-v1.0-33b
#
name: droid_code_edit
model:
  name: fastforward_droid
  checkpoint_path: "/mnt/efs/augment/user/dxy/logs/edit.01.24/Droid33B.38.39.Vendor36-S8K_E1-b4x8x2x1-lr_constant_1e-5-ffw"
  prompt:
    # Total size of the prompt
    max_prompt_tokens: 6656  # this is a mistake -- fix later
    # The fraction of the prompt for the input (prefix/selected_code/suffix/path/instruction), but not including retrieved chunks.
    input_fraction: 1.0
    # The fraction of the input that should be apportioned to the selected code, the rest is equally divided between prefix and suffix.
    selected_code_fraction: 0.4
    max_instruction_tokens: 512
generation_options:
  temperature: 0
  top_k: 0
  top_p: 0
  max_generated_tokens: 1536
