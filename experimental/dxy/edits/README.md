# Edit Codes

## Data Preparation

Please have a look at each script before running and change some "dxy"-specific folders to your own.

**Step 1**: parse and organize the raw GitHub commit data.

```python
# See details at experimental/dxy/edits/prepare_data.py
# Update the paths that included dxy in the above python file.
python experimental/dxy/edits/prepare_data.py --mode check --date 2023-10-17
python experimental/dxy/edits/prepare_data.py --mode download --date 2023-10-17
python experimental/dxy/edits/prepare_data.py --mode filter --date 2023-10-17
python experimental/dxy/edits/prepare_data.py --mode convert --date 2023-10-17
```

**Step 2**: re-organize the entire data

```bash
bash experimental/dxy/edits/scripts/organize.sh
```

This script is to split data based the range of number of deleted lines, i.e., `0~0`, `1~1`, etc.

**Step 3**: reversely generate the instruction

```python
python experimental/dxy/edits/synthesize_instruction.py --identifier del_00_00-per40
```

**Step 4**: build the dataset for the fine-tuning pipeline

Run this notebook `experimental/dxy/notebooks/edit/create-dataset-2023-10-17.ipynb` to build
the final IndexedDataset for the fine-tuning pipeline.

## Fine-tuning

**Single node training:**

```bash
# Launch a single pod
./launch_pod.py --cluster CW --pod_name TEMP-8-h100-3 --gpu_type H100_NVLINK_80GB --gpu_count 8 --memory 768 --cpu_count 96

# Log into the machine and launch the training job
cd ~/src

<NAME_EMAIL>:D-X-Y/augment.git

torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \
 --vocab_size=32016 --rope_theta=1000000.0 \
 --learning_rate=3e-5 --decay_lr=False \
 --max_iters=5000 --batch_size=4 \
 --eval_interval=50 \
 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \
 --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train_r1n_s4096_onlytgt \
 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_onlytgt \
 --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \
 --wandb_log=True --wandb_project=edit-exps --wandb_run_name=CodeLLaMA-7B-Data_s4k_onlytgt-5000s-lr_00003
```

**Inference:**

```python
# Load the checkpoint directory
from research.models.llama2_models import LLAMA2Model

model = LLAMA2Model(
    checkpoint_path=pathlib.Path(
        "/mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-Data_s4k_onlytgt-5000s-lr_00001/ckp_in_llama_format"
    )
)
model.load()
```

**Miscellaneous:**

See `experimental/dxy/notebooks/edit/explore-2023-10-24.ipynb` to play with some data.

## Servering

```python
python experimental/dxy/demo/launch_flask.py
```
