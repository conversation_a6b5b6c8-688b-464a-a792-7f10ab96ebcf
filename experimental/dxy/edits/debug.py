"""The debug file."""
from experimental.dxy.edits.pipelines.generate_both import (
    GenerateInstructionAndSelection,
)
from experimental.dxy.edits.sample_lib import find_edit_data_scope
from experimental.dxy.edits.util_lib import load_edit_eval_tests, load_repo_data

if __name__ == "__main__":
    tests = load_edit_eval_tests()
    # check the prefix, middle, and suffix
    for test in tests:
        _ = test.prefix
        _ = test.middle
        _ = test.suffix
    raw_repo_data = load_repo_data()
    raw_edit_scopes = find_edit_data_scope(raw_repo_data)
    pipeline = GenerateInstructionAndSelection(DEBUG=True)
    sythetic_samples = pipeline.generate(raw_edit_scopes[0], tests, [])
