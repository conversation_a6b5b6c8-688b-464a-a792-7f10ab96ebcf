"""Debug file.

python experimental/dxy/edits/notebooks/random/test.py
"""
import copy
import pathlib
import pdb

from megatron.tokenizer.tokenizer import (
    AbstractTokenizer,
    DeepSeekCoderBaseTokenizer,
    DeepSeekTokenizer,
)

from research.core.model_input import ModelInput
from research.core.prompt_formatters import Abstract<PERSON>rom<PERSON><PERSON><PERSON>atter
from research.data.synthetic_code_edit.seed_lib import load_code_edit_seeds
from research.models.llama2_models import FastBackwardDeepSeekModel
from research.models.meta_model import GenerationOptions


class DroidFormatter(AbstractPromptFormatter):
    """Prompt formatter for Dr<PERSON>, a code instruct model."""

    max_prefix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    max_suffix_tokens: int = 1536
    """Maximum number of tokens in the prefix."""

    def create_default_tokenizer(self) -> AbstractTokenizer:
        """A function to create the default tokenizer."""
        return DeepSeekCoderBaseTokenizer()

    def prepare_prompt(
        self,
        model_input: ModelInput,
    ) -> tuple[list[int], dict]:
        if model_input.retrieved_chunks:
            raise NotImplementedError(f"Do not support retrieved chunks for {self}.")
        if self.preamble:
            raise NotImplementedError(f"Do not support preamble for {self}.")
        tokenizer = self.tokenizer
        assert isinstance(tokenizer, DeepSeekTokenizer)

        prompt = (
            [tokenizer.bos_id]
            + tokenizer.tokenize(model_input.extra["instruction"])
            + [tokenizer.fim_prefix_id]
            + tokenizer.tokenize(model_input.prefix)[-self.max_prefix_tokens :]
            + [tokenizer.fim_suffix_id]
            + tokenizer.tokenize(model_input.suffix)[: self.max_suffix_tokens]
            + [tokenizer.fim_middle_id]
            + tokenizer.tokenize(model_input.extra["selected_code"])
            + [tokenizer.pause_id]
        )

        return prompt, {}


def main():
    seeds = load_code_edit_seeds()
    print(f"Loaded {len(seeds)} examples.")
    ckp_path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/logs/edit.12.21/DSC-B-ALL-S4K_WUP0_110-WD1_0-b4x8x2x1-lr_2e-5_constant/checkpoint_llama_iteration_120"
    )
    model = FastBackwardDeepSeekModel(ckp_path, seq_length=4096, model_parallel_size=4)
    model.load()
    for category, examples in seeds.items():
        print(f"Category: {category}")
        for example in examples:
            example = copy.deepcopy(example)
            assert example.instructions is not None
            minput = ModelInput(
                prefix=example.prefix,
                suffix=example.suffix,
                extra={
                    "instruction": example.instructions[0].text,
                    "selected_code": example.selected_code,
                },
            )
            prompt, _ = DroidFormatter().prepare_prompt(minput)
            result = model.raw_generate(
                prompt, GenerationOptions(max_generated_tokens=512, top_k=None)
            )
            pdb.set_trace()
            print(prompt)
            print(result)


if __name__ == "__main__":
    main()
