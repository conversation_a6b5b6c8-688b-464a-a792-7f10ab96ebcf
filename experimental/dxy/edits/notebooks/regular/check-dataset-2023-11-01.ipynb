{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import re\n", "import termcolor\n", "import random\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "\n", "tokenizer = Llama2Tokenizer()\n", "\n", "\n", "def filter_string(s):\n", "    return \"\".join([char for char in s.lower() if char.isalpha() or char == \" \"])\n", "\n", "\n", "def extract_content(input_string):\n", "    pattern = r\"<INST>(.*?)<SELECTED>\"\n", "    matches = re.findall(pattern, input_string, re.DOTALL)\n", "\n", "    # Strip each match of leading and trailing whitespaces\n", "    return [match.strip() for match in matches]\n", "\n", "\n", "def split_list(lst: list[int], delimiter: int):\n", "    result = []\n", "    current_subsequence = []\n", "    for item in lst:\n", "        if item == delimiter:\n", "            if current_subsequence:  # Avoid appending empty lists\n", "                result.append(current_subsequence)\n", "                current_subsequence = []\n", "        else:\n", "            current_subsequence.append(item)\n", "    if current_subsequence:  # Append the last subsequence if it's not empty\n", "        result.append(current_subsequence)\n", "    return result\n", "\n", "\n", "def show_dataset_info(dataset: MMapIndexedDataset):\n", "    print(f\"There are {len(dataset)} examples\")\n", "    indexes = [0, random.randint(0, len(dataset) - 1), len(dataset) - 1]\n", "    indexes = sorted(list(set(indexes)))\n", "    for index in indexes:\n", "        example = dataset[index]\n", "        negative_num = np.sum(example < 0).item()  # type: ignore\n", "        print(\n", "            termcolor.colored(\n", "                f\"Index={index:5d}, negative_num={negative_num}\", color=\"green\"\n", "            )\n", "        )\n", "        example: list[int] = np.abs(example).tolist()\n", "        parts = split_list(example, tokenizer.eos_id)\n", "        parts = [x for x in parts if x != [tokenizer.eos_id]]\n", "        print(\n", "            termcolor.colored(\n", "                f\"There are {len(parts)} unpacked examples.\", color=\"green\"\n", "            )\n", "        )\n", "        for j, part in enumerate(parts):\n", "            print(termcolor.colored(f\"Part {j:5d}/{len(parts):5d}\", color=\"blue\"))\n", "            results = tokenizer.detok<PERSON>ze(part)\n", "            print(results)\n", "            print(\"*\" * 100 + \"\\n\")\n", "        print(\"-\" * 100 + \"\\n\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "Get 115140 instructions.\n"]}], "source": ["dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_full_pack\"\n", ")\n", "instructions = []\n", "for i in range(len(dataset)):\n", "    example: list[int] = np.abs(dataset[i]).tolist()\n", "    text: str = tokenizer.detokenize(example)\n", "    for inst in extract_content(text):\n", "        instructions.append(inst)\n", "print(f\"Get {len(instructions)} instructions.\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["#unique_instructions = 45141\n", "#unique_v2_instructions = 44971\n"]}], "source": ["unique_instructions = set(instructions)\n", "print(f\"#unique_instructions = {len(unique_instructions)}\")\n", "unique_v2_instructions = list(set([filter_string(x) for x in unique_instructions]))\n", "print(f\"#unique_v2_instructions = {len(unique_v2_instructions)}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["add docs and autodocs tasks\n", "add tests for multiple gibson inserts\n", "refactor the function to check for references to remote and pendingreceiver in interfaces structs and unions and import them only if they are referenced\n", "remove unnecessary import of logging\n", "replace the createdomain method with a createstorageroot method\n", "add a new function to test the dimensions of the sram table and compare it with the expected values\n", "stop defining pluginunloaded functions that arent doing anything\n", "add created and modified fields to receipt\n", "fix model id to auto increment\n", "replace the index function with a userviewset class that uses django rest framework and connects to a user model\n", "import czmlversion packet preamble from czml and write tests for preamble\n", "fix data provider example file\n", "add more parameters to the function\n", "add run to the vm which iterates through all the bytecodes and executes them\n", "complete unfinished code committed by mistake\n", "add two new methods to compute the expected scale and the expected log scale of a connection averaging over c\n", "remove the bestninedepth function\n", "remove unused nhs database mockup\n", "replace fileio usage with open\n", "extend the toindex method to handle different data types like list and set not just numpy arrays\n", "modify the response creation and add a process to create a versions bucket if it fails delete the newly created bucket and handle the errors accordingly\n", "add a condition to check if the regressor is not dmdc when control input u is passed\n", "rearrange the code to check for <PERSON><PERSON><PERSON> first\n", "load the pretrained weights then do a warmup training after that load the warmup trained weights and start the actual training\n", "fix quick union functions issue\n", "handle the exception when getting data\n", "add the choose test and take test to urls\n", "remove logic for iterating directories to search for config file\n", "add a function to read a dataframe from a file\n", "replace the code for printing the arabic range with the printrange function\n"]}], "source": ["for index in range(30):\n", "    print(unique_v2_instructions[index])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "There are 2472 examples\n", "\u001b[32mIndex=    0, negative_num=4091\u001b[0m\n", "\u001b[32mThere are 1 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON>art     0/    1\u001b[0m\n", "<INST>remove the daily_reminder function<SELECTED>@client.command(aliases=[\"daily-reminder\", \"set-daily-alarm\"])\n", "async def daily_reminder(ctx, time):\n", "    # Takes input from the user (task) about what they would like to accomplish\n", "    # Send a reminder after 24 hours to see how far they have come up with the task\n", "    # At the end of every hour, ask the user if they have completed the task.\n", "    # Wait for the stop command from user (You may use \"client.wait_for\")\n", "    pass\n", "\n", "<SUFFIX>\n", "@client.command(aliases=[\"start-pomodoro\"])\n", "async def pomodoro(ctx):\n", "    # Set a timer for 25 minutes\n", "    time = 1500\n", "    # Notify the user after 25 minutes\n", "    message = await ctx.send(\"Your countdown has been started for 25 minutes\")\n", "    while True:\n", "        time -= 1\n", "        if time == 0:\n", "            await ctx.send(ctx.message.author.mention + \" your break has started.\")\n", "            break\n", "\n", "        await asyncio.sleep(1)\n", "    # Set the timer for 5 minutes (starting of the break)\n", "    time = 300\n", "    await message.edit(content=(\"Your break countdown has been started for 5 minutes\"))\n", "    while True:\n", "        time -= 1\n", "        if time == 0:\n", "            break\n", "\n", "        await asyncio.sleep(1)\n", "    # Notify the user after 5 minutes that Pomodoro has ended\n", "    await ctx.send(ctx.message.author.mention + \" Pomodoro has ended!\")\n", "\n", "def getQuote(tags=[\"inspirational\", \"success\"]):  # default arguments\n", "    #add tags to the url\n", "    url = \"https://api.quotable.io/random?tags=\"\n", "    for tag in tags:\n", "        url = url+tag+\"|\"\n", "    # get json response from the quoteable api\n", "    response = urllib.request.urlopen(url).read()\n", "    # Convert json response into a dictionary\n", "    response_dict = json.loads(response)\n", "    quote_author =  response_dict[\"author\"]\n", "    quote_text = response_dict[\"content\"]\n", "\n", "    return (quote_text,quote_author)\n", "\n", "\n", "@client.command(aliases=[\"quote\",\"motivation\"])\n", "async def motivational_quote(ctx,*tags):\n", "    # tags that are available in the quoteable api\n", "    AVAILABLE_TAGS = ['business', 'education', 'faith', 'famous-quotes', 'friendship', 'future', 'happiness',\n", "                      'history', 'inspirational', 'life', 'literature', 'love', 'nature', 'politics', 'proverb',\n", "                       'religion', 'science', 'success', 'technology', 'wisdom']\n", "    # check if the tags enterend as arguments are valid \n", "    quote = ()\n", "    if len(tags) > 0:\n", "        if set(tags).issubset(set(AVAILABLE_TAGS)):\n", "            quote = getQuote(tags=tags)\n", "        else:\n", "            await ctx.send(\"Invalid tag\\nthe available tags are:\\n\"+\", \".join(AVAILABLE_TAGS))\n", "            return\n", "    else:\n", "        quote = getQuote()\n", "    quote_text = \"\\\"\" + quote[0] + \"\\\"\"\n", "    quote_author = \"-\" + quote[1]   \n", "    # Make a discord embed with quote \n", "<PREFIX>        await asyncio.sleep(1)\n", "\n", "\n", "@client.command(aliases=[\"set-alarm\"])\n", "async def alarm(ctx, time):\n", "    # Take user input (time in 24-hour format), for example 23:00\n", "    # Check if it is a valid 24-hour format, and return an apppropriate message\n", "    # Parse the user input using time.split(\":\")\n", "    # Remind the user as soon as it's time\n", "    pass\n", "\n", "\n", "@client.command(aliases=[\"stopwatch\", \"start-stopwatch\"])\n", "async def stopwach(ctx):\n", "    # Start a stopwatch\n", "    # Wait for the stop command from user (You may use \"client.wait_for\")\n", "    pass\n", "\n", "\n", "@client.command(aliases=[\"hourly-reminder\", \"set-hourly-reminder\"])\n", "async def hourly_reminder(ctx, task):\n", "    # Takes input from the user (task) about what they would like to accomplish\n", "    # Send a reminder after one hour to see how far they have come up with the task\n", "    # At the end of every hour, ask the user if they have completed the task.\n", "    # Wait for the stop command from user (You may use \"client.wait_for\")\n", "    pass\n", "\n", "\n", "@client.command(aliases=[\"daily-reminder\", \"set-daily-alarm\"])\n", "async def daily_reminder(ctx,*, task):\n", "    # Takes input from the user (task) about what they would like to accomplish\n", "    await ctx.send(ctx.author.mention + f\" Task: {task}, saved successfully\")\n", "\n", "    # Start reminder loop\n", "    reminder_loop.start(ctx, task)\n", "\n", "\n", "@tasks.loop(minutes=60)\n", "async def reminder_loop(ctx, task):\n", "    # Send reminder and ask for confirmation\n", "    await ctx.send(ctx.author.mention + f\" Task: \\\"{task}\\\", should be completed today, \\nHave done it already?\")\n", "\n", "    # Check message source\n", "    def check(m):\n", "        return m.channel == ctx.channel and m.author == ctx.author\n", "\n", "    # Wait for user reply to cancel loop\n", "    try:\n", "        reply = await client.wait_for(\"message\", check=check, timeout=300)\n", "        if reply.content.lower() in [\"stop\", \"yes\", \"yep\", \"done\", \"sure\"]:\n", "            await ctx.send(ctx.author.mention + f\" Congratulations, you finished your daily task: \\n\\t\\\"{task}\\\"\")\n", "            reminder_loop.cancel()\n", "        else:\n", "            await ctx.send(ctx.author.mention + \" Okay, I'll remind you again in an hour\")\n", "\n", "    # Continue in case of timeout\n", "    except Exception:\n", "        await ctx.send(ctx.author.mention + \" I'll remind you again in an hour\")\n", "\n", "\n", "<UPDATED> ```\n", "```\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "\u001b[32mIndex=   66, negative_num=3913\u001b[0m\n", "\u001b[32mThere are 1 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON>art     0/    1\u001b[0m\n", "<INST>Add test for forward registering<SELECTED>from website.addons.forward.tests.factories import ForwardSettingsFactory\n", "\n", "<SUFFIX>\n", "class TestSettingsValidation(OsfTestCase):\n", "\n", "    def setUp(self):\n", "        super(TestSettingsValidation, self).setUp()\n", "        self.settings = ForwardSettingsFactory()\n", "\n", "    def test_validate_url_bad(self):\n", "        self.settings.url = 'badurl'\n", "        with assert_raises(ValidationError):\n", "            self.settings.save()\n", "\n", "    def test_validate_url_good(self):\n", "        self.settings.url = 'http://frozen.pizza.reviews/'\n", "        try:\n", "            self.settings.save()\n", "        except ValidationError:\n", "            assert 0\n", "\n", "\n", "    def test_label_sanitary(self):\n", "        self.settings.label = 'safe'\n", "        try:\n", "            self.settings.save()\n", "        except ValidationError:\n", "            assert False\n", "\n", "    def test_label_unsanitary(self):\n", "        self.settings.label = 'un<br />safe'\n", "        with assert_raises(ValidationError):\n", "            self.settings.save()\n", "<PREFIX># -*- coding: utf-8 -*-\n", "\n", "from nose.tools import *  # PEP8 asserts\n", "\n", "from modularodm.exceptions import ValidationError\n", "\n", "from tests.base import OsfTestCase\n", "<UPDATED> ```\n", "from tests.factories import ProjectFactory, RegistrationFactory\n", "from website.addons.forward.tests.factories import ForwardSettingsFactory\n", "\n", "\n", "class TestNodeSettings(OsfTestCase):\n", "\n", "    def setUp(self):\n", "        super(TestNodeSettings, self).setUp()\n", "        self.node = ProjectFactory()\n", "        self.settings = ForwardSettingsFactory(owner=self.node)\n", "        self.node.save()\n", "\n", "    def test_forward_registered(self):\n", "        registration = RegistrationFactory(project=self.node)\n", "        assert registration.has_addon('forward')\n", "        \n", "        forward = registration.get_addon('forward')\n", "        assert_equal(forward.url, 'http://frozen.pizza.reviews/')\n", "```\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "\u001b[32mIndex= 2471, negative_num=3735\u001b[0m\n", "\u001b[32mThere are 1 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON>art     0/    1\u001b[0m\n", "<INST>Add further tests of the cms urls<SELECTED>from incuna_test_utils.testcases.urls import URLTestCase\n", "\n", "from .. import views\n", "\n", "\n", "class TestCMSIndexURL(URLTestCase):\n", "    \"\"\"Make sure that the CMSIndex view has a URL\"\"\"\n", "    def test_url(self):\n", "        self.assert_url_matches_view(\n", "            views.CMSIndex,\n", "            '/cms/',\n", "            'cms:index',\n", "        )\n", "<SUFFIX><PREFIX><UPDATED> ```\n", "from unittest import mock\n", "\n", "from django.test import TestCase\n", "from incuna_test_utils.testcases.urls import URLTestCase\n", "\n", "from .. import urls, views\n", "\n", "\n", "class TestCMSIndexURL(URLTestCase):\n", "    \"\"\"Make sure that the CMSIndex view has a URL\"\"\"\n", "    def test_url(self):\n", "        self.assert_url_matches_view(\n", "            views.CMSIndex,\n", "            '/cms/',\n", "            'cms:index',\n", "        )\n", "\n", "\n", "class TestCMSURLs(TestCase):\n", "    @mock.patch('conman.cms.urls.url')\n", "    @mock.patch('conman.cms.urls.include')\n", "    @mock.patch('django.apps.apps.get_app_config')\n", "    def test_urls(self, get_app_config, include, url):\n", "        fake_config = mock.Mock()\n", "        fake_config.cms_urls = 'example.path.to.urls'\n", "        fake_config.label = 'example'\n", "\n", "        fake_config.managed_apps = {fake_config}\n", "        get_app_config.return_value = fake_config\n", "\n", "        cms_urls = list(urls.urls())\n", "        expected = [\n", "            url(r'^$', views.CMSIndex.as_view, name='index'),\n", "            url(r'^example', include(fake_config.cms_urls))\n", "        ]\n", "        self.assertSequenceEqual(cms_urls, expected)\n", "```\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad\"\n", ")\n", "\n", "show_dataset_info(dataset)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_onlytgt_pack\"\n", ")\n", "\n", "show_dataset_info(dataset)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "There are 669 examples\n", "\u001b[32mIndex=    0, negative_num=979\u001b[0m\n", "\u001b[32mThere are 2 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON><PERSON>     0/    2\u001b[0m\n", "\n", "Base on the context of the following Python class and dataclass, please response to the instruction.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "```\n", "\n", "### Instruction:\n", "compute 199999168 + 434366986\n", "\n", "### Response:\n", "AdditionRule(\n", "  inputs=(Number(\n", "      digits=[Digit(\n", "          value=8,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=8\n", "        )]\n", "    ),\n", "    Number(\n", "      digits=[Digit(\n", "          value=6,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=8,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=3,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=3,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=8\n", "        )]\n", "    )),\n", "  procedure=[PerPositionAddition(\n", "      left=Digit(\n", "        value=8,\n", "        position=0\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=0\n", "      ),\n", "      carry=1,\n", "      result=4\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=1\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=1\n", "      ),\n", "      carry=1,\n", "      result=5\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=1,\n", "        position=2\n", "      ),\n", "      right=Digit(\n", "        value=9,\n", "        position=2\n", "      ),\n", "      carry=1,\n", "      result=1\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=3\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=3\n", "      ),\n", "      carry=1,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=4\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=4\n", "      ),\n", "      carry=1,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=5\n", "      ),\n", "      right=Digit(\n", "        value=3,\n", "        position=5\n", "      ),\n", "      carry=1,\n", "      result=3\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=6\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=6\n", "      ),\n", "      carry=1,\n", "      result=4\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=7\n", "      ),\n", "      right=Digit(\n", "        value=3,\n", "        position=7\n", "      ),\n", "      carry=1,\n", "      result=3\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=1,\n", "        position=8\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=8\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    )],\n", "  output=Number(\n", "    digits=[Digit(\n", "        value=4,\n", "        position=0\n", "      ),\n", "      Digit(\n", "        value=5,\n", "        position=1\n", "      ),\n", "      Digit(\n", "        value=1,\n", "        position=2\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=3\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=4\n", "      ),\n", "      Digit(\n", "        value=3,\n", "        position=5\n", "      ),\n", "      Digit(\n", "        value=4,\n", "        position=6\n", "      ),\n", "      Digit(\n", "        value=3,\n", "        position=7\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=8\n", "      )]\n", "  )\n", ")\n", "\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON>art     1/    2\u001b[0m\n", "\n", "Base on the context of the following Python class and dataclass, please response to the instruction.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "```\n", "\n", "### Instruction:\n", "compute 975944250 + 790674514\n", "\n", "### Response:\n", "AdditionRule(\n", "  inputs=(Number(\n", "      digits=[Digit(\n", "          value=0,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=5,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=5,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=8\n", "        )]\n", "    ),\n", "    Number(\n", "      digits=[Digit(\n", "          value=4,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=5,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=8\n", "        )]\n", "    )),\n", "  procedure=[PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=0\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=0\n", "      ),\n", "      carry=0,\n", "      result=4\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=5,\n", "        position=1\n", "      ),\n", "      right=Digit(\n", "        value=1,\n", "        position=1\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=2,\n", "        position=2\n", "      ),\n", "      right=Digit(\n", "        value=5,\n", "        position=2\n", "      ),\n", "      carry=0,\n", "      result=7\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=4,\n", "        position=3\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=3\n", "      ),\n", "      carry=0,\n", "      result=8\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=4,\n", "        position=4\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=4\n", "      ),\n", "      carry=1,\n", "      result=1\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=5\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=5\n", "      ),\n", "      carry=1,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=5,\n", "        position=6\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=6\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=7,\n", "        position=7\n", "      ),\n", "      right=Digit(\n", "        value=9,\n", "        position=7\n", "      ),\n", "      carry=1,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=8\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=8\n", "      ),\n", "      carry=1,\n", "      result=7\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=9\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=9\n", "      ),\n", "      carry=1,\n", "      result=1\n", "    )],\n", "  output=Number(\n", "    digits=[Digit(\n", "        value=4,\n", "        position=0\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=1\n", "      ),\n", "      Digit(\n", "        value=7,\n", "        position=2\n", "      ),\n", "      Digit(\n", "        value=8,\n", "        position=3\n", "      ),\n", "      Digit(\n", "        value=1,\n", "        position=4\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=5\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=6\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=7\n", "      ),\n", "      Digit(\n", "        value=7,\n", "        position=8\n", "      ),\n", "      Digit(\n", "        value=1,\n", "        position=9\n", "      )]\n", "  )\n", ")\n", "\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "\u001b[32mIndex=  287, negative_num=1061\u001b[0m\n", "\u001b[32mThere are 2 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON><PERSON>     0/    2\u001b[0m\n", "\n", "Base on the context of the following Python class and dataclass, please response to the instruction.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "```\n", "\n", "### Instruction:\n", "compute 366194536 + 260607802\n", "\n", "### Response:\n", "AdditionRule(\n", "  inputs=(Number(\n", "      digits=[Digit(\n", "          value=6,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=3,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=5,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=3,\n", "          position=8\n", "        )]\n", "    ),\n", "    Number(\n", "      digits=[Digit(\n", "          value=2,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=8,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=8\n", "        )]\n", "    )),\n", "  procedure=[PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=0\n", "      ),\n", "      right=Digit(\n", "        value=2,\n", "        position=0\n", "      ),\n", "      carry=0,\n", "      result=8\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=3,\n", "        position=1\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=1\n", "      ),\n", "      carry=0,\n", "      result=3\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=5,\n", "        position=2\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=2\n", "      ),\n", "      carry=1,\n", "      result=3\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=4,\n", "        position=3\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=3\n", "      ),\n", "      carry=1,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=4\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=4\n", "      ),\n", "      carry=1,\n", "      result=0\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=1,\n", "        position=5\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=5\n", "      ),\n", "      carry=0,\n", "      result=8\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=6\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=6\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=7\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=7\n", "      ),\n", "      carry=1,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=3,\n", "        position=8\n", "      ),\n", "      right=Digit(\n", "        value=2,\n", "        position=8\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    )],\n", "  output=Number(\n", "    digits=[Digit(\n", "        value=8,\n", "        position=0\n", "      ),\n", "      Digit(\n", "        value=3,\n", "        position=1\n", "      ),\n", "      Digit(\n", "        value=3,\n", "        position=2\n", "      ),\n", "      Digit(\n", "        value=2,\n", "        position=3\n", "      ),\n", "      Digit(\n", "        value=0,\n", "        position=4\n", "      ),\n", "      Digit(\n", "        value=8,\n", "        position=5\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=6\n", "      ),\n", "      Digit(\n", "        value=2,\n", "        position=7\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=8\n", "      )]\n", "  )\n", ")\n", "\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON>art     1/    2\u001b[0m\n", "\n", "Base on the context of the following Python class and dataclass, please response to the instruction.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "```\n", "\n", "### Instruction:\n", "compute 169766214 + 498453708\n", "\n", "### Response:\n", "AdditionRule(\n", "  inputs=(Number(\n", "      digits=[Digit(\n", "          value=4,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=8\n", "        )]\n", "    ),\n", "    Number(\n", "      digits=[Digit(\n", "          value=8,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=3,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=5,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=8,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=8\n", "        )]\n", "    )),\n", "  procedure=[PerPositionAddition(\n", "      left=Digit(\n", "        value=4,\n", "        position=0\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=0\n", "      ),\n", "      carry=1,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=1,\n", "        position=1\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=1\n", "      ),\n", "      carry=0,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=2,\n", "        position=2\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=2\n", "      ),\n", "      carry=0,\n", "      result=9\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=3\n", "      ),\n", "      right=Digit(\n", "        value=3,\n", "        position=3\n", "      ),\n", "      carry=0,\n", "      result=9\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=4\n", "      ),\n", "      right=Digit(\n", "        value=5,\n", "        position=4\n", "      ),\n", "      carry=1,\n", "      result=1\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=7,\n", "        position=5\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=5\n", "      ),\n", "      carry=1,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=6\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=6\n", "      ),\n", "      carry=1,\n", "      result=8\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=7\n", "      ),\n", "      right=Digit(\n", "        value=9,\n", "        position=7\n", "      ),\n", "      carry=1,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=1,\n", "        position=8\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=8\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    )],\n", "  output=Number(\n", "    digits=[Digit(\n", "        value=2,\n", "        position=0\n", "      ),\n", "      Digit(\n", "        value=2,\n", "        position=1\n", "      ),\n", "      Digit(\n", "        value=9,\n", "        position=2\n", "      ),\n", "      Digit(\n", "        value=9,\n", "        position=3\n", "      ),\n", "      Digit(\n", "        value=1,\n", "        position=4\n", "      ),\n", "      Digit(\n", "        value=2,\n", "        position=5\n", "      ),\n", "      Digit(\n", "        value=8,\n", "        position=6\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=7\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=8\n", "      )]\n", "  )\n", ")\n", "\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "\u001b[32mIndex=  668, negative_num=979\u001b[0m\n", "\u001b[32mThere are 2 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON><PERSON>     0/    2\u001b[0m\n", "\n", "Base on the context of the following Python class and dataclass, please response to the instruction.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "```\n", "\n", "### Instruction:\n", "compute 214070099 + 122781306\n", "\n", "### Response:\n", "AdditionRule(\n", "  inputs=(Number(\n", "      digits=[Digit(\n", "          value=9,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=8\n", "        )]\n", "    ),\n", "    Number(\n", "      digits=[Digit(\n", "          value=6,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=3,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=8,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=8\n", "        )]\n", "    )),\n", "  procedure=[PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=0\n", "      ),\n", "      right=Digit(\n", "        value=6,\n", "        position=0\n", "      ),\n", "      carry=1,\n", "      result=5\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=1\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=1\n", "      ),\n", "      carry=1,\n", "      result=0\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=2\n", "      ),\n", "      right=Digit(\n", "        value=3,\n", "        position=2\n", "      ),\n", "      carry=0,\n", "      result=4\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=3\n", "      ),\n", "      right=Digit(\n", "        value=1,\n", "        position=3\n", "      ),\n", "      carry=0,\n", "      result=1\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=7,\n", "        position=4\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=4\n", "      ),\n", "      carry=1,\n", "      result=5\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=5\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=5\n", "      ),\n", "      carry=0,\n", "      result=8\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=4,\n", "        position=6\n", "      ),\n", "      right=Digit(\n", "        value=2,\n", "        position=6\n", "      ),\n", "      carry=0,\n", "      result=6\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=1,\n", "        position=7\n", "      ),\n", "      right=Digit(\n", "        value=2,\n", "        position=7\n", "      ),\n", "      carry=0,\n", "      result=3\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=2,\n", "        position=8\n", "      ),\n", "      right=Digit(\n", "        value=1,\n", "        position=8\n", "      ),\n", "      carry=0,\n", "      result=3\n", "    )],\n", "  output=Number(\n", "    digits=[Digit(\n", "        value=5,\n", "        position=0\n", "      ),\n", "      Digit(\n", "        value=0,\n", "        position=1\n", "      ),\n", "      Digit(\n", "        value=4,\n", "        position=2\n", "      ),\n", "      Digit(\n", "        value=1,\n", "        position=3\n", "      ),\n", "      Digit(\n", "        value=5,\n", "        position=4\n", "      ),\n", "      Digit(\n", "        value=8,\n", "        position=5\n", "      ),\n", "      Digit(\n", "        value=6,\n", "        position=6\n", "      ),\n", "      Digit(\n", "        value=3,\n", "        position=7\n", "      ),\n", "      Digit(\n", "        value=3,\n", "        position=8\n", "      )]\n", "  )\n", ")\n", "\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON>art     1/    2\u001b[0m\n", "\n", "Base on the context of the following Python class and dataclass, please response to the instruction.\n", "\n", "```\n", "Type0to9 = typing.Literal[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "@dataclasses.dataclass\n", "class Digit:\n", "    \"\"\"A digit is a tuple of a value and a position.\"\"\"\n", "\n", "    value: Type0to9\n", "    position: int\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionAddition:\n", "    \"\"\"A class to maintain the addition at each position\"\"\"\n", "\n", "    left: <PERSON><PERSON>\n", "    \"\"\"The left digit value.\"\"\"\n", "\n", "    right: Di<PERSON>\n", "    \"\"\"The right digit value.\"\"\"\n", "\n", "    carry: typing.Literal[0, 1]\n", "    \"\"\"The additional carried value.\"\"\"\n", "\n", "    result: Type0to9\n", "\n", "\n", "@dataclasses.dataclass\n", "class Number:\n", "    digits: typing.List[Digit]\n", "\n", "\n", "@dataclasses.dataclass\n", "class PerPositionMultiplication:\n", "    left: <PERSON><PERSON>\n", "    right: Number\n", "    result: Number\n", "\n", "\n", "@dataclasses.dataclass\n", "class AdditionRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionAddition]\n", "    output: Number\n", "\n", "@dataclasses.dataclass\n", "class MultiplicationRule:\n", "    inputs: typing.Tuple[Number, Number]\n", "    procedure: typing.List[PerPositionMultiplication]\n", "    output: Number\n", "```\n", "\n", "### Instruction:\n", "compute 999570766 + 278704185\n", "\n", "### Response:\n", "AdditionRule(\n", "  inputs=(Number(\n", "      digits=[Digit(\n", "          value=6,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=6,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=5,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=9,\n", "          position=8\n", "        )]\n", "    ),\n", "    Number(\n", "      digits=[Digit(\n", "          value=5,\n", "          position=0\n", "        ),\n", "        Digit(\n", "          value=8,\n", "          position=1\n", "        ),\n", "        Digit(\n", "          value=1,\n", "          position=2\n", "        ),\n", "        Digit(\n", "          value=4,\n", "          position=3\n", "        ),\n", "        Digit(\n", "          value=0,\n", "          position=4\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=5\n", "        ),\n", "        Digit(\n", "          value=8,\n", "          position=6\n", "        ),\n", "        Digit(\n", "          value=7,\n", "          position=7\n", "        ),\n", "        Digit(\n", "          value=2,\n", "          position=8\n", "        )]\n", "    )),\n", "  procedure=[PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=0\n", "      ),\n", "      right=Digit(\n", "        value=5,\n", "        position=0\n", "      ),\n", "      carry=1,\n", "      result=1\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=6,\n", "        position=1\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=1\n", "      ),\n", "      carry=1,\n", "      result=5\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=7,\n", "        position=2\n", "      ),\n", "      right=Digit(\n", "        value=1,\n", "        position=2\n", "      ),\n", "      carry=0,\n", "      result=9\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=3\n", "      ),\n", "      right=Digit(\n", "        value=4,\n", "        position=3\n", "      ),\n", "      carry=0,\n", "      result=4\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=7,\n", "        position=4\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=4\n", "      ),\n", "      carry=0,\n", "      result=7\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=5,\n", "        position=5\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=5\n", "      ),\n", "      carry=1,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=6\n", "      ),\n", "      right=Digit(\n", "        value=8,\n", "        position=6\n", "      ),\n", "      carry=1,\n", "      result=8\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=7\n", "      ),\n", "      right=Digit(\n", "        value=7,\n", "        position=7\n", "      ),\n", "      carry=1,\n", "      result=7\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=9,\n", "        position=8\n", "      ),\n", "      right=Digit(\n", "        value=2,\n", "        position=8\n", "      ),\n", "      carry=1,\n", "      result=2\n", "    ),\n", "    PerPositionAddition(\n", "      left=Digit(\n", "        value=0,\n", "        position=9\n", "      ),\n", "      right=Digit(\n", "        value=0,\n", "        position=9\n", "      ),\n", "      carry=1,\n", "      result=1\n", "    )],\n", "  output=Number(\n", "    digits=[Digit(\n", "        value=1,\n", "        position=0\n", "      ),\n", "      Digit(\n", "        value=5,\n", "        position=1\n", "      ),\n", "      Digit(\n", "        value=9,\n", "        position=2\n", "      ),\n", "      Digit(\n", "        value=4,\n", "        position=3\n", "      ),\n", "      Digit(\n", "        value=7,\n", "        position=4\n", "      ),\n", "      Digit(\n", "        value=2,\n", "        position=5\n", "      ),\n", "      Digit(\n", "        value=8,\n", "        position=6\n", "      ),\n", "      Digit(\n", "        value=7,\n", "        position=7\n", "      ),\n", "      Digit(\n", "        value=2,\n", "        position=8\n", "      ),\n", "      Digit(\n", "        value=1,\n", "        position=9\n", "      )]\n", "  )\n", ")\n", "\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["# dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/xtry-rules/0_to_1e9/simple-valid\")\n", "dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/xtry-rules/0_to_1e9/structure-valid\"\n", ")\n", "show_dataset_info(dataset)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}