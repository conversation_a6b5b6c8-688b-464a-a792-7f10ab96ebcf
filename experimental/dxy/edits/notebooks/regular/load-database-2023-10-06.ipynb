{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from supabase.client import create_client, Client\n", "\n", "url: str = os.environ.get(\"SUPABASE_URL\", \"\")\n", "key: str = os.environ.get(\"SUPABASE_KEY\", \"\")\n", "print(f\"URL: {url}\")\n", "print(f\"Key: {key}\")\n", "supabase: Client = create_client(url, key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_name: str = \"augment_vscode_log\"\n", "response = supabase.table(table_name).select(\"*\").execute()\n", "all_data = [x for x in response.data if x[\"action\"] == \"edit\"]\n", "print(f\"There are {len(all_data)} datas.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import defaultdict\n", "from experimental.dxy.edits import data_type\n", "\n", "\n", "def parse_openai_code(s: str, code_start_prefix: str = \"Code:\"):\n", "    part0, instruction = s.split(\"Instruction:\")\n", "    assert part0.startswith(code_start_prefix)\n", "    code = part0[len(code_start_prefix) :].strip()\n", "    return code, instruction\n", "\n", "\n", "collected_edit_data: list[data_type.SimpleEditData] = []\n", "api2num = defaultdict(lambda: 0)\n", "\n", "for data in all_data:\n", "    prompt = data[\"prompt\"]\n", "    if data[\"api\"] == \"openai\":\n", "        if isinstance(prompt, list) and len(prompt) == 2:\n", "            try:\n", "                code, instruction = parse_openai_code(prompt[1][\"content\"])\n", "            except:\n", "                continue\n", "        elif isinstance(prompt, dict) and \"code\" in prompt and \"instruction\" in prompt:\n", "            code, instruction = prompt[\"code\"], prompt[\"instruction\"]\n", "        else:\n", "            raise ValueError(prompt)\n", "        api2num[\"openai\"] += 1\n", "    elif data[\"api\"] in (\"llama-7b-selected\", \"llama-34b-selected\", \"llama-selected\"):\n", "        if isinstance(prompt, dict) and \"code\" in prompt and \"instruction\" in prompt:\n", "            code, instruction = prompt[\"code\"], prompt[\"instruction\"]\n", "        elif isinstance(prompt, list) and len(prompt) == 2:\n", "            try:\n", "                code, instruction = parse_openai_code(prompt[1][\"content\"])\n", "            except:\n", "                continue\n", "        elif isinstance(prompt, dict) and \"prompt\" in prompt:\n", "            try:\n", "                code, instruction = parse_openai_code(\n", "                    prompt[\"prompt\"], \"Original code:\\n\"\n", "                )\n", "            except:\n", "                continue\n", "        else:\n", "            raise ValueError(prompt)\n", "        api2num[\"llama\"] += 1\n", "    elif data[\"api\"] == \"augment\":\n", "        if (\n", "            isinstance(prompt, dict)\n", "            and \"selected_code\" in prompt\n", "            and \"instruction\" in prompt\n", "        ):\n", "            code, instruction = prompt[\"selected_code\"], prompt[\"instruction\"]\n", "        else:\n", "            try:\n", "                code, instruction = parse_openai_code(\n", "                    prompt[\"prompt\"], \"Original code:\\n\"\n", "                )\n", "            except:\n", "                continue\n", "        api2num[\"augment\"] += 1\n", "    else:\n", "        print(f\"api={data['api']}, prompt={prompt}\")\n", "        continue\n", "        # raise ValueError(f\"api={data['api']}, prompt={prompt}\")\n", "    # Parse the modified code\n", "    if \"modified_code\" in data and isinstance(data[\"modified_code\"], str):\n", "        modified_code = data[\"modified_code\"]\n", "    elif \"response\" in data and isinstance(data[\"response\"], str):\n", "        modified_code = data[\"response\"]\n", "    elif \"response\" in data and data[\"response\"] is None:\n", "        modified_code = None\n", "    elif (\n", "        \"response\" in data\n", "        and isinstance(data[\"response\"], dict)\n", "        and \"code\" in data[\"response\"]\n", "    ):\n", "        modified_code = data[\"response\"][\"code\"]\n", "    elif (\n", "        \"response\" in data\n", "        and isinstance(data[\"response\"], dict)\n", "        and \"response\" in data[\"response\"]\n", "    ):\n", "        modified_code = data[\"response\"][\"response\"]\n", "    else:\n", "        raise ValueError(f\"data.keys() = {data.keys()}, {data['response'].keys()}\")\n", "    # print(data[\"action\"])\n", "    if (\n", "        \"prompt\" in data\n", "        and isinstance(data[\"prompt\"], dict)\n", "        and \"prefix\" in data[\"prompt\"]\n", "        and \"suffix\" in data[\"prompt\"]\n", "    ):\n", "        prefix, suffix = data[\"prompt\"][\"prefix\"], data[\"prompt\"][\"suffix\"]\n", "    else:\n", "        prefix, suffix = None, None\n", "    cur_data = data_type.SimpleEditData(\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        selected_code=code,\n", "        instruction=instruction,\n", "        modified_code=modified_code,\n", "    )\n", "    collected_edit_data.append(cur_data)\n", "\n", "for key, value in api2num.items():\n", "    print(f\"{key}: {value}\")\n", "print(f\"There are {len(collected_edit_data)} collected edit data.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["avaliable_datas = []\n", "for x in collected_edit_data:\n", "    if x.prefix and x.suffix and x.selected_code and x.instruction:\n", "        avaliable_datas.append(x)\n", "print(f\"There are {len(avaliable_datas)} avaliable data.\")\n", "# print(collected_edit_data[11].selected_code, end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "\n", "def show_example(x: data_type.SimpleEditData):\n", "    print(f\"Instruction: {x.instruction}\")\n", "    print(colored(x.prefix, color=\"blue\"), end=\"\")\n", "    print(colored(x.selected_code, color=\"red\"), end=\"\")\n", "    print(colored(x.suffix, color=\"blue\"))\n", "\n", "\n", "show_example(avaliable_datas[14])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instruction_set = set()\n", "filtered_edit_data = []\n", "for x in collected_edit_data:\n", "    instruct = x.instruction.strip().replace(\"/chat\", \"\").replace(\"/edit\", \"\")\n", "    if instruct not in instruction_set:\n", "        instruction_set.add(instruct)\n", "        filtered_edit_data.append(x)\n", "print(\n", "    f\"There are {len(filtered_edit_data)} / {len(collected_edit_data)} elements left.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["line_counts = defaultdict(lambda: 0)\n", "for x in filtered_edit_data:\n", "    code = x.selected_code\n", "    if code is None:\n", "        continue\n", "    cur_lines = code.splitlines()\n", "    line_counts[len(cur_lines)] += 1\n", "\n", "k_v_pairs = sorted([(k, v) for k, v in line_counts.items()])\n", "for key, value in k_v_pairs:\n", "    print(f\"{key} : {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions = [x.instruction for x in collected_edit_data]\n", "instructions = [x.strip() for x in instructions if x]\n", "instructions = [x.replace(\"/chat\", \"\").replace(\"/edit\", \"\") for x in instructions]\n", "instructions = [x.strip() for x in instructions]\n", "instructions = [x for x in instructions if x]\n", "instructions = list(set(instructions))\n", "\n", "print(f\"Left {len(instructions)} instructions\")\n", "for x in instructions:\n", "    print(x)\n", "    print(\"-\" * 100)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}