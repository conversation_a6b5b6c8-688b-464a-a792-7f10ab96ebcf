{"cells": [{"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 194 files in total.\n", "There are 8192 elements in the first batch of the first jsonl file.\n"]}], "source": ["import pathlib\n", "from research.core import utils_for_dataclass, utils_for_file\n", "from experimental.dxy.edits.data_type import EditData\n", "from experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\n", "\n", "data_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/json-2023-10-06-cache-del_00_00/\"\n", ")\n", "jsonl_files = sorted(list(data_dir.glob(\"*-to-*-in-*.jsonl.zst\")), key=lambda x: str(x))\n", "jsonl_files = [str(x) for x in jsonl_files]\n", "print(f\"There are {len(jsonl_files)} files in total.\")\n", "batch_of_json_str = utils_for_file.read_jsonl_zst(jsonl_files[0])\n", "print(\n", "    f\"There are {len(batch_of_json_str)} elements in the first batch of the first jsonl file.\"\n", ")\n", "batch_of_edit_data = [\n", "    utils_for_dataclass.create_from_json(EditData, x) for x in batch_of_json_str\n", "]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ ] from django.contrib.auth.decorators import login_required\n", "[ ] from django.views.decorators.cache import never_cache\n", "[ ] \n", "\u001b[32m[+] from django.utils.http import urlquote\n", "\u001b[0m[ ] from django.contrib.auth import authenticate, login, logout\n", "[ ] from itertools import chain\n", "[ ] from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON>\n"]}], "source": ["data = batch_of_edit_data[0]\n", "data.show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.show_old_hunk()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data.show_new_hunk()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_diff_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_selected_code(), end=\"\")\n", "print(\"-\" * 100)\n", "print(data.get_modified_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "\n", "lrange = data.get_lrange()\n", "print(\n", "    termcolor.colored(data.get_prefix_via_lrange(lrange=lrange), color=\"green\"), end=\"\"\n", ")\n", "print(\n", "    termcolor.colored(data.get_content_via_lrange(lrange=lrange), color=\"red\"), end=\"\"\n", ")\n", "print(termcolor.colored(data.get_suffix_via_lrange(lrange=lrange), color=\"blue\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.new_file_content)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["predict_edit_data = predict_edit_data_v0"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "There are 11 messages.\n", "[the 00-th message] Let's imagine a scenario: there is an AI assistant helping users to write the Python code.\n", "The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code.\n", "[the 01-th message] Ok, I understand this scenario, what do you want me to do?\n", "[the 02-th message] Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.\n", "Can you guess what is the user's original instruction?\n", "[the 03-th message] Sure, I will guess, what is the entire codes, and the target piece of codes?\n", "[the 04-th message] Here is the preceding part of the target piece of codes:\n", "```\n", "# -*- coding: utf-8 -*-\n", "\n", "# views\n", "\n", "from django.conf import settings\n", "from django.http import HttpResponse\n", "from django.template import Context, loader\n", "from django.shortcuts import render, redirect\n", "import collections\n", "import datetime\n", "import os\n", "import ti\n", "from django.contrib.auth.decorators import login_required\n", "from django.views.decorators.cache import never_cache\n", "\n", "```\n", "[the 05-th message] Got it, what is the target piece of codes and its possible succeeding part?\n", "[the 06-th message] The target piece of code is empty, and thus the instruction will be about add something.\n", "[the 07-th message] Got it, what is its succeeding part and the final updated codes?\n", "[the 08-th message] Here is the succeeding part of the target piece of codes:\n", "```\n", "from django.contrib.auth import authenticate, login, logout\n", "from itertools import chain\n", "from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "from ti.models import *\n", "import json\n", "\n", "import operator\n", "\n", "@login_required\n", "def home(request):\n", "    ctx = {'pages': Page.objects.all}\n", "    return render(request, 'pagelist', ctx, content_type=\"text/html\")\n", "\n", "@login_required\n", "def page_info(request, page_id=None):\n", "\n", "```\n", "[the 09-th message] Got<PERSON>, what is the updated version?\n", "[the 10-th message] Here is the updated version\n", "```\n", "from django.utils.http import urlquote\n", "```\n", "\n", "Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:\n", "- put a nice docstring to well explain this class\n", "- use promise chain instead of async\n", "- extend it to else condition\n", "- fix errors\n", "- move the accesstoken to url as a parameter of token=\n", "- make this line more readable\n", "- generate floats, not only ints\n", "- add a param to choose upper/lower\n", "- change all command names to snake case\n", "- replace <PERSON> by <PERSON><PERSON><PERSON>\n", "- this function seems will never trigger the default branch of executeChat, fix it\n", "- refactor this function by using optional_map func\n", "- simplify the cache control\n", "- console log all the ids of each file\n", "- Refactor the code. Change config.foo to CONFIG['foo']\n", "- change Optional[str] to str | None\n", "- write test train split using df pandas\n", "- instead of hard code options in the quick pick array, randomly generate 10 numbers as options\n", "- write a short pytorch module that implements linear regression, have comments\n", "- polish it with concise codes\n", "\n", "When you guess his instruction, please follow these requirements:\n", "- (1) try your best to simulate his tone;\n", "- (2) produce the instruction vague but not too vague naturely like a human, for example,\n", "-- (2.1) \"Add the import for urlquote from django.utils.http\" can be simplified as \"add the import of urlquote\";\n", "- (3) produce the instruction short and brief;\n", "- (4) in your output, please quote the instruction like ```instruction```.\n", "\n", "------------------------------ Selected Code ------------------------------\n", "# -*- coding: utf-8 -*-\n", "\n", "# views\n", "\n", "from django.conf import settings\n", "from django.http import HttpResponse\n", "from django.template import Context, loader\n", "from django.shortcuts import render, redirect\n", "import collections\n", "import datetime\n", "import os\n", "import ti\n", "from django.contrib.auth.decorators import login_required\n", "from django.views.decorators.cache import never_cache\n", "\n", "\u001b[34m<<|empty-place-holder|>>\u001b[0mfrom django.contrib.auth import authenticate, login, logout\n", "from itertools import chain\n", "from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "from ti.models import *\n", "import json\n", "\n", "import operator\n", "\n", "@login_required\n", "def home(request):\n", "    ctx = {'pages': Page.objects.all}\n", "    return render(request, 'pagelist', ctx, content_type=\"text/html\")\n", "\n", "@login_required\n", "def page_info(request, page_id=None):\n", "----------------------------------------------------------------------------------------------------\n", "------------------------------ Modified Code ------------------------------\n", "# -*- coding: utf-8 -*-\n", "\n", "# views\n", "\n", "from django.conf import settings\n", "from django.http import HttpResponse\n", "from django.template import Context, loader\n", "from django.shortcuts import render, redirect\n", "import collections\n", "import datetime\n", "import os\n", "import ti\n", "from django.contrib.auth.decorators import login_required\n", "from django.views.decorators.cache import never_cache\n", "\n", "\u001b[31mfrom django.utils.http import urlquote\n", "\u001b[0mfrom django.contrib.auth import authenticate, login, logout\n", "from itertools import chain\n", "from django.db.models import <PERSON>, <PERSON>, <PERSON>, <PERSON>\n", "from ti.models import *\n", "import json\n", "\n", "import operator\n", "\n", "@login_required\n", "def home(request):\n", "    ctx = {'pages': Page.objects.all}\n", "    return render(request, 'pagelist', ctx, content_type=\"text/html\")\n", "\n", "@login_required\n", "def page_info(request, page_id=None):\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[34mBased on the provided information, the user's instruction could be:\n", "\n", "```import urlquote from django.utils.http```\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["_ = predict_edit_data(batch_of_edit_data[0], debug=True)"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "There are 11 messages.\n", "[the 00-th message] Let's imagine a scenario: there is an AI assistant helping users to write the Python code.\n", "The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code.\n", "[the 01-th message] Ok, I understand this scenario, what do you want me to do?\n", "[the 02-th message] Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.\n", "Can you guess what is the user's original instruction?\n", "[the 03-th message] Sure, I will guess, what is the entire codes, and the target piece of codes?\n", "[the 04-th message] Here is the preceding part of the target piece of codes:\n", "```\n", "```\n", "[the 05-th message] Got it, what is the target piece of codes and its possible succeeding part?\n", "[the 06-th message] Here is the target piece of codes:\n", "```\n", "\n", "```\n", "[the 07-th message] Got it, what is its succeeding part and the final updated codes?\n", "[the 08-th message] Here is the succeeding part of the target piece of codes:\n", "```\n", "class VEventExpansionFailed(ValueError):\n", "    pass\n", "\n", "```\n", "[the 09-th message] Got<PERSON>, what is the updated version?\n", "[the 10-th message] Here is the updated version\n", "```\n", "# @ToDo(jor<PERSON>) add doc strings here.\n", "```\n", "\n", "Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:\n", "- put a nice docstring to well explain this class\n", "- use promise chain instead of async\n", "- extend it to else condition\n", "- fix errors\n", "- move the accesstoken to url as a parameter of token=\n", "- make this line more readable\n", "- generate floats, not only ints\n", "- add a param to choose upper/lower\n", "- change all command names to snake case\n", "- replace <PERSON> by <PERSON><PERSON><PERSON>\n", "- this function seems will never trigger the default branch of executeChat, fix it\n", "- refactor this function by using optional_map func\n", "- simplify the cache control\n", "- console log all the ids of each file\n", "- Refactor the code. Change config.foo to CONFIG['foo']\n", "- change Optional[str] to str | None\n", "- write test train split using df pandas\n", "- instead of hard code options in the quick pick array, randomly generate 10 numbers as options\n", "- write a short pytorch module that implements linear regression, have comments\n", "- polish it with concise codes\n", "\n", "When you guess his instruction, please follow these requirements:\n", "- (1) try your best to simulate his tone;\n", "- (2) produce the instruction vague but not too vague naturely like a human, for example,\n", "-- (2.1) \"Add the import for urlquote from django.utils.http\" can be simplified as \"add the import of urlquote\";\n", "- (3) produce the instruction short and brief;\n", "- (4) in your output, please quote the instruction like ```instruction```.\n", "\n", "------------------------------ Selected Code ------------------------------\n", "\u001b[34m<<|empty-place-holder|>>\u001b[0mclass VEventExpansionFailed(ValueError):\n", "    pass\n", "----------------------------------------------------------------------------------------------------\n", "------------------------------ Modified Code ------------------------------\n", "\u001b[31m# @<PERSON><PERSON><PERSON>(jorrick) add doc strings here.\n", "\u001b[0mclass VEventExpansionFailed(ValueError):\n", "    pass\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[34mBased on the provided information, the user's instruction could be:\n", "\n", "```Add a comment to remind to add docstrings.```\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["_ = predict_edit_data(batch_of_edit_data[1], debug=True)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "There are 11 messages.\n", "[the 00-th message] Let's imagine a scenario: there is an AI assistant helping users to write the Python code.\n", "The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code.\n", "[the 01-th message] Ok, I understand this scenario, what do you want me to do?\n", "[the 02-th message] Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.\n", "Can you guess what is the user's original instruction?\n", "[the 03-th message] Sure, I will guess, what is the entire codes, and the target piece of codes?\n", "[the 04-th message] Here is the preceding part of the target piece of codes:\n", "```\n", "            sys.stderr.write(f\"WARNING: {logmsg}\\n\")\n", "            sys.stderr.flush()\n", "\n", "    def info(self, msg=None, logmsg=None):\n", "        \"\"\"Issue a informational message.\n", "\n", "        When sent to the log, it goes to standard output.\n", "        \"\"\"\n", "        if msg is not None:\n", "            self._addMessage(\"info\", f\"INFO: {msg}\")\n", "        if logmsg is not None:\n", "            sys.stdout.write(f\"INFO: {logmsg}\\n\")\n", "            sys.stdout.flush()\n", "\n", "    def plain(self, msg=None, logmsg=None):\n", "```\n", "[the 05-th message] Got it, what is the target piece of codes and its possible succeeding part?\n", "[the 06-th message] The target piece of code is empty, and thus the instruction will be about add something.\n", "[the 07-th message] Got it, what is its succeeding part and the final updated codes?\n", "[the 08-th message] Here is the succeeding part of the target piece of codes:\n", "```\n", "        if msg is not None:\n", "            self._addMessage(\"info\", msg)\n", "        if logmsg is not None:\n", "            sys.stdout.write(f\"{logmsg}\\n\")\n", "            sys.stdout.flush()\n", "\n", "    def generateMessages(self):\n", "        \"\"\"Wrap the accumulates messages into html.\n", "\n", "        They are ready to be included in a response.\n", "\n", "        The list of accumulated messages will be cleared afterwards.\n", "        \"\"\"\n", "        html = [\"\"\"<div class=\"messages\">\"\"\"]\n", "\n", "\n", "```\n", "[the 09-th message] Got<PERSON>, what is the updated version?\n", "[the 10-th message] Here is the updated version\n", "```\n", "        \"\"\"Issue a informational message, without bells and whistles.\n", "\n", "        When sent to the log, it goes to standard output.\n", "        \"\"\"\n", "```\n", "\n", "Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:\n", "- put a nice docstring to well explain this class\n", "- use promise chain instead of async\n", "- extend it to else condition\n", "- fix errors\n", "- move the accesstoken to url as a parameter of token=\n", "- make this line more readable\n", "- generate floats, not only ints\n", "- add a param to choose upper/lower\n", "- change all command names to snake case\n", "- replace <PERSON> by <PERSON><PERSON><PERSON>\n", "- this function seems will never trigger the default branch of executeChat, fix it\n", "- refactor this function by using optional_map func\n", "- simplify the cache control\n", "- console log all the ids of each file\n", "- Refactor the code. Change config.foo to CONFIG['foo']\n", "- change Optional[str] to str | None\n", "- write test train split using df pandas\n", "- instead of hard code options in the quick pick array, randomly generate 10 numbers as options\n", "- write a short pytorch module that implements linear regression, have comments\n", "- polish it with concise codes\n", "\n", "When you guess his instruction, please follow these requirements:\n", "- (1) try your best to simulate his tone;\n", "- (2) produce the instruction vague but not too vague naturely like a human, for example,\n", "-- (2.1) \"Add the import for urlquote from django.utils.http\" can be simplified as \"add the import of urlquote\";\n", "- (3) produce the instruction short and brief;\n", "- (4) in your output, please quote the instruction like ```instruction```.\n", "\n", "------------------------------ Selected Code ------------------------------\n", "            sys.stderr.write(f\"WARNING: {logmsg}\\n\")\n", "            sys.stderr.flush()\n", "\n", "    def info(self, msg=None, logmsg=None):\n", "        \"\"\"Issue a informational message.\n", "\n", "        When sent to the log, it goes to standard output.\n", "        \"\"\"\n", "        if msg is not None:\n", "            self._addMessage(\"info\", f\"INFO: {msg}\")\n", "        if logmsg is not None:\n", "            sys.stdout.write(f\"INFO: {logmsg}\\n\")\n", "            sys.stdout.flush()\n", "\n", "    def plain(self, msg=None, logmsg=None):\n", "\u001b[34m<<|empty-place-holder|>>\u001b[0m        if msg is not None:\n", "            self._addMessage(\"info\", msg)\n", "        if logmsg is not None:\n", "            sys.stdout.write(f\"{logmsg}\\n\")\n", "            sys.stdout.flush()\n", "\n", "    def generateMessages(self):\n", "        \"\"\"Wrap the accumulates messages into html.\n", "\n", "        They are ready to be included in a response.\n", "\n", "        The list of accumulated messages will be cleared afterwards.\n", "        \"\"\"\n", "        html = [\"\"\"<div class=\"messages\">\"\"\"]\n", "\n", "----------------------------------------------------------------------------------------------------\n", "------------------------------ Modified Code ------------------------------\n", "            sys.stderr.write(f\"WARNING: {logmsg}\\n\")\n", "            sys.stderr.flush()\n", "\n", "    def info(self, msg=None, logmsg=None):\n", "        \"\"\"Issue a informational message.\n", "\n", "        When sent to the log, it goes to standard output.\n", "        \"\"\"\n", "        if msg is not None:\n", "            self._addMessage(\"info\", f\"INFO: {msg}\")\n", "        if logmsg is not None:\n", "            sys.stdout.write(f\"INFO: {logmsg}\\n\")\n", "            sys.stdout.flush()\n", "\n", "    def plain(self, msg=None, logmsg=None):\n", "\u001b[31m        \"\"\"Issue a informational message, without bells and whistles.\n", "\n", "        When sent to the log, it goes to standard output.\n", "        \"\"\"\n", "\u001b[0m        if msg is not None:\n", "            self._addMessage(\"info\", msg)\n", "        if logmsg is not None:\n", "            sys.stdout.write(f\"{logmsg}\\n\")\n", "            sys.stdout.flush()\n", "\n", "    def generateMessages(self):\n", "        \"\"\"Wrap the accumulates messages into html.\n", "\n", "        They are ready to be included in a response.\n", "\n", "        The list of accumulated messages will be cleared afterwards.\n", "        \"\"\"\n", "        html = [\"\"\"<div class=\"messages\">\"\"\"]\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[34mBased on the provided information, the user's instruction could be:\n", "\n", "```\"Add a brief docstring to explain this function.\"```\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["_ = predict_edit_data(batch_of_edit_data[2], debug=True)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "There are 11 messages.\n", "[the 00-th message] Let's imagine a scenario: there is an AI assistant helping users to write the Python code.\n", "The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code.\n", "[the 01-th message] Ok, I understand this scenario, what do you want me to do?\n", "[the 02-th message] Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.\n", "Can you guess what is the user's original instruction?\n", "[the 03-th message] Sure, I will guess, what is the entire codes, and the target piece of codes?\n", "[the 04-th message] Here is the preceding part of the target piece of codes:\n", "```\n", "from a10sdk.common.A10BaseClass import A10BaseClass\n", "\n", "\n", "class Tftp(A10BaseClass):\n", "    \n", "    \"\"\"Class Description::\n", "    NAT64 TFTP ALG (default: disabled).\n", "\n", "    Class tftp supports CRUD Operations and inherits from `common/A10BaseClass`.\n", "    This class is the `\"PARENT\"` class for this module.`\n", "\n", "    :param tftp_enable: {\"optional\": true, \"enum\": [\"enable\"], \"type\": \"string\", \"description\": \"'enable': Enable NAT64 TFTP ALG; \", \"format\": \"enum\"}\n", "```\n", "[the 05-th message] Got it, what is the target piece of codes and its possible succeeding part?\n", "[the 06-th message] The target piece of code is empty, and thus the instruction will be about add something.\n", "[the 07-th message] Got it, what is its succeeding part and the final updated codes?\n", "[the 08-th message] Here is the succeeding part of the target piece of codes:\n", "```\n", "    :param DeviceProxy: The device proxy for REST operations and session handling. Refer to `common/device_proxy.py`\n", "\n", "    \n", "\n", "    URL for this object::\n", "    `https://<Hostname|Ip address>//axapi/v3/cgnv6/nat64/alg/tftp`.\n", "\n", "    \n", "\n", "    \n", "    \"\"\"\n", "    def __init__(self, **kwargs):\n", "        self.ERROR_MSG = \"\"\n", "        self.required=[]\n", "        self.b_key = \"tftp\"\n", "\n", "```\n", "[the 09-th message] Got<PERSON>, what is the updated version?\n", "[the 10-th message] Here is the updated version\n", "```\n", "    :param uuid: {\"description\": \"uuid of the object\", \"format\": \"string\", \"minLength\": 1, \"modify-not-allowed\": 1, \"optional\": true, \"maxLength\": 64, \"type\": \"string\"}\n", "```\n", "\n", "Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:\n", "- put a nice docstring to well explain this class\n", "- use promise chain instead of async\n", "- extend it to else condition\n", "- fix errors\n", "- move the accesstoken to url as a parameter of token=\n", "- make this line more readable\n", "- generate floats, not only ints\n", "- add a param to choose upper/lower\n", "- change all command names to snake case\n", "- replace <PERSON> by <PERSON><PERSON><PERSON>\n", "- this function seems will never trigger the default branch of executeChat, fix it\n", "- refactor this function by using optional_map func\n", "- simplify the cache control\n", "- console log all the ids of each file\n", "- Refactor the code. Change config.foo to CONFIG['foo']\n", "- change Optional[str] to str | None\n", "- write test train split using df pandas\n", "- instead of hard code options in the quick pick array, randomly generate 10 numbers as options\n", "- write a short pytorch module that implements linear regression, have comments\n", "- polish it with concise codes\n", "\n", "When you guess his instruction, please follow these requirements:\n", "- (1) try your best to simulate his tone;\n", "- (2) produce the instruction vague but not too vague naturely like a human, for example,\n", "-- (2.1) \"Add the import for urlquote from django.utils.http\" can be simplified as \"add the import of urlquote\";\n", "- (3) produce the instruction short and brief;\n", "- (4) in your output, please quote the instruction like ```instruction```.\n", "\n", "------------------------------ Selected Code ------------------------------\n", "from a10sdk.common.A10BaseClass import A10BaseClass\n", "\n", "\n", "class Tftp(A10BaseClass):\n", "    \n", "    \"\"\"Class Description::\n", "    NAT64 TFTP ALG (default: disabled).\n", "\n", "    Class tftp supports CRUD Operations and inherits from `common/A10BaseClass`.\n", "    This class is the `\"PARENT\"` class for this module.`\n", "\n", "    :param tftp_enable: {\"optional\": true, \"enum\": [\"enable\"], \"type\": \"string\", \"description\": \"'enable': Enable NAT64 TFTP ALG; \", \"format\": \"enum\"}\n", "\u001b[34m<<|empty-place-holder|>>\u001b[0m    :param DeviceProxy: The device proxy for REST operations and session handling. Refer to `common/device_proxy.py`\n", "\n", "    \n", "\n", "    URL for this object::\n", "    `https://<Hostname|Ip address>//axapi/v3/cgnv6/nat64/alg/tftp`.\n", "\n", "    \n", "\n", "    \n", "    \"\"\"\n", "    def __init__(self, **kwargs):\n", "        self.ERROR_MSG = \"\"\n", "        self.required=[]\n", "        self.b_key = \"tftp\"\n", "----------------------------------------------------------------------------------------------------\n", "------------------------------ Modified Code ------------------------------\n", "from a10sdk.common.A10BaseClass import A10BaseClass\n", "\n", "\n", "class Tftp(A10BaseClass):\n", "    \n", "    \"\"\"Class Description::\n", "    NAT64 TFTP ALG (default: disabled).\n", "\n", "    Class tftp supports CRUD Operations and inherits from `common/A10BaseClass`.\n", "    This class is the `\"PARENT\"` class for this module.`\n", "\n", "    :param tftp_enable: {\"optional\": true, \"enum\": [\"enable\"], \"type\": \"string\", \"description\": \"'enable': Enable NAT64 TFTP ALG; \", \"format\": \"enum\"}\n", "\u001b[31m    :param uuid: {\"description\": \"uuid of the object\", \"format\": \"string\", \"minLength\": 1, \"modify-not-allowed\": 1, \"optional\": true, \"maxLength\": 64, \"type\": \"string\"}\n", "\u001b[0m    :param DeviceProxy: The device proxy for REST operations and session handling. Refer to `common/device_proxy.py`\n", "\n", "    \n", "\n", "    URL for this object::\n", "    `https://<Hostname|Ip address>//axapi/v3/cgnv6/nat64/alg/tftp`.\n", "\n", "    \n", "\n", "    \n", "    \"\"\"\n", "    def __init__(self, **kwargs):\n", "        self.ERROR_MSG = \"\"\n", "        self.required=[]\n", "        self.b_key = \"tftp\"\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[34mBased on the provided information, the user's instruction could be:\n", "\n", "```\"Add a parameter for the uuid of the object.\"```\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["_ = predict_edit_data(batch_of_edit_data[3], debug=True)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "There are 11 messages.\n", "[the 00-th message] Let's imagine a scenario: there is an AI assistant helping users to write the Python code.\n", "The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code.\n", "[the 01-th message] Ok, I understand this scenario, what do you want me to do?\n", "[the 02-th message] Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.\n", "Can you guess what is the user's original instruction?\n", "[the 03-th message] Sure, I will guess, what is the entire codes, and the target piece of codes?\n", "[the 04-th message] Here is the preceding part of the target piece of codes:\n", "```\n", "class GLcharNHandler(CustomHandler):\n", "  \"\"\"Handler for functions that pass a single string with an optional len.\"\"\"\n", "\n", "  def InitFunction(self, func):\n", "    \"\"\"<PERSON><PERSON><PERSON> from TypeHandler.\"\"\"\n", "    func.cmd_args = []\n", "    func.AddCmdArg(Argument('bucket_id', 'GLuint'))\n", "\n", "  def NeedsDataTransferFunction(self, func):\n", "    \"\"\"Overriden from TypeHandler.\"\"\"\n", "    return False\n", "\n", "  def WriteServiceImplementation(self, func, f):\n", "    \"\"\"<PERSON><PERSON><PERSON> from TypeHandler.\"\"\"\n", "    self.WriteServiceHandlerFunctionHeader(func, f)\n", "```\n", "[the 05-th message] Got it, what is the target piece of codes and its possible succeeding part?\n", "[the 06-th message] The target piece of code is empty, and thus the instruction will be about add something.\n", "[the 07-th message] Got it, what is its succeeding part and the final updated codes?\n", "[the 08-th message] Here is the succeeding part of the target piece of codes:\n", "```\n", "    f.write(\"\"\"\n", "  GLuint bucket_id = static_cast<GLuint>(c.%(bucket_id)s);\n", "  Bucket* bucket = GetBucket(bucket_id);\n", "  if (!bucket || bucket->size() == 0) {\n", "    return error::kInvalidArguments;\n", "  }\n", "  std::string str;\n", "  if (!bucket->GetAsString(&str)) {\n", "    return error::kInvalidArguments;\n", "  }\n", "  %(gl_func_name)s(0, str.c_str());\n", "  return error::kNoError;\n", "}\n", "\n", "\"\"\" % {\n", "\n", "```\n", "[the 09-th message] Got<PERSON>, what is the updated version?\n", "[the 10-th message] Here is the updated version\n", "```\n", "    if func.IsES31():\n", "      return\n", "```\n", "\n", "Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:\n", "- put a nice docstring to well explain this class\n", "- use promise chain instead of async\n", "- extend it to else condition\n", "- fix errors\n", "- move the accesstoken to url as a parameter of token=\n", "- make this line more readable\n", "- generate floats, not only ints\n", "- add a param to choose upper/lower\n", "- change all command names to snake case\n", "- replace <PERSON> by <PERSON><PERSON><PERSON>\n", "- this function seems will never trigger the default branch of executeChat, fix it\n", "- refactor this function by using optional_map func\n", "- simplify the cache control\n", "- console log all the ids of each file\n", "- Refactor the code. Change config.foo to CONFIG['foo']\n", "- change Optional[str] to str | None\n", "- write test train split using df pandas\n", "- instead of hard code options in the quick pick array, randomly generate 10 numbers as options\n", "- write a short pytorch module that implements linear regression, have comments\n", "- polish it with concise codes\n", "\n", "When you guess his instruction, please follow these requirements:\n", "- (1) try your best to simulate his tone;\n", "- (2) produce the instruction vague but not too vague naturely like a human, for example,\n", "-- (2.1) \"Add the import for urlquote from django.utils.http\" can be simplified as \"add the import of urlquote\";\n", "- (3) produce the instruction short and brief;\n", "- (4) in your output, please quote the instruction like ```instruction```.\n", "\n", "------------------------------ Selected Code ------------------------------\n", "class GLcharNHandler(CustomHandler):\n", "  \"\"\"Handler for functions that pass a single string with an optional len.\"\"\"\n", "\n", "  def InitFunction(self, func):\n", "    \"\"\"<PERSON><PERSON><PERSON> from TypeHandler.\"\"\"\n", "    func.cmd_args = []\n", "    func.AddCmdArg(Argument('bucket_id', 'GLuint'))\n", "\n", "  def NeedsDataTransferFunction(self, func):\n", "    \"\"\"Overriden from TypeHandler.\"\"\"\n", "    return False\n", "\n", "  def WriteServiceImplementation(self, func, f):\n", "    \"\"\"<PERSON><PERSON><PERSON> from TypeHandler.\"\"\"\n", "    self.WriteServiceHandlerFunctionHeader(func, f)\n", "\u001b[34m<<|empty-place-holder|>>\u001b[0m    f.write(\"\"\"\n", "  GLuint bucket_id = static_cast<GLuint>(c.%(bucket_id)s);\n", "  Bucket* bucket = GetBucket(bucket_id);\n", "  if (!bucket || bucket->size() == 0) {\n", "    return error::kInvalidArguments;\n", "  }\n", "  std::string str;\n", "  if (!bucket->GetAsString(&str)) {\n", "    return error::kInvalidArguments;\n", "  }\n", "  %(gl_func_name)s(0, str.c_str());\n", "  return error::kNoError;\n", "}\n", "\n", "\"\"\" % {\n", "----------------------------------------------------------------------------------------------------\n", "------------------------------ Modified Code ------------------------------\n", "class GLcharNHandler(CustomHandler):\n", "  \"\"\"Handler for functions that pass a single string with an optional len.\"\"\"\n", "\n", "  def InitFunction(self, func):\n", "    \"\"\"<PERSON><PERSON><PERSON> from TypeHandler.\"\"\"\n", "    func.cmd_args = []\n", "    func.AddCmdArg(Argument('bucket_id', 'GLuint'))\n", "\n", "  def NeedsDataTransferFunction(self, func):\n", "    \"\"\"Overriden from TypeHandler.\"\"\"\n", "    return False\n", "\n", "  def WriteServiceImplementation(self, func, f):\n", "    \"\"\"<PERSON><PERSON><PERSON> from TypeHandler.\"\"\"\n", "    self.WriteServiceHandlerFunctionHeader(func, f)\n", "\u001b[31m    if func.IsES31():\n", "      return\n", "\u001b[0m    f.write(\"\"\"\n", "  GLuint bucket_id = static_cast<GLuint>(c.%(bucket_id)s);\n", "  Bucket* bucket = GetBucket(bucket_id);\n", "  if (!bucket || bucket->size() == 0) {\n", "    return error::kInvalidArguments;\n", "  }\n", "  std::string str;\n", "  if (!bucket->GetAsString(&str)) {\n", "    return error::kInvalidArguments;\n", "  }\n", "  %(gl_func_name)s(0, str.c_str());\n", "  return error::kNoError;\n", "}\n", "\n", "\"\"\" % {\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[34mBased on the changes made in the updated code, the user's instruction could be:\n", "\n", "```Skip the function if it's ES31.```\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["_ = predict_edit_data(batch_of_edit_data[4], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(\n", "    utils_for_dataclass.create_from_json_file(EditData, json_files[5]), debug=True\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}