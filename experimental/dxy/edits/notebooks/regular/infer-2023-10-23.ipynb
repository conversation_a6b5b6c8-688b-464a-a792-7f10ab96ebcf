{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"] = \"7\"\n", "import pathlib\n", "import tqdm\n", "import json\n", "import torch\n", "from research.models.llama2_models import LLAMA2Model\n", "\n", "model = LLAMA2Model(\n", "    checkpoint_path=pathlib.Path(\n", "        # \"/mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-Data_s4k_onlytgt-5000s-lr_00001/ckp_in_llama_format\"\n", "        \"/mnt/efs/augment/user/dxy/logs/edit/CodeLLaMA-Python7B-data2v1r_full-s10000-b8-lr_000002/ckp_in_llama_format\"\n", "    )\n", ")\n", "model.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(model.model.freqs_cis[-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits import data_type\n", "from research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "from experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "\n", "data_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/json-2023-10-22/raw-cache-del_05_05/\"\n", ")\n", "jsonl_files = sorted(list(data_dir.glob(\"*-to-*-in-*.jsonl.zst\")), key=lambda x: str(x))\n", "jsonl_files = [str(x) for x in jsonl_files]\n", "print(f\"There are {len(jsonl_files)} files in total.\")\n", "batch_of_json_str = utils_for_file.read_jsonl_zst(jsonl_files[0])\n", "print(f\"There are {len(batch_of_json_str)} json files.\")\n", "\n", "# Load the training data\n", "train_data_dir = pathlib.Path(\n", "    \"/home/<USER>/datasets/edit/organized-basic-instruct-2023-10-13/\"\n", ")\n", "train_json_files = utils_for_file.read_jsonl(\n", "    train_data_dir / \"instruct-del_05_05-per40.json\"\n", ")\n", "print(f\"There are {len(train_json_files)} train json files.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_edit_fn(edit_data: data_type.EditData, instruction: str):\n", "    edit_data.show_lines()\n", "    template = EditPromptTemplate.get_input_templates()[0]\n", "    lrange = edit_data.get_lrange()\n", "    prefix = edit_data.get_prefix_via_lrange(lrange=lrange)\n", "    prefix_last_15_lines = utils_for_str.get_last_n_lines(prefix, 15)\n", "    suffix = edit_data.get_suffix_via_lrange(lrange=lrange)\n", "    suffix_first_15_lines = utils_for_str.get_first_n_lines(suffix, 15)\n", "    raw_prompt = template.format(\n", "        prefix=prefix_last_15_lines,\n", "        suffix=suffix_first_15_lines,\n", "        selected_code=edit_data.get_selected_code(),\n", "        instruction=instruction,\n", "    )\n", "    B_INST, E_INST = \"[INST]\", \"[/INST]\"\n", "    input_prompt = f\"{B_INST}\\n{raw_prompt}\\n{E_INST}\"\n", "    tokenizer = model.tokenizer\n", "    tokens = [tokenizer.bos_id] + tokenizer.tokenize(input_prompt)\n", "    output = model.raw_generate(\n", "        tokens, options=GenerationOptions(max_generated_tokens=256, top_k=0)\n", "    )\n", "    print(output)\n", "    return tokens, output"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 6\n", "edit_data = utils_for_dataclass.create_from_json(\n", "    data_type.EditData, batch_of_json_str[index]\n", ")\n", "instruction = \"rename switching_set_folder_suffix as test_stream_folder_suffix\"\n", "# instruction = \"rename switching_set_folder_suffix as test_stream_folder_suffix\"\n", "\n", "tokens, output = predict_edit_fn(edit_data, instruction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(model.tokenizer.detokenize(tokens))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the index dataset\n", "import random\n", "import torch\n", "import tqdm\n", "import numpy as np\n", "from research.core import utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData\n", "from augment.experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "\n", "\n", "def parse_data_fn(tensor: list[int]):\n", "    question = [int(-num) for num in tensor if num < 0]\n", "    answer = [int(num) for num in tensor if num > 0]\n", "    return question, answer\n", "\n", "\n", "dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/original_unpacked_data\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question_tokens, answer_tokens = parse_data_fn(dataset[2])\n", "question = model.tokenizer.detokenize(question_tokens)\n", "answer = model.tokenizer.detokenize(answer_tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["output = model.raw_generate(\n", "    [model.tokenizer.bos_id] + question_tokens,\n", "    options=GenerationOptions(max_generated_tokens=128, top_k=0),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sentencepiece import SentencePieceProcessor\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b-Python/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b-Instruct/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-13B-V1.0/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"<s>\"))\n", "print(sp_model.piece_to_id(\"</s>\"))\n", "print(sp_model.piece_to_id(\"[PAD]\"))\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the index dataset\n", "import random\n", "import torch\n", "import numpy as np\n", "from research.core import utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData\n", "from augment.experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "\n", "tokenizer_codellama = CodeLLaMATokenizer()\n", "tokenizer_llama2 = Llama2Tokenizer()\n", "\n", "print(tokenizer_codellama.bos_id)\n", "print(tokenizer_llama2.bos_id)\n", "print(tokenizer_codellama.vocab_size)\n", "print(tokenizer_llama2.vocab_size)\n", "\n", "\n", "tokenizer = tokenizer_codellama\n", "output_path = pathlib.Path(\n", "    \"/home/<USER>/datasets/edit/fine-tune-data-2023-10-temp/train_001\"\n", ")\n", "output_dir = output_path.parent\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "\n", "for edit, instruction in zip(all_edit_data, all_instruction):\n", "    question, answer = EditPromptTemplate.create_prompt(edit, instruction, tokenizer)\n", "    tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "    builder.add_item(torch.Tensor(tensor))\n", "    builder.end_document()\n", "builder.finalize(output_path.with_suffix(\".idx\"))\n", "print(output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "train_dataset = indexed_dataset.make_dataset(str(output_path), \"mmap\", skip_warmup=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset = indexed_dataset.make_dataset(\n", "    \"/mnt/efs/augment/user/guy/data-pipeline/starcoder/llama2_exported_dataset_4096/dataset\",\n", "    \"mmap\",\n", "    skip_warmup=True,\n", ")\n", "print(len(train_dataset[0]))\n", "print(len(train_dataset[1]))\n", "print(len(train_dataset[2]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_edit_data[1].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = train_dataset[1]\n", "print(type(x))\n", "print(tokenizer.detokenize(x.tolist()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(train_dataset[0]))\n", "# print(tokenizer.detokenize())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer.inv_vocab[tokenizer.eos_id]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer_codellama.vocab_size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs, targets = EditPromptTemplate.create_prompt(\n", "    all_edit_data[0], all_instruction[0], tokenizer_codellama\n", ")\n", "print(tokenizer_codellama.detokenize(inputs))\n", "print(\"-\" * 100)\n", "print(tokenizer_codellama.detokenize(targets))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_instruction[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_edit_data[0].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(targets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["skip_due_to_no_blocks[6][0].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v0(skip_due_to_no_blocks[6][0], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v1(skip_due_to_no_blocks[6][0], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v0(all_edit_data[-1], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v1(all_edit_data[-1], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_instruction[-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_edit_data[4].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_instruction[:100]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["skip_due_to_no_blocks[3].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = skip_due_to_no_blocks[3]\n", "# print(data.get_prefix_via_lrange(data.get_lrange()))\n", "print(data.get_diff_code())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(skip_due_to_no_blocks[3].get_diff_code_in_context(0, 0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "from augment.research.core import utils_for_dataclass, utils_for_file\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\n", "\n", "\n", "suffixes = [\n", "    \"del_00_00-per60\",\n", "    \"del_01_01-per60\",\n", "    \"del_02_02-per60\",\n", "    \"del_03_03-per60\",\n", "    \"del_04_04-per60\",\n", "    \"del_05_05-per60\",\n", "    \"del_06_06-per60\",\n", "    \"del_07_07-per60\",\n", "    \"del_08_08-per60\",\n", "    \"del_09_09-per60\",\n", "    \"del_10_10-per60\",\n", "    \"del_11_20-per15\",\n", "    \"del_21_30-per15\",\n", "]\n", "\n", "manager_by_suffix, total = {}, 0\n", "for suffix in suffixes:\n", "    cur_path = pathlib.Path(\n", "        f\"/home/<USER>/datasets/edit/organized-basic/manager-{suffix}.jsonl.zst\"\n", "    )\n", "    manager = EditDataManager.read_from_jsonl(str(cur_path))\n", "    manager_by_suffix[suffix] = manager\n", "    print(f\"There are {len(manager):5d} examples for {suffix}\")\n", "    total += len(manager)\n", "print(f\"There are {total} examples in total.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unique_paths = set()\n", "for x in manager.edit_data_list:\n", "    unique_paths.add(x.file_path)\n", "print(f\"There are {len(unique_paths)} unique paths in the manager.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["edit_data_list = manager.get_data_list(21, 22)\n", "print(f\"There are {len(edit_data_list)} EditData elements.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = manager.edit_data_list[0]\n", "data.show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_diff_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_selected_code(), end=\"\")\n", "print(\"-\" * 100)\n", "print(data.get_modified_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "\n", "lrange = data.get_lrange()\n", "print(\n", "    termcolor.colored(data.get_prefix_via_lrange(lrange=lrange), color=\"green\"), end=\"\"\n", ")\n", "print(\n", "    termcolor.colored(data.get_content_via_lrange(lrange=lrange), color=\"red\"), end=\"\"\n", ")\n", "print(termcolor.colored(data.get_suffix_via_lrange(lrange=lrange), color=\"blue\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.new_file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data = predict_edit_data_v0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], model=\"gpt-3.5-turbo\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[1], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[2], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[3], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import typing\n", "\n", "import tqdm\n", "\n", "from experimental.dxy.edits import data_type\n", "from research.core import utils_for_dataclass, utils_for_file\n", "\n", "\n", "manager = data_type.EditDataManager(\n", "    expected_del_range=(8, 8), edit_data_list=[], max_data_per_add=100000\n", ")\n", "data_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/json-2023-10-13/raw-cache-del_08_08/\"\n", ")\n", "assert data_dir.exists(), data_dir\n", "jsonl_files = sorted(list(data_dir.glob(\"*-to-*-in-*.jsonl.zst\")), key=lambda x: str(x))\n", "jsonl_files = [str(x) for x in jsonl_files]\n", "print(f\"There are {len(jsonl_files)} files in total.\")\n", "for jsonl_file in tqdm.tqdm(jsonl_files):\n", "    batch_of_json_str = utils_for_file.read_jsonl_zst(jsonl_file)\n", "    print(f\"There are {len(batch_of_json_str)} elements from {jsonl_file}.\")\n", "    successes = []\n", "    for json_str in batch_of_json_str:\n", "        edit_data = utils_for_dataclass.create_from_json(data_type.EditData, json_str)\n", "        success = manager.append(\n", "            edit_data,\n", "            ignore_dup_file=False,\n", "            ignore_dup_code=False,\n", "            ignore_dup_path_code=True,\n", "            verbose=False,\n", "        )\n", "        successes.append(success)\n", "    print(\n", "        f\"Successfully insert {sum(successes)} / {len(batch_of_json_str)} data.\"\n", "        f\" Current manager size: {len(manager)}.\"\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}