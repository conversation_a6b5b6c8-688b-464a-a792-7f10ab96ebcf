{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2.1/annotated.json has 102 examples.\n"]}], "source": ["import os\n", "import pathlib\n", "from research.core import utils_for_file\n", "\n", "pr_eval_path = pathlib.Path(\"/mnt/efs/augment/user/yury/pr_data/pr_suggested_edits_eval/v2.1/annotated.json\")\n", "pr_eval_data = utils_for_file.read_json(pr_eval_path)\n", "print(f\"{pr_eval_path} has {len(pr_eval_data)} examples.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["all_files = []\n", "for x in utils_for_file.fina_all_files(\"/home/<USER>/src/augment\"):\n", "    all_files.append(x)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['.git/logs/refs/remotes/origin/jacqueline/data_export_flags', '.git/logs/refs/remotes/origin/jacqueline/user_event_endpoint', '.git/logs/refs/remotes/origin/jacqueline/ri_record_user_events', '.git/logs/refs/remotes/origin/jacqueline/bigquery_dev_dataset', '.git/logs/refs/remotes/origin/jacqueline/write_user_events', '.git/logs/refs/remotes/origin/jacqueline/ri_session_user_id', '.git/logs/refs/remotes/origin/jacqueline/ri_topic_protos', '.git/logs/refs/remotes/origin/01-24-script_for_mixed_SO/SC_bin/idx_files_with_specific_ratios', '.git/info/exclude', '.git/info/refs']\n"]}], "source": ["print(all_files[-10:])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['original_instruction', 'new_code', 'original_review_file', 'repo_owner_name', 'repo_owner', 'repo_name', 'html_url', 'path', 'id', 'pr_id', 'full_pr_info', 'author', 'full_comment_info', 'graphql_comment_info', 'original_commit', 'edit_distance', 'start_line', 'end_line', 'old_file', 'old_code', 'prefix', 'suffix', 'commit_html_url', 'owner_who_has_commit', 'manual_instruction'])\n", "https://github.com/pytorch/vision/pull/4860#discussion_r743441116\n", "https://github.com/NicolasHug/vision/commit/53283c24c3831c623e7d98fd4549a1eac5969c02\n", "torchvision/datasets/_optical_flow.py\n", "validate split with utility function\n", "b'ansforms)\\n'\n", "b'        if split not in (\"train\", \"val\"):\\n            raise ValueError(\"split must be either \\'train\\' or \\'val\\'\")'\n", "b'        verify_str_arg(\"split\", split, (\"train\", \"val\"))'\n", "b'\\n        r'\n"]}], "source": ["x = pr_eval_data[0]\n", "print(x.keys())\n", "print(x['html_url'])\n", "\n", "\n", "print(x[\"commit_html_url\"])\n", "print(x[\"path\"])\n", "# print(x[\"original_instruction\"])\n", "print(x[\"manual_instruction\"])\n", "print(x[\"prefix\"][-10:].encode())\n", "print(x[\"old_code\"].encode())\n", "print(x[\"new_code\"].encode())\n", "print(x[\"suffix\"][:10].encode())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for x in pr_eval_data:\n", "    print(x[\"commit_html_url\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file, utils_for_str\n", "from research.eval.harness.tasks.edit_eval_task import EditEvalTask\n", "\n", "task = EditEvalTask()\n", "\n", "# EM: 58.4%\n", "res_wi_ret_16k_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/FfZtfj8j/dxy-8-a40__RemoteEditSystem_3a2m/cache\"\n", ")\n", "# EM: 58.4%\n", "res_wi_ret_8k_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/eqpUkxib/dxy-8-a40__RemoteEditSystem_6s3a/cache\"\n", ")\n", "# EM: 53.9%\n", "# EM: 57.3%\n", "res_wi_ret_5k_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/oNxrNTtA/dxy-8-a40__RemoteEditSystem_plhs/cache\"\n", ")\n", "# EM: 58.4%\n", "res_wo_ret_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/WwJaL4xs/dxy-8-a40__RemoteEditSystem_n3xa/cache\"\n", ")\n", "\n", "name2path: dict[str, pathlib.Path] = {\n", "    \"16K\": res_wi_ret_16k_path,\n", "    \"8K\": res_wi_ret_8k_path,\n", "    \"5K\": res_wi_ret_5k_path,\n", "    \"Non\": res_wo_ret_path,\n", "}\n", "\n", "\n", "def is_different(list_of_success: list[bool]):\n", "    first_element = list_of_success[0]\n", "    for element in list_of_success:\n", "        if element != first_element:\n", "            return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make a request\n", "from research.eval.harness.systems.remote_edit_system import RemoteEditSystem\n", "\n", "index = 4\n", "system = RemoteEditSystem(\n", "    url=\"https://dev-dxy.us-central.api.augmentcode.com\",\n", "    inference_mode=\"api_proxy\",\n", "    model=\"droid-33B-FP8-R1-edit\",\n", ")\n", "sample, repo = task[index]\n", "system.clear_retriever()\n", "system.add_docs(repo)\n", "model_input = task._edit_input_to_model_input(sample)\n", "result = system.generate(model_input)\n", "print(sample.path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.extra_output.additional_info[\"request_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["content = repo[0].text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(content[4003 : 4003 + 20].encode())\n", "print(sample.prefix[:20].encode())\n", "print(content[11664 - 20 : 11664].encode())\n", "print(sample.suffix[-20:].encode())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sample.prefix_begin)\n", "print(sample.suffix_end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for idx in range(len(task)):\n", "    sample, _ = task[idx]\n", "    cur_results, cur_successes = {}, []\n", "    for key, path in name2path.items():\n", "        cur_json = utils_for_file.read_json(path / f\"{idx}.json\")\n", "        cur_json[\"is_correct\"] = cur_json[\"generated_text\"] == sample.updated_code\n", "        cur_results[key] = cur_json\n", "        cur_successes.append(cur_json[\"is_correct\"])\n", "    if not is_different(cur_successes):\n", "        continue\n", "    print(f\"Index = {idx} makes different.\")\n", "    for key, cur_result in cur_results.items():\n", "        print(\n", "            f\"{key}::: success={cur_result['is_correct']}, RI={cur_result['request_id']}\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def display(index: int, context: int = 3):\n", "    sample, _ = task[index]\n", "    print(f\"File Name: {sample.path}\")\n", "    res_wi_ret = utils_for_file.read_json(res_wi_ret_8k_path / f\"{index}.json\")\n", "    res_wo_ret = utils_for_file.read_json(res_wo_ret_path / f\"{index}.json\")\n", "    print(f\"RI w/. retrieval: {res_wi_ret['request_id']}\")\n", "    print(f\"RI w/o retrieval: {res_wo_ret['request_id']}\")\n", "    print(f\"Instruction: {sample.instruction}\")\n", "    print(\n", "        \"-\" * 32\n", "        + \" non-retrieval\"\n", "        + f\" ({res_wo_ret['generated_text']==sample.updated_code}) \"\n", "        + \"-\" * 32\n", "    )\n", "    print(utils_for_str.get_last_n_lines(sample.prefix, context), end=\"\")\n", "    print(\n", "        utils_for_str.get_diff_str(sample.selected_code, res_wo_ret[\"generated_text\"])\n", "    )\n", "    print(utils_for_str.get_first_n_lines(sample.suffix, context), end=\"\\n\\n\")\n", "    print(\n", "        \"-\" * 32\n", "        + \" retrieval\"\n", "        + f\" ({res_wi_ret['generated_text']==sample.updated_code}) \"\n", "        + \"-\" * 32\n", "    )\n", "    print(utils_for_str.get_last_n_lines(sample.prefix, context), end=\"\")\n", "    print(\n", "        utils_for_str.get_diff_str(sample.selected_code, res_wi_ret[\"generated_text\"])\n", "    )\n", "    print(utils_for_str.get_first_n_lines(sample.suffix, context), end=\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(5, context=10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(14, context=10)\n", "# index = 0\n", "# print(task[index][0].updated_code)\n", "# print(task[index][0].selected_code)\n", "# print(res_wo_ret[\"generated_text\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}