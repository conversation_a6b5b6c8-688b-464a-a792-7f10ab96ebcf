{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "import random\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "\n", "tokenizer = Llama2Tokenizer()\n", "\n", "\n", "def split_list(lst: list[int], delimiter: int):\n", "    result = []\n", "    current_subsequence = []\n", "    for item in lst:\n", "        if item == delimiter:\n", "            if current_subsequence:  # Avoid appending empty lists\n", "                result.append(current_subsequence)\n", "                current_subsequence = []\n", "        else:\n", "            current_subsequence.append(item)\n", "    if current_subsequence:  # Append the last subsequence if it's not empty\n", "        result.append(current_subsequence)\n", "    return result\n", "\n", "\n", "def show_dataset_info(dataset: MMapIndexedDataset):\n", "    print(f\"There are {len(dataset)} examples\")\n", "    index = random.randint(0, len(dataset) - 1)\n", "    example = dataset[index]\n", "    negative_num = np.sum(example < 0).item()  # type: ignore\n", "    print(\n", "        termcolor.colored(\n", "            f\"Index={index:5d}, negative_num={negative_num}\", color=\"green\"\n", "        )\n", "    )\n", "    example: list[int] = np.abs(example).tolist()\n", "    parts = split_list(example, tokenizer.eos_id)\n", "    parts = [x for x in parts if x != [tokenizer.eos_id]]\n", "    print(\n", "        termcolor.colored(f\"There are {len(parts)} unpacked examples.\", color=\"green\")\n", "    )\n", "    for j, part in enumerate(parts):\n", "        print(termcolor.colored(f\"Part {j:5d}/{len(parts):5d}\", color=\"blue\"))\n", "        results = tokenizer.detok<PERSON>ze(part)\n", "        print(results)\n", "        print(\"*\" * 100 + \"\\n\")\n", "    print(\"-\" * 100 + \"\\n\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "There are 1095 examples\n", "\u001b[32mIndex=  477, negative_num=3318\u001b[0m\n", "\u001b[32mThere are 10 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON>art     0/   10\u001b[0m\n", "<INST>Upgrade Mocha to version 3.4.2.<SELECTED>    \"reify\": \"^0.11.0\",\n", "    \"mocha\": \"~3.4.0\"\n", "<SUFFIX>  },\n", "  \"engines\": {\n", "    \"node\": \">= 0.8\"\n", "  }\n", "}\n", "<PREFIX>{\n", "  \"author\": \"<PERSON> <<EMAIL>>\",\n", "  \"name\": \"ast-types\",\n", "  \"description\": \"Esprima-compatible implementation of the Mozilla JS Parser API\",\n", "  \"keywords\": [\n", "    \"ast\",\n", "    \"abstract syntax tree\",\n", "    \"hierarchy\",\n", "    \"mozilla\",\n", "    \"spidermonkey\",\n", "    \"parser api\",\n", "    \"esprima\",\n", "    \"types\",\n", "    \"type system\",\n", "    \"type checking\",\n", "    \"dynamic types\",\n", "    \"parsing\",\n", "    \"transformation\",\n", "    \"syntax\"\n", "  ],\n", "  \"version\": \"0.9.11\",\n", "  \"homepage\": \"http://github.com/benjamn/ast-types\",\n", "  \"repository\": {\n", "    \"type\": \"git\",\n", "    \"url\": \"git://github.com/benjamn/ast-types.git\"\n", "  },\n", "  \"license\": \"MIT\",\n", "  \"main\": \"main.js\",\n", "  \"scripts\": {\n", "    \"test\": \"mocha --reporter spec --full-trace test/run.js\"\n", "  },\n", "  \"dependencies\": {},\n", "  \"devDependencies\": {\n", "    \"babel-types\": \"^6.23.0\",\n", "    \"babylon\": \"^6.16.1\",\n", "    \"espree\": \"^3.1.7\",\n", "    \"esprima\": \"~3.1.3\",\n", "    \"esprima-fb\": \"~14001.1.0-dev-harmony-fb\",\n", "<UPDATED> ```\n", "    \"mocha\": \"^3.4.2\",\n", "    \"reify\": \"^0.11.0\"\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON>art     1/   10\u001b[0m\n", "<INST>Add default implementation of Subsample interface.<SELECTED><SUFFIX><PREFIX><UPDATED> ```\n", "package net.imagej.ops.views;\n", "\n", "import net.imagej.ops.view.ViewOps;\n", "import net.imagej.ops.view.ViewOps.Subsample;\n", "import net.imglib2.RandomAccessible;\n", "import net.imglib2.view.SubsampleView;\n", "import net.imglib2.view.Views;\n", "\n", "import org.scijava.ItemIO;\n", "import org.scijava.plugin.Parameter;\n", "import org.scijava.plugin.Plugin;\n", "\n", "@Plugin(type = ViewOps.Subsample.class, name = ViewOps.Subsample.NAME)\n", "public class DefaultSubsample<T> extends\n", "\t\tAbstractView<RandomAccessible<T>, SubsampleView<T>> implements\n", "\t\tSubsample<RandomAccessible<T>, SubsampleView<T>> {\n", "\n", "\t@Parameter(type = ItemIO.INPUT)\n", "\tprivate long step;\n", "\n", "\t@Override\n", "\tpublic SubsampleView<T> compute(RandomAccessible<T> input) {\n", "\t\treturn Views.subsample(input, step);\n", "\t}\n", "\n", "}\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     2/   10\u001b[0m\n", "<INST>Remove check for null params in onSubmit<SELECTED>      SearchParams params = getModelObject();\n", "      if (params != null) {\n", "        setResponsePage(new HomePage(new SearchTweetRetriever(params)));\n", "      }\n", "<SUFFIX>    }\n", "  }\n", "}\n", "<PREFIX>package com.grailbox.twicket;\n", "\n", "import org.apache.wicket.markup.html.basic.Label;\n", "import org.apache.wicket.markup.html.form.StatelessForm;\n", "import org.apache.wicket.markup.html.form.TextField;\n", "import org.apache.wicket.markup.html.panel.Panel;\n", "import org.apache.wicket.model.CompoundPropertyModel;\n", "import org.apache.wicket.validation.validator.StringValidator.MinimumLengthValidator;\n", "\n", "public class SearchPanel extends Panel {\n", "  private static final long serialVersionUID = -8610733111417498232L;\n", "\n", "  public SearchPanel(String id) {\n", "    super(id);\n", "    add(new SearchForm(\"searchForm\"));\n", "  }\n", "\n", "  private class SearchForm extends StatelessForm<SearchParams> {\n", "    private static final long serialVersionUID = -5253297831508495384L;\n", "\n", "    private SearchForm(final String id) {\n", "      super(id);\n", "      setModel(new CompoundPropertyModel<SearchParams>(new SearchParams()));\n", "      add(new Label(\"searchLabel\", \"Search Terms:\"));\n", "      add(new TextField<String>(\"searchString\").setRequired(true).add(new MinimumLengthValidator(3)));\n", "    }\n", "\n", "    @Override\n", "    protected final void onSubmit() {\n", "<UPDATED> ```\n", "      setResponsePage(new HomePage(new SearchTweetRetriever(getModelObject())));\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     3/   10\u001b[0m\n", "<INST>Add styling to H1 and H2<SELECTED>}\n", "\n", "\n", "<SUFFIX>\n", "/*****************************************************\n", "COLORS\n", "******************************************************/\n", "\n", "/* site body */\n", "body {\n", "  background-color: #fff;\n", "  color: #999;\n", "}\n", "\n", "/* green header */\n", "header {\n", "  background: #6ab47b;\n", "  border-color: #599a68;\n", "}\n", "\n", "/* nav background on mobile */\n", "nav {\n", "  background: #599a68;\n", "}\n", "\n", "/* logo text */\n", "h1, h2 {\n", "  color: #fff;\n", "}\n", "\n", "/* links */\n", "a {\n", "  color: #6ab47b;\n", "}\n", "\n", "/* nav link */\n", "nav a, a:visited {\n", "  color: #fff;\n", "}\n", "\n", "/* selected nav link */\n", "nav a.selected, nav a:hover {\n", "  color: #32673f;\n", "}\n", "\n", "\n", "/**************************************/\n", ".product-tabs .active {\n", "  color: white;\n", "  background: orange;\n", "}<PREFIX>/*****************************************************\n", "GENERAL\n", "******************************************************/\n", "\n", "#wrapper {\n", "  max-width: 940px;\n", "  margin: 0 auto;\n", "  padding: 0 5%;\n", "}\n", "\n", "a {\n", "  text-decoration: none;\n", "}\n", "\n", "\n", "\n", "/*****************************************************\n", "HEADING\n", "******************************************************/\n", "\n", "#logo {\n", "  text-align: center;\n", "  margin: 0;\n", "}\n", "\n", "h1 {\n", "  font-family: 'Changa One', sans-serif;\n", "  margin: 15px 0;\n", "  font-size: 1.75em;\n", "<UPDATED> ```\n", "  font-weight: normal;\n", "  line-height: 0.8em;\n", "}\n", "\n", "h2 {\n", "  font-size: 0.75em;\n", "  margin: -5px 0 0;\n", "  font-weight: normal;\n", "}\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     4/   10\u001b[0m\n", "<INST>Use official discourse-plugin-ci for Travis CI<SELECTED>sudo: required # required: https://github.com/discourse/mini_racer#travis-ci\n", "dist: trusty # required: https://github.com/discourse/mini_racer#travis-ci\n", "language: ruby\n", "cache:\n", "  directories:\n", "    - discourse/vendor/bundle\n", "services:\n", "  - postgresql\n", "  - redis-server\n", "addons:\n", "  postgresql: 9.3\n", "  apt:\n", "    packages:\n", "    - gifsicle\n", "    - <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    - optipng\n", "    - j<PERSON>\n", "  code_climate:\n", "    repo_token: 34de2e1d1093068fa2e757bfce3a6214ae11c4b3f63bf36d81ec105c04ac2463\n", "services:\n", "  - redis-server\n", "env:\n", "  - LOAD_PLUGINS=1 RUN_COVERAGE=1 RAILS_ENV=test\n", "before_install:\n", "  - cd discourse\n", "  - git init\n", "  - git remote add -t \\* -f origin https://github.com/discourse/discourse.git\n", "  - git checkout tags/latest-release\n", "  - mkdir plugins/discourse-nntp-bridge\n", "  - cd ..\n", "  - shopt -s extglob dotglob\n", "  - mv !(discourse) discourse/plugins/discourse-nntp-bridge/\n", "  - shopt -u dotglob\n", "  - cd discourse\n", "  - echo \"gem 'simplecov'\" >> Gemfile\n", "  - bundle install\n", "script:\n", "  - bundle exec rake db:create db:migrate db:test:prepare\n", "  - bundle exec rake plugin:spec[discourse-nntp-bridge]\n", "after_script:\n", "  - gem install codeclimate-test-reporter\n", "  - codeclimate-test-reporter --path-prefix=\"plugins/discourse-nntp-bridge\"\n", "<SUFFIX><PREFIX><UPDATED> ```\n", "# We want to use the KVM-based system, so require sudo\n", "sudo: required\n", "services:\n", "  - docker\n", "\n", "before_install:\n", "  - git clone --depth=1 https://github.com/discourse/discourse-plugin-ci\n", "\n", "install: true # Prevent travis doing bundle install\n", "\n", "script:\n", "  - discourse-plugin-ci/script.sh\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     5/   10\u001b[0m\n", "<INST>Include any version of fzf<SELECTED>. ~/homebrew/Cellar/fzf/0.12.0/shell/completion.zsh\n", ". ~/homebrew/Cellar/fzf/0.12.0/shell/key-bindings.zsh\n", "<SUFFIX>\n", "alias gco='$FZF_ROOT/pass_thru.sh mirror_repo'\n", "alias gcoo='$FZF_ROOT/pass_thru.sh mirror_repo_oxid'\n", "alias gcor='$FZF_ROOT/pass_thru.sh mirror_repo_rezonanc'\n", "<PREFIX>export FZF_ROOT=\"$HOME/bin/fzf\"\n", "\n", "<UPDATED> ```\n", ". ~/homebrew/Cellar/fzf/*/shell/completion.zsh\n", ". ~/homebrew/Cellar/fzf/*/shell/key-bindings.zsh\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     6/   10\u001b[0m\n", "<INST>Update Node.js documentation (6.3.1, 4.4.7)<SELECTED>      self.release = '6.2.2'\n", "      self.base_url = 'https://nodejs.org/api/'\n", "    end\n", "\n", "    version '4 LTS' do\n", "      self.release = '4.4.6'\n", "<SUFFIX>      self.base_url = \"https://nodejs.org/dist/v#{release}/docs/api/\"\n", "    end\n", "  end\n", "end\n", "<PREFIX>module Docs\n", "  class Node < UrlScraper\n", "    self.name = 'Node.js'\n", "    self.slug = 'node'\n", "    self.type = 'node'\n", "    self.links = {\n", "      home: 'https://nodejs.org/',\n", "      code: 'https://github.com/nodejs/node'\n", "    }\n", "\n", "    html_filters.push 'node/clean_html', 'node/entries', 'title'\n", "\n", "    options[:title] = false\n", "    options[:root_title] = 'Node.js'\n", "    options[:container] = '#apicontent'\n", "    options[:skip] = %w(index.html all.html documentation.html synopsis.html)\n", "\n", "    options[:attribution] = <<-HTML\n", "      &copy; Joyent, Inc. and other Node contributors<br>\n", "      Licensed under the MIT License.<br>\n", "      Node.js is a trademark of Joyent, Inc. and is used with its permission.<br>\n", "      We are not endorsed by or affiliated with <PERSON><PERSON>.\n", "    HTML\n", "\n", "    version do\n", "<UPDATED> ```\n", "      self.release = '6.3.1'\n", "      self.base_url = 'https://nodejs.org/api/'\n", "    end\n", "\n", "    version '4 LTS' do\n", "      self.release = '4.4.7'\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     7/   10\u001b[0m\n", "<INST>Update English (en-GB) localization of Pontoon Intro.<SELECTED>\n", "# Header\n", "upper-title=Pontoon by Mo<PERSON>\n", "headline-1=LocaliSe the web.\n", "<SUFFIX>\n", "# What\n", "what-desc=Pontoon allows you to localise web content in place, with context and spatial limitations right in front of you.\n", "context-desc=By localising web page on the page itself, you no longer need to worry if the word you are translating is a verb or noun.\n", "\n", "# How\n", "how-desc=Pontoon is a very simple and intuitive tool that requires little to no technical skill for localisers to use.\n", "hover-desc=Move your mouse over headings, links, paragraphs or other text blocks on this page. A dashed rectangle will appear around each of these blocks, marking strings that are available for localisation on the page itself.\n", "\n", "# More\n", "\n", "# Developers\n", "\n", "# Footer\n", "author=Crafted by Mozilla\n", "\n", "<PREFIX># Title tag\n", "\n", "# Navigation\n", "<UPDATED> ```\n", "navigation-title=Pontoon Intro\n", "navigation-developers=Developers\n", "\n", "# Header\n", "upper-title=Pontoon by Mo<PERSON>\n", "headline-1=LocaliSe the web.\n", "headline-2=In Place.\n", "call-to-action=<PERSON>\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     8/   10\u001b[0m\n", "<INST>Add paragraph explaining that ECL has two loop implementations.<SELECTED>\\ecl{} uses MIT LOOP with only minor modifications.\n", "<SUFFIX>\n", "\\sbcl{} includes an implementation of the \\texttt{loop} macro that was\n", "originally derived from MIT LOOP, but that also includes code from the\n", "\\texttt{loop} macro of the \\genera{} operating system.  Furthermore,\n", "the \\sbcl{} implementation of the \\texttt{loop} macro has been\n", "modified and improved to allow for user-definable extensions, such as\n", "the extension defined in \\cite{Rhodes:2007:USC:1622123.1622138} for\n", "iterating over user-definable sequences.\n", "\n", "\\clisp{} has its own implementation of the \\texttt{loop} macro.  The\n", "bulk of the implementation can be found in a function named\n", "\\texttt{expand-loop}.  This function consists of more than $900$ lines\n", "of code.\n", "<PREFIX>\\section{Previous work}\n", "\n", "One of the first implementations of the \\commonlisp{} \\texttt{loop}\n", "macro is the one that is often referred to as ``MIT LOOP''\n", "\\cite{Burke:Moon:MIT.loop}.\n", "\n", "<UPDATED> ```\n", "\\ecl{} includes two implementations of the \\texttt{loop} macro, namely\n", "the initial MIT LOOP with only minor modifications, and the variation\n", "by \\symbolics{} also with minor modifications.\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     9/   10\u001b[0m\n", "<INST>Use the python in the virtualenv for everything<SELECTED>envlist = py26,py27,py33,pep8,cover\n", "\n", "[testenv]\n", "<SUFFIX>setenv = VIRTUAL_ENV={envdir}\n", "         LANG=en_US.UTF-8\n", "         LANGUAGE=en_US:en\n", "         LC_ALL=C\n", "\n", "deps = -r{toxinidir}/requirements.txt\n", "       -r{toxinidir}/test-requirements.txt\n", "commands = python setup.py testr --testr-args='{posargs}'\n", "\n", "[testenv:pep8]\n", "commands = flake8\n", "\n", "[testenv:venv]\n", "commands = {posargs}\n", "\n", "[testenv:cover]\n", "commands = coverage erase\n", "           python setup.py testr --coverage --omit='tuskarclient/tests/*' --testr-args='{posargs}'\n", "           coverage report -m\n", "\n", "[tox:jenkins]\n", "downloadcache = ~/cache/pip\n", "\n", "[flake8]\n", "ignore =\n", "show-source = True\n", "exclude=.venv,.git,.tox,dist,doc,*openstack/common*,*lib/python*,*egg,build\n", "<PREFIX>[tox]\n", "<UPDATED> ```\n", "minversion = 1.6\n", "skipsdist = True\n", "envlist = py26,py27,py33,pep8,cover\n", "\n", "[testenv]\n", "usedevelop = True\n", "install_command = pip install {opts} {packages}\n", "```\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-11-02/valid-v3_r1n_s4096_onlytgt_pack\"\n", ")\n", "show_dataset_info(dataset)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n"]}], "source": ["dataset_python_inhouse = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid_r1n_s4096_onlytgt\"\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 1018 examples\n", "\u001b[32mIndex=  991, negative_num=2350\u001b[0m\n", "\u001b[32mThere are 4 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON><PERSON>     0/    4\u001b[0m\n", "[INST]\n", "Here are some codes:\n", "```\n", "        print()\n", "        \n", "        if epoch % 10 == 0:\n", "            # save checkpoint\n", "            PATH = \"label_convert_{}.pt\".format(epoch)\n", "            torch.save({\n", "                'epoch': epoch,\n", "                'model_state_dict': model.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'loss': epoch_loss\n", "                }, PATH)\n", "\n", "    time_elapsed = time.time() - start\n", "    print('Training complete in {:.0f}m {:.0f}s'.format(\n", "        time_elapsed // 60, time_elapsed % 60))\n", "************************\n", "    print('Best val loss: {:4f}'.format(best_loss))\n", "\n", "    # load best model weights\n", "    model.load_state_dict(best_model_wts)\n", "    return model\n", "\n", "************************\n", "   \n", "        \n", "#------------------------------ Main code here -------------------------------#\n", "if __name__ == \"__main__\":\n", "    try:\n", "        torch.multiprocessing.set_start_method('spawn')    \n", "    except:\n", "        pass\n", "    \n", "    # use this to watch gpu in console            watch -n 2 nvidia-smi\n", "    # CUDA for PyTorch\n", "    use_cuda = torch.cuda.is_available()\n", "    device = torch.device(\"cuda:0\" if use_cuda else \"cpu\")\n", "    torch.cuda.empty_cache()\n", "\n", "```\n", "please suggest modifications to (optionally empty) codes enclosed by consequent `*` that align with this instruction: 'add a new class called Point_Loss and print the best epoch as well'\n", "[/INST] The modified codes should be :\n", "```\n", "    print('Best val loss: {:4f}, epoch {}'.format(best_loss,best_epoch))\n", "\n", "    # load best model weights\n", "    model.load_state_dict(best_model_wts)\n", "    return model\n", "\n", "\n", "class Point_Loss(nn.Module):        \n", "    def __init__(self):\n", "        super(Point_Loss,self).__init__()\n", "        \n", "    def forward(self,output,target,Pinv,pts_2d,device):\n", "        \"\"\" Assumes Pinv and pts_2d are tensors\"\"\"\n", "        \n", "        # concatenate a third row of ones with pts_2d\n", "        ones = torch.ones((pts_2d.shape[0],1,pts_2d.shape[-1])).to(device)\n", "        pts_2d = torch.cat((pts_2d,ones),1)\n", "        \n", "        # multiply pts_2d by output and target, respectively\n", "        output = output.unsqueeze(1)\n", "        target = target.unsqueeze(1)\n", "        out_scaled = torch.mul(pts_2d,output)\n", "        targ_scaled = torch.mul(pts_2d,target)\n", "    \n", "        \n", "        # multiply each result by Pinv\n", "        Pinv = torch.stack([Pinv for i in range(0,targ_scaled.shape[0])])\n", "        out_3d = torch.bmm(Pinv,out_scaled)\n", "        targ_3d = torch.bmm(Pinv,targ_scaled)\n", "        loss = nn.<PERSON><PERSON><PERSON>()\n", "        return loss(out_3d,targ_3d)\n", "\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON>art     1/    4\u001b[0m\n", "[INST]\n", "Please evaluate the code snippet enclosed by `#` symbols below:\n", "```\n", "    assert len(M.index(\"foo\").eq(\"meh\")) == 5\n", "    assert len(M.index(\"foo\").eq(\"meh\")) == 5\n", "    assert M.index(\"foo\").isin([\"bar\", \"not\"]).matrix.nnz == 2\n", "\n", "    numpy.random.seed()\n", "    seed = numpy.random.randint(0, 100000)\n", "    sel_gids = numpy.random.choice(M.gids[M.depth < M.depth[-1]], 4, replace=False)\n", "    numpy.random.seed(seed)\n", "    gids = M.index(\"depth\").random_numerical_gids(sel_gids, n_bins=2)\n", "    assert M.gids[-1] not in gids\n", "    assert len(gids) == len(sel_gids)\n", "    numpy.random.seed(seed)\n", "    subpop = M.index(\"depth\").random_numerical(sel_gids, n_bins=2)\n", "    assert numpy.all(gids == subpop.gids)\n", "\n", "##########################\n", "\n", "def test_connectivity_matrix_edge_indexing():\n", "    M = test_module.ConnectivityMatrix(m_sparse, vertex_properties=props)\n", "    assert M.filter().lt(2).matrix.nnz == M.matrix.nnz\n", "    assert M.filter().lt(1).matrix.nnz == 0\n", "\n", "    numpy.random.seed(123)\n", "    <PERSON>.add_edge_property(\"for_testing\", numpy.random.rand(len(M._edges)))\n", "    assert M.default(\"for_testing\").filter().lt(0.5).matrix.nnz == 3\n", "\n", "##########################\n", "\n", "def test_load_save():\n", "    M = test_module.ConnectivityMatrix(m_sparse, vertex_properties=props)\n", "    <PERSON>.to_h5(\"test.h5\", group_name=\"test_group\", prefix=\"test_matrix\")\n", "    N = test_module.ConnectivityMatrix.from_h5(\"test.h5\", group_name=\"test_group\",\n", "    prefix=\"test_matrix\")\n", "    assert (M.array == N.array).all()\n", "\n", "\n", "def test_time_dependent_matrix():\n", "    numpy.random.seed(123)\n", "    row = numpy.random.randint(0, 100, 500)\n", "    col = numpy.random.randint(0, 100, 500)\n", "    rowcol = pandas.DataFrame({\"row\": row, \"col\": col}).drop_duplicates()\n", "    L = len(rowcol)\n", "```\n", "And make changes as per this directive: 'Extend the test for connectivity matrix edge indexing and add more assertions.'.\n", "[/INST] Here's the code with the suggested changes:\n", "```\n", "    sel_gids = M.gids[[0, -1]]\n", "    subpop = M.index(\"foo\").random_categorical(sel_gids)\n", "    assert len(subpop) == 2\n", "    assert \"bar\" in subpop.foo and \"meh\" in subpop.foo\n", "\n", "\n", "def test_connectivity_matrix_edge_indexing_and_adding():\n", "    M = test_module.ConnectivityMatrix(m_sparse, vertex_properties=props)\n", "    assert M.filter().lt(2).matrix.nnz == M.matrix.nnz\n", "    assert M.filter().lt(1).matrix.nnz == 0\n", "\n", "    numpy.random.seed(123)\n", "    <PERSON>.add_edge_property(\"for_testing\", numpy.random.rand(len(M._edges)))\n", "    Md = M.default(\"for_testing\")\n", "    assert len(Md.filter().lt(0.5)._edges) + len(Md.filter().ge(0.5)._edges) == len(M._edges)\n", "    assert len(Md.filter().le(0.5)._edges) + len(Md.filter().gt(0.5)._edges) == len(M._edges)\n", "    assert M.default(\"for_testing\").filter().lt(0.5).matrix.nnz == 3\n", "\n", "    int_property = numpy.random.randint(1, 6, size=M.array.shape)\n", "    int_property[M.array == 0] = 0\n", "    <PERSON>.add_edge_property(\"category_testing\", int_property)\n", "    <PERSON>.add_edge_property(\"category_testing2\", sparse.coo_matrix(int_property))\n", "    assert len(<PERSON><PERSON>filter(\"category_testing\").isin([1, 2, 3, 4, 5])._edges) == len(M._edges)\n", "    fltrd_M = <PERSON>.filter(\"category_testing\").eq(1)\n", "    assert (fltrd_M.edges[\"category_testing\"] == 1).mean() == 1.0\n", "\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     2/    4\u001b[0m\n", "[INST]\n", "Here are some codes:\n", "```\n", "            ascii_path = dirpath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            if not os.path.exists(localfs_path):\n", "                os.makedirs(localfs_path)\n", "\n", "    def read(self, filepath, offset, size):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii','ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            with open(localfs_path, \"r\") as f:\n", "                f.seek(offset)\n", "                buf = f.read(size)\n", "\n", "            return buf\n", "\n", "************************\n", "    def write(self, filepath, buf):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            with open(localfs_path, \"w\") as f:\n", "                f.write(buf)\n", "\n", "    def unlink(self, filepath):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            if not os.path.exists(localfs_path):\n", "                os.unlink(localfs_path)\n", "************************\n", "\n", "    def clear_cache(self, path):\n", "        pass\n", "\n", "    def plugin(self):\n", "        return self.__class__\n", "\n", "    def role(self):\n", "        return self.role\n", "\n", "    def set_notification_cb(self, notification_cb):\n", "        self.notification_cb = notification_cb\n", "```\n", "please suggest modifications to (optionally empty) codes enclosed by consequent `*` that align with this instruction: 'Modify the write function to include an offset parameter and use it when writing to the file. Also, add functions to rename files, set and get extended attributes, and list all extended attributes of a file.'\n", "[/INST] The revised codes are\n", "```\n", "    def write(self, filepath, offset, buf):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            with open(localfs_path, \"w\") as f:\n", "                f.seek(offset, 0)\n", "                f.write(buf)\n", "\n", "    def unlink(self, filepath):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            os.unlink(localfs_path)\n", "\n", "    def rename(self, filepath1, filepath2):\n", "        with self._get_lock():\n", "            ascii_path1 = filepath1.encode('ascii', 'ignore')\n", "            ascii_path2 = filepath2.encode('ascii', 'ignore')\n", "            localfs_path1 = self._make_localfs_path(ascii_path1)\n", "            localfs_path2 = self._make_localfs_path(ascii_path2)\n", "            os.rename(localfs_path1, localfs_path2)\n", "\n", "    def set_xattr(self, filepath, key, value):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            xattr.setxattr(localfs_path, key, value)\n", "\n", "    def get_xattr(self, filepath, key):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            return xattr.getxattr(localfs_path, key)\n", "\n", "    def list_xattr(self, filepath):\n", "        with self._get_lock():\n", "            ascii_path = filepath.encode('ascii', 'ignore')\n", "            localfs_path = self._make_localfs_path(ascii_path)\n", "            return xattr.listxattr(localfs_path)\n", "```\n", "****************************************************************************************************\n", "\n", "\u001b[34m<PERSON><PERSON>     3/    4\u001b[0m\n", "[INST]\n", "Within this code block:\n", "```\n", "\n", "The `urlpatterns` list routes URLs to views. For more information please see:\n", "    https://docs.djangoproject.com/en/4.2/topics/http/urls/\n", "Examples:\n", "Function views\n", "    1. Add an import:  from my_app import views\n", "    2. Add a URL to urlpatterns:  path('', views.home, name='home')\n", "Class-based views\n", "    1. Add an import:  from other_app.views import Home\n", "    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')\n", "Including another URLconf\n", "    1. Import the include() function: from django.urls import include, path\n", "    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))\n", "\"\"\"\n", "from django.contrib import admin\n", "++++++++++++++++++++++++++\n", "from django.urls import path\n", "from api.views import front, book, book_detail\n", "\n", "urlpatterns = [\n", "    path('admin/', admin.site.urls),\n", "    path(\"\", front, name=\"front\"),\n", "    path(\"api/book/\", book, name=\"book\"),\n", "    path(\"api/book/<int:pk>/\", book_detail, name=\"detail\")\n", "++++++++++++++++++++++++++\n", "]\n", "```\n", "Identify and modify the segment between the + signs following the guideline: 'Add JWT token paths and include user urls in urlpatterns'.\n", "[/INST] Your code has been updated as follows:\n", "```\n", "from django.urls import path, include\n", "from api.views import front, book, book_detail\n", "# from api.views import front\n", "from rest_framework_simplejwt.views import (\n", "    TokenObtainPairView,\n", "    TokenRefreshView,\n", ")\n", "\n", "urlpatterns = [\n", "    path('admin/', \n", "          admin.site.urls),\n", "\n", "    # JWT\n", "    path('api/token/', \n", "          TokenObtainPairView.as_view(), \n", "          name='token_obtain_pair'),\n", "    path('api/token/refresh/', \n", "          TokenRefreshView.as_view(), \n", "          name='token_refresh'),\n", "\n", "    path(\"\", \n", "          front, \n", "          name=\"front\"),\n", "    path(\"api/user/\",\n", "          include('users.urls', namespace='users')),\n", "    path(\"api/book/\", \n", "          book, \n", "          name=\"book\"),\n", "    path(\"api/book/<int:pk>/\", \n", "          book_detail, \n", "          name=\"detail\")\n", "```\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["show_dataset_info(dataset_python_inhouse)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n"]}], "source": ["dataset_python_inhouse_plus_commitpack = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad\"\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 2472 examples\n", "\u001b[32mIndex=  405, negative_num=3976\u001b[0m\n", "\u001b[32mThere are 1 unpacked examples.\u001b[0m\n", "\u001b[34m<PERSON>art     0/    1\u001b[0m\n", "<INST>Add try block to argv remove statement<SELECTED>        argv.remove('--with-watch')\n", "        watchcmd = 'clear && ' + ' '.join(argv)\n", "        call_args = ['watchmedo', 'shell-command', '-c',\n", "            watchcmd, '-R', '-p', '*.py', '.']\n", "        try:\n", "            self.call(call_args)\n", "        except KeyboardInterrupt:\n", "            self.sys.stdout.write('\\nStopped\\n')\n", "\n", "<SUFFIX><PREFIX>import sys\n", "from nose.plugins import Plugin\n", "from subprocess import Popen\n", "\n", "\n", "class WatchPlugin(Plugin):\n", "    \"\"\"\n", "    Plugin that use watchdog for continuous tests run.\n", "    \"\"\"\n", "    name = 'watch'\n", "    is_watching = False\n", "    sys = sys\n", "\n", "    def call(self, args):\n", "        <PERSON><PERSON>(args).wait()\n", "\n", "    def finalize(self, result):\n", "        argv = list(self.sys.argv)\n", "<UPDATED> ```\n", "        try:\n", "            argv.remove('--with-watch')\n", "        except ValueError:\n", "            pass\n", "        watchcmd = 'clear && ' + ' '.join(argv)\n", "        call_args = ['watchmedo', 'shell-command', '-c',\n", "            watchcmd, '-R', '-p', '*.py', '.']\n", "        try:\n", "            self.call(call_args)\n", "        except KeyboardInterrupt:\n", "            self.sys.stdout.write('\\nStopped\\n')\n", "```\n", "****************************************************************************************************\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["show_dataset_info(dataset_python_inhouse_plus_commitpack)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}