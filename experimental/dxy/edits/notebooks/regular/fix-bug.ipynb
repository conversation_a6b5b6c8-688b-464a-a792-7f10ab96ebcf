{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load the Raw Data"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "path_to_data = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.raw/dogfood_edit_logs/2023-12-20-at-01-28-46-TID139945142880000-77nym2iu.json\"\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_file, utils_for_str\n", "\n", "data_dict = utils_for_file.read_json(path_to_data)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['instruction', 'selected_code', 'prefix', 'suffix', 'top_k', 'top_p', 'temperature', 'lang', 'lines_in_prefix_suffix', 'blobs', 'memories', 'response', 'full_response', 'prompt', 'actual_prefix_in_prompt', 'actual_suffix_in_prompt', 'sender_ip', 'time_cost(seconds)', 'thread_id'])\n"]}], "source": ["print(data_dict.keys())"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--file_store_path\",\n", "        type=Path,\n", "        default=Path(\"/tmp\"),\n", "        help=\"Where to persistently store blobs/files uploaded by the extension\",\n", "    )\n", "    args = parser.parse_args()\n", "\n", "    file_store = FileStore(args.file_store_path)\n", "    docs: list[Document] = file_store.get_files()\n", "    for doc in docs:\n", "        print(doc)\n", "\n"]}], "source": ["print(data_dict[\"selected_code\"])"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```\n", "def print_files(file_store):\n", "    docs: list[Document] = file_store.get_files()\n", "    for doc in docs:\n", "        print(doc)\n", "\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--file_store_path\",\n", "        type=Path,\n", "        default=Path(\"/tmp\"),\n", "        help=\"Where to persistently store blobs/files uploaded by the extension\",\n", "    )\n", "    args = parser.parse_args()\n", "\n", "    file_store = FileStore(args.file_store_path)\n", "    print_files(file_store)\n", "```\n", "\n"]}], "source": ["print(data_dict[\"full_response\"])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"\"\"Show the files stored in the file store.\"\"\"\n", "\n", "import argparse\n", "\n", "from research.model_server.file_store import FileStore\n", "\n", "\n"]}], "source": ["print(data_dict[\"prefix\"])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--file_store_path\",\n", "        type=Path,\n", "        default=Path(\"/tmp\"),\n", "\n"]}], "source": ["print(data_dict[\"response\"])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "if __name__ == \"__main__\":\n", "    main()\n", "\n", "    \n"]}], "source": ["print(data_dict[\"suffix\"])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def print_files(file_store):\n", "    docs: list[Document] = file_store.get_files()\n", "    for doc in docs:\n", "        print(doc)\n", "\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--file_store_path\",\n", "        type=Path,\n", "        default=Path(\"/tmp\"),\n", "        help=\"Where to persistently store blobs/files uploaded by the extension\",\n", "    )\n", "    args = parser.parse_args()\n", "\n", "    file_store = FileStore(args.file_store_path)\n", "    print_files(file_store)\n"]}], "source": ["print(utils_for_str.extract_the_last_markdown_block(data_dict[\"full_response\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = utils_for_str.postprocess_model_response_for_edit(\n", "    generated_text=data_dict[\"full_response\"],\n", "    start_symbols=\"@@@@@@@@@\",\n", "    end_symbols=\"&&&&&&&&&\",\n", "    prefix=data_dict[\"prefix\"],\n", "    suffix=data_dict[\"suffix\"],\n", ")\n", "print(x)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}