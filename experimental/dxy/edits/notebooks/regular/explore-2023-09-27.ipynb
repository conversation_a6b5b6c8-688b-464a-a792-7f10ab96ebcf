{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["OPENAI_KEY = \"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import openai\n", "from openai import openai_object\n", "from typing import cast\n", "from termcolor import colored\n", "import research.core.utils_for_str as str_lib\n", "\n", "openai.api_key = OPENAI_KEY\n", "\n", "\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.1,\n", "    max_tokens: int = 64,\n", ") -> str:\n", "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = \"user\" if idx == 0 else \"assistant\"\n", "        request_messages.append({\"role\": role, \"content\": message})\n", "    # model=\"gpt-3.5-turbo\"\n", "    response = openai.ChatCompletion.create(\n", "        model=\"gpt-4\",\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "    )\n", "    response = cast(openai_object.OpenAIObject, response)\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["prefix = r\"\"\"\n", "    def log_likelihood_continuation(\n", "        self, model_input: ModelInput, continuation: str\n", "    ) -> Optional[float]:\n", "        raise NotImplementedError()\n", "\n", "    def _retrieve(\n", "        self, model_input: ModelInput, model: GenerativeLanguageModel\n", "    ) -> list[Chunk]:\n", "        if self.retriever:\n", "\"\"\"\n", "\n", "suffix = r\"\"\"\n", "            del scores\n", "            model_input, _ = filter_localctx_and_ground_truth_from_retrievals(\n", "                model_input, model.prompt_formatter\n", "            )\n", "            if self.max_retrieved_chunks is not None and self.max_retrieved_chunks >= 0:\n", "                # TODO(guy) verify we're dropping the correct chunks here\n", "                retrieved_chunks = retrieved_chunks[: self.max_retrieved_chunks]\n", "            return retrieved_chunks\n", "        return []\"\"\"\n", "\n", "selected_code = (\n", "    r\"\"\"            retrieved_chunks, scores = self.retriever.query(model_input)\"\"\"\n", ")\n", "\n", "instruction = \"grab the doc_ids from model input's extra and feed it in the query func\"\n", "\n", "modified_code = r\"\"\"            doc_ids = model_input.extra.get(\"doc_ids\", None)\n", "            model_input.retrieved_chunks, scores = self.retriever.query(\n", "                model_input, doc_ids=doc_ids\n", "            )\"\"\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32m\"Please modify the code to retrieve document IDs from the model input's extra attribute. Then, pass these document IDs as an argument to the retriever's query method. Assign the returned chunks to the retrieved_chunks attribute of the model input.\"\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[31mgrab the doc_ids from model input's extra and feed it in the query func\u001b[0m\n"]}], "source": ["# Method #1\n", "\n", "system_prompt = r\"\"\"You are helpful and super awesome Python expert that can solve any Python-related problem reasonable and correctly.\n", "You always think step by step and try to give helpful and useful solutions for the user.\"\"\"\n", "\n", "prompt = f\"\"\"Here is the prefix of a piece of code in a file:\n", "```\n", "{prefix}\n", "```\n", "Here is the suffix of that piece of code in the same file:\n", "```\n", "{suffix}\n", "```\n", "That piece of code in the file is:\n", "```\n", "{selected_code}\n", "```\n", "An user instructs an AI assistant to change the above code to:\n", "```\n", "{modified_code}\n", "```\n", "Please try your best to guess what is the brief instruction that the user instructs the AI assistant.\n", "When you reply me, please quto the instruction with triple backticks.\n", "\"\"\"\n", "\n", "result = generate_response_via_chat([prompt], system_prompt, max_tokens=256)\n", "final_result = str_lib.extract_code_within_backticks(result)\n", "if len(final_result) == 0:\n", "    print(\"Did not find any code block.\")\n", "    final_result = result\n", "elif le<PERSON>(final_result) == 1:\n", "    final_result = final_result[0]\n", "else:\n", "    print(f\"Find multiple code blocks.\")\n", "    final_result = result\n", "\n", "print(colored(final_result, color=\"green\"))\n", "print(\"-\" * 100)\n", "print(colored(instruction, color=\"red\"))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mdoc_ids = model_input.extra.get('doc_ids', None)\n", "retrieved_chunks, scores = self.retriever.query(model_input, doc_ids)\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[31m            doc_ids = model_input.extra.get(\"doc_ids\", None)\n", "            model_input.retrieved_chunks, scores = self.retriever.query(\n", "                model_input, doc_ids=doc_ids\n", "            )\u001b[0m\n"]}], "source": ["# Method #2\n", "\n", "system_prompt = r\"\"\"You are helpful and super awesome Python expert that can solve any Python-related problem reasonable and correctly.\n", "You always think step by step and try to give helpful and useful solutions for the user.\"\"\"\n", "\n", "prompt = f\"\"\"Here is the prefix of a piece of code in a file:\n", "```\n", "{prefix}\n", "```\n", "Here is the suffix of that piece of code in the same file:\n", "```\n", "{suffix}\n", "```\n", "That piece of code in the file is:\n", "```\n", "{selected_code}\n", "```\n", "I want to do this change `{instruction}` to this piece of code.\n", "Please only write down the modified code without including the prefix and suffix, because they are just to help you understand more context about the original code.\n", "When you write down the codes, please be extremely careful about the leading indentation and make sure they are correct and competiable with prefix and suffix.\n", "\"\"\"\n", "\n", "result = generate_response_via_chat([prompt], system_prompt, max_tokens=256)\n", "final_result = str_lib.extract_code_within_backticks(result)\n", "if len(final_result) == 0:\n", "    print(\"Did not find any code block.\")\n", "    final_result = result\n", "elif le<PERSON>(final_result) == 1:\n", "    final_result = final_result[0]\n", "else:\n", "    print(f\"Find multiple code blocks.\")\n", "    final_result = result\n", "\n", "print(colored(final_result, color=\"green\"))\n", "print(\"-\" * 100)\n", "print(colored(modified_code, color=\"red\"))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[32mdoc_ids = model_input.extra.get(\"doc_ids\", None)\n", "model_input.retrieved_chunks, scores = self.retriever.query(\n", "    model_input, doc_ids=doc_ids\n", ")\n", "\n", "del scores\n", "model_input, _ = filter_localctx_and_ground_truth_from_retrievals(\n", "    model_input, model.prompt_formatter\n", ")\n", "if self.max_retrieved_chunks is not None and self.max_retrieved_chunks >= 0:\n", "    # TODO(guy) verify we're dropping the correct chunks here\n", "    retrieved_chunks = retrieved_chunks[: self.max_retrieved_chunks]\n", "return retrieved_chunks\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[31m            retrieved_chunks, scores = self.retriever.query(model_input)\u001b[0m\n"]}], "source": ["# Method #3\n", "\n", "system_prompt = r\"\"\"You are helpful and super awesome Python expert that can solve any Python-related problem reasonable and correctly.\n", "You always think step by step and try to give helpful and useful solutions for the user.\"\"\"\n", "\n", "prompt = f\"\"\"Here is the prefix of a piece of code in a file:\n", "```\n", "{prefix}\n", "```\n", "Here is the suffix of that piece of code in the same file:\n", "```\n", "{suffix}\n", "```\n", "An user instructs an AI assistant by `{instruction}` and the AI assistant gives the following response:\n", "```\n", "{modified_code}\n", "```\n", "Please try your best to recover the original code snippet without including the prefix and suffix, because they are just to help you understand more context about the original code.\"\"\"\n", "\n", "result = generate_response_via_chat([prompt], system_prompt, max_tokens=256)\n", "final_result = str_lib.extract_code_within_backticks(result)\n", "if len(final_result) == 0:\n", "    print(\"Did not find any code block.\")\n", "    final_result = result\n", "elif le<PERSON>(final_result) == 1:\n", "    final_result = final_result[0]\n", "else:\n", "    print(f\"Find multiple code blocks.\")\n", "    final_result = result\n", "\n", "print(colored(final_result, color=\"green\"))\n", "print(\"-\" * 100)\n", "print(colored(selected_code, color=\"red\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}