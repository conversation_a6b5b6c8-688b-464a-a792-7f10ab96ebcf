{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.systems.remote_edit_system import RemoteEditSystem, InferenceMode\n", "\n", "system = RemoteEditSystem(\n", "    url=\"https://dev-dxy.us-central.api.augmentcode.com\",\n", "    # url=\"https://dogfood.api.augmentcode.com\",\n", "    inference_mode=InferenceMode.api_proxy.value,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.types import Document\n", "\n", "system.add_docs(\n", "    [Document(id=\"test\", text=\"def foo():\\n    return 1\\n\", path=\"test.py\")]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.eval.harness.tasks.edit_eval_task import EditEvalTask\n", "\n", "task = EditEvalTask()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample = task._edit_input_to_model_input(task[0])\n", "output = system.generate(sample)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(system._current_blob_names)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}