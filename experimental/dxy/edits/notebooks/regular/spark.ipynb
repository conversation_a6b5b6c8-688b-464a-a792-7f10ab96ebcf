{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import dataclasses\n", "import json\n", "import pathlib\n", "import typing\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "import pyspark.sql.pandas.functions as FF\n", "import tqdm\n", "from pyspark.sql.types import IntegerType, StringType, StructField, StructType\n", "\n", "from experimental.dxy.edits import data_type\n", "from research.core import utils_for_file, utils_for_log\n", "from research.data.spark import k8s_session\n", "\n", "\n", "def create_spark(max_workers: int = 100):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"128\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "        },\n", "    )\n", "    return spark"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/10/23 05:36:21 WARN Utils: Your hostname, dxy-8-a40 resolves to a loopback address: *********; using ************** instead (on interface enp5s0)\n", "23/10/23 05:36:21 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}], "source": ["spark = create_spark()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["df_2join : 14556184\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["df_3join : 9949948\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[Stage 11:>                                                         (0 + 1) / 1]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["df_dedup : 1532472\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["df_path_2join = \"s3a://dxy-dev-bucket/edit/joined_hunks_x1st_2023-10-22\"\n", "df_path_3join = \"s3a://dxy-dev-bucket/edit/joined_hunks_final_2023-10-22\"\n", "df_path_dedup = \"s3a://dxy-dev-bucket/edit/joined_hunks_deduped_2023-10-22\"\n", "\n", "df_2join = spark.read.parquet(df_path_2join)\n", "df_3join = spark.read.parquet(df_path_3join)\n", "df_dedup = spark.read.parquet(df_path_dedup)\n", "print(f\"df_2join : {df_2join.count()}\")\n", "print(f\"df_3join : {df_3join.count()}\")\n", "print(f\"df_dedup : {df_dedup.count()}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["root\n", " |-- old_file_sha: string (nullable = true)\n", " |-- commit_sha: string (nullable = true)\n", " |-- lines: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- content: string (nullable = true)\n", " |    |    |-- origin: string (nullable = true)\n", " |-- new_file_sha: string (nullable = true)\n", " |-- new_lines: long (nullable = true)\n", " |-- new_start: long (nullable = true)\n", " |-- num_lines: long (nullable = true)\n", " |-- old_lines: long (nullable = true)\n", " |-- old_start: long (nullable = true)\n", " |-- part: string (nullable = true)\n", " |-- date: date (nullable = true)\n", " |-- total_length: integer (nullable = true)\n", " |-- repo: string (nullable = true)\n", " |-- old_path_name: string (nullable = true)\n", " |-- old_content: string (nullable = true)\n", "\n", "root\n", " |-- new_file_sha: string (nullable = true)\n", " |-- old_file_sha: string (nullable = true)\n", " |-- commit_sha: string (nullable = true)\n", " |-- lines: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- content: string (nullable = true)\n", " |    |    |-- origin: string (nullable = true)\n", " |-- new_lines: long (nullable = true)\n", " |-- new_start: long (nullable = true)\n", " |-- num_lines: long (nullable = true)\n", " |-- old_lines: long (nullable = true)\n", " |-- old_start: long (nullable = true)\n", " |-- part: string (nullable = true)\n", " |-- date: date (nullable = true)\n", " |-- total_length: integer (nullable = true)\n", " |-- repo: string (nullable = true)\n", " |-- old_path_name: string (nullable = true)\n", " |-- old_content: string (nullable = true)\n", " |-- new_path_name: string (nullable = true)\n", " |-- new_content: string (nullable = true)\n", "\n", "root\n", " |-- new_file_sha: string (nullable = true)\n", " |-- old_file_sha: string (nullable = true)\n", " |-- commit_sha: string (nullable = true)\n", " |-- lines: array (nullable = true)\n", " |    |-- element: struct (containsNull = true)\n", " |    |    |-- content: string (nullable = true)\n", " |    |    |-- origin: string (nullable = true)\n", " |-- new_lines: long (nullable = true)\n", " |-- new_start: long (nullable = true)\n", " |-- num_lines: long (nullable = true)\n", " |-- old_lines: long (nullable = true)\n", " |-- old_start: long (nullable = true)\n", " |-- part: string (nullable = true)\n", " |-- date: date (nullable = true)\n", " |-- total_length: integer (nullable = true)\n", " |-- repo: string (nullable = true)\n", " |-- old_path_name: string (nullable = true)\n", " |-- old_content: string (nullable = true)\n", " |-- new_path_name: string (nullable = true)\n", " |-- new_content: string (nullable = true)\n", "\n"]}], "source": ["df_2join.printSchema()\n", "df_3join.printSchema()\n", "df_dedup.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_s3_dir: str = \"s3a://dxy-dev-bucket/edit\"\n", "date: str = \"2023-10-22\"\n", "\n", "hunk_raw_data_dir = \"s3a://augment-github/hunk/filtered_python/\"\n", "print(f\"Read from {hunk_raw_data_dir}\")\n", "hunks_df = spark.read.parquet(hunk_raw_data_dir)\n", "\n", "files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")\n", "files_df.printSchema()\n", "\n", "old_files = files_df.filter(F.length(\"content\") < 1e6).select(\n", "    F.col(\"id\").alias(\"old_file_sha\"),\n", "    F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "    F.col(\"max_stars_repo_path\").alias(\"old_path_name\"),\n", "    F.col(\"content\").alias(\"old_content\"),\n", ")\n", "print(\"The schema for the old_files variable:\")\n", "old_files.printSchema()\n", "\n", "path_1st_join = f\"{root_s3_dir}/x1st_joined_hunks_{date}/\"\n", "df_1st_join = hunks_df.join(old_files, \"old_file_sha\")\n", "print(f\"Try to dump into {path_1st_join}.\")\n", "df_1st_join.write.mode(\"overwrite\").parquet(path_1st_join)\n", "df_1st_join = spark.read.parquet(path_1st_join)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"The df_1st_join DF has {df_1st_join.count()} examples\")\n", "\n", "new_files = files_df.select(\n", "    F.col(\"id\").alias(\"new_file_sha\"),\n", "    F.col(\"max_stars_repo_path\").alias(\"new_path_name\"),\n", "    F.col(\"content\").alias(\"new_content\"),\n", ").filter(F.length(\"new_content\") < 1e6)\n", "\n", "df_joined = df_1st_join.join(new_files, \"new_file_sha\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(f\"The hunk data has {hunks_df.count()/1e6:7.2f}M examples.\")\n", "\n", "deduped_hunks_df = hunks_df.filter(hunks_df.total_length < 3e5).dropDuplicates(\n", "    [\"old_file_sha\", \"old_start\", \"old_lines\"]\n", ")\n", "deduped_hunk_path = f\"{root_s3_dir}/raw_deduped2v_hunks_{date}/\"\n", "print(f\"Try to dump into {deduped_hunk_path}\")\n", "deduped_hunks_df.write.mode(\"overwrite\").parquet(deduped_hunk_path)\n", "print(\n", "    f\"The hunk data has {deduped_hunks_df.count()/1e6:7.2f}M examples after deduplication.\"\n", ")\n", "\n", "files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")\n", "files_df.printSchema()\n", "print(f\"The file data has {files_df.count()/1e6:7.2f}M examples.\")\n", "deduped_files_df = files_df.dropDuplicates([\"max_stars_repo_path\"])\n", "print(\n", "    f\"The file data has {deduped_files_df.count()/1e6:7.2f}M examples after deduplication.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["date = \"2023-10-10\"\n", "root_s3_dir = \"s3a://dxy-dev-bucket/edit\"\n", "spark = create_spark()\n", "\n", "# hunk_raw_data_dir = \"s3a://augment-github/hunk/converted/\"\n", "hunk_raw_data_dir = \"s3://augment-github/hunk/converted/date=2023-10-05/\"\n", "print(f\"Read from {hunk_raw_data_dir}\")\n", "hunks_df = (\n", "    spark.read.format(\"delta\")\n", "    .option(\"mode\", \"DROPMALFORMED\")\n", "    .load(hunk_raw_data_dir)\n", "    .repartition(1000, \"commit_sha\")\n", ")\n", "hunks_df.printSchema()\n", "print(f\"The hunk data has {hunks_df.count()/1e6:7.2f}M examples.\")\n", "\n", "deduped_hunks_df = hunks_df.dropDuplicates([\"old_file_sha\", \"old_start\", \"old_lines\"])\n", "deduped_hunk_path = f\"{root_s3_dir}/raw_deduped_hunks_{date}/\"\n", "print(f\"Try to dump into {deduped_hunk_path}\")\n", "deduped_hunks_df.write.mode(\"overwrite\").parquet(deduped_hunk_path)\n", "print(\n", "    f\"The hunk data has {deduped_hunks_df.count()/1e6:7.2f}M examples after deduplication.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hunks_df = spark.read.parquet(\"s3a://augment-github/hunk/converted/date=2023-10-05/\")\n", "deduped_hunks_df = hunks_df.dropDuplicates([\"old_file_sha\", \"old_start\", \"old_lines\"])\n", "deduped_hunk_path = f\"{root_s3_dir}/raw_deduped_hunks_date=2023-10-05/\"\n", "print(f\"Try to dump into {deduped_hunk_path}\")\n", "deduped_hunks_df.write.mode(\"overwrite\").parquet(deduped_hunk_path)\n", "print(\n", "    f\"The hunk data has {deduped_hunks_df.count()/1e6:7.2f}M examples after deduplication.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from delta import DeltaTable\n", "\n", "table = DeltaTable.forPath(spark, \"s3a://augment-github/hunk/converted/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table.delete(\"date='2023-10-05'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(\"s3a://dxy-dev-bucket/edit/deduped_joined_hunks_2023-10-13/\")\n", "df.printSchema()\n", "print(df.count())\n", "print(df.select(\"old_path_name\").distinct().count())\n", "print(df.select(\"new_path_name\").distinct().count())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    df.dropDuplicates(\n", "        [\"old_file_sha\", \"new_file_sha\", \"old_start\", \"old_lines\"]\n", "    ).count()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(\n", "    \"s3a://dxy-dev-bucket/edit/final-edit-data-cache-all-2023-10-13\"\n", ")\n", "df.printSchema()\n", "print(df.count())\n", "print(df.select(\"hash_of_lines\").distinct().count())\n", "# print(df.select('json_str').distinct().count())"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}