{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.spark import k8s_session\n", "import pyspark.sql.functions as F\n", "\n", "\n", "spark = k8s_session(\n", "    conf={\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"200\",\n", "        #    'spark.sql.parquet.enableVectorizedReader': 'false',\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hunks_df = spark.read.format(\"delta\").load(\"s3a://augment-github/hunk/converted/\")\n", "files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hunks_df.printSchema()\n", "\n", "files_df.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["old_files = files_df.select(\n", "    F.col(\"id\").alias(\"old_file_sha\"),\n", "    F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "    F.col(\"max_stars_repo_path\").alias(\"old_path_name\"),\n", "    F.col(\"content\").alias(\"old_content\"),\n", ")\n", "\n", "new_files = files_df.select(\n", "    F.col(\"id\").alias(\"new_file_sha\"),\n", "    F.col(\"max_stars_repo_path\").alias(\"new_path_name\"),\n", "    F.col(\"content\").alias(\"new_content\"),\n", ")\n", "\n", "hunks_df.join(old_files, \"old_file_sha\").join(new_files, \"new_file_sha\").write.mode(\n", "    \"overwrite\"\n", ").parquet(\"s3a://augment-temporary/enriched_hunks/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = spark.read.parquet(\"s3a://augment-temporary/enriched_hunks/\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.printSchema()\n", "\n", "df.count()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(df)\n", "# new_start: start line index in the new file\n", "# new_lines: number of lines in the new file\n", "# lines: an array of struct -- dict,"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["validation_df = df.limit(100)\n", "print(validation_df.count())\n", "\n", "for row in validation_df.toLocalIterator():\n", "    print(row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["spark.stop()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}