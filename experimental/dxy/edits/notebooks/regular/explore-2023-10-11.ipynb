{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1658/1658 [00:00<00:00, 4898.89it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1658 examples for del_00_00-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 683/683 [00:00<00:00, 1945.59it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are   683 examples for del_01_01-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1081/1081 [00:00<00:00, 5958.26it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1081 examples for del_02_02-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1243/1243 [00:00<00:00, 5509.92it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1243 examples for del_03_03-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1359/1359 [00:00<00:00, 5410.48it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1359 examples for del_04_04-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1511/1511 [00:00<00:00, 4761.69it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1511 examples for del_05_05-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1585/1585 [00:00<00:00, 4871.19it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1585 examples for del_06_06-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1675/1675 [00:00<00:00, 2445.49it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1675 examples for del_07_07-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1753/1753 [00:00<00:00, 4415.21it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1753 examples for del_08_08-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1877/1877 [00:00<00:00, 4299.79it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1877 examples for del_09_09-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 1975/1975 [00:00<00:00, 4067.93it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  1975 examples for del_10_10-per60\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 4109/4109 [00:01<00:00, 2699.54it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  4109 examples for del_11_20-per15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["load from jsonl: 100%|██████████| 4781/4781 [00:02<00:00, 2098.31it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are  4781 examples for del_21_30-per15\n", "There are 25290 examples in total.\n"]}], "source": ["import pathlib\n", "import tqdm\n", "from augment.research.core import utils_for_dataclass, utils_for_file\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\n", "\n", "\n", "suffixes = [\n", "    \"del_00_00-per60\",\n", "    \"del_01_01-per60\",\n", "    \"del_02_02-per60\",\n", "    \"del_03_03-per60\",\n", "    \"del_04_04-per60\",\n", "    \"del_05_05-per60\",\n", "    \"del_06_06-per60\",\n", "    \"del_07_07-per60\",\n", "    \"del_08_08-per60\",\n", "    \"del_09_09-per60\",\n", "    \"del_10_10-per60\",\n", "    \"del_11_20-per15\",\n", "    \"del_21_30-per15\",\n", "]\n", "\n", "manager_by_suffix, total = {}, 0\n", "for suffix in suffixes:\n", "    cur_path = pathlib.Path(\n", "        f\"/home/<USER>/datasets/edit/organized-basic/manager-{suffix}.jsonl.zst\"\n", "    )\n", "    manager = EditDataManager.read_from_jsonl(str(cur_path))\n", "    manager_by_suffix[suffix] = manager\n", "    print(f\"There are {len(manager):5d} examples for {suffix}\")\n", "    total += len(manager)\n", "print(f\"There are {total} examples in total.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unique_paths = set()\n", "for x in manager.edit_data_list:\n", "    unique_paths.add(x.file_path)\n", "print(f\"There are {len(unique_paths)} unique paths in the manager.\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 15 EditData elements.\n"]}], "source": ["edit_data_list = manager.get_data_list(21, 22)\n", "print(f\"There are {len(edit_data_list)} EditData elements.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ ]     # the test itself\n", "[ ]     #########################\n", "[ ] \n", "\u001b[31m[-]     print\n", "\u001b[0m\u001b[31m[-]     print \"=\" * 79\n", "\u001b[0m\u001b[31m[-]     print \"2D case\"\n", "\u001b[0m\u001b[31m[-]     print \"=\" * 79\n", "\u001b[0m\u001b[31m[-]     print\n", "\u001b[0m\u001b[32m[+]     print()\n", "\u001b[0m\u001b[32m[+]     print( \"=\" * 79 )\n", "\u001b[0m\u001b[32m[+]     print( \"2D case\" )\n", "\u001b[0m\u001b[32m[+]     print( \"=\" * 79 )\n", "\u001b[0m\u001b[32m[+]     print()\n", "\u001b[0m[ ] \n", "\u001b[31m[-]     print \"expr: %s, xi = %s\" % (expr, xi)\n", "\u001b[0m\u001b[32m[+]     print( \"expr: %s, xi = %s\" % (expr, xi) )\n", "\u001b[0m[ ] \n", "[ ]     labels = [\"F\", \"DX\", \"DY\", \"DX2\", \"DXDY\", \"DY2\", \"DX3\", \"DX2DY\", \"DXDY2\", \"DY3\", \"DX4\", \"DX3DY\", \"DX2DY2\", \"DXDY3\", \"DY4\"]\n", "\u001b[31m[-]     print \"legend: %s\" % (\"\\t\".join(labels))\n", "\u001b[0m\u001b[32m[+]     print( \"legend: %s\" % (\"\\t\".join(labels)) )\n", "\u001b[0m[ ]     knowns_str = \"\"\n", "[ ]     for j in range(wlsqm.SIZE2):  # SIZE2 = maximum size of c matrix for 2D case\n", "[ ]         if j > 0:\n", "[ ]             knowns_str += '\\t'\n", "[ ]         if knowns & (1 << j):\n", "[ ]             knowns_str += labels[j]\n", "\u001b[31m[-]     print \"knowns: %s\" % knowns_str\n", "\u001b[0m\u001b[32m[+]     print( \"knowns: %s\" % knowns_str )\n", "\u001b[0m[ ] #    # http://stackoverflow.com/questions/699866/python-int-to-binary\n", "\u001b[31m[-] #    print \"knowns (mask): %s\" % format(knowns, '010b')[::-1]\n", "\u001b[0m\u001b[32m[+] #    print (\"knowns (mask): %s\" % format(knowns, '010b')[::-1] )\n", "\u001b[0m[ ] \n", "\u001b[31m[-]     print \"surrogate order: %d\" % fit_order\n", "\u001b[0m\u001b[32m[+]     print( \"surrogate order: %d\" % fit_order )\n", "\u001b[0m[ ] \n", "[ ]     if noise_eps > 0.:\n", "\u001b[31m[-]         print \"simulating noisy input with eps = %g\" % noise_eps\n", "\u001b[0m\u001b[32m[+]         print( \"simulating noisy input with eps = %g\" % noise_eps )\n", "\u001b[0m[ ] \n", "[ ]     # SymPy expr --> lambda(x,y)\n", "[ ]     lambdify_numpy_2d = lambda expr: sy.lambdify((\"x\",\"y\"), expr, modules=\"numpy\")\n"]}], "source": ["data = manager.edit_data_list[0]\n", "data.show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_diff_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_selected_code(), end=\"\")\n", "print(\"-\" * 100)\n", "print(data.get_modified_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "\n", "lrange = data.get_lrange()\n", "print(\n", "    termcolor.colored(data.get_prefix_via_lrange(lrange=lrange), color=\"green\"), end=\"\"\n", ")\n", "print(\n", "    termcolor.colored(data.get_content_via_lrange(lrange=lrange), color=\"red\"), end=\"\"\n", ")\n", "print(termcolor.colored(data.get_suffix_via_lrange(lrange=lrange), color=\"blue\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.new_file_content)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["predict_edit_data = predict_edit_data_v0"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------------------------------------------------------------------\n", "There are 11 messages.\n", "[the 00-th message] Let's imagine a scenario: there is an AI assistant helping users to write the Python code.\n", "The user will give the assistant some Python codes and asks the assistant to follow his instruction to modify a piece of code.\n", "[the 01-th message] Ok, I understand this scenario, what do you want me to do?\n", "[the 02-th message] Now, I will give you the entire codes, the target piece of codes to be modified, and the updated codes made by the AI assistant.\n", "Can you guess what is the user's original instruction?\n", "[the 03-th message] Sure, I will guess, what is the entire codes, and the target piece of codes?\n", "[the 04-th message] Here is the preceding part of the target piece of codes:\n", "```\n", "    #\n", "    # Here \"unknown\" means any element of fi[] not tagged as known in the \"knowns\" bitmask.\n", "    #\n", "    nk = 24  # used if grid_type == 'random'\n", "\n", "    r  = 1e-1  # neighborhood radius\n", "\n", "#    grid_type = 'random'\n", "#    grid_type = 'stencil'\n", "    grid_type = 'sudoku'\n", "\n", "    #########################\n", "    # the test itself\n", "    #########################\n", "\n", "```\n", "[the 05-th message] Got it, what is the target piece of codes and its possible succeeding part?\n", "[the 06-th message] Here is the target piece of codes:\n", "```\n", "    print\n", "    print \"=\" * 79\n", "    print \"2D case\"\n", "    print \"=\" * 79\n", "    print\n", "\n", "    print \"expr: %s, xi = %s\" % (expr, xi)\n", "\n", "    labels = [\"F\", \"DX\", \"DY\", \"DX2\", \"DXDY\", \"DY2\", \"DX3\", \"DX2DY\", \"DXDY2\", \"DY3\", \"DX4\", \"DX3DY\", \"DX2DY2\", \"DXDY3\", \"DY4\"]\n", "    print \"legend: %s\" % (\"\\t\".join(labels))\n", "    knowns_str = \"\"\n", "    for j in range(wlsqm.SIZE2):  # SIZE2 = maximum size of c matrix for 2D case\n", "        if j > 0:\n", "            knowns_str += '\\t'\n", "        if knowns & (1 << j):\n", "            knowns_str += labels[j]\n", "    print \"knowns: %s\" % knowns_str\n", "#    # http://stackoverflow.com/questions/699866/python-int-to-binary\n", "#    print \"knowns (mask): %s\" % format(knowns, '010b')[::-1]\n", "\n", "    print \"surrogate order: %d\" % fit_order\n", "\n", "    if noise_eps > 0.:\n", "        print \"simulating noisy input with eps = %g\" % noise_eps\n", "\n", "```\n", "[the 07-th message] Got it, what is its succeeding part and the final updated codes?\n", "[the 08-th message] Here is the succeeding part of the target piece of codes:\n", "```\n", "\n", "    # SymPy expr --> lambda(x,y)\n", "    lambdify_numpy_2d = lambda expr: sy.lambdify((\"x\",\"y\"), expr, modules=\"numpy\")\n", "    f         = lambdify_numpy_2d(expr)\n", "    dfdx      = lambdify_numpy_2d(sy.diff(expr, \"x\"))\n", "    dfdy      = lambdify_numpy_2d(sy.diff(expr, \"y\"))\n", "    d2fdx2    = lambdify_numpy_2d(sy.diff(expr, \"x\", 2))\n", "    d2fdxdy   = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\"), \"y\" ))\n", "    d2fdy2    = lambdify_numpy_2d(sy.diff(expr, \"y\", 2))\n", "    d3fdx3    = lambdify_numpy_2d(sy.diff(expr, \"x\", 3))\n", "    d3fdx2dy  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\", 2), \"y\" ))\n", "    d3fdxdy2  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\"), \"y\", 2 ))\n", "    d3fdy3    = lambdify_numpy_2d(sy.diff(expr, \"y\", 3))\n", "    d4fdx4    = lambdify_numpy_2d(sy.diff(expr, \"x\", 4))\n", "    d4fdx3dy  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\", 3), \"y\" ))\n", "\n", "```\n", "[the 09-th message] Got<PERSON>, what is the updated version?\n", "[the 10-th message] Here is the updated version\n", "```\n", "    print()\n", "    print( \"=\" * 79 )\n", "    print( \"2D case\" )\n", "    print( \"=\" * 79 )\n", "    print()\n", "\n", "    print( \"expr: %s, xi = %s\" % (expr, xi) )\n", "\n", "    labels = [\"F\", \"DX\", \"DY\", \"DX2\", \"DXDY\", \"DY2\", \"DX3\", \"DX2DY\", \"DXDY2\", \"DY3\", \"DX4\", \"DX3DY\", \"DX2DY2\", \"DXDY3\", \"DY4\"]\n", "    print( \"legend: %s\" % (\"\\t\".join(labels)) )\n", "    knowns_str = \"\"\n", "    for j in range(wlsqm.SIZE2):  # SIZE2 = maximum size of c matrix for 2D case\n", "        if j > 0:\n", "            knowns_str += '\\t'\n", "        if knowns & (1 << j):\n", "            knowns_str += labels[j]\n", "    print( \"knowns: %s\" % knowns_str )\n", "#    # http://stackoverflow.com/questions/699866/python-int-to-binary\n", "#    print (\"knowns (mask): %s\" % format(knowns, '010b')[::-1] )\n", "\n", "    print( \"surrogate order: %d\" % fit_order )\n", "\n", "    if noise_eps > 0.:\n", "        print( \"simulating noisy input with eps = %g\" % noise_eps )\n", "```\n", "\n", "Oh, by the way, the user's instruction is usually in a casual and natural tone, and here are some examples for your reference:\n", "- put a nice docstring to well explain this class\n", "- use promise chain instead of async\n", "- extend it to else condition\n", "- fix errors\n", "- move the accesstoken to url as a parameter of token=\n", "- make this line more readable\n", "- generate floats, not only ints\n", "- add a param to choose upper/lower\n", "- change all command names to snake case\n", "- replace <PERSON> by <PERSON><PERSON><PERSON>\n", "- this function seems will never trigger the default branch of executeChat, fix it\n", "- refactor this function by using optional_map func\n", "- simplify the cache control\n", "- console log all the ids of each file\n", "- Refactor the code. Change config.foo to CONFIG['foo']\n", "- change Optional[str] to str | None\n", "- write test train split using df pandas\n", "- instead of hard code options in the quick pick array, randomly generate 10 numbers as options\n", "- write a short pytorch module that implements linear regression, have comments\n", "- polish it with concise codes\n", "\n", "When you guess his instruction, please follow these requirements:\n", "- (1) try your best to simulate his tone;\n", "- (2) produce the instruction vague but not too vague naturely like a human, for example,\n", "-- (2.1) \"Add the import for urlquote from django.utils.http\" can be simplified as \"add the import of urlquote\";\n", "- (3) produce the instruction short and brief;\n", "- (4) in your output, please quote the instruction like ````instruction````.\n", "\n", "------------------------------ Selected Code ------------------------------\n", "    #\n", "    # Here \"unknown\" means any element of fi[] not tagged as known in the \"knowns\" bitmask.\n", "    #\n", "    nk = 24  # used if grid_type == 'random'\n", "\n", "    r  = 1e-1  # neighborhood radius\n", "\n", "#    grid_type = 'random'\n", "#    grid_type = 'stencil'\n", "    grid_type = 'sudoku'\n", "\n", "    #########################\n", "    # the test itself\n", "    #########################\n", "\n", "\u001b[34m    print\n", "    print \"=\" * 79\n", "    print \"2D case\"\n", "    print \"=\" * 79\n", "    print\n", "\n", "    print \"expr: %s, xi = %s\" % (expr, xi)\n", "\n", "    labels = [\"F\", \"DX\", \"DY\", \"DX2\", \"DXDY\", \"DY2\", \"DX3\", \"DX2DY\", \"DXDY2\", \"DY3\", \"DX4\", \"DX3DY\", \"DX2DY2\", \"DXDY3\", \"DY4\"]\n", "    print \"legend: %s\" % (\"\\t\".join(labels))\n", "    knowns_str = \"\"\n", "    for j in range(wlsqm.SIZE2):  # SIZE2 = maximum size of c matrix for 2D case\n", "        if j > 0:\n", "            knowns_str += '\\t'\n", "        if knowns & (1 << j):\n", "            knowns_str += labels[j]\n", "    print \"knowns: %s\" % knowns_str\n", "#    # http://stackoverflow.com/questions/699866/python-int-to-binary\n", "#    print \"knowns (mask): %s\" % format(knowns, '010b')[::-1]\n", "\n", "    print \"surrogate order: %d\" % fit_order\n", "\n", "    if noise_eps > 0.:\n", "        print \"simulating noisy input with eps = %g\" % noise_eps\n", "\u001b[0m\n", "    # SymPy expr --> lambda(x,y)\n", "    lambdify_numpy_2d = lambda expr: sy.lambdify((\"x\",\"y\"), expr, modules=\"numpy\")\n", "    f         = lambdify_numpy_2d(expr)\n", "    dfdx      = lambdify_numpy_2d(sy.diff(expr, \"x\"))\n", "    dfdy      = lambdify_numpy_2d(sy.diff(expr, \"y\"))\n", "    d2fdx2    = lambdify_numpy_2d(sy.diff(expr, \"x\", 2))\n", "    d2fdxdy   = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\"), \"y\" ))\n", "    d2fdy2    = lambdify_numpy_2d(sy.diff(expr, \"y\", 2))\n", "    d3fdx3    = lambdify_numpy_2d(sy.diff(expr, \"x\", 3))\n", "    d3fdx2dy  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\", 2), \"y\" ))\n", "    d3fdxdy2  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\"), \"y\", 2 ))\n", "    d3fdy3    = lambdify_numpy_2d(sy.diff(expr, \"y\", 3))\n", "    d4fdx4    = lambdify_numpy_2d(sy.diff(expr, \"x\", 4))\n", "    d4fdx3dy  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\", 3), \"y\" ))\n", "----------------------------------------------------------------------------------------------------\n", "------------------------------ Modified Code ------------------------------\n", "    #\n", "    # Here \"unknown\" means any element of fi[] not tagged as known in the \"knowns\" bitmask.\n", "    #\n", "    nk = 24  # used if grid_type == 'random'\n", "\n", "    r  = 1e-1  # neighborhood radius\n", "\n", "#    grid_type = 'random'\n", "#    grid_type = 'stencil'\n", "    grid_type = 'sudoku'\n", "\n", "    #########################\n", "    # the test itself\n", "    #########################\n", "\n", "\u001b[31m    print()\n", "    print( \"=\" * 79 )\n", "    print( \"2D case\" )\n", "    print( \"=\" * 79 )\n", "    print()\n", "\n", "    print( \"expr: %s, xi = %s\" % (expr, xi) )\n", "\n", "    labels = [\"F\", \"DX\", \"DY\", \"DX2\", \"DXDY\", \"DY2\", \"DX3\", \"DX2DY\", \"DXDY2\", \"DY3\", \"DX4\", \"DX3DY\", \"DX2DY2\", \"DXDY3\", \"DY4\"]\n", "    print( \"legend: %s\" % (\"\\t\".join(labels)) )\n", "    knowns_str = \"\"\n", "    for j in range(wlsqm.SIZE2):  # SIZE2 = maximum size of c matrix for 2D case\n", "        if j > 0:\n", "            knowns_str += '\\t'\n", "        if knowns & (1 << j):\n", "            knowns_str += labels[j]\n", "    print( \"knowns: %s\" % knowns_str )\n", "#    # http://stackoverflow.com/questions/699866/python-int-to-binary\n", "#    print (\"knowns (mask): %s\" % format(knowns, '010b')[::-1] )\n", "\n", "    print( \"surrogate order: %d\" % fit_order )\n", "\n", "    if noise_eps > 0.:\n", "        print( \"simulating noisy input with eps = %g\" % noise_eps )\n", "\u001b[0m\n", "    # SymPy expr --> lambda(x,y)\n", "    lambdify_numpy_2d = lambda expr: sy.lambdify((\"x\",\"y\"), expr, modules=\"numpy\")\n", "    f         = lambdify_numpy_2d(expr)\n", "    dfdx      = lambdify_numpy_2d(sy.diff(expr, \"x\"))\n", "    dfdy      = lambdify_numpy_2d(sy.diff(expr, \"y\"))\n", "    d2fdx2    = lambdify_numpy_2d(sy.diff(expr, \"x\", 2))\n", "    d2fdxdy   = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\"), \"y\" ))\n", "    d2fdy2    = lambdify_numpy_2d(sy.diff(expr, \"y\", 2))\n", "    d3fdx3    = lambdify_numpy_2d(sy.diff(expr, \"x\", 3))\n", "    d3fdx2dy  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\", 2), \"y\" ))\n", "    d3fdxdy2  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\"), \"y\", 2 ))\n", "    d3fdy3    = lambdify_numpy_2d(sy.diff(expr, \"y\", 3))\n", "    d4fdx4    = lambdify_numpy_2d(sy.diff(expr, \"x\", 4))\n", "    d4fdx3dy  = lambdify_numpy_2d(sy.diff( sy.diff(expr, \"x\", 3), \"y\" ))\n", "----------------------------------------------------------------------------------------------------\n", "\u001b[34mBased on the changes made in the updated version of the code, it seems like the user's instruction was to update the print statements to be compatible with Python 3. \n", "\n", "So, the user's instruction could be: ````\"Update the print statements for Python 3 compatibility\"````.\u001b[0m\n", "----------------------------------------------------------------------------------------------------\n"]}], "source": ["_ = predict_edit_data(manager.edit_data_list[0], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], model=\"gpt-3.5-turbo\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[1], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[2], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[3], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[4], debug=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}