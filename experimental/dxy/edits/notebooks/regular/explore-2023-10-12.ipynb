{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "import json\n", "from augment.research.core import utils_for_dataclass, utils_for_file\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\n", "\n", "\n", "json_file = (\n", "    \"/home/<USER>/datasets/edit/organized-basic-instruct/instruct-del_01_01-per60.json\"\n", ")\n", "\n", "with pathlib.Path(json_file).open(\"r\") as f:\n", "    for line in f.readlines():\n", "        xdict = json.loads(line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "from augment.research.core import utils_for_dataclass, utils_for_file\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\n", "\n", "\n", "suffixes = [\n", "    \"del_00_00-per60\",\n", "    \"del_01_01-per60\",\n", "    \"del_02_02-per60\",\n", "    \"del_03_03-per60\",\n", "    \"del_04_04-per60\",\n", "    \"del_05_05-per60\",\n", "    \"del_06_06-per60\",\n", "    \"del_07_07-per60\",\n", "    \"del_08_08-per60\",\n", "    \"del_09_09-per60\",\n", "    \"del_10_10-per60\",\n", "    \"del_11_20-per15\",\n", "    \"del_21_30-per15\",\n", "]\n", "\n", "manager_by_suffix, total = {}, 0\n", "for suffix in suffixes:\n", "    cur_path = pathlib.Path(\n", "        f\"/home/<USER>/datasets/edit/organized-basic/manager-{suffix}.jsonl.zst\"\n", "    )\n", "    manager = EditDataManager.read_from_jsonl(str(cur_path))\n", "    manager_by_suffix[suffix] = manager\n", "    print(f\"There are {len(manager):5d} examples for {suffix}\")\n", "    total += len(manager)\n", "print(f\"There are {total} examples in total.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unique_paths = set()\n", "for x in manager.edit_data_list:\n", "    unique_paths.add(x.file_path)\n", "print(f\"There are {len(unique_paths)} unique paths in the manager.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["edit_data_list = manager.get_data_list(21, 22)\n", "print(f\"There are {len(edit_data_list)} EditData elements.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = manager.edit_data_list[0]\n", "data.show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_diff_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_selected_code(), end=\"\")\n", "print(\"-\" * 100)\n", "print(data.get_modified_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "\n", "lrange = data.get_lrange()\n", "print(\n", "    termcolor.colored(data.get_prefix_via_lrange(lrange=lrange), color=\"green\"), end=\"\"\n", ")\n", "print(\n", "    termcolor.colored(data.get_content_via_lrange(lrange=lrange), color=\"red\"), end=\"\"\n", ")\n", "print(termcolor.colored(data.get_suffix_via_lrange(lrange=lrange), color=\"blue\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.new_file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data = predict_edit_data_v0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], model=\"gpt-3.5-turbo\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[1], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[2], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[3], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[4], debug=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}