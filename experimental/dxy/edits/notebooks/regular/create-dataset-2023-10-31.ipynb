{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create the Training Dataset"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "import json\n", "import re\n", "import random\n", "import torch\n", "import numpy as np\n", "import collections\n", "\n", "import typing\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from experimental.dxy.edits.reverse_get_instruction import (\n", "    predict_edit_data_v0,\n", "    predict_edit_data_v1,\n", ")\n", "from experimental.dxy.edits.prompter import EditPromptTemplate\n", "from experimental.dxy.edits.util_lib import pack_sequences, pad_sequences\n", "from megatron.tokenizer.tokenizer import AbstractTokenizer\n", "\n", "\n", "def detect_merge_conflict(input_str) -> bool:\n", "    pattern1 = re.compile(r\"<<<<<<< (HEAD|[\\da-fA-F]{10+})\\n\")\n", "    pattern2 = re.compile(r\">>>>>>> (HEAD|[\\da-fA-F]{10+})\\n\")\n", "\n", "    match1 = pattern1.search(input_str)\n", "    if match1:\n", "        return True\n", "    match2 = pattern2.search(input_str)\n", "    if match2:\n", "        return True\n", "    return False\n", "\n", "\n", "def keep_by_diff_range(\n", "    all_edit_data: list[EditData],\n", "    max_per_diff: dict[tuple[int, int], int],\n", "):\n", "    \"\"\"Keep the edit data by the diff range.\"\"\"\n", "    kept_edit_data: list[EditData] = []\n", "    num_per_diff: dict[tuple[int, int], int] = collections.defaultdict(int)\n", "    for x in all_edit_data:\n", "        diff = x.get_diffs()\n", "        if diff in max_per_diff and num_per_diff[diff] < max_per_diff[diff]:\n", "            kept_edit_data.append(x)\n", "            num_per_diff[diff] += 1\n", "    print(f\"There are {len(kept_edit_data)} / {len(all_edit_data)} examples in total.\")\n", "    return kept_edit_data\n", "\n", "\n", "def build_to_fine_tun_data(\n", "    edit_data_w_instruction: list[EditData], tokenizer: AbstractTokenizer\n", "):\n", "    raw_examples: list[dict] = []\n", "    examples: list[list[int]] = []\n", "    skipped_due_to_zero_token = 0\n", "\n", "    for edit in tqdm.tqdm(edit_data_w_instruction, total=len(edit_data_w_instruction)):\n", "        question, answer = EditPromptTemplate.create_prompt_v2(\n", "            edit, tokenizer, num_of_context_lines=60\n", "        )\n", "        tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "        if 0 in tensor:\n", "            skipped_due_to_zero_token += 1\n", "            continue\n", "        examples.append(tensor)\n", "        raw_examples.append({\"question\": question, \"answer\": answer})\n", "    print(f\"Total number of examples = {len(examples)}\")\n", "    print(\n", "        f\"Average number of tokens per example = {np.mean([len(x) for x in examples])} += {np.std([len(x) for x in examples])}\"\n", "    )\n", "    print(\n", "        f\"Minimum number of tokens per example = {np.min([len(x) for x in examples])}\"\n", "    )\n", "    print(\n", "        f\"Maximum number of tokens per example = {np.max([len(x) for x in examples])}\"\n", "    )\n", "    return raw_examples\n", "\n", "\n", "def build_whole_dataset(\n", "    raw_examples: list[dict],\n", "    root_dir: str,\n", "    base_name: str,\n", "    repeats: int = 1,\n", "    seq_len: int = 4096,\n", "    mask_input: bool = False,\n", "    use_pack_or_pad: bool = True,\n", "):\n", "    random.shuffle(raw_examples)\n", "    # Split the raw training / validation dataset\n", "    print(f\"There are {len(raw_examples)} examples in total.\")\n", "\n", "    repeated_examples = []\n", "    for _ in range(repeats):\n", "        for ex in raw_examples:\n", "            question, answer = ex[\"question\"], ex[\"answer\"]\n", "            if mask_input:\n", "                question = [-x for x in question]\n", "            tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "            repeated_examples.append(tensor)\n", "    print(f\"#repeated_examples = {len(repeated_examples)} with repeats={repeats}\")\n", "    random.shuffle(repeated_examples)\n", "\n", "    if use_pack_or_pad:\n", "        sequences = pack_sequences(repeated_examples, seq_len, -tokenizer.eos_id)\n", "    else:\n", "        sequences = pad_sequences(repeated_examples, seq_len, -tokenizer.eos_id)\n", "    random.shuffle(sequences)\n", "\n", "    print(f\"There are {len(sequences)} sequences.\")\n", "\n", "    mask_suffix = \"onlytgt\" if mask_input else \"full\"\n", "    pad_pack_suffix = \"pack\" if use_pack_or_pad else \"pad\"\n", "    output_path = (\n", "        pathlib.Path(root_dir)\n", "        / f\"{base_name}_r{repeats}n_s{seq_len}_{mask_suffix}_{pad_pack_suffix}\"\n", "    )\n", "\n", "    output_dir = output_path.parent\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    # Build the train dataset.\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sequence in sequences:\n", "        # Make the total sequence length to be 4096 + 1\n", "        builder.add_item(torch.Tensor(sequence + [-tokenizer.eos_id]))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Built the {base_name} dataset at {output_path}.\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 25767 edit data with instructions.\n", "skip_due_to_no_blocks=100\n", "skip_due_to_multiple_blocks=287\n", "skip_due_to_merge_conflict=46\n"]}], "source": ["# Load our in-house dataset\n", "file_names = (\n", "    \"instruct-del_00_00-per40.json\",\n", "    \"instruct-del_01_01-per40.json\",\n", "    \"instruct-del_02_02-per40.json\",\n", "    \"instruct-del_03_03-per40.json\",\n", "    \"instruct-del_04_04-per40.json\",\n", "    \"instruct-del_05_05-per40.json\",\n", "    \"instruct-del_06_06-per40.json\",\n", "    \"instruct-del_07_07-per40.json\",\n", "    \"instruct-del_08_08-per40.json\",\n", "    \"instruct-del_09_09-per40.json\",\n", "    \"instruct-del_10_10-per40.json\",\n", "    \"instruct-del_11_20-per10.json\",\n", "    \"instruct-del_21_30-per10.json\",\n", ")\n", "json_file_dir = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13/\"\n", ")\n", "\n", "\n", "edit_data_in_house = []\n", "skip_due_to_no_blocks = []\n", "skip_due_to_multiple_blocks = []\n", "skip_due_to_merge_conflict = []\n", "\n", "for json_file in file_names:\n", "    json_file_path = pathlib.Path(json_file_dir) / json_file\n", "    with json_file_path.open(\"r\") as f:\n", "        for line in f:\n", "            xdict = json.loads(line)\n", "            data = utils_for_dataclass.create_from_dict(EditData, xdict[\"edit_data\"])\n", "            instruction = xdict[\"instruction\"]\n", "            data.instruction = instruction\n", "            blocks = utils_for_str.extract_code_within_backticks(instruction)\n", "            if len(blocks) == 0:\n", "                # print(f\"No blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_no_blocks.append(data)\n", "                continue\n", "            elif len(blocks) > 1:\n", "                # print(f\"Too many blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_multiple_blocks.append(data)\n", "                continue\n", "            final_instruction = blocks[0]\n", "            if final_instruction[0] == '\"' and final_instruction[-1] == '\"':\n", "                final_instruction = final_instruction[1:-1]\n", "            elif final_instruction[0] == \"`\" and final_instruction[-1] == \"`\":\n", "                final_instruction = final_instruction[1:-1]\n", "            data.instruction = final_instruction\n", "            if detect_merge_conflict(data.new_file_content) or detect_merge_conflict(\n", "                data.old_file_content\n", "            ):\n", "                skip_due_to_merge_conflict.append(data)\n", "                continue\n", "            edit_data_in_house.append(data)\n", "print(f\"There are {len(edit_data_in_house)} edit data with instructions.\")\n", "print(f\"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}\")\n", "print(f\"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}\")\n", "print(f\"skip_due_to_merge_conflict={len(skip_due_to_merge_conflict)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create for the Python-only dataset"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load 702062 examples from commitpackft.all.jsonl.zst\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 702062/702062 [00:11<00:00, 61959.09it/s]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Load 56017 Python-Only examples from commitpackft.all.jsonl.zst\n", "There are 25721 / 56017 examples in total.\n"]}], "source": ["# Load the CommitPackFT data\n", "import tqdm\n", "from research.core import utils_for_dataclass\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits import data_type\n", "\n", "all_raw_edit_from_cpft = utils_for_file.read_jsonl_zst(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.processed/commitpackft.all.jsonl.zst\"\n", ")\n", "print(f\"Load {len(all_raw_edit_from_cpft)} examples from commitpackft.all.jsonl.zst\")\n", "temp_cpft_edit_from_cpft = []\n", "for x in tqdm.tqdm(all_raw_edit_from_cpft, total=len(all_raw_edit_from_cpft)):\n", "    if x[\"language\"] != \"Python\":\n", "        continue\n", "    if detect_merge_conflict(x[\"old_file_content\"]):\n", "        continue\n", "    if detect_merge_conflict(x[\"new_file_content\"]):\n", "        continue\n", "    x = utils_for_dataclass.create_from_dict(data_type.EditData, x)\n", "    temp_cpft_edit_from_cpft.append(x)\n", "print(\n", "    f\"Load {len(temp_cpft_edit_from_cpft)} Python-Only examples from commitpackft.all.jsonl.zst\"\n", ")\n", "\n", "# Filter out for the balanced sampling.\n", "max_per_diff: dict[tuple[int, int], int] = dict()\n", "for j in range(0, 41):\n", "    max_per_diff[(0, j)] = 100\n", "for i in range(1, 11):\n", "    for j in range(0, 41):\n", "        max_per_diff[(i, j)] = 300\n", "for i in range(11, 31):\n", "    for j in range(0, 41):\n", "        max_per_diff[(i, j)] = 100\n", "\n", "commitpack_python = keep_by_diff_range(temp_cpft_edit_from_cpft, max_per_diff)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Therea are 49521 examples\n", "  0  12:  139\n", "  0   1:  105\n", "  0  28:  139\n", "  0   3:  132\n", "  0   5:  135\n", "  0   4:  131\n", "  0   2:  132\n", "  0   9:  138\n", "  0  14:  139\n", "  0  11:  139\n", "  0   7:  137\n", "  0   6:  135\n", "  0  36:  140\n", "  0  15:  139\n", "  0   8:  138\n", "  0  18:  140\n", "  0  21:  140\n", "  0  24:  140\n", "  0  10:  138\n", "  0  40:  139\n", "  0  16:  140\n", "  0  17:  140\n", "  0  23:  140\n", "  0  35:  138\n", "  0  39:  140\n", "  0  33:  140\n", "  0  19:  140\n", "  0  29:  140\n", "  0  13:  139\n", "  0  20:  140\n", "  0  37:  138\n", "  0  34:  139\n", "  0  27:  140\n", "  0  26:  140\n", "  0  25:  139\n", "  0  22:  139\n", "  0  30:  140\n", "  0  31:  140\n", "  0  32:  139\n", "  0  38:  140\n", "  1   2:  295\n", "  1   3:  309\n", "  1   1:  238\n", "  1   5:  260\n", "  1   0:  271\n", "  1  13:   70\n", "  1   4:  316\n", "  1  16:   50\n", "  1   9:   78\n", "  1   7:  127\n", "  1  26:   45\n", "  1  10:   70\n", "  1  11:   64\n", "  1   6:  190\n", "  1  31:   42\n", "  1  24:   42\n", "  1  15:   57\n", "  1  19:   53\n", "  1  14:   50\n", "  1   8:  110\n", "  1  21:   45\n", "  1  39:   41\n", "  1  28:   42\n", "  1  20:   49\n", "  1  12:   67\n", "  1  18:   47\n", "  1  17:   48\n", "  1  35:   42\n", "  1  23:   43\n", "  1  25:   45\n", "  1  33:   42\n", "  1  40:   40\n", "  1  36:   41\n", "  1  34:   40\n", "  1  22:   50\n", "  1  30:   43\n", "  1  38:   43\n", "  1  27:   42\n", "  1  32:   45\n", "  1  29:   43\n", "  1  37:   41\n", "  2   3:  249\n", "  2   2:  300\n", "  2   4:  187\n", "  2  10:   69\n", "  2  11:   63\n", "  2   1:  243\n", "  2  13:   51\n", "  2  17:   46\n", "  2   5:  163\n", "  2   0:  200\n", "  2   6:  129\n", "  2  27:   43\n", "  2  19:   44\n", "  2   7:  103\n", "  2  23:   45\n", "  2   8:   83\n", "  2  12:   58\n", "  2  34:   41\n", "  2  32:   41\n", "  2  15:   46\n", "  2  40:   41\n", "  2   9:   65\n", "  2  38:   43\n", "  2  33:   43\n", "  2  21:   45\n", "  2  18:   49\n", "  2  22:   42\n", "  2  30:   43\n", "  2  29:   43\n", "  2  16:   45\n", "  2  28:   42\n", "  2  14:   51\n", "  2  26:   40\n", "  2  20:   44\n", "  2  31:   44\n", "  2  24:   43\n", "  2  39:   40\n", "  2  35:   41\n", "  2  25:   41\n", "  2  36:   40\n", "  2  37:   41\n", "  3   7:   95\n", "  3   3:  308\n", "  3   1:  127\n", "  3   4:  194\n", "  3  11:   59\n", "  3   0:  138\n", "  3   5:  151\n", "  3   9:   85\n", "  3   2:  116\n", "  3  14:   52\n", "  3  15:   46\n", "  3  32:   41\n", "  3   8:   90\n", "  3   6:  141\n", "  3  10:   83\n", "  3  12:   58\n", "  3  18:   44\n", "  3  23:   44\n", "  3  26:   43\n", "  3  13:   58\n", "  3  28:   43\n", "  3  16:   53\n", "  3  21:   44\n", "  3  20:   48\n", "  3  17:   48\n", "  3  38:   41\n", "  3  22:   43\n", "  3  31:   42\n", "  3  39:   41\n", "  3  19:   50\n", "  3  29:   39\n", "  3  30:   42\n", "  3  33:   41\n", "  3  34:   42\n", "  3  27:   44\n", "  3  25:   41\n", "  3  24:   43\n", "  3  36:   42\n", "  3  35:   40\n", "  3  37:   41\n", "  3  40:   41\n", "  4   2:   94\n", "  4   5:  185\n", "  4   1:  109\n", "  4  13:   60\n", "  4   3:  103\n", "  4   4:  310\n", "  4   6:  149\n", "  4  12:   64\n", "  4   7:  108\n", "  4   9:   81\n", "  4  10:   75\n", "  4  11:   64\n", "  4  20:   46\n", "  4   0:  105\n", "  4  17:   53\n", "  4  14:   49\n", "  4  23:   49\n", "  4   8:  101\n", "  4  35:   40\n", "  4  15:   51\n", "  4  21:   47\n", "  4  30:   44\n", "  4  18:   43\n", "  4  16:   56\n", "  4  25:   45\n", "  4  33:   41\n", "  4  32:   41\n", "  4  29:   41\n", "  4  28:   41\n", "  4  31:   41\n", "  4  24:   44\n", "  4  36:   40\n", "  4  27:   45\n", "  4  19:   50\n", "  4  40:   41\n", "  4  39:   43\n", "  4  38:   42\n", "  4  22:   44\n", "  4  34:   42\n", "  4  26:   42\n", "  4  37:   41\n", "  5   8:  130\n", "  5  24:   43\n", "  5   7:  131\n", "  5  10:   76\n", "  5   5:  314\n", "  5  37:   42\n", "  5   2:   66\n", "  5   9:  108\n", "  5   6:  157\n", "  5   0:   75\n", "  5  11:   91\n", "  5   4:  105\n", "  5   3:   83\n", "  5  20:   50\n", "  5  14:   62\n", "  5   1:   77\n", "  5  29:   44\n", "  5  13:   63\n", "  5  31:   39\n", "  5  18:   47\n", "  5  17:   53\n", "  5  15:   55\n", "  5  26:   43\n", "  5  23:   48\n", "  5  22:   44\n", "  5  12:   66\n", "  5  19:   43\n", "  5  16:   50\n", "  5  25:   44\n", "  5  39:   40\n", "  5  32:   42\n", "  5  33:   41\n", "  5  36:   41\n", "  5  28:   44\n", "  5  34:   41\n", "  5  38:   41\n", "  5  21:   46\n", "  5  30:   40\n", "  5  35:   44\n", "  5  27:   41\n", "  5  40:   41\n", "  6   6:  297\n", "  6   9:  122\n", "  6   7:  140\n", "  6  25:   42\n", "  6  11:   81\n", "  6  17:   60\n", "  6   0:   59\n", "  6   1:   57\n", "  6   8:  132\n", "  6  18:   57\n", "  6  12:   71\n", "  6  10:   99\n", "  6  13:   67\n", "  6   4:   79\n", "  6  19:   50\n", "  6   5:  104\n", "  6  22:   52\n", "  6  15:   59\n", "  6  28:   43\n", "  6  31:   42\n", "  6  23:   45\n", "  6  16:   64\n", "  6  14:   60\n", "  6   3:   56\n", "  6  33:   43\n", "  6   2:   67\n", "  6  27:   45\n", "  6  21:   49\n", "  6  24:   44\n", "  6  37:   42\n", "  6  36:   40\n", "  6  30:   44\n", "  6  34:   40\n", "  6  20:   45\n", "  6  26:   44\n", "  6  32:   39\n", "  6  29:   41\n", "  6  40:   40\n", "  6  38:   41\n", "  6  35:   40\n", "  6  39:   42\n", "  7   7:  253\n", "  7  19:   53\n", "  7   9:  104\n", "  7   5:   71\n", "  7  11:   89\n", "  7   8:  124\n", "  7  20:   48\n", "  7  18:   59\n", "  7  13:   74\n", "  7  26:   43\n", "  7   6:   72\n", "  7  22:   45\n", "  7   3:   51\n", "  7  17:   46\n", "  7  10:  101\n", "  7   2:   46\n", "  7  15:   66\n", "  7  39:   43\n", "  7  33:   41\n", "  7  12:   74\n", "  7   4:   63\n", "  7   1:   47\n", "  7  23:   45\n", "  7   0:   40\n", "  7  24:   46\n", "  7  16:   66\n", "  7  25:   49\n", "  7  14:   65\n", "  7  32:   40\n", "  7  31:   45\n", "  7  37:   41\n", "  7  21:   52\n", "  7  35:   44\n", "  7  29:   41\n", "  7  40:   41\n", "  7  36:   40\n", "  7  28:   44\n", "  7  30:   43\n", "  7  27:   47\n", "  7  38:   41\n", "  7  34:   41\n", "  8  15:   64\n", "  8   6:   63\n", "  8  29:   41\n", "  8  14:   77\n", "  8   5:   65\n", "  8   3:   55\n", "  8   7:   81\n", "  8   8:  211\n", "  8  10:  104\n", "  8   4:   59\n", "  8  34:   42\n", "  8   9:  135\n", "  8  13:   83\n", "  8   0:   52\n", "  8  11:   90\n", "  8   2:   45\n", "  8  24:   44\n", "  8  20:   50\n", "  8  12:   82\n", "  8   1:   52\n", "  8  28:   40\n", "  8  37:   41\n", "  8  25:   46\n", "  8  18:   58\n", "  8  19:   49\n", "  8  16:   63\n", "  8  21:   57\n", "  8  27:   43\n", "  8  39:   41\n", "  8  22:   52\n", "  8  17:   63\n", "  8  26:   44\n", "  8  40:   42\n", "  8  31:   44\n", "  8  33:   45\n", "  8  32:   43\n", "  8  35:   41\n", "  8  23:   46\n", "  8  36:   41\n", "  8  38:   40\n", "  8  30:   45\n", "  9  23:   48\n", "  9  15:   57\n", "  9   0:   55\n", "  9   9:  196\n", "  9  12:   89\n", "  9   5:   46\n", "  9   7:   74\n", "  9  10:  114\n", "  9   8:   86\n", "  9   6:   67\n", "  9   4:   48\n", "  9  11:  112\n", "  9  14:   72\n", "  9  13:   70\n", "  9   2:   41\n", "  9  18:   77\n", "  9   3:   49\n", "  9  21:   49\n", "  9   1:   47\n", "  9  16:   68\n", "  9  25:   48\n", "  9  20:   52\n", "  9  31:   49\n", "  9  36:   41\n", "  9  24:   46\n", "  9  37:   42\n", "  9  38:   42\n", "  9  19:   51\n", "  9  17:   67\n", "  9  32:   45\n", "  9  27:   44\n", "  9  28:   43\n", "  9  26:   51\n", "  9  22:   54\n", "  9  29:   44\n", "  9  34:   40\n", "  9  33:   44\n", "  9  39:   44\n", "  9  30:   44\n", "  9  35:   45\n", "  9  40:   41\n", " 10  10:  188\n", " 10   2:   43\n", " 10   4:   47\n", " 10   6:   57\n", " 10  11:  116\n", " 10   1:   42\n", " 10   9:   79\n", " 10   8:   67\n", " 10  13:   87\n", " 10  16:   68\n", " 10  17:   63\n", " 10  14:   85\n", " 10  15:   70\n", " 10  12:   98\n", " 10  25:   47\n", " 10   0:   58\n", " 10   5:   52\n", " 10  20:   55\n", " 10  22:   57\n", " 10   3:   46\n", " 10  23:   51\n", " 10  19:   57\n", " 10  24:   50\n", " 10  18:   61\n", " 10  32:   42\n", " 10  34:   45\n", " 10  36:   41\n", " 10  30:   45\n", " 10   7:   52\n", " 10  28:   46\n", " 10  29:   49\n", " 10  21:   52\n", " 10  40:   43\n", " 10  38:   41\n", " 10  31:   42\n", " 10  27:   45\n", " 10  26:   48\n", " 10  35:   43\n", " 10  39:   40\n", " 10  33:   43\n", " 10  37:   40\n", " 19  23:   32\n", " 11  11:  100\n", " 17  20:   41\n", " 13  13:   96\n", " 14  13:   36\n", " 19  19:   65\n", " 14   5:   13\n", " 16  16:  105\n", " 14  14:   98\n", " 13  27:   18\n", " 14  29:   13\n", " 17  13:   21\n", " 13  11:   30\n", " 13  35:   11\n", " 13  21:   24\n", " 15  13:   25\n", " 13  29:   13\n", " 14  16:   69\n", " 13   3:   13\n", " 20  20:   75\n", " 15  18:   39\n", " 13   2:   10\n", " 19  31:   19\n", " 13  12:   36\n", " 11   9:   29\n", " 12  11:   42\n", " 11  19:   28\n", " 19  18:   29\n", " 13  17:   45\n", " 12   8:   18\n", " 12  15:   64\n", " 11  17:   41\n", " 18  18:   76\n", " 12   5:   13\n", " 12  22:   27\n", " 13  18:   45\n", " 16   0:   13\n", " 13   7:   12\n", " 19  22:   40\n", " 12  14:   68\n", " 19  32:   21\n", " 12  21:   27\n", " 17  29:   20\n", " 14  17:   45\n", " 18  33:   13\n", " 15  23:   28\n", " 15  15:   86\n", " 16  17:   56\n", " 17  22:   35\n", " 17  21:   45\n", " 17   8:   13\n", " 12  12:   98\n", " 12  36:   12\n", " 16  10:   17\n", " 17   4:   11\n", " 13  16:   45\n", " 14  18:   45\n", " 15  20:   40\n", " 11  28:   14\n", " 11  24:   25\n", " 12   4:   16\n", " 14  10:   15\n", " 15  25:   32\n", " 15   4:   11\n", " 14   4:   13\n", " 11  13:   78\n", " 14   0:   13\n", " 19   0:    3\n", " 13  15:   75\n", " 11  12:   67\n", " 13  28:   22\n", " 16  26:   29\n", " 17  18:   48\n", " 18  23:   33\n", " 17  11:   14\n", " 13  36:   14\n", " 15  16:   59\n", " 13  19:   36\n", " 18  15:   20\n", " 12  18:   47\n", " 18  13:   15\n", " 17  27:   28\n", " 14  25:   20\n", " 15  19:   49\n", " 13  24:   19\n", " 17  14:   21\n", " 13  10:   25\n", " 19  37:   12\n", " 15   6:   13\n", " 16  11:   13\n", " 19  20:   58\n", " 17  23:   34\n", " 12   0:   11\n", " 18  40:   14\n", " 14  35:   13\n", " 17  30:   21\n", " 18  14:   23\n", " 11   0:   15\n", " 17  12:   22\n", " 14   6:   10\n", " 19  24:   38\n", " 15  11:   21\n", " 20  26:   34\n", " 14  15:   59\n", " 17  17:   93\n", " 12  27:   16\n", " 14  12:   32\n", " 20  33:   17\n", " 14  20:   29\n", " 13   4:   15\n", " 17  35:   15\n", " 20  24:   33\n", " 19  29:   21\n", " 17  19:   45\n", " 20  30:   19\n", " 12   7:   14\n", " 18  22:   42\n", " 12  10:   27\n", " 15  27:   27\n", " 11  18:   35\n", " 15  30:   19\n", " 14  37:   11\n", " 15   1:    9\n", " 12   9:   31\n", " 16  18:   49\n", " 17   2:   12\n", " 11   1:   14\n", " 14  27:   20\n", " 19  10:   13\n", " 13  14:   60\n", " 13  25:   25\n", " 14  22:   29\n", " 17   3:   11\n", " 15  35:   16\n", " 18  24:   25\n", " 11  16:   34\n", " 15  22:   29\n", " 15  12:   28\n", " 18  11:   13\n", " 13  23:   28\n", " 16  20:   39\n", " 15   7:   15\n", " 12  39:   11\n", " 18  17:   39\n", " 16  31:   20\n", " 16  22:   35\n", " 11  15:   52\n", " 16  19:   38\n", " 15  14:   35\n", " 15   3:   11\n", " 11  32:   15\n", " 15  21:   42\n", " 16  25:   35\n", " 11   7:   21\n", " 11  14:   61\n", " 16   5:   10\n", " 11  10:   54\n", " 20   7:   11\n", " 19  15:   16\n", " 16  28:   18\n", " 18  20:   49\n", " 15  24:   20\n", " 14  28:   14\n", " 15   8:   14\n", " 15  38:   12\n", " 12  17:   38\n", " 15  17:   61\n", " 18  30:   21\n", " 14  11:   21\n", " 16  39:   17\n", " 17  24:   28\n", " 11  23:   23\n", " 16  40:   10\n", " 18   0:    9\n", " 16  24:   27\n", " 17  25:   27\n", " 11  38:   13\n", " 16  38:   15\n", " 14  19:   37\n", " 11   8:   28\n", " 19  12:   13\n", " 19   2:   11\n", " 11  20:   29\n", " 15   9:   19\n", " 13   8:   19\n", " 20   5:   12\n", " 17  37:   15\n", " 19   7:   15\n", " 16  13:   18\n", " 18  21:   40\n", " 18  19:   45\n", " 18  27:   27\n", " 17  10:   12\n", " 11   6:   16\n", " 20  19:   27\n", " 14   9:   15\n", " 20  22:   47\n", " 11  26:   20\n", " 12  16:   53\n", " 19  16:   26\n", " 20  23:   36\n", " 14  36:   17\n", " 17   1:   12\n", " 16  29:   20\n", " 20   8:   11\n", " 16  34:   15\n", " 16   8:   13\n", " 12  19:   35\n", " 12  13:   72\n", " 13   9:   16\n", " 16  33:   13\n", " 14  31:   14\n", " 11  25:   20\n", " 14  30:   15\n", " 19  13:   18\n", " 19  39:   15\n", " 20  40:   15\n", " 17  16:   32\n", " 18  35:   19\n", " 17  15:   25\n", " 20  15:   17\n", " 12   2:   12\n", " 16   9:   15\n", " 14  38:   14\n", " 20  21:   45\n", " 11   3:   11\n", " 19  14:   15\n", " 19  25:   36\n", " 16   2:   11\n", " 14  33:   18\n", " 14  21:   36\n", " 19  21:   43\n", " 11  30:   16\n", " 18  37:   16\n", " 18   9:   11\n", " 17  36:   17\n", " 12   1:   15\n", " 14   7:   15\n", " 20  18:   18\n", " 12   6:   17\n", " 13  30:   16\n", " 13   5:   13\n", " 15   0:    9\n", " 19  34:   17\n", " 19  11:   12\n", " 18  12:   16\n", " 15  33:   16\n", " 11   4:   14\n", " 19  26:   25\n", " 17   7:   12\n", " 18  25:   26\n", " 17   0:    6\n", " 17  26:   27\n", " 14   8:   14\n", " 20   4:   12\n", " 18  26:   30\n", " 12  28:   13\n", " 19  17:   23\n", " 17  31:   18\n", " 12  30:   18\n", " 11  29:   13\n", " 17  40:   12\n", " 12  20:   31\n", " 11  21:   30\n", " 13  34:   14\n", " 12  23:   25\n", " 13  37:   13\n", " 14   3:   14\n", " 14  39:   11\n", " 20  34:   18\n", " 12  24:   17\n", " 18  29:   23\n", " 12   3:   11\n", " 13  20:   20\n", " 18  16:   29\n", " 16  12:   18\n", " 20  17:   19\n", " 15  26:   19\n", " 16   1:    9\n", " 11  35:   13\n", " 11  22:   30\n", " 12  26:   20\n", " 14  23:   24\n", " 15   5:   15\n", " 11  33:   15\n", " 16  15:   43\n", " 17  34:   16\n", " 16  21:   28\n", " 13  31:   12\n", " 13  39:   10\n", " 13   0:    9\n", " 11  37:   15\n", " 20  25:   30\n", " 18  34:   15\n", " 20  14:   13\n", " 16   4:   13\n", " 16   6:   11\n", " 19  30:   16\n", " 15   2:   11\n", " 20  36:   24\n", " 20  31:   22\n", " 11  27:   20\n", " 15  32:   16\n", " 18   2:   10\n", " 15  10:   18\n", " 11  40:   11\n", " 19   4:   12\n", " 19   8:   10\n", " 15  28:   15\n", " 12  25:   16\n", " 18  28:   23\n", " 16  14:   28\n", " 14   2:   12\n", " 13   6:   16\n", " 20  16:   14\n", " 17  39:   15\n", " 18  38:   14\n", " 20  10:   11\n", " 14  24:   27\n", " 19  35:   14\n", " 12  29:   16\n", " 16  23:   24\n", " 15  36:   17\n", " 18  10:   20\n", " 11   2:   13\n", " 14  40:   14\n", " 19  28:   27\n", " 20   3:   10\n", " 16  30:   18\n", " 12  33:   17\n", " 20  12:   11\n", " 18  31:   24\n", " 19  38:   18\n", " 20  13:   12\n", " 13  33:   17\n", " 11  39:   13\n", " 16  27:   18\n", " 20   0:    2\n", " 19   1:   14\n", " 11  31:   12\n", " 15  37:   15\n", " 17   6:   11\n", " 12  32:   15\n", " 20  39:   13\n", " 13  40:   11\n", " 19  33:   22\n", " 20  28:   29\n", " 16  35:   15\n", " 18   8:   11\n", " 14   1:   10\n", " 13  26:   18\n", " 20  27:   26\n", " 13   1:   12\n", " 16   3:   12\n", " 19   3:   12\n", " 20  37:   17\n", " 14  32:   13\n", " 18   6:   13\n", " 12  31:   17\n", " 17  32:   17\n", " 15  31:   17\n", " 11   5:   15\n", " 20  32:   21\n", " 20   9:   12\n", " 14  34:   12\n", " 17   5:   12\n", " 17   9:   12\n", " 18   1:   11\n", " 12  35:   15\n", " 13  22:   27\n", " 14  26:   24\n", " 18   5:   10\n", " 20   1:   13\n", " 16   7:   14\n", " 20   6:   11\n", " 17  33:   14\n", " 17  28:   19\n", " 18  32:   16\n", " 20  29:   21\n", " 19  27:   23\n", " 12  40:   10\n", " 20   2:   10\n", " 19   6:   10\n", " 20  11:   10\n", " 12  34:   11\n", " 18   3:   14\n", " 19  36:   15\n", " 15  29:   17\n", " 18  36:   15\n", " 11  34:   16\n", " 15  40:   13\n", " 11  36:   12\n", " 18   4:   10\n", " 16  37:   14\n", " 15  34:   16\n", " 12  37:   12\n", " 18  39:   15\n", " 18   7:   11\n", " 19  40:   14\n", " 20  35:   17\n", " 19   9:   13\n", " 16  32:   19\n", " 13  38:   15\n", " 13  32:   12\n", " 15  39:   13\n", " 19   5:   12\n", " 16  36:   13\n", " 17  38:   18\n", " 12  38:   11\n", " 20  38:   13\n", " 25  20:   16\n", " 26  17:   12\n", " 22  22:   49\n", " 21  21:   60\n", " 24  14:   13\n", " 22  24:   38\n", " 23  24:   37\n", " 22  15:   12\n", " 22  19:   13\n", " 21  27:   26\n", " 27  16:   11\n", " 28  24:   16\n", " 21   3:    9\n", " 28  36:   14\n", " 29  31:   31\n", " 25  38:   14\n", " 22  37:   17\n", " 21  12:   11\n", " 27  33:   19\n", " 23  23:   53\n", " 24  32:   24\n", " 21  23:   40\n", " 24  26:   41\n", " 29  32:   27\n", " 29   9:    9\n", " 30  35:   19\n", " 24   7:   12\n", " 23  14:   12\n", " 24  24:   55\n", " 24  12:   10\n", " 22  18:   15\n", " 24  38:   12\n", " 25  32:   23\n", " 21  24:   37\n", " 27  18:   13\n", " 21  28:   27\n", " 26   2:   10\n", " 21  37:   15\n", " 25  17:   13\n", " 24  23:   24\n", " 27  27:   42\n", " 21  17:   15\n", " 25  16:   11\n", " 22  30:   28\n", " 26  11:   13\n", " 24  16:   14\n", " 30  30:   35\n", " 26  35:   19\n", " 28   0:    5\n", " 29  40:   16\n", " 23  18:   11\n", " 25  18:   12\n", " 27  30:   24\n", " 27  34:   24\n", " 22  17:   14\n", " 22  32:   14\n", " 29  18:   11\n", " 21  25:   21\n", " 29  30:   35\n", " 23  25:   36\n", " 29  33:   25\n", " 24  18:   18\n", " 27  23:   15\n", " 22   3:   11\n", " 22   8:   10\n", " 28  19:   10\n", " 25  30:   27\n", " 24  28:   31\n", " 23  33:   20\n", " 26  26:   47\n", " 21  16:   11\n", " 29  13:   11\n", " 21   6:   10\n", " 25  28:   28\n", " 24  31:   24\n", " 22  40:   16\n", " 27  13:   10\n", " 25  27:   32\n", " 23   3:   10\n", " 22  34:   17\n", " 27  20:   12\n", " 25  36:   15\n", " 23  34:   18\n", " 21  22:   44\n", " 24   3:   11\n", " 30  19:   11\n", " 23  17:   16\n", " 23  29:   19\n", " 21  35:   19\n", " 29  29:   35\n", " 29   6:   10\n", " 27  36:   20\n", " 27  29:   31\n", " 30  37:   19\n", " 22  31:   21\n", " 25  19:   10\n", " 26  16:   12\n", " 27  17:   12\n", " 30  29:   28\n", " 26  25:   20\n", " 22   5:   11\n", " 21  30:   23\n", " 26  34:   16\n", " 22  36:   15\n", " 27  15:   10\n", " 26   5:   10\n", " 24  17:   14\n", " 23  31:   25\n", " 22  27:   33\n", " 25   7:   11\n", " 29   0:    3\n", " 28  26:   16\n", " 23  30:   24\n", " 27  25:   15\n", " 22  20:   26\n", " 27  39:   12\n", " 28  22:   13\n", " 22  26:   33\n", " 24  34:   16\n", " 21  29:   25\n", " 28  15:   10\n", " 27  28:   32\n", " 25   0:    2\n", " 26  19:   14\n", " 28  30:   28\n", " 28  38:   13\n", " 26   9:   11\n", " 28  32:   22\n", " 22  21:   26\n", " 24  21:   12\n", " 25  12:   10\n", " 21   9:   14\n", " 30  31:   20\n", " 30  39:   18\n", " 25  33:   19\n", " 25  25:   51\n", " 28  33:   16\n", " 26  36:   20\n", " 22  14:   11\n", " 27  10:   10\n", " 26  14:   13\n", " 24   4:   10\n", " 28  28:   35\n", " 23  28:   34\n", " 25  22:   14\n", " 22  29:   17\n", " 25  31:   21\n", " 27  21:   10\n", " 28  16:   11\n", " 29  12:   10\n", " 24  25:   33\n", " 26  27:   32\n", " 28  35:   18\n", " 25  26:   31\n", " 27   4:   10\n", " 23  10:   11\n", " 25  24:   20\n", " 26  29:   32\n", " 21  15:   14\n", " 29  27:   17\n", " 24   2:   10\n", " 25  13:   11\n", " 26   8:   10\n", " 23  26:   40\n", " 29  11:   11\n", " 26  31:   19\n", " 30  36:   21\n", " 30  18:   12\n", " 30  23:   14\n", " 23  19:   13\n", " 24  22:   18\n", " 23   0:    3\n", " 21  19:   20\n", " 22  35:   19\n", " 25  39:   12\n", " 21  26:   37\n", " 27  11:   10\n", " 26  15:   13\n", " 25   1:    8\n", " 23  21:   17\n", " 27   2:   12\n", " 26   6:    8\n", " 25  10:   11\n", " 22  16:   18\n", " 27  24:   16\n", " 30  40:   17\n", " 27  37:   22\n", " 24  19:   16\n", " 29  34:   18\n", " 22  33:   19\n", " 24  29:   26\n", " 30  28:   15\n", " 30   6:   10\n", " 23  20:   17\n", " 25  37:   15\n", " 26  23:   16\n", " 21  13:   13\n", " 30  12:   12\n", " 24  36:   14\n", " 24  20:   14\n", " 22   9:   12\n", " 28  40:   13\n", " 25  14:   11\n", " 26  37:   17\n", " 25  23:   22\n", " 22   1:   10\n", " 26  30:   24\n", " 23  27:   33\n", " 23  16:   11\n", " 25  34:   18\n", " 27  31:   26\n", " 27   9:   10\n", " 29  25:   14\n", " 26  18:   12\n", " 30   0:    3\n", " 25   2:   10\n", " 22  28:   20\n", " 27   3:   10\n", " 23   8:   10\n", " 25  21:   14\n", " 21  32:   24\n", " 23  11:   11\n", " 28  10:   10\n", " 26  28:   18\n", " 23  22:   27\n", " 22  25:   37\n", " 28  31:   27\n", " 25   9:   13\n", " 21   5:   10\n", " 22   0:   10\n", " 21   8:   10\n", " 21  36:   18\n", " 22  23:   42\n", " 25  11:   12\n", " 30  13:   11\n", " 23  39:   16\n", " 24  30:   24\n", " 23  32:   28\n", " 23   9:    9\n", " 30  34:   20\n", " 23  36:   18\n", " 21  40:   13\n", " 22  11:   13\n", " 21  38:   15\n", " 24  15:   11\n", " 28  34:   23\n", " 28  11:   10\n", " 22   2:   10\n", " 27  32:   22\n", " 22  39:   17\n", " 23  35:   11\n", " 28   3:   10\n", " 23  13:   12\n", " 27  12:    9\n", " 23   4:   13\n", " 30  32:   22\n", " 21  34:   16\n", " 27   0:    3\n", " 24  40:   16\n", " 30  14:   11\n", " 24   6:   11\n", " 24   8:   12\n", " 26  21:   13\n", " 29  22:   15\n", " 21  20:   34\n", " 28  20:   12\n", " 27  19:   10\n", " 26  38:   18\n", " 22  10:    9\n", " 23   5:   10\n", " 24  39:   17\n", " 26  24:   16\n", " 28  29:   27\n", " 21  33:   21\n", " 22   7:   13\n", " 23   2:   10\n", " 28  25:   19\n", " 30   1:    9\n", " 26   1:   10\n", " 23   1:   10\n", " 24   0:    4\n", " 26  12:   10\n", " 26   7:    9\n", " 30  11:   10\n", " 26  20:   15\n", " 21  39:   12\n", " 22   4:   11\n", " 30  25:   12\n", " 28  27:   20\n", " 27  14:   12\n", " 21  14:   13\n", " 26  13:   13\n", " 29  14:   11\n", " 28   1:   11\n", " 25   4:   10\n", " 29  19:   10\n", " 30  16:   10\n", " 29  28:   13\n", " 21   0:    3\n", " 25  29:   32\n", " 27  22:   14\n", " 21  18:   18\n", " 30  22:   11\n", " 29  20:   10\n", " 27   1:   10\n", " 25  35:   21\n", " 22  38:   15\n", " 21   7:   11\n", " 30  21:   16\n", " 27   5:   10\n", " 21  11:   14\n", " 24   1:   10\n", " 27  40:   16\n", " 26  22:   18\n", " 28   4:   10\n", " 26  32:   23\n", " 24  35:   17\n", " 29  24:   15\n", " 28  17:   10\n", " 29  23:   11\n", " 29  21:   13\n", " 29  35:   15\n", " 26  33:   18\n", " 27   8:   10\n", " 21  31:   22\n", " 28  18:   13\n", " 30  15:   11\n", " 28  21:   12\n", " 28  13:   10\n", " 30  38:   20\n", " 24  11:   10\n", " 28   6:   10\n", " 26  40:   15\n", " 28  37:   19\n", " 30  20:   12\n", " 24   9:   10\n", " 22   6:   10\n", " 27  38:   16\n", " 27  26:   22\n", " 22  12:   11\n", " 29  37:   24\n", " 26   3:   10\n", " 23  40:   13\n", " 28   7:   11\n", " 28   5:   10\n", " 28   9:   10\n", " 28  23:   13\n", " 26  10:   10\n", " 28  14:   10\n", " 24  27:   33\n", " 29  39:   17\n", " 30  33:   22\n", " 25   6:   10\n", " 22  13:   13\n", " 24  33:   14\n", " 30   8:   10\n", " 29  26:   13\n", " 23   6:   13\n", " 28   2:   10\n", " 23  12:   11\n", " 30  17:   10\n", " 26   0:    4\n", " 23   7:   11\n", " 30   7:   10\n", " 24  37:   18\n", " 25  40:   12\n", " 23  15:   12\n", " 30  27:   12\n", " 26  39:   14\n", " 29  38:   13\n", " 28  12:   11\n", " 24   5:   10\n", " 27  35:   18\n", " 28  39:   19\n", " 25   5:   10\n", " 25  15:   11\n", " 29  17:   10\n", " 29   2:   10\n", " 29   1:   11\n", " 21   1:    9\n", " 24  13:   14\n", " 29  36:   15\n", " 30   5:   10\n", " 23  37:   14\n", " 21  10:   12\n", " 27   6:    9\n", " 30  24:   13\n", " 25   3:   10\n", " 23  38:   14\n", " 21   4:   10\n", " 30  10:   10\n", " 29   4:   10\n", " 27   7:   10\n", " 30  26:   13\n", " 29  10:   10\n", " 29   5:   11\n", " 29  15:   12\n", " 29   3:   10\n", " 29   8:   10\n", " 30   4:    9\n", " 21   2:   10\n", " 24  10:   12\n", " 29  16:   10\n", " 28   8:   11\n", " 30   9:    8\n", " 26   4:   10\n", " 29   7:   10\n", " 30   2:   10\n", " 30   3:   10\n", " 25   8:    9\n"]}], "source": ["# Further Filtering\n", "all_edit_data = []\n", "for x in edit_data_in_house + commitpack_python:\n", "    file_name = x.file_path.split(\"/\")[-1]\n", "    if file_name.lower().startswith(\"setup\") or file_name.lower().startswith(\"readme\"):\n", "        continue\n", "    if \"<INST>\" in x.new_file_content:\n", "        continue\n", "    if \"<SELECTED>\" in x.new_file_content:\n", "        continue\n", "    if \"<UPDATED>\" in x.new_file_content:\n", "        continue\n", "    if \"<PREFIX>\" in x.new_file_content:\n", "        continue\n", "    if \"<SUFFIX>\" in x.new_file_content:\n", "        continue\n", "    all_edit_data.append(x)\n", "print(f\"Therea are {len(all_edit_data)} examples\")\n", "\n", "num_per_diff = dict()\n", "for x in all_edit_data:\n", "    assert x.instruction\n", "    diff = x.get_diffs()\n", "    if diff not in num_per_diff:\n", "        num_per_diff[diff] = 0\n", "    num_per_diff[diff] += 1\n", "for (del_l, add_l), num in num_per_diff.items():\n", "    print(f\"{del_l:3d} {add_l:3d}: {num:4d}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CodeLLaMATokenizer Vocab Size = 32016\n", "Llama2Tokenizer Vocab Size = 32000\n", "WizardCoder-Python-13B-V1.0 Vocab Size = 32000\n", "\n", "\n", "\n", "Chosed tokenizer Vocab Size = 32000\n", "train_ratio = 0.95\n", "#train_edit_data_w_instruction = 47044\n", "#valid_edit_data_w_instruction = 2477\n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 47044/47044 [01:41<00:00, 462.12it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of examples = 47044\n", "Average number of tokens per example = 875.477808009523 += 630.1450490803908\n", "Minimum number of tokens per example = 61\n", "Maximum number of tokens per example = 10062\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2477/2477 [00:05<00:00, 428.97it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of examples = 2477\n", "Average number of tokens per example = 910.3698021800565 += 656.8871851585445\n", "Minimum number of tokens per example = 85\n", "Maximum number of tokens per example = 5966\n", "-\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Create the index dataset\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "\n", "tokenizer_codellama = CodeLLaMATokenizer()\n", "tokenizer_llama2 = Llama2Tokenizer()\n", "tokenizer_wizard = Llama2Tokenizer(\n", "    model_path=\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-13B-V1.0/tokenizer.model\"\n", ")\n", "\n", "tokenizer_codellama_inv_vocab = tokenizer_codellama.inv_vocab\n", "tokenizer_llama2_inv_vocab = tokenizer_llama2.inv_vocab\n", "tokenizer_wizard_inv_vocab = tokenizer_wizard.inv_vocab\n", "\n", "print(f\"CodeLLaMATokenizer Vocab Size = {tokenizer_codellama.vocab_size}\")\n", "print(f\"Llama2Tokenizer Vocab Size = {tokenizer_llama2.vocab_size}\")\n", "print(f\"WizardCoder-Python-13B-V1.0 Vocab Size = {tokenizer_wizard.vocab_size}\")\n", "print(\"\\n\\n\")\n", "\n", "tokenizer = tokenizer_llama2\n", "print(f\"Chosed tokenizer Vocab Size = {tokenizer.vocab_size}\")\n", "\n", "indexes = list(range(len(all_edit_data)))\n", "random.shuffle(indexes)\n", "train_ratio = 0.95\n", "train_indexes = indexes[: int(len(indexes) * train_ratio)]\n", "valid_indexes = indexes[int(len(indexes) * train_ratio) :]\n", "train_edit_data_w_instruction = [all_edit_data[i] for i in train_indexes]\n", "valid_edit_data_w_instruction = [all_edit_data[i] for i in valid_indexes]\n", "print(f\"train_ratio = {train_ratio}\")\n", "print(f\"#train_edit_data_w_instruction = {len(train_edit_data_w_instruction)}\")\n", "print(f\"#valid_edit_data_w_instruction = {len(valid_edit_data_w_instruction)}\")\n", "print(\"\\n\")\n", "\n", "\n", "train_examples = build_to_fine_tun_data(\n", "    train_edit_data_w_instruction, tokenizer=tokenizer\n", ")\n", "print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "valid_examples = build_to_fine_tun_data(\n", "    valid_edit_data_w_instruction, tokenizer=tokenizer\n", ")\n", "print(\"-\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 47044/47044 [00:04<00:00, 10738.30it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Save 47044 examples to /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/unpacked_train\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 2477/2477 [00:00<00:00, 10831.01it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Save 2477 examples to /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/unpacked_valid\n", "There are 47044 examples in total.\n", "#repeated_examples = 141132 with repeats=3\n", "Skip 81 examples due to over-4096-tokens.\n", "Packed 141132 examples into 141051 4096-length-sequences.\n", "On average, the number of paddings is 3223.34.\n", "There are 141051 sequences.\n", "Built the train-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_full_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 2477 examples in total.\n", "#repeated_examples = 2477 with repeats=1\n", "Skip 5 examples due to over-4096-tokens.\n", "Packed 2477 examples into 2472 4096-length-sequences.\n", "On average, the number of paddings is 3193.62.\n", "There are 2472 sequences.\n", "Built the valid-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_full_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 47044 examples in total.\n", "#repeated_examples = 141132 with repeats=3\n", "Skip 81 examples due to over-4096-tokens.\n", "Packed 141132 examples into 25912 4096-length-sequences.\n", "On average, the number of paddings is 650.04.\n", "There are 25912 sequences.\n", "Built the train-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_full_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 2477 examples in total.\n", "#repeated_examples = 2477 with repeats=1\n", "Skip 5 examples due to over-4096-tokens.\n", "Packed 2477 examples into 469 4096-length-sequences.\n", "On average, the number of paddings is 692.05.\n", "There are 469 sequences.\n", "Built the valid-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_full_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 47044 examples in total.\n", "#repeated_examples = 141132 with repeats=3\n", "Skip 81 examples due to over-4096-tokens.\n", "Packed 141132 examples into 141051 4096-length-sequences.\n", "On average, the number of paddings is 3223.34.\n", "There are 141051 sequences.\n", "Built the train-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_onlytgt_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 2477 examples in total.\n", "#repeated_examples = 2477 with repeats=1\n", "Skip 5 examples due to over-4096-tokens.\n", "Packed 2477 examples into 2472 4096-length-sequences.\n", "On average, the number of paddings is 3193.62.\n", "There are 2472 sequences.\n", "Built the valid-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 47044 examples in total.\n", "#repeated_examples = 141132 with repeats=3\n", "Skip 81 examples due to over-4096-tokens.\n", "Packed 141132 examples into 25907 4096-length-sequences.\n", "On average, the number of paddings is 653.15.\n", "There are 25907 sequences.\n", "Built the train-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_onlytgt_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 2477 examples in total.\n", "#repeated_examples = 2477 with repeats=1\n", "Skip 5 examples due to over-4096-tokens.\n", "Packed 2477 examples into 469 4096-length-sequences.\n", "On average, the number of paddings is 683.11.\n", "There are 469 sequences.\n", "Built the valid-v1 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["# Build the raw unpacked dataset\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "unpacked_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/unpacked\"\n", ")\n", "unpacked_dir.mkdir(parents=True, exist_ok=True)\n", "for save_name, examples in ((\"train\", train_examples), (\"valid\", valid_examples)):\n", "    output_path = unpacked_dir.parent / f\"{unpacked_dir.stem}_{save_name}\"\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for example in tqdm.tqdm(examples):\n", "        question, answer = example[\"question\"], example[\"answer\"]\n", "        builder.add_item(torch.concat((-torch.Tensor(question), torch.Tensor(answer))))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Save {len(examples)} examples to {output_path}\")\n", "\n", "for mask_input in (<PERSON><PERSON><PERSON>, <PERSON>):\n", "    for pad_or_pack in (<PERSON><PERSON><PERSON>, <PERSON>):\n", "        build_whole_dataset(\n", "            train_examples,\n", "            \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/\",\n", "            \"train-v1\",\n", "            repeats=6,\n", "            seq_len=4096,\n", "            mask_input=mask_input,\n", "            use_pack_or_pad=pad_or_pack,\n", "        )\n", "        print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "        build_whole_dataset(\n", "            valid_examples,\n", "            \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/\",\n", "            \"valid-v1\",\n", "            repeats=1,\n", "            seq_len=4096,\n", "            mask_input=mask_input,\n", "            use_pack_or_pad=pad_or_pack,\n", "        )\n", "        print(\"\\n\" + \"-\" * 100 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Training on this new dataset\n", "\n", "Only fine-tuning from `CodeLlama-7b-Python`\n", "\n", "```bash\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32000 --rope_theta=1000000.0 \\\n", " --learning_rate=1e-6 --decay_lr=False --weight_decay=1e-1 \\\n", " --max_iters=2000 --batch_size=8 \\\n", " --eval_interval=50 --eval_iters=0 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train1v_r3n_s4096_onlytgt \\\n", " --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid1v_r3n_s4096_onlytgt \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \\\n", " --wandb_log=True --wandb_project=edit-exps --wandb_run_name=DXY-CL-PY7B-MixPY1vOTarget-step_2000-WD0_1-b8-lr_000001\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32000 --rope_theta=1000000.0 \\\n", " --learning_rate=1e-6 --decay_lr=False \\\n", " --max_iters=2000 --batch_size=8 \\\n", " --eval_interval=50 --eval_iters=0 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train1v_r3n_s4096_full \\\n", " --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid1v_r3n_s4096_full \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit-v2/ \\\n", " --wandb_log=True --wandb_project=edit-exps --wandb_run_name=DXY-CL-PY7B-MixPY1vFull-step_2000-b8-lr_000001\n", "\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create all-language dataset"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load 702062 examples from commitpackft.all.jsonl.zst\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 702062/702062 [02:06<00:00, 5568.46it/s] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Load 701919 all-language examples from commitpackft.all.jsonl.zst\n", "There are 344760 / 701919 examples in total.\n"]}], "source": ["# Load the CommitPackFT data\n", "import tqdm\n", "from research.core import utils_for_dataclass\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits import data_type\n", "\n", "all_raw_edit_from_cpft = utils_for_file.read_jsonl_zst(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.processed/commitpackft.all.jsonl.zst\"\n", ")\n", "print(f\"Load {len(all_raw_edit_from_cpft)} examples from commitpackft.all.jsonl.zst\")\n", "temp_cpft_edit_from_cpft = []\n", "for x in tqdm.tqdm(all_raw_edit_from_cpft, total=len(all_raw_edit_from_cpft)):\n", "    if detect_merge_conflict(x[\"old_file_content\"]):\n", "        continue\n", "    if detect_merge_conflict(x[\"new_file_content\"]):\n", "        continue\n", "    x = utils_for_dataclass.create_from_dict(data_type.EditData, x)\n", "    temp_cpft_edit_from_cpft.append(x)\n", "print(\n", "    f\"Load {len(temp_cpft_edit_from_cpft)} all-language examples from commitpackft.all.jsonl.zst\"\n", ")\n", "\n", "# Filter out for the balanced sampling.\n", "max_per_diff: dict[tuple[int, int], int] = dict()\n", "for j in range(0, 41):\n", "    max_per_diff[(0, j)] = 2000\n", "for i in range(1, 11):\n", "    for j in range(0, 41):\n", "        max_per_diff[(i, j)] = 4000\n", "for i in range(11, 31):\n", "    for j in range(0, 41):\n", "        max_per_diff[(i, j)] = 2000\n", "for i in range(31, 81):\n", "    for j in range(0, 101):\n", "        max_per_diff[(i, j)] = 100\n", "\n", "commitpack = keep_by_diff_range(temp_cpft_edit_from_cpft, max_per_diff)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Therea are 349550 / 370527 examples\n", "  0  12: 1818\n", "  0   1: 1978\n", "  0  28: 1640\n", "  0   3: 2020\n", "  0   5: 2029\n", "  0   4: 2021\n", "  0   2: 2013\n", "  0   9: 1794\n", "  0  14: 1829\n", "  0  11: 1816\n", "  0   7: 1981\n", "  0   6: 2030\n", "  0  36: 1259\n", "  0  15: 1884\n", "  0   8: 2016\n", "  0  18: 1894\n", "  0  21: 1941\n", "  0  24: 1875\n", "  0  10: 1761\n", "  0  40: 1145\n", "  0  16: 1863\n", "  0  17: 1920\n", "  0  23: 1788\n", "  0  35: 1320\n", "  0  39: 1160\n", "  0  33: 1443\n", "  0  19: 1914\n", "  0  29: 1500\n", "  0  13: 1868\n", "  0  20: 1934\n", "  0  37: 1152\n", "  0  34: 1369\n", "  0  27: 1687\n", "  0  26: 1733\n", "  0  25: 1726\n", "  0  22: 1948\n", "  0  30: 1533\n", "  0  31: 1475\n", "  0  32: 1462\n", "  0  38: 1128\n", "  1   2: 4027\n", "  1   3: 3910\n", "  1   1: 3936\n", "  1   5: 3022\n", "  1   0: 4014\n", "  1  13:  301\n", "  1   4: 3687\n", "  1  16:  181\n", "  1   9:  712\n", "  1   7: 1355\n", "  1  26:   84\n", "  1  10:  511\n", "  1  11:  395\n", "  1   6: 1862\n", "  1  31:   70\n", "  1  24:   96\n", "  1  15:  205\n", "  1  19:  139\n", "  1  14:  187\n", "  1   8:  942\n", "  1  21:  105\n", "  1  39:   56\n", "  1  28:   71\n", "  1  20:  121\n", "  1  12:  304\n", "  1  18:  128\n", "  1  17:  173\n", "  1  35:   66\n", "  1  23:   97\n", "  1  25:   99\n", "  1  33:   65\n", "  1  40:   56\n", "  1  36:   57\n", "  1  34:   59\n", "  1  22:  108\n", "  1  30:   75\n", "  1  38:   64\n", "  1  27:   80\n", "  1  32:   64\n", "  1  29:   75\n", "  1  37:   62\n", "  2   3: 3045\n", "  2   2: 4023\n", "  2   4: 2248\n", "  2  10:  370\n", "  2  11:  280\n", "  2   1: 3783\n", "  2  13:  204\n", "  2  17:  110\n", "  2   5: 1378\n", "  2   0: 3787\n", "  2   6: 1201\n", "  2  27:   67\n", "  2  19:   97\n", "  2   7:  798\n", "  2  23:   74\n", "  2   8:  624\n", "  2  12:  248\n", "  2  34:   50\n", "  2  32:   60\n", "  2  15:  138\n", "  2  40:   47\n", "  2   9:  429\n", "  2  38:   52\n", "  2  33:   54\n", "  2  21:   87\n", "  2  18:  106\n", "  2  22:   91\n", "  2  30:   64\n", "  2  29:   56\n", "  2  16:  137\n", "  2  28:   61\n", "  2  14:  167\n", "  2  26:   68\n", "  2  20:  101\n", "  2  31:   54\n", "  2  24:   58\n", "  2  39:   47\n", "  2  35:   51\n", "  2  25:   61\n", "  2  36:   50\n", "  2  37:   48\n", "  3   7:  908\n", "  3   3: 4025\n", "  3   1: 1956\n", "  3   4: 2148\n", "  3  11:  342\n", "  3   0: 2236\n", "  3   5: 1753\n", "  3   9:  537\n", "  3   2: 1549\n", "  3  14:  188\n", "  3  15:  152\n", "  3  32:   45\n", "  3   8:  661\n", "  3   6: 1061\n", "  3  10:  385\n", "  3  12:  267\n", "  3  18:  120\n", "  3  23:   78\n", "  3  26:   67\n", "  3  13:  236\n", "  3  28:   66\n", "  3  16:  151\n", "  3  21:   77\n", "  3  20:  100\n", "  3  17:  117\n", "  3  38:   52\n", "  3  22:   73\n", "  3  31:   48\n", "  3  39:   49\n", "  3  19:  101\n", "  3  29:   62\n", "  3  30:   66\n", "  3  33:   50\n", "  3  34:   49\n", "  3  27:   61\n", "  3  25:   71\n", "  3  24:   79\n", "  3  36:   51\n", "  3  35:   50\n", "  3  37:   48\n", "  3  40:   48\n", "  4   2:  903\n", "  4   5: 1752\n", "  4   1:  964\n", "  4  13:  291\n", "  4   3: 1159\n", "  4   4: 3763\n", "  4   6: 1497\n", "  4  12:  431\n", "  4   7: 1025\n", "  4   9: 1478\n", "  4  10:  584\n", "  4  11: 1714\n", "  4  20:  108\n", "  4   0: 1737\n", "  4  17:  146\n", "  4  14:  247\n", "  4  23:   86\n", "  4   8: 1048\n", "  4  35:   57\n", "  4  15:  318\n", "  4  21:   94\n", "  4  30:   61\n", "  4  18:  125\n", "  4  16:  179\n", "  4  25:   70\n", "  4  33:   48\n", "  4  32:   51\n", "  4  29:   54\n", "  4  28:   59\n", "  4  31:   59\n", "  4  24:   77\n", "  4  36:   56\n", "  4  27:   60\n", "  4  19:  120\n", "  4  40:   46\n", "  4  39:   47\n", "  4  38:   50\n", "  4  22:   91\n", "  4  34:   60\n", "  4  26:   67\n", "  4  37:   56\n", "  5   8:  841\n", "  5  24:   77\n", "  5   7: 1337\n", "  5  10:  494\n", "  5   5: 3756\n", "  5  37:   50\n", "  5   2:  464\n", "  5   9:  727\n", "  5   6: 1444\n", "  5   0: 1010\n", "  5  11:  489\n", "  5   4:  910\n", "  5   3:  750\n", "  5  20:  101\n", "  5  14:  247\n", "  5   1:  703\n", "  5  29:   59\n", "  5  13:  313\n", "  5  31:   52\n", "  5  18:  127\n", "  5  17:  168\n", "  5  15:  207\n", "  5  26:   75\n", "  5  23:   80\n", "  5  22:   89\n", "  5  12:  386\n", "  5  19:  109\n", "  5  16:  170\n", "  5  25:   76\n", "  5  39:   49\n", "  5  32:   54\n", "  5  33:   53\n", "  5  36:   48\n", "  5  28:   58\n", "  5  34:   47\n", "  5  38:   48\n", "  5  21:  115\n", "  5  30:   65\n", "  5  35:   56\n", "  5  27:   59\n", "  5  40:   48\n", "  6   6: 3823\n", "  6   9:  702\n", "  6   7: 1202\n", "  6  25:   74\n", "  6  11:  469\n", "  6  17:  164\n", "  6   0:  724\n", "  6   1:  422\n", "  6   8: 1191\n", "  6  18:  160\n", "  6  12:  443\n", "  6  10:  644\n", "  6  13:  304\n", "  6   4:  618\n", "  6  19:  124\n", "  6   5:  765\n", "  6  22:   97\n", "  6  15:  250\n", "  6  28:   62\n", "  6  31:   61\n", "  6  23:   90\n", "  6  16:  228\n", "  6  14:  280\n", "  6   3:  367\n", "  6  33:   56\n", "  6   2:  363\n", "  6  27:   73\n", "  6  21:  101\n", "  6  24:   77\n", "  6  37:   51\n", "  6  36:   48\n", "  6  30:   67\n", "  6  34:   54\n", "  6  20:  117\n", "  6  26:   83\n", "  6  32:   51\n", "  6  29:   60\n", "  6  40:   48\n", "  6  38:   60\n", "  6  35:   54\n", "  6  39:   45\n", "  7   7: 3595\n", "  7  19:  161\n", "  7   9:  965\n", "  7   5:  540\n", "  7  11:  547\n", "  7   8: 1094\n", "  7  20:  112\n", "  7  18:  152\n", "  7  13:  377\n", "  7  26:   77\n", "  7   6:  598\n", "  7  22:  100\n", "  7   3:  306\n", "  7  17:  190\n", "  7  10:  603\n", "  7   2:  215\n", "  7  15:  270\n", "  7  39:   55\n", "  7  33:   59\n", "  7  12:  467\n", "  7   4:  430\n", "  7   1:  309\n", "  7  23:   93\n", "  7   0:  480\n", "  7  24:   95\n", "  7  16:  208\n", "  7  25:   87\n", "  7  14:  299\n", "  7  32:   63\n", "  7  31:   68\n", "  7  37:   52\n", "  7  21:  121\n", "  7  35:   54\n", "  7  29:   65\n", "  7  40:   50\n", "  7  36:   55\n", "  7  28:   74\n", "  7  30:   61\n", "  7  27:   87\n", "  7  38:   51\n", "  7  34:   56\n", "  8  15:  263\n", "  8   6:  455\n", "  8  29:   68\n", "  8  14:  355\n", "  8   5:  283\n", "  8   3:  196\n", "  8   7:  540\n", "  8   8: 2913\n", "  8  10:  900\n", "  8   4:  422\n", "  8  34:   55\n", "  8   9:  932\n", "  8  13:  447\n", "  8   0:  478\n", "  8  11:  596\n", "  8   2:  186\n", "  8  24:   92\n", "  8  20:  136\n", "  8  12:  510\n", "  8   1:  216\n", "  8  28:   56\n", "  8  37:   51\n", "  8  25:   97\n", "  8  18:  202\n", "  8  19:  169\n", "  8  16:  247\n", "  8  21:  122\n", "  8  27:   71\n", "  8  39:   49\n", "  8  22:  123\n", "  8  17:  224\n", "  8  26:   83\n", "  8  40:   52\n", "  8  31:   61\n", "  8  33:   55\n", "  8  32:   58\n", "  8  35:   53\n", "  8  23:  105\n", "  8  36:   50\n", "  8  38:   50\n", "  8  30:   69\n", "  9  23:  109\n", "  9  15:  351\n", "  9   0:  320\n", "  9   9: 2767\n", "  9  12:  520\n", "  9   5:  248\n", "  9   7:  432\n", "  9  10:  861\n", "  9   8:  499\n", "  9   6:  259\n", "  9   4:  163\n", "  9  11:  923\n", "  9  14:  356\n", "  9  13:  516\n", "  9   2:  117\n", "  9  18:  236\n", "  9   3:  156\n", "  9  21:  128\n", "  9   1:  161\n", "  9  16:  294\n", "  9  25:   90\n", "  9  20:  140\n", "  9  31:   59\n", "  9  36:   55\n", "  9  24:  106\n", "  9  37:   52\n", "  9  38:   56\n", "  9  19:  152\n", "  9  17:  238\n", "  9  32:   66\n", "  9  27:   77\n", "  9  28:   80\n", "  9  26:  103\n", "  9  22:  121\n", "  9  29:   85\n", "  9  34:   58\n", "  9  33:   64\n", "  9  39:   54\n", "  9  30:   66\n", "  9  35:   67\n", "  9  40:   49\n", " 10  10: 2311\n", " 10   2:  114\n", " 10   4:  154\n", " 10   6:  224\n", " 10  11:  806\n", " 10   1:  138\n", " 10   9:  480\n", " 10   8:  363\n", " 10  13:  482\n", " 10  16:  290\n", " 10  17:  234\n", " 10  14:  454\n", " 10  15:  327\n", " 10  12:  773\n", " 10  25:  109\n", " 10   0:  291\n", " 10   5:  160\n", " 10  20:  167\n", " 10  22:  134\n", " 10   3:  131\n", " 10  23:  111\n", " 10  19:  163\n", " 10  24:  108\n", " 10  18:  219\n", " 10  32:   69\n", " 10  34:   62\n", " 10  36:   56\n", " 10  30:   73\n", " 10   7:  248\n", " 10  28:   81\n", " 10  29:   75\n", " 10  21:  144\n", " 10  40:   57\n", " 10  38:   58\n", " 10  31:   67\n", " 10  27:   77\n", " 10  26:   93\n", " 10  35:   58\n", " 10  39:   51\n", " 10  33:   61\n", " 10  37:   55\n", " 19  23:  206\n", " 11  11: 1890\n", " 17  20:  252\n", " 13  13: 1714\n", " 14  13:  297\n", " 19  19:  912\n", " 14   5:   53\n", " 16  16: 1163\n", " 14  14: 1402\n", " 13  27:   79\n", " 14  29:   60\n", " 17  13:  113\n", " 13  11:  260\n", " 13  35:   32\n", " 13  21:  142\n", " 15  13:  231\n", " 13  29:   58\n", " 14  16:  521\n", " 13   3:   55\n", " 20  20:  807\n", " 15  18:  310\n", " 13   2:   40\n", " 19  31:   64\n", " 13  12:  305\n", " 11   9:  321\n", " 12  11:  354\n", " 11  19:  177\n", " 19  18:  193\n", " 13  17:  333\n", " 12   8:  164\n", " 12  15:  407\n", " 11  17:  259\n", " 18  18:  932\n", " 12   5:   71\n", " 12  22:  143\n", " 13  18:  257\n", " 16   0:   89\n", " 13   7:   92\n", " 19  22:  243\n", " 12  14:  548\n", " 19  32:   63\n", " 12  21:  133\n", " 17  29:   71\n", " 14  17:  338\n", " 18  33:   44\n", " 15  23:  156\n", " 15  15: 1250\n", " 16  17:  596\n", " 17  22:  203\n", " 17  21:  267\n", " 17   8:   51\n", " 12  12: 1633\n", " 12  36:   24\n", " 16  10:   77\n", " 17   4:   21\n", " 13  16:  354\n", " 14  18:  344\n", " 15  20:  186\n", " 11  28:   51\n", " 11  24:   90\n", " 12   4:   87\n", " 14  10:  141\n", " 15  25:  121\n", " 15   4:   31\n", " 14   4:   39\n", " 11  13:  662\n", " 14   0:  126\n", " 19   0:   36\n", " 13  15:  559\n", " 11  12:  685\n", " 13  28:   75\n", " 16  26:  103\n", " 17  18:  568\n", " 18  23:  187\n", " 17  11:   65\n", " 13  36:   29\n", " 15  16:  669\n", " 13  19:  232\n", " 18  15:  124\n", " 12  18:  247\n", " 18  13:   78\n", " 17  27:  113\n", " 14  25:   99\n", " 15  19:  288\n", " 13  24:   94\n", " 17  14:  126\n", " 13  10:  158\n", " 19  37:   38\n", " 15   6:   38\n", " 16  11:   85\n", " 19  20:  544\n", " 17  23:  182\n", " 12   0:  152\n", " 18  40:   30\n", " 14  35:   38\n", " 17  30:   72\n", " 18  14:  105\n", " 11   0:  161\n", " 17  12:   92\n", " 14   6:   59\n", " 19  24:  217\n", " 15  11:  122\n", " 20  26:  171\n", " 14  15:  625\n", " 17  17: 1080\n", " 12  27:   62\n", " 14  12:  248\n", " 20  33:   74\n", " 14  20:  209\n", " 13   4:   49\n", " 17  35:   44\n", " 20  24:  220\n", " 19  29:   88\n", " 17  19:  374\n", " 20  30:   95\n", " 12   7:   85\n", " 18  22:  280\n", " 12  10:  273\n", " 15  27:   92\n", " 11  18:  182\n", " 15  30:   68\n", " 14  37:   32\n", " 15   1:   43\n", " 12   9:  198\n", " 16  18:  418\n", " 17   2:   18\n", " 11   1:   90\n", " 14  27:   69\n", " 19  10:   42\n", " 13  14:  655\n", " 13  25:  116\n", " 14  22:  150\n", " 17   3:   25\n", " 15  35:   39\n", " 18  24:  165\n", " 11  16:  297\n", " 15  22:  163\n", " 15  12:  161\n", " 18  11:   55\n", " 13  23:  112\n", " 16  20:  294\n", " 15   7:   59\n", " 12  39:   24\n", " 18  17:  236\n", " 16  31:   62\n", " 16  22:  207\n", " 11  15:  383\n", " 16  19:  293\n", " 15  14:  284\n", " 15   3:   37\n", " 11  32:   45\n", " 15  21:  213\n", " 16  25:  141\n", " 11   7:  186\n", " 11  14:  427\n", " 16   5:   42\n", " 11  10:  361\n", " 20   7:   23\n", " 19  15:   97\n", " 16  28:   78\n", " 18  20:  368\n", " 15  24:  142\n", " 14  28:   62\n", " 15   8:   63\n", " 15  38:   35\n", " 12  17:  261\n", " 15  17:  452\n", " 18  30:   75\n", " 14  11:  153\n", " 16  39:   32\n", " 17  24:  141\n", " 11  23:   97\n", " 16  40:   34\n", " 18   0:   66\n", " 16  24:  151\n", " 17  25:  134\n", " 11  38:   31\n", " 16  38:   31\n", " 14  19:  235\n", " 11   8:  196\n", " 19  12:   53\n", " 19   2:   20\n", " 11  20:  139\n", " 15   9:   85\n", " 13   8:   98\n", " 20   5:   24\n", " 17  37:   32\n", " 19   7:   41\n", " 16  13:  128\n", " 18  21:  269\n", " 18  19:  573\n", " 18  27:  109\n", " 17  10:   57\n", " 11   6:  117\n", " 20  19:  202\n", " 14   9:   96\n", " 20  22:  339\n", " 11  26:   84\n", " 12  16:  355\n", " 19  16:  116\n", " 20  23:  253\n", " 14  36:   42\n", " 17   1:   54\n", " 16  29:   70\n", " 20   8:   28\n", " 16  34:   37\n", " 16   8:   57\n", " 12  19:  209\n", " 12  13:  683\n", " 13   9:  137\n", " 16  33:   44\n", " 14  31:   54\n", " 11  25:   80\n", " 14  30:   51\n", " 19  13:   67\n", " 19  39:   35\n", " 20  40:   41\n", " 17  16:  253\n", " 18  35:   48\n", " 17  15:  164\n", " 20  15:   75\n", " 12   2:   38\n", " 16   9:   71\n", " 14  38:   30\n", " 20  21:  536\n", " 11   3:   75\n", " 19  14:   84\n", " 19  25:  178\n", " 16   2:   29\n", " 14  33:   41\n", " 14  21:  175\n", " 19  21:  324\n", " 11  30:   44\n", " 18  37:   38\n", " 18   9:   53\n", " 17  36:   44\n", " 12   1:   68\n", " 14   7:   65\n", " 20  18:  136\n", " 12   6:   98\n", " 13  30:   58\n", " 13   5:   75\n", " 15   0:   87\n", " 19  34:   48\n", " 19  11:   69\n", " 18  12:   87\n", " 15  33:   52\n", " 11   4:   72\n", " 19  26:  146\n", " 17   7:   48\n", " 18  25:  146\n", " 17   0:   52\n", " 17  26:  102\n", " 14   8:   91\n", " 20   4:   20\n", " 18  26:  134\n", " 12  28:   58\n", " 19  17:  147\n", " 17  31:   75\n", " 12  30:   56\n", " 11  29:   44\n", " 17  40:   26\n", " 12  20:  169\n", " 11  21:  154\n", " 13  34:   32\n", " 12  23:  110\n", " 13  37:   24\n", " 14   3:   37\n", " 14  39:   29\n", " 20  34:   52\n", " 12  24:  101\n", " 18  29:   76\n", " 12   3:   59\n", " 13  20:  156\n", " 18  16:  177\n", " 16  12:  128\n", " 20  17:  125\n", " 15  26:   75\n", " 16   1:   36\n", " 11  35:   26\n", " 11  22:  119\n", " 12  26:   84\n", " 14  23:  129\n", " 15   5:   49\n", " 11  33:   29\n", " 16  15:  271\n", " 17  34:   51\n", " 16  21:  243\n", " 13  31:   41\n", " 13  39:   26\n", " 13   0:  120\n", " 11  37:   32\n", " 20  25:  196\n", " 18  34:   51\n", " 20  14:   65\n", " 16   4:   31\n", " 16   6:   45\n", " 19  30:   81\n", " 15   2:   30\n", " 20  36:   58\n", " 20  31:   64\n", " 11  27:   69\n", " 15  32:   49\n", " 18   2:   25\n", " 15  10:   85\n", " 11  40:   26\n", " 19   4:   27\n", " 19   8:   34\n", " 15  28:   72\n", " 12  25:   71\n", " 18  28:  106\n", " 16  14:  192\n", " 14   2:   34\n", " 13   6:   66\n", " 20  16:   95\n", " 17  39:   25\n", " 18  38:   39\n", " 20  10:   27\n", " 14  24:  126\n", " 19  35:   48\n", " 12  29:   58\n", " 16  23:  153\n", " 15  36:   37\n", " 18  10:   69\n", " 11   2:   44\n", " 14  40:   24\n", " 19  28:  112\n", " 20   3:   25\n", " 16  30:   64\n", " 12  33:   36\n", " 20  12:   48\n", " 18  31:   88\n", " 19  38:   45\n", " 20  13:   50\n", " 13  33:   51\n", " 11  39:   28\n", " 16  27:   82\n", " 20   0:   45\n", " 19   1:   23\n", " 11  31:   45\n", " 15  37:   32\n", " 17   6:   34\n", " 12  32:   41\n", " 20  39:   39\n", " 13  40:   19\n", " 19  33:   69\n", " 20  28:  119\n", " 16  35:   39\n", " 18   8:   43\n", " 14   1:   52\n", " 13  26:   76\n", " 20  27:  123\n", " 13   1:   69\n", " 16   3:   34\n", " 19   3:   19\n", " 20  37:   39\n", " 14  32:   49\n", " 18   6:   32\n", " 12  31:   39\n", " 17  32:   59\n", " 15  31:   63\n", " 11   5:   96\n", " 20  32:   74\n", " 20   9:   39\n", " 14  34:   24\n", " 17   5:   38\n", " 17   9:   51\n", " 18   1:   26\n", " 12  35:   37\n", " 13  22:  121\n", " 14  26:   94\n", " 18   5:   22\n", " 20   1:   23\n", " 16   7:   51\n", " 20   6:   22\n", " 17  33:   50\n", " 17  28:   77\n", " 18  32:   56\n", " 20  29:  106\n", " 19  27:  114\n", " 12  40:   22\n", " 20   2:   16\n", " 19   6:   26\n", " 20  11:   31\n", " 12  34:   39\n", " 18   3:   26\n", " 19  36:   42\n", " 15  29:   61\n", " 18  36:   41\n", " 11  34:   41\n", " 15  40:   25\n", " 11  36:   27\n", " 18   4:   17\n", " 16  37:   28\n", " 15  34:   35\n", " 12  37:   27\n", " 18  39:   32\n", " 18   7:   34\n", " 19  40:   34\n", " 20  35:   55\n", " 19   9:   40\n", " 16  32:   60\n", " 13  38:   33\n", " 13  32:   34\n", " 15  39:   34\n", " 19   5:   34\n", " 16  36:   41\n", " 17  38:   38\n", " 12  38:   26\n", " 20  38:   37\n", " 25  20:   60\n", " 26  17:   31\n", " 22  22:  645\n", " 21  21:  749\n", " 24  14:   34\n", " 22  24:  259\n", " 23  24:  447\n", " 22  15:   40\n", " 22  19:   89\n", " 21  27:  159\n", " 27  16:   24\n", " 28  24:   62\n", " 21   3:   20\n", " 28  36:   57\n", " 29  31:  137\n", " 25  38:   42\n", " 22  37:   46\n", " 21  12:   46\n", " 27  33:   87\n", " 23  23:  634\n", " 24  32:   88\n", " 21  23:  300\n", " 24  26:  234\n", " 29  32:  113\n", " 29   9:   14\n", " 30  35:   83\n", " 24   7:   19\n", " 23  14:   38\n", " 24  24:  553\n", " 24  12:   25\n", " 22  18:   95\n", " 24  38:   40\n", " 25  32:  109\n", " 21  24:  204\n", " 27  18:   33\n", " 21  28:  131\n", " 26   2:   11\n", " 21  37:   44\n", " 25  17:   39\n", " 24  23:  149\n", " 27  27:  425\n", " 21  17:   97\n", " 25  16:   40\n", " 22  30:  111\n", " 26  11:   20\n", " 24  16:   34\n", " 30  30:  308\n", " 26  35:   63\n", " 28   0:   12\n", " 29  40:   43\n", " 23  18:   60\n", " 25  18:   37\n", " 27  30:  114\n", " 27  34:   88\n", " 22  17:   53\n", " 22  32:   61\n", " 29  18:   20\n", " 21  25:  167\n", " 29  30:  209\n", " 23  25:  261\n", " 29  33:  104\n", " 24  18:   63\n", " 27  23:   54\n", " 22   3:   19\n", " 22   8:   26\n", " 28  19:   21\n", " 25  30:  119\n", " 24  28:  140\n", " 23  33:   63\n", " 26  26:  445\n", " 21  16:   60\n", " 29  13:   16\n", " 21   6:   21\n", " 25  28:  158\n", " 24  31:  103\n", " 22  40:   38\n", " 27  13:   25\n", " 25  27:  209\n", " 23   3:   19\n", " 22  34:   59\n", " 27  20:   42\n", " 25  36:   57\n", " 23  34:   62\n", " 21  22:  468\n", " 24   3:   19\n", " 30  19:   25\n", " 23  17:   72\n", " 23  29:  118\n", " 21  35:   55\n", " 29  29:  360\n", " 29   6:   12\n", " 27  36:   60\n", " 27  29:  186\n", " 30  37:   63\n", " 22  31:   70\n", " 25  19:   37\n", " 26  16:   22\n", " 27  17:   27\n", " 30  29:   86\n", " 26  25:  121\n", " 22   5:   20\n", " 21  30:   98\n", " 26  34:   60\n", " 22  36:   52\n", " 27  15:   21\n", " 26   5:   12\n", " 24  17:   44\n", " 23  31:   87\n", " 22  27:  162\n", " 25   7:   23\n", " 29   0:   17\n", " 28  26:   90\n", " 23  30:   99\n", " 27  25:   75\n", " 22  20:  141\n", " 27  39:   37\n", " 28  22:   39\n", " 22  26:  190\n", " 24  34:   70\n", " 21  29:  127\n", " 28  15:   20\n", " 27  28:  271\n", " 25   0:   23\n", " 26  19:   31\n", " 28  30:  162\n", " 28  38:   41\n", " 26   9:   19\n", " 28  32:  106\n", " 22  21:  172\n", " 24  21:   78\n", " 25  12:   18\n", " 21   9:   23\n", " 30  31:  198\n", " 30  39:   52\n", " 25  33:   80\n", " 25  25:  541\n", " 28  33:   89\n", " 26  36:   52\n", " 22  14:   48\n", " 27  10:   12\n", " 26  14:   29\n", " 24   4:   13\n", " 28  28:  357\n", " 23  28:  152\n", " 25  22:   79\n", " 22  29:   94\n", " 25  31:  106\n", " 27  21:   48\n", " 28  16:   17\n", " 29  12:   20\n", " 24  25:  370\n", " 26  27:  300\n", " 28  35:   74\n", " 25  26:  348\n", " 27   4:   15\n", " 23  10:   25\n", " 25  24:  133\n", " 26  29:  133\n", " 21  15:   54\n", " 29  27:   68\n", " 24   2:   14\n", " 25  13:   33\n", " 26   8:   17\n", " 23  26:  187\n", " 29  11:   17\n", " 26  31:  107\n", " 30  36:   64\n", " 30  18:   31\n", " 30  23:   34\n", " 23  19:   58\n", " 24  22:  110\n", " 23   0:   24\n", " 21  19:  137\n", " 22  35:   51\n", " 25  39:   42\n", " 21  26:  188\n", " 27  11:   26\n", " 26  15:   23\n", " 25   1:   13\n", " 23  21:  125\n", " 27   2:   15\n", " 26   6:   17\n", " 25  10:   21\n", " 22  16:   68\n", " 27  24:   64\n", " 30  40:   45\n", " 27  37:   75\n", " 24  19:   62\n", " 29  34:   78\n", " 22  33:   85\n", " 24  29:  139\n", " 30  28:   75\n", " 30   6:   15\n", " 23  20:   69\n", " 25  37:   51\n", " 26  23:   66\n", " 21  13:   57\n", " 30  12:   16\n", " 24  36:   53\n", " 24  20:   72\n", " 22   9:   22\n", " 28  40:   36\n", " 25  14:   25\n", " 26  37:   57\n", " 25  23:  106\n", " 22   1:   33\n", " 26  30:  128\n", " 23  27:  174\n", " 23  16:   37\n", " 25  34:   74\n", " 27  31:  117\n", " 27   9:   20\n", " 29  25:   54\n", " 26  18:   28\n", " 30   0:   16\n", " 25   2:   15\n", " 22  28:  121\n", " 27   3:   17\n", " 23   8:   19\n", " 25  21:   80\n", " 21  32:   81\n", " 23  11:   24\n", " 28  10:   17\n", " 26  28:  193\n", " 23  22:  152\n", " 22  25:  185\n", " 28  31:  113\n", " 25   9:   25\n", " 21   5:   26\n", " 22   0:   43\n", " 21   8:   28\n", " 21  36:   46\n", " 22  23:  456\n", " 25  11:   22\n", " 30  13:   15\n", " 23  39:   34\n", " 24  30:  133\n", " 23  32:   90\n", " 23   9:   22\n", " 30  34:  110\n", " 23  36:   59\n", " 21  40:   47\n", " 22  11:   37\n", " 21  38:   41\n", " 24  15:   40\n", " 28  34:   97\n", " 28  11:   15\n", " 22   2:   21\n", " 27  32:  125\n", " 22  39:   39\n", " 23  35:   58\n", " 28   3:   20\n", " 23  13:   37\n", " 27  12:   17\n", " 23   4:   21\n", " 30  32:  129\n", " 21  34:   59\n", " 27   0:   25\n", " 24  40:   37\n", " 30  14:   18\n", " 24   6:   15\n", " 24   8:   23\n", " 26  21:   64\n", " 29  22:   37\n", " 21  20:  200\n", " 28  20:   33\n", " 27  19:   29\n", " 26  38:   48\n", " 22  10:   27\n", " 23   5:   18\n", " 24  39:   45\n", " 26  24:  115\n", " 28  29:  227\n", " 21  33:   54\n", " 22   7:   20\n", " 23   2:   17\n", " 28  25:   69\n", " 30   1:   13\n", " 26   1:   15\n", " 23   1:   22\n", " 24   0:   28\n", " 26  12:   21\n", " 26   7:   16\n", " 30  11:   13\n", " 26  20:   44\n", " 21  39:   33\n", " 22   4:   24\n", " 30  25:   34\n", " 28  27:  115\n", " 27  14:   18\n", " 21  14:   48\n", " 26  13:   23\n", " 29  14:   23\n", " 28   1:   19\n", " 25   4:   17\n", " 29  19:   20\n", " 30  16:   19\n", " 29  28:   92\n", " 21   0:   55\n", " 25  29:  154\n", " 27  22:   50\n", " 21  18:  104\n", " 30  22:   27\n", " 29  20:   22\n", " 27   1:   24\n", " 25  35:   72\n", " 22  38:   46\n", " 21   7:   18\n", " 30  21:   31\n", " 27   5:   14\n", " 21  11:   35\n", " 24   1:   21\n", " 27  40:   45\n", " 26  22:   57\n", " 28   4:   12\n", " 26  32:  105\n", " 24  35:   59\n", " 29  24:   39\n", " 28  17:   26\n", " 29  23:   34\n", " 29  21:   26\n", " 29  35:   77\n", " 26  33:   87\n", " 27   8:   19\n", " 21  31:   74\n", " 28  18:   26\n", " 30  15:   19\n", " 28  21:   42\n", " 28  13:   22\n", " 30  38:   62\n", " 24  11:   26\n", " 28   6:   16\n", " 26  40:   37\n", " 28  37:   62\n", " 30  20:   25\n", " 24   9:   20\n", " 22   6:   26\n", " 27  38:   46\n", " 27  26:  127\n", " 22  12:   26\n", " 29  37:   69\n", " 26   3:   12\n", " 23  40:   35\n", " 28   7:   15\n", " 28   5:   15\n", " 28   9:   19\n", " 28  23:   48\n", " 26  10:   20\n", " 28  14:   21\n", " 24  27:  176\n", " 29  39:   55\n", " 30  33:  100\n", " 25   6:   18\n", " 22  13:   37\n", " 24  33:   79\n", " 30   8:   11\n", " 29  26:   60\n", " 23   6:   20\n", " 28   2:   17\n", " 23  12:   30\n", " 30  17:   19\n", " 26   0:   22\n", " 23   7:   18\n", " 30   7:   13\n", " 24  37:   46\n", " 25  40:   35\n", " 23  15:   32\n", " 30  27:   49\n", " 26  39:   34\n", " 29  38:   45\n", " 28  12:   18\n", " 24   5:   22\n", " 27  35:   63\n", " 28  39:   51\n", " 25   5:   17\n", " 25  15:   37\n", " 29  17:   28\n", " 29   2:   13\n", " 29   1:   17\n", " 21   1:   16\n", " 24  13:   36\n", " 29  36:   61\n", " 30   5:   17\n", " 23  37:   36\n", " 21  10:   25\n", " 27   6:   15\n", " 30  24:   39\n", " 25   3:   12\n", " 23  38:   45\n", " 21   4:   17\n", " 30  10:   15\n", " 29   4:   12\n", " 27   7:   15\n", " 30  26:   43\n", " 29  10:   15\n", " 29   5:   12\n", " 29  15:   25\n", " 29   3:   10\n", " 29   8:   14\n", " 30   4:   10\n", " 21   2:   12\n", " 24  10:   27\n", " 29  16:   17\n", " 28   8:   19\n", " 30   9:   11\n", " 26   4:   13\n", " 29   7:   14\n", " 30   2:   13\n", " 30   3:   14\n", " 25   8:   13\n", " 41  30:    6\n", " 34  53:    4\n", " 33  35:   89\n", " 36  38:   45\n", " 38  33:   18\n", " 41  39:   14\n", " 37   2:    4\n", " 31  32:  100\n", " 37  37:   99\n", " 45  18:    3\n", " 32  46:   24\n", " 57  19:    1\n", " 38  38:   94\n", " 31  53:    7\n", " 33  31:   40\n", " 39  39:   93\n", " 57  22:    1\n", " 32  23:   13\n", " 45   0:    8\n", " 33   0:    2\n", " 37  32:   18\n", " 36  25:    9\n", " 32  43:   29\n", " 32  38:   47\n", " 49  54:    8\n", " 44  49:   14\n", " 34  21:    8\n", " 33  28:   32\n", " 37  11:    5\n", " 32  31:   59\n", " 31   0:    8\n", " 34  46:   22\n", " 32  39:   48\n", " 42  43:   36\n", " 37  43:   35\n", " 38  40:   53\n", " 55  55:   16\n", " 32  32:  100\n", " 53  61:    6\n", " 44  47:   16\n", " 37  24:    8\n", " 40  35:   16\n", " 43  32:    4\n", " 34  20:   11\n", " 31  37:   54\n", " 50  51:   13\n", " 63   5:    3\n", " 44  33:    4\n", " 32  30:   47\n", " 33  39:   48\n", " 33  52:    8\n", " 53  41:    6\n", " 45  48:   19\n", " 48  46:   13\n", " 41  31:    8\n", " 33  44:   13\n", " 37  30:   11\n", " 34  32:   32\n", " 44  52:    6\n", " 46  46:   35\n", " 36   8:    2\n", " 32  91:    1\n", " 38  34:   19\n", " 51  56:    9\n", " 36  35:   53\n", " 42  45:   23\n", " 34  39:   47\n", " 34  34:   99\n", " 41   7:    1\n", " 40  42:   40\n", " 34  18:   11\n", " 35  22:    5\n", " 62  51:    1\n", " 33  33:   99\n", " 40   5:    5\n", " 33  42:   33\n", " 31  15:   15\n", " 46  54:   10\n", " 42  48:   13\n", " 49  13:    3\n", " 38  12:    2\n", " 35  43:   28\n", " 51  66:    2\n", " 37  41:   44\n", " 32  42:   19\n", " 41  42:   34\n", " 45  24:    5\n", " 34  41:   37\n", " 31  45:   14\n", " 33  37:   53\n", " 34  26:   15\n", " 52  62:    3\n", " 42  53:    8\n", " 42  13:    3\n", " 31  31:   98\n", " 37  49:   11\n", " 35  39:   63\n", " 53  53:   23\n", " 32  35:   65\n", " 45   1:    1\n", " 37  45:   24\n", " 38  36:   30\n", " 35  32:   32\n", " 32  40:   38\n", " 32  37:   60\n", " 44  50:   14\n", " 35  40:   43\n", " 31  34:   79\n", " 36  37:   77\n", " 53  36:    1\n", " 34  47:   12\n", " 31  12:    5\n", " 31  29:   77\n", " 42  42:   81\n", " 34  36:   78\n", " 48  48:   31\n", " 32  33:   98\n", " 44  45:   28\n", " 41  40:   35\n", " 49  48:   10\n", " 34  27:   17\n", " 46  51:   13\n", " 31  36:   93\n", " 46  53:    4\n", " 34   4:    2\n", " 43  41:   20\n", " 42  47:   16\n", " 43  51:    6\n", " 33  60:    4\n", " 33  26:   21\n", " 39  48:   17\n", " 42  17:   10\n", " 37  44:   27\n", " 47  63:    3\n", " 37  36:   59\n", " 53  34:    2\n", " 42  49:   15\n", " 55  57:    6\n", " 38  70:    2\n", " 41   5:    2\n", " 35  29:   26\n", " 38  39:   59\n", " 31  38:   48\n", " 44  44:   77\n", " 33  41:   33\n", " 49  47:   14\n", " 44  20:    1\n", " 31  40:   37\n", " 37  47:   13\n", " 53  51:    5\n", " 35  38:   54\n", " 40  40:   92\n", " 39  41:   46\n", " 41  45:   21\n", " 31  42:   31\n", " 46  43:    7\n", " 33   6:    2\n", " 51  67:    1\n", " 34  33:   50\n", " 40  41:   34\n", " 32  11:    4\n", " 50  76:    1\n", " 42   2:    2\n", " 32  36:   49\n", " 31   7:    2\n", " 43  43:   50\n", " 39  42:   37\n", " 38  61:    3\n", " 63  12:    1\n", " 38  48:   17\n", " 35  18:    6\n", " 38  50:    9\n", " 44  32:    7\n", " 33  40:   36\n", " 35  35:   95\n", " 56  47:    2\n", " 33  43:   20\n", " 40  52:   14\n", " 34  37:   69\n", " 45  28:    3\n", " 52  46:    3\n", " 32   1:    8\n", " 73  69:    1\n", " 45  40:    5\n", " 39  45:   29\n", " 49   7:    1\n", " 31  11:   10\n", " 56  43:    2\n", " 48  12:    1\n", " 56  36:    1\n", " 45  17:    1\n", " 31  43:   29\n", " 47  49:   14\n", " 50   3:    1\n", " 33  20:   15\n", " 37  21:    5\n", " 44  22:    2\n", " 36  45:   23\n", " 41  41:   96\n", " 57  24:    2\n", " 55  61:    4\n", " 42  44:   34\n", " 39  55:    8\n", " 47  48:   20\n", " 33  38:   51\n", " 31  44:   27\n", " 32  45:   19\n", " 37  39:   51\n", " 34  17:    4\n", " 38   5:    6\n", " 76  77:    1\n", " 45  45:   53\n", " 57  65:    2\n", " 44  48:   15\n", " 46  20:    2\n", " 46  49:   10\n", " 32  29:   25\n", " 33  27:   24\n", " 53  60:    1\n", " 36  32:   19\n", " 43  50:   10\n", " 49  35:    3\n", " 39  25:   11\n", " 34  43:   30\n", " 45  55:    6\n", " 50  48:    3\n", " 39  12:    4\n", " 40  30:    5\n", " 41  44:   30\n", " 54  64:    2\n", " 31  21:   16\n", " 43  16:    6\n", " 41  21:    2\n", " 45  41:    8\n", " 35  37:   74\n", " 42  51:   19\n", " 51  53:    6\n", " 40  17:    2\n", " 52  22:    2\n", " 45  53:   11\n", " 46  59:    2\n", " 31  33:   95\n", " 50  50:   24\n", " 45  29:    6\n", " 37  33:   15\n", " 33  36:   78\n", " 41  43:   43\n", " 39   1:    3\n", " 36   9:    5\n", " 34  35:   93\n", " 40  39:   30\n", " 39  68:    2\n", " 38  13:    2\n", " 37  55:    9\n", " 36  62:    6\n", " 32  59:    2\n", " 35  31:   28\n", " 36  41:   47\n", " 31  27:   36\n", " 40  44:   28\n", " 44  68:    1\n", " 53   4:    1\n", " 33  34:   97\n", " 35  30:   20\n", " 42  41:   18\n", " 36  36:   92\n", " 59  59:    6\n", " 32  49:   20\n", " 47  47:   34\n", " 56   1:    6\n", " 44  42:   11\n", " 31  28:   45\n", " 32  24:   16\n", " 38  46:   16\n", " 61  66:    3\n", " 35  34:   58\n", " 41  50:   17\n", " 42  37:    8\n", " 42  38:   10\n", " 42   1:    8\n", " 45  36:    7\n", " 38  30:   15\n", " 32  34:   93\n", " 48  45:   12\n", " 67  24:    2\n", " 45  59:    3\n", " 53  40:    3\n", " 33  32:   65\n", " 34  31:   35\n", " 43  45:   32\n", " 60   2:    2\n", " 37  34:   28\n", " 51  16:    1\n", " 41   2:    2\n", " 35  27:   14\n", " 31  24:   21\n", " 39  26:   11\n", " 70   1:    2\n", " 74  76:    1\n", " 37  38:   66\n", " 48  51:   19\n", " 46  47:   20\n", " 47  55:    8\n", " 35  45:   28\n", " 34  30:   29\n", " 40  49:   11\n", " 53  59:    4\n", " 43  19:    2\n", " 38  42:   26\n", " 38  37:   30\n", " 47  37:    6\n", " 36  39:   44\n", " 47  53:   12\n", " 35  36:   90\n", " 36  44:   23\n", " 40  38:   23\n", " 48  53:    8\n", " 34   2:    4\n", " 31  16:    7\n", " 52  56:    8\n", " 57  57:   11\n", " 51  47:    4\n", " 45  52:    8\n", " 43  40:   11\n", " 52  52:   15\n", " 42  39:   20\n", " 47  43:    9\n", " 39  40:   57\n", " 33  30:   29\n", " 32   0:   10\n", " 58  51:    2\n", " 39  29:    8\n", " 54  62:    1\n", " 41  53:    9\n", " 40  43:   16\n", " 43  35:    5\n", " 40  46:   26\n", " 45   9:    2\n", " 37  64:    2\n", " 53  27:    2\n", " 35  50:    9\n", " 37  40:   43\n", " 36  33:   22\n", " 40  45:   24\n", " 32  28:   31\n", " 42  27:    4\n", " 62  78:    3\n", " 46  40:    5\n", " 49  49:   23\n", " 41  38:   17\n", " 43  53:    8\n", " 56  12:    1\n", " 40  61:    5\n", " 37  46:   11\n", " 34  45:   21\n", " 38  54:   10\n", " 32  12:    5\n", " 37  31:   18\n", " 39  38:   31\n", " 39  43:   31\n", " 34  42:   30\n", " 38  26:   12\n", " 31  49:   15\n", " 38  43:   27\n", " 40  23:    4\n", " 38  44:   29\n", " 34   8:    5\n", " 33  47:   12\n", " 39  46:   23\n", " 42  36:   13\n", " 40  26:    7\n", " 32  54:    7\n", " 43  14:    2\n", " 53  56:    3\n", " 43  42:   27\n", " 48  56:    6\n", " 79  82:    1\n", " 32  50:    7\n", " 36  59:    7\n", " 54  61:    3\n", " 49  50:   15\n", " 52  28:    2\n", " 40  25:    2\n", " 56  56:    9\n", " 41  28:    8\n", " 49   1:    4\n", " 34  38:   52\n", " 36  34:   32\n", " 39  18:    4\n", " 44  28:    6\n", " 40  24:    5\n", " 53  24:    1\n", " 42   6:    2\n", " 32  44:   24\n", " 39  67:    1\n", " 36  30:   22\n", " 50  13:    1\n", " 34   3:    5\n", " 46  35:    7\n", " 38  15:    4\n", " 36  47:   24\n", " 67  69:    1\n", " 56  57:    3\n", " 31  30:   73\n", " 54  53:    9\n", " 34  40:   28\n", " 43  54:    9\n", " 34  23:   13\n", " 44  26:    4\n", " 31   2:    3\n", " 45  38:    3\n", " 33  19:    7\n", " 38  52:   16\n", " 59  35:    2\n", " 33  21:   15\n", " 31  26:   21\n", " 38  21:    5\n", " 40  22:    3\n", " 48   4:    2\n", " 49   3:    1\n", " 39  23:    6\n", " 37   5:    2\n", " 34  25:   13\n", " 33  24:   23\n", " 31  23:   26\n", " 35  17:    4\n", " 45  33:    4\n", " 49  34:    3\n", " 38  22:    5\n", " 32  25:   19\n", " 43  48:   13\n", " 38  24:    7\n", " 45  10:    1\n", " 32  15:    7\n", " 39  24:    8\n", " 50  35:    3\n", " 38   1:    4\n", " 41   1:    4\n", " 48   0:    2\n", " 39   0:    3\n", " 77   3:    1\n", " 41  49:   10\n", " 44  21:    5\n", " 38  77:    4\n", " 37  53:    7\n", " 46  62:    6\n", " 72  38:    1\n", " 42  57:    3\n", " 41  26:    5\n", " 47  46:   13\n", " 37  29:   19\n", " 38  45:   34\n", " 51  17:    2\n", " 31  41:   29\n", " 55  59:    3\n", " 41  55:    8\n", " 61  61:    3\n", " 35  44:   18\n", " 59  50:    5\n", " 33  29:   35\n", " 42  60:    2\n", " 44  24:    5\n", " 43  29:    6\n", " 32  22:   11\n", " 36  53:    9\n", " 43  62:    3\n", " 31   4:    3\n", " 34  14:    3\n", " 32  48:   15\n", " 63  64:    1\n", " 47  69:    2\n", " 51  51:   20\n", " 44  36:    3\n", " 44  76:    1\n", " 36  55:    7\n", " 44  46:   35\n", " 33  10:    5\n", " 63  42:    1\n", " 67  28:    1\n", " 35  56:    5\n", " 73  57:    1\n", " 55  64:    3\n", " 37  59:    5\n", " 31  19:   17\n", " 32  41:   19\n", " 33   2:    3\n", " 35  46:   25\n", " 36   6:    2\n", " 56  54:    1\n", " 36  49:   11\n", " 34  50:    8\n", " 61   1:    1\n", " 38  28:   10\n", " 32  19:    7\n", " 39  36:   23\n", " 51  46:    4\n", " 46  21:    1\n", " 34  61:    3\n", " 77  35:    1\n", " 35   1:    4\n", " 42  14:    1\n", " 58  24:    1\n", " 31  25:   24\n", " 42  46:   25\n", " 40  32:   15\n", " 38  69:    3\n", " 67  81:    1\n", " 51  45:    7\n", " 32  52:   14\n", " 33  25:   16\n", " 48  21:    1\n", " 37  54:    9\n", " 35   9:    3\n", " 33  71:    1\n", " 47  13:    1\n", " 54  42:    7\n", " 59  13:    1\n", " 35  49:    9\n", " 50  60:    3\n", " 41  47:   19\n", " 34  52:    7\n", " 34  29:   24\n", " 41  36:   16\n", " 37   8:    3\n", " 35  41:   45\n", " 36  21:    7\n", " 48  50:    7\n", " 49  22:    2\n", " 46  30:    2\n", " 51  63:    1\n", " 46  48:   29\n", " 37  56:    2\n", " 39  44:   23\n", " 31  52:   11\n", " 33  45:   24\n", " 33  17:    4\n", " 32  17:    9\n", " 44  19:    4\n", " 57  55:    2\n", " 31  35:   78\n", " 31  46:   15\n", " 44  57:    3\n", " 32  68:    2\n", " 47  64:    3\n", " 44  66:    4\n", " 32   6:    8\n", " 60  54:    2\n", " 38  18:    4\n", " 31   5:    4\n", " 44  18:    4\n", " 46   0:    6\n", " 32  26:   24\n", " 41  32:   13\n", " 35  33:   35\n", " 43  49:   14\n", " 49  30:    1\n", " 36  40:   42\n", " 46  73:    1\n", " 46  61:    2\n", " 36  67:    5\n", " 37  25:   13\n", " 49  58:    5\n", " 49  51:   14\n", " 40  85:    1\n", " 51  59:    4\n", " 53  54:    7\n", " 57  62:    4\n", " 56  55:    5\n", " 61  41:    1\n", " 48  52:    9\n", " 63  84:    1\n", " 43  73:    1\n", " 37  28:   13\n", " 44  40:    9\n", " 45  71:    2\n", " 51  50:    7\n", " 52  54:    8\n", " 33  63:    1\n", " 59  51:    1\n", " 56  61:    2\n", " 72  58:    1\n", " 39  70:    2\n", " 48  49:   11\n", " 40  36:    8\n", " 31  18:    5\n", " 42  76:    1\n", " 54  60:    5\n", " 34  72:    2\n", " 70  69:    1\n", " 53  52:    5\n", " 45  49:    8\n", " 36  48:    9\n", " 58  53:    1\n", " 34  51:    6\n", " 55  58:    2\n", " 50  54:    8\n", " 47  51:    6\n", " 73  29:    1\n", " 37  35:   33\n", " 31  17:   16\n", " 40   0:    6\n", " 36  18:    5\n", " 44   6:    2\n", " 66  14:    1\n", " 69  53:    1\n", " 34  13:    4\n", " 47  40:    5\n", " 34   0:    8\n", " 34  70:    2\n", " 31  14:    5\n", " 48  40:    7\n", " 33  51:   14\n", " 36  11:    2\n", " 76  10:    1\n", " 36   3:    3\n", " 35  51:   10\n", " 46  44:   13\n", " 46  18:    2\n", " 50  38:    3\n", " 31   8:    7\n", " 37   6:    5\n", " 47  19:    2\n", " 36  31:   17\n", " 37   1:    4\n", " 35  20:    7\n", " 38  20:    9\n", " 68   3:    1\n", " 51  39:    4\n", " 39   9:    2\n", " 31  39:   47\n", " 56   0:    1\n", " 47  52:   13\n", " 39   4:    3\n", " 36  50:   16\n", " 36  42:   42\n", " 36   1:    3\n", " 39  14:    4\n", " 49  61:    3\n", " 43  47:   20\n", " 46  15:    2\n", " 31   1:    6\n", " 40  48:   18\n", " 40  57:    3\n", " 40  14:    5\n", " 32  82:    1\n", " 33   7:    3\n", " 36  19:    6\n", " 35  42:   21\n", " 43  33:    5\n", " 38  32:   11\n", " 38  23:    7\n", " 54  34:    1\n", " 77   9:    1\n", " 55   4:    2\n", " 40  47:   20\n", " 31   9:    4\n", " 35  23:   13\n", " 43  46:   16\n", " 40   8:    2\n", " 49  18:    1\n", " 47  35:    2\n", " 32  14:    6\n", " 33   1:    2\n", " 40  10:    1\n", " 45  43:   13\n", " 43  24:    2\n", " 33  66:    1\n", " 36  46:   27\n", " 36  54:   10\n", " 41  14:    3\n", " 43  44:   22\n", " 38   0:    5\n", " 55  43:    1\n", " 56  17:    3\n", " 78  12:    1\n", " 33  14:    6\n", " 39  58:    5\n", " 46  45:   15\n", " 58  60:    5\n", " 33  16:    7\n", " 50  40:    6\n", " 49  56:    5\n", " 40  51:   11\n", " 43   1:    3\n", " 48  20:    4\n", " 48  39:    8\n", " 39  32:   12\n", " 33  48:   11\n", " 57  59:    3\n", " 35  28:   11\n", " 56  63:    2\n", " 31  22:   16\n", " 36  27:   12\n", " 39  50:   16\n", " 39  73:    2\n", " 70  36:    1\n", " 55  54:    3\n", " 49  67:    3\n", " 46  64:    5\n", " 51  68:    1\n", " 49  64:    4\n", " 53  62:    3\n", " 46  55:   11\n", " 35  21:   13\n", " 72  56:    1\n", " 35  73:    2\n", " 46  71:    1\n", " 56  64:    1\n", " 32  55:   11\n", " 44  70:    2\n", " 48  62:    2\n", " 31  64:    4\n", " 41  54:    6\n", " 31  20:   10\n", " 38  11:    3\n", " 42  34:   13\n", " 41  34:   17\n", " 39  34:   11\n", " 47   3:    3\n", " 60   4:    1\n", " 37  26:    5\n", " 33  12:    6\n", " 47  12:    2\n", " 34  49:   13\n", " 36  17:    7\n", " 39  35:   13\n", " 47  20:    2\n", " 36  43:   30\n", " 43  34:    8\n", " 33  46:   24\n", " 44  59:    3\n", " 37   7:    2\n", " 52  60:    5\n", " 35  76:    2\n", " 44  30:    4\n", " 47  58:    3\n", " 42  40:   18\n", " 52  49:    1\n", " 33  56:    6\n", " 54  45:    6\n", " 60  55:    1\n", " 37  17:   10\n", " 37  22:    6\n", " 54  27:    1\n", " 48  58:    2\n", " 44  43:   12\n", " 32  27:   22\n", " 34  28:   14\n", " 53  39:    3\n", " 63   9:    1\n", " 57  15:    2\n", " 37  20:    8\n", " 40  76:    1\n", " 43  52:    5\n", " 41  58:    5\n", " 33  23:   17\n", " 44  41:   11\n", " 36  24:    8\n", " 42  50:   13\n", " 31  55:    9\n", " 54  51:    2\n", " 61  12:    1\n", " 38  14:    2\n", " 37  51:   13\n", " 41   8:    3\n", " 45  44:   15\n", " 42  12:    2\n", " 39  33:   16\n", " 31   3:    4\n", " 46  37:    5\n", " 44  35:    3\n", " 43  27:    6\n", " 41  35:   15\n", " 46  41:    8\n", " 42  59:    3\n", " 35  59:    4\n", " 49  28:    4\n", " 40  53:    6\n", " 49  24:    1\n", " 79   0:    1\n", " 46  36:    5\n", " 31  10:    2\n", " 33   4:    2\n", " 41  33:   12\n", " 38   3:    1\n", " 40  67:    3\n", " 64  63:    1\n", " 37  14:    3\n", " 38  51:    8\n", " 57  34:    2\n", " 41  57:    3\n", " 37  42:   28\n", " 56  62:    4\n", " 56  20:    2\n", " 32   7:    4\n", " 31  56:    7\n", " 50   1:    2\n", " 44  13:    1\n", " 61  17:    1\n", " 31  51:   16\n", " 43   0:    5\n", " 33  57:   10\n", " 39  28:    7\n", " 36  26:    7\n", " 40  28:    6\n", " 41  48:   17\n", " 59  45:    3\n", " 36  51:   11\n", " 51  52:   15\n", " 69  69:    2\n", " 33  62:    2\n", " 58  59:    5\n", " 74  53:    1\n", " 41  23:    5\n", " 45  22:    1\n", " 43   5:    3\n", " 42  64:    1\n", " 49  60:    3\n", " 42  35:    6\n", " 35  52:   10\n", " 49  38:    7\n", " 51  49:    5\n", " 31  54:    6\n", " 50  52:   15\n", " 40  13:    3\n", " 43  13:    2\n", " 37  23:   12\n", " 54  54:   13\n", " 56  66:    1\n", " 41  52:    7\n", " 35  15:    2\n", " 65  32:    2\n", " 42  29:    4\n", " 35  14:    4\n", " 58  67:    1\n", " 41  11:    2\n", " 50   6:    2\n", " 41  15:    4\n", " 33  54:   12\n", " 54  37:    3\n", " 43   2:    2\n", " 64  10:    2\n", " 36  57:    3\n", " 57  48:    2\n", " 38  41:   23\n", " 34  44:   31\n", " 66   8:    1\n", " 55  74:    1\n", " 36  28:   11\n", " 47  31:    9\n", " 44  37:    8\n", " 66  39:    1\n", " 31  13:    4\n", " 57   4:    1\n", " 35  48:   10\n", " 32  20:   14\n", " 39  13:    2\n", " 45  34:    5\n", " 56  23:    1\n", " 39  10:    1\n", " 43  37:    8\n", " 49   9:    1\n", " 54  44:    3\n", " 32  16:    7\n", " 40  31:    8\n", " 44  53:    9\n", " 40  50:   13\n", " 79  78:    1\n", " 59  16:    1\n", " 62  47:    2\n", " 63  38:    2\n", " 31  47:   17\n", " 36  20:    3\n", " 39  37:   22\n", " 38  80:    2\n", " 67   3:    2\n", " 48  28:    3\n", " 34  22:    9\n", " 45  60:    5\n", " 46  31:    3\n", " 66  24:    2\n", " 48  37:    8\n", " 57   0:    1\n", " 61  22:    1\n", " 48  44:    4\n", " 34  24:    9\n", " 55  37:    2\n", " 45  46:   18\n", " 39  47:   17\n", " 42  69:    1\n", " 66  40:    1\n", " 40  37:   14\n", " 48  35:    4\n", " 37  67:    3\n", " 32  18:    7\n", " 47  45:    9\n", " 43  39:   10\n", " 34  48:   16\n", " 58  22:    2\n", " 40  20:    5\n", " 46  42:    8\n", " 46  65:    3\n", " 42  28:    8\n", " 60  17:    2\n", " 47  42:    3\n", " 51  62:    4\n", " 42  31:    7\n", " 61  34:    2\n", " 39  57:    4\n", " 49  52:    6\n", " 34  15:    3\n", " 50   5:    1\n", " 45  31:    7\n", " 54  56:    7\n", " 43  31:    7\n", " 53  32:    1\n", " 46  67:    3\n", " 61   4:    1\n", " 42  30:    3\n", " 47  34:    1\n", " 45  56:    5\n", " 42  58:    8\n", " 45   5:    1\n", " 40  34:   10\n", " 59  26:    1\n", " 70  26:    1\n", " 58  55:    2\n", " 65   0:    1\n", " 47  22:    2\n", " 33  55:    9\n", " 49   5:    1\n", " 38  27:   10\n", " 61  60:    2\n", " 46   5:    4\n", " 45  50:   11\n", " 32  13:    1\n", " 64   3:    1\n", " 41  63:    3\n", " 36  14:    4\n", " 72  19:    1\n", " 36  10:    2\n", " 31  48:    6\n", " 37  52:    8\n", " 42  32:    6\n", " 40  27:    9\n", " 50  34:    2\n", " 32  57:    4\n", " 54  18:    1\n", " 53  65:    1\n", " 46  33:    6\n", " 58  57:    1\n", " 38  31:   17\n", " 71  18:    1\n", " 44   5:    2\n", " 48  38:    3\n", " 62  43:    1\n", " 35  26:   12\n", " 32   4:    2\n", " 31  57:    6\n", " 43  38:    4\n", " 60  60:    5\n", " 48  47:    9\n", " 47  61:    3\n", " 52   1:    3\n", " 34  12:    7\n", " 47  59:    3\n", " 53  49:    6\n", " 60  29:    1\n", " 43  25:    5\n", " 32  53:   10\n", " 47  16:    2\n", " 58  37:    2\n", " 39   5:    3\n", " 41  64:    1\n", " 77  67:    1\n", " 54  47:    3\n", " 59  52:    1\n", " 53  37:    1\n", " 42  22:    2\n", " 62   8:    1\n", " 36  29:   13\n", " 33  50:   10\n", " 52  23:    2\n", " 54  23:    2\n", " 37  19:    6\n", " 48  29:    1\n", " 32   8:    2\n", " 63  32:    1\n", " 39  16:    3\n", " 57   2:    1\n", " 39  49:   10\n", " 45  88:    1\n", " 41  37:   19\n", " 71  63:    1\n", " 55  25:    1\n", " 35  60:    3\n", " 39  19:    4\n", " 49  21:    2\n", " 65  17:    1\n", " 33  13:    9\n", " 60  58:    3\n", " 37   4:    1\n", " 43  58:    6\n", " 66  46:    2\n", " 45  15:    5\n", " 35  24:    8\n", " 42  19:    2\n", " 53  16:    1\n", " 53  55:   12\n", " 32  81:    1\n", " 31  70:    3\n", " 43  22:    2\n", " 36  52:    7\n", " 38  47:   16\n", " 66  74:    1\n", " 47  65:    1\n", " 44  58:    3\n", " 40  60:    6\n", " 57  10:    1\n", " 65  56:    1\n", " 52  51:    5\n", " 40  55:    4\n", " 53  33:    2\n", " 78  75:    2\n", " 32  58:    5\n", " 72  71:    2\n", " 50  79:    1\n", " 59  58:    3\n", " 48  61:    3\n", " 53  35:    2\n", " 59  61:    1\n", " 51  58:    5\n", " 34  65:    4\n", " 51  61:    4\n", " 54  30:    2\n", " 50  72:    1\n", " 63  46:    1\n", " 39  59:    3\n", " 38  49:   18\n", " 39  52:    9\n", " 66  51:    1\n", " 35  81:    1\n", " 66  78:    1\n", " 41  59:    3\n", " 54  49:    2\n", " 42  33:   10\n", " 44  65:    3\n", " 33  58:    3\n", " 33  53:   10\n", " 76   2:    2\n", " 34  69:    4\n", " 44  14:    2\n", " 70   0:    1\n", " 56  25:    1\n", " 42  72:    2\n", " 32  47:   19\n", " 52  59:    6\n", " 37  48:    9\n", " 49  29:    4\n", " 72  73:    1\n", " 47  23:    4\n", " 45  20:    3\n", " 37  60:    2\n", " 36  66:    1\n", " 47  38:    2\n", " 59  40:    1\n", " 36  78:    3\n", " 33  49:   11\n", " 45  16:    2\n", " 40   3:    3\n", " 43  59:    4\n", " 52  53:   12\n", " 45  25:    2\n", " 50  81:    1\n", " 52  70:    3\n", " 48  67:    1\n", " 40  68:    3\n", " 42  55:    8\n", " 54  33:    2\n", " 60  40:    2\n", " 58  16:    1\n", " 56  33:    3\n", " 32  61:    6\n", " 59  24:    2\n", " 47  82:    5\n", " 46  60:    5\n", " 44  62:    2\n", " 42  24:    3\n", " 43  61:    3\n", " 34  68:    4\n", " 57  42:    3\n", " 50  24:    2\n", " 40  56:    2\n", " 59  43:    2\n", " 40  66:    2\n", " 71  93:    1\n", " 38  53:    8\n", " 33  68:    3\n", " 45  42:   16\n", " 45  47:   16\n", " 50  63:    1\n", " 34  60:    1\n", " 31  50:    6\n", " 51  38:    1\n", " 34  66:    2\n", " 61  29:    3\n", " 72  53:    1\n", " 57  35:    1\n", " 35   0:    6\n", " 43  68:    1\n", " 49  11:    1\n", " 51  23:    1\n", " 62  94:    1\n", " 53  71:    1\n", " 59  76:    1\n", " 57  58:    4\n", " 70  20:    1\n", " 45  39:    5\n", " 40  16:    4\n", " 59  42:    2\n", " 60  64:    1\n", " 48  70:    2\n", " 52  25:    1\n", " 60  24:    1\n", " 35  53:    2\n", " 44  55:    9\n", " 37  16:    2\n", " 41  29:    6\n", " 75  80:    2\n", " 57  61:    1\n", " 52  67:    1\n", " 42  52:   11\n", " 47  33:    3\n", " 68 100:    1\n", " 38  68:    4\n", " 59  65:    1\n", " 56  26:    1\n", " 52  39:    2\n", " 60  72:    1\n", " 38  35:   10\n", " 53  69:    1\n", " 62  55:    1\n", " 46  52:   10\n", " 54  48:    1\n", " 39   2:    2\n", " 57  94:    1\n", " 55  52:    2\n", " 56  24:    1\n", " 32  73:    3\n", " 58  69:    1\n", " 38  17:    4\n", " 39  56:    3\n", " 31  63:    1\n", " 55  34:    2\n", " 57  49:    4\n", " 61  62:    2\n", " 45  32:    3\n", " 61  65:    1\n", " 54   0:    3\n", " 42  16:    2\n", " 37  65:    6\n", " 58  58:    7\n", " 38  67:    1\n", " 57  56:    2\n", " 49  57:    4\n", " 60  15:    1\n", " 55  33:    2\n", " 80   2:    2\n", " 49  53:   10\n", " 47  50:    6\n", " 36  16:    3\n", " 48  31:    1\n", " 54  26:    3\n", " 47  57:    4\n", " 46  50:    4\n", " 37  82:    1\n", " 41  24:    7\n", " 53   3:    1\n", " 42  54:   10\n", " 37  62:    2\n", " 59  44:    5\n", " 35   2:    2\n", " 40  65:    4\n", " 61  45:    1\n", " 35  57:    8\n", " 34  11:    4\n", " 35  54:    9\n", " 39  64:    4\n", " 52  58:    2\n", " 45  23:    2\n", " 46  34:    4\n", " 52  57:    7\n", " 80  25:    1\n", " 45  35:    4\n", " 44  29:    2\n", " 40  33:    7\n", " 44  71:    1\n", " 48  27:    2\n", " 51  29:    2\n", " 44  63:    2\n", " 56  31:    3\n", " 38   4:    1\n", " 56  49:    1\n", " 54  39:    1\n", " 33  15:    6\n", " 53  77:    2\n", " 43  55:    8\n", " 53  43:    4\n", " 76  45:    1\n", " 65  10:    3\n", " 47  10:    3\n", " 36  22:    7\n", " 41  51:    7\n", " 66  25:    1\n", " 58  46:    3\n", " 76  32:    1\n", " 42  63:    3\n", " 33   8:    4\n", " 50  68:    4\n", " 63   1:    1\n", " 41  61:    3\n", " 39  71:    1\n", " 34  79:    1\n", " 32  63:    3\n", " 59  38:    1\n", " 73  90:    1\n", " 52  14:    3\n", " 52  76:    1\n", " 54  31:    1\n", " 37   0:    7\n", " 52  68:    2\n", " 45  62:    3\n", " 52  72:    2\n", " 50  22:    4\n", " 58  78:    1\n", " 31  74:    2\n", " 54  35:    2\n", " 40  18:    2\n", " 50  66:    2\n", " 51   8:    1\n", " 38  57:    5\n", " 42  56:    2\n", " 42  21:    1\n", " 37  57:    5\n", " 51  43:    4\n", " 78  71:    1\n", " 51  55:    8\n", " 44  86:    1\n", " 49  75:    1\n", " 55  51:    3\n", " 39  61:    3\n", " 34   1:    5\n", " 47  18:    1\n", " 65  74:    1\n", " 46  12:    1\n", " 33  22:   14\n", " 39  15:    2\n", " 44  72:    1\n", " 54  58:    1\n", " 38  55:   11\n", " 41  46:   15\n", " 59  68:    2\n", " 49  40:   11\n", " 47  30:    4\n", " 31  58:    6\n", " 39  62:    3\n", " 32   9:    8\n", " 50  46:    4\n", " 53  44:    4\n", " 60  59:    2\n", " 39  51:    8\n", " 44  54:    4\n", " 46  80:    2\n", " 34  76:    1\n", " 62  50:    2\n", " 49  17:    2\n", " 49  42:    8\n", " 49  44:    3\n", " 48  42:    9\n", " 48   7:    1\n", " 61  76:    1\n", " 42  62:    1\n", " 45  58:    8\n", " 49  32:    2\n", " 56  38:    1\n", " 54  24:    1\n", " 49  55:    8\n", " 52  64:    1\n", " 47  41:    4\n", " 51  42:    6\n", " 39  53:   11\n", " 31  69:    2\n", " 41  10:    3\n", " 53   0:    2\n", " 75  91:    1\n", " 34  54:    3\n", " 44  27:    2\n", " 59  22:    1\n", " 59  32:    1\n", " 50  56:    5\n", " 40  19:    3\n", " 37  81:    1\n", " 48  54:    4\n", " 49  45:   13\n", " 59  70:    2\n", " 44  60:    3\n", " 35  68:    1\n", " 46  56:    8\n", " 59  60:    5\n", " 43  26:    4\n", " 34  87:    1\n", " 65  67:    1\n", " 50  45:    8\n", " 61  70:    1\n", " 32  51:    8\n", " 62  69:    2\n", " 38  60:    8\n", " 40  82:    1\n", " 44  69:    1\n", " 50  29:    1\n", " 64  25:    1\n", " 54  73:    1\n", " 59  41:    1\n", " 33  72:    1\n", " 39  82:    1\n", " 45  51:    6\n", " 47  60:    2\n", " 46  63:    2\n", " 65  57:    2\n", " 36  73:    2\n", " 68  58:    1\n", " 55  47:    2\n", " 41  69:    1\n", " 53  48:    2\n", " 51  19:    1\n", " 53  50:    3\n", " 61  39:    1\n", " 55  38:    1\n", " 47  29:    3\n", " 41  75:    2\n", " 42  11:    3\n", " 47   9:    1\n", " 58  63:    2\n", " 40  69:    4\n", " 42  26:    9\n", " 34  55:    5\n", " 62  52:    1\n", " 41  65:    2\n", " 49  36:    2\n", " 35  74:    2\n", " 35  65:    3\n", " 73  74:    1\n", " 51  36:    1\n", " 48  36:    4\n", " 49   4:    1\n", " 49   0:    3\n", " 40   9:    2\n", " 68  11:    2\n", " 41  25:    4\n", " 71  17:    2\n", " 40   1:    4\n", " 40  29:    4\n", " 32  21:   10\n", " 43  57:    4\n", " 59  55:    1\n", " 57  43:    2\n", " 37  58:    3\n", " 61  67:    1\n", " 46  11:    2\n", " 39  78:    1\n", " 58  44:    1\n", " 58   1:    2\n", " 35   3:    4\n", " 36  61:    3\n", " 71  35:    1\n", " 44  16:    2\n", " 45  57:    3\n", " 40  54:    7\n", " 57  63:    1\n", " 50  39:    4\n", " 64  64:    2\n", " 55  73:    1\n", " 49  39:    3\n", " 35  10:    1\n", " 32   2:    2\n", " 51  21:    1\n", " 47  39:    5\n", " 49  69:    1\n", " 48  15:    3\n", " 41   0:    4\n", " 42  25:    4\n", " 33   3:    3\n", " 71  13:    1\n", " 52  41:    1\n", " 44   0:    1\n", " 52  78:    1\n", " 55  17:    1\n", " 36  23:    7\n", " 43  36:    8\n", " 40  15:    2\n", " 72  82:    1\n", " 58  65:    4\n", " 50  26:    1\n", " 38  76:    1\n", " 31  60:    3\n", " 59   2:    1\n", " 74  24:    2\n", " 58  28:    1\n", " 50  43:    4\n", " 35  25:    9\n", " 56  44:    1\n", " 51   1:    2\n", " 55  70:    5\n", " 47  68:    1\n", " 52  37:    2\n", " 54  59:   10\n", " 35  47:   10\n", " 51  35:    2\n", " 55  56:    4\n", " 62  75:    1\n", " 64  37:    1\n", " 52  42:    2\n", " 48  34:    3\n", " 47  27:    4\n", " 40  75:    1\n", " 37  72:    1\n", " 40  64:    5\n", " 47  71:    2\n", " 58  35:    1\n", " 66  67:    1\n", " 48  23:    2\n", " 47  56:    4\n", " 38   2:    3\n", " 40  63:    2\n", " 55  50:    4\n", " 35  61:    5\n", " 31  62:    5\n", " 67  63:    1\n", " 47  78:    1\n", " 34  19:    1\n", " 39  30:    6\n", " 35   6:    4\n", " 52  45:    2\n", " 50  47:    3\n", " 39  54:    2\n", " 68  46:    1\n", " 71   1:    3\n", " 62  72:    1\n", " 48  59:    2\n", " 70  15:    2\n", " 35  55:    4\n", " 45  64:    5\n", " 38   9:    5\n", " 34  58:    3\n", " 31  59:    2\n", " 75  37:    1\n", " 52  43:    3\n", " 55  75:    1\n", " 47  26:    1\n", " 43  21:    5\n", " 55  44:    2\n", " 41  19:    4\n", " 52  61:    3\n", " 55  42:    1\n", " 38  75:    1\n", " 38  29:    9\n", " 60  57:    1\n", " 39  20:    3\n", " 45  19:    2\n", " 38  19:    1\n", " 64  67:    2\n", " 55  46:    3\n", " 59  62:    2\n", " 46  39:    5\n", " 68  30:    1\n", " 38  62:    3\n", " 47  44:    7\n", " 31  68:    2\n", " 52  47:    2\n", " 39  63:    1\n", " 51  54:    2\n", " 40  59:    4\n", " 44  64:    1\n", " 44  38:   10\n", " 41  27:    6\n", " 44  25:    6\n", " 49  37:    1\n", " 35  19:    3\n", " 51  25:    1\n", " 42  61:    4\n", " 75  25:    1\n", " 46  13:    3\n", " 62  63:    1\n", " 47  54:    5\n", " 58  56:    2\n", " 37  50:   10\n", " 35  13:    4\n", " 34  64:    5\n", " 58  64:    1\n", " 61  14:    1\n", " 50  42:    2\n", " 51  30:    3\n", " 34  96:    1\n", " 54  57:    2\n", " 64  32:    2\n", " 62  58:    2\n", " 31  66:    2\n", " 36  60:    2\n", " 45  30:    3\n", " 45   7:    1\n", " 44  51:    4\n", " 53  28:    1\n", " 57   1:    1\n", " 53  23:    1\n", " 52  44:    2\n", " 46  32:    3\n", " 36  83:    1\n", " 67  17:    1\n", " 33  11:    2\n", " 50  64:    1\n", " 62  62:    3\n", " 34  16:    4\n", " 63  67:    1\n", " 57  28:    1\n", " 67  57:    1\n", " 37  13:    3\n", " 45  78:    1\n", " 44  12:    1\n", " 43  20:    2\n", " 40   4:    2\n", " 33  64:    2\n", " 62  59:    2\n", " 34   9:    3\n", " 43   7:    2\n", " 37  73:    1\n", " 37  10:    1\n", " 33  61:    3\n", " 39  11:    2\n", " 49  66:    1\n", " 44  34:    2\n", " 35  84:    1\n", " 46  29:    1\n", " 63  25:    1\n", " 48  43:    3\n", " 38  72:    1\n", " 49  20:    2\n", " 40  73:    1\n", " 51  57:    6\n", " 53  20:    2\n", " 43  30:    3\n", " 52  79:    1\n", " 61  18:    2\n", " 70  32:    1\n", " 47   5:    1\n", " 64  47:    1\n", " 56  60:    4\n", " 61  21:    1\n", " 56  58:    2\n", " 40   7:    2\n", " 46   2:    1\n", " 44  80:    1\n", " 50  77:    1\n", " 34  57:    4\n", " 34  59:    1\n", " 72  20:    1\n", " 44  39:    4\n", " 60  13:    1\n", " 35  70:    1\n", " 53  45:    4\n", " 63  75:    1\n", " 70  21:    1\n", " 55  62:    1\n", " 54  63:    2\n", " 51  22:    1\n", " 47  25:    3\n", " 54  13:    1\n", " 41   9:    1\n", " 39  22:    6\n", " 75   7:    1\n", " 60  49:    2\n", " 47  67:    2\n", " 54  55:    5\n", " 43   6:    3\n", " 48  14:    1\n", " 35  62:    3\n", " 80  14:    1\n", " 78   3:    1\n", " 49  33:    1\n", " 53  63:    1\n", " 36  65:    2\n", " 31  89:    1\n", " 52  55:    5\n", " 50  15:    1\n", " 61  63:    4\n", " 53  21:    2\n", " 48  33:    3\n", " 49  43:    4\n", " 69  70:    1\n", " 56  59:    2\n", " 35  63:    2\n", " 35  12:    4\n", " 60  71:    1\n", " 43  15:    1\n", " 36  82:    1\n", " 63  14:    1\n", " 45  37:    3\n", " 32  64:    1\n", " 59  14:    1\n", " 43  74:    1\n", " 64  66:    1\n", " 60   8:    1\n", " 38  64:    2\n", " 53  31:    2\n", " 58  14:    1\n", " 53  58:    2\n", " 52  11:    2\n", " 40  58:    3\n", " 37  61:    3\n", " 39  17:    4\n", " 50  74:    2\n", " 51   2:    1\n", " 32  78:    1\n", " 35  69:    1\n", " 72  48:    1\n", " 41  13:    2\n", " 43  23:    2\n", " 69  43:    2\n", " 44  61:    3\n", " 70  40:    1\n", " 56  14:    1\n", " 52   0:    1\n", " 39  27:    5\n", " 32  60:    3\n", " 45  61:    5\n", " 57  60:    4\n", " 62  39:    1\n", " 73  36:    1\n", " 31  67:    1\n", " 56  53:    2\n", " 43  79:    1\n", " 48  55:    5\n", " 48  32:    2\n", " 41  17:    2\n", " 31  72:    2\n", " 41  16:    2\n", " 37  27:    7\n", " 33  65:    4\n", " 57  33:    1\n", " 71  27:    1\n", " 43   9:    1\n", " 70   5:    1\n", " 55   5:    2\n", " 58  61:    3\n", " 39  21:    3\n", " 33  18:    4\n", " 67  19:    1\n", " 73  70:    1\n", " 61  56:    1\n", " 67  30:    1\n", " 53  25:    1\n", " 75  42:    1\n", " 68  31:    1\n", " 67  71:    1\n", " 36  70:    1\n", " 68  60:    1\n", " 39  31:    7\n", " 48  60:    1\n", " 63  59:    1\n", " 40  62:    2\n", " 36  56:    1\n", " 53  46:    2\n", " 70  28:    1\n", " 57  30:    1\n", " 52  48:    2\n", " 58  42:    1\n", " 56   8:    4\n", " 46   1:    3\n", " 46  23:    3\n", " 75   1:    1\n", " 46   3:    2\n", " 77  19:    1\n", " 48  41:    3\n", " 38   6:    3\n", " 72  49:    2\n", " 43  17:    1\n", " 47  75:    2\n", " 73   1:    1\n", " 74   5:    1\n", " 55  23:    2\n", " 42  18:    4\n", " 80  77:    1\n", " 65  73:    1\n", " 53  15:    1\n", " 51   5:    2\n", " 72  78:    1\n", " 53  73:    1\n", " 42  66:    1\n", " 58   3:    1\n", " 35  58:    4\n", " 50  31:    3\n", " 66  19:    1\n", " 50  49:    6\n", " 51  40:    2\n", " 72  87:    1\n", " 41   6:    2\n", " 51   3:    3\n", " 32  10:    2\n", " 42  83:    1\n", " 46   6:    2\n", " 63  62:    1\n", " 42   9:    2\n", " 48  11:    3\n", " 65   7:    1\n", " 64   0:    2\n", " 64  18:    1\n", " 62  31:    1\n", " 41   3:    2\n", " 32   5:    2\n", " 47  62:    1\n", " 55   1:    1\n", " 46  24:    3\n", " 51  15:    1\n", " 51  18:    2\n", " 52  30:    2\n", " 33   9:    3\n", " 34  94:    1\n", " 72   8:    2\n", " 48  81:    1\n", " 34  10:    5\n", " 60  10:    2\n", " 73  75:    1\n", " 44  31:    5\n", " 46  27:    1\n", " 71  78:    1\n", " 48  72:    1\n", " 51  32:    2\n", " 50  12:    1\n", " 61  58:    1\n", " 71  21:    1\n", " 66  66:    2\n", " 58  66:    1\n", " 31  81:    1\n", " 46  58:    4\n", " 50  27:    2\n", " 35   7:    2\n", " 80  84:    1\n", " 49  31:    2\n", " 55   7:    1\n", " 64   1:    1\n", " 54  21:    1\n", " 39   7:    2\n", " 66  33:    1\n", " 47  84:    1\n", " 59  33:    2\n", " 34   7:    1\n", " 73   7:    1\n", " 68  49:    2\n", " 36  68:    1\n", " 50  33:    1\n", " 71  59:    1\n", " 49  27:    2\n", " 59  69:    1\n", " 35  71:    1\n", " 58  29:    2\n", " 71  55:    1\n", " 53  29:    1\n", " 48  66:    2\n", " 35  91:    1\n", " 35  72:    1\n", " 44   2:    3\n", " 54   2:    3\n", " 51  34:    1\n", " 43  70:    1\n", " 73  63:    1\n", " 60  44:    1\n", " 62  40:    1\n", " 46   7:    1\n", " 44  56:    6\n", " 50  71:    2\n", " 76   0:    1\n", " 46  22:    2\n", " 51  60:    2\n", " 46  28:    1\n", " 58  48:    2\n", " 63  66:    2\n", " 55  11:    1\n", " 32  75:    1\n", " 58  73:    1\n", " 43  63:    1\n", " 77  16:    1\n", " 54  22:    3\n", " 41  22:    3\n", " 73   0:    1\n", " 54  50:    3\n", " 73   3:    1\n", " 71  65:    1\n", " 48  57:    2\n", " 75  61:    3\n", " 45  54:    2\n", " 36  76:    1\n", " 37  12:    2\n", " 55   2:    1\n", " 38  58:    6\n", " 52   9:    1\n", " 46  38:    2\n", " 36  13:    2\n", " 34  62:    1\n", " 35  64:    3\n", " 66   1:    1\n", " 52  32:    1\n", " 53  47:    2\n", " 56  39:    1\n", " 66  70:    1\n", " 46  26:    1\n", " 47  15:    1\n", " 59  49:    1\n", " 57  52:    2\n", " 59   0:    1\n", " 36  15:    1\n", " 35   5:    1\n", " 53  26:    1\n", " 61  33:    1\n", " 50  55:    2\n", " 59   3:    1\n", " 59  48:    1\n", " 57  39:    1\n", " 45  27:    1\n", " 49  23:    1\n", " 32  84:    1\n", " 33  74:    1\n", " 38  73:    1\n", " 37  69:    1\n", " 50  58:    1\n", " 65  62:    1\n", " 53  17:    2\n", " 65  34:    1\n", " 63  60:    2\n", " 64  62:    2\n", " 71  16:    1\n", " 62  29:    1\n", " 37  71:    1\n", " 36   0:    2\n", " 59  39:    2\n", " 73  11:    2\n", " 31  92:    1\n", " 69  21:    1\n", " 57  14:    1\n", " 41  56:    4\n", " 55   0:    1\n", " 64  20:    1\n", " 70  18:    1\n", " 40  21:    1\n", " 50  69:    1\n", " 80  41:    1\n", " 55  60:    4\n", " 54  46:    1\n", " 54   5:    1\n", " 68   0:    1\n", " 45  70:    1\n", " 48  64:    2\n", " 46  68:    1\n", " 50  17:    1\n", " 63   2:    1\n", " 68  24:    1\n", " 52  15:    1\n", " 77  31:    1\n", " 73  43:    1\n", " 32  67:    1\n", " 47  32:    4\n", " 39  66:    2\n", " 56  46:    2\n", " 45  26:    2\n", " 49  46:    1\n", " 60  79:    2\n", " 53  38:    2\n", " 41  20:    3\n", " 67  56:    1\n", " 41  76:    1\n", " 56  16:    1\n", " 52  74:    1\n", " 43  10:    1\n", " 56  22:    1\n", " 33  69:    1\n", " 70  43:    1\n", " 68   2:    1\n", " 33   5:    2\n", " 70   3:    1\n", " 59  30:    1\n", " 63  63:    2\n", " 59  86:    1\n", " 62  49:    1\n", " 78  30:    2\n", " 61  37:    1\n", " 58  54:    1\n", " 41  83:    1\n", " 54  84:    1\n", " 49  73:    1\n", " 38  16:    3\n", " 44   1:    9\n", " 39   6:    1\n", " 40  11:    2\n", " 54  11:    2\n", " 58  19:    1\n", " 62   3:    1\n", " 54   7:    1\n", " 36   2:    2\n", " 47  21:    2\n", " 51  70:    2\n", " 79  21:    1\n", " 50  57:    1\n", " 58  10:    2\n", " 76  76:    1\n", " 35  97:    1\n", " 67  68:    1\n", " 50  78:    1\n", " 77  77:    1\n", " 39  77:    1\n", " 75  74:    1\n", " 76  11:    1\n", " 35   8:    3\n", " 68   7:    1\n", " 69   4:    1\n", " 54  80:    1\n", " 57  78:    1\n", " 36  63:    2\n", " 53  74:    1\n", " 52  97:    1\n", " 69   7:    1\n", " 61  35:    1\n", " 52  24:    2\n", " 48  63:    2\n", " 72  54:    1\n", " 61  53:    1\n", " 70  68:    1\n", " 44  23:    2\n", " 46  66:    1\n", " 60  67:    2\n", " 76  12:    1\n", " 46  14:    1\n", " 71  61:    1\n", " 53  12:    1\n", " 36  80:    1\n", " 43   4:    1\n", " 55  49:    1\n", " 49  62:    1\n", " 38  66:    1\n", " 78   1:    1\n", " 62  22:    1\n", " 75  76:    1\n", " 52  18:    1\n", " 40  72:    1\n", " 65  66:    1\n", " 46  89:    1\n", " 59  63:    1\n", " 43  60:    5\n", " 58  62:    2\n", " 52  69:    1\n", " 52  16:    2\n", " 60  69:    1\n", " 36  87:    1\n", " 70  60:    1\n", " 35   4:    1\n", " 63  49:    1\n", " 40  79:    1\n", " 55  20:    1\n", " 71  56:    1\n", " 51  41:    1\n", " 69  26:    3\n", " 56  86:    1\n", " 55  88:    1\n", " 47   2:    1\n", " 54  38:    2\n", " 32  66:    1\n", " 67  29:    1\n", " 67  55:    1\n", " 56  27:    1\n", " 32  94:    1\n", " 40  74:    1\n", " 57   5:    1\n", " 40  70:    1\n", " 37   9:    2\n", " 37  80:    1\n", " 48  19:    1\n", " 56   4:    1\n", " 41   4:    1\n", " 54  76:    1\n", " 48   5:    1\n", " 33  67:    1\n", " 56  48:    2\n", " 66  10:    1\n", " 31  86:    2\n", " 66   9:    1\n", " 69  76:    1\n", " 56  50:    1\n", " 39  60:    1\n", " 52  29:    1\n", " 61   5:    1\n", " 54  77:    1\n", " 73  18:    1\n", " 59  57:    1\n", " 58  39:    1\n", " 46  57:    2\n", " 32  56:    3\n", " 65  15:    1\n", " 53  67:    1\n", " 42  20:    2\n", " 51  33:    1\n", " 70  23:    1\n", " 61  40:    1\n", " 52  50:    2\n", " 53  75:    1\n", " 34  63:    1\n", " 62  66:    2\n", " 64  59:    2\n", " 48  65:    2\n", " 61  28:    1\n", " 57  54:    1\n", " 58  25:    2\n", " 42  23:    1\n", " 67  21:    1\n", " 51   0:    1\n", " 53  30:    3\n", " 49  19:    2\n", " 63  68:    1\n", " 45  66:    2\n", " 55  41:    2\n", " 61  43:    1\n", " 34   6:    1\n", " 51  12:    1\n", " 51  28:    3\n", " 46  79:    1\n", " 48  26:    1\n", " 38  65:    2\n", " 63  24:    1\n", " 71  33:    1\n", " 57  11:    1\n", " 67  67:    2\n", " 59  15:    2\n", " 72  72:    2\n", " 55  35:    1\n", " 57  71:    2\n", " 70  74:    1\n", " 50  61:    1\n", " 64  57:    1\n", " 56  65:    1\n", " 74  72:    1\n", " 70  82:    1\n", " 55  65:    1\n", " 65  63:    1\n", " 42  99:    1\n", " 51  44:    2\n", " 50  32:    2\n", " 61  55:    1\n", " 65  25:    1\n", " 43  75:    1\n", " 72  85:    1\n", " 48  22:    2\n", " 32  62:    1\n", " 50  20:    1\n", " 48  25:    1\n", " 45  11:    1\n", " 34  78:    1\n", " 37  15:    3\n", " 35  11:    2\n", " 36   4:    1\n", " 54  72:    2\n", " 62   9:    1\n", " 47   1:    1\n", " 54  28:    1\n", " 74  13:    1\n", " 44  11:    1\n", " 51   6:    1\n", " 60  12:    1\n", " 34  56:    2\n", " 55  48:    1\n", " 58  43:    1\n", " 51  91:    1\n", " 36  58:    3\n", " 66  64:    1\n", " 39  76:    2\n", " 33  59:    1\n", " 59  67:    1\n", " 41  68:    1\n", " 57  45:    1\n", " 50  53:    1\n", " 42   0:    1\n", " 43  18:    2\n", " 61  31:    1\n", " 48  30:    1\n", " 45  13:    1\n", " 66  72:    1\n", " 55  32:    1\n", " 31  61:    1\n", " 34   5:    1\n", " 59  56:    1\n", " 62  14:    1\n", " 34  81:    1\n", " 65  49:    1\n", " 53  64:    1\n", " 77  22:    1\n", " 46  70:    1\n", " 52   3:    1\n", " 38  59:    2\n", " 48  71:    1\n", " 43   3:    1\n", " 60  46:    1\n", " 57  67:    1\n", " 47  36:    3\n", " 55  36:    1\n", " 49  65:    1\n", " 45  65:    1\n", " 55  66:    1\n", " 51  64:    1\n", " 49  41:    3\n", " 52  35:    1\n", " 51  37:    1\n", " 74  79:    1\n", " 31  90:    1\n", " 42  71:    1\n", " 68  25:    1\n", " 56  42:    1\n", " 60  23:    1\n", " 53  13:    1\n", " 50  44:    1\n", " 46  86:    1\n", " 43  64:    2\n", " 58  23:    1\n", " 45  63:    2\n", " 47  17:    1\n", " 72  76:    1\n", " 55  24:    1\n", " 59  11:    1\n", " 31  65:    1\n", " 64  35:    1\n", " 39  65:    2\n", " 50  11:    1\n", " 54  52:    1\n", " 74  21:    1\n", " 44  67:    1\n", " 49  26:    1\n", " 42  10:    1\n", " 46  10:    1\n", " 55  53:    1\n", " 44   7:    1\n", " 61  26:    1\n", " 59  80:    1\n", " 52  31:    2\n", " 52  26:    1\n", " 69  17:    1\n", " 36   7:    1\n", " 65  18:    1\n", " 58  70:    1\n", " 45  67:    1\n", " 62  34:    1\n", " 63  16:    1\n", " 31  77:    1\n", " 32  74:    1\n", " 51  48:    1\n", " 63  28:    1\n", " 35  78:    1\n", " 65  13:    2\n", " 57  16:    1\n", " 61  54:    2\n", " 70  48:    1\n", " 40  12:    1\n", " 66  73:    1\n", " 45  85:    1\n", " 57  69:    1\n", " 43  69:    1\n", " 40  80:    1\n", " 42  65:    1\n", " 65  65:    1\n", " 57  51:    1\n", " 56   5:    1\n", " 48  73:    1\n", " 65  48:    1\n", " 34  75:    1\n", " 54  74:    1\n", " 74  31:    1\n", " 40  71:    2\n", " 47  28:    1\n", " 68  90:    1\n", " 60  42:    1\n", " 66  47:    2\n", " 49  16:    1\n", " 53  14:    1\n", " 67  18:    1\n", " 46  25:    2\n", " 45  69:    1\n", " 69  38:    1\n", " 61  82:    1\n", " 63   7:    1\n", " 31  79:    1\n", " 36  71:    1\n", " 60  35:    1\n", " 55   6:    1\n", " 49  25:    1\n", " 79   4:    1\n", " 49  15:    1\n"]}], "source": ["# Further Filtering\n", "all_edit_data = []\n", "for x in edit_data_in_house + commitpack:\n", "    file_name = x.file_path.split(\"/\")[-1]\n", "    if file_name.lower().startswith(\"setup\") or file_name.lower().startswith(\"readme\"):\n", "        continue\n", "    if \"<INST>\" in x.new_file_content:\n", "        continue\n", "    if \"<SELECTED>\" in x.new_file_content:\n", "        continue\n", "    if \"<UPDATED>\" in x.new_file_content:\n", "        continue\n", "    if \"<PREFIX>\" in x.new_file_content:\n", "        continue\n", "    if \"<SUFFIX>\" in x.new_file_content:\n", "        continue\n", "    all_edit_data.append(x)\n", "print(\n", "    f\"Therea are {len(all_edit_data)} / {len(edit_data_in_house) + len(commitpack)} examples\"\n", ")\n", "\n", "num_per_diff = dict()\n", "for x in all_edit_data:\n", "    assert x.instruction\n", "    diff = x.get_diffs()\n", "    if diff not in num_per_diff:\n", "        num_per_diff[diff] = 0\n", "    num_per_diff[diff] += 1\n", "for (del_l, add_l), num in num_per_diff.items():\n", "    print(f\"{del_l:3d} {add_l:3d}: {num:4d}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chosed tokenizer Vocab Size = 32000\n", "train_ratio = 0.97\n", "#train_edit_data_w_instruction = 339063\n", "#valid_edit_data_w_instruction = 10487\n", "\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 339063/339063 [06:06<00:00, 925.82it/s] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of examples = 339063\n", "Average number of tokens per example = 466.69337851667683 += 334.78485071939343\n", "Minimum number of tokens per example = 61\n", "Maximum number of tokens per example = 10062\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10487/10487 [00:11<00:00, 890.20it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of examples = 10487\n", "Average number of tokens per example = 466.21273958234 += 339.4837433703961\n", "Minimum number of tokens per example = 61\n", "Maximum number of tokens per example = 5263\n", "-\n"]}], "source": ["# Create the index dataset\n", "from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "\n", "tokenizer_llama2 = Llama2Tokenizer()\n", "tokenizer = tokenizer_llama2\n", "print(f\"Chosed tokenizer Vocab Size = {tokenizer.vocab_size}\")\n", "\n", "indexes = list(range(len(all_edit_data)))\n", "random.shuffle(indexes)\n", "train_ratio = 0.97\n", "train_indexes = indexes[: int(len(indexes) * train_ratio)]\n", "valid_indexes = indexes[int(len(indexes) * train_ratio) :]\n", "train_edit_data_w_instruction = [all_edit_data[i] for i in train_indexes]\n", "valid_edit_data_w_instruction = [all_edit_data[i] for i in valid_indexes]\n", "print(f\"train_ratio = {train_ratio}\")\n", "print(f\"#train_edit_data_w_instruction = {len(train_edit_data_w_instruction)}\")\n", "print(f\"#valid_edit_data_w_instruction = {len(valid_edit_data_w_instruction)}\")\n", "print(\"\\n\")\n", "\n", "train_examples = build_to_fine_tun_data(\n", "    train_edit_data_w_instruction, tokenizer=tokenizer\n", ")\n", "print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "valid_examples = build_to_fine_tun_data(\n", "    valid_edit_data_w_instruction, tokenizer=tokenizer\n", ")\n", "print(\"-\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 339063/339063 [00:18<00:00, 18807.40it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Save 339063 examples to /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/unpacked-v2-train\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10487/10487 [00:00<00:00, 18448.20it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Save 10487 examples to /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/unpacked-v2-valid\n", "There are 339063 examples in total.\n", "#repeated_examples = 2034378 with repeats=6\n", "Skip 174 examples due to over-4096-tokens.\n", "Packed 2034378 examples into 2034204 4096-length-sequences.\n", "On average, the number of paddings is 3629.76.\n", "There are 2034204 sequences.\n", "Built the train-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_full_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 10487 examples in total.\n", "#repeated_examples = 10487 with repeats=1\n", "Skip 3 examples due to over-4096-tokens.\n", "Packed 10487 examples into 10484 4096-length-sequences.\n", "On average, the number of paddings is 3631.00.\n", "There are 10484 sequences.\n", "Built the valid-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_full_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 339063 examples in total.\n", "#repeated_examples = 2034378 with repeats=6\n", "Skip 174 examples due to over-4096-tokens.\n", "Packed 2034378 examples into 213207 4096-length-sequences.\n", "On average, the number of paddings is 350.22.\n", "There are 213207 sequences.\n", "Built the train-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_full_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 10487 examples in total.\n", "#repeated_examples = 10487 with repeats=1\n", "Skip 3 examples due to over-4096-tokens.\n", "Packed 10487 examples into 1096 4096-length-sequences.\n", "On average, the number of paddings is 361.62.\n", "There are 1096 sequences.\n", "Built the valid-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_full_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 339063 examples in total.\n", "#repeated_examples = 2034378 with repeats=6\n", "Skip 174 examples due to over-4096-tokens.\n", "Packed 2034378 examples into 2034204 4096-length-sequences.\n", "On average, the number of paddings is 3629.76.\n", "There are 2034204 sequences.\n", "Built the train-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_onlytgt_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 10487 examples in total.\n", "#repeated_examples = 10487 with repeats=1\n", "Skip 3 examples due to over-4096-tokens.\n", "Packed 10487 examples into 10484 4096-length-sequences.\n", "On average, the number of paddings is 3631.00.\n", "There are 10484 sequences.\n", "Built the valid-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_onlytgt_pad.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 339063 examples in total.\n", "#repeated_examples = 2034378 with repeats=6\n", "Skip 174 examples due to over-4096-tokens.\n", "Packed 2034378 examples into 213282 4096-length-sequences.\n", "On average, the number of paddings is 349.95.\n", "There are 213282 sequences.\n", "Built the train-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_onlytgt_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 10487 examples in total.\n", "#repeated_examples = 10487 with repeats=1\n", "Skip 3 examples due to over-4096-tokens.\n", "Packed 10487 examples into 1099 4096-length-sequences.\n", "On average, the number of paddings is 365.99.\n", "There are 1099 sequences.\n", "Built the valid-v2 dataset at /mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_onlytgt_pack.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["# Build the raw unpacked dataset\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "unpacked_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/unpacked-v2\"\n", ")\n", "unpacked_dir.mkdir(parents=True, exist_ok=True)\n", "for save_name, examples in ((\"train\", train_examples), (\"valid\", valid_examples)):\n", "    output_path = unpacked_dir.parent / f\"{unpacked_dir.stem}-{save_name}\"\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for example in tqdm.tqdm(examples):\n", "        question, answer = example[\"question\"], example[\"answer\"]\n", "        builder.add_item(torch.concat((-torch.Tensor(question), torch.Tensor(answer))))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Save {len(examples)} examples to {output_path}\")\n", "\n", "\n", "for mask_input in (<PERSON><PERSON><PERSON>, <PERSON>):\n", "    for pad_or_pack in (<PERSON><PERSON><PERSON>, <PERSON>):\n", "        build_whole_dataset(\n", "            train_examples,\n", "            \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/\",\n", "            \"train-v2\",\n", "            repeats=6,\n", "            seq_len=4096,\n", "            mask_input=mask_input,\n", "            use_pack_or_pad=pad_or_pack,\n", "        )\n", "        print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "        build_whole_dataset(\n", "            valid_examples,\n", "            \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/\",\n", "            \"valid-v2\",\n", "            repeats=1,\n", "            seq_len=4096,\n", "            mask_input=mask_input,\n", "            use_pack_or_pad=pad_or_pack,\n", "        )\n", "        print(\"\\n\" + \"-\" * 100 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Scripts\n", "\n", "Fine-tune from `CodeLlama-7b-Python`\n", "\n", "```bash\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32000 --rope_theta=1000000.0 \\\n", " --learning_rate=1e-6 --decay_lr=False --weight_decay=1e-1 \\\n", " --max_iters=3000 --batch_size=8 \\\n", " --eval_interval=50 --eval_iters=0 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train1v_r1n_s4096_onlytgt \\\n", " --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid1v_r1n_s4096_onlytgt \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \\\n", " --wandb_log=True --wandb_project=edit-exps --wandb_run_name=DXY-CL-PY7B-MixALL1vOTarget-step_3000-WD0_1-b8-lr_000001\n", "\n", "```\n", "\n", "Fine-tune from `CodeLlama-7B-Base`\n", "\n", "```bash\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32016 --rope_theta=1000000.0 \\\n", " --learning_rate=1e-6 --decay_lr=False --weight_decay=1e-1 \\\n", " --max_iters=4000 --batch_size=8 \\\n", " --eval_interval=50 --eval_iters=0 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train1v_r1n_s4096_onlytgt \\\n", " --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid1v_r1n_s4096_onlytgt \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \\\n", " --wandb_log=True --wandb_project=edit-exps --wandb_run_name=DXY-CL-BASE7B-MixALL1vOTarget-step_4000-WD0_1-b8-lr_000001\n", "\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32016 --rope_theta=1000000.0 \\\n", " --learning_rate=1e-5 --decay_lr=False --weight_decay=1e-1 \\\n", " --max_iters=4000 --batch_size=8 \\\n", " --eval_interval=50 --eval_iters=0 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train1v_r1n_s4096_onlytgt \\\n", " --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid1v_r1n_s4096_onlytgt \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit/ \\\n", " --wandb_log=True --wandb_project=edit-exps --wandb_run_name=DXY-CL-BASE7B-MixALL1vOTarget-step_4000-WD0_1-b8-lr_00001\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32016 --rope_theta=1000000.0 \\\n", " --learning_rate=2e-5 --decay_lr=False --weight_decay=1.0 \\\n", " --max_iters=4000 --batch_size=8 \\\n", " --eval_interval=100 --eval_iters=32 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_onlytgt_pad \\\n", " --eval_data_path=\"all@/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_onlytgt_pad;python@/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad\" \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit.pad/ \\\n", " --wandb_log=True --wandb_project=edit-exps-v2 \\\n", " --wandb_group=DXY-ALL --wandb_run_name=CL-BASE7B-MixALL2v_PAD_OTarget-step_4000-WD1_0-b8-lr_00002\n", "\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32016 --rope_theta=1000000.0 \\\n", " --learning_rate=5e-5 --decay_lr=False --weight_decay=1.0 \\\n", " --max_iters=4000 --batch_size=8 \\\n", " --eval_interval=100 --eval_iters=32 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/train-v2_r6n_s4096_onlytgt_pack \\\n", " --eval_data_path=\"all@/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_onlytgt_pad;python@/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad\" \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit.pack/ \\\n", " --wandb_log=True --wandb_project=edit-exps-v2 \\\n", " --wandb_group=DXY-ALL --wandb_run_name=CL-BASE7B-MixALL2v_PACK_OTarget-step_4000-WD1_0-b8-lr_00005\n", "\n", "\n", "torchrun --standalone --nproc_per_node=8 train.py configs/llama_7b.py \\\n", " --vocab_size=32016 --rope_theta=1000000.0 \\\n", " --learning_rate=2e-5 --decay_lr=False --weight_decay=1.0 \\\n", " --max_iters=3000 --batch_size=8 \\\n", " --eval_interval=100 --eval_iters=32 \\\n", " --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-hf/ \\\n", " --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/train-v1_r3n_s4096_onlytgt_pack \\\n", " --eval_data_path=\"all@/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-10-31/valid-v2_r1n_s4096_onlytgt_pad;python@/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid-v1_r1n_s4096_onlytgt_pad\" \\\n", " --out_dir=/mnt/efs/augment/user/dxy/logs/edit.pack/ \\\n", " --wandb_log=True --wandb_project=edit-exps-v2 \\\n", " --wandb_group=DXY-ALL --wandb_run_name=CL-BASE7B-MixPY1v_PACK_OTarget-step_3000-WD1_0-b8-lr_00002\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Misc"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "block_size = 4096\n", "eval_dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-10-31/valid1v_r1n_s4096_onlytgt\"\n", ")\n", "eval_data_order = np.array(range(len(eval_dataset)))\n", "my_idxs = eval_data_order[100:164]\n", "temp = [\n", "    torch.from_numpy(eval_dataset[i][0:block_size].astype(np.int64))\n", "    for i in eval_data_order\n", "]\n", "x = torch.stack(temp)\n", "print(x.shape)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}