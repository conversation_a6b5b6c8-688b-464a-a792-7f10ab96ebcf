{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits import data_type\n", "\n", "\n", "def main():\n", "    repo_data_dir = pathlib.Path(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.raw/commitpackft\"\n", "    )\n", "\n", "    assert repo_data_dir.exists()\n", "    print(\"Processing commitpackft data...\")\n", "    data_dir = repo_data_dir / \"data\"\n", "    assert data_dir.exists\n", "    # load all the sub folders\n", "    sub_folders = []\n", "    for sub_folder in data_dir.iterdir():\n", "        if sub_folder.is_dir():\n", "            sub_folders.append(sub_folder)\n", "    print(f\"Found {len(sub_folders)} sub folders\")\n", "    all_data = []\n", "    for sub_folder in tqdm.tqdm(sub_folders, total=len(sub_folders)):\n", "        data_jsonl_path = sub_folder / \"data.jsonl\"\n", "        data_list = utils_for_file.read_jsonl(data_jsonl_path)\n", "        all_data.extend(data_list)\n", "    print(f\"There are {len(all_data)} examples in total.\")\n", "    return all_data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import typing\n", "from experimental.dxy.edits import data_type\n", "\n", "\n", "def simple_git_diff(\n", "    old_contents: str, new_contents: str\n", ") -> tuple[tuple[int, int], tuple[int, int]]:\n", "    old_lines = old_contents.splitlines(keepends=True)\n", "    new_lines = new_contents.splitlines(keepends=True)\n", "    old_l, old_r = 0, len(old_lines) - 1\n", "    new_l, new_r = 0, len(new_lines) - 1\n", "\n", "    # Find the starting line where the difference begins\n", "    while old_l <= old_r and new_l <= new_r and old_lines[old_l] == new_lines[new_l]:\n", "        old_l += 1\n", "        new_l += 1\n", "\n", "    # Find the ending line where the difference ends\n", "    while old_l <= old_r and new_l <= new_r and old_lines[old_r] == new_lines[new_r]:\n", "        old_r -= 1\n", "        new_r -= 1\n", "\n", "    # Return the ranges of differences for old and new contents\n", "    return ((old_l, old_r), (new_l, new_r))\n", "\n", "\n", "def convert_commitpackft_to_edit(data: dict) -> typing.Optional[data_type.EditData]:\n", "    \"\"\"Convert the commitpackft to the edit data type.\"\"\"\n", "    if data[\"old_contents\"] == data[\"new_contents\"]:\n", "        return None\n", "    old_lines = data[\"old_contents\"].splitlines(keepends=True)\n", "    new_lines = data[\"new_contents\"].splitlines(keepends=True)\n", "    old_diff, new_diff = simple_git_diff(data[\"old_contents\"], data[\"new_contents\"])\n", "    lines: list[data_type.Line] = []\n", "    for l in range(old_diff[0], old_diff[1] + 1):\n", "        lines.append(data_type.Line(old_lines[l], \"-\"))\n", "    for l in range(new_diff[0], new_diff[1] + 1):\n", "        lines.append(data_type.Line(new_lines[l], \"+\"))\n", "\n", "    edit_data = data_type.EditData(\n", "        repo=data[\"repos\"],\n", "        commit_sha=data[\"commit\"],\n", "        file_path=data[\"new_file\"],\n", "        lines=lines,\n", "        old_file_content=data[\"old_contents\"],\n", "        new_file_content=data[\"new_contents\"],\n", "        old_hunk_range=(old_diff[0] + 1, old_diff[1] + 2),\n", "        new_hunk_range=(new_diff[0] + 1, new_diff[1] + 2),\n", "        instruction=data[\"instruction\"],\n", "    )\n", "    return edit_data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load 702062 examples from commitpackft.all.jsonl.zst\n"]}], "source": ["from research.core import utils_for_dataclass\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits import data_type\n", "\n", "all_raw_edit_from_cpft = utils_for_file.read_jsonl_zst(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.processed/commitpackft.all.jsonl.zst\"\n", ")\n", "print(f\"Load {len(all_raw_edit_from_cpft)} examples from commitpackft.all.jsonl.zst\")\n", "all_edit_from_cpft = [\n", "    utils_for_dataclass.create_from_dict(data_type.EditData, x)\n", "    for x in all_raw_edit_from_cpft\n", "]"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i=0 : num_deleted=193656 : num_added=25370\n", "i=1 : num_deleted=235999 : num_added=237914\n", "i=2 : num_deleted=38197 : num_added=51004\n", "i=3 : num_deleted=25182 : num_added=34802\n", "i=4 : num_deleted=23225 : num_added=28605\n", "i=5 : num_deleted=17276 : num_added=23820\n", "i=6 : num_deleted=14221 : num_added=19385\n", "i=7 : num_deleted=12787 : num_added=17083\n", "i=8 : num_deleted=11265 : num_added=15769\n", "i=9 : num_deleted=10426 : num_added=14980\n", "i=10 : num_deleted=9226 : num_added=12703\n", "i=11 : num_deleted=8473 : num_added=13132\n", "i=12 : num_deleted=7613 : num_added=10988\n", "i=13 : num_deleted=7266 : num_added=10480\n", "i=14 : num_deleted=6671 : num_added=9388\n", "i=15 : num_deleted=6272 : num_added=9205\n", "i=16 : num_deleted=5945 : num_added=8696\n", "i=17 : num_deleted=5430 : num_added=8201\n", "i=18 : num_deleted=5246 : num_added=7713\n", "i=19 : num_deleted=4943 : num_added=7409\n", "i=20 : num_deleted=4671 : num_added=7135\n", "i=21 : num_deleted=4272 : num_added=6869\n", "i=22 : num_deleted=3895 : num_added=6554\n", "i=23 : num_deleted=3672 : num_added=6079\n", "i=24 : num_deleted=3429 : num_added=6000\n", "i=25 : num_deleted=3209 : num_added=5590\n", "i=26 : num_deleted=2768 : num_added=5390\n", "i=27 : num_deleted=2638 : num_added=4984\n", "i=28 : num_deleted=2362 : num_added=4687\n", "i=29 : num_deleted=2138 : num_added=4412\n", "i=30 : num_deleted=1982 : num_added=4265\n", "i=31 : num_deleted=1933 : num_added=3963\n", "i=32 : num_deleted=1565 : num_added=3856\n", "i=33 : num_deleted=1505 : num_added=3595\n", "i=34 : num_deleted=1334 : num_added=3391\n", "i=35 : num_deleted=1207 : num_added=3185\n", "i=36 : num_deleted=1049 : num_added=2987\n", "i=37 : num_deleted=1033 : num_added=2776\n", "i=38 : num_deleted=890 : num_added=2595\n", "i=39 : num_deleted=814 : num_added=2533\n", "i=40 : num_deleted=718 : num_added=2415\n", "i=41 : num_deleted=687 : num_added=2310\n", "i=42 : num_deleted=593 : num_added=2059\n", "i=43 : num_deleted=459 : num_added=2028\n", "i=44 : num_deleted=463 : num_added=1880\n", "i=45 : num_deleted=383 : num_added=1823\n", "i=46 : num_deleted=365 : num_added=1778\n", "i=47 : num_deleted=307 : num_added=1645\n", "i=48 : num_deleted=279 : num_added=1444\n", "i=49 : num_deleted=266 : num_added=1331\n", "{'Inno Setup', 'Tcl', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'edn', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>talk', '<PERSON><PERSON><PERSON>le', 'NetLinx', 'wisp', 'reStructuredText', 'Go', 'WebIDL', 'Mathematica', 'Inform 7', 'KRL', 'Solidity', '<PERSON>', 'Nit', 'ATS', 'HLS<PERSON>', 'Idris', 'Vala', 'Sass', 'fish', 'Squirrel', 'Slash', 'PureScript', 'Kotlin', 'ooc', 'XProc', 'HTML', 'POV-Ray SDL', 'Eagle', 'JSON5', 'Csound', 'Hy', 'mupad', 'HTML+PHP', 'Literate CoffeeScript', 'CSV', 'CMake', 'Literate Haskell', 'Perl6', 'Slim', 'HTML+ERB', 'Logos', 'Rouge', 'Oz', '<PERSON>', 'nesC', 'Assembly', 'Emacs Lisp', 'xBase', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', 'Yacc', 'A<PERSON><PERSON><PERSON>', 'Processing', 'VCL', '<PERSON>fuck', 'Ra<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'IGOR Pro', 'Ioke', 'M4', 'XS', 'LSL', 'Maple', 'unknown', 'Zephir', 'Forth', 'ColdFusion CFC', 'Zig', 'Ceylon', \"Ren'Py\", 'Objective-C++', 'Jupyter Notebook', 'Latte', 'TypeScript', 'Fancy', 'RDoc', 'Scaml', 'UrWeb', 'JFlex', 'DM', 'UnrealScript', 'Shell', 'STON', 'Gettext Catalog', 'Logtalk', 'Red', 'Groovy', 'XSLT', 'Python', 'PostScript', 'XML', 'HTML+Django', 'PureBasic', 'Haml', 'SQF', 'Ninja', 'INI', 'Perl', 'Elixir', 'PigLatin', 'Standard ML', 'Genshi', 'Creole', 'Visual Basic', 'Nu', 'TOML', 'JavaScript', 'AsciiDoc', 'Scala', 'Lean', 'VimL', 'Stylus', 'CartoCSS', 'GAS', 'Factor', 'C2hs Haskell', \"Cap'n Proto\", 'Vue', 'Cucumber', 'Eiffel', 'LFE', 'Ruby', 'XPages', 'Common Lisp', 'Mask', 'MediaWiki', 'ColdFusion', 'Groff', 'JSONiq', 'MoonScript', 'SaltStack', 'SMT', 'Parrot Assembly', 'RobotFramework', 'ANTLR', 'API Blueprint', 'C++', 'QMake', 'Uno', 'Rust', 'Chapel', 'F#', 'BlitzMax', 'Java', 'Cuda', 'VHDL', 'Lua', 'Thrift', 'Bluespec', 'Groovy Server Pages', 'EmberScript', 'Harbour', 'Tcsh', 'QML', 'Sage', 'Ragel in Ruby Host', 'SQL', 'AppleScript', 'Pod', 'Rebol', 'C#', 'ApacheConf', 'SAS', 'desktop', 'OpenCL', 'Graphviz (DOT)', 'CoffeeScript', 'FLUX', 'Handlebars', 'Smarty', 'Textile', 'Stata', 'ABAP', 'HCL', 'JSONLD', 'Metal', 'ActionScript', 'HTML+EEX', 'Swift', 'Erlang', 'FORTRAN', 'SystemVerilog', 'CSS', 'Propeller Spin', 'XQuery', 'FreeMarker', 'Java Server Pages', 'SourcePawn', 'Nimrod', 'JSON', 'ECL', 'GraphQL', 'Literate Agda', 'Julia', 'Gnuplot', 'HTTP', 'Pure Data', 'YANG', 'Mako', 'Module Management System', 'SCSS', 'DNS Zone', 'RHTML', 'Protocol Buffer', 'Makefile', 'Volt', 'NSIS', 'G-code', 'LiveScript', 'LLVM', 'Jasmin', 'TeX', 'OpenSCAD', 'AutoHotkey', 'Nix', 'PowerShell', 'GLSL', 'Dart', 'SPARQL', 'AspectJ', 'Agda', 'Pascal', 'Bro', 'BitBake', 'Markdown', 'Diff', 'Scilab', 'Text', 'Awk', 'ASP', 'Cython', 'Haxe', 'Jade', 'Isabelle', 'Org', 'PAWN', 'Modelica', 'LilyPond', 'Augeas', 'DIGITAL Command Language', 'Dylan', 'Less', 'MTML', 'Twig', 'PHP', 'APL', 'Batchfile', 'Pike', 'Scheme', 'GDScript', 'Liquid', 'SVG', 'Xtend', 'SuperCollider', 'Io', 'JSX', 'Parrot Internal Representation', 'Unity3D Asset', 'Linker Script', 'RAML', 'Arc', 'Nginx', 'OCaml', 'C', 'Pony', 'Gentoo Ebuild'}\n"]}], "source": ["import collections\n", "\n", "num_del = collections.defaultdict(int)\n", "num_add = collections.defaultdict(int)\n", "lanages = set()\n", "for x in all_edit_from_cpft:\n", "    deleted, added = x.get_diffs()\n", "    lanages.add(x.language)\n", "    num_del[deleted] += 1\n", "    num_add[added] += 1\n", "for i in range(50):\n", "    print(f\"i={i} : num_deleted={num_del[i]} : num_added={num_add[i]}\")\n", "print(lanages)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i=0 : num_deleted=19610 : num_added=1241\n", "i=1 : num_deleted=13085 : num_added=12627\n", "i=2 : num_deleted=2289 : num_added=2869\n", "i=3 : num_deleted=1646 : num_added=2039\n", "i=4 : num_deleted=1435 : num_added=1849\n", "i=5 : num_deleted=1249 : num_added=1573\n", "i=6 : num_deleted=1215 : num_added=1409\n", "i=7 : num_deleted=1000 : num_added=1204\n", "i=8 : num_deleted=959 : num_added=1181\n", "i=9 : num_deleted=900 : num_added=1072\n", "i=10 : num_deleted=874 : num_added=1048\n", "i=11 : num_deleted=837 : num_added=988\n", "i=12 : num_deleted=752 : num_added=886\n", "i=13 : num_deleted=716 : num_added=958\n", "i=14 : num_deleted=660 : num_added=888\n", "i=15 : num_deleted=675 : num_added=859\n", "i=16 : num_deleted=615 : num_added=874\n", "i=17 : num_deleted=587 : num_added=828\n", "i=18 : num_deleted=569 : num_added=849\n", "i=19 : num_deleted=533 : num_added=809\n", "i=20 : num_deleted=493 : num_added=906\n", "i=21 : num_deleted=456 : num_added=807\n", "i=22 : num_deleted=411 : num_added=799\n", "i=23 : num_deleted=404 : num_added=698\n", "i=24 : num_deleted=366 : num_added=688\n", "i=25 : num_deleted=321 : num_added=701\n", "i=26 : num_deleted=312 : num_added=678\n", "i=27 : num_deleted=311 : num_added=626\n", "i=28 : num_deleted=259 : num_added=592\n", "i=29 : num_deleted=262 : num_added=560\n", "i=30 : num_deleted=248 : num_added=586\n", "i=31 : num_deleted=236 : num_added=563\n", "i=32 : num_deleted=196 : num_added=544\n", "i=33 : num_deleted=190 : num_added=499\n", "i=34 : num_deleted=174 : num_added=477\n", "i=35 : num_deleted=147 : num_added=456\n", "i=36 : num_deleted=120 : num_added=448\n", "i=37 : num_deleted=116 : num_added=445\n", "i=38 : num_deleted=103 : num_added=406\n", "i=39 : num_deleted=76 : num_added=402\n", "i=40 : num_deleted=83 : num_added=362\n", "i=41 : num_deleted=71 : num_added=363\n", "i=42 : num_deleted=74 : num_added=345\n", "i=43 : num_deleted=58 : num_added=349\n", "i=44 : num_deleted=48 : num_added=300\n", "i=45 : num_deleted=36 : num_added=319\n", "i=46 : num_deleted=32 : num_added=294\n", "i=47 : num_deleted=27 : num_added=321\n", "i=48 : num_deleted=29 : num_added=261\n", "i=49 : num_deleted=30 : num_added=248\n"]}], "source": ["import collections\n", "\n", "num_del = collections.defaultdict(int)\n", "num_add = collections.defaultdict(int)\n", "\n", "for x in all_edit_from_cpft:\n", "    if x.language == \"Python\":\n", "        deleted, added = x.get_diffs()\n", "        num_del[deleted] += 1\n", "        num_add[added] += 1\n", "for i in range(50):\n", "    print(f\"i={i} : num_deleted={num_del[i]} : num_added={num_add[i]}\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "\n", "def show_randomly():\n", "    total = len(all_edit_from_cpft)\n", "    index = random.randint(0, total - 1)\n", "    data = all_edit_from_cpft[index]\n", "    data.show_lines()\n", "    print(f\"Instruction: {data.instruction}\")\n", "    print(f\"File: {data.file_path}\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31m[-] register = template.Library()\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-] @register.filter\n", "\u001b[0m\u001b[31m[-] def is_parent_of(page1, page2):\n", "\u001b[0m\u001b[31m[-]     \"\"\"\n", "\u001b[0m\u001b[31m[-]     Determines whether a given page is the parent of another page\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-]     Example:\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-]     {% if page|is_parent_of:feincms_page %} ... {% endif %}\n", "\u001b[0m\u001b[31m[-]     \"\"\"\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-]     if page1 is None:\n", "\u001b[0m\u001b[31m[-]         return False\n", "\u001b[0m\u001b[31m[-]     \n", "\u001b[0m\u001b[31m[-]     return (page1.tree_id == page2.tree_id and\n", "\u001b[0m\u001b[31m[-]             page1.lft < page2.lft and\n", "\u001b[0m\u001b[31m[-]             page1.rght > page2.rght)\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-] @register.filter\n", "\u001b[0m\u001b[31m[-] def is_equal_or_parent_of(page1, page2):\n", "\u001b[0m\u001b[31m[-]     return (page1.tree_id == page2.tree_id and\n", "\u001b[0m\u001b[31m[-]             page1.lft <= page2.lft and\n", "\u001b[0m\u001b[31m[-]             page1.rght >= page2.rght)\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-] @register.filter\n", "\u001b[0m\u001b[31m[-] def is_sibling_of(page1, page2):\n", "\u001b[0m\u001b[31m[-]     \"\"\"\n", "\u001b[0m\u001b[31m[-]     Determines whether a given page is a sibling of another page\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-]     {% if page|is_sibling_of:feincms_page %} ... {% endif %}\n", "\u001b[0m\u001b[31m[-]     \"\"\"\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-]     if page1 is None or page2 is None:\n", "\u001b[0m\u001b[31m[-]         return False\n", "\u001b[0m\u001b[31m[-]     return (page1.parent_id == page2.parent_id)\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-] @register.filter\n", "\u001b[0m\u001b[31m[-] def get_extension(filename):\n", "\u001b[0m\u001b[31m[-]     \"\"\" Return the extension from a file name \"\"\"\n", "\u001b[0m\u001b[31m[-]     return os.path.splitext(filename)[1][1:]\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] from feincms.templatetags.feincms_tags import feincms_render_content\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] register = template.Library()\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] @register.filter\n", "\u001b[0m\u001b[32m[+] def is_parent_of(page1, page2):\n", "\u001b[0m\u001b[32m[+]     \"\"\"\n", "\u001b[0m\u001b[32m[+]     Determines whether a given page is the parent of another page\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+]     Example:\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+]     {% if page|is_parent_of:feincms_page %} ... {% endif %}\n", "\u001b[0m\u001b[32m[+]     \"\"\"\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+]     if page1 is None:\n", "\u001b[0m\u001b[32m[+]         return False\n", "\u001b[0m\u001b[32m[+]     \n", "\u001b[0m\u001b[32m[+]     return (page1.tree_id == page2.tree_id and\n", "\u001b[0m\u001b[32m[+]             page1.lft < page2.lft and\n", "\u001b[0m\u001b[32m[+]             page1.rght > page2.rght)\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] @register.filter\n", "\u001b[0m\u001b[32m[+] def is_equal_or_parent_of(page1, page2):\n", "\u001b[0m\u001b[32m[+]     return (page1.tree_id == page2.tree_id and\n", "\u001b[0m\u001b[32m[+]             page1.lft <= page2.lft and\n", "\u001b[0m\u001b[32m[+]             page1.rght >= page2.rght)\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] @register.filter\n", "\u001b[0m\u001b[32m[+] def is_sibling_of(page1, page2):\n", "\u001b[0m\u001b[32m[+]     \"\"\"\n", "\u001b[0m\u001b[32m[+]     Determines whether a given page is a sibling of another page\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+]     {% if page|is_sibling_of:feincms_page %} ... {% endif %}\n", "\u001b[0m\u001b[32m[+]     \"\"\"\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+]     if page1 is None or page2 is None:\n", "\u001b[0m\u001b[32m[+]         return False\n", "\u001b[0m\u001b[32m[+]     return (page1.parent_id == page2.parent_id)\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] @register.filter\n", "\u001b[0m\u001b[32m[+] def get_extension(filename):\n", "\u001b[0m\u001b[32m[+]     \"\"\" Return the extension from a file name \"\"\"\n", "\u001b[0m\u001b[32m[+]     return os.path.splitext(filename)[1][1:]\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] @register.assignment_tag(takes_context=True)\n", "\u001b[0m\u001b[32m[+] def feincms_render_content_as(context, content, request=None):\n", "\u001b[0m\u001b[32m[+]     return feincms_render_content(context, content, request)\n", "\u001b[0mInstruction: Add assignment tag util for rendering chunks to tpl context\n", "File: feincmstools/templatetags/feincmstools_tags.py\n"]}], "source": ["show_randomly()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-] final int screenWidth = 1366;\n", "[-] final int screenHeight = 768;\n", "[-] final color black = color(0, 0, 0);\n", "[-] \n", "[-] Camera camera;\n", "[-] Mouse mouse;\n", "[-] Room room;\n", "[-] \n", "[-] void setup() {\n", "[-]   size(screenWidth, screenHeight, P3D);\n", "[+] final int width = 1366;\n", "[+] final int height = 768;\n", "[+] final color black = color(0, 0, 0);\n", "[+] \n", "[+] Camera camera;\n", "[+] Mouse mouse;\n", "[+] Room room;\n", "[+] \n", "[+] void setup() {\n", "[+]   size(width, height, P3D);\n", "[ ]   noCursor();\n", "[ ]   mouse = new Mouse();\n", "[ ]   camera = new Camera(mouse, width, height);\n", "[ ]   room = new Room(camera);\n", "[ ] }\n", "[ ] \n", "[ ] void draw() {\n", "[ ]   background(black);\n", "[ ]   if (mouse.centred()) mouse.moved();\n", "[ ]   else mouse.centre();\n", "[ ]   camera.set();\n", "[ ]   room.draw();\n", "[ ] }\n", "[ ] \n", "[ ] void keyPressed() {\n", "\n"]}], "source": ["print(edit_data.get_diff_code_in_context(15, 15))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["'FPCamera.pde'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["edit_data.file_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_data[0][\"new_file\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}