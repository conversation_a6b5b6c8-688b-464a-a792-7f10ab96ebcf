{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "\n", "api_token = os.environ.get(\"AUGMENT_TOKEN\", None)\n", "assert api_token is not None\n", "\n", "\n", "def get_models(url: str = \"https://dev-dxy.us-central.api.augmentcode.com\"):\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {api_token}\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-request-id\": \"00000000-0000-0000-0000-000000000001\",\n", "        \"x-request-session-id\": \"11111111-1111-1111-1111-111111111112\",\n", "    }\n", "    response = requests.post(\n", "        f\"{url}/get-models-api-auth\",\n", "        json={},\n", "        headers=headers,\n", "        timeout=120,\n", "    )\n", "    results = response.json()\n", "    default_model = results[\"default_model\"]\n", "    print(f\"The default model is : {default_model}\")\n", "    models, names = results[\"models\"], []\n", "    for index, model in enumerate(models):\n", "        print(f\"[{index}/{len(models)}] : {model['name']}\")\n", "        names.append(model[\"name\"])\n", "    return names\n", "\n", "\n", "_ = get_models()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file, utils_for_str\n", "from research.eval.harness.tasks.edit_eval_task import EditEvalTask\n", "\n", "task = EditEvalTask()\n", "\n", "# EM: 58.4%\n", "res_wi_ret_16k_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/FfZtfj8j/dxy-8-a40__RemoteEditSystem_3a2m/cache\"\n", ")\n", "# EM: 58.4%\n", "res_wi_ret_8k_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/eqpUkxib/dxy-8-a40__RemoteEditSystem_6s3a/cache\"\n", ")\n", "# EM: 53.9%\n", "# EM: 57.3%\n", "res_wi_ret_5k_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/oNxrNTtA/dxy-8-a40__RemoteEditSystem_plhs/cache\"\n", ")\n", "# EM: 58.4%\n", "res_wo_ret_path = pathlib.Path(\n", "    \"/mnt/efs/augment/eval/jobs/WwJaL4xs/dxy-8-a40__RemoteEditSystem_n3xa/cache\"\n", ")\n", "\n", "name2path: dict[str, pathlib.Path] = {\n", "    \"16K\": res_wi_ret_16k_path,\n", "    \"8K\": res_wi_ret_8k_path,\n", "    \"5K\": res_wi_ret_5k_path,\n", "    \"Non\": res_wo_ret_path,\n", "}\n", "\n", "\n", "def is_different(list_of_success: list[bool]):\n", "    first_element = list_of_success[0]\n", "    for element in list_of_success:\n", "        if element != first_element:\n", "            return True\n", "    return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Make a request\n", "from research.eval.harness.systems.remote_edit_system import RemoteEditSystem\n", "\n", "index = 4\n", "system = RemoteEditSystem(\n", "    url=\"https://dev-dxy.us-central.api.augmentcode.com\",\n", "    inference_mode=\"api_proxy\",\n", "    model=\"droid-33B-FP8-R1-edit\",\n", ")\n", "sample, repo = task[index]\n", "system.clear_retriever()\n", "system.add_docs(repo)\n", "model_input = task._edit_input_to_model_input(sample)\n", "result = system.generate(model_input)\n", "print(sample.path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result.extra_output.additional_info[\"request_id\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["content = repo[0].text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(content[4003 : 4003 + 20].encode())\n", "print(sample.prefix[:20].encode())\n", "print(content[11664 - 20 : 11664].encode())\n", "print(sample.suffix[-20:].encode())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sample.prefix_begin)\n", "print(sample.suffix_end)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for idx in range(len(task)):\n", "    sample, _ = task[idx]\n", "    cur_results, cur_successes = {}, []\n", "    for key, path in name2path.items():\n", "        cur_json = utils_for_file.read_json(path / f\"{idx}.json\")\n", "        cur_json[\"is_correct\"] = cur_json[\"generated_text\"] == sample.updated_code\n", "        cur_results[key] = cur_json\n", "        cur_successes.append(cur_json[\"is_correct\"])\n", "    if not is_different(cur_successes):\n", "        continue\n", "    print(f\"Index = {idx} makes different.\")\n", "    for key, cur_result in cur_results.items():\n", "        print(\n", "            f\"{key}::: success={cur_result['is_correct']}, RI={cur_result['request_id']}\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def display(index: int, context: int = 3):\n", "    sample, _ = task[index]\n", "    print(f\"File Name: {sample.path}\")\n", "    res_wi_ret = utils_for_file.read_json(res_wi_ret_8k_path / f\"{index}.json\")\n", "    res_wo_ret = utils_for_file.read_json(res_wo_ret_path / f\"{index}.json\")\n", "    print(f\"RI w/. retrieval: {res_wi_ret['request_id']}\")\n", "    print(f\"RI w/o retrieval: {res_wo_ret['request_id']}\")\n", "    print(f\"Instruction: {sample.instruction}\")\n", "    print(\n", "        \"-\" * 32\n", "        + \" non-retrieval\"\n", "        + f\" ({res_wo_ret['generated_text']==sample.updated_code}) \"\n", "        + \"-\" * 32\n", "    )\n", "    print(utils_for_str.get_last_n_lines(sample.prefix, context), end=\"\")\n", "    print(\n", "        utils_for_str.get_diff_str(sample.selected_code, res_wo_ret[\"generated_text\"])\n", "    )\n", "    print(utils_for_str.get_first_n_lines(sample.suffix, context), end=\"\\n\\n\")\n", "    print(\n", "        \"-\" * 32\n", "        + \" retrieval\"\n", "        + f\" ({res_wi_ret['generated_text']==sample.updated_code}) \"\n", "        + \"-\" * 32\n", "    )\n", "    print(utils_for_str.get_last_n_lines(sample.prefix, context), end=\"\")\n", "    print(\n", "        utils_for_str.get_diff_str(sample.selected_code, res_wi_ret[\"generated_text\"])\n", "    )\n", "    print(utils_for_str.get_first_n_lines(sample.suffix, context), end=\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(5, context=10)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(14, context=10)\n", "# index = 0\n", "# print(task[index][0].updated_code)\n", "# print(task[index][0].selected_code)\n", "# print(res_wo_ret[\"generated_text\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}