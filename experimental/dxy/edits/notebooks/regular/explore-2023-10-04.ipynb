{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_dataclass\n", "from experimental.dxy.edits.data_type import EditData\n", "\n", "data_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/json-2023-10-06-xcache-del_01\"\n", ")\n", "json_files = sorted(list(data_dir.glob(\"*-*.json\")), key=lambda x: str(x))\n", "json_files = [str(x) for x in json_files]\n", "print(f\"There are {len(json_files)} files in total.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data: EditData = utils_for_dataclass.create_from_json_file(EditData, json_files[0])\n", "for line in data.lines:\n", "    print(line)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_selected_code())\n", "print(\"-\" * 100)\n", "print(data.get_modified_code())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "\n", "lrange = data.get_lrange()\n", "print(\n", "    termcolor.colored(data.get_prefix_via_lrange(lrange=lrange), color=\"green\"), end=\"\"\n", ")\n", "print(\n", "    termcolor.colored(data.get_content_via_lrange(lrange=lrange), color=\"red\"), end=\"\"\n", ")\n", "print(termcolor.colored(data.get_suffix_via_lrange(lrange=lrange), color=\"blue\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.new_file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import openai\n", "from termcolor import colored\n", "import research.core.utils_for_str as str_lib\n", "import experimental.dxy.edits.api_lib as api_lib\n", "\n", "\n", "system_prompt = r\"\"\"You are helpful and super smart Python expert that can solve any Python-related problem reasonable and correctly.\n", "You always think step by step and try to give helpful and useful solutions for the user.\"\"\"\n", "\n", "typical_instructions = [\n", "    \"put a nice docstring to well explain this class\",\n", "    \"use promise chain instead of async\",\n", "    \"extend it to else condition\",\n", "    \"fix errors\",\n", "    \"move the accesstoken to url as a parameter of token=\",\n", "    \"make this line more readable\",\n", "    \"generate floats, not only ints\",\n", "    \"add a param to choose upper/lower\",\n", "    \"change all command names to snake case\",\n", "    \"replace True by False\",\n", "    \"this function seems will never trigger the default branch of executeChat, fix it\",\n", "    \"refactor this function by using optional_map func\",\n", "    \"simplify the cache control\",\n", "    \"console log all the ids of each file\",\n", "    \"Refactor the code. Change config.foo to CONFIG['foo']\",\n", "    \"change Optional[str] to str | None\",\n", "    \"write test train split using df pandas\",\n", "    \"instead of hard code options in the quick pick array, randomly generate 10 numbers as options\",\n", "    \"write a short pytorch module that implements linear regression, have comments\",\n", "    \"polish it with concise codes\",\n", "]\n", "typical_instruct_str = \"\\n\".join(f\"- {x}\" for x in typical_instructions)\n", "\n", "\n", "def build_messages(\n", "    selected_code: str,\n", "    modified_code: str,\n", "    prefix: str,\n", "    suffix: str,\n", "    use_sample_instruction: bool = True,\n", "):\n", "    messages = []\n", "    messages.append(\n", "        \"\"\"I was helping my friend to write the Python code.\n", "He gives me some Python codes and asks me to follow his instruction to modify a piece of code.\n", "I will give you the entire codes, the target piece of codes, and the modified codes by myself.\n", "Can you guess what is his original instructions.\"\"\"\n", "    )\n", "    messages.append(\n", "        \"Sure, I will guess, what is the entire codes, and the target piece of codes?\"\n", "    )\n", "    prefix_last_15_lines = str_lib.get_last_n_lines(prefix, 15)\n", "    messages.append(\n", "        f\"\"\"Here is the preceding part of the target piece of codes:\n", "```python\n", "{prefix_last_15_lines}\n", "```\n", "\"\"\"\n", "    )\n", "    messages.append(\n", "        \"Got it, what is the target piece of codes and its possible succeeding part?\"\n", "    )\n", "    messages.append(\n", "        f\"\"\"Here is the target piece of codes:\n", "```python\n", "{selected_code}\n", "```\n", "\"\"\"\n", "    )\n", "    messages.append(\n", "        \"Got it, what is its succeeding part and your final modified codes?\"\n", "    )\n", "    suffix_first_15_lines = str_lib.get_first_n_lines(suffix, 15)\n", "    messages.append(\n", "        f\"\"\"Here is the succeeding part of the target piece of codes:\n", "```python\n", "{suffix_first_15_lines}\n", "```\n", "\"\"\"\n", "    )\n", "    messages.append(\"Got<PERSON>, what is the modified version?\")\n", "    messages.append(\n", "        f\"\"\"Here is the modified version\n", "```python\n", "{modified_code}\n", "```\n", "\"\"\"\n", "    )\n", "    if use_sample_instruction:\n", "        messages[\n", "            -1\n", "        ] += f\"\"\"Oh, by the way, my friend's instruction is in a casual and natural tone, and here are some of his previous instructions:\n", "{typical_instruct_str}\n", "When you guess his instruction, please also (1) try your best to simulate his tone, (2) put your guessed instruction within two ``` to quote.\n", "\"\"\"\n", "    else:\n", "        messages[\n", "            -1\n", "        ] += f\"\"\"Oh, by the way, when you guess his instruction, please put your guessed instruction within two ``` to quote.\"\"\"\n", "    return messages\n", "\n", "\n", "def predict_edit_data(x: EditData, debug: bool = True) -> str:\n", "    selected_code: str = x.get_selected_code()\n", "    modified_code: str = x.get_modified_code()\n", "    lrange = x.get_lrange()\n", "    prefix = x.get_prefix_via_lrange(lrange=lrange)\n", "    prefix_last_15_lines = str_lib.get_last_n_lines(prefix, 15)\n", "    suffix = x.get_suffix_via_lrange(lrange=lrange)\n", "    suffix_first_10_lines = str_lib.get_first_n_lines(suffix, 10)\n", "    messages = build_messages(\n", "        selected_code, modified_code, prefix=prefix, suffix=suffix\n", "    )\n", "    instruction = api_lib.generate_response_via_chat(messages, system_prompt)\n", "    if debug:\n", "        print(\"-\" * 100)\n", "        print(colored(prefix_last_15_lines, color=\"green\"), end=\"\")\n", "        if selected_code:\n", "            print(colored(selected_code, color=\"blue\"), end=\"\")\n", "        else:\n", "            print(colored(\"<<|empty-place-holder|>>\", color=\"blue\"), end=\"\")\n", "        print(colored(suffix_first_10_lines, color=\"green\"), end=\"\\n\")\n", "        print(\"-\" * 100)\n", "        print(colored(prefix_last_15_lines, color=\"green\"), end=\"\")\n", "        print(colored(modified_code, color=\"red\"), end=\"\")\n", "        print(colored(suffix_first_10_lines, color=\"green\"), end=\"\\n\")\n", "        print(\"-\" * 100)\n", "        print(colored(instruction, color=\"blue\"))\n", "        print(\"-\" * 100)\n", "    return instruction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data: EditData = utils_for_dataclass.create_from_json_file(EditData, json_files[0])\n", "_ = predict_edit_data(data, debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(\n", "    utils_for_dataclass.create_from_json_file(EditData, json_files[1]), debug=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(\n", "    utils_for_dataclass.create_from_json_file(EditData, json_files[2]), debug=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(\n", "    utils_for_dataclass.create_from_json_file(EditData, json_files[3]), debug=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(\n", "    utils_for_dataclass.create_from_json_file(EditData, json_files[4]), debug=True\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(\n", "    utils_for_dataclass.create_from_json_file(EditData, json_files[5]), debug=True\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.17"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}