{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import re\n", "import termcolor\n", "import random\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import Llama2Tokenizer\n", "\n", "tokenizer = Llama2Tokenizer()\n", "\n", "\n", "def filter_string(s):\n", "    return \"\".join([char for char in s.lower() if char.isalpha() or char == \" \"])\n", "\n", "\n", "def extract_content(input_string):\n", "    pattern = r\"<INST>(.*?)<SELECTED>\"\n", "    matches = re.findall(pattern, input_string, re.DOTALL)\n", "\n", "    # Strip each match of leading and trailing whitespaces\n", "    return [match.strip() for match in matches]\n", "\n", "\n", "def split_list(lst: list[int], delimiter: int):\n", "    result = []\n", "    current_subsequence = []\n", "    for item in lst:\n", "        if item == delimiter:\n", "            if current_subsequence:  # Avoid appending empty lists\n", "                result.append(current_subsequence)\n", "                current_subsequence = []\n", "        else:\n", "            current_subsequence.append(item)\n", "    if current_subsequence:  # Append the last subsequence if it's not empty\n", "        result.append(current_subsequence)\n", "    return result\n", "\n", "\n", "def show_dataset_info(dataset: MMapIndexedDataset):\n", "    print(f\"There are {len(dataset)} examples\")\n", "    indexes = [0, random.randint(0, len(dataset) - 1), len(dataset) - 1]\n", "    indexes = sorted(list(set(indexes)))\n", "    for index in indexes:\n", "        example = dataset[index]\n", "        negative_num = np.sum(example < 0).item()  # type: ignore\n", "        print(\n", "            termcolor.colored(\n", "                f\"Index={index:5d}, negative_num={negative_num}\", color=\"green\"\n", "            )\n", "        )\n", "        example: list[int] = np.abs(example).tolist()\n", "        parts = split_list(example, tokenizer.eos_id)\n", "        parts = [x for x in parts if x != [tokenizer.eos_id]]\n", "        print(\n", "            termcolor.colored(\n", "                f\"There are {len(parts)} unpacked examples.\", color=\"green\"\n", "            )\n", "        )\n", "        for j, part in enumerate(parts):\n", "            print(termcolor.colored(f\"Part {j:5d}/{len(parts):5d}\", color=\"blue\"))\n", "            results = tokenizer.detok<PERSON>ze(part)\n", "            print(results)\n", "            print(\"*\" * 100 + \"\\n\")\n", "        print(\"-\" * 100 + \"\\n\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "Get 48435 instructions.\n"]}], "source": ["# dataset = MMapIndexedDataset(\"/mnt/efs/augment/user/dxy/datasets/edit.ft/py-inhouse-commitpackft-2023-11-02/train-v3_r1n_s4096_onlytgt_pad\")\n", "dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-11-02/valid-v3_r1n_s4096_onlytgt_pack\"\n", ")\n", "instructions = []\n", "for i in range(len(dataset)):\n", "    example: list[int] = np.abs(dataset[i]).tolist()\n", "    text: str = tokenizer.detokenize(example)\n", "    for inst in extract_content(text):\n", "        instructions.append(inst)\n", "print(f\"Get {len(instructions)} instructions.\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["#unique_instructions = 47342\n", "#unique_v2_instructions = 47165\n"]}], "source": ["unique_instructions = set(instructions)\n", "print(f\"#unique_instructions = {len(unique_instructions)}\")\n", "unique_v2_instructions = list(set([filter_string(x) for x in unique_instructions]))\n", "print(f\"#unique_v2_instructions = {len(unique_v2_instructions)}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["add docs and autodocs tasks\n", "add tests for multiple gibson inserts\n", "refactor the function to check for references to remote and pendingreceiver in interfaces structs and unions and import them only if they are referenced\n", "remove unnecessary import of logging\n", "replace the createdomain method with a createstorageroot method\n", "add a new function to test the dimensions of the sram table and compare it with the expected values\n", "stop defining pluginunloaded functions that arent doing anything\n", "add created and modified fields to receipt\n", "fix model id to auto increment\n", "replace the index function with a userviewset class that uses django rest framework and connects to a user model\n", "import czmlversion packet preamble from czml and write tests for preamble\n", "fix data provider example file\n", "add more parameters to the function\n", "results          resultspredictions   for  in rangebatchsize   noqa f        resultsscores   for  in rangebatchsize   noqa f        resultsattention   for  in rangebatchsize   noqa f        resultsgoldscore    batchsize        resultsbatch  batch        resultsaliveseq  aliveseq        resultstopklogprobs  topklogprobs        resultshypotheses  hypotheses\n", "add run to the vm which iterates through all the bytecodes and executes them\n", "complete unfinished code committed by mistake\n", "add two new methods to compute the expected scale and the expected log scale of a connection averaging over c\n", "remove the bestninedepth function\n", "remove unused nhs database mockup\n", "replace fileio usage with open\n", "extend the toindex method to handle different data types like list and set not just numpy arrays\n", "modify the response creation and add a process to create a versions bucket if it fails delete the newly created bucket and handle the errors accordingly\n", "add a condition to check if the regressor is not dmdc when control input u is passed\n", "rearrange the code to check for <PERSON><PERSON><PERSON> first\n", "fix quick union functions issue\n", "handle the exception when getting data\n", "add the choose test and take test to urls\n", "remove logic for iterating directories to search for config file\n", "add a function to read a dataframe from a file\n", "replace the code for printing the arabic range with the printrange function\n"]}], "source": ["for index in range(30):\n", "    print(unique_v2_instructions[index])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}