{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import dataclasses\n", "import requests\n", "from research.core import utils_for_dataclass\n", "from research.model_server.model_server_requests import (\n", "    BlobPayload,\n", "    BatchUploadRequest,\n", "    BatchUploadResponse)\n", "\n", "\n", "api_token = os.environ.get(\"AUGMENT_TOKEN\", None)\n", "assert api_token is not None\n", "\n", "\n", "def get_models(url: str = \"https://dev-dxy.us-central.api.augmentcode.com\"):\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {api_token}\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-request-id\": \"00000000-0000-0000-0000-000000000001\",\n", "        \"x-request-session-id\": \"11111111-1111-1111-1111-111111111112\",\n", "    }\n", "    response = requests.post(\n", "        f\"{url}/get-models-api-auth\",\n", "        json={},\n", "        headers=headers,\n", "        timeout=120,\n", "    )\n", "    results = response.json()\n", "    default_model = results[\"default_model\"]\n", "    print(f\"The default model is : {default_model}\")\n", "    models, names = results['models'], []\n", "    for index, model in enumerate(models):\n", "        print(f\"[{index}/{len(models)}] : {model['name']}\")\n", "        names.append(model['name'])\n", "    return names\n", "\n", "\n", "def upload_blob(path: str, content: str, url: str = \"https://dev-dxy.us-central.api.augmentcode.com\"):\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {api_token}\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-request-id\": \"00000000-0000-0000-0000-000000000001\",\n", "        \"x-request-session-id\": \"11111111-1111-1111-1111-111111111112\",\n", "    }\n", "    blob = BlobPayload(blob_name=\"\", path=path, content=content)\n", "    batch_upload_request = BatchUploadRequest(blobs=[blob])\n", "    response = requests.post(\n", "        f\"{url}/batch-upload-api-auth\",\n", "        json=dataclasses.asdict(batch_upload_request),\n", "        headers=headers,\n", "        timeout=120,\n", "    )\n", "    utils_for_dataclass.create_from_dict(BatchUploadResponse, response.json())\n", "    return response"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The default model is : roguesl-farpref-16B-eth6-c\n", "[0/13] : roguesl-plain-16B-fp8-eth6-04-1-c\n", "[1/13] : roguesl-plainv2-16B-eth6-c\n", "[2/13] : roguesl-16B-fp8-eth6-04-1-c\n", "[3/13] : roguesl-farpref-16B-eth6-c\n", "[4/13] : deeproguesl-p-33B-eth6-04-1\n", "[5/13] : ender-16B-ethanol6-04-1-c\n", "[6/13] : roguesl-16B-fp8-eth6-16-1-c\n", "[7/13] : rogue-16B-fp8-ethanol6-04-1-c\n", "[8/13] : droid-33B-FP8-R1-edit\n", "[9/13] : ender-noin-16B-ethanol6-04-1-c\n", "[10/13] : ender-noin4m-16B-eth6-c\n", "[11/13] : roguesl-farpref-16B-seth6-p1024\n", "[12/13] : roguesl-farpref4m-16B-eth6\n", "The default model is : ender-7B-ethanol6-04-1\n", "[0/2] : droid-33B-FP8-R1-edit\n", "[1/2] : ender-7B-ethanol6-04-1\n"]}], "source": ["_ = get_models(\"https://dogfood.api.augmentcode.com\")\n", "_ = get_models()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = upload_blob(\"test/test.py\", \"print('hello')\", \"https://dogfood.api.augmentcode.com\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(type(response.status_code))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = utils_for_dataclass.create_from_dict(BatchUploadResponse, response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def send_request_to_api_proxy(\n", "    data: dict,\n", "    model: str = \"droid-1B-BF16-v1-edit\",\n", "    url: str = \"https://dev-dxy.us-central.api.augmentcode.com\",\n", "):\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {api_token}\",\n", "        \"Content-Type\": \"application/json\",\n", "        \"x-request-id\": \"00000000-0000-0000-0000-000000000001\",\n", "        \"x-request-session-id\": \"11111111-1111-1111-1111-111111111112\",\n", "    }\n", "\n", "    request_data = {\n", "        \"model\": model,\n", "        \"prefix\": data[\"prefix\"],\n", "        \"selected_text\": data[\"selected_code\"],\n", "        \"suffix\": data[\"suffix\"],\n", "        \"instruction\": data[\"instruction\"],\n", "        \"blob_names\": [\"518d395ef71d0820c5d6890e5feb27d54dd1e892bed292bb6c0b41fb3ad7e42b\"],\n", "    }\n", "\n", "    response = requests.post(\n", "        f\"{url}/edit-api-auth\",\n", "        json=request_data,\n", "        headers=headers,\n", "        timeout=120,\n", "    )\n", "    result = response.json()[\"text\"]\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.eval.edit.data_tools.collected import load_data_git_conflict_format\n", "\n", "data_root = pathlib.Path(\"/home/<USER>/src/augment/research/eval/edit/data\")\n", "data = load_data_git_conflict_format(data_root, 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = data[0]\n", "results = send_request_to_api_proxy(\n", "    {\n", "        \"prefix\": x[\"prefix\"] * 1000,\n", "        \"selected_code\": x[\"code\"] * 10,\n", "        \"suffix\": x[\"suffix\"] * 1000,\n", "        \"instruction\": x[\"instruction\"],\n", "    },\n", "    model=\"droid-1B-BF16-v1-edit\",\n", ")\n", "# print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(x[\"code\"]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(x[\"instruction\"])\n", "print(x[\"code\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}