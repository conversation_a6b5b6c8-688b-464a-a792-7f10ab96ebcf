{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 25813 edit data and 25813 instructions\n", "skip_due_to_no_blocks=100\n", "skip_due_to_multiple_blocks=287\n"]}], "source": ["import pathlib\n", "import tqdm\n", "import json\n", "import termcolor\n", "from augment.research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import (\n", "    predict_edit_data_v0,\n", "    predict_edit_data_v1,\n", ")\n", "\n", "file_names = (\n", "    \"instruct-del_00_00-per40.json\",\n", "    \"instruct-del_01_01-per40.json\",\n", "    \"instruct-del_02_02-per40.json\",\n", "    \"instruct-del_03_03-per40.json\",\n", "    \"instruct-del_04_04-per40.json\",\n", "    \"instruct-del_05_05-per40.json\",\n", "    \"instruct-del_06_06-per40.json\",\n", "    \"instruct-del_07_07-per40.json\",\n", "    \"instruct-del_08_08-per40.json\",\n", "    \"instruct-del_09_09-per40.json\",\n", "    \"instruct-del_10_10-per40.json\",\n", "    \"instruct-del_11_20-per10.json\",\n", "    \"instruct-del_21_30-per10.json\",\n", ")\n", "json_file_dir = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13/\"\n", ")\n", "\n", "\n", "all_edit_data, all_instruction = [], []\n", "skip_due_to_no_blocks = []\n", "skip_due_to_multiple_blocks = []\n", "\n", "for json_file in file_names:\n", "    json_file_path = pathlib.Path(json_file_dir) / json_file\n", "    with json_file_path.open(\"r\") as f:\n", "        for line in f:\n", "            xdict = json.loads(line)\n", "            data = utils_for_dataclass.create_from_dict(EditData, xdict[\"edit_data\"])\n", "            instruction = xdict[\"instruction\"]\n", "            blocks = utils_for_str.extract_code_within_backticks(instruction)\n", "            if len(blocks) == 0:\n", "                # print(f\"No blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_no_blocks.append((data, instruction))\n", "                continue\n", "            elif len(blocks) > 1:\n", "                # print(f\"Too many blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_multiple_blocks.append((data, instruction))\n", "                continue\n", "            final_instruction = blocks[0]\n", "            if final_instruction[0] == '\"' and final_instruction[-1] == '\"':\n", "                final_instruction = final_instruction[1:-1]\n", "            elif final_instruction[0] == \"`\" and final_instruction[-1] == \"`\":\n", "                final_instruction = final_instruction[1:-1]\n", "            all_edit_data.append(data)\n", "            all_instruction.append(final_instruction)\n", "print(\n", "    f\"There are {len(all_edit_data)} edit data and {len(all_instruction)} instructions\"\n", ")\n", "print(f\"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}\")\n", "print(f\"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ ]             raise Exception(\"Http core is null\") \n", "[ ]         return await self.request_adapter.send_primitive_async(request_info, \"bytes\", response_handler, error_mapping)\n", "[ ]     \n", "\u001b[32m[+]     def to_get_request_information(self,request_configuration: Optional[GetTeamsDeviceUsageTotalUserCountsWithPeriodRequestBuilderGetRequestConfiguration] = None) -> RequestInformation:\n", "\u001b[0m\u001b[32m[+]         \"\"\"\n", "\u001b[0m\u001b[32m[+]         Invoke function getTeamsDeviceUsageTotalUserCounts\n", "\u001b[0m\u001b[32m[+]         Args:\n", "\u001b[0m\u001b[32m[+]             requestConfiguration: Configuration for the request such as headers, query parameters, and middleware options.\n", "\u001b[0m\u001b[32m[+]         Returns: RequestInformation\n", "\u001b[0m\u001b[32m[+]         \"\"\"\n", "\u001b[0m\u001b[32m[+]         request_info = RequestInformation()\n", "\u001b[0m\u001b[32m[+]         request_info.url_template = self.url_template\n", "\u001b[0m\u001b[32m[+]         request_info.path_parameters = self.path_parameters\n", "\u001b[0m\u001b[32m[+]         request_info.http_method = Method.GET\n", "\u001b[0m\u001b[32m[+]         if request_configuration:\n", "\u001b[0m\u001b[32m[+]             request_info.add_request_headers(request_configuration.headers)\n", "\u001b[0m\u001b[32m[+]             request_info.add_request_options(request_configuration.options)\n", "\u001b[0m\u001b[32m[+]         return request_info\n", "\u001b[0m\u001b[32m[+]     \n", "\u001b[0m[ ]     @dataclass\n", "[ ]     class GetTeamsDeviceUsageTotalUserCountsWithPeriodRequestBuilderGetRequestConfiguration():\n", "[ ]         \"\"\"\n", "\n", "\u001b[34minstruction: Define a function to get request information\u001b[0m\n"]}], "source": ["index = 399\n", "all_edit_data[index].show_lines()\n", "print(\"\")\n", "print(termcolor.colored(f\"instruction: {all_instruction[index]}\", color=\"blue\"))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ ]                 if msg.args[0] != 4:\n", "[ ]                     raise\n", "[ ]                 read = []\n", "\u001b[31m[-]             if directRd in read:\n", "\u001b[0m\u001b[31m[-]                 # read output from pseudo terminal stdout/stderr, and pass to \n", "\u001b[0m\u001b[31m[-]                 # terminal and log\n", "\u001b[0m\u001b[31m[-]                 try:\n", "\u001b[0m\u001b[31m[-]                     output = os.read(directRd, BUFFER)\n", "\u001b[0m\u001b[31m[-]                 except OSError, msg:\n", "\u001b[0m\u001b[31m[-]                     if msg.errno == errno.EIO: \n", "\u001b[0m\u001b[31m[-]                         # input/output error - pipe closed\n", "\u001b[0m\u001b[31m[-]                         # shut down logger\n", "\u001b[0m\u001b[31m[-]                         break\n", "\u001b[0m\u001b[31m[-]                         if unLogged:\n", "\u001b[0m\u001b[31m[-]                             lexer.write(unLogged + '\\n')\n", "\u001b[0m\u001b[31m[-]                     elif msg.errno != errno.EINTR:\n", "\u001b[0m\u001b[31m[-]                         # EINTR is due to an interrupted read - that could be\n", "\u001b[0m\u001b[31m[-]                         # due to a SIGWINCH signal.  Raise any other error\n", "\u001b[0m\u001b[31m[-]                         raise\n", "\u001b[0m\u001b[31m[-]                 else:\n", "\u001b[0m\u001b[31m[-]                     lexer.write(output)\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-]                 if output:\n", "\u001b[0m\u001b[31m[-]                     # always read all of directWrite before reading anything else\n", "\u001b[0m\u001b[31m[-]                     continue\n", "\u001b[0m[ ] \n", "[ ]             if ptyFd in read:\n", "\u001b[31m[-]                 # read output from pseudo terminal stdout/stderr, and pass to \n", "\u001b[0m\u001b[32m[+]                 # read output from pseudo terminal stdout/stderr, and pass to\n", "\u001b[0m[ ]                 # terminal and log\n", "\u001b[32m[+] \n", "\u001b[0m[ ]                 try:\n", "[ ]                     output = os.read(ptyFd, BUFFER)\n", "[ ]                 except <PERSON><PERSON><PERSON>r, msg:\n", "\n", "\u001b[34minstruction: Remove the block of code handling the 'directRd in read' condition.\u001b[0m\n"]}], "source": ["index = -100\n", "all_edit_data[index].show_lines()\n", "print(\"\")\n", "print(termcolor.colored(f\"instruction: {all_instruction[index]}\", color=\"blue\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}