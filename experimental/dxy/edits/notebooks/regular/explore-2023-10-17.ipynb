{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '/home/<USER>/datasets/edit/organized-basic-instruct-2023-10-13/instruct-del_01_01-per60.json'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/home/<USER>/src/augment/experimental/dxy/notebooks/edit/explore-2023-10-17.ipynb Cell 2\u001b[0m line \u001b[0;36m3\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d6478792d382d613430222c2273657474696e6773223a7b22686f7374223a227373683a2f2f38613430227d7d/home/<USER>/src/augment/experimental/dxy/notebooks/edit/explore-2023-10-17.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=27'>28</a>\u001b[0m \u001b[39mfor\u001b[39;00m json_file \u001b[39min\u001b[39;00m file_names:\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d6478792d382d613430222c2273657474696e6773223a7b22686f7374223a227373683a2f2f38613430227d7d/home/<USER>/src/augment/experimental/dxy/notebooks/edit/explore-2023-10-17.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=28'>29</a>\u001b[0m     json_file_path \u001b[39m=\u001b[39m pathlib\u001b[39m.\u001b[39mPath(json_file_dir) \u001b[39m/\u001b[39m json_file\n\u001b[0;32m---> <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d6478792d382d613430222c2273657474696e6773223a7b22686f7374223a227373683a2f2f38613430227d7d/home/<USER>/src/augment/experimental/dxy/notebooks/edit/explore-2023-10-17.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=29'>30</a>\u001b[0m     \u001b[39mwith\u001b[39;00m json_file_path\u001b[39m.\u001b[39;49mopen(\u001b[39m\"\u001b[39;49m\u001b[39mr\u001b[39;49m\u001b[39m\"\u001b[39;49m) \u001b[39mas\u001b[39;00m f:\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d6478792d382d613430222c2273657474696e6773223a7b22686f7374223a227373683a2f2f38613430227d7d/home/<USER>/src/augment/experimental/dxy/notebooks/edit/explore-2023-10-17.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=30'>31</a>\u001b[0m         \u001b[39mfor\u001b[39;00m line \u001b[39min\u001b[39;00m f:\n\u001b[1;32m     <a href='vscode-notebook-cell://attached-container%2B7b22636f6e7461696e65724e616d65223a222f6d796175676d656e742d6175676d656e742d6478792d382d613430222c2273657474696e6773223a7b22686f7374223a227373683a2f2f38613430227d7d/home/<USER>/src/augment/experimental/dxy/notebooks/edit/explore-2023-10-17.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=31'>32</a>\u001b[0m             xdict \u001b[39m=\u001b[39m json\u001b[39m.\u001b[39mloads(line)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/pathlib.py:1252\u001b[0m, in \u001b[0;36mPath.open\u001b[0;34m(self, mode, buffering, encoding, errors, newline)\u001b[0m\n\u001b[1;32m   1246\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39mopen\u001b[39m(\u001b[39mself\u001b[39m, mode\u001b[39m=\u001b[39m\u001b[39m'\u001b[39m\u001b[39mr\u001b[39m\u001b[39m'\u001b[39m, buffering\u001b[39m=\u001b[39m\u001b[39m-\u001b[39m\u001b[39m1\u001b[39m, encoding\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m,\n\u001b[1;32m   1247\u001b[0m          errors\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, newline\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m):\n\u001b[1;32m   1248\u001b[0m \u001b[39m    \u001b[39m\u001b[39m\"\"\"\u001b[39;00m\n\u001b[1;32m   1249\u001b[0m \u001b[39m    Open the file pointed by this path and return a file object, as\u001b[39;00m\n\u001b[1;32m   1250\u001b[0m \u001b[39m    the built-in open() function does.\u001b[39;00m\n\u001b[1;32m   1251\u001b[0m \u001b[39m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 1252\u001b[0m     \u001b[39mreturn\u001b[39;00m io\u001b[39m.\u001b[39;49mopen(\u001b[39mself\u001b[39;49m, mode, buffering, encoding, errors, newline,\n\u001b[1;32m   1253\u001b[0m                    opener\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_opener)\n", "File \u001b[0;32m/opt/conda/lib/python3.9/pathlib.py:1120\u001b[0m, in \u001b[0;36mPath._opener\u001b[0;34m(self, name, flags, mode)\u001b[0m\n\u001b[1;32m   1118\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_opener\u001b[39m(\u001b[39mself\u001b[39m, name, flags, mode\u001b[39m=\u001b[39m\u001b[39m0o666\u001b[39m):\n\u001b[1;32m   1119\u001b[0m     \u001b[39m# A stub for the opener argument to built-in open()\u001b[39;00m\n\u001b[0;32m-> 1120\u001b[0m     \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_accessor\u001b[39m.\u001b[39;49mopen(\u001b[39mself\u001b[39;49m, flags, mode)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/home/<USER>/datasets/edit/organized-basic-instruct-2023-10-13/instruct-del_01_01-per60.json'"]}], "source": ["import pathlib\n", "import tqdm\n", "import json\n", "from augment.research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import (\n", "    predict_edit_data_v0,\n", "    predict_edit_data_v1,\n", ")\n", "\n", "file_names = (\n", "    \"instruct-del_00_00-per40.json\",\n", "    \"instruct-del_01_01-per60.json\",\n", "    \"instruct-del_02_02-per60.json\",\n", "    # \"instruct-del_03_03-per60.json\",\n", "    # \"instruct-del_04_04-per60.json\",\n", "    # \"instruct-del_05_05-per60.json\",\n", "    # \"instruct-del_11_20-per10.json\",\n", "    # \"instruct-del_21_30-per10.json\",\n", ")\n", "json_file_dir = \"/home/<USER>/datasets/edit/organized-basic-instruct-2023-10-13/\"\n", "\n", "\n", "all_edit_data, all_instruction = [], []\n", "skip_due_to_no_blocks = []\n", "skip_due_to_multiple_blocks = []\n", "\n", "for json_file in file_names:\n", "    json_file_path = pathlib.Path(json_file_dir) / json_file\n", "    with json_file_path.open(\"r\") as f:\n", "        for line in f:\n", "            xdict = json.loads(line)\n", "            data = utils_for_dataclass.create_from_dict(EditData, xdict[\"edit_data\"])\n", "            instruction = xdict[\"instruction\"]\n", "            blocks = utils_for_str.extract_code_within_backticks(instruction)\n", "            if len(blocks) == 0:\n", "                # import pdb; pdb.set_trace()\n", "                # print(f\"No blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_no_blocks.append((data, instruction))\n", "                continue\n", "            elif len(blocks) > 1:\n", "                # print(f\"Too many blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_multiple_blocks.append((data, instruction))\n", "                continue\n", "            all_edit_data.append(data)\n", "            all_instruction.append(blocks[0])\n", "print(\n", "    f\"There are {len(all_edit_data)} edit data and {len(all_instruction)} instructions\"\n", ")\n", "print(f\"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}\")\n", "print(f\"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index = 6\n", "all_edit_data[index].show_lines()\n", "print(all_instruction[index])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the index dataset\n", "import random\n", "import torch\n", "import tqdm\n", "import numpy as np\n", "from research.core import utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData\n", "from augment.experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "\n", "tokenizer_codellama = CodeLLaMATokenizer()\n", "tokenizer_llama2 = Llama2Tokenizer()\n", "tokenizer_wizard = Llama2Tokenizer(\n", "    model_path=\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-13B-V1.0/tokenizer.model\"\n", ")\n", "\n", "tokenizer_codellama_inv_vocab = tokenizer_codellama.inv_vocab\n", "tokenizer_llama2_inv_vocab = tokenizer_llama2.inv_vocab\n", "tokenizer_wizard_inv_vocab = tokenizer_wizard.inv_vocab\n", "\n", "vocab_size = max(\n", "    tokenizer_codellama.vocab_size,\n", "    tokenizer_llama2.vocab_size,\n", "    tokenizer_wizard.vocab_size,\n", ")\n", "print(f\"CodeLLaMATokenizer Vocab Size = {tokenizer_codellama.vocab_size}\")\n", "print(f\"Llama2Tokenizer Vocab Size = {tokenizer_llama2.vocab_size}\")\n", "print(f\"WizardCoder-Python-13B-V1.0 Vocab Size = {tokenizer_wizard.vocab_size}\")\n", "print(f\"Vocab Size = {vocab_size}\")\n", "for idx in tqdm.tqdm(range(vocab_size), total=vocab_size):\n", "    value_codellama = tokenizer_codellama_inv_vocab.get(idx, None)\n", "    value_llama2 = tokenizer_llama2_inv_vocab.get(idx, None)\n", "    value_wizard = tokenizer_wizard_inv_vocab.get(idx, None)\n", "    if not (value_codellama == value_llama2 == value_wizard):\n", "        print(\n", "            f\"Token Index = {idx} : {value_llama2} vs. {value_codellama} vs. {value_wizard}\"\n", "        )\n", "\n", "print(f\"tokenizer_codellama.prefix_id = {tokenizer_codellama.prefix_id}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sentencepiece import SentencePieceProcessor\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/hf/CodeLlama-7b-Python-hf/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b-Python/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/CodeLlama-7b-Instruct/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)\n", "\n", "sp_model = SentencePieceProcessor(\n", "    model_file=\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-13B-V1.0/tokenizer.model\"\n", ")\n", "print(sp_model.vocab_size())\n", "print(sp_model.piece_to_id(\"<s>\"))\n", "print(sp_model.piece_to_id(\"</s>\"))\n", "print(sp_model.piece_to_id(\"[PAD]\"))\n", "print(sp_model.piece_to_id(\"▁<PRE>\"))\n", "print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the index dataset\n", "import random\n", "import torch\n", "import numpy as np\n", "from research.core import utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData\n", "from augment.experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "\n", "tokenizer_codellama = CodeLLaMATokenizer()\n", "tokenizer_llama2 = Llama2Tokenizer()\n", "\n", "print(tokenizer_codellama.bos_id)\n", "print(tokenizer_llama2.bos_id)\n", "print(tokenizer_codellama.vocab_size)\n", "print(tokenizer_llama2.vocab_size)\n", "\n", "\n", "tokenizer = tokenizer_codellama\n", "output_path = pathlib.Path(\n", "    \"/home/<USER>/datasets/edit/fine-tune-data-2023-10-temp/train_001\"\n", ")\n", "output_dir = output_path.parent\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "\n", "for edit, instruction in zip(all_edit_data, all_instruction):\n", "    question, answer = EditPromptTemplate.create_prompt(edit, instruction, tokenizer)\n", "    tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "    builder.add_item(torch.Tensor(tensor))\n", "    builder.end_document()\n", "builder.finalize(output_path.with_suffix(\".idx\"))\n", "print(output_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "train_dataset = indexed_dataset.make_dataset(str(output_path), \"mmap\", skip_warmup=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_dataset = indexed_dataset.make_dataset(\n", "    \"/mnt/efs/augment/user/guy/data-pipeline/starcoder/llama2_exported_dataset_4096/dataset\",\n", "    \"mmap\",\n", "    skip_warmup=True,\n", ")\n", "print(len(train_dataset[0]))\n", "print(len(train_dataset[1]))\n", "print(len(train_dataset[2]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_edit_data[1].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = train_dataset[1]\n", "print(type(x))\n", "print(tokenizer.detokenize(x.tolist()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(train_dataset[0]))\n", "# print(tokenizer.detokenize())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer.inv_vocab[tokenizer.eos_id]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokenizer_codellama.vocab_size"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["inputs, targets = EditPromptTemplate.create_prompt(\n", "    all_edit_data[0], all_instruction[0], tokenizer_codellama\n", ")\n", "print(tokenizer_codellama.detokenize(inputs))\n", "print(\"-\" * 100)\n", "print(tokenizer_codellama.detokenize(targets))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_instruction[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_edit_data[0].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(targets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["skip_due_to_no_blocks[6][0].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v0(skip_due_to_no_blocks[6][0], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v1(skip_due_to_no_blocks[6][0], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v0(all_edit_data[-1], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data_v1(all_edit_data[-1], model=\"gpt-4\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(all_instruction[-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_edit_data[4].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_instruction[:100]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["skip_due_to_no_blocks[3].show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = skip_due_to_no_blocks[3]\n", "# print(data.get_prefix_via_lrange(data.get_lrange()))\n", "print(data.get_diff_code())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(skip_due_to_no_blocks[3].get_diff_code_in_context(0, 0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import tqdm\n", "from augment.research.core import utils_for_dataclass, utils_for_file\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import predict_edit_data_v0\n", "\n", "\n", "suffixes = [\n", "    \"del_00_00-per60\",\n", "    \"del_01_01-per60\",\n", "    \"del_02_02-per60\",\n", "    \"del_03_03-per60\",\n", "    \"del_04_04-per60\",\n", "    \"del_05_05-per60\",\n", "    \"del_06_06-per60\",\n", "    \"del_07_07-per60\",\n", "    \"del_08_08-per60\",\n", "    \"del_09_09-per60\",\n", "    \"del_10_10-per60\",\n", "    \"del_11_20-per15\",\n", "    \"del_21_30-per15\",\n", "]\n", "\n", "manager_by_suffix, total = {}, 0\n", "for suffix in suffixes:\n", "    cur_path = pathlib.Path(\n", "        f\"/home/<USER>/datasets/edit/organized-basic/manager-{suffix}.jsonl.zst\"\n", "    )\n", "    manager = EditDataManager.read_from_jsonl(str(cur_path))\n", "    manager_by_suffix[suffix] = manager\n", "    print(f\"There are {len(manager):5d} examples for {suffix}\")\n", "    total += len(manager)\n", "print(f\"There are {total} examples in total.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["unique_paths = set()\n", "for x in manager.edit_data_list:\n", "    unique_paths.add(x.file_path)\n", "print(f\"There are {len(unique_paths)} unique paths in the manager.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["edit_data_list = manager.get_data_list(21, 22)\n", "print(f\"There are {len(edit_data_list)} EditData elements.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = manager.edit_data_list[0]\n", "data.show_lines()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_diff_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.get_selected_code(), end=\"\")\n", "print(\"-\" * 100)\n", "print(data.get_modified_code(), end=\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import termcolor\n", "\n", "lrange = data.get_lrange()\n", "print(\n", "    termcolor.colored(data.get_prefix_via_lrange(lrange=lrange), color=\"green\"), end=\"\"\n", ")\n", "print(\n", "    termcolor.colored(data.get_content_via_lrange(lrange=lrange), color=\"red\"), end=\"\"\n", ")\n", "print(termcolor.colored(data.get_suffix_via_lrange(lrange=lrange), color=\"blue\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data.new_file_content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predict_edit_data = predict_edit_data_v0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(manager.edit_data_list[0], model=\"gpt-3.5-turbo\", debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[1], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[2], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["_ = predict_edit_data(edit_data_list[3], debug=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import typing\n", "\n", "import tqdm\n", "\n", "from experimental.dxy.edits import data_type\n", "from research.core import utils_for_dataclass, utils_for_file\n", "\n", "\n", "manager = data_type.EditDataManager(\n", "    expected_del_range=(8, 8), edit_data_list=[], max_data_per_add=100000\n", ")\n", "data_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/json-2023-10-13/raw-cache-del_08_08/\"\n", ")\n", "assert data_dir.exists(), data_dir\n", "jsonl_files = sorted(list(data_dir.glob(\"*-to-*-in-*.jsonl.zst\")), key=lambda x: str(x))\n", "jsonl_files = [str(x) for x in jsonl_files]\n", "print(f\"There are {len(jsonl_files)} files in total.\")\n", "for jsonl_file in tqdm.tqdm(jsonl_files):\n", "    batch_of_json_str = utils_for_file.read_jsonl_zst(jsonl_file)\n", "    print(f\"There are {len(batch_of_json_str)} elements from {jsonl_file}.\")\n", "    successes = []\n", "    for json_str in batch_of_json_str:\n", "        edit_data = utils_for_dataclass.create_from_json(data_type.EditData, json_str)\n", "        success = manager.append(\n", "            edit_data,\n", "            ignore_dup_file=False,\n", "            ignore_dup_code=False,\n", "            ignore_dup_path_code=True,\n", "            verbose=False,\n", "        )\n", "        successes.append(success)\n", "    print(\n", "        f\"Successfully insert {sum(successes)} / {len(batch_of_json_str)} data.\"\n", "        f\" Current manager size: {len(manager)}.\"\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}