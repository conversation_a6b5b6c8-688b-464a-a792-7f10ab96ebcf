{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "current_dir = pathlib.Path.cwd()\n", "eval_data_dir = current_dir / \"data\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import typing\n", "from research.eval.generation.execution import sandbox_execute\n", "\n", "\n", "def load_tests(data_dir: pathlib.Path) -> list[dict[str, typing.Any]]:\n", "    source_files = list(data_dir.glob(\"*.txt\"))\n", "    source_files = sorted(source_files)\n", "    print(f\"Loaded {len(source_files)} source files\")\n", "    all_data: list[dict[str, typing.Any]] = []\n", "    for source_file in source_files:\n", "        source_code = source_file.read_text()\n", "        python_file = source_file.with_suffix(\".py\")\n", "        assert python_file.exists()\n", "        python_content = python_file.read_text()\n", "        _, _, cur_edit_data = sandbox_execute(python_content, 1.0, [\"edits\"])\n", "        cur_edit_data = cur_edit_data[\"edits\"]\n", "        source_lines = source_code.splitlines(keepends=True)\n", "        for edit_data in cur_edit_data:\n", "            lrange = edit_data[\"lrange\"]\n", "            instructions = edit_data[\"instruction\"]\n", "            assert isinstance(\n", "                instructions, list\n", "            ), f\"type(instructions): {type(instructions)}\"\n", "            response = edit_data[\"response\"]\n", "            for instruction in instructions:\n", "                assert 0 <= lrange[0] <= lrange[1]\n", "                prefix = \"\".join(source_lines[: lrange[0]])\n", "                selected_code = \"\".join(source_lines[lrange[0] : lrange[1]])\n", "                suffix = \"\".join(source_lines[lrange[1] :])\n", "                all_data.append(\n", "                    {\n", "                        \"source_lines\": source_lines,\n", "                        \"lrange\": lrange,\n", "                        \"prefix\": prefix,\n", "                        \"selected_code\": selected_code,\n", "                        \"suffix\": suffix,\n", "                        \"instruction\": instruction,\n", "                        \"response\": response,\n", "                    }\n", "                )\n", "        print(\n", "            f\"Finish processing {source_file} ({len(all_data)} edit examples in total)\"\n", "        )\n", "    print(f\"Loaded {len(all_data)} examples.\")\n", "    return all_data"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from termcolor import colored\n", "\n", "\n", "def show_edit_data(data: dict):\n", "    \"\"\"Show the edit data in a git diff style.\"\"\"\n", "    print(colored(data[\"prefix\"], color=\"blue\"), end=\"\")\n", "    print(colored(\"<\" * 80, color=\"yellow\"), end=\"\\n\")\n", "    print(colored(data[\"selected_code\"], color=\"red\"), end=\"\")\n", "    print(colored(\">\" * 80, color=\"yellow\"), end=\"\\n\")\n", "    print(f\"instruction: {data['instruction']}\", end=\"\\n\")\n", "    print(colored(\">\" * 80, color=\"yellow\"), end=\"\\n\")\n", "    print(colored(data[\"response\"], color=\"green\"), end=\"\")\n", "    print(colored(\"=\" * 80, color=\"yellow\"), end=\"\\n\")\n", "    print(colored(data[\"suffix\"], color=\"blue\"), end=\"\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 7 source files\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0001.txt (3 edit examples in total)\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0002.txt (7 edit examples in total)\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0003.txt (8 edit examples in total)\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0004.txt (9 edit examples in total)\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0005.txt (11 edit examples in total)\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0006.txt (12 edit examples in total)\n", "Finish processing /home/<USER>/src/augment/experimental/dxy/edits/eval/data/0007.txt (13 edit examples in total)\n", "Loaded 13 examples.\n"]}], "source": ["eval_data_list = load_tests(eval_data_dir)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Show the -1-th example (red is the selected code and green is the updated code):\n", "\u001b[34m[\n", "  {\n", "    \"type\": \"https\",\n", "    \"instruction\": \"what is my ip\",\n", "    \"command\": \"hostname -i\"\n", "  },\n", "  {\n", "    \"type\": \"configuring\",\n", "    \"instruction\": \"list all configuration files\",\n", "    \"command\": \"ls -l /etc\"\n", "  },\n", "  {\n", "    \"type\": \"compression\",\n", "    \"instruction\": \"compress the file.txt\",\n", "    \"command\": \"gzip file.txt\"\n", "  },\n", "  {\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m    type terminal\n", "    instruction list the environment variables\n", "    command env\n", "  },\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: correct its format as others\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    \"type\": \"terminal\",\n", "    \"instruction\": \"list the environment variables\",\n", "    \"command\": \"env\"\n", "  },\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m  {\n", "    \"type\": \"versioning\",\n", "    \"instruction\": \"get the current branch\",\n", "    \"command\": \"git branch\"\n", "  }\n", "]\n", "\u001b[0m"]}], "source": ["index = -1\n", "\n", "print(\n", "    f\"# Show the {index}-th example (red is the selected code and green is the updated code):\"\n", ")\n", "show_edit_data(eval_data_list[index])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Evaluate 4/13-th example\n", "\u001b[34m\"\"\"Prepare Data for Edit.\"\"\"\n", "import logging\n", "import typing\n", "\n", "# import tenacity\n", "import openai\n", "from openai import openai_object\n", "\n", "# from openai import error as openai_error\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31mOpenAIChatModels = typing.Literal[\"gpt-4\", \"gpt-3.5-turbo\"]\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = \"gpt-4\",\n", ") -> str:\n", "    request_messages = [{\"role\": \"system\", \"content\": system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = \"user\" if idx == 0 else \"assistant\"\n", "        request_messages.append({\"role\": role, \"content\": message})\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: replace double quotes with single quote\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32mOpenAIChatModels = typing.Literal['gpt-4', 'gpt-3.5-turbo']\n", "\n", "logger = logging.getLogger(__name__)\n", "\n", "\n", "def generate_response_via_chat(\n", "    messages: list[str],\n", "    system_prompt: str,\n", "    temperature: float = 0.2,\n", "    max_tokens: int = 256,\n", "    model: OpenAIChatModels = 'gpt-4',\n", ") -> str:\n", "    request_messages = [{'role': 'system', 'content': system_prompt}]\n", "    for idx, message in enumerate(messages):\n", "        role = 'user' if idx == 0 else 'assistant'\n", "        request_messages.append({'role': role, 'content': message})\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m    response = openai.ChatCompletion.create(\n", "        model=model,\n", "        messages=request_messages,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=1,\n", "        frequency_penalty=0,\n", "        presence_penalty=0,\n", "    )\n", "    response = typing.cast(openai_object.OpenAIObject, response)\n", "    return response.choices[0].message.content\u001b[0m\n", "\n", "Exactly match groud truth: True\n", "#lines: 16 vs. 16\n"]}], "source": ["# Send request to an edit server, if the url does not work, please\n", "# contact <PERSON><PERSON><PERSON> or <PERSON><PERSON>.\n", "import copy\n", "import requests\n", "\n", "index = 4\n", "print(f\"Evaluate {index}/{len(eval_data_list)}-th example\")\n", "cur_edit_data = copy.deepcopy(eval_data_list[index])\n", "ground_truth = cur_edit_data[\"response\"]\n", "\n", "# edit_url = \"http://10.145.167.243:5000/edit\"\n", "# edit_url = \"http://216.153.49.246:5004/edit\"\n", "# edit_url = \"http://216.153.49.246:5005/edit\"\n", "# edit_url = \"http://10.146.8.94:5005/edit\"\n", "edit_url = \"https://dxy-edit.tenant-augment-eng.las1.ingress.coreweave.cloud:443/edit\"\n", "\n", "results = requests.post(\n", "    edit_url,\n", "    json={\n", "        \"prefix\": cur_edit_data[\"prefix\"],\n", "        \"suffix\": cur_edit_data[\"suffix\"],\n", "        \"selected_code\": cur_edit_data[\"selected_code\"],\n", "        \"instruction\": cur_edit_data[\"instruction\"],\n", "        \"top_k\": 5,\n", "        \"top_p\": 0.1,\n", "        \"temperature\": 0.5,\n", "        \"lines_in_prefix_suffix\": 5,\n", "        \"debug\": True,\n", "    },\n", "    timeout=60.0,\n", ")\n", "response = results.json()[\"response\"]\n", "\n", "cur_edit_data[\"response\"] = response\n", "show_edit_data(cur_edit_data)\n", "print(f\"\\n\\nExactly match groud truth: {response == ground_truth}\")\n", "lines_response, lines_gt = len(response.splitlines(keepends=True)), len(\n", "    ground_truth.splitlines(keepends=True)\n", ")\n", "print(f\"#lines: {lines_response} vs. {lines_gt}\")\n", "if lines_response == lines_gt and response != ground_truth:\n", "    for i, (line_response, line_gt) in enumerate(\n", "        zip(response.splitlines(keepends=True), ground_truth.splitlines(keepends=True))\n", "    ):\n", "        if line_response != line_gt:\n", "            print(f\"Line [{i}-GT]:{line_gt.rstrip()}\", end=\"\\n\")\n", "            print(f\"Line [{i}-PD]:{line_response.rstrip()}\", end=\"\\n\")\n", "elif response != ground_truth:\n", "    print(\"\\nGround truth:\")\n", "    print(ground_truth)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["import json\n", "PATH = '~/datasets/edit/commitpackft/data.jsonl'\n", "\n", "data = []\n", "\n", "with open(PATH) as f:\n", "    for line in f:\n", "        datum = json.loads(line[:-1])\n", "        data.append(datum)\n", "\n"]}], "source": ["print(results.json()[\"response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from research.core import utils_for_str\n", "# from experimental.dxy.demo.post_processing import post_process_for_edit\n", "\n", "# full_response = results.json()[\"full_response\"]\n", "# prefix = utils_for_str.get_last_n_lines(results.json()[\"prefix\"], 5)\n", "# suffix = utils_for_str.get_first_n_lines(results.json()[\"suffix\"], 5)\n", "# start_symbols, end_symbols = \"@\" * 24 + \"\\n\", \"&\" * 24 + \"\\n\"\n", "\n", "# response = post_process_for_edit(\n", "#     full_response,\n", "#     start_symbols=start_symbols,\n", "#     end_symbols=end_symbols,\n", "#     prefix=prefix,\n", "#     suffix=suffix,\n", "# )\n", "# print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(results.json()[\"response\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_str\n", "from research.models.remote_models import (\n", "    CodeLLaMA_LLaMACPP_Model,\n", "    DeepSeekInstruct_LLaMACPP_Model,\n", ")\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "\n", "model = DeepSeekInstruct_LLaMACPP_Model(url=\"http://127.0.0.1:8087\")\n", "\n", "index = -1\n", "cur_edit_data = copy.deepcopy(eval_data_list[index])\n", "prefix = cur_edit_data[\"prefix\"]\n", "suffix = cur_edit_data[\"suffix\"]\n", "selected_code = cur_edit_data[\"selected_code\"]\n", "instruction = cur_edit_data[\"instruction\"]\n", "\n", "prompt = f\"\"\"Edit Request: {instruction}\n", "\n", "Please follow these instructions carefully and restrictly:\n", "- The edit request is only applied to the code within the asterisks.\n", "- Keep the codes outside of the asterisks exactly identical.\n", "- Completely remove parts that are no longer needed; do not simply comment them out.\n", "- Preserve the original indentation and style.\n", "- In case of unclear instructions, maintain the original code.\n", "- Always keep the asterisks * in the outputs at its original location.\n", "- Do not output any additional text, headers, comments, suggestions, formatting, or explanations.\n", "\n", "Full code\n", "```\n", "{utils_for_str.get_last_n_lines(prefix, 24)}{'*' * 30}\n", "{selected_code}{'*' * 30}\n", "{utils_for_str.get_first_n_lines(suffix, 24)}\n", "```\n", "\"\"\"\n", "\n", "response = model.generate(\n", "    ModelInput(prefix=prompt),\n", "    options=GenerationOptions(max_generated_tokens=1024),\n", ")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.models.remote_models import DeepSeekInstruct_LLaMACPP_Model\n", "from research.core.model_input import ModelInput\n", "from research.models.meta_model import GenerationOptions\n", "import json\n", "\n", "model = DeepSeekInstruct_LLaMACPP_Model(url=\"http://127.0.0.1:8087\")\n", "results = model.generate(\n", "    ModelInput(prefix=prompt), options=GenerationOptions(max_generated_tokens=1024)\n", ")\n", "print(results)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}}, "nbformat": 4, "nbformat_minor": 2}