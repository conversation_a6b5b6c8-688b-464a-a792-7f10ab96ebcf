{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 25813 edit data.\n", "skip_due_to_no_blocks=100\n", "skip_due_to_multiple_blocks=287\n"]}], "source": ["import pathlib\n", "import tqdm\n", "import json\n", "import termcolor\n", "from augment.research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import (\n", "    predict_edit_data_v0,\n", "    predict_edit_data_v1,\n", ")\n", "\n", "file_names = (\n", "    \"instruct-del_00_00-per40.json\",\n", "    \"instruct-del_01_01-per40.json\",\n", "    \"instruct-del_02_02-per40.json\",\n", "    \"instruct-del_03_03-per40.json\",\n", "    \"instruct-del_04_04-per40.json\",\n", "    \"instruct-del_05_05-per40.json\",\n", "    \"instruct-del_06_06-per40.json\",\n", "    \"instruct-del_07_07-per40.json\",\n", "    \"instruct-del_08_08-per40.json\",\n", "    \"instruct-del_09_09-per40.json\",\n", "    \"instruct-del_10_10-per40.json\",\n", "    \"instruct-del_11_20-per10.json\",\n", "    \"instruct-del_21_30-per10.json\",\n", ")\n", "json_file_dir = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13/\"\n", ")\n", "\n", "\n", "all_edit_data = []\n", "skip_due_to_no_blocks = []\n", "skip_due_to_multiple_blocks = []\n", "\n", "for json_file in file_names:\n", "    json_file_path = pathlib.Path(json_file_dir) / json_file\n", "    with json_file_path.open(\"r\") as f:\n", "        for line in f:\n", "            xdict = json.loads(line)\n", "            data = utils_for_dataclass.create_from_dict(EditData, xdict[\"edit_data\"])\n", "            instruction = xdict[\"instruction\"]\n", "            blocks = utils_for_str.extract_code_within_backticks(instruction)\n", "            if len(blocks) == 0:\n", "                # print(f\"No blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_no_blocks.append((data, instruction))\n", "                continue\n", "            elif len(blocks) > 1:\n", "                # print(f\"Too many blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_multiple_blocks.append((data, instruction))\n", "                continue\n", "            final_instruction = blocks[0]\n", "            if final_instruction[0] == '\"' and final_instruction[-1] == '\"':\n", "                final_instruction = final_instruction[1:-1]\n", "            elif final_instruction[0] == \"`\" and final_instruction[-1] == \"`\":\n", "                final_instruction = final_instruction[1:-1]\n", "            data.instruction = final_instruction\n", "            all_edit_data.append(data)\n", "print(f\"There are {len(all_edit_data)} edit data.\")\n", "print(f\"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}\")\n", "print(f\"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ ]             return False\n", "[ ] \n", "[ ]         if status_code == HTTP_CODE_ZERO:\n", "\u001b[31m[-]             bambou_log.error(\"NURESTConnection: Connection error with code 0. Sending NUNURESTConnectionFailureNotification notification and exiting.\")\n", "\u001b[0m\u001b[32m[+]             bambou_logger.error(\"NURESTConnection: Connection error with code 0. Sending NUNURESTConnectionFailureNotification notification and exiting.\")\n", "\u001b[0m[ ]             self._print_information()\n", "[ ]             return False\n", "[ ] \n", "\u001b[31m[-]         bambou_log.error(\"NURESTConnection: Report this error, because this should not happen: %s\" % self._response)\n", "\u001b[0m\u001b[32m[+]         bambou_logger.error(\"NURESTConnection: Report this error, because this should not happen: %s\" % self._response)\n", "\u001b[0m[ ]         return False\n", "[ ] \n", "[ ]     def _print_information(self):\n", "[ ]         \"\"\" Prints information instead of sending a confirmation \"\"\"\n", "[ ] \n", "[ ]         if len(self._response.errors) == 0:\n", "\u001b[31m[-]             bambou_log.error(\"NURESTConnection ERROR without error message [%s] %s\" % (self._response.status_code, self._response.reason))\n", "\u001b[0m\u001b[32m[+]             bambou_logger.error(\"NURESTConnection ERROR without error message [%s] %s\" % (self._response.status_code, self._response.reason))\n", "\u001b[0m[ ] \n", "[ ]         else:\n", "\u001b[31m[-]             bambou_log.error(\"NURESTConnection (%s %s) ERROR %s:\\n%s\" % (self._request.method, self._request.url, self._response.status_code, json.dumps(self._response.errors, indent=4)))\n", "\u001b[0m\u001b[32m[+]             bambou_logger.error(\"NURESTConnection (%s %s) ERROR %s:\\n%s\" % (self._request.method, self._request.url, self._response.status_code, json.dumps(self._response.errors, indent=4)))\n", "\u001b[0m[ ] \n", "[ ]     # HTTP Calls\n", "[ ] \n", "instruction: Replace all instances of `bambou_log` with `bambou_logger`.\n"]}], "source": ["import random\n", "\n", "index = random.randint(0, len(all_edit_data) - 1)\n", "\n", "all_edit_data[index].show_lines()\n", "print(f\"instruction: {all_edit_data[index].instruction}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}