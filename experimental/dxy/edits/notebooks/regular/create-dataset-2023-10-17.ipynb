{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create the Training Dataset"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 25813 edit data with instructions.\n", "skip_due_to_no_blocks=100\n", "skip_due_to_multiple_blocks=287\n"]}], "source": ["import pathlib\n", "import tqdm\n", "import json\n", "from augment.research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData, EditDataManager\n", "from augment.experimental.dxy.edits.reverse_get_instruction import (\n", "    predict_edit_data_v0,\n", "    predict_edit_data_v1,\n", ")\n", "\n", "file_names = (\n", "    \"instruct-del_00_00-per40.json\",\n", "    \"instruct-del_01_01-per40.json\",\n", "    \"instruct-del_02_02-per40.json\",\n", "    \"instruct-del_03_03-per40.json\",\n", "    \"instruct-del_04_04-per40.json\",\n", "    \"instruct-del_05_05-per40.json\",\n", "    \"instruct-del_06_06-per40.json\",\n", "    \"instruct-del_07_07-per40.json\",\n", "    \"instruct-del_08_08-per40.json\",\n", "    \"instruct-del_09_09-per40.json\",\n", "    \"instruct-del_10_10-per40.json\",\n", "    \"instruct-del_11_20-per10.json\",\n", "    \"instruct-del_21_30-per10.json\",\n", ")\n", "json_file_dir = \"/home/<USER>/datasets/edit/organized-basic-instruct-2023-10-13/\"\n", "\n", "\n", "all_edit_data_w_instruction = []\n", "skip_due_to_no_blocks = []\n", "skip_due_to_multiple_blocks = []\n", "\n", "for json_file in file_names:\n", "    json_file_path = pathlib.Path(json_file_dir) / json_file\n", "    with json_file_path.open(\"r\") as f:\n", "        for line in f:\n", "            xdict = json.loads(line)\n", "            data = utils_for_dataclass.create_from_dict(EditData, xdict[\"edit_data\"])\n", "            instruction = xdict[\"instruction\"]\n", "            blocks = utils_for_str.extract_code_within_backticks(instruction)\n", "            if len(blocks) == 0:\n", "                # print(f\"No blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_no_blocks.append((data, instruction))\n", "                continue\n", "            elif len(blocks) > 1:\n", "                # print(f\"Too many blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_multiple_blocks.append((data, instruction))\n", "                continue\n", "            final_instruction = blocks[0]\n", "            if final_instruction[0] == '\"' and final_instruction[-1] == '\"':\n", "                final_instruction = final_instruction[1:-1]\n", "            elif final_instruction[0] == \"`\" and final_instruction[-1] == \"`\":\n", "                final_instruction = final_instruction[1:-1]\n", "            all_edit_data_w_instruction.append((data, final_instruction))\n", "print(f\"There are {len(all_edit_data_w_instruction)} edit data with instructions.\")\n", "print(f\"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}\")\n", "print(f\"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CodeLLaMATokenizer Vocab Size = 32016\n", "Llama2Tokenizer Vocab Size = 32000\n", "WizardCoder-Python-13B-V1.0 Vocab Size = 32000\n", "\n", "\n", "\n", "Chosed tokenizer Vocab Size = 32000\n", "train_ratio = 0.95\n", "#train_edit_data_w_instruction = 24522\n", "#valid_edit_data_w_instruction = 1291\n", "\n", "\n", "Number of input templates = 8\n", "Number of target templates = 10\n", "Number of choices for templates = (8, 5)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 24522/24522 [02:59<00:00, 136.83it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of examples = 122610\n", "Average number of tokens per example = 711.7736481526792 += 277.46357356428297\n", "Minimum number of tokens per example = 100\n", "Maximum number of tokens per example = 4836\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "Number of input templates = 8\n", "Number of target templates = 10\n", "Number of choices for templates = (8, 5)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1291/1291 [00:09<00:00, 130.85it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Total number of examples = 6455\n", "Average number of tokens per example = 704.9287374128583 += 265.4621894123974\n", "Minimum number of tokens per example = 159\n", "Maximum number of tokens per example = 3012\n", "-\n"]}], "source": ["# Create the index dataset\n", "import random\n", "import torch\n", "import tqdm\n", "import numpy as np\n", "from research.core import utils_for_str\n", "from augment.experimental.dxy.edits.data_type import EditData\n", "from augment.experimental.dxy.edits.prompter import EditPromptTemplate\n", "\n", "from megatron.tokenizer.tokenizer import (\n", "    AbstractTokenizer,\n", "    CodeLLaMATokenizer,\n", "    Llama2Tokenizer,\n", ")\n", "\n", "tokenizer_codellama = CodeLLaMATokenizer()\n", "tokenizer_llama2 = Llama2Tokenizer()\n", "tokenizer_wizard = Llama2Tokenizer(\n", "    model_path=\"/mnt/efs/augment/checkpoints/llama/hf/WizardCoder-Python-13B-V1.0/tokenizer.model\"\n", ")\n", "\n", "tokenizer_codellama_inv_vocab = tokenizer_codellama.inv_vocab\n", "tokenizer_llama2_inv_vocab = tokenizer_llama2.inv_vocab\n", "tokenizer_wizard_inv_vocab = tokenizer_wizard.inv_vocab\n", "\n", "print(f\"CodeLLaMATokenizer Vocab Size = {tokenizer_codellama.vocab_size}\")\n", "print(f\"Llama2Tokenizer Vocab Size = {tokenizer_llama2.vocab_size}\")\n", "print(f\"WizardCoder-Python-13B-V1.0 Vocab Size = {tokenizer_wizard.vocab_size}\")\n", "print(\"\\n\\n\")\n", "\n", "tokenizer = tokenizer_llama2\n", "print(f\"Chosed tokenizer Vocab Size = {tokenizer.vocab_size}\")\n", "\n", "indexes = list(range(len(all_edit_data_w_instruction)))\n", "random.shuffle(indexes)\n", "train_ratio = 0.95\n", "train_indexes = indexes[: int(len(indexes) * train_ratio)]\n", "valid_indexes = indexes[int(len(indexes) * train_ratio) :]\n", "train_edit_data_w_instruction = [all_edit_data_w_instruction[i] for i in train_indexes]\n", "valid_edit_data_w_instruction = [all_edit_data_w_instruction[i] for i in valid_indexes]\n", "print(f\"train_ratio = {train_ratio}\")\n", "print(f\"#train_edit_data_w_instruction = {len(train_edit_data_w_instruction)}\")\n", "print(f\"#valid_edit_data_w_instruction = {len(valid_edit_data_w_instruction)}\")\n", "print(\"\\n\")\n", "\n", "\n", "def build_to_fine_tun_data(edit_data_w_instruction: list[tuple[EditData, str]]):\n", "    raw_examples: list[dict] = []\n", "    examples: list[list[int]] = []\n", "    skipped_due_to_zero_token = 0\n", "    num_input_templates, num_target_templates = EditPromptTemplate.get_num_templates()\n", "    num_choices_for_templates = (\n", "        min(9, num_input_templates),\n", "        min(5, num_target_templates),\n", "    )\n", "    print(f\"Number of input templates = {num_input_templates}\")\n", "    print(f\"Number of target templates = {num_target_templates}\")\n", "    print(f\"Number of choices for templates = {num_choices_for_templates}\")\n", "\n", "    for edit, instruction in tqdm.tqdm(\n", "        edit_data_w_instruction, total=len(edit_data_w_instruction)\n", "    ):\n", "        cur_input_template_indexes = random.sample(\n", "            range(num_input_templates), num_choices_for_templates[0]\n", "        )\n", "        cur_target_template_indexes = random.sample(\n", "            range(num_target_templates), num_choices_for_templates[1]\n", "        )\n", "        for cur_input_template_index, cur_target_template_index in zip(\n", "            cur_input_template_indexes, cur_target_template_indexes\n", "        ):\n", "            question, answer = EditPromptTemplate.create_prompt(\n", "                edit,\n", "                instruction,\n", "                tokenizer,\n", "                input_template_index=cur_input_template_index,\n", "                target_template_index=cur_target_template_index,\n", "            )\n", "            tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "            if 0 in tensor:\n", "                skipped_due_to_zero_token += 1\n", "                continue\n", "            examples.append(tensor)\n", "            raw_examples.append({\"question\": question, \"answer\": answer})\n", "    print(f\"Total number of examples = {len(examples)}\")\n", "    print(\n", "        f\"Average number of tokens per example = {np.mean([len(x) for x in examples])} += {np.std([len(x) for x in examples])}\"\n", "    )\n", "    print(\n", "        f\"Minimum number of tokens per example = {np.min([len(x) for x in examples])}\"\n", "    )\n", "    print(\n", "        f\"Maximum number of tokens per example = {np.max([len(x) for x in examples])}\"\n", "    )\n", "    return raw_examples\n", "\n", "\n", "train_examples = build_to_fine_tun_data(train_edit_data_w_instruction)\n", "print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "valid_examples = build_to_fine_tun_data(valid_edit_data_w_instruction)\n", "print(\"-\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 122610/122610 [00:08<00:00, 15124.91it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Save 122610 examples to /mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/unpacked/train\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 6455/6455 [00:00<00:00, 14923.30it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Save 6455 examples to /mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/unpacked/valid\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Build the raw unpacked dataset\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "unpacked_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/unpacked\"\n", ")\n", "unpacked_dir.mkdir(parents=True, exist_ok=True)\n", "for save_name, examples in ((\"train\", train_examples), (\"valid\", valid_examples)):\n", "    output_path = unpacked_dir / save_name\n", "\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for example in tqdm.tqdm(examples):\n", "        question, answer = example[\"question\"], example[\"answer\"]\n", "        builder.add_item(torch.concat((-torch.Tensor(question), torch.Tensor(answer))))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Save {len(examples)} examples to {output_path}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 122610 examples in total.\n", "#repeated_examples = 122610 with repeats=1\n", "Skip 15 examples due to over-4096-tokens.\n", "Packed 122610 examples into 19357 4096-length-sequences.\n", "On average, the number of paddings is 410.40.\n", "There are 19357 sequences.\n", "Built the train3v dataset at /mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train3v_r1n_s4096_full.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 6455 examples in total.\n", "#repeated_examples = 6455 with repeats=1\n", "Skip 0 examples due to over-4096-tokens.\n", "Packed 6455 examples into 1012 4096-length-sequences.\n", "On average, the number of paddings is 405.52.\n", "There are 1012 sequences.\n", "Built the valid3v dataset at /mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid3v_r1n_s4096_full.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 122610 examples in total.\n", "#repeated_examples = 122610 with repeats=1\n", "Skip 15 examples due to over-4096-tokens.\n", "Packed 122610 examples into 19359 4096-length-sequences.\n", "On average, the number of paddings is 408.62.\n", "There are 19359 sequences.\n", "Built the train3v dataset at /mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/train3v_r1n_s4096_onlytgt.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n", "There are 6455 examples in total.\n", "#repeated_examples = 6455 with repeats=1\n", "Skip 0 examples due to over-4096-tokens.\n", "Packed 6455 examples into 1017 4096-length-sequences.\n", "On average, the number of paddings is 410.17.\n", "There are 1017 sequences.\n", "Built the valid3v dataset at /mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13/valid3v_r1n_s4096_onlytgt.\n", "\n", "----------------------------------------------------------------------------------------------------\n", "\n"]}], "source": ["import typing\n", "import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "\n", "def pack_sequences(\n", "    examples: list[list[int]],\n", "    seq_len: int,\n", "    pad_id: int,\n", ") -> list[list[int]]:\n", "    \"\"\"Pack shorter examples into longer ones.\"\"\"\n", "    final_sequences: list[list[int]] = []\n", "    paddings, skip_over_length = [], 0\n", "    current_batch: typing.Optional[list[int]] = None\n", "    for ex in examples:\n", "        if len(ex) >= seq_len:\n", "            skip_over_length += 1\n", "            continue\n", "        if current_batch is None:\n", "            current_batch = ex\n", "            continue\n", "        elif len(current_batch) > seq_len:\n", "            raise ValueError(\"Impossible\")\n", "        elif len(current_batch) + len(ex) > seq_len:\n", "            paddings.append(seq_len - len(current_batch))\n", "            current_batch.extend([pad_id] * (seq_len - len(current_batch)))\n", "            final_sequences.append(current_batch)\n", "            current_batch = None\n", "        else:\n", "            current_batch.extend(ex)\n", "    if current_batch is not None:\n", "        paddings.append(seq_len - len(current_batch))\n", "        current_batch.extend([pad_id] * (seq_len - len(current_batch)))\n", "        final_sequences.append(current_batch)\n", "    print(f\"Skip {skip_over_length} examples due to over-{seq_len}-tokens.\")\n", "    print(\n", "        f\"Packed {len(examples)} examples into {len(final_sequences)} {seq_len}-length-sequences.\"\n", "    )\n", "    print(f\"On average, the number of paddings is {np.mean(paddings):.2f}.\")\n", "    return final_sequences\n", "\n", "\n", "def build_whole_dataset(\n", "    raw_examples: list[dict],\n", "    root_dir: str,\n", "    base_name: str,\n", "    repeats: int = 1,\n", "    seq_len: int = 4096,\n", "    mask_input: bool = False,\n", "):\n", "    random.shuffle(raw_examples)\n", "    # Split the raw training / validation dataset\n", "    print(f\"There are {len(raw_examples)} examples in total.\")\n", "\n", "    repeated_examples = []\n", "    for _ in range(repeats):\n", "        for ex in raw_examples:\n", "            question, answer = ex[\"question\"], ex[\"answer\"]\n", "            if mask_input:\n", "                question = [-x for x in question]\n", "            tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "            repeated_examples.append(tensor)\n", "    print(f\"#repeated_examples = {len(repeated_examples)} with repeats={repeats}\")\n", "    random.shuffle(repeated_examples)\n", "\n", "    sequences = pack_sequences(repeated_examples, seq_len, -tokenizer.eos_id)\n", "    random.shuffle(sequences)\n", "\n", "    print(f\"There are {len(sequences)} sequences.\")\n", "\n", "    mask_suffix = \"onlytgt\" if mask_input else \"full\"\n", "    output_path = (\n", "        pathlib.Path(root_dir) / f\"{base_name}_r{repeats}n_s{seq_len}_{mask_suffix}\"\n", "    )\n", "\n", "    output_dir = output_path.parent\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "    # Build the train dataset.\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sequence in sequences:\n", "        # Make the total sequence length to be 4096 + 1\n", "        builder.add_item(torch.Tensor(sequence + [tokenizer.eos_id]))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Built the {base_name} dataset at {output_path}.\")\n", "\n", "\n", "for mask_input in (<PERSON><PERSON><PERSON>, <PERSON>):\n", "    build_whole_dataset(\n", "        train_examples,\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13\",\n", "        \"train3v\",\n", "        mask_input=mask_input,\n", "    )\n", "    print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "    build_whole_dataset(\n", "        valid_examples,\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13\",\n", "        \"valid3v\",\n", "        mask_input=mask_input,\n", "    )\n", "    print(\"\\n\" + \"-\" * 100 + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import typing\n", "# import numpy as np\n", "# from megatron.data.indexed_dataset import MMapIndexedDataset, MMapIndexedDatasetBuilder\n", "\n", "\n", "# def pack_sequences(\n", "#     examples: list[list[int]],\n", "#     seq_len: int,\n", "#     pad_id: int,\n", "# ) -> list[list[int]]:\n", "#     \"\"\"Pack shorter examples into longer ones.\"\"\"\n", "#     final_sequences: list[list[int]] = []\n", "#     paddings, skip_over_length = [], 0\n", "#     current_batch: typing.Optional[list[int]] = None\n", "#     for ex in examples:\n", "#         if len(ex) >= seq_len:\n", "#             skip_over_length += 1\n", "#             continue\n", "#         if current_batch is None:\n", "#             current_batch = ex\n", "#             continue\n", "#         elif len(current_batch) > seq_len:\n", "#             raise ValueError(\"Impossible\")\n", "#         elif len(current_batch) + len(ex) > seq_len:\n", "#             paddings.append(seq_len - len(current_batch))\n", "#             current_batch.extend([pad_id] * (seq_len - len(current_batch)))\n", "#             final_sequences.append(current_batch)\n", "#             current_batch = None\n", "#         else:\n", "#             current_batch.extend(ex)\n", "#     if current_batch is not None:\n", "#         paddings.append(seq_len - len(current_batch))\n", "#         current_batch.extend([pad_id] * (seq_len - len(current_batch)))\n", "#         final_sequences.append(current_batch)\n", "#     print(f\"Skip {skip_over_length} examples due to over-{seq_len}-tokens.\")\n", "#     print(\n", "#         f\"Packed {len(examples)} examples into {len(final_sequences)} {seq_len}-length-sequences.\"\n", "#     )\n", "#     print(f\"On average, the number of paddings is {np.mean(paddings):.2f}.\")\n", "#     return final_sequences\n", "\n", "\n", "# def build_whole_dataset(\n", "#     raw_examples: list[dict],\n", "#     root_dir: str = \"/mnt/efs/augment/user/dxy/datasets/edit/fine-tune-data-2023-13\",\n", "#     repeats: int = 1,\n", "#     seq_len: int = 4096,\n", "#     mask_input: bool = False,\n", "# ):\n", "#     random.shuffle(raw_examples)\n", "#     # Split the raw training / validation dataset\n", "#     print(f\"There are {len(raw_examples)} examples in total.\")\n", "#     train_ratio = 0.95\n", "#     raw_train_examples = raw_examples[: int(len(raw_examples) * train_ratio)]\n", "#     raw_valid_sequences = raw_examples[int(len(raw_examples) * train_ratio) :]\n", "#     print(f\"There are {len(raw_train_examples)} train examples in total.\")\n", "#     print(f\"There are {len(raw_valid_sequences)} valid examples in total.\")\n", "\n", "#     repeated_train_examples = []\n", "#     repeated_valid_examples = []\n", "#     for _ in range(repeats):\n", "#         for ex in raw_train_examples:\n", "#             question, answer = ex[\"question\"], ex[\"answer\"]\n", "#             if mask_input:\n", "#                 question = [-x for x in question]\n", "#             tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "#             repeated_train_examples.append(tensor)\n", "#         for ex in raw_valid_sequences:\n", "#             question, answer = ex[\"question\"], ex[\"answer\"]\n", "#             if mask_input:\n", "#                 question = [-x for x in question]\n", "#             tensor = [tokenizer.bos_id] + question + answer + [tokenizer.eos_id]\n", "#             repeated_valid_examples.append(tensor)\n", "#     print(\n", "#         f\"#repeated_train_examples = {len(repeated_train_examples)} with repeats={repeats}\"\n", "#     )\n", "#     print(\n", "#         f\"#repeated_valid_examples = {len(repeated_valid_examples)} with repeats={repeats}\"\n", "#     )\n", "#     random.shuffle(repeated_train_examples)\n", "#     random.shuffle(repeated_valid_examples)\n", "\n", "#     train_sequences = pack_sequences(\n", "#         repeated_train_examples, seq_len, -tokenizer.eos_id\n", "#     )\n", "#     valid_sequences = pack_sequences(\n", "#         repeated_valid_examples, seq_len, -tokenizer.eos_id\n", "#     )\n", "#     random.shuffle(train_sequences)\n", "#     random.shuffle(valid_sequences)\n", "\n", "#     print(\n", "#         f\"There are {len(train_sequences)} train sequences and {len(valid_sequences)} valid sequences.\"\n", "#     )\n", "\n", "#     mask_suffix = \"onlytgt\" if mask_input else \"full\"\n", "#     train_output_path = (\n", "#         pathlib.Path(root_dir) / f\"train2v_r{repeats}n_s{seq_len}_{mask_suffix}\"\n", "#     )\n", "\n", "#     valid_output_path = (\n", "#         pathlib.Path(root_dir) / f\"valid2v_r{repeats}n_s{seq_len}_{mask_suffix}\"\n", "#     )\n", "#     output_dir = train_output_path.parent\n", "#     output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "#     # Build the train dataset.\n", "#     train_builder = MMapIndexedDatasetBuilder(\n", "#         train_output_path.with_suffix(\".bin\"), dtype=np.int32\n", "#     )\n", "#     for sequence in train_sequences:\n", "#         # Make the total sequence length to be 4096 + 1\n", "#         train_builder.add_item(torch.Tensor(sequence + [tokenizer.eos_id]))\n", "#         train_builder.end_document()\n", "#     train_builder.finalize(train_output_path.with_suffix(\".idx\"))\n", "#     print(f\"Built the train dataset at {train_output_path}.\")\n", "\n", "#     # Build the validation dataset.\n", "#     valid_builder = MMapIndexedDatasetBuilder(\n", "#         valid_output_path.with_suffix(\".bin\"), dtype=np.int32\n", "#     )\n", "#     for sequence in valid_sequences:\n", "#         # Make the total sequence length to be 4096 + 1\n", "#         valid_builder.add_item(torch.Tensor(sequence + [tokenizer.eos_id]))\n", "#         valid_builder.end_document()\n", "#     valid_builder.finalize(valid_output_path.with_suffix(\".idx\"))\n", "#     print(f\"Built the validation dataset at {valid_output_path}.\")\n", "\n", "\n", "# build_whole_dataset(filtered_examples, mask_input=True)\n", "# print(\"\\n\" + \"-\" * 100 + \"\\n\")\n", "# build_whole_dataset(filtered_examples, mask_input=False)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}