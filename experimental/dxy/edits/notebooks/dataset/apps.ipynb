{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 5000 training examples and 5000 test examples.\n"]}], "source": ["from datasets import load_dataset\n", "\n", "ds_train = load_dataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.raw/apps\", split=\"train\"\n", ")\n", "ds_test = load_dataset(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/apps\", split=\"test\")\n", "print(f\"There are {len(ds_train)} training examples and {len(ds_test)} test examples.\")"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5000 -> 4804\n", "5000 -> 3765\n"]}], "source": ["# See details here: https://huggingface.co/datasets/codeparrot/apps\n", "import json\n", "\n", "\n", "def process_data(ds):\n", "    samples = []\n", "    for x in ds:\n", "        try:\n", "            x[\"solutions\"] = json.loads(x[\"solutions\"])\n", "            x[\"input_output\"] = json.loads(x[\"input_output\"])\n", "        except:\n", "            continue\n", "        samples.append(x)\n", "    print(f\"{len(ds)} -> {len(samples)}\")\n", "    return samples\n", "\n", "\n", "train_examples = process_data(ds_train)\n", "test_examples = process_data(ds_test)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['problem_id', 'question', 'solutions', 'input_output', 'difficulty', 'url', 'starter_code'])\n"]}], "source": ["print(train_examples[0].keys())"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You have a string $s$ — a sequence of commands for your toy robot. The robot is placed in some cell of a rectangular grid. He can perform four commands:  'W' — move one cell up;  'S' — move one cell down;  'A' — move one cell left;  'D' — move one cell right. \n", "\n", "Let $Grid(s)$ be the grid of minimum possible area such that there is a position in the grid where you can place the robot in such a way that it will not fall from the grid while running the sequence of commands $s$. For example, if $s = \\text{DSAWWAW}$ then $Grid(s)$ is the $4 \\times 3$ grid:  you can place the robot in the cell $(3, 2)$;  the robot performs the command 'D' and moves to $(3, 3)$;  the robot performs the command 'S' and moves to $(4, 3)$;  the robot performs the command 'A' and moves to $(4, 2)$;  the robot performs the command 'W' and moves to $(3, 2)$;  the robot performs the command 'W' and moves to $(2, 2)$;  the robot performs the command 'A' and moves to $(2, 1)$;  the robot performs the command 'W' and moves to $(1, 1)$.  [Image] \n", "\n", "You have $4$ extra letters: one 'W', one 'A', one 'S', one 'D'. You'd like to insert at most one of these letters in any position of sequence $s$ to minimize the area of $Grid(s)$.\n", "\n", "What is the minimum area of $Grid(s)$ you can achieve?\n", "\n", "\n", "-----Input-----\n", "\n", "The first line contains one integer $T$ ($1 \\le T \\le 1000$) — the number of queries.\n", "\n", "Next $T$ lines contain queries: one per line. This line contains single string $s$ ($1 \\le |s| \\le 2 \\cdot 10^5$, $s_i \\in \\{\\text{W}, \\text{A}, \\text{S}, \\text{D}\\}$) — the sequence of commands.\n", "\n", "It's guaranteed that the total length of $s$ over all queries doesn't exceed $2 \\cdot 10^5$.\n", "\n", "\n", "-----Output-----\n", "\n", "Print $T$ integers: one per query. For each query print the minimum area of $Grid(s)$ you can achieve.\n", "\n", "\n", "-----Example-----\n", "Input\n", "3\n", "DSAWWAW\n", "D\n", "WA\n", "\n", "Output\n", "8\n", "2\n", "4\n", "\n", "\n", "\n", "-----Note-----\n", "\n", "In the first query you have to get string $\\text{DSAWW}\\underline{D}\\text{AW}$.\n", "\n", "In second and third queries you can not decrease the area of $Grid(s)$.\n"]}], "source": ["index = 11\n", "print(train_examples[index][\"question\"])"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["n = int(input())\n", "\n", "def area(width, height) :\n", "    return (width+1) * (height+1)\n", "\n", "def calcul(s1, c, s2) :\n", "    maxx, maxy, minx, miny = 0, 0, 0, 0\n", "    x, y = 0, 0\n", "    for k in range(len(s1)) :\n", "        if s1[k] == \"W\" :\n", "            y += 1\n", "        if s1[k] == \"S\" :\n", "            y -= 1\n", "        if s1[k] == \"A\" :\n", "            x -= 1\n", "        if s1[k] == \"D\" :\n", "            x += 1\n", "        maxx = max(maxx, x)\n", "        minx = min(minx, x)\n", "\n", "        maxy = max(maxy, y)\n", "        miny = min(miny, y)\n", "\n", "\n", "\n", "\n", "    if c == \"W\" :\n", "        y += 1\n", "    elif c == \"S\" :\n", "        y -= 1\n", "    elif c == \"A\" :\n", "        x -= 1\n", "    elif c == \"D\" :\n", "        x += 1\n", "    else :\n", "        print(c, \"ok\")\n", "\n", "    maxx = max(maxx, x)\n", "    minx = min(minx, x)\n", "\n", "    maxy = max(maxy, y)\n", "    miny = min(miny, y)\n", "\n", "    for k in range(len(s2)) :\n", "        if s2[k] == \"W\" :\n", "            y += 1\n", "        if s2[k] == \"S\" :\n", "            y -= 1\n", "        if s2[k] == \"A\" :\n", "            x -= 1\n", "        if s2[k] == \"D\" :\n", "            x += 1\n", "        maxx = max(maxx, x)\n", "        minx = min(minx, x)\n", "\n", "        maxy = max(maxy, y)\n", "        miny = min(miny, y)\n", "\n", "\n", "\n", "    diffx = maxx - minx\n", "    diffy = maxy - miny\n", "    tmp = area(diffx, diffy)\n", "\n", "\n", "    return tmp\n", "\n", "def pre_calcul(s, moment, pre_avant, date_debut) :\n", "    x, y, maxx, minx, maxy, miny = pre_avant\n", "    for k in range(date_debut, moment) :\n", "        if s[k] == \"W\" :\n", "            y += 1\n", "        if s[k] == \"S\" :\n", "            y -= 1\n", "        if s[k] == \"A\" :\n", "            x -= 1\n", "        if s[k] == \"D\" :\n", "            x += 1\n", "        maxx = max(maxx, x)\n", "        minx = min(minx, x)\n", "\n", "        maxy = max(maxy, y)\n", "        miny = min(miny, y)\n", "\n", "    return (x, y, maxx, minx, maxy, miny)\n", "\n", "def calcul2(s, c, moment, precalcul) :\n", "    x, y, maxx, minx, maxy, miny = precalcul\n", "\n", "\n", "\n", "    if c == \"W\" :\n", "        y += 1\n", "    elif c == \"S\" :\n", "        y -= 1\n", "    elif c == \"A\" :\n", "        x -= 1\n", "    elif c == \"D\" :\n", "        x += 1\n", "    else :\n", "        print(c, \"ok\")\n", "\n", "    maxx = max(maxx, x)\n", "    minx = min(minx, x)\n", "\n", "    maxy = max(maxy, y)\n", "    miny = min(miny, y)\n", "\n", "    for k in range(moment, len(s)) :\n", "        if s[k] == \"W\" :\n", "            y += 1\n", "        if s[k] == \"S\" :\n", "            y -= 1\n", "        if s[k] == \"A\" :\n", "            x -= 1\n", "        if s[k] == \"D\" :\n", "            x += 1\n", "        maxx = max(maxx, x)\n", "        minx = min(minx, x)\n", "\n", "        maxy = max(maxy, y)\n", "        miny = min(miny, y)\n", "\n", "\n", "\n", "    diffx = maxx - minx\n", "    diffy = maxy - miny\n", "    tmp = area(diffx, diffy)\n", "\n", "\n", "    return tmp\n", "\n", "for _ in range(n) :\n", "    s = input()\n", "    maxx, maxy, minx, miny = 0, 0, 0, 0\n", "    x, y = 0, 0\n", "    momentminx, momentmaxx, momentminy, momentmaxy = -1, -1, -1, -1\n", "    for k in range(len(s)) :\n", "        if s[k] == \"W\" :\n", "            y += 1\n", "        if s[k] == \"S\" :\n", "            y -= 1\n", "        if s[k] == \"A\" :\n", "            x -= 1\n", "        if s[k] == \"D\" :\n", "            x += 1\n", "\n", "        if x > maxx :\n", "            momentmaxx = k\n", "        if y > maxy :\n", "            momentmaxy = k\n", "        if x < minx :\n", "            momentminx = k\n", "        if y < miny :\n", "            momentminy = k\n", "        maxx = max(maxx, x)\n", "        minx = min(minx, x)\n", "\n", "        maxy = max(maxy, y)\n", "        miny = min(miny, y)\n", "    diffx = maxx - minx\n", "    diffy = maxy - miny\n", "\n", "\n", "    tmp = 999999999999999999999999999999999999\n", "    l = [momentmaxx, momentmaxy, momentminx, momentminy]\n", "    l = list(set(l))\n", "    l = [i for i in l if i != -1]\n", "    l.sort()\n", "    if l != [] :\n", "        precalcul = pre_calcul(s, l[0], (0, 0, 0, 0, 0, 0), 0)\n", "        avant = l[0]\n", "        for moment in l :\n", "            precalcul = pre_calcul(s, moment, precalcul, avant)\n", "            avant = moment\n", "            tmp = min(tmp, calcul2(s, 'W', moment, precalcul))\n", "            tmp = min(tmp, calcul2(s, 'S', moment, precalcul))\n", "            tmp = min(tmp, calcul2(s, 'A', moment, precalcul))\n", "            tmp = min(tmp, calcul2(s, 'D', moment, precalcul))\n", "    print(tmp)\n", "\n"]}], "source": ["print(train_examples[index][\"solutions\"][0])"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['3\\nDSAWWAW\\nD\\nWA\\n']\n"]}], "source": ["print(train_examples[index][\"input_output\"][\"inputs\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Fields\n", "\n", "|Field|Type|Description|\n", "|---|---|---|\n", "|problem_id|int|problem id|\n", "|question|string|problem description|\n", "|solutions|string|some python solutions|\n", "|input_output|string|Json string with \"inputs\" and \"outputs\" of the test cases, might also include \"fn_name\" the name of the function|\n", "|difficulty|string|difficulty level of the problem|\n", "|url|string|url of the source of the problem|\n", "|starter_code|string|starter code to include in prompts|\n", "\n", "we mention that only few samples have `fn_name` and `starter_code` specified\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}