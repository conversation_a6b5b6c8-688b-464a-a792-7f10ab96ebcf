{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "folder = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/codealpaca\")\n", "assert folder.exists()\n", "old_code_alpaca_path = folder / \"data\" / \"code_alpaca_20k.json\"\n", "new_code_alpaca_path = folder / \"data\" / \"new_codealpaca.json\""]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["#old_code_alpaca = 20022\n", "#new_code_alpaca = 4535\n"]}], "source": ["from research.core import utils_for_file\n", "\n", "old_code_alpaca = utils_for_file.read_json(old_code_alpaca_path)\n", "new_code_alpaca = utils_for_file.read_json(new_code_alpaca_path)\n", "print(f\"#old_code_alpaca = {len(old_code_alpaca)}\")\n", "print(f\"#new_code_alpaca = {len(new_code_alpaca)}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'instruction': 'Formulate an equation to calculate the height of a triangle given the angle, side lengths and opposite side length.',\n", " 'input': '',\n", " 'output': 'Height of triangle = opposite side length * sin (angle) / side length'}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["old_code_alpaca[1]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'instruction': 'Write a C++ program that calculates the area of a rectangle, given its width and height.',\n", " 'input': 'width: 5, height: 8',\n", " 'response': '#include <iostream>\\nusing namespace std;\\n\\nint main() {\\n    int width = 5;\\n    int height = 8;\\n    int area = width * height;\\n\\n    cout << \"Area of rectangle: \" << area << endl;\\n    return 0;}',\n", " 'output': '#include <iostream>\\nusing namespace std;\\n\\nint main() {\\n    int width = 5;\\n    int height = 8;\\n    int area = width * height;\\n\\n    cout << \"Area of rectangle: \" << area << endl;\\n    return 0;}'}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["new_code_alpaca[0]"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["PROMPT_DICT = {\n", "    \"prompt_input\": (\n", "        \"Below is an instruction that describes a task, paired with an input that provides further context. \"\n", "        \"Write a response that appropriately completes the request.\\n\\n\"\n", "        \"### Instruction:\\n{instruction}\\n\\n### Input:\\n{input}\\n\\n### Response:\"\n", "    ),\n", "    \"prompt_no_input\": (\n", "        \"Below is an instruction that describes a task. \"\n", "        \"Write a response that appropriately completes the request.\\n\\n\"\n", "        \"### Instruction:\\n{instruction}\\n\\n### Response:\"\n", "    ),\n", "}"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}