{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Visualize the InstructCoder Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import pathlib\n", "from collections import defaultdict\n", "from base.ranges.range_types import LineRange\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from research.utils import dataclass_utils\n", "from experimental.dxy.edits.util_lib import load_instruct_coder_data\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "ic_additional_seeds = load_instruct_coder_data()\n", "ic_github_seeds = load_instruct_coder_data(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/github_seed.json\"\n", "    )\n", ")\n", "print(f\"#ic_additional_seeds = {len(ic_additional_seeds)}\")\n", "print(f\"#ic_github_seeds = {len(ic_github_seeds)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Instruction: Implement lazy evaluation to defer computation until necessary, reducing memory usage.\n", "\u001b[34m[ ] import os\n", "\u001b[0m\u001b[34m[ ] \n", "\u001b[0m\u001b[34m[ ] def index_documents(directory):\n", "\u001b[0m\u001b[31m[-]     documents = []\n", "\u001b[0m\u001b[31m[-]     for filename in os.listdir(directory):\n", "\u001b[0m\u001b[31m[-]         if filename.endswith('.txt'):\n", "\u001b[0m\u001b[31m[-]             with open(os.path.join(directory, filename), 'r') as f:\n", "\u001b[0m\u001b[31m[-]                 document = {\n", "\u001b[0m\u001b[31m[-]                     'filename': filename,\n", "\u001b[0m\u001b[31m[-]                     'content': f.read()\n", "\u001b[0m\u001b[31m[-]                 }\n", "\u001b[0m\u001b[31m[-]                 documents.append(document)\n", "\u001b[0m\u001b[31m[-]     return documents\n", "\u001b[0m\u001b[31m[-] \n", "\u001b[0m\u001b[31m[-] def search(query, documents):\n", "\u001b[0m\u001b[31m[-]     results = []\n", "\u001b[0m\u001b[31m[-]     for document in documents:\n", "\u001b[0m\u001b[31m[-]         if query in document['content']:\n", "\u001b[0m\u001b[32m[+]     for filename in os.listdir(directory):\n", "\u001b[0m\u001b[32m[+]         if filename.endswith('.txt'):\n", "\u001b[0m\u001b[32m[+]             with open(os.path.join(directory, filename), 'r') as f:\n", "\u001b[0m\u001b[32m[+]                 document = {\n", "\u001b[0m\u001b[32m[+]                     'filename': filename,\n", "\u001b[0m\u001b[32m[+]                     'content': lambda: f.read()\n", "\u001b[0m\u001b[32m[+]                 }\n", "\u001b[0m\u001b[32m[+]                 yield document\n", "\u001b[0m\u001b[32m[+] \n", "\u001b[0m\u001b[32m[+] def search(query, documents):\n", "\u001b[0m\u001b[32m[+]     results = []\n", "\u001b[0m\u001b[32m[+]     for document in documents:\n", "\u001b[0m\u001b[32m[+]         if query in document['content']():\n", "\u001b[0m\u001b[34m[ ]             results.append(document['filename'])\n", "\u001b[0m\u001b[34m[ ]     return results\n", "\u001b[0m\u001b[34m[ ] \n", "\u001b[0m\u001b[34m[ ] directory = '/path/to/documents'\n", "\u001b[0m\u001b[34m[ ] documents = index_documents(directory)\n", "\u001b[0m\u001b[34m[ ] results = search('search term', documents)\n", "\u001b[0m\u001b[34m[ ] print(results)\u001b[0m"]}], "source": ["# ic_github_seeds[0].show_code_w_updated_code_and_instruction()\n", "ic_additional_seeds[5].show_code_w_updated_code_and_instruction()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}