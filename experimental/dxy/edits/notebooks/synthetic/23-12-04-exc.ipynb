{"cells": [{"cell_type": "code", "execution_count": 75, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from collections import defaultdict\n", "from base.ranges.range_types import LineRange\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from research.utils import dataclass_utils\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "# seeds = [\n", "#     utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "#     for x in utils_for_file.read_jsonl(\n", "#         \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-seeds_via_icoder_addseed.jsonl\"\n", "#     )\n", "# ]\n", "seeds = [\n", "    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "    for x in utils_for_file.read_json(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-seeds_via_icoder_addseed.json\"\n", "    )\n", "]\n", "augmented_seeds = [\n", "    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "    for x in utils_for_file.read_json(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-augmented_seeds_via_icoder_addseed.json\"\n", "    )\n", "]\n", "\n", "final_edit_scopes = [\n", "    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-edit-scopes.jsonl.zst\"\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_edit_scopes[0].show_code(max_context_lines=20)\n", "augmented_seeds[31].show_code_w_updated_code_and_instruction(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["augmented_seeds[3].show_code_w_updated_code_and_instruction(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionFewShot,\n", ")\n", "from experimental.dxy.edits.pipelines.generate_selected_code import (\n", "    GenerateSelectedCodeViaReverseInstruction,\n", "    GenerateInstructionViaSUCodeRI,\n", ")\n", "\n", "pipeline_1 = GenerateReverseInstructionFewShot(DEBUG=True, num_of_completion=1)\n", "pipeline_2 = GenerateSelectedCodeViaReverseInstruction(DEBUG=True, num_of_completion=1)\n", "pipeline_3 = GenerateInstructionViaSUCodeRI(DEBUG=True, num_of_completion=1)\n", "\n", "target_sample = final_edit_scopes[0]\n", "\n", "[sample_w_reverse_instruction], meta_info_1 = pipeline_1.generate(\n", "    target_sample, augmented_seeds, []\n", ")\n", "[sample_w_selected_code], meta_info_2 = pipeline_2.generate(\n", "    sample_w_reverse_instruction, augmented_seeds, []\n", ")\n", "[sample_w_annotation], meta_info_3 = pipeline_3.generate(\n", "    sample_w_selected_code, augmented_seeds, []\n", ")\n", "sample_w_annotation.show_code_w_updated_code_and_instruction(20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation\n", "\n", "- Step 1: generate reverse instruction for candidate example\n", "- Step 2: generate the selected code.\n", "- Step 3: generate the instruction."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionFewShot,\n", ")\n", "from experimental.dxy.edits.pipelines.generate_selected_code import (\n", "    GenerateSelectedCodeViaReverseInstruction,\n", "    GenerateInstructionViaSUCodeRI,\n", ")\n", "\n", "pipeline_1 = GenerateReverseInstructionFewShot(DEBUG=True, num_of_completion=1)\n", "pipeline_2 = GenerateSelectedCodeViaReverseInstruction(DEBUG=True, num_of_completion=1)\n", "pipeline_3 = GenerateInstructionViaSUCodeRI(DEBUG=True, num_of_completion=1)\n", "\n", "target_sample = final_edit_scopes[0]\n", "\n", "[sample_w_reverse_instruction], messages_1 = pipeline_1.generate(\n", "    target_sample, augmented_seeds, []\n", ")\n", "[sample_w_selected_code], messages_2 = pipeline_2.generate(\n", "    sample_w_reverse_instruction, augmented_seeds, []\n", ")\n", "[sample_w_annotation], messages_3 = pipeline_3.generate(\n", "    sample_w_selected_code, augmented_seeds, []\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_w_reverse_instruction.show_code(20)\n", "print(\n", "    f\"Revserse instruction: {sample_w_reverse_instruction.misc['reverse_instruction']}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_w_annotation.show_code_w_updated_code_and_instruction(max_context_lines=30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sample_w_reverse_instruction.misc.keys())\n", "print(sample_w_selected_code.misc.keys())\n", "print(sample_w_annotation.misc.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_w_selected_code.misc[\"reverse_instruction\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation (<PERSON><PERSON> Seed's Instruction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionZeroShot,\n", ")\n", "\n", "pipeline = GenerateReverseInstructionZeroShot()\n", "\n", "answers, messages = pipeline.generate(seeds[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[0].show_code_w_updated_code_and_instruction()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(answers[0].misc[\"reverse_instruction\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation (Reverse Instruction for Candidate Example)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionFewShot,\n", ")\n", "\n", "pipeline = GenerateReverseInstructionFewShot()\n", "\n", "answers, messages = pipeline.generate(final_edit_scopes[0], augmented_seeds, [])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for idx, message in enumerate(messages):\n", "#     print(\"*\" * 10 + f\" {idx:02d} \" + \"*\" * 10)\n", "#     print(message)\n", "answers[0].show_code(max_context_lines=20)\n", "print(\"*\" * 100)\n", "print(answers[0].misc[\"reverse_instruction\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation (Generate Instruction and Selected Code in One-shot)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_both import (\n", "    GenerateInstructionAndSelection,\n", ")\n", "\n", "pipeline = GenerateInstructionAndSelection(\n", "    DEBUG=True, num_samples_from_seed=4, num_samples_from_machine=2\n", ")\n", "\n", "answers, messages = pipeline.generate(\n", "    final_edit_scopes[0],\n", "    seeds,\n", "    [],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[0].show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[1].show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[2].show_code_w_updated_code_and_instruction()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[4].show_code_w_updated_code_and_instruction()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}