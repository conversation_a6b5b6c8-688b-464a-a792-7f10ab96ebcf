{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Find 5000 files in total.\n", "Loaded 2915 examples for data_igor.\n", "Loaded 106341 examples for load_yury_data.\n", "Loaded 108391 examples for data_instructcoder_train.\n", "Loaded 5708 examples for data_instructcoder_valid.\n"]}], "source": ["from experimental.dxy.edits.temp_lib import (\n", "    load_igor_data,\n", "    load_instruct_coder,\n", "    load_yury_data,\n", ")\n", "\n", "data_igor = load_igor_data()\n", "data_yury = load_yury_data()\n", "data_instructcoder_train = load_instruct_coder(\"train\")\n", "data_instructcoder_valid = load_instruct_coder(\"valid\")\n", "\n", "print(f\"Loaded {len(data_igor)} examples for data_igor.\")\n", "print(f\"Loaded {len(data_yury)} examples for load_yury_data.\")\n", "print(f\"Loaded {len(data_instructcoder_train)} examples for data_instructcoder_train.\")\n", "print(f\"Loaded {len(data_instructcoder_valid)} examples for data_instructcoder_valid.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Plotting the edit scope: 100%|██████████| 2915/2915 [00:00<00:00, 262662.12it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /home/<USER>/cache/vis-results-24-01-04/data_from_igor.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Plotting the edit scope: 100%|██████████| 106341/106341 [00:03<00:00, 30478.51it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /home/<USER>/cache/vis-results-24-01-04/data_from_yury.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Plotting the edit scope: 100%|██████████| 108391/108391 [00:00<00:00, 578865.56it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /home/<USER>/cache/vis-results-24-01-04/data_from_instructcoder_train.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Plotting the edit scope: 100%|██████████| 5708/5708 [00:00<00:00, 595031.37it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /home/<USER>/cache/vis-results-24-01-04/data_from_instructcoder_valid.pdf\n"]}], "source": ["# Visualize the data.\n", "from experimental.dxy.edits import vis_lib\n", "\n", "vis_lib.plot_edit_scope(\n", "    data_igor,\n", "    filepath=\"/home/<USER>/cache/vis-results-24-01-04/data_from_igor.pdf\",\n", ")\n", "vis_lib.plot_edit_scope(\n", "    data_yury,\n", "    filepath=\"/home/<USER>/cache/vis-results-24-01-04/data_from_yury.pdf\",\n", ")\n", "vis_lib.plot_edit_scope(\n", "    data_instructcoder_train,\n", "    filepath=\"/home/<USER>/cache/vis-results-24-01-04/data_from_instructcoder_train.pdf\",\n", ")\n", "vis_lib.plot_edit_scope(\n", "    data_instructcoder_valid,\n", "    filepath=\"/home/<USER>/cache/vis-results-24-01-04/data_from_instructcoder_valid.pdf\",\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 22776 instructions.\n"]}], "source": ["all_instructions = []\n", "for x in data_igor:\n", "    assert x.instructions is not None\n", "    for ins in x.instructions:\n", "        all_instructions.append(ins.text)\n", "print(f\"Loaded {len(all_instructions)} instructions.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Generate features: 100%|██████████| 22776/22776 [42:19<00:00,  8.97it/s]  \n"]}], "source": ["import tqdm\n", "from research.data.synthetic_code_edit.api_lib import GptEmbeddingWrapper\n", "\n", "gpt_embedding_wrapper = GptEmbeddingWrapper(\n", "    shareable_between_processes=False,\n", "    cache_file=\"/mnt/efs/augment/user/dxy/datasets/cache-embedding/cache.db\",\n", ")\n", "features = []\n", "for inst in tqdm.tqdm(\n", "    all_instructions, total=len(all_instructions), desc=\"Generate features\"\n", "):\n", "    feats = gpt_embedding_wrapper(inst)\n", "    features.append(feats)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'text-embedding-ada-002': 0.036456, 'total_price': 0.036456}\n"]}], "source": ["print(gpt_embedding_wrapper.get_stats())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}