{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["32035\n"]}], "source": ["from research.core.all_prompt_formatters import get_prompt_formatter\n", "\n", "prompter = get_prompt_formatter(\"deepseek_coder_instruct\")\n", "print(prompter.tokenizer.vocab_size)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "\n", "save_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0\"\n", ")\n", "save_dir.mkdir(exist_ok=True, parents=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3173 examples.\n", "Type of the first example: <class 'dict'>\n", "Keys of the first example: dict_keys(['tokens', 'instruction', 'path', 'prefix', 'suffix', 'old_middle', 'new_middle'])\n"]}], "source": ["import random\n", "from research.core import utils_for_file\n", "\n", "raw_token_data = utils_for_file.read_json(\n", "    \"/mnt/efs/augment/user/igor/nlp/2023-12-21_02-03-25/tokenized.json\"\n", ")\n", "print(f\"Loaded {len(raw_token_data)} examples.\")\n", "print(f\"Type of the first example: {type(raw_token_data[0])}\")\n", "if isinstance(raw_token_data[0], dict):\n", "    print(f\"Keys of the first example: {raw_token_data[0].keys()}\")\n", "examples = [x[\"tokens\"] for x in raw_token_data]\n", "random.shuffle(examples)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["num = 3000\n", "train_examples = examples[:num]\n", "valid_examples = examples[num:]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import typing\n", "import pathlib\n", "from research.data.synthetic_code_edit.bins.build_finetune_dataset import (\n", "    torch,\n", "    np,\n", "    pack_sequences,\n", "    MMapIndexedDatasetBuilder,\n", ")\n", "\n", "\n", "def build_dataset(data: list[list[int]], output: typing.Union[pathlib.Path, str], sequence_length: int):\n", "    sequences = pack_sequences(data, sequence_length, -prompter.tokenizer.eod_id)\n", "    print(f\"Saving {len(sequences)} sequences to {output} ...\")\n", "    output_path = pathlib.Path(output)\n", "    output_path.parent.mkdir(parents=False, exist_ok=True)\n", "    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(\".bin\"), dtype=np.int32)\n", "    for sequence in sequences:\n", "        # Make the total sequence length to be sequence_length + 1\n", "        builder.add_item(torch.Tensor(sequence + [-prompter.tokenizer.eod_id]))\n", "        builder.end_document()\n", "    builder.finalize(output_path.with_suffix(\".idx\"))\n", "    print(f\"Saved {len(sequences)} sequences to {output_path}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["#examples=3173, #train=3000, #valid=173\n", "Skip 127 examples due to over-4096-tokens.\n", "Packed 3173 examples into 1941 4096-length-sequences.\n", "On average, the number of paddings is 1077.49.\n", "Saving 1941 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S4096-DSCI ...\n", "Saved 1941 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S4096-DSCI\n", "Skip 122 examples due to over-4096-tokens.\n", "Packed 3000 examples into 1839 4096-length-sequences.\n", "On average, the number of paddings is 1077.53.\n", "Saving 1839 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/train-S4096-DSCI ...\n", "Saved 1839 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/train-S4096-DSCI\n", "Skip 5 examples due to over-4096-tokens.\n", "Packed 173 examples into 102 4096-length-sequences.\n", "On average, the number of paddings is 1076.68.\n", "Saving 102 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S4096-DSCI ...\n", "Saved 102 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S4096-DSCI\n", "\n", "\n", "\n", "Skip 0 examples due to over-8192-tokens.\n", "Packed 3173 examples into 938 8192-length-sequences.\n", "On average, the number of paddings is 1330.04.\n", "Saving 938 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S8192-DSCI ...\n", "Saved 938 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S8192-DSCI\n", "Skip 0 examples due to over-8192-tokens.\n", "Packed 3000 examples into 890 8192-length-sequences.\n", "On average, the number of paddings is 1331.61.\n", "Saving 890 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/train-S8192-DSCI ...\n", "Saved 890 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/train-S8192-DSCI\n", "Skip 0 examples due to over-8192-tokens.\n", "Packed 173 examples into 48 8192-length-sequences.\n", "On average, the number of paddings is 1300.85.\n", "Saving 48 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S8192-DSCI ...\n", "Saved 48 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S8192-DSCI\n", "\n", "\n", "\n", "Skip 0 examples due to over-16384-tokens.\n", "Packed 3173 examples into 430 16384-length-sequences.\n", "On average, the number of paddings is 1415.34.\n", "Saving 430 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S16384-DSCI ...\n", "Saved 430 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S16384-DSCI\n", "Skip 0 examples due to over-16384-tokens.\n", "Packed 3000 examples into 408 16384-length-sequences.\n", "On average, the number of paddings is 1418.93.\n", "Saving 408 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/train-S16384-DSCI ...\n", "Saved 408 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/train-S16384-DSCI\n", "Skip 0 examples due to over-16384-tokens.\n", "Packed 173 examples into 22 16384-length-sequences.\n", "On average, the number of paddings is 1348.77.\n", "Saving 22 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S16384-DSCI ...\n", "Saved 22 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/valid-S16384-DSCI\n", "\n", "\n", "\n"]}], "source": ["print(f\"#examples={len(examples)}, #train={len(train_examples)}, #valid={len(valid_examples)}\")\n", "\n", "for seq_len in (4096, 8192, 16384):\n", "    build_dataset(examples, save_dir / f\"full-S{seq_len}-DSCI\", seq_len)\n", "    build_dataset(train_examples, save_dir / f\"train-S{seq_len}-DSCI\", seq_len)\n", "    build_dataset(valid_examples, save_dir / f\"valid-S{seq_len}-DSCI\", seq_len)\n", "    print(\"\\n\\n\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}