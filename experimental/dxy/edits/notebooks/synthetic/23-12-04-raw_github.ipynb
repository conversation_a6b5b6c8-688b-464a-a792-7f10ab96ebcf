{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import argparse\n", "import dataclasses\n", "import json\n", "import pathlib\n", "import typing\n", "\n", "import pandas as pd\n", "import pyspark.sql.functions as F\n", "import pyspark.sql.pandas.functions as FF\n", "import tqdm\n", "from pyspark.sql.types import IntegerType, StringType, StructField, StructType\n", "\n", "from experimental.dxy.edits import data_type\n", "from research.core import utils_for_file, utils_for_log\n", "from research.data.spark import k8s_session\n", "\n", "\n", "def create_spark(max_workers: int = 100):\n", "    spark = k8s_session(\n", "        max_workers=max_workers,\n", "        conf={\n", "            \"spark.sql.parquet.columnarReaderBatchSize\": \"128\",\n", "            \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "            \"spark.task.maxFailures\": \"10\",\n", "        },\n", "    )\n", "    return spark\n", "\n", "\n", "spark = create_spark()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")\n", "py_files_df = (\n", "    files_df.select(\n", "        F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "        F.col(\"max_stars_repo_path\").alias(\"file_path\"),\n", "        F.col(\"id\").alias(\"file_sha\"),\n", "        <PERSON><PERSON>col(\"lang\"),\n", "        <PERSON><PERSON>col(\"content\"),\n", "        <PERSON>.col(\"repo_size\"),\n", "    )\n", "    .filter(F.col(\"file_path\").endswith(\".py\"))\n", "    .filter(F.length(\"content\") < 1e6)\n", ")\n", "print(f\"There are {py_files_df.count()} files.\")\n", "# limited_df = files_df.sample(0.0005)\n", "# print(f\"#limited_df : {limited_df.count()}\")\n", "# pandas_df = limited_df.toPandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["py_files_df.printSchema()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["repo_names = py_files_df.select(F.col(\"repo\")).distinct().collect()\n", "print(f\"There are {len(repo_names)} repositories.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/cache-github-file\")\n", "assert save_dir.exists()\n", "\n", "py_files_df.printSchema()\n", "max_num_files = 1000\n", "\n", "for index in range(10000):\n", "    name = repo_names[index].repo\n", "    repo_df = py_files_df.filter(F.col(\"repo\") == name)\n", "    count = repo_df.count()\n", "    print(f\"{name:20s} has {repo_df.count()}\")\n", "    all_data = []\n", "    if count < max_num_files:\n", "        for index, row in repo_df.toPandas().iterrows():\n", "            all_data.append(row.to_dict())\n", "        org_name, repo_name = name.split(\"/\")\n", "        org_dir = save_dir / org_name\n", "        org_dir.mkdir(parents=False, exist_ok=True)\n", "        utils_for_file.write_jsonl_zst(org_dir / f\"{repo_name}.jsonl.zst\", all_data)\n", "    else:\n", "        print(f\"Skip {name} due to over {max_num_files} files.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}