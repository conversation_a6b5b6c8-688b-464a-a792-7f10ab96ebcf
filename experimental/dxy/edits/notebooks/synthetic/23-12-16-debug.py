"""DEBUG FILE."""
import pathlib
import pdb

from experimental.dxy.edits.pipelines.simple_selected_code_v2 import (
    post_process_selected_code,
)
from research.core import utils_for_dataclass, utils_for_file, utils_for_str
from research.data.synthetic_code_edit import sampling_lib, seed_lib, types

seeds = seed_lib.load_code_edit_seeds()
seeds = {k: v for k, v in seeds.items() if len(v) > 0}
print(f"Loaded {len(seeds)} categories: {list(seeds.keys())}")

raw_github_data = [
    utils_for_dataclass.create_from_dict(types.CodeEditData, x)
    for x in utils_for_file.read_jsonl_zst(
        "/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst"
    )
]
print(f"Loaded {len(raw_github_data)} examples.")


data_path = pathlib.Path(
    "/mnt/efs/augment/user/dxy/visualization/json-data-r0/001-2642.json"
)
data_dict = utils_for_file.read_json(data_path)
data_to_debug = utils_for_dataclass.create_from_dict(types.CodeEditData, data_dict)

ori_x = raw_github_data[1]
selected_code_with_marker = utils_for_str.extract_the_last_markdown_block(
    data_to_debug.metadata["stage_1@dialogue"][-1]
)
context_length = data_to_debug.metadata["stage_1@context_length"]
assert selected_code_with_marker is not None
assert ori_x.updated_code is not None

lindex_to_minimal_scope = sampling_lib.find_minimal_self_contained_range_per_line(
    ori_x.prefix + ori_x.updated_code + ori_x.suffix
)

new_data = post_process_selected_code(ori_x, context_length, selected_code_with_marker)
pdb.set_trace()
print("-")
