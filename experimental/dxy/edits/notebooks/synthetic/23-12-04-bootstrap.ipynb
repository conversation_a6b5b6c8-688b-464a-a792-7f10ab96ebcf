{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generate Code Edit Category (from <PERSON>)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import re\n", "import copy\n", "import random\n", "from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat\n", "\n", "SYSTEM_PROMPT = r\"\"\"Context: In Python, the function 'def edit(prefix: str, selected_code: str, suffix: str, instruction: str) -> str' is used to modify 'selected_code' into 'updated_code' based on the provided 'instruction'.\n", "\n", "Task: Generate a list of typical code editing categories used by developers.\n", "\n", "Requirements:\n", "- Provide two categories at a time, without any introductory or additional text.\n", "- Category names should only contain alphanumeric characters and maximum one hyphen (\"-\").\n", "- Each category must be practical and realistically applicable.\n", "- Initially, you will receive example categories from the user. Based on these, generate new categories.\n", "\"\"\"\n", "\n", "\n", "def generate_categories_one_round(seeds: list[str], shot: int, target_num: int):\n", "    \"\"\"Generate categories.\"\"\"\n", "    results = copy.deepcopy(seeds)\n", "    round = 0\n", "    while len(results) < target_num:\n", "        samples = random.sample(seeds, shot)\n", "        messages = [\"\\n\".join(samples)]\n", "        [response] = generate_response_via_chat(\n", "            messages=messages,\n", "            system_prompt=SYSTEM_PROMPT,\n", "            temperature=1.0,\n", "            max_tokens=128,\n", "            model=\"gpt-4-0613\",\n", "            num_completion=1,\n", "        )\n", "        answers = [x.strip() for x in response.splitlines(False) if x.strip()]\n", "\n", "        def is_good_answer(x):\n", "            if not x:\n", "                return False\n", "            if x in seeds or x in results:\n", "                return False\n", "            if \"\\n\" in x or \" \" in x or x.count(\"-\") > 1:\n", "                return False\n", "            if not re.match(r\"^[a-zA-Z][a-zA-Z-]*$\", x):\n", "                return False\n", "            return True\n", "\n", "        answers = [x for x in answers if is_good_answer(x)]\n", "        results.extend(answers)\n", "        print(f\"After the {round:2d}-th iteration, we have {len(results)} categories.\")\n", "        round += 1\n", "    return results"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After the 0-th iteration, we have 12 categories.\n", "After the 1-th iteration, we have 14 categories.\n", "After the 2-th iteration, we have 29 categories.\n", "After the 0-th iteration, we have 30 categories.\n", "After the 0-th iteration, we have 30 categories.\n", "After the 1-th iteration, we have 30 categories.\n", "After the 2-th iteration, we have 31 categories.\n", "After the 3-th iteration, we have 32 categories.\n", "After the 4-th iteration, we have 33 categories.\n", "After the 5-th iteration, we have 33 categories.\n", "After the 6-th iteration, we have 34 categories.\n", "After the 7-th iteration, we have 34 categories.\n", "After the 8-th iteration, we have 35 categories.\n", "After the 9-th iteration, we have 37 categories.\n", "After the 10-th iteration, we have 37 categories.\n", "After the 11-th iteration, we have 37 categories.\n", "After the 12-th iteration, we have 38 categories.\n", "After the 13-th iteration, we have 40 categories.\n", "After the 0-th iteration, we have 41 categories.\n", "After the 1-th iteration, we have 49 categories.\n", "After the 2-th iteration, we have 49 categories.\n", "After the 3-th iteration, we have 50 categories.\n"]}], "source": ["seed_categories = [\n", "    \"formatting\",\n", "    \"linting\",\n", "    \"refactoring\",\n", "    \"documenting\",\n", "    \"debugging\",\n", "    \"fixing\",\n", "    \"performance\",\n", "    \"optimization\",\n", "    \"variable-renaming\",\n", "    \"structure-data\",\n", "    \"language-translating\",\n", "]\n", "\n", "categories_0 = seed_categories\n", "categories_1 = generate_categories_one_round(categories_0, shot=5, target_num=20)\n", "categories_2 = generate_categories_one_round(categories_1, shot=5, target_num=30)\n", "categories_3 = generate_categories_one_round(categories_2, shot=5, target_num=40)\n", "categories_4 = generate_categories_one_round(categories_3, shot=5, target_num=50)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["formatting\n", "linting\n", "refactoring\n", "documenting\n", "debugging\n", "fixing\n", "performance\n", "optimization\n", "variable-renaming\n", "structure-data\n", "language-translating\n", "error-handling\n", "code-refactoring\n", "performance-enhancement\n", "code-styling\n", "logic-improvement\n", "syntax-correcting\n", "version-updating\n", "code-simplifying\n", "library-integrating\n", "feature-adding\n", "method-overriding\n", "inheritance-implementing\n", "test-coding\n", "template-creating\n", "multithreading\n", "security-enhancing\n", "exception-handling\n", "code-cleaning\n", "function-optimizing\n", "function-decorating\n", "code-formatting\n", "code-optimization\n", "bug-fixing\n", "object-orienting\n", "debugging-code\n", "optimizing-performance\n", "unit-testing\n", "memory-optimizing\n", "debugging-troubleshooting\n", "refactoring-code\n", "security-patching\n", "object-refactoring\n", "commenting\n", "performance-improving\n", "algorithm-updating\n", "oop-designing\n", "better-logging\n", "thread-optimizing\n", "memory-management\n"]}], "source": ["print(\"\\n\".join(categories_4))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import re\n", "import copy\n", "import random\n", "from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat\n", "\n", "DEDUP_SYSTEM_PROMPT = r\"\"\"Context: In Python, the function 'def edit(prefix: str, selected_code: str, suffix: str, instruction: str) -> str' is used to modify 'selected_code' into 'updated_code' based on the provided 'instruction'.\n", "\n", "Task: The user has provided a list of typical code editing categories used by developers.\n", "However, there are some redundant categories, similar categories, and some categories are not realistic.\n", "Your task is to generate new categories that are more realistic, more practical, more reasonable, less ambiguity based on the user provided categories.\n", "\n", "Requirements:\n", "- Initially, you will receive categories from the user and the required number of categories.\n", "- Your results should only contain the new categories, where each category sits on a separate line without any other text.\n", "- Try to keep each edit category at the same granularity and only use two words for each category.\n", "\"\"\"\n", "\n", "message = (\n", "    \"Categories:\\n\"\n", "    + \"\\n- \".join(categories_4)\n", "    + \"\\n\\n\"\n", "    + \"Required number of categories: 20\"\n", ")\n", "[response] = generate_response_via_chat(\n", "    messages=[message],\n", "    system_prompt=DEDUP_SYSTEM_PROMPT,\n", "    temperature=0.3,\n", "    max_tokens=256,\n", "    model=\"gpt-4-0613\",\n", "    num_completion=1,\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["[response_1] = generate_response_via_chat(\n", "    messages=[\n", "        message,\n", "        response,\n", "        \"I hope to also include a category for translation (for example translate bash to python), help me improve the categories.\",\n", "    ],\n", "    system_prompt=DEDUP_SYSTEM_PROMPT,\n", "    temperature=0.3,\n", "    max_tokens=256,\n", "    model=\"gpt-4-0613\",\n", "    num_completion=1,\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Code-Formatting\n", "Logic-Refactoring\n", "Performance-Optimization\n", "Variable-Renaming\n", "Error-Management\n", "Library-Integration\n", "Feature-Addition\n", "Test-Writing\n", "Security-Enhancement\n", "Exception-Handling\n", "Memory-Optimization\n", "Unit-Testing\n", "Security-Patching\n", "Algorithm-Updating\n", "Logging-Improvement\n", "Thread-Management\n", "Inheritance-Implementation\n", "Code-Cleaning\n", "Translation-Implementing\n", "Documentation-Updating\n"]}], "source": ["# print(response)\n", "print(response_1)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file\n", "\n", "save_dir = pathlib.Path(\n", "    \"/home/<USER>/src/augment/experimental/dxy/edits/seeds/categories.json\"\n", ")\n", "utils_for_file.write_json(\n", "    save_dir, [x.strip() for x in response_1.splitlines()], indent=2\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}