{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Visualize the CommitPack FT Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the CommitPackFT data\n", "import tqdm\n", "from research.core import utils_for_dataclass\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits import data_type\n", "\n", "all_raw_edit_from_cpft = utils_for_file.read_jsonl_zst(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.processed/commitpackft.all.jsonl.zst\"\n", ")\n", "print(f\"Load {len(all_raw_edit_from_cpft)} examples from commitpackft.all.jsonl.zst\")\n", "python_cpft_edit_from_cpft = []\n", "for x in tqdm.tqdm(all_raw_edit_from_cpft, total=len(all_raw_edit_from_cpft)):\n", "    if x[\"language\"] != \"Python\":\n", "        continue\n", "    x = utils_for_dataclass.create_from_dict(data_type.EditData, x)\n", "    python_cpft_edit_from_cpft.append(x)\n", "print(\n", "    f\"Load {len(python_cpft_edit_from_cpft)} Python-Only examples from commitpackft.all.jsonl.zst\"\n", ")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fix implied_group, it still refers to the old module name\n", "[ ] \n", "[ ]     _columns = {\n", "[ ]         'group_use_product_description_per_inv_line': fields.boolean(\n", "[ ]             \"\"\"Allow using only the product description on the\n", "[ ]             invoice order lines\"\"\",\n", "[-]             implied_group=\"invoice_line_description.\"\n", "[+]             implied_group=\"account_invoice_line_description.\"\n", "[ ]             \"group_use_product_description_per_inv_line\",\n", "[ ]             help=\"\"\"Allows you to use only product description on the\n", "[ ]             invoice order lines.\"\"\"\n", "[ ]         ),\n", "[ ]     }\n", "\n"]}], "source": ["index = 4\n", "\n", "print(python_cpft_edit_from_cpft[index].instruction)\n", "print(python_cpft_edit_from_cpft[index].get_diff_code_in_context(5, 5))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}