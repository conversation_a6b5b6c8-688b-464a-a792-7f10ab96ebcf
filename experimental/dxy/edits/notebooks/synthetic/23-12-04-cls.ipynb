{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "import pathlib\n", "from collections import defaultdict\n", "from base.ranges.range_types import LineRange\n", "from research.core import constants, utils_for_file, utils_for_dataclass\n", "from research.utils import dataclass_utils\n", "from experimental.dxy.edits.util_lib import load_instruct_coder_data\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "ic_additional_seeds = load_instruct_coder_data()\n", "ic_github_seeds = load_instruct_coder_data(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/github_seed.json\"\n", "    )\n", ")\n", "print(f\"#ic_additional_seeds = {len(ic_additional_seeds)}\")\n", "print(f\"#ic_github_seeds = {len(ic_github_seeds)}\")\n", "\n", "categories = utils_for_file.read_json(\n", "    constants.AUGMENT_ROOT\n", "    / \"research\"\n", "    / \"data\"\n", "    / \"synthetic_code_edit\"\n", "    / \"categories.json\"\n", ")\n", "categories = tuple(categories)\n", "print(f\"#categories = {len(categories)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ic_additional_seeds[3].show_code_w_updated_code_and_instruction()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_category import GenerateEditCategoryV0\n", "\n", "pipeline = GenerateEditCategoryV0(categories=categories)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["x = pipeline.generate(ic_additional_seeds[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ic_additional_seeds[0].show_code_w_updated_code_and_instruction()\n", "print(\"\\n\\n\")\n", "print(x[0][0].misc['category@edit'])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}