{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3 categories: ['Formatting-Cleaning', 'Refactoring', 'Bug-Fixing']\n", "Loaded 2642 examples.\n"]}], "source": ["import dataclasses\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from research.data.synthetic_code_edit import types\n", "from research.data.synthetic_code_edit import seed_lib\n", "from research.data.synthetic_code_edit import sampling_lib\n", "from research.data.synthetic_code_edit import util_lib\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "seeds = seed_lib.load_code_edit_seeds()\n", "seeds = {k: v for k, v in seeds.items() if len(v) > 0}\n", "print(f\"Loaded {len(seeds)} categories: {list(seeds.keys())}\")\n", "\n", "raw_github_data = [\n", "    utils_for_dataclass.create_from_dict(types.CodeEditData, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst\"\n", "    )\n", "]\n", "print(f\"Loaded {len(raw_github_data)} examples.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 17 seeds with categories of ['Refactoring', 'Formatting-Cleaning', 'Bug-Fixing']\n"]}], "source": ["seeds_python = [\n", "    x for v in seeds.values() for x in v if x.metadata[\"language\"] == \"python\"\n", "]\n", "categories = set([x.metadata[\"category\"] for x in seeds_python])\n", "print(f\"Loaded {len(seeds_python)} seeds with categories of {list(categories)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generation"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[GenerateCategoryFewShot.init] Loaded 3 categories: ('Formatting-Cleaning', 'Refactoring', 'Bug-Fixing')\n", "[GenerateCategoryFewShot.init] Formatting-Cleaning : 4 seed examples\n", "[GenerateCategoryFewShot.init] Refactoring         : 3 seed examples\n", "[GenerateCategoryFewShot.init] Bug-Fixing          : 10 seed examples\n", "[GenerateInverseInstructionFewShot.init] Loaded 3 categories: ('Formatting-Cleaning', 'Refactoring', 'Bug-Fixing')\n", "[GenerateInverseInstructionFewShot.init] Formatting-Cleaning : 4 seed examples\n", "[GenerateInverseInstructionFewShot.init] Refactoring         : 3 seed examples\n", "[GenerateInverseInstructionFewShot.init] Bug-Fixing          : 10 seed examples\n", "[GenerateSelectedCode.init] Loaded 3 categories: ('Formatting-Cleaning', 'Refactoring', 'Bug-Fixing')\n", "[GenerateSelectedCode.init] Formatting-Cleaning : 4 seed examples\n", "[GenerateSelectedCode.init] Refactoring         : 3 seed examples\n", "[GenerateSelectedCode.init] Bug-Fixing          : 10 seed examples\n", "[GenerateInstructionV0.init] Loaded 3 categories: ('Formatting-Cleaning', 'Refactoring', 'Bug-Fixing')\n", "[GenerateInstructionV0.init] Formatting-Cleaning : 4 seed examples\n", "[GenerateInstructionV0.init] Refactoring         : 3 seed examples\n", "[GenerateInstructionV0.init] Bug-Fixing          : 10 seed examples\n", "[GenerateCategoryFewShot.sample_samples] 4 -> 4 -> 2 samples ((3, 2)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix')).\n", "[GenerateCategoryFewShot.sample_samples] 3 -> 3 -> 2 samples ((1, 0)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix')).\n", "[GenerateCategoryFewShot.sample_samples] 10 -> 10 -> 2 samples ((3, 2)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix')).\n", "Category: Refactoring\n", "[GenerateInverseInstructionFewShot.sample_samples] 3 -> 3 -> 2 samples ((0, 1)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix', 'inverse_instructions')).\n", "[GenerateSelectedCode.sample_samples] 3 -> 3 -> 3 samples ((1, 2, 0)) (num=3, keys_to_check=('prefix', 'selected_code', 'suffix', 'updated_code', 'inverse_instructions')).\n", "[GenerateInstructionV0.sample_samples] 3 -> 3 -> 3 samples ((0, 1, 2)) (num=3, keys_to_check=('prefix', 'selected_code', 'suffix', 'updated_code', 'instructions')).\n", "[GenerateCategoryFewShot.sample_samples] 4 -> 4 -> 2 samples ((1, 0)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix')).\n", "[GenerateCategoryFewShot.sample_samples] 3 -> 3 -> 2 samples ((1, 0)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix')).\n", "[GenerateCategoryFewShot.sample_samples] 10 -> 10 -> 2 samples ((9, 4)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix')).\n", "Category: Refactoring\n", "[GenerateInverseInstructionFewShot.sample_samples] 3 -> 3 -> 2 samples ((2, 0)) (num=2, keys_to_check=('prefix', 'updated_code', 'suffix', 'inverse_instructions')).\n", "[GenerateSelectedCode.sample_samples] 3 -> 3 -> 3 samples ((2, 1, 0)) (num=3, keys_to_check=('prefix', 'selected_code', 'suffix', 'updated_code', 'inverse_instructions')).\n", "[GenerateInstructionV0.sample_samples] 3 -> 3 -> 3 samples ((1, 2, 0)) (num=3, keys_to_check=('prefix', 'selected_code', 'suffix', 'updated_code', 'instructions')).\n"]}], "source": ["import pathlib\n", "from experimental.dxy.edits.pipelines.generate_category import (\n", "    GenerateCategoryFewShot,\n", ")\n", "from experimental.dxy.edits.pipelines.generate_inverse_instruction import (\n", "    GenerateInverseInstructionFewShot,\n", ")\n", "from experimental.dxy.edits.pipelines.generate_selected_code import (\n", "    GenerateSelectedCode,\n", ")\n", "from experimental.dxy.edits.pipelines.generate_instruction import (\n", "    GenerateInstructionV0,\n", ")\n", "\n", "pipeline_category = GenerateCategoryFewShot(\n", "    SEED_EXAMPLES=tuple(seeds_python), num_samples_per_category=2, DEBUG=True\n", ")\n", "pipeline_inverseinst = GenerateInverseInstructionFewShot(\n", "    SEED_EXAMPLES=tuple(seeds_python), DEBUG=True\n", ")\n", "pipeline_selection = GenerateSelectedCode(\n", "    SEED_EXAMPLES=tuple(seeds_python), num_samples_from_seed=3, DEBUG=True\n", ")\n", "pipeline_instruction = GenerateInstructionV0(\n", "    SEED_EXAMPLES=tuple(seeds_python), num_samples_from_seed=3, DEBUG=True\n", ")\n", "\n", "for index in range(1, 3):\n", "    target_sample = raw_github_data[index]\n", "    sample_v1, meta_info_v1 = pipeline_category.generate(target_sample)\n", "    category = sample_v1.metadata[\"category\"]\n", "    print(f\"Category: {category}\")\n", "    sample_v2, meta_info_v2 = pipeline_inverseinst.generate(\n", "        sample_v1, category=category\n", "    )\n", "    sample_v3, meta_info_v3 = pipeline_selection.generate(sample_v2, category=category)\n", "    sample_v4, meta_info_v4 = pipeline_instruction.generate(\n", "        sample_v3, category=category\n", "    )\n", "    html_str = util_lib.get_html_str(sample_v4)\n", "    save_path = pathlib.Path(f\"/home/<USER>/cache/vis/debug/test-{index:03d}.html\")\n", "    save_path.parent.mkdir(parents=True, exist_ok=True)\n", "    with save_path.open(\"w\") as f:\n", "        f.write(html_str)\n", "    print(f\"Save into : {save_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sample_v2 = utils_for_dataclass.create_from_dict(\n", "#     types.CodeEditData,\n", "#     utils_for_file.read_json(\n", "#         \"/home/<USER>/cache/vis/12-14-NEW/raw_data/003-2642.json\"\n", "#     ),\n", "# )\n", "# category = sample_v2.metadata[\"category\"]\n", "# # sample_v2, meta_info_v2 = pipeline_inverseinst.generate(sample_v1, category=category)\n", "# sample_v3, meta_info_v3 = pipeline_selection.generate(sample_v2, category=category, model=\"gpt-4-1106-preview\")\n", "# # sample_v4, meta_info_v4 = pipeline_instruction.generate(sample_v3, category=category)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sample_v3.selected_code = pipeline_selection._parse_for_target(meta_info_v3[\"responses\"][0], sample_v3)\n", "# print(sample_v3.get_pretty_diff_code(max_context_lines=10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(meta_info_v1[\"messages\"][0])\n", "# print(meta_info_v1[\"responses\"][0])\n", "\n", "# print(meta_info_v2[\"messages\"][0])\n", "# print(meta_info_v2[\"responses\"][1])\n", "\n", "# print(meta_info_v3[\"messages\"][0])\n", "# print(meta_info_v3[\"responses\"][0])\n", "# print(sample_v3.selected_code)\n", "\n", "# print(meta_info_v4[\"messages\"][0])\n", "# print(meta_info_v4[\"responses\"][0])\n", "# print(meta_info_v4[\"responses\"][1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # print(meta_info_v3[\"responses\"][0])\n", "# print(meta_info_v2[\"responses\"][0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # print(meta_info_v3[\"responses\"][0])\n", "# pipeline_selection._parse_for_target(meta_info_v3[\"responses\"][0], sample_v2)\n", "# print(meta_info_v4[\"messages\"][0])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}