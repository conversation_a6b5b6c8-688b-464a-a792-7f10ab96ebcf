{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from collections import defaultdict\n", "from base.ranges.range_types import LineRange\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from research.utils import dataclass_utils\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope\n", "\n", "# seeds = [\n", "#     utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "#     for x in utils_for_file.read_jsonl(\n", "#         \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-seeds_via_icoder_addseed.jsonl\"\n", "#     )\n", "# ]\n", "seeds = [\n", "    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "    for x in utils_for_file.read_json(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-seeds_via_icoder_addseed.json\"\n", "    )\n", "]\n", "augmented_seeds = [\n", "    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "    for x in utils_for_file.read_json(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-augmented_seeds_via_icoder_addseed.json\"\n", "    )\n", "]\n", "\n", "final_edit_scopes = [\n", "    utils_for_dataclass.create_from_dict(SimpleRawEditScope, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/raw-edit-scopes.jsonl.zst\"\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# final_edit_scopes[0].show_code(max_context_lines=20)\n", "seeds[0].show_code_w_updated_code_and_instruction(30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(seeds[0].misc.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation\n", "\n", "- Step 1: generate reverse instruction for candidate example\n", "- Step 2: generate the selected code.\n", "- Step 3: generate the instruction."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionFewShot,\n", ")\n", "from experimental.dxy.edits.pipelines.generate_selected_code import (\n", "    GenerateSelectedCodeViaReverseInstruction,\n", "    GenerateInstructionViaSUCodeRI,\n", ")\n", "\n", "pipeline_1 = GenerateReverseInstructionFewShot(DEBUG=True, num_of_completion=1)\n", "pipeline_2 = GenerateSelectedCodeViaReverseInstruction(DEBUG=True, num_of_completion=1)\n", "pipeline_3 = GenerateInstructionViaSUCodeRI(DEBUG=True, num_of_completion=1)\n", "\n", "target_sample = final_edit_scopes[0]\n", "\n", "[sample_w_reverse_instruction], messages_1 = pipeline_1.generate(\n", "    target_sample, augmented_seeds, []\n", ")\n", "[sample_w_selected_code], messages_2 = pipeline_2.generate(\n", "    sample_w_reverse_instruction, augmented_seeds, []\n", ")\n", "[sample_w_annotation], messages_3 = pipeline_3.generate(\n", "    sample_w_selected_code, augmented_seeds, []\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        ns = namespaces.Namespace(\n", "            'qrouter-bar', self.conf, agent.driver, agent.use_ipv6)\n", "        ns.create()\n", "        ns.delete()\n", "        self.mock_ip.netns.delete.assert_called_once_with(\"qrouter-bar\")\n", "\n", "    def test_destroy_snat_namespace(self):\n", "        namespace = 'snat-bar'\n", "\n", "        self.list_network_namespaces.return_value = [namespace]\n", "        self.mock_ip.get_devices.return_value = [\n", "            l3_test_common.FakeDev('qg-aaaa'),\n", "            l3_test_common.FakeDev('sg-aaaa')]\n", "\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "\n", "        ns = dvr_snat_ns.SnatNamespace(\n", "            'bar', self.conf, agent.driver, agent.use_ipv6)\n", "        ns.create()\n", "\n", "        ns.delete()\n", "        calls = [mock.call('qg-aaaa',\n", "                           namespace=namespace,\n", "                           prefix=namespaces.EXTERNAL_DEV_PREFIX),\n", "                 mock.call('sg-aaaa',\n", "                           namespace=namespace,\n", "                           prefix=lib_constants.SNAT_INT_DEV_PREFIX)]\n", "        self.mock_driver.unplug.assert_has_calls(calls, any_order=True)\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m    def _configure_metadata_proxy(self, enableflag=True):\n", "\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        router_id = _uuid()\n", "        router = {'id': router_id,\n", "                  'external_gateway_info': {},\n", "                  'routes': [],\n", "                  'distributed': False}\n", "        driver = metadata_driver.MetadataDriver\n", "\n", "        ipv6_mock.return_value = True\n", "        agent._process_added_router(router)\n", "\n", "        agent._safe_router_removed(router_id)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Implement conditional logic to handle the enabling or disabling of the metadata proxy within the '_configure_metadata_proxy' function based on the 'enableflag'.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    def _configure_metadata_proxy(self, enableflag=True):\n", "        if not enableflag:\n", "            self.conf.set_override('enable_metadata_proxy', False)\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        router_id = _uuid()\n", "        router = {'id': router_id,\n", "                  'external_gateway_info': {},\n", "                  'routes': [],\n", "                  'distributed': False}\n", "        driver = metadata_driver.MetadataDriver\n", "        with mock.patch.object(\n", "                driver, 'destroy_monitored_metadata_proxy') as destroy_proxy, \\\n", "                mock.patch.object(\n", "                    driver, 'spawn_monitored_metadata_proxy') as spawn_proxy, \\\n", "                mock.patch.object(netutils, 'is_ipv6_enabled') as ipv6_mock:\n", "            ipv6_mock.return_value = True\n", "            agent._process_added_router(router)\n", "            if enableflag:\n", "                spawn_proxy.assert_called_with(\n", "                    mock.ANY,\n", "                    mock.ANY,\n", "                    self.conf.metadata_port,\n", "                    mock.ANY,\n", "                    router_id=router_id,\n", "                    bind_address='::',\n", "                )\n", "            else:\n", "                self.assertFalse(spawn_proxy.call_count)\n", "            agent._safe_router_removed(router_id)\n", "            if enableflag:\n", "                destroy_proxy.assert_called_with(mock.ANY,\n", "                                                 router_id,\n", "                                                 mock.ANY,\n", "                                                 'qrouter-' + router_id)\n", "            else:\n", "                self.assertFalse(destroy_proxy.call_count)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def test_enable_metadata_proxy(self):\n", "        self._configure_metadata_proxy()\n", "\n", "    def test_disable_metadata_proxy_spawn(self):\n", "        self._configure_metadata_proxy(enableflag=False)\n", "\n", "    def test__process_router_added_router_not_in_cache_after_failure(self):\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        router_id = _uuid()\n", "        router = {'id': router_id}\n", "        ri_mock = mock.Mock()\n", "\n", "        class TestRouterProcessingException(Exception):\n", "            pass\n", "\n", "        with mock.patch.object(agent, \"_router_added\"), \\\n", "                mock.patch.dict(agent.router_info, {router_id: ri_mock}):\n", "            ri_mock.process.side_effect = TestRouterProcessingException()\n", "            self.assertRaises(\n", "                TestRouterProcessingException,\n", "                agent._process_added_router, router)\n", "            self.assertNotIn(router_id, agent.router_info)\n", "\n", "    def _test_process_routers_update_rpc_timeout(self, ext_net_call=False,\n", "                                                 ext_net_call_failed=False):\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        agent.fullsync = False\n", "        agent._process_router_if_compatible = mock.Mock()\n", "        router_id = _uuid()\n", "\u001b[0m"]}], "source": ["sample_w_annotation.show_code_w_updated_code_and_instruction(max_context_lines=30)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['reverse_instruction'])\n", "dict_keys(['reverse_instruction', 'thinking_procedure@selected_code', 'updated_code'])\n", "dict_keys(['reverse_instruction', 'thinking_procedure@selected_code', 'updated_code', 'instruction'])\n"]}], "source": ["print(sample_w_reverse_instruction.misc.keys())\n", "print(sample_w_selected_code.misc.keys())\n", "print(sample_w_annotation.misc.keys())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sample_w_selected_code.misc[\"reverse_instruction\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation (<PERSON><PERSON> Seed's Instruction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionZeroShot,\n", ")\n", "\n", "pipeline = GenerateReverseInstructionZeroShot()\n", "\n", "answers, messages = pipeline.generate(seeds[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[0].show_code_w_updated_code_and_instruction()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(answers[0].misc[\"reverse_instruction\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation (Reverse Instruction for Candidate Example)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_reverse_instruction import (\n", "    GenerateReverseInstructionFewShot,\n", ")\n", "\n", "pipeline = GenerateReverseInstructionFewShot()\n", "\n", "answers, messages = pipeline.generate(final_edit_scopes[0], augmented_seeds, [])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for idx, message in enumerate(messages):\n", "#     print(\"*\" * 10 + f\" {idx:02d} \" + \"*\" * 10)\n", "#     print(message)\n", "answers[0].show_code(max_context_lines=20)\n", "print(\"*\" * 100)\n", "print(answers[0].misc[\"reverse_instruction\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic Generation (Generate Instruction and Selected Code in One-shot)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.generate_both import (\n", "    GenerateInstructionAndSelection,\n", ")\n", "\n", "pipeline = GenerateInstructionAndSelection(\n", "    DEBUG=True, num_samples_from_seed=4, num_samples_from_machine=2\n", ")\n", "\n", "answers, messages = pipeline.generate(\n", "    final_edit_scopes[0],\n", "    seeds,\n", "    [],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[0].show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[1].show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[2].show_code_w_updated_code_and_instruction()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["answers[4].show_code_w_updated_code_and_instruction()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}