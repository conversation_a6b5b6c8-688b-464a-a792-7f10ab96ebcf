{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load PR Data V2\n", "import typing\n", "from research.core.utils_for_file import read_json, read_jsonl\n", "from research.data.synthetic_code_edit.types import CodeEditData, Instruction\n", "from experimental.dxy.edits.temp_lib import filter_code_edit_data\n", "\n", "\n", "def load_pr_data_v2() -> list[CodeEditData]:\n", "    PR_DATA_PATH = \"/mnt/efs/augment/user/dxy/datasets/edit.raw/edit-pr-data/pr_suggested_edits.v2.jsonl\"\n", "    PR_V2_LIST_OF_DATA = read_jsonl(PR_DATA_PATH)\n", "    print(f\"Loaded {len(PR_V2_LIST_OF_DATA)} raw PR examples.\")\n", "    all_data: list[CodeEditData] = []\n", "    corrected, others, wrong = 0, 0, 0\n", "    for data in PR_V2_LIST_OF_DATA:\n", "        selected_code = data[\"old_code\"]\n", "        updated_code = data[\"new_code\"]\n", "        prefix = data[\"prefix\"]\n", "        suffix = data[\"suffix\"]\n", "        if not selected_code or not updated_code:\n", "            continue\n", "        elif selected_code[-1] != \"\\n\" and updated_code[-1] != \"\\n\" and suffix:\n", "            selected_code += \"\\n\"\n", "            updated_code += \"\\n\"\n", "            corrected += 1\n", "        elif selected_code[-1] == \"\\n\" and updated_code[-1] != \"\\n\":\n", "            wrong += 1\n", "            continue\n", "        elif selected_code[-1] != \"\\n\" and updated_code[-1] == \"\\n\":\n", "            wrong += 1\n", "            continue\n", "        else:\n", "            others += 1\n", "        instructions = [\n", "            Instruction(data[\"revised_short_instruction\"], None),\n", "            # Instruction(data[\"revised_verbose_instruction\"], None),\n", "        ]\n", "        x = CodeEditData(\n", "            repo_url=data[\"repo_name\"],\n", "            file_name=data[\"path\"],\n", "            prefix=prefix,\n", "            suffix=suffix,\n", "            selected_code=selected_code,\n", "            updated_code=updated_code,\n", "            instructions=instructions,\n", "        )\n", "        all_data.append(x)\n", "    print(\n", "        f\"There are {len(all_data)}/{len(PR_V2_LIST_OF_DATA)} examples ({corrected} corrected, {others} others, {wrong} wrong) in total.\"\n", "    )\n", "    return all_data\n", "\n", "\n", "pr_data_v2 = load_pr_data_v2()\n", "pr_data_v2_filtered = filter_code_edit_data(pr_data_v2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 6854 data, 6854 data, and 5799 data.\n", "There are 6854 data, 5799 data, and 54 data.\n"]}], "source": ["import typing\n", "from research.core.utils_for_file import read_json\n", "from research.data.synthetic_code_edit.types import CodeEditData, Instruction\n", "from research.data.synthetic_code_edit.util_lib import read_vendor_data_w_annotation\n", "\n", "\n", "def convert_fn(\n", "    xlist: typing.List[typing.Dict[str, typing.Any]]\n", ") -> typing.List[CodeEditData]:\n", "    \"\"\"Convert the json data to CodeEditData.\"\"\"\n", "    all_code_edit: list[CodeEditData] = []\n", "    for x in xlist:\n", "        instructions = [Instruction(ins, None) for ins in x[\"instructions\"]]\n", "        all_code_edit.append(\n", "            CodeEditData(\n", "                repo_url=x[\"repo_url\"],\n", "                file_name=x[\"path\"],\n", "                prefix=x[\"prefix\"],\n", "                suffix=x[\"suffix\"],\n", "                selected_code=x[\"old_middle\"],\n", "                updated_code=x[\"new_middle\"],\n", "                instructions=instructions,\n", "                metadata=x,\n", "            )\n", "        )\n", "    return all_code_edit\n", "\n", "\n", "raw_data_36 = read_json(\n", "    \"/mnt/efs/augment/user/igor/data/droid/droid-repo-36/prompts.json\"\n", ")\n", "raw_data_38 = read_json(\n", "    \"/mnt/efs/augment/user/igor/data/droid/droid-repo-38/prompts.json\"\n", ")\n", "raw_data_39 = read_json(\n", "    \"/mnt/efs/augment/user/igor/data/droid/droid-repo-39/prompts.json\"\n", ")\n", "print(\n", "    f\"There are {len(raw_data_36)} data, {len(raw_data_38)} data, and {len(raw_data_39)} data.\"\n", ")\n", "data_38_in_edit = convert_fn(raw_data_38)\n", "data_39_in_edit = convert_fn(raw_data_39)\n", "data_vendor_dict = read_vendor_data_w_annotation()\n", "data_vendor = []\n", "for k, v in data_vendor_dict.items():\n", "    data_vendor.extend(v)\n", "print(\n", "    f\"There are {len(data_38_in_edit)} data, {len(data_39_in_edit)} data, and {len(data_vendor)} data.\"\n", ")\n", "all_data = data_38_in_edit + data_39_in_edit + data_vendor + pr_data_v2_filtered"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Visualize the data.\n", "# from experimental.dxy.edits import vis_lib\n", "\n", "# vis_lib.plot_edit_scope(\n", "#     all_data,\n", "#     filepath=\"/home/<USER>/cache/vis-results-24-01-24/data_sync38_39_vendor.pdf\",\n", "# )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Prepare Prompt: 100%|██████████| 64167/64167 [31:54<00:00, 33.52it/s]  \n"]}], "source": ["import tqdm\n", "import copy\n", "import random\n", "from research.core.model_input import ModelInput\n", "from research.core.edit_prompt_formatters import DroidEditPromptFormatter\n", "\n", "\n", "def editdata_to_tokens(\n", "    data: list[CodeEditData], formatter: DroidEditPromptFormatter\n", ") -> list[list[int]]:\n", "    \"\"\"Convert a list of CodeEditData to a list of tokens.\"\"\"\n", "    tokens_per_example: list[list[int]] = []\n", "    for ex in tqdm.tqdm(data, total=len(data), desc=\"Prepare Prompt\"):\n", "        assert ex.instructions is not None\n", "        assert ex.selected_code is not None\n", "        assert ex.updated_code is not None\n", "        instruction = random.choice(ex.instructions).text\n", "        minput = ModelInput(\n", "            prefix=ex.prefix,\n", "            suffix=ex.suffix,\n", "            extra={\n", "                \"instruction\": instruction,\n", "                \"selected_code\": ex.selected_code,\n", "            },\n", "        )\n", "        prompt, _ = formatter.prepare_prompt(minput)\n", "        tokens = [-x for x in prompt] + prompt_formatter.tokenizer.tokenize(\n", "            ex.updated_code\n", "        )\n", "        tokens_per_example.append(tokens + [prompt_formatter.tokenizer.eod_id])\n", "    return tokens_per_example\n", "\n", "\n", "def augment_code_edit_data(data_list: list[CodeEditData], k: int = 3):\n", "    \"\"\"Augment the code edit data with random instructions.\"\"\"\n", "    new_data_list: list[CodeEditData] = []\n", "    for data in data_list:\n", "        assert data.instructions is not None\n", "        all_instructions = [x.text for x in data.instructions]\n", "        selected_all_instructions = random.sample(\n", "            all_instructions, min(k, len(all_instructions))\n", "        )\n", "        for ins in selected_all_instructions:\n", "            new_data = copy.deepcopy(data)\n", "            new_data.instructions = [Instruction(ins, None)]\n", "            new_data_list.append(new_data)\n", "    return new_data_list\n", "\n", "\n", "# 2048, 4096, 8192, 16384\n", "# prompt tokens -> 8192 - 2048 = 6144, output tokens = 2048\n", "# selection -> (6144 - 512) * 0.4 = 2252.8\n", "prompt_formatter = DroidEditPromptFormatter(\n", "    max_prompt_tokens=6656,  # a mistake.\n", "    input_fraction=1.0,\n", "    selected_code_fraction=0.4,\n", "    max_instruction_tokens=512,\n", ")\n", "\n", "augmented_data = augment_code_edit_data(all_data)\n", "random.shuffle(augmented_data)\n", "augmented_data_tokens = editdata_to_tokens(augmented_data, prompt_formatter)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skip 9 examples due to over-8192-tokens.\n", "Packed 64167 examples into 21324 8192-length-sequences.\n", "On average, the number of paddings is 1441.97.\n", "Saved 21324 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.PR.Vendor54/train-S8192\n"]}], "source": ["import pathlib\n", "import dataclasses\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits.temp_lib import build_dataset\n", "\n", "save_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/Droid.38.39.PR.Vendor54/\"\n", ")\n", "assert save_dir.exists()\n", "\n", "utils_for_file.write_jsonl(\n", "    save_dir / \"edit-data.jsonl\",\n", "    [dataclasses.asdict(x) for x in all_data],\n", ")\n", "\n", "sequence_length = 8192\n", "build_dataset(\n", "    augmented_data_tokens,\n", "    prompt_formatter,\n", "    save_dir / f\"train-S{sequence_length}\",\n", "    sequence_length=sequence_length,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.synthetic_code_edit.util_lib import read_vendor_data_w_annotation\n", "\n", "data = read_vendor_data_w_annotation()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}