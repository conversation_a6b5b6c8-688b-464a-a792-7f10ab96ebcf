{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize the Generated Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Randomly sample the edit scopes.\n", "import random\n", "import dataclasses\n", "from collections import defaultdict\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from experimental.dxy.edits.util_lib import load_edit_eval_tests, load_repo_data\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope, find_edit_data_scope\n", "from experimental.dxy.edits.vis_lib import save_histogram, plot_edit_scope"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xpath = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/002-7175.json\"\n", ")\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xpath = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/005-7175.json\"\n", ")\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xpath = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/006-7175.json\"\n", ")\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xpath = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/007-7175.json\"\n", ")\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xpath = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/007-7175.json\"\n", ")\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xpath = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/008-7175.json\"\n", ")\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}