{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import random\n", "import pathlib\n", "from research.core import utils_for_file\n", "\n", "save_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/\"\n", ")\n", "\n", "# list_of_dicts = utils_for_file.read_jsonl_zst(save_dir / \"all.jsonl.zst\")\n", "# print(f\"There are {len(list_of_dicts)} files.\")\n", "\n", "# random.shuffle(list_of_dicts)\n", "# list_of_100k_dicts = list_of_dicts[:100_000]\n", "# print(f\"Randomly sampled {len(list_of_100k_dicts)} dictionaries.\")\n", "# utils_for_file.write_jsonl_zst(save_dir / \"100k.jsonl.zst\", list_of_100k_dicts)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 100000 files.\n"]}], "source": ["list_of_100k_dicts = utils_for_file.read_jsonl_zst(save_dir / \"100k.jsonl.zst\")\n", "print(f\"There are {len(list_of_100k_dicts)} files.\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 11/100000 [00:00<20:57, 79.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 0 examples so far and led to 3 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  1%|          | 1025/100000 [00:11<15:40, 105.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 1000 examples so far and led to 2470 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  2%|▏         | 2016/100000 [00:21<13:57, 116.97it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 2000 examples so far and led to 4951 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  3%|▎         | 3012/100000 [00:32<13:51, 116.58it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 3000 examples so far and led to 7416 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  4%|▍         | 4010/100000 [00:43<15:01, 106.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 4000 examples so far and led to 9902 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  5%|▌         | 5023/100000 [00:55<14:29, 109.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 5000 examples so far and led to 12408 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  6%|▌         | 6015/100000 [01:05<16:08, 97.09it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 6000 examples so far and led to 14861 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  7%|▋         | 7004/100000 [01:16<15:41, 98.75it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 7000 examples so far and led to 17322 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  8%|▊         | 8011/100000 [01:26<17:12, 89.10it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 8000 examples so far and led to 19839 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  9%|▉         | 9018/100000 [01:38<16:57, 89.40it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 9000 examples so far and led to 22326 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 10%|█         | 10006/100000 [01:49<16:03, 93.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 10000 examples so far and led to 24806 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 11%|█         | 11021/100000 [01:59<14:08, 104.83it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 11000 examples so far and led to 27288 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 12%|█▏        | 12013/100000 [02:10<14:13, 103.10it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 12000 examples so far and led to 29806 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 13%|█▎        | 13015/100000 [02:20<12:14, 118.51it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 13000 examples so far and led to 32296 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 13%|█▎        | 13333/100000 [02:24<14:46, 97.80it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 14%|█▍        | 14015/100000 [02:31<15:45, 90.94it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 14000 examples so far and led to 34757 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 15%|█▌        | 15020/100000 [02:42<12:45, 111.05it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 15000 examples so far and led to 37273 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 16%|█▌        | 16021/100000 [02:53<13:51, 101.01it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 16000 examples so far and led to 39713 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 17%|█▋        | 17017/100000 [03:04<15:43, 87.94it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 17000 examples so far and led to 42228 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 18%|█▊        | 18003/100000 [03:15<14:28, 94.45it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 18000 examples so far and led to 44733 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 19%|█▉        | 19019/100000 [03:26<10:32, 128.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 19000 examples so far and led to 47230 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 20%|██        | 20008/100000 [03:37<17:27, 76.33it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 20000 examples so far and led to 49759 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 21%|██        | 21019/100000 [03:48<14:19, 91.88it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 21000 examples so far and led to 52256 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 22%|██▏       | 22017/100000 [03:59<11:30, 112.87it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 22000 examples so far and led to 54750 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 23%|██▎       | 23029/100000 [04:10<10:16, 124.93it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 23000 examples so far and led to 57262 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 24%|██▍       | 24012/100000 [04:21<14:37, 86.57it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 24000 examples so far and led to 59786 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 25%|██▌       | 25007/100000 [04:32<15:02, 83.09it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 25000 examples so far and led to 62289 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 26%|██▌       | 26019/100000 [04:44<14:26, 85.42it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 26000 examples so far and led to 64827 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 27%|██▋       | 26883/100000 [04:54<13:58, 87.19it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 27%|██▋       | 27016/100000 [04:56<11:20, 107.24it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 27000 examples so far and led to 67297 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 28%|██▊       | 28009/100000 [05:06<13:50, 86.70it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 28000 examples so far and led to 69768 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 29%|██▉       | 29008/100000 [05:17<11:59, 98.72it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 29000 examples so far and led to 72254 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 30%|███       | 30017/100000 [05:28<12:26, 93.77it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 30000 examples so far and led to 74743 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 31%|███       | 31002/100000 [05:39<12:36, 91.22it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 31000 examples so far and led to 77215 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 32009/100000 [05:51<14:42, 77.06it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 32000 examples so far and led to 79742 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 33%|███▎      | 33016/100000 [06:02<11:57, 93.32it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 33000 examples so far and led to 82213 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 34%|███▍      | 34014/100000 [06:13<14:56, 73.58it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 34000 examples so far and led to 84731 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 35%|███▌      | 35024/100000 [06:25<10:32, 102.74it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 35000 examples so far and led to 87230 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 36%|███▌      | 36021/100000 [06:35<11:13, 94.96it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 36000 examples so far and led to 89704 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 37%|███▋      | 37014/100000 [06:45<10:00, 104.93it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 37000 examples so far and led to 92191 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 38%|███▊      | 38010/100000 [06:56<10:36, 97.35it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 38000 examples so far and led to 94696 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 39%|███▉      | 39013/100000 [07:07<10:31, 96.58it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 39000 examples so far and led to 97171 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 40%|████      | 40013/100000 [07:18<10:54, 91.67it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 40000 examples so far and led to 99666 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 41%|████      | 41009/100000 [07:29<09:29, 103.66it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 41000 examples so far and led to 102181 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 41%|████      | 41087/100000 [07:30<10:35, 92.73it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 42%|████▏     | 42009/100000 [07:40<11:27, 84.32it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 42000 examples so far and led to 104657 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 43%|████▎     | 43007/100000 [07:51<11:50, 80.25it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 43000 examples so far and led to 107111 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 44%|████▍     | 44024/100000 [08:02<08:24, 111.06it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 44000 examples so far and led to 109625 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 45%|████▌     | 45016/100000 [08:13<08:30, 107.79it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 45000 examples so far and led to 112116 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 46%|████▌     | 46007/100000 [08:24<11:07, 80.87it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 46000 examples so far and led to 114582 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 47%|████▋     | 47012/100000 [08:34<07:19, 120.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 47000 examples so far and led to 117056 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 48%|████▊     | 48007/100000 [08:45<10:45, 80.56it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 48000 examples so far and led to 119535 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 49%|████▉     | 49015/100000 [08:56<07:42, 110.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 49000 examples so far and led to 122011 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|█████     | 50015/100000 [09:06<08:30, 97.98it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 50000 examples so far and led to 124498 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 51%|█████     | 51012/100000 [09:18<09:33, 85.40it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 51000 examples so far and led to 126998 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 52%|█████▏    | 52011/100000 [09:28<08:57, 89.33it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 52000 examples so far and led to 129494 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 53%|█████▎    | 53023/100000 [09:39<07:12, 108.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 53000 examples so far and led to 132017 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 54%|█████▍    | 54011/100000 [09:50<07:19, 104.65it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 54000 examples so far and led to 134508 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 55%|█████▌    | 55012/100000 [10:01<07:12, 103.91it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 55000 examples so far and led to 136994 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 56%|█████▌    | 56013/100000 [10:12<09:16, 79.11it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 56000 examples so far and led to 139495 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 57%|█████▋    | 57017/100000 [10:24<06:13, 114.98it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 57000 examples so far and led to 141946 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 58%|█████▊    | 58011/100000 [10:35<06:58, 100.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 58000 examples so far and led to 144458 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 58%|█████▊    | 58166/100000 [10:37<07:10, 97.17it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 59%|█████▉    | 59011/100000 [10:46<07:45, 87.99it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 59000 examples so far and led to 146938 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 60%|██████    | 60011/100000 [10:58<09:03, 73.55it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 60000 examples so far and led to 149457 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 61%|██████    | 61021/100000 [11:08<05:56, 109.36it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 61000 examples so far and led to 151949 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 62%|██████▏   | 62013/100000 [11:19<05:43, 110.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 62000 examples so far and led to 154485 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 63%|██████▎   | 63006/100000 [11:30<07:06, 86.81it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 63000 examples so far and led to 156939 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 64%|██████▍   | 64022/100000 [11:41<05:45, 104.12it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 64000 examples so far and led to 159433 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 65%|██████▌   | 65011/100000 [11:53<05:47, 100.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 65000 examples so far and led to 161894 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 65%|██████▌   | 65389/100000 [11:57<07:52, 73.33it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 66%|██████▌   | 66007/100000 [12:05<08:08, 69.57it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 66000 examples so far and led to 164397 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 67%|██████▋   | 67015/100000 [12:16<05:44, 95.64it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 67000 examples so far and led to 166896 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 68%|██████▊   | 68014/100000 [12:27<04:50, 109.93it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 68000 examples so far and led to 169359 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 69%|██████▊   | 68566/100000 [12:32<04:17, 122.09it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 69%|██████▉   | 69017/100000 [12:37<04:51, 106.34it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 69000 examples so far and led to 171819 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 70%|███████   | 70023/100000 [12:48<04:29, 111.31it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 70000 examples so far and led to 174284 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 71%|███████   | 71012/100000 [12:59<05:38, 85.63it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 71000 examples so far and led to 176768 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 71%|███████   | 71230/100000 [13:01<03:55, 121.97it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 72%|███████▏  | 72033/100000 [13:10<03:44, 124.80it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 72000 examples so far and led to 179278 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 73%|███████▎  | 73017/100000 [13:21<04:03, 110.74it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 73000 examples so far and led to 181714 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 74%|███████▍  | 74009/100000 [13:31<03:46, 114.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 74000 examples so far and led to 184202 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 75%|███████▌  | 75018/100000 [13:43<04:56, 84.22it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 75000 examples so far and led to 186703 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 76%|███████▌  | 76006/100000 [13:55<04:34, 87.30it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 76000 examples so far and led to 189220 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 77%|███████▋  | 77015/100000 [14:07<03:38, 105.32it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 77000 examples so far and led to 191724 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 78%|███████▊  | 78014/100000 [14:18<04:02, 90.62it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 78000 examples so far and led to 194241 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 79%|███████▉  | 79010/100000 [14:29<04:12, 83.05it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 79000 examples so far and led to 196726 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|███████▉  | 79516/100000 [14:35<03:22, 101.35it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|████████  | 80007/100000 [14:41<03:42, 89.95it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 80000 examples so far and led to 199221 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 81%|████████  | 81023/100000 [14:52<02:42, 116.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 81000 examples so far and led to 201738 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 82%|████████▏ | 82020/100000 [15:03<03:19, 90.26it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 82000 examples so far and led to 204191 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 83%|████████▎ | 83013/100000 [15:15<03:26, 82.38it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 83000 examples so far and led to 206664 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 84%|████████▍ | 84009/100000 [15:26<03:33, 75.05it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 84000 examples so far and led to 209139 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 85%|████████▌ | 85007/100000 [15:37<03:02, 82.20it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 85000 examples so far and led to 211637 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 86%|████████▌ | 86012/100000 [15:47<02:16, 102.68it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 86000 examples so far and led to 214103 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 87%|████████▋ | 87008/100000 [15:59<02:21, 91.58it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 87000 examples so far and led to 216546 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 87%|████████▋ | 87249/100000 [16:02<02:07, 99.65it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 88%|████████▊ | 88024/100000 [16:09<01:33, 128.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 88000 examples so far and led to 219026 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 89%|████████▉ | 89016/100000 [16:20<02:14, 81.90it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 89000 examples so far and led to 221571 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 90%|█████████ | 90015/100000 [16:32<01:48, 92.18it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 90000 examples so far and led to 224054 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 91%|█████████ | 90976/100000 [16:42<01:24, 106.25it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 91%|█████████ | 91020/100000 [16:42<01:13, 122.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 91000 examples so far and led to 226508 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 92%|█████████▏| 92020/100000 [16:53<01:10, 113.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 92000 examples so far and led to 229032 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 93%|█████████▎| 93011/100000 [17:04<00:54, 127.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 93000 examples so far and led to 231512 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 94%|█████████▍| 94015/100000 [17:15<00:58, 101.47it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 94000 examples so far and led to 234008 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 95%|█████████▌| 95020/100000 [17:27<00:57, 86.97it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 95000 examples so far and led to 236532 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 96%|█████████▌| 96021/100000 [17:39<00:45, 86.70it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 96000 examples so far and led to 238994 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 97%|█████████▋| 97012/100000 [17:50<00:40, 72.98it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 97000 examples so far and led to 241484 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 97%|█████████▋| 97272/100000 [17:53<00:32, 83.47it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to find lranges due to Cannot build scope tree from the root node.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 98%|█████████▊| 98008/100000 [18:01<00:25, 77.76it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 98000 examples so far and led to 243983 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 99%|█████████▉| 99006/100000 [18:12<00:10, 91.35it/s] "]}, {"name": "stdout", "output_type": "stream", "text": ["Processed 99000 examples so far and led to 246469 in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 100000/100000 [18:23<00:00, 90.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 248951 examples in total.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import tqdm\n", "import random\n", "from research.data.synthetic_code_edit.types import CodeEditData\n", "from research.data.synthetic_code_edit.util_lib import (\n", "    generate_codedit_from_raw_github_dict_data,\n", ")\n", "\n", "all_sampled_data: list[CodeEditData] = []\n", "for index, data in tqdm.tqdm(\n", "    enumerate(list_of_100k_dicts), total=len(list_of_100k_dicts)\n", "):\n", "    constraints = []\n", "    if random.random() < 0.05:\n", "        constraints.append((1, 10))\n", "    else:\n", "        constraints.append((5, 15))\n", "    constraints.append((15, 35))\n", "    constraints.append((35, 80))\n", "    results = generate_codedit_from_raw_github_dict_data(data, constraints)\n", "    for x in results:\n", "        if x is not None:\n", "            all_sampled_data.append(x)\n", "    if index % 1000 == 0:\n", "        print(\n", "            f\"Processed {index} examples so far and led to {len(all_sampled_data)} in total.\"\n", "        )\n", "print(f\"There are {len(all_sampled_data)} examples in total.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "\n", "random.shuffle(all_sampled_data)\n", "all_sampled_data_in_dict = [dataclasses.asdict(x) for x in all_sampled_data]\n", "utils_for_file.write_jsonl_zst(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/240k-codeedit-from-100k-files.jsonl.zst\"\n", "    ),\n", "    all_sampled_data_in_dict,\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m            'res_path':res_path\n", "        }))\n", "\n", "        return (None,None)\n", "\n", "    def list_chal(self,off,num,min_accttype = UserService.ACCTTYPE_MEMBER):\n", "\u001b[0m<<<<<<<\n", "\u001b[31m<selected-code-is-not-available>\n", "\u001b[0m=======\n", "\u001b[32m        cur = yield self.db.cursor()\n", "        yield cur.execute(('SELECT '\n", "            '\"challenge\".\"chal_id\",'\n", "            '\"challenge\".\"pro_id\",'\n", "            '\"challenge\".\"acct_id\",'\n", "            '\"challenge\".\"timestamp\",'\n", "            '\"account\".\"name\" AS \"acct_name\",'\n", "            '\"collect_test\".\"state\",'\n", "            '\"collect_test\".\"runtime\",'\n", "            '\"collect_test\".\"memory\" '\n", "            'FROM \"challenge\" '\n", "            'INNER JOIN \"account\" '\n", "            'ON \"challenge\".\"acct_id\" = \"account\".\"acct_id\" '\n", "            'INNER JOIN \"collect_test\" '\n", "            'ON \"challenge\".\"chal_id\" = \"collect_test\".\"chal_id\" '\n", "            'WHERE \"account\".\"acct_type\" >= %s '\n", "            'ORDER BY \"challenge\".\"timestamp\" DESC OFFSET %s LIMIT %s;'),\n", "            (min_accttype,off,num))\n", "        \n", "        challist = list()\n", "        for (chal_id,pro_id,acct_id,timestamp,acct_name,\n", "                state,runtime,memory) in cur:\n", "            challist.append({\n", "                'chal_id':chal_id,\n", "                'pro_id':pro_id,\n", "                'acct_id':acct_id,\n", "                'timestamp':timestamp,\n", "                'acct_name':acct_name,\n", "                'state':state,\n", "                'runtime':runtime,\n", "                'memory':memory\n", "            })\n", "\n", "        return (None,challist)\n", "\u001b[0m>>>>>>>\n", "\u001b[34m\n", "    def get_stat(self,min_accttype = UserService.ACCTTYPE_MEMBER):\n", "        cur = yield self.db.cursor()\n", "        yield cur.execute(('SELECT COUNT(1) FROM \"challenge\" '\n", "            'INNER JOIN \"account\" '\n", "            'ON \"challenge\".\"acct_id\" = \"account\".\"acct_id\" '\n", "\u001b[0m\n"]}], "source": ["from research.data.synthetic_code_edit import util_lib\n", "\n", "print(util_lib.get_pretty_diff_code_for_codedit(all_sampled_data[4], 6))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}