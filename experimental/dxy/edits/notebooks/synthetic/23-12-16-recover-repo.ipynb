{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/12/19 01:37:51 WARN Utils: Your hostname, dxy-8-a40 resolves to a loopback address: *********; using ************** instead (on interface enp5s0)\n", "23/12/19 01:37:51 WARN Utils: Set SPARK_LOCAL_IP if you need to bind to another address\n", "Setting default log level to \"WARN\".\n", "To adjust logging level use sc.setLogLevel(newLevel). For SparkR, use setLogLevel(newLevel).\n"]}], "source": ["import pyspark.sql.functions as F\n", "from pyspark.sql.functions import rand\n", "from research.data.spark import k8s_session\n", "\n", "spark = k8s_session(\n", "    max_workers=100,\n", "    conf={\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"128\",\n", "        \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "        \"spark.task.maxFailures\": \"10\",\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["23/12/19 01:38:53 WARN MetricsConfig: Cannot locate configuration: tried hadoop-metrics2-s3a-file-system.properties,hadoop-metrics2.properties\n", "[Stage 1:====================================================>(1968 + 1) / 1969]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 30575912 files.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")\n", "py_files_df = (\n", "    files_df.select(\n", "        F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "        F.col(\"max_stars_repo_path\").alias(\"file_path\"),\n", "        F.col(\"id\").alias(\"file_sha\"),\n", "        <PERSON><PERSON>col(\"lang\"),\n", "        <PERSON><PERSON>col(\"content\"),\n", "        <PERSON>.col(\"repo_size\"),\n", "    )\n", "    .filter(F.col(\"file_path\").endswith(\".py\"))\n", "    .filter(F.length(\"content\") < 1e6)\n", "    .withColumn(\"num_lines\", F.size(F.split(F.col(\"content\"), \"\\n\")))\n", ")\n", "py_files_df = py_files_df.filter(F.col(\"num_lines\") <= 800)\n", "print(f\"There are {py_files_df.count()} files.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Split the file_path column to extract commit and path\n", "split_col = F.split(py_files_df[\"file_path\"], \":\")\n", "py_files_df = py_files_df.withColumn(\"commit\", split_col.getItem(0))\n", "py_files_df = py_files_df.withColumn(\"path\", split_col.getItem(1))\n", "# Construct the repo_url using the repo and commit columns\n", "py_files_df = py_files_df.withColumn(\n", "    \"repo_url\",\n", "    <PERSON><PERSON>concat(F.lit(\"https://github.com/\"), \"repo\", F.lit(\"/commit/\"), \"commit\"),\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 6:===================================================>  (190 + 10) / 200]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 757734 unique repositories.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["# Count the number of repositories in the repo table.\n", "unique_repo_count = py_files_df.select(\"repo\").distinct().count()\n", "print(f\"There are {unique_repo_count} unique repositories.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Retrieve the repo information given a CodeEditData"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_file, utils_for_str, utils_for_dataclass\n", "from research.data.synthetic_code_edit import types\n", "raw_github_data = [\n", "    utils_for_dataclass.create_from_dict(types.CodeEditData, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst\"\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["https://github.com/dimagi/commcare-hq/commit/6ae2006b2a823c4733054de0fc2de17107f26617\n"]}], "source": ["print(raw_github_data[0].repo_url)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Stage 10:===================================================>(1968 + 1) / 1969]\r"]}, {"name": "stdout", "output_type": "stream", "text": ["https://github.com/dimagi/commcare-hq/commit/6ae2006b2a823c4733054de0fc2de17107f26617 has 133 files.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                                                                                \r"]}], "source": ["repo_url = raw_github_data[0].repo_url\n", "filtered_df = py_files_df.filter(F.col(\"repo_url\") == repo_url)\n", "print(f\"{repo_url} has {filtered_df.count()} files.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}