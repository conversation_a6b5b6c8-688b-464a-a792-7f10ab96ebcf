{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import pathlib\n", "from research.core import utils_for_file\n", "\n", "json_file = pathlib.Path(\"/mnt/efs/augment/user/igor/nlp/2023-12-20_19-26-37/tokenized.json\")\n", "data_list = utils_for_file.read_json(json_file)\n", "print(f\"Loaded {len(data_list)} examples.\")\n", "# for idx in range(5):\n", "#     print(type(data_list[idx]))\n", "#     print(len(data_list[idx]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inspect IndexedDataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from research.core.all_prompt_formatters import get_prompt_formatter\n", "\n", "valid_dataset = MMapIndexedDataset(\n", "   \"/mnt/efs/augment/user/dxy/datasets/edit.ft/InstructCoder/valid-S16384-DSCI\"\n", ")\n", "print(f\"The valid dataset has {len(valid_dataset)} records.\")\n", "\n", "prompter = get_prompt_formatter(\"deepseek_coder_instruct\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["example = valid_dataset[0].tolist()  # type: ignore\n", "example = [abs(x) for x in example]\n", "print(f\"The example has {len(example)} tokens.\")\n", "text = prompter.tokenizer.detokenize(example)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Inspect New Tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_file\n", "\n", "raw_token_data = utils_for_file.read_json(\"/mnt/efs/augment/user/igor/nlp/2023-12-21_02-03-25/tokenized.json\")\n", "print(f\"Loaded {len(raw_token_data)} examples.\")\n", "print(f\"Type of the first example: {type(raw_token_data[0])}\")\n", "if isinstance(raw_token_data[0], dict):\n", "    print(f\"Keys of the first example: {raw_token_data[0].keys()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.all_prompt_formatters import get_prompt_formatter\n", "\n", "tokens = raw_token_data[0]['tokens']\n", "prompter = get_prompt_formatter(\"deepseek_coder_instruct\")\n", "text = prompter.tokenizer.detokenize(tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}