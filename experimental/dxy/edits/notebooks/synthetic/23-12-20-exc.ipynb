{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3 categories: ['Formatting-Cleaning', 'Refactoring', 'Bug-Fixing']\n", "Loaded 248951 examples.\n"]}], "source": ["import dataclasses\n", "from research.core import utils_for_file, utils_for_str, utils_for_dataclass\n", "from research.data.synthetic_code_edit import types\n", "\n", "raw_github_data = [\n", "    utils_for_dataclass.create_from_dict(types.CodeEditData, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/240k-codeedit-from-100k-files.jsonl.zst\"\n", "    )\n", "]\n", "print(f\"Loaded {len(raw_github_data)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.data.synthetic_code_edit import seed_lib\n", "from research.data.synthetic_code_edit import util_lib\n", "\n", "seeds = seed_lib.load_code_edit_seeds()\n", "seeds = {k: v for k, v in seeds.items() if len(v) > 0}\n", "print(f\"Loaded {len(seeds)} categories: {list(seeds.keys())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["seeds_python = [\n", "    x for v in seeds.values() for x in v if x.metadata[\"language\"] == \"python\"\n", "]\n", "categories = set([x.metadata[\"category\"] for x in seeds_python])\n", "print(f\"Loaded {len(seeds_python)} seeds with categories of {list(categories)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["full_inverse_instructions: list[str] = []\n", "for x in seeds_python:\n", "    if x.inverse_instructions is None:\n", "        continue\n", "    # for y in x.inverse_instructions:\n", "    #     inverse_instructions.append(y.text)\n", "    full_inverse_instructions.append(x.inverse_instructions[0].text)\n", "# print(\"\\n\".join(full_inverse_instructions))\n", "\n", "# Put general instructions\n", "small_inverse_instructions = [\n", "    \"introduce a major bug\",\n", "    \"pollute its syntax by replacing key-value pair as key = value\",\n", "    \"Mess up the spaces after commas , and around plus signs + in the code\",\n", "    \"replace function call with inline condition\",\n", "    \"remove the type annotations for the arguments\",\n", "    \"Return the wrong result from get_readme_content\",\n", "    \"integrate the A function into B function or C function\",\n", "    \"use percent formatting for string\",\n", "    \"inline check_data func and delete\",\n", "    \"rename variable A as variable B\",\n", "    \"introduce a subtle bug in the for loop\",\n", "    \"change all variable names to non-descriptive single letters\",\n", "    \"refactor the main function into smaller functions\",\n", "    \"introduce useless imports\",\n", "    \"Replace a logical chunk of code with commented-out pseudocode\",\n", "    \"reorder the imports randomly\",\n", "    \"Replace chunks of code with TODOs\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from experimental.dxy.edits.pipelines.simple_selected_code_v2 import (\n", "    generate_inverse_instruction_plus_selected_code,\n", ")\n", "from experimental.dxy.edits.pipelines.simple_instruction import generate_instruction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_dir_json_r1 = pathlib.Path(\"/mnt/efs/augment/user/dxy/visualization/json-data-r1\")\n", "save_dir_json_r1.mkdir(exist_ok=True, parents=True)\n", "save_dir_json_r2 = pathlib.Path(\"/mnt/efs/augment/user/dxy/visualization/json-data-r2\")\n", "save_dir_json_r2.mkdir(exist_ok=True, parents=True)\n", "save_dir_html = pathlib.Path(\"/mnt/efs/augment/user/dxy/visualization/8000/htmls\")\n", "save_dir_html.mkdir(exist_ok=True, parents=True)\n", "\n", "passed, failed = 0, 0\n", "for index in range(30):\n", "    data_v0 = raw_github_data[index]\n", "    try:\n", "        data_v1 = generate_inverse_instruction_plus_selected_code(\n", "            data_v0,\n", "            small_inverse_instructions,\n", "            num_examples=4,\n", "            num_context_lines=12,\n", "            model=\"gpt-4-0613\",\n", "        )\n", "        save_path_json = (\n", "            save_dir_json_r1 / f\"{index:03d}-{len(raw_github_data):03d}.json\"\n", "        )\n", "        utils_for_file.write_json(save_path_json, dataclasses.asdict(data_v1), indent=2)\n", "        print(f\"Save into {save_path_json}\")\n", "        data_v2 = generate_instruction(\n", "            data_v1, num_context_lines=12, model=\"gpt-4-0613\"\n", "        )\n", "        save_path_json = (\n", "            save_dir_json_r2 / f\"{index:03d}-{len(raw_github_data):03d}.json\"\n", "        )\n", "        utils_for_file.write_json(save_path_json, dataclasses.asdict(data_v2), indent=2)\n", "        # Save into the html file.\n", "        save_path_html = save_dir_html / f\"{index:03d}-{len(raw_github_data):03d}.html\"\n", "        save_path_html.write_text(util_lib.get_html_str(data_v2))\n", "        print(f\"Save into {save_path_html}\")\n", "        passed += 1\n", "    except Exception as e:\n", "        print(e)\n", "        print(f\"Failed to generate the {index}-th sample\")\n", "        failed += 1\n", "    print(\n", "        f\"index={index}, passed={passed}/{passed+failed}, failed={failed}/{passed+failed}\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data_v2 = generate_instruction(data_v1, num_context_lines=12, model=\"gpt-4-0613\")\n", "save_path_html = save_dir_html / f\"{index:03d}-{len(raw_github_data):03d}.html\"\n", "save_path_html.write_text(util_lib.get_html_str(data_v2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["save_path_html.write_text(util_lib.get_html_str(data_v1))\n", "print(save_path_html)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_str\n", "\n", "print(\n", "    utils_for_str.extract_the_last_markdown_block(\n", "        data_v1.metadata[\"stage_1@response_v2\"]\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(data_v1.metadata[\"stage_1@context_length\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file, utils_for_dataclass\n", "\n", "data_path = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/visualization/json-data-r0/001-2642.json\"\n", ")\n", "data_dict = utils_for_file.read_json(data_path)\n", "data_to_debug = utils_for_dataclass.create_from_dict(types.CodeEditData, data_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(util_lib.get_pretty_diff_code_for_codedit(data_v2, max_context_lines=16))"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.data.synthetic_code_edit import api_lib\n", "\n", "GptAPI = api_lib.GptWrapper()\n", "\n", "results = GptAPI(\n", "    messages=[{\"role\": \"user\", \"content\": \"write me a Python code\"}],\n", "    model=\"gpt-4-1106-preview\",\n", ")\n", "print(results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}