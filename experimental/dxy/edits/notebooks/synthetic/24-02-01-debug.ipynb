{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    warming up index mmap file...\n", "    reading sizes...\n", "    reading pointers...\n", "    reading document index...\n", "    warming up data mmap file...\n", "    creating numpy buffer of mmap...\n", "    creating memory view of numpy buffer...\n", "The entire training dataset has 6726 records.\n"]}], "source": ["import numpy as np\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from base.tokenizers.deepseek_tokenizer import DeepSeekCoderInstructTokenizer\n", "\n", "dataset = MMapIndexedDataset(\n", "   \"/mnt/efs/augment/user/igor/data/droid/droid-repo-48/train\"\n", ")\n", "print(f\"The entire training dataset has {len(dataset)} records.\")\n", "\n", "tokenizer = DeepSeekCoderInstructTokenizer()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16385\n"]}], "source": ["tokens = dataset[0]\n", "tokens = np.abs(tokens).tolist()\n", "print(len(tokens))\n", "prompt = tokenizer.detok<PERSON>ze(tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(prompt)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}