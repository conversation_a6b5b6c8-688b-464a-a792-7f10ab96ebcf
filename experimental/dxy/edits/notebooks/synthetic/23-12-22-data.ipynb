{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Find 5000 files in total.\n", "Average num: 1194.6741573033707, maximum num per key: 5973\n", "key: 0, len: 1400 / 1400\n", "key: 1, len: 78871 / 5973\n", "key: 2, len: 7483 / 5973\n", "key: 3, len: 4756 / 4756\n", "key: 4, len: 3232 / 3232\n", "key: 5, len: 2329 / 2329\n", "key: 6, len: 1706 / 1706\n", "key: 7, len: 1256 / 1256\n", "key: 8, len: 894 / 894\n", "key: 9, len: 766 / 766\n", "key: 10, len: 590 / 590\n", "key: 11, len: 492 / 492\n", "key: 12, len: 355 / 355\n", "key: 13, len: 305 / 305\n", "key: 14, len: 285 / 285\n", "key: 15, len: 232 / 232\n", "key: 16, len: 158 / 158\n", "key: 17, len: 137 / 137\n", "key: 18, len: 123 / 123\n", "key: 19, len: 86 / 86\n", "key: 20, len: 89 / 89\n", "key: 21, len: 83 / 83\n", "key: 22, len: 70 / 70\n", "key: 23, len: 63 / 63\n", "key: 24, len: 52 / 52\n", "key: 25, len: 45 / 45\n", "key: 26, len: 29 / 29\n", "key: 27, len: 36 / 36\n", "key: 28, len: 35 / 35\n", "key: 29, len: 29 / 29\n", "key: 30, len: 24 / 24\n", "key: 31, len: 21 / 21\n", "key: 32, len: 9 / 9\n", "key: 33, len: 21 / 21\n", "key: 34, len: 21 / 21\n", "key: 35, len: 12 / 12\n", "key: 36, len: 23 / 23\n", "key: 37, len: 12 / 12\n", "key: 38, len: 11 / 11\n", "key: 39, len: 6 / 6\n", "key: 40, len: 17 / 17\n", "key: 41, len: 12 / 12\n", "key: 42, len: 7 / 7\n", "key: 43, len: 6 / 6\n", "key: 44, len: 4 / 4\n", "key: 45, len: 9 / 9\n", "key: 46, len: 7 / 7\n", "key: 47, len: 5 / 5\n", "key: 48, len: 6 / 6\n", "key: 49, len: 7 / 7\n", "key: 50, len: 5 / 5\n", "key: 51, len: 9 / 9\n", "key: 52, len: 5 / 5\n", "key: 53, len: 4 / 4\n", "key: 54, len: 8 / 8\n", "key: 55, len: 2 / 2\n", "key: 56, len: 2 / 2\n", "key: 57, len: 4 / 4\n", "key: 58, len: 5 / 5\n", "key: 59, len: 4 / 4\n", "key: 60, len: 3 / 3\n", "key: 61, len: 2 / 2\n", "key: 62, len: 3 / 3\n", "key: 63, len: 2 / 2\n", "key: 64, len: 1 / 1\n", "key: 65, len: 1 / 1\n", "key: 67, len: 5 / 5\n", "key: 70, len: 1 / 1\n", "key: 71, len: 3 / 3\n", "key: 73, len: 8 / 8\n", "key: 74, len: 1 / 1\n", "key: 82, len: 1 / 1\n", "key: 84, len: 1 / 1\n", "key: 85, len: 2 / 2\n", "key: 89, len: 2 / 2\n", "key: 92, len: 1 / 1\n", "key: 93, len: 1 / 1\n", "key: 96, len: 1 / 1\n", "key: 100, len: 1 / 1\n", "key: 101, len: 1 / 1\n", "key: 103, len: 1 / 1\n", "key: 105, len: 1 / 1\n", "key: 106, len: 1 / 1\n", "key: 113, len: 1 / 1\n", "key: 114, len: 1 / 1\n", "key: 117, len: 1 / 1\n", "key: 123, len: 1 / 1\n", "key: 125, len: 2 / 2\n", "key: 128, len: 1 / 1\n", "Loaded 2915 examples for data_igor.\n", "Loaded 22776 examples for data_igor_aug.\n", "Loaded 106341 examples for data_yury.\n", "Loaded 31918 examples for data_yury_filter.\n", "Loaded 108391 examples for data_instructcoder_train.\n", "Loaded 5708 examples for data_instructcoder_valid.\n"]}], "source": ["from experimental.dxy.edits.temp_lib import (\n", "    load_igor_data,\n", "    load_instruct_coder,\n", "    load_yury_data,\n", "    filter_code_edit_data,\n", "    augment_code_edit_data,\n", "    editdata_to_tokens,\n", ")\n", "\n", "data_igor = load_igor_data()\n", "data_igor_aug = augment_code_edit_data(data_igor, strict_sample_ratio=0.3)\n", "data_instructcoder_train = load_instruct_coder(\"train\")\n", "data_instructcoder_valid = load_instruct_coder(\"valid\")\n", "data_yury = load_yury_data()\n", "data_yury_filter = filter_code_edit_data(data_yury)\n", "\n", "print(f\"Loaded {len(data_igor)} examples for data_igor.\")\n", "print(f\"Loaded {len(data_igor_aug)} examples for data_igor_aug.\")\n", "print(f\"Loaded {len(data_yury)} examples for data_yury.\")\n", "print(f\"Loaded {len(data_yury_filter)} examples for data_yury_filter.\")\n", "print(f\"Loaded {len(data_instructcoder_train)} examples for data_instructcoder_train.\")\n", "print(f\"Loaded {len(data_instructcoder_valid)} examples for data_instructcoder_valid.\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Prepare Prompt: 100%|██████████| 22776/22776 [01:50<00:00, 206.00it/s]\n", "Prepare Prompt: 100%|██████████| 22776/22776 [01:49<00:00, 207.94it/s]\n", "Prepare Prompt:  45%|████▍     | 14267/31918 [01:49<02:20, 125.33it/s]Token indices sequence length is longer than the specified maximum sequence length for this model (32927 > 16384). Running this sequence through the model will result in indexing errors\n", "Prepare Prompt: 100%|██████████| 31918/31918 [04:10<00:00, 127.51it/s]\n", "Prepare Prompt: 100%|██████████| 108391/108391 [06:01<00:00, 299.79it/s]\n", "Prepare Prompt: 100%|██████████| 5708/5708 [00:22<00:00, 256.54it/s]\n"]}], "source": ["from experimental.dxy.edits.prompt_formatter import DSInstructNLPFormatter\n", "\n", "prompt_formatter = DSInstructNLPFormatter()\n", "\n", "tokens_from_igor = editdata_to_tokens(data_igor, prompt_formatter)\n", "tokens_from_aug_igor = editdata_to_tokens(data_igor_aug, prompt_formatter)\n", "tokens_from_yury_filtered = editdata_to_tokens(data_yury_filter, prompt_formatter)\n", "tokens_from_isc_train = editdata_to_tokens(data_instructcoder_train, prompt_formatter)\n", "tokens_from_isc_valid = editdata_to_tokens(data_instructcoder_valid, prompt_formatter)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Skip 41 examples due to over-4096-tokens.\n", "Packed 22776 examples into 5463 4096-length-sequences.\n", "On average, the number of paddings is 541.44.\n", "Saved 5463 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/BS-IGOR-S4096\n", "Skip 41 examples due to over-4096-tokens.\n", "Packed 22776 examples into 5457 4096-length-sequences.\n", "On average, the number of paddings is 537.53.\n", "Saved 5457 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/AUG-IGOR-S4096\n", "Skip 19 examples due to over-4096-tokens.\n", "Packed 31918 examples into 12528 4096-length-sequences.\n", "On average, the number of paddings is 746.77.\n", "Saved 12528 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/PRDATA-S4096\n", "Skip 0 examples due to over-4096-tokens.\n", "Packed 108391 examples into 13015 4096-length-sequences.\n", "On average, the number of paddings is 263.90.\n", "Saved 13015 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/INSC-TRAIN-S4096\n", "Skip 41 examples due to over-4096-tokens.\n", "Packed 131167 examples into 18439 4096-length-sequences.\n", "On average, the number of paddings is 338.02.\n", "Saved 18439 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/COM-AUG_ISC-S4096\n", "Skip 60 examples due to over-4096-tokens.\n", "Packed 54694 examples into 17925 4096-length-sequences.\n", "On average, the number of paddings is 671.86.\n", "Saved 17925 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/COM-AUG_PRDATA-S4096\n", "Skip 0 examples due to over-4096-tokens.\n", "Packed 5708 examples into 681 4096-length-sequences.\n", "On average, the number of paddings is 257.78.\n", "Saved 681 sequences to /mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/INSC-VALID-S4096\n", "\n", "\n", "\n"]}], "source": ["import pathlib\n", "from experimental.dxy.edits.temp_lib import build_dataset\n", "\n", "save_dir = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08\"\n", ")\n", "assert save_dir.exists()\n", "\n", "for seq_len in (4096,):\n", "    build_dataset(\n", "        tokens_from_<PERSON><PERSON>, prompt_formatter, save_dir / f\"BS-IGOR-S{seq_len}\", seq_len\n", "    )\n", "    build_dataset(\n", "        tokens_from_aug_igor,\n", "        prompt_formatter,\n", "        save_dir / f\"AUG-IGOR-S{seq_len}\",\n", "        seq_len,\n", "    )\n", "    build_dataset(\n", "        tokens_from_yury_filtered,\n", "        prompt_formatter,\n", "        save_dir / f\"PRDATA-S{seq_len}\",\n", "        seq_len,\n", "    )\n", "    build_dataset(\n", "        tokens_from_isc_train,\n", "        prompt_formatter,\n", "        save_dir / f\"INSC-TRAIN-S{seq_len}\",\n", "        seq_len,\n", "    )\n", "    build_dataset(\n", "        tokens_from_aug_igor + tokens_from_isc_train,\n", "        prompt_formatter,\n", "        save_dir / f\"COM-AUG_ISC-S{seq_len}\",\n", "        seq_len,\n", "    )\n", "    build_dataset(\n", "        tokens_from_aug_igor + tokens_from_yury_filtered,\n", "        prompt_formatter,\n", "        save_dir / f\"COM-AUG_PRDATA-S{seq_len}\",\n", "        seq_len,\n", "    )\n", "    build_dataset(\n", "        tokens_from_isc_valid,\n", "        prompt_formatter,\n", "        save_dir / f\"INSC-VALID-S{seq_len}\",\n", "        seq_len,\n", "    )\n", "    print(\"\\n\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Training\n", "\n", "```\n", "./scripts/multinode_run.sh dxy-edit-train 4 \"pkill torchrun\"\n", "```\n", "\n", "```\n", "./scripts/multinode_torchrun.sh dxy-edit-train 4 /mnt/efs/augment/user/dxy/src/augment \"train.py configs/deepseek_base_33b.py --learning_rate=1e-5 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 --batch_size=2 --gradient_accumulation_steps=1 --eval_interval=10 --eval_iters=128 --block_size=4096 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/ --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.AUG-V1.S4K/instruct-train-S4096-DSCI --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.AUG-V1.S4K/instruct-valid-S4096-DSCI --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.04/ --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 --wandb_run_name=DSC-I-D0Vaug-ALL-S4K_WUP0_E1-WD1_0-b4x8x2x1-lr_1e-5_constant\"\n", "```\n", "\n", "\n", "```\n", "./scripts/multinode_torchrun.sh dxy-edit-train 4 /mnt/efs/augment/user/dxy/src/augment \"train.py configs/deepseek_base_33b.py --learning_rate=2e-5 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 --batch_size=4 --gradient_accumulation_steps=1 --eval_interval=10 --eval_iters=128 --block_size=4096 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/ --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.07/COM-AUG_ISC-S4096 --eval_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.07/INSC-VALID-S4096 --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.07/ --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 --wandb_run_name=MergeV0-S4K_WUP0_E1-WD1_0-b4x8x4x1-lr_2e-5_constant\"\n", "```\n", "\n", "\n", "```\n", "./scripts/multinode_torchrun.sh dxy-edit-train 4 /mnt/efs/augment/user/dxy/src/augment \"train.py configs/deepseek_base_33b.py --learning_rate=2e-5 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 --batch_size=4 --gradient_accumulation_steps=1 --eval_interval=20 --eval_iters=32 --block_size=4096 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/ --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/COM-AUG_PRDATA-S4096 --eval_data_path='/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/BS-IGOR-S4096;/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/COM-AUG_PRDATA-S4096;/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/INSC-VALID-S4096' --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.08/ --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 --wandb_run_name=MergeV1-S4K_WUP0_E1-WD1_0-b4x8x4x1-lr_2e-5_constant\"\n", "```\n", "\n", "```\n", "./scripts/multinode_torchrun.sh dxy-edit-train 4 /mnt/efs/augment/user/dxy/src/augment \"train.py configs/deepseek_base_33b.py --learning_rate=2e-5 --max_iters=0 --max_epochs=1 --decay_lr=False --warmup_iters=0 --weight_decay=1.0 --batch_size=4 --gradient_accumulation_steps=1 --eval_interval=20 --eval_iters=32 --block_size=4096 --hf_checkpoint_dir=/mnt/efs/augment/checkpoints/deepseek/deepseek-coder-33b-instruct/ --train_data_path=/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/PRDATA-S4096 --eval_data_path='/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/BS-IGOR-S4096;/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/COM-AUG_PRDATA-S4096;/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/INSC-VALID-S4096;/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.24.01.08/PRDATA-S4096' --out_dir=/mnt/efs/augment/user/dxy/logs/edit.01.08/ --wandb_log=True --wandb_project=edit-exps-v2 --wandb_group=DXY-V3 --wandb_run_name=PRDATA-S4K_WUP0_E1-WD1_0-b4x8x4x1-lr_2e-5_constant\"\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from research.core.all_prompt_formatters import get_prompt_formatter\n", "\n", "valid_dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0.S4K/instruct-valid-S4096-DSCI\"\n", ")\n", "print(f\"The valid dataset has {len(valid_dataset)} records.\")\n", "\n", "prompter = get_prompt_formatter(\"deepseek_coder_instruct\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}