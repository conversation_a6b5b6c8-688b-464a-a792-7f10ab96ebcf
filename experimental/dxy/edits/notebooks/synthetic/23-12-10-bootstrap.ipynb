{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Generate Instruction for Code Edit Category"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import re\n", "import copy\n", "import random\n", "from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat\n", "\n", "SYSTEM_PROMPT = r\"\"\"Context: In Python, the function 'def edit(prefix: str, selected_code: str, suffix: str, instruction: str) -> str' is used to modify 'selected_code' into 'updated_code' based on the provided 'instruction'.\n", "\n", "Task: Generate a list of potential edit instructions given a specific edit instruction category.\n", "\n", "Requirements:\n", "- Initially, you will receive example instructions from the user and the instruction category.\n", "- Based on the given information, generate new instructions.\n", "- Try to come up with creative, realistic, and reasonable instructions.\n", "- Put each instructions in a separate line without any introductory text.\n", "- Every time, only produce 5 instructions and try to avoid repeating instructions.\n", "\"\"\"\n", "\n", "\n", "def generate_instructions_one_round(category: str, seeds: list[str], shot: int, target_num: int):\n", "    results = copy.deepcopy(seeds)\n", "    round = 0\n", "    while len(results) < target_num:\n", "        samples = random.sample(seeds, min(shot, len(seeds)))\n", "        messages = [f\"Category: {category}\\nInstructions:\\n\" + \"\\n\".join(samples)]\n", "        [response] = generate_response_via_chat(\n", "            messages=messages,\n", "            system_prompt=SYSTEM_PROMPT,\n", "            temperature=1.0,\n", "            max_tokens=128,\n", "            model=\"gpt-4-0613\",\n", "            num_completion=1,\n", "        )\n", "        answers = [x.strip() for x in response.splitlines(False) if x.strip()]\n", "\n", "        def is_good_answer(x):\n", "            if not x:\n", "                return False\n", "            if x.lower() in seeds or x.lower() in results:\n", "                return False\n", "            if \"updated_code\" in x.lower() or \"selected_code\" in x.lower():\n", "                return False\n", "            return True\n", "\n", "        answers = [x for x in answers if is_good_answer(x)]\n", "        results.extend(answers)\n", "        print(f\"After the {round:2d}-th iteration, we have {len(results)} instructions.\")\n", "        round += 1\n", "    return results"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["After the  0-th iteration, we have 11 instructions.\n", "After the  0-th iteration, we have 16 instructions.\n", "After the  1-th iteration, we have 21 instructions.\n", "After the  0-th iteration, we have 26 instructions.\n", "After the  1-th iteration, we have 31 instructions.\n"]}], "source": ["seeds = [\n", "    \"Randomly adjust the spaces after commas , and around plus signs + in the code\",\n", "    \"Make variables camel case\",\n", "    \"use , instead of ; for them\",\n", "    \"Capitalize all constants defined in the code\",\n", "    \"Remove any trailing whitespace at the end of each line\",\n", "    \"Use .format() method instead of using '+' for string concatenation\",\n", "]\n", "category = \"Formatting-Cleaning\"\n", "instructions_r1 = generate_instructions_one_round(category, seeds=seeds, shot=5, target_num=10)\n", "instructions_r2 = generate_instructions_one_round(category, seeds=instructions_r1, shot=5, target_num=20)\n", "instructions_r3 = generate_instructions_one_round(category, seeds=instructions_r2, shot=5, target_num=30)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Randomly adjust the spaces after commas , and around plus signs + in the code\n", "Make variables camel case\n", "use , instead of ; for them\n", "Capitalize all constants defined in the code\n", "Remove any trailing whitespace at the end of each line\n", "Use .format() method instead of using '+' for string concatenation\n", "Replace all tabs with four spaces for code alignment\n", "Convert all comments into multi-line comments for clarity\n", "Remove unused variable declarations in your code\n", "Make functions snake_case to follow Python conventions\n", "Replace all double quotes with single quotes in string literals\n", "Remove trailing whitespaces from each line for neatness\n", "Standardize indents to four spaces for uniformity\n", "Convert all tabs into spaces to avoid tab-space confusion\n", "Capitalize the first letter of class names to follow Python conventions\n", "Replace all inline comments with documentation string for better visibility\n", "Convert all code comments to Docstrings for better readability and understanding.\n", "Replace implicit string concatenation by explicit concatenation using '+'.\n", "Structurally align your equals '=' signs for variable assignments for clearer code.\n", "Remove all trailing spaces in the code.\n", "Ensure there is a space before and after operators like +,-, *, and /.\n", "Remove all trailing white spaces in the code\n", "Replace tab indentation with four spaces\n", "Correct code by applying PEP8 guidelines for better readability\n", "Convert multiline statements into single line using proper delimiter\n", "Remove unnecessary parenthesis in the expressions\n", "Convert all global constants to UPPER CASE to follow Python conventions\n", "Remove trailing whitespaces from all the lines of code\n", "Replace '==' operators with 'is' where identity checks are needed\n", "Switch the use of single quotes for strings to double quotes for consistency\n", "Ensure the use of a single space around operator to improve readability\n"]}], "source": ["print(\"\\n\".join(instructions_r3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import re\n", "import copy\n", "import random\n", "from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat\n", "\n", "DEDUP_SYSTEM_PROMPT = r\"\"\"Context: In Python, the function 'def edit(prefix: str, selected_code: str, suffix: str, instruction: str) -> str' is used to modify 'selected_code' into 'updated_code' based on the provided 'instruction'.\n", "\n", "Task: The user has provided a list of typical code editing categories used by developers.\n", "However, there are some redundant categories, similar categories, and some categories are not realistic.\n", "Your task is to generate new categories that are more realistic, more practical, more reasonable, less ambiguity based on the user provided categories.\n", "\n", "Requirements:\n", "- Initially, you will receive categories from the user and the required number of categories.\n", "- Your results should only contain the new categories, where each category sits on a separate line without any other text.\n", "- Try to keep each edit category at the same granularity and only use two words for each category.\n", "\"\"\"\n", "\n", "message = (\n", "    \"Categories:\\n\"\n", "    + \"\\n- \".join(categories_4)\n", "    + \"\\n\\n\"\n", "    + \"Required number of categories: 20\"\n", ")\n", "[response] = generate_response_via_chat(\n", "    messages=[message],\n", "    system_prompt=DEDUP_SYSTEM_PROMPT,\n", "    temperature=0.3,\n", "    max_tokens=256,\n", "    model=\"gpt-4-0613\",\n", "    num_completion=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["[response_1] = generate_response_via_chat(\n", "    messages=[\n", "        message,\n", "        response,\n", "        \"I hope to also include a category for translation (for example translate bash to python), help me improve the categories.\",\n", "    ],\n", "    system_prompt=DEDUP_SYSTEM_PROMPT,\n", "    temperature=0.3,\n", "    max_tokens=256,\n", "    model=\"gpt-4-0613\",\n", "    num_completion=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(response)\n", "print(response_1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file\n", "\n", "save_dir = pathlib.Path(\n", "    \"/home/<USER>/src/augment/experimental/dxy/edits/seeds/categories.json\"\n", ")\n", "utils_for_file.write_json(\n", "    save_dir, [x.strip() for x in response_1.splitlines()], indent=2\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}