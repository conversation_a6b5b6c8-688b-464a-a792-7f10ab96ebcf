{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "import dataclasses\n", "from research.core import utils_for_file, utils_for_str, utils_for_dataclass\n", "from research.data.synthetic_code_edit import types\n", "from research.data.synthetic_code_edit import seed_lib\n", "from research.data.synthetic_code_edit import sampling_lib\n", "from research.data.synthetic_code_edit import util_lib\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "seeds = seed_lib.load_code_edit_seeds()\n", "seeds = {k: v for k, v in seeds.items() if len(v) > 0}\n", "print(f\"Loaded {len(seeds)} categories: {list(seeds.keys())}\")\n", "\n", "raw_github_data = [\n", "    utils_for_dataclass.create_from_dict(types.CodeEditData, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst\"\n", "    )\n", "]\n", "print(f\"Loaded {len(raw_github_data)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from research.core import utils_for_file, utils_for_dataclass\n", "\n", "data_path = pathlib.Path(\n", "    \"/mnt/efs/augment/user/dxy/visualization/json-data-r0/001-2642.json\"\n", ")\n", "data_dict = utils_for_file.read_json(data_path)\n", "data_to_debug = utils_for_dataclass.create_from_dict(types.CodeEditData, data_dict)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["num_context_lines = 12\n", "data = raw_github_data[1]\n", "lindex_to_minimal_scope = sampling_lib.find_minimal_self_contained_range_per_line(\n", "    data.prefix + data.updated_code + data.suffix\n", ")\n", "prefix_lines = data.prefix.splitlines(keepends=True)\n", "suffix_lines = data.suffix.splitlines(keepends=True)\n", "num_in_middle = data.updated_code.splitlines(keepends=True)\n", "max_num_prefix_lines = min(len(prefix_lines), 10)\n", "max_num_suffix_lines = min(len(suffix_lines), num_context_lines)\n", "end_index = len(prefix_lines) + len(num_in_middle) + max_num_suffix_lines - 1\n", "max_num_suffix_lines = max_num_suffix_lines + lindex_to_minimal_scope[end_index][1] - end_index\n", "start_index = len(prefix_lines) - max_num_prefix_lines\n", "print(f\"start_index: {start_index}\")\n", "max_num_prefix_lines = (\n", "        max_num_prefix_lines + start_index - lindex_to_minimal_scope[start_index][0]\n", "    )\n", "print(f\"max_num_prefix_lines: {max_num_prefix_lines}\")\n", "print(f\"max_num_suffix_lines: {max_num_suffix_lines}\")\n", "# test = lindex_to_minimal_scope[end_index]\n", "# print(end_index)\n", "# print(test)\n", "result_str = util_lib.create_diff_with_leading_brackets(\n", "        prefix=utils_for_str.get_last_n_lines(data.prefix, max_num_prefix_lines),\n", "        suffix=utils_for_str.get_first_n_lines(data.suffix, max_num_suffix_lines),\n", "        selected_code=\"\",\n", "        updated_code=data.updated_code,\n", "        marker_for_select=\"-\",\n", "        marker_for_update=\"x\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(result_str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lindex_to_minimal_scope[0]\n", "lindex_to_minimal_scope[1]\n", "print(lindex_to_minimal_scope[2])\n", "\n", "# print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(util_lib.get_pretty_diff_code_for_codedit(data_to_debug, 4))\n", "ori_x = raw_github_data[1]\n", "selected_code_with_marker = utils_for_str.extract_the_last_markdown_block(data_to_debug.metadata[\"stage_1@dialogue\"][-1])\n", "context_length = data_to_debug.metadata[\"stage_1@context_length\"]\n", "# print(x)\n", "# print(ori_x.updated_code)\n", "# print(x)\n", "print(selected_code_with_marker)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.dxy.edits.pipelines.simple_selected_code_v2 import (\n", "    post_process_selected_code\n", ")\n", "\n", "post_process_selected_code(ori_x, context_length, selected_code_with_marker)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["XXX"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "from research.core import utils_for_file\n", "\n", "raw_token_data = utils_for_file.read_json(\n", "    \"/mnt/efs/augment/user/igor/nlp/2023-12-21_02-03-25/tokenized.json\"\n", ")\n", "print(f\"Loaded {len(raw_token_data)} examples.\")\n", "print(f\"Type of the first example: {type(raw_token_data[0])}\")\n", "if isinstance(raw_token_data[0], dict):\n", "    print(f\"Keys of the first example: {raw_token_data[0].keys()}\")\n", "examples = [x[\"tokens\"] for x in raw_token_data]\n", "random.shuffle(examples)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDataset\n", "from research.core.all_prompt_formatters import get_prompt_formatter\n", "\n", "valid_dataset = MMapIndexedDataset(\n", "   \"/mnt/efs/augment/user/dxy/datasets/edit.ft/SyntheticCodeEdit.V0/full-S4096-DSCI\"\n", ")\n", "print(f\"The valid dataset has {len(valid_dataset)} records.\")\n", "\n", "prompter = get_prompt_formatter(\"deepseek_coder_base\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(valid_dataset[0].min())\n", "print(valid_dataset[0].max())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tokens = [abs(x) for x in valid_dataset[0].tolist()]\n", "text = prompter.tokenizer.detokenize(tokens)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(text)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}