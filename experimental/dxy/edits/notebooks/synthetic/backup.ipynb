{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 14 source files from /home/<USER>/src/augment/research/eval/edit/data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 14/14 [00:01<00:00, 12.90it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 27 examples.\n"]}], "source": ["import pathlib\n", "import typing\n", "from experimental.dxy.edits.util_lib import load_edit_eval_tests\n", "\n", "tests = load_edit_eval_tests()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\"\"\"Show the contents of the file store.\"\"\"\n", "\n", "import argparse\n", "\n", "from research.model_server.file_store import FileStore\n", "\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31mdef main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--storage_path\",\n", "        type=str,\n", "        required=True,\n", "        help=\"path to the file store\",\n", "    )\n", "    args = parser.parse_args()\n", "    storage_path = args.storage_path\n", "    store = FileStore(Path(storage_path))\n", "    docs: list[Document] = store.get_files()\n", "    for doc in docs:\n", "        print(doc.id, doc.path)\n", "        print(doc.text)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: refactor main logic to print_files function\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32mdef print_files(docs: list[Document]):\n", "    for doc in docs:\n", "        print(doc.id, doc.path)\n", "        print(doc.text)\n", "\n", "def main():\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\n", "        \"--storage_path\",\n", "        type=str,\n", "        required=True,\n", "        help=\"path to the file store\",\n", "    )\n", "    args = parser.parse_args()\n", "    storage_path = args.storage_path\n", "    store = FileStore(Path(storage_path))\n", "    docs: list[Document] = store.get_files()\n", "    print_files(docs)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "\n", "if __name__ == \"__main__\":\n", "    main()\u001b[0m"]}], "source": ["tests[-1].show_code_w_updated_code_and_instruction()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_str\n", "\n", "\n", "def convert_fn(cur_data: dict) -> typing.Tuple[str, int, int]:\n", "    prefix = cur_data[\"prefix\"]\n", "    suffix = cur_data[\"suffix\"]\n", "    selected_code = cur_data[\"selected_code\"]\n", "    num_lines_in_prefix = len(prefix.splitlines(keepends=True))\n", "    num_lines_in_selection = len(selected_code.splitlines(keepends=True))\n", "    return (\n", "        utils_for_str.add_line_number(prefix + selected_code + suffix),\n", "        num_lines_in_prefix,\n", "        num_lines_in_prefix + num_lines_in_selection,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_file\n", "from research.core import utils_for_dataclass\n", "from experimental.dxy.edits import data_type\n", "\n", "xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13.jsonl.zst\"\n", "edit_data_in_house = utils_for_file.read_jsonl_zst(xpath)\n", "edit_data_in_house = [\n", "    utils_for_dataclass.create_from_dict(data_type.EditData, x)\n", "    for x in edit_data_in_house\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_str\n", "from experimental.dxy.edits.data_type import EditData\n", "from experimental.dxy.edits.data_type import get_lines_and_delete_line_indexes\n", "\n", "index = len(edit_data_in_house) // 4 + 1\n", "\n", "xdata = edit_data_in_house[index]\n", "\n", "\n", "def get_code_snippet_and_start_index(xdata: EditData, context: int = 32):\n", "    lines, potential_startlines = get_lines_and_delete_line_indexes(xdata)\n", "    for index in potential_startlines:\n", "        context_start = max(0, index - context)\n", "        context_end = min(len(lines), index + context)\n", "        yield utils_for_str.add_line_number(\n", "            \"\".join(lines[context_start:context_end])\n", "        ), index"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\"Synthetic Data for Edit.\"\"\"\n", "import copy\n", "import json\n", "import random\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "\n", "def convert_to_json_message(\n", "    start_line_number: str,\n", "    end_line_number: str,\n", "    reason: str,\n", "    category: str,\n", "    instruction: str,\n", "):\n", "    xdict = {}\n", "    xdict[\"Start-Line-Number\"] = start_line_number\n", "    xdict[\"Reason\"] = reason\n", "    xdict[\"End-Line-Number\"] = end_line_number\n", "    xdict[\"Need-Edit\"] = True\n", "    xdict[\"Catogory-of-Instruction\"] = category\n", "    xdict[\"Edit-Instruction\"] = instruction\n", "    return json.dumps(xdict, indent=2)\n", "\n", "\n", "system_prompt = r\"\"\"You are expert AI program and your answer will always be in the json format if possible.\n", "You will be given some code snippets, each code are marked with [Lx] at the start to indicate its line number.\n", "You will be given a start line number, and try to figure out the end line number (excluding the end line number itself).\n", "The selected code within the start line number and end line number will be the code you need to update, please make sure:\n", "- the selected code is reasonable, self-contained, do not cross the class boundary;\n", "- the selected code is not too long, do not exceed 50 lines;\n", "- the selected code is not too short, do not less than 5 lines;\n", "You should first produce the reason to select the code, then produce the end line number.\n", "Note that the end line number can be the same as start line number, which means in this case, you select empty code and the update is essentially adding new lines.\n", "After the end line number is produce, you should also try to suggest a reasonable edit instruction + the instruction type.\n", "If no edit is needed, just output a dict with {\"\"Need-Edit\": <PERSON>alse, \"Reason\": ...}.\n", "\"\"\"\n", "\n", "messages = []\n", "indexes = list(range(len(eval_data_list)))\n", "random.shuffle(indexes)\n", "\n", "for index in indexes:\n", "    xdata = copy.deepcopy(eval_data_list[index])\n", "    content, xstart, xend = convert_fn(xdata)\n", "    messages.append(f\"\"\"{content}\\n\\nThe start line number is {xstart}.\"\"\")\n", "    messages.append(\n", "        convert_to_json_message(\n", "            xstart,\n", "            xend,\n", "            xdata[\"misc\"][\"reason_to_lrange\"],\n", "            xdata[\"misc\"][\"category_of_instruction\"],\n", "            xdata[\"instruction\"],\n", "        )\n", "    )\n", "\n", "index = 6442\n", "eval_content, eval_start = next(\n", "    get_code_snippet_and_start_index(edit_data_in_house[index])\n", ")\n", "eval_start = \"L19\"\n", "messages.append(f\"\"\"{eval_content}\\n\\nThe start line number is {eval_start}.\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["responses = generate_response_via_chat(\n", "    messages,\n", "    system_prompt,\n", "    temperature=1.0,\n", "    num_completion=5,\n", "    response_format=\"json_object\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(messages[-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(responses[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(responses[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(responses[2])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data.indexed_dataset import MMapIndexedDataset\n", "\n", "index_dataset = MMapIndexedDataset(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.ft/all-inhouse-commitpackft-2023-11-02/train-v3_r4n_s4096_onlytgt_pack\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}