{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Randomly sample the edit scopes.\n", "import random\n", "import dataclasses\n", "from collections import defaultdict\n", "from research.core import utils_for_file\n", "from experimental.dxy.edits.util_lib import load_edit_eval_tests, load_repo_data\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope, find_edit_data_scope\n", "from experimental.dxy.edits.vis_lib import save_histogram, plot_edit_scope\n", "\n", "DATA_SAVE_DIR = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/\"\n", "\n", "raw_repo_data = load_repo_data()\n", "raw_edit_scopes = find_edit_data_scope(raw_repo_data)\n", "num2edit_scopes = defaultdict(list)\n", "for x in raw_edit_scopes:\n", "    num2edit_scopes[x.scope.stop - x.scope.start].append(x)\n", "numbers = {x: len(num2edit_scopes[x]) for x in num2edit_scopes.keys()}\n", "print(f\"Numbers: {numbers}\")\n", "max_number = sorted(list(numbers.values()), reverse=True)[len(numbers) // 2]\n", "average = len(raw_edit_scopes) // len(num2edit_scopes)\n", "print(f\"The average number should be {average}.\")\n", "print(f\"Set the max number should be {max_number}.\")\n", "for key in num2edit_scopes:\n", "    if len(num2edit_scopes[key]) > max_number:\n", "        num2edit_scopes[key] = random.sample(num2edit_scopes[key], max_number)\n", "final_edit_scopes: list[SimpleRawEditScope] = []\n", "for key in num2edit_scopes:\n", "    final_edit_scopes.extend(num2edit_scopes[key])\n", "random.shuffle(final_edit_scopes)\n", "print(f\"The final number of scopes is {len(final_edit_scopes)}.\")\n", "plot_edit_scope(final_edit_scopes, f\"{DATA_SAVE_DIR}/vis/sample.pdf\", bins_range=1)\n", "# save_histogram(\n", "#     [x.scope.stop - x.scope.start for x in final_edit_scopes],\n", "#     \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/histogram.pdf\",\n", "#     bins_range=1,\n", "# )\n", "utils_for_file.write_jsonl_zst(\n", "    f\"{DATA_SAVE_DIR}/raw-edit-scopes.jsonl.zst\",\n", "    [dataclasses.asdict(x) for x in final_edit_scopes],\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 14 source files from /home/<USER>/src/augment/research/eval/edit/data\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 14/14 [00:03<00:00,  3.56it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Loaded 27 examples.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Plotting the edit scope: 100%|██████████| 27/27 [00:00<00:00, 102671.09it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["No updated code\n", "Finish plotting the edit scope into /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03//vis/test-data.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["InstructCoder Load: 100%|██████████| 634/634 [00:00<00:00, 89562.11it/s]\n", "Plotting the edit scope: 100%|██████████| 634/634 [00:00<00:00, 205644.48it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03//vis/instruct_coder_github_seed.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["InstructCoder Load: 100%|██████████| 592/592 [00:00<00:00, 63754.02it/s]\n", "Plotting the edit scope: 100%|██████████| 592/592 [00:00<00:00, 171137.09it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03//vis/instruct_coder_additional_seed.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["InstructCoder Load: 100%|██████████| 108391/108391 [00:03<00:00, 32014.91it/s]\n", "Plotting the edit scope: 100%|██████████| 108391/108391 [00:00<00:00, 150040.05it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03//vis/instruct_coder_train.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["InstructCoder Load: 100%|██████████| 5708/5708 [00:00<00:00, 76244.77it/s]\n", "Plotting the edit scope: 100%|██████████| 5708/5708 [00:00<00:00, 193023.47it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03//vis/instruct_coder_valid.pdf\n"]}], "source": ["# Create\n", "import pathlib\n", "from experimental.dxy.edits.util_lib import (\n", "    load_edit_eval_tests,\n", "    load_repo_data,\n", "    load_instruct_coder_data,\n", ")\n", "from experimental.dxy.edits.vis_lib import save_histogram, plot_edit_scope\n", "\n", "DATA_SAVE_DIR = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03/\"\n", "\n", "inhouse_test_data = load_edit_eval_tests()\n", "plot_edit_scope(inhouse_test_data, f\"{DATA_SAVE_DIR}/vis/test-data.pdf\", bins_range=1)\n", "utils_for_file.write_jsonl_zst(\n", "    f\"{DATA_SAVE_DIR}/raw-test-data.jsonl.zst\",\n", "    [dataclasses.asdict(x) for x in inhouse_test_data],\n", ")\n", "\n", "# Instruct Coder GitHub seed\n", "instruct_coder_github_seed = load_instruct_coder_data(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/github_seed.json\"\n", "    )\n", ")\n", "plot_edit_scope(\n", "    instruct_coder_github_seed,\n", "    f\"{DATA_SAVE_DIR}/vis/instruct_coder_github_seed.pdf\",\n", "    bins_range=1,\n", ")\n", "utils_for_file.write_jsonl_zst(\n", "    f\"{DATA_SAVE_DIR}/raw-instruct_coder_github_seed.jsonl.zst\",\n", "    [dataclasses.asdict(x) for x in instruct_coder_github_seed],\n", ")\n", "\n", "# Instruct Coder additional seed\n", "instruct_coder_additional_seed = load_instruct_coder_data(\n", "    pathlib.Path(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/additional_seed.json\"\n", "    )\n", ")\n", "plot_edit_scope(\n", "    instruct_coder_additional_seed,\n", "    f\"{DATA_SAVE_DIR}/vis/instruct_coder_additional_seed.pdf\",\n", "    bins_range=1,\n", ")\n", "utils_for_file.write_jsonl_zst(\n", "    f\"{DATA_SAVE_DIR}/raw-instruct_coder_additional_seed.jsonl.zst\",\n", "    [dataclasses.asdict(x) for x in instruct_coder_additional_seed],\n", ")\n", "\n", "# Instruct Coder train\n", "instruct_coder_train = load_instruct_coder_data(\n", "    pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/train.json\")\n", ")\n", "plot_edit_scope(\n", "    instruct_coder_train, f\"{DATA_SAVE_DIR}/vis/instruct_coder_train.pdf\", bins_range=1\n", ")\n", "utils_for_file.write_jsonl_zst(\n", "    f\"{DATA_SAVE_DIR}/raw-instruct_coder_train.jsonl.zst\",\n", "    [dataclasses.asdict(x) for x in instruct_coder_train],\n", ")\n", "\n", "# Instruct Coder valid\n", "instruct_coder_valid = load_instruct_coder_data(\n", "    pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/edit.raw/InstructCoder/valid.json\")\n", ")\n", "plot_edit_scope(\n", "    instruct_coder_valid, f\"{DATA_SAVE_DIR}/vis/instruct_coder_valid.pdf\", bins_range=1\n", ")\n", "utils_for_file.write_jsonl_zst(\n", "    f\"{DATA_SAVE_DIR}/raw-instruct_coder_valid.jsonl.zst\",\n", "    [dataclasses.asdict(x) for x in instruct_coder_valid],\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["592\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Plotting the edit scope: 100%|██████████| 186/186 [00:00<00:00, 159114.94it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Finish plotting the edit scope into /mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03//vis/seeds_via_icoder_addseed.pdf\n"]}], "source": ["# Re-sample\n", "from experimental.dxy.edits.util_lib import sample_w_constraint\n", "\n", "print(len(instruct_coder_additional_seed))\n", "constraints = {\n", "    \"#suffix_lines==0\": (\n", "        lambda x: len(x.suffix.splitlines(keepends=True)) == 0,\n", "        30,\n", "    ),\n", "    \"#suffix_lines==1\": (\n", "        lambda x: len(x.suffix.splitlines(keepends=True)) == 1,\n", "        30,\n", "    ),\n", "    \"#suffix_lines==2\": (\n", "        lambda x: len(x.suffix.splitlines(keepends=True)) == 2,\n", "        30,\n", "    ),\n", "    \"#suffix_lines==3\": (\n", "        lambda x: len(x.suffix.splitlines(keepends=True)) == 3,\n", "        30,\n", "    ),\n", "    \"#suffix_lines==4\": (\n", "        lambda x: len(x.suffix.splitlines(keepends=True)) == 4,\n", "        30,\n", "    ),\n", "    \"#prefix_lines==0\": (\n", "        lambda x: len(x.prefix.splitlines(keepends=True)) == 0,\n", "        30,\n", "    ),\n", "    \"#prefix_lines==1\": (\n", "        lambda x: len(x.prefix.splitlines(keepends=True)) == 1,\n", "        30,\n", "    ),\n", "    \"#prefix_lines==2\": (\n", "        lambda x: len(x.prefix.splitlines(keepends=True)) == 2,\n", "        30,\n", "    ),\n", "    \"#prefix_lines==3\": (\n", "        lambda x: len(x.prefix.splitlines(keepends=True)) == 3,\n", "        30,\n", "    ),\n", "    \"#prefix_lines==4\": (\n", "        lambda x: len(x.prefix.splitlines(keepends=True)) == 4,\n", "        30,\n", "    ),\n", "    \"#selected_lines==0\": (\n", "        lambda x: len(x.selected_code.splitlines(keepends=True)) == 0,\n", "        0,\n", "    ),\n", "    \"#selected_lines==1\": (\n", "        lambda x: len(x.selected_code.splitlines(keepends=True)) == 1,\n", "        10,\n", "    ),\n", "    \"#selected_lines==2\": (\n", "        lambda x: len(x.selected_code.splitlines(keepends=True)) == 2,\n", "        10,\n", "    ),\n", "    \"#selected_lines>=50\": (\n", "        lambda x: len(x.selected_code.splitlines(keepends=True)) >= 50,\n", "        0,\n", "    ),\n", "    \"#updated_lines==0\": (\n", "        lambda x: len(x.updated_code.splitlines(keepends=True)) == 0,\n", "        0,\n", "    ),\n", "    \"#updated_lines>=50\": (\n", "        lambda x: len(x.updated_code.splitlines(keepends=True)) >= 50,\n", "        0,\n", "    ),\n", "}\n", "\n", "final_seed = sample_w_constraint(\n", "    instruct_coder_additional_seed,\n", "    constraints=constraints,\n", ")\n", "\n", "plot_edit_scope(\n", "    final_seed, f\"{DATA_SAVE_DIR}/vis/seeds_via_icoder_addseed.pdf\", bins_range=1\n", ")\n", "utils_for_file.write_jsonl(\n", "    f\"{DATA_SAVE_DIR}/raw-seeds_via_icoder_addseed.jsonl\",\n", "    [dataclasses.asdict(x) for x in final_seed],\n", ")\n", "utils_for_file.write_json(\n", "    f\"{DATA_SAVE_DIR}/raw-seeds_via_icoder_addseed.json\",\n", "    [dataclasses.asdict(x) for x in final_seed],\n", "    indent=2,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Display some examples!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_edit_scopes[1].show_code()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_seed[3].show_code_w_updated_code_and_instruction()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}