{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Preparing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core import utils_for_file\n", "from research.data.synthetic_code_edit import types\n", "from research.data.synthetic_code_edit import seed_lib\n", "from research.data.synthetic_code_edit import sampling_lib\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "seeds = seed_lib.load_code_edit_seeds()\n", "seeds = {k: v for k, v in seeds.items() if len(v) > 0}\n", "print(f\"Loaded {len(seeds)} categories: {list(seeds.keys())}\")\n", "\n", "# raw_github_data = utils_for_file.read_jsonl_zst(\n", "#     \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/all.jsonl.zst\"\n", "# )\n", "raw_github_data = utils_for_file.read_jsonl_zst(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k.jsonl.zst\"\n", ")\n", "print(f\"Raw GitHub data's keys: {raw_github_data[0].keys()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import copy\n", "import typing\n", "import random\n", "\n", "\n", "def preprocess_github(data: dict, min_lines: int, max_lines: int) -> typing.Optional[types.CodeEditData]:\n", "    data = copy.deepcopy(data)\n", "    try:\n", "        lranges = sampling_lib.find_lrange_via_consecutive_sibling_nodes(\n", "            data[\"content\"], max_consecutive_nodes=8\n", "        )\n", "    except:\n", "        return None\n", "    possible_lranges = [\n", "        lrange\n", "        for lrange in lranges\n", "        if min_lines <= lrange.stop - lrange.start <= max_lines\n", "    ]\n", "    random.shuffle(possible_lranges)\n", "    # print(f\"There are {len(possible_lranges)} possible lranges.\")\n", "    if not len(possible_lranges):\n", "        return None\n", "    lrange = possible_lranges[0]\n", "    lines = data[\"content\"].splitlines(keepends=True)\n", "    prefix = \"\".join(lines[: lrange.start])\n", "    middle = \"\".join(lines[lrange.start : lrange.stop])\n", "    suffix = \"\".join(lines[lrange.stop :])\n", "    return types.CodeEditData(\n", "        repo_url=data[\"repo_url\"],\n", "        file_name=data[\"file_path\"],\n", "        prefix=prefix,\n", "        suffix=suffix,\n", "        updated_code=middle,\n", "        metadata={\"original-github\": data},\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "all_sampled_data = []\n", "for index, data in enumerate(raw_github_data):\n", "    if random.random() < 0.05:\n", "        x1 = preprocess_github(data, 1, 10)\n", "    else:\n", "        x1 = preprocess_github(data, 5, 10)\n", "    x2 = preprocess_github(data, 10, 30)\n", "    x3 = preprocess_github(data, 30, 60)\n", "    if x1 is not None:\n", "        all_sampled_data.append(x1)\n", "    if x2 is not None:\n", "        all_sampled_data.append(x2)\n", "    if x3 is not None:\n", "        all_sampled_data.append(x3)\n", "print(f\"There are {len(all_sampled_data)} examples in total.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from research.core import utils_for_file\n", "\n", "all_sampled_data_in_json = [dataclasses.asdict(x) for x in all_sampled_data]\n", "utils_for_file.write_jsonl_zst(\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst\",\n", "    all_sampled_data_in_json\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}