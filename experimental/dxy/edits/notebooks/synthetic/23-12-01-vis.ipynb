{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Visualize the Generated Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Randomly sample the edit scopes.\n", "import random\n", "import dataclasses\n", "from collections import defaultdict\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from experimental.dxy.edits.util_lib import load_edit_eval_tests, load_repo_data\n", "from experimental.dxy.edits.sample_lib import SimpleRawEditScope, find_edit_data_scope\n", "from experimental.dxy.edits.vis_lib import save_histogram, plot_edit_scope"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m        self.list_network_namespaces.return_value = [namespace]\n", "        self.mock_ip.get_devices.return_value = [\n", "            l3_test_common.FakeDev('qg-aaaa'),\n", "            l3_test_common.FakeDev('sg-aaaa')]\n", "\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "\n", "        ns = dvr_snat_ns.SnatNamespace(\n", "            'bar', self.conf, agent.driver, agent.use_ipv6)\n", "        ns.create()\n", "\n", "        ns.delete()\n", "        calls = [mock.call('qg-aaaa',\n", "                           namespace=namespace,\n", "                           prefix=namespaces.EXTERNAL_DEV_PREFIX),\n", "                 mock.call('sg-aaaa',\n", "                           namespace=namespace,\n", "                           prefix=lib_constants.SNAT_INT_DEV_PREFIX)]\n", "        self.mock_driver.unplug.assert_has_calls(calls, any_order=True)\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Implement tests for enabling and disabling the metadata proxy on the router configuration.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    def _configure_metadata_proxy(self, enableflag=True):\n", "        if not enableflag:\n", "            self.conf.set_override('enable_metadata_proxy', False)\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        router_id = _uuid()\n", "        router = {'id': router_id,\n", "                  'external_gateway_info': {},\n", "                  'routes': [],\n", "                  'distributed': False}\n", "        driver = metadata_driver.MetadataDriver\n", "        with mock.patch.object(\n", "                driver, 'destroy_monitored_metadata_proxy') as destroy_proxy, \\\n", "                mock.patch.object(\n", "                    driver, 'spawn_monitored_metadata_proxy') as spawn_proxy, \\\n", "                mock.patch.object(netutils, 'is_ipv6_enabled') as ipv6_mock:\n", "            ipv6_mock.return_value = True\n", "            agent._process_added_router(router)\n", "            if enableflag:\n", "                spawn_proxy.assert_called_with(\n", "                    mock.ANY,\n", "                    mock.ANY,\n", "                    self.conf.metadata_port,\n", "                    mock.ANY,\n", "                    router_id=router_id,\n", "                    bind_address='::',\n", "                )\n", "            else:\n", "                self.assertFalse(spawn_proxy.call_count)\n", "            agent._safe_router_removed(router_id)\n", "            if enableflag:\n", "                destroy_proxy.assert_called_with(mock.ANY,\n", "                                                 router_id,\n", "                                                 mock.ANY,\n", "                                                 'qrouter-' + router_id)\n", "            else:\n", "                self.assertFalse(destroy_proxy.call_count)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def test_enable_metadata_proxy(self):\n", "        self._configure_metadata_proxy()\n", "\n", "    def test_disable_metadata_proxy_spawn(self):\n", "        self._configure_metadata_proxy(enableflag=False)\n", "\n", "    def test__process_router_added_router_not_in_cache_after_failure(self):\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        router_id = _uuid()\n", "        router = {'id': router_id}\n", "        ri_mock = mock.Mock()\n", "\n", "        class TestRouterProcessingException(Exception):\n", "            pass\n", "\n", "        with mock.patch.object(agent, \"_router_added\"), \\\n", "                mock.patch.dict(agent.router_info, {router_id: ri_mock}):\n", "            ri_mock.process.side_effect = TestRouterProcessingException()\n", "            self.assertRaises(\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/000-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m    def test_duplicate_ips(self):\n", "        with self.subnet() as subnet:\n", "            # Allocate specific IP\n", "            kwargs = {\"fixed_ips\": [{'subnet_id': subnet['subnet']['id'],\n", "                                     'ip_address': '********'},\n", "                                    {'subnet_id': subnet['subnet']['id'],\n", "                                     'ip_address': '********'}]}\n", "            net_id = subnet['subnet']['network_id']\n", "            res = self._create_port(self.fmt, net_id=net_id, **kwargs)\n", "            self.assertEqual(webob.exc.HTTPClientError.code, res.status_int)\n", "\n", "    def test_fixed_ip_invalid_subnet_id(self):\n", "        with self.subnet() as subnet:\n", "            # Allocate specific IP\n", "            kwargs = {\"fixed_ips\": [{'subnet_id': 'i am invalid',\n", "                                     'ip_address': '********'}]}\n", "            net_id = subnet['subnet']['network_id']\n", "            res = self._create_port(self.fmt, net_id=net_id, **kwargs)\n", "            self.assertEqual(webob.exc.HTTPClientError.code, res.status_int)\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m    def test_fixed_ip_invalid_ip(self):\n", "        with self.subnet() as subnet:\n", "            # Allocate specific IP\n", "            kwargs = {\"fixed_ips\": [{'subnet_id': subnet['subnet']['id'],\n", "                                     'ip_address': '********'}]}\n", "            net_id = subnet['subnet']['network_id']\n", "            res = self._create_port(self.fmt, net_id=net_id, **kwargs)\n", "            self.assertEqual(webob.exc.HTTPClientError.code, res.status_int)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Change the allocated IP address to a valid format within the subnet range.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    def test_fixed_ip_invalid_ip(self):\n", "        with self.subnet() as subnet:\n", "            # Allocate specific IP\n", "            kwargs = {\"fixed_ips\": [{'subnet_id': subnet['subnet']['id'],\n", "                                     'ip_address': '********5555'}]}\n", "            net_id = subnet['subnet']['network_id']\n", "            res = self._create_port(self.fmt, net_id=net_id, **kwargs)\n", "            self.assertEqual(webob.exc.HTTPClientError.code, res.status_int)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def test_requested_ips_only(self):\n", "        with self.subnet() as subnet:\n", "            fixed_ip_data = [{'ip_address': '********',\n", "                             'subnet_id': subnet['subnet']['id']}]\n", "            with self.port(subnet=subnet, fixed_ips=fixed_ip_data) as port:\n", "                ips = port['port']['fixed_ips']\n", "                self.assertEqual(1, len(ips))\n", "                self.assertEqual('********', ips[0]['ip_address'])\n", "                self.assertEqual(ips[0]['subnet_id'], subnet['subnet']['id'])\n", "                ips_only = ['*********', '********0', '********2', '********1',\n", "                            '********', '*********', '*********']\n", "                ports_to_delete = []\n", "                for i in ips_only:\n", "                    kwargs = {\"fixed_ips\": [{'ip_address': i}]}\n", "                    net_id = port['port']['network_id']\n", "                    res = self._create_port(self.fmt, net_id=net_id, **kwargs)\n", "                    port = self.deserialize(self.fmt, res)\n", "                    ports_to_delete.append(port)\n", "                    ips = port['port']['fixed_ips']\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/001-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m    def test_call_driver(self):\n", "        network = mock.MagicMock()\n", "        network.id = '1'\n", "        network.segments = None\n", "        dhcp = dhcp_agent.DhcpAgent(cfg.CONF)\n", "        self.assertTrue(dhcp.call_driver('foo', network))\n", "        self.driver.assert_called_once_with(cfg.CONF,\n", "                                            mock.ANY,\n", "                                            mock.ANY,\n", "                                            mock.ANY,\n", "                                            mock.ANY,\n", "                                            None)\n", "\n", "    def test_call_driver_no_network(self):\n", "        network = None\n", "        dhcp = dhcp_agent.DhcpAgent(cfg.CONF)\n", "        self.assertIsNone(dhcp.call_driver('foo', network))\n", "\n", "    def _test_call_driver_failure(self, exc=None,\n", "                                  trace_level='exception', expected_sync=True):\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m    def test_call_driver(self):\n", "        network = mock.MagicMock()\n", "        network.id = '1'\n", "        network.segments = None\n", "        dhcp = dhcp_agent.DhcpAgent(cfg.CONF)\n", "        self.assertTrue(dhcp.call_driver('foo', network))\n", "        self.driver.assert_called_once_with(cfg.CONF,\n", "                                            mock.ANY,\n", "                                            mock.ANY,\n", "                                            mock.ANY,\n", "                                            mock.ANY,\n", "                                            None)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Add mock exception handling for the 'call_driver' method to validate the network model.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m        network = mock.MagicMock()\n", "        network.id = '1'\n", "        network.segments = None\n", "        self.driver.return_value.foo.side_effect = exc or Exception\n", "        dhcp = dhcp_agent.DhcpAgent(HOSTNAME)\n", "        with mock.patch.object(dhcp,\n", "                               'schedule_resync') as schedule_resync:\n", "            self.assertIsNone(dhcp.call_driver('foo', network))\n", "            self.driver.assert_called_once_with(cfg.CONF,\n", "                                                mock.ANY,\n", "                                                mock.ANY,\n", "                                                mock.ANY,\n", "                                                mock.ANY,\n", "                                                None)\n", "            self.assertEqual(expected_sync, schedule_resync.called)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def test_call_driver_ip_address_generation_failure(self):\n", "        error = oslo_messaging.RemoteError(\n", "            exc_type='IpAddressGenerationFailure')\n", "        self._test_call_driver_failure(exc=error, expected_sync=False)\n", "\n", "    def test_call_driver_failure(self):\n", "        self._test_call_driver_failure()\n", "\n", "    def test_call_driver_remote_error_net_not_found(self):\n", "        self._test_call_driver_failure(\n", "            exc=oslo_messaging.RemoteError(exc_type='NetworkNotFound'),\n", "            trace_level='warning')\n", "\n", "    def test_call_driver_network_not_found(self):\n", "        self._test_call_driver_failure(\n", "            exc=exceptions.NetworkNotFound(net_id='1'),\n", "            trace_level='warning')\n", "\n", "    def test_call_driver_conflict(self):\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/002-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m        # NOTE(slaweq): self.internal_ports is a list of port objects but\n", "        # when it is updated in _process_internal_ports() method,\n", "        # but it can be based only on indexes of elements in\n", "        # self.internal_ports as index of element to updated is unknown.\n", "        # It has to be done based on port_id and this method is doing exactly\n", "        # that.\n", "        for index, p in enumerate(self.internal_ports):\n", "            if p['id'] == port['id']:\n", "                self.internal_ports[index] = port\n", "                break\n", "        else:\n", "            self.internal_ports.append(port)\n", "\n", "    @staticmethod\n", "    def _get_updated_ports(existing_ports, current_ports):\n", "        updated_ports = []\n", "        current_ports_dict = {p['id']: p for p in current_ports}\n", "        for existing_port in existing_ports:\n", "            current_port = current_ports_dict.get(existing_port['id'])\n", "            if current_port:\n", "                fixed_ips_changed = (\n", "                    sorted(existing_port['fixed_ips'],\n", "                           key=helpers.safe_sort_key) !=\n", "                    sorted(current_port['fixed_ips'],\n", "                           key=helpers.safe_sort_key))\n", "                mtu_changed = existing_port['mtu'] != current_port['mtu']\n", "                if fixed_ips_changed or mtu_changed:\n", "                    updated_ports.append(current_port)\n", "        return updated_ports\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Include an IPv6 address handling method for ports\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    @staticmethod\n", "    def _port_has_ipv6_subnet(port):\n", "        if 'subnets' in port:\n", "            for subnet in port['subnets']:\n", "                if (netaddr.IPNetwork(subnet['cidr']).version == 6 and\n", "                        subnet['cidr'] !=\n", "                        lib_constants.PROVISIONAL_IPV6_PD_PREFIX):\n", "                    return True\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def enable_radvd(self, internal_ports=None):\n", "        LOG.debug('Spawning radvd daemon in router device: %s', self.router_id)\n", "        if not internal_ports:\n", "            internal_ports = self.internal_ports\n", "        self.radvd.enable(internal_ports)\n", "\n", "    def disable_radvd(self):\n", "        if self.radvd:\n", "            LOG.debug('Terminating radvd daemon in router device: %s',\n", "                      self.router_id)\n", "            self.radvd.disable()\n", "\n", "    def internal_network_updated(self, port):\n", "        interface_name = self.get_internal_device_name(port['id'])\n", "        ip_cidrs = common_utils.fixed_ip_cidrs(port['fixed_ips'])\n", "        mtu = port['mtu']\n", "        self.driver.set_mtu(interface_name, mtu, namespace=self.ns_name,\n", "                            prefix=INTERNAL_DEV_PREFIX)\n", "        self.driver.init_router_port(\n", "            interface_name,\n", "            ip_cidrs=ip_cidrs,\n", "            namespace=self.ns_name)\n", "\n", "    def address_scope_mangle_rule(self, device_name, mark_mask):\n", "        return '-i %s -j MARK --set-xmark %s' % (device_name, mark_mask)\n", "\n", "    def address_scope_filter_rule(self, device_name, mark_mask):\n", "        return '-o %s -m mark ! --mark %s -j DROP' % (\n", "            device_name, mark_mask)\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/003-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m        cidr = None\n", "        for fixed_ip in port['fixed_ips']:\n", "            subnet_id = fixed_ip['subnet_id']\n", "            subnet = self._plugin.get_subnet(context, subnet_id)\n", "            if subnet['ip_version'] != const.IP_VERSION_4:\n", "                continue\n", "            cidr = subnet['cidr']\n", "        return cidr\n", "\n", "    def _get_v4_network_of_all_router_ports(self, context, router_id,\n", "                                            ports=None):\n", "        networks = []\n", "        ports = ports or self._get_router_ports(context, router_id)\n", "        for port in ports:\n", "            network = self._get_v4_network_for_router_port(context, port)\n", "            if network:\n", "                networks.append(network)\n", "\n", "        return networks\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Generate router external IDs.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    def _gen_router_ext_ids(self, router):\n", "        return {\n", "            ovn_const.O<PERSON>_ROUTER_NAME_EXT_ID_KEY:\n", "                router.get('name', 'no_router_name'),\n", "            ovn_const.OVN_REV_NUM_EXT_ID_KEY: str(utils.get_revision_number(\n", "                router, ovn_const.TYPE_ROUTERS)),\n", "            ovn_const.OVN_AZ_HINTS_EXT_ID_KEY:\n", "                ','.join(common_utils.get_az_hints(router)),\n", "        }\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def create_router(self, context, router, add_external_gateway=True):\n", "        \"\"\"Create a logical router.\"\"\"\n", "        external_ids = self._gen_router_ext_ids(router)\n", "        enabled = router.get('admin_state_up')\n", "        lrouter_name = utils.ovn_name(router['id'])\n", "        added_gw_port = None\n", "        options = {'always_learn_from_arp_request': 'false',\n", "                   'dynamic_neigh_routers': 'true'}\n", "        with self._nb_idl.transaction(check_error=True) as txn:\n", "            txn.add(self._nb_idl.create_lrouter(lrouter_name,\n", "                                                external_ids=external_ids,\n", "                                                enabled=enabled,\n", "                                                options=options))\n", "            # TODO(lucasagomes): add_external_gateway is being only used\n", "            # by the ovn_db_sync.py script, remove it after the database\n", "            # synchronization work\n", "            if add_external_gateway:\n", "                networks = self._get_v4_network_of_all_router_ports(\n", "                    context, router['id'])\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/007-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m            LOG.error(\"No type driver for external network_type: %s. \"\n", "                      \"Service terminated!\", ext_network_type)\n", "            raise SystemExit(1)\n", "\n", "    def _process_provider_segment(self, segment):\n", "        (network_type, physical_network,\n", "         segmentation_id) = (self._get_attribute(segment, attr)\n", "                             for attr in provider.ATTRIBUTES)\n", "\n", "        if validators.is_attr_set(network_type):\n", "            segment = {api.NETWORK_TYPE: network_type,\n", "                       api.PHYSICAL_NETWORK: physical_network,\n", "                       api.SEGMENTATION_ID: segmentation_id}\n", "            self.validate_provider_segment(segment)\n", "            return segment\n", "\n", "        msg = _(\"network_type required\")\n", "        raise exc.InvalidInput(error_message=msg)\n", "\n", "    def _process_provider_create(self, network):\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m        if any(validators.is_attr_set(network.get(attr))\n", "               for attr in provider.ATTRIBUTES):\n", "            segment = self._get_provider_segment(network)\n", "            return [self._process_provider_segment(segment)]\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Remove the multiprovider segment processing from the 'if' condition and related code.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m        if any(validators.is_attr_set(network.get(attr))\n", "               for attr in provider.ATTRIBUTES):\n", "            # Verify that multiprovider and provider attributes are not set\n", "            # at the same time.\n", "            if validators.is_attr_set(network.get(mpnet_apidef.SEGMENTS)):\n", "                raise mpnet_exc.SegmentsSetInConjunctionWithProviders()\n", "            segment = self._get_provider_segment(network)\n", "            return [self._process_provider_segment(segment)]\n", "        elif validators.is_attr_set(network.get(mpnet_apidef.SEGMENTS)):\n", "            segments = [self._process_provider_segment(s)\n", "                        for s in network[mpnet_apidef.SEGMENTS]]\n", "            mpnet_apidef.check_duplicate_segments(\n", "                segments, self.is_partial_segment)\n", "            return segments\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def _match_segment(self, segment, filters):\n", "        return all(not filters.get(attr) or segment.get(attr) in filters[attr]\n", "                   for attr in provider.ATTRIBUTES)\n", "\n", "    def _get_provider_segment(self, network):\n", "        # TODO(manishg): Placeholder method\n", "        # Code intended for operating on a provider segment should use\n", "        # this method to extract the segment, even though currently the\n", "        # segment attributes are part of the network dictionary. In the\n", "        # future, network and segment information will be decoupled and\n", "        # here we will do the job of extracting the segment information.\n", "        return network\n", "\n", "    def network_matches_filters(self, network, filters):\n", "        if not filters:\n", "            return True\n", "        if any(validators.is_attr_set(network.get(attr))\n", "               for attr in provider.ATTRIBUTES):\n", "            segments = [self._get_provider_segment(network)]\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/008-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m                                     network_id)\n", "        interface_info = {'subnet_id': subnet['id']}\n", "        interface = self.plugin.add_router_interface(\n", "            self.admin_ctx, router['id'], interface_info)\n", "        try:\n", "            self.plugin.delete_router(self.admin_ctx, router['id'])\n", "        except l3_exc.RouterInUse as exc:\n", "            msg = 'Router %s has ports still attached: %s' % (\n", "                router['id'], interface['port_id'])\n", "            self.assertEqual(msg, exc.msg)\n", "        else:\n", "            self.fail('The router %s deletion should have raised a '\n", "                      '\"RouterInUse\" exception' % router['id'])\n", "        bindings = self.plugin.get_ha_router_port_bindings(\n", "            self.admin_ctx, [router['id']])\n", "        self.assertEqual(2, len(bindings))\n", "\n", "    def test_update_ha_router_replicated_interface_port_ip_not_allowed(self):\n", "        router = self._create_router()\n", "        network_id = self._create_network(self.core_plugin, self.admin_ctx)\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Reduce redundancy by removing the repeated code block that defines and uses the 'subnet' variable.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m        subnet = self._create_subnet(self.core_plugin, self.admin_ctx,\n", "                                     network_id)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m        interface_info = {'subnet_id': subnet['id']}\n", "        self.plugin.add_router_interface(self.admin_ctx,\n", "                                         router['id'],\n", "                                         interface_info)\n", "        filters = {'device_id': [router['id']],\n", "                   'device_owner': [constants.DEVICE_OWNER_HA_REPLICATED_INT]}\n", "        ports = self.core_plugin.get_ports(self.admin_ctx, filters=filters)\n", "        port = {'port': {'fixed_ips': [\n", "            {'ip_address': '**********'},\n", "            {'ip_address': '**********'}]}}\n", "        self.assertRaises(n_exc.BadRequest,\n", "                          self.core_plugin.update_port,\n", "                          self.admin_ctx, ports[0]['id'],\n", "                          port)\n", "\n", "    def test_update_router_port_bindings_no_ports(self):\n", "        self.plugin._update_router_port_bindings(\n", "            self.admin_ctx, {}, self.agent1['host'])\n", "\n", "    def _get_first_interface(self, router_id):\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/010-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\n", "            res1 = self._create_port(\n", "                self.fmt, n['network']['id'],\n", "                security_groups=[sg1_id,\n", "                                 sg2_id])\n", "            ports_rest1 = self.deserialize(self.fmt, res1)\n", "            port_id1 = ports_rest1['port']['id']\n", "            self.rpc.devices = {port_id1: ports_rest1['port']}\n", "            devices = [port_id1, 'no_exist_device']\n", "\n", "            res2 = self._create_port(\n", "                self.fmt, n['network']['id'],\n", "                security_groups=[sg2_id])\n", "            ports_rest2 = self.deserialize(self.fmt, res2)\n", "            port_id2 = ports_rest2['port']['id']\n", "            port_fixed_ip2 = ports_rest2['port']['fixed_ips'][0]['ip_address']\n", "            ctx = context.get_admin_context()\n", "            ports_rpc = self.rpc.security_group_rules_for_devices(\n", "                ctx, devices=devices)\n", "            port_rpc = ports_rpc[port_id1]\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m            expected = [{'direction': 'ingress',\n", "                         'source_ip_prefix': port_fixed_ip2 + '/32',\n", "                         'protocol': const.PROTO_NAME_TCP,\n", "                         'ethertype': const.IPv4,\n", "                         'port_range_max': 25, 'port_range_min': 24,\n", "                         'remote_group_id': sg2_id,\n", "                         'security_group_id': sg1_id}\n", "                        ] + ingress_address_assignment_rules(ports_rest1)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Introduce ingoing traffic rules to the expected list for assigned addresses and security groups.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m            expected = [{'direction': 'egress', 'ethertype': const.IPv4,\n", "                         'security_group_id': sg1_id},\n", "                        {'direction': 'egress', 'ethertype': const.IPv6,\n", "                         'security_group_id': sg1_id},\n", "                        {'direction': 'egress', 'ethertype': const.IPv4,\n", "                         'security_group_id': sg2_id},\n", "                        {'direction': 'egress', 'ethertype': const.IPv6,\n", "                         'security_group_id': sg2_id},\n", "                        {'direction': 'ingress',\n", "                         'source_ip_prefix': port_fixed_ip2 + '/32',\n", "                         'protocol': const.PROTO_NAME_TCP,\n", "                         'ethertype': const.IPv4,\n", "                         'port_range_max': 25, 'port_range_min': 24,\n", "                         'remote_group_id': sg2_id,\n", "                         'security_group_id': sg1_id},\n", "                        ] + ingress_address_assignment_rules(ports_rest1)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m            self.assertEqual(expected,\n", "                             port_rpc['security_group_rules'])\n", "            self._delete('ports', port_id1)\n", "            self._delete('ports', port_id2)\n", "\n", "    def test_security_group_info_for_devices_ipv4_source_group(self):\n", "\n", "        with self.network() as n,\\\n", "                self.subnet(n),\\\n", "                self.security_group() as sg1,\\\n", "                self.security_group() as sg2,\\\n", "                self.address_group(addresses=['********/24']) as ag1:\n", "            sg1_id = sg1['security_group']['id']\n", "            sg2_id = sg2['security_group']['id']\n", "            ag1_id = ag1['address_group']['id']\n", "            ag1_ip = ag1['address_group']['addresses'][0]\n", "            rule1 = self._build_security_group_rule(\n", "                sg1_id,\n", "                'ingress', const.PROTO_NAME_TCP, '24',\n", "                '25', remote_group_id=sg2['security_group']['id'])\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/011-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m        ri.process()\n", "        # For some reason set logic does not work well with\n", "        # IpTablesRule instances\n", "        nat_rules_delta = [r for r in ri.iptables_manager.ipv4['nat'].rules\n", "                           if r not in orig_nat_rules]\n", "        self.assertEqual(1, len(nat_rules_delta))\n", "        mangle_rules_delta = [\n", "            r for r in ri.iptables_manager.ipv4['mangle'].rules\n", "            if r not in orig_mangle_rules]\n", "        self.assertEqual(1, len(mangle_rules_delta))\n", "        self._verify_snat_mangle_rules(nat_rules_delta, mangle_rules_delta,\n", "                                       router, random_fully)\n", "        self.assertEqual(1, self.send_adv_notif.call_count)\n", "\n", "    def test_process_router_snat_enabled_random_fully(self):\n", "        self._test_process_router_snat_enabled(True)\n", "\n", "    def test_process_router_snat_enabled_random_fully_false(self):\n", "        self._test_process_router_snat_enabled(False)\n", "\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m    def test_process_update_snat_routing_table(self):\n", "        self._test_update_routing_table()\n", "\n", "    def test_process_not_update_snat_routing_table(self):\n", "        self._test_update_routing_table(is_snat_host=False)\n", "\n", "    def test_process_not_update_ha_routing_table(self):\n", "        self._test_update_routing_table(enable_ha=True)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Add back the '_test_update_routing_table' method and its associated test cases to the test suite.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m    def _test_update_routing_table(self, is_snat_host=True, enable_ha=False):\n", "        router = l3_test_common.prepare_router_data(enable_ha=enable_ha)\n", "        uuid = router['id']\n", "        s_netns = 'snat-' + uuid\n", "        q_netns = 'qrouter-' + uuid\n", "        fake_route1 = {'destination': '***********/16',\n", "                       'nexthop': '**********'}\n", "        calls = [mock.call('replace', fake_route1, q_netns)]\n", "        agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)\n", "        self._set_ri_kwargs(agent, uuid, router)\n", "        router_cls = (dvr_edge_ha_router.DvrEdgeHaRouter if enable_ha else\n", "                      dvr_router.DvrEdgeRouter)\n", "        ri = router_cls(HOSTNAME, **self.ri_kwargs)\n", "        ri._update_routing_table = mock.Mock()\n", "\n", "        with mock.patch.object(ri, '_is_this_snat_host') as snat_host:\n", "            snat_host.return_value = is_snat_host\n", "            ri.update_routing_table('replace', fake_route1)\n", "            if is_snat_host and not enable_ha:\n", "                calls += [mock.call('replace', fake_route1, s_netns)]\n", "            ri._update_routing_table.assert_has_calls(calls, any_order=True)\n", "            self.assertEqual(len(calls), ri._update_routing_table.call_count)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m\n", "    def test_process_update_snat_routing_table(self):\n", "        self._test_update_routing_table()\n", "\n", "    def test_process_not_update_snat_routing_table(self):\n", "        self._test_update_routing_table(is_snat_host=False)\n", "\n", "    def test_process_not_update_ha_routing_table(self):\n", "        self._test_update_routing_table(enable_ha=True)\n", "\n", "    def _test_update_routing_table_ecmp(self, is_snat_host=True,\n", "                                        enable_ha=False):\n", "        router = l3_test_common.prepare_router_data()\n", "        uuid = router['id']\n", "        s_netns = 'snat-' + uuid\n", "        q_netns = 'qrouter-' + uuid\n", "        fake_route_list = [{'destination': '***********/16',\n", "                            'nexthop': '**********'},\n", "                           {'destination': '***********/16',\n", "                            'nexthop': '**********'}]\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/012-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[34m        l2pop_mech.L2PopulationAgentNotify = mock.Mock()\n", "        l2pop_mech.rpc_ctx = mock.Mock()\n", "        port = {'device_owner': ''}\n", "        context = mock.Mock()\n", "        context.current = port\n", "        with mock.patch.object(l2pop_mech,\n", "                               '_get_agent_fdb',\n", "                               return_value=None) as upd_port_down,\\\n", "                mock.patch.object(l2pop_mech.L2PopulationAgentNotify,\n", "                                  'remove_fdb_entries'):\n", "            l2pop_mech.delete_port_postcommit(context)\n", "            self.assertTrue(upd_port_down.called)\n", "\n", "    def test_delete_unbound_port(self):\n", "        self._test_delete_port_handles_agentless_host_id(None)\n", "\n", "    def test_delete_port_bound_to_agentless_host(self):\n", "        self._test_delete_port_handles_agentless_host_id('test')\n", "\n", "    def _test_delete_port_handles_agentless_host_id(self, host):\n", "\u001b[0m\u001b[33m<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<\u001b[0m\n", "\u001b[31m        self._test_delete_port_handles_agentless_host_id(host)\n", "\u001b[0m\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "instruction: Integrate test setup for 'delete_port_postcommit' into a shared testing method to reduce redundancy.\n", "\u001b[33m>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\u001b[0m\n", "\u001b[32m        l2pop_mech = l2pop_mech_driver.L2populationMechanismDriver()\n", "        l2pop_mech.initialize()\n", "\n", "        with self.port() as port:\n", "            port['port'][portbindings.HOST_ID] = host\n", "            bindings = [models.PortBindingLevel()]\n", "            port_context = driver_context.PortContext(\n", "                self.driver, self.context, port['port'],\n", "                self.driver.get_network(\n", "                    self.context, port['port']['network_id']),\n", "                models.PortBinding(), bindings)\n", "            # The point is to provide coverage and to assert that no exceptions\n", "            # are raised.\n", "            l2pop_mech.delete_port_postcommit(port_context)\n", "\u001b[0m\u001b[33m================================================================================\u001b[0m\n", "\u001b[34m    def test_delete_dvr_snat_port_fdb_entries(self):\n", "        l2pop_mech = l2pop_mech_driver.L2populationMechanismDriver()\n", "        l2pop_mech.initialize()\n", "\n", "        self._setup_l3()\n", "\n", "        with self.subnet(network=self._network, enable_dhcp=False) as snet:\n", "            host_arg = {portbindings.HOST_ID: HOST, 'admin_state_up': True}\n", "            with self.port(subnet=snet,\n", "                           is_admin=True,\n", "                           device_owner=constants.DEVICE_OWNER_ROUTER_SNAT,\n", "                           arg_list=(portbindings.HOST_ID,),\n", "                           **host_arg) as p:\n", "                device = 'tap' + p['port']['id']\n", "                self.callbacks.update_device_up(self.adminContext,\n", "                                                agent_id=HOST, device=device)\n", "                self.mock_fanout.reset_mock()\n", "\n", "                p['port'][portbindings.HOST_ID] = HOST\n", "                bindings = [models.PortBindingLevel()]\n", "\u001b[0m"]}], "source": ["xpath = \"/mnt/efs/augment/user/dxy/datasets/edit.local/edit-data-12-03.try/013-7175.json\"\n", "json_dict = utils_for_file.read_json(xpath)\n", "edit_data = utils_for_dataclass.create_from_dict(SimpleRawEditScope, json_dict)\n", "edit_data.show_code_w_updated_code_and_instruction(max_context_lines=20)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}