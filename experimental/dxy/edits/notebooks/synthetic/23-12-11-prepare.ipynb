{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyspark.sql.functions as F\n", "from pyspark.sql.functions import rand\n", "from research.data.spark import k8s_session\n", "\n", "spark = k8s_session(\n", "    max_workers=100,\n", "    conf={\n", "        \"spark.sql.parquet.columnarReaderBatchSize\": \"128\",\n", "        \"spark.sql.execution.arrow.maxRecordsPerBatch\": \"128\",\n", "        \"spark.task.maxFailures\": \"10\",\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files_df = spark.read.parquet(\n", "    \"s3a://augment-github/starcoder_format/sorted_by_id_2023-09-30/\"\n", ")\n", "py_files_df = (\n", "    files_df.select(\n", "        F.col(\"max_stars_repo_name\").alias(\"repo\"),\n", "        F.col(\"max_stars_repo_path\").alias(\"file_path\"),\n", "        F.col(\"id\").alias(\"file_sha\"),\n", "        <PERSON><PERSON>col(\"lang\"),\n", "        <PERSON><PERSON>col(\"content\"),\n", "        <PERSON>.col(\"repo_size\"),\n", "    )\n", "    .filter(F.col(\"file_path\").endswith(\".py\"))\n", "    .filter(F.length(\"content\") < 1e6)\n", "    .withColumn(\"num_lines\", F.size(F.split(F.col(\"content\"), \"\\n\")))\n", ")\n", "py_files_df = py_files_df.filter(F.col(\"num_lines\") <= 600)\n", "print(f\"There are {py_files_df.count()} files.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split the file_path column to extract commit and path\n", "split_col = F.split(py_files_df[\"file_path\"], \":\")\n", "py_files_df = py_files_df.withColumn(\"commit\", split_col.getItem(0))\n", "py_files_df = py_files_df.withColumn(\"path\", split_col.getItem(1))\n", "# Construct the repo_url using the repo and commit columns\n", "py_files_df = py_files_df.withColumn(\n", "    \"repo_url\",\n", "    <PERSON><PERSON>concat(F.lit(\"https://github.com/\"), \"repo\", F.lit(\"/commit/\"), \"commit\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["subset_of_1m_files = py_files_df.orderBy(rand()).limit(1000_000)\n", "subset_of_1m_files.write.mode(\"overwrite\").parquet(\n", "    \"s3a://dxy-dev-bucket/edit/raw-github-1m-2023-09-30\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xdata = spark.read.parquet(\n", "    \"s3a://dxy-dev-bucket/edit/raw-github-1m-2023-09-30\"\n", ")\n", "xdata.repartition(10).write.mode(\"overwrite\").parquet(\n", "    \"s3a://dxy-dev-bucket/edit/raw-github-1m-2023-09-30\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xdata = spark.read.parquet(\n", "    \"s3a://dxy-dev-bucket/edit/raw-github-1m-2023-09-30\"\n", ")\n", "xdata.printSchema()\n", "pandas_df = xdata.toPandas()\n", "# Convert the Pandas DataFrame to a list of dictionaries\n", "list_of_dicts = pandas_df.to_dict(orient='records')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import random\n", "import pathlib\n", "from research.core import utils_for_file\n", "\n", "save_dir = pathlib.Path(\"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/\")\n", "save_dir.mkdir(exist_ok=True, parents=True)\n", "\n", "utils_for_file.write_jsonl_zst(\n", "    save_dir / \"all.jsonl.zst\",\n", "    list_of_dicts\n", ")\n", "random.shuffle(list_of_dicts)\n", "utils_for_file.write_jsonl_zst(\n", "    save_dir / \"1k.jsonl.zst\",\n", "    list_of_dicts[:1000]\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}