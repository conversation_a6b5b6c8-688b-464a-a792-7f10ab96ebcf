{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from research.core import utils_for_file, utils_for_dataclass\n", "from research.data.synthetic_code_edit import types\n", "from research.data.synthetic_code_edit import seed_lib\n", "from research.data.synthetic_code_edit import sampling_lib\n", "from experimental.dxy.edits.api_lib import generate_response_via_chat\n", "\n", "seeds = seed_lib.load_code_edit_seeds()\n", "seeds = {k: v for k, v in seeds.items() if len(v) > 0}\n", "print(f\"Loaded {len(seeds)} categories: {list(seeds.keys())}\")\n", "\n", "# raw_github_data = utils_for_file.read_jsonl_zst(\n", "#     \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k.jsonl.zst\"\n", "# )\n", "raw_github_data = [\n", "    utils_for_dataclass.create_from_dict(types.CodeEditData, x)\n", "    for x in utils_for_file.read_jsonl_zst(\n", "        \"/mnt/efs/augment/user/dxy/datasets/edit.local/raw-github-1m-2023-09-30/1k-in-codeedit.jsonl.zst\"\n", "    )\n", "]\n", "print(f\"Loaded {len(raw_github_data)} examples.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category = \"Formatting-Cleaning\"\n", "cur_seeds = [x for x in seeds[category] if x.metadata[\"language\"] == \"python\"]\n", "print(f\"Find {len(cur_seeds)}/{len(seeds[category])} python seeds for {category}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for x in cur_seeds:\n", "#     print(x.instructions)\n", "# print(raw_github_data[0].get_pretty_diff_code(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate the Inverse Instruction\n", "from experimental.dxy.edits.pipelines.generate_inverse_instruction import (\n", "    GenerateInverseInstructionFewShot,\n", ")\n", "\n", "target_sample = raw_github_data[0]\n", "pipeline_generate_inverse_instruction = GenerateInverseInstructionFewShot(\n", "    DEBUG=True, num_samples_from_seed=4, num_of_completion=5\n", ")\n", "(\n", "    samples_w_reverse_instruction,\n", "    meta_info_1,\n", ") = pipeline_generate_inverse_instruction.generate(\n", "    target_sample, cur_seeds, [],\n", "    # model=\"gpt-4-0314\"\n", ")\n", "print(f\"There are {len(samples_w_reverse_instruction)} reverse instructions\")\n", "\n", "# print(sample_w_reverse_instruction.get_pretty_diff_code(5))\n", "# for message in meta_info_1[\"messages\"]:\n", "#     print(message)\n", "#     print(\"-\" * 100)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(samples_w_reverse_instruction[2].get_pretty_diff_code(5))\n", "\n", "# for response in meta_info_1[\"responses\"]:\n", "#     print(response)\n", "#     print(\"-\" * 100)\n", "# print(meta_info_1[\"responses\"][0])\n", "# print(meta_info_1[\"responses\"][1])\n", "# print(meta_info_1[\"responses\"][2])\n", "# # print(meta_info_1[\"messages\"][0])\n", "\n", "# for message in meta_info_1[\"messages\"]:\n", "#     print(message)\n", "#     print(\"-\" * 100)\n", "\n", "# print(sample_w_reverse_instruction.get_pretty_diff_code(10))\n", "\n", "# print(meta_info_1[\"messages\"][0])\n", "# results = generate_response_via_chat(\n", "#     meta_info_1[\"messages\"],\n", "#     meta_info_1[\"system_prompt\"],\n", "#     temperature=1.0,\n", "#     num_completion=1,\n", "#     model=\"gpt-4-0613\",\n", "#     # model=\"gpt-4-1106-preview\",\n", "#     use_json=False,\n", "# )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate the selected code\n", "from experimental.dxy.edits.pipelines.generate_selected_code import (\n", "    GenerateSelectedCode,\n", ")\n", "\n", "pipeline_generate_selected_code = GenerateSelectedCode(DEBUG=True, num_samples_from_seed=3, num_of_completion=1)\n", "[sample_w_selected_code], meta_info_2 = pipeline_generate_selected_code.generate(\n", "    samples_w_reverse_instruction[0], cur_seeds, []\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sample_w_selected_code.get_pretty_diff_code(5))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for x in meta_info_2[\"messages\"]:\n", "#     print(x)\n", "#     print(\"-\" * 100)\n", "# # results = generate_response_via_chat(\n", "# #     meta_info_2[\"messages\"],\n", "# #     system_prompt=meta_info_2[\"system_prompt\"],\n", "# #     temperature=0.5,\n", "# #     max_tokens=512,\n", "# #     model=\"gpt-4-0613\")\n", "# # print(results[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(sample_w_selected_code.get_pretty_diff_code(16))"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[GenerateInstructionViaSUCodeRI.sample_samples] 4 -> 4 -> 3 samples ((0, 1, 3)) (num=3, keys_to_check=('prefix', 'selected_code', 'suffix', 'updated_code', 'inverse_instructions')).\n", "[GenerateInstructionViaSUCodeRI.sample_samples] 0 -> 0 -> 0 samples (()) (num=1, keys_to_check=('prefix', 'selected_code', 'suffix', 'updated_code', 'inverse_instructions')).\n"]}], "source": ["# Generate the instruction\n", "from experimental.dxy.edits.pipelines.generate_selected_code import (\n", "    GenerateInstructionViaSUCodeRI,\n", ")\n", "\n", "pipeline_generate_instruction = GenerateInstructionViaSUCodeRI(\n", "    DEBUG=True, num_samples_from_seed=3, num_of_completion=1\n", ")\n", "[sample_w_annotation], meta_info_3 = pipeline_generate_instruction.generate(\n", "    sample_w_selected_code, cur_seeds, []\n", ")"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Instructions:\n", "- Instruction(text=\"Replace the import paths to match Django's structure\", tags=None)\n", "\n", "Inverse Instructions:\n", "- Instruction(text='Replace the import statements with relative imports where possible', tags=None)\n", "\n", "\u001b[34m\n", "from corehq.apps.hqcase.management.commands.ptop_reindexer_v2 import FACTORIES_BY_SLUG\n", "\n", "from corehq.pillows.utils import get_all_expected_es_indices\n", "\n", "from corehq.elastic import get_es_new\n", "\n", "from cStringIO import StringIO\n", "import traceback\n", "from datetime import datetime\n", "\u001b[0m<<<<<<<\n", "\u001b[31mfrom ...core.mail import mail_admins\n", "from ...pillows.user import add_demo_user_to_user_index\n", "import gevent\n", "from ...core.management.base import BaseCommand\n", "from ...conf import settings\n", "\u001b[0m=======\n", "\u001b[32mfrom django.core.mail import mail_admins\n", "from corehq.pillows.user import add_demo_user_to_user_index\n", "import gevent\n", "from django.core.management.base import BaseCommand\n", "from django.conf import settings\n", "\u001b[0m>>>>>>>\n", "\u001b[34m\n", "\n", "def get_reindex_commands(alias_name):\n", "    # pillow_command_map is a mapping from es pillows\n", "    # to lists of management commands or functions\n", "    # that should be used to rebuild the index from scratch\n", "    pillow_command_map = {\n", "        'hqdomains': ['domain'],\n", "        'hqcases': ['case', 'sql-case'],\n", "        'xforms': ['form', 'sql-form'],\n", "\u001b[0m\n"]}], "source": ["print(sample_w_annotation.get_pretty_diff_code(10))"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/plain": ["'6ae2006b2a823c4733054de0fc2de17107f26617:corehq/apps/hqcase/management/commands/ptop_preindex.py'"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["sample_w_annotation.file_name"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}