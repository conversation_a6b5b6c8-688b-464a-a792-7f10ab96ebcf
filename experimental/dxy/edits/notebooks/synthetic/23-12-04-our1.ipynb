{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Visualize the Generated Data (V0.0)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 25767 edit data with instructions.\n", "skip_due_to_no_blocks=100\n", "skip_due_to_multiple_blocks=287\n", "skip_due_to_merge_conflict=46\n"]}], "source": ["import pathlib\n", "import json\n", "import re\n", "from research.core import utils_for_dataclass, utils_for_file, utils_for_str\n", "from experimental.dxy.edits.data_type import EditData\n", "\n", "def detect_merge_conflict(input_str) -> bool:\n", "    pattern1 = re.compile(r\"<<<<<<< (HEAD|[\\da-fA-F]{10+})\\n\")\n", "    pattern2 = re.compile(r\">>>>>>> (HEAD|[\\da-fA-F]{10+})\\n\")\n", "\n", "    match1 = pattern1.search(input_str)\n", "    if match1:\n", "        return True\n", "    match2 = pattern2.search(input_str)\n", "    if match2:\n", "        return True\n", "    return False\n", "\n", "# Load our in-house dataset\n", "file_names = (\n", "    \"instruct-del_00_00-per40.json\",\n", "    \"instruct-del_01_01-per40.json\",\n", "    \"instruct-del_02_02-per40.json\",\n", "    \"instruct-del_03_03-per40.json\",\n", "    \"instruct-del_04_04-per40.json\",\n", "    \"instruct-del_05_05-per40.json\",\n", "    \"instruct-del_06_06-per40.json\",\n", "    \"instruct-del_07_07-per40.json\",\n", "    \"instruct-del_08_08-per40.json\",\n", "    \"instruct-del_09_09-per40.json\",\n", "    \"instruct-del_10_10-per40.json\",\n", "    \"instruct-del_11_20-per10.json\",\n", "    \"instruct-del_21_30-per10.json\",\n", ")\n", "json_file_dir = (\n", "    \"/mnt/efs/augment/user/dxy/datasets/edit.local/organized-basic-instruct-2023-10-13/\"\n", ")\n", "\n", "\n", "edit_data_in_house = []\n", "skip_due_to_no_blocks = []\n", "skip_due_to_multiple_blocks = []\n", "skip_due_to_merge_conflict = []\n", "\n", "for json_file in file_names:\n", "    json_file_path = pathlib.Path(json_file_dir) / json_file\n", "    with json_file_path.open(\"r\") as f:\n", "        for line in f:\n", "            xdict = json.loads(line)\n", "            data = utils_for_dataclass.create_from_dict(EditData, xdict[\"edit_data\"])\n", "            instruction = xdict[\"instruction\"]\n", "            data.instruction = instruction\n", "            blocks = utils_for_str.extract_code_within_backticks(instruction)\n", "            if len(blocks) == 0:\n", "                # print(f\"No blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_no_blocks.append(data)\n", "                continue\n", "            elif len(blocks) > 1:\n", "                # print(f\"Too many blocks for this instruction:\\n{instruction}\")\n", "                skip_due_to_multiple_blocks.append(data)\n", "                continue\n", "            final_instruction = blocks[0]\n", "            if final_instruction[0] == '\"' and final_instruction[-1] == '\"':\n", "                final_instruction = final_instruction[1:-1]\n", "            elif final_instruction[0] == \"`\" and final_instruction[-1] == \"`\":\n", "                final_instruction = final_instruction[1:-1]\n", "            data.instruction = final_instruction\n", "            if detect_merge_conflict(data.new_file_content) or detect_merge_conflict(\n", "                data.old_file_content\n", "            ):\n", "                skip_due_to_merge_conflict.append(data)\n", "                continue\n", "            edit_data_in_house.append(data)\n", "print(f\"There are {len(edit_data_in_house)} edit data with instructions.\")\n", "print(f\"skip_due_to_no_blocks={len(skip_due_to_no_blocks)}\")\n", "print(f\"skip_due_to_multiple_blocks={len(skip_due_to_multiple_blocks)}\")\n", "print(f\"skip_due_to_merge_conflict={len(skip_due_to_merge_conflict)}\")\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Add two more buttons for Silver_Armour and Gold_Armour\n", "[ ]     B_Shop_armors_armor5.pack()    \n", "[ ]     B_Shop_armors_armor6= But<PERSON>(frame_shop_armors_no, text=\"BunSamosa_Armour\", command=lambda: shop_armor_armor6())\n", "[ ]     B_Shop_armors_armor6.pack()\n", "[ ]     B_Shop_armors_armor7 = Button(frame_shop_armors_no, text=\"ACM_Armour\", command=lambda: shop_armor_armor7())\n", "[ ]     B_Shop_armors_armor7.pack()\n", "[+]     B_Shop_armors_armor8 = Button(frame_shop_armors_no, text=\"Silver_Armour\", command=lambda: shop_armor_armor8())\n", "[+]     B_Shop_armors_armor8.pack()\n", "[+]     B_Shop_armors_armor9 = Button(frame_shop_armors_no, text=\"Gold_Armour\", command=lambda: shop_armor_armor9())\n", "[+]     B_Shop_armors_armor9.pack()\n", "[ ]     B_Shop_armors_back = Button(frame_shop_armors_no, text=\"back\", command=lambda: shop_armor_to_main())\n", "[ ]     B_Shop_armors_back.pack(side=BOTTOM)\n", "[ ] \n", "[ ] def shop_armor_armor1():\n", "[ ]     global Iron_Armour\n", "\n"]}], "source": ["index = 5\n", "\n", "print(edit_data_in_house[index].instruction)\n", "print(edit_data_in_house[index].get_diff_code_in_context(5, 5))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}