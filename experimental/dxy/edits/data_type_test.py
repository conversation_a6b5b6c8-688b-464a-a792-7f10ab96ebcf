"""The unit test for the data_type.py file.

pytest experimental/dxy/edits/data_type_test.py
"""
import dataclasses
import json
import typing
import unittest

from experimental.dxy.edits import data_type
from research.core import utils_for_dataclass


def create_edit_data(lines: typing.List[data_type.Line]):
    data = data_type.EditData(
        repo="",
        commit_sha="",
        file_path="",
        lines=lines,
        old_file_content="",
        new_file_content="",
        old_hunk_range=(0, 0),
        new_hunk_range=(1, 1),
    )
    return data


class TestDataType(unittest.TestCase):
    """Test the Data Type."""

    def test_edit_data_type(self):
        """Test different get functions."""
        data = create_edit_data(
            [
                data_type.Line("a\n", " "),
                data_type.Line("b\n", "-"),
                data_type.Line("c\n", "+"),
            ]
        )
        self.assertEqual(data.get_diffs(), (1, 1))
        self.assertEqual(data.get_selected_code(), "b\n")
        self.assertEqual(data.get_modified_code(), "c\n")
        data.new_hunk_range = (2, 4)
        self.assertEqual(data.get_lrange(), (2, 3))
        data.new_file_content = "xx\na\nc\n"
        self.assertEqual(data.get_prefix_via_lrange(data.get_lrange()), "xx\na\n")
        self.assertEqual(data.get_suffix_via_lrange(data.get_lrange()), "")

        data = create_edit_data(
            [
                data_type.Line("1\n", " "),
                data_type.Line("2\n", "-"),
                data_type.Line("3\n", "+"),
                data_type.Line("4\n", " "),
                data_type.Line("5\n", "-"),
                data_type.Line("6\n", "+"),
                data_type.Line("7\n", " "),
                data_type.Line("8\n", " "),
            ]
        )
        self.assertEqual(data.get_diffs(), (3, 3))
        self.assertEqual(data.get_selected_code(), "2\n4\n5\n")
        self.assertEqual(data.get_modified_code(), "3\n4\n6\n")

        data_str = json.dumps(dataclasses.asdict(data), indent=2)
        deserialized_data = utils_for_dataclass.create_from_json(
            data_type.EditData, data_str
        )
        self.assertEqual(data, deserialized_data)
        self.assertEqual(data.get_diff_code_in_context(0, 0), data.get_diff_code())
        self.assertEqual(data.commit_sha, deserialized_data.commit_sha)
        self.assertEqual(data.file_path, deserialized_data.file_path)
        self.assertEqual(data.lines, deserialized_data.lines)
        self.assertEqual(data.old_file_content, deserialized_data.old_file_content)
