"""Visualization library."""
import pathlib
import typing

import matplotlib
import matplotlib.pyplot as plt
import tqdm

from research.data.synthetic_code_edit.types import CodeEditData

matplotlib.use("Agg")


def save_histogram(
    data: dict[str, list[int]],
    filepath: typing.Union[str, pathlib.Path] = "histogram.pdf",
    bins_range: int = 10,
):
    """Generates and saves a histogram of the distribution of a list of integers as a PDF file.

    :param data: Dictionary with keys as titles and values as lists of integers to be visualized.
    :param filepath: Name of the PDF file to save the histogram.
    :param bins_range: The range size for each bin in the histogram.
    :return: None
    """

    # Number of subplots (one for each key in the dictionary)
    num_plots = len(data)
    fig, axs = plt.subplots(num_plots, figsize=(10, 4 * num_plots))

    # Make sure axs is an array even if there's only one plot
    if num_plots == 1:
        axs = [axs]

    for (key, values), ax in zip(data.items(), axs):
        # Define the ranges for the x-axis
        max_value = max(values) + bins_range
        bins = range(0, max_value, bins_range)

        # Creating the histogram
        ax.hist(values, bins=bins, edgecolor="black")

        # Adding labels and title
        ax.set_xlabel("Range of Integers")
        ax.set_ylabel("Frequency")
        ax.set_title(f"Distribution of '{key}'")
    # Adjust layout to prevent overlapping
    plt.tight_layout()

    # Save the plot as a PDF file
    file_dir = pathlib.Path(filepath).parent
    file_dir.mkdir(parents=True, exist_ok=True)
    plt.savefig(str(filepath))

    # Close the plot to free memory
    plt.close()


def plot_edit_scope(
    data: list[CodeEditData],
    filepath: typing.Union[str, pathlib.Path],
    bins_range: int = 1,
):
    """Plot the edit scope."""
    data_for_plot = {}
    # Compute the selected lines
    selected_lines = []
    for x in data:
        selected_code = x.selected_code
        if selected_code is not None:
            selected_lines.append(len(selected_code.splitlines(keepends=True)))
    if not selected_lines:
        print("No selected code")
    else:
        data_for_plot["#selected lines"] = selected_lines
    # Compute the updated lines
    updated_lines = []
    for x in data:
        updated_code = x.updated_code
        if updated_code is not None:
            updated_lines.append(len(updated_code.splitlines(keepends=True)))
    if not updated_lines:
        print("No updated code")
        data_for_plot["#updated lines"] = []
    else:
        data_for_plot["#updated lines"] = updated_lines
    data_for_plot["#prefix"] = []
    data_for_plot["#suffix"] = []
    for x in tqdm.tqdm(data, total=len(data), desc="Plotting the edit scope"):
        data_for_plot["#prefix"].append(len(x.prefix.splitlines(keepends=True)))
        data_for_plot["#suffix"].append(len(x.suffix.splitlines(keepends=True)))
    save_histogram(data_for_plot, filepath, bins_range)
    print(f"Finish plotting the edit scope into {filepath}")
