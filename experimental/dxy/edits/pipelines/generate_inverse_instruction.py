"""The pipeline to generate the reverse instruction."""
import copy
import dataclasses
import json
import random
import typing

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from experimental.dxy.edits.pipelines import Pipeline
from research.core import utils_for_str
from research.data.synthetic_code_edit.types import CodeEditData, Instruction
from research.data.synthetic_code_edit.util_lib import create_code_with_leading_brackets

SYSTEM_PROMPT_ZEROSHOT = r"""Context: A Python function 'edit' is defined as 'def edit(prefix: str, selected_code: str, suffix: str, instruction: str) -> str'. This function modifies 'selected_code' based on the 'instruction' argument and returns 'updated_code'. The modification heavily depends on the provided 'instruction'.

Task:
Examine the given 'Prefix', 'Selected Code', 'Suffix', and 'instruction'. Determine the most likely 'inverse_instruction' such that:
- Applying edit(prefix, updated_code, suffix, inverse_instruction) will revert 'updated_code' back to the original 'selected_code'.
- The user's message to you will be in the json format with keys 'prefix', 'selected_code', 'suffix', 'instruction', and 'updated_code'.
- Produce your thinking procedure in 2-3 brief sentences and the final inverse_instruction.
- The output should be in the json format with keys 'thinking_procedure' and 'inverse_instruction'.

Here are some examples of the 'instruction' and 'inverse_instruction':
- 'Refactor for better readability' and 'Obfuscate the code'
- 'Optimize for performance' and 'Introduce non-critical inefficiencies'
- 'Add detailed comments' and 'Remove comments'
- 'Replace double quotes with single quote' and 'Replace single quote with double quotes'
- 'Standardize naming conventions' and 'Use inconsistent naming conventions'
"""


@dataclasses.dataclass
class GenerateReverseInstructionZeroShot(Pipeline):
    """The pipeline to generate the instructions."""

    max_context_lines: int = 80
    num_of_completion: int = 1

    def generate(
        self, target_sample: CodeEditData
    ) -> typing.Tuple[typing.List[CodeEditData], typing.Any]:
        """Generate the data."""
        # The samples to be used
        messages = []
        # The sample to be generated
        json_dict = {}
        assert target_sample.instructions is not None
        json_dict["prefix"] = target_sample.prefix
        json_dict["selected_code"] = target_sample.selected_code
        json_dict["suffix"] = target_sample.suffix
        json_dict["instruction"] = random.choice(target_sample.instructions).text
        json_dict["updated_code"] = target_sample.updated_code
        messages.append(json.dumps(json_dict, indent=2))
        responses = generate_response_via_chat(
            messages,
            SYSTEM_PROMPT_ZEROSHOT,
            temperature=1.0,
            max_tokens=512,
            num_completion=self.num_of_completion,
            model="gpt-4-0613",
            use_json=True,
        )
        final_results: typing.List[CodeEditData] = []
        for response in responses:
            try:
                json_response = json.loads(response)
                thinking_procedure = json_response["thinking_procedure"]
                inverse_instruction = json_response["inverse_instruction"]
            except json.JSONDecodeError:
                print(f"Failed to decode the response: {response}")
                thinking_procedure = response
                inverse_instruction = None
            final_result = copy.deepcopy(target_sample)
            final_result.metadata[
                "thinking_procedure@inverse_instruction"
            ] = thinking_procedure
            final_result.metadata["inverse_instruction"] = inverse_instruction
            final_results.append(final_result)
        return final_results, messages


@dataclasses.dataclass
class GenerateInverseInstructionFewShot(Pipeline):
    """The pipeline to generate the instructions."""

    num_samples_from_seed: int = 2
    max_context_lines: int = 32

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "updated_code",
        "suffix",
        "inverse_instructions",
    )

    SYSTEM_PROMPT: typing.Optional[
        str
    ] = """You are an powerful AI programming assistant and follow the user's requirements carefully and to the letter. Always think step by step and never rush."""

    def __post_init__(self):
        super().__post_init__()

    def _build_init_prompt(self) -> str:
        text = """Assume an AI assistant can update a specific range of codes following the user's instruction.
That specific range of codes (user's selected code) are highlighted with leading [x] and sorrunding contexts are marked with leading [ ].
The AI assistant can only modify the selected code by the instruction, the rest of the code will remain unchanged and mostly used as context.
Now, given the codes, you need to help me produce the most possible instruction.

We will start with some examples and the last one is the target example.\n
"""
        return text

    def _build_sample(self, sample: CodeEditData, index: int) -> str:
        assert sample.updated_code is not None
        assert sample.inverse_instructions is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        inverse_instruction = self._random_choice(sample.inverse_instructions).text
        return rf"""# The {index}-th example

Code:
```
{create_code_with_leading_brackets(prefix, updated_code, suffix)}```

Instruction:
```
{inverse_instruction}
```

"""

    def _build_target_sample(
        self, sample: CodeEditData, category: typing.Optional[str]
    ) -> str:
        assert sample.updated_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        requirements = [
            "- Focus on the selected codes (where each line has a leading [x]) and other codes (with leading [ ]) is just to help u better understand the context.",
            "- The potential instruction will only be applied to the selected code (which has leading [x]), so think of a reasonable instruction.",
        ]
        if category is not None:
            requirements.append(
                f"- The instructions should belong to the category of {category} similar to the above examples."
            )
        requirements.extend(
            [
                "- Do not try to apply the edit instruction to the context codes.",
                "- Please replay the selected codes to remind yourself about its content.",
                "- You can also replay the instructions in the provided examples and follow their styles.",
                "- Be mindful that the instruction can be super diverse from enhancing the code to damaging the codes or introducing bugs, so that even if the current codes look good, you can be creative on the potential instruction.",
            ]
        )
        requirements_str = "\n".join(requirements)
        if category is not None:
            tail_of_prompt = f"Based on the structure and content of the code, an appropriate instruction in the category of {category} could be:"
        else:
            tail_of_prompt = "Based on the structure and content of the code, an appropriate instruction could be:"
        return rf"""# The target example
Code:
```
{create_code_with_leading_brackets(prefix, updated_code, suffix)}```

Please try your best to select the category that best fits the instruction if possible or suggest a new category if none of the provided ones fit.
Remember:
{requirements_str}

Sure, the selected code for the target example is as follows:
```
{updated_code}```
{tail_of_prompt}
"""

    def _parse_target(self, response: str) -> str:
        answer = utils_for_str.extract_the_last_markdown_block(response)
        if answer is None:
            return response
        return answer

    def generate(
        self,
        target_sample: CodeEditData,
        model: OpenAIChatModels = "gpt-4-1106-preview",
        category: typing.Optional[str] = None,
    ) -> typing.Tuple[CodeEditData, typing.Any]:
        """Generate the data."""
        if category is not None:
            seed_samples = self.seeds_by_category[category]
        else:
            seed_samples = self.SEED_EXAMPLES
        in_context_samples = self.sample_samples(
            seed_samples, self.num_samples_from_seed
        )
        random.shuffle(in_context_samples)
        assert len(in_context_samples) > 0

        # The samples to be used
        message = self._build_init_prompt()
        for index, sample in enumerate(in_context_samples):
            message += self._build_sample(sample, index)
        message += self._build_target_sample(target_sample, category)
        [response] = generate_response_via_chat(
            [message],
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model=model,
            max_tokens=512,
            use_json=False,
        )
        messages = [
            message,
            response,
            "Super great! Please directly produce the instruction without any introductory text.",
        ]
        [response_v2] = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model="gpt-3.5-turbo-1106",
            max_tokens=512,
            use_json=False,
        )
        final_result = copy.deepcopy(target_sample)
        final_result.inverse_instructions = [
            Instruction(self._parse_target(response_v2), None)
        ]
        final_result.metadata["auxiliary@inverse_instruction@response"] = response
        final_result.metadata["auxiliary@inverse_instruction@response_v2"] = response_v2
        return final_result, {
            "in_context_samples": in_context_samples,
            "messages": [message],
            "system_prompt": self.SYSTEM_PROMPT,
            "responses": [response, response_v2],
        }
