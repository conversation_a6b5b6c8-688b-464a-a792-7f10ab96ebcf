"""The meta definition of the data generation pipeline."""
import abc
import dataclasses
import random
import typing

from experimental.dxy.edits.api_lib import OpenAIChatModels
from research.data.synthetic_code_edit.types import CodeEditData


def code_edit_data_get_key(sample: CodeEditData, key: str):
    if hasattr(sample, key):
        return getattr(sample, key)
    elif key in sample.metadata:
        return sample.metadata[key]
    else:
        return None


@dataclasses.dataclass
class Pipeline:
    """The pipeline template used to synthetically generate data."""

    DEBUG: bool = False
    REQUIRED_KEYS: typing.Tuple[str, ...] = ()
    SEED_EXAMPLES: typing.Tuple[CodeEditData, ...] = ()

    def __post_init__(self):
        seeds_by_category = {}
        for seed in self.SEED_EXAMPLES:
            if "category" not in seed.metadata:
                continue
            category = seed.metadata["category"]
            if category not in seeds_by_category:
                seeds_by_category[category] = []
            seeds_by_category[category].append(seed)
        self.seeds_by_category = seeds_by_category
        self.categories = tuple(seeds_by_category.keys())
        if self.DEBUG:
            print(
                f"[{self.__class__.__name__}.init] Loaded {len(self.categories)} categories: {self.categories}"
            )
            for category, seeds in self.seeds_by_category.items():
                print(
                    f"[{self.__class__.__name__}.init] {category:<20}: {len(seeds)} seed examples"
                )

    def _check_required_keys(
        self, sample: CodeEditData, keys: typing.Optional[typing.Tuple[str, ...]]
    ):
        """Check if the given sample has the required keys."""
        if keys is None:
            keys = self.REQUIRED_KEYS
        for key in keys:
            if code_edit_data_get_key(sample, key) is None:
                return False
        return True

    def _random_choice(self, choices: typing.Optional[typing.List[typing.Any]] = None):
        """Randomly select an element from the given list."""
        if choices is None:
            raise ValueError("choices must be provided.")
        if len(choices) == 0:
            raise ValueError("choices must not be empty.")
        return random.choice(choices)

    def sample_samples(
        self,
        samples: typing.Sequence[CodeEditData],
        num: int,
        must_has_keys: typing.Optional[typing.Tuple[str, ...]] = None,
    ) -> list[CodeEditData]:
        """Get samples from the given list."""
        if must_has_keys is None:
            keys_to_check = self.REQUIRED_KEYS
        else:
            keys_to_check = must_has_keys
        samples_to_be_selected = []
        for sample in samples:
            has_all_key = self._check_required_keys(sample, keys_to_check)
            if has_all_key:
                samples_to_be_selected.append(sample)
        final_samples = random.sample(
            samples_to_be_selected, min(num, len(samples_to_be_selected))
        )
        if self.DEBUG:
            indexes = tuple([samples.index(sample) for sample in final_samples])
            print(
                f"[{self.__class__.__name__}.sample_samples] {len(samples)}"
                f" -> {len(samples_to_be_selected)} -> {len(final_samples)}"
                f" samples ({indexes}) (num={num}, keys_to_check={keys_to_check})."
            )
        return final_samples

    @abc.abstractmethod
    def generate(
        self,
        target_sample: CodeEditData,
        seed_samples: typing.List[CodeEditData],
        machine_generated_samples: typing.List[CodeEditData],
        model: OpenAIChatModels = "gpt-4-1106-preview",
    ) -> typing.Tuple[typing.List[CodeEditData], typing.Any]:
        """Generate the data via this pipeline."""
