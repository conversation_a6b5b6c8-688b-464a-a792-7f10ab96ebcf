"""The pipeline to generate the selected code."""
import copy
import dataclasses
import random
import typing

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from experimental.dxy.edits.pipelines import Pipeline
from research.core import utils_for_str
from research.data.synthetic_code_edit.types import CodeEditData, Instruction
from research.data.synthetic_code_edit.util_lib import (
    create_code_with_leading_brackets,
    create_diff_with_leading_brackets,
)


@dataclasses.dataclass
class GenerateInstructionV0(Pipeline):
    """The pipeline to generate the instruction via the selected code, updated code, and the reverse instruction."""

    num_samples_from_seed: int = 2
    max_context_lines: int = 40

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "selected_code",
        "suffix",
        "updated_code",
        "instructions",
    )

    SYSTEM_PROMPT: str = r"""You are an powerful AI programming assistant and follow the user's requirements carefully and to the letter. Always think step by step and never rush."""

    def __post_init__(self):
        return super().__post_init__()

    def _build_init_prompt(self) -> str:
        return r"""Assume an AI assistant can update a specific range of codes following the user's instruction.
That specific range of codes (target code) are highlighted with leading [x] and sorrunding contexts are marked with leading [ ].
The AI assistant can only modify the target code by the instruction, the rest of the code will remain unchanged and mostly used as context.

"""

    def _build_sample(self, sample: CodeEditData, index: int) -> str:
        assert sample.updated_code is not None
        assert sample.instructions is not None
        assert sample.selected_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        instruction = self._random_choice(sample.instructions).text
        selected_code = sample.selected_code
        return rf"""# The {index}-th example

Code:
```
{create_code_with_leading_brackets(prefix, selected_code, suffix)}```

The updated version of the highlighted code:
```
{create_code_with_leading_brackets('', updated_code, '')}```

Instruction:
```
{instruction}
```
"""

    def _build_target_sample(
        self, sample: CodeEditData, category: typing.Optional[str]
    ) -> str:
        assert sample.updated_code is not None
        assert sample.selected_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        selected_code = sample.selected_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        requirements = [
            "- Focus on the target codes (where each line has a leading [x]) and other codes (with leading [ ]) is just to help u better understand the context.",
            "- The potential instruction will only be applied to the target code (which has leading [x]), so think of a reasonable instruction.",
        ]
        if category is not None:
            requirements.append(
                f"- The instructions should belong to the category of {category} similar to the above examples."
            )
        requirements.extend(
            [
                "- Do not try to apply the edit instruction to the context codes.",
                "- You can replay the updated codes to remind yourself about its content.",
                "- Be mindful that the instruction can be super diverse from enhancing the code to damaging the codes or introducing bugs, so that even if the current codes look good, you can be creative on the potential instruction.",
            ]
        )
        requirements_str = "\n".join(requirements)
        return rf"""# The target example
Code:
```
{create_code_with_leading_brackets(prefix, selected_code, suffix)}```

The updated version of the highlighted code:
```
{create_code_with_leading_brackets('', updated_code, '')}```

Please try your best to produce the most appropriate instruction that modify the selected code to the updated code.
Remember:
{requirements_str}

Sure, if we zoom in to look at the differences between the selected code and the updated code, you can see that the highlighted code is changed to the updated code.
```
{create_diff_with_leading_brackets(selected_code=selected_code, updated_code=updated_code, prefix='', suffix='')}```
"""

    def generate(
        self,
        target_sample: CodeEditData,
        model: OpenAIChatModels = "gpt-4-1106-preview",
        category: typing.Optional[str] = None,
    ) -> typing.Tuple[CodeEditData, typing.Any]:
        """Generate the data."""
        if category is not None:
            seed_samples = self.seeds_by_category[category]
        else:
            seed_samples = self.SEED_EXAMPLES
        in_context_samples = self.sample_samples(
            seed_samples, self.num_samples_from_seed
        )
        random.shuffle(in_context_samples)
        assert len(in_context_samples) > 0
        # Build the messages
        message = self._build_init_prompt()
        for index, sample in enumerate(in_context_samples):
            message += self._build_sample(sample, index)
        message += self._build_target_sample(target_sample, category)
        # Call the OpenAI API
        [response] = generate_response_via_chat(
            [message],
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model=model,
            use_json=False,
        )
        messages = [
            message,
            response,
            "Compare the instructions in the provided examples, do you feel this instruction is too verbose? If so, try your best to simplify the instruction following their styles.",
        ]
        [response_v2] = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model=model,
            max_tokens=512,
            use_json=False,
        )
        messages.extend(
            [
                response_v2,
                "Super great! Can you simplify your answer into just the instruction without any introductory text? Please directly reply the instruction.",
            ]
        )
        [response_v3] = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model="gpt-3.5-turbo-1106",
            max_tokens=512,
            use_json=False,
        )
        final_result = copy.deepcopy(target_sample)
        final_result.metadata["auxiliary@instruction@response"] = response
        final_result.metadata["auxiliary@instruction@response_v2"] = response_v2
        final_result.instructions = [Instruction(response_v3, None)]
        return final_result, {
            "messages": [message],
            "system_prompt": self.SYSTEM_PROMPT,
            "responses": [response, response_v2],
        }
