"""The pipeline to generate both the instruction and the code."""
import copy
import dataclasses
import json
import random
import typing

from experimental.dxy.edits.api_lib import generate_response_via_chat
from experimental.dxy.edits.pipelines import Pipeline
from research.core import utils_for_str
from research.data.synthetic_code_edit.types import CodeEditData, Instruction


@dataclasses.dataclass
class GenerateInstructionAndSelection(Pipeline):
    """The pipeline to generate the instructions."""

    num_samples_from_seed: int = 4
    num_samples_from_machine: int = 2
    max_context_lines: int = 40

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "middle",
        "suffix",
        "updated_code",
    )
    SYSTEM_PROMPT: str = r"""Context: We have a Python function named 'edit' defined as 'def edit(prefix: str, selected_code: str, suffix: str, instruction: str) -> str', which takes four arguments and returns a modified string w.r.t. selected_code based on the 'instruction'. The function's behavior is highly influenced by the 'instruction' argument.

Objective: To deduce the missing 'instruction' and 'selected_code' that, when used with the known 'prefix' and 'suffix', results in the given 'updated_code'.

Provided Data in the json format:
1. Prefix (known)
2. Updated Code (known result after applying 'edit')
3. Suffix (known)

Task:
Analyze the provided 'Prefix', 'Updated Code', and 'Suffix' to infer the most likely 'instruction' and 'selected_code' that were used in the function 'edit' to be transformed in combination with 'Prefix' and 'Suffix' into the 'Updated Code'.

Notes:
1. Examine the differences and similarities between the 'Prefix', 'Updated Code', and 'Suffix'.
2. Consider what kind of transformation or operation represented by 'instruction' could lead to such a result.
3. Be creative on the instruction and consider diverse, practical, and reasonable instructions.
4. Identify the portion of the 'Updated Code' that likely represents the 'selected_code'.
5. Pay attention to any patterns, structural changes, or content modifications between the provided code segments.
6. Your analysis should logically connect the known elements (Prefix, Updated Code, Suffix) with the inferred 'instruction' and 'selected_code'.
7. Always output in a json format with the key of "instruction" and "selected_code".
"""

    def generate(
        self,
        target_sample: CodeEditData,
        seed_samples: typing.List[CodeEditData],
        machine_generated_samples: typing.List[CodeEditData],
    ) -> typing.Tuple[typing.List[CodeEditData], typing.Any]:
        """Generate the data."""
        seeds_to_use = self.sample_samples(seed_samples, self.num_samples_from_seed)
        machine_generated_samples_to_use = self.sample_samples(
            machine_generated_samples, self.num_samples_from_machine
        )
        in_context_samples = seeds_to_use + machine_generated_samples_to_use
        random.shuffle(in_context_samples)
        assert len(in_context_samples) > 0
        # The samples to be used
        messages = []
        for sample in in_context_samples:
            json_dict = {
                "prefix": utils_for_str.get_last_n_lines(
                    sample.prefix, self.max_context_lines
                ),
                "updated_code": sample.updated_code,
                "suffix": utils_for_str.get_first_n_lines(
                    sample.suffix, self.max_context_lines
                ),
            }
            json_str = json.dumps(json_dict, indent=2)
            messages.append(json_str)
            answer_dict = {
                "instruction": self._random_choice(sample.instructions).text,
                "selected_code": sample.selected_code,
            }
            answer_str = json.dumps(answer_dict, indent=2)
            messages.append(answer_str)
        # The sample to be generated
        json_str = json.dumps(
            {
                "prefix": utils_for_str.get_last_n_lines(
                    target_sample.prefix, self.max_context_lines
                ),
                "updated_code": target_sample.updated_code,
                "suffix": utils_for_str.get_first_n_lines(
                    target_sample.suffix, self.max_context_lines
                ),
            },
            indent=2,
        )
        messages.append(json_str)
        responses = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=1.0,
            num_completion=5,
            model="gpt-4-1106-preview",
            use_json=True,
        )
        final_results: typing.List[CodeEditData] = []
        for response in responses:
            json_response = json.loads(response)
            instruction = json_response["instruction"]
            selected_code = json_response["selected_code"]
            final_result = copy.deepcopy(target_sample)
            final_result.selected_code = selected_code
            final_result.instructions = [Instruction(instruction, None)]
            final_results.append(final_result)
        return final_results, messages
