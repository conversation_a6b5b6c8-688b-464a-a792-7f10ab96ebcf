"""The pipeline to generate the selected code."""
import copy
import dataclasses
import random
import typing

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from experimental.dxy.edits.pipelines import Pipeline
from research.core import utils_for_str
from research.data.synthetic_code_edit.types import CodeEditData
from research.data.synthetic_code_edit.util_lib import create_code_with_leading_brackets


@dataclasses.dataclass
class GenerateSelectedCode(Pipeline):
    """The pipeline to generate...."""

    num_samples_from_seed: int = 2
    max_context_lines: int = 40

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "selected_code",
        "suffix",
        "updated_code",
        "inverse_instructions",
    )

    SYSTEM_PROMPT: str = r"""You are an powerful AI programming assistant and follow the user's requirements carefully and to the letter. Always think step by step and never rush."""

    def __post_init__(self):
        super().__post_init__()

    def _build_init_prompt(self) -> str:
        return r"""Assume an AI assistant can update a specific range of codes following the user's instruction.
That specific range of codes (target code) are highlighted with leading [x] and sorrunding contexts are marked with leading [ ].
The AI assistant can only modify the target code by the instruction, the rest of the code will remain unchanged and mostly used as context.

"""

    def _build_sample(self, sample: CodeEditData, index: int) -> str:
        assert sample.updated_code is not None
        assert sample.inverse_instructions is not None
        assert sample.selected_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        inverse_instruction = self._random_choice(sample.inverse_instructions).text
        selected_code = sample.selected_code
        prefix_max5lines = utils_for_str.get_last_n_lines(sample.prefix, 5)
        return rf"""# The {index}-th example

Code:
```
{create_code_with_leading_brackets(prefix, updated_code, suffix)}```

Instruction:
```
{inverse_instruction}
```

The updated version of the highlighted code with with little preceding context before it:
```
{create_code_with_leading_brackets(prefix_max5lines, selected_code, '')}```

"""

    def _build_target_sample(self, sample: CodeEditData) -> str:
        assert sample.updated_code is not None
        assert sample.inverse_instructions is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        inverse_instruction = self._random_choice(sample.inverse_instructions).text
        requirements = [
            "- Focus on the target codes (where each line has a leading [x]) and other codes (with leading [ ]) is just to help u better understand the context.",
            "- The instruction will only be applied to the target code (which has leading [x]).",
            "- Do not try to apply the edit instruction to the context codes.",
            "- Do not change any other codes with leading [ ] as they are context and should not be changed."
            "- Still mark [x] for your updated code while keep [ ] for the context",
        ]
        requirements_str = "\n".join(requirements)
        prefix_max5lines = utils_for_str.get_last_n_lines(sample.prefix, 5)
        return rf"""# The target example
Code:
```
{create_code_with_leading_brackets(prefix, updated_code, suffix)}```

Instruction:
```
{inverse_instruction}
```

Please try your best to produce the most appropriate updated code that can align with the provided code and instruction.
Strictly following these requirements:
{requirements_str}

Sure, I understand your requirements, here is updated version of the highlighted code with little preceding context before it:
```
{create_code_with_leading_brackets(prefix_max5lines, '', '')}"""

    def _parse_for_target(
        self, response: str, sample: CodeEditData
    ) -> typing.Optional[str]:
        selected_code = utils_for_str.extract_the_last_markdown_block(response)
        if selected_code is None:
            return None
        if sample.updated_code is None:
            return None
        if sample.updated_code[-1] == "\n" and selected_code[-1] != "\n":
            selected_code += "\n"
        lines, trigger_x = [], False
        for line in selected_code.splitlines(keepends=True):
            if line.startswith("[x]"):
                xline = line[4:] if line.startswith("[x] ") else line[3:]
                lines.append(xline)
                trigger_x = True
            elif line.startswith("[ ]"):
                xline = line[4:] if line.startswith("[ ] ") else line[3:]
                if not trigger_x:
                    lines.append(xline)
                else:
                    continue  # skip
            else:
                lines.append(line)
        return "".join(lines)

    def generate(
        self,
        target_sample: CodeEditData,
        model: OpenAIChatModels = "gpt-4-1106-preview",
        category: typing.Optional[str] = None,
    ) -> typing.Tuple[CodeEditData, typing.Any]:
        """Generate the data."""
        if category is not None:
            seed_samples = self.seeds_by_category[category]
        else:
            seed_samples = self.SEED_EXAMPLES
        in_context_samples = self.sample_samples(
            seed_samples, self.num_samples_from_seed
        )
        random.shuffle(in_context_samples)
        assert len(in_context_samples) > 0
        # Build the messages
        message = self._build_init_prompt()
        for index, sample in enumerate(in_context_samples):
            message += self._build_sample(sample, index)
        message += self._build_target_sample(target_sample)
        # Call the OpenAI API
        [response] = generate_response_via_chat(
            [message],
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            max_tokens=1024,
            model=model,
            use_json=False,
        )
        final_result = copy.deepcopy(target_sample)
        final_result.selected_code = self._parse_for_target(response, target_sample)
        final_result.metadata["auxiliary@selected_code@response"] = response
        final_result.metadata["auxiliary@selected_code@message"] = message
        return final_result, {
            "in_context_samples": in_context_samples,
            "messages": [message],
            "system_prompt": self.SYSTEM_PROMPT,
            "responses": [response],
        }
