"""Simple functions to generate the reverse instructions."""
import copy
import random

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from research.core import utils_for_str
from research.data.synthetic_code_edit import sampling_lib, util_lib
from research.data.synthetic_code_edit.types import CodeEditData


def post_process_selected_code(
    data: CodeEditData,
    context_lengths: tuple[int, int],
    selected_code_with_marker: str,
) -> tuple[CodeEditData, bool]:
    """Post-process the generated selected code."""
    data = copy.deepcopy(data)
    prefix_lines = data.prefix.splitlines(keepends=True)
    suffix_lines = data.suffix.splitlines(keepends=True)
    if context_lengths[0] > len(prefix_lines) or context_lengths[1] > len(suffix_lines):
        raise ValueError(
            f"The context lengths {context_lengths} are too large for the prefix and suffix."
        )
    if (
        data.updated_code is not None
        and len(data.updated_code) > 0
        and data.updated_code[-1] == "\n"
        and len(selected_code_with_marker) > 0
        and selected_code_with_marker[-1] != "\n"
    ):
        selected_code_with_marker += "\n"
    raw_selected_lines = selected_code_with_marker.splitlines(keepends=True)
    selected_lines = []
    context_is_messy = False
    for x in raw_selected_lines:
        if (
            x.startswith("[x]")
            or x.startswith("[ ]")
            or x.startswith("[+]")
            or x.startswith("[-]")
        ):
            selected_lines.append(x)
        else:
            context_is_messy = True
    # Check the format of selected lines
    if len(selected_lines) < context_lengths[0] + context_lengths[1]:
        raise ValueError(
            f"The selected code {selected_code_with_marker} is too short for the context lengths {context_lengths}."
        )
    for index in range(context_lengths[0]):
        line = selected_lines[index]
        if not line.startswith("[ ]"):
            context_is_messy = True
            break
    for index in range(context_lengths[1]):
        line = selected_lines[-index - 1]
        if not line.startswith("[ ]"):
            context_is_messy = True
            break
    real_selection_start = context_lengths[0]
    middle_eats_prefix_lines = 0
    for index in range(context_lengths[0]):
        line = selected_lines[index]
        corresponding_line = prefix_lines[index - context_lengths[0]]
        if line[4:].strip() != corresponding_line.strip():
            middle_eats_prefix_lines = context_lengths[0] - index
            real_selection_start = index
            break
    # print(f"real_selection_start: {real_selection_start}")
    real_selection_end = len(selected_lines) - context_lengths[1]
    middle_eats_suffix_lines = 0
    for index in range(context_lengths[1]):
        line = selected_lines[-index - 1]
        corresponding_line = suffix_lines[context_lengths[1] - index - 1]
        # print(f"line={line.encode()} vs. suffix={corresponding_line.encode()}")
        if line[4:].strip() != corresponding_line.strip():
            middle_eats_suffix_lines = context_lengths[1] - index
            real_selection_end = len(selected_lines) - index
        break
    data.middle_eats_context(middle_eats_prefix_lines, middle_eats_suffix_lines)
    data.selected_code = "".join(
        [x[4:] for x in selected_lines[real_selection_start:real_selection_end]]
    )
    return data, context_is_messy


def generate_inverse_instruction_plus_selected_code(
    data: CodeEditData,
    inverse_instructions: list[str],
    num_examples: int = 5,
    num_context_lines: int = 8,
    model: OpenAIChatModels = "gpt-4-0613",
) -> CodeEditData:
    # Compute the real context length
    prefix_lines = data.prefix.splitlines(keepends=True)
    suffix_lines = data.suffix.splitlines(keepends=True)
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    assert data.updated_code is not None
    lindex_to_minimal_scope = sampling_lib.find_minimal_self_contained_range_per_line(
        data.prefix + data.updated_code + data.suffix
    )
    lines_in_middle = data.updated_code.splitlines(keepends=True)
    # Calculate the minimal self-contained range for the context
    end_index = len(prefix_lines) + len(lines_in_middle) + max_num_suffix_lines - 1
    max_num_suffix_lines = (
        max_num_suffix_lines + lindex_to_minimal_scope[end_index][1] - end_index
    )
    start_index = len(prefix_lines) - max_num_prefix_lines
    max_num_prefix_lines = (
        max_num_prefix_lines + start_index - lindex_to_minimal_scope[start_index][0]
    )
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    # Build the core code string
    result_str = util_lib.create_diff_with_leading_brackets(
        prefix=utils_for_str.get_last_n_lines(data.prefix, max_num_prefix_lines),
        suffix=utils_for_str.get_first_n_lines(data.suffix, max_num_suffix_lines),
        selected_code="",
        updated_code=data.updated_code,
        marker_for_select="-",
        marker_for_update="x",
    )
    cur_inverse_instructions = copy.deepcopy(inverse_instructions)
    random.shuffle(cur_inverse_instructions)
    expected_num_of_changed_lines = random.randint(
        1, len(data.updated_code.splitlines(keepends=True))
    )
    cur_inverse_instructions = cur_inverse_instructions[:num_examples]
    inverse_instruct_str = "\n".join([f"- {x}" for x in cur_inverse_instructions])
    prompt = f"""Here are a piece of codes with each line has a leading [x] or a leading [ ].

```python
{result_str}```

Consider this list of code-editing instructions:
{inverse_instruct_str}

Choose the first matching instruction that can be reasonably and practically adapted to edit to the provided source code.
Then generate the final code-editing instruction by adapting the chosen one to the provided source code.
Note that the intent of this instruction is to put the code into a state *before* a specific change - i.e., move the code back in time.


Requirements:
- Be mindful about the codes with leading [x] because they are the target codes ranges.
- Make the instruction focused on the codes with leading [x] because they will be applied to that range.
- Remember that we hope to use the adapted instruction change about {expected_num_of_changed_lines} lines of codes.
- First, briefly analyze the codes with leading [x] and other codes with leading [ ] to help u better understand the context.
- First, produce the first matching instruction and explain why do u think it is matching.
- Secondly, produce your thinking about how to reasonablly and practically adapt it so that it is applicable to the codes with leading [x].
- Finally, produce the adapted instruction.
"""

    [response_v1] = generate_response_via_chat(
        [prompt],
        None,
        temperature=0,
        num_completion=1,
        model=model,
        max_tokens=512,
        use_json=False,
    )
    messages_v2 = [
        prompt,
        response_v1,
        f"""Great! Now, let's think step by step to apply this instruction to change about {expected_num_of_changed_lines} lines of codes with [x].

Requirements:
- Produce the full codes with both [x] and [ ].
- Do not add extra comments to explain the reason of the change.""",
    ]
    [response_v2] = generate_response_via_chat(
        messages_v2,
        None,
        temperature=0,
        num_completion=1,
        model=model,
        max_tokens=1024,
        use_json=False,
    )
    raw_selected_code = utils_for_str.extract_the_last_markdown_block(response_v2)
    if raw_selected_code is None:
        raise ValueError("Cannot find the selected code.")
    data_w_selected_code, context_is_messy = post_process_selected_code(
        data,
        (
            max_num_prefix_lines,
            max_num_suffix_lines,
        ),
        raw_selected_code,
    )
    # messages_extract = messages + [
    #     response_v2,
    #     "Great! Extract the adapted instruction and the modified code (with [x] and [ ]) into a json dict with two keys as instruction and updated_code, where the value of updated_code is a string of the modified code.",
    # ]
    # [response_v3] = generate_response_via_chat(
    #     messages_extract,
    #     None,
    #     temperature=0,
    #     num_completion=1,
    #     model="gpt-4-1106-preview",
    #     max_tokens=1024,
    #     use_json=True,
    # )
    # json_dict = json.loads(response_v3)
    result = data_w_selected_code
    result.metadata["stage_1@input_code_str"] = result_str
    result.metadata["stage_1@context_length"] = (
        max_num_prefix_lines,
        max_num_suffix_lines,
    )
    # result.metadata["stage_1@selected_code"] = json_dict["updated_code"]
    # result.inverse_instructions = [Instruction(json_dict["instruction"], None)]
    result.metadata["info_for_report"] = {
        "context_is_messy": context_is_messy,
        "expected_num_of_changed_lines": expected_num_of_changed_lines,
        "example-inverse-instructions": cur_inverse_instructions,
    }
    result.metadata["stage_1@lindex_to_minimal_scope"] = lindex_to_minimal_scope
    result.metadata["stage_1@raw_updated_code"] = data.updated_code
    result.metadata["stage_1@raw_selected_code"] = raw_selected_code
    result.metadata["stage_1@context_is_messy"] = context_is_messy
    result.metadata["stage_1@dialogue"] = messages_v2 + [response_v2]
    return result
