"""The pipeline to generate the edit category."""
import copy
import dataclasses
import random
import typing

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from experimental.dxy.edits.pipelines import Pipeline
from research.core import utils_for_str
from research.data.synthetic_code_edit.types import CodeEditData
from research.data.synthetic_code_edit.util_lib import create_code_with_leading_brackets


@dataclasses.dataclass
class GenerateEditCategoryV0(Pipeline):
    """The pipeline to generate...."""

    categories: typing.Tuple[str, ...] = ()

    max_context_lines: int = 40
    num_of_completion: int = 1

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "selected_code",
        "suffix",
        "instruction",
    )

    TEMPLATE: str = r"""Full Code:
```
{prefix}@@@@@{selected_code}&&&&&{suffix}```

Instruction: {instruction}
"""

    SYSTEM_PROMPT: str = r"""Context: An AI assistant is designed to help users update a specific piece of code ('selected_code') according to given instructions ('instruction'). To understand the 'selected_code' within its context, users must provide the code segments that precede ('prefix') and follow ('suffix') the 'selected_code'.

Objective: The user will initially provide a list of potential edit categories. The task for the AI is to classify the given 'selected_code' and 'instruction' into one of these categories, or suggest a new category if none of the provided ones fit.

Requirements:
- The user must supply the 'prefix', 'selected_code', and 'suffix'. These should be clearly separated using '@@@@@' for the prefix and 'selected_code', and '&&&&&' for the 'selected_code' and 'suffix'.
- If the 'instruction' does not align with any of the user-provided categories, the AI should create and assign a new category.
- The AI should prioritize using the existing categories provided by the user whenever applicable.
- The output should only contain the instruction category without any introductory text.
"""

    def _build_sample(self, sample: CodeEditData) -> str:
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        selected_code = sample.selected_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        instruction = self._random_choice(sample.instructions).text
        return self.TEMPLATE.format(
            prefix=prefix,
            selected_code=selected_code,
            suffix=suffix,
            instruction=instruction,
        )

    def generate(
        self,
        target_sample: CodeEditData,
        model: OpenAIChatModels = "gpt-4-0613",
    ) -> typing.Tuple[CodeEditData, typing.Any]:
        """Generate the data."""
        # Build the messages
        messages = [
            "Here are all the candidate edit categories:\n"
            + "\n".join(self.categories),
            "Got it, what is the code and the instruction?",
        ]
        messages.append(self._build_sample(target_sample))
        assert len(messages) == 3
        # Call the OpenAI API
        responses = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=1.0,
            num_completion=self.num_of_completion,
            model=model,
            use_json=False,
        )
        final_result = copy.deepcopy(target_sample)
        final_result.metadata["categories@edit"] = responses
        return final_result, {
            "messages": messages,
            "system_prompt": self.SYSTEM_PROMPT,
        }


@dataclasses.dataclass
class GenerateEditCategoryZeroShotV1(Pipeline):
    """The pipeline to generate...."""

    categories: typing.Tuple[str, ...] = ()

    max_context_lines: int = 32
    num_of_completion: int = 1

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "selected_code",
        "suffix",
    )

    SYSTEM_PROMPT: str = r"""Context: An AI assistant is designed to help users update a specific piece of code ('selected_code') according to given instructions ('instruction'). To understand the 'selected_code' within its context, users must provide the code segments that precede ('prefix') and follow ('suffix') the 'selected_code'.

Objective: The user will initially provide a list of potential edit categories. The task for the AI is to classify the given 'selected_code' and 'instruction' into one of these categories, or suggest a new category if none of the provided ones fit.
However, now the 'instruction' information is missing, and you need to infer the 'instruction' from the 'selected_code' and make a good guess of the edit category.

Requirements:
- The user must supply the 'prefix', 'selected_code', and 'suffix'. These should be clearly separated using '@@@@@' for the prefix and 'selected_code', and '&&&&&' for the 'selected_code' and 'suffix'.
- If none of the existing categories align with the 'selected_code', the AI should create and assign a new category.
- The AI should prioritize using the existing categories provided by the user whenever applicable.
- The output should only contain the instruction category without any introductory text.
"""

    def _build_sample(self, sample: CodeEditData) -> str:
        assert sample.updated_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        return f"""Full Code:
```
{prefix}@@@@@
{updated_code}&&&&&
{suffix}
```
"""

    def generate(
        self,
        target_sample: CodeEditData,
        model: OpenAIChatModels = "gpt-4-0613",
    ) -> typing.Tuple[CodeEditData, typing.Any]:
        """Generate the data."""
        # Build the messages
        messages = [
            "Here are all the candidate edit categories:\n"
            + "\n".join(self.categories),
            "Got it, what is the code and the instruction?",
        ]
        messages.append(self._build_sample(target_sample))
        assert len(messages) == 3
        # Call the OpenAI API
        responses = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=1.0,
            num_completion=self.num_of_completion,
            model=model,
            use_json=False,
        )
        final_result = copy.deepcopy(target_sample)
        final_result.metadata["categories@edit"] = responses
        return final_result, {
            "messages": messages,
            "system_prompt": self.SYSTEM_PROMPT,
        }


@dataclasses.dataclass
class GenerateCategoryFewShot(Pipeline):
    """The pipeline to generate the instructions."""

    num_samples_per_category: int = 2
    max_context_lines: int = 8

    REQUIRED_KEYS: typing.Tuple[str, ...] = (
        "prefix",
        "updated_code",
        "suffix",
    )

    SYSTEM_PROMPT: typing.Optional[
        str
    ] = """You are an powerful AI programming assistant and follow the user's requirements carefully and to the letter. Always think step by step and never rush."""

    def __post_init__(self):
        super().__post_init__()

    def _build_init_prompt(self) -> str:
        text = """Assume an AI assistant can update a specific range of codes following the user's instruction.
That specific range of codes (target code) are highlighted with leading [x] and sorrunding contexts are marked with leading [ ].
Now the user will provide a list of potential categories. The task for the AI is to classify the instruction into a most possible category or suggest a new category if none of the provided ones fit.
The most challenging part is that the user does not provide the instruction, and you need to infer the instruction from the code and make a good guess of the category.

We will start with some examples and the last one is the target example.\n"""
        return text

    def _build_sample(self, sample: CodeEditData, index: int) -> str:
        assert sample.updated_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        inverse_instruction = self._random_choice(sample.inverse_instructions).text
        category = sample.metadata["category"]
        return rf"""# The {index}-th example

Code:
```
{create_code_with_leading_brackets(prefix, updated_code, suffix)}```

Instruction: {inverse_instruction}

Category: {category}

"""

    def _build_target_sample(self, sample: CodeEditData) -> str:
        assert sample.updated_code is not None
        prefix = utils_for_str.get_last_n_lines(sample.prefix, self.max_context_lines)
        updated_code = sample.updated_code
        suffix = utils_for_str.get_first_n_lines(sample.suffix, self.max_context_lines)
        category_str = "\n".join(self.categories)
        return f"""# The target example
Code:
```
{create_code_with_leading_brackets(prefix, updated_code, suffix)}```

Here are all the candidate edit categories:
{category_str}

Please try your best to select the category that best fits the instruction if possible or suggest a new category if none of the provided ones fit.
Remember:
- Focus on the target codes (where each line has a leading [x]) and other codes (with leading [ ]) is just to help u better understand the context.
- The potential instruction will only be applied to the target code (which has leading [x]), so think of a reasonable instruction.
- Do not try to apply the edit instruction to the context codes.
- You can replay the target codes to remind yourself about its content.
- Be mindful that the instruction can be super diverse from enhancing the code to damaging the codes or introducing bugs, so that even if the current codes look good, you can be creative on the potential instruction.
"""

    # def _parse_target(self, response: str) -> typing.Optional[str]:
    #     pattern = "|".join([re.escape(f"Category: {x}") for x in self.categories])
    #     matches = re.findall(pattern, response)
    #     # Remove the 'Category: ' prefix and remove duplicates
    #     matches = [match.replace("Category: ", "") for match in matches]
    #     matches = list(set(matches))
    #     if len(matches) == 1:
    #         return matches[0]
    #     else:
    #         return None

    def generate(
        self,
        target_sample: CodeEditData,
        model: OpenAIChatModels = "gpt-4-1106-preview",
    ) -> typing.Tuple[CodeEditData, typing.Any]:
        """Generate the data."""

        in_context_samples = []
        for category in self.categories:
            in_context_samples += self.sample_samples(
                self.seeds_by_category[category],
                self.num_samples_per_category,
            )
        random.shuffle(in_context_samples)
        assert len(in_context_samples) > 0

        # The samples to be used
        message = self._build_init_prompt()
        for index, sample in enumerate(in_context_samples):
            message += self._build_sample(sample, index)
        message += self._build_target_sample(target_sample)
        [response] = generate_response_via_chat(
            [message],
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model=model,
            max_tokens=512,
            use_json=False,
        )
        messages = [
            message,
            response,
            "Super great! Can you simplify your answer into just the category name without any introductory text? Please directly reply the category name.",
        ]
        [response_v2] = generate_response_via_chat(
            messages,
            self.SYSTEM_PROMPT,
            temperature=0,
            num_completion=1,
            model="gpt-3.5-turbo-1106",
            max_tokens=512,
            use_json=False,
        )
        final_result = copy.deepcopy(target_sample)
        final_result.metadata["category"] = response_v2
        return final_result, {
            "in_context_samples": in_context_samples,
            "messages": [message],
            "system_prompt": self.SYSTEM_PROMPT,
            "responses": [response, response_v2],
        }
