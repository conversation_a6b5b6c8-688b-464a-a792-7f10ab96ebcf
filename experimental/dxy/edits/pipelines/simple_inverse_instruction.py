"""Simple functions to generate the reverse instructions."""
import copy
import json
import random

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from research.core import utils_for_str
from research.data.synthetic_code_edit import util_lib
from research.data.synthetic_code_edit.types import CodeEditData, Instruction


def select_relative_instructions(
    data: CodeEditData,
    inverse_instructions: list[str],
    num_context_lines: int = 40,
    model: OpenAIChatModels = "gpt-4-1106-preview",
) -> CodeEditData:
    prefix_lines = data.prefix.splitlines(keepends=True)
    suffix_lines = data.suffix.splitlines(keepends=True)
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    assert data.updated_code is not None
    result_str = util_lib.create_diff_with_leading_brackets(
        prefix=utils_for_str.get_last_n_lines(data.prefix, max_num_prefix_lines),
        suffix=utils_for_str.get_first_n_lines(data.suffix, max_num_suffix_lines),
        selected_code="",
        updated_code=data.updated_code,
        marker_for_select="-",
        marker_for_update="x",
    )
    cur_inverse_instructions = copy.deepcopy(inverse_instructions)
    random.shuffle(cur_inverse_instructions)
    inverse_instruct_str = "\n".join([f"- {x}" for x in cur_inverse_instructions])
    prompt = f"""Here are a piece of codes with each line has a leading [x] or a leading [ ].

```python
{result_str}```

Consider this list of code-editing instructions:
{inverse_instruct_str}

Choose two instructions that can be reasonably and practically used to modify the codes with leading [x].

Requirements:
- First, briefly analyze the codes with leading [x] and other codes with leading [ ] to help u better understand the context.
- Secondly, produce your thinking about how to reasonablly and practically pick up the two instructions.
- Finally, produce the selected instructions.
"""

    [response_v1] = generate_response_via_chat(
        [prompt],
        None,
        temperature=0,
        num_completion=1,
        model=model,
        max_tokens=512,
        use_json=False,
    )
    [response_v2] = generate_response_via_chat(
        [
            prompt,
            response_v1,
            "Extract the selected instructions into a json dict with two keys as instruction_1 and instruction_2.",
        ],
        None,
        temperature=0,
        num_completion=1,
        model="gpt-3.5-turbo-1106",
        max_tokens=512,
        use_json=True,
    )
    json_dict = json.loads(response_v2)
    result = copy.deepcopy(data)
    result.metadata["plausible_instructions@prompt"] = prompt
    result.metadata["plausible_instructions@response_v1"] = response_v1
    result.metadata["plausible_instructions@results"] = [
        json_dict["instruction_1"],
        json_dict["instruction_2"],
    ]
    return result


def generate_inverse_instruction(
    data: CodeEditData,
    inverse_instructions: list[str],
    num_examples: int = 10,
    num_context_lines: int = 8,
    model: OpenAIChatModels = "gpt-4-0613",
) -> CodeEditData:
    prefix_lines = data.prefix.splitlines(keepends=True)
    suffix_lines = data.suffix.splitlines(keepends=True)
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    assert data.updated_code is not None
    result_str = util_lib.create_diff_with_leading_brackets(
        prefix=utils_for_str.get_last_n_lines(data.prefix, max_num_prefix_lines),
        suffix=utils_for_str.get_first_n_lines(data.suffix, max_num_suffix_lines),
        selected_code="",
        updated_code=data.updated_code,
        marker_for_select="-",
        marker_for_update="x",
    )
    cur_inverse_instructions = copy.deepcopy(inverse_instructions)
    random.shuffle(cur_inverse_instructions)
    cur_inverse_instructions = cur_inverse_instructions[:num_examples]
    inverse_instruct_str = "\n".join([f"- {x}" for x in cur_inverse_instructions])
    prompt = f"""Here are a piece of codes with each line has a leading [x] or a leading [ ].

```python
{result_str}```

Consider this list of code-editing instructions:
{inverse_instruct_str}

Choose the first matching instruction that can be reasonably and practically adapted to edit to the provided source code.
Then generate the final code-editing instruction by adapting the chosen one to the provided source code.

Requirements:
- Be mindful about the codes with leading [x] because they are the target codes ranges.
- Make the instruction focused on the codes with leading [x] because they will be applied to that range.
- First, briefly analyze the codes with leading [x] and other codes with leading [ ] to help u better understand the context.
- First, produce the first matching instruction and explain why do u think it is matching.
- Secondly, produce your thinking about how to reasonablly and practically adapt it so that it is applicable to the codes with leading [x].
- Finally, produce the adapted instruction.
"""

    [response_v1] = generate_response_via_chat(
        [prompt],
        None,
        temperature=0,
        num_completion=1,
        model=model,
        max_tokens=512,
        use_json=False,
    )
    [response_v2] = generate_response_via_chat(
        [
            prompt,
            response_v1,
            "Extract the adapted instruction into a json dict with a single key as instruction.",
        ],
        None,
        temperature=0,
        num_completion=1,
        model="gpt-3.5-turbo-1106",
        max_tokens=256,
        use_json=True,
    )
    inverse_instruct = json.loads(response_v2)["instruction"]
    result = copy.deepcopy(data)
    result.inverse_instructions = [Instruction(inverse_instruct, None)]
    result.metadata["inverse_instruction@prompt"] = prompt
    result.metadata["inverse_instruction@response_v1"] = response_v1
    result.metadata["inverse_instruction@response_v2"] = response_v2
    return result
