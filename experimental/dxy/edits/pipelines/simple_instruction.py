"""Simple functions to generate the reverse instructions."""
import copy
import difflib
import json

from experimental.dxy.edits.api_lib import OpenAIChatModels, generate_response_via_chat
from research.core import utils_for_str
from research.data.synthetic_code_edit import sampling_lib
from research.data.synthetic_code_edit.types import CodeEditData, Instruction


def generate_instruction(
    data: CodeEditData,
    num_context_lines: int = 8,
    model: OpenAIChatModels = "gpt-4-0613",
) -> CodeEditData:
    #
    prefix_lines = data.prefix.splitlines(keepends=True)
    suffix_lines = data.suffix.splitlines(keepends=True)
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    assert data.updated_code is not None
    lindex_to_minimal_scope = sampling_lib.find_minimal_self_contained_range_per_line(
        data.prefix + data.updated_code + data.suffix
    )
    lines_in_middle = data.updated_code.splitlines(keepends=True)
    # Calculate the minimal self-contained range for the context
    end_index = len(prefix_lines) + len(lines_in_middle) + max_num_suffix_lines - 1
    max_num_suffix_lines = (
        max_num_suffix_lines + lindex_to_minimal_scope[end_index][1] - end_index
    )
    start_index = len(prefix_lines) - max_num_prefix_lines
    max_num_prefix_lines = (
        max_num_prefix_lines + start_index - lindex_to_minimal_scope[start_index][0]
    )
    max_num_prefix_lines = min(len(prefix_lines), num_context_lines)
    max_num_suffix_lines = min(len(suffix_lines), num_context_lines)
    assert data.updated_code is not None
    assert data.selected_code is not None
    diff = difflib.unified_diff(
        data.selected_code.splitlines(True), data.updated_code.splitlines(True)
    )
    diff_str = "".join(diff)
    full_code_str = (
        utils_for_str.get_last_n_lines(data.prefix, max_num_prefix_lines)
        + diff_str
        + utils_for_str.get_first_n_lines(data.suffix, max_num_suffix_lines)
    )
    prompt = f"""
Code diff (old version is marked with a leading - and new version is marked with a leading +)
```
{full_code_str}
```

Give me an instruction that a developer may have followed to edit old version code to get new version code.

Give me multiple versions of the instruction with different complexity from normal (say 8 words) to extremely terse (2 words, maybe just "fix it", "add missing", etc.).
The output should just consist one instruction version per line formatted as "Instruction: ..."
"""

    [response_v1] = generate_response_via_chat(
        [prompt],
        None,
        temperature=0.5,
        num_completion=1,
        model=model,
        max_tokens=512,
        use_json=False,
    )
    messages_v1 = [
        prompt,
        response_v1,
        """Great! Check over the whole example. Do you think this is a high-quality example of applying an instruction to the old version of the codes?
Please assessing the quality as High, Medium or Low. E.g., "Quality: Medium"
""",
    ]
    [response_v2] = generate_response_via_chat(
        messages_v1,
        None,
        temperature=0,
        num_completion=1,
        model=model,
        max_tokens=512,
        use_json=False,
    )
    messages_v2 = messages_v1 + [
        response_v2,
        """Extract your proposed instructions and assessment into a json dict as {'instructions': [...], 'assessment': ...}.""",
    ]
    [response_v3] = generate_response_via_chat(
        messages_v2,
        None,
        temperature=0,
        num_completion=1,
        model="gpt-3.5-turbo-1106",
        max_tokens=512,
        use_json=True,
    )
    json_dict = json.loads(response_v3)
    result = copy.deepcopy(data)
    result.instructions = []
    for instruction in json_dict["instructions"]:
        result.instructions.append(Instruction(instruction, None))
    if "info_for_report" not in result.metadata:
        result.metadata["info_for_report"] = {}
    result.metadata["info_for_report"]["assessment@instruction"] = json_dict[
        "assessment"
    ]
    result.metadata["stage_instruction@assessment"] = json_dict["assessment"]
    result.metadata["stage_instruction@prompt"] = prompt
    result.metadata["stage_instruction@dialogue"] = messages_v2 + [response_v3]
    return result
