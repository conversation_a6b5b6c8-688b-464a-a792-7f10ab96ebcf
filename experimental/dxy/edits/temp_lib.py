"""A temporary library."""

import copy
import pathlib
import random
import typing

import tqdm

from research.core import utils_for_dataclass, utils_for_file, utils_for_str
from research.core.model_input import ModelInput
from research.data.synthetic_code_edit.bins.build_finetune_dataset import (
    MMapIndexedDatasetBuilder,
    np,
    pack_sequences,
    torch,
)
from research.data.synthetic_code_edit.types import CodeEditData, Instruction


def get_instruction(
    example: CodeEditData, mode: typing.Literal["short", "long", "random"]
) -> str:
    assert example.instructions is not None
    assert len(example.instructions) > 0
    instructions = [x.text for x in example.instructions]
    if mode == "short":
        sorted_instructions = sorted(instructions, key=lambda x: len(x))
        return sorted_instructions[0]
    elif mode == "long":
        sorted_instructions = sorted(instructions, key=lambda x: len(x))
        return sorted_instructions[-1]
    elif mode == "random":
        return random.choice(instructions)
    else:
        raise ValueError(f"Unknown mode: {mode}")


def editdata_to_tokens(data: list[CodeEditData], prompt_formatter) -> list[list[int]]:
    """Convert a list of CodeEditData to a list of tokens."""
    tokens_per_example: list[list[int]] = []
    for ex in tqdm.tqdm(data, total=len(data), desc="Prepare Prompt"):
        assert ex.instructions is not None
        assert ex.selected_code is not None
        assert ex.updated_code is not None
        instruction = random.choice(ex.instructions).text
        minput = ModelInput(
            prefix=ex.prefix,
            suffix=ex.suffix,
            extra={
                "instruction": instruction,
                "selected_code": ex.selected_code,
            },
        )
        prompt, _ = prompt_formatter.prepare_prompt(minput)
        targets = utils_for_str.create_markdown_block(ex.updated_code)
        tokens = [-x for x in prompt] + prompt_formatter.tokenizer.tokenize(targets)
        tokens_per_example.append(tokens + [prompt_formatter.tokenizer.eod_id])
    return tokens_per_example


def build_dataset(
    data: list[list[int]],
    prompt_formatter,
    output: typing.Union[pathlib.Path, str],
    sequence_length: int,
):
    """Build the dataset."""
    data = copy.deepcopy(data)
    random.shuffle(data)
    sequences = pack_sequences(
        data, sequence_length, -prompt_formatter.tokenizer.eod_id
    )
    output_path = pathlib.Path(output)
    output_path.parent.mkdir(parents=False, exist_ok=True)
    builder = MMapIndexedDatasetBuilder(output_path.with_suffix(".bin"), dtype=np.int32)
    for sequence in sequences:
        # Make the total sequence length to be sequence_length + 1
        builder.add_item(sequence + [-prompt_formatter.tokenizer.eod_id])
        builder.end_document()
    builder.finalize(output_path.with_suffix(".idx"))
    print(f"Saved {len(sequences)} sequences to {output_path}")


def load_igor_data() -> list[CodeEditData]:
    directory_path = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/edit.raw/in-house-xx/mix1.2023-12-31_00-29-39/all"
    )
    all_json_files = [f for f in directory_path.glob("*.json")]
    print(f"Find {len(all_json_files)} files in total.")
    json_list = [utils_for_file.read_json(x) for x in all_json_files]
    examples: list[CodeEditData] = []
    for data in json_list:
        if not data["success"]:
            continue
        instructions = data["instructions"]
        old_code, new_code = data["old_code"], data["new_code"]
        old_lines = old_code.splitlines(keepends=True)
        new_lines = new_code.splitlines(keepends=True)
        p_l, s_l = utils_for_str.get_shared_prefix_suffix_lines(old_code, new_code)
        prefix = "".join(old_lines[:p_l])
        suffix = "".join(old_lines[len(old_lines) - s_l :])
        selected_code = "".join(old_lines[p_l : len(old_lines) - s_l])
        updated_code = "".join(new_lines[p_l : len(new_lines) - s_l])
        x = CodeEditData(
            repo_url="",
            file_name="",
            prefix=prefix,
            suffix=suffix,
            selected_code=selected_code,
            updated_code=updated_code,
            instructions=[Instruction(ins, None) for ins in instructions],
        )
        examples.append(x)
    return examples


def load_instruct_coder(
    name: typing.Literal["github_seed", "train", "valid", "additional_seed"]
) -> list[CodeEditData]:
    root_dir = pathlib.Path(
        "/mnt/efs/augment/user/dxy/datasets/edit.processed/InstructCoder.CodeEditData"
    )
    all_data = utils_for_file.read_jsonl_zst(root_dir / f"{name}.jsonl.zst")
    final_examples = [
        utils_for_dataclass.create_from_dict(CodeEditData, x) for x in all_data
    ]
    return final_examples


def load_yury_data() -> list[CodeEditData]:
    raw_pr_data = utils_for_file.read_json(
        "/mnt/efs/augment/user/dxy/datasets/edit.raw/in-house-xx/yury-pr/pr_suggested_edits.raw.v1.json"
    )
    examples: list[CodeEditData] = []
    for data in raw_pr_data:
        instruction = data["instruction"]
        selected_code, updated_code = data["old_code"], data["new_code"]
        prefix, suffix = data["prefix"], data["suffix"]
        x = CodeEditData(
            repo_url="",
            file_name=data["path"],
            prefix=prefix,
            suffix=suffix,
            selected_code=selected_code,
            updated_code=updated_code,
            instructions=[Instruction(instruction, None)],
        )
        examples.append(x)
    return examples


def filter_code_edit_data(
    data_list: list[CodeEditData],
    max_num_selected_code_lines: int = 128,
    max_num_updated_code_lines: int = 128,
    max_num_over_average: int = 3,
):
    """Filter the code edit data."""
    data_by_num_selected_code_lines: dict[int, list[CodeEditData]] = {}
    for data in data_list:
        assert data.selected_code is not None
        assert data.updated_code is not None
        num_selected_code_lines = len(data.selected_code.splitlines(keepends=True))
        num_updated_code_lines = len(data.updated_code.splitlines(keepends=True))
        if num_selected_code_lines > max_num_selected_code_lines:
            continue
        if num_updated_code_lines > max_num_updated_code_lines:
            continue
        if num_selected_code_lines not in data_by_num_selected_code_lines:
            data_by_num_selected_code_lines[num_selected_code_lines] = []
        data_by_num_selected_code_lines[num_selected_code_lines].append(data)
    keys = sorted(data_by_num_selected_code_lines.keys())
    nums = [len(data_by_num_selected_code_lines[key]) for key in keys]
    average_num = sum(nums) / len(nums)
    max_num_per_key = int(average_num * max_num_over_average)
    new_data_list: list[CodeEditData] = []
    for key in keys:
        cap = min(max_num_per_key, len(data_by_num_selected_code_lines[key]))
        print(f"key: {key}, len: {len(data_by_num_selected_code_lines[key])} / {cap}")
        selected_examples = random.sample(data_by_num_selected_code_lines[key], cap)
        new_data_list.extend(selected_examples)
    print(
        f"Average num: {average_num}, maximum num per key: {max_num_per_key}, and finally get {len(new_data_list)} examples."
    )
    return new_data_list


def augment_code_edit_data(
    data_list: list[CodeEditData],
    strict_sample_ratio: float = 0.2,
    max_context_lines: int = 80,
) -> list[CodeEditData]:
    """Augment the code edit data."""
    new_data_list: list[CodeEditData] = []
    for data in data_list:
        assert data.instructions is not None
        all_instructions = [x.text for x in data.instructions]
        prefix_lines = data.prefix.splitlines(keepends=True)
        suffix_lines = data.suffix.splitlines(keepends=True)
        assert data.selected_code is not None
        assert data.updated_code is not None
        for ins in all_instructions:
            # 20% use the tight selected_code and updated code
            if random.random() < strict_sample_ratio:
                eat_num_prefix_lines = 0
                eat_num_suffix_lines = 0
            else:
                eat_num_prefix_lines = random.randint(
                    0, min(max_context_lines, len(prefix_lines))
                )
                eat_num_suffix_lines = random.randint(
                    0, min(max_context_lines, len(suffix_lines))
                )
            new_prefix_lines = prefix_lines[: len(prefix_lines) - eat_num_prefix_lines]
            new_suffix_lines = suffix_lines[eat_num_suffix_lines:]
            eaten_prefix_str = "".join(
                prefix_lines[len(prefix_lines) - eat_num_prefix_lines :]
            )
            eaten_suffix_str = "".join(suffix_lines[:eat_num_suffix_lines])
            new_selected_code = eaten_prefix_str + data.selected_code + eaten_suffix_str
            new_updated_code = eaten_prefix_str + data.updated_code + eaten_suffix_str
            new_prefix = "".join(new_prefix_lines)
            new_suffix = "".join(new_suffix_lines)
            new_data_list.append(
                CodeEditData(
                    repo_url=data.repo_url,
                    file_name=data.file_name,
                    prefix=new_prefix,
                    suffix=new_suffix,
                    selected_code=new_selected_code,
                    updated_code=new_updated_code,
                    instructions=[Instruction(ins, None)],
                )
            )
    return new_data_list


# if __name__ == "__main__":
#     # data = load_igor_data()
#     data = load_yury_data()
