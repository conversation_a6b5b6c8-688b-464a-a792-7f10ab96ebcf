"""Many helper functions to deal with string data."""
import re
import string


def remove_punctuation(input_string, strict: bool = True):
    if strict:
        return re.sub(r"[^\w\s]", "", input_string)
    else:
        translator = str.maketrans("", "", string.punctuation)
    return input_string.translate(translator)


def trim_tail_white_space_per_paragraph(string: str):
    return "\n".join([p.rstrip() for p in string.split("\n")])


def meaningful_line(inputs: str) -> float:
    chars = "".join([x for x in inputs.split() if x])
    words = remove_punctuation(inputs, strict=True).split()
    words = [x for x in words if x]
    return max(1, len(words) * 0.6 + len(chars) * 0.3)


def trim_result_if_meet_tail_of_prefix(
    result: str, prefix: str, min_matched_lines: int = 2
) -> str:
    prefix_lines = prefix.split("\n")
    result_lines = result.split("\n")
    # print(f'There are {len(prefix_lines)} prefix lines')
    avaliable_locations = set([_ for _ in range(len(result_lines))])
    possible_result = ""
    matched_lines = 0
    for index in range(0, len(prefix_lines)):
        r_index = len(prefix_lines) - 1 - index
        cur_line = prefix_lines[r_index]
        matched_indexes = []
        for j, r_line in enumerate(result_lines):
            if cur_line == r_line:
                matched_indexes.append(j + index)
        if len(avaliable_locations & set(matched_indexes)) == 0:
            if matched_lines >= min_matched_lines:
                return possible_result
            break
        else:
            avaliable_locations = avaliable_locations & set(matched_indexes)
            # matched_lines += int(prefix_lines[r_index] != "")
            matched_lines += meaningful_line(prefix_lines[r_index])
            possible_end_index = min(avaliable_locations) - index
            # print(f'possible_end_index={possible_end_index}')
            possible_result = "\n".join(result_lines[:possible_end_index])
        # print(f'index={r_index}: {cur_line}')
        # print(f'matched_indexes={matched_indexes}')
        # print(f'avaliable_locations={avaliable_locations}')
    return result


def trim_result_if_meet_head_of_suffix(
    result: str,
    suffix: str,
    min_matched_lines: int = 2,
    min_matched_lines_if_fully_match: int = 1,
) -> str:
    suffix_lines = suffix.split("\n")
    result_lines = result.split("\n")
    for index in range(len(result_lines)):
        matched_lines, reach_end = 0, False
        for j, suffix_line in enumerate(suffix_lines):
            if index + j >= len(result_lines):
                reach_end = True
                break
            if result_lines[index + j] != suffix_line:
                break
            matched_lines += meaningful_line(suffix_line)
        if matched_lines >= min_matched_lines:
            return "\n".join(result_lines[:index])
        if reach_end and matched_lines >= min_matched_lines_if_fully_match:
            return "\n".join(result_lines[:index])
    return result
