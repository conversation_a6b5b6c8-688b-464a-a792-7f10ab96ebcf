"""File helper."""
import bz2
import os
import pickle


def append_ext_if_possible(file_path: str, ext: str = ".pbz2") -> str:
    return file_path if file_path.endswith(ext) else file_path + ext


def pickle_save(obj: ..., file_path: str, ext: str = ".pbz2", protocol: int = 4):
    """Use pickle to save data (obj) into file_path.

    Args:
      obj: The object to be saved into a path.
      file_path: The target saving path.
      ext: The extension of file name.
      protocol: The pickle protocol. According to this documentation
        (https://docs.python.org/3/library/pickle.html#data-stream-format),
        the protocol version 4 was added in Python 3.4. It adds support for very
        large objects, pickling more kinds of objects, and some data format
        optimizations. It is the default protocol starting with Python 3.8.
    """
    with bz2.BZ2File(append_ext_if_possible(file_path, ext), "wb") as cfile:
        pickle.dump(obj, cfile, protocol=protocol)


def pickle_load(file_path: str, ext: str = ".pbz2"):
    """Use pickle to load the file on different systems."""
    # return pickle.load(open(file_path, "rb"))
    file_path = append_ext_if_possible(file_path, ext)
    if not os.path.isfile(file_path):
        print(f"Did not find the file {file_path}.")
        return None
    with bz2.BZ2File(file_path, "rb") as cfile:
        return pickle.load(cfile)
