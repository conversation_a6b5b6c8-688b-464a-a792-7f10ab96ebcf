"""A helper file for Vulcan."""
import collections
import os
import pathlib
from typing import Optional, Union, cast

from termcolor import colored

from experimental.dxy.code.file_helper import pickle_load

# from research.eval.patch_lib import Patch
from research.eval.vulcan import VulcanTest
from research.eval.vulcan import load_tests as load_raw_tests


def display_output(
    test: VulcanTest, model_output: Optional[str] = None, extra_header: str = ""
):
    if model_output is None:
        model_output = test.expected
    correct = model_output.strip() == test.expected.strip()

    name = pathlib.Path(test.filename).name

    print(
        "========================================================================================"
    )
    print(f"EXAMPLE: {name} {'✓' if correct else '✗'} | {extra_header}")
    print(
        "----------------------------------------------------------------------------------------"
    )
    print(
        colored(test.prefix, "blue")
        + colored(model_output, "red", "on_black")
        + colored(test.suffix, "green")
    )
    print(
        "========================================================================================"
    )
    print()
    print()


def display_cache(file_or_test: Union[str, VulcanTest]):
    if isinstance(file_or_test, str):
        test = pickle_load(file_or_test)
    else:
        test = file_or_test
    test = cast(VulcanTest, test)
    display_output(test, model_output=None, extra_header="Ground Truth")
    all_model_keys = list(test.meta["results"].keys())
    for key in all_model_keys:
        result = test.meta["results"][key]
        extra_header = test.meta["extra_headers"][key]
        display_output(test, model_output=result, extra_header=extra_header)


def convert_test_by_label(
    all_tests: list[VulcanTest], all_in_none: bool = False
) -> dict[Optional[str], list[VulcanTest]]:
    test_by_label = collections.defaultdict(list)
    for test in all_tests:
        if "label" in test.meta:
            label = test.meta["label"]
        else:
            label = None
        test_by_label[label].append(test)
    if all_in_none:
        test_by_label[None] = all_tests
    return test_by_label


def show_label_info(all_tests: list[VulcanTest]):
    test_by_label = convert_test_by_label(all_tests)
    for label, tests in test_by_label.items():
        print(f"{label} : {len(tests)} tests")


def load_tests(
    ignore_empty_suffix: bool = True,
    target_ext: str = "py",
    cache_folder: Optional[str] = None,
    cache_suffix: Optional[str] = None,
) -> list[VulcanTest]:
    all_tests, skip = load_raw_tests(), 0
    tests_by_ext = collections.defaultdict(list)
    for test in all_tests:
        ext = test.filename.split(".")[-1]
        if (
            ignore_empty_suffix and (len(test.suffix) == 0 or len(test.expected) == 0)
        ) or ("archived" in test.filename):
            print(f"Ignore {test.filename}")
            skip += 1
            continue
        tests_by_ext[ext].append(test)
    print(f"In total, there are {len(all_tests)} tests and skipped {skip} tests.")
    for ext, xlist in tests_by_ext.items():
        print(f"There are {len(xlist)} with {ext} extension.")
    if target_ext not in tests_by_ext:
        raise ValueError(f"Valid ext: {tests_by_ext.keys()}")
    all_tests = tests_by_ext[target_ext]
    all_tests = sorted(all_tests, key=lambda x: x.filename)
    print(f"Loaded {len(all_tests)} tests.")
    for idx, test in enumerate(all_tests):
        print(
            f"[{idx:02d}] [#char: {len(test.expected):3d}] {os.path.basename(test.filename):35s} {test.meta}"
        )
        # Add results and extra_headers fields to save all the predictions
        test.meta["results"] = collections.OrderedDict()
        test.meta["extra_headers"] = collections.OrderedDict()
    print("-" * 100)
    show_label_info(all_tests)
    print("-" * 100)
    if cache_folder and cache_suffix:
        print(f"Use the saved cache results from {cache_folder} and {cache_suffix}")
        for test in all_tests:
            filename = os.path.basename(test.filename)
            cache_filename = os.path.join(cache_folder, f"{filename}-{cache_suffix}")
            cache_test = pickle_load(cache_filename)
            if cache_test is None:
                continue
            print(f"Find cache {cache_filename} and loading...")
            all_model_keys = list(cache_test.meta["results"].keys())
            for key in all_model_keys:
                test.meta["results"][key] = cache_test.meta["results"][key]
                test.meta["extra_headers"][key] = cache_test.meta["extra_headers"][key]
    return all_tests


def load_examplar_tests():
    EXAMPLAR_TEST_ROOT = pathlib.Path(__file__).parent.parent / "exemplars"
    print(f"EXAMPLAR_TEST_ROOT: {EXAMPLAR_TEST_ROOT}")
    all_tests = load_raw_tests(EXAMPLAR_TEST_ROOT)
    print("-" * 100)
    show_label_info(all_tests)
    print("-" * 100)
    return all_tests
