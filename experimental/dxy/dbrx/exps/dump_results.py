"""Dump the different versions of DBRX model to compare.

python experimental/dxy/dbrx/demo/dump_results.py --model dbrx --prompt 0
python experimental/dxy/dbrx/demo/dump_results.py --model dbrx --prompt 1
python experimental/dxy/dbrx/demo/dump_results.py --model dbrx --prompt 2

python experimental/dxy/dbrx/demo/dump_results.py --model dbrx_ref_cpu --prompt 0
python experimental/dxy/dbrx/demo/dump_results.py --model dbrx_ref_cpu --prompt 1
python experimental/dxy/dbrx/demo/dump_results.py --model dbrx_ref_cpu --prompt 2
"""

import argparse
import pathlib
import pdb

import torch
from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer
from experimental.dxy.dbrx.exps.test_samples import (get_prompt_0,
                                                     get_prompt_1,
                                                     get_prompt_2)
from experimental.dxy.dbrx.reference import configuration_dbrx, modeling_dbrx
from experimental.dxy.dbrx.utils import DbrxHFCheckpoint
from research.core.utils import display_memory_info
from research.core.utils_for_log import time_string
from transformers import AutoModelForCausalLM, AutoTokenizer


def call_dbrx(tokens: list[int], model, tokenizer, max_new_tokens: int = 64):
    with torch.inference_mode():
        model.eval()
        input_ids = torch.tensor([tokens], dtype=torch.long).to("cuda")
        attention_mask = torch.ones_like(input_ids).to("cuda")
        outputs_generation = model.generate(
            input_ids=input_ids,
            attention_mask=attention_mask,
            max_new_tokens=max_new_tokens,
        ).squeeze(dim=0)
        # Compute the logits
        outputs_w_logits = model(input_ids)
        logits = outputs_w_logits.logits.squeeze(dim=0)
        # Convert and return the results
        logits, outputs_generation = logits.cpu(), outputs_generation.cpu().tolist()
        assert outputs_generation[: len(tokens)] == tokens
        text_generated = tokenizer.decode(outputs_generation[len(tokens) :])
        return logits, outputs_generation, text_generated


def call_dbrx_cpu(
    tokens: list[int], tokenizer: DBRXInstructTokenizer, max_new_tokens: int = 16
):
    dbrx_ckp = DbrxHFCheckpoint("/mnt/efs/augment/checkpoints/databricks/dbrx-instruct")
    dbrx_ref_config = configuration_dbrx.DbrxConfig(**dbrx_ckp.config)
    print(f"{time_string()}: Load the dbrx ref config from {dbrx_ckp.ckp_dir}")
    print(dbrx_ref_config)
    with torch.inference_mode():
        print(f"{time_string()}: Start to create the dbrx ref model from the config")
        # Use float32 for the CPU run.
        # torch.set_default_dtype(dbrx_ref_config.torch_dtype)
        dbrx_ref_model = modeling_dbrx.DbrxForCausalLM(dbrx_ref_config)
        dbrx_ref_model.eval()
        print(f"{time_string()}: Finish creating the dbrx ref model from the config")
        model_state_dict = dbrx_ref_model.state_dict()
        print(dbrx_ref_model)
        # Load the correct weights
        dbrx_ckp.load_all_weights()
        # Check the keys
        expected_keys_from_model = sorted(list(set(model_state_dict.keys())))
        expected_keys_from_ckp = sorted(list(dbrx_ckp.weight_buffer.keys()))
        if expected_keys_from_ckp != expected_keys_from_model:
            print(
                f"{time_string()}: The keys from the model and the keys from the checkpoint do not match."
            )
            pdb.set_trace()
        for key in expected_keys_from_model:
            if model_state_dict[key].shape != dbrx_ckp.weight_buffer[key].shape:
                print(f"{time_string()}: The shape for {key} does not match.")
                pdb.set_trace()
            # if model_state_dict[key].dtype != dbrx_ckp.weight_buffer[key].dtype:
            #     print(f"{time_string()}: The dtype for {key} does not match.")
            #     pdb.set_trace()
        # Update the state dict by the weight buffer
        print(f"{time_string()}: Start to load the weight buffer")
        dbrx_ref_model.load_state_dict(dbrx_ckp.weight_buffer)
        print(f"{time_string()}: Finish loading the weight buffer")

        # The actual forward pass
        input_ids = torch.LongTensor([tokens], device="cpu")
        attention_mask = torch.ones_like(input_ids, device="cpu")
        outputs_w_logits = dbrx_ref_model.forward(input_ids)

        logits = outputs_w_logits.logits.squeeze(dim=0)  # type: ignore
        print(f"{time_string()}: Finish computing the logits")

        # -- multi-step generation
        outputs_generation = dbrx_ref_model.generate(
            input_ids=input_ids,
            attention_mask=attention_mask,
            max_new_tokens=max_new_tokens,
        )
        print(f"{time_string()}: Finish the generation for {max_new_tokens} steps.")
        outputs_generation = outputs_generation.squeeze(dim=0).tolist()  # type: ignore
        assert outputs_generation[: len(tokens)] == tokens
        text_generated = tokenizer.detokenize(outputs_generation[len(tokens) :])
        return logits, outputs_generation, text_generated


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        choices=("dbrx", "dbrx_ref_cpu"),
        default="dbrx",
        help="The core LLM used for inference.",
    )
    parser.add_argument(
        "--prompt",
        type=int,
        default=0,
        choices=(0, 1, 2),
        help="The prompt to use",
    )
    parser.add_argument(
        "--output_dir",
        type=pathlib.Path,
        default=pathlib.Path("/mnt/efs/augment/user/dxy/logs/dump-dbrx"),
        help="The output directory to save the dump results",
    )
    args = parser.parse_args()

    output_dir = args.output_dir
    output_dir.mkdir(parents=False, exist_ok=True)

    display_memory_info()
    print("-" * 32)
    prompt = {
        0: get_prompt_0(),
        1: get_prompt_1(),
        2: get_prompt_2(),
    }[args.prompt]
    if args.model == "dbrx":
        tokenizer = AutoTokenizer.from_pretrained(
            "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
            trust_remote_code=True,
            token="hf_YOUR_TOKEN",
        )
        model = AutoModelForCausalLM.from_pretrained(
            "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
            device_map="auto",
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            token="hf_YOUR_TOKEN",
        )
        tokens = tokenizer.encode(prompt)
        logits_per_token, tokens_generated, text_generated = call_dbrx(
            tokens, model, tokenizer
        )
    elif args.model == "dbrx_ref_cpu":
        tokenizer = DBRXInstructTokenizer()
        tokens = tokenizer.tokenize_unsafe(prompt)
        logits_per_token, tokens_generated, text_generated = call_dbrx_cpu(
            tokens, tokenizer
        )
    else:
        raise ValueError(f"Unknown model name: {args.model}")
    print(f"Model: {args.model}")
    print(f"Prompt: {prompt}")
    print(f"Prompt has {len(tokens)} tokens: {tokens}")
    print(f"Generated {len(tokens_generated)}: {tokens_generated}")
    print(f"Logits' shape: {logits_per_token.shape}")
    print(f"Generated text: {text_generated}")

    # Save into the dump directory
    save_path = output_dir / f"{args.model}-prompt{args.prompt}.torch"
    torch.save(
        {
            "prompt": prompt,
            "logits": logits_per_token,
            "tokens": tokens,
            "tokens_generated": tokens_generated,
            "text_generated": text_generated,
        },
        save_path,
    )
    print(f"Saved to {save_path}\n")
    print("-" * 32)
    display_memory_info()
    print("-" * 32)
