"""Test the RoPE embeddings.

python experimental/dxy/dbrx/exps/test_rotary.py
"""
import pdb

import torch

from base.fastforward.positional_embeddings import (_interleave_to_not,
                                                    _not_to_interleave,
                                                    compiled_rotary)


def rotate_half(x):
    """Rotates half the hidden dims of the input."""
    x1 = x[..., : x.shape[-1] // 2]
    x2 = x[..., x.shape[-1] // 2 :]
    return torch.cat((-x2, x1), dim=-1)


def rotary_embed_in_llama_style(x: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor):
    """Yet another implementation of RoPE."""
    seq_len, _, dim = x.shape
    cos, sin = cos.to(x.dtype)[:seq_len, None], sin.to(x.dtype)[:seq_len, None]
    # [x_1, y_1, x_2, y_2, ..., x_n, y_n]
    x_aux = _interleave_to_not(x)
    # [x_1, x_2, ..., x_n, y_1, y_2, ..., y_n]
    cos, sin = torch.cat((cos, cos), dim=-1), torch.cat((sin, sin), dim=-1)
    res_aux = (x_aux * cos) + rotate_half(x_aux) * sin
    return _not_to_interleave(res_aux)


if __name__ == "__main__":
    device = "cuda"
    rope_dim = 32
    base = 500000
    max_seq_len = 1024
    rope_scaling_factor = 1.0
    dim_range = torch.arange(0, rope_dim, 2, dtype=torch.float32, device=device)
    freqs_powers = dim_range[: (rope_dim // 2)] / rope_dim

    freqs = 1.0 / (base**freqs_powers)
    t = (
        torch.arange(max_seq_len, dtype=torch.float32, device=device)
        / rope_scaling_factor
    )
    freqs = torch.outer(t, freqs)
    freqs_cos = freqs.cos()
    freqs_sin = freqs.sin()
    # precision loss
    freqs_cos = freqs_cos.to(torch.bfloat16).float()
    freqs_sin = freqs_sin.to(torch.bfloat16).float()

    tensors = torch.randn(16, 1, rope_dim, dtype=torch.bfloat16, device=device)
    idxs = torch.arange(16, dtype=torch.int32, device=device)

    tensors = tensors.float()
    tensors_r1 = compiled_rotary.rotary_embed(
        tensors.clone(), freqs_cos, freqs_sin, idxs
    )
    # tensors_r2 = indexed_rotary_embed(tensors.clone(), freqs_cos[:, None, :], freqs_sin[:, None, :], idxs)
    tensors_r3 = rotary_embed_in_llama_style(tensors, freqs_cos, freqs_sin)

    for idx in range(3):
        print(f"tensors_r1[{idx}, 0]:\n{tensors_r1[idx, 0]}")
        # print(f"tensors_r2[{idx}, 0]:\n{tensors_r2[idx, 0]}")
        print(f"tensors_r3[{idx}, 0]:\n{tensors_r3[idx, 0]}")
        print("\n\n")
    # pdb.set_trace()
    print("-")
