"""Unit tests for DBRX fastfoward model.

python experimental/dxy/dbrx/exps/unit_test.py
"""

import pdb

import numpy as np
import torch
import torch.nn as nn

from base.fastforward import layers


def _init_module(module: nn.Module, gaussian_init: bool = False):
    with torch.inference_mode():
        for p in module.parameters():
            std = 1.0 / np.sqrt(p.shape[0])
            p.normal_(std=std) if gaussian_init else p.uniform_(-0.01, 0.01)


emb_dim = 32
mlp_dim = 64
moe_num_experts = 16
moe_top_k = 4
seq_len = 8


with torch.inference_mode():
    inp = torch.empty(seq_len, emb_dim, dtype=torch.float32, device="cuda").normal_()
    layer = layers.DbrxFFN(
        emb_dim=emb_dim,
        mlp_dim=mlp_dim,
        moe_num_experts=moe_num_experts,
        moe_top_k=moe_top_k,
        moe_normalize_expert_weights=1.0,
        dtype=torch.float32,
        device="cuda",
    )

    _init_module(layer, gaussian_init=True)
    out = layer(inp)
    output_np = out.detach().cpu().numpy()
    assert output_np.shape == (seq_len, emb_dim)

    # Manually compute without tensor parallel and sparsity.
    inputs_np = inp.detach().cpu().numpy()
    w1_np = layer.w1.detach().cpu().numpy()
    w2_np = layer.w2.detach().cpu().numpy()
    v1_np = layer.v1.detach().cpu().numpy()
router_np = layer.router.weight.detach().cpu().numpy()
assert router_np.shape == (moe_num_experts, emb_dim)
router_logits = inputs_np @ router_np.T
router_logits = router_logits - np.max(router_logits, axis=-1, keepdims=True)
router_probs = np.exp(router_logits) / np.sum(
    np.exp(router_logits), axis=-1, keepdims=True
)
top_experts_indices = np.argsort(-router_probs, axis=-1)[:, :moe_top_k]
top_experts_mask = np.zeros_like(router_probs, dtype=bool)
np.put_along_axis(top_experts_mask, top_experts_indices, True, axis=-1)
router_probs[~top_experts_mask] = 0
router_probs = router_probs / np.sum(router_probs, axis=-1, keepdims=True)
assert w1_np.shape == (moe_num_experts, emb_dim, mlp_dim)
gate_proj = (inputs_np[:, None, None, :] @ w1_np[None, ...]).reshape(
    seq_len, moe_num_experts, mlp_dim
)
gate_proj = gate_proj / (1.0 + np.exp(-gate_proj))
up_proj = (inputs_np[:, None, None, :] @ v1_np[None, ...]).reshape(
    seq_len, moe_num_experts, mlp_dim
)
gated_proj = gate_proj * up_proj
down_proj = (gated_proj[..., None, :] @ w2_np[None, ...]).reshape(
    seq_len, moe_num_experts, emb_dim
)
manual_output_np = (down_proj * router_probs[..., None]).sum(axis=1)
pdb.set_trace()
print("-")
