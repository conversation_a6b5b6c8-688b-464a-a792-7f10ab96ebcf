"""Test the DBRX fastfoward model.

CUDA_VISIBLE_DEVICES="" python experimental/dxy/dbrx/exps/test_fwd.py --prompt 0
CUDA_VISIBLE_DEVICES="0,1,2,3" python experimental/dxy/dbrx/exps/test_fwd.py --prompt 0 --num_processes 4
"""

import argparse
import pathlib

import torch
import tqdm
from base.fastforward import cached_attention
from base.fastforward.checkpoints import save_load
from base.fastforward.fwd_dbrx import (
    Dbrx,
    DbrxAttentionFactory,
    DbrxModelSpec,
    generate_step_fn,
)
from base.fastforward.model_specs import get_llama_model_spec
from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer
from experimental.dxy.dbrx.exps.test_samples import (
    get_prompt_0,
    get_prompt_1,
    get_prompt_2,
)
from experimental.dxy.dbrx.utils import DbrxHFCheckpoint
from research.core.utils_for_log import time_string
from transformers import AutoTokenizer


def convert_ckp_to_ffw(
    model_spec: DbrxModelSpec, output_dir: pathlib.Path, max_n_layers=None
):
    print(f"{time_string()}: Start to save the checkpoint into {output_dir}")
    output_dir.mkdir(parents=False, exist_ok=True)
    dbrx_ckp = DbrxHFCheckpoint("/mnt/efs/augment/checkpoints/databricks/dbrx-instruct")
    dbrx_ckp.load_all_weights()

    with torch.no_grad():
        model = Dbrx(model_spec, device="cpu")
        expected_model_state_dict = model.state_dict()
        expected_model_keys = sorted(list(set(expected_model_state_dict.keys())))
    # Convert non-Transformer-block layer
    non_transformer_tensors = {}
    non_transformer_tensors["embs.word_embs"] = dbrx_ckp.weight_buffer[
        "transformer.wte.weight"
    ]
    non_transformer_tensors["score.weight"] = dbrx_ckp.weight_buffer["lm_head.weight"]
    non_transformer_tensors["final_layer_norm.weight"] = dbrx_ckp.weight_buffer[
        "transformer.norm_f.weight"
    ]
    for key in non_transformer_tensors.keys():
        assert (
            non_transformer_tensors[key].shape == expected_model_state_dict[key].shape
        )
        assert key in expected_model_keys
        expected_model_keys.remove(key)
    save_load.save_weights(output_dir, non_transformer_tensors)
    for ilayer in tqdm.tqdm(
        range(dbrx_ckp.n_layers), desc="Processing Transformer blocks"
    ):
        # Process each Transformer block
        v1 = dbrx_ckp.weight_buffer[f"transformer.blocks.{ilayer}.ffn.experts.mlp.v1"]
        w1 = dbrx_ckp.weight_buffer[f"transformer.blocks.{ilayer}.ffn.experts.mlp.w1"]
        w2 = dbrx_ckp.weight_buffer[f"transformer.blocks.{ilayer}.ffn.experts.mlp.w2"]
        router = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.ffn.router.layer.weight"
        ]
        qkv = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.attn.Wqkv.weight"
        ]
        out_proj = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.attn.out_proj.weight"
        ]
        norm1 = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.norm_1.weight"
        ]
        norm2 = dbrx_ckp.weight_buffer[
            f"transformer.blocks.{ilayer}.norm_attn_norm.norm_2.weight"
        ]
        cur_block_tensors = {}
        cur_block_tensors[f"layers.{ilayer}.ffn.router.weight"] = router
        v1x = v1.view(
            model_spec.moe_num_experts, model_spec.mlp_hidden_dim, model_spec.emb_dim
        )
        cur_block_tensors[f"layers.{ilayer}.ffn.v1"] = v1x.transpose(1, 2).contiguous()
        w1x = w1.view(
            model_spec.moe_num_experts, model_spec.mlp_hidden_dim, model_spec.emb_dim
        )
        cur_block_tensors[f"layers.{ilayer}.ffn.w1"] = w1x.transpose(1, 2).contiguous()
        w2x = w2.view(
            model_spec.moe_num_experts, model_spec.mlp_hidden_dim, model_spec.emb_dim
        )
        cur_block_tensors[f"layers.{ilayer}.ffn.w2"] = w2x.contiguous()
        cur_block_tensors[f"layers.{ilayer}.attn_norm.weight"] = norm1
        cur_block_tensors[f"layers.{ilayer}.ffn_norm.weight"] = norm2
        # Reorganize the qkv tensor.
        q_w, k_w, v_w = qkv.split(
            [
                model_spec.emb_dim,
                model_spec.num_heads_kv * model_spec.head_dim,
                model_spec.num_heads_kv * model_spec.head_dim,
            ],
            dim=0,
        )
        q_w = q_w.view(
            model_spec.num_heads_kv, -1, model_spec.head_dim, model_spec.emb_dim
        ).view(model_spec.num_heads_kv, -1, model_spec.emb_dim)
        k_w = k_w.view(
            model_spec.num_heads_kv, 1, model_spec.head_dim, model_spec.emb_dim
        ).view(model_spec.num_heads_kv, -1, model_spec.emb_dim)
        v_w = v_w.view(
            model_spec.num_heads_kv, 1, model_spec.head_dim, model_spec.emb_dim
        ).view(model_spec.num_heads_kv, -1, model_spec.emb_dim)
        new_qkv = torch.cat([q_w, k_w, v_w], dim=1).view(-1, model_spec.emb_dim)
        cur_block_tensors[f"layers.{ilayer}.attn.qkv.weight"] = new_qkv
        cur_block_tensors[f"layers.{ilayer}.attn.out.weight"] = out_proj
        for key in cur_block_tensors.keys():
            assert cur_block_tensors[key].shape == expected_model_state_dict[key].shape
            assert key in expected_model_keys
            expected_model_keys.remove(key)
        save_load.save_weights(output_dir, cur_block_tensors, incremental=True)
        if max_n_layers is not None and ilayer >= max_n_layers:
            print(f"{time_string()} break at layer {ilayer}.")
            break
    print(f"{time_string()}: finish saving the checkpoint into {output_dir}.")
    assert (
        max_n_layers is not None or len(expected_model_keys) == 0
    ), f"The following keys are not found in the model: {expected_model_keys}"


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--prompt",
        type=int,
        default=0,
        choices=(0, 1, 2),
        help="The prompt to use",
    )
    parser.add_argument(
        "--num_processes",
        type=int,
        default=1,
        help="The number of processes to use",
    )
    parser.add_argument(
        "--output_dir",
        type=pathlib.Path,
        default=pathlib.Path("/home/<USER>/local-ckp/dbrx-instruct-fwd"),
        help="The output directory to save the dump results",
    )
    parser.add_argument(
        "--fwd_ckp_dir",
        type=pathlib.Path,
        default=pathlib.Path("/home/<USER>/local-ckp/dbrx-instruct-fwd"),
        help="The checkpoint directory with the fast-forward format",
    )
    args = parser.parse_args()

    prompt = {
        0: get_prompt_0(),
        1: get_prompt_1(),
        2: get_prompt_2(),
    }[args.prompt]

    tokenizer_1 = AutoTokenizer.from_pretrained(
        "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
        trust_remote_code=True,
        token="hf_YOUR_TOKEN",
    )
    tokenizer_2 = DBRXInstructTokenizer()
    tokens = tokenizer_1.encode(prompt)
    assert tokens == tokenizer_2.tokenize_unsafe(prompt)

    model_spec = get_llama_model_spec("dbrx-instruct")
    assert isinstance(model_spec, DbrxModelSpec)
    # convert_ckp_to_ffw(
    #     model_spec,
    #     output_dir=pathlib.Path("/home/<USER>/local-ckp/dbrx-instruct-2layer-fwd"),
    #     max_n_layers=2,
    # )

    convert_ckp_to_ffw(
        model_spec,
        output_dir=pathlib.Path("/home/<USER>/local-ckp/dbrx-instruct-fwd"),
    )

    # print("CUDA Available:", torch.cuda.is_available())
    # model_spec.checkpoint_path = args.fwd_ckp_dir
    # model = generate_step_fn(model_spec, num_processes=args.num_processes)
    # att_factory = DbrxAttentionFactory(model_spec, num_processes=args.num_processes)

    # attn_cache = att_factory(1024)
    # attn_cache.reset(0)
    # generated_tokens = []

    # next_prompt = tokens
    # # torch.tensor(tokens, dtype=torch.int32, device="cuda")
    # while len(generated_tokens) < 32:
    #     assert len(next_prompt) < cached_attention.MAX_Q_LEN
    #     scores = model(next_prompt, attn_cache)
    #     next_token = int(torch.argmax(scores[-1]).cpu().item())
    #     generated_tokens.append(next_token)
    #     next_prompt = [next_token]
    # generated_text = tokenizer_2.detokenize(generated_tokens)
    # print(f"Generated text: {generated_text}")
    # print("-" * 32)
