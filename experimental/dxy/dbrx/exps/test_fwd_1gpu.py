"""Test the DBRX fastfoward model.

CUDA_VISIBLE_DEVICES="0" python experimental/dxy/dbrx/exps/test_fwd_1gpu.py --prompt 0
"""

import argparse
import pathlib
import pdb

import torch
import tqdm
from base.fastforward import cached_attention
from base.fastforward.checkpoints import save_load
from base.fastforward.fwd_dbrx import (
    Dbrx,
    DbrxAttentionFactory,
    DbrxModelSpec,
    generate_step_fn,
)
from base.fastforward.model_specs import get_llama_model_spec
from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer
from experimental.dxy.dbrx.exps.test_samples import (
    get_prompt_0,
    get_prompt_1,
    get_prompt_2,
)
from experimental.dxy.dbrx.utils import DbrxHFCheckpoint
from research.core.utils_for_log import time_string
from transformers import AutoTokenizer

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--prompt",
        type=int,
        default=0,
        choices=(0, 1, 2),
        help="The prompt to use",
    )
    parser.add_argument(
        "--fwd_ckp_dir",
        type=pathlib.Path,
        default=pathlib.Path("/home/<USER>/local-ckp/dbrx-instruct-2layer-fwd"),
        help="The checkpoint directory with the fast-forward format",
    )
    args = parser.parse_args()

    prompt = {
        0: get_prompt_0(),
        1: get_prompt_1(),
        2: get_prompt_2(),
    }[args.prompt]

    tokenizer_1 = AutoTokenizer.from_pretrained(
        "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
        trust_remote_code=True,
        token="hf_YOUR_TOKEN",
    )
    tokenizer_2 = DBRXInstructTokenizer()
    tokens = tokenizer_1.encode(prompt)
    assert tokens == tokenizer_2.tokenize_unsafe(prompt)

    model_spec = get_llama_model_spec("dbrx-instruct")
    assert isinstance(model_spec, DbrxModelSpec)
    model_spec.num_layers = 3

    print(f"CUDA Available: {torch.cuda.is_available()}")
    model_spec.checkpoint_path = args.fwd_ckp_dir
    model = generate_step_fn(model_spec, num_processes=1)
    att_factory = DbrxAttentionFactory(model_spec, num_processes=1)

    attn_cache = att_factory(1024)
    attn_cache.reset(0)
    generated_tokens = []

    next_prompt = tokens
    # torch.tensor(tokens, dtype=torch.int32, device="cuda")
    while len(generated_tokens) < 1:
        assert len(next_prompt) < cached_attention.MAX_Q_LEN
        scores = model(next_prompt, attn_cache)
        next_token = int(torch.argmax(scores[-1]).cpu().item())
        generated_tokens.append(next_token)
        next_prompt = [next_token]
    generated_text = tokenizer_2.detokenize(generated_tokens)
    print(f"Generated text: {generated_text}")
    pdb.set_trace()
    print("-" * 32)
