"""Dump the different versions of DBRX model to compare.

CUDA_VISIBLE_DEVICES="0,1,2,3,4,5,6" python experimental/dxy/dbrx/exps/test_ref_model.py --prompt 0
"""

import argparse
import pdb

import torch
from experimental.dxy.dbrx.exps.test_samples import (get_prompt_0,
                                                     get_prompt_1,
                                                     get_prompt_2)
from research.core.utils import display_memory_info
from transformers import AutoModelForCausalLM, AutoTokenizer

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        choices=("dbrx", "dbrx_ref_cpu"),
        default="dbrx",
        help="The core LLM used for inference.",
    )
    parser.add_argument(
        "--prompt",
        type=int,
        default=0,
        choices=(0, 1, 2),
        help="The prompt to use",
    )
    args = parser.parse_args()

    display_memory_info()
    print("-" * 32)
    prompt = {
        0: get_prompt_0(),
        1: get_prompt_1(),
        2: get_prompt_2(),
    }[args.prompt]
    if args.model == "dbrx":
        tokenizer = AutoTokenizer.from_pretrained(
            "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
            trust_remote_code=True,
            token="hf_YOUR_TOKEN",
        )
        model = AutoModelForCausalLM.from_pretrained(
            "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
            device_map="auto",
            torch_dtype=torch.bfloat16,
            trust_remote_code=True,
            token="hf_YOUR_TOKEN",
        )
        tokens = tokenizer.encode(prompt)

        with torch.inference_mode():
            model.eval()
            input_ids = torch.tensor([tokens], dtype=torch.long).to("cuda")
            attention_mask = torch.ones_like(input_ids).to("cuda")
            outputs_generation = model.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,
                max_new_tokens=32,
            ).squeeze(dim=0)
            # Compute the logits
            outputs_w_logits = model(input_ids)
            logits = outputs_w_logits.logits.squeeze(dim=0)
            # Convert and return the results
            logits, outputs_generation = logits.cpu(), outputs_generation.cpu().tolist()
            assert outputs_generation[: len(tokens)] == tokens
            text_generated = tokenizer.decode(outputs_generation[len(tokens) :])
    pdb.set_trace()
    print("-" * 32)
    display_memory_info()
    print("-" * 32)
