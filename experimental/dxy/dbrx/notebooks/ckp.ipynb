{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pathlib\n", "from typing import Union\n", "from research.core import utils_for_file\n", "from safetensors import safe_open\n", "\n", "\n", "def _load_nested_value_from_config(config: dict, keys: Union[str, list[str]], default_value=None):\n", "    \"\"\"Load a nested value from a config.\"\"\"\n", "    current = config\n", "    if isinstance(keys, str):\n", "        keys = [keys]\n", "    for key in keys:\n", "        if key in current:\n", "            current = current[key]\n", "        elif default_value is not None:\n", "            return default_value\n", "        else:\n", "            raise ValueError(f\"Key {key} not found in config {config}\")\n", "    return current\n", "\n", "\n", "class DbrxHFCheckpoint:\n", "    \"\"\"A class for loading a HuggingFace checkpoint.\"\"\"\n", "\n", "    def __init__(self, ckp_dir: str):\n", "        self.ckp_dir = pathlib.Path(ckp_dir)\n", "        assert self.ckp_dir.exists()\n", "        self.config_json = self.ckp_dir / \"config.json\"\n", "        assert self.config_json.exists()\n", "        self.config = utils_for_file.read_json(self.config_json)\n", "        # read the hyper-parameters\n", "        self.d_model = self.config[\"d_model\"]\n", "        self.n_heads = self.config[\"n_heads\"]\n", "        self.n_layers = self.config[\"n_layers\"]\n", "        self.ffn_hidden_size = _load_nested_value_from_config(self.config, [\"ffn_config\", \"ffn_hidden_size\"])\n", "        self.moe_num_experts = _load_nested_value_from_config(self.config, [\"ffn_config\", \"moe_num_experts\"])\n", "        self.moe_top_k = _load_nested_value_from_config(self.config, [\"ffn_config\", \"moe_top_k\"])\n", "        self.max_seq_len = self.config[\"max_seq_len\"]\n", "        # load the model safetensor index\n", "        self.safetensor_index = utils_for_file.read_json(self.ckp_dir / \"model.safetensors.index.json\")\n", "        self.weight_map = self.safetensor_index[\"weight_map\"]\n", "        # create a weight buffer\n", "        self.weight_buffer = {}\n", "\n", "    def reduce_to_n_layers(self, n_layers: int):\n", "        assert n_layers <= self.n_layers\n", "        keys_to_delete = []\n", "        for index in range(n_layers, self.n_layers):\n", "            for key in self.weight_map:\n", "                if key.startswith(f\"transformer.blocks.{index}.\"):\n", "                    keys_to_delete.append(key)\n", "        total_keys = len(self.weight_map)\n", "        for key in keys_to_delete:\n", "            self.weight_map.pop(key)\n", "        self.config[\"n_layers\"] = n_layers\n", "        self.n_layers = n_layers\n", "        print(f\"Removed {len(keys_to_delete)}/{total_keys} keys.\")\n", "        return self\n", "\n", "    def load_all_weights(self):\n", "        all_safetensor_paths: set[pathlib.Path] = set()\n", "        required_weight_names = []\n", "        for weight_name, safetensor_filename in self.weight_map.items():\n", "            safetensor_path: pathlib.Path = self.ckp_dir / safetensor_filename\n", "            assert safetensor_path.exists(), f\"{safetensor_path} does not exist.\"\n", "            all_safetensor_paths.add(safetensor_path)\n", "            required_weight_names.append(weight_name)\n", "        for idx, safetensor_path in enumerate(all_safetensor_paths):\n", "            print(f\"Try to load {idx}/{len(all_safetensor_paths)}-th checkpoint.\")\n", "            with safe_open(safetensor_path, framework=\"pt\", device=\"cpu\") as f:\n", "                for key in f.keys():\n", "                    if key in self.weight_buffer:\n", "                        print(f\"Weight {key} already in the buffer skip.\")\n", "                        continue\n", "                    self.weight_buffer[key] = f.get_tensor(key)\n", "        for weight_name in required_weight_names:\n", "            if weight_name not in self.weight_buffer:\n", "                print(f\"Still missing {weight_name}!!!!!!!!\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt: Introduction to Large Language Models\n", "Tokens: [38255, 311, 20902, 11688, 27972]\n", "Removed 304/323 keys.\n"]}], "source": ["import sys\n", "import pathlib\n", "import importlib\n", "from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer\n", "from research.core.utils import display_memory_info\n", "from experimental.dxy.dbrx.reference import configuration_dbrx, modeling_dbrx\n", "\n", "DBRX_HF_DIR = \"/mnt/efs/augment/checkpoints/databricks/dbrx-instruct\"\n", "dbrx_ckp = DbrxHFCheckpoint(DBRX_HF_DIR)\n", "tokenizer = DBRXInstructTokenizer()\n", "prompt = \"Introduction to Large Language Models\"\n", "tokens = tokenizer.tokenize_safe(prompt)\n", "print(f\"Prompt: {prompt}\")\n", "print(f\"Tokens: {tokens}\")\n", "\n", "dbrx_ref_config = configuration_dbrx.DbrxConfig(**dbrx_ckp.config)\n", "\n", "dbrx_ckp_2_layers = DbrxHFCheckpoint(DBRX_HF_DIR).reduce_to_n_layers(2)\n", "dbrx_ref_config_2_layers = configuration_dbrx.DbrxConfig(**dbrx_ckp_2_layers.config)\n", "dbrx_ref_model_2_layers = modeling_dbrx.DbrxForCausalLM(dbrx_ref_config_2_layers)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# display_memory_info()\n", "\n", "dbrx_ckp.load_all_weights()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['transformer.blocks.17.ffn.experts.mlp.v1', 'transformer.blocks.17.ffn.experts.mlp.w1', 'transformer.blocks.5.ffn.experts.mlp.w2', 'transformer.blocks.6.ffn.experts.mlp.w1', 'transformer.blocks.6.ffn.router.layer.weight', 'transformer.blocks.6.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.6.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.6.norm_attn_norm.norm_1.weight', 'transformer.blocks.6.norm_attn_norm.norm_2.weight', 'transformer.blocks.39.ffn.experts.mlp.v1', 'transformer.blocks.39.ffn.experts.mlp.w1', 'transformer.blocks.9.ffn.experts.mlp.v1', 'transformer.blocks.9.ffn.experts.mlp.w1', 'transformer.blocks.21.ffn.experts.mlp.w2', 'transformer.blocks.22.ffn.experts.mlp.w1', 'transformer.blocks.22.ffn.router.layer.weight', 'transformer.blocks.22.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.22.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.22.norm_attn_norm.norm_1.weight', 'transformer.blocks.22.norm_attn_norm.norm_2.weight', 'transformer.blocks.30.ffn.experts.mlp.v1', 'transformer.blocks.30.ffn.experts.mlp.w2', 'transformer.blocks.31.ffn.router.layer.weight', 'transformer.blocks.31.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.31.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.31.norm_attn_norm.norm_1.weight', 'transformer.blocks.31.norm_attn_norm.norm_2.weight', 'transformer.blocks.19.ffn.experts.mlp.w2', 'transformer.blocks.20.ffn.experts.mlp.w1', 'transformer.blocks.20.ffn.router.layer.weight', 'transformer.blocks.20.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.20.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.20.norm_attn_norm.norm_1.weight', 'transformer.blocks.20.norm_attn_norm.norm_2.weight', 'transformer.blocks.25.ffn.experts.mlp.w2', 'transformer.blocks.26.ffn.experts.mlp.w1', 'transformer.blocks.26.ffn.router.layer.weight', 'transformer.blocks.26.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.26.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.26.norm_attn_norm.norm_1.weight', 'transformer.blocks.26.norm_attn_norm.norm_2.weight', 'transformer.blocks.4.ffn.experts.mlp.v1', 'transformer.blocks.4.ffn.experts.mlp.w2', 'transformer.blocks.5.ffn.router.layer.weight', 'transformer.blocks.5.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.5.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.5.norm_attn_norm.norm_1.weight', 'transformer.blocks.5.norm_attn_norm.norm_2.weight', 'transformer.blocks.2.ffn.experts.mlp.v1', 'transformer.blocks.2.ffn.experts.mlp.w2', 'transformer.blocks.3.ffn.router.layer.weight', 'transformer.blocks.3.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.3.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.3.norm_attn_norm.norm_1.weight', 'transformer.blocks.3.norm_attn_norm.norm_2.weight', 'transformer.blocks.5.ffn.experts.mlp.v1', 'transformer.blocks.5.ffn.experts.mlp.w1', 'transformer.blocks.8.ffn.experts.mlp.v1', 'transformer.blocks.8.ffn.experts.mlp.w2', 'transformer.blocks.9.ffn.router.layer.weight', 'transformer.blocks.9.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.9.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.9.norm_attn_norm.norm_1.weight', 'transformer.blocks.9.norm_attn_norm.norm_2.weight', 'transformer.blocks.23.ffn.experts.mlp.v1', 'transformer.blocks.23.ffn.experts.mlp.w1', 'transformer.blocks.14.ffn.experts.mlp.v1', 'transformer.blocks.14.ffn.experts.mlp.w2', 'transformer.blocks.15.ffn.router.layer.weight', 'transformer.blocks.15.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.15.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.15.norm_attn_norm.norm_1.weight', 'transformer.blocks.15.norm_attn_norm.norm_2.weight', 'transformer.blocks.38.ffn.experts.mlp.v1', 'transformer.blocks.38.ffn.experts.mlp.w2', 'transformer.blocks.39.ffn.router.layer.weight', 'transformer.blocks.39.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.39.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.39.norm_attn_norm.norm_1.weight', 'transformer.blocks.39.norm_attn_norm.norm_2.weight', 'transformer.blocks.25.ffn.experts.mlp.v1', 'transformer.blocks.25.ffn.experts.mlp.w1', 'transformer.blocks.15.ffn.experts.mlp.w2', 'transformer.blocks.16.ffn.experts.mlp.w1', 'transformer.blocks.16.ffn.router.layer.weight', 'transformer.blocks.16.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.16.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.16.norm_attn_norm.norm_1.weight', 'transformer.blocks.16.norm_attn_norm.norm_2.weight', 'transformer.blocks.7.ffn.experts.mlp.w2', 'transformer.blocks.8.ffn.experts.mlp.w1', 'transformer.blocks.8.ffn.router.layer.weight', 'transformer.blocks.8.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.8.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.8.norm_attn_norm.norm_1.weight', 'transformer.blocks.8.norm_attn_norm.norm_2.weight', 'transformer.blocks.3.ffn.experts.mlp.w2', 'transformer.blocks.4.ffn.experts.mlp.w1', 'transformer.blocks.4.ffn.router.layer.weight', 'transformer.blocks.4.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.4.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.4.norm_attn_norm.norm_1.weight', 'transformer.blocks.4.norm_attn_norm.norm_2.weight', 'transformer.blocks.13.ffn.experts.mlp.v1', 'transformer.blocks.13.ffn.experts.mlp.w1', 'transformer.blocks.33.ffn.experts.mlp.v1', 'transformer.blocks.33.ffn.experts.mlp.w1', 'transformer.blocks.26.ffn.experts.mlp.v1', 'transformer.blocks.26.ffn.experts.mlp.w2', 'transformer.blocks.27.ffn.router.layer.weight', 'transformer.blocks.27.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.27.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.27.norm_attn_norm.norm_1.weight', 'transformer.blocks.27.norm_attn_norm.norm_2.weight', 'transformer.blocks.0.ffn.experts.mlp.w1', 'transformer.blocks.0.ffn.router.layer.weight', 'transformer.blocks.0.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.0.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.0.norm_attn_norm.norm_1.weight', 'transformer.blocks.0.norm_attn_norm.norm_2.weight', 'transformer.wte.weight', 'transformer.blocks.1.ffn.experts.mlp.v1', 'transformer.blocks.1.ffn.experts.mlp.w1', 'transformer.blocks.15.ffn.experts.mlp.v1', 'transformer.blocks.15.ffn.experts.mlp.w1', 'transformer.blocks.23.ffn.experts.mlp.w2', 'transformer.blocks.24.ffn.experts.mlp.w1', 'transformer.blocks.24.ffn.router.layer.weight', 'transformer.blocks.24.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.24.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.24.norm_attn_norm.norm_1.weight', 'transformer.blocks.24.norm_attn_norm.norm_2.weight', 'transformer.blocks.27.ffn.experts.mlp.v1', 'transformer.blocks.27.ffn.experts.mlp.w1', 'transformer.blocks.27.ffn.experts.mlp.w2', 'transformer.blocks.28.ffn.experts.mlp.w1', 'transformer.blocks.28.ffn.router.layer.weight', 'transformer.blocks.28.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.28.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.28.norm_attn_norm.norm_1.weight', 'transformer.blocks.28.norm_attn_norm.norm_2.weight', 'transformer.blocks.3.ffn.experts.mlp.v1', 'transformer.blocks.3.ffn.experts.mlp.w1', 'transformer.blocks.35.ffn.experts.mlp.w2', 'transformer.blocks.36.ffn.experts.mlp.w1', 'transformer.blocks.36.ffn.router.layer.weight', 'transformer.blocks.36.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.36.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.36.norm_attn_norm.norm_1.weight', 'transformer.blocks.36.norm_attn_norm.norm_2.weight', 'transformer.blocks.29.ffn.experts.mlp.w2', 'transformer.blocks.30.ffn.experts.mlp.w1', 'transformer.blocks.30.ffn.router.layer.weight', 'transformer.blocks.30.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.30.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.30.norm_attn_norm.norm_1.weight', 'transformer.blocks.30.norm_attn_norm.norm_2.weight', 'transformer.blocks.31.ffn.experts.mlp.v1', 'transformer.blocks.31.ffn.experts.mlp.w1', 'transformer.blocks.32.ffn.experts.mlp.v1', 'transformer.blocks.32.ffn.experts.mlp.w2', 'transformer.blocks.33.ffn.router.layer.weight', 'transformer.blocks.33.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.33.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.33.norm_attn_norm.norm_1.weight', 'transformer.blocks.33.norm_attn_norm.norm_2.weight', 'transformer.blocks.34.ffn.experts.mlp.v1', 'transformer.blocks.34.ffn.experts.mlp.w2', 'transformer.blocks.35.ffn.router.layer.weight', 'transformer.blocks.35.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.35.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.35.norm_attn_norm.norm_1.weight', 'transformer.blocks.35.norm_attn_norm.norm_2.weight', 'transformer.blocks.29.ffn.experts.mlp.v1', 'transformer.blocks.29.ffn.experts.mlp.w1', 'transformer.blocks.28.ffn.experts.mlp.v1', 'transformer.blocks.28.ffn.experts.mlp.w2', 'transformer.blocks.29.ffn.router.layer.weight', 'transformer.blocks.29.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.29.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.29.norm_attn_norm.norm_1.weight', 'transformer.blocks.29.norm_attn_norm.norm_2.weight', 'transformer.blocks.6.ffn.experts.mlp.v1', 'transformer.blocks.6.ffn.experts.mlp.w2', 'transformer.blocks.7.ffn.router.layer.weight', 'transformer.blocks.7.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.7.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.7.norm_attn_norm.norm_1.weight', 'transformer.blocks.7.norm_attn_norm.norm_2.weight', 'transformer.blocks.10.ffn.experts.mlp.v1', 'transformer.blocks.10.ffn.experts.mlp.w2', 'transformer.blocks.11.ffn.router.layer.weight', 'transformer.blocks.11.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.11.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.11.norm_attn_norm.norm_1.weight', 'transformer.blocks.11.norm_attn_norm.norm_2.weight', 'transformer.blocks.17.ffn.experts.mlp.w2', 'transformer.blocks.18.ffn.experts.mlp.w1', 'transformer.blocks.18.ffn.router.layer.weight', 'transformer.blocks.18.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.18.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.18.norm_attn_norm.norm_1.weight', 'transformer.blocks.18.norm_attn_norm.norm_2.weight', 'transformer.blocks.13.ffn.experts.mlp.w2', 'transformer.blocks.14.ffn.experts.mlp.w1', 'transformer.blocks.14.ffn.router.layer.weight', 'transformer.blocks.14.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.14.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.14.norm_attn_norm.norm_1.weight', 'transformer.blocks.14.norm_attn_norm.norm_2.weight', 'transformer.blocks.16.ffn.experts.mlp.v1', 'transformer.blocks.16.ffn.experts.mlp.w2', 'transformer.blocks.17.ffn.router.layer.weight', 'transformer.blocks.17.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.17.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.17.norm_attn_norm.norm_1.weight', 'transformer.blocks.17.norm_attn_norm.norm_2.weight', 'transformer.blocks.11.ffn.experts.mlp.v1', 'transformer.blocks.11.ffn.experts.mlp.w1', 'transformer.blocks.24.ffn.experts.mlp.v1', 'transformer.blocks.24.ffn.experts.mlp.w2', 'transformer.blocks.25.ffn.router.layer.weight', 'transformer.blocks.25.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.25.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.25.norm_attn_norm.norm_1.weight', 'transformer.blocks.25.norm_attn_norm.norm_2.weight', 'lm_head.weight', 'transformer.blocks.39.ffn.experts.mlp.w2', 'transformer.norm_f.weight', 'transformer.blocks.20.ffn.experts.mlp.v1', 'transformer.blocks.20.ffn.experts.mlp.w2', 'transformer.blocks.21.ffn.router.layer.weight', 'transformer.blocks.21.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.21.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.21.norm_attn_norm.norm_1.weight', 'transformer.blocks.21.norm_attn_norm.norm_2.weight', 'transformer.blocks.33.ffn.experts.mlp.w2', 'transformer.blocks.34.ffn.experts.mlp.w1', 'transformer.blocks.34.ffn.router.layer.weight', 'transformer.blocks.34.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.34.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.34.norm_attn_norm.norm_1.weight', 'transformer.blocks.34.norm_attn_norm.norm_2.weight', 'transformer.blocks.12.ffn.experts.mlp.v1', 'transformer.blocks.12.ffn.experts.mlp.w2', 'transformer.blocks.13.ffn.router.layer.weight', 'transformer.blocks.13.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.13.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.13.norm_attn_norm.norm_1.weight', 'transformer.blocks.13.norm_attn_norm.norm_2.weight', 'transformer.blocks.35.ffn.experts.mlp.v1', 'transformer.blocks.35.ffn.experts.mlp.w1', 'transformer.blocks.21.ffn.experts.mlp.v1', 'transformer.blocks.21.ffn.experts.mlp.w1', 'transformer.blocks.31.ffn.experts.mlp.w2', 'transformer.blocks.32.ffn.experts.mlp.w1', 'transformer.blocks.32.ffn.router.layer.weight', 'transformer.blocks.32.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.32.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.32.norm_attn_norm.norm_1.weight', 'transformer.blocks.32.norm_attn_norm.norm_2.weight', 'transformer.blocks.11.ffn.experts.mlp.w2', 'transformer.blocks.12.ffn.experts.mlp.w1', 'transformer.blocks.12.ffn.router.layer.weight', 'transformer.blocks.12.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.12.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.12.norm_attn_norm.norm_1.weight', 'transformer.blocks.12.norm_attn_norm.norm_2.weight', 'transformer.blocks.37.ffn.experts.mlp.v1', 'transformer.blocks.37.ffn.experts.mlp.w1', 'transformer.blocks.37.ffn.experts.mlp.w2', 'transformer.blocks.38.ffn.experts.mlp.w1', 'transformer.blocks.38.ffn.router.layer.weight', 'transformer.blocks.38.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.38.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.38.norm_attn_norm.norm_1.weight', 'transformer.blocks.38.norm_attn_norm.norm_2.weight', 'transformer.blocks.0.ffn.experts.mlp.v1', 'transformer.blocks.0.ffn.experts.mlp.w2', 'transformer.blocks.1.ffn.router.layer.weight', 'transformer.blocks.1.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.1.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.1.norm_attn_norm.norm_1.weight', 'transformer.blocks.1.norm_attn_norm.norm_2.weight', 'transformer.blocks.36.ffn.experts.mlp.v1', 'transformer.blocks.36.ffn.experts.mlp.w2', 'transformer.blocks.37.ffn.router.layer.weight', 'transformer.blocks.37.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.37.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.37.norm_attn_norm.norm_1.weight', 'transformer.blocks.37.norm_attn_norm.norm_2.weight', 'transformer.blocks.7.ffn.experts.mlp.v1', 'transformer.blocks.7.ffn.experts.mlp.w1', 'transformer.blocks.1.ffn.experts.mlp.w2', 'transformer.blocks.2.ffn.experts.mlp.w1', 'transformer.blocks.2.ffn.router.layer.weight', 'transformer.blocks.2.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.2.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.2.norm_attn_norm.norm_1.weight', 'transformer.blocks.2.norm_attn_norm.norm_2.weight', 'transformer.blocks.22.ffn.experts.mlp.v1', 'transformer.blocks.22.ffn.experts.mlp.w2', 'transformer.blocks.23.ffn.router.layer.weight', 'transformer.blocks.23.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.23.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.23.norm_attn_norm.norm_1.weight', 'transformer.blocks.23.norm_attn_norm.norm_2.weight', 'transformer.blocks.10.ffn.experts.mlp.w1', 'transformer.blocks.10.ffn.router.layer.weight', 'transformer.blocks.10.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.10.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.10.norm_attn_norm.norm_1.weight', 'transformer.blocks.10.norm_attn_norm.norm_2.weight', 'transformer.blocks.9.ffn.experts.mlp.w2', 'transformer.blocks.18.ffn.experts.mlp.v1', 'transformer.blocks.18.ffn.experts.mlp.w2', 'transformer.blocks.19.ffn.router.layer.weight', 'transformer.blocks.19.norm_attn_norm.attn.Wqkv.weight', 'transformer.blocks.19.norm_attn_norm.attn.out_proj.weight', 'transformer.blocks.19.norm_attn_norm.norm_1.weight', 'transformer.blocks.19.norm_attn_norm.norm_2.weight', 'transformer.blocks.19.ffn.experts.mlp.v1', 'transformer.blocks.19.ffn.experts.mlp.w1'])\n"]}], "source": ["print(dbrx_ckp.weight_buffer.keys())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[-1.0864e-02,  1.1292e-02,  1.1047e-02,  ...,  1.6724e-02,\n", "         -3.5477e-04, -7.9956e-03],\n", "        [ 5.7068e-03, -3.5889e-02,  3.8624e-05,  ...,  1.0681e-02,\n", "          1.9409e-02,  2.8198e-02],\n", "        [-7.4158e-03,  6.0425e-03,  1.2589e-03,  ..., -1.1292e-02,\n", "          2.9175e-02, -1.3275e-03],\n", "        ...,\n", "        [ 1.5991e-02,  1.3855e-02,  2.5879e-02,  ...,  1.3245e-02,\n", "         -5.7068e-03,  5.3101e-03],\n", "        [-2.6131e-04, -1.6098e-03,  2.4567e-03,  ..., -1.4877e-04,\n", "          4.6082e-03,  1.8188e-02],\n", "        [-1.9287e-02,  7.7820e-03, -1.9897e-02,  ...,  3.4943e-03,\n", "          7.9956e-03,  2.6611e-02]], dtype=torch.bfloat16)\n"]}], "source": ["print(dbrx_ckp.weight_buffer[\"transformer.blocks.17.ffn.experts.mlp.v1\"])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total Memory: 251.69 GB\n", "Available Memory: 215.24 GB\n", "Used Memory: 34.26 GB\n"]}], "source": ["display_memory_info()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DbrxForCausalLM(\n", "  (transformer): DbrxModel(\n", "    (wte): Embedding(100352, 6144)\n", "    (blocks): ModuleList(\n", "      (0-1): 2 x DbrxBlock(\n", "        (norm_attn_norm): DbrxNormAttentionNorm(\n", "          (norm_1): LayerNorm((6144,), eps=1e-05, elementwise_affine=True)\n", "          (attn): DbrxAttention(\n", "            (Wqkv): Linear(in_features=6144, out_features=8192, bias=False)\n", "            (out_proj): Linear(in_features=6144, out_features=6144, bias=False)\n", "            (rotary_emb): DbrxRotaryEmbedding()\n", "          )\n", "          (norm_2): LayerNorm((6144,), eps=1e-05, elementwise_affine=True)\n", "        )\n", "        (ffn): DbrxFFN(\n", "          (router): DbrxRouter(\n", "            (layer): Linear(in_features=6144, out_features=16, bias=False)\n", "          )\n", "          (experts): DbrxExperts(\n", "            (mlp): DbrxExpertGLU()\n", "          )\n", "        )\n", "      )\n", "    )\n", "    (norm_f): LayerNorm((6144,), eps=1e-05, elementwise_affine=True)\n", "  )\n", "  (lm_head): Linear(in_features=6144, out_features=100352, bias=False)\n", ")\n"]}], "source": ["print(dbrx_ref_model_2_layers)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DbrxBlock(\n", "  (norm_attn_norm): DbrxNormAttentionNorm(\n", "    (norm_1): LayerNorm((6144,), eps=1e-05, elementwise_affine=True)\n", "    (attn): DbrxAttention(\n", "      (Wqkv): Linear(in_features=6144, out_features=8192, bias=False)\n", "      (out_proj): Linear(in_features=6144, out_features=6144, bias=False)\n", "      (rotary_emb): DbrxRotaryEmbedding()\n", "    )\n", "    (norm_2): LayerNorm((6144,), eps=1e-05, elementwise_affine=True)\n", "  )\n", "  (ffn): DbrxFFN(\n", "    (router): DbrxRouter(\n", "      (layer): Linear(in_features=6144, out_features=16, bias=False)\n", "    )\n", "    (experts): DbrxExperts(\n", "      (mlp): DbrxExpertGLU()\n", "    )\n", "  )\n", ")\n", "transformer.wte.weight\n", "transformer.blocks.0.norm_attn_norm.norm_1.weight\n", "transformer.blocks.0.norm_attn_norm.attn.Wqkv.weight\n", "transformer.blocks.0.norm_attn_norm.attn.out_proj.weight\n", "transformer.blocks.0.norm_attn_norm.norm_2.weight\n", "transformer.blocks.0.ffn.router.layer.weight\n", "transformer.blocks.0.ffn.experts.mlp.w1\n", "transformer.blocks.0.ffn.experts.mlp.v1\n", "transformer.blocks.0.ffn.experts.mlp.w2\n", "transformer.blocks.1.norm_attn_norm.norm_1.weight\n", "transformer.blocks.1.norm_attn_norm.attn.Wqkv.weight\n", "transformer.blocks.1.norm_attn_norm.attn.out_proj.weight\n", "transformer.blocks.1.norm_attn_norm.norm_2.weight\n", "transformer.blocks.1.ffn.router.layer.weight\n", "transformer.blocks.1.ffn.experts.mlp.w1\n", "transformer.blocks.1.ffn.experts.mlp.v1\n", "transformer.blocks.1.ffn.experts.mlp.w2\n", "transformer.norm_f.weight\n", "lm_head.weight\n"]}], "source": ["print(dbrx_ref_model_2_layers.transformer.blocks[1])\n", "\n", "for key, weights in dbrx_ref_model_2_layers.named_parameters():\n", "    print(key)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}