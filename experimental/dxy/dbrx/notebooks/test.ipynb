{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from base.fastforward.fwd_model_output import SamplingConfig\n", "\n", "\n", "def sample_v1(logits, config: SamplingConfig) -> torch.Tensor:\n", "    top_logits, top_indices = torch.topk(logits, config.top_k, dim=-1)\n", "    top_probs = torch.softmax(top_logits, dim=-1)\n", "    # Create the random generator so that for the same rng_seed, it will return the same result\n", "    if config.rng_seed is None:\n", "        generator = None\n", "    else:\n", "        generator = torch.Generator(device=logits.device)\n", "        generator.manual_seed(config.rng_seed)\n", "    print(top_probs)\n", "    next_token_index = torch.multinomial(top_probs, num_samples=1, generator=generator)\n", "    next_token = torch.gather(top_indices, dim=-1, index=next_token_index)\n", "    return next_token\n", "\n", "\n", "def sample_v2(logits, config: SamplingConfig) -> torch.Tensor:\n", "    probs = torch.nn.functional.softmax(logits / config.temperature, dim=-1)\n", "    top_probs, top_indices = torch.topk(probs, config.top_k, dim=-1)\n", "    top_probs = top_probs / torch.sum(top_probs, dim=-1, keepdim=True)\n", "    # Create the random generator so that for the same rng_seed, it will return the same result\n", "    if config.rng_seed is None:\n", "        generator = None\n", "    else:\n", "        generator = torch.Generator(device=logits.device)\n", "        generator.manual_seed(config.rng_seed)\n", "    print(top_probs)\n", "    next_token_index = torch.multinomial(top_probs, num_samples=1, generator=generator)\n", "    next_token = torch.gather(top_indices, dim=-1, index=next_token_index)\n", "    return next_token"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["shape = (4, 8)\n", "tensor = torch.randn(*shape)\n", "out_v1 = sample_v1(tensor, SamplingConfig(top_k=2, temperature=1.0, rng_seed=2))\n", "out_v2 = sample_v2(tensor, SamplingConfig(top_k=2, temperature=1.0, rng_seed=2))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}