{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "tokenizer = AutoTokenizer.from_pretrained(\"databricks/dbrx-instruct\", trust_remote_code=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\"how are you\"))\n", "print(tokenizer.encode(\"董宣毅\"))\n", "print(tokenizer.encode((\"void quicksort(std::vector<int> & i)\")))\n", "print(tokenizer.encode(\"if a == 1:\\n    print(a)\\nelse:\\n\\t\\tprint(a)\\n\"))\n", "print(tokenizer.encode(\"\"\"\\\n", "def foo():\n", "    if a == 1:\n", "        print(a, arg =\"foo\")\n", "    return a\n", "\"\"\"))\n", "print(tokenizer.encode(\"1\"))\n", "print(tokenizer.encode(\"11\"))\n", "print(tokenizer.encode(\"111\"))\n", "print(tokenizer.encode(\"1111\"))\n", "print(tokenizer.encode(\"11111111111\"))\n", "print(tokenizer.encode(\"99000\"))\n", "print(tokenizer.encode(\"0 0\"))\n", "print(tokenizer.encode(\" \"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(tokenizer.encode(\"<|endoftext|>\"))\n", "print(tokenizer.encode(\"<|\"))\n", "print(tokenizer.encode(\"endoftext\"))\n", "print(tokenizer.encode(\"|>\"))\n", "print(tokenizer.encode(\"<|skip|>\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.dbrx_tokenizer import DBRXInstructTokenizer\n", "\n", "tokenizer = DBRXInstructTokenizer()\n", "print(tokenizer.tokenize_unsafe(\"how are you\"))\n", "print(tokenizer.tokenize_unsafe(\"董宣毅\"))\n", "print(tokenizer.tokenize_unsafe((\"void quicksort(std::vector<int> & i)\")))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.dbrx_tokenizer import pathlib, json, data_gym, _get_additional_special_tokens\n", "\n", "vocab_file = pathlib.Path(\"/home/<USER>/src/augment/base/tokenizers/dbrx_instruct_vocab.json\")\n", "\n", "with vocab_file.open(encoding=\"utf-8\") as vocab_file:\n", "        all_contents = json.load(vocab_file)\n", "        vocab: dict[str, int] = all_contents[\"model\"][\"vocab\"]\n", "        special_tokens: dict[str, int] = {\n", "            x[\"content\"]: x[\"id\"] for x in all_contents[\"added_tokens\"]\n", "        }\n", "        set_of_vocab_ids = set(vocab.values())\n", "        set_of_all_ids = set(vocab.values()) | set(special_tokens.values())\n", "        assert len(set_of_vocab_ids) == len(vocab)\n", "        for special_token, special_token_id in special_tokens.items():\n", "            if special_token in vocab:\n", "                assert special_token_id == vocab[special_token]\n", "        for token_id in range(len(vocab)):\n", "            assert token_id in set_of_vocab_ids\n", "        for token_id in range(len(set_of_all_ids)):\n", "            assert token_id in set_of_all_ids\n", "\n", "        for idx, token in enumerate(_get_additional_special_tokens()):\n", "            special_tokens[token] = len(set_of_all_ids) + idx\n", "    # # Byte sequences corresponding to tokens are data gym in vocab.json\n", "    # decoder = data_gym.DataGymDecoder()\n", "    # decode_data_gym = decoder.decode\n", "    # bpe_ranks: dict[bytes, int] = {}\n", "    # for gym, token_id in vocab.items():\n", "    #     bpe_ranks[decode_data_gym(gym)] = token_id\n", "    # special_ranks: dict[str, int] = {}\n", "    # for token, token_id in special_tokens.items():\n", "    #     assert token not in special_ranks\n", "    #     special_ranks[token] = token_id\n", "    # return bpe_ranks, special_ranks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["special_tokens"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(vocab))\n", "print(len(special_tokens))\n", "print(len(set_of_all_ids))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for token, id in vocab.items():\n", "    if id >= 100256:\n", "        print(token, id)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(_get_additional_special_tokens()))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}