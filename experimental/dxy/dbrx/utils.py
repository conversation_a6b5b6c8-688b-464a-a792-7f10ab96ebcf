import pathlib
from typing import Union

import tqdm
from safetensors import safe_open

from research.core import utils_for_file


def _load_nested_value_from_config(
    config: dict, keys: Union[str, list[str]], default_value=None
):
    """Load a nested value from a config."""
    current = config
    if isinstance(keys, str):
        keys = [keys]
    for key in keys:
        if key in current:
            current = current[key]
        elif default_value is not None:
            return default_value
        else:
            raise ValueError(f"Key {key} not found in config {config}")
    return current


class DbrxHFCheckpoint:
    """A class for loading a HuggingFace checkpoint."""

    def __init__(self, ckp_dir: str):
        self.ckp_dir = pathlib.Path(ckp_dir)
        assert self.ckp_dir.exists()
        self.config_json = self.ckp_dir / "config.json"
        assert self.config_json.exists()
        self.config = utils_for_file.read_json(self.config_json)
        # read the hyper-parameters
        self.d_model = self.config["d_model"]
        self.n_heads = self.config["n_heads"]
        self.n_layers = self.config["n_layers"]
        self.ffn_hidden_size = _load_nested_value_from_config(
            self.config, ["ffn_config", "ffn_hidden_size"]
        )
        self.moe_num_experts = _load_nested_value_from_config(
            self.config, ["ffn_config", "moe_num_experts"]
        )
        self.moe_top_k = _load_nested_value_from_config(
            self.config, ["ffn_config", "moe_top_k"]
        )
        self.max_seq_len = self.config["max_seq_len"]
        # load the model safetensor index
        self.safetensor_index = utils_for_file.read_json(
            self.ckp_dir / "model.safetensors.index.json"
        )
        self.weight_map = self.safetensor_index["weight_map"]
        # create a weight buffer
        self.weight_buffer = {}

    def reduce_to_n_layers(self, n_layers: int):
        assert n_layers <= self.n_layers
        keys_to_delete = []
        for index in range(n_layers, self.n_layers):
            for key in self.weight_map:
                if key.startswith(f"transformer.blocks.{index}."):
                    keys_to_delete.append(key)
        total_keys = len(self.weight_map)
        for key in keys_to_delete:
            self.weight_map.pop(key)
        self.config["n_layers"] = n_layers
        self.n_layers = n_layers
        print(f"Removed {len(keys_to_delete)}/{total_keys} keys.")
        return self

    def load_all_weights(self):
        all_safetensor_paths: set[pathlib.Path] = set()
        required_weight_names = []
        for weight_name, safetensor_filename in self.weight_map.items():
            safetensor_path: pathlib.Path = self.ckp_dir / safetensor_filename
            assert safetensor_path.exists(), f"{safetensor_path} does not exist."
            all_safetensor_paths.add(safetensor_path)
            required_weight_names.append(weight_name)
        for safetensor_path in tqdm.tqdm(
            all_safetensor_paths,
            desc="Loading weights from ckp",
            total=len(all_safetensor_paths),
        ):
            with safe_open(safetensor_path, framework="pt", device="cpu") as f:  # type: ignore
                for key in f.keys():
                    if key in self.weight_buffer:
                        print(f"Weight {key} already in the buffer skip.")
                        continue
                    self.weight_buffer[key] = f.get_tensor(key)
        for weight_name in required_weight_names:
            if weight_name not in self.weight_buffer:
                print(f"Still missing {weight_name}!!!!!!!!")
