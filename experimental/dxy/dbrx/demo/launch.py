"""Launch a simple HTTP server for visualization.

python experimental/dxy/dbrx/demo/launch.py --port 7860 --model dbrx
python experimental/dxy/dbrx/demo/launch.py --port 7860 --model fake
"""

import argparse

import gradio as gr
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer


class GlobalState:
    global_call: int = 0
    chat_history: list[tuple[str, str]] = []


class System:
    def __init__(self):
        self.tokenizer = None
        self.model = None

    def initialize(self, name: str):
        if name == "dbrx":
            self.tokenizer = AutoTokenizer.from_pretrained(
                "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
                trust_remote_code=True,
                token="hf_YOUR_TOKEN",
            )

            self.model = AutoModelForCausalLM.from_pretrained(
                "/mnt/efs/augment/checkpoints/databricks/dbrx-instruct/",
                device_map="auto",
                torch_dtype=torch.bfloat16,
                trust_remote_code=True,
                token="hf_YOUR_TOKEN",
            )
        elif name == "fake":

            class FakeToken(dict):
                def __init__(self):
                    super().__init__()
                    self.input_ids = torch.tensor([0])

                def to(self, *args, **kwargs):
                    return self

            class FakeTokenizer:
                def apply_chat_template(self, *args, **kwargs):
                    return FakeToken()

                def decode(self, *args, **kwargs):
                    return "fake"

            self.tokenizer = FakeTokenizer()
        else:
            raise ValueError(f"Unknown model name: {name}")


state = GlobalState()
system = System()


def call_model_to_generate_response(message: str):
    tokenizer, model = system.tokenizer, system.model
    assert tokenizer is not None
    assert message is not None
    messages = []
    for user_message, assistant_message in state.chat_history:
        messages.append({"role": "user", "content": user_message})
        messages.append({"role": "assistant", "content": assistant_message})
    messages.append({"role": "user", "content": message})
    input_ids = tokenizer.apply_chat_template(
        messages,
        return_dict=True,
        tokenize=True,
        add_generation_prompt=True,
        return_tensors="pt",
    ).to(
        "cuda"
    )  # type: ignore
    # Get the real inputs
    prompt_tokens = input_ids.input_ids.squeeze().tolist()
    prompt_text = tokenizer.decode(prompt_tokens)

    if model is None:
        response: str = "fake response"
    else:
        outputs = model.generate(**input_ids, max_new_tokens=200)
        all_tokens = outputs.squeeze().tolist()
        assert all_tokens[: len(prompt_tokens)] == prompt_tokens
        real_output_tokens = all_tokens[len(prompt_tokens) :]
        response: str = tokenizer.decode(real_output_tokens)
    # Update the chat history
    state.chat_history.append((message, response))
    # Internal logging
    print("-" * 16 + f" {state.global_call}-th call " + "-" * 16)
    print(f"Last Prompt tokens: {prompt_tokens}")
    print(f"Last Prompt text: {prompt_text}")
    state.global_call += 1
    # Build the entire chat history
    formatted_histories = []
    for user_message, assistant_message in state.chat_history:
        # formatted_histories.append(f"<div><b>User:</b> {user_message}</div>")
        # formatted_histories.append(f"<div><b>Assistant:</b> {assistant_message}</div>")
        formatted_histories.append(
            f'<div style="text-align: left; margin-bottom: 0.5em;"><strong style="color: #4A90E2;">User:</strong> <div style="display: inline-block; background: #e7e7e7; padding: 5px; border-radius: 5px;">{user_message}</div></div>'
        )
        formatted_histories.append(
            f'<div style="text-align: right; margin-bottom: 1em;"><strong style="color: #4CAF50;">Assistant:</strong> <div style="display: inline-block; background: #dff0d8; padding: 5px; border-radius: 5px;">{assistant_message}</div></div>'
        )
    return (
        """<div style="font-family: Arial, sans-serif; font-size: 0.9em;">
{content}
</div>""".format(
            content="\n".join(formatted_histories)
        ),
        prompt_tokens,
        prompt_text,
        "",
    )


def clear_state():
    state.chat_history = []
    return "", "", "", ""


with gr.Blocks() as demo:
    gr.Markdown("# Try the DBRX model.")

    with gr.Row():
        with gr.Column():
            # chat_history_display = gr.Textbox(
            #     label="Chat History", interactive=False, interpretation="html"
            # )
            chat_history_display = gr.HTML()
            message = gr.Textbox(label="Your Message")
            with gr.Row():
                submit_button = gr.Button("Submit")
                cancel_button = gr.Button("🗑️ Cancel")
        with gr.Column():
            prompt_tokens_display = gr.Textbox(label="Prompt Tokens", interactive=False)
            prompt_text_display = gr.Textbox(label="Prompt Text", interactive=False)

    submit_button.click(
        call_model_to_generate_response,
        inputs=message,
        outputs=[
            chat_history_display,
            prompt_tokens_display,
            prompt_text_display,
            message,
        ],
    )
    message.submit(
        call_model_to_generate_response,
        inputs=message,
        outputs=[
            chat_history_display,
            prompt_tokens_display,
            prompt_text_display,
            message,
        ],
    )
    cancel_button.click(
        clear_state,
        outputs=[
            chat_history_display,
            prompt_tokens_display,
            prompt_text_display,
            message,
        ],
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--model",
        type=str,
        choices=("dbrx", "fake"),
        default="dbrx",
        help="The core LLM used for inference.",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=7860,
        help="The port to send request to this server.",
    )
    args = parser.parse_args()
    system.initialize(args.model)
    demo.launch(server_name="0.0.0.0", server_port=args.port)
