#!/bin/bash
# bash experimental/dxy/fbw/try-dsc-v2-base.sh
#
set -e

# Automatically get the current directory
current_dir=$(dirname "$(realpath "${BASH_SOURCE[0]}")")
echo "Current directory: $current_dir"

# Set up the output directory.
out_dir=$HOME/fastbackward_outputs
echo "Output directory: $out_dir"

# Run locally.
cd research/fastbackward

python train.py \
    configs/deepseek_coder_v2_lite.py \
    --out_dir=${out_dir} \
    --n_layers=4 \
    --block_size=4096 \
    --max_iters=10 \
    --lr_decay_iters=10 \
    --eval_interval=3 \
    --eval_items=10 \
    --train_data_path=/mnt/efs/augment/user/zhewei/data/starcoder/filename_match/training \
    --eval_data_path=/mnt/efs/augment/user/zhewei/data/starcoder/filename_match/validation \
    --model_vocab_size=51200 \
    --batch_size=2 \
    --gradient_accumulation_steps=2 \
    --use_activation_checkpointing=False \
    --run_name=demo_test \
    --wandb_project=""

cd $current_dir
