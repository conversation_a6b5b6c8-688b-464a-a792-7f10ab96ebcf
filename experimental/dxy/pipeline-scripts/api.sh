#!/bin/bash
#
# Usage:
# AUGMENT_CHECKPOINTS_ROOT=/home/<USER>/checkpoints CUDA_VISIBLE_DEVICES=0 \
#   bash ...
#
# bash experimental/dxy/pipeline-scripts/api.sh --patch google/pyglove:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
# bash experimental/dxy/pipeline-scripts/api.sh --patch pydantic/pydantic:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
# bash experimental/dxy/pipeline-scripts/api.sh --patch thealgorithms/python:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
#
# bash experimental/dxy/pipeline-scripts/api.sh --hydra2patch google/pyglove:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
# bash experimental/dxy/pipeline-scripts/api.sh --hydra2patch pydantic/pydantic:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
# bash experimental/dxy/pipeline-scripts/api.sh --hydra2patch thealgorithms/python:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
#
# bash experimental/dxy/pipeline-scripts/api.sh --syshydra2patch google/pyglove:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
# bash experimental/dxy/pipeline-scripts/api.sh --syshydra2patch pydantic/pydantic:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
#
# bash experimental/dxy/pipeline-scripts/api.sh --build_dataset google/pyglove:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
# bash experimental/dxy/pipeline-scripts/api.sh --build_dataset pydantic/pydantic:v1.0 /mnt/efs/augment/user/dxy/data-patches-v4
#
set -e
echo script name: $0
echo $# arguments
if [ "$#" -ne 3 ] ;then
  echo "Input illegal number of parameters " $#
  echo "Need 3 parameters for mode, image name and save_dir"
  exit 1
fi

mode=$1
image_name=$2
save_dir=$3
# core_file="debug"
core_file="ge2lines_or_ge3paren"
sanitized_name=$(python3 -c "from augment.research.core.utils_for_str import sanitize_filename; print(sanitize_filename('$image_name'))")
echo "The script should save data into ${save_dir}/${sanitized_name}"


init_program(){
    python experimental/dxy/exps/generate_nodes_for_api.py \
        --image_name ${image_name} \
        --output_dir ${save_dir}
}

generate_sys(){
    python experimental/dxy/exps/attach_sys_to_nodes.py \
        --data_dir ${save_dir}/${sanitized_name}/ \
        --source_doc_name api-raw-${core_file}.torch \
        --target_doc_name api-raw-${core_file}-sys \
        --system_name rogue-6k


    python experimental/dxy/exps/attach_sys_to_nodes.py \
        --data_dir ${save_dir}/${sanitized_name}/ \
        --source_doc_name api-raw-${core_file}.torch \
        --target_doc_name api-raw-${core_file}-sys \
        --system_name fimv2-16b-4kl


    python experimental/dxy/exps/attach_sys_to_nodes.py \
        --data_dir ${save_dir}/${sanitized_name}/ \
        --source_doc_name api-raw-${core_file}.torch \
        --target_doc_name api-raw-${core_file}-sys \
        --system_name basic-sc-7b


    python experimental/dxy/exps/attach_sys_to_nodes.py \
        --data_dir ${save_dir}/${sanitized_name}/ \
        --source_doc_name api-raw-${core_file}.torch \
        --target_doc_name api-raw-${core_file}-sys \
        --system_name basic-sc-3b
}

generate_hydra_to_nodes(){
    python experimental/dxy/exps/attach_hydra_to_nodes_in_parallel.py \
        --data_dir ${save_dir}/${sanitized_name}/ \
        --source_doc_name api-raw-${core_file}.torch \
        --target_doc_name api-raw-${core_file}-hydra \
        --max_parallelism 128
}

generate_final_patches(){
    python experimental/dxy/exps/filter_nodes_by_sys_hydra.py \
        --input_dir ${save_dir}/${sanitized_name}/ \
        --basename api-raw-${core_file}

    python experimental/dxy/exps/sample_patches.py \
        --input_file ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-nodes/filtered-results.torch \
        --output_dir ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/ \
        --sample_type api
}

generate_hydra_to_patch(){
    python experimental/dxy/exps/attach_hydra_to_patches.py \
    --source_file ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/final-patch-nodes.torch \
    --output_dir ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/hydra-default \
    --max_parallelism 128
}

generate_sys_with_hydra_to_patch(){
    python experimental/dxy/exps/attach_sys_to_patches.py \
    --source_file ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/final-patch-nodes.torch \
    --output_dir ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/system-default \
    --system_name trim-sc-3b
}

build_dataset(){
    python experimental/dxy/exps/build_final_dataset.py \
    --input_dir ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/ \
    --output_dir ${save_dir}/${sanitized_name}/api-raw-${core_file}-final-patches/dataset \
    --limit 100 200 300
}

usage() {
    echo "Usage: $0 [--init|--gen_sys|--gen_hydra|--gen_patches|--hydra2patch|--syshydra2patch|--build_dataset] image_name save_dir"
    exit 1
}

case "${mode}" in
    --init) init_program ;;
    --gen_sys) generate_sys ;;
    --gen_hydra) generate_hydra_to_nodes ;;
    --gen_patches) generate_final_patches ;;
    --hydra2patch) generate_hydra_to_patch;;
    --syshydra2patch) generate_sys_with_hydra_to_patch;;
    --build_dataset) build_dataset;;
    *) usage ;;
esac
