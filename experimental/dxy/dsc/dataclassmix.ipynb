{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.fastbackward.model import ModelArgs\n", "from dataclasses_json import DataClassJsonMixin, Undefined\n", "\n", "\n", "params = {\n", "    \"dim\": 2048,\n", "    \"n_layers\": 27,\n", "    \"rope_scaling_factor\": 1.0,\n", "    \"vocab_size\": 102400,\n", "    \"rotary_config\": {\n", "        \"rotary_ratio\": 1.0,\n", "        \"rotary_theta\": 10000.0,\n", "        \"max_position_embeddings\": 163840,\n", "        \"rotary_interleave\": True,\n", "        \"ext_config\": {\n", "            \"rotary_scaling_factor\": 40.0,\n", "            \"unscaled_max_position_embeddings\": 4096,\n", "            \"beta_fast\": 32,\n", "            \"beta_slow\": 1,\n", "            \"mscale\": 0.707,\n", "        },\n", "    },\n", "    \"attn_config\": {\n", "        \"hidden_dim\": 2048,\n", "        \"num_heads\": 16,\n", "        \"v_head_dim\": 128,\n", "        \"q_lora_rank\": None,\n", "        \"kv_lora_rank\": 512,\n", "        \"qk_rope_head_dim\": 64,\n", "        \"qk_nope_head_dim\": 128,\n", "        \"eps\": 1e-06,\n", "        \"bias\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    \"ffn_config\": {\n", "        \"hidden_dim\": 2048,\n", "        \"n_routed_experts\": 64,\n", "        \"routed_scaling_factor\": 1.0,\n", "        \"num_experts_per_token\": 6,\n", "        \"intermediate_size\": 1408,\n", "        \"n_shared_experts\": 2,\n", "        \"topk_method\": \"greedy\",\n", "    },\n", "    \"first_layer_ffn_config\": {\n", "        \"hidden_dim\": 2048,\n", "        \"intermediate_size\": 10944,\n", "        \"bias\": <PERSON><PERSON><PERSON>,\n", "    },\n", "    \"norm_eps\": 1e-06,\n", "    \"ffn_type\": \"\",\n", "    \"bias\": \"none\",\n", "    \"norm_type\": \"rmsnorm\",\n", "    \"pos_embed_type\": \"rope\",\n", "    \"skip_output\": <PERSON><PERSON><PERSON>,\n", "    \"max_seq_len\": 128000,\n", "    \"max_generation_batch_size\": 1,\n", "    \"generation_mode\": True,\n", "    \"use_activation_checkpointing\": False,\n", "    \"use_sequence_parallel\": <PERSON><PERSON><PERSON>,\n", "}"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["model_args = ModelArgs.from_dict(params)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DeepSeekV2MoESpec(hidden_dim=2048, n_routed_experts=64, routed_scaling_factor=1.0, num_experts_per_token=6, intermediate_size=1408, n_shared_experts=2, topk_method='greedy')\n"]}], "source": ["print(model_args.ffn_config)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MlpSpec(hidden_dim=2048, bias=False)\n"]}], "source": ["print(model_args.first_layer_ffn_config)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["X(x=B(a=1, b=2, c=3))\n", "{'x': {'a': 1, 'b': 2, 'c': 3}}\n", "X(x=A(a=1, c=3))\n"]}], "source": ["import dataclasses\n", "from dataclasses_json import DataClassJsonMixin\n", "\n", "@dataclasses.dataclass\n", "class A(DataClassJsonMixin):\n", "    a: int\n", "    c: int\n", "\n", "@dataclasses.dataclass\n", "class B(DataClassJsonMixin):\n", "    a: int\n", "    b: int\n", "    c: int\n", "\n", "@dataclasses.dataclass\n", "class X(DataClassJsonMixin):\n", "    x: A | B | None = None\n", "\n", "obj = X(x=B(a=1, b=2, c=3))\n", "print(obj)\n", "json_dict = obj.to_dict()\n", "print(json_dict)\n", "\n", "obj2 = X.from_dict(json_dict)\n", "print(obj2)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}