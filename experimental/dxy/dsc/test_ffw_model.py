"""Test the FastForward DeepSeek-V2 model.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/dsc/test_ffw_model.py
"""

import torch

from base.fastforward.deepseek_v2.fwd_dsv2 import (
    DeepSeekCoderV2AttentionFactory,
    generate_step_fn,
)
from base.fastforward.deepseek_v2.model_specs import get_model_spec
from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer
from research.core import utils_for_log

if __name__ == "__main__":
    model_spec = get_model_spec(
        "deepseek-coder-v2-lite",
        checkpoint_path="/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct-ffw",
        checkpoint_sha256="fe9e145c789f4298c9aa8f4d9e1a5ea557ec7c8d4ba4c9902a4404edfc431963",
    )
    # model_spec = get_model_spec("deepseek-coder-v2-nano")
    # model_spec.checkpoint_path = "/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Nano-Instruct-ffw"

    print(
        f"{utils_for_log.time_string()} Start loading the model with this spec:\n{model_spec}"
    )
    # fmt: off
    step_fn = generate_step_fn(model_spec, dtype=torch.bfloat16, load_checkpoint_weights=True)
    print(f"{utils_for_log.time_string()} Finish loading the model.")
    tokens = [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]
    attn_factory = DeepSeekCoderV2AttentionFactory(model_spec, dtype=torch.bfloat16)
    attn = attn_factory(128)
    attn.reset()
    max_generated_tokens, output_tokens, output_logits = 32, [], []
    # The Lite-Instruct model should produce [41257, 0, 24275, 317, 274, 9674, 280, 254, 19992, 31794, 6712, 279, 12974, 25, 185, 185, 10897, 11338 ...]
    inp = tokens
    for _ in range(max_generated_tokens):
        logits = step_fn(inp, attn).checked_cast(torch.Tensor)
        next_tok = int(logits[-1].argmax().item())
        inp = [next_tok]
        # logging
        output_tokens.append(next_tok)
        output_logits.append(logits)
    # fmt: on
    print(f"The first logits: {output_logits[0]}")
    print(f"The output tokens: {output_tokens}")
    tokenizer = DeepSeekCoderV2Tokenizer()
    input_str = tokenizer.detokenize(tokens)
    output_str = tokenizer.detokenize(output_tokens)
    print(f"The input string:\n{input_str}")
    print("-" * 128)
    print(f"The output string:\n{output_str}")
