"""Run the reference HuggingFace model

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/dsc/test_ref_model.py
"""

import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer

if __name__ == "__main__":
    base_tokenizer = DeepSeekCoderV2Tokenizer()

    deepseek_coder_v2_lite_instruct_dir = (
        "/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct"
    )

    tokenizer = AutoTokenizer.from_pretrained(
        deepseek_coder_v2_lite_instruct_dir, trust_remote_code=True
    )
    messages = [{"role": "user", "content": "write a quick sort algorithm in python."}]
    hf_inputs = tokenizer.apply_chat_template(
        messages, add_generation_prompt=True, return_tensors="pt"
    )
    assert isinstance(hf_inputs, torch.Tensor)
    tokens: list[int] = hf_inputs.cpu().numpy()[0].tolist()
    prompt = tokenizer.decode(tokens)
    print(f"Tokens: {tokens}")
    print(f"Prompt:\n{prompt}")
    print(f"Tokens from base: {base_tokenizer.tokenize_unsafe(prompt)}")

    hf_model = AutoModelForCausalLM.from_pretrained(
        deepseek_coder_v2_lite_instruct_dir,
        trust_remote_code=True,
        torch_dtype=torch.float32,
    ).cuda()
    hf_inputs = hf_inputs.to(hf_model.device)  # type: ignore
    # tokenizer.eos_token_id is the id of <｜end▁of▁sentence｜>  token
    outputs = hf_model.generate(
        hf_inputs,
        max_new_tokens=32,
        do_sample=False,
        top_k=1,
        top_p=0.95,
        num_return_sequences=1,
        eos_token_id=tokenizer.eos_token_id,
    )
    output_tokens = outputs[0][len(tokens) :]
    print(tokenizer.decode(output_tokens, skip_special_tokens=True))
