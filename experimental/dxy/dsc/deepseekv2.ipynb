{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokens: [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]\n", "Prompt:\n", "<｜begin▁of▁sentence｜>User: write a quick sort algorithm in python.\n", "\n", "Assistant:\n", "Tokens from base: [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]\n"]}], "source": ["from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer\n", "from research.core.model_input import ChatInput\n", "\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "\n", "base_tokenizer = DeepSeekCoderV2Tokenizer()\n", "\n", "deepseek_coder_v2_lite_instruct_dir = \"/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(deepseek_coder_v2_lite_instruct_dir, trust_remote_code=True)\n", "messages=[\n", "    { 'role': 'user', 'content': \"write a quick sort algorithm in python.\"}\n", "]\n", "hf_inputs = tokenizer.apply_chat_template(messages, add_generation_prompt=True, return_tensors=\"pt\")\n", "tokens = hf_inputs.cpu().numpy()[0].tolist()  # type: ignore\n", "prompt = tokenizer.decode(tokens)\n", "print(f\"Tokens: {tokens}\")\n", "print(f\"Prompt:\\n{prompt}\")\n", "print(f\"Tokens from base: {base_tokenizer.tokenize_unsafe(prompt)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hf_inputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hf_model = AutoModelForCausalLM.from_pretrained(deepseek_coder_v2_lite_instruct_dir, trust_remote_code=True, torch_dtype=torch.bfloat16).cuda()\n", "hf_inputs = hf_inputs.to(hf_model.device) # type: ignore\n", "# tokenizer.eos_token_id is the id of <｜end▁of▁sentence｜>  token\n", "outputs = hf_model.generate(hf_inputs, max_new_tokens=512, do_sample=False, top_k=50, top_p=0.95, num_return_sequences=1, eos_token_id=tokenizer.eos_token_id)\n", "print(tokenizer.decode(outputs[0][len(inputs[0]):], skip_special_tokens=True))\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[100000, 100003,   1558,   3399,     62,  14318,      7,   3049,   1780,\n", "            185,    300,    565,  10389,      7,   3049,      8,  10551,    207,\n", "             16,     25,    185,    391,    972,   5382,    185,    300,  43326,\n", "            403,   5382,     58,     15,     60,    185,    300,   2116,    403,\n", "           9636,    185,    300,   1329,    403,   9636,    185, 100002,    185,\n", "            391,    565,   5382,     58,     72,     60,    459,  43326,     25,\n", "            185,    595,   2116,     13,   6880,      7,   3049,     58,     72,\n", "           5855,    185,    391,   1979,     25,    185,    595,   1329,     13,\n", "           6880,      7,   3049,     58,     72,   5855,    185,    300,    972,\n", "           3399,     62,  14318,      7,   1354,      8,    919,    825,  79720,\n", "             60,    919,   3399,     62,  14318,      7,   1035,      8, 100004]])\n"]}], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "\n", "\n", "deepseek_coder_v2_lite_base_dir = \"/mnt/efs/augment/checkpoints/deepseek/DeepSeek-Coder-V2-Lite-Base\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(deepseek_coder_v2_lite_base_dir, trust_remote_code=True)\n", "input_text = \"\"\"<｜fim▁begin｜>def quick_sort(arr):\n", "    if len(arr) <= 1:\n", "        return arr\n", "    pivot = arr[0]\n", "    left = []\n", "    right = []\n", "<｜fim▁hole｜>\n", "        if arr[i] < pivot:\n", "            left.append(arr[i])\n", "        else:\n", "            right.append(arr[i])\n", "    return quick_sort(left) + [pivot] + quick_sort(right)<｜fim▁end｜>\"\"\"\n", "inputs = tokenizer(input_text, return_tensors=\"pt\").input_ids\n", "print(inputs)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}