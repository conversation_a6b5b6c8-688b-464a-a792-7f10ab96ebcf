"""Test the FastBackward DeepSeek-V2 model.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/dsc/test_fbw_model.py \
    --ckp_dir=/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Base-FBW-dense-mp1/ \
    --use_inference

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/dsc/test_fbw_model.py \
    --ckp_dir=/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Base-FBW-sparse-mp1/ \
    --use_inference
"""

import argparse
import json
import pathlib
import time

import torch

from base.tokenizers.deepseek_coder_v2_tokenizer import DeepSeekCoderV2Tokenizer
from research.fastbackward import inference
from research.fastbackward.model import ModelArgs


def get_model(model_args: ModelArgs, use_inference: bool, ckp_dir: pathlib.Path):
    if use_inference:
        start_time = time.time()
        num_gpus = torch.cuda.device_count()
        print(f"Detected {num_gpus} GPUs.")
        model_runner = inference.ParallelTransformerRunner(num_gpus)
        model_runner.load_model(model_args, ckp_dir)
        print(f"Finish loading the model in {time.time() - start_time}s.")
        return model_runner
    else:
        master_port = inference.find_available_local_port()
        print(f"We find the master port: {master_port}")
        inference._parallel_init(0, 1, "127.0.0.1", master_port)
        print("Start to create the model.")
        model = inference.Transformer(model_args).bfloat16()
        start_time = time.time()
        state_dict = torch.load(ckp_dir / "consolidated.00.pth", map_location="cpu")
        print(f"Finish loading the model in {time.time() - start_time}s.")
        model.load_state_dict(state_dict, strict=True)
        model.to(device=torch.device("cuda"), dtype=torch.bfloat16)
        return model


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--use_inference",
        action="store_true",
        default=False,
        help="Whether to use the inference runner",
    )
    parser.add_argument(
        "--ckp_dir",
        type=pathlib.Path,
        default=None,
        help="The checkpoint directory",
    )
    args = parser.parse_args()
    param_path = args.ckp_dir / "params.json"
    with param_path.open("r") as f:
        params = json.loads(f.read())
    params["max_generation_batch_size"] = 1
    params["use_sequence_parallel"] = False
    print(f"params:\n{params}")
    model_args = ModelArgs.schema().load(params)
    model_args.max_seq_len = 8192
    print(f"The model args:\n{model_args}")

    model_runner = get_model(
        model_args, use_inference=args.use_inference, ckp_dir=args.ckp_dir
    )

    prompt_tokens = [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]  # fmt: off
    tokens_th = torch.full((1, 10000), 0, dtype=torch.long, device="cuda")
    tokens_th[0, : len(prompt_tokens)] = torch.tensor(prompt_tokens, dtype=torch.long, device="cuda")  # fmt: off
    prev_pos = 0
    generated_tokens = []
    for cur_pos in range(len(prompt_tokens), len(prompt_tokens) + 32):
        logits = model_runner.generate(tokens_th[:, prev_pos:cur_pos], prev_pos)
        next_token = torch.argmax(logits[:, -1], dim=-1)
        next_token = next_token.reshape(-1)
        tokens_th[:, cur_pos] = next_token
        prev_pos = cur_pos
        generated_tokens.append(next_token.item())
    print(f"The generated tokens: {generated_tokens}")

    tokenizer = DeepSeekCoderV2Tokenizer()
    input_str = tokenizer.detokenize(prompt_tokens)
    output_str = tokenizer.detokenize(generated_tokens)
    print(f"The input string:\n{input_str}")
    print("-" * 128)
    print(f"The output string:\n{output_str}")
    if isinstance(model_runner, inference.ParallelTransformerRunner):
        model_runner.unload_model()


if __name__ == "__main__":
    main()
