{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from research.fastbackward import model as fbw_model\n", "from base.fastforward import positional_embeddings\n", "\n", "vocab_size = 4\n", "rotary_config = positional_embeddings.RotaryConfig(\n", "    rotary_ratio=1.0,\n", "    rotary_theta=10000.0,\n", "    max_position_embeddings=163840,\n", "    ext_config=positional_embeddings.YaRNExtensionConfig(\n", "        rotary_scaling_factor=40.0,\n", "        unscaled_max_position_embeddings=4096,\n", "        beta_fast=32,\n", "        beta_slow=1,\n", "        mscale=0.707,\n", "    ),\n", ")\n", "attn_config = fbw_model.DeepSeekV2MLASpec(\n", "    hidden_dim=128,\n", "    num_heads=4,\n", "    v_head_dim=64,\n", "    q_lora_rank=None,\n", "    kv_lora_rank=16,\n", "    qk_rope_head_dim=32,\n", "    qk_nope_head_dim=64,\n", "    eps=1e-6,\n", "    bias=True,\n", ")\n", "# attn_config = fbw_model.GenericAttnSpec(\n", "#     hidden_dim=16,\n", "#     n_heads=4,\n", "#     n_kv_heads=2,\n", "#     norm_type=\"layernorm\",\n", "#     pos_embed_type=\"rope\",\n", "#     bias=False,\n", "# )\n", "# ffn_config = fbw_model.DeepSeekV2MoESpec(\n", "#     hidden_dim=16,\n", "#     n_routed_experts=4,\n", "#     routed_scaling_factor=1.0,\n", "#     num_experts_per_token=2,\n", "#     intermediate_size=12,\n", "#     n_shared_experts=1,\n", "#     topk_method=\"greedy\",\n", "#     use_dense_moe=True,\n", "# )\n", "ffn_config = fbw_model.GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "\n", "model_args = fbw_model.ModelArgs(\n", "    dim=128,\n", "    n_layers=2,\n", "    n_heads=0,\n", "    n_kv_heads=0,\n", "    vocab_size=vocab_size,\n", "    ffn_type=\"\",\n", "    bias=\"\",\n", "    norm_type=\"layernorm\",\n", "    rotary_config=rotary_config,\n", "    attn_config=attn_config,\n", "    first_layer_ffn_config=fbw_model.GluSpec(\n", "        hidden_dim=128, intermediate_size=256, bias=False\n", "    ),\n", "    ffn_config=ffn_config,\n", "    max_seq_len=128,\n", "    use_sequence_parallel=False,\n", "    use_activation_checkpointing=False,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ckptdir: /home/<USER>/test-grads\n", "> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-10 02:09:57,832 - research.fastbackward.model.__init__@L849 - rank@0:INFO: Creating a Transformer model.\n", "2024-08-10 02:09:57,832 - research.fastbackward.model.__init__@L869 - rank@0:INFO: Attention config:\n", "DeepSeekV2MLASpec(hidden_dim=128, num_heads=4, v_head_dim=64, q_lora_rank=None, kv_lora_rank=16, qk_rope_head_dim=32, qk_nope_head_dim=64, eps=1e-06, bias=True)\n", "2024-08-10 02:09:57,832 - research.fastbackward.model.__init__@L870 - rank@0:INFO: First layer FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:09:57,832 - research.fastbackward.model.__init__@L871 - rank@0:INFO: Other layers' FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n"]}], "source": ["from research.fastbackward.checkpointing import checkpointing\n", "from research.fastbackward.checkpointing.utils import (\n", "    merge_model_parallel_consolidated_checkpoints,\n", "    split_consolidated_checkpoint,\n", ")\n", "from research.fastbackward.tests.fake_distributed_runner import distributed_runner\n", "from research.fastbackward.tests.test_parallel_gradients import (\n", "    _create_model,\n", "    _run_model,\n", "    _assert_params_match,\n", "    _assert_grads_match,\n", ")\n", "import pathlib\n", "\n", "ckptdir = pathlib.Path(\"/home/<USER>/test-grads\")\n", "print(f\"ckptdir: {ckptdir}\")\n", "\n", "with distributed_runner(world_size=1, model_parallel_size=1) as runner:\n", "    runner.run(_create_model, ckptdir, model_args)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x = tensor([[3, 3, 3, 2],\n", "        [3, 1, 1, 3],\n", "        [0, 2, 1, 0],\n", "        [3, 3, 3, 2],\n", "        [2, 1, 3, 0],\n", "        [2, 3, 3, 3],\n", "        [1, 1, 2, 0],\n", "        [2, 1, 1, 3]])\n", "y = tensor([[3, 3, 2, 1],\n", "        [1, 1, 3, 2],\n", "        [2, 1, 0, 1],\n", "        [3, 3, 2, 2],\n", "        [1, 3, 0, 0],\n", "        [3, 3, 3, 1],\n", "        [1, 2, 0, 2],\n", "        [1, 1, 3, 1]])\n"]}], "source": ["import torch\n", "\n", "batch = 8\n", "seqlen = 4\n", "gradient_accumulation_steps = 2\n", "data = torch.randint(vocab_size, (batch, seqlen + 1), dtype=torch.int64, device=\"cpu\")\n", "x = data[:, :-1].contiguous()\n", "y = data[:, 1:].contiguous()\n", "print(f\"{x = }\")\n", "print(f\"{y = }\")\n", "\n", "runner_args = (\n", "    _run_model,\n", "    ckptdir,\n", "    model_args,\n", "    x,\n", "    y,\n", "    torch.float32,\n", "    gradient_accumulation_steps,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-10 02:10:01,108 - research.fastbackward.model.__init__@L849 - rank@0:INFO: Creating a Transformer model.\n", "2024-08-10 02:10:01,108 - research.fastbackward.model.__init__@L869 - rank@0:INFO: Attention config:\n", "DeepSeekV2MLASpec(hidden_dim=128, num_heads=4, v_head_dim=64, q_lora_rank=None, kv_lora_rank=16, qk_rope_head_dim=32, qk_nope_head_dim=64, eps=1e-06, bias=True)\n", "2024-08-10 02:10:01,108 - research.fastbackward.model.__init__@L870 - rank@0:INFO: First layer FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:10:01,108 - research.fastbackward.model.__init__@L871 - rank@0:INFO: Other layers' FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:10:01,289 - research.fastbackward.model.configure_fsdp_optimizer@L1118 - rank@0:INFO: num decayed parameter tensors: 16, with 390,144 parameters\n", "2024-08-10 02:10:01,289 - research.fastbackward.model.configure_fsdp_optimizer@L1121 - rank@0:INFO: num non-decayed parameter tensors: 16, with 1,664 parameters\n"]}, {"name": "stdout", "output_type": "stream", "text": ["There are 32 gradient tensors.\n"]}], "source": ["with distributed_runner(world_size=1, timeout_s=20.0) as runner:\n", "    # NOTE: we _always_ use gradacc=1 for the reference grads\n", "    runner.run(_run_model, ckptdir, model_args, x, y, torch.float32, 1, \"DP1MP1\")\n", "    ref_params = torch.load(ckptdir / \"DP1MP1-00p.pth\", map_location=\"cpu\")\n", "    ref_grads = torch.load(ckptdir / \"DP1MP1-00g.pth\", map_location=\"cpu\")\n", "    print(f\"There are {len(ref_grads)} gradient tensors.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 1\n", "> Initializing DDP with size 2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L849 - rank@0:INFO: Creating a Transformer model.\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L869 - rank@0:INFO: Attention config:\n", "DeepSeekV2MLASpec(hidden_dim=128, num_heads=4, v_head_dim=64, q_lora_rank=None, kv_lora_rank=16, qk_rope_head_dim=32, qk_nope_head_dim=64, eps=1e-06, bias=True)\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L849 - rank@1:INFO: Creating a Transformer model.\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L870 - rank@0:INFO: First layer FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L871 - rank@0:INFO: Other layers' FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L869 - rank@1:INFO: Attention config:\n", "DeepSeekV2MLASpec(hidden_dim=128, num_heads=4, v_head_dim=64, q_lora_rank=None, kv_lora_rank=16, qk_rope_head_dim=32, qk_nope_head_dim=64, eps=1e-06, bias=True)\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L870 - rank@1:INFO: First layer FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:10:09,953 - research.fastbackward.model.__init__@L871 - rank@1:INFO: Other layers' FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:10:10,271 - research.fastbackward.model.configure_fsdp_optimizer@L1118 - rank@1:INFO: num decayed parameter tensors: 16, with 390,144 parameters\n", "2024-08-10 02:10:10,271 - research.fastbackward.model.configure_fsdp_optimizer@L1121 - rank@1:INFO: num non-decayed parameter tensors: 16, with 1,664 parameters\n", "2024-08-10 02:10:10,272 - research.fastbackward.model.configure_fsdp_optimizer@L1118 - rank@0:INFO: num decayed parameter tensors: 16, with 390,144 parameters\n", "2024-08-10 02:10:10,272 - research.fastbackward.model.configure_fsdp_optimizer@L1121 - rank@0:INFO: num non-decayed parameter tensors: 16, with 1,664 parameters\n"]}], "source": ["# Condition 1: 2x data-parallel, 1x model-parallel\n", "with distributed_runner(world_size=2, timeout_s=20.0) as runner:\n", "    runner.run(*runner_args, \"DP2MP1\")\n", "for dp_rank in range(2):\n", "    params = torch.load(ckptdir / f\"DP2MP1-{dp_rank}0p.pth\", map_location=\"cpu\")\n", "    _assert_params_match(params, ref_params, label=f\"DP2MP1-DR{dp_rank}\")\n", "    grads = torch.load(ckptdir / f\"DP2MP1-{dp_rank}0g.pth\", map_location=\"cpu\")\n", "    _assert_grads_match(\n", "        grads,\n", "        ref_grads,\n", "        label=f\"DP2MP1-DR{dp_rank}\",\n", "    )"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["> Initializing model parallel with size 2\n", "> Initializing DDP with size 1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2024-08-10 02:11:10,775 - research.fastbackward.model.__init__@L849 - rank@1:INFO: Creating a Transformer model.\n", "2024-08-10 02:11:10,776 - research.fastbackward.model.__init__@L869 - rank@1:INFO: Attention config:\n", "DeepSeekV2MLASpec(hidden_dim=128, num_heads=4, v_head_dim=64, q_lora_rank=None, kv_lora_rank=16, qk_rope_head_dim=32, qk_nope_head_dim=64, eps=1e-06, bias=True)\n", "2024-08-10 02:11:10,776 - research.fastbackward.model.__init__@L870 - rank@1:INFO: First layer FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:11:10,776 - research.fastbackward.model.__init__@L871 - rank@1:INFO: Other layers' FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:11:10,786 - research.fastbackward.model.__init__@L849 - rank@0:INFO: Creating a Transformer model.\n", "2024-08-10 02:11:10,787 - research.fastbackward.model.__init__@L869 - rank@0:INFO: Attention config:\n", "DeepSeekV2MLASpec(hidden_dim=128, num_heads=4, v_head_dim=64, q_lora_rank=None, kv_lora_rank=16, qk_rope_head_dim=32, qk_nope_head_dim=64, eps=1e-06, bias=True)\n", "2024-08-10 02:11:10,787 - research.fastbackward.model.__init__@L870 - rank@0:INFO: First layer FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:11:10,787 - research.fastbackward.model.__init__@L871 - rank@0:INFO: Other layers' FFN config:\n", "GluSpec(hidden_dim=128, intermediate_size=256, bias=False)\n", "2024-08-10 02:11:11,121 - research.fastbackward.model.configure_fsdp_optimizer@L1118 - rank@1:INFO: num decayed parameter tensors: 16, with 201,216 parameters\n", "2024-08-10 02:11:11,121 - research.fastbackward.model.configure_fsdp_optimizer@L1121 - rank@1:INFO: num non-decayed parameter tensors: 16, with 1,664 parameters\n", "2024-08-10 02:11:11,129 - research.fastbackward.model.configure_fsdp_optimizer@L1118 - rank@0:INFO: num decayed parameter tensors: 16, with 201,216 parameters\n", "2024-08-10 02:11:11,129 - research.fastbackward.model.configure_fsdp_optimizer@L1121 - rank@0:INFO: num non-decayed parameter tensors: 16, with 1,664 parameters\n"]}], "source": ["# Condition 2: 1x data-parallel, 2x model-parallel\n", "with distributed_runner(\n", "    world_size=2,\n", "    model_parallel_size=2,\n", "    model_parallel_subgroups=None,\n", "    timeout_s=20.0,\n", ") as runner:\n", "    runner.run(*runner_args, \"DP1MP2\")\n", "\n", "param_sds = [\n", "    torch.load(ckptdir / f\"DP1MP2-0{i}p.pth\", map_location=\"cpu\") for i in range(2)\n", "]\n", "params = merge_model_parallel_consolidated_checkpoints(model_args, param_sds)\n", "_assert_params_match(params, ref_params, label=\"DP1MP2\")\n", "\n", "grad_sds = [\n", "    torch.load(ckptdir / f\"DP1MP2-0{i}g.pth\", map_location=\"cpu\") for i in range(2)\n", "]\n", "grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)\n", "_assert_grads_match(grads, ref_grads, label=\"DP1MP2\", rtol=1e-4, atol=1e-4)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["grad_sds[0][key].shape = torch.Size([128])\n", "tensor([ 0.0273, -0.0266, -0.0082,  0.1173, -0.1083,  0.1653, -0.0223,  0.2730,\n", "         0.0143, -0.1422,  0.0428,  0.0170, -0.2416, -0.2539, -0.2128,  0.1260,\n", "         0.2612, -0.1146, -0.1934,  0.0037, -0.0788, -0.0319, -0.0950,  0.0810,\n", "         0.0805,  0.1308,  0.3158,  0.1519, -0.2731,  0.0216,  0.0200,  0.1833,\n", "        -0.2829, -0.0041,  0.0714,  0.4420, -0.2110,  0.0278, -0.0273,  0.0032,\n", "         0.2462, -0.1370, -0.0113,  0.1598, -0.2299, -0.0586,  0.0211, -0.1514,\n", "        -0.2394,  0.1651, -0.1018,  0.0446, -0.0388, -0.0622, -0.0290, -0.0598,\n", "        -0.1456,  0.0994, -0.1498, -0.0394,  0.2105,  0.1350,  0.0549,  0.0561,\n", "        -0.0379,  0.0141, -0.0429, -0.1551,  0.0378, -0.1025, -0.0549,  0.1796,\n", "         0.2765,  0.0158, -0.0708, -0.1188,  0.0725,  0.1029, -0.2544, -0.0665,\n", "        -0.3249,  0.1087,  0.0248, -0.0060, -0.0207,  0.1620,  0.1172,  0.0513,\n", "        -0.1229,  0.1160, -0.0975,  0.0593, -0.1262, -0.1288, -0.0721, -0.0515,\n", "        -0.0087, -0.2096, -0.1241, -0.0040, -0.0060,  0.0270, -0.1093,  0.0395,\n", "        -0.0400,  0.3591,  0.0574,  0.3431,  0.0067,  0.2344, -0.0835, -0.0677,\n", "         0.2689,  0.0484,  0.0390, -0.1752,  0.0901, -0.1782,  0.1636,  0.1550,\n", "         0.2048, -0.1559, -0.1629, -0.0128,  0.0260, -0.2832,  0.0136,  0.0726])\n", "tensor([ 0.0273, -0.0266, -0.0082,  0.1173, -0.1083,  0.1653, -0.0223,  0.2730,\n", "         0.0143, -0.1422,  0.0428,  0.0170, -0.2416, -0.2539, -0.2128,  0.1260,\n", "         0.2612, -0.1146, -0.1934,  0.0037, -0.0788, -0.0319, -0.0950,  0.0810,\n", "         0.0805,  0.1308,  0.3158,  0.1519, -0.2731,  0.0216,  0.0200,  0.1833,\n", "        -0.2829, -0.0041,  0.0714,  0.4420, -0.2110,  0.0278, -0.0273,  0.0032,\n", "         0.2462, -0.1370, -0.0113,  0.1598, -0.2299, -0.0586,  0.0211, -0.1514,\n", "        -0.2394,  0.1651, -0.1018,  0.0446, -0.0388, -0.0622, -0.0290, -0.0598,\n", "        -0.1456,  0.0994, -0.1498, -0.0394,  0.2105,  0.1350,  0.0549,  0.0561,\n", "        -0.0379,  0.0141, -0.0429, -0.1551,  0.0378, -0.1025, -0.0549,  0.1796,\n", "         0.2765,  0.0158, -0.0708, -0.1188,  0.0725,  0.1029, -0.2544, -0.0665,\n", "        -0.3249,  0.1087,  0.0248, -0.0060, -0.0207,  0.1620,  0.1172,  0.0513,\n", "        -0.1229,  0.1160, -0.0975,  0.0593, -0.1262, -0.1288, -0.0721, -0.0515,\n", "        -0.0087, -0.2096, -0.1241, -0.0040, -0.0060,  0.0270, -0.1093,  0.0395,\n", "        -0.0400,  0.3591,  0.0574,  0.3431,  0.0067,  0.2344, -0.0835, -0.0677,\n", "         0.2689,  0.0484,  0.0390, -0.1752,  0.0901, -0.1782,  0.1636,  0.1550,\n", "         0.2048, -0.1559, -0.1629, -0.0128,  0.0260, -0.2832,  0.0136,  0.0726])\n", "\n", "ref_grads[key].shape = torch.Size([128])\n", "tensor([ 0.0273, -0.0266, -0.0082,  0.1173, -0.1083,  0.1653, -0.0223,  0.2730,\n", "         0.0143, -0.1422,  0.0428,  0.0170, -0.2416, -0.2539, -0.2128,  0.1260,\n", "         0.2612, -0.1146, -0.1934,  0.0037, -0.0788, -0.0319, -0.0950,  0.0810,\n", "         0.0805,  0.1308,  0.3158,  0.1519, -0.2731,  0.0216,  0.0200,  0.1833,\n", "        -0.2829, -0.0041,  0.0714,  0.4420, -0.2110,  0.0278, -0.0273,  0.0032,\n", "         0.2462, -0.1370, -0.0113,  0.1598, -0.2299, -0.0586,  0.0211, -0.1514,\n", "        -0.2394,  0.1651, -0.1018,  0.0446, -0.0388, -0.0622, -0.0290, -0.0598,\n", "        -0.1456,  0.0994, -0.1498, -0.0394,  0.2105,  0.1350,  0.0549,  0.0561,\n", "        -0.0379,  0.0141, -0.0429, -0.1551,  0.0378, -0.1025, -0.0549,  0.1796,\n", "         0.2765,  0.0158, -0.0708, -0.1188,  0.0725,  0.1029, -0.2544, -0.0665,\n", "        -0.3249,  0.1087,  0.0248, -0.0060, -0.0207,  0.1620,  0.1172,  0.0513,\n", "        -0.1229,  0.1160, -0.0975,  0.0593, -0.1262, -0.1288, -0.0721, -0.0515,\n", "        -0.0087, -0.2096, -0.1241, -0.0040, -0.0060,  0.0270, -0.1093,  0.0395,\n", "        -0.0400,  0.3591,  0.0574,  0.3431,  0.0067,  0.2344, -0.0835, -0.0677,\n", "         0.2689,  0.0484,  0.0390, -0.1752,  0.0901, -0.1782,  0.1636,  0.1550,\n", "         0.2048, -0.1559, -0.1629, -0.0128,  0.0260, -0.2832,  0.0136,  0.0726])\n"]}], "source": ["key = \"layers.0.attention.o_proj.bias\"\n", "print(f\"{grad_sds[0][key].shape = }\")\n", "print(grad_sds[0][key])\n", "print(grad_sds[1][key])\n", "\n", "print(f\"\\n{ref_grads[key].shape = }\")\n", "print(ref_grads[key])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["key = \"layers.1.attention.kv_a_proj_with_mqa.bias\"\n", "print(f\"{grad_sds[0][key].shape = }\")\n", "indexes = slice(16, 32)\n", "print(grad_sds[0][key][indexes])\n", "print(grad_sds[1][key][indexes])\n", "\n", "\n", "print(f\"\\n{ref_grads[key].shape = }\")\n", "print(ref_grads[key][indexes])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# key = \"layers.1.attention.o_proj.weight\"\n", "# key = \"layers.0.feed_forward.w3.bias\"\n", "# key = \"layers.0.attention.wq.weight\"\n", "key = \"layers.1.attention.kv_a_layernorm.weight\"\n", "print(f\"{grad_sds[0][key].shape = }\")\n", "print(grad_sds[0][key].view(-1)[:10])\n", "print(grad_sds[1][key].view(-1)[:10])\n", "print(f\"\\n{ref_grads[key].shape = }\")\n", "print(ref_grads[key].view(-1)[:10])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tok_embeddings.weight: max diff=0.002716: avg diff=0.000661\n", "# layers.0.attention.kv_b_proj.weight: max diff=0.000205: avg diff=0.000016\n", "# layers.0.attention.o_proj.weight: max diff=0.000262: avg diff=0.000033\n", "# layers.1.attention.o_proj.weight: max diff=0.000121: avg diff=0.000018"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key, ref_grad in ref_grads.items():\n", "    # print(f\"key: {key}, grad: {ref_grad}\")\n", "    print(f\"{key = }\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# key = \"tok_embeddings.weight\"\n", "# # key = \"layers.0.feed_forward.w3.bias\"\n", "# # key = \"layers.0.attention.wq.weight\"\n", "# # print(grad_sds[0][key])\n", "# # print(grad_sds[1][key])\n", "# print(grads[key])\n", "# print(ref_grads[key])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(grads[\"layers.0.feed_forward.w2.bias\"])\n", "# print(ref_grads[\"layers.0.feed_forward.w2.bias\"])"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}