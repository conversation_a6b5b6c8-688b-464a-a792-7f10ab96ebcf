"""Test the DeepSeek-V2 attention gradients.

CUDA_VISIBLE_DEVICES=0 python experimental/dxy/dsc/test-dsv2-att-grads.py --num_heads 4
CUDA_VISIBLE_DEVICES=0 python experimental/dxy/dsc/test-dsv2-att-grads.py --num_heads 8
"""

import argparse
import pathlib

import torch

from base.fastforward import positional_embeddings
from research.fastbackward import model as fbw_model
from research.fastbackward.checkpointing.utils import (
    merge_model_parallel_consolidated_checkpoints,
)
from research.fastbackward.tests.fake_distributed_runner import distributed_runner
from research.fastbackward.tests.test_parallel_gradients import (
    _assert_grads_match,
    _assert_params_match,
    _create_model,
    _run_model,
)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--num_heads",
        type=int,
        default=None,
        required=True,
        help="The number of heads in the attention.",
    )
    args = parser.parse_args()

    vocab_size = 4
    rotary_config = positional_embeddings.RotaryConfig(
        rotary_ratio=1.0,
        rotary_theta=10000.0,
        max_position_embeddings=163840,
        ext_config=positional_embeddings.YaRNExtensionConfig(
            rotary_scaling_factor=40.0,
            unscaled_max_position_embeddings=4096,
            beta_fast=32,
            beta_slow=1,
            mscale=0.707,
        ),
    )
    attn_config = fbw_model.DeepSeekV2MLASpec(
        hidden_dim=64,
        num_heads=args.num_heads,
        v_head_dim=32,
        q_lora_rank=None,
        kv_lora_rank=8,
        qk_rope_head_dim=16,
        qk_nope_head_dim=32,
        eps=1e-6,
        bias=True,
    )
    ffn_config = fbw_model.DeepSeekV2MoESpec(
        hidden_dim=64,
        n_routed_experts=4,
        routed_scaling_factor=1.0,
        num_experts_per_token=2,
        intermediate_size=128,
        n_shared_experts=2,
        topk_method="greedy",
        use_dense_moe=False,
    )
    fbw_model.SwiGLUSpec(hidden_dim=64, intermediate_size=128, bias=False)
    model_args = fbw_model.ModelArgs(
        dim=64,
        n_layers=1,
        n_heads=0,
        n_kv_heads=0,
        vocab_size=vocab_size,
        ffn_type="",
        bias="",
        norm_type="layernorm",
        rotary_config=rotary_config,
        attn_config=attn_config,
        first_layer_ffn_config=ffn_config,
        ffn_config=ffn_config,
        max_seq_len=128,
        use_sequence_parallel=False,
        use_activation_checkpointing=False,
    )

    ckptdir = pathlib.Path("/home/<USER>/test-grads")
    print(f"ckptdir: {ckptdir}")

    with distributed_runner(world_size=1, model_parallel_size=1) as runner:
        runner.run(_create_model, ckptdir, model_args)

    batch, seqlen = 8, 4
    gradient_accumulation_steps = 1
    data = torch.randint(
        vocab_size, (batch, seqlen + 1), dtype=torch.int64, device="cpu"
    )
    x = data[:, :-1].contiguous()
    y = data[:, 1:].contiguous()
    print(f"{x = }")
    print(f"{y = }")

    runner_args = (
        _run_model,
        ckptdir,
        model_args,
        x,
        y,
        torch.float32,
        gradient_accumulation_steps,
    )

    with distributed_runner(world_size=1, timeout_s=20.0) as runner:
        # NOTE: we _always_ use gradacc=1 for the reference grads
        runner.run(_run_model, ckptdir, model_args, x, y, torch.float32, 1, "DP1MP1")
        ref_params = torch.load(ckptdir / "DP1MP1-00p.pth", map_location="cpu")
        ref_grads = torch.load(ckptdir / "DP1MP1-00g.pth", map_location="cpu")
        print(f"There are {len(ref_grads)} gradient tensors.")

    # Condition 4: 1x data-parallel, 4x model-parallel
    with distributed_runner(
        world_size=4,
        model_parallel_size=4,
        model_parallel_subgroups=None,
        timeout_s=30.0,
    ) as runner:
        runner.run(*runner_args, "DP1MP4")

    param_sds = [
        torch.load(ckptdir / f"DP1MP4-0{i}p.pth", map_location="cpu") for i in range(4)
    ]
    params = merge_model_parallel_consolidated_checkpoints(model_args, param_sds)
    _assert_params_match(params, ref_params, label="DP1MP4")

    grad_sds = [
        torch.load(ckptdir / f"DP1MP4-0{i}g.pth", map_location="cpu") for i in range(4)
    ]
    grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)
    _assert_grads_match(grads, ref_grads, label="DP1MP4", rtol=1e-4, atol=1e-4)
    print("Passed DP1MP4.")
