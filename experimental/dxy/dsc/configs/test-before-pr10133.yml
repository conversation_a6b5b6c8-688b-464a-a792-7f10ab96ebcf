#
# python research/fastbackward/determined/launch.py experimental/dxy/dsc/configs/test-before-pr10133.yml
#
# 7B -> 7B * (12 + 4)
# WandB webview is at: https://wandb.ai/augment/dxy-exps?nw=nwuserbarrydxy
# Before the DS-V2 PR (#10133) in, the job link: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/81050/logs
#
determined:
  description: null
  workspace: Dev
  project: Xuanyi-Dong
  profiling:
    enabled: true

augment:
  podspec_path: "8xH100.yaml"
  project_group: finetuning
  gpu_count: 16

fastbackward_configs:
 - configs/starcoder2_7b.py

fastbackward_args:
  loss_mask_policy: fim
  fim_middle_token_id: 2
  eot_token_id: 0
  pad_token_id: 49152
  gradient_accumulation_steps: 4
  batch_size: 1
  max_iters: 10
  warmup_iters: 5
  lr_decay_iters: 10
  block_size: 7936
  min_lr: 1.0e-6
  learning_rate: 1.0e-5
  decay_lr: True
  log_interval: 1
  eval_log_interval: 200
  eval_interval: 0
  checkpoint: /mnt/efs/augment/checkpoints/starcoder2-7b-fb
  train_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-default/sc2/dataset;/mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-unittest-literal/sc2/dataset
  eval_data_path: /mnt/efs/spark-data/user/dxy/elden/0619_90k_0702/dataset-normal/sc2/validation_dataset
  model_vocab_size: 49176
  checkpoint_optimizer_state: False

  tokenizer_name: StarCoder2Tokenizer
  use_research_tokenizer: True
  visualize_logits_samples: 8

  run_name: SC2-7B-MIX-TEST-Before-10133
  wandb_project: dxy-exps
