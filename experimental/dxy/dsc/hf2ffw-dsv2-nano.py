"""Load the HuggingFace format and convert it to the FastForward checkpoint format.

python experimental/dxy/dsc/hf2ffw-dsv2-nano.py
"""

import pathlib

import torch

from base.fastforward.checkpoints import save_load
from base.fastforward.deepseek_v2.fwd_dsv2 import (
    DeepSeekCoderV2AttentionFactory,
    DeepSeekV2,
)
from base.fastforward.deepseek_v2.model_specs import DeepSeekV2ModelSpec, get_model_spec
from experimental.dxy.dsc.hf2ffw_dsv2 import HFCheckpoint
from research.core import utils_for_log


class HFCheckpointV2(HFCheckpoint):
    """A class to load the DeepSeek-Coder-V2 HuggingFace checkpoint."""

    def convert_to_ffw_state_dict(
        self, model_spec: DeepSeekV2ModelSpec
    ) -> dict[str, torch.Tensor]:
        # assert self.num_hidden_layers == model_spec.num_layers
        assert self.n_routed_experts == model_spec.moe.n_routed_experts
        weights: dict[str, torch.Tensor] = {}
        weights["embs.word_embs"] = self.weight_buffer["model.embed_tokens.weight"]
        weights["final_layer_norm.weight"] = self.weight_buffer["model.norm.weight"]
        weights["score.weight"] = self.weight_buffer["lm_head.weight"]
        for ilayer in range(model_spec.num_layers):
            # Convert the attention layer
            # fmt: off
            weights[f"layers.{ilayer}.attn.q_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.q_proj.weight"]
            weights[f"layers.{ilayer}.attn.kv_a_proj_with_mqa.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_a_proj_with_mqa.weight"]
            weights[f"layers.{ilayer}.attn.kv_a_layernorm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_a_layernorm.weight"]
            weights[f"layers.{ilayer}.attn.kv_b_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.kv_b_proj.weight"]
            weights[f"layers.{ilayer}.attn.o_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.self_attn.o_proj.weight"]
            # Handle the MLP or MoE layer
            if ilayer == 0:
                weights[f"layers.{ilayer}.ffn.gate_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate_proj.weight"]
                weights[f"layers.{ilayer}.ffn.up_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.up_proj.weight"]
                weights[f"layers.{ilayer}.ffn.down_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.down_proj.weight"]
            else:
                weights[f"layers.{ilayer}.ffn.gate_weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.gate.weight"]
                weights[f"layers.{ilayer}.ffn.shared_experts.gate_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.gate_proj.weight"]
                weights[f"layers.{ilayer}.ffn.shared_experts.up_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.up_proj.weight"]
                weights[f"layers.{ilayer}.ffn.shared_experts.down_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.shared_experts.down_proj.weight"]
                for iexpert in range(self.n_routed_experts):
                    weights[f"layers.{ilayer}.ffn.experts.{iexpert}.gate_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.gate_proj.weight"]
                    weights[f"layers.{ilayer}.ffn.experts.{iexpert}.up_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.up_proj.weight"]
                    weights[f"layers.{ilayer}.ffn.experts.{iexpert}.down_proj.weight"] = self.weight_buffer[f"model.layers.{ilayer}.mlp.experts.{iexpert}.down_proj.weight"]
            # Convert the RMS norm
            weights[f"layers.{ilayer}.attn_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.input_layernorm.weight"]
            weights[f"layers.{ilayer}.ffn_norm.weight"] = self.weight_buffer[f"model.layers.{ilayer}.post_attention_layernorm.weight"]
            # fmt: on
        return weights


if __name__ == "__main__":
    deepseek_coder_v2_lite_instruct_dir = (
        "/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Lite-Instruct"
    )

    ckp_manager = HFCheckpointV2(pathlib.Path(deepseek_coder_v2_lite_instruct_dir))
    ckp_manager.load_all_weights()

    model_spec = get_model_spec("deepseek-coder-v2-nano")
    state_dict = ckp_manager.convert_to_ffw_state_dict(model_spec)
    model = DeepSeekV2(
        ms=model_spec,
        dtype=torch.bfloat16,
        device="cuda",
        process_idx=0,
        num_processes=1,
    )
    print(f"model_spec:\n{model_spec}")
    # fmt: off
    model.load_state_dict(state_dict, strict=True)
    print(f"{utils_for_log.time_string()} Finish loading the model.")
    output_ckpt_dir = "/mnt/efs/augment/checkpoints/dxy/deepseek-ai/DeepSeek-Coder-V2-Nano-Instruct-ffw"
    save_load.save_weights(output_ckpt_dir, state_dict, incremental=True)
    print(f"{utils_for_log.time_string()} Finish saving the fastforward checkpoint into {output_ckpt_dir}.")
    tokens = [100000, 5726, 25, 3708, 245, 3399, 3734, 6712, 279, 9934, 13, 185, 185, 77398, 25]
    # fmt: on
    attn_factory = DeepSeekCoderV2AttentionFactory(model_spec)
    attn = attn_factory(128)
    attn.reset()
    logits = model(tokens, attn).checked_cast(torch.Tensor)
    print(f"logits: {logits}")
    print(f"logits: {logits}")
    print(f"logits: {logits}")
