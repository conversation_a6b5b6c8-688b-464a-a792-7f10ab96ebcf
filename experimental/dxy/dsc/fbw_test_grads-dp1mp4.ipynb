{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "IndentationError", "evalue": "expected an indented block after 'if' statement on line 517 (model.py, line 519)", "output_type": "error", "traceback": ["Traceback \u001b[0;36m(most recent call last)\u001b[0m:\n", "\u001b[0m  File \u001b[1;32m/opt/conda/lib/python3.11/site-packages/IPython/core/interactiveshell.py:3577\u001b[0m in \u001b[1;35mrun_code\u001b[0m\n    exec(code_obj, self.user_global_ns, self.user_ns)\u001b[0m\n", "\u001b[0;36m  Cell \u001b[0;32mIn[2], line 1\u001b[0;36m\n\u001b[0;31m    from research.fastbackward import model as fbw_model\u001b[0;36m\n", "\u001b[0;36m  File \u001b[0;32m~/augment/research/fastbackward/model.py:519\u001b[0;36m\u001b[0m\n\u001b[0;31m    else:\u001b[0m\n\u001b[0m    ^\u001b[0m\n\u001b[0;31mIndentationError\u001b[0m\u001b[0;31m:\u001b[0m expected an indented block after 'if' statement on line 517\n"]}], "source": ["from research.fastbackward import model as fbw_model\n", "from base.fastforward import positional_embeddings\n", "\n", "vocab_size = 4\n", "rotary_config = positional_embeddings.RotaryConfig(\n", "    rotary_ratio=1.0,\n", "    rotary_theta=10000.0,\n", "    max_position_embeddings=163840,\n", "    ext_config=positional_embeddings.YaRNExtensionConfig(\n", "        rotary_scaling_factor=40.0,\n", "        unscaled_max_position_embeddings=4096,\n", "        beta_fast=32,\n", "        beta_slow=1,\n", "        mscale=0.707,\n", "    ),\n", ")\n", "attn_config = fbw_model.DeepSeekV2MLASpec(\n", "    hidden_dim=64,\n", "    num_heads=4,\n", "    v_head_dim=32,\n", "    q_lora_rank=None,\n", "    kv_lora_rank=8,\n", "    qk_rope_head_dim=16,\n", "    qk_nope_head_dim=32,\n", "    eps=1e-6,\n", "    bias=True,\n", ")\n", "ffn_config = fbw_model.GluSpec(hidden_dim=64, intermediate_size=128, bias=False)\n", "\n", "model_args = fbw_model.ModelArgs(\n", "    dim=64,\n", "    n_layers=1,\n", "    n_heads=0,\n", "    n_kv_heads=0,\n", "    vocab_size=vocab_size,\n", "    ffn_type=\"\",\n", "    bias=\"\",\n", "    norm_type=\"layernorm\",\n", "    rotary_config=rotary_config,\n", "    attn_config=attn_config,\n", "    first_layer_ffn_config=fbw_model.GluSpec(\n", "        hidden_dim=64, intermediate_size=128, bias=False\n", "    ),\n", "    ffn_config=ffn_config,\n", "    max_seq_len=128,\n", "    use_sequence_parallel=False,\n", "    use_activation_checkpointing=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.fastbackward.checkpointing import checkpointing\n", "from research.fastbackward.checkpointing.utils import (\n", "    merge_model_parallel_consolidated_checkpoints,\n", "    split_consolidated_checkpoint,\n", ")\n", "from research.fastbackward.tests.fake_distributed_runner import distributed_runner\n", "from research.fastbackward.tests.test_parallel_gradients import (\n", "    _create_model,\n", "    _run_model,\n", "    _assert_params_match,\n", "    _assert_grads_match,\n", ")\n", "import pathlib\n", "\n", "ckptdir = pathlib.Path(\"/home/<USER>/test-grads\")\n", "print(f\"ckptdir: {ckptdir}\")\n", "\n", "with distributed_runner(world_size=1, model_parallel_size=1) as runner:\n", "    runner.run(_create_model, ckptdir, model_args)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "batch = 8\n", "seqlen = 4\n", "gradient_accumulation_steps = 1\n", "data = torch.randint(vocab_size, (batch, seqlen + 1), dtype=torch.int64, device=\"cpu\")\n", "x = data[:, :-1].contiguous()\n", "y = data[:, 1:].contiguous()\n", "print(f\"{x = }\")\n", "print(f\"{y = }\")\n", "\n", "runner_args = (\n", "    _run_model,\n", "    ckptdir,\n", "    model_args,\n", "    x,\n", "    y,\n", "    torch.float32,\n", "    gradient_accumulation_steps,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["with distributed_runner(world_size=1, timeout_s=20.0) as runner:\n", "    # NOTE: we _always_ use gradacc=1 for the reference grads\n", "    runner.run(_run_model, ckptdir, model_args, x, y, torch.float32, 1, \"DP1MP1\")\n", "    ref_params = torch.load(ckptdir / \"DP1MP1-00p.pth\", map_location=\"cpu\")\n", "    ref_grads = torch.load(ckptdir / \"DP1MP1-00g.pth\", map_location=\"cpu\")\n", "    print(f\"There are {len(ref_grads)} gradient tensors.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Condition 1: 2x data-parallel, 1x model-parallel\n", "with distributed_runner(world_size=2, timeout_s=20.0) as runner:\n", "    runner.run(*runner_args, \"DP2MP1\")\n", "for dp_rank in range(2):\n", "    params = torch.load(ckptdir / f\"DP2MP1-{dp_rank}0p.pth\", map_location=\"cpu\")\n", "    _assert_params_match(params, ref_params, label=f\"DP2MP1-DR{dp_rank}\")\n", "    grads = torch.load(ckptdir / f\"DP2MP1-{dp_rank}0g.pth\", map_location=\"cpu\")\n", "    _assert_grads_match(\n", "        grads,\n", "        ref_grads,\n", "        label=f\"DP2MP1-DR{dp_rank}\",\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Condition 2: 1x data-parallel, 2x model-parallel\n", "with distributed_runner(\n", "    world_size=2,\n", "    model_parallel_size=2,\n", "    model_parallel_subgroups=None,\n", "    timeout_s=20.0,\n", ") as runner:\n", "    runner.run(*runner_args, \"DP1MP2\")\n", "\n", "param_sds = [\n", "    torch.load(ckptdir / f\"DP1MP2-0{i}p.pth\", map_location=\"cpu\") for i in range(2)\n", "]\n", "params = merge_model_parallel_consolidated_checkpoints(model_args, param_sds)\n", "_assert_params_match(params, ref_params, label=\"DP1MP2\")\n", "\n", "grad_sds = [\n", "    torch.load(ckptdir / f\"DP1MP2-0{i}g.pth\", map_location=\"cpu\") for i in range(2)\n", "]\n", "grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)\n", "_assert_grads_match(grads, ref_grads, label=\"DP1MP2\", rtol=1e-4, atol=1e-4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Condition 4: 1x data-parallel, 4x model-parallel\n", "with distributed_runner(\n", "    world_size=4,\n", "    model_parallel_size=4,\n", "    model_parallel_subgroups=None,\n", "    timeout_s=30.0,\n", ") as runner:\n", "    runner.run(*runner_args, \"DP1MP4\")\n", "\n", "param_sds = [\n", "    torch.load(ckptdir / f\"DP1MP4-0{i}p.pth\", map_location=\"cpu\") for i in range(4)\n", "]\n", "params = merge_model_parallel_consolidated_checkpoints(model_args, param_sds)\n", "_assert_params_match(params, ref_params, label=\"DP1MP4\")\n", "\n", "grad_sds = [\n", "    torch.load(ckptdir / f\"DP1MP4-0{i}g.pth\", map_location=\"cpu\") for i in range(4)\n", "]\n", "grads = merge_model_parallel_consolidated_checkpoints(model_args, grad_sds)\n", "_assert_grads_match(grads, ref_grads, label=\"DP1MP4\", rtol=1e-4, atol=1e-4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["key = \"layers.1.attention.kv_a_proj_with_mqa.bias\"\n", "print(f\"{grad_sds[0][key].shape = }\")\n", "# indexes = slice(0, 16)\n", "indexes = slice(16, 48)\n", "print(grad_sds[0][key][indexes])\n", "print(grad_sds[1][key][indexes])\n", "print(grad_sds[2][key][indexes])\n", "print(grad_sds[3][key][indexes])\n", "\n", "print((grad_sds[0][key][indexes] + grad_sds[1][key][indexes] + grad_sds[2][key][indexes] + grad_sds[3][key][indexes]) / 4)\n", "\n", "print(f\"\\n{ref_grads[key].shape = }\")\n", "print(ref_grads[key][indexes])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["key = \"layers.1.attention.kv_a_proj_with_mqa.bias\"\n", "print(f\"{grad_sds[0][key].shape = }\")\n", "indexes = slice(16, 32)\n", "print(grad_sds[0][key][indexes])\n", "print(grad_sds[1][key][indexes])\n", "\n", "\n", "print(f\"\\n{ref_grads[key].shape = }\")\n", "print(ref_grads[key][indexes])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for key, ref_grad in ref_grads.items():\n", "    print(f\"key: {key}, grad: {ref_grad.shape = }\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}