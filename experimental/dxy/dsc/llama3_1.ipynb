{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokens: [128000, 128006, 882, 128007, 271, 5040, 264, 4062, 3460, 12384, 304, 10344, 13, 128009, 128006, 78191, 128007, 271]\n", "Prompt:\n", "<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n", "\n", "write a quick sort algorithm in python.<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "\n"]}], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "\n", "ckp_dir = \"/mnt/efs/augment/checkpoints/llama3.1/hf/Meta-Llama-3.1-8B-Instruct\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(ckp_dir, trust_remote_code=True)\n", "messages=[\n", "    { 'role': 'user', 'content': \"write a quick sort algorithm in python.\"}\n", "]\n", "hf_inputs = tokenizer.apply_chat_template(messages, add_generation_prompt=True, return_tensors=\"pt\")\n", "tokens = hf_inputs.cpu().numpy()[0].tolist()  # type: ignore\n", "prompt = tokenizer.decode(tokens)\n", "print(f\"Tokens: {tokens}\")\n", "print(f\"Prompt:\\n{prompt}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "29fbdccfbd3c4080bb33cbe1ff592c6e", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["The attention mask and the pad token id were not set. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n", "Setting `pad_token_id` to `eos_token_id`:128009 for open-end generation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["**Quick Sort Algorithm in Python**\n", "=====================================\n", "\n", "Quick sort is a divide-and-conquer algorithm that picks a 'pivot' element from the array and partitions the other elements into two sub-arrays, according to whether they are less than or greater than the pivot. The sub-arrays are then recursively sorted.\n", "\n", "**Implementation**\n", "-----------------\n", "\n", "```python\n", "def quick_sort(arr):\n", "    \"\"\"\n", "    Sorts an array using the quick sort algorithm.\n", "\n", "    Args:\n", "        arr (list): The array to be sorted.\n", "\n", "    Returns:\n", "        list: The sorted array.\n", "    \"\"\"\n", "    if len(arr) <= 1:\n", "        # Base case: If the array has one or zero elements, it is already sorted.\n", "        return arr\n", "    else:\n", "        # Select the pivot element (in this case, the middle element).\n", "        pivot = arr[len(arr) // 2]\n", "        \n", "        # Divide the array into three sub-arrays: elements less than the pivot,\n", "        # elements equal to the pivot, and elements greater than the pivot.\n", "        left = [x for x in arr if x < pivot]\n", "        middle = [x for x in arr if x == pivot]\n", "        right = [x for x in arr if x > pivot]\n", "        \n", "        # Recursively sort the sub-arrays and combine the results.\n", "        return quick_sort(left) + middle + quick_sort(right)\n", "\n", "# Example usage:\n", "arr = [5, 2, 8, 3, 1, 6, 4]\n", "print(quick_sort(arr))  # Output: [1, 2, 3, 4, 5, 6, 8]\n", "```\n", "\n", "**Explanation**\n", "---------------\n", "\n", "1.  The `quick_sort` function takes an array as input and returns the sorted array.\n", "2.  The base case is when the array has one or zero elements, in which case it is already sorted.\n", "3.  The pivot element is selected as the middle element of the array.\n", "4.  The array is divided into three sub-arrays: `left`, `middle`, and `right`, based on whether each element is less than, equal to, or greater than the pivot.\n", "5.  The `left` and `right` sub-arrays are recursively sorted using the `quick_sort` function.\n", "6.  The sorted `left` and `right` sub-arrays are combined with the `middle` sub-array to produce the final sorted array.\n", "\n", "**Time Complexity**\n", "-------------------\n", "\n", "The time complexity of the quick\n"]}], "source": ["hf_model = AutoModelForCausalLM.from_pretrained(ckp_dir, trust_remote_code=True, torch_dtype=torch.bfloat16).cuda()\n", "hf_inputs = hf_inputs.to(hf_model.device) # type: ignore\n", "# tokenizer.eos_token_id is the id of <｜end▁of▁sentence｜>  token\n", "outputs = hf_model.generate(hf_inputs, max_new_tokens=128, do_sample=False, top_k=50, top_p=0.95, num_return_sequences=1, eos_token_id=tokenizer.eos_token_id)\n", "print(tokenizer.decode(outputs[0][len(hf_inputs[0]):], skip_special_tokens=True))\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}