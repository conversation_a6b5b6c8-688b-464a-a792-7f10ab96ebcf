from google.cloud import bigtable
from google.cloud.bigtable import column_family
import datetime

# Configure the client
project_id = "system-services-dev"
instance_id = "bigtable-central-dev"
table_id = "dev-mike-settings"

# Source and target row keys
source_key = "270110ad199a266d7d6cec4f58a5a0ca#user#c2d1bfe6-9a55-497a-a8b4-a455b492ced0#settings"
target_key = "270110ad199a266d7d6cec4f58a5a0ca#user#mike#settings"

# Create a client
client = bigtable.Client(project=project_id, admin=True)
instance = client.instance(instance_id)
table = instance.table(table_id)

# Read source row
row_key = source_key.encode("utf-8")
row = table.read_row(row_key)
print(row.cells)

if row:
    # Find the cell in the Settings family with github_user_settings qualifier
    cf_name = "Settings"
    qualifier = b"github_user_settings"  # Changed to bytes
    version_qualifier = b"version"

    if cf_name in row.cells and qualifier in row.cells[cf_name]:
        # Get the binary value from the source row
        binary_value = row.cells[cf_name][qualifier][0].value
        print("read:", binary_value)
        version_binary_value = row.cells[cf_name][version_qualifier][0].value
        print("version:", version_binary_value)

        # Write to the target row
        target_row_key = target_key.encode("utf-8")
        row_to_write = table.direct_row(target_row_key)
        row_to_write.set_cell(cf_name, qualifier, binary_value)
        row_to_write.set_cell(cf_name, version_qualifier, version_binary_value)
        row_to_write.commit()

        print(f"Successfully copied binary data from {source_key} to {target_key}")
    else:
        print(
            f"Column family {cf_name} or qualifier {qualifier} not found in source row"
        )
else:
    print(f"Source row {source_key} not found")
