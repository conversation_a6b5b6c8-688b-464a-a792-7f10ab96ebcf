#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to read a range of rows from BigTable in the development environment.

This script connects to the bigtable-central-dev instance and allows you to:
1. List all tables in the instance
2. Read a specific row by key
3. Read a range of rows by prefix or start/end keys
4. Count rows by prefix
5. Delete rows by prefix (with dry run option)

Usage:
  python bigtable_row_range.py --list-tables
  python bigtable_row_range.py --table dev-mike-content-manager --row-key "your-row-key"
  python bigtable_row_range.py --table dev-mike-content-manager --prefix "your-prefix"
  python bigtable_row_range.py --table dev-mike-content-manager --start-key "start" --end-key "end"
  python bigtable_row_range.py --table dev-mike-content-manager --count-prefix "your-prefix"
  python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "your-prefix" --dry-run
  python bigtable_row_range.py --table dev-mike-content-manager --delete-prefix "your-prefix" --execute
"""

import argparse
import time
from collections import defaultdict
from typing import Dict, List, Tuple

from google.cloud import bigtable


# Constants for the development environment
PROJECT_ID = "system-services-dev"
INSTANCE_ID = "bigtable-central-dev"


def get_bigtable_client():
    """Create and return a BigTable client."""
    return bigtable.Client(project=PROJECT_ID, admin=True)


def list_tables():
    """List all tables in the BigTable instance."""
    client = get_bigtable_client()
    instance = client.instance(INSTANCE_ID)

    print(f"Tables in {INSTANCE_ID}:")
    for table in instance.list_tables():
        print(f"- {table.table_id}")


def read_row(table_id: str, row_key: str):
    """Read a single row from BigTable by its key."""
    client = get_bigtable_client()
    instance = client.instance(INSTANCE_ID)
    table = instance.table(table_id)

    row = table.read_row(row_key.encode())
    if not row:
        print(f"Row with key '{row_key}' not found.")
        return

    print(f"Row key: {row.row_key.decode()}")
    for column_family, columns in row.cells.items():
        for column, cells in columns.items():
            for cell in cells:
                try:
                    value = cell.value.decode()
                except UnicodeDecodeError:
                    value = f"<binary data of length {len(cell.value)}>"
                print(f"  {column_family}:{column.decode()} = {value}")
                print(f"    Timestamp: {cell.timestamp}")


def read_rows_by_prefix(table_id: str, prefix: str):
    """Read rows from BigTable by prefix."""
    client = get_bigtable_client()
    instance = client.instance(INSTANCE_ID)
    table = instance.table(table_id)

    # Create a prefix row range
    prefix_bytes = prefix.encode()

    print(f"Reading rows with prefix '{prefix}':")
    rows = table.read_rows(start_key=prefix_bytes, end_key=prefix_bytes + b"\xff")

    row_count = 0
    for row in rows:
        row_count += 1
        print(f"\nRow key: {row.row_key.decode()}")
        for column_family, columns in row.cells.items():
            for column, cells in columns.items():
                for cell in cells:
                    try:
                        value = cell.value.decode()
                    except UnicodeDecodeError:
                        value = f"<binary data of length {len(cell.value)}>"
                    print(f"  {column_family}:{column.decode()} = {value}")

    if row_count == 0:
        print(f"No rows found with prefix '{prefix}'.")
    else:
        print(f"\nTotal rows: {row_count}")


def read_row_range(table_id: str, start_key: str, end_key: str):
    """Read a range of rows from BigTable between start_key and end_key."""
    client = get_bigtable_client()
    instance = client.instance(INSTANCE_ID)
    table = instance.table(table_id)

    # Create a row range

    print(f"Reading rows from '{start_key}' to '{end_key}':")
    rows = table.read_rows(start_key=start_key.encode(), end_key=end_key.encode())

    row_count = 0
    for row in rows:
        row_count += 1
        print(f"\nRow key: {row.row_key.decode()}")
        for column_family, columns in row.cells.items():
            for column, cells in columns.items():
                for cell in cells:
                    try:
                        value = cell.value.decode()
                    except UnicodeDecodeError:
                        value = f"<binary data of length {len(cell.value)}>"
                    print(f"  {column_family}:{column.decode()} = {value}")

    if row_count == 0:
        print(f"No rows found in range from '{start_key}' to '{end_key}'.")
    else:
        print(f"\nTotal rows: {row_count}")


def list_by_prefix(table_id: str, prefix: str) -> Dict[str, int]:
    """Count rows by prefix and return a dictionary of counts."""
    client = get_bigtable_client()
    instance = client.instance(INSTANCE_ID)
    table = instance.table(table_id)

    # Create a prefix row range if prefix is provided
    if prefix:
        prefix_bytes = prefix.encode()
        rows = table.read_rows(start_key=prefix_bytes, end_key=prefix_bytes + b"\xff")
    else:
        rows = table.read_rows()

    count = defaultdict(int)
    total_rows = 0

    for row in rows:
        total_rows += 1
        key = row.row_key.decode()
        print(f"{key}")
        parts = key.split("#")
        if len(parts) > 1:
            # Count by the first part of the key (before the first #)
            prefix_key = parts[0]
            count[prefix_key] += 1
        else:
            # If there's no #, use the whole key
            count[key] += 1

    print(f"Total rows: {total_rows}")
    print("Counts by prefix:")
    for prefix_key, count_value in sorted(
        count.items(), key=lambda x: x[1], reverse=True
    ):
        print(f"  {prefix_key}: {count_value}")

    return dict(count)


def delete_by_prefix(
    table_id: str, prefix: str, dry_run: bool = True
) -> Tuple[int, List[str]]:
    """Delete rows from BigTable by prefix.

    Args:
        table_id: The ID of the table to delete from
        prefix: The prefix of the rows to delete
        dry_run: If True, only print the rows that would be deleted without actually deleting them

    Returns:
        A tuple of (number of rows deleted, list of deleted row keys)
    """
    client = get_bigtable_client()
    instance = client.instance(INSTANCE_ID)
    table = instance.table(table_id)

    # First, read all rows with the prefix to show what will be deleted
    prefix_bytes = prefix.encode()
    rows = table.read_rows(start_key=prefix_bytes, end_key=prefix_bytes + b"\xff")

    # Collect all the row keys
    row_keys = []
    for row in rows:
        row_keys.append(row.row_key)

    if not row_keys:
        print(f"No rows found with prefix '{prefix}'.")
        return 0, []

    # Print the rows that will be deleted
    print(f"Found {len(row_keys)} rows with prefix '{prefix}':")
    for i, row_key in enumerate(row_keys):
        print(f"  {i+1}. {row_key.decode()}")

    if dry_run:
        print(
            "\nDRY RUN: No rows were deleted. Use --execute to actually delete the rows."
        )
        return 0, []

    # Confirm deletion if not in dry run mode
    print(
        f"\nWARNING: You are about to delete {len(row_keys)} rows from table '{table_id}'"
    )
    print("Type 'yes' to confirm deletion: ")
    confirmation = input().strip().lower()

    if confirmation != "yes":
        print("Deletion cancelled.")
        return 0, []

    # Delete the rows in batches to avoid timeouts
    batch_size = 100
    deleted_keys = []

    for i in range(0, len(row_keys), batch_size):
        batch = row_keys[i : i + batch_size]
        print(
            f"Deleting batch {i//batch_size + 1}/{(len(row_keys)-1)//batch_size + 1} ({len(batch)} rows)..."
        )

        # Create a batch of mutations (one delete per row)
        rows = []
        for row_key in batch:
            row = table.direct_row(row_key)
            row.delete()
            rows.append(row)

        # Apply the mutations
        table.mutate_rows(rows)
        deleted_keys.extend([key.decode() for key in batch])

        # Small delay to avoid overwhelming the server
        time.sleep(0.1)

    print(f"Successfully deleted {len(deleted_keys)} rows with prefix '{prefix}'.")
    return len(deleted_keys), deleted_keys


def main():
    parser = argparse.ArgumentParser(
        description="Read rows from BigTable in the development environment."
    )
    parser.add_argument(
        "--list-tables", action="store_true", help="List all tables in the instance"
    )
    parser.add_argument("--table", help="The table ID to read from")
    parser.add_argument("--row-key", help="Read a specific row by key")
    parser.add_argument("--prefix", help="Read rows with this prefix")
    parser.add_argument("--start-key", help="Start key for row range")
    parser.add_argument("--end-key", help="End key for row range")
    parser.add_argument(
        "--list-prefix", help="list row key by prefix (use empty string for all rows)"
    )
    parser.add_argument("--delete-prefix", help="Delete rows with this prefix")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        default=True,
        help="Only show what would be deleted (default)",
    )
    parser.add_argument(
        "--execute",
        action="store_false",
        dest="dry_run",
        help="Actually delete the rows (turns off dry run)",
    )

    args = parser.parse_args()

    if args.list_tables:
        list_tables()
        return

    if not args.table:
        print("Error: --table is required unless --list-tables is specified")
        parser.print_help()
        return

    if args.row_key:
        read_row(args.table, args.row_key)
    elif args.prefix:
        read_rows_by_prefix(args.table, args.prefix)
    elif args.start_key and args.end_key:
        read_row_range(args.table, args.start_key, args.end_key)
    elif args.list_prefix is not None:  # Allow empty string
        list_by_prefix(args.table, args.list_prefix)
    elif args.delete_prefix:
        delete_by_prefix(args.table, args.delete_prefix, args.dry_run)
    else:
        print(
            "Error: You must specify one of --row-key, --prefix, --start-key and --end-key, --count-prefix, or --delete-prefix"
        )
        parser.print_help()


if __name__ == "__main__":
    main()
