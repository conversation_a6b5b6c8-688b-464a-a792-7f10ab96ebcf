{"cells": [{"cell_type": "code", "execution_count": null, "id": "fcf491e6", "metadata": {}, "outputs": [], "source": ["import polars as pl\n", "import pickle\n", "\n", "# parquet_path = \"/mnt/efs/augment/user/mike/test_vanguard/test_vanguard_permissive/mixdata/mix_empty10\"\n", "\n", "# df = pl.read_parquet(parquet_path)\n", "# samples = df.head(20)\n", "\n", "# print(samples.columns)\n", "# for sample in samples.iter_rows(named=True):\n", "#     pause_spans = pickle.loads(sample[\"middle_spans\"])\n", "#     pretty_print_pause_spans(\n", "#         sample[\"file_path\"],\n", "#         sample[\"prefix\"],\n", "#         sample[\"suffix\"],\n", "#         sample[\"original_ground_truth\"],\n", "#         [pause_span.content.range for pause_span in pause_spans],\n", "#     )"]}, {"cell_type": "markdown", "id": "913e6700", "metadata": {}, "source": ["# Hindsight Training Data Examination"]}, {"cell_type": "code", "execution_count": null, "id": "8444386a", "metadata": {}, "outputs": [], "source": ["# import MMIndexedDataset\n", "from megatron.data.indexed_dataset import MMapIndexedDataset\n", "\n", "eval_dataset_path = \"/mnt/efs/augment/user/mike/hindsight_pipeline/dogfood/dogfood_groundtruth_2024-11-01_2025-01-14/indexed_dataset/8352b_1024s_1024r_2048d/dataset\"\n", "eval_dataset = MMapIndexedDataset(eval_dataset_path, skip_warmup=True)\n", "print(len(eval_dataset))\n", "\n", "old_eval_dataset_path = \"/mnt/efs/augment/user/pranay/hindsight/dogfood_2024-11-15_2024-12-14/datasets/8352b_1024s_1024r_2048d_1976l/dataset\"\n", "old_eval_dataset = MMapIndexedDataset(old_eval_dataset_path, skip_warmup=True)\n", "print(len(old_eval_dataset))\n", "\n", "train_dataset_path = \"/mnt/efs/augment/user/mike/hindsight_pipeline/vanguard/vanguard_permissive_2024-11-01_2025-01-14/indexed_dataset/mix_empty15_8352b_1024s_1024r_2048d/dataset\"\n", "train_dataset = MMapIndexedDataset(train_dataset_path, skip_warmup=True)\n", "print(len(train_dataset))\n", "\n", "old_train_dataset_path = \"/mnt/efs/augment/user/pranay/hindsight/datasets/vanguard_permissive_mix_empty15_2024-11-01_2025-01-14/i0-vanguard0/datasets/8352b_1024s_1024r_2048d_1976l/dataset\"\n", "old_train_dataset = MMapIndexedDataset(old_train_dataset_path, skip_warmup=True)\n", "print(len(old_train_dataset))"]}, {"cell_type": "code", "execution_count": null, "id": "95eea22d", "metadata": {}, "outputs": [], "source": ["from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer\n", "import numpy as np\n", "from colorama import Fore, Style\n", "\n", "tokenizer = Qwen25CoderTokenizer()\n", "\n", "\n", "# print(f\"fim token: {tokenizer.special_tokens.fim_middle}, pause token: {tokenizer.special_tokens.pause}, eos token: {tokenizer.special_tokens.eos}, padding token: {tokenizer.special_tokens.padding}\")\n", "def get_special_token_stats(dataset):\n", "    for s in dataset[:2000]:\n", "        if tokenizer.special_tokens.padding in s:\n", "            s = s[: list(s).index(tokenizer.special_tokens.padding)]\n", "        fim_indices = np.where(s == tokenizer.special_tokens.fim_middle)\n", "        padding_indices = np.where(s == tokenizer.special_tokens.padding)\n", "        pause_indices = np.where(s == tokenizer.special_tokens.pause)\n", "        eos_indices = np.where(s == tokenizer.special_tokens.eos)\n", "        if len(pause_indices[0]) > 3:\n", "            print(\n", "                f\"{Fore.YELLOW}fim: {len(fim_indices[0])}, pause: {len(pause_indices[0])}, eos: {len(eos_indices[0])}, padding: {len(padding_indices[0])}{Style.RESET_ALL}\"\n", "            )\n", "            print(f\"pause indices: {pause_indices}\")\n", "            print(f\"eos indices: {eos_indices}\")\n", "            print(\"long pauses:\")\n", "            s = s.tolist()\n", "            s = s[\n", "                : s.index(tokenizer.special_tokens.padding)\n", "                if tokenizer.special_tokens.padding in s\n", "                else len(s)\n", "            ]\n", "            fim_idx = s.index(tokenizer.special_tokens.fim_middle)\n", "            prefix = s[: fim_idx + 1]\n", "            suffix = s[fim_idx + 1 :]\n", "            print(\n", "                f\"{''.join(tokenizer.detokenize(prefix).splitlines(keepends=True)[-5:])}\",\n", "                end=\"\",\n", "            )\n", "            print(f\"{Fore.RED}{tokenizer.detokenize(suffix)}{Style.RESET_ALL}\")\n", "\n", "\n", "# print(\"old train dataset\")\n", "# get_special_token_stats(old_train_dataset)\n", "# print(\"new train dataset\")\n", "# get_special_token_stats(train_dataset)\n", "# print(\"old eval dataset\")\n", "# get_special_token_stats(old_eval_dataset)\n", "print(\"new eval dataset\")\n", "get_special_token_stats(eval_dataset)"]}, {"cell_type": "markdown", "id": "81ca5457", "metadata": {}, "source": ["# Hindsight data examination"]}, {"cell_type": "code", "execution_count": null, "id": "55bcc7fa", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "from experimental.pranay.hindsight_training.pause_behavior import (\n", "    load_hindsight_data,\n", ")\n", "from research.eval.harness.utils import read_jsonl_zst\n", "\n", "from research.static_analysis.parsing import GlobalTsParser\n", "from pathlib import Path\n", "\n", "\n", "def get_model_stats(path: str):\n", "    data = read_jsonl_zst(Path(path))\n", "    model_stats = {}\n", "    for datum in data:\n", "        model = datum[\"completion\"][\"response\"][\"model\"]\n", "        if model not in model_stats:\n", "            model_stats[model] = 0\n", "        model_stats[model] += 1\n", "    return model_stats"]}, {"cell_type": "code", "execution_count": null, "id": "1845c898", "metadata": {}, "outputs": [], "source": ["from multiprocessing import Pool\n", "\n", "h0412 = \"/mnt/efs/augment/data/eval/hindsight/2025-04-12-5d-v1.6/dogfood-shard/data.jsonl.zst\"\n", "h0115 = \"/mnt/efs/augment/data/eval/hindsight/2025-01-15-14d-v1.4/dogfood-shard/data.jsonl.zst\"\n", "h1117 = \"/mnt/efs/augment/data/eval/hindsight/2024-11-17-18d-v1.3/dogfood-shard/data.jsonl.zst\"\n", "\n", "\n", "with Pool(5) as p:\n", "    results = p.map(get_model_stats, [h0412, h0115, h1117])\n", "    for result in results:\n", "        print(result)"]}, {"cell_type": "code", "execution_count": null, "id": "b385a8dd", "metadata": {}, "outputs": [], "source": ["print(results)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}