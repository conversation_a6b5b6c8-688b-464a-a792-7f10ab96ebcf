import json
from pathlib import Path
from base.datasets.completion_dataset_gcs import CompletionDataset, Filters
from base.datasets import tenants
from base.datasets.completion import CompletionDatum
from base.datasets.hindsight_completion import HindsightCompletionDatum
from research.core.utils_for_file import write_jsonl_zst
import random
import zstandard as zstd
from colorama import Fore, Style

from datetime import datetime
from base.tokenizers import create_tokenizer_by_name
from research.models.fastforward_llama_models import LLAMA_FastForwardModel
from research.models.meta_model import GenerationOptions
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from research.models.meta_model import RawGenerateOutput
from typing import cast

import contextlib
import io
import sys
import torch
import gc
import numpy as np
from tqdm import tqdm
from megatron.data.indexed_dataset import MMapIndexedDataset

from research.fastbackward.metrics import FimExactMatchRate
from research.fastbackward.data import IGNORE_LABEL, unmask_token


class ExecuteModel:
    _instances = []

    @contextlib.contextmanager
    def _suppress_output(self):
        """Context manager to suppress stdout temporarily while keeping stderr."""
        stdout = sys.stdout
        output = io.StringIO()
        try:
            sys.stdout = output
            yield
        finally:
            sys.stdout = stdout

    def __init__(
        self,
        checkpoint: str,
        sha: str,
        tokenizer: Qwen25CoderTokenizer,
        use_fp8: bool = False,
    ):
        with self._suppress_output():
            self.tokenizer = tokenizer
            self.config = {
                "name": "fastforward_qwen25coder_14b",
                "checkpoint_path": checkpoint,
                "checkpoint_sha256": sha,
                "sequence_length": 8648,  # 6600,
                "use_fp8": use_fp8,
            }
            from research.eval.harness.factories import create_model

            self.model = None
            self._model_creator = lambda: cast(
                LLAMA_FastForwardModel, create_model(self.config)
            )

            self.options = GenerationOptions(
                max_generated_tokens=256,
                stop_tokens=[
                    tokenizer.special_tokens.eos,
                    tokenizer.special_tokens.pause,
                ],
            )

            ExecuteModel._instances.append(self)

    def ensure_loaded(self):
        """Ensure this model is loaded and others are unloaded."""
        with self._suppress_output():
            # Unload all other models
            for instance in ExecuteModel._instances:
                if instance is not self and instance.model is not None:
                    instance.deep_unload()

            # Load this model if needed
            if self.model is None:
                self.model = self._model_creator()
                self.model.load()

    def __call__(
        self, prompt_tokens: list[int], keep_pause_tokens: bool = False
    ) -> list[int]:
        with self._suppress_output():
            self.ensure_loaded()
            assert self.model is not None
            # st = self.tokenizer.special_tokens
            generated_tokens = self.model.raw_generate_tokens(
                prompt_tokens, options=self.options
            ).tokens

            # original_length = len(generated_tokens)
            # generated_tokens, _ = self.extract_generation_before_stop_tokens(
            #     generated_tokens, [st.eos]
            # )
            # generation_stopped_at_max_length = original_length == len(generated_tokens)
            # if generation_stopped_at_max_length and st.pause is not None:
            #     for index in reversed(range(len(generated_tokens))):
            #         if generated_tokens[index] == st.pause:
            #             generated_tokens = generated_tokens[:index]
            #             break

            # if keep_pause_tokens:
            #     return generated_tokens

            # generated_tokens = [
            #     token for token in generated_tokens if token != st.pause
            # ]
            return generated_tokens

    def simple_call(
        self, prompt_tokens: list[int], max_tokens: int = 256
    ) -> RawGenerateOutput:
        with self._suppress_output():
            self.ensure_loaded()
            assert self.model is not None
            st = self.tokenizer.special_tokens
            options = GenerationOptions(
                max_generated_tokens=max_tokens, stop_tokens=[st.eos, st.pause]
            )
            return self.model.raw_generate_tokens(prompt_tokens, options=options)

    def forward_pass_for_logits(self, full_prompt: torch.Tensor) -> torch.Tensor:
        with self._suppress_output():
            self.ensure_loaded()
            assert self.model is not None
            return self.model.forward_pass_single_logits(full_prompt)

    def deep_unload(self):
        with self._suppress_output():
            if self.model is not None:
                self.model.unload()
                self.model = None
                gc.collect()
                torch.cuda.empty_cache()

    def extract_generation_before_stop_tokens(
        self, generated: list[int], stop_token_ids: list[int | None]
    ) -> tuple[list[int], int | None]:
        stop_tokens_ids_set = {
            token_id for token_id in stop_token_ids if token_id is not None
        }
        fim_stop_token_id = None
        for index in range(len(generated)):
            if generated[index] in stop_tokens_ids_set:
                fim_stop_token_id = generated[index]
                generated = generated[:index]
                break
        return generated, fim_stop_token_id


def get_prompt_target(
    tokens: list[int] | np.ndarray, tokenizer: Qwen25CoderTokenizer
) -> tuple[list[int], list[int]]:
    """Extract prompt and target tokens from concatenated tokens."""
    if isinstance(tokens, np.ndarray):
        tokens = tokens.tolist()

    # Get special tokens
    fim_middle = tokenizer.special_tokens.fim_middle
    padding = tokenizer.special_tokens.padding

    # Remove padding tokens
    if padding in tokens:
        tokens = tokens[: tokens.index(padding)]

    # Find fim_middle token
    if fim_middle not in tokens:
        raise ValueError("fim_middle token not found in tokens")

    middle_idx = tokens.index(fim_middle)
    prompt_tokens = tokens[: middle_idx + 1]  # Include fim_middle in prompt
    target_tokens = tokens[middle_idx + 1 :]  # Start after fim_middle

    return prompt_tokens, target_tokens


def simple_eval(
    dataset: MMapIndexedDataset,
    model: ExecuteModel,
    tokenizer: Qwen25CoderTokenizer,
    num_samples: int = 100,
    output_path: str = "experimental/mike/hindsight_training/eval/exact_match_results.jsonl",
) -> dict:
    """Simple evaluation function."""
    exact_matches = 0
    total_chars_generated = 0
    valid_samples = 0

    print(f"Running evaluation on {num_samples} samples...")
    results = []
    for i in tqdm(range(min(num_samples, len(dataset)))):
        try:
            # Get prompt and target
            prompt_tokens, target_tokens = get_prompt_target(dataset[i], tokenizer)

            # # Strip EOS from target tokens if present
            # if target_tokens and target_tokens[-1] == eos:
            #     target_tokens = target_tokens[:-1]

            # Skip if target is empty after stripping EOS
            if not target_tokens:
                continue

            target_token_stop = min(
                target_tokens.index(tokenizer.special_tokens.eos) + 1
                if tokenizer.special_tokens.eos in target_tokens
                else len(target_tokens),
                target_tokens.index(tokenizer.special_tokens.pause) + 1
                if tokenizer.special_tokens.pause in target_tokens
                else len(target_tokens),
            )
            target_tokens = target_tokens[:target_token_stop]

            # Get model prediction
            # logits = model.forward_pass_for_logits(
            #     torch.tensor(prompt_tokens + target_tokens).to("cuda")
            # )
            # predicted_tokens_tensor = torch.argmax(logits, dim=-1)

            # # Align predicted tokens with target tokens by shifting.
            # # The logit at index i is for predicting token i+1.
            # # So, to get predictions for target_tokens, we need logits from
            # # len(prompt_tokens)-1 up to the length of the target_tokens.
            # predicted_tokens_tensor = predicted_tokens_tensor[
            #     len(prompt_tokens) - 1 : len(prompt_tokens) - 1 + len(target_tokens)
            # ]
            # predicted_tokens = predicted_tokens_tensor.tolist()

            # use model generation to eval generation length
            predicted_tokens = model(prompt_tokens)
            predicted_tokens_tensor = torch.tensor(predicted_tokens)
            target_tokens_tensor = torch.tensor(target_tokens).to(
                predicted_tokens_tensor.device
            )

            # # Convert to text for comparison
            is_exact_match = False
            target_text = tokenizer.detokenize(target_tokens)
            predicted_text = tokenizer.detokenize(predicted_tokens)
            if predicted_tokens_tensor.equal(target_tokens_tensor):
                is_exact_match = True
                exact_matches += 1

            # Count characters generated
            total_chars_generated += len(predicted_text)
            valid_samples += 1

            # Print first few examples for debugging
            if i < 10 or random.random() < 0.05:
                print(f"\n--- Sample {i} ---")
                print(f"Target: {Fore.GREEN}{target_text}{Style.RESET_ALL}")
                print(f"Predicted: {Fore.BLUE}{predicted_text}{Style.RESET_ALL}")
                print(f"Match: {is_exact_match}")
            if i % 120 == 0:
                print(
                    f"{Fore.YELLOW}Progress result at {i} match rate: {exact_matches}/{valid_samples}={exact_matches / valid_samples:.3f}"
                )
                print(
                    f"{Fore.YELLOW}Avg chars generated: {total_chars_generated / valid_samples}{Style.RESET_ALL}"
                )
            results.append(
                {
                    "index": i,
                    "prompt": tokenizer.detokenize(prompt_tokens),
                    "predicted": predicted_tokens,
                    "target": tokenizer.detokenize(target_tokens),
                    "is_exact_match": is_exact_match,
                }
            )

        except Exception as e:
            print(f"Error processing sample {i}: {e}")
            continue

    # Calculate metrics
    exact_match_rate = exact_matches / valid_samples if valid_samples > 0 else 0
    avg_chars_generated = (
        total_chars_generated / valid_samples if valid_samples > 0 else 0
    )

    print(f"\n{Fore.YELLOW}=== Evaluation Results ==={Style.RESET_ALL}")
    print(f"Valid samples: {valid_samples}/{num_samples}")
    print(f"Exact match rate: {exact_match_rate:.3f} ({exact_matches}/{valid_samples})")
    print(f"Average characters generated: {avg_chars_generated:.1f}")
    print(f"Saving results to {output_path}...")
    with open(output_path, "w") as f:
        for result in results:
            f.write(json.dumps(result) + "\n")

    return {
        "exact_match_rate": exact_match_rate,
        "avg_chars_generated": avg_chars_generated,
        "exact_matches": exact_matches,
        "valid_samples": valid_samples,
        "total_samples": num_samples,
    }


def load_dataset(dataset_path: str) -> MMapIndexedDataset:
    """Load dataset for evaluation."""
    dataset = MMapIndexedDataset(dataset_path)
    print(f"Dataset loaded with {len(dataset)} samples")
    return dataset


def load_model_and_tokenizer(
    checkpoint: str, sha: str, use_fp8: bool = False
) -> tuple[ExecuteModel, Qwen25CoderTokenizer]:
    """Load model and tokenizer for evaluation."""
    # Create tokenizer
    tokenizer: Qwen25CoderTokenizer = cast(
        Qwen25CoderTokenizer, create_tokenizer_by_name("qwen25coder")
    )

    # Load model
    model = ExecuteModel(
        checkpoint=checkpoint,
        sha=sha,  # Add SHA if needed
        tokenizer=tokenizer,
        use_fp8=use_fp8,
    )
    print("Loading model to GPU...")
    model.ensure_loaded()
    print("model loaded!")
    return model, tokenizer


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Evaluate a local model.")
    parser.add_argument(
        "--checkpoint",
        type=str,
        default="/mnt/efs/augment/checkpoints/mike/completion-v3-3-3-ffw",
        help="Path to the model checkpoint.",
    )
    parser.add_argument(
        "--sha",
        type=str,
        default="92a01e5c40dede9d0e149e446fda71d0de232a2f0daf3f39d0ba1945e2290699",
        help="SHA256 of the model checkpoint.",
    )
    parser.add_argument(
        "--dataset-path",
        type=str,
        default="/mnt/efs/augment/user/mike/hindsight_pipeline/dogfood/dogfood_groundtruth_2024-11-01_2025-01-14/indexed_dataset/8352b_1024s_1024r_2048d/dataset",
        help="Path to the dataset.",
    )
    parser.add_argument(
        "--num-samples",
        type=int,
        default=10,
        help="Number of samples to evaluate.",
    )
    parser.add_argument(
        "--use-fp8",
        action="store_true",
        help="Whether to use FP8 for evaluation.",
    )
    args = parser.parse_args()

    # Load and run evaluation
    model, tokenizer = load_model_and_tokenizer(
        args.checkpoint, sha=args.sha, use_fp8=args.use_fp8
    )
    dataset = load_dataset(args.dataset_path)
    results = simple_eval(dataset, model, tokenizer, num_samples=args.num_samples)
    print(f"\nFinal results: {results}")
