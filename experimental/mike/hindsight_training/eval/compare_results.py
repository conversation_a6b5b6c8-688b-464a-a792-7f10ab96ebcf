#!/usr/bin/env python3
"""
Script to compare two JSONL evaluation result files and highlight differences.
Each JSONL file should contain evaluation results with the structure:
{
    "index": int,
    "prompt": str,
    "predicted": list[int],
    "target": str,
    "is_exact_match": bool
}
"""

import json
import argparse
from pathlib import Path
from colorama import Fore, Style, init
from typing import Dict, List, Any
from base.tokenizers import create_tokenizer_by_name
from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from typing import cast

# Initialize colorama for cross-platform colored output
init(autoreset=True)


def load_jsonl(file_path: str) -> Dict[int, Dict[str, Any]]:
    """Load JSONL file and return a dictionary indexed by the 'index' field."""
    results = {}
    with open(file_path, "r") as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                if "index" not in data:
                    print(f"Warning: Line {line_num} missing 'index' field")
                    continue
                results[data["index"]] = data
            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}")
                continue
    return results


def print_simple_diff(text1: str, text2: str, label1: str = "Old", label2: str = "New"):
    """Print a simple comparison between two texts."""
    if text1 == text2:
        return

    print(f"\n{Fore.YELLOW}Prediction difference:{Style.RESET_ALL}")
    print(f"{Fore.RED}{label1}:{Style.RESET_ALL}")
    print(f"{text1}")
    print(f"{Fore.GREEN}{label2}:{Style.RESET_ALL}")
    print(f"{text2}")


def print_example(
    data: Dict[str, Any],
    title: str,
    tokenizer: Qwen25CoderTokenizer,
    color: str = Fore.WHITE,
):
    """Print a single example with formatting."""
    print(f"\n{color}{'='*60}")
    print(f"{title}")
    print(f"Index: {data['index']}")
    print(f"{'='*60}{Style.RESET_ALL}")

    print(f"\n{Fore.YELLOW}Prompt:{Style.RESET_ALL}")
    prompt = data["prompt"]
    if len(prompt) > 200:
        print(f"{prompt[:200]}...")
    else:
        print(prompt)

    print(f"\n{Fore.BLUE}Target:{Style.RESET_ALL}")
    print(f"{data['target']}")

    print(f"\n{Fore.CYAN}Predicted:{Style.RESET_ALL}")
    predicted_tokens = data["predicted"]
    try:
        predicted_text = tokenizer.detokenize(predicted_tokens)
        print(f"{predicted_text}")
    except Exception as e:
        print(f"Error detokenizing: {e}")
        if len(str(predicted_tokens)) > 200:
            print(f"{str(predicted_tokens)[:200]}...")
        else:
            print(predicted_tokens)

    print(f"\n{Fore.MAGENTA}Exact Match:{Style.RESET_ALL} {data['is_exact_match']}")


def compare_results(old_file: str, new_file: str, max_examples: int = 10):
    """Compare two JSONL result files and print differences."""
    print(f"Loading old results from: {old_file}")
    old_results = load_jsonl(old_file)

    print(f"Loading new results from: {new_file}")
    new_results = load_jsonl(new_file)

    print(f"\nLoaded {len(old_results)} old results and {len(new_results)} new results")

    # Create tokenizer for detokenizing predictions
    print("Loading tokenizer...")
    tokenizer: Qwen25CoderTokenizer = cast(
        Qwen25CoderTokenizer, create_tokenizer_by_name("qwen25coder")
    )

    # Find common indices
    common_indices = set(old_results.keys()) & set(new_results.keys())
    print(f"Found {len(common_indices)} common examples")

    if not common_indices:
        print("No common examples found!")
        return

    # Analyze differences
    old_exact_new_not = []  # Exact match in old but not in new
    new_exact_old_not = []  # Exact match in new but not in old
    both_exact = []  # Exact match in both
    neither_exact = []  # Exact match in neither

    # Track regression analysis
    last_token_only_regressions = []  # Regressions where only last token differs
    pause_eos_regressions = []  # Regressions where last token is pause vs eos

    for idx in common_indices:
        old_match = old_results[idx]["is_exact_match"]
        new_match = new_results[idx]["is_exact_match"]

        if old_match and not new_match:
            old_exact_new_not.append(idx)

            # Analyze if this is a last-token-only regression
            old_tokens = old_results[idx]["predicted"]
            new_tokens = new_results[idx]["predicted"]

            if len(old_tokens) > 0 and len(new_tokens) > 0:
                # Check if only last token differs
                if (
                    len(old_tokens) == len(new_tokens)
                    and old_tokens[:-1] == new_tokens[:-1]
                    and old_tokens[-1] != new_tokens[-1]
                ):
                    last_token_only_regressions.append(idx)

                    # Check if it's specifically pause vs eos
                    old_last = old_tokens[-1]
                    new_last = new_tokens[-1]
                    pause_token = tokenizer.special_tokens.pause
                    eos_token = tokenizer.special_tokens.eos

                    if (old_last == pause_token and new_last == eos_token) or (
                        old_last == eos_token and new_last == pause_token
                    ):
                        pause_eos_regressions.append(idx)

        elif new_match and not old_match:
            new_exact_old_not.append(idx)
        elif old_match and new_match:
            both_exact.append(idx)
        else:
            neither_exact.append(idx)

    # Print summary
    print(f"\n{Fore.YELLOW}{'='*80}")
    print("COMPARISON SUMMARY")
    print(f"{'='*80}{Style.RESET_ALL}")

    print(f"\n{Fore.GREEN}Both exact match:{Style.RESET_ALL} {len(both_exact)}")
    print(f"{Fore.RED}Old exact, new not:{Style.RESET_ALL} {len(old_exact_new_not)}")
    print(f"{Fore.BLUE}New exact, old not:{Style.RESET_ALL} {len(new_exact_old_not)}")
    print(f"{Fore.WHITE}Neither exact:{Style.RESET_ALL} {len(neither_exact)}")

    # Calculate rates
    old_exact_rate = (len(both_exact) + len(old_exact_new_not)) / len(common_indices)
    new_exact_rate = (len(both_exact) + len(new_exact_old_not)) / len(common_indices)

    print(
        f"\n{Fore.CYAN}Old exact match rate:{Style.RESET_ALL} {old_exact_rate:.3f} ({len(both_exact) + len(old_exact_new_not)}/{len(common_indices)})"
    )
    print(
        f"{Fore.CYAN}New exact match rate:{Style.RESET_ALL} {new_exact_rate:.3f} ({len(both_exact) + len(new_exact_old_not)}/{len(common_indices)})"
    )

    # Regression analysis
    if old_exact_new_not:
        print(f"\n{Fore.YELLOW}Regression Analysis:{Style.RESET_ALL}")
        print(
            f"  Last token only regressions: {len(last_token_only_regressions)}/{len(old_exact_new_not)} ({len(last_token_only_regressions)/len(old_exact_new_not)*100:.1f}%)"
        )
        print(
            f"  Pause/EOS token regressions: {len(pause_eos_regressions)}/{len(old_exact_new_not)} ({len(pause_eos_regressions)/len(old_exact_new_not)*100:.1f}%)"
        )

        # Show token IDs for reference
        pause_token = tokenizer.special_tokens.pause
        eos_token = tokenizer.special_tokens.eos
        print(f"  (pause token ID: {pause_token}, EOS token ID: {eos_token})")

    # Print examples of regressions (old exact, new not)
    if old_exact_new_not:
        print(f"\n{Fore.RED}{'='*80}")
        print(
            f"REGRESSIONS: Old exact match → New not exact match ({len(old_exact_new_not)} examples)"
        )
        print(f"{'='*80}{Style.RESET_ALL}")

        for i, idx in enumerate(sorted(old_exact_new_not)[:max_examples]):
            print_example(
                old_results[idx],
                f"REGRESSION EXAMPLE {i+1}/{min(len(old_exact_new_not), max_examples)} (Index {idx})",
                tokenizer,
                Fore.RED,
            )

            # Show diff between old and new predictions if they differ
            old_pred_tokens = old_results[idx]["predicted"]
            new_pred_tokens = new_results[idx]["predicted"]
            if old_pred_tokens != new_pred_tokens:
                try:
                    old_pred_text = tokenizer.detokenize(old_pred_tokens)
                    new_pred_text = tokenizer.detokenize(new_pred_tokens)
                    print_simple_diff(
                        old_pred_text, new_pred_text, "Old Prediction", "New Prediction"
                    )
                except Exception:
                    print(
                        f"\n{Fore.YELLOW}Prediction difference (tokens):{Style.RESET_ALL}"
                    )
                    print(f"{Fore.RED}Old:{Style.RESET_ALL} {old_pred_tokens}")
                    print(f"{Fore.GREEN}New:{Style.RESET_ALL} {new_pred_tokens}")

        if len(old_exact_new_not) > max_examples:
            print(
                f"\n{Fore.YELLOW}... and {len(old_exact_new_not) - max_examples} more regression examples{Style.RESET_ALL}"
            )

    # Print examples of improvements (new exact, old not)
    if new_exact_old_not:
        print(f"\n{Fore.GREEN}{'='*80}")
        print(
            f"IMPROVEMENTS: New exact match ← Old not exact match ({len(new_exact_old_not)} examples)"
        )
        print(f"{'='*80}{Style.RESET_ALL}")

        for i, idx in enumerate(sorted(new_exact_old_not)[:max_examples]):
            print_example(
                new_results[idx],
                f"IMPROVEMENT EXAMPLE {i+1}/{min(len(new_exact_old_not), max_examples)} (Index {idx})",
                tokenizer,
                Fore.GREEN,
            )

            # Show diff between old and new predictions if they differ
            old_pred_tokens = old_results[idx]["predicted"]
            new_pred_tokens = new_results[idx]["predicted"]
            if old_pred_tokens != new_pred_tokens:
                try:
                    old_pred_text = tokenizer.detokenize(old_pred_tokens)
                    new_pred_text = tokenizer.detokenize(new_pred_tokens)
                    print_simple_diff(
                        old_pred_text, new_pred_text, "Old Prediction", "New Prediction"
                    )
                except Exception:
                    print(
                        f"\n{Fore.YELLOW}Prediction difference (tokens):{Style.RESET_ALL}"
                    )
                    print(f"{Fore.RED}Old:{Style.RESET_ALL} {old_pred_tokens}")
                    print(f"{Fore.GREEN}New:{Style.RESET_ALL} {new_pred_tokens}")

        if len(new_exact_old_not) > max_examples:
            print(
                f"\n{Fore.YELLOW}... and {len(new_exact_old_not) - max_examples} more improvement examples{Style.RESET_ALL}"
            )


def main():
    parser = argparse.ArgumentParser(
        description="Compare two JSONL evaluation result files"
    )
    parser.add_argument("old_file", help="Path to the old JSONL results file")
    parser.add_argument("new_file", help="Path to the new JSONL results file")
    parser.add_argument(
        "--max-examples",
        type=int,
        default=10,
        help="Maximum number of examples to show for each category (default: 10)",
    )

    args = parser.parse_args()

    # Check if files exist
    if not Path(args.old_file).exists():
        print(f"Error: Old file '{args.old_file}' does not exist")
        return 1

    if not Path(args.new_file).exists():
        print(f"Error: New file '{args.new_file}' does not exist")
        return 1

    compare_results(args.old_file, args.new_file, args.max_examples)
    return 0


if __name__ == "__main__":
    exit(main())
