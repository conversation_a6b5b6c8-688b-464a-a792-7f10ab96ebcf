system:
  name: elden
  model:
    name: fastforward_qwen25coder_14b
    checkpoint_path: /mnt/efs/augment/checkpoints/mike/completion-v3-3-3-ffw
    checkpoint_sha256: 92a01e5c40dede9d0e149e446fda71d0de232a2f0daf3f39d0ba1945e2290699
    # checkpoint_path: /mnt/efs/augment/checkpoints/qwencompletion/14b_elden_8192_hindsight_ffwd
    # checkpoint_sha256: ed386a451c3ad89655df5bf7e4deb2eb9a6fa4987a75dfa0dbc1f5abc8b54775
    num_gpus: 2
    prompt:
      max_prefix_tokens: 1280
      max_suffix_tokens: 768
      max_signature_tokens: 0
      max_retrieved_chunk_tokens: 0
      preamble: ""
  generation_options:
    temperature: 0
    top_k: 0
    top_p: 0
    max_generated_tokens: 280
  # retriever:
  #   name: null
  #   chunker:
  #     name: line_level
  #     max_lines_per_chunk: 40
  dense_retriever:  # New: Configure as null to disable dense retrieval
    name: null
  signature_retriever:  # New: Configure as null to disable signature retrieval (sets empty signature_chunks)
    name: null
  experimental:
    retriever_top_k: 25
    trim_on_dedent: True
    trim_on_max_lines: null
    remove_suffix: False
    trim_trailing_newline_on_prefix: True
    fim_gen_mode: evaluation

task:
  name: cceval

podspec: 2xH100.yaml

determined:
  name: cceval-mike-qwen2_5-14b-v3-2
  workspace: Dev
  project: mike-completion-hindsight
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
