# Hindsight evaluation configuration aligned with production completion host
# Based on qwelden_v3 configuration which uses 8192 token context
# Updated to use latest retriever models: smart ethanol + methanol
#
# Key production settings matched:
# - Context length: 8192 tokens (not 6048)
# - Input fraction: 1/4 (2048 tokens for prefix+suffix)
# - Prefix fraction: 3/4 of input (1536 tokens)
# - Retrieval budget: 6144 tokens split between retrievers
# - Tokenizer: qwen25coder for Qwen models
# - Dense retriever: smart ethanol (stareth_2000s_128docactual_smartnoheader)
# - Signature retriever: methanol (methanol_0416.4_1250)
#
# Note: Production features like extra_stop_tokens and skip_token_str are handled
# by the completion host handler layer and not needed for evaluation

system:
  name: prod_elden
  model:
    name: fastforward_qwen25coder_14b
    checkpoint_path: /mnt/efs/augment/checkpoints/mike/completion-v3-3-3-ffw
    checkpoint_sha256: 92a01e5c40dede9d0e149e446fda71d0de232a2f0daf3f39d0ba1945e2290699
    num_gpus: 2

  # Match production qwelden_v3 tokenizer
  tokenizer: qwen25coder

  # Elden system configuration (required for ProdEldenSystem)
  config:
    line_chunk_retriever_top_k: 30
    sig_chunk_retriever_top_k: 40
    fim_gen_mode: evaluation

  # Elden formatter configuration
  formatter_config:
    name: ender
    apportionment_config:
      max_content_len: 8192  # Production qwelden_v3 uses 8192
      input_fraction: 0.25  # 4/16 = 1/4 for input (2048 tokens)
      prefix_fraction: 0.75  # 3/4 of input for prefix (1536 tokens)
      max_path_tokens: 50
      per_retriever_max_tokens:
        # Production allocates: dense_signature: 1024, recency: 1024, diff: 2048
        # Matching production exactly
        dense_retriever: 2048
        dense_signature: 1024
        diff_retriever: 2048
        recency_retriever: 1024
    prompt_formatter_config:
      component_order:
        - path
        - prefix
        - retrieval
        - signature
        - diff
        - nearby_prefix
        - suffix
      signature_chunk_origin: dense_signature
      filter_visible_chunks_by_content: true
      stateless_caching_config:
        nearby_prefix_token_len: 512
        quantize_token_len: 64
        quantize_char_len: 250

  # Dense retriever - line-level code chunks (updated to smart ethanol)
  dense_retriever:
    scorer:
      name: dense_scorer_v2_fbwd
      checkpoint_path: /mnt/efs/augment/checkpoints/michiel/retriever/ethanol/stareth_2000s_128docactual_smartnoheader
      tokenizer_name: starcoder
    chunker:
      name: smart_line_level
      max_chunk_chars: 768
      max_headers: 3
    query_formatter:
      name: base:ethanol6.16.1-query-embedding
      max_tokens: 1023
      tokenizer: starcoder
    document_formatter:
      name: base:ethanol6-embedding-with-path-key
      max_tokens: 999
      tokenizer: starcoder

  # Signature retriever - function/class signatures (updated to methanol)
  signature_retriever:
    scorer:
      name: dense_scorer_v2_fbwd_neox
      checkpoint_path: /mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/
    chunker:
      name: signature
    query_formatter:
      name: ethanol6_query
      max_tokens: 1023
      add_path: true
      add_suffix: true
      max_lines: -1
      tokenizer_name: StarCoderTokenizer
    document_formatter:
      name: simple_document
      max_tokens: 999
      add_path: false
      tokenizer_name: StarCoderTokenizer

  # Diff retriever - for edit events (matches production)
  diff_retriever:
    name: diff_retriever

  generation_options:
    max_generated_tokens: 280
    temperature: 0.0
    top_k: 0
    top_p: 0

task:
  name: hindsight
  dataset: 2025-04-12-5d-v1.6
  tenant_name: dogfood-shard
  blob_limit: 10000
  limit: 1000
  service_account_file: /mnt/augment/secrets/cw-ri-importer/cw-ri-importer.json

# Hardware - 2 GPUs for model parallelism (matches num_gpus: 2)
podspec: 2xH100-gcp.yaml
determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: hindsight_elden_8k_production_aligned
  project: mike-completion-hindsight
  workspace: Dev

augment:
  dai_gcp_service_accounts:
    - secret: aug-prod-cw-ri-importer  # pragma: allowlist secret
      mountpoint: /mnt/augment/secrets/cw-ri-importer
