{"cells": [{"cell_type": "code", "execution_count": null, "id": "ff24e761", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "79db8891", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "from experimental.pranay.hindsight_training.pause_behavior import (\n", "    load_hindsight_data,\n", "    filter_datums,\n", "    ground_truth_starts_with_generated_text,\n", "    print_datum_info,\n", "    mark_pause_spans,\n", "    get_new_prefix,\n", ")\n", "\n", "from research.static_analysis.parsing import GlobalTsParser\n", "from base.static_analysis.parsing import show_ts_node\n", "from base.static_analysis.common import guess_lang_from_fp\n", "\n", "from typing import List\n", "from pathlib import Path\n", "from colorama import Fore, Style"]}, {"cell_type": "code", "execution_count": null, "id": "1a4b4dca", "metadata": {}, "outputs": [], "source": ["V1_1_fp = \"/mnt/efs/augment/user/jeff/hindsight/2025-01-15-14d-v1.4/dogfood-shard/data.jsonl.zst\"\n", "hindsight_datum: List[HindsightCompletionDatum] = load_hindsight_data(\n", "    V1_1_fp, model_filter=\"qweldenv1-1-14b\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e3d9a87e", "metadata": {}, "outputs": [], "source": ["# We have a lot of unknown languages that we don't have tree-sitter support for\n", "# Note that tree-sitter-language-pack provides a lot more languages, including svelte.\n", "# Should be minimal effort to include more and see what are languages we are missing in Vanguard.\n", "from collections import Counter\n", "\n", "unknown_lang_count = Counter()\n", "\n", "hindsight_datum_filtered = []\n", "for datum in hindsight_datum:\n", "    path = datum.completion.request.path\n", "    if guess_lang_from_fp(path):\n", "        hindsight_datum_filtered.append(datum)\n", "    else:\n", "        unknown_lang_count[Path(path).suffix.lower()] += 1\n", "\n", "hindsight_datum = hindsight_datum_filtered\n", "print(unknown_lang_count)"]}, {"cell_type": "code", "execution_count": null, "id": "44881047", "metadata": {}, "outputs": [], "source": ["interest_datums: List[HindsightCompletionDatum] = filter_datums(\n", "    hindsight_datum, ground_truth_starts_with_generated_text\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "a60049cb", "metadata": {}, "outputs": [], "source": ["from research.data.rag.hindsight_completion_pauser import HindsightCompletionPauser\n", "from tqdm import tqdm\n", "\n", "pauser = HindsightCompletionPauser(max_first_span_size=120)\n", "\n", "unsupported_lang_count = 0\n", "error_count = 0\n", "no_pause_count = 0\n", "same_len_count = 0\n", "old_lengths = []\n", "new_lengths = []\n", "\n", "for idx, datum in enumerate(interest_datums):\n", "    # print_datum_info(datum, idx)\n", "    pause_result = pauser.compute_pause_spans(datum)\n", "    if pause_result.unsupported_language:\n", "        unsupported_lang_count += 1\n", "        continue\n", "    elif pause_result.has_parsing_errors_in_middle:\n", "        error_count += 1\n", "        continue\n", "    elif not pause_result.pause_spans:\n", "        no_pause_count += 1\n", "        print(f\"{Fore.RED}No pause spans found{Style.RESET_ALL}\")\n", "        continue\n", "    original_pause_len = len(datum.completion.response.text)\n", "    new_pause_len = pause_result.pause_spans[0].stop - pause_result.pause_spans[0].start\n", "    full_content = (\n", "        datum.completion.request.prefix\n", "        + datum.ground_truth\n", "        + datum.completion.request.suffix\n", "    )\n", "    new_pause_span = full_content[\n", "        pause_result.pause_spans[0].start : pause_result.pause_spans[0].stop\n", "    ]\n", "    if original_pause_len == len(new_pause_span):\n", "        same_len_count += 1\n", "    elif abs(original_pause_len - new_pause_len) > 20:\n", "        print(\n", "            f\"Idx: {idx} request ID: {datum.completion.request_id} {Fore.RED}Length difference: {original_pause_len} -> {new_pause_len} ({new_pause_len - original_pause_len:+d}){Style.RESET_ALL}\"\n", "        )\n", "        print(f\"{Fore.YELLOW}Old v1 Completion:{Style.RESET_ALL}\")\n", "        print(\n", "            f\"{''.join(datum.completion.request.prefix.splitlines(keepends=True)[-5:])}{Fore.RED}{datum.completion.response.text}{Style.RESET_ALL}{''.join(datum.completion.request.suffix.splitlines(keepends=True)[:5])}\"\n", "        )\n", "        print(f\"{Fore.GREEN}New pause span:{Style.RESET_ALL}\")\n", "        # print(f\"{datum.completion.request.prefix}\\n{Fore.GREEN}{new_pause_span}{Style.RESET_ALL}\\n{datum.completion.request.suffix}\")\n", "        print(\n", "            f\"{''.join(datum.completion.request.prefix.splitlines(keepends=True)[-5:])}{Fore.GREEN}{new_pause_span}{Style.RESET_ALL}{''.join(datum.completion.request.suffix.splitlines(keepends=True)[:5])}\"\n", "        )\n", "    old_lengths.append(original_pause_len)\n", "    new_lengths.append(new_pause_len)\n", "    # print(pause_result)\n", "print(\n", "    f\"{unsupported_lang_count=}, {error_count=}, {no_pause_count=}, {len(interest_datums)=}\"\n", ")\n", "print(\n", "    f\"{same_len_count=} avg old length: {sum(old_lengths)/len(old_lengths):.2f}, avg new length: {sum(new_lengths)/len(new_lengths):.2f}, RMSE: {( sum( [(a - b) ** 2 for a, b in zip(old_lengths, new_lengths) ]) / len(old_lengths))**0.5}\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "5279aeed", "metadata": {}, "outputs": [], "source": ["from experimental.mike.hindsight_completion.evaluate_pauser import process_row\n", "import polars as pl\n", "import logging\n", "\n", "parquet_path = \"/mnt/efs/augment/user/pranay/hindsight/datasets/vanguard_permissive_hindsight_2024-11-01_2025-01-14/i0-vanguard0/target\"\n", "\n", "df = pl.scan_parquet(parquet_path).collect()  # Collect immediately\n", "total_rows = len(df)\n", "print(f\"Loaded {total_rows} rows\")\n", "\n", "# Convert to list of row dictionaries for multiprocessing\n", "rows = list(df.iter_rows(named=True))\n", "\n", "empty_datum = []\n", "error_parsing_datum = []\n", "for row in rows[:2000]:\n", "    result = process_row(row)\n", "    if \"error\" in result and result[\"error\"] == \"no_pause_spans\":\n", "        empty_datum.append(row)\n", "    elif \"error\" in result:\n", "        error_parsing_datum.append(row)\n", "    else:\n", "        pass"]}, {"cell_type": "code", "execution_count": null, "id": "e6058930", "metadata": {}, "outputs": [], "source": ["from experimental.mike.hindsight_completion.evaluate_pauser import pretty_print_datum\n", "from research.data.rag.hindsight_completion_pauser import HindsightCompletionPauser\n", "import pickle\n", "import datetime\n", "from base.datasets.hindsight_completion import HindsightCompletionDatum\n", "from base.datasets.completion import (\n", "    CompletionDatum,\n", "    CompletionRequest,\n", "    CompletionResponse,\n", ")\n", "\n", "\n", "def stat_for_row(row, pauser):\n", "    ground_truth = row[\"original_ground_truth\"].rstrip(\"<|endoftext|>\")\n", "    ground_truth = ground_truth.rstrip(\"<|pause|>\")\n", "    datum = HindsightCompletionDatum(\n", "        ground_truth=ground_truth,\n", "        completion=CompletionDatum(\n", "            request_id=row[\"request_id\"],\n", "            user_id=\"test_user_id\",\n", "            request=CompletionRequest(\n", "                prefix=row[\"prefix\"],\n", "                suffix=row[\"suffix\"],\n", "                path=row[\"file_path\"],\n", "                blob_names=[],\n", "                output_len=0,\n", "                timestamp=datetime.datetime.now(),\n", "            ),\n", "            response=CompletionResponse(\n", "                text=\"dummy completion text\",\n", "                model=\"dummy-model\",\n", "                skipped_suffix=\"\",\n", "                suffix_replacement_text=\"\",\n", "                unknown_blob_names=[],\n", "                retrieved_chunks=[],\n", "                timestamp=datetime.datetime.now(),\n", "                tokens=[\"dummy\", \"tokens\"],\n", "                token_log_probs=[-0.5, -0.3],\n", "                prompt_tokens=[\"prompt\", \"tokens\"],\n", "                prompt_token_ids=[1, 2],\n", "                token_ids=[3, 4],\n", "            ),\n", "        ),\n", "    )\n", "    pauser_result = pauser.compute_pause_spans(datum)\n", "    return pauser_result\n", "\n", "\n", "pauser = HindsightCompletionPauser()\n", "print(f\"empty: {len(empty_datum)}, error: {len(error_parsing_datum)}\")\n", "for i, row in enumerate(empty_datum[:10]):\n", "    print(f\"Empty datum {i}, ground truth: {row['original_ground_truth']}\")\n", "    pretty_print_datum(row[\"prefix\"], row[\"suffix\"], \"\\n\")\n", "\n", "result = stat_for_row(empty_datum[0], pauser)\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "id": "d5ad6f2a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}