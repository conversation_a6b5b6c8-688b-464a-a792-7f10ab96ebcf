{"cells": [{"cell_type": "code", "execution_count": null, "id": "3999bc43", "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "Fast version - Count file types without full datum conversion.\n", "\"\"\"\n", "\n", "from pathlib import Path\n", "from collections import Counter\n", "from tqdm import tqdm\n", "import importlib\n", "\n", "from research.eval.harness.utils import read_jsonl_zst\n", "from base.static_analysis.common import guess_lang_from_fp\n", "\n", "# Reload the module to pick up changes\n", "import base.static_analysis.common\n", "\n", "importlib.reload(base.static_analysis.common)\n", "\n", "# Date ranges to process\n", "date_ranges = [\n", "    \"2024-11-01_2024-11-14\",\n", "    \"2024-11-15_2024-11-30\",\n", "    \"2024-12-01_2024-12-14\",\n", "    \"2024-12-15_2024-12-31\",\n", "    \"2025-01-01_2025-01-14\",\n", "    \"2025-02-15_2025-02-28\",\n", "]\n", "\n", "# Base path\n", "base_path = \"/mnt/efs/augment/user/pranay/hindsight\"\n", "\n", "# Counters\n", "known_lang_count = Counter()\n", "unknown_lang_count = Counter()\n", "no_extension_count = Counter()\n", "total_files = 0\n", "\n", "# Process each date range\n", "for date_range in date_ranges:\n", "    file_path = Path(base_path) / date_range / \"i0-vanguard0\" / \"data.jsonl.zst\"\n", "\n", "    if not file_path.exists():\n", "        print(f\"Skipping {file_path} - file not found\")\n", "        continue\n", "\n", "    print(f\"\\nProcessing {file_path}\")\n", "\n", "    # Read the data\n", "    try:\n", "        data = read_jsonl_zst(file_path)\n", "        print(f\"  Loaded {len(data)} entries\")\n", "\n", "        # Count languages without full conversion\n", "        for datum_dict in tqdm(data, desc=f\"  Processing {date_range}\"):\n", "            try:\n", "                # Just extract the path directly from the dict\n", "                path = datum_dict[\"completion\"][\"request\"][\"path\"]\n", "                lang = guess_lang_from_fp(path)\n", "\n", "                if lang:\n", "                    known_lang_count[lang] += 1\n", "                else:\n", "                    # Extract file extension for unknown types\n", "                    suffix = Path(path).suffix.lower()\n", "                    if suffix:\n", "                        unknown_lang_count[suffix] += 1\n", "                    else:\n", "                        file_name = Path(path).name\n", "                        unknown_lang_count[\"no_extension\"] += 1\n", "                        no_extension_count[file_name] += 1\n", "\n", "                total_files += 1\n", "\n", "            except Exception as e:\n", "                print(f\"  Error processing datum: {e}\")\n", "                continue\n", "\n", "    except Exception as e:\n", "        print(f\"  Error reading file: {e}\")\n", "        continue\n", "\n", "# Print results (same as above)\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"FILE TYPE STATISTICS\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\nTotal files processed: {total_files:,}\")\n", "\n", "print(\"\\nKNOWN LANGUAGES:\")\n", "for lang, count in known_lang_count.most_common():\n", "    percentage = (count / total_files * 100) if total_files > 0 else 0\n", "    print(f\"  {lang:15s}: {count:8,d} ({percentage:5.2f}%)\")\n", "\n", "print(\"\\nUNKNOWN FILE TYPES:\")\n", "for ext, count in unknown_lang_count.most_common():\n", "    percentage = (count / total_files * 100) if total_files > 0 else 0\n", "    print(f\"  {ext:15s}: {count:8,d} ({percentage:5.2f}%)\")\n", "\n", "print(\"\\nNO EXTENSION FILES:\")\n", "for path, count in no_extension_count.most_common():\n", "    if count < 10:\n", "        break\n", "    percentage = (count / total_files * 100) if total_files > 0 else 0\n", "    print(f\"  {path:15s}: {count:8,d} ({percentage:5.2f}%)\")\n", "\n", "# Summary\n", "known_total = sum(known_lang_count.values())\n", "unknown_total = sum(unknown_lang_count.values())\n", "print(\"\\nSUMMARY:\")\n", "print(f\"  Known languages  : {known_total:,d} ({known_total/total_files*100:.2f}%)\")\n", "print(f\"  Unknown types    : {unknown_total:,d} ({unknown_total/total_files*100:.2f}%)\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}