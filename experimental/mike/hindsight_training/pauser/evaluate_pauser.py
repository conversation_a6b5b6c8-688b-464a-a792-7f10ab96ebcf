#!/usr/bin/env python3
"""Evaluate hindsight completion pauser performance with detailed statistics."""

import argparse
import datetime
import logging
import pickle
import random
from dataclasses import dataclass
from multiprocessing import Pool
from pathlib import Path
from typing import Dict, List, Any, Optional

import polars as pl
from colorama import Fore, Style
from tqdm import tqdm

from base.datasets.completion import (
    CompletionDatum,
    CompletionRequest,
    CompletionResponse,
)
from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.ranges.range_types import Char<PERSON><PERSON><PERSON>
from base.static_analysis.common import guess_lang_from_fp
from research.data.rag.hindsight_completion_pauser import HindsightCompletionPauser

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


@dataclass
class Config:
    """Configuration for the evaluation script."""

    num_samples: int = 3000
    num_processes: int = 48
    batch_size: int = 100
    sampling_prob: float = 0.05


@dataclass
class Stats:
    """Statistics for a single row evaluation."""

    lang: str
    ground_truth_length: int
    original_pause_length: int
    pauser_length: int
    has_parsing_errors_in_middle: bool
    unsupported_language: bool
    same_prefix: bool
    spans_equal_ground_truth: bool
    required_prefix_skipping: bool
    num_prefix_lines_skipped: int
    num_prefix_chars_skipped: int
    original_prefix_lines: int
    original_prefix_chars: int
    debug: Optional[Dict[str, Any]] = None


def pretty_print_datum(prefix: str, suffix: str, completion_text: str):
    """Print colored completion context."""
    lines_before = "".join(prefix.splitlines(keepends=True)[-5:])
    lines_after = "".join(suffix.splitlines(keepends=True)[:5])
    print(
        f"{Fore.YELLOW}{lines_before}{Fore.RED}{completion_text}{Fore.GREEN}{lines_after}{Style.RESET_ALL}"
    )


def pretty_print_pause_spans(
    file_path: str,
    prefix: str,
    suffix: str,
    ground_truth: str,
    pause_spans: List[CharRange],
):
    """Print colored pause spans visualization."""
    print(
        f"====={Fore.YELLOW}{file_path} spans: {Fore.GREEN}{pause_spans}{Style.RESET_ALL}====\n"
    )
    print(f"{''.join(prefix.splitlines(keepends=True)[-5:])}", end="")
    for span in pause_spans:
        span_text = ground_truth[span.start : span.stop]
        print(
            f"{Fore.RED}<PAUSE_START>{Fore.GREEN}{span_text}{Fore.BLUE}<PAUSE_END>{Style.RESET_ALL}",
            end="",
        )
    print(f"{''.join(suffix.splitlines(keepends=True)[:5])}")


def create_completion_datum(row: Dict[str, Any]) -> HindsightCompletionDatum:
    """Create HindsightCompletionDatum from row data."""
    ground_truth = row["original_ground_truth"]
    if ground_truth.endswith("<|endoftext|>"):
        ground_truth = ground_truth[: -len("<|endoftext|>")]
    if ground_truth.endswith("<|pause|>"):
        ground_truth = ground_truth[: -len("<|pause|>")]
    return HindsightCompletionDatum(
        ground_truth=ground_truth,
        completion=CompletionDatum(
            request_id=row["request_id"],
            user_id="test_user_id",
            request=CompletionRequest(
                prefix=row["prefix"],
                suffix=row["suffix"],
                path=row["file_path"],
                blob_names=[],
                output_len=0,
                timestamp=datetime.datetime.now(),
            ),
            response=CompletionResponse(
                text="dummy completion text",
                model="dummy-model",
                skipped_suffix="",
                suffix_replacement_text="",
                unknown_blob_names=[],
                retrieved_chunks=[],
                timestamp=datetime.datetime.now(),
                tokens=["dummy", "tokens"],
                token_log_probs=[-0.5, -0.3],
                prompt_tokens=["prompt", "tokens"],
                prompt_token_ids=[1, 2],
                token_ids=[3, 4],
            ),
        ),
    )


def analyze_row(
    row: Dict[str, Any], pauser: HindsightCompletionPauser, config: Config
) -> Dict[str, Any]:
    """Analyze a single row and return statistics."""
    middle_spans = pickle.loads(row["middle_spans"])
    original_pause_length = len(middle_spans[0].content.code)

    datum = create_completion_datum(row)
    ground_truth = datum.ground_truth

    # Check for empty ground truth
    if len(ground_truth) == 0:
        return {"error": "empty_ground_truth"}

    pauser_result = pauser.compute_pause_spans(datum)

    if not pauser_result.pause_spans:
        if random.random() < config.sampling_prob:
            pretty_print_datum(row["prefix"], row["suffix"], ground_truth)
        return {"error": "no_pause_spans"}

    if random.random() < config.sampling_prob:
        pretty_print_pause_spans(
            row["file_path"],
            row["prefix"],
            row["suffix"],
            ground_truth,
            pauser_result.pause_spans,
        )

    # Calculate metrics
    lang = guess_lang_from_fp(row["file_path"])
    original_pause_span = ground_truth[:original_pause_length]
    pauser_pause_span = ground_truth[: pauser_result.pause_spans[0].stop]
    same_prefix = original_pause_span == pauser_pause_span

    # Count original prefix stats
    original_prefix_lines = len(row["prefix"].splitlines())
    original_prefix_chars = len(row["prefix"])

    # Check if concatenation of all pause spans equals ground truth
    concatenated_spans = "".join(
        ground_truth[span.start : span.stop] for span in pauser_result.pause_spans
    )
    spans_equal_ground_truth = concatenated_spans == ground_truth
    if not spans_equal_ground_truth:
        print("Spans do not equal ground truth!")
        pretty_print_pause_spans(
            row["file_path"],
            row["prefix"],
            row["suffix"],
            ground_truth,
            pauser_result.pause_spans,
        )

    return {
        "lang": lang,
        "ground_truth_length": len(ground_truth),
        "original_pause_length": original_pause_length,
        "pauser_length": pauser_result.pause_spans[0].stop,
        "has_parsing_errors_in_middle": pauser_result.has_parsing_errors_in_middle,
        "unsupported_language": pauser_result.fail_to_use_parser,
        "same_prefix": same_prefix,
        "spans_equal_ground_truth": spans_equal_ground_truth,
        "required_prefix_skipping": pauser_result.debug_info.required_prefix_skipping
        if pauser_result.debug_info
        else False,
        "num_prefix_lines_skipped": pauser_result.debug_info.num_prefix_lines_skipped
        if pauser_result.debug_info
        else 0,
        "num_prefix_chars_skipped": pauser_result.debug_info.num_prefix_chars_skipped
        if pauser_result.debug_info
        else 0,
        "original_prefix_lines": original_prefix_lines,
        "original_prefix_chars": original_prefix_chars,
        "debug": {
            "prefix": row["prefix"],
            "suffix": row["suffix"],
            "ground_truth": ground_truth,
            "original_pause_span": original_pause_span[:100],
            "pauser_pause_span": pauser_pause_span[:100],
            "has_parsing_errors_in_middle": pauser_result.has_parsing_errors_in_middle,
            "unsupported_language": pauser_result.fail_to_use_parser,
            "file_path": row["file_path"],
        }
        if pauser_result.has_parsing_errors_in_middle
        or pauser_result.fail_to_use_parser
        else None,
    }


def process_row_wrapper(args):
    """Wrapper for multiprocessing."""
    row, config = args
    pauser = HindsightCompletionPauser()
    return analyze_row(row, pauser, config)


def calculate_averages(results: List[Dict[str, Any]], key: str) -> float:
    """Calculate average for a numeric key from results."""
    return sum(r[key] for r in results) / len(results)


def calculate_rate(results: List[Dict[str, Any]], condition_key: str) -> float:
    """Calculate rate for a boolean condition from results."""
    return sum(1 for r in results if r[condition_key]) / len(results)


def print_processing_breakdown(results: List[Any]):
    """Print detailed processing breakdown."""
    total = len(results)
    successful = [r for r in results if isinstance(r, dict) and "error" not in r]
    error_results = [r for r in results if isinstance(r, dict) and "error" in r]
    none_results = [r for r in results if r is None]

    parsing_failures = [
        r for r in error_results if r["error"].startswith("parsing_failed")
    ]
    no_pause_spans = [r for r in error_results if r["error"] == "no_pause_spans"]
    empty_ground_truth = [
        r for r in error_results if r["error"] == "empty_ground_truth"
    ]
    other_errors = [
        r
        for r in error_results
        if not r["error"].startswith("parsing_failed")
        and r["error"] != "no_pause_spans"
        and r["error"] != "empty_ground_truth"
    ]

    print("\n=== PROCESSING BREAKDOWN ===")
    print(f"Total rows processed: {total}")
    print(
        f"✅ Successful processing: {len(successful)} ({len(successful)/total*100:.1f}%)"
    )
    print(
        f"❌ Parsing failures (exceptions thrown): {len(parsing_failures)} ({len(parsing_failures)/total*100:.1f}%)"
    )
    print(
        f"📝 Empty ground truth: {len(empty_ground_truth)} ({len(empty_ground_truth)/total*100:.1f}%)"
    )
    print(
        f"⚠️  No pause spans found: {len(no_pause_spans)} ({len(no_pause_spans)/total*100:.1f}%)"
    )
    print(f"🔍 Other errors: {len(other_errors)} ({len(other_errors)/total*100:.1f}%)")
    print(f"❓ None results: {len(none_results)} ({len(none_results)/total*100:.1f}%)")

    return successful


def print_detailed_breakdown(successful_results: List[Dict[str, Any]]):
    """Print detailed issue breakdown and quality metrics."""
    if not successful_results:
        return

    n = len(successful_results)
    unsupported_rate = calculate_rate(successful_results, "unsupported_language")
    prefix_removal_samples = [
        s for s in successful_results if s["required_prefix_skipping"]
    ]
    prefix_removal_rate = len(prefix_removal_samples) / n
    parsing_errors_rate = calculate_rate(
        successful_results, "has_parsing_errors_in_middle"
    )
    same_prefix_rate = calculate_rate(successful_results, "same_prefix")
    spans_equal_ground_truth_rate = calculate_rate(
        successful_results, "spans_equal_ground_truth"
    )

    print(f"\n=== DETAILED BREAKDOWN (n={n} successful) ===")
    print("📊 ISSUE BREAKDOWN:")
    print(f"  🔤 Unsupported language rate: {unsupported_rate * 100:.1f}%")
    print(f"  🔧 Prefix removal required rate: {prefix_removal_rate * 100:.1f}%")
    print(f"  ⚠️  Final parsing errors rate: {parsing_errors_rate * 100:.1f}%")

    print("\n📈 QUALITY METRICS:")
    print(f"  ✅ Same prefix rate: {same_prefix_rate * 100:.1f}%")
    print(
        f"  🔗 Spans equal ground truth rate: {spans_equal_ground_truth_rate * 100:.1f}%"
    )
    print(
        f"  📏 Average ground_truth_length: {calculate_averages(successful_results, 'ground_truth_length'):.2f}"
    )
    print(
        f"  📏 Average original_pause_length: {calculate_averages(successful_results, 'original_pause_length'):.2f}"
    )
    print(
        f"  📏 Average pauser_length: {calculate_averages(successful_results, 'pauser_length'):.2f}"
    )

    return prefix_removal_samples, parsing_errors_rate, prefix_removal_rate


def print_prefix_removal_breakdown(
    prefix_removal_samples: List[Dict[str, Any]],
    parsing_errors_rate: float,
    prefix_removal_rate: float,
):
    """Print prefix removal detailed breakdown."""
    if not prefix_removal_samples:
        return

    resolved = [
        s for s in prefix_removal_samples if not s["has_parsing_errors_in_middle"]
    ]
    unresolved = [
        s for s in prefix_removal_samples if s["has_parsing_errors_in_middle"]
    ]

    total_with_skipping = len(prefix_removal_samples)
    resolved_rate = len(resolved) / total_with_skipping * 100
    unresolved_rate = len(unresolved) / total_with_skipping * 100

    print("\n=== PREFIX REMOVAL DETAILED BREAKDOWN ===")
    print(f"🔧 Of {total_with_skipping} samples requiring prefix removal:")
    print(f"  ✅ Fixed by prefix removal: {len(resolved)} ({resolved_rate:.1f}%)")
    print(f"  ❌ Still have parsing errors: {len(unresolved)} ({unresolved_rate:.1f}%)")
    print(
        f"\n💡 Validation: Final parsing error rate ({parsing_errors_rate * 100:.1f}%) ≈ "
        f"Prefix removal rate ({prefix_removal_rate * 100:.1f}%) × Unfixed rate ({unresolved_rate:.1f}%) = "
        f"{prefix_removal_rate * unresolved_rate * 100:.1f}%"
    )


def print_per_language_stats(successful_results: List[Dict[str, Any]]):
    """Print per-language statistics."""
    print("\n=== PER-LANGUAGE STATS ===")

    # Group by language
    lang_groups = {}
    for result in successful_results:
        lang = result["lang"]
        if lang not in lang_groups:
            lang_groups[lang] = []
        lang_groups[lang].append(result)

    # Print stats for each language
    for lang, lang_stats in sorted(
        lang_groups.items(), key=lambda x: len(x[1]), reverse=True
    ):
        if len(lang_stats) < 10:  # Skip languages with too few samples
            continue

        same_prefix_rate = calculate_rate(lang_stats, "same_prefix")
        parsing_errors_rate = calculate_rate(lang_stats, "has_parsing_errors_in_middle")
        unsupported_rate = calculate_rate(lang_stats, "unsupported_language")

        print(f"\n{lang}: {len(lang_stats)} samples")
        print(
            f"  ground_truth_length: {calculate_averages(lang_stats, 'ground_truth_length'):.2f}"
        )
        print(
            f"  original_pause_length: {calculate_averages(lang_stats, 'original_pause_length'):.2f}"
        )
        print(f"  pauser_length: {calculate_averages(lang_stats, 'pauser_length'):.2f}")
        print(f"  same_prefix_rate: {same_prefix_rate * 100:.1f}%")
        print(f"  parsing_errors_rate: {parsing_errors_rate * 100:.1f}%")
        print(f"  unsupported_language_rate: {unsupported_rate * 100:.1f}%")


def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description="Evaluate hindsight completion pauser")
    parser.add_argument(
        "--num-samples", type=int, default=10000, help="Number of samples to process"
    )
    parser.add_argument(
        "--num-processes",
        type=int,
        default=48,
        help="Number of processes for multiprocessing",
    )
    parser.add_argument(
        "--sampling-prob",
        type=float,
        default=0.005,
        help="Probability of printing debug examples",
    )
    parser.add_argument(
        "--batch-size", type=int, default=100, help="Batch size for multi-processing"
    )
    args = parser.parse_args()

    config = Config(
        args.num_samples, args.num_processes, args.batch_size, args.sampling_prob
    )

    # Load data
    logging.info("Loading data...")
    df = pl.read_parquet(
        "/mnt/efs/augment/user/pranay/hindsight/datasets/vanguard_permissive_hindsight_2024-11-01_2025-01-14/i0-vanguard0/target"
    )
    df = df.sample(n=config.num_samples, seed=42)

    # Process data
    logging.info(
        f"Processing {len(df)} samples with {config.num_processes} processes..."
    )
    rows_with_config = [(row, config) for row in df.to_dicts()]

    with Pool(config.num_processes) as pool:
        results = list(
            tqdm(
                pool.imap(
                    process_row_wrapper, rows_with_config, chunksize=config.batch_size
                ),
                total=len(rows_with_config),
            )
        )

    # Print results
    successful_results = print_processing_breakdown(results)
    if successful_results:
        breakdown_result = print_detailed_breakdown(successful_results)
        if breakdown_result:
            prefix_removal_samples, parsing_errors_rate, prefix_removal_rate = (
                breakdown_result
            )
            print_prefix_removal_breakdown(
                prefix_removal_samples, parsing_errors_rate, prefix_removal_rate
            )
        print_per_language_stats(successful_results)


if __name__ == "__main__":
    main()
