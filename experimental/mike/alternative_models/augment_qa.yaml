augment:
    gpu_count: 1
determined:
    metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
    name: gpt5_reasoning_v1_c4_p2
    project: mike
    workspace: Dev
podspec: gpu-small-gcp.yaml
system:
    chat:
        exponential_retry_delay: true
        retry_client_closed_request: true
        retry_count: 25
        retry_internal_server_error: true
        retry_sleep_seconds: 10.0
    client:
        timeout: 300
        url: https://dev-mike.us-central.api.augmentcode.com
    model_name: gpt5-reasoning-16k-v1-c4-p2-chat
    name: remote_chat
task:
    dataset_path: /mnt/efs/augment/data/processed/augment_qa/v3_1
    html_report_output_dir: /mnt/efs/augment/public_html/augment_qa/v3_1
    name: augment_qa
