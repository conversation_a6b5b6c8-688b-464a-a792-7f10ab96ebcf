"""Ray Stage 1: Generate FIM samples from repo parquet dataset.

Compatible inputs:
- Output of experimental/mike/retrieval/0-repo.py (Ray or Spark generated repos parquet directory)
- Legacy Spark sampled repo directories like /mnt/efs/spark-data/shared/repo/2024-0928_10k/

Outputs:
- Parquet shards containing columns: fim_problems (bytes), file_list (array<struct<...>>)
- config.json with run metadata

Processing steps (fused for simplicity):
1. For each repo row, run RobustFIMSampler over file_list to produce batches (<=500 problems each)
2. Filter out FIM problems whose prefix is empty
3. Remove trouble keys from each file in file_list (constants.TROUBLE_KEYS)
4. Serialize list[FimProblem] -> pickle bytes under fim_problems
5. Emit one row per emitted batch

CLI Example:
python experimental/mike/retrieval/1-fim.py \
  --mode local \
  --input /mnt/efs/spark-data/shared/repo/2024-0928_10k \
  --output-root /mnt/efs/spark-data/shared/ethanol/test_run
"""

from __future__ import annotations

import argparse
import datetime as dt
import json
import os
import pickle
from pathlib import Path
from typing import Any, Iterator

import ray
from ray.data import Dataset

from research.data.rag import constants, common as rag_common

# Global (per Ray worker process) sampler cache
_SAMPLER: rag_common.RobustFIMSampler | None = None


def _time() -> str:
    return dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser()
    p.add_argument("--mode", choices=["local", "ray"], default="ray")
    p.add_argument(
        "--input",
        required=True,
        help="Path to repo parquet directory (Ray or Spark generated)",
    )
    p.add_argument(
        "--output-root",
        required=True,
        help="Root output directory (will create run_root/fim)",
    )
    p.add_argument(
        "--run-name",
        default=None,
        help="Run name. If omitted, inferred from input dir basename",
    )
    # FIM sampling parameters
    p.add_argument("--every-n-lines", type=int, default=200)
    p.add_argument("--max-problems-per-file", type=int, default=5)
    p.add_argument("--random-seed", type=int, default=74912)
    # File filter parameters
    p.add_argument("--small-filter-char-threshold", type=int, default=500)
    p.add_argument("--small-downsampled-probability", type=float, default=0.1)
    p.add_argument("--small-downsample-char-threshold", type=int, default=1500)
    p.add_argument(
        "--sample-languages",
        type=str,
        default=",".join(sorted(constants.SAMPLE_LANGUAGES)),
        help="Comma separated language set; default uses constants.SAMPLE_LANGUAGES",
    )
    # Limits for debugging
    p.add_argument(
        "--limit-repos",
        type=int,
        default=None,
        help="Limit number of repo rows processed (pre-FIM)",
    )
    p.add_argument(
        "--limit-fim-rows",
        type=int,
        default=None,
        help="Limit number of output FIM rows (post sampling)",
    )
    return p.parse_args()


def init_ray(mode: str) -> None:
    if mode == "local":
        print(f"[{_time()}] Initializing Ray locally")
        ray.init(ignore_reinit_error=True)
    else:
        print(f"[{_time()}] Connecting to Ray cluster (address auto)")
        ray.init(address="auto", ignore_reinit_error=True)


def infer_run_name(run_name: str | None, input_path: str) -> str:
    if run_name:
        return run_name
    return Path(input_path.rstrip("/").split("/")[-1]).name


def build_sampler(args) -> rag_common.RobustFIMSampler:
    file_filter = rag_common.FileFilter(
        small_filter_char_threshold=args.small_filter_char_threshold,
        small_downsampled_probability=args.small_downsampled_probability,
        small_downsample_char_threshold=args.small_downsample_char_threshold,
        sample_languages=args.sample_languages.split(","),
        only_keep_unit_test_file=False,
    )
    return rag_common.RobustFIMSampler(
        file_filter=file_filter,
        max_problems_per_file=args.max_problems_per_file,
        every_n_lines=args.every_n_lines,
        random_seed=args.random_seed,
    )


def _drop_trouble_keys(file_list: list[dict]) -> list[dict]:
    cleaned = []
    trouble = constants.TROUBLE_KEYS
    for f in file_list:
        # Copy to avoid mutating original row (may be reused in other tasks)
        nf = {k: v for k, v in f.items() if k not in trouble}
        cleaned.append(nf)
    return cleaned


def fim_flat_map(
    row: dict, args_serialized: bytes
) -> Iterator[dict]:  # executed on workers
    global _SAMPLER
    if _SAMPLER is None:
        args = pickle.loads(args_serialized)
        _SAMPLER = build_sampler(args)
    file_list = row.get("file_list")
    if not file_list:
        return
    # Iterate batches from sampler (each batch: {fim_problems: list[FimProblem], file_list: original})
    for batch in _SAMPLER(file_list):
        problems = batch["fim_problems"]
        # Filter out empty prefix problems
        filtered = [p for p in problems if p.prefix]
        if not filtered:
            continue
        cleaned_files = _drop_trouble_keys(batch["file_list"])
        yield {
            "fim_problems": pickle.dumps(filtered),
            "file_list": cleaned_files,
        }


def load_input_dataset(path: str) -> Dataset:
    print(f"[{_time()}] Reading repos parquet: {path}")
    ds = ray.data.read_parquet(path)
    print(f"[{_time()}] Schema: {ds.schema()}")
    return ds


def write_output(ds: Dataset, output_dir: str, config: dict[str, Any]):
    print(f"[{_time()}] Writing FIM dataset (Parquet) -> {output_dir}")
    ds.write_parquet(output_dir)
    cfg_path = os.path.join(output_dir, "config.json")
    with open(cfg_path, "w") as f:
        f.write(json.dumps(config, indent=2))
    print(f"[{_time()}] Wrote config: {cfg_path}")


def main():
    args = parse_args()
    run_name = infer_run_name(args.run_name, args.input)
    run_root = os.path.join(args.output_root, run_name)
    output_dir = os.path.join(run_root, "fim")
    os.makedirs(output_dir, exist_ok=True)

    init_ray(args.mode)

    ds = load_input_dataset(args.input)
    total_in = ds.count()
    if args.limit_repos:
        ds = ds.limit(args.limit_repos)
    repos_processed = ds.count()

    # Broadcast serialized args to workers for lazy sampler init
    args_serialized = pickle.dumps(args)
    mapped = ds.flat_map(lambda row: fim_flat_map(row, args_serialized))

    if args.limit_fim_rows:
        mapped = mapped.limit(args.limit_fim_rows)
    fim_rows = mapped.count()

    write_output(
        mapped,
        output_dir,
        config={
            "input": args.input,
            "output_dir": output_dir,
            "run_name": run_name,
            "every_n_lines": args.every_n_lines,
            "max_problems_per_file": args.max_problems_per_file,
            "random_seed": args.random_seed,
            "small_filter_char_threshold": args.small_filter_char_threshold,
            "small_downsampled_probability": args.small_downsampled_probability,
            "small_downsample_char_threshold": args.small_downsample_char_threshold,
            "sample_languages": args.sample_languages.split(","),
            "limit_repos": args.limit_repos,
            "limit_fim_rows": args.limit_fim_rows,
            "stats": {
                "total_repo_rows_in": total_in,
                "repos_processed": repos_processed,
                "fim_problem_rows_out": fim_rows,
            },
            "timestamp": _time(),
        },
    )

    if args.mode == "ray":
        ray.shutdown()
    print(f"[{_time()}] Done.")


if __name__ == "__main__":  # pragma: no cover
    main()
