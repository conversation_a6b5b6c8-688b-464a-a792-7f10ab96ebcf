"""Ray Stage 2: Retrieval augmentation of FIM problems.

Input: <PERSON><PERSON><PERSON> produced by stage 1 (fim) OR Spark fim stage.
  Required columns:
    - fim_problems : bytes (pickle list[FimProblem])
    - file_list    : array<struct<...>> list of file dicts with at least path/lang/content keys

Output:
  Parquet (one row per FIM problem) with columns:
    - prefix, middle_spans (bytes), suffix, suffix_offset,
      middle_char_start, middle_char_end, file_path,
      retrieved_chunks (serialized)
  Plus config.json with stats.

Design:
  We use Ray Dataset map_batches with ActorPoolStrategy via a module-level
  global retriever object (lazy init per worker). This keeps code minimal
  while ensuring heavy model + index state is reused.

CLI Example:
  python experimental/mike/retrieval/2-retrieval.py \\
    --mode local \\
    --input /mnt/efs/spark-data/shared/ethanol/run1/fim \\
    --output-root /mnt/efs/spark-data/shared/ethanol \\
    --run-name run1 \\
    --retriever-config-file experimental/mike/retrieval/configs/dense_retriever_config.json
"""

from __future__ import annotations

import argparse
import copy
import datetime as dt
import json
import os
import pickle
from pathlib import Path
from typing import Any, Iterator

import ray
from ray.data import Dataset
from ray.data import ActorPoolStrategy

from research.data.rag import constants
from research.data.retrieval.scoring import GenerateRetrievedChunksFromFim
from research.eval.harness.factories import create_retriever
from research.retrieval.retrieval_database import RetrievalDatabase
from research.data.retrieval.scoring import serialize_retrieved_chunks

# Global per-worker state
_RETRIEVER_WRAPPER: GenerateRetrievedChunksFromFim | None = None
_RETRIEVER_CFG: dict | None = None
_ARGS = None


def _time() -> str:
    return dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser()
    p.add_argument("--mode", choices=["local", "ray"], default="ray")
    p.add_argument("--input", required=True)
    p.add_argument("--output-root", required=True)
    p.add_argument("--run-name", default=None)
    p.add_argument(
        "--retriever-config-file", required=True, help="JSON retriever config"
    )
    p.add_argument("--num-retrieved-chunks", type=int, default=128)
    p.add_argument(
        "--retrieval-languages",
        type=str,
        default=",".join(sorted(constants.RETRIEVAL_LANGUAGES)),
    )
    p.add_argument("--use-ground-truth", action="store_true")
    # Limits
    p.add_argument("--limit-repos", type=int, default=None)
    p.add_argument("--limit-fim-rows", type=int, default=None)
    # Execution
    p.add_argument("--num-workers", type=int, default=1)
    p.add_argument("--gpus-per-worker", type=int, default=1)
    return p.parse_args()


def infer_run_name(run_name: str | None, input_path: str) -> str:
    if run_name:
        return run_name
    return Path(input_path.rstrip("/").split("/")[-1]).name


def _lazy_init_retriever(args_serialized: bytes):  # executed inside workers
    global _RETRIEVER_WRAPPER, _RETRIEVER_CFG, _ARGS
    if _RETRIEVER_WRAPPER is not None:
        return
    _ARGS = pickle.loads(args_serialized)
    with open(_ARGS.retriever_config_file) as f:
        cfg = json.load(f)
    cfg = copy.deepcopy(cfg)
    # Build retrieval database once
    retrieval_db = create_retriever(cfg)
    assert isinstance(retrieval_db, RetrievalDatabase), type(retrieval_db)
    languages = (
        _ARGS.retrieval_languages.split(",") if _ARGS.retrieval_languages else []
    )
    _RETRIEVER_WRAPPER = GenerateRetrievedChunksFromFim(
        retrieval_database_factories={"retrieved": lambda: retrieval_db},
        retrieval_languages=languages,
        num_retrieved_chunks=_ARGS.num_retrieved_chunks,
        use_ground_truth=_ARGS.use_ground_truth,
    )


def _expand_row(
    row: dict, args_serialized: bytes
) -> Iterator[dict]:  # per FIM repo row
    _lazy_init_retriever(args_serialized)
    fim_bytes = row.get("fim_problems")
    file_list = row.get("file_list")
    if not fim_bytes or not file_list:
        return
    try:
        fim_problems = pickle.loads(fim_bytes)
    except Exception:
        return
    count = 0
    for out in _RETRIEVER_WRAPPER(file_list, fim_problems):  # type: ignore[arg-type]
        # out contains: prefix, middle_spans (pickled), suffix, etc. from scoring.GenerateRetrievedChunksFromFim
        # We standardize key names to match score stage expectations.
        retrieved_chunks_ser = out.get("retrieved_chunks") or out.get(
            "retrieved_chunks".replace("retrieved_", "retrieved_")
        )  # safety
        if retrieved_chunks_ser is None:
            # Build from key-specific retrieved chunks (e.g., 'retrieved_chunks' already present)
            # In ethanol_score Spark pipeline the key produced is 'retrieved_chunks'
            pass
        yield {
            "prefix": out["prefix"],
            "middle_spans": out[
                "middle_spans"
            ],  # already pickled in GenerateRetrievedChunksFromFim
            "suffix": out["suffix"],
            "suffix_offset": out["suffix_offset"],
            "middle_char_start": out["middle_char_start"],
            "middle_char_end": out["middle_char_end"],
            "file_path": out["file_path"],
            "retrieved_chunks": out["retrieved_chunks"],
        }
        count += 1
        if _ARGS.limit_fim_rows and count >= _ARGS.limit_fim_rows:
            break


def main():  # pragma: no cover
    args = parse_args()
    run_name = infer_run_name(args.run_name, args.input)
    run_root = os.path.join(args.output_root, run_name)
    output_dir = os.path.join(run_root, "retrieval")
    os.makedirs(output_dir, exist_ok=True)

    # Ray init
    if args.mode == "local":
        print(f"[{_time()}] Init Ray local")
        ray.init(ignore_reinit_error=True)
    else:
        print(f"[{_time()}] Init Ray cluster auto")
        ray.init(address="auto", ignore_reinit_error=True)

    print(f"[{_time()}] Reading FIM parquet: {args.input}")
    ds: Dataset = ray.data.read_parquet(args.input)
    total_repo_rows = ds.count()
    if args.limit_repos:
        ds = ds.limit(args.limit_repos)
    repo_rows = ds.count()

    args_serialized = pickle.dumps(args)

    # Expand
    expanded = ds.flat_map(
        lambda r: _expand_row(r, args_serialized),
        compute=ActorPoolStrategy(
            size=args.num_workers, max_tasks_in_flight_per_actor=1
        ),
        ray_remote_args={"num_gpus": args.gpus_per_worker, "num_cpus": 1},
    )

    out_count = expanded.count()

    expanded.write_parquet(output_dir)
    cfg = {
        "input": args.input,
        "output_dir": output_dir,
        "run_name": run_name,
        "retriever_config_file": args.retriever_config_file,
        "num_retrieved_chunks": args.num_retrieved_chunks,
        "retrieval_languages": args.retrieval_languages.split(","),
        "use_ground_truth": args.use_ground_truth,
        "limit_repos": args.limit_repos,
        "limit_fim_rows": args.limit_fim_rows,
        "stats": {
            "total_fim_repo_rows_in": total_repo_rows,
            "fim_repo_rows_processed": repo_rows,
            "retrieval_rows_out": out_count,
        },
        "timestamp": _time(),
    }
    with open(os.path.join(output_dir, "config.json"), "w") as f:
        f.write(json.dumps(cfg, indent=2))
    print(f"[{_time()}] Wrote config.json")

    if args.mode == "ray":
        ray.shutdown()
    print(f"[{_time()}] Done retrieval stage.")


if __name__ == "__main__":  # pragma: no cover
    main()
