"""Ray Retrieval Score Stage (Parquet -> <PERSON><PERSON><PERSON> via RayRunner).

Simplified stage:
  1. Read retrieval-stage Parquet input directly (prefix, suffix, middle_spans bytes, etc.)
  2. RayRunner actors (single init per actor) compute perplexity scores per retrieved chunk
  3. Write Parquet outputs (original columns + ppl_scores + ppl_prompt_len)

All JSONL conversion code has been removed; we operate only on Parquet now.
Minimal single code path (local vs ray handled inside RayRunner).


example:
local:


ray:
python research/data/ray/submit_ray.py \
  --ray-address $RAY_ADDRESS \
  -- \
  python experimental/mike/retrieval/3-score.py \
    --mode ray \
    --input gs://gcp-us1-spark-data/user/mike/ethanol/small_test/retrieval/retrieval \
    --output-root gs://gcp-us1-spark-data/user/mike/ethanol/small_test/ \
    --model-config-file /home/<USER>/repos/augment/research/data/retrieval/pipelines/ethanol_score.py \
    --score-batch-size 4 \
    --max-target-tokens 256 \
    --min-num-retrieved-chunks 20 \
    --num-workers 16 \
    --gpus-per-worker 1

"""

from __future__ import annotations

import argparse
import copy
import datetime as dt
import json
import os
import pickle
from dataclasses import dataclass
from pathlib import Path

import pyarrow.parquet as pq
import pyarrow as pa

from dataclasses_json import DataClassJsonMixin

from research.data.ray.ray_utils import AbstractRayActor, RayRunner
from research.data.retrieval.scoring import (
    filter_overlap_chunks_map,
    deserialize_retrieved_chunks,
)
from research.eval.harness.metrics import compute_mean_log_likelihood
from research.core.model_input import ModelInput
from research.core.tokenizers import get_tokenizer
from research.fim import fim_prompt


# FS helper (fsspec everywhere for local/GCS/S3 transparency)
import fsspec


def _fs_mkdir(path: str):
    fs, p = fsspec.core.url_to_fs(path)
    try:
        fs.mkdirs(p, exist_ok=True)
    except FileExistsError:
        pass


# --------------------------------------------------------------------------------------
# Dataclasses (JSON-serializable for RayRunner)
# --------------------------------------------------------------------------------------


@dataclass
class RetrievalScoreInput(DataClassJsonMixin):
    prefix: str
    suffix: str
    middle_spans: bytes  # original pickled middle_spans bytes
    middle_char_start: int
    middle_char_end: int
    file_path: str
    suffix_offset: int
    retrieved_chunks: str  # serialized retrieved chunks (string)


@dataclass
class RetrievalScoreOutput(RetrievalScoreInput):
    ppl_scores: str  # json string {"scores": [...]} (same as Spark ethanol schema)
    ppl_prompt_len: str  # json list of prompt token lengths


# --------------------------------------------------------------------------------------
# Actor
# --------------------------------------------------------------------------------------


class RetrievalScoreActor(AbstractRayActor[RetrievalScoreInput, RetrievalScoreOutput]):
    def __init__(
        self,
        model_config_file: str,
        score_batch_size: int,
        max_target_tokens: int,
        score_signature: bool,
        min_num_retrieved_chunks: int,
    ):
        super().__init__(input_cls=RetrievalScoreInput, output_cls=RetrievalScoreOutput)
        import fsspec
        import time

        t0 = time.time()
        print(f"[RetrievalScoreActor:init] reading model config {model_config_file}")
        with fsspec.open(model_config_file) as f:
            cfg = json.load(f)
        cfg = copy.deepcopy(cfg)
        cfg["kv_cache_batch_size"] = score_batch_size

        from base.tokenizers import list_tokenizers as _list_prod_tokenizers

        if "tokenizer_name" in cfg:
            tn = cfg["tokenizer_name"]
            if tn in _list_prod_tokenizers():
                cfg["override_tokenizer"] = tn
                cfg.pop("tokenizer_name")

        self.cfg = cfg
        self.score_batch_size = score_batch_size
        self.max_target_tokens = max_target_tokens
        self.score_signature = score_signature
        self.min_num_retrieved_chunks = min_num_retrieved_chunks

        from research.eval.harness.factories import create_model as _create_model

        print("[RetrievalScoreActor:init] constructing model object")
        self.model = _create_model(cfg)
        print("[RetrievalScoreActor:init] loading model weights...")
        load_t0 = time.time()
        self.model.load()
        print(
            f"[RetrievalScoreActor:init] model loaded in {time.time()-load_t0:.2f}s (total init {time.time()-t0:.2f}s)"
        )
        self.tokenizer = self.model.prompt_formatter.tokenizer  # type: ignore
        self.special = self.tokenizer.special_tokens  # type: ignore

    def _build_middle_tokens(self, middle_spans_bytes: bytes) -> list[int]:
        import time

        t0 = time.time()
        deserialized_middle_spans = pickle.loads(middle_spans_bytes)
        middle_tokens = fim_prompt._format_middle(
            middle_spans=deserialized_middle_spans,
            tokenize_span=lambda span: self.tokenizer.tokenize_safe(span.code),
            skip_id=self.special.skip,
            pause_id=self.special.pause,
            fim_stop_id=self.special.eos,
        )
        middle_tokens = middle_tokens[: self.max_target_tokens]
        print(
            f"[RetrievalScoreActor:middle] built {len(middle_tokens)} tokens in {time.time()-t0:.2f}s"
        )
        return middle_tokens

    def process(self, row: RetrievalScoreInput) -> list[RetrievalScoreOutput]:  # type: ignore[override]
        import time

        start = time.time()
        print(
            f"[RetrievalScoreActor:process] file={row.file_path} middle_range=({row.middle_char_start},{row.middle_char_end}) retrieved_chunks_str_len={len(row.retrieved_chunks)}"
        )
        t_filter = time.time()
        overlap_filtered = filter_overlap_chunks_map(
            file_path=row.file_path,
            middle_char_start=row.middle_char_start,
            middle_char_end=row.middle_char_end,
            retrieved_chunks=row.retrieved_chunks,
        )
        print(
            f"[RetrievalScoreActor:process] overlap filter took {time.time()-t_filter:.2f}s"
        )
        retrieved_chunks_ser = overlap_filtered["retrieved_chunks"]
        t_deser = time.time()
        chunks = deserialize_retrieved_chunks(retrieved_chunks_ser)
        print(
            f"[RetrievalScoreActor:process] deserialized {len(chunks)} chunks in {time.time()-t_deser:.2f}s"
        )
        if len(chunks) < self.min_num_retrieved_chunks:
            print(
                f"[RetrievalScoreActor:process] too_few_chunks({len(chunks)} < {self.min_num_retrieved_chunks}) skip in {time.time()-start:.2f}s"
            )
            return []

        middle_tokens = self._build_middle_tokens(row.middle_spans)

        scores: list[float] = []
        prompt_token_list: list[list[int]] = []
        batch_inputs: list[ModelInput] = []
        token_prep_start = time.time()

        def flush_batch():
            nonlocal batch_inputs
            if not batch_inputs:
                return
            fwd_t0 = time.time()
            print(
                f"[RetrievalScoreActor:process] forward_pass start batch={len(batch_inputs)}"
            )
            outputs = self.model.forward_pass(batch_inputs)
            print(
                f"[RetrievalScoreActor:process] forward_pass done batch={len(batch_inputs)} took {time.time()-fwd_t0:.2f}s"
            )
            for out in outputs:
                mean_ll = compute_mean_log_likelihood(
                    out.logits, out.label_tokens, out.target_mask
                )
                scores.append(mean_ll)
            batch_inputs = []

        for idx, chunk in enumerate(chunks):
            mi = ModelInput(
                prefix=row.prefix,
                suffix=row.suffix,
                path=row.file_path,
                retrieved_chunks=[] if self.score_signature else [chunk],
                cursor_position=-999999,
                extra={
                    "target_tokens": middle_tokens,
                    "signature_chunks": [chunk] if self.score_signature else [],
                },
            )
            prep_t0 = time.time()
            prompt_tokens = self.model.prompt_formatter.prepare_prompt(mi)[0]
            prep_dt = time.time() - prep_t0
            prompt_token_list.append(prompt_tokens)
            batch_inputs.append(mi)
            if (idx + 1) % 10 == 0:
                print(
                    f"[RetrievalScoreActor:process] prepped {idx+1}/{len(chunks)} prompts (last {prep_dt:.2f}s) elapsed {time.time()-token_prep_start:.2f}s"
                )
            if len(batch_inputs) >= self.score_batch_size:
                flush_batch()
        flush_batch()

        assert len(scores) == len(chunks)
        ppl_scores = json.dumps({"scores": scores})
        ppl_prompt_len = json.dumps([len(p) for p in prompt_token_list])
        print(
            f"[RetrievalScoreActor:process] done chunks={len(chunks)} total_time={time.time()-start:.2f}s"
        )

        return [
            RetrievalScoreOutput(
                prefix=row.prefix,
                suffix=row.suffix,
                middle_spans=row.middle_spans,
                middle_char_start=row.middle_char_start,
                middle_char_end=row.middle_char_end,
                file_path=row.file_path,
                suffix_offset=row.suffix_offset,
                retrieved_chunks=retrieved_chunks_ser,
                ppl_scores=ppl_scores,
                ppl_prompt_len=ppl_prompt_len,
            )
        ]


# --------------------------------------------------------------------------------------
# CLI / Driver
# --------------------------------------------------------------------------------------


def _time() -> str:
    return dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser()
    p.add_argument("--mode", choices=["local", "ray"], default="ray")
    p.add_argument("--input", required=True)
    p.add_argument("--output-root", required=True)
    p.add_argument("--run-name", default=None)
    p.add_argument("--model-config-file", required=True)
    p.add_argument("--score-batch-size", type=int, default=4)
    p.add_argument("--max-target-tokens", type=int, default=256)
    p.add_argument("--score-signature", action="store_true")
    p.add_argument("--min-num-retrieved-chunks", type=int, default=20)
    p.add_argument("--limit-rows", type=int, default=None)
    p.add_argument("--num-workers", type=int, default=1)
    p.add_argument("--gpus-per-worker", type=int, default=1)
    return p.parse_args()


def infer_run_name(run_name: str | None, input_path: str) -> str:
    if run_name:
        return run_name
    return Path(input_path.rstrip("/").split("/")[-1]).name


def main():  # pragma: no cover
    args = parse_args()
    run_name = infer_run_name(args.run_name, args.input)
    run_root = os.path.join(args.output_root, run_name)
    output_dir = os.path.join(run_root, "score")
    fs_out, p_out = fsspec.core.url_to_fs(output_dir)
    try:
        fs_out.mkdirs(p_out, exist_ok=True)
    except FileExistsError:
        pass

    print(
        f"[{_time()}] Running actor scoring over parquet (workers={args.num_workers})"
    )
    with RayRunner(
        actor_cls=RetrievalScoreActor,
        actor_args={
            "model_config_file": args.model_config_file,
            "score_batch_size": args.score_batch_size,
            "max_target_tokens": args.max_target_tokens,
            "score_signature": args.score_signature,
            "min_num_retrieved_chunks": args.min_num_retrieved_chunks,
        },
        num_workers=args.num_workers,
        num_cpu_per_worker=8,
        num_gpu_per_worker=args.gpus_per_worker,
        local=args.mode == "local",
    ) as runner:
        runner.process_parquet(args.input, output_dir)

    cfg = {
        "input": args.input,
        "output_dir": output_dir,
        "run_name": run_name,
        "model_config_file": args.model_config_file,
        "score_batch_size": args.score_batch_size,
        "max_target_tokens": args.max_target_tokens,
        "score_signature": args.score_signature,
        "min_num_retrieved_chunks": args.min_num_retrieved_chunks,
        "limit_rows": args.limit_rows,
        "timestamp": _time(),
    }
    with fsspec.open(
        os.path.join(output_dir, "config.json"), "w", encoding="utf-8"
    ) as f:
        f.write(json.dumps(cfg, indent=2))
    print(f"[{_time()}] Wrote config.json")

    print(f"[{_time()}] Done retrieval score stage.")


if __name__ == "__main__":  # pragma: no cover
    main()
