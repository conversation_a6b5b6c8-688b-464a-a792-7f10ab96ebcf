"""Ray Stage 0: Sample repositories from The Stack.

Converted from Spark version (research/data/rag/run_sample_repo.py) to Ray.

Features:
- Filters by main repo language (constants.REPO_LANGUAGES)
- Filters by repo size (total_size between min/max)
- Random samples limit_repos using fraction + cushion (+0.1 like Spark version)
- Re-shuffles and hard limits to exact count
- Repartitions to reasonable shard count (>=20)
- Writes sharded JSON (Ray native) + config.json (run config) in output directory
- Local (--mode local) and cluster (--mode ray) modes

Usage (local test):
python experimental/mike/retrieval/0-repo.py \
  --mode local \
  --input /mnt/efs/spark-data/shared/repo_source_parquet \
  --limit-repos 1000

Usage (cluster via submit_ray.py):
python research/data/ray/submit_ray.py --ray-address $RAY_ADDRESS -- \
  python experimental/mike/retrieval/0-repo.py \
    --mode ray \
    --limit-repos 90000 \
    --repo-min-size 500000 \
    --repo-max-size 100000000
"""

from __future__ import annotations

import argparse
import datetime as dt
import json
import os
import sys
from pathlib import Path
from typing import Any

import ray
from ray.data import Dataset

# Reuse language list
from research.data.rag import constants


def _normalize_input_path(p: str) -> str:
    # Ray expects s3:// not s3a://
    if p.startswith("s3a://"):
        return "s3://" + p[len("s3a://") :]
    return p


def _time() -> str:
    return dt.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--mode",
        type=str,
        default="ray",
        choices=["local", "ray"],
        help="Execution mode: local=single process Ray, ray=cluster attach",
    )
    parser.add_argument(
        "--input",
        type=str,
        default="s3a://the-stack-processed/by-repo-3",
        help="Input path (parquet files)",
    )
    parser.add_argument(
        "--output-root",
        type=str,
        default="/mnt/efs/spark-data/shared/repo",
        help="Root directory for output run",
    )
    parser.add_argument(
        "--limit-repos",
        type=int,
        required=True,
        help="Number of repositories to sample",
    )
    parser.add_argument(
        "--repo-min-size", type=int, default=500_000, help="Minimum total_size to keep"
    )
    parser.add_argument(
        "--repo-max-size",
        type=int,
        default=100_000_000,
        help="Maximum total_size to keep",
    )
    parser.add_argument("--random-seed", type=int, default=74912, help="Random seed")
    parser.add_argument(
        "--top-lang-k", type=int, default=100, help="Show top-K repo languages"
    )
    parser.add_argument(
        "--no-lang-report",
        action="store_true",
        help="Disable language frequency report",
    )
    return parser.parse_args()


def init_ray(mode: str) -> None:
    if mode == "local":
        print(f"[{_time()}] Initializing Ray locally")
        ray.init(ignore_reinit_error=True)
    else:
        # RAY_ADDRESS should be set by the submission wrapper
        print(f"[{_time()}] Connecting to Ray cluster (address auto)")
        ray.init(address="auto", ignore_reinit_error=True)


def load_dataset(input_path: str) -> Dataset:
    print(f"[{_time()}] Reading parquet from {input_path}")
    ds: Dataset = ray.data.read_parquet(input_path)
    print(
        f"[{_time()}] Loaded dataset schema: {ds.schema()} | estimated rows (lazy count soon)"
    )
    return ds


def report_languages(ds: Dataset, top_k: int) -> None:
    print(f"[{_time()}] Computing top {top_k} languages")
    lang_ds: Dataset = ds.map(lambda r: {"lang": r["max_size_lang"]["langpart"]})
    grouped: Dataset = lang_ds.groupby("lang").count()
    top: Dataset = grouped.sort(key="count", descending=True).limit(top_k)
    top.show()


def apply_filters(
    ds: Dataset, repo_langs: set[str], min_size: int, max_size: int
) -> Dataset:
    print(
        f"[{_time()}] Filtering to repo main language in set (size={len(repo_langs)})"
    )
    ds = ds.filter(lambda r: r["max_size_lang"]["langpart"] in repo_langs)
    print(f"[{_time()}] Applying size bounds {min_size}..{max_size}")
    ds = ds.filter(
        lambda r: (r["total_size"] >= min_size) and (r["total_size"] <= max_size)
    )
    return ds


def sample_dataset(ds: Dataset, limit: int, seed: int) -> tuple[Dataset, int]:
    print(f"[{_time()}] Counting filtered dataset")
    total = ds.count()
    print(f"[{_time()}] Total after filters: {total}")
    if total == 0:
        print("No data after filters; exiting.")
        return ds, total
    fraction = min(limit / total + 0.1, 1.0)
    print(
        f"[{_time()}] Sampling fraction cushion: fraction={fraction:.4f} (limit={limit})"
    )
    if fraction < 1.0:
        ds = ds.random_sample(fraction=fraction, seed=seed)
    ds = ds.random_shuffle(seed=seed).limit(limit)
    sampled = ds.count()
    print(f"[{_time()}] Sampled {sampled}/{total} repos")
    return ds, total


def repartition(ds: Dataset, limit: int) -> Dataset:
    parts = max(limit // 20, 20)
    print(f"[{_time()}] Repartitioning to {parts} shards")
    return ds.repartition(parts)


def make_output_dir(output_root: str, limit_repos: int) -> str:
    date_str = dt.datetime.now().strftime("%Y-%m%d")
    folder = f"{date_str}_{int(limit_repos/1000):.0f}k"
    output_dir = os.path.join(output_root, folder)
    print(f"[{_time()}] Output directory: {output_dir}")
    return output_dir


def write_dataset(ds: Dataset, output_dir: str):
    print(f"[{_time()}] Writing dataset (Parquet shards) to {output_dir}")
    ds.write_parquet(output_dir)


def write_config(config: dict[str, Any], output_dir: str):
    import fsspec  # local import to avoid global dependency in some envs

    cfg_path = os.path.join(output_dir.rstrip("/"), "config.json")
    cfg_json = json.dumps(config, indent=2)
    print(f"[{_time()}] Writing config to {cfg_path}\n{cfg_json}")
    # Create parent directory via fsspec
    fs, parent = fsspec.core.url_to_fs(output_dir)
    try:
        fs.mkdirs(parent, exist_ok=True)
    except FileExistsError:
        pass
    with fsspec.open(cfg_path, "w", encoding="utf-8") as f:
        f.write(cfg_json)


def main():
    args = parse_args()
    input_path = _normalize_input_path(args.input)
    output_dir = make_output_dir(args.output_root, args.limit_repos)

    config = {
        "input": args.input,
        "repo_languages": list(constants.REPO_LANGUAGES),
        "limit_repos": args.limit_repos,
        "repo_min_size": args.repo_min_size,
        "repo_max_size": args.repo_max_size,
        "random_seed": args.random_seed,
    }

    init_ray(args.mode)

    ds = load_dataset(input_path)
    if not args.no_lang_report:
        report_languages(ds, args.top_lang_k)
    ds = apply_filters(
        ds,
        repo_langs=set(constants.REPO_LANGUAGES),
        min_size=args.repo_min_size,
        max_size=args.repo_max_size,
    )
    ds, total = sample_dataset(ds, args.limit_repos, args.random_seed)
    if total == 0:
        write_config(config, output_dir)
        if args.mode == "ray":
            ray.shutdown()
        return
    ds = repartition(ds, args.limit_repos)
    write_dataset(ds, output_dir)
    write_config(config, output_dir)

    if args.mode == "ray":  # explicit shutdown
        ray.shutdown()
    print(f"[{_time()}] Done.")


if __name__ == "__main__":  # pragma: no cover
    main()
