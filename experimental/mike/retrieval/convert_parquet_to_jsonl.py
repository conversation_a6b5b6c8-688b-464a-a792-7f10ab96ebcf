"""Convert Parquet retrieval data to JSONL format.

Converts parquet files containing retrieval data to JSONL, where:
- Each parquet row becomes one JSON line
- middle_spans bytes field is base64 encoded to middle_spans_b64
- Output directory is <input>_jsonl
"""

import argparse
import base64
import json
from pathlib import Path

import pyarrow.parquet as pq


def convert_retreival_parquet_to_jsonl(
    input_path: str, output_dir: str | None = None
) -> dict:
    """Convert parquet file(s) to JSONL format.

    Args:
        input_path: Path to parquet file or directory containing parquet files
        output_dir: Output directory (defaults to <input>_jsonl)

    Returns:
        Dict with conversion statistics
    """
    # Find parquet files
    input_p = Path(input_path)
    if input_p.is_file() and input_p.suffix == ".parquet":
        parquet_files = [input_p]
    elif input_p.is_dir():
        parquet_files = sorted(input_p.glob("*.parquet"))
    else:
        raise ValueError(f"Input must be a parquet file or directory: {input_path}")

    # Setup output directory
    out_dir = (
        Path(output_dir) if output_dir else Path(str(input_path).rstrip("/") + "_jsonl")
    )
    out_dir.mkdir(parents=True, exist_ok=True)

    # Convert each file
    total_rows = 0
    file_stats = []

    for pf in parquet_files:
        table = pq.read_table(pf)
        out_file = out_dir / (pf.stem + ".jsonl")
        rows = 0

        with open(out_file, "w") as f:
            for batch in table.to_batches():
                for row in batch.to_pylist():
                    # Base64 encode middle_spans bytes
                    row["middle_spans_b64"] = base64.b64encode(
                        row["middle_spans"]
                    ).decode()
                    del row["middle_spans"]

                    # Write JSON line without escaping unicode
                    f.write(json.dumps(row, ensure_ascii=False) + "\n")
                    rows += 1

        file_stats.append({"file": str(pf), "jsonl": str(out_file), "rows": rows})
        total_rows += rows

    # Write manifest
    manifest = {"files": file_stats, "total_rows": total_rows}
    with open(out_dir / "manifest.json", "w") as f:
        json.dump(manifest, f, indent=2)

    return manifest


def main():
    """Command line interface."""
    parser = argparse.ArgumentParser(description="Convert Parquet to JSONL")
    parser.add_argument("input", help="Parquet file or directory")
    parser.add_argument("--output", help="Output directory (defaults to <input>_jsonl)")
    args = parser.parse_args()

    manifest = convert_retreival_parquet_to_jsonl(args.input, args.output)
    print(json.dumps({"status": "ok", "total_rows": manifest["total_rows"]}, indent=2))


if __name__ == "__main__":
    main()
