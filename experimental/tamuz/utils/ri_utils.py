import logging

from base.datasets.gcs_client import GCSRequestInsightFetcher
from services.chat_host.chat_pb2 import ChatR<PERSON><PERSON>, ChatResponse
from experimental.tamuz.utils.pickle_cache import pickle_cache

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

DOGFOOD_SHARD = "dogfood-shard"
CACHE_DIRECTORY = "/mnt/efs/augment/user/vpas/ri_cache"


# @pickle_cache(CACHE_DIRECTORY)
def get_chat_host_request(
    request_id: str,
    tenant_name: str = DOGFOOD_SHARD,
) -> ChatRequest | None:
    """
    Get the chat_host_request event for a specific request ID.

    Args:
        request_id: The request ID to fetch the chat_host_request for
        tenant_name: The tenant name to query for

    Returns:
        The chat_host_request event data or None if not found
    """
    logger.debug(f"Fetching chat_host_request for request {request_id}...")
    result = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_request(
        request_id=request_id,
        request_event_names=frozenset({"chat_host_request"}),
    )

    # Check if we got valid data
    if not result or not result.events:
        logger.debug(f"No chat_host_request found for request {request_id}")
        return None

    # Find the chat_host_request event
    for event in result.events:
        if event.HasField("chat_host_request"):
            logger.debug(f"Found chat_host_request for request {request_id}")
            return event.chat_host_request.request

    logger.debug(f"No chat_host_request found for request {request_id}")
    return None


def get_chat_host_request_factory(tenant_name: str = DOGFOOD_SHARD):
    gcs_request_insight_fetcher = GCSRequestInsightFetcher.from_tenant_name(tenant_name)

    # @pickle_cache(CACHE_DIRECTORY)
    def _chat_host_request(request_id: str) -> ChatRequest | None:
        """
        Get the chat_host_request event for a specific request ID.

        Args:
            request_id: The request ID to fetch the chat_host_request for
            tenant_name: The tenant name to query for

        Returns:
            The chat_host_request event data or None if not found
        """
        logger.debug(f"Fetching chat_host_request for request {request_id}...")
        result = gcs_request_insight_fetcher.get_request(
            request_id=request_id,
            request_event_names=frozenset({"chat_host_request"}),
        )

        # Check if we got valid data
        if not result or not result.events:
            logger.debug(f"No chat_host_request found for request {request_id}")
            return None

        # Find the chat_host_request event
        for event in result.events:
            if event.HasField("chat_host_request"):
                logger.debug(f"Found chat_host_request for request {request_id}")
                return event.chat_host_request.request

        logger.debug(f"No chat_host_request found for request {request_id}")
        return None

    return _chat_host_request


def get_chat_host_request_full_factory(tenant_name: str = DOGFOOD_SHARD):
    """Factory function that returns a function to get the full RIChatRequest event."""
    gcs_request_insight_fetcher = GCSRequestInsightFetcher.from_tenant_name(tenant_name)

    # @pickle_cache(CACHE_DIRECTORY)
    def _chat_host_request_full(request_id: str):
        """
        Get the full chat_host_request event (RIChatRequest) for a specific request ID.

        Args:
            request_id: The request ID to fetch the chat_host_request for

        Returns:
            The full RIChatRequest event or None if not found
        """
        logger.debug(f"Fetching full chat_host_request for request {request_id}...")
        result = gcs_request_insight_fetcher.get_request(
            request_id=request_id,
            request_event_names=frozenset({"chat_host_request"}),
        )

        # Check if we got valid data
        if not result or not result.events:
            logger.debug(f"No chat_host_request found for request {request_id}")
            return None

        # Find the chat_host_request event
        for event in result.events:
            if event.HasField("chat_host_request"):
                logger.debug(f"Found full chat_host_request for request {request_id}")
                return event.chat_host_request

        logger.debug(f"No chat_host_request found for request {request_id}")
        return None

    return _chat_host_request_full


# @pickle_cache(CACHE_DIRECTORY)
def get_chat_host_response(
    request_id: str,
    tenant_name: str = DOGFOOD_SHARD,
) -> ChatResponse | None:
    """
    Get the chat_host_response event for a specific request ID.

    Args:
        request_id: The request ID to fetch the chat_host_response for
        tenant_name: The tenant name to query for

    Returns:
        The chat_host_response event data or None if not found
    """
    logger.debug(f"Fetching chat_host_response for request {request_id}...")
    result = GCSRequestInsightFetcher.from_tenant_name(tenant_name).get_request(
        request_id=request_id,
        request_event_names=frozenset({"chat_host_response"}),
    )

    # Check if we got valid data
    if not result or not result.events:
        logger.debug(f"No chat_host_response found for request {request_id}")
        return None

    # Find the chat_host_response event
    for event in result.events:
        if event.HasField("chat_host_response"):
            logger.debug(f"Found chat_host_response for request {request_id}")
            return event.chat_host_response.response

    logger.debug(f"No chat_host_response found for request {request_id}")
    return None


def get_chat_host_response_factory(
    tenant_name: str = DOGFOOD_SHARD,
) -> ChatResponse | None:
    gcs_request_insight_fetcher = GCSRequestInsightFetcher.from_tenant_name(tenant_name)

    # @pickle_cache(CACHE_DIRECTORY)
    def _get_chat_host_response(request_id: str) -> ChatResponse | None:
        """
        Get the chat_host_response event for a specific request ID.

        Args:
            request_id: The request ID to fetch the chat_host_response for
            tenant_name: The tenant name to query for

        Returns:
            The chat_host_response event data or None if not found
        """
        logger.debug(f"Fetching chat_host_response for request {request_id}...")
        result = gcs_request_insight_fetcher.get_request(
            request_id=request_id,
            request_event_names=frozenset({"chat_host_response"}),
        )

        # Check if we got valid data
        if not result or not result.events:
            logger.debug(f"No chat_host_response found for request {request_id}")
            return None

        # Find the chat_host_response event
        for event in result.events:
            if event.HasField("chat_host_response"):
                logger.debug(f"Found chat_host_response for request {request_id}")
                return event.chat_host_response.response

        logger.debug(f"No chat_host_response found for request {request_id}")
        return None

    return _get_chat_host_response
