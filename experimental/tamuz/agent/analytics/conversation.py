from dataclasses import dataclass, field
import json
import logging
from typing import Callable, Iterable, Dict

from more_itertools import unique

from base.prompt_format.common import (
    ChatRequestToolResult,
    ChatResultNode,
    ChatResultNodeType,
    ChatResultToolUse,
)

# from base.prompt_format_chat.lib.chat_history_builder import format_chat_history
import services.chat_host.chat_pb2 as chat_pb2
from services.chat_host.chat_proto_util import (
    convert_history as services_convert_history,
)
# from experimental.vpas.agent.str_replace_editor.utils import StrReplaceToolResult

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def extract_tool_use(
    response_nodes: Iterable[ChatResultNode],
) -> ChatResultToolUse | None:
    for node in response_nodes:
        if node.type == ChatResultNodeType.TOOL_USE:
            return node.tool_use
    return None


@dataclass
class StrReplaceEditorToolCallAnalysis:
    command: str | None = None
    path: str | None = None
    view_range: list[int] | None = None
    # Stores the expanded view range when view_range was expanded to meet minimum size
    # Format: [start_line, end_line]
    expanded_view_range: list[int] | None = None

    # view related fields:
    was_viewed: bool | None = None
    # True if in any previous turns this path was mentioned in retrieval tool output
    was_path_retrieved: bool | None = None
    # True if in any previous turns this file was created with a tool call
    was_file_created: bool | None = None
    # True if in any previous turns this file was mentioned with line numbers
    # e.g. from linter output in a format of <path>:<line_number>
    had_line_number_info: bool | None = None
    # True if in any previous turns this file was mentioned in a diff
    had_diff_info: bool | None = None
    # True if tool response from view command contains text: "View range expanded to meet minimum size of"
    was_view_expanded: bool | None = None

    # True if we didn't have any info about the content of the file before the view call
    # based on the above fields
    should_not_use_view_range: bool | None = None

    # True if this view command's range is a subrange of the previous expanded view range
    is_subrange_of_expanded_view: bool | None = None

    # True only for entries which are:
    # - command is view
    # - view_range is not None
    # - this view call is followed by at least two view call on the same file with non empty view_ranges
    # - all these view calls should go one after another with no other call in-between
    # - current view call should be the first in this sequence
    first_in_seq_of_views: bool | None = None

    # True only for entries which are:
    # - command is view
    # - view_range is not None
    # - this view call is preceded by at least two view call on the same file with non empty view_ranges
    # - all these view calls should go one after another with no other call in-between
    # - current view call should be the last in this sequence
    last_in_seq_of_views: bool | None = None

    # Error classification fields
    error_type: str | None = None  # Type of error if the tool call failed
    error_message: str | None = None  # Full error message if the tool call failed
    # str_replace_tool_result: StrReplaceToolResult | None = None


@dataclass
class SelectedCodeContext:
    """Context about code selection when the user message was sent."""

    path: str | None = None  # File path where code was selected
    selected_code: str | None = None  # The actual selected code
    prefix: str | None = None  # Code before the selection
    suffix: str | None = None  # Code after the selection
    lang: str | None = None  # Programming language of the file


@dataclass
class SystemPromptComponents:
    """Components that make up the system prompt for this request."""

    user_guidelines: str | None = None  # Custom user rules
    workspace_guidelines: str | None = None  # Workspace-specific rules
    agent_memories: str | None = None  # Agent memories
    persona_type: int | None = None  # PersonaType enum value
    # Note: The actual computed system prompt would need to be reconstructed
    # from these components plus the base persona prompt


@dataclass
class RepositoryContext:
    """Repository and workspace context for the request."""

    diff: str | None = None  # Repository diff
    relevant_commit_messages: list[str] = field(default_factory=list)  # Commit context
    example_commit_messages: list[str] = field(default_factory=list)  # Example commits
    changed_file_stats: dict | None = None  # File change statistics


ToolCallAnalysis = StrReplaceEditorToolCallAnalysis


@dataclass
class ToolCall:
    tool_use_request_id: str
    tool_use_request: chat_pb2.ChatRequest
    tool_use: ChatResultToolUse

    tool_result_request_id: str | None = None
    tool_result_request: chat_pb2.ChatRequest | None = None
    tool_result: ChatRequestToolResult | None = None

    analysis: ToolCallAnalysis | None = None


@dataclass
class AgentTurn:
    message: str
    request_id: str
    request: chat_pb2.ChatRequest
    tool_call: ToolCall | None = None
    message_token_count: int = 0  # Number of tokens in the agent message


@dataclass
class AgentRound:
    """
    One round in a conversation between user and agent.

    A round starts with a user message followed by a sequence of agent turns.
    The round ends when the agent has completed its task(meaning its last turn does not have a tool call)
    """

    user_message: str
    user_message_request_id: str
    user_message_request: chat_pb2.ChatRequest
    agent_turns: list[AgentTurn] = field(default_factory=list)
    ide_state: Dict[str, any] | None = None  # IDE state at the start of this round

    # New context fields extracted from the ChatRequest
    selected_code_context: SelectedCodeContext | None = None
    system_prompt_components: SystemPromptComponents | None = None
    repository_context: RepositoryContext | None = None
    user_message_token_count: int = 0  # Number of tokens in the user message
    raw_tokenization_text: str | None = (
        None  # Raw tokenization string sent to the model
    )


@dataclass
class ExchangeData:
    request_id: str | None = None

    user_message: str | None = None
    tool_result: ChatRequestToolResult | None = None

    agent_response: str | None = None
    tool_use: ChatResultToolUse | None = None


def extract_selected_code_context(request: chat_pb2.ChatRequest) -> SelectedCodeContext:
    """Extract selected code context from a ChatRequest."""
    return SelectedCodeContext(
        path=request.path if request.path else None,
        selected_code=request.selected_code if request.selected_code else None,
        prefix=request.prefix if request.prefix else None,
        suffix=request.suffix if request.suffix else None,
        lang=request.lang if request.lang else None,
    )


def extract_system_prompt_components(
    request: chat_pb2.ChatRequest,
) -> SystemPromptComponents:
    """Extract system prompt components from a ChatRequest."""
    persona_type = None
    try:
        if hasattr(request, "persona_type") and request.persona_type is not None:
            persona_type = int(request.persona_type)
    except (AttributeError, ValueError, TypeError):
        persona_type = None

    return SystemPromptComponents(
        user_guidelines=request.user_guidelines if request.user_guidelines else None,
        workspace_guidelines=request.workspace_guidelines
        if request.workspace_guidelines
        else None,
        agent_memories=request.agent_memories if request.agent_memories else None,
        persona_type=persona_type,
    )


def extract_repository_context(request: chat_pb2.ChatRequest) -> RepositoryContext:
    """Extract repository context from a ChatRequest."""
    return RepositoryContext(
        diff=request.diff if request.diff else None,
        relevant_commit_messages=list(request.relevant_commit_messages)
        if request.relevant_commit_messages
        else [],
        example_commit_messages=list(request.example_commit_messages)
        if request.example_commit_messages
        else [],
        # Note: changed_file_stats would need additional parsing if needed
        changed_file_stats=None,
    )


def extract_raw_tokenization_text(ri_chat_request) -> str | None:
    """Extract raw tokenization text from a RIChatRequest."""
    if ri_chat_request is None:
        return None

    try:
        # Check if the RIChatRequest has a tokenization field
        if hasattr(ri_chat_request, "tokenization") and ri_chat_request.tokenization:
            tokenization = ri_chat_request.tokenization
            # Check if the tokenization has a text field
            if hasattr(tokenization, "text") and tokenization.text:
                return tokenization.text
    except (AttributeError, TypeError):
        pass

    return None


def convert_history(history: Iterable[chat_pb2.Exchange]) -> list[ExchangeData]:
    exchange_data_list = []
    logger.debug("Converting history")
    for exchange in history:
        logger.debug(f"Processing exchange {exchange.request_id}")
        request_nodes = list(exchange.request_nodes)

        if len(request_nodes) == 0 and exchange.request_message:
            request_nodes = [
                chat_pb2.ChatRequestNode(
                    id=0,
                    type=chat_pb2.ChatRequestNodeType.TEXT,
                    text_node=chat_pb2.ChatRequestText(
                        content=exchange.request_message
                    ),
                )
            ]

        text_nodes = [
            node
            for node in request_nodes
            if node.type == chat_pb2.ChatRequestNodeType.TEXT
        ]
        tool_result_nodes = [
            node
            for node in request_nodes
            if node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT
        ]

        # there is a bug on the client that causes TEXT nodes to be duplicated
        # we should fix the client, but for now we just dedup here
        text_nodes = list(
            unique(text_nodes, key=lambda node: node.text_node.content.strip())
        )

        assert len(text_nodes) <= 1
        assert len(tool_result_nodes) <= 1

        assert len(text_nodes) + len(tool_result_nodes) == 1

        request_node = text_nodes[0] if text_nodes else tool_result_nodes[0]

        user_message = None
        tool_result_node = None
        if request_node.type == chat_pb2.ChatRequestNodeType.TEXT:
            user_message = (
                request_node.text_node.content if request_node.text_node else None
            )
        elif request_node.type == chat_pb2.ChatRequestNodeType.TOOL_RESULT:
            tool_result_node = ChatRequestToolResult(
                tool_use_id=request_node.tool_result_node.tool_use_id,
                content=request_node.tool_result_node.content,
                is_error=request_node.tool_result_node.is_error,
                request_id=exchange.request_id,
            )

        agent_response = ""
        tool_use = None
        for response_node in exchange.response_nodes:
            if response_node.type == ChatResultNodeType.RAW_RESPONSE:
                agent_response += response_node.content
            elif response_node.type == ChatResultNodeType.TOOL_USE:
                tool_use = ChatResultToolUse(
                    tool_use_id=response_node.tool_use.tool_use_id,
                    name=response_node.tool_use.tool_name,
                    input=json.loads(response_node.tool_use.input_json),
                )

        exchange_data_list.append(
            ExchangeData(
                request_id=exchange.request_id,
                user_message=user_message,
                tool_result=tool_result_node,
                agent_response=agent_response,
                tool_use=tool_use,
            )
        )
    return exchange_data_list


def is_tool_result_truncated(tool_result_node: chat_pb2.ChatRequestToolResult) -> bool:
    return tool_result_node.content.startswith("[Truncated...")


@dataclass
class ConversationMetadata:
    """Metadata extracted from the conversation's chat requests."""

    model_name: str | None = None
    user_guidelines: str | None = None
    workspace_guidelines: str | None = None
    agent_memories: str | None = None
    tool_definitions: list[Dict[str, str]] = field(
        default_factory=list
    )  # List of {name, description}


@dataclass
class Conversation:
    last_request_id: str
    agent_rounds: list[AgentRound]
    tool_result_request_id_to_turn: Dict[str, AgentTurn] = field(default_factory=dict)
    metadata: ConversationMetadata = field(default_factory=ConversationMetadata)

    @classmethod
    def extract_metadata_from_request(
        cls, chat_request: chat_pb2.ChatRequest
    ) -> ConversationMetadata:
        """Extract metadata from a chat request."""
        metadata = ConversationMetadata()

        # Extract simple fields
        if hasattr(chat_request, "model_name"):
            metadata.model_name = chat_request.model_name

        if hasattr(chat_request, "user_guidelines"):
            metadata.user_guidelines = chat_request.user_guidelines

        if hasattr(chat_request, "workspace_guidelines"):
            metadata.workspace_guidelines = chat_request.workspace_guidelines

        if hasattr(chat_request, "agent_memories"):
            metadata.agent_memories = chat_request.agent_memories

        # Extract tool definitions
        if hasattr(chat_request, "tool_definitions"):
            for tool_def in chat_request.tool_definitions:
                tool_info = {
                    "name": tool_def.name if hasattr(tool_def, "name") else None,
                    "description": tool_def.description
                    if hasattr(tool_def, "description")
                    else None,
                }
                metadata.tool_definitions.append(tool_info)

        return metadata

    @classmethod
    def from_chat_request(
        cls,
        request_id: str,
        chat_request: chat_pb2.ChatRequest,
        chat_response: chat_pb2.ChatResponse,
        get_chat_host_request_func: Callable[[str], chat_pb2.ChatRequest],
        get_chat_host_request_full_func: Callable[[str], any] | None = None,
    ) -> "Conversation":
        # Initialize chat_history
        if hasattr(chat_request, "chat_history"):
            chat_history = list(chat_request.chat_history)
        else:
            chat_history = []

        current_exchange = chat_pb2.Exchange(
            request_id=request_id,
            request_message=chat_request.message,
            request_nodes=list(chat_request.nodes),
            response_nodes=list(chat_response.nodes),
        )
        chat_history.append(current_exchange)

        chat_history = convert_history(chat_history)

        # Process the chat history to create agent rounds
        agent_rounds = []
        tool_result_request_id_to_turn = {}

        current_agent_round = None
        logger.debug("Processing chat history")
        for exchange in chat_history:
            assert exchange.request_id is not None
            # Determine exchange type: either a user message starting a new round,
            # or a tool response to a previous tool call. This affects how we
            # process the current exchange and update the conversation structure.
            if exchange.user_message is not None:
                # If we have a user message, we are starting a new agent round

                if current_agent_round:
                    # current agent round was interrupted
                    agent_rounds.append(current_agent_round)

                user_request = get_chat_host_request_func(exchange.request_id)

                # Extract IDE state from the user message request
                ide_state = None
                if hasattr(user_request, "nodes"):
                    for node in user_request.nodes:
                        if (
                            hasattr(node, "type")
                            and node.type == chat_pb2.ChatRequestNodeType.IDE_STATE
                        ):
                            if hasattr(node, "ide_state_node"):
                                ide_state_node = node.ide_state_node
                                ide_state = {
                                    "workspace_folders": [],
                                    "current_terminal": None,
                                }

                                if hasattr(ide_state_node, "workspace_folders"):
                                    for folder in ide_state_node.workspace_folders:
                                        folder_info = {
                                            "repository_root": folder.repository_root
                                            if hasattr(folder, "repository_root")
                                            else None,
                                            "folder_root": folder.folder_root
                                            if hasattr(folder, "folder_root")
                                            else None,
                                        }
                                        ide_state["workspace_folders"].append(
                                            folder_info
                                        )

                                if (
                                    hasattr(ide_state_node, "current_terminal")
                                    and ide_state_node.current_terminal
                                ):
                                    terminal = ide_state_node.current_terminal
                                    ide_state["current_terminal"] = {
                                        "terminal_id": terminal.terminal_id
                                        if hasattr(terminal, "terminal_id")
                                        else None,
                                        "current_working_directory": terminal.current_working_directory
                                        if hasattr(
                                            terminal, "current_working_directory"
                                        )
                                        else None,
                                    }
                                break

                # Extract raw tokenization text if available
                raw_tokenization_text = None
                if get_chat_host_request_full_func:
                    try:
                        ri_chat_request = get_chat_host_request_full_func(
                            exchange.request_id
                        )
                        raw_tokenization_text = extract_raw_tokenization_text(
                            ri_chat_request
                        )
                    except Exception as e:
                        logger.debug(
                            f"Failed to extract raw tokenization text for {exchange.request_id}: {e}"
                        )

                current_agent_round = AgentRound(
                    user_message=exchange.user_message,
                    user_message_request_id=exchange.request_id,
                    user_message_request=user_request,
                    ide_state=ide_state,
                    selected_code_context=extract_selected_code_context(user_request),
                    system_prompt_components=extract_system_prompt_components(
                        user_request
                    ),
                    repository_context=extract_repository_context(user_request),
                    raw_tokenization_text=raw_tokenization_text,
                    # TODO: Add user_message_token_count calculation
                )
            else:
                # Otherwise, we are in the middle of an agent round.
                # This exchange contains a tool response to the tool call
                # made by the model in the previous exchange
                assert current_agent_round is not None
                assert current_agent_round.agent_turns
                turn = current_agent_round.agent_turns[-1]
                assert turn.tool_call is not None
                tool_call = turn.tool_call
                tool_call.tool_result = exchange.tool_result
                tool_call.tool_result_request_id = exchange.request_id
                tool_call.tool_result_request = get_chat_host_request_func(
                    exchange.request_id
                )
                tool_result_request_id_to_turn[exchange.request_id] = turn

            turn = AgentTurn(
                request_id=exchange.request_id,
                request=get_chat_host_request_func(exchange.request_id),
                message=exchange.agent_response or "",
            )
            current_agent_round.agent_turns.append(turn)
            if exchange.tool_use is None:
                # If the agent didn't make a tool call, this round is complete.
                # We add the current round to our collection and reset for the next round.
                agent_rounds.append(current_agent_round)
                current_agent_round = None
            else:
                turn.tool_call = ToolCall(
                    tool_use_request_id=exchange.request_id,
                    tool_use_request=get_chat_host_request_func(exchange.request_id),
                    tool_use=exchange.tool_use,
                )

        if current_agent_round is not None:
            agent_rounds.append(current_agent_round)

        # Extract metadata from the chat request
        metadata = cls.extract_metadata_from_request(chat_request)

        # Create the conversation object
        conversation = cls(
            last_request_id=request_id,
            agent_rounds=agent_rounds,
            tool_result_request_id_to_turn=tool_result_request_id_to_turn,
            metadata=metadata,
        )

        return conversation
