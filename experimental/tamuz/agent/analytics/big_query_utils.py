import datetime
import logging
from typing import Optional, List, Tuple

from google.cloud import bigquery
from base.datasets.gcp_creds import get_gcp_creds
from base.datasets.tenants import DatasetTenant

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Constants
CACHE_DIRECTORY = "/home/<USER>/ri_cache"


def get_agent_conv_last_request_ids(
    tenant: DatasetTenant,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> list[str]:
    """Get the last request_id for each conversation in the given time range.

    Args:
        from_datetime: Start of the time range (inclusive). If None, no lower bound.
        to_datetime: End of the time range (inclusive). If None, no upper bound.
        tenant: The tenant to filter by.

    Returns:
        List of request_ids for the last request in each conversation.
    """
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Build time filter conditions
    time_conditions = []
    if from_datetime:
        time_conditions.append(f"time >= TIMESTAMP('{from_datetime.isoformat()}')")
    if to_datetime:
        time_conditions.append(f"time <= TIMESTAMP('{to_datetime.isoformat()}')")
    time_filter = " AND ".join(time_conditions) if time_conditions else "1=1"

    # Query to get the last request_id for each conversation
    query = f"""
    WITH agent_requests AS (
        SELECT
            request_id,
            time,
            JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') as conversation_id
        FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
        WHERE tenant = '{tenant.name}' AND {time_filter}
        AND JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') IS NOT NULL
    ),
    latest_requests AS (
        SELECT
            conversation_id,
            MAX(time) as latest_time
        FROM agent_requests
        GROUP BY conversation_id
    )
    SELECT ar.request_id
    FROM agent_requests ar
    JOIN latest_requests lr
    ON ar.conversation_id = lr.conversation_id AND ar.time = lr.latest_time
    ORDER BY ar.time DESC
    """

    rows = bigquery_client.query_and_wait(query)
    return [row.request_id for row in rows]


def get_agent_conv_request_ids_and_turn_counts(
    tenant: DatasetTenant,
    from_datetime: Optional[datetime.datetime] = None,
    to_datetime: Optional[datetime.datetime] = None,
) -> List[Tuple[str, str, int]]:
    """Get the conversation_id, last request_id, and number of turns for each conversation in the given time range.

    Args:
        from_datetime: Start of the time range (inclusive). If None, no lower bound.
        to_datetime: End of the time range (inclusive). If None, no upper bound.
        tenant: The tenant to filter by.

    Returns:
        List of tuples containing (conversation_id, last_request_id, turn_count) for each conversation.
    """
    TABLE = "agent_request_event"
    gcp_creds, _ = get_gcp_creds(None)
    bigquery_client = bigquery.Client(project=tenant.project_id, credentials=gcp_creds)

    # Build time filter conditions
    time_conditions = []
    if from_datetime:
        time_conditions.append(f"time >= TIMESTAMP('{from_datetime.isoformat()}')")
    if to_datetime:
        time_conditions.append(f"time <= TIMESTAMP('{to_datetime.isoformat()}')")
    time_filter = " AND ".join(time_conditions) if time_conditions else "1=1"

    # Query to get the conversation_id, last request_id, and turn count for each conversation
    query = f"""
    WITH agent_requests AS (
        SELECT
            request_id,
            time,
            JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') as conversation_id
        FROM `{tenant.project_id}.{tenant.analytics_dataset_name}.{TABLE}`
        WHERE tenant = '{tenant.name}' AND {time_filter}
        AND JSON_EXTRACT_SCALAR(sanitized_json, '$.conversation_id') IS NOT NULL
    ),
    turn_counts AS (
        SELECT
            conversation_id,
            COUNT(*) as turn_count,
            MAX(time) as latest_time
        FROM agent_requests
        GROUP BY conversation_id
    )
    SELECT
        ar.conversation_id,
        ar.request_id,
        tc.turn_count
    FROM agent_requests ar
    JOIN turn_counts tc
    ON ar.conversation_id = tc.conversation_id AND ar.time = tc.latest_time
    ORDER BY ar.time DESC
    """

    rows = bigquery_client.query_and_wait(query)
    return [(row.conversation_id, row.request_id, row.turn_count) for row in rows]
