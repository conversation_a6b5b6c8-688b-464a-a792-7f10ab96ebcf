#!/usr/bin/env python3
"""
Python wrapper for the Go blob fetcher.
This allows the existing Python export system to use the Go blob fetcher
for improved performance while maintaining compatibility.
"""

import json
import subprocess
import tempfile
import os
from pathlib import Path
from typing import List, Dict, Any, Optional


class GoBlobFetcher:
    """Python wrapper for the Go blob fetcher."""

    def __init__(self, go_binary_path: Optional[str] = None):
        """
        Initialize the Go blob fetcher wrapper.

        Args:
            go_binary_path: Path to the Go binary. If None, assumes it's in the same directory.
        """
        if go_binary_path is None:
            # Assume the binary is in the same directory as this script
            script_dir = Path(__file__).parent
            go_binary_path = script_dir / "blob-fetcher"

        self.go_binary_path = Path(go_binary_path)
        if not self.go_binary_path.exists():
            raise FileNotFoundError(f"Go binary not found at {self.go_binary_path}")

    def fetch_blobs(
        self,
        blob_ids: List[str],
        tenant: str,
        output_dir: str,
        metadata_only: bool = False,
        workers: int = 50,
    ) -> Dict[str, Any]:
        """
        Fetch blobs using the Go implementation.

        Args:
            blob_ids: List of blob IDs to fetch
            tenant: Tenant name (e.g., 'i1-vanguard0')
            output_dir: Directory to save blobs to
            metadata_only: If True, only fetch metadata, not blob content
            workers: Number of concurrent workers

        Returns:
            Dictionary with results including metadata and statistics
        """
        # Create temporary file for blob IDs
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump({"initial": blob_ids}, f)
            blob_ids_file = f.name

        try:
            # Build command - Go program expects output directory, not file
            cmd = [
                str(self.go_binary_path),
                "-blob-ids",
                blob_ids_file,
                "-tenant",
                tenant,
                "-output",
                output_dir,
                "-workers",
                str(workers),
            ]

            if metadata_only:
                cmd.append("-metadata-only")

            # Run the Go binary
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            # Parse the JSON output
            return json.loads(result.stdout)

        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"Go blob fetcher failed: {e.stderr}")
        finally:
            # Clean up temporary file
            os.unlink(blob_ids_file)

    def fetch_single_blob(
        self, blob_id: str, tenant: str, output_dir: str, metadata_only: bool = False
    ) -> Dict[str, Any]:
        """
        Fetch a single blob.

        Args:
            blob_id: Single blob ID to fetch
            tenant: Tenant name
            output_dir: Directory to save blob to
            metadata_only: If True, only fetch metadata

        Returns:
            Dictionary with blob metadata and statistics
        """
        return self.fetch_blobs([blob_id], tenant, output_dir, metadata_only, workers=1)


def main():
    """Example usage of the Go blob fetcher wrapper."""
    import argparse

    parser = argparse.ArgumentParser(description="Python wrapper for Go blob fetcher")
    parser.add_argument(
        "--blob-ids-file", required=True, help="JSON file with blob IDs"
    )
    parser.add_argument("--tenant", required=True, help="Tenant name")
    parser.add_argument("--output-dir", required=True, help="Output directory")
    parser.add_argument(
        "--metadata-only", action="store_true", help="Only fetch metadata"
    )
    parser.add_argument("--workers", type=int, default=50, help="Number of workers")
    parser.add_argument("--go-binary", help="Path to Go binary")

    args = parser.parse_args()

    # Load blob IDs
    with open(args.blob_ids_file, "r") as f:
        blob_data = json.load(f)
        if isinstance(blob_data, dict) and "initial" in blob_data:
            blob_ids = blob_data["initial"]
        elif isinstance(blob_data, list):
            blob_ids = blob_data
        else:
            raise ValueError("Invalid blob IDs file format")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Initialize fetcher
    fetcher = GoBlobFetcher(args.go_binary)

    # Fetch blobs
    try:
        result = fetcher.fetch_blobs(
            blob_ids=blob_ids,
            tenant=args.tenant,
            output_dir=args.output_dir,
            metadata_only=args.metadata_only,
            workers=args.workers,
        )

        print(f"Successfully processed {result['total_requested']} blobs")
        print(f"Success: {result['successful']}, Failed: {result['failed']}")
        print(f"Duration: {result['duration']:.2f}s")
        print(f"Speed: {result['speed']:.2f} blobs/second")

        if result["failed"] > 0:
            print("\nErrors:")
            for error in result.get("errors", []):
                print(f"  {error}")

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
