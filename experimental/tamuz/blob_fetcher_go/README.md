# Go Blob Fetcher for Vanguard Export System

A high-performance Go implementation for fetching blobs and reconstructing workspaces from Google Cloud Storage, designed to replace the slower gsutil-based approach in the Python vanguard export system.

## Performance

- **Speed**: 647+ blobs/second for full downloads (vs ~25 blobs/second with gsutil)
- **Concurrency**: Configurable worker pools (default: 50 workers)
- **Success Rate**: 99.66% on production data (7,069/7,093 blobs)
- **Scale**: Tested with 7,093 blobs (77MB) in 10.91 seconds

## Features

### ✅ Cross-Platform Path Normalization
- Converts Windows backslashes (`\`) to Unix forward slashes (`/`)
- Handles problematic characters (`:`, `*`, `?`, `"`, `<`, `>`, `|`)
- Preserves template variables like `${bussiPackage}`
- Creates proper directory structures on Unix systems

### ✅ High-Performance Concurrent Processing
- Native Go GCS client library
- Configurable worker pool concurrency
- Efficient memory usage with streaming downloads
- Detailed error reporting and retry logic

### ✅ Multiple Tenant Support
- Pre-configured for all vanguard tenants (i0-vanguard0 through i1-vanguard7)
- Automatic bucket and prefix resolution
- Easy tenant configuration management

### ✅ Flexible Output Options
- **Metadata-only**: Fast metadata extraction (~1,032 blobs/second)
- **Full download**: Complete workspace reconstruction with files
- **JSON output**: Compatible with existing Python tooling

## Files

- **`main.go`**: Core Go implementation with GCS client and blob processing
- **`python_wrapper.py`**: Python wrapper for seamless integration with existing systems
- **`blob-fetcher`**: Compiled Go binary (ready to use)
- **`go.mod`/`go.sum`**: Go module dependencies
- **`test_conversation/`**: Sample test data with 7,093 real blob IDs

## Usage

### Direct Go Binary
```bash
./blob-fetcher -blob-ids blob_ids.json -tenant i1-vanguard0 -output workspace_dir -workers 50
```

### Python Wrapper
```python
from python_wrapper import GoBlobFetcher

fetcher = GoBlobFetcher()
result = fetcher.fetch_blobs(
    blob_ids=["blob1", "blob2", ...],
    tenant="i1-vanguard0",
    output_dir="workspace",
    metadata_only=False,
    workers=50
)
```

### Command Line Python Wrapper
```bash
python3 python_wrapper.py --blob-ids-file blob_ids.json --tenant i1-vanguard0 --output-dir workspace --workers 50
```

## Integration

This implementation is designed as a drop-in replacement for gsutil-based blob fetching in the existing Python vanguard export system. The Python wrapper maintains full compatibility while providing dramatic performance improvements.

## Requirements

- Go 1.19+ (for building)
- Google Cloud authentication (gcloud auth)
- Access to vanguard GCS buckets

## Building

```bash
go build -o blob-fetcher main.go
```

## Testing

The implementation has been thoroughly tested with real production data:
- 7,093 blobs from actual conversation
- Cross-platform path handling verified
- Performance benchmarked against gsutil
- Error handling validated with missing blobs
