#!/bin/bash
# Setup script for Vanguard Export System

# Get the repository root (4 levels up from this script)
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
REPO_ROOT="$( cd "$SCRIPT_DIR/../../../.." && pwd )"

# Export PYTHONPATH to include the repository root
export PYTHONPATH="$REPO_ROOT:$PYTHONPATH"

# Also export the path to the export system
export VANGUARD_EXPORT_ROOT="$SCRIPT_DIR"

echo "Environment setup complete!"
echo "Repository root: $REPO_ROOT"
echo "Export system root: $VANGUARD_EXPORT_ROOT"
echo ""
echo "To use the export system:"
echo "1. Source this script: source setup_env.sh"
echo "2. Activate virtual environment: source ../vpas/agent/analytics/venv_export/bin/activate"
echo "3. Run scripts from the scripts/ directory"
