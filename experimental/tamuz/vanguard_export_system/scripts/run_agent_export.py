#!/usr/bin/env python3
"""
Export all agent conversations from the last week using Go blob fetcher for high performance.
This script will:
1. Export conversation metadata for all vanguard tenants from the last week
2. Use the Go blob fetcher to reconstruct workspaces in parallel
3. Run in tmux for long-running operation with progress tracking
"""

import argparse
import concurrent.futures
import datetime
import json
import logging
import sys
import threading
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

# Setup paths for imports
script_dir = Path(__file__).resolve().parent
export_system_root = script_dir.parent
repo_root = export_system_root.parent.parent.parent  # Go up to repository root

# Add both paths - repo root for base dependencies, export system for local modules
sys.path.insert(0, str(repo_root))
sys.path.insert(0, str(export_system_root))

from experimental.tamuz.agent.analytics.big_query_utils import (  # noqa: E402
    get_agent_conv_request_ids_and_turn_counts,
)
from src.export_base import export_conversations_with_complete_workspace, get_tenant  # noqa: E402
from src.go_blob_fetcher import GoBlobFetcher, download_blobs_with_go_fetcher  # noqa: E402

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Global progress tracking
progress_lock = threading.Lock()
global_progress = {
    "total_conversations": 0,
    "completed_conversations": 0,
    "failed_conversations": 0,
    "total_blobs": 0,
    "completed_blobs": 0,
    "start_time": None,
    "tenants_progress": {},
}


def log_progress():
    """Log current progress statistics."""
    with progress_lock:
        if global_progress["start_time"]:
            elapsed = time.time() - global_progress["start_time"]
            completed = global_progress["completed_conversations"]
            total = global_progress["total_conversations"]

            if completed > 0:
                avg_time_per_conv = elapsed / completed
                eta_seconds = avg_time_per_conv * (total - completed)
                eta = datetime.timedelta(seconds=int(eta_seconds))

                logger.info(
                    f"📊 PROGRESS: {completed}/{total} conversations ({completed/total*100:.1f}%) | "
                    f"Failed: {global_progress['failed_conversations']} | "
                    f"Elapsed: {datetime.timedelta(seconds=int(elapsed))} | "
                    f"ETA: {eta}"
                )


def export_conversation_with_go_workspace(
    conversation_data: tuple,  # (conv_id, req_id, turn_count)
    tenant_name: str,
    output_base_dir: Path,
    config: dict,
    from_date: datetime.datetime,
    to_date: datetime.datetime,
) -> Dict[str, Any]:
    """Export a single conversation and reconstruct workspace using Go blob fetcher."""

    try:
        conversation_id, request_id, turn_count = conversation_data
        logger.info(
            f"🚀 Starting export for conversation {conversation_id} ({tenant_name}) - {turn_count} turns"
        )

        # Get tenant configuration
        tenant = get_tenant(tenant_name)
        if not tenant:
            raise ValueError(f"Unknown tenant: {tenant_name}")

        # Create output directory for this conversation
        conv_output_dir = output_base_dir / tenant_name
        conv_output_dir.mkdir(parents=True, exist_ok=True)

        # Export conversation metadata first (fast) - use request_id instead of conversation_id
        result = export_conversations_with_complete_workspace(
            from_date=from_date,  # Use config date range
            to_date=to_date,  # Use config date range
            tenant_name=tenant_name,
            num_conversations=1,  # We only want this specific conversation
            output_dir=conv_output_dir,
            request_id=request_id,  # Export this specific request ID
            max_blobs=config.get("max_blobs_per_conversation", 500000),
            parallel_workers=config.get("performance_settings", {}).get(
                "max_workers_per_conversation", 100
            ),
            optimize_binary=True,
        )

        if not result:
            raise Exception(f"Failed to export conversation metadata: {result}")

        # Get blob IDs from the exported conversation
        # The result is a Path to the export directory, find the blob_ids.json file within it
        export_dir = result
        blob_ids_file = (
            export_dir
            / "conversations"
            / f"conversation_{request_id}"
            / "blob_ids.json"
        )
        if not blob_ids_file.exists():
            raise Exception(f"Blob IDs file not found: {blob_ids_file}")

        with open(blob_ids_file, "r") as f:
            blob_data = json.load(f)

        # Extract blob IDs from the correct structure
        blob_ids = blob_data.get("initial", [])

        if not blob_ids:
            logger.warning(f"No blob IDs found for conversation {conversation_id}")
            with progress_lock:
                global_progress["completed_conversations"] += 1
            return {"success": True, "blob_count": 0, "message": "No blobs to download"}

        logger.info(
            f"📦 Found {len(blob_ids)} blobs for conversation {conversation_id}"
        )

        # Use Go blob fetcher to reconstruct workspace
        workspace_dir = (
            export_dir
            / "conversations"
            / f"conversation_{request_id}"
            / "initial_workspace"
        )

        go_fetcher = GoBlobFetcher()
        download_result = go_fetcher.download_blobs_with_go(
            blob_ids=blob_ids,
            tenant=tenant,
            output_dir=workspace_dir,
            max_workers=config.get("go_blob_fetcher_settings", {}).get(
                "max_workers", 50
            ),
        )

        # Update global progress
        with progress_lock:
            global_progress["completed_conversations"] += 1
            global_progress["total_blobs"] += download_result.get("total_requested", 0)
            global_progress["completed_blobs"] += download_result.get("successful", 0)

        logger.info(
            f"✅ Completed conversation {conversation_id}: "
            f"{download_result.get('successful', 0)}/{download_result.get('total_requested', 0)} blobs "
            f"({download_result.get('speed', 0):.1f} blobs/sec)"
        )

        return {
            "success": True,
            "conversation_id": conversation_id,
            "tenant": tenant_name,
            "blob_count": len(blob_ids),
            "download_stats": download_result,
        }

    except Exception as e:
        logger.error(f"❌ Failed to export conversation {conversation_id}: {e}")
        with progress_lock:
            global_progress["failed_conversations"] += 1
        return {
            "success": False,
            "conversation_id": conversation_id,
            "tenant": tenant_name,
            "error": str(e),
        }


def export_tenant_conversations(
    tenant_name: str,
    date_range: Dict[str, str],
    conversations_per_day: Optional[int],
    output_base_dir: Path,
    config: dict,
) -> Dict[str, Any]:
    """Export all conversations for a tenant within the date range."""

    logger.info(f"🎯 Starting export for tenant {tenant_name}")

    try:
        # Get tenant configuration
        tenant = get_tenant(tenant_name)
        if not tenant:
            raise ValueError(f"Unknown tenant: {tenant_name}")

        # Get conversation IDs for the date range
        from_date = datetime.datetime.strptime(date_range["from_date"], "%Y-%m-%d")
        to_date = datetime.datetime.strptime(date_range["to_date"], "%Y-%m-%d")

        # Use BigQuery to discover conversations
        logger.info(
            f"Discovering conversations for {tenant_name} from {from_date} to {to_date}"
        )

        conversation_data = get_agent_conv_request_ids_and_turn_counts(
            tenant=tenant,
            from_datetime=from_date,
            to_datetime=to_date,
        )

        # Filter conversations based on minimum turns and other criteria
        min_turns = config.get("filters", {}).get("min_turns", 2)
        filtered_conversations = []

        for conv_id, req_id, turn_count in conversation_data:
            if turn_count >= min_turns:
                filtered_conversations.append((conv_id, req_id, turn_count))

        # Limit conversations per day if specified
        if conversations_per_day is not None and conversations_per_day > 0:
            filtered_conversations = filtered_conversations[:conversations_per_day]

        logger.info(
            f"Found {len(filtered_conversations)} conversations for {tenant_name}"
        )

        # Update global progress
        with progress_lock:
            global_progress["total_conversations"] += len(filtered_conversations)
            global_progress["tenants_progress"][tenant_name] = {
                "total": len(filtered_conversations),
                "completed": 0,
                "failed": 0,
            }

        # Export conversations in parallel
        max_parallel = config.get("performance_settings", {}).get(
            "max_parallel_conversations", 10
        )
        results = []

        with concurrent.futures.ThreadPoolExecutor(
            max_workers=max_parallel
        ) as executor:
            future_to_conv = {
                executor.submit(
                    export_conversation_with_go_workspace,
                    conv_data,
                    tenant_name,
                    output_base_dir,
                    config,
                    from_date,
                    to_date,
                ): conv_data[0]
                for conv_data in filtered_conversations  # Use conversation data tuples
            }

            for future in concurrent.futures.as_completed(future_to_conv):
                conv_id = future_to_conv[future]
                try:
                    result = future.result()
                    results.append(result)

                    # Update tenant progress
                    with progress_lock:
                        if result["success"]:
                            global_progress["tenants_progress"][tenant_name][
                                "completed"
                            ] += 1
                        else:
                            global_progress["tenants_progress"][tenant_name][
                                "failed"
                            ] += 1

                    # Log progress every 10 conversations
                    if len(results) % 10 == 0:
                        log_progress()

                except Exception as e:
                    logger.error(f"Exception in conversation {conv_id}: {e}")
                    results.append(
                        {
                            "success": False,
                            "conversation_id": conv_id,
                            "tenant": tenant_name,
                            "error": str(e),
                        }
                    )

        successful = sum(1 for r in results if r["success"])
        failed = len(results) - successful

        logger.info(
            f"✅ Completed tenant {tenant_name}: {successful} successful, {failed} failed"
        )

        return {
            "tenant": tenant_name,
            "total": len(filtered_conversations),
            "successful": successful,
            "failed": failed,
            "results": results,
        }

    except Exception as e:
        logger.error(f"❌ Failed to export tenant {tenant_name}: {e}")
        return {
            "tenant": tenant_name,
            "error": str(e),
            "total": 0,
            "successful": 0,
            "failed": 0,
        }


def main():
    """Main export function."""
    parser = argparse.ArgumentParser(
        description="Export  agent conversations with Go blob fetcher"
    )
    parser.add_argument(
        "--config",
        default="configs/export_agent_conversations.json",
        help="Configuration file path",
    )
    parser.add_argument(
        "--tenants", nargs="+", help="Specific tenants to export (default: all)"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be exported without doing it",
    )

    args = parser.parse_args()

    # Load configuration
    config_path = Path(args.config)
    if not config_path.is_absolute():
        config_path = export_system_root / config_path

    with open(config_path, "r") as f:
        config = json.load(f)

    # Setup output directory
    output_base_dir = export_system_root / config["output_settings"]["base_directory"]
    output_base_dir.mkdir(exist_ok=True)

    # Get tenants to process
    if args.tenants:
        tenants = args.tenants
    else:
        tenants = config["tenants"]["all_vanguard_tenants"]

    logger.info("🚀 Starting last week agent conversation export")
    logger.info(
        f"📅 Date range: {config['date_range']['from_date']} to {config['date_range']['to_date']}"
    )
    logger.info(f"🏢 Tenants: {tenants}")
    logger.info(f"📁 Output directory: {output_base_dir}")

    if args.dry_run:
        logger.info("🔍 DRY RUN - No actual export will be performed")
        return

    # Initialize progress tracking
    global_progress["start_time"] = time.time()

    # Export tenants in parallel
    max_parallel_tenants = config.get("performance_settings", {}).get(
        "max_parallel_tenants", 4
    )
    tenant_results = []

    with concurrent.futures.ThreadPoolExecutor(
        max_workers=max_parallel_tenants
    ) as executor:
        future_to_tenant = {
            executor.submit(
                export_tenant_conversations,
                tenant,
                config["date_range"],
                config["daily_settings"]["conversations_per_tenant_per_day"],
                output_base_dir,
                config,
            ): tenant
            for tenant in tenants
        }

        for future in concurrent.futures.as_completed(future_to_tenant):
            tenant = future_to_tenant[future]
            try:
                result = future.result()
                tenant_results.append(result)
                logger.info(f"🏁 Finished tenant {tenant}")
                log_progress()
            except Exception as e:
                logger.error(f"Exception in tenant {tenant}: {e}")
                tenant_results.append(
                    {
                        "tenant": tenant,
                        "error": str(e),
                        "total": 0,
                        "successful": 0,
                        "failed": 0,
                    }
                )

    # Final summary
    total_conversations = sum(r.get("total", 0) for r in tenant_results)
    total_successful = sum(r.get("successful", 0) for r in tenant_results)
    total_failed = sum(r.get("failed", 0) for r in tenant_results)

    elapsed = time.time() - global_progress["start_time"]

    logger.info("🎉 EXPORT COMPLETE!")
    logger.info(f"📊 Total conversations: {total_conversations}")
    logger.info(f"✅ Successful: {total_successful}")
    logger.info(f"❌ Failed: {total_failed}")
    logger.info(f"⏱️  Total time: {datetime.timedelta(seconds=int(elapsed))}")
    logger.info(f"📦 Total blobs processed: {global_progress['completed_blobs']}")

    # Save final results
    results_file = output_base_dir / "export_results.json"
    with open(results_file, "w") as f:
        json.dump(
            {
                "config": config,
                "start_time": global_progress["start_time"],
                "end_time": time.time(),
                "duration": elapsed,
                "tenant_results": tenant_results,
                "summary": {
                    "total_conversations": total_conversations,
                    "successful": total_successful,
                    "failed": total_failed,
                    "total_blobs": global_progress["total_blobs"],
                    "completed_blobs": global_progress["completed_blobs"],
                },
            },
            f,
            indent=2,
        )

    logger.info(f"📄 Results saved to {results_file}")


if __name__ == "__main__":
    main()
