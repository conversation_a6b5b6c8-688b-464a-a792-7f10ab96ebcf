#!/usr/bin/env python3
"""
Package finished conversations into a clean structure.

This script:
1. Recursively searches for all conversations in a root directory
2. Copies them to a clean output directory (just conversations, no tenant/date info)
3. Removes only blob_ids.json from each conversation (preserves conversation.json and initial_workspace/)
4. Packages everything into a single tar file
"""

import argparse
import json
import logging
import shutil
import tarfile
import tempfile
from pathlib import Path
from typing import List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def find_all_conversations(root_dir: Path) -> List[Path]:
    """Find all conversation directories recursively."""
    conversations = []

    # Look for directories matching pattern conversation_*
    for conv_dir in root_dir.rglob("conversation_*"):
        if conv_dir.is_dir():
            # Verify it has conversation.json
            if (conv_dir / "conversation.json").exists():
                conversations.append(conv_dir)
                logger.debug(f"Found conversation: {conv_dir}")

    logger.info(f"Found {len(conversations)} conversations")
    return conversations


def clean_conversation(src_conv_dir: Path, dst_conv_dir: Path) -> bool:
    """
    Copy conversation to destination and clean up unnecessary files.

    Returns True if successful, False otherwise.
    """
    try:
        # Copy entire conversation directory
        shutil.copytree(src_conv_dir, dst_conv_dir)

        # Only remove blob_ids.json from conversation directories
        # Keep conversation.json and initial_workspace/ intact
        # Note: metadata.json and README.md are at export level, not conversation level
        blob_ids_file = dst_conv_dir / "blob_ids.json"
        if blob_ids_file.exists():
            blob_ids_file.unlink()
            logger.debug(f"Removed {blob_ids_file}")
        else:
            logger.debug(f"No blob_ids.json found in {dst_conv_dir}")

        # Check if we have the essential files
        if not (dst_conv_dir / "conversation.json").exists():
            logger.warning(f"No conversation.json in {dst_conv_dir}")
            return False

        # Check if we have workspace
        workspace_dir = dst_conv_dir / "initial_workspace"
        if not workspace_dir.exists():
            logger.warning(f"No initial_workspace in {dst_conv_dir}")
            return False

        return True

    except Exception as e:
        logger.error(f"Failed to clean conversation {src_conv_dir}: {e}")
        return False


def package_conversations(root_dir: Path, output_file: Path) -> Tuple[int, int]:
    """
    Package all conversations from root_dir into a tar file.

    Returns (successful_count, total_count)
    """
    conversations = find_all_conversations(root_dir)
    if not conversations:
        logger.error("No conversations found")
        return 0, 0

    successful_count = 0

    # Create temporary directory for clean conversations
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        clean_conversations_dir = temp_path / "conversations"
        clean_conversations_dir.mkdir()

        logger.info(f"Processing {len(conversations)} conversations...")

        # Process each conversation
        for i, conv_dir in enumerate(conversations, 1):
            conv_id = conv_dir.name
            dst_conv_dir = clean_conversations_dir / conv_id

            logger.info(f"[{i}/{len(conversations)}] Processing {conv_id}")

            if clean_conversation(conv_dir, dst_conv_dir):
                successful_count += 1
            else:
                logger.warning(f"Failed to process {conv_id}")
                # Remove failed conversation directory if it exists
                if dst_conv_dir.exists():
                    shutil.rmtree(dst_conv_dir)

        logger.info(
            f"Successfully processed {successful_count}/{len(conversations)} conversations"
        )

        # Create tar file
        logger.info(f"Creating tar file: {output_file}")
        with tarfile.open(output_file, "w") as tar:
            # Add all conversations to tar
            for conv_dir in clean_conversations_dir.iterdir():
                if conv_dir.is_dir():
                    tar.add(conv_dir, arcname=conv_dir.name)

        logger.info(f"✅ Package created: {output_file}")
        logger.info(f"📊 Size: {output_file.stat().st_size / (1024*1024):.1f} MB")

    return successful_count, len(conversations)


def add_conversation_to_tar(
    tar: tarfile.TarFile, conv_path: Path, arcname: str, delete_after: bool = False
) -> bool:
    """
    Add a conversation directly to tar file, excluding blob_ids.json.

    Args:
        tar: The tar file object
        conv_path: Path to the conversation directory
        arcname: Archive name for the conversation
        delete_after: Whether to delete the conversation directory after adding to tar

    Returns True if successful, False otherwise.
    """
    try:
        for item in conv_path.iterdir():
            # Skip blob_ids.json files
            if item.name == "blob_ids.json":
                logger.debug(f"Skipping {item}")
                continue

            item_arcname = f"{arcname}/{item.name}"

            if item.is_file():
                tar.add(item, arcname=item_arcname)
            elif item.is_dir():
                tar.add(item, arcname=item_arcname)

        # Delete the conversation directory if requested
        if delete_after:
            try:
                # Remove the entire conversation directory
                shutil.rmtree(conv_path)
                logger.debug(f"Deleted {conv_path} after adding to tar")
            except Exception as e:
                logger.warning(f"Failed to delete {conv_path}: {e}")

        return True
    except Exception as e:
        logger.error(f"Failed to add conversation {conv_path.name} to tar: {e}")
        return False


def package_conversations_chunked(
    root_dir: Path,
    output_dir: Path,
    num_chunks: int = 10,
    compress: bool = False,
    delete_after: bool = True,
) -> Tuple[int, int]:
    """
    Package conversations into multiple chunked tar files.

    This method splits conversations into multiple smaller archives to avoid
    disk space issues and make processing more manageable.

    Returns (successful_count, total_count)
    """
    conversations = find_all_conversations(root_dir)
    if not conversations:
        logger.error("No conversations found")
        return 0, 0

    total_conversations = len(conversations)
    chunk_size = (
        total_conversations + num_chunks - 1
    ) // num_chunks  # Ceiling division

    logger.info(
        f"Splitting {total_conversations} conversations into {num_chunks} chunks of ~{chunk_size} each"
    )

    # Determine file extension
    extension = ".tar.gz" if compress else ".tar"
    tar_mode = "w:gz" if compress else "w"

    successful_count = 0

    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)

    # Process each chunk
    for chunk_idx in range(num_chunks):
        start_idx = chunk_idx * chunk_size
        end_idx = min(start_idx + chunk_size, total_conversations)

        if start_idx >= total_conversations:
            break

        chunk_conversations = conversations[start_idx:end_idx]
        chunk_file = (
            output_dir
            / f"conversations_chunk_{chunk_idx + 1:02d}_of_{num_chunks:02d}{extension}"
        )

        logger.info(
            f"📦 Creating chunk {chunk_idx + 1}/{num_chunks}: {chunk_file.name}"
        )
        logger.info(
            f"   Conversations {start_idx + 1}-{end_idx} ({len(chunk_conversations)} conversations)"
        )

        try:
            with tarfile.open(chunk_file, tar_mode) as tar:
                for i, conv_path in enumerate(chunk_conversations, 1):
                    global_idx = start_idx + i
                    logger.info(
                        f"[{global_idx}/{total_conversations}] Adding {conv_path.name}"
                    )

                    # Add conversation directly to tar, excluding blob_ids.json
                    arcname = conv_path.name
                    success = add_conversation_to_tar(
                        tar, conv_path, arcname, delete_after=delete_after
                    )

                    if success:
                        successful_count += 1
                    else:
                        logger.warning(f"Failed to add {conv_path.name}")

            logger.info(f"✅ Chunk {chunk_idx + 1} created: {chunk_file}")
            logger.info(f"📊 Size: {chunk_file.stat().st_size / (1024*1024):.1f} MB")

        except Exception as e:
            logger.error(f"Failed to create chunk {chunk_idx + 1}: {e}")
            # Continue with next chunk instead of failing completely
            continue

    logger.info("🎉 Chunked packaging complete!")
    logger.info(f"📊 Total successful: {successful_count}/{total_conversations}")

    return successful_count, total_conversations


def main():
    parser = argparse.ArgumentParser(
        description="Package finished conversations into a clean tar file"
    )
    parser.add_argument(
        "root_dir", type=Path, help="Root directory to search for conversations"
    )
    parser.add_argument(
        "-o", "--output", type=Path, help="Output tar file path or directory for chunks"
    )
    parser.add_argument(
        "-v", "--verbose", action="store_true", help="Enable verbose logging"
    )
    parser.add_argument(
        "--chunks",
        type=int,
        default=0,
        help="Split into N chunks (default: 0 = single file)",
    )
    parser.add_argument(
        "--compress", action="store_true", help="Compress tar files with gzip"
    )
    parser.add_argument(
        "--delete-after",
        action="store_true",
        default=True,
        help="Delete conversation directories after adding to tar (default: True, saves disk space)",
    )
    parser.add_argument(
        "--no-delete-after",
        dest="delete_after",
        action="store_false",
        help="Keep conversation directories after adding to tar",
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Validate input
    if not args.root_dir.exists():
        logger.error(f"Root directory does not exist: {args.root_dir}")
        return 1

    if not args.root_dir.is_dir():
        logger.error(f"Root path is not a directory: {args.root_dir}")
        return 1

    # Set default output
    if not args.output:
        timestamp = args.root_dir.name
        if args.chunks > 0:
            args.output = args.root_dir.parent / f"conversations_chunks_{timestamp}"
        else:
            suffix = ".tar.gz" if args.compress else ".tar"
            args.output = (
                args.root_dir.parent / f"conversations_package_{timestamp}{suffix}"
            )

    logger.info("🚀 Starting conversation packaging")
    logger.info(f"📁 Root directory: {args.root_dir}")

    if args.chunks > 0:
        logger.info(f"📦 Output directory: {args.output}")
        logger.info(f"🔢 Chunks: {args.chunks}")
        logger.info(f"🗜️ Compression: {'enabled' if args.compress else 'disabled'}")
        logger.info(f"🗑️ Delete after: {'enabled' if args.delete_after else 'disabled'}")

        # Ensure output directory exists
        args.output.mkdir(parents=True, exist_ok=True)

        try:
            successful, total = package_conversations_chunked(
                args.root_dir,
                args.output,
                args.chunks,
                args.compress,
                args.delete_after,
            )
        except Exception as e:
            logger.error(f"Failed to package conversations in chunks: {e}")
            return 1
    else:
        logger.info(f"📦 Output file: {args.output}")
        if args.compress:
            logger.info("🗜️ Compression: enabled")

        # Ensure output directory exists
        args.output.parent.mkdir(parents=True, exist_ok=True)

        try:
            successful, total = package_conversations(args.root_dir, args.output)
        except Exception as e:
            logger.error(f"Failed to package conversations: {e}")
            return 1

    if successful == 0:
        logger.error("No conversations were successfully packaged")
        return 1
    elif successful < total:
        logger.warning(
            f"Only {successful}/{total} conversations were successfully packaged"
        )
        return 0
    else:
        logger.info(f"🎉 All {successful} conversations successfully packaged!")
        return 0


if __name__ == "__main__":
    exit(main())
