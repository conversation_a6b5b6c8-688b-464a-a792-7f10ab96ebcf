{"date_range": {"from_date": "2025-05-25", "to_date": "2025-05-26"}, "tenants": {"all_vanguard_tenants": ["i0-vanguard0", "i1-vanguard4"]}, "daily_settings": {"conversations_per_tenant_per_day": 10, "process_days_sequentially": true, "max_retries_per_day": 2}, "output_settings": {"base_directory": "test_export_output"}, "filters": {"min_turns": 3, "max_blobs_per_conversation": 1000, "skip_welcome_conversations": false, "agent_conversations_only": true}, "performance_settings": {"max_workers_per_conversation": 4, "max_parallel_conversations": 2, "max_parallel_tenants": 2, "use_go_blob_fetcher": true}, "go_blob_fetcher_settings": {"max_workers": 8, "timeout": 300, "enable_workspace_reconstruction": true}}