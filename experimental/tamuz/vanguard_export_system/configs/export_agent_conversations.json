{"date_range": {"from_date": "2025-05-20", "to_date": "2025-06-01"}, "tenants": {"all_vanguard_tenants": ["i0-vanguard0", "i0-vanguard1", "i0-vanguard2", "i0-vanguard3", "i0-vanguard4", "i0-vanguard5", "i0-vanguard6", "i0-vanguard7", "i1-vanguard0", "i1-vanguard1", "i1-vanguard2", "i1-vanguard3", "i1-vanguard4", "i1-vanguard5", "i1-vanguard6", "i1-vanguard7"]}, "daily_settings": {"conversations_per_tenant_per_day": 5000, "process_days_sequentially": true, "max_retries_per_day": 2}, "output_settings": {"base_directory": "/mnt/efs/augment/user/tamuz/vanguard_05_20_to_06_01_agent_export"}, "filters": {"min_turns": 1, "max_blobs_per_conversation": 100000, "skip_welcome_conversations": false, "agent_conversations_only": true}, "performance_settings": {"max_workers_per_conversation": 100, "max_parallel_conversations": 10, "max_parallel_tenants": 4, "use_go_blob_fetcher": true}, "go_blob_fetcher_settings": {"max_workers": 50, "timeout": 3600, "enable_workspace_reconstruction": true}}