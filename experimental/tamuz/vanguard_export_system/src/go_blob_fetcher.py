"""
Go-based blob fetcher integration for the Vanguard export system.
This module provides a Python interface to the Go blob fetcher implementation.
"""

import json
import logging
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Any

from base.datasets.tenants import DatasetTenant

logger = logging.getLogger(__name__)


class GoBlobFetcher:
    """Python wrapper for the Go blob fetcher implementation."""

    def __init__(self, go_binary_path: Optional[Path] = None):
        """Initialize the Go blob fetcher.

        Args:
            go_binary_path: Path to the Go binary. If None, will look for it in the expected location.
        """
        if go_binary_path is None:
            # Default path relative to this file
            current_dir = Path(__file__).parent
            go_binary_path = (
                current_dir.parent.parent / "blob_fetcher_go" / "blob-fetcher"
            )

        self.go_binary_path = go_binary_path

        if not self.go_binary_path.exists():
            raise RuntimeError(f"Go binary not found at {self.go_binary_path}")

    def fetch_blob_metadata_batch(
        self, blob_ids: List[str], tenant: DatasetTenant, max_workers: int = 50
    ) -> Dict[str, str]:
        """Fetch metadata for multiple blobs in parallel using Go implementation.

        Args:
            blob_ids: List of blob IDs to fetch metadata for
            tenant: Tenant configuration
            max_workers: Maximum number of concurrent workers

        Returns:
            Dictionary mapping blob_id to filename for blobs with valid filenames
        """
        logger.info(
            f"📋 Fetching GCS metadata for {len(blob_ids)} blobs using Go implementation..."
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create blob IDs file
            blob_ids_file = temp_path / "blob_ids.json"
            with open(blob_ids_file, "w") as f:
                json.dump(blob_ids, f)

            # Create output directory (not used for metadata-only)
            output_dir = temp_path / "output"
            output_dir.mkdir()

            # Run Go fetcher for metadata only
            result = self._run_go_fetcher(
                blob_ids_file=blob_ids_file,
                tenant_name=tenant.name,
                output_dir=output_dir,
                max_workers=max_workers,
                metadata_only=True,
            )

            if not result:
                logger.error("Go blob fetcher failed")
                return {}

            # Extract filenames from metadata
            blob_filenames = {}
            blob_metadata = result.get("blob_metadata") or []
            for metadata in blob_metadata:
                blob_id = metadata["blob_id"]
                filename = metadata["filename"]

                # Only include blobs with valid filenames (not "unknown/...")
                if filename and not filename.startswith("unknown/"):
                    blob_filenames[blob_id] = filename

            logger.info(
                f"Successfully fetched metadata for {len(blob_filenames)} blobs"
            )

            if result.get("errors"):
                logger.warning(
                    f"Encountered {len(result['errors'])} errors during metadata fetch"
                )
                for error in result["errors"][:5]:  # Log first 5 errors
                    logger.debug(f"Metadata fetch error: {error}")

            return blob_filenames

    def download_blobs_with_go(
        self,
        blob_ids: List[str],
        tenant: DatasetTenant,
        output_dir: Path,
        max_workers: int = 50,
    ) -> Dict[str, Any]:
        """Download blobs using Go implementation.

        Args:
            blob_ids: List of blob IDs to download
            tenant: Tenant configuration
            output_dir: Directory to download blobs to
            max_workers: Maximum number of concurrent workers

        Returns:
            Dictionary with download statistics
        """
        logger.info(f"Downloading {len(blob_ids)} blobs using Go implementation...")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create blob IDs file
            blob_ids_file = temp_path / "blob_ids.json"
            with open(blob_ids_file, "w") as f:
                json.dump(blob_ids, f)

            # Run Go fetcher for download
            result = self._run_go_fetcher(
                blob_ids_file=blob_ids_file,
                tenant_name=tenant.name,
                output_dir=output_dir,
                max_workers=max_workers,
                metadata_only=False,
            )

            if not result:
                logger.error("Go blob fetcher failed")
                return {"error": "Go blob fetcher failed"}

            # Return statistics in format compatible with existing system
            return {
                "total_requested": result.get("total_requested", 0),
                "successful": result.get("successful", 0),
                "failed": result.get("failed", 0),
                "duration": result.get("duration", 0),
                "speed": result.get("speed", 0),
                "errors": result.get("errors", []),
            }

    def _run_go_fetcher(
        self,
        blob_ids_file: Path,
        tenant_name: str,
        output_dir: Path,
        max_workers: int = 50,
        metadata_only: bool = False,
        timeout: int = 3600,
    ) -> Optional[Dict[str, Any]]:
        """Run the Go blob fetcher and return the result.

        Args:
            blob_ids_file: Path to file containing blob IDs
            tenant_name: Name of the tenant
            output_dir: Output directory
            max_workers: Maximum number of workers
            metadata_only: Whether to only fetch metadata
            timeout: Timeout in seconds

        Returns:
            Dictionary with results or None if failed
        """
        # Build command
        cmd = [
            str(self.go_binary_path),
            "--blob-ids",
            str(blob_ids_file),
            "--tenant",
            tenant_name,
            "--output",
            str(output_dir),
            "--workers",
            str(max_workers),
        ]

        if metadata_only:
            cmd.append("--metadata-only")

        logger.debug(f"Running Go fetcher: {' '.join(cmd)}")

        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=timeout
            )

            if result.returncode != 0:
                logger.error(f"Go fetcher failed with return code {result.returncode}")
                logger.error(f"STDERR: {result.stderr}")
                return None

            # Parse JSON output
            try:
                return json.loads(result.stdout)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Go fetcher JSON output: {e}")
                logger.error(f"STDOUT: {result.stdout}")
                return None

        except subprocess.TimeoutExpired:
            logger.error(f"Go fetcher timed out after {timeout} seconds")
            return None
        except Exception as e:
            logger.error(f"Error running Go fetcher: {e}")
            return None


def reorganize_downloaded_files(
    output_dir: Path, blob_filenames: Optional[Dict[str, str]] = None
):
    """Reorganize downloaded files based on blob_filenames mapping.

    This function is compatible with the existing reorganize_downloaded_files function.
    """
    if not blob_filenames:
        return

    logger.info("Reorganizing files based on original paths...")

    for blob_id, target_path in blob_filenames.items():
        source_file = output_dir / blob_id
        if source_file.exists():
            target_file = output_dir / target_path
            target_file.parent.mkdir(parents=True, exist_ok=True)

            # Move file to correct location
            source_file.rename(target_file)


# Convenience functions that match the existing API
def get_blob_metadata_batch_go(
    blob_ids: List[str], tenant: DatasetTenant, max_workers: int = 50
) -> Dict[str, str]:
    """Get metadata for multiple blobs using Go implementation.

    This function provides the same interface as the existing get_blob_metadata_batch
    but uses the Go implementation for better performance.
    """
    fetcher = GoBlobFetcher()
    return fetcher.fetch_blob_metadata_batch(blob_ids, tenant, max_workers)


def download_blobs_with_go_fetcher(
    blob_ids: List[str], tenant: DatasetTenant, output_dir: Path, max_workers: int = 50
) -> Dict[str, Any]:
    """Download blobs using Go implementation.

    This function provides a similar interface to the existing gsutil-based download
    but uses the Go implementation.
    """
    fetcher = GoBlobFetcher()
    return fetcher.download_blobs_with_go(blob_ids, tenant, output_dir, max_workers)
