#!/usr/bin/env python3
"""
Export agent conversations with complete workspace state.

This script exports agent conversations from a specified tenant over a given time period,
along with the complete workspace state at both the beginning and end of the conversation.

Key features:
1. Extracts the workspace ID from the conversation
2. Queries the Workspace Manager to get all blobs in the workspace
3. Downloads the content of each blob to reconstruct the complete workspace
4. Tracks changes to the workspace throughout the conversation
5. Provides initial snapshot of the workspace
6. Works for all conversations, even those without file operations
7. Reconstructs the workspace state by tracking blob additions and deletions

If a conversation doesn't meet the minimum turns threshold, the script will keep randomly
selecting conversations until it finds enough that meet the criteria.
"""

import argparse
import datetime
import json as json_module
import json
import logging
import os
import random
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from google.cloud import storage
from base.datasets.tenants import DatasetTenant, get_tenant

# Use a simple progress indicator instead of tqdm
# from tqdm import tqdm
from services.chat_host.chat_pb2 import ChatRequest
from experimental.tamuz.agent.analytics.big_query_utils import (
    get_agent_conv_request_ids_and_turn_counts,
)
from experimental.tamuz.agent.analytics.conversation import Conversation

from experimental.tamuz.utils.ri_utils import (
    get_chat_host_request_factory,
    get_chat_host_request_full_factory,
    get_chat_host_response_factory,
)

# Try to import ContentManagerClient for workspace manager integration
# If imports fail, we'll use a fallback approach
try:
    from services.content_manager.client.content_manager_client import (
        ContentManagerClient,
    )
    from services.lib.request_context.request_context import RequestContext
    from base.python.grpc import client_options

    WORKSPACE_MANAGER_AVAILABLE = True
except ImportError as e:
    logger = logging.getLogger(__name__)
    logger.warning(f"Could not import workspace manager modules: {e}")
    logger.warning("Workspace manager integration will not be available")
    WORKSPACE_MANAGER_AVAILABLE = False

# Note: In a production environment, you would need to import these modules
# from services.lib.tls import tls_config
# import pydantic


# Define a decorator for timing functions
def timed_function(func):
    """Decorator to time function execution."""

    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.debug(
            f"Function {func.__name__} took {end_time - start_time:.2f} seconds"
        )
        return result

    return wrapper


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


# Add a verbose argument to enable debug logging
def set_log_level(verbose=False):
    """Set the log level based on the verbose flag."""
    if verbose:
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Export agent conversations with complete workspace state"
    )
    parser.add_argument("-t", "--tenant", required=True, help="Tenant name to process")
    parser.add_argument(
        "-n",
        "--num-conversations",
        type=int,
        default=5,
        help="Number of random conversations to export",
    )
    parser.add_argument(
        "-o",
        "--output-dir",
        default="./exported_conversations",
        help="Directory to save exported conversations",
    )
    parser.add_argument(
        "--from-date",
        type=lambda s: datetime.datetime.fromisoformat(s),
        help="Start date for the time range (ISO format)",
    )
    parser.add_argument(
        "--to-date",
        type=lambda s: datetime.datetime.fromisoformat(s),
        help="End date for the time range (ISO format)",
    )
    parser.add_argument(
        "--last-days",
        type=int,
        help="Number of days to look back from today",
    )
    parser.add_argument(
        "--request-id",
        help="Specific request ID to export",
    )
    parser.add_argument(
        "--min-turns",
        type=int,
        default=0,
        help="Minimum number of agent turns required for a conversation to be included",
    )

    parser.add_argument(
        "--max-blobs",
        type=int,
        default=3000,
        help="Maximum number of file blobs in the workspace",
    )

    parser.add_argument(
        "-v",
        "--verbose",
        action="store_true",
        help="Enable verbose (debug) logging",
    )

    parser.add_argument(
        "--parallel-workers",
        type=int,
        default=20,
        help="Number of parallel workers for downloading blobs (default: 20)",
    )

    parser.add_argument(
        "--optimize-binary",
        action="store_true",
        help="Optimize binary file downloads by only downloading partial content for large binary files",
    )

    parser.add_argument(
        "--generate-changelog",
        action="store_true",
        help="Generate a changelog of blob additions and deletions throughout the conversation",
    )

    parser.add_argument(
        "--blob-only",
        action="store_true",
        help="Only save blob IDs and metadata without downloading full content (optimized for large exports)",
    )

    parser.add_argument(
        "--exclude-files",
        nargs="*",
        default=[
            "README.md",
            "readme.txt",
            "LICENSE",
            "CHANGELOG.md",
            ".gitignore",
            "package-lock.json",
        ],
        help="File patterns to exclude from export (default: README.md, readme.txt, LICENSE, CHANGELOG.md, .gitignore, package-lock.json)",
    )

    parser.add_argument(
        "--skip-welcome-conversations",
        action="store_true",
        help="Skip conversations that contain only welcome messages",
    )

    return parser.parse_args()


def process_request_id(
    request_id,
    _get_chat_host_request,
    _get_chat_host_response,
    _get_chat_host_request_full=None,
):
    """Process a single request ID to get the conversation."""
    try:
        start_time = datetime.datetime.now()
        logger.debug(f"Processing request ID: {request_id}")

        # Get the chat host request and response
        chat_request = _get_chat_host_request(request_id=request_id)

        chat_response = _get_chat_host_response(request_id=request_id)

        if not chat_request or not chat_response:
            logger.warning(f"Could not find chat request or response for {request_id}")
            return None

        # Convert to conversation
        conversation = Conversation.from_chat_request(
            request_id=request_id,
            chat_request=chat_request,
            chat_response=chat_response,
            get_chat_host_request_func=_get_chat_host_request,
            get_chat_host_request_full_func=_get_chat_host_request_full,
        )

        end_time = datetime.datetime.now()
        logger.debug(f"Processed request ID: {request_id} in {end_time - start_time}")

        return conversation

    except Exception as e:
        logger.error(f"Error processing request ID {request_id}: {e}", exc_info=True)
        return None


def serialize_conversation(conversation: Conversation) -> Dict[str, Any]:
    """Serialize a conversation to a dictionary."""
    serialized = {
        "last_request_id": conversation.last_request_id,
        "agent_rounds": [],
        "metadata": {
            "model_name": conversation.metadata.model_name,
            "user_guidelines": conversation.metadata.user_guidelines,
            "workspace_guidelines": conversation.metadata.workspace_guidelines,
            "agent_memories": conversation.metadata.agent_memories,
            "tool_definitions": conversation.metadata.tool_definitions,
        },
    }

    for round_idx, agent_round in enumerate(conversation.agent_rounds):
        serialized_round = {
            "round_idx": round_idx,
            "user_message": agent_round.user_message,
            "user_message_request_id": agent_round.user_message_request_id,
            "ide_state": agent_round.ide_state,
            "agent_turns": [],
        }

        # Add selected code context if available
        if (
            hasattr(agent_round, "selected_code_context")
            and agent_round.selected_code_context
        ):
            context = agent_round.selected_code_context
            serialized_round["selected_code_context"] = {
                "path": context.path,
                "selected_code": context.selected_code,
                "prefix": context.prefix,
                "suffix": context.suffix,
                "lang": context.lang,
            }

        # Add system prompt components if available
        if (
            hasattr(agent_round, "system_prompt_components")
            and agent_round.system_prompt_components
        ):
            components = agent_round.system_prompt_components
            serialized_round["system_prompt_components"] = {
                "user_guidelines": components.user_guidelines,
                "workspace_guidelines": components.workspace_guidelines,
                "agent_memories": components.agent_memories,
                "persona_type": components.persona_type,
            }

        # Add repository context if available
        if (
            hasattr(agent_round, "repository_context")
            and agent_round.repository_context
        ):
            repo_context = agent_round.repository_context
            serialized_round["repository_context"] = {
                "diff": repo_context.diff,
                "relevant_commit_messages": repo_context.relevant_commit_messages,
                "example_commit_messages": repo_context.example_commit_messages,
                "changed_file_stats": repo_context.changed_file_stats,
            }

        # Add raw tokenization text if available
        if (
            hasattr(agent_round, "raw_tokenization_text")
            and agent_round.raw_tokenization_text
        ):
            serialized_round["raw_tokenization_text"] = (
                agent_round.raw_tokenization_text
            )

        for turn_idx, turn in enumerate(agent_round.agent_turns):
            serialized_turn = {
                "turn_idx": turn_idx,
                "message": turn.message,
                "request_id": turn.request_id,
                "message_token_count": getattr(turn, "message_token_count", 0),
            }

            if turn.tool_call:
                serialized_turn["tool_call"] = {
                    "tool_name": turn.tool_call.tool_use.name
                    if turn.tool_call.tool_use
                    else None,
                    "tool_input": turn.tool_call.tool_use.input
                    if turn.tool_call.tool_use
                    else None,
                }

                if turn.tool_call.tool_result:
                    serialized_turn["tool_call"]["tool_result"] = {
                        "content": turn.tool_call.tool_result.content,
                        "is_error": turn.tool_call.tool_result.is_error,
                        "request_id": turn.tool_call.tool_result.request_id,
                    }

            serialized_round["agent_turns"].append(serialized_turn)

        serialized["agent_rounds"].append(serialized_round)

    return serialized


@dataclass
class WorkspaceBlobs:
    """Container for initial blob information from a conversation."""

    checkpoint_id: Optional[str] = None
    added_blobs: List[str] = field(default_factory=list)
    deleted_blobs: List[str] = field(default_factory=list)


def extract_blobs_from_request(request: Optional[ChatRequest]) -> WorkspaceBlobs:
    """Extract the initial blob IDs from a initial_request.

    Args:
        request: The ChatRequest object containing blob information

    Returns:
        An WorkspaceBlobs object containing checkpoint_id, added_blobs, deleted_blobs
    """
    result = WorkspaceBlobs()

    # If request is None, return empty result
    if request is None:
        logger.debug("Request is None")
        return result

    # Check if the request has blobs field
    if not hasattr(request, "blobs") or not request.blobs:
        logger.debug("No blobs found in request")
        return result

    # Process each Blobs object in the request
    for blobs in request.blobs:
        # Extract checkpoint ID if available
        if hasattr(blobs, "baseline_checkpoint_id") and blobs.baseline_checkpoint_id:
            result.checkpoint_id = blobs.baseline_checkpoint_id

        # Extract added blobs
        if hasattr(blobs, "added") and blobs.added:
            for blob_id in blobs.added:
                hex_blob_id = blob_id.hex()
                result.added_blobs.append(hex_blob_id)

                # Try to extract filename from blob metadata
                # Note: The actual metadata extraction will happen in download_blob_from_gcs
                # This is just a placeholder for future direct metadata extraction if available

        # Extract deleted blobs
        if hasattr(blobs, "deleted") and blobs.deleted:
            result.deleted_blobs.extend(blob_id.hex() for blob_id in blobs.deleted)

    logger.debug(
        f"Extracted blobs: checkpoint={result.checkpoint_id}, "
        f"added={len(result.added_blobs)}, deleted={len(result.deleted_blobs)}, "
    )

    return result


def get_blob_ids_from_checkpoint(
    checkpoint_id: Optional[str], tenant: DatasetTenant
) -> List[str]:
    """Get blob IDs from a checkpoint.

    Args:
        checkpoint_id: The checkpoint ID to get blob IDs from
        tenant: The tenant configuration

    Returns:
        A list of blob IDs from the checkpoint
    """
    blob_ids = []

    if not checkpoint_id:
        return blob_ids

    try:
        storage_client = storage.Client(project=tenant.project_id)
        checkpoint_bucket = storage_client.bucket(tenant.checkpoint_bucket_name)
        checkpoint_path = f"{tenant.checkpoint_bucket_prefix}/{checkpoint_id}"
        checkpoint_blob = checkpoint_bucket.blob(checkpoint_path)
        if checkpoint_blob.exists():
            checkpoint_content = checkpoint_blob.download_as_text()
            checkpoint_blob_ids = json_module.loads(checkpoint_content)
            blob_ids.extend(checkpoint_blob_ids)

            logger.info(
                f"Retrieved {len(checkpoint_blob_ids)} blob IDs from checkpoint {checkpoint_id}"
            )
        else:
            logger.warning(
                f"Checkpoint {checkpoint_id} does not exist in bucket {tenant.checkpoint_bucket_name}"
            )
    except Exception as e:
        logger.warning(f"Failed to get checkpoint {checkpoint_id}: {e}")

    return blob_ids


@timed_function
def export_conversations_with_complete_workspace(
    from_date,
    to_date,
    tenant_name,
    num_conversations,
    output_dir,
    request_id=None,
    min_turns=0,
    max_blobs=3000,
    parallel_workers=20,
    optimize_binary=False,
    generate_changelog=False,
    blob_only=False,
    exclude_files=None,
    skip_welcome_conversations=False,
    add_timestamp=True,
):
    """Export random conversations with complete workspace state.

    This function exports agent conversations with both initial and final workspace states.
    It tracks file operations throughout the conversation and reconstructs the final
    workspace state by applying these operations to the initial state.

    Args:
        from_date: Start date for the time range
        to_date: End date for the time range
        tenant_name: Tenant name to process
        num_conversations: Number of random conversations to export
        output_dir: Directory to save exported conversations
        request_id: Specific request ID to export
        min_turns: Minimum number of agent turns required for a conversation to be included
        max_blobs: Maximum number of blobs to process per conversation
        parallel_workers: Number of parallel workers for downloading blobs
        optimize_binary: Whether to optimize binary file downloads
        generate_changelog: Whether to generate a changelog of blob additions and deletions
        blob_only: If True, only save blob metadata without downloading content
        exclude_files: List of file patterns to exclude from export
        skip_welcome_conversations: If True, skip conversations with only welcome messages

    Returns:
        Path to the exported directory, or None if no conversations were found
    """
    start_time = time.time()
    logger.info(
        f"Exporting {num_conversations} agent conversations from {from_date} to {to_date} for tenant {tenant_name}"
    )

    # Set default exclude patterns if none provided
    if exclude_files is None:
        exclude_files = []

    logger.info(f"Blob-only mode: {blob_only}")
    logger.info(f"Skip welcome conversations: {skip_welcome_conversations}")
    logger.info(f"Exclude file patterns: {exclude_files}")

    # Get tenant information
    tenant = get_tenant(tenant_name)

    logger.info("Fetching conversation data with request IDs and turn counts...")
    conversation_data = get_agent_conv_request_ids_and_turn_counts(
        tenant=tenant,
        from_datetime=from_date,
        to_datetime=to_date,
    )
    logger.info(f"Found {len(conversation_data)} agent conversations")

    if not conversation_data:
        logger.warning("No conversations found in the specified time range")
        return None

    if request_id:
        logger.info(f"Exporting specific conversation with request ID: {request_id}")
        selected_conversations = [
            conv for conv in conversation_data if conv[1] == request_id
        ]
        if not selected_conversations:
            logger.warning(f"Conversation with request ID {request_id} not found")
            return None
    else:
        filtered_conversations = []

        # Filter conversations based on turn count
        for conv_id, req_id, turn_count in conversation_data:
            if min_turns > 0 and turn_count < min_turns:
                continue
            filtered_conversations.append((conv_id, req_id, turn_count))

        logger.info(
            f"Found {len(filtered_conversations)} conversations with at least {min_turns} turns"
        )

        # If we have more conversations than needed, randomly select a subset
        if len(filtered_conversations) > num_conversations:
            selected_conversations = random.sample(
                filtered_conversations, num_conversations
            )
        else:
            selected_conversations = filtered_conversations

        logger.info(f"Selected {len(selected_conversations)} conversations")

    # Create output directory
    timestamp = datetime.datetime.now().strftime("%Y%m%dM")
    output_path = Path(output_dir) / f"export_{timestamp}"
    output_path.mkdir(parents=True, exist_ok=True)

    # Create conversations directory
    conversations_dir = output_path / "conversations"
    conversations_dir.mkdir(exist_ok=True)

    # No need to create blobs directory as it's always empty

    # Process each conversation
    processed_conversations = []
    for conv_id, req_id, turn_count in selected_conversations:
        try:
            # Get the conversation (we already verified it meets our criteria during selection)
            _get_chat_host_request = get_chat_host_request_factory(tenant_name)
            _get_chat_host_request_full = get_chat_host_request_full_factory(
                tenant_name
            )
            _get_chat_host_response = get_chat_host_response_factory(tenant_name)
            conversation = process_request_id(
                req_id,
                _get_chat_host_request,
                _get_chat_host_response,
                _get_chat_host_request_full,
            )

            if not conversation:
                logger.warning(f"Could not find conversation for request ID: {req_id}")
                continue

            # Serialize the conversation
            serialized_conv = serialize_conversation(conversation)
            serialized_conv["conversation_type"] = "agent"

            # Create a directory for this conversation
            conv_dir = conversations_dir / f"conversation_{req_id}"
            conv_dir.mkdir(exist_ok=True)

            # Create directories for initial and final workspace states
            initial_workspace_dir = conv_dir / "initial_workspace"
            # final_workspace_dir = conv_dir / "final_workspace"
            initial_workspace_dir.mkdir(exist_ok=True)
            # final_workspace_dir.mkdir(exist_ok=True)

            # Save conversation data
            with open(conv_dir / "conversation.json", "w", encoding="utf-8") as f:
                json_module.dump(serialized_conv, f, indent=2, ensure_ascii=False)

            initial_request = _get_chat_host_request(
                request_id=conversation.agent_rounds[0].user_message_request_id
            )
            initial_workspace_blobs = extract_blobs_from_request(initial_request)

            initial_blob_ids = get_blob_ids_from_checkpoint(
                initial_workspace_blobs.checkpoint_id, tenant
            )

            for blob in initial_workspace_blobs.added_blobs:
                if blob not in initial_blob_ids:
                    initial_blob_ids.append(blob)

            initial_blob_ids = [
                b
                for b in initial_blob_ids
                if b not in initial_workspace_blobs.deleted_blobs
            ]

            logger.info(
                f"Computed {len(initial_blob_ids)} initial blob IDs for conversation {req_id}"
            )

            # Apply max_blobs limit
            if len(initial_blob_ids) > max_blobs:
                logger.warning(
                    f"Conversation {req_id} has {len(initial_blob_ids)} initial blobs, "
                    f"which exceeds the max_blobs limit of {max_blobs}. "
                    f"Truncating blob list."
                )
                initial_blob_ids = initial_blob_ids[:max_blobs]

            # Save blob IDs
            blob_data = {
                "initial": initial_blob_ids,
                "initial_state": {
                    "checkpoint_id": initial_workspace_blobs.checkpoint_id,
                    "added_blobs": initial_workspace_blobs.added_blobs,
                    "deleted_blobs": initial_workspace_blobs.deleted_blobs,
                },
            }

            with open(conv_dir / "blob_ids.json", "w", encoding="utf-8") as f:
                json_module.dump(blob_data, f, indent=2, ensure_ascii=False)

            processed_conversations.append(req_id)

        except Exception as e:
            logger.error(f"Error processing conversation {req_id}: {e}", exc_info=True)

    # Create metadata file
    metadata = {
        "export_time": datetime.datetime.now().isoformat(),
        "tenant_name": tenant_name,
        "tenant_id": tenant.tenant_id,
        "from_date": from_date.isoformat() if from_date else None,
        "to_date": to_date.isoformat() if to_date else None,
        "num_conversations": len(processed_conversations),
        "conversations": processed_conversations,
    }

    with open(output_path / "metadata.json", "w", encoding="utf-8") as f:
        json_module.dump(metadata, f, indent=2, ensure_ascii=False)

    # Create README file
    readme_content = f"""# Agent Conversations Export with Complete Workspace

Export time: {datetime.datetime.now().isoformat()}
Tenant: {tenant_name}
Number of conversations: {len(processed_conversations)}
Time range: {from_date.isoformat() if from_date else 'N/A'} to {to_date.isoformat() if to_date else 'N/A'}

## Directory Structure

- `metadata.json`: Contains metadata about the export
- `conversation_stats.csv`: Contains statistics about all conversations including conversation IDs, request IDs, and turn counts
- `conversations/`: Contains individual conversation directories
  - `conversation_<request_id>/`: Directory for each conversation
    - `conversation.json`: Conversation data with all turns, tool calls, and user messages
    - `initial_workspace/`: Directory containing the initial state of the workspace
    - `blob_changelog.json`: Tracks all blob changes (additions and deletions) throughout the conversation
"""

    with open(output_path / "README.md", "w") as f:
        f.write(readme_content)

    end_time = time.time()
    elapsed_time = end_time - start_time
    logger.info(
        f"Exported {len(processed_conversations)} conversations to {output_path}"
    )
    logger.info(f"Total export time: {elapsed_time:.2f} seconds")
    return output_path


def main():
    args = parse_args()

    # Set log level based on verbose flag
    set_log_level(args.verbose)

    # Calculate date range
    to_date = args.to_date or datetime.datetime.now()

    if args.from_date:
        from_date = args.from_date
    elif args.last_days:
        from_date = to_date - datetime.timedelta(days=args.last_days)
    else:
        # Default to last 7 days
        from_date = to_date - datetime.timedelta(days=7)

    # Export conversations
    export_path = export_conversations_with_complete_workspace(
        from_date=from_date,
        to_date=to_date,
        tenant_name=args.tenant,
        num_conversations=args.num_conversations,
        output_dir=args.output_dir,
        request_id=args.request_id,
        min_turns=args.min_turns,
        max_blobs=args.max_blobs,
        parallel_workers=args.parallel_workers,
        optimize_binary=args.optimize_binary,
        generate_changelog=args.generate_changelog,
        blob_only=args.blob_only,
        exclude_files=args.exclude_files,
        skip_welcome_conversations=args.skip_welcome_conversations,
    )

    if export_path:
        logger.info(f"Export completed successfully. Output directory: {export_path}")
    else:
        logger.error("Export failed")


if __name__ == "__main__":
    main()
