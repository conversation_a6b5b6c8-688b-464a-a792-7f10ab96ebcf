# script to calculate checkpoint id given a folder of files
# copied from experimental/surbhi/create_checkpoint_id_script.py

import os
import hashlib
import binascii
import argparse
import fnmatch


def compute_file_id(path, contents):
    """Compute the blob name (file ID) for a given file."""
    hasher = hashlib.sha256()
    hasher.update(path.encode("utf-8"))
    if isinstance(contents, str):
        contents = contents.encode("utf-8")
    hasher.update(contents)
    return hasher.hexdigest()


def calc_checkpoint_id(blob_ids):
    # Ensure there is at least one input string
    if not blob_ids:
        return None

    # Convert hex strings to byte strings
    byte_strings = [bytes.fromhex(blob_id) for blob_id in blob_ids]

    # The checkpoint id is formed by XORing the byte strings together
    result = bytearray(byte_strings[0])
    for hash_bytes in byte_strings[1:]:
        # XOR each byte pair
        for i in range(len(result)):
            result[i] ^= hash_bytes[i]

    # Convert the result back to hex string
    encoded_hex = binascii.hexlify(result).decode("ascii")
    return encoded_hex


def should_ignore(file_path, ignore_patterns):
    """Check if a file should be ignored based on the ignore patterns."""
    for pattern in ignore_patterns:
        if fnmatch.fnmatch(file_path, pattern):
            return True
    return False


# use ignore_patterns to not create blob names for certain files (ex. files that wouldn't get uploaded to content manager)
def process_folder(folder_path, ignore_patterns=[]):
    blob_names = []
    try:
        for root, dirs, files in os.walk(folder_path, topdown=True):
            dir_idx = 0
            while dir_idx < len(dirs):
                # do the deletion in place
                if should_ignore(dirs[dir_idx], ignore_patterns):
                    print(f"Ignoring directory: {dirs[dir_idx]}")
                    dirs.pop(dir_idx)
                else:
                    dir_idx += 1
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, folder_path)
                if should_ignore(relative_path, ignore_patterns):
                    print(f"Ignoring file: {relative_path}")
                    continue
                try:
                    with open(file_path, "rb") as f:
                        content = f.read()
                    blob_name = compute_file_id(relative_path, content)
                    blob_names.append(blob_name)
                    print(f"Processing file: {relative_path}, blob name:{blob_name}")
                except IOError as e:
                    print(f"Error reading file {file_path}: {e}")
    except OSError as e:
        print(f"Error accessing folder {folder_path}: {e}")
    return blob_names


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Calculate checkpoint ID for a folder of files."
    )
    parser.add_argument("--ignore", nargs="*", default=[], help="Patterns to ignore")
    args = parser.parse_args()

    default_ignores = [".git"]
    ignores = default_ignores + args.ignore

    blob_names = process_folder(os.getcwd(), ignore_patterns=ignores)
    checkpoint_id = calc_checkpoint_id(blob_names)
    print(f"Calculated checkpoint ID: {checkpoint_id}")
