"""<PERSON><PERSON><PERSON> to set up column level access policies for BigQuery tables.

This script sets up policy tags to mask PII data in BigQuery tables. Those tags
can follow hierarchies, currently this script creates this hierarchy of tags:
    * PII -> has SHA256 masking policy
    ** User ID -> has no masking policy (inherits the PII masking policy) and is applied to user_id column in request_insight BigQuery tables

The idea of the hierarchy is that the child tags can be applied to columns in
tables and left alone. Then, if we change how we want to mask/hide that data, or
who has access to the parent tag, we can simply move the child tag around to the
right parent.

We also create a taxonomy, which is a container for policy tags.

Note that this script does not apply the tags to any columns or give any one
access to read the columns, masked or unmasked. The roles for that are in
https://cloud.google.com/bigquery/docs/column-data-masking-intro#roles_for_querying_masked_data.

Based on https://cloud.google.com/bigquery/docs/column-level-security.

GCP setup (requires serviceusage.services.enable and Data Catalog Admin):
  If this is the first time doing this in that project, the Data Catalog and
  BigQuery Data Policy APIs may need to be enabled
  https://cloud.google.com/bigquery/docs/column-level-security#before_you_begin

Local setup (requires Data Catalog Policy Tag Admin):
  Have gcloud credentials set up
  $ pip install google-cloud-datacatalog
  $ pip install google-cloud-bigquery-datapolicies

To run:
  Note that the location used here must match the location of the bigquery
  datasets/tables this will be used on.

Commands:
  dev: python3.9 experimental/aswin/gcp_bigquery_column_access.py --project system-services-dev --location us --prefix dev
  staging: python3.9 experimental/aswin/gcp_bigquery_column_access.py --project system-services-prod --location us --prefix staging
  prod: python3.9 experimental/aswin/gcp_bigquery_column_access.py --project system-services-prod --location us --prefix prod
  eu staging: python3.9 experimental/aswin/gcp_bigquery_column_access.py --project system-services-prod --location eu --prefix eu_staging
  eu prod: python3.9 experimental/aswin/gcp_bigquery_column_access.py --project system-services-prod --location eu --prefix eu_prod
"""
# pylint: disable=no-name-in-module

import argparse

from google.cloud import bigquery_datapolicies_v1, datacatalog_v1

# Copied from cloud_info.jsonnet
projects = [
    ("system-services-dev", "1035750215372"),
    ("system-services-prod", "835723878709"),
]


def create_names(prefix):
    names = {}
    taxonomy_name = f"{prefix}_request_insight_taxonomy"
    names["taxonomy_name"] = taxonomy_name
    names["pii_parent_tag"] = f"{prefix}_pii"
    names["user_id_child_tag"] = f"{prefix}_user_id"
    names["pii_data_policy_name"] = f"{taxonomy_name}_pii_masking_policy"
    # Use "DEFAULT_MASKING_VALUE" to hide values entirely, and "SHA256" to
    # replace with a hash
    names["masking_policy"] = "SHA256"
    return names


def create_taxonomy(client, names, project, location):
    """Create a taxonomy to hold the request insight policies."""
    parent = datacatalog_v1.PolicyTagManagerClient.common_location_path(
        project, location
    )

    # Check if it already exists
    taxonomies = client.list_taxonomies(parent=parent)
    for taxonomy in taxonomies:
        if taxonomy.display_name == names["taxonomy_name"]:
            print(f"Found existing taxonomy {taxonomy.name}")
            return taxonomy

    # If it doesn't exist, create it
    taxonomy = datacatalog_v1.Taxonomy()
    taxonomy.display_name = names["taxonomy_name"]
    taxonomy.description = "Used to mask fields with PII in request insight data"

    taxonomy = client.create_taxonomy(parent=parent, taxonomy=taxonomy)
    print(f"Created taxonomy {taxonomy.display_name} {taxonomy.name}")
    return taxonomy


def create_policy_tags(client, names, taxonomy):
    """Create policy tags for each column type."""
    # Check if the tags already exist
    policy_tags = client.list_policy_tags(parent=taxonomy.name)
    pii_parent_tag, user_id_child_tag = None, None
    for policy_tag in policy_tags:
        if policy_tag.display_name == names["pii_parent_tag"]:
            print(f"Found existing parent policy tag {policy_tag.name}")
            pii_parent_tag = policy_tag
        elif policy_tag.display_name == names["user_id_child_tag"]:
            print(f"Found existing child policy tag {policy_tag.name}")
            user_id_child_tag = policy_tag

    # If they doesn't exist, create them
    if pii_parent_tag is None:
        pii_parent_tag = datacatalog_v1.PolicyTag()
        pii_parent_tag.display_name = names["pii_parent_tag"]
        pii_parent_tag.description = (
            "Used to mask fields with PII in request insight data"
        )

        pii_parent_tag = client.create_policy_tag(
            parent=taxonomy.name, policy_tag=pii_parent_tag
        )
        print(f"Created policy tag {pii_parent_tag.name}")

    if user_id_child_tag is None:
        user_id_child_tag = datacatalog_v1.PolicyTag()
        user_id_child_tag.display_name = names["user_id_child_tag"]
        user_id_child_tag.description = (
            "Used to mask fields with user_id in request insight data"
        )
        user_id_child_tag.parent_policy_tag = pii_parent_tag.name

        user_id_child_tag = client.create_policy_tag(
            parent=taxonomy.name, policy_tag=user_id_child_tag
        )
        print(f"Created policy tag {user_id_child_tag.name}")

    return pii_parent_tag, user_id_child_tag


def create_data_policy(client, names, project, location, parent_tag):
    """Create a data policy to mask all PII data.

    Note that the data policy is applied to the parent tag, and automatically
    cascades to all subtags. This also allows us to move subtags around to
    different parent tags as requirements change.
    """
    parent = bigquery_datapolicies_v1.DataPolicyServiceClient.common_location_path(
        project, location
    )

    # Check if the policies already exist
    data_policies = client.list_data_policies(parent=parent)
    for data_policy in data_policies:
        if data_policy.data_policy_id == names["pii_data_policy_name"]:
            # These policy names have the project ID as a string instead of a
            # number, unlike the tag client
            policy_name_with_project_name = data_policy.policy_tag
            for project_name, project_id in projects:
                if project_id in policy_name_with_project_name:
                    policy_name_with_project_name = (
                        policy_name_with_project_name.replace(project_id, project_name)
                    )
                    print(
                        f"Converted policy name {data_policy.policy_tag} to {policy_name_with_project_name}"
                    )
            if policy_name_with_project_name == parent_tag.name:
                print(f"Found existing data policy {data_policy.name}")
                return data_policy
            else:
                print(
                    f"Deleting outdated data policy {data_policy.name} with tag {data_policy.policy_tag}"
                )
                client.delete_data_policy(name=data_policy.name)

    # If they don't exist, create them
    data_policy = bigquery_datapolicies_v1.DataPolicy()
    data_policy.policy_tag = parent_tag.name
    data_policy.data_policy_id = names["pii_data_policy_name"]
    data_policy.data_masking_policy.predefined_expression = names["masking_policy"]
    data_policy.data_policy_type = (
        bigquery_datapolicies_v1.DataPolicy.DataPolicyType.DATA_MASKING_POLICY
    )

    data_policy = client.create_data_policy(parent=parent, data_policy=data_policy)
    print(f"Created data policy {data_policy.name}")
    return data_policy


def main():
    # Add argument parsing for the taxonomy
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--project",
        choices=[p[0] for p in projects],
        type=str,
        required=True,
        help="The project to use",
    )
    parser.add_argument(
        "--location",
        type=str,
        required=True,
        help="The location to use, this should match the location of the BigQuery datasets/tables the policies will be applied to ('us' is probably the right choice)",
    )
    parser.add_argument(
        "--prefix",
        type=str,
        choices=["dev", "staging", "prod", "eu_staging", "eu_prod"],
        required=True,
        help="The prefix to use when naming resources",
    )
    args = parser.parse_args()

    if args.prefix.endswith("staging") or args.prefix.endswith("prod"):
        assert args.project == "system-services-prod"
    else:
        assert args.project == "system-services-dev"

    names = create_names(args.prefix)

    tag_client = datacatalog_v1.PolicyTagManagerClient()

    taxonomy = create_taxonomy(tag_client, names, args.project, args.location)
    parent_tag, child_tag = create_policy_tags(tag_client, names, taxonomy)

    policy_client = bigquery_datapolicies_v1.DataPolicyServiceClient()
    create_data_policy(policy_client, names, args.project, args.location, parent_tag)

    # Add fmt strings for the project and location - using the values from
    # cloud_info.jsonnet adds a little extra bit of safety
    parent_tag_name = parent_tag.name.replace(args.project, "%s", 1)
    child_tag_name = child_tag.name.replace(args.project, "%s", 1)

    print(
        f"""
To apply to a BigQuery Table, add this to bigquery.jsonnet:

  local dataAccessPolicyTags =
    ...
    pii: '{parent_tag_name}' % cloudInfo.projectId,
    userId: '{child_tag_name}' % cloudInfo.projectId,


Then add a policyTags field to the user_id column in that BigQuery table's schema (also in bigquery.jsonnet), like this:

  createTableDefinition(
    ...
    {{
      name: 'user_id',
      type: 'STRING',
      mode: 'REQUIRED',
      description: 'The user ID of the request',
      policyTags: if std.objectHas(bigqueryLib.dataAccessPolicyTags, 'userId') then {{
        names: [
          bigqueryLib.dataAccessPolicyTags.userId,
        ],
      }} else {{}},
    }}
    ...

Access to each of these is also defined in bigquery.jsonnet, in the
IAMPartialPolicy that refers to the DataCatalogPolicyTag created by this script.
"""
    )


if __name__ == "__main__":
    main()
