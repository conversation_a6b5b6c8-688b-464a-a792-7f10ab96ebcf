"""Script to find tenants from last request JSON that don't exist in jsonnet files."""

import os
import pathlib
import re
import json
from typing import List, Dict, Any


def parse_tenant_block(block: str) -> Dict[str, Any]:
    """Parse a single tenant block from the content."""
    result = {}

    # Find name field
    name_match = re.search(r'name:\s*["\']([^"\']+)["\']', block)
    if name_match:
        result["name"] = name_match.group(1)

    # Find tenantId field
    tenant_id_match = re.search(r'tenantId:\s*["\']([^"\']+)["\']', block)
    if tenant_id_match:
        result["tenantId"] = tenant_id_match.group(1)

    return result


def parse_jsonnet_file(file_path: str) -> List[Dict[str, Any]]:
    """Parse a jsonnet file to extract tenant information using regex."""
    try:
        with open(file_path, "r") as f:
            content = f.read()

        results = []

        # For single tenant files (in prod_tenants directory)
        if "prod_tenants" in str(file_path):
            result = parse_tenant_block(content)
            if result:
                results.append(result)
            return results

        # For tenants.jsonnet with multiple tenants
        tenant_blocks = re.finditer(
            r'{\s*(?:(?!},).)*?name:\s*["\'][^"\']+["\'](?:(?!},).)*?}',
            content,
            re.DOTALL,
        )

        for block in tenant_blocks:
            result = parse_tenant_block(block.group(0))
            if result:
                results.append(result)

        return results
    except Exception as e:
        print(f"Error parsing {file_path}: {str(e)}")
        return []


def get_all_tenants() -> set:
    """Get set of all tenant IDs from jsonnet files."""
    workspace = pathlib.Path(os.getenv("BUILD_WORKSPACE_DIRECTORY", os.getcwd()))
    tenant_ids = set()

    # Check main tenants file
    tenants_file = workspace / "deploy/tenants/tenants.jsonnet"
    if tenants_file.exists():
        for tenant in parse_jsonnet_file(str(tenants_file)):
            if "tenantId" in tenant:
                tenant_ids.add(tenant["tenantId"])

    # Check individual prod tenant files
    prod_tenants_dir = workspace / "deploy/tenants/prod_tenants"
    if prod_tenants_dir.exists():
        for file in prod_tenants_dir.glob("*.jsonnet"):
            for tenant in parse_jsonnet_file(str(file)):
                if "tenantId" in tenant:
                    tenant_ids.add(tenant["tenantId"])

    return tenant_ids


def main():
    """Main function."""
    print("Finding tenants that exist in last request but not in jsonnet files...")

    # Load tenant_last_request.json
    with open(
        "/home/<USER>/augment/experimental/cam/request_metadata_tenants.json", "r"
    ) as f:
        last_requests = json.load(f)

    # Get all tenant IDs from jsonnet files
    existing_tenant_ids = get_all_tenants()

    # Find tenants that don't exist in jsonnet files
    missing_tenants = []
    for tenant in last_requests:
        if tenant["tenant_id"] not in existing_tenant_ids:
            missing_tenants.append(tenant)

    if not missing_tenants:
        print("No missing tenants found.")
        return

    # Format each tenant as a struct for the backfill query
    tenant_structs = []
    for tenant in missing_tenants:
        struct = (
            f"  STRUCT(\n"
            f"    '{tenant['tenant_id']}' AS id,\n"
            f"    '{tenant['tenant']}' AS name,\n"
            f"    '{tenant['shard_namespace']}' AS shard_namespace,\n"
            f"    '{tenant['cloud']}' AS cloud,\n"
            f"    TIMESTAMP('{tenant['time']}') AS deleted_at\n"
            f"  )"
        )
        tenant_structs.append(struct)

    # Build the complete backfill query
    structs_sql = ",\n".join(tenant_structs)
    query = (
        "MERGE `system-services-prod.us_prod_request_insight_analytics_dataset.tenant` T\n"
        "USING (\n"
        "  SELECT *\n"
        "  FROM UNNEST([\n"
        f"{structs_sql}\n"
        "  ])\n"
        ") S\n"
        "ON T.id = S.id\n"
        "WHEN NOT MATCHED THEN\n"
        "  INSERT (id, name, shard_namespace, cloud, deleted_at)\n"
        "  VALUES (S.id, S.name, S.shard_namespace, S.cloud, S.deleted_at)"
    )

    print("\nBackfill query for missing tenants:")
    print("-" * 80)
    print(query)

    # Print summary
    print(
        f"\nFound {len(missing_tenants)} tenants that exist in last_request but not in jsonnet files"
    )
    print("\nMissing tenants:")
    for tenant in missing_tenants:
        print(
            f"  - {tenant['tenant']} ({tenant['tenant_id']}) in {tenant['shard_namespace']} - {tenant['cloud']}"
        )


if __name__ == "__main__":
    main()
