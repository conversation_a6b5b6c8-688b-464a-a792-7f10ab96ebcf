"""Script to find deleted tenant IDs from jsonnet files."""

import os
import pathlib
import re
import json
from typing import List, Dict, Any

# Define missing tenant IDs at module level
MISSING_TENANT_IDS = {
    "1132fc0adaea2fb6dc8a6bafe34f8920",
    "d24c5f1959a656ed3070d0d60fc8b91e",
    "e73fd756deb03f77f9603de1d70a5ce7",
    "51b96ec9cd1b02a7c48e30f8503092be",
    "7f9a0b86e7c0308c3b2d0497b591ad0d",
    "e68d7177cd1a8f3ba0e4c3b2c5f21dc2",
    "3cf2895651f00f8ff04b20565c3cb245",
    "9a351ed261f4b1d92ca94dc9edcf082",
    "1a5770926bc2223e4929e0f37ac3a405",
    "c8fcbdf57b2afd269d0aa025a52d0ff8",
    "f6d45cbba473ab6d7a1caa6f4396eb9e",
    "82976673e88293e144296f4cc850540",
    "c779019fcd8b3c14b7675cd8c2774644",
    "c9f7c12116a4e98dd35e717cc8a7bc83",
    "dac61860c58300f4dc0454cff5fcf8f1",
    "4c9f2312976ab9200c9060106a0484cc",
    "61587b3cb6b461949e4590da993d6f6e",
    "750379b5926e9f728aa6c253d37e3792",
    "e79aca8b8e00f2050230549c876741f3",
    "6258e306f9da1a70f160abe1a1e8150c",
    "31ebaf491689ff5504953a375281b0af",
    "9cc588011a013e3615db2b6a5fbff6f",
    "f2b842dcf8079a0ea85c2b9b555ba52e",
    "fa91c560d015447ca4f9d3c48ff96df4",
    "7378e8e3ddf9f0c69209709f6490e062",
    "42f20285b77fe13769825727e3e86799",
    "9d4b7149f6d839fa7dd17778d8ae4e6",
    "dbad5dbea0c2a7a9d575c6beeb520bc7",
    "7dca4d25a2d64f4adfb63b66da9968db",
    "6cc517486bbf036b71d83e5f9f4488a8",
    "a3aec01c7019b1ca4e17ab2c0ce82b62",
    "dd8582279728b797aeb6b725c1f1182c",
    "d6dae50cfca4816c6c08c794aad1e069",
}


def parse_tenant_block(block: str) -> Dict[str, Any]:
    """Parse a single tenant block from the content."""
    result = {}

    # Find name field
    name_match = re.search(r'name:\s*["\']([^"\']+)["\']', block)
    if name_match:
        result["name"] = name_match.group(1)

    # Find tenantId field
    tenant_id_match = re.search(r'tenantId:\s*["\']([^"\']+)["\']', block)
    if tenant_id_match:
        result["tenantId"] = tenant_id_match.group(1)

    # Find deleted_at field
    deleted_match = re.search(r'deleted_at:\s*["\']([^"\']+)["\']', block)
    if deleted_match:
        result["deleted_at"] = deleted_match.group(1)

    # Find namespace field
    namespace_match = re.search(r'namespace:\s*["\']([^"\']+)["\']', block)
    if namespace_match:
        result["namespace"] = namespace_match.group(1)

    # Find cloud field
    cloud_match = re.search(r'cloud:\s*["\']([^"\']+)["\']', block)
    if cloud_match:
        result["cloud"] = cloud_match.group(1)

    return result


def parse_jsonnet_file(file_path: str) -> List[Dict[str, Any]]:
    """Parse a jsonnet file to extract tenant information using regex."""
    try:
        with open(file_path, "r") as f:
            content = f.read()

        results = []

        # For single tenant files (in prod_tenants directory)
        if "prod_tenants" in str(file_path):
            result = parse_tenant_block(content)
            if result:
                results.append(result)
            return results

        # For tenants.jsonnet with multiple tenants
        # Find all blocks that look like tenant definitions
        tenant_blocks = re.finditer(
            r'{\s*(?:(?!},).)*?name:\s*["\'][^"\']+["\'](?:(?!},).)*?}',
            content,
            re.DOTALL,
        )

        for block in tenant_blocks:
            result = parse_tenant_block(block.group(0))
            if result:
                results.append(result)

        return results
    except Exception as e:
        print(f"Error parsing {file_path}: {str(e)}")
        return []


def get_deleted_tenants() -> List[Dict[str, Any]]:
    """Get list of deleted tenant IDs and their deletion timestamps."""
    workspace = pathlib.Path(os.getenv("BUILD_WORKSPACE_DIRECTORY", os.getcwd()))
    deleted_tenants = []

    # Check main tenants file
    tenants_file = workspace / "deploy/tenants/tenants.jsonnet"
    if tenants_file.exists():
        for tenant in parse_jsonnet_file(str(tenants_file)):
            if tenant.get("tenantId") in MISSING_TENANT_IDS:
                deleted_tenants.append(tenant)

    # Check individual prod tenant files
    prod_tenants_dir = workspace / "deploy/tenants/prod_tenants"
    if prod_tenants_dir.exists():
        for file in prod_tenants_dir.glob("*.jsonnet"):
            for tenant in parse_jsonnet_file(str(file)):
                if tenant.get("tenantId") in MISSING_TENANT_IDS:
                    deleted_tenants.append(tenant)

    return deleted_tenants


def main():
    """Main function."""
    print("Finding specific deleted tenants...")
    deleted_tenants = get_deleted_tenants()

    if not deleted_tenants:
        print("No matching deleted tenants found.")
        return

    # Format each tenant as a struct for the backfill query
    tenant_structs = []
    for tenant in deleted_tenants:
        struct = (
            f"STRUCT(\n"
            f"    '{tenant['tenantId']}' AS id,\n"
            f"    '{tenant['name']}' AS name,\n"
            f"    '{tenant.get('namespace', 'unknown')}' AS shard_namespace,\n"
            f"    '{tenant.get('cloud', 'GCP_US_CENTRAL1_PROD')}' AS cloud,\n"
            f"    TIMESTAMP('{tenant['deleted_at']}') AS deleted_at\n"
            f"  )"
        )
        tenant_structs.append(struct)

    # Build the complete backfill query
    query = """MERGE `%(projectId)s.%(dataset)s.tenant` T
USING (
  SELECT *
  FROM UNNEST([
    {}
  ])
) S
ON T.id = S.id
WHEN MATCHED THEN
  UPDATE SET
    name = S.name,
    shard_namespace = S.shard_namespace,
    cloud = S.cloud,
    deleted_at = S.deleted_at
WHEN NOT MATCHED THEN
  INSERT (id, name, shard_namespace, cloud, deleted_at)
  VALUES (S.id, S.name, S.shard_namespace, S.cloud, S.deleted_at)""".format(
        ",\n    ".join(tenant_structs)
    )

    print("\nBackfill query for missing tenants:")
    print("-" * 80)
    print(query)

    # Print summary
    print(
        f"\nFound {len(deleted_tenants)} matching tenants out of {len(MISSING_TENANT_IDS)} missing IDs"
    )
    missing = MISSING_TENANT_IDS - {t["tenantId"] for t in deleted_tenants}
    if missing:
        print("\nWarning: Could not find data for these tenant IDs:")
        for tenant_id in missing:
            print(f"  - {tenant_id}")


if __name__ == "__main__":
    main()
