package main

import (
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"os"
	"sync"
	"time"

	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/customer"
	"github.com/stripe/stripe-go/v80/subscription"
)

const (
	// API keys
	apiKey = ""

	// Price IDs
	communityPlanID    = "price_1Qu5SBAmv9gV96tuG8NqMZIv"
	professionalPlanID = "price_1Qu5RAAmv9gV96tuxgUJ0v4y"

	// Maximum number of concurrent operations
	maxConcurrent = 5
)

func main() {
	// Initialize Stripe
	stripe.Key = apiKey

	// Open the CSV file
	file, err := os.Open("/Users/<USER>/Documents/filtered_result_no_ent.csv")
	if err != nil {
		log.Fatalf("Failed to open file: %v", err)
	}
	defer file.Close()

	// Create output files
	noSubscriptionFile, err := os.Create("/Users/<USER>/Documents/no_subscription_users.txt")
	if err != nil {
		log.Fatalf("Failed to create no subscription users file: %v", err)
	}
	defer noSubscriptionFile.Close()

	communityPlanFile, err := os.Create("/Users/<USER>/Documents/community_plan_users.txt")
	if err != nil {
		log.Fatalf("Failed to create community plan users file: %v", err)
	}
	defer communityPlanFile.Close()

	// Create mutexes for file writing
	var noSubscriptionMutex sync.Mutex
	var communityPlanMutex sync.Mutex

	// Create CSV reader
	reader := csv.NewReader(file)

	// Read header
	header, err := reader.Read()
	if err != nil {
		log.Fatalf("Failed to read header: %v", err)
	}

	// Create a channel to limit concurrency
	semaphore := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup

	// Process each row
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading record: %v", err)
			continue
		}

		// Create a map for easier access to fields
		row := make(map[string]string)
		for i, field := range header {
			if i < len(record) {
				row[field] = record[i]
			}
		}

		// Get customer ID from the CSV
		stripeCustomerID := row["id"]

		// Skip if no customer ID
		if stripeCustomerID == "" {
			continue
		}

		// Acquire semaphore slot
		semaphore <- struct{}{}
		wg.Add(1)

		// Process in a goroutine
		go func(stripeCustomerID string) {
			defer wg.Done()
			defer func() { <-semaphore }() // Release semaphore slot when done

			// Get customer directly by ID
			c, err := customer.Get(stripeCustomerID, nil)
			if err != nil {
				log.Printf("Error retrieving customer %s: %v", stripeCustomerID, err)
				return
			}

			// Get customer's subscriptions
			subParams := &stripe.SubscriptionListParams{
				Customer: stripe.String(c.ID),
			}
			subIter := subscription.List(subParams)

			hasActiveSubscription := false
			isOnCommunityPlan := false

			for subIter.Next() {
				sub := subIter.Subscription()

				// Check if subscription is active
				if sub.Status == "active" || sub.Status == "trialing" {
					hasActiveSubscription = true

					// Check if on community plan
					for _, item := range sub.Items.Data {
						if item.Price.ID == communityPlanID {
							isOnCommunityPlan = true
							break
						}
					}
				}
			}

			if err := subIter.Err(); err != nil {
				log.Printf("Error iterating subscriptions for customer %s: %v", c.ID, err)
			}

			// Case 1: No active subscriptions
			if !hasActiveSubscription {
				augmentUserID := row["augment_user_id"]
				noSubscriptionMutex.Lock()
				fmt.Fprintf(noSubscriptionFile, "%s,%s\n", stripeCustomerID, augmentUserID)
				noSubscriptionMutex.Unlock()
				log.Printf("Customer %s (UUID: %s) has no active subscriptions", c.ID, augmentUserID)
			}

			// Case 2: On community plan
			if hasActiveSubscription && isOnCommunityPlan {
				augmentUserID := row["augment_user_id"]
				communityPlanMutex.Lock()
				fmt.Fprintf(communityPlanFile, "%s,%s\n", stripeCustomerID, augmentUserID)
				communityPlanMutex.Unlock()
				log.Printf("Customer %s (UUID: %s) is on community plan", c.ID, augmentUserID)
			}

			time.Sleep(time.Second / 100)
		}(stripeCustomerID)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	fmt.Println("Processing complete!")
}
