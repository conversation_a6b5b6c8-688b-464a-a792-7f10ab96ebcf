#!/usr/bin/env python3
"""
Script to add tier fields to all tenants in tenants.jsonnet and individual tenant files.

This script:
1. Reads the tenants.jsonnet file, adds a tier field to each tenant based on regex patterns
2. Processes all individual tenant files in deploy/tenants/prod_tenants directory
3. Adds 'ENTERPRISE' tier to individual tenant files if they don't already have a tier

Tiers are determined as follows:
- "PROFESSIONAL" tier: tenant name matches /discovery\d+/i or /^d\d+/i
- "COMMUNITY" tier: tenant name matches /vanguard/i or /^i\d+/i
- "ENTERPRISE" tier: all other tenants
"""

import argparse
import glob
import logging
import os
import pathlib
import re
import sys


def is_professional_tier(tenant_name):
    """Check if tenant is professional tier based on name."""
    return bool(
        re.search(r"discovery\d+", tenant_name, re.IGNORECASE)
        or re.search(r"^d\d+", tenant_name, re.IGNORECASE)
    )


def is_community_tier(tenant_name):
    """Check if tenant is community tier based on name."""
    return bool(
        re.search(r"vanguard", tenant_name, re.IGNORECASE)
        or re.search(r"^i\d+", tenant_name, re.IGNORECASE)
    )


def get_tier(tenant_name):
    """Determine the tier based on tenant name."""
    if is_professional_tier(tenant_name):
        return "PROFESSIONAL"
    elif is_community_tier(tenant_name):
        return "COMMUNITY"
    else:
        return "ENTERPRISE"


def process_tenants_file(file_path):
    """Process the tenants.jsonnet file to add tier fields to all tenants."""
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Find all tenant blocks in the file
    tenant_blocks = []
    current_block = ""
    in_block = False
    bracket_count = 0

    for line in content.split("\n"):
        if "{" in line and not in_block:
            in_block = True
            bracket_count = line.count("{") - line.count("}")
            current_block = line
        elif in_block:
            current_block += "\n" + line
            bracket_count += line.count("{") - line.count("}")

            if bracket_count == 0:
                in_block = False
                if "name:" in current_block:
                    tenant_blocks.append(current_block)
                current_block = ""

    # Process each tenant block
    new_content = content
    for block in tenant_blocks:
        # Extract the tenant name
        name_match = re.search(r'name:\s*[\'"]([^\'"]+)[\'"]', block)
        if not name_match:
            continue

        tenant_name = name_match.group(1)
        tier = get_tier(tenant_name)

        # Check if the tier field already exists
        if re.search(r'tier:\s*[\'"][^\'"]+[\'"]', block):
            # Replace existing tier field
            modified_block = re.sub(
                r'tier:\s*[\'"][^\'"]+[\'"]', f"tier: '{tier}'", block
            )
        else:
            # Add tier field after name field
            modified_block = re.sub(
                r'(name:\s*[\'"][^\'"]+[\'"],)', f"\\1\n    tier: '{tier}',", block
            )

        if modified_block != block:
            new_content = new_content.replace(block, modified_block)
            print(f"Set tier '{tier}' for tenant '{tenant_name}' in {file_path}")

    return new_content


def process_individual_tenant_file(file_path):
    """Process an individual tenant file to add tier field if missing or update if exists."""
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # Extract the tenant name
    name_match = re.search(r'name:\s*[\'"]([^\'"]+)[\'"]', content)
    if not name_match:
        print(f"Could not find tenant name in {file_path}, skipping")
        return content

    tenant_name = name_match.group(1)
    tier = get_tier(tenant_name)

    # Check if the tier field already exists
    if re.search(r'tier:\s*[\'"][^\'"]+[\'"]', content):
        # Replace existing tier field
        new_content = re.sub(r'tier:\s*[\'"][^\'"]+[\'"]', f"tier: '{tier}'", content)
        print(f"Updated tier to '{tier}' for tenant '{tenant_name}' in {file_path}")
    else:
        # Add tier field after name field
        new_content = re.sub(
            r'(name:\s*[\'"][^\'"]+[\'"],)', f"\\1\n  tier: '{tier}',", content
        )
        print(f"Added '{tier}' tier for tenant '{tenant_name}' in {file_path}")

    return new_content


def process_all_tenant_files(base_dir, dry_run=False, output_dir=None):
    """Process all tenant files in the prod_tenants directory."""
    prod_tenants_dir = os.path.join(base_dir, "deploy", "tenants", "prod_tenants")
    tenant_files = glob.glob(os.path.join(prod_tenants_dir, "*.jsonnet"))

    for file_path in tenant_files:
        try:
            new_content = process_individual_tenant_file(file_path)

            if not dry_run:
                output_path = file_path
                if output_dir:
                    output_path = os.path.join(output_dir, os.path.basename(file_path))

                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(new_content)
        except Exception as e:
            print(f"Error processing {file_path}: {str(e)}")


def main():
    """Main entry function."""
    parser = argparse.ArgumentParser(
        description="Add tier fields to tenants in tenants.jsonnet and individual tenant files"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Print the modified file content without writing it",
    )
    parser.add_argument(
        "--output-dir",
        help="Output directory for modified files (defaults to the same directories)",
    )

    args = parser.parse_args()

    # Get workspace directory
    workspace_dir = "/home/<USER>/augment"
    tenants_file = os.path.join(workspace_dir, "deploy", "tenants", "tenants.jsonnet")

    if not os.path.exists(tenants_file):
        print(f"Error: Tenants file not found at {tenants_file}")
        sys.exit(1)

    print(f"Processing main tenants file: {tenants_file}")

    try:
        # Process main tenants file
        new_content = process_tenants_file(tenants_file)

        if args.dry_run:
            print("Dry run for main tenants file:")
            print(new_content)
        else:
            output_file = tenants_file
            if args.output_dir:
                output_file = os.path.join(
                    args.output_dir, os.path.basename(tenants_file)
                )

            with open(output_file, "w", encoding="utf-8") as f:
                f.write(new_content)
            print(f"Successfully updated {output_file}")

        # Process individual tenant files
        print("\nProcessing individual tenant files in prod_tenants directory:")
        process_all_tenant_files(workspace_dir, args.dry_run, args.output_dir)

        if args.dry_run:
            print("Dry run completed. No files were modified.")
        else:
            print("Successfully updated all tenant files")

    except Exception as e:
        print(f"Error: Failed to process tenant files: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
