package main

import (
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/subscription"
)

const (
	// Replace with your test API key for sandbox testing
	// dev
	// apiKey = ""
	// prod
	apiKey = ""

	// Price IDs from your config
	// dev
	// professionalPlanID = "price_1QuILlAcLbOdpxPg6ehebUNN"
	// prod
	professionalPlanID = "price_1Qu5RAAmv9gV96tuxgUJ0v4y"

	// Standard trial period in days
	standardTrialDays = 14

	// Maximum number of concurrent operations
	maxConcurrent = 5
)

func main() {
	// Initialize Stripe
	stripe.Key = apiKey

	// Open the CSV file
	// dev
	// file, err := os.Open("/Users/<USER>/Documents/unified_customers_dev.csv")
	// prod
	file, err := os.Open("/Users/<USER>/Documents/unified_customers_prod.csv")
	if err != nil {
		log.Fatalf("Failed to open file: %v", err)
	}
	defer file.Close()

	// Create CSV reader
	reader := csv.NewReader(file)

	// Read header
	header, err := reader.Read()
	if err != nil {
		log.Fatalf("Failed to read header: %v", err)
	}

	// Create a channel to limit concurrency
	semaphore := make(chan struct{}, maxConcurrent)
	var wg sync.WaitGroup

	// Process each row
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading record: %v", err)
			continue
		}

		// Create a map for easier access to fields
		row := make(map[string]string)
		for i, field := range header {
			if i < len(record) {
				row[field] = record[i]
			}
		}

		// Check if customer has a plan
		customerID := row["id"]
		plan := row["Plan"]
		createdDateStr := row["Created (UTC)"]

		if plan == "" || strings.TrimSpace(plan) == "" {
			// Acquire semaphore slot
			semaphore <- struct{}{}
			wg.Add(1)

			// Process in a goroutine
			go func(customerID, createdDateStr string) {
				defer wg.Done()
				defer func() { <-semaphore }() // Release semaphore slot when done

				fmt.Printf("Creating subscription for customer %s without a plan\n", customerID)

				// Calculate remaining trial days
				remainingDays := calculateRemainingTrialDays(createdDateStr)

				// Create subscription with professional plan
				params := &stripe.SubscriptionParams{
					Customer: stripe.String(customerID),
					Items: []*stripe.SubscriptionItemsParams{
						{
							Price: stripe.String(professionalPlanID),
						},
					},
					TrialSettings: &stripe.SubscriptionTrialSettingsParams{
						EndBehavior: &stripe.SubscriptionTrialSettingsEndBehaviorParams{
							MissingPaymentMethod: stripe.String(string(stripe.SubscriptionTrialSettingsEndBehaviorMissingPaymentMethodCancel)),
						},
					},
				}

				// Only set trial period if days remaining
				if remainingDays > 0 {
					params.TrialPeriodDays = stripe.Int64(remainingDays)
					fmt.Printf("Setting trial period to %d days for customer %s\n", remainingDays, customerID)
				} else {
					fmt.Printf("No trial period for customer %s (trial period expired)\n", customerID)
				}

				sub, err := subscription.New(params)
				if err != nil {
					log.Printf("Failed to create subscription for customer %s: %v", customerID, err)
				} else {
					log.Printf("Created subscription for customer %s: %s", customerID, sub.ID)
				}
			}(customerID, createdDateStr)
		} else {
			fmt.Printf("Customer %s already has plan: %s\n", customerID, plan)
		}
	}

	// Wait for all goroutines to complete
	wg.Wait()
	fmt.Println("Processing complete!")
}

// calculateRemainingTrialDays calculates remaining trial days based on creation date
func calculateRemainingTrialDays(createdDateStr string) int64 {
	// Parse the creation date
	createdDate, err := time.Parse("2006-01-02 15:04", createdDateStr)
	if err != nil {
		log.Printf("Error parsing date %s: %v, using default trial period", createdDateStr, err)
		return standardTrialDays
	}

	// Calculate days since creation
	daysSinceCreation := int64(time.Since(createdDate).Hours() / 24)

	// Calculate remaining trial days
	remainingDays := standardTrialDays - daysSinceCreation
	if remainingDays < 0 {
		remainingDays = 0
	}

	return remainingDays
}
