import argparse
import datetime
import json
import os
import pathlib

import matplotlib.pyplot as plt
import numpy as np
from sklearn.model_selection import GridSearchCV
from sklearn.neighbors import KernelDensity


def estimate_bandwidth(data: np.ndarray) -> float:
    """Estimate the bandwidth of the given data, removing outliers."""
    mean, std = np.mean(data), np.std(data)

    data = data[(data > mean - 2 * std) & (data < mean + 2 * std)].reshape(-1, 1)
    q3, q1 = np.percentile(data, [75.0, 25.0])  # type: ignore
    iqr = q3 - q1

    return 0.9 * min(np.std(data), iqr / 1.34) * (data.shape[1] ** (-0.2))


class User:
    def __init__(self, user_id: str, requests: list):
        self.user_id = user_id
        self.requests = np.array(requests)
        self.samples = np.array(
            list(parse_rows_into_samples(requests)), dtype=np.float32
        )  # Shape (num_requests, 3)

        print("Fitting KDEs for user ", user_id)
        gaps = self.get_request_gaps().reshape(-1, 1)
        self.request_gap_kde = KernelDensity(bandwidth=estimate_bandwidth(gaps))
        self.request_gap_kde.fit(gaps)

        burst_gaps = self.get_burst_gaps().reshape(-1, 1)
        self.burst_gap_kde = KernelDensity(bandwidth=estimate_bandwidth(burst_gaps))
        self.burst_gap_kde.fit(burst_gaps)
        print("Successfully fitted KDEs for user ", user_id)
        self.curr_request = None

    def has_sufficient_data(self):
        return len(self.samples) > 100 and len(self.get_burst_gaps()) > 10

    def get_best_kde(self, data: np.ndarray):
        """Return the best KDE for the given data."""
        params = {"bandwidth": np.logspace(-1, 1, 10)}
        grid = GridSearchCV(KernelDensity(), params)
        grid.fit(data)
        return grid.best_estimator_

    def get_request_gaps(self):
        """Return the time between each request."""
        return self.samples[:, 1]

    def get_burst_relative_times(self):
        """Return the time of request relative to start of burst."""
        return self.samples[:, 0]

    def get_burst_mask(self):
        """Return the mask of whether a request is the start of a new burst."""
        return self.samples[:, 2] != 0

    def get_burst_gaps(self):
        """Return the time between each burst."""
        # Return the request gap if the request is the start of a new burst
        return self.samples[self.get_burst_mask(), 1]

    def get_bursts(self):
        return np.split(self.requests, np.nonzero(self.get_burst_mask())[0])

    def export_to(self, filepath: pathlib.Path):
        with open(filepath, "w+") as f:
            print(f"Exporting profile for user {self.user_id} to {str(filepath)}")
            for burst in self.get_bursts():
                for row in burst:
                    f.write(
                        json.dumps(
                            {
                                "millisOffset": 0,
                                "prompt": row.get("prompt", ""),
                                "blobs": row.get("blobs", {}),
                            }
                        )
                        + "\n"
                    )

    def plot_request_gaps(self, plt, bins=200, range=(0, 2)):
        request_gaps = self.get_request_gaps()
        plt.hist(request_gaps, bins=bins, range=range, label="Request Gaps")
        plt.hist(
            self.request_gap_kde.sample(len(request_gaps)),
            bins=bins,
            range=range,
            alpha=0.6,
        )
        plt.set_ylabel("Count")
        plt.set_xlabel("Duration between requests")
        plt.set_title(self.user_id)

    def plot_burst_gaps(self, plt, bins=200, range=(0, 200)):
        burst_gaps = self.get_burst_gaps()
        plt.hist(burst_gaps, bins=bins, range=range, label="Duration between bursts")
        plt.hist(
            self.burst_gap_kde.sample(len(burst_gaps)),
            bins=bins,
            range=range,
            alpha=0.6,
        )
        plt.set_ylabel("Count")
        plt.set_xlabel("Duration between bursts")
        plt.set_title(self.user_id)

    def next_request(self):
        pass


def map_user_to_rows(rows: list) -> dict[str, list]:
    """Map the user IDs to all of their requests"""
    user_to_rows = {}
    for row in rows:
        user_id = row.get("user_id", "None")
        row["user_id"] = user_id
        row["time"] = datetime.datetime.fromtimestamp(row["time"])
        user_to_rows.setdefault(user_id, []).append(row)
    return user_to_rows


def ordered_rows(rows: list) -> list:
    """Sort the rows by time"""
    return sorted(rows, key=lambda x: x["time"])


def set_overlap(a: set, b: set) -> float:
    """Return the overlap of two sets"""
    a_or_b = a.union(b)
    a_and_b = a.intersection(b)
    if len(a_or_b) == 0:
        return 0
    return float(len(a_and_b)) / float(len(a_or_b))


def parse_rows_into_samples(
    rows: list,
    max_burst_duration: datetime.timedelta = datetime.timedelta(seconds=10),
    resolution: datetime.timedelta = datetime.timedelta(seconds=1),
):
    """Parse the row into samples.

    Returns
    - Time to start of burst
    - Time to previous request
    - Whether the request is the start of a new burst
    """
    ZERO_DURATION = datetime.timedelta(seconds=0) / resolution

    def diff_time(r1, r2):
        return (r1["time"] - r2["time"]) / resolution

    def blob_overlap(r1, r2):
        b1 = set((r1.get("blobs", {}) or {}).get("added", []))
        b2 = set((r2.get("blobs", {}) or {}).get("added", []))
        return set_overlap(b1, b2)

    last_row = None
    burst = []
    for row in rows:
        # New (and first) burst of activity
        if last_row is None:
            yield np.array([ZERO_DURATION, ZERO_DURATION, True], dtype=np.float32)
        # Last request and current request are too far apart, so a new burst started
        elif (row["time"] - last_row["time"]) > max_burst_duration:
            yield np.array(
                [ZERO_DURATION, diff_time(row, last_row), True], dtype=np.float32
            )
            burst = []
        # Last request and current request blobs are too different, a new burst started
        elif blob_overlap(row, last_row) < 0.5:
            yield np.array(
                [ZERO_DURATION, diff_time(row, last_row), True], dtype=np.float32
            )
            burst = []
        # In the middle of a burst of activity
        else:
            yield np.array(
                [diff_time(row, burst[0]), diff_time(row, last_row), False],
                dtype=np.float32,
            )
        last_row = row
        burst.append(row)


def main():
    """Generate profiles from the request event data that is stored on disk.

    This script assumes that the request event data is stored in a JSONL file,
    with each line representing a request pulled from the BigQuery table `request_event`.

    "User"s are parameterized users, and each user has a list of requests
    as its data source for fitting its model.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--project",
        type=str,
        required=True,
        default="system-services-prod",
        help="The project to use",
    )
    parser.add_argument(
        "--back",
        type=int,
        required=True,
        default=1,
        help="The number of days back in time to pull from",
    )
    parser.add_argument(
        "--root_dir",
        type=str,
        required=True,
        default=str(pathlib.Path(os.path.expanduser("~")) / "augment"),
        help="The output directory",
    )
    args = parser.parse_args()

    root_dir = pathlib.Path(args.root_dir)
    export_dir = root_dir / "profiles"
    export_dir.mkdir(exist_ok=True)

    back_duration_ms = args.back * 24 * 60 * 60 * 1000  # `back` days

    # Save the output locally in a JSON file
    with open(root_dir / f"request-event-past-{back_duration_ms}ms.jsonl", "r+") as f:
        rows = [json.loads(line) for line in f.readlines()]
        user_to_rows = {k: ordered_rows(v) for k, v in map_user_to_rows(rows).items()}
        users = [User(user, rows) for user, rows in user_to_rows.items()]
        users = [user for user in users if user.has_sufficient_data()]

        # Generate plots given the users that have enough data to plot
        plts, axs = plt.subplots(
            nrows=len(users), ncols=2, figsize=(5 * 2, 5 * len(users))
        )
        for i, user in enumerate(users):
            user.export_to(export_dir / f"user-{user.user_id}-requests.jsonl")
            user.plot_request_gaps(axs[i][0])
            user.plot_burst_gaps(axs[i][1])

        plts.savefig(str(root_dir / "request-burst-duration-per-user.png"), dpi=200)


if __name__ == "__main__":
    main()
