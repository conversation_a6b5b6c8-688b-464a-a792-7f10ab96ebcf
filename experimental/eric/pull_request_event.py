import argparse
import json
import os
import pathlib

from google.cloud import bigquery  # type: ignore


def get_request_events(client, back_duration_ms):
    """Get all the request events from the last `back_duration_ms` milliseconds.

    Returns
    - A list of rows
    """
    QUERY = """
  SELECT
    request_id,
    event_type,
    tenant,
    time,
    request_event.raw_json.blobs AS blobs,
    JSON_VALUE(request_event.raw_json, "$.user_id") AS user_id,
    JSON_VALUE(request_event.raw_json, "$.prompt") AS prompt
  FROM `system-services-prod.staging_request_insight_full_export_dataset.request_event`
    AS request_event
  WHERE
    (TIMESTAMP(time) BETWEEN TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL @back_duration_ms MILLISECOND) AND CURRENT_TIMESTAMP()) AND
    (JSON_VALUE(raw_json, '$.probe_only') IS NULL OR JSON_VALUE(raw_json, '$.probe_only') != 'true') AND
    (JSON_VALUE(raw_json, '$.user_id') != 'health-check-1' AND JSON_VALUE(raw_json, '$.user_id') IS NOT NULL) AND
    event_type = 'infer_request'
    """
    return client.query(
        QUERY,
        job_config=bigquery.QueryJobConfig(
            use_legacy_sql=False,
            query_parameters=[
                bigquery.ScalarQueryParameter(
                    "back_duration_ms", "INT64", back_duration_ms
                ),
            ],
        ),
    ).result()


def main():
    """Pulls the request events from BigQuery.

    This script pulls all events from the last `back_duration_ms` milliseconds.
    The output is saved in a JSONL file, where each row is a JSON representing the
    row pulled out of BigQuery. This is mainly used to help with speed of iteration in
    the development of the extraction pipeline.
    """
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--project",
        type=str,
        default="system-services-prod",
        help="The project to use",
    )
    parser.add_argument(
        "--back",
        type=int,
        default=1,
        help="The number of days back in time to pull from",
    )
    parser.add_argument(
        "--root_dir",
        type=str,
        default=str(pathlib.Path(os.path.expanduser("~")) / "augment"),
        help="The output directory",
    )
    args = parser.parse_args()

    root_dir = pathlib.Path(args.root_dir)
    project = args.project
    back_duration_ms = args.back * 24 * 60 * 60 * 1000  # `back` days

    # Save the output locally in a JSON file
    with open(root_dir / f"request-event-past-{back_duration_ms}ms.jsonl", "w+") as f:
        rows = get_request_events(
            client=bigquery.Client(project=project),
            back_duration_ms=back_duration_ms,
        )
        row_idx = 0
        for row in rows:
            print("Got new row: {} - {}".format(row_idx, row.get("request_id")))
            # Need to convert time to ISO format
            f.write(json.dumps({**dict(row), "time": row.get("time").timestamp()}))
            f.write("\n")
            row_idx += 1


if __name__ == "__main__":
    main()
