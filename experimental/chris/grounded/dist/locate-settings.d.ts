/**
 * Interface representing a VS Code settings location
 */
export interface SettingsLocation {
    filePath: string;
    scope: 'user' | 'remote-ssh' | 'container' | 'codespaces';
    exists: boolean;
    writable: boolean;
}
/**
 * Gets the potential settings.json paths based on the platform
 */
export declare const getPotentialSettingsPaths: () => string[];
/**
 * Checks if a file is accessible for reading/writing
 */
export declare const isFileAccessible: (filePath: string) => Promise<boolean>;
/**
 * Detects if we're in a remote environment
 */
export declare const isRemoteEnvironment: () => boolean;
/**
 * Get the scope of the VS Code environment
 */
export declare const getScope: () => "user" | "remote-ssh" | "container" | "codespaces";
/**
 * Locate all settings.json files
 */
export declare const locateSettingsFiles: (customPath?: string) => Promise<SettingsLocation[]>;
