import chalk from 'chalk';
import { execa } from 'execa';
/**
 * Formats a settings diff for display
 */
export const formatDiff = (diff) => {
    const key = chalk.cyan(diff.key);
    const oldValue = chalk.red(JSON.stringify(diff.oldValue));
    const newValue = chalk.green(JSON.stringify(diff.newValue));
    const action = diff.action === 'added' ? chalk.yellow('+ added') : chalk.blue('~ modified');
    return `${action} ${key}: ${oldValue} → ${newValue}`;
};
/**
 * Formats the result of a patch operation for display
 */
export const formatPatchResult = (result) => {
    const { filePath, diffs, success, error, isAlreadyDisabled, backupPath } = result;
    if (!success) {
        return chalk.red(`❌ Failed to update ${filePath}: ${error}`);
    }
    if (isAlreadyDisabled) {
        return chalk.gray(`✓ ${filePath} already has all AI features disabled`);
    }
    const output = [
        chalk.green(`✓ Successfully updated ${filePath}`),
    ];
    if (backupPath) {
        output.push(chalk.gray(`  Backup created at: ${backupPath}`));
    }
    if (diffs.length > 0) {
        output.push(chalk.yellow(`  Changes made (${diffs.length}):`));
        // Group diffs by action for cleaner display
        const added = diffs.filter(d => d.action === 'added');
        const modified = diffs.filter(d => d.action === 'modified');
        if (added.length > 0) {
            output.push(chalk.yellow('  Added:'));
            for (const diff of added) {
                output.push(`    ${formatDiff(diff)}`);
            }
        }
        if (modified.length > 0) {
            output.push(chalk.yellow('  Modified:'));
            for (const diff of modified) {
                output.push(`    ${formatDiff(diff)}`);
            }
        }
    }
    return output.join('\n');
};
/**
 * Checks if VS Code is running and suggests reload commands
 */
export const suggestVSCodeReload = async () => {
    try {
        // Check if VS Code is running (platform specific)
        let isVSCodeRunning = false;
        if (process.platform === 'win32') {
            const { stdout } = await execa('tasklist', ['/fi', 'imagename eq code.exe', '/nh']);
            isVSCodeRunning = stdout.includes('code.exe');
        }
        else {
            const { stdout } = await execa('pgrep', ['-f', 'code']);
            isVSCodeRunning = !!stdout.trim();
        }
        if (isVSCodeRunning) {
            return chalk.yellow('⚠️ VS Code is currently running. You may need to restart it or run:\n' +
                '  code --force-reload\n' +
                'for settings changes to take effect.');
        }
        return undefined;
    }
    catch (error) {
        // If we can't determine if VS Code is running, return a generic message
        return chalk.yellow('⚠️ Please restart VS Code or run "code --force-reload" for settings changes to take effect.');
    }
};
