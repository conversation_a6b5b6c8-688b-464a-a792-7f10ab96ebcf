import type { SettingsLocation } from './locate-settings.js';
/**
 * Represents a diff between original and modified settings
 */
export interface SettingsDiff {
    key: string;
    oldValue: any;
    newValue: any;
    action: 'added' | 'modified';
}
/**
 * Result of a patching operation
 */
export interface PatchResult {
    filePath: string;
    backupPath?: string;
    diffs: SettingsDiff[];
    success: boolean;
    error?: string;
    isAlreadyDisabled: boolean;
}
/**
 * Creates a timestamped backup filename
 */
export declare const createBackupFilename: (originalPath: string) => string;
/**
 * Creates a backup of the settings file
 */
export declare const backupSettingsFile: (settingsLocation: SettingsLocation) => Promise<string | undefined>;
/**
 * Restores a settings file from the most recent backup
 */
export declare const restoreFromBackup: (settingsLocation: SettingsLocation) => Promise<boolean>;
/**
 * Merges disable flags into the settings content
 */
export declare const mergeSettings: (settingsContent: string, disableFlags?: Record<string, boolean | string | number | Record<string, boolean>>) => {
    content: string;
    diffs: SettingsDiff[];
    isAlreadyDisabled: boolean;
};
/**
 * Patches the settings file by adding disable flags
 */
export declare const patchSettings: (settingsLocation: SettingsLocation, dryRun?: boolean) => Promise<PatchResult>;
