import fs from 'fs-extra';
import path from 'path';
import os from 'os';
/**
 * Gets the user home directory
 */
const getHomeDir = () => {
    return os.homedir();
};
/**
 * Gets the potential settings.json paths based on the platform
 */
export const getPotentialSettingsPaths = () => {
    const homeDir = getHomeDir();
    const platform = process.platform;
    const paths = [];
    // Windows
    if (platform === 'win32') {
        paths.push(path.join(process.env.APPDATA || '', 'Code', 'User', 'settings.json'));
        paths.push(path.join(process.env.APPDATA || '', 'Code - Insiders', 'User', 'settings.json'));
    }
    // macOS
    else if (platform === 'darwin') {
        paths.push(path.join(homeDir, 'Library', 'Application Support', 'Code', 'User', 'settings.json'));
        paths.push(path.join(homeDir, 'Library', 'Application Support', 'Code - Insiders', 'User', 'settings.json'));
    }
    // Linux and others
    else {
        paths.push(path.join(homeDir, '.config', 'Code', 'User', 'settings.json'));
        paths.push(path.join(homeDir, '.config', 'Code - Insiders', 'User', 'settings.json'));
    }
    return paths;
};
/**
 * Checks if a file is accessible for reading/writing
 */
export const isFileAccessible = async (filePath) => {
    try {
        // Check if we can read and write to the file
        await fs.access(filePath, fs.constants.R_OK | fs.constants.W_OK);
        return true;
    }
    catch (error) {
        return false;
    }
};
/**
 * Detects if we're in a remote environment
 */
export const isRemoteEnvironment = () => {
    // Check for VS Code remote environment variables
    return !!(process.env.VSCODE_IPC_HOOK_CLI ||
        process.env.VSCODE_REMOTE_CONTAINERS_SESSION_ID ||
        process.env.VSCODE_REMOTE_SSH_SESSION_ID ||
        process.env.CODESPACES);
};
/**
 * Get the scope of the VS Code environment
 */
export const getScope = () => {
    if (process.env.CODESPACES) {
        return 'codespaces';
    }
    if (process.env.VSCODE_REMOTE_CONTAINERS_SESSION_ID) {
        return 'container';
    }
    if (process.env.VSCODE_REMOTE_SSH_SESSION_ID) {
        return 'remote-ssh';
    }
    return 'user';
};
/**
 * Locate all settings.json files
 */
export const locateSettingsFiles = async (customPath) => {
    const result = [];
    // If a custom path is provided, check only that
    if (customPath) {
        const exists = await fs.pathExists(customPath);
        const writable = exists ? await isFileAccessible(customPath) : true; // If it doesn't exist yet, assume we can create it
        result.push({
            filePath: customPath,
            scope: 'user', // Assume user scope for custom paths
            exists,
            writable
        });
        return result;
    }
    // Otherwise look for all potential settings files
    const potentialPaths = getPotentialSettingsPaths();
    const scope = getScope();
    for (const filePath of potentialPaths) {
        const exists = await fs.pathExists(filePath);
        if (exists) {
            const writable = await isFileAccessible(filePath);
            result.push({
                filePath,
                scope,
                exists,
                writable
            });
        }
        else {
            // File doesn't exist yet, but we should include it as a potential location
            // Check if parent directory exists
            const dirPath = path.dirname(filePath);
            const dirExists = await fs.pathExists(dirPath);
            if (dirExists) {
                result.push({
                    filePath,
                    scope,
                    exists: false,
                    writable: true, // Assume we can create the file if the parent directory exists
                });
            }
        }
    }
    return result;
};
