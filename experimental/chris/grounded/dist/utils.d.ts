import type { SettingsDiff, <PERSON>R<PERSON>ult } from './patcher.js';
/**
 * Formats a settings diff for display
 */
export declare const formatDiff: (diff: SettingsDiff) => string;
/**
 * Formats the result of a patch operation for display
 */
export declare const formatPatchResult: (result: PatchResult) => string;
/**
 * Checks if VS Code is running and suggests reload commands
 */
export declare const suggestVSCodeReload: () => Promise<string | undefined>;
