{"name": "@augmentcode/grounded", "version": "0.1.0", "description": "CLI tool to disable GitHub Copilot and AI chat elements in VS Code", "main": "dist/index.js", "bin": {"grounded": "dist/index.js"}, "type": "module", "scripts": {"build": "tsc", "test": "vitest run", "test:watch": "vitest", "lint": "eslint src", "prepublishOnly": "npm run build", "release": "pnpm build && changeset publish", "changeset": "changeset"}, "keywords": ["vscode", "copilot", "github", "ai", "privacy"], "author": "AugmentCode", "license": "MIT", "files": ["dist", "README.md", "LICENSE"], "devDependencies": {"@changesets/cli": "^2.27.1", "@types/fs-extra": "^11.0.4", "@types/node": "^20.10.5", "eslint": "^8.56.0", "typescript": "^5.3.3", "vitest": "^1.0.4"}, "dependencies": {"chalk": "^5.3.0", "commander": "^11.1.0", "date-fns": "^2.30.0", "execa": "^8.0.1", "fs-extra": "^11.2.0", "jsonc-parser": "^3.2.0"}, "engines": {"node": ">=18"}}