# @augmentcode/grounded

A CLI tool to disable GitHub Copilot and AI chat elements in VS Code settings.

- ✅ Works on macOS, Linux, and Windows
- ✅ Finds the correct settings.json file (user, SSH, container, Codespaces)
- ✅ Creates automatic backups before making changes
- ✅ Preserves comments and formatting in your settings files
- ✅ Disables 20+ AI-related features with a single command

## Quick Start

```bash
# Run directly with npx
npx @augmentcode/grounded

# Or install globally
npm install -g @augmentcode/grounded
grounded
```

## Features

- **Cross-platform**: Automatically detects the correct settings.json file locations on macOS, Linux, and Windows
- **Environment-aware**: Works across multiple contexts (local, SSH, containers, Codespaces)
- **Safe**: Always creates a timestamped backup before modifying files
- **Idempotent**: Can be run multiple times safely, exits with code 0 if everything is already disabled
- **Preserves formatting**: Keeps your existing indentation, ordering, and comments
- **Informative**: Shows clear diffs of what changed
- **Reversible**: Easily restore from backups with the `--restore` flag

## Usage

```
npx @augmentcode/grounded [options]
```

### Options

| Option | Description |
|--------|-------------|
| `-d, --dry-run` | Preview changes without modifying files |
| `-r, --restore` | Restore settings from the most recent backup |
| `-f, --file <path>` | Specify a custom settings.json file path |
| `-s, --silent` | Suppress output except for errors |
| `-h, --help` | Display help information |
| `-v, --version` | Display version information |

## Disabled Settings

This tool disables the following AI-related features:

### GitHub Copilot Core

- `github.copilot.enable`: `false`
- `github.copilot.nextEditSuggestions.enabled`: `false`
- `github.copilot.editor.enableCodeActions`: `false`
- `github.copilot.renameSuggestions.triggerAutomatically`: `false`

### Chat Features

- `chat.commandCenter.enabled`: `false`
- `chat.setupFromDialog`: `false`
- `chat.agent.enabled`: `false`
- `chat.extensionTools.enabled`: `false`
- `chat.focusWindowOnConfirmation`: `false`
- `chat.mcp.enabled`: `false`
- `chat.implicitContext.enabled`: `false`
- `chat.tools.autoApprove`: `false`

### Copilot Chat

- `github.copilot.chat.runCommand.enabled`: `false`
- `github.copilot.chat.followUps`: `false`
- `github.copilot.chat.codesearch.enabled`: `false`
- `github.copilot.chat.agent.autoFix`: `false`
- `github.copilot.chat.agent.runTasks`: `false`

### Editor & Terminal

- `editor.inlineSuggest.enabled`: `false`
- `remote.SSH.experimental.chat`: `false`
- `terminal.integrated.initialHint`: `false`

### Telemetry

- `telemetry.feedback.enabled`: `false`
- `telemetry.telemetryLevel`: `off`

## Examples

### Basic Usage

```bash
# Disable all AI features
npx @augmentcode/grounded
```

### Preview Changes

```bash
# Show what would be changed without modifying files
npx @augmentcode/grounded --dry-run
```

### Restore from Backup

```bash
# Revert to the previous settings
npx @augmentcode/grounded --restore
```

### Specify Custom Settings File

```bash
# Use a specific settings.json file
npx @augmentcode/grounded --file /path/to/custom/settings.json
```

## Why Use This Tool?

- **Privacy**: Prevent accidental code uploads to AI services
- **Licensing**: Avoid potential licensing issues with AI-generated code
- **Performance**: Disable resource-intensive features
- **Distraction-free**: Remove chat prompts and UI elements
- **Compliance**: Meet organizational requirements for AI tool usage

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

MIT