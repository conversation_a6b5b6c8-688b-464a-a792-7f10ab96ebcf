import fs from 'fs-extra';
import path from 'path';
import * as jsonc from 'jsonc-parser';
import { format } from 'date-fns';
import { DISABLE_FLAGS, BACKUP_SUFFIX, BACKUP_TIMESTAMP_FORMAT } from './constants.js';
import type { SettingsLocation } from './locate-settings.js';

/**
 * Represents a diff between original and modified settings
 */
export interface SettingsDiff {
  key: string;
  oldValue: any;
  newValue: any;
  action: 'added' | 'modified';
}

/**
 * Result of a patching operation
 */
export interface PatchResult {
  filePath: string;
  backupPath?: string;
  diffs: SettingsDiff[];
  success: boolean;
  error?: string;
  isAlreadyDisabled: boolean;
}

/**
 * Creates a timestamped backup filename
 */
export const createBackupFilename = (originalPath: string): string => {
  const timestamp = format(new Date(), BACKUP_TIMESTAMP_FORMAT);
  return `${originalPath}${BACKUP_SUFFIX}-${timestamp}`;
};

/**
 * Creates a backup of the settings file
 */
export const backupSettingsFile = async (settingsLocation: SettingsLocation): Promise<string | undefined> => {
  const { filePath, exists } = settingsLocation;
  
  // If file doesn't exist, there's nothing to backup
  if (!exists) return undefined;
  
  const backupPath = createBackupFilename(filePath);
  
  try {
    await fs.copy(filePath, backupPath);
    return backupPath;
  } catch (error) {
    throw new Error(`Failed to create backup at ${backupPath}: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Restores a settings file from the most recent backup
 */
export const restoreFromBackup = async (settingsLocation: SettingsLocation): Promise<boolean> => {
  const { filePath } = settingsLocation;
  const dirPath = path.dirname(filePath);
  const baseName = path.basename(filePath);
  
  try {
    // List all files in the directory
    const files = await fs.readdir(dirPath);
    
    // Filter files to find backups of this settings file
    const backupRegex = new RegExp(`^${baseName}\\${BACKUP_SUFFIX}-\\d{8}-\\d{6}$`);
    const backups = files
      .filter(file => backupRegex.test(file))
      .map(file => path.join(dirPath, file))
      .sort()
      .reverse(); // Sort descending to get most recent first
    
    if (backups.length === 0) {
      throw new Error(`No backups found for ${filePath}`);
    }
    
    // Restore from the most recent backup
    const mostRecentBackup = backups[0];
    await fs.copy(mostRecentBackup, filePath, { overwrite: true });
    
    return true;
  } catch (error) {
    throw new Error(`Failed to restore from backup: ${error instanceof Error ? error.message : String(error)}`);
  }
};

/**
 * Merges disable flags into the settings content
 */
export const mergeSettings = (
  settingsContent: string,
  disableFlags: Record<string, boolean | string | number | Record<string, boolean>> = DISABLE_FLAGS
): { content: string; diffs: SettingsDiff[]; isAlreadyDisabled: boolean } => {
  // Parse the settings with jsonc to preserve comments
  const parsed = jsonc.parse(settingsContent || '{}');
  const diffs: SettingsDiff[] = [];
  let isAlreadyDisabled = true;
  
  // Check each flag to see if it needs to be added or modified
  for (const [key, value] of Object.entries(disableFlags)) {
    const currentValue = parsed[key];
    const action = currentValue === undefined ? 'added' : 'modified';
    
    // Only add to diffs if the current value is different from what we want
    if (currentValue !== value) {
      diffs.push({
        key,
        oldValue: currentValue,
        newValue: value,
        action,
      });
      
      // Set to the new value
      parsed[key] = value;
      isAlreadyDisabled = false;
    }
  }
  
  // If there are changes, stringify the modified object
  if (diffs.length > 0) {
    // Create edit operations to apply to the original text to preserve formatting and comments
    const edits = jsonc.modify(settingsContent || '{}', [], parsed, {
      formattingOptions: {
        insertSpaces: true,
        tabSize: 2
      }
    });
    
    // Apply the edits to the original text
    const updatedContent = jsonc.applyEdits(settingsContent || '{}', edits);
    return { content: updatedContent, diffs, isAlreadyDisabled };
  }
  
  // No changes needed
  return { content: settingsContent, diffs, isAlreadyDisabled };
};

/**
 * Patches the settings file by adding disable flags
 */
export const patchSettings = async (
  settingsLocation: SettingsLocation,
  dryRun = false
): Promise<PatchResult> => {
  const { filePath, exists, writable } = settingsLocation;
  
  // Result object with default values
  const result: PatchResult = {
    filePath,
    diffs: [],
    success: false,
    isAlreadyDisabled: true,
  };
  
  try {
    // Check if file is writable
    if (exists && !writable) {
      result.error = `File ${filePath} exists but is not writable (possibly policy-controlled)`;
      return result;
    }
    
    // Read existing content or use empty object if file doesn't exist
    let currentContent = '{}';
    if (exists) {
      currentContent = await fs.readFile(filePath, 'utf8');
    }
    
    // Merge settings with disable flags
    const { content: updatedContent, diffs, isAlreadyDisabled } = mergeSettings(currentContent);
    
    result.diffs = diffs;
    result.isAlreadyDisabled = isAlreadyDisabled;
    
    // If no changes or dry run, return early
    if (isAlreadyDisabled || dryRun) {
      result.success = true;
      return result;
    }
    
    // Create backup if file exists
    if (exists) {
      result.backupPath = await backupSettingsFile(settingsLocation);
    }
    
    // Write the updated content
    await fs.outputFile(filePath, updatedContent);
    
    result.success = true;
    return result;
  } catch (error) {
    result.error = `Failed to patch settings: ${error instanceof Error ? error.message : String(error)}`;
    return result;
  }
};