#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { locateSettingsFiles } from './locate-settings.js';
import { patchSettings, restoreFromBackup } from './patcher.js';
import { formatPatchResult, suggestVSCodeReload } from './utils.js';
import { DISABLE_FLAGS } from './constants.js';

// Define the CLI program
const program = new Command();

// Set version from package.json
program
  .name('grounded')
  .description('CLI tool to disable GitHub Copilot and AI chat elements in VS Code')
  .version('0.1.0');

// Add options
program
  .option('-d, --dry-run', 'Preview changes without modifying files')
  .option('-r, --restore', 'Restore settings from the most recent backup')
  .option('-f, --file <path>', 'Specify a custom settings.json file path')
  .option('-s, --silent', 'Suppress output except for errors');

program.parse();

const options = program.opts();

// Main function
async function main() {
  try {
    // Locate settings files
    const settingsLocations = await locateSettingsFiles(options.file);
    
    if (settingsLocations.length === 0) {
      console.error(chalk.red('❌ No VS Code settings files found. Please check your installation or specify a custom path.'));
      process.exit(1);
    }
    
    // Report found locations unless silent
    if (!options.silent) {
      console.log(chalk.blue(`Found ${settingsLocations.length} VS Code settings location(s):`));
      for (const loc of settingsLocations) {
        console.log(`  ${chalk.cyan(loc.filePath)} (${loc.scope}, ${loc.exists ? 'exists' : 'will be created'}${!loc.writable ? ', read-only' : ''})`);
      }
      console.log();
    }
    
    // Process each location
    let successCount = 0;
    let alreadyDisabledCount = 0;
    let errorCount = 0;
    
    for (const settingsLocation of settingsLocations) {
      try {
        if (options.restore) {
          // Restore from backup
          if (!options.silent) {
            console.log(chalk.yellow(`Restoring ${settingsLocation.filePath} from backup...`));
          }
          
          const restored = await restoreFromBackup(settingsLocation);
          
          if (restored) {
            if (!options.silent) {
              console.log(chalk.green(`✓ Successfully restored ${settingsLocation.filePath} from backup`));
            }
            successCount++;
          }
        } else {
          // Apply the patch
          const result = await patchSettings(settingsLocation, options.dryRun);
          
          if (!options.silent) {
            console.log(formatPatchResult(result));
          }
          
          if (result.success) {
            if (result.isAlreadyDisabled) {
              alreadyDisabledCount++;
            } else {
              successCount++;
            }
          } else {
            errorCount++;
          }
        }
      } catch (error) {
        console.error(chalk.red(`❌ Error processing ${settingsLocation.filePath}: ${error instanceof Error ? error.message : String(error)}`));
        errorCount++;
      }
    }
    
    // Print summary for dry runs
    if (options.dryRun && !options.silent) {
      console.log(chalk.yellow('\n⚠️ This was a dry run, no files were modified'));
    }
    
    // Print reload suggestion if needed
    if (successCount > 0 && !options.dryRun && !options.silent) {
      const reloadSuggestion = await suggestVSCodeReload();
      if (reloadSuggestion) {
        console.log(`\n${reloadSuggestion}`);
      }
    }
    
    // Print summary unless silent
    if (!options.silent) {
      console.log(chalk.blue('\nSummary:'));
      if (options.restore) {
        console.log(`  ${chalk.green(`✓ Restored: ${successCount}`)} / ${chalk.red(`❌ Failed: ${errorCount}`)}`);
      } else {
        console.log(`  ${chalk.green(`✓ Updated: ${successCount}`)} / ${chalk.gray(`✓ Already disabled: ${alreadyDisabledCount}`)} / ${chalk.red(`❌ Failed: ${errorCount}`)}`);
      }
      
      // Print all disabled flags in a structured form for reference
      if (!options.restore && (successCount > 0 || alreadyDisabledCount > 0)) {
        console.log(chalk.blue('\nDisabled AI features:'));
        
        // Group by category for better display
        const categories = {
          'GitHub Copilot Core': Object.entries(DISABLE_FLAGS).filter(([key]) => key.startsWith('github.copilot') && !key.includes('chat')),
          'Chat Features': Object.entries(DISABLE_FLAGS).filter(([key]) => key.startsWith('chat.')),
          'Copilot Chat': Object.entries(DISABLE_FLAGS).filter(([key]) => key.includes('copilot.chat')),
          'Editor & Terminal': Object.entries(DISABLE_FLAGS).filter(([key]) => key.startsWith('editor.') || key.startsWith('terminal.') || key.startsWith('remote.')),
          'Telemetry': Object.entries(DISABLE_FLAGS).filter(([key]) => key.startsWith('telemetry.')),
        };
        
        for (const [category, entries] of Object.entries(categories)) {
          if (entries.length > 0) {
            console.log(chalk.cyan(`  ${category}:`));
            for (const [key, value] of entries) {
              console.log(`    ${chalk.gray(key)}: ${value === false ? chalk.red('disabled') : chalk.yellow(value)}`);
            }
          }
        }
      }
    }
    
    // Exit with success if any file was successfully processed or was already in the right state
    if (successCount > 0 || alreadyDisabledCount > 0) {
      process.exit(0);
    } else if (errorCount > 0) {
      process.exit(1);
    } else {
      process.exit(2); // No files processed
    }
  } catch (error) {
    console.error(chalk.red(`❌ Unexpected error: ${error instanceof Error ? error.message : String(error)}`));
    process.exit(1);
  }
}

main();