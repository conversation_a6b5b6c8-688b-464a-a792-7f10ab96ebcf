{
  "editor.fontSize": 14,
  "editor.tabSize": 2,
  // All AI features are disabled
  "github.copilot.enable": {"*": false},
  "github.copilot.nextEditSuggestions.enabled": false,
  "github.copilot.editor.enableCodeActions": false,
  "github.copilot.renameSuggestions.triggerAutomatically": false,
  "github.copilot.suggestSettings": false,
  
  "editor.inlineSuggest.enabled": false,
  "editor.inlineSuggest.edits.allowCodeShifting": false, 
  "editor.inlineSuggest.edits.renderSideBySide": false,
  
  "chat.commandCenter.enabled": false,
  "chat.setupFromDialog": false,
  "chat.agent.enabled": false,
  "chat.extensionTools.enabled": false,
  "chat.focusWindowOnConfirmation": false,
  "chat.mcp.enabled": false,
  "chat.implicitContext.enabled": false,
  "chat.tools.autoApprove": false,
  "chat.promptFilesLocations": false,
  "chat.terminalChatLocation": false,
  "chat.agent.maxRequests": 0,
  
  "github.copilot.chat.runCommand.enabled": false,
  "github.copilot.chat.followUps": false,
  "github.copilot.chat.codesearch.enabled": false,
  "github.copilot.chat.useProjectTemplates": false,
  "github.copilot.chat.scopeSelection": false,
  "github.copilot.chat.localeOverride": false,
  "github.copilot.chat.codeGeneration.useInstructionFiles": false,
  "github.copilot.chat.terminalChatLocation": false,
  "github.copilot.chat.generateTests.codeLens": false,
  "github.copilot.chat.setupTests.enabled": false,
  "github.copilot.chat.search.semanticTextResults": false,
  "github.copilot.chat.startDebugging.enabled": false,
  "github.copilot.chat.reviewSelection.enabled": false,
  
  "github.copilot.chat.agent.autoFix": false,
  "github.copilot.chat.agent.runTasks": false,
  "github.copilot.chat.agent.thinkingTool": false,
  "github.copilot.chat.newWorkspaceCreation.enabled": false,
  
  "workbench.commandPalette.experimental.askChatLocation": false,
  
  "remote.SSH.experimental.chat": false,
  "terminal.integrated.initialHint": false,
  
  "telemetry.feedback.enabled": false,
  "telemetry.telemetryLevel": "off"
}