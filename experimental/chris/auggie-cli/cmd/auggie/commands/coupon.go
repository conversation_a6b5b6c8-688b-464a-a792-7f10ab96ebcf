package commands

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	"github.com/augmentcode/auggie/pkg/coupon"
	"github.com/spf13/cobra"
)

// GetCouponCmd returns the coupon command
func GetCouponCmd() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "coupon",
		Short: "Manage Stripe coupons",
		Long: `Create and manage Stripe coupons.
Supports creating single-use and multi-use coupons, and applying them to customers.`,
	}

	// Add subcommands
	cmd.AddCommand(getCouponCreateCmd())
	cmd.AddCommand(getCouponApplyCmd())

	return cmd
}

func getCouponCreateCmd() *cobra.Command {
	var (
		creatorEmail string
		reason       string
		productID    string
		prefix       string
		name         string
		maxUses      int64
	)

	cmd := &cobra.Command{
		Use:   "create",
		Short: "Create a new coupon",
		Long:  "Create a new coupon and output the coupon code",
		RunE: func(cmd *cobra.Command, args []string) error {
			stripeKey := os.Getenv("STRIPE_KEY")
			if stripeKey == "" {
				return fmt.Errorf("STRIPE_KEY environment variable is required")
			}

			c, err := coupon.New(stripeKey, productID, prefix)
			if err != nil {
				return fmt.Errorf("failed to initialize coupon: %w", err)
			}

			// Generate the base coupon code
			couponCode := c.GenerateCouponCode(creatorEmail, reason)

			// Create the coupon in Stripe and get the salted code
			saltedCode, err := c.CreateCoupon(couponCode, 100, name, maxUses)
			if err != nil {
				return fmt.Errorf("failed to create coupon: %w", err)
			}

			// Output the salted coupon code
			fmt.Println(saltedCode)
			return nil
		},
	}

	// Required flags
	cmd.Flags().StringVar(&creatorEmail, "creator", "", "Creator's email address (required)")
	cmd.Flags().StringVar(&reason, "reason", "", "Reason for the discount (required)")
	cmd.Flags().StringVar(&productID, "product", "", "Stripe product ID (required)")
	cmd.Flags().StringVar(&prefix, "prefix", "", "Prefix for coupon codes (required)")

	// Optional flags
	cmd.Flags().StringVar(&name, "name", "", "Name for the coupon")
	cmd.Flags().Int64Var(&maxUses, "max-uses", 1, "Maximum number of times the coupon can be used")

	cmd.MarkFlagRequired("creator")
	cmd.MarkFlagRequired("reason")
	cmd.MarkFlagRequired("product")
	cmd.MarkFlagRequired("prefix")

	return cmd
}

func getCouponApplyCmd() *cobra.Command {
	var (
		creatorEmail string
		reason       string
		productID    string
		prefix       string
		name         string
		maxUses      int64
		csvFile      string
	)

	cmd := &cobra.Command{
		Use:   "apply [uuid...]",
		Short: "Apply a coupon to users",
		Long: `Apply a coupon to one or more users specified by UUID.
Either provide UUIDs as arguments or use the --csv flag to read from a file.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			stripeKey := os.Getenv("STRIPE_KEY")
			if stripeKey == "" {
				return fmt.Errorf("STRIPE_KEY environment variable is required")
			}

			// Get UUIDs either from command line args or CSV file
			var uuids []string
			var err error
			if csvFile != "" {
				uuids, err = readUUIDsFromCSV(csvFile)
				if err != nil {
					return fmt.Errorf("failed to read CSV file: %w", err)
				}
			} else {
				uuids = args
			}

			if len(uuids) == 0 {
				return fmt.Errorf("no UUIDs provided. Either provide UUIDs as arguments or use --csv flag")
			}

			c, err := coupon.New(stripeKey, productID, prefix)
			if err != nil {
				return fmt.Errorf("failed to initialize coupon: %w", err)
			}

			// Apply discount
			req := coupon.ApplyDiscountRequest{
				CustomerUUIDs:  uuids,
				CreatorEmail:   creatorEmail,
				Reason:         reason,
				DiscountAmount: 100,
				CouponName:     name,
				MaxUses:        maxUses,
			}

			if err := c.ApplyDiscount(req); err != nil {
				return fmt.Errorf("failed to apply discount: %w", err)
			}

			fmt.Printf("Successfully applied discounts to %d customers\n", len(uuids))
			return nil
		},
	}

	// Required flags
	cmd.Flags().StringVar(&creatorEmail, "creator", "", "Creator's email address (required)")
	cmd.Flags().StringVar(&reason, "reason", "", "Reason for the discount (required)")
	cmd.Flags().StringVar(&productID, "product", "", "Stripe product ID (required)")
	cmd.Flags().StringVar(&prefix, "prefix", "", "Prefix for coupon codes (required)")

	// Optional flags
	cmd.Flags().StringVar(&name, "name", "", "Name for the coupon")
	cmd.Flags().Int64Var(&maxUses, "max-uses", 1, "Maximum number of times the coupon can be used")
	cmd.Flags().StringVar(&csvFile, "csv", "", "Path to CSV file containing UUIDs")

	cmd.MarkFlagRequired("creator")
	cmd.MarkFlagRequired("reason")
	cmd.MarkFlagRequired("product")
	cmd.MarkFlagRequired("prefix")

	return cmd
}

func readUUIDsFromCSV(filepath string) ([]string, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("opening CSV file: %w", err)
	}
	defer file.Close()

	var uuids []string
	// Read each line as a UUID
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		uuid := strings.TrimSpace(scanner.Text())
		if uuid != "" {
			uuids = append(uuids, uuid)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("reading CSV file: %w", err)
	}

	return uuids, nil
}
