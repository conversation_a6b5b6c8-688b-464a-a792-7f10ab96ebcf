package commands

import (
	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "auggie",
	Short: "Auggie - A collection of Augment Code utilities",
	Long: `Auggie is a CLI tool that provides various utilities for Augment Code services.
Currently supports:
- Coupon: Create and manage Stripe coupons`,
}

// Execute runs the root command
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	// Add subcommands here
	rootCmd.AddCommand(GetCouponCmd())
}
