# Auggie CLI

A command line tool for Augment Code services.

## Installation

```bash
go install github.com/augmentcode/couponer-go/cmd/auggie@latest
```

## Commands

Currently supports:

- `coupon`: Create and manage Stripe coupons

## Coupon Command Usage

The coupon command has two subcommands:

### Create a coupon

Generates and creates a new coupon, outputting the coupon code:

```bash
auggie coupon create --prefix=<prefix> --creator=<email> --reason=<reason> --product=<productId> [--name="Coupon Name"] [--max-uses=N]
```

### Apply a coupon

Creates and applies a coupon to one or more users:

```bash
# Apply to list of users
auggie coupon apply --prefix=<prefix> --creator=<email> --reason=<reason> --product=<productId> [--name="Coupon Name"] [--max-uses=N] <list of userIds>

# Apply using CSV file
auggie coupon apply --csv=<path> --prefix=<prefix> --creator=<email> --reason=<reason> --product=<productId> [--name="Coupon Name"] [--max-uses=N]
```

## Specifications

- Uses Stripe for subscription billing management
- Applies 100% discount code to subscriptions with a specific product
- Coupons are limited to one-time use by default
- Supports configurable maximum number of uses via --max-uses flag
- Coupons are restricted to specific products
- Works with both active and trialing subscriptions

### Coupon Code Format

Coupon codes follow the format:

```
{prefix}-{base64(creator_email)}-{base64(reason)}-{random_salt}
```

Where:

- `prefix`: User-defined prefix for the coupon to quickly identify purpose/source
- `creator_email`: Base64-encoded email of the person creating the coupon
- `reason`: Base64-encoded reason for the discount
- `random_salt`: Random 6-character string to ensure uniqueness

## Process Flow

1. For 'create':

   - Generate unique coupon code with random salt
   - Create coupon in Stripe with specified max uses (default: 1)
   - Output the coupon code

2. For 'apply':
   - Take a list of UUIDs (from arguments or CSV)
   - For each UUID:
     1. Find the customer ID in Stripe
     2. Get their subscription
     3. Generate unique coupon code
     4. If their subscription includes the specified product, apply the discount code

## Examples

Create a single-use coupon (default):

```bash
auggie coupon create --creator=<EMAIL> --reason=holiday2023 --product=prod_123 --prefix=HOLIDAY --name="Holiday Special"
```

Create a multi-use coupon:

```bash
auggie coupon create --creator=<EMAIL> --reason=holiday2023 --product=prod_123 --prefix=HOLIDAY --name="Holiday Special" --max-uses=5
```

Apply coupon to specific users:

```bash
auggie coupon apply --creator=<EMAIL> --reason=holiday2023 --product=prod_123 --prefix=HOLIDAY user_uuid1 user_uuid2
```

Apply multi-use coupon using CSV file:

```bash
auggie coupon apply --creator=<EMAIL> --reason=holiday2023 --product=prod_123 --prefix=HOLIDAY --max-uses=10 --csv=users.csv
```

## Environment Variables

- `STRIPE_KEY`: Your Stripe API key (required)
