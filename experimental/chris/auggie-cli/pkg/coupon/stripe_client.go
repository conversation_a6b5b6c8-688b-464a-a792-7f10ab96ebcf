package coupon

import (
	"github.com/stripe/stripe-go/v74"
	"github.com/stripe/stripe-go/v74/coupon"
	"github.com/stripe/stripe-go/v74/customer"
	"github.com/stripe/stripe-go/v74/subscription"
)

// StripeClient interface defines the methods we need to mock
type StripeClient interface {
	CreateCoupon(params *stripe.CouponParams) (*stripe.Coupon, error)
	UpdateCustomer(customerID string, params *stripe.CustomerParams) (*stripe.Customer, error)
	FindCustomerByUUID(uuid string) (*stripe.Customer, error)
	GetActiveSubscription(customerID string) (*stripe.Subscription, error)
}

// DefaultStripeClient implements StripeClient with actual Stripe API calls
type DefaultStripeClient struct{}

func (c *DefaultStripeClient) CreateCoupon(params *stripe.CouponParams) (*stripe.Coupon, error) {
	return coupon.New(params)
}

func (c *DefaultStripeClient) UpdateCustomer(customerID string, params *stripe.CustomerParams) (*stripe.Customer, error) {
	return customer.Update(customerID, params)
}

func (c *DefaultStripeClient) FindCustomerByUUID(uuid string) (*stripe.Customer, error) {
	params := &stripe.CustomerListParams{}
	params.Filters.AddFilter("metadata[uuid]", "", uuid)

	i := customer.List(params)
	for i.Next() {
		return i.Customer(), nil
	}

	return nil, ErrCustomerNotFound
}

func (c *DefaultStripeClient) GetActiveSubscription(customerID string) (*stripe.Subscription, error) {
	params := &stripe.SubscriptionListParams{
		Customer: stripe.String(customerID),
	}
	params.Filters.AddFilter("status", "", "active")
	params.Filters.AddFilter("status", "", "trialing")

	i := subscription.List(params)
	for i.Next() {
		return i.Subscription(), nil
	}

	return nil, ErrNoActiveSubscription
}
