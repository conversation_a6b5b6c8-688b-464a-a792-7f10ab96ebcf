package coupon

import (
	"encoding/base64"
	"fmt"
	"strings"
)

// GenerateCouponCode creates a unique coupon code in the format: {prefix}-{base64(creator_email)}-{base64(reason)}
func (c *Couponer) GenerateCouponCode(creatorEmail, reason string) string {
	creatorB64 := base64.StdEncoding.EncodeToString([]byte(creatorEmail))
	reasonB64 := base64.StdEncoding.EncodeToString([]byte(reason))

	return fmt.Sprintf("%s-%s-%s",
		c.prefix,
		strings.TrimRight(creatorB64, "="),
		strings.TrimRight(reasonB64, "="))
}

// CreateCoupon creates a new coupon without applying it
func (c *Couponer) CreateCoupon(couponCode string, discountAmount int64, couponName string, maxUses int64) (string, error) {
	return c.createCoupon(couponCode, discountAmount, couponName, maxUses)
}

// ApplyDiscount applies a discount to customer subscriptions
func (c *Couponer) ApplyDiscount(req ApplyDiscountRequest) error {
	if !isValidEmail(req.CreatorEmail) {
		return ErrInvalidEmail
	}

	for _, uuid := range req.CustomerUUIDs {
		customer, err := c.findCustomerByUUID(uuid)
		if err != nil {
			return fmt.Errorf("finding customer %s: %w", uuid, err)
		}

		sub, err := c.getActiveSubscription(customer.ID)
		if err != nil {
			return fmt.Errorf("getting subscription for customer %s: %w", uuid, err)
		}

		if !c.hasProduct(sub, c.productID) {
			return ErrProductNotIncluded
		}

		couponCode := c.GenerateCouponCode(req.CreatorEmail, req.Reason)

		// Create the coupon and get the salted code
		saltedCode, err := c.createCoupon(couponCode, req.DiscountAmount, req.CouponName, req.MaxUses)
		if err != nil {
			return fmt.Errorf("creating coupon for customer %s: %w", uuid, err)
		}

		// Apply the salted code
		if err := c.applyCoupon(customer.ID, saltedCode); err != nil {
			return fmt.Errorf("applying coupon for customer %s: %w", uuid, err)
		}
	}

	return nil
}
