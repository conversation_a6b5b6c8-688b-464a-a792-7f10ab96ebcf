package coupon

import "errors"

var (
	ErrMissingStripeKey     = errors.New("stripe key is required")
	ErrMissingProductID     = errors.New("product ID is required")
	ErrMissingPrefix        = errors.New("prefix is required")
	ErrMissingClient        = errors.New("stripe client is required")
	ErrInvalidEmail         = errors.New("invalid email address")
	ErrCustomerNotFound     = errors.New("customer not found")
	ErrNoActiveSubscription = errors.New("no active subscription found")
	ErrProductNotIncluded   = errors.New("product not included in subscription")
)
