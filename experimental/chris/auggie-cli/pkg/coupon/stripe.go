package coupon

import (
	"crypto/rand"
	"encoding/hex"
	"github.com/stripe/stripe-go/v74"
)

func (c *Couponer) findCustomerByUUID(uuid string) (*stripe.Customer, error) {
	return c.client.FindCustomerByUUID(uuid)
}

func (c *Couponer) getActiveSubscription(customerID string) (*stripe.Subscription, error) {
	return c.client.GetActiveSubscription(customerID)
}

func (c *Couponer) hasProduct(sub *stripe.Subscription, productID string) bool {
	if sub == nil {
		return false
	}
	for _, item := range sub.Items.Data {
		if item.Price.Product.ID == productID {
			return true
		}
	}
	return false
}

// generateSalt creates a random 6-character string
func generateSalt() (string, error) {
	bytes := make([]byte, 3) // 3 bytes = 6 hex characters
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func (c *Couponer) createCoupon(couponCode string, discountAmount int64, couponName string, maxUses int64) (string, error) {
	salt, err := generateSalt()
	if err != nil {
		return "", err
	}

	// Add salt to coupon code
	saltedCode := couponCode + "-" + salt

	couponParams := &stripe.CouponParams{
		ID:             stripe.String(saltedCode),
		Duration:       stripe.String(string(stripe.CouponDurationForever)),
		PercentOff:     stripe.Float64(float64(discountAmount)),
		MaxRedemptions: stripe.Int64(maxUses),
		AppliesTo: &stripe.CouponAppliesToParams{
			Products: []*string{stripe.String(c.productID)},
		},
	}

	// Add name if provided
	if couponName != "" {
		couponParams.Name = stripe.String(couponName)
	}

	_, err = c.client.CreateCoupon(couponParams)
	if err != nil {
		return "", err
	}

	return saltedCode, nil
}

func (c *Couponer) applyCoupon(customerID, couponCode string) error {
	customerParams := &stripe.CustomerParams{
		Coupon: stripe.String(couponCode),
	}
	_, err := c.client.UpdateCustomer(customerID, customerParams)
	return err
}
