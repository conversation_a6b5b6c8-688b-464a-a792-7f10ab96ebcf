package coupon

import (
	"github.com/stripe/stripe-go/v74"
	"testing"
)

func newTestCouponer() *Couponer {
	mockClient := &MockStripeClient{
		CreateCouponFunc: func(params *stripe.CouponParams) (*stripe.Coupon, error) {
			return &stripe.Coupon{ID: *params.ID}, nil
		},
		UpdateCustomerFunc: func(customerID string, params *stripe.CustomerParams) (*stripe.Customer, error) {
			return &stripe.Customer{ID: customerID}, nil
		},
		FindCustomerByUUIDFunc: func(uuid string) (*stripe.Customer, error) {
			return &stripe.Customer{ID: "cus_" + uuid}, nil
		},
		GetActiveSubscriptionFunc: func(customerID string) (*stripe.Subscription, error) {
			return &stripe.Subscription{
				ID: "sub_" + customerID,
				Items: &stripe.SubscriptionItemList{
					Data: []*stripe.SubscriptionItem{
						{
							Price: &stripe.Price{
								Product: &stripe.Product{ID: "prod_123"},
							},
						},
					},
				},
			}, nil
		},
	}

	c, _ := NewWithClient("prod_123", "test", mockClient)
	return c
}

func TestGenerateCouponCode(t *testing.T) {
	c := newTestCouponer()

	tests := []struct {
		name         string
		creatorEmail string
		reason       string
		want         string
	}{
		{
			name:         "basic case",
			creatorEmail: "<EMAIL>",
			reason:       "special-discount",
			want:         "test-Y3JlYXRvckBleGFtcGxlLmNvbQ-c3BlY2lhbC1kaXNjb3VudA",
		},
		{
			name:         "with special characters",
			creatorEmail: "<EMAIL>",
			reason:       "holiday.2023",
			want:         "test-Y3JlYXRvcit0ZXN0QGV4YW1wbGUuY29t-aG9saWRheS4yMDIz",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := c.GenerateCouponCode(tt.creatorEmail, tt.reason)
			if got != tt.want {
				t.Errorf("GenerateCouponCode() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCreateCoupon(t *testing.T) {
	c := newTestCouponer()

	tests := []struct {
		name        string
		couponCode  string
		discountAmt int64
		couponName  string
		maxUses     int64
		wantErr     bool
	}{
		{
			name:        "single use coupon",
			couponCode:  "test-code",
			discountAmt: 100,
			couponName:  "Test Coupon",
			maxUses:     1,
			wantErr:     false,
		},
		{
			name:        "multi use coupon",
			couponCode:  "test-code-multi",
			discountAmt: 100,
			couponName:  "Test Multi Coupon",
			maxUses:     5,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			saltedCode, err := c.CreateCoupon(tt.couponCode, tt.discountAmt, tt.couponName, tt.maxUses)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateCoupon() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && len(saltedCode) <= len(tt.couponCode) {
				t.Errorf("CreateCoupon() saltedCode = %v, expected longer than input code", saltedCode)
			}
		})
	}
}

func TestApplyDiscount(t *testing.T) {
	c := newTestCouponer()

	tests := []struct {
		name    string
		req     ApplyDiscountRequest
		wantErr bool
	}{
		{
			name: "single use coupon",
			req: ApplyDiscountRequest{
				CustomerUUIDs:  []string{"user1", "user2"},
				CreatorEmail:   "<EMAIL>",
				Reason:         "test-discount",
				DiscountAmount: 100,
				CouponName:     "Test Coupon",
				MaxUses:        1,
			},
			wantErr: false,
		},
		{
			name: "multi use coupon",
			req: ApplyDiscountRequest{
				CustomerUUIDs:  []string{"user1", "user2"},
				CreatorEmail:   "<EMAIL>",
				Reason:         "test-discount",
				DiscountAmount: 100,
				CouponName:     "Test Multi Coupon",
				MaxUses:        5,
			},
			wantErr: false,
		},
		{
			name: "invalid email",
			req: ApplyDiscountRequest{
				CustomerUUIDs:  []string{"user1"},
				CreatorEmail:   "invalid-email",
				Reason:         "test-discount",
				DiscountAmount: 100,
				MaxUses:        1,
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := c.ApplyDiscount(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyDiscount() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestIsValidEmail(t *testing.T) {
	tests := []struct {
		name  string
		email string
		want  bool
	}{
		{"valid email", "<EMAIL>", true},
		{"valid email with plus", "<EMAIL>", true},
		{"valid email with dots", "<EMAIL>", true},
		{"invalid email no @", "testexample.com", false},
		{"invalid email no domain", "test@", false},
		{"invalid email empty", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isValidEmail(tt.email); got != tt.want {
				t.Errorf("isValidEmail() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNew(t *testing.T) {
	tests := []struct {
		name      string
		stripeKey string
		productID string
		prefix    string
		wantErr   error
	}{
		{
			name:      "valid parameters",
			stripeKey: "sk_test_123",
			productID: "prod_123",
			prefix:    "test",
			wantErr:   nil,
		},
		{
			name:      "missing stripe key",
			stripeKey: "",
			productID: "prod_123",
			prefix:    "test",
			wantErr:   ErrMissingStripeKey,
		},
		{
			name:      "missing product ID",
			stripeKey: "sk_test_123",
			productID: "",
			prefix:    "test",
			wantErr:   ErrMissingProductID,
		},
		{
			name:      "missing prefix",
			stripeKey: "sk_test_123",
			productID: "prod_123",
			prefix:    "",
			wantErr:   ErrMissingPrefix,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := New(tt.stripeKey, tt.productID, tt.prefix)
			if err != tt.wantErr {
				t.Errorf("New() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestNewWithClient(t *testing.T) {
	mockClient := &MockStripeClient{}

	tests := []struct {
		name      string
		productID string
		prefix    string
		client    StripeClient
		wantErr   error
	}{
		{
			name:      "valid parameters",
			productID: "prod_123",
			prefix:    "test",
			client:    mockClient,
			wantErr:   nil,
		},
		{
			name:      "missing product ID",
			productID: "",
			prefix:    "test",
			client:    mockClient,
			wantErr:   ErrMissingProductID,
		},
		{
			name:      "missing prefix",
			productID: "prod_123",
			prefix:    "",
			client:    mockClient,
			wantErr:   ErrMissingPrefix,
		},
		{
			name:      "missing client",
			productID: "prod_123",
			prefix:    "test",
			client:    nil,
			wantErr:   ErrMissingClient,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewWithClient(tt.productID, tt.prefix, tt.client)
			if err != tt.wantErr {
				t.Errorf("NewWithClient() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
