package coupon

import (
	"github.com/stripe/stripe-go/v74"
)

// MockStripeClient implements StripeClient for testing
type MockStripeClient struct {
	CreateCouponFunc          func(params *stripe.CouponParams) (*stripe.Coupon, error)
	UpdateCustomerFunc        func(customerID string, params *stripe.CustomerParams) (*stripe.Customer, error)
	FindCustomerByUUIDFunc    func(uuid string) (*stripe.Customer, error)
	GetActiveSubscriptionFunc func(customerID string) (*stripe.Subscription, error)
}

func (m *MockStripeClient) CreateCoupon(params *stripe.CouponParams) (*stripe.Coupon, error) {
	return m.CreateCouponFunc(params)
}

func (m *MockStripeClient) UpdateCustomer(customerID string, params *stripe.CustomerParams) (*stripe.Customer, error) {
	return m.UpdateCustomerFunc(customerID, params)
}

func (m *MockStripeClient) FindCustomerByUUID(uuid string) (*stripe.Customer, error) {
	return m.FindCustomerByUUIDFunc(uuid)
}

func (m *MockStripeClient) GetActiveSubscription(customerID string) (*stripe.Subscription, error) {
	return m.GetActiveSubscriptionFunc(customerID)
}
