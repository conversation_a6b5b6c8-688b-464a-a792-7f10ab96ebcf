package coupon

import "github.com/stripe/stripe-go/v74"

// Couponer handles stripe subscription and coupon management
type Couponer struct {
	productID string
	prefix    string
	client    StripeClient
}

// New creates a new Couponer instance
func New(stripeKey string, productID string, prefix string) (*Couponer, error) {
	if stripeKey == "" {
		return nil, ErrMissingStripeKey
	}
	if productID == "" {
		return nil, ErrMissingProductID
	}
	if prefix == "" {
		return nil, ErrMissingPrefix
	}

	stripe.Key = stripeKey
	return &Couponer{
		productID: productID,
		prefix:    prefix,
		client:    &DefaultStripeClient{},
	}, nil
}

// NewWithClient creates a new Couponer instance with a custom client
func NewWithClient(productID string, prefix string, client StripeClient) (*Couponer, error) {
	if productID == "" {
		return nil, ErrMissingProductID
	}
	if prefix == "" {
		return nil, ErrMissingPrefix
	}
	if client == nil {
		return nil, ErrMissingClient
	}

	return &Couponer{
		productID: productID,
		prefix:    prefix,
		client:    client,
	}, nil
}

// ApplyDiscountRequest contains the information needed to apply a discount
type ApplyDiscountRequest struct {
	CustomerUUIDs  []string
	CreatorEmail   string
	Reason         string
	DiscountAmount int64  // percentage, 100 for 100% discount
	CouponName     string // optional name for the coupon
	MaxUses        int64  // maximum number of times the coupon can be used
}
