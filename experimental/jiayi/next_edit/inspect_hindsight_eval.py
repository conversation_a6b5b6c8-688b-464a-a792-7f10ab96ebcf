from dataclasses import dataclass
from typing import Sequence
import ipywidgets as widgets


from base.prompt_format_next_edit.gen_prompt_formatter import equal_modulo_spaces
from research.eval.harness.tasks.next_edit_gen_eval_task import EvalExampleOutput
from research.next_edits.next_edits_dataset import HindsightEditGenProblem
from research.utils.notebook_uis.edit_gen import (
    render_diff_html,
    render_hindsight_edit_gen_problem,
)
from research.utils.notebook_uis.notebook_uis import (
    _UIParams,
    render_item_browser_with_params,
)


@dataclass
class EditGenResultComparison:
    problem: HindsightEditGenProblem
    eval_outputs: dict[str, EvalExampleOutput]

    def is_output_correct_em(self, name: str) -> bool:
        """Check if the output is correct using equal_modulo_spaces."""
        output = self.eval_outputs[name]
        gt = self.problem.ground_truth
        predicted_code = (
            output.predicted_code
            if output.predicted_code is not None
            else self.problem.selected_code
        )
        return equal_modulo_spaces(predicted_code, gt.replacement)


def render_result_comparison(comparison: EditGenResultComparison, params: _UIParams):
    problem_ui = render_hindsight_edit_gen_problem(
        comparison.problem, params, problem_prompt=None
    )
    gt_output = EvalExampleOutput(
        path=comparison.problem.current_path,
        edit_region=comparison.problem.edit_region,
        predicted_has_change=comparison.problem.ground_truth.changed,
        predicted_code=comparison.problem.ground_truth.replacement,
        request_id=comparison.problem.origin.request_id,
    )
    all_outputs = {"Ground Truth": gt_output} | comparison.eval_outputs

    titles = ["Problem"]
    children: list[widgets.Widget] = [problem_ui]

    for name, output in all_outputs.items():
        titles.append(name)
        selected_code = comparison.problem.selected_code
        if output.predicted_code is None or selected_code == output.predicted_code:
            children.append(widgets.HTML(f"<p>{name} has no change.</p>"))
            continue
        diff_html = render_diff_html(
            file_path=output.path,
            before_text=selected_code,
            after_text=output.predicted_code,
            num_context_lines=10,
        )
        children.append(widgets.HTML(diff_html))

    tab = widgets.Tab(children=children, titles=titles)
    return tab


def render_result_comparisons(comparisons: Sequence[EditGenResultComparison]):
    N = len(comparisons)

    def display_item(i: int, params: _UIParams):
        return render_result_comparison(comparisons[i], params)

    return render_item_browser_with_params(N, display_item)
