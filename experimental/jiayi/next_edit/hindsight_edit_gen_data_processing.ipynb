{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils import pickle_load\n", "from research.next_edits.next_edits_dataset import HindsightEditGenProblem\n", "\n", "\n", "problems = pickle_load(\n", "    \"/mnt/efs/augment/data/processed/next-edit-hindsight/dogfood_2025_01_31_14days_10s/edit_classification_eval-5000.pkl\",\n", "    list[HindsightEditGenProblem],\n", ")\n", "print(f\"Loaded {len(problems)} problems.\")\n", "positive_probs = [p for p in problems if p.output.changed]\n", "print(\n", "    f\"Containing {len(positive_probs)} ({len(positive_probs) / len(problems):.1%}) positive problems.\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["problems = problems[:200]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.next_edits.edit_gen_stages import (\n", "    RetrievalConfig,\n", "    perform_dense_retrieval,\n", ")\n", "from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (\n", "    edit_ethanol_config,\n", ")\n", "from tqdm import tqdm\n", "\n", "ret_config = RetrievalConfig(\n", "    retriever_config=edit_ethanol_config,\n", "    num_retrieved_chunks=56,\n", "    skip_dense_retrieval=True,\n", "    edit_group_sizes=(1, 1, 2, 3),\n", ")\n", "\n", "retrieval_augmented = perform_dense_retrieval(ret_config, problems, seed=42)\n", "retrieval_augmented = list(tqdm(retrieval_augmented, total=len(problems), smoothing=0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.prompt_format_next_edit.gen_prompt_formatter import (\n", "    EditGenFormatterConfig,\n", "    section_budgets_10k,\n", ")\n", "from megatron.tokenizer.tokenizer import StarCoderTokenizer\n", "\n", "from research.next_edits.edit_gen_stages import PromptConfig, format_as_tokens\n", "\n", "prompt_config = PromptConfig(\n", "    tokenizer_name=\"starcoder2\",\n", "    formatter_config=EditGenFormatterConfig(\n", "        diff_context_lines=12,\n", "        max_prompt_tokens=10_200,\n", "        section_budgets=section_budgets_10k(),\n", "        diff_chunk_size_chars=500,\n", "    ),\n", "    max_output_tokens=600,\n", ")\n", "\n", "tokenized_probs = list(format_as_tokens(prompt_config, retrieval_augmented, seed=42))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer\n", "from research.fastbackward.loss_masking import unmask_token_np\n", "\n", "tokenizer = StarCoder2Tokenizer()\n", "\n", "prompts = dict[str, str]()\n", "for tk_prob in tokenized_probs:\n", "    tokens = tk_prob.tokens\n", "    prompt = tokenizer.detokenize(unmask_token_np(tokens))\n", "    loss_tokens = tokens[tokens >= 0]\n", "    loss_prompt = tokenizer.detokenize(loss_tokens)\n", "    separator = \"~=\" * 20 + \"Tokens with loss\" + \"~=\" * 20\n", "    assert tk_prob.hindsight_origin\n", "    request_id = tk_prob.hindsight_origin.request_id\n", "    prompts[request_id] = f\"{prompt}\\n{separator}\\n{loss_prompt}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_hindsight_edit_gen_viewer\n", "\n", "probs_with_prompts = [p for p in problems if p.origin.request_id in prompts]\n", "display_hindsight_edit_gen_viewer(\n", "    probs_with_prompts, problem_prompts=list(prompts.values())\n", ")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}