{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["There is a Python script version of this notebook: `make_edit_gen_hindsight_data.py`."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 2 problems.\n"]}], "source": ["from research.core.utils import pickle_load\n", "from pathlib import Path\n", "\n", "from research.next_edits.next_edits_dataset import HindsightEditGenProblem\n", "\n", "training_data, stats = pickle_load(\n", "    Path(\n", "        \"/mnt/efs/augment/data/processed/next-edit-hindsight/dogfood-v8_2025_02_22_42days/training_data.pkl\"\n", "    ),\n", "    tuple[list[HindsightEditGenProblem], list[str]],\n", ")\n", "print(f\"Loaded {len(training_data)} problems.\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from research.next_edits.convert_hindsight_data import is_touching_the_last_edits\n", "\n", "\n", "touch_example = training_data[5003]\n", "is_touching_the_last_edits(touch_example, n_last_edits=3)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["73.7%\n"]}], "source": ["def edit_in_same_file(problem: HindsightEditGenProblem) -> bool | None:\n", "    \"\"\"Check if the ground truth edit is in the same file as the last edit.\"\"\"\n", "    if not problem.squashable_edits.edit_events:\n", "        return None\n", "    if not problem.ground_truth.changed:\n", "        return None\n", "    last_edit = problem.squashable_edits.edit_events[-1]\n", "    return last_edit.path == problem.current_path\n", "\n", "\n", "is_same_file = [\n", "    result for p in training_data if (result := edit_in_same_file(p)) is not None\n", "]\n", "print(f\"{sum(is_same_file) / len(is_same_file):.1%}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "28c7a0ff46f84a6db00f0334c1967f0f", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HBox(children=(IntText(value=2000, description='Prefix len:'), IntText(value=1000, description=…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from research.utils.notebook_uis.edit_gen import display_hindsight_edit_gen_viewer\n", "\n", "\n", "display_hindsight_edit_gen_viewer(training_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pickle\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    NextEditIntermediateType,\n", "    NextEditRawDataQueryArgs,\n", "    NextEditIntermediateErrorDetails,\n", ")\n", "from experimental.jiayi.next_edit.download_hindsight_data import (\n", "    IntermediateDataDownloader,\n", ")\n", "from experimental.jiayi.next_edit.edit_gen_hindsight_data_lib import VERSION\n", "from random import Random\n", "from datetime import datetime, timedelta\n", "import pytz\n", "\n", "\n", "california_tz = pytz.timezone(\"America/Los_Angeles\")\n", "end_time = california_tz.localize(datetime.fromisoformat(\"2025-02-03T23:00:00.0\"))\n", "total_duration = <PERSON><PERSON><PERSON>(days=28)\n", "dataset_name = f\"dogfood-v{VERSION}_2025_02_03_28days_10s\"\n", "print(f\"Dataset name: {dataset_name}\")\n", "\n", "result_dir = Path(f\"/mnt/efs/augment/data/processed/next-edit-hindsight/{dataset_name}\")\n", "\n", "downloader = IntermediateDataDownloader()  # Blobs are cached inside this object"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_data: list[NextEditIntermediateType]\n", "error_collector: NextEditIntermediateErrorDetails\n", "\n", "intermediate_data_file = result_dir / \"intermediate_type.pkl\"\n", "\n", "if intermediate_data_file.exists():\n", "    print(f\"loading results from file: {intermediate_data_file}\")\n", "    all_data, error_collector = pickle.load(open(intermediate_data_file, \"rb\"))\n", "else:\n", "    all_data, error_collector = downloader.download_intermediate_data(\n", "        Random(42),\n", "        end_time,\n", "        total_duration,\n", "        future_events_duration=timedelta(hours=1),\n", "        query_args=NextEditRawDataQueryArgs(),\n", "    )\n", "    print(error_collector.summary())\n", "    print(f\"Saving results to {intermediate_data_file}\")\n", "    result_dir.mkdir(parents=True, exist_ok=True)\n", "    with open(intermediate_data_file, \"wb\") as f:\n", "        pickle.dump((all_data, error_collector), f)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"File size: {intermediate_data_file.stat().st_size / 2**30:.2f} GB\")\n", "print(f\"Num examples: {len(all_data)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.utils.notebook_uis.edit_gen import display_next_edit_intermediate_problems\n", "\n", "\n", "display_next_edit_intermediate_problems(all_data)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.static_analysis.common import groupby\n", "from research.utils.notebook_uis.edit_gen import display_next_edit_intermediate_problems\n", "\n", "\n", "user_to_data = groupby(\n", "    (datum for datum in all_data if datum.future_events),\n", "    lambda d: d.future_events[0].user_id,\n", ")\n", "surbhi_data = list(user_to_data[\"<EMAIL>\"])\n", "\n", "\n", "display_next_edit_intermediate_problems(surbhi_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running The Ground Truth Heuristic"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import collections\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import GroundTruthEdit\n", "from research.next_edits.next_hunk_heuristic import (\n", "    NextHunkHeuristic,\n", "    HeuristicFailed,\n", ")\n", "from tqdm import tqdm\n", "\n", "heuristic = NextHunkHeuristic()\n", "successful_examples = list[tuple[NextEditIntermediateType, GroundTruthEdit]]()\n", "errors = list[tuple[int, HeuristicFailed]]()\n", "\n", "for i, datum in enumerate(tqdm(all_data, smoothing=0)):\n", "    try:\n", "        if gt := heuristic.get_gold_edits(datum, analyzer=None):\n", "            successful_examples.append((datum, gt))\n", "    except HeuristicFailed as e:\n", "        errors.append((i, e))\n", "\n", "print(f\"{len(successful_examples)=}\")\n", "print(f\"A total of {len(errors)} ({len(errors) / len(all_data):.1%}) examples failed.\")\n", "print(\"Top errors:\")\n", "counter = collections.Counter([e.category for _, e in errors])\n", "for category, count in counter.most_common(10):\n", "    print(f\"\\t{category}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from collections import Counter\n", "\n", "user_data_counter = Counter(\n", "    datum.future_events[0].user_id for datum, _ in successful_examples\n", ")\n", "for user, count in user_data_counter.most_common(10):\n", "    print(f\"{user}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pickle\n", "\n", "from base.datasets.tenants import DOGFOOD_SHARD\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    DatasetWithGroundTruths,\n", ")\n", "\n", "ground_truth_file = result_dir / \"dataset_with_ground_truths.pkl\"\n", "\n", "\n", "print(\"Saving ground truth dataset...\")\n", "result_dataset = DatasetWithGroundTruths(\n", "    DOGFOOD_SHARD.name, successful_examples, [e for _, e in errors]\n", ")\n", "\n", "with open(ground_truth_file, \"wb\") as f:\n", "    pickle.dump(result_dataset, f)\n", "\n", "# print out the saved file size\n", "print(f\"File saved to: {ground_truth_file}\")\n", "print(f\"File size: {ground_truth_file.stat().st_size / 2**30:.2f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from research.core.utils import pickle_load, pickle_dump\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    DatasetWithGroundTruths,\n", ")\n", "\n", "ground_truth_file = result_dir / \"dataset_with_ground_truths.pkl\"\n", "\n", "print(f\"loading results from file: {ground_truth_file}\")\n", "result_dataset = pickle_load(ground_truth_file, DatasetWithGroundTruths)\n", "successful_examples = result_dataset.data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter out sessions with too few samples\n", "\n", "max_repo_files = 25_000\n", "min_probs_per_session = 50\n", "\n", "successful_count_original = len(successful_examples)\n", "session_id_counter = Counter(datum.session_id for datum, _ in successful_examples)\n", "sessions_with_too_few_data = {\n", "    k for k, v in session_id_counter.items() if v < min_probs_per_session\n", "}\n", "\n", "successful_examples = [\n", "    (datum, gt)\n", "    for datum, gt in successful_examples\n", "    if datum.session_id not in sessions_with_too_few_data\n", "    and len(datum.request.blob_names) <= max_repo_files\n", "]\n", "\n", "print(\n", "    f\"Keeping {len(successful_examples)} ({len(successful_examples) / successful_count_original:.1%}) labeled examples for training.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate classification data\n", "\n", "These are smart-chunk based examples with a 1:1 positive:negative ratio."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.retrieval.chunking.smart_chunking import SmartChunker\n", "from random import Random\n", "from experimental.jiayi.next_edit.edit_gen_hindsight_data_lib import (\n", "    make_classification_edit_dataset,\n", ")\n", "import pickle\n", "\n", "chunker = SmartChunker(max_chunk_chars=3000, max_headers=0)\n", "rng = Random(42)\n", "\n", "filtered_class_probs = make_classification_edit_dataset(\n", "    rng,\n", "    downloader.caches,\n", "    successful_examples,\n", "    chunker,\n", "    max_samples=5000,\n", "    max_changed_lines=10,\n", ")\n", "\n", "\n", "classification_data_file = (\n", "    result_dir / f\"edit_classification_eval-{len(filtered_class_probs)}.pkl\"\n", ")\n", "\n", "pickle_dump(filtered_class_probs, classification_data_file)\n", "\n", "# print out the saved file size\n", "print(f\"File saved to: {classification_data_file}\")\n", "print(f\"File size: {classification_data_file.stat().st_size / 2**30:.2f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "from research.next_edits.convert_hindsight_data import (\n", "    measure_last_edit_location_acc_on_problems,\n", ")\n", "from research.utils.sampling_utils import downsample_to\n", "from research.next_edits.convert_hindsight_data import (\n", "    measure_last_edit_location_acc_on_ground_truths,\n", ")\n", "\n", "stats = measure_last_edit_location_acc_on_ground_truths(\n", "    SmartChunker(2000, max_headers=0), successful_examples\n", ")\n", "\n", "print(json.dumps(stats, indent=2))\n", "\n", "stats = measure_last_edit_location_acc_on_problems(\n", "    downsample_to(Random(42), filtered_class_probs, 2000)\n", ")\n", "\n", "print(json.dumps(stats, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare the data for Spark pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.next_edit.edit_gen_hindsight_data_lib import (\n", "    make_training_dataset,\n", ")\n", "from random import Random\n", "from base.retrieval.chunking.smart_chunking import SmartChunker\n", "\n", "chunker = SmartChunker(max_chunk_chars=3000, max_headers=0)\n", "rng = Random(42)\n", "\n", "\n", "training_data = make_training_dataset(\n", "    rng,\n", "    downloader.caches,\n", "    successful_examples,\n", "    chunker,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from base.static_analysis.common import groupby\n", "import pandas as pd\n", "from tqdm import tqdm\n", "\n", "from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (\n", "    get_stage_output_path,\n", ")\n", "\n", "spark_data_dir = get_stage_output_path(dataset_name, 1, \"spark_input\")\n", "\n", "# group data by session_id\n", "session_to_probs = groupby(training_data, lambda p: p.origin.session_id)\n", "# through away sessions with less than 20 problems\n", "session_to_probs = {k: v for k, v in session_to_probs.items() if len(v) >= 20}\n", "n_remaining = sum(len(v) for v in session_to_probs.values())\n", "print(\n", "    f\"Remaining {n_remaining} ({n_remaining / len(training_data):.1%}) problems in {len(session_to_probs)} sessions.\"\n", ")\n", "\n", "spark_data_dir.mkdir(parents=True, exist_ok=True)\n", "for i, (session_id, probs) in enumerate(\n", "    tqdm(session_to_probs.items(), smoothing=0, desc=\"Saving to parquet files\")\n", "):\n", "    df = pd.DataFrame(\n", "        [\n", "            {\n", "                \"repo_path\": session_id,\n", "                \"num_problems\": len(probs),\n", "                \"pickled_results\": pickle.dumps(probs),\n", "            }\n", "        ]\n", "    )\n", "    df.to_parquet(spark_data_dir / f\"part-{i}-{session_id}.parquet\")\n", "\n", "print(f\"Dataset saved to: {spark_data_dir}\")\n", "print(f\"Number of sessions: {len(session_to_probs)}\")\n", "# recursively count size of spark_data_dir\n", "dir_size = sum(f.stat().st_size for f in spark_data_dir.glob(\"**/*\"))\n", "print(f\"Total dataset size: {dir_size / 2**30:.2f} GB\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}