# ruff: noqa: E402

import shutil
from datetime import datetime, timed<PERSON>ta
from pathlib import Path

import pytz

from base.prompt_format_next_edit.gen_prompt_formatter import (
    EditGenFormatterConfig,
    section_budgets_10k,
)
from base.retrieval.chunking.smart_chunking import SmartChunker
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    NEXT_EDIT_SPARK_ROOT,
    clear_existing_data,
    create_final_stage,
    create_stage2,
    create_stage3,
    edit_ethanol_config,
    round_up_seq_length,
    run_cached_stages,
)
from research.data.spark.pipelines.utils.cached_stage_utils import cached_stage
from research.next_edits.edit_gen_stages import PromptConfig, RetrievalConfig
from research.utils.inspect_indexed_dataset import print_green, print_yellow
import experimental.jiayi.next_edit.edit_gen_hindsight_data_lib as lib


def get_stage_output_path(
    data_source_name: str, stage_id: int | str, combined_config_str: str
) -> Path:
    """Returns the path to a stage's output."""
    if isinstance(stage_id, str) and not stage_id.startswith("-"):
        stage_id = f"-{stage_id}"
    return (
        NEXT_EDIT_SPARK_ROOT
        / f"{data_source_name}/stage{stage_id}/{combined_config_str}"
    )


def start_training_data_spark_job(
    skip_retrieval: bool,
    max_workers: int,
    clear_existing: bool = False,
    print_paths_only: bool = False,
):
    from research.data.spark.pipelines.stages import next_edit_gen_pipelines

    # need this large enough to fit the Augment repo inside a row
    next_edit_gen_pipelines.ROW_SIZE_LIMIT_MB = 1600

    california_tz = pytz.timezone("America/Los_Angeles")

    # params for the user events stage
    tenant_name = "i1-vanguard0"
    query_args = lib.NextEditRawDataQueryArgs(min_session_requests=50)
    total_duration = timedelta(days=40)
    end_time = california_tz.localize(datetime.fromisoformat("2025-03-26T23:00:00.0"))
    intermediate_config_name = f"v{lib.VERSION}_2025_03_26_40days"

    user_events_data_path = get_stage_output_path(
        tenant_name, "user_events", intermediate_config_name
    )

    def user_events_stage(input_path, output_path):
        del input_path
        return lib.user_events_stage(
            output_dir=output_path,
            tenant_name=tenant_name,
            end_time=end_time,
            total_duration=total_duration,
            query_args=query_args,
        )

    intermediate_data_path = get_stage_output_path(
        tenant_name, "intermediate_data", intermediate_config_name
    )
    future_events_duration = timedelta(hours=1)

    # params for the ground truth data stage
    ground_truth_data_path = get_stage_output_path(
        tenant_name, "ground_truth", intermediate_config_name
    )

    # params for training problems stage
    training_prob_chunker = SmartChunker(max_chunk_chars=3000, max_headers=0)
    training_problems_path = get_stage_output_path(
        tenant_name, "training_problems", intermediate_config_name
    )

    # Params for retrieval stage
    retrieval_config_name = "no_retrieval" if skip_retrieval else "ethanol-K50-diff_1_2"
    retrieval_config = RetrievalConfig(
        retriever_config=edit_ethanol_config,
        num_retrieved_chunks=50,
        edit_group_sizes=(1, 2),
        timeout_per_repo=1200 if skip_retrieval else 4800,
        skip_dense_retrieval=skip_retrieval,
    )

    # Params for stage 3.
    prompt_config_name = "star2_seq12k_pause500_out600"
    prompt_config = PromptConfig(
        tokenizer_name="starcoder2",
        formatter_config=EditGenFormatterConfig(
            diff_context_lines=12,
            max_prompt_tokens=10_200,
            section_budgets=section_budgets_10k(),
            diff_chunk_size_chars=500,
        ),
        max_output_tokens=600,
    )

    # Params for final stage.
    positive_ratio = 0.50
    pad_to_length = round_up_seq_length(prompt_config.max_sequence_length()) + 1

    # --------------------------------------------------------
    # End specifying the stage parameters.

    retrieval_config_str = (
        f"{intermediate_config_name}_R{RetrievalConfig.VERSION}_{retrieval_config_name}"
    )
    prompt_config_str = (
        f"{retrieval_config_str},P{PromptConfig.VERSION}_{prompt_config_name}"
    )
    i_data_config_str = (
        f"{prompt_config_str}-pos_{positive_ratio:.2f}-pad_{pad_to_length}"
    )

    retrieval_output_path = get_stage_output_path(
        tenant_name, "retrieval", retrieval_config_str
    )
    prompt_output_path = get_stage_output_path(tenant_name, "prompt", prompt_config_str)
    idata_save_path = (
        Path("/mnt/efs/augment/data/processed/next-edit-hindsight/")
        / tenant_name
        / "indexed_dataset"
        / i_data_config_str
    )

    if print_paths_only:
        print("Exiting early since print_paths_only=True.")
        return

    stage_output_paths = [
        user_events_data_path,
        intermediate_data_path,
        ground_truth_data_path,
        training_problems_path,
        retrieval_output_path,
        prompt_output_path,
        idata_save_path,
    ]

    for i, path in enumerate(stage_output_paths):
        print_green(f"Stage {i+1} output path: {path}")

    if clear_existing:
        clear_existing_data(stage_output_paths)

    empty_path = Path("")  # user events stage does not take an input path
    run_cached_stages(
        stages=[
            user_events_stage,
            lambda input_path, output_path: lib.events_to_intermediate_data_stage(
                input_path,
                output_path,
                query_args,
                future_events_duration=future_events_duration,
                max_workers=max_workers,
            ),
            lambda input_path, output_path: lib.ground_truth_heuristic_stage(
                input_path,
                output_path,
                max_workers=max_workers,
            ),
            lambda input_path, output_path: lib.make_training_problems_stage(
                input_path,
                output_path,
                training_prob_chunker,
                max_workers=max_workers,
            ),
            create_stage2(
                retrieval_config,
                max_workers=max_workers,
            ),
            create_stage3(prompt_config, max_workers=max_workers),
            create_final_stage(
                pad_to_length=pad_to_length,
                positive_ratio=positive_ratio,
                tokenizer_name=prompt_config.tokenizer_name,
                unmask_tokens=True,  # We will use FIM loss masking.
            ),
        ],
        result_paths=[empty_path, *stage_output_paths],
    )


if __name__ == "__main__":
    start_training_data_spark_job(
        skip_retrieval=False,
        max_workers=128,
    )
