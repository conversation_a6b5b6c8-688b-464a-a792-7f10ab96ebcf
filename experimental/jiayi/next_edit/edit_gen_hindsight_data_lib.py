from functools import cache
import pickle
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from random import Random
from typing import Sequence

import pandas as pd
from tqdm import tqdm

from base.datasets.gcs_client import GCSRequestInsightFetcher
from base.datasets.hindsight_next_edit import NextEditIntermediateType
from base.datasets.hindsight_next_edit_intermediate_dataset import (
    DatasetWithGroundTruths,
    GCSCaches,
    GroundTruthEdit,
    HindsightNextEditRawData,
    NextEditIntermediateDataLoader,
    NextEditIntermediateErrorDetails,
    NextEditRawDataQueryArgs,
    download_user_events,
    filter_user_events,
    user_events_to_next_edit_data,
)
from base.datasets.tenants import get_tenant
from base.datasets.user_event import UserEvent
from base.diff_utils.git_conflict_utils import contains_git_conflict_marker
from base.retrieval.chunking.smart_chunking import SmartChunker
from base.static_analysis.common import groupby
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    count_problems_in_parquet_files,
    get_session,
)
from research.data.spark.pipelines.utils import map_parquet
from research.model_server.model_server_app import SessionId
from research.next_edits.convert_hindsight_data import (
    convert_to_edit_classification_problem,
    deduplicate_problems,
    is_large_edit_problem,
    is_touching_the_last_edits,
    is_undo_recent_change_example,
    n_changed_size_lines,
    rebalance_positive_negative_examples,
    remove_import_deletion_examples,
)
from research.next_edits.next_edits_dataset import HindsightEditGenProblem
from research.next_edits.next_hunk_heuristic import (
    HeuristicArgs,
    HeuristicFailed,
    NextHunkHeuristic,
    fix_next_edit_datum_events,
    dedup_next_edit_data,
)
from research.utils.sampling_utils import downsample_to

VERSION = "10"
"""This version number is bumped up every time the dataset is changed in a way that
requires re-running the Spark job.

Change log:
- v2: Downsample the requests using the end of streak strategy.
- v3: Filter out requests that do not have any follow-up user edits in the next
  30 minutes or have any follow-up user edit that is too close to the request time
  (1 second). Then downsample the requests using the end of streak strategy.
- v4: Filter out requests whose ground truth touches the most recent change or undoes
  the most recent 5 changes.
- v5: Filter out requests that occur after or before a long period of inactivity.
  Change edit_group_sizes to (1, 2). Set downsample ratio to 1.
- v6: Filter out requests that modify the last 3 edits.
- v7: Strengthen to filter any request that touches the last 3 edits. Improved event
  grouping logic used by the filter.
- v8:
  - Filter out problems with no diff history.
  - Fix bug in `is_suggestion_touching_recent_changes`.
  - Increase hunk_context_lines to 8.
  - Filter out problems that contain git conflict markers.
- v9: Filter out sessions with fewer than 50 requests by default.
- v10: Revert the change that filters out problems containing git conflict markers.
"""


def make_classification_edit_dataset(
    rng: Random,
    caches: GCSCaches,
    labeled_data: Sequence[tuple[NextEditIntermediateType, GroundTruthEdit]],
    chunker: SmartChunker,
    max_samples: int | None,
    max_changed_lines: int = 10,
) -> tuple[list[HindsightEditGenProblem], list[str]]:
    classification_problems = list[HindsightEditGenProblem]()
    for datum, ground_truth in tqdm(
        labeled_data, smoothing=0, desc="make_classification_edit_dataset"
    ):
        classification_problems.extend(
            convert_to_edit_classification_problem(
                rng,
                chunker,
                datum,
                ground_truth,
                caches,
            )
        )
    filtered_class_probs, stats = filter_hindsight_problems(
        classification_problems,
        max_changed_lines=max_changed_lines,
    )
    filtered_class_probs = rebalance_positive_negative_examples(
        rng, filtered_class_probs
    )
    stats.append(f"After rebalancing, {len(filtered_class_probs)=}")
    if max_samples is not None:
        filtered_class_probs = downsample_to(rng, filtered_class_probs, max_samples)
    stats.append(f"After downsampling, {len(filtered_class_probs)=}")
    return filtered_class_probs, stats


def make_training_problems(
    rng: Random,
    caches: GCSCaches,
    labeled_data: Sequence[tuple[NextEditIntermediateType, GroundTruthEdit]],
    chunker: SmartChunker,
    max_changed_lines: int = 50,
    max_deleted_lines: int = 10,
) -> tuple[list[HindsightEditGenProblem], list[str]]:
    """Make a dataset for training the edit generation model."""
    problems = list[HindsightEditGenProblem]()
    for datum, ground_truth in tqdm(
        labeled_data, smoothing=0, desc="make_training_dataset"
    ):
        problems.extend(
            convert_to_edit_classification_problem(
                rng,
                chunker,
                datum,
                ground_truth,
                caches,
            )
        )
    print(f"{len(problems)=}")
    total = len(problems)
    problems, stats = filter_hindsight_problems(
        problems,
        max_changed_lines=max_changed_lines,
        max_deleted_lines=max_deleted_lines,
    )
    problems = rebalance_positive_negative_examples(rng, problems)
    stats.append(f"After rebalancing, {len(problems)=}")
    if total > 0:
        stats.append(f"{len(problems) / total:.1%} of the original data remains.")
    return problems, stats


def filter_hindsight_problems(
    problems: Sequence[HindsightEditGenProblem],
    max_changed_lines: int = 50,
    max_deleted_lines: int = 10,
) -> tuple[list[HindsightEditGenProblem], list[str]]:
    stats = list[str]()
    stats.append(f"Before filtering, {len(problems)=}")
    problems = [p for p in problems if not is_large_edit_problem(p, max_changed_lines)]
    stats.append(
        f"After filtering out large edits ({max_changed_lines=}), {len(problems)=}"
    )
    problems = [p for p in problems if n_changed_size_lines(p) >= -max_deleted_lines]
    stats.append(
        f"After filtering out edits that delete more than {max_deleted_lines} lines, {len(problems)=}"
    )
    problems = list(deduplicate_problems(problems))
    stats.append(f"After deduplication, {len(problems)=}")
    problems = [
        p
        for p in tqdm(problems, smoothing=0, desc="Filtering modifying examples")
        if not is_touching_the_last_edits(p, n_last_edits=3)
    ]
    stats.append(
        f"After filtering examples that modifies the last 3 edits, {len(problems)=}"
    )
    problems = [
        p
        for p in tqdm(problems, smoothing=0, desc="Filtering undo recent changes")
        if not is_undo_recent_change_example(p)
    ]
    stats.append(f"After filtering examples that undo recent changes, {len(problems)=}")
    problems = list(remove_import_deletion_examples(problems))
    stats.append(f"After removing import deletion examples, {len(problems)=}")
    return problems, stats


def row_safe_pickle(data: Sequence, size_limit: int = 2**30) -> bytes:
    """Pickle the data such that it can be safely written as a data frame row.

    If it's too large to fit into a data frame row, pickle an empty list instead."""
    pickled = pickle.dumps(data)
    if len(pickled) < size_limit:
        return pickled
    else:
        return pickle.dumps([])


def user_events_stage(
    output_dir: Path,
    tenant_name: str,
    end_time: datetime,
    total_duration: timedelta,
    query_args: NextEditRawDataQueryArgs,
    max_parquet_files: int = 5_000,
) -> None:
    """Download user events and save them as parquet files."""

    print("Downloading all user events...")
    user_events = download_user_events(
        end_time - total_duration,
        total_duration,
        timedelta(hours=1),
        query_args,
        get_tenant(tenant_name),
    )
    print(f"Downloaded {len(user_events)} user events.")
    # first sort by user_id, then group by session_id to help improve data adjacency
    user_events.sort(key=lambda x: x.user_id)
    user_events_by_session = groupby(user_events, lambda x: x.session_id)
    sessions_per_file = max(1, len(user_events_by_session) // max_parquet_files)
    # Group sessions into batches of sessions_per_file
    session_batches: list[list[dict]] = []
    current_batch: list[dict] = []
    for session_id, events in user_events_by_session.items():
        current_batch.append(
            {
                "session_id": session_id,
                "tenant": tenant_name,
                "num_events": len(events),
                "pickled_results": row_safe_pickle(events),
            }
        )
        if len(current_batch) >= sessions_per_file:
            session_batches.append(current_batch)
            current_batch = []

    # Add any remaining sessions
    if current_batch:
        session_batches.append(current_batch)

    # Save each batch to a parquet file
    output_dir.mkdir(exist_ok=True, parents=True)
    for i, batch in enumerate(session_batches):
        df = pd.DataFrame(batch)
        df.to_parquet(output_dir / f"part-{i}.parquet")

    print(f"Results saved as {len(session_batches)} parquet files.")
    print(f"Total number of sessions: {len(user_events_by_session)}")


@cache
def _get_gcs_caches(tenant_name: str) -> GCSCaches:
    """Get a globally shared cache for the tenant."""
    return GCSCaches.create(get_tenant(tenant_name))


def events_to_intermediate_data_stage(
    input_dir: Path,
    output_dir: Path,
    query_args: NextEditRawDataQueryArgs,
    future_events_duration: timedelta,
    max_workers: int,
    max_repo_files: int = 40_000,
    timeout: int = 1800,
    max_problems_per_row: int = 1000,
):
    """Convert user events to intermediate data.

    Args:
        input_dir: Directory containing parquet files with user events.
        output_dir: Directory to write output parquet files.
        query_args: Arguments for filtering user events.
        future_events_duration: Duration to look ahead for future events.
        max_workers: Maximum number of workers to use.
        max_repo_files: Maximum number of files in a repo to process.
        timeout: Timeout for the Spark job in seconds.
        max_problems_per_row: Maximum number of problems to store in a single row.
            If there are more problems, they will be split across multiple rows.
    """

    def process_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        # Create a list to store all the output rows
        output_rows = list[dict]()

        # Process each row in the input DataFrame
        for _, row in df.iterrows():
            session_id = row["session_id"]
            tenant = row["tenant"]
            pickled_results = row["pickled_results"]

            user_events: list[UserEvent] = pickle.loads(pickled_results)
            text_edit_events, request_id_issued_events = filter_user_events(
                user_events, query_args
            )
            error_collector = NextEditIntermediateErrorDetails()

            if not text_edit_events or not request_id_issued_events:
                # We cannot perform any analysis on this session, so drop this row.
                continue

            caches = _get_gcs_caches(tenant)
            request_fetcher = GCSRequestInsightFetcher.from_tenant(caches.tenant)

            next_edit_data = user_events_to_next_edit_data(
                text_edit_events,
                request_id_issued_events,
                query_args,
                request_fetcher,
                caches.checkpoint_cache,
            )
            raw_data = HindsightNextEditRawData(
                session_id=session_id,
                text_edit_events=text_edit_events,
                next_edit_data=next_edit_data,
            )
            loader = NextEditIntermediateDataLoader(
                caches.tenant,
                raw_data,
                caches.blob_cache,
                caches.path_cache,
            )
            all_data = list(loader.iterator(future_events_duration, error_collector))
            # filter out repos with too many files
            filtered_data = [
                datum
                for datum in all_data
                if len(datum.request.blob_names) <= max_repo_files
            ]
            for datum in filtered_data:
                fix_next_edit_datum_events(datum)
            filtered_data = list(dedup_next_edit_data(filtered_data))
            removed_data = {x.request.request_id for x in all_data} - {
                x.request.request_id for x in filtered_data
            }
            error_collector.duplicated_data.extend(removed_data)

            # Split the data into chunks
            chunks = [
                filtered_data[i : i + max_problems_per_row]
                for i in range(0, len(filtered_data), max_problems_per_row)
            ]

            # Create a row for each chunk
            for chunk_id, chunk in enumerate(chunks):
                row = {
                    "session_id": session_id,
                    "chunk_id": chunk_id,
                    "tenant": tenant,
                    "num_problems": len(chunk),
                    "pickled_results": row_safe_pickle(chunk),
                    "error_details": pickle.dumps(error_collector)
                    if chunk_id == 0
                    else pickle.dumps(NextEditIntermediateErrorDetails()),
                }
                output_rows.append(row)

        # Convert the list of rows to a DataFrame
        return pd.DataFrame(output_rows)

    with get_session(
        use_gpu=False, max_workers=max_workers, name=output_dir.name
    ) as spark:
        stats = map_parquet.apply_pandas(
            spark,
            process_dataframe,
            input_path=str(input_dir),
            output_path=str(output_dir),
            input_columns=["session_id", "tenant", "pickled_results"],
            batch_size=1,
            ignore_error=True,
            row_size_limit_mb=1600,
            timeout=timeout,
        )
    num_problems = count_problems_in_parquet_files(output_dir)
    stats["total_num_problems"] = num_problems
    stats.pop("task_info", None)
    print(stats)
    return stats


def ground_truth_heuristic_stage(
    input_dir: Path,
    output_dir: Path,
    max_workers: int,
    min_probs_per_session: int = 50,
    timeout: int = 1800,
):
    """Run the ground truth heuristic on the intermediate data."""

    def process_row(session_id: str, tenant: str, pickled_results: bytes):
        args = HeuristicArgs(hunk_context_lines=8)
        intermediate_data: list[NextEditIntermediateType] = pickle.loads(
            pickled_results
        )

        successful_examples = list[tuple[NextEditIntermediateType, GroundTruthEdit]]()
        errors = list[HeuristicFailed]()

        for datum in intermediate_data:
            try:
                gt = NextHunkHeuristic.get_gold_edits(datum, args)
                successful_examples.append((datum, gt))
            except HeuristicFailed as e:
                errors.append(e)

        if len(successful_examples) < min_probs_per_session:
            # Filter out sessions with too few samples
            successful_examples = []

        columns = {
            "session_id": session_id,
            "tenant": tenant,
            "num_problems": len(successful_examples),
            "pickled_results": row_safe_pickle(successful_examples),
            "error_details": row_safe_pickle(errors),
        }

        return pd.Series(columns)

    with get_session(
        use_gpu=False, max_workers=max_workers, name=output_dir.name
    ) as spark:
        stats = map_parquet.apply(
            spark,
            process_row,
            input_path=str(input_dir),
            output_path=str(output_dir),
            batch_size=1,
            ignore_error=True,
            input_columns=["session_id", "tenant", "pickled_results"],
            row_size_limit_mb=1600,
            timeout=timeout,
        )

    num_problems = count_problems_in_parquet_files(output_dir)
    stats["total_num_problems"] = num_problems
    stats.pop("task_info", None)
    print(stats)
    return stats


def make_training_problems_stage(
    input_dir: Path,
    output_dir: Path,
    chunker: SmartChunker,
    max_workers: int,
    timeout: int = 1800,
):
    """Make training problems from the ground truth data."""

    def process_row(session_id: str, tenant: str, pickled_results: bytes):
        result_dataset: list[tuple[NextEditIntermediateType, GroundTruthEdit]] = (
            pickle.loads(pickled_results)
        )
        caches = _get_gcs_caches(tenant)

        problems, stats = make_training_problems(
            Random(42),
            caches,
            result_dataset,
            chunker,
        )
        columns = {
            "repo_path": session_id,  # this needs to be called "repo_path" for the next stage to work
            "tenant": tenant,
            "num_problems": len(problems),
            "pickled_results": row_safe_pickle(problems),
        }
        return pd.Series(columns)

    with get_session(
        use_gpu=False, max_workers=max_workers, name=output_dir.name
    ) as spark:
        stats = map_parquet.apply(
            spark,
            process_row,
            input_path=str(input_dir),
            output_path=str(output_dir),
            input_columns=["session_id", "tenant", "pickled_results"],
            batch_size=1,
            ignore_error=True,
            row_size_limit_mb=1600,
            timeout=timeout,
        )
    num_problems = count_problems_in_parquet_files(output_dir)
    stats["total_num_problems"] = num_problems
    stats.pop("task_info", None)
    print(stats)
    return stats
