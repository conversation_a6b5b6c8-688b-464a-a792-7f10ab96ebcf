from pathlib import Path
from experimental.jiayi.finetuning.training_config_utils import (
    NextEditModelArc,
    StarCoder2Arc,
)
from experimental.jiayi.next_edit.download_checkpoints import (
    download_merge_checkpoint,
    checkpoint_root,
)
from experimental.jiayi.next_edit.quantize_checkpoint import run_quantization
from experimental.jiayi.scripts.copy_model_weights_gcp import copy_to_gcp_command
from research.utils.inspect_indexed_dataset import print_yellow


def download_quantize_upload(
    checkpoint_id: str,
    save_name: str,
    model_arc: NextEditModelArc,
    from_gcp: bool,
    calibration_data_dir: Path,
):
    download_merge_checkpoint(checkpoint_id, save_name, model_arc, from_gcp=from_gcp)
    run_quantization(
        model_arc,
        checkpoint_root / (save_name + "-ffw"),
        calibration_data_dir,
        data_split="train",
    )
    command = copy_to_gcp_command(save_name + "-ffw-fp8")
    print_yellow(
        "Run the following command on your GCP Dev machine (not research pod):"
    )
    print(command)


if __name__ == "__main__":
    download_quantize_upload(
        "c131396d-7f37-49a9-991f-8745687caf24",
        "next-edit-gen/i1-vanguard0-v9_2025_03_27_40days-based_on-S28-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b",
        StarCoder2Arc(size="15b"),
        from_gcp=False,
        calibration_data_dir=Path(
            "/mnt/efs/augment/data/processed/next-edit-hindsight/"
            "i1-vanguard0/indexed_dataset/v9_2025_03_27_40days_R5_ethanol-K50-diff_1_2,P22_star2_seq12k_pause500_out600-pos_0.50-pad_10881"
        ),
    )
