{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import dataclasses\n", "from experimental.jiayi.next_edit.download_hindsight_data import (\n", "    IntermediateDataDownloader,\n", ")\n", "from datetime import datetime, timedelta\n", "import pytz\n", "\n", "\n", "from base.datasets.user_event import (\n", "    NextEditRequestIdIssuedEvent,\n", "    UserEvent,\n", ")\n", "from base.datasets.user_event_lib import (\n", "    UserEventFilters,\n", "    UserEventStream,\n", ")\n", "\n", "user_ids = [\"jiayi\"]\n", "\n", "california_tz = pytz.timezone(\"America/Los_Angeles\")\n", "downloader = IntermediateDataDownloader()\n", "\n", "timestamp_end = california_tz.localize(datetime.fromisoformat(\"2025-02-06T20:30:00.0\"))\n", "total_duration = timed<PERSON>ta(hours=8)\n", "timestamp_begin = timestamp_end - total_duration\n", "future_events_duration = timedelta(hours=1)\n", "\n", "\n", "def loc_time(dt: datetime) -> datetime:\n", "    return dt.astimezone(california_tz)\n", "\n", "\n", "def localize_event(dt: UserEvent) -> UserEvent:\n", "    return dataclasses.replace(dt, time=loc_time(dt.time))\n", "\n", "\n", "latest_request_time = loc_time(timestamp_begin + total_duration)\n", "latest_edit_time = loc_time(latest_request_time + future_events_duration)\n", "filters = UserEventFilters(\n", "    timestamp_begin=timestamp_begin,\n", "    timestamp_end=latest_edit_time,\n", "    user_ids=user_ids,\n", "    event_types=[\n", "        \"next_edit_request_id_issued\",\n", "        \"text_edit\",\n", "    ],\n", ")\n", "stream = UserEventStream.from_query(downloader.caches.tenant, filters, limit=None)\n", "events = [localize_event(e) for e in stream.events]\n", "\n", "request_id_issued_events = [\n", "    event\n", "    for event in events\n", "    if isinstance(event, NextEditRequestIdIssuedEvent)\n", "    and event.time < latest_request_time\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "import pandas as pd\n", "import numpy as np\n", "from base.datasets.hindsight_next_edit_intermediate_dataset import (\n", "    _pick_end_of_streak_events,\n", "    _downsample_events_by_time_uniform,\n", "    filter_requests_by_edits,\n", ")\n", "from datetime import timedelta\n", "from base.datasets.user_event import TextEditEvent\n", "\n", "\n", "edit_events = [event for event in events if isinstance(event, TextEditEvent)]\n", "edit_times = [{\"time\": event.time, \"type\": \"edit\", \"y\": 0.05} for event in edit_events]\n", "\n", "request_times = [\n", "    {\n", "        \"time\": event.time,\n", "        \"type\": \"request_id_issued\",\n", "        \"y\": 0,\n", "        \"request_id\": event.request_id,\n", "    }\n", "    for event in request_id_issued_events\n", "]\n", "\n", "survived_requests = filter_requests_by_edits(\n", "    request_id_issued_events,\n", "    edit_events,\n", ")\n", "survived_times = [\n", "    {\"time\": event.time, \"type\": \"survived\", \"y\": -0.05, \"request_id\": event.request_id}\n", "    for event in survived_requests\n", "]\n", "\n", "downsampled_events = _downsample_events_by_time_uniform(\n", "    survived_requests, <PERSON><PERSON><PERSON>(seconds=5)\n", ")\n", "downsampled_times = [\n", "    {\"time\": event.time, \"type\": \"survived_downsampled\", \"y\": -0.1}\n", "    for event in downsampled_events\n", "]\n", "\n", "end_of_streak_events = _pick_end_of_streak_events(\n", "    survived_requests, <PERSON><PERSON><PERSON>(seconds=5)\n", ")\n", "end_of_streak_times = [\n", "    {\"time\": event.time, \"type\": \"end_of_streak\", \"y\": -0.15}\n", "    for event in end_of_streak_events\n", "]\n", "\n", "\n", "print(f\"Original requests: {len(request_id_issued_events)}\")\n", "print(f\"Survived requests: {len(survived_times)}\")\n", "print(f\"Downsampled requests: {len(downsampled_times)}\")\n", "print(f\"End of streak requests: {len(end_of_streak_times)}\")\n", "\n", "# Create DataFrame with both streams\n", "df = pd.DataFrame(\n", "    sorted(\n", "        request_times\n", "        + edit_times\n", "        + survived_times\n", "        + downsampled_times\n", "        + end_of_streak_times,\n", "        key=lambda x: x[\"time\"],\n", "    )\n", ")\n", "\n", "# Sort by time to interleave the sequences\n", "df = df.sort_values(\"time\")\n", "\n", "# Create timeline visualization with color distinction\n", "fig = px.scatter(\n", "    df,\n", "    x=\"time\",\n", "    y=\"y\",\n", "    color=\"type\",\n", "    title=\"Request Event Timeline\",\n", "    labels={\"time\": \"Time\"},\n", "    hover_data=[\"request_id\"],\n", "    height=500,\n", ")\n", "\n", "# Update layout for better visualization\n", "fig.update_traces(marker=dict(size=4))\n", "fig.update_layout(\n", "    xaxis_title=\"Time\",\n", "    yaxis_showticklabels=False,\n", "    yaxis_title=\"\",\n", "    hovermode=\"x\",\n", "    yaxis_range=[-2, 2],\n", ")\n", "\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}