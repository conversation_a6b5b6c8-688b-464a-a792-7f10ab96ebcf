{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from research.core.utils import pickle_load\n", "from research.next_edits.next_edits_dataset import HindsightEditGenProblem\n", "\n", "\n", "dataset_path = \"/mnt/efs/augment/data/processed/next-edit-hindsight/dogfood-v4_2025_02_10_7days_10s/edit_classification_eval-5000.pkl\"\n", "\n", "problems = pickle_load(dataset_path, list[HindsightEditGenProblem])\n", "\n", "# we use (request_id, current_path, edit_region) as the problem signature, and we rely\n", "# on the signatures to map the eval results back to the correct problems\n", "signature_to_problem = {\n", "    (p.origin.request_id, p.current_path, p.edit_region): p for p in problems\n", "}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# maps checkpoint names to the artifact paths\n", "from pathlib import Path\n", "\n", "from research.eval.harness.tasks.next_edit_gen_eval_task import EvalExampleOutput\n", "\n", "\n", "eval_result_paths = {\n", "    \"v7\": \"/mnt/efs/augment/eval/jobs/4t2cX2wR\",\n", "    \"Dogfood-v4\": \"/mnt/efs/augment/eval/jobs/mTfa84et\",\n", "}\n", "\n", "eval_results = {\n", "    name: pickle_load(Path(path) / \"eval_outputs.pkl\", list[EvalExampleOutput])\n", "    for name, path in eval_result_paths.items()\n", "}\n", "\n", "signature_to_results = dict[tuple, dict[str, EvalExampleOutput]]()\n", "for name, results in eval_results.items():\n", "    for output in results:\n", "        assert output.request_id\n", "        signature = (output.request_id, output.path, output.edit_region)\n", "        signature_to_results.setdefault(signature, {})[name] = output"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.next_edit.inspect_hindsight_eval import EditGenResultComparison\n", "\n", "\n", "comparisons = list[EditGenResultComparison]()\n", "first_eval_outputs = next(iter(eval_results.values()))\n", "for output in first_eval_outputs:\n", "    assert (request_id := output.request_id)\n", "    signature = (request_id, output.path, output.edit_region)\n", "    problem = signature_to_problem[signature]\n", "    results = signature_to_results[signature]\n", "    comparison = EditGenResultComparison(problem=problem, eval_outputs=results)\n", "    comparisons.append(comparison)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from experimental.jiayi.next_edit.inspect_hindsight_eval import (\n", "    render_result_comparisons,\n", ")\n", "\n", "positive_examples = [\n", "    comparison for comparison in comparisons if comparison.problem.ground_truth.changed\n", "]\n", "\n", "old_name = \"v7\"\n", "new_name = \"Dogfood-v4\"\n", "\n", "old_is_better_examples = [\n", "    comparison\n", "    for comparison in positive_examples\n", "    if comparison.is_output_correct_em(old_name)\n", "    and not comparison.is_output_correct_em(new_name)\n", "]\n", "\n", "new_is_better_examples = [\n", "    comparison\n", "    for comparison in positive_examples\n", "    if not comparison.is_output_correct_em(old_name)\n", "    and comparison.is_output_correct_em(new_name)\n", "]\n", "\n", "old_new_both_wrong_examples = [\n", "    comparison\n", "    for comparison in positive_examples\n", "    if not comparison.is_output_correct_em(old_name)\n", "    and not comparison.is_output_correct_em(new_name)\n", "]\n", "\n", "\n", "print(f\"{len(positive_examples)=}\")\n", "print(f\"{len(old_is_better_examples)=}\")\n", "print(f\"{len(new_is_better_examples)=}\")\n", "print(f\"{len(old_new_both_wrong_examples)=}\")\n", "\n", "render_result_comparisons(old_is_better_examples)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}