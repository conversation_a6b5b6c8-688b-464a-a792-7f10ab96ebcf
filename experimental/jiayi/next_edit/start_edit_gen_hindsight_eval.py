"""<PERSON><PERSON><PERSON> to run all edit generation evaluation jobs.

This script should be manually edited and run to launch all the evaluation jobs.
"""

from pathlib import Path
from typing import Literal, assert_never

from base.prompt_format_next_edit.gen_prompt_formatter import (
    default_section_budgets,
    section_budgets_13k,
    section_budgets_10k,
)
from experimental.jiayi.finetuning.training_config_utils import (
    NextEditModelArc,
    Qwen25CoderArc,
    StarCoder2Arc,
)
from experimental.jiayi.evaluation.eval_config_tools import launch_eval
from research.data.spark.pipelines.stages.next_edit_gen_pipelines import (
    raven_retriever_config,
)
from research.utils.inspect_indexed_dataset import print_green

filter_checkpoints = [
    {
        "name": "vzhao/next_edit/checkpoints/S28-R4_ethanol-P21-SCv6,RS1_0.33_-1.0_0.5-pr_grouped_10k-starcoder2_15b_ffw",
        "edit_group_sizes": [1],
        "model_arc": StarCoder2Arc(size="15b"),
    },
    {
        "name": "vzhao/next_edit/checkpoints/S28-R4_ethanol-P21-SCv6,RS1_0.2_-1.0_0.5-pr_grouped_10k-starcoder2_15b_ffw",
        "edit_group_sizes": [1],
        "model_arc": StarCoder2Arc(size="15b"),
    },
    {
        "name": "vzhao/next_edit/checkpoints/S28-R4_ethanol-P21-SCv6,RS1_0.15_-1.0_0.5-pr_grouped_10k-starcoder2_15b_ffw",
        "edit_group_sizes": [1],
        "model_arc": StarCoder2Arc(size="15b"),
    },
]
checkpoints_to_eval = [
    {
        "name": "next-edit-gen/i1-vanguard0-v10_2025_04_03_50days-based_on-S28-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw",
        "edit_group_sizes": [1, 2],
        "model_arc": StarCoder2Arc(size="15b"),
    },
    {
        "name": "next-edit-gen/dogfood-v5_2025_02_10_28days-S29-R5-P22-pr_grouped_10k-starcoder2_15b-ffw",
        "edit_group_sizes": [1, 2],
        "model_arc": StarCoder2Arc(size="15b"),
    },
    {
        "name": "next-edit-gen/S27-R4_ethanol-P18_star2_diff12_seq12k-pr_grouped_10k-starcoder2_15b-ffw",
        "edit_group_sizes": [1],
        "model_arc": StarCoder2Arc(size="15b"),
    },
    {
        "name": "next-edit-gen/S28_wip0.6_small1000-R4_ethanol-P21_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw",
        "edit_group_sizes": [1],
        "model_arc": StarCoder2Arc(size="15b"),
    },
    {
        "name": "next-edit-gen/S29-R5_ethanol-P22_star2_seq12k_pause500_out600-pr_grouped_10k-starcoder2_15b-ffw",
        "edit_group_sizes": [1],
        "model_arc": StarCoder2Arc(size="15b"),
    },
]

use_retrieval = True
# the context length that should be used during eval
context_length: Literal["8k", "12k", "16k"] = "12k"


def get_sys_config(checkpoint_path: Path, model_arc: NextEditModelArc):
    if isinstance(model_arc, StarCoder2Arc):
        model_config = {
            "name": "starcoder2_fastforward",
            "model_path": str(checkpoint_path),
        }
    elif isinstance(model_arc, Qwen25CoderArc):
        model_config = {
            "name": f"fastforward_qwen25coder_{model_arc.size}",
            "checkpoint_path": str(checkpoint_path),
        }
    else:
        assert_never(model_arc)

    if context_length == "16k":
        formatter_config = dict(
            diff_context_lines=9,
            max_prompt_tokens=13_200,
            section_budgets=section_budgets_13k(),
        )
    elif context_length == "12k":
        formatter_config = dict(
            diff_context_lines=9,
            max_prompt_tokens=10_200,
            section_budgets=section_budgets_10k(),
        )
    elif context_length == "8k":
        formatter_config = dict(
            diff_context_lines=9,
            max_prompt_tokens=6_800,
            section_budgets=default_section_budgets(),
        )

    system_config = {
        "name": "next_edit_gen",
        "model": model_config,
        "generation_options": {"max_generated_tokens": 500},
        "retriever": raven_retriever_config,
        "prompt_formatter": formatter_config,
    }
    if not use_retrieval:
        system_config.pop("retriever", None)
    return system_config


tasks = {
    name: {
        "name": "next_edit_gen",
        "eval_source_name": "EvalSourceHindsight",
        "dataset_path": dataset_path,
        "limit_examples": 4000,
    }
    for name, dataset_path in {
        "classify-dogfood-v8_2025_02_22_42days": "/mnt/efs/augment/data/processed/next-edit-hindsight/dogfood-v8_2025_02_22_42days/classification_data.pkl",
    }.items()
}

if __name__ == "__main__":
    for checkpoint in checkpoints_to_eval:
        checkpoint_path = Path("/mnt/efs/augment/checkpoints/") / checkpoint["name"]
        system_config = get_sys_config(checkpoint_path, checkpoint["model_arc"])
        for dataset_name, task_config in tasks.items():
            task_config["edit_group_sizes"] = checkpoint["edit_group_sizes"]
            model_name = checkpoint_path.name
            determined_name = f"{dataset_name}, {model_name}"

            launch_eval(determined_name, system_config, task_config, "1xH100.yaml")
            print_green(f"Eval launched for {determined_name}")
