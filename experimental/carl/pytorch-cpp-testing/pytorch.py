import time

import torch
import torch.nn.functional as F

def main():
    hidden = 1024
    nlayers = 24
    vocab = 52000
    nheads = 16
    headdim = hidden // nheads
    seqlen = 1

    # Don't judge me
    q = []
    qbias = []
    k = []
    kbias = []
    v = []
    vbias = []
    proj = []
    proj_bias = []
    hto4h = []
    hto4h_bias = []
    fourhtoh = []
    fourhtoh_bias = []
    ln1 = []
    ln1_bias = []
    ln2 = []
    ln2_bias = []

    def t(shape):
        return torch.randn(shape, dtype=torch.float16, device="cuda")

    embed = t((vocab, hidden))
    for _ in range(nlayers):
        q.append(t((hidden, hidden)))
        qbias.append(t(hidden))
        k.append(t((hidden, hidden)))
        kbias.append(t(hidden))
        v.append(t((hidden, hidden)))
        vbias.append(t(hidden))
        proj.append(t((hidden, hidden)))
        proj_bias.append(t(hidden))

        hto4h.append(t((4 * hidden, hidden)))
        hto4h_bias.append(t(4 * hidden))
        fourhtoh.append(t((hidden, 4 * hidden)))
        fourhtoh_bias.append(t(hidden))

        ln1.append(t(hidden))
        ln1_bias.append(t(hidden))
        ln2.append(t(hidden))
        ln2_bias.append(t(hidden))

    final_ln_weight = t(hidden)
    final_ln_bias = t(hidden)
    final_proj = t((vocab, hidden))

    x = torch.randint(0, vocab, (seqlen,), dtype=torch.int64, device="cuda")
    torch.cuda.synchronize()

    g = torch.cuda.CUDAGraph()
    static_x = torch.empty((seqlen,), dtype=torch.int64, device="cuda")
    s = torch.cuda.Stream()
    s.wait_stream(torch.cuda.current_stream())
    with torch.cuda.stream(s):
        for _ in range(10):
            h = embed[static_x]
            for i in range(nlayers):
                attn_in = F.layer_norm(h, (hidden,), ln1[i], ln1_bias[i], 1.e-6)
                q_act = F.linear(attn_in, q[i], qbias[i]).view(seqlen, nheads, headdim)
                k_act = F.linear(attn_in, k[i], kbias[i]).view(seqlen, nheads, headdim)
                v_act = F.linear(attn_in, v[i], vbias[i]).view(seqlen, nheads, headdim)
                attn_out = F.scaled_dot_product_attention(
                    q_act.transpose(1, 0),
                    k_act.transpose(1, 0),
                    v_act.transpose(1, 0),
                    is_causal=True,
                ).transpose(1, 0).reshape(seqlen, hidden)
                attn_out = F.linear(attn_out, proj[i], proj_bias[i])
                h = h + attn_out

                ff_in = F.layer_norm(h, (hidden,), ln2[i], ln2_bias[i], 1.e-6)
                ff_in = F.linear(ff_in, hto4h[i], hto4h_bias[i])
                ff_in = F.gelu(ff_in)
                ff_out = F.linear(ff_in, fourhtoh[i], fourhtoh_bias[i])
                h = h + ff_out

            h = F.layer_norm(h, (hidden,), final_ln_weight, final_ln_bias, 1.e-6)
            h = F.linear(h, final_proj)
    torch.cuda.current_stream().wait_stream(s)

    with torch.cuda.graph(g):
        h = embed[static_x]
        for i in range(nlayers):
            attn_in = F.layer_norm(h, (hidden,), ln1[i], ln1_bias[i], 1.e-6)
            q_act = F.linear(attn_in, q[i], qbias[i]).view(seqlen, nheads, headdim)
            k_act = F.linear(attn_in, k[i], kbias[i]).view(seqlen, nheads, headdim)
            v_act = F.linear(attn_in, v[i], vbias[i]).view(seqlen, nheads, headdim)
            attn_out = F.scaled_dot_product_attention(
                q_act.transpose(1, 0),
                k_act.transpose(1, 0),
                v_act.transpose(1, 0),
                is_causal=True,
            ).transpose(1, 0).reshape(seqlen, hidden)
            attn_out = F.linear(attn_out, proj[i], proj_bias[i])
            h = h + attn_out

            ff_in = F.layer_norm(h, (hidden,), ln2[i], ln2_bias[i], 1.e-6)
            ff_in = F.linear(ff_in, hto4h[i], hto4h_bias[i])
            ff_in = F.gelu(ff_in)
            ff_out = F.linear(ff_in, fourhtoh[i], fourhtoh_bias[i])
            h = h + ff_out

        h = F.layer_norm(h, (hidden,), final_ln_weight, final_ln_bias, 1.e-6)
        h = F.linear(h, final_proj)

    static_x.copy_(x)
    torch.cuda.synchronize()
    start = time.time()
    nruns = 100
    for _ in range(nruns):
        g.replay()
    torch.cuda.synchronize()
    end = time.time()
    print(f"{(end - start) * 1000. / nruns}ms per iter")
    print(h.shape)



    # torch.cuda.synchronize()
    # start = time.time()
    # nruns = 10
    # for _ in range(nruns):
    #     h = embed[x]
    #     for i in range(nlayers):
    #         attn_in = F.layer_norm(h, (hidden,), ln1[i], ln1_bias[i], 1.e-6)
    #         q_act = F.linear(attn_in, q[i], qbias[i]).view(seqlen, nheads, headdim)
    #         k_act = F.linear(attn_in, k[i], kbias[i]).view(seqlen, nheads, headdim)
    #         v_act = F.linear(attn_in, v[i], vbias[i]).view(seqlen, nheads, headdim)
    #         attn_out = F.scaled_dot_product_attention(
    #             q_act.transpose(1, 0),
    #             k_act.transpose(1, 0),
    #             v_act.transpose(1, 0),
    #             is_causal=True,
    #         ).transpose(1, 0).reshape(seqlen, hidden)
    #         attn_out = F.linear(attn_out, proj[i], proj_bias[i])
    #         h = h + attn_out

    #         ff_in = F.layer_norm(h, (hidden,), ln2[i], ln2_bias[i], 1.e-6)
    #         ff_in = F.linear(ff_in, hto4h[i], hto4h_bias[i])
    #         ff_in = F.gelu(ff_in)
    #         ff_out = F.linear(ff_in, fourhtoh[i], fourhtoh_bias[i])
    #         h = h + ff_out

    #     h = F.layer_norm(h, (hidden,), final_ln_weight, final_ln_bias, 1.e-6)
    #     h = F.linear(h, final_proj)
    # torch.cuda.synchronize()
    # end = time.time()
    # print(f"{(end - start) * 1000. / nruns}ms per iter")
    # print(h.shape)


if __name__ == "__main__":
    main()
