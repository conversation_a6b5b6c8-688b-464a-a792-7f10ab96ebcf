#include <ATen/ATen.h>
#include <torch/torch.h>

#include <chrono>
#include <iostream>
#include <vector>

int main(int argc, char **argv) {
    // starcoder 1b (except no MQA)
    int hidden = 1024;
    int nlayers = 24;
    int vocab = 52000;
    int nheads = 16;

    int headdim = hidden / nheads;

    int seqlen = 1;
    auto default_options = at::TensorOptions().device(at::kCUDA).dtype(at::kHalf);

    auto embed = at::randn({vocab, hidden}, default_options);
    std::vector<at::Tensor> q, qbias, k, kbias, v, vbias, proj, proj_bias, hto4h, hto4h_bias,
                            fourhtoh, fourhtoh_bias, ln1, ln1_bias, ln2, ln2_bias;
    for (size_t i = 0; i < nlayers; ++i) {
        q.push_back(at::randn({hidden, hidden}, default_options));
        qbias.push_back(at::randn({hidden}, default_options));
        k.push_back(at::randn({hidden, hidden}, default_options));
        kbias.push_back(at::randn({hidden}, default_options));
        v.push_back(at::randn({hidden, hidden}, default_options));
        vbias.push_back(at::randn({hidden}, default_options));
        proj.push_back(at::randn({hidden, hidden}, default_options));
        proj_bias.push_back(at::randn({hidden}, default_options));
        hto4h.push_back(at::randn({4 * hidden, hidden}, default_options));
        hto4h_bias.push_back(at::randn({4 * hidden}, default_options));
        fourhtoh.push_back(at::randn({hidden, 4 * hidden}, default_options));
        fourhtoh_bias.push_back(at::randn({hidden}, default_options));
        ln1.push_back(at::randn({hidden}, default_options));
        ln1_bias.push_back(at::randn({hidden}, default_options));
        ln2.push_back(at::randn({hidden}, default_options));
        ln2_bias.push_back(at::randn({hidden}, default_options));
    }
    auto final_ln_weight = at::randn({hidden}, default_options);
    auto final_ln_bias = at::randn({hidden}, default_options);
    auto final_proj = at::randn({vocab, hidden}, default_options);
    auto x = at::randint(0, vocab, {seqlen}, at::device(at::kCUDA).dtype(at::kLong));

    torch::cuda::synchronize();
    for (int warmup = 0; warmup < 10; ++warmup) {
        auto h = embed.index({x});

        for (size_t i = 0; i < nlayers; ++i) {
            auto attn_in = at::layer_norm(h, {hidden}, ln1[i], ln1_bias[i], 1.e-6);
            auto q_act = at::linear(attn_in, q[i], qbias[i]).reshape({seqlen, nheads, headdim});
            auto k_act = at::linear(attn_in, k[i], kbias[i]).reshape({seqlen, nheads, headdim});
            auto v_act = at::linear(attn_in, v[i], vbias[i]).reshape({seqlen, nheads, headdim});
            // TODO: rotary goes here
            // PyTorch expects [nheads, seqlen, headdim]
            auto attn_out = at::scaled_dot_product_attention(at::transpose(q_act, 1, 0),
                                                            at::transpose(k_act, 1, 0),
                                                            at::transpose(v_act, 1, 0),
                                                            {}, 0.0, true);
            // attn_out is now [nheads, seqlen, headdim] -- turn back to [seqlen, hidden]
            attn_out = at::transpose(attn_out, 1, 0).reshape({seqlen, hidden});
            attn_out = at::linear(attn_out, proj[i], proj_bias[i]);
            h = h + attn_out;

            auto ff_in = at::layer_norm(h, {hidden}, ln2[i], ln2_bias[i], 1.e-6);
            ff_in = at::linear(ff_in, hto4h[i], hto4h_bias[i]);
            ff_in = at::gelu(ff_in);
            auto ff_out = at::linear(ff_in, fourhtoh[i], fourhtoh_bias[i]);
            h = h + ff_out;
        }
        h = at::layer_norm(h, {hidden}, final_ln_weight, final_ln_bias, 1.e-6);
        h = at::linear(h, final_proj);
    }

    torch::cuda::synchronize();
    auto start = std::chrono::system_clock::now();
    int nruns = 10;
    for (int run = 0; run < nruns; ++run) {
        auto h = embed.index({x});

        for (size_t i = 0; i < nlayers; ++i) {
            auto attn_in = at::layer_norm(h, {hidden}, ln1[i], ln1_bias[i], 1.e-6);
            auto q_act = at::linear(attn_in, q[i], qbias[i]).reshape({seqlen, nheads, headdim});
            auto k_act = at::linear(attn_in, k[i], kbias[i]).reshape({seqlen, nheads, headdim});
            auto v_act = at::linear(attn_in, v[i], vbias[i]).reshape({seqlen, nheads, headdim});
            // TODO: rotary goes here
            // PyTorch expects [nheads, seqlen, headdim]
            auto attn_out = at::scaled_dot_product_attention(at::transpose(q_act, 1, 0),
                                                            at::transpose(k_act, 1, 0),
                                                            at::transpose(v_act, 1, 0),
                                                            {}, 0.0, true);
            // attn_out is now [nheads, seqlen, headdim] -- turn back to [seqlen, hidden]
            attn_out = at::transpose(attn_out, 1, 0).reshape({seqlen, hidden});
            attn_out = at::linear(attn_out, proj[i], proj_bias[i]);
            h = h + attn_out;

            auto ff_in = at::layer_norm(h, {hidden}, ln2[i], ln2_bias[i], 1.e-6);
            ff_in = at::linear(ff_in, hto4h[i], hto4h_bias[i]);
            ff_in = at::gelu(ff_in);
            auto ff_out = at::linear(ff_in, fourhtoh[i], fourhtoh_bias[i]);
            h = h + ff_out;
        }
        h = at::layer_norm(h, {hidden}, final_ln_weight, final_ln_bias, 1.e-6);
        h = at::linear(h, final_proj);
    }
    torch::cuda::synchronize();
    auto end = std::chrono::system_clock::now();
    std::chrono::duration<double> elapsed_seconds = end - start;
    std::cout << (elapsed_seconds.count() * 1000. / nruns) << "ms per iter" << std::endl;
    return 0;
}
