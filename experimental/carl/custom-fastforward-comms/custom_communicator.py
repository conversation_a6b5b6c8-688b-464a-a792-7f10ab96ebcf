import cuda
import custom_communicator_extension as comm_ext
import torch
import torch.distributed as dist
from cuda import cudart


def cuda_assert(err: cuda.CUresult):
    if isinstance(err, cuda.CUresult):
        if err != cuda.CUresult.CUDA_SUCCESS:
            raise RuntimeError(f"Cuda Error: {err}")
    else:
        raise RuntimeError(f"Unknown error type: {err}")


def _allgather_ipc_handles(my_handle, world_size):
    if dist.get_world_size() != world_size:
        raise RuntimeError("World size must match")

    my_handle_tensor = torch.frombuffer(
        bytearray(my_handle.reserved), dtype=torch.uint8
    ).cuda()
    gather_results = [torch.empty_like(my_handle_tensor) for _ in range(world_size)]
    dist.all_gather(gather_results, my_handle_tensor)
    all_handles = []
    for handle_tensor in gather_results:
        handle = cudart.cudaIpcMemHandle_t()
        handle.reserved = bytes(handle_tensor.to_list())
        all_handles.append(handle)
    return all_handles


def _open_ipc_handles(all_handles, my_rank, my_ptr):
    all_ptrs = []
    for i, handle in enumerate(all_handles):
        if i == my_rank:
            all_ptrs.append(my_ptr)
        else:
            err, peer_ptr = cudart.cudaIpcOpenMemHandle(
                handle, cudart.cudaIpcMemLazyEnablePeerAccess
            )
            cuda_assert(err)
            all_ptrs.append(peer_ptr)
    return all_ptrs


def _cuda_malloc(nbytes):
    err, ptr = cudart.cudaMalloc(nbytes)
    cuda_assert(err)
    return ptr


class CustomCommunicator:
    def __init__(self, msg_numel, msg_dtype):
        self.msg_numel = msg_numel
        self.msg_dtype = msg_dtype
        self.is_initialized = False
        self.step_is_started = False

        self.world_size = None
        self.rank = None
        self.all_input_ptrs = []
        self.output_ptr = None
        self.all_barrier_ptrs = []
        self.barrier_count = None
        self.params_ptr = None

    # TODO: I have no idea if this is at all sane
    def __del__(self):
        if not getattr(self, "is_initialized", False):
            return

        # 1) Close all opened handles to _peer_ ptrs
        for ptr_list in [self.all_input_ptrs, self.all_barrier_ptrs]:
            for i, ptr in enumerate(ptr_list):
                if i != self.rank:
                    err = cudart.cudaIpcCloseMemHandle(ptr)
                    cuda_assert(err)

        # 2) Barrier -- do not free before unmapping
        dist.barrier()

        # 3) Free all of my own allocations:
        for ptr in [
            self.all_input_ptrs[self.rank],
            self.output_ptr,
            self.all_barrier_ptrs[self.rank],
            self.params_ptr,
        ]:
            err = cudart.cudaFree(ptr)
            cuda_assert(err)

    def initialize(self):
        if not dist.is_initialized():
            raise RuntimeError("Must initialize distributed first")
        self.world_size = dist.get_world_size()
        self.rank = dist.get_rank()

        # Step 1: allocate buffers
        msg_size_bytes = self.msg_numel * self.msg_dtype.itemsize
        input_ptr = _cuda_malloc(msg_size_bytes)
        self.output_ptr = _cuda_malloc(msg_size_bytes)
        # Barrier buffer:
        # - You need two buffers, each with world_size elements of uint32_t
        barrier_size_bytes = 2 * self.world_size * 4
        barrier_ptr = _cuda_malloc(barrier_size_bytes)
        err = cudart.cudaMemset(barrier_ptr, 0, barrier_size_bytes)
        cuda_assert(err)
        self.params_ptr = _cuda_malloc(comm_ext.allreduce_params_size())

        # Step 2: open handles to the buffers
        err, input_handle = cudart.cudaIpcGetMemHandle(input_ptr)
        cuda_assert(err)
        err, barrier_handle = cudart.cudaIpcGetMemHandle(barrier_ptr)
        cuda_assert(err)

        # Step 3: all-gather the handles
        all_input_handles = _allgather_ipc_handles(input_handle, self.world_size)
        all_barrier_handles = _allgather_ipc_handles(barrier_handle, self.world_size)

        # Step 4: open mem-handles to peers (note: don't open the self handle!)
        self.all_input_ptrs = _open_ipc_handles(all_input_handles, self.rank, input_ptr)
        self.all_barrier_ptrs = _open_ipc_handles(
            all_barrier_handles, self.rank, barrier_ptr
        )

        self.barrier_count = 0
        self.is_initialized = True

    def start_step(self):
        if not self.is_initialized:
            raise RuntimeError("Must initialize first")
        if self.barrier_count != 0:
            raise RuntimeError("Must `end_step()` before starting a new step")
        self.step_is_started = True

    def all_reduce(self):
        if not self.is_initialized:
            raise RuntimeError("Must initialize first")
        if not self.step_is_started:
            raise RuntimeError("Must `start_step()` before calling `all_reduce()`")
        self.barrier_count += 1
        # call extension allreduce with:
        # - all_input_ptrs
        # - all_buffer_ptrs
        # - output_ptr
        # - message_size
        # - world_size
        # - my_rank
        # - barrier_count
        # - datatype
        # TODO:
        # - Create an AllreduceParams struct

    # TODO: methods (or attributes) that expose the input and output buffers as tensors
    # Afaik, the best way to do this will be to call into a cpp extension where we can use:
    #  torch::from_blob(ptr, ...)
    # See: https://pytorch.org/cppdocs/api/function_namespacetorch_1ad7fb2a7759ef8c9443b489ddde494787.html
    # Note that alternatively, we could store all the buffers in this class as torch tensors
    # that have been converted from_blob and then rely on Deleters for cleanup?
    # Maybe even allocate _as torch tensors_ and then open ipc handles on them? (Kind of sketchy)

    def end_step(self):
        if not self.is_initialized:
            raise RuntimeError("Must initialize first")
        if not self.step_is_started:
            raise RuntimeError("Must `start_step()` before ending a step")
        # TODO: barrier-and-zero the barrier buffer
        self.step_is_started = False
        self.barrier_count = 0
