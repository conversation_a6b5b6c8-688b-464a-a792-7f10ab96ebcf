#include <cuda.h>
#include <cuda_bf16.h>
#include <cuda_fp16.h>
#include <mpi.h>

#include <cassert>
#include <chrono>
#include <cstdio>
#include <iostream>
#include <thread>

#define CHECK_CUDA(val) check((val), #val, __FILE__, __LINE__)
void check(cudaError_t err, const char* const func, const char* const file, const int line) {
    if (err != cudaSuccess) {
        std::cerr << "CUDA Runtime Error at: " << file << ":" << line << std::endl;
        std::cerr << cudaGetErrorString(err) << " " << func << std::endl;
        std::exit(EXIT_FAILURE);
    }
}

namespace ffwd::allreduce {

template <typename T1, typename T2>
inline size_t divUp(const T1& a, const T2& n) {
    size_t tmp_a = static_cast<size_t>(a);
    size_t tmp_n = static_cast<size_t>(n);
    return (tmp_a + tmp_n - 1) / tmp_n;
}

inline int roundUp(int a, int n) { return divUp(a, n) * n; }

// flag functions
static inline __device__ void st_flag_release(uint32_t const& flag, uint32_t* flag_addr) {
    asm volatile("st.global.release.sys.b32 [%1], %0;" ::"r"(flag), "l"(flag_addr));
}

static inline __device__ uint32_t ld_flag_acquire(uint32_t* flag_addr) {
    uint32_t flag;
    asm volatile("ld.global.acquire.sys.b32 %0, [%1];" : "=r"(flag) : "l"(flag_addr));
    return flag;
}

// Flag notes:
// Each process has two buffers:
// - gpu_barrier_buf: 2 * world_size elements
//  - Write flag to bufs[peer_rank][my_rank] for all peer_ranks in [0, nranks)
//  - Spin on bufs[my_rank][peer_rank] for all peer_ranks in [0, nranks)
//  - The 2x is for the 2nd barrier (not clear why!)
// - block_barrier_buf: 2 * grid_size * world_size elements
//  - Write flag to bufs[peer_rank][my_block][my_rank] for peer_ranks in [0, nranks)
//  - Spin on bufs[my_rank][my_block][peer_rank] for peer_ranks in [0, nranks)
//  - Same on the 2x (who knows!)

__inline__ __device__ void multi_gpu_barrier(uint32_t** flag_bufs, uint32_t flag_val,
                                             size_t my_rank, size_t world_size, int tidx,
                                             int bidx) {
    // Thread i handles the flags for peer_rank == i. Other threads will wait at syncthreads.
    if (tidx < world_size) {
        uint32_t* my_flag_buf = flag_bufs[my_rank];
        uint32_t* peer_flag_buf = flag_bufs[tidx];

        // Elect the first block to do the writing, but every block will do waiting
        if (bidx == 0) {
            st_flag_release(flag_val, peer_flag_buf + my_rank);
        }
        uint32_t* my_flag_addr = my_flag_buf + tidx;
        while (ld_flag_acquire(my_flag_addr) != flag_val) {
            /* spin */
        }

        // Now do it again!
        if (bidx == 0) {
            st_flag_release(flag_val, peer_flag_buf + world_size + my_rank);
        }
        my_flag_addr = my_flag_buf + world_size + tidx;
        while (ld_flag_acquire(my_flag_addr) != flag_val) {
            /* spin */
        }
    }
    __syncthreads();
}

__inline__ __device__ void block_barrier(uint32_t** flag_bufs, uint32_t flag_val, size_t my_rank,
                                         size_t world_size, int tidx, int bidx, int grid_size) {
    // Thread i handles the flags for peer_rank == i. Other threads wait at syncthreads.
    if (tidx < world_size) {
        uint32_t* my_flag_buf = flag_bufs[my_rank];
        uint32_t* peer_flag_buf = flag_bufs[tidx];

        // Each block writes flag to bufs[peer_rank][my_block][my_rank]
        uint32_t block_offset = bidx * world_size;
        st_flag_release(flag_val, peer_flag_buf + block_offset + my_rank);

        // Then wait on bufs[my_rank][my_block][peer_rank]
        uint32_t* my_flag_addr = my_flag_buf + block_offset + tidx;
        while (ld_flag_acquire(my_flag_addr) != flag_val) {
            /* spin */
        }

        // Do it again!
        block_offset += grid_size * world_size;
        st_flag_release(flag_val, peer_flag_buf + block_offset + my_rank);

        my_flag_addr = my_flag_buf + block_offset + tidx;
        while (ld_flag_acquire(my_flag_addr) != flag_val) {
            /* spin */
        }
    }
    __syncthreads();
}

using PackedFloat = union {
    int4 packed;
    float unpacked[4];
};

using PackedHalf = union {
    int4 packed;
    half2 unpacked[4];
};

template <typename T>
struct PackedOn16Bytes {};

template <>
struct PackedOn16Bytes<float> {
    using Type = PackedFloat;
};

template <>
struct PackedOn16Bytes<half> {
    using Type = PackedHalf;
};

using PackedBFloat16 = union {
    int4 packed;
    __nv_bfloat162 unpacked[4];
};

template <>
struct PackedOn16Bytes<__nv_bfloat16> {
    using Type = PackedBFloat16;
};

template <typename T>
inline __device__ int4 add128b(T& a, T& b) {
    T c;
    c.unpacked[0] = a.unpacked[0] + b.unpacked[0];
    c.unpacked[1] = a.unpacked[1] + b.unpacked[1];
    c.unpacked[2] = a.unpacked[2] + b.unpacked[2];
    c.unpacked[3] = a.unpacked[3] + b.unpacked[3];
    return c.packed;
}

constexpr size_t MAX_RANKS_PER_NODE = 8;
constexpr size_t DEFAULT_BLOCK_SIZE = 1024;
constexpr size_t WARP_SIZE = 32;
constexpr size_t MAX_ALL_REDUCE_BLOCKS = 24;

struct AllReduceParams {
    size_t elts_total;
    size_t elts_per_rank;
    size_t elts_per_block;
    size_t local_rank;
    uint32_t barrier_flag;
    uint32_t* gpu_barrier_ptrs[MAX_RANKS_PER_NODE];
    uint32_t* block_barrier_ptrs[MAX_RANKS_PER_NODE];

    void* peer_comm_buffer_ptrs[MAX_RANKS_PER_NODE];
    void* local_output_buffer_ptr;
};

template <typename T, int RANKS_PER_NODE>
static __global__ void oneShotAllReduceKernel(AllReduceParams params) {
    int const bidx = blockIdx.x;
    int const tidx = threadIdx.x;

    static constexpr int PACKED_ELTS = 16 / sizeof(T);
    using PackedStruct = typename PackedOn16Bytes<T>::Type;

    T* local_output_buffer = reinterpret_cast<T*>(params.local_output_buffer_ptr);

    // Start and end offsets of the thread
    size_t const chunk_start = bidx * params.elts_per_block + tidx * PACKED_ELTS;
    size_t const chunk_end = min((bidx + 1) * params.elts_per_block, params.elts_total);

    T* buffers[RANKS_PER_NODE];
#pragma unroll
    for (int ii = 0; ii < RANKS_PER_NODE; ++ii) {
        int rank = (params.local_rank + ii) % RANKS_PER_NODE;
        buffers[ii] = reinterpret_cast<T*>(params.peer_comm_buffer_ptrs[rank]);
    }
    multi_gpu_barrier(params.gpu_barrier_ptrs, params.barrier_flag, params.local_rank,
                      RANKS_PER_NODE, tidx, bidx);

    for (size_t iter_offset = chunk_start; iter_offset < chunk_end;
         iter_offset += blockDim.x * PACKED_ELTS) {
        PackedStruct vals[RANKS_PER_NODE];
#pragma unroll
        for (int ii = 0; ii < RANKS_PER_NODE; ++ii) {
            vals[ii].packed = *reinterpret_cast<int4 const*>(&buffers[ii][iter_offset]);
        }

        PackedStruct sums;
        sums.packed = {0, 0, 0, 0};
#pragma unroll
        for (int rank = 0; rank < RANKS_PER_NODE; ++rank) {
            int ii = (rank + RANKS_PER_NODE - params.local_rank) % RANKS_PER_NODE;
            sums.packed = add128b(sums, vals[ii]);
        }
        *reinterpret_cast<int4*>(&local_output_buffer[iter_offset]) = sums.packed;
    }
}

template <typename T, int RANKS_PER_NODE>
static __global__ void twoShotAllReduceKernel(AllReduceParams params) {
    int bidx = blockIdx.x;
    int tidx = threadIdx.x;
    int grid_size = gridDim.x;

    static constexpr int PACKED_ELTS = 16 / sizeof(T);
    // PackedType.packed is an int4 (128 bytes)
    // PackedType.unpacked is a 4-element array of {float, half2, bfloat2}
    using PackedType = typename PackedOn16Bytes<T>::Type;

    T* local_shared_buffer = reinterpret_cast<T*>(params.peer_comm_buffer_ptrs[params.local_rank]);
    T* local_output_buffer = reinterpret_cast<T*>(params.local_output_buffer_ptr);

    size_t chunk_start = bidx * params.elts_per_block + tidx * PACKED_ELTS;
    size_t chunk_end = min(chunk_start + params.elts_per_block, params.elts_per_rank);

    T* buffers[RANKS_PER_NODE];
    int ranks[RANKS_PER_NODE];
#pragma unroll
    for (int i = 0; i < RANKS_PER_NODE; ++i) {
        // Remap the ranks so a loop over ranks is shifted for each GPU
        int rank = (params.local_rank + i) % RANKS_PER_NODE;
        ranks[i] = rank;
        buffers[i] = reinterpret_cast<T*>(params.peer_comm_buffer_ptrs[rank]);
    }

    multi_gpu_barrier(params.gpu_barrier_ptrs, params.barrier_flag, params.local_rank,
                      RANKS_PER_NODE, tidx, bidx);

    int rank_offset = params.local_rank * params.elts_per_rank;
    // Data is ready to read -- time to reduce_scatter.
    // Outer loop is over offset into a chunk
    for (size_t local_offset = chunk_start; local_offset < chunk_end;
         local_offset += blockDim.x * PACKED_ELTS) {
        // How far to offset based on which rank has this data
        size_t responsible_block_offset = local_offset + rank_offset;

        // Inner loop: Read a 128 byte slice (per thread) from every rank and update sum
        PackedType vals[RANKS_PER_NODE];
#pragma unroll
        for (int i = 0; i < RANKS_PER_NODE; ++i) {
            vals[i].packed = *reinterpret_cast<int4 const*>(&buffers[i][responsible_block_offset]);
        }

        // Reduce those 128 bytes of values (either as 4x floats or 8x halfs)
        PackedType sums;
        sums.packed = {0, 0, 0, 0};
#pragma unroll
        for (int rank = 0; rank < RANKS_PER_NODE; ++rank) {
            // Always start from real rank 0 for stable order.
            int i = (rank + RANKS_PER_NODE - params.local_rank) % RANKS_PER_NODE;
            sums.packed = add128b(sums, vals[i]);
        }

        // Save the reduce value into the local shared buffer
        *reinterpret_cast<int4*>(&local_shared_buffer[responsible_block_offset]) = sums.packed;
    }

    block_barrier(params.block_barrier_ptrs, params.barrier_flag, params.local_rank, RANKS_PER_NODE,
                  tidx, bidx, grid_size);

    // Allgather the ready data for this block
    // Outer loop is the same: over offset into the chunk this block is responsible for
    for (size_t local_offset = chunk_start; local_offset < chunk_end;
         local_offset += blockDim.x * PACKED_ELTS) {
        // Inner loop: read the per-block slice from every GPU
#pragma unroll
        for (int i = 0; i < RANKS_PER_NODE; ++i) {
            // Use the shifted `ranks` to find starting place
            size_t offset_rank = ranks[i] * params.elts_per_rank + local_offset;
            if (offset_rank >= params.elts_total) {
                continue;
            }
            *reinterpret_cast<int4*>(&local_output_buffer[offset_rank]) =
                *reinterpret_cast<int4*>(&buffers[i][offset_rank]);
        }
    }
}

template <typename T>
static void oneShotAllReduceKernel(
    AllReduceParams params,
    int world_size,
    int blocks_per_grid,
    int threads_per_block
) {
    switch (world_size) {
        case 2:
            oneShotAllReduceKernel<T, 2><<<blocks_per_grid, threads_per_block>>>(params);
            break;
        case 4:
            oneShotAllReduceKernel<T, 4><<<blocks_per_grid, threads_per_block>>>(params);
            break;
        case 8:
            oneShotAllReduceKernel<T, 8><<<blocks_per_grid, threads_per_block>>>(params);
            break;
        default:
            assert(false);
    }
}

template <typename T>
static void twoShotAllReduceKernel(
    AllReduceParams params,
    int world_size,
    int blocks_per_grid,
    int threads_per_block
) {
    switch (world_size) {
        case 2:
            twoShotAllReduceKernel<T, 2><<<blocks_per_grid, threads_per_block>>>(params);
            break;
        case 4:
            twoShotAllReduceKernel<T, 4><<<blocks_per_grid, threads_per_block>>>(params);
            break;
        case 8:
            twoShotAllReduceKernel<T, 8><<<blocks_per_grid, threads_per_block>>>(params);
            break;
        default:
            assert(false);
    }
}

};  // namespace ffwd::allreduce

using namespace ffwd::allreduce;

template <typename T>
__global__ void slowfill(T* x, size_t n, float val) {
    int tidx = threadIdx.x;
    for (size_t i = tidx; i < n; i += blockDim.x) {
        x[i] = static_cast<T>(val);
    }
}

/*
To compile:
 > nvcc -arch=sm_90 -I /usr/local/mpi/include -L /usr/local/mpi/lib -lmpi newCustomAllreduce.cu

To run:
 > mpirun -np <WORLD_SIZE> ./a.out

To profile:
 > nsys nvprof --print-gpu-trace mpirun -np <WORLD_SIZE> ./a.out

(Install `nsys` with `sudo dpkg -i
/mnt/efs/augment/user/carl/NsightSystems-linux-cli-public-2024.4.1.61-3431596.deb)
*/

int main(int argc, char** argv) {
    MPI_Init(NULL, NULL);
    int rank, world_size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &world_size);
    CHECK_CUDA(cudaSetDevice(rank));

    using ElemT = __nv_bfloat16;
    int numel = 2048 * 8192;
    bool use_one_shot = false;

    // Allocate input data and fill with a constant value
    ElemT* x;
    cudaMalloc(&x, numel * sizeof(*x));
    slowfill<<<1, 1024>>>(x, numel, 1.0 * (rank + 1));

    // Compute kernel launch parameters
    size_t elts_per_thread = 16 / sizeof(ElemT);
    size_t threads_per_block = DEFAULT_BLOCK_SIZE;
    size_t total_threads = 0;
    int blocks_per_grid = 0;
    if (use_one_shot) {
        assert(numel % elts_per_thread == 0);
        total_threads = roundUp(numel / elts_per_thread, WARP_SIZE);
        threads_per_block = std::min(DEFAULT_BLOCK_SIZE, total_threads);
        blocks_per_grid = std::min(MAX_ALL_REDUCE_BLOCKS, divUp(total_threads, threads_per_block));
    } else {
        // NOTE: two-shot has fewer threads since you're reading only 1/Nth of the data
        // This means you are more latency-bound for small messages
        assert(numel % (elts_per_thread * world_size) == 0);
        total_threads = roundUp(numel / (elts_per_thread * world_size), WARP_SIZE);
        blocks_per_grid = 1;
        while (total_threads % blocks_per_grid != 0 ||
               total_threads / blocks_per_grid > DEFAULT_BLOCK_SIZE) {
            blocks_per_grid += 1;
        }
        threads_per_block = total_threads / blocks_per_grid;

        if (blocks_per_grid > MAX_ALL_REDUCE_BLOCKS) {
            size_t iter_factor = 1;
            while (blocks_per_grid / iter_factor > MAX_ALL_REDUCE_BLOCKS ||
                   blocks_per_grid % iter_factor) {
                iter_factor += 1;
            }
            blocks_per_grid /= iter_factor;
        }
    }

    AllReduceParams params;
    params.elts_total = numel;
    params.elts_per_rank = params.elts_total / world_size;
    if (use_one_shot) {
        params.elts_per_block = roundUp(divUp(params.elts_total, blocks_per_grid), elts_per_thread);
    } else {
        params.elts_per_block =
            roundUp(divUp(params.elts_per_rank, blocks_per_grid), elts_per_thread);
    }
    params.local_rank = rank;
    params.barrier_flag = 1;

    // TMP -- playing with block sizing for speed
    params.elts_per_block /= 2;
    blocks_per_grid *= 2;

    printf(
        "elts_total=%zu, elts_per_rank=%zu, elts_per_block=%zu, local_rank=%zu, barrier_flag=%u\n",
        params.elts_total, params.elts_per_rank, params.elts_per_block, params.local_rank,
        params.barrier_flag);
    printf("threads_per_block=%zu, blocks_per_grid=%d\n", threads_per_block, blocks_per_grid);

    // Allocate output and barrier buffers
    cudaMalloc(&params.local_output_buffer_ptr, numel * sizeof(*x));
    uint32_t *gpu_barrier, *block_barrier;
    cudaMalloc(&gpu_barrier, 2 * world_size * sizeof(uint32_t));
    cudaMemset(gpu_barrier, 0, 2 * world_size * sizeof(uint32_t));
    cudaMalloc(&block_barrier, 2 * world_size * blocks_per_grid * sizeof(uint32_t));
    cudaMemset(block_barrier, 0, 2 * world_size * blocks_per_grid * sizeof(uint32_t));

    // Open and all-gather handles to input and barrier buffers
    cudaIpcMemHandle_t data_handles[MAX_RANKS_PER_NODE];
    CHECK_CUDA(cudaIpcGetMemHandle(data_handles + rank, x));
    cudaIpcMemHandle_t gpu_barrier_handles[MAX_RANKS_PER_NODE];
    CHECK_CUDA(cudaIpcGetMemHandle(gpu_barrier_handles + rank, gpu_barrier));
    cudaIpcMemHandle_t block_barrier_handles[MAX_RANKS_PER_NODE];
    CHECK_CUDA(cudaIpcGetMemHandle(block_barrier_handles + rank, block_barrier));

    MPI_Allgather(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, data_handles, sizeof(cudaIpcMemHandle_t),
                  MPI_BYTE, MPI_COMM_WORLD);
    MPI_Allgather(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, gpu_barrier_handles,
                  sizeof(cudaIpcMemHandle_t), MPI_BYTE, MPI_COMM_WORLD);
    MPI_Allgather(MPI_IN_PLACE, 0, MPI_DATATYPE_NULL, block_barrier_handles,
                  sizeof(cudaIpcMemHandle_t), MPI_BYTE, MPI_COMM_WORLD);

    // Set pointers for input and barrier buffers
    for (int i = 0; i < world_size; ++i) {
        if (i == rank) {
            params.peer_comm_buffer_ptrs[i] = x;
            params.gpu_barrier_ptrs[i] = gpu_barrier;
            params.block_barrier_ptrs[i] = block_barrier;
        } else {
            CHECK_CUDA(cudaIpcOpenMemHandle(&params.peer_comm_buffer_ptrs[i], data_handles[i],
                                            cudaIpcMemLazyEnablePeerAccess));
            CHECK_CUDA(cudaIpcOpenMemHandle((void**)&params.gpu_barrier_ptrs[i],
                                            gpu_barrier_handles[i],
                                            cudaIpcMemLazyEnablePeerAccess));
            CHECK_CUDA(cudaIpcOpenMemHandle((void**)&params.block_barrier_ptrs[i],
                                            block_barrier_handles[i],
                                            cudaIpcMemLazyEnablePeerAccess));
        }
    }
    CHECK_CUDA(cudaDeviceSynchronize());
    if (use_one_shot) {
        oneShotAllReduceKernel<ElemT>(params, world_size, blocks_per_grid, threads_per_block);
    } else {
        twoShotAllReduceKernel<ElemT>(params, world_size, blocks_per_grid, threads_per_block);
    }

    CHECK_CUDA(cudaDeviceSynchronize());
    for (int i = 0; i < 10; ++i) {
        params.barrier_flag += 1;
        if (use_one_shot) {
            oneShotAllReduceKernel<ElemT>(params, world_size, blocks_per_grid, threads_per_block);
        } else {
            twoShotAllReduceKernel<ElemT>(params, world_size, blocks_per_grid, threads_per_block);
        }
    }
    CHECK_CUDA(cudaDeviceSynchronize());
    // float *h_x = new float[numel];
    // cudaMemcpy(h_x, x, numel * sizeof(*h_x), cudaMemcpyDeviceToHost);
    // for (int i = 0; i < numel; i += 64) {
    //     printf("%d: %f\n", rank, h_x[i]);
    // }
    MPI_Finalize();
    return 0;
}
