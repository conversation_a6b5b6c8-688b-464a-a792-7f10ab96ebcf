{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import torch\n", "from base.fastforward.starcoder import fwd_starcoder, fwd_starcoder_fp8, model_specs\n", "from base.fastforward import cached_attention, fwd\n", "from base.fastforward.llama import (\n", "    fwd_llama,\n", "    fwd_llama_fp8,\n", "    model_specs as llama_model_specs,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eth_path = \"/home/<USER>/starteth-fp8\"\n", "eth_sha = \"ea80339a0fa868f1e474c9c92b919360e0c0dfbd3e6566d72258ce3b06e7a019\"\n", "\n", "ms = model_specs.get_starcoder_model_spec(\n", "    model_name=\"starcoder-1b\",\n", "    checkpoint_path=eth_path,\n", "    checkpoint_sha256=eth_sha,\n", ")\n", "ms.output_projection_dim = 512\n", "step_fn = fwd_starcoder_fp8.generate_step_fn(\n", "    ms,\n", "    auto_capture_graphs=False,\n", "    output_type=fwd.OutputTensorType.EMBEDDING,\n", ")\n", "attn_factory = fwd_starcoder.StarcoderAttentionFactory(\n", "    ms,\n", "    attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,\n", ")\n", "attn = attn_factory(8192)\n", "attn.reset(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["eth_path_fp16 = \"/mnt/efs/augment/checkpoints/ethanol/smart/stareth_smart_128doc_2000s/global_step2000-ckpt_v2\"\n", "eth_sha_fp16 = \"79fc9c4d484a35a6563119c6ceb686ac4f3bd8b7d97f2fea27494ff2704de191\"\n", "\n", "ms_fp16 = model_specs.get_starcoder_model_spec(\n", "    model_name=\"starcoder-1b\",\n", "    checkpoint_path=eth_path_fp16,\n", "    checkpoint_sha256=eth_sha_fp16,\n", ")\n", "step_fn_fp16 = fwd_starcoder.generate_step_fn(\n", "    ms_fp16,\n", "    auto_capture_graphs=False,\n", "    output_type=fwd.OutputTensorType.EMBEDDING,\n", ")\n", "attn_factory_fp16 = fwd_starcoder.StarcoderAttentionFactory(\n", "    ms_fp16,\n", "    attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,\n", ")\n", "attn_fp16 = attn_factory_fp16(8192)\n", "attn.reset(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from megatron.data import indexed_dataset\n", "\n", "ds_path = (\n", "    \"/mnt/efs/spark-data/shared/ethanol/med128smartnoheader/indexed_dataset/dataset\"\n", ")\n", "ds = indexed_dataset.make_dataset(ds_path, impl=\"mmap\", skip_warmup=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["x = ds[8888].tolist()[:8192]\n", "attn.reset(0)\n", "attn_fp16.reset(0)\n", "emb_fp8 = step_fn(x, attn).checked_cast(torch.Tensor)\n", "emb_fp16 = step_fn_fp16(x, attn_fp16).checked_cast(torch.Tensor)\n", "attn.reset(0)\n", "attn_fp16.reset(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["torch.nn.functional.cosine_similarity(emb_fp8, emb_fp16, dim=1).sort().values.mean()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created BasicAttention with stable_id 68f95621-d55b-4045-a399-2917305cea9a.\n"]}], "source": ["chatanol_ms_fp16 = llama_model_specs.get_llama_model_spec(\n", "    model_name=\"qwen2_5-coder-1.5b-retriever\",\n", "    checkpoint_path=\"/mnt/efs/augment/checkpoints/chatanol/chatanol-qwen-v1\",\n", "    checkpoint_sha256=\"ee4e3c3896cc94f318a2a023d2902c6843fb1487440f7200b6d233299cc896b2\",\n", ")\n", "step_fn_fp16 = fwd_llama.generate_step_fn(\n", "    chatanol_ms_fp16,\n", "    output_type=fwd.OutputTensorType.EMBEDDING,\n", ")\n", "attn_factory_fp16 = fwd_llama.LlamaAttentionFactory(\n", "    chatanol_ms_fp16,\n", "    attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,\n", ")\n", "attn_fp16 = attn_factory_fp16(32000)\n", "attn_fp16.reset(0)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chatanol_ms_fp8 = llama_model_specs.get_llama_model_spec(\n", "    model_name=\"qwen2_5-coder-1.5b-retriever\",\n", "    checkpoint_path=\"/mnt/efs/augment/checkpoints/chatanol/chatanol-qwen-v1-fp8-smoothquant\",\n", "    checkpoint_sha256=\"36f3369ac41eac22958f938b6fbc2226cabe1d43842196474835b0ef6cd7abef\",\n", ")\n", "step_fn_fp8 = fwd_llama_fp8.generate_step_fn(\n", "    chatanol_ms_fp8,\n", "    output_type=fwd.OutputTensorType.EMBEDDING,\n", ")\n", "attn_factory_fp8 = fwd_llama.LlamaAttentionFactory(\n", "    chatanol_ms_fp8,\n", "    attention_impl=cached_attention.AttentionImpl.MULTI_REQUEST_FLASH,\n", ")\n", "attn_fp8 = attn_factory_fp8(32000)\n", "attn_fp8.reset(0)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}