"""Tool to convert llama2 checkpoints for neox."""

import argparse
import collections
import json
from pathlib import Path

import torch


def main():
    """Main function."""
    p = argparse.ArgumentParser()
    p.add_argument(
        "--srcdir",
        type=str,
        required=True,
        help="Source directory containing llama2 checkpoint",
    )
    p.add_argument(
        "--dstdir",
        type=str,
        required=True,
        help="Directory to write the resulting neox (non-pipeline) checkpoint",
    )
    p.add_argument(
        "--model-parallel-size",
        type=int,
        default=1,
        help="Model parallel degree of source llama2 checkpoint",
    )

    args = p.parse_args()
    srcdir = Path(args.srcdir)
    assert srcdir.exists()
    dstdir = Path(args.dstdir)
    assert dstdir.exists()

    with (srcdir / "params.json").open(encoding="utf-8") as fh:
        config = json.load(fh)
    hidden_dim = config["dim"]
    hidden_per_mp_rank = hidden_dim // args.model_parallel_size
    nheads = config["n_heads"]
    if config.get("n_kv_heads", nheads) != nheads:
        grouped_query_attention = True
        n_kv_heads = config["n_kv_heads"]
    else:
        grouped_query_attention = False
        n_kv_heads = nheads

    # Initialize destination directory
    ckpt_dirname = "checkpoint"
    with (dstdir / "latest").open("w", encoding="utf-8") as fh:
        fh.write(ckpt_dirname)
    cpkt_dir = dstdir / ckpt_dirname
    cpkt_dir.mkdir()

    full_embed = None
    vocab = 0
    for mp_rank in range(args.model_parallel_size):
        srcsd = torch.load(
            (srcdir / f"consolidated.{mp_rank:02d}.pth").as_posix(), map_location="cpu"
        )
        embed = srcsd["tok_embeddings.weight"]
        if full_embed is None:
            vocab = embed.shape[0]
            full_embed = torch.empty(
                (vocab, hidden_dim), dtype=embed.dtype, device=embed.device
            )
        start = hidden_per_mp_rank * mp_rank
        end = start + hidden_per_mp_rank
        full_embed[:, start:end] = embed
    assert vocab > 0 and full_embed is not None
    vocab_per_mp_rank = vocab // args.model_parallel_size

    for mp_rank in range(args.model_parallel_size):
        srcsd = torch.load(
            (srcdir / f"consolidated.{mp_rank:02d}.pth").as_posix(), map_location="cpu"
        )
        result = collections.OrderedDict()
        vocab_start = vocab_per_mp_rank * mp_rank
        vocab_end = vocab_start + vocab_per_mp_rank
        result["embed.word_embeddings.weight"] = full_embed[
            vocab_start:vocab_end, :
        ].clone()
        for i in range(config["n_layers"]):
            srcprefix = f"layers.{i}."
            q, k, v, o = [
                srcsd[srcprefix + f"attention.w{key}.weight"]
                for key in ["q", "k", "v", "o"]
            ]
            dstprefix = f"transformer_layers.{i}."
            if grouped_query_attention:
                result[dstprefix + "attention.query.weight"] = q.clone()
                # See comment below for discussion of K/V interleaving
                chunked_heads = [
                    torch.chunk(x, n_kv_heads // args.model_parallel_size, dim=0)
                    for x in (k, v)
                ]
                kv = torch.cat(
                    [torch.cat(heads, dim=0) for heads in zip(*chunked_heads)], dim=0
                )
                result[dstprefix + "attention.key_value.weight"] = kv.clone()
            else:
                # Q/K/V weights need to get interleaved as such:
                # --Q_0--
                # --K_0--
                # --V_0--
                # --Q_1--
                # ...
                # where Q_i is W_Q for the ith attention head. This code is admittedly subtle.
                # 1) For head of Q/K/V, break them into chunks of size headdim x hidden
                chunked_heads = [
                    torch.chunk(x, nheads // args.model_parallel_size, dim=0)
                    for x in (q, k, v)
                ]
                # 2a) The inner torch.cat generates a list of 3*headdim x hidden chunks stacking Q/K/Vs
                # 2b) Then the outer torch.cat stacks those together into the full QKV weight matrix
                qkv = torch.cat(
                    [torch.cat(heads, dim=0) for heads in zip(*chunked_heads)], dim=0
                )
                result[dstprefix + "attention.query_key_value.weight"] = qkv.clone()
            result[dstprefix + "attention.dense.weight"] = o.clone()

            w1, w2, w3 = [
                srcsd[srcprefix + f"feed_forward.w{key}.weight"]
                for key in ["1", "2", "3"]
            ]
            # Note the ordering on (w3, w1) here:
            # - In llama, the output of the w1 matmul is gated by gelu
            # - In neox, the "right" half of the fused matmul is the gate chunk
            result[dstprefix + "mlp.dense_h_to_4h.weight"] = torch.cat(
                (w3, w1), dim=0
            ).clone()
            result[dstprefix + "mlp.dense_4h_to_h.weight"] = w2.clone()
            result[dstprefix + "input_layernorm.scale"] = srcsd[
                srcprefix + "attention_norm.weight"
            ].clone()
            result[dstprefix + "post_attention_layernorm.scale"] = srcsd[
                srcprefix + "ffn_norm.weight"
            ].clone()

        result["final_norm.scale"] = srcsd["norm.weight"].clone()
        result["final_projection.final_linear.weight"] = srcsd["output.weight"].clone()
        del srcsd

        ds_statedict = {}
        ds_statedict["module"] = result
        ds_statedict["csr_tensor_module_names"] = set()
        ds_statedict[
            "global_steps"
        ] = 500000  # All llama2 models are 2T tokens, batch=4M
        ds_statedict["iteration"] = ds_statedict["global_steps"]
        ds_statedict["skipped_steps"] = 0
        ds_statedict["mp_world_size"] = args.model_parallel_size
        ds_statedict["dp_world_size"] = 1

        torch.save(
            ds_statedict,
            (cpkt_dir / f"mp_rank_{mp_rank:02d}_model_states.pt").as_posix(),
        )
        del result
        del ds_statedict


if __name__ == "__main__":
    main()
