import logging
import queue
import threading
import time
import math

from collections import defaultdict

import numpy as np
import pydantic
import torch
from base.fastforward.all_reduce import AllReduceImpl
from base.fastforward.cached_attention import AttentionImpl
from base.fastforward.llama import fwd_llama, fwd_llama_fp8, model_specs
from base.fastforward.llama.model_specs import SplitHeadModes
from base.fastforward.parallel import ParallelConfig
from base.logging.struct_logging import setup_struct_logging
from services.inference_host.server.continuous_batching import (
    continuous_batching_inference_runner,
)
from services.lib.grpc.auth.service_auth import AuthInfo
from services.lib.request_context.request_context import RequestContext

setup_struct_logging(debug=False)
logging.getLogger().setLevel(logging.WARNING)

# bazel run //experimental/carl/services:loadtest --check_visibility=false


def get_prompt(data, idx):
    im_start_id = 151644
    # im_end_id = 151645
    # tool_start_id = 151657
    # tool_end_id = 151658
    # pad_token_id = 151643

    label_prefix = [151644, 77091, 198]

    def rindex(lst, value):
        for i in range(len(lst) - 1, -1, -1):
            if lst[i] == value:
                return i
        return -1

    MASKED_ZERO_TOKEN = -(2**31)
    tokens = data[idx].tolist()
    unmasked = []
    for token in tokens:
        if token == MASKED_ZERO_TOKEN:
            unmasked.append(0)
        else:
            unmasked.append(abs(token))
    im_start_position = rindex(unmasked, im_start_id)
    assert im_start_position != -1
    assert im_start_position not in unmasked[im_start_position + 1 :]
    assert (
        unmasked[im_start_position : im_start_position + len(label_prefix)]
        == label_prefix
    )
    prompt_tokens = unmasked[: im_start_position + len(label_prefix)]
    return prompt_tokens


def result_consumer(q):
    measured_times = defaultdict(list)
    try:
        while True:
            args = q.get()
            if args is None:
                break
            req, start_time, rps = args
            assert req is not None and start_time is not None
            if rps is not None and rps not in measured_times:
                measured_times[rps] = []
            _ = req()
            time_ms = (time.time() - start_time) * 1000.0
            if rps is not None:
                measured_times[rps].append(time_ms)
            print(f"[{q.qsize()=}] Time: {time_ms:.2f} ms")
    finally:
        # TODO next: have these numbers sent back to the parent proc
        # It can do aggregation + summary stats
        # Can also dynamically adjust the number of requests at each RPS (can send back queue depth? if 0 for a while, you're good)
        print("Summary statistics:")
        # print(measured_times)
        print("RPS,Mean Latency (ms), Median Latency (ms), 90th %ile Latency (ms)")
        for rps in sorted(measured_times.keys()):
            times = measured_times[rps]
            print(
                f"{rps},{np.mean(times):.2f},{np.median(times):.2f},{np.percentile(times, 90):.2f}"
            )


def random_poisson_interval(rate):
    return np.random.exponential(1 / rate)


def main():
    ms = model_specs.get_llama_model_spec(
        model_name="qwen2_5-7b",
        checkpoint_path="/home/<USER>/pleasehold_qwen7b_v4_bsz128_ff_fp8",
        checkpoint_sha256="89408ebb3fb241bc87a4cb6ba0269716f44802f3e29e7a9c4acd15e8f57285a9",
    )
    ms.attn_split_head_mode = SplitHeadModes.NO_SPLIT

    # data = np.load(
    #     "/home/<USER>/augment/experimental/carl/load-testing/router-validation.npy"
    # )

    batch_sizes = [32, 128, 512, 1024, 2048, 4096]
    max_seqlen = 1024 * 32
    parallel_config = ParallelConfig(num_processes=2, tp_size=1, sp_size=2)
    step_fn = fwd_llama_fp8.generate_step_fn(
        ms,
        parallel_config=parallel_config,
        auto_capture_graphs=True,
        batch_sizes=batch_sizes,
        all_reduce_impl=AllReduceImpl.FASTFORWARD,
        small_round_parallel_config=ParallelConfig.from_legacy_config(2, False),
        small_round_token_cutoff=32,
    )
    attn_factory = fwd_llama.LlamaAttentionFactory(
        ms=ms,
        parallel_config=parallel_config,
        attention_impl=AttentionImpl.MULTI_REQUEST_FLASH_V3,
        use_register_tokens_kernel=True,
    )

    model_cfg = continuous_batching_inference_runner.ModelConfig(
        step_fn=step_fn,
        attn_factory=attn_factory,
        round_sizes=batch_sizes,
        do_warmup=True,  # IMPORTANT
    )
    auth_info = AuthInfo(
        tenant_id="test_tenant_id",
        tenant_name="test_tenant",
        shard_namespace="test_namespace",
        cloud="test_cloud",
        user_id=pydantic.SecretStr("test_user"),
    )

    for i in range(parallel_config.num_processes):
        with torch.cuda.device(f"cuda:{i}"):
            torch.cuda.profiler.cudart().cudaProfilerStart()  # type: ignore

    send_q = queue.Queue()
    # recv_q = queue.Queue()
    consumer_thread = threading.Thread(target=result_consumer, args=(send_q,))
    consumer_thread.start()

    # requests_per_second = 1
    # rps_to_measure = [1, 2, 3, 4, 5]
    # rps_to_measure = np.linspace(1., 5., 21).tolist()
    # TODO: we should do the rps measurement automatically based on a step size
    # can get the starting point from the single request time and keep going until we hit a wall
    rps_step_size = 0.2
    vocab_size = 50000
    context_size = 12000
    max_decode_tokens = 6
    num_timing_samples = 50
    # print(f"Measuring {rps_to_measure}")
    with continuous_batching_inference_runner.ContinuousBatchingInferenceRunner(
        model_name=ms.name,
        models=[model_cfg],
        max_seq_length=max_seqlen,
        cache_pool_size=8,
        use_sorted_batching=True,
        default_timeout_sec=15.0,
        use_non_neural_speculation=False,
    ) as runner:

        def _generate_request():
            prompt = np.random.randint(0, vocab_size, (context_size,)).tolist()
            start = time.time()
            return runner.infer(
                request_context=RequestContext.create(),
                prompt=prompt,
                max_decode_tokens=max_decode_tokens,
                auth_info=auth_info,
                end_token_ids=(),
            ), start

        load_free_times = []
        for _ in range(5):
            req, start = _generate_request()
            req()
            load_free_times.append((time.time() - start))
        single_request_time = np.median(load_free_times)
        ##stable_rps = 1 / single_request_time
        ##requests_per_second = math.floor(stable_rps / rps_step_size) * rps_step_size - rps_step_size
        hit_a_wall = False
        print(f"Single request time: {single_request_time * 1000:.2f}ms")
        requests_per_second = 7.0
        while not hit_a_wall:
            requests_per_second += rps_step_size
            while send_q.qsize() > 0:
                time.sleep(0.5)  # Drain the queue
            time_btwn_requests = 1 / requests_per_second
            print(f"Running at {requests_per_second} requests per second")
            prev_time = time.time()
            # has_request_overlap = single_request_time * 1.1 > time_btwn_requests
            try:
                # Need to fill the queue before measuring anything
                num_fill_requests = int(single_request_time / time_btwn_requests)
                done_measuring = False
                for _ in range(num_fill_requests + 1):
                    cur_time = time.time()
                    wait_time = max(0.0, time_btwn_requests - (cur_time - prev_time))
                    time.sleep(wait_time)
                    req, start = _generate_request()
                    send_q.put((req, start, None))
                    prev_time = start

                idx = 0
                starting_q_depth = send_q.qsize()
                while not done_measuring:
                    cur_time = time.time()
                    wait_time = max(0.0, time_btwn_requests - (cur_time - prev_time))
                    time.sleep(wait_time)
                    req, start = _generate_request()
                    send_q.put((req, start, requests_per_second))
                    prev_time = start
                    idx += 1
                    if idx % num_timing_samples == 0:
                        end_q_depth = send_q.qsize()
                        if end_q_depth <= starting_q_depth + 1:
                            done_measuring = True
                            hit_a_wall = True  ## TODO: remove this
                        if idx // num_timing_samples >= 4:
                            print(
                                f"WARNING: queue still growing after {idx} samples. Aborting rps={requests_per_second}."
                            )
                            done_measuring = True
                            hit_a_wall = True
            except KeyboardInterrupt:
                break

        send_q.put(None)
        consumer_thread.join()

        for i in range(parallel_config.num_processes):
            with torch.cuda.device(f"cuda:{i}"):
                torch.cuda.profiler.cudart().cudaProfilerStop()  # type: ignore

        # for requests_per_second in rps_to_measure:
        #     time.sleep(2.0) # drain the queue
        #     time_btwn_requests = 1 / requests_per_second
        #     print(f"Running at {requests_per_second} requests per second")
        #     idx = -1
        #     prev_time = time.time()
        #     try:
        #         for _ in range(50):
        #             # wait_time = random_poisson_interval(requests_per_second)
        #             cur_time = time.time()
        #             wait_time = max(0., time_btwn_requests - (cur_time - prev_time))
        #             if wait_time == 0.:
        #                 print("WARNING: generator thread running behind")
        #             time.sleep(wait_time)
        #             prev_time = time.time()
        #             idx = (idx + 1) % len(data)  # wraparound
        #             # prompt = get_prompt(data, idx)
        #             prompt = np.random.randint(0, 50000, (12000,)).tolist()
        #             start = time.time()
        #             req = runner.infer(
        #                 # Creates a ctx with random request_id and request_session_id
        #                 # (everything else is basically empty)
        #                 request_context=RequestContext.create(),
        #                 prompt=prompt,
        #                 # max_decode_tokens=128,
        #                 max_decode_tokens=6,
        #                 auth_info=auth_info,
        #                 # end_token_ids=[151645],
        #             )
        #             q.put((req, start, len(prompt), requests_per_second))
        #     except KeyboardInterrupt:
        #         break

        # q.put((None, None, None, None))
        # consumer_thread.join()


if __name__ == "__main__":
    main()
