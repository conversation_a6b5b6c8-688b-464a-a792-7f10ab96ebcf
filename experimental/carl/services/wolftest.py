import logging
import os
import torch

from base.logging.struct_logging import setup_struct_logging
from base.fastforward import fwd
from base.fastforward.starcoder import fwd_starcoder
from services.embedder_host.server import (
    embedder_model,
)

setup_struct_logging(debug=False)
logging.getLogger().setLevel(logging.WARNING)


def main():
    os.environ["CUDA_LAUNCH_BLOCKING"] = "1"
    # round_sizes = [256, 512, 1024, 2048, 1024 * 6]  #  - 8]  # , 8192]
    round_sizes = [1024 * 6]
    ms = fwd.ModelSpec(
        name="wolf-location-v17-query",
        checkpoint_path="/mnt/efs/augment/checkpoints/autofix-location/v17_ffw/global_step1935-ckpt_v2/",
        checkpoint_sha256="64656e7c68e6bbb1829f9ece37b8d459cc0ecb00a820ec619eaae63a8dac6982",
        vocab_size=51200,
        num_layers=24,
        num_heads=16,
        emb_dim=2048,
        head_dim=128,
        output_projection_dim=None,
        rotary_pct=0.0,
        rotary_theta=1e4,
        rotary_scaling_factor=1.0,
        max_position_embeddings=8192,
        unscaled_max_position_embeddings=8192,
        norm_eps=1e-5,
    )
    step_fn = fwd_starcoder.generate_step_fn(
        ms,
        # auto_capture_graphs=True,
        auto_capture_graphs=False,
        batch_sizes=round_sizes,
        output_type=fwd.OutputTensorType.EMBEDDING,
    )
    attn_factory = fwd_starcoder.StarcoderAttentionFactory(
        ms,
        pre_attention_kernel_fusion=True,
        use_register_tokens_kernel=True,
    )
    embedder = embedder_model.EmbedderModelFFWD(
        model_name=ms.name,
        step_fn=step_fn,
        attn_factory=attn_factory,
        max_length=max(round_sizes),
        round_sizes=round_sizes,
        num_attention_caches=1,
        do_warmup=True,
    )
    print(embedder)


if __name__ == "__main__":
    main()
