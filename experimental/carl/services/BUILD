load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_binary")

#py_binary(
#    name = "wolftest",
#    srcs = ["wolftest.py"],
#    deps = [
#        "//base/fastforward:fwd",
#        "//base/fastforward/starcoder:fwd_starcoder",
#        "//services/embedder_host/server:embedder_model",
#        "//base/logging:struct_logging",
#        requirement("torch"),
#    ],
#)

#py_binary(
#    name = "loadtest",
#    srcs = ["loadtest.py"],
#    deps = [
#        "//base/fastforward:all_reduce",
#        "//base/fastforward:cached_attention",
#        "//base/fastforward:parallel",
#        "//base/logging:struct_logging",
#        "//base/fastforward/llama:fwd_llama",
#        "//base/fastforward/llama:fwd_llama_fp8",
#        "//base/fastforward/llama:model_specs",
#        "//services/inference_host/server/continuous_batching:continuous_batching_inference_runner",
#        "//services/lib/grpc/auth:service_auth",
#        "//services/lib/request_context:request_context_py",
#        requirement("numpy"),
#        requirement("pydantic"),
#        requirement("torch"),
#    ],
#)
