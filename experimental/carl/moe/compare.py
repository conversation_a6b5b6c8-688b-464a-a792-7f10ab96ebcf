import torch

from experimental.carl.moe.configuration_dbrx import DbrxFFNConfig
from experimental.carl.moe.modeling_dbrx import DbrxFFN

from megablocks.layers.arguments import Arguments
from megablocks.layers.dmoe import dMoE

# Config for the released DBRX model
hidden_size = 6144
config = DbrxFFNConfig(
    ffn_hidden_size=10752,
    moe_num_experts=16,
    moe_top_k=1,
    moe_jitter_eps=0.,
    moe_loss_weight=0.05,
)
my_dtype = torch.float16

ffn = DbrxFFN(hidden_size, config)
# State dict containing only the first MoE FFN layer. Weights are:
# - router.layer.weight
# - experts.mlp.{w1, v1, w2}
sd = torch.load("/mnt/efs/augment/user/carl/dbrx-single-ffn.pt")
ffn.load_state_dict(sd)
ffn.to(device="cuda", dtype=my_dtype)

# Fake input activations
batch = 2
seqlen = 256
x = torch.randn((batch, seqlen, hidden_size), device="cuda", dtype=my_dtype)

with torch.no_grad():
    y, router_logits = ffn(x)
print(y.shape, router_logits.shape)
del ffn

# Try to match the DBRX model config with the `dMoE` layer from megablocks
args = Arguments(
    hidden_size=hidden_size,
    ffn_hidden_size = config.ffn_hidden_size,
    bias=False,
    activation_fn=torch.nn.functional.silu,

    moe_num_experts=config.moe_num_experts,
    moe_top_k=config.moe_top_k,
    moe_normalize_expert_weights=config.moe_normalize_expert_weights,
    moe_loss_weight=config.moe_loss_weight,
    moe_jitter_eps=config.moe_jitter_eps,

    mlp_type="glu",
    mlp_impl="sparse",  # Maybe?
)

moe = dMoE(args)
moe.load_state_dict(sd)
moe.to(device="cuda", dtype=my_dtype)

with torch.no_grad():
    ret = moe(x)
del moe
print(ret.shape)
print((y - ret).abs().max())
