import os
from setuptools import setup
from torch.utils import cpp_extension

sources = ["src/simplenccl.cpp"]
include_dirs = [f"{os.path.dirname(os.path.abspath(__file__))}/include/"]

module = cpp_extension.CUDAExtension(
    name="simplenccl",
    sources=sources,
    include_dirs=include_dirs,
    libraries=["nccl"],
)
setup(
    name="Simple-NCCL",
    version="0.0.1",
    ext_modules=[module],
    cmdclass={'build_ext': cpp_extension.BuildExtension}
)
