#pragma once

#include <pybind11/chrono.h>
#include <torch/python.h>

#include <torch/csrc/distributed/c10d/Backend.hpp>
#include <torch/csrc/distributed/c10d/Store.hpp>
#include <torch/csrc/distributed/c10d/Types.hpp>
#include <torch/csrc/distributed/c10d/Utils.hpp>
#include <torch/csrc/distributed/c10d/Work.hpp>

#include "nccl.h"

using namespace pybind11::literals;

namespace c10d {

class SimpleNCCLBackend : public Backend {
   public:
    SimpleNCCLBackend(const c10::intrusive_ptr<::c10d::Store>& store, int rank, int size);

    c10::intrusive_ptr<Work> allreduce(std::vector<at::Tensor>& tensors,
                                       const AllreduceOptions& opts = AllreduceOptions()) override;

    static c10::intrusive_ptr<Backend> createSimpleNCCLBackend(
        const c10::intrusive_ptr<::c10d::Store>& store, int rank, int size,
        const std::chrono::duration<float>& timeout);

    static void SimpleNCCLBackendConstructor() __attribute__((constructor)) {
        py::object module = py::module::import("torch.distributed");
        py::object register_backend = module.attr("Backend").attr("register_backend");
        register_backend("simplenccl", py::cpp_function(createSimpleNCCLBackend),
                         "devices"_a = "cuda");
    }

   private:
    ncclComm_t ncclComm_;  // should this be an intrusive_ptr?
};

class SimpleNCCLWork : public Work {
   public:
    SimpleNCCLWork(OpType opType, c10::intrusive_ptr<c10::ivalue::Future> future)
        : Work(-1, opType), future_(std::move(future)) {}

    bool isCompleted() override;
    bool isSuccess() const override;
    bool wait(std::chrono::milliseconds timeout = kUnsetTimeout) override;
    virtual c10::intrusive_ptr<c10::ivalue::Future> getFuture() override;

   private:
    c10::intrusive_ptr<c10::ivalue::Future> future_;
};

}  // namespace c10d
