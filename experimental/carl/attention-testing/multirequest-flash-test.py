import flash_attn
import torch


def main():
    cache_seqlen = 4096
    num_caches = 8

    ntokens = 512
    ntokens_smallbuf = ntokens // 8
    nheads = 64
    nheads_kv = 8
    headdim = 128
    my_dtype = torch.float16

    # Hardcoded metadata about request-in-round (here, 3)
    # Actual seqlens:
    seqlens = [ntokens - 37 - 7, 37, 7]
    for s in seqlens[1:]:
        # Check that all requests except the first fit in the "small" q_buf
        assert s <= ntokens_smallbuf
    requests_in_round = len(seqlens)
    # Which cache line does each request go to:
    cache_line_idxs = torch.tensor([0, 5, 2], dtype=torch.int32, device="cuda")
    # How much of each cache line is populated so far:
    per_request_cache_lens = torch.tensor(
        [1024, 1024 * 3, 3333], dtype=torch.int32, device="cuda"
    )

    # How far from the end of q_buf do we need to back up to fill in each
    # request. Assumption is that all reqs except the first use the small q_buf.
    inverse_seqlens = torch.tensor(
        [ntokens - seqlens[0]] + [ntokens_smallbuf - s for s in seqlens[1:]],
        dtype=torch.int32,
        device="cuda",
    )
    cumulative_seqlens = torch.cumsum(
        torch.tensor([0] + seqlens, device="cuda"),
        dim=0,
        dtype=torch.int32,
    )

    # Actual Q/K/V data
    q = torch.randn((ntokens, nheads, headdim), device="cuda", dtype=my_dtype)
    k_cache = torch.randn(
        (num_caches, cache_seqlen, nheads_kv, headdim), device="cuda", dtype=my_dtype
    )
    v_cache = torch.randn(
        (num_caches, cache_seqlen, nheads_kv, headdim), device="cuda", dtype=my_dtype
    )

    # Static input/output buffers for the per-request kernel call
    # Example of a small and large buffer for multiple requests
    q_buf_large = torch.zeros((ntokens, nheads, headdim), device="cuda", dtype=my_dtype)
    q_buf_small = torch.zeros(
        (ntokens_smallbuf, nheads, headdim), device="cuda", dtype=my_dtype
    )
    this_seqlen = torch.zeros((1,), dtype=torch.int32, device="cuda")
    this_cache_line_idx = torch.zeros((1,), dtype=torch.int32, device="cuda")
    result = torch.zeros((ntokens, nheads, headdim), device="cuda", dtype=my_dtype)

    # NOTE: we could do a small custom kernel for all this indexed copying to minimize
    # kernel count and/or workaround pytorch not wanting to do it with an indexed-copy
    for req in range(requests_in_round):
        # First request is the only "large" one
        if req == 0:
            q_buf = q_buf_large
        else:
            q_buf = q_buf_small
        q_buf[inverse_seqlens[req] :, :, :].copy_(
            q[cumulative_seqlens[req] : cumulative_seqlens[req + 1], :, :]
        )
        this_seqlen.copy_(per_request_cache_lens[req])
        this_cache_line_idx.copy_(cache_line_idxs[req])

        ret = flash_attn.flash_attn_with_kvcache(
            q=q_buf.unsqueeze(0),
            k_cache=k_cache,
            v_cache=v_cache,
            cache_seqlens=this_seqlen,
            cache_batch_idx=this_cache_line_idx,
            causal=True,
        )
        result[cumulative_seqlens[req] : cumulative_seqlens[req + 1], :, :].copy_(
            ret[0, inverse_seqlens[req] :, :, :]
        )

    # For comparison: how we are calling it today
    all_cache_idxs = []
    for req in range(requests_in_round):
        all_cache_idxs.extend([cache_line_idxs[req].item()] * seqlens[req])
    cache_idxs = torch.tensor(all_cache_idxs, dtype=torch.int32, device="cuda")
    all_cache_locs = []
    for req in range(requests_in_round):
        start = per_request_cache_lens[req].item() - seqlens[req] + 1
        end = start + seqlens[req]
        all_cache_locs.extend(list(range(start, end)))
    cache_locs = torch.tensor(all_cache_locs, dtype=torch.int32, device="cuda")

    ref = flash_attn.flash_attn_with_kvcache(
        q=q.reshape(ntokens, 1, nheads, headdim),
        k_cache=k_cache,
        v_cache=v_cache,
        cache_seqlens=cache_locs,
        cache_batch_idx=cache_idxs,
    ).squeeze(1)
    print("Max abs diff: ", (result - ref).abs().max().item())


if __name__ == "__main__":
    main()
