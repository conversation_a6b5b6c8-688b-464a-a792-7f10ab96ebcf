import types
import typing

from experimental.carl.agents.simple_grpc_debug_client import SimpleGrpcDebugClient
from services.embedder_host import embedder_pb2
from services.embedder_host.client.client import EmbedderClient
from services.embeddings_search_host import embeddings_search_pb2
from services.embeddings_search_host.client.client import EmbeddingsSearchClient


class FakeStubCycler:
    def __init__(
        self,
        methods: dict[str, typing.Callable],
    ):
        self.proxy = types.SimpleNamespace()
        for name, fn in methods.items():
            setattr(self.proxy, name, fn)

    def get_stub(self):
        return self.proxy


class GrpcEmbedderClient(EmbedderClient):
    QWELDEN_HOST = "embedder-chatanol-qwen-v1-1-svc"

    def __init__(
        self,
        grpc_client: SimpleGrpcDebugClient,
        embedder_host: str,
    ):
        self.grpc_client = grpc_client
        self.embedder_host = embedder_host
        self.stub_cycler = FakeStubCycler(
            {
                "CalculateEmbedding": self.calculate_embedding_proxy,
            }
        )

    def calculate_embedding_proxy(
        self,
        request,
        *args,
        **kwargs,
    ):
        return self.grpc_client.invoke_proto(
            self.embedder_host,
            "embedder.Embedder",
            "CalculateEmbedding",
            request,
            embedder_pb2.EmbeddingResponse,
        )


class GrpcEmbeddingsSearchClient(EmbeddingsSearchClient):
    def __init__(
        self,
        grpc_client: SimpleGrpcDebugClient,
        embeddings_search_host: str = "embeddings-search-cpu-svc",
    ):
        self.grpc_client = grpc_client
        self.embeddings_search_host = embeddings_search_host
        self.stub_cycler = FakeStubCycler(
            {
                "SearchChunks": self.search_chunks_proxy,
            }
        )

    def search_chunks_proxy(
        self,
        request,
        *args,
        **kwargs,
    ):
        return self.grpc_client.invoke_proto_stream(
            self.embeddings_search_host,
            "embeddings_search.EmbeddingsSearch",
            "SearchChunks",
            request,
            embeddings_search_pb2.SearchChunksResponse,
        )
