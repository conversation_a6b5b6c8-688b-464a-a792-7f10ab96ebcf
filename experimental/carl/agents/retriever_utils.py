from dataclasses import dataclass
from pathlib import Path

from base.tokenizers import create_tokenizer_by_name
from base.prompt_format_retrieve import get_retrieval_prompt_formatter_by_name
from experimental.carl.agents import grpc_clients, simple_grpc_debug_client
from services.lib.retrieval.dense_retriever import DenseRetriever


@dataclass
class RetrieverConfig:
    embedder_host: str
    transformation_key: str
    tokenizer_name: str
    prompt_formatter_name: str
    origin: str


QWELDEN_25 = RetrieverConfig(
    embedder_host=grpc_clients.GrpcEmbedderClient.QWELDEN_HOST,
    transformation_key="dr-chatanol-qwen-v1-1-smart-768char",
    tokenizer_name="qwen25coder",
    prompt_formatter_name="chatanol6",
    origin="dense_retriever",
)


def make_grpc_retriever(
    embedder_endpoint: str,
    embeddings_search_endpoint: str,
    api_token_path: str | Path,
    retriever_config: RetrieverConfig,
    max_retrieval_results: int,
) -> DenseRetriever:
    grpc_embedder_client = simple_grpc_debug_client.SimpleGrpcDebugClient(
        embedder_endpoint, Path(api_token_path)
    )
    embedder_client = grpc_clients.GrpcEmbedderClient(
        grpc_embedder_client, retriever_config.embedder_host
    )
    grpc_embeddings_search_client = simple_grpc_debug_client.SimpleGrpcDebugClient(
        embeddings_search_endpoint, Path(api_token_path)
    )
    embeddings_search_client = grpc_clients.GrpcEmbeddingsSearchClient(
        grpc_embeddings_search_client
    )

    tokenizer = create_tokenizer_by_name(retriever_config.tokenizer_name)
    prompt_formatter = get_retrieval_prompt_formatter_by_name(
        retriever_config.prompt_formatter_name, tokenizer
    )
    return DenseRetriever(
        embedder_client=embedder_client,
        embeddings_search_client=embeddings_search_client,
        query_tokenizer=tokenizer,
        origin=retriever_config.origin,
        transformation_key=retriever_config.transformation_key,
        query_prompt_formatter=prompt_formatter,
        num_results=max_retrieval_results,
        search_timeout_ms=1000,
    )
