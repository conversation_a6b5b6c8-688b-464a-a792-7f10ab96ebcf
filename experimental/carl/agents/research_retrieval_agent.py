import json
from fnmatch import fnmatch
from typing import Any

from base.prompt_format_retrieve.prompt_formatter import ChatRetrieverPromptInput
from base.third_party_clients.third_party_model_client import ThirdPartyModelClient
from research.core.types import Chunk
from research.retrieval.types import DocumentIndex
from services.agents.server.codebase_retrieval_agent import (
    CodebaseRetrievalQueryGenerationTool,
    _format_retrieval,
    _generate_tool_use,
    _requests_from_tool_use,
    create_directory_structure,
    format_directory_subtree,
)
from services.chat_host import chat_pb2
from services.lib.retrieval.retriever import RetrievalChunk


def _retrieval_chunk_from_chunk(chunk: Chunk, score: float) -> RetrievalChunk:
    return RetrievalChunk(
        text=chunk.text,
        path=chunk.path,  # type: ignore
        char_start=chunk.char_offset,
        char_end=chunk.char_offset + chunk.length,
        blob_name=chunk.path,
        chunk_index=None,
        origin="dense_retriever",
        score=score,
        header=chunk.header,
        line_start=chunk.line_offset,
        length_in_lines=chunk.length_in_lines,
    )


class ResearchQueryRewritingRetrievalAgent:
    def __init__(
        self, retriever: DocumentIndex, max_chunks: int = 1024, max_chars: int = 20000
    ):
        self.retriever = retriever
        self.max_chunks = max_chunks
        self.max_chars = max_chars
        self.retrieval_tool = CodebaseRetrievalQueryGenerationTool()
        self.retrieval_tool_param = self.retrieval_tool.get_tool_definition()

    def retrieve(
        self,
        query: str,
        path: str = "",
        contains_string: str = "",
    ) -> tuple[list[Chunk], list[float]]:
        chunks, scores = self.retriever.query(query, top_k=None)
        result_chunks, result_scores = [], []
        for chunk, score in zip(chunks, scores):
            if (
                chunk.path is not None
                and path != ""
                and not fnmatch(chunk.path, path)
                and not fnmatch("/" + chunk.path, path)
            ):
                continue
            if contains_string != "" and contains_string not in chunk.text:
                continue
            result_chunks.append(chunk)
            result_scores.append(score)
        return result_chunks[: self.max_chunks], result_scores[: self.max_chunks]

    def run(
        self,
        query: str,
        llm_client: ThirdPartyModelClient,
    ):
        initial_retrievals, initial_scores = self.retrieve(query)
        retriever_queries, used_initial_retrieval = self.get_retriever_queries(
            llm_client,
            query.message,
            [
                _retrieval_chunk_from_chunk(x, y)
                for x, y in zip(initial_retrievals, initial_scores)
            ],
            self.max_chars // 2,
        )
        follow_up_results = []
        follow_up_scores = []
        for retriever_query in retriever_queries:
            retrievals, scores = self.retrieve(
                ChatRetrieverPromptInput(  # type: ignore
                    prefix="",
                    suffix="",
                    path="",
                    message=retriever_query["description"],
                    selected_code="",
                    chat_history=[],
                ),
                retriever_query.get("path", ""),
                retriever_query.get("contains_string", ""),
            )
            follow_up_results.extend(retrievals)
            follow_up_scores.extend(scores)
        all_results = initial_retrievals + follow_up_results
        all_scores = initial_scores + follow_up_scores
        all_pairs = list(zip(all_results, all_scores))

        all_pairs.sort(key=lambda x: x[1], reverse=True)
        return [x for x, _ in all_pairs][: self.max_chunks]

    def get_retriever_queries(
        self,
        llm_client: ThirdPartyModelClient,
        information_request: str,
        initial_retrievals: list[RetrievalChunk],
        max_initial_retrieval_chars: int,
    ) -> tuple[list[dict[str, Any]], list[RetrievalChunk]]:
        """Generate the queries to the retriever."""

        formatted_initial_retrieval, used_initial_retrievals = _format_retrieval(
            initial_retrievals, max_initial_retrieval_chars
        )
        directory_structure = create_directory_structure(initial_retrievals)
        formatted_directory_output, _ = format_directory_subtree(
            directory_structure,
            "",
            max_initial_retrieval_chars // 2,
            max_initial_retrieval_chars // 2,
        )

        message_str = f"""\
The information request is: {information_request}

The directory structure is:
{formatted_directory_output}
Keep in mind that this may not be a full directory subtree, only up to a character limit. Some files or directories might be missing, and the structure might be incomplete.

{formatted_initial_retrieval or ""}

You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
Call the {self.retrieval_tool_param.name} tool to generate queries.
Please pay careful attention to the tool description to optimally use the tool.
Look at the information gathered already, and determine what additional information is missing. Make sure to also look at the directory tree and previous codebase snippets to guide your search.
"""

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        tool_use = _generate_tool_use(
            llm_client,
            message_str,
            self.retrieval_tool_param,
            lambda _: None,
        )

        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )
        return (
            _requests_from_tool_use(tool_use, self.retrieval_tool),
            used_initial_retrievals,
        )
