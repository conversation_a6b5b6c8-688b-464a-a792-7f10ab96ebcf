from base.blob_names.python import blob_names
from base.datasets import gcs_client
from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from experimental.carl.agents import retriever_utils
from experimental.carl.agents.retrieval_agent import QueryRewritingRetrievalAgent
from research.environments import get_eng_secret
from services.agents.server.codebase_retrieval_agent import _retrieve
from services.lib.request_context.request_context import RequestContext


def main():
    retriever = retriever_utils.make_grpc_retriever(
        embedder_endpoint="https://dev-carl.us-central.api.augmentcode.com",
        embeddings_search_endpoint="https://staging-shard-0.api.augmentcode.com",
        api_token_path="/home/<USER>/.augment/token",
        retriever_config=retriever_utils.QWELDEN_25,
        max_retrieval_results=1024,
    )

    agent = QueryRewritingRetrievalAgent(retriever)
    llm_client = AnthropicDirectClient(
        api_key=get_eng_secret("seal-research-anthropic-key"),
        model_name="claude-opus-4-20250514",
        temperature=0,
        max_output_tokens=40000,
    )

    # "sonnet3.5": "claude-3-5-sonnet-20240620",
    # "sonnet3.5-v2": "claude-3-5-sonnet-20241022",
    # "sonnet3.7": "claude-3-7-sonnet-20250219",
    # "sonnet4": "claude-sonnet-4-20250514",
    # "haiku3": "claude-3-haiku-20240307",
    # "haiku3.5": "claude-3-5-haiku-20241022",
    # claude-opus-4-20250514

    request_id = "874b579e-940d-46d0-8cc0-3fdb7355f2e3"  # staging sample
    # request_id = "b4e8878b-ccc0-4c4d-bf3e-5d32adba301a"  # dev-carl sample
    fetcher = gcs_client.GCSRequestInsightFetcher.from_tenant_name("dogfood-shard")
    # fetcher = gcs_client.GCSRequestInsightFetcher.from_tenant_id(
    #     project="system-services-dev",
    #     tenant_id="ef5b04e6d6494538c84ed59228ebeee6",
    #     bucket_name="dev-carl-request-insight-events",
    # )
    request = fetcher.get_request(request_id)
    retrieval_request = gcs_client.group_by_event_name(request.events)[
        "remote_tool_call_request"
    ][0].remote_tool_call_request.codebase_retrieval_request
    ret = agent.run(
        retrieval_request,
        llm_client,
    )
    print(ret)

    # blobs = [blob_names.Blobs.from_proto(proto=b) for b in retrieval_request.blobs]
    # chunks = _retrieve(
    #     retriever=retriever,
    #     request_description=retrieval_request.information_request,
    #     request_path="",
    #     request_contains_string="",
    #     request_context=RequestContext.create(),
    #     auth_info=None,  # type: ignore
    #     blobs=blobs,
    #     max_chunks=1024,
    # )
    # for i, c in enumerate(chunks):
    #     print(f"{i:04d} {c.path} {c.line_start:04d} {c.score:.2f}")


if __name__ == "__main__":
    main()
