import json
import uuid

import requests
from google.protobuf import json_format
from pydantic import SecretStr


class SimpleGrpcDebugClient:
    def __init__(
        self,
        api_proxy_url,
        auth_token_path,
    ):
        self.api_proxy_url = api_proxy_url
        self.auth_token = SecretStr(auth_token_path.read_text("utf8").strip())
        self.session_id = str(uuid.uuid4())
        self.host_cache = {}

    def invoke_proto(self, host, service, method, input_proto, output_proto_type):
        data = json_format.MessageToDict(input_proto)
        response_str = self.invoke_raw(host, service, method, data)
        response = json.loads(response_str)
        assert len(response) == 1, f"Expected single output proto, got {len(response)}"
        return json_format.ParseDict(response[0], output_proto_type())

    def invoke_proto_stream(
        self, host, service, method, input_proto, output_proto_type
    ):
        data = json_format.MessageToDict(input_proto)
        response_str = self.invoke_raw(host, service, method, data)
        ret = [
            json_format.ParseDict(response, output_proto_type())
            for response in json.loads(response_str)
        ]
        return ret

    def invoke_raw(self, host, service, method, data):
        r = requests.post(
            f"{self.api_proxy_url}/grpc-debug/invoke",
            json={
                "endpoint": self.host_url(host),
                "service": service,
                "method": method,
                "data": data,
            },
            headers=self.headers(),
            timeout=30,
        )
        r.raise_for_status()
        if "error" in r.json():
            raise ValueError(f"gRPC method invocation failed: {r.json()['error']}")
        return r.json()["response_json"]

    def headers(self):
        request_id = str(uuid.uuid4())
        return {
            "Authorization": f"Bearer {self.auth_token.get_secret_value()}",
            "X-Request-id": request_id,
            "X-Request-Session-Id": self.session_id,
        }

    def host_url(self, host):
        if host not in self.host_cache:
            r = requests.get(
                f"{self.api_proxy_url}/grpc-debug/endpoints",
                headers=self.headers(),
                timeout=30,
            )
            r.raise_for_status()
            for host_data in r.json():
                self.host_cache[host_data["name"]] = host_data["url"]

        return self.host_cache[host]
