from base.third_party_clients.anthropic_direct_client import AnthropicDirectClient
from research.environments import get_eng_secret
from research.eval.harness.tasks import augment_qa_task
from research.eval.harness.tasks.augment_qa_task import AugmentQATask
from research.eval.harness.tasks.augment_qa_utils import Sam<PERSON>
from research.eval.harness.factories import create_retriever
from research.core.types import Document
from research.eval.harness import utils
from research.eval.harness.systems.abs_system import <PERSON><PERSON>tionR<PERSON>ult
from experimental.carl.agents.research_retrieval_agent import (
    ResearchQueryRewritingRetrievalAgent,
)
from experimental.michiel.research.agentqa.lib.retrieval_formatter import (
    RetrievalsFormatter,
)


def score_formatted_retrievals(
    sample: Sample, formatter_output: str
) -> dict[str, float]:
    n_keywords = float(len(set(sample.keywords)))
    n_matched_keywords = augment_qa_task.count_keywords(
        formatter_output, sample.keywords
    )
    retrievals_keyword_recall = n_matched_keywords / n_keywords

    n_gold_paths = len(sample.gold_paths)
    n_matched_paths = augment_qa_task.count_keywords(
        formatter_output, sample.gold_paths
    )
    gold_paths_recall = n_matched_paths / n_gold_paths

    return {
        "retrievals_keyword_recall": retrievals_keyword_recall,
        "gold_paths_recall": gold_paths_recall,
    }


def compute_metrics(results: list[augment_qa_task.AugmentQAOutput]):
    metrics = {}
    n_samples = len(results)
    if n_samples == 0:
        return metrics
    n_samples = float(n_samples)
    for key in results[0].metrics.keys():
        metrics[key] = sum(r.metrics[key] for r in results) / n_samples
    metrics["samples"] = len(results)
    return metrics


def main():
    task = AugmentQATask.from_yaml_config(
        {
            "dataset_path": "/mnt/efs/augment/data/processed/augment_qa/v3_1",
            "html_report_output_dir": "/home/<USER>/augment-qa/out",
        }
    )
    retriever_config = {
        "scorer": {
            "name": "dense_scorer_v2_ffwd_llama_fp8",
            "checkpoint_path": "/mnt/efs/augment/checkpoints/chatanol/chatanol-qwen-v1-fp8-smoothquant",
            "sha256": "36f3369ac41eac22958f938b6fbc2226cabe1d43842196474835b0ef6cd7abef",
            "model_name": "qwen2_5-coder-1.5b-retriever",
            "tokenizer_name": "qwen25coder",
            "cache_dir": "/home/<USER>/data/retrieval_cache",
        },
        "chunker": {
            "name": "smart_line_level",
            "max_chunk_chars": 768,
            "max_headers": 3,
        },
        "query_formatter": {
            "name": "base:chatanol6",
            "tokenizer_name": "qwen25coder",
            "max_tokens": 4096,
        },
        "document_formatter": {
            "name": "base:chatanol6-embedding-with-path-key",
            "tokenizer_name": "qwen25coder",
            "add_path": True,
            "max_tokens": 1024,
        },
    }
    retriever = create_retriever(retriever_config)
    retriever.load()

    sample = task.samples[0]
    repo_path = task.dataset_path / f"repos/{sample.repo}.jsonl.zst"
    docs = [Document(**d) for d in utils.read_jsonl_zst(repo_path)]
    retriever.add_docs(docs)

    results_as_completion_results = []
    result_scores = []

    for sample in task.samples:
        chunks, _ = retriever.query(sample, top_k=1024)
        formatter = RetrievalsFormatter()
        formatter.add_chunks(chunks, 20_000)
        result = CompletionResult(
            generated_text=formatter.format(),
            prompt_tokens=[],  # unused
            retrieved_chunks=chunks,
        )
        results_as_completion_results.append(result)
        result_scores.append(augment_qa_task.score_response(sample, result))
    full_baseline_metrics = compute_metrics(result_scores)

    llm_client = AnthropicDirectClient(
        api_key=get_eng_secret("seal-research-anthropic-key"),
        model_name="claude-sonnet-4-20250514",
        # model_name="claude-3-5-haiku-20241022",
        temperature=0,
        max_output_tokens=40000,
    )

    results_as_completion_results = []
    result_scores = []

    agent = ResearchQueryRewritingRetrievalAgent(retriever, max_chunks=1024)
    for sample in task.samples:
        formatter = RetrievalsFormatter()
        chunks = agent.run(
            sample,
            llm_client,  # type: ignore
        )
        formatter.add_chunks(chunks, 20_000)
        result = CompletionResult(
            generated_text=formatter.format(),
            prompt_tokens=[],  # unused
            retrieved_chunks=chunks,
        )
        results_as_completion_results.append(result)
        result_scores.append(augment_qa_task.score_response(sample, result))

    print("AGENT")
    full_metrics = compute_metrics(result_scores)
    for k, v in full_metrics.items():
        print(f"{k}: {v}")

    print("BASELINE")
    for k, v in full_baseline_metrics.items():
        print(f"{k}: {v}")


if __name__ == "__main__":
    main()
