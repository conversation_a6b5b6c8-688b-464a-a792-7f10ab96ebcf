# This file copies a lot of logic from services/agents/server/codebase_retrieval_agent.py
# so that we can experiment with it out-of-band.
import json
from textwrap import dedent
from typing import Any

from base.blob_names.python import blob_names
from base.prompt_format_chat.prompt_formatter import Exchange
from base.third_party_clients.third_party_model_client import ThirdPartyModelClient
from services.agents.agents_pb2 import CodebaseRetrievalRequest
from services.agents.server.codebase_retrieval_agent import (
    CodebaseRetrievalQueryGenerationTool,
    _format_retrieval,
    _extract_and_interleave_chunks,
    _generate_tool_use,
    _requests_from_tool_use,
    _retrieve,
    create_directory_structure,
    format_dialog_as_string,
    format_directory_subtree,
)
from services.chat_host import chat_pb2
from services.chat_host.chat_proto_util import convert_exchange
from services.lib.request_context.request_context import RequestContext
from services.lib.retrieval.retriever import RetrievalChunk, Retriever


class QueryRewritingRetrievalAgent:
    # Based on CodebaseQueryRewritingRetrievalAgent
    def __init__(
        self, retriever: Retriever, max_chunks: int = 1024, max_chars: int = 20000
    ):
        self.retriever = retriever
        self.max_chunks = max_chunks
        self.max_chars = max_chars
        self.retrieval_tool = CodebaseRetrievalQueryGenerationTool()
        self.retrieval_tool_param = self.retrieval_tool.get_tool_definition()

    def retrieve(
        self,
        blobs: list[blob_names.Blobs],
        request_description: str,
        request_path: str = "",
        request_contains_string: str = "",
    ) -> list[RetrievalChunk]:
        return _retrieve(
            retriever=self.retriever,
            request_description=request_description,
            request_path=request_path,
            request_contains_string=request_contains_string,
            request_context=RequestContext.create(),
            auth_info=None,  # type: ignore
            blobs=blobs,
            max_chunks=self.max_chunks,
        )

    def run(
        self,
        retrieval_request: CodebaseRetrievalRequest,
        llm_client: ThirdPartyModelClient,
    ):
        information_request = retrieval_request.information_request
        blobs = [blob_names.Blobs.from_proto(proto=b) for b in retrieval_request.blobs]
        dialog_messages = [
            convert_exchange(exchange) for exchange in retrieval_request.dialog
        ]
        initial_retrieval = self.retrieve(
            blobs,
            information_request,
        )
        retriever_queries, exchange, used_initial_retrieval = (
            self.get_retriever_queries(
                llm_client,
                information_request,
                dialog_messages,
                initial_retrieval,
                self.max_chars // 2,
            )
        )
        query_result_list = []
        print("GOT RETRIEVER QUERIES")
        for retriever_query in retriever_queries:
            print(retriever_query)
            chunks = self.retrieve(
                blobs,
                retriever_query["description"],
                retriever_query.get("path", ""),
                retriever_query.get("contains_string", ""),
            )
            query_result_list.append((retriever_query, chunks))

        combined_chunks = used_initial_retrieval + _extract_and_interleave_chunks(
            query_result_list
        )
        tool_output_str, _ = _format_retrieval(combined_chunks, self.max_chars)
        return tool_output_str

    def get_retriever_queries(
        self,
        llm_client: ThirdPartyModelClient,
        information_request: str,
        dialog_messages: list[Exchange],
        initial_retrievals: list[RetrievalChunk],
        max_initial_retrieval_chars: int,
    ) -> tuple[list[dict[str, Any]], chat_pb2.Exchange, list[RetrievalChunk]]:
        """Generate the queries to the retriever."""

        MAX_DIALOG_CHARS = 30000

        formatted_initial_retrieval, used_initial_retrievals = _format_retrieval(
            initial_retrievals, max_initial_retrieval_chars
        )
        directory_structure = create_directory_structure(initial_retrievals)
        formatted_directory_output, _ = format_directory_subtree(
            directory_structure,
            "",
            max_initial_retrieval_chars // 2,
            max_initial_retrieval_chars // 2,
        )

        message_str = f"""\
The information request is: {information_request}

The directory structure is:
{formatted_directory_output}
Keep in mind that this may not be a full directory subtree, only up to a character limit. Some files or directories might be missing, and the structure might be incomplete.

{formatted_initial_retrieval or ""}

You are a tool-calling agent that generates queries for a dense retrieval tool to gather information from a codebase to help answer a request for information.
Call the {self.retrieval_tool_param.name} tool to generate queries.
Please pay careful attention to the tool description to optimally use the tool.
Look at the information gathered already, and determine what additional information is missing. Make sure to also look at the directory tree and previous codebase snippets to guide your search.
"""

        DIALOG_PROMPT = dedent(
            """\
            The information request is in the context of an agent attempting to execute a task.
            Below are some messages of the dialogue of this agent and the user:
            """
        )

        if dialog_messages is not None:
            message_str += DIALOG_PROMPT
            message_str += format_dialog_as_string(dialog_messages, MAX_DIALOG_CHARS)

        message_str += (
            f"Once again, the information request is: {information_request}\n"
        )

        tool_use = _generate_tool_use(
            llm_client,
            message_str,
            self.retrieval_tool_param,
            lambda _: None,
        )

        request_node = chat_pb2.ChatRequestNode(
            type=chat_pb2.ChatRequestNodeType.TEXT,
            text_node=chat_pb2.ChatRequestText(
                content="Intermediate retrieval query generation."
            ),
        )
        resp_nodes = []
        if tool_use is not None:
            resp_nodes.append(
                chat_pb2.ChatResultNode(
                    type=chat_pb2.ChatResultNodeType.TOOL_USE,
                    tool_use=chat_pb2.ChatResultToolUse(
                        tool_use_id=tool_use.tool_use_id,
                        tool_name=tool_use.tool_name,
                        input_json=json.dumps(tool_use.input),
                    ),
                )
            )
        exchange = chat_pb2.Exchange(
            request_nodes=[request_node],
            response_nodes=resp_nodes,
        )
        return (
            _requests_from_tool_use(tool_use, self.retrieval_tool),
            exchange,
            used_initial_retrievals,
        )
