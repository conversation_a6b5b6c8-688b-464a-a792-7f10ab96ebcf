# Pretrain a 830M parameter (including 150M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/830M-model-def.yml

determined:
  name: 830M-llama-80Btoken-pretrain-seqlen4096-4node-batch512K  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["830M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 32
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 830M-llama-80Btoken-pretrain-seqlen4096-4node-batch512K  # pragma: allowlist secret
  wandb_project: pretrain

  # 512k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 4
  gradient_accumulation_steps: 1
  train_batch_size: 128

  # 160k steps @ 512k tokens ~ 80B tokens
  lr_decay_iters: 162000
  train_iters: 162000
  warmup: 0.012345679012345678  # 2k iters

  save_interval: 5000  # Don't save too often
