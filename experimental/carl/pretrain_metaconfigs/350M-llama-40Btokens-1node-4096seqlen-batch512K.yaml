# Pretrain a 350M parameter (including 100M word embedding) model to the same quality
# as a chinchilla optimal model of twice the size.

includes:
- augment_configs/pretrain/datasets/starcoder.yml
- augment_configs/pretrain/arch/llama/arch-common.yml
- augment_configs/pretrain/arch/conan/gptj-runtime-common.yml
- augment_configs/pretrain/arch/conan/sizes/350M-model-def.yml

determined:
  name: 350M-llama-40Btoken-pretrain-seqlen4096-1node-batch512K  # pragma: allowlist secret
  description: null
  workspace: Dev
  project: pretrain
  labels: ["350M", "pretrain"]
  max_restarts: 0
  perform_initial_validation: True

augment:
  podspec_path: "8xA100.yaml"
  gpu_count: 8
  save_trial_best: 0

  checkpoint_handling:
    action: "gc"
  entrypoint: "/run/determined/workdir/research/gpt-neox/jobs/determined.sh"

overrides:
  wandb_name: 350M-llama-40Btoken-pretrain-seqlen4096-1node-batch512K  # pragma: allowlist secret
  wandb_project: pretrain

  # 512k tokens per batch (seqlen 4096)
  train_micro_batch_size_per_gpu: 16
  gradient_accumulation_steps: 1
  train_batch_size: 128

  # 80k steps @ 512k tokens ~ 40B tokens
  lr_decay_iters: 82000
  train_iters: 82000
  warmup: 0.024390243902439025  # 2k iters

  save_interval: 5000  # Don't save too often
