"""Simple FLOPs/memory/communication model for GPT."""
import argparse
from contextlib import contextmanager


class Context:
    """Helper class for tracking ops during "forward" of the perfmodel."""

    def __init__(self, fp_width=2, int_width=8):
        self.fp_width = fp_width
        self.int_width = int_width
        self.name_parts = []
        self.ops = []

    def cur_name(self):
        """Full-path name of the current context."""
        return "/".join(self.name_parts)

    @contextmanager
    def name(self, n):
        """Context manager to scope a new sub-name when adding an Op."""
        self.name_parts.append(n)
        yield
        self.name_parts.pop()

    def append(self, *args, **kwargs):
        """Insert `Op(*args, **kwargs)` into the list of ops."""
        self.ops.append(Op(self, *args, **kwargs))


class TransfomerArgs:
    """Simple struct to carry around the model configuration (a la neox-args)."""

    def __init__(
        self,
        batch,
        seqlen,
        hidden_size,
        vocab_size,
        num_heads,
        num_layers,
        rotary_pct=0.8,
        model_parallel=1,
    ):
        self.batch = batch
        self.seqlen = seqlen
        self.hidden_size = hidden_size
        self.vocab_size = vocab_size
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.head_dim = hidden_size // num_heads
        self.rotary_pct = rotary_pct
        self.model_parallel = model_parallel


class Op:
    """Class to hold onto the FLOPs/memory/communication attributes of an op.

    - fwd_flops: FLOP count for fprop of this op
    - fwd_mem: number of _values_ read/written during fprop (NOTE: _not_ bytes)
    - bwd_factor: the factor by which flops/memory are scaled for backprop
    - {fwd, bwd}_comm: a tuple of the form (op_name, bytes) for any communication
    - nparams: number of learnable parameters associated with this op
    - value_type: indicates how to interpret the size of values in `fwd_mem`
    """

    def __init__(
        self,
        ctx,
        fwd_flops=0,
        fwd_mem=0,
        bwd_factor=1,
        fwd_comm=None,
        bwd_comm=None,
        nparams=0,
        value_type="fp",
    ):
        assert value_type in ["fp", "int", "raw"]
        if value_type == "fp":
            value_width = ctx.fp_width
        elif value_type == "int":
            value_width = ctx.int_width
        elif value_type == "raw":
            value_width = 1
        self.name = ctx.cur_name()
        self.fwd_flops = fwd_flops
        self.fwd_mem = fwd_mem * value_width
        self.bwd_flops = self.fwd_flops * bwd_factor
        self.bwd_mem = self.fwd_mem * bwd_factor
        self.fwd_comm = fwd_comm
        self.bwd_comm = bwd_comm
        self.nparams = nparams


def batch_matmul(ctx, batch, m, n, k, name="bmm"):
    """Batch matmul of `batch` products, each (m x k) * (k * n)."""
    with ctx.name(name):
        ctx.append(
            fwd_flops=batch * m * n * k * 2,
            fwd_mem=batch * (m * k + k * n + m * n),
            bwd_factor=2,
        )


def embed(ctx, num_indices, embed_dim, vocab_size, name="embed"):
    """Lookup of `num_indices` embeddings, each of size `embed_dim`."""
    with ctx.name(name):
        ctx.append(
            fwd_mem=ctx.int_width * num_indices
            + ctx.fp_width * num_indices * embed_dim * 2,
            value_type="raw",
            nparams=embed_dim * vocab_size,
        )


def fused_causal_softmax(ctx, args, name="fused-causal-softmax"):
    """Fused implementation of causal softmax inside the attention layer."""
    with ctx.name(name):
        ctx.append(
            # NB: causal mask means you read+write only half the tensor, so there is no factor of 2
            fwd_mem=args.batch
            * args.num_heads
            * args.seqlen
            * args.seqlen
        )


def layernorm(ctx, batch, hidden_size, name="layernorm"):
    """Layernorm on a _total_ batch size of `batch`."""
    with ctx.name(name):
        ctx.append(
            fwd_mem=batch * hidden_size * 2,
            bwd_factor=2,  # TODO: not quite right
            nparams=2 * hidden_size,
        )


def linear(ctx, batch, hidden_in, hidden_out, name="linear"):
    """Linear layer (matmul+bias) on a _total_ batch of `batch`."""
    with ctx.name(name):
        m, n, k = batch, hidden_out, hidden_in
        ctx.append(
            fwd_flops=2 * m * n * k,
            fwd_mem=m * k + k * n + m * n,
            bwd_factor=2,
            nparams=hidden_in * hidden_out + hidden_out,
        )


def rotary_embed(ctx, args, name="rotary-embed"):
    """Rotary embedding inside transformer attention."""
    # SOL is to read rotary_pct of each of K/Q matrices, pointwise multiply with the corresponding
    # cos/sin values (which are small, since independent of batch/num_heads) and write the result.
    # In practice, PyTorch generates quite a lot more memory traffic for rearranging and
    # concatenating.
    with ctx.name(name):
        ctx.append(
            fwd_mem=int(
                args.batch
                * args.seqlen
                * args.num_heads
                * args.head_dim
                * args.rotary_pct
                * 2
            )
        )


def mp_replicate(ctx, num_bytes, mp_factor, name="mp-replicate"):
    """Replicate `num_bytes` to multiple consumers (eg: both self-attention and mlp)."""
    with ctx.name(name):
        ctx.append(bwd_comm=("allreduce", num_bytes, mp_factor))


def mp_reduce(ctx, num_bytes, mp_factor, name="mp-reduce"):
    """Allreduce `num_bytes` from multiple producers (eg: after model-parallel block)."""
    with ctx.name(name):
        ctx.append(fwd_comm=("allreduce", num_bytes, mp_factor))


def transformer_attention(ctx, args, name="attention"):
    """Attention portion of the transformer block.

    Elements of the layer are:
    - Input layernorm
    - KQV matmul
    - Rotary embedding
    - Attention scores matmul (KQ^T)
    - Softmax on attention scores
    - Multiply softmax output with values
    - Final dense matmul
    """
    with ctx.name(name):
        layernorm(ctx, args.batch * args.seqlen, args.hidden_size)
        linear(
            ctx,
            args.batch * args.seqlen,
            args.hidden_size,
            args.hidden_size * 3 // args.model_parallel,
            "qkv-linear",
        )
        rotary_embed(ctx, args)
        bmm_batch = args.batch * args.num_heads // args.model_parallel
        batch_matmul(
            ctx, bmm_batch, args.seqlen, args.seqlen, args.head_dim, "scores-bmm"
        )
        fused_causal_softmax(ctx, args)
        batch_matmul(
            ctx, bmm_batch, args.head_dim, args.seqlen, args.seqlen, "values-bmm"
        )
        linear(
            ctx,
            args.batch * args.seqlen,
            args.hidden_size // args.model_parallel,
            args.hidden_size,
            "densify",
        )


def transformer_mlp(ctx, args, name="mlp"):
    """MLP portion of the transformer block."""
    with ctx.name(name):
        shared_hidden_size = args.hidden_size * 4 // args.model_parallel
        linear(
            ctx,
            args.batch * args.seqlen,
            args.hidden_size,
            shared_hidden_size,
            "h-to-4h",
        )
        linear(
            ctx,
            args.batch * args.seqlen,
            shared_hidden_size,
            args.hidden_size,
            "4h-to-h",
        )


def transformer_block(ctx, args, name="block"):
    """Full transformer block.

    Note this assumes "GPT-J-style" residuals in which activations go to attention/mlp in parallel.
    """
    with ctx.name(name):
        activation_bytes = args.batch * args.seqlen * args.hidden_size * ctx.fp_width
        mp_replicate(ctx, activation_bytes, args.model_parallel)
        transformer_attention(ctx, args)
        mp_replicate(ctx, activation_bytes, args.model_parallel)
        transformer_mlp(ctx, args)
        mp_reduce(ctx, activation_bytes, args.model_parallel)


def gpt(ctx, args, name="gpt"):
    """Full GPT model: embed; stack of transformer blocks; final layernorm and linear."""
    with ctx.name(name):
        activation_bytes = args.batch * args.seqlen * args.hidden_size * ctx.fp_width
        embed(
            ctx,
            args.batch * args.seqlen,
            args.hidden_size,
            args.vocab_size // args.model_parallel,
        )
        mp_reduce(ctx, activation_bytes, args.model_parallel, "mp-reduce-embed")
        for i in range(args.num_layers):
            transformer_block(ctx, args, f"block-{i}")
        layernorm(ctx, args.batch * args.seqlen, args.hidden_size)
        mp_replicate(ctx, activation_bytes, args.model_parallel)
        linear(
            ctx,
            args.batch * args.seqlen,
            args.hidden_size,
            args.vocab_size // args.model_parallel,
            "final-linear",
        )


def analytic_flop_count(args):
    """Analystic count of model FLOPs a la the PaLM paper. Ignores non-matmul/bmm ops."""
    forward_backward = (
        3  # Each fprop matmul generates 2x bprop matmuls (grad wrt both inputs)
    )
    # Per-layer QKV, densify, MLP
    qkv = 2 * args.hidden_size * args.hidden_size * 3
    densify = 2 * args.hidden_size * args.hidden_size
    mlp = 2 * 2 * (args.hidden_size * args.hidden_size * 4)
    linear_flops = args.num_layers * forward_backward * (qkv + densify + mlp)
    # final linear
    linear_flops += 2 * forward_backward * args.hidden_size * args.vocab_size
    # per-layer batch matmuls
    single_bmm_flops = (
        2 * args.num_heads * args.head_dim * args.seqlen
    )  # Single token self-attn
    per_layer_bmm_flops = 2 * single_bmm_flops  # KQ^T and scores * values
    total_bmm_flops = args.num_layers * forward_backward * per_layer_bmm_flops
    flops_per_token = linear_flops + total_bmm_flops
    return flops_per_token * args.batch * args.seqlen


def main():
    """Sample command-line use of the GPT perfmodel."""
    p = argparse.ArgumentParser()
    # Default values are batch=1, 2B codegen model
    # Some other model configs:
    # - 6B: hidden_size=4096, vocab_size=51200, num_heads=16, num_layers=33
    # - 16B: hidden_size=6144, vocab_size=51200, num_heads=24, num_layers=34
    # - 65B (llama): hidden_size=8192, vocab_size=51200, num_heads=64, num_layers=80
    p.add_argument("--batch", type=int, default=1)
    p.add_argument("--seqlen", type=int, default=2048)
    p.add_argument("--hidden-size", type=int, default=2560)
    p.add_argument("--vocab-size", type=int, default=51200)
    p.add_argument("--num-heads", type=int, default=32)
    p.add_argument("--num-layers", type=int, default=32)
    p.add_argument("--rotary-pct", type=float, default=0.8)
    p.add_argument("--model-parallel", type=int, default=1)

    args = TransfomerArgs(**vars(p.parse_args()))
    ctx = Context()
    gpt(ctx, args)

    fwd_flops = sum(op.fwd_flops for op in ctx.ops)
    bwd_flops = sum(op.bwd_flops for op in ctx.ops)
    nparams = sum(op.nparams for op in ctx.ops)

    print("Params:", nparams)
    print("Fwd FLOPs:", fwd_flops)
    print("Bwd FLOPs:", bwd_flops)
    print("Total:", (fwd_flops + bwd_flops) / 1000000000000.0, "trillion")
    print("Palm flops:", analytic_flop_count(args) / 1000000000000.0, "trillion")
    print("Flops per token", (fwd_flops + bwd_flops) / (args.batch * args.seqlen))


if __name__ == "__main__":
    main()
