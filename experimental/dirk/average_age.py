from collections import defaultdict
import os
import subprocess
import argparse
from datetime import datetime, timezone
from dateutil.parser import parse


def get_file_commit_date(filepath, base_dir):
    """Get the last commit date of a file."""
    try:
        # Get the last commit date for the file in ISO format
        result = subprocess.check_output(
            ["git", "log", "-1", "--format=%cI", filepath], stderr=subprocess.STDOUT,
            cwd=base_dir
        )
        commit_date = result.decode().strip()
        return parse(commit_date)
    except subprocess.CalledProcessError:
        print(f"Error getting commit date for {filepath}")
        return None


def get_all_files_in_repo(repo_dir):
    """Retrieve all tracked files in a Git repository."""
    try:
        result = subprocess.check_output(["git", "ls-files"], cwd=repo_dir)
        file_list = result.decode().splitlines()
        return [os.path.join(repo_dir, f) for f in file_list]
    except subprocess.CalledProcessError:
        print("Error listing files in the repository.")
        return []


def calculate_average_file_age(repo_dir):
    """Calculate the average age of files in the repository."""
    all_files = get_all_files_in_repo(repo_dir)
    if not all_files:
        print("No files found in the repository.")
        return

    total_age = 0
    file_count = 0
    today = datetime.now(timezone.utc)
    ages = defaultdict(int)

    for file_path in all_files:
        commit_date = get_file_commit_date(file_path, repo_dir)
        if commit_date:
            file_age = (today - commit_date).days
            print(f"{file_path}: {file_age} days")
            total_age += file_age
            file_count += 1
            ages[file_age] += 1

    running_count = 0
    for age in sorted(ages.keys()):
        count = ages[age]
        running_count += count
        print(f"{age}: {count} ({running_count}/{file_count})")


    if file_count == 0:
        print("No valid commit dates found.")
    else:
        average_age = total_age / file_count
        print(f"Average age of files: {average_age:.2f} days")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calculate average file age in a Git repository.")
    parser.add_argument("repo_dir", help="Path to the Git repository directory.")
    args = parser.parse_args()
    repo_dir = args.repo_dir
    if os.path.isdir(repo_dir) and os.path.isdir(os.path.join(repo_dir, ".git")):
        calculate_average_file_age(repo_dir)
    else:
        print("Invalid Git repository directory.")
