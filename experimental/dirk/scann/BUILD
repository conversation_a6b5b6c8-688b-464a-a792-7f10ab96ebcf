load("//tools/bzl:python.bzl", "py_binary")

py_binary(
    name = "extract_embeddings",
    srcs = [
        "extract_embeddings.py",
    ],
    deps = [
        "//base/logging:console_logging",
        "//services/content_manager/client",
        "//services/lib/grpc:grpc_args_parser",
        "//services/lib/grpc:token_parser",
        "//services/lib/request_context:request_context_py",
        "//services/request_insight/central/client:request_insight_central_client_py",
    ],
)
