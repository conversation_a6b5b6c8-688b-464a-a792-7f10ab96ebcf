"""CLI Utility to update the content manager information."""

import argparse
import binascii
import json
import logging
import pathlib
import sys

import grpc
import pydantic

from base.logging.console_logging import setup_console_logging
from services.content_manager.client import content_manager_client
from services.lib.grpc import grpc_args_parser, token_parser
from services.lib.request_context.request_context import RequestContext
from services.request_insight import request_insight_pb2
from services.request_insight.central import request_insight_central_pb2
from services.request_insight.central.client.client import RequestInsightCentralClient


def _get_request_context(token: pydantic.SecretStr):
    return RequestContext.create(auth_token=token)


def _extract_embeddings(
    content_manager_rpc_client: content_manager_client.ContentManagerClient,
    request_insight_client: RequestInsightCentralClient,
    args,
    token: pydantic.SecretStr,
):
    output_dir = args.output_dir
    output_dir.mkdir(parents=True, exist_ok=True)
    request_events: list[request_insight_pb2.RequestEvent] = list(
        response.request_event
        for response in request_insight_client.get_request_events(
            request=request_insight_central_pb2.GetRequestEventsRequest(
                tenant_id=args.tenant_id,
                request_id=args.request_id,
            ),
            request_context=_get_request_context(token),
        )
    )
    blob_lists = []
    for event in request_events:
        if event.HasField("embeddings_search_request"):
            print(event.embeddings_search_request)
            for blobs in event.embeddings_search_request.blobs:
                current_blobs = []
                resolved_blobs = (
                    content_manager_rpc_client.get_all_blobs_from_checkpoint(
                        blobs.baseline_checkpoint_id, _get_request_context(token)
                    )
                )
                assert resolved_blobs
                for blob in resolved_blobs:
                    current_blobs.append(binascii.hexlify(blob).decode("ascii"))
                for blob in blobs.added:
                    current_blobs.append(binascii.hexlify(blob).decode("ascii"))
                for blob in blobs.deleted:
                    current_blobs.remove(binascii.hexlify(blob).decode("ascii"))
                blob_lists.extend(current_blobs)
    for blob_name in blob_lists:
        print(blob_name)
        blob_path = output_dir / f"{blob_name}.npy"
        if not blob_path.exists():
            try:
                content, _ = content_manager_rpc_client.download_all(
                    blob_name,
                    _get_request_context(token),
                    transformation_key=args.transformation_key,
                    sub_key="embeddings.npy",
                )
                print(blob_name)
                with blob_path.open("wb") as f:
                    f.write(content)
            except grpc.RpcError as ex:
                if ex.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
                    print(f"Not found: {blob_name}")
                else:
                    raise ex

        metadata_path = output_dir / f"{blob_name}.json"
        if not metadata_path.exists():
            try:
                _, metadata = content_manager_rpc_client.download_all(
                    blob_name,
                    _get_request_context(token),
                    transformation_key="",
                    sub_key="",
                )
                metadata_path.write_text(json.dumps(metadata))
            except grpc.RpcError as ex:
                if ex.code() == grpc.StatusCode.NOT_FOUND:  # type: ignore
                    print(f"Not found: {blob_name}")
                else:
                    raise ex


def main():
    """Main function."""
    setup_console_logging(add_timestamp=False)
    parser = argparse.ArgumentParser()
    grpc_args_parser.add_endpoint_args(parser)
    token_parser.add_token_args(parser)

    parser.add_argument("--central-namespace", default="central")
    parser.add_argument("--tenant-id", required=True)
    parser.add_argument("--request-id", required=True)
    parser.add_argument("--output-dir", required=True, type=pathlib.Path)
    parser.add_argument(
        "--transformation-key",
        default="dr-starethanol6-16-1-proj512-30line-1024char-v3",
    )

    parser.set_defaults(action=None)

    args = parser.parse_args()
    try:
        with grpc_args_parser.create_client(
            args,
            content_manager_client.ContentManagerClient.create_for_endpoint,
            default_service_name="content-manager-svc",
            default_endpoint="content-manager-svc:50051",
        ) as content_manager_rpc_client:
            central_args = argparse.Namespace(**vars(args))
            central_args.namespace = args.central_namespace
            with grpc_args_parser.create_client(
                central_args,
                RequestInsightCentralClient.create_for_endpoint,
                default_service_name="request-insight-central-svc",
                default_endpoint=f"request-insight-central-svc.{args.central_namespace}:50051",
                secret_namespace=args.namespace,
                default_certficate_name="support-central-certificate",
                local_port=50052,
            ) as request_insight_client:
                token = token_parser.get_token(args)
                _extract_embeddings(
                    content_manager_rpc_client=content_manager_rpc_client,
                    request_insight_client=request_insight_client,
                    args=args,
                    token=token,
                )
    except KeyboardInterrupt:
        sys.exit(1)
    except argparse.ArgumentError as e:
        logging.error("%s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
