{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import scann\n", "import time\n", "import numpy\n", "import pathlib\n", "import typing\n", "import json\n", "import sys\n", "from collections import defaultdict\n", "from dataclasses import dataclass, field"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class Embedding:\n", "    path: pathlib.Path\n", "    data: numpy.ndarray"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_embeddings(path):\n", "    e = []\n", "    for file in path.iterdir():\n", "        try:\n", "            if file.suffix == \".npy\":\n", "                n = numpy.load(file)\n", "                print(f\"{file}: {n.shape}\")\n", "                path = json.loads(file.with_suffix(\".json\").read_text())[\"path\"]\n", "                e.append(Embedding(pathlib.Path(path), n))\n", "        except FileNotFoundError:\n", "            pass\n", "    print(\"file count: \", len(e))\n", "    return e"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["e = load_embeddings(pathlib.Path.home() / \"979ce985-3d15-4ef6-a725-9eedcccc5c7a\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class DirectoryEmbeddings:\n", "    path: pathlib.Path\n", "    embeddings: list[Embedding]\n", "    indirect_embeddings: list[Embedding]\n", "    seacher: typing.Any | None = None\n", "    children: list[\"DirectoryEmbeddings\"] = field(default_factory=list)\n", "\n", "    def chunk_count(self):\n", "        return sum(e.data.shape[0] for e in self.embeddings) + sum(\n", "            e.data.shape[0] for e in self.indirect_embeddings\n", "        )\n", "\n", "    def build_searcher(self):\n", "        if self.seacher is None:\n", "            e = numpy.concatenate([e.data for e in self.embeddings])\n", "            self.seacher = (\n", "                scann.scann_ops_pybind.builder(e, 1024, \"dot_product\")\n", "                .score_ah(8, anisotropic_quantization_threshold=0.2)\n", "                .build()\n", "            )\n", "        return self.seacher\n", "\n", "    def visit(self, f):\n", "        children_values = []\n", "        for c in self.children:\n", "            v = c.visit(f)\n", "            children_values.append((c, v))\n", "        return f(self, children_values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def combine_to_directory(embeddings: list[Embedding]):\n", "    result = {}\n", "    for e in sorted(embeddings, key=lambda x: x.path):\n", "        p = e.path.parent\n", "        if p not in result:\n", "            result[p] = DirectoryEmbeddings(p, [], [])\n", "            last_p = p\n", "            pp = p.parent\n", "            while last_p != pp:\n", "                if pp not in result:\n", "                    result[pp] = DirectoryEmbeddings(pp, [], [])\n", "                if result[last_p] not in result[pp].children:\n", "                    result[pp].children.append(result[last_p])\n", "                last_p = pp\n", "                pp = pp.parent\n", "        result[p].embeddings.append(e)\n", "\n", "    print(\"Directory count: \", len(result))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["d = combine_to_directory(e)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_tree(d: DirectoryEmbeddings, indent: int = 0, f=sys.stdout):\n", "    print(\"  \" * indent, d.path, d.chunk_count(), len(d.embeddings), file=f)\n", "    for c in sorted(d.children, key=lambda x: x.path.name.lower()):\n", "        print_tree(c, indent + 1, f=f)\n", "\n", "\n", "print_tree(d[pathlib.Path(\"\")], f=open(\"/tmp/tree.txt\", \"w\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searchers = []\n", "\n", "\n", "def v(d: DirectoryEmbeddings, children_values: list[tuple[DirectoryEmbeddings, int]]):\n", "    global searchers\n", "    chunks = []\n", "    for _, v in children_values:\n", "        chunks.extend(v)\n", "    chunks += [e.data for e in d.embeddings]\n", "    chunk_count = sum(chunk.shape[0] for chunk in chunks)\n", "    if chunk_count > 1000 or d.path == pathlib.Path():\n", "        e = numpy.concatenate(chunks)\n", "        searcher = (\n", "            scann.scann_ops_pybind.builder(e, 32, \"dot_product\")\n", "            .score_ah(8, anisotropic_quantization_threshold=0.2)\n", "            .reorder(max(32, chunk_count // 20))\n", "            .build()\n", "        )\n", "        searchers.append(searcher)\n", "        print(\"Building searcher for \", d.path, chunk_count)\n", "        return []\n", "    return chunks\n", "\n", "\n", "d[pathlib.Path()].visit(v)\n", "len(searchers)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = numpy.load(\n", "    \"/home/<USER>/979ce985-3d15-4ef6-a725-9eedcccc5c7a/915128b5f52df6800ca308ca21f0362bee13147b0fcfb71b7bd57ccf4ed8aafc.npy\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "for s in searchers:\n", "    bf_neighbors, bf_distances = s.search_batched(query)\n", "end = time.time()\n", "print((end - start) * 1000, \"ms\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all_npy = numpy.concatenate([e.data for e in e])\n", "all_searcher = (\n", "    scann.scann_ops_pybind.builder(all_npy, 32, \"dot_product\")\n", "    .score_ah(8, anisotropic_quantization_threshold=0.2)\n", "    .reorder(8 * 1024)\n", "    .build()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "bf_neighbors, bf_distances = all_searcher.search_batched(query)\n", "end = time.time()\n", "print((end - start) * 1000, \"ms\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(searchers))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}