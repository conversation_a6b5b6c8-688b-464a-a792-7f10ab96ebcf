{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install scann numpy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import scann\n", "import time\n", "import numpy\n", "import pathlib\n", "import logging\n", "import sys\n", "from collections import defaultdict\n", "import hashlib\n", "import itertools\n", "import random\n", "import typing\n", "from dataclasses import dataclass, field\n", "\n", "import grpc\n", "\n", "logging.basicConfig(level=logging.INFO, stream=sys.stdout, force=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_embeddings(path):\n", "    e = []\n", "    for file in path.iterdir():\n", "        if file.suffix == \".npy\":\n", "            n = numpy.load(file)\n", "            print(f\"{file}: {n.shape}\")\n", "            e.append(n)\n", "    print(\"file count: \", len(e))\n", "    r = numpy.concatenate(e)\n", "    print(r.shape)\n", "    return r"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["e = load_embeddings(pathlib.Path(\"/mnt/efs/augment/user/request-7942bcf6-66b9-4e3d-8634-b93ff658e614/7942bcf6-66b9-4e3d-8634-b93ff658e614\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searcher_bf = (\n", "    scann.scann_ops_pybind.builder(e, 32, \"dot_product\").score_brute_force().build()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searcher_ah = (\n", "    scann.scann_ops_pybind.builder(e, 32, \"dot_product\")\n", "    .score_ah(8, anisotropic_quantization_threshold=0.2)\n", "    .reorder(64 * 1024)\n", "    .build()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searcher = (\n", "    scann.scann_ops_pybind.builder(e, 32, \"dot_product\")\n", "    .score_ah(4, anisotropic_quantization_threshold=0.2)\n", "    .reorder(1024)\n", "    .tree(num_leaves=2000, num_leaves_to_search=1000, training_sample_size=250000)\n", "    .build()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = numpy.random.rand(1, 512)\n", "query /= numpy.linalg.norm(query)\n", "query = numpy.load(\n", "    \"/mnt/efs/augment/user/request-7942bcf6-66b9-4e3d-8634-b93ff658e614/7942bcf6-66b9-4e3d-8634-b93ff658e614/00002dbeb9fea31c7501886f3a6c62624efd9e9c997dada1b92fb2ed62e6fab5.npy\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start = time.time()\n", "bf_neighbors, bf_distances = searcher_bf.search_batched(query)\n", "end = time.time()\n", "print((end - start) * 1000, \"ms\")\n", "\n", "start = time.time()\n", "ah_neighbors, ah_distances = searcher_ah.search_batched(query)\n", "end = time.time()\n", "print((end - start) * 1000, \"ms\")\n", "\n", "count = 0\n", "for i in range(ah_neighbors.shape[1]):\n", "    if ah_neighbors[0, i] == bf_neighbors[0, i]:\n", "        count += 1\n", "print(count / ah_neighbors.shape[1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dir(searcher)\n", "p = pathlib.Path.home() / \"scann.searcher\"\n", "p.mkdir(exist_ok=True)\n", "searcher.serialize(str(p))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["searcher_bf = (\n", "    scann.scann_ops_pybind.builder(e.astype(numpy.float32), 64, \"dot_product\")\n", "    .score_brute_force()\n", "    .build()\n", ")\n", "bf_neighbors, bf_distances = searcher_bf.search_batched(\n", "    query[0:1].astype(numpy.float32)\n", ")\n", "bf_neighbors"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = numpy.dot(e.astype(numpy.float32), query[0:1].T.astype(numpy.float32)).T[0]\n", "print(data.shape)\n", "top_32_indices = numpy.argpartition(-data, 64)[:64]\n", "top_32_indices = top_32_indices[numpy.argsort(-data[top_32_indices])]\n", "top_32_values = data[top_32_indices]\n", "print(top_32_indices)\n", "print(top_32_values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["T = typing.TypeVar(\"T\")\n", "\n", "\n", "@dataclass\n", "class RetrievalChunk:\n", "    \"\"\"A retrieval chunk.\"\"\"\n", "\n", "    score: float\n", "    blob_name: bytes\n", "    chunk_index: int\n", "\n", "\n", "def _chunked_iterable(\n", "    iterable: typing.Iterable[T], size: int\n", ") -> typing.Generator[list[T], None, None]:\n", "    it = iter(iterable)\n", "    while True:\n", "        chunk = list(itertools.islice(it, size))\n", "        if not chunk:\n", "            break\n", "        yield chunk\n", "\n", "\n", "class SearchException(grpc.RpcError):\n", "    \"\"\"Exception thrown during search processing.\n", "\n", "    Note that subclasses of grpc.RpcError are expected to have code() and details() methods even\n", "    though RpcError itself does not.\"\"\"\n", "\n", "    def __init__(self, status_code: grpc.StatusCode, msg: str = \"\"):\n", "        self.status_code = status_code\n", "        self.msg = msg\n", "\n", "    def code(self) -> grpc.StatusCode:\n", "        return self.status_code\n", "\n", "    def details(self) -> str:\n", "        return self.msg\n", "\n", "    def __str__(self) -> str:\n", "        return f\"CompletionException({self.status_code}: {self.msg})\"\n", "\n", "\n", "@dataclass\n", "class Embeddings:\n", "    \"\"\"Embeddings for a single blob.\"\"\"\n", "\n", "    # the blob name of the embeddings\n", "    blob_name: bytes\n", "\n", "    # the metadata for the embeddings\n", "    # here this means the metadata of the Row blob. Aka it usually has the path name in it\n", "    metadata: dict[str, str]\n", "\n", "    # the embeddings as numpy array\n", "    embeddings: numpy.n<PERSON><PERSON>\n", "\n", "\n", "@dataclass\n", "class DirectoryEmbeddings:\n", "    \"\"\"Embeddings for a directory.\"\"\"\n", "\n", "    # the path of the directory\n", "    path: pathlib.Path\n", "\n", "    # the embeddings for the directory\n", "    embeddings: list[Embeddings]\n", "\n", "    # the children of the directory\n", "    children: list[\"DirectoryEmbeddings\"] = field(default_factory=list)\n", "\n", "    def visit(self, f):\n", "        \"\"\"Visits the directory tree.\"\"\"\n", "        children_values = []\n", "        for c in self.children:\n", "            v = c.visit(f)\n", "            children_values.append((c, v))\n", "        return f(self, children_values)\n", "\n", "\n", "def cluster_dir_embeddings(\n", "    root: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ") -> list[tuple[pathlib.Path, list[Embeddings]]]:\n", "    \"\"\"Clusters the directory embeddings into chunks.\n", "\n", "    It takes a directory tree and returns a list of chunks.\n", "\n", "    Currently, it does a buttom up approach of combining directory tree.\n", "    If a directory subtree is large enough, it will be combined into a single chunk.\n", "    Otherwise, it will be merged into the parent directory's chunk.\n", "\n", "    This is a VERY simple heuristic.\n", "\n", "    Each chunk is a tuple of the path and a list of embeddings.\n", "    We will build an index for each chunk.\n", "    \"\"\"\n", "    result: list[tuple[pathlib.Path, list[Embeddings]]] = []\n", "\n", "    def v(\n", "        d: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "        children_values: list[tuple[DirectoryEmbeddings, typing.Any]],\n", "    ):\n", "        chunks: list[Embeddings] = []\n", "        for _, v in children_values:\n", "            chunks.extend(v)\n", "        chunks += [e for e in d.embeddings]\n", "        chunk_count = sum(chunk.embeddings.shape[0] for chunk in chunks)\n", "        if chunk_count > 1000 or d.path == pathlib.Path():\n", "            result.append((d.path, chunks[:]))\n", "            return []\n", "        return chunks\n", "\n", "    root.visit(v)\n", "    return result\n", "\n", "\n", "def combine_to_directory(\n", "    embeddings: list[Embeddings],\n", ") -> dict[pathlib.Path, DirectoryEmbeddings]:\n", "    \"\"\"Combines embeddings into a directory tree.\n", "\n", "    It takes a list of embeddings and returns a directory tree.\n", "    The directory tree is a dictionary of path to DirectoryEmbeddings.\n", "    Each DirectoryEmbeddings has a list of embeddings and a list of children.\n", "    \"\"\"\n", "    result = {}\n", "    for e in sorted(embeddings, key=lambda x: x.path):\n", "        p = e.path.parent\n", "        if p not in result:\n", "            result[p] = DirectoryEmbeddings(p, [], [])\n", "            last_p = p\n", "            pp = p.parent\n", "            while last_p != pp:\n", "                if pp not in result:\n", "                    result[pp] = DirectoryEmbeddings(pp, [], [])\n", "                if result[last_p] not in result[pp].children:\n", "                    result[pp].children.append(result[last_p])\n", "                last_p = pp\n", "                pp = pp.parent\n", "        result[p].embeddings.append(e)\n", "    return result\n", "\n", "\n", "class Index(typing.Protocol):\n", "    \"\"\"An index for embeddings.\"\"\"\n", "\n", "    def search(\n", "        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]\n", "    ) -> typing.Sequence[RetrievalChunk]:\n", "        \"\"\"Performs a query on the index.\"\"\"\n", "        raise NotImplementedError()\n", "\n", "\n", "class NumpyIndex(Index):\n", "    \"\"\"A simple numpy based index.\"\"\"\n", "\n", "    def __init__(self, embeddings: list[Embeddings]):\n", "        \"\"\"Constructs a new numpy index.\"\"\"\n", "        self.embeddings = [e for e in embeddings if e.embeddings.shape[0] > 0]\n", "\n", "    def search(\n", "        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]\n", "    ) -> typing.Sequence[RetrievalChunk]:\n", "        \"\"\"Performs a query on the index.\n", "\n", "        The implementation is a simple dot product using numpy of every embedding.\n", "        That is followed by sorting and returning the top-32 results.\n", "\n", "        <PERSON><PERSON><PERSON>\n", "            query: the query as a numpy array\n", "\n", "        Returns\n", "            a list of RetrievalChunk\n", "        \"\"\"\n", "        result = []\n", "        for e in self.embeddings:\n", "            if e.blob_name in blobs_removed:\n", "                continue\n", "            ip = numpy.dot(e.embeddings, numpy.transpose(query))\n", "            for i, ip2 in enumerate(ip[0]):\n", "                rc = RetrievalChunk(\n", "                    score=float(ip2),\n", "                    blob_name=e.blob_name.hex(),\n", "                    chunk_index=i,\n", "                )\n", "                result.append(rc)\n", "        result.sort(key=lambda e: e.score, reverse=True)\n", "        return result[:num_results]\n", "\n", "    def __str__(self):\n", "        s = [e.blob_name.hex() for e in self.embeddings]\n", "        return f\"NumpyIndex({s})\"\n", "\n", "    def __repr__(self):\n", "        s = [e.blob_name.hex() for e in self.embeddings]\n", "        return f\"NumpyIndex({s})\"\n", "\n", "\n", "class ScannIndex(Index):\n", "    \"\"\"A scann based index.\n", "\n", "    A scann index is an index that uses the scann library to perform a search.\n", "    The scann library is a high-performance library for nearest neighbor search.\n", "    See https://github.com/google-research/google-research/tree/master/scann for more details.\n", "\n", "    The search is not exact. It is an approximate nearest neighbor search.\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self, name: str, embeddings: list[Embeddings], exact: bool, threads: int\n", "    ):\n", "        \"\"\"Constructs a new scann index.\"\"\"\n", "        self.name = name\n", "\n", "        # filter out empty embeddings\n", "        self.embeddings = [e for e in embeddings if e.embeddings.shape[0] > 0]\n", "\n", "        self.blob_names = set([e.blob_name for e in self.embeddings])\n", "\n", "        if not self.embeddings:\n", "            self.counts = []\n", "            self.indexes = None\n", "            self.searcher = None\n", "            logging.warning(\"Built empty scann index %s\", self)\n", "            return\n", "\n", "        # n is the concatenated embeddings\n", "        n = numpy.concatenate([e.embeddings for e in self.embeddings]).astype(\n", "            numpy.float32\n", "        )\n", "\n", "        # counts is the index of the embeddings at the different indexes of n\n", "        # self.embeddings[counts[x]].blob_name is the blob name of the xth embedding\n", "        counts = numpy.concatenate(  # type: ignore\n", "            [[i] * len(e.embeddings) for i, e in enumerate(self.embeddings)]  # type: ignore\n", "        )\n", "        self.counts = counts\n", "\n", "        # indexes is the index of the chunks at the different indexes of n\n", "        # indexes[x] is the chunk index of the xth embedding\n", "        indexes = numpy.concatenate(\n", "            [list(range(len(e.embeddings))) for e in self.embeddings]\n", "        )\n", "        self.indexes = indexes\n", "\n", "        # build the scann searcher\n", "        # the configuraiton means that we use AH (anisotropic hashing) with 8 bits and 256 reordering.\n", "        # reordering here means that it finds the 256 highest product quantized values and then\n", "        # does a second pass to take the full dot product of those and pick the top 32.\n", "        if exact:\n", "            self.searcher = (\n", "                scann.scann_ops_pybind.builder(n, 32, \"dot_product\")\n", "                .score_brute_force()\n", "                .build()  # type: ignore\n", "            )\n", "            reorder = 0\n", "        else:\n", "            reorder = max(32, n.shape[0] // 20)\n", "            self.searcher = (\n", "                scann.scann_ops_pybind.builder(n, 32, \"dot_product\")\n", "                .score_ah(8, anisotropic_quantization_threshold=0.2)\n", "                .set_n_training_threads(threads)  # type: ignore\n", "                .reorder(reorder)  # type: ignore\n", "                .build()\n", "            )\n", "        logging.info(\"Built scann index %s %s, reorder %s\", self, n.shape, reorder)\n", "\n", "    def __str__(self):\n", "        return (\n", "            f\"ScannIndex({self.name}, {[b.blob_name.hex() for b in self.embeddings]})\"\n", "        )\n", "\n", "    def __repr__(self):\n", "        return (\n", "            f\"ScannIndex({self.name},  {[b.blob_name.hex() for b in self.embeddings]})\"\n", "        )\n", "\n", "    def search(\n", "        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]\n", "    ) -> typing.Sequence[RetrievalChunk]:\n", "        \"\"\"Performs a query on the index.\n", "\n", "        The implementation is a scann searcher.\n", "\n", "        <PERSON><PERSON><PERSON>\n", "            query: the query as a numpy array\n", "\n", "        Returns\n", "            a list of RetrievalChunk\n", "        \"\"\"\n", "        if not self.searcher:\n", "            logging.warning(\"No searcher for %s\", self)\n", "            return []\n", "        start_time = time.time()\n", "        relevant_removed_blobs = blobs_removed & self.blob_names\n", "        for iteration in range(3):\n", "            logging.info(\n", "                \"Searching index %s for %s results with %s/%s blobs removed, query %s %s\",\n", "                self,\n", "                num_results,\n", "                len(relevant_removed_blobs),\n", "                len(self.blob_names),\n", "                query.shape,\n", "                query.dtype,\n", "            )\n", "            neighbors, distances = self.searcher.search_batched(\n", "                query.astype(numpy.float32), num_results\n", "            )\n", "            result = []\n", "            for n, d in zip(neighbors[0], distances[0]):\n", "                blob_name = self.embeddings[self.counts[n]].blob_name\n", "                if blob_name in relevant_removed_blobs:\n", "                    logging.info(\"Skipping removed blob %s\", blob_name.hex())\n", "                    continue\n", "                index: int = self.indexes[n]  # type: ignore\n", "                rc = RetrievalChunk(\n", "                    score=d,\n", "                    blob_name=blob_name.hex(),\n", "                    chunk_index=index,\n", "                )\n", "                result.append(rc)\n", "            # Very fast and loose rule\n", "            if (\n", "                len(result) < num_results * 0.9\n", "                and len(result) < len(self.embeddings) * 0.5\n", "                and iteration < 2\n", "            ):\n", "                logging.info(\"Not enough results, retrying\")\n", "                num_results *= 2\n", "                continue\n", "\n", "            end_time = time.time()\n", "            logging.info(\n", "                \"Searched index %s in %s ms\", self, (end_time - start_time) * 1000\n", "            )\n", "            for i, r in enumerate(result):\n", "                logging.info(\n", "                    \"Result %s: %s.%s with score %s\",\n", "                    i,\n", "                    r.blob_name,\n", "                    r.chunk_index,\n", "                    r.score,\n", "                )\n", "            return result\n", "\n", "        assert False, \"Should not reach here\"\n", "\n", "\n", "class MergeIndex(Index):\n", "    \"\"\"Merges multiple indexes into one.\"\"\"\n", "\n", "    def __init__(self, inner: typing.Sequence[Index]):\n", "        \"\"\"Constructs a new merge index.\"\"\"\n", "        self.inner = inner\n", "\n", "    def search(\n", "        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]\n", "    ) -> typing.Sequence[RetrievalChunk]:\n", "        \"\"\"Performs a query on the index.\n", "\n", "        The implementation is a simple merge of the results from the inner indexes.\n", "        \"\"\"\n", "        result = []\n", "        for i in self.inner:\n", "            result.extend(i.search(query, num_results, blobs_removed))\n", "        result.sort(key=lambda e: e.score, reverse=True)\n", "        return result[:num_results]\n", "\n", "    def __str__(self):\n", "        return f\"MergeIndex({self.inner})\"\n", "\n", "    def __repr__(self):\n", "        return f\"MergeIndex({self.inner})\"\n", "\n", "\n", "def embeddings_hash(embeddings: typing.Sequence[Embeddings]) -> bytes:\n", "    \"\"\"Computes a hash of the embeddings.\"\"\"\n", "    blob_names = [e.blob_name for e in embeddings]\n", "    blob_names.sort()\n", "    h = hashlib.sha256()\n", "    h.update(b\" \".join(blob_names))\n", "    return h.digest()\n", "\n", "\n", "def generate_hash_functions(\n", "    k, max_value\n", ") -> typing.Sequence[typing.Callable[[int], int]]:\n", "    \"\"\"Generates a list of hash functions.\"\"\"\n", "    hash_funcs = []\n", "    p = next_prime(max_value)\n", "    for _ in range(k):\n", "        a = random.randint(1, max_value)\n", "        b = random.randint(0, max_value)\n", "        hash_funcs.append(lambda x, a=a, b=b, p=p: (a * x + b) % p)\n", "    return hash_funcs\n", "\n", "\n", "def next_prime(n):\n", "    # Simple function to find the next prime >= n\n", "    def is_prime(num):\n", "        if num < 2:\n", "            return False\n", "        for i in range(2, int(num**0.5) + 1):\n", "            if num % i == 0:\n", "                return False\n", "        return True\n", "\n", "    while not is_prime(n):\n", "        n += 1\n", "    return n\n", "\n", "\n", "num_hashes = 32\n", "\n", "hash_functions = generate_hash_functions(num_hashes, max_value=1000)\n", "\n", "\n", "def divide_into_bands(signature, num_bands):\n", "    band_size = len(signature) // num_bands\n", "    # tuple so that it is hashable\n", "    bands = [\n", "        tuple(signature[i * band_size : (i + 1) * band_size]) for i in range(num_bands)\n", "    ]\n", "    return bands\n", "\n", "\n", "def compute_minhash_signature(\n", "    set_elements: typing.Sequence[Embeddings],\n", "    hash_funcs: typing.Sequence[typing.Callable[[int], int]],\n", ") -> typing.Sequence[int]:\n", "    \"\"\"Computes the minhash signature of a set of embeddings.\"\"\"\n", "    signature = []\n", "    for hash_func in hash_funcs:\n", "        min_hash = float(\"inf\")\n", "        for element in set_elements:\n", "            hashed = hash_func(hash(element.blob_name))\n", "            min_hash = min(min_hash, hashed)\n", "        signature.append(min_hash)\n", "    return signature\n", "\n", "\n", "class LshIndex:\n", "    \"\"\"Index for locality sensitive hashing.\"\"\"\n", "\n", "    def __init__(self):\n", "        \"\"\"Constructs a new LshIndex.\"\"\"\n", "        self.index = {}\n", "\n", "    def add(self, set_id: str, signature: typing.Sequence[int], num_bands: int):\n", "        logging.info(\n", "            \"Adding %s to index: signature %s, num_bands %s\",\n", "            set_id,\n", "            signature,\n", "            num_bands,\n", "        )\n", "        bands = divide_into_bands(signature, num_bands)\n", "        for band_id, band in enumerate(bands):\n", "            hash_value = hash(band)\n", "            self.index.setdefault((band_id, hash_value), set()).add(set_id)\n", "\n", "    def find_candidates(\n", "        self, set_id: str, signature: typing.Sequence[int], num_bands: int\n", "    ):\n", "        logging.info(\n", "            \"Finding candidates for %s: signature %s, num_bands %s, index size %s\",\n", "            set_id,\n", "            signature,\n", "            num_bands,\n", "            len(self.index),\n", "        )\n", "        candidates = set()\n", "        bands = divide_into_bands(signature, num_bands)\n", "        for band_id, band in enumerate(bands):\n", "            hash_value = hash(band)\n", "            bucket = self.index.get((band_id, hash_value), set())\n", "            candidates.update(bucket)\n", "        candidates.discard(set_id)  # Remove the set itself from candidates\n", "        logging.info(\"Found candidates: %s\", candidates)\n", "        return candidates\n", "\n", "\n", "class IndexDelta:\n", "    \"\"\"Represents a delta between two indexes.\"\"\"\n", "\n", "    def __init__(self, added: set[bytes], removed: set[bytes], overlap: int):\n", "        \"\"\"Constructs a new index delta.\"\"\"\n", "        self.added = added\n", "        self.removed = removed\n", "        self.overlap = overlap\n", "\n", "    def __str__(self):\n", "        return f\"IndexDelta(added={[e.hex() for e in self.added]}, removed={[e.hex() for e in self.removed]}, overlap={self.overlap})\"\n", "\n", "    def __repr__(self):\n", "        return f\"IndexDelta(added={[e.hex() for e in self.added]}, removed={[e.hex() for e in self.removed]}, overlap={self.overlap})\"\n", "\n", "\n", "class LshChunkIndexEntry:\n", "    def __init__(\n", "        self,\n", "        index: Index,\n", "        signature: typing.Sequence[int],\n", "        embedding_group: typing.Sequence[Embeddings],\n", "        original_checkpoint_id: str,\n", "    ):\n", "        self.index = index\n", "        self.signature = signature\n", "        self.embedding_group = embedding_group\n", "        self.blob_names = set([e.blob_name for e in embedding_group])\n", "        self.original_checkpoint_id = original_checkpoint_id\n", "\n", "    def __str__(self):\n", "        return (\n", "            f\"LshChunkIndexEntry({self.embedding_group, self.original_checkpoint_id})\"\n", "        )\n", "\n", "    def __repr__(self):\n", "        return (\n", "            f\"LshChunkIndexEntry({self.embedding_group, self.original_checkpoint_id})\"\n", "        )\n", "\n", "    def compute_delta(self, embedding_group: typing.Sequence[Embeddings]) -> IndexDelta:\n", "        \"\"\"Computes the overlap between the embedding group of this entry and the given embedding group.\"\"\"\n", "        overlap = 0\n", "        # added = not in existing group, but in new group\n", "        added = set([e.blob_name for e in embedding_group]) - set(\n", "            [e.blob_name for e in self.embedding_group]\n", "        )\n", "        # removed = in existing group, but not in new group\n", "        removed = set([e.blob_name for e in self.embedding_group]) - set(\n", "            [e.blob_name for e in embedding_group]\n", "        )\n", "        for e in embedding_group:\n", "            if e.blob_name in self.blob_names:\n", "                overlap += 1\n", "        return IndexDelta(added, removed, overlap)\n", "\n", "\n", "class RemovedIndexWrapper(Index):\n", "    \"\"\"Wraps an index and removes the deleted embeddings.\"\"\"\n", "\n", "    def __init__(self, inner: Index, removed: set[bytes]):\n", "        self.inner = inner\n", "        self.removed = removed\n", "\n", "    def search(\n", "        self, query: numpy.ndarray, num_results: int, blobs_removed: set[bytes]\n", "    ) -> typing.Sequence[RetrievalChunk]:\n", "        \"\"\"Performs a query on the index.\n", "\n", "        The implementation is a simple wrapper that filters out the removed embeddings.\n", "        A real implementation would need to be more efficient.\n", "\n", "        \"\"\"\n", "        if blobs_removed:\n", "            b = blobs_removed | self.removed\n", "        else:\n", "            b = self.removed\n", "\n", "        return self.inner.search(query=query, num_results=num_results, blobs_removed=b)\n", "\n", "    def __str__(self):\n", "        return f\"RemovedIndexWrapper({self.inner}, removed={[e.hex() for e in self.removed]})\"\n", "\n", "    def __repr__(self):\n", "        return f\"RemovedIndexWrapper({self.inner}, removed={[e.hex() for e in self.removed]})\"\n", "\n", "\n", "def group_by_first_byte(\n", "    embeddings: list[Embeddings],\n", ") -> typing.Iterable[list[Embeddings]]:\n", "    groups = defaultdict(list)\n", "    for embedding in embeddings:\n", "        byte_obj = embedding.blob_name\n", "        groups[byte_obj[0]].append(embedding)\n", "    return [sublist for sublist in groups.values() if len(sublist) > 0]\n", "\n", "\n", "class IndexBuilder(typing.Protocol):\n", "    def build_index(self, embeddings: list[Embeddings], checkpoint_id: str) -> Index:\n", "        raise NotImplementedError()\n", "\n", "\n", "class MinHashBuilder(IndexBuilder):\n", "    def __init__(self, allowed_use_numpy: bool, exact: bool, threads: int):\n", "        self.allowed_use_numpy = allowed_use_numpy\n", "        self.exact = exact\n", "        self.threads = threads\n", "        self.lsh_index = LshIndex()\n", "        self.lsh_chunk_index = {}\n", "\n", "    def build_index(self, embeddings: list[Embeddings], checkpoint_id: str) -> Index:\n", "        chunk_indexes = []\n", "        embeddings.sort(key=lambda e: e.blob_name)\n", "        logging.info(\"Building index for %s\", [e.blob_name for e in embeddings])\n", "        for embedding_group in group_by_first_byte(embeddings):\n", "            embedding_group_name = embeddings_hash(embedding_group)\n", "            logging.info(\n", "                \"Build chunk index for %s, name %s\",\n", "                [e.blob_name.hex() for e in embedding_group],\n", "                embedding_group_name.hex(),\n", "            )\n", "\n", "            signature = compute_minhash_signature(embedding_group, hash_functions)\n", "\n", "            candidates = self.lsh_index.find_candidates(\n", "                embedding_group_name.hex(), signature, 10\n", "            )\n", "            best_overlap: typing.Tuple[IndexDelta, LshChunkIndexEntry] | None = None\n", "            for candidate in candidates:\n", "                candidate_index: LshChunkIndexEntry | None = self.lsh_chunk_index.get(\n", "                    candidate\n", "                )\n", "                if candidate_index:\n", "                    delta = candidate_index.compute_delta(embedding_group)\n", "                    logging.info(\"Checking candidate index: overlap: %s\", delta.overlap)\n", "                    if best_overlap is None or delta.overlap > best_overlap[0].overlap:\n", "                        best_overlap = (delta, candidate_index)\n", "\n", "            chunk_index: Index | None = None\n", "            if best_overlap is not None:\n", "                logging.info(\"Best overlap: %s\", best_overlap[0])\n", "                if best_overlap[0].overlap > len(embedding_group) * 0.9:\n", "                    logging.info(\n", "                        \"checkpoint %s: Using existing index based on overlap: %s from checkpoint %s\",\n", "                        checkpoint_id,\n", "                        best_overlap[0],\n", "                        best_overlap[1].original_checkpoint_id,\n", "                    )\n", "                    chunk_index = best_overlap[1].index\n", "                    assert chunk_index is not None\n", "                    if best_overlap[0].removed:\n", "                        chunk_index = RemovedIndexWrapper(\n", "                            chunk_index,\n", "                            set([e for e in best_overlap[0].removed]),\n", "                        )\n", "                    if best_overlap[0].added:\n", "                        added_embeddings = [\n", "                            e\n", "                            for e in embedding_group\n", "                            if e.blob_name in best_overlap[0].added\n", "                        ]\n", "                        chunk_index = MergeIndex(\n", "                            [\n", "                                chunk_index,\n", "                                NumpyIndex(added_embeddings),\n", "                            ]\n", "                        )\n", "\n", "            if chunk_index is None:\n", "                # we didn't find a good candidate, so we build a new index\n", "                if self.allowed_use_numpy:\n", "                    chunk_index = NumpyIndex(embedding_group)\n", "                else:\n", "                    chunk_index = ScannIndex(\n", "                        embedding_group_name.hex(),\n", "                        embedding_group,\n", "                        self.exact,\n", "                        self.threads,\n", "                    )\n", "                self.lsh_index.add(embedding_group_name.hex(), signature, 10)\n", "                self.lsh_chunk_index[embedding_group_name.hex()] = LshChunkIndexEntry(\n", "                    chunk_index, signature, embedding_group, checkpoint_id\n", "                )\n", "            chunk_indexes.append(chunk_index)\n", "        i = MergeIndex(chunk_indexes)\n", "        return i\n", "\n", "\n", "class FlatBuilder(IndexBuilder):\n", "    def __init__(self, allowed_use_numpy: bool, exact: bool, threads: int):\n", "        self.allowed_use_numpy = allowed_use_numpy\n", "        self.exact = exact\n", "        self.threads = threads\n", "\n", "    def build_index(self, embeddings: list[Embeddings], checkpoint_id: str) -> Index:\n", "        if self.allowed_use_numpy:\n", "            i = NumpyIndex(embeddings)\n", "        else:\n", "            i = ScannIndex(checkpoint_id, embeddings, self.exact, self.threads)\n", "        return i"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "\n", "def load_embeddings(path):\n", "    e = []\n", "    for file in path.iterdir():\n", "        try:\n", "            if file.suffix == \".npy\":\n", "                n = numpy.load(file)\n", "                print(f\"{file}: {n.shape}\")\n", "                m = json.loads(file.with_suffix(\".json\").read_text())\n", "                e.append(Embeddings(bytes.fromhex(file.with_suffix(\"\").name), m, n))\n", "        except FileNotFoundError:\n", "            pass\n", "    print(\"file count: \", len(e))\n", "    return e"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embeddings_a6 = load_embeddings(\n", "    pathlib.Path(\"/tmp/a6fe44a4-73a7-4510-8a8e-d443454dd2dd\")\n", ")\n", "embeddings_b4 = load_embeddings(\n", "    pathlib.Path(\"/tmp/b43da157-5804-45a9-aaf5-9581d15f004a\")\n", ")\n", "query = embeddings_a6[0].embeddings[0:1]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["embeddings_a6.sort(key=lambda e: e.blob_name)\n", "print([e.blob_name for e in embeddings_a6[0:16]])\n", "embeddings_b4.sort(key=lambda e: e.blob_name)\n", "print([e.blob_name for e in embeddings_b4[0:16]])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_builder = MinHashBuilder(True, True, 1)\n", "flat_builder = FlatBuilder(True, True, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_index = min_hash_builder.build_index(embeddings_a6, \"a6_1\")\n", "exact_index = flat_builder.build_index(embeddings_a6, \"test\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_result = min_hash_index.search(query, 32, set())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exact_result = exact_index.search(query, 32, set())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exact_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_index_b4 = min_hash_builder.build_index(embeddings_b4, \"b4\")\n", "exact_index_b4 = flat_builder.build_index(embeddings_b4, \"b4\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_result_b4 = min_hash_index_b4.search(query, 32, set())\n", "exact_result_b4 = exact_index_b4.search(query, 32, set())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_result_b4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exact_result_b4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pathlib.Path.home().joinpath(\"min_hash_index_b4\").write_text(str(min_hash_index_b4))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["min_hash_index.inner"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logging.basicConfig(level=logging.INFO)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}