from google.cloud import bigtable

SIGMA = "5add39e379a24c054ccd3bb88cc46e27"
SIFIVE = "b64d2734d74bffad8b9f36e155d0029f"


def delete_rows_with_prefix(instance_id, table_id, prefix, project_id):
    """
    Deletes rows with specific prefixes from a Bigtable table.

    Args:
        instance_id (str): The ID of the Bigtable instance.
        table_id (str): The ID of the table from which rows will be deleted.
        prefixes (list): A list of row key prefixes to delete.
        project_id (str): The ID of the GCP project.
    """
    # Initialize Bigtable client and instance
    client = bigtable.Client(project=project_id, admin=True)
    instance = client.instance(instance_id)
    table = instance.table(table_id)

    print(f"Deleting rows from {table_id} with prefix: {prefix}")
    table.drop_by_prefix(row_key_prefix=prefix)

    print("Row deletion complete.")


def main():
    for tenant_id in [
        "24919d1b367b80b93cca0734418377c5",
        "fc05abbd51156ca07640ec1c652cbf66",
        "e73fd756deb03f77f9603de1d70a5ce7",
        "5fbf919a1d8bc4de47fd56d424c86a62",
    ]:
        delete_rows_with_prefix(
            "bigtable-central",
            "central-content-manager",
            f"{tenant_id}#info",
            "system-services-prod",
        )
        delete_rows_with_prefix(
            "bigtable-central",
            "central-content-manager",
            f"{tenant_id}#content",
            "system-services-prod",
        )


if __name__ == "__main__":
    main()
