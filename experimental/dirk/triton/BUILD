load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl/pytest:defs.bzl", "pytest_test")

# use triton in a pytest
pytest_test(
    name = "triton-test",
    srcs = ["triton_test.py"],
    tags = [
        "gpu",
    ],
    deps = [
        requirement("torch"),
        requirement("triton"),
        requirement("setuptools"),  # triton has an undeclared dependency on setuptools
        requirement("numpy"),
    ],
)

# use triton in a binary
py_binary(
    name = "run",
    srcs = ["triton_test.py"],
    main = "triton_test.py",
    deps = [
        requirement("torch"),
        requirement("triton"),
        requirement("setuptools"),  # triton has an undeclared dependency on setuptools
        requirement("numpy"),
    ],
)
