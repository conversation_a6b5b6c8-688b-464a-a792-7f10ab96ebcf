{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Example for Unicode Safety"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["This notebooks demonstrates a few pitfalls when dealing with character encodings, byte array and Unicode."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Changing behavior by splitting bytes"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This example shows a common pitfall when working on bytes.\n", "Let's assume we have a byte array containing an UTF-8 encoded string.\n", "\n", "It is dangerous to do splitting operations on the byte array. The example shows that if the byte array b is split into two pieces at offset 7, both byte arrays no longer contain valid UTF-8 strings.\n", "\n", "The normal `decode` call throws an UnicodeDecodeError. While it is possible to shallow the exception with e.g. `errors=replace`, that is changing the meaning of the string."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s = u\"Hello \\U0001F44B World\"\n", "print(s)\n", "print()\n", "\n", "b = s.encode(\"utf-8\")\n", "print(b)\n", "b1, b2 = b[:7], b[7:]\n", "print(b1, \"#\", b2)\n", "print()\n", "\n", " # throws UnicodeDecodeError\n", "# s1 = b1.decode(\"utf-8\")\n", "# s2 = b2.decode(\"utf-8\")\n", "s1 = b1.decode(\"utf-8\", errors=\"replace\")\n", "s2 = b2.decode(\"utf-8\", errors=\"replace\")\n", "print(s1 + s2)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Changing behavior by splitting unicode strings\n", "\n", "This example shows how splitting a unicode string can change the behavior.\n", "The example is using Emoji Modifier Sequences, but there are other variants where\n", "Unicode characters are combined.\n", "\n", "The next code block creates and prints \"Hello\", the waving hand emoji with medium skin tone, followed by \"World\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["s = \"Hello \\U0001F44B\\U0001F3FD World\"\n", "print(s)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we split that string into two strings from `[0, 7)` and `[7, 14)`, we change the output. The waving hand is printed without a skin tone modifier and the medium skin tone character is now invalid."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(s[:7], s[7:])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mismatch between UTF-16 and UTF-8\n", "\n", "UTF-16 is a character encoding that is common (but is getting less common) in the Java and Windows world.\n", "\n", "The following example shows a valid UTF-16 string that is an invalid UTF-8 string."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = b\"\\x3D\\xD8\\x00\\xDE\"\n", "print(\"UTF-16:\", b.decode(\"utf-16\"))\n", "\n", "# throws UnicodeDecodeError\n", "# b.decode(\"utf-8\")\n", "print(\"UTF-8:\", b.decode(\"utf-8\", errors=\"replace\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}