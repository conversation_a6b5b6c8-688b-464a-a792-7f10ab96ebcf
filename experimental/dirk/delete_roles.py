"""Delete role bindings for deleted service accounts."""
import json
import shlex
import subprocess


def get_work():
    cmd = [
        "gcloud",
        "projects",
        "get-iam-policy",
        "system-services-dev",
        "--format",
        "json",
    ]
    r = subprocess.run(cmd, capture_output=True, text=True, check=True)
    r = json.loads(r.stdout)["bindings"]
    for binding in r:
        for member in binding["members"]:
            if member.startswith("deleted:serviceAccount:"):
                # yield role and service account name
                yield binding["role"], member


def main():
    for role, service_account in reversed(list(get_work())):
        cmd = [
            "gcloud",
            "projects",
            "remove-iam-policy-binding",
            "system-services-dev",
            "--member",
            f"{service_account}",
            "--role",
            role,
        ]
        print(shlex.join(cmd))
        subprocess.run(cmd, check=True)


if __name__ == "__main__":
    main()
