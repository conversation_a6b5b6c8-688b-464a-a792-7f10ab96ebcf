#!/usr/bin/env python3
import subprocess
import json
from datetime import datetime, timedelta, timezone
from dateutil import parser


def get_kubectl_json(command):
    """Runs a kubectl command and returns the JSON output."""
    result = subprocess.run(command, stdout=subprocess.PIPE, check=True)
    return json.loads(result.stdout)


def get_namespaces():
    """Get all namespaces and filter those starting with 'dev-'."""
    namespaces_output = subprocess.run(
        ["kubectl", "get", "namespaces", "-o", "jsonpath='{.items[*].metadata.name}'"],
        stdout=subprocess.PIPE,
        check=True,
    )
    namespaces = namespaces_output.stdout.decode("utf-8").split()
    return [ns for ns in namespaces if ns.startswith("dev-")]


def get_deployments_in_namespace(namespace):
    """Get all deployments in a specific namespace."""
    command = ["kubectl", "get", "deployments", "-n", namespace, "-o", "json"]
    return get_kubectl_json(command)


def get_last_update_time(conditions):
    """Extract the last update time from the 'Progressing' condition."""
    for condition in conditions:
        if condition["type"] == "Progressing" and "lastUpdateTime" in condition:
            return parser.parse(condition["lastUpdateTime"])
    return None


def is_old(timestamp):
    """Check if a timestamp is old"""
    return (datetime.now(timezone.utc) - timestamp) > timedelta(hours=72)


def main():
    # Get all namespaces starting with 'dev-'
    namespaces = get_namespaces()

    for ns in namespaces:
        deployments = get_deployments_in_namespace(ns)

        for deployment in deployments["items"]:
            containers = deployment["spec"]["template"]["spec"]["containers"]

            # Check if any container in the deployment has GPU resource requests or limits
            uses_gpu = any(
                "nvidia.com/gpu" in container.get("resources", {}).get("requests", {})
                or "nvidia.com/gpu" in container.get("resources", {}).get("limits", {})
                for container in containers
            )

            has_replicas = deployment["spec"]["replicas"] > 0

            if uses_gpu and has_replicas:
                # Check the 'Progressing' condition for lastUpdateTime
                conditions = deployment["status"].get("conditions", [])
                last_update_time = get_last_update_time(conditions)

                if last_update_time and is_old(last_update_time):
                    deployment_name = deployment["metadata"]["name"]
                    print(
                        f"kubectl scale deployment {deployment_name} -n {ns} --replicas=0 --timeout=30s"
                    )


if __name__ == "__main__":
    main()
