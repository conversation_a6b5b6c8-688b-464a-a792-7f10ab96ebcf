"""Generate a file with random tokens."""
import argparse
import random
from pathlib import Path


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("output_file", type=Path)
    parser.add_argument("--input-len", required=True, type=int)
    parser.add_argument("--seed", type=int)
    args = parser.parse_args()
    output_file: Path = args.output_file

    if args.seed:
        r = random.Random(args.seed)
    else:
        r = random.Random()
    with output_file.open(mode="w", encoding="utf-8") as f:
        print("Writing", args.input_len, "tokens to", output_file)
        tokens = [str(r.randint(0, 10000)) for _ in range(args.input_len)]
        print(", ".join(tokens), file=f, flush=True)


if __name__ == "__main__":
    main()
