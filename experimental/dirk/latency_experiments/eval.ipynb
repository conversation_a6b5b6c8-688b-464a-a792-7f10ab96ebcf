{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# FTM latency experiments\n", "\n", "More details can be found at [https://www.notion.so/FTM-Latency-Experiments-5cf6a5b18ff84bb9b811a143ef2ddf75?pvs=4]."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Installation and Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install matplotlib pandas\n", "!pip install nbconvert\n", "import pandas\n", "import json\n", "from pathlib import Path"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Loading Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["EXPERIMENT_DIR = Path(\"/mnt/efs/augment/user/dirk/latency_jobs\")\n", "\n", "def get_config(path: Path):\n", "    n = path.name\n", "    if \"config\" in n:\n", "        return n[:-12]\n", "    return n[:-5]\n", "\n", "def read_job_file(job_path: Path):\n", "    df = pandas.json_normalize(json.loads(job_path.read_text()))\n", "    df[\"name\"] = job_path.name[:-5]\n", "    df[\"file\"] = str(job_path)\n", "    df[\"config\"] = df[\"input.instance_config\"].transform(lambda c: get_config(Path(c)))\n", "    if \"input.code_version\" in df.columns:\n", "        df[\"code_version\"] = df[\"input.code_version\"].transform(lambda c: c)\n", "    else:\n", "        df[\"code_version\"] = \"ft51\"\n", "    df = df.rename(\n", "        columns={\n", "            \"input.gpu\": \"gpu\",\n", "            \"input.request_input_len\": \"input_len\",\n", "            \"input.request_output_len\": \"output_len\",\n", "            \"input.tensor_parallelism\": \"tp\",\n", "            \"summary.latency\": \"latency\",\n", "        }\n", "    )\n", "    df = df[\n", "        [\"code_version\", \"tp\", \"config\", \"input_len\", \"output_len\", \"gpu\", \"latency\", \"name\", \"file\"]\n", "    ]\n", "    return df\n", "\n", "\n", "def read_all():\n", "    result_path = EXPERIMENT_DIR / \"results\"\n", "    assert result_path.exists()\n", "    return pandas.concat([read_job_file(f) for f in result_path.glob(\"*.json\")])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r = read_all().sort_values(by=[\"tp\", \"config\", \"input_len\", \"output_len\"])\n", "print(f\"Found {len(r)} results\")\n", "a10 = r[(r.gpu == \"NVIDIA A10G\") & (r.code_version == \"ft51\")]\n", "a100 = r[(r.gpu == \"A100\") & (r.code_version == \"ft51\")]\n", "ft53 = r[r.code_version == \"ft53\"]\n", "print(f\"Found {len(ft53)} results\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Single A10 GPU latency\n", "\n", "### Latency Budget\n", "\n", "These charts show for a given model and GPU configuration, which input len/output len token stay within a defined latency budget, e.g. 1 seconds.\n", "All these charts are for the NVIDIA A10 GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_latency_budget(data, filter_config, filter_latency, filter_tp):\n", "    r2 = data[\n", "        (data.tp == filter_tp)\n", "        & (data.config == filter_config)\n", "        & (data.latency <= filter_latency)\n", "    ].sort_values(by=[\"output_len\", \"input_len\"])\n", "    print(r2[[\"output_len\", \"input_len\", \"latency\"]])\n", "    p1 = r2.plot.scatter(\n", "        x=\"input_len\",\n", "        y=\"output_len\",\n", "        c=\"latency\",\n", "        ylabel=\"Output tokens\",\n", "        xlabel=\"Input tokens\",\n", "        colormap=\"plasma\",\n", "        title=f\"{filter_config} - {filter_latency}s - {filter_tp} GPU\",\n", "    )\n", "    p1.plot()\n", "\n", "\n", "plot_latency_budget(a10, \"codegen-6B-multi\", 1.0, 1)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- The diagram indicates the reason for the interest to use multi-GPU to reduce latency. For an output length of 64 token, the largest input length that stays within the 2s latency budget has 512 tokens. No generation with 128 tokens stays in budget"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Latency With Fixed Input Length\n", "\n", "This chart shows the latency with a request input len of 1024 for different models using a single A10 GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = a10[(a10.tp == 1) & (a10.input_len == 1024)]\n", "r3 = r3.pivot(index=\"output_len\", columns=\"config\", values=\"latency\")\n", "p = r3.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"latency (in s)\",\n", "    xlabel=\"Output length (in token)\",\n", "    loglog=True,\n", ")\n", "p.plot();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- In general, the latency grows linearly with the number of output tokens. \n", "- That doesn't appear to be true for the 350M model. The reasons are not investigated.\n", "- The memory variants shows slightly higher latency than the plain codegen models. This is due to the extra memory attention calculations. As a reminder, in this experiments the `null` memory index was used.\n", "- The codegen model was trained with an input length of 2K tokens. Thus, it can be expected that quality might suffer once the input length is increased beyond. In addition, the size of the input tokens not only use quadratic compute time, but also quadratic memory. Experiments with larger input length configurations have been ran, but the experiment job runs out of GPU memory (OOM). This limitation can be overcome by additional work."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Latency With Fixed Output Length\n", "\n", "This chart shows the latency with a request output length of 128 for different models using a single GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = a10[(a10.tp == 1) & (a10.output_len == 128)]\n", "r3 = r3.pivot(index=\"input_len\", columns=\"config\", values=\"latency\")\n", "p = r3.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"latency (in s)\",\n", "    xlabel=\"Input length (in token)\",\n", "    #loglog=True\n", ")\n", "p.plot();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- As expected is the latency growing quadratic to the input length. This is esp. showing once a input length of 1024 is reached.\n", "- Currently, the staging deployment uses between 160-170 tokens (500 characters). The data indicates that this number of overly pessimistic. The input length can be increased to 1024 with only `TODO` percent increase in latency."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Multi A10 GPU Latency\n", "\n", "This section dives into FTM latency when multiple A10 GPU are used. The tradeoff is that larger models can be used (and more memories in the current memory index). In addition, for a given model a higher GPU count reduces overall latency. On the other hand, hardware nodes with more than a single GPU are more expensive than single GPU models.\n", "\n", "Here, all measurements with a single GPU are done on `g5.2xlarge` AWS instances with one A10 GPU. All multi GPU measurements are done on `g5.12xlarge` with 4 A10 GPU."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Comparision\n", "\n", "This chart show the results for the 6B model (the only model executed in single GPU and multi GPU mode) with a fixed output length of 64 tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = a10[(a10.config == \"codegen-6B-multi\") & (a10.output_len == 64)]\n", "r3 = r3.pivot(index=\"input_len\", columns=\"tp\", values=\"latency\")\n", "p = r3.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"latency (in s)\",\n", "    xlabel=\"Input length (in token)\",\n", "    title=\"Latency for the 6B-multi model with 64 output tokens for different numbers of GPUS\",\n", ")\n", "p.plot()\n", "print(r3)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- Scaling the number of GPU shows a surprisingly good scaling behavior. For example with 1024 input tokens, 2 GPU take 40% less time than with 1 GPU. Optimal would be 50%, but that cannot be achieved due to the obvious (communication) overhead."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = a10[(a10.config == \"codegen-16B-multi\") & (a10.output_len == 64)]\n", "r3 = r3.pivot(index=\"input_len\", columns=\"tp\", values=\"latency\")\n", "p = r3.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"latency (in s)\",\n", "    xlabel=\"Input length (in token)\",\n", "    title=\"Latency for the 16B-multi model with 64 output tokens for different numbers of GPUS\",\n", ")\n", "p.plot()\n", "print(r3)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Latency Budget\n", "\n", "This chart shows the combinations of input token length and output token lengths using 4 GPU and the 16B model where the latency is within 1.0 seconds."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "plot_latency_budget(a10, \"codegen-6B-multi\", 1.0, 4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_latency_budget(a10, \"codegen-16B-multi\", 1.0, 4)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- This diagram shows the latency budget limits with largest model with the highest number of GPU available on `g5.12xlarge` systems.\n", "- While scaling GPU reduces the latency and enabled larger model sizes, there are limits to the joy.\n", "- To stay within a 1s limit (which is already a relative high latency limit) even with 4 GPU tradeoffs are required. For example, with 64 output tokens, the largest possible context is (TODO not yet known)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r[(r.code_version == \"ft51\") & (r.config == \"codegen-6B-multi\") & (r.input_len == 192) & (r.output_len == 512)].sort_values(by=[\"tp\", \"config\", \"output_len\", \"input_len\"])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## A100 Results\n", "\n", "This sections show the latency experiment results for the A100 GPU(s). The measurements were performed in the CoreWeave data center. As the experiments do only rely on the GPU and the interconnect, I expect similar results in AWS with the same GPU type."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The next diagram shows a basic comparison between 4-A10 GPU and A100 GPU(s).\n", "One takeaway is that one A100 is roughly equivalent to 4 A10.\n", "The scale-up from one A100 to two A100 is roughly 60%. For 1024 input tokens, the latency decreases from 1.7s to 1.1s.\n", "Surprisingly, the scale-up from two A100 to four A100 equally good. For 1024 tokens, the latency further decreases from 1.1s to 0.7s.\n", "To some extent, it looks like an effect of the 1024 input token length. The input plateau (how long the latency is flat when the input tokens are increased) stops earlier for a larger number of GPUs. That behavior was also seen with A100 GPU."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = r[(r.code_version == \"ft51\") & (r.config == \"codegen-16B-multi\") & (r.output_len == 1) & (((r.gpu == \"A100\")) | ((r.gpu != \"A100\") & (r.tp == 4)))]\n", "r3 = r3.pivot(index=\"input_len\", columns=[\"gpu\", \"tp\"], values=\"latency\")\n", "p = r3.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"latency (in s)\",\n", "    xlabel=\"Input length (in token)\",\n", "    title=\"Latency for the 16B-multi model with 64 output tokens for different numbers of GPUS\",\n", ")\n", "p.plot()\n", "print(r3)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The next diagram shows the latency budget chart with the 6B and 16B model using a single A100.\n", "As indicated earlier the results are similar to the results with four A10."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_latency_budget(a100, \"codegen-6B-multi\", 1.0, 1)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_latency_budget(a100, \"codegen-16B-multi\", 1.0, 1)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_latency_budget(a100, \"codegen-16B-multi\", 1.0, 2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# FasterTransformer 5.3\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This section shows the results for FasterTransformer 5.3 which uses the TensorRT based fused multi-head attention (fmha) and FlashAttention (for larger models and context sizes).\n", "\n", "FlashAttention optimizes the memory and latency at larger context sizes. It doesn't change the decoding phase of the generation. The hope is to be able to use larger context sizes before the the quadratic nature of the context sizes make larger context sizes infeasible."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_code_version_latency(data, config, output_len=1):\n", "    r3 = data[(data.config == f\"codegen-{config}-multi\") & (data.output_len == output_len)]\n", "    r3 = r3.pivot(index=\"input_len\", columns=[\"code_version\"], values=\"latency\")\n", "    p = r3.plot(\n", "        marker=\"o\",\n", "        linestyle=\"--\",\n", "        ylabel=\"latency (in s)\",\n", "        xlabel=\"Input length (in token)\",\n", "        title=f\"Latency for the {config}-multi model with {output_len} output token\",\n", "        #loglog=True\n", "    )\n", "    p.plot()\n", "    r3[\"diff\"] = (1 - r3[\"ft53\"] / r3[\"ft51\"]) * 100.0\n", "    print(r3)\n", "r3 = r[(r.gpu != \"A100\") & (r.tp == 1)]\n", "plot_code_version_latency(data=r3, config=\"2B\", output_len=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = r[(r.gpu != \"A100\") & (r.tp == 1)]\n", "plot_code_version_latency(data=r3, config=\"6B\", output_len=1)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Surprisingly, we do not see the same speedup with the 6B model. A closer [examination](https://github.com/augmentcode/augment/pull/325#issuecomment-1472536783) confirmed the latency experiments."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## FasterTransformer 5.3 with A100 GPU\n", "\n", "Our likely production target are A100 GPU.\n", "\n", "The experiments focus on the CodeGen 6B model on one A100 and the CodeGen 16B model on two A100."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### CodeGen 6B on a single A100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = r[(r.gpu == \"A100\") & (r.tp == 1)]\n", "plot_code_version_latency(data=r3, config=\"6B\", output_len=1)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Similar to the 6B model on the A10, there are only slight changes on latency on the A100."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_latency_budget(ft53[ft53.gpu == \"A100\"], \"codegen-6B-multi\", 1.0, 1)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### CodeGen 16B on 2 A100"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = r[(r.gpu == \"A100\") & (r.tp == 2)]\n", "plot_code_version_latency(data=r3, config=\"16B\", output_len=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_latency_budget(ft53[ft53.gpu == \"A100\"], \"codegen-16B-multi\", 1.0, 2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = r[(r.config == \"codegen-16B-multi\") & (r.gpu == \"A100\") & (r.tp == 2) & (r.code_version == \"ft53\")]\n", "r3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r3 = r[(r.config == \"codegen-6B-multi\") & (r.gpu == \"A100\") & (r.tp == 2)]\n", "r3"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Token/s graphs"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The next diagram shows the time until the first token. In fact, it is the time with output_len 1, but that is a close enough proxy\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = r[(r.gpu == \"A100\") & (r.code_version == \"ft53\") & (r.output_len == 1) & (r.input_len < 6000)] \n", "data[\"full_config\"] = data.apply(lambda e : f\"{e.gpu} / {e.tp} / {e.config}\", axis=1)\n", "r3 = data.pivot(index=\"input_len\", columns=[\"full_config\"], values=\"latency\")\n", "print(r3)\n", "p = r3.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"latency (in s)\",\n", "    xlabel=\"Input length (in token)\",\n", "    title=\"Latency to first tokens for different model/hardware configurations\",\n", "    #loglog=True\n", ")\n", "p.plot()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The next diagram shows the token/s for different models"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = r[(r.gpu == \"A100\") & (r.code_version == \"ft53\") & ((r.output_len == 512) | (r.output_len == 1))]\n", "data[\"full_config\"] = data.apply(lambda e : f\"{e.gpu} / {e.tp} / {e.config}\", axis=1)\n", "data = data.pivot(index=[\"full_config\", \"input_len\"], columns=[\"output_len\"], values=\"latency\")\n", "tps = 1 / ((data.iloc[:,1] - data.iloc[:,0]) / 511)\n", "data[\"tps\"] = tps\n", "print(data)\n", "data = data.reset_index()\n", "data = data.pivot(index=[\"input_len\"], columns=[\"full_config\"], values=\"tps\")\n", "p = data.plot(\n", "    marker=\"o\",\n", "    linestyle=\"--\",\n", "    ylabel=\"Tokens per s\",\n", "    xlabel=\"Input length (in token)\",\n", "    title=\"Tokens per second after the first token for different models\",\n", ")\n", "p.plot()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "f9f85f796d01129d0dd105a088854619f454435301f6ffec2fea96ecbd9be4ac"}}}, "nbformat": 4, "nbformat_minor": 2}