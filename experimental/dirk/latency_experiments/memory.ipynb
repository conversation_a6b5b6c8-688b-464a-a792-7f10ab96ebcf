{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# FTM Memory\n", "\n", "This document allows calculating the GPU memory requirements when using the FT-based augment model.\n", "At this point, this document ignores memory attention."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fp16 = 2\n", "fp32 = 4\n", "bool_size = 1\n", "int_size = 4\n", "\n", "\n", "class ModelConfig:\n", "    def __init__(self, name, num_layers, vocab_size, head_num, size_per_head, tp, fmha: bool= True):\n", "        self.name = name\n", "        self.num_layers = num_layers\n", "        self.vocab_size = vocab_size\n", "        self.hidden_units = head_num * size_per_head\n", "        self.tp = tp\n", "\n", "        self.inter_size = self.hidden_units * 4\n", "        self.head_num = head_num\n", "        self.size_per_head = size_per_head\n", "        self.local_head_num = head_num // tp\n", "        self.local_hidden_units = self.local_head_num * self.size_per_head\n", "        self.fmha = fmha\n", "\n", "\n", "CodeGen6B = ModelConfig(\n", "    name=\"6B\", num_layers=33, vocab_size=51200, head_num=16, size_per_head=256, tp=1\n", ")\n", "CodeGen16B = ModelConfig(\n", "    name=\"16B\", num_layers=34, vocab_size=51200, head_num=24, size_per_head=256, tp=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_weights(model_config: ModelConfig):\n", "    df = pandas.DataFrame()\n", "    # from augment_weight.cc\n", "    df[\"pre_decoder_embedding\"] = [\n", "        model_config.vocab_size * model_config.hidden_units * fp16\n", "    ]\n", "    df[\"post_decoder_layer_norm_beta\"] = model_config.hidden_units * fp16\n", "    df[\"post_decoder_layer_norm_gamma\"] = model_config.hidden_units * fp16\n", "    df[\"post_decoder_embedding_kernel\"] = (\n", "        model_config.hidden_units * model_config.vocab_size * fp16\n", "    )\n", "    df[\"post_decoder_embedding_bias\"] = model_config.vocab_size * fp16\n", "    df[\"dummy\"] = 1 * fp16\n", "\n", "    # from augment_decoder_layer_weight.cc\n", "    df[\"pre_layernorm_weights_beta\"] = model_config.hidden_units * fp16 * model_config.num_layers\n", "    df[\"pre_layernorm_weights_gamma\"] = model_config.hidden_units * fp16 * model_config.num_layers\n", "    df[\"self_attention_weights_query_weight_kernel\"] = (\n", "        model_config.hidden_units\n", "        * 3\n", "        * model_config.hidden_units\n", "        // model_config.tp\n", "        * fp16 * model_config.num_layers\n", "    )\n", "    df[\"self_attention_weights_query_weight_bias\"] = (\n", "        3 * model_config.hidden_units * fp16 * model_config.num_layers\n", "    )\n", "    df[\"self_attention_weights_attention_output_weight_kernel\"] = (\n", "        model_config.hidden_units * model_config.hidden_units // model_config.tp * fp16 * model_config.num_layers\n", "    )\n", "    df[\"self_attention_weights_attention_output_weight_bias\"] = 0\n", "    df[\"ffn_weights_intermediate_weight_kernel\"] = (\n", "        model_config.hidden_units * model_config.inter_size * fp16 * model_config.num_layers\n", "    )\n", "    df[\"ffn_weights_intermediate_weight_bias\"] = (\n", "        model_config.inter_size // model_config.tp * fp16 * model_config.num_layers\n", "    )\n", "    df[\"ffn_weights_output_weight_kernel\"] = (\n", "        model_config.inter_size * model_config.hidden_units // model_config.tp * fp16 * model_config.num_layers\n", "    )\n", "    df[\"ffn_weights_output_weight_bias\"] = model_config.hidden_units * fp16 * model_config.num_layers\n", "    return df\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_buffer(\n", "    model_config: ModelConfig, max_seq_len, max_input_len, batch_size=1, beam_width=1\n", "):\n", "    df = pandas.DataFrame()\n", "    df[\"model_name\"] = [model_config.name]\n", "    df[\"max_seq_len\"] = [max_seq_len]\n", "    df[\"max_input_len\"] = [max_input_len]\n", "    batchxbeam = batch_size * beam_width\n", "    self_cache_size = (\n", "        (model_config.num_layers)\n", "        * batchxbeam\n", "        * max_seq_len\n", "        * model_config.hidden_units\n", "        // model_config.tp\n", "    )\n", "\n", "    # augment_model.cc\n", "    df[\"input_attention_mask\"] = [batchxbeam * max_seq_len * max_seq_len * fp16]\n", "    df[\"decoder_input_buf_\"] = batchxbeam * model_config.hidden_units * fp16\n", "    df[\"decoder_output_buf_\"] = batchxbeam * model_config.hidden_units * fp16\n", "    df[\"normed_decoder_output_buf_\"] = batchxbeam * model_config.hidden_units * fp16\n", "    df[\"logits_buf_\"] = batchxbeam * model_config.vocab_size * fp32\n", "    df[\"nccl_logits_buf_ \"] = batchxbeam * model_config.vocab_size * fp32\n", "    df[\"cum_log_probs_\"] = batchxbeam * fp32\n", "    df[\"finished_buf\"] = batchxbeam * bool_size\n", "    df[\"sequence_lengths\"] = batchxbeam * int_size\n", "\n", "    df[\"key_cache\"] = self_cache_size * fp16\n", "    df[\"value_cache\"] = self_cache_size * fp16\n", "    if beam_width > 1:\n", "        df[\"cache_indirections_0\"] = batchxbeam * max_seq_len * int_size\n", "        df[\"cache_indirections_1\"] = batchxbeam * max_seq_len * int_size\n", "\n", "    df[\"tiled_input_ids_buf\"] = batchxbeam * max_input_len * int_size\n", "    df[\"tiled_input_lengths_buf_\"] = batchxbeam * int_size\n", "    df[\"tiled_total_padding_count\"] = batchxbeam * int_size\n", "\n", "    df[\"transposed_output_ids_buf\"] = batchxbeam * max_seq_len * int_size\n", "    df[\"output_ids_buf\"] = batchxbeam * max_seq_len * int_size\n", "    df[\"parent_ids_buf\"] = batchxbeam * max_seq_len * int_size\n", "    df[\"seq_limit_len_\"] = batch_size * int_size\n", "    df[\"masked_tokens\"] = batchxbeam * max_seq_len\n", "\n", "    df[\"start_ids_buf\"] = batch_size * int_size\n", "    df[\"end_ids_buf\"] = batch_size * int_size\n", "\n", "    df[\"context_decoder_input_buf\"] = (\n", "        batchxbeam * max_input_len * model_config.hidden_units * fp16\n", "    )\n", "    df[\"context_decoder_output_buf\"] = (\n", "        batchxbeam * max_input_len * model_config.hidden_units * fp16\n", "    )\n", "    df[\"output_log_probs_buf\"] = batchxbeam * max_seq_len * fp32\n", "\n", "    df[\"generation_should_stop\"] = bool_size\n", "\n", "    # augment_context_decoder.cc\n", "    df[\"context_decoder_normed_input\"] = (\n", "        batchxbeam * max_input_len * model_config.hidden_units * fp16\n", "    )\n", "    df[\"context_self_attn_output\"] = (\n", "        batchxbeam * max_input_len * model_config.hidden_units * fp16\n", "    )\n", "    df[\"context_ffn_output\"] = (\n", "        batchxbeam * max_input_len * model_config.hidden_units * fp16\n", "    )\n", "    df[\"context_decoder_layer_output\"] = (\n", "        batchxbeam * max_input_len * model_config.hidden_units * fp16\n", "    )\n", "\n", "    # augment_decoder.cc\n", "    df[\"decoder_normed_input\"] = batchxbeam * model_config.hidden_units * fp16\n", "    df[\"self_attn_output\"] = batchxbeam * model_config.hidden_units * fp16\n", "    df[\"ffn_output\"] = batchxbeam * model_config.hidden_units * fp16\n", "    df[\"decoder_layer_output\"] = batchxbeam * model_config.hidden_units * fp16\n", "\n", "    # DecoderSelfAttention\n", "    df[\"qkv_buf\"] = batchxbeam * 3 * model_config.local_hidden_units * fp16\n", "    df[\"context_buf\"] = batchxbeam * model_config.local_hidden_units * fp16\n", "\n", "    # GptContextAttentionLayer.cc\n", "    df[\"context_qkv_buf_\"] = (\n", "        batchxbeam * 3 * max_input_len * model_config.local_hidden_units * fp16\n", "    )\n", "    df[\"context_q_buf_2_\"] = (\n", "        batchxbeam * max_input_len * model_config.local_hidden_units * fp16\n", "    )\n", "    df[\"context_k_buf_2_\"] = (\n", "        batchxbeam * 3 * max_input_len * model_config.local_hidden_units * fp16\n", "    )\n", "    df[\"context_v_buf_2_\"] = (\n", "        batchxbeam * 3 * max_input_len * model_config.local_hidden_units * fp16\n", "    )\n", "\n", "    if model_config.fmha:\n", "        df[\"context_qk_buf_\"] = 0\n", "    else:\n", "        df[\"context_qk_buf_\"] = (\n", "            batchxbeam * model_config.local_head_num * max_input_len * max_input_len * fp16\n", "        )\n", "    df[\"context_qkv_buf_2\"] = (\n", "        batchxbeam * max_input_len * model_config.local_hidden_units * fp16\n", "    )\n", "    df[\"context_qkv_buf_3\"] = (\n", "        batchxbeam * max_input_len * model_config.local_hidden_units * fp16\n", "    )\n", "    return df\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w = get_weights(CodeGen6B)\n", "b = pandas.concat(\n", "    pandas.concat(\n", "        [get_buffer(CodeGen6B, max_seq_len=i + 64, max_input_len=i), w], axis=1\n", "    )\n", "    for i in [16, 32, 64, 128, 256, 512, 1024, 2048, 4098, 8 * 1024, 16 * 1024]\n", ")\n", "b[\"all_memory\"] = b[b.columns[3:]].sum(axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b[b.columns[3:]][-1:].transpose().sort_values(by=0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["w = get_weights(CodeGen6B)\n", "print(\"Weights\", w.sum(axis=1))\n", "input_len = 64 * 1024\n", "b = pandas.concat([get_buffer(CodeGen6B, max_seq_len=input_len + 64, max_input_len=input_len), w], axis=1)\n", "b[\"all_memory\"] = b[b.columns[3:]].sum(axis=1)\n", "b2 = b[b.columns[3:]]\n", "print(b[\"all_memory\"])\n", "b2.transpose().sort_values(by=0)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["20121774496 / (1024 * 1024 * 1024)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}