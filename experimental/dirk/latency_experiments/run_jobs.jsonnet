local createJob = function(jobName,
                           gpuCount,
                           parallelism,
                           imageTag,
                           jobsDir='/mnt/efs/augment/user/dirk/latency_jobs',
                           augmentNfsPvc='efs-augment-claim',
                           namespace='dev-dirk')
  {
    apiVersion: 'batch/v1',
    kind: 'Job',
    metadata: {
      name: jobName,
      namespace: namespace,
    },
    spec: {
      ttlSecondsAfterFinished: 300,
      parallelism: parallelism,
      template: {
        spec: {
          securityContext: {
            runAsUser: 1000,
            fsGroup: 1000,
          },
          containers: [
            {
              name: 'runner',
              image: imageTag,
              args: [
                '--jobs-dir',
                jobsDir,
              ],
              volumeMounts: [
                {
                  name: 'persistent-storage',
                  mountPath: '/mnt/efs/augment',
                },
                {
                  mountPath: '/dev/shm',
                  name: 'dshm',
                },
              ],
              resources: {
                limits: {
                  cpu: 6 * gpuCount,
                  'nvidia.com/gpu': gpuCount,
                  memory: '%sGi' % (24 * gpuCount),
                },
              },
              env: [
                {
                  name: 'WORKER_NAME',
                  valueFrom: {
                    fieldRef: {
                      fieldPath: 'metadata.name',
                    },
                  },
                },
              ],
            },
          ],
          volumes: [
            {
              name: 'persistent-storage',
              persistentVolumeClaim: {
                claimName: augmentNfsPvc,
              },
            },
            {
              name: 'dshm',
              emptyDir: {
                medium: 'Memory',
              },
            },
          ],
          restartPolicy: 'Never',
        },
      },

      backoffLimit: 4,
    },
  };
function(imageTag='829650725646.dkr.ecr.us-west-2.amazonaws.com/latency-eval:dev', aws=true)
  if (aws) then
    [
      createJob('latency-eval', gpuCount=1, parallelism=2, imageTag=imageTag),
      //createJob('latency-eval-mg', gpuCount=4, parallelism=1, imageTag=imageTag) {
      //  spec: super.spec {
      //    template: super.template {
      //      spec: super.spec {
      //        tolerations: [{
      //          key: 'gpu',
      //          operator: 'Equal',
      //          value: '4a10',
      //          effect: 'NoSchedule',
      //        }],
      //      },
      //    },
      //  },
      //},
    ] else [
    createJob('dirk-latency-eval-2-1',
              gpuCount=2,
              parallelism=1,
              imageTag=imageTag,
              jobsDir='/mnt/efs/augment/user/dirk/latency_jobs_cw',
              augmentNfsPvc='aug-cw-las1',
              namespace='tenant-augment-eng') {
      spec: super.spec {
        template: super.template {
          spec: super.spec {
            affinity: {
              nodeAffinity: {
                requiredDuringSchedulingIgnoredDuringExecution: {
                  nodeSelectorTerms: [{
                    matchExpressions: [
                      {
                        key: 'topology.kubernetes.io/region',
                        operator: 'In',
                        values: [
                          'LAS1',
                        ],
                      },
                      {
                        key: 'gpu.nvidia.com/class',
                        operator: 'In',
                        values: [
                          'A100_NVLINK_80GB',
                        ],
                      },
                    ],
                  }],
                },
              },
            },
          },
        },
      },
    },
  ]
