# ECR repository for the latency experiment image
apiVersion: ecr.services.k8s.aws/v1alpha1
kind: Repository
metadata:
  name: latency-eval
  namespace: dev-dirk
spec:
  name: latency-eval
  imageScanningConfiguration:
    scanOnPush: false
  imageTagMutability: MUTABLE
  lifecyclePolicy: '{"rules":[{"rulePriority":1,"description":"Expire images older than 1 days","selection":{"tagStatus":"untagged","countType":"sinceImagePushed","countUnit":"days","countNumber":1},"action":{"type":"expire"}}]}'
