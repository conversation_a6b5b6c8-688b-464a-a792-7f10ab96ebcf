local input_lens = [1, 16, 32, 64, 96, 128, 128 + 64, 256, 384, 512, 1024, 2048, 4096];
local output_lens = [1, 16, 32, 48, 64, 80, 96, 128, 256, 384, 512];
local instance_configs = std.flatMap(function(ic) [[ic[0], gpu_count] for gpu_count in ic[1]], [
  ['codegen-350M-multi.json', [1]],
  ['codegen-2B-multi.json', [1, 2, 4]],
  ['codegen-2B.2022-07-22.json', [1]],
  ['codegen-6B-multi.json', [1, 2, 4]],
  ['codegen-6B.2022-11-12.json', [1]],
  ['codegen-16B-multi.json', [2, 4]],
]);
local gpus = ['NVIDIA A10G'];
local configs = [
  {
    request_input_len: input_len,
    request_output_len: output_len,
    instance_config: 'models/inference/configs/%s' % ic[0],
    tensor_parallelism: ic[1],
    gpu: gpu,
  }
  for input_len in input_lens
  for output_len in output_lens
  for ic in instance_configs
  for gpu in gpus
];

local a100_input_lens = [1, 16, 32, 64, 96, 128, 128 + 64, 256, 384, 512, 1024, 2048, 4096];
local a100_output_lens = [1, 16, 32, 48, 64, 80, 96, 128, 256, 384, 512];
local a100_instance_configs = std.flatMap(function(ic) [[ic[0], gpu_count] for gpu_count in ic[1]], [
  ['codegen-2B-multi.json', [1]],
  ['codegen-6B-multi.json', [1, 2, 4, 8]],
  ['codegen-16B-multi.json', [1, 2, 4, 8]],
]);
local a100_gpus = ['A100'];
local a100_configs = [
  {
    request_input_len: input_len,
    request_output_len: output_len,
    instance_config: 'models/inference/configs/%s' % ic[0],
    tensor_parallelism: ic[1],
    gpu: gpu,
    code_version: 'ft53',
  }
  for input_len in a100_input_lens
  for output_len in a100_output_lens
  for ic in a100_instance_configs
  for gpu in a100_gpus
];

local ft53_input_lens = [256, 384, 512, 1024, 2048, 1024 + 2048, 4096, 5 * 1024, 6 * 1024];
local ft53_instance_configs = std.flatMap(function(ic) [[ic[0], gpu_count] for gpu_count in ic[1]], [
  ['codegen-350M-multi.json', [1]],
  ['codegen-2B-multi.json', [1]],
  ['codegen-2B.2022-07-22.json', [1]],
  ['codegen-6B-multi.json', [1]],
  ['codegen-6B.2022-11-12.json', [1]],
  ['codegen-16B-multi.json', []],
]);
local ft53_configs = [
  {
    request_input_len: input_len,
    request_output_len: output_len,
    instance_config: 'models/inference/configs/%s' % ic[0],
    tensor_parallelism: ic[1],
    gpu: gpu,
    code_version: 'ft53',
  }
  for input_len in ft53_input_lens
  for output_len in output_lens
  for ic in ft53_instance_configs
  for gpu in gpus
];


local ft53_a100_input_lens = [256, 384, 512, 1024, 2048, 4096, 5 * 1024, 6 * 1024, 7 * 1024, 8 * 1024, 10 * 1024, 12 * 1024, 14 * 1024, 16 * 1024, 20 * 1024, 24 * 1024, 28 * 1024, 32 * 1024];
local ft53_a100_output_lens = [1, 16, 32, 48, 64];
local ft53_a100_instance_configs = std.flatMap(function(ic) [[ic[0], gpu_count] for gpu_count in ic[1]], [
  ['codegen-350M-multi.json', []],
  ['codegen-2B-multi.json', []],
  ['codegen-2B.2022-07-22.json', []],
  ['codegen-6B-multi.json', [1, 2]],
  ['codegen-6B.2022-11-12.json', []],
  ['codegen-16B-multi.json', [1, 2]],
]);
local ft53_a100_configs = [
  {
    request_input_len: input_len,
    request_output_len: output_len,
    instance_config: 'models/inference/configs/%s' % ic[0],
    tensor_parallelism: ic[1],
    gpu: gpu,
    code_version: 'ft53',
  }
  for input_len in ft53_a100_input_lens
  for output_len in ft53_a100_output_lens
  for ic in ft53_a100_instance_configs
  for gpu in a100_gpus
];


local generateJobFile = function(config, cur)
  local hash = std.md5(std.manifestJson(config));
  local obj = config;
  local filename = '%s.json' % hash;
  cur {
    [filename]: obj,
  };

std.foldr(generateJobFile, ft53_a100_configs, {})
