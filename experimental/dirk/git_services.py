import subprocess
from collections import Counter
import argparse

def analyze_git_commits(repo_path, subdirectories, since):
    """
    Analyzes git commits in a repository, focusing on changes in specified subdirectories
    within a given time frame.

    Args:
        repo_path (str): The path to the git repository.
        subdirectories (list): A list of subdirectory names to filter by.
        since (str): A string representing the time frame to consider (e.g., "3 months ago", "1 year ago").

    Returns:
        tuple: A tuple containing:
            - commit_list (list): A list of commit SHAs that touched the specified subdirectories, ordered chronologically.
            - author_counts (list): A list of tuples, where each tuple contains an author's name and their commit count,
                                    ordered by commit count in descending order.
    """
    try:
        # Construct the git log command
        command = [
            "git",
            "log",
            "--since",
            since,
            "--pretty=format:%H %ae",  # Output commit SHA and author email
            "--name-only",              # Include the list of files changed
            "--",
            *subdirectories             # Specify the subdirectories to filter
        ]

        print(" ".join(command))
        # Execute the git log command
        process = subprocess.run(command, cwd=repo_path, capture_output=True, text=True, check=True)
        output = process.stdout.strip()
        lines = output.split('\n')

        authors = Counter()
        for line in lines:
            if line and not line.startswith(" "):  # Line contains commit SHA and author
                parts = line.split()
                if len(parts) >= 2:
                    authors[parts[1]] += 1

        # Order authors by commit count
        ordered_authors = authors.most_common()

        return ordered_authors

    except subprocess.CalledProcessError as e:
        print(f"Error executing git command: {e}")
        return [], []
    except FileNotFoundError:
        print(f"Error: Git command not found. Make sure Git is installed and in your PATH.")
        return [], []
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return [], []

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze Git commits touching specified subdirectories.")
    parser.add_argument(
        "--repo",
        default=".",
        help="Path to the Git repository (default: current directory).",
    )
    parser.add_argument(
        "--subdirectories",
        default="services,base",
        help="Comma-separated list of subdirectories to analyze (default: services,base).",
    )
    parser.add_argument(
        "--since",
        default="6 months ago",
        help="Time frame to consider (e.g., '3 months ago', '1 year ago', default: 3 months ago).",
    )

    args = parser.parse_args()

    repo_path = args.repo
    subdirectories_to_analyze = [subdir.strip() for subdir in args.subdirectories.split(',')]
    time_frame = args.since

    authors = analyze_git_commits(repo_path, subdirectories_to_analyze, time_frame)

    if authors:
        print(f"\nAuthors ordered by commit count (touching '{subdirectories_to_analyze[0]}' or '{subdirectories_to_analyze[1]}'):")
        for author, count in authors:
            print(f"- {author}: {count} commits")
    else:
        print("No author statistics to display.")