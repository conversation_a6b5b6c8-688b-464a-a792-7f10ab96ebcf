{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ls -l /tmp/repos/google_lightweight_mmm"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy\n", "from pathlib import Path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_index():\n", "    result = []\n", "    for i in range(10000):\n", "        fname = Path(f\"/tmp/repos/google_lightweight_mmm/{i}.npy\")\n", "        if not fname.exists():\n", "            break\n", "        a = numpy.load(fname)\n", "        result.append(a.astype(numpy.float32))\n", "    return numpy.concatenate(result)\n", "r = load_index()\n", "print(r.shape)\n", "r"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_query(q):\n", "    return numpy.load(f\"/tmp/repos/google_lightweight_mmm/{q}_query.npy\").astype(numpy.float32)\n", "\n", "q = load_query(0)\n", "print(q.shape)\n", "q"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ip_result = numpy.matmul(q, r.transpose())\n", "print(ip_result)\n", "a = numpy.flip(numpy.argsort(ip_result), 1)\n", "print(a.shape)\n", "a"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_k = a[:,:32]\n", "top_k"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(38, ip_result[0][38])\n", "print(37, ip_result[0][37])\n", "print(39, ip_result[0][39])\n", "print(57, ip_result[0][57])\n", "print(53, ip_result[0][53])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r[38]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}