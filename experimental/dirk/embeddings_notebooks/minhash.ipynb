{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# experiments for minhash"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import logging\n", "import typing\n", "import random"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def generate_hash_functions(k, max_value):\n", "    hash_funcs = []\n", "    for _ in range(k):\n", "        a = random.randint(1, max_value)\n", "        b = random.randint(0, max_value)\n", "        p = next_prime(max_value)\n", "        hash_funcs.append(lambda x, a=a, b=b, p=p: (a * x + b) % p)\n", "    return hash_funcs\n", "\n", "\n", "def next_prime(n):\n", "    # Simple function to find the next prime >= n\n", "    def is_prime(num):\n", "        if num < 2:\n", "            return False\n", "        for i in range(2, int(num**0.5) + 1):\n", "            if num % i == 0:\n", "                return False\n", "        return True\n", "\n", "    while not is_prime(n):\n", "        n += 1\n", "    return n\n", "\n", "\n", "num_hashes = 32\n", "\n", "hash_functions = generate_hash_functions(num_hashes, max_value=1000)\n", "\n", "\n", "def divide_into_bands(signature, num_bands):\n", "    band_size = len(signature) // num_bands\n", "    bands = [\n", "        tuple(signature[i * band_size : (i + 1) * band_size]) for i in range(num_bands)\n", "    ]\n", "    return bands\n", "\n", "\n", "def compute_minhash_signature(set_elements: typing.Sequence[str], hash_funcs):\n", "    signature = []\n", "    for hash_func in hash_funcs:\n", "        min_hash = float(\"inf\")\n", "        for element in set_elements:\n", "            hashed = hash_func(hash(element))\n", "            min_hash = min(min_hash, hashed)\n", "        signature.append(min_hash)\n", "    return signature"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["class LshIndex:\n", "    def __init__(self):\n", "        self.index = {}\n", "\n", "    def add(self, set_id: str, signature: typing.Sequence[int], num_bands: int):\n", "        logging.info(\n", "            \"Adding %s to index: signature %s, num_bands %s\",\n", "            set_id,\n", "            signature,\n", "            num_bands,\n", "        )\n", "        bands = divide_into_bands(signature, num_bands)\n", "        for band_id, band in enumerate(bands):\n", "            hash_value = hash(band)\n", "            self.index.setdefault((band_id, hash_value), set()).add(set_id)\n", "\n", "    def find_candidates(\n", "        self, set_id: str, signature: typing.Sequence[int], num_bands: int\n", "    ):\n", "        logging.info(\n", "            \"Finding candidates for %s: signature %s, num_bands %s, index size %s\",\n", "            set_id,\n", "            signature,\n", "            num_bands,\n", "            len(self.index),\n", "        )\n", "        candidates = set()\n", "        bands = divide_into_bands(signature, num_bands)\n", "        for band_id, band in enumerate(bands):\n", "            hash_value = hash(band)\n", "            bucket = self.index.get((band_id, hash_value), set())\n", "            candidates.update(bucket)\n", "        candidates.discard(set_id)  # Remove the set itself from candidates\n", "        logging.info(\"Found candidates: %s\", candidates)\n", "        return candidates"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["index = LshIndex()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["a = tuple([str(a) for a in range(1000)])\n", "sig = compute_minhash_signature(a, hash_functions)\n", "index.add(\"a\", sig, 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = list(a)\n", "b[0] = str(1001)\n", "sig = compute_minhash_signature(b, hash_functions)\n", "c = index.find_candidates(\"b\", sig, 4)\n", "print(c)\n", "assert \"a\" in c"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["c = tuple([str(a) for a in range(1000, 2000)])\n", "sig = compute_minhash_signature(c, hash_functions)\n", "candidates = index.find_candidates(\"c\", sig, 4)\n", "print(candidates)\n", "assert len(candidates) == 0"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 2}