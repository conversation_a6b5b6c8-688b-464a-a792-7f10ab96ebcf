"""Example how to use treesitter in Bazel."""

import argparse
import pathlib

from tree_sitter_language_pack import get_parser


def _run(args: argparse.Namespace):
    parser = get_parser(args.language)  # type: ignore

    content = pathlib.Path(args.file).read_bytes()
    tree = parser.parse(content)

    root_node = tree.root_node
    print(root_node)


def main():
    """Main entry function."""
    parser = argparse.ArgumentParser()
    parser.add_argument("file")
    parser.add_argument("--language", default="python")
    args = parser.parse_args()
    _run(args)


if __name__ == "__main__":
    main()
