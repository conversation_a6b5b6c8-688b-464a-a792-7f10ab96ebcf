# This code is compatible with Terraform 4.25.0 and versions that are backwards compatible to 4.25.0.
# For information about validating this Terraform code, see https://developer.hashicorp.com/terraform/tutorials/gcp-get-started/google-cloud-platform-build#format-and-validate-the-configuration

resource "google_compute_instance" "dev-dirk2" {
  boot_disk {
    auto_delete = true
    device_name = "instance-1"

    initialize_params {
      image = "projects/ubuntu-os-cloud/global/images/ubuntu-2004-focal-v20230817"  # pragma: allowlist secret
      size  = 500
      type  = "pd-balanced"
    }

    mode = "READ_WRITE"
  }

  can_ip_forward      = false
  deletion_protection = false
  enable_display      = false

  guest_accelerator {
    count = 1
    type  = "projects/system-services-dev/zones/us-central1-a/acceleratorTypes/nvidia-l4"
  }

  labels = {
    goog-ec-src = "vm_add-tf"
  }

  machine_type = "g2-standard-16"

  metadata = {
    ssh-keys = "dirk:ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDJC20qnPPD/fra4OxfeW3lG/SpepigalW2WopyEVkkuT701psEui22c+YHUKE4EYq7gQbPW0sYGsT4K2NgoUIN9YXRxN13miBM5T23my4ibfXqWXeSfDiKwxiedKwIs+HBVO0IcRDVS9ekeVjAxz1UrX7+n0NdQHYuF3eUrmpBF6mnvqpl5LsiHTneHuaRudGud8KpNn3hK1zbW21a5uosBbhrhg56LJU/sN78h2BKACfpbLkqUdbvqckykFgZzJ587Xiff3uRncGXo4WCsMqXfKTtk7Ow9A4M/pKw5lACzSECq1ZqqX3LE983PCCDhe69plf4MHAqL6qVAvKO4BNXk4P6o8ntAY4xrnNbxA3UMU/74x881Yc7SpXVTKLJZ9ycf5nq5XIPRtuXW1jYM/OuwGuOHi+iLiB7aMOY9IC3YFTiHmr7V8l3E3V23Pxi99bcma5Mr0SBtVxOJ0eNFGFOO1ErcEpzZluhej4wmEJbH5SgB0Flzi7ZvDYtF6r9cak= dirk"
  }

  name = "dev-dirk2"

  network_interface {
    access_config {
      network_tier = "PREMIUM"
    }

    subnetwork = "projects/system-services-dev/regions/us-central1/subnetworks/default"
  }

  scheduling {
    automatic_restart   = true
    on_host_maintenance = "TERMINATE"
    preemptible         = false
    provisioning_model  = "STANDARD"
  }

  service_account {
    email  = "<EMAIL>"
    scopes = ["https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/logging.write", "https://www.googleapis.com/auth/monitoring.write", "https://www.googleapis.com/auth/service.management.readonly", "https://www.googleapis.com/auth/servicecontrol", "https://www.googleapis.com/auth/trace.append"]
  }

  shielded_instance_config {
    enable_integrity_monitoring = true
    enable_secure_boot          = false
    enable_vtpm                 = true
  }

  zone = "us-central1-a"
}
