
# K8S cluster

```
gcloud beta container --project "system-services-dev" clusters create "dev-1" --region "us-central1" --no-enable-basic-auth --cluster-version "1.27.3-gke.100" --release-channel "regular" --machine-type "e2-standard-16" --image-type "COS_CONTAINERD" --disk-type "pd-balanced" --disk-size "100" --metadata disable-legacy-endpoints=true --scopes "https://www.googleapis.com/auth/devstorage.read_only","https://www.googleapis.com/auth/logging.write","https://www.googleapis.com/auth/monitoring","https://www.googleapis.com/auth/servicecontrol","https://www.googleapis.com/auth/service.management.readonly","https://www.googleapis.com/auth/trace.append" --spot --num-nodes "1" --logging=SYSTEM,WORKLOAD --monitoring=SYSTEM --enable-private-nodes --master-ipv4-cidr "**********/28" --enable-master-global-access --enable-ip-alias --network "projects/system-services-dev/global/networks/default" --subnetwork "projects/system-services-dev/regions/us-central1/subnetworks/default" --no-enable-intra-node-visibility --default-max-pods-per-node "110" --enable-autoscaling --total-min-nodes "0" --total-max-nodes "3" --location-policy "ANY" --security-posture=standard --workload-vulnerability-scanning=disabled --no-enable-master-authorized-networks --addons HorizontalPodAutoscaling,HttpLoadBalancing,GcePersistentDiskCsiDriver,ConfigConnector,GcpFilestoreCsiDriver --enable-autoupgrade --enable-autorepair --max-surge-upgrade 1 --max-unavailable-upgrade 0 --binauthz-evaluation-mode=DISABLED --enable-managed-prometheus --workload-pool "system-services-dev.svc.id.goog" --enable-shielded-nodes
```

or
```
gcloud beta container --project "system-services-dev" clusters create "dev-2" --region "us-central1" --no-enable-basic-auth --cluster-version "1.27.3-gke.100" --release-channel "regular" --machine-type "e2-standard-16" --image-type "COS_CONTAINERD" --disk-type "pd-balanced" --disk-size "100" --metadata disable-legacy-endpoints=true --scopes "https://www.googleapis.com/auth/devstorage.read_only","https://www.googleapis.com/auth/logging.write","https://www.googleapis.com/auth/monitoring","https://www.googleapis.com/auth/servicecontrol","https://www.googleapis.com/auth/service.management.readonly","https://www.googleapis.com/auth/trace.append" --spot --num-nodes "1" --logging=SYSTEM,WORKLOAD --monitoring=SYSTEM,API_SERVER,SCHEDULER,CONTROLLER_MANAGER,STORAGE,POD,DEPLOYMENT,STATEFULSET,DAEMONSET,HPA --enable-ip-alias --network "projects/system-services-dev/global/networks/default" --subnetwork "projects/system-services-dev/regions/us-central1/subnetworks/default" --no-enable-intra-node-visibility --default-max-pods-per-node "110" --enable-autoscaling --total-min-nodes "0" --total-max-nodes "3" --location-policy "ANY" --security-posture=standard --workload-vulnerability-scanning=disabled --enable-dataplane-v2 --no-enable-master-authorized-networks --addons HorizontalPodAutoscaling,HttpLoadBalancing,GcePersistentDiskCsiDriver,ConfigConnector,GcpFilestoreCsiDriver --enable-autoupgrade --enable-autorepair --max-surge-upgrade 1 --max-unavailable-upgrade 0 --binauthz-evaluation-mode​=DISABLED --enable-managed-prometheus --workload-pool "system-services-dev.svc.id.goog" --enable-shielded-nodes
```

# Config Connector

```
gcloud iam service-accounts create configconnector-sa
gcloud projects add-iam-policy-binding "system-services-dev" \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/editor"
gcloud iam service-accounts add-iam-policy-binding \
    <EMAIL> \
    --member="serviceAccount:system-services-dev.svc.id.goog[cnrm-system/cnrm-controller-manager]" \
    --role="roles/iam.workloadIdentityUser"
```

# DNS Zone

```
gcloud dns --project=system-services-dev managed-zones create gcp-central1-zone --description="" --dns-name="gcp-us-central1.eng.augmentcode.com." --visibility="public" --dnssec-state="off" --log-dns-queries
```


# Persistent Disk CSI driver

```
gcloud container clusters update dev-1 \
   --update-addons=GcePersistentDiskCsiDriver=ENABLED
```


# Cert Manager


see https://cert-manager.io/docs/configuration/acme/dns01/google/

```
PROJECT_ID=system-services-dev
gcloud iam service-accounts create dns01-solver --display-name "dns01-solver"
gcloud projects add-iam-policy-binding $PROJECT_ID \
   --member serviceAccount:dns01-solver@$PROJECT_ID.iam.gserviceaccount.com \
   --role roles/dns.admin
gcloud iam service-accounts add-iam-policy-binding \
    --role roles/iam.workloadIdentityUser \
    --member "serviceAccount:$PROJECT_ID.svc.id.goog[cert-manager/cert-manager]" \
    dns01-solver@$PROJECT_ID.iam.gserviceaccount.com
```


# Create GPU node pool

Note: When creating a node pool in the GKE UI, the nodes appear to not detect the GPU.

## Spot Pool
```
gcloud container node-pools create gpu-l4-pool3-spot \
  --accelerator type=nvidia-l4,count=1,gpu-driver-version=latest \
  --machine-type g2-standard-12 \
  --location-policy=ANY \
  --region us-central1 --cluster dev-2 \
  --node-locations us-central1-a,us-central1-b,us-central1-c \
  --num-nodes 1 --total-min-nodes 0 --total-max-nodes 4 --enable-autoscaling \
  --spot
```

## Non-Spot Pool
```
gcloud container node-pools create gpu-l4-pool3-nospot \
  --accelerator type=nvidia-l4,count=1,gpu-driver-version=latest \
  --machine-type g2-standard-12 \
  --location-policy=ANY \
  --region us-central1 --cluster dev-2 \
  --node-locations us-central1-a,us-central1-b,us-central1-c \
  --num-nodes 1 --total-min-nodes 0 --total-max-nodes 4 --enable-autoscaling
```

gcloud container node-pools create cpu-pool1-nospot \
  --machine-type e2-standard-16 \
  --location-policy=ANY \
  --region us-central1 --cluster dev-2 \
  --node-locations us-central1-a,us-central1-b,us-central1-c \
  --num-nodes 1 --total-min-nodes 0 --total-max-nodes 4 --enable-autoscaling
```

# Create H100 GPU pool

```
gcloud beta container --project "system-services-dev" node-pools create "gpu-h100-pool3" --cluster "dev-2" --region "us-central1" --node-version "1.27.3-gke.100" --machine-type "a3-highgpu-8g" --accelerator "type=nvidia-h100-80gb,count=8,gpu-driver-version=latest" --image-type "COS_CONTAINERD" --disk-type "pd-ssd" --disk-size "3000" --metadata disable-legacy-endpoints=true --node-taints gpu=8h100:NoSchedule --scopes "https://www.googleapis.com/auth/devstorage.read_only","https://www.googleapis.com/auth/logging.write","https://www.googleapis.com/auth/monitoring","https://www.googleapis.com/auth/servicecontrol","https://www.googleapis.com/auth/service.management.readonly","https://www.googleapis.com/auth/trace.append" --num-nodes "2" --enable-autoupgrade --enable-autorepair --max-surge-upgrade 0 --max-unavailable-upgrade 1 --node-locations "us-central1-a"
```

# Create multi-GPU node pool

```
gcloud container node-pools create gpu-l4-multi-pool3-spot \
  --accelerator type=nvidia-l4,count=4,gpu-driver-version=latest \
  --machine-type g2-standard-48 \
  --location-policy=ANY \
  --region us-central1 --cluster dev-2 \
  --node-locations us-central1-a,us-central1-b,us-central1-c \
  --num-nodes 1 --total-min-nodes 0 --total-max-nodes 4 --enable-autoscaling \
  --node-taints gpu=4l4:NoSchedule \
  --spot
````

# T4 GPU

```
gcloud container node-pools create "gpu-t4-pool-spot" \
  --machine-type "n1-standard-16" \
  --accelerator "type=nvidia-tesla-t4,count=4,gpu-driver-version=latest" \
  --location-policy=ANY \
  --region us-central1 --cluster dev-2 \
  --node-locations us-central1-a,us-central1-b,us-central1-c \
  --num-nodes 1 --total-min-nodes 0 --total-max-nodes 4 --enable-autoscaling \
  --spot
```

```
gcloud container node-pools create "gpu-t4-pool-nospot" \
  --machine-type "n1-standard-16" \
  --accelerator "type=nvidia-tesla-t4,count=4,gpu-driver-version=latest" \
  --location-policy=ANY \
  --region us-central1 --cluster dev-2 \
  --node-locations us-central1-a,us-central1-b,us-central1-c \
  --num-nodes 1 --total-min-nodes 0 --total-max-nodes 4 --enable-autoscaling
```
