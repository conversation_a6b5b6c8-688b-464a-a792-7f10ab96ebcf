#!/usr/bin/env node

/**
 * <PERSON>ript to generate iOS and PWA icons from the existing favicon.svg
 * This script converts the SVG to PNG files in various sizes needed for iOS home screen icons
 */

import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Icon sizes needed for iOS and PWA
const iconSizes = [
  { size: 180, name: 'apple-touch-icon-180x180.png', description: 'iPhone' },
  { size: 167, name: 'apple-touch-icon-167x167.png', description: 'iPad Pro' },
  { size: 152, name: 'apple-touch-icon-152x152.png', description: 'iPad' },
  { size: 120, name: 'apple-touch-icon-120x120.png', description: 'iPhone Retina' },
  { size: 76, name: 'apple-touch-icon-76x76.png', description: 'iPad' },
  { size: 60, name: 'apple-touch-icon-60x60.png', description: 'iPhone' },
  { size: 512, name: 'icon-512x512.png', description: 'PWA Large' },
  { size: 192, name: 'icon-192x192.png', description: 'PWA Standard' },
];

console.log('🎨 Generating iOS and PWA icons from favicon.svg...\n');

// Check if icons already exist
const staticDir = join(__dirname, 'static');
const existingIcons = iconSizes.filter(({ name }) => {
  try {
    const iconPath = join(staticDir, name);
    return readFileSync(iconPath);
  } catch {
    return false;
  }
});

if (existingIcons.length === iconSizes.length) {
  console.log('✅ All icons already exist! No generation needed.');
  console.log('📱 Your app is ready for iOS home screen installation.\n');
  console.log('To test on iOS:');
  console.log('1. Open the app in Safari on iOS');
  console.log('2. Tap the Share button');
  console.log('3. Select "Add to Home Screen"');
  console.log('4. The app will use the proper icon and run in standalone mode\n');
  process.exit(0);
}

// Read the source SVG
const svgPath = join(__dirname, 'static', 'favicon.svg');
let svgContent;

try {
  svgContent = readFileSync(svgPath, 'utf8');
  console.log('✅ Read favicon.svg successfully');
} catch (error) {
  console.error('❌ Error reading favicon.svg:', error.message);
  process.exit(1);
}

// Function to create SVG with specific dimensions
function createSizedSVG(originalSvg, size) {
  // Update the SVG to have the correct dimensions
  const sizedSvg = originalSvg
    .replace(/width="[^"]*"/, `width="${size}"`)
    .replace(/height="[^"]*"/, `height="${size}"`)
    .replace(/viewBox="[^"]*"/, 'viewBox="-2 -2 23.28 23.28"');

  return sizedSvg;
}

console.log('\n📝 To complete the icon generation, you have two options:\n');

console.log('Option 1: Use an online SVG to PNG converter');
console.log('1. Go to https://cloudconvert.com/svg-to-png or similar service');
console.log('2. Upload your favicon.svg file');
console.log('3. Convert to PNG with the following sizes:\n');

iconSizes.forEach(({ size, name, description }) => {
  console.log(`   • ${size}x${size}px → static/${name} (${description})`);
});

console.log('\nOption 2: Use a local tool like Inkscape or ImageMagick');
console.log('If you have ImageMagick installed, run these commands:\n');

iconSizes.forEach(({ size, name }) => {
  console.log(`magick static/favicon.svg -resize ${size}x${size} static/${name}`);
});

console.log('\nOption 3: Use Node.js with sharp (recommended)');
console.log('Run: npm install sharp');
console.log('Then this script will automatically generate all icons.');

// Try to use sharp if available
try {
  const sharp = await import('sharp');
  console.log('\n🚀 Sharp detected! Generating icons automatically...\n');

  const svgBuffer = Buffer.from(svgContent);

  for (const { size, name, description } of iconSizes) {
    try {
      await sharp.default(svgBuffer)
        .resize(size, size)
        .png()
        .toFile(join(__dirname, 'static', name));

      console.log(`✅ Generated ${name} (${size}x${size} - ${description})`);
    } catch (error) {
      console.error(`❌ Error generating ${name}:`, error.message);
    }
  }

  console.log('\n🎉 All icons generated successfully!');

} catch (error) {
  console.log('\n💡 To auto-generate icons, install sharp: npm install sharp');
  console.log('Then run this script again.');
}

console.log('\n📱 After generating the icons, the HTML has been updated with proper meta tags for iOS home screen support.');
