# Trigger and Remote Agent API Types

This document details the request and response types for all trigger and remote agent API endpoints.

## Remote Agent Endpoints

### Core Remote Agent Management

#### CreateRemoteAgent
**Endpoint:** `POST /remote-agents/create`

**Request Type:** `CreateRemoteAgentRequest`
```protobuf
message CreateRemoteAgentRequest {
  // The repository + commit to start the agent workspace
  RemoteAgentWorkspaceSetup workspace_setup = 1;

  // The initial request to send to the agent
  RemoteAgentChatRequestDetails initial_request_details = 2;

  // The name of the model to use. Mainly intended for internal use.
  optional string model = 3;

  // The setup script to run to setup the agent workspace. Assumes bash.
  optional string setup_script = 4;

  // HACK: The token of the client to use for the remote agent. This is used
  // internally so that we can run remote agents in a dev deploy but have them
  // use some staging APIs, assuming that staging users are running the rest of
  // their extensions on staging.
  // FIXME: Remove this once remote agents are in staging.
  optional string token = 5 [debug_redact = true];

  // Whether this agent is a setup script agent meant to generate a setup script.
  optional bool is_setup_script_agent = 6;

  // User defined title for the agent to be displayed in the UI
  optional string title = 7;
}
```

**Response Type:** `CreateRemoteAgentResponse`
```protobuf
message CreateRemoteAgentResponse {
  // The ID of the created agent
  string remote_agent_id = 1;

  // The status of the agent: either STARTING or ERROR
  RemoteAgentStatus status = 2;
}
```

#### DeleteRemoteAgent
**Endpoint:** `POST /remote-agents/delete`

**Request Type:** `DeleteRemoteAgentRequest`
```protobuf
message DeleteRemoteAgentRequest {
  string remote_agent_id = 1;
}
```

**Response Type:** `DeleteRemoteAgentResponse`
```protobuf
message DeleteRemoteAgentResponse {}
```

#### ListRemoteAgents
**Endpoint:** `POST /remote-agents/list`

**Request Type:** `ListRemoteAgentsRequest`
```protobuf
message ListRemoteAgentsRequest {}
```

**Response Type:** `ListRemoteAgentsResponse`
```protobuf
message ListRemoteAgentsResponse {
  repeated RemoteAgent remote_agents = 1;

  // The maximum number of total remote agents this user can have
  int32 max_remote_agents = 2;

  // The maximum number of active remote agents this user can have (not including paused agents)
  int32 max_active_remote_agents = 3;
}
```

#### ListRemoteAgentsStream
**Endpoint:** `POST /remote-agents/list-stream`

**Request Type:** `ListRemoteAgentsStreamRequest`
```protobuf
message ListRemoteAgentsStreamRequest {
  // Optional: The timestamp of the last update the client has seen.
  // If not provided or mismatching, the server will send the current state of all agents
  // and then stream subsequent updates.
  //
  // NOTE: Timestamp-based optimization is currently unimplemented, so the server
  // will always send the current state of all agents in the initial response.
  optional google.protobuf.Timestamp last_update_timestamp = 1;
}
```

**Response Type:** `ListRemoteAgentsStreamResponse` (streaming)
```protobuf
message ListRemoteAgentsStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response for efficiency.
  repeated AgentListUpdate updates = 1;
}
```

#### UpdateRemoteAgent
**Endpoint:** `POST /remote-agents/update`

**Request Type:** `UpdateRemoteAgentRequest`
```protobuf
message UpdateRemoteAgentRequest {
  string remote_agent_id = 1;

  // User defined title for the agent to be displayed in the UI
  optional string new_title = 2;
}
```

**Response Type:** `UpdateRemoteAgentResponse`
```protobuf
message UpdateRemoteAgentResponse {
  RemoteAgent agent = 1;
}
```

### Remote Agent Interaction

#### RemoteAgentChat
**Endpoint:** `POST /remote-agents/chat`

**Request Type:** `RemoteAgentChatRequest`
```protobuf
message RemoteAgentChatRequest {
  // Unique identifier for the remote agent
  string remote_agent_id = 1;

  RemoteAgentChatRequestDetails request_details = 2;
}
```

**Response Type:** `RemoteAgentChatResponse`
```protobuf
message RemoteAgentChatResponse {}
```

#### GetRemoteAgentChatHistory
**Endpoint:** `POST /remote-agents/get-chat-history`

**Request Type:** `GetRemoteAgentChatHistoryRequest`
```protobuf
message GetRemoteAgentChatHistoryRequest {
  string remote_agent_id = 1;

  // The sequence ID of the last processed exchange.
  // Use 0 to indicate that this is the first request, sequence IDs are
  // assigned starting from 1.
  // The server will return exchanges with sequence IDs greater than this value.
  // This enables clients to implement pagination by requesting subsequent pages
  // using the sequence ID of the last exchange from the previous response.
  uint32 last_processed_sequence_id = 2;
}
```

**Response Type:** `GetRemoteAgentChatHistoryResponse`
```protobuf
message GetRemoteAgentChatHistoryResponse {
  repeated RemoteAgentExchange chat_history = 2;

  // User-facing summary of the session.
  optional string session_summary = 3;

  optional RemoteAgent remote_agent = 4;
}
```

#### GetRemoteAgentHistoryStream
**Endpoint:** `POST /remote-agents/agent-history-stream`

**Request Type:** `GetRemoteAgentHistoryStreamRequest`
```protobuf
message GetRemoteAgentHistoryStreamRequest {
  // The ID of the remote agent to stream history for
  string remote_agent_id = 1;

  // The sequence ID of the last processed exchange.
  // The server will return exchanges with sequence IDs greater than this value.
  // This enables clients to implement pagination and reconnection by requesting
  // subsequent updates using the sequence ID of the last exchange from the previous response.
  uint32 last_processed_sequence_id = 2;
}
```

**Response Type:** `GetRemoteAgentHistoryStreamResponse` (streaming)
```protobuf
message GetRemoteAgentHistoryStreamResponse {
  // A list of structured updates. Multiple updates can be included in a single
  // response. The updates should be associated with at most one sequence ID.
  repeated AgentHistoryUpdate updates = 1;
}
```

#### InterruptRemoteAgent
**Endpoint:** `POST /remote-agents/interrupt`

**Request Type:** `InterruptRemoteAgentRequest`
```protobuf
message InterruptRemoteAgentRequest {
  // Unique identifier for the remote agent to interupt
  string remote_agent_id = 1;
}
```

**Response Type:** `InterruptRemoteAgentResponse`
```protobuf
message InterruptRemoteAgentResponse {
  // The status of the agent after the interruptio attempt
  RemoteAgentStatus status = 1;
}
```

#### PauseRemoteAgent
**Endpoint:** `POST /remote-agents/pause`

**Request Type:** `RemoteAgentPauseRequest`
```protobuf
message RemoteAgentPauseRequest {
  string remote_agent_id = 1;
}
```

**Response Type:** `RemoteAgentPauseResponse`
```protobuf
message RemoteAgentPauseResponse {}
```

#### ResumeRemoteAgent
**Endpoint:** `POST /remote-agents/resume`

**Request Type:** `RemoteAgentResumeRequest`
```protobuf
message RemoteAgentResumeRequest {
  string remote_agent_id = 1;
}
```

**Response Type:** `RemoteAgentResumeResponse`
```protobuf
message RemoteAgentResumeResponse {}
```

#### ResumeHintRemoteAgent
**Endpoint:** `POST /remote-agents/resume-hint`

**Request Type:** `RemoteAgentResumeHintRequest`
```protobuf
message RemoteAgentResumeHintRequest {
  string remote_agent_id = 1;
  // The reason for the hint being provided
  RemoteAgentResumeHintReason hint_reason = 2;
}
```

**Response Type:** `RemoteAgentResumeHintResponse`
```protobuf
message RemoteAgentResumeHintResponse {
  // Whether the resume process was started (returns before completion)
  bool resume_started = 1;
}
```

### Remote Agent Utilities

#### RemoteAgentAddSSHKey
**Endpoint:** `POST /remote-agents/add-ssh-key`

**Request Type:** `RemoteAgentAddSSHKeyRequest`
```protobuf
message RemoteAgentAddSSHKeyRequest {
  string remote_agent_id = 1;
  repeated string public_keys = 2;
}
```

**Response Type:** `RemoteAgentAddSSHKeyResponse`
```protobuf
message RemoteAgentAddSSHKeyResponse {
  // Returns all configured keys, not just the newly added ones.
  RemoteAgentSSHConfig ssh_config = 1;
}
```

#### RemoteAgentWorkspaceLogs
**Endpoint:** `POST /remote-agents/logs`

**Request Type:** `RemoteAgentWorkspaceLogsRequest`
```protobuf
message RemoteAgentWorkspaceLogsRequest {
  string remote_agent_id = 1;

  // These are the last step and sequence id that was received from the backend
  // to the frontend. The backend will return all the logs up to 1000 characters
  // including this step, sequence pair.
  // step 0, sequence 0 is a special case where the sequence does not increment,
  // but the returned log updates
  optional uint32 last_processed_step = 2; // default: 0
  optional uint32 last_processed_sequence_id = 3; // default: 0
}
```

**Response Type:** `RemoteAgentWorkspaceLogsResponse`
```protobuf
message RemoteAgentWorkspaceLogsResponse {
  string remote_agent_id = 1;
  RemoteWorkspaceSetupStatus workspace_setup_status = 3;
}
```

#### RemoteAgentGenerateSummary
**Endpoint:** `POST /remote-agents/generate-summary`

**Request Type:** `RemoteAgentGenerateSummaryRequest`
```protobuf
message RemoteAgentGenerateSummaryRequest {
  string remote_agent_id = 1;
  // [start, end] (inclusive)!
  uint32 start_sequence_id = 2;
  uint32 end_sequence_id = 3;
}
```

**Response Type:** `RemoteAgentGenerateSummaryResponse`
```protobuf
message RemoteAgentGenerateSummaryResponse {
  repeated DiffExplanationSection diff_descriptions = 1;
}
```

## Trigger Endpoints

All trigger endpoints are under `/remote-agent-actions/triggers/` and are marked as experimental.

### Trigger Management

#### CreateTrigger
**Endpoint:** `POST /remote-agent-actions/triggers/create`

**Request Type:** `CreateTriggerRequest`
```protobuf
message CreateTriggerRequest {
  TriggerConfiguration configuration = 1;
}
```

**Response Type:** `CreateTriggerResponse`
```protobuf
message CreateTriggerResponse {
  Trigger trigger = 1;
}
```

#### ListTriggers
**Endpoint:** `POST /remote-agent-actions/triggers/list`

**Request Type:** `ListTriggersRequest`
```protobuf
message ListTriggersRequest {}
```

**Response Type:** `ListTriggersResponse`
```protobuf
message ListTriggersResponse {
  repeated Trigger triggers = 1;
}
```

#### UpdateTrigger
**Endpoint:** `POST /remote-agent-actions/triggers/update`

**Request Type:** `UpdateTriggerRequest`
```protobuf
message UpdateTriggerRequest {
  string trigger_id = 1;
  TriggerConfiguration configuration = 2;
}
```

**Response Type:** `UpdateTriggerResponse`
```protobuf
message UpdateTriggerResponse {
  Trigger trigger = 1;
}
```

#### DeleteTrigger
**Endpoint:** `POST /remote-agent-actions/triggers/delete`

**Request Type:** `DeleteTriggerRequest`
```protobuf
message DeleteTriggerRequest {
  string trigger_id = 1;
}
```

**Response Type:** `DeleteTriggerResponse`
```protobuf
message DeleteTriggerResponse {}
```

### Trigger Execution & Monitoring

#### GetTriggerExecutions
**Endpoint:** `POST /remote-agent-actions/triggers/executions`

**Request Type:** `GetTriggerExecutionsRequest`
```protobuf
message GetTriggerExecutionsRequest {
  string trigger_id = 1;
  optional uint32 limit = 2; // defaults to 50
  optional uint32 offset = 3; // for pagination
}
```

**Response Type:** `GetTriggerExecutionsResponse`
```protobuf
message GetTriggerExecutionsResponse {
  repeated TriggerExecution executions = 1;
  uint32 total_count = 2; // total number of executions for this trigger
}
```

#### ExecuteTriggerManually
**Endpoint:** `POST /remote-agent-actions/triggers/execute-manually`

**Request Type:** `ExecuteTriggerManuallyRequest`
```protobuf
message ExecuteTriggerManuallyRequest {
  string trigger_id = 1;
  string entity_id = 2;
  optional string extra_prompt = 3; // Additional instructions to append to the agent prompt
}
```

**Response Type:** `ExecuteTriggerManuallyResponse`
```protobuf
message ExecuteTriggerManuallyResponse {
  string execution_id = 1;
  optional string remote_agent_id = 2;
  optional string message = 3;
}
```

#### GetMatchingEntities
**Endpoint:** `POST /remote-agent-actions/triggers/matching-entities`

**Request Type:** `GetMatchingEntitiesRequest`
```protobuf
message GetMatchingEntitiesRequest {
  // Either provide trigger_id for existing trigger or conditions for testing
  optional string trigger_id = 1;
  optional TriggerConditions conditions = 2;

  // Event source for filtering
  EventSource event_source = 3;

  // Pagination
  optional uint32 limit = 4; // defaults to 10, max 50
  optional uint32 offset = 5;

  // Time range for filtering entities
  optional string since = 6; // ISO 8601 timestamp
  optional string until = 7; // ISO 8601 timestamp
}
```

**Response Type:** `GetMatchingEntitiesResponse`
```protobuf
message GetMatchingEntitiesResponse {
  // GitHub entities (populated when event_source is EVENT_SOURCE_GITHUB)
  repeated GitHubEntity github_entities = 1;

  // Linear entities (populated when event_source is EVENT_SOURCE_LINEAR)
  repeated LinearEntity linear_entities = 2;

  uint32 total_count = 3;
  bool has_more = 4;

  // Metadata about the matching process
  optional string debug_info = 5;
}
```

### Additional Remote Agent Actions

#### ExecuteManualAgent
**Endpoint:** `POST /remote-agent-actions/execute-manual-agent`

**Request Type:** `ExecuteManualAgentRequest`
```protobuf
message ExecuteManualAgentRequest {
  string entity_id = 1;

  // Entity type specification (one must be set)
  optional GitHubEntityType github_entity_type = 2;
  optional LinearEntityType linear_entity_type = 3;

  AgentConfig agent_config = 4; // Agent configuration with starting nodes containing instructions
}
```

**Response Type:** `ExecuteManualAgentResponse`
```protobuf
message ExecuteManualAgentResponse {
  string execution_id = 1;
  optional string remote_agent_id = 2;
  optional string message = 3;
}
```

#### GetEntityDetails
**Endpoint:** `POST /remote-agent-actions/get-entity-details`

**Request Type:** `GetEntityDetailsRequest`
```protobuf
message GetEntityDetailsRequest {
  string entity_id = 1;

  // Entity type and source specification
  EventSource event_source = 2;
  optional GitHubEntityType github_entity_type = 3;
  optional LinearEntityType linear_entity_type = 4;

  // For GitHub entities, repository context may be needed
  optional string repository = 5; // Format: "owner/repo"
}
```

**Response Type:** `GetEntityDetailsResponse`
```protobuf
message GetEntityDetailsResponse {
  // Only one will be populated based on request
  optional GitHubEntity github_entity = 1;
  optional LinearEntity linear_entity = 2;

  bool found = 3;
  optional string error_message = 4;
}
```

## Supporting Types and Enums

### Remote Agent Status
```protobuf
enum RemoteAgentStatus {
  AGENT_UNSPECIFIED = 0;

  // The agent has been created, but has not yet been assigned to a Workspace.
  AGENT_PENDING = 5;

  // The agent is starting and setting up its environment
  AGENT_STARTING = 1;

  // The agent is actively working on its task
  AGENT_RUNNING = 2;

  // The agent is idle and waiting for further instructions, it may have
  // completed its task or may need further instruction
  AGENT_IDLE = 3;

  // The agent has encountered an error and cannot continue
  AGENT_ERROR = 4;

  // The agent has been paused by the user
  AGENT_PAUSED = 6;

  // The agent is resuming from a paused state
  AGENT_RESUMING = 7;
}
```

### Event Sources
```protobuf
enum EventSource {
  EVENT_SOURCE_UNSPECIFIED = 0;
  EVENT_SOURCE_GITHUB = 1;
  EVENT_SOURCE_LINEAR = 2;
  EVENT_SOURCE_JIRA = 3;
}
```

### Entity Types
```protobuf
enum GitHubEntityType {
  GITHUB_ENTITY_TYPE_UNSPECIFIED = 0;
  GITHUB_ENTITY_TYPE_PULL_REQUEST = 1;
  GITHUB_ENTITY_TYPE_WORKFLOW_RUN = 2;
}

enum LinearEntityType {
  LINEAR_ENTITY_TYPE_UNSPECIFIED = 0;
  LINEAR_ENTITY_TYPE_ISSUE = 1;
}
```

### Resume Hint Reasons
```protobuf
enum RemoteAgentResumeHintReason {
  // Default/unknown hint reason
  RESUME_AGENT_HINT_REASON_UNSPECIFIED = 0;
  // User started typing a message to the agent
  RESUME_AGENT_HINT_REASON_TYPING_MESSAGE = 1;
  // User is viewing the agent's chat history
  RESUME_AGENT_HINT_REASON_VIEWING_AGENT = 2;
}
```

### Core Data Types

#### RemoteAgent
```protobuf
message RemoteAgent {
  string remote_agent_id = 1;
  RemoteAgentWorkspaceSetup workspace_setup = 2;
  RemoteAgentStatus status = 3;
  google.protobuf.Timestamp started_at = 4;
  google.protobuf.Timestamp updated_at = 5;

  // A summary of the whole agentic session
  string session_summary = 6;

  // Summaries of what happened in each turn
  repeated string turn_summaries = 7;

  optional RemoteAgentSSHConfig ssh_config = 8;

  // Whether this agent is a setup script agent meant to generate a setup script.
  optional bool is_setup_script_agent = 9;

  // Whether the agent has unread updates
  // If true, it means the agent has updates that haven't been viewed by the user
  optional bool has_updates = 10;
  RemoteAgentWorkspaceStatus workspace_status = 11;

  google.protobuf.Timestamp expires_at = 12;

  optional string title = 13;
}
```

#### RemoteAgentWorkspaceSetup
```protobuf
message RemoteAgentWorkspaceSetup {
  oneof starting_files {
    GithubCommitRef github_commit_ref = 1;
  }

  // global git config to set, the values here will be passed to
  // `git config --global <key> <value>`
  // Recommended keys to pass are user.name and user.email
  map<string, string> git_config = 2;
}
```

#### TriggerConfiguration
```protobuf
message TriggerConfiguration {
  string name = 1;
  optional string description = 2;
  EventSource event_source = 3;
  TriggerConditions conditions = 4;
  AgentConfig agent_config = 5;
  optional bool enabled = 6; // defaults to true

  // Template information (if created from a template)
  optional string template_id = 7;
  optional string template_name = 8;
  optional string template_json = 9;
}
```

#### Trigger
```protobuf
message Trigger {
  string trigger_id = 1;
  TriggerConfiguration configuration = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;

  // Execution statistics
  uint32 execution_count = 5;
  optional google.protobuf.Timestamp last_execution_at = 6;
  optional string last_execution_status = 7;
}
```

#### RemoteAgentChatRequestDetails
```protobuf
message RemoteAgentChatRequestDetails {
  // User message, represented as a list of structured content blocks
  // Note: if the `message` field is present, it should be interpreted as
  // a leading text node followed by the list of nodes.
  repeated ChatRequestNode request_nodes = 1;

  // Additional system prompt specified by the user.
  optional string user_guidelines = 2;

  // Additional system prompt specified by the workspace.
  optional string workspace_guidelines = 3;

  // Memories to be included for the Agent system prompt.
  optional string agent_memories = 4;

  // The model ID to use for the request.
  optional string model_id = 5;
}
```

#### AgentConfig
```protobuf
message AgentConfig {
  WorkspaceSetup workspace_setup = 1;
  repeated ChatRequestNode starting_nodes = 2;
  optional string user_guidelines = 3;
  optional string workspace_guidelines = 4;
  optional string agent_memories = 5;
  optional string model = 6;
  optional bool is_setup_script_agent = 7;
  repeated McpServerConfig mcp_servers = 8;
  optional string title = 9;
}
```

#### ChatRequestNode
```protobuf
message ChatRequestNode {
  // Unique id of the node.
  int32 id = 1;

  // Type of the node.
  ChatRequestNodeType type = 2;

  optional ChatRequestText text_node = 3;
  optional ChatRequestToolResult tool_result_node = 4;
  optional ChatRequestImage image_node = 5;
  optional ChatRequestIdeState ide_state_node = 7;
  optional ChatRequestEditEvents edit_events_node = 8;
}
```

## Notes

- All endpoints use **POST** requests with JSON body payloads
- All endpoints require authentication
- Trigger endpoints are marked as **experimental** and subject to change
- Streaming endpoints return multiple responses over time
- Optional fields may be omitted from requests
- Timestamps use `google.protobuf.Timestamp` format
- For CreateRemoteAgent requests, omit the `model_id` field to let the system automatically select the appropriate agent model
- Use integer values for ChatRequestNode `type` field (e.g., `type: 0` for TEXT) rather than string values
