# Related Types for Trigger and Remote Agent APIs

This document contains all the supporting types, enums, and message definitions that are referenced in the trigger and remote agent API endpoints.

## Chat and Node Types

### ChatRequestNodeType
```protobuf
enum ChatRequestNodeType {
  // User message text.
  TEXT = 0;

  // Result of a tool use.
  TOOL_RESULT = 1;

  // An image(Default format: PNG).
  IMAGE = 2;

  // If sent with no auxiliary data in ChatRequestNode,
  // came from a client which failed to hydrate the real
  // IMAGE node.
  IMAGE_ID = 3;

  // IDE state information.
  IDE_STATE = 4;

  // User edits information.
  EDIT_EVENTS = 5;

  // CHECKPOINT_REF is a CLIENT ONLY node type which should never be sent to the
  // server. Alas we cannot avoid bugs and have sent this to the backend, so we
  // give it a name here so that api-proxy can identify and drop such nodes.
  CHECKPOINT_REF = 6;
}
```

### ChatResultNodeType
```protobuf
enum ChatResultNodeType {
  // The raw response from the model.
  // Content is a single string with raw response.
  RAW_RESPONSE = 0;

  // Our guess of what user will ask next.
  // Content is "{question1}\n{question2}".
  SUGGESTED_QUESTIONS = 1;

  // Indication that streaming of the main response finished.
  // Content is always empty.
  MAIN_TEXT_FINISHED = 2;

  // Workspace file chunks used in prompt
  // Every line in content is "{file_path}:{char_start}-{char_end}".
  WORKSPACE_FILE_CHUNKS = 3;

  // Sources that were useful to generate the response
  // Every line in content is "{file_path}".
  RELEVANT_SOURCES = 4;

  // Tool use requested by the AI model.
  // When tool use generation begins, TOOL_USE_START is sent with name and id.
  // When the tool use is fully generated, TOOL_USE will be sent with all fields
  // populated completely.  Real streaming TBD.
  TOOL_USE = 5;
  TOOL_USE_START = 7;

  // Thinking/reasoning content from the model (e.g., from OpenAI's reasoning_content)
  THINKING = 8;
}
```

### ToolResultContentNodeType
```protobuf
enum ToolResultContentNodeType {
  // Unspecified content type
  CONTENT_TYPE_UNSPECIFIED = 0;

  // Text content
  CONTENT_TEXT = 1;
  // Image content
  CONTENT_IMAGE = 2;
}
```

### ImageFormatType
```protobuf
enum ImageFormatType {
  // Default unspecified value - should not be used
  IMAGE_FORMAT_UNSPECIFIED = 0;
  // PNG format
  PNG = 1;
  // JPEG format
  JPEG = 2;
  // GIF format
  GIF = 3;
  // WebP format
  WEBP = 4;
}
```

### EditEventSource
```protobuf
enum EditEventSource {
  // Default unspecified value
  UNSPECIFIED = 0;
  // Edit was performed by the user
  USER_EDIT = 1;
  // Edit was performed by reverting to a checkpoint
  CHECKPOINT_REVERT = 2;
}
```

## Chat Message Components

### ChatRequestText
```protobuf
message ChatRequestText {
  string content = 1;
}
```

### ChatRequestImage
```protobuf
message ChatRequestImage {
  // Base64 encoded image data
  string image_data = 1;
  // Format of the image data
  ImageFormatType format = 2;
}
```

### ToolResultContentNode
```protobuf
message ToolResultContentNode {
  // Type of the node
  ToolResultContentNodeType type = 1;

  // Text content if this is a text node
  optional string text_content = 2;

  // Image content if this is an image node
  optional ChatRequestImage image_content = 3;
}
```

### ChatRequestToolResult
```protobuf
message ChatRequestToolResult {
  string tool_use_id = 1;
  // Plain text content (ignored when content_nodes is present)
  string content = 2;
  bool is_error = 3;
  optional string request_id = 4;
  // List of content nodes (text or images)
  // If present, takes precedence over content field
  repeated ToolResultContentNode content_nodes = 5;
}
```

## Workspace Setup Types

### WorkspaceSetup
```protobuf
message WorkspaceSetup {
  oneof starting_files {
    GithubRef github_ref = 1;
  }
  // TODO: add more here, like setup commands, env vars, etc?
  optional string patch = 2;
  optional string setup_script = 3;

  // global git config to set, the values here will be passed to
  // `git config --global <key> <value>`
  // Recommended keys to pass are user.name and user.email
  map<string, string> git_config = 4;
}
```

### GithubRef
```protobuf
message GithubRef {
  string url = 1;
  string ref = 2;
}
```

### GithubCommitRef
```protobuf
message GithubCommitRef {
  string url = 1;
  string commit_sha = 2;
}
```

### RemoteWorkspaceSetupStepStatus
```protobuf
enum RemoteWorkspaceSetupStepStatus {
  SETUP_STEP_STATUS_UNSPECIFIED = 0;
  SETUP_STEP_STATUS_PENDING = 1;
  SETUP_STEP_STATUS_RUNNING = 2;
  SETUP_STEP_STATUS_COMPLETED = 3;
  SETUP_STEP_STATUS_FAILED = 4;
}
```

### RemoteWorkspaceSetupStep
```protobuf
message RemoteWorkspaceSetupStep {
  // A string we can show users, like "Cloning repository" or "Running setup script"
  string step_description = 1;
  string logs = 2;
  RemoteWorkspaceSetupStepStatus status = 3;
  // this is monotonically increasing but not necessarily consecutive.
  uint32 sequence_id = 4;
  // This is the step number, starting from 0. It is consecutive.
  uint32 step_number = 5;
  // TODO: add an enum here if the UI wants to be able to identify specific steps?
}
```

### RemoteWorkspaceSetupStatus
```protobuf
message RemoteWorkspaceSetupStatus {
  // While the agent is in starting state, should always contain at least one step that is running
  repeated RemoteWorkspaceSetupStep steps = 1;
}
```

## SSH Configuration Types

### SSHConfigOption
```protobuf
message SSHConfigOption {
  // Key and value for a single SSH config option, based on
  // https://man7.org/linux/man-pages/man5/ssh_config.5.html.
  string key = 1;
  string value = 2;
}
```

### RemoteAgentSSHConfig
```protobuf
message RemoteAgentSSHConfig {
  repeated string public_keys = 1;

  // The hostname and config are the same for all public keys.
  string hostname = 2;
  repeated SSHConfigOption ssh_config_options = 3;
}
```

## Trigger Condition Types

### TriggerConditionType
```protobuf
enum TriggerConditionType {
  TRIGGER_CONDITION_TYPE_UNSPECIFIED = 0;
  TRIGGER_CONDITION_GITHUB = 1;
  TRIGGER_CONDITION_LINEAR = 2;
  // Future: TRIGGER_CONDITION_JIRA = 3;
}
```

### TriggerConditions
```protobuf
message TriggerConditions {
  // The type of condition, which determines which field is set
  TriggerConditionType type = 1;

  // For TRIGGER_CONDITION_GITHUB conditions
  optional GitHubTriggerConditions github = 2;
  // For TRIGGER_CONDITION_LINEAR conditions
  optional LinearTriggerConditions linear = 3;
  // Future: optional JiraTriggerConditions jira = 4;
}
```

### GitHubTriggerConditions
```protobuf
message GitHubTriggerConditions {
  GitHubEntityType entity_type = 1;

  // Entity-specific conditions (only one should be populated based on entity_type)
  optional GitHubPullRequestTriggerConditions pull_request = 2;
  optional GitHubWorkflowRunTriggerConditions workflow_run = 3;
}
```

### GitHubPullRequestTriggerConditions
```protobuf
message GitHubPullRequestTriggerConditions {
  // Repository filter (owner/repo format, e.g., "igor0/augment")
  optional string repository = 1;

  // Author filter (@me for current user, or specific username)
  optional string author = 2;

  // Assignee filter (@me for current user, or specific username)
  optional string assignee = 3;

  // Reviewer filter (@me for current user, or specific username)
  optional string reviewer = 4;

  // Label filters (all must be present)
  repeated string labels = 5;

  // Activity type filters for webhook events ("opened", "synchronize", "ready_for_review", "closed", etc.)
  repeated string activity_types = 6;

  // Draft filter
  optional bool draft = 7;

  // Base branch filter
  optional string base_branch = 8;

  // Head branch filter
  optional string head_branch = 9;
}
```

### GitHubWorkflowRunTriggerConditions
```protobuf
message GitHubWorkflowRunTriggerConditions {
  // Repository filter (owner/repo format, e.g., "igor0/augment")
  optional string repository = 1;

  // Actor filter (@me for current user, or specific username)
  optional string actor = 2;

  // Triggering actor filter (@me for current user, or specific username)
  optional string triggering_actor = 3;

  // Workflow name or ID filters
  repeated string workflows = 4;

  // Event type filter - single event only ("push", "pull_request", "schedule", etc.)
  // GitHub API only supports filtering by one event type at a time
  optional string event = 5;

  // Status filter ("queued", "in_progress", "completed")
  // Cannot be used together with conclusion - GitHub API uses single "status" parameter
  optional string status = 6;

  // Conclusion filter ("success", "failure", "neutral", "cancelled", "skipped", "timed_out", "action_required")
  // Cannot be used together with status - GitHub API uses single "status" parameter
  optional string conclusion = 7;

  // Branch filter
  optional string branch = 8;
}
```

### LinearTriggerConditions
```protobuf
message LinearTriggerConditions {
  LinearEntityType entity_type = 1;

  // Entity-specific conditions (only one should be populated based on entity_type)
  optional LinearIssueTriggerConditions issue = 2;
}
```

### LinearIssueTriggerConditions
```protobuf
message LinearIssueTriggerConditions {
  // Team filter (team key like "AUG" or team name)
  optional string team = 1;

  // Creator filter (@me for current user, or specific user ID/email)
  optional string creator = 2;

  // Assignee filter (@me for current user, or specific user ID/email)
  optional string assignee = 3;

  // State filters (by name like "In Progress" or by type like "started")
  repeated string states = 4;

  // State type filters ("backlog", "unstarted", "started", "completed", "canceled")
  repeated string state_types = 5;

  // Priority filters (0=No priority, 1=Urgent, 2=High, 3=Medium, 4=Low)
  repeated int32 priorities = 6;

  // Label filters (all must be present)
  repeated string labels = 7;

  // Project filters (project name or ID)
  repeated string projects = 8;

  // Activity type filters for webhook events ("create", "update", "remove")
  repeated string activity_types = 9;

  // Estimate filters (story points)
  repeated int32 estimates = 10;
}
```

## Entity Types

### GitHubEntity
```protobuf
message GitHubEntity {
  string id = 1;
  GitHubEntityType type = 2;
  GitHubEntityRepository repository = 3;
  string timestamp = 4;

  // Entity-specific data (only one should be populated based on type)
  optional GitHubPullRequestEntity pull_request = 5;
  optional GitHubWorkflowRunEntity workflow_run = 6;
}
```

### GitHubEntityRepository
```protobuf
message GitHubEntityRepository {
  string owner = 1;
  string name = 2;
  string full_name = 3;
  string html_url = 4;
  string default_branch = 5;
  bool private = 6;
}
```

### GitHubEntityUser
```protobuf
message GitHubEntityUser {
  string login = 1;
  string avatar_url = 2;
  string html_url = 3;
  int64 id = 4;
}
```

### GitHubPullRequestEntity
```protobuf
message GitHubPullRequestEntity {
  int32 number = 1;
  string title = 2;
  string body = 3;
  string state = 4; // "open", "closed", "merged"
  string html_url = 5;
  string created_at = 6;
  string updated_at = 7;
  optional string merged_at = 8;
  optional string closed_at = 9;
  GitHubEntityUser user = 10;
  repeated GitHubEntityUser assignees = 11;
  repeated GitHubEntityUser requested_reviewers = 12;
  string head_ref = 13;
  string base_ref = 14;
  string head_sha = 15;
  string base_sha = 16;
  bool draft = 17;
  repeated string labels = 18;
}
```

### GitHubWorkflowRunEntity
```protobuf
message GitHubWorkflowRunEntity {
  int64 id = 1;
  string name = 2;
  string display_title = 3;
  int32 run_number = 4;
  string event = 5; // "push", "pull_request", "schedule", etc.
  string status = 6; // "queued", "in_progress", "completed"
  optional string conclusion = 7; // "success", "failure", "cancelled", "skipped", etc.
  string workflow_id = 8;
  string workflow_name = 9;
  string html_url = 10;
  string created_at = 11;
  string updated_at = 12;
  optional string run_started_at = 13;
  GitHubEntityUser actor = 14;
  GitHubEntityUser triggering_actor = 15;
  string head_branch = 16;
  string head_sha = 17;
}
```

### LinearEntity
```protobuf
message LinearEntity {
  string id = 1;
  LinearEntityType type = 2;
  string timestamp = 3;

  // Entity-specific data (only one should be populated based on type)
  optional LinearIssueEntity issue = 4;
}
```

### LinearEntityUser
```protobuf
message LinearEntityUser {
  string id = 1;
  string name = 2;
  string display_name = 3;
  string email = 4;
  optional string avatar_url = 5;
}
```

### LinearEntityTeam
```protobuf
message LinearEntityTeam {
  string id = 1;
  string key = 2; // Team key like "AUG"
  string name = 3;
  optional string description = 4;
}
```

### LinearEntityWorkflowState
```protobuf
message LinearEntityWorkflowState {
  string id = 1;
  string name = 2;
  string type = 3; // "backlog", "unstarted", "started", "completed", "canceled"
  optional string description = 4;
  optional string color = 5;
}
```

### LinearEntityProject
```protobuf
message LinearEntityProject {
  string id = 1;
  string name = 2;
  optional string description = 3;
  string state = 4; // "backlog", "planned", "started", "paused", "completed", "canceled"
  optional string url = 5;
}
```

### LinearEntityLabel
```protobuf
message LinearEntityLabel {
  string id = 1;
  string name = 2;
  optional string description = 3;
  optional string color = 4;
}
```

### LinearIssueEntity
```protobuf
message LinearIssueEntity {
  string id = 1;
  string identifier = 2; // Human-readable ID like "AUG-123"
  string title = 3;
  optional string description = 4;
  int32 priority = 5; // 0=No priority, 1=Urgent, 2=High, 3=Medium, 4=Low
  string url = 6;
  string created_at = 7;
  string updated_at = 8;
  optional string started_at = 9;
  optional string completed_at = 10;
  optional string canceled_at = 11;
  optional string due_date = 12;
  optional double estimate = 13; // Story points or time estimate

  // Related entities
  optional LinearEntityUser creator = 14;
  optional LinearEntityUser assignee = 15;
  optional LinearEntityTeam team = 16;
  optional LinearEntityWorkflowState state = 17;
  optional LinearEntityProject project = 18;
  repeated LinearEntityLabel labels = 19;
}
```

## Execution and Exchange Types

### TriggerExecution
```protobuf
message TriggerExecution {
  string execution_id = 1;
  string trigger_id = 2;
  string event_id = 3;
  optional string remote_agent_id = 4;
  google.protobuf.Timestamp started_at = 5;
  optional google.protobuf.Timestamp completed_at = 6;
  optional string error_message = 7;

  // Event payload that triggered this execution
  optional string event_payload = 8;
}
```

### Exchange
```protobuf
message Exchange {
  string request_message = 1;
  optional string response_text = 2;
  optional string request_id = 3;
  repeated ChatRequestNode request_nodes = 4;
  repeated ChatResultNode response_nodes = 5;
}
```

### RemoteAgentExchange
```protobuf
message RemoteAgentExchange {
  Exchange exchange = 1;

  // Files changed as a result of this exchange
  repeated ChangedFile changed_files = 2;

  // Sequence ID for tracking the order of exchanges
  // Sequence ID must be monotonic starting from 1
  uint32 sequence_id = 3;

  // Optional user-facing summary of the turn (or last few turns).
  optional string turn_summary = 4;

  // Timestamp when the exchange was finished.
  google.protobuf.Timestamp finished_at = 5;
}
```

### ExchangeUpdate
```protobuf
message ExchangeUpdate {
  // The request ID associated with this update.
  // This can be used to correlate the update with a specific request.
  optional string request_id = 1;

  // The sequence ID of the exchange being updated.
  // This allows clients to maintain order and handle reconnection scenarios.
  uint32 sequence_id = 2;

  // The text to append to the current response.
  // This is the incremental text that should be added to the existing response.
  string appended_text = 3;

  // The nodes to append to the current response.
  // These are additional result nodes that should be added to the existing response.
  repeated ChatResultNode appended_nodes = 4;

  // Whether this update marks the end of the exchange.
  // When true, no more updates will be sent for this exchange.
  bool is_final = 5;
}
```

### ChangedFile
```protobuf
message ChangedFile {
  string path = 1;
  string change_type = 2; // "created", "modified", "deleted"
  optional string content = 3; // New content for created/modified files
}
```

## MCP Server Configuration

### McpServerConfig
```protobuf
message McpServerConfig {
  oneof config {
    McpServerStdioConfig stdio = 1;
    McpServerHttpConfig http = 2;
  }
}
```

### McpServerStdioConfig
```protobuf
message McpServerStdioConfig {
  string command = 1;
  repeated string args = 2;
  map<string, string> env = 3;
}
```

### McpServerHttpConfig
```protobuf
message McpServerHttpConfig {
  string url = 1;
  map<string, string> headers = 2;
}
```

## Chat Result Types

### ChatResultNode
```protobuf
message ChatResultNode {
  int32 id = 1;
  ChatResultNodeType type = 2;
  string content = 3;
  optional ChatResultToolUse tool_use = 4;
  optional ChatResultAgentMemory agent_memory = 5;
  optional string request_id = 6;
}
```

### ChatResultToolUse
```protobuf
message ChatResultToolUse {
  string tool_use_id = 1;
  string tool_name = 2;
  string input_json = 3;

  // Whether this is a partial tool use response.
  bool is_partial = 4;
}
```

### ChatResultAgentMemory
```protobuf
message ChatResultAgentMemory {
  string content = 1;
}
```

## Notes

- All timestamps use `google.protobuf.Timestamp` format
- Entity IDs are strings that uniquely identify entities within their respective systems
- Repository references use "owner/repo" format for GitHub
- Linear team keys are short identifiers like "AUG" while team names are full names
- Priority values for Linear issues: 0=No priority, 1=Urgent, 2=High, 3=Medium, 4=Low
- GitHub workflow run status values: "queued", "in_progress", "completed"
- GitHub workflow run conclusion values: "success", "failure", "neutral", "cancelled", "skipped", "timed_out", "action_required"
- Linear state types: "backlog", "unstarted", "started", "completed", "canceled"
- All optional fields may be omitted from requests/responses
