<!doctype html>
<html lang="en" class="h-full">
	<head>
		<meta charset="utf-8" />

		<!-- Standard favicon -->
		<link rel="icon" href="%sveltekit.assets%/favicon.svg" type="image/svg+xml" />
		<link rel="icon" href="%sveltekit.assets%/favicon.ico" type="image/x-icon" />
		<link rel="icon" href="%sveltekit.assets%/favicon.png" type="image/png" />

		<!-- Apple Touch Icons for iOS home screen -->
		<link rel="apple-touch-icon" sizes="180x180" href="%sveltekit.assets%/apple-touch-icon-180x180.png" />
		<link rel="apple-touch-icon" sizes="167x167" href="%sveltekit.assets%/apple-touch-icon-167x167.png" />
		<link rel="apple-touch-icon" sizes="152x152" href="%sveltekit.assets%/apple-touch-icon-152x152.png" />
		<link rel="apple-touch-icon" sizes="120x120" href="%sveltekit.assets%/apple-touch-icon-120x120.png" />
		<link rel="apple-touch-icon" sizes="76x76" href="%sveltekit.assets%/apple-touch-icon-76x76.png" />
		<link rel="apple-touch-icon" sizes="60x60" href="%sveltekit.assets%/apple-touch-icon-60x60.png" />

		<!-- PWA Manifest -->
		<link rel="manifest" href="%sveltekit.assets%/manifest.json" />

		<!-- iOS Web App Meta Tags -->
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="default" />
		<meta name="apple-mobile-web-app-title" content="Augment" />
		<meta name="format-detection" content="telephone=no" />

		<!-- PWA Meta Tags -->
		<meta name="application-name" content="Augment" />
		<meta name="mobile-web-app-capable" content="yes" />

		<!-- Theme colors -->
		<meta name="theme-color" content="#3b82f6" />
		<meta name="msapplication-TileColor" content="#3b82f6" />
		<meta name="msapplication-config" content="%sveltekit.assets%/browserconfig.xml" />

		<!-- Viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, maximum-scale=1.0" />

		<!-- SEO and Social Meta Tags -->
		<meta name="description" content="Augment Browser - AI-powered coding assistant for enhanced development workflow" />
		<meta name="keywords" content="AI, coding, assistant, development, augment, browser" />
		<meta name="author" content="Augment Code" />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:title" content="Augment Browser" />
		<meta property="og:description" content="AI-powered coding assistant for enhanced development workflow" />
		<meta property="og:image" content="%sveltekit.assets%/apple-touch-icon-180x180.png" />

		<!-- Twitter -->
		<meta property="twitter:card" content="summary" />
		<meta property="twitter:title" content="Augment Browser" />
		<meta property="twitter:description" content="AI-powered coding assistant for enhanced development workflow" />
		<meta property="twitter:image" content="%sveltekit.assets%/apple-touch-icon-180x180.png" />

		<!-- Theme initialization script - runs immediately to prevent FOUC -->
		<script>
			(function() {
				// Get theme from localStorage or system preference
				function getInitialTheme() {
					const stored = localStorage.getItem('theme');
					if (stored === 'dark' || stored === 'light') return stored;

					// Check system preference
					if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
						return 'dark';
					}

					return 'light';
				}

				// Apply theme immediately
				const theme = getInitialTheme();
				if (theme === 'dark') {
					document.documentElement.classList.add('dark');
				}
			})();
		</script>

		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover" class="h-full bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>
