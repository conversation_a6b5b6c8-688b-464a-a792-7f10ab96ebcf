import type { Handle } from '@sveltejs/kit';
import { redirect } from '@sveltejs/kit';

// Routes that don't require password protection
const PUBLIC_ROUTES = [
	'/site-login',
	'/api/site-auth',
	'/login',
	'/auth',
	'/api/auth',
	'/githubUserCallback',
	'/linearCallback'
];

/**
 * Check if route is public (doesn't require password)
 */
function isPublicRoute(pathname: string): boolean {
	return PUBLIC_ROUTES.some((route) => pathname.startsWith(route));
}

/**
 * Check if user has valid site access
 */
function hasValidSiteAccess(cookies: any): boolean {
	return cookies.get('site-authenticated') === 'true';
}

/**
 * Generate Content Security Policy header
 */
function generateCSPHeader(): string {
	const csp = [
		"default-src 'self'",
		"script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Monaco editor requires unsafe-eval
		"style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com", // Tailwind, component styles, and highlight.js
		"img-src 'self' data: https:",
		"font-src 'self' data:",
		"connect-src 'self' https://*.augmentcode.com",
		"frame-src 'none'",
		"object-src 'none'",
		"base-uri 'self'",
		"form-action 'self'",
		"frame-ancestors 'none'"
	];
	return csp.join('; ');
}

export const handle: Handle = async ({ event, resolve }) => {
	const { url, request, cookies } = event;
	const pathname = url.pathname;

	// Skip password check for public routes
	if (isPublicRoute(pathname)) {
		const response = await resolve(event);

		// Add security headers to all responses
		response.headers.set('Content-Security-Policy', generateCSPHeader());
		response.headers.set('X-Frame-Options', 'DENY');
		response.headers.set('X-Content-Type-Options', 'nosniff');
		response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
		response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

		return response;
	}

	// Check if user has valid site access
	const siteAuth = cookies.get('site-authenticated');
	console.log(`[hooks.server.ts] Checking site access for ${pathname}, cookie value:`, siteAuth);

	if (!hasValidSiteAccess(cookies)) {
		console.log(`[hooks.server.ts] Site access denied for ${pathname}, redirecting to site-login`);
		const redirectTo = encodeURIComponent(pathname + url.search);
		throw redirect(302, `/site-login?redirectTo=${redirectTo}`);
	}

	console.log(`[hooks.server.ts] Site access granted for ${pathname}`);

	const response = await resolve(event);

	// Add security headers to all responses
	response.headers.set('Content-Security-Policy', generateCSPHeader());
	response.headers.set('X-Frame-Options', 'DENY');
	response.headers.set('X-Content-Type-Options', 'nosniff');
	response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
	response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

	return response;
};
