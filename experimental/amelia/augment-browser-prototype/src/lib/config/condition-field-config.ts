/**
 * Condition Field Configuration
 *
 * Defines all available condition fields for each provider and entity type.
 * Used to generate dynamic forms and ensure consistency across components.
 */

export interface ConditionFieldConfig {
	key: string;
	label: string;
	placeholder: string;
	hint?: string;
	type?: 'text' | 'select' | 'number';
	options?: Array<{ value: string; label: string }>;
}

export interface EntityConditionConfig {
	provider: string;
	entityType: string;
	fields: ConditionFieldConfig[];
}

// GitHub Pull Request condition fields
const githubPullRequestFields: ConditionFieldConfig[] = [
	{
		key: 'prRepository',
		label: 'Repository',
		placeholder: 'owner/repo (leave empty for all)',
		hint: 'Filter by specific repository'
	},
	{
		key: 'prAuthor',
		label: 'Author',
		placeholder: '@me, username',
		hint: 'Who created the pull request'
	},
	{
		key: 'prAssignee',
		label: 'Assignee',
		placeholder: '@me, username',
		hint: 'Who is assigned to the pull request'
	},
	{
		key: 'prReviewer',
		label: 'Reviewer',
		placeholder: '@me, username',
		hint: 'Who is requested to review'
	},
	{
		key: 'prBaseBranch',
		label: 'Base Branch',
		placeholder: 'main, develop',
		hint: 'Target branch for the pull request'
	},
	{
		key: 'prHeadBranch',
		label: 'Head Branch',
		placeholder: 'feature/*',
		hint: 'Source branch pattern'
	}
];

// GitHub Workflow Run condition fields
const githubWorkflowRunFields: ConditionFieldConfig[] = [
	{
		key: 'workflowRepository',
		label: 'Repository',
		placeholder: 'owner/repo (leave empty for all)',
		hint: 'Filter by specific repository'
	},
	{
		key: 'workflowActor',
		label: 'Actor',
		placeholder: '@me, username',
		hint: 'Who triggered the workflow'
	},
	{
		key: 'workflowEvent',
		label: 'Trigger Event',
		placeholder: 'push, pull_request',
		hint: 'What event triggered the workflow'
	},
	{
		key: 'workflowStatus',
		label: 'Status',
		type: 'select',
		placeholder: 'All',
		options: [
			{ value: '', label: 'All' },
			{ value: 'queued', label: 'Queued' },
			{ value: 'in_progress', label: 'In Progress' },
			{ value: 'completed', label: 'Completed' }
		],
		hint: 'Current workflow status'
	},
	{
		key: 'workflowConclusion',
		label: 'Conclusion',
		type: 'select',
		placeholder: 'All',
		options: [
			{ value: '', label: 'All' },
			{ value: 'success', label: 'Success' },
			{ value: 'failure', label: 'Failure' },
			{ value: 'cancelled', label: 'Cancelled' },
			{ value: 'skipped', label: 'Skipped' }
		],
		hint: 'Final workflow result'
	},
	{
		key: 'workflowBranch',
		label: 'Branch',
		placeholder: 'main, develop',
		hint: 'Branch that triggered the workflow'
	}
];

// Linear Issue condition fields
const linearIssueFields: ConditionFieldConfig[] = [
	{
		key: 'linearTeam',
		label: 'Team',
		placeholder: 'AUG, ENG',
		hint: 'Team key or name'
	},
	{
		key: 'linearCreator',
		label: 'Creator',
		placeholder: '@me, username',
		hint: 'Who created the issue'
	},
	{
		key: 'linearAssignee',
		label: 'Assignee',
		placeholder: '@me, username',
		hint: 'Who is assigned to the issue'
	},
	{
		key: 'linearProject',
		label: 'Project',
		placeholder: 'Project name',
		hint: 'Linear project name or ID'
	},
	{
		key: 'linearTitleContains',
		label: 'Title Contains',
		placeholder: 'bug, feature, urgent',
		hint: 'Filter issues by title content'
	},
	{
		key: 'linearActivityTypes',
		label: 'Activity Types',
		placeholder: 'created, updated',
		hint: 'Comma-separated list of activity types'
	},
	{
		key: 'linearMinEstimate',
		label: 'Min Estimate',
		type: 'number',
		placeholder: '1',
		hint: 'Minimum story points/estimate'
	},
	{
		key: 'linearMaxEstimate',
		label: 'Max Estimate',
		type: 'number',
		placeholder: '8',
		hint: 'Maximum story points/estimate'
	},
	{
		key: 'linearStates',
		label: 'States',
		placeholder: 'In Progress, Review',
		hint: 'Comma-separated list of issue states'
	},
	{
		key: 'linearStateTypes',
		label: 'State Types',
		placeholder: 'started, completed',
		hint: 'Comma-separated list of state types'
	},
	{
		key: 'linearLabels',
		label: 'Labels',
		placeholder: 'bug, urgent, frontend',
		hint: 'Comma-separated list of required labels'
	},
	{
		key: 'linearPriorities',
		label: 'Priorities',
		placeholder: '0, 1, 2',
		hint: 'Comma-separated list of priorities (0=None, 1=Urgent, 2=High, 3=Medium, 4=Low)'
	}
];

// Complete configuration for all entity types
export const ENTITY_CONDITION_CONFIGS: EntityConditionConfig[] = [
	{
		provider: 'github',
		entityType: 'pull_request',
		fields: githubPullRequestFields
	},
	{
		provider: 'github',
		entityType: 'workflow_run',
		fields: githubWorkflowRunFields
	},
	{
		provider: 'linear',
		entityType: 'issue',
		fields: linearIssueFields
	}
];

// Helper functions
export function getConditionFieldsForEntity(provider: string, entityType: string): ConditionFieldConfig[] {
	const config = ENTITY_CONDITION_CONFIGS.find(
		c => c.provider === provider && c.entityType === entityType
	);
	return config?.fields || [];
}

export function getAllConditionFields(): ConditionFieldConfig[] {
	return ENTITY_CONDITION_CONFIGS.flatMap(config => config.fields);
}

export function getConditionFieldByKey(key: string): ConditionFieldConfig | undefined {
	return getAllConditionFields().find(field => field.key === key);
}
