<script lang="ts">
	import { entities, triggers, shouldShowEntitySkeleton } from '$lib/stores/global-state.svelte';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import EntityCombobox from './EntityCombobox.svelte';

	interface Props {
		value?: UnifiedEntity | null;
		placeholder?: string;
		disabled?: boolean;
		class?: string;
		containerClass?: string;
		onValueChange?: (entity: UnifiedEntity | null, trigger?: NormalizedTrigger | null) => void;
		onEntitySelected?: (entity: UnifiedEntity | null, trigger?: NormalizedTrigger | null) => void;
	}

	let {
		value = null,
		placeholder = 'Search all entities...',
		disabled = false,
		class: className = '',
		containerClass = '',
		onValueChange,
		onEntitySelected
	}: Props = $props();

	// Show all entities, including mock entities when no real entities are available
	let allAvailableEntities = $derived($entities);

	function handleEntityChange(entity: UnifiedEntity | null) {
		let trigger: NormalizedTrigger | null = null;

		if (entity) {
			// Get trigger information if available
			const entityWithTrigger = entity as UnifiedEntity & {
				triggerId?: string;
				triggerName?: string;
			};
			if (entityWithTrigger.triggerId) {
				trigger = $triggers.find((t) => t.id === entityWithTrigger.triggerId) || null;
			}
		}

		onValueChange?.(entity, trigger);
		onEntitySelected?.(entity, trigger);
	}
</script>

<EntityCombobox
	entities={allAvailableEntities}
	{value}
	{placeholder}
	{disabled}
	clearable={true}
	isLoading={$shouldShowEntitySkeleton}
	onValueChange={handleEntityChange}
	class="w-full {className}"
	{containerClass}
/>
