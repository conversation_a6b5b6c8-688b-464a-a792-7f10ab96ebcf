<script lang="ts">
	import { Icon, XMark } from 'svelte-hero-icons';
	import { tick } from 'svelte';
	import MatchRow from '$lib/components/ui/data-display/MatchRow.svelte';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';

	interface Props {
		entities: UnifiedEntity[];
		value?: UnifiedEntity | null;
		placeholder?: string;
		disabled?: boolean;
		clearable?: boolean;
		class?: string;
		containerClass?: string;
		isLoading?: boolean;
		onValueChange?: (entity: UnifiedEntity | null) => void;
	}

	let {
		entities = [],
		value = null,
		placeholder = 'Search entities...',
		disabled = false,
		clearable = false,
		class: className = '',
		containerClass = '',
		isLoading = false,
		onValueChange
	}: Props = $props();

	let isOpen = $state(false);
	let searchTerm = $state('');
	let inputElement: HTMLInputElement;

	let highlightedIndex = $state(-1);

	// Filter entities based on search term
	let filteredEntities = $derived(() => {
		if (!searchTerm.trim()) return entities;
		const term = searchTerm.toLowerCase();
		return entities.filter(
			(entity) =>
				entity.title.toLowerCase().includes(term) ||
				entity.description?.toLowerCase().includes(term) ||
				entity.providerId.toLowerCase().includes(term)
		);
	});

	// Input value for display
	let inputValue = $state('');

	// Update input value when value changes
	$effect(() => {
		if (value) {
			inputValue = value.title;
		} else {
			inputValue = '';
		}
	});

	function openDropdown() {
		if (disabled) return;
		isOpen = true;
		searchTerm = '';
		highlightedIndex = -1;
		tick().then(() => {
			inputElement?.focus();
		});
	}

	function closeDropdown() {
		isOpen = false;
		highlightedIndex = -1;
		// Reset input value to selected value
		if (value) {
			inputValue = value.title;
		} else {
			inputValue = '';
		}
		searchTerm = '';
	}

	function selectEntity(entity: UnifiedEntity) {
		onValueChange?.(entity);
		closeDropdown();
	}

	function clearSelection() {
		onValueChange?.(null);
		closeDropdown();
	}

	function handleKeydown(event: KeyboardEvent) {
		if (!isOpen) {
			if (event.key === 'Enter' || event.key === ' ' || event.key === 'ArrowDown') {
				event.preventDefault();
				openDropdown();
			}
			return;
		}

		switch (event.key) {
			case 'Escape':
				event.preventDefault();
				closeDropdown();
				break;
			case 'ArrowDown':
				event.preventDefault();
				highlightedIndex = Math.min(highlightedIndex + 1, filteredEntities().length - 1);
				break;
			case 'ArrowUp':
				event.preventDefault();
				highlightedIndex = Math.max(highlightedIndex - 1, -1);
				break;
			case 'Enter':
				event.preventDefault();
				if (highlightedIndex >= 0 && highlightedIndex < filteredEntities().length) {
					selectEntity(filteredEntities()[highlightedIndex]);
				}
				break;
		}
	}

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		searchTerm = target.value;
		inputValue = target.value;
		highlightedIndex = -1;
	}
</script>

<div class="relative {className}">
	<!-- Input Field -->
	<div class="relative">
		<div
			class="relative w-full cursor-pointer rounded-md border border-slate-300 bg-white py-1.5 pr-8 pl-3 text-left shadow-sm focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500 dark:border-slate-600 dark:bg-slate-800 {disabled
				? 'cursor-not-allowed opacity-50'
				: ''} {containerClass}"
			onclick={!disabled ? openDropdown : undefined}
		>
			<input
				bind:this={inputElement}
				bind:value={inputValue}
				oninput={handleInput}
				onkeydown={handleKeydown}
				onfocus={openDropdown}
				onblur={() => {
					// Close dropdown after a delay to allow for option selection
					setTimeout(() => {
						closeDropdown();
					}, 150);
				}}
				class="w-full border-none bg-transparent p-0 text-sm text-slate-900 placeholder-slate-400 focus:ring-0 focus:outline-none dark:text-white dark:placeholder-slate-400"
				placeholder={value ? '' : placeholder}
				readonly={!isOpen}
				tabindex={disabled ? -1 : 0}
				{disabled}
			/>

			<div class="absolute inset-y-0 right-0 flex items-center space-x-1 pr-2">
				{#if clearable && value}
					<button
						type="button"
						class="rounded p-1 hover:bg-slate-100 dark:hover:bg-slate-700"
						onclick={(e) => {
							e.stopPropagation();
							clearSelection();
						}}
					>
						<Icon src={XMark} class="h-4 w-4 text-slate-400" />
					</button>
				{/if}
				<!-- <Icon src={ChevronDown} class="h-4 w-4 text-slate-400 {isOpen ? 'rotate-180' : ''} transition-transform" /> -->
			</div>
		</div>
	</div>

	<!-- Dropdown -->
	{#if isOpen}
		<div
			class="ring-opacity-5 absolute z-50 max-h-90 w-full min-w-[26em] overflow-auto rounded-md border border-slate-300 bg-white py-1 text-base shadow-lg focus:outline-none dark:border-slate-600 dark:bg-slate-800"
		>
			{#if isLoading}
				<div
					class="relative cursor-default px-3 py-3 text-slate-500 select-none dark:text-slate-400"
				>
					<div class="flex items-center gap-2">
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-slate-300 border-t-blue-500 dark:border-slate-600"
						></div>
						<span>Loading entities...</span>
					</div>
				</div>
			{:else if filteredEntities().length === 0}
				<div
					class="relative cursor-default px-3 py-2 text-slate-500 select-none dark:text-slate-400"
				>
					{searchTerm ? 'No entities found' : 'No entities available'}
				</div>
			{:else}
				{#each filteredEntities() as entity, index}
					<div
						class="cursor-pointer {highlightedIndex === index
							? 'bg-blue-50 dark:bg-blue-900/20'
							: 'hover:bg-slate-50 dark:hover:bg-slate-700'}"
						onclick={() => selectEntity(entity)}
						role="option"
						aria-selected={value?.id === entity.id}
					>
						<MatchRow {entity} showActions={false} showHoverCard={false} />
					</div>
				{/each}
			{/if}
		</div>
	{/if}
</div>
