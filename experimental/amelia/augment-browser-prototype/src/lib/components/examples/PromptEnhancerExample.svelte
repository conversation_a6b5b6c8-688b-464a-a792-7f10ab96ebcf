<script lang="ts">
	import PromptEnhancer from '../ui/PromptEnhancer.svelte';
	import type { PromptEnhancementResult } from '$lib/utils/prompts';

	let currentPrompt = $state('');
	let enhancementHistory: Array<{ original: string; enhanced: string; timestamp: Date }> = $state([]);
	let lastError: PromptEnhancementResult | null = $state(null);

	function getCurrentPrompt(): string {
		return currentPrompt;
	}

	function handlePromptEnhanced(enhancedPrompt: string) {
		// Store the enhancement in history
		enhancementHistory = [
			...enhancementHistory,
			{
				original: currentPrompt,
				enhanced: enhancedPrompt,
				timestamp: new Date()
			}
		];

		// Update the current prompt
		currentPrompt = enhancedPrompt;
		lastError = null;
	}

	function handlePromptEnhancementError(error: PromptEnhancementResult) {
		lastError = error;
		console.error('Prompt enhancement error:', error);
	}

	function clearHistory() {
		enhancementHistory = [];
		lastError = null;
	}

	function revertToOriginal(index: number) {
		if (enhancementHistory[index]) {
			currentPrompt = enhancementHistory[index].original;
		}
	}

	function useEnhanced(index: number) {
		if (enhancementHistory[index]) {
			currentPrompt = enhancementHistory[index].enhanced;
		}
	}
</script>

<div class="max-w-4xl mx-auto p-6 space-y-6">
	<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
		<h2 class="text-xl font-semibold text-slate-900 dark:text-white mb-4">
			Prompt Enhancer Example
		</h2>

		<p class="text-slate-600 dark:text-slate-400 mb-6">
			Type a prompt below and click the sparkles icon to enhance it using AI. The enhancer will make your prompt clearer, more specific, and less ambiguous.
		</p>

		<!-- Input Area -->
		<div class="relative">
			<textarea
				bind:value={currentPrompt}
				placeholder="Enter a prompt to enhance... (e.g., 'make the button blue')"
				class="w-full resize-none rounded-lg border border-slate-300 bg-white px-3 py-2 pr-12 text-sm text-slate-900 placeholder-slate-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none dark:border-slate-600 dark:bg-slate-700 dark:text-white dark:placeholder-slate-400"
				rows="4"
			></textarea>
			<div class="absolute right-2 top-2">
				<PromptEnhancer
					{getCurrentPrompt}
					onEnhanced={handlePromptEnhanced}
					onError={handlePromptEnhancementError}
				/>
			</div>
		</div>

		<!-- Error Display -->
		{#if lastError}
			<div class="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
				<p class="text-sm text-red-800 dark:text-red-400">
					<strong>Enhancement Error:</strong> {lastError.errorMessage || 'Unknown error occurred'}
				</p>
			</div>
		{/if}
	</div>

	<!-- Enhancement History -->
	{#if enhancementHistory.length > 0}
		<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
			<div class="flex items-center justify-between mb-4">
				<h3 class="text-lg font-medium text-slate-900 dark:text-white">
					Enhancement History
				</h3>
				<button
					onclick={clearHistory}
					class="text-sm text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
				>
					Clear History
				</button>
			</div>

			<div class="space-y-4">
				{#each enhancementHistory as enhancement, index}
					<div class="border border-slate-200 dark:border-slate-600 rounded-lg p-4">
						<div class="text-xs text-slate-500 dark:text-slate-400 mb-2">
							{enhancement.timestamp.toLocaleString()}
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
									Original:
								</h4>
								<div class="bg-slate-50 dark:bg-slate-700 rounded p-3 text-sm text-slate-600 dark:text-slate-300">
									{enhancement.original}
								</div>
								<button
									onclick={() => revertToOriginal(index)}
									class="mt-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
								>
									Use Original
								</button>
							</div>

							<div>
								<h4 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
									Enhanced:
								</h4>
								<div class="bg-green-50 dark:bg-green-900/20 rounded p-3 text-sm text-slate-600 dark:text-slate-300">
									{enhancement.enhanced}
								</div>
								<button
									onclick={() => useEnhanced(index)}
									class="mt-2 text-xs text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-200"
								>
									Use Enhanced
								</button>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Usage Instructions -->
	<div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-6">
		<h3 class="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
			How to Use
		</h3>
		<ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
			<li>• Type a prompt in the text area above</li>
			<li>• Click the sparkles (✨) icon to enhance your prompt</li>
			<li>• The AI will make your prompt clearer and more specific</li>
			<li>• View the enhancement history to compare original vs enhanced versions</li>
			<li>• Click "Use Original" or "Use Enhanced" to switch between versions</li>
		</ul>
	</div>
</div>
