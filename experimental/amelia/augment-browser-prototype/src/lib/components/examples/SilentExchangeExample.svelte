<script lang="ts">
	import { sendSilentExchange, sendSilentExchangeStream } from '$lib/api/chat';
	import type { SilentExchangeRequest } from '$lib/api/chat';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { PaperAirplane } from 'svelte-hero-icons';

	let message = $state('Explain this code snippet');
	let selectedCode = $state('function hello() {\n  console.log("Hello, world!");\n}');
	let path = $state('example.js');
	let lang = $state('javascript');
	let response = $state('');
	let streamingResponse = $state('');
	let isLoading = $state(false);
	let isStreaming = $state(false);

	async function sendNonStreamingRequest() {
		if (!message.trim() || isLoading) return;

		isLoading = true;
		response = '';

		try {
			const request: SilentExchangeRequest = {
				message: message.trim(),
				selectedCode: selectedCode.trim() || undefined,
				path: path.trim() || undefined,
				lang: lang.trim() || undefined,
				mode: 0 // CHAT mode
			};

			const result = await sendSilentExchange(request);
			response = result.text;
		} catch (error) {
			console.error('Silent exchange error:', error);
			response = `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
		} finally {
			isLoading = false;
		}
	}

	async function sendStreamingRequest() {
		if (!message.trim() || isStreaming) return;

		isStreaming = true;
		streamingResponse = '';

		try {
			const request: SilentExchangeRequest = {
				message: message.trim(),
				selectedCode: selectedCode.trim() || undefined,
				path: path.trim() || undefined,
				lang: lang.trim() || undefined,
				mode: 0 // CHAT mode
			};

			await sendSilentExchangeStream(request, (chunk) => {
				if (chunk.type === 'content_delta' && chunk.text) {
					streamingResponse += chunk.text;
				} else if (chunk.type === 'error') {
					console.error('Streaming error:', chunk.error);
					streamingResponse += `\n\nError: ${chunk.error}`;
				} else if (chunk.type === 'done') {
					console.log('Streaming completed');
				}
			});
		} catch (error) {
			console.error('Silent exchange stream error:', error);
			streamingResponse += `\n\nError: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
		} finally {
			isStreaming = false;
		}
	}
</script>

<div class="max-w-4xl mx-auto p-6 space-y-6">
	<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
		<h2 class="text-xl font-semibold text-slate-900 dark:text-white mb-4">
			Silent Exchange API Example
		</h2>

		<div class="space-y-4">
			<!-- Message Input -->
			<div>
				<label for="message-input" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
					Message
				</label>
				<textarea
					id="message-input"
					bind:value={message}
					placeholder="Enter your message..."
					class="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
					rows="2"
				></textarea>
			</div>

			<!-- Selected Code Input -->
			<div>
				<label for="code-input" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
					Selected Code (optional)
				</label>
				<textarea
					id="code-input"
					bind:value={selectedCode}
					placeholder="Enter code snippet..."
					class="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 font-mono"
					rows="4"
				></textarea>
			</div>

			<!-- Path and Language -->
			<div class="grid grid-cols-2 gap-4">
				<div>
					<label for="path-input" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
						Path (optional)
					</label>
					<input
						id="path-input"
						bind:value={path}
						placeholder="example.js"
						class="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
					/>
				</div>
				<div>
					<label for="lang-input" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
						Language (optional)
					</label>
					<input
						id="lang-input"
						bind:value={lang}
						placeholder="javascript"
						class="w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-900 dark:text-white placeholder-slate-500 dark:placeholder-slate-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
					/>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex gap-3">
				<Button
					variant="primary"
					icon={PaperAirplane}
					onclick={sendNonStreamingRequest}
					disabled={isLoading || !message.trim()}
				>
					{isLoading ? 'Sending...' : 'Send Non-Streaming'}
				</Button>

				<Button
					variant="secondary"
					icon={PaperAirplane}
					onclick={sendStreamingRequest}
					disabled={isStreaming || !message.trim()}
				>
					{isStreaming ? 'Streaming...' : 'Send Streaming'}
				</Button>
			</div>
		</div>
	</div>

	<!-- Non-Streaming Response -->
	{#if response}
		<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
			<h3 class="text-lg font-medium text-slate-900 dark:text-white mb-3">
				Non-Streaming Response
			</h3>
			<div class="prose prose-slate dark:prose-invert max-w-none">
				<pre class="whitespace-pre-wrap text-sm">{response}</pre>
			</div>
		</div>
	{/if}

	<!-- Streaming Response -->
	{#if streamingResponse}
		<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
			<h3 class="text-lg font-medium text-slate-900 dark:text-white mb-3">
				Streaming Response
				{#if isStreaming}
					<span class="inline-block w-2 h-4 bg-blue-600 animate-pulse ml-2"></span>
				{/if}
			</h3>
			<div class="prose prose-slate dark:prose-invert max-w-none">
				<pre class="whitespace-pre-wrap text-sm">{streamingResponse}</pre>
			</div>
		</div>
	{/if}
</div>
