<script lang="ts">
	import { getContext } from 'svelte';
	import type { ScaleLinear, ScaleTime, ScaleBand } from 'd3';

	interface DataPoint {
		x: any;
		y: any;
		[key: string]: any;
	}

	interface Props {
		data: DataPoint[];
		fill?: string | ((d: DataPoint) => string);
		stroke?: string | ((d: DataPoint) => string);
		strokeWidth?: number;
		opacity?: number | ((d: DataPoint) => number);
		className?: string;
		orientation?: 'vertical' | 'horizontal';
	}

	let {
		data,
		fill = '#3b82f6',
		stroke = 'none',
		strokeWidth = 1,
		opacity = 1,
		className = '',
		orientation = 'vertical'
	}: Props = $props();

	// Get chart context
	const chart = getContext('chart') as {
		innerHeight: number;
		xScale: ScaleLinear<number, number> | ScaleTime<number, number> | ScaleBand<string>;
		yScale: ScaleLinear<number, number> | ScaleBand<string>;
	};

	function getBarProps(d: DataPoint) {
		if (orientation === 'vertical') {
			const xScale = chart.xScale;
			const yScale = chart.yScale as ScaleLinear<number, number>;

			let x: number;
			let width: number;

			if ('bandwidth' in xScale) {
				// ScaleBand
				const scale = xScale as ScaleBand<string>;
				x = scale(String(d.x)) || 0;
				width = scale.bandwidth();
			} else {
				// ScaleLinear or ScaleTime - assume we want bars with some default width
				x = (xScale as ScaleLinear<number, number> | ScaleTime<number, number>)(d.x) - 5;
				width = 10;
			}

			const y = yScale(d.y);
			const height = chart.innerHeight - y;

			return { x, y, width, height };
		} else {
			// Horizontal bars
			const xScale = chart.xScale as ScaleLinear<number, number>;
			const yScale = chart.yScale;

			let y: number;
			let height: number;

			if ('bandwidth' in yScale) {
				// ScaleBand
				const scale = yScale as ScaleBand<string>;
				y = scale(String(d.y)) || 0;
				height = scale.bandwidth();
			} else {
				// ScaleLinear - assume we want bars with some default height
				y = (yScale as ScaleLinear<number, number>)(d.y) - 5;
				height = 10;
			}

			const x = 0;
			const width = xScale(d.x);

			return { x, y, width, height };
		}
	}

	function getValue<T>(value: T | ((d: DataPoint) => T), d: DataPoint): T {
		return typeof value === 'function' ? (value as (d: DataPoint) => T)(d) : value;
	}
</script>

{#each data as d}
	{@const barProps = getBarProps(d)}
	<rect
		x={barProps.x}
		y={barProps.y}
		width={barProps.width}
		height={barProps.height}
		fill={getValue(fill, d)}
		stroke={getValue(stroke, d)}
		stroke-width={strokeWidth}
		opacity={getValue(opacity, d)}
		class={className}
	/>
{/each}
