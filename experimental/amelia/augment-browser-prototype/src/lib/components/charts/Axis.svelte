<script lang="ts">
	import { getContext } from 'svelte';
	import type { ScaleLinear, ScaleTime, ScaleBand } from 'd3';

	interface Props {
		orientation: 'top' | 'bottom' | 'left' | 'right';
		tickCount?: number;
		tickSize?: number;
		tickPadding?: number;
		tickFormat?: (value: any) => string;
		className?: string;
		showDomain?: boolean;
		showTicks?: boolean;
		showLabels?: boolean;
	}

	let {
		orientation,
		tickCount = 5,
		tickSize = 6,
		tickPadding = 3,
		tickFormat,
		className = '',
		showDomain = true,
		showTicks = true,
		showLabels = true
	}: Props = $props();

	// Get chart context
	const chart = getContext('chart') as {
		innerWidth: number;
		innerHeight: number;
		xScale: ScaleLinear<number, number> | ScaleTime<number, number> | ScaleBand<string>;
		yScale: ScaleLinear<number, number> | ScaleBand<string>;
	};

	let scale = $derived(orientation === 'top' || orientation === 'bottom' ? chart.xScale : chart.yScale);

	let ticks = $derived((() => {
		if (!scale) return [];
		// const s = scale();
		// if ('ticks' in s) {
			// ScaleLinear or ScaleTime
			return (scale as ScaleLinear<number, number> | ScaleTime<number, number>).ticks(tickCount);
		// } else if ('domain' in s) {
		// 	// ScaleBand
		// 	return (s as ScaleBand<string>).domain();
		// }
		return [];
	})());

	let transform = $derived((() => {
		switch (orientation) {
			case 'top':
				return 'translate(0, 0)';
			case 'bottom':
				return `translate(0, ${chart.innerHeight})`;
			case 'left':
				return 'translate(0, 0)';
			case 'right':
				return `translate(${chart.innerWidth}, 0)`;
		}
	})());

	let isHorizontal = $derived(orientation === 'top' || orientation === 'bottom');

	function getTickPosition(tick: any) {
		if (!scale) return 0;
		// const s = scale();
		// if ('bandwidth' in s) {
		// 	// ScaleBand
		// 	const pos = (s as ScaleBand<string>)(String(tick));
		// 	return pos !== undefined ? pos + (s as ScaleBand<string>).bandwidth() / 2 : 0;
		// } else {
			// ScaleLinear or ScaleTime
			return (scale as ScaleLinear<number, number> | ScaleTime<number, number>)(tick);
		// }
	}

	function formatTick(tick: any) {
		if (tickFormat) {
			return tickFormat(tick);
		}
		if (tick instanceof Date) {
			return tick.toLocaleDateString();
		}
		return String(tick);
	}
</script>

<g {transform} class="axis axis-{orientation} {className}">
	{#if showDomain}
		<!-- Domain line -->
		{#if isHorizontal}
			<line x1="0" y1="0" x2={chart.innerWidth} y2="0" stroke="currentColor" />
		{:else}
			<line x1="0" y1="0" x2="0" y2={chart.innerHeight} stroke="currentColor" />
		{/if}
	{/if}

	{#if showTicks || showLabels}
		{#each ticks as tick}
			{@const tickPos = getTickPosition(tick)}
			<g transform={isHorizontal ? `translate(${tickPos}, 0)` : `translate(0, ${tickPos})`}>
				{#if showTicks}
					<!-- Tick mark -->
					{#if isHorizontal}
						<line
							x1="0"
							y1="0"
							x2="0"
							y2={orientation === 'top' ? -tickSize : tickSize}
							stroke="currentColor"
						/>
					{:else}
						<line
							x1="0"
							y1="0"
							x2={orientation === 'left' ? -tickSize : tickSize}
							y2="0"
							stroke="currentColor"
						/>
					{/if}
				{/if}

				{#if showLabels}
					<!-- Tick label -->
					{#if isHorizontal}
						<text
							x="0"
							y={orientation === 'top' ? -tickSize - tickPadding : tickSize + tickPadding}
							text-anchor="middle"
							dominant-baseline={orientation === 'top' ? 'auto' : 'hanging'}
							fill="currentColor"
							font-size="12"
						>
							{formatTick(tick)}
						</text>
					{:else}
						<text
							x={orientation === 'left' ? -tickSize - tickPadding : tickSize + tickPadding}
							y="0"
							text-anchor={orientation === 'left' ? 'end' : 'start'}
							dominant-baseline="middle"
							fill="currentColor"
							font-size="12"
						>
							{formatTick(tick)}
						</text>
					{/if}
				{/if}
			</g>
		{/each}
	{/if}
</g>

<style>
	.axis {
		color: #6b7280;
	}
</style>
