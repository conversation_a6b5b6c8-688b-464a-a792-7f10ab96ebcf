<script lang="ts">
	import { getContext } from 'svelte';
	import { line } from 'd3';
	import type { ScaleLinear, ScaleTime, ScaleBand } from 'd3';

	interface DataPoint {
		x: any;
		y: any;
	}

	interface Props {
		data: DataPoint[];
		stroke?: string;
		strokeWidth?: number;
		fill?: string;
		curve?: any;
		className?: string;
		xAccessor?: (d: any) => any;
		yAccessor?: (d: any) => any;
	}

	let {
		data,
		stroke = '#3b82f6',
		strokeWidth = 2,
		fill = 'none',
		curve,
		className = '',
		xAccessor = d => d.x,
		yAccessor = d => d.y
	}: Props = $props();

	// Get chart context
	const chart = getContext('chart') as {
		xScale: ScaleLinear<number, number> | ScaleTime<number, number> | ScaleBand<string>;
		yScale: ScaleLinear<number, number> | ScaleBand<string>;
	};

	// Create D3 line generator
	let lineGenerator = $derived(() => {
		const generator = line<DataPoint>()
			.x(d => {
				const scale = chart.xScale;
				if ('bandwidth' in scale) {
					// ScaleBand
					return (scale as ScaleBand<string>)(String(xAccessor(d))) || 0;
				} else {
					// ScaleLinear or ScaleTime
					return (scale as ScaleLinear<number, number> | ScaleTime<number, number>)(xAccessor(d));
				}
			})
			.y(d => {
				const scale = chart.yScale;
				if ('bandwidth' in scale) {
					// ScaleBand
					return (scale as ScaleBand<string>)(String(yAccessor(d))) || 0;
				} else {
					// ScaleLinear
					return (scale as ScaleLinear<number, number>)(yAccessor(d));
				}
			});

		if (curve) {
			generator.curve(curve);
		}

		return generator;
	});

	let pathData = $derived(lineGenerator()(data) || '');
</script>

<path
	d={pathData}
	{stroke}
	stroke-width={strokeWidth}
	{fill}
	class={className}
/>
