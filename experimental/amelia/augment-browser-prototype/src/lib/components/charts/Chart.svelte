<script lang="ts">
	import { setContext } from 'svelte';
	import type { ScaleLinear, ScaleTime, ScaleBand } from 'd3';

	interface Props {
		width?: number;
		height?: number;
		margin?: { top: number; right: number; bottom: number; left: number };
		xScale?: ScaleLinear<number, number> | ScaleTime<number, number> | ScaleBand<string>;
		yScale?: ScaleLinear<number, number> | ScaleBand<string>;
		children?: any;
	}

	let {
		width = 600,
		height = 400,
		margin = { top: 20, right: 20, bottom: 40, left: 40 },
		xScale,
		yScale,
		children
	}: Props = $props();

	// Calculate inner dimensions
	let innerWidth = $derived(width - margin.left - margin.right);
	let innerHeight = $derived(height - margin.top - margin.bottom);

	// Set context for child components
	setContext('chart', {
		get width() { return width; },
		get height() { return height; },
		get margin() { return margin; },
		get innerWidth() { return innerWidth; },
		get innerHeight() { return innerHeight; },
		get xScale() { return xScale; },
		get yScale() { return yScale; }
	});
</script>

<div class="chart-container">
	<svg {width} {height}>
		<g transform="translate({margin.left}, {margin.top})">
			{@render children?.()}
		</g>
	</svg>
</div>

<style>
	.chart-container {
		display: inline-block;
	}

	svg {
		overflow: visible;
	}
</style>
