<script lang="ts">
	import { fly } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { Icon, MagnifyingGlass } from 'svelte-hero-icons';

	import type { CleanChatExchangeData, CleanChangedFile } from '$lib/api/unified-client';
	import type { ChatHistoryGroup } from './utils';
	import ChangedFilesModal from './ChangedFilesModal.svelte';
	import { aggregateChangedFiles, getAggregatedFilePath } from '$lib/utils/file-aggregation';

	interface Props {
		group: ChatHistoryGroup;
		exchanges: CleanChatExchangeData[];
		exchangeIndex?: number;
		onUpdateCodeView?: (exchangeIndex: number) => void;
		hideContainer?: boolean;
	}

	let {
		group,
		exchanges,
		exchangeIndex,
		onUpdateCodeView,
		hideContainer = false
	}: Props = $props();

	// Modal state
	let showChangedFilesModal = $state(false);

	// Calculate statistics for all exchanges in the group
	const stats = $derived.by(() => {
		let toolsCalled = 0;
		let memoriesRecalled = 0;
		let newMemoriesCreated = 0;

		// Create a map from exchange ID to original exchange for quick lookup
		const exchangeMap = new Map<string, CleanChatExchangeData>();
		exchanges.forEach((exchange) => {
			if (exchange.exchange?.requestId) {
				exchangeMap.set(exchange.exchange.requestId, exchange);
			}
		});

		// Iterate through all exchanges in the group
		for (const processedExchange of group.exchanges) {
			// Find the original exchange data
			const originalExchange = exchangeMap.get(processedExchange.id);

			if (originalExchange) {
				// Count unique files modified from tool calls as fallback
				const responseNodes = originalExchange.exchange?.responseNodes || [];
				const fileModifyingTools = responseNodes.filter(
					(node: any) =>
						node.toolUse &&
						['str-replace-editor', 'save-file', 'remove-files'].includes(
							node.toolUse.toolName || ''
						)
				);

				// Count tools called
				toolsCalled += responseNodes.filter((node: any) => node.toolUse).length;

				// Count memories (agent memory nodes)
				const memoryNodes = responseNodes.filter(
					(node: any) => node.agentMemory || node.agent_memory
				);

				memoryNodes.forEach((node: any) => {
					const memory = node.agentMemory || node.agent_memory;
					if (memory) {
						if (memory.isFlushed) {
							memoriesRecalled++;
						} else {
							newMemoriesCreated++;
						}
					}
				});
			}
		}

		const groupExchangeIds = group.exchanges.map((e) => e.sequenceId);
		const groupFullExchanges = exchanges.filter((e) => groupExchangeIds.includes(e.sequenceId));
		// need to convert from
		// 	{
		// 		"oldPath": "research/test_ci_failure.py",
		// 		"newPath": "research/test_ci_failure.py",
		// 		"changeType": 2,
		// 		"oldContents": "#!/usr/bin/env python3\n\"\"\"\nTest file to trigger CI failure for testing purposes.\nThis file contains intentionally failing tests.\n\"\"\"\n\nimport pytest\n\n\ndef test_intentional_failure():\n    \"\"\"This test is designed to fail to trigger CI failure.\"\"\"\n    assert False, \"This test is intentionally failing to trigger CI failure for testing\"\n\n\ndef test_another_failure():\n    \"\"\"Another failing test to ensure CI catches it.\"\"\"\n    raise Exception(\"Intentional exception to cause CI failure\")\n\n\ndef test_assertion_error():\n    \"\"\"Test with assertion error.\"\"\"\n    expected = \"success\"\n    actual = \"failure\"\n    assert expected == actual, f\"Expected {expected} but got {actual}\"\n\n\nif __name__ == \"__main__\":\n    # Run the tests if executed directly\n    pytest.main([__file__])\n",
		// 		"newContents": "#!/usr/bin/env python3\n\"\"\"\nTest file to trigger CI failure for testing purposes.\nThis file contains intentionally failing tests.\n\"\"\"\n\nimport pytest\n\n# Syntax error to cause immediate failure\nthis is not valid python syntax !!!\n\n\ndef test_intentional_failure():\n    \"\"\"This test is designed to fail to trigger CI failure.\"\"\"\n    assert False, \"This test is intentionally failing to trigger CI failure for testing\"\n\n\ndef test_another_failure():\n    \"\"\"Another failing test to ensure CI catches it.\"\"\"\n    raise Exception(\"Intentional exception to cause CI failure\")\n\n\ndef test_assertion_error():\n    \"\"\"Test with assertion error.\"\"\"\n    expected = \"success\"\n    actual = \"failure\"\n    assert expected == actual, f\"Expected {expected} but got {actual}\"\n\n\nif __name__ == \"__main__\":\n    # Run the tests if executed directly\n    pytest.main([__file__])\n"
		// }
		// to CleanChangedFile[]
		const changedFiles = groupFullExchanges.map((e) => e.changedFiles || []).flat();
		const convertChangedFileFromAPI = (apiFile: any): CleanChangedFile => {
			return {
				path: apiFile.newPath || apiFile.path || '',
				oldPath: apiFile.oldPath,
				content: apiFile.newContents,
				oldContent: apiFile.oldContents,
				changeType: apiFile.changeType,
				diff: apiFile.diff,
				linesAdded: apiFile.linesAdded,
				linesDeleted: apiFile.linesDeleted
			};
		};
		const cleanChangedFiles = changedFiles.map(convertChangedFileFromAPI);
		let allChangedFiles = aggregateChangedFiles([cleanChangedFiles]);
		let linesWritten = allChangedFiles.reduce((acc, file) => acc + (file.linesAdded || 0), 0);
		let filesModified = new Set(allChangedFiles.map((file) => getAggregatedFilePath(file)));

		console.log('allChangedFiles', allChangedFiles, group, allChangedFiles, groupFullExchanges);

		return {
			linesWritten,
			filesModified: filesModified.size,
			toolsCalled,
			memoriesRecalled,
			newMemoriesCreated,
			allChangedFiles
		};
	});

	// Format numbers with appropriate suffixes
	function formatNumber(num: number): string {
		if (num >= 1000) {
			return (num / 1000).toFixed(1) + 'k';
		}
		return num.toString();
	}

	// Handle click on changed files
	function handleFilesClick() {
		showChangedFilesModal = true;
	}

	let hasEdits = $derived(stats.linesWritten > 0 || stats.filesModified > 0);
	let hasMemories = $derived(stats.memoriesRecalled > 0 || stats.newMemoriesCreated > 0);
	let hasChangedFiles = $derived(stats.allChangedFiles.length > 0);
</script>

{#if hideContainer}
	<!-- Content without container (for use in combined layout) -->
	<div class="flex items-center justify-between">
		<!-- Left side: Changed files button (if any) -->
		<!-- {#if hasChangedFiles}
			<button
				class="flex items-center gap-2 rounded-md px-3 py-1.5 text-xs text-slate-600 transition-colors hover:bg-slate-100 hover:text-slate-900 dark:text-slate-400 dark:hover:bg-slate-700 dark:hover:text-slate-100"
				onclick={handleFilesClick}
			>
				<Icon src={MagnifyingGlass} class="h-3.5 w-3.5" micro />
				<span class="font-medium">Changed {stats.filesModified} files</span>
			</button>
		{:else}
			<div></div>
		{/if} -->

		<!-- Right side: Turn summary stats -->
		<div
			class="ml-auto flex items-center divide-x divide-slate-200 text-xs text-slate-500 dark:divide-slate-700 dark:text-slate-400"
		>
			{#if hasEdits}
				<span class="flex items-center gap-1 px-3">
					{#if stats.linesWritten > 0}
						<span class="font-medium text-slate-600 dark:text-slate-300"
							>{formatNumber(stats.linesWritten)}</span
						>
						<span>lines</span>
					{/if}
					{#if stats.linesWritten > 0 && stats.filesModified > 0}
						<span class="text-slate-400">·</span>
					{/if}
					{#if stats.filesModified > 0}
						<button
							class="-mx-2 flex cursor-pointer items-center gap-1 rounded px-2.5 py-1 transition-colors hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-700 dark:hover:text-slate-100"
							onclick={handleFilesClick}
							title="View changed files"
						>
							Changed
							<span class="font-medium text-slate-600 dark:text-slate-300"
								>{formatNumber(stats.filesModified)}</span
							>
							<span>files</span>
						</button>
					{/if}
				</span>
			{/if}

			{#if hasMemories}
				<span class="flex items-center gap-1 px-3">
					{#if stats.memoriesRecalled > 0}
						<span class="font-medium text-slate-600 dark:text-slate-300"
							>{formatNumber(stats.memoriesRecalled)}</span
						>
						<span>recalled</span>
					{/if}
					{#if stats.memoriesRecalled > 0 && stats.newMemoriesCreated > 0}
						<span class="text-slate-400">·</span>
					{/if}
					{#if stats.newMemoriesCreated > 0}
						<span class="font-medium text-slate-600 dark:text-slate-300"
							>{formatNumber(stats.newMemoriesCreated)}</span
						>
						<span>created</span>
					{/if}
				</span>
			{/if}

			{#if stats.toolsCalled > 0}
				<span class="flex items-center gap-1 px-3">
					Used
					<span class="font-medium text-slate-600 dark:text-slate-300"
						>{formatNumber(stats.toolsCalled)}</span
					>
					<span>tools</span>
				</span>
			{/if}
		</div>
	</div>
{:else}
	<!-- Full container with border and background -->
	<div class="mt-4" in:fly={{ y: 10, duration: 300, easing: quintOut }}>
		<div
			class="border-t border-slate-200/60 bg-slate-50/30 dark:border-slate-700/60 dark:bg-slate-800/20"
		>
			<div class="flex items-center justify-center px-1 py-3">
				<!-- Turn summary stats -->
				<div
					class="flex items-center divide-x divide-slate-200 text-xs text-slate-500 dark:divide-slate-700 dark:text-slate-400"
				>
					{#if hasEdits}
						<span class="flex items-center gap-1 px-3">
							{#if stats.linesWritten > 0}
								<span class="font-medium text-slate-600 dark:text-slate-300"
									>{formatNumber(stats.linesWritten)}</span
								>
								<span>lines</span>
							{/if}
							{#if stats.linesWritten > 0 && stats.filesModified > 0}
								<span class="text-slate-400">·</span>
							{/if}
							{#if stats.filesModified > 0}
								{#if hasChangedFiles}
									<button
										class="flex items-center gap-1 rounded px-1 transition-colors hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-700 dark:hover:text-slate-100"
										onclick={handleFilesClick}
										title="View changed files"
									>
										<Icon src={MagnifyingGlass} class="h-3 w-3" micro />
										<span class="font-medium text-slate-600 dark:text-slate-300"
											>{formatNumber(stats.filesModified)}</span
										>
										<span>files</span>
									</button>
								{:else}
									<span class="font-medium text-slate-600 dark:text-slate-300"
										>{formatNumber(stats.filesModified)}</span
									>
									<span>files</span>
								{/if}
							{/if}
						</span>
					{/if}

					{#if hasMemories}
						<span class="flex items-center gap-1 px-3">
							{#if stats.memoriesRecalled > 0}
								<span class="font-medium text-slate-600 dark:text-slate-300"
									>{formatNumber(stats.memoriesRecalled)}</span
								>
								<span>recalled</span>
							{/if}
							{#if stats.memoriesRecalled > 0 && stats.newMemoriesCreated > 0}
								<span class="text-slate-400">·</span>
							{/if}
							{#if stats.newMemoriesCreated > 0}
								<span class="font-medium text-slate-600 dark:text-slate-300"
									>{formatNumber(stats.newMemoriesCreated)}</span
								>
								<span>created</span>
							{/if}
						</span>
					{/if}

					{#if stats.toolsCalled > 0}
						<span class="flex items-center gap-1 px-3">
							Used
							<span class="font-medium text-slate-600 dark:text-slate-300"
								>{formatNumber(stats.toolsCalled)}</span
							>
							<span>tools</span>
						</span>
					{/if}
				</div>
			</div>
		</div>
	</div>
{/if}

{#if showChangedFilesModal}
	<!-- Changed Files Modal -->
	<ChangedFilesModal
		isOpen
		changedFiles={stats.allChangedFiles}
		{exchanges}
		onClose={() => (showChangedFilesModal = false)}
	/>
{/if}
