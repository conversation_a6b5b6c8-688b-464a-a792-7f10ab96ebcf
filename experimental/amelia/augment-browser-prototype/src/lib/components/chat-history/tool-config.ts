/**
 * Configuration for tool display in chat history
 * Maps tool names to their display properties and behavior
 */

import { GitHub } from '$lib/icons/GitHubIcon.svelte';
import { Linear } from '$lib/icons/LinearIcon.svelte';
import {
	DocumentText,
	PencilSquare,
	CodeBracket,
	Trash,
	DocumentDuplicate,
	MagnifyingGlass,
	CircleStack,
	CommandLine,
	ArrowPath,
	BugAnt,
	Play,
	Stop,
	GlobeAlt,
	Server,
	Photo,
	Film,
	MusicalNote,
	ChatBubbleLeftRight,
	CpuChip,
	CloudArrowUp,
	CloudArrowDown,
	Cog6Tooth,
	FolderOpen,
	Eye,
	WrenchScrewdriver,
	Bolt
} from 'svelte-hero-icons';

export interface ToolConfig {
	icon: any;
	label: string;
	description?: string;
	keyParams?: string[]; // Priority order for extracting key info
	category?: 'file' | 'search' | 'dev' | 'web' | 'api' | 'media' | 'ai' | 'system';
}

export const TOOL_CONFIGS: Record<string, ToolConfig> = {
	// File Operations
	view: {
		icon: DocumentText,
		label: 'Looked at file',
		keyParams: ['path', 'file_path'],
		category: 'file'
	},
	'save-file': {
		icon: PencilSquare,
		label: 'Created file',
		keyParams: ['path', 'file_path'],
		category: 'file'
	},
	'str-replace-editor': {
		icon: CodeBracket,
		label: 'Edited file',
		keyParams: ['path', 'file_path'],
		category: 'file'
	},
	'remove-files': {
		icon: Trash,
		label: 'Deleted files',
		keyParams: ['file_paths', 'path'],
		category: 'file'
	},
	copy: {
		icon: DocumentDuplicate,
		label: 'Copied file',
		keyParams: ['source', 'destination', 'path'],
		category: 'file'
	},

	// Search Operations
	'grep-search': {
		icon: MagnifyingGlass,
		label: 'Searched files',
		keyParams: ['query', 'directory_absolute_path'],
		category: 'search'
	},
	'web-search': {
		icon: GlobeAlt,
		label: 'Searched web',
		keyParams: ['query'],
		category: 'search'
	},
	'codebase-retrieval': {
		icon: CircleStack,
		label: 'Searched codebase',
		keyParams: ['information_request'],
		category: 'search'
	},
	'git-commit-retrieval': {
		icon: ArrowPath,
		label: 'Searched git history',
		keyParams: ['information_request'],
		category: 'search'
	},

	// Development Tools
	'setup-script': {
		icon: WrenchScrewdriver,
		label: 'Executed setup script',
		keyParams: ['script_content', 'content'],
		category: 'dev'
	},
	'launch-process': {
		icon: CommandLine,
		label: 'Ran command',
		keyParams: ['command', 'cwd'],
		category: 'dev'
	},
	'read-process': {
		icon: Eye,
		label: 'Read process output',
		keyParams: ['terminal_id'],
		category: 'dev'
	},
	'write-process': {
		icon: PencilSquare,
		label: 'Sent input to process',
		keyParams: ['input_text', 'terminal_id'],
		category: 'dev'
	},
	'kill-process': {
		icon: Stop,
		label: 'Stopped process',
		keyParams: ['terminal_id'],
		category: 'dev'
	},
	'list-processes': {
		icon: CommandLine,
		label: 'Listed processes',
		keyParams: [],
		category: 'dev'
	},
	'read-terminal': {
		icon: CommandLine,
		label: 'Read terminal',
		keyParams: [],
		category: 'dev'
	},
	diagnostics: {
		icon: BugAnt,
		label: 'Checked for issues',
		keyParams: ['paths'],
		category: 'dev'
	},

	// Web Operations
	'web-fetch': {
		icon: GlobeAlt,
		label: 'Fetched webpage',
		keyParams: ['url'],
		category: 'web'
	},
	'open-browser': {
		icon: GlobeAlt,
		label: 'Opened browser',
		keyParams: ['url'],
		category: 'web'
	},

	// AI/Agent Operations
	'spawn-sub-agent': {
		icon: ChatBubbleLeftRight,
		label: 'Created sub-agent',
		keyParams: ['summary', 'prompt'],
		category: 'ai'
	},
	remember: {
		icon: CpuChip,
		label: 'Saved memory',
		keyParams: ['memory'],
		category: 'ai'
	},

	// Media Operations
	'render-mermaid': {
		icon: Photo,
		label: 'Rendered diagram',
		keyParams: ['title'],
		category: 'media'
	},

	// System Operations
	upload: {
		icon: CloudArrowUp,
		label: 'Uploaded file',
		keyParams: ['path', 'destination'],
		category: 'system'
	},
	download: {
		icon: CloudArrowDown,
		label: 'Downloaded file',
		keyParams: ['url', 'path'],
		category: 'system'
	},

	// integrations
	'github-api': {
		icon: GitHub,
		label: 'Talked to GitHub',
		keyParams: ['summary', 'path'],
		category: 'api'
	},
	glean: {
		icon: Server,
		label: 'Searched Glean',
		keyParams: ['query'],
		category: 'api'
	},
	'linear-api': {
		icon: Linear,
		label: 'Talked to Linear',
		keyParams: ['summary', 'path'],
		category: 'api'
	},
	'jira-api': {
		icon: Server,
		label: 'Talked to Jira',
		keyParams: ['summary', 'path'],
		category: 'api'
	},
	'notion-api': {
		icon: Server,
		label: 'Talked to Notion',
		keyParams: ['summary', 'path'],
		category: 'api'
	},
	'glean-api': {
		icon: Server,
		label: 'Talked to Glean',
		keyParams: ['summary', 'path'],
		category: 'api'
	},
	'supabase-api': {
		icon: Server,
		label: 'Talked to Supabase',
		keyParams: ['summary', 'path'],
		category: 'api'
	}
};

// Default configuration for unknown tools
export const DEFAULT_TOOL_CONFIG: ToolConfig = {
	icon: Cog6Tooth,
	label: 'Used tool',
	keyParams: [],
	category: 'system'
};

/**
 * Get tool configuration by name with fuzzy matching
 */
export function getToolConfig(toolName: string): ToolConfig {
	if (!toolName) return DEFAULT_TOOL_CONFIG;

	const name = toolName.toLowerCase();

	// Exact match first
	if (TOOL_CONFIGS[name]) {
		return TOOL_CONFIGS[name];
	}

	// Fuzzy matching - find tools that contain the name or vice versa
	for (const [configName, config] of Object.entries(TOOL_CONFIGS)) {
		if (name.includes(configName) || configName.includes(name)) {
			return config;
		}
	}

	// Debug logging for unknown tools
	console.warn(`Unknown tool name: "${toolName}" (normalized: "${name}")`);

	// Return default with the original tool name as label
	return {
		...DEFAULT_TOOL_CONFIG,
		label: `Unknown Tool: ${toolName}`
	};
}

/**
 * Extract key description from parameters based on tool config
 */
export function extractKeyDescription(
	toolName: string,
	params: Record<string, any>
): string | null {
	const config = getToolConfig(toolName);

	// Try each key parameter in priority order
	for (const key of config.keyParams || []) {
		const value = params[key];
		if (value !== undefined && value !== null) {
			const stringValue = String(value);
			if (stringValue.length === 0) continue;

			// Special handling for different parameter types
			if (key === 'path' || key === 'file_path') {
				// Show just filename for long paths
				if (stringValue.length > 30) {
					const parts = stringValue.split('/');
					return parts[parts.length - 1];
				}
				return stringValue;
			} else if (key === 'url') {
				// Show hostname for URLs
				try {
					const url = new URL(stringValue);
					return url.hostname;
				} catch {
					return stringValue.length > 40 ? stringValue.substring(0, 40) + '...' : stringValue;
				}
			} else {
				// Generic truncation for other parameters
				return stringValue.length > 40 ? stringValue.substring(0, 40) + '...' : stringValue;
			}
		}
	}

	// Fallback: find any meaningful string parameter
	const meaningfulParam = Object.entries(params).find(
		([key, value]) =>
			typeof value === 'string' &&
			value.length > 0 &&
			!['true', 'false', 'null', 'undefined'].includes(value.toLowerCase())
	);

	if (meaningfulParam) {
		const [key, value] = meaningfulParam;
		const stringValue = String(value);
		return stringValue.length > 40 ? stringValue.substring(0, 40) + '...' : stringValue;
	}

	return null;
}
