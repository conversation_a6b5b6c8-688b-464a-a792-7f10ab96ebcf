<script lang="ts">
	import type { CleanChangedFile } from '$lib/api/unified-client';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { DocumentText } from 'svelte-hero-icons';

	interface Props {
		changedFiles: CleanChangedFile[];
		exchangeIndex: number;
		onUpdateCodeView?: (exchangeIndex: number) => void;
	}

	let { changedFiles = [], exchangeIndex, onUpdateCodeView }: Props = $props();

	// Get file names for display
	let fileNames = $derived(
		changedFiles.map((file) => {
			const path = file.path || file.oldPath || '';
			const parts = path.split('/');
			return parts[parts.length - 1] || path;
		})
	);

	// Create summary text
	let summaryText = $derived.by(() => {
		const count = changedFiles.length;
		if (count === 0) return '';

		if (count === 1) {
			return `Changed ${fileNames[0]}`;
		} else if (count <= 3) {
			return `Changed ${fileNames.join(', ')}`;
		} else {
			return `Changed ${count} files`;
		}
	});

	function handleClick() {
		if (onUpdateCodeView) {
			onUpdateCodeView(exchangeIndex);
		}
	}
</script>

{#if changedFiles.length > 0}
	<div class="mt-3 w-full">
		<Button
			variant="outline"
			size="sm"
			onclick={handleClick}
			class="w-full justify-start text-left text-xs text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100"
			icon={DocumentText}
		>
			<span class="truncate">{summaryText}</span>
		</Button>
	</div>
{/if}
