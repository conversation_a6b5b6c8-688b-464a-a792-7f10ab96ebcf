<script lang="ts">
	import { extractInitialInstructionsFromSessionSummary } from '$lib/utils/remote-agent-utils';
	import Tooltip from '../ui/overlays/Tooltip.svelte';
	import type { ChatHistoryGroup } from './utils';

	interface Props {
		groups: ChatHistoryGroup[];
		onSegmentClick?: (groupId: string) => void;
	}

	let { groups = [], onSegmentClick }: Props = $props();

	// Calculate segment properties
	let segments = $derived.by(() => {
		const segments = groups.map((group) => {
			const flatContent = group.exchanges
				.map((ex) => {
					const assistantContent = ex.assistantNodes.map((n) => n.content).join(' ');
					return assistantContent;
				})
				.join(' ');

			// Calculate segment length based on content length and message count
			const contentLength = flatContent.length;

			return {
				id: group.id,
				rawLength: contentLength,
				type: 'assistant',
				summary: group.summary,
				messageCount: group.messageCount,
				timestamp: group.timestamp,
				userMessage: getUserMessage(group),
				group
			};
		});
		const totalLength = segments.reduce((sum, seg) => sum + seg.rawLength, 0);
		console.log('totalLength:', totalLength);

		// Get user message from group for tooltip
		function getUserMessage(group: ChatHistoryGroup): string {
			return (
				extractInitialInstructionsFromSessionSummary(group.exchanges[0].userNodes[0].content) || ''
			);
		}

		// Define minimum segment width as a percentage (ensures tooltips are hoverable)
		const minSegmentPercentage = Math.min(3, 100 / segments.length); // 3% minimum or equal distribution, whichever is smaller

		let runningX = 0;
		return segments.map((seg) => {
			const x = runningX;
			const proportionalLength = (seg.rawLength / totalLength) * 100;
			// Ensure minimum width while preserving proportional distribution for larger segments
			const length = Math.max(proportionalLength, minSegmentPercentage);
			runningX += length;
			return {
				...seg,
				length,
				x
			};
		});
	});

	function handleSegmentClick(segment: (typeof segments)[0]) {
		onSegmentClick?.(segment.id);
	}
</script>

<!-- Progress Indicator Container -->
{#if segments.length > 0}
	<div class="flex w-full items-center">
		{#each segments as segment (segment.id)}
			<div class="" style="flex: {segment.length} 0 0;">
				<Tooltip
					class="flex w-full items-center px-[1px]"
					tabindex={-1}
					text={segment.userMessage}
					placement="top"
					delay={0}
				>
					<button
						onclick={() => handleSegmentClick(segment)}
						class="block h-3 w-full cursor-pointer rounded-full border border-transparent bg-slate-200 hover:border-slate-300"
						role="button"
						tabindex="0"
						onkeydown={(e) => e.key === 'Enter' && handleSegmentClick(segment)}
						aria-label="Chat segment: {segment.summary}"
					/>
				</Tooltip>
			</div>
		{/each}
	</div>
{/if}
