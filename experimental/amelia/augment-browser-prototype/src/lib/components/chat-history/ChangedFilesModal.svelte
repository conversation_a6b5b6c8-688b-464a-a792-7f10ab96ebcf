<script lang="ts">
	import type { CleanChangedFile } from '$lib/api/unified-client';
	import Modal from '$lib/components/ui/overlays/Modal.svelte';
	import CodeStoryWidget from '../ui/widgets/CodeStoryWidget.svelte';

	interface Props {
		isOpen: boolean;
		changedFiles: CleanChangedFile[];
		onClose: () => void;
		exchanges?: any[]; // For generating code story
	}

	let { isOpen, changedFiles, onClose, exchanges = [] }: Props = $props();
</script>

<Modal {isOpen} title="Changed Files" size="lg" {onClose}>
	<div class="h-full overflow-auto p-6">
		{#if changedFiles.length === 0}
			<div class="flex h-32 items-center justify-center">
				<p class="text-slate-500 dark:text-slate-400">No files were changed</p>
			</div>
		{:else}
			<CodeStoryWidget {changedFiles} />
		{/if}
	</div>
</Modal>
