/**
 * Chat History Components
 *
 * A comprehensive chat history system following the chat node formatting guide.
 * Provides structured rendering of chat exchanges with auto-collapse functionality,
 * summary extraction, and proper handling of different node types.
 */

// Main components
export { default as ChatHistoryView } from './ChatHistoryView.svelte';
export { default as ExchangeView } from './ExchangeView.svelte';

// Node renderers
export { default as NodeRenderer } from './nodes/NodeRenderer.svelte';
export { default as TextNode } from './nodes/TextNode.svelte';
export { default as ToolResultNode } from './nodes/ToolResultNode.svelte';
export { default as ToolUseNode } from './nodes/ToolUseNode.svelte';
export { default as ImageNode } from './nodes/ImageNode.svelte';
export { default as NotesNode } from './nodes/NotesNode.svelte';

// Configuration and utilities
export * from './config';
export * from './utils';

// Type exports
export type { ProcessedExchange, ProcessedNode, ChatHistoryGroup } from './utils';

/**
 * Usage Examples:
 *
 * Basic usage:
 * ```svelte
 * <script>
 *   import { ChatHistoryView } from '$lib/components/chat-history';
 *   // TODO: Update to use new global state system
 *   // import { getAgentRawExchanges } from '$lib/stores/global-state.svelte';
 *
 *   let agentId = 'agent-123';
 *   let exchanges = getAgentRawExchanges(agentId);
 * </script>
 *
 * <ChatHistoryView exchanges={$exchanges} />
 * ```
 *
 * With custom configuration:
 * ```svelte
 * <ChatHistoryView
 *   {exchanges}
 *   autoCollapse={true}
 *   showTimestamps={true}
 *   showDebugInfo={false}
 *   maxHeight="500px"
 *   onScrollToBottom={() => console.log('Scrolled to bottom')}
 * />
 * ```
 *
 * Individual exchange rendering:
 * ```svelte
 * <script>
 *   import { ExchangeView, processExchange } from '$lib/components/chat-history';
 *
 *   let rawExchange = { CleanChatExchangeData };
 *   let processedExchange = processExchange(rawExchange, 0);
 * </script>
 *
 * <ExchangeView exchange={processedExchange} />
 * ```
 *
 * Custom node rendering:
 * ```svelte
 * <script>
 *   import { NodeRenderer } from '$lib/components/chat-history';
 *
 *   let node = {
 *     id: 'node-1',
 *     type: 0, // TEXT
 *     content: 'Hello world',
 *     isRequest: true
 *   };
 * </script>
 *
 * <NodeRenderer {node} defaultExpanded={true} />
 * ```
 */
