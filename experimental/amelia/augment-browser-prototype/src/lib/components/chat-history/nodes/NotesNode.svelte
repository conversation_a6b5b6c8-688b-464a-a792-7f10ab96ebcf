<script lang="ts">
	import { I<PERSON>, <PERSON><PERSON><PERSON>, ChevronUp } from 'svelte-hero-icons';
	import type { NotebookNote } from '$lib/types/structured-response';

	interface Props {
		notes: NotebookNote[];
		isExpanded?: boolean;
	}

	let { notes, isExpanded = false }: Props = $props();

	let expanded = $state(isExpanded);

	// Format category name for display
	function formatCategoryName(category: string): string {
		return category.split('_').map(word =>
			word.charAt(0).toUpperCase() + word.slice(1)
		).join(' ');
	}


</script>

<div class="border border-slate-200 dark:border-slate-800 rounded-lg bg-white dark:bg-slate-800 shadow-sm my-2">
	<!-- Header -->
	<button
		class="w-full flex items-center gap-1.5 text-left bg-slate-50 dark:bg-slate-700/30 p-2 rounded-md"
		onclick={() => expanded = !expanded}
	>
		<div class="flex-shrink-0">
			<Icon src={BookO<PERSON>} class="w-3 h-3 text-slate-600 dark:text-slate-400" micro />
		</div>
		<div class="flex-1">
			<div class="text-xs font-medium text-slate-600 dark:text-slate-400">
				Added {notes.length} finding{notes.length === 1 ? '' : 's'} to notebook
			</div>
		</div>
		<div class="flex-shrink-0">
			<Icon
				src={ChevronUp}
				class="w-3 h-3 text-slate-500 dark:text-slate-400 {expanded ? '-rotate-180' : ''} transition-transform duration-200"
				micro
			/>
		</div>
	</button>

	<!-- Expanded Content -->
	{#if expanded}
		<div class="space-y-2 p-3 pb-5">
			{#each notes as note}
				<div class="flex items-start gap-2">
					<div class="flex-shrink-0 mt-3">
						<div class="w-1 h-1 rounded-full bg-slate-300"></div>
					</div>
					<div class="flex-1 min-w-0">
						<!-- Category Badge -->
						<div class="mb-1">
							<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">
								{formatCategoryName(note.category)}
							</span>
						</div>

						<!-- Note Content -->
						<div class="ml-2 text-xs text-slate-500 dark:text-slate-400">
							{note.content}
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
