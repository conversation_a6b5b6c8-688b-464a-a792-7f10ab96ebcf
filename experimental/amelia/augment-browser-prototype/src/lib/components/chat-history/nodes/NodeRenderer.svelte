<script lang="ts">
	import { ChatRequestNodeType, ChatResultNodeType } from '$lib/api/unified-client';
	import TextNode from './TextNode.svelte';
	import ToolResultNode from './ToolResultNode.svelte';
	import ToolUseNode from './ToolUseNode.svelte';
	import ImageNode from './ImageNode.svelte';
	import NotesNode from './NotesNode.svelte';
	import { getRequestNodeConfig, getResponseNodeConfig } from '../config';
	import type { ProcessedNode } from '../utils';

	interface Props {
		node: ProcessedNode;
		requestId?: string;
		defaultExpanded?: boolean;
		showDebugInfo?: boolean;
		isStreaming?: boolean;
		hideCollapseHeader?: boolean;
		toolCalls?: ProcessedNode[];
		isLastNode?: boolean;
	}

	let {
		node,
		requestId,
		defaultExpanded = false,
		showDebugInfo = false,
		isStreaming = false,
		hideCollapseHeader = false,
		toolCalls = [],
		isLastNode = false
	}: Props = $props();

	// Temporary debug: Show raw node data for shell commands
	let isShellCommand = $derived(() => {
		return (
			node.content &&
			typeof node.content === 'string' &&
			(node.content.includes('npm run') ||
				node.content.includes('$ ') ||
				node.content.includes('bash'))
		);
	});

	// Determine node configuration
	let nodeConfig = $derived(
		node.isRequest
			? getRequestNodeConfig(node.type as ChatRequestNodeType)
			: getResponseNodeConfig(node.type as ChatResultNodeType)
	);

	// Determine which component to render based on node type
	function getNodeComponent() {
		// Handle tool result nodes first, regardless of isRequest flag
		// Only route to ToolResultNode if it's actually a TOOL_RESULT type, not an enhanced TOOL_USE
		if (node.type === ChatRequestNodeType.TOOL_RESULT) {
			return ToolResultNode;
		}

		if (node.isRequest) {
			// Request node types
			switch (node.type as ChatRequestNodeType) {
				case ChatRequestNodeType.TEXT:
					return TextNode;
				case ChatRequestNodeType.IMAGE:
				case ChatRequestNodeType.IMAGE_ID:
					return ImageNode;
				case ChatRequestNodeType.IDE_STATE:
				case ChatRequestNodeType.EDIT_EVENTS:
				case ChatRequestNodeType.CHECKPOINT_REF:
					return TextNode; // Fallback to text for these types
				default:
					return TextNode;
			}
		} else {
			// Response node types
			switch (node.type as ChatResultNodeType) {
				case ChatResultNodeType.RAW_RESPONSE:
				case ChatResultNodeType.SUGGESTED_QUESTIONS:
				case ChatResultNodeType.MAIN_TEXT_FINISHED:
				case ChatResultNodeType.AGENT_MEMORY:
					return TextNode;
				case ChatResultNodeType.TOOL_USE:
					return ToolUseNode;
				default:
					return TextNode;
			}
		}
	}

	// Get component props based on node type
	function getComponentProps() {
		const baseProps = {
			requestId,
			node,
			defaultExpanded,
			isStreaming,
			hideCollapseHeader,
			isLastNode
		};

		// Add type-specific props
		if (node.type === ChatRequestNodeType.TEXT || node.type === ChatResultNodeType.RAW_RESPONSE) {
			return {
				...baseProps
			};
		}

		// Add toolCalls for ToolResultNode
		if (node.type === ChatRequestNodeType.TOOL_RESULT) {
			return {
				...baseProps,
				toolCalls
			};
		}

		return baseProps;
	}

	// Check if node should be rendered (some nodes might be empty or invalid)
	function shouldRenderNode(): boolean {
		// Don't render nodes with no content
		if (!node.content && !node.toolUse && !node.toolResult && !node.image) {
			return false;
		}

		// Don't render empty text nodes
		if (
			(node.type === ChatRequestNodeType.TEXT || node.type === ChatResultNodeType.RAW_RESPONSE) &&
			!node.content?.trim()
		) {
			return false;
		}

		return true;
	}

	let Component = $derived(getNodeComponent());
	let componentProps = $derived(getComponentProps());
	let shouldRender = $derived(shouldRenderNode());
</script>

{#if shouldRender}
	<div class="node-renderer my-3" data-node-type={node.type} data-is-request={node.isRequest}>
		{#if showDebugInfo}
			<!-- Debug information -->
			<div
				class="mb-2 rounded border border-yellow-200 bg-yellow-50 p-2 text-xs dark:border-yellow-800 dark:bg-yellow-900/20"
			>
				<div class="space-y-1 font-mono">
					<div><strong>Type:</strong> {node.type} ({nodeConfig.name})</div>
					<div><strong>Role:</strong> {node.isRequest ? 'Request' : 'Response'}</div>
					<div><strong>ID:</strong> {node.id}</div>
					{#if node.isError}
						<div class="text-red-600 dark:text-red-400"><strong>Error:</strong> true</div>
					{/if}
					{#if node.toolUse}
						<div><strong>Tool Use:</strong> {JSON.stringify(node.toolUse, null, 2)}</div>
					{/if}
					{#if node.toolResult}
						<div><strong>Tool Result:</strong> {JSON.stringify(node.toolResult, null, 2)}</div>
					{/if}
				</div>
			</div>
		{/if}

		<!-- Render the appropriate component -->
		<Component {...componentProps}></Component>

		<!-- Render notes if present -->
		{#if node.notes && node.notes.length > 0}
			<NotesNode notes={node.notes} isExpanded={defaultExpanded} />
		{/if}
	</div>
{:else if showDebugInfo}
	<!-- Show debug info for empty/invalid nodes when debug is enabled -->
	<div
		class="node-renderer-empty rounded border border-gray-200 bg-gray-50 p-2 text-xs dark:border-gray-700 dark:bg-gray-800"
	>
		<div class="font-mono text-gray-500 dark:text-gray-400">
			<div><strong>Empty Node:</strong> {node.type} ({nodeConfig.name})</div>
			<div><strong>Role:</strong> {node.isRequest ? 'Request' : 'Response'}</div>
			<div><strong>Content:</strong> {node.content || 'null'}</div>
			<div><strong>Metadata:</strong> {JSON.stringify(node.metadata, null, 2)}</div>
		</div>
	</div>
{/if}
