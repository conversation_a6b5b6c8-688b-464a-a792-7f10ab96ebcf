<script lang="ts">
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { Icon, ChevronDown, ExclamationTriangle, CheckCircle, CommandLine, Play, ClipboardDocument } from 'svelte-hero-icons';
	import { CHAT_STYLES, CHAT_CONFIG } from '../config';
	import type { ProcessedNode } from '../utils';
	import SyntaxHighlight from '$lib/components/ui/content/SyntaxHighlight.svelte';
	import SetupScriptCommandStatus from '$lib/components/ui/feedback/SetupScriptCommandStatus.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	interface Props {
		node: ProcessedNode;
		defaultExpanded?: boolean;
		toolCalls?: ProcessedNode[];
	}

	let {
		node,
		defaultExpanded = false,
		toolCalls = []
	}: Props = $props();

	// State management
	let isExpanded = $state(defaultExpanded);

	// Derived values
	let toolResult = $derived(node.toolResult || node.metadata?.tool_result_node);
	let isError = $derived(toolResult?.is_error || node.isError || false);
	let toolUseId = $derived(toolResult?.tool_use_id || 'unknown');

	// Parse setup script result data
	let setupScriptData = $derived.by(() => {
		try {
			if (!node.content) return null;
			const parsed = JSON.parse(node.content);
			return parsed;
		} catch {
			return null;
		}
	});

	let scriptResult = $derived(setupScriptData?.script_result);
	let testResults = $derived(setupScriptData?.test_results || []);

	// Overall status
	let overallStatus = $derived.by(() => {
		if (isError) return 'error';
		if (scriptResult?.status === 'FAILED') return 'error';
		if (testResults.some((test: any) => test.status === 'FAILED')) return 'warning';
		if (scriptResult?.status === 'SUCCESS') return 'success';
		return 'unknown';
	});

	// Style classes based on status
	let containerClass = $derived(
		overallStatus === 'error'
			? `${CHAT_STYLES.nodes.toolResult.container} ${CHAT_STYLES.nodes.toolResult.error}`
			: CHAT_STYLES.nodes.toolResult.container
	);

	let headerClass = $derived(
		overallStatus === 'error'
			? `${CHAT_STYLES.nodes.toolResult.header} text-red-900 dark:text-red-100 bg-red-50 dark:bg-red-900/20`
			: overallStatus === 'warning'
			? `${CHAT_STYLES.nodes.toolResult.header} text-yellow-900 dark:text-yellow-100 bg-yellow-50 dark:bg-yellow-900/20`
			: CHAT_STYLES.nodes.toolResult.header
	);

	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleExpanded();
		}
	}

	// Format the tool use ID for display - show last 8 characters
	function formatToolUseId(id: string): string {
		if (id.length > 8) {
			return id.slice(-8);
		}
		return id;
	}

	// Get status icon
	function getStatusIcon() {
		if (overallStatus === 'error') return ExclamationTriangle;
		if (overallStatus === 'warning') return ExclamationTriangle;
		return CheckCircle;
	}

	// Get status text
	function getStatusText(): string {
		if (overallStatus === 'error') return 'Failed';
		if (overallStatus === 'warning') return 'Partial Success';
		if (overallStatus === 'success') return 'Success';
		return 'Completed';
	}

	// Get status color classes
	function getStatusColorClass(): string {
		if (overallStatus === 'error') return 'text-red-500';
		if (overallStatus === 'warning') return 'text-yellow-500';
		return 'text-green-500';
	}

	function getStatusBadgeClass(): string {
		if (overallStatus === 'error') return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300';
		if (overallStatus === 'warning') return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300';
		return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300';
	}

	// Convert status from API format to UI format
	function convertStatus(status: string): 'success' | 'error' | 'skipped' | 'loading' {
		switch (status) {
			case 'SUCCESS': return 'success';
			case 'FAILED': return 'error';
			case 'SKIPPED': return 'skipped';
			default: return 'error';
		}
	}

	function copyToClipboard(text: string) {
		navigator.clipboard.writeText(text);
	}
</script>

<div class="setup-script-tool-result-node">
	<div class={containerClass}>
		<!-- Header with tool info and expand/collapse control -->
		<button
			class={`${headerClass} cursor-pointer hover:opacity-80 transition-opacity`}
			onclick={toggleExpanded}
			onkeydown={handleKeydown}
			aria-expanded={isExpanded}
			aria-label={isExpanded ? 'Collapse setup script result' : 'Expand setup script result'}
		>
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<!-- Status icon -->
					<Icon
						src={getStatusIcon()}
						class={`w-4 h-4 ${getStatusColorClass()}`}
						micro
					/>

					<!-- Tool info -->
					<div class="flex items-center gap-2">
						<Icon src={CommandLine} class="w-4 h-4 text-slate-600 dark:text-slate-400" />
						<span class="font-medium">Setup Script Result</span>
						<span class="text-xs opacity-60 font-mono">
							{formatToolUseId(toolUseId)}
						</span>
					</div>

					<!-- Status badge -->
					<span class={`text-xs px-2 py-0.5 rounded-full ${getStatusBadgeClass()}`}>
						{getStatusText()}
					</span>
				</div>

				<!-- Expand/collapse icon -->
				<div class={`${CHAT_STYLES.collapsed.expandIcon} ${isExpanded ? 'rotate-180' : ''}`}>
					<Icon src={ChevronDown} class="w-4 h-4" />
				</div>
			</div>

			<!-- Collapsed preview -->
			{#if !isExpanded && setupScriptData}
				<div class="mt-2 text-xs opacity-70">
					{#if scriptResult}
						Script: {convertStatus(scriptResult.status) === 'success' ? 'Success' : 'Failed'}
					{/if}
					{#if testResults.length > 0}
						• Tests: {testResults.filter((t: any) => t.status === 'SUCCESS').length}/{testResults.length} passed
					{/if}
				</div>
			{/if}
		</button>

		<!-- Expanded content -->
		{#if isExpanded}
			<div
				class={CHAT_STYLES.nodes.toolResult.content}
				transition:slide={{ duration: CHAT_CONFIG.animations.duration, easing: quintOut }}
			>
				{#if setupScriptData}
					<!-- Script Execution Result -->
					{#if scriptResult}
						<div class="mb-4">
							<div class="flex items-center gap-2 mb-2">
								<Icon src={Play} class="w-4 h-4 text-slate-600 dark:text-slate-400" />
								<h4 class="text-sm font-medium text-slate-900 dark:text-white">Script Execution</h4>
								<SetupScriptCommandStatus commandResult={convertStatus(scriptResult.status)} />
							</div>

							{#if scriptResult.output}
								<div class="relative">
									<div class="absolute top-2 right-2 z-10">
										<Button
											variant="ghost"
											size="sm"
											icon={ClipboardDocument}
											onclick={() => copyToClipboard(scriptResult.output)}
										>
											Copy
										</Button>
									</div>
									<div class={`rounded border p-3 ${
										convertStatus(scriptResult.status) === 'error'
											? 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
											: 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
									}`}>
										<SyntaxHighlight
											code={scriptResult.output}
											language="bash"
											class="text-xs"
										/>
									</div>
								</div>
							{/if}
						</div>
					{/if}

					<!-- Test Results -->
					{#if testResults.length > 0}
						<div class="mb-4">
							<div class="flex items-center gap-2 mb-2">
								<Icon src={CheckCircle} class="w-4 h-4 text-slate-600 dark:text-slate-400" />
								<h4 class="text-sm font-medium text-slate-900 dark:text-white">Test Results</h4>
								<span class="text-xs text-slate-500 dark:text-slate-400">
									{testResults.filter((t: any) => t.status === 'SUCCESS').length}/{testResults.length} passed
								</span>
							</div>

							<div class="space-y-2">
								{#each testResults as test}
									<div class="flex items-center justify-between rounded border border-slate-200 p-2 dark:border-slate-700">
										<div class="flex items-center gap-2">
											<SetupScriptCommandStatus commandResult={convertStatus(test.status)} />
											<code class="text-xs text-slate-600 dark:text-slate-400">{test.command}</code>
										</div>
										{#if test.output}
											<Button
												variant="ghost"
												size="sm"
												onclick={() => copyToClipboard(test.output)}
											>
												<Icon src={ClipboardDocument} class="h-3 w-3" />
												Copy
											</Button>
										{/if}
									</div>
								{/each}
							</div>
						</div>
					{/if}
				{:else}
					<!-- Fallback for non-structured content -->
					<div class="p-4">
						{#if node.content}
							<SyntaxHighlight
								code={node.content}
								language="json"
								class="text-xs"
							/>
						{:else}
							<div class="text-gray-500 dark:text-gray-400 italic text-sm">
								No output from setup script execution
							</div>
						{/if}
					</div>
				{/if}

				<!-- Additional metadata if available -->
				{#if toolResult?.request_id}
					<div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600 text-xs opacity-60">
						<div class="flex items-center gap-4">
							<span>Request ID: <code class="font-mono">{toolResult.request_id}</code></span>
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>
