<script lang="ts">
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { Icon, ChevronDown, ExclamationTriangle, CheckCircle } from 'svelte-hero-icons';
	import { CHAT_STYLES, CHAT_CONFIG } from '../config';
	import { extractToolName } from '../utils';
	import type { ProcessedNode } from '../utils';
	import SyntaxHighlight from '$lib/components/ui/content/SyntaxHighlight.svelte';
	import SetupScriptToolResultNode from './SetupScriptToolResultNode.svelte';

	interface Props {
		node: ProcessedNode;
		defaultExpanded?: boolean;
		toolCalls?: ProcessedNode[];
	}

	let { node, defaultExpanded = false, toolCalls = [] }: Props = $props();

	// State management
	let isExpanded = $state(defaultExpanded);

	// Derived values
	let toolResult = $derived(node.toolResult || node.metadata?.tool_result_node);

	// Find the matching tool call to get the actual tool name
	let matchingToolCall = $derived(() => {
		if (!toolResult?.tool_use_id || !toolCalls.length) return null;
		return toolCalls.find(
			(call) =>
				call.toolUse?.tool_use_id === toolResult.tool_use_id ||
				call.metadata?.tool_use?.tool_use_id === toolResult.tool_use_id
		);
	});

	// Check if this is a setup-script tool result
	let isSetupScriptTool = $derived(() => {
		const matchedCall = matchingToolCall();

		// Check both tool_name and toolName properties
		const toolName =
			matchedCall?.toolUse?.tool_name ||
			matchedCall?.toolUse?.toolName ||
			matchedCall?.metadata?.tool_use?.tool_name ||
			matchedCall?.metadata?.tool_use?.toolName;

		// Debug logging (remove in production)
		// console.log('Setup script detection result:', {
		// 	toolName,
		// 	isSetupScript: toolName === 'setup-script',
		// 	hasContentStructure: (() => {
		// 		try {
		// 			if (node.content) {
		// 				const parsed = JSON.parse(node.content);
		// 				return !!(parsed.script_result || parsed.test_results);
		// 			}
		// 		} catch {}
		// 		return false;
		// 	})()
		// });

		// Check if content has setup script structure first (most reliable)
		try {
			if (node.content) {
				const parsed = JSON.parse(node.content);
				if (parsed.script_result || parsed.test_results) return true;
			}
		} catch {
			// Not JSON, continue with other checks
		}

		// Check multiple ways to identify setup-script tools
		if (toolName === 'setup-script') return true;

		// Check if tool_use_id contains setup-script
		if (toolResult?.tool_use_id?.includes('setup-script')) return true;

		// Check if the extracted tool name is setup-script
		const extractedName = extractToolName({
			tool_name: toolResult?.tool_name,
			toolName: toolResult?.toolName
		});
		if (extractedName === 'setup-script') return true;

		return false;
	});

	// Smart error detection - check content for error indicators
	let isError = $derived.by(() => {
		// Check the is_error flag first
		if (node.isError || toolResult?.is_error) return true;

		// Check content for error keywords
		if (!node.content) return false;

		const content = node.content.toLowerCase();
		return (
			content.includes('npm error') ||
			content.includes('error:') ||
			content.includes('error ') ||
			content.includes('failed') ||
			content.includes('exception') ||
			content.includes('traceback') ||
			content.includes('fatal:') ||
			content.includes('exit code 1')
		);
	});
	let toolUseId = $derived(toolResult?.tool_use_id || 'unknown');
	let toolName = $derived(() => {
		const matchedCall = matchingToolCall();
		// First try to get tool name from matching tool call
		if (matchedCall?.toolUse?.tool_name) {
			return matchedCall.toolUse.tool_name;
		}
		if (matchedCall?.metadata?.tool_use?.tool_name) {
			return matchedCall.metadata.tool_use.tool_name;
		}
		// Fallback to extracting from tool result or tool_use_id
		return extractToolName({ tool_name: toolResult?.tool_name }) || 'Tool';
	});

	// Style classes based on error state
	let containerClass = $derived(
		isError
			? `${CHAT_STYLES.nodes.toolResult.container} ${CHAT_STYLES.nodes.toolResult.error}`
			: CHAT_STYLES.nodes.toolResult.container
	);

	let headerClass = $derived(
		isError
			? `${CHAT_STYLES.nodes.toolResult.header} text-red-900 dark:text-red-100 bg-red-50 dark:bg-red-900/20`
			: CHAT_STYLES.nodes.toolResult.header
	);

	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleExpanded();
		}
	}

	// Format the tool use ID for display - show last 8 characters
	function formatToolUseId(id: string): string {
		if (id.length > 8) {
			return id.slice(-8);
		}
		return id;
	}

	// Get status icon
	function getStatusIcon() {
		return isError ? ExclamationTriangle : CheckCircle;
	}

	// Get status text
	function getStatusText(): string {
		return isError ? 'Error' : 'Success';
	}

	// Get content preview for collapsed state
	function getContentPreview(): string {
		if (!node.content) return 'No output';

		const lines = node.content.split('\n').filter((line) => line.trim());
		if (lines.length === 0) return 'No output';

		const firstLine = lines[0].trim();
		if (firstLine.length > 100) {
			return firstLine.substring(0, 97) + '...';
		}

		if (lines.length > 1) {
			return `${firstLine} (+${lines.length - 1} more lines)`;
		}

		return firstLine;
	}

	// Detect content language for syntax highlighting
	function detectContentLanguage(): string {
		if (!node.content) return 'text';

		const content = node.content.trim();

		// Check for JSON
		if (
			(content.startsWith('{') && content.endsWith('}')) ||
			(content.startsWith('[') && content.endsWith(']'))
		) {
			try {
				JSON.parse(content);
				return 'json';
			} catch {
				// Not valid JSON, continue checking
			}
		}

		// Check for common shell/terminal output patterns
		if (content.includes('npm error') || content.includes('npm ERR!')) return 'bash';
		if (content.includes('$ ') || content.includes('> ')) return 'bash';
		if (content.includes('Error:') || content.includes('Exception:')) return 'bash';

		// Check for code patterns
		if (content.includes('function ') || content.includes('const ') || content.includes('let '))
			return 'javascript';
		if (content.includes('def ') || content.includes('import ')) return 'python';
		if (content.includes('<') && content.includes('>')) return 'xml';

		// Default to plain text
		return 'text';
	}

	// Check if content should use syntax highlighting
	function shouldUseSyntaxHighlighting(): boolean {
		const language = detectContentLanguage();
		return language !== 'text' && !!node.content && node.content.trim().length > 0;
	}
</script>

{#if isSetupScriptTool()}
	<!-- Use specialized setup script tool result component -->
	<SetupScriptToolResultNode {node} {defaultExpanded} {toolCalls} />
{:else}
	<!-- Use default tool result component -->
	<div class="tool-result-node">
		<div class={containerClass}>
			<!-- Header with tool info and expand/collapse control -->
			<button
				class={`${headerClass} cursor-pointer transition-opacity hover:opacity-80`}
				onclick={toggleExpanded}
				onkeydown={handleKeydown}
				aria-expanded={isExpanded}
				aria-label={isExpanded ? 'Collapse tool result' : 'Expand tool result'}
			>
				<div class="flex items-center justify-between">
					<div class="flex items-center gap-2">
						<!-- Status icon -->
						<Icon
							src={getStatusIcon()}
							class={`h-4 w-4 ${isError ? 'text-red-500' : 'text-green-500'}`}
							micro
						/>

						<!-- Tool info -->
						<div class="flex items-center gap-2">
							<span class="font-medium">
								{#if toolName() && toolName() !== 'Unknown Tool' && toolName() !== 'Tool'}
									{toolName()} Result
								{:else}
									Tool Result
								{/if}
							</span>
							<span class="font-mono text-xs opacity-60">
								{formatToolUseId(toolUseId)}
							</span>
						</div>

						<!-- Status badge -->
						<span
							class={`rounded-full px-2 py-0.5 text-xs ${
								isError
									? 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
									: 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
							}`}
						>
							{getStatusText()}
						</span>
					</div>

					<!-- Expand/collapse icon -->
					<div class={`${CHAT_STYLES.collapsed.expandIcon} ${isExpanded ? 'rotate-180' : ''}`}>
						<Icon src={ChevronDown} class="h-4 w-4" />
					</div>
				</div>

				<!-- Content preview when collapsed -->
				{#if !isExpanded}
					<div class="mt-1 truncate text-xs opacity-75">
						{getContentPreview()}
					</div>
				{/if}
			</button>

			<!-- Expanded content -->
			{#if isExpanded}
				<div
					class={CHAT_STYLES.nodes.toolResult.content}
					transition:slide={{ duration: CHAT_CONFIG.animations.duration, easing: quintOut }}
				>
					{#if node.content}
						{#if shouldUseSyntaxHighlighting()}
							<div
								class={`p-4 ${
									isError ? 'bg-red-50 dark:bg-red-900/20' : 'bg-gray-50 dark:bg-gray-800'
								}`}
							>
								<SyntaxHighlight
									code={node.content}
									language={detectContentLanguage()}
									class="text-xs"
								/>
							</div>
						{:else}
							<pre
								class={`p-4 font-mono text-xs leading-relaxed break-words whitespace-pre-wrap ${
									isError
										? 'bg-red-50 text-red-900 dark:bg-red-900/20 dark:text-red-100'
										: 'bg-gray-50 text-gray-900 dark:bg-gray-800 dark:text-gray-100'
								}`}>{node.content}</pre>
						{/if}
					{:else}
						<div class="p-4 text-sm text-gray-500 italic dark:text-gray-400">
							No output from tool execution
						</div>
					{/if}

					<!-- Additional metadata if available -->
					{#if toolResult?.request_id}
						<div class="mt-3 border-t border-gray-600 pt-3 text-xs opacity-60">
							<div class="flex items-center gap-4">
								<span>Request ID: <code class="font-mono">{toolResult.request_id}</code></span>
								{#if toolResult.tool_name}
									<span>Tool: <code class="font-mono">{toolResult.tool_name}</code></span>
								{/if}
							</div>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>
{/if}
