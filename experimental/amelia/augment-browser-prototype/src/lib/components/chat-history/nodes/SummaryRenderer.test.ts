import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/svelte';
import Summary<PERSON><PERSON><PERSON> from './SummaryRenderer.svelte';

describe('SummaryRenderer', () => {
	it('should render content without summary tags normally', () => {
		const content = 'This is just regular text content without any summary tags.';
		const { container } = render(SummaryRenderer, { content });

		// Should render the content normally
		expect(container.textContent).toContain('This is just regular text content');
		// Should not have any summary containers
		expect(container.querySelector('.summary-container')).toBeNull();
	});

	it('should extract and render summary tags specially', () => {
		const content = `
			Some regular text here.

			<summary>
			The user confirmed the changes were satisfactory with a simple "ok" response. No further action was needed.
			</summary>

			More regular text here.
		`;

		const { container } = render(SummaryRenderer, { content });

		// Should have a summary container
		const summaryContainer = container.querySelector('.summary-container');
		expect(summaryContainer).toBeTruthy();

		// Should contain the summary content
		expect(container.textContent).toContain('The user confirmed the changes were satisfactory');

		// Should contain the remaining text
		expect(container.textContent).toContain('Some regular text here');
		expect(container.textContent).toContain('More regular text here');

		// Should not contain the raw XML tags
		expect(container.textContent).not.toContain('<summary>');
		expect(container.textContent).not.toContain('</summary>');
	});

	it('should extract and render general_summary tags specially', () => {
		const content = `
			Some text before.

			<general_summary>
			The task has been completed successfully. The user acknowledged the changes to the README.md file where I added dog emojis (🐕 in the title and 🐶 in the welcome message) and expressed satisfaction with the result.
			</general_summary>

			Some text after.
		`;

		const { container } = render(SummaryRenderer, { content });

		// Should have a summary container
		const summaryContainer = container.querySelector('.summary-container');
		expect(summaryContainer).toBeTruthy();

		// Should contain the general summary content
		expect(container.textContent).toContain('The task has been completed successfully');

		// Should contain the remaining text
		expect(container.textContent).toContain('Some text before');
		expect(container.textContent).toContain('Some text after');

		// Should not contain the raw XML tags
		expect(container.textContent).not.toContain('<general_summary>');
		expect(container.textContent).not.toContain('</general_summary>');
	});

	it('should handle both summary and general_summary tags in the same content', () => {
		const content = `
			Regular text at the start.

			<summary>
			Task-specific summary here.
			</summary>

			Some text in between.

			<general_summary>
			Overall conversation summary here.
			</general_summary>

			Regular text at the end.
		`;

		const { container } = render(SummaryRenderer, { content });

		// Should have multiple summary containers
		const summaryContainers = container.querySelectorAll('.summary-container');
		expect(summaryContainers).toHaveLength(2);

		// Should contain both summary contents
		expect(container.textContent).toContain('Task-specific summary here');
		expect(container.textContent).toContain('Overall conversation summary here');

		// Should contain all remaining text
		expect(container.textContent).toContain('Regular text at the start');
		expect(container.textContent).toContain('Some text in between');
		expect(container.textContent).toContain('Regular text at the end');

		// Should not contain any raw XML tags
		expect(container.textContent).not.toContain('<summary>');
		expect(container.textContent).not.toContain('<general_summary>');
	});

	it('should handle empty or whitespace-only remaining text', () => {
		const content = `
			<summary>Only summary content here.</summary>
		`;

		const { container } = render(SummaryRenderer, { content });

		// Should have a summary container
		const summaryContainer = container.querySelector('.summary-container');
		expect(summaryContainer).toBeTruthy();

		// Should contain the summary content
		expect(container.textContent).toContain('Only summary content here');

		// Should not have remaining content section if it's empty
		const remainingContent = container.querySelector('.remaining-content');
		expect(remainingContent).toBeNull();
	});
});
