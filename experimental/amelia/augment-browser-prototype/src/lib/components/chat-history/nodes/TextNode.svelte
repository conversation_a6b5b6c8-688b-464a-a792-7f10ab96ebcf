<script lang="ts">
	import { onMount } from 'svelte';
	import { ChevronDown, Icon } from 'svelte-hero-icons';

	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { extractInitialInstructionsFromSessionSummary } from '$lib/utils/remote-agent-utils';
	import { CHAT_CONFIG, CHAT_STYLES } from '../config';
	import type { ProcessedNode } from '../utils';
	import { shouldRenderAsMarkdown } from '../utils';
	import SummaryRenderer from './SummaryRenderer.svelte';

	interface Props {
		node: ProcessedNode;
		requestId?: string;
		isLastNode?: boolean;
		isStreaming?: boolean;
		defaultExpanded?: boolean;
		hideCollapseHeader?: boolean;
	}

	let {
		node,
		requestId,
		isLastNode = false,
		isStreaming = false,
		defaultExpanded = true,
		hideCollapseHeader = false
	}: Props = $props();

	// State management
	let isExpanded = $state(defaultExpanded);
	let contentRef = $state<HTMLDivElement>();
	let shouldAutoCollapse = $state(false);
	let actualHeight = $state(0);

	// Derived values
	let shouldUseMarkdown = $derived(shouldRenderAsMarkdown(node.content));
	// For assistant responses (not user requests), use full content without cleaning
	let cleanedContent = $derived(
		!node.isRequest
			? node.content
			: extractInitialInstructionsFromSessionSummary(node.content) || node.content
	);

	// Check if content should be auto-collapsed (taller than 5 lines ≈ 110px)
	const FIVE_LINES_HEIGHT = 110; // Approximate height of 5 lines in prose-sm

	onMount(() => {
		// Only allow auto-collapse for user request nodes, never for response nodes
		if (contentRef && CHAT_CONFIG.autoCollapse.enabled && !hideCollapseHeader && node.isRequest) {
			actualHeight = contentRef.scrollHeight;
			shouldAutoCollapse = actualHeight > FIVE_LINES_HEIGHT;

			// If auto-collapsible and not explicitly set to expanded, collapse it
			if (shouldAutoCollapse && defaultExpanded) {
				isExpanded = false;
			}
		}
	});

	function toggleExpanded() {
		// Only allow toggling for user request nodes, never for response nodes
		if (shouldAutoCollapse && !hideCollapseHeader && node.isRequest) {
			isExpanded = !isExpanded;
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleExpanded();
		}
	}

	// Only show chevron when content is actually taller than 5 lines and it's a user request node
	let showChevron = $derived(!hideCollapseHeader && shouldAutoCollapse && node.isRequest);
</script>

<div class="text-node-wrapper">
	<div class="relative flex">
		<div
			bind:this={contentRef}
			class={`${CHAT_STYLES.nodes.text} min-w-0 flex-1 ${shouldAutoCollapse && !isExpanded ? 'max-h-[110px] overflow-hidden' : ''}`}
		>
			<SummaryRenderer content={cleanedContent} {shouldUseMarkdown} {isStreaming} />
		</div>

		{#if showChevron}
			<Button
				variant="ghost"
				size="icon-sm"
				class="-mr-4 block"
				onclick={toggleExpanded}
				onkeydown={handleKeydown}
				aria-expanded={isExpanded}
				aria-label={isExpanded ? 'Collapse text' : 'Expand text'}
			>
				<div class={`${CHAT_STYLES.collapsed.expandIcon} ${isExpanded ? 'rotate-180' : ''}`}>
					<Icon src={ChevronDown} class="h-3 w-3" micro />
				</div>
			</Button>
		{/if}
	</div>
</div>

<style>
</style>
