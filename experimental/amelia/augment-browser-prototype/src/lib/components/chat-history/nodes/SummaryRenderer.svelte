<script lang="ts">
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import StreamingText from '$lib/components/ui/content/StreamingText.svelte';
	import { extractXMLTagContent } from '$lib/utils/session-summary-parser';
	import SummaryWidget from '$lib/components/ui/widgets/SummaryWidget.svelte';

	interface Props {
		content: string;
		shouldUseMarkdown?: boolean;
		isStreaming?: boolean;
		onContentChange?: () => void;
	}

	let { content, shouldUseMarkdown = true, isStreaming = false, onContentChange }: Props = $props();

	interface ParsedContent {
		summaries: Array<{
			type: 'summary' | 'general_summary';
			content: string;
		}>;
		remainingText: string;
	}

	/**
	 * Parse content to extract summary tags and remaining text
	 */
	function parseContentWithSummaries(text: string): ParsedContent {
		if (!text) {
			return { summaries: [], remainingText: '' };
		}

		// Filter out failed image data placeholders
		let filteredText = text.replace(/\s*<failed to load image data>\s*/g, '').trim();

		// If the entire content was just the failed image data placeholder, return empty
		if (!filteredText) {
			return { summaries: [], remainingText: '' };
		}

		const summaries: ParsedContent['summaries'] = [];
		let remainingText = filteredText;

		// Extract summary tags
		const summaryContent = extractXMLTagContent(filteredText, 'summary');
		if (summaryContent) {
			summaries.push({ type: 'summary', content: summaryContent });
			// Remove the summary tag from remaining text
			remainingText = remainingText.replace(/<summary>[\s\S]*?<\/summary>/i, '').trim();
		}

		// Extract general_summary tags
		const generalSummaryContent = extractXMLTagContent(filteredText, 'general_summary');
		if (generalSummaryContent) {
			summaries.push({ type: 'general_summary', content: generalSummaryContent });
			// Remove the general_summary tag from remaining text
			remainingText = remainingText
				.replace(/<general_summary>[\s\S]*?<\/general_summary>/i, '')
				.trim();
		}

		return { summaries, remainingText };
	}

	let parsedContent = $derived(parseContentWithSummaries(content));
	let hasSummaries = $derived(parsedContent.summaries.length > 0);
</script>

{#if hasSummaries}
	<!-- Render remaining text if any -->
	{#if parsedContent.remainingText.trim()}
		<div
			class="remaining-content"
			onclick={(e) => console.log('Remaining text clicked:', parsedContent)}
		>
			{#if isStreaming}
				{#key 'remaining-text-streaming'}
					<StreamingText
						content={parsedContent.remainingText}
						{isStreaming}
						useMarkdown={shouldUseMarkdown}
						{onContentChange}
					/>
				{/key}
			{:else if shouldUseMarkdown}
				<Markdown content={parsedContent.remainingText} hasNoPadding />
			{:else}
				<div class="break-words whitespace-pre-wrap">
					{parsedContent.remainingText}
				</div>
			{/if}
		</div>
	{/if}

	<!-- Render summaries in special containers -->
	{#each parsedContent.summaries as summary}
		<div class="summary-container mt-4">
			{#if summary.type === 'summary'}
				<!-- Task Summary -->
				<SummaryWidget summary={summary.content} />
			{:else if summary.type === 'general_summary'}
				<!-- General Summary -->
				<!-- <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
					<div class="flex items-center gap-2 mb-2">
						<Icon src={Sparkles} class="w-4 h-4 text-amber-600 dark:text-amber-400" micro />
						<h4 class="text-sm font-medium text-amber-900 dark:text-amber-100">General Summary</h4>
					</div>
					<div class="text-sm text-amber-800 dark:text-amber-200 leading-relaxed">
						{#if shouldUseMarkdown}
							<Markdown content={summary.content} size="sm" hasNoPadding />
						{:else}
							<div class="whitespace-pre-wrap break-words">
								{summary.content}
							</div>
						{/if}
					</div>
				</div> -->
			{/if}
		</div>
	{/each}
{:else}
	<!-- No summaries found, render content normally -->
	{#if isStreaming}
		{#key 'main-content-streaming'}
			<StreamingText {content} {isStreaming} useMarkdown={shouldUseMarkdown} {onContentChange} />
		{/key}
	{:else if shouldUseMarkdown}
		<Markdown {content} hasNoPadding />
	{:else}
		<div class="break-words whitespace-pre-wrap">
			{content}
		</div>
	{/if}
{/if}

<style>
	.summary-container {
		/* Ensure proper spacing and visual separation */
		margin-bottom: 1rem;
	}

	.remaining-content {
		/* Add some spacing if there's content after summaries */
		margin-top: 0.5rem;
	}
</style>
