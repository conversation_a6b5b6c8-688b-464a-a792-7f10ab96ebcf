# Chat History Node Renderers

This directory contains components for rendering different types of chat nodes in the chat history interface.

## Components

### SummaryRenderer.svelte

A specialized component that parses text content to detect and render summary tags in a visually distinct way.

#### Features

- **Automatic Detection**: Detects `<summary>` and `<general_summary>` XML tags in text content
- **Special Rendering**: Renders summaries in styled containers with appropriate icons and colors
- **Content Separation**: Extracts summary content and renders remaining text separately
- **Markdown Support**: Supports both markdown and plain text rendering
- **Clean Output**: Removes raw XML tags from the final rendered output

#### Usage

```svelte
<script>
  import SummaryRenderer from './SummaryRenderer.svelte';
</script>

<SummaryRenderer
  content={textWithSummaryTags}
  shouldUseMarkdown={true}
/>
```

#### Example Input/Output

**Input:**
```
I have completed the task successfully.

<summary>
The user confirmed the changes were satisfactory with a simple "ok" response. No further action was needed.
</summary>

<general_summary>
The task has been completed successfully. The user acknowledged the changes and expressed satisfaction with the result.
</general_summary>

Thank you for your feedback!
```

**Output:**
- Regular text: "I have completed the task successfully."
- Blue summary box with document icon: "The user confirmed the changes were satisfactory..."
- Amber general summary box with sparkles icon: "The task has been completed successfully..."
- Regular text: "Thank you for your feedback!"

#### Styling

- **Summary (`<summary>`)**: Blue-themed container with document icon
- **General Summary (`<general_summary>`)**: Amber-themed container with sparkles icon
- **Remaining Text**: Rendered normally with markdown support

### TextNode.svelte

The main text rendering component that now uses SummaryRenderer internally.

#### Integration

TextNode automatically uses SummaryRenderer for all text content, which means:
- All chat messages are automatically parsed for summary tags
- Summary tags are rendered specially without any additional configuration
- Backward compatibility is maintained for content without summary tags

### Other Node Renderers

- **NodeRenderer.svelte**: Main dispatcher for different node types
- **ToolResultNode.svelte**: Renders tool execution results
- **ToolUseNode.svelte**: Renders tool usage information
- **ImageNode.svelte**: Renders image content
- **NotesNode.svelte**: Renders note content

## Architecture

The chat rendering flow is:

```
ChatHistoryView → ExchangeView → NodeRenderer → TextNode → SummaryRenderer
```

This ensures that all text content in chat history automatically benefits from summary tag parsing and special rendering.

## Testing

Tests are available in `SummaryRenderer.test.ts` to verify:
- Normal text rendering (without summary tags)
- Summary tag extraction and rendering
- General summary tag extraction and rendering
- Mixed content with both tag types
- Edge cases (empty content, whitespace handling)

## Configuration

The summary rendering uses the existing chat styling system and integrates with:
- Dark/light mode themes
- Markdown rendering preferences
- Chat history configuration options

No additional configuration is required - summary rendering is automatically enabled for all chat content.
