<script lang="ts">
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { Icon, ChevronDown, Photo, ExclamationTriangle } from 'svelte-hero-icons';
	import { CHAT_STYLES, CHAT_CONFIG } from '../config';
	import type { ProcessedNode } from '../utils';

	interface Props {
		node: ProcessedNode;
		defaultExpanded?: boolean;
	}

	let {
		node,
		defaultExpanded = false
	}: Props = $props();

	// State management
	let isExpanded = $state(defaultExpanded);
	let imageLoaded = $state(false);
	let imageError = $state(false);

	// Derived values
	let imageData = $derived(node.image || node.metadata?.image_node);
	let imageFormat = $derived(imageData?.format || 'png');
	let base64Data = $derived(imageData?.image_data || '');

	// Create data URL for image
	let imageUrl = $derived.by(() => {
		if (!base64Data) return '';

		// Map format enum to MIME type
		const formatMap: Record<number, string> = {
			0: 'png',
			1: 'jpeg',
			2: 'gif',
			3: 'webp'
		};

		const mimeFormat = typeof imageFormat === 'number'
			? formatMap[imageFormat] || 'png'
			: imageFormat;

		return `data:image/${mimeFormat};base64,${base64Data}`;
	});

	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleExpanded();
		}
	}

	function handleImageLoad() {
		imageLoaded = true;
		imageError = false;
	}

	function handleImageError() {
		imageLoaded = false;
		imageError = true;
	}

	// Get image size info if available
	function getImageInfo(): string {
		if (!imageData) return 'No image data';

		const sizeKB = Math.round((base64Data.length * 3) / 4 / 1024);
		const formatText = typeof imageFormat === 'number'
			? ['PNG', 'JPEG', 'GIF', 'WebP'][imageFormat] || 'Unknown'
			: imageFormat.toUpperCase();

		return `${formatText} • ${sizeKB}KB`;
	}

	// Check if image data is valid
	let hasValidImage = $derived(!!imageUrl && !imageError);
</script>

<div class="image-node">
	<div class={CHAT_STYLES.nodes.image.container}>
		<!-- Header with image info and expand/collapse control -->
		<button
			class={`${CHAT_STYLES.nodes.image.header} cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors`}
			onclick={toggleExpanded}
			onkeydown={handleKeydown}
			aria-expanded={isExpanded}
			aria-label={isExpanded ? 'Collapse image' : 'Expand image'}
		>
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<!-- Image icon -->
					{#if imageError}
						<Icon src={ExclamationTriangle} class="w-4 h-4 text-red-500" />
					{:else}
						<Icon src={Photo} class="w-4 h-4 text-gray-600 dark:text-gray-400" />
					{/if}

					<!-- Image info -->
					<div class="flex items-center gap-2">
						<span class="font-medium">
							{node.isRequest ? 'Attached Image' : 'Generated Image'}
						</span>
						<span class="text-xs opacity-75">
							{getImageInfo()}
						</span>
					</div>

					<!-- Status indicator -->
					{#if imageError}
						<span class="text-xs px-2 py-0.5 rounded-full bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300">
							Error
						</span>
					{:else if imageLoaded}
						<span class="text-xs px-2 py-0.5 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300">
							Loaded
						</span>
					{/if}
				</div>

				<!-- Expand/collapse icon -->
				<div class={`${CHAT_STYLES.collapsed.expandIcon} ${isExpanded ? 'rotate-180' : ''}`}>
					<Icon src={ChevronDown} class="w-4 h-4" />
				</div>
			</div>
		</button>

		<!-- Expanded content -->
		{#if isExpanded}
			<div
				class={CHAT_STYLES.nodes.image.content}
				transition:slide={{ duration: CHAT_CONFIG.animations.duration, easing: quintOut }}
			>
				{#if hasValidImage}
					<!-- Image display -->
					<div class="space-y-3">
						<img
							src={imageUrl}
							alt=""
							class={`${CHAT_STYLES.nodes.image.image} border border-gray-200 dark:border-gray-600`}
							onload={handleImageLoad}
							onerror={handleImageError}
							loading="lazy"
						/>

						<!-- Image metadata -->
						<div class="text-xs text-gray-500 dark:text-gray-400 space-y-1">
							<div class="flex items-center justify-between">
								<span>Format: {typeof imageFormat === 'number'
									? ['PNG', 'JPEG', 'GIF', 'WebP'][imageFormat] || 'Unknown'
									: imageFormat.toUpperCase()}</span>
								<span>Size: {Math.round((base64Data.length * 3) / 4 / 1024)}KB</span>
							</div>
							{#if node.metadata?.file_path}
								<div>Path: <code class="font-mono">{node.metadata.file_path}</code></div>
							{/if}
						</div>
					</div>
				{:else if imageError}
					<!-- Error state -->
					<div class="flex items-center justify-center py-8 text-center">
						<div class="space-y-2">
							<Icon src={ExclamationTriangle} class="w-8 h-8 text-red-500 mx-auto" />
							<div class="text-sm text-red-600 dark:text-red-400">
								Failed to load image
							</div>
							<div class="text-xs text-gray-500 dark:text-gray-400">
								The image data may be corrupted or in an unsupported format
							</div>
						</div>
					</div>
				{:else}
					<!-- No image data -->
					<div class="flex items-center justify-center py-8 text-center">
						<div class="space-y-2">
							<Icon src={Photo} class="w-8 h-8 text-gray-400 mx-auto" />
							<div class="text-sm text-gray-500 dark:text-gray-400">
								No image data available
							</div>
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>
