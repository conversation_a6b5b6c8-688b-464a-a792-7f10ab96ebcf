<script lang="ts">
	import { Icon, ChevronDown } from 'svelte-hero-icons';
	import { quintOut } from 'svelte/easing';
	import { slide } from 'svelte/transition';
	import { CHAT_CONFIG } from '../config';
	import type { ProcessedNode } from '../utils';
	import { extractToolName } from '../utils';
	import { getToolConfig, extractKeyDescription } from '../tool-config';
	import SyntaxHighlight from '../../ui/content/SyntaxHighlight.svelte';

	interface Props {
		node: ProcessedNode;
	}

	let { node }: Props = $props();

	let isExpanded = $state(false);

	// Derived values
	let toolUse = $derived(node.toolUse || node.metadata?.tool_use || node.metadata?.toolUse);
	let toolName = $derived(extractToolName(toolUse) || toolUse?.toolName);
	let inputJson = $derived(toolUse?.inputJson || toolUse?.input_json || '{}');
	let mcpServerName = $derived(toolUse?.mcpServerName || toolUse?.mcp_server_name);
	let hasResult = $derived(node.hasResult || false);
	let resultContent = $derived(node.resultContent || '');
	let isError = $derived(node.isError || false);

	// Parse input JSON safely
	let parsedInput = $derived.by(() => {
		try {
			return JSON.parse(inputJson);
		} catch {
			return inputJson;
		}
	});

	// Get tool configuration
	const toolConfig = $derived(getToolConfig(toolName || ''));

	// Get key description using config
	const keyDescription = $derived(extractKeyDescription(toolName || '', parsedInput || {}));

	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleExpanded();
		}
	}

	// Get formatted parameters for pretty display
	function getFormattedParams(): Array<{ key: string; value: any; type: string }> {
		if (typeof parsedInput !== 'object' || parsedInput === null) {
			return [];
		}

		return Object.entries(parsedInput).map(([key, value]) => ({
			key,
			value,
			type: typeof value
		}));
	}

	// Format a single parameter value
	function formatParamValue(value: any, type: string): string {
		if (type === 'string') {
			return value;
		} else if (type === 'boolean') {
			return value ? 'true' : 'false';
		} else if (type === 'number') {
			return String(value);
		} else if (Array.isArray(value)) {
			return `[${value.length} items]`;
		} else if (type === 'object' && value !== null) {
			const keys = Object.keys(value);
			return `{${keys.length} properties}`;
		}
		return String(value);
	}

	// Render nested objects prettily
	function renderValue(value: any, depth: number = 0): string {
		if (value === null) return 'null';
		if (value === undefined) return 'undefined';

		if (typeof value === 'string') {
			return depth > 0 ? `"${value}"` : value;
		}

		if (typeof value === 'boolean' || typeof value === 'number') {
			return String(value);
		}

		if (Array.isArray(value)) {
			if (value.length === 0) return '[]';
			if (depth > 2) return `[${value.length} items]`;

			const items = value.slice(0, 3).map((item) => renderValue(item, depth + 1));
			const result = items.join(', ');
			return value.length > 3 ? `[${result}, ...${value.length - 3} more]` : `[${result}]`;
		}

		if (typeof value === 'object') {
			const keys = Object.keys(value);
			if (keys.length === 0) return '{}';
			if (depth > 2) return `{${keys.length} properties}`;

			const pairs = keys.slice(0, 3).map((key) => `${key}: ${renderValue(value[key], depth + 1)}`);
			const result = pairs.join(', ');
			return keys.length > 3 ? `{${result}, ...${keys.length - 3} more}` : `{${result}}`;
		}

		return String(value);
	}
</script>

<!-- Cohesive tool call display -->
<div class="my-0.5">
	<div
		class={`overflow-hiddenx rounded-lg border border-slate-200 bg-slate-50 transition-all duration-200 dark:border-slate-700 dark:bg-slate-800`}
	>
		<!-- Header - stays consistent -->
		<button
			class={`sticky -top-4 w-full rounded-t-lg ${isExpanded ? '' : 'rounded-b-lg'} flex cursor-pointer items-center gap-1.5 border-b bg-slate-50 px-2 py-1.5 text-xs transition-colors hover:bg-slate-100 dark:bg-slate-800 dark:hover:bg-slate-700/50 ${isExpanded ? 'border-slate-200' : 'border-transparent'}`}
			onclick={toggleExpanded}
			onkeydown={handleKeydown}
			aria-expanded={isExpanded}
			aria-label={isExpanded ? 'Hide details' : 'Show details'}
		>
			<!-- Tool icon -->
			<div class="flex-shrink-0">
				<Icon src={toolConfig.icon} class="h-3 w-3 text-slate-600 dark:text-slate-400" micro />
			</div>

			<!-- Tool label -->
			<span class="font-medium whitespace-nowrap text-slate-900 dark:text-slate-100">
				{toolConfig.label}
			</span>

			<!-- MCP server badge -->
			{#if mcpServerName}
				<span
					class="rounded bg-slate-200 px-1.5 py-0.5 text-xs text-slate-500 dark:bg-slate-700 dark:text-slate-400"
				>
					{mcpServerName}
				</span>
			{/if}

			<!-- Key info preview when collapsed -->
			{#if !isExpanded && keyDescription}
				<span class="truncate text-slate-600 opacity-75 dark:text-slate-400">
					{keyDescription}
				</span>
			{/if}

			<!-- Spacer -->
			<div class="flex-1"></div>

			<!-- Success indicator -->
			{#if hasResult}
				<div
					class={`h-2 w-2 flex-shrink-0 rounded-full ${isError ? 'bg-red-500' : 'bg-emerald-500'}`}
				></div>
			{/if}

			<!-- Expand chevron -->
			<Icon
				src={ChevronDown}
				class={`h-3 w-3 flex-shrink-0 text-slate-400 transition-transform ${isExpanded ? '' : 'rotate-180'}`}
			/>
		</button>

		<!-- Expanded content - seamlessly connected -->
		{#if isExpanded}
			<div
				class="px-3 py-2 text-xs"
				transition:slide={{ duration: CHAT_CONFIG.animations.duration, easing: quintOut }}
			>
				{#if getFormattedParams().length > 0}
					<div class="space-y-2">
						{#each getFormattedParams() as param}
							<div class="grid grid-cols-[auto_1fr] items-start gap-3">
								<span
									class="text-right text-slate-500 dark:text-slate-500 {param.type === 'object' ||
									Array.isArray(param.value) ||
									(param.type === 'string' && param.value.length > 50)
										? 'mt-1.5'
										: ''}"
								>
									{param.key}
								</span>
								<div class="min-w-0 text-slate-800 dark:text-slate-200">
									{#if param.type === 'object' || Array.isArray(param.value)}
										<div
											class="rounded border border-slate-200 bg-white px-2 py-1.5 font-mono text-xs dark:border-slate-600 dark:bg-slate-900"
										>
											{renderValue(param.value)}
										</div>
									{:else if param.type === 'string' && param.value.length > 50}
										<div
											class="rounded border border-slate-200 bg-white px-2 py-1.5 font-mono dark:border-slate-600 dark:bg-slate-900"
										>
											<div class="break-words whitespace-pre-wrap">{param.value}</div>
										</div>
									{:else}
										<span class="break-words">{formatParamValue(param.value, param.type)}</span>
									{/if}
								</div>
							</div>
						{/each}
					</div>
				{:else}
					<div class="py-3 text-center text-slate-500 italic dark:text-slate-400">
						No parameters provided
					</div>
				{/if}

				<!-- Tool result section -->
				{#if hasResult && resultContent}
					<div class="mt-3 border-t border-slate-200 pt-3 dark:border-slate-600">
						<div class="mb-2 flex items-center gap-2">
							<span class="text-xs text-slate-500 dark:text-slate-500">Result</span>
							{#if isError}
								<span
									class="rounded bg-red-100 px-1.5 py-0.5 text-xs text-red-700 dark:bg-red-900/30 dark:text-red-400"
									>Error</span
								>
							{:else}
								<span
									class="rounded bg-green-100 px-1.5 py-0.5 text-xs text-green-700 dark:bg-green-900/30 dark:text-green-400"
									>Success</span
								>
							{/if}
						</div>
						<div class="">
							<SyntaxHighlight code={resultContent} language="shell" />
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>
