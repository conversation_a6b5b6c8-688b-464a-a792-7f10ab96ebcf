/**
 * Utility functions for chat history processing
 * Handles data transformation, summary extraction, and message grouping
 */

import type { CleanChatExchangeData } from '$lib/api/unified-client';

import { extractSummaryFromTags } from '$lib/types/structured-response';
import type { NotebookNote, NoteCategory } from '$lib/types/structured-response';
import { CHAT_CONFIG } from './config';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface ProcessedExchange {
	id: string;
	sequenceId: number;
	timestamp: string;
	userNodes: ProcessedNode[];
	assistantNodes: ProcessedNode[];
	summary?: string;
	isCollapsed: boolean;
	hasToolUses: boolean;
	hasErrors: boolean;
}

export interface ProcessedNode {
	id: string;
	type: number;
	content: string;
	isRequest: boolean;
	metadata?: any;
	toolUse?: any;
	toolResult?: any;
	image?: any;
	isError?: boolean;
	notes?: NotebookNote[]; // For nodes that contain notes
	resultContent?: string; // Content from the tool result
	hasResult?: boolean; // Whether this tool call has a result
}

export interface ChatHistoryGroup {
	id: string;
	timestamp: string;
	exchanges: ProcessedExchange[];
	summary: string;
	isCollapsed: boolean;
	messageCount: number;
}

// ============================================================================
// EXCHANGE PROCESSING
// ============================================================================

/**
 * Filter out TOOL_USE nodes that have corresponding TOOL_RESULT nodes
 * This combines tool calls with their results for a cleaner display
 */
/**
 * Filter out TOOL_USE nodes that have corresponding TOOL_RESULT nodes across all exchanges
 * This combines tool calls with their results for a cleaner display
 */
function combineToolCallsWithResults(exchanges: ProcessedExchange[]): ProcessedExchange[] {
	// Create a map of tool_use_id to tool result data
	const toolResults = new Map<string, any>();

	// First pass: collect all tool results
	exchanges.forEach((exchange) => {
		exchange.assistantNodes.forEach((node) => {
			if (node.type === 1) {
				// TOOL_RESULT type
				const toolUseId =
					node.toolResult?.tool_use_id ||
					node.toolResult?.toolUseId ||
					node.metadata?.tool_result_node?.tool_use_id ||
					node.metadata?.toolResultNode?.tool_use_id ||
					node.metadata?.toolResultNode?.toolUseId;

				if (toolUseId) {
					toolResults.set(toolUseId, {
						content: node.content,
						isError: node.isError,
						toolResult:
							node.toolResult || node.metadata?.toolResultNode || node.metadata?.tool_result_node
					});
				}
			}
		});
	});

	// Second pass: enhance tool calls with results and filter out standalone results
	return exchanges.map((exchange) => ({
		...exchange,
		assistantNodes: exchange.assistantNodes.filter((node) => {
			if (node.type === 1) {
				// Filter out TOOL_RESULT nodes - they'll be combined with TOOL_USE nodes
				return false;
			}

			if (node.type === 5) {
				// TOOL_USE type - enhance with result if available
				const toolUseId =
					node.toolUse?.tool_use_id ||
					node.toolUse?.toolUseId ||
					node.metadata?.tool_use?.tool_use_id ||
					node.metadata?.toolUse?.tool_use_id ||
					node.metadata?.toolUse?.toolUseId;

				if (toolUseId && toolResults.has(toolUseId)) {
					const result = toolResults.get(toolUseId);
					// Enhance the tool call node with result data
					node.toolResult = result.toolResult;
					node.resultContent = result.content;
					node.hasResult = true;
					node.isError = result.isError;
				}
			}

			return true; // Keep all other nodes
		})
	}));
}

/**
 * Process multiple exchanges and apply cross-exchange tool filtering
 */
export function processExchangesWithToolFiltering(
	exchanges: CleanChatExchangeData[]
): ProcessedExchange[] {
	// Debug: Check for duplicate exchanges and deduplicate
	const sequenceIds = exchanges.map((ex) => ex.sequenceId);
	const uniqueSequenceIds = new Set(sequenceIds);
	if (sequenceIds.length !== uniqueSequenceIds.size) {
		// Deduplicate exchanges by sequenceId, keeping the last occurrence
		const seenSequenceIds = new Set<number>();
		const deduplicatedExchanges: CleanChatExchangeData[] = [];

		// Process in reverse to keep the last occurrence of each sequenceId
		for (let i = exchanges.length - 1; i >= 0; i--) {
			const exchange = exchanges[i];
			if (!seenSequenceIds.has(exchange.sequenceId)) {
				seenSequenceIds.add(exchange.sequenceId);
				deduplicatedExchanges.unshift(exchange); // Add to beginning to maintain order
			}
		}

		exchanges = deduplicatedExchanges;
	}

	// First, process all exchanges individually
	const processedExchanges = exchanges.map((exchange, index) => processExchange(exchange, index));

	// Then apply cross-exchange tool filtering
	return combineToolCallsWithResults(processedExchanges);
}

/**
 * Process a raw exchange into a structured format
 */
export function processExchange(exchange: CleanChatExchangeData, index: number): ProcessedExchange {
	const exchangeId = exchange.exchange?.requestId || `exchange-${index}`;
	const timestamp = new Date().toISOString(); // Clean format doesn't have timestamps

	// Clean format has requestNodes and responseNodes directly
	const requestNodes = exchange.exchange?.requestNodes || [];
	const responseNodes = exchange.exchange?.responseNodes || [];

	// Process request nodes (only TEXT nodes for user input)
	const userNodes = processRequestNodes(requestNodes, exchangeId);

	// If no request nodes but we have requestMessage, create a text node
	const requestMessage = exchange.exchange?.requestMessage;
	if (userNodes.length === 0 && requestMessage) {
		userNodes.push({
			id: `${exchangeId}-req-text`,
			type: 0, // TEXT type
			content: requestMessage,
			isRequest: true,
			metadata: { type: 'text', content: requestMessage },
			toolResult: undefined,
			image: undefined,
			isError: false
		});
	}

	// Process response nodes
	const allResponseNodes = processResponseNodes(responseNodes, exchangeId);

	// Separate text nodes from tool use nodes for proper ordering
	const textNodes = allResponseNodes.filter((node) => node.type === 0); // RAW_RESPONSE/TEXT
	const toolUseNodes = allResponseNodes.filter((node) => node.type === 5); // TOOL_USE
	const otherNodes = allResponseNodes.filter((node) => node.type !== 0 && node.type !== 5);

	// Process tool result nodes from request nodes and add them to assistant nodes
	// Tool results are part of the agent's workflow, not user input
	const toolResultNodes = processToolResultNodes(requestNodes, exchangeId);

	// Combine and sort all assistant nodes in the desired order:
	// 1. Text responses first (so they appear before tool use boxes)
	// 2. Other response nodes
	// 3. Tool use nodes (the tool use boxes)
	// 4. Tool result nodes last
	const assistantNodes = [...textNodes, ...otherNodes, ...toolUseNodes, ...toolResultNodes];

	// Note: Tool filtering is now done at a higher level to handle cross-exchange tool calls

	// If no response nodes but we have responseText, create a text node
	const responseText = exchange.exchange?.responseText;
	if (assistantNodes.length === 0 && responseText) {
		const notes = extractNotesFromContent(responseText);
		const cleanContent = responseText
			.replace(/<note\s+category="[^"]+">([^<]+)<\/note>/gi, '')
			.trim();

		assistantNodes.push({
			id: `${exchangeId}-res-text`,
			type: 0, // TEXT type
			content: cleanContent,
			isRequest: false,
			metadata: { type: 'text', content: responseText },
			toolUse: undefined,
			isError: false,
			notes: notes.length > 0 ? notes : undefined
		});
	}

	// Extract summary from response content
	const summary = extractExchangeSummary(assistantNodes);

	// Determine if exchange should be collapsed
	const shouldCollapse = shouldCollapseExchange(userNodes, assistantNodes, summary);

	// Check for tool uses and errors
	const hasToolUses = assistantNodes.some((node) => node.toolUse);
	const hasErrors = assistantNodes.some((node) => node.isError);

	const result = {
		id: exchangeId,
		sequenceId: exchange.sequenceId || index,
		timestamp,
		userNodes,
		assistantNodes,
		summary,
		isCollapsed: shouldCollapse,
		hasToolUses,
		hasErrors
	};

	return result;
}

/**
 * Process request nodes from an exchange - only include actual user input
 */
function processRequestNodes(nodes: any[], exchangeId: string): ProcessedNode[] {
	if (!nodes || !Array.isArray(nodes)) return [];

	// Only include TEXT nodes (type 0) as user nodes
	// Tool results (type 1) and other types are not user input
	return nodes
		.filter((node) => node.type === 0) // Only TEXT nodes
		.map((node, index) => ({
			id: `${exchangeId}-req-${index}`,
			type: node.type || 0,
			content: extractNodeContent(node, true),
			isRequest: true,
			metadata: node,
			toolResult: node.tool_result_node,
			image: node.image_node,
			isError: isToolResultError(node)
		}));
}

/**
 * Process response nodes from an exchange
 */
function processResponseNodes(nodes: any[], exchangeId: string): ProcessedNode[] {
	if (!nodes || !Array.isArray(nodes)) return [];

	return nodes.map((node, index) => {
		const content = node.content || '';

		const notes = extractNotesFromContent(content);

		// Remove note tags from content for display
		let cleanContent = content.replace(/<note\s+category="[^"]+">([^<]+)<\/note>/gi, '').trim();

		// Improved duplicate detection - only remove exact duplicates that are clearly streaming artifacts
		// This is more conservative to avoid removing legitimate repeated content
		if (cleanContent.length > 200) {
			const sentences = cleanContent.split(/[.!?]+/).filter((s: string) => s.trim().length > 10);
			const uniqueSentences = [];
			const seenSentences = new Set();

			for (const sentence of sentences) {
				const trimmed = sentence.trim();
				// Only consider it a duplicate if it's an exact match and reasonably long
				// This prevents removing legitimate repeated phrases or short sentences
				if (trimmed && trimmed.length > 20 && !seenSentences.has(trimmed)) {
					seenSentences.add(trimmed);
					uniqueSentences.push(sentence);
				} else if (trimmed && trimmed.length <= 20) {
					// Always keep short sentences to avoid removing legitimate content
					uniqueSentences.push(sentence);
				}
			}

			// Only apply deduplication if we found significant duplicates (more than 20% reduction)
			if (uniqueSentences.length < sentences.length * 0.8) {
				cleanContent = uniqueSentences.join('. ').trim();
				if (
					cleanContent &&
					!cleanContent.endsWith('.') &&
					!cleanContent.endsWith('!') &&
					!cleanContent.endsWith('?')
				) {
					cleanContent += '.';
				}
			}
		}

		return {
			id: `${exchangeId}-res-${index}`,
			type: node.type || 0,
			content: cleanContent,
			isRequest: false,
			metadata: node,
			toolUse: node.tool_use || node.toolUse || (node.type === 5 ? node : undefined),
			isError: false, // Response nodes don't typically have errors
			notes: notes.length > 0 ? notes : undefined
		};
	});
}

/**
 * Process tool result nodes from request nodes - these are part of the agent's workflow
 */
function processToolResultNodes(nodes: any[], exchangeId: string): ProcessedNode[] {
	if (!nodes || !Array.isArray(nodes)) return [];

	// Only include TOOL_RESULT nodes (type 1) and other non-TEXT types
	return nodes
		.filter((node) => node.type !== 0) // Exclude TEXT nodes (already processed as user nodes)
		.map((node, index) => ({
			id: `${exchangeId}-tool-${index}`,
			type: node.type,
			content: extractNodeContent(node, true),
			isRequest: false, // Treat as assistant workflow, not user input
			metadata: node,
			toolResult: node.tool_result_node,
			image: node.image_node,
			isError: isToolResultError(node)
		}));
}

/**
 * Extract content from a node based on its type
 */
function extractNodeContent(node: any, isRequest: boolean): string {
	if (isRequest) {
		// Request node content extraction - clean format uses camelCase
		if (node.textNode?.content) return node.textNode.content;
		if (node.toolResultNode?.content) return node.toolResultNode.content;
		if (node.imageNode) return '[Image]';

		return node.content || node.text || '';
	} else {
		// Response node content extraction
		return node.content || '';
	}
}

// ============================================================================
// SUMMARY EXTRACTION
// ============================================================================

/**
 * Extract summary from exchange nodes
 */
function extractExchangeSummary(assistantNodes: ProcessedNode[]): string | undefined {
	if (!assistantNodes.length) return undefined;

	// Try to find summary in the last response node
	const lastNode = assistantNodes[assistantNodes.length - 1];
	if (!lastNode.content) return undefined;

	// First, try to extract from <summary> tags
	if (CHAT_CONFIG.summary.prioritizeTags) {
		const tagSummary = extractSummaryFromTags(lastNode.content);
		if (tagSummary) {
			return truncateText(tagSummary, CHAT_CONFIG.summary.maxLength);
		}
	}

	// Fallback to first few sentences
	const sentences = lastNode.content.split(/[.!?]+/).filter((s) => s.trim());
	if (sentences.length > 0) {
		let summary = sentences[0].trim();
		if (sentences.length > 1 && summary.length < CHAT_CONFIG.summary.fallbackLength) {
			summary += '. ' + sentences[1].trim();
		}
		return truncateText(summary, CHAT_CONFIG.summary.maxLength);
	}

	// Final fallback to truncated content
	return truncateText(lastNode.content, CHAT_CONFIG.summary.fallbackLength);
}

/**
 * Determine if an exchange should be collapsed by default
 */
function shouldCollapseExchange(
	userNodes: ProcessedNode[],
	assistantNodes: ProcessedNode[],
	summary?: string
): boolean {
	if (!CHAT_CONFIG.autoCollapse.enabled) return false;

	// Never collapse exchanges that contain assistant responses
	if (assistantNodes.length > 0) return false;

	// Don't collapse if there's an error
	if (assistantNodes.some((node) => node.isError)) return false;

	// Don't collapse if it's very short
	const totalContent = [...userNodes, ...assistantNodes].map((node) => node.content).join(' ');

	if (totalContent.length < CHAT_CONFIG.autoCollapse.summaryLength) return false;

	// Collapse if we have a good summary
	return !!summary;
}

// ============================================================================
// MESSAGE GROUPING
// ============================================================================

/**
 * Group exchanges into logical groups for display
 * Each group starts when the user provides input (using a response) and includes
 * all subsequent assistant responses until the next user input
 */
export function groupExchanges(exchanges: ProcessedExchange[]): ChatHistoryGroup[] {
	if (!exchanges.length) return [];

	const groups: ChatHistoryGroup[] = [];
	let currentGroup: ProcessedExchange[] = [];

	for (const exchange of exchanges) {
		// Determine if this exchange has user content
		const hasUserContent = exchange.userNodes.some(
			(node) => node.content && node.content.trim().length > 0
		);
		const hasAssistantContent = exchange.assistantNodes.length > 0;

		// Skip exchanges with no content
		if (!hasUserContent && !hasAssistantContent) {
			continue;
		}

		// Start a new group if this exchange has user content
		// This represents the user "using" the previous response and starting a new turn
		if (hasUserContent && currentGroup.length > 0) {
			groups.push(createGroup(currentGroup, groups.length));
			currentGroup = [];
		}

		// Add exchange to current group
		currentGroup.push(exchange);
	}

	// Add the final group
	if (currentGroup.length > 0) {
		groups.push(createGroup(currentGroup, groups.length));
	}

	console.log('Groups:', groups, exchanges);

	return groups;
}

/**
 * Create a group from a set of exchanges
 */
function createGroup(exchanges: ProcessedExchange[], groupIndex: number): ChatHistoryGroup {
	const firstExchange = exchanges[0];
	const lastExchange = exchanges[exchanges.length - 1];

	// Create group summary from the last exchange or multiple exchanges
	let groupSummary = '';
	if (exchanges.length === 1) {
		groupSummary = firstExchange.summary || extractLastSentencesFromExchange(firstExchange) || '';
	} else {
		// For multiple exchanges, try to get summary from the last exchange
		const lastExchangeSummary =
			lastExchange.summary || extractLastSentencesFromExchange(lastExchange);
		if (lastExchangeSummary) {
			groupSummary = lastExchangeSummary;
		} else {
			// Fallback to combining summaries from recent exchanges
			const summaries = exchanges
				.map((ex) => ex.summary || extractLastSentencesFromExchange(ex))
				.filter(Boolean)
				.slice(-2); // Take last 2 summaries

			if (summaries.length > 0) {
				groupSummary = summaries.join(' • ');
			} else {
				groupSummary = '';
			}
		}
	}

	// Determine if group should be collapsed (all but the last group)
	const shouldCollapse = exchanges.every((ex) => ex.isCollapsed);

	// Create unique group ID using group index to prevent duplicates
	// Include exchange count and timestamp for additional uniqueness
	const uniqueId = `group-${groupIndex}-${firstExchange.sequenceId}-${lastExchange.sequenceId}-${exchanges.length}`;

	return {
		id: uniqueId,
		timestamp: lastExchange.timestamp,
		exchanges,
		summary: groupSummary,
		isCollapsed: shouldCollapse,
		messageCount: exchanges.length
	};
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Extract last sentence or two from an exchange for summary
 */
function extractLastSentencesFromExchange(exchange: ProcessedExchange): string | undefined {
	if (!exchange.assistantNodes.length) return undefined;

	const lastNode = exchange.assistantNodes[exchange.assistantNodes.length - 1];
	if (!lastNode.content) return undefined;

	// Split content into sentences
	const sentences = lastNode.content
		.split(/[.!?]+/)
		.map((s) => s.trim())
		.filter((s) => s.length > 0);

	if (sentences.length === 0) return undefined;

	// Take the last 1-2 sentences
	if (sentences.length === 1) {
		return truncateText(sentences[0], CHAT_CONFIG.summary.maxLength);
	} else {
		const lastTwo = sentences.slice(-2).join('. ') + '.';
		const lastOne = sentences[sentences.length - 1] + '.';

		// If last two sentences are too long, just use the last one
		if (lastTwo.length > CHAT_CONFIG.summary.maxLength) {
			return truncateText(lastOne, CHAT_CONFIG.summary.maxLength);
		}

		return lastTwo;
	}
}

/**
 * Extract notebook notes from XML note tags in content
 */
function extractNotesFromContent(content: string): NotebookNote[] {
	const notes: NotebookNote[] = [];

	// Regex to match <note category="...">content</note> tags
	const noteRegex = /<note\s+category="([^"]+)">([^<]+)<\/note>/gi;
	let match;

	while ((match = noteRegex.exec(content)) !== null) {
		const category = match[1] as NoteCategory;
		const noteContent = match[2].trim();

		// Validate category
		// const validCategories: NoteCategory[] = [
		// 	'code_architecture',
		// 	'implementation_details',
		// 	'user_preferences',
		// 	'technical_constraints',
		// 	'business_logic',
		// 	'integration_points'
		// ];

		// if (validCategories.includes(category)) {
		notes.push({
			content: noteContent,
			category,
			timestamp: new Date().toISOString()
		});
		// }
	}

	return notes;
}

/**
 * Truncate text to a maximum length
 */
function truncateText(text: string, maxLength: number): string {
	if (text.length <= maxLength) return text;

	// Try to break at word boundary
	const truncated = text.substring(0, maxLength);
	const lastSpace = truncated.lastIndexOf(' ');

	if (lastSpace > maxLength * 0.8) {
		return truncated.substring(0, lastSpace) + '...';
	}

	return truncated + '...';
}

/**
 * Extract all notebook notes from chat history exchanges
 */
export function extractNotesFromChatHistory(exchanges: CleanChatExchangeData[]): NotebookNote[] {
	const allNotes: NotebookNote[] = [];

	for (const exchange of exchanges) {
		// Extract notes from assistant nodes
		const responseNodes = exchange.exchange.responseNodes || [];
		for (const node of responseNodes) {
			const content = extractNodeContent(node, false);
			if (content) {
				const notes = extractNotesFromContent(content);
				// Add exchange ID to notes for tracking
				notes.forEach((note) => {
					note.exchangeId = exchange.exchange.requestId;
					allNotes.push(note);
				});
			}
		}
	}

	return allNotes;
}

/**
 * Format timestamp for display
 */
export function formatTimestamp(timestamp: string): string {
	const date = new Date(timestamp);
	const now = new Date();
	const diffMs = now.getTime() - date.getTime();
	const diffMins = Math.floor(diffMs / (1000 * 60));
	const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
	const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

	if (diffMins < 1) return 'Just now';
	if (diffMins < 60) return `${diffMins}m ago`;
	if (diffHours < 24) return `${diffHours}h ago`;
	if (diffDays < 7) return `${diffDays}d ago`;

	return date.toLocaleDateString();
}

/**
 * Check if content should be rendered as markdown
 */
export function shouldRenderAsMarkdown(content: string): boolean {
	// Simple heuristics for markdown detection
	const markdownPatterns = [
		/^#{1,6}\s/, // Headers
		/\*\*.*\*\*/, // Bold
		/\*.*\*/, // Italic
		/`.*`/, // Code
		/\[.*\]\(.*\)/, // Links
		/^[-*+]\s/, // Lists
		/^>\s/ // Blockquotes
	];

	return markdownPatterns.some((pattern) => pattern.test(content));
}

/**
 * Extract tool name from tool use node
 */
export function extractToolName(
	toolUse: { tool_name?: string; toolName?: string } | null | undefined
): string {
	const name = toolUse?.tool_name || toolUse?.toolName;
	// Don't prefix with "Unknown Tool" for known tool names
	if (name === 'setup-script') return name;
	return name || 'Unknown Tool';
}

/**
 * Smart error detection for tool result nodes
 */
function isToolResultError(node: any): boolean {
	// Check the is_error flag first
	if (node.tool_result_node?.is_error) return true;

	// Check content for error keywords
	const content = node.tool_result_node?.content;
	if (!content) return false;

	const lowerContent = content.toLowerCase();
	return (
		lowerContent.includes('npm error') ||
		lowerContent.includes('error:') ||
		lowerContent.includes('error ') ||
		lowerContent.includes('failed') ||
		lowerContent.includes('exception') ||
		lowerContent.includes('traceback') ||
		lowerContent.includes('fatal:') ||
		lowerContent.includes('exit code 1')
	);
}

/**
 * Check if a node represents an error state
 */
export function isErrorNode(node: ProcessedNode): boolean {
	return node.isError || node.toolResult?.is_error || node.content.toLowerCase().includes('error');
}
