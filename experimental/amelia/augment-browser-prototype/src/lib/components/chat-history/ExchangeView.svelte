<script lang="ts">
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { Icon, ChevronDown } from 'svelte-hero-icons';
	import NodeRenderer from './nodes/NodeRenderer.svelte';
	import { CHAT_STYLES, CHAT_CONFIG } from './config';

	import type { ProcessedExchange } from './utils';

	interface Props {
		exchange: ProcessedExchange;
		isStreaming?: boolean;
		showDebugInfo?: boolean;
		autoCollapse?: boolean;
		shouldFlashUserMessage?: boolean;
	}

	let {
		exchange,
		isStreaming = false,
		showDebugInfo = false,
		autoCollapse = true,
		shouldFlashUserMessage = false
	}: Props = $props();

	// State management
	let isExpanded = $state(!autoCollapse || !exchange.isCollapsed);

	// Derived values
	let hasAssistantNodes = $derived(exchange.assistantNodes.length > 0);

	// Check if user nodes have actual content (not blank)
	let hasUserContent = $derived(
		exchange.userNodes.some((node) => node.content && node.content.trim().length > 0)
	);
	let displaySummary = $derived(exchange.summary || 'Exchange');

	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleExpanded();
		}
	}
</script>

<div class="exchange-view" data-exchange-id={exchange.id}>
	{#if exchange.isCollapsed && autoCollapse}
		<!-- Collapsed view with summary -->
		<div class={CHAT_STYLES.collapsed.container}>
			<button
				class={CHAT_STYLES.collapsed.header}
				onclick={toggleExpanded}
				onkeydown={handleKeydown}
				aria-expanded={isExpanded}
				aria-label="Expand exchange"
			>
				<div class="flex items-center justify-between">
					<div class="flex min-w-0 flex-1 items-center gap-2">
						<!-- Summary text -->
						<div class="min-w-0 flex-1">
							<div class={`${CHAT_STYLES.collapsed.summary} truncate`}>
								{displaySummary}
							</div>
						</div>
					</div>

					<!-- Expand icon -->
					<div class={`${CHAT_STYLES.collapsed.expandIcon} ${isExpanded ? 'rotate-180' : ''}`}>
						<Icon src={ChevronDown} class="h-4 w-4" />
					</div>
				</div>
			</button>
		</div>
	{/if}

	{#if isExpanded || !exchange.isCollapsed || !autoCollapse}
		<!-- Expanded view with full content -->
		<div
			class={CHAT_STYLES.messages.exchange}
			transition:slide={{ duration: CHAT_CONFIG.animations.duration, easing: quintOut }}
		>
			<!-- User messages - only show if they have actual content -->
			{#if hasUserContent}
				<div class="mb-3 flex justify-end">
					<div
						class="max-w-[90%] rounded-lg bg-slate-100 px-5 text-sm shadow-sm dark:bg-slate-800 {shouldFlashUserMessage
							? 'user-message-flash'
							: ''}"
					>
						<div class="text-slate-900 dark:text-slate-100">
							{#each exchange.userNodes as node (node.id)}
								{#if node.content && node.content.trim().length > 0}
									<NodeRenderer {node} {showDebugInfo} />
								{/if}
							{/each}
						</div>
					</div>
				</div>
			{/if}

			<!-- Assistant messages -->
			{#if hasAssistantNodes}
				<div>
					{#each exchange.assistantNodes as node, index (node.id)}
						<NodeRenderer
							{node}
							{showDebugInfo}
							{isStreaming}
							requestId={exchange.id}
							isLastNode={index === exchange.assistantNodes.length - 1}
						/>
					{/each}
				</div>
			{/if}
		</div>
	{/if}

	{#if showDebugInfo}
		<!-- Debug information -->
		<div
			class="mt-2 rounded border border-blue-200 bg-blue-50 p-2 text-xs dark:border-blue-800 dark:bg-blue-900/20"
		>
			<div class="space-y-1 font-mono">
				<div><strong>Exchange ID:</strong> {exchange.id}</div>
				<div><strong>Sequence ID:</strong> {exchange.sequenceId}</div>
				<div><strong>User Nodes:</strong> {exchange.userNodes.length}</div>
				<div><strong>Assistant Nodes:</strong> {exchange.assistantNodes.length}</div>
				<div><strong>Is Collapsed:</strong> {exchange.isCollapsed}</div>
				<div><strong>Has Tool Uses:</strong> {exchange.hasToolUses}</div>
				<div><strong>Has Errors:</strong> {exchange.hasErrors}</div>
				{#if exchange.summary}
					<div><strong>Summary:</strong> {exchange.summary}</div>
				{/if}
			</div>
		</div>
	{/if}
</div>
