/**
 * Configuration for chat history components
 * Centralizes styling, behavior, and display options
 */

import type { ChatRequestNodeType, ChatResultNodeType } from '$lib/api/unified-client';

// ============================================================================
// DISPLAY CONFIGURATION
// ============================================================================

export const CHAT_CONFIG = {
	// Auto-collapse settings
	autoCollapse: {
		enabled: true,
		maxHeight: 120, // pixels
		summaryLength: 150 // characters
	},

	// Message grouping
	grouping: {
		timeThreshold: 5 * 60 * 1000, // 5 minutes in milliseconds
		maxMessagesPerGroup: 10
	},

	// Animation settings
	animations: {
		duration: 300,
		easing: 'quintOut',
		stagger: 50 // delay between items
	},

	// Scrolling behavior
	scrolling: {
		autoScroll: true,
		scrollThreshold: 300, // pixels from bottom
		smoothScroll: true
	},

	// Summary extraction
	summary: {
		maxLength: 200,
		fallbackLength: 100,
		prioritizeTags: true // Prioritize <summary> tags
	}
} as const;

// ============================================================================
// STYLING CONFIGURATION
// ============================================================================

export const CHAT_STYLES = {
	// Base container styles
	container: 'relative flex flex-col h-full min-h-0 mb-3',

	// Message containers
	messages: {
		container: 'flex-1 min-h-0 overflow-y-auto px-6 py-4 space-y-4 scroll-smooth',
		group: 'space-y-3',
		exchange: 'space-y-2'
	},

	// User messages
	user: {
		container: 'flex justify-end',
		bubble: 'max-w-[80%] bg-blue-500 text-white rounded-lg px-3 py-2 text-sm shadow-sm',
		text: 'whitespace-pre-wrap break-words'
	},

	// Assistant messages
	assistant: {
		container: 'flex justify-start',
		bubble: 'max-w-[85%] bg-gray-100 dark:bg-gray-800 rounded-lg px-4 py-3 text-sm shadow-sm',
		text: 'whitespace-pre-wrap break-words text-gray-900 dark:text-gray-100',
		summary: 'text-gray-700 dark:text-gray-300 italic'
	},

	// Collapsed states
	collapsed: {
		container: '',
		header:
			'px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors rounded-md',
		content: '',
		summary: 'text-sm text-gray-600 dark:text-gray-400',
		expandIcon: 'text-gray-400 dark:text-gray-500 transition-transform'
	},

	// Node-specific styles
	nodes: {
		text: 'prose prose-sm dark:prose-invert max-w-none',
		toolResult: {
			container: 'border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden',
			header: 'bg-gray-50 dark:bg-gray-800 px-3 py-2 text-sm font-medium',
			content: 'px-3 py-2 bg-gray-900 text-gray-100 font-mono text-xs overflow-x-auto',
			error: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
		},
		toolUse: {
			container: 'border border-blue-200 dark:border-blue-800 rounded-md overflow-hidden',
			header:
				'bg-blue-50 dark:bg-blue-900/20 px-3 py-2 text-sm font-medium text-blue-900 dark:text-blue-100',
			content: 'px-3 py-2 bg-gray-50 dark:bg-gray-800'
		},
		image: {
			container: 'border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden',
			header: 'bg-gray-50 dark:bg-gray-800 px-3 py-2 text-sm font-medium',
			content: 'p-3',
			image: 'max-w-full h-auto rounded'
		}
	},

	// Loading states
	loading: {
		container: 'flex items-center justify-center py-12',
		spinner: 'animate-spin h-5 w-5 text-gray-400',
		text: 'ml-3 text-sm text-gray-500 dark:text-gray-400'
	},

	// Empty states
	empty: {
		container: 'flex items-center justify-center py-12 text-center',
		text: 'text-sm text-gray-500 dark:text-gray-400',
		subtext: 'text-xs text-gray-400 dark:text-gray-500 mt-1'
	},

	// Timestamps
	timestamp: 'text-xs text-gray-400 dark:text-gray-500 mb-2',

	// Status indicators
	status: {
		streaming: 'inline-flex items-center text-xs text-blue-600 dark:text-blue-400',
		error: 'inline-flex items-center text-xs text-red-600 dark:text-red-400',
		complete: 'inline-flex items-center text-xs text-green-600 dark:text-green-400'
	}
} as const;

// ============================================================================
// NODE TYPE CONFIGURATION
// ============================================================================

export const NODE_TYPE_CONFIG = {
	// Request node types
	request: {
		[0]: {
			// TEXT
			name: 'Text',
			icon: '💬',
			collapsible: true
		},
		[1]: {
			// TOOL_RESULT
			name: 'Tool Result',
			icon: '🔧',
			collapsible: false
		},
		[2]: {
			// IMAGE
			name: 'Image',
			icon: '🖼️',
			collapsible: false
		},
		[3]: {
			// IMAGE_ID
			name: 'Image Reference',
			icon: '🔗',
			collapsible: false
		},
		[4]: {
			// IDE_STATE
			name: 'IDE State',
			icon: '💻',
			collapsible: false
		},
		[5]: {
			// EDIT_EVENTS
			name: 'Edit Events',
			icon: '✏️',
			collapsible: false
		},
		[6]: {
			// CHECKPOINT_REF
			name: 'Checkpoint',
			icon: '📍',
			collapsible: false
		}
	},

	// Response node types
	response: {
		[0]: {
			// RAW_RESPONSE
			name: 'Response',
			icon: '🤖',
			collapsible: false
		},
		[1]: {
			// SUGGESTED_QUESTIONS
			name: 'Suggested Questions',
			icon: '❓',
			collapsible: false
		},
		[2]: {
			// MAIN_TEXT_FINISHED
			name: 'Response Complete',
			icon: '✅',
			collapsible: false
		},
		[3]: {
			// WORKSPACE_FILE_CHUNKS
			name: 'Workspace Files',
			icon: '📁',
			collapsible: false
		},
		[4]: {
			// RELEVANT_SOURCES
			name: 'Sources',
			icon: '📚',
			collapsible: false
		},
		[5]: {
			// TOOL_USE
			name: 'Tool Use',
			icon: '🛠️',
			collapsible: false
		},
		[7]: {
			// TOOL_USE_START
			name: 'Tool Use Start',
			icon: '🔧',
			collapsible: false
		},
		[8]: {
			// THINKING
			name: 'Thinking',
			icon: '🧠',
			collapsible: false
		}
	}
} as const;

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get configuration for a request node type
 */
export function getRequestNodeConfig(type: ChatRequestNodeType) {
	return (
		NODE_TYPE_CONFIG.request[type] || {
			name: 'Unknown',
			icon: '❓',
			collapsible: false
		}
	);
}

/**
 * Get configuration for a response node type
 */
export function getResponseNodeConfig(type: ChatResultNodeType) {
	return (
		(NODE_TYPE_CONFIG.response as any)[type] || {
			name: 'Unknown',
			icon: '❓',
			collapsible: false
		}
	);
}

/**
 * Check if a node type should be collapsible
 */
export function isNodeCollapsible(type: number, isRequest: boolean = true) {
	const config = isRequest
		? (NODE_TYPE_CONFIG.request as any)[type]
		: (NODE_TYPE_CONFIG.response as any)[type];

	return config?.collapsible ?? false;
}

/**
 * Get display name for a node type
 */
export function getNodeDisplayName(type: number, isRequest: boolean = true) {
	const config = isRequest
		? (NODE_TYPE_CONFIG.request as any)[type]
		: (NODE_TYPE_CONFIG.response as any)[type];

	return config?.name ?? 'Unknown';
}

/**
 * Get icon for a node type
 */
export function getNodeIcon(type: number, isRequest: boolean = true) {
	const config = isRequest
		? (NODE_TYPE_CONFIG.request as any)[type]
		: (NODE_TYPE_CONFIG.response as any)[type];

	return config?.icon ?? '❓';
}
