<script lang="ts">
	import { onMount, tick } from 'svelte';
	import { fly, slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { ArrowDown, ArrowUp, ChevronDown, Icon } from 'svelte-hero-icons';
	import {
		ChatResultNodeType,
		type CleanChatExchangeData,
		RemoteAgentWorkspaceStatus,
		type CleanRemoteAgent,
		RemoteAgentStatus
	} from '$lib/api/unified-client';
	import ExchangeView from './ExchangeView.svelte';
	import NodeRenderer from './nodes/NodeRenderer.svelte';
	import TurnSummary from './TurnSummary.svelte';
	import FeedbackPanel from '../ui/feedback/FeedbackPanel.svelte';
	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import SummaryWidget from '$lib/components/ui/widgets/SummaryWidget.svelte';
	import { CHAT_STYLES, CHAT_CONFIG } from './config';
	import { processExchangesWithToolFiltering, groupExchanges } from './utils';
	import type { OptimisticMessage, StreamingMessage } from '$lib/stores/global-state.svelte';
	import type { ChatHistoryGroup } from './utils';
	import ChatProgressIndicator from './ChatProgressIndicator.svelte';

	import SummaryRenderer from './nodes/SummaryRenderer.svelte';
	import AgentStatusIndicator from '$lib/components/agents/AgentStatusIndicator.svelte';

	import { getAgentIndicatorConfig } from '$lib/utils/agent-status-indicators.svelte';
	import {
		pendingMessagesByAgent,
		getOldestPendingMessageTime
	} from '$lib/utils/optimistic-messages';

	interface Props {
		exchanges: CleanChatExchangeData[];
		isLoading?: boolean;
		showDebugInfo?: boolean;
		autoCollapse?: boolean;
		maxHeight?: string;
		onScrollToBottom?: () => void;
		optimisticMessage?: OptimisticMessage;
		isStreaming?: boolean;
		streamingContent?: string;
		streamingMessages?: Map<string, StreamingMessage>;
		workspaceStatus?: RemoteAgentWorkspaceStatus;
		agent?: CleanRemoteAgent | null;
		exchangeGroups?: ChatHistoryGroup[];
		onUpdateCodeView?: (exchangeIndex: number) => void;
	}

	let {
		exchanges = [],
		isLoading = false,
		showDebugInfo = false,
		autoCollapse = true,
		maxHeight = '100%',
		onScrollToBottom,
		optimisticMessage,
		isStreaming = false,
		streamingContent = '',
		streamingMessages = new Map(),
		workspaceStatus,
		agent = null,
		exchangeGroups = [],
		onUpdateCodeView
	}: Props = $props();

	// DOM references
	let chatContainer: HTMLDivElement;

	// State management
	let shouldAutoScroll = $state<boolean>(CHAT_CONFIG.scrolling.autoScroll);
	let isUserScrolling = $state(false);
	let showScrollButton = $state(false);
	let lastExchangeCount = $state(0);
	let groupExpanded = $state<Record<string, boolean>>({});

	// Navigation state
	let currentMessageIndex = $state(0); // Start at first message
	let messageElements = $state<HTMLElement[]>([]);
	let isNavigating = $state(false); // Flag to prevent scroll conflicts
	let navigationTimeout: ReturnType<typeof setTimeout> | null = null;
	let flashingMessageIndex = $state(-1); // Index of message to flash, -1 means no flash

	// Process exchanges into structured format with cross-exchange tool filtering
	let processedExchanges = $derived(processExchangesWithToolFiltering(exchanges));

	// Create a mapping from exchange requestId to original index
	let exchangeIndexMap = $derived.by(() => {
		const map = new Map<string, number>();
		exchanges.forEach((exchange, index) => {
			map.set(exchange.exchange.requestId, index);
		});
		return map;
	});

	// Collect all tool calls from all exchanges for linking tool results
	let allToolCalls = $derived.by(() => {
		const toolCalls: any[] = [];
		processedExchanges.forEach((exchange) => {
			// Tool calls are in assistantNodes with type 5 (TOOL_USE)
			exchange.assistantNodes.forEach((node) => {
				if (
					node.type === ChatResultNodeType.TOOL_USE &&
					(node.toolUse || node.metadata?.tool_use)
				) {
					toolCalls.push(node);
				}
			});
		});
		return toolCalls;
	});

	// Use provided exchangeGroups or derive from processedExchanges
	let finalExchangeGroups = $derived(
		exchangeGroups.length > 0 ? exchangeGroups : groupExchanges(processedExchanges)
	);

	function getCleanResponseText(exchange: any) {
		return (
			exchange.assistantNodes
				.map((node: any) => node.content)
				.join(' ')
				.replace(/<note\s+category="[^"]+">([^<]+)<\/note>/gi, '')
				// remove summary & general_summary tags
				.replace(/<summary>([\s\S]*?)<\/summary>/gi, '')
				.replace(/<general_summary>([\s\S]*?)<\/general_summary>/gi, '')
		);
	}

	// Check if we should auto-scroll when new content arrives
	$effect(() => {
		if (
			exchanges.length > lastExchangeCount &&
			shouldAutoScroll &&
			chatContainer &&
			!isNavigating
		) {
			// Use a small delay to ensure DOM has updated
			setTimeout(() => {
				tick().then(() => {
					scrollToBottom(true);
				});
			}, 50);
		}
		lastExchangeCount = exchanges.length;
	});

	// Also auto-scroll when processedExchanges change (for streaming updates)
	$effect(() => {
		if (processedExchanges.length > 0 && shouldAutoScroll && chatContainer && !isNavigating) {
			// Check if we're already near the bottom
			const { scrollTop, scrollHeight, clientHeight } = chatContainer;
			const isNearBottom =
				scrollHeight - scrollTop - clientHeight < CHAT_CONFIG.scrolling.scrollThreshold;

			if (isNearBottom) {
				setTimeout(() => {
					tick().then(() => {
						scrollToBottom(false); // Use instant scroll for streaming updates
					});
				}, 10);
			}
		}
	});

	// Auto-scroll when streaming content changes - handled by onContentChange callback

	// Function to handle content changes from streaming text
	function handleStreamingContentChange() {
		if (shouldAutoScroll && chatContainer && !isNavigating) {
			// Use multiple animation frames to ensure DOM has updated
			requestAnimationFrame(() => {
				requestAnimationFrame(() => {
					scrollToBottom(false);
				});
			});
		}
	}

	// Auto-scroll when optimistic message is added
	$effect(() => {
		if (optimisticMessage && shouldAutoScroll && chatContainer && !isNavigating) {
			setTimeout(() => {
				tick().then(() => {
					scrollToBottom(true); // Smooth scroll for user messages
				});
			}, 50);
		}
	});

	// Auto-scroll when agent status indicator becomes active (e.g., "generating response" appears)
	$effect(() => {
		if (agent && shouldAutoScroll && chatContainer && !isNavigating) {
			// Check for pending messages
			const hasPending =
				$pendingMessagesByAgent.has(agent.id) && $pendingMessagesByAgent.get(agent.id)!.length > 0;
			const messageStartTime = getOldestPendingMessageTime(agent.id);

			// Get indicator configuration
			const indicatorConfig = getAgentIndicatorConfig(
				agent,
				isStreaming,
				hasPending,
				{ phase: 'idle' },
				messageStartTime || undefined
			);

			// Auto-scroll when indicator becomes active
			if (indicatorConfig.isActive) {
				setTimeout(() => {
					tick().then(() => {
						scrollToBottom(true); // Smooth scroll for status changes
					});
				}, 50);
			}
		}
	});

	// Handle scroll events
	function handleScroll() {
		if (!chatContainer) return;

		const { scrollTop, scrollHeight, clientHeight } = chatContainer;
		const isNearBottom =
			scrollHeight - scrollTop - clientHeight < CHAT_CONFIG.scrolling.scrollThreshold;

		// Update auto-scroll behavior based on user position
		shouldAutoScroll = isNearBottom;
		showScrollButton = !isNearBottom && scrollHeight > clientHeight;

		// Update current message index based on scroll position (only when not navigating)
		if (!isNavigating) {
			currentMessageIndex = detectCurrentMessage();
		}

		// Detect if user is actively scrolling
		isUserScrolling = true;
		clearTimeout(scrollTimeout);
		scrollTimeout = setTimeout(() => {
			isUserScrolling = false;
		}, 150);
	}

	let scrollTimeout: ReturnType<typeof setTimeout>;

	// Scroll to bottom function
	function scrollToBottom(smooth: boolean = CHAT_CONFIG.scrolling.smoothScroll) {
		if (!chatContainer) return;
		if (isNavigating) return;

		const scrollOptions: ScrollToOptions = {
			top: chatContainer.scrollHeight,
			behavior: smooth ? 'smooth' : 'instant'
		};

		chatContainer.scrollTo(scrollOptions);
		console.log('Scrolling to bottom', scrollOptions, shouldAutoScroll);

		// Call callback if provided
		if (onScrollToBottom) {
			onScrollToBottom();
		}
	}

	// Handle scroll to bottom button click
	function handleScrollToBottomClick() {
		if (messageElements.length > 0) {
			currentMessageIndex = messageElements.length - 1;
			scrollToMessage(currentMessageIndex);
		}
		shouldAutoScroll = true;
	}

	// Initialize scroll position
	onMount(() => {
		if (chatContainer && exchanges.length > 0) {
			// Start at bottom for new conversations
			tick().then(() => {
				scrollToBottom(false);
			});
		}
	});

	// Helper function to clear navigation state
	function clearNavigationState() {
		if (navigationTimeout) {
			clearTimeout(navigationTimeout);
		}
		navigationTimeout = setTimeout(() => {
			isNavigating = false;
			navigationTimeout = null;
		}, 600); // Slightly longer timeout to ensure scroll completes
	}

	// Helper function to detect which message is currently focused
	function detectCurrentMessage() {
		if (!chatContainer || messageElements.length === 0) return 0;

		const containerRect = chatContainer.getBoundingClientRect();
		const viewportMiddle = containerRect.top + containerRect.height / 2;

		// Find the first message whose bottom edge is past the viewport middle
		for (let i = 0; i < messageElements.length; i++) {
			const elementRect = messageElements[i].getBoundingClientRect();
			const elementBottom = elementRect.bottom;

			if (elementBottom > viewportMiddle) {
				return i;
			}
		}

		// If no message bottom is past middle, we're at the last message
		return messageElements.length - 1;
	}

	// Update message elements when exchanges change
	$effect(() => {
		// used to trigger reactivity atm
		console.log(exchangeGroups.length, exchanges);
		// Only depend on exchangeGroups, not messageElements to avoid infinite loop
		if (chatContainer && exchangeGroups.length >= 0) {
			// Find all message elements (exchange groups)
			const elements = Array.from(
				chatContainer.querySelectorAll('.exchange-group')
			) as HTMLElement[];

			// Only update if the count actually changed to prevent loops
			if (elements.length !== messageElements.length) {
				messageElements = elements;

				// Reset navigation if we're beyond the available messages
				if (currentMessageIndex >= elements.length) {
					currentMessageIndex = -1;
				}
			}
		}
	});

	// Navigation functions
	function scrollToPreviousMessage() {
		if (!chatContainer || messageElements.length === 0) return;

		if (currentMessageIndex <= 0) {
			// Already at first message, do nothing
			return;
		}

		currentMessageIndex--;
		scrollToMessage(currentMessageIndex);
		triggerFlash(currentMessageIndex);
	}

	function scrollToNextMessage() {
		if (!chatContainer || messageElements.length === 0) return;

		if (currentMessageIndex >= messageElements.length - 1) {
			// Already at last message, do nothing
			return;
		}

		currentMessageIndex++;
		scrollToMessage(currentMessageIndex);
		triggerFlash(currentMessageIndex);
	}

	// Trigger flash animation for user message
	function triggerFlash(messageIndex: number) {
		flashingMessageIndex = messageIndex;
		// Clear flash after animation completes
		setTimeout(() => {
			flashingMessageIndex = -1;
		}, 600); // Match animation duration
	}

	function scrollToMessage(index: number) {
		if (!chatContainer || index < 0 || index >= messageElements.length) return;

		const element = messageElements[index];
		if (element) {
			// Set navigation flag to prevent conflicts
			isNavigating = true;

			// Scroll to the message with some padding
			const elementTop = element.offsetTop;
			const containerHeight = chatContainer.clientHeight;
			const scrollPosition = Math.max(0, elementTop - containerHeight / 4);

			chatContainer.scrollTo({
				top: scrollPosition,
				behavior: 'smooth'
			});
			console.log('Scrolling to message', index, 'at position', scrollPosition);

			// Disable auto-scroll when manually navigating
			shouldAutoScroll = false;

			// Clear navigation flag after scroll completes
			clearNavigationState();
		}
	}

	// Keyboard navigation
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'End' && (event.ctrlKey || event.metaKey)) {
			event.preventDefault();
			handleScrollToBottomClick();
		} else if (event.key === 'ArrowUp' && (event.ctrlKey || event.metaKey)) {
			event.preventDefault();
			scrollToPreviousMessage();
		} else if (event.key === 'ArrowDown' && (event.ctrlKey || event.metaKey)) {
			event.preventDefault();
			scrollToNextMessage();
		}
	}

	// Get empty state message
	function getEmptyStateMessage(): { title: string; subtitle: string } {
		if (isLoading) {
			return {
				title: 'Loading conversation...',
				subtitle: 'Please wait while we fetch the chat history'
			};
		}

		return {
			title: 'No conversation yet',
			subtitle: 'Start a conversation to see messages here'
		};
	}

	let emptyState = $derived(getEmptyStateMessage());
</script>

<svelte:window onkeydown={handleKeydown} />

<div class={CHAT_STYLES.container} style="max-height: {maxHeight};">
	<!-- Navigation controls -->
	{#if processedExchanges.length > 0}
		<div
			class="sticky top-0 z-20 flex items-center justify-between gap-2 bg-white/80 p-2 backdrop-blur-sm dark:bg-slate-900/80"
		>
			<!-- Progress indicator (left side) -->
			{#if finalExchangeGroups.length > 1}
				<div class="min-w-0 flex-1">
					<ChatProgressIndicator
						groups={finalExchangeGroups}
						onSegmentClick={(groupId) => {
							const index = finalExchangeGroups.findIndex((g) => g.id === groupId);
							if (index !== undefined) {
								currentMessageIndex = index;
								scrollToMessage(index);
							}
						}}
					/>
				</div>
			{:else}
				<div class="flex-1"></div>
			{/if}

			<!-- Navigation controls (right side) -->
			<div class="ml-3 flex items-center">
				<!-- Position indicator -->
				{#if messageElements.length > 0}
					<span class="mr-2 text-xs text-slate-500 dark:text-slate-400">
						{currentMessageIndex + 1} / {messageElements.length}
					</span>
				{/if}

				<Button
					variant="ghost"
					size="icon-sm"
					icon={ArrowUp}
					onclick={scrollToPreviousMessage}
					disabled={messageElements.length === 0}
					aria-label="Previous message (Cmd+↑)"
					title="Previous message (Cmd+↑)"
				/>
				<Button
					variant="ghost"
					size="icon-sm"
					icon={ArrowDown}
					onclick={scrollToNextMessage}
					disabled={messageElements.length === 0}
					aria-label="Next message (Cmd+↓)"
					title="Next message (Cmd+↓)"
				/>
			</div>
		</div>
	{/if}

	<!-- Chat messages container -->
	<div
		bind:this={chatContainer}
		class={CHAT_STYLES.messages.container}
		onscroll={handleScroll}
		role="log"
		aria-label="Chat history"
		aria-live="polite"
	>
		{#if isLoading && processedExchanges.length === 0}
			<!-- Loading state -->
			<div class={CHAT_STYLES.loading.container}>
				<LoadingIndicator variant="spinner" class={CHAT_STYLES.loading.spinner} />
				<span class={CHAT_STYLES.loading.text}>{emptyState.title}</span>
			</div>
		{:else if processedExchanges.length === 0}
			<!-- Empty state -->
			<div class={CHAT_STYLES.empty.container}>
				<div>
					<div class={CHAT_STYLES.empty.text}>{emptyState.title}</div>
					<div class={CHAT_STYLES.empty.subtext}>{emptyState.subtitle}</div>
				</div>
			</div>
		{:else}
			<!-- Chat content -->
			<div class={CHAT_STYLES.messages.group}>
				{#each finalExchangeGroups as group, groupIndex (group.id)}
					<div class="exchange-group mb-6" data-group-id={group.id}>
						<!-- Check if this is an assistant group (single or multiple) -->
						{#if group.exchanges.every((ex) => ex.assistantNodes.length > 0 && !ex.userNodes.some((node) => node.content && node.content.trim().length > 0))}
							<!-- Render consecutive assistant responses as one collapsible unit -->
							{@const groupId = `group-${group.exchanges[0]?.id || 'unknown'}`}
							{@const allNodes = group.exchanges.flatMap((ex) => ex.assistantNodes)}
							{@const shouldAutoCollapse = false && allNodes.length > 3}
							{@const groupSummary =
								group.exchanges.length === 1
									? 'Assistant response'
									: `${group.exchanges.length} assistant responses`}
							{@const isExpanded = groupExpanded[groupId] ?? !shouldAutoCollapse}

							<div class="assistant-group">
								{#if shouldAutoCollapse}
									<button
										class="w-full rounded-lg border border-slate-200 bg-slate-50 p-3 text-left transition-colors hover:bg-slate-100 dark:border-slate-700 dark:bg-slate-800/50 dark:hover:bg-slate-700/50"
										onclick={() => (groupExpanded[groupId] = !isExpanded)}
									>
										<div class="flex items-center justify-between">
											<span class="text-sm font-medium text-slate-700 dark:text-slate-300">
												{groupSummary}
											</span>
											<Icon
												src={ChevronDown}
												class="h-4 w-4 text-slate-500 transition-transform {isExpanded
													? 'rotate-180'
													: ''}"
											/>
										</div>
									</button>

									{#if isExpanded}
										<div
											class="mt-2 space-y-2"
											transition:slide={{ duration: 200, easing: quintOut }}
										>
											{#each group.exchanges as exchange (exchange.id)}
												{#each exchange.assistantNodes as node (node.id)}
													<NodeRenderer
														requestId={exchange.id}
														{node}
														{showDebugInfo}
														hideCollapseHeader={true}
														toolCalls={allToolCalls}
													/>
												{/each}
											{/each}
										</div>
									{/if}
								{:else}
									<!-- Show directly if not many nodes -->
									<div class="space-y-2">
										{#each group.exchanges as exchange, exchangeIndex (exchange.id)}
											{#each exchange.assistantNodes as node, nodeIndex (node.id)}
												<div class="text-slate-900 dark:text-slate-100">
													<NodeRenderer
														requestId={exchange.id}
														{node}
														{showDebugInfo}
														hideCollapseHeader={true}
														toolCalls={allToolCalls}
														isLastNode={exchangeIndex === group.exchanges.length - 1 &&
															nodeIndex === exchange.assistantNodes.length - 1}
													/>
												</div>
											{/each}
										{/each}

										{#if group.summary}
											<div class="mb-3">
												<SummaryWidget summary={group.summary} />
											</div>
										{/if}
									</div>
								{/if}
							</div>
						{:else}
							<!-- Render individual exchanges normally -->
							{#each group.exchanges as exchange, index (exchange.id)}
								<div class="my-3">
									<ExchangeView
										{exchange}
										isStreaming={isStreaming &&
											groupIndex === finalExchangeGroups.length - 1 &&
											index === group.exchanges.length - 1}
										{showDebugInfo}
										autoCollapse={false}
										shouldFlashUserMessage={flashingMessageIndex === groupIndex}
									/>
								</div>
							{/each}
						{/if}

						<!-- Combined TurnSummary and FeedbackPanel -->
						{#if group.exchanges.length > 0}
							{@const lastExchange = group.exchanges[group.exchanges.length - 1]}
							{@const lastExchangeIndex = exchangeIndexMap.get(lastExchange.id)}
							{@const isLastGroup = groupIndex === finalExchangeGroups.length - 1}
							{#if lastExchangeIndex !== undefined}
								<div class="mt-8" transition:slide={{ axis: 'y', duration: 200 }}>
									<!-- Shared top border container -->
									<div
										class="border-t border-slate-200/60 bg-slate-50/30 dark:border-slate-700/60 dark:bg-slate-800/20"
									>
										<div class="flex items-center gap-4 px-1 py-3">
											<!-- Left side: FeedbackPanel (only for last group and not streaming) -->
											{#if isLastGroup && !isStreaming && agent?.status === RemoteAgentStatus.AGENT_IDLE && agent?.workspaceStatus !== RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING}
												<div
													class="-mt-2 flex-shrink-0"
													transition:slide={{ axis: 'y', duration: 200 }}
												>
													<FeedbackPanel
														responseText={getCleanResponseText(lastExchange)}
														requestId={lastExchange.id}
													/>
												</div>
											{/if}

											<!-- Right side: TurnSummary -->
											<div class="min-w-0 flex-1">
												<TurnSummary
													{group}
													{exchanges}
													exchangeIndex={lastExchangeIndex}
													{onUpdateCodeView}
													hideContainer={true}
												/>
											</div>
										</div>
									</div>
								</div>
							{/if}
						{/if}
					</div>
				{/each}

				<!-- Optimistic user message -->
				{#if optimisticMessage && optimisticMessage.type === 'user'}
					<div class="my-3" in:fly={{ y: 20, duration: 200 }}>
						<div class="mb-3 flex justify-end">
							<div
								class="max-w-[90%] rounded-lg bg-slate-100 px-5 text-sm opacity-70 shadow-sm dark:bg-slate-800"
							>
								<div class="my-3 text-slate-900 dark:text-slate-100">
									<SummaryRenderer content={optimisticMessage.content} shouldUseMarkdown={false} />
								</div>
							</div>
						</div>
					</div>
				{/if}

				<!-- Streaming assistant response -->
				{#if isStreaming && streamingContent}
					<div class="my-3" in:fly={{ y: 20, duration: 200 }}>
						<div class="text-slate-900 dark:text-slate-100">
							{'isStreaming'}
							<SummaryRenderer
								content={streamingContent}
								shouldUseMarkdown={true}
								isStreaming={true}
								onContentChange={handleStreamingContentChange}
							/>
						</div>
					</div>
				{/if}

				<!-- New streaming messages from Map -->
				{#each Array.from(streamingMessages.values()) as streamingMessage (streamingMessage.tempId)}
					<div class="my-3" in:fly={{ y: 20, duration: 200 }}>
						<div class="text-slate-900 dark:text-slate-100">
							{#if streamingMessage.error}
								<div
									class="rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/20 dark:text-red-400"
								>
									<p class="text-sm font-medium">Error: {streamingMessage.error}</p>
								</div>
							{:else}
								{!streamingMessage.isComplete ? 'isStreaming ' : 'no'}
								<SummaryRenderer
									content={streamingMessage.content || ''}
									shouldUseMarkdown={true}
									isStreaming={!streamingMessage.isComplete}
									onContentChange={handleStreamingContentChange}
								/>
							{/if}
						</div>
					</div>
				{/each}

				<!-- Agent Status Indicator - positioned at bottom of chat messages -->
				<AgentStatusIndicator {agent} {isStreaming} class="py-2" />
			</div>
		{/if}
	</div>

	<style>
		@keyframes flash-highlight {
			0% {
				background-color: rgb(59 130 246 / 0.1);
			}
			50% {
				background-color: rgb(59 130 246 / 0.2);
			}
			100% {
				background-color: transparent;
			}
		}

		:global(.user-message-flash) {
			animation: flash-highlight 0.6s ease-out;
		}
	</style>

	<!-- Scroll to bottom button -->
	{#if showScrollButton}
		<div class="absolute right-4 bottom-4 z-10" transition:fly={{ y: 20, duration: 200 }}>
			<Button
				variant="secondary"
				size="icon-sm"
				icon={ArrowDown}
				onclick={handleScrollToBottomClick}
				class="shadow-lg"
				aria-label="Scroll to bottom"
			></Button>
		</div>
	{/if}
</div>
