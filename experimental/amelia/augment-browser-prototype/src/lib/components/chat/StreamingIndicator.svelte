<script lang="ts">
	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import { getAgent, getChatSession } from '$lib/stores/global-state.svelte';
	import { onMount, onDestroy } from 'svelte';
	import { fly, fade } from 'svelte/transition';
	import LoadingIndicator from '../ui/feedback/LoadingIndicator.svelte';

	interface Props {
		agentId: string;
		class?: string;
	}

	let { agentId, class: className = '' }: Props = $props();

	// Get agent and chat session data
	let agent = $derived(getAgent(agentId));
	let chatSession = $derived(getChatSession(agentId));

	// Determine streaming status
	let isAgentThinking = $derived(
		$agent?.data?.status === RemoteAgentStatus.AGENT_RUNNING ||
		$agent?.data?.status === RemoteAgentStatus.AGENT_STARTING
	);

	let isStreamingResponse = $derived($chatSession?.data?.isStreaming || false);
	let streamingContent = $derived($chatSession?.data?.streamingContent || '');

	// Animation state for typing dots
	let typingDots = $state('');
	let animationInterval: NodeJS.Timeout | null = null;

	// Start typing animation when agent is thinking
	$effect(() => {
		if (isAgentThinking || isStreamingResponse) {
			startTypingAnimation();
		} else {
			stopTypingAnimation();
		}
	});

	function startTypingAnimation() {
		if (animationInterval) return;

		let dotCount = 0;
		animationInterval = setInterval(() => {
			dotCount = (dotCount + 1) % 4;
			typingDots = '.'.repeat(dotCount);
		}, 500);
	}

	function stopTypingAnimation() {
		if (animationInterval) {
			clearInterval(animationInterval);
			animationInterval = null;
		}
		typingDots = '';
	}

	onDestroy(() => {
		stopTypingAnimation();
	});

	// Determine what to show
	let statusMessage = $derived.by(() => {
		if (isStreamingResponse && streamingContent) {
			return 'Streaming response...';
		} else if (isAgentThinking) {
			return 'Agent is thinking';
		} else if ($agent?.data?.status === RemoteAgentStatus.AGENT_STARTING) {
			return 'Agent is starting up';
		}
		return null;
	});

	let shouldShow = $derived(statusMessage !== null);
</script>

{#if shouldShow}
	<div
		class="streaming-indicator {className}"
		transition:fly={{ y: 10, duration: 200 }}
	>
		<div class="flex items-center gap-1.5 px-3 py-2 rounded-lg">
			<!-- Animated dots -->
			 <LoadingIndicator variant="asterisk" size="sm" color="blue" />

			<!-- Status message -->
			<span class="text-sm text-blue-700 dark:text-blue-300">
				{statusMessage}{typingDots}
			</span>

			<!-- Streaming content preview (if available) -->
			{#if isStreamingResponse && streamingContent}
				<div class="text-xs text-blue-600 dark:text-blue-400 max-w-xs truncate">
					{streamingContent}
				</div>
			{/if}
		</div>
	</div>
{/if}

<style>
	.streaming-indicator {
		@apply transition-all duration-200;
	}

	@keyframes pulse {
		0%, 100% {
			opacity: 1;
		}
		50% {
			opacity: 0.5;
		}
	}

	.animate-pulse {
		animation: pulse 1.5s ease-in-out infinite;
	}
</style>
