<script lang="ts">
	import type * as Monaco from 'monaco-editor/esm/vs/editor/editor.api';
	import { onDestroy } from 'svelte';
	import { MonacoContext } from './context';
	import MonacoComponent from './Monaco.svelte';

	export let text: string;
	export let lang: string | undefined = undefined;
	export let pathName: string | undefined = undefined;
	export let options: Record<string, any> = {};
	export let editorInstance: any | undefined = undefined;
	export let height: number | undefined = undefined;
	export let theme: string = 'vs-dark';

	// Get the Monaco context
	const monacoContext = MonacoContext.getContext();
	const monaco = monacoContext.monaco;

	let model: Monaco.editor.ITextModel | undefined;
	let supportedLanguages: string[] = [];
	let composedLang: string | undefined;

	// Simple language detection based on file extension or provided lang
	function getLanguageFromPath(pathName: string | undefined, lang: string | undefined): string {
		if (lang && supportedLanguages.includes(lang)) {
			return lang;
		}

		if (pathName) {
			const extension = pathName.split('.').pop()?.toLowerCase();
			const languageMap: Record<string, string> = {
				js: 'javascript',
				jsx: 'javascript',
				ts: 'typescript',
				tsx: 'typescript',
				svelte: 'svelte',
				html: 'html',
				css: 'css',
				scss: 'scss',
				json: 'json',
				md: 'markdown',
				py: 'python',
				rs: 'rust',
				go: 'go',
				java: 'java',
				c: 'c',
				cpp: 'cpp',
				sh: 'shell',
				sql: 'sql',
				xml: 'xml',
				yaml: 'yaml',
				yml: 'yaml'
			};

			if (
				extension &&
				languageMap[extension] &&
				supportedLanguages.includes(languageMap[extension])
			) {
				return languageMap[extension];
			}
		}

		return 'plaintext';
	}

	// Update the language when lang or supportedLanguages change
	function updateLanguage() {
		composedLang = getLanguageFromPath(pathName, lang);

		// Update the model language if it exists
		if (model && composedLang && $monaco) {
			$monaco.editor.setModelLanguage(model, composedLang);
		}
	}

	// Update the URI and model when pathName changes
	function updateUri(pathName: string | undefined) {
		if (!$monaco) return;

		// Create a new URI and model for Monaco
		const uri = pathName
			? $monaco.Uri.parse(`file://${pathName}#${crypto.randomUUID()}`)
			: $monaco.Uri.parse(`file://#${crypto.randomUUID()}`);

		// Dispose of the old model if it exists, then create a new one
		model?.dispose();
		model = $monaco.editor.createModel(text, composedLang, uri);
	}

	// Update the model text when text changes
	$: if (model) {
		model.setValue(text);
	}

	$: supportedLanguages = $monaco?.languages.getLanguages().map((lang) => lang.id) || [];
	$: lang !== undefined && $monaco && updateLanguage();
	$: $monaco && updateUri(pathName);

	// Clean up on component destroy
	onDestroy(() => {
		model?.dispose();
	});
</script>

{#if model}
	<div class="monaco-simple">
		<MonacoComponent {options} {model} bind:editorInstance {height} {theme} />
	</div>
{:else}
	<div class="monaco-simple-loading">
		<div class="loading-content">
			<div class="loading-spinner"></div>
			<span class="loading-text">Preparing editor...</span>
		</div>
	</div>
{/if}

<style>
	.monaco-simple {
		width: 100%;
		height: 100%;
	}

	.monaco-simple-loading {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f8f9fa;
		border: 1px solid #e5e7eb;
		border-radius: 4px;
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 0.5rem;
		padding: 1rem;
	}

	.loading-spinner {
		width: 20px;
		height: 20px;
		border: 2px solid #e5e7eb;
		border-top: 2px solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	.loading-text {
		color: #6b7280;
		font-size: 0.875rem;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	/* Dark mode support */
	:global(.dark) .monaco-simple-loading {
		background-color: #1f2937;
		border-color: #374151;
	}

	:global(.dark) .loading-spinner {
		border-color: #374151;
		border-top-color: #60a5fa;
	}

	:global(.dark) .loading-text {
		color: #9ca3af;
	}
</style>
