<script lang="ts">
	import { MonacoContext } from './context';

	/**
	 * Whether to show a loading indicator while Monaco is loading
	 * @default true
	 */
	export let showLoadingIndicator: boolean = true;

	// Create the Monaco context
	const monacoContext = new MonacoContext();

	// Get the stores from the context
	const isLoading = monacoContext.isLoading;
	const error = monacoContext.error;
	const isReady = monacoContext.isReady;
</script>

{#if $isLoading && showLoadingIndicator}
	<div class="monaco-editor-skeleton">
		<!-- Editor header bar -->
		<!-- <div class="editor-header">
			<div class="header-tabs">
				<div class="tab active">
					<div class="tab-icon"></div>
					<div class="tab-text"></div>
				</div>
				<div class="tab">
					<div class="tab-icon"></div>
					<div class="tab-text"></div>
				</div>
			</div>
		</div> -->

		<!-- Editor content area -->
		<div class="editor-content">
			<!-- Line numbers column -->
			<div class="line-numbers">
				{#each Array(12) as _, i}
					<div class="line-number">{i + 1}</div>
				{/each}
			</div>

			<!-- Code content -->
			<div class="code-content">
				{#each Array(12) as _, i}
					{@const lineWidth =
						Math.random() > 0.3 ? (Math.random() > 0.5 ? 'w-4/5' : 'w-3/4') : 'w-1/2'}
					<div class="code-line">
						<div
							class="relative {lineWidth} h-4 overflow-hidden rounded bg-slate-100 dark:bg-slate-700"
						>
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: {i * 0.1}s;"
							></div>
						</div>
					</div>
				{/each}
			</div>
		</div>
	</div>
{:else if $error}
	<div class="monaco-provider-error">
		<span class="error-text">Failed to load Monaco Editor: {$error.message}</span>
	</div>
{:else if $isReady}
	<slot />
{/if}

<style>
	.monaco-editor-skeleton {
		overflow: hidden;
		background: #ffffff;
		min-height: 300px;
	}

	.editor-header {
		background: #f8fafc;
		border-bottom: 1px solid #e5e7eb;
		padding: 0.5rem;
	}

	.header-tabs {
		display: flex;
		gap: 0.25rem;
	}

	.tab {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.375rem 0.75rem;
		border-radius: 0.25rem;
		background: #e5e7eb;
	}

	.tab.active {
		background: #ffffff;
		border: 1px solid #d1d5db;
	}

	.tab-icon {
		width: 12px;
		height: 12px;
		background: #9ca3af;
		border-radius: 2px;
	}

	.tab-text {
		width: 60px;
		height: 12px;
		background: #d1d5db;
		border-radius: 2px;
	}

	.editor-content {
		display: flex;
		min-height: 250px;
	}

	.line-numbers {
		background: #f8fafc;
		border-right: 1px solid #e5e7eb;
		padding: 0.75rem 0.5rem;
		min-width: 3rem;
		text-align: right;
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.75rem;
		line-height: 1.5;
	}

	.line-number {
		color: #9ca3af;
		height: 1.5rem;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.code-content {
		flex: 1;
		padding: 0.75rem;
	}

	.code-line {
		height: 1.5rem;
		display: flex;
		align-items: center;
		margin-bottom: 0.125rem;
	}

	.monaco-provider-error {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 0.5rem;
		padding: 1rem;
		min-height: 100px;
	}

	.error-text {
		color: #dc2626;
		font-size: 0.875rem;
	}

	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}

	/* Dark mode support */
	:global(.dark) .monaco-editor-skeleton {
		background: #1f2937;
		border-color: #374151;
	}

	:global(.dark) .editor-header {
		background: #111827;
		border-bottom-color: #374151;
	}

	:global(.dark) .tab {
		background: #374151;
	}

	:global(.dark) .tab.active {
		background: #1f2937;
		border-color: #4b5563;
	}

	:global(.dark) .tab-icon {
		background: #6b7280;
	}

	:global(.dark) .tab-text {
		background: #4b5563;
	}

	:global(.dark) .line-numbers {
		background: #111827;
		border-right-color: #374151;
	}

	:global(.dark) .line-number {
		color: #6b7280;
	}

	:global(.dark) .error-text {
		color: #f87171;
	}
</style>
