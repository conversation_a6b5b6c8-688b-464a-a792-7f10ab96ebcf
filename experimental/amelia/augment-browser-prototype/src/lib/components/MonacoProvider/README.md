# MonacoProvider

This component provides a context for Monaco editor instances. It lazy-loads Monaco using dynamic imports and provides a Svelte context that can be used by child components.

## Problem

Monaco editor is a large dependency that should be lazy-loaded to improve initial page load performance. Currently, Monaco might be loaded in various ways across the codebase:

1. Direct imports from `monaco-editor` which bundle the entire editor
2. Manual handling of Monaco loading in each component
3. Inconsistent loading patterns leading to potential duplicate loading

## Solution

The `MonacoProvider` component:

1. Provides a consistent way to lazy-load Monaco using dynamic imports
2. Creates a Svelte context that can be accessed by child components
3. Defers rendering of children until Monaco is loaded
4. Provides loading and error states
5. Supports both light and dark themes

## Usage

### Basic Usage

```svelte
<script>
  import MonacoProvider from "$lib/components/MonacoProvider";
</script>

<MonacoProvider.Root>
  <!-- Monaco is guaranteed to be loaded when this content renders -->
  <MonacoProvider.SimpleMonaco
    text="console.log('Hello, world!');"
    lang="javascript"
    theme="vs-dark"
  />
</MonacoProvider.Root>
```

### Using the Built-in Monaco Components

The MonacoProvider package includes its own Monaco and SimpleMonaco components that are designed to work with the provider:

```svelte
<script>
  import MonacoProvider from "$lib/components/MonacoProvider";
</script>

<MonacoProvider.Root>
  <MonacoProvider.SimpleMonaco
    text="console.log('Hello, world!');"
    lang="javascript"
    theme="vs-light"
  />
</MonacoProvider.Root>
```

### Theme Support

The components support both light and dark themes:

```svelte
<script>
  import MonacoProvider from "$lib/components/MonacoProvider";

  let isDark = false;
</script>

<MonacoProvider.Root>
  <MonacoProvider.SimpleMonaco
    text="const greeting = 'Hello, world!';"
    lang="javascript"
    theme={isDark ? 'vs-dark' : 'vs-light'}
  />
</MonacoProvider.Root>
```

## Accessing Monaco in Child Components

Child components can access the Monaco instance using the `MonacoContext`:

```svelte
<script>
  import { MonacoContext } from "$lib/components/MonacoProvider";
  import { onDestroy } from "svelte";
  import { get } from "svelte/store";

  // Get the Monaco context
  const monacoContext = MonacoContext.getContext();

  // Get the Monaco instance store
  const monaco = monacoContext.monaco;

  let editorInstance;
  let editorContainer;

  // Method 1: Using the subscribe method
  const unsubscribe = monaco.subscribe((monacoInstance) => {
    if (monacoInstance && editorContainer) {
      editorInstance = monacoInstance.editor.create(editorContainer, {
        value: "// Your code here",
        language: "javascript",
        theme: "vs-dark"
      });
    }
  });

  // Don't forget to unsubscribe when the component is destroyed
  onDestroy(() => {
    unsubscribe();
    if (editorInstance) {
      editorInstance.dispose();
    }
  });

  // Method 2: Using the get function from svelte/store
  function createEditor() {
    const monacoInstance = get(monaco);
    if (monacoInstance && editorContainer) {
      editorInstance = monacoInstance.editor.create(editorContainer, {
        value: "// Your code here",
        language: "javascript",
        theme: "vs-dark"
      });
    }
  }
</script>

<div bind:this={editorContainer}></div>
```

### Checking if Monaco is Ready

The MonacoContext provides an `isReady` store that you can use to check if Monaco is ready to use:

```svelte
<script>
  import { MonacoContext } from "$lib/components/MonacoProvider";

  const monacoContext = MonacoContext.getContext();
  const isReady = monacoContext.isReady;
</script>

{#if $isReady}
  <!-- Monaco is ready to use -->
  <div class="text-green-600 dark:text-green-400">Monaco is ready!</div>
{:else}
  <!-- Monaco is still loading -->
  <div class="text-gray-600 dark:text-gray-400">Loading Monaco...</div>
{/if}
```

## Components

### Root

The `Root` component is the main entry point for the MonacoProvider. It creates the Monaco context and defers rendering of children until Monaco is loaded.

#### Props

- `showLoadingIndicator` (boolean, default: `true`): Whether to show a loading indicator while Monaco is loading

### Monaco

The `Monaco` component is a wrapper around the Monaco editor that uses the MonacoProvider context.

#### Props

- `options` (object): Options to pass to the Monaco editor
- `model` (object): Monaco text model to use
- `decorations` (array): Decorations to apply to the editor
- `height` (number): Height of the editor in pixels
- `theme` (string, default: 'vs-dark'): Monaco theme to use ('vs-light', 'vs-dark', etc.)
- `editorInstance` (object): Reference to the editor instance

### SimpleMonaco

The `SimpleMonaco` component is a higher-level wrapper around the Monaco editor that handles common tasks like language detection and model creation.

#### Props

- `text` (string): Text content to display in the editor
- `lang` (string): Language to use for syntax highlighting
- `pathName` (string): Path name to use for the editor URI
- `options` (object): Options to pass to the Monaco editor
- `theme` (string, default: 'vs-dark'): Monaco theme to use ('vs-light', 'vs-dark', etc.)
- `editorInstance` (object): Reference to the editor instance
- `height` (number): Height of the editor in pixels

## Creating a Diff Viewer

You can also create a diff viewer using the Monaco context:

```svelte
<script>
  import { MonacoContext } from "$lib/components/MonacoProvider";
  import { onDestroy } from "svelte";

  export let originalText = "";
  export let modifiedText = "";
  export let language = "javascript";

  const monacoContext = MonacoContext.getContext();
  const monaco = monacoContext.monaco;

  let diffEditor;
  let container;

  $: if ($monaco && container && !diffEditor) {
    const originalModel = $monaco.editor.createModel(originalText, language);
    const modifiedModel = $monaco.editor.createModel(modifiedText, language);

    diffEditor = $monaco.editor.createDiffEditor(container, {
      theme: 'vs-dark',
      readOnly: true,
      renderSideBySide: true
    });

    diffEditor.setModel({
      original: originalModel,
      modified: modifiedModel
    });
  }

  onDestroy(() => {
    diffEditor?.dispose();
  });
</script>

<div bind:this={container} class="h-96 w-full border border-gray-300 dark:border-gray-600 rounded"></div>
```
