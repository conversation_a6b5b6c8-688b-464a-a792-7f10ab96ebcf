<script lang="ts">
	import { onDestroy } from 'svelte';
	import { MonacoContext } from './context';
	import type * as Monaco from 'monaco-editor/esm/vs/editor/editor.api';

	// Default options for the Monaco editor
	const DEFAULT_OPTIONS: Monaco.editor.IStandaloneEditorConstructionOptions = {
		tabSize: 4,
		readOnly: true,
		scrollBeyondLastLine: false,
		minimap: {
			enabled: false
		},
		scrollbar: {
			alwaysConsumeMouseWheel: false,
			vertical: 'hidden'
		},
		wordWrap: 'on',
		theme: 'vs-dark',
		automaticLayout: true,
		unicodeHighlight: {
			ambiguousCharacters: false,
			invisibleCharacters: false
		}
	};

	export let options: Record<string, any> = {};
	export let model: Monaco.editor.ITextModel;
	export let decorations: Monaco.editor.IModelDeltaDecoration[] | undefined = undefined;
	export let height: number | undefined = undefined;
	export let theme: string = 'vs-dark';

	// Store all monaco state. Allow external users to bind to the editor
	export let editorInstance: Monaco.editor.IStandaloneCodeEditor | undefined = undefined;
	let editorContainer: HTMLElement;
	let decoratorCollection: Monaco.editor.IEditorDecorationsCollection | undefined = undefined;

	// Get the Monaco context
	const monacoContext = MonacoContext.getContext();
	const monaco = monacoContext.monaco;

	// Function to update the editor height based on content
	function updateEditorHeight() {
		if (!editorInstance) return;

		if (height !== undefined) {
			editorContainer.style.height = `${height}px`;
			editorInstance.layout();
			return;
		}

		const contentHeight = Math.min(1000, editorInstance.getContentHeight());
		editorContainer.style.height = `${contentHeight}px`;
		editorInstance.layout();
	}

	// Function to setup a new model
	function setupNewModel(
		model: Monaco.editor.ITextModel,
		decorations: Monaco.editor.IModelDeltaDecoration[]
	) {
		if (!editorInstance) return;
		editorInstance.setModel(model);

		if (decorations.length > 0) {
			decoratorCollection?.clear();
			decoratorCollection = editorInstance.createDecorationsCollection(decorations);
		}
		model.onDidChangeContent(updateEditorHeight);
		updateEditorHeight();
	}

	// Subscribe to Monaco to initialize the editor
	$: {
		if ($monaco && editorContainer && !editorInstance) {
			const opts = { ...DEFAULT_OPTIONS, ...options, theme };
			editorInstance = $monaco.editor.create(editorContainer, opts);

			setupNewModel(model, decorations || []);

			editorInstance.onDidChangeModel(updateEditorHeight);
		}
	}
	// Whenever model changes, we want to try setting it up again
	$: setupNewModel(model, decorations || []);

	// Update theme when it changes
	$: {
		if (editorInstance) {
			editorInstance.updateOptions({ theme });
			editorInstance.layout(undefined);
		}
	}

	// Clean up on component destroy
	onDestroy(() => {
		editorInstance?.dispose();
	});
</script>

<svelte:window on:focus={() => updateEditorHeight()} />
<div class="monaco-editor-container">
	<div class="monaco-editor-wrapper" bind:this={editorContainer}></div>
</div>

<style>
	.monaco-editor-container {
		position: relative;
		height: 100%;
		width: 100%;
	}

	.monaco-editor-wrapper {
		height: 100%;
		width: 100%;
		/* border: 1px solid #e5e7eb; */
		/* border-radius: 0.375rem; */
		overflow: hidden;
	}

	/* Dark mode support */
	:global(.dark) .monaco-editor-wrapper {
		border-color: #374151;
	}

	/* Focus state */
	.monaco-editor-wrapper:focus-within {
		border-color: #3b82f6;
		box-shadow: 0 0 0 1px #3b82f6;
	}

	:global(.dark) .monaco-editor-wrapper:focus-within {
		border-color: #60a5fa;
		box-shadow: 0 0 0 1px #60a5fa;
	}
</style>
