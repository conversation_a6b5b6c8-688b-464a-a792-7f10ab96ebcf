import Root from "./Root.svelte";
import Monaco from "./Monaco.svelte";
import SimpleMonaco from "./SimpleMonaco.svelte";
import { MonacoContext } from "./context";
import type { MonacoType, MonacoProviderOptions } from "./types";

// Export the components
export { Root, Monaco, SimpleMonaco };

// Export the context
export { MonacoContext };

// Export the types
export type { MonacoType, MonacoProviderOptions };

// Export a default object with all components
/* eslint-disable @typescript-eslint/naming-convention */
export default {
  Root,
  Monaco,
  SimpleMonaco,
};
/* eslint-enable @typescript-eslint/naming-convention */
