import { getContext, setContext } from 'svelte';
import { derived, writable, type Readable } from 'svelte/store';
import type { MonacoType } from './types';

/**
 * Context for managing Monaco editor instance
 * This context provides access to the Monaco editor instance and loading state
 */
export class MonacoContext {
	/* eslint-disable @typescript-eslint/naming-convention */
	public static CONTEXT_KEY = 'augment-monaco-provider';
	/* eslint-enable @typescript-eslint/naming-convention */

	// Monaco instance store
	private _monaco = writable<MonacoType | null>(null);

	// Loading state store
	private _isLoading = writable<boolean>(true);

	// Error state store
	private _error = writable<Error | null>(null);

	/**
	 * Creates a new Monaco context
	 * Automatically sets the context on construction
	 */
	constructor() {
		setContext(MonacoContext.CONTEXT_KEY, this);
		void this._initializeMonaco();
	}

	/**
	 * Initialize Monaco by dynamically importing it
	 */
	private async _initializeMonaco(): Promise<void> {
		try {
			// Set loading state
			this._isLoading.set(true);

			// Configure Monaco environment before importing
			if (typeof window !== 'undefined') {
				(window as any).MonacoEnvironment = {
					getWorker: function (_workerId: string, _label: string) {
						// Return a minimal worker that does nothing to avoid web worker issues
						return new Worker(
							URL.createObjectURL(
								new Blob(
									[
										`
										self.onmessage = function(e) {
											// Handle basic worker messages
											if (e.data && e.data.type) {
												switch (e.data.type) {
													case 'getProxy':
														self.postMessage({ id: e.data.id, result: {} });
														break;
													default:
														self.postMessage({ id: e.data.id, result: null });
												}
											}
										};
										`
									],
									{ type: 'application/javascript' }
								)
							)
						);
					}
				};
			}

			// Dynamically import Monaco
			const monaco = await import('monaco-editor');

			// Add global error handler for Monaco file access issues
			if (typeof window !== 'undefined') {
				const originalConsoleError = console.error;
				console.error = function (...args) {
					// Filter out known Monaco file access errors
					const errorMessage = args.join(' ');
					if (
						errorMessage.includes('toUrl') ||
						errorMessage.includes('_FileAccessImpl') ||
						errorMessage.includes('Cannot read properties of undefined')
					) {
						console.warn('Monaco file access error (suppressed):', ...args);
						return;
					}
					originalConsoleError.apply(console, args);
				};
			}

			// Configure Monaco language services to reduce worker dependencies
			if (typeof window !== 'undefined') {
				// Disable TypeScript diagnostics that require workers
				monaco.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
					noSemanticValidation: true,
					noSyntaxValidation: false,
					noSuggestionDiagnostics: true
				});

				monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
					noSemanticValidation: true,
					noSyntaxValidation: false,
					noSuggestionDiagnostics: true
				});

				// Set compiler options to reduce complexity and avoid file access issues
				monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
					target: monaco.languages.typescript.ScriptTarget.ES2020,
					allowNonTsExtensions: true,
					moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
					module: monaco.languages.typescript.ModuleKind.CommonJS,
					noEmit: true,
					esModuleInterop: true,
					jsx: monaco.languages.typescript.JsxEmit.React,
					reactNamespace: 'React',
					allowJs: true,
					typeRoots: ['node_modules/@types'],
					// Disable features that might cause file access issues
					skipLibCheck: true,
					skipDefaultLibCheck: true,
					noLib: false,
					lib: ['ES2020', 'DOM']
				});

				// Also configure JavaScript defaults
				monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
					target: monaco.languages.typescript.ScriptTarget.ES2020,
					allowNonTsExtensions: true,
					moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
					module: monaco.languages.typescript.ModuleKind.CommonJS,
					noEmit: true,
					esModuleInterop: true,
					allowJs: true,
					skipLibCheck: true,
					skipDefaultLibCheck: true,
					noLib: false,
					lib: ['ES2020', 'DOM']
				});

				// Register Svelte language
				this._registerSvelteLanguage(monaco);
			}

			// Set Monaco instance
			this._monaco.set(monaco);

			// Clear loading state
			this._isLoading.set(false);
		} catch (err) {
			// Set error state
			this._error.set(err instanceof Error ? err : new Error(String(err)));
			this._isLoading.set(false);
			console.error('Failed to load Monaco:', err);
		}
	}

	/**
	 * Register Svelte language support with Monaco
	 */
	private _registerSvelteLanguage(monaco: MonacoType): void {
		// Register the Svelte language
		monaco.languages.register({ id: 'svelte' });

		// Define Svelte language configuration
		monaco.languages.setLanguageConfiguration('svelte', {
			comments: {
				blockComment: ['<!--', '-->'],
				lineComment: '//'
			},
			brackets: [
				['{', '}'],
				['[', ']'],
				['(', ')'],
				['<', '>']
			],
			autoClosingPairs: [
				{ open: '{', close: '}' },
				{ open: '[', close: ']' },
				{ open: '(', close: ')' },
				{ open: '<', close: '>', notIn: ['string'] },
				{ open: '"', close: '"', notIn: ['string'] },
				{ open: "'", close: "'", notIn: ['string', 'comment'] },
				{ open: '`', close: '`', notIn: ['string', 'comment'] }
			],
			surroundingPairs: [
				{ open: '{', close: '}' },
				{ open: '[', close: ']' },
				{ open: '(', close: ')' },
				{ open: '<', close: '>' },
				{ open: '"', close: '"' },
				{ open: "'", close: "'" },
				{ open: '`', close: '`' }
			],
			folding: {
				markers: {
					start: new RegExp('^\\s*<!--\\s*#region\\b.*-->'),
					end: new RegExp('^\\s*<!--\\s*#endregion\\b.*-->')
				}
			}
		});

		// Define Svelte syntax highlighting
		monaco.languages.setMonarchTokensProvider('svelte', {
			defaultToken: '',
			tokenPostfix: '.svelte',

			// Svelte-specific keywords and directives
			keywords: [
				'if',
				'else',
				'each',
				'as',
				'await',
				'then',
				'catch',
				'key',
				'export',
				'let',
				'const',
				'var',
				'function',
				'class',
				'import',
				'from',
				'true',
				'false',
				'null',
				'undefined',
				'this'
			],

			svelteDirectives: [
				'bind',
				'on',
				'use',
				'transition',
				'in',
				'out',
				'animate',
				'class',
				'style'
			],

			operators: [
				'=',
				'>',
				'<',
				'!',
				'~',
				'?',
				':',
				'==',
				'<=',
				'>=',
				'!=',
				'&&',
				'||',
				'++',
				'--',
				'+',
				'-',
				'*',
				'/',
				'&',
				'|',
				'^',
				'%',
				'<<',
				'>>',
				'>>>',
				'+=',
				'-=',
				'*=',
				'/=',
				'&=',
				'|=',
				'^=',
				'%=',
				'<<=',
				'>>=',
				'>>>='
			],

			// Define tokenizer rules
			tokenizer: {
				root: [
					// HTML tags
					[/<\/?[a-zA-Z][\w-]*/, 'tag'],

					// Svelte blocks
					[/\{#(if|each|await|key)\b/, 'keyword.control'],
					[/\{:(else|then|catch)\b/, 'keyword.control'],
					[/\{\/[a-zA-Z]+\}/, 'keyword.control'],

					// Svelte expressions
					[/\{[^}]*\}/, 'expression'],

					// Svelte directives
					[
						/(bind|on|use|transition|in|out|animate|class|style):[a-zA-Z-]+/,
						'attribute.name.svelte'
					],

					// HTML attributes
					[/[a-zA-Z-]+(?=\s*=)/, 'attribute.name'],

					// Strings
					[/"([^"\\]|\\.)*$/, 'string.invalid'],
					[/'([^'\\]|\\.)*$/, 'string.invalid'],
					[/"/, 'string', '@string_double'],
					[/'/, 'string', '@string_single'],
					[/`/, 'string', '@string_backtick'],

					// Comments
					[/<!--/, 'comment', '@comment'],
					[/\/\/.*$/, 'comment'],

					// Numbers
					[/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
					[/0[xX][0-9a-fA-F]+/, 'number.hex'],
					[/\d+/, 'number'],

					// Keywords
					[
						/[a-z_$][\w$]*/,
						{
							cases: {
								'@keywords': 'keyword',
								'@default': 'identifier'
							}
						}
					]
				],

				comment: [
					[/[^<\-]+/, 'comment'],
					[/-->/, 'comment', '@pop'],
					[/<!--/, 'comment.invalid'],
					[/[<\-]/, 'comment']
				],

				string_double: [
					[/[^\\"]+/, 'string'],
					[/\\./, 'string.escape.invalid'],
					[/"/, 'string', '@pop']
				],

				string_single: [
					[/[^\\']+/, 'string'],
					[/\\./, 'string.escape.invalid'],
					[/'/, 'string', '@pop']
				],

				string_backtick: [
					[/[^\\`$]+/, 'string'],
					[/\$\{/, 'delimiter.bracket', '@bracketCounting'],
					[/\\./, 'string.escape'],
					[/`/, 'string', '@pop']
				],

				bracketCounting: [
					[/\{/, 'delimiter.bracket', '@bracketCounting'],
					[/\}/, 'delimiter.bracket', '@pop'],
					{ include: 'root' }
				]
			}
		});
	}

	/**
	 * Get the Monaco instance
	 * @returns A readable store containing the Monaco instance
	 */
	public get monaco(): Readable<MonacoType | null> {
		return this._monaco;
	}

	/**
	 * Get the loading state
	 * @returns A readable store containing the loading state
	 */
	public get isLoading(): Readable<boolean> {
		return this._isLoading;
	}

	/**
	 * Get the error state
	 * @returns A readable store containing the error state
	 */
	public get error(): Readable<Error | null> {
		return this._error;
	}

	/**
	 * Get a derived store that indicates if Monaco is ready to use
	 * @returns A readable store that is true when Monaco is loaded and ready to use
	 */
	public isReady = derived(
		[this._monaco, this._isLoading, this._error],
		([$monaco, $isLoading, $error]) => $monaco !== null && !$isLoading && $error === null
	);

	/**
	 * Get the Monaco context from the current component
	 * @returns The Monaco context
	 * @throws Error if the context is not found
	 */
	public static getContext(): MonacoContext {
		const context = getContext<MonacoContext>(MonacoContext.CONTEXT_KEY);
		if (!context) {
			throw new Error(
				"Monaco context not found. Make sure you're using MonacoProvider.Root as a parent component."
			);
		}
		return context;
	}
}
