<script lang="ts">
	import Combobox from '$lib/components/ui/forms/Combobox.svelte';
	import { GitHubUtils, type GitHubRepo, type GitHubBranch } from '$lib/api/github-api';
	import { addToast } from '$lib/stores/toast';
	import { branchCache } from '$lib/stores/github-branch-cache.svelte';
	import { debounce } from '$lib/utils/timing';
	import { getRecentBranches } from '$lib/utils/recent-repos-storage';

	import { GitBranch } from '$lib/icons/GitBranchIcon.svelte';

	interface Props {
		repository?: GitHubRepo | null;
		value?: GitHubBranch | null;
		placeholder?: string;
		disabled?: boolean;
		class?: string;
		inputClass?: string;
		containerClass?: string;
		maxWidth?: string;
		onValueChange?: (branch: GitHubBranch | null) => void;
		onBranchSelected?: (branch: GitHubBranch | null) => void;
	}

	let {
		repository = null,
		value = null,
		placeholder = 'Select a branch...',
		disabled = false,
		class: className = '',
		inputClass = '',
		containerClass = '',
		maxWidth,
		onValueChange,
		onBranchSelected
	}: Props = $props();

	// Component state
	let searchTerm = $state('');
	let error = $state('');
	let recentBranches = $state<GitHubBranch[]>([]);

	// Derived state from cache
	let branches = $derived(repository ? branchCache.getBranches(repository) : []);
	let isLoading = $derived(repository ? branchCache.isLoading(repository) : false);

	// Track filtered branches from Combobox
	let filteredBranches = $state<GitHubBranch[]>([]);
	let currentSearchTerm = $state('');

	// Debounced function to load more branches
	const debouncedLoadMore = debounce(
		(repo: GitHubRepo, searchTerm: string, filteredBranches: GitHubBranch[]) => {
			if (searchTerm && filteredBranches.length < 10 && !branchCache.isComplete(repo)) {
				branchCache.loadMoreForSearch(repo, searchTerm, filteredBranches).catch((err) => {
					console.error('Error loading more branches for search:', err);
				});
			}
		},
		300
	); // 300ms debounce delay

	// Handle filtered results from Combobox
	function handleFiltered(filteredOptions: any[], searchTerm: string) {
		// Extract the branch data from the combobox options
		filteredBranches = filteredOptions.map((option) => option.data as GitHubBranch);

		// Only trigger loading if search term actually changed
		if (repository && searchTerm !== currentSearchTerm) {
			currentSearchTerm = searchTerm;
			debouncedLoadMore(repository, searchTerm, filteredBranches);
		}
	}

	// Combobox options derived from branches
	let comboboxOptions = $derived.by(() => {
		const options = [];

		// Add recent branches at the top (only if we have no search term)
		if (recentBranches && recentBranches.length > 0 && (!searchTerm || searchTerm.trim() === '')) {
			const recentOptions = recentBranches.map((branch, index) => ({
				value: GitHubUtils.getBranchKey(branch),
				label: GitHubUtils.formatBranchLabel(branch),
				icon: GitBranch,
				data: branch,
				class:
					index === recentBranches.length - 1
						? 'border-b border-slate-200 dark:border-slate-700 last:border-b-0'
						: ''
			}));

			if (recentOptions.length > 0) {
				options.push(...recentOptions);
			}
		}

		// Add regular branches
		options.push(
			...branches
				.filter((branch) => {
					// Don't duplicate recent branches
					return !recentBranches?.find(
						(r) => GitHubUtils.getBranchKey(r) === GitHubUtils.getBranchKey(branch)
					);
				})
				.map((branch) => ({
					value: GitHubUtils.getBranchKey(branch),
					label: GitHubUtils.formatBranchLabel(branch),
					icon: GitBranch,
					data: branch
				}))
		);

		return options;
	});

	// Selected option value for combobox
	let selectedValue = $derived.by(() => {
		if (!value) return undefined;
		return GitHubUtils.getBranchKey(value);
	});

	// Track the current repository key to prevent unnecessary reloads
	let currentRepoKey = $state<string | null>(null);
	// Track if user has interacted with the combobox (opened it or searched)
	let hasInteracted = $state(false);
	// Track if we're currently searching for the main branch
	let isSearchingForMain = $state(false);

	// Watch for repository changes and clear state
	$effect(() => {
		const newRepoKey = repository ? GitHubUtils.getRepoKey(repository) : null;

		if (newRepoKey && newRepoKey !== currentRepoKey) {
			currentRepoKey = newRepoKey;
			error = '';
			hasInteracted = false; // Reset interaction flag for new repository

			// Load recent branches for this repository
			if (repository) {
				recentBranches = getRecentBranches(repository);
			}
		} else if (!newRepoKey && currentRepoKey) {
			// Clear state when no repository is selected
			currentRepoKey = null;
			error = '';
			searchTerm = '';
			hasInteracted = false;
			recentBranches = [];
			// Clear selected branch
			if (value) {
				onValueChange?.(null);
				onBranchSelected?.(null);
			}
		}
	});

	// Load initial branches when repository is set
	$effect(() => {
		if (repository && !branchCache.isFresh(repository)) {
			// Load initial branches immediately when repository is selected
			branchCache.loadBranches(repository).catch((err) => {
				error = err instanceof Error ? err.message : 'Failed to load branches';
				addToast({
					type: 'error',
					message: `Failed to load branches for ${repository.owner}/${repository.name}`,
					duration: 5000
				});
			});
		}
	});

	// Auto-select main/default branch when repository is set
	$effect(() => {
		if (!value && repository) {
			// Set searching state
			isSearchingForMain = true;

			// Use the new findMainBranch method to properly search for main branch across all pages
			branchCache
				.findMainBranch(repository)
				.then((mainBranch) => {
					if (mainBranch && !value) {
						// Check !value again in case user selected something while loading
						onValueChange?.(mainBranch);
						onBranchSelected?.(mainBranch);
					}
				})
				.catch((err) => {
					console.error('Error finding main branch:', err);
					// Fallback to first available branch if any
					const availableBranches = branchCache.getBranches(repository);
					if (availableBranches.length > 0 && !value) {
						onValueChange?.(availableBranches[0]);
						onBranchSelected?.(availableBranches[0]);
					}
				})
				.finally(() => {
					// Clear searching state
					isSearchingForMain = false;
				});
		}
	});

	async function handleSearch(query: string) {
		if (!repository) return;

		// Mark as interacted when user starts searching
		if (!hasInteracted) {
			hasInteracted = true;

			// Load initial branches if not already cached/fresh
			if (!branchCache.isFresh(repository)) {
				try {
					await branchCache.loadBranches(repository);
					// After initial load, preload all remaining branches in the background
					if (!branchCache.isComplete(repository)) {
						branchCache.loadAllBranches(repository);
					}
				} catch (err) {
					error = err instanceof Error ? err.message : 'Failed to load branches';
					addToast({
						type: 'error',
						message: `Failed to load branches for ${repository.owner}/${repository.name}`,
						duration: 5000
					});
					return;
				}
			}
		}

		// Update search term for tracking
		searchTerm = query.trim();
	}

	function handleValueChange(optionValue: string | undefined) {
		if (!optionValue) {
			const newValue = null;
			onValueChange?.(newValue);
			onBranchSelected?.(newValue);
			return;
		}

		const selectedOption = comboboxOptions.find((opt) => opt.value === optionValue);
		if (selectedOption) {
			const selectedBranch = selectedOption.data as GitHubBranch;
			onValueChange?.(selectedBranch);
			onBranchSelected?.(selectedBranch);
		}
	}

	function handleClear() {
		const newValue = null;
		onValueChange?.(newValue);
		onBranchSelected?.(newValue);
	}

	// Custom display value for the combobox
	let displayValue = $derived.by(() => {
		if (!value) return '';
		return GitHubUtils.formatBranchLabel(value);
	});

	// Determine if the combobox should be disabled
	let isDisabled = $derived(disabled || !repository);

	// Update placeholder based on state
	let effectivePlaceholder = $derived.by(() => {
		if (!repository) {
			return 'Select a repository first...';
		}
		if (isSearchingForMain) {
			return 'Checking for main...';
		}
		if (isLoading) {
			return 'Loading branches...';
		}
		return placeholder;
	});
</script>

<div class="space-y-1 {className}">
	<Combobox
		options={comboboxOptions}
		value={selectedValue}
		{displayValue}
		placeholder={effectivePlaceholder}
		disabled={isDisabled}
		{isLoading}
		{inputClass}
		containerClass={`${containerClass} !pr-0`}
		dropdownClass="min-w-48"
		dropdownMaxWidth={maxWidth || '16rem'}
		dropdownItemLabelClass="whitespace-nowrap line-clamp-1 ellipsis overflow-hidden text-ellipsis"
		searchable={true}
		icon={GitBranch}
		searchThreshold={0.8}
		hasChevron={false}
		onchange={(val) => handleValueChange(val)}
		onclear={handleClear}
		onsearch={handleSearch}
		onfiltered={handleFiltered}
		class="w-full"
	/>

	{#if error}
		<p class="text-sm text-red-600 dark:text-red-400">{error}</p>
	{/if}
</div>
