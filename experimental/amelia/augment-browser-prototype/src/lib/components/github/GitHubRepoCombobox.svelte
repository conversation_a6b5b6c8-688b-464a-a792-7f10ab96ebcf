<script lang="ts">
	import { onMount } from 'svelte';
	import Combobox from '$lib/components/ui/forms/Combobox.svelte';
	import { githubAPI, GitHubUtils, type GitHubRepo } from '$lib/api/github-api';
	import { addToast } from '$lib/stores/toast';
	import {
		parseGitHubUrl,
		createRepoFromParsedUrl,
		isGitHubUrl
	} from '$lib/utils/github-url-utils';
	import { getRecentRepos } from '$lib/utils/recent-repos-storage';
	import { debounce } from '$lib/utils/timing';
	import { GitHub } from '$lib/icons/GitHubIcon.svelte';

	interface Props {
		value?: GitHubRepo | null;
		placeholder?: string;
		inputClass?: string;
		containerClass?: string;
		disabled?: boolean;
		class?: string;
		maxWidth?: string;
		onValueChange?: (repo: GitHubRepo | null, parsedBranch?: string) => void;
		onRepoSelected?: (repo: GitHubRepo | null, parsedBranch?: string) => void;
	}

	let {
		value = null,
		placeholder = 'Select a repository...',
		inputClass = '',
		disabled = false,
		class: className = '',
		containerClass = '',
		maxWidth,
		onValueChange,
		onRepoSelected
	}: Props = $props();

	// Component state
	let repos = $state<GitHubRepo[]>([]);
	let filteredRepos = $state<GitHubRepo[]>([]);
	let recentRepos = $state<GitHubRepo[]>([]);
	let isLoading = $state(false);
	let error = $state('');
	let isAuthenticated = $state(false);

	// URL parsing state
	let currentSearchQuery = $state('');
	let showRecentRepos = $state(true); // Track whether to show recent repos
	let parsedUrlInfo = $state<{
		owner: string;
		name: string;
		branch?: string;
		isValid: boolean;
	} | null>(null);

	// Combobox options derived from repos
	let comboboxOptions = $derived.by(() => {
		const options = [];

		// Add currently selected repo if it exists and isn't in the existingRepos repos
		const existingRepos = [...filteredRepos, ...recentRepos];
		if (
			value &&
			!existingRepos.find((repo) => GitHubUtils.getRepoKey(repo) === GitHubUtils.getRepoKey(value))
		) {
			options.push({
				value: GitHubUtils.getRepoKey(value),
				label: GitHubUtils.formatRepoLabel(value),
				description: GitHubUtils.formatRepoDescription(value),
				icon: GitHub,
				data: value
			});
		}

		// Add recent repos at the top (only when no search query)
		if (recentRepos && recentRepos.length > 0 && showRecentRepos) {
			const recentOptions = recentRepos.map((repo, index) => ({
				value: GitHubUtils.getRepoKey(repo),
				label: GitHubUtils.formatRepoLabel(repo),
				description: GitHubUtils.formatRepoDescription(repo),
				icon: GitHub,
				data: repo,
				class:
					index === recentRepos.length - 1 ? 'border-b border-slate-200 dark:border-slate-700' : ''
			}));

			options.push(...recentOptions);
		}

		// Add filtered repos
		if (filteredRepos && Array.isArray(filteredRepos)) {
			options.push(
				...filteredRepos
					// .filter((repo) => {
					// 	// When showing recent repos (no search), don't duplicate recent repos in the main list
					// 	// When searching (not showing recent repos), include all filtered results
					// 	if (showRecentRepos) {
					// 		return !recentRepos?.find(
					// 			(r) => GitHubUtils.getRepoKey(r) === GitHubUtils.getRepoKey(repo)
					// 		);
					// 	}
					// 	// When searching, include all results (recent repos are not shown separately)
					// 	return true;
					// })
					.map((repo) => ({
						value: GitHubUtils.getRepoKey(repo),
						label: GitHubUtils.formatRepoLabel(repo),
						description: GitHubUtils.formatRepoDescription(repo),
						icon: GitHub,
						data: repo
					}))
			);
		}

		return options;
	});

	// Selected option value for combobox
	let selectedValue = $derived.by(() => {
		if (!value) return undefined;
		return GitHubUtils.getRepoKey(value);
	});

	// Load repositories on mount
	onMount(async () => {
		// Load recent repos first (synchronous)
		recentRepos = getRecentRepos();

		// Then load all repos
		await loadRepos();
	});

	async function loadRepos() {
		if (isLoading) return;

		isLoading = true;
		error = '';

		try {
			const fetchedRepos = await githubAPI.listRepos();
			repos = fetchedRepos;
			filteredRepos = fetchedRepos;
			isAuthenticated = true;
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to load repositories';

			// Check if it's an authentication error
			if (errorMessage.includes('Not authenticated') || errorMessage.includes('401')) {
				isAuthenticated = false;
				error = 'Please sign in to access your GitHub repositories';
			} else {
				error = errorMessage;
				addToast({
					type: 'error',
					message: 'Failed to load GitHub repositories',
					duration: 5000
				});
			}
		} finally {
			isLoading = false;
		}
	}

	function handleGitHubAuth() {
		// Redirect to login page since GitHub authentication is handled through the main auth flow
		window.location.href = '/login';
	}

	function handleValueChange(optionValue: string | undefined) {
		if (!optionValue) {
			const newValue = null;
			onValueChange?.(newValue);
			onRepoSelected?.(newValue);
			return;
		}

		const options = comboboxOptions;
		const selectedOption = options.find((opt) => opt.value === optionValue);
		if (selectedOption) {
			const selectedRepo = selectedOption.data as GitHubRepo;

			// Check if this selection came from a parsed URL with a branch
			// We identify URL repos by checking if the description starts with "Use repository from URL"
			const isUrlRepo = selectedRepo.description?.startsWith('Use repository from URL');
			const branchFromParsedUrl =
				isUrlRepo && parsedUrlInfo?.isValid && parsedUrlInfo.branch
					? parsedUrlInfo.branch
					: undefined;

			onValueChange?.(selectedRepo, branchFromParsedUrl);
			onRepoSelected?.(selectedRepo, branchFromParsedUrl);
		}
	}

	// Debounced search function to prevent excessive filtering
	const debouncedSearch = debounce((query: string) => {
		// Update the current search query here so recent repos logic works correctly
		currentSearchQuery = query;

		if (!repos || !Array.isArray(repos)) {
			filteredRepos = [];
			parsedUrlInfo = null;
			showRecentRepos = true; // Show recent repos when no repos available
			return;
		}

		const searchTerm = query.toLowerCase().trim();

		// If no search term, show all repos and recent repos
		if (!searchTerm) {
			filteredRepos = repos;
			parsedUrlInfo = null;
			showRecentRepos = true;
			return;
		}

		// Hide recent repos when searching
		showRecentRepos = false;

		// Check if the query looks like a GitHub URL
		let urlRepo = null;
		if (isGitHubUrl(searchTerm)) {
			const parseResult = parseGitHubUrl(searchTerm);
			if (parseResult.success && parseResult.data) {
				// Set parsed URL info for preview
				parsedUrlInfo = {
					owner: parseResult.data.owner,
					name: parseResult.data.name,
					branch: parseResult.data.branch,
					isValid: true
				};

				// Create a repo object from the URL to potentially add to results
				urlRepo = createRepoFromParsedUrl(parseResult.data);
				// Add a descriptive description to make it clear this is from a URL
				urlRepo.description = `Use repository from URL${parseResult.data.branch ? ` (branch: ${parseResult.data.branch})` : ''}`;
			} else {
				parsedUrlInfo = null;
			}
		} else {
			parsedUrlInfo = null;
		}

		// Filter repos based on search term
		const matchingRepos = repos.filter((repo) => {
			const repoKey = GitHubUtils.getRepoKey(repo).toLowerCase(); // "owner/name" format
			const repoDescription = (repo.description || '').toLowerCase();

			return repoKey.includes(searchTerm) || repoDescription.includes(searchTerm);
		});

		// Sort results to prioritize repo name matches first, then org matches
		filteredRepos = matchingRepos.sort((a, b) => {
			const aKey = GitHubUtils.getRepoKey(a).toLowerCase();
			const bKey = GitHubUtils.getRepoKey(b).toLowerCase();
			const aRepoName = a.name.toLowerCase();
			const bRepoName = b.name.toLowerCase();
			const aOwner = a.owner.toLowerCase();
			const bOwner = b.owner.toLowerCase();

			// Helper function to check if repo name matches (exact or contains)
			const aRepoMatches = aRepoName === searchTerm || aRepoName.includes(searchTerm);
			const bRepoMatches = bRepoName === searchTerm || bRepoName.includes(searchTerm);

			// Helper function to check if owner matches (exact or contains)
			const aOwnerMatches = aOwner === searchTerm || aOwner.includes(searchTerm);
			const bOwnerMatches = bOwner === searchTerm || bOwner.includes(searchTerm);

			// 1. Prioritize repos where repo name matches over repos where only owner matches
			if (aRepoMatches && !bRepoMatches) return -10;
			if (bRepoMatches && !aRepoMatches) return 10;

			// 2. If both have repo name matches, prioritize exact matches
			if (aRepoMatches && bRepoMatches) {
				if (aRepoName === searchTerm && bRepoName !== searchTerm) return -20;
				if (bRepoName === searchTerm && aRepoName !== searchTerm) return 20;

				// If both are exact or both are partial, prioritize starts with
				if (aRepoName.startsWith(searchTerm) && !bRepoName.startsWith(searchTerm)) return -15;
				if (bRepoName.startsWith(searchTerm) && !aRepoName.startsWith(searchTerm)) return 15;

				// If both start with, prioritize alphabetical order
				return aRepoName.localeCompare(bRepoName);
			}

			// 3. If neither has repo name matches, check owner matches
			if (!aRepoMatches && !bRepoMatches) {
				if (aOwnerMatches && !bOwnerMatches) return -1;
				if (bOwnerMatches && !aOwnerMatches) return 1;

				// If both have owner matches, prioritize exact matches
				if (aOwnerMatches && bOwnerMatches) {
					if (aOwner === searchTerm && bOwner !== searchTerm) return -1;
					if (bOwner === searchTerm && aOwner !== searchTerm) return 1;

					if (aOwner.startsWith(searchTerm) && !bOwner.startsWith(searchTerm)) return -1;
					if (bOwner.startsWith(searchTerm) && !aOwner.startsWith(searchTerm)) return 1;
				}
			}

			// 4. Check full repo key matches as fallback
			if (aKey === searchTerm && bKey !== searchTerm) return -1;
			if (bKey === searchTerm && aKey !== searchTerm) return 1;

			if (aKey.startsWith(searchTerm) && !bKey.startsWith(searchTerm)) return -1;
			if (bKey.startsWith(searchTerm) && !aKey.startsWith(searchTerm)) return 1;

			// 5. Default alphabetical sort by repo name, then owner
			const repoNameComparison = aRepoName.localeCompare(bRepoName);
			if (repoNameComparison !== 0) return repoNameComparison;
			return aOwner.localeCompare(bOwner);
		});

		// Add URL repo to results if it exists and isn't already included
		if (urlRepo) {
			const existingRepo = filteredRepos.find(
				(repo) => GitHubUtils.getRepoKey(repo) === GitHubUtils.getRepoKey(urlRepo)
			);

			if (existingRepo) {
				// If the repo already exists in filtered results, move it to the top
				filteredRepos = [existingRepo, ...filteredRepos.filter((repo) => repo !== existingRepo)];
			} else {
				// Add the URL-derived repo as the first option (press Enter to save)
				filteredRepos = [urlRepo, ...filteredRepos];
			}
		}

		// If no matches found and the search term looks like "org/repo" format,
		// create a synthetic repo option
		if (filteredRepos.length === 0 && searchTerm.includes('/') && !searchTerm.includes(' ')) {
			const parts = searchTerm.split('/');
			if (parts.length === 2 && parts[0].trim() && parts[1].trim()) {
				const [owner, name] = parts;
				const syntheticRepo: GitHubRepo = {
					owner: owner.trim(),
					name: name.trim(),
					html_url: `https://github.com/${owner.trim()}/${name.trim()}`,
					description: `Repository: ${owner.trim()}/${name.trim()}`,
					private: false,
					fork: false,
					archived: false,
					disabled: false,
					default_branch: 'main'
				};
				filteredRepos = [syntheticRepo];
			}
		}
	}, 150); // 150ms debounce delay

	function handleSearch(query: string) {
		// Immediately hide recent repos if user is typing, show them if query is empty
		const trimmedQuery = query.trim();
		if (trimmedQuery === '') {
			showRecentRepos = true;
		} else {
			showRecentRepos = false;
		}

		// Debounce the actual filtering logic
		debouncedSearch(query);
	}

	// Custom display value for the combobox - truncate if too long
	let displayValue = $derived(() => {
		if (!value) return '';
		return GitHubUtils.formatRepoLabel(value);
	});

	function handleKeydown(_event: KeyboardEvent) {
		// No special handling needed - URL repo is now a regular combobox option
	}

	let isComboboxOpen = $state(false);
	let comboboxInputElement = $state<HTMLInputElement>();
</script>

<div class="space-y-1 {className}">
	{#if !isAuthenticated && error.includes('sign in')}
		<div
			class="flex items-center gap-2 rounded-lg border border-slate-200 bg-slate-50 p-3 dark:border-slate-700 dark:bg-slate-800"
		>
			<div class="flex-1">
				<p class="text-sm text-slate-600 dark:text-slate-400">
					Sign in to browse your GitHub repositories
				</p>
			</div>
			<button
				type="button"
				onclick={handleGitHubAuth}
				class="rounded-md bg-slate-900 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-slate-800 dark:bg-slate-100 dark:text-slate-900 dark:hover:bg-slate-200"
			>
				Sign In
			</button>
		</div>
	{:else}
		<Combobox
			options={comboboxOptions}
			hasChevron={false}
			value={selectedValue}
			displayValue={displayValue()}
			{placeholder}
			{disabled}
			{isLoading}
			{inputClass}
			{containerClass}
			dropdownMaxWidth={maxWidth}
			searchable={true}
			clearable={false}
			disableFuzzySort={true}
			bind:isOpen={isComboboxOpen}
			bind:inputElement={comboboxInputElement}
			onchange={(val) => handleValueChange(val)}
			onkeydown={handleKeydown}
			onsearch={handleSearch}
			class="w-full [&_input]:overflow-hidden [&_input]:text-ellipsis [&_input]:whitespace-nowrap"
			dropdownClass="min-w-90"
		></Combobox>

		{#if error && !error.includes('sign in')}
			<p class="text-sm text-red-600 dark:text-red-400">{error}</p>
		{/if}
	{/if}
</div>
