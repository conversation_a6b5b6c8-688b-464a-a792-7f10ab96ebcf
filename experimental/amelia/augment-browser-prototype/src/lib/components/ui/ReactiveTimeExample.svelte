<script lang="ts">
	import { onDestroy } from 'svelte';
	import {
		createReactiveRelativeTime,
		reactiveRelativeTime,
		useReactiveRelativeTime
	} from '$lib/utils/reactive-time.svelte.js';

	// Example timestamps
	const now = new Date();
	const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
	const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
	const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

	// Method 1: Manual cleanup (most control)
	const { relativeTime: time1, cleanup } = createReactiveRelativeTime(fiveMinutesAgo);
	onDestroy(cleanup);

	// Method 2: Automatic cleanup (simplest)
	const time2 = reactiveRelativeTime(oneHourAgo);

	// Method 3: Hook-style with manual update (most flexible)
	const { relativeTime: time3, updateNow } = useReactiveRelativeTime(oneDayAgo);
</script>

<div class="space-y-4 p-4">
	<h3 class="text-lg font-semibold">Reactive Relative Time Examples</h3>

	<div class="space-y-2">
		<div class="flex items-center gap-2">
			<span class="font-medium">5 minutes ago:</span>
			<span class="text-slate-600">{time1}</span>
		</div>

		<div class="flex items-center gap-2">
			<span class="font-medium">1 hour ago:</span>
			<span class="text-slate-600">{time2}</span>
		</div>

		<div class="flex items-center gap-2">
			<span class="font-medium">1 day ago:</span>
			<span class="text-slate-600">{time3}</span>
			<button
				onclick={updateNow}
				class="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
			>
				Update Now
			</button>
		</div>
	</div>

	<div class="text-sm text-slate-500">
		<p>These times update automatically with smart intervals:</p>
		<ul class="list-disc list-inside mt-1 space-y-1">
			<li>Every second for times &lt; 1 minute old</li>
			<li>Every minute for times &lt; 1 hour old</li>
			<li>Every hour for times &lt; 1 day old</li>
			<li>Every day for older times</li>
		</ul>
	</div>
</div>
