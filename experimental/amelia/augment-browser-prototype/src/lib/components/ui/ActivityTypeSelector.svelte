<script lang="ts">
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import ButtonGroup from '$lib/components/ui/navigation/ButtonGroup.svelte';
	interface Props {
		selectedTypes: string[];
		provider: 'github' | 'linear';
		entityType: string;
		onchange?: (selectedTypes: string[]) => void;
		class?: string;
	}

	let {
		selectedTypes = $bindable([]),
		provider,
		entityType,
		onchange,
		class: className = ''
	}: Props = $props();

	// Define activity type options based on provider and entity type
	const activityTypeOptions = $derived.by(() => {
		if (provider === 'github' && entityType === 'pull_request') {
			return [
				{ value: 'opened', label: 'Opened' },
				{ value: 'synchronize', label: 'Updated' },
				{ value: 'ready_for_review', label: 'Ready' },
				{ value: 'closed', label: 'Closed' }
			];
		} else if (provider === 'linear' && entityType === 'issue') {
			return [
				{ value: 'created', label: 'Created' },
				{ value: 'updated', label: 'Updated' },
				{ value: 'completed', label: 'Completed' },
				{ value: 'archived', label: 'Archived' }
			];
		}
		return [];
	});

	function toggleActivityType(activityType: string) {
		const newSelectedTypes = selectedTypes.includes(activityType)
			? selectedTypes.filter(type => type !== activityType)
			: [...selectedTypes, activityType];

		selectedTypes = newSelectedTypes;
		onchange?.(newSelectedTypes);
	}

	function isSelected(activityType: string): boolean {
		return selectedTypes.includes(activityType);
	}
</script>

<div class="space-y-1 {className}">
	<ButtonGroup variant="outline" size="sm">
		{#each activityTypeOptions as option}
			<Button
				variant={isSelected(option.value) ? 'primary' : 'outline'}
				size="sm"
				onclick={() => toggleActivityType(option.value)}
				class="transition-all duration-200 text-xs px-2 py-1"
			>
				{option.label}
			</Button>
		{/each}
	</ButtonGroup>

	{#if selectedTypes.length === 0}
		<div class="text-xs text-slate-500 dark:text-slate-400">
			All events
		</div>
	{:else}
		<div class="text-xs text-slate-600 dark:text-slate-400">
			{selectedTypes.join(', ')}
		</div>
	{/if}
</div>
