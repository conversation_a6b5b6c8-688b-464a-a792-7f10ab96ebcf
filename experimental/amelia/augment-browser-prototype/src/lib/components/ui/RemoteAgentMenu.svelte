<script lang="ts">
	import {
		Icon,
		BookmarkSquare,
		DocumentDuplicate,
		PencilSquare,
		Play,
		Pause,
		Trash
	} from 'svelte-hero-icons';
	import { type CleanRemoteAgent, RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';
	import DropdownMenu from './DropdownMenu.svelte';
	import { EllipsisHorizontal } from 'svelte-hero-icons';
	import {
		getWorkspaceStatusIcon,
		getWorkspaceStatusText
	} from '$lib/utils/agent-status-indicators.svelte';
	import Button from './navigation/Button.svelte';

	interface Props {
		agent: CleanRemoteAgent;
		onClose?: () => void;
		onPin?: () => void;
		onCopy?: () => void;
		onRename?: () => void;
		onDelete?: () => void;
		onPause?: () => void;
		onResume?: () => void;
		// Loading states
		isDeleting?: boolean;
		isPausing?: boolean;
		isResuming?: boolean;
		copyFeedback?: string;
	}

	let {
		agent,
		onClose,
		onPin,
		onCopy,
		onRename,
		onDelete,
		onPause,
		onResume,
		isDeleting = false,
		isPausing = false,
		isResuming = false,
		copyFeedback = ''
	}: Props = $props();

	let isMenuOpen = $state(false);

	function handlePin() {
		onPin?.();
		isMenuOpen = false;
		onClose?.();
	}

	function handleCopy() {
		onCopy?.();
	}

	function handleRename() {
		isMenuOpen = false;
		onClose?.();
		onRename?.();
	}

	function handleDelete() {
		onDelete?.();
		// Menu will close when deletion completes
	}

	function handlePause() {
		onPause?.();
		// Menu will close when pause completes
	}

	function handleResume() {
		onResume?.();
		// Menu will close when resume completes
	}
</script>

{#snippet menuContent()}
	<!-- Pin -->
	<!-- <Button
		variant="ghost"
		class="w-full text-left"
		size="sm"
		onclick={handlePin}
		icon={BookmarkSquare}>Pin</Button
	> -->

	<!-- Copy ID -->
	<Button
		variant="ghost"
		class="w-full text-left"
		size="sm"
		onclick={handleCopy}
		icon={DocumentDuplicate}
	>
		{copyFeedback || 'Copy ID'}
	</Button>

	<!-- Rename -->
	<Button
		variant="ghost"
		class="w-full text-left"
		size="sm"
		onclick={handleRename}
		icon={PencilSquare}>Rename</Button
	>

	<!-- Delete -->
	<Button
		variant="ghost"
		class="w-full text-left"
		size="sm"
		onclick={handleDelete}
		disabled={isDeleting}
		icon={Trash}
	>
		{isDeleting ? 'Deleting...' : 'Delete'}
	</Button>

	<!-- Debug Features -->
	<div class="mt-1 border-t border-slate-200 pt-1 dark:border-slate-600">
		<div class="px-4 py-2 text-xs font-medium text-slate-500 dark:text-slate-400">
			INTERNAL Debug Features
		</div>

		{#if agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING}
			<Button
				variant="ghost"
				icon={Pause}
				size="sm"
				class="w-full text-left"
				onclick={handlePause}
				disabled={isPausing}
			>
				{isPausing ? 'Pausing...' : 'Pause'}
			</Button>
		{:else if agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED}
			<Button
				variant="ghost"
				icon={Play}
				size="sm"
				class="w-full text-left"
				onclick={handleResume}
				disabled={isResuming}
			>
				{isResuming ? 'Resuming...' : 'Resume'}
			</Button>
		{/if}

		<!-- some debug info -->
		<div
			class="flex items-center gap-2 px-4 py-2 font-mono text-xs text-slate-500 dark:text-slate-400"
		>
			<Icon src={getWorkspaceStatusIcon(agent.workspaceStatus)} class="h-3 w-3" mini />
			{getWorkspaceStatusText(agent)}
		</div>
	</div>
{/snippet}

<DropdownMenu
	buttonIcon={EllipsisHorizontal}
	buttonSize="icon-sm"
	buttonVariant="ghost"
	buttonAriaLabel="Agent options"
	bind:open={isMenuOpen}
	{onClose}
	children={menuContent}
/>
