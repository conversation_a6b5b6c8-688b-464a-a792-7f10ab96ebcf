<script lang="ts">
	import { Icon, User } from 'svelte-hero-icons';

	interface Props {
		user?: {
			name?: string;
			login?: string;
			avatar_url?: string;
		} | string;
		providerId?: string;
		size?: number;
		class?: string;
		showBorder?: boolean;
	}

	let {
		user,
		providerId = '',
		size = 16,
		class: className = '',
		showBorder = false
	}: Props = $props();

	// Determine if we should show GitHub avatar
	let shouldShowGitHubAvatar = $derived(
		providerId === 'github' &&
		typeof user === 'object' &&
		user?.login
	);

	// Get avatar URL
	let avatarUrl = $derived(() => {
		if (!shouldShowGitHubAvatar || typeof user !== 'object') return null;

		// Use provided avatar_url if available, otherwise construct GitHub URL
		if (user.avatar_url) {
			return user.avatar_url;
		}

		if (user.login) {
			return `https://github.com/${user.login}.png?size=${size * 2}`; // 2x for retina
		}

		return null;
	});

	// Get alt text
	let altText = $derived(() => {
		if (typeof user === 'string') {
			return `${user} avatar`;
		}

		if (typeof user === 'object' && user) {
			return `${user.name || user.login || 'User'} avatar`;
		}

		return 'User avatar';
	});

	// Combined CSS classes
	let avatarClasses = $derived(() => {
		const baseClasses = `w-${Math.ceil(size/4)} h-${Math.ceil(size/4)} rounded-full bg-slate-100 dark:bg-slate-800`;
		const borderClasses = showBorder ? 'border border-slate-200 dark:border-slate-700' : '';
		return `${baseClasses} ${borderClasses} ${className}`.trim();
	});
</script>

{#if shouldShowGitHubAvatar && avatarUrl()}
	<img
		src={avatarUrl()}
		alt={altText()}
		class={avatarClasses()}
		loading="lazy"
	/>
{:else}
	<Icon src={User} class={avatarClasses()} mini />
{/if}
