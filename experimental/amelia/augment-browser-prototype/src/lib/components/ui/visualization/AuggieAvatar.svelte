<script lang="ts">
	import { stringToSeededRandom, stringToHash } from '$lib/utils/hash';
	import {
		hats,
		eyes,
		mouths,
		extras,
		colors,
		getRandomColorsWithSeed
	} from './auggie-avatar-consts.js';

	interface Props {
		colorSeed?: string;
		faceSeed?: string;
		size?: number;
		class?: string;
	}

	let { colorSeed = '', faceSeed = '', size = 40, class: className = '' }: Props = $props();

	// Ensure seeds are always strings to prevent flickering from undefined -> string transitions
	let stableColorSeed = $derived(colorSeed || 'default-color');
	let stableFaceSeed = $derived(faceSeed || 'default-face');

	// Create seeded random generator for face features
	let faceRandom = $derived(stringToSeededRandom(stableFaceSeed));

	// Generate selections based on seeds
	let [selectedColor, selectedColor2] = $derived(getRandomColorsWithSeed(stableColorSeed));
	let selectedHat = $derived(stableFaceSeed === 'blank' ? '' : faceRandom.pick(hats));
	let selectedEyes = $derived(stableFaceSeed === 'blank' ? eyes[0] : faceRandom.pick(eyes));
	let selectedMouth = $derived(stableFaceSeed === 'blank' ? '' : faceRandom.pick(mouths));
	let selectedExtra = $derived(stableFaceSeed === 'blank' ? '' : faceRandom.pick(extras));

	// Generate deterministic unique ID for gradients based on seeds to avoid conflicts and flickering
	let uniqueId = $derived(stringToHash(`${stableColorSeed}-${stableFaceSeed}`).toString(36));
	let gradientId = $derived(`auggie-gradient-${stableColorSeed.slice(0, 8)}-${uniqueId}`);
</script>

<div class="inline-block {className}" style="width: {size}px; height: {size}px;">
	<svg
		width={size}
		height={size}
		viewBox="0 0 553 553"
		fill="currentColor"
		stroke-width="0"
		stroke="currentColor"
		class="overflow-visible text-gray-900"
	>
		<defs>
			<linearGradient
				id={gradientId}
				x1="100%"
				y1="0%"
				x2="100%"
				y2="100%"
				gradientUnits="userSpaceOnUse"
			>
				<stop offset="0%" stop-color={selectedColor} />
				<stop offset="100%" stop-color={selectedColor2} />
			</linearGradient>
		</defs>

		<!-- Background circle -->
		<!-- <circle cx="50%" cy="56%" r="300" fill="white" /> -->
		<g>
			<!-- <circle cx="50%" cy="56%" r="300" opacity="0.6" /> -->
			<path
				d="M463.332 90.83C488.483 90.0203 510.52 98.572 526.839 114.708C543.42 131.104 551.376 152.54 551.912 177.598L551.914 177.663L551.915 177.729L553.114 256.877C562.948 259.821 572.358 265.212 579.896 273.348L580.404 273.903L580.693 274.225L580.97 274.558C589.587 284.936 594.864 297.704 595.963 312.018L596.056 313.408L596.084 313.892L596.089 314.376C596.25 328.536 591.5 342.262 582.642 353.293C575.471 362.836 565.757 369.33 555.07 372.734L556.273 452.094L556.278 452.418L556.273 452.741C556.038 467.804 553.388 481.754 547.646 495.013L547.391 495.604L547.099 496.179C539.896 510.36 528.785 522.724 514.122 530.678L514.124 530.679C500.943 537.889 486.676 540.378 472.392 541.069L469.653 541.418L468.171 541.605L466.967 541.587C466.725 541.61 466.408 541.648 465.704 541.737C465.098 541.814 464.072 541.946 462.996 542.046C461.99 542.139 460.687 542.229 459.216 542.225L112.715 545.961L112.437 545.964L112.158 545.959C111.58 545.95 111.066 545.968 109.957 546.016C108.902 546.061 107.252 546.136 105.287 546.105L101.874 546.053L101.714 545.997C100.514 546.079 98.4786 546.216 96.4707 546.215C83.8151 546.562 65.16 545.498 47.1094 536.95L46.8457 536.825L46.5869 536.693C32.4441 529.51 19.8232 518.282 12.0684 502.426C5.06702 489.369 1.98711 474.003 2.16504 458.748L0.969727 379.942C-10.2842 376.632 -19.9475 369.847 -27.2129 361.314L-27.2969 361.215L-27.3799 361.116C-36.7321 349.853 -41.0901 336.379 -41.2471 322.589L-41.25 322.304L-41.2451 322.02C-41.018 307.51 -36.3887 293.714 -27.167 282.453C-20.5428 274.364 -11.3808 267.998 -0.912109 264.523L-2.94531 184.554L-2.94629 184.511L-2.94824 184.469C-3.48212 159.496 3.68897 136.821 20.4346 119.886C36.8307 103.304 58.2666 95.3462 83.3252 94.8105L83.5352 94.8075L90.8838 94.7226C90.9264 94.7165 90.969 94.7117 91.0107 94.706L91.9336 94.2636L96.6846 94.246L175.998 93.9618L380.522 91.5663H380.581L463.332 90.83Z"
				class="text-white dark:text-black"
				fill="white"
				stroke-width="50"
			></path>
			<path
				d="M463.332 90.83C488.483 90.0203 510.52 98.572 526.839 114.708C543.42 131.104 551.376 152.54 551.912 177.598L551.914 177.663L551.915 177.729L553.114 256.877C562.948 259.821 572.358 265.212 579.896 273.348L580.404 273.903L580.693 274.225L580.97 274.558C589.587 284.936 594.864 297.704 595.963 312.018L596.056 313.408L596.084 313.892L596.089 314.376C596.25 328.536 591.5 342.262 582.642 353.293C575.471 362.836 565.757 369.33 555.07 372.734L556.273 452.094L556.278 452.418L556.273 452.741C556.038 467.804 553.388 481.754 547.646 495.013L547.391 495.604L547.099 496.179C539.896 510.36 528.785 522.724 514.122 530.678L514.124 530.679C500.943 537.889 486.676 540.378 472.392 541.069L469.653 541.418L468.171 541.605L466.967 541.587C466.725 541.61 466.408 541.648 465.704 541.737C465.098 541.814 464.072 541.946 462.996 542.046C461.99 542.139 460.687 542.229 459.216 542.225L112.715 545.961L112.437 545.964L112.158 545.959C111.58 545.95 111.066 545.968 109.957 546.016C108.902 546.061 107.252 546.136 105.287 546.105L101.874 546.053L101.714 545.997C100.514 546.079 98.4786 546.216 96.4707 546.215C83.8151 546.562 65.16 545.498 47.1094 536.95L46.8457 536.825L46.5869 536.693C32.4441 529.51 19.8232 518.282 12.0684 502.426C5.06702 489.369 1.98711 474.003 2.16504 458.748L0.969727 379.942C-10.2842 376.632 -19.9475 369.847 -27.2129 361.314L-27.2969 361.215L-27.3799 361.116C-36.7321 349.853 -41.0901 336.379 -41.2471 322.589L-41.25 322.304L-41.2451 322.02C-41.018 307.51 -36.3887 293.714 -27.167 282.453C-20.5428 274.364 -11.3808 267.998 -0.912109 264.523L-2.94531 184.554L-2.94629 184.511L-2.94824 184.469C-3.48212 159.496 3.68897 136.821 20.4346 119.886C36.8307 103.304 58.2666 95.3462 83.3252 94.8105L83.5352 94.8075L90.8838 94.7226C90.9264 94.7165 90.969 94.7117 91.0107 94.706L91.9336 94.2636L96.6846 94.246L175.998 93.9618L380.522 91.5663H380.581L463.332 90.83Z"
				class="text-white dark:text-black"
				stroke="currentColor"
				stroke-width="50"
				fill="url(#{gradientId})"
			></path>
			<!-- <path d="M83.1762 495.664C74.3014 495.664 66.8249 494.123 60.7467 491.041C54.697 487.959 50.1027 483.279 47.0208 477.087C43.9389 470.894 42.3694 463.332 42.3694 454.429V362.742C42.3694 351.442 40.1436 343.223 35.7204 338.087C31.2688 332.979 23.3928 330.268 12.0924 330.011C8.58248 330.011 5.75739 328.67 3.61717 325.959C1.44842 323.276 0.392578 320.166 0.392578 316.685C0.392578 312.918 1.44842 309.807 3.61717 307.382C5.75739 304.956 8.61101 303.615 12.0924 303.358C23.3928 303.073 31.2688 300.39 35.7204 295.282C40.1721 290.174 42.3694 282.07 42.3694 271.027V179.34C42.3694 165.871 45.8793 155.626 52.8707 148.635C59.8621 141.643 69.9639 138.133 83.1762 138.133H190.958C195.01 138.133 198.348 139.36 201.059 141.757C203.742 144.183 205.112 147.293 205.112 151.032C205.112 154.542 203.97 157.566 201.687 160.135C199.404 162.703 196.379 163.987 192.584 163.987H92.5076C86.8288 163.987 82.5199 165.471 79.5806 168.439C76.6129 171.407 75.129 175.972 75.129 182.193V274.308C75.129 282.384 73.5024 289.718 70.2778 296.31C67.0532 302.93 62.7443 308.095 57.3509 311.89C51.9576 315.657 45.651 317.541 38.3743 317.541V315.914C45.651 315.914 51.9576 317.797 57.3509 321.564C62.7443 325.331 67.0532 330.525 70.2778 337.145C73.5024 343.737 75.129 351.071 75.129 359.146V451.661C75.129 457.882 76.6129 462.448 79.5806 465.415C82.5484 468.412 86.8574 469.867 92.5076 469.867H192.584C196.351 469.867 199.376 471.151 201.687 473.719C203.999 476.288 205.112 479.313 205.112 482.823C205.112 486.333 203.77 489.329 201.059 491.897C198.348 494.465 195.01 495.749 190.958 495.749H83.1762V495.664Z" ></path>
			<path d="M361.611 495.669C357.559 495.669 354.22 494.385 351.509 491.817C348.798 489.249 347.457 486.224 347.457 482.742C347.457 479.261 348.598 476.208 350.881 473.639C353.164 471.071 356.189 469.787 359.984 469.787H460.061C465.74 469.787 470.049 468.332 472.988 465.335C475.956 462.368 477.44 457.802 477.44 451.581V359.066C477.44 350.991 479.066 343.657 482.291 337.065C485.515 330.445 489.824 325.28 495.218 321.484C500.611 317.717 506.918 315.834 514.194 315.834V317.461C506.918 317.461 500.611 315.577 495.218 311.81C489.824 308.044 485.515 302.85 482.291 296.23C479.066 289.638 477.44 282.304 477.44 274.228V182.113C477.44 175.921 475.956 171.355 472.988 168.359C470.02 165.391 465.711 163.907 460.061 163.907H359.984C356.218 163.907 353.193 162.623 350.881 160.055C348.598 157.515 347.457 154.462 347.457 150.952C347.457 147.185 348.798 144.103 351.509 141.677C354.192 139.252 357.559 138.053 361.611 138.053H469.392C482.605 138.053 492.678 141.563 499.698 148.555C506.718 155.546 510.199 165.79 510.199 179.26V270.946C510.199 281.99 512.425 290.094 516.848 295.202C521.3 300.31 529.176 303.021 540.476 303.278C543.986 303.535 546.811 304.876 548.951 307.302C551.12 309.727 552.176 312.838 552.176 316.604C552.176 320.114 551.12 323.196 548.951 325.879C546.811 328.561 543.958 329.931 540.476 329.931C529.176 330.188 521.3 332.899 516.848 338.007C512.396 343.115 510.199 351.333 510.199 362.662V454.349C510.199 463.252 508.658 470.786 505.548 477.007C502.437 483.228 497.872 487.85 491.822 490.961C485.744 494.043 478.267 495.584 469.392 495.584H361.611V495.669Z" ></path> -->
		</g>

		<!-- Base face -->
		<path
			d="M83.1762 495.664C74.3014 495.664 66.8249 494.123 60.7467 491.041C54.697 487.959 50.1027 483.279 47.0208 477.087C43.9389 470.894 42.3694 463.332 42.3694 454.429V362.742C42.3694 351.442 40.1436 343.223 35.7204 338.087C31.2688 332.979 23.3928 330.268 12.0924 330.011C8.58248 330.011 5.75739 328.67 3.61717 325.959C1.44842 323.276 0.392578 320.166 0.392578 316.685C0.392578 312.918 1.44842 309.807 3.61717 307.382C5.75739 304.956 8.61101 303.615 12.0924 303.358C23.3928 303.073 31.2688 300.39 35.7204 295.282C40.1721 290.174 42.3694 282.07 42.3694 271.027V179.34C42.3694 165.871 45.8793 155.626 52.8707 148.635C59.8621 141.643 69.9639 138.133 83.1762 138.133H190.958C195.01 138.133 198.348 139.36 201.059 141.757C203.742 144.183 205.112 147.293 205.112 151.032C205.112 154.542 203.97 157.566 201.687 160.135C199.404 162.703 196.379 163.987 192.584 163.987H92.5076C86.8288 163.987 82.5199 165.471 79.5806 168.439C76.6129 171.407 75.129 175.972 75.129 182.193V274.308C75.129 282.384 73.5024 289.718 70.2778 296.31C67.0532 302.93 62.7443 308.095 57.3509 311.89C51.9576 315.657 45.651 317.541 38.3743 317.541V315.914C45.651 315.914 51.9576 317.797 57.3509 321.564C62.7443 325.331 67.0532 330.525 70.2778 337.145C73.5024 343.737 75.129 351.071 75.129 359.146V451.661C75.129 457.882 76.6129 462.448 79.5806 465.415C82.5484 468.412 86.8574 469.867 92.5076 469.867H192.584C196.351 469.867 199.376 471.151 201.687 473.719C203.999 476.288 205.112 479.313 205.112 482.823C205.112 486.333 203.77 489.329 201.059 491.897C198.348 494.465 195.01 495.749 190.958 495.749H83.1762V495.664Z"
		/>
		<path
			d="M361.611 495.668C357.559 495.668 354.22 494.384 351.509 491.816C348.798 489.248 347.457 486.223 347.457 482.741C347.457 479.26 348.598 476.207 350.881 473.638C353.164 471.07 356.189 469.786 359.984 469.786H460.061C465.74 469.786 470.049 468.331 472.988 465.334C475.956 462.367 477.44 457.801 477.44 451.58V359.065C477.44 350.99 479.066 343.656 482.291 337.064C485.515 330.444 489.824 325.279 495.218 321.483C500.611 317.716 506.918 315.833 514.194 315.833V317.46C506.918 317.46 500.611 315.576 495.218 311.809C489.824 308.043 485.515 302.849 482.291 296.229C479.066 289.637 477.44 282.303 477.44 274.227V182.112C477.44 175.92 475.956 171.354 472.988 168.358C470.02 165.39 465.711 163.906 460.061 163.906H359.984C356.218 163.906 353.193 162.622 350.881 160.054C348.598 157.514 347.457 154.461 347.457 150.951C347.457 147.184 348.798 144.102 351.509 141.676C354.192 139.251 357.559 138.052 361.611 138.052H469.392C482.605 138.052 492.678 141.562 499.698 148.554C506.718 155.545 510.199 165.789 510.199 179.259V270.945C510.199 281.989 512.425 290.093 516.848 295.201C521.3 300.309 529.176 303.02 540.476 303.277C543.986 303.534 546.811 304.875 548.951 307.301C551.12 309.726 552.176 312.837 552.176 316.603C552.176 320.113 551.12 323.195 548.951 325.878C546.811 328.56 543.958 329.93 540.476 329.93C529.176 330.187 521.3 332.898 516.848 338.006C512.396 343.114 510.199 351.332 510.199 362.661V454.348C510.199 463.251 508.658 470.785 505.548 477.006C502.437 483.227 497.872 487.849 491.822 490.96C485.744 494.042 478.267 495.583 469.392 495.583H361.611V495.668Z"
		/>

		<!-- Hat (if any) -->
		{#if selectedHat}
			<g>
				{@html selectedHat}
			</g>
		{/if}

		<!-- Eyes -->
		<g>
			{@html selectedEyes}
		</g>

		<!-- Mouth -->
		<g>
			{@html selectedMouth}
		</g>

		<!-- Extras (glasses, facial hair, etc.) -->
		{#if selectedExtra}
			<g>
				{@html selectedExtra}
			</g>
		{/if}
	</svg>
</div>
