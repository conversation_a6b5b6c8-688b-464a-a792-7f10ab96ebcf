<script lang="ts">
	const {
        width = 14
    } = $props();
</script>

<svg width={width} viewBox="0 0 14 12" fill="currentColor">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5.99785 0.0603833C6.0776 0.0166554 6.18553 0 6.31034 0C6.43558 0 6.54412 0.0174478 6.62429 0.0633491C6.71196 0.113554 6.75962 0.194754 6.75962 0.296064V0.29946L6.69114 2.12954C6.69083 2.16939 6.68105 2.20838 6.6591 2.24335C6.63698 2.27855 6.60576 2.30458 6.57044 2.32332C6.50251 2.35937 6.41216 2.37238 6.31034 2.37238C6.20856 2.37238 6.11821 2.35937 6.05028 2.32332C6.01495 2.30458 5.98374 2.27855 5.96162 2.24335C5.93967 2.20839 5.92989 2.1694 5.92958 2.12956C5.9124 1.67392 5.89846 1.32069 5.88703 1.0704L5.88694 1.06828C5.88118 0.811568 5.87551 0.630009 5.86984 0.52309C5.86417 0.415821 5.86109 0.347134 5.86109 0.321531V0.287438C5.86109 0.240367 5.872 0.194896 5.89677 0.15422C5.9214 0.113741 5.95668 0.0829901 5.99785 0.0603833ZM7.45009 0.0603833C7.52979 0.0166554 7.63772 0 7.76257 0C7.88778 0 7.99635 0.0174478 8.07649 0.0633491C8.1642 0.113554 8.21182 0.194754 8.21182 0.296064V0.29946L8.14337 2.12954C8.14307 2.16939 8.13329 2.20838 8.11134 2.24335C8.08921 2.27855 8.05796 2.30458 8.02263 2.32332C7.95471 2.35937 7.86436 2.37238 7.76257 2.37238C7.66079 2.37238 7.57044 2.35937 7.50252 2.32332C7.46719 2.30458 7.43593 2.27855 7.41381 2.24335C7.39186 2.20839 7.38208 2.16942 7.38178 2.12957C7.36459 1.67392 7.35069 1.32069 7.33922 1.0704L7.33913 1.06828C7.33342 0.811568 7.3277 0.630009 7.32203 0.52309C7.31636 0.415821 7.31333 0.347134 7.31333 0.321531V0.287438C7.31333 0.240367 7.32424 0.194896 7.34896 0.15422C7.37359 0.113741 7.40888 0.0829901 7.45009 0.0603833ZM8.65448 2.91101C8.74076 2.83315 8.84756 2.79639 8.96896 2.79639H11.6823C12.0308 2.79639 12.3106 2.88969 12.5092 3.08775C12.7085 3.28648 12.8003 3.5716 12.8003 3.92631V6.23891C12.8003 6.50847 12.8553 6.68674 12.9455 6.79118C13.0325 6.89128 13.1989 6.95725 13.4741 6.96363L13.4787 6.96372C13.5883 6.97187 13.682 7.01494 13.7529 7.09561C13.8247 7.17633 13.8569 7.27756 13.8569 7.39029C13.8569 7.49876 13.8237 7.59665 13.7558 7.68076C13.6849 7.77006 13.5869 7.81664 13.4731 7.81695C13.1985 7.82345 13.0326 7.88942 12.9453 7.98966C12.8547 8.09406 12.8003 8.27418 12.8003 8.5519V10.8645C12.8003 11.0994 12.7596 11.3039 12.6735 11.4764C12.5867 11.6503 12.4582 11.7812 12.2883 11.8686L12.2878 11.8688C12.119 11.9545 11.9159 11.9952 11.6823 11.9952H9.05948V11.9977H8.96896C8.84557 11.9977 8.73873 11.9576 8.65258 11.876C8.56803 11.7954 8.52214 11.6956 8.52214 11.5813C8.52214 11.4722 8.55863 11.3734 8.63102 11.2915C8.70764 11.2049 8.80977 11.1641 8.92796 11.1641H11.4474C11.5757 11.1641 11.6569 11.1315 11.7084 11.079L11.7089 11.0785C11.7599 11.0274 11.7943 10.9398 11.7943 10.7955V8.46194C11.7943 8.24569 11.838 8.04708 11.9257 7.86726C12.0129 7.68817 12.1311 7.54517 12.2805 7.43978C12.3052 7.42247 12.3309 7.40638 12.357 7.39194C12.3309 7.37745 12.3055 7.36157 12.2808 7.34427C12.131 7.23957 12.0128 7.09592 11.9256 6.91648C11.8379 6.7367 11.7943 6.5381 11.7943 6.32188V3.9986C11.7943 3.85547 11.7601 3.76753 11.7087 3.71541C11.6563 3.66306 11.5742 3.63005 11.4474 3.63005H8.92796C8.81003 3.63005 8.70838 3.58842 8.63145 3.50263C8.55859 3.42128 8.52214 3.32262 8.52214 3.2124C8.52214 3.09293 8.56586 2.99024 8.65448 2.91101ZM8.65448 2.91101L8.65409 2.91137L8.71474 2.97868L8.65448 2.91101ZM1.49011 3.09018C1.68801 2.89191 1.96882 2.79868 2.31712 2.79868H5.03049C5.15249 2.79868 5.25864 2.83621 5.34492 2.91328L5.34535 2.91366C5.4325 2.99252 5.47731 3.09566 5.47731 3.21469C5.47731 3.32524 5.44073 3.42271 5.36843 3.50445C5.29197 3.59087 5.18998 3.63234 5.07149 3.63234H2.55205C2.42424 3.63234 2.34257 3.66548 2.29069 3.71774C2.23971 3.76881 2.2051 3.85656 2.2051 4.00089V6.32418C2.2051 6.54048 2.16147 6.73909 2.07372 6.91895C1.98654 7.098 1.86838 7.241 1.71896 7.34635C1.69424 7.36366 1.66854 7.37975 1.64245 7.39419C1.66854 7.40868 1.69394 7.42455 1.71866 7.44186C1.86846 7.54656 1.98667 7.69021 2.07379 7.86964C2.16155 8.04947 2.2051 8.24794 2.2051 8.46424V10.7978C2.2051 10.942 2.23946 11.0294 2.29069 11.0809C2.34272 11.1336 2.42464 11.1663 2.55205 11.1663H5.07149C5.18941 11.1663 5.29089 11.2079 5.36782 11.2932C5.44172 11.3755 5.47731 11.4741 5.47731 11.5836C5.47731 11.6986 5.43211 11.7974 5.34691 11.8783C5.26046 11.9607 5.15314 12 5.03049 12H2.22661V11.9954C2.03075 11.986 1.85826 11.9455 1.71164 11.8711C1.54127 11.7844 1.41177 11.6516 1.32588 11.4786C1.24074 11.3071 1.19915 11.1015 1.19915 10.8668V8.5542C1.19915 8.27774 1.14408 8.09674 1.05393 7.99165C0.967051 7.89168 0.800887 7.82575 0.526349 7.81925C0.411659 7.81894 0.314633 7.7734 0.243434 7.68275C0.175699 7.59835 0.142578 7.4998 0.142578 7.39259C0.142578 7.27982 0.174719 7.17858 0.246564 7.09782C0.317841 7.01728 0.412326 6.9743 0.520691 6.96614L0.525254 6.9658C0.800757 6.95903 0.967185 6.89345 1.05407 6.7933C1.14494 6.6886 1.19915 6.51099 1.19915 6.24121V3.9286C1.19915 3.57367 1.29189 3.28877 1.49011 3.09018ZM9.55197 6.39393C9.00796 6.39393 8.56686 6.83585 8.56686 7.38088C8.56686 7.92594 9.00796 8.36787 9.55197 8.36787C10.096 8.36787 10.5371 7.92594 10.5371 7.38088C10.5371 6.83585 10.096 6.39393 9.55197 6.39393ZM3.67022 7.38088C3.67022 6.83585 4.11131 6.39393 4.65536 6.39393C5.19937 6.39393 5.64048 6.83585 5.64048 7.38088C5.64048 7.92594 5.19937 8.36787 4.65536 8.36787C4.11131 8.36787 3.67022 7.92594 3.67022 7.38088Z"/>
</svg>
