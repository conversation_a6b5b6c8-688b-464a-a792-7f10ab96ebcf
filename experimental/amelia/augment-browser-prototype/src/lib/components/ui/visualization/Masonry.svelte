<script lang="ts">
	import { onMount } from 'svelte';

	interface Props {
		items: any[];
		idKey?: string;
		minColWidth?: number;
		maxColWidth?: number;
		gap?: number;
		style?: string;
		children: (item: any, index: number) => any;
	}

	let {
		items,
		idKey = 'id',
		minColWidth = 320,
		maxColWidth = 400,
		gap = 16,
		style = '',
		children
	}: Props = $props();

	let containerElement: HTMLDivElement;
	let columnCount = $state(3);
	let width = $state(0);

	// Calculate optimal number of columns based on container width
	function calculateColumns(width: number) {
		if (!containerElement) return;

		// Calculate how many columns can fit with minimum width
		const maxPossibleColumns = Math.floor(width / minColWidth);

		// Calculate how many columns we need with maximum width
		const minNeededColumns = Math.ceil(width / maxColWidth);

		// Use the optimal number between min and max constraints, but at least 1
		const optimalColumns = Math.max(1, Math.min(maxPossibleColumns, Math.max(minNeededColumns, 1)));

		// Don't exceed the number of items we have
		const finalColumnCount = Math.min(optimalColumns, items.length || 1);

		columnCount = finalColumnCount;
	}

	// Sort items into columns for proper ordering
	const sortedColumns = $derived.by(() => {
		const columns: any[][] = Array.from({ length: columnCount }, () => []);

		items.forEach((item, index) => {
			const columnIndex = index % columnCount;
			columns[columnIndex].push(item);
		});

		return columns;
	});

	$effect(() => {
		calculateColumns(width);
	});
</script>

<div
	bind:this={containerElement}
	bind:clientWidth={width}
	class="masonry-container"
	style="gap: {gap}px; {style}"
>
	{#each sortedColumns as column, columnIndex}
		<div class="masonry-column" style="gap: {gap}px;">
			{#each column as item, itemIndex (item[idKey] || itemIndex)}
				{@render children(item, columnIndex * columnCount + itemIndex)}
			{/each}
		</div>
	{/each}
</div>

<style>
	.masonry-container {
		display: flex;
		align-items: flex-start;
		width: 100%;
	}

	.masonry-column {
		display: flex;
		flex-direction: column;
		flex: 1;
		min-width: 0; /* Prevent flex items from overflowing */
	}
</style>
