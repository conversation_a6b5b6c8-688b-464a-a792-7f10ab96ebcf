<script lang="ts">
	import { onMount } from 'svelte';
	import {interpolate } from 'd3';
	import { Tween } from 'svelte/motion';

	interface Props {
		value: number;
		duration?: number;
		format?: (value: number) => string;
		class?: string;
	}

	let {
		value,
		duration = 200,
		format = (v: number) => Math.round(v).toString(),
		class: className = ''
	}: Props = $props();

	let displayValue = new Tween<number>(value, { duration });

	$effect(() => {
		displayValue.set(value);
	});

</script>

<span class={className}>
	{format(displayValue.current)}
</span>
