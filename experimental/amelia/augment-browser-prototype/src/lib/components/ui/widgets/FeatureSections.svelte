<script lang="ts">
	import { Icon, Bolt, Document } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import TriggerAutoToggle from '$lib/components/ui/TriggerAutoToggle.svelte';
	import { GitHub } from '$lib/icons/GitHubIcon.svelte';
	import { Linear } from '$lib/icons/LinearIcon.svelte';

	interface QueueStat {
		count: number;
		providerId: string;
		name: string;
		trigger?: {
			configuration?: {
				agent_config?: {
					user_guidelines?: string;
				};
			};
		};
	}

	interface Props {
		queueStats: QueueStat[];
	}

	let { queueStats }: Props = $props();

	let isFakeDelegationOn = $state(true);
</script>

<div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12 mb-10 max-w-6xl">
	<!-- Column 1: Current Work Queue -->
	<div class="flex flex-col items-center">
		<!-- Queue Visual -->
		<div class="w-full bg-gradient-to-b from-blue-50 to-sky-50 dark:from-blue-600/10 dark:to-slate-800 rounded-lg p-6 h-72 flex flex-col justify-center">
			<div class="space-y-3">
				{#each queueStats as stat}
				<div class="bg-white dark:bg-slate-900 rounded-lg p-3 shadow-sm border border-slate-200 dark:border-slate-700">
					<div class="flex items-center justify-between">
						<div class="flex items-center gap-2">
							<Icon src={stat.providerId === 'github' ? GitHub : stat.providerId === 'linear' ? Linear : Document} class="w-4 h-4 text-slate-600 dark:text-slate-400" />
							<span class="text-sm font-medium text-slate-900 dark:text-white">{stat.name}</span>
						</div>
						<span class="mr-1 text-sm font-mono text-slate-400 dark:text-slate-600">{stat.count}</span>
					</div>
					<div class="ml-6 text-xs text-slate-600 dark:text-slate-400 mt-1 line-clamp-1">{stat.trigger?.configuration?.agent_config?.user_guidelines}</div>
					</div>
				{/each}
			</div>
		</div>
		<div class="w-full flex flex-col pt-5 pb-3 gap-2 text-center">
		<h3 class="text-lg font-semibold text-slate-900 dark:text-white">Your Inbox</h3>
		<p class="font-light text-slate-600 dark:text-slate-400">
		You're split across one million apps. We slurp all of your data into one place. For each type of work, make an Action and we'll find matching items.
		</p>
		<p class="font-light text-slate-600 dark:text-slate-400">
			{#if queueStats.reduce((acc, stat) => acc + stat.count, 0) > 0}
				We've started you off with {queueStats.length} Actions, and you have <strong>{queueStats.reduce((acc, stat) => acc + stat.count, 0)} items</strong> ready to tackle.
			{:else}
				We've started you off with {queueStats.length} Actions, and your queue is looking clean! New items will appear here as your triggers detect them.
			{/if}
		</p>
		</div>
	</div>

	<!-- Column 2: Instant Agent Assignment -->
	<div class="flex flex-col items-center">
		<!-- Instant Agent Visual -->
		<div class="w-full bg-gradient-to-b from-blue-50 to-sky-50 dark:from-blue-600/10 dark:to-slate-800 rounded-lg p-6 h-72 flex flex-col justify-center">
			<div class="space-y-4">
				<!-- Task Item -->
				<div class="bg-white dark:bg-slate-900 rounded-lg p-4 shadow-sm border border-slate-200 dark:border-slate-700">
					<div class="flex items-center justify-between">
						<div>
							<div class="flex items-center gap-2">
						<Icon src={GitHub} class="w-4 h-4 text-slate-600 dark:text-slate-400" />

								<div class="text-sm font-medium text-slate-900 dark:text-white">Fix login bug</div>
							</div>
							<div class="ml-6 text-xs text-slate-600 dark:text-slate-400">GitHub Issue #123</div>
						</div>
						<!-- <div class="flex items-center gap-2">
							<Icon src={Sparkles} class="w-4 h-4 text-blue-500" mini />
							<span class="text-xs text-blue-600 dark:text-blue-400 font-medium">Agent Ready</span>
						</div> -->
					</div>

					<Button class="w-full mt-3" variant="secondary">Assign to Agent</Button>
				</div>
				<!-- Agent Assignment Arrow -->
				<div class="flex items-center justify-center">
					<div class="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-full text-xs font-medium">
						<Icon src={Bolt} class="w-3 h-3" mini />
					</div>
				</div>
				<!-- Agent Working -->
				<div class="bg-white dark:bg-slate-900 rounded-lg p-3 shadow-sm border border-slate-200 dark:border-slate-700">
					<div class="flex items-center gap-2">
						<div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
						<span class="text-xs text-slate-600 dark:text-slate-400">Agent analyzing code...</span>
					</div>
				</div>
			</div>
		</div>
		<div class="w-full flex flex-col pt-5 pb-3 gap-2 text-center">
		<h3 class="text-lg font-semibold text-slate-900 dark:text-white">
			Assign your work to an agent
		</h3>
		<p class="font-light text-slate-600 dark:text-slate-400">
			Assign an agent to handle any task instantly. Get AI-powered assistance for code reviews, bug fixes, and more.
		</p>
		</div>
	</div>

	<!-- Column 3: Auto-mode -->
	<div class="flex flex-col items-center">
		<!-- Auto-delegate Visual -->
		<div class="w-full bg-gradient-to-b from-blue-50 to-sky-50 dark:from-blue-600/10 dark:to-slate-800 rounded-lg p-6 h-72 flex flex-col justify-center">
			<div class="space-y-2">
				<!-- Auto Toggle -->
				<div class="mb-5 flex items-center justify-center">
					<!-- <div class="flex items-center gap-3 px-4 py-2 bg-sky-500 text-white rounded-full">
						<div class="w-3 h-3 bg-white rounded-full animate-pulse"></div>
						<span class="text-sm font-medium">Auto-mode ON</span>
					</div> -->
					<TriggerAutoToggle
						enabled={isFakeDelegationOn}
						onToggle={() => {
							isFakeDelegationOn = !isFakeDelegationOn;
						}}
						size="md"
						variant="compact"
					/>
				</div>
				<!-- New Task Appears -->
				<div class="bg-white dark:bg-slate-900 rounded-lg p-3 shadow-sm border border-slate-200 dark:border-slate-700 transform transition-all duration-500">
						<div>
							<div class="flex items-center gap-2">
						<Icon src={GitHub} class="w-4 h-4 text-slate-600 dark:text-slate-400" />

								<div class="text-sm font-medium text-slate-900 dark:text-white">
									 Refactor data processing pipeline
								</div>
							</div>
							<!-- <div class="ml-6 text-xs text-slate-600 dark:text-slate-400">GitHub Issue #123</div> -->
						</div>
				</div>
				<!-- second task -->
				 <div class="-mt-4 ml-3 -mr-3 bg-white dark:bg-slate-900 rounded-lg p-3 shadow-sm border border-slate-200 dark:border-slate-700">
					<div class="flex items-center gap-2">
						<div class="flex items-center gap-2">
						<Icon src={Linear} class="w-4 h-4 text-slate-600 dark:text-slate-400" />

								<div class="text-sm font-medium text-slate-900 dark:text-white">Fix login bug</div>
							</div>
							<!-- <div class="ml-6 text-xs text-slate-600 dark:text-slate-400">Linear Issue #123</div> -->
						</div>
				</div>
				<!-- Auto Assignment -->
				<div class="py-2 flex items-center justify-center transition-all transform {isFakeDelegationOn ? "" : "opacity-0 translate-y-1"}">
					<div class="text-xs text-blue-600 dark:text-blue-400 font-medium flex items-center gap-1">
						<Icon src={Bolt} class="w-3 h-3" mini />
						<span>Assigned to agent</span>
					</div>
				</div>
				<!-- Agent Working -->
				<div class="bg-white dark:bg-slate-900 rounded-lg p-3 shadow-sm border border-slate-200 dark:border-slate-700 transition-all transform {isFakeDelegationOn ? "" : "opacity-0 translate-y-1"}">
					<div class="flex items-center gap-2">
						<div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
						<span class="text-xs text-slate-600 dark:text-slate-400">Agent reviewing PR...</span>
					</div>
				</div>
				<div class="-mt-4 ml-3 -mr-3 bg-white dark:bg-slate-900 rounded-lg p-3 shadow-sm border border-slate-200 dark:border-slate-700 transition-all transform {isFakeDelegationOn ? "" : "opacity-0 translate-y-1"}">
					<div class="flex items-center gap-2">
						<div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
						<span class="text-xs text-slate-600 dark:text-slate-400">Agent analyzing code...</span>
					</div>
				</div>
			</div>
		</div>
		<div class="w-full flex flex-col pt-5 pb-3 gap-2 text-center">
		<h3 class="text-lg font-semibold text-slate-900 dark:text-white">
			Auto-delegate while you're away
		</h3>
		<p class="font-light text-slate-600 dark:text-slate-400">
			Turn on auto-mode to have new tasks picked up the moment they appear—no clicks required.
		</p>
		</div>
	</div>
</div>
