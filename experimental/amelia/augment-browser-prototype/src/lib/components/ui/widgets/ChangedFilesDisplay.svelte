<script lang="ts">
	import { Icon, DocumentDuplicate } from 'svelte-hero-icons';
	import type { CleanChangedFile } from '$lib/api/unified-client';

	interface Props {
		changedFiles: CleanChangedFile[];
		maxFiles?: number;
	}

	let {
		changedFiles,
		maxFiles = 3
	}: Props = $props();
</script>

{#if changedFiles.length > 0}
	<div class="mt-2">
		<div class="flex items-center gap-1 flex-wrap">
			{#each changedFiles.slice(0, maxFiles) as file}
				<div class="flex items-center gap-1 text-xs text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-700 px-2 py-1 rounded">
					<Icon src={DocumentDuplicate} class="w-3 h-3" micro />
					<span class="truncate max-w-[120px]">{file.oldPath || file.path || 'Unknown file'}</span>
				</div>
			{/each}
			{#if changedFiles.length > maxFiles}
				<span class="text-xs text-slate-400 dark:text-slate-500">
					+{changedFiles.length - maxFiles} more
				</span>
			{/if}
		</div>
	</div>
{/if}
