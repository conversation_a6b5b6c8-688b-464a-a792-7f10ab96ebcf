<script lang="ts">
	import { Icon, MagnifyingGlass } from 'svelte-hero-icons';
	import GroupedExpandableSection from './GroupedExpandableSection.svelte';
	import ExecutionRow from '$lib/components/ui/data-display/ExecutionRow.svelte';
	import ExecutionRowSkeleton from '$lib/components/ui/data-display/ExecutionRowSkeleton.svelte';
	import { groupExecutionsByAgentStatusFiltered } from '$lib/utils/execution-grouping';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import Input from '$lib/components/ui/forms/Input.svelte';
	import { getContext } from 'svelte';

	interface Props {
		triggerData: Array<{
			trigger: NormalizedTrigger;
			entities: any[];
			executions: any[];
			totalMatches: number;
			isLoading: boolean;
			error?: string;
		}>;
		isLoading: boolean;
		onAgentClick: (agent: any) => void;
	}

	let {
		triggerData,
		isLoading,
		onAgentClick
	}: Props = $props();

	// Search state
	let searchQuery = $state('');

	// Get hover context
	const hoverContext = getContext('hover-context') as {
		hoveredEntityId: string | null;
		hoveredExecutionId: string | null;
		setHoveredEntity: (entityId: string | null) => void;
		setHoveredExecution: (id: string | null) => void;
	};

	// Derived state for all executions
	let allExecutions = $derived.by(() => {
		const executions: Array<{
			timestamp: Date;
			data: any;
			trigger: NormalizedTrigger;
		}> = [];

		triggerData.forEach(({ trigger, executions: triggerExecutions }) => {
			triggerExecutions.forEach(execution => {
				executions.push({
					timestamp: execution.startedAt,
					data: execution,
					trigger
				});
			});
		});

		// Sort by timestamp (most recent first)
		return executions.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
	});

	// Filtered executions based on search
	let filteredExecutions = $derived.by(() => {
		if (!searchQuery.trim()) {
			return allExecutions.map(e => e.data);
		}

		const query = searchQuery.toLowerCase();
		return allExecutions
			.filter(execution => {
				const exec = execution.data;
				const trigger = execution.trigger;

				// Search in execution ID
				if (exec.id?.toLowerCase().includes(query)) return true;

				// Search in remote agent ID
				if (exec.remote_agent_id?.toLowerCase().includes(query)) return true;

				// Search in trigger name
				if (trigger.name?.toLowerCase().includes(query)) return true;

				// Search in status
				const status = exec.status?.toString().toLowerCase() || '';
				if (status.includes(query)) return true;

				// Search in error message
				const errorMessage = exec.error_message || '';
				if (errorMessage.toLowerCase().includes(query)) return true;

				return false;
			})
			.map(e => e.data);
	});

	// Group filtered executions
	let groupedExecutions = $derived(groupExecutionsByAgentStatusFiltered(filteredExecutions));
</script>

<div class="w-full h-auto">
	<!-- Executions Section -->
	<GroupedExpandableSection
		title="Agents on the job"
		groups={groupedExecutions}
		totalCount={groupedExecutions[0]?.items.length || 0}
		collapsedHeight={300}
		isLoading={isLoading}
		skeletonComponent={ExecutionRowSkeleton}
		skeletonCount={2}
	>

	{#if !isLoading && allExecutions.length > 3}
		<!-- Search Input inside the section -->
			<Input
			variant="ghost"
			icon={MagnifyingGlass}
				bind:value={searchQuery}
				placeholder="Search matches..."
				iconClass="translate-x-1"
				inputClass="!pl-10"
			/>
		{/if}

	{#snippet groupContent(groupItems, groupIndex)}

			{#each groupItems as execution}
				{@const isHighlighted = hoverContext?.hoveredExecutionId === execution.id}
				<ExecutionRow
					{execution}
					{isHighlighted}
					onExecutionClick={(agent) => onAgentClick(agent)}
					onMouseEnter={(entity) => {
						hoverContext?.setHoveredExecution(execution.id);
						if (entity) {
							hoverContext?.setHoveredEntity(entity.id);
						}
					}}
					onMouseLeave={() => {
						hoverContext?.setHoveredExecution(null);
						hoverContext?.setHoveredEntity(null);
					}}
				/>
			{/each}
		{/snippet}
	</GroupedExpandableSection>
</div>
