<script lang="ts">
	import { I<PERSON>, Eye, Play } from 'svelte-hero-icons';
	import { GitPullRequest } from '$lib/icons/GitPullRequest.svelte';
	import Button from '../navigation/Button.svelte';
	import SetupScriptCommandStatus from '../feedback/SetupScriptCommandStatus.svelte';
	import MonacoEditor from '../content/MonacoEditor.svelte';
	import { addToast } from '$lib/stores/toast';
	import Tooltip from '../overlays/Tooltip.svelte';

	interface Props {
		scriptContent: string;
		scriptResult?: {
			output?: string;
			status?: 'success' | 'error' | 'skipped';
		};
		testResults?: Array<{
			command: string;
			status: 'success' | 'error' | 'skipped' | 'loading';
			output?: string;
		}>;
		isLoading?: boolean;
		onAddToPR?: () => void;
		onViewInChat?: () => void;
		onRunScript?: () => void;
	}

	let {
		scriptContent,
		scriptResult,
		testResults = [],
		isLoading = false,
		onAddToPR,
		onViewInChat,
		onRunScript
	}: Props = $props();

	// Status summary logic
	let statusSummary = $derived.by(() => {
		if (isLoading) return 'Running script...';
		if (scriptResult?.status === 'success') {
			if (testResults.some((result) => result.status === 'error')) {
				return 'Script ran with failed tests';
			}
			if (testResults.some((result) => result.status === 'skipped')) {
				return 'Script ran but skipped tests';
			}
			return 'Script ran successfully';
		}
		if (scriptResult?.status === 'error') return 'Script failed to run';
		return 'Script created';
	});

	function handleAddToPR() {
		return;
		onAddToPR?.();
	}

	function handleViewTest(output: string) {
		// For now, just copy to clipboard or show in a modal
		// In a full implementation, this would open in an editor
		navigator.clipboard.writeText(output);
		addToast({
			type: 'info',
			message: 'Test output copied to clipboard'
		});
	}
</script>

<div
	class="c-setup-script-tool space-y-4 rounded-lg border border-slate-200 bg-white p-4 dark:border-slate-700 dark:bg-slate-800"
>
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div class="space-y-1">
			<h3 class="text-sm font-medium text-slate-900 dark:text-white">Setup script created</h3>
			<div class="flex items-center gap-1.5 text-xs text-slate-600 dark:text-slate-400">
				<SetupScriptCommandStatus
					commandResult={isLoading ? 'loading' : scriptResult?.status || 'none'}
				/>
				<span>{statusSummary}</span>
			</div>
		</div>

		<!-- Actions -->
		<div class="flex items-center gap-2">
			{#if onViewInChat}
				<Button variant="ghost" size="sm" icon={Eye} onclick={onViewInChat}>View in chat</Button>
			{/if}

			{#if onRunScript && !scriptResult}
				<Button variant="ghost" size="sm" icon={Play} onclick={onRunScript}>Run</Button>
			{/if}

			{#if onAddToPR}
				<Tooltip
					text="Not implemented yet. Give us a shout if you want this feature!"
					position="top"
					maxWidth="200px"
					tooltipClass="w-[200px] max-w-none"
					delay={0}
				>
					<Button variant="ghost" size="sm" icon={GitPullRequest} onclick={handleAddToPR}>
						Add to PR
					</Button>
				</Tooltip>
			{/if}
		</div>
	</div>

	<!-- Script Content -->
	<div class="space-y-2">
		<h4 class="text-xs font-medium text-slate-700 dark:text-slate-300">Script</h4>
		<MonacoEditor value={scriptContent} language="bash" readonly={true} height="200px" />
	</div>

	<!-- Script Results -->
	{#if scriptResult?.output}
		<div class="space-y-2">
			<div class="flex items-center gap-1.5">
				<SetupScriptCommandStatus commandResult={scriptResult.status || 'none'} />
				<h4 class="text-xs font-medium text-slate-700 dark:text-slate-300">Output</h4>
			</div>
			<MonacoEditor value={scriptResult.output} language="bash" readonly={true} height="150px" />
		</div>
	{/if}

	<!-- Test Results -->
	{#if testResults.length > 0}
		<div class="space-y-2">
			<h4 class="text-xs font-medium text-slate-700 dark:text-slate-300">Test Results</h4>
			<div class="space-y-2">
				{#each testResults as test}
					<div
						class="flex items-center justify-between rounded border border-slate-200 p-2 dark:border-slate-700"
					>
						<div class="flex items-center gap-1">
							<SetupScriptCommandStatus commandResult={test.status} />
							<code class="text-xs text-slate-600 dark:text-slate-400">{test.command}</code>
						</div>
						{#if test.output}
							<Button variant="ghost" size="sm" onclick={() => handleViewTest(test.output || '')}>
								<Icon src={Eye} class="h-3 w-3" />
								View
							</Button>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	{/if}
</div>
