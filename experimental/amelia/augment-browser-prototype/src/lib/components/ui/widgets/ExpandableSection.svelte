<script lang="ts">
	import { Icon, ChevronDown, Inbox } from 'svelte-hero-icons';

	interface Props {
		title: string;
		subtitle?: string;
		count?: number;
		totalCount?: number;
		expanded?: boolean;
		items: any[];
		emptyMessage?: string;
		isLoading?: boolean;
		skeletonComponent?: any;
		skeletonCount?: number;
		collapsedHeight?: number;
		onToggle?: () => void;
		children?: any;
	}

	let {
		title,
		subtitle,
		count,
		totalCount,
		expanded = $bindable(false),
		items,
		emptyMessage = "",
		isLoading = false,
		skeletonComponent,
		skeletonCount = 3,
		collapsedHeight = 500,
		onToggle,
		children
	}: Props = $props();

	let containerElement: HTMLElement;

	function handleToggle() {
		expanded = !expanded;
		onToggle?.();

		// if closed, scroll to top
		if (!expanded && containerElement) {
			console.log('scrolling to top', containerElement);
			setTimeout(() => {
				containerElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
				console.log('scrolled to top', containerElement.scrollTop);
			}, 300);
		}
	}

</script>

<div class="relative border border-slate-200 dark:border-slate-700 rounded-lg h-auto" bind:this={containerElement}>

		<!-- Header -->
		<button
			class="w-full flex items-center justify-between px-4 pt-4 pb-2 border-b border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors"
			onclick={handleToggle}
		>
			<div class="flex items-center gap-3">
				<h4 class="text-sm font-medium text-slate-900 dark:text-white">
					{title}
				</h4>
				{#if subtitle}
					<span class="text-xs text-slate-500 dark:text-slate-400">
						{subtitle}
					</span>
				{/if}
				<Icon src={ChevronDown} class="w-4 h-4 text-slate-500 dark:text-slate-400 transition-transform {expanded ? 'rotate-180' : ''}" micro />
			</div>
			<div class="flex items-center gap-2">
				{#if count !== undefined}
					<span class="text-sm font-medium text-slate-600 dark:text-slate-400">
						{count}{#if totalCount !== undefined} / {totalCount}{/if}
					</span>
				{:else if isLoading}
				<div class="h-4 bg-slate-200 dark:bg-slate-700 rounded w-8 animate-pulse"></div>
				{/if}
			</div>
		</button>

		<div class="relative">
			<div
				class="transition-all duration-300 {expanded ? 'max-h-[10000px] overflow-y-auto' : 'overflow-hidden'}"
				style="max-height: {expanded ? '10000px' : `${collapsedHeight}px`}"
			>
					{@render children()}
{#if isLoading}
	<!-- Loading State -->

		<!-- Skeleton Content -->
				{#each Array(skeletonCount) as _}
					{#if skeletonComponent}
						{@const SkeletonComponent = skeletonComponent}
						<SkeletonComponent />
					{:else}
						<!-- Default skeleton -->
						<div class="flex items-center gap-3 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg animate-pulse">
							<div class="w-5 h-5 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
							<div class="flex-1 space-y-2">
								<div class="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4"></div>
								<div class="h-3 bg-slate-200 dark:bg-slate-700 rounded w-1/2"></div>
							</div>
							<div class="h-6 bg-slate-200 dark:bg-slate-700 rounded w-16"></div>
						</div>
					{/if}
				{/each}
{:else if items.length === 0 && emptyMessage}

		<!-- Empty State Content -->
		<div class="px-4 pb-6">
			<div class="text-center py-8">
				<div class="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 flex items-center justify-center">
<Icon src={Inbox} class="w-6 h-6 text-slate-400" micro />
				</div>
				<p class="text-sm font-medium text-slate-900 dark:text-white mb-2">
					{emptyMessage}
				</p>
				<p class="text-xs text-slate-500 dark:text-slate-500">
					{#if title.toLowerCase().includes('match')}
						New items will appear here when your Action's criteria are met
					{:else if title.toLowerCase().includes('execution')}
						Executions will show up here once agents start working
					{:else}
						Items will appear here when available
					{/if}
				</p>
			</div>
		</div>
{/if}

		<!-- Scrollable Content Container -->
			</div>


		</div>
		<!-- Expand/Collapse Button -->
		{#if items.length > 0}
			<div class="left-0 -mt-6 right-0 bg-gradient-to-b via-white dark:via-slate-900 to-transparent h-12 flex items-end justify-center pb-2 transform transition-all translate-y-6 pointer-events-none {expanded ? 'sticky bottom-6' : 'absolute bottom-0'}">
				<!-- fake bottom -->
				 <div class="absolute inset-[-1px] bottom-1/2 border border-t-0 border-slate-200 dark:border-slate-700 rounded-b-lg"></div>
				 <div class="absolute inset-[-1px] top-1/2 bg-white dark:bg-slate-900"></div>
				 <div class="absolute inset-[-1px] top-[calc(50%-0.2rem)] border-x border-white dark:border-slate-900"></div>
				<button
					class="bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-full w-8 h-8 flex items-center justify-center shadow-sm hover:shadow-md cursor-pointer z-10 transform transition-all pointer-events-auto {expanded ? 'rotate-180' : ''}"
					onclick={handleToggle}
				>
					<Icon src={ChevronDown} class="w-4 h-4 text-slate-700 dark:text-slate-300" micro />
				</button>
			</div>
		{/if}
	</div>
