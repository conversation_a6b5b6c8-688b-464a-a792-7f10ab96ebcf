<script lang="ts">
	import { Icon, BookOpen, MagnifyingGlass } from 'svelte-hero-icons';
	import type { NotebookNote } from '$lib/types/structured-response';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import Input from './Input.svelte';
	import { slide } from 'svelte/transition';

	interface Props {
		notes: NotebookNote[];
		title?: string;
		showSearch?: boolean;
		maxHeight?: string;
	}

	let {
		notes,
		title = 'Agent Notebook',
		showSearch = true,
		maxHeight = '400px'
	}: Props = $props();

	// Search functionality
	let searchQuery = $state('');
	let isSearchOpen = $state(false);
	let searchInputRef = $state<HTMLInputElement | undefined>();

	// Focus input when search opens
	$effect(() => {
		if (isSearchOpen && searchInputRef) {
			searchInputRef.focus();
			searchInputRef.select();
		}
	});

	// Filter notes based on search
	let filteredNotes = $derived.by(() => {
		if (!searchQuery.trim()) return notes;

		const query = searchQuery.toLowerCase();
		return notes.filter(note =>
			note.content.toLowerCase().includes(query) ||
			note.category.toLowerCase().includes(query)
		);
	});

	// Format category name for display
	function formatCategoryName(category: string): string {
		return category.split('_').map(word =>
			word.charAt(0).toUpperCase() + word.slice(1)
		).join(' ');
	}

	// Group notes by category
	let groupedNotes = $derived.by(() => {
		const groups: Record<string, NotebookNote[]> = {};

		for (const note of filteredNotes) {
			if (!groups[note.category]) {
				groups[note.category] = [];
			}
			groups[note.category].push(note);
		}

		return groups;
	});

	// Format note content for display
	function formatNoteContent(content: string): string {
		// Clean up any extra whitespace and ensure proper formatting
		return content.trim();
	}
</script>

{#if notes.length > 0}
	<div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
		<!-- Header -->
		<div class="flex items-center justify-between px-4 py-2 border-b border-gray-200 dark:border-gray-700 rounded-t-lg">
			<div class="flex items-center gap-2">
				<Icon src={BookOpen} class="w-4 h-4 text-gray-400 dark:text-gray-600" micro />
				<h3 class="text-sm font-medium text-gray-900 dark:text-white">
					{title}
				</h3>
				{#if filteredNotes.length < notes.length}
				<span class="text-xs text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-700 px-2 py-0.5 rounded-full">
					{filteredNotes.length} {filteredNotes.length === 1 ? 'note' : 'notes'}
					{#if searchQuery.trim() && filteredNotes.length !== notes.length}
						of {notes.length}
					{/if}
				</span>
				{/if}
			</div>

			{#if showSearch && notes.length > 3}
				<!-- Search Button/Input -->
				<div class="relative h-7 flex">
					{#if isSearchOpen}
						<!-- Search Input -->
						<div class="!overflow-visible" transition:slide={{axis: "x", duration: 200 }}>
							<Input
							size="sm"
							icon={MagnifyingGlass}
								bind:element={searchInputRef}
								placeholder="Search notes..."
								bind:value={searchQuery}
								onblur={() => {
									if (!searchQuery.trim()) {
										isSearchOpen = false;
									}
								}}
								onkeydown={(e) => {
									if (e.key === 'Escape') {
										searchQuery = '';
										isSearchOpen = false;
									}
								}}
								class="flex-1"
							/>
						</div>
					{:else}
						<!-- Search Button -->
						<button
							transition:slide={{axis: "x", duration: 200 }}
							onclick={() => isSearchOpen = true}
							class="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
							aria-label="Search notes"
						>
							<Icon src={MagnifyingGlass} class="w-4 h-4" micro />
						</button>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Notes Content -->
		<div class="overflow-y-auto p-4" style="max-height: {maxHeight}">
			{#if filteredNotes.length === 0 && searchQuery.trim()}
				<!-- No search results -->
				<div class="flex flex-col items-center justify-center py-8 px-4 text-center">
					<Icon src={MagnifyingGlass} class="w-8 h-8 text-gray-400 mb-2" />
					<h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
						No notes match your search
					</h4>
					<p class="text-xs text-gray-400 dark:text-gray-500 max-w-xs">
						Try adjusting your search terms or clear the search to see all notes.
					</p>
				</div>
			{:else}
				<!-- Notebook-style grouped content -->
				<div class="space-y-6">
					{#each Object.entries(groupedNotes) as [category, categoryNotes]}
						<div class="space-y-2">
							<!-- Category Header -->
							<h4 class="text-sm font-medium text-gray-600 dark:text-gray-300">
								{formatCategoryName(category)}
							</h4>

							<!-- Notes as bullet points -->
							<div class="space-y-1.5 ml-1">
								{#each categoryNotes as note, index ((note.exchangeId || 'note') + index)}
									<div class="flex items-start gap-1.5">
										<!-- Bullet point -->
										<div class="flex-shrink-0 mt-[9.5px]">
											<div class="w-1 h-1 rounded-full bg-gray-400 dark:bg-gray-500"></div>
										</div>

										<!-- Note content -->
										<div class="flex-1 text-sm text-gray-600 dark:text-gray-200 leading-relaxed">
											<Markdown content={formatNoteContent(note.content)} size="sm" hasNoPadding />
										</div>
									</div>
								{/each}
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
{/if}
