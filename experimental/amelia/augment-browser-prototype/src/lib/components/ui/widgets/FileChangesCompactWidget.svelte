<script lang="ts">
	import { Icon, DocumentText, Plus, Minus, ArrowPath, Pencil, DocumentPlus } from 'svelte-hero-icons';
	import type { CleanChangedFile } from '$lib/api/unified-client';

	interface Props {
		changedFiles: CleanChangedFile[];
		showFileNames?: boolean;
		maxFiles?: number;
		onClick?: () => void;
	}

	let {
		changedFiles,
		showFileNames = true,
		maxFiles = 2,
		onClick
	}: Props = $props();

	// Calculate totals - note: ChangedFile from API client doesn't have additions/deletions
	const totalAdditions = $derived(0); // Not available in current API
	const totalDeletions = $derived(0); // Not available in current API

	// Group files by change type
	const filesByType = $derived(() => {
		const groups = {
			added: changedFiles.filter(f => f.changeType === 'added').length,
			modified: changedFiles.filter(f => f.changeType === 'modified').length,
			deleted: changedFiles.filter(f => f.changeType === 'deleted').length,
			renamed: changedFiles.filter(f => f.changeType === 'renamed').length
		};
		return groups;
	});

	// Get change type info
	function getChangeTypeInfo(changeType: string) {
		switch (changeType) {
			case 'added':
				return { icon: Plus, color: 'text-green-600 dark:text-green-400' };
			case 'deleted':
				return { icon: Minus, color: 'text-red-600 dark:text-red-400' };
			case 'modified':
				return { icon: Pencil, color: 'text-blue-600 dark:text-blue-400' };
			case 'renamed':
				return { icon: ArrowPath, color: 'text-purple-600 dark:text-purple-400' };
			default:
				return { icon: DocumentText, color: 'text-slate-600 dark:text-slate-400' };
		}
	}

	// Get file name from path
	function getFileName(path: string): string {
		return path.split('/').pop() || path;
	}

	// Get file path
	function getFilePath(file: CleanChangedFile): string {
		return file.oldPath || file.path || '';
	}
</script>

{#if changedFiles.length > 0}
	<div
		class="inline-flex items-center gap-2 px-3 py-2 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg transition-all duration-200 {onClick ? 'cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 hover:border-slate-300 dark:hover:border-slate-600' : ''}"
		onclick={onClick}
		role={onClick ? 'button' : undefined}
		tabindex={onClick ? 0 : undefined}
	>
		<!-- Icon and count -->
		<div class="flex items-center gap-1.5">
			<Icon src={DocumentPlus} class="w-4 h-4 text-slate-500 dark:text-slate-400" micro />
			<span class="text-sm font-medium text-slate-700 dark:text-slate-300">
				{changedFiles.length}
			</span>
		</div>

		<!-- Change type indicators -->
		<div class="flex items-center gap-1">
			{#if filesByType().modified > 0}
				<div class="flex items-center gap-0.5">
					<Icon src={Pencil} class="w-3 h-3 text-blue-600 dark:text-blue-400" micro />
					<span class="text-xs text-blue-600 dark:text-blue-400">{filesByType().modified}</span>
				</div>
			{/if}
			{#if filesByType().added > 0}
				<div class="flex items-center gap-0.5">
					<Icon src={Plus} class="w-3 h-3 text-green-600 dark:text-green-400" micro />
					<span class="text-xs text-green-600 dark:text-green-400">{filesByType().added}</span>
				</div>
			{/if}
			{#if filesByType().deleted > 0}
				<div class="flex items-center gap-0.5">
					<Icon src={Minus} class="w-3 h-3 text-red-600 dark:text-red-400" micro />
					<span class="text-xs text-red-600 dark:text-red-400">{filesByType().deleted}</span>
				</div>
			{/if}
			{#if filesByType().renamed > 0}
				<div class="flex items-center gap-0.5">
					<Icon src={ArrowPath} class="w-3 h-3 text-purple-600 dark:text-purple-400" micro />
					<span class="text-xs text-purple-600 dark:text-purple-400">{filesByType().renamed}</span>
				</div>
			{/if}
		</div>

		<!-- Addition/deletion stats -->
		{#if totalAdditions > 0 || totalDeletions > 0}
			<div class="flex items-center gap-1 text-xs">
				{#if totalAdditions > 0}
					<span class="text-green-600 dark:text-green-400">+{totalAdditions}</span>
				{/if}
				{#if totalDeletions > 0}
					<span class="text-red-600 dark:text-red-400">-{totalDeletions}</span>
				{/if}
			</div>
		{/if}

		<!-- File names preview -->
		{#if showFileNames && changedFiles.length > 0}
			<div class="flex items-center gap-1 text-xs text-slate-500 dark:text-slate-400">
				<span>•</span>
				<div class="flex items-center gap-1">
					{#each changedFiles.slice(0, maxFiles) as file, i}
						<span class="truncate max-w-[80px]">{getFileName(getFilePath(file))}</span>
						{#if i < Math.min(maxFiles, changedFiles.length) - 1}<span>,</span>{/if}
					{/each}
					{#if changedFiles.length > maxFiles}
						<span>+{changedFiles.length - maxFiles}</span>
					{/if}
				</div>
			</div>
		{/if}
	</div>
{/if}
