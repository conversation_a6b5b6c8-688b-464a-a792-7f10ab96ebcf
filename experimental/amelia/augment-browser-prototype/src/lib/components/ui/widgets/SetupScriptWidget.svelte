<script lang="ts">
	import { Icon, ChevronLeft, ChevronRight, CommandLine, Eye } from 'svelte-hero-icons';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import Button from '../navigation/Button.svelte';
	import SetupScriptToolComponent from './SetupScriptToolComponent.svelte';
	import { getChatExchanges } from '$lib/stores/global-state.svelte';
	import { addToast } from '$lib/stores/toast';
	import {
		extractSetupScriptVersions,
		executeSetupScript,
		type SetupScriptVersion
	} from '$lib/utils/setup-script-utils';
	import { sendAgentMessage } from '$lib/utils/agent-actions';

	interface Props {
		agentId: string;
		class?: string;
	}

	let { agentId, class: className = '' }: Props = $props();

	// Get chat exchanges for this agent
	let chatExchangesStore = $derived(getChatExchanges(agentId));
	let exchanges = $derived($chatExchangesStore || []);

	// Extract setup script versions from exchanges using utility function
	let scriptVersions = $derived.by((): SetupScriptVersion[] => {
		if (!exchanges || exchanges.length === 0) return [];
		return extractSetupScriptVersions(exchanges);
	});

	let currentVersionIndex = $state(0);
	let currentVersion = $derived(scriptVersions[currentVersionIndex] || null);

	function goToPreviousVersion() {
		if (currentVersionIndex < scriptVersions.length - 1) {
			currentVersionIndex++;
		}
	}

	function goToNextVersion() {
		if (currentVersionIndex > 0) {
			currentVersionIndex--;
		}
	}

	async function handleAddToPR() {
		if (!currentVersion || !agentId) return;

		try {
			// Send a message to the agent asking it to add the setup script to a PR
			const message = `Please add this setup script to a pull request:

\`\`\`bash
${currentVersion.content}
\`\`\`

Save it as a setup script file (e.g., .augment/setup.sh or scripts/setup.sh) and include it in your next pull request.`;

			// Use the same utility function as the Create PR button
			await sendAgentMessage(agentId, message, {
				successMessage: 'Setup script PR request sent successfully',
				errorMessage: 'Failed to send setup script PR request'
			});
		} catch (error) {
			console.error('Failed to send setup script PR request:', error);
		}
	}

	async function handleRunScript() {
		if (!currentVersion) return;

		try {
			const result = await executeSetupScript(currentVersion.content);
			if (result.success) {
				addToast({
					type: 'success',
					message: 'Script executed successfully'
				});
			} else {
				addToast({
					type: 'error',
					message: result.error || 'Script execution failed'
				});
			}
		} catch (error) {
			addToast({
				type: 'error',
				message: 'Failed to execute script'
			});
		}
	}

	async function scrollToExchange(requestId: string) {
		try {
			// Check if we're already on the agent's chat page
			const currentPath = page.url.pathname;
			const agentChatPath = `/agents/${agentId}`;

			// Navigate to agent's chat page if not already there
			if (currentPath !== agentChatPath) {
				await goto(agentChatPath);
				// Wait a bit for the page to load
				await new Promise((resolve) => setTimeout(resolve, 500));
			}

			// Try to find and scroll to the specific exchange
			// Look for exchange elements with data attributes or IDs that match the requestId
			const exchangeElement =
				document.querySelector(`[data-exchange-id="${requestId}"]`) ||
				document.querySelector(`[data-request-id="${requestId}"]`) ||
				document.getElementById(`exchange-${requestId}`);

			if (exchangeElement && exchangeElement instanceof HTMLElement) {
				exchangeElement.scrollIntoView({
					behavior: 'smooth',
					block: 'center'
				});

				// Add a subtle highlight effect
				exchangeElement.style.transition = 'box-shadow 0.3s ease';
				exchangeElement.style.boxShadow = '0 0 0 2px rgb(59 130 246 / 0.5)';

				// Remove the highlight after 2 seconds
				setTimeout(() => {
					exchangeElement.style.boxShadow = '';
				}, 2000);
			} else {
				// Fallback: scroll to the chat history container
				const chatHistoryElement = document.querySelector('[data-chat-history-container]');
				if (chatHistoryElement) {
					chatHistoryElement.scrollIntoView({
						behavior: 'smooth',
						block: 'start'
					});
				}

				addToast({
					type: 'info',
					message: 'Navigated to chat history - the specific exchange may not be visible'
				});
			}
		} catch (error) {
			console.error('Failed to scroll to exchange:', error);
			addToast({
				type: 'error',
				message: 'Failed to navigate to chat exchange'
			});
		}
	}
</script>

{#if scriptVersions.length > 0}
	<div class="setup-script-widget space-y-4 {className}">
		<!-- Header with version navigation -->
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-2">
				<Icon src={CommandLine} class="h-5 w-5 text-slate-600 dark:text-slate-400" />
				<h3 class="text-sm font-medium text-slate-900 dark:text-white">Setup Scripts</h3>
				{#if scriptVersions.length > 1}
					<span class="text-xs text-slate-500 dark:text-slate-400">
						{scriptVersions.length - currentVersionIndex} of {scriptVersions.length}
					</span>
				{/if}
			</div>

			<div class="flex items-center gap-1">
				{#if scriptVersions.length > 1}
					<Button
						variant="ghost"
						size="sm"
						onclick={goToPreviousVersion}
						disabled={currentVersionIndex >= scriptVersions.length - 1}
					>
						<Icon src={ChevronLeft} class="h-4 w-4" />
					</Button>
					<Button
						variant="ghost"
						size="sm"
						onclick={goToNextVersion}
						disabled={currentVersionIndex <= 0}
					>
						<Icon src={ChevronRight} class="h-4 w-4" />
					</Button>
				{/if}
			</div>
		</div>

		<!-- Current script version -->
		{#if currentVersion}
			<SetupScriptToolComponent
				scriptContent={currentVersion.content}
				scriptResult={currentVersion.scriptResult}
				testResults={currentVersion.testResults}
				isLoading={currentVersion.status === 'running'}
				onAddToPR={handleAddToPR}
				onViewInChat={currentVersion.requestId
					? () => scrollToExchange(currentVersion.requestId)
					: undefined}
				onRunScript={handleRunScript}
			/>

			<!-- Version info -->
			<div class="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
				<span>Created {new Date(currentVersion.timestamp).toLocaleString()}</span>
			</div>
		{/if}
	</div>
{/if}
