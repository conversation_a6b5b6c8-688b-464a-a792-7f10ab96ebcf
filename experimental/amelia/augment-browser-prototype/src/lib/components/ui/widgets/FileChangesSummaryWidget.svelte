<script lang="ts">
	import { Icon, DocumentText, Plus, Minus, ArrowPath, Pencil, DocumentPlus, ChevronRight } from 'svelte-hero-icons';
	import type { CleanChangedFile } from '$lib/api/unified-client';

	interface Props {
		changedFiles: CleanChangedFile[];
		maxPreviewFiles?: number;
		showStats?: boolean;
		compact?: boolean;
		onClick?: () => void;
	}

	let {
		changedFiles,
		maxPreviewFiles = 3,
		showStats = true,
		compact = false,
		onClick
	}: Props = $props();

	// Calculate totals - note: ChangedFile from API client doesn't have additions/deletions
	const totalAdditions = $derived(0); // Not available in current API
	const totalDeletions = $derived(0); // Not available in current API

	// Group files by change type
	const filesByType = $derived(() => {
		const groups = {
			added: changedFiles.filter(f => f.changeType === 'added'),
			modified: changedFiles.filter(f => f.changeType === 'modified'),
			deleted: changedFiles.filter(f => f.changeType === 'deleted'),
			renamed: changedFiles.filter(f => f.changeType === 'renamed')
		};
		return groups;
	});

	// Get change type info
	function getChangeTypeInfo(changeType: string) {
		switch (changeType) {
			case 'added':
				return { icon: Plus, color: 'text-green-600 dark:text-green-400', bgColor: 'bg-green-50 dark:bg-green-900/20', label: 'Added' };
			case 'deleted':
				return { icon: Minus, color: 'text-red-600 dark:text-red-400', bgColor: 'bg-red-50 dark:bg-red-900/20', label: 'Deleted' };
			case 'modified':
				return { icon: Pencil, color: 'text-blue-600 dark:text-blue-400', bgColor: 'bg-blue-50 dark:bg-blue-900/20', label: 'Modified' };
			case 'renamed':
				return { icon: ArrowPath, color: 'text-purple-600 dark:text-purple-400', bgColor: 'bg-purple-50 dark:bg-purple-900/20', label: 'Renamed' };
			default:
				return { icon: DocumentText, color: 'text-slate-600 dark:text-slate-400', bgColor: 'bg-slate-50 dark:bg-slate-900/20', label: 'Changed' };
		}
	}

	// Get file name from path
	function getFileName(path: string): string {
		return path.split('/').pop() || path;
	}

	// Get file path
	function getFilePath(file: CleanChangedFile): string {
		return file.oldPath || file.path || '';
	}

	// Preview files (first few of each type)
	const previewFiles = $derived(() => {
		const files: { file: CleanChangedFile; type: string }[] = [];

		// Add files from each type, prioritizing modified > added > deleted > renamed
		const typeOrder: string[] = ['modified', 'added', 'deleted', 'renamed'];

		for (const type of typeOrder) {
			const filesOfType = filesByType()[type as keyof typeof filesByType] || [];
			for (const file of filesOfType.slice(0, Math.max(1, Math.floor(maxPreviewFiles / typeOrder.length)))) {
				if (files.length < maxPreviewFiles) {
					files.push({ file, type });
				}
			}
		}

		return files;
	});
</script>

{#if changedFiles.length > 0}
	<div
		class="group relative bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden transition-all duration-200 {onClick ? 'cursor-pointer hover:shadow-md hover:border-slate-300 dark:hover:border-slate-600' : ''} {compact ? 'p-3' : 'p-4'}"
		class:hover:scale-[1.02]={onClick}
		onclick={onClick}
		role={onClick ? 'button' : undefined}
		tabindex={onClick ? 0 : undefined}
	>
		<!-- Header -->
		<div class="flex items-center justify-between gap-3 mb-3">
			<div class="flex items-center gap-2">
				<div class="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
					<Icon src={DocumentPlus} class="w-4 h-4 text-blue-600 dark:text-blue-400" micro />
				</div>
				<div>
					<div class="text-sm font-semibold text-slate-900 dark:text-slate-100">
						{changedFiles.length} file{changedFiles.length !== 1 ? 's' : ''} changed
					</div>
					{#if showStats && (totalAdditions > 0 || totalDeletions > 0)}
						<div class="flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
							{#if totalAdditions > 0}
								<span class="flex items-center gap-1">
									<div class="w-2 h-2 bg-green-500 rounded-full"></div>
									+{totalAdditions}
								</span>
							{/if}
							{#if totalDeletions > 0}
								<span class="flex items-center gap-1">
									<div class="w-2 h-2 bg-red-500 rounded-full"></div>
									-{totalDeletions}
								</span>
							{/if}
						</div>
					{/if}
				</div>
			</div>

			{#if onClick}
				<Icon src={ChevronRight} class="w-4 h-4 text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300 transition-colors" micro />
			{/if}
		</div>

		<!-- File Type Summary -->
		{#if !compact}
			<div class="flex items-center gap-2 mb-3 flex-wrap">
				{#each Object.entries(filesByType()) as [type, files]}
					{#if files.length > 0}
						{@const info = getChangeTypeInfo(type)}
						<div class="flex items-center gap-1.5 px-2 py-1 {info.bgColor} rounded-md">
							<Icon src={info.icon} class="w-3 h-3 {info.color}" micro />
							<span class="text-xs font-medium {info.color}">{files.length} {info.label.toLowerCase()}</span>
						</div>
					{/if}
				{/each}
			</div>
		{/if}

		<!-- Preview Files -->
		<div class="space-y-1.5">
			{#each previewFiles() as { file, type }}
				{@const info = getChangeTypeInfo(type)}
				{@const filePath = getFilePath(file)}
				{@const fileName = getFileName(filePath)}
				<div class="flex items-center gap-2 text-xs">
					<Icon src={info.icon} class="w-3 h-3 {info.color} flex-shrink-0" micro />
					<span class="font-medium text-slate-700 dark:text-slate-300 truncate flex-1">{fileName}</span>
					{#if showStats && ((file.additions || 0) > 0 || (file.deletions || 0) > 0)}
						<div class="flex items-center gap-1 flex-shrink-0">
							{#if (file.additions || 0) > 0}
								<span class="text-green-600 dark:text-green-400">+{file.additions}</span>
							{/if}
							{#if (file.deletions || 0) > 0}
								<span class="text-red-600 dark:text-red-400">-{file.deletions}</span>
							{/if}
						</div>
					{/if}
				</div>
			{/each}

			{#if changedFiles.length > maxPreviewFiles}
				<div class="text-xs text-slate-500 dark:text-slate-400 italic">
					+{changedFiles.length - maxPreviewFiles} more file{changedFiles.length - maxPreviewFiles !== 1 ? 's' : ''}
				</div>
			{/if}
		</div>

		<!-- Subtle gradient overlay for depth -->
		<div class="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-slate-100/20 dark:to-slate-800/20 pointer-events-none rounded-xl"></div>
	</div>
{/if}
