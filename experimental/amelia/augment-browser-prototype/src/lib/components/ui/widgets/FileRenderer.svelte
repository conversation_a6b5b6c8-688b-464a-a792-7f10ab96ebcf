<script lang="ts">
	import { ArrowsPointingOut } from 'svelte-hero-icons';
	import { type CleanChangedFile } from '$lib/api/unified-client';
	import MonacoProvider from '$lib/components/MonacoProvider';
	import DiffViewer from '$lib/components/agents/DiffViewer.svelte';
	import ContentViewer from '$lib/components/agents/ContentViewer.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	interface Props {
		file: CleanChangedFile;
		height?: string | number;
		enableFocusToScroll?: boolean;
		showDrawerButton?: boolean;
		onOpenDrawer?: (file: CleanChangedFile) => void;
		class?: string;
	}

	let {
		file,
		height = 300,
		enableFocusToScroll = true,
		showDrawerButton = false,
		onOpenDrawer,
		class: className = ''
	}: Props = $props();

	// Helper functions
	function getFilePath(file: CleanChangedFile): string {
		return file.path || file.oldPath || 'Unknown file';
	}

	function getFileName(filePath: string): string {
		return filePath.split('/').pop() || filePath;
	}

	// Determine if we should show diff or content
	function shouldShowDiff(file: CleanChangedFile): boolean {
		// For modified files, always show diff
		if (file.changeType === 'modified') {
			return true;
		}

		// For renamed files, show diff if content changed
		if (file.changeType === 'renamed' && file.oldContent !== file.content) {
			return true;
		}

		// For all other cases (added, deleted, added_then_deleted), show content
		return false;
	}

	// Get content to display for content-only view
	function getContentToDisplay(file: CleanChangedFile): string {
		switch (file.changeType) {
			case 'added':
				return file.content || '';
			case 'deleted':
				return file.oldContent || '';
			case 'added_then_deleted':
				return file.content || file.oldContent || '';
			case 'renamed':
				// If content didn't change, show current content
				return file.content || file.oldContent || '';
			default:
				return file.content || file.oldContent || '';
		}
	}

	// Handle drawer button click
	function handleOpenDrawer() {
		if (onOpenDrawer) {
			onOpenDrawer(file);
		}
	}

	// Derived values
	let fileName = $derived(getFileName(getFilePath(file)));
	let filePath = $derived(getFilePath(file));
	let showDiff = $derived(shouldShowDiff(file));
	let contentToDisplay = $derived(getContentToDisplay(file));
	let heightString = $derived(typeof height === 'number' ? `${height}px` : height);
</script>

<div
	class="w-full overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 {className}"
>
	<!-- Header -->
	<div
		class="border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 dark:border-gray-700 dark:from-gray-800 dark:to-gray-900"
	>
		<div class="flex items-center justify-between">
			<div class="flex min-w-0 flex-1 items-center gap-3">
				<div class="h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
				<span class="truncate text-sm font-medium text-gray-800 dark:text-gray-200">
					{fileName}
				</span>
				{#if filePath !== fileName}
					<span class="truncate text-xs text-gray-500 dark:text-gray-400">
						{filePath}
					</span>
				{/if}
			</div>

			{#if showDrawerButton}
				<Button
					variant="ghost"
					size="icon-sm"
					icon={ArrowsPointingOut}
					onclick={handleOpenDrawer}
					class="flex-shrink-0 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
					aria-label="Open in drawer"
				/>
			{/if}
		</div>
	</div>

	<!-- Content -->
	<div class="w-full" style="height: {heightString};">
		{#if showDiff}
			<!-- Show diff view for modified files or renamed files with content changes -->
			<MonacoProvider.Root>
				<DiffViewer
					fileName={filePath}
					oldContent={file.oldContent || ''}
					newContent={file.content || ''}
					height={heightString}
					{enableFocusToScroll}
					doShowHeader={false}
					class="rounded-none border-none"
				/>
			</MonacoProvider.Root>
		{:else}
			<!-- Show content view for added, deleted, or unchanged renamed files -->
			<ContentViewer
				content={contentToDisplay}
				fileName={filePath}
				{height}
				{enableFocusToScroll}
				class="rounded-none border-none"
			/>
		{/if}
	</div>
</div>
