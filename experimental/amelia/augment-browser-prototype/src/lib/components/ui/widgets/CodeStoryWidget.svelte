<script lang="ts">
	import {
		Icon,
		Plus,
		Minus,
		ArrowPath,
		Pencil,
		DocumentText,
		ChevronDown
	} from 'svelte-hero-icons';
	import { fly, slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { type CleanChangedFile } from '$lib/api/unified-client';
	import MonacoProvider from '$lib/components/MonacoProvider';
	import DiffViewer from '$lib/components/agents/DiffViewer.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Drawer from '$lib/components/ui/overlays/Drawer.svelte';
	import FileRenderer from './FileRenderer.svelte';
	import { theme } from '$lib/stores/theme';

	interface Props {
		changedFiles: CleanChangedFile[];
	}

	let { changedFiles }: Props = $props();

	// Get file change type display info
	function getChangeTypeInfo(changeType: string) {
		switch (changeType) {
			case 'added':
				return {
					icon: Plus,
					color: 'text-emerald-700 dark:text-emerald-300',
					bgColor: 'bg-emerald-50 dark:from-emerald-900/30 dark:to-emerald-800/20',
					label: 'Added'
				};
			case 'deleted':
				return {
					icon: Minus,
					color: 'text-red-700 dark:text-red-300',
					bgColor: 'bg-red-50 dark:from-red-900/30 dark:to-red-800/20',
					label: 'Deleted'
				};
			case 'modified':
				return {
					icon: Pencil,
					color: 'text-blue-700 dark:text-blue-300',
					bgColor: 'bg-blue-50 dark:from-blue-900/30 dark:to-blue-800/20',
					label: 'Modified'
				};
			case 'renamed':
				return {
					icon: ArrowPath,
					color: 'text-purple-700 dark:text-purple-300',
					bgColor: 'bg-purple-50 dark:from-purple-900/30 dark:to-purple-800/20',
					label: 'Renamed'
				};
			case 'added_then_deleted':
				return {
					icon: ArrowPath,
					color: 'text-orange-700 dark:text-orange-300',
					bgColor: 'bg-orange-50 dark:from-orange-900/30 dark:to-orange-800/20',
					label: 'Added then Deleted'
				};
			default:
				return {
					icon: DocumentText,
					color: 'text-slate-700 dark:text-slate-300',
					bgColor: 'bg-slate-50 dark:from-slate-900/30 dark:to-slate-800/20',
					label: 'Changed'
				};
		}
	}

	// Get the file path from the changed file object
	function getFilePath(file: CleanChangedFile): string {
		return file.oldPath || file.path || 'Unknown file';
	}

	// Get file name from path
	function getFileName(filePath: string): string {
		return filePath.split('/').pop() || filePath;
	}

	// Get directory path from file path (everything except the filename)
	function getDirectoryPath(filePath: string): string {
		const parts = filePath.split('/');
		if (parts.length <= 1) return '';
		return parts.slice(0, -1).join('/');
	}

	// State for expanded files - expand all by default
	let expandedFiles = $state(new Set<string>());

	// Initialize expanded files when changedFiles changes
	$effect(() => {
		if (changedFiles.length > 0) {
			const allFilePaths = changedFiles.map((file) => getFilePath(file));
			expandedFiles = new Set(allFilePaths);
		}
	});

	// Toggle file expansion
	function toggleFileExpansion(filePath: string) {
		if (expandedFiles.has(filePath)) {
			expandedFiles.delete(filePath);
		} else {
			expandedFiles.add(filePath);
		}
		expandedFiles = new Set(expandedFiles);
	}

	// Check if file has meaningful content to show
	function hasContentToShow(file: CleanChangedFile): boolean {
		return !!(file.oldContent || file.content || file.diff);
	}

	// Drawer state management
	let drawerOpen = $state(false);
	let selectedFile = $state<CleanChangedFile | null>(null);

	// Open file in drawer
	function openFileInDrawer(file: CleanChangedFile) {
		selectedFile = file;
		drawerOpen = true;
	}

	// Close drawer
	function closeDrawer() {
		drawerOpen = false;
		selectedFile = null;
	}

	// Get summary of changes
	let changesSummary = $derived.by(() => {
		const counts = {
			added: changedFiles.filter((f) => f.changeType === 'added').length,
			modified: changedFiles.filter((f) => f.changeType === 'modified').length,
			deleted: changedFiles.filter((f) => f.changeType === 'deleted').length,
			renamed: changedFiles.filter((f) => f.changeType === 'renamed').length,
			addedThenDeleted: changedFiles.filter((f) => f.changeType === 'added_then_deleted').length
		};

		const parts = [];
		if (counts.added > 0) parts.push(`${counts.added} added`);
		if (counts.modified > 0) parts.push(`${counts.modified} modified`);
		if (counts.deleted > 0) parts.push(`${counts.deleted} deleted`);
		if (counts.renamed > 0) parts.push(`${counts.renamed} renamed`);
		if (counts.addedThenDeleted > 0) parts.push(`${counts.addedThenDeleted} temporary`);

		return parts.join(', ');
	});
</script>

<div class="w-full dark:border-slate-700 dark:bg-slate-800">
	{#if changedFiles.length > 0}
		<!-- Changes Summary -->
		<!-- <div class="mb-3">
			<div class="flex items-center gap-1.5 text-sm text-slate-600 dark:text-slate-400">
				<Icon src={DocumentText} class="h-4 w-4" micro />
				<span class="">{changedFiles.length} files updated</span>
				{#if changesSummary}
					<span class="text-slate-500 dark:text-slate-500">•</span>
					<span>{changesSummary}</span>
				{/if}
			</div>
		</div> -->

		<div class="space-y-3">
			{#each changedFiles as file}
				{@const filePath = getFilePath(file)}
				{@const fileName = getFileName(filePath)}
				{@const directoryPath = getDirectoryPath(filePath)}
				{@const changeInfo = getChangeTypeInfo(file.changeType)}
				{@const isExpanded = expandedFiles.has(filePath)}
				{@const hasContent = hasContentToShow(file)}

				<div class="relative" transition:fly={{ duration: 200, easing: quintOut }}>
					<div class="bg-white duration-200 dark:bg-slate-800">
						<!-- File Header -->
						<div class="py-2 pl-4">
							<div class="flex items-center justify-between">
								<!-- File path info -->
								<div class="min-w-0 flex-1">
									<div class="flex flex-col gap-0.5">
										<span class="truncate text-sm font-semibold text-slate-900 dark:text-slate-100">
											{fileName}
										</span>
										{#if directoryPath}
											<span class="truncate text-xs text-slate-500 dark:text-slate-400">
												{directoryPath}
											</span>
										{/if}
									</div>
								</div>

								<!-- Right side: Status badge and expand button -->
								<div class="flex flex-shrink-0 items-center gap-1">
									<!-- Change type badge with label -->
									<span
										class="inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium {changeInfo.color} bg-opacity-10 {changeInfo.bgColor}"
									>
										<Icon src={changeInfo.icon} class="w-3.5" micro />
										<span>{changeInfo.label}</span>
									</span>

									<!-- Expand/Collapse button -->
									{#if hasContent}
										<Button
											variant="ghost"
											size="icon-sm"
											class="!rounded-full {isExpanded ? '' : 'rotate-180'}"
											onclick={() => toggleFileExpansion(filePath)}
											aria-label={isExpanded ? 'Collapse diff' : 'Expand diff'}
											icon={ChevronDown}
										/>
									{/if}
								</div>
							</div>
						</div>

						<!-- Code content -->
						{#if hasContent && isExpanded}
							<div transition:slide={{ duration: 300, easing: quintOut }}>
								<FileRenderer
									{file}
									height={300}
									enableFocusToScroll={true}
									showDrawerButton={true}
									onOpenDrawer={openFileInDrawer}
								/>
							</div>
						{/if}
					</div>
				</div>
			{/each}
		</div>
	{:else}
		<div class="py-12 text-center text-slate-500 dark:text-slate-400">
			<Icon src={DocumentText} class="mx-auto mb-2 h-8 w-8 opacity-50" />
			<p>No code changes to display</p>
		</div>
	{/if}
</div>

<!-- File Drawer -->
<Drawer
	open={drawerOpen}
	onClose={closeDrawer}
	title={selectedFile ? getFileName(getFilePath(selectedFile)) : 'File View'}
	subtitle={selectedFile ? getFilePath(selectedFile) : ''}
	size="xl"
	position="right"
>
	{#if selectedFile}
		<div class="h-full w-[60em]">
			<MonacoProvider.Root>
				{#if selectedFile.changeType === 'added_then_deleted' || (selectedFile.changeType === 'added' && !selectedFile.diff)}
					<!-- For added_then_deleted files or added files without diff, show the content with syntax highlighting -->
					<MonacoProvider.SimpleMonaco
						text={selectedFile.content || selectedFile.oldContent || ''}
						pathName={getFilePath(selectedFile)}
						theme={$theme === 'dark' ? 'vs-dark' : 'vs-light'}
						height={600}
						options={{
							readOnly: true,
							minimap: { enabled: true },
							scrollBeyondLastLine: false,
							wordWrap: 'on'
						}}
					/>
				{:else if selectedFile.changeType === 'modified'}
					<!-- For modified files, show diff view -->
					<DiffViewer
						class="rounded-none border-none"
						doShowHeader={false}
						height="100%"
						fileName={getFilePath(selectedFile)}
						oldContent={selectedFile.oldContent || ''}
						newContent={selectedFile.content || ''}
						autoFocus
					/>
				{:else}
					<!-- For added/deleted/renamed files, show full content -->
					<DiffViewer
						class="rounded-none border-none"
						doShowHeader={false}
						height="100%"
						fileName={getFilePath(selectedFile)}
						oldContent={selectedFile.oldContent || ''}
						newContent={selectedFile.content || ''}
						autoFocus
					/>
				{/if}
			</MonacoProvider.Root>
		</div>
	{:else}
		<div class="flex h-full items-center justify-center">
			<p class="text-gray-500 dark:text-gray-400">No file selected</p>
		</div>
	{/if}
</Drawer>
