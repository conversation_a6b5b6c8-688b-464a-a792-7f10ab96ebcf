<script lang="ts">
	import FileRenderer from './FileRenderer.svelte';
	import type { CleanChangedFile } from '$lib/api/unified-client';

	// Test data for different file change types
	const testFiles: CleanChangedFile[] = [
		{
			path: 'src/components/Button.svelte',
			changeType: 'added',
			content: '<script>\n  export let variant = "primary";\n</script>\n\n<button class="{variant}">\n  <slot />\n</button>',
			oldContent: null
		},
		{
			path: 'src/utils/helpers.ts',
			changeType: 'modified',
			content: 'export function formatDate(date: Date) {\n  return date.toLocaleDateString();\n}\n\nexport function capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}',
			oldContent: 'export function formatDate(date: Date) {\n  return date.toLocaleDateString();\n}'
		},
		{
			path: 'src/legacy/OldComponent.svelte',
			changeType: 'deleted',
			content: null,
			oldContent: '<script>\n  // This component is no longer needed\n  export let data;\n</script>\n\n<div>{data}</div>'
		}
	];

	let selectedFile = $state<CleanChangedFile>(testFiles[0]);
	let showDrawer = $state(false);

	function handleOpenDrawer(file: CleanChangedFile) {
		console.log('Opening drawer for file:', file.path);
		showDrawer = true;
	}
</script>

<div class="p-8 space-y-6">
	<h1 class="text-2xl font-bold">FileRenderer Component Test</h1>

	<!-- File selector -->
	<div class="space-y-2">
		<label class="block text-sm font-medium">Select test file:</label>
		<select bind:value={selectedFile} class="border rounded px-3 py-2">
			{#each testFiles as file}
				<option value={file}>{file.path} ({file.changeType})</option>
			{/each}
		</select>
	</div>

	<!-- FileRenderer test -->
	<div class="space-y-4">
		<h2 class="text-lg font-semibold">FileRenderer with drawer button:</h2>
		<div class="border-2 border-dashed border-gray-300 p-4">
			<FileRenderer
				file={selectedFile}
				height={400}
				enableFocusToScroll={true}
				showDrawerButton={true}
				onOpenDrawer={handleOpenDrawer}
			/>
		</div>
	</div>

	<!-- FileRenderer without drawer button -->
	<div class="space-y-4">
		<h2 class="text-lg font-semibold">FileRenderer without drawer button:</h2>
		<div class="border-2 border-dashed border-gray-300 p-4">
			<FileRenderer
				file={selectedFile}
				height={300}
				enableFocusToScroll={false}
				showDrawerButton={false}
			/>
		</div>
	</div>

	{#if showDrawer}
		<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div class="bg-white p-6 rounded-lg max-w-2xl w-full mx-4">
				<h3 class="text-lg font-semibold mb-4">Drawer Test</h3>
				<p>Drawer opened for: {selectedFile.path}</p>
				<button
					class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
					onclick={() => showDrawer = false}
				>
					Close
				</button>
			</div>
		</div>
	{/if}
</div>
