<script lang="ts">
	import { <PERSON>con, XMark } from 'svelte-hero-icons';
	import type { TriggerCondition, TriggerField } from '$lib/types';
	import { getOperatorDisplayName } from '$lib/trigger-providers';
	import { createEventDispatcher } from 'svelte';
	import Select from './Select.svelte';
	import Input from './Input.svelte';

	interface Props {
		condition: TriggerCondition;
		index: number;
		availableFields: TriggerField[];
		showRemove?: boolean;
		variant?: 'default' | 'compact' | 'readable';
	}

	let {
		condition,
		index,
		availableFields,
		showRemove = true,
		variant = 'default'
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		remove: number;
		edit: { index: number; condition: TriggerCondition };
	}>();

	// Get field info for better display
	let fieldInfo = $derived(
		availableFields.find(f => f.path === condition.field)
	);

	// Get display name for the field
	let fieldDisplayName = $derived(
		fieldInfo?.name || condition.field
	);

	// Format the value for display
	let formattedValue = $derived(() => {
		if (typeof condition.value === 'string') {
			return `"${condition.value}"`;
		}
		if (Array.isArray(condition.value)) {
			return `[${condition.value.join(', ')}]`;
		}
		return String(condition.value);
	});

	function handleRemove() {
		dispatch('remove', index);
	}

	function handleEdit() {
		dispatch('edit', { index, condition });
	}
</script>

{#if variant === 'readable'}
	<!-- Human-readable format -->
	<div class="flex items-center gap-2 group">
		{#if index > 0}
			<span class="text-sm text-gray-500 dark:text-gray-400 font-medium">and</span>
		{/if}
		{#if showRemove}
			<button
				type="button"
				onclick={handleRemove}
				class="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
				aria-label="Remove condition"
			>
				<div class="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
					<Icon src={XMark} micro />
				</div>
			</button>
		{/if}
	</div>
{:else if variant === 'compact'}
	<!-- Compact pill format -->
	<div class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded-md text-xs group">
		<span>{fieldDisplayName}</span>
		<span class="text-blue-600 dark:text-blue-400">{getOperatorDisplayName(condition.operator)}</span>
		<span>{formattedValue}</span>
		{#if showRemove}
			<button
				type="button"
				onclick={handleRemove}
				class="ml-1 opacity-70 hover:opacity-100 transition-opacity"
				aria-label="Remove condition"
			>
				<div class="w-3 h-3">
					<Icon src={XMark} micro />
				</div>
			</button>
		{/if}
	</div>
{:else}
	<!-- Default format with edit capability -->
	<div class="flex gap-2 items-start group">
		<div class="flex-1">
			<Select
				value={condition.field}
				onchange={(value) => dispatch('edit', { index, condition: { ...condition, field: value } })}
				options={availableFields.map(f => ({ value: f.path, label: f.name }))}
			/>
		</div>
		<div class="w-40">
			<Select
				value={condition.operator}
				onchange={(value) => dispatch('edit', { index, condition: { ...condition, operator: value as TriggerCondition['operator'] } })}
				options={fieldInfo?.allowedOperators.map(op => ({ value: op, label: getOperatorDisplayName(op) })) || []}
			/>
		</div>
		<div class="flex-1">
			<Input
				type="text"
				value={condition.value}
				oninput={(value) => dispatch('edit', { index, condition: { ...condition, value: value } })}
				placeholder={fieldInfo?.example ? `e.g., ${fieldInfo.example}` : "Value"}
			/>
			{#if fieldInfo}
				<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
					{fieldInfo.description}
				</p>
			{/if}
		</div>
		{#if showRemove}
			<button
				type="button"
				onclick={handleRemove}
				class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
				aria-label="Remove condition"
			>
				<div class="w-4 h-4">
					<Icon src={XMark} micro />
				</div>
			</button>
		{/if}
	</div>
{/if}
