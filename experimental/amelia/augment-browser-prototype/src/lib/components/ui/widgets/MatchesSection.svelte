<script lang="ts">
	import { MagnifyingGlass } from 'svelte-hero-icons';
	import ExpandableSection from './ExpandableSection.svelte';
	import MatchRow from '$lib/components/ui/data-display/MatchRow.svelte';
	import MatchRowSkeleton from '$lib/components/ui/data-display/MatchRowSkeleton.svelte';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import Input from '$lib/components/ui/forms/Input.svelte';
	import { getContext } from 'svelte';

	interface Props {
		triggerData: Array<{
			trigger: NormalizedTrigger;
			entities: any[];
			executions: any[];
			totalMatches: number;
			isLoading: boolean;
			error?: string;
		}>;
		allExecutions?: any[]; // All executions for finding matches with existing agents
		isLoading: boolean;
		showDismissButton?: boolean; // Whether to show dismiss buttons
		onEntityClick: (entity: any, trigger?: any) => void;
		onAgentClick: (agent: any) => void;
	}

	let {
		triggerData,
		allExecutions = [],
		isLoading,
		showDismissButton = false,
		onEntityClick,
		onAgentClick
	}: Props = $props();

	// Search state
	let searchQuery = $state('');

	// Track if any assign to agent form is open
	let hasOpenAssignForm = $state(false);

	// Get hover context
	const hoverContext = getContext('hover-context') as {
		hoveredEntityId: string | null;
		hoveredExecutionId: string | null;
		setHoveredEntity: (entityId: string | null) => void;
		setHoveredExecution: (executionId: string | null) => void;
	};

	// Utility function to find execution for an entity
	function findExecutionForEntity(entity: any): any | null {
		return allExecutions.find((execution) => {
			// Check if execution has entity data directly
			if (execution.entity && execution.entity.id === entity.id) {
				return true;
			}

			// Check event payload for entity information
			if (execution.event_payload) {
				try {
					const payload =
						typeof execution.event_payload === 'string'
							? JSON.parse(execution.event_payload)
							: execution.event_payload;

					return (
						payload.entity_id === entity.id ||
						payload.entity_url === entity.url ||
						(payload.entity_title && payload.entity_title === entity.title)
					);
				} catch (error) {
					// Ignore JSON parse errors
				}
			}

			return false;
		});
	}

	// Expandable section state - force expand when assign form is open
	let isExpanded = $state(false);
	$effect(() => {
		if (hasOpenAssignForm) {
			isExpanded = true;
		}
	});

	// Derived state for all matches
	let allMatches = $derived.by(() => {
		const matches: Array<{
			timestamp: Date;
			data: any;
			trigger: NormalizedTrigger;
		}> = [];

		triggerData.forEach(({ trigger, entities }) => {
			entities.forEach((entity) => {
				matches.push({
					timestamp: new Date(
						(entity.metadata as any)?.updated_at || (entity.metadata as any)?.created_at || 0
					),
					data: entity,
					trigger
				});
			});
		});

		// Sort by timestamp (most recent first)
		return matches.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
	});

	// Filtered matches based on search
	let filteredMatches = $derived.by(() => {
		if (!searchQuery.trim()) {
			return allMatches;
		}

		const query = searchQuery.toLowerCase();
		return allMatches.filter((match) => {
			const entity = match.data;
			const trigger = match.trigger;

			// Search in entity title/name
			const title = entity.title || entity.name || '';
			if (title.toLowerCase().includes(query)) return true;

			// Search in trigger name
			if (trigger.name?.toLowerCase().includes(query)) return true;

			// Search in entity body/description
			const body = entity.body || entity.description || '';
			if (body.toLowerCase().includes(query)) return true;

			// Search in entity author/creator
			const author = entity.author?.login || entity.creator?.name || '';
			if (author.toLowerCase().includes(query)) return true;

			return false;
		});
	});
</script>

<div class="h-auto w-full">
	<!-- Matches Section -->
	<ExpandableSection
		title="Recent Matches"
		count={filteredMatches.length}
		items={filteredMatches}
		emptyMessage={searchQuery ? 'No matches found for your search' : 'No matches yet'}
		collapsedHeight={300}
		{isLoading}
		skeletonComponent={MatchRowSkeleton}
		skeletonCount={3}
		bind:expanded={isExpanded}
	>
		{#if !isLoading && allMatches.length > 3}
			<!-- Search Input inside the section -->
			<Input
				variant="ghost"
				icon={MagnifyingGlass}
				bind:value={searchQuery}
				placeholder="Search matches..."
				iconClass="translate-x-1"
				inputClass="!pl-10"
			/>
		{/if}

		<div class="">
			{#each filteredMatches as match}
				{@const isHovered = hoverContext?.hoveredEntityId === match.data.id}
				<MatchRow
					entity={match.data}
					{isHovered}
					{showDismissButton}
					triggerId={match.trigger?.id}
					onclick={() => onEntityClick(match.data, match.trigger)}
					{onAgentClick}
					onMouseEnter={({ execution }) => {
						hoverContext?.setHoveredEntity(match.data.id);
						if (execution) {
							hoverContext?.setHoveredExecution(execution.id);
						}
					}}
					onMouseLeave={() => {
						hoverContext?.setHoveredEntity(null);
						hoverContext?.setHoveredExecution(null);
					}}
				/>
			{/each}
		</div>
	</ExpandableSection>
</div>
