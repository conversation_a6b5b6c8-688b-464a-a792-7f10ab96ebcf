<script lang="ts">
	import type { Snippet } from 'svelte';
	import ExpandableSection from './ExpandableSection.svelte';

	interface GroupItem {
		title: string;
		items: any[];
		emptyMessage?: string;
	}

	interface Props {
		title: string;
		subtitle?: string;
		groups: GroupItem[];
		groupContent?: Snippet<[any[], number]>;
		totalCount?: number;
		expanded?: boolean;
		isLoading?: boolean;
		skeletonComponent?: any;
		skeletonCount?: number;
		collapsedHeight?: number;
		onToggle?: () => void;
		children?: any;
	}

	let {
		title,
		subtitle,
		groups,
		totalCount,
		groupContent,
		expanded = $bindable(false),
		isLoading = false,
		skeletonComponent,
		skeletonCount = 3,
		collapsedHeight = 500,
		onToggle,
		children
	}: Props = $props();

	// Calculate total items across all groups
	let totalItems = $derived(groups.reduce((sum, group) => sum + group.items.length, 0));
	let displayCount = $derived(totalCount !== undefined ? totalCount : totalItems);

	// Create a flat items array for ExpandableSection
	let flatItems = $derived(groups.flatMap(group => group.items));

</script>

<ExpandableSection
	{title}
	{subtitle}
	count={displayCount}
	items={flatItems}
	{expanded}
	{isLoading}
	{skeletonComponent}
	{skeletonCount}
	{collapsedHeight}
	{onToggle}
>
	{#snippet children()}
		{@render children?.()}

		<!-- Grouped Content -->
		{#each groups as group, groupIndex}
			{#if group.items.length > 0}
				<!-- Group Header - only show for non-first groups or when title is "Deleted Agents" -->
				{#if (groups.length > 1 && groupIndex > 0) || group.title === 'Deleted Agents'}
					<div class="px-4 py-2 bg-slate-50 dark:bg-slate-800 border-t border-slate-200 border-b border-b-slate-100 dark:border-b-slate-800 dark:border-slate-800">
						<h5 class="text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wide">
							{group.title} ({group.items.length})
						</h5>
					</div>
				{/if}

				<!-- Group Items -->
				<div class="divide-y divide-slate-200 dark:divide-slate-700">
					{@render groupContent?.(group.items, groupIndex)}
				</div>
			{:else if group.emptyMessage && !isLoading}
				<!-- Group Empty State -->
				{#if (groups.length > 1 && groupIndex > 0) || group.title === 'Deleted Agents'}
					<div class="px-4 py-2 bg-slate-50 dark:bg-slate-800 border-t border-slate-200 border-b border-b-slate-100 dark:border-b-slate-800 dark:border-slate-800">
						<h5 class="text-xs font-medium text-slate-700 dark:text-slate-300 uppercase tracking-wide">
							{group.title} (0)
						</h5>
					</div>
				{/if}
				<div class="px-4 py-12 text-center">
					<p class="text-xs text-slate-500 dark:text-slate-400 italic">
						{group.emptyMessage}
					</p>
				</div>
			{/if}
		{/each}
	{/snippet}
</ExpandableSection>
