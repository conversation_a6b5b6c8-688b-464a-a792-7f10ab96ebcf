<script lang="ts">
	import { RemoteAgentStatus, RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import { slide, scale } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	interface Props {
		status: RemoteAgentStatus;
		workspaceStatus?: RemoteAgentWorkspaceStatus;
		hasUpdates?: boolean;
		showDescription?: boolean;
		variant?: 'card' | 'banner' | 'compact';
		class?: string;
	}

	let {
		status,
		workspaceStatus = RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_UNSPECIFIED,
		hasUpdates = false,
		showDescription = true,
		variant = 'card',
		class: className = ''
	}: Props = $props();

	// Enhanced status information with detailed descriptions and actions
	let statusDetails = $derived(
		(() => {
			switch (status) {
				case RemoteAgentStatus.AGENT_STARTING:
					return {
						title: 'Environment Starting',
						description: 'Setting up workspace and dependencies',
						longDescription:
							'The agent is initializing its environment, installing dependencies, and preparing to work on your task.',
						color: 'blue',
						icon: '🚀',
						progress: 25,
						actions: ['View logs', 'Cancel setup']
					};
				case RemoteAgentStatus.AGENT_RUNNING:
					return {
						title: 'Working',
						description: 'Actively processing your request',
						longDescription:
							'The agent is analyzing your requirements, making code changes, and working towards completing your task.',
						color: 'blue',
						icon: '⚙️',
						progress: 60,
						actions: ['View progress', 'Send message', 'Pause work']
					};
				case RemoteAgentStatus.AGENT_IDLE:
					return {
						title: hasUpdates ? 'Ready for Review' : 'Waiting',
						description: hasUpdates
							? 'Changes are ready for your review'
							: 'Waiting for your next instruction',
						longDescription: hasUpdates
							? 'The agent has completed work and made changes that are ready for your review and feedback.'
							: 'The agent is ready and waiting for your next instruction or task.',
						color: 'green',
						icon: hasUpdates ? '✅' : '⏸️',
						progress: hasUpdates ? 100 : 0,
						actions: hasUpdates
							? ['Review changes', 'Send feedback', 'Approve']
							: ['Send message', 'Assign task']
					};
				case RemoteAgentStatus.AGENT_FAILED:
					return {
						title: 'Failed',
						description: 'Encountered an error and cannot continue',
						longDescription:
							'The agent has encountered an error that prevents it from continuing. You may need to restart or provide additional guidance.',
						color: 'red',
						icon: '❌',
						progress: 0,
						actions: ['View error', 'Restart', 'Debug']
					};
				case RemoteAgentStatus.AGENT_PENDING:
					return {
						title: 'Pending',
						description: 'Waiting to be assigned to a workspace',
						longDescription:
							'The agent has been created but is waiting for an available workspace to begin processing your request.',
						color: 'orange',
						icon: '⏳',
						progress: 10,
						actions: ['Check queue', 'Cancel']
					};
				default:
					return {
						title: 'Unknown Status',
						description: 'Status is unclear',
						longDescription:
							'The agent status could not be determined. Please refresh or contact support.',
						color: 'gray',
						icon: '❓',
						progress: 0,
						actions: ['Refresh', 'Contact support']
					};
			}
		})()
	);
</script>

{#if variant === 'banner'}
	<!-- Banner variant - full width with progress -->
	<div
		class="agent-state-banner agent-state-banner--{statusDetails.color} {className}"
		in:slide={{ duration: 300, easing: quintOut }}
	>
		<div class="flex items-center gap-4">
			<div class="flex-shrink-0">
				<RemoteAgentStatusIndicator
					{status}
					{workspaceStatus}
					{hasUpdates}
					variant="sleek"
					size="lg"
					isExpanded={true}
				/>
			</div>
			<div class="min-w-0 flex-1">
				<div class="mb-1 flex items-center gap-2">
					<span class="text-lg">{statusDetails.icon}</span>
					<h3 class="font-semibold text-gray-900 dark:text-white">{statusDetails.title}</h3>
				</div>
				<p class="text-sm text-gray-600 dark:text-gray-400">{statusDetails.description}</p>
				{#if statusDetails.progress > 0}
					<div class="mt-2 h-1.5 w-full rounded-full bg-gray-200 dark:bg-gray-700">
						<div
							class="progress-bar progress-bar--{statusDetails.color} h-1.5 rounded-full transition-all duration-500"
							style="width: {statusDetails.progress}%"
						></div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{:else if variant === 'compact'}
	<!-- Compact variant - minimal space -->
	<div class="agent-state-compact {className}" in:scale={{ duration: 200, easing: quintOut }}>
		<div class="flex items-center gap-2">
			<RemoteAgentStatusIndicator
				{status}
				{workspaceStatus}
				{hasUpdates}
				variant="sleek"
				size="sm"
			/>
			<span class="text-sm font-medium text-gray-700 dark:text-gray-300">{statusDetails.title}</span
			>
		</div>
	</div>
{:else}
	<!-- Card variant - detailed view -->
	<div
		class="agent-state-card agent-state-card--{statusDetails.color} {className}"
		in:scale={{ duration: 300, easing: quintOut }}
	>
		<div class="flex items-start gap-4">
			<div class="mt-1 flex-shrink-0">
				<RemoteAgentStatusIndicator
					{status}
					{workspaceStatus}
					{hasUpdates}
					variant="sleek"
					size="xl"
					isExpanded={false}
				/>
			</div>
			<div class="min-w-0 flex-1">
				<div class="mb-2 flex items-center gap-2">
					<span class="text-2xl">{statusDetails.icon}</span>
					<h3 class="text-lg font-semibold text-gray-900 dark:text-white">{statusDetails.title}</h3>
				</div>

				{#if showDescription}
					<p class="mb-3 text-sm text-gray-600 dark:text-gray-400">
						{statusDetails.longDescription}
					</p>
				{:else}
					<p class="mb-3 text-sm text-gray-600 dark:text-gray-400">{statusDetails.description}</p>
				{/if}

				{#if statusDetails.progress > 0}
					<div class="mb-4">
						<div class="mb-1 flex justify-between text-xs text-gray-500 dark:text-gray-400">
							<span>Progress</span>
							<span>{statusDetails.progress}%</span>
						</div>
						<div class="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
							<div
								class="progress-bar progress-bar--{statusDetails.color} h-2 rounded-full transition-all duration-700"
								style="width: {statusDetails.progress}%"
							></div>
						</div>
					</div>
				{/if}

				<!-- Action buttons -->
				<div class="flex flex-wrap gap-2">
					{#each statusDetails.actions as action}
						<button class="action-button action-button--{statusDetails.color}" type="button">
							{action}
						</button>
					{/each}
				</div>
			</div>
		</div>
	</div>
{/if}

<style>
	/* Banner variant */
	.agent-state-banner {
		padding: 1rem 1.5rem;
		border-radius: 0.75rem;
		border-left: 4px solid;
		background: rgba(255, 255, 255, 0.8);
		backdrop-filter: blur(8px);
		border: 1px solid rgba(0, 0, 0, 0.1);
	}

	.agent-state-banner--blue {
		border-left-color: #3b82f6;
		background: rgba(59, 130, 246, 0.05);
	}

	.agent-state-banner--green {
		border-left-color: #10b981;
		background: rgba(16, 185, 129, 0.05);
	}

	.agent-state-banner--red {
		border-left-color: #ef4444;
		background: rgba(239, 68, 68, 0.05);
	}

	.agent-state-banner--orange {
		border-left-color: #f97316;
		background: rgba(249, 115, 22, 0.05);
	}

	.agent-state-banner--gray {
		border-left-color: #9ca3af;
		background: rgba(156, 163, 175, 0.05);
	}

	/* Compact variant */
	.agent-state-compact {
		padding: 0.5rem;
	}

	/* Card variant */
	.agent-state-card {
		padding: 1.5rem;
		border-radius: 1rem;
		background: rgba(255, 255, 255, 0.9);
		backdrop-filter: blur(12px);
		border: 1px solid rgba(0, 0, 0, 0.1);
		box-shadow:
			0 4px 6px -1px rgba(0, 0, 0, 0.1),
			0 2px 4px -1px rgba(0, 0, 0, 0.06);
	}

	.agent-state-card--blue {
		background: rgba(59, 130, 246, 0.03);
		border-color: rgba(59, 130, 246, 0.2);
	}

	.agent-state-card--green {
		background: rgba(16, 185, 129, 0.03);
		border-color: rgba(16, 185, 129, 0.2);
	}

	.agent-state-card--red {
		background: rgba(239, 68, 68, 0.03);
		border-color: rgba(239, 68, 68, 0.2);
	}

	.agent-state-card--orange {
		background: rgba(249, 115, 22, 0.03);
		border-color: rgba(249, 115, 22, 0.2);
	}

	.agent-state-card--gray {
		background: rgba(156, 163, 175, 0.03);
		border-color: rgba(156, 163, 175, 0.2);
	}

	/* Progress bars */
	.progress-bar--blue {
		background: linear-gradient(90deg, #3b82f6, #1d4ed8);
	}

	.progress-bar--green {
		background: linear-gradient(90deg, #10b981, #059669);
	}

	.progress-bar--red {
		background: linear-gradient(90deg, #ef4444, #dc2626);
	}

	.progress-bar--orange {
		background: linear-gradient(90deg, #f97316, #ea580c);
	}

	.progress-bar--gray {
		background: linear-gradient(90deg, #9ca3af, #6b7280);
	}

	/* Action buttons */
	.action-button {
		padding: 0.375rem 0.75rem;
		border-radius: 0.375rem;
		font-size: 0.75rem;
		font-weight: 500;
		border: 1px solid;
		background: rgba(255, 255, 255, 0.8);
		transition: all 0.2s ease;
		cursor: pointer;
	}

	.action-button:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.action-button--blue {
		border-color: #3b82f6;
		color: #1e40af;
	}

	.action-button--blue:hover {
		background: #3b82f6;
		color: white;
	}

	.action-button--green {
		border-color: #10b981;
		color: #065f46;
	}

	.action-button--green:hover {
		background: #10b981;
		color: white;
	}

	.action-button--red {
		border-color: #ef4444;
		color: #991b1b;
	}

	.action-button--red:hover {
		background: #ef4444;
		color: white;
	}

	.action-button--orange {
		border-color: #f97316;
		color: #9a3412;
	}

	.action-button--orange:hover {
		background: #f97316;
		color: white;
	}

	.action-button--gray {
		border-color: #9ca3af;
		color: #374151;
	}

	.action-button--gray:hover {
		background: #9ca3af;
		color: white;
	}

	/* Dark mode */
	:global(.dark) .agent-state-banner,
	:global(.dark) .agent-state-card {
		background: rgba(0, 0, 0, 0.4);
		border-color: rgba(255, 255, 255, 0.1);
	}

	:global(.dark) .action-button {
		background: rgba(0, 0, 0, 0.3);
		color: #d1d5db;
	}

	:global(.dark) .action-button--blue {
		color: #93c5fd;
	}

	:global(.dark) .action-button--green {
		color: #6ee7b7;
	}

	:global(.dark) .action-button--red {
		color: #fca5a5;
	}

	:global(.dark) .action-button--orange {
		color: #fdba74;
	}
</style>
