<script lang="ts">
	import { <PERSON><PERSON>, <PERSON>rk<PERSON> } from 'svelte-hero-icons';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	interface Props {
		summary: string;
	}

	let { summary }: Props = $props();
</script>

{#if summary}
	<!-- Compact variant with subtle styling -->
	<div
		class="rounded-lg border border-gray-200 bg-white px-4 py-3 text-sm leading-relaxed text-gray-700 shadow-sm dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300"
	>
		<Markdown content={summary} size="sm" hasNoPadding />
	</div>
{/if}
