<script lang="ts">
	import type { Snippet } from "svelte";
	import Button from "./Button.svelte";
	import { ChevronDown, Icon } from "svelte-hero-icons";

// ExpandSection
let {
    header,
    children,
    collapsedHeight = 100
}: {
    header?: Snippet;
    children: Snippet;
    collapsedHeight?: number;
} = $props();

let isExpanded = $state(false);
let container: HTMLElement;

$effect(() => {
    if (container) {
        isExpanded = container.scrollHeight <= collapsedHeight;
    }
});
</script>

<div
    bind:this={container}
    class="relative transition-all duration-200"
>
<div class="w-full sticky flex top-0 py-2 bg-white dark:bg-gray-900">
    {#if header}
        <div class="flex-1">

            {@render header()}
        </div>
        <div class="flex-none">
            <Button
            size="icon-sm"
                variant="ghost"
                onclick={() => { isExpanded = !isExpanded; }}
                icon={ChevronDown}
                class="transition-all duration-200 {isExpanded ? '' : 'rotate-90'}"
            />
        </div>
    {:else}
        <div class="flex-1">
            <Button
                size="xs"
                variant="ghost"
                onclick={() => { isExpanded = !isExpanded; }}
            >
                {isExpanded ? 'Show less' : 'Show more'}
            </Button>
        </div>
    {/if}
    </div>

    <div
        class="overflow-hidden transition-all duration-200 w-full"
        style={`max-height: ${isExpanded ? 5000 : collapsedHeight}px;`}
    >
    {@render children()}
</div>
</div>
