<script lang="ts">
	import {
		Icon,
		ChevronDown,
		ChevronRight,
		Plus,
		Minus,
		ArrowPath,
		Pencil,
		DocumentPlus
	} from 'svelte-hero-icons';
	import { slide } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { type CleanChangedFile } from '$lib/api/unified-client';

	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { GitPullRequest } from '$lib/icons/GitPullRequest.svelte';
	import Drawer from '$lib/components/ui/overlays/Drawer.svelte';
	import FileRenderer from './FileRenderer.svelte';

	interface Props {
		changedFiles: CleanChangedFile[];
		isExpanded?: boolean;
		onCreatePR?: () => void;
		hasPRUrl?: boolean;
	}

	let { changedFiles, isExpanded = false, onCreatePR, hasPRUrl = false }: Props = $props();

	let localExpanded = $state(isExpanded);

	// Get file change type display info
	function getChangeTypeInfo(changeType: string) {
		switch (changeType) {
			case 'added':
				return {
					icon: Plus,
					color: 'text-emerald-600 dark:text-emerald-400',
					bgColor: 'bg-green-50 dark:bg-green-900/20'
				};
			case 'deleted':
				return {
					icon: Minus,
					color: 'text-red-600 dark:text-red-400',
					bgColor: 'bg-red-50 dark:bg-red-900/20'
				};
			case 'modified':
				return {
					icon: Pencil,
					color: 'text-amber-400 dark:text-amber-600',
					bgColor: 'bg-blue-50 dark:bg-blue-900/20'
				};
			case 'renamed':
				return {
					icon: ArrowPath,
					color: 'text-purple-600 dark:text-purple-400',
					bgColor: 'bg-purple-50 dark:bg-purple-900/20'
				};
			case 'added_then_deleted':
				return {
					icon: ArrowPath,
					color: 'text-orange-600 dark:text-orange-400',
					bgColor: 'bg-orange-50 dark:bg-orange-900/20'
				};
			default:
				return {
					icon: Pencil,
					color: 'text-slate-600 dark:text-slate-400',
					bgColor: 'bg-slate-50 dark:bg-slate-900/20'
				};
		}
	}

	// Get the file path from the changed file object
	function getFilePath(file: CleanChangedFile): string {
		return file.path || file.oldPath || 'Unknown file';
	}

	// Get file name from path
	function getFileName(filePath: string | undefined): string {
		if (!filePath) return 'Unknown file';
		return filePath.split('/').pop() || filePath;
	}

	// Get directory path from file path (everything except the filename)
	function getDirectoryPath(filePath: string | undefined): string {
		if (!filePath) return '';
		const parts = filePath.split('/');
		if (parts.length <= 1) return '';
		return parts.slice(0, -1).join('/');
	}

	// Get the display name for a directory (only the last segment)
	function getDirectoryDisplayName(fullPath: string): string {
		if (!fullPath) return '';
		const parts = fullPath.split('/');
		return parts[parts.length - 1] || fullPath;
	}

	// Group files by directory structure
	interface FileTreeNode {
		path: string;
		files: CleanChangedFile[];
		children: Map<string, FileTreeNode>;
		isRoot?: boolean;
	}

	function buildFileTree(files: CleanChangedFile[]): FileTreeNode {
		const root: FileTreeNode = {
			path: '',
			files: [],
			children: new Map(),
			isRoot: true
		};

		for (const file of files) {
			const filePath = getFilePath(file);
			const parts = filePath.split('/');

			if (parts.length === 1) {
				// File in root directory
				root.files.push(file);
			} else {
				// File in subdirectory
				const dirPath = parts.slice(0, -1).join('/');
				let current = root;

				// Build the directory path
				const pathParts = dirPath.split('/');
				let currentPath = '';

				for (const part of pathParts) {
					currentPath = currentPath ? `${currentPath}/${part}` : part;

					if (!current.children.has(part)) {
						current.children.set(part, {
							path: currentPath,
							files: [],
							children: new Map()
						});
					}
					current = current.children.get(part)!;
				}

				// Add file to the final directory
				current.files.push(file);
			}
		}

		return root;
	}

	let fileTree = $derived(buildFileTree(changedFiles));

	// For collapsed preview - show first 3 files as rows
	const MAX_PREVIEW_ROWS = 3;
	let previewFiles = $derived(changedFiles.slice(0, MAX_PREVIEW_ROWS));
	let remainingFilesCount = $derived(Math.max(0, changedFiles.length - MAX_PREVIEW_ROWS));

	// Calculate the minimum depth to make all depths relative to the shallowest path
	let minDepth = $derived.by(() => {
		if (changedFiles.length === 0) return 0;

		const depths = changedFiles.map((file) => {
			const filePath = getFilePath(file);
			return filePath.split('/').length - 1; // -1 because file name doesn't count as depth
		});

		return Math.min(...depths);
	});

	// Track expanded state for each directory - expand all by default
	function getAllDirectoryPaths(node: FileTreeNode): string[] {
		const paths: string[] = [];
		// Add any non-root directory (whether it has children or files)
		if (!node.isRoot) {
			paths.push(node.path);
		}
		for (const child of node.children.values()) {
			paths.push(...getAllDirectoryPaths(child));
		}
		return paths;
	}

	let expandedDirs = $state(new Set<string>());

	// Update expanded directories when file tree changes
	$effect(() => {
		const allPaths = getAllDirectoryPaths(fileTree);
		expandedDirs = new Set(allPaths);
	});

	// State for file diff drawer
	let drawerOpen = $state(false);
	let selectedFile = $state<CleanChangedFile | null>(null);

	// Function to open file diff in drawer
	function openFileDiff(file: CleanChangedFile) {
		selectedFile = file;
		drawerOpen = true;
	}

	// Function to close drawer
	function closeDrawer() {
		drawerOpen = false;
		selectedFile = null;
	}
</script>

{#if changedFiles.length > 0}
	<div
		class="overflow-hidden rounded-lg border border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800"
	>
		<!-- Header with PR Action -->
		<div class="border-b border-slate-200 px-4 dark:border-slate-700">
			<div class="mt-3 mb-2 flex items-center justify-between gap-4">
				<!-- File Summary Info -->
				<div class="flex items-center gap-1.5">
					<Icon src={DocumentPlus} class="h-5 w-5 text-slate-500 dark:text-slate-400" micro />
					<div>
						<div class="text-sm font-medium text-slate-900 dark:text-slate-100">
							{changedFiles.length} file{changedFiles.length !== 1 ? 's' : ''} changed
						</div>
					</div>
				</div>

				<!-- Action Button -->
				{#if onCreatePR}
					<Button
						size="sm"
						variant="outline"
						onclick={onCreatePR}
						class="-my-2 flex-shrink-0 border-slate-200 bg-white hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-900 dark:hover:bg-slate-800"
					>
						<div class="flex items-center gap-2">
							<Icon src={GitPullRequest} micro class="h-4 w-4 text-slate-500 dark:text-slate-400" />
							{hasPRUrl ? 'View PR' : 'Create PR'}
						</div>
					</Button>
				{/if}
			</div>
		</div>

		<!-- Collapsed preview - show first 3 files as rows -->
		{#if !localExpanded && changedFiles.length > 0}
			<div class="divide-y divide-slate-200 dark:divide-slate-700">
				{#each previewFiles as file}
					{@const changeInfo = getChangeTypeInfo(file.changeType)}
					{@const filePath = getFilePath(file)}
					<div>
						<button
							class="flex w-full items-center justify-start gap-2 px-3 py-1.5 text-left transition-colors duration-200 hover:bg-slate-50 dark:hover:bg-slate-700/50"
							onclick={() => openFileDiff(file)}
						>
							<!-- Change type badge -->
							<div class="flex-shrink-0">
								<span
									class="inline-flex items-center rounded-full py-1 text-xs font-medium {changeInfo.color}"
								>
									<Icon src={changeInfo.icon} class="w-3.5" micro />
								</span>
							</div>

							<!-- File name -->
							<div class="flex min-w-0 flex-1 items-center justify-between">
								<div class="text-xs font-medium text-slate-900 dark:text-slate-100">
									{getFileName(filePath)}
								</div>
								{#if getDirectoryPath(filePath)}
									<div class="ml-2 flex-shrink-0 text-xs text-slate-500 dark:text-slate-400">
										{getDirectoryPath(filePath)}
									</div>
								{/if}
							</div>

							<!-- Stats not available in current API -->
						</button>
					</div>
				{/each}

				<!-- Expand/Collapse trigger row -->
				{#if remainingFilesCount > 0 || localExpanded}
					<div>
						<button
							class="group flex w-full items-center justify-between px-3 py-2 text-xs font-medium text-slate-600 transition-all duration-200 hover:bg-slate-50 hover:text-slate-900 dark:text-slate-400 dark:hover:bg-slate-700/50 dark:hover:text-slate-100"
							onclick={() => {
								localExpanded = !localExpanded;
							}}
							aria-label={localExpanded ? 'Collapse file changes' : 'Expand file changes'}
						>
							<span>
								{#if remainingFilesCount > 0}
									{localExpanded
										? 'Show less'
										: `Show ${remainingFilesCount} more file${remainingFilesCount !== 1 ? 's' : ''}`}
								{:else}
									{localExpanded ? 'Show less' : 'Show all files'}
								{/if}
							</span>
							<Icon
								src={localExpanded ? ChevronDown : ChevronRight}
								class="h-3.5 w-3.5 transition-transform duration-200 group-hover:scale-110"
								micro
							/>
						</button>
					</div>
				{/if}
			</div>
		{/if}

		<!-- Expanded content -->
		{#if localExpanded}
			<div transition:slide={{ duration: 300, easing: quintOut }}>
				<div class="divide-y divide-slate-200 dark:divide-slate-700">
					{#snippet renderFileTreeNode(node: FileTreeNode, depth: number = 0)}
						<!-- Render directory header if not root and has children -->
						{#if !node.isRoot && node.files.length > 0}
							{@const isExpanded = expandedDirs.has(node.path)}
							<div
								class="flex cursor-pointer items-center gap-2 px-3 py-[5px] hover:bg-slate-50 dark:hover:bg-slate-700/50"
								style="padding-left: {Math.max(0, depth - minDepth) * 16 + 12}px"
								role="button"
								tabindex="0"
								transition:slide={{ axis: 'y' }}
								onclick={() => {
									if (isExpanded) {
										expandedDirs.delete(node.path);
									} else {
										expandedDirs.add(node.path);
									}
									expandedDirs = new Set(expandedDirs);
								}}
								onkeydown={(e) => {
									if (e.key === 'Enter' || e.key === ' ') {
										e.preventDefault();
										if (isExpanded) {
											expandedDirs.delete(node.path);
										} else {
											expandedDirs.add(node.path);
										}
										expandedDirs = new Set(expandedDirs);
									}
								}}
								aria-label={isExpanded
									? `Collapse ${node.path} directory`
									: `Expand ${node.path} directory`}
							>
								<Icon
									src={isExpanded ? ChevronDown : ChevronRight}
									class="h-3 w-3 text-slate-400"
									micro
								/>
								<span class="text-xs font-medium text-slate-600 dark:text-slate-300">
									{getDirectoryDisplayName(node.path)}
								</span>
							</div>
						{/if}

						<!-- Render files in this directory -->
						{#if node.isRoot || expandedDirs.has(node.path)}
							<div
								class="w-full divide-y divide-slate-200 dark:divide-slate-700"
								transition:slide={{ axis: 'y' }}
							>
								{#each node.files as file}
									{@const changeInfo = getChangeTypeInfo(file.changeType)}
									{@const filePath = getFilePath(file)}
									{@const indentLevel = Math.max(0, depth + (node.isRoot ? 0 : 1) - minDepth)}
									<div style="padding-left: {indentLevel * 16}px" transition:slide={{ axis: 'y' }}>
										<button
											class="flex w-full items-center justify-start gap-2 px-3 py-2 text-left transition-colors duration-200 hover:bg-slate-50 dark:hover:bg-slate-700/50"
											onclick={() => openFileDiff(file)}
										>
											<!-- Change type badge -->
											<div class="flex-shrink-0">
												<span
													class="inline-flex items-center rounded-full py-1 text-xs font-medium {changeInfo.color}"
												>
													<Icon src={changeInfo.icon} class="w-3.5" micro />
												</span>
											</div>

											<!-- File name -->
											<div class="min-w-0 flex-1">
												<div
													class="text-xs font-medium whitespace-nowrap text-slate-900 dark:text-slate-100"
												>
													{getFileName(filePath)}
												</div>
											</div>

											<!-- Stats not available in current API -->
										</button>
									</div>
								{/each}

								<!-- Render child directories -->
								{#each Array.from(node.children.values()) as childNode}
									{@render renderFileTreeNode(childNode, depth + (node.isRoot ? 0 : 1))}
								{/each}
							</div>
						{/if}
					{/snippet}

					{@render renderFileTreeNode(fileTree)}

					<!-- Collapse trigger row at the end -->
					<div>
						<button
							class="group flex w-full items-center justify-between px-3 py-2 text-xs font-medium text-slate-600 transition-all duration-200 hover:bg-slate-50 hover:text-slate-900 dark:text-slate-400 dark:hover:bg-slate-700/50 dark:hover:text-slate-100"
							onclick={() => {
								localExpanded = !localExpanded;
							}}
							aria-label="Collapse file changes"
						>
							<span>Show less</span>
							<Icon
								src={ChevronDown}
								class="h-3.5 w-3.5 transition-transform duration-200 group-hover:scale-110"
								micro
							/>
						</button>
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}

<!-- File Diff Drawer -->
<Drawer
	open={drawerOpen}
	onClose={closeDrawer}
	title={selectedFile ? getFileName(getFilePath(selectedFile)) : 'File Diff'}
	subtitle={selectedFile ? getFilePath(selectedFile) : ''}
	size="xl"
	position="right"
>
	{#if selectedFile}
		<div class="h-full w-[60em]">
			<FileRenderer
				file={selectedFile}
				height="100%"
				enableFocusToScroll={false}
				showDrawerButton={false}
				class="rounded-none border-none"
			/>
		</div>
	{:else}
		<div class="flex h-full items-center justify-center">
			<p class="text-gray-500 dark:text-gray-400">No file selected</p>
		</div>
	{/if}
</Drawer>
