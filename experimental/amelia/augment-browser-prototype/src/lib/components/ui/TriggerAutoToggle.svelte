<script lang="ts">
	import { Icon, Bolt } from 'svelte-hero-icons';

	interface Props {
		enabled: boolean;
		onToggle: () => void;
		size?: 'sm' | 'md' | 'lg';
		showIcon?: boolean;
		showLabel?: boolean;
		variant?: 'default' | 'compact';
	}

	let {
		enabled,
		onToggle,
		size = 'sm',
		showIcon = true,
		showLabel = true,
		variant = 'default'
	}: Props = $props();

	const sizeClasses = {
		sm: 'text-xs',
		md: 'text-sm',
		lg: 'text-md'
	};

	// Custom toggle size configurations
	const toggleConfig = {
		sm: {
			container: 'w-10 h-5',
			thumb: 'w-4 h-4',
			translate: 'translate-x-5',
			icon: 'w-2.5 h-2.5'
		},
		md: {
			container: 'w-12 h-6',
			thumb: 'w-5 h-5',
			translate: 'translate-x-6',
			icon: 'w-3 h-3'
		},
		lg: {
			container: 'w-14 h-7',
			thumb: 'w-6 h-6',
			translate: 'translate-x-7',
			icon: 'w-3.5 h-3.5'
		}
	};

	const config = toggleConfig[size];

	// Computed classes for the toggle
	const containerClasses = $derived([
		'relative inline-flex shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-all duration-300 ease-in-out',
		'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900',
		'shadow-sm hover:shadow-md',
		config.container,
		enabled
			? 'bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500'
			: 'bg-slate-200 dark:bg-slate-700 hover:bg-slate-300 dark:hover:bg-slate-600'
	].filter(Boolean).join(' '));

	const thumbClasses = $derived([
		'pointer-events-none inline-flex items-center justify-center rounded-full bg-white shadow-lg transform ring-0 transition-all duration-300 ease-in-out',
		config.thumb,
		enabled ? config.translate : 'translate-x-0'
	].join(' '));

	function handleToggle() {
		onToggle();
	}
</script>

<div class="flex items-center gap-2 {sizeClasses[size]}">
	{#if variant === 'compact'}
		<div class="flex items-center gap-2">
			<span class="{enabled ? 'text-blue-600 dark:text-blue-400' : 'text-gray-600 dark:text-gray-400'} font-medium">Auto-delegate</span>
			<!-- Custom toggle with bolt in handle -->
			<button
				type="button"
				role="switch"
				aria-checked={enabled}
				aria-label="Toggle automatic execution"
				class={containerClasses}
				onclick={handleToggle}
			>
				<span class={thumbClasses}>
					<Icon micro src={Bolt} class="{config.icon} {enabled ? 'text-blue-600' : 'text-slate-400'}" />
				</span>
			</button>
		</div>
	{:else}
		<div class="flex items-center gap-2">
			{#if showIcon}
				<Icon micro src={Bolt} class="w-4 h-4 text-blue-600 dark:text-blue-400" />
			{/if}
			<div class="w-2 h-2 rounded-full {enabled ? 'bg-green-500' : 'bg-gray-400'}"></div>
			{#if showLabel}
				<span class="text-gray-600 dark:text-gray-400">
					{enabled ? 'Runs automatically' : 'Manual only'}
				</span>
			{/if}
			<!-- Custom toggle with bolt in handle -->
			<button
				type="button"
				role="switch"
				aria-checked={enabled}
				aria-label="Toggle automatic execution"
				class={containerClasses}
				onclick={handleToggle}
			>
				<span class={thumbClasses}>
					<Icon micro src={Bolt} class="{config.icon} {enabled ? 'text-blue-600' : 'text-slate-400'}" />
				</span>
			</button>
		</div>
	{/if}
</div>
