<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import {
		getWorkspaceStatusText,
		getWorkspaceStatusIcon
	} from '$lib/utils/agent-status-indicators.svelte';
	import { getRelativeTime } from '$lib/utils/time';
	import { Icon, ExclamationTriangle, Trash } from 'svelte-hero-icons';
	import RemoteAgentStatusIndicator from '$lib/components/ui/feedback/RemoteAgentStatusIndicator.svelte';
	import {
		getEntityByAgent,
		ensureEntityLoadedForAgent,
		getFlatTriggerExecutions
	} from '$lib/stores/global-state.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import AuggieAvatar from '$lib/components/ui/visualization/AuggieAvatar.svelte';
	import { triggers } from '$lib/stores/global-state.svelte';
	import WorkflowRunCard from '$lib/components/ui/data-display/WorkflowRunCard.svelte';
	import RemoteAgentTitle from './RemoteAgentTitle.svelte';
	import AgentDeletionModal from './overlays/AgentDeletionModal.svelte';

	interface Props {
		agent: CleanRemoteAgent;
		onAgentClick?: (agent: CleanRemoteAgent) => void;
		class?: string;
	}

	let { agent, onAgentClick, class: className = '' }: Props = $props();
	let showDeletionModal = $state(false);

	// Enrich agent with metadata for better display - skip for CleanRemoteAgent

	// Get trigger execution data
	let triggerExecutionsStore = getFlatTriggerExecutions();
	let triggerExecutions = $derived($triggerExecutionsStore);
	let execution = $derived(
		(() => {
			if (!agent?.id) return null;
			return triggerExecutions.find((exec) => exec.remoteAgentId === agent.id);
		})()
	);

	// Get entity from execution
	let trigger = $derived($triggers.find((t) => t.id === execution?.triggerId));

	// Get linked entity for this agent
	let linkedEntityStore = $derived(getEntityByAgent(agent.id));
	let linkedEntity = $derived($linkedEntityStore);

	// Trigger loading when agent changes
	$effect(() => {
		if (agent?.id) {
			ensureEntityLoadedForAgent(agent.id);
		}
	});

	// let linkedEntity = $derived.by(getEntityByRemoteAgentId(agent.remoteAgentId));

	// Simple loading state: show loading when we don't have an entity yet
	let isLinkedEntityLoading = $derived.by(() => {
		// Use the already computed linkedEntity
		const entity = linkedEntity;
		// Show loading if we don't have an entity yet (API call might be in progress)
		const loading = entity?.loading || false;
		return loading;
	});

	// Get latest turn summary - CleanRemoteAgent doesn't have turnSummaries
	let latestTurnSummary = $derived(null);

	// Helper function to format duration
	function formatDuration(milliseconds: number): string {
		const seconds = Math.floor(milliseconds / 1000);
		const minutes = Math.floor(seconds / 60);
		const hours = Math.floor(minutes / 60);
		const days = Math.floor(hours / 24);

		if (days > 0) return `${days}d ${hours % 24}h`;
		if (hours > 0) return `${hours}h ${minutes % 60}m`;
		if (minutes > 0) return `${minutes}m`;
		return `${seconds}s`;
	}

	function handleClick() {
		if (onAgentClick) {
			onAgentClick(agent);
		}
	}

	function handleDeleteAgent() {
		showDeletionModal = true;
	}
</script>

<button
	class="group relative flex w-full max-w-[30em] cursor-pointer flex-col overflow-hidden rounded-2xl border border-slate-200 bg-gradient-to-b from-white to-blue-50/30 text-left transition-all duration-300 hover:border-slate-300 hover:shadow-slate-900/5 dark:border-slate-700 dark:from-slate-900 dark:to-blue-800/10 dark:hover:border-slate-600 dark:hover:shadow-slate-900/20 {className}"
	onclick={() => {
		handleClick();
	}}
	onkeydown={(e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			handleClick();
		}
	}}
>
	<div class="relative px-5 pt-4 pb-3">
		<!-- Avatar and Status -->
		{#if agent}
			<div class="flex w-full justify-between">
				<AuggieAvatar colorSeed={agent.id} faceSeed={agent.id} size={50} />

				<div class="flex-none">
					<RemoteAgentStatusIndicator
						status={agent.status}
						workspaceStatus={agent.workspaceStatus}
						hasUpdates={agent.hasUpdates}
						isExpanded
						size="md"
					/>
				</div>
			</div>
			<div class="flex items-start justify-between">
				<div class="flex items-center gap-3">
					<div class="mt-0.5 flex flex-col">
						<div class="group/title mt-2 mb-1 w-full cursor-pointer text-left focus:outline-none">
							<RemoteAgentTitle {agent} size="sm" class="line-clamp-2" />
						</div>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- Main Content -->
	<div class="flex-1 space-y-3 px-5 pb-4">
		<!-- Latest Activity with beautiful styling -->
		{#if latestTurnSummary}
			<div
				class="relative rounded-xl border border-blue-100/50 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 p-4 dark:border-blue-900/30 dark:from-blue-950/20 dark:to-indigo-950/20"
			>
				<div class="absolute top-3 left-3 h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
				<div class="ml-5">
					<div class="mb-1 text-xs font-medium text-blue-700 dark:text-blue-300">
						Latest activity
					</div>
					<p class="line-clamp-3 text-sm leading-relaxed text-slate-700 dark:text-slate-300">
						{latestTurnSummary}
					</p>
				</div>
			</div>
		{/if}

		<!-- Linked Entity with elegant presentation -->
		{#if linkedEntity?.data}
			{#if linkedEntity.error}
				<!-- Error state -->
				<div class="flex h-8 items-center gap-1.5 rounded-lg bg-slate-100 px-2 dark:bg-slate-950">
					<Icon src={ExclamationTriangle} class="h-4 w-4 text-slate-400 dark:text-slate-400" mini />
					<span class="text-xs text-slate-500 dark:text-slate-500"
						>Failed to load linked entity</span
					>
				</div>
			{:else}
				<!-- button with link -->
				<!-- <a class="w-full flex items-center gap-2 cursor-pointer bg-slate-100 dark:bg-slate-900 border border-slate-100 dark:border-slate-800  rounded-lg px-3 py-2 hover:bg-slate-100 dark:hover:bg-slate-800 transition-colors duration-200" href={$linkedEntity.url} target="_blank" rel="noopener noreferrer"
			 onclick={(e) => e.stopPropagation()}>
					<CustomIcon icon={$linkedEntity.providerId} size={14} class="text-slate-400 dark:text-slate-400" />
						<div class="flex-1 text-xs font-medium text-slate-500 dark:text-slate-400">
						{getProviderDisplayName($linkedEntity.providerId)} {getEntityTypeDisplayName($linkedEntity.entityType, $linkedEntity.providerId)}
					</div>
					<Icon src={ArrowTopRightOnSquare} class="w-4 h-4 text-slate-500" mini />
				</a> -->

				<WorkflowRunCard entity={linkedEntity.data} doAllowAgentCreation={false} />
			{/if}
		{:else if isLinkedEntityLoading}
			<div class="h-8 animate-pulse rounded-lg bg-slate-100 dark:bg-slate-700"></div>
		{/if}

		<!-- Bottom metadata -->
		<div class="flex items-center justify-between">
			<div class="flex items-center gap-3 text-xs text-slate-500 dark:text-slate-400">
				<div class="flex items-center gap-1">
					<div class="h-1 w-1 rounded-full bg-slate-400"></div>
					<span>Started {getRelativeTime(agent.startedAt)}</span>
				</div>
				{#if agent.workspaceStatus}
					<div class="flex items-center gap-1">
						<Icon src={getWorkspaceStatusIcon(agent.workspaceStatus)} class="h-3 w-3" mini />
						<span>Workspace {getWorkspaceStatusText(agent).toLowerCase()}</span>
					</div>
				{/if}
			</div>
			<div class="relative">
				{#if agent.updatedAt !== agent.startedAt}
					<span
						class="transform text-xs text-slate-500 transition-all group-hover:-translate-x-2 group-hover:opacity-0 dark:text-slate-400"
					>
						Updated {getRelativeTime(agent.updatedAt)}
					</span>
				{/if}
				<!-- actions -->
				<div
					class="absolute inset-0 flex translate-x-2 transform cursor-pointer items-center justify-end gap-1.5 opacity-0 transition-all group-hover:translate-x-0 group-hover:opacity-100"
				>
					<Button
						variant="ghost"
						size="icon-sm"
						icon={Trash}
						onclick={(e) => {
							e.stopPropagation();
							handleDeleteAgent();
						}}
					></Button>
				</div>
			</div>
		</div>

		<!-- Expiry Warning removed - not available in CleanRemoteAgent -->

		<!-- Elegant action button -->
		<!-- {#if enrichedAgent.canReceiveMessages}
			<button
				onclick={handleClick}
				class="w-full mt-2 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white text-sm font-medium rounded-xl transition-all duration-200 hover:shadow-lg hover:shadow-blue-500/25 hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
			>
				Open agent workspace
			</button>
		{/if} -->
	</div>
</button>

<!-- Agent Deletion Modal -->
<AgentDeletionModal
	isOpen={showDeletionModal}
	{agent}
	onClose={() => (showDeletionModal = false)}
/>
