<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import { Icon, MagnifyingGlass, Plus, CheckCircle, Clock, Cog6Tooth } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Input from '$lib/components/ui/forms/Input.svelte';
	import RemoteActionRun from '$lib/components/ui/RemoteActionRun.svelte';
	import WorkflowRunCardSkeleton from '$lib/components/ui/data-display/WorkflowRunCardSkeleton.svelte';
	import Masonry from '$lib/components/ui/visualization/Masonry.svelte';
	import { fade, scale } from 'svelte/transition';
	import { crossfade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	const [send, receive] = crossfade({
		duration: 600,
		easing: quintOut,
		fallback(node, params) {
			const style = getComputedStyle(node);
			const transform = style.transform === 'none' ? '' : style.transform;
			return {
				duration: 300,
				easing: quintOut,
				css: t => `
					transform: ${transform} scale(${0.8 + 0.2 * t});
					opacity: ${t}
				`
			};
		}
	});
	import IntegratedAgentForm from './IntegratedAgentForm.svelte';
	interface Props {
		agents: CleanRemoteAgent[];
		isLoading: boolean;
		isExpanded?: boolean;
		selectedAgentId?: string;
		filters: {
			search: string;
			status: string;
			assignee: string;
			hasAgent: string;
		};
		onAgentClick: (agent: CleanRemoteAgent) => void;
		onCreateClick: () => void;
		onFilterChange: (key: string, value: string) => void;
		onClearFilters: () => void;
		showCreateButton?: boolean;
	}

	let {
		agents,
		isLoading,
		isExpanded = false,
		filters,
		onAgentClick,
		onCreateClick,
		onFilterChange,
		onClearFilters,
		showCreateButton = true
	}: Props = $props();

	// Helper function to check if agent was updated recently (last 7 days)
	function isRecentlyUpdated(agent: CleanRemoteAgent): boolean {
		const sevenDaysAgo = new Date();
		sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
		return new Date(agent.updatedAt) > sevenDaysAgo;
	}

	// Categorize agents into sections
	let categorizedAgents = $derived.by(() => {
		let filteredAgents = agents;

		// Apply search filter
		if (filters.search) {
			const searchLower = filters.search.toLowerCase();
			filteredAgents = filteredAgents.filter(agent =>
				agent.sessionSummary?.toLowerCase().includes(searchLower) ||
				agent.turnSummaries?.some((summary: string) => summary.toLowerCase().includes(searchLower))
			);
		}

		// Apply status filter
		if (filters.status) {
			filteredAgents = filteredAgents.filter(agent => agent.status.toString() === filters.status);
		}

		// Categorize agents
		const readyForReview: CleanRemoteAgent[] = [];
		const working: CleanRemoteAgent[] = [];
		const recent: CleanRemoteAgent[] = [];
		const allOthers: CleanRemoteAgent[] = [];

		filteredAgents.forEach(agent => {
			// Ready for Review: has updates and is idle
			if (agent.hasUpdates && agent.status === RemoteAgentStatus.AGENT_IDLE) {
				readyForReview.push(agent);
			}
			// Working: actively running, starting, or pending
			else if ([
				RemoteAgentStatus.AGENT_RUNNING,
				RemoteAgentStatus.AGENT_STARTING,
				RemoteAgentStatus.AGENT_PENDING
			].includes(agent.status)) {
				working.push(agent);
			}
			// Recent: updated in last 7 days (excluding above categories)
			else if (isRecentlyUpdated(agent)) {
				recent.push(agent);
			}
			// All others
			else {
				allOthers.push(agent);
			}
		});

		// Sort each category by updatedAt (most recent first)
		const sortByUpdated = (a: CleanRemoteAgent, b: CleanRemoteAgent) =>
			b.updatedAt.getTime() - a.updatedAt.getTime();

		return {
			readyForReview: readyForReview.sort(sortByUpdated),
			working: working.sort(sortByUpdated),
			recent: recent.sort(sortByUpdated),
			allOthers: allOthers.sort(sortByUpdated)
		};
	});

	// Get total count for display
	const totalAgents = $derived(
		categorizedAgents.readyForReview.length +
		categorizedAgents.working.length +
		categorizedAgents.recent.length +
		categorizedAgents.allOthers.length
	);
</script>

<div class="flex-1 bg-white dark:bg-gray-900 min-h-full flex flex-col h-full">
	<!-- Header with Search -->
	<div class="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 flex-shrink-0">


		<div class="flex items-center justify-between gap-3 transition-all {isExpanded ? 'px-6 py-4' : 'px-4 py-4'}">

	<div class="max-w-[30em]">
		<!-- Integrated Agent Creation Form -->
		<IntegratedAgentForm
		/>
		</div>
			<Input
				icon={MagnifyingGlass}
				type="search"
				class="flex-1 max-w-md"
				placeholder="Search agents..."
				value={filters.search}
				oninput={(value) => onFilterChange('search', String(value))}
			/>

			{#if showCreateButton}
				<Button
					variant="primary"
					icon={Plus}
					onclick={onCreateClick}
					size="sm"
				>
					Create Agent
				</Button>
			{/if}
		</div>
	</div>

	<!-- Dashboard Content -->
	<div class="flex-1 overflow-y-auto transition-all {isExpanded ? 'px-6 py-6' : 'px-4 py-4'}">
		{#if isLoading}
			<!-- Loading State -->
			<div in:fade={{ duration: 300, delay: 100 }} out:fade={{ duration: 200 }}>
				<Masonry
					items={new Array(6).fill(null).map((_, i) => ({ id: `skeleton-${i}` }))}
					idKey="id"
					minColWidth={320}
					maxColWidth={500}
					gap={isExpanded ? 16 : 12}
					style="padding: {isExpanded ? '1rem' : '0.75rem'};"
				>
					{#snippet children()}
						<WorkflowRunCardSkeleton />
					{/snippet}
				</Masonry>
			</div>
		{:else if totalAgents === 0}
			<!-- Empty State -->
			<div class="flex items-center justify-center py-12">
				{#if agents.length === 0}
					<div class="text-center">
						<Icon src={Plus} class="w-12 h-12 text-gray-400 mx-auto mb-4" />
						<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No agents yet</h3>
						<p class="text-sm text-gray-500 dark:text-gray-400 mb-4 max-w-sm">
							Create your first agent to start automating tasks and workflows.
						</p>
						{#if showCreateButton}
							<Button
								variant="primary"
								icon={Plus}
								onclick={onCreateClick}
								size="md"
							>
								Create Your First Agent
							</Button>
						{/if}
					</div>
				{:else}
					<div class="text-center">
						<Icon src={MagnifyingGlass} class="w-12 h-12 text-gray-400 mx-auto mb-4" />
						<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No matching agents</h3>
						<p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
							Try adjusting your search or filters to find what you're looking for.
						</p>
						<Button
							variant="ghost"
							size="sm"
							onclick={onClearFilters}
						>
							Clear filters
						</Button>
					</div>
				{/if}
			</div>
		{:else}
			<!-- Dashboard Sections -->
			<div class="space-y-8" in:fade={{ duration: 300, delay: 100 }}>
				<!-- Ready for Review Section -->
				{#if categorizedAgents.readyForReview.length > 0}
					<section class="space-y-4">
						<div class="flex items-center gap-3">
							<Icon src={CheckCircle} class="w-5 h-5 text-green-600 dark:text-green-400" />
							<h2 class="text-lg font-semibold text-gray-900 dark:text-white">
								Ready for Review
							</h2>
							<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
								{categorizedAgents.readyForReview.length}
							</span>
						</div>
						<p class="text-sm text-gray-600 dark:text-gray-400 -mt-2">
							Agents with completed work ready for your review
						</p>
						<Masonry
							items={categorizedAgents.readyForReview}
							idKey="remoteAgentId"
							minColWidth={320}
							maxColWidth={500}
							gap={isExpanded ? 16 : 12}
						>
							{#snippet children(agent)}
								<div
									in:receive={{ key: agent.remoteAgentId }}
									out:send={{ key: agent.remoteAgentId }}
								>
									<RemoteActionRun
										{agent}
										onAgentClick={(agent) => onAgentClick(agent)}
									/>
								</div>
							{/snippet}
						</Masonry>
					</section>
				{/if}

				<!-- Working Section -->
				{#if categorizedAgents.working.length > 0}
					<section class="space-y-4">
						<div class="flex items-center gap-1.5">
							<Icon src={Cog6Tooth} class="w-5 h-5 text-blue-600 dark:text-blue-400" micro />
							<h2 class="text-lg font-semibold text-gray-900 dark:text-white">
								Working
							</h2>
							<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
								{categorizedAgents.working.length}
							</span>
						</div>
						<p class="text-sm text-gray-600 dark:text-gray-400 -mt-2">
							Agents currently running or starting up
						</p>
						<Masonry
							items={categorizedAgents.working}
							idKey="remoteAgentId"
							minColWidth={320}
							maxColWidth={500}
							gap={isExpanded ? 16 : 12}
						>
							{#snippet children(agent)}
								<div
									in:receive={{ key: agent.remoteAgentId }}
									out:send={{ key: agent.remoteAgentId }}
								>
									<RemoteActionRun
										{agent}
										onAgentClick={(agent) => onAgentClick(agent)}
									/>
								</div>
							{/snippet}
						</Masonry>
					</section>
				{/if}

				<!-- Recent Section -->
				{#if categorizedAgents.recent.length > 0}
					<section class="space-y-4">
						<div class="flex items-center gap-1.5">
							<Icon src={Clock} class="w-5 h-5 text-gray-600 dark:text-gray-400" micro />
							<h2 class="text-lg font-semibold text-gray-900 dark:text-white">
								Recent
							</h2>
							<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
								{categorizedAgents.recent.length}
							</span>
						</div>
						<p class="text-sm text-gray-600 dark:text-gray-400 -mt-2">
							Agents updated in the last 7 days
						</p>
						<Masonry
							items={categorizedAgents.recent}
							idKey="remoteAgentId"
							minColWidth={320}
							maxColWidth={500}
							gap={isExpanded ? 16 : 12}
						>
							{#snippet children(agent)}
								<div
									in:receive={{ key: agent.remoteAgentId }}
									out:send={{ key: agent.remoteAgentId }}
								>
									<RemoteActionRun
										{agent}
										onAgentClick={(agent) => onAgentClick(agent)}
									/>
								</div>
							{/snippet}
						</Masonry>
					</section>
				{/if}

				<!-- All Others Section -->
				{#if categorizedAgents.allOthers.length > 0}
					<section class="space-y-4">
						<div class="flex items-center gap-3">
							<h2 class="text-lg font-semibold text-gray-900 dark:text-white">
								All Agents
							</h2>
							<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
								{categorizedAgents.allOthers.length}
							</span>
						</div>
						<p class="text-sm text-gray-600 dark:text-gray-400 -mt-2">
							All other agents
						</p>
						<Masonry
							items={categorizedAgents.allOthers}
							idKey="remoteAgentId"
							minColWidth={320}
							maxColWidth={500}
							gap={isExpanded ? 16 : 12}
						>
							{#snippet children(agent)}
								<div
									in:receive={{ key: agent.remoteAgentId }}
									out:send={{ key: agent.remoteAgentId }}
								>
									<RemoteActionRun
										{agent}
										onAgentClick={(agent) => onAgentClick(agent)}
									/>
								</div>
							{/snippet}
						</Masonry>
					</section>
				{/if}
			</div>
		{/if}
	</div>
</div>
