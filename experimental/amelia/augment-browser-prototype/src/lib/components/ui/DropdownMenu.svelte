<script lang="ts">
	import { clickOutside } from '$lib/actions/clickOutside';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Portal from './overlays/Portal.svelte';

	interface Props {
		buttonIcon: any;
		buttonSize?: 'icon-sm' | 'xs' | 'sm' | 'md' | 'lg' | 'icon-xs' | 'icon-md' | 'icon-lg' | 'pill';
		buttonVariant?:
			| 'ghost'
			| 'primary'
			| 'secondary'
			| 'ghost-light'
			| 'ghost-inline'
			| 'destructive'
			| 'outline'
			| 'success'
			| 'pill-blue'
			| 'pill-green'
			| 'pill-slate';
		buttonAriaLabel?: string;
		buttonClass?: string;
		width?: string;
		children?: any;
		open?: boolean; // Bindable prop to control open state
		onClose?: () => void;
		onOpen?: () => void;
	}

	let {
		buttonIcon,
		buttonSize = 'icon-sm',
		buttonVariant = 'ghost',
		buttonAriaLabel = 'Menu',
		buttonClass = '',
		width = 'w-56',
		children,
		open = $bindable(),
		onClose,
		onOpen
	}: Props = $props();

	// Use bindable prop if provided, otherwise use internal state
	let internalOpen = $state(false);
	let isOpen = $derived(open !== undefined ? open : internalOpen);

	let triggerElement = $state<HTMLButtonElement>();

	let positionRight = $state(false);
	let dropdownPosition = $state({ top: 0, left: 0, width: 0 });

	const calculatePosition = () => {
		if (!isOpen || !triggerElement) return;

		const buttonRect = triggerElement.getBoundingClientRect();
		const dropdownWidth = width === 'w-56' ? 224 : 200; // Default fallback
		const viewportWidth = window.innerWidth;

		// Check if there's enough space to the right
		const spaceToRight = viewportWidth - buttonRect.right;
		positionRight = spaceToRight < dropdownWidth;

		// Calculate dropdown position
		const dropdownLeft = positionRight
			? buttonRect.left + buttonRect.width - dropdownWidth
			: buttonRect.left;
		dropdownPosition = {
			top: buttonRect.bottom,
			left: dropdownLeft,
			width: dropdownWidth
		};
	};

	$effect(() => {
		if (isOpen && triggerElement) {
			calculatePosition();
		}
	});

	function openMenu() {
		if (open !== undefined) {
			open = true;
		} else {
			internalOpen = true;
		}
		onOpen?.();
	}

	function closeMenu() {
		if (open !== undefined) {
			open = false;
		} else {
			internalOpen = false;
		}
		onClose?.();
	}

	function handleClickOutside(event: Event) {
		// Don't close if clicking on the trigger button
		if (triggerElement && triggerElement.contains(event.target as Node)) {
			return;
		}
		closeMenu();
	}
</script>

<div class="relative">
	<Button
		bind:element={triggerElement}
		onclick={openMenu}
		aria-label={buttonAriaLabel}
		icon={buttonIcon}
		size={buttonSize}
		variant={buttonVariant}
		class={buttonClass}
	/>

	{#if isOpen}
		<Portal>
			<div
				use:clickOutside={handleClickOutside}
				class="fixed top-full z-50 mt-1 {width} rounded-md border border-slate-200 bg-white shadow-lg dark:border-slate-700 dark:bg-slate-800 {positionRight
					? 'right-0'
					: 'left-0'}"
				style="top: {dropdownPosition.top}px; left: {dropdownPosition.left}px;"
			>
				<div class="py-1">
					{@render children?.()}
				</div>
			</div>
		</Portal>
	{/if}
</div>
