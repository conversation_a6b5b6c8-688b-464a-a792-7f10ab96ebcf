<script lang="ts">
	import { Icon, Plus } from 'svelte-hero-icons';
	import type { TriggerCondition, TriggerField } from '$lib/types';
	import { getOperatorDisplayName } from '$lib/trigger-providers';
	import Input from './Input.svelte';
	import Select from './Select.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	interface Props {
		availableFields: TriggerField[];
		disabled?: boolean;
		placeholder?: string;
		onadd?: (condition: TriggerCondition) => void;
	}

	let { availableFields, disabled = false, placeholder = "Add condition...", onadd }: Props = $props();

	let isCreating = $state(false);
	let newCondition = $state<TriggerCondition>({
		field: '',
		operator: 'equals',
		value: ''
	});

	let fieldInputElement = $state<HTMLElement>();
	let valueInputElement = $state<HTMLElement>();

	// Get selected field info
	let selectedField = $derived(
		availableFields.find(f => f.path === newCondition.field)
	);

	// Prepare operator options for Select component
	let operatorOptions = $derived(() => {
		if (selectedField) {
			return selectedField.allowedOperators.map(op => ({
				value: op,
				label: getOperatorDisplayName(op)
			}));
		}
		return [{ value: 'equals', label: getOperatorDisplayName('equals') }];
	});

	// Filter fields based on input
	let filteredFields = $state<TriggerField[]>([]);
	let fieldQuery = $state('');
	let showFieldSuggestions = $state(false);

	$effect(() => {
		if (fieldQuery.trim()) {
			filteredFields = availableFields.filter(field =>
				field.name.toLowerCase().includes(fieldQuery.toLowerCase()) ||
				field.path.toLowerCase().includes(fieldQuery.toLowerCase())
			);
		} else {
			filteredFields = availableFields.slice(0, 5); // Show first 5 by default
		}
	});

	function startCreating() {
		if (disabled) return;
		isCreating = true;
		newCondition = { field: '', operator: 'equals', value: '' };
		fieldQuery = '';
		showFieldSuggestions = true;
		setTimeout(() => {
			const input = fieldInputElement?.querySelector('input');
			input?.focus();
		}, 0);
	}

	function cancelCreating() {
		isCreating = false;
		showFieldSuggestions = false;
		newCondition = { field: '', operator: 'equals', value: '' };
		fieldQuery = '';
	}

	function selectField(field: TriggerField) {
		newCondition.field = field.path;
		fieldQuery = field.name;
		showFieldSuggestions = false;

		// Set default operator for this field type
		if (field.allowedOperators.length > 0) {
			newCondition.operator = field.allowedOperators[0];
		}

		setTimeout(() => {
			const input = valueInputElement?.querySelector('input');
			input?.focus();
		}, 0);
	}

	function handleFieldInput(value: string | number) {
		fieldQuery = String(value);
		showFieldSuggestions = true;

		// Clear field if query doesn't match any field name exactly
		const exactMatch = availableFields.find(f => f.name === fieldQuery);
		if (exactMatch) {
			newCondition.field = exactMatch.path;
		} else {
			newCondition.field = '';
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			cancelCreating();
		} else if (event.key === 'Enter' && canSubmit()) {
			submitCondition();
		}
	}

	function canSubmit() {
		return newCondition.field && newCondition.operator && newCondition.value.trim();
	}

	function submitCondition() {
		if (!canSubmit()) return;

		onadd?.({ ...newCondition });
		cancelCreating();
	}
</script>

<svelte:window on:keydown={handleKeydown} />

<div class="relative">
	{#if !isCreating}
		<!-- Add condition button -->
		<button
			type="button"
			onclick={startCreating}
			disabled={disabled}
			class="group flex items-center gap-2 px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed w-full text-left"
		>
			<div class="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300">
				<Icon src={Plus} micro />
			</div>
			<span>{placeholder}</span>
		</button>
	{:else}
		<!-- Inline condition creator -->
		<div class="flex gap-2 items-start p-2 border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800">
			<!-- Field input with autocomplete -->
			<div class="flex-1 relative" bind:this={fieldInputElement}>
				<Input
					type="text"
					bind:value={fieldQuery}
					oninput={handleFieldInput}
					onfocus={() => showFieldSuggestions = true}
					placeholder="Field..."
					size="sm"
				/>

				<!-- Field suggestions dropdown -->
				{#if showFieldSuggestions && filteredFields.length > 0}
					<div class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg z-50 max-h-48 overflow-y-auto">
						{#each filteredFields as field}
							<button
								type="button"
								onclick={() => selectField(field)}
								class="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
							>
								<div class="font-medium text-gray-900 dark:text-white">{field.name}</div>
								<div class="text-xs text-gray-500 dark:text-gray-400">{field.description}</div>
							</button>
						{/each}
					</div>
				{/if}
			</div>

			<!-- Operator select -->
			<div class="w-32">
				<Select
					bind:value={newCondition.operator}
					options={operatorOptions()}
					size="sm"
				/>
			</div>

			<!-- Value input -->
			<div class="flex-1" bind:this={valueInputElement}>
				<Input
					type="text"
					bind:value={newCondition.value}
					placeholder={selectedField?.example ? `e.g., ${selectedField.example}` : "Value..."}
					size="sm"
				/>
			</div>

			<!-- Action buttons -->
			<div class="flex gap-1">
				<Button
					variant="primary"
					size="sm"
					onclick={submitCondition}
					disabled={!canSubmit()}
				>
					Add
				</Button>
				<Button
					variant="ghost"
					size="sm"
					onclick={cancelCreating}
				>
					Cancel
				</Button>
			</div>
		</div>
	{/if}
</div>
