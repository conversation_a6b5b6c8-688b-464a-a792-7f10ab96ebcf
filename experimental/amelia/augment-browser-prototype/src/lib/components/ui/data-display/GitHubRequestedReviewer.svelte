<script lang="ts">
	interface Props {
		entity: any; // Raw API entity data
	}

	let { entity }: Props = $props();
</script>

<!-- GitHub-style reviewer list item -->
<div class="flex items-center justify-between py-[2px]">
	<div class="flex items-center gap-1">
		<!-- Avatar -->
		{#if entity.avatarUrl}
			<img
				src={entity.avatarUrl}
				alt={entity.login || 'User'}
				class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
			/>
		{:else}
			<div
				class="flex h-5 w-5 items-center justify-center rounded-full bg-slate-300 text-xs font-medium text-slate-700 dark:bg-slate-600 dark:text-slate-300"
			>
				{(entity.login || 'U').charAt(0).toUpperCase()}
			</div>
		{/if}

		<!-- Reviewer name -->
		<a
			class="text-xs font-medium text-slate-900 dark:text-slate-100"
			href={entity.htmlUrl}
			target="_blank"
			rel="noopener noreferrer"
		>
			{entity.login || 'Unknown'}
		</a>
	</div>

	<!-- Status indicator -->
	<div class="flex items-center gap-2">
		{#if entity.state === 'approved'}
			<!-- Green checkmark for approved -->
			<div class="flex h-4 w-4 items-center justify-center rounded-full bg-green-500">
				<svg class="h-3 w-3 text-white" fill="currentColor" viewBox="0 0 20 20">
					<path
						fill-rule="evenodd"
						d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
						clip-rule="evenodd"
					/>
				</svg>
			</div>
		{:else}
			<!-- Orange dot for pending/requested -->
			<div class="h-2 w-2 rounded-full bg-amber-600 dark:bg-amber-400"></div>
		{/if}
	</div>
</div>
