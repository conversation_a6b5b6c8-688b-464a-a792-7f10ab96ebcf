<script lang="ts">
	import {
		Icon,
		CheckCircle,
		XCircle,
		Clock,
		ExclamationTriangle,
		PlayCircle,
		EllipsisHorizontal,
		ArrowTopRightOnSquare
	} from 'svelte-hero-icons';
	import ReactiveRelativeTime from '../ReactiveRelativeTime.svelte';
	import type { GitHubPREntity } from '$lib/providers/github';
	import Button from '../navigation/Button.svelte';

	interface Props {
		entity: any; // Raw check suite data from GitHub API
		mainEntity: GitHubPREntity;
	}

	let { entity, mainEntity }: Props = $props();

	// Get the appropriate icon and styling based on check suite conclusion
	const checkSuiteConfig = $derived(() => {
		switch (entity.conclusion?.toLowerCase()) {
			case 'success':
				return {
					icon: CheckCircle,
					iconClass: 'text-green-600 dark:text-green-400',
					bgClass: 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700',
					textClass: 'text-green-800 dark:text-green-200',
					statusText: 'Successful',
					statusColor: 'text-green-600 dark:text-green-400'
				};
			case 'failure':
				return {
					icon: XCircle,
					iconClass: 'text-red-600 dark:text-red-400',
					bgClass: 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700',
					textClass: 'text-red-800 dark:text-red-200',
					statusText: 'Failing',
					statusColor: 'text-red-600 dark:text-red-400'
				};
			case 'cancelled':
				return {
					icon: XCircle,
					iconClass: 'text-gray-600 dark:text-gray-400',
					bgClass: 'bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-700',
					textClass: 'text-gray-800 dark:text-gray-200',
					statusText: 'Cancelled',
					statusColor: 'text-gray-600 dark:text-gray-400'
				};
			case 'timed_out':
				return {
					icon: Clock,
					iconClass: 'text-red-600 dark:text-red-400',
					bgClass: 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700',
					textClass: 'text-red-800 dark:text-red-200',
					statusText: 'Timed out',
					statusColor: 'text-red-600 dark:text-red-400'
				};
			case 'neutral':
			case 'skipped':
				return {
					icon: ExclamationTriangle,
					iconClass: 'text-gray-600 dark:text-gray-400',
					bgClass: 'bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-700',
					textClass: 'text-gray-800 dark:text-gray-200',
					statusText: 'Skipped',
					statusColor: 'text-gray-600 dark:text-gray-400'
				};
			case 'action_required':
				return {
					icon: ExclamationTriangle,
					iconClass: 'text-yellow-600 dark:text-yellow-400',
					bgClass: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700',
					textClass: 'text-yellow-800 dark:text-yellow-200',
					statusText: 'Action required',
					statusColor: 'text-yellow-600 dark:text-yellow-400'
				};
			case 'in_progress':
				return {
					icon: PlayCircle,
					iconClass: 'text-blue-600 dark:text-blue-400',
					bgClass: 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700',
					textClass: 'text-blue-800 dark:text-blue-200',
					statusText: 'In progress',
					statusColor: 'text-blue-600 dark:text-blue-400'
				};
			default:
				// For pending states
				return {
					icon: Clock,
					iconClass: 'text-yellow-600 dark:text-yellow-400',
					bgClass: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700',
					textClass: 'text-yellow-800 dark:text-yellow-200',
					statusText: 'Pending',
					statusColor: 'text-yellow-600 dark:text-yellow-400'
				};
		}
	});

	// Use app name as-is without reformatting
	const appName = $derived(entity.appName || 'GitHub Actions');

	// Format duration if available
	const duration = $derived(() => {
		if (entity.startedAt && entity.completedAt) {
			const start = new Date(entity.startedAt);
			const end = new Date(entity.completedAt);
			const diffMs = end.getTime() - start.getTime();
			const diffSeconds = Math.floor(diffMs / 1000);
			const diffMinutes = Math.floor(diffSeconds / 60);

			if (diffMinutes > 0) {
				return `${diffMinutes}m ${diffSeconds % 60}s`;
			}
			return `${diffSeconds}s`;
		}
		return null;
	});

	// Generate deep link URL with check_run_id if available
	const deepLinkUrl = $derived.by(() => {
		if (entity.checkRunsUrl) {
			// const [_, checkSuiteId] = entity.checkRunsUrl.match(/(?:check-suites)\/(\d+)/);
			// Convert PR URL to checks URL with check_run_id
			// e.g., https://github.com/augmentcode/augment/pull/29849
			// becomes https://github.com/augmentcode/augment/pull/29849/checks?check_run_id=46495296881
			return `${mainEntity?.url}/checks`;
		}
		return entity.htmlUrl;
	});
</script>

<!-- GitHub-style check suite display -->
<div class="group flex items-center gap-1.5 py-1 dark:bg-slate-800">
	<!-- Status Icon -->
	<div class="flex h-5 w-5 flex-shrink-0 items-center justify-center">
		<Icon src={checkSuiteConfig().icon} class="h-4 w-4 {checkSuiteConfig().iconClass}" micro />
	</div>

	<!-- Main Content -->
	<div class="min-w-0 flex-1">
		<div class="flex items-center gap-2 text-sm">
			<!-- App Name and Status -->
			<span class="font-medium text-slate-900 dark:text-slate-100">
				{appName}
			</span>
			<span class="text-sm {checkSuiteConfig().statusColor}">
				{checkSuiteConfig().statusText}
			</span>
			{#if duration()}
				<span class="text-sm text-slate-500 dark:text-slate-400">
					after {duration()}
				</span>
			{:else if entity.createdAt}
				<span class="text-sm text-slate-500 dark:text-slate-400">
					<ReactiveRelativeTime date={entity.createdAt} />
				</span>
			{/if}
		</div>

		<!-- Additional details if available -->
		{#if entity.description || entity.name}
			<div class="mt-1 text-sm text-slate-600 dark:text-slate-400">
				{entity.description || entity.name || ''}
			</div>
		{/if}
	</div>

	<!-- Actions -->
	<div class="">
		{#if deepLinkUrl}
			<Button
				variant="ghost-light"
				size="icon-sm"
				href={deepLinkUrl}
				target="_blank"
				rel="noopener noreferrer"
				title="View check suite details"
			>
				<Icon src={ArrowTopRightOnSquare} class="h-4 w-4" micro />
			</Button>
		{/if}
	</div>
</div>
