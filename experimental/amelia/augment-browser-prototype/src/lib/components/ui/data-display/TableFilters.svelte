<script lang="ts">
	import type { Snippet } from 'svelte';
	import { Icon, MagnifyingGlass, XMark } from 'svelte-hero-icons';
	import Input from './Input.svelte';
	import Select from './Select.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	interface FilterOption {
		value: string;
		label: string;
	}

	interface FilterConfig {
		key: string;
		label: string;
		type: 'select' | 'multiselect' | 'search';
		options?: FilterOption[];
		placeholder?: string;
	}

	interface Props {
		filters: FilterConfig[];
		values: Record<string, any>;
		onFilterChange: (key: string, value: any) => void;
		onClearAll?: () => void;
		searchPlaceholder?: string;
		additionalFilters?: Snippet;
		class?: string;
	}

	let {
		filters,
		values,
		onFilterChange,
		onClearAll,
		searchPlaceholder = "Search...",
		additionalFilters,
		class: className = ''
	}: Props = $props();

	// Check if any filters are active
	const hasActiveFilters = $derived(
		Object.values(values).some(value =>
			value !== undefined &&
			value !== null &&
			value !== '' &&
			(Array.isArray(value) ? value.length > 0 : true)
		)
	);

	function handleSearchChange(event: Event) {
		const target = event.target as HTMLInputElement;
		onFilterChange('search', target.value);
	}

	function handleSelectChange(key: string, value: string) {
		onFilterChange(key, value);
	}

	function clearAllFilters() {
		if (onClearAll) {
			onClearAll();
		} else {
			// Clear all filters individually
			filters.forEach(filter => {
				onFilterChange(filter.key, filter.type === 'multiselect' ? [] : '');
			});
			onFilterChange('search', '');
		}
	}
</script>

<div class=" {className}">
	<div class="py-1">
		<div class="flex flex-wrap items-center gap-4">
			<!-- Search -->
			<div class="flex-1">
				<div class="relative">
					<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<Icon src={MagnifyingGlass} class="h-4 w-4 text-slate-400" />
					</div>
					<Input
						type="text"
						placeholder={searchPlaceholder}
						value={values.search || ''}
						oninput={handleSearchChange}
					/>
				</div>
			</div>

			<!-- Filter Controls -->
			<div class="flex items-center gap-3">
				{#each filters as filter (filter.key)}
					{#if filter.type === 'select'}
						<div class="min-w-40">
							<Select
								value={values[filter.key] || ''}
								onchange={(value) => handleSelectChange(filter.key, value)}
								placeholder={filter.placeholder || `Select ${filter.label}`}
								options={[
									{ value: '', label: `All ${filter.label}` },
									...(filter.options || [])
								]}
							/>
						</div>
					{/if}
				{/each}

				{#if additionalFilters}
					{@render additionalFilters()}
				{/if}

				<!-- Clear Filters -->
				{#if hasActiveFilters}
					<Button
						variant="ghost"
						size="sm"
						icon={XMark}
						onclick={clearAllFilters}
					>
						Clear
					</Button>
				{/if}
			</div>
		</div>

		<!-- Active Filter Tags -->
		{#if hasActiveFilters}
			<div class="flex flex-wrap items-center gap-2 mt-3 pt-3 border-t border-slate-200 dark:border-slate-700">
				<span class="text-xs text-slate-600 dark:text-slate-400">Active filters:</span>

				{#if values.search}
					<span class="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-md">
						Search: "{values.search}"
						<button
							onclick={() => onFilterChange('search', '')}
							class="hover:bg-blue-200 dark:hover:bg-blue-800/50 rounded-sm p-0.5"
						>
							<Icon src={XMark} class="w-3 h-3" />
						</button>
					</span>
				{/if}

				{#each filters as filter (filter.key)}
					{#if values[filter.key] && values[filter.key] !== ''}
						{@const selectedOption = filter.options?.find(opt => opt.value === values[filter.key])}
						<span class="inline-flex items-center gap-1 px-2 py-1 bg-slate-100 dark:bg-slate-800 text-slate-800 dark:text-slate-200 text-xs rounded-md">
							{filter.label}: {selectedOption?.label || values[filter.key]}
							<button
								onclick={() => onFilterChange(filter.key, '')}
								class="hover:bg-slate-200 dark:hover:bg-slate-700 rounded-sm p-0.5"
							>
								<Icon src={XMark} class="w-3 h-3" />
							</button>
						</span>
					{/if}
				{/each}
			</div>
		{/if}
	</div>
</div>
