<script lang="ts">
	import Markdown from "./Markdown.svelte";

	interface Option {
		value: string;
		label: string;
	}

	interface Props {
		id?: string;
		value?: string | number | boolean;
		options: Option[];
		placeholder?: string;
		disabled?: boolean;
		required?: boolean;
		onchange?: (value: string) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
	}

	let {
		id,
		value = $bindable(),
		options = [],
		placeholder,
		disabled = false,
		required = false,
		onchange,
		onfocus,
		onblur
	}: Props = $props();

	// Generate unique ID if not provided
	const fieldId = id || `select-list-${Math.random().toString(36).substring(2, 11)}`;

	function handleOptionClick(optionValue: string) {
		if (disabled) return;

		value = optionValue;
		onchange?.(optionValue);
	}

	// Convert value to string for comparison
	const stringValue = $derived(String(value || ''));

	function handleKeydown(event: KeyboardEvent, optionValue: string) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleOptionClick(optionValue);
		}
	}
</script>

<div class="space-y-1">
	<!-- {#if placeholder && !stringValue}
		<div class="text-sm text-slate-500 dark:text-slate-400 px-3 py-2">
			{placeholder}
		</div>
	{/if} -->

	<div class="" role="listbox" aria-labelledby={fieldId}>
		{#each options as option, index}
		{@const isFirst = index === 0}
		{@const isLast = index === options.length - 1}
			<button
				type="button"
				class="relative w-full font-normal text-left px-3 py-2 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:z-10 {stringValue === option.value
					? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100 border border-blue-400 dark:border-blue-700 z-5'
					: 'bg-white dark:bg-slate-700 text-slate-600 dark:text-white border border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-600'} {isFirst ? 'rounded-t-md' : ''} {isLast ? 'mt-[-1px] rounded-b-md' : 'mt-[-1px]'} {disabled
					? 'opacity-50 cursor-not-allowed'
					: 'cursor-pointer'}"
				{disabled}
				role="option"
				aria-selected={stringValue === option.value}
				tabindex={disabled ? -1 : 0}
				onclick={() => handleOptionClick(option.value)}
				onkeydown={(event) => handleKeydown(event, option.value)}
				onfocus={onfocus}
				onblur={onblur}
			>
				<Markdown content={option.label} size="xs" hasNoPadding />
			</button>
		{/each}
	</div>
</div>
