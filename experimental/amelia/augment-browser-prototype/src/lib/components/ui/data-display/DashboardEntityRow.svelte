<script lang="ts">
	import type { Task } from '$lib/types';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';

	import { getRelativeTimeForStr } from '$lib/utils/time';
	import { Icon, ArrowTopRightOnSquare, User } from 'svelte-hero-icons';
	import Button from '../navigation/Button.svelte';
	import { getEntityTypeIcon } from '$lib/config/entity-types';
	import CustomIcon from '../visualization/CustomIcon.svelte';

	interface Props {
		entity: UnifiedEntity;
		linkedTask?: Task | null;
		onEntityClick?: (entity: UnifiedEntity) => void;
		onViewWorkflow?: (taskId: string) => void;
	}

	let { entity, linkedTask = null, onEntityClick, onViewWorkflow }: Props = $props();

	// Get entity type icon component
	const entityTypeIcon = getEntityTypeIcon(entity.providerId, entity.entityType);

	function handleClick() {
		if (onEntityClick) {
			onEntityClick(entity);
		}
	}

	function handleViewWorkflow(e: MouseEvent) {
		e.stopPropagation();
		if (linkedTask && onViewWorkflow) {
			onViewWorkflow(linkedTask.id);
		}
	}
</script>

<!-- Responsive layout: horizontal on larger screens, vertical on smaller screens -->
<div
	class="group w-full cursor-pointer transition-colors hover:bg-slate-50 dark:hover:bg-slate-800/50"
	onclick={handleClick}
	role="button"
	tabindex="0"
	onkeydown={(e) => e.key === 'Enter' && handleClick()}
>
	<!-- Desktop layout: horizontal flex - only show on very wide screens -->
	<div class="hidden px-6 2xl:flex">
		<!-- Entity Type Icon -->
		<div
			class="flex min-w-0 items-center justify-center border-b border-slate-100 px-3 py-4 dark:border-slate-800"
		>
			<div class="flex items-center gap-1.5">
				<CustomIcon
					icon={entity.providerId}
					size={18}
					class="flex-shrink-0 text-slate-600 dark:text-slate-400"
				/>
				<Icon
					src={entityTypeIcon}
					class="h-4 w-4 flex-shrink-0 text-slate-400 dark:text-slate-400"
					micro
				/>
			</div>
		</div>

		<!-- Title & Description -->
		<div
			class="min-w-0 flex-1 overflow-hidden border-b border-slate-100 px-4 py-4 dark:border-slate-800"
		>
			<div class="min-w-0">
				<div class="mb-1 flex items-center gap-2">
					<h4
						class="flex-1 truncate text-sm leading-tight font-semibold text-slate-900 transition-colors group-hover:text-slate-700 dark:text-white dark:group-hover:text-slate-200"
					>
						{entity.title}
					</h4>
					{#if entity.url}
						<a
							href={entity.url}
							target="_blank"
							rel="noopener noreferrer"
							class="flex-shrink-0 text-slate-400 opacity-0 transition-opacity group-hover:opacity-100 hover:text-slate-600 dark:hover:text-slate-300"
							onclick={(e) => e.stopPropagation()}
							title="Open in external site"
						>
							<Icon src={ArrowTopRightOnSquare} class="h-4 w-4" />
						</a>
					{/if}
				</div>

				{#if entity.description}
					<p
						class="line-clamp-1 min-w-0 flex-1 text-xs leading-relaxed text-slate-500 dark:text-slate-400"
					>
						{entity.description}
					</p>
				{/if}
			</div>
		</div>
		<!-- Assignee -->
		<div class="min-w-0 border-b border-slate-100 px-3 py-4 dark:border-slate-800">
			{#if entity.assignee}
				<div class="flex min-w-0 items-center gap-1.5 text-xs text-slate-600 dark:text-slate-400">
					<Icon src={User} class="h-3.5 w-3.5 flex-shrink-0" />
					<span class="truncate font-medium">{entity.assignee.name}</span>
				</div>
			{/if}
		</div>

		<!-- Labels -->
		<div class="min-w-0 border-b border-slate-100 px-3 py-4 dark:border-slate-800">
			{#if entity.labels?.length}
				<div class="flex min-w-0 items-center gap-1">
					<span
						class="inline-flex max-w-full items-center truncate rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
					>
						{entity.labels[0].name}
					</span>
					{#if entity.labels.length > 1}
						<span class="flex-shrink-0 text-xs font-medium text-slate-500 dark:text-slate-400"
							>+{entity.labels.length - 1}</span
						>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Updated Time -->
		<div class="min-w-0 border-b border-slate-100 px-3 py-4 text-right dark:border-slate-800">
			{#if entity.updatedAt}
				<span class="text-xs font-medium text-slate-500 dark:text-slate-400">
					{getRelativeTimeForStr(entity.updatedAt)}
				</span>
			{/if}
		</div>

		<!-- Status & Actions -->
		<div class="min-w-0 border-b border-slate-100 px-3 py-4 dark:border-slate-800">
			<div class="flex min-w-0 items-center justify-end gap-2">
				<!-- Status indicator -->
				{#if linkedTask}
					<div class="flex items-center gap-1.5">
						<div
							class="h-2 w-2 flex-shrink-0 rounded-full bg-green-500"
							title="Linked to workflow"
						></div>
						<Button
							variant="secondary"
							size="sm"
							class="flex-shrink-0 !border-green-200 !bg-green-50 px-2.5 py-1 text-xs font-medium !text-green-700 !shadow-none hover:!bg-green-100 dark:!border-green-800 dark:!bg-green-950/50 dark:!text-green-300 dark:hover:!bg-green-950/70"
							onclick={handleViewWorkflow}
						>
							View
						</Button>
					</div>
				{:else}
					<div class="flex items-center gap-5">
						<div
							class="h-2 w-2 flex-shrink-0 rounded-full bg-slate-300 dark:bg-slate-600"
							title="Not linked"
						></div>
						<!-- {#if onStartWork}
							<Button
								variant="outline"
								size="sm"
								class="text-xs px-2.5 py-1 flex-shrink-0 font-medium hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:border-blue-300 dark:hover:border-blue-700 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
								onclick={handleStartWork}
							>
								Assign to agent
							</Button>
						{/if} -->
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Mobile/Tablet layout: vertical stack - show on all screens except very wide -->
	<div class="border-b border-slate-100 px-4 py-3 2xl:hidden dark:border-slate-800">
		<!-- Header with icons and title -->
		<div class="mb-2 flex items-start gap-3">
			<div class="mt-0.5 flex flex-shrink-0 items-center gap-1.5">
				<CustomIcon icon={entity.providerId} size={16} class="text-slate-600 dark:text-slate-400" />
				<Icon src={entityTypeIcon} class="h-3.5 w-3.5 text-slate-400 dark:text-slate-400" micro />
			</div>
			<div class="min-w-0 flex-1">
				<div class="flex items-center gap-2">
					<h4
						class="flex-1 truncate text-sm leading-tight font-semibold text-slate-900 transition-colors group-hover:text-slate-700 dark:text-white dark:group-hover:text-slate-200"
					>
						{entity.title}
					</h4>
					{#if entity.url}
						<a
							href={entity.url}
							target="_blank"
							rel="noopener noreferrer"
							class="flex-shrink-0 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
							onclick={(e) => e.stopPropagation()}
							title="Open in external site"
						>
							<Icon src={ArrowTopRightOnSquare} class="h-4 w-4" />
						</a>
					{/if}
				</div>
				{#if entity.description}
					<p class="mt-1 line-clamp-2 text-xs leading-relaxed text-slate-500 dark:text-slate-400">
						{entity.description}
					</p>
				{/if}
			</div>
		</div>

		<!-- Metadata row -->
		<div class="mb-2 flex items-center justify-between gap-4">
			<div class="flex items-center gap-4 text-xs text-slate-500 dark:text-slate-400">
				{#if entity.assignee}
					<div class="flex items-center gap-1">
						<Icon src={User} class="h-3 w-3" />
						<span class="font-medium">{entity.assignee.name}</span>
					</div>
				{/if}
				{#if entity.updatedAt}
					<span class="font-medium">
						{getRelativeTimeForStr(entity.updatedAt)}
					</span>
				{/if}
			</div>
		</div>

		<!-- Labels and actions row -->
		<div class="flex items-center justify-between gap-3">
			<div class="flex min-w-0 flex-1 items-center gap-2">
				{#if entity.labels?.length}
					<span
						class="inline-flex items-center truncate rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700 dark:bg-blue-900/30 dark:text-blue-300"
					>
						{entity.labels[0].name}
					</span>
					{#if entity.labels.length > 1}
						<span class="text-xs font-medium text-slate-500 dark:text-slate-400"
							>+{entity.labels.length - 1}</span
						>
					{/if}
				{/if}
			</div>

			<!-- Status & Actions -->
			<div class="flex flex-shrink-0 items-center gap-2">
				{#if linkedTask}
					<div class="flex items-center gap-1.5">
						<div class="h-2 w-2 rounded-full bg-green-500" title="Linked to workflow"></div>
						<Button
							variant="secondary"
							size="sm"
							class="!border-green-200 !bg-green-50 px-2 py-1 text-xs font-medium !text-green-700 !shadow-none hover:!bg-green-100 dark:!border-green-800 dark:!bg-green-950/50 dark:!text-green-300 dark:hover:!bg-green-950/70"
							onclick={handleViewWorkflow}
						>
							View
						</Button>
					</div>
				{:else}
					<div class="flex items-center gap-2">
						<div
							class="h-2 w-2 rounded-full bg-slate-300 dark:bg-slate-600"
							title="Not linked"
						></div>
						<!-- {#if onStartWork}
							<Button
								variant="outline"
								size="sm"
								class="px-2 py-1 text-xs font-medium transition-colors hover:border-blue-300 hover:bg-blue-50 hover:text-blue-700 dark:hover:border-blue-700 dark:hover:bg-blue-950/30 dark:hover:text-blue-300"
								onclick={handleStartWork}
							>
								Assign to agent
							</Button>
						{/if} -->
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>
