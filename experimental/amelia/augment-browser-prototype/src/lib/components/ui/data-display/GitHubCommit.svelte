<script lang="ts">
	import { Icon, CodeBracket } from 'svelte-hero-icons';

	interface Props {
		entity: any; // Raw API entity data
	}

	let { entity }: Props = $props();

	const timeAgo = $derived(entity.createdAt ? new Date(entity.createdAt).toLocaleDateString() : '');

	const shortSha = $derived(entity.sha?.substring(0, 7) || entity.id?.substring(0, 7) || 'unknown');
</script>

<div
	class="flex gap-3 rounded-lg border border-green-200 bg-green-50 p-3 dark:border-green-700 dark:bg-green-900/20"
>
	<!-- Commit Icon -->
	<div
		class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-100 dark:bg-green-800"
	>
		<Icon src={CodeBracket} class="h-4 w-4 text-green-600 dark:text-green-400" />
	</div>

	<div class="min-w-0 flex-1">
		<!-- Commit Message -->
		<div class="mb-1 font-medium text-gray-900 dark:text-gray-100">
			{entity.commit?.message || entity.title || 'Commit'}
		</div>

		<!-- Commit Details -->
		<div class="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
			<span class="rounded bg-gray-100 px-2 py-1 font-mono dark:bg-gray-800">
				{shortSha}
			</span>
			{#if entity.commit?.author?.name || entity.author?.login}
				<span>by {entity.commit?.author?.name || entity.author?.login}</span>
			{/if}
			{#if timeAgo}
				<span>{timeAgo}</span>
			{/if}
		</div>

		<!-- Full commit message if different from title -->
		{#if entity.description && entity.description !== entity.title}
			<div class="mt-2 text-sm text-gray-700 dark:text-gray-300">
				<p class="line-clamp-3 whitespace-pre-wrap">
					{entity.description}
				</p>
			</div>
		{/if}

		<!-- Actions -->
		{#if entity.htmlUrl || entity.url}
			<div class="mt-2">
				<a
					href={entity.htmlUrl || entity.url}
					target="_blank"
					rel="noopener noreferrer"
					class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
				>
					View commit →
				</a>
			</div>
		{/if}
	</div>
</div>
