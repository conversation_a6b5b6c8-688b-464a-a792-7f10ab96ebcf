<script lang="ts">
	// Skeleton loader for MatchRow component - matches new sleek GitHub/Vercel-style layout
	// Add some randomness to make it look more natural
	let titleWidth = Math.random() > 0.5 ? 'w-3/4' : 'w-2/3';
	let authorWidth = Math.random() > 0.5 ? 'w-16' : 'w-20';
	let assigneeWidth = Math.random() > 0.5 ? 'w-14' : 'w-18';
</script>

<!-- Sleek GitHub/Vercel-style Row Skeleton -->
<div class="group @container border-b border-slate-200 dark:border-slate-700">
	<!-- Main Row -->
	<div class="flex items-center justify-between px-4 py-3">
		<!-- Left: Entity info -->
		<div class="flex min-w-0 flex-1 items-center gap-3">
			<!-- Title and metadata -->
			<div class="min-w-0 flex-1">
				<div class="mb-1 flex items-center gap-2">
					<!-- Title with shimmer effect -->
					<div
						class="relative {titleWidth} h-4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700"
					>
						<div
							class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
						></div>
					</div>

					<!-- Labels (hidden on smaller screens like real component) -->
					<div class="hidden gap-1 2xl:flex">
						<div class="relative h-5 w-12 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.6s;"
							></div>
						</div>
						<div class="relative h-5 w-16 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.7s;"
							></div>
						</div>
					</div>
				</div>

				<!-- Metadata line -->
				<div
					class="flex items-center gap-1.5 text-xs whitespace-nowrap text-slate-500 dark:text-slate-400"
				>
					<!-- Provider icon -->
					<div class="relative h-4 w-4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
						<div
							class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
							style="animation-delay: 0.2s;"
						></div>
					</div>

					<!-- Author -->
					<div class="flex items-center gap-1.5">
						<div
							class="relative h-4 w-4 overflow-hidden rounded-full bg-slate-200 dark:bg-slate-700"
						>
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.3s;"
							></div>
						</div>
						<div
							class="relative {authorWidth} hidden h-3 overflow-hidden rounded bg-slate-200 @[500px]:block dark:bg-slate-700"
						>
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.4s;"
							></div>
						</div>
					</div>

					<!-- Arrow -->
					<div class="relative h-3 w-3 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
						<div
							class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
							style="animation-delay: 0.45s;"
						></div>
					</div>

					<!-- Assignee -->
					<div class="flex items-center gap-1.5">
						<div
							class="relative h-4 w-4 overflow-hidden rounded-full bg-slate-200 dark:bg-slate-700"
						>
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.5s;"
							></div>
						</div>
						<div
							class="relative {assigneeWidth} hidden h-3 overflow-hidden rounded bg-slate-200 @[500px]:block dark:bg-slate-700"
						>
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.55s;"
							></div>
						</div>
					</div>

					<!-- Trigger name -->
					<div class="relative h-1 w-1 overflow-hidden rounded-full bg-slate-200 dark:bg-slate-700">
						<div
							class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
							style="animation-delay: 0.6s;"
						></div>
					</div>
					<div class="relative h-3 w-20 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
						<div
							class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
							style="animation-delay: 0.65s;"
						></div>
					</div>
				</div>
			</div>
		</div>

		<!-- Right: Action buttons -->
		<div class="ml-3 flex flex-shrink-0 items-center gap-2">
			<!-- Dismiss button skeleton -->
			<div class="relative h-8 w-8 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
					style="animation-delay: 0.75s;"
				></div>
			</div>

			<!-- Main action button skeleton -->
			<div class="relative h-7 w-24 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
					style="animation-delay: 0.8s;"
				></div>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
