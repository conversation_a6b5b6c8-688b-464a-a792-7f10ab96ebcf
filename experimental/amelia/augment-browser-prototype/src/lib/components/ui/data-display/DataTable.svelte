<script lang="ts">
	import type { Snippet } from 'svelte';
	import { Icon, ChevronUp, ChevronDown } from 'svelte-hero-icons';

	interface Column {
		key: string;
		label: string;
		sortable?: boolean;
		width?: string;
		align?: 'left' | 'center' | 'right';
	}

	interface Props {
		columns: Column[];
		data: any[];
		sortKey?: string;
		sortDirection?: 'asc' | 'desc';
		onSort?: (key: string, direction: 'asc' | 'desc') => void;
		rowComponent: Snippet<[any, number]>;
		emptyState?: Snippet;
		loading?: boolean;
		class?: string;
	}

	let {
		columns,
		data,
		sortKey,
		sortDirection = 'asc',
		onSort,
		rowComponent,
		emptyState,
		loading = false,
		class: className = ''
	}: Props = $props();

	function handleSort(column: Column) {
		if (!column.sortable || !onSort) return;

		const newDirection = sortKey === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
		onSort(column.key, newDirection);
	}

	function getSortIcon(column: Column) {
		if (!column.sortable) return null;
		if (sortKey !== column.key) return null;
		return sortDirection === 'asc' ? ChevronUp : ChevronDown;
	}
</script>

<div class="grid gap-4 px-6 py-3 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden {className}"
style="grid-template-columns: {columns.map(c => c.width || '1fr').join(' ')}">


	<!-- Table Body -->
		{#if loading}
			<!-- Loading State -->
			<div class="px-6 py-12 text-center" style="grid-column: span {columns.length};">
				<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-900 dark:border-slate-100 mx-auto"></div>
				<p class="text-sm text-slate-600 dark:text-slate-400 mt-3">Loading...</p>
			</div>
		{:else if data.length === 0}
			<!-- Empty State -->
			<div class="px-6 py-12" style="grid-column: span {columns.length};">
				{#if emptyState}
					{@render emptyState()}
				{:else}
					<div class="text-center">
						<p class="text-sm text-slate-600 dark:text-slate-400">No data available</p>
					</div>
				{/if}
			</div>
		{:else}
			<!-- Table Header -->
			{#each columns as column (column.key)}
				<button
					class="flex items-center gap-2 text-left text-xs font-medium text-slate-600 dark:text-slate-400 uppercase tracking-wider
						{column.sortable ? 'hover:text-slate-900 dark:hover:text-slate-200 cursor-pointer' : 'cursor-default'}
						{column.align === 'center' ? 'justify-center' : column.align === 'right' ? 'justify-end' : 'justify-start'}"
					onclick={() => handleSort(column)}
					disabled={!column.sortable}
				>
					<span>{column.label}</span>
					{#if getSortIcon(column)}
						<Icon src={getSortIcon(column)} class="w-3 h-3" />
					{/if}
				</button>
			{/each}
			<!-- Data Rows -->
			{#each data as item, index (item.id || index)}
					{@render rowComponent(item, index)}
			{/each}
		{/if}
</div>
