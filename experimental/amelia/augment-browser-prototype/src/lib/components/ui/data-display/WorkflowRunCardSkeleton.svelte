<script lang="ts">
	// Skeleton loader for WorkflowRunCard
	// random height between 200-300
	let height = Math.floor(Math.random() * (300 - 200) + 200);
</script>

<div class="w-full bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-xl overflow-hidden animate-pulse" style="height: {height}px;">
	<!-- Header -->
	<div class="flex items-center gap-2 px-3 py-2 bg-slate-50 dark:bg-slate-800/50 rounded-t-xl border-b border-slate-200 dark:border-slate-700">
		<!-- Provider icon skeleton -->
		<div class="w-4 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
		<!-- Entity type skeleton -->
		<div class="w-20 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
	</div>

	<!-- Content -->
	<div class="p-4 space-y-3">
		<!-- Title skeleton -->
		<div class="w-3/4 h-5 bg-slate-300 dark:bg-slate-600 rounded"></div>

		<!-- Description skeleton -->
		<div class="space-y-2">
			<div class="w-full h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
			<div class="w-2/3 h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
		</div>

		<!-- Metadata row skeleton -->
		<div class="flex items-center justify-between pt-2">
			<div class="flex items-center gap-2">
				<!-- Status indicator skeleton -->
				<div class="w-2 h-2 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
				<!-- Status text skeleton -->
				<div class="w-16 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
			</div>
			<!-- Time skeleton -->
			<div class="w-20 h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
		</div>

		<!-- Action buttons skeleton -->
		<div class="flex gap-2 pt-2">
			<div class="w-20 h-8 bg-slate-300 dark:bg-slate-600 rounded-md"></div>
			<div class="w-16 h-8 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
		</div>
	</div>
</div>
