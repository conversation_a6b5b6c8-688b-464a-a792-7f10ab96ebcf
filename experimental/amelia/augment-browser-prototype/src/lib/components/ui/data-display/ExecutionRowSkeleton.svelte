<script lang="ts">
	// Skeleton loader for execution rows - matches current ExecutionRow layout
	// Add some randomness to make it look more natural
	let titleWidth = Math.random() > 0.5 ? 'w-2/3' : 'w-3/5';
	let statusWidth = Math.random() > 0.5 ? 'w-24' : 'w-28';
	let triggerWidth = Math.random() > 0.5 ? 'w-16' : 'w-20';
</script>

<!-- Execution Row Skeleton -->
<div class="group relative border-b border-slate-200 dark:border-slate-700">
	<!-- Main Content -->
	<div class="p-4">
		<!-- Title with execution context -->
		<div class="flex items-start gap-3">
			<!-- Avatar (larger to match AuggieAvatar size) -->
			<div class="relative w-9 h-9 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
				<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.2s;"></div>
			</div>

			<div class="flex-1 min-w-0 gap-1 flex flex-col items-start text-xs text-slate-500 dark:text-slate-400">
				<!-- Title -->
				<div class="relative {titleWidth} h-4 mb-1 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
					<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.3s;"></div>
				</div>

				<!-- Trigger subtitle -->
				<div class="relative w-20 h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
					<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.4s;"></div>
				</div>

				<!-- Execution Details -->
				<div class="flex items-center justify-between w-full mt-2">
					<!-- Left side: timing and status info -->
					<div class="flex items-center gap-1.5 text-xs">
						<!-- Started time -->
						<div class="relative {statusWidth} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.5s;"></div>
						</div>

						<!-- Separator dot -->
						<div class="relative w-1 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.6s;"></div>
						</div>

						<!-- Trigger name -->
						<div class="relative {triggerWidth} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.7s;"></div>
						</div>
					</div>

					<!-- Right side: action button -->
					<div class="relative w-20 h-6 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.8s;"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
