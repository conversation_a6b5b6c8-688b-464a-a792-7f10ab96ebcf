<script lang="ts">
	// Skeleton loader for RemoteAgentRow component - matches the current layout
	// Add some randomness to make it look more natural
	let titleWidth = Math.random() > 0.5 ? 'w-3/4' : 'w-2/3';
	let metadataWidth = Math.random() > 0.5 ? 'w-1/2' : 'w-2/5';
	let authorWidth = Math.random() > 0.5 ? 'w-16' : 'w-20';
</script>

<!-- Remote Agent Row Skeleton -->
<div class="group/row relative w-full border-b border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-900">
	<!-- Agent Row -->
	<div class="flex w-full items-start gap-3 p-3 pb-1">
		<!-- Content -->
		<div class="min-w-0 flex-1">
			<!-- Title -->
			<div class="relative mb-0.5 flex items-start justify-between gap-1">
				<div class="min-w-0 flex-1">
					<!-- Title with shimmer effect -->
					<div class="relative {titleWidth} h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden mb-1">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
					</div>
					<!-- Subtitle line -->
					<div class="relative w-1/2 h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.1s;"></div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Entity Metadata -->
	<div class="mt-2 flex items-center gap-1.5 px-3 pb-3">
		<!-- Provider icon -->
		<div class="relative w-3.5 h-3.5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.2s;"></div>
		</div>

		<!-- Author avatar -->
		<div class="relative w-3.5 h-3.5 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.3s;"></div>
		</div>

		<!-- Author name -->
		<div class="relative {authorWidth} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.4s;"></div>
		</div>

		<!-- Separator dot -->
		<div class="w-1 h-1 bg-slate-300 dark:bg-slate-600 rounded-full"></div>

		<!-- Trigger name -->
		<div class="relative {metadataWidth} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.5s;"></div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
