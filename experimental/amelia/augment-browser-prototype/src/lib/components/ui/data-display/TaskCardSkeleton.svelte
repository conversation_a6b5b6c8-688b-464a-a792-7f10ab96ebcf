<script lang="ts">
	// Skeleton loader for TaskCard component
	// Add some randomness to make it look more natural
	let titleWidth = Math.random() > 0.5 ? 'w-3/4' : 'w-2/3';
	let descriptionWidth = Math.random() > 0.5 ? 'w-5/6' : 'w-4/5';
	let metadataWidth = Math.random() > 0.5 ? 'w-1/2' : 'w-2/5';
</script>

<!-- Task Card Skeleton -->
<div class="bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-4 animate-pulse">
	<div class="flex items-center gap-3">
		<!-- Provider icon -->
		<div class="relative w-5 h-5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
		</div>
		<!-- Title -->
		<div class="flex-1">
			<div class="relative {titleWidth} h-5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden mb-2">
				<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
			</div>
			<div class="relative {descriptionWidth} h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
				<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.1s;"></div>
			</div>
		</div>
		<!-- External link icon -->
		<div class="relative w-4 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.2s;"></div>
		</div>
	</div>

	<!-- Metadata -->
	<div class="mt-3 flex items-center gap-2">
		<div class="relative {metadataWidth} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.3s;"></div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
