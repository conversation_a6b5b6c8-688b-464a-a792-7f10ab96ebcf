<script lang="ts">
	interface Props {
		entity: any; // Raw API entity data
	}

	let { entity }: Props = $props();
</script>

<!-- GitHub-style compact review list item -->
<div class="flex items-center justify-between py-1">
	<div class="flex items-center gap-2">
		<!-- Avatar -->
		{#if entity.author?.avatar}
			<img
				src={entity.author.avatar}
				alt={entity.author.name || entity.author.login || 'User'}
				class="h-5 w-5 rounded-full"
			/>
		{:else}
			<div
				class="flex h-5 w-5 items-center justify-center rounded-full bg-slate-300 text-xs font-medium text-slate-700 dark:bg-slate-600 dark:text-slate-300"
			>
				{(entity.author?.name || entity.author?.login || 'U').charAt(0).toUpperCase()}
			</div>
		{/if}

		<!-- Reviewer name -->
		<span class="text-sm text-slate-900 dark:text-slate-100">
			{entity.author?.login || 'Unknown'}
		</span>
	</div>

	<!-- Status indicator -->
	<div class="flex items-center gap-1">
		{#if entity.state === 'approved' || entity.state === 'APPROVED'}
			<!-- Green checkmark for approved -->
			<svg class="h-4 w-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
				<path
					fill-rule="evenodd"
					d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
					clip-rule="evenodd"
				/>
			</svg>
		{:else if entity.state === 'changes_requested' || entity.state === 'CHANGES_REQUESTED'}
			<!-- Red X for changes requested -->
			<svg class="h-4 w-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
				<path
					fill-rule="evenodd"
					d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
					clip-rule="evenodd"
				/>
			</svg>
		{:else if entity.state === 'commented' || entity.state === 'COMMENTED'}
			<!-- Shield icon for commented -->
			<svg class="h-4 w-4 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
				<path
					fill-rule="evenodd"
					d="M10 1L5 3v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V3l-5-2z"
					clip-rule="evenodd"
				/>
			</svg>
		{:else}
			<!-- Orange dot for pending -->
			<div class="h-2 w-2 rounded-full bg-orange-400"></div>
		{/if}
	</div>
</div>
