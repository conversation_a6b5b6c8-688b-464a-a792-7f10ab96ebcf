<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { RemoteAgentStatus, RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { deleteAgents } from '$lib/stores/data-operations.svelte';
	import { shouldShowTriggerSkeleton, getTrigger } from '$lib/stores/global-state.svelte';
	import { modalState } from '$lib/stores/modal';
	import { goto } from '$app/navigation';

	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import type { GitHubRepo } from '$lib/api/github-api';
	import {
		getUniqueNormalizedRepos,
		getNormalizedRepoCounts,
		agentMatchesNormalizedRepo,
		extractAndNormalizeRepoFromUrl
	} from '$lib/utils/repository-normalization';
	import {
		ArchiveBoxXMark,
		BellAlert,
		CheckCircle,
		ChevronLeft,
		ChevronRight,
		Icon,
		PaperAirplane,
		Play,
		Plus
	} from 'svelte-hero-icons';
	import { quintOut } from 'svelte/easing';
	import { crossfade, fly, slide } from 'svelte/transition';
	import { get } from 'svelte/store';
	import Textarea from '../forms/Textarea.svelte';
	import TriggerCreateDrawer from '../overlays/TriggerCreateDrawer.svelte';
	import ExpandableSection from '../widgets/ExpandableSection.svelte';
	import MatchRow from './MatchRow.svelte';
	import MatchRowSkeleton from './MatchRowSkeleton.svelte';
	import RemoteAgentRow from './RemoteAgentRow.svelte';
	import RemoteAgentRowSkeleton from './RemoteAgentRowSkeleton.svelte';
	import TriggerDrawer from './TriggerDrawer.svelte';
	import TriggerList from './TriggerList.svelte';
	import Tooltip from '../overlays/Tooltip.svelte';

	interface Props {
		agents: CleanRemoteAgent[];
		isLoading: boolean;
		filters: {
			search: string;
			status: string;
			assignee: string;
			hasAgent: string;
			repository: string;
		};
		onAgentClick: (agent: CleanRemoteAgent) => void;
		onFilterChange: (key: string, value: string) => void;
		onClearFilters: () => void;
	}

	let { agents, isLoading, filters, onAgentClick, onFilterChange }: Props = $props();

	// Entity details drawer state

	// State for textarea
	let textareaValue = $state('');
	let selectedTriggerForDrawer = $state<{
		triggerId: string;
	} | null>(null);

	// Selected trigger data
	let selectedTriggerData = $derived(
		selectedTriggerForDrawer ? getTrigger(selectedTriggerForDrawer.triggerId) : null
	);

	// Convert NormalizedTrigger to TriggerResult format for the edit drawer
	function convertToTriggerResult(normalizedTrigger: NormalizedTrigger): {
		id: string;
		configuration: any;
		enabled: boolean;
	} {
		return {
			id: normalizedTrigger.id,
			configuration: normalizedTrigger.configuration,
			enabled: normalizedTrigger.isEnabled
		};
	}

	let configTrigger = $state<{ id: string; configuration: any; enabled: boolean } | null>(null);
	let shouldShowCreateTriggerDrawer = $state(false);

	// State for show dismissed toggle
	let showDismissed = $state(false);

	// State for showing all repositories
	let showAllRepos = $state(false);

	// State for sidebar collapse
	let sidebarCollapsed = $state(false);

	// Crossfade for smooth agent transitions between columns
	const [send, receive] = crossfade({
		duration: 400,
		easing: quintOut,
		fallback: (node) => {
			return slide(node, { duration: 400, easing: quintOut, delay: 0 });
		}
	});

	// Helper function to extract GitHub repository name from URL
	function getGitHubRepo(url: string | undefined): string {
		if (!url) return '';
		const match = url.match(/github\.com\/([^/]+\/[^/]+)/);
		return match ? match[1] : '';
	}

	// Repository calculations
	const uniqueRepositories = $derived(
		getUniqueNormalizedRepos(agents, (agent) => getGitHubRepo(agent.githubUrl))
	);

	const repoCounts = $derived(
		getNormalizedRepoCounts(agents, (agent) => getGitHubRepo(agent.githubUrl))
	);

	const displayedRepositories = $derived.by(() => {
		const repos = uniqueRepositories;
		if (showAllRepos || repos.length <= 3) {
			return repos;
		} else {
			return repos.slice(0, 2);
		}
	});

	// Handler for entity clicks
	function handleEntityClick(
		entity: UnifiedEntity & { triggerId: string; triggerName: string },
		trigger?: NormalizedTrigger
	) {
		// Navigate to agent creation with preselected entity
		const params = new URLSearchParams();
		if (entity?.id) params.set('entity', entity.id);
		if (trigger?.id) params.set('trigger', trigger.id);

		const queryString = params.toString();
		const url = queryString ? `/agents/create?${queryString}` : '/agents/create';
		goto(url);
	}

	// Helper function to create GitHubRepo object from repository filter string
	function createRepoFromFilterString(repoString: string): GitHubRepo {
		// Use the normalized repository name (removes .git suffix)
		const normalizedRepo = extractAndNormalizeRepoFromUrl(repoString) || repoString;
		const [owner, name] = normalizedRepo.split('/');
		return {
			owner,
			name,
			html_url: `https://github.com/${normalizedRepo}`,
			created_at: '',
			updated_at: '',
			default_branch: 'main',
			description: undefined,
			private: false,
			fork: false,
			archived: false,
			disabled: false,
			language: undefined,
			stargazers_count: 0,
			forks_count: 0
		};
	}

	// Handler for textarea click
	function handleTextareaClick() {
		// Small delay to allow the send animation to start
		setTimeout(() => {
			const params = new URLSearchParams();

			// If there's a repository filter active, add it to the URL
			if (filters.repository) {
				params.set('repository', filters.repository);
			}

			const queryString = params.toString();
			const url = queryString ? `/agents/create?${queryString}` : '/agents/create';
			goto(url);
		}, 50);
	}

	// Categorize agents into sections (unfiltered for column visibility)
	const unfilteredCategorizedAgents = $derived.by(() => {
		// Categorize agents without any filters to determine column visibility
		const readyForReview: CleanRemoteAgent[] = [];
		const working: CleanRemoteAgent[] = [];
		const idle: CleanRemoteAgent[] = [];

		agents.forEach((agent) => {
			// Ready for Review: has updates and is idle
			if (agent.hasUpdates && agent.status === RemoteAgentStatus.AGENT_IDLE) {
				readyForReview.push(agent);
			}
			// Working: actively running, starting, or pending
			else if (
				[
					RemoteAgentStatus.AGENT_RUNNING,
					RemoteAgentStatus.AGENT_STARTING,
					RemoteAgentStatus.AGENT_PENDING
				].includes(agent.status)
			) {
				working.push(agent);
			}
			// Idle
			else {
				idle.push(agent);
			}
		});

		return {
			readyForReview,
			working,
			idle
		};
	});

	// Categorize agents into sections (filtered for display)
	const categorizedAgents = $derived.by(() => {
		let filteredAgents = agents;

		// Apply search filter
		if (filters.search) {
			const searchLower = filters.search.toLowerCase();
			filteredAgents = filteredAgents.filter((agent) =>
				agent.title?.toLowerCase().includes(searchLower)
			);
		}

		// Apply status filter
		if (filters.status) {
			filteredAgents = filteredAgents.filter((agent) => agent.status.toString() === filters.status);
		}

		// Apply repository filter (normalized)
		if (filters.repository) {
			filteredAgents = filteredAgents.filter((agent) => {
				return agentMatchesNormalizedRepo(
					agent,
					(a) => getGitHubRepo(a.githubUrl),
					filters.repository
				);
			});
		}

		// Categorize agents
		const working: CleanRemoteAgent[] = [];
		const readyForReview: CleanRemoteAgent[] = [];
		const idle: CleanRemoteAgent[] = [];

		filteredAgents.forEach((agent) => {
			// Ready for Review: has updates and is idle
			if (agent.hasUpdates && agent.status === RemoteAgentStatus.AGENT_IDLE) {
				readyForReview.push(agent);
			}
			// Working: actively running, starting, or pending
			else if (
				[
					RemoteAgentStatus.AGENT_RUNNING,
					RemoteAgentStatus.AGENT_STARTING,
					RemoteAgentStatus.AGENT_PENDING
				].includes(agent.status) ||
				agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING
			) {
				working.push(agent);
			}
			// Idle
			else {
				idle.push(agent);
			}
		});

		// Sort each category by updatedAt (most recent first)
		const sortByUpdated = (a: CleanRemoteAgent, b: CleanRemoteAgent) =>
			new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();

		return {
			readyForReview: readyForReview.sort(sortByUpdated),
			working: working.sort(sortByUpdated),
			idle: idle.sort(sortByUpdated)
		};
	});

	function openTriggerDrawer(trigger: NormalizedTrigger) {
		selectedTriggerForDrawer = { triggerId: trigger.id };
	}

	function closeTriggerDrawer() {
		selectedTriggerForDrawer = null;
	}

	// Handler for "Add and delete all" button in Done column
	function handleDeleteAllDone() {
		if (confirm('Are you sure you want to delete all done agents?')) {
			deleteAgents(categorizedAgents.idle.map((agent) => agent.id));
		}
	}

	// Handler for sidebar toggle
	function toggleSidebar() {
		sidebarCollapsed = !sidebarCollapsed;
	}
</script>

<div class="relative flex h-full">
	<!-- Left Column: Intro, Metrics, Create Input -->
	<div
		class="flex h-full flex-shrink-0 flex-col overflow-hidden border-r border-slate-200 bg-white transition-all duration-300 dark:border-slate-700 dark:bg-slate-900 {sidebarCollapsed
			? 'w-0 -translate-x-full'
			: 'w-92'}"
	>
		<div class="flex h-full w-92 flex-col">
			<!-- Header -->
			<div class="p-6 pb-5">
				<!-- Expanded Header -->
				<div class="flex items-start justify-between">
					<div class="flex-1">
						<h1
							class="mb-2 text-2xl font-semibold whitespace-nowrap text-slate-900 dark:text-white"
						>
							Remote Agents
						</h1>
						<p class="text-sm text-slate-600 dark:text-slate-400">
							Manage and monitor your agents {#if uniqueRepositories.length > 1}from...{/if}
						</p>
					</div>
				</div>

				<!-- <div class="pt-5">
				<Input
					icon={MagnifyingGlass}
					type="search"
					placeholder="Search agents and entities..."
					value={filters.search}
					oninput={(value) => onFilterChange('search', String(value))}
					inputClass="pl-8"
				/>
			</div> -->

				{#if !sidebarCollapsed}
					<!-- Repository Filter Pills -->
					{#if uniqueRepositories.length > 1}
						<div class="pt-2">
							<!-- <div class="mb-1">
							<span class="text-xs text-slate-600 dark:text-slate-400">Show agents from...</span>
						</div> -->
							<div class="flex flex-col">
								<!-- All Repositories pill -->
								<Button
									variant="ghost"
									size="xs"
									onclick={() => onFilterChange('repository', '')}
									class="text-left focus:!ring-0 {filters.repository === ''
										? 'bg-slate-100 dark:bg-slate-700'
										: ''}"
								>
									All repos
								</Button>
								<!-- Individual repository pills -->
								{#each displayedRepositories as repo}
									<Button
										variant="ghost"
										size="xs"
										onclick={() => {
											onFilterChange('repository', filters.repository === repo ? '' : repo);
										}}
										class="text-left focus:!ring-0 {filters.repository === repo
											? 'bg-slate-100 dark:bg-slate-700'
											: ''}"
									>
										<div class="flex">
											<div class="flex-1">
												{repo}
											</div>
											<span
												class="ml-1 flex h-4 w-4 items-center justify-center rounded-full bg-slate-100 text-[0.66rem] font-medium text-slate-600 dark:bg-slate-700 dark:text-slate-400"
											>
												{repoCounts[repo]}
											</span>
										</div>
									</Button>
								{/each}
								<!-- Show more/less button -->
								{#if uniqueRepositories.length > displayedRepositories.length}
									<Button
										variant="ghost"
										size="xs"
										onclick={() => {
											showAllRepos = !showAllRepos;
										}}
										class="text-left font-normal text-slate-500 focus:!ring-0 dark:text-slate-400"
									>
										{showAllRepos ? 'Show less' : `+ Show ${uniqueRepositories.length - 2} more`}
									</Button>
								{/if}
							</div>
						</div>
					{/if}
				{/if}
			</div>

			<!-- Metrics -->
			<!-- {#if totalAgents > 0}
			<div class="p-6 border-b border-slate-200 dark:border-slate-700">
				<div class="grid grid-cols-2 gap-4">
					<div class="bg-slate-50 dark:bg-slate-700 rounded-lg p-3">
						<div class="text-2xl font-bold text-slate-900 dark:text-white">{metrics().total}</div>
						<div class="text-xs text-slate-600 dark:text-slate-400">Total Agents</div>
					</div>
					<div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
						<div class="text-2xl font-bold text-green-700 dark:text-green-400">{metrics().successRate}%</div>
						<div class="text-xs text-green-600 dark:text-green-400">Success Rate</div>
					</div>
					<div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
						<div class="text-2xl font-bold text-blue-700 dark:text-blue-400">{metrics().avgRuntimeHours}h</div>
						<div class="text-xs text-blue-600 dark:text-blue-400">Avg Runtime</div>
					</div>
					<div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
						<div class="text-2xl font-bold text-purple-700 dark:text-purple-400">{metrics().linkedTasks}</div>
						<div class="text-xs text-purple-600 dark:text-purple-400">Linked Tasks</div>
					</div>
				</div>
			</div>
		{/if} -->

			<!-- Create Agent Textarea -->
			<div class="border-t border-slate-200 px-6 pt-5 dark:border-slate-700">
				<!-- <div class="mb-2">
					<p class="text-xs text-slate-600 dark:text-slate-400">
						Create a new agent to get some work done.
					</p>
				</div> -->
				<div class="mb-6 w-full">
					{#if $modalState.type !== 'agent-creation'}
						<div class="relative">
							<Textarea
								bind:value={textareaValue}
								placeholder="Create a new agent to get some work done."
								class="z-50 h-full w-full cursor-pointer"
								textareaClass="h-full cursor-pointer focus:!ring-0"
								rows={3}
								resize="none"
								onclick={handleTextareaClick}
								readonly
							/>
							<!-- fake send button -->
							<div class="absolute right-2 bottom-2 opacity-20">
								<Button
									type="button"
									variant="primary"
									size="icon-sm"
									disabled={!textareaValue.trim()}
									onclick={handleTextareaClick}
									class="shadow-sm"
									aria-label="Create agent"
									icon={PaperAirplane}
								/>
							</div>
						</div>
					{/if}
				</div>
			</div>

			{#if !sidebarCollapsed}
				<!-- Triggers and Entities -->
				<div class="stable-transition-container flex-1 overflow-y-auto">
					{#if selectedTriggerForDrawer}
						<div class="stable-transition-item h-full w-full min-w-0">
							<!-- Drawer View -->
							<TriggerDrawer
								triggerId={selectedTriggerForDrawer?.triggerId}
								isLoading={$shouldShowTriggerSkeleton}
								onBack={closeTriggerDrawer}
								onEntityClick={handleEntityClick}
								openTriggerEditDrawer={() => {
									const triggerData = selectedTriggerData;
									if (!triggerData) return;
									const triggerValue = get(triggerData);
									if (!triggerValue?.data) return;
									configTrigger = convertToTriggerResult(triggerValue.data);
									shouldShowCreateTriggerDrawer = true;
								}}
							/>
						</div>
					{:else}
						<!-- Main List View -->
						<div
							class="stable-transition-item px-0 pb-20"
							in:fly={{ x: -360, duration: 300 }}
							out:fly={{ x: -360, duration: 300 }}
						>
							<TriggerList
								{showDismissed}
								onTriggerClick={openTriggerDrawer}
								onCreateTrigger={() => {
									shouldShowCreateTriggerDrawer = true;
								}}
							/>
						</div>
					{/if}
				</div>
			{:else}
				<!-- Collapsed Sidebar Content -->
				<div class="flex-1 overflow-y-auto">
					<div class="flex flex-col items-center space-y-3 px-2 py-4">
						<!-- Inbox Icon -->
						<Button
							variant="ghost"
							size="icon-sm"
							onclick={() => {
								sidebarCollapsed = false;
							}}
							class="opacity-60 hover:opacity-100"
							aria-label="View inbox"
							icon={BellAlert}
						/>
						<!-- Working Icon -->
						{#if unfilteredCategorizedAgents.working.length > 0}
							<div class="relative">
								<Button
									variant="ghost"
									size="icon-sm"
									class="opacity-60 hover:opacity-100"
									aria-label="Working agents"
									icon={Play}
								/>
								<span
									class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-blue-500 text-[0.6rem] font-medium text-white"
								>
									{unfilteredCategorizedAgents.working.length}
								</span>
							</div>
						{/if}
						<!-- Ready for Review Icon -->
						{#if unfilteredCategorizedAgents.readyForReview.length > 0}
							<div class="relative">
								<Button
									variant="ghost"
									size="icon-sm"
									class="opacity-60 hover:opacity-100"
									aria-label="Ready for review"
									icon={CheckCircle}
								/>
								<span
									class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-green-500 text-[0.6rem] font-medium text-white"
								>
									{unfilteredCategorizedAgents.readyForReview.length}
								</span>
							</div>
						{/if}
					</div>
				</div>
			{/if}
			<!-- save space for the collase/expand button -->
			<div class="h-12 w-full flex-0"></div>
		</div>
	</div>

	<!-- Collapsed Header -->
	<div
		class="group/toggle absolute bottom-0 left-0 z-10 flex h-12 items-center gap-1 border-t border-r border-slate-200 bg-white transition-all dark:border-slate-700 dark:bg-slate-900 {sidebarCollapsed
			? 'max-w-20 min-w-0'
			: 'max-w-[50em] min-w-92'}"
	>
		{#if sidebarCollapsed}
			<Tooltip text="Create new Agent" position="top" delay={0}>
				<Button class="ml-2 !px-3" size="icon-sm" icon={Plus} onclick={handleTextareaClick} />
			</Tooltip>

			<Tooltip text="Expand sidebar" position="top" delay={0}>
				<Button
					variant="ghost"
					size="icon-sm"
					class="mr-2 !px-3"
					onclick={toggleSidebar}
					aria-label="Expand sidebar"
					icon={ChevronRight}
				/>
			</Tooltip>
		{:else}
			<Button
				variant="ghost"
				size="sm"
				onclick={toggleSidebar}
				class="w-full justify-start rounded-none py-3"
				aria-label="Collapse sidebar"
				icon={ChevronLeft}
			>
				<div class="flex-none text-left">Collapse sidebar</div>
			</Button>
		{/if}
	</div>

	<!-- Right Side: Scrollable Columns -->
	<div class="flex flex-1">
		{#each [{ // 	label: 'Inbox',
				// 	description: 'Matching entities from all triggers. Create a new trigger if you want different types of entities to show up here.',
				// 	items: groupedEntities,
				// 	unfilteredItems: groupedEntities, // Entities are already filtered by search, so use same for visibility
				// 	icon: ClipboardDocumentList,
				// 	type: 'grouped-entities',
				// 	skeletonCount: 3,
				// 	isLoading: $isLoadingTriggerMatches
				// }, {
				label: 'In Progress', description: 'Agents currently running or starting up', items: categorizedAgents.working, unfilteredItems: unfilteredCategorizedAgents.working, icon: Play, type: 'agents', skeletonCount: 4, isLoading: isLoading }, { label: 'Ready for Review', description: 'Agents with unseen changes', items: categorizedAgents.readyForReview, unfilteredItems: unfilteredCategorizedAgents.readyForReview, icon: BellAlert, type: 'agents', skeletonCount: 2, isLoading: isLoading }, { label: 'Done', description: 'Agents that have completed their task but are still active', items: categorizedAgents.idle, unfilteredItems: unfilteredCategorizedAgents.idle, icon: CheckCircle, type: 'agents', skeletonCount: 8, isLoading: isLoading }] as column, columnIndex (column.label)}
			<!-- {#if column.unfilteredItems.length > 0 || column.isLoading} -->
			<div
				class="flex max-w-[50em] flex-shrink-0 flex-col border-r border-slate-200 transition-all dark:border-slate-700"
				style="flex: {column.unfilteredItems.length > 0 ? 2 : 1} 0; min-width: {column
					.unfilteredItems.length > 0
					? '0'
					: '200px'};"
				in:slide={{ axis: 'x', duration: 300, delay: columnIndex * 100 }}
				out:slide={{ axis: 'x', duration: 200 }}
			>
				{#if column.isLoading}
					<div
						class="border-b border-slate-200 bg-slate-50 p-4 dark:border-slate-700 dark:bg-slate-900"
					>
						<div class="mb-1 flex items-center gap-2">
							<Icon src={column.icon} class="h-4 w-4 text-slate-600 dark:text-slate-400" micro />
							<h2
								class="flex-1 truncate text-sm font-semibold whitespace-nowrap text-slate-900 dark:text-slate-100"
							>
								{column.label}
							</h2>
							<span
								class="inline-flex items-center rounded-full bg-slate-100 px-2 py-0.5 text-xs font-medium text-slate-800 dark:bg-slate-700 dark:text-slate-200"
							>
								<div class="h-3 w-4 animate-pulse rounded bg-slate-200 dark:bg-slate-600"></div>
							</span>
						</div>
						<p
							class="truncate text-xs whitespace-nowrap text-slate-600 dark:text-slate-400"
							title={column.description}
						>
							{column.description}
						</p>
					</div>
					<div class="flex-1 overflow-y-auto bg-slate-50 dark:bg-slate-950">
						<div class="">
							{#each Array(column.skeletonCount).fill(null) as _, skeletonIndex}
								<div
									in:fly={{ y: 20, duration: 300, delay: columnIndex * 100 + skeletonIndex * 50 }}
								>
									{#if column.type === 'entities'}
										<MatchRowSkeleton />
									{:else}
										<RemoteAgentRowSkeleton />
									{/if}
								</div>
							{/each}
						</div>
					</div>
				{:else}
					<div
						class="border-b border-slate-200 bg-slate-50 p-4 dark:border-slate-700 dark:bg-slate-900"
					>
						<div class="mb-1 flex items-center gap-2">
							<Icon src={column.icon} class="h-4 w-4 text-slate-600 dark:text-slate-400" micro />
							<h2
								class="flex-1 truncate text-sm font-semibold whitespace-nowrap text-slate-900 dark:text-slate-100"
							>
								{column.label}
							</h2>

							{#if column.label === 'Done' && column.items.length > 0}
								<div class="-my-2">
									<Button
										variant="ghost"
										size="xs"
										onclick={handleDeleteAllDone}
										class="w-full text-xs"
										icon={ArchiveBoxXMark}
										iconPosition="right"
									>
										Clear all done
									</Button>
								</div>
							{/if}
							<span
								class="inline-flex items-center rounded-full bg-slate-100 px-2 py-0.5 text-xs font-medium text-slate-800 dark:bg-slate-700 dark:text-slate-200"
							>
								{#if column.type === 'grouped-entities'}
									{(
										column.items as unknown as Array<{
											triggerName: string;
											entities: any[];
											count: number;
										}>
									).reduce((total, group) => total + group.count, 0)}
								{:else}
									{column.items.length}
								{/if}
							</span>
						</div>
						<p
							class="truncate text-xs whitespace-nowrap text-slate-600 dark:text-slate-400"
							title={column.description}
						>
							{column.description}
						</p>
					</div>
					<div class="flex-1 overflow-y-auto bg-slate-50 dark:bg-slate-950">
						{#if column.type === 'grouped-entities'}
							{#each column.items as group, groupIndex (`group-${column.label}-${groupIndex}`)}
								{@const triggerGroup = group as unknown as {
									trigger: NormalizedTrigger;
									entities: Array<UnifiedEntity & { triggerId: string; triggerName: string }>;
								}}
								<div class="mb-4 last:mb-0">
									<ExpandableSection
										title={triggerGroup.trigger.name}
										count={triggerGroup.entities.length}
										items={triggerGroup.entities}
										emptyMessage="No entities in this trigger"
										collapsedHeight={120}
										expanded={true}
									>
										<div class="divide-y divide-slate-200 dark:divide-slate-700">
											{#each triggerGroup.entities as entity, entityIndex (`entity-${groupIndex}-${entityIndex}`)}
												{@const typedEntity = entity as UnifiedEntity & {
													triggerId: string;
													triggerName: string;
												}}
												{@const relatedTrigger = undefined}
												<div in:fly={{ y: 20, duration: 300, delay: 100 }}>
													<MatchRow
														entity={typedEntity}
														onclick={() =>
															handleEntityClick(typedEntity, relatedTrigger || undefined)}
													/>
												</div>
											{/each}
										</div>
									</ExpandableSection>
								</div>
							{/each}
						{:else}
							{#each column.items as agent (`agent-${agent.id}`)}
								{@const typedAgent = agent as CleanRemoteAgent}
								<div in:receive={{ key: agent.id }} out:send={{ key: agent.id }}>
									<RemoteAgentRow agent={typedAgent} {onAgentClick} />
								</div>
							{/each}
						{/if}
					</div>
				{/if}
			</div>
			<!-- {/if} -->
		{/each}
	</div>
</div>

{#if shouldShowCreateTriggerDrawer}
	<!-- Config Drawer -->
	<TriggerCreateDrawer
		open
		existingTrigger={configTrigger}
		editMode={true}
		onClose={() => {
			configTrigger = null;
			shouldShowCreateTriggerDrawer = false;
		}}
		onSuccess={(update) => {
			configTrigger = null;
			shouldShowCreateTriggerDrawer = false;
			if (update && typeof update === 'object' && 'deleted' in update && update.deleted) {
				closeTriggerDrawer();
			}
		}}
	/>
{/if}
