<script lang="ts">
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import { Icon } from 'svelte-hero-icons';
	import ReactiveRelativeTime from '../ReactiveRelativeTime.svelte';

	interface Props {
		entity: UnifiedEntity;
		icon?: any;
	}

	let { entity, icon }: Props = $props();
</script>

<div
	class="flex gap-3 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800"
>
	<!-- Entity Icon -->
	{#if icon}
		<div
			class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"
		>
			<Icon src={icon} class="h-4 w-4 text-gray-600 dark:text-gray-400" />
		</div>
	{:else}
		<div
			class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700"
		>
			<span class="text-xs font-medium text-gray-600 dark:text-gray-400">
				{entity.entityType?.charAt(0)?.toUpperCase() || '?'}
			</span>
		</div>
	{/if}

	<div class="min-w-0 flex-1">
		<!-- Title -->
		<div class="mb-1 font-medium text-gray-900 dark:text-gray-100">
			{entity.title || 'Unknown Entity'}
		</div>

		<!-- Details -->
		<div class="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
			<span class="capitalize">{entity.entityType?.replace('_', ' ') || 'Entity'}</span>
			{#if entity.state}
				<span class="rounded bg-gray-100 px-2 py-1 text-xs dark:bg-gray-700">
					{entity.state}
				</span>
			{/if}
			{#if entity.createdAt}
				<ReactiveRelativeTime date={entity.createdAt} />
			{/if}
		</div>

		<!-- Description -->
		{#if entity.description}
			<div class="mt-2 text-sm text-gray-700 dark:text-gray-300">
				<p class="line-clamp-2">
					{entity.description}
				</p>
			</div>
		{/if}

		<!-- Actions -->
		{#if entity.url}
			<div class="mt-2">
				<a
					href={entity.url}
					target="_blank"
					rel="noopener noreferrer"
					class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
				>
					View →
				</a>
			</div>
		{/if}
	</div>
</div>
