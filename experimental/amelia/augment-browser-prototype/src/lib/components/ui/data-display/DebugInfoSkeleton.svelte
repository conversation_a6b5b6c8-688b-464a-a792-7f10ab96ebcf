<script lang="ts">
	// Skeleton loader for Debug Info section
	// Add some randomness to make it look more natural
	let infoCount = Math.floor(Math.random() * 3) + 2; // 2-4 info items
</script>

<!-- Debug Info Skeleton -->
<div class="space-y-2 text-sm animate-pulse">
	{#each Array(infoCount) as _, i}
		<div class="flex justify-between">
			<!-- Label -->
			<div class="relative w-20 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
				<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1}s;"></div>
			</div>
			<!-- Value -->
			<div class="relative w-32 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
				<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1 + 0.1}s;"></div>
			</div>
		</div>
	{/each}
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
