<script lang="ts">
	import { Icon, CheckCircle, XCircle, Clock, ExclamationTriangle } from 'svelte-hero-icons';
	import ReactiveRelativeTime from '../ReactiveRelativeTime.svelte';

	interface Props {
		entity: any; // Raw status data from GitHub API
	}

	let { entity }: Props = $props();

	// Get the appropriate icon and styling based on status state
	const statusConfig = $derived(() => {
		switch (entity.state?.toLowerCase()) {
			case 'success':
				return {
					icon: CheckCircle,
					iconClass: 'text-green-600 dark:text-green-400',
					bgClass: 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700',
					textClass: 'text-green-800 dark:text-green-200'
				};
			case 'failure':
			case 'error':
				return {
					icon: XCircle,
					iconClass: 'text-red-600 dark:text-red-400',
					bgClass: 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700',
					textClass: 'text-red-800 dark:text-red-200'
				};
			case 'pending':
				return {
					icon: Clock,
					iconClass: 'text-yellow-600 dark:text-yellow-400',
					bgClass: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700',
					textClass: 'text-yellow-800 dark:text-yellow-200'
				};
			default:
				return {
					icon: ExclamationTriangle,
					iconClass: 'text-gray-600 dark:text-gray-400',
					bgClass: 'bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-700',
					textClass: 'text-gray-800 dark:text-gray-200'
				};
		}
	});
</script>

<div class="flex gap-3 rounded-lg border p-3 {statusConfig().bgClass}">
	<!-- Status Icon -->
	<div class="flex h-6 w-6 flex-shrink-0 items-center justify-center">
		<Icon src={statusConfig().icon} class="h-5 w-5 {statusConfig().iconClass}" />
	</div>

	<div class="min-w-0 flex-1">
		<!-- Status Header -->
		<div class="flex items-center gap-2">
			<span class="font-medium {statusConfig().textClass}">
				{entity.context || 'Status Check'}
			</span>
			<span
				class="rounded-full px-2 py-0.5 text-xs font-medium uppercase {statusConfig()
					.bgClass} {statusConfig().textClass}"
			>
				{entity.state || 'unknown'}
			</span>
		</div>

		<!-- Description -->
		{#if entity.description}
			<div class="mt-1 text-sm text-slate-700 dark:text-slate-300">
				{entity.description}
			</div>
		{/if}

		<!-- Footer with timestamp and link -->
		<div class="mt-2 flex items-center gap-3 text-xs text-slate-500 dark:text-slate-400">
			{#if entity.createdAt}
				<ReactiveRelativeTime date={entity.createdAt} />
			{/if}

			{#if entity.targetUrl}
				<a
					href={entity.targetUrl}
					target="_blank"
					rel="noopener noreferrer"
					class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
				>
					View details →
				</a>
			{/if}
		</div>
	</div>
</div>
