<script lang="ts">
	import type { Task } from '$lib/types';
	import { Icon, CheckCircle, XCircle, Clock, ExclamationTriangle, User, Calendar } from 'svelte-hero-icons';
	import { getRelativeTimeForStr } from '$lib/utils/time';
	import { fly } from 'svelte/transition';
	import { cubicOut } from 'svelte/easing';

	interface Props {
		task: Task;
		isDetailed?: boolean;
		isSelected?: boolean;
		animationDelay?: number;
		onclick?: () => void;
	}

	let {
		task,
		isDetailed = false,
		isSelected = false,
		animationDelay = 0,
		onclick
	}: Props = $props();

	// Helper functions for status
	function getTaskStatusIcon(status: string) {
		switch (status) {
			case 'RUNNING':
				return Clock;
			case 'FINISHED':
				return CheckCircle;
			case 'FAILED':
				return XCircle;
			default:
				return ExclamationTriangle;
		}
	}

	function getTaskStatusColor(status: string) {
		switch (status) {
			case 'RUNNING':
				return 'text-blue-600 dark:text-blue-400';
			case 'FINISHED':
				return 'text-green-600 dark:text-green-400';
			case 'FAILED':
				return 'text-red-600 dark:text-red-400';
			default:
				return 'text-gray-600 dark:text-gray-400';
		}
	}

	function getStatusBadgeClasses(status: string) {
		switch (status) {
			case 'RUNNING':
				return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400';
			case 'FINISHED':
				return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400';
			case 'FAILED':
				return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400';
			default:
				return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400';
		}
	}

	function getStatusDotClasses(status: string) {
		switch (status) {
			case 'RUNNING':
				return 'bg-green-500';
			case 'FINISHED':
				return 'bg-blue-500';
			case 'FAILED':
				return 'bg-red-500';
			default:
				return 'bg-gray-400';
		}
	}

	let statusIcon = $derived(getTaskStatusIcon(task.status));
	let statusColor = $derived(getTaskStatusColor(task.status));
	let statusBadgeClasses = $derived(getStatusBadgeClasses(task.status));
	let statusDotClasses = $derived(getStatusDotClasses(task.status));
</script>

{#if isDetailed}
	<!-- Detailed Card Layout -->
	<button class="group relative rounded-lg transition-all duration-200 text-left p-4 {
		isSelected
			? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 shadow-md'
			: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600'
	}"
		{onclick}
		in:fly={{ y: 20, duration: 300, delay: animationDelay, easing: cubicOut }}>

		<!-- Header with status and agent indicator -->
		<div class="flex items-start justify-between mb-3">
			<div class="flex items-center gap-2">
				<Icon src={statusIcon} class="w-4 h-4 {statusColor}" />
				<span class="text-xs font-medium px-2 py-1 rounded {statusBadgeClasses}">
					{task.status}
				</span>
			</div>
			{#if task.remoteAgentId}
				<span class="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-2 py-1 rounded font-medium">
					Agent
				</span>
			{/if}
		</div>

		<!-- Task title -->
		<h3 class="font-semibold text-gray-900 dark:text-white text-sm mb-2 line-clamp-2">
			{task.title}
		</h3>

		<!-- Description -->
		{#if task.description}
			<p class="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
				{task.description}
			</p>
		{/if}

		<!-- Metadata -->
		<div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
			<div class="flex items-center gap-1">
				<Icon src={Calendar} class="w-3 h-3" />
				<span>{getRelativeTimeForStr(task.updatedAt.toISOString())}</span>
			</div>
			{#if task.assignee}
				<div class="flex items-center gap-1">
					<Icon src={User} class="w-3 h-3" />
					<span class="truncate max-w-20">{task.assignee.email || task.assignee.id}</span>
				</div>
			{/if}
		</div>
	</button>
{:else}
	<!-- Compact Card Layout -->
	<button class="group relative w-full rounded-md transition-all duration-200 text-left {
		isSelected
			? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 shadow-sm'
			: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-sm hover:border-gray-300 dark:hover:border-gray-600'
	}"
		{onclick}
		in:fly={{ x: -20, duration: 250, delay: animationDelay, easing: cubicOut }}>
		<div class="p-3">
			<div class="flex items-start justify-between">
				<!-- Main content -->
				<div class="flex-1 min-w-0">
					<!-- Task title and status -->
					<div class="flex items-center gap-2 mb-2">
						<div class="w-2 h-2 rounded-full {statusDotClasses} flex-shrink-0"></div>
						<h3 class="font-medium text-gray-900 dark:text-white text-sm truncate">
							{task.title}
						</h3>
						{#if task.remoteAgentId}
							<span class="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-1.5 py-0.5 rounded">
								Agent
							</span>
						{/if}
					</div>

					<!-- Metadata -->
					<div class="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
						<span class="truncate">
							{getRelativeTimeForStr(task.updatedAt.toISOString())}
						</span>
						{#if task.assignee}
							<span class="truncate">
								{task.assignee.email || task.assignee.id}
							</span>
						{/if}
					</div>

					<!-- Description (if present and space allows) -->
					{#if task.description}
						<p class="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
							{task.description}
						</p>
					{/if}
				</div>
			</div>
		</div>
	</button>
{/if}
