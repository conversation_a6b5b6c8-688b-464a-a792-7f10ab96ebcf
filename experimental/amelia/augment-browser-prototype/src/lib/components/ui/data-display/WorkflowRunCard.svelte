<script lang="ts">
	import type { Task, TriggerExecution } from '$lib/types';

	import { getRelativeTime, getRelativeTimeForStr } from '$lib/utils/time';
	import {
		ArrowTopRightOnSquare,
		CheckCircle,
		Clock,
		ExclamationTriangle,
		Icon,
		PlayCircle,
		StopCircle,
		User,
		XCircle
	} from 'svelte-hero-icons';

	import {
		getEntityTypeDisplayName,
		getEntityTypeIcon,
		getProviderDisplayName
	} from '$lib/config/entity-types';
	import { type UnifiedEntity } from '$lib/utils/entity-conversion';
	import { parseSessionSummaryTitle } from '$lib/utils/session-summary-parser';
	import Markdown from '../content/Markdown.svelte';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import AgentAssignmentForm from '../forms/AgentAssignmentForm.svelte';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { getTriggerForEntity } from '$lib/stores/global-state.svelte';

	interface Props {
		entity?: UnifiedEntity;
		run?: TriggerExecution;
		task?: Task;
		remoteAgent?: CleanRemoteAgent;
		linkedTask?: Task | null;
		isAnimating?: boolean;
		isSelected?: boolean;
		doAllowAgentCreation?: boolean;
		onclick?: ({
			entity,
			trigger
		}: {
			entity?: UnifiedEntity;
			trigger?: NormalizedTrigger;
		}) => void;
	}

	let {
		entity,
		run,
		task,
		remoteAgent,
		linkedTask = null,
		isAnimating = false,
		isSelected = false,
		doAllowAgentCreation = true,
		onclick
	}: Props = $props();

	// Entity state management
	let fetchedEntity = $state<UnifiedEntity | null>(null);
	// let isLoadingEntity = $state(false);

	// // Get trigger executions to find entity information
	// let triggerExecutions = $derived(getFlatTriggerExecutions());

	// Fetch entity details using GetEntityDetails API when we have a remote agent but no entity
	// async function fetchEntityFromRemoteAgent() {
	// 	if (!remoteAgent || entity || isLoadingEntity || !$session) return;

	// 	// First, try to find the trigger execution to get entity information
	// 	const execution = $triggerExecutions.find((exec) => exec.remote_agent_id === remoteAgent.remote_agent_id);

	// 	if (!execution?.event_payload) {
	// 		console.warn('No trigger execution found for remote agent:', remoteAgent.remote_agent_id);
	// 		return;
	// 	}

	// 	// Parse entity information from the trigger execution payload
	// 	let entityInfo;
	// 	try {
	// 		const payload = typeof execution.event_payload === 'string'
	// 			? JSON.parse(execution.event_payload)
	// 			: execution.event_payload;

	// 		if (payload.entity_id && payload.entity_type) {
	// 			entityInfo = {
	// 				entityId: payload.entity_id,
	// 				entityType: payload.entity_type,
	// 				eventSource: payload.event_source || '1', // Default to GitHub
	// 				repository: payload.repository || 'augmentcode/augment'
	// 			};
	// 		}
	// 	} catch (error) {
	// 		console.error('Error parsing trigger execution payload:', error);
	// 		return;
	// 	}

	// 	if (!entityInfo) {
	// 		console.warn('Could not extract entity information from trigger execution');
	// 		return;
	// 	}

	// 	try {
	// 		isLoadingEntity = true;
	// 		console.log('Fetching entity details using GetEntityDetails API:', entityInfo);

	// 		const response = await getEntityDetails(
	// 			$session.tenantUrl,
	// 			$session.accessToken,
	// 			{
	// 				entityId: entityInfo.entityId,
	// 				eventSource: entityInfo.eventSource.toString(),
	// 				entityType: entityInfo.entityType,
	// 				repository: entityInfo.repository
	// 			}
	// 		);

	// 		if (response.error) {
	// 			console.error('Failed to fetch entity details:', response.error);
	// 			return;
	// 		}

	// 		if (response.data?.found && response.data.entityData) {
	// 			const entityData = JSON.parse(response.data.entityData);

	// 			// Convert to unified entity format based on provider
	// 			const providerId = entityInfo.eventSource === '1' ? 'github' : 'linear';

	// 			fetchedEntity = {
	// 				id: entityInfo.entityId,
	// 				title: entityData.title || entityData.name || 'Unknown Entity',
	// 				description: entityData.body || entityData.description || '',
	// 				url: entityData.html_url || entityData.url || '',
	// 				state: entityData.state || 'unknown',
	// 				providerId: providerId,
	// 				entityType: entityInfo.entityType,
	// 				createdAt: entityData.created_at || new Date().toISOString(),
	// 				updatedAt: entityData.updated_at || new Date().toISOString(),
	// 				author: entityData.user ? {
	// 					name: entityData.user.login || entityData.user.name || 'Unknown',
	// 					login: entityData.user.login,
	// 					avatar: entityData.user.avatar_url
	// 				} : undefined,
	// 				assignee: entityData.assignee ? {
	// 					name: entityData.assignee.login || entityData.assignee.name || 'Unknown',
	// 					login: entityData.assignee.login,
	// 					avatar: entityData.assignee.avatar_url
	// 				} : undefined,
	// 				labels: entityData.labels || [],
	// 				metadata: entityData
	// 			};
	// 			console.log('Successfully fetched and converted entity using GetEntityDetails:', fetchedEntity);
	// 		}
	// 	} catch (error) {
	// 		console.error('Error fetching entity details:', error);
	// 	} finally {
	// 		isLoadingEntity = false;
	// 	}
	// }

	// // Trigger entity fetch when remote agent or trigger executions change
	// $effect(() => {
	// 	if (remoteAgent && !entity && $triggerExecutions.length > 0) {
	// 		fetchEntityFromRemoteAgent();
	// 	}
	// });

	let triggerInfo = $derived(getTriggerForEntity(entity));
	let trigger = $derived($triggerInfo?.data || null);

	// Entity is already unified, no conversion needed
	let unifiedEntity = $derived(entity || fetchedEntity);

	// Helper functions to extract useful metadata from entities
	function getEntityMetadata(entity: UnifiedEntity | null) {
		if (!entity) return null;

		return {
			author: entity.author,
			assignee: entity.assignee,
			state: entity.state,
			priority: entity.priority,
			labels: entity.labels?.slice(0, 2) || [], // Show max 2 labels
			repository: entity.metadata?.repository,
			identifier: entity.metadata?.identifier,
			team: entity.metadata?.team,
			isDraft: entity.metadata?.draft,
			number: entity.metadata?.number
		};
	}

	// Helper function to get GitHub avatar URL
	function getGitHubAvatarUrl(login: string | undefined, size: number = 40): string | null {
		console.log('getGitHubAvatarUrl', login, size);
		if (!login) return null;
		return `https://github.com/${login}.png?size=${size}`;
	}

	// Helper function to get GitHub-style state pill styling
	function getGitHubStatePill(state: string | undefined, isDraft: boolean = false) {
		if (!state) return null;

		const stateKey = state.toLowerCase();

		if (isDraft) {
			return {
				label: 'Draft',
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-600 dark:text-slate-400',
				borderColor: 'border-slate-300 dark:border-slate-600'
			};
		}

		const githubStates: Record<string, any> = {
			open: {
				label: 'Open',
				bgColor: 'bg-green-50 dark:bg-green-950/30',
				textColor: 'text-green-700 dark:text-green-300',
				borderColor: 'border-green-200 dark:border-green-800'
			},
			closed: {
				label: 'Closed',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				borderColor: 'border-red-200 dark:border-red-800'
			},
			merged: {
				label: 'Merged',
				bgColor: 'bg-purple-50 dark:bg-purple-950/30',
				textColor: 'text-purple-700 dark:text-purple-300',
				borderColor: 'border-purple-200 dark:border-purple-800'
			}
		};

		return (
			githubStates[stateKey] || {
				label: state,
				bgColor: 'bg-slate-50 dark:bg-slate-800',
				textColor: 'text-slate-700 dark:text-slate-300',
				borderColor: 'border-slate-200 dark:border-slate-600'
			}
		);
	}

	// Helper function to get Linear-style state pill styling
	function getLinearStatePill(state: string | undefined) {
		if (!state) return null;

		const stateKey = state.toLowerCase();

		const linearStates: Record<string, any> = {
			backlog: {
				label: 'Backlog',
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-700 dark:text-slate-300',
				dotColor: 'bg-slate-400 dark:bg-slate-500'
			},
			todo: {
				label: 'Todo',
				bgColor: 'bg-blue-50 dark:bg-blue-950/30',
				textColor: 'text-blue-700 dark:text-blue-300',
				dotColor: 'bg-blue-500'
			},
			'in progress': {
				label: 'In Progress',
				bgColor: 'bg-yellow-50 dark:bg-yellow-950/30',
				textColor: 'text-yellow-700 dark:text-yellow-300',
				dotColor: 'bg-yellow-500'
			},
			done: {
				label: 'Done',
				bgColor: 'bg-green-50 dark:bg-green-950/30',
				textColor: 'text-green-700 dark:text-green-300',
				dotColor: 'bg-green-500'
			},
			canceled: {
				label: 'Canceled',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				dotColor: 'bg-red-500'
			},
			cancelled: {
				label: 'Cancelled',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				dotColor: 'bg-red-500'
			}
		};

		return (
			linearStates[stateKey] || {
				label: state,
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-700 dark:text-slate-300',
				dotColor: 'bg-slate-400 dark:bg-slate-500'
			}
		);
	}

	// Helper function to get state color (for non-GitHub styling)
	function getStateColor(state: string | undefined, providerId: string) {
		if (!state) return 'text-slate-500 dark:text-slate-400';

		const stateColors: Record<string, Record<string, string>> = {
			github: {
				open: 'text-green-600 dark:text-green-400',
				closed: 'text-red-600 dark:text-red-400',
				merged: 'text-purple-600 dark:text-purple-400',
				draft: 'text-slate-600 dark:text-slate-400'
			},
			linear: {
				backlog: 'text-slate-600 dark:text-slate-400',
				todo: 'text-blue-600 dark:text-blue-400',
				'in progress': 'text-yellow-600 dark:text-yellow-400',
				done: 'text-green-600 dark:text-green-400',
				canceled: 'text-red-600 dark:text-red-400'
			}
		};

		return stateColors[providerId]?.[state.toLowerCase()] || 'text-slate-500 dark:text-slate-400';
	}

	// Helper functions for Task objects
	function getTaskStatusIcon(status: string) {
		switch (status) {
			case 'RUNNING':
				return Clock;
			case 'FINISHED':
				return CheckCircle;
			case 'FAILED':
				return XCircle;
			default:
				return ExclamationTriangle;
		}
	}

	function getTaskStatusColor(status: string) {
		switch (status) {
			case 'RUNNING':
				return 'text-blue-600 dark:text-blue-400';
			case 'FINISHED':
				return 'text-green-600 dark:text-green-400';
			case 'FAILED':
				return 'text-red-600 dark:text-red-400';
			default:
				return 'text-gray-600 dark:text-gray-400';
		}
	}

	// Get run status info
	function getRunStatusInfo(status: any) {
		const statusStr = typeof status === 'string' ? status : String(status);
		switch (statusStr?.toLowerCase()) {
			case 'pending':
				return {
					icon: Clock,
					color: 'text-yellow-600 dark:text-yellow-400',
					bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
					label: 'Pending'
				};
			case 'running':
				return {
					icon: PlayCircle,
					color: 'text-blue-600 dark:text-blue-400',
					bgColor: 'bg-blue-50 dark:bg-blue-900/20',
					label: 'Running'
				};
			case 'completed':
				return {
					icon: CheckCircle,
					color: 'text-green-600 dark:text-green-400',
					bgColor: 'bg-green-50 dark:bg-green-900/20',
					label: 'Completed'
				};
			case 'failed':
				return {
					icon: XCircle,
					color: 'text-red-600 dark:text-red-400',
					bgColor: 'bg-red-50 dark:bg-red-900/20',
					label: 'Failed'
				};
			case 'cancelled':
				return {
					icon: StopCircle,
					color: 'text-slate-600 dark:text-slate-400',
					bgColor: 'bg-slate-50 dark:bg-slate-800',
					label: 'Cancelled'
				};
			default:
				return {
					icon: ExclamationTriangle,
					color: 'text-slate-600 dark:text-slate-400',
					bgColor: 'bg-slate-50 dark:bg-slate-800',
					label: 'Unknown'
				};
		}
	}

	let runStatus = $derived(run ? getRunStatusInfo(run.status) : null);

	// Extract metadata for display
	let entityMetadata = $derived(getEntityMetadata(unifiedEntity));
	let stateColor = $derived(getStateColor(unifiedEntity?.state, unifiedEntity?.providerId || ''));
</script>

<div
	class="group relative flex w-full flex-col overflow-hidden rounded-xl border bg-white text-left transition-all duration-200 dark:bg-slate-900 {onclick
		? 'cursor-pointer'
		: ''} {isSelected
		? 'border-blue-500 bg-blue-50 shadow-md dark:border-blue-400 dark:bg-blue-900/50'
		: doAllowAgentCreation
			? 'border-slate-200 hover:border-slate-300 hover:shadow-md dark:border-slate-700 dark:hover:border-slate-600 dark:hover:shadow-lg'
			: 'border-slate-200 dark:border-slate-700'}"
	role="button"
	tabindex="0"
	onclick={() => onclick({ entity, trigger })}
>
	<!-- Header with Provider and Status -->
	{#if unifiedEntity || task || remoteAgent}
		<div
			class="flex items-center justify-between border-b border-slate-100 bg-slate-50/50 py-1 pr-1.5 pl-4 dark:border-slate-800 dark:bg-slate-800/30"
		>
			{#snippet headerContent()}
				{#if unifiedEntity}
					<CustomIcon
						icon={unifiedEntity.providerId}
						size={16}
						class="text-slate-400 dark:text-slate-400"
					/>
					{@const entityTypeIcon = getEntityTypeIcon(
						unifiedEntity.providerId,
						unifiedEntity.entityType
					)}
					<!-- <Icon src={entityTypeIcon} class="w-4 h-4 text-slate-400 dark:text-slate-400" micro /> -->
					<span class="font-mediumx text-xs text-slate-500 dark:text-slate-400">
						{getProviderDisplayName(unifiedEntity.providerId)}
						{getEntityTypeDisplayName(unifiedEntity.entityType, unifiedEntity.providerId)}
					</span>
					<!-- Number/Identifier -->
					{#if entityMetadata.number || entityMetadata.identifier}
						<span class="mt-[1px] font-mono text-xs text-slate-500 dark:text-slate-400">
							#{entityMetadata.number || entityMetadata.identifier}
						</span>
					{/if}
				{:else if task}
					<Icon src={CheckCircle} class="h-4 w-4 text-slate-400 dark:text-slate-400" />
					<span class="text-xs font-medium text-slate-500 dark:text-slate-400">Task Run</span>
				{:else if remoteAgent}
					<Icon src={User} class="h-4 w-4 text-slate-400 dark:text-slate-400" />
					<span class="text-xs font-medium text-slate-500 dark:text-slate-400">Remote Agent</span>
				{/if}
			{/snippet}

			<div class="flex gap-1.5 py-1">
				{@render headerContent()}
			</div>

			<!-- Status and External Link -->
			<div class="flex items-center gap-2">
				<!-- Status Indicator -->
				{#if remoteAgent}
					<RemoteAgentStatusIndicator
						status={remoteAgent.status}
						workspaceStatus={remoteAgent.workspaceStatus}
						hasUpdates={remoteAgent.hasUpdates}
						variant="sleek"
						size="sm"
						isExpanded={false}
					/>
				{:else if runStatus}
					<div class="flex items-center gap-1.5">
						<Icon src={runStatus.icon} class="h-3 w-3 {runStatus.color}" />
						<span class="text-xs font-medium {runStatus.color}">{runStatus.label}</span>
					</div>
				{:else if task}
					{@const taskStatusIcon = getTaskStatusIcon(task.status)}
					{@const taskStatusColor = getTaskStatusColor(task.status)}
					<div class="flex items-center gap-1.5">
						<Icon src={taskStatusIcon} class="h-3 w-3 {taskStatusColor}" />
						<span class="text-xs font-medium {taskStatusColor}">{task.status}</span>
					</div>
				{/if}

				<!-- External Link -->
				{#if entity?.url}
					<a
						href={entity.url}
						target="_blank"
						rel="noopener noreferrer"
						class="flex-shrink-0 rounded-md p-1.5 text-slate-400 transition-all duration-200 hover:bg-slate-100 hover:text-slate-600 dark:hover:bg-slate-800 dark:hover:text-slate-300"
						onclick={(e) => e.stopPropagation()}
						title="Open in new tab"
					>
						<Icon src={ArrowTopRightOnSquare} class="h-4 w-4" micro />
					</a>
				{/if}
			</div>
		</div>
	{/if}

	<!-- Main Content -->
	<div class="flex-1 space-y-4 p-4 pb-3.5">
		<!-- Title -->
		<div class="min-w-0 flex-1">
			{#snippet titleContent()}
				<h3
					class="line-clamp-2 flex-1 text-base leading-tight font-semibold text-slate-900 transition-colors group-hover:text-slate-700 dark:text-white dark:group-hover:text-slate-100"
				>
					{#if unifiedEntity}
						{unifiedEntity.metadata?.display_title || unifiedEntity.title}
					{:else if task}
						{task.title}
					{:else if remoteAgent}
						{remoteAgent.title}
					{:else if run}
						Execution {run.id.slice(-8)}
					{:else}
						Workflow Run
					{/if}
				</h3>

				<div class="-mt-0.5">
					<!-- State - GitHub style pill for GitHub, Linear style pill for Linear, dot for others -->
					{#if entityMetadata.state}
						{#if unifiedEntity?.providerId === 'github'}
							{@const statePill = getGitHubStatePill(entityMetadata.state, entityMetadata.isDraft)}
							{#if statePill}
								<span
									class="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-medium {statePill.bgColor} {statePill.textColor} {statePill.borderColor}"
								>
									{statePill.label}
								</span>
							{/if}
						{:else if unifiedEntity?.providerId === 'linear'}
							{@const statePill = getLinearStatePill(entityMetadata.state)}
							{#if statePill}
								<span
									class="inline-flex items-center gap-1.5 rounded-md px-2 py-1 text-xs font-medium {statePill.bgColor} {statePill.textColor}"
								>
									<div class="h-1.5 w-1.5 rounded-full {statePill.dotColor}"></div>
									{statePill.label}
								</span>
							{/if}
						{:else}
							<div class="flex items-center gap-1">
								<div class="h-2 w-2 rounded-full {stateColor.replace('text-', 'bg-')}"></div>
								<span class="text-xs font-medium {stateColor} capitalize"
									>{entityMetadata.state}</span
								>
							</div>
						{/if}
					{/if}
				</div>
			{/snippet}

			<div class="flex w-full items-start text-left">
				{@render titleContent()}
			</div>

			<!-- Metadata Row -->
			{#if entityMetadata}
				<div class="mt-2 flex items-center gap-3">
					<!-- Timestamp -->
					<div class="text-xs text-slate-500 italic dark:text-slate-400">
						{#if entity}
							{@const entityUpdatedAt = unifiedEntity?.updatedAt}
							{#if entityUpdatedAt}
								Updated {getRelativeTimeForStr(entityUpdatedAt)}
							{/if}
						{:else if task}
							Updated {getRelativeTimeForStr(
								task.updatedAt instanceof Date ? task.updatedAt.toISOString() : task.updatedAt
							)}
						{:else if remoteAgent}
							Updated {getRelativeTime(remoteAgent.updatedAt)}
						{:else if run?.startedAt}
							Started {getRelativeTime(run.startedAt)}
						{/if}
					</div>

					<!-- Draft indicator for non-GitHub (GitHub draft is handled in state pill) -->
					{#if entityMetadata.isDraft && unifiedEntity?.providerId !== 'github'}
						<span
							class="inline-flex items-center rounded bg-slate-100 px-1.5 py-0.5 text-xs font-medium text-slate-600 dark:bg-slate-800 dark:text-slate-400"
						>
							Draft
						</span>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Description -->
		{#if entity}
			{@const entityDescription = unifiedEntity?.description}
			{#if entityDescription}
				<div class="text-sm leading-relaxed text-slate-600 dark:text-slate-400">
					<Markdown content={entityDescription} size="xs" lineClamp={3} color="gray" />
				</div>
			{/if}
		{:else if task?.description}
			<div class="text-sm leading-relaxed text-slate-600 dark:text-slate-400">
				<Markdown content={task.description} size="xs" lineClamp={3} color="gray" />
			</div>
		{/if}

		<!-- People and Labels -->
		{#if entityMetadata && (entityMetadata.author || entityMetadata.assignee || entityMetadata.labels?.length)}
			<div class="flex flex-wrap items-center gap-3">
				<!-- Author -->
				{#if entityMetadata.author}
					<div class="flex items-center gap-1.5">
						{#if unifiedEntity?.providerId === 'github' && entityMetadata.author.login}
							{@const avatarUrl = getGitHubAvatarUrl(entityMetadata.author.login, 20)}
							{#if avatarUrl}
								<img
									src={avatarUrl}
									alt="{entityMetadata.author.name} avatar"
									class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
									loading="lazy"
								/>
							{:else}
								<div
									class="flex h-5 w-5 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900/30"
								>
									<Icon src={User} class="h-3 w-3 text-indigo-600 dark:text-indigo-400" mini />
								</div>
							{/if}
						{:else if entityMetadata.author.avatar}
							<img
								src={entityMetadata.author.avatar}
								alt="{entityMetadata.author.name} avatar"
								class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
								loading="lazy"
							/>
						{:else}
							<div
								class="flex h-5 w-5 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900/30"
							>
								<Icon src={User} class="h-3 w-3 text-indigo-600 dark:text-indigo-400" mini />
							</div>
						{/if}
						<span class="text-xs text-slate-600 dark:text-slate-400"
							>{entityMetadata.author.display_name || entityMetadata.author.name}</span
						>
					</div>
				{/if}

				<!-- Assignee -->
				{#if entityMetadata.assignee}
					<div class="flex items-center gap-1.5">
						{#if unifiedEntity?.providerId === 'github' && entityMetadata.assignee.login}
							{@const avatarUrl = getGitHubAvatarUrl(entityMetadata.assignee.login, 20)}
							{#if avatarUrl}
								<img
									src={avatarUrl}
									alt="{entityMetadata.assignee.name} avatar"
									class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
									loading="lazy"
								/>
							{:else}
								<div
									class="flex h-5 w-5 items-center justify-center rounded-full bg-teal-100 dark:bg-teal-900/30"
								>
									<Icon src={User} class="h-3 w-3 text-teal-600 dark:text-teal-400" mini />
								</div>
							{/if}
						{:else}
							<div
								class="flex h-5 w-5 items-center justify-center rounded-full bg-teal-100 dark:bg-teal-900/30"
							>
								<Icon src={User} class="h-3 w-3 text-teal-600 dark:text-teal-400" mini />
							</div>
						{/if}
						<span class="text-xs text-slate-600 dark:text-slate-400"
							>{entityMetadata.assignee.display_name || entityMetadata.assignee.name}</span
						>
					</div>
				{/if}
			</div>
		{:else if task?.assignee}
			<div class="flex items-center gap-3">
				<div class="flex items-center gap-1.5">
					<div
						class="flex h-5 w-5 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30"
					>
						<Icon src={User} class="h-3 w-3 text-green-600 dark:text-green-400" />
					</div>
					<span class="text-xs text-slate-600 dark:text-slate-400"
						>{task.assignee.email || task.assignee.id}</span
					>
				</div>
				{#if task.remoteAgentId}
					<span
						class="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-700 dark:bg-blue-800 dark:text-blue-300"
					>
						Agent
					</span>
				{/if}
			</div>
		{/if}

		<!-- Labels -->
		{#if entityMetadata.labels?.length}
			<div class="flex items-center gap-3">
				<div class="flex flex-wrap gap-1">
					{#each entityMetadata.labels as label}
						<span
							class="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-medium"
							style="background-color: {label.color
								? `#${label.color}15`
								: '#f8fafc'}; border-color: {label.color
								? `#${label.color}40`
								: '#e2e8f0'}; color: {label.color ? `#${label.color}` : '#475569'}"
						>
							{label.name}
						</span>
					{/each}
				</div>
			</div>
		{/if}
	</div>
</div>
