<script lang="ts">
	interface Props {
		entity: any; // Raw API entity data
	}

	let { entity }: Props = $props();
</script>

<!-- GitHub-style compact assignee list item -->
<div class="flex items-center justify-between py-1">
	<div class="flex items-center gap-2">
		<!-- Avatar -->
		{#if entity.avatarUrl}
			<img src={entity.avatarUrl} alt={entity.login || 'User'} class="h-5 w-5 rounded-full" />
		{:else}
			<div
				class="flex h-5 w-5 items-center justify-center rounded-full bg-slate-300 text-xs font-medium text-slate-700 dark:bg-slate-600 dark:text-slate-300"
			>
				{(entity.login || 'U').charAt(0).toUpperCase()}
			</div>
		{/if}

		<!-- Assignee name -->
		<span class="text-sm text-slate-900 dark:text-slate-100">
			{entity.login || 'Unknown'}
		</span>
	</div>
</div>
