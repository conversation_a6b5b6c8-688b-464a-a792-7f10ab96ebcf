<script lang="ts">
	import { getAgentPreview } from '$lib/stores/agent-preview.svelte';
	import { ChatBubbleLeft, Icon } from 'svelte-hero-icons';
	import { DocumentText } from 'svelte-hero-icons';
	import Markdown from '../content/Markdown.svelte';
	import { getAgentChangedFilesWithDetails } from '$lib/utils/agent-data';
	import StreamingText from '../content/StreamingText.svelte';
	import Tooltip from '../overlays/Tooltip.svelte';
	import { truncateText } from '$lib/utils/exchange-content-utils';

	interface Props {
		agentId: string;
		class?: string;
		maxLines?: number;
	}

	let { agentId, class: className = '', maxLines = 2 }: Props = $props();

	// Get preview data for this agent
	let previewStore = $derived(getAgentPreview(agentId));
	let preview = $derived($previewStore);

	// Get changed files for this agent
	let changedFilesStore = $derived(getAgentChangedFilesWithDetails(agentId));
	let changedFiles = $derived($changedFilesStore || []);

	// Determine display content and styling
	let displayContent = $derived.by(() => {
		// If agent is streaming, show streaming state
		if (preview.isStreaming) {
			return {
				text: preview.lastMessage || '',
				isStreaming: true,
				isEmpty: !preview.lastMessage,
				isToolCall: preview.isToolCall,
				toolName: preview.toolName
			};
		}

		// If has content, show it
		if (preview.lastMessage) {
			return {
				text: preview.lastMessage,
				isStreaming: false,
				isEmpty: false,
				isToolCall: preview.isToolCall,
				toolName: preview.toolName
			};
		}

		// No content available
		return {
			text: '',
			isStreaming: false,
			isEmpty: true,
			isToolCall: false
		};
	});

	// CSS classes for different states
	let contentClasses = $derived.by(() => {
		const base = 'text-xs leading-snug';
		const lineClamp = maxLines > 1 ? `line-clamp-${maxLines}` : 'truncate';

		if (displayContent.isEmpty) {
			return `${base} text-slate-400 dark:text-slate-500 italic ${lineClamp}`;
		}

		if (displayContent.isStreaming) {
			return `${base} text-slate-600 dark:text-slate-300 ${lineClamp}`;
		}

		if (displayContent.isToolCall) {
			return `${base} text-blue-600 dark:text-blue-400 ${lineClamp}`;
		}

		return `${base} text-slate-500 dark:text-slate-400 ${lineClamp}`;
	});

	// Changed files indicator logic
	let hasChangedFiles = $derived(changedFiles.length > 0);
	let changedFilesCount = $derived(changedFiles.length);

	// Group files by change type for tooltip
	let filesByType = $derived(() => {
		const groups = {
			added: changedFiles.filter((f) => f.changeType === 'added'),
			modified: changedFiles.filter((f) => f.changeType === 'modified'),
			deleted: changedFiles.filter((f) => f.changeType === 'deleted'),
			renamed: changedFiles.filter((f) => f.changeType === 'renamed')
		};
		return groups;
	});

	// Tooltip content
	let tooltipContent = $derived.by(() => {
		if (!hasChangedFiles) return '';

		const lines = changedFiles.map((file) => {
			const symbol = {
				added: '+',
				modified: 'Δ',
				deleted: '-',
				renamed: '>'
			}[file.changeType];
			const fileName = file.path.split('/').pop() || file.path;
			return `${symbol} ${truncateText(fileName, 30)}`;
		});

		return lines.join(', ');
	});
</script>

{#if displayContent.text || displayContent.isStreaming}
	<div class="flex min-w-0 flex-col gap-1 {className}">
		<!-- Message content -->
		<div class="flex min-w-0 items-start gap-1.5">
			<!-- Message icon -->
			<div class="mt-0.5 flex-shrink-0">
				{#if preview.contentType === 'user-message'}
					<div class="flex items-center gap-1">
						<Icon src={ChatBubbleLeft} class="h-3 w-3 text-slate-400 dark:text-slate-500" mini />
					</div>
				{/if}
			</div>

			<!-- Message text or streaming skeleton -->
			<div class="min-w-0 flex-1">
				{#if displayContent.isStreaming && !displayContent.text.trim()}
					<!-- Streaming skeleton when no content yet -->
					<div class="space-y-1">
						<div class="relative h-3 w-3/4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
							></div>
						</div>
						<div class="relative h-3 w-1/2 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
							<div
								class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
								style="animation-delay: 0.1s;"
							></div>
						</div>
					</div>
				{:else}
					<!-- streaming -->
					{#if displayContent.isStreaming}
						<StreamingText
							content={displayContent.text}
							isStreaming={true}
							useMarkdown={false}
							class={contentClasses}
							color="slate"
						/>
					{:else}
						<!-- Regular content -->
						<Markdown
							content={displayContent.text.trim().replace(/\n/g, ' ')}
							size="xs"
							hasNoPadding
							lineClamp={2}
							color="slate"
						/>
					{/if}
				{/if}
			</div>

			<!-- Changed files indicator -->
			{#if hasChangedFiles}
				<div class="flex flex-shrink-0">
					<Tooltip
						text={tooltipContent}
						delay={0}
						position="left"
						tooltipClass="whitespace-nowrap max-w-none"
					>
						<div class="flex items-center gap-1 text-xs text-slate-400 dark:text-slate-500">
							<span>{changedFilesCount}</span>
							<Icon src={DocumentText} class="h-3 w-3" mini />
						</div>
					</Tooltip>
				</div>
			{/if}
		</div>

		<!-- Timestamp -->
		<!-- {#if showTimestamp && formattedTime}
		<div class="flex items-center gap-1 ml-4">
			<span class={timestampClasses}>
				{formattedTime}
			</span>
			{#if displayContent.isStreaming}
				<span class="text-xs text-emerald-500 dark:text-emerald-400">
					• streaming
				</span>
			{/if}
		</div>
	{/if} -->
	</div>
{/if}

<style>
	/* Shimmer animation for loading skeleton */
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
