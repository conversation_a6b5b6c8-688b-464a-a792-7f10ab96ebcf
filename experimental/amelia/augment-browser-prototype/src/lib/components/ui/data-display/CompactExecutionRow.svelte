<script lang="ts">
	import { I<PERSON>, Clock, CheckCircle, XCircle, ArrowPath } from 'svelte-hero-icons';
	import { globalEntitiesStore } from '$lib/stores/entities';
	import AuggieAvatar from '$lib/components/ui/AuggieAvatar.svelte';
	import { getAgent } from '$lib/stores/global-state.svelte';
	import {
		extractEntityInfoFromRemoteAgent,
		getEntityByRemoteAgentId
	} from '$lib/utils/entity-store-utils';
	import { generateEntityKey } from '$lib/stores/entities';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import type { CleanExecution } from '$lib/api/unified-client';

	interface Props {
		execution: CleanExecution;
		trigger: any;
		projectId: string;
		onExecutionClick?: (execution: any) => void;
		showBorder?: boolean;
	}

	let { execution, trigger, projectId, onExecutionClick, showBorder = true }: Props = $props();

	// Get execution agent
	let executionAgentStore = $derived(getAgent(execution.remote_agent_id));
	let executionAgent = $derived($executionAgentStore);

	let entityInfo = $derived(extractEntityInfoFromRemoteAgent(execution.remote_agent_id));

	// Get linked entity if available - reactive to global entities store changes
	const entityKey = $derived(
		generateEntityKey(
			entityInfo?.id || '',
			entityInfo?.entityType || '',
			entityInfo?.providerId || ''
		)
	);
	const linkedEntity = $derived($globalEntitiesStore[entityKey]);

	// Effect to ensure entity fetch is triggered when component mounts
	$effect(() => {
		// Trigger the entity lookup which will start background fetch if needed
		getEntityByRemoteAgentId(execution.remote_agent_id);
	});

	// Status configuration with more distinct styling
	let statusConfig = $derived(
		(() => {
			const status = execution.status?.toLowerCase();
			if (status === 'completed') {
				return {
					label: 'Completed',
					icon: CheckCircle,
					text: 'text-emerald-600 dark:text-emerald-400',
					dot: 'bg-emerald-500'
				};
			}
			if (status === 'failed') {
				return {
					label: 'Failed',
					icon: XCircle,
					text: 'text-red-600 dark:text-red-400',
					dot: 'bg-red-500'
				};
			}
			if (status === 'running') {
				return {
					label: 'Running',
					icon: ArrowPath,
					text: 'text-blue-600 dark:text-blue-400',
					dot: 'bg-blue-500 animate-pulse'
				};
			}
			return {
				label: status || 'Unknown',
				icon: Clock,
				text: 'text-amber-600 dark:text-amber-400',
				dot: 'bg-amber-500'
			};
		})()
	);

	let formattedStartTime = $derived(
		new Date(execution.started_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
	);

	function handleExecutionClick(e: Event) {
		e.stopPropagation();
		onExecutionClick?.(execution);
	}
</script>

<!-- Compact Table Row Style -->
<div
	class="group cursor-pointer transition-colors hover:bg-slate-50 dark:hover:bg-slate-800/50 {showBorder
		? 'border-b border-slate-200 dark:border-slate-700'
		: ''}"
	role="button"
	tabindex="0"
	onclick={handleExecutionClick}
	onkeydown={(e) => e.key === 'Enter' && handleExecutionClick(e)}
>
	<!-- Main Row -->
	<div class="flex items-center justify-between px-4 py-2.5">
		<!-- Left: Execution info -->
		<div class="flex min-w-0 flex-1 items-center gap-3">
			<!-- Avatar -->
			{#if executionAgent}
				<AuggieAvatar colorSeed={executionAgent?.id} faceSeed={executionAgent?.id} size={24} />
			{:else}
				<div
					class="flex h-6 w-6 items-center justify-center rounded-full bg-slate-300 dark:bg-slate-600"
				>
					<Icon src={statusConfig.icon} class="h-3 w-3 text-white" />
				</div>
			{/if}

			<!-- Title and metadata -->
			<div class="min-w-0 flex-1">
				<h4
					class="line-clamp-1 text-sm font-medium text-slate-900 hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
				>
					{execution.entity?.title ||
						linkedEntity?.title ||
						`Working on ${trigger?.name || 'task'}`}
				</h4>

				<!-- Metadata line -->
				<div class="mt-0.5 flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400">
					<div class="flex items-center gap-1">
						<div class="h-2 w-2 rounded-full {statusConfig.dot}"></div>
						<span class={statusConfig.text}>
							{statusConfig.label}
						</span>
					</div>

					<span class="text-slate-400">•</span>
					<span>Started {formattedStartTime}</span>

					{#if trigger?.name}
						<span class="text-slate-400">•</span>
						<span>via {trigger.name}</span>
					{/if}

					{#if execution.id}
						<span class="text-slate-400">•</span>
						<span class="font-mono">#{execution.id.slice(-6)}</span>
					{/if}
				</div>
			</div>
		</div>

		<!-- Right: Status indicator -->
		{#if executionAgent}
			<div class="ml-3 flex-shrink-0">
				<RemoteAgentStatusIndicator
					status={executionAgent?.status}
					workspaceStatus={executionAgent?.workspaceStatus}
					hasUpdates={executionAgent?.has_updates}
					isExpanded={false}
					size="sm"
				/>
			</div>
		{/if}
	</div>
</div>
