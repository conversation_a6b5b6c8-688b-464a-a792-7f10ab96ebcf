<script lang="ts">
	import { type UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { Snippet } from 'svelte';
	import { getEntityTypeDisplayName, getProviderDisplayName } from '$lib/config/entity-types';
	import { getEntityStateConfig } from '$lib/utils/task-references';
	import { getRelativeTimeForStr } from '$lib/utils/time';
	import {
		ArrowTopRightOnSquare,
		Icon,
		User,
		CheckCircle,
		Clock,
		ExclamationTriangle,
		PlayCircle,
		StopCircle,
		XCircle
	} from 'svelte-hero-icons';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import Markdown from '../content/Markdown.svelte';
	import Button from '../navigation/Button.svelte';

	interface Props {
		entity: UnifiedEntity;
		class?: string;
		headerActions?: Snippet;
	}

	let { entity, class: className = '', headerActions }: Props = $props();

	// Helper functions to extract useful metadata from entities
	function getEntityMetadata(entity: UnifiedEntity) {
		return {
			author: entity.author,
			assignee: entity.assignee,
			state: entity.state,
			priority: entity.priority,
			labels: entity.labels || [],
			repository: entity.metadata?.repository,
			identifier: entity.metadata?.identifier,
			team: entity.metadata?.team,
			isDraft: entity.metadata?.draft,
			number: entity.metadata?.number,
			status: entity.metadata?.status,
			conclusion: entity.metadata?.conclusion,
			runNumber: entity.metadata?.run_number,
			actor: entity.metadata?.actor,
			triggeringActor: entity.metadata?.triggering_actor,
			headBranch: entity.metadata?.head_branch,
			estimate: entity.metadata?.estimate
		};
	}

	// Helper function to get GitHub avatar URL
	function getGitHubAvatarUrl(login: string | undefined, size: number = 20): string | null {
		if (!login) return null;
		return `https://github.com/${login}.png?size=${size}`;
	}

	// Helper function to get GitHub-style state pill styling
	function getGitHubStatePill(state: string | undefined, isDraft: boolean = false) {
		if (!state) return null;

		const stateKey = state.toLowerCase();

		if (isDraft) {
			return {
				label: 'Draft',
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-600 dark:text-slate-400',
				borderColor: 'border-slate-300 dark:border-slate-600'
			};
		}

		const githubStates: Record<string, any> = {
			open: {
				label: 'Open',
				bgColor: 'bg-green-50 dark:bg-green-950/30',
				textColor: 'text-green-700 dark:text-green-300',
				borderColor: 'border-green-200 dark:border-green-800'
			},
			closed: {
				label: 'Closed',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				borderColor: 'border-red-200 dark:border-red-800'
			},
			merged: {
				label: 'Merged',
				bgColor: 'bg-purple-50 dark:bg-purple-950/30',
				textColor: 'text-purple-700 dark:text-purple-300',
				borderColor: 'border-purple-200 dark:border-purple-800'
			}
		};

		return (
			githubStates[stateKey] || {
				label: state,
				bgColor: 'bg-slate-50 dark:bg-slate-800',
				textColor: 'text-slate-700 dark:text-slate-300',
				borderColor: 'border-slate-200 dark:border-slate-600'
			}
		);
	}

	// Helper function to get Linear-style state pill styling
	function getLinearStatePill(state: string | undefined) {
		if (!state) return null;

		const stateKey = state.toLowerCase();

		const linearStates: Record<string, any> = {
			backlog: {
				label: 'Backlog',
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-700 dark:text-slate-300',
				dotColor: 'bg-slate-400 dark:bg-slate-500'
			},
			todo: {
				label: 'Todo',
				bgColor: 'bg-blue-50 dark:bg-blue-950/30',
				textColor: 'text-blue-700 dark:text-blue-300',
				dotColor: 'bg-blue-500'
			},
			'in progress': {
				label: 'In Progress',
				bgColor: 'bg-yellow-50 dark:bg-yellow-950/30',
				textColor: 'text-yellow-700 dark:text-yellow-300',
				dotColor: 'bg-yellow-500'
			},
			done: {
				label: 'Done',
				bgColor: 'bg-green-50 dark:bg-green-950/30',
				textColor: 'text-green-700 dark:text-green-300',
				dotColor: 'bg-green-500'
			},
			canceled: {
				label: 'Canceled',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				dotColor: 'bg-red-500'
			},
			cancelled: {
				label: 'Cancelled',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				dotColor: 'bg-red-500'
			}
		};

		return (
			linearStates[stateKey] || {
				label: state,
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-700 dark:text-slate-300',
				dotColor: 'bg-slate-400 dark:bg-slate-500'
			}
		);
	}

	// Helper function to get workflow run status info
	function getWorkflowRunStatusInfo(status: string | undefined, conclusion: string | undefined) {
		if (!status) return null;

		const statusStr = status.toLowerCase();

		if (statusStr === 'completed' && conclusion) {
			const conclusionStr = conclusion.toLowerCase();
			switch (conclusionStr) {
				case 'success':
					return {
						icon: CheckCircle,
						color: 'text-green-600 dark:text-green-400',
						bgColor: 'bg-green-50 dark:bg-green-900/20',
						label: 'Success'
					};
				case 'failure':
					return {
						icon: XCircle,
						color: 'text-red-600 dark:text-red-400',
						bgColor: 'bg-red-50 dark:bg-red-900/20',
						label: 'Failed'
					};
				case 'cancelled':
					return {
						icon: StopCircle,
						color: 'text-slate-600 dark:text-slate-400',
						bgColor: 'bg-slate-50 dark:bg-slate-800',
						label: 'Cancelled'
					};
				case 'skipped':
					return {
						icon: ExclamationTriangle,
						color: 'text-yellow-600 dark:text-yellow-400',
						bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
						label: 'Skipped'
					};
				default:
					return {
						icon: CheckCircle,
						color: 'text-green-600 dark:text-green-400',
						bgColor: 'bg-green-50 dark:bg-green-900/20',
						label: 'Completed'
					};
			}
		}

		switch (statusStr) {
			case 'queued':
				return {
					icon: Clock,
					color: 'text-yellow-600 dark:text-yellow-400',
					bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
					label: 'Queued'
				};
			case 'in_progress':
				return {
					icon: PlayCircle,
					color: 'text-blue-600 dark:text-blue-400',
					bgColor: 'bg-blue-50 dark:bg-blue-900/20',
					label: 'Running'
				};
			default:
				return {
					icon: ExclamationTriangle,
					color: 'text-slate-600 dark:text-slate-400',
					bgColor: 'bg-slate-50 dark:bg-slate-800',
					label: status
				};
		}
	}

	// Helper function to get Linear priority display
	function getLinearPriorityInfo(priority: number | undefined) {
		if (priority === undefined || priority === null) return null;

		const priorityMap: Record<number, any> = {
			0: {
				label: 'No Priority',
				color: 'text-slate-500 dark:text-slate-400',
				bgColor: 'bg-slate-100 dark:bg-slate-800'
			},
			1: {
				label: 'Urgent',
				color: 'text-red-700 dark:text-red-300',
				bgColor: 'bg-red-50 dark:bg-red-950/30'
			},
			2: {
				label: 'High',
				color: 'text-orange-700 dark:text-orange-300',
				bgColor: 'bg-orange-50 dark:bg-orange-950/30'
			},
			3: {
				label: 'Medium',
				color: 'text-yellow-700 dark:text-yellow-300',
				bgColor: 'bg-yellow-50 dark:bg-yellow-950/30'
			},
			4: {
				label: 'Low',
				color: 'text-blue-700 dark:text-blue-300',
				bgColor: 'bg-blue-50 dark:bg-blue-950/30'
			}
		};

		return priorityMap[priority] || priorityMap[0];
	}

	// Extract metadata for display
	let entityMetadata = $derived(getEntityMetadata(entity));
	let workflowStatus = $derived(
		entity.entityType === 'workflow_run'
			? getWorkflowRunStatusInfo(entityMetadata.status, entityMetadata.conclusion)
			: null
	);
	let linearPriority = $derived(
		entity.providerId === 'linear'
			? getLinearPriorityInfo(
					typeof entityMetadata.priority === 'number' ? entityMetadata.priority : undefined
				)
			: null
	);

	function getStateColor(state = ''): string {
		if (!state) return '';
		const stateConfig = getEntityStateConfig(entity.entityType, state);
		if (stateConfig) {
			const colorMap = {
				green: 'text-emerald-700 dark:text-emerald-300',
				red: 'text-red-700 dark:text-red-300',
				purple: 'text-purple-700 dark:text-purple-300',
				blue: 'text-blue-700 dark:text-blue-300',
				gray: 'text-gray-600 dark:text-gray-400'
			};
			return colorMap[stateConfig.color as keyof typeof colorMap] || colorMap.gray;
		}

		// Fallback to original logic
		const lowerState = state.toLowerCase?.();
		if (['open', 'unstarted', 'started', 'in progress'].includes(lowerState)) {
			return 'text-emerald-700 dark:text-emerald-300';
		} else if (['closed', 'done', 'completed'].includes(lowerState)) {
			return 'text-gray-600 dark:text-gray-400';
		} else if (['merged'].includes(lowerState)) {
			return 'text-purple-700 dark:text-purple-300';
		}
		return 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400';
	}
</script>

<!-- Enhanced Entity Card -->
<div
	class="group relative flex w-full flex-col overflow-hidden rounded-xl border border-slate-200 bg-white text-left transition-all duration-200 dark:border-slate-700 dark:bg-slate-900 {className}"
>
	<!-- Header with Provider and Entity Type -->
	<div
		class="flex flex-wrap items-center justify-between border-b border-slate-100 py-2 pr-1.5 pl-4 font-medium text-slate-600 dark:border-slate-800 dark:bg-slate-800/30 dark:text-slate-400"
	>
		<div class="flex items-center py-1 whitespace-nowrap">
			<CustomIcon icon={entity.providerId} size={16} class="" />
			<span class="ml-2 text-sm">
				{getProviderDisplayName(entity.providerId)}
				{getEntityTypeDisplayName(entity.entityType, entity.providerId)}
			</span>
			<!-- Number/Identifier -->
			{#if entityMetadata.number || entityMetadata.identifier}
				<span class="mt-[2px] ml-1 font-mono text-xs">
					#{entityMetadata.number || entityMetadata.identifier}
				</span>
			{/if}
		</div>

		<!-- Header Actions Snippet -->
		{#if headerActions}
			{@render headerActions()}
		{/if}
		<div class="ml-auto flex flex-none items-center">
			<!-- State indicators based on provider -->
			{#if entity.providerId === 'github'}
				{#if entity.entityType === 'pull_request'}
					{@const statePill = getGitHubStatePill(entityMetadata.state, entityMetadata.isDraft)}
					{#if statePill}
						<span
							class="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-medium whitespace-nowrap {statePill.bgColor} {statePill.textColor} {statePill.borderColor}"
						>
							{statePill.label}
						</span>
					{/if}
				{:else if entity.entityType === 'workflow_run' && workflowStatus}
					<div class="flex items-center gap-1.5">
						<Icon src={workflowStatus.icon} class="h-3 w-3 {workflowStatus.color}" />
						<span class="text-xs font-medium {workflowStatus.color}">{workflowStatus.label}</span>
					</div>
				{/if}
			{:else if entity.providerId === 'linear'}
				{@const statePill = getLinearStatePill(entityMetadata.state)}
				{#if statePill}
					<span
						class="inline-flex items-center gap-1.5 rounded-md px-2 py-1 text-xs font-medium whitespace-nowrap {statePill.bgColor} {statePill.textColor}"
					>
						<div class="h-1.5 w-1.5 rounded-full {statePill.dotColor}"></div>
						{statePill.label}
					</span>
				{/if}
			{:else if entityMetadata.state}
				<div class="flex items-center gap-1">
					<div
						class="h-2 w-2 rounded-full {getStateColor(entityMetadata.state).replace(
							'text-',
							'bg-'
						)}"
					></div>
					<span class="text-xs font-medium {getStateColor(entityMetadata.state)} capitalize"
						>{entityMetadata.state}</span
					>
				</div>
			{/if}
		</div>

		<!-- External Link -->
		{#if entity.url}
			<Button
				href={entity.url}
				target="_blank"
				rel="noopener noreferrer"
				variant="ghost"
				class="ml-2"
				onclick={(e) => e.stopPropagation()}
				title="Open in new tab"
				icon={ArrowTopRightOnSquare}
				size="icon-sm"
			/>
		{/if}
	</div>

	<!-- Main Content -->
	<div class="flex-1 space-y-4 p-4 pb-3.5">
		<!-- Title and State -->
		<div class="min-w-0 flex-1">
			<div class="flex w-full items-start text-left">
				<div class="flex min-w-0 flex-1 gap-2">
					{#if entity.url}
						<a
							href={entity.url}
							target="_blank"
							rel="noopener noreferrer"
							class="inline-block cursor-pointer"
							onclick={(e) => e.stopPropagation()}
						>
							<h3
								class="line-clamp-2 text-lg leading-tight font-semibold text-slate-900 transition-colors hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
							>
								{entity.title}
							</h3>
						</a>
					{:else}
						<h3
							class="line-clamp-2 text-base leading-tight font-semibold text-slate-900 dark:text-white"
						>
							{entity.title}
						</h3>
					{/if}
				</div>
			</div>

			<!-- Metadata Row -->
			<div class="flex items-center gap-3">
				<!-- Timestamp -->
				<div class="text-xs text-slate-500 italic dark:text-slate-400">
					{#if entity.updatedAt}
						Updated {getRelativeTimeForStr(entity.updatedAt)}
					{:else if entity.createdAt}
						Created {getRelativeTimeForStr(entity.createdAt)}
					{/if}
				</div>

				<!-- Linear Priority -->
				{#if entity.providerId === 'linear' && linearPriority && linearPriority.label !== 'No Priority'}
					<span
						class="inline-flex items-center rounded px-1.5 py-0.5 text-xs font-medium {linearPriority.bgColor} {linearPriority.color}"
					>
						{linearPriority.label}
					</span>
				{/if}

				<!-- Workflow Run Number -->
				{#if entity.entityType === 'workflow_run' && entityMetadata.runNumber}
					<span
						class="inline-flex items-center rounded bg-slate-100 px-1.5 py-0.5 text-xs font-medium text-slate-600 dark:bg-slate-800 dark:text-slate-400"
					>
						Run #{entityMetadata.runNumber}
					</span>
				{/if}

				<!-- Draft indicator for non-GitHub (GitHub draft is handled in state pill) -->
				{#if entityMetadata.isDraft && entity.providerId !== 'github'}
					<span
						class="inline-flex items-center rounded bg-slate-100 px-1.5 py-0.5 text-xs font-medium text-slate-600 dark:bg-slate-800 dark:text-slate-400"
					>
						Draft
					</span>
				{/if}
			</div>
		</div>

		<!-- Description -->
		{#if entity.description}
			<div class="text-sm leading-relaxed text-slate-600 dark:text-slate-400">
				<Markdown content={entity.description} size="xs" lineClamp={3} color="gray" />
			</div>
		{/if}

		<!-- People and Labels -->
		{#if entityMetadata && (entityMetadata.author || entityMetadata.assignee || entityMetadata.labels?.length)}
			<div class="flex flex-wrap items-center gap-3">
				<!-- Author -->
				{#if entityMetadata.author}
					<div class="flex items-center gap-1.5">
						{#if entity.providerId === 'github' && entityMetadata.author.login}
							{@const avatarUrl = getGitHubAvatarUrl(entityMetadata.author.login, 20)}
							{#if avatarUrl}
								<img
									src={avatarUrl}
									alt="{entityMetadata.author.name} avatar"
									class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
									loading="lazy"
								/>
							{:else}
								<div
									class="flex h-5 w-5 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900/30"
								>
									<Icon src={User} class="h-3 w-3 text-indigo-600 dark:text-indigo-400" mini />
								</div>
							{/if}
						{:else if entityMetadata.author.avatar}
							<img
								src={entityMetadata.author.avatar}
								alt="{entityMetadata.author.name} avatar"
								class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
								loading="lazy"
							/>
						{:else}
							<div
								class="flex h-5 w-5 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900/30"
							>
								<Icon src={User} class="h-3 w-3 text-indigo-600 dark:text-indigo-400" mini />
							</div>
						{/if}
						<span class="text-xs text-slate-600 dark:text-slate-400">
							{entityMetadata.author.display_name || entityMetadata.author.name}
						</span>
					</div>
				{/if}

				<!-- Assignee -->
				{#if entityMetadata.assignee}
					<div class="flex items-center gap-1.5">
						{#if entity.providerId === 'github' && entityMetadata.assignee.login}
							{@const avatarUrl = getGitHubAvatarUrl(entityMetadata.assignee.login, 20)}
							{#if avatarUrl}
								<img
									src={avatarUrl}
									alt="{entityMetadata.assignee.name} avatar"
									class="h-5 w-5 rounded-full border border-slate-200 dark:border-slate-700"
									loading="lazy"
								/>
							{:else}
								<div
									class="flex h-5 w-5 items-center justify-center rounded-full bg-teal-100 dark:bg-teal-900/30"
								>
									<Icon src={User} class="h-3 w-3 text-teal-600 dark:text-teal-400" mini />
								</div>
							{/if}
						{:else}
							<div
								class="flex h-5 w-5 items-center justify-center rounded-full bg-teal-100 dark:bg-teal-900/30"
							>
								<Icon src={User} class="h-3 w-3 text-teal-600 dark:text-teal-400" mini />
							</div>
						{/if}
						<span class="text-xs text-slate-600 dark:text-slate-400">
							{entityMetadata.assignee.display_name || entityMetadata.assignee.name}
						</span>
					</div>
				{/if}
			</div>
		{/if}

		<!-- Labels -->
		{#if entityMetadata.labels?.length}
			<div class="flex items-center gap-3">
				<div class="flex flex-wrap gap-1">
					{#each entityMetadata.labels.slice(0, 4) as label}
						<span
							class="inline-flex items-center rounded-full border px-2 py-0.5 text-xs font-medium"
							style="background-color: {label.color
								? `#${label.color}15`
								: '#f8fafc'}; border-color: {label.color
								? `#${label.color}40`
								: '#e2e8f0'}; color: {label.color ? `#${label.color}` : '#475569'}"
						>
							{label.name}
						</span>
					{/each}
					{#if entityMetadata.labels.length > 4}
						<span class="text-xs text-slate-400">
							+{entityMetadata.labels.length - 4} more
						</span>
					{/if}
				</div>
			</div>
		{/if}

		<!-- Additional metadata for specific entity types -->
		{#if entity.entityType === 'workflow_run'}
			<div class="flex flex-wrap items-center gap-3 text-xs text-slate-500 dark:text-slate-400">
				{#if entityMetadata.actor}
					<span>Actor: {entityMetadata.actor.login || entityMetadata.actor.name}</span>
				{/if}
				{#if entityMetadata.headBranch}
					<span>Branch: {entityMetadata.headBranch}</span>
				{/if}
			</div>
		{:else if entity.providerId === 'linear'}
			<div class="flex flex-wrap items-center gap-3 text-xs text-slate-500 dark:text-slate-400">
				{#if entityMetadata.team}
					<span>Team: {entityMetadata.team}</span>
				{/if}
				{#if entityMetadata.estimate}
					<span>Estimate: {entityMetadata.estimate} pts</span>
				{/if}
			</div>
		{/if}
	</div>
</div>
