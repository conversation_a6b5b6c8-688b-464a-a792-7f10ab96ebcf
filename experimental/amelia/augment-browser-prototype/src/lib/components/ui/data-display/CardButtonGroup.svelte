<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		direction?: 'vertical' | 'horizontal';
		class?: string;
		role?: string;
		'aria-label'?: string;
		children: Snippet;
	}

	let {
		direction = 'vertical',
		class: className = '',
		role,
		'aria-label': ariaLabel,
		children
	}: Props = $props();

	// CSS classes for different directions
	const containerClasses = {
		vertical: 'flex flex-col',
		horizontal: 'flex flex-row'
	};

	const groupClasses = {
		vertical: `
			[&>button:first-child]:!rounded-t-lg
			[&>button:first-child]:!rounded-b-none
			[&>button:last-child]:!rounded-b-lg
			[&>button:last-child]:!rounded-t-none
			[&>button:not(:first-child):not(:last-child)]:!rounded-none
			[&>button:not(:first-child)]:!mt-[-1px]
			[&>button:only-child]:!rounded-lg
		`.replace(/\s+/g, ' ').trim(),
		horizontal: `
			[&>button:first-child]:!rounded-l-lg
			[&>button:first-child]:!rounded-r-none
			[&>button:last-child]:!rounded-r-lg
			[&>button:last-child]:!rounded-l-none
			[&>button:not(:first-child):not(:last-child)]:!rounded-none
			[&>button:not(:last-child)]:!border-r-0
			[&>button:only-child]:!rounded-lg
		`.replace(/\s+/g, ' ').trim()
	};
</script>

<div
	{role}
	aria-label={ariaLabel}
	class="{containerClasses[direction]} {groupClasses[direction]} {className}"
>
	{@render children()}
</div>
