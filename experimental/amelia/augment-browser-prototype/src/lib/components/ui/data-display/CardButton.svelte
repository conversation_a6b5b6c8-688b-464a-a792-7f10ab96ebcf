<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		selected?: boolean;
		disabled?: boolean;
		onclick?: () => void;
		class?: string;
		role?: string;
		ariaChecked?: boolean;
		ariaLabel?: string;
		variant?: 'standalone' | 'grouped';
		children: Snippet;
	}

	let {
		selected = false,
		disabled = false,
		onclick,
		class: className = '',
		role,
		ariaChecked,
		ariaLabel,
		variant = 'standalone',
		children
	}: Props = $props();

	function handleClick() {
		if (!disabled && onclick) {
			onclick();
		}
	}

	// Compute all classes dynamically using Svelte 5 syntax
	const buttonClasses = $derived(
		[
			// Base classes
			'relative w-full text-left p-4 border transition-all duration-200 group',

			// Variant classes
			variant === 'standalone' ? 'rounded-lg' : 'rounded-none',

			// State classes - using !important to override CardButtonGroup styles
			selected
				? '!bg-white !border-slate-200 dark:!bg-slate-700 dark:!border-slate-700 z-10'
				: '!bg-slate-50 !border-slate-200 hover:!bg-slate-100 dark:!bg-slate-800 dark:hover:!bg-slate-700 dark:hover:!border-slate-600 dark:!border-slate-700 z-0',

			// Disabled classes
			disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',

			// Custom classes
			className
		]
			.filter(Boolean)
			.join(' ')
	);
</script>

<button
	type="button"
	{role}
	aria-checked={ariaChecked}
	aria-label={ariaLabel}
	onclick={handleClick}
	{disabled}
	class={buttonClasses}
>
	{@render children()}
</button>
