<script lang="ts">
	import { fly } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import {
		<PERSON><PERSON><PERSON><PERSON>,
		Bolt,
		Cog6Tooth,
		Eye,
		EyeSlash,
		Icon,
		MagnifyingGlass
	} from 'svelte-hero-icons';
	import Input from '../forms/Input.svelte';
	import Button from '../navigation/Button.svelte';
	import MatchRowSkeleton from './MatchRowSkeleton.svelte';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import {
		loadMatchingEntities,
		executeTrigger,
		loadAgents
	} from '$lib/stores/data-operations.svelte';
	import { getTriggerEntityGroupsWithDismissed, getTrigger } from '$lib/stores/global-state.svelte';
	import MatchRowSimple from './MatchRowSimple.svelte';
	import Tooltip from '../overlays/Tooltip.svelte';
	import UnconfiguredProviderNotification from '../notifications/UnconfiguredProviderNotification.svelte';
	import { providerAuthStates } from '$lib/stores/provider-auth';
	import {
		generateAgentTitle,
		createTitleGenerationContext
	} from '$lib/utils/agent-title-generation';
	import { apiClient } from '$lib/api/unified-client';

	interface Props {
		triggerId: string;
		isLoading: boolean;
		openTriggerEditDrawer: () => void;
		onBack: () => void;
		onEntityClick: (
			entity: UnifiedEntity & { triggerId: string; triggerName: string },
			trigger?: NormalizedTrigger
		) => void;
	}

	let { triggerId, isLoading, openTriggerEditDrawer, onBack, onEntityClick }: Props = $props();

	// Get trigger reactively from global store
	let triggerStore = $derived(getTrigger(triggerId));
	let trigger = $derived($triggerStore.data);

	// Search state
	let searchQuery = $state('');

	// Show dismissed state
	let showDismissed = $state(false);

	// Manual execution state
	let isExecuting = $state(false);
	let executionError = $state<string | null>(null);

	// Get reactive store for entities with dismiss filtering
	let entitiesStore = $derived(getTriggerEntityGroupsWithDismissed(showDismissed));

	// Get entities from global state for this trigger with dismiss filtering
	let entities = $derived.by(() => {
		if (!trigger) return [];
		const groups = $entitiesStore;
		const group = groups.find((g) => g.trigger.id === trigger.id);
		return group?.entities || [];
	});

	// Helper function to check if a trigger uses an unconfigured provider
	function isTriggerProviderUnconfigured(trigger: NormalizedTrigger | null): boolean {
		if (!trigger) return false;
		// Skip cron triggers as they don't need external provider auth
		if (trigger.provider === 'schedule') return false;

		// Check if the provider is configured
		return !$providerAuthStates[trigger.provider]?.isConfigured;
	}

	// Filtered entities based on search query (dismiss filtering already handled by reactive store)
	let filteredEntities = $derived.by(() => {
		// Filter by search query if present
		if (!searchQuery.trim()) {
			return entities;
		}

		const query = searchQuery.toLowerCase();
		return entities.filter((entity) => {
			// Search in entity title
			const title = entity.title || '';
			if (title.toLowerCase().includes(query)) return true;

			// Search in entity description
			const description = entity.description || '';
			if (description.toLowerCase().includes(query)) return true;

			// Search in entity author/creator
			const author = entity.author?.login || entity.author?.name || '';
			if (author.toLowerCase().includes(query)) return true;

			return false;
		});
	});

	// Handle manual execution of scheduled triggers
	async function handleManualExecution() {
		if (!trigger || isExecuting) return;

		isExecuting = true;
		executionError = null;

		try {
			// For scheduled triggers, use a timestamped entity ID since they don't have specific entities
			const entityId = `scheduled-execution-${new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-')}`;
			const remoteAgentIdPromise = executeTrigger(trigger.id, entityId);
			const titleGenerationContext = createTitleGenerationContext(
				undefined, // no specific entity for scheduled triggers
				trigger,
				trigger.configuration?.agentConfig?.userGuidelines || trigger.description, // use trigger instructions as user instructions
				{
					// No specific repository/branch info for scheduled triggers
				}
			);
			const generatedTitlePromise = generateAgentTitle(titleGenerationContext);

			const [remoteAgentId, generatedTitle] = await Promise.all([
				remoteAgentIdPromise,
				generatedTitlePromise
			]);

			if (remoteAgentId && generatedTitle) {
				try {
					await apiClient.agents.updateTitle(remoteAgentId, generatedTitle);
					console.log('Agent title updated successfully in background:', generatedTitle);
					// Refresh agents list to show the updated title
					loadAgents(true);
				} catch (titleUpdateError) {
					console.warn('Failed to update agent title in background:', titleUpdateError);
				}
			}

			// Success feedback could be added here (e.g., toast notification)
			console.log('Scheduled trigger executed successfully');
		} catch (error) {
			console.error('Failed to execute scheduled trigger:', error);
			executionError = error instanceof Error ? error.message : 'Failed to execute trigger';
		} finally {
			isExecuting = false;
		}
	}

	// Handle background title generation for the created agent
	async function handleBackgroundTitleGeneration(
		remoteAgentId: string,
		trigger: NormalizedTrigger
	) {
		try {
			// Create title generation context for scheduled trigger
			const titleGenerationContext = createTitleGenerationContext(
				undefined, // no specific entity for scheduled triggers
				trigger,
				trigger.configuration?.agentConfig?.userGuidelines || trigger.description, // use trigger instructions as user instructions
				{
					// No specific repository/branch info for scheduled triggers
				}
			);

			const generatedTitle = await generateAgentTitle(titleGenerationContext);

			if (generatedTitle) {
				try {
					await apiClient.agents.updateTitle(remoteAgentId, generatedTitle);
					console.log('Agent title updated successfully in background:', generatedTitle);
					// Refresh agents list to show the updated title
					loadAgents(true);
				} catch (titleUpdateError) {
					console.warn('Failed to update agent title in background:', titleUpdateError);
				}
			}
		} catch (titleGenerationError) {
			console.warn('Failed to generate title in background:', titleGenerationError);
		}
	}
</script>

{#if trigger}
	<div
		class="flex h-full w-full flex-col"
		in:fly={{ x: 360, duration: 300, easing: quintOut }}
		out:fly={{ x: 360, duration: 300, easing: quintOut }}
	>
		<!-- Header -->
		<div
			class="sticky top-0 z-10 flex items-center justify-between gap-1 border-b border-slate-200 bg-slate-50 px-5 py-1.5 dark:border-slate-700 dark:bg-slate-900"
		>
			<div class="flex w-full items-center gap-1">
				<Button
					variant="ghost-light"
					size="icon-sm"
					icon={ArrowLeft}
					onclick={onBack}
					aria-label="Back to trigger list"
				/>
				<h3 class="flex-1 truncate text-sm font-medium text-slate-700 dark:text-slate-300">
					{trigger.name}
				</h3>

				{#if trigger.provider !== 'schedule'}
					<div class="mr-2 flex items-center gap-2">
						<Tooltip
							maxWidth="300px"
							tooltipClass="whitespace-nowrap max-w-none"
							text={showDismissed
								? 'Showing all matches. Click to hide dismissed.'
								: 'Hiding dismissed matches. Click to show.'}
							placement="top"
							delay={0}
						>
							<Button
								variant="ghost-light"
								size="icon-sm"
								onclick={() => {
									showDismissed = !showDismissed;
									loadMatchingEntities(triggerId, { showDismissed });
								}}
								aria-label={showDismissed
									? 'Showing dismissed matches. Click to hide.'
									: 'Hiding dismissed matches. Click to show.'}
							>
								{#if showDismissed}
									<Icon src={Eye} class="h-4 w-4" micro />
								{:else}
									<Icon src={EyeSlash} class="h-4 w-4" micro />
								{/if}
							</Button>
						</Tooltip>
					</div>
				{/if}
				<Button
					variant="ghost-light"
					size="icon-sm"
					icon={Cog6Tooth}
					onclick={() => openTriggerEditDrawer()}
				/>
			</div>
		</div>

		<!-- Search (if more than 3 items) -->
		{#if entities.length > 3}
			<div class="px-6 py-3 pb-2 dark:border-slate-700">
				<Input
					icon={MagnifyingGlass}
					type="search"
					placeholder="Search matches..."
					bind:value={searchQuery}
					inputClass="pl-7"
					size="sm"
				/>
			</div>
		{/if}

		<!-- Unconfigured Provider Notification -->
		{#if !isLoading && isTriggerProviderUnconfigured(trigger)}
			<div class="px-6 pt-1 pb-3">
				<UnconfiguredProviderNotification
					unconfiguredProviders={[
						{
							providerId: trigger.provider,
							providerName: trigger.provider.charAt(0).toUpperCase() + trigger.provider.slice(1),
							triggerCount: 1,
							triggers: [
								{
									id: trigger.id,
									provider: trigger.provider,
									name: trigger.name
								}
							]
						}
					]}
					onAuthSuccess={(providerId) => {
						console.log(`Provider ${providerId} connected successfully`);
					}}
					class="text-sm"
				/>
			</div>
		{/if}

		<!-- Entity List -->
		{#if trigger.provider === 'schedule'}
			<!-- add a button to run the trigger -->
			<div class="p-6">
				<p class="text-sm text-slate-500 dark:text-slate-400">
					{(trigger.conditions as any).schedule?.description || ''}
				</p>
				<Button
					variant="primary"
					size="sm"
					class="mt-3"
					icon={Bolt}
					onclick={handleManualExecution}
					disabled={isExecuting}
				>
					{isExecuting ? 'Running...' : 'Run now'}
				</Button>
				{#if executionError}
					<p class="mt-2 text-sm text-red-600 dark:text-red-400">
						{executionError}
					</p>
				{/if}
			</div>
		{:else}
			<div class="flex-1 overflow-y-auto">
				{#if isLoading}
					{#each Array(5).fill(null) as _}
						<MatchRowSkeleton />
					{/each}
				{:else if filteredEntities.length === 0}
					<div class="py-8 text-center">
						<p class="text-sm text-slate-500 dark:text-slate-400">
							{searchQuery ? 'No matches found for your search' : 'No matching entities'}
						</p>
					</div>
				{:else}
					{#each filteredEntities as entity}
						<div class="w-full" transition:fly={{ x: -6, duration: 300 }}>
							<MatchRowSimple
								{entity}
								showDismissButton={true}
								triggerId={trigger.id}
								onclick={() =>
									onEntityClick(
										{ ...entity, triggerId: trigger.id, triggerName: trigger.name },
										trigger
									)}
							/>
						</div>
					{/each}
				{/if}
			</div>
		{/if}
	</div>
{/if}
