<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { reactiveRelativeTime } from '$lib/utils/time';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import {
		extractEntityDetailsFromAgent,
		getEntityByAgent,
		getTriggerForAgent,
		ensureEntityLoadedForAgent
	} from '$lib/stores/global-state.svelte';
	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import RemoteAgentTitle from '../RemoteAgentTitle.svelte';
	import AuggieAvatar from '../visualization/AuggieAvatar.svelte';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	// Removed unused imports
	import AgentDeletionModal from '../overlays/AgentDeletionModal.svelte';
	import RemoteAgentMenu from '../RemoteAgentMenu.svelte';

	interface Props {
		agent: CleanRemoteAgent;
		onAgentClick?: (agent: CleanRemoteAgent) => void;
		class?: string;
	}

	let { agent, onAgentClick, class: className = '' }: Props = $props();

	// Find the trigger that created this execution
	let triggerInfo = $derived(getTriggerForAgent(agent.id));
	let trigger = $derived($triggerInfo?.data || null);

	// Get linked entity from global state
	let entityDetails = $derived(extractEntityDetailsFromAgent(agent));
	let linkedEntityInfo = $derived(getEntityByAgent(agent.id));
	let linkedEntity = $derived($linkedEntityInfo?.data || null);

	// Reactively ensure entity is loaded when needed
	$effect(() => {
		const entityInfo = $linkedEntityInfo;
		// Only trigger loading if entity doesn't exist, isn't loading, and has no error
		if (!entityInfo.data && !entityInfo.loading && !entityInfo.error) {
			ensureEntityLoadedForAgent(agent.id);
		}
	});

	let relativeUpdateTime = $derived.by(reactiveRelativeTime(agent.updatedAt));

	function getGitHubRepo(url: string | undefined): string {
		if (!url) return '';
		const match = url.match(/github\.com\/([^/]+\/[^/]+)/);
		return match ? match[1] : '';
	}
	let repo = $derived(getGitHubRepo(agent.githubUrl));
	let showDeletionModal = $state(false);
	let copyFeedback = $state('');

	function handleDeleteAgent() {
		showDeletionModal = true;
	}

	// Menu callback functions
	function handleCopyId() {
		navigator.clipboard.writeText(agent.id);
		copyFeedback = 'Copied!';
		setTimeout(() => {
			copyFeedback = '';
		}, 2000);
	}

	function handleRename() {
		// TODO: Implement rename functionality
		console.log('Rename agent:', agent.id);
	}

	function handlePause() {
		// TODO: Implement pause functionality
		console.log('Pause agent:', agent.id);
	}

	function handleResume() {
		// TODO: Implement resume functionality
		console.log('Resume agent:', agent.id);
	}

	function handleMenuClose() {
		copyFeedback = '';
	}
</script>

<div
	class="group/row relative m-1.5 rounded-lg border border-slate-200 bg-white text-left transition-all duration-200 hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-900 dark:hover:bg-slate-800/50 {onAgentClick
		? 'cursor-pointer'
		: ''} {className}"
	onclick={() => onAgentClick?.(agent)}
	onkeydown={(e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			e.preventDefault();
			onAgentClick?.(agent);
		}
	}}
	role={onAgentClick ? 'button' : 'div'}
	tabindex={onAgentClick ? 0 : -1}
>
	<!-- Unread indicator -->
	{#if agent.hasUpdates && agent.status === RemoteAgentStatus.AGENT_IDLE}
		<div
			class="absolute top-1 right-1 z-10 h-3 w-3 rounded-full border-2 border-white bg-emerald-500 dark:border-slate-900"
		></div>
	{/if}
	<div class="flex w-full">
		<div class="flex min-w-0 flex-1 flex-col gap-2">
			<!-- Agent Row -->
			<div class="flex w-full items-start text-left">
				<!-- Avatar -->
				<div
					class="flex h-full flex-shrink-0 items-center justify-center border-r border-slate-200 py-1 pr-2.5 pl-3 dark:border-slate-700"
				>
					<AuggieAvatar colorSeed={agent.id} faceSeed={agent.id} size={36} />
				</div>
				<!-- Content -->
				<div class="flex min-w-0 flex-1 flex-col gap-1 px-3 py-1.5 pb-2.5">
					<!-- Title -->
					<div class="relative flex items-end justify-between gap-1">
						<div class="min-w-0 flex-1 pb-0.5">
							<RemoteAgentTitle
								{agent}
								size="sm"
								class="!line-clamp-1 truncate text-sm font-medium text-slate-900 dark:text-white"
							/>
						</div>
						<div class="group/actions flex-none pr-0.5">
							<!-- Timestamp shown by default, kebab menu on hover -->
							{#if agent.updatedAt && agent.updatedAt !== agent.startedAt}
								<span
									class="text-xs whitespace-nowrap text-slate-500 transition-opacity duration-200 group-focus-within/actions:opacity-0 group-hover/row:opacity-0 dark:text-slate-500"
								>
									{relativeUpdateTime}
								</span>
							{/if}
							<!-- Kebab menu that shows on hover -->
							<div
								class="absolute top-0 right-0 translate-y-2 transform opacity-0 transition-all duration-200 group-hover/row:translate-y-0 group-hover/row:opacity-100 focus-within:translate-y-0 focus-within:opacity-100"
								onclick={(e) => e.stopPropagation()}
								onkeydown={(e) => e.stopPropagation()}
								role="presentation"
							>
								<RemoteAgentMenu
									{agent}
									onCopy={handleCopyId}
									onRename={handleRename}
									onDelete={handleDeleteAgent}
									onPause={handlePause}
									onResume={handleResume}
									onClose={handleMenuClose}
									{copyFeedback}
								/>
							</div>
						</div>
					</div>

					<div class="flex min-w-0 flex-wrap items-center gap-x-1 gap-y-1">
						<!-- repo -->
						<!-- {#if repo}
							<div class="flex min-w-0 items-center gap-1">
								<span class="truncate text-xs text-slate-500 dark:text-slate-500">
									{repo}
								</span>
							</div>
						{/if} -->

						{#if linkedEntity?.title}
							<div class="flex min-w-0 flex-1 items-center gap-1">
								<CustomIcon icon={linkedEntity.providerId} size={13} class="flex-shrink-0 " />
								<span class="truncate text-xs text-slate-500 dark:text-slate-500">
									{`${trigger?.name || '[Deleted trigger]'}`}
								</span>
							</div>
						{/if}
						<!-- {#if agent.startedAt}
					<span class="text-xs text-slate-500 dark:text-slate-500">
						• Started {getRelativeTimeForStr(agent.startedAt.toISOString())}
					</span>
				{/if} -->
						<div class="ml-auto">
							<RemoteAgentStatusIndicator
								status={agent.status}
								workspaceStatus={agent.workspaceStatus}
								hasUpdates={agent.hasUpdates}
								size="sm"
								class="mt-0.5"
								isExpanded
							/>
						</div>
					</div>

					<!-- Agent Preview - Last message or streaming content -->
				</div>
			</div>

			<!-- {#if !!entityDetails}
			<CustomIcon icon={entityDetails.providerId} size={16} class="text-slate-400 dark:text-slate-400 flex-shrink-0" />
			<div class="flex items-center gap-1.5 text-xs text-slate-500 dark:text-slate-400 min-w-0 overflow-hidden">
				<span class="text-slate-500 dark:text-slate-400 min-w-0 truncate">
					Linked to {getProviderDisplayName(entityDetails.providerId)} {getEntityTypeDisplayName(entityDetails.entityType, entityDetails.providerId)}
				</span>
				{#if !linkedEntity}
					<span class="text-slate-500 dark:text-slate-400 min-w-0 truncate">
						• <Icon src={ExclamationTriangle} class="-mt-0.5 w-3 h-3 inline-block" mini /> Can't load details
					</span>
				{/if}
				{#if trigger?.name}
					<span class="text-xs text-slate-500 dark:text-slate-500 min-w-0 truncate flex-shrink">
						• via {trigger.name}
					</span>
				{/if}
			</div>
			{/if} -->
		</div>
	</div>

	<!-- <div
		class="rounded-b-lg border-t border-slate-200 bg-slate-50 px-3 py-2 empty:hidden dark:border-slate-700 dark:bg-slate-950"
	>
		<AgentPreview agentId={agent.id} maxLines={2} />
	</div> -->
</div>

<!-- Agent Deletion Modal -->
<AgentDeletionModal
	isOpen={showDeletionModal}
	{agent}
	onClose={() => (showDeletionModal = false)}
/>
