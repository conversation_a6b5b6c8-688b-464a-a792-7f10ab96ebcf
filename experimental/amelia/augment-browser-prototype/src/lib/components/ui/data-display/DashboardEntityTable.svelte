<script lang="ts">
	import type { Task } from '$lib/types';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import { fly } from 'svelte/transition';
	import DashboardEntityRow from './DashboardEntityRow.svelte';

	interface Props {
		entities: UnifiedEntity[];
		linkedTasks?: Record<string, Task>;
		onEntityClick?: (entity: UnifiedEntity) => void;
		onViewWorkflow?: (taskId: string) => void;
	}

	let { entities, linkedTasks = {}, onEntityClick, onViewWorkflow }: Props = $props();
</script>

<!-- Responsive container that works in narrow columns -->
<div class="w-full min-w-0 overflow-hidden">
	{#if entities.length === 0}
		<div class="py-8 text-center text-slate-500 dark:text-slate-400">No entities to display</div>
	{:else}
		{#each entities as entity (entity.id)}
			{@const linkedTask = linkedTasks[entity.id]}
			<div class="w-full min-w-0" transition:fly={{ x: -20 }}>
				<DashboardEntityRow {entity} {linkedTask} {onEntityClick} {onViewWorkflow} />
			</div>
		{/each}
	{/if}
</div>
