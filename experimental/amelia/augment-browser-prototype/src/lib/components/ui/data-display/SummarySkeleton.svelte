<script lang="ts">
	// Skeleton loader for summary section
	// Add some randomness to make it look more natural
	let line1Width = Math.random() > 0.5 ? 'w-full' : 'w-5/6';
	let line2Width = Math.random() > 0.5 ? 'w-4/5' : 'w-3/4';
	let line3Width = Math.random() > 0.5 ? 'w-3/5' : 'w-1/2';
</script>

<!-- Summary Skeleton -->
<div class="">
	<div class="space-y-3 px-3 py-3">
		<!-- Title line -->
		<div class="relative {line1Width} h-4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
			<div
				class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
			></div>
		</div>

		<!-- Content lines -->
		<div class="space-y-2">
			<div class="relative {line2Width} h-3 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
					style="animation-delay: 0.1s;"
				></div>
			</div>
			<div class="relative {line3Width} h-3 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
					style="animation-delay: 0.2s;"
				></div>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
