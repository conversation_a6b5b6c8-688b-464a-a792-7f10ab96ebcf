<script lang="ts">
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import { sendEntityRow, receiveEntityRow } from '$lib/utils/shared-transitions';
	import { dismissEntityAndSync } from '$lib/stores/data-operations.svelte';
	import { addToast } from '$lib/stores/toast';
	import { ArrowUturnLeft, Icon, XMark } from 'svelte-hero-icons';
	import Tooltip from '../overlays/Tooltip.svelte';
	import Button from '../navigation/Button.svelte';
	import Markdown from '../content/Markdown.svelte';
	import { getRelativeTimeForStr } from '$lib/utils/time';

	interface Props {
		entity: UnifiedEntity;
		showDismissButton?: boolean; // Whether to show dismiss button
		triggerId?: string; // Required for dismiss functionality
		onclick?: ({
			entity,
			trigger
		}: {
			entity: UnifiedEntity;
			trigger: NormalizedTrigger | null;
		}) => void;
		class?: string;
		trigger?: NormalizedTrigger | null;
	}

	let {
		entity,
		showDismissButton = false,
		triggerId,
		onclick,
		class: className = '',
		trigger = null
	}: Props = $props();

	// Dismiss functionality
	let isDismissing = $state(false);

	// Detect if we're on a mobile device (screen size based)
	let isMobileDevice = $state(false);

	// Check for mobile device based on screen size
	$effect(() => {
		if (typeof window !== 'undefined') {
			const updateMobileState = () => {
				isMobileDevice = window.innerWidth < 768; // Mobile/tablet breakpoint
			};

			updateMobileState();
			window.addEventListener('resize', updateMobileState);

			return () => {
				window.removeEventListener('resize', updateMobileState);
			};
		}
	});

	async function handleDismiss(event: Event) {
		event.stopPropagation(); // Prevent triggering row click

		if (!triggerId || !entity.id || isDismissing) return;

		isDismissing = true;
		try {
			const newDismissedStatus = !entity.isDismissed;
			await dismissEntityAndSync(triggerId, entity.id, newDismissedStatus);
			// addToast({
			// 	type: 'success',
			// 	message: newDismissedStatus
			// 		? 'Entity dismissed successfully'
			// 		: 'Entity restored successfully'
			// });
		} catch (error) {
			console.error('Failed to dismiss entity:', error);
			addToast({
				type: 'error',
				message: 'Failed to dismiss entity'
			});
		} finally {
			isDismissing = false;
		}
	}
</script>

<!-- Simple Match Row with just title -->
<div
	role="listitem"
	class="group relative transition-colors {entity.isDismissed
		? 'bg-slate-50 opacity-60 dark:bg-slate-800/30'
		: 'bg-white dark:bg-slate-900'} hover:bg-slate-50 dark:hover:bg-slate-800/50 {onclick
		? 'cursor-pointer'
		: ''} {className}"
	onclick={() => onclick?.({ entity, trigger })}
>
	<!-- Main Row -->
	<div class="flex justify-between py-3 pr-2 pl-6">
		<!-- Content -->
		<div class="min-w-0 flex-1">
			<!-- Title clamped to 3 lines -->
			<h3
				class="line-clamp-3 text-left text-sm leading-tight font-medium {entity.isDismissed
					? 'text-slate-500 line-through dark:text-slate-400'
					: 'text-slate-900 dark:text-white'}"
			>
				{entity.title}
			</h3>

			<div class="my-2 flex w-full gap-2">
				{#if entity.createdAt}
					<time class="text-xs text-slate-500 dark:text-slate-400">
						Created {getRelativeTimeForStr(entity.createdAt)}
					</time>
				{/if}
				<!-- updated or started -->
				{#if entity.updatedAt && entity.updatedAt !== entity.createdAt}
					<time class="text-xs text-slate-500 dark:text-slate-400">
						Updated {getRelativeTimeForStr(entity.updatedAt)}
					</time>
				{/if}
			</div>

			<Markdown
				class="mt-1"
				content={(entity.description || '').slice(0, 200)}
				size="xs"
				lineClamp={3}
				color="gray"
			/>
		</div>

		<!-- Dismiss/Restore button -->
		{#if showDismissButton && triggerId}
			<div class="-mt-0.5 flex-none">
				<Tooltip
					text={entity.isDismissed ? 'Restore this match' : 'Dismiss this match'}
					position="top"
					delay={0}
				>
					<Button
						variant="ghost"
						size="icon-sm"
						type="button"
						onclick={handleDismiss}
						disabled={isDismissing}
						loading={isDismissing}
						class="{isMobileDevice
							? 'opacity-100'
							: 'opacity-0 transition-opacity group-hover:opacity-100'} {isMobileDevice
							? 'touch-manipulation'
							: ''}"
						title={entity.isDismissed ? 'Restore this match' : 'Dismiss this match'}
					>
						{#if entity.isDismissed}
							<Icon src={ArrowUturnLeft} class="h-3 w-3" micro />
						{:else}
							<Icon src={XMark} class="h-3 w-3" micro />
						{/if}
					</Button>
				</Tooltip>
			</div>
		{/if}
	</div>
	<div class="w-full border-b border-slate-200 dark:border-slate-700"></div>
</div>
