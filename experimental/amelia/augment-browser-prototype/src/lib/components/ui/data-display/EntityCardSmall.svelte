<script lang="ts">
	import { type UnifiedEntity } from '$lib/utils/entity-conversion';
	import { getEntityStateConfig } from '$lib/utils/task-references';
	import { ArrowTopRightOnSquare, Icon, Tag, User } from 'svelte-hero-icons';
	import CustomIcon from '../visualization/CustomIcon.svelte';

	interface Props {
		entity: UnifiedEntity;
		class?: string;
	}

	let { entity, class: className = '' }: Props = $props();

	// Parse metadata if it exists
	let metadata = $derived.by(() => {
		if (!entity.metadata) return null;
		try {
			return typeof entity.metadata === 'string' ? JSON.parse(entity.metadata) : entity.metadata;
		} catch {
			return null;
		}
	});

	function getStateColor(state = ''): string {
		if (!state) return '';
		const stateConfig = getEntityStateConfig(entity.entityType, state);
		if (stateConfig) {
			const colorMap = {
				green: 'text-emerald-700 dark:text-emerald-300',
				red: 'text-red-700 dark:text-red-300',
				purple: 'text-purple-700 dark:text-purple-300',
				blue: 'text-blue-700 dark:text-blue-300',
				gray: 'text-gray-600 dark:text-gray-400'
			};
			return colorMap[stateConfig.color as keyof typeof colorMap] || colorMap.gray;
		}

		// Fallback to original logic
		const lowerState = state.toLowerCase?.();
		if (['open', 'unstarted', 'started', 'in progress'].includes(lowerState)) {
			return 'text-emerald-700 dark:text-emerald-300';
		} else if (['closed', 'done', 'completed'].includes(lowerState)) {
			return 'text-gray-600 dark:text-gray-400';
		} else if (['merged'].includes(lowerState)) {
			return 'text-purple-700 dark:text-purple-300';
		}
		return 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400';
	}
</script>

{#if metadata}
	<!-- Compact Entity Card -->
	<div
		class="group rounded-lg border border-slate-300 bg-white transition-colors hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-800 dark:hover:bg-slate-800/50 {className}"
	>
		<!-- Main Row -->
		<div class="flex items-center justify-between px-4 py-3">
			<!-- Left: Entity info -->
			<div class="flex min-w-0 flex-1 items-center gap-3">
				<!-- Title and metadata -->
				<div class="min-w-0 flex-1">
					<div class="mb-1 flex items-center gap-2">
						{#if entity.url}
							<a
								href={entity.url}
								target="_blank"
								rel="noopener noreferrer"
								class="min-w-0 flex-1 cursor-pointer"
								onclick={(e) => e.stopPropagation()}
							>
								<h3
									class="line-clamp-1 text-left text-sm font-medium text-slate-900 hover:text-blue-600 dark:text-white dark:hover:text-blue-400"
								>
									{entity.title}
								</h3>
							</a>
						{:else}
							<h3
								class="line-clamp-1 min-w-0 flex-1 text-left text-sm font-medium text-slate-900 dark:text-white"
							>
								{entity.title}
							</h3>
						{/if}

						{#if entity.url}
							<a
								href={entity.url}
								target="_blank"
								rel="noopener noreferrer"
								class="flex-shrink-0 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300"
								onclick={(e) => e.stopPropagation()}
								title="Open in external site"
							>
								<Icon src={ArrowTopRightOnSquare} class="h-4 w-4" micro />
							</a>
						{/if}
					</div>

					<!-- Metadata line -->
					<div
						class="flex flex-wrap items-center gap-1.5 text-xs text-slate-500 dark:text-slate-400"
					>
						<!-- Provider icon -->
						<CustomIcon
							icon={entity.providerId}
							size={16}
							class="flex-shrink-0 text-slate-400 dark:text-slate-400"
						/>

						{#if entity.description}
							<span class="line-clamp-1 min-w-0 flex-1">
								{entity.description}
							</span>
						{/if}
					</div>
				</div>
			</div>
		</div>

		<!-- Additional metadata row (if needed) -->
		{#if metadata?.assignee || (metadata?.assignees && metadata?.assignees.length > 0) || (metadata?.labels && metadata?.labels.length > 0)}
			<div class="border-t border-slate-200 px-4 py-2 dark:border-slate-700">
				<div class="flex flex-wrap items-center gap-4 text-xs text-slate-500 dark:text-slate-400">
					<!-- Assignee -->
					{#if metadata?.assignee || (metadata?.assignees && metadata?.assignees.length > 0)}
						{@const assignee = metadata?.assignee || metadata?.assignees?.[0]}
						{#if assignee}
							<div class="flex items-center gap-1.5">
								<Icon src={User} micro class="h-3 w-3" />
								<span>{assignee.name || assignee.login || assignee}</span>
							</div>
						{/if}
					{/if}

					<!-- Labels -->
					{#if metadata?.labels && metadata?.labels.length > 0}
						<div class="flex items-center gap-1.5">
							<Icon src={Tag} micro class="h-3 w-3" />
							<div class="flex flex-wrap gap-1">
								{#each metadata?.labels.filter((label) => label.name?.trim()).slice(0, 3) as label}
									<span
										class="inline-flex items-center rounded border border-blue-200 bg-blue-50 px-1.5 py-0.5 text-xs font-medium text-blue-700 dark:border-blue-800 dark:bg-blue-950/30 dark:text-blue-300"
									>
										{label.name}
									</span>
								{/each}
								{#if metadata?.labels.length > 3}
									<span class="text-slate-400">
										+{metadata?.labels.length - 3} more
									</span>
								{/if}
							</div>
						</div>
					{/if}
				</div>
			</div>
		{/if}
	</div>
{/if}
