<script lang="ts">
	import ReactiveRelativeTime from '../ReactiveRelativeTime.svelte';
	interface Props {
		entity: any; // Raw API entity data
	}

	let { entity }: Props = $props();
</script>

<!-- Timeline-style comment layout -->
<div class="flex gap-2">
	<!-- Author Avatar -->
	{#if entity.author?.avatarUrl}
		<img
			src={entity.author.avatarUrl}
			alt={entity.author.name || entity.author.login}
			class="h-6 w-6 flex-shrink-0 rounded-full border border-slate-200 dark:border-slate-700"
		/>
	{:else}
		<div
			class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-gray-300 dark:bg-gray-600"
		>
			<span class="text-xs font-medium text-gray-600 dark:text-gray-300">
				{entity.author?.login?.charAt(0)?.toUpperCase() || '?'}
			</span>
		</div>
	{/if}

	<!-- Comment bubble -->
	<div class="mt-[2px] min-w-0 flex-1 text-sm">
		<!-- Header with author and timestamp -->
		<div class="mb-2 flex items-center gap-1">
			{#if entity.htmlUrl}
				<a
					href={entity.htmlUrl}
					target="_blank"
					rel="noopener noreferrer"
					class="font-medium text-slate-900 underline hover:text-blue-600 dark:text-slate-100 dark:hover:text-blue-400"
				>
					{entity.author?.login || entity.user?.login || 'Unknown'}
				</a>
			{:else}
				<span class="font-medium text-slate-900 dark:text-slate-100">
					{entity.author?.login || entity.user?.login || 'Unknown'}
				</span>
			{/if}
			<span class="text-sm text-slate-500 dark:text-slate-400">commented</span>
			{#if entity.createdAt}
				<span class="text-sm text-slate-500 dark:text-slate-400"
					><ReactiveRelativeTime date={entity.createdAt} /></span
				>
			{/if}
		</div>

		<!-- Comment content in bordered container -->
		<div
			class="rounded-lg border border-slate-200 bg-white p-3 shadow-sm dark:border-slate-700 dark:bg-slate-800"
		>
			<div class="text-sm text-slate-700 dark:text-slate-300">
				{entity.body || entity.description || 'No comment text'}
			</div>
		</div>
	</div>
</div>
