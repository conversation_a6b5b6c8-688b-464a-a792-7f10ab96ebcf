<script lang="ts">
	// Import specialized components
	import GitHubReviewComment from './GitHubReviewComment.svelte';
	import GitHubReview from './GitHubReview.svelte';
	import GitHubAssignee from './GitHubAssignee.svelte';
	import GitH<PERSON>Commit from './GitHubCommit.svelte';
	import GitH<PERSON>Label from './GitHubLabel.svelte';
	import GitHubRequestedReviewer from './GitHubRequestedReviewer.svelte';
	import GitHubStatus from './GitHubStatus.svelte';
	import GitHubCheckSuite from './GitHubCheckSuite.svelte';
	import GenericRelatedEntity from './GenericRelatedEntity.svelte';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import EntityCard from './EntityCard.svelte';
	import { ChevronDown, ChevronUp, Icon } from 'svelte-hero-icons';
	import { slide } from 'svelte/transition';

	interface Props {
		entity: any; // Raw linked entity with potential related entity arrays
		headerActions?: Snippet;
	}

	let { entity, headerActions }: Props = $props();

	// State management for expand/collapse
	let isExpanded = $state(true);

	// All possible related entity types
	const ALL_ENTITY_TYPES = [
		'reviewComments',
		'commits',
		'linkedIssues',
		'linkedPullRequests',
		'requestedReviewers',
		'reviews',
		'assignees',
		'labels',
		'statuses',
		'checkSuites'
	] as const;

	// Helper function to get entities from the correct location
	function getEntitiesForType(relationshipType: string): any[] {
		// For statuses and checkSuites, look in pullRequest
		// if (relationshipType === 'statuses') {
		// 	return entity.pullRequest?.[relationshipType] || [];
		// }
		// For other types, look in metadata
		return entity.metadata?.[relationshipType] || [];
	}

	// Check if there are any related entities to display
	let hasRelatedEntities = $derived(
		ALL_ENTITY_TYPES.some((key) => {
			const entities = getEntitiesForType(key);
			return entities && entities.length > 0;
		})
	);

	// Toggle function
	function toggleExpanded() {
		isExpanded = !isExpanded;
	}

	// Helper function to get the appropriate component for the main entity
	function getMainEntityComponent() {
		console.log(entity);
		// Try to determine entity type from the entity structure
		if (entity.body && entity.author) {
			return GitHubReviewComment;
		}
		if (entity.commit && entity.sha) {
			return GitHubCommit;
		}
		if (
			entity.state &&
			(entity.state === 'APPROVED' ||
				entity.state === 'PENDING' ||
				entity.state === 'CHANGES_REQUESTED')
		) {
			return GitHubReview;
		}
		if (entity.login || entity.name) {
			return GitHubAssignee;
		}
		if (entity.color && entity.name) {
			return GitHubLabel;
		}
		// Default fallback
		return GenericRelatedEntity;
	}

	// Helper function to get the appropriate component for related entities
	function getRelatedEntityComponent(relationshipType: string) {
		switch (relationshipType) {
			case 'reviewComments':
				return GitHubReviewComment;
			case 'commits':
				return GitHubCommit;
			case 'reviews':
				return GitHubReview;
			case 'assignees':
				return GitHubAssignee;
			case 'requestedReviewers':
				return GitHubRequestedReviewer;
			case 'labels':
				return GitHubLabel;
			case 'statuses':
				return GitHubStatus;
			case 'checkSuites':
				return GitHubCheckSuite;
			case 'linkedIssues':
			case 'linkedPullRequests':
			default:
				return GenericRelatedEntity;
		}
	}

	// Helper function to get readable names for entity types
	function getEntityTypeDisplayName(relationshipType: string): string {
		switch (relationshipType) {
			case 'reviewComments':
				return 'Comments';
			case 'commits':
				return 'Commits';
			case 'reviews':
				return 'Reviews';
			case 'assignees':
				return 'Assignees';
			case 'labels':
				return 'Labels';
			case 'linkedIssues':
				return 'Linked Issues';
			case 'linkedPullRequests':
				return 'Linked Pull Requests';
			case 'requestedReviewers':
				return 'Requested Reviewers';
			case 'statuses':
				return 'Status Checks';
			case 'checkSuites':
				return 'GitHub Actions';
			default:
				return relationshipType;
		}
	}

	// Helper function to get summary text for entity type
	function getEntityTypeSummary(relationshipType: string, entities: any[]): string {
		if (!entities || entities.length === 0) return '';

		switch (relationshipType) {
			case 'reviewComments':
				return `${entities.length} comment${entities.length !== 1 ? 's' : ''}`;
			case 'commits':
				return `${entities.length} commit${entities.length !== 1 ? 's' : ''}`;
			case 'reviews':
				const approved = entities.filter((r) => r.state === 'APPROVED').length;
				const pending = entities.filter((r) => r.state === 'PENDING').length;
				const changes = entities.filter((r) => r.state === 'CHANGES_REQUESTED').length;
				if (approved > 0 || pending > 0 || changes > 0) {
					const parts = [];
					if (approved > 0) parts.push(`${approved} ✓`);
					if (changes > 0) parts.push(`${changes} ✗`);
					if (pending > 0) parts.push(`${pending} ⏳`);
					return parts.join(' ');
				}
				return `${entities.length} review${entities.length !== 1 ? 's' : ''}`;
			case 'assignees':
				return `${entities.length} assignee${entities.length !== 1 ? 's' : ''}`;
			case 'labels':
				return `${entities.length} label${entities.length !== 1 ? 's' : ''}`;
			case 'linkedIssues':
				return `${entities.length} linked issue${entities.length !== 1 ? 's' : ''}`;
			case 'linkedPullRequests':
				return `${entities.length} linked PR${entities.length !== 1 ? 's' : ''}`;
			case 'requestedReviewers':
				return `${entities.length} reviewer${entities.length !== 1 ? 's' : ''}`;
			case 'statuses':
				const successStatuses = entities.filter((s) => s.state === 'success').length;
				const failureStatuses = entities.filter(
					(s) => s.state === 'failure' || s.state === 'error'
				).length;
				const pendingStatuses = entities.filter((s) => s.state === 'pending').length;
				if (successStatuses > 0 || failureStatuses > 0 || pendingStatuses > 0) {
					const parts = [];
					if (successStatuses > 0) parts.push(`${successStatuses} ✓`);
					if (failureStatuses > 0) parts.push(`${failureStatuses} ✗`);
					if (pendingStatuses > 0) parts.push(`${pendingStatuses} ⏳`);
					return parts.join(' ');
				}
				return `${entities.length} status${entities.length !== 1 ? 'es' : ''}`;
			case 'checkSuites':
				const successSuites = entities.filter((c) => c.conclusion === 'success').length;
				const failureSuites = entities.filter(
					(c) =>
						c.conclusion === 'failure' ||
						c.conclusion === 'cancelled' ||
						c.conclusion === 'timed_out'
				).length;
				const pendingSuites = entities.filter(
					(c) => !c.conclusion || c.conclusion === 'in_progress'
				).length;
				if (successSuites > 0 || failureSuites > 0 || pendingSuites > 0) {
					const parts = [];
					if (successSuites > 0) parts.push(`${successSuites} ✓`);
					if (failureSuites > 0) parts.push(`${failureSuites} ✗`);
					if (pendingSuites > 0) parts.push(`${pendingSuites} ⏳`);
					return parts.join(' ');
				}
				return `${entities.length} check${entities.length !== 1 ? 's' : ''}`;
			default:
				return `${entities.length} ${relationshipType}`;
		}
	}
</script>

<!-- Main entity display -->
<div class="space-y-2">
	<!-- Display the main entity using the appropriate component -->
	{#if entity}
		<EntityCard {entity} {headerActions} />
	{/if}

	<!-- Related entities summary row -->
	{#if hasRelatedEntities}
		<div class="rounded-lg transition-colors duration-200 dark:border-slate-700 dark:bg-slate-800">
			<!-- Summary header with toggle -->
			<!-- <button
				class="flex w-full cursor-pointer items-center justify-between rounded-lg p-3 transition-colors duration-200 hover:bg-slate-100 dark:hover:bg-slate-700"
				onclick={toggleExpanded}
				aria-expanded={isExpanded}
				aria-label={isExpanded ? 'Collapse related entities' : 'Expand related entities'}
			>
				<div class="flex flex-wrap items-center gap-2 text-sm">
					<div class="flex items-center gap-2">
						<CustomIcon
							icon={entity.providerId || 'github'}
							size={14}
							class="flex-shrink-0 text-slate-600 dark:text-slate-400"
						/>
					</div>

					{#each ALL_ENTITY_TYPES as relationshipType}
						{@const entities = getEntitiesForType(relationshipType)}
						{#if entities && entities.length > 0}
							{@const summary = getEntityTypeSummary(relationshipType, entities)}
							<span
								class="rounded-full border border-slate-200 bg-white px-2 py-1 text-xs font-medium text-slate-600 dark:border-slate-600 dark:bg-slate-700 dark:text-slate-300"
							>
								{summary}
							</span>
						{/if}
					{/each}
				</div>

				<div class="ml-2 flex-shrink-0">
					<div class="transition-transform duration-200 {isExpanded ? 'rotate-180' : ''}">
						<Icon src={ChevronUp} class="h-4 w-4 text-slate-500 dark:text-slate-400" micro />
					</div>
				</div>
			</button> -->

			<!-- Expanded content -->
			{#if isExpanded}
				<div
					class="mt-3 overflow-hidden transition-all duration-300"
					transition:slide={{ axis: 'y' }}
				>
					<div class="space-y-6 p-4">
						{#each ALL_ENTITY_TYPES as relationshipType}
							{@const entities = getEntitiesForType(relationshipType)}
							{#if entities && entities.length > 0}
								{@const displayName = getEntityTypeDisplayName(relationshipType)}

								<div class="space-y-3">
									<!-- Section header -->
									<div
										class="flex items-center gap-2 border-b border-slate-100 pb-2 dark:border-slate-600"
									>
										<h4 class="text-sm font-semibold text-slate-900 dark:text-slate-100">
											{displayName}
										</h4>
										<span
											class="rounded-full bg-slate-100 px-2 py-0.5 text-xs text-slate-500 dark:bg-slate-700 dark:text-slate-400"
										>
											{entities.length}
										</span>
									</div>

									<!-- Entity list -->
									<div class="space-y-5">
										{#each entities as relatedEntity}
											{@const RelatedEntityComponent = getRelatedEntityComponent(relationshipType)}
											<div class="pl-0">
												<RelatedEntityComponent entity={relatedEntity} mainEntity={entity} />
											</div>
										{/each}
									</div>
								</div>
							{/if}
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
