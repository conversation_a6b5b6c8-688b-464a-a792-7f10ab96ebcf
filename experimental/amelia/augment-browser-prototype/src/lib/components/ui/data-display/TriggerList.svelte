<script lang="ts">
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { normalizedTriggers, shouldShowTriggerSkeleton } from '$lib/stores/global-state.svelte';
	import { Plus } from 'svelte-hero-icons';
	import Button from '../navigation/Button.svelte';
	import TriggerListItem from './TriggerListItem.svelte';
	import MatchRowSkeleton from './MatchRowSkeleton.svelte';

	interface Props {
		showDismissed?: boolean;
		onTriggerClick: (trigger: NormalizedTrigger) => void;
		onCreateTrigger?: () => void;
	}

	let { showDismissed = false, onTriggerClick, onCreateTrigger }: Props = $props();

	// Get all triggers and sort them by type, keeping scheduled ones at the bottom
	let sortedTriggers = $derived.by(() => {
		const allTriggers = $normalizedTriggers;
		if (!allTriggers) return [];

		return [...allTriggers].sort((a, b) => {
			const aProvider = a.provider;
			const bProvider = b.provider;

			// If one is scheduled and the other isn't, scheduled goes to bottom
			if (aProvider === 'schedule' && bProvider !== 'schedule') return 1;
			if (bProvider === 'schedule' && aProvider !== 'schedule') return -1;

			// If both are scheduled or both are not scheduled, sort alphabetically
			return aProvider.localeCompare(bProvider);
		});
	});

	function handleCreateTrigger() {
		if (onCreateTrigger) {
			onCreateTrigger();
		}
	}
</script>

<div class="mb-3 px-6">
	<div class="mb-1 flex w-full items-center justify-between">
		<h3 class="text-sm font-medium text-slate-700 dark:text-slate-300">Inbox</h3>
	</div>
	<p class="text-xs text-slate-500 dark:text-slate-400">
		Automatically create an agent to tackle work from GitHub or Linear.
	</p>
</div>

{#if $shouldShowTriggerSkeleton}
	<div class="px-6 py-4">
		{#each Array(3).fill(null) as _}
			<div class="mb-4 last:mb-0">
				<MatchRowSkeleton />
			</div>
		{/each}
	</div>
{:else if sortedTriggers.length === 0}
	<div class="py-8 text-center">
		<p class="text-sm text-slate-500 dark:text-slate-400">No triggers found</p>
		<p class="mt-1 text-xs text-slate-400 dark:text-slate-500">
			Create triggers to see matching entities here
		</p>
	</div>
{:else}
	<div class="">
		{#each sortedTriggers as trigger (trigger.id)}
			<TriggerListItem {trigger} {showDismissed} {onTriggerClick} />
		{/each}
	</div>
{/if}

<Button variant="ghost" class="mt-1 ml-2.5" size="sm" icon={Plus} onclick={handleCreateTrigger}>
	Create new Action
</Button>
