<script lang="ts">
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { getMatchingEntitiesForTrigger } from '$lib/stores/global-state.svelte';
	import { providerAuthStates } from '$lib/stores/provider-auth';
	import { ArrowRight } from 'svelte-hero-icons';
	import Button from '../navigation/Button.svelte';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import UnconfiguredProviderNotification from '../notifications/UnconfiguredProviderNotification.svelte';

	interface Props {
		trigger: NormalizedTrigger;
		showDismissed?: boolean;
		onTriggerClick: (trigger: NormalizedTrigger) => void;
	}

	let { trigger, showDismissed = false, onTriggerClick }: Props = $props();

	// Get matching entities for this trigger
	let matchingEntitiesStore = $derived(getMatchingEntitiesForTrigger(trigger.id));
	let matchingEntities = $derived($matchingEntitiesStore);

	// Filter entities based on showDismissed flag
	let filteredEntities = $derived.by(() => {
		if (!matchingEntities) return [];
		return matchingEntities.filter((entity) => showDismissed || !entity.isDismissed);
	});

	// Helper function to check if a trigger uses an unconfigured provider
	function isTriggerProviderUnconfigured(trigger: NormalizedTrigger): boolean {
		// Skip cron triggers as they don't need external provider auth
		if (trigger.provider === 'schedule') return false;
		return !$providerAuthStates[trigger.provider]?.isConfigured || false;
	}

	function handleTriggerClick() {
		onTriggerClick(trigger);
	}
</script>

<div class="">
	<!-- Trigger Header -->
	<div
		class="flex cursor-pointer items-center justify-between gap-1 px-6 py-2 transition-colors hover:bg-slate-50 dark:hover:bg-slate-900/50"
		role="button"
		tabindex="0"
		onclick={handleTriggerClick}
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				e.preventDefault();
				handleTriggerClick();
			}
		}}
	>
		<div class="flex w-full items-center gap-2">
			<CustomIcon icon={trigger.provider} size={15} />
			<h3 class="line-clamp-1 flex-1 text-sm font-medium text-slate-700 dark:text-slate-300">
				{trigger.name}
			</h3>
			{#if filteredEntities.length > 0}
				<span
					class="ml-auto flex h-4 w-4 flex-none items-center justify-center rounded-full bg-slate-100 text-[0.66rem] font-medium text-slate-600 dark:bg-slate-700 dark:text-slate-200"
				>
					{filteredEntities.length}
				</span>
			{/if}
		</div>
		<!-- Right arrow -->
		<Button
			icon={ArrowRight}
			size="icon-sm"
			variant="ghost-light"
			class="transition-transform duration-200"
		/>
	</div>

	<!-- Unconfigured Provider Notification -->
	{#if isTriggerProviderUnconfigured(trigger)}
		<div class="px-6 pt-1 pb-3">
			<UnconfiguredProviderNotification
				unconfiguredProviders={[
					{
						providerId: trigger.provider,
						providerName: trigger.provider.charAt(0).toUpperCase() + trigger.provider.slice(1),
						triggerCount: 1,
						triggers: [
							{
								id: trigger.id,
								provider: trigger.provider,
								name: trigger.name
							}
						]
					}
				]}
				onAuthSuccess={(providerId) => {
					console.log(`Provider ${providerId} connected successfully`);
				}}
				class="text-sm"
			/>
		</div>
	{/if}
</div>
