<script lang="ts">
	// Skeleton loader for DashboardEntityTable
	interface Props {
		rows?: number;
	}

	let { rows = 5 }: Props = $props();
</script>

<div class="w-full">
	<!-- Table Header -->
	<div class="grid grid-cols-12 gap-4 px-6 py-3 bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700 text-sm font-medium text-slate-600 dark:text-slate-400">
		<div class="col-span-1">
			<div class="w-8 h-4 bg-slate-300 dark:bg-slate-600 rounded animate-pulse"></div>
		</div>
		<div class="col-span-4">
			<div class="w-16 h-4 bg-slate-300 dark:bg-slate-600 rounded animate-pulse"></div>
		</div>
		<div class="col-span-2">
			<div class="w-12 h-4 bg-slate-300 dark:bg-slate-600 rounded animate-pulse"></div>
		</div>
		<div class="col-span-2">
			<div class="w-16 h-4 bg-slate-300 dark:bg-slate-600 rounded animate-pulse"></div>
		</div>
		<div class="col-span-2">
			<div class="w-20 h-4 bg-slate-300 dark:bg-slate-600 rounded animate-pulse"></div>
		</div>
		<div class="col-span-1">
			<div class="w-12 h-4 bg-slate-300 dark:bg-slate-600 rounded animate-pulse"></div>
		</div>
	</div>

	<!-- Table Rows -->
	{#each Array(rows) as _, i}
		<div class="grid grid-cols-12 gap-4 px-6 py-4 border-b border-slate-100 dark:border-slate-800 hover:bg-slate-50 dark:hover:bg-slate-800/50 animate-pulse">
			<!-- Provider Icon -->
			<div class="col-span-1 flex items-center">
				<div class="w-5 h-5 bg-slate-300 dark:bg-slate-600 rounded"></div>
			</div>

			<!-- Title and Description -->
			<div class="col-span-4 space-y-2">
				<div class="w-3/4 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
				<div class="w-full h-3 bg-slate-200 dark:bg-slate-700 rounded"></div>
				<div class="w-1/2 h-3 bg-slate-200 dark:bg-slate-700 rounded"></div>
			</div>

			<!-- Entity Type -->
			<div class="col-span-2 flex items-center">
				<div class="w-20 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
			</div>

			<!-- Status -->
			<div class="col-span-2 flex items-center gap-2">
				<div class="w-2 h-2 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
				<div class="w-16 h-4 bg-slate-300 dark:bg-slate-600 rounded"></div>
			</div>

			<!-- Updated -->
			<div class="col-span-2 flex items-center">
				<div class="w-24 h-4 bg-slate-200 dark:bg-slate-700 rounded"></div>
			</div>

			<!-- Actions -->
			<div class="col-span-1 flex items-center justify-end gap-2">
				<div class="w-8 h-8 bg-slate-300 dark:bg-slate-600 rounded"></div>
				<div class="w-8 h-8 bg-slate-200 dark:bg-slate-700 rounded"></div>
			</div>
		</div>
	{/each}
</div>
