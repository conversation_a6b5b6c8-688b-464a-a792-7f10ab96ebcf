<script lang="ts">
	import { RemoteAgentStatus, type CleanTriggerExecution } from '$lib/api/unified-client';
	import AuggieAvatar from '$lib/components/ui/visualization/AuggieAvatar.svelte';
	import {
		getAgent,
		getEntityByAgent,
		ensureEntityLoadedForAgent,
		getTriggerForExecution
	} from '$lib/stores/global-state.svelte';
	import { getRelativeTime, getRelativeTimeForStr } from '$lib/utils/time';
	import { Icon, NoSymbol } from 'svelte-hero-icons';
	import { sendEntityRow, receiveEntityRow } from '$lib/utils/shared-transitions';
	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';
	import Pill from '$lib/components/ui/navigation/Pill.svelte';
	import RemoteAgentStatusIndicator from '$lib/components/ui/feedback/RemoteAgentStatusIndicator.svelte';

	interface Props {
		execution: CleanTriggerExecution;
		onExecutionClick?: (execution: any) => void;
		isHighlighted?: boolean; // Whether this execution should be highlighted
		onMouseEnter?: (entity?: any) => void; // Mouse enter handler
		onMouseLeave?: () => void; // Mouse leave handler
	}

	let {
		execution,
		onExecutionClick,
		isHighlighted = false,
		onMouseEnter,
		onMouseLeave
	}: Props = $props();

	let triggerInfo = $derived(getTriggerForExecution(execution.id));
	let trigger = $derived($triggerInfo?.data || null);

	// Track loading state for new agents
	let isLoadingAgent = $state(false);
	let loadingTimeout: NodeJS.Timeout | null = null;

	// Get execution agent
	let executionAgentStore = $derived(
		execution.remoteAgentId ? getAgent(execution.remoteAgentId) : null
	);
	let executionAgent = $derived($executionAgentStore?.data);

	// Check if this is a newly created agent that might still be loading
	$effect(() => {
		if (execution.remoteAgentId && !executionAgent) {
			// Check if this execution was created recently (within last 30 seconds)
			const executionTime = execution.startedAt;
			const now = Date.now();
			console.log({ executionTime });
			const isRecent = now - executionTime < 30000; // 30 seconds

			if (isRecent) {
				isLoadingAgent = true;
				// Set a timeout to stop showing loading after 10 seconds
				if (loadingTimeout) clearTimeout(loadingTimeout);
				loadingTimeout = setTimeout(() => {
					isLoadingAgent = false;
				}, 10000);
			} else {
				isLoadingAgent = false;
			}
		} else if (executionAgent) {
			isLoadingAgent = false;
			if (loadingTimeout) {
				clearTimeout(loadingTimeout);
				loadingTimeout = null;
			}
		}
	});

	const linkedEntityRaw = $derived(
		execution.remoteAgentId ? getEntityByAgent(execution.remoteAgentId) : null
	);
	const linkedEntity = $derived($linkedEntityRaw?.data);

	// Trigger loading when agent changes
	$effect(() => {
		if (execution.remoteAgentId) {
			ensureEntityLoadedForAgent(execution.remoteAgentId);
		}
	});

	function handleExecutionClick(e: Event) {
		e.stopPropagation();
		onExecutionClick?.(executionAgent);
	}
</script>

<!-- Execution Row with distinct card-like design -->
<div
	class="group relative {isHighlighted
		? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950'
		: ''}"
	role="button"
	tabindex="0"
	in:receiveEntityRow={{ key: execution.entityId || execution.id }}
	out:sendEntityRow={{ key: execution.entityId || execution.id }}
	onclick={() => {
		console.log('Execution clicked:', execution, trigger, linkedEntity);
	}}
	onkeydown={(e) => {
		if (e.key === 'Enter' || e.key === ' ') {
			console.log('Execution clicked:', execution, trigger, linkedEntity);
		}
	}}
	onmouseenter={() => onMouseEnter?.(linkedEntity)}
	onmouseleave={() => onMouseLeave?.()}
>
	<!-- Status Header -->
	<!-- <div class="flex items-center justify-between px-4 py-2 bg-slate-50/50 dark:bg-slate-800/30 border-b border-slate-100 dark:border-slate-800">
		<div class="flex items-center gap-2">
			<div class="w-2 h-2 rounded-full {statusConfig.dot}"></div>
			<Icon src={statusConfig.icon} class="w-3.5 h-3.5 {statusConfig.text}" />
			<span class="text-xs font-medium {statusConfig.text}">
				{statusConfig.label}
			</span>
			<span class="text-xs text-slate-500 dark:text-slate-400 font-mono">
				#{execution.id?.slice(-6) || 'N/A'}
			</span>
		</div>
		<time class="text-xs text-slate-500 dark:text-slate-400 font-medium">
			{formattedDate}
		</time>
	</div> -->

	<!-- Main Content -->
	<div class="p-4">
		<!-- Title with execution context -->
		<div class="flex items-start gap-3">
			{#if executionAgent}
				<AuggieAvatar
					colorSeed={execution.remoteAgentId}
					faceSeed={execution.remoteAgentId}
					size={36}
				/>
			{:else if isLoadingAgent}
				<div
					class="mx-2 flex h-6 w-6 items-center justify-center rounded-full bg-slate-300 dark:bg-slate-600"
				>
					<LoadingIndicator variant="spinner" size="sm" />
				</div>
			{:else}
				<Icon src={NoSymbol} class="mx-1.5 h-6 w-6 text-slate-200 dark:text-slate-800" micro />
			{/if}
			<div
				class="flex min-w-0 flex-1 flex-col items-start gap-1 text-xs text-slate-500 dark:text-slate-400"
			>
				<button onclick={handleExecutionClick} class="cursor-pointer">
					<h4
						class="line-clamp-2 text-left text-sm leading-tight font-medium text-slate-900 dark:text-white"
					>
						{linkedEntity?.title || `Working on ${trigger?.name || 'task'}`}
					</h4>
				</button>
				{#if trigger?.name}
					<div class="">
						via {trigger.name}
					</div>
				{/if}

				<!-- Execution Details -->
				<div class="flex items-center justify-between">
					<!-- Left side: timing and status info -->
					<div class="flex items-center gap-1.5 text-xs">
						<!-- <div class="flex items-center gap-1">
					<Icon src={Clock} class="w-3 h-3 text-slate-400" />
					<span class="text-slate-600 dark:text-slate-400">
						Started {formattedStartTime}
					</span>
				</div> -->

						<div class="line-clamp-1 flex items-center gap-1 text-slate-500 dark:text-slate-400">
							Started {getRelativeTime(execution.startedAt)}
						</div>

						{#if trigger?.name}
							<span class="text-slate-400">•</span>
							<span>via {trigger.name}</span>
						{/if}
					</div>
				</div>
			</div>
			{#if executionAgent}
				<RemoteAgentStatusIndicator
					status={executionAgent?.status}
					workspaceStatus={executionAgent?.workspaceStatus}
					hasUpdates={executionAgent?.hasUpdates}
					isExpanded
					size="md"
				/>
			{:else if execution.remoteAgentId && isLoadingAgent}
				<!-- Show loading state for newly created agents -->
				<RemoteAgentStatusIndicator
					status={RemoteAgentStatus.AGENT_STARTING}
					isExpanded
					size="md"
				/>
			{:else}
				<Pill variant="gray" size="sm">Agent deleted</Pill>
			{/if}
		</div>
	</div>
</div>
