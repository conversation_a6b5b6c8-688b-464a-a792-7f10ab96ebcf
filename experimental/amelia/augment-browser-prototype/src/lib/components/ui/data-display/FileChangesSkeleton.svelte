<script lang="ts">
	// Skeleton loader for File Changes section
	// Add some randomness to make it look more natural
	let fileCount = Math.floor(Math.random() * 4) + 2; // 2-5 files
</script>

<!-- File Changes Skeleton -->
<div class="space-y-2 animate-pulse">
	{#each Array(fileCount) as _, i}
		<div class="rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 p-3">
			<div class="flex items-center justify-between">
				<!-- File info -->
				<div class="flex items-center gap-2 flex-1">
					<!-- File icon -->
					<div class="relative w-4 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1}s;"></div>
					</div>
					<!-- File path -->
					<div class="relative w-48 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1 + 0.1}s;"></div>
					</div>
				</div>
				<!-- Change stats -->
				<div class="flex items-center gap-2">
					<div class="relative w-8 h-3 bg-green-200 dark:bg-green-800 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1 + 0.2}s;"></div>
					</div>
					<div class="relative w-8 h-3 bg-red-200 dark:bg-red-800 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1 + 0.3}s;"></div>
					</div>
				</div>
			</div>
		</div>
	{/each}
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
