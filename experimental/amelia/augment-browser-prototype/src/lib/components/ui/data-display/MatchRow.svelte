<script lang="ts">
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import UserAvatar from '../visualization/UserAvatar.svelte';
	import Button from '../navigation/Button.svelte';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import { openAgentCreationModal } from '$lib/stores/modal';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { CleanRemoteAgent, CleanTriggerExecution } from '$lib/api/unified-client';
	import {
		getAgent,
		getExecutionForAgent,
		getExecutionsForTrigger,
		getTriggerForAgent,
		getTriggerForEntity,
		globalState
	} from '$lib/stores/global-state.svelte';
	import { ArrowRight, ArrowUturnLeft, Icon, User, XMark } from 'svelte-hero-icons';
	import { sendEntityRow, receiveEntityRow } from '$lib/utils/shared-transitions';
	import Markdown from '../content/Markdown.svelte';
	import Portal from '../overlays/Portal.svelte';
	import { onMount, onDestroy } from 'svelte';
	import { getRelativeTimeForStr } from '$lib/utils/time';
	import { getProviderDisplayName, getEntityTypeDisplayName } from '$lib/config/entity-types';
	import { dismissEntityAndSync } from '$lib/stores/data-operations.svelte';
	import { addToast } from '$lib/stores/toast';
	import Tooltip from '../overlays/Tooltip.svelte';

	interface Props {
		entity: UnifiedEntity;
		showActions?: boolean;
		showDismissButton?: boolean; // Whether to show dismiss button
		triggerId?: string; // Required for dismiss functionality
		class?: string;
		isHovered?: boolean; // Whether this row is being hovered
		showHoverCard?: boolean; // Whether to show hover card
		hideTriggerName?: boolean; // Whether to hide the "via {trigger.name}" line
		onAgentClick?: (agent: CleanRemoteAgent) => void;
		onMouseEnter?: ({ execution }: { execution: CleanTriggerExecution | undefined }) => void;
		onMouseLeave?: ({ execution }: { execution: CleanTriggerExecution | undefined }) => void;
		onclick?: ({
			entity,
			trigger
		}: {
			entity: UnifiedEntity;
			trigger: NormalizedTrigger | null;
		}) => void;
	}

	let {
		entity,
		onAgentClick,
		showActions = true,
		showDismissButton = false,
		triggerId,
		onclick,
		class: className = '',
		isHovered = false,
		showHoverCard = true,
		hideTriggerName = false,
		onMouseEnter,
		onMouseLeave
	}: Props = $props();

	// Dismiss functionality
	let isDismissing = $state(false);

	async function handleDismiss(event: Event) {
		event.stopPropagation(); // Prevent triggering row click

		if (!triggerId || !entity.id || isDismissing) return;

		isDismissing = true;
		try {
			// Toggle the dismissed status
			const newDismissedStatus = !entity.isDismissed;
			await dismissEntityAndSync(triggerId, entity.id, newDismissedStatus);
			// addToast({
			// 	type: 'success',
			// 	message: newDismissedStatus
			// 		? 'Entity dismissed successfully'
			// 		: 'Entity restored successfully'
			// });
		} catch (error) {
			console.error('Failed to toggle entity dismiss status:', error);
			addToast({
				type: 'error',
				message: 'Failed to update entity status'
			});
		} finally {
			isDismissing = false;
		}
	}

	let width = $state(0);

	// Hover popup state
	let isHovering = $state(false);
	let hoverTimeout: ReturnType<typeof setTimeout> | null = null;
	let cardElement = $state<HTMLElement | null>(null);
	let showPopupBelow = $state(true);
	let popupPosition = $state({ top: 0, left: 0, width: 0 });

	// Detect if we're on a mobile device (screen size based)
	let isMobileDevice = $state(false);

	let triggerInfo = $derived(getTriggerForEntity(entity));
	let trigger = $derived($triggerInfo?.data || null);

	let executionInfo = $derived(getExecutionForAgent(entity.id || ''));
	let execution = $derived($executionInfo || null);

	let linkedRemoteAgentStore = $derived(getAgent(execution?.remoteAgentId || ''));
	let linkedRemoteAgent = $derived($linkedRemoteAgentStore?.data || undefined);

	// Check for mobile device based on screen size
	$effect(() => {
		if (typeof window !== 'undefined') {
			const updateMobileState = () => {
				isMobileDevice = window.innerWidth < 768; // Mobile/tablet breakpoint
			};

			updateMobileState();
			window.addEventListener('resize', updateMobileState);

			return () => {
				window.removeEventListener('resize', updateMobileState);
			};
		}
	});

	// Determine if hover cards should be shown (disabled on mobile or when explicitly disabled)
	const shouldShowHoverCard = $derived(showHoverCard && !isMobileDevice);

	// Hide popup on scroll
	function handleScroll() {
		if (isHovering) {
			isHovering = false;
			if (hoverTimeout) {
				clearTimeout(hoverTimeout);
				hoverTimeout = null;
			}
		}
	}

	// Set up scroll event listener
	onMount(() => {
		// Listen for scroll events on window and any scrollable containers
		window.addEventListener('scroll', handleScroll, { passive: true });
		document.addEventListener('scroll', handleScroll, { passive: true });

		// Also listen for scroll events on any parent containers
		let element = cardElement?.parentElement;
		while (element) {
			const style = window.getComputedStyle(element);
			if (
				style.overflow === 'auto' ||
				style.overflow === 'scroll' ||
				style.overflowY === 'auto' ||
				style.overflowY === 'scroll'
			) {
				element.addEventListener('scroll', handleScroll, { passive: true });
			}
			element = element.parentElement;
		}
	});

	onDestroy(() => {
		window.removeEventListener('scroll', handleScroll);
		document.removeEventListener('scroll', handleScroll);

		// Remove listeners from parent containers
		let element = cardElement?.parentElement;
		while (element) {
			element.removeEventListener('scroll', handleScroll);
			element = element.parentElement;
		}

		if (hoverTimeout) {
			clearTimeout(hoverTimeout);
		}
	});

	// Helper functions for entity data (similar to EntityDetailsDrawer)
	function getDescription(): string {
		if (entity.description) return entity.description;
		if (entity.metadata?.description && typeof entity.metadata.description === 'string')
			return entity.metadata.description;
		return '';
	}

	function getCreatedTime(): string | null {
		const createdAt = entity.metadata?.created_at || entity.metadata?.createdAt;
		if (createdAt && typeof createdAt === 'string') {
			return getRelativeTimeForStr(createdAt);
		}
		return null;
	}

	function getUpdatedTime(): string | null {
		const updatedAt = entity.metadata?.updated_at || entity.metadata?.updatedAt;
		if (updatedAt && typeof updatedAt === 'string') {
			return getRelativeTimeForStr(updatedAt);
		}
		return null;
	}

	function getAssignee(): string | null {
		const assignee = entity.metadata?.assignee;
		if (assignee) {
			if (typeof assignee === 'string') return assignee;
			if (typeof assignee === 'object' && assignee !== null) {
				return (
					(assignee as any)?.display_name ||
					(assignee as any)?.name ||
					(assignee as any)?.login ||
					null
				);
			}
		}
		return null;
	}

	function getAuthor(): { name: string; login?: string } | null {
		const author = entity.metadata?.author;
		if (author && typeof author === 'object' && author !== null) {
			const name =
				(author as any)?.display_name ||
				(author as any)?.name ||
				(author as any)?.login ||
				'Unknown';
			const login = (author as any)?.login;
			return { name, login };
		}
		return null;
	}

	function getLabels(): string[] {
		const labels = entity.labels || entity.metadata?.labels;
		if (Array.isArray(labels)) {
			return labels
				.map((label) => (typeof label === 'string' ? label : label?.name || 'Label'))
				.slice(0, 6);
		}
		return [];
	}

	function getGitHubAvatarUrl(login: string | undefined, size: number = 32): string | null {
		if (!login) return null;
		return `https://github.com/${login}.png?size=${size}`;
	}

	function getStatePill(state: string | undefined, isDraft: boolean = false) {
		if (!state) return null;

		const providerId = (entity?.metadata?.provider as string) || 'github';
		const stateKey = state.toLowerCase();

		if (isDraft) {
			return {
				label: 'Draft',
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-600 dark:text-slate-400',
				borderColor: 'border-slate-300 dark:border-slate-600'
			};
		}

		if (providerId === 'github') {
			const githubStates: Record<string, any> = {
				open: {
					label: 'Open',
					bgColor: 'bg-green-50 dark:bg-green-950/30',
					textColor: 'text-green-700 dark:text-green-300',
					borderColor: 'border-green-200 dark:border-green-800'
				},
				closed: {
					label: 'Closed',
					bgColor: 'bg-red-50 dark:bg-red-950/30',
					textColor: 'text-red-700 dark:text-red-300',
					borderColor: 'border-red-200 dark:border-red-800'
				},
				merged: {
					label: 'Merged',
					bgColor: 'bg-purple-50 dark:bg-purple-950/30',
					textColor: 'text-purple-700 dark:text-purple-300',
					borderColor: 'border-purple-200 dark:border-purple-800'
				}
			};
			return (
				githubStates[stateKey] || {
					label: state,
					bgColor: 'bg-slate-50 dark:bg-slate-800',
					textColor: 'text-slate-700 dark:text-slate-300',
					borderColor: 'border-slate-200 dark:border-slate-600'
				}
			);
		}

		// Default state styling
		return {
			label: state,
			bgColor: 'bg-slate-50 dark:bg-slate-800',
			textColor: 'text-slate-700 dark:text-slate-300',
			borderColor: 'border-slate-200 dark:border-slate-600'
		};
	}

	// Hover handlers
	function handleMouseEnter() {
		// Don't show hover cards on mobile devices
		if (!shouldShowHoverCard) {
			onMouseEnter?.({ execution });
			return;
		}

		if (hoverTimeout) {
			clearTimeout(hoverTimeout);
		}

		hoverTimeout = setTimeout(() => {
			isHovering = true;

			// Calculate position
			if (cardElement) {
				const rect = cardElement.getBoundingClientRect();
				const viewportHeight = window.innerHeight;
				const viewportWidth = window.innerWidth;
				const spaceBelow = viewportHeight - rect.bottom;
				const spaceAbove = rect.top;

				// Show below if there's enough space (300px), otherwise show above
				showPopupBelow = spaceBelow > 300 || spaceBelow > spaceAbove;
				const padding = 0;

				// Calculate horizontal position (keep within viewport)
				const popupWidth = 400; // Approximate popup width
				let left = rect.left;
				if (left + popupWidth > viewportWidth - padding) {
					left = viewportWidth - popupWidth - padding;
				}
				if (left < padding) {
					left = padding;
				}

				// Calculate vertical position
				const top = showPopupBelow ? rect.bottom : rect.top;
				console.log('top', top);

				popupPosition = {
					top,
					left,
					width: Math.min(popupWidth, viewportWidth - 32)
				};
			}
		}, 300); // 300ms delay before showing

		onMouseEnter?.({ execution });
	}

	function handleMouseLeave() {
		if (hoverTimeout) {
			clearTimeout(hoverTimeout);
			hoverTimeout = null;
		}

		isHovering = false;
		onMouseLeave?.({ execution });
	}
</script>

<!-- Sleek GitHub/Vercel-style Row -->
<div
	role="listitem"
	class="group @container relative border-b border-slate-200 transition-colors hover:bg-slate-50 dark:border-slate-700 dark:hover:bg-slate-800/50 {linkedRemoteAgent
		? 'bg-slate-50 dark:bg-slate-800'
		: 'bg-white dark:bg-slate-900'} {isHovered
		? '!border-blue-200 !bg-blue-50 dark:!border-blue-800 dark:!bg-blue-950'
		: ''} {hideTriggerName ? 'border-b-0' : ''} {onclick
		? 'cursor-pointer'
		: ''} {entity.isDismissed ? 'opacity-50' : ''} {className}"
	in:receiveEntityRow={{ key: entity.id }}
	out:sendEntityRow={{ key: entity.id }}
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
	bind:clientWidth={width}
	bind:this={cardElement}
	onclick={() => onclick({ entity, trigger })}
>
	<!-- Main Row -->
	<div class="flex items-center justify-between px-4 {hideTriggerName ? 'py-2' : 'py-3'}">
		<!-- Left: Entity info -->
		<div class="flex min-w-0 flex-1 items-center gap-3">
			<!-- Title and metadata -->
			<div class="flex min-w-0 flex-1 flex-col gap-1">
				<div class="flex items-center gap-2">
					<h3 class="line-clamp-1 text-left text-sm font-medium text-slate-900 dark:text-white">
						{entity.title}
					</h3>
					{#if entity.isDismissed}
						<span
							class="inline-flex items-center rounded border border-slate-300 bg-slate-100 px-1.5 py-0.5 text-xs font-medium text-slate-600 dark:border-slate-600 dark:bg-slate-700 dark:text-slate-300"
						>
							Dismissed
						</span>
					{/if}
					{#if entity.labels && entity.labels.length > 0}
						<div class="hidden gap-1 @lg:flex">
							{#each entity.labels.slice(0, 2) as label}
								<span
									class="inline-flex items-center rounded border border-blue-200 bg-blue-50 px-1.5 py-0.5 text-xs font-medium text-blue-700 dark:border-blue-800 dark:bg-blue-950/30 dark:text-blue-300"
								>
									{typeof label === 'string' ? label : label?.name || 'Label'}
								</span>
							{/each}
						</div>
					{/if}
				</div>

				<div class="-mt-0.5 mb-1 flex w-full gap-2">
					{#if entity.createdAt}
						<time class="text-xs text-slate-500 dark:text-slate-400">
							Created {getRelativeTimeForStr(entity.createdAt)}
						</time>
					{/if}
					<!-- updated or started -->
					{#if entity.updatedAt && entity.updatedAt !== entity.createdAt}
						<time class="text-xs text-slate-500 dark:text-slate-400">
							Updated {getRelativeTimeForStr(entity.updatedAt)}
						</time>
					{/if}
				</div>

				{#if !hideTriggerName}
					<!-- Metadata line -->
					<div
						class="flex items-center gap-1.5 text-xs whitespace-nowrap text-slate-500 dark:text-slate-400"
					>
						<!-- Provider icon -->
						<CustomIcon icon={entity.providerId} size={16} class="flex-shrink-0" />

						<!-- <Icon src={getEntityTypeIcon(entity.providerId, entity.entityType)} class="w-4 h-4" micro /> -->

						{#if entity.author}
							<div class="flex items-center gap-1.5">
								<UserAvatar user={entity.author} providerId={entity.providerId} size={16} />
								<span class="hidden @[500px]:inline">
									{typeof entity.author === 'string'
										? entity.author
										: entity.author?.name || entity.author?.login || 'Unknown'}
								</span>
							</div>
						{/if}

						{#if entity.assignees && entity.assignees.length > 0}
							{#if entity.author}
								<Icon src={ArrowRight} class="h-3 w-3 text-slate-400" mini />
							{/if}
							<div class="flex items-center gap-1.5">
								<UserAvatar user={entity.assignees[0]} providerId={entity.providerId} size={16} />
								<span class="hidden @[500px]:inline">
									{entity.assignees[0]?.display_name || entity.assignees[0]?.name}
								</span>
								{#if entity.assignees.length > 1}
									<span class="hidden text-slate-400 @[500px]:inline"
										>+{entity.assignees.length - 1}</span
									>
								{/if}
							</div>
						{/if}

						{#if trigger?.name}
							<span class="text-slate-400">•</span>
							<span>via {trigger.name}</span>
						{/if}
					</div>
				{/if}
			</div>
		</div>

		<!-- Right: Action buttons -->
		{#if showActions || showDismissButton}
			<div class="flex flex-shrink-0 items-center gap-2">
				<!-- Dismiss/Restore button -->
				{#if showDismissButton && triggerId}
					<Tooltip
						text={entity.isDismissed ? 'Restore this match' : 'Dismiss this match'}
						position="top"
						delay={0}
					>
						<Button
							variant="ghost"
							size="icon-sm"
							onclick={handleDismiss}
							disabled={isDismissing}
							loading={isDismissing}
							class="{isMobileDevice
								? 'opacity-100'
								: 'opacity-0 transition-opacity group-hover:opacity-100'} {isMobileDevice
								? 'touch-manipulation'
								: ''}"
							title={entity.isDismissed ? 'Restore this match' : 'Dismiss this match'}
						>
							{#if entity.isDismissed}
								<Icon src={ArrowUturnLeft} class="h-4 w-4" micro />
							{:else}
								<Icon src={XMark} class="h-4 w-4" micro />
							{/if}
						</Button>
					</Tooltip>
				{/if}

				<!-- Main action button -->
				{#if showActions}
					{#if linkedRemoteAgent}
						<Button
							variant="secondary"
							size="sm"
							onclick={() => onAgentClick?.(linkedRemoteAgent)}
							class="group/button flex-shrink-0"
						>
							<div class="flex items-center gap-2">
								<RemoteAgentStatusIndicator
									status={linkedRemoteAgent.status}
									workspaceStatus={linkedRemoteAgent.workspaceStatus}
									hasUpdates={linkedRemoteAgent.hasUpdates}
									variant="sleek"
									size="sm"
									isExpanded={false}
								/>
								<span
									class="text-sm font-medium {width > 400
										? ''
										: 'flex w-0 opacity-0 transition-all duration-200 group-hover/button:w-[8em] group-hover/button:opacity-100'}"
									>View Agent</span
								>
								<Icon src={ArrowRight} class="h-4 w-4" micro />
							</div>
						</Button>
					{:else}
						<!-- <Button
							variant="secondary"
							size="sm"
							onclick={handleOpenAgentModal}
							disabled={isAnimating}
							class="flex-shrink-0 group/button"
						>
							<div class="flex items-center">
								<div class="{width > 400 ? 'mr-2' : 'w-0 group-hover/button:w-[8em] flex opacity-0 group-hover/button:opacity-100 transition-all duration-200'}">
									{isAnimating ? 'Starting work...' : 'Assign to Agent'}
								</div>
								<Icon src={ArrowRight} class="w-4 h-4" micro />
							</div>
						</Button> -->
					{/if}
				{/if}
			</div>
		{/if}
	</div>
</div>

<!-- Hover Popup Card (Portal) -->
{#if isHovering && shouldShowHoverCard}
	<Portal>
		<div
			class="fixed z-50 {showPopupBelow ? '' : '-translate-y-full transform'}"
			style="
					top: {popupPosition.top}px;
					left: {popupPosition.left}px;
					width: {popupPosition.width}px;
				"
		>
			<div
				class="max-w-md overflow-hidden rounded-lg border border-slate-200 bg-white p-4 shadow-lg backdrop-blur-sm dark:border-slate-700 dark:bg-slate-800"
			>
				<!-- Entity Info Row -->
				<div class="mb-3 flex items-center gap-3 text-xs text-slate-500 dark:text-slate-400">
					<CustomIcon icon={entity.providerId} size={16} class="flex-shrink-0" />
					<span class="font-medium">{getProviderDisplayName(entity.providerId)}</span>
					<span>{getEntityTypeDisplayName(entity.entityType, entity.providerId)}</span>
					{#if entity.metadata?.number}
						<span>•</span>
						<span>#{entity.metadata.number}</span>
					{/if}
				</div>

				<!-- Header with Title and Status -->
				<div class="mb-3">
					<div class="flex items-start justify-between gap-3">
						<h4
							class="flex-1 text-sm leading-tight font-semibold text-slate-900 dark:text-white"
							style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;"
						>
							{entity.title}
						</h4>

						{#if entity.state}
							{@const statePill = getStatePill(entity.state, Boolean(entity.metadata?.draft))}
							{#if statePill}
								<span
									class="inline-flex items-center rounded-full border px-2 py-1 text-xs font-medium {statePill.bgColor} {statePill.textColor} {statePill.borderColor} flex-shrink-0"
								>
									{statePill.label}
								</span>
							{/if}
						{/if}
					</div>
				</div>

				<!-- Description -->
				{#if getDescription()}
					<div class="mb-3">
						<div
							class="max-h-24 overflow-y-auto text-xs leading-relaxed text-slate-600 dark:text-slate-400"
						>
							<div class="break-words">
								<Markdown content={getDescription()} size="xs" hasNoPadding />
							</div>
						</div>
					</div>
				{/if}

				<!-- Timeline -->
				{#if getUpdatedTime() || getCreatedTime() || entity.metadata?.milestone}
					<div class="mb-3">
						<div class="space-y-1 text-xs text-slate-500 dark:text-slate-400">
							{#if getCreatedTime()}
								<div>Created {getCreatedTime()}</div>
							{/if}
							{#if getUpdatedTime()}
								<div>Updated {getUpdatedTime()}</div>
							{/if}
							{#if entity.metadata?.milestone}
								<div>Milestone: {entity.metadata.milestone}</div>
							{/if}
						</div>
					</div>
				{/if}

				<!-- People -->
				{#if getAssignee() || getAuthor()}
					<div class="mb-3">
						<div class="space-y-1 text-xs text-slate-500 dark:text-slate-400">
							{#if getAuthor()}
								{@const author = getAuthor()}
								<div class="flex items-center gap-2">
									{#if author?.login && (entity?.metadata?.provider as string) === 'github'}
										{@const avatarUrl = getGitHubAvatarUrl(author.login, 20)}
										{#if avatarUrl}
											<img
												src={avatarUrl}
												alt="{author.name} avatar"
												class="h-4 w-4 flex-shrink-0 rounded-full border border-slate-200 dark:border-slate-700"
												loading="lazy"
											/>
										{:else}
											<Icon src={User} class="h-4 w-4 flex-shrink-0" mini />
										{/if}
									{:else}
										<Icon src={User} class="h-4 w-4 flex-shrink-0" mini />
									{/if}
									<span class="truncate">Author: {author?.name}</span>
								</div>
							{/if}

							{#if getAssignee()}
								<div class="flex items-center gap-2">
									<Icon src={User} class="h-4 w-4 flex-shrink-0" mini />
									<span class="truncate">Assignee: {getAssignee()}</span>
								</div>
							{/if}
						</div>
					</div>
				{/if}

				<!-- Labels -->
				{#if getLabels().length > 0}
					<div class="mb-3">
						<div class="flex flex-wrap gap-1">
							{#each getLabels() as label, index}
								{#if index < 4}
									<span
										class="inline-flex items-center rounded bg-slate-100 px-1.5 py-0.5 text-xs text-slate-600 dark:bg-slate-700 dark:text-slate-400"
									>
										{label.length > 15 ? label.slice(0, 15) + '...' : label}
									</span>
								{/if}
							{/each}
							{#if getLabels().length > 4}
								<span
									class="inline-flex items-center rounded bg-slate-50 px-1.5 py-0.5 text-xs text-slate-500 dark:bg-slate-800 dark:text-slate-500"
								>
									+{getLabels().length - 4}
								</span>
							{/if}
						</div>
					</div>
				{/if}

				<!-- Trigger Information -->
				{#if trigger?.name}
					<div class="border-t border-slate-100 pt-3 dark:border-slate-700">
						<div class="text-xs text-slate-500 dark:text-slate-400">
							via {trigger.name}
						</div>
					</div>
				{/if}
			</div>
		</div>
	</Portal>
{/if}
