<script lang="ts">
	import {
		I<PERSON>,
		Bolt,
		<PERSON><PERSON><PERSON><PERSON>,
		XCircle,
		Clock,
		ExclamationTriangle,
		Play,
		Pause,
		Cog6Tooth,
		ArrowDown,
		Sparkles
	} from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import CustomIcon from '$lib/components/ui/CustomIcon.svelte';
	import { getRelativeTime } from '$lib/utils/time';
	import { fly, slide } from 'svelte/transition';
	import { cubicOut } from 'svelte/easing';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { getExecutionsForTrigger } from '$lib/stores/global-state.svelte';
	import { getEntityDisplayName } from '$lib/utils/task-references';
	import { getEntityTypeFromTrigger } from '$lib/utils/entity-conversion';
	import { getEntityTypeIcon } from '$lib/config/entity-types';
	import InlineInstructionsForm from '../forms/InlineInstructionsForm.svelte';
	import { createRemoteAgentManually } from '$lib/utils/dashboard-entity-operations';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';

	interface Props {
		trigger: NormalizedTrigger;
		isSelected?: boolean;
		animationDelay?: number;
		onTriggerClick?: (trigger: NormalizedTrigger) => void;
		onToggleStatus?: (trigger: NormalizedTrigger) => void;
		onConfigure?: (trigger: NormalizedTrigger) => void;
		onStartWork?: (trigger: NormalizedTrigger) => void;
		showStartWork?: boolean;
	}

	let {
		trigger,
		isSelected = false,
		animationDelay = 0,
		onTriggerClick,
		onToggleStatus,
		onConfigure,
		onStartWork,
		showStartWork = false
	}: Props = $props();

	// State for start work functionality
	let showInstructionsForm = $state(false);
	let isCreatingAgent = $state(false);
	let isLoadingEntities = $state(false);
	let matchingEntities = $state<UnifiedEntity[]>([]);

	// Get executions for this trigger
	let executionsStore = $derived(getExecutionsForTrigger(trigger.id));
	let executionCount = $derived($executionsStore.data.length);

	// Helper function to get status info (similar to WorkflowRunCard)
	function getStatusInfo(status: 'success' | 'error' | 'pending' | 'none') {
		switch (status) {
			case 'success':
				return {
					icon: CheckCircle,
					color: 'text-emerald-600 dark:text-emerald-400',
					bgColor: 'bg-emerald-50 dark:bg-emerald-900/20',
					borderColor: 'border-emerald-200 dark:border-emerald-800',
					label: 'Success'
				};
			case 'error':
				return {
					icon: XCircle,
					color: 'text-red-600 dark:text-red-400',
					bgColor: 'bg-red-50 dark:bg-red-900/20',
					borderColor: 'border-red-200 dark:border-red-800',
					label: 'Failed'
				};
			case 'pending':
				return {
					icon: Clock,
					color: 'text-amber-600 dark:text-amber-400',
					bgColor: 'bg-amber-50 dark:bg-amber-900/20',
					borderColor: 'border-amber-200 dark:border-amber-800',
					label: 'Auto-run'
				};
			default:
				return {
					icon: ExclamationTriangle,
					color: 'text-slate-500 dark:text-slate-400',
					bgColor: 'bg-slate-50 dark:bg-slate-800',
					borderColor: 'border-slate-200 dark:border-slate-700',
					label: 'Idle'
				};
		}
	}

	let statusInfo = $derived(getStatusInfo(trigger.lastExecutionStatus));

	function handleClick() {
		onTriggerClick?.(trigger);
	}

	function handleToggleStatus(e: MouseEvent) {
		e.stopPropagation();
		if (onToggleStatus) {
			onToggleStatus(trigger);
		}
	}

	function handleConfigure(e: MouseEvent) {
		e.stopPropagation();
		if (onConfigure) {
			onConfigure(trigger);
		}
	}

	async function handleStartWork(e: MouseEvent) {
		e.stopPropagation();

		// First load matching entities if we haven't already
		if (matchingEntities.length === 0 && !isLoadingEntities) {
			await loadMatchingEntities();
		}

		// If we have entities, show the instructions form
		if (matchingEntities.length > 0) {
			showInstructionsForm = true;
		}
	}

	async function loadMatchingEntities() {
		if (!trigger.configuration) return;

		try {
			isLoadingEntities = true;

			// Fix the github_ref.repository field for template triggers
			const configToUse = { ...trigger.configuration };
			if (configToUse.agent_config?.workspace_setup?.starting_files?.github_ref) {
				const githubRef = configToUse.agent_config.workspace_setup.starting_files.github_ref;

				// If we have a URL but no repository, extract repository from URL
				if (githubRef.url && !githubRef.repository) {
					const match = githubRef.url.match(/github\.com\/([^\/]+\/[^\/]+)/);
					if (match) {
						githubRef.repository = match[1];
					}
				}
			}

			// TODO: Implement fetchMatchingEntities in new global state system
			// const entities = await fetchMatchingEntities(configToUse, { limit: 10 });
			matchingEntities = []; // Temporarily disabled during migration
		} catch (error) {
			console.error('Failed to load matching entities:', error);
		} finally {
			isLoadingEntities = false;
		}
	}

	async function handleCreateAgent(instructions: string) {
		if (matchingEntities.length === 0) return;

		// Use the first matching entity
		const entity = matchingEntities[0];

		try {
			isCreatingAgent = true;
			const result = await createRemoteAgentManually(entity, '', instructions);

			// Hide the form and reset state
			showInstructionsForm = false;

			// Call the original onStartWork callback if provided
			if (onStartWork) {
				onStartWork(trigger);
			}

			console.log('Remote agent created:', result.agentId);
		} catch (error) {
			console.error('Failed to create remote agent:', error);
		} finally {
			isCreatingAgent = false;
		}
	}

	function handleCancelInstructions() {
		showInstructionsForm = false;
	}

	let [_, entityType] = $derived(getEntityTypeFromTrigger(trigger));
</script>

<div
	class={`group relative flex w-full flex-col overflow-hidden rounded-xl border text-left transition-all duration-200 ${
		isSelected
			? 'border-blue-500 bg-blue-50 shadow-md dark:border-blue-400 dark:bg-slate-700'
			: onTriggerClick
				? 'border-slate-200 bg-white hover:border-slate-300 hover:shadow-md dark:border-slate-700 dark:bg-slate-900 dark:hover:border-slate-600 dark:hover:shadow-lg'
				: 'border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-900'
	}`}
	in:fly={{ y: 20, duration: 400, delay: animationDelay, easing: cubicOut }}
>
	<!-- Header with Provider and Status -->
	<div
		class="flex items-center justify-between border-b border-slate-100 bg-slate-50/50 px-4 py-2.5 dark:border-slate-800 dark:bg-slate-800/30"
	>
		<!-- Clickable header content -->
		<button
			class="flex items-center gap-2 rounded transition-opacity hover:opacity-80 focus:ring-2 focus:ring-blue-500/20 focus:outline-none {onTriggerClick
				? 'cursor-pointer'
				: 'cursor-default'}"
			onclick={handleClick}
		>
			<CustomIcon icon={trigger.provider} size={16} class="text-slate-400 dark:text-slate-400" />
			<!-- <Icon src={getEntityTypeIcon(trigger.provider, entityType)} class="w-4 h-4 text-slate-400 dark:text-slate-400" micro /> -->
			<span class="text-xs font-medium tracking-wide text-slate-500 uppercase dark:text-slate-400">
				{entityType.replace(/_/g, ' ')}
			</span>
		</button>

		<!-- Status and Actions -->
		<div class="flex flex-col gap-1">
			<!-- Auto-run Status Indicator -->
			<div class="flex items-center gap-1">
				<Icon
					micro
					src={Bolt}
					class="h-3 w-3 {trigger.isEnabled
						? 'text-blue-600 dark:text-blue-400'
						: 'text-gray-400 dark:text-gray-500'}"
				/>
				<span
					class="{trigger.isEnabled
						? 'text-blue-600 dark:text-blue-400'
						: 'text-gray-600 dark:text-gray-400'} text-xs font-medium"
				>
					{trigger.isEnabled ? 'Auto-run' : 'Manual'}
				</span>
			</div>
		</div>
	</div>

	<!-- Inline Instructions Form -->
	{#if showInstructionsForm && matchingEntities.length > 0}
		<div transition:slide={{ duration: 300, easing: cubicOut }}>
			<InlineInstructionsForm
				entity={matchingEntities[0]}
				isVisible={showInstructionsForm}
				isCreating={isCreatingAgent}
				onSubmit={handleCreateAgent}
				onCancel={handleCancelInstructions}
			/>
		</div>
	{/if}

	<!-- Main Content -->
	<div class="flex-1 space-y-4 p-4">
		<!-- Title -->
		<div class="min-w-0 flex-1">
			<button
				class="w-full rounded text-left {onTriggerClick
					? 'cursor-pointer transition-opacity hover:opacity-80 focus:ring-2 focus:ring-blue-500/20 focus:outline-none'
					: 'cursor-default'}"
				onclick={handleClick}
			>
				<h3
					class="line-clamp-2 text-base leading-tight font-semibold text-slate-900 transition-colors group-hover:text-slate-700 dark:text-white dark:group-hover:text-slate-100"
				>
					{trigger.name}
				</h3>
			</button>

			<!-- Description -->
			{#if trigger.description}
				<div class="mt-2 text-sm leading-relaxed text-slate-600 dark:text-slate-400">
					<p class="line-clamp-2">{trigger.description}</p>
				</div>
			{/if}
		</div>

		<!-- Conditions Summary -->
		<!-- <div class="inline-flex items-center gap-2 px-3 py-1.5 rounded-lg bg-slate-100 dark:bg-slate-700 border border-slate-200/50 dark:border-slate-600/50">
			<Icon src={Bolt} class="w-3.5 h-3.5 text-slate-500 dark:text-slate-400" />
			<span class="text-sm font-medium text-slate-700 dark:text-slate-300">
				{trigger.conditionsSummary}
			</span>
		</div> -->
	</div>

	<!-- Footer -->
	<div
		class="border-t border-slate-100 bg-slate-50/30 px-4 py-3 dark:border-slate-800 dark:bg-slate-800/20"
	>
		<div class="flex items-center justify-between">
			<!-- Stats -->
			<div class="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400">
				<div class="flex items-center gap-1.5">
					<div class="h-2 w-2 rounded-full bg-slate-300 dark:bg-slate-600"></div>
					<span class="font-medium">{executionCount || trigger.executionCount || 0}</span>
					<span>runs</span>
				</div>
			</div>

			<!-- Timestamp -->
			<span class="text-sm text-slate-500 dark:text-slate-400">
				{getRelativeTime(trigger.createdAt)}
			</span>
		</div>

		<!-- Action Buttons (visible on hover) -->
		{#if onToggleStatus || onConfigure}
			<div
				class="mt-2 flex items-center gap-1 opacity-0 transition-opacity duration-200 group-hover:opacity-100"
			>
				{#if onToggleStatus}
					<Button variant="ghost" size="sm" class="text-xs" onclick={handleToggleStatus}>
						<Icon src={trigger.isEnabled ? Pause : Play} class="mr-1 h-3 w-3" />
						{trigger.isEnabled ? 'Pause' : 'Activate'}
					</Button>
				{/if}

				{#if onConfigure}
					<Button variant="ghost" size="sm" class="text-xs" onclick={handleConfigure}>
						<Icon src={Cog6Tooth} class="mr-1 h-3 w-3" />
						Configure
					</Button>
				{/if}
			</div>
		{/if}
	</div>
</div>
