<script lang="ts">
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import { getRelativeTimeForStr } from '$lib/utils/time';
	import { Icon, ArrowTopRightOnSquare, User } from 'svelte-hero-icons';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import { getEntityTypeDisplayName, getEntityTypeIcon, getProviderDisplayName } from '$lib/config/entity-types';

	interface Props {
		entity: UnifiedEntity;
		onEntityClick?: (entity: UnifiedEntity) => void;
		class?: string;
	}

	let {
		entity,
		onEntityClick,
		class: className = ''
	}: Props = $props();

	// Helper functions to extract useful metadata from entities
	function getEntityMetadata(entity: UnifiedEntity) {
		return {
			author: entity.author,
			assignee: entity.assignee,
			state: entity.state,
			repository: entity.metadata?.repository,
			identifier: entity.metadata?.identifier,
			number: entity.metadata?.number,
			isDraft: entity.metadata?.draft
		};
	}

	// Helper function to get GitHub avatar URL
	function getGitHubAvatarUrl(login: string | undefined, size: number = 20): string | null {
		if (!login) return null;
		return `https://github.com/${login}.png?size=${size}`;
	}

	// Helper function to get GitHub-style state pill styling
	function getGitHubStatePill(state: string | undefined, isDraft: boolean = false) {
		if (!state) return null;

		const stateKey = state.toLowerCase();

		if (isDraft) {
			return {
				label: 'Draft',
				bgColor: 'bg-slate-100 dark:bg-slate-800',
				textColor: 'text-slate-600 dark:text-slate-400',
				borderColor: 'border-slate-300 dark:border-slate-600'
			};
		}

		const githubStates: Record<string, any> = {
			open: {
				label: 'Open',
				bgColor: 'bg-green-50 dark:bg-green-950/30',
				textColor: 'text-green-700 dark:text-green-300',
				borderColor: 'border-green-200 dark:border-green-800'
			},
			closed: {
				label: 'Closed',
				bgColor: 'bg-red-50 dark:bg-red-950/30',
				textColor: 'text-red-700 dark:text-red-300',
				borderColor: 'border-red-200 dark:border-red-800'
			},
			merged: {
				label: 'Merged',
				bgColor: 'bg-purple-50 dark:bg-purple-950/30',
				textColor: 'text-purple-700 dark:text-purple-300',
				borderColor: 'border-purple-200 dark:border-purple-800'
			}
		};

		return githubStates[stateKey] || {
			label: state,
			bgColor: 'bg-slate-50 dark:bg-slate-800',
			textColor: 'text-slate-700 dark:text-slate-300',
			borderColor: 'border-slate-200 dark:border-slate-600'
		};
	}

	// Helper function to get state color (for non-GitHub styling)
	function getStateColor(state: string | undefined, providerId: string) {
		if (!state) return 'text-slate-500 dark:text-slate-400';

		const stateColors: Record<string, Record<string, string>> = {
			github: {
				open: 'text-green-600 dark:text-green-400',
				closed: 'text-red-600 dark:text-red-400',
				merged: 'text-purple-600 dark:text-purple-400',
				draft: 'text-slate-600 dark:text-slate-400'
			},
			linear: {
				backlog: 'text-slate-600 dark:text-slate-400',
				todo: 'text-blue-600 dark:text-blue-400',
				'in progress': 'text-yellow-600 dark:text-yellow-400',
				done: 'text-green-600 dark:text-green-400',
				canceled: 'text-red-600 dark:text-red-400'
			}
		};

		return stateColors[providerId]?.[state.toLowerCase()] || 'text-slate-500 dark:text-slate-400';
	}

	// Extract metadata for display
	let entityMetadata = $derived(getEntityMetadata(entity));
	let stateColor = $derived(getStateColor(entity.state, entity.providerId));

	function handleClick() {
		if (onEntityClick) {
			onEntityClick(entity);
		}
	}
</script>

<div
	class="group w-full flex flex-col text-left relative transition-all duration-300 border border-slate-100 dark:border-slate-700 rounded-xl overflow-hidden bg-white shadow-sm dark:bg-slate-900 backdrop-blur-sm hover:border-slate-300 dark:hover:border-slate-600 {className}"
>
	<!-- Elegant header with provider branding -->
	<div class="relative px-4 py-2">
		<!-- Provider badge -->
		<div class="flex items-center justify-between mb-1">
			<button
				class="flex items-center gap-1.5 cursor-pointer group/provider focus:outline-none"
				onclick={handleClick}
			>
				<div class="">
					<CustomIcon icon={entity.providerId} size={12} class="text-slate-600 dark:text-slate-400" />
				</div>
				<div class="flex items-center gap-1">
					<!-- {#if entity.entityType}
						{@const entityTypeIcon = getEntityTypeIcon(entity.providerId, entity.entityType)}
						<Icon src={entityTypeIcon} class="w-3 h-3 text-slate-500 dark:text-slate-400" micro />
					{/if} -->
					<span class="text-xs text-slate-600 dark:text-slate-400">
						{getProviderDisplayName(entity.providerId)} {getEntityTypeDisplayName(entity.entityType, entity.providerId)}
					</span>
				</div>
			</button>

			<!-- External link with elegant styling -->
			{#if entity.url}
				<a
					href={entity.url}
					target="_blank"
					rel="noopener noreferrer"
					class="flex-shrink-0 w-6 h-6 hover:bg-slate-200 dark:hover:bg-slate-700 rounded-lg flex items-center justify-center transition-all duration-200 hover:shadow-sm"
					onclick={(e) => e.stopPropagation()}
					title="Open in new tab"
				>
					<Icon src={ArrowTopRightOnSquare} class="w-3 h-3 text-slate-500 dark:text-slate-400" micro />
				</a>
			{/if}
		</div>

		<!-- Title with elegant click interaction -->
		<button
			class="w-full text-left cursor-pointer group/title focus:outline-none"
			onclick={handleClick}
		>
			<h3 class="text-sm font-semibold text-slate-900 dark:text-white leading-tight line-clamp-2 group-hover/title:text-blue-600 dark:group-hover/title:text-blue-400 transition-colors duration-200">
				{entity.metadata?.display_title || entity.title}
			</h3>
		</button>
	</div>

</div>
