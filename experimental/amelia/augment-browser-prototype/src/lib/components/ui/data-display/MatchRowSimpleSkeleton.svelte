<script lang="ts">
	// Skeleton loader for MatchRowSimple component
	// Add some randomness to make it look more natural
	let titleWidth = Math.random() > 0.5 ? 'w-3/4' : 'w-2/3';
	let descriptionWidth = Math.random() > 0.5 ? 'w-1/2' : 'w-2/3';
</script>

<!-- Simple Match Row Skeleton -->
<div class="group relative bg-white dark:bg-slate-900">
	<!-- Main Row -->
	<div class="flex items-center justify-between px-6 py-2">
		<!-- Content -->
		<div class="min-w-0 flex-1">
			<!-- Title skeleton -->
			<div class="relative {titleWidth} h-4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
				></div>
			</div>

			<!-- Description skeleton -->
			<div class="mt-1 relative {descriptionWidth} h-3 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
					style="animation-delay: 0.2s;"
				></div>
			</div>
		</div>

		<!-- Dismiss button skeleton -->
		<div class="ml-3 flex-shrink-0">
			<div class="relative h-6 w-6 overflow-hidden rounded bg-slate-200 dark:bg-slate-700">
				<div
					class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
					style="animation-delay: 0.4s;"
				></div>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
