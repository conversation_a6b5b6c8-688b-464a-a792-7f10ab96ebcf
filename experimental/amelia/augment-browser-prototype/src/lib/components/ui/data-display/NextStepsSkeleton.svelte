<script lang="ts">
	// Skeleton loader for Next Steps section
	// Add some randomness to make it look more natural
	let buttonCount = Math.floor(Math.random() * 3) + 2; // 2-4 buttons
</script>

<!-- Next Steps Skeleton -->
<div class="gap-3 flex flex-wrap animate-pulse">
	{#each Array(buttonCount) as _, i}
		<div class="flex-1 justify-start min-w-[10em] rounded-md border border-slate-200 dark:border-slate-600 bg-white dark:bg-slate-800 p-3 shadow-sm">
			<div class="flex items-center gap-3">
				<!-- Icon -->
				<div class="relative w-7 h-7 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
					<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1}s;"></div>
				</div>
				<!-- Text content -->
				<div class="text-left flex-1">
					<div class="relative w-20 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden mb-1">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1 + 0.1}s;"></div>
					</div>
					<div class="relative w-32 h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.1 + 0.2}s;"></div>
					</div>
				</div>
			</div>
		</div>
	{/each}
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
