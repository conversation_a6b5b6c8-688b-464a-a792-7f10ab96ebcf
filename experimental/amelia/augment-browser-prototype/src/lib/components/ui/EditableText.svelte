<script lang="ts">
	interface Props {
		value: string;
		isEditing?: boolean;
		onSave: (newValue: string) => void;
		onCancel?: () => void;
		placeholder?: string;
		class?: string;
	}

	let {
		value,
		isEditing = $bindable(false),
		onSave,
		onCancel,
		placeholder = 'Enter text...',
		class: className = ''
	}: Props = $props();

	let inputElement = $state<HTMLInputElement>();
	let editValue = $state(value);

	// Extract font-weight classes from className to ensure input matches display
	let fontWeightClass = $derived(() => {
		const fontWeightClasses = [
			'font-thin',
			'font-extralight',
			'font-light',
			'font-normal',
			'font-medium',
			'font-semibold',
			'font-bold',
			'font-extrabold',
			'font-black'
		];
		return fontWeightClasses.find((cls) => className.includes(cls)) || '';
	});

	// Update edit value when value prop changes
	$effect(() => {
		editValue = value;
	});

	// Focus input when entering edit mode
	$effect(() => {
		if (isEditing && inputElement) {
			inputElement.focus();
			inputElement.select();
		}
	});

	function startEditing() {
		editValue = value;
		isEditing = true;
	}

	function save() {
		if (editValue.trim() !== value) {
			onSave(editValue.trim());
		}
		isEditing = false;
	}

	function cancel() {
		editValue = value;
		isEditing = false;
		onCancel?.();
	}

	function handleKeydown(e: KeyboardEvent) {
		if (e.key === 'Enter') {
			e.preventDefault();
			save();
		} else if (e.key === 'Escape') {
			e.preventDefault();
			cancel();
		}
	}

	function handleBlur() {
		save();
	}

	function handleDoubleClick() {
		startEditing();
	}
</script>

{#if isEditing}
	<input
		bind:this={inputElement}
		bind:value={editValue}
		onkeydown={handleKeydown}
		onblur={handleBlur}
		{placeholder}
		class="m-0 max-w-full min-w-0 truncate border-none bg-transparent p-0 outline-none {fontWeightClass} {className}"
		style="font-family: inherit; font-size: inherit; line-height: inherit; color: inherit; width: 100%;"
	/>
{:else}
	<span
		ondblclick={handleDoubleClick}
		class="block min-w-0 cursor-pointer truncate {className}"
		title="Double-click to edit"
		role="button"
		tabindex="0"
		onkeydown={(e) => {
			if (e.key === 'Enter' || e.key === ' ') {
				e.preventDefault();
				startEditing();
			}
		}}
	>
		{value || placeholder}
	</span>
{/if}
