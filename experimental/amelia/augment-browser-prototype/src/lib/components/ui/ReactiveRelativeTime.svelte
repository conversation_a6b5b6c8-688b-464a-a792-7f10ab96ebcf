<script lang="ts">
	import { reactiveRelativeTime } from '$lib/utils/reactive-time.svelte.js';

	interface Props {
		/** The date to show relative time for (Date, string, or number) */
		date: Date | string | number;
		/** Optional CSS class to apply */
		class?: string;
		/** Optional title attribute (shows full date on hover) */
		showTitle?: boolean;
	}

	let { date, class: className = '', showTitle = true }: Props = $props();

	// Get reactive relative time
	const relativeTime = $derived.by(reactiveRelativeTime(date));

	// Convert to Date for title
	const dateObj = new Date(date);
	const fullDate = isNaN(dateObj.getTime()) ? 'Invalid date' : dateObj.toLocaleString();
</script>

<span class={className} title={showTitle ? fullDate : undefined}>
	{relativeTime}
</span>
