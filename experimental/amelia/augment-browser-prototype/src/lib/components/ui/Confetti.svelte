<script lang="ts">
	interface Props {
		/** Whether to trigger the confetti animation */
		trigger?: boolean;
		/** Number of confetti particles */
		count?: number;
		/** Duration of the animation in seconds */
		duration?: number;
		/** Colors for the confetti particles */
		colors?: string[];
	}

	const {
		trigger = false,
		count = 8,
		duration = 0.6,
		colors = [
			'#ef4444', // red
			'#f97316', // orange
			'#eab308', // yellow
			'#22c55e', // green
			'#3b82f6', // blue
			'#8b5cf6', // purple
			'#ec4899', // pink
			'#06b6d4' // cyan
		]
	}: Props = $props();

	let particles: Array<{
		id: number;
		color: string;
		angle: number;
		distance: number;
		delay: number;
		x: number;
		y: number;
	}> = $state([]);

	let animationId = $state(0);
	let lastTriggerValue = $state(false);

	// Generate particles when trigger changes from false to true
	$effect(() => {
		if (trigger && !lastTriggerValue) {
			console.log('SVG Confetti triggered!');
			generateParticles();
		}
		lastTriggerValue = trigger;
	});

	function generateParticles() {
		const currentAnimationId = ++animationId;
		particles = Array.from({ length: count }, (_, i) => {
			const angle = Math.random() * 360 * (Math.PI / 180); // Convert to radians
			const distance = 25 + Math.random() * 20;
			return {
				id: currentAnimationId * count + i,
				color: colors[i % colors.length],
				angle,
				distance,
				delay: i * 0.05, // 50ms in seconds
				x: Math.cos(angle) * distance,
				y: Math.sin(angle) * distance
			};
		});
	}
</script>

<div class="pointer-events-none absolute inset-0 flex items-center justify-center">
	{#if trigger && particles.length > 0}
		<svg width="100" height="100" viewBox="-50 -50 100 100" class="overflow-visible">
			{#each particles as particle (particle.id)}
				<circle cx="0" cy="0" r="2" fill={particle.color}>
					<animateTransform
						attributeName="transform"
						type="translate"
						values="0,0; {particle.x},{particle.y}"
						dur="{duration}s"
						begin="{particle.delay}s"
						fill="freeze"
					/>
					<animate
						attributeName="opacity"
						values="1; 0"
						dur="{duration}s"
						begin="{particle.delay}s"
						fill="freeze"
					/>
					<animateTransform
						attributeName="transform"
						type="scale"
						values="1; 0.2"
						dur="{duration}s"
						begin="{particle.delay}s"
						fill="freeze"
						additive="sum"
					/>
				</circle>
			{/each}
		</svg>
	{/if}
</div>
