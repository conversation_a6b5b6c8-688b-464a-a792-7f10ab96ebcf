<script lang="ts">
	import CustomIcon from '$lib/components/ui/visualization/CustomIcon.svelte';
	import type { TriggerProvider } from '$lib/types';

	interface Props {
		providers: TriggerProvider[];
		selectedProvider: string;
		onProviderChange: (providerId: string) => void;
		disabled?: boolean;
		class?: string;
	}

	let { providers, selectedProvider, onProviderChange, disabled = false, class: className = '' }: Props = $props();

	// Map provider IDs to their icon names for CustomIcon component
	const providerIconNames: Record<string, 'github' | 'linear' | 'slack' | 'datadog' | 'pagerduty' | 'cron'> = {
		github: 'github',
		linear: 'linear',
		slack: 'slack',
		datadog: 'datadog',
		pagerduty: 'pagerduty',
		cron: 'cron'
	};

	// Get brand colors for each provider
	const providerColors: Record<string, string> = {
		github: 'text-gray-900 dark:text-white',
		linear: 'text-blue-600 dark:text-blue-400',
		slack: 'text-purple-600 dark:text-purple-400',
		datadog: 'text-purple-700 dark:text-purple-400',
		pagerduty: 'text-green-600 dark:text-green-400',
		cron: 'text-orange-600 dark:text-orange-400'
	};

	function handleProviderClick(providerId: string) {
		if (!disabled) {
			onProviderChange(providerId);
		}
	}
</script>

<fieldset class="space-y-3 {className}">
	<legend class="block text-sm font-medium text-gray-700 dark:text-gray-300">
		Provider
	</legend>

	<div class="grid grid-cols-[repeat(auto-fit,_minmax(160px,_1fr))] gap-4">
		{#each providers as provider}
			{@const iconName = providerIconNames[provider.id]}
			{@const isSelected = selectedProvider === provider.id}
			{@const colorClass = providerColors[provider.id] || 'text-gray-600 dark:text-gray-400'}

			<button
				type="button"
				onclick={() => handleProviderClick(provider.id)}
				disabled={disabled}
				class="flex flex-col items-center gap-2 p-4 border rounded-lg transition-all duration-200
					{isSelected
						? 'border-blue-500 bg-blue-50 dark:bg-blue-950/50 shadow-sm'
						: 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800/50'
					}
					{disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
				"
			>
				{#if iconName}
					<div class="w-6 h-6 {isSelected ? 'text-blue-600 dark:text-blue-400' : colorClass}">
						<CustomIcon icon={iconName} size="100%" />
					</div>
				{/if}

				<div class="text-center">
					<div class="text-sm font-medium text-gray-900 dark:text-white">
						{provider.name}
					</div>
					<div class="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-3">
						{provider.description}
					</div>
				</div>
			</button>
		{/each}
	</div>
</fieldset>
