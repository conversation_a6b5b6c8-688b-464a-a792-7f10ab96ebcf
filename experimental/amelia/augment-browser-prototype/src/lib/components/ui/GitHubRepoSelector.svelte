<script lang="ts">
	import { onMount } from 'svelte';
	import { Icon, Check, Star, Eye, MagnifyingGlass } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Input from './Input.svelte';
	import LoadingIndicator from './LoadingIndicator.svelte';
	import { GitBranch } from './CustomIcon.svelte';

	interface GitHubRepo {
		id: number;
		name: string;
		full_name: string;
		description: string | null;
		private: boolean;
		html_url: string;
		stargazers_count: number;
		forks_count: number;
		watchers_count: number;
		language: string | null;
		updated_at: string;
		owner: {
			login: string;
			avatar_url: string;
		};
	}

	interface Props {
		accessToken: string;
		selectedRepos?: string[];
		onSelectionChange: (repos: string[]) => void;
		onCancel: () => void;
		onConfirm: (repos: string[]) => void;
		maxSelection?: number;
	}

	let {
		accessToken,
		selectedRepos = [],
		onSelectionChange,
		onCancel,
		onConfirm,
		maxSelection = 5
	}: Props = $props();

	let repos = $state<GitHubRepo[]>([]);
	let filteredRepos = $state<GitHubRepo[]>([]);
	let isLoading = $state(true);
	let error = $state<string | null>(null);
	let searchQuery = $state('');
	let selectedRepoNames = $state<Set<string>>(new Set(selectedRepos));

	// Filter repos based on search query
	$effect(() => {
		if (!searchQuery.trim()) {
			filteredRepos = repos;
		} else {
			const query = searchQuery.toLowerCase();
			filteredRepos = repos.filter(repo =>
				repo.name.toLowerCase().includes(query) ||
				repo.full_name.toLowerCase().includes(query) ||
				repo.description?.toLowerCase().includes(query)
			);
		}
	});

	// Notify parent of selection changes
	$effect(() => {
		onSelectionChange(Array.from(selectedRepoNames));
	});

	onMount(async () => {
		await fetchRepositories();
	});

	async function fetchRepositories() {
		isLoading = true;
		error = null;

		try {
			// Fetch user's repositories
			const response = await fetch('https://api.github.com/user/repos?sort=updated&per_page=100', {
				headers: {
					'Authorization': `Bearer ${accessToken}`,
					'Accept': 'application/vnd.github.v3+json'
				}
			});

			if (!response.ok) {
				throw new Error('Failed to fetch repositories');
			}

			const repoData = await response.json();
			repos = repoData;
			filteredRepos = repoData;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load repositories';
		} finally {
			isLoading = false;
		}
	}

	function toggleRepo(repoFullName: string) {
		const newSelection = new Set(selectedRepoNames);

		if (newSelection.has(repoFullName)) {
			newSelection.delete(repoFullName);
		} else {
			// Check max selection limit
			if (newSelection.size >= maxSelection) {
				return; // Don't add if at max
			}
			newSelection.add(repoFullName);
		}

		selectedRepoNames = newSelection;
	}

	function handleConfirm() {
		onConfirm(Array.from(selectedRepoNames));
	}

	function formatDate(dateString: string) {
		return new Date(dateString).toLocaleDateString();
	}

	function formatNumber(num: number) {
		if (num >= 1000) {
			return (num / 1000).toFixed(1) + 'k';
		}
		return num.toString();
	}
</script>

<div class="space-y-4">
	<!-- Header -->
	<div>
		<h3 class="text-lg font-medium text-slate-900 dark:text-white mb-2">
			Select GitHub Repositories
		</h3>
		<p class="text-sm text-slate-600 dark:text-slate-400">
			Choose up to {maxSelection} repositories to sync with Augment. You can change this later.
		</p>
	</div>

	<!-- Search -->
	<div class="relative">
		<Icon src={MagnifyingGlass} class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
		<Input
			bind:value={searchQuery}
			placeholder="Search repositories..."
			class="pl-10"
		/>
	</div>

	<!-- Selection count -->
	<div class="flex items-center justify-between text-sm">
		<span class="text-slate-600 dark:text-slate-400">
			{selectedRepoNames.size} of {maxSelection} repositories selected
		</span>
		{#if selectedRepoNames.size > 0}
			<button
				class="text-blue-600 dark:text-blue-400 hover:underline"
				onclick={() => selectedRepoNames = new Set()}
			>
				Clear all
			</button>
		{/if}
	</div>

	<!-- Repository list -->
	<div class="border border-slate-200 dark:border-slate-700 rounded-lg max-h-96 overflow-y-auto">
		{#if isLoading}
			<div class="flex items-center justify-center py-8">
				<LoadingIndicator variant="spinner" size="md" />
				<span class="ml-2 text-slate-600 dark:text-slate-400">Loading repositories...</span>
			</div>
		{:else if error}
			<div class="p-4 text-center">
				<p class="text-red-600 dark:text-red-400">{error}</p>
				<Button
					variant="ghost"
					size="sm"
					onclick={fetchRepositories}
					class="mt-2"
				>
					Try again
				</Button>
			</div>
		{:else if filteredRepos.length === 0}
			<div class="p-4 text-center text-slate-500 dark:text-slate-400">
				{searchQuery ? 'No repositories match your search' : 'No repositories found'}
			</div>
		{:else}
			<div class="divide-y divide-slate-200 dark:divide-slate-700">
				{#each filteredRepos as repo (repo.id)}
					{@const isSelected = selectedRepoNames.has(repo.full_name)}
					{@const isDisabled = !isSelected && selectedRepoNames.size >= maxSelection}
					<button
						class="w-full p-4 text-left hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors {isDisabled ? 'opacity-50 cursor-not-allowed' : ''}"
						onclick={() => !isDisabled && toggleRepo(repo.full_name)}
						disabled={isDisabled}
					>
						<div class="flex items-start gap-3">
							<!-- Selection checkbox -->
							<div class="flex-shrink-0 mt-1">
								<div class="w-4 h-4 border-2 border-slate-300 dark:border-slate-600 rounded flex items-center justify-center {isSelected ? 'bg-blue-500 border-blue-500' : ''}">
									{#if isSelected}
										<Icon src={Check} class="w-3 h-3 text-white" />
									{/if}
								</div>
							</div>

							<!-- Repository info -->
							<div class="flex-1 min-w-0">
								<div class="flex items-center gap-2 mb-1">
									<h4 class="font-medium text-slate-900 dark:text-white truncate">
										{repo.name}
									</h4>
									{#if repo.private}
										<span class="text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded">
											Private
										</span>
									{/if}
								</div>

								<p class="text-sm text-slate-600 dark:text-slate-400 mb-2">
									{repo.full_name}
								</p>

								{#if repo.description}
									<p class="text-sm text-slate-500 dark:text-slate-400 mb-2 line-clamp-2">
										{repo.description}
									</p>
								{/if}

								<!-- Repository stats -->
								<div class="flex items-center gap-4 text-xs text-slate-500 dark:text-slate-400">
									{#if repo.language}
										<span class="flex items-center gap-1">
											<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
											{repo.language}
										</span>
									{/if}
									<span class="flex items-center gap-1">
										<Icon src={Star} class="w-3 h-3" />
										{formatNumber(repo.stargazers_count)}
									</span>
									<span class="flex items-center gap-1">
										<Icon src={GitBranch} class="w-3 h-3" />
										{formatNumber(repo.forks_count)}
									</span>
									<span>Updated {formatDate(repo.updated_at)}</span>
								</div>
							</div>
						</div>
					</button>
				{/each}
			</div>
		{/if}
	</div>

	<!-- Actions -->
	<div class="flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
		<Button
			variant="ghost"
			onclick={onCancel}
		>
			Cancel
		</Button>
		<Button
			variant="primary"
			onclick={handleConfirm}
			disabled={selectedRepoNames.size === 0}
		>
			Connect {selectedRepoNames.size} {selectedRepoNames.size === 1 ? 'Repository' : 'Repositories'}
		</Button>
	</div>
</div>
