<script lang="ts">
	import {
		Icon,
		PaperAirplane,
		MagnifyingGlass,
		ArrowLeft,
		ArrowRight,
		Link,
		ChevronDown,
		ChevronUp,
		Trash,
		XMark,
		Pencil,
		ArrowTopRightOnSquare
	} from 'svelte-hero-icons';
	import Modal from './Modal.svelte';
	import { crossfade } from 'svelte/transition';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';

	const [send, receive] = crossfade({ duration: 300 });
	import GitHubRepoCombobox from '$lib/components/github/GitHubRepoCombobox.svelte';
	import GitHubBranchCombobox from '$lib/components/github/GitHubBranchCombobox.svelte';
	import EntityCardSmall from '../data-display/EntityCardSmall.svelte';
	import MatchRow from '../data-display/MatchRow.svelte';
	import Markdown from '../content/Markdown.svelte';
	import UserAvatar from '../visualization/UserAvatar.svelte';
	import { modalState, closeModal, type AgentCreationModalProps } from '$lib/stores/modal';
	import {
		shouldShowTriggerSkeleton,
		shouldShowAgentSkeleton,
		shouldShowEntitySkeleton,
		getTriggerEntityGroups,
		getTrigger,
		getEntity
	} from '$lib/stores/global-state.svelte';
	import {
		createTriggerAndSync,
		loadAgents,
		executeTrigger,
		loadMatchingEntities
	} from '$lib/stores/data-operations.svelte';
	import { apiClient, ChatRequestNodeType, type CreateAgentRequest } from '$lib/api/unified-client';
	import { addToast } from '$lib/stores/toast';
	import { wrapInitialMessageWithLatestInstructions } from '$lib/types/structured-response';
	import { createRemoteAgentManually } from '$lib/utils/dashboard-entity-operations';
	import { githubAPI, type GitHubRepo, type GitHubBranch } from '$lib/api/github-api';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { getAgentStats } from '$lib/stores/agent-limits.svelte';
	import {
		getErrorMessage,
		getWarningMessage,
		checkAgentLimits,
		validateFormFields,
		determineErrorCodeFromResponse
	} from '$lib/utils/error-messages';
	import {
		generateAgentTitle,
		createTitleGenerationContext
	} from '$lib/utils/agent-title-generation';
	import { onMount } from 'svelte';
	import {
		loadLastUsedRepoAndBranch,
		saveLastUsedRepoAndBranch,
		getLatestAgentRepoAndBranch
	} from '$lib/utils/agent-form-storage';
	import { addRecentRepo, addRecentBranch } from '$lib/utils/recent-repos-storage';
	import {
		extractGitHubInfoFromTrigger,
		extractGitHubInfoFromEntity
	} from '$lib/utils/github-url-utils';
	import { agents as agentsList, deleteAgent } from '$lib/stores/global-state.svelte';
	import { getRelativeTime } from '$lib/utils/time';
	import { getEntityTypeDisplayName, getProviderDisplayName } from '$lib/utils/entity-types';

	import { extractInitialInstructionsFromSessionSummary } from '$lib/utils/remote-agent-utils';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import { fly, slide } from 'svelte/transition';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import Input from '../forms/Input.svelte';
	import MatchRowSkeleton from '../data-display/MatchRowSkeleton.svelte';

	import Button from '../navigation/Button.svelte';
	import Textarea from '../forms/Textarea.svelte';
	import PromptEnhancer from '../PromptEnhancer.svelte';
	import type { PromptEnhancementResult } from '$lib/utils/prompts';
	import SetupScriptPicker from '../../agents/SetupScriptPicker.svelte';
	import type { SetupScript, GithubSetupScript } from '$lib/types';
	import Tooltip from './Tooltip.svelte';
	import ReactiveRelativeTime from '../ReactiveRelativeTime.svelte';
	import AgentScheduler from '../forms/AgentScheduler.svelte';
	import { createTriggerConfiguration } from '$lib/utils/trigger-form-utils';

	// Get modal state
	let isOpen = $derived($modalState.isOpen && $modalState.type === 'agent-creation');
	let modalProps = $derived($modalState.props as AgentCreationModalProps | undefined);

	// Form state
	let prompt = $state('');
	let repositoryUrl = $state('https://github.com/augmentcode/augment');
	let branch = $state('main');
	let isSubmitting = $state(false);
	let error = $state('');
	let promptTextarea = $state<HTMLTextAreaElement>();
	let isPromptFocused = $state(false);

	// GitHub integration state
	let selectedRepo = $state<GitHubRepo | null>(null);
	let selectedBranch = $state<GitHubBranch | null>(null);

	// Setup script state
	let selectedSetupScript = $state<SetupScript | null>(null);
	let setupScriptContent = $state('');
	let setupScriptType = $state<'basic' | 'auto' | 'manual'>('basic');

	// Scheduling state
	let scheduleData = $state({
		enabled: false,
		cronExpression: '0 9 * * *',
		timezone: 'UTC',
		description: '',
		startDate: '',
		endDate: '',
		naturalLanguageInput: '',
		isValid: true,
		errors: [] as string[],
		warnings: [] as string[]
	});

	// Entity selection state
	let selectedEntity = $state<UnifiedEntity | null>(null);
	let selectedTrigger = $state<NormalizedTrigger | null>(null);
	let searchTerm = $state('');
	let showEntityDetails = $state(false);

	// Mobile state
	let showEntitySelection = $state(false); // For mobile entity selection overlay
	let isMobile = $state(false);

	// Trigger group collapse state
	let collapsedTriggers = $state<Set<string>>(new Set());

	// Instructions UI state
	let isInstructionsExpanded = $state(false);

	function toggleTriggerExpansion(triggerId: string) {
		if (collapsedTriggers.has(triggerId)) {
			collapsedTriggers.delete(triggerId);
		} else {
			collapsedTriggers.add(triggerId);
		}
		collapsedTriggers = new Set(collapsedTriggers); // Trigger reactivity
	}

	// Loading state
	let isEntitiesLoading = $derived(
		$shouldShowTriggerSkeleton || $shouldShowAgentSkeleton || $shouldShowEntitySkeleton
	);

	// Derived repository URL from selected repos
	let derivedRepositoryUrl = $derived(() => {
		if (selectedRepo) {
			return githubAPI.getRepoUrl(selectedRepo.owner, selectedRepo.name);
		}
		return repositoryUrl;
	});

	// Validation
	let isValid = $derived(() => {
		const hasPrompt = prompt.trim().length > 0;
		const hasRepo = selectedRepo || repositoryUrl.trim().length > 0;
		const hasBranch = selectedBranch || branch.trim().length > 0;
		return hasPrompt && hasRepo && hasBranch;
	});

	// Client-side validation including agent limits
	let validationWarnings = $state<string[]>([]);
	let validationErrors = $state<string[]>([]);

	let clientValidation = $derived.by(() => {
		if (!$agentStats) return { canCreate: true, errors: [], warnings: [] };

		const formValidation = validateFormFields({
			prompt: prompt.trim(),
			repositoryUrl: derivedRepositoryUrl().trim(),
			branch: selectedBranch?.name || branch.trim(),
			useGitHubIntegration: true
		});

		const limitValidation = checkAgentLimits(
			$agentStats.activeCount,
			$agentStats.totalCount,
			$agentStats.maxActive,
			$agentStats.maxTotal
		);

		const errors: string[] = [];
		const warnings: string[] = [];

		formValidation.errorCodes.forEach((code) => {
			errors.push(getErrorMessage(code).message);
		});

		if (limitValidation.errorCode) {
			errors.push(getErrorMessage(limitValidation.errorCode).message);
		}

		formValidation.warningCodes.forEach((code) => {
			warnings.push(getWarningMessage(code).message);
		});

		limitValidation.warningCodes.forEach((code) => {
			warnings.push(getWarningMessage(code).message);
		});

		return {
			canCreate: errors.length === 0,
			errors,
			warnings
		};
	});

	// Update validation state reactively
	$effect(() => {
		console.log('Client validation changed:');
		validationWarnings = clientValidation.warnings;
		validationErrors = clientValidation.errors;
	});

	// Reset instructions expanded state when entity changes
	$effect(() => {
		console.log('selectedEntity changed:');
		// When entity changes, start with collapsed instructions
		if (selectedEntity) {
			isInstructionsExpanded = false;
		}
	});

	let canSubmit = $derived(isValid() && clientValidation.canCreate);
	let agentStats = $derived(getAgentStats());

	// Keyboard shortcut hint visibility
	let showKeyboardHint = $derived(isPromptFocused && prompt.trim().length > 0);

	// Agent deletion state
	let deletingAgents = $state<Set<string>>(new Set());

	// Get agents list for warning display
	let agents = $derived($agentsList);

	// Check if warning is about agent limits
	let hasAgentLimitWarning = $derived.by(() => {
		return validationWarnings.some(
			(warning) =>
				warning.includes('agent limit') ||
				warning.includes('active agents') ||
				warning.includes('running remote agents')
		);
	});

	async function handleDeleteAgent(agentId: string) {
		await apiClient.agents.delete(agentId);

		// Refresh agents list
		await loadAgents(true);

		try {
			await deleteAgent(agentId);
			addToast({
				type: 'success',
				message: 'Agent deleted successfully',
				duration: 3000
			});
		} catch (error) {
			console.error('Failed to delete agent:', error);
			addToast({
				type: 'error',
				message: error instanceof Error ? error.message : 'Failed to delete agent',
				duration: 5000
			});
		}
	}

	// Get reactive trigger-entity groups from global state
	let triggerEntityGroups = $derived(getTriggerEntityGroups());

	// Filter entities based on search term and group by trigger
	let filteredEntitiesByTrigger = $derived.by(() => {
		const groups = $triggerEntityGroups;
		const term = searchTerm.toLowerCase().trim();

		const result: Array<{ trigger: NormalizedTrigger; entities: UnifiedEntity[] }> = [];

		groups.forEach(({ trigger, entities }) => {
			let filteredEntities = entities;

			if (term) {
				filteredEntities = entities.filter(
					(entity) =>
						entity.title.toLowerCase().includes(term) ||
						entity.description?.toLowerCase().includes(term) ||
						entity.providerId.toLowerCase().includes(term)
				);
			}

			result.push({ trigger, entities: filteredEntities });
		});

		return result;
	});

	// Initialize form when modal opens with preselected entity
	onMount(() => {
		// Mobile detection
		const checkMobile = () => {
			isMobile = window.innerWidth < 768; // md breakpoint
		};
		checkMobile();
		window.addEventListener('resize', checkMobile);

		if (isOpen && modalProps?.preselectedEntity) {
			handleEntitySelected(modalProps.preselectedEntity, modalProps.preselectedTrigger);
		} else if (isOpen && (modalProps?.preselectedEntityId || modalProps?.preselectedTriggerId)) {
			// Handle entity and trigger IDs by fetching the actual objects
			handleEntityAndTriggerIds(modalProps.preselectedEntityId, modalProps.preselectedTriggerId);
		} else if (isOpen && !modalProps?.preselectedEntity && showEntityDetails) {
			// Reset to entity list view when opening without preselected entity
			showEntityDetails = false;
			selectedEntity = null;
			selectedTrigger = null;
		}

		// focus the textarea if no selected entity
		if (promptTextarea && !selectedEntity) {
			promptTextarea.focus();
		}

		return () => {
			window.removeEventListener('resize', checkMobile);
		};
	});

	// Load matching entities when trigger is selected
	$effect(() => {
		if (selectedTrigger && selectedTrigger.provider !== 'schedule') {
			// Load matching entities for the selected trigger
			loadMatchingEntities(selectedTrigger.id, { showDismissed: false });
		}
	});

	// Auto-populate repository and branch on mount
	onMount(async () => {
		await autoPopulateRepoAndBranch();
	});

	async function autoPopulateRepoAndBranch() {
		if (selectedRepo || selectedBranch) return;

		try {
			const lastUsed = loadLastUsedRepoAndBranch();
			if (lastUsed && !selectedEntity) {
				selectedRepo = lastUsed.repository;
				selectedBranch = lastUsed.branch;
				return;
			}

			const latestAgent = await getLatestAgentRepoAndBranch();
			if (latestAgent) {
				selectedRepo = latestAgent.repository;
				selectedBranch = latestAgent.branch;
				saveLastUsedRepoAndBranch(latestAgent.repository, latestAgent.branch);
			}
		} catch (error) {
			console.warn('Failed to auto-populate repo and branch:', error);
		}
	}

	// GitHub event handlers
	function handleRepoSelected(repo: GitHubRepo | null, parsedBranch?: string) {
		selectedRepo = repo;

		if (parsedBranch) {
			selectedBranch = {
				name: parsedBranch,
				commit: { sha: '', url: '' },
				protected: false
			};
		} else {
			selectedBranch = null;
		}

		if (selectedRepo && selectedBranch) {
			saveLastUsedRepoAndBranch(selectedRepo, selectedBranch);
		}
	}

	function handleBranchSelected(branchObj: GitHubBranch | null) {
		selectedBranch = branchObj;

		if (selectedRepo && selectedBranch) {
			saveLastUsedRepoAndBranch(selectedRepo, selectedBranch);
		}
	}

	// Entity selection handler
	function handleEntitySelected(entity: UnifiedEntity | null, trigger?: NormalizedTrigger | null) {
		selectedEntity = entity;
		selectedTrigger = trigger || null;

		if (isMobile) {
			// On mobile, close entity selection overlay and don't show details
			showEntitySelection = false;
			showEntityDetails = false;
		} else {
			// On desktop, show entity details in the right panel
			showEntityDetails = !!entity;
		}

		if (!entity) return;

		// Extract GitHub info from trigger first
		if (trigger) {
			const githubInfo = extractGitHubInfoFromTrigger(trigger);
			console.log('GitHub info from trigger:', trigger, githubInfo);

			if (githubInfo) {
				if (githubInfo.url) {
					repositoryUrl = githubInfo.url;
					if (githubInfo.repo) {
						selectedRepo = githubInfo.repo;
					}
				}

				if (githubInfo.branch) {
					branch = githubInfo.branch;
					if (githubInfo.branchObj) {
						selectedBranch = githubInfo.branchObj;
					}
				}
			}
			console.log('Selected selectedRepo:', selectedRepo);

			// Auto-populate task description
			if (!prompt.trim()) {
				prompt = trigger.configuration?.agentConfig?.userGuidelines || '';
			}
		}

		// Extract GitHub info from entity
		const entityGithubInfo = extractGitHubInfoFromEntity(entity);
		if (entityGithubInfo) {
			// When there's no trigger, always use entity's repo info
			// When there's a trigger, only use entity's repo info if no repo is already set
			if (!trigger || !selectedRepo) {
				if (entityGithubInfo.url) {
					repositoryUrl = entityGithubInfo.url;
				}

				if (entityGithubInfo.repo) {
					selectedRepo = entityGithubInfo.repo;
				}
			}

			if (entityGithubInfo.branch) {
				branch = entityGithubInfo.branch;
				if (entityGithubInfo.branchObj) {
					selectedBranch = entityGithubInfo.branchObj;
				}
			}
		}
	}

	function handleEntityAndTriggerIds(entityId?: string | null, triggerId?: string | null) {
		// For now, we need to find the entity and trigger from the available data
		// Since we don't have direct ID lookup, we'll need to search through the trigger entity groups

		let foundEntity: UnifiedEntity | null = null;
		let foundTrigger: NormalizedTrigger | null = null;

		// Search through all trigger groups to find matching entity and trigger
		for (const group of $triggerEntityGroups) {
			if (triggerId && group.trigger.id === triggerId) {
				foundTrigger = group.trigger;
			}

			if (entityId) {
				const entity = group.entities.find((e: any) => e.id === entityId);
				if (entity) {
					foundEntity = entity;
					if (!foundTrigger) {
						foundTrigger = group.trigger;
					}
				}
			}
		}

		if (foundEntity) {
			handleEntitySelected(foundEntity, foundTrigger);
		}
	}

	async function handleSubmit() {
		if (!canSubmit || isSubmitting) return;

		if (validationErrors.length > 0) {
			error = validationErrors[0];
			return;
		}

		// Validate scheduling if enabled
		if (scheduleData.enabled && !scheduleData.isValid) {
			error = 'Please fix scheduling errors before creating the agent';
			return;
		}

		isSubmitting = true;
		error = '';

		try {
			let agentResult: any;
			let titleGenerationContext: any;

			// Handle scheduled agent creation
			if (scheduleData.enabled) {
				// Create a scheduled trigger first
				console.log({
					cronExpression: scheduleData.cronExpression,
					timezone: scheduleData.timezone || undefined,
					description: scheduleData.description,
					startDate: scheduleData.startDate || undefined,
					endDate: scheduleData.endDate || undefined
				});
				const triggerConfig = createTriggerConfiguration(
					[
						{ type: 'cronExpression', value: scheduleData.cronExpression },
						{
							type: 'timezone',
							value:
								scheduleData.timezone === 'UTC' ? undefined : scheduleData.timezone || undefined
						},
						{ type: 'description', value: scheduleData.description },
						{ type: 'startDate', value: scheduleData.startDate || undefined },
						{ type: 'endDate', value: scheduleData.endDate || undefined }
					],
					'schedule',
					'schedule',
					{
						name: `Scheduled Agent: ${prompt.trim().slice(0, 50)}...`,
						description: `Scheduled agent: ${scheduleData.description}`,
						userGuidelines: prompt.trim(),
						workspaceGuidelines: '',
						setupScript: '',
						repositoryUrl: derivedRepositoryUrl().trim(),
						branchSelection: selectedBranch?.name || branch.trim()
					},
					true,
					`${selectedRepo?.owner}/${selectedRepo?.name}`
				);
				console.log('Scheduled trigger config:', triggerConfig, scheduleData);

				// Create the trigger
				const result = await createTriggerAndSync(triggerConfig);
				console.log('Created scheduled trigger:', result);

				const triggerId = result?.trigger?.triggerId;
				console.log('Trigger ID:', triggerId);

				// Execute the trigger manually to create the first agent instance
				const entityId = `scheduled-execution-${new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-')}`;
				const remoteAgentIdPromise = executeTrigger(triggerId, entityId);

				// Create title generation context for scheduled trigger
				const titleGenerationContext = createTitleGenerationContext(
					undefined, // no specific entity for scheduled triggers
					result?.trigger, // the created trigger
					prompt.trim(), // use the user's prompt as user instructions
					{
						// No specific repository/branch info for scheduled triggers
					}
				);
				const generatedTitlePromise = generateAgentTitle(titleGenerationContext);

				const [remoteAgentId, generatedTitle] = await Promise.all([
					remoteAgentIdPromise,
					generatedTitlePromise
				]);

				if (!remoteAgentId) {
					throw new Error('Failed to execute scheduled trigger');
				}

				agentResult = { remoteAgentId };

				// Update title in background
				if (generatedTitle) {
					try {
						await apiClient.agents.updateTitle(remoteAgentId, generatedTitle);
						console.log('Agent title updated successfully:', generatedTitle);
						// Refresh agents list to show the updated title
						loadAgents(true);
					} catch (titleUpdateError) {
						console.warn('Failed to update agent title:', titleUpdateError);
					}
				}
			} else if (selectedEntity) {
				if (!selectedTrigger) {
					throw new Error('No trigger selected');
				}
				const options: any = {
					branch: selectedBranch?.name || branch.trim(),
					repositoryUrl: derivedRepositoryUrl().trim()
				};

				// Create agent first
				agentResult = await createRemoteAgentManually(
					selectedEntity,
					selectedTrigger.id,
					prompt.trim(),
					options
				);

				// Prepare title generation context for background processing
				titleGenerationContext = createTitleGenerationContext(
					selectedEntity,
					selectedTrigger,
					prompt.trim(),
					{
						repositoryUrl: derivedRepositoryUrl().trim(),
						branch: selectedBranch?.name || branch.trim()
					}
				);
			} else {
				const workspaceSetup: CreateAgentRequest['workspaceSetup'] = {
					startingFiles: {
						githubCommitRef: {
							repositoryUrl: derivedRepositoryUrl().trim(),
							gitRef: selectedBranch?.name || branch.trim()
						}
					},
					...(setupScriptContent.trim() && {
						setupScript: setupScriptContent.trim()
					})
				};

				const wrappedPrompt = wrapInitialMessageWithLatestInstructions(prompt.trim());

				// Create agent first
				agentResult = await apiClient.agents.create({
					initialRequestDetails: {
						requestNodes: [
							{
								id: 1,
								type: ChatRequestNodeType.TEXT,
								textNode: {
									content: wrappedPrompt
								}
							}
						]
					},
					workspaceSetup
				});

				// Prepare title generation context for background processing
				titleGenerationContext = createTitleGenerationContext(
					undefined, // no entity for direct agent creation
					undefined, // no trigger for direct agent creation
					prompt.trim(),
					{
						repositoryUrl: derivedRepositoryUrl().trim(),
						branch: selectedBranch?.name || branch.trim()
					}
				);
			}

			// Track recently used repo and branch after successful creation
			if (selectedRepo) {
				addRecentRepo(selectedRepo);
				if (selectedBranch) {
					addRecentBranch(selectedRepo, selectedBranch);
				}
			}

			// Reset form and close modal immediately after agent creation
			resetForm();
			handleCloseModal();

			// Refresh agents data
			loadAgents(true);

			// Continue title generation and update in background
			// This will continue even after component unmounts
			handleBackgroundTitleUpdate(agentResult, titleGenerationContext);
		} catch (err) {
			console.error('Failed to create agent:', err);
			const errorCode = determineErrorCodeFromResponse(err);
			const errorMessage = getErrorMessage(errorCode);
			error = errorMessage.message;
		} finally {
			isSubmitting = false;
		}
	}

	/**
	 * Handle title generation and update in background after modal closes
	 */
	async function handleBackgroundTitleUpdate(agentResult: any, titleGenerationContext: any) {
		try {
			// Generate title in background
			const generatedTitle = await generateAgentTitle(titleGenerationContext);

			if (generatedTitle) {
				// Determine the correct agent ID based on the result structure
				const agentId = agentResult?.agentId || agentResult?.remoteAgentId;

				if (agentId) {
					try {
						await apiClient.agents.updateTitle(agentId, generatedTitle);
						console.log('Agent title updated successfully in background:', generatedTitle);

						// Refresh agents data to show the updated title
						loadAgents(true);
					} catch (titleUpdateError) {
						console.warn('Failed to update agent title in background:', titleUpdateError);
						// Don't fail silently - this is just a background operation
					}
				} else {
					console.warn('No agent ID found for background title update');
				}
			}
		} catch (titleGenerationError) {
			console.warn('Failed to generate title in background:', titleGenerationError);
			// Don't fail silently - this is just a background operation
		}
	}

	function resetForm() {
		prompt = '';
		selectedRepo = null;
		selectedBranch = null;
		repositoryUrl = 'https://github.com/augmentcode/augment';
		branch = 'main';
		selectedEntity = null;
		selectedTrigger = null;
		searchTerm = '';
		showEntityDetails = false;
		error = '';
		selectedSetupScript = null;
		setupScriptContent = '';
		isInstructionsExpanded = false;
	}

	function handleCloseModal() {
		closeModal();
		modalProps?.onClose?.();

		// If we're on a create route, navigate back to the parent route
		const currentPath = page.route.id || '';
		if (currentPath.includes('/create')) {
			if (currentPath.includes('/agents/create')) {
				goto('/agents');
			} else if (currentPath.includes('/inbox/create')) {
				goto('/inbox');
			}
		}
	}

	function handleBackToEntityList() {
		// Clear entity selection first
		showEntityDetails = false;
		selectedEntity = null;
		selectedTrigger = null;
		searchTerm = '';
		prompt = '';
		isInstructionsExpanded = false;
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' && (event.metaKey || event.ctrlKey)) {
			event.preventDefault();
			handleSubmit();
		}
	}

	function handlePromptFocus() {
		isPromptFocused = true;
	}

	function handlePromptBlur() {
		isPromptFocused = false;
	}

	// Prompt enhancer functions
	function handlePromptEnhanced(enhancedPrompt: string) {
		prompt = enhancedPrompt;
	}

	function handlePromptEnhancementError(error: PromptEnhancementResult) {
		console.error('Prompt enhancement error:', error);
		// Could show a toast notification here in the future
	}

	function getEntityLabels(): string[] {
		if (!selectedEntity?.labels) return [];
		return selectedEntity.labels.map((label) => (typeof label === 'string' ? label : label.name));
	}

	// Mobile-specific handlers
	function handleMobileEntityCardClick() {
		if (selectedEntity) {
			// If entity is selected, open entity selection to change it
			showEntitySelection = true;
		} else {
			// If no entity selected, open entity selection
			showEntitySelection = true;
		}
	}

	function handleMobileEntitySelectionClose() {
		showEntitySelection = false;
	}

	function handleMobileEntityClear() {
		selectedEntity = null;
		selectedTrigger = null;
		showEntitySelection = false;
	}

	// Instructions UI helpers
	function toggleInstructionsExpanded() {
		isInstructionsExpanded = !isInstructionsExpanded;
	}

	// Setup script handlers
	function handleSetupScriptSelected(script: GithubSetupScript | null) {
		// Convert GithubSetupScript to SetupScript format if needed
		if (script) {
			selectedSetupScript = {
				name: script.displayName || script.name,
				path: script.name,
				content: '', // Content will be loaded by SetupScriptPicker
				location: 'browser' as const
			};
		} else {
			selectedSetupScript = null;
		}
		// Note: setupScriptContent is managed by SetupScriptPicker component
	}

	function handleSetupTypeChanged(type: 'basic' | 'auto' | 'manual') {
		setupScriptType = type;
		// Clear script content when changing type
		if (type !== 'manual') {
			setupScriptContent = '';
			selectedSetupScript = null;
		}
	}

	function handleSetupScriptError({ message }: { message: string }) {
		console.error('Setup script error:', message);
		// Could show a toast or set an error state here if needed
	}

	// Scheduling handlers
	function handleScheduleUpdate(data: typeof scheduleData) {
		scheduleData = { ...data };
	}

	function handleScheduleToggle(enabled: boolean) {
		scheduleData.enabled = enabled;
	}
</script>

<Modal
	{isOpen}
	title="Create new Agent"
	size="xl"
	onClose={handleCloseModal}
	class="flex h-[98dvh] flex-col lg:h-[85dvh]"
>
	<div class="flex h-full min-h-0 flex-col md:flex-row">
		<!-- Agent creation form -->
		<div class="relative z-10 flex min-w-0 flex-[2] flex-col">
			<div class="flex-1 overflow-y-auto p-3 lg:p-6">
				<form
					onsubmit={(e) => {
						e.preventDefault();
						handleSubmit();
					}}
					class="space-y-4"
				>
					<!-- Mobile Selected Entity Card -->
					{#if isMobile}
						<div class="space-y-2">
							<div class="flex items-center justify-between">
								<div class="block text-sm font-medium text-slate-700 dark:text-slate-300">
									Action to work on (optional)
								</div>
								{#if selectedEntity}
									<div class="flex items-center gap-2">
										<Button
											type="button"
											onclick={handleMobileEntityCardClick}
											variant="ghost"
											size="icon-sm"
											icon={Pencil}
										/>
										<Button
											type="button"
											onclick={handleMobileEntityClear}
											variant="ghost"
											size="icon-sm"
											icon={XMark}
										/>
									</div>
								{/if}
							</div>
							{#if selectedEntity}
								<div class="w-full">
									<EntityCardSmall entity={selectedEntity} />
								</div>
							{:else}
								<button
									type="button"
									onclick={handleMobileEntityCardClick}
									class="w-full rounded-lg border border-dashed border-slate-300 p-4 text-center text-slate-500 transition-colors hover:border-slate-400 dark:border-slate-600 dark:text-slate-400 dark:hover:border-slate-500"
								>
									<div class="space-y-1">
										<div class="text-sm font-medium">Select an Action</div>
										<div class="text-xs">Tap to browse available actions</div>
									</div>
								</button>
							{/if}
						</div>
					{/if}

					<!-- Repository Configuration -->
					<div class="space-y-2">
						<div class="block text-sm font-medium text-slate-700 dark:text-slate-300">
							Repository & branch the agent will work on
						</div>
						<Tooltip
							tooltipClass="!max-w-90"
							class="w-full"
							text={selectedEntity
								? 'Set to the repository and branch of the selected Action.'
								: ''}
							delay={0}
						>
							<div class="flex flex-col gap-2 lg:flex-row">
								<div class="flex-1">
									<GitHubRepoCombobox
										value={selectedRepo}
										onValueChange={handleRepoSelected}
										disabled={!!selectedEntity}
										placeholder="Search repositories or paste URL..."
										maxWidth="32rem"
										class="w-full"
									/>
								</div>
								{#if selectedRepo}
									<div class="lg:w-52">
										<GitHubBranchCombobox
											repository={selectedRepo}
											value={selectedBranch}
											onValueChange={handleBranchSelected}
											placeholder="Branch..."
											disabled={!!selectedEntity}
											maxWidth="16rem"
											class="w-full"
										/>
									</div>
								{/if}
							</div>
						</Tooltip>
					</div>

					<!-- Prompt Input -->
					<div class="">
						<label
							for="prompt"
							class="mb-2 block text-sm font-medium text-slate-700 dark:text-slate-300"
						>
							Instructions for the agent
						</label>

						<!-- Textarea with optional overlay for collapsed state -->
						<div
							class="relative"
							in:receive={{ key: 'agent-textarea' }}
							out:send={{ key: 'agent-textarea' }}
						>
							<div class={selectedEntity && !isInstructionsExpanded ? '' : ''}>
								<Textarea
									id="prompt"
									bind:value={prompt}
									bind:textareaElement={promptTextarea}
									placeholder={selectedEntity
										? `Describe what you want the agent to do with "${selectedEntity.title}"...`
										: 'Describe the task you want the agent to complete...'}
									rows={selectedEntity ? 6 : 15}
									class="w-full"
									textareaClass={`transition-all pr-10 ${selectedEntity && !isInstructionsExpanded ? 'min-h-28 h-28 max-h-28' : 'min-h-52'}`}
									autoResize={true}
									onkeydown={handleKeydown}
									onfocus={handlePromptFocus}
									onblur={handlePromptBlur}
									required
									disabled={!!(selectedEntity && !isInstructionsExpanded)}
								/>

								<!-- Prompt Enhancer -->
								{#if !(selectedEntity && !isInstructionsExpanded)}
									<div class="absolute top-2 right-2">
										<PromptEnhancer
											{prompt}
											onEnhanced={handlePromptEnhanced}
											onError={handlePromptEnhancementError}
											isDisabled={!!(selectedEntity && !isInstructionsExpanded)}
										/>
									</div>
								{/if}

								{#if selectedEntity && !isInstructionsExpanded}
									<!-- Transparent overlay with customize button -->
									<div
										class="absolute inset-[1px] flex items-center justify-center rounded-lg bg-white/90 dark:bg-slate-900/80"
									>
										<Button onclick={toggleInstructionsExpanded} variant="secondary">
											Customize Instructions
										</Button>
									</div>
								{/if}
							</div>

							{#if selectedEntity && isInstructionsExpanded}
								<button
									type="button"
									onclick={toggleInstructionsExpanded}
									class="absolute top-2 right-2 rounded-md p-1 text-slate-400 hover:bg-slate-100 hover:text-slate-600 dark:hover:bg-slate-700 dark:hover:text-slate-300"
									title="Collapse instructions"
								>
									<Icon src={ChevronUp} class="h-4 w-4" mini />
								</button>
							{/if}

							{#if showKeyboardHint}
								<div
									class="absolute right-2 bottom-2 text-xs text-slate-500 dark:text-slate-400"
									transition:slide={{ axis: 'y' }}
								>
									⌘ + Enter to create
								</div>
							{/if}
						</div>
					</div>

					<!-- {#if selectedEntity}
						<div class="flex w-full flex-col gap-1">
							<div
								class="flex items-center gap-1 text-xs font-medium text-slate-500 dark:text-slate-400"
							>
								<Icon src={Link} class="inline-block h-3 w-3" micro />
								Linked to {getProviderDisplayName(selectedEntity.providerId)}
								{getEntityTypeDisplayName(selectedEntity.entityType)}
								via {selectedTrigger?.name}
							</div>
							<div class="flex w-full max-w-92">
								<EntityCardSmall entity={selectedEntity} />
								<Button
									variant="ghost"
									size="icon-sm"
									onclick={handleBackToEntityList}
									icon={XMark}
									class="my-auto ml-3 h-auto flex-none"
								/>
							</div>
						</div>
					{/if} -->

					<!-- Setup Script Selection -->
					<div class="space-y-2">
						<SetupScriptPicker
							bind:selectedSetupType={setupScriptType}
							onSetupTypeChanged={handleSetupTypeChanged}
							onScriptSelected={handleSetupScriptSelected}
							repositoryUrl={derivedRepositoryUrl()}
							branch={selectedBranch?.name || branch}
							disableAutoGeneration={!derivedRepositoryUrl().trim()}
							onAutoGeneration={handleCloseModal}
						/>
					</div>

					<!-- Error Display -->
					{#if error}
						<div
							class="flex items-start gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20"
						>
							<div class="mt-0.5 h-4 w-4 flex-shrink-0 rounded-full bg-red-500"></div>
							<span class="text-sm text-red-700 dark:text-red-400">{error}</span>
						</div>
					{/if}

					<!-- Validation Warnings -->
					{#if validationWarnings.length > 0}
						<div
							class="flex items-start gap-2 rounded-lg border border-amber-200 bg-amber-50 p-3 px-3.5 dark:border-amber-800 dark:bg-amber-900/20"
						>
							<div class="w-full overflow-hidden text-sm text-amber-700 dark:text-amber-400">
								{#each validationWarnings as warning}
									<div class="mb-2">{warning}</div>
								{/each}

								{#if hasAgentLimitWarning && agents.length > 0}
									<div class="mt-3 space-y-2">
										<div class="text-xs font-medium text-amber-800 dark:text-amber-300">
											Current agents:
										</div>
										<div class="space-y-1">
											{#each agents as agent}
												<div
													class="flex items-center justify-between gap-2 rounded border border-slate-200 bg-white px-3 py-2 text-slate-700 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-300"
												>
													<div class="min-w-0 flex-1">
														<div class="flex gap-2">
															<a
																href="/agents/{agent.id}"
																class="block flex-1 truncate text-xs font-medium"
																onclick={handleCloseModal}
															>
																{extractInitialInstructionsFromSessionSummary(agent.sessionSummary)}
															</a>
															<RemoteAgentStatusIndicator
																status={agent.status}
																workspaceStatus={agent.workspaceStatus}
																hasUpdates={agent.hasUpdates}
																size="sm"
																isExpanded
															/>
														</div>
														<div class="mt-0.5 text-xs">
															Started {getRelativeTime(agent.startedAt)} ago
															{#if agent.updatedAt && getRelativeTime(agent.updatedAt) !== getRelativeTime(agent.startedAt)}
																• Updated {getRelativeTime(agent.updatedAt)} ago
															{/if}
														</div>
													</div>
													<Button
														type="button"
														variant="secondary"
														size="icon-sm"
														onclick={() => handleDeleteAgent(agent.id)}
														disabled={deletingAgents.has(agent.id)}
														class="flex-shrink-0"
														icon={Trash}
													></Button>
												</div>
											{/each}
										</div>
									</div>
								{/if}
							</div>
						</div>
					{/if}

					<!-- Agent Limits Info -->
					{#if $agentStats}
						<div class="text-xs text-slate-500 dark:text-slate-400">
							Active agents: {$agentStats.activeCount}/{$agentStats.maxActive}
							• Total agents: {$agentStats.totalCount}/{$agentStats.maxTotal}
						</div>
					{/if}

					<!-- Agent Scheduling Section -->
					<div class="border-t border-slate-200 pt-4 dark:border-slate-700">
						<AgentScheduler
							bind:enabled={scheduleData.enabled}
							bind:cronExpression={scheduleData.cronExpression}
							bind:timezone={scheduleData.timezone}
							bind:description={scheduleData.description}
							bind:startDate={scheduleData.startDate}
							bind:endDate={scheduleData.endDate}
							onToggle={handleScheduleToggle}
							disabled={isSubmitting}
						/>
					</div>
				</form>
			</div>

			<!-- Footer with submit button -->
			<div class="border-t border-slate-200 py-3 dark:border-slate-700">
				<div class="flex justify-end gap-3 px-6">
					<Button
						type="button"
						variant="secondary"
						onclick={handleCloseModal}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						type="button"
						variant="primary"
						disabled={!canSubmit || isSubmitting}
						onclick={handleSubmit}
						class={isSubmitting ? 'animate-pulse' : ''}
						icon={isSubmitting ? undefined : PaperAirplane}
					>
						{#if isSubmitting}
							Creating...
						{:else}
							Create Agent
						{/if}
					</Button>
				</div>
			</div>
		</div>

		<!-- Entity selection or details - Hidden on mobile unless in selection mode -->
		<div
			class="relative z-[5] min-w-0 flex-1 flex-col border-slate-200 md:border-l dark:border-slate-700 {isMobile
				? 'hidden'
				: 'flex'}"
		>
			{#if showEntityDetails && selectedEntity}
				<!-- Entity Details View -->
				<div class="border-b border-slate-200 px-3 py-2.5 pl-6 dark:border-slate-700">
					<div class="flex items-center gap-1 text-sm font-medium text-slate-500">
						<Icon src={Link} class="inline-block h-4 w-4" micro />
						Linked to {getProviderDisplayName(selectedEntity.providerId)}
						{getEntityTypeDisplayName(selectedEntity.entityType)}

						<Button
							variant="ghost"
							size="sm"
							onclick={handleBackToEntityList}
							class="ml-auto flex items-center gap-2 text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white"
							icon={XMark}
							iconPosition="right"
						>
							Unlink
						</Button>
					</div>
				</div>
			{:else}
				<!-- Entity Search -->
				<div class="border-b border-slate-200 px-2 py-2 pb-2 dark:border-slate-700">
					<div class="relative">
						<Input
							icon={MagnifyingGlass}
							type="text"
							inputClass="pl-9"
							bind:value={searchTerm}
							placeholder="Search Action matches..."
						/>
					</div>
				</div>
			{/if}

			<div class="relative flex min-h-0 flex-1 overflow-hidden">
				<div class="h-full w-full flex-1 overflow-auto">
					<!-- Entity List -->
					<div class="relative z-0 flex-1 py-4" transition:fly={{ x: -50 }}>
						{#if isEntitiesLoading}
							<!-- Loading Skeletons -->
							<div class="space-y-3">
								{#each Array(3) as _, triggerIndex}
									<div class="">
										<!-- Trigger Header Skeleton -->
										<div class="flex w-full items-center justify-between px-4 py-2">
											<div
												class="h-4 w-32 animate-pulse rounded bg-slate-200 dark:bg-slate-700"
											></div>
											<div class="flex items-center gap-2">
												<div
													class="h-4 w-6 animate-pulse rounded bg-slate-200 dark:bg-slate-700"
												></div>
												<div
													class="h-4 w-4 animate-pulse rounded bg-slate-200 dark:bg-slate-700"
												></div>
											</div>
										</div>

										<!-- Entity Skeletons -->
										<div class="">
											{#each Array(2 + triggerIndex) as _}
												<MatchRowSkeleton />
											{/each}
										</div>
									</div>
								{/each}
							</div>
						{:else if filteredEntitiesByTrigger.length === 0}
							<div class="px-3 py-8 text-center text-slate-500 dark:text-slate-400">
								{searchTerm ? 'No matching entities found' : 'No entities available'}
							</div>
						{:else}
							<div class="space-y-3">
								{#each filteredEntitiesByTrigger as { trigger, entities }}
									{@const isExpanded = !collapsedTriggers.has(trigger.id)}
									<div class="">
										<!-- Trigger Header -->
										<button
											class="sticky top-0 z-20 flex w-full cursor-pointer items-center justify-between border-b border-slate-200 bg-white px-4 py-2 transition-colors hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-900 dark:hover:bg-slate-800/50"
											onclick={() => toggleTriggerExpansion(trigger.id)}
										>
											<h3 class="text-sm font-medium text-slate-700 dark:text-slate-300">
												{trigger.name}
											</h3>
											<div class="flex items-center gap-2">
												<span class="text-xs text-slate-600 dark:text-slate-400">
													{entities.length}
												</span>
												<Icon
													src={ChevronDown}
													class="h-4 w-4 text-slate-500 transition-transform dark:text-slate-400 {isExpanded
														? ''
														: 'rotate-180'}"
													micro
												/>
											</div>
										</button>

										<!-- Collapsible Content -->
										{#if isExpanded}
											<div class="w-full" transition:slide={{ axis: 'y' }}>
												{#each entities as entity}
													<div class="w-full" transition:slide={{ axis: 'y' }}>
														<MatchRow
															{entity}
															triggerId={trigger.id}
															showActions={false}
															showHoverCard={false}
															onclick={() => handleEntitySelected(entity, trigger)}
															showDismissButton
														/>
													</div>
												{/each}
											</div>
										{/if}
									</div>
								{/each}
							</div>
						{/if}
					</div>
				</div>

				{#if showEntityDetails && selectedEntity}
					<!-- Entity Details Content -->
					<div
						class="absolute inset-0 z-20 space-y-6 overflow-y-auto bg-white p-6 dark:bg-slate-800"
						transition:fly={{ x: 50 }}
					>
						<!-- Entity Title and Status -->
						<div class="space-y-3">
							<div class="flex w-full">
								<h1
									class="flex-1 text-lg leading-tight font-semibold text-slate-900 dark:text-white"
								>
									{selectedEntity.title}
								</h1>
								<a href={selectedEntity.url} target="_blank" class="mt-0.5">
									<Icon
										src={ArrowTopRightOnSquare}
										class="h-4 w-4 text-slate-500 dark:text-slate-400"
										micro
									/>
								</a>
							</div>

							<div class="-mt-1 mb-2 flex w-full gap-2">
								{#if selectedEntity.createdAt}
									<time class="text-xs text-slate-500 dark:text-slate-400">
										Created <ReactiveRelativeTime date={selectedEntity.createdAt} />
									</time>
								{/if}
								<!-- updated or started -->
								{#if selectedEntity.updatedAt && selectedEntity.updatedAt !== selectedEntity.createdAt}
									<time class="text-xs text-slate-500 dark:text-slate-400">
										Updated <ReactiveRelativeTime date={selectedEntity.updatedAt} />
									</time>
								{/if}
							</div>

							<!-- Entity metadata -->
							<div
								class="flex flex-wrap items-center gap-2 text-xs text-slate-600 dark:text-slate-400"
							>
								<div
									class="flex items-center gap-1.5 text-xs whitespace-nowrap text-slate-500 dark:text-slate-400"
								>
									<!-- Provider icon -->
									<CustomIcon icon={selectedEntity.providerId} size={16} class="flex-shrink-0" />

									<!-- <Icon src={getEntityTypeIcon(selectedEntity.providerId, selectedEntity.entityType)} class="w-4 h-4" micro /> -->

									{#if selectedEntity.author}
										<div class="flex items-center gap-1.5">
											<UserAvatar
												user={selectedEntity.author}
												providerId={selectedEntity.providerId}
												size={16}
											/>
											<span class="hidden @[500px]:inline">
												{typeof selectedEntity.author === 'string'
													? selectedEntity.author
													: selectedEntity.author?.name ||
														selectedEntity.author?.login ||
														'Unknown'}
											</span>
										</div>
									{/if}

									{#if selectedEntity.assignees && selectedEntity.assignees.length > 0}
										{#if selectedEntity.author}
											<Icon src={ArrowRight} class="h-3 w-3 text-slate-400" mini />
										{/if}
										<div class="flex items-center gap-1.5">
											<UserAvatar
												user={selectedEntity.assignees[0]}
												providerId={selectedEntity.providerId}
												size={16}
											/>
											<span class="hidden @[500px]:inline">
												{selectedEntity.assignees[0]?.display_name ||
													selectedEntity.assignees[0]?.name}
											</span>
											{#if selectedEntity.assignees.length > 1}
												<span class="hidden text-slate-400 @[500px]:inline"
													>+{selectedEntity.assignees.length - 1}</span
												>
											{/if}
										</div>
									{/if}

									{#if selectedTrigger?.name}
										<span class="text-slate-400">•</span>
										<span>via {selectedTrigger.name}</span>
									{/if}
								</div>
							</div>
						</div>

						<!-- Description -->
						{#if selectedEntity.description}
							<div class="space-y-3">
								<div
									class="prose prose-sm dark:prose-invert overflow-wrap-anywhere max-w-none break-words text-slate-600 dark:text-slate-400"
								>
									<Markdown content={selectedEntity.description} />
								</div>
							</div>
						{/if}

						<!-- Labels -->
						{#if getEntityLabels().length > 0}
							<div class="space-y-2">
								<h3 class="text-sm font-medium text-slate-700 dark:text-slate-300">Labels</h3>
								<div class="flex flex-wrap gap-2">
									{#each getEntityLabels() as label}
										<span
											class="inline-flex items-center rounded-md border border-slate-200 bg-slate-100 px-2.5 py-1 text-xs font-medium text-slate-700 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-300"
										>
											{label}
										</span>
									{/each}
								</div>
							</div>
						{/if}
					</div>
				{/if}
			</div>
		</div>
	</div>

	<!-- Mobile Entity Selection Overlay -->
	{#if isMobile && showEntitySelection}
		<div
			class="fixed inset-0 z-50 flex flex-col bg-white dark:bg-slate-900"
			transition:fly={{ y: '100%', duration: 300 }}
		>
			<!-- Header -->
			<div
				class="flex items-center justify-between border-b border-slate-200 p-4 dark:border-slate-700"
			>
				<h2 class="text-lg font-semibold text-slate-900 dark:text-white">Select Action</h2>
				<Button
					variant="ghost"
					size="sm"
					onclick={handleMobileEntitySelectionClose}
					icon={ArrowLeft}
				>
					Back
				</Button>
			</div>

			<!-- Search -->
			<div class="border-b border-slate-200 p-2 dark:border-slate-700">
				<Input
					icon={MagnifyingGlass}
					type="text"
					inputClass="pl-9"
					bind:value={searchTerm}
					placeholder="Search Action matches..."
				/>
			</div>

			<!-- Entity List -->
			<div class="flex-1 overflow-y-auto">
				{#if isEntitiesLoading}
					<!-- Loading Skeletons -->
					<div class="space-y-3 p-4">
						{#each Array(3) as _, triggerIndex}
							<div class="">
								<!-- Trigger Header Skeleton -->
								<div class="flex w-full items-center justify-between px-4 py-2">
									<div class="h-4 w-32 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
									<div class="flex items-center gap-2">
										<div class="h-4 w-6 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
										<div class="h-4 w-4 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
									</div>
								</div>

								<!-- Entity Skeletons -->
								<div class="">
									{#each Array(2 + triggerIndex) as _}
										<MatchRowSkeleton />
									{/each}
								</div>
							</div>
						{/each}
					</div>
				{:else if filteredEntitiesByTrigger.length === 0}
					<div class="px-3 py-8 text-center text-slate-500 dark:text-slate-400">
						{searchTerm ? 'No matching entities found' : 'No entities available'}
					</div>
				{:else}
					<div class="space-y-3">
						{#each filteredEntitiesByTrigger as { trigger, entities }}
							{@const isExpanded = !collapsedTriggers.has(trigger.id)}
							<div class="">
								<!-- Trigger Header -->
								<button
									class="sticky top-0 z-20 flex w-full items-center justify-between border-b border-slate-200 bg-white px-4 py-2 transition-colors hover:bg-slate-50 dark:border-slate-700 dark:bg-slate-900 dark:hover:bg-slate-800/50"
									onclick={() => {
										if (isExpanded) {
											collapsedTriggers.add(trigger.id);
										} else {
											collapsedTriggers.delete(trigger.id);
										}
										collapsedTriggers = new Set(collapsedTriggers);
									}}
								>
									<div class="flex items-center gap-2">
										<span class="text-sm font-medium text-slate-700 dark:text-slate-300">
											{trigger.name}
										</span>
									</div>
									<div class="flex items-center gap-2">
										<span class="text-xs font-medium text-slate-500 dark:text-slate-400">
											{entities.length}
										</span>
										<Icon
											src={ChevronDown}
											class="h-4 w-4 text-slate-400 transition-transform {isExpanded
												? ''
												: '-rotate-90'}"
											mini
										/>
									</div>
								</button>

								<!-- Entity List -->
								{#if isExpanded}
									<div class="" transition:slide>
										{#each entities as entity}
											<MatchRow
												{entity}
												triggerId={trigger.id}
												showActions={false}
												onclick={() => handleEntitySelected(entity, trigger)}
												showDismissButton
											/>
										{/each}
									</div>
								{/if}
							</div>
						{/each}
					</div>
				{/if}
			</div>
		</div>
	{/if}
</Modal>

<style>
	/* Handle long URLs in markdown content */
	:global(.prose a) {
		word-break: break-all;
		overflow-wrap: anywhere;
		hyphens: auto;
	}

	/* Ensure code blocks also break properly */
	:global(.prose code) {
		word-break: break-all;
		overflow-wrap: anywhere;
	}

	/* Handle pre blocks */
	:global(.prose pre) {
		overflow-x: auto;
		white-space: pre-wrap;
		word-break: break-all;
	}
</style>
