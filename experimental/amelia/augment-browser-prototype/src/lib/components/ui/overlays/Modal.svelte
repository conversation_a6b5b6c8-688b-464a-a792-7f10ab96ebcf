<script lang="ts">
	import { Icon, XMark } from 'svelte-hero-icons';
	import { fly, fade } from 'svelte/transition';
	import { onMount, onDestroy } from 'svelte';
	import Portal from './Portal.svelte';

	interface Props {
		isOpen: boolean;
		title?: string;
		size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
		onClose?: () => void;
		closeOnEscape?: boolean;
		closeOnBackdrop?: boolean;
		showCloseButton?: boolean;
		class?: string;
	}

	let {
		isOpen = false,
		title,
		size = 'lg',
		onClose,
		closeOnEscape = true,
		closeOnBackdrop = true,
		showCloseButton = true,
		class: className = ''
	}: Props = $props();

	let modalElement: HTMLElement;

	const sizeClasses = {
		sm: 'max-w-md max-h-[95vh]',
		md: 'max-w-lg max-h-[95vh]',
		lg: 'max-w-4xl max-h-[95vh]',
		xl: 'max-w-6xl max-h-[95vh]',
		full: 'max-w-[95vw max-h-[95vh]]'
	};

	function handleKeydown(event: KeyboardEvent) {
		if (closeOnEscape && event.key === 'Escape') {
			onClose?.();
		}
	}

	function handleBackdropClick(event: MouseEvent) {
		if (closeOnBackdrop && event.target === event.currentTarget) {
			onClose?.();
		}
	}

	// Focus management
	onMount(() => {
		if (isOpen) {
			document.addEventListener('keydown', handleKeydown);
			// Focus the modal for accessibility
			modalElement?.focus();
		}
	});

	onDestroy(() => {
		document.removeEventListener('keydown', handleKeydown);
	});

	$effect(() => {
		if (isOpen) {
			document.addEventListener('keydown', handleKeydown);
			// Prevent body scroll when modal is open
			document.body.style.overflow = 'hidden';
		} else {
			document.removeEventListener('keydown', handleKeydown);
			document.body.style.overflow = '';
		}

		return () => {
			document.removeEventListener('keydown', handleKeydown);
			document.body.style.overflow = '';
		};
	});
</script>

{#if isOpen}
	<Portal>
		<!-- Backdrop -->
		<div
			class="fixed inset-0 z-50 flex items-center justify-center p-2"
			transition:fade={{ duration: 200 }}
		>
			<!-- Backdrop overlay -->
			<button
				class="absolute inset-0 cursor-pointer bg-white/90 backdrop-blur-sm dark:bg-black/90"
				onclick={handleBackdropClick}
				tabindex="-1"
			/>

			<!-- Modal -->
			<div
				bind:this={modalElement}
				class="relative z-10 w-full {sizeClasses[
					size
				]} flex max-h-[98dvh] flex-col overflow-hidden rounded-lg border border-slate-200 bg-white shadow-xl dark:border-slate-700 dark:bg-slate-900 {className}"
				role="dialog"
				aria-modal="true"
				aria-labelledby={title ? 'modal-title' : undefined}
				tabindex="-1"
				transition:fly={{ y: 20, duration: 200 }}
			>
				<!-- Header -->
				{#if title || showCloseButton}
					<div
						class="flex flex-none items-center justify-between border-b border-slate-200 px-6 py-4 dark:border-slate-700"
					>
						{#if title}
							<h2 id="modal-title" class="text-lg font-semibold text-slate-900 dark:text-white">
								{title}
							</h2>
						{:else}
							<div></div>
						{/if}

						{#if showCloseButton}
							<button
								type="button"
								class="rounded-md p-1 text-slate-400 hover:bg-slate-100 hover:text-slate-500 dark:hover:bg-slate-800 dark:hover:text-slate-300"
								onclick={onClose}
								aria-label="Close modal"
							>
								<Icon src={XMark} class="h-5 w-5" />
							</button>
						{/if}
					</div>
				{/if}

				<!-- Content -->
				<div class="flex-1 overflow-y-auto">
					<slot />
				</div>
			</div>
		</div>
	</Portal>
{/if}
