<script lang="ts">
	import {
		Icon,
		ExclamationTriangle,
		ChevronRight,
		MagnifyingGlass,
		LightBulb,
		Trash,
		XMark,
		Bolt,
		InformationCircle
	} from 'svelte-hero-icons';

	import Button from '../navigation/Button.svelte';
	import Drawer from './Drawer.svelte';
	import FormField from '../forms/FormField.svelte';
	import PromptEnhancer from '../PromptEnhancer.svelte';
	import type { PromptEnhancementResult, PromptEnhancementContext } from '$lib/utils/prompts';
	import Tooltip from './Tooltip.svelte';
	import ButtonGroup from '../navigation/ButtonGroup.svelte';
	import IterativeChat from '../content/IterativeChat.svelte';

	import { session } from '$lib/stores/auth';
	import { checkProviderAuth, providerAuthStates } from '$lib/stores/provider-auth';
	import { PROVIDERS } from '$lib/providers';
	import ProviderAuthButton from '$lib/components/ui/navigation/ProviderAuthButton.svelte';
	import {
		createTriggerAndSync,
		updateTriggerAndSync,
		deleteTriggerAndSync
	} from '$lib/stores/data-operations.svelte';
	import { type UnifiedEntity } from '$lib/utils/entity-conversion';
	import { debounce } from '$lib/utils/timing';
	import { slide } from 'svelte/transition';
	import { getEntityTypeDisplayName, getProviderDisplayName } from '$lib/config/entity-types';
	import { storedState } from '$lib/utils/stored-state.svelte';
	import MatchRow from '../data-display/MatchRow.svelte';
	import { getCurrentRepository } from '$lib/utils/project-repository';

	import SimpleConditionsBuilder from '$lib/components/ui/forms/SimpleConditionsBuilder.svelte';
	import AdvancedConditionsBuilder from '$lib/components/ui/forms/AdvancedConditionsBuilder.svelte';
	import TriggerAutoToggle from '$lib/components/ui/TriggerAutoToggle.svelte';
	import ActivityTypeSelector from '$lib/components/ui/ActivityTypeSelector.svelte';
	import GitHubRepoCombobox from '$lib/components/github/GitHubRepoCombobox.svelte';
	import SetupScriptPicker from '../../agents/SetupScriptPicker.svelte';

	// Import utility functions
	import {
		getConditionOptionsFromConfig,
		conditionsToBackendFormat as utilConditionsToBackendFormat,
		backendToConditions as utilBackendToConditions,
		triggerConfigToConditions as utilTriggerConfigToConditions,
		getDefaultInstructions as utilGetDefaultInstructions,
		createTriggerConfigForFetching as utilCreateTriggerConfigForFetching,
		createTriggerConfiguration,
		type Condition,
		type TriggerFormData
	} from '$lib/utils/trigger-form-utils';
	import { apiClient } from '$lib/api/unified-client';
	import Textarea from '../forms/Textarea.svelte';
	import { onMount } from 'svelte';

	interface TriggerResult {
		id: string;
		configuration: any; // Could be improved with proper typing
		enabled: boolean;
	}

	interface FormUpdateData {
		name?: string;
		description?: string;
		userGuidelines?: string;
		workspaceGuidelines?: string;
		setupScript?: string;
		repositoryUrl?: string;
		branchSelection?: string;
		triggerType?: 'pull_request' | 'workflow_run' | 'issue';
		selectedProvider?: string;
	}

	interface Props {
		open: boolean;
		onClose: () => void;
		onSuccess?: (result: TriggerResult | { deleted: boolean }) => void;
		// Edit mode props
		editMode?: boolean;
		existingTrigger?: TriggerResult | null;
		// Initial configuration props
		initialProvider?: string;
		initialEntityType?: string;
		initialConditions?: Condition[];
	}

	let {
		open,
		onClose,
		onSuccess,
		editMode = false,
		existingTrigger = null,
		initialProvider,
		initialEntityType,
		initialConditions
	}: Props = $props();

	// UI state
	let showAdvanced = $state(false);
	let isSubmitting = $state(false);
	let error = $state<string | null>(null);
	let useSimpleUI = $state(true); // Toggle between simple and advanced UI
	let isIntroDismissed = $state(
		storedState('localStorage', 'isTriggerCreateIntroDismissed', false)
	);
	let showAIChat = $state(false); // AI chat visibility state
	let containerWidth = $state(0);

	// Set initial AI chat visibility based on screen width
	$effect(() => {
		if (containerWidth > 0) {
			showAIChat = containerWidth >= 1200;
		}
	});

	// Matching entities state
	let matchingEntities = $state<UnifiedEntity[]>([]);
	let entitiesLoading = $state(false);
	let entitiesError = $state<string | null>(null);
	let showMatchingEntities = $state(false);
	let entitiesCache = new Map<string, UnifiedEntity[]>();

	// Clean form state - only core fields
	let formData = $state({
		name: '',
		description: '',
		userGuidelines: '',
		workspaceGuidelines: '',
		setupScript: '',
		branchSelection: 'main', // 'main', '@{pr_head_ref}', '@{pr_base_ref}'
		repositoryUrl: '' // Repository URL for agent checkout (supports variables like @{pr_head_repo_url})
	});

	// Trigger enabled state
	let triggerEnabled = $state(false);

	// Provider and trigger selection
	let selectedProvider = $state<string>('github');
	let triggerType = $state<'pull_request' | 'workflow_run' | 'issue' | 'schedule'>('pull_request');

	// Single source of truth for all conditions
	let conditions = $state<Condition[]>([{ type: 'assigned_to_me' }]);

	// Activity types state
	let selectedActivityTypes = $state<string[]>([]);

	// Setup script state
	let selectedSetupScript = $state<any>(null);
	let setupScriptContent = $state('');
	let setupScriptType = $state<'basic' | 'auto' | 'manual'>('basic');

	// Repository selection state
	let selectedRepository = $state<any>(null);

	// Get current repository for GitHub triggers
	const projectGitHubRepo = $derived.by(() => {
		try {
			return getCurrentRepository();
		} catch {
			return '';
		}
	});

	// Reactive condition options for simple UI
	const conditionOptions = $derived.by(() => {
		return getConditionOptionsFromConfig(selectedProvider as any, triggerType as any);
	});

	// Check if activity types are available for current provider/entity
	const hasActivityTypeOption = $derived(
		conditionOptions.some((opt) => opt.value === 'activity_types')
	);

	// Helper function to handle activity type changes
	function handleActivityTypeChange(newSelectedTypes: string[]) {
		selectedActivityTypes = newSelectedTypes;

		// Update or add activity type condition
		const activityTypeIndex = conditions.findIndex((c) => c.type === 'activity_types');
		const newConditions = [...conditions];

		if (newSelectedTypes.length > 0) {
			const activityTypeCondition = {
				type: 'activity_types',
				value: newSelectedTypes.join(',')
			};

			if (activityTypeIndex >= 0) {
				newConditions[activityTypeIndex] = activityTypeCondition;
			} else {
				newConditions.push(activityTypeCondition);
			}
		} else {
			// Remove activity type condition if no types selected
			if (activityTypeIndex >= 0) {
				newConditions.splice(activityTypeIndex, 1);
			}
		}

		conditions = newConditions;
	}

	// Helper function to parse activity types from condition value
	function parseActivityTypes(value: string | undefined): string[] {
		if (!value) return [];
		return value
			.split(',')
			.map((type) => type.trim())
			.filter((type) => type.length > 0);
	}

	// Sync selectedActivityTypes with conditions
	$effect(() => {
		// Ensure conditions is an array before calling find
		if (!Array.isArray(conditions)) {
			selectedActivityTypes = [];
			return;
		}

		const activityCondition = conditions.find((c) => c.type === 'activity_types');
		if (activityCondition) {
			selectedActivityTypes = parseActivityTypes(activityCondition.value);
		} else {
			selectedActivityTypes = [];
		}
	});

	// Function to get default instructions based on provider and entity type
	function getDefaultInstructions(provider: string, entityType: string): string {
		return utilGetDefaultInstructions(provider, entityType);
	}

	// Effect to set default instructions when provider/entity type changes
	$effect(() => {
		if (selectedProvider && triggerType) {
			// Set default instructions if the field is empty and we're not in edit mode
			// Also check that we don't have an existing trigger (which means we're editing)
			if (
				!editMode &&
				!existingTrigger &&
				(!formData.userGuidelines || formData.userGuidelines.trim() === '')
			) {
				formData.userGuidelines = getDefaultInstructions(selectedProvider, triggerType);
			}
		}
	});

	// Effect to set setup script to auto when trigger is selected
	$effect(() => {
		if (selectedProvider && triggerType && !editMode && !existingTrigger) {
			// Only auto-set to 'auto' if we're not in edit mode and setup script is still 'basic'
			if (setupScriptType === 'basic') {
				setupScriptType = 'auto';
			}
		}
	});

	// Form update functions for IterativeChat
	function onFormUpdate(update: FormUpdateData) {
		if (update.name !== undefined) formData.name = update.name;
		if (update.description !== undefined) formData.description = update.description;
		if (update.userGuidelines !== undefined) formData.userGuidelines = update.userGuidelines;
		if (update.workspaceGuidelines !== undefined)
			formData.workspaceGuidelines = update.workspaceGuidelines;
		if (update.setupScript !== undefined) formData.setupScript = update.setupScript;
		if (update.branchSelection !== undefined) formData.branchSelection = update.branchSelection;
		if (update.repositoryUrl !== undefined) formData.repositoryUrl = update.repositoryUrl;

		// Handle condition updates by updating the conditions array directly
		// TODO: Implement condition updates from IterativeChat if needed

		// Handle trigger type changes
		if (update.triggerType !== undefined) {
			triggerType = update.triggerType;
		}

		// Trigger entity refresh when conditions change
		if (
			selectedProvider &&
			triggerType &&
			(selectedProvider === 'github' || selectedProvider === 'linear')
		) {
			entitiesLoading = true;
			entitiesError = null;
			debouncedFetchEntities();
		}
		// Schedule providers don't need entity fetching
	}

	function getCurrentFormState() {
		return {
			...formData,
			triggerType,
			selectedProvider
		};
	}

	function onRevertUpdate(
		previousState: FormUpdateData & { triggerType?: string; selectedProvider?: string }
	) {
		if (previousState) {
			Object.assign(formData, previousState);
			if (previousState.triggerType !== undefined) {
				triggerType = previousState.triggerType;
			}
			if (previousState.selectedProvider !== undefined) {
				selectedProvider = previousState.selectedProvider;
			}
		}
	}

	// Reactive store for checking if the selected provider is connected
	const isSelectedProviderConnected = $derived(
		(() => {
			// Schedule/cron doesn't require external provider authentication
			if (selectedProvider === 'cron' || selectedProvider === 'schedule') return true;

			// Check actual provider configuration status reactively
			return $providerAuthStates[selectedProvider]?.isConfigured || false;
		})()
	);

	// Derived values for button disabled state and tooltip
	const isButtonDisabled = $derived(
		isSubmitting ||
			!formData.name.trim() ||
			!formData.userGuidelines?.trim() ||
			(selectedProvider !== 'github' &&
				selectedProvider !== 'linear' &&
				selectedProvider !== 'schedule')
	);

	const disabledReason = $derived(() => {
		if (isSubmitting) {
			return editMode ? 'Updating action...' : 'Creating action...';
		}
		if (!formData.name.trim()) {
			return 'Action name is required';
		}
		if (!formData.userGuidelines?.trim()) {
			return 'Instructions are required';
		}
		if (
			selectedProvider !== 'github' &&
			selectedProvider !== 'linear' &&
			selectedProvider !== 'schedule'
		) {
			return 'Only GitHub, Linear, and Schedule actions are supported';
		}
		return '';
	});

	// Generate cache key for matching entities
	function generateCacheKey() {
		const conditions = getCurrentConditions();
		return JSON.stringify({
			provider: selectedProvider,
			triggerType,
			conditions
		});
	}

	// Get current conditions in backend format
	function getCurrentConditions() {
		return utilConditionsToBackendFormat(conditions, selectedProvider, triggerType);
	}

	// Fetch matching entities
	async function fetchEntities() {
		const cacheKey = generateCacheKey();

		// Check cache first
		if (entitiesCache.has(cacheKey)) {
			matchingEntities = entitiesCache.get(cacheKey)!;
			entitiesLoading = false;
			entitiesError = null;
			return;
		}

		entitiesLoading = true;
		entitiesError = null;

		try {
			const triggerConfig = utilCreateTriggerConfigForFetching(
				conditions,
				selectedProvider,
				triggerType
			);
			const entities = await apiClient.triggers.matchingEntitiesForConfig(
				triggerConfig.conditions,
				{ limit: 10 }
			);

			matchingEntities = entities;
			entitiesCache.set(cacheKey, entities);
		} catch (err) {
			entitiesError = err instanceof Error ? err.message : 'Failed to load entities';
			matchingEntities = [];
		} finally {
			entitiesLoading = false;
		}
	}

	// Debounced entity fetching
	const debouncedFetchEntities = debounce(fetchEntities, 500);

	let conditionsString = $derived(
		JSON.stringify({
			provider: selectedProvider,
			triggerType: triggerType,
			conditions: conditions
		})
	);

	// Effect to trigger entity fetching when conditions change
	$effect(() => {
		// Only fetch if we have a provider and trigger type selected
		if (
			selectedProvider &&
			triggerType &&
			(selectedProvider === 'github' || selectedProvider === 'linear') &&
			conditionsString
		) {
			// Set loading immediately
			entitiesLoading = true;
			entitiesError = null;

			// Trigger debounced fetch
			debouncedFetchEntities();
		}
	});

	// Setup script handlers
	function handleSetupScriptSelected(script: any | null) {
		selectedSetupScript = script;
		setupScriptContent = script?.content || '';
		console.log('Selected setup script:', script, 'Content:', setupScriptContent);
		// Update the setup script field with the content
		if (setupScriptContent) {
			formData.setupScript = setupScriptContent;
		}
	}

	function handleSetupTypeChanged(type: 'basic' | 'auto' | 'manual') {
		setupScriptType = type;
		// Clear script content when changing type
		if (type !== 'manual') {
			setupScriptContent = '';
			selectedSetupScript = null;
		}
	}

	function handleSetupScriptError({ message }: { message: string }) {
		console.error('Setup script error:', message);
	}

	// Create context for prompt enhancement
	let promptContext = $derived<PromptEnhancementContext>({
		contextType: 'trigger-creation',
		triggerName: formData.name,
		triggerDescription: formData.description,
		repositoryUrl: formData.repositoryUrl,
		branch: formData.branchSelection
	});

	// Prompt enhancer functions for userGuidelines
	function handleUserGuidelinesEnhanced(enhancedPrompt: string) {
		formData.userGuidelines = enhancedPrompt;
	}

	function handleUserGuidelinesEnhancementError(error: PromptEnhancementResult) {
		console.error('User guidelines enhancement error:', error);
	}

	// Prompt enhancer functions for workspaceGuidelines

	function handleWorkspaceGuidelinesEnhanced(enhancedPrompt: string) {
		formData.workspaceGuidelines = enhancedPrompt;
	}

	function handleWorkspaceGuidelinesEnhancementError(error: PromptEnhancementResult) {
		console.error('Workspace guidelines enhancement error:', error);
	}

	// Helper functions for GitHub setup script selector
	function getRepositoryUrlForSetupScript(): string {
		// Use the repository URL from form data first
		if (formData.repositoryUrl) {
			return formData.repositoryUrl;
		}
		// Fall back to project default if no repository selected
		if (projectGitHubRepo) {
			return `https://github.com/${projectGitHubRepo}`;
		}
		return '';
	}

	function getBranchForSetupScript(): string {
		// Use main branch for setup scripts (they should be stable)
		return 'main';
	}

	// Handle repository selection
	function handleRepositorySelection(repo: any) {
		selectedRepository = repo;

		if (repo) {
			// Set the repository URL for agent checkout
			formData.repositoryUrl = `https://github.com/${repo.owner}/${repo.name}`;
		} else {
			// Clear repository URL if no repo selected
			formData.repositoryUrl = '';
		}
	}

	function resetForm() {
		formData = {
			name: '',
			description: '',
			userGuidelines: '',
			workspaceGuidelines: '',
			setupScript: '',
			branchSelection: 'main',
			repositoryUrl: ''
		};
		conditions = [];
		selectedProvider = 'github';
		triggerType = 'pull_request';
		triggerEnabled = false;
		// Reset setup script state
		selectedSetupScript = null;
		setupScriptContent = '';
		setupScriptType = 'basic';
		// Reset repository selection
		selectedRepository = null;
		showAdvanced = false;
		isSubmitting = false;
		error = null;
	}

	function handleClose() {
		resetForm();
		onClose();
	}

	async function handleSubmit(e?: Event) {
		e?.preventDefault();

		// Prevent double submission
		if (isSubmitting) {
			return;
		}

		if (!formData.name.trim()) {
			error = 'Name is required';
			return;
		}

		// Use default instructions if none provided
		if (!formData.userGuidelines?.trim()) {
			formData.userGuidelines = getDefaultInstructions(selectedProvider, triggerType);
		}

		if (!isSelectedProviderConnected) {
			error = 'Please connect to the selected provider before creating the action';
			return;
		}

		isSubmitting = true;
		error = null;

		try {
			// Support GitHub, Linear, and Schedule triggers
			if (
				selectedProvider !== 'github' &&
				selectedProvider !== 'linear' &&
				selectedProvider !== 'schedule'
			) {
				error = `${PROVIDERS.find((p) => p.id === selectedProvider)?.name || selectedProvider} actions are not yet supported. Please select GitHub, Linear, or Schedule.`;
				return;
			}

			// Get session data
			const currentSession = $session;
			if (!currentSession) {
				error = 'No authenticated session found. Please log in first.';
				return;
			}

			// Create the trigger configuration using the utility function
			const triggerFormData: TriggerFormData = {
				name: formData.name,
				description: formData.description,
				userGuidelines: formData.userGuidelines,
				workspaceGuidelines: formData.workspaceGuidelines,
				setupScript: formData.setupScript,
				repositoryUrl: formData.repositoryUrl,
				branchSelection: formData.branchSelection
			};

			const configuration = createTriggerConfiguration(
				conditions,
				selectedProvider,
				triggerType,
				triggerFormData,
				triggerEnabled,
				projectGitHubRepo
			);
			console.log('Created trigger configuration:', { configuration, conditions });
			console.log('Configuration conditions:', JSON.stringify(configuration.conditions, null, 2));

			// Validate the configuration before submitting
			// const validationResult = validateTriggerConfiguration(configuration);
			// if (!validationResult.isValid) {
			// 	error = `Validation failed:\n${formatValidationErrors(validationResult.errors)}`;
			// 	return;
			// }

			let result: any;
			if (editMode && existingTrigger) {
				// Update existing trigger
				result = await updateTriggerAndSync(existingTrigger.id, configuration);
			} else {
				// Create new trigger
				result = await createTriggerAndSync(configuration);
			}

			onSuccess?.(result);
			handleClose();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create trigger';
		} finally {
			isSubmitting = false;
		}
	}

	async function handleDelete() {
		if (!editMode || !existingTrigger?.id) {
			return;
		}

		// Confirm deletion
		if (!confirm('Are you sure you want to delete this trigger? This action cannot be undone.')) {
			return;
		}

		isSubmitting = true;
		error = null;

		try {
			await deleteTriggerAndSync(existingTrigger.id);
			onSuccess?.({ deleted: true });
			handleClose();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to delete trigger';
		} finally {
			isSubmitting = false;
		}
	}

	// Reset form when drawer opens or populate with existing data
	let previousOpen = $state(false);
	$effect(() => {
		console.log('Drawer open state changed:', open, 'Previous:', previousOpen);
		// Only run when drawer transitions from closed to open
		if (open && !previousOpen) {
			console.log('Drawer opened, resetting form...', {
				editMode,
				existingTrigger,
				initialProvider,
				initialEntityType,
				initialConditions
			});
			if (editMode && existingTrigger) {
				populateFormFromTrigger(existingTrigger);
			} else {
				resetForm();
				// Apply initial configuration if provided
				if (initialProvider || initialEntityType || initialConditions) {
					populateFormFromInitialData();
				}
			}
		}
		previousOpen = open;
	});

	onMount(() => {
		if (editMode && existingTrigger) {
			populateFormFromTrigger(existingTrigger);
		} else if (initialProvider || initialEntityType || initialConditions) {
			populateFormFromInitialData();
		}
	});

	// Populate form from initial data (from dashboard filters)
	function populateFormFromInitialData() {
		if (initialProvider) {
			selectedProvider = initialProvider;
		}

		if (initialEntityType) {
			triggerType = initialEntityType as 'pull_request' | 'workflow_run' | 'issue';
		}

		// Conditions are now handled by the unified conditions array
		// No need to populate individual formData properties
	}

	// Populate form from existing trigger data
	function populateFormFromTrigger(trigger: TriggerResult) {
		const config = trigger.configuration;
		if (!config) return;

		// Store the user guidelines before changing provider/triggerType to prevent overwriting
		const existingUserGuidelines = config.agentConfig?.userGuidelines || '';

		// Determine trigger type and populate conditions using unified approach first
		if (config.conditions?.github) {
			selectedProvider = 'github';
			const github = config.conditions.github;

			if (github.pull_request) {
				triggerType = 'pull_request';
			} else if (github.workflow_run) {
				triggerType = 'workflow_run';
			}
		} else if (config.conditions?.linear) {
			selectedProvider = 'linear';
			triggerType = 'issue';
		} else if (config.conditions?.schedule) {
			selectedProvider = 'schedule';
			triggerType = 'schedule';
		}

		// Now populate form data after provider/triggerType are set
		formData.name = config.name || '';
		formData.description = config.description || '';
		formData.userGuidelines = existingUserGuidelines;
		formData.workspaceGuidelines = config.agentConfig?.workspaceGuidelines || '';
		formData.setupScript = config.agentConfig?.workspaceSetup?.setupScript || '';
		formData.branchSelection =
			config.agentConfig?.workspaceSetup?.startingFiles?.githubRef?.ref || 'main';
		formData.repositoryUrl =
			config.agentConfig?.workspaceSetup?.startingFiles?.githubRef?.url || '';

		// Convert backend conditions to unified conditions format
		conditions = utilTriggerConfigToConditions(config, selectedProvider, triggerType);

		// Initialize selectedRepository from repositoryUrl
		if (formData.repositoryUrl) {
			// Extract owner/name from GitHub URL
			const match = formData.repositoryUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
			if (match) {
				const [, owner, name] = match;
				selectedRepository = { owner, name };
			}
		}
		console.log('Populated form from trigger:', { config, conditions, formData });

		// Set enabled state
		triggerEnabled = config.enabled !== false; // Default to true if not specified
	}

	function dismissIntro() {
		isIntroDismissed.set(true);
	}

	function showIntro() {
		isIntroDismissed.set(false);
	}
</script>

<Drawer
	{open}
	onClose={handleClose}
	title={editMode ? 'Edit Action' : 'Create Action'}
	subtitle={editMode
		? 'Update your action configuration'
		: 'Automate your workflow with intelligent actions'}
	size="xl"
	contentHeight="static"
	class="[--panel-padding-x:1rem] lg:[--panel-padding-x:2.5rem]"
>
	{#snippet headerActions()}
		<!-- AI Chat Toggle -->
		<Button
			variant={showAIChat ? 'primary' : 'secondary'}
			size="sm"
			onclick={() => (showAIChat = !showAIChat)}
			icon={LightBulb}
		>
			AI Help
		</Button>
	{/snippet}

	{#snippet children()}
		<div class="grid h-full min-h-0 grid-cols-[1fr_auto]" bind:clientWidth={containerWidth}>
			<!-- Main Form Area -->
			<div class="flex min-w-0 flex-1 flex-col overflow-hidden">
				<!-- UI Toggle -->
				<!-- <div class="flex-none border-b border-gray-200 dark:border-gray-700 p-4">
				<div class="flex items-center justify-between">
					<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
						{editMode ? "Edit Action" : "Create Action"}
					</h2>
				</div>
			</div> -->

				<form class="min-h-0 flex-1 space-y-6 overflow-y-auto px-[var(--panel-padding-x)] py-6">
					<!-- Dismissible Intro Section -->
					{#if !editMode && !isIntroDismissed.get()}
						<div class="relative flex gap-1" transition:slide={{ axis: 'y' }}>
							<!-- Dismiss Button -->
							<Button
								variant="ghost"
								size="icon-sm"
								onclick={dismissIntro}
								class="absolute top-3 right-3 p-1 text-slate-400 transition-colors hover:text-slate-600 dark:text-slate-500 dark:hover:text-slate-300"
								aria-label="Dismiss intro"
							>
								<Icon src={XMark} class="h-4 w-4" />
							</Button>
							<Icon src={InformationCircle} class="h-5 w-5 text-slate-400" micro />

							<div class="pr-8">
								<p class="text-sm leading-relaxed text-slate-500 dark:text-slate-500">
									Actions are smart filters that automatically pull in tasks from GitHub, Linear,
									and other tools based on criteria you define, or run on a schedule. Once created,
									your Action will start monitoring for matches and can auto-delegate them to AI
									agents to work on.
								</p>
							</div>
						</div>
					{:else if !editMode && isIntroDismissed.get()}
						<!-- "What are Actions?" button when intro is dismissed -->
						<div class="flex justify-center" transition:slide={{ axis: 'y' }}>
							<Button
								variant="ghost"
								onclick={showIntro}
								class="group flex items-center gap-2 rounded-lg px-3 pb-2 text-xs text-slate-500 transition-all duration-200 hover:bg-slate-50 hover:text-slate-700 dark:text-slate-400 dark:hover:bg-slate-800 dark:hover:text-slate-300"
								icon={Bolt}
							>
								<span class="">What are Actions?</span>
							</Button>
						</div>
					{/if}

					<!-- Form Fields -->
					<div class="grid grid-cols-3 gap-4">
						<FormField
							labelText="Action Name"
							bind:value={formData.name}
							placeholder="e.g., My Pull Requests"
							required
						/>

						<FormField
							labelText="Description"
							bind:value={formData.description}
							placeholder="Brief description..."
						/>

						<!-- Repository Selection -->
						<div class="space-y-1">
							<span class="block text-sm font-medium text-slate-900 dark:text-slate-100">
								Repository
							</span>
							<GitHubRepoCombobox
								value={selectedRepository}
								placeholder="Select a repository..."
								onValueChange={handleRepositorySelection}
								class="w-full"
							/>
							<p class="text-xs text-slate-500 dark:text-slate-400">
								Choose which repository the agent should work on
							</p>
						</div>
					</div>

					<div
						class="relative rounded-lg border border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800"
					>
						<!-- Header with UI Toggle -->
						<div
							class="flex w-full items-center justify-between border-b border-gray-200 px-5 py-2 dark:border-gray-700"
						>
							<span class="text-sm font-medium text-slate-700 dark:text-slate-300">
								Action configuration
							</span>
							<ButtonGroup>
								<Button
									variant={useSimpleUI ? 'primary' : 'secondary'}
									size="sm"
									onclick={() => (useSimpleUI = true)}
								>
									Simple
								</Button>
								<Button
									variant={!useSimpleUI ? 'primary' : 'secondary'}
									size="sm"
									onclick={() => (useSimpleUI = false)}
								>
									Complex
								</Button>
							</ButtonGroup>
						</div>

						{#if useSimpleUI}
							<SimpleConditionsBuilder
								bind:providerId={selectedProvider}
								bind:entityType={triggerType}
								bind:conditions
								{conditionOptions}
							/>
						{:else}
							<!-- Advanced form -->
							<AdvancedConditionsBuilder
								bind:selectedProvider
								bind:triggerType
								bind:conditions
								providers={PROVIDERS}
								{projectGitHubRepo}
							/>
						{/if}

						<!-- Provider Authentication -->
						{#if !isSelectedProviderConnected}
							<div
								class="mx-6 mb-6 rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20"
							>
								<div class="flex items-start gap-3">
									<Icon
										src={ExclamationTriangle}
										class="mt-0.5 h-5 w-5 text-yellow-600 dark:text-yellow-400"
									/>
									<div class="flex-1">
										<h3 class="mb-1 text-sm font-medium text-yellow-800 dark:text-yellow-200">
											Provider Authentication Required
										</h3>
										<p class="mb-3 text-sm text-yellow-700 dark:text-yellow-300">
											You need to connect to {PROVIDERS.find((p) => p.id === selectedProvider)
												?.name || selectedProvider} before creating actions.
										</p>
										<ProviderAuthButton
											providerId={selectedProvider}
											variant="compact"
											onAuthSuccess={() => {
												// Refresh the component state after successful auth
												checkProviderAuth(selectedProvider);
											}}
										/>
									</div>
								</div>
							</div>
						{/if}
						<!-- Enhanced Matching Entities Preview -->
						{#if selectedProvider && triggerType && (selectedProvider === 'github' || selectedProvider === 'linear')}
							<div class="mt-3 border-t border-slate-200 dark:border-slate-700">
								<button
									type="button"
									onclick={() => (showMatchingEntities = !showMatchingEntities)}
									class="w-full cursor-pointer px-5 py-4 text-left {matchingEntities.length > 0
										? 'hover:bg-white/80 dark:hover:bg-slate-700/80'
										: ''} transition-all duration-200"
								>
									<div class="flex items-center justify-between">
										<div class="flex flex-1 items-center gap-3">
											<div class="flex items-center gap-2">
												<!-- <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full animate-pulse"></div> -->
												<span class="text-sm font-medium text-slate-700 dark:text-slate-300">
													Preview matching items
												</span>
											</div>
											{#if entitiesLoading}
												<!-- <div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div> -->
											{:else if matchingEntities.length > 0}
												<span
													class="rounded-full bg-emerald-100 px-2 py-1 text-xs font-medium text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300"
												>
													{matchingEntities.length === 10
														? `${matchingEntities.length}+`
														: matchingEntities.length} found
												</span>
											{:else if entitiesError}
												<span
													class="rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-700 dark:bg-red-900/50 dark:text-red-300"
												>
													Error
												</span>
											{:else}
												<span
													class="rounded-full bg-slate-200 px-2 py-1 text-xs text-slate-600 dark:bg-slate-600 dark:text-slate-400"
												>
													No matches
												</span>
											{/if}
										</div>

										<!-- Collapsed Preview -->
										<div class="mr-2 flex items-center gap-2">
											{#if entitiesLoading}
												<div
													class="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"
												></div>
											{/if}
											<!-- Mini entity previews -->
											<div class="ml-auto flex -space-x-1">
												{#each matchingEntities.slice(0, 3) as entity, i}
													<div
														class="flex h-6 w-6 items-center justify-center rounded-full border-2 border-white bg-gradient-to-br from-blue-400 to-purple-500 text-xs font-bold text-white shadow-sm dark:border-slate-700"
														style="z-index: {3 - i}"
													>
														{entity.title.charAt(0).toUpperCase()}
													</div>
												{/each}
												{#if matchingEntities.length > 3}
													<div
														class="flex h-6 w-6 items-center justify-center rounded-full border-2 border-white bg-slate-300 text-xs font-medium text-slate-600 dark:border-slate-700 dark:bg-slate-600 dark:text-slate-400"
													>
														+{matchingEntities.length - 3}
													</div>
												{/if}
											</div>
										</div>

										{#if matchingEntities.length > 0 || entitiesLoading || entitiesError}
											<Icon
												src={ChevronRight}
												class="h-4 w-4 text-slate-400 transition-transform duration-200 {showMatchingEntities
													? 'rotate-90'
													: ''}"
											/>
										{/if}
									</div>
								</button>

								{#if showMatchingEntities && (matchingEntities.length > 0 || entitiesLoading || entitiesError)}
									<div
										class="border-t border-slate-200/50 bg-white/40 dark:border-slate-600/50 dark:bg-slate-800/40"
										transition:slide
									>
										{#if entitiesLoading}
											<!-- Enhanced skeleton loaders -->
											<div class="space-y-3 p-4">
												{#each Array(3) as _, i}
													<div
														class="flex animate-pulse items-center gap-3 rounded-lg bg-white/60 p-3 dark:bg-slate-700/60"
													>
														<div
															class="h-8 w-8 rounded-full bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-600 dark:to-slate-700"
														></div>
														<div class="flex-1 space-y-2">
															<div
																class="h-4 rounded bg-slate-200 dark:bg-slate-600 {i === 0
																	? 'w-3/4'
																	: i === 1
																		? 'w-5/6'
																		: 'w-2/3'}"
															></div>
															<div class="h-3 w-1/2 rounded bg-slate-100 dark:bg-slate-700"></div>
														</div>
													</div>
												{/each}
											</div>
										{:else if entitiesError}
											<div class="p-6 text-center">
												<div
													class="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/30"
												>
													<Icon
														src={ExclamationTriangle}
														class="h-6 w-6 text-red-600 dark:text-red-400"
													/>
												</div>
												<p class="text-sm font-medium text-red-600 dark:text-red-400">
													{entitiesError}
												</p>
											</div>
										{:else if matchingEntities.length > 0}
											<div class="grid grid-cols-2">
												{#each matchingEntities.slice(0, 5) as entity}
													<div
														class="m-[-0.5px] border border-b-0 border-slate-200 dark:border-slate-700"
													>
														<MatchRow {entity} onclick={() => window.open(entity.url, '_blank')} />
													</div>
												{/each}
												{#if matchingEntities.length > 5}
													<div class="flex h-full items-center justify-center pt-2 text-center">
														<span class="text-sm text-slate-500 dark:text-slate-400">
															+{matchingEntities.length - 5} more entities
														</span>
													</div>
												{/if}
											</div>
										{:else}
											<div class="p-6 text-center">
												<div
													class="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-slate-100 dark:bg-slate-700"
												>
													<Icon src={MagnifyingGlass} class="h-6 w-6 text-slate-400" />
												</div>
												<p class="text-sm text-slate-500 dark:text-slate-400">
													No entities match your current conditions
												</p>
												<p class="mt-1 text-xs text-slate-400 dark:text-slate-500">
													Try adjusting your filters above
												</p>
											</div>
										{/if}
									</div>
								{/if}
							</div>
						{/if}
					</div>

					<!-- Instructions -->
					<div class="space-y-4">
						<!-- <h3 class="text-lg font-medium text-gray-900 dark:text-white">Agent Instructions</h3> -->

						<div class="relative">
							<FormField
								type="textarea"
								labelText={`What instructions should the agent follow ${selectedProvider === 'schedule' ? 'on the schedule' : ["when there's a new", getProviderDisplayName(selectedProvider), getEntityTypeDisplayName(triggerType, selectedProvider)].join(' ')}?`}
								bind:value={formData.userGuidelines}
								placeholder={selectedProvider && triggerType
									? getDefaultInstructions(selectedProvider, triggerType)
									: 'When this trigger fires, the agent should analyze the code changes, review for potential issues, and provide feedback on...'}
								rows={6}
								autoResize
								required
								hint="Be specific about what actions the agent should take and what kind of analysis or feedback you want. Default instructions will be provided if left empty."
								inputClass="pr-10"
							/>

							<!-- Prompt Enhancer -->
							<div class="absolute top-8 right-2">
								<PromptEnhancer
									prompt={formData.userGuidelines || ''}
									onEnhanced={handleUserGuidelinesEnhanced}
									onError={handleUserGuidelinesEnhancementError}
									context={promptContext}
								/>
							</div>
						</div>
					</div>

					<!-- Advanced Settings -->
					<div class="rounded-lg border border-gray-200 dark:border-gray-700">
						<button
							type="button"
							onclick={() => (showAdvanced = !showAdvanced)}
							class="flex w-full cursor-pointer items-center justify-between {showAdvanced
								? 'rounded-t-lg'
								: 'rounded-lg'} p-4 text-left transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50"
						>
							<span class="text-sm font-medium text-gray-700 dark:text-gray-300">
								Advanced Settings
							</span>
							<Icon
								src={ChevronRight}
								class="h-4 w-4 text-gray-400 transition-transform duration-200 {showAdvanced
									? 'rotate-90'
									: ''}"
							/>
						</button>

						{#if showAdvanced}
							<div
								class="space-y-4 border-t border-gray-200 px-6 pt-6 pb-8 dark:border-gray-700"
								transition:slide
							>
								<div class="flex gap-6 max-lg:flex-col">
									<div class="lg:flex-1">
										<div class="relative">
											<FormField
												type="textarea"
												labelText="Additional Workspace Context"
												bind:value={formData.workspaceGuidelines}
												placeholder="Provide additional context about your codebase, coding standards, or specific requirements..."
												rows={3}
												hint="Optional: Help the agent understand your project's conventions and requirements"
												inputClass="pr-10"
											/>

											<!-- Prompt Enhancer -->
											<div class="absolute top-8 right-2">
												<PromptEnhancer
													prompt={formData.workspaceGuidelines || ''}
													onEnhanced={handleWorkspaceGuidelinesEnhanced}
													onError={handleWorkspaceGuidelinesEnhancementError}
													context={promptContext}
												/>
											</div>
										</div>
									</div>

									<div class="flex flex-1 flex-col gap-4">
										<fieldset class="!mb-2 w-full space-y-1 lg:flex-1">
											<SetupScriptPicker
												bind:selectedSetupType={setupScriptType}
												onSetupTypeChanged={handleSetupTypeChanged}
												onScriptSelected={handleSetupScriptSelected}
												repositoryUrl={getRepositoryUrlForSetupScript()}
												branch={getBranchForSetupScript()}
												disableAutoGeneration={!getRepositoryUrlForSetupScript().trim()}
											/>
										</fieldset>

										{#if selectedProvider === 'github' && triggerType === 'pull_request'}
											<FormField
												type="select"
												labelText="Branch Selection"
												bind:value={formData.branchSelection}
												hint="Choose which branch the agent should work on"
												options={[
													{ value: 'main', label: 'Main branch (default)' },
													{
														value: '@{pr_head_ref}',
														label: 'PR head branch (the branch being merged)'
													},
													{ value: '@{pr_base_ref}', label: 'PR base branch (the target branch)' }
												]}
											/>

											<FormField
												type="select"
												labelText="Repository URL"
												bind:value={formData.repositoryUrl}
												hint="Repository URL for agent checkout (useful for forks)"
												options={[
													{ value: '', label: 'Use condition repository (default)' },
													{
														value: '@{pr_head_repo_url}',
														label: 'PR head repository (source fork)'
													},
													{
														value: '@{pr_base_repo_url}',
														label: 'PR base repository (target repo)'
													}
												]}
											/>
										{/if}
									</div>
								</div>
							</div>
						{/if}
					</div>

					<!-- Auto-mode Toggle -->
					<div
						class="rounded-lg border border-slate-200 bg-slate-50 p-4 dark:border-slate-700 dark:bg-slate-800"
					>
						<div class="flex items-center justify-between">
							<div class="flex-1">
								<h3 class="text-sm font-medium text-slate-900 dark:text-white">Auto-delegate</h3>
								<p class="mt-1 text-xs text-slate-500 dark:text-slate-400">
									Automatically assign new matches to agents when they appear
								</p>
							</div>
							<TriggerAutoToggle
								enabled={triggerEnabled}
								onToggle={() => (triggerEnabled = !triggerEnabled)}
								size="md"
								variant="default"
								showLabel={false}
								showIcon={false}
							/>
						</div>

						<!-- Activity Type Selector -->
						{#if hasActivityTypeOption && triggerEnabled}
							<div class="mt-4 border-t border-slate-200 pt-4 dark:border-slate-700">
								<div class="flex flex-wrap items-start gap-4">
									<div class="min-w-32 flex-1">
										<div class="mb-1 text-sm font-medium text-slate-700 dark:text-slate-300">
											Event types
										</div>
										<p class="text-xs text-slate-500 dark:text-slate-400">
											Auto-delegate when these events occur
										</p>
									</div>
									<div class="flex-shrink-0">
										<ActivityTypeSelector
											bind:selectedTypes={selectedActivityTypes}
											provider={selectedProvider as 'github' | 'linear'}
											entityType={triggerType}
											onchange={handleActivityTypeChange}
											class="text-xs"
										/>
									</div>
								</div>
							</div>
						{/if}
					</div>

					<!-- Error Display -->
					{#if error}
						<div
							class="flex items-center gap-2 rounded-md border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20"
						>
							<Icon src={ExclamationTriangle} class="h-5 w-5 text-red-500" />
							<span class="text-sm text-red-700 dark:text-red-400">{error}</span>
						</div>
					{/if}
				</form>

				<!-- Form Actions -->
				<div class="flex-none border-t border-gray-200 p-6 dark:border-gray-700">
					<div class="flex items-center justify-between">
						<!-- Delete button (only in edit mode) -->
						<div>
							{#if editMode && existingTrigger?.id}
								<Button
									variant="outline"
									onclick={handleDelete}
									disabled={isSubmitting}
									icon={Trash}
									class="text-red-600 hover:border-red-300 hover:text-red-700 dark:text-red-400 dark:hover:border-red-500 dark:hover:text-red-300"
								>
									Delete Action
								</Button>
							{/if}
						</div>

						<!-- Main action buttons -->
						<div class="flex items-center gap-3">
							<Button variant="outline" onclick={handleClose} disabled={isSubmitting}>
								Cancel
							</Button>
							<Tooltip
								text={disabledReason()}
								position="top"
								disabled={!isButtonDisabled}
								delay={300}
								maxWidth="250px"
							>
								{#snippet children()}
									<Button
										onclick={(e) => {
											e.preventDefault();
											handleSubmit(e);
										}}
										variant="primary"
										disabled={isButtonDisabled}
										type="submit"
									>
										{isSubmitting
											? editMode
												? 'Updating...'
												: 'Creating...'
											: editMode
												? 'Update Trigger'
												: 'Create Trigger'}
									</Button>
								{/snippet}
							</Tooltip>
						</div>
					</div>
				</div>
			</div>

			<!-- AI Chat Assistant -->
			{#if showAIChat}
				<div
					class="h-full min-h-0 flex-none overflow-hidden border-l border-gray-200 dark:border-gray-700"
					transition:slide={{ axis: 'x' }}
				>
					<div class="w-[400px]">
						<IterativeChat
							systemPrompt={`You are an AI assistant helping users create triggers for automation across GitHub, Linear, and scheduled tasks. You can ask questions about their requirements and suggest form updates. When suggesting updates, provide explanatory text and then list the configuration at the end in a JSON code block with the key 'formUpdate' containing the partial form data to apply. The JSON will be automatically parsed and displayed in a structured card - do not render or describe the JSON structure in your response.

Available Trigger Types:
GitHub:
- Pull Request: Monitor pull request events (opened, synchronize, ready_for_review, closed)
- Workflow Run: Monitor GitHub Actions workflow runs (completed, failed, etc.)

Linear:
- Linear Issue: Monitor Linear issue events (created, updated, state changes, etc.)

Schedule:
- Scheduled Trigger: Run on a time-based schedule using cron expressions

Available form fields for Pull Request triggers:
- name: Trigger name
- description: Brief description
- userGuidelines: Instructions for what the agent should do
- workspaceGuidelines: Additional workspace context
- setupScript: Shell commands to run before agent starts working
- branchSelection: Branch for agent to work on (main, @{pr_head_ref}, @{pr_base_ref})
- repositoryUrl: Repository URL for agent checkout (@{pr_head_repo_url}, @{pr_base_repo_url}, or custom URL)
- repository: GitHub repository (owner/repo format)
- author: PR author filter (@me for current user)
- assignee: PR assignee filter
- reviewer: PR reviewer filter
- baseBranch: Target branch filter
- headBranch: Source branch filter
- activityTypes: Array of activity types (opened, synchronize, ready_for_review, closed)

Available form fields for Workflow Run triggers:
- name: Trigger name
- description: Brief description
- userGuidelines: Instructions for what the agent should do
- workspaceGuidelines: Additional workspace context
- setupScript: Shell commands to run before agent starts working
- repository: GitHub repository (owner/repo format)
- actor: Workflow actor filter (@me for current user)
- event: Trigger event filter (push, pull_request, etc.)
- status: Workflow status filter (completed, in_progress, etc.)
- conclusion: Workflow conclusion filter (success, failure, cancelled, etc.)
- branch: Branch filter

Available form fields for Linear Issue triggers:
- name: Trigger name
- description: Brief description
- userGuidelines: Instructions for what the agent should do
- workspaceGuidelines: Additional workspace context
- setupScript: Shell commands to run before agent starts working
- team: Linear team key or name (e.g., "AUG")
- creator: Issue creator filter (@me for current user)
- assignee: Issue assignee filter (@me for current user)
- project: Linear project name or ID
- titleContains: Filter issues by title content
- minEstimate: Minimum story points/estimate
- maxEstimate: Maximum story points/estimate
- states: Comma-separated list of workflow state names (e.g., "In Progress, Review")
- stateTypes: Comma-separated list of state types (backlog, unstarted, started, completed, canceled)
- labels: Comma-separated list of required labels (e.g., "bug, urgent, frontend")
- priorities: Comma-separated list of priorities (0=None, 1=Urgent, 2=High, 3=Medium, 4=Low)
- linearActivityTypes: Comma-separated list of activity types (created, updated, state_changed, etc.)

Available form fields for Schedule triggers:
- name: Trigger name
- description: Brief description
- userGuidelines: Instructions for what the agent should do
- workspaceGuidelines: Additional workspace context
- setupScript: Shell commands to run before agent starts working
- cronExpression: Cron expression for schedule (e.g., "0 9 * * *" for daily at 9 AM)
- timezone: Timezone for schedule (e.g., "America/New_York", defaults to UTC)
- startDate: When to start executing (ISO format, optional)
- endDate: When to stop executing (ISO format, optional)

You can also change the trigger type by setting triggerType to 'pull_request', 'workflow_run', 'issue', or 'schedule'.

When users ask about setting up specific scenarios, provide the complete configuration. For example, if they want to monitor failed builds:

\`\`\`json
{
  "formUpdate": {
    "name": "Failed Build Monitor",
    "description": "Monitor and analyze failed GitHub Actions workflows",
    "triggerType": "workflow_run",
    "userGuidelines": "When a workflow fails, analyze the failure logs, identify the root cause, and suggest specific fixes. Look for common issues like test failures, build errors, or deployment problems.",
    "status": "completed",
    "conclusion": "failure"
  }
}
\`\`\`

For Linear issue monitoring:

\`\`\`json
{
  "formUpdate": {
    "name": "Urgent Bug Tracker",
    "description": "Monitor urgent bugs in Linear and provide immediate analysis",
    "triggerType": "linear_issue",
    "userGuidelines": "When an urgent bug is created or updated, analyze the issue description, suggest potential root causes, and recommend investigation steps. Prioritize issues that affect user experience.",
    "labels": "bug, urgent",
    "priorities": "1, 2",
    "linearActivityTypes": "created, updated"
  }
}
\`\`\`

Available templates: Coming soon...`}
							{onFormUpdate}
							{getCurrentFormState}
							{onRevertUpdate}
							templateSuggestions={[
								// we have access to github prs and workflows and linear issues
								'Review PRs I create',
								'Review PRs assigned to me',
								'Review PRs with an Augment label',
								'Fix failed builds',
								'Handle my Linear issues',
								'Handle Linear bugs assigned to me',
								// more complex conditions
								'Handle high-priority Linear issues',
								'Fix failed builds in the main branch'
							]}
							generalSuggestions={[
								'How do GitHub trigger conditions work?',
								"What's the difference between pull request and workflow triggers?",
								'How can I filter by specific branches or users?',
								'What are some common trigger patterns?',
								'How do I set up automated code reviews?',
								'Can I monitor multiple repositories?',
								'How do Linear triggers work?',
								'What Linear issue filters are available?',
								'How can I monitor specific Linear teams or projects?'
							]}
							placeholder="Help me set up a trigger for..."
						/>
					</div>
				</div>
			{/if}
		</div>
	{/snippet}
</Drawer>
