<script lang="ts">
	import Modal from './Modal.svelte';
	import Button from '../navigation/Button.svelte';
	import {
		Icon,
		Calendar,
		CodeBracket,
		Trash,
		HandThumbDown,
		HandThumbUp,
		ArrowTopRightOnSquare
	} from 'svelte-hero-icons';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { apiClient } from '$lib/api/unified-client';
	import {
		extractEntityDetailsFromAgent,
		getEntityByAgent,
		getTriggerForAgent,
		deleteAgent as deleteAgentFromStore
	} from '$lib/stores/global-state.svelte';
	import { dismissEntityAndSync, loadMatchingEntities } from '$lib/stores/data-operations.svelte';
	import { addToast } from '$lib/stores/toast';
	import { get } from 'svelte/store';
	import RemoteAgentTitle from '../RemoteAgentTitle.svelte';
	import { getEntityTypeDisplayName } from '$lib/config/entity-types';
	import { getProviderDisplayName } from '$lib/utils/entity-types';
	import RemoteAgentHeader from '../layout/RemoteAgentHeader.svelte';
	import EntityCardSmall from '../data-display/EntityCardSmall.svelte';
	import EntityCard from '../data-display/EntityCard.svelte';

	interface Props {
		isOpen: boolean;
		agent: CleanRemoteAgent | null;
		onClose: () => void;
		onAgentDeleted?: () => void;
	}

	let { isOpen = false, agent, onClose, onAgentDeleted }: Props = $props();

	let isDeleting = $state(false);
	let isReviving = $state(false);
	let deleteButtonRef = $state<HTMLButtonElement>();
	let deleteAndReviveButtonRef = $state<HTMLButtonElement>();

	// Extract entity details from agent
	let entityDetails = $derived(agent ? extractEntityDetailsFromAgent(agent) : null);
	let entityInfo = $derived(agent ? getEntityByAgent(agent.id) : null);
	let linkedEntity = $derived(entityInfo ? get(entityInfo).data : null);

	// Get trigger information
	let triggerStore = $derived(agent ? getTriggerForAgent(agent.id) : null);
	let trigger = $derived(triggerStore ? get(triggerStore).data : null);

	// Auto-focus the appropriate button when modal opens
	$effect(() => {
		if (isOpen && !isDeleting && !isReviving) {
			// Focus the appropriate button based on whether there's a linked entity
			setTimeout(() => {
				if (deleteButtonRef) {
					deleteButtonRef.focus();
				}
			}, 100); // Small delay to ensure modal is fully rendered
		}
	});

	// Handle keyboard events
	function handleKeydown(event: KeyboardEvent) {
		if (!isOpen) return;

		if (event.key === 'Enter' && !isDeleting && !isReviving) {
			event.preventDefault();
			// If there's a linked entity, prefer "Delete and don't undismiss" (which is just delete)
			// Otherwise, use regular delete
			if (linkedEntity) {
				handleDeleteAgent();
			} else {
				handleDeleteAgent();
			}
		}
	}

	// Format dates
	function formatDate(date: Date): string {
		return new Intl.DateTimeFormat('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		}).format(date);
	}

	// Extract repository name from URL
	function getRepoName(url?: string): string {
		if (!url) return 'Unknown repository';
		const match = url.match(/github\.com\/([^/]+\/[^/]+)/);
		return match ? match[1] : 'Unknown repository';
	}

	// Truncate text
	function truncateText(text: string, maxLength: number): string {
		if (text.length <= maxLength) return text;
		return text.substring(0, maxLength) + '...';
	}

	// Handle cancel
	function handleCancel() {
		onClose();
	}

	// Handle delete agent only
	async function handleDeleteAgent() {
		if (!agent || isDeleting) return;

		isDeleting = true;
		try {
			// Delete the agent via API
			await apiClient.agents.delete(agent.id);

			// Update local store
			deleteAgentFromStore(agent.id);

			onAgentDeleted?.();
			onClose();
		} catch (error) {
			console.error('Failed to delete agent:', error);
			addToast({
				type: 'error',
				message: error instanceof Error ? error.message : 'Failed to delete agent',
				duration: 5000
			});
		} finally {
			isDeleting = false;
		}
	}

	// Handle delete agent and revive entity
	async function handleDeleteAndRevive() {
		if (!agent || !entityDetails || !trigger || isDeleting || isReviving) return;

		// Capture trigger ID early to avoid race conditions
		const triggerId = trigger.id;
		const entityId = entityDetails.entityId;
		const entityType = entityDetails.entityType;

		if (!triggerId || !entityId) {
			console.error('Missing trigger ID or entity ID for revive operation');
			addToast({
				type: 'error',
				message: 'Cannot restore entity: missing required information',
				duration: 5000
			});
			return;
		}

		isDeleting = true;
		isReviving = true;

		try {
			// First delete the agent via API
			await apiClient.agents.delete(agent.id);

			// Update local store
			deleteAgentFromStore(agent.id);

			// Then revive the entity (undismiss it)
			await dismissEntityAndSync(triggerId, entityId, false);

			addToast({
				type: 'success',
				message: `Agent deleted and ${getEntityTypeDisplayName(entityType)} restored to feed`,
				duration: 5000
			});

			// reload matching entities for the trigger
			loadMatchingEntities(triggerId);

			onAgentDeleted?.();
			onClose();
		} catch (error) {
			console.error('Failed to delete agent and revive entity:', error);
			addToast({
				type: 'error',
				message: 'Failed to restore entity to feed',
				duration: 5000
			});
		} finally {
			isDeleting = false;
			isReviving = false;
		}
	}
</script>

<Modal {isOpen} title="Are you sure you want to delete this agent?" size="md" {onClose}>
	{#if agent}
		<div class="p-6" role="dialog" tabindex="-1" onkeydown={handleKeydown}>
			<!-- Agent Information -->
			<div class="mb-6 rounded-lg bg-slate-50 p-4 dark:bg-slate-800">
				<RemoteAgentHeader {agent} hasActions={false} />

				{#if linkedEntity}
					<div class="mt-5">
						<EntityCardSmall entity={linkedEntity} />
					</div>
				{/if}
			</div>

			<!-- Action Buttons -->
			<div class="flex flex-col gap-3">
				<div class="flex w-full flex-wrap gap-x-3">
					<!-- Delete and Revive (primary action if entity exists) -->

					{#if entityDetails && trigger}
						<div class="flex-1">
							<Button
								bind:element={deleteAndReviveButtonRef}
								onclick={handleDeleteAndRevive}
								disabled={isDeleting || isReviving}
								variant="secondary"
								class="w-full flex-1 justify-center focus:ring-2"
								icon={HandThumbDown}
								isMultiline
							>
								<div class="flex w-full flex-col text-left">
									{isReviving ? 'Deleting and restoring...' : 'This failed'}
								</div>
							</Button>
							<div class="mt-1 text-center text-xs font-light text-slate-500 dark:text-slate-400">
								Delete and restore
								{getEntityTypeDisplayName(entityDetails.entityType)} to Feed
							</div>
						</div>
					{/if}

					<!-- Delete Agent Only -->
					<div class="flex-1">
						<Button
							bind:element={deleteButtonRef}
							onclick={handleDeleteAgent}
							disabled={isDeleting || isReviving}
							variant="secondary"
							class="w-full flex-1 justify-center focus:ring-2"
							icon={entityDetails && trigger ? HandThumbUp : Trash}
							isMultiline
						>
							<div
								class="flex {entityDetails && trigger ? 'w-full flex-col text-left' : 'flex-none'}"
							>
								{#if entityDetails && trigger}
									{isDeleting && !isReviving ? 'Deleting...' : 'This succeeded'}
								{:else}
									{isDeleting ? 'Deleting...' : 'Delete Agent'}
								{/if}
							</div>
						</Button>
						{#if entityDetails && trigger}
							<div class="mt-1 text-center text-xs font-light text-slate-500 dark:text-slate-400">
								Delete agent
							</div>
						{/if}
					</div>
				</div>

				<!-- Cancel -->
				<Button
					onclick={handleCancel}
					disabled={isDeleting || isReviving}
					variant="ghost"
					class="w-full justify-center"
				>
					Cancel
				</Button>
			</div>
		</div>
	{/if}
</Modal>
