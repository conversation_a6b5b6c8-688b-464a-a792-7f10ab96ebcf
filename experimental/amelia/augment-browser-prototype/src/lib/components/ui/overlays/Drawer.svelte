<script lang="ts">
	import { Icon, XMark } from 'svelte-hero-icons';
	import Button from '../navigation/Button.svelte';
	import Portal from './Portal.svelte';
	import { onMount, type Snippet } from 'svelte';
	import { fade, fly } from 'svelte/transition';

	interface Props {
		open: boolean;
		onClose: () => void;
		title?: string;
		subtitle?: string;
		size?: 'sm' | 'md' | 'lg' | 'xl';
		position?: 'right' | 'left' | 'top' | 'bottom';
		contentHeight?: 'static' | 'scroll';
		header?: Snippet;
		footer?: Snippet;
		headerActions?: Snippet;
		children?: Snippet;
		target?: string | HTMLElement; // Portal target
		class?: string;
		style?: string;
	}

	let {
		open = false,
		onClose,
		title,
		subtitle,
		size = 'lg',
		position = 'bottom',
		contentHeight = 'scroll',
		header,
		headerActions,
		footer,
		children,
		target = 'body',
		class: className = '',
		style = ''
	}: Props = $props();

	let drawerElement = $state<HTMLElement>();

	const isVertical = $derived(position === 'top' || position === 'bottom');

	// Size classes with mobile-friendly constraints
	const sizeClassesHorizontal = {
		sm: 'max-w-sm w-full sm:w-auto',
		md: 'max-w-md w-full sm:w-auto',
		lg: 'max-w-[40em] w-full sm:w-auto',
		xl: 'max-w-[55em] w-full sm:w-auto'
	};
	const sizeClassesVertical = {
		sm: 'max-h-96',
		md: 'max-h-[20em]',
		lg: 'max-h-[44em]',
		xl: 'max-h-[93dvh]'
	};

	// Position classes with mobile-friendly positioning
	const positionClasses = {
		right: 'right-0 top-0 bottom-0 border-l sm:right-0',
		left: 'left-0 top-0 bottom-0 border-r sm:left-0',
		top: 'top-0 left-0 right-0 border-b',
		bottom: 'bottom-0 left-0 right-0 border-t'
	};

	// Mobile-specific sizing: max 95vw width on mobile, responsive on larger screens
	const mobileSizeClasses = $derived(
		isVertical
			? sizeClassesVertical[size]
			: `${sizeClassesHorizontal[size]} max-w-[95vw] sm:max-w-[40em] lg:max-w-[55em]`
	);

	// Handle escape key
	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape' && open) {
			onClose();
		}
	}

	// Focus management
	onMount(() => {
		if (open && drawerElement) {
			// Focus the first focusable element
			const focusableElements = drawerElement.querySelectorAll(
				'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
			);
			const firstElement = focusableElements[0] as HTMLElement;
			if (firstElement) {
				firstElement.focus();
			}
		}
	});

	$effect(() => {
		if (open) {
			// Prevent body scroll when drawer is open
			document.body.style.overflow = 'hidden';
			document.addEventListener('keydown', handleKeydown);
		} else {
			document.body.style.overflow = '';
			document.removeEventListener('keydown', handleKeydown);
		}

		// Cleanup on unmount
		return () => {
			document.body.style.overflow = '';
			document.removeEventListener('keydown', handleKeydown);
		};
	});
</script>

{#if open}
	<Portal {target}>
		<!-- Backdrop - More touch-friendly on mobile -->
		<div
			class="fixed inset-0 z-50 cursor-pointer touch-manipulation bg-white opacity-30 transition-opacity duration-300 dark:bg-black"
			onclick={onClose}
			ontouchstart={onClose}
			role="button"
			tabindex="-1"
			onkeydown={(e) => e.key === 'Enter' && onClose()}
			aria-label="Close drawer"
			transition:fade={{ duration: 100 }}
		></div>

		<!-- Drawer -->
		<div
			bind:this={drawerElement}
			class="fixed {positionClasses[
				position
			]} {mobileSizeClasses} {className} z-50 flex flex-col border-slate-200 bg-white shadow-xl dark:border-slate-700 dark:bg-slate-900"
			role="dialog"
			aria-modal="true"
			aria-labelledby={title ? 'drawer-title' : undefined}
			transition:fly={isVertical
				? { y: position === 'top' ? -120 : 120, duration: 200 }
				: { x: position === 'right' ? 120 : -120, duration: 200 }}
			{style}
		>
			<div class="flex h-full max-h-full min-h-0 flex-col">
				<!-- Header -->
				{#if header}
					{@render header()}
				{:else if title || subtitle}
					<div
						class="relative flex items-center justify-between border-b border-slate-200/60 bg-white/80 px-[var(--panel-padding-x)] py-4 backdrop-blur-sm dark:border-slate-800/60 dark:bg-slate-950/80"
					>
						<!-- Subtle gradient overlay -->
						<div
							class="pointer-events-none absolute inset-0 bg-gradient-to-r from-transparent via-slate-50/30 to-transparent dark:via-slate-900/30"
						></div>

						<div class="relative min-w-0 flex-1">
							{#if title}
								<h2
									id="drawer-title"
									class="truncate text-xl font-medium tracking-tight text-slate-900 dark:text-white"
								>
									{title}
								</h2>
							{/if}
							{#if subtitle}
								<p class="mt-0.5 text-sm font-normal text-slate-600 dark:text-slate-400">
									{subtitle}
								</p>
							{/if}
						</div>

						{#if headerActions}
							{@render headerActions()}
						{/if}

						<Button
							variant="ghost"
							size="icon-sm"
							onclick={onClose}
							aria-label="Close drawer"
							class="relative ml-4 flex-shrink-0 transition-colors duration-200 hover:bg-slate-100/80 dark:hover:bg-slate-800/80"
						>
							<Icon src={XMark} class="h-4 w-4 text-slate-500 dark:text-slate-400" />
						</Button>
					</div>
				{:else}
					<!-- Close button only -->
					<div
						class="relative flex justify-end border-b border-slate-200/60 bg-white/80 p-4 backdrop-blur-sm dark:border-slate-800/60 dark:bg-slate-950/80"
					>
						<!-- Subtle gradient overlay -->
						<div
							class="pointer-events-none absolute inset-0 bg-gradient-to-r from-transparent via-slate-50/30 to-transparent dark:via-slate-900/30"
						></div>

						<Button
							variant="ghost"
							size="icon-sm"
							onclick={onClose}
							aria-label="Close drawer"
							class="relative transition-colors duration-200 hover:bg-slate-100/80 dark:hover:bg-slate-800/80"
						>
							<Icon src={XMark} class="h-4 w-4 text-slate-500 dark:text-slate-400" />
						</Button>
					</div>
				{/if}

				<!-- Content -->
				<div
					class={{
						scroll: 'flex-1 overflow-y-auto',
						static: 'flex flex-1 flex-col overflow-y-hidden'
					}[contentHeight]}
				>
					{@render children?.()}

					<!-- Footer slot -->
					{#if footer}
						<div
							class="relative border-t border-slate-200/60 bg-white/80 backdrop-blur-sm dark:border-slate-800/60 dark:bg-slate-950/80"
						>
							<!-- Subtle gradient overlay -->
							<div
								class="pointer-events-none absolute inset-0 bg-gradient-to-r from-transparent via-slate-50/30 to-transparent dark:via-slate-900/30"
							></div>
							<div class="relative">
								{@render footer()}
							</div>
						</div>
					{/if}
				</div>
			</div>
		</div>
	</Portal>
{/if}

<style>
	/* Ensure smooth transitions */
	.transform {
		will-change: transform;
	}
</style>
