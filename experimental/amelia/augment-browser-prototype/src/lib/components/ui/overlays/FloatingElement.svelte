<script lang="ts">
	import { tick, type Snippet } from 'svelte';
	import { computePosition, flip, shift, offset, arrow, size, autoUpdate } from '@floating-ui/dom';
	import type { Placement, Strategy, Middleware } from '@floating-ui/dom';
	import Portal from './Portal.svelte';

	interface Props {
		// Core positioning
		placement?: Placement;
		strategy?: Strategy;
		offset?: number;

		// Behavior
		flip?: boolean;
		shift?: boolean;
		autoSize?: boolean;
		arrow?: boolean;

		// State
		show: boolean;
		reference: HTMLElement | null;

		// Styling
		class?: string;
		style?: string;
		zIndex?: number;

		// Portal
		portal?: boolean;
		portalTarget?: string | HTMLElement;

		// Callbacks
		onPositionUpdate?: (position: { x: number; y: number; placement: Placement }) => void;

		// Content
		children: Snippet;
		arrowContent?: Snippet;
	}

	let {
		placement = 'bottom',
		strategy = 'absolute',
		offset: offsetValue = 8,
		flip: enableFlip = true,
		shift: enableShift = true,
		autoSize = false,
		arrow: enableArrow = false,
		show = false,
		reference = null,
		class: className = '',
		style = '',
		zIndex = 1000,
		portal = true,
		portalTarget,
		onPositionUpdate,
		children,
		arrowContent
	}: Props = $props();

	let floatingElement = $state<HTMLElement>();
	let arrowElement: HTMLElement | null = null;
	let cleanup: (() => void) | null = null;

	let position = $state({ x: 0, y: 0, placement: placement as Placement });
	let arrowPosition = $state({ x: 0, y: 0 });

	// Build middleware array based on props
	const middleware = $derived(() => {
		const middlewares: Middleware[] = [];

		if (offsetValue) {
			middlewares.push(offset(offsetValue));
		}

		if (enableFlip) {
			middlewares.push(
				flip({
					fallbackPlacements: ['top', 'bottom', 'left', 'right'],
					crossAxis: false,
					boundary: document.body
				})
			);
		}

		if (enableShift) {
			middlewares.push(
				shift({
					padding: 8,
					boundary: document.body
				})
			);
		}

		if (autoSize) {
			middlewares.push(
				size({
					apply({ availableWidth, availableHeight, elements }) {
						Object.assign(elements.floating.style, {
							maxWidth: `${availableWidth}px`,
							maxHeight: `${availableHeight}px`
						});
					},
					padding: 8
				})
			);
		}

		if (enableArrow && arrowElement) {
			middlewares.push(arrow({ element: arrowElement }));
		}

		return middlewares;
	});

	async function updatePosition() {
		if (!reference || !floatingElement) return;

		try {
			const result = await computePosition(reference, floatingElement, {
				placement,
				strategy,
				middleware: middleware()
			});

			position = {
				x: result.x,
				y: result.y,
				placement: result.placement
			};

			// Handle arrow positioning
			if (enableArrow && result.middlewareData.arrow && arrowElement) {
				const { x: arrowX, y: arrowY } = result.middlewareData.arrow;
				arrowPosition = {
					x: arrowX ?? 0,
					y: arrowY ?? 0
				};
			}

			// Call position update callback
			onPositionUpdate?.(position);
		} catch (error) {
			console.warn('FloatingElement: Failed to compute position', error);
		}
	}

	// Setup auto-update when element is shown
	$effect(() => {
		if (show && reference && floatingElement) {
			// Initial position update
			updatePosition();

			// Setup auto-update for dynamic positioning
			cleanup = autoUpdate(reference, floatingElement, updatePosition, {
				// Update on scroll, resize, and when elements change
				ancestorScroll: true,
				ancestorResize: true,
				elementResize: true,
				layoutShift: true,
				// Animate on position changes for smooth updates
				animationFrame: true
			});
		} else if (cleanup) {
			cleanup();
			cleanup = null;
		}

		// Cleanup on unmount
		return () => {
			if (cleanup) {
				cleanup();
				cleanup = null;
			}
		};
	});

	// Computed styles for the floating element
	const floatingStyles = $derived(() => {
		const baseStyles = `
			position: ${strategy};
			top: 0;
			left: 0;
			transform: translate3d(${Math.round(position.x)}px, ${Math.round(position.y)}px, 0);
			z-index: ${zIndex};
		`;
		return `${baseStyles} ${style}`;
	});

	// Computed styles for the arrow
	const arrowStyles = $derived(() => {
		if (!enableArrow) return '';

		const side = position.placement.split('-')[0];
		const staticSide = {
			top: 'bottom',
			right: 'left',
			bottom: 'top',
			left: 'right'
		}[side];

		const isVertical = side === 'top' || side === 'bottom';

		if (isVertical) {
			// For vertical placements (top/bottom), only use left positioning
			return `
				position: absolute;
				${staticSide}: -4px;
				left: ${arrowPosition.x}px;
			`;
		} else {
			// For horizontal placements (left/right), only use top positioning
			return `
				position: absolute;
				${staticSide}: -4px;
				top: ${arrowPosition.y}px;
			`;
		}
	});

	// Handle arrow element binding
	function handleArrowElement(element: HTMLElement) {
		arrowElement = element;
		// Trigger position update when arrow element is available
		if (show && reference && floatingElement) {
			tick().then(updatePosition);
		}
	}
</script>

{#if show}
	{#if portal}
		<Portal target={portalTarget}>
			<div bind:this={floatingElement} class={className} style={floatingStyles()} role="tooltip">
				{@render children()}

				{#if enableArrow && arrowContent}
					<div use:handleArrowElement style={arrowStyles()}>
						{@render arrowContent()}
					</div>
				{/if}
			</div>
		</Portal>
	{:else}
		<div bind:this={floatingElement} class={className} style={floatingStyles()} role="tooltip">
			{@render children()}

			{#if enableArrow && arrowContent}
				<div use:handleArrowElement style={arrowStyles()}>
					{@render arrowContent()}
				</div>
			{/if}
		</div>
	{/if}
{/if}
