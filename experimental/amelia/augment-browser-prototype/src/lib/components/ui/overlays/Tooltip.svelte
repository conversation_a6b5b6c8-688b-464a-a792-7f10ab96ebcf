<script lang="ts">
	import { onMount } from 'svelte';
	import { fade } from 'svelte/transition';
	import type { Snippet } from 'svelte';
	import type { Placement } from '@floating-ui/dom';
	import FloatingElement from './FloatingElement.svelte';

	interface Props {
		text: string;
		placement?: Placement;
		delay?: number;
		disabled?: boolean;
		class?: string;
		duration?: number;
		doShowOnFocus?: boolean;
		tooltipClass?: string;
		tabindex?: number;
		maxWidth?: string;
		children: Snippet;
	}

	let {
		text,
		placement = 'top',
		delay = 500,
		disabled = false,
		duration = 200,
		doShowOnFocus = false,
		class: className = '',
		tooltipClass = '',
		maxWidth = '200px',
		tabindex = 0,
		children
	}: Props = $props();

	let showTooltip = $state(false);
	let timeoutId: number | null = null;
	let triggerElement = $state<HTMLElement>();

	function handleMouseEnter() {
		if (disabled || !text.trim()) return;

		timeoutId = window.setTimeout(() => {
			showTooltip = true;
		}, delay);
	}

	function handleMouseLeave() {
		if (timeoutId) {
			clearTimeout(timeoutId);
			timeoutId = null;
		}
		showTooltip = false;
	}

	function handleFocus() {
		if (!doShowOnFocus) return;
		if (disabled || !text.trim()) return;
		showTooltip = true;
	}

	function handleBlur() {
		showTooltip = false;
	}

	onMount(() => {
		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId);
			}
		};
	});
</script>

<div
	bind:this={triggerElement}
	class="relative inline-block {className}"
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
	onfocus={handleFocus}
	onblur={handleBlur}
	role="button"
	{tabindex}
>
	<!-- Trigger content -->
	{@render children()}
</div>

<!-- Tooltip rendered with FloatingElement -->
{#if showTooltip && text.trim() && triggerElement}
	<FloatingElement
		show={showTooltip}
		reference={triggerElement}
		{placement}
		offset={8}
		flip={true}
		shift={true}
		arrow={true}
		class="pointer-events-none z-[9999] rounded bg-gray-900 px-2 py-1 text-xs font-medium text-white shadow-lg dark:bg-gray-100 dark:text-gray-900 {tooltipClass}"
		style="max-width: {maxWidth};"
	>
		<div role="tooltip">
			{text}
		</div>

		{#snippet arrowContent()}
			<div class="h-2 w-2 rotate-45 bg-gray-900 dark:bg-gray-100"></div>
		{/snippet}
	</FloatingElement>
{/if}
