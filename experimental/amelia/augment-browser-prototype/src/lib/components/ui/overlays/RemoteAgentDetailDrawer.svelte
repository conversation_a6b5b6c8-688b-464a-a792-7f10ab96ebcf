<script lang="ts">
	import { goto } from '$app/navigation';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import {
		ArrowTopRightOnSquare,
		Icon,
		Trash,
		XMark,
		ChatBubbleLeftRight,
		DocumentText,
		ArrowRight
	} from 'svelte-hero-icons';

	import AgentChatResponse from '$lib/components/agents/AgentChatResponse.svelte';
	import RemoteAgentHeader from '$lib/components/ui/layout/RemoteAgentHeader.svelte';
	import RemoteAgentDetailContent from '$lib/components/ui/RemoteAgentDetailContent.svelte';
	import ChatHistoryView from '$lib/components/chat-history/ChatHistoryView.svelte';
	import ChatProgressIndicator from '$lib/components/chat-history/ChatProgressIndicator.svelte';
	import StreamingIndicator from '$lib/components/chat/StreamingIndicator.svelte';
	import ChatHistorySkeleton from '$lib/components/ui/feedback/ChatHistorySkeleton.svelte';
	import { groupExchanges, processExchangesWithToolFiltering } from '$lib/components/chat-history';
	import AgentDeletionModal from './AgentDeletionModal.svelte';
	import {
		loadChatHistory,
		subscribeToAgentStreaming,
		unsubscribeFromAgentStreaming,
		startPollingAgent,
		startEnhancedPollingAgent,
		stopPollingAgent,
		isInitialLoad
	} from '$lib/stores/data-operations.svelte';
	import {
		getEntityByAgent,
		markAgentAsViewed,
		getChatExchanges,
		getChatSession,
		getAgent
	} from '$lib/stores/global-state.svelte';
	import { onDestroy } from 'svelte';
	import Drawer from './Drawer.svelte';
	import LoadingIndicator from '../feedback/LoadingIndicator.svelte';

	interface Props {
		open: boolean;
		agent: CleanRemoteAgent;
		onClose: () => void;
		onAgentDeleted?: () => void;
	}

	let { open, agent, onClose, onAgentDeleted }: Props = $props();

	// Get reactive agent data from store to ensure we have the latest status
	let agentStore = $derived(agent ? getAgent(agent.id) : null);
	let currentAgent = $derived($agentStore?.data || agent);

	// Tab state
	let activeTab = $state('overview');
	let showDeletionModal = $state(false);
	let codeViewExchangeIndex = $state(-1); // -1 = all changes, >= 0 = specific exchange

	// Tab configuration
	const tabs = [
		{ id: 'overview', label: 'Overview', icon: DocumentText },
		{ id: 'chat', label: 'Chat', icon: ChatBubbleLeftRight }
	];

	function handleAgentDeleted() {
		onAgentDeleted?.();
		onClose();
	}

	function handleUpdateCodeView(exchangeIndex: number) {
		codeViewExchangeIndex = exchangeIndex;
		activeTab = 'overview'; // Switch to overview tab to show code changes
	}

	// Get linked entity from global state
	let entityStore = $derived(getEntityByAgent(currentAgent.id));
	let linkedEntity = $derived($entityStore?.data || null);

	// Get chat session data for optimistic messages and streaming
	let chatSessionStore = $derived(currentAgent?.id ? getChatSession(currentAgent.id) : null);
	let chatSession = $derived($chatSessionStore?.data);

	// Get chat exchanges for this agent
	let chatExchangesStore = $derived(currentAgent?.id ? getChatExchanges(currentAgent.id) : null);
	let exchanges = $derived($chatExchangesStore || []);

	// Process exchanges for progress indicator
	let processedExchanges = $derived(processExchangesWithToolFiltering(exchanges));
	let exchangeGroups = $derived(groupExchanges(processedExchanges));

	// Determine what message to show in the chat history panel
	let chatHistoryMessage = $derived(
		(() => {
			// Check if we have a selected agent
			if (!currentAgent) {
				return 'No agent selected';
			}

			// Check if agent is starting
			if (currentAgent.status === RemoteAgentStatus.AGENT_STARTING) {
				return 'Environment loading';
			}

			// If agent exists but no exchanges yet, only show "No history yet" if we're not in initial load or currently loading
			if (exchanges.length === 0) {
				// If it's initial load or currently loading, return null to show skeleton/loading indicator
				if (isInitialLoad(currentAgent.id) || $chatSessionStore?.loading) {
					return null;
				}
				// Otherwise, we've confirmed there's no history after loading
				return 'No history yet';
			}

			// Should not reach here if we have exchanges
			return null;
		})()
	);

	let currentStreamingId = $state<string | null>(null);
	const componentId = 'agent-detail-drawer';

	// Effect to mark agent as viewed when loaded
	$effect(() => {
		if (currentAgent && currentAgent.hasUpdates) {
			markAgentAsViewed(currentAgent.id);
		}
	});

	// Effect to handle agent ID changes and manage streaming
	$effect(() => {
		// Unsubscribe from previous streaming if any
		if (currentStreamingId && currentStreamingId !== currentAgent?.id) {
			console.log('Unsubscribing from streaming for previous agent:', currentStreamingId);
			unsubscribeFromAgentStreaming(currentStreamingId, componentId);
			stopPollingAgent(currentStreamingId);
		}

		// Subscribe to streaming for new agent when drawer is open
		if (open && currentAgent?.id && currentAgent.id !== currentStreamingId) {
			console.log('Subscribing to streaming for agent in drawer:', currentAgent.id);
			currentStreamingId = currentAgent.id;

			// Load chat history and start streaming
			loadChatHistory(currentAgent.id).then(() => {
				try {
					subscribeToAgentStreaming(currentAgent.id, componentId);

					// If agent is running, also start enhanced polling as backup for status transitions
					if (currentAgent.status === RemoteAgentStatus.AGENT_RUNNING) {
						console.log(
							'Agent is running, starting enhanced polling as backup for status transitions'
						);
						startEnhancedPollingAgent(currentAgent.id);
					}
				} catch (error) {
					console.warn('Streaming failed, falling back to polling:', error);
					if (currentAgent.status === RemoteAgentStatus.AGENT_RUNNING) {
						startEnhancedPollingAgent(currentAgent.id);
					} else {
						startPollingAgent(currentAgent.id);
					}
				}
			});
		}

		// Unsubscribe from streaming when drawer closes
		if (!open && currentStreamingId) {
			console.log('Unsubscribing from streaming because drawer closed:', currentStreamingId);
			unsubscribeFromAgentStreaming(currentStreamingId, componentId);
			stopPollingAgent(currentStreamingId);
			currentStreamingId = null;
		}
	});

	onDestroy(() => {
		// Unsubscribe from streaming when the component is unmounted
		if (currentStreamingId) {
			console.log('Unsubscribing from streaming for agent on destroy:', currentStreamingId);
			unsubscribeFromAgentStreaming(currentStreamingId, componentId);
			stopPollingAgent(currentStreamingId);
		}
	});

	function deleteAgentLocal() {
		showDeletionModal = true;
	}

	function openFullPage() {
		goto(`/agents/${currentAgent.id}`);
	}

	// Handle progress indicator segment click - scroll to chat history and update code view
	function handleSegmentClick(groupId: string) {
		// Find the exchange index for this group and update code view
		const group = exchangeGroups.find((g) => g.id === groupId);
		if (group && group.exchanges.length > 0) {
			const firstExchange = group.exchanges[0];
			const exchangeIndex = exchanges.findIndex((ex) => ex.exchange.requestId === firstExchange.id);
			if (exchangeIndex !== -1) {
				handleUpdateCodeView(exchangeIndex);
			}
		}

		// First, try to find the specific group element
		const groupElement = document.querySelector(`[data-group-id="${groupId}"]`);

		if (groupElement) {
			groupElement.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});
		} else {
			// Fallback: Find the chat history container using the data attribute
			const chatHistoryElement = document.querySelector('[data-chat-history-container]');
			if (chatHistoryElement) {
				chatHistoryElement.scrollIntoView({
					behavior: 'smooth',
					block: 'start'
				});
			} else {
				console.warn('Chat history container not found for scroll');
			}
		}
	}
</script>

{#if currentAgent}
	<Drawer {open} {onClose} size="xl" position="right" contentHeight="static" class="lg:!w-[55em]">
		{#snippet header()}
			<div
				class="relative flex items-center justify-between border-b border-gray-200/60 bg-white/80 py-4 backdrop-blur-sm md:px-[var(--panel-padding-x)] dark:border-gray-800/60 dark:bg-gray-950/80"
			>
				<!-- Subtle gradient overlay -->
				<div
					class="pointer-events-none absolute inset-0 bg-gradient-to-r from-transparent via-gray-50/30 to-transparent dark:via-gray-900/30"
				></div>

				<div class="relative min-w-0 flex-1">
					<RemoteAgentHeader agent={currentAgent} onAgentDeleted={handleAgentDeleted} />
				</div>

				<!-- action buttons -->
				<div class="flex items-center gap-2">
					<Button
						variant="ghost"
						size="icon-sm"
						onclick={onClose}
						aria-label="Close drawer"
						class="absolute top-3 right-3 flex-shrink-0 transition-colors duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-800/80"
					>
						<Icon src={XMark} class="h-4 w-4 text-gray-500 dark:text-gray-400" />
					</Button>
				</div>
			</div>
		{/snippet}

		{#snippet children()}
			<div class="flex h-full min-h-0 w-full flex-col">
				<!-- Tab Navigation -->
				<div
					class="flex-shrink-0 border-b border-gray-200/60 bg-white/80 dark:border-gray-800/60 dark:bg-gray-950/80"
				>
					<div class="px-3 pt-2 pb-2 md:px-6 xl:px-10">
						<div class="flex items-center justify-between">
							<div class="flex space-x-1">
								{#each tabs as tab}
									<Button
										variant={activeTab === tab.id ? 'primary' : 'ghost'}
										icon={tab.icon}
										onclick={() => (activeTab = tab.id)}
										size="sm"
									>
										{tab.label}
									</Button>
								{/each}
							</div>
							<a
								href={`/agents/${currentAgent.id}`}
								class="flex items-center gap-2 text-sm font-medium text-slate-500"
							>
								Open Full Page
								<Icon src={ArrowRight} class="h-4 w-4" mini />
							</a>
						</div>
					</div>
				</div>

				<!-- Tab Content -->
				<div class="min-h-0 flex-1 overflow-hidden">
					{#if activeTab === 'overview'}
						<!-- Overview Tab -->
						<div class="mt-4 h-full overflow-auto">
							<RemoteAgentDetailContent
								agent={currentAgent}
								onAgentDeleted={handleAgentDeleted}
								currentViewIndex={codeViewExchangeIndex}
							/>
						</div>
					{:else if activeTab === 'chat'}
						<!-- Chat Tab -->
						<div class="flex h-full flex-col">
							<!-- Chat History -->
							<div class="min-h-0 flex-1" data-chat-history-container>
								{#if exchanges.length > 0}
									<ChatHistoryView
										{exchanges}
										optimisticMessage={chatSession?.optimisticMessage}
										isStreaming={chatSession?.isStreaming || false}
										streamingContent={chatSession?.streamingContent || ''}
										streamingMessages={chatSession?.streamingMessages || new Map()}
										workspaceStatus={currentAgent.workspaceStatus}
										agent={currentAgent}
										{exchangeGroups}
										onSegmentClick={handleSegmentClick}
										onUpdateCodeView={handleUpdateCodeView}
									/>
								{:else if chatHistoryMessage}
									{#if chatHistoryMessage === 'Environment loading'}
										<div class="flex items-center justify-center gap-2 py-20 text-slate-500">
											<LoadingIndicator variant="asterisk" color="blue" />
											Spinning up environment...
										</div>
									{:else}
										<div class="p-4 text-center text-gray-500">{chatHistoryMessage}</div>
									{/if}
								{:else if currentAgent && isInitialLoad(currentAgent.id)}
									<ChatHistorySkeleton />
								{:else}
									<div class="p-4 text-center text-gray-500">Loading chat history...</div>
								{/if}
							</div>

							<!-- Chat Input -->
							<div
								class="flex-shrink-0 border-t border-gray-200/60 bg-white/80 backdrop-blur-sm dark:border-gray-800/60 dark:bg-gray-950/80"
							>
								<div class="">
									<AgentChatResponse agentId={currentAgent.id} />
								</div>
							</div>
						</div>
					{/if}
				</div>
			</div>
		{/snippet}
	</Drawer>
{:else if open}
	<Drawer {open} {onClose} size="xl" position="right" contentHeight="static">
		{#snippet children()}
			<div class="flex flex-1 items-center justify-center">
				<div class="text-center">
					<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Agent not found</h3>
					<p class="text-sm text-gray-500 dark:text-gray-400">
						The agent you're looking for doesn't exist or has been deleted.
					</p>
				</div>
			</div>
		{/snippet}
	</Drawer>
{/if}

<!-- Agent Deletion Modal -->
<AgentDeletionModal
	isOpen={showDeletionModal}
	agent={currentAgent}
	onClose={() => (showDeletionModal = false)}
	onAgentDeleted={handleAgentDeleted}
/>
