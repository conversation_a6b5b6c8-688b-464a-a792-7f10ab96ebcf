<script lang="ts">
	import { Icon, InformationCircle, PaperAirplane } from 'svelte-hero-icons';
	import Button from '../navigation/Button.svelte';
	import Drawer from './Drawer.svelte';
	import Input from '../forms/Input.svelte';
	import Textarea from '../forms/Textarea.svelte';
	import {
		apiClient,
		ChatRequestNodeType,
		type RemoteAgentWorkspaceSetup
	} from '$lib/api/unified-client';

	function createWorkspaceSetup(repositoryUrl: string, branch: string): RemoteAgentWorkspaceSetup {
		return {
			startingFiles: {
				githubCommitRef: {
					repositoryUrl,
					gitRef: branch
				}
			}
		};
	}
	import { addToast } from '$lib/stores/toast';
	import { wrapInitialMessageWithLatestInstructions } from '$lib/types/structured-response';
	import GitHub from '$lib/icons/GitHubIcon.svelte';

	interface Props {
		open: boolean;
		onClose: () => void;
		onSuccess: () => void;
	}

	let { open, onClose, onSuccess }: Props = $props();

	let isSubmitting = $state(false);
	let error = $state('');

	let formData = $state({
		prompt: '',
		repositoryUrl: 'https://github.com/augmentcode/augment',
		branch: 'main'
	});

	// Validation
	let isValid = $derived(
		formData.prompt.trim().length > 0 &&
			formData.repositoryUrl.trim().length > 0 &&
			formData.branch.trim().length > 0
	);

	// Auto-focus management
	let promptTextarea: any;

	$effect(() => {
		if (open && promptTextarea && !isSubmitting) {
			// Small delay to ensure drawer is fully open
			setTimeout(() => {
				promptTextarea?.focus?.();
			}, 100);
		}
	});

	async function handleSubmit() {
		if (!isValid || isSubmitting) return;

		isSubmitting = true;
		error = '';

		try {
			const workspaceSetup = createWorkspaceSetup(
				formData.repositoryUrl.trim(),
				formData.branch.trim()
			);

			// Wrap the initial prompt with structured response instructions
			const wrappedPrompt = wrapInitialMessageWithLatestInstructions(formData.prompt.trim());

			await apiClient.agents.create({
				initialRequestDetails: {
					requestNodes: [
						{
							id: 1,
							type: ChatRequestNodeType.TEXT,
							textNode: {
								content: wrappedPrompt
							}
						}
					]
				},
				workspaceSetup
			});

			addToast({
				type: 'success',
				message: `Agent created successfully`,
				duration: 5000
			});

			// Reset form
			formData = {
				prompt: '',
				repositoryUrl: 'https://github.com/augmentcode/augment',
				branch: 'main'
			};

			onSuccess();
		} catch (err) {
			console.error('Failed to create agent:', err);
			error = err instanceof Error ? err.message : 'Failed to create agent';
		} finally {
			isSubmitting = false;
		}
	}

	function handleClose() {
		if (isSubmitting) return;
		onClose();
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' && (event.metaKey || event.ctrlKey)) {
			event.preventDefault();
			handleSubmit();
		}
	}
</script>

<Drawer
	{open}
	{onClose}
	title="Create Agent"
	subtitle="Start a new coding agent to work on your repository"
	size="xl"
	contentHeight="static"
>
	<!-- Form Content -->
	<div class="h-full flex-1 overflow-y-auto p-6">
		<form
			onsubmit={(e) => {
				e.preventDefault();
				handleSubmit();
			}}
			class="space-y-6"
		>
			<!-- Main Prompt Input -->
			<div class="space-y-2">
				<label for="prompt" class="block text-sm font-medium text-slate-700 dark:text-slate-300">
					What would you like your agent to work on?
				</label>
				<div class="relative">
					<Textarea
						bind:this={promptTextarea}
						id="prompt"
						bind:value={formData.prompt}
						placeholder="Describe the task you want the agent to complete..."
						rows={4}
						class="w-full"
						textareaClass="pr-12 text-base border-slate-200 dark:border-slate-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all resize-none"
						onkeydown={handleKeydown}
						required
					/>

					<!-- Send button inside textarea -->
					<div class="absolute right-3 bottom-3">
						<Button
							type="button"
							variant="primary"
							size="icon-sm"
							disabled={!isValid || isSubmitting}
							onclick={handleSubmit}
							class="shadow-sm"
							aria-label="Create agent"
						>
							{#if isSubmitting}
								<div
									class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
								></div>
							{:else}
								<Icon src={PaperAirplane} class="h-4 w-4" />
							{/if}
						</Button>
					</div>
				</div>
				<p class="text-xs text-slate-500">
					Press <kbd
						class="rounded border bg-slate-100 px-1.5 py-0.5 font-mono text-xs dark:bg-slate-800"
						>⌘ + Enter</kbd
					> to create
				</p>
			</div>

			<!-- Repository Configuration -->
			<div class="space-y-4 border-t border-slate-200 pt-4 dark:border-slate-700">
				<div class="mb-3 flex items-center gap-2">
					<GitHub class="h-4 w-4 text-slate-500" />
					<span class="text-sm font-medium text-slate-700 dark:text-slate-300"
						>Repository Setup</span
					>
				</div>

				<div class="grid grid-cols-3 gap-3">
					<div class="col-span-2">
						<label
							for="repository"
							class="mb-1 block text-xs font-medium text-slate-600 dark:text-slate-400"
						>
							Repository URL
						</label>
						<Input
							id="repository"
							type="url"
							bind:value={formData.repositoryUrl}
							placeholder="https://github.com/owner/repo"
							class="text-sm"
							required
						/>
					</div>
					<div>
						<label
							for="branch"
							class="mb-1 block text-xs font-medium text-slate-600 dark:text-slate-400"
						>
							Branch
						</label>
						<Input
							id="branch"
							type="text"
							bind:value={formData.branch}
							placeholder="main"
							class="text-sm"
							required
						/>
					</div>
				</div>
			</div>

			<!-- Error Display -->
			{#if error}
				<div
					class="flex items-center gap-2 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20"
				>
					<Icon src={InformationCircle} class="h-5 w-5 flex-shrink-0 text-red-500" />
					<span class="text-sm text-red-700 dark:text-red-400">{error}</span>
				</div>
			{/if}

			<!-- Actions -->
			<div class="flex items-center justify-between pt-4">
				<div class="text-xs text-slate-500">Agent will be created with Claude 3.5 Sonnet</div>
				<div class="flex items-center gap-3">
					<Button variant="ghost" onclick={handleClose} disabled={isSubmitting}>Cancel</Button>
					<Button
						variant="primary"
						onclick={handleSubmit}
						disabled={!isValid || isSubmitting}
						loading={isSubmitting}
					>
						{isSubmitting ? 'Creating Agent...' : 'Create Agent'}
					</Button>
				</div>
			</div>
		</form>
	</div>
</Drawer>
