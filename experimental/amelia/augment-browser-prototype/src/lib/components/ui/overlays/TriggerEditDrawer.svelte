<script lang="ts">
	import { Icon, InformationCircle, ChevronRight } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Drawer from './Drawer.svelte';
	import IterativeChat from './IterativeChat.svelte';
	import EventPills from './EventPills.svelte';
	import CardButton from './CardButton.svelte';
	import CardButtonGroup from './CardButtonGroup.svelte';
	import ConditionDisplay from './ConditionDisplay.svelte';
	import InlineConditionCreator from './InlineConditionCreator.svelte';
	import Input from './Input.svelte';
	import Textarea from './Textarea.svelte';
	import Toggle from './Toggle.svelte';
	// TODO: Implement updateTrigger with proper API mapping in new global state system
	// import { updateTrigger } from '$lib/stores/global-state.svelte';
	function updateTrigger(id: string, data: any) {
		console.warn('updateTrigger disabled during migration', { id, data });
		return Promise.resolve();
	}
	import type { Trigger, UpdateTriggerInput, TriggerSourceType, TriggerCondition } from '$lib/types';
	import { TRIGGER_PROVIDERS, getProviderById, getEventByName, getAvailableFields } from '$lib/trigger-providers';
	import { slide } from 'svelte/transition';
	import CustomIcon from '$lib/components/ui/visualization/CustomIcon.svelte';

	interface Props {
		open: boolean;
		trigger: Trigger | null;
		onClose: () => void;
		onSuccess?: () => void;
	}

	let { open, trigger, onClose, onSuccess }: Props = $props();

	// Form state
	let formData = $state<UpdateTriggerInput>({});
	let isSubmitting = $state(false);
	let error = $state<string | null>(null);
	let showAdvanced = $state(false);

	// Reactive values for provider/event selection
	let selectedProvider = $derived(formData.sourceProvider ? getProviderById(formData.sourceProvider) : null);
	let selectedEvent = $derived(selectedProvider && formData.sourceEvent && formData.sourceProvider ? getEventByName(formData.sourceProvider, formData.sourceEvent) : null);
	let availableFields = $derived(selectedProvider && formData.sourceEvent && formData.sourceProvider ? getAvailableFields(formData.sourceProvider, formData.sourceEvent) : []);

	// Initialize form when drawer opens or trigger changes
	$effect(() => {
		if (open && trigger) {
			// Parse conditions and workspace setup
			let parsedConditions: TriggerCondition[] = [];
			let parsedWorkspaceSetup = null;

			try {
				if (trigger.conditions) {
					parsedConditions = JSON.parse(trigger.conditions);
				}
			} catch (e) {
				console.warn('Failed to parse trigger conditions:', e);
			}

			try {
				if (trigger.workspaceSetup) {
					parsedWorkspaceSetup = JSON.parse(trigger.workspaceSetup);
				}
			} catch (e) {
				console.warn('Failed to parse workspace setup:', e);
			}

			formData = {
				name: trigger.name,
				description: trigger.description || '',
				instructions: trigger.instructions,
				isActive: trigger.isActive,
				sourceType: trigger.sourceType as TriggerSourceType,
				sourceProvider: trigger.sourceProvider || '',
				sourceEvent: trigger.sourceEvent || '',
				workspaceSetup: parsedWorkspaceSetup || {
					githubRef: 'main',
					url: '',
					ref: 'main'
				},
				conditions: parsedConditions,
				maxPerHour: trigger.maxPerHour || 10,
				debounceSeconds: trigger.debounceSeconds || 300
			};
			error = null;
		}
	});

	function addCondition(condition: TriggerCondition) {
		if (!formData.conditions) formData.conditions = [];
		formData.conditions.push(condition);
		formData = { ...formData }; // Trigger reactivity
	}

	function removeCondition(index: number) {
		if (formData.conditions) {
			formData.conditions.splice(index, 1);
			formData = { ...formData }; // Trigger reactivity
		}
	}

	function editCondition(event: { index: number; condition: TriggerCondition }) {
		if (formData.conditions) {
			formData.conditions[event.index] = event.condition;
			formData = { ...formData }; // Trigger reactivity
		}
	}

	// Clear event and conditions when provider changes
	$effect(() => {
		if (formData.sourceProvider) {
			// Clear event if it doesn't exist for the new provider
			const provider = getProviderById(formData.sourceProvider);
			if (provider && formData.sourceEvent) {
				const eventExists = provider.events.some(e => e.name === formData.sourceEvent);
				if (!eventExists) {
					formData.sourceEvent = '';
					formData.conditions = [];
				}
			}
		}
	});

	function onFormUpdate(update: Partial<UpdateTriggerInput>) {
		// Apply partial updates to form data
		Object.assign(formData, update);
		formData = { ...formData }; // Trigger reactivity
	}

	function getCurrentFormState() {
		return { ...formData };
	}

	function onRevertUpdate(previousState: Partial<UpdateTriggerInput>) {
		// Revert to previous form state
		formData = { ...formData, ...previousState };
	}

	async function handleSubmit() {
		if (!trigger?.id) {
			error = 'No trigger selected for editing';
			return;
		}

		if (!formData.name?.trim() || !formData.instructions?.trim()) {
			error = 'Name and instructions are required';
			return;
		}

		isSubmitting = true;
		error = null;

		try {
			await updateTrigger(trigger.id, formData);
			onSuccess?.();
			onClose();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to update trigger';
		} finally {
			isSubmitting = false;
		}
	}
</script>

<Drawer
	{open}
	{onClose}
	title="Edit Trigger"
	subtitle="Modify trigger configuration and settings"
	size="xl"
	contentHeight="static"
>
	<div class="h-full min-h-0 grid grid-cols-[1fr_400px]">
		<!-- Form Content -->
		<div class="flex-1 h-full p-6 overflow-y-auto">
		<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-6">
			<!-- Source Configuration -->
			<div class="grid grid-cols-[320px_1fr] gap-6">
				<!-- Provider Selection (Left Column) -->
				<div class="space-y-4">
					<div>
						<div class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
							Provider
						</div>
						<CardButtonGroup direction="vertical" class="w-full" role="radiogroup" aria-label="Select provider">
							{#each TRIGGER_PROVIDERS as provider}
								<CardButton
									variant="grouped"
									selected={formData.sourceProvider === provider.id}
									ariaLabel={`Select ${provider.name} provider`}
									onclick={() => {
										formData.sourceProvider = provider.id;
										const firstEvent = provider.events[0]?.name;
										if (firstEvent) {
											formData.sourceEvent = firstEvent;
										}
									}}
								>
									<div class="flex items-center gap-3">
										<div class="w-8 h-8">
											{#if provider.iconName && ['github', 'linear', 'slack', 'datadog', 'pagerduty', 'cron'].includes(provider.iconName)}
											<CustomIcon icon={provider.iconName as 'github' | 'linear' | 'slack' | 'datadog' | 'pagerduty' | 'cron'} size="100%" />
											{:else if provider.iconSrc}
											<Icon src={provider.iconSrc} alt={provider.name} class="w-8 h-8" />
											{/if}
										</div>
										<div class="min-w-0 flex-1">
											<div class="font-medium text-sm text-gray-900 dark:text-white">{provider.name}</div>
											<div class="text-xs text-gray-500 dark:text-gray-400">{provider.description}</div>
										</div>
									</div>
								</CardButton>
							{/each}
						</CardButtonGroup>
					</div>
				</div>

				<!-- Event and Conditions (Right Column) -->
				<div class="space-y-6">
					<!-- Event Selection -->
					<div>
						<div class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
							Event
						</div>
						<EventPills
							provider={selectedProvider || null}
							selectedEvent={formData.sourceEvent || ''}
							onEventChange={(eventName: string) => { formData.sourceEvent = eventName; }}
							disabled={!selectedProvider}
						/>
					</div>

					<!-- Conditions -->
					<div>
						<div class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
							Trigger Conditions
						</div>

						{#if !selectedEvent}
							<div class="text-sm text-gray-500 dark:text-gray-400 italic">
								Select a provider and event to configure conditions
							</div>
						{:else}
							<div class="space-y-3">
								<!-- Existing conditions -->
								{#if formData.conditions && formData.conditions.length > 0}
									{#each formData.conditions as condition, index}
										<ConditionDisplay
											{condition}
											{index}
											availableFields={availableFields}
											on:remove={(e) => removeCondition(e.detail)}
											on:edit={(e) => editCondition(e.detail)}
										/>
									{/each}
								{/if}

								<!-- Inline condition creator -->
								<InlineConditionCreator
									availableFields={availableFields}
									onadd={addCondition}
									placeholder={formData.conditions?.length ? "Add another condition..." : "Add condition..."}
								/>
							</div>

							{#if !formData.conditions || formData.conditions.length === 0}
								<div class="text-xs text-gray-500 dark:text-gray-400 mt-2">
									No conditions set - trigger will fire for all events of this type
								</div>
							{/if}
						{/if}
					</div>
				</div>
			</div>

			<!-- separator -->
			 <div class="border-b border-gray-200 dark:border-gray-700 my-12"></div>

			<!-- Basic Information -->
			<div class="space-y-4">
				<div>
					<label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Trigger Name *
					</label>
					<Input
						id="name"
						type="text"
						bind:value={formData.name}
						placeholder="e.g., Auto-fix P0 Issues"
						required
					/>
				</div>

				<div>
					<label for="instructions" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
						Agent Instructions *
					</label>
					<Textarea
						id="instructions"
						bind:value={formData.instructions}
						placeholder="Detailed instructions for the agent when this trigger fires..."
						rows={4}
						required
					/>
				</div>
			</div>

			<!-- Advanced Settings -->
			<div class="border border-gray-200 dark:border-gray-700 rounded-lg">
				<button
					type="button"
					onclick={() => { showAdvanced = !showAdvanced; }}
					class="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
				>
					<span class="text-sm font-medium text-gray-700 dark:text-gray-300">
						Advanced Settings
					</span>
					<div class="w-4 h-4 text-gray-400 transition-transform duration-200 {showAdvanced ? 'rotate-90' : ''}">
						<Icon src={ChevronRight} mini />
					</div>
				</button>

				{#if showAdvanced}
					<div class="px-4 pt-4 pb-4 space-y-4 border-t border-gray-200 dark:border-gray-700" transition:slide={{axis: 'y'}}>
						<!-- Rate Limiting -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<label for="maxPerHour" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									Max Executions per Hour
								</label>
								<Input
									id="maxPerHour"
									type="number"
									bind:value={formData.maxPerHour}
									min={1}
									max={100}
								/>
								<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
									Limit how many times this trigger can execute per hour
								</p>
							</div>

							<div>
								<label for="debounceSeconds" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
									Debounce (seconds)
								</label>
								<Input
									id="debounceSeconds"
									type="number"
									bind:value={formData.debounceSeconds}
									min={0}
									max={3600}
								/>
								<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
									Wait time before allowing the same trigger to fire again
								</p>
							</div>
						</div>

						<!-- Active Toggle -->
						<div class="flex items-center gap-3">
							<Toggle
								id="isActive"
								bind:checked={formData.isActive}
								label="Active"
							/>
							<p class="text-xs text-gray-500 dark:text-gray-400">
								Enable or disable this trigger
							</p>
						</div>
					</div>
				{/if}
			</div>

			<!-- Error Display -->
			{#if error}
				<div class="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
					<Icon src={InformationCircle} class="w-5 h-5 text-red-500" />
					<span class="text-sm text-red-700 dark:text-red-400">{error}</span>
				</div>
			{/if}

			<div class="flex items-center justify-end gap-3 p-6">
					<Button
						variant="outline"
						onclick={onClose}
						disabled={isSubmitting}
					>
						Cancel
					</Button>
					<Button
						variant="primary"
						onclick={handleSubmit}
						disabled={isSubmitting || !formData.name?.trim() || !formData.instructions?.trim()}
					>
						{isSubmitting ? 'Updating...' : 'Update Trigger'}
					</Button>
				</div>
		</form>
	</div>

		<!-- Chat Assistant -->
		<div class="w-full h-full min-h-0 flex-none border-l border-gray-200 dark:border-gray-700 flex flex-col">
			<IterativeChat
				systemPrompt={`You are an AI assistant helping users edit triggers for automation. You can ask questions about their requirements and suggest form updates. When suggesting updates, provide explanatory text and then list the configuration at the end in a JSON code block with the key 'formUpdate' containing the partial form data to apply. The JSON will be automatically parsed and displayed in a structured card - do not render or describe the JSON structure in your response.

Available Trigger Providers and Events:
${TRIGGER_PROVIDERS.map(provider =>
	`${provider.name} (${provider.id}):
${provider.events.map(event => `  - ${event.name}: ${event.description}`).join('\n')}`
).join('\n\n')}

Available Event Fields by Provider:
${TRIGGER_PROVIDERS.map(provider =>
	provider.events.map(event =>
		`${provider.name}.${event.name}:
${event.fields.map(field => `  - ${field.name} (${field.path}): ${field.description} [${field.allowedOperators.join(', ')}]`).join('\n')}`
	).join('\n')
).join('\n\n')}

IMPORTANT: Only use the exact event names and field paths listed above. Do not invent new event names like "issue.created" - use the actual event names like "issues", "pull_request", "push", etc.

Available form fields: name, description, instructions, sourceProvider, sourceEvent, conditions (array), maxPerHour, debounceSeconds, isActive.`}
				{onFormUpdate}
				{getCurrentFormState}
				{onRevertUpdate}
				templateSuggestions={[
					"How can I improve this trigger's conditions?",
					"What are best practices for rate limiting?",
					"How can I make this trigger more specific?",
					"Should I adjust the debounce settings?",
					"How can I optimize the agent instructions?"
				]}
				generalSuggestions={[
					"How do trigger conditions work?",
					"What providers and events are available?",
					"How do I set up rate limiting and debouncing?",
					"What's the difference between webhook and scheduled triggers?",
					"How can I test my trigger before activating it?",
					"What are some common trigger patterns?",
					"How do I handle errors in trigger execution?",
					"What happens when I change the provider or event?"
				]}
				placeholder="Ask me anything about editing this trigger..."
			/>
		</div>

</div>

</Drawer>
