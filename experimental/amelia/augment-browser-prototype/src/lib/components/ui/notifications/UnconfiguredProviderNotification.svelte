<script lang="ts">
	import { Icon, ExclamationTriangle, XMark } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import ProviderAuthButton from '$lib/components/ui/navigation/ProviderAuthButton.svelte';
	import CustomIcon from '$lib/components/ui/visualization/CustomIcon.svelte';
	import { fly } from 'svelte/transition';
	import type { UnconfiguredProviderInfo } from '$lib/utils/provider-trigger-detection';

	interface Props {
		unconfiguredProviders: UnconfiguredProviderInfo[];
		onDismiss?: (providerId: string) => void;
		onAuthSuccess?: (providerId: string) => void;
		class?: string;
	}

	let {
		unconfiguredProviders = [],
		onDismiss,
		onAuthSuccess,
		class: className = ''
	}: Props = $props();

	let dismissedProviders = $state(new Set<string>());

	// Filter out dismissed providers
	const visibleProviders = $derived(
		unconfiguredProviders.filter((provider) => !dismissedProviders.has(provider.providerId))
	);

	function handleDismiss(providerId: string) {
		dismissedProviders.add(providerId);
		onDismiss?.(providerId);
	}

	function handleAuthSuccess(providerId: string) {
		onAuthSuccess?.(providerId);
	}
</script>

{#if visibleProviders.length > 0}
	<div class="space-y-3 {className}">
		{#each visibleProviders as provider (provider.providerId)}
			<div
				class="relative rounded-lg bg-slate-50 py-1 pr-1 pl-3 dark:bg-slate-800"
				in:fly={{ y: -20, duration: 300 }}
			>
				<!-- Dismiss button -->
				<!-- <button
					class="absolute top-3 right-3 text-slate-600 dark:text-slate-400 hover:text-slate-800 dark:hover:text-slate-200 transition-colors"
					onclick={() => handleDismiss(provider.providerId)}
					aria-label="Dismiss notification"
				>
					<Icon src={XMark} class="w-4 h-4" micro />
				</button> -->

				<div class="flex items-center gap-2">
					<!-- Provider icon -->
					<div class="flex-shrink-0">
						<CustomIcon
							icon={provider.providerId}
							size={20}
							class="text-slate-600 dark:text-slate-400"
						/>
					</div>

					<!-- Content -->
					<div class="min-w-0 flex-1">
						<div class="flex items-center gap-2">
							<!-- <Icon src={ExclamationTriangle} class="w-4 h-4 text-slate-600 dark:text-slate-400" micro /> -->
							<h3 class="flex-1 text-sm font-medium text-slate-800 dark:text-slate-200">
								{provider.providerName} not connected
							</h3>

							<!-- <p class="text-sm text-slate-700 dark:text-slate-300 mb-3">
							You haven't connected your account yet.
						</p> -->

							<!-- Connect button -->
							<ProviderAuthButton
								providerId={provider.providerId}
								variant="compact"
								showLabel={true}
								onAuthSuccess={() => handleAuthSuccess(provider.providerId)}
							/>
						</div>
					</div>
				</div>
			</div>
		{/each}
	</div>
{/if}
