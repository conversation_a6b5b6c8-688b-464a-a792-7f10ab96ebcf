<script lang="ts">
	import Pill from './Pill.svelte';
	import { GitBranch, GitHub, GitRepo } from './CustomIcon.svelte';
	import { Icon, CodeBracket } from 'svelte-hero-icons';

	interface Props {
		repositoryUrl?: string;
		gitRef?: string;
		size?: 'xs' | 'sm' | 'md';
		class?: string;
		showLabels?: boolean;
		layout?: 'horizontal' | 'vertical';
	}

	let {
		repositoryUrl,
		gitRef,
		size = 'sm',
		class: className = '',
		showLabels = false,
		layout = 'horizontal'
	}: Props = $props();

	// Extract repository name from URL
	const repositoryName = $derived(() => {
		if (!repositoryUrl) return '';
		return repositoryUrl.replace('https://github.com/', '');
	});

	// Determine if we should show the components
	const hasRepository = $derived(!!repositoryUrl);
	const hasBranch = $derived(!!gitRef);
	const hasAnyInfo = $derived(hasRepository || hasBranch);

	// Layout classes
	const layoutClasses = {
		horizontal: 'flex items-center gap-2',
		vertical: 'flex flex-col gap-2'
	};
</script>

{#if hasAnyInfo}
	<div class="{layoutClasses[layout]} {className}">
		{#if showLabels && layout === 'vertical'}
			<div class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
				Repository & Branch
			</div>
		{/if}

		<div class="{layout === 'vertical' ? 'flex flex-col gap-1.5' : 'flex items-center gap-2'}">
			{#if hasRepository}
				{#if showLabels && layout === 'horizontal'}
					<span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
						Repository
					</span>
				{/if}
				<Pill
					variant="gray"
					{size}
					href={repositoryUrl}
					target="_blank"
					icon={GitRepo}
				>
					{repositoryName()}
				</Pill>
			{/if}

			{#if hasBranch}
				{#if showLabels && layout === 'horizontal'}
					<span class="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
						Branch
					</span>
				{/if}
				<Pill
					variant="default"
					{size}
					icon={GitBranch}
				>
					{gitRef}
				</Pill>
			{/if}
		</div>
	</div>
{/if}
