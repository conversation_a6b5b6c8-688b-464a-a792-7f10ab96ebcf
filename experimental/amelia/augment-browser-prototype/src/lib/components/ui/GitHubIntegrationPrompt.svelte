<script lang="ts">
	import { getProviderAuthStatus, initiateProviderOAuth } from '$lib/stores/provider-auth';
	import { addToast } from '$lib/stores/toast';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { Icon } from 'svelte-hero-icons';
	import { CodeBracket, CheckCircle } from 'svelte-hero-icons';

	// Props
	let {
		projectTitle = 'this project',
		onIntegrationComplete
	}: {
		projectTitle?: string;
		onIntegrationComplete?: () => void;
	} = $props();

	// Get GitHub provider auth status
	const githubAuthStatus = getProviderAuthStatus('github');
	let isLoading = $state(false);

	async function handleConnectGitHub() {
		try {
			isLoading = true;
			console.log('Starting GitHub OAuth flow...');
			console.log('GitHub auth status:', $githubAuthStatus);

			// For GitHub OAuth, this will open a popup window
			await initiateProviderOAuth('github');
			console.log('OAuth popup opened successfully');

			// Reset loading state since popup is now open
			isLoading = false;
		} catch (error) {
			console.error('GitHub integration failed:', error);
			addToast({
				type: 'error',
				message: error instanceof Error ? error.message : 'GitHub integration failed',
				duration: 5000
			});
			isLoading = false;
		}
	}

	// Reactive values
	let isAuthenticated = $derived($githubAuthStatus?.isConfigured || false);
	let needsAuthentication = $derived($githubAuthStatus?.isAuthenticationRequired || false);
	let isLoaded = $derived($githubAuthStatus !== undefined);
</script>

{#if isLoaded && needsAuthentication && !isAuthenticated}
	<div class="flex min-h-screen items-center justify-center bg-slate-50 p-4 dark:bg-slate-900">
		<div class="w-full max-w-md">
			<div class="space-y-6 rounded-lg bg-white p-8 text-center shadow-lg dark:bg-slate-800">
				<!-- GitHub Icon -->
				<div class="flex justify-center">
					<div
						class="flex h-16 w-16 items-center justify-center rounded-full bg-slate-100 dark:bg-slate-700"
					>
						<Icon src={CodeBracket} class="h-8 w-8 text-slate-600 dark:text-slate-400" />
					</div>
				</div>

				<!-- Title and Description -->
				<div class="space-y-3">
					<h1 class="text-2xl font-bold text-slate-900 dark:text-white">Connect GitHub</h1>
					<p class="leading-relaxed text-slate-600 dark:text-slate-400">
						To access {projectTitle}, you need to connect your GitHub account. This allows you to:
					</p>
				</div>

				<!-- Features List -->
				<div class="space-y-3 text-left">
					<div class="flex items-start gap-3">
						<Icon src={CheckCircle} class="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
						<span class="text-sm text-slate-600 dark:text-slate-400">
							Sync issues and pull requests
						</span>
					</div>
					<div class="flex items-start gap-3">
						<Icon src={CheckCircle} class="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
						<span class="text-sm text-slate-600 dark:text-slate-400">
							Access repository information
						</span>
					</div>
					<div class="flex items-start gap-3">
						<Icon src={CheckCircle} class="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
						<span class="text-sm text-slate-600 dark:text-slate-400">
							Trigger automated workflows
						</span>
					</div>
					<div class="flex items-start gap-3">
						<Icon src={CheckCircle} class="mt-0.5 h-5 w-5 flex-shrink-0 text-green-500" />
						<span class="text-sm text-slate-600 dark:text-slate-400">
							Manage tasks and assignments
						</span>
					</div>
				</div>

				<!-- Connection Status -->
				{#if isAuthenticated}
					<div
						class="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20"
					>
						<div class="flex items-center gap-2 text-green-700 dark:text-green-400">
							<Icon src={CheckCircle} class="h-5 w-5" />
							<span class="font-medium">GitHub Connected</span>
						</div>
						<p class="mt-1 text-sm text-green-600 dark:text-green-500">
							GitHub integration is active. You can now access all project features.
						</p>
					</div>
				{/if}

				<!-- Action Button -->
				<div class="pt-2">
					{#if isAuthenticated}
						<Button
							variant="primary"
							size="lg"
							onclick={() => onIntegrationComplete?.()}
							class="w-full"
							icon="ArrowRightOnRectangle"
						>
							Continue to Dashboard
						</Button>
					{:else}
						<Button
							variant="primary"
							size="lg"
							onclick={handleConnectGitHub}
							loading={isLoading}
							icon={CodeBracket}
						>
							{isLoading ? 'Opening GitHub...' : 'Connect GitHub Account'}
						</Button>
					{/if}
				</div>

				<!-- Security Note -->
				<div class="text-xs leading-relaxed text-slate-500 dark:text-slate-400">
					<Icon src={CheckCircle} class="mr-1 inline h-4 w-4" />
					Secure OAuth authentication. We only request the minimum permissions needed.
				</div>
			</div>
		</div>
	</div>
{/if}
