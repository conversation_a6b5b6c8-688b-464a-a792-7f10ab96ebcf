<script lang="ts">
	import { Icon, ChevronDown, Check, XMark } from 'svelte-hero-icons';
	import { tick, onMount, type Snippet } from 'svelte';
	import fuzzysort from 'fuzzysort';
	import FloatingElement from '../overlays/FloatingElement.svelte';

	interface ComboboxOption {
		value: string;
		label: string;
		description?: string;
		icon?: any;
		disabled?: boolean;
		needsValue?: boolean;
		placeholder?: string;
		class?: string;
		data?: any; // Optional data payload for the option
	}

	interface Props {
		options: ComboboxOption[];
		value?: string;
		icon?: any;
		hasChevron?: boolean;
		displayValue?: string; // Override what's displayed when closed
		placeholder?: string;
		inputClass?: string;
		containerClass?: string;
		dropdownClass?: string;
		disabled?: boolean;
		searchable?: boolean;
		clearable?: boolean;
		creatable?: boolean; // Allow creating new options by typing
		isOpen?: boolean;
		isLoading?: boolean;
		searchThreshold?: number;
		disableFuzzySort?: boolean; // Disable fuzzy sorting to preserve option order
		inputElement?: HTMLInputElement;
		variant?: 'default' | 'underline';
		dropdownContent?: Snippet;
		class?: string;
		dropdownItemClass?: string;
		dropdownItemLabelClass?: string;
		dropdownMaxWidth?: string; // Maximum width for the dropdown
		onkeydown?: (event: KeyboardEvent) => void;
		onchange?: (value: string, option: ComboboxOption) => void;
		onclear?: () => void;
		onblur?: (event: FocusEvent) => void;
		onsearch?: (query: string) => void;
		onfiltered?: (filteredOptions: ComboboxOption[], searchTerm: string) => void;
	}

	let {
		options = [],
		value = $bindable(),
		icon,
		hasChevron = true,
		displayValue,
		placeholder = 'Select an option...',
		inputClass = '',
		containerClass = '',
		dropdownClass = '',
		disabled = false,
		searchable = true,
		clearable = false,
		creatable = false,
		variant = 'default',
		isOpen = $bindable(false),
		isLoading = false,
		searchThreshold = 0.3,
		disableFuzzySort = false,
		inputElement = $bindable<HTMLInputElement | undefined>(),
		dropdownContent,
		class: className = '',
		dropdownItemClass = '',
		dropdownItemLabelClass = '',
		dropdownMaxWidth = '24rem', // Default to 384px (max-w-96 equivalent)
		onkeydown,
		onchange,
		onclear,
		onblur,
		onsearch,
		onfiltered
	}: Props = $props();

	let searchTerm = $state('');
	let containerElement = $state<HTMLDivElement>();
	let dropdownElement = $state<HTMLDivElement>();
	let highlightedIndex = $state(-1);
	// Simplified state - FloatingElement handles positioning
	let dropdownOpen = $state(isOpen);

	const listboxId = `combobox-listbox-${Math.random().toString(36).substring(2, 11)}`;

	const selectedOption = $derived(options.find((opt) => opt.value === value));

	// Input value that shows selected label when closed, search term when open
	let inputValue = $state('');

	// Update input value when selection or open state changes (but not during active typing)
	$effect(() => {
		// Only update when dropdown is closed AND user is not actively typing
		if (!dropdownOpen && !userIsTyping) {
			if (selectedOption) {
				// Use displayValue if provided, otherwise use the selected option's label
				inputValue = displayValue || selectedOption.label;
			} else if (value) {
				inputValue = value;
			} else {
				inputValue = '';
			}
		}
		// Note: When dropdown is open, the oninput handler manages inputValue
		// to allow free typing without interference from this effect
	});

	// Track if user is actively typing (vs programmatic searchTerm changes)
	let userIsTyping = $state(false);

	// Call onsearch callback when search term changes
	$effect(() => {
		// Call onsearch when dropdown is open and either:
		// 1. User is actively typing, OR
		// 2. Search term is empty (to reset results)
		if (dropdownOpen && onsearch && (userIsTyping || searchTerm === '')) {
			onsearch(searchTerm);
		}
	});

	const filteredOptions = $derived(
		(() => {
			let baseOptions = options;

			if (searchTerm.trim()) {
				if (disableFuzzySort) {
					// Simple filtering without sorting - preserve original order
					const searchLower = searchTerm.toLowerCase();
					baseOptions = options.filter((option) => {
						const labelMatch = option.label.toLowerCase().includes(searchLower);
						const descMatch = option.description?.toLowerCase().includes(searchLower) || false;
						return labelMatch || descMatch;
					});
				} else {
					// Use fuzzysort for better fuzzy search
					const results = fuzzysort.go(searchTerm, options, {
						keys: ['label', 'description'],
						// More accurate matching (higher threshold = stricter)
						threshold: searchThreshold,
						// Limit results for performance
						limit: 100
					});

					// Return the original objects, sorted by score
					baseOptions = results.map((result) => result.obj);
				}
			}

			return baseOptions;
		})()
	);

	// Final options list that includes create option if needed
	const displayOptions = $derived(
		(() => {
			// Only show create option when actively searching and no exact match exists
			const shouldShowCreate =
				creatable &&
				searchTerm.trim() &&
				dropdownOpen && // Only when dropdown is open
				!options.some((option) => option.value === searchTerm.trim());

			if (shouldShowCreate) {
				const createOption: ComboboxOption & { isCreateOption: boolean } = {
					value: searchTerm.trim(),
					label: `Create "${searchTerm.trim()}"`,
					class:
						'border-b border-slate-200 dark:border-slate-700 font-medium text-blue-600 dark:text-blue-400',
					isCreateOption: true,
					disabled: false
				};
				return [createOption, ...filteredOptions];
			}

			return filteredOptions;
		})()
	);

	// Call onfiltered callback when filtered options change
	$effect(() => {
		if (onfiltered) {
			onfiltered(displayOptions, searchTerm);
		}
	});

	// Sync external isOpen prop
	$effect(() => {
		dropdownOpen = isOpen;
	});

	function openDropdown() {
		if (disabled) return;
		dropdownOpen = true;
		highlightedIndex = -1;
		userIsTyping = false; // Reset typing flag
		searchTerm = ''; // Clear search term when opening

		// Add mobile body scroll prevention
		if (window.innerWidth < 768) {
			document.body.classList.add('dropdown-open');
		}

		if (searchable) {
			tick().then(() => {
				inputElement?.focus();
				// select all text when opening for easier typing
				inputElement?.setSelectionRange(0, inputElement.value.length);

				// On mobile, scroll input into view when keyboard opens
				if (window.innerWidth < 768 && inputElement) {
					// Small delay to allow keyboard to open
					setTimeout(() => {
						inputElement?.scrollIntoView({
							behavior: 'smooth',
							block: 'center',
							inline: 'nearest'
						});
					}, 100);
				}
			});
		}
	}

	function scrollToHighlighted() {
		if (highlightedIndex >= 0 && dropdownElement) {
			const optionElements = dropdownElement.querySelectorAll('[role="option"]');
			const highlightedElement = optionElements[highlightedIndex] as HTMLElement;
			if (highlightedElement) {
				highlightedElement.scrollIntoView({
					block: 'nearest',
					behavior: 'smooth'
				});
			}
		}
	}

	function closeDropdown() {
		dropdownOpen = false;
		searchTerm = '';
		highlightedIndex = -1;

		// Remove mobile body scroll prevention
		document.body.classList.remove('dropdown-open');
	}

	function selectOption(option: ComboboxOption & { isCreateOption?: boolean }) {
		if (option.disabled) return;

		// Clear search term first to prevent reactive loops
		searchTerm = '';

		// For create options, use the raw value instead of the "Create ..." label
		if (option.isCreateOption) {
			value = option.value;
			inputValue = option.value; // Show the actual value, not "Create ..."
		} else {
			value = option.value;
			inputValue = option.label;
		}

		onchange?.(option.value, option);
		closeDropdown();
	}

	function clearSelection() {
		value = undefined;
		onclear?.();
		closeDropdown();
	}

	function handleKeydown(event: KeyboardEvent) {
		onkeydown?.(event);
		if (!isOpen) {
			if (event.key === 'Enter' || event.key === 'ArrowDown') {
				event.preventDefault();
				openDropdown();
			}
			return;
		}

		switch (event.key) {
			case 'Escape':
				event.preventDefault();
				event.stopPropagation(); // Prevent the escape from bubbling up to close the drawer
				closeDropdown();
				break;
			case 'ArrowDown':
				event.preventDefault();
				highlightedIndex = Math.min(highlightedIndex + 1, displayOptions.length - 1);
				scrollToHighlighted();
				break;
			case 'ArrowUp':
				event.preventDefault();
				highlightedIndex = Math.max(highlightedIndex - 1, -1);
				scrollToHighlighted();
				break;
			case 'Enter':
				event.preventDefault();
				if (highlightedIndex >= 0 && highlightedIndex < displayOptions.length) {
					selectOption(displayOptions[highlightedIndex]);
				}
				break;
			case 'Tab':
				closeDropdown();
				break;
		}
	}

	function handleClickOutside(event: MouseEvent) {
		if (dropdownOpen && containerElement && !containerElement.contains(event.target as Node)) {
			closeDropdown();
		}
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);

		// Cleanup function
		return () => {
			// Remove mobile body scroll prevention if still applied
			document.body.classList.remove('dropdown-open');

			// Remove event listeners
			document.removeEventListener('click', handleClickOutside);
		};
	});
</script>

<div class="relative {className}" bind:this={containerElement}>
	<div
		class="relative w-auto cursor-pointer text-left {variant === 'underline'
			? 'flex border-b border-slate-200 bg-transparent px-0 py-0 transition-all duration-200 focus-within:border-solid focus-within:border-slate-500 hover:border-solid hover:border-slate-500/80 dark:border-slate-500/60 dark:hover:border-slate-400/80'
			: 'rounded-md border border-slate-200 bg-white py-2 focus-within:border-slate-500 focus-within:ring-1 focus-within:ring-slate-500 dark:border-slate-700 dark:bg-slate-900'} {(selectedOption?.icon ||
			icon) &&
		variant !== 'underline'
			? 'pl-7'
			: variant === 'underline'
				? 'pl-0'
				: 'pl-3'} {variant === 'underline' || !hasChevron ? 'pr-4' : 'pr-10'} {disabled
			? 'cursor-not-allowed opacity-50 focus-within:border-1 focus-within:!border-slate-200 focus-within:!ring-0 dark:focus-within:!border-slate-600'
			: ''} {containerClass}"
		onclick={openDropdown}
		role="combobox"
		aria-expanded={dropdownOpen}
		aria-haspopup="listbox"
		aria-controls={listboxId}
		tabindex={searchable ? -1 : disabled ? -1 : 0}
	>
		{#if (selectedOption?.icon || icon) && variant !== 'underline'}
			<div class="absolute inset-y-0 left-0 flex items-center pl-2">
				<Icon src={icon || selectedOption?.icon} class="h-4 w-4 text-slate-400" micro />
			</div>
		{/if}

		{#if searchable}
			{#if selectedOption?.icon && variant === 'underline' && !isOpen}
				<Icon src={selectedOption.icon} class="mt-0.5 mr-1.5 inline h-4 w-4 text-slate-400" />
			{/if}
			<div
				class="relative {variant === 'underline'
					? 'inline-flex text-lg text-inherit'
					: 'w-full text-sm'}"
			>
				<input
					bind:this={inputElement}
					bind:value={inputValue}
					oninput={(e) => {
						const newValue = (e.target as HTMLInputElement).value;
						inputValue = newValue;
						if (!dropdownOpen) {
							openDropdown();
						}
						userIsTyping = true;
						searchTerm = newValue;
						// Reset userIsTyping flag after a short delay
						setTimeout(() => {
							userIsTyping = false;
						}, 100);
					}}
					onkeydown={handleKeydown}
					onfocus={openDropdown}
					onblur={(e) => {
						// Close dropdown after a delay to allow for option selection
						setTimeout(() => {
							isOpen = false;
							highlightedIndex = -1;
						}, 150);

						// Call the external onblur handler if provided
						if (onblur) {
							onblur(e);
						}
					}}
					class="w-full border-none bg-transparent p-0 {variant === 'underline'
						? 'absolute top-0 left-0 mt-[-3px] text-inherit'
						: 'text-slate-900 dark:text-white'} placeholder-slate-400 focus:ring-0 focus:outline-none dark:placeholder-slate-400 {disabled
						? 'cursor-not-allowed'
						: ''} {inputClass}"
					placeholder={selectedOption && !isOpen
						? ''
						: value || selectedOption?.label || placeholder}
					readonly={false}
					tabindex={disabled ? -1 : 0}
					{disabled}
				/>
				{#if variant === 'underline'}
					<!-- invisible text for saving space -->
					<div class="pointer-events-none flex h-[1.4em] w-full min-w-10 pr-2 opacity-0">
						{inputValue || selectedOption?.label || placeholder}
					</div>
				{/if}
			</div>
		{:else}
			<span
				class="block truncate {variant === 'underline' ? 'text-lg' : 'text-sm'} {variant ===
				'underline'
					? 'text-inherit'
					: 'text-slate-900 dark:text-white'}"
			>
				{#if selectedOption?.icon && variant === 'underline'}
					<Icon src={selectedOption.icon} class="mr-1 inline h-4 w-4 text-slate-400" />
				{/if}
				{displayValue || selectedOption?.label || placeholder}
			</span>
		{/if}

		<div
			class="absolute inset-y-0 right-0 flex items-center {variant === 'underline' || !hasChevron
				? 'pr-0'
				: 'pr-2'} space-x-1"
		>
			{#if clearable && value && variant !== 'underline'}
				<button
					type="button"
					class="rounded p-1 hover:bg-slate-100 dark:hover:bg-slate-700"
					onclick={(e) => {
						e.stopPropagation();
						clearSelection();
					}}
				>
					<Icon src={XMark} class="h-4 w-4 text-slate-400" />
				</button>
			{/if}
			{#if variant !== 'underline' && hasChevron}
				<Icon
					src={ChevronDown}
					class="h-4 w-4 text-slate-400 transition-transform duration-200 {dropdownOpen
						? 'rotate-180'
						: ''}"
				/>
			{/if}
		</div>
	</div>

	<!-- Floating Dropdown -->
	<FloatingElement
		show={dropdownOpen}
		reference={containerElement}
		placement="bottom-start"
		offset={4}
		flip={true}
		shift={true}
		autoSize={false}
		class="z-[100] max-h-60 w-auto overflow-hidden overflow-y-auto rounded-md bg-white shadow-lg ring-1 ring-slate-300 focus:outline-none dark:bg-slate-800 dark:ring-slate-600 {dropdownClass}"
		style="min-width: {Math.min(
			containerElement?.offsetWidth || 200,
			300
		)}px; max-width: {dropdownMaxWidth};"
	>
		{#if dropdownContent}
			{@render dropdownContent()}
		{/if}
		<div class="w-full py-1" role="listbox" id={listboxId}>
			{#each displayOptions as option, index}
				<button
					type="button"
					class="relative w-full cursor-pointer py-2 pr-9 pl-3 text-left select-none hover:bg-slate-100 dark:hover:bg-slate-700 {option.class ||
						''} {highlightedIndex === index
						? 'bg-slate-50 dark:bg-slate-900/20'
						: ''} {option.disabled ? 'cursor-not-allowed opacity-50' : ''} {dropdownItemClass}"
					onclick={() => selectOption(option)}
					role="option"
					aria-selected={value === option.value}
					disabled={option.disabled}
				>
					<div class="flex items-start space-x-3">
						{#if option.icon}
							<Icon src={option.icon} class="mt-0.5 h-5 w-5 flex-shrink-0 text-slate-400" micro />
						{/if}
						<div class="min-w-0 flex-1">
							<div
								class="{variant === 'underline'
									? 'text-sm'
									: 'text-sm'} font-medium break-all text-slate-900 dark:text-white {dropdownItemLabelClass}"
							>
								{option.label}
							</div>
							{#if option.description}
								<div class="mt-0.5 line-clamp-2 text-xs text-slate-500 dark:text-slate-400">
									{option.description}
								</div>
							{/if}
						</div>
					</div>

					{#if value === option.value}
						<span class="absolute inset-y-0 right-0 flex items-center pr-4">
							<Icon src={Check} class="h-4 w-4 text-slate-600" />
						</span>
					{/if}
				</button>
			{/each}

			{#if isLoading}
				<!-- Skeleton loading rows -->
				{#each [1, 2, 3] as i (i)}
					<div class="flex items-center gap-3 px-3 py-2">
						<!-- Icon skeleton -->
						<div class="h-4 w-4 animate-pulse rounded bg-slate-200 dark:bg-slate-700"></div>
						<!-- Text skeleton -->
						<div class="flex-1 space-y-1">
							<div
								class="h-4 animate-pulse rounded bg-slate-200 dark:bg-slate-700"
								style="width: {i === 1 ? '75%' : i === 2 ? '60%' : '85%'}"
							></div>
							{#if i !== 2}
								<div
									class="h-3 animate-pulse rounded bg-slate-100 dark:bg-slate-800"
									style="width: {i === 1 ? '45%' : '35%'}"
								></div>
							{/if}
						</div>
					</div>
				{/each}
			{:else if displayOptions.length === 0}
				<div class="px-3 py-2 text-sm text-slate-500 dark:text-slate-400">No options found</div>
			{/if}
		</div>
	</FloatingElement>
</div>
