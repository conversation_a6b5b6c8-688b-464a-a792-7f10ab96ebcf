<script lang="ts">
	interface Props {
		id?: string;
		value?: number;
		min?: number;
		max?: number;
		step?: number;
		disabled?: boolean;
		class?: string;
		onchange?: (value: number) => void;
		oninput?: (value: number) => void;
	}

	let {
		id,
		value = $bindable(),
		min = 0,
		max = 100,
		step = 1,
		disabled = false,
		class: className = '',
		onchange,
		oninput
	}: Props = $props();

	// Generate unique ID if not provided
	const sliderId = id || `slider-${Math.random().toString(36).substring(2, 11)}`;

	// Ensure value is within bounds and has a default
	$effect(() => {
		if (value === undefined || value === null) {
			value = min;
		} else {
			if (value < min) value = min;
			if (value > max) value = max;
		}
	});

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = Number(target.value);
		// Don't manually set value here since bind:value handles it
		oninput?.(newValue);
	}

	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = Number(target.value);
		// Don't manually set value here since bind:value handles it
		onchange?.(newValue);
	}

	// Calculate percentage for styling
	const percentage = $derived(() => {
		const safeValue = value ?? min;
		return ((safeValue - min) / (max - min)) * 100;
	});

	// Safe value for display
	const displayValue = $derived(value ?? min);
</script>

<div class="space-y-2 {className}">
	<div class="relative">
		<!-- Range input -->
		<input
			{id}
			type="range"
			bind:value
			{min}
			{max}
			{step}
			{disabled}
			class="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-lg appearance-none cursor-pointer slider-thumb focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all"
			oninput={handleInput}
			onchange={handleChange}
		/>

		<!-- Value display -->
		<div class="flex justify-between items-center mt-2 text-xs text-slate-500 dark:text-slate-400">
			<span>{min}</span>
			<span class="font-medium text-slate-700 dark:text-slate-300">{displayValue}</span>
			<span>{max}</span>
		</div>
	</div>
</div>

<style>
	/* Custom slider thumb styling */
	:global(.slider-thumb::-webkit-slider-thumb) {
		appearance: none;
		height: 20px;
		width: 20px;
		border-radius: 50%;
		background: #3b82f6;
		cursor: pointer;
		border: 2px solid #ffffff;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}

	:global(.slider-thumb::-webkit-slider-thumb:hover) {
		background: #2563eb;
		transform: scale(1.1);
	}

	:global(.slider-thumb::-webkit-slider-thumb:active) {
		background: #1d4ed8;
		transform: scale(1.05);
	}

	:global(.slider-thumb:disabled::-webkit-slider-thumb) {
		background: #9ca3af;
		cursor: not-allowed;
		transform: none;
	}

	/* Firefox */
	:global(.slider-thumb::-moz-range-thumb) {
		height: 20px;
		width: 20px;
		border-radius: 50%;
		background: #3b82f6;
		cursor: pointer;
		border: 2px solid #ffffff;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		transition: all 0.2s ease;
	}

	:global(.slider-thumb::-moz-range-thumb:hover) {
		background: #2563eb;
		transform: scale(1.1);
	}

	:global(.slider-thumb::-moz-range-thumb:active) {
		background: #1d4ed8;
		transform: scale(1.05);
	}

	:global(.slider-thumb:disabled::-moz-range-thumb) {
		background: #9ca3af;
		cursor: not-allowed;
		transform: none;
	}

	/* Track styling for dark mode */
	:global(.dark .slider-thumb::-webkit-slider-thumb) {
		border-color: #374151;
	}

	:global(.dark .slider-thumb::-moz-range-thumb) {
		border-color: #374151;
	}
</style>
