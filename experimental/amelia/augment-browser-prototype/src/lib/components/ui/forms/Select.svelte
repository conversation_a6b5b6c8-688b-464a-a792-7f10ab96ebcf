<script lang="ts">
	import { Icon, ChevronDown } from 'svelte-hero-icons';
	import { onMount } from 'svelte';
	import FloatingElement from '../overlays/FloatingElement.svelte';

	interface Option {
		value: string;
		label: string;
	}

	interface Props {
		id?: string;
		value?: string;
		options: Option[];
		placeholder?: string;
		disabled?: boolean;
		required?: boolean;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'error';
		class?: string;
		onchange?: (value: string) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
	}

	let {
		id,
		value = $bindable(),
		options = [],
		placeholder = 'Select...',
		disabled = false,
		required = false,
		size = 'md',
		variant = 'default',
		class: className = '',
		onchange,
		onfocus,
		onblur
	}: Props = $props();

	// Generate unique ID if not provided
	const fieldId = id || `select-${Math.random().toString(36).substring(2, 11)}`;

	let isOpen = $state(false);
	let triggerElement: HTMLButtonElement;

	// Base styles following the existing design patterns
	const baseStyles =
		'w-full bg-white dark:bg-slate-700 border border-slate-200 dark:border-slate-600 text-slate-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors';

	// Size styles
	const sizeStyles = {
		sm: 'px-2.5 py-1.5 text-xs',
		md: 'px-3 py-2 text-sm',
		lg: 'px-4 py-3 text-base'
	};

	// Variant styles
	const variantStyles = {
		default: '',
		error: 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500'
	};

	// Disabled styles
	const disabledStyles = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

	// Combined button class
	const buttonClasses = $derived(
		[
			baseStyles,
			sizeStyles[size],
			variantStyles[variant],
			disabledStyles,
			'flex items-center justify-between'
		]
			.filter(Boolean)
			.join(' ')
	);

	// Get selected option
	const selectedOption = $derived(options.find((option) => option.value === value));

	// Display text
	const displayText = $derived(selectedOption ? selectedOption.label : placeholder);

	// Icon size based on select size
	const iconSizes = {
		sm: 'w-3 h-3',
		md: 'w-4 h-4',
		lg: 'w-5 h-5'
	};

	const iconClass = $derived(iconSizes[size]);

	function toggleDropdown() {
		if (disabled) return;
		isOpen = !isOpen;
	}

	function selectOption(option: Option) {
		value = option.value;
		isOpen = false;
		onchange?.(option.value);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			isOpen = false;
		} else if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleDropdown();
		} else if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
			event.preventDefault();
			if (!isOpen) {
				toggleDropdown();
			}
		}
	}

	function handleOptionKeydown(event: KeyboardEvent, option: Option) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			selectOption(option);
		}
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event: MouseEvent) {
		if (isOpen && triggerElement && !triggerElement.contains(event.target as Node)) {
			const dropdown = document.querySelector(`[data-select-dropdown="${fieldId}"]`);
			if (dropdown && !dropdown.contains(event.target as Node)) {
				isOpen = false;
			}
		}
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);

		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});
</script>

<div class="relative {className}">
	<button
		type="button"
		id={fieldId}
		bind:this={triggerElement}
		onclick={toggleDropdown}
		onkeydown={handleKeydown}
		{onfocus}
		{onblur}
		class={buttonClasses}
		{disabled}
		aria-haspopup="listbox"
		aria-expanded={isOpen}
		aria-labelledby={fieldId}
	>
		<span class={selectedOption ? '' : 'text-slate-400 dark:text-slate-500'}>
			{displayText}
		</span>
		<div
			class="text-slate-400 transition-transform duration-200 dark:text-slate-500 {isOpen
				? 'rotate-180'
				: ''}"
		>
			<Icon src={ChevronDown} mini class={iconClass} />
		</div>
	</button>

	<!-- Floating Dropdown -->
	<FloatingElement
		show={isOpen}
		reference={triggerElement}
		placement="bottom-start"
		offset={4}
		flip={true}
		shift={true}
		autoSize={true}
		class="z-50 max-h-60 overflow-auto rounded-md border border-slate-200 bg-white shadow-lg dark:border-slate-600 dark:bg-slate-800"
		style="min-width: {triggerElement?.offsetWidth || 200}px;"
	>
		<div role="listbox">
			{#each options as option}
				<button
					type="button"
					class="w-full px-3 py-2 text-left text-sm hover:bg-slate-50 focus:bg-slate-50 focus:outline-none dark:hover:bg-slate-700 dark:focus:bg-slate-700 {value ===
					option.value
						? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
						: 'text-slate-900 dark:text-white'}"
					onclick={() => selectOption(option)}
					onkeydown={(event) => handleOptionKeydown(event, option)}
					role="option"
					aria-selected={value === option.value}
					tabindex="0"
				>
					{option.label}
				</button>
			{/each}
		</div>
	</FloatingElement>
</div>
