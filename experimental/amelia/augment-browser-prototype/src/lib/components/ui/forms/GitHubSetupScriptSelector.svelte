<script lang="ts">
	import {
		Icon,
		ChevronDown,
		MagnifyingGlass,
		DocumentText,
		ExclamationTriangle
	} from 'svelte-hero-icons';
	import { fly } from 'svelte/transition';
	import type { GithubSetupScript } from '$lib/types';
	import {
		listGithubSetupScripts,
		readGithubSetupScript,
		createGithubRefFromRepoAndBranch,
		getSetupScriptDisplayName,
		sortSetupScripts,
		filterSetupScripts,
		validateSetupScriptContent
	} from '$lib/utils/github-setup-scripts';
	import Input from './Input.svelte';

	// Props
	interface Props {
		repositoryUrl: string;
		branch: string;
		selectedScript: GithubSetupScript | null;
		selectedScriptContent: string;
		disabled?: boolean;
		placeholder?: string;
		class: string;
		onscriptSelected?: ({
			script,
			content
		}: {
			script: GithubSetupScript | null;
			content: string;
		}) => void;
		onerror?: ({ message }: { message: string }) => void;
	}

	let {
		repositoryUrl = '',
		branch = 'main',
		selectedScript = null,
		selectedScriptContent = '',
		disabled = false,
		placeholder = 'Select setup script...',
		class: className = '',
		onscriptSelected,
		onerror
	}: Props = $props();

	// State
	let isOpen = $state(false);
	let searchTerm = $state('');
	let scripts = $state<GithubSetupScript[]>([]);
	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let isLoadingContent = $state(false);
	let contentError = $state<string | null>(null);

	// Derived values
	let githubRef = $derived.by(() => {
		if (!repositoryUrl || !branch) return null;
		return createGithubRefFromRepoAndBranch(repositoryUrl, branch);
	});

	let filteredScripts = $derived(sortSetupScripts(filterSetupScripts(scripts, searchTerm)));

	let canLoadScripts = $derived(!disabled && !!githubRef && !isLoading);
	let shouldDisableCombobox = $derived(
		disabled || !githubRef || isLoading || (!isLoading && !error && scripts.length === 0)
	);
	let lastGithubRef: string | null | undefined;

	$effect(() => {
		// Guard: only run when canLoadScripts is true *and* ref changed
		if (!canLoadScripts || githubRef === lastGithubRef) return;

		lastGithubRef = githubRef;
		loadScripts();
	});

	async function loadScripts() {
		const ref = githubRef;
		if (!ref) return;

		isLoading = true;
		error = null;

		try {
			const result = await listGithubSetupScripts(ref);

			if (result.error) {
				error = result.error;
				scripts = [];
			} else {
				scripts = result.scripts;
				error = null;
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load setup scripts';
			scripts = [];
		} finally {
			isLoading = false;
		}
	}

	async function selectScript(script: GithubSetupScript | null) {
		if (!script) {
			selectedScript = null;
			selectedScriptContent = '';
			onscriptSelected?.({ script: null, content: '' });
			isOpen = false;
			return;
		}

		const ref = githubRef;
		if (!ref) return;

		isLoadingContent = true;
		contentError = null;

		try {
			const result = await readGithubSetupScript(ref, script.name);

			if (result.error) {
				contentError = result.error;
				onerror?.({ message: result.error });
			} else {
				selectedScript = script;
				selectedScriptContent = result.content;

				// Validate content and show warnings
				const validation = validateSetupScriptContent(result.content);
				if (validation.warnings.length > 0) {
					console.warn('Setup script validation warnings:', validation.warnings);
				}

				onscriptSelected?.({ script, content: result.content });
			}
		} catch (err) {
			const errorMsg = err instanceof Error ? err.message : 'Failed to read setup script';
			contentError = errorMsg;
			onerror?.({ message: errorMsg });
		} finally {
			isLoadingContent = false;
			isOpen = false;
		}
	}

	function toggleDropdown() {
		if (shouldDisableCombobox) return;
		isOpen = !isOpen;
		if (isOpen) {
			searchTerm = '';
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			isOpen = false;
		}
	}
</script>

<svelte:window onkeydown={handleKeydown} />

<div class="relative {className}">
	<!-- Trigger Button -->
	<button
		type="button"
		onclick={toggleDropdown}
		disabled={shouldDisableCombobox}
		class="flex w-full items-center justify-between rounded-lg border border-slate-300 bg-white px-3 py-2 text-left text-sm shadow-sm transition-colors hover:border-slate-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none disabled:cursor-not-allowed disabled:bg-slate-50 disabled:text-slate-500 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-300 dark:hover:border-slate-500 dark:focus:border-blue-400 dark:disabled:bg-slate-700"
	>
		<div class="flex min-w-0 flex-1 items-center gap-2">
			<Icon src={DocumentText} class="h-4 w-4 flex-shrink-0 text-slate-400" mini />
			<span class="truncate">
				{#if isLoadingContent}
					Loading script content...
				{:else if selectedScript}
					{getSetupScriptDisplayName(selectedScript)}
				{:else if error}
					{error}
				{:else if !githubRef}
					Select repository and branch first
				{:else if isLoading}
					Loading scripts...
				{:else if scripts.length === 0}
					No setup scripts available
				{:else}
					{placeholder}
				{/if}
			</span>
		</div>
		<Icon
			src={ChevronDown}
			class="h-4 w-4 flex-shrink-0 text-slate-400 transition-transform {isOpen ? 'rotate-180' : ''}"
			mini
		/>
	</button>

	<!-- Dropdown -->
	{#if isOpen}
		<div
			class="absolute z-50 mt-1 w-full rounded-lg border border-slate-200 bg-white shadow-lg dark:border-slate-700 dark:bg-slate-800"
			transition:fly={{ y: -10, duration: 200 }}
		>
			<!-- Search -->
			{#if scripts.length > 3}
				<div class="border-b border-slate-200 p-2 dark:border-slate-700">
					<Input
						icon={MagnifyingGlass}
						type="text"
						bind:value={searchTerm}
						placeholder="Search scripts..."
						inputClass="pl-9 text-sm"
						class="w-full"
					/>
				</div>
			{/if}

			<!-- Script List -->
			<div class="max-h-60 overflow-y-auto">
				{#if isLoading}
					<div class="flex items-center gap-2 px-3 py-2 text-sm text-slate-500">
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-slate-300 border-t-slate-600"
						></div>
						Loading scripts...
					</div>
				{:else if error}
					<div class="flex items-center gap-2 px-3 py-2 text-sm text-red-600 dark:text-red-400">
						<Icon src={ExclamationTriangle} class="h-4 w-4" mini />
						{error}
					</div>
				{:else if filteredScripts.length === 0}
					<div class="px-3 py-2 text-sm text-slate-500">
						{searchTerm ? 'No scripts match your search' : 'No setup scripts found'}
					</div>
				{:else}
					<!-- Clear selection option -->
					{#if selectedScript}
						<button
							type="button"
							onclick={() => selectScript(null)}
							class="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-slate-600 hover:bg-slate-50 dark:text-slate-400 dark:hover:bg-slate-700"
						>
							<Icon src={DocumentText} class="h-4 w-4 text-slate-400" mini />
							<span class="italic">No setup script</span>
						</button>
						<div class="border-t border-slate-200 dark:border-slate-700"></div>
					{/if}

					<!-- Script options -->
					{#each filteredScripts as script}
						<button
							type="button"
							onclick={() => selectScript(script)}
							class="flex w-full items-center gap-2 px-3 py-2 text-left text-sm transition-colors hover:bg-slate-50 dark:hover:bg-slate-700 {selectedScript?.name ===
							script.name
								? 'bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
								: 'text-slate-700 dark:text-slate-300'}"
						>
							<Icon src={DocumentText} class="h-4 w-4 text-slate-400" mini />
							<div class="min-w-0 flex-1">
								<div class="truncate font-medium">
									{getSetupScriptDisplayName(script)}
								</div>
								{#if script.name !== script.displayName}
									<div class="truncate text-xs text-slate-500 dark:text-slate-400">
										{script.name}
									</div>
								{/if}
							</div>
						</button>
					{/each}
				{/if}
			</div>
		</div>
	{/if}

	<!-- Content Error -->
	{#if contentError}
		<div class="mt-1 flex items-center gap-1 text-xs text-red-600 dark:text-red-400">
			<Icon src={ExclamationTriangle} class="h-3 w-3" mini />
			{contentError}
		</div>
	{/if}
</div>
