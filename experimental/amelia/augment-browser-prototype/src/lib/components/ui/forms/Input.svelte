<script lang="ts">
	import type { IconSource } from 'svelte-hero-icons';
	import { Icon } from 'svelte-hero-icons';

	interface Props {
		id?: string;
		type?: 'text' | 'email' | 'password' | 'number' | 'search' | 'tel' | 'url';
		value?: string | number;
		placeholder?: string;
		required?: boolean;
		disabled?: boolean;
		readonly?: boolean;
		element?: HTMLInputElement;
		min?: number;
		max?: number;
		step?: number;
		maxlength?: number;
		pattern?: string;
		autocomplete?: AutoFill;
		icon?: IconSource;
		iconPosition?: 'left' | 'right';
		iconClass?: string;
		class?: string;
		inputClass?: string;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'ghost' | 'error';
		oninput?: (value: string | number) => void;
		onchange?: (value: string | number) => void;
		onkeypress?: (event: KeyboardEvent) => void;
		onkeydown?: (event: KeyboardEvent) => void;
		onkeyup?: (event: KeyboardEvent) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
		[key: string]: any;
	}

	let {
		id,
		type = 'text',
		value = $bindable(),
		placeholder,
		required = false,
		disabled = false,
		readonly = false,
		element = $bindable<HTMLInputElement | undefined>(),
		min,
		max,
		step,
		maxlength,
		pattern,
		autocomplete,
		icon,
		iconPosition = 'left',
		iconClass = '',
		class: className = '',
		inputClass = '',
		size = 'md',
		variant = 'default',
		oninput,
		onchange,
		onkeypress,
		onkeydown,
		onkeyup,
		onfocus,
		onblur,
		...restProps
	}: Props = $props();

	// Generate unique ID if not provided
	const fieldId = id || `input-${Math.random().toString(36).substring(2, 11)}`;

	// Base input styles following the existing design patterns
	const baseInputStyles = 'w-full';

	// Size styles
	const sizeStyles = {
		sm: 'px-2.5 py-1.5 text-xs',
		md: 'px-3 py-2 text-sm',
		lg: 'px-4 py-3 text-base'
	};

	// Variant styles
	const variantStyles = {
		default:
			'bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 text-slate-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors placeholder:text-slate-400 dark:placeholder:text-slate-500',
		ghost:
			'border border-transparent bg-transparent text-slate-900 dark:text-white focus:ring-0 focus:ring-offset-0 focus:outline-none focus:bg-slate-50 dark:focus:bg-slate-700',
		error: 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500'
	};

	// Disabled styles
	const disabledStyles = disabled ? 'opacity-50 cursor-not-allowed' : '';

	// Icon styles
	const iconStyles = icon ? (iconPosition === 'left' ? 'pl-7' : 'pr-10') : '';

	// Combined input class
	const inputClasses = $derived(
		[
			baseInputStyles,
			sizeStyles[size],
			variantStyles[variant],
			disabledStyles,
			iconStyles,
			inputClass
		]
			.filter(Boolean)
			.join(' ')
	);

	// Icon size based on input size
	const iconSizes = {
		sm: 'w-4 h-4',
		md: 'w-5 h-5',
		lg: 'w-6 h-6'
	};

	const iconClassLocal = $derived([iconSizes[size], iconClass].filter(Boolean).join(' '));

	// Icon positioning
	const iconPositionStyles = {
		left: {
			sm: 'left-2',
			md: 'left-2.5',
			lg: 'left-3'
		},
		right: {
			sm: 'right-2',
			md: 'right-2.5',
			lg: 'right-3'
		}
	};

	const iconPositionClass = $derived(iconPositionStyles[iconPosition][size]);

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		let newValue: string | number;

		if (type === 'number') {
			newValue = target.value === '' ? '' : Number(target.value);
		} else {
			newValue = target.value;
		}

		oninput?.(newValue);
	}

	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		let newValue: string | number;

		if (type === 'number') {
			newValue = target.value === '' ? '' : Number(target.value);
		} else {
			newValue = target.value;
		}

		onchange?.(newValue);
	}
</script>

<div class="relative {className}">
	{#if icon && iconPosition === 'left'}
		<div
			class="absolute {iconPositionClass} pointer-events-none top-1/2 -translate-y-1/2 transform text-slate-400 dark:text-slate-500"
		>
			<Icon src={icon} mini class={iconClassLocal} />
		</div>
	{/if}

	<input
		id={fieldId}
		bind:this={element}
		{type}
		bind:value
		{placeholder}
		{required}
		{disabled}
		{readonly}
		{min}
		{max}
		{step}
		{maxlength}
		{pattern}
		{autocomplete}
		class={inputClasses}
		oninput={handleInput}
		onchange={handleChange}
		{onkeypress}
		{onkeydown}
		{onkeyup}
		{onfocus}
		{onblur}
		{...restProps}
	/>

	{#if icon && iconPosition === 'right'}
		<div
			class="absolute {iconPositionClass} pointer-events-none top-1/2 -translate-y-1/2 transform text-slate-400 dark:text-slate-500"
		>
			<Icon src={icon} mini class={iconClassLocal} />
		</div>
	{/if}
</div>
