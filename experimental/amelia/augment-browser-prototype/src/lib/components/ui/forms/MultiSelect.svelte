<script lang="ts">
	import { Icon, ChevronDown, XMark } from 'svelte-hero-icons';
	import { onMount } from 'svelte';
	import FloatingElement from '../overlays/FloatingElement.svelte';

	interface Option {
		value: string;
		label: string;
	}

	interface Props {
		id?: string;
		value?: string[]; // Array of selected values
		options: Option[];
		placeholder?: string;
		disabled?: boolean;
		required?: boolean;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'error';
		class?: string;
		maxDisplayed?: number; // Max number of selected items to display before showing count
		onchange?: (value: string[]) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
	}

	let {
		id,
		value = $bindable([]),
		options = [],
		placeholder = 'Select options...',
		disabled = false,
		required = false,
		size = 'md',
		variant = 'default',
		class: className = '',
		maxDisplayed = 3,
		onchange,
		onfocus,
		onblur
	}: Props = $props();

	// Generate unique ID if not provided
	const fieldId = id || `multiselect-${Math.random().toString(36).substring(2, 11)}`;

	let isOpen = $state(false);
	let triggerElement: HTMLButtonElement;

	// Size-based classes
	const sizeClasses = {
		sm: 'px-2 py-1 text-sm min-h-[32px]',
		md: 'px-3 py-2 text-sm min-h-[40px]',
		lg: 'px-4 py-3 text-base min-h-[48px]'
	};

	const iconSizes = {
		sm: 'w-4 h-4',
		md: 'w-4 h-4',
		lg: 'w-5 h-5'
	};

	// Computed classes
	const buttonClasses = $derived(`
		w-full flex items-center justify-between gap-2 border rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 dark:focus:ring-offset-slate-800
		${sizeClasses[size]}
		${variant === 'error'
			? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-950/20'
			: 'border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 hover:border-slate-400 dark:hover:border-slate-500'
		}
		${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
		${className}
	`.trim());

	const iconClass = $derived(iconSizes[size]);

	// Get selected options
	const selectedOptions = $derived(
		options.filter(option => value.includes(option.value))
	);

	// Display text for the trigger button
	const displayText = $derived(() => {
		if (selectedOptions.length === 0) {
			return placeholder;
		}

		if (selectedOptions.length <= maxDisplayed) {
			return selectedOptions.map(opt => opt.label).join(', ');
		}

		const displayed = selectedOptions.slice(0, maxDisplayed).map(opt => opt.label).join(', ');
		const remaining = selectedOptions.length - maxDisplayed;
		return `${displayed} +${remaining} more`;
	});

	function toggleDropdown() {
		if (disabled) return;
		isOpen = !isOpen;
	}

	function toggleOption(option: Option) {
		const newValue = value.includes(option.value)
			? value.filter(v => v !== option.value)
			: [...value, option.value];

		value = newValue;
		onchange?.(newValue);
	}

	function removeOption(optionValue: string, event: Event) {
		event.stopPropagation();
		const newValue = value.filter(v => v !== optionValue);
		value = newValue;
		onchange?.(newValue);
	}

	function clearAll(event: Event) {
		event.stopPropagation();
		value = [];
		onchange?.([]);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleDropdown();
		} else if (event.key === 'Escape') {
			isOpen = false;
		}
	}

	function handleOptionKeydown(event: KeyboardEvent, option: Option) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			toggleOption(option);
		}
	}

	// Close dropdown when clicking outside
	function handleClickOutside(event: MouseEvent) {
		if (!triggerElement?.contains(event.target as Node)) {
			isOpen = false;
		}
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});
</script>

<div class="relative {className}">
	<button
		type="button"
		id={fieldId}
		bind:this={triggerElement}
		onclick={toggleDropdown}
		onkeydown={handleKeydown}
		onfocus={onfocus}
		onblur={onblur}
		class={buttonClasses}
		{disabled}
		aria-haspopup="listbox"
		aria-expanded={isOpen}
		aria-labelledby={fieldId}
	>
		<div class="flex-1 flex items-center gap-1 min-w-0">
			{#if selectedOptions.length === 0}
				<span class="text-slate-400 dark:text-slate-500 truncate">
					{placeholder}
				</span>
			{:else if selectedOptions.length <= maxDisplayed}
				<div class="flex flex-wrap gap-1 min-w-0">
					{#each selectedOptions as option}
						<span class="inline-flex items-center gap-1 px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-xs rounded-md">
							<span class="truncate">{option.label}</span>
							<button
								type="button"
								onclick={(e) => removeOption(option.value, e)}
								class="hover:bg-blue-200 dark:hover:bg-blue-800/50 rounded-sm p-0.5 transition-colors"
								aria-label="Remove {option.label}"
							>
								<Icon src={XMark} mini class="w-3 h-3" />
							</button>
						</span>
					{/each}
				</div>
			{:else}
				<span class="text-slate-900 dark:text-white truncate">
					{displayText}
				</span>
			{/if}
		</div>

		<div class="flex items-center gap-1 flex-shrink-0">
			{#if selectedOptions.length > 0}
				<button
					type="button"
					onclick={clearAll}
					class="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors p-0.5"
					aria-label="Clear all selections"
				>
					<Icon src={XMark} mini class={iconClass} />
				</button>
			{/if}
			<div class="text-slate-400 dark:text-slate-500 transition-transform duration-200 {isOpen ? 'rotate-180' : ''}">
				<Icon src={ChevronDown} mini class={iconClass} />
			</div>
		</div>
	</button>

	<!-- Floating Dropdown -->
	<FloatingElement
		show={isOpen}
		reference={triggerElement}
		placement="bottom-start"
		offset={4}
		flip={true}
		shift={true}
		autoSize={true}
		class="z-50 bg-white dark:bg-slate-800 border border-slate-300 dark:border-slate-600 rounded-md shadow-lg max-h-60 overflow-auto"
		style="min-width: {triggerElement?.offsetWidth || 200}px;"
	>
		<div role="listbox" aria-multiselectable="true">
			{#each options as option}
				{@const isSelected = value.includes(option.value)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left text-sm hover:bg-slate-50 dark:hover:bg-slate-700 focus:bg-slate-50 dark:focus:bg-slate-700 focus:outline-none flex items-center gap-2 {isSelected ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : 'text-slate-900 dark:text-white'}"
					onclick={() => toggleOption(option)}
					onkeydown={(event) => handleOptionKeydown(event, option)}
					role="option"
					aria-selected={isSelected}
				>
					<div class="w-4 h-4 border border-slate-300 dark:border-slate-600 rounded flex items-center justify-center {isSelected ? 'bg-blue-600 border-blue-600' : ''}">
						{#if isSelected}
							<div class="w-2 h-2 bg-white rounded-sm"></div>
						{/if}
					</div>
					<span class="flex-1">{option.label}</span>
				</button>
			{/each}
		</div>
	</FloatingElement>
</div>
