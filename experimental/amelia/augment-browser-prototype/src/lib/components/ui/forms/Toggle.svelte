<script lang="ts">
	interface Props {
		id?: string;
		checked?: boolean;
		disabled?: boolean;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'success' | 'warning' | 'danger';
		label?: string;
		description?: string;
		labelClass?: string;
		descriptionClass?: string;
		class?: string;
		onchange?: (checked: boolean) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
		'aria-label'?: string;
		'aria-describedby'?: string;
	}

	let {
		id,
		checked = $bindable(false),
		disabled = false,
		size = 'md',
		variant = 'default',
		label,
		description,
		labelClass,
		descriptionClass,
		class: className = '',
		onchange,
		onfocus,
		onblur,
		'aria-label': ariaLabel,
		'aria-describedby': ariaDescribedBy
	}: Props = $props();

	// Generate unique ID if not provided
	const toggleId = id || `toggle-${Math.random().toString(36).substring(2, 11)}`;
	const descriptionId = description ? `${toggleId}-description` : undefined;

	// Size configurations
	const sizeConfig = {
		sm: {
			container: 'w-8 h-4',
			thumb: 'w-3 h-3',
			translate: 'translate-x-4'
		},
		md: {
			container: 'w-11 h-6',
			thumb: 'w-5 h-5',
			translate: 'translate-x-5'
		},
		lg: {
			container: 'w-14 h-7',
			thumb: 'w-6 h-6',
			translate: 'translate-x-7'
		}
	};

	// Variant configurations
	const variantConfig = {
		default: {
			on: 'bg-blue-600 dark:bg-blue-500',
			off: 'bg-slate-200 dark:bg-slate-700'
		},
		success: {
			on: 'bg-green-600 dark:bg-green-500',
			off: 'bg-slate-200 dark:bg-slate-700'
		},
		warning: {
			on: 'bg-yellow-500 dark:bg-yellow-400',
			off: 'bg-slate-200 dark:bg-slate-700'
		},
		danger: {
			on: 'bg-red-600 dark:bg-red-500',
			off: 'bg-slate-200 dark:bg-slate-700'
		}
	};

	const config = sizeConfig[size];
	const colors = variantConfig[variant];

	// Computed classes
	const containerClasses = $derived(
		[
			'relative inline-flex shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out',
			'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900',
			config.container,
			checked ? colors.on : colors.off,
			disabled ? 'opacity-50 cursor-not-allowed' : '',
			className
		]
			.filter(Boolean)
			.join(' ')
	);

	const thumbClasses = $derived(
		[
			'pointer-events-none inline-block rounded-full bg-white shadow transform ring-0 transition duration-200 ease-in-out',
			config.thumb,
			checked ? config.translate : 'translate-x-0'
		].join(' ')
	);

	function handleChange() {
		if (disabled) return;

		checked = !checked;
		onchange?.(checked);
	}

	function handleKeydown(event: KeyboardEvent) {
		if (disabled) return;

		if (event.key === ' ' || event.key === 'Enter') {
			event.preventDefault();
			handleChange();
		}
	}
</script>

<div class="flex items-start gap-3">
	<!-- Toggle Switch -->
	<button
		{...{ id: toggleId }}
		type="button"
		role="switch"
		aria-checked={checked}
		aria-label={ariaLabel || label}
		aria-describedby={ariaDescribedBy || descriptionId}
		class={containerClasses}
		{disabled}
		onclick={handleChange}
		onkeydown={handleKeydown}
		{onfocus}
		{onblur}
	>
		<span class={thumbClasses}></span>
	</button>

	<!-- Label and Description -->
	{#if label || description}
		<div class="flex flex-col">
			{#if label}
				<label
					for={toggleId}
					class="cursor-pointer text-sm font-medium text-slate-900 dark:text-slate-100 {labelClass} {disabled
						? 'cursor-not-allowed opacity-50'
						: ''}"
				>
					{label}
				</label>
			{/if}
			{#if description}
				<p
					id={descriptionId}
					class="text-sm text-slate-500 dark:text-slate-400 {descriptionClass} {disabled
						? 'opacity-50'
						: ''}"
				>
					{description}
				</p>
			{/if}
		</div>
	{/if}
</div>
