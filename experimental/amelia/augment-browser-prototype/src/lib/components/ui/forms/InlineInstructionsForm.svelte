<script lang="ts">
	import { Icon, ArrowRight, ExclamationTriangle } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Textarea from './Textarea.svelte';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import { getDefaultInstructions } from '$lib/utils/entity-to-agent';

	interface Props {
		entity: UnifiedEntity;
		defaultInstructions?: string;
		isVisible: boolean;
		isCreating?: boolean;
		layout?: 'stacked' | 'inline';
		onSubmit: (instructions: string) => Promise<void>;
		onCancel: () => void;
		error?: string | null;
	}

	let {
		entity,
		defaultInstructions = '',
		isVisible = false,
		isCreating = false,
		layout = 'stacked',
		onSubmit,
		onCancel,
		error = null
	}: Props = $props();

	let instructions = $state('');

	// Auto-focus when form becomes visible
	$effect(() => {
		if (isVisible) {
			// Small delay to ensure the element is rendered
			setTimeout(() => {
				const textarea = document.getElementById('agent-instructions');
				textarea?.focus();
			}, 50);
		}
	});

	// Set default instructions when entity changes
	$effect(() => {
		if (entity && isVisible && !instructions.trim()) {
			instructions = defaultInstructions || getDefaultInstructions(entity);
		}
	});

	async function handleSubmit() {
		if (!instructions.trim() || isCreating) return;

		try {
			await onSubmit(instructions.trim());
		} catch (error) {
			console.error('Failed to create remote agent:', error);
		}
	}

	function handleCancel() {
		instructions = '';
		onCancel();
	}

	function handleKeydown(event: KeyboardEvent) {
		// Submit on Ctrl/Cmd + Enter
		if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
			event.preventDefault();
			handleSubmit();
		}
		// Cancel on Escape
		else if (event.key === 'Escape') {
			event.preventDefault();
			handleCancel();
		}
	}

	function useDefaultInstructions() {
		instructions = getDefaultInstructions(entity);
		const textarea = document.getElementById('agent-instructions');
		textarea?.focus();
	}

	// Parse error message to extract details if available
	function parseErrorMessage(errorMsg: string): { message: string; details?: string } {
		try {
			// Handle format: {"error":"Backend error: ","details":"{\"error\":\"Too many requests\"}"}
			if (errorMsg.includes('"details"')) {
				const parsed = JSON.parse(errorMsg);
				if (parsed.details) {
					const details = JSON.parse(parsed.details);
					return {
						message: parsed.error || 'An error occurred',
						details: details.error || details.message || parsed.details
					};
				}
			}
		} catch (e) {
			// If parsing fails, return the original message
		}
		return { message: errorMsg };
	}

	let parsedError = $derived(error ? parseErrorMessage(error) : null);
</script>

{#if isVisible}
	<div
		onclick={(e) => e.stopPropagation()}
		onkeydown={(e) => e.stopPropagation()}
		class="relative space-y-3 {layout === 'inline'
			? 'flex flex-col sm:flex-row sm:items-end sm:gap-4 sm:space-y-0'
			: ''}"
		role="form"
		aria-label="Agent assignment form"
	>
		<!-- Header -->
		<!-- <div class="flex items-center justify-between"> -->
		<!-- <div class="flex items-center gap-1.5">
				<Icon src={Sparkles} class="w-4 h-4 text-blue-600 dark:text-blue-400" mini />
				<h4 class="text-sm font-semibold text-slate-900 dark:text-white">
					Create a Remote Agent
				</h4>
			</div> -->
		<!-- </div> -->
		<!-- <button
			class="absolute top-2 right-2 p-1 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 rounded-md hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
			onclick={handleCancel}
			title="Cancel"
		>
			<Icon src={XMark} class="w-4 h-4" />
		</button> -->

		<!-- Instructions Input -->
		<div class="space-y-2 {layout === 'inline' ? 'flex-1' : ''}">
			<label
				for="agent-instructions"
				class="block text-xs font-medium text-slate-700 dark:text-slate-300"
			>
				Instructions for the agent
			</label>
			<Textarea
				id="agent-instructions"
				bind:value={instructions}
				placeholder="Describe what you'd like the remote agent to help you with..."
				rows={layout === 'inline' ? 3 : 6}
				size="sm"
				textareaClass="focus:!ring-0 focus:!border-slate-300 dark:focus:!border-slate-600"
				onkeydown={handleKeydown}
				disabled={isCreating}
			/>
		</div>

		<!-- Actions -->
		<div
			class="flex items-center gap-2 pt-0 {layout === 'inline'
				? 'flex-shrink-0 sm:w-32 sm:flex-col sm:items-stretch'
				: ''}"
		>
			<Button
				variant="outline"
				isMultiline
				onclick={handleCancel}
				disabled={isCreating}
				class="flex justify-start"
				size={layout === 'inline' ? 'sm' : 'md'}
			>
				Cancel
			</Button>

			<Button
				variant="primary"
				onclick={handleSubmit}
				disabled={!instructions.trim() || isCreating}
				class={layout === 'inline' ? '' : 'flex-1'}
				icon={ArrowRight}
				iconPosition="right"
				size={layout === 'inline' ? 'sm' : 'md'}
			>
				{#if isCreating}
					<div class="flex items-center gap-2">
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
						></div>
						{layout === 'inline' ? 'Creating...' : 'Creating agent...'}
					</div>
				{:else}
					<div class="flex items-center gap-2">
						{layout === 'inline' ? 'Assign' : 'Assign to agent'}
					</div>
				{/if}
			</Button>
		</div>

		<!-- Error Display -->
		{#if parsedError}
			<div class="rounded-lg bg-red-50 px-3 py-2 dark:bg-red-950/30">
				<div class="flex items-start gap-1.5">
					<Icon
						src={ExclamationTriangle}
						mini
						class="mt-0.5 h-4 w-4 flex-shrink-0 text-red-600 dark:text-red-400"
					/>
					<div class="min-w-0 flex-1">
						<!-- <p class="text-sm font-medium text-red-800 dark:text-red-200">
							{parsedError.message}
						</p> -->
						{#if parsedError.details}
							<p class="text-xs text-red-700 dark:text-red-300">
								{parsedError.details}
							</p>
						{/if}
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}
