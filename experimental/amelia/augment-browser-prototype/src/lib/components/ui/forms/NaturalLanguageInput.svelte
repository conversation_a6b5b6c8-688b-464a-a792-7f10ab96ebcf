<script lang="ts">
	import { I<PERSON>, <PERSON>rkles, XMark } from 'svelte-hero-icons';
	import { fly } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';

	interface Props {
		value?: string;
		placeholder?: string;
		disabled?: boolean;
		suggestions?: Array<{ value: string; label: string }>;
		onchange?: (value: string) => void;
		onsearch?: (searchTerm: string) => void;
		oninput?: (value: string) => void;
		oncancel?: () => void;
		isLoading?: boolean;
		class?: string;
	}

	let {
		value = $bindable(''),
		placeholder = 'e.g., every day at 9am, every Monday at 2pm...',
		disabled = false,
		suggestions = [],
		onchange,
		onsearch,
		oninput,
		oncancel,
		isLoading = false,
		class: className = ''
	}: Props = $props();

	let isHovered = $state(false);
	let isFocused = $state(false);
	let showSuggestions = $state(false);
	let inputRef: HTMLInputElement;

	// Handle input changes
	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		value = target.value;

		// Call input callback to notify parent of changes
		if (oninput) {
			oninput(target.value);
		}

		// Call search callback immediately for suggestions
		if (onsearch) {
			onsearch(target.value);
		}

		// Show suggestions when typing
		if (target.value.trim()) {
			showSuggestions = true;
		}

		// No automatic conversion on input - only on blur or sparkles click
	}

	// Handle focus
	function handleFocus() {
		isFocused = true;
		showSuggestions = true;
	}

	// Handle blur
	function handleBlur() {
		// Trigger conversion on blur
		if (onchange && value.trim()) {
			onchange(value);
		}

		// Delay hiding suggestions to allow for clicks
		setTimeout(() => {
			isFocused = false;
			showSuggestions = false;
		}, 150);
	}

	// Handle suggestion selection
	function selectSuggestion(suggestion: { value: string; label: string }) {
		value = suggestion.value;
		showSuggestions = false;
		inputRef?.blur();

		if (onchange) {
			onchange(suggestion.value);
		}
	}

	// Handle container click
	function handleContainerClick() {
		if (!isFocused) {
			inputRef?.focus();
		}
	}

	// Handle sparkles icon click
	function handleSparklesClick(event: Event) {
		event.stopPropagation();
		if (onchange && value.trim()) {
			onchange(value);
		}
	}

	// Handle key navigation
	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			showSuggestions = false;
			inputRef?.blur();
		} else if (event.key === 'Enter') {
			event.preventDefault();
			// Trigger conversion on Enter
			if (onchange && value.trim()) {
				onchange(value);
			}
			showSuggestions = false;
		}
	}

	// Handle cancel button click
	function handleCancel(event: Event) {
		event.stopPropagation();
		if (oncancel) {
			oncancel();
		}
	}
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div class="relative {className}">
	<!-- Input Container -->
	<div
		class="group relative cursor-text rounded-md border border-transparent px-3 py-2 transition-all duration-200 {isFocused
			? 'border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-800/50'
			: isHovered
				? 'bg-slate-25 dark:bg-slate-900/25'
				: 'bg-transparent'}"
		onmouseenter={() => (isHovered = true)}
		onmouseleave={() => (isHovered = false)}
		onclick={handleContainerClick}
	>
		<!-- Input -->
		<input
			bind:this={inputRef}
			type="text"
			bind:value
			{placeholder}
			{disabled}
			class="w-full bg-transparent text-sm text-slate-700 placeholder-slate-400 outline-none dark:text-slate-300 dark:placeholder-slate-500 {disabled
				? 'cursor-not-allowed opacity-50'
				: ''}"
			onfocus={handleFocus}
			onblur={handleBlur}
			oninput={handleInput}
			onkeydown={handleKeyDown}
		/>

		<!-- Loading/Action Icon -->
		{#if !disabled}
			{#if isLoading}
				<!-- Loading Spinner with Cancel on Hover -->
				<!-- svelte-ignore a11y_click_events_have_key_events -->
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<div
					class="group absolute top-1/2 right-2 -translate-y-1/2 cursor-pointer text-slate-400 transition-all duration-200 hover:text-red-500 dark:text-slate-500 dark:hover:text-red-400"
					onclick={handleCancel}
					title="Converting... (click to cancel)"
				>
					<div class="group-hover:hidden">
						<!-- Loading Spinner -->
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-slate-300 border-t-blue-500 dark:border-slate-600 dark:border-t-blue-400"
						></div>
					</div>
					<div class="hidden group-hover:block">
						<Icon src={XMark} class="h-4 w-4" mini />
					</div>
				</div>
			{:else if isHovered || isFocused}
				<!-- Sparkle Icon -->
				<!-- svelte-ignore a11y_click_events_have_key_events -->
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<div
					class="absolute top-1/2 right-2 -translate-y-1/2 cursor-pointer text-slate-400 transition-all duration-200 hover:text-blue-500 dark:text-slate-500 dark:hover:text-blue-400"
					onclick={handleSparklesClick}
					title="Convert to cron expression"
				>
					<Icon src={Sparkles} class="h-4 w-4" mini />
				</div>
			{/if}
		{/if}
	</div>

	<!-- Suggestions Dropdown -->
	{#if showSuggestions && suggestions.length > 0}
		<div
			class="absolute top-full right-0 left-0 z-50 mt-1 rounded-md border border-slate-200 bg-white shadow-lg dark:border-slate-700 dark:bg-slate-800"
		>
			{#each suggestions as suggestion, index}
				<!-- svelte-ignore a11y_click_events_have_key_events -->
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<div
					class="cursor-pointer px-3 py-2 text-sm text-slate-700 hover:bg-slate-50 dark:text-slate-300 dark:hover:bg-slate-700 {index ===
					suggestions.length - 1
						? ''
						: 'border-b border-slate-100 dark:border-slate-700'}"
					onclick={() => selectSuggestion(suggestion)}
					in:fly={{
						y: 20,
						duration: 300,
						delay: index * 50,
						easing: quintOut
					}}
				>
					<div class="font-medium">{suggestion.label}</div>
					{#if suggestion.value !== suggestion.label}
						<div class="mt-0.5 text-xs text-slate-500 dark:text-slate-400">
							{suggestion.value}
						</div>
					{/if}
				</div>
			{/each}
		</div>
	{/if}
</div>
