<script lang="ts">
	import { slide } from 'svelte/transition';
	import InlineInstructionsForm from '$lib/components/ui/InlineInstructionsForm.svelte';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';

	interface Props {
		entity: UnifiedEntity;
		trigger?: NormalizedTrigger;
		isVisible: boolean;
		isCreating?: boolean;
		error?: string | null;
		layout?: 'stacked' | 'inline';
		onSubmit: (instructions: string) => void;
		onCancel: () => void;
	}

	let {
		entity,
		trigger,
		isVisible,
		isCreating = false,
		error = null,
		layout = 'stacked',
		onSubmit,
		onCancel
	}: Props = $props();

</script>

{#if entity && isVisible}
	<div transition:slide={{axis: "y"}} class="pt-2">
		<InlineInstructionsForm
			{entity}
			{layout}
			{error}
			defaultInstructions={trigger?.configuration?.agent_config?.user_guidelines}
			{isVisible}
			isCreating={isCreating}
			{onSubmit}
			{onCancel}
		/>
	</div>
{/if}
