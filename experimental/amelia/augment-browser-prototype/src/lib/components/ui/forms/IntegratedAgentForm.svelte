<script lang="ts">
	import { Icon, PaperAirplane } from 'svelte-hero-icons';
	import { GitHub } from '$lib/icons/GitHubIcon.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Input from './Input.svelte';
	import Textarea from './Textarea.svelte';
	import {
		apiClient,
		ChatRequestNodeType,
		type RemoteAgentWorkspaceSetup
	} from '$lib/api/unified-client';

	function createWorkspaceSetup(repositoryUrl: string, branch: string): RemoteAgentWorkspaceSetup {
		return {
			startingFiles: {
				githubCommitRef: {
					repositoryUrl,
					gitRef: branch
				}
			}
		};
	}
	import { addToast } from '$lib/stores/toast';
	import { wrapInitialMessageWithLatestInstructions } from '$lib/types/structured-response';
	import { GitBranch } from '$lib/icons/GitBranchIcon.svelte';
	import { scale, fly } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { crossfade } from 'svelte/transition';
	import {
		generateAgentTitle,
		createTitleGenerationContext
	} from '$lib/utils/agent-title-generation';
	import { addRecentRepo, addRecentBranch } from '$lib/utils/recent-repos-storage';
	import { githubAPI, type GitHubRepo, type GitHubBranch } from '$lib/api/github-api';

	const [send, receive] = crossfade({
		duration: 600,
		easing: quintOut,
		fallback(node, params) {
			const style = getComputedStyle(node);
			const transform = style.transform === 'none' ? '' : style.transform;
			return {
				duration: 300,
				easing: quintOut,
				css: (t) => `
					transform: ${transform} scale(${0.8 + 0.2 * t});
					opacity: ${t}
				`
			};
		}
	});

	interface Props {
		class?: string;
		oncreate?: (agent?: any) => void;
	}

	let { class: className = '', oncreate }: Props = $props();

	// Form state
	let prompt = $state('');
	let repositoryUrl = $state('https://github.com/augmentcode/augment');
	let branch = $state('main');
	let isSubmitting = $state(false);
	let error = $state('');

	// Animation state
	let showAnimationCard = $state(false);
	let animationCardData = $state<any>(null);
	let formElement = $state<HTMLElement>();

	// Validation
	let isValid = $derived(
		prompt.trim().length > 0 && repositoryUrl.trim().length > 0 && branch.trim().length > 0
	);

	// Auto-focus management
	let promptTextarea: any;

	$effect(() => {
		if (promptTextarea && !isSubmitting) {
			promptTextarea.focus?.();
		}
	});

	async function handleSubmit() {
		if (!isValid || isSubmitting) return;

		isSubmitting = true;
		error = '';

		try {
			const workspaceSetup = createWorkspaceSetup(repositoryUrl.trim(), branch.trim());

			// Wrap the initial prompt with structured response instructions
			const wrappedPrompt = wrapInitialMessageWithLatestInstructions(prompt.trim());

			// Create animation card data before submitting
			const agentKey = `temp-${Date.now()}-${Math.random()}`;
			animationCardData = {
				key: agentKey,
				prompt: prompt.trim(),
				repositoryUrl: repositoryUrl.trim(),
				branch: branch.trim(),
				status: 'creating'
			};

			// Show animation card
			showAnimationCard = true;

			// Start title generation in parallel with agent creation
			const titleGenerationContext = createTitleGenerationContext(
				undefined, // no entity for direct agent creation
				undefined, // no trigger for direct agent creation
				prompt.trim(),
				{
					repositoryUrl: repositoryUrl.trim(),
					branch: branch.trim()
				}
			);

			const titlePromise = generateAgentTitle(titleGenerationContext);
			const agentPromise = apiClient.agents.create({
				initialRequestDetails: {
					requestNodes: [
						{
							id: 1,
							type: ChatRequestNodeType.TEXT,
							textNode: {
								content: wrappedPrompt
							}
						}
					]
				},
				workspaceSetup
			});

			// Wait for both operations to complete
			const [titleResult, agentResult] = await Promise.allSettled([titlePromise, agentPromise]);

			// Handle agent creation result
			if (agentResult.status === 'rejected') {
				throw agentResult.reason;
			}

			// Update agent title if generation was successful
			if (
				titleResult.status === 'fulfilled' &&
				titleResult.value &&
				agentResult.value?.remoteAgentId
			) {
				try {
					await apiClient.agents.updateTitle(agentResult.value.remoteAgentId, titleResult.value);
					console.log('Agent title updated successfully:', titleResult.value);
				} catch (titleUpdateError) {
					console.warn('Failed to update agent title:', titleUpdateError);
					// Don't fail the entire operation if title update fails
				}
			}

			// Hide animation card after a delay to let it animate
			setTimeout(() => {
				showAnimationCard = false;
				animationCardData = null;
			}, 1200);

			addToast({
				type: 'success',
				message: 'Agent created successfully',
				duration: 5000
			});

			// Track recently used repo and branch after successful creation
			try {
				// Parse the repository URL to create GitHubRepo object
				const urlMatch = repositoryUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
				if (urlMatch) {
					const [, owner, repoName] = urlMatch;
					const cleanRepoName = repoName.replace(/\.git$/, ''); // Remove .git suffix if present

					const repo: GitHubRepo = {
						owner: owner,
						name: cleanRepoName,
						html_url: repositoryUrl,
						created_at: '',
						updated_at: '',
						default_branch: 'main',
						description: undefined,
						private: false,
						fork: false,
						archived: false,
						disabled: false,
						language: undefined,
						stargazers_count: 0,
						forks_count: 0
					};

					const branchObj: GitHubBranch = {
						name: branch.trim(),
						commit: {
							sha: '',
							url: ''
						},
						protected: false
					};

					addRecentRepo(repo);
					addRecentBranch(repo, branchObj);
				}
			} catch (trackingError) {
				console.warn('Failed to track recent repo/branch:', trackingError);
			}

			// Reset form and keep focused for next agent
			prompt = '';
			oncreate?.(agentResult.value);
		} catch (err) {
			console.error('Failed to create agent:', err);
			error = err instanceof Error ? err.message : 'Failed to create agent';
		} finally {
			isSubmitting = false;
		}
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter' && (event.metaKey || event.ctrlKey)) {
			event.preventDefault();
			handleSubmit();
		}
	}
</script>

<div class={className} bind:this={formElement}>
	<form
		onsubmit={(e) => {
			e.preventDefault();
			handleSubmit();
		}}
		class=""
	>
		<!-- Repository Configuration -->
		<div class="flex items-center gap-1">
			<div class="mb-[-1px] grid flex-1 grid-cols-3">
				<Input
					id="repository"
					type="url"
					bind:value={repositoryUrl}
					icon={GitHub}
					placeholder="github.com/owner/repo"
					size="sm"
					class="col-span-2 mr-[-1px] focus-within:z-10"
					inputClass="!rounded-b-none !rounded-tr-none"
					required
				/>
				<Input
					id="branch"
					type="text"
					bind:value={branch}
					placeholder="main"
					icon={GitBranch}
					size="sm"
					class="text-xs focus-within:z-10"
					inputClass="!rounded-b-none !rounded-tl-none"
					required
				/>
			</div>
			<!-- <span class="text-slate-400">⌘↵</span> -->
		</div>

		<!-- Main Prompt Input -->
		<div class="relative">
			<Textarea
				bind:this={promptTextarea}
				id="prompt"
				bind:value={prompt}
				placeholder="What should your agent work on?"
				rows={4}
				class="w-full"
				textareaClass="pr-10 text-sm border-slate-200 dark:border-slate-700 rounded-b-md rounded-t-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-all resize-none placeholder:text-slate-400 dark:placeholder:text-slate-500"
				onkeydown={handleKeydown}
				required
			/>

			<!-- Send button inside textarea -->
			<div class="absolute right-1.5 bottom-1.5">
				<Button
					type="button"
					variant="primary"
					size="icon-sm"
					disabled={!isValid || isSubmitting}
					onclick={handleSubmit}
					class="h-6 w-6 overflow-hidden shadow-sm {isSubmitting ? 'animate-pulse' : ''}"
					aria-label="Create agent"
				>
					<Icon
						src={PaperAirplane}
						class="h-3 w-3 transform transition-transform {isSubmitting ? 'translate-x-5' : ''}"
						micro
					/>
				</Button>
			</div>
		</div>

		<!-- Error Display -->
		{#if error}
			<div class="flex items-center gap-2 rounded-md bg-red-50 p-2 dark:bg-red-900/20">
				<div class="h-1 w-1 rounded-full bg-red-500"></div>
				<p class="text-xs text-red-600 dark:text-red-400">{error}</p>
			</div>
		{/if}
	</form>

	<!-- Animation Card -->
	{#if showAnimationCard && animationCardData}
		<div
			class="pointer-events-none fixed z-50"
			style="top: {formElement?.getBoundingClientRect().top +
				60}px; left: {formElement?.getBoundingClientRect()
				.left}px; width: {formElement?.getBoundingClientRect().width}px;"
			in:scale={{ duration: 300, start: 0.8 }}
			out:fly={{
				y: -300,
				x: 50,
				duration: 800,
				easing: quintOut
			}}
		>
			<div
				class="rounded-lg border border-slate-200 bg-white p-4 shadow-lg dark:border-slate-700 dark:bg-slate-900"
			>
				<div class="flex items-center gap-3">
					<div
						class="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600"
					>
						<Icon src={PaperAirplane} class="h-4 w-4 text-white" />
					</div>
					<div class="min-w-0 flex-1">
						<h3 class="truncate text-sm font-medium text-slate-900 dark:text-white">
							{animationCardData.prompt}
						</h3>
						<div class="mt-1 flex items-center gap-2 text-xs text-slate-500">
							<Icon src={GitHub} class="h-3 w-3" />
							<span class="truncate"
								>{animationCardData.repositoryUrl.replace('https://github.com/', '')}</span
							>
							<span>•</span>
							<span>{animationCardData.branch}</span>
						</div>
					</div>
					<div class="flex items-center gap-1">
						<div class="h-2 w-2 animate-pulse rounded-full bg-blue-500"></div>
						<span class="text-xs text-slate-500">Creating...</span>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
