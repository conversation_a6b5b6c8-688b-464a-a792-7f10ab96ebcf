<script lang="ts">
	import CronExpressionBuilder from './CronExpressionBuilder.svelte';

	interface Props {
		conditions: any; // Can be array or object
	}

	let { conditions = $bindable() }: Props = $props();

	// Helper function to extract value from conditions array
	function getConditionValue(type: string, defaultValue: string = ''): string {
		if (Array.isArray(conditions)) {
			const condition = conditions.find((c) => c.type === type);
			return condition?.value || defaultValue;
		} else if (conditions && typeof conditions === 'object') {
			// Legacy object format
			return conditions[type] || defaultValue;
		}
		return defaultValue;
	}

	// Initialize schedule conditions with defaults
	let cronExpression = $state(getConditionValue('cronExpression', '0 9 * * *'));
	let timezone = $state(getConditionValue('timezone', 'UTC'));
	let description = $state(getConditionValue('description', ''));
	let startDate = $state(getConditionValue('startDate', ''));
	let endDate = $state(getConditionValue('endDate', ''));

	function handleScheduleUpdate(data: {
		cronExpression: string;
		timezone?: string;
		description?: string;
		startDate?: string;
		endDate?: string;
	}) {
		cronExpression = data.cronExpression;
		timezone = data.timezone || 'UTC';
		description = data.description || '';
		startDate = data.startDate || '';
		endDate = data.endDate || '';

		// Convert to array format expected by the rest of the system
		const newConditions = [];

		// Always include cronExpression
		newConditions.push({ type: 'cronExpression', value: cronExpression });

		// Include other fields if they have values
		if (timezone && timezone !== 'UTC') {
			newConditions.push({ type: 'timezone', value: timezone });
		}
		if (description) {
			newConditions.push({ type: 'description', value: description });
		}
		if (startDate) {
			newConditions.push({ type: 'startDate', value: startDate });
		}
		if (endDate) {
			newConditions.push({ type: 'endDate', value: endDate });
		}

		conditions = newConditions;
	}
</script>

<div class="space-y-6">
	<CronExpressionBuilder
		bind:cronExpression
		bind:timezone
		bind:description
		bind:startDate
		bind:endDate
		onUpdate={handleScheduleUpdate}
	/>
</div>
