<script lang="ts">
	import { Icon, XMark, InformationCircle, Plus, Clock } from 'svelte-hero-icons';
	import { GitHub } from '$lib/icons/GitHubIcon.svelte';
	import { Linear } from '$lib/icons/LinearIcon.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Combobox from '$lib/components/ui/forms/Combobox.svelte';

	import { slide } from 'svelte/transition';
	import ScheduleConditionsBuilder from './ScheduleConditionsBuilder.svelte';

	interface ConditionItem {
		type: string;
		value?: string;
	}

	interface Props {
		providerId: string;
		entityType: string;
		conditions: ConditionItem[];
		conditionOptions: Array<{
			value: string;
			label: string;
			description?: string;
			needsValue?: boolean;
			placeholder?: string;
			hint?: string;
			shortcut?: boolean;
			shortcutValue?: string;
			isHiddenFromConditionsBuilders?: boolean;
		}>;
	}

	let {
		providerId = $bindable(),
		entityType = $bindable(),
		conditions = $bindable(),
		conditionOptions
	}: Props = $props();

	let focusedInputIndex = $state<number | null>(null);

	let selectedCombo = $state<string>('github-pull_request');

	$effect(() => {
		const combo = `${providerId}-${entityType}`;
		if (combo !== selectedCombo) {
			selectedCombo = combo;
		}
	});

	function handleComboChange(value: string) {
		selectedCombo = value;
		const [newProviderId, newEntityType] = value.split('-');
		providerId = newProviderId;
		entityType = newEntityType as 'pull_request' | 'workflow_run' | 'issue' | 'schedule';
	}

	function handleConditionsChange(newConditions: ConditionItem[]) {
		conditions = newConditions;
	}

	function handleFocusedInputChange(index: number | null) {
		focusedInputIndex = index;
	}

	function addAnotherCondition() {
		handleConditionsChange([...conditions, { type: '', value: undefined }]);
		setTimeout(() => {
			const inputs = document.querySelectorAll(
				'input[placeholder*="Select condition"], input[placeholder*="Enter value"]'
			);
			const targetInput = inputs[inputs.length - 1] as HTMLInputElement; // Last condition input
			if (targetInput) {
				targetInput.focus();
			}
		}, 100);
	}

	function updateCondition(index: number, type: string, option?: any) {
		const newConditions = [...conditions];

		// Check if this is a shortcut condition
		if (option?.shortcut && option?.shortcutValue) {
			// For shortcuts, use the actual field type and set the shortcut value
			newConditions[index] = { type: option.value, value: option.shortcutValue };
		} else {
			// For regular conditions, just set the type
			newConditions[index] = { ...newConditions[index], type };
		}

		handleConditionsChange(newConditions);
	}

	function updateConditionValue(index: number, value: string) {
		const newConditions = [...conditions];
		newConditions[index] = { ...newConditions[index], value };
		handleConditionsChange(newConditions);
	}

	function removeCondition(index: number) {
		const newConditions = conditions.filter((_, i) => i !== index);
		handleConditionsChange(newConditions);
	}

	function handleConditionBlur(e: FocusEvent, index: number) {
		setTimeout(() => {
			const relatedTarget = e.relatedTarget as HTMLElement;
			const isClickingAddButton = relatedTarget?.textContent?.includes('add another');

			// Check if this condition is empty (no selection and no value)
			const condition = conditions[index];
			const hasCondition = condition?.type && condition.type !== '';
			const hasValue = condition?.value && condition.value !== '';

			if (!hasCondition && !hasValue && !isClickingAddButton) {
				removeCondition(index);
			}
		}, 150);
	}

	function handleValueBlur(e: FocusEvent, index: number) {
		handleFocusedInputChange(null);

		setTimeout(() => {
			const relatedTarget = e.relatedTarget as HTMLElement;
			const isClickingAddButton = relatedTarget?.textContent?.includes('add another');

			// Check if this condition is empty (no selection and no value)
			const condition = conditions[index];
			const hasCondition = condition?.type && condition.type !== '';
			const hasValue = condition?.value && condition.value !== '';

			if (!hasCondition && !hasValue && !isClickingAddButton) {
				removeCondition(index);
			}
		}, 150);
	}
</script>

<!-- Statement-like Configuration -->
<div class="space-y-4 px-12 py-8 pt-10">
	<div
		class="flex flex-wrap items-center text-lg leading-relaxed font-medium text-slate-600 dark:text-white"
	>
		Create agents {providerId === 'schedule' ? 'on a ' : 'to help me with'}
		<Combobox
			variant="underline"
			options={[
				{
					value: 'github-pull_request',
					label: 'GitHub Pull Requests',
					description: 'Code reviews & PRs',
					icon: GitHub
				},
				{
					value: 'github-workflow_run',
					label: 'GitHub Workflow Runs',
					description: 'CI/CD & automation',
					icon: GitHub
				},
				{
					value: 'linear-issue',
					label: 'Linear Issues',
					description: 'Linear issue tracking',
					icon: Linear
				},
				{
					value: 'schedule-schedule',
					label: 'Schedule',
					description: 'Kick agents off at scheduled times',
					class: 'border-t border-slate-200 dark:border-slate-700',
					icon: Clock
				}
			]}
			value={selectedCombo}
			class="mx-2 mb-[-0.4em] inline-block w-64"
			dropdownClass="w-32"
			placeholder="Select entity type..."
			onchange={handleComboChange}
		/>
		{#if providerId !== 'schedule'}
			<!-- Interactive trailing text for first condition -->
			{#if !conditions.some((condition) => !conditionOptions.find((opt) => opt.value === condition.type)?.isHiddenFromConditionsBuilders)}
				<!-- that are...  -->
			{:else}
				that are
			{/if}

			<!-- Additional conditions -->
			{#each conditions.filter((condition) => !conditionOptions.find((opt) => opt.value === condition.type)?.isHiddenFromConditionsBuilders) as conditionItem, index}
				{@const conditionOption = conditionOptions.find((opt) => opt.value === conditionItem.type)}
				{@const baseConditionOption = conditionOptions.find(
					(opt) => opt.value === conditionItem.type && !opt.shortcut
				)}
				{@const displayLabel = baseConditionOption?.label}
				<div class="relative my-0.5 inline-flex" transition:slide={{ axis: 'x', duration: 200 }}>
					<div class="flex items-center">
						{#if index > 0}
							<span class="text-lg font-medium text-slate-600 dark:text-white">and</span>
						{/if}
						<div class="relative mb-[-0.4em] ml-2 inline-block">
							<!-- Regular condition UI -->
							<div class="inline-flex items-center focus-within:border-transparent">
								<Combobox
									options={conditionOptions.filter((opt) => opt.value !== 'activity_types')}
									bind:value={conditionItem.type}
									displayValue={displayLabel}
									variant="underline"
									containerClass="!w-40"
									dropdownClass="w-32"
									class="m-[-1px] rounded-l-md border-0 bg-transparent focus:ring-0"
									placeholder="Select condition..."
									onchange={(value, option) => {
										updateCondition(index, value, option);
										// Auto-focus the value input if this condition needs a value
										if (option.needsValue) {
											setTimeout(() => {
												const conditionContainer = document.querySelectorAll(
													'.inline-flex.items-center.bg-gray-100'
												)[index];
												const valueInput = conditionContainer?.querySelector(
													'input[type="text"]'
												) as HTMLInputElement;
												if (valueInput) {
													valueInput.focus();
												}
											}, 100);
										}
									}}
									onblur={(e) => handleConditionBlur(e, index)}
								/>
								{#if conditionItem.type}
									<input
										type="text"
										data-is-last-empty-combobox={index === conditions.length - 1 &&
											conditionItem.type === ''}
										bind:value={conditionItem.value}
										placeholder={conditionOptions.find((opt) => opt.value === conditionItem.type)
											?.placeholder || 'Enter value...'}
										class="m-[-1px] mt-[-0.3em] inline-flex items-center border-0 border-b border-slate-400/60 bg-transparent p-0 py-[0.5px] text-lg transition-all duration-200 focus:border-slate-500/80 focus:ring-0 focus:outline-0 dark:border-slate-500/60 dark:focus:border-slate-400/80"
										onfocus={() => handleFocusedInputChange(index)}
										onblur={(e) => {
											handleValueBlur(e, index);
											handleConditionBlur(e, index);
										}}
										oninput={(e) =>
											updateConditionValue(index, (e.target as HTMLInputElement).value)}
									/>
								{/if}
							</div>
						</div>
						<Button
							variant="ghost"
							icon={XMark}
							color="gray"
							size="icon-md"
							class="z-0 -ml-1 translate-y-0.5 transform !text-slate-400"
							tabindex="-1"
							onclick={() => removeCondition(index)}
						></Button>
					</div>

					<!-- Help text for focused input -->
					{#if focusedInputIndex === index && conditionOption?.hint}
						<div class="absolute right-0 bottom-0 left-0 z-50 mt-0 translate-y-full transform">
							<div
								class="rounded-lg border border-slate-200/60 bg-white px-3 py-2 shadow-xl backdrop-blur-sm dark:border-slate-700/60 dark:bg-slate-900"
							>
								<div class="flex items-start gap-2">
									<div class="flex-shrink-0">
										<Icon src={InformationCircle} class="h-5 w-5 text-slate-500" mini />
									</div>
									<div class="min-w-0 flex-1">
										<div
											class="text-sm leading-normal font-normal text-slate-700 dark:text-slate-300"
										>
											{conditionOption.hint}
										</div>
										{#if conditionOption.placeholder}
											<div
												class="mt-2 rounded border bg-slate-100 px-2 py-1 font-mono text-xs text-slate-600 dark:bg-slate-900/30 dark:text-slate-400"
											>
												Example: {conditionOption.placeholder}
											</div>
										{/if}
									</div>
								</div>
							</div>
						</div>
					{/if}
				</div>
			{/each}

			{#if providerId !== 'schedule'}
				<!-- Interactive trailing text for additional conditions -->
				<div class="-my-1 inline-flex items-center">
					<!-- {#if conditions.length > 0}
				and
			{/if} -->
					<Button
						variant="ghost-light"
						type="button"
						class=""
						size="lg"
						icon={Plus}
						onmousedown={(e: MouseEvent) => {
							// Prevent blur from happening when clicking this button
							e.preventDefault();
							addAnotherCondition();
						}}
						onfocus={(_e: FocusEvent) => {
							// Check if we're coming from a condition input
							const lastCondition = conditions[conditions.length - 1];
							const doesLastConditionHaveValue = lastCondition?.type && lastCondition.type !== '';
							console.log(
								'does last condition have value',
								doesLastConditionHaveValue,
								lastCondition,
								conditions.length - 1,
								conditions
							);

							// if (doesLastConditionHaveValue) {
							addAnotherCondition();
							// }
						}}
					>
						{#if !conditions.some((condition) => !conditionOptions.find((opt) => opt.value === condition.type)?.isHiddenFromConditionsBuilders)}
							<span class="">add condition</span>
						{:else}
							add another condition
						{/if}
					</Button>
				</div>
			{/if}
		{/if}
	</div>

	{#if providerId === 'schedule'}
		<div class="mt-9 w-full">
			<ScheduleConditionsBuilder bind:conditions />
		</div>
	{/if}
</div>
