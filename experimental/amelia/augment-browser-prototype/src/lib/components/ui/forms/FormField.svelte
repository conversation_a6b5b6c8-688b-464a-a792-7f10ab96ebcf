<script lang="ts">
	import { Icon, InformationCircle, type IconSource } from 'svelte-hero-icons';
	import SelectList from '$lib/components/ui/forms/Select.svelte';
	import MultiSelect from '$lib/components/ui/forms/MultiSelect.svelte';
	import Slider from '$lib/components/ui/forms/Slider.svelte';
	import Toggle from '$lib/components/ui/forms/Toggle.svelte';
	import Input from '$lib/components/ui/forms/Input.svelte';
	import Textarea from '$lib/components/ui/forms/Textarea.svelte';
	import type { Snippet } from 'svelte';

	interface Props {
		id?: string;
		labelText?: string;
		label?: Snippet;
		type?:
			| 'text'
			| 'datetime'
			| 'date'
			| 'time'
			| 'datetime-local'
			| 'textarea'
			| 'select'
			| 'multiselect'
			| 'checkbox'
			| 'boolean'
			| 'toggle'
			| 'number'
			| 'email'
			| 'password'
			| 'slider';
		value?: string | number | boolean | string[];
		placeholder?: string;
		required?: boolean;
		disabled?: boolean;
		error?: string;
		hint?: string;
		options?: string[] | { value: string; label: string }[]; // For select type
		rows?: number; // For textarea
		min?: number; // For number and slider types
		max?: number; // For number and slider types
		step?: number; // For number and slider types
		icon?: IconSource;
		size?: 'xs' | 'sm' | 'md' | 'lg';
		autoResize?: boolean;
		class?: string;
		inputClass?: string;
		onchange?: (value: string | number | boolean | string[]) => void;
		oninput?: (value: string | number | boolean | string[]) => void;
		onkeypress?: (event: KeyboardEvent) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
	}

	let {
		id,
		label,
		labelText = '',
		type = 'text',
		value = $bindable(),
		placeholder,
		required = false,
		disabled = false,
		error,
		hint,
		options = [],
		rows = 3,
		min,
		max,
		step,
		icon,
		size = 'md',
		class: className = '',
		inputClass = '',
		autoResize = false,
		onchange,
		oninput,
		onkeypress,
		onfocus,
		onblur
	}: Props = $props();

	// Generate unique ID if not provided
	const fieldId = id || `field-${Math.random().toString(36).substring(2, 11)}`;

	// Computed values for different input types
	let multiselectValue = $derived.by(() => {
		if (type === 'multiselect') {
			return Array.isArray(value) ? value : [];
		}
		return [];
	});

	// Handle toggle change
	function handleToggleChange(newValue: boolean) {
		value = newValue;
		onchange?.(newValue);
	}

	// Normalize options to consistent format
	const normalizedOptions = $derived.by(() => {
		return options.map((option) =>
			typeof option === 'string' ? { value: option, label: option } : option
		);
	});

	// Type-safe value for checkbox
	let checkboxValue = $state(false);

	// Type-safe value for toggle
	let toggleValue = $state(false);

	// Type-safe value for slider
	let sliderValue = $state(0);

	// Sync checkbox value with main value
	$effect(() => {
		if (type === 'checkbox' || type === 'boolean') {
			checkboxValue = Boolean(value);
		} else if (type === 'toggle') {
			toggleValue = Boolean(value);
		} else if (type === 'slider') {
			sliderValue = typeof value === 'number' ? value : Number(value) || min || 0;
		}
	});

	// Sync slider value back to main value
	$effect(() => {
		if (type === 'slider' && sliderValue !== value) {
			value = sliderValue;
		}
	});

	// Sync toggle value back to main value
	$effect(() => {
		if (type === 'toggle' && toggleValue !== value) {
			value = toggleValue;
		}
	});

	// Handle slider input/change events
	function handleSliderInput(newValue: number) {
		// Don't manually set value here since the Slider component should handle it
		oninput?.(newValue);
	}

	function handleSliderChange(newValue: number) {
		// Don't manually set value here since the Slider component should handle it
		onchange?.(newValue);
	}
</script>

<div class="space-y-1 {className}">
	{#if label}
		{@render label()}
	{:else if labelText}
		<label for={fieldId} class="block text-sm font-medium text-slate-900 dark:text-slate-100">
			<!-- <Markdown content={labelText} size="sm" hasNoPadding /> -->
			{labelText}
			<!-- {#if required}
				<span class="text-red-500 ml-1">*</span>
			{/if} -->
		</label>
	{/if}

	{#if type === 'textarea'}
		<Textarea
			id={fieldId}
			value={String(value || '')}
			{placeholder}
			{disabled}
			{required}
			{rows}
			{size}
			{autoResize}
			textareaClass={inputClass}
			variant={error ? 'error' : 'default'}
			oninput={(newValue) => {
				value = newValue;
				oninput?.(newValue);
			}}
			onchange={(newValue) => {
				value = newValue;
				onchange?.(newValue);
			}}
			{onkeypress}
			{onfocus}
			{onblur}
		/>
	{:else if type === 'select'}
		<SelectList
			id={fieldId}
			bind:value
			options={normalizedOptions}
			{placeholder}
			{disabled}
			{required}
			onchange={(newValue) => {
				// Don't manually set value here since bind:value handles it
				onchange?.(newValue);
			}}
			{onfocus}
			{onblur}
		/>
	{:else if type === 'multiselect'}
		<MultiSelect
			id={fieldId}
			value={multiselectValue}
			options={normalizedOptions}
			{placeholder}
			{disabled}
			{required}
			onchange={(newValue) => {
				value = newValue;
				onchange?.(newValue);
			}}
			{onfocus}
			{onblur}
		/>
	{:else if type === 'toggle'}
		<Toggle
			id={fieldId}
			bind:checked={toggleValue}
			{disabled}
			label={placeholder}
			onchange={handleToggleChange}
			{onfocus}
			{onblur}
		/>
	{:else if type === 'checkbox' || type === 'boolean'}
		<Toggle
			id={fieldId}
			bind:checked={checkboxValue}
			{disabled}
			label={placeholder}
			onchange={handleToggleChange}
			{onfocus}
			{onblur}
		/>
	{:else if type === 'slider'}
		<Slider
			id={fieldId}
			bind:value={sliderValue}
			{min}
			{max}
			{step}
			{disabled}
			oninput={handleSliderInput}
			onchange={handleSliderChange}
		/>
	{:else}
		<Input
			id={fieldId}
			type={type as 'text' | 'number' | 'email' | 'password'}
			value={typeof value === 'boolean' ? '' : Array.isArray(value) ? '' : value}
			{placeholder}
			{disabled}
			{required}
			{min}
			{max}
			{size}
			{step}
			{icon}
			{inputClass}
			variant={error ? 'error' : 'default'}
			oninput={(newValue) => {
				value = newValue;
				oninput?.(newValue);
			}}
			onchange={(newValue) => {
				value = newValue;
				onchange?.(newValue);
			}}
			{onkeypress}
			{onfocus}
			{onblur}
		/>
	{/if}

	{#if error}
		<p class="text-sm text-red-600 dark:text-red-400">{error}</p>
	{:else if hint}
		<p class="gap-1 text-xs text-slate-500 dark:text-slate-400">
			<Icon src={InformationCircle} micro class="-mt-0.5 inline-block h-3 w-3 opacity-50" />
			{hint}
		</p>
	{/if}
</div>
