<script lang="ts">
	import { Icon, Clock, CheckCircle, ExclamationTriangle } from 'svelte-hero-icons';
	import <PERSON><PERSON>ield from './FormField.svelte';

	import CronInput from './CronInput.svelte';
	import NaturalLanguageInput from './NaturalLanguageInput.svelte';
	import { slide } from 'svelte/transition';
	import cronstrue from 'cronstrue';
	import { convertDescriptionToCron, getCronSuggestions } from '$lib/utils/cron-conversion';
	import Button from '../navigation/Button.svelte';

	interface Props {
		enabled?: boolean;
		cronExpression?: string;
		timezone?: string;
		startDate?: string;
		endDate?: string;
		description?: string;
		naturalLanguageInput?: string;
		onToggle: (enabled: boolean) => void;
		disabled?: boolean;
	}

	let {
		enabled = $bindable(false),
		cronExpression = $bindable('0 9 * * *'),
		timezone = $bindable('UTC'),
		startDate = $bindable(''),
		endDate = $bindable(''),
		description = $bindable(''),
		naturalLanguageInput = $bindable(''),
		onToggle,
		disabled = false
	}: Props = $props();

	// Internal state
	let isConverting = $state(false);
	let conversionError = $state('');
	let showAdvanced = $state(false);
	let currentAbortController: AbortController | null = null;

	// Caching for natural language conversions
	const conversionCache = new Map<string, { cronExpression: string; description: string }>();
	const MAX_CACHE_SIZE = 50; // Limit cache size to prevent memory issues
	let lastConvertedInput = $state('');

	// No debouncing needed - only update state on actual selection

	// Create a mapping between natural language and cron expressions
	const cronPresetMap = new Map([
		['every day at 9am', '0 9 * * *'],
		['every weekday at 8:30am', '30 8 * * 1-5'],
		['every Monday at 2pm', '0 14 * * 1'],
		['every hour', '0 * * * *'],
		['every 30 minutes', '*/30 * * * *'],
		['daily at midnight', '0 0 * * *'],
		['weekly on Friday at 5pm', '0 17 * * 5'],
		['every 15 minutes during business hours', '*/15 9-17 * * 1-5']
	]);

	// Reverse mapping for cron to natural language
	const naturalLanguageMap = new Map(
		Array.from(cronPresetMap.entries()).map(([nl, cron]) => [cron, nl])
	);

	// Natural language options for the combobox

	let naturalLanguageOptions = $derived(
		getCronSuggestions().map((suggestion) => ({
			value: suggestion,
			label: suggestion
		}))
	);

	let localStartDate = $state(convertFromISO8601(startDate) || '');
	let localEndDate = $state(convertFromISO8601(endDate) || '');

	$effect(() => {
		localStartDate = convertFromISO8601(startDate) || '';
		localEndDate = convertFromISO8601(endDate) || '';
	});

	// Initialize natural language input when component loads or cron expression changes
	$effect(() => {
		if (cronExpression && !naturalLanguageInput) {
			updateDescriptionAndNaturalLanguageFromCron(cronExpression);
		}
	});

	// Convert ISO 8601 to datetime-local format for display
	function convertFromISO8601(iso8601: string | undefined): string | undefined {
		if (!iso8601) return undefined;

		try {
			// Parse the ISO date and format it for datetime-local
			const date = new Date(iso8601);
			if (isNaN(date.getTime())) {
				return undefined;
			}

			// Format as YYYY-MM-DDTHH:MM (datetime-local format)
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');

			return `${year}-${month}-${day}T${hours}:${minutes}`;
		} catch (error) {
			return undefined;
		}
	}

	// Validation available via validateCronExpression(cronExpression) if needed

	// Human readable cron description
	let readableCron = $derived.by(() => {
		try {
			return cronstrue.toString(cronExpression);
		} catch {
			return 'Invalid cron expression';
		}
	});

	function handleToggle() {
		enabled = !enabled;
		onToggle(enabled);

		// Reset state when disabling
		if (!enabled) {
			naturalLanguageInput = '';
			conversionError = '';
			showAdvanced = false;
		}
	}

	// Simple update function for when values are actually selected

	// Update description and natural language from cron expression (for onchange events)
	function updateDescriptionAndNaturalLanguageFromCron(cronExpr: string) {
		try {
			description = cronstrue.toString(cronExpr);
			conversionError = '';

			// Update natural language if we have a preset mapping
			const naturalLanguage = naturalLanguageMap.get(cronExpr);
			if (naturalLanguage) {
				naturalLanguageInput = naturalLanguage;
			} else {
				naturalLanguageInput = description;
			}
		} catch (error) {
			description = 'Invalid cron expression';
			conversionError = 'Invalid cron expression format';
		}
	}

	// Check if natural language input has a preset cron, otherwise use API
	async function handleNaturalLanguageChange(nlInput: string) {
		const presetCron = cronPresetMap.get(nlInput.toLowerCase());

		if (presetCron) {
			// Use preset mapping - no API call needed
			cronExpression = presetCron;
			updateDescriptionAndNaturalLanguageFromCron(presetCron);
		} else {
			// Use API for custom natural language
			await handleConvertDescription(nlInput);
		}
	}

	async function handleConvertDescription(inputText?: string) {
		const textToConvert = inputText || naturalLanguageInput.trim();
		if (!textToConvert) return;

		// Prevent duplicate requests for the same input
		if (textToConvert === lastConvertedInput) {
			return;
		}

		// Check cache first
		const normalizedInput = textToConvert.toLowerCase().trim();
		const cachedResult = conversionCache.get(normalizedInput);
		if (cachedResult) {
			cronExpression = cachedResult.cronExpression;
			description = cachedResult.description;
			// Use cronstrue to generate a clean natural language description
			try {
				naturalLanguageInput = cronstrue.toString(cachedResult.cronExpression);
			} catch {
				naturalLanguageInput = cachedResult.description;
			}
			lastConvertedInput = textToConvert;
			return;
		}

		// Cancel any existing request
		if (currentAbortController) {
			currentAbortController.abort();
		}

		// Create new abort controller
		currentAbortController = new AbortController();
		const abortController = currentAbortController;

		isConverting = true;
		conversionError = '';
		lastConvertedInput = textToConvert;

		try {
			const result = await convertDescriptionToCron(textToConvert, abortController.signal);

			// Check if this request was cancelled
			if (abortController.signal.aborted) {
				return;
			}

			if (result.success && result.cronExpression) {
				cronExpression = result.cronExpression;
				// Use cronstrue to generate a clean natural language description
				try {
					naturalLanguageInput = cronstrue.toString(result.cronExpression);
				} catch {
					naturalLanguageInput = result.description || readableCron;
				}
				description = result.description || readableCron;

				// Cache the successful result
				conversionCache.set(normalizedInput, {
					cronExpression: result.cronExpression,
					description: result.description || readableCron
				});

				// Limit cache size (simple LRU: remove oldest entries)
				if (conversionCache.size > MAX_CACHE_SIZE) {
					const firstKey = conversionCache.keys().next().value;
					if (firstKey) {
						conversionCache.delete(firstKey);
					}
				}
			} else {
				conversionError = result.error || 'Failed to convert description to cron expression';
			}
		} catch (error) {
			// Don't show error if request was cancelled
			if (abortController.signal.aborted) {
				return;
			}
			console.error('Cron conversion error:', error);
			conversionError = error instanceof Error ? error.message : 'Conversion failed';
		} finally {
			// Only clear loading if this is still the current request
			if (currentAbortController === abortController) {
				isConverting = false;
				currentAbortController = null;
			}
		}
	}

	// Handle cancellation
	function handleCancel() {
		if (currentAbortController) {
			currentAbortController.abort();
			currentAbortController = null;
			isConverting = false;
			conversionError = '';
		}
	}

	// Common timezone options
	const timezoneOptions = [
		{ value: 'UTC', label: 'UTC' },
		{ value: 'America/New_York', label: 'Eastern Time' },
		{ value: 'America/Chicago', label: 'Central Time' },
		{ value: 'America/Denver', label: 'Mountain Time' },
		{ value: 'America/Los_Angeles', label: 'Pacific Time' },
		{ value: 'Europe/London', label: 'London' },
		{ value: 'Europe/Paris', label: 'Paris' },
		{ value: 'Asia/Tokyo', label: 'Tokyo' },
		{ value: 'Asia/Shanghai', label: 'Shanghai' }
	];
</script>

<div class="space-y-4">
	<!-- Toggle Section -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-2.5">
			<Icon src={Clock} class="h-6 w-6 text-slate-400 dark:text-slate-500" mini />
			<div>
				<div class="text-sm font-medium text-slate-700 dark:text-slate-300">
					Schedule this agent
				</div>
				<div class="text-xs text-slate-500 dark:text-slate-400">
					Run this agent automatically on a schedule
				</div>
			</div>
		</div>

		<button
			type="button"
			onclick={handleToggle}
			{disabled}
			aria-label={enabled ? 'Disable scheduling' : 'Enable scheduling'}
			class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 {enabled
				? 'bg-blue-600'
				: 'bg-slate-200 dark:bg-slate-700'}"
		>
			<span
				class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {enabled
					? 'translate-x-5'
					: 'translate-x-0'}"
			></span>
		</button>
	</div>

	<!-- Expanded Content -->
	{#if enabled}
		<div transition:slide={{ duration: 300 }} class="space-y-4 pl-8">
			<!-- Dual Input Layout -->
			<div class="space-y-4">
				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<!-- Cron Expression Input -->
					<div class="space-y-2">
						<CronInput
							bind:value={cronExpression}
							disabled={isConverting}
							{isConverting}
							onchange={() => {
								updateDescriptionAndNaturalLanguageFromCron(cronExpression);
							}}
							onkeydown={(event) => {
								if (event.key === 'Enter') {
									// Convert current cron expression to natural language
									updateDescriptionAndNaturalLanguageFromCron(cronExpression);
								}
							}}
						/>
					</div>

					<!-- Natural Language Input -->
					<div class="space-y-2">
						<div class="block text-sm font-medium text-slate-700 dark:text-slate-300">
							Natural Language
						</div>

						<NaturalLanguageInput
							bind:value={naturalLanguageInput}
							placeholder="e.g., every day at 9am, every Monday at 2pm..."
							disabled={false}
							isLoading={isConverting}
							suggestions={naturalLanguageOptions}
							onchange={(value) => {
								naturalLanguageInput = value;
								handleNaturalLanguageChange(value);
							}}
							oninput={(value) => {
								// Reset last converted input when user types
								if (value !== lastConvertedInput) {
									lastConvertedInput = '';
								}
							}}
							onsearch={(searchTerm) => {
								// Handle search for suggestions if needed
								console.log('Searching for:', searchTerm);
							}}
							oncancel={handleCancel}
						/>
					</div>
				</div>

				<!-- Conversion Error -->
				{#if conversionError}
					<div class="flex items-start gap-2 rounded-md bg-red-50 p-2 dark:bg-red-900/20">
						<Icon src={ExclamationTriangle} class="mt-0.5 h-4 w-4 text-red-500" mini />
						<span class="text-sm text-red-700 dark:text-red-400">{conversionError}</span>
					</div>
				{/if}
			</div>

			<!-- Show Advanced Options Button -->
			<div class="flex justify-end">
				<Button onclick={() => (showAdvanced = !showAdvanced)} variant="ghost" size="xs">
					{showAdvanced ? 'Hide' : 'Show'} timezone & time range options
				</Button>
			</div>

			<!-- Advanced Options -->
			{#if showAdvanced}
				<div transition:slide={{ duration: 200 }} class="space-y-3">
					<div class="flex flex-wrap gap-3">
						<!-- Timezone -->
						<FormField
							labelText="Timezone"
							type="select"
							class="min-w-[10em] flex-1"
							bind:value={timezone}
							options={timezoneOptions}
						/>

						<!-- Manual Cron Expression -->
						<FormField
							type="datetime-local"
							labelText="Start Date (Optional)"
							class="min-w-[10em] flex-1"
							bind:value={localStartDate}
							placeholder="YYYY-MM-DDTHH:MM"
							hint="When to start executing this schedule"
						/>
						<FormField
							type="datetime-local"
							labelText="End Date (Optional)"
							class="min-w-[10em] flex-1"
							bind:value={localEndDate}
							placeholder="YYYY-MM-DDTHH:MM"
							hint="When to stop executing this schedule"
						/>
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
