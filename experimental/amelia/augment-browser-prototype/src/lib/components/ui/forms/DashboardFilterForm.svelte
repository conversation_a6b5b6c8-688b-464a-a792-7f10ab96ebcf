<script lang="ts">
	import FormField from './FormField.svelte';
	import { getFilterFieldsForEntity, formatEntityTypeDisplayName } from '$lib/utils/dashboard-filter-forms';
	import type { ConditionFilters } from '$lib/utils/condition-filters';

	interface Props {
		provider: string;
		entityType: string;
		conditionFilters: ConditionFilters;
		onUpdate: (filters: ConditionFilters) => void;
	}

	let { provider, entityType, conditionFilters, onUpdate }: Props = $props();

	// Get field configurations for the current provider and entity type
	let filterFields = $derived(getFilterFieldsForEntity(provider, entityType));
	let displayName = $derived(formatEntityTypeDisplayName(entityType));

	function handleFieldUpdate() {
		onUpdate(conditionFilters);
	}
</script>

{#if filterFields.length > 0}
	<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
		{#each filterFields as field}
			<FormField
				labelText={field.label}
				type={field.type}
				bind:value={conditionFilters[field.key]}
				placeholder={field.placeholder}
				options={field.options}
				size="sm"
				oninput={handleFieldUpdate}
				onchange={handleFieldUpdate}
			/>
		{/each}
	</div>
{:else}
	<div class="text-sm text-slate-500 dark:text-slate-400 mt-3">
		No filters available for {displayName}
	</div>
{/if}
