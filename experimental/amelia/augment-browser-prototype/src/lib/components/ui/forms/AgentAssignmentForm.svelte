<script lang="ts">
	import { Icon, ArrowDown, ArrowR<PERSON>, ArrowUp } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import InlineInstructionsForm from './InlineInstructionsForm.svelte';
	import { createRemoteAgentManually } from '$lib/utils/dashboard-entity-operations';
	import { slide } from 'svelte/transition';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';

	interface Props {
		entity: UnifiedEntity;
		trigger?: NormalizedTrigger;
		remoteAgent?: CleanRemoteAgent;
		layout?: 'stacked' | 'inline';
		showActions?: boolean;
		isAnimating?: boolean;
		onStartWork?: (entity: UnifiedEntity) => void;
		onViewWorkflow?: (agentId: string) => void;
		onAgentClick?: (agent: CleanRemoteAgent) => void;
	}

	let {
		entity,
		trigger,
		remoteAgent,
		layout = 'stacked',
		showActions = true,
		isAnimating = false,
		onStartWork,
		onViewWorkflow,
		onAgentClick
	}: Props = $props();

	// State for inline instructions form
	let showInstructionsForm = $state(false);
	let isCreatingAgent = $state(false);
	let error = $state<string | null>(null);

	function handleStartWork(e?: MouseEvent) {
		if (e) e.stopPropagation();

		// Clear any previous errors
		error = null;

		// Show inline instructions form instead of calling onStartWork directly
		if (entity) {
			showInstructionsForm = true;
		}
	}

	async function handleCreateAgent(instructions: string) {
		if (!entity) return;

		try {
			isCreatingAgent = true;
			error = null;

			console.log('Creating remote agent manually for entity:', { entity, instructions });
			const result = await createRemoteAgentManually(entity, trigger, instructions);

			// Hide the form and reset state
			showInstructionsForm = false;

			// Optionally call the original onStartWork callback if provided
			if (onStartWork) {
				onStartWork(entity);
			}

			console.log('Remote agent created:', result.agentId);
		} catch (err) {
			console.error('Failed to create remote agent:', err);

			// Parse error message for better display
			let errorMessage = 'Failed to create remote agent';
			if (err instanceof Error) {
				errorMessage = err.message;
			} else if (typeof err === 'string') {
				errorMessage = err;
			}

			error = errorMessage;
		} finally {
			isCreatingAgent = false;
		}
	}

	function handleCancelInstructions() {
		showInstructionsForm = false;
		isCreatingAgent = false;
		error = null;
	}

	function handleViewWorkflow(e?: MouseEvent) {
		if (e) e.stopPropagation();
		if (remoteAgent && onViewWorkflow) {
			onViewWorkflow(remoteAgent.id);
		}
	}

	function handleAgentClick(e?: MouseEvent) {
		if (e) e.stopPropagation();
		if (remoteAgent && onAgentClick) {
			onAgentClick(remoteAgent);
		}
	}
</script>

{#if showActions}
	{#if !showInstructionsForm}
		<div class="flex items-center justify-between" transition:slide={{ axis: 'y' }}>
			<!-- Action Button -->
			<div class="ml-auto w-full flex-shrink-0">
				{#if remoteAgent}
					<Button variant="primary" size="sm" onclick={handleViewWorkflow} class="w-full">
						<div class="flex items-center gap-2">
							<RemoteAgentStatusIndicator
								status={remoteAgent.status}
								workspaceStatus={remoteAgent.workspaceStatus}
								hasUpdates={remoteAgent.hasUpdates}
								variant="sleek"
								size="sm"
								isExpanded={false}
							/>
							<span class="text-sm font-medium">View Agent</span>
							<Icon src={ArrowRight} class="h-4 w-4" micro />
						</div>
					</Button>
				{:else if entity}
					<!-- Show "Start work" button when there's an entity but no remote agent -->
					<Button
						variant="ghost"
						size="sm"
						class="w-full"
						onclick={handleStartWork}
						icon={isCreatingAgent ? ArrowUp : ArrowDown}
						iconPosition="right"
						disabled={isAnimating || showInstructionsForm || isCreatingAgent}
					>
						{#if isAnimating || isCreatingAgent}
							<div class="flex items-center gap-2">
								<div
									class="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"
								></div>
								{isCreatingAgent ? 'Creating agent...' : 'Starting work...'}
							</div>
						{:else}
							Assign to Agent
						{/if}
					</Button>
				{/if}
			</div>
		</div>
	{/if}

	<!-- Inline Instructions Form -->
	{#if entity && showInstructionsForm}
		<div transition:slide={{ axis: 'y' }} class="pt-2">
			<InlineInstructionsForm
				{entity}
				{layout}
				{error}
				defaultInstructions={trigger?.configuration?.agent_config?.user_guidelines}
				isVisible={showInstructionsForm}
				isCreating={isCreatingAgent}
				onSubmit={handleCreateAgent}
				onCancel={handleCancelInstructions}
			/>
		</div>
	{/if}
{/if}
