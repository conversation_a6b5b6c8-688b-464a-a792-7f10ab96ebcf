<script lang="ts">
	import Input from './Input.svelte';

	interface Props {
		value?: string;
		disabled?: boolean;
		class?: string;
		onchange?: (value: string) => void;
		onkeydown?: (event: KeyboardEvent) => void;
		isConverting?: boolean;
	}

	let {
		value = $bindable('0 9 * * *'),
		disabled = false,
		class: className = '',
		onchange,
		onkeydown,
		isConverting = false
	}: Props = $props();

	// Segment definitions with labels and hints
	const segmentInfo = [
		{ label: 'Min', placeholder: '0', hint: '0-59', maxLength: 2 },
		{ label: 'Hour', placeholder: '9', hint: '0-23', maxLength: 2 },
		{ label: 'Day', placeholder: '*', hint: '1-31', maxLength: 2 },
		{ label: 'Month', placeholder: '*', hint: '1-12', maxLength: 2 },
		{ label: 'Weekday', placeholder: '*', hint: '0-7', maxLength: 1 }
	];

	// Internal state for each segment
	let segments = $state(['0', '9', '*', '*', '*']);
	let inputRefs: HTMLInputElement[] = [];

	// ASCII animation state
	let animationSegments = $state(['', '', '', '', '']);
	let animationIntervals: NodeJS.Timeout[] = [];

	// ASCII characters for animation
	const asciiChars = ['*', '/', '-', '+', '=', '~', '^', '%', '#', '@', '&', '!', '?'];

	// Start ASCII animation when converting
	$effect(() => {
		if (isConverting) {
			startAsciiAnimation();
		} else {
			stopAsciiAnimation();
		}
	});

	function startAsciiAnimation() {
		// Clear any existing intervals
		stopAsciiAnimation();

		// Start animation for each segment with different delays
		segments.forEach((_, index) => {
			const interval = setInterval(
				() => {
					const randomChar = asciiChars[Math.floor(Math.random() * asciiChars.length)];
					animationSegments[index] = randomChar;
				},
				100 + index * 20
			); // Stagger the animations

			animationIntervals.push(interval);
		});
	}

	function stopAsciiAnimation() {
		animationIntervals.forEach((interval) => clearInterval(interval));
		animationIntervals = [];
		animationSegments = ['', '', '', '', ''];
	}

	// Sync segments with the main value
	$effect(() => {
		if (value) {
			const parts = value.split(' ');
			if (parts.length === 5) {
				segments = [...parts];
			}
		}
	});

	// Sync main value when segments change
	$effect(() => {
		const newValue = segments.join(' ');
		if (newValue !== value) {
			value = newValue;
		}
	});

	// Handle input changes
	function handleInput(index: number, newValue: string) {
		segments[index] = newValue;

		// Auto-advance on certain characters
		if (newValue && index < segments.length - 1) {
			const shouldAdvance =
				newValue.length >= segmentInfo[index].maxLength ||
				newValue.includes('*') ||
				newValue.includes('/') ||
				newValue.includes('-') ||
				newValue.includes(',');

			if (shouldAdvance) {
				inputRefs[index + 1]?.focus();
			}
		}
	}

	// Handle key navigation
	function handleKeyDown(index: number, event: KeyboardEvent) {
		const target = event.target as HTMLInputElement;

		switch (event.key) {
			case 'Enter':
				event.preventDefault();
				// Call the parent's onkeydown handler if provided
				if (onkeydown) {
					onkeydown(event);
				}
				break;

			case ' ':
				event.preventDefault();
				if (index < segments.length - 1) {
					inputRefs[index + 1]?.focus();
				}
				break;

			case 'Backspace':
				if (!target.value && index > 0) {
					event.preventDefault();
					inputRefs[index - 1]?.focus();
				}
				break;

			case 'ArrowRight':
				if (target.selectionStart === target.value.length && index < segments.length - 1) {
					event.preventDefault();
					inputRefs[index + 1]?.focus();
				}
				break;

			case 'ArrowLeft':
				if (target.selectionStart === 0 && index > 0) {
					event.preventDefault();
					inputRefs[index - 1]?.focus();
				}
				break;
		}
	}

	// Handle paste events
	function handlePaste(index: number, event: ClipboardEvent) {
		event.preventDefault();
		const pastedText = event.clipboardData?.getData('text') || '';

		// Check if it looks like a full cron expression (5 parts separated by spaces)
		const parts = pastedText.trim().split(/\s+/);

		if (parts.length === 5) {
			// Distribute across all segments
			segments = [...parts];
			// Focus the last segment
			inputRefs[4]?.focus();
		} else if (parts.length === 1) {
			// Single value, just paste into current segment
			segments[index] = parts[0];
			if (index < segments.length - 1) {
				inputRefs[index + 1]?.focus();
			}
		}
	}

	// Focus management
	function handleFocus(_index: number, event: FocusEvent) {
		const target = event.target as HTMLInputElement;
		// Select all text on focus for easy replacement
		setTimeout(() => target.select(), 0);
	}

	function handleBlur(index: number, event: FocusEvent) {
		const target = event.target as HTMLInputElement;
		if (!target.value.trim()) {
			segments[index] = '*';
		}
		handleInput(index, '*');
		onchange?.(value);
	}
</script>

<div class="cron-input {className}">
	<!-- Labels -->
	<div class="flex gap-1">
		{#each segmentInfo as info}
			<div class="flex-1 text-center">
				<div class="mb-1 block text-sm font-medium text-slate-600 dark:text-slate-400">
					{info.label}
				</div>
			</div>
		{/each}
	</div>

	<!-- Input segments -->
	<div class="flex gap-1">
		{#each segments as _segment, index}
			<div class="relative flex-1">
				<Input
					bind:this={inputRefs[index]}
					type="text"
					bind:value={segments[index]}
					placeholder={segmentInfo[index].placeholder}
					{disabled}
					class=""
					inputClass="text-center font-mono {isConverting ? 'text-transparent' : ''}"
					onInput={(value) => {
						handleInput(index, value);
					}}
					onkeydown={(e: KeyboardEvent) => handleKeyDown(index, e)}
					onpaste={(e: ClipboardEvent) => handlePaste(index, e)}
					onfocus={(e: FocusEvent) => handleFocus(index, e)}
					onblur={(e: FocusEvent) => handleBlur(index, e)}
				/>

				<!-- ASCII Animation Overlay -->
				{#if isConverting && animationSegments[index]}
					<div
						class="pointer-events-none absolute inset-0 flex items-center
							   justify-center font-mono text-sm text-slate-400 dark:text-slate-600"
					>
						{animationSegments[index]}
					</div>
				{/if}
			</div>
		{/each}
	</div>

	<!-- Hints -->
	<div class="flex gap-1">
		{#each segmentInfo as info}
			<div class="mt-1 flex flex-1 justify-center text-center">
				<span class="text-xs leading-none text-slate-500 dark:text-slate-400">
					{info.hint}
				</span>
			</div>
		{/each}
	</div>
</div>

<style>
	.cron-input {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
	}
</style>
