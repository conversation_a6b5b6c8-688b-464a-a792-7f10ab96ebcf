<script lang="ts">
	import { Icon, XMark } from 'svelte-hero-icons';
	import type { TriggerCondition, TriggerField } from '$lib/types';
	import { getOperatorDisplayName } from '$lib/trigger-providers';
	import Input from './Input.svelte';
	import ButtonGroup from './ButtonGroup.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	interface Props {
		availableFields: TriggerField[];
		conditions: TriggerCondition[];
		onConditionsChange: (conditions: TriggerCondition[]) => void;
	}

	let { availableFields, conditions, onConditionsChange }: Props = $props();

	// Store operator selections for fields that don't have active conditions yet
	let operatorSelections = $state<Record<string, string>>({});

	// Operator symbols for toggle buttons - using more intuitive symbols
	const operatorSymbols: Record<string, string> = {
		equals: '=',
		not_equals: '≠',
		greater_than: '>',
		less_than: '<',
		greater_than_or_equal: '≥',
		less_than_or_equal: '≤',
		contains: '∋',
		not_contains: '∌',
		includes: '∈',
		not_includes: '∉',
		in: '∈',
		not_in: '∉',
		starts_with: 'A*',
		ends_with: '*Z',
		regex: '/.*/',
		exists: '✓',
		not_exists: '✗'
	};

	// Get active condition for a field
	function getActiveCondition(fieldPath: string): TriggerCondition | null {
		return conditions.find(c => c.field === fieldPath) || null;
	}

	// Check if a field has an active condition
	function isFieldActive(fieldPath: string): boolean {
		const condition = getActiveCondition(fieldPath);
		return condition !== null && condition.value?.trim() !== '';
	}

	// Get the current operator for a field (from condition or selection)
	function getCurrentOperator(field: TriggerField): string {
		const condition = getActiveCondition(field.path);
		if (condition) {
			return condition.operator;
		}
		return operatorSelections[field.path] || field.allowedOperators[0] || 'equals';
	}

	// Add or update a condition
	function updateCondition(fieldPath: string, operator: string, value: string) {
		const newConditions = conditions.filter(c => c.field !== fieldPath);
		if (value?.trim()) {
			newConditions.push({ field: fieldPath, operator: operator as any, value: value?.trim() });
		}
		onConditionsChange(newConditions);
	}

	// Update operator for a field
	function updateOperator(fieldPath: string, operator: string) {
		const condition = getActiveCondition(fieldPath);
		if (condition) {
			// Update existing condition
			updateCondition(fieldPath, operator, condition.value);
		} else {
			// Store operator selection for when value is added
			operatorSelections[fieldPath] = operator;
		}
	}


</script>

<div class="space-y-3">
	<div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
		Trigger Conditions
	</div>

	{#if availableFields.length === 0}
		<div class="text-sm text-gray-500 dark:text-gray-400 italic">
			No fields available for this event
		</div>
	{:else}
		<!-- Field List -->
		<div class="space-y-2">
			{#each availableFields as field}
				{@const isActive = isFieldActive(field.path)}
				{@const condition = getActiveCondition(field.path)}
			{@const currentOperator = getCurrentOperator(field)}
			{@const currentValue = condition?.value || ''}

				<div class="py-1 transition-all duration-200 {isActive ? '' : 'opacity-60'}">
					<!-- Compact Field Row -->
					<div class="flex items-center gap-3">
						<!-- Field Name -->
						<div class="flex-shrink-0 w-32">
							<div class="text-sm text-gray-600 dark:text-gray-400">
								{field.name}
							</div>
						</div>

						<!-- Operator Selection -->
						<div class="flex-shrink-0">
							<div class="flex gap-1 {isActive ? '' : 'opacity-30'}">
								<ButtonGroup>
								{#each field.allowedOperators.slice(0, 12) as operator}
									<Button
										onclick={() => updateOperator(field.path, operator)}
										color={currentOperator === operator ? 'primary' : 'secondary'}
										variant={currentOperator === operator ? 'primary' : 'secondary'}
										size="xs"
										title={getOperatorDisplayName(operator)}
									>
										{operatorSymbols[operator] || operator}
									</Button>
								{/each}
								</ButtonGroup>
								{#if field.allowedOperators.length > 12}
									<span class="text-xs text-gray-400 px-1">+{field.allowedOperators.length - 12}</span>
								{/if}
							</div>
						</div>

						<!-- Value Input -->
						<div class="flex-1">
							<Input
								type="text"
								value={currentValue}
								oninput={(value) => updateCondition(field.path, currentOperator, String(value))}
								placeholder={field.example ? `${field.example}` : "Enter value..."}
								size="sm"
							/>
						</div>

						<!-- Clear Button -->
						{#if isActive}
							<button
								type="button"
								onclick={() => updateCondition(field.path, currentOperator, '')}
								class="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors"
								aria-label="Clear condition"
							>
								<Icon src={XMark} class="w-4 h-4" mini />
							</button>
						{/if}
					</div>
				</div>
			{/each}
		</div>

		<!-- Summary -->
		<div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg">
			<div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
				Active Conditions ({conditions.length})
			</div>
			{#if conditions.length === 0}
				<div class="text-xs text-gray-500 dark:text-gray-400 italic">
					No conditions set - trigger will fire for all events of this type
				</div>
			{:else}
				<div class="space-y-1">
					{#each conditions as condition, index}
						{@const field = availableFields.find(f => f.path === condition.field)}
						<div class="text-xs text-gray-600 dark:text-gray-300">
							{#if index > 0}<span class="text-gray-500">AND</span>{/if}
							<span class="">{field?.name || condition.field}</span>
							<span class="text-gray-500">{getOperatorDisplayName(condition.operator)}</span>
							<span class="font-mono bg-gray-100 dark:bg-gray-700 px-1 rounded">"{condition.value}"</span>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	{/if}
</div>
