<script lang="ts">
	// Simple skeleton component for form updates that are being streamed
</script>

<div class="mt-3 border border-blue-200 dark:border-blue-800 rounded-lg overflow-hidden shadow-sm">
	<!-- Header -->
	<div class="bg-blue-50 dark:bg-blue-900/20 px-3 py-2 border-b border-blue-200 dark:border-blue-800">
		<div class="flex items-center gap-2 mb-1">
			<div class="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0 animate-pulse"></div>
			<div class="h-3 bg-blue-300 dark:bg-blue-600 rounded w-20 animate-pulse"></div>
			<div class="h-4 bg-blue-200 dark:bg-blue-700 rounded w-12 animate-pulse"></div>
		</div>
	</div>

	<!-- Content -->
	<div class="bg-white dark:bg-gray-900 px-3 py-2">
		<div class="space-y-3">
			<!-- Field 1 -->
			<div class="space-y-1">
				<div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse"></div>
				<div class="h-4 bg-gray-100 dark:bg-gray-800 rounded w-full animate-pulse"></div>
			</div>

			<!-- Field 2 -->
			<div class="space-y-1">
				<div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-20 animate-pulse"></div>
				<div class="h-4 bg-gray-100 dark:bg-gray-800 rounded w-3/4 animate-pulse"></div>
			</div>

			<!-- Field 3 -->
			<div class="space-y-1">
				<div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
				<div class="h-8 bg-gray-100 dark:bg-gray-800 rounded w-full animate-pulse"></div>
			</div>
		</div>
	</div>
</div>
