<script lang="ts">
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { ArrowUturnLeft, ArrowUturnRight } from 'svelte-hero-icons';

	interface Props {
		formUpdate: any;
		formStateBefore?: any;
		onRevert?: () => void;
	}

	let { formUpdate, formStateBefore, onRevert }: Props = $props();

	let showingPrevious = $state(false);

	function toggleVersion() {
		showingPrevious = !showingPrevious;
	}

	function handleRevert() {
		if (onRevert && formStateBefore) {
			onRevert();
		}
	}

	// Get the data to display based on current view
	let displayData = $derived(showingPrevious && formStateBefore ?
		Object.fromEntries(
			Object.keys(formUpdate).map(key => [key, formStateBefore[key]])
		) :
		formUpdate
	);

	function formatFieldName(key: string): string {
		return key.replace(/([A-Z])/g, ' $1').trim();
	}

	function formatValue(value: any): string {
		if (value === null || value === undefined) return '(empty)';
		if (typeof value === 'object') return JSON.stringify(value);
		return String(value);
	}
</script>

<div class="mt-3 border border-blue-200 dark:border-blue-800 rounded-lg overflow-hidden shadow-sm">
	<!-- Header -->
	<div class="bg-blue-50 dark:bg-blue-900/20 px-3 py-2 pb-1 border-b border-blue-200 dark:border-blue-800">
		<div class="flex items-center gap-2 mb-1">
			<div class="w-1.5 h-1.5 bg-blue-500 rounded-full flex-shrink-0"></div>
			<span class="text-blue-700 dark:text-blue-400 font-medium text-xs">
				Form updated
			</span>
			<span class="ml-auto text-blue-600 dark:text-blue-500 text-xs bg-blue-100 dark:bg-blue-900/40 px-1.5 py-0.5 rounded">
				{Object.keys(formUpdate).length} field{Object.keys(formUpdate).length !== 1 ? 's' : ''}
			</span>
		</div>

	</div>

	<!-- Update Details -->
	<div class="bg-white dark:bg-gray-900 px-3 py-2">
		<div class="space-y-2">
			{#each Object.entries(displayData) as [key, value]}
				{@const hasChanged = formStateBefore && formStateBefore[key] !== formUpdate[key]}
				<div class="space-y-1">
					<div class="flex items-center justify-between">
						<span class="font-medium text-gray-600 dark:text-gray-400 text-[0.6rem] uppercase tracking-wide">
							{formatFieldName(key)}
						</span>
						<!-- {#if hasChanged}
							<span class="text-blue-600 dark:text-blue-400 flex-shrink-0 text-xs">
								{showingPrevious ? '→' : '✓'}
							</span>
						{/if} -->
					</div>
					<div class="text-xs leading-relaxed whitespace-wrap break-words {hasChanged && !showingPrevious ? 'bg-blue-50 dark:bg-blue-900/20 px-2 py-1.5 rounded text-blue-700' : 'pl-2'}">
						{formatValue(value)}
					</div>
				</div>
			{/each}
		</div>
	</div>
</div>
{#if formStateBefore}
	<div class="flex items-center justify-between py-2 px-2">
		<div class="flex items-center gap-1">
			<Button
				variant="ghost"
				size="xs"
				icon={showingPrevious ? ArrowUturnRight : ArrowUturnLeft}
				onclick={toggleVersion}
				class="gap-2"
			>
			{showingPrevious ? 'Previous values' : 'Revert'}
			</Button>

			{#if onRevert && showingPrevious}
				<Button
					variant="ghost"
					size="xs"
					onclick={handleRevert}
					class="text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300 text-xs px-1.5 py-0.5"
				>
					Revert
				</Button>
			{/if}
		</div>
	</div>
{/if}
