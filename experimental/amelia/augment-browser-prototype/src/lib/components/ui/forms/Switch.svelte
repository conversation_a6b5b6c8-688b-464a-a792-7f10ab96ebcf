<script lang="ts">
	interface Props {
		checked?: boolean;
		disabled?: boolean;
		onchange?: (checked: boolean) => void;
		class?: string;
		size?: 'sm' | 'md' | 'lg';
	}

	let {
		checked = false,
		disabled = false,
		onchange,
		class: className = '',
		size = 'md'
	}: Props = $props();

	function handleClick() {
		if (disabled) return;
		onchange?.(!checked);
	}

	const sizeClasses = {
		sm: 'w-8 h-4',
		md: 'w-11 h-6',
		lg: 'w-14 h-8'
	};

	const thumbSizeClasses = {
		sm: 'w-3 h-3',
		md: 'w-5 h-5',
		lg: 'w-7 h-7'
	};

	const translateClasses = $derived({
		sm: checked ? 'translate-x-4' : 'translate-x-0',
		md: checked ? 'translate-x-5' : 'translate-x-0',
		lg: checked ? 'translate-x-6' : 'translate-x-0'
	});
</script>

<button
	type="button"
	class="relative inline-flex flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 {sizeClasses[size]} {checked
		? 'bg-blue-600'
		: 'bg-slate-200 dark:bg-slate-700'} {disabled
		? 'opacity-50 cursor-not-allowed'
		: ''} {className}"
	role="switch"
	aria-checked={checked}
	{disabled}
	onclick={handleClick}
>
	<span class="sr-only">Toggle switch</span>
	<span
		class="pointer-events-none inline-block rounded-full bg-white shadow transform ring-0 transition duration-200 ease-in-out {thumbSizeClasses[size]} {translateClasses[size]}"
	></span>
</button>
