<script lang="ts">
	import FormField from '$lib/components/ui/forms/FormField.svelte';
	import CardButton from '$lib/components/ui/data-display/CardButton.svelte';
	import CardButtonGroup from '$lib/components/ui/data-display/CardButtonGroup.svelte';
	import { Icon } from 'svelte-hero-icons';

	import { getConditionConfigForProvider } from '$lib/utils/trigger-form-utils';
	import ScheduleConditionsBuilder from './ScheduleConditionsBuilder.svelte';

	interface Provider {
		id: string;
		name: string;
		description: string;
		icon: any;
	}

	interface Condition {
		type: string;
		value?: string;
	}

	interface Props {
		selectedProvider: string;
		triggerType: 'schedule' | 'pull_request' | 'workflow_run' | 'issue';
		providers: Provider[];
		conditions: Condition[];
		projectGitHubRepo?: string;
	}

	let {
		selectedProvider = $bindable(),
		triggerType = $bindable(),
		providers,
		conditions = $bindable(),
		projectGitHubRepo
	}: Props = $props();

	// Get dynamic config for current provider/entity
	const conditionConfig = $derived(
		getConditionConfigForProvider(selectedProvider as any, triggerType as any)
	);

	// Dynamic form data based on config
	let formData = $state<Record<string, any>>({});

	function handleProviderChange(providerId: string) {
		selectedProvider = providerId;
		const firstEntityType = triggerTypeOptions[0]?.value;
		if (firstEntityType) {
			triggerType = firstEntityType;
		} else {
			triggerType = 'schedule';
		}
	}

	function handleTriggerTypeChange(type: 'schedule' | 'pull_request' | 'workflow_run' | 'issue') {
		triggerType = type;
	}

	// Initialize formData from conditions when component mounts or conditions change from outside
	$effect(() => {
		// Reset formData based on current config
		const newFormData: Record<string, any> = {};

		// Initialize all fields from config
		for (const config of conditionConfig) {
			const fieldKey = config.value;
			newFormData[fieldKey] = '';
		}

		// Populate from existing conditions
		for (const condition of conditions) {
			if (newFormData.hasOwnProperty(condition.type)) {
				newFormData[condition.type] = condition.value || '';
			}
		}

		formData = newFormData;
	});

	// Function to sync formData changes back to conditions (called manually)
	function syncFormDataToConditions() {
		const newConditions: Condition[] = [];

		// Add conditions based on formData values using config
		for (const config of conditionConfig) {
			const fieldKey = config.value;
			const fieldValue = formData[fieldKey];

			if (fieldValue && fieldValue !== '') {
				newConditions.push({ type: fieldKey, value: fieldValue });
			}
		}

		// Add default condition if none exist
		if (newConditions.length === 0) {
			newConditions.push({ type: 'assigned_to_me' });
		}

		conditions = newConditions;
	}

	// Get width class based on config
	function getWidthClass(width?: string): string {
		switch (width) {
			case 'xs':
				return 'max-w-24';
			case 'sm':
				return 'max-w-32';
			case 'md':
				return 'max-w-48';
			case 'lg':
				return 'max-w-64';
			case 'xl':
				return 'max-w-80';
			case 'full':
				return 'w-full';
			default:
				return 'max-w-48'; // Default to medium width
		}
	}

	// Reactive trigger type options based on selected provider
	const triggerTypeOptions = $derived(
		(() => {
			if (selectedProvider === 'github') {
				return [
					{
						value: 'pull_request',
						label: 'Pull Requests',
						description: 'Monitor pull request events'
					},
					{
						value: 'workflow_run',
						label: 'Workflow Runs',
						description: 'Monitor GitHub Actions runs'
					}
				];
			} else if (selectedProvider === 'linear') {
				return [{ value: 'issue', label: 'Issues', description: 'Monitor Linear issue events' }];
			}
			return [];
		})()
	);
</script>

<!-- Provider Configuration -->
<div class="grid gap-6 px-5 pt-6 pb-6 lg:grid-cols-[320px_1fr]">
	<!-- Provider and Trigger Type Selection (Left Column) -->
	<div class="space-y-4">
		<!-- Provider Selection -->
		<div>
			<div class="mb-3 block text-sm font-medium text-gray-700 dark:text-gray-300">Provider</div>
			<CardButtonGroup
				direction="vertical"
				class="w-full"
				role="radiogroup"
				aria-label="Select provider"
			>
				{#each providers as provider}
					<CardButton
						variant="grouped"
						selected={selectedProvider === provider.id}
						ariaLabel="Select {provider?.name} provider"
						onclick={() => handleProviderChange(provider.id)}
					>
						<div class="relative flex items-center gap-3">
							<div class="flex h-6 w-6 items-center justify-center">
								<Icon src={provider.icon} class="h-6 w-6" />
							</div>
							<div class="min-w-0 flex-1">
								<div class="text-sm font-medium text-gray-900 dark:text-white">
									{provider?.name}
								</div>
								<div class="text-xs text-gray-500 dark:text-gray-400">{provider.description}</div>
							</div>
						</div>
					</CardButton>
				{/each}
			</CardButtonGroup>
		</div>

		<!-- Trigger Type Selection -->
		{#if selectedProvider !== 'schedule'}
			<div>
				<div class="mb-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
					Trigger Type
				</div>
				<CardButtonGroup
					direction="vertical"
					class="w-full"
					role="radiogroup"
					aria-label="Select trigger type"
				>
					{#each triggerTypeOptions as option}
						<CardButton
							variant="grouped"
							selected={triggerType === option.value}
							ariaLabel="Select {option.label} trigger type"
							onclick={() =>
								handleTriggerTypeChange(option.value as 'pull_request' | 'workflow_run' | 'issue')}
						>
							<div class="min-w-0 flex-1">
								<div class="text-sm font-medium text-gray-900 dark:text-white">{option.label}</div>
								<div class="text-xs text-gray-500 dark:text-gray-400">{option.description}</div>
							</div>
						</CardButton>
					{/each}
				</CardButtonGroup>
			</div>
		{/if}
	</div>

	<!-- Configuration Form (Right Column) -->

	{#if triggerType === 'schedule'}
		<ScheduleConditionsBuilder bind:conditions />
	{:else}
		<div class="space-y-6">
			<!-- Condition Filters -->
			{#if conditionConfig.length > 0}
				<div>
					<div class="mb-3 block text-sm font-medium text-gray-700 dark:text-gray-300">
						Condition Filters
					</div>

					<!-- Dynamic form fields based on config -->
					<div class="flex flex-wrap gap-x-5 gap-y-3">
						{#each conditionConfig as config}
							{#if !config.isHiddenFromConditionsBuilders}
								<FormField
									labelText={config.label}
									bind:value={formData[config.value]}
									placeholder={config.placeholder || ''}
									hint={config.hint || config.description}
									class={getWidthClass(config.width)}
									onchange={syncFormDataToConditions}
								/>
							{/if}
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
