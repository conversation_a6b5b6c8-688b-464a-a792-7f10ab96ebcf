<script lang="ts">
	import { debounce } from '$lib/utils/timing';
	interface Props {
		id?: string;
		value?: string;
		placeholder?: string;
		required?: boolean;
		disabled?: boolean;
		readonly?: boolean;
		rows?: number;
		cols?: number;
		maxlength?: number;
		minlength?: number;
		wrap?: 'soft' | 'hard';
		resize?: 'none' | 'both' | 'horizontal' | 'vertical' | 'auto';
		class?: string;
		textareaClass?: string;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'default' | 'error';
		autoResize?: boolean;
		autoFocus?: boolean;
		maxHeight?: number;
		textareaElement?: HTMLTextAreaElement;
		oninput?: (value: string) => void;
		onchange?: (value: string) => void;
		onkeypress?: (event: KeyboardEvent) => void;
		onkeydown?: (event: KeyboardEvent) => void;
		onkeyup?: (event: KeyboardEvent) => void;
		onfocus?: (event: FocusEvent) => void;
		onblur?: (event: FocusEvent) => void;
		[key: string]: any;
	}

	let {
		id,
		value = $bindable(),
		placeholder,
		required = false,
		disabled = false,
		readonly = false,
		rows = 3,
		cols,
		maxlength,
		minlength,
		wrap = 'soft',
		resize = 'vertical',
		class: className = '',
		textareaClass = '',
		size = 'md',
		variant = 'default',
		autoResize = false,
		autoFocus = false,
		maxHeight,
		textareaElement = $bindable(),
		oninput,
		onchange,
		onkeypress,
		onkeydown,
		onkeyup,
		onfocus,
		onblur,
		...restProps
	}: Props = $props();

	// Generate unique ID if not provided
	const fieldId = id || `textarea-${Math.random().toString(36).substring(2, 11)}`;

	// Base textarea styles following the existing design patterns
	const baseTextareaStyles =
		'w-full bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-600 text-slate-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 transition-colors placeholder:text-slate-400 dark:placeholder:text-slate-500';

	// Size styles
	const sizeStyles = {
		sm: 'px-2.5 py-1.5 text-sm',
		md: 'px-3 py-2 text-sm',
		lg: 'px-4 py-3 text-base'
	};

	// Variant styles
	const variantStyles = {
		default: '',
		error: 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500'
	};

	// Disabled styles
	const disabledStyles = disabled ? 'opacity-50 cursor-not-allowed' : '';

	// Resize styles
	const resizeStyles = {
		none: 'resize-none',
		both: 'resize',
		horizontal: 'resize-x',
		vertical: 'resize-y',
		auto: 'resize-none' // Will be handled by auto-resize functionality
	};

	// Combined textarea class
	const textareaClasses = $derived(
		[
			baseTextareaStyles,
			sizeStyles[size],
			variantStyles[variant],
			disabledStyles,
			resizeStyles[resize],
			textareaClass
		]
			.filter(Boolean)
			.join(' ')
	);

	// Debounced resize function for better performance
	const debouncedResize = debounce(() => {
		if (autoResize && textareaElement) {
			textareaElement.style.height = 'auto';
			const newHeight = maxHeight
				? Math.min(textareaElement.scrollHeight, maxHeight)
				: textareaElement.scrollHeight;
			textareaElement.style.height = newHeight + 10 + 'px';

			// Enable scrolling if content exceeds max height
			// if (maxHeight && textareaElement.scrollHeight > maxHeight) {
			// 	textareaElement.style.overflowY = 'auto';
			// } else {
			// 	textareaElement.style.overflowY = 'hidden';
			// }
		}
	}, 100); // 100ms debounce

	function handleInput(event: Event) {
		const target = event.target as HTMLTextAreaElement;

		// Trigger debounced resize
		if (autoResize) {
			debouncedResize();
		}

		oninput?.(target.value);
	}

	function handleChange(event: Event) {
		const target = event.target as HTMLTextAreaElement;
		onchange?.(target.value);
	}

	// Initialize auto-resize on mount and when value changes
	$effect(() => {
		if (autoResize && textareaElement && value !== undefined) {
			debouncedResize();
		}
	});
</script>

<div class="flex {className}">
	<textarea
		bind:this={textareaElement}
		id={fieldId}
		bind:value
		{placeholder}
		{required}
		{disabled}
		{readonly}
		{rows}
		{cols}
		{maxlength}
		{minlength}
		{wrap}
		class={textareaClasses}
		{autoFocus}
		oninput={handleInput}
		onchange={handleChange}
		{onkeypress}
		{onkeydown}
		{onkeyup}
		{onfocus}
		{onblur}
		{...restProps}
	></textarea>
</div>
