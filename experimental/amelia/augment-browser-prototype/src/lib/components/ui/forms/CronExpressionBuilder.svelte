<script lang="ts">
	import Button from '../navigation/Button.svelte';
	import <PERSON><PERSON><PERSON> from './FormField.svelte';
	import { Icon, Clock } from 'svelte-hero-icons';
	import cronstrue from 'cronstrue';

	interface Props {
		cronExpression: string;
		timezone?: string;
		description?: string;
		startDate?: string;
		endDate?: string;
		onUpdate: (data: {
			cronExpression: string;
			timezone?: string;
			description?: string;
			startDate?: string;
			endDate?: string;
		}) => void;
	}

	let {
		cronExpression = $bindable(),
		timezone = $bindable(),
		description = $bindable(),
		startDate = $bindable(),
		endDate = $bindable(),
		onUpdate
	}: Props = $props();

	// Convert ISO 8601 to datetime-local format for display
	function convertFromISO8601(iso8601: string | undefined): string | undefined {
		if (!iso8601) return undefined;

		try {
			// Parse the ISO date and format it for datetime-local
			const date = new Date(iso8601);
			if (isNaN(date.getTime())) {
				return undefined;
			}

			// Format as YYYY-MM-DDTHH:MM (datetime-local format)
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');

			return `${year}-${month}-${day}T${hours}:${minutes}`;
		} catch (error) {
			return undefined;
		}
	}

	// Local variables for the datetime-local inputs
	let localStartDate = $state(convertFromISO8601(startDate) || '');
	let localEndDate = $state(convertFromISO8601(endDate) || '');

	// Track if we've initialized to prevent overriding user input
	let initialized = $state(false);

	// Update local variables when props change (only on initial load)
	$effect(() => {
		if (!initialized) {
			const newLocalStartDate = convertFromISO8601(startDate) || '';
			const newLocalEndDate = convertFromISO8601(endDate) || '';

			localStartDate = newLocalStartDate;
			localEndDate = newLocalEndDate;
			initialized = true;
		}
	});

	// Common cron presets
	const cronPresets = [
		{ label: 'Daily at 9 AM', value: '0 9 * * *', description: 'Runs every day at 9:00 AM' },
		{
			label: 'Weekdays at 9 AM',
			value: '0 9 * * 1-5',
			description: 'Runs Monday-Friday at 9:00 AM'
		},
		{ label: 'Weekly on Monday', value: '0 9 * * 1', description: 'Runs every Monday at 9:00 AM' },
		{ label: 'Bi-weekly', value: '0 9 * * 1/2', description: 'Runs every other Monday at 9:00 AM' },
		{
			label: 'Monthly on 1st',
			value: '0 9 1 * *',
			description: 'Runs on the 1st of every month at 9:00 AM'
		},
		{
			label: 'Monthly on 15th',
			value: '0 9 15 * *',
			description: 'Runs on the 15th of every month at 9:00 AM'
		},
		{
			label: 'Quarterly',
			value: '0 9 1 */3 *',
			description: 'Runs on the 1st day of every 3rd month at 9:00 AM'
		},
		{
			label: 'Semi-annually',
			value: '0 9 1 */6 *',
			description: 'Runs on the 1st day of every 6th month at 9:00 AM'
		},
		{
			label: 'Annually',
			value: '0 9 1 1 *',
			description: 'Runs on January 1st at 9:00 AM every year'
		}
	];

	// Timezone options (common ones)
	const timezoneOptions = [
		{ value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
		{ value: 'America/New_York', label: 'Eastern Time (US)' },
		{ value: 'America/Chicago', label: 'Central Time (US)' },
		{ value: 'America/Denver', label: 'Mountain Time (US)' },
		{ value: 'America/Los_Angeles', label: 'Pacific Time (US)' },
		{ value: 'Europe/London', label: 'London (GMT/BST)' },
		{ value: 'Europe/Paris', label: 'Paris (CET/CEST)' },
		{ value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
		{ value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
		{ value: 'Australia/Sydney', label: 'Sydney (AEST/AEDT)' }
	];

	function handlePresetSelect(preset: (typeof cronPresets)[0]) {
		cronExpression = preset.value;
		description = preset.description;
		handleUpdate();
	}

	// Convert datetime-local format to ISO 8601 with timezone
	function convertToISO8601(
		datetimeLocal: string | undefined,
		timezoneId: string = 'UTC'
	): string | undefined {
		if (!datetimeLocal) return undefined;

		try {
			// datetime-local format: "2024-01-01T09:00"
			// We need to add seconds and timezone
			const dateTimeWithSeconds =
				datetimeLocal.includes(':') && datetimeLocal.split(':').length === 2
					? `${datetimeLocal}:00`
					: datetimeLocal;

			if (timezoneId === 'UTC') {
				// For UTC, just append Z
				return `${dateTimeWithSeconds}Z`;
			} else {
				// For other timezones, we need to convert properly
				// Get timezone offset for the specified timezone
				// This is a simplified approach - in production you'd want to use a proper timezone library
				const timezoneOffsets: Record<string, string> = {
					'America/New_York': '-05:00', // EST (simplified)
					'America/Los_Angeles': '-08:00', // PST (simplified)
					'Europe/London': '+00:00', // GMT
					'Europe/Paris': '+01:00', // CET (simplified)
					'Asia/Tokyo': '+09:00',
					'Asia/Shanghai': '+08:00',
					'Australia/Sydney': '+10:00' // AEST (simplified)
				};

				const offset = timezoneOffsets[timezoneId] || '+00:00';
				return `${dateTimeWithSeconds}${offset}`;
			}
		} catch {
			return undefined;
		}
	}

	function handleUpdate() {
		// Convert datetime-local values to ISO 8601 with timezone
		const convertedStartDate = convertToISO8601(localStartDate, timezone);
		const convertedEndDate = convertToISO8601(localEndDate, timezone);

		onUpdate({
			cronExpression,
			timezone,
			description,
			startDate: convertedStartDate,
			endDate: convertedEndDate
		});
	}

	let readableCron = $derived.by(() => {
		try {
			return cronstrue.toString(cronExpression);
		} catch {
			return 'Invalid cron expression';
		}
	});
</script>

<div class="">
	<!-- Manual Cron Expression -->
	<div class="relative w-full">
		<FormField
			type="text"
			labelText="Cron Expression"
			bind:value={cronExpression}
			placeholder="0 9 * * *"
			onchange={handleUpdate}
		/>
		<div
			class="pointer-events-none flex transform items-center text-xs leading-none text-slate-500 italic max-lg:mt-2 max-lg:mb-3 max-lg:ml-3 lg:absolute lg:right-4 lg:bottom-3 dark:text-slate-400"
		>
			{readableCron}
		</div>
	</div>

	<div class="mt-2 flex flex-row flex-wrap gap-1">
		{#each cronPresets as preset}
			<Button
				variant={cronExpression === preset.value ? 'primary' : 'secondary'}
				size="xs"
				onclick={() => handlePresetSelect(preset)}
			>
				{preset.label}
			</Button>
		{/each}
	</div>

	<div class="mt-6 flex flex-wrap gap-4">
		<!-- Timezone -->
		<FormField
			type="select"
			labelText="Timezone"
			bind:value={timezone}
			options={timezoneOptions}
			placeholder="Select timezone (defaults to UTC)"
			class="flex-1 whitespace-nowrap"
			onchange={handleUpdate}
		/>

		<!-- Date Range (Optional) -->
		<FormField
			type="datetime-local"
			labelText="Start Date (Optional)"
			bind:value={localStartDate}
			placeholder="YYYY-MM-DDTHH:MM"
			class="flex-1"
			hint="When to start executing this schedule"
			onchange={handleUpdate}
		/>
		<FormField
			type="datetime-local"
			labelText="End Date (Optional)"
			bind:value={localEndDate}
			placeholder="YYYY-MM-DDTHH:MM"
			class="flex-1"
			hint="When to stop executing this schedule"
			onchange={handleUpdate}
		/>
	</div>
</div>
