<script lang="ts">
	import { ChevronDown, ChevronRight, Icon } from 'svelte-hero-icons';

	interface Props {
		text: string;
		maxLines?: number;
		class?: string;
	}

	let { text, maxLines = 3, class: className = '' }: Props = $props();

	let isExpanded = $state(false);
	let textElement: HTMLElement;
	let needsExpansion = $state(false);

	// Check if text needs expansion by measuring line height
	$effect(() => {
		if (textElement) {
			const lineHeight = parseInt(getComputedStyle(textElement).lineHeight);
			const actualHeight = textElement.scrollHeight;
			const maxHeight = lineHeight * maxLines;
			needsExpansion = actualHeight > maxHeight;
		}
	});

	function toggleExpansion() {
		isExpanded = !isExpanded;
	}
</script>

<div class="text-xs text-slate-500 dark:text-slate-400 {className}">
	<div
		bind:this={textElement}
		class="overflow-hidden transition-all duration-200"
		style={!isExpanded ? `display: -webkit-box; -webkit-line-clamp: ${maxLines}; -webkit-box-orient: vertical;` : ''}
	>
		{text}
	</div>

	{#if needsExpansion}
		<button
			class="flex items-center gap-1 mt-1 text-xs text-slate-600 dark:text-slate-300 hover:text-slate-800 dark:hover:text-slate-100 transition-colors"
			onclick={toggleExpansion}
		>
			<Icon
				src={isExpanded ? ChevronDown : ChevronRight}
				class="w-3 h-3"
				micro
			/>
			{isExpanded ? 'Show less' : 'Show more'}
		</button>
	{/if}
</div>
