<script lang="ts">
	import { onMount } from 'svelte';
	import Markdown from './Markdown.svelte';

	interface Props {
		/** The full content to stream */
		content: string;
		color?: string;
		/** Whether this content is actively streaming */
		isStreaming?: boolean;
		/** Whether to render as markdown */
		useMarkdown?: boolean;
		/** Base animation speed (characters per frame at 60fps) */
		speed?: number;
		/** Speed when catching up to new content */
		catchUpSpeed?: number;
		/** How far behind before switching to catch-up mode */
		catchUpThreshold?: number;
		/** Skip animation entirely */
		disabled?: boolean;
		/** Additional CSS classes */
		class?: string;
		/** Error message to display instead of content */
		error?: string;
		/** Callback when content changes (for scroll handling) */
		onContentChange?: () => void;
	}

	let {
		content = '',
		color = 'slate',
		isStreaming = false,
		useMarkdown = true,
		speed = 2,
		catchUpSpeed = 8,
		catchUpThreshold = 20,
		disabled = false,
		class: className = '',
		error,
		onContentChange
	}: Props = $props();

	// Animation state
	let visibleLength = $state(0);
	let animationFrameId = $state<number | null>(null);
	let previousContent = $state('');
	let previousIsStreaming = $state(false);
	let contentHash = $state('');
	let lastResetReason = $state('');
	let isInitialLoad = $state(true);

	// Derived state
	let targetLength = $derived(content.length);
	let displayContent = $derived(content.substring(0, visibleLength));
	let needsCatchUp = $derived(targetLength - visibleLength > catchUpThreshold);
	let shouldAnimate = $derived(!disabled && isStreaming && targetLength > 0);

	/**
	 * Main animation function
	 */
	function animate() {
		if (!shouldAnimate || visibleLength >= targetLength) {
			// Animation complete or should stop
			animationFrameId = null;
			return;
		}

		// Determine animation speed based on how far behind we are
		const currentSpeed = needsCatchUp ? catchUpSpeed : speed;

		// Update visible length, ensuring we don't overshoot
		visibleLength = Math.min(visibleLength + currentSpeed, targetLength);

		// Continue animation if we haven't reached the target
		if (visibleLength < targetLength) {
			animationFrameId = requestAnimationFrame(animate);
		} else {
			animationFrameId = null;
		}
	}

	/**
	 * Start animation
	 */
	function startAnimation() {
		if (animationFrameId !== null) {
			cancelAnimationFrame(animationFrameId);
		}
		animationFrameId = requestAnimationFrame(animate);
	}

	/**
	 * Stop animation and clean up
	 */
	function stopAnimation() {
		if (animationFrameId !== null) {
			cancelAnimationFrame(animationFrameId);
			animationFrameId = null;
		}
	}

	// Single effect to handle all state changes
	$effect(() => {
		// Handle disabled state
		if (disabled) {
			stopAnimation();
			visibleLength = targetLength;
			previousContent = content;
			previousIsStreaming = isStreaming;
			return;
		}

		// Handle content changes
		const contentChanged = content !== previousContent;
		const contentShrunk = content.length < previousContent.length;
		const contentGrew = content.length > previousContent.length;
		const contentExtendsExisting =
			contentGrew && previousContent.length > 0 && content.startsWith(previousContent);

		// Create a simple hash of the first part of content to detect complete replacements
		const newContentHash = content.substring(0, Math.min(100, content.length));

		if (isInitialLoad && content.length > 0) {
			// Initial load: show all content immediately without animation
			console.log('StreamingText: Initial load, showing content immediately', {
				contentLength: content.length,
				isStreaming
			});
			visibleLength = content.length;
			isInitialLoad = false;
			lastResetReason = 'initial_load';
		} else if (contentShrunk) {
			// Content shrunk - reset animation
			console.log('StreamingText: Resetting animation due to content shrinking', {
				previousLength: previousContent.length,
				newLength: content.length,
				visibleLength,
				isStreaming
			});
			visibleLength = 0;
			lastResetReason = 'content_shrunk';
		} else if (
			contentChanged &&
			content.length > 0 &&
			previousContent.length > 0 &&
			!contentExtendsExisting &&
			!isStreaming
		) {
			// Content was completely replaced (not just extended) and we're not streaming
			console.log('StreamingText: Resetting animation due to content replacement', {
				previousLength: previousContent.length,
				newLength: content.length,
				visibleLength,
				isStreaming,
				previousStart: previousContent.substring(0, 50),
				newStart: content.substring(0, 50)
			});
			visibleLength = 0;
			isInitialLoad = true; // Treat as initial load for new content
			lastResetReason = 'content_replaced';
		} else if (contentExtendsExisting) {
			// Content grew by extending existing content - preserve animation progress
			// and only animate the new part
			const newVisibleLength = Math.max(visibleLength, previousContent.length);
			console.log('StreamingText: Content extended, preserving progress and animating new part', {
				previousLength: previousContent.length,
				newLength: content.length,
				oldVisibleLength: visibleLength,
				newVisibleLength,
				isStreaming,
				willAnimate: isStreaming && newVisibleLength < content.length
			});
			visibleLength = newVisibleLength;
		} else if (contentGrew) {
			// Content grew but doesn't extend existing - ensure visibleLength doesn't exceed new length
			const newVisibleLength = Math.min(visibleLength, content.length);
			console.log('StreamingText: Content grew (non-extending), adjusting visible length', {
				previousLength: previousContent.length,
				newLength: content.length,
				oldVisibleLength: visibleLength,
				newVisibleLength,
				isStreaming
			});
			visibleLength = newVisibleLength;
		}

		// Update content hash
		contentHash = newContentHash;

		// Handle streaming state changes
		if (!isStreaming && previousIsStreaming && visibleLength < targetLength) {
			// Streaming just stopped - show remaining content immediately
			visibleLength = targetLength;
			stopAnimation();
		} else if (shouldAnimate && visibleLength < targetLength && animationFrameId === null) {
			// Start animation if needed
			startAnimation();
		} else if (!shouldAnimate && visibleLength < targetLength) {
			// Show full content immediately when not animating
			visibleLength = targetLength;
		}

		// Update tracking variables
		previousContent = content;
		previousIsStreaming = isStreaming;

		// Call content change callback if provided
		if (onContentChange) {
			onContentChange();
		}
	});

	// Cleanup on unmount
	onMount(() => {
		return () => {
			stopAnimation();
		};
	});
</script>

<!--
	Render the content based on whether we're using markdown or plain text
	Always show the display content (animated portion) when animating,
	or full content when animation is disabled/complete
-->
{#if error}
	<!-- Show error message -->
	<div
		class="rounded-lg bg-red-50 p-4 text-red-700 dark:bg-red-900/20 dark:text-red-400 {className}"
	>
		<p class="text-sm font-medium">
			{#if error.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')}
				Connection interrupted. Attempting to reconnect...
			{:else if error.includes('network error')}
				Network error. Retrying connection...
			{:else if error.includes('timeout')}
				Request timed out. Please try again.
			{:else}
				Error: {error}
			{/if}
		</p>
	</div>
{:else if disabled || !shouldAnimate}
	<!-- Show full content immediately when disabled or not streaming -->
	{#if useMarkdown}
		<Markdown {content} class={className} {color} hasNoPadding />
	{:else}
		<div class="break-words whitespace-pre-wrap {className}">
			{content}
		</div>
	{/if}
{:else}
	<!-- Show animated content -->
	{#if useMarkdown}
		<Markdown content={displayContent} class={className} {color} hasNoPadding />
	{:else}
		<div class="break-words whitespace-pre-wrap {className}">
			{displayContent}
		</div>
	{/if}
{/if}
