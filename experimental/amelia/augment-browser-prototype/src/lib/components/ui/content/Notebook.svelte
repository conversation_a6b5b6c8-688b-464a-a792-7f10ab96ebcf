<script lang="ts">
	import { derived } from 'svelte/store';
	import { getAgentRawExchanges } from '$lib/utils/agent-data';
	import { extractNotesFromChatHistory } from '$lib/components/chat-history/utils';
	import { Icon, BookOpen, ClipboardDocument, MagnifyingGlass } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { addToast } from '$lib/stores/toast';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';

	interface Props {
		agent: CleanRemoteAgent;
	}

	let { agent }: Props = $props();

	// Search functionality
	let searchQuery = $state('');

	// Get raw exchanges for this agent
	const rawExchangesStore = getAgentRawExchanges(agent.id);

	// Extract all notes from chat history
	const allNotes = derived(rawExchangesStore, (exchanges) => {
		if (!exchanges || exchanges.length === 0) return [];
		return extractNotesFromChatHistory(exchanges);
	});

	// Filtered notes based on search query
	const filteredNotes = derived([allNotes], ([$notes]) => {
		if (!searchQuery.trim()) {
			return $notes;
		}

		const query = searchQuery.toLowerCase();
		return $notes.filter((note) => note.content.toLowerCase().includes(query));
	});

	// Copy all notes to clipboard
	async function copyNotesToClipboard() {
		const notes = $filteredNotes;
		if (notes.length === 0) return;

		const notesText = notes.map((note, index) => `${index + 1}. ${note.content}`).join('\n\n');

		try {
			await navigator.clipboard.writeText(notesText);
			addToast({
				type: 'success',
				message: `Copied ${notes.length} note${notes.length === 1 ? '' : 's'} to clipboard`,
				duration: 3000
			});
		} catch (error) {
			console.error('Failed to copy notes to clipboard:', error);
			addToast({
				type: 'error',
				message: 'Failed to copy notes to clipboard',
				duration: 3000
			});
		}
	}

	// Format note content for display
	function formatNoteContent(content: string): string {
		// Remove any leading/trailing whitespace and ensure proper formatting
		return content.trim();
	}

	// Format category name for display
	function formatCategoryName(category: string): string {
		return category
			.split('_')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ');
	}

	// Get category color
	function getCategoryColor(category: string): string {
		const colors: Record<string, string> = {
			code_architecture: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
			implementation_details:
				'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
			user_preferences: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
			technical_constraints:
				'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
			business_logic: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
			integration_points: 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300'
		};
		return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
	}
</script>

<div class="flex h-full flex-col">
	<!-- Header -->
	<div
		class="flex items-center justify-between border-b border-gray-200 bg-gray-50 px-[var(--panel-padding-x)] py-4 dark:border-gray-700 dark:bg-gray-800/50"
	>
		<div class="flex items-center gap-2">
			<Icon src={BookOpen} class="h-5 w-5 text-gray-600 dark:text-gray-400" />
			<h3 class="text-sm font-medium text-gray-900 dark:text-white">Notebook</h3>
			<span
				class="rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-500 dark:bg-gray-700 dark:text-gray-400"
			>
				{$filteredNotes.length}
				{$filteredNotes.length === 1 ? 'note' : 'notes'}
				{#if searchQuery.trim() && $filteredNotes.length !== $allNotes.length}
					of {$allNotes.length}
				{/if}
			</span>
		</div>

		{#if $allNotes.length > 0}
			<Button
				variant="ghost"
				size="sm"
				icon={ClipboardDocument}
				onclick={copyNotesToClipboard}
				class="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
			>
				Copy {searchQuery.trim() ? 'Filtered' : 'All'}
			</Button>
		{/if}
	</div>

	<!-- Search Bar -->
	{#if $allNotes.length > 0}
		<div class="border-b border-gray-200 p-4 dark:border-gray-700">
			<div class="relative">
				<Icon
					src={MagnifyingGlass}
					class="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400"
				/>
				<input
					type="text"
					bind:value={searchQuery}
					placeholder="Search notes..."
					class="w-full rounded-md border border-gray-300 bg-white py-2 pr-4 pl-10 text-sm text-gray-900 focus:border-transparent focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-900 dark:text-white"
				/>
			</div>
		</div>
	{/if}

	<!-- Notes Content -->
	<div class="flex-1 overflow-y-auto">
		{#if $allNotes.length === 0}
			<div class="flex h-full flex-col items-center justify-center p-8 text-center">
				<Icon src={BookOpen} class="mb-4 h-12 w-12 text-gray-300 dark:text-gray-600" />
				<h4 class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">No notes yet</h4>
				<p class="max-w-xs text-xs text-gray-400 dark:text-gray-500">
					Notes from agent responses will appear here as the agent works and documents important
					findings.
				</p>
			</div>
		{:else if $filteredNotes.length === 0}
			<div class="flex h-full flex-col items-center justify-center p-8 text-center">
				<Icon src={MagnifyingGlass} class="mb-4 h-12 w-12 text-gray-300 dark:text-gray-600" />
				<h4 class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">
					No notes match your search
				</h4>
				<p class="max-w-xs text-xs text-gray-400 dark:text-gray-500">
					Try adjusting your search terms or clear the search to see all notes.
				</p>
			</div>
		{:else}
			<div class="divide-y divide-gray-200 dark:divide-gray-700">
				{#each $filteredNotes as note, index ((note.exchangeId || 'unknown') + index)}
					<div class="p-4">
						<div class="flex items-start gap-3">
							<!-- Note Type Indicator -->
							<div class="mt-1 flex-shrink-0">
								<div class="h-2 w-2 rounded-full bg-blue-500"></div>
							</div>

							<div class="min-w-0 flex-1">
								<!-- Category Badge (for notebook notes) -->
								{#if note.category}
									<div class="mb-2">
										<span
											class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium {getCategoryColor(
												note.category
											)}"
										>
											{formatCategoryName(note.category)}
										</span>
									</div>
								{/if}

								<!-- Note Content -->
								<div class="text-sm text-gray-900 dark:text-gray-100">
									<Markdown content={formatNoteContent(note.content)} size="sm" hasNoPadding />
								</div>

								<!-- Note Type Label -->
								<div class="mt-2 text-xs text-gray-500 dark:text-gray-400">Note</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>
</div>
