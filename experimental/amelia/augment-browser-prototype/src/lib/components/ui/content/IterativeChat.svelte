<script lang="ts">
	import { sendSilentExchangeStream, sendStreamingChatCompletion } from '$lib/api/chat';
	import type { ChatMessage } from '$lib/api/chat';
	import { Icon, PaperAirplane, ChevronDown, ChevronRight, LightBulb } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import FormUpdateBlock from '$lib/components/ui/forms/FormUpdateBlock.svelte';
	import FormUpdateSkeleton from '$lib/components/ui/forms/FormUpdateSkeleton.svelte';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import PromptEnhancer from '../PromptEnhancer.svelte';
	import type { PromptEnhancementResult } from '$lib/utils/prompts';

	interface Props {
		systemPrompt: string;
		onFormUpdate?: (update: any) => void;
		getCurrentFormState?: () => any;
		onRevertUpdate?: (previousState: any) => void;
		placeholder?: string;
		suggestions?: string[];
		templateSuggestions?: string[]; // Suggestions based on templates
		generalSuggestions?: string[]; // General help suggestions
	}

	let {
		systemPrompt,
		onFormUpdate,
		getCurrentFormState,
		onRevertUpdate,
		placeholder = 'Ask a question...',
		suggestions = [],
		templateSuggestions = [],
		generalSuggestions = []
	}: Props = $props();

	interface ChatTurn {
		id: string;
		userMessage: string;
		assistantMessage: string;
		isStreaming: boolean;
		isCollapsed: boolean;
		formUpdate?: any;
		formStateBefore?: any; // Store form state before the update
		hasPartialJson?: boolean; // Track if there's a partial JSON block being streamed
	}

	let chatTurns = $state<ChatTurn[]>([]);
	let currentInput = $state('');
	let isLoading = $state(false);

	function generateId(): string {
		return Math.random().toString(36).substring(2, 11);
	}

	// Function to detect if there's a partial JSON block at the end of the response
	function hasPartialJsonBlock(text: string): boolean {
		// Look for ```json at the end without a closing ```
		const jsonBlockStart = text.lastIndexOf('```json');
		if (jsonBlockStart === -1) return false;

		const afterJsonStart = text.substring(jsonBlockStart + 7); // 7 = length of '```json'
		const hasClosingBlock = afterJsonStart.includes('```');

		// Also check if there's any content after ```json (even just whitespace or partial JSON)
		const hasContent = afterJsonStart.trim().length > 0;

		return hasContent && !hasClosingBlock;
	}

	// Function to get the display text (hiding partial JSON blocks)
	function getDisplayText(text: string, isStreaming: boolean): string {
		if (!isStreaming) return text;

		const jsonBlockStart = text.lastIndexOf('```json');
		if (jsonBlockStart === -1) return text;

		const afterJsonStart = text.substring(jsonBlockStart + 7);
		const hasClosingBlock = afterJsonStart.includes('```');
		const hasContent = afterJsonStart.trim().length > 0;

		// If there's content after ```json but no closing block, hide the partial JSON
		if (hasContent && !hasClosingBlock) {
			return text.substring(0, jsonBlockStart).trim();
		}

		// hide full json blocks
		return text
			.replace(/```json[\s\S]*?```/g, '')
			.replace(/\n\s*\n/g, '\n')
			.trim();
	}

	async function sendMessage() {
		if (!currentInput.trim() || isLoading) return;

		const userMessage = currentInput.trim();
		currentInput = '';
		isLoading = true;

		// Collapse all previous turns
		chatTurns.forEach((turn) => (turn.isCollapsed = true));

		// Create new turn
		const newTurn: ChatTurn = {
			id: generateId(),
			userMessage,
			assistantMessage: '',
			isStreaming: true,
			isCollapsed: false
		};

		chatTurns = [...chatTurns, newTurn];

		try {
			const message = [
				// systemPrompt,
				// ...chatTurns
				// 	.slice(0, -1)
				// 	.map((turn) => `User: ${turn.userMessage}\nAssistant: ${turn.assistantMessage}`),
				`User: ${userMessage}`
			].join('\n\n');

			let streamingResponse = '';

			await sendSilentExchangeStream({ message }, (chunk) => {
				if (chunk.type === 'content_delta' && chunk.text) {
					streamingResponse += chunk.text;
					// Update the current turn
					const currentTurn = chatTurns[chatTurns.length - 1];
					if (currentTurn) {
						currentTurn.assistantMessage = streamingResponse;
						currentTurn.hasPartialJson = hasPartialJsonBlock(streamingResponse);
						chatTurns = [...chatTurns];
					}
				} else if (chunk.type === 'error') {
					console.error('Streaming error:', chunk.error);
				}
			});

			// Mark streaming as complete
			const currentTurn = chatTurns[chatTurns.length - 1];
			if (currentTurn) {
				currentTurn.isStreaming = false;
				currentTurn.hasPartialJson = false;

				// Try to extract form update from response
				try {
					// Look for JSON blocks with formUpdate
					const formUpdateMatch = currentTurn.assistantMessage.match(
						/```json\s*(\{[\s\S]*?\})\s*```/
					);
					if (formUpdateMatch) {
						const jsonStr = formUpdateMatch[1];
						const parsed = JSON.parse(jsonStr);
						if (parsed.formUpdate) {
							// Store current form state before applying update
							currentTurn.formStateBefore = getCurrentFormState?.();
							currentTurn.formUpdate = parsed.formUpdate;
							onFormUpdate?.(parsed.formUpdate);

							// Remove the JSON block from the displayed message
							currentTurn.assistantMessage = currentTurn.assistantMessage
								.replace(/```json\s*\{[\s\S]*?\}\s*```/g, '')
								.replace(/\n\s*\n/g, '\n')
								.trim();
						}
					} else {
						// Also try to find inline JSON objects with formUpdate
						const inlineMatch = currentTurn.assistantMessage.match(
							/\{[^{}]*"formUpdate"[^{}]*\{[^{}]*\}[^{}]*\}/
						);
						if (inlineMatch) {
							const parsed = JSON.parse(inlineMatch[0]);
							if (parsed.formUpdate) {
								// Store current form state before applying update
								currentTurn.formStateBefore = getCurrentFormState?.();
								currentTurn.formUpdate = parsed.formUpdate;
								onFormUpdate?.(parsed.formUpdate);

								// Remove the inline JSON from the displayed message
								currentTurn.assistantMessage = currentTurn.assistantMessage
									.replace(inlineMatch[0], '')
									.replace(/\n\s*\n/g, '\n')
									.trim();
							}
						}
					}
				} catch (e) {
					// Ignore JSON parsing errors
					console.debug('Could not parse form update from response:', e);
				}

				chatTurns = [...chatTurns];
			}
		} catch (error) {
			console.error('Chat error:', error);
			const currentTurn = chatTurns[chatTurns.length - 1];
			if (currentTurn) {
				currentTurn.assistantMessage = `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`;
				currentTurn.isStreaming = false;
				currentTurn.hasPartialJson = false;
				chatTurns = [...chatTurns];
			}
		} finally {
			isLoading = false;
		}
	}

	function toggleTurn(turnId: string) {
		const turn = chatTurns.find((t) => t.id === turnId);
		if (turn) {
			turn.isCollapsed = !turn.isCollapsed;
			chatTurns = [...chatTurns];
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			sendMessage();
		}
	}

	function handlePromptEnhanced(enhancedPrompt: string) {
		currentInput = enhancedPrompt;
	}

	function handlePromptEnhancementError(error: PromptEnhancementResult) {
		console.error('Prompt enhancement error:', error);
		// Could show a toast notification here in the future
	}
</script>

<div class="flex h-full flex-col bg-slate-50 dark:bg-slate-900">
	<!-- Chat History -->
	<div class="flex-1 space-y-3 overflow-y-auto p-4">
		{#each chatTurns as turn (turn.id)}
			<div class="space-y-2">
				<!-- User Message -->
				<div class="flex justify-end">
					<div
						class="w-full rounded-xl border border-gray-200 bg-white px-3 py-2 text-sm text-slate-500 shadow-sm dark:border-gray-700 dark:bg-gray-800"
					>
						{turn.userMessage}
					</div>
				</div>

				<!-- Assistant Response -->
				<div class="flex justify-start">
					<div class="w-full overflow-hidden rounded-lg border-slate-200 dark:border-slate-700">
						{#if turn.isCollapsed}
							<!-- Collapsed View -->
							<button
								onclick={() => toggleTurn(turn.id)}
								class="group flex w-full items-center gap-2 rounded-lg border border-slate-200 p-3 text-left transition-colors hover:bg-slate-50 dark:border-slate-700 dark:hover:bg-slate-700"
							>
								<Icon
									src={ChevronRight}
									class="h-4 w-4 text-slate-400 transition-colors group-hover:text-slate-600 dark:group-hover:text-slate-300"
								/>
								<span
									class="truncate text-sm text-slate-600 transition-colors group-hover:text-slate-900 dark:text-slate-400 dark:group-hover:text-slate-200"
								>
									{turn.assistantMessage.slice(0, 60)}...
								</span>
							</button>
						{:else}
							<!-- Expanded View -->
							<div class="p-3">
								<!-- <button
									onclick={() => toggleTurn(turn.id)}
									class="flex items-center gap-1 mb-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors text-xs font-medium group"
								>
									<Icon src={ChevronDown} class="w-3 h-3 group-hover:scale-110 transition-transform" />
									<span>Collapse</span>
								</button> -->
								<Markdown content={getDisplayText(turn.assistantMessage, turn.isStreaming)} />
								{#if turn.isStreaming}
									<span class="ml-1 inline-block h-4 w-2 animate-pulse bg-blue-600"></span>
								{/if}
								{#if turn.formUpdate}
									<FormUpdateBlock
										formUpdate={turn.formUpdate}
										formStateBefore={turn.formStateBefore}
										onRevert={() => {
											if (turn.formStateBefore && onRevertUpdate) {
												onRevertUpdate(turn.formStateBefore);
											}
										}}
									/>
								{:else if turn.isStreaming && turn.hasPartialJson}
									<FormUpdateSkeleton />
								{/if}
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/each}

		{#if chatTurns.length === 0}
			<div class="px-3 py-4">
				<!-- Legacy suggestions (for backward compatibility) -->
				{#if suggestions.length > 0}
					<div class="mb-4">
						<div class="mb-2 text-xs font-medium text-slate-500 dark:text-slate-400">
							Quick suggestions:
						</div>
						{#each suggestions as suggestion}
							<button
								class="line-clamp-2 w-full cursor-pointer py-1 text-left text-sm whitespace-pre-wrap text-slate-500"
								onclick={() => {
									currentInput = suggestion;
									sendMessage();
								}}
							>
								{suggestion}
							</button>
						{/each}
					</div>
				{/if}

				<!-- Template-based suggestions -->
				{#if templateSuggestions.length > 0}
					<div class="mb-4">
						<div class="mb-2 text-xs font-medium">Suggestions</div>
						{#each templateSuggestions as suggestion}
							<button
								class="line-clamp-2 flex w-full cursor-pointer items-center gap-0.5 py-1 text-left text-sm whitespace-pre-wrap text-slate-500"
								onclick={() => {
									currentInput = suggestion;
									sendMessage();
								}}
							>
								<Icon src={LightBulb} class="h-4 w-4 text-slate-300" micro />
								{suggestion}
							</button>
						{/each}
					</div>
				{/if}

				<!-- General help suggestions -->
				{#if generalSuggestions.length > 0}
					<div class="">
						<div class="mb-2 text-xs font-medium">FAQs</div>
						{#each generalSuggestions as suggestion}
							<button
								class="line-clamp-2 w-full cursor-pointer py-1 text-left text-sm whitespace-pre-wrap text-slate-500"
								onclick={() => {
									currentInput = suggestion;
									sendMessage();
								}}
							>
								{suggestion}
							</button>
						{/each}
					</div>
				{/if}
			</div>
		{/if}
	</div>

	<!-- Input -->
	<div class="border-t border-slate-200 bg-white p-4 dark:border-slate-700 dark:bg-slate-800">
		<div class="flex gap-2">
			<div class="relative flex-1">
				<textarea
					bind:value={currentInput}
					onkeypress={handleKeyPress}
					{placeholder}
					class="w-full resize-none rounded-lg border border-slate-300 bg-white px-3 py-2 pr-10 text-sm text-slate-900 placeholder-slate-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none dark:border-slate-600 dark:bg-slate-700 dark:text-white dark:placeholder-slate-400"
					rows="2"
					disabled={isLoading}
				></textarea>
				<div class="absolute top-2 right-2">
					<PromptEnhancer
						prompt={currentInput}
						onEnhanced={handlePromptEnhanced}
						onError={handlePromptEnhancementError}
						isDisabled={isLoading}
					/>
				</div>
			</div>
			<Button
				variant="primary"
				size="icon-sm"
				icon={PaperAirplane}
				onclick={sendMessage}
				disabled={isLoading || !currentInput.trim()}
				class="self-end"
				aria-label="Send message"
			/>
		</div>
		<!-- <div class="text-xs text-slate-500 dark:text-slate-400 mt-2">
			Press Enter to send, Shift+Enter for new line
		</div> -->
	</div>
</div>
