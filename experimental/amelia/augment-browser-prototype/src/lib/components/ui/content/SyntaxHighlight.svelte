<script lang="ts">
	import hljs from 'highlight.js';
	import { theme } from '$lib/stores/theme';
	import { onMount } from 'svelte';
	import { loadHighlightTheme, getHighlightThemeName } from '$lib/utils/highlight-theme';

	interface Props {
		code: string;
		language?: string;
		class?: string;
	}

	let { code, language = 'json', class: className = '' }: Props = $props();

	// Load theme on mount and when theme changes
	onMount(() => {
		loadHighlightTheme(getHighlightThemeName($theme === 'dark'));
	});

	$effect(() => {
		loadHighlightTheme(getHighlightThemeName($theme === 'dark'));
	});

	// Highlight the code
	const highlightedCode = $derived(
		(() => {
			if (!code) return '';

			try {
				if (language && hljs.getLanguage(language)) {
					return hljs.highlight(code, { language }).value;
				} else {
					// Try auto-detection if specific language not found
					return hljs.highlightAuto(code).value;
				}
			} catch (error) {
				console.warn('Failed to highlight code:', error);
				return code; // Fallback to plain text
			}
		})()
	);
</script>

<pre
	class="{className} m-0 overflow-x-auto !border-none !bg-white p-1 font-mono text-xs leading-relaxed dark:!bg-slate-900"><code
		class="hljs w-full language-{language} text-slate-600 dark:text-slate-300"
		data-language={language}>{@html highlightedCode}</code
	></pre>

<style>
	/* Allow highlight.js themes to handle colors, only override background for transparency */
	:global(.hljs) {
		background: transparent !important;
	}
</style>
