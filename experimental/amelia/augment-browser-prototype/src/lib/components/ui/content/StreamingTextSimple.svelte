<script lang="ts">
	import { onMount } from 'svelte';

	interface Props {
		content: string;
		isStreaming?: boolean;
		speed?: number;
		catchUpSpeed?: number;
		catchUpThreshold?: number;
		disabled?: boolean;
		class?: string;
	}

	let {
		content = '',
		isStreaming = false,
		speed = 2,
		catchUpSpeed = 8,
		catchUpThreshold = 20,
		disabled = false,
		class: className = ''
	}: Props = $props();

	// Animation state
	let visibleLength = $state(0);
	let animationFrameId = $state<number | null>(null);
	let previousContent = $state('');

	// Derived state
	let targetLength = $derived(content.length);
	let displayContent = $derived(content.substring(0, visibleLength));
	let needsCatchUp = $derived(targetLength - visibleLength > catchUpThreshold);
	let shouldAnimate = $derived(!disabled && isStreaming && targetLength > 0);

	/**
	 * Main animation function
	 */
	function animate() {
		if (!shouldAnimate || visibleLength >= targetLength) {
			animationFrameId = null;
			return;
		}

		const currentSpeed = needsCatchUp ? catchUpSpeed : speed;
		visibleLength = Math.min(visibleLength + currentSpeed, targetLength);

		if (visibleLength < targetLength) {
			animationFrameId = requestAnimationFrame(animate);
		} else {
			animationFrameId = null;
		}
	}

	/**
	 * Start animation
	 */
	function startAnimation() {
		if (animationFrameId !== null) {
			cancelAnimationFrame(animationFrameId);
		}
		animationFrameId = requestAnimationFrame(animate);
	}

	/**
	 * Stop animation
	 */
	function stopAnimation() {
		if (animationFrameId !== null) {
			cancelAnimationFrame(animationFrameId);
			animationFrameId = null;
		}
	}

	// React to content changes
	$effect(() => {
		const contentChanged = content !== previousContent;
		const contentShrankOrChanged =
			content.length < previousContent.length ||
			(contentChanged && !content.startsWith(previousContent));

		if (contentShrankOrChanged) {
			visibleLength = 0;
		}

		previousContent = content;

		if (shouldAnimate && visibleLength < targetLength) {
			startAnimation();
		}
	});

	// React to streaming state changes
	$effect(() => {
		if (!isStreaming && visibleLength < targetLength) {
			visibleLength = targetLength;
			stopAnimation();
		} else if (isStreaming && visibleLength < targetLength) {
			startAnimation();
		}
	});

	// React to disabled state
	$effect(() => {
		if (disabled) {
			stopAnimation();
			visibleLength = targetLength;
		}
	});

	// Cleanup on unmount
	onMount(() => {
		return () => {
			stopAnimation();
		};
	});

	// Show full content immediately when not animating
	$effect(() => {
		if (!shouldAnimate) {
			visibleLength = targetLength;
		}
	});
</script>

<div class="whitespace-pre-wrap break-words {className}">
	{disabled || !shouldAnimate ? content : displayContent}
</div>
