<script lang="ts">
	import { Icon, DocumentDuplicate, ChevronDown } from 'svelte-hero-icons';
	import SyntaxHighlight from './SyntaxHighlight.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import SuccessfulButton from '../feedback/SuccessfulButton.svelte';

	interface Props {
		path: string;
		mode: string;
		language?: string;
		code: string;
		class?: string;
	}

	let { path, mode, language = 'text', code, class: className = '' }: Props = $props();

	// State
	let isExpanded = $state(true);
	let copySuccess = $state(false);

	// Derived values
	let fileName = $derived(path.split('/').pop() || path);
	let displayPath = $derived(path.split('/').slice(0, -1).join('/'));

	// Auto-detect language from file extension if not provided
	let detectedLanguage = $derived.by(() => {
		if (language && language !== 'text') return language;

		const extension = fileName.split('.').pop()?.toLowerCase();
		const languageMap: Record<string, string> = {
			js: 'javascript',
			ts: 'typescript',
			jsx: 'javascript',
			tsx: 'typescript',
			py: 'python',
			svelte: 'svelte',
			vue: 'vue',
			md: 'markdown',
			json: 'json',
			css: 'css',
			scss: 'scss',
			html: 'html',
			xml: 'xml',
			yaml: 'yaml',
			yml: 'yaml',
			sh: 'bash',
			bash: 'bash',
			zsh: 'bash',
			sql: 'sql',
			go: 'go',
			rs: 'rust',
			php: 'php',
			rb: 'ruby',
			java: 'java',
			c: 'c',
			cpp: 'cpp',
			cs: 'csharp',
			swift: 'swift',
			kt: 'kotlin'
		};

		return languageMap[extension || ''] || 'text';
	});

	// Copy code to clipboard
	async function copyCode() {
		try {
			await navigator.clipboard.writeText(code);
			return 'success';
		} catch (err) {
			console.error('Failed to copy code:', err);
			throw err;
		}
	}

	// Handle file path click (could be extended to open file)
	function handlePathClick() {
		// In a real implementation, this could open the file in an editor
		console.log('Open file:', path);
	}

	function toggleExpanded() {
		isExpanded = !isExpanded;
	}
</script>

<div
	class="my-4 flex min-h-0 flex-col overflow-hidden rounded-lg border border-slate-200 bg-white dark:border-slate-800 dark:bg-slate-950 {className}"
>
	<!-- Header -->
	<div
		class="flex items-center justify-between border-b border-slate-200 bg-slate-50 px-4 py-1.5 dark:border-slate-700 dark:bg-slate-900"
	>
		<div class="flex min-w-0 flex-1 items-center gap-3">
			<button
				class="flex flex-1 cursor-pointer items-center gap-1.5 truncate border-none bg-none p-0 text-xs font-medium text-slate-900 hover:text-slate-900 hover:underline dark:text-slate-300 dark:hover:text-slate-100"
				onclick={handlePathClick}
				title="Click to open file"
			>
				<div class="">
					{fileName}
				</div>

				<div class="text-slate-500 dark:text-slate-400">
					{displayPath}
				</div>
			</button>
		</div>

		<div class="flex flex-shrink-0 items-center gap-1">
			<SuccessfulButton
				onClick={copyCode}
				icon={DocumentDuplicate}
				tooltip={{ neutral: 'Copy code', success: 'Copied!' }}
			/>

			<Button
				variant="ghost"
				size="icon-sm"
				onclick={toggleExpanded}
				title={isExpanded ? 'Collapse' : 'Expand'}
				class="rounded-lg text-slate-500 transition-all duration-200 hover:bg-slate-200 hover:text-slate-700 dark:text-slate-400 dark:hover:bg-slate-700 dark:hover:text-slate-200"
			>
				<div class={`transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}>
					<Icon src={ChevronDown} class="h-4 w-4" />
				</div>
			</Button>
		</div>
	</div>

	<!-- Code content -->
	{#if isExpanded}
		<div class="min-h-0 flex-1 bg-slate-50 dark:bg-slate-900">
			<SyntaxHighlight
				{code}
				language={detectedLanguage}
				class="!my-0 rounded-none border-none text-slate-600 dark:text-slate-300 [&_code]:flex-1 [&_pre]:m-0 [&_pre]:flex [&_pre]:min-h-full [&_pre]:flex-col [&_pre]:rounded-none [&_pre]:border-none [&_pre]:bg-transparent"
			/>
		</div>
	{/if}
</div>
