<script lang="ts">
	import { marked } from 'marked';
	import hljs from 'highlight.js';
	import { theme } from '$lib/stores/theme';
	import { onMount } from 'svelte';
	import { loadHighlightTheme, getHighlightThemeName } from '$lib/utils/highlight-theme';
	import AugmentCodeSnippet from './AugmentCodeSnippet.svelte';

	interface Props {
		content: string;
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		class?: string;
		maxWidth?: 'none' | 'prose' | 'full';
		lineClamp?: number;
		hasNoPadding?: boolean;
		variant?: 'default' | 'invert' | 'muted';
		color?:
			| 'default'
			| 'red'
			| 'orange'
			| 'yellow'
			| 'green'
			| 'blue'
			| 'indigo'
			| 'purple'
			| 'pink'
			| 'gray'
			| 'slate'
			| string;
	}

	let {
		content,
		size = 'sm',
		class: className = '',
		maxWidth = 'none',
		lineClamp,
		hasNoPadding = false,
		variant = 'default',
		color = 'default'
	}: Props = $props();

	// Load theme on mount and when theme changes
	onMount(async () => {
		try {
			await loadHighlightTheme(getHighlightThemeName($theme === 'dark'));
		} catch (error) {
			console.warn('Failed to load initial highlight.js theme:', error);
		}
	});

	// Watch for theme changes
	$effect(async () => {
		try {
			await loadHighlightTheme(getHighlightThemeName($theme === 'dark'));
		} catch (error) {
			console.warn('Failed to load highlight.js theme:', error);
		}
	});

	// Configure marked for better security and formatting
	marked.setOptions({
		breaks: true,
		gfm: true
	});

	// Custom renderer for code blocks with syntax highlighting
	const renderer = new marked.Renderer();

	renderer.code = function ({ text: code, lang: language }: { text: string; lang?: string }) {
		// Apply syntax highlighting if language is specified
		let highlightedCode = code;
		if (language && hljs.getLanguage(language)) {
			try {
				highlightedCode = hljs.highlight(code, { language }).value;
			} catch (error) {
				console.warn('Failed to highlight code:', error);
				highlightedCode = hljs.highlightAuto(code).value;
			}
		} else if (language) {
			// Try auto-detection if specific language not found
			try {
				highlightedCode = hljs.highlightAuto(code).value;
			} catch (error) {
				console.warn('Failed to auto-highlight code:', error);
			}
		} else {
			// No language specified, try auto-detection
			try {
				highlightedCode = hljs.highlightAuto(code).value;
			} catch (error) {
				console.warn('Failed to auto-highlight code:', error);
				// Fallback to escaped HTML for plain text
				highlightedCode = code.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
			}
		}

		return `<pre class="hljs"><code class="hljs language-${language || 'plaintext'}">${highlightedCode}</code></pre>`;
	};

	// Store for augment code snippets
	let augmentCodeSnippets: Array<{
		id: string;
		path: string;
		mode: string;
		language: string;
		code: string;
	}> = [];

	// Simple processing function
	function processContent(text: string) {
		// Reset snippets array
		augmentCodeSnippets = [];

		// Convert augment_code_snippet XML to markdown
		let result = text.replace(
			/<augment_code_snippet\s+path="([^"]+)"\s+mode="([^"]+)">\s*```(\w+)?\s*([\s\S]*?)\s*```\s*<\/augment_code_snippet>/g,
			(match, path, mode, language, code) => {
				const snippetId = `augment-snippet-${augmentCodeSnippets.length}`;
				const cleanedCode = code.trim();

				augmentCodeSnippets.push({
					id: snippetId,
					path,
					mode,
					language: language || 'text',
					code: cleanedCode
				});

				return `<div data-augment-snippet="${snippetId}"></div>`;
			}
		);

		// Parse markdown
		const htmlContent = marked.parse(result, { renderer });

		// Split content into parts
		const parts: Array<{ type: 'html' | 'snippet'; content: string; snippetData?: any }> = [];
		const snippetRegex = /<div data-augment-snippet="([^"]+)"><\/div>/g;

		let lastIndex = 0;
		let match;

		while ((match = snippetRegex.exec(htmlContent)) !== null) {
			// Add HTML content before this snippet
			if (match.index > lastIndex) {
				const htmlPart = htmlContent.slice(lastIndex, match.index);
				if (htmlPart.trim()) {
					parts.push({ type: 'html', content: htmlPart });
				}
			}

			// Add snippet
			const snippetId = match[1];
			const snippetData = augmentCodeSnippets.find((s) => s.id === snippetId);
			if (snippetData) {
				parts.push({ type: 'snippet', content: '', snippetData });
			}

			lastIndex = match.index + match[0].length;
		}

		// Add remaining HTML content
		if (lastIndex < htmlContent.length) {
			const remainingHtml = htmlContent.slice(lastIndex);
			if (remainingHtml.trim()) {
				parts.push({ type: 'html', content: remainingHtml });
			}
		}

		// If no snippets were found, just return the HTML content
		if (parts.length === 0) {
			parts.push({ type: 'html', content: htmlContent });
		}

		return parts;
	}

	// Reactive statement to process content
	const contentParts = $derived(processContent(content));

	// Compute classes
	const sizeClasses = {
		xs: 'text-xs',
		sm: 'text-sm',
		md: 'text-base',
		lg: 'text-lg',
		xl: 'text-xl'
	};

	const maxWidthClasses = {
		none: '',
		prose: 'max-w-prose',
		full: 'max-w-full'
	};

	const variantClasses = {
		default: '',
		invert: 'text-white',
		muted: 'text-gray-600 dark:text-gray-400'
	};

	const isCustomColor = $derived(color && color !== 'default');

	const combinedClasses = $derived(
		[
			'prose prose-sm dark:prose-invert w-full max-w-none',
			sizeClasses[size],
			maxWidthClasses[maxWidth],
			variantClasses[variant],
			hasNoPadding ? '' : 'p-4',
			lineClamp ? `line-clamp-${lineClamp}` : '',
			className
		]
			.filter(Boolean)
			.join(' ')
	);
</script>

<div class={combinedClasses} style={isCustomColor ? `color: ${color}` : ''}>
	{#each contentParts as part, index (index)}
		{#if part.type === 'html'}
			{@html part.content}
		{:else if part.type === 'snippet' && part.snippetData}
			<AugmentCodeSnippet
				path={part.snippetData.path}
				mode={part.snippetData.mode}
				language={part.snippetData.language}
				code={part.snippetData.code}
			/>
		{/if}
	{/each}
</div>

<style>
	.prose {
		color: inherit;
	}

	/* Ensure code blocks have proper styling with fallback background */
	.prose :global(pre) {
		border-radius: 0.5rem;
		overflow-x: auto;
		white-space: pre-wrap;
		word-wrap: break-word;
		/* Provide fallback styling when highlight.js themes don't provide adequate styling */
		background-color: #f8f9fa !important;
		border: 1px solid #e9ecef !important;
		padding: 1rem !important;
		margin: 1rem 0 !important;
	}

	:global(.dark) .prose :global(pre) {
		background-color: #1e1e1e !important;
		border-color: #333 !important;
	}

	/* Let highlight.js themes override when they provide proper styling */
	.prose :global(pre.hljs) {
		background-color: var(--hljs-bg, #f8f9fa) !important;
		border: 1px solid var(--hljs-border, #e9ecef) !important;
	}

	:global(.dark) .prose :global(pre.hljs) {
		background-color: var(--hljs-bg, #1e1e1e) !important;
		border-color: var(--hljs-border, #333) !important;
	}

	/* Only style inline code, not code blocks */
	.prose :global(code:not(pre code)) {
		background-color: #f3f4f6;
		padding: 0.125rem 0.25rem;
		border-radius: 0.25rem;
		font-size: 0.8em;
		font-weight: 500;
	}

	:global(.dark) .prose :global(code:not(pre code)) {
		background-color: #1f2937;
	}

	/* Reset styles for code blocks to let highlight.js themes take control */
	.prose :global(pre code) {
		background-color: transparent !important;
		padding: 0;
		border-radius: 0;
		font-weight: inherit;
		white-space: pre-wrap;
		word-wrap: break-word;
		overflow-wrap: break-word;
	}

	/* Ensure highlight.js text colors are preserved */
	.prose :global(pre code.hljs) {
		color: inherit !important;
	}

	/* Better list styling */
	.prose :global(ul > li) {
		margin-top: 0.25rem;
		margin-bottom: 0.25rem;
	}

	.prose :global(ol > li) {
		margin-top: 0.25rem;
		margin-bottom: 0.25rem;
	}

	/* Improve link styling */
	.prose :global(a) {
		text-decoration: none;
		transition: all 0.2s ease;
	}

	.prose :global(a:hover) {
		text-decoration: underline;
	}
</style>
