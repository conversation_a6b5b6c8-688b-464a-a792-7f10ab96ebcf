<script lang="ts">
	import type { CleanChatExchangeData } from '$lib/api/unified-client';

	import { getRelativeTimeForStr } from '$lib/utils/time';

	interface Props {
		chatHistory: CleanChatExchangeData[];
		showTimestamps?: boolean;
		showUserMessages?: boolean;
		showAgentMessages?: boolean;
		maxHeight?: string;
		class?: string;
	}

	let {
		chatHistory = [],
		showTimestamps = true,
		showUserMessages = true,
		showAgentMessages = true,
		maxHeight = '400px',
		class: className = ''
	}: Props = $props();
</script>

<div class="chat-history {className}" style="max-height: {maxHeight};">
	<div class="h-full overflow-y-auto">
		{#if chatHistory.length > 0}
			<div class="space-y-6 p-4">
				{#each chatHistory as exchange, index (exchange.exchange.requestId || `exchange-${index}`)}
					<div class="exchange-group">
						<!-- Exchange Header -->
						{#if showTimestamps && exchange.finishedAt}
							<div class="exchange-timestamp mb-3 text-xs text-gray-400 dark:text-gray-500">
								{getRelativeTimeForStr(exchange.finishedAt)}
							</div>
						{/if}

						<!-- User Message -->
						{#if showUserMessages && exchange.exchange.requestNodes?.length > 0}
							<div class="mb-3 flex justify-end">
								<div class="message-bubble max-w-[80%] rounded-lg bg-blue-600 px-4 py-3 text-white">
									<div class="message-role mb-2 text-xs font-medium opacity-70">You</div>
									<div class="message-content text-sm break-words whitespace-pre-wrap">
										{#each exchange.exchange.requestNodes as node}
											{#if node.textNode?.content}
												{node.textNode.content}
											{:else if exchange.exchange.requestMessage}
												{exchange.exchange.requestMessage}
											{/if}
										{/each}
									</div>
								</div>
							</div>
						{/if}

						<!-- Agent Response -->
						{#if showAgentMessages && exchange.exchange.responseNodes && exchange.exchange.responseNodes.length > 0}
							<div class="flex justify-start">
								<div
									class="message-bubble max-w-[80%] rounded-lg bg-gray-100 px-4 py-3 text-gray-900 dark:bg-gray-800 dark:text-white"
								>
									<div class="message-role mb-2 text-xs font-medium opacity-70">Agent</div>

									<!-- Show response content -->
									{#each exchange.exchange.responseNodes as node}
										{#if node.textNode?.content}
											<div class="message-content text-sm break-words whitespace-pre-wrap">
												{node.textNode.content}
											</div>
										{/if}
									{/each}

									<!-- Fallback to responseText if no nodes -->
									{#if exchange.exchange.responseText && exchange.exchange.responseNodes.length === 0}
										<div class="message-content text-sm break-words whitespace-pre-wrap">
											{exchange.exchange.responseText}
										</div>
									{/if}
								</div>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		{/if}
	</div>
</div>

<style>
	.chat-history {
		border: 1px solid rgb(229 231 235);
		border-radius: 0.5rem;
		background-color: white;
	}

	:global(.dark) .chat-history {
		border-color: rgb(55 65 81);
		background-color: rgb(17 24 39);
	}

	.message-bubble {
		box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
		word-wrap: break-word;
		overflow-wrap: break-word;
	}

	.message-content {
		line-height: 1.5;
	}

	.tool-calls {
		border-top: 1px solid rgba(0, 0, 0, 0.1);
		padding-top: 0.75rem;
	}

	:global(.dark) .tool-calls {
		border-top-color: rgba(255, 255, 255, 0.1);
	}

	/* Scrollbar styling */
	.chat-history ::-webkit-scrollbar {
		width: 6px;
	}

	.chat-history ::-webkit-scrollbar-track {
		background-color: rgb(243 244 246);
	}

	:global(.dark) .chat-history ::-webkit-scrollbar-track {
		background-color: rgb(31 41 55);
	}

	.chat-history ::-webkit-scrollbar-thumb {
		background-color: rgb(209 213 219);
		border-radius: 9999px;
	}

	:global(.dark) .chat-history ::-webkit-scrollbar-thumb {
		background-color: rgb(75 85 99);
	}

	.chat-history ::-webkit-scrollbar-thumb:hover {
		background-color: rgb(156 163 175);
	}

	:global(.dark) .chat-history ::-webkit-scrollbar-thumb:hover {
		background-color: rgb(107 114 128);
	}
</style>
