<script lang="ts">
	import {
		sendStreamingChatCompletion,
		sendChatCompletion,
		extractTextFromResponse
	} from '$lib/api/chat';
	import type { ChatMessage } from '$lib/api/chat';
	import { Trash, PaperAirplane } from 'svelte-hero-icons';
	import Button from '../navigation/Button.svelte';
	import SummaryRenderer from '../../chat-history/nodes/SummaryRenderer.svelte';
	import PromptEnhancer from '../PromptEnhancer.svelte';
	import type { PromptEnhancementResult } from '$lib/utils/prompts';

	let messages: ChatMessage[] = [];
	let currentInput = '';
	let isLoading = false;
	let streamingResponse = '';
	let useStreaming = true;

	async function sendMessage() {
		if (!currentInput.trim() || isLoading) return;

		const userMessage: ChatMessage = {
			role: 'user',
			content: currentInput.trim()
		};

		messages = [...messages, userMessage];
		currentInput = '';
		isLoading = true;
		streamingResponse = '';

		try {
			if (useStreaming) {
				// Add placeholder for assistant response
				messages = [...messages, { role: 'assistant', content: '' }];

				await sendStreamingChatCompletion(
					messages.slice(0, -1), // Don't include the empty assistant message
					(chunk) => {
						if (chunk.type === 'content_delta' && chunk.text) {
							streamingResponse += chunk.text;
							// Update the last message (assistant response)
							messages = [
								...messages.slice(0, -1),
								{ role: 'assistant', content: streamingResponse }
							];
						} else if (chunk.type === 'error') {
							console.error('Streaming error:', chunk.error);
						}
					}
				);
			} else {
				const response = await sendChatCompletion(messages);
				const assistantMessage: ChatMessage = {
					role: 'assistant',
					content: extractTextFromResponse(response)
				};
				messages = [...messages, assistantMessage];
			}
		} catch (error) {
			console.error('Chat error:', error);
			const errorMessage: ChatMessage = {
				role: 'assistant',
				content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`
			};
			messages = [...messages, errorMessage];
		} finally {
			isLoading = false;
			streamingResponse = '';
		}
	}

	function clearChat() {
		messages = [];
		streamingResponse = '';
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			sendMessage();
		}
	}

	function getCurrentPrompt(): string {
		return currentInput;
	}

	function handlePromptEnhanced(enhancedPrompt: string) {
		currentInput = enhancedPrompt;
	}

	function handlePromptEnhancementError(error: PromptEnhancementResult) {
		console.error('Prompt enhancement error:', error);
		// Could show a toast notification here in the future
	}
</script>

<div class="mx-auto flex h-full max-w-4xl flex-col">
	<!-- Header -->
	<div class="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
		<h2 class="text-lg font-semibold text-gray-900 dark:text-white">Chat with Claude</h2>
		<div class="flex items-center gap-4">
			<label class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
				<input
					type="checkbox"
					bind:checked={useStreaming}
					class="rounded border-gray-300 dark:border-gray-600"
				/>
				Streaming
			</label>
			<Button
				variant="ghost"
				size="icon-sm"
				icon={Trash}
				onclick={clearChat}
				class="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
				aria-label="Clear chat"
			/>
		</div>
	</div>

	<!-- Messages -->
	<div class="flex-1 space-y-4 overflow-y-auto p-4">
		{#each messages as message}
			<div class="flex {message.role === 'user' ? 'justify-end' : 'justify-start'}">
				<div
					class="max-w-[80%] {message.role === 'user'
						? 'bg-blue-600 text-white'
						: 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white'} rounded-lg px-4 py-2"
				>
					<div class="mb-1 text-xs font-medium opacity-70">
						{message.role === 'user' ? 'You' : 'Claude'}
					</div>
					<SummaryRenderer content={message.content} shouldUseMarkdown={false} />
				</div>
			</div>
		{/each}

		{#if isLoading && !useStreaming}
			<div class="flex justify-start">
				<div class="rounded-lg bg-gray-100 px-4 py-2 dark:bg-gray-800">
					<div class="flex items-center gap-2">
						<div class="h-4 w-4 animate-spin rounded-full border-b-2 border-gray-600"></div>
						<span class="text-gray-600 dark:text-gray-400">Claude is thinking...</span>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<!-- Input -->
	<div class="border-t border-gray-200 p-4 dark:border-gray-700">
		<div class="flex gap-2">
			<div class="relative flex-1">
				<textarea
					bind:value={currentInput}
					onkeypress={handleKeyPress}
					placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
					class="w-full resize-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-10 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
					rows="3"
					disabled={isLoading}
				></textarea>
				<div class="absolute top-2 right-2">
					<PromptEnhancer
						{getCurrentPrompt}
						onEnhanced={handlePromptEnhanced}
						onError={handlePromptEnhancementError}
						isDisabled={isLoading}
					/>
				</div>
			</div>
			<Button
				variant="primary"
				size="icon-sm"
				icon={PaperAirplane}
				onclick={sendMessage}
				disabled={isLoading || !currentInput.trim()}
				class="px-4 py-2"
			/>
		</div>
	</div>
</div>
