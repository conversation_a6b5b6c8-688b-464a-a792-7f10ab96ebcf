<script lang="ts">
	import { onMount } from 'svelte';
	import SyntaxHighlight from './SyntaxHighlight.svelte';

	interface Props {
		value: string;
		language?: string;
		readonly?: boolean;
		height?: string;
		class?: string;
		onChange?: (value: string) => void;
	}

	let {
		value,
		language = 'text',
		readonly = false,
		height = '200px',
		class: className = '',
		onChange
	}: Props = $props();

	let editorContainer: HTMLDivElement;
	let monaco: any;
	let editor: any;

	onMount(async () => {
		// For now, we'll use a simple syntax-highlighted textarea
		// In a full implementation, you would load Monaco Editor here
		// This is a fallback implementation using SyntaxHighlight
	});

	function handleInput(event: Event) {
		const target = event.target as HTMLTextAreaElement;
		onChange?.(target.value);
	}
</script>

<div
	bind:this={editorContainer}
	class="monaco-editor-container {className}"
	style="height: {height}"
>
	{#if readonly}
		<!-- Read-only view with syntax highlighting -->
		<div
			class="h-full overflow-auto rounded-lg border border-slate-200 bg-slate-50 dark:border-slate-700 dark:bg-slate-900"
		>
			<SyntaxHighlight code={value} {language} class="h-full" />
		</div>
	{:else}
		<!-- Editable textarea fallback -->
		<textarea
			{value}
			oninput={handleInput}
			class="h-full w-full resize-none rounded-lg border border-slate-200 bg-white p-3 font-mono text-sm dark:border-slate-700 dark:bg-slate-800 dark:text-white"
			placeholder="Enter code here..."
		></textarea>
	{/if}
</div>

<style>
	.monaco-editor-container {
		position: relative;
		border-radius: 0.375rem;
		overflow: hidden;
	}
</style>
