<script lang="ts">
	import { sendSilentExchange } from '$lib/api/chat';
	import {
		getInputWithEnhancePrompt,
		extractEnhancedPrompt,
		canEnhancePrompt,
		PromptEnhancementError,
		type PromptEnhancementResult,
		type PromptEnhancementContext
	} from '$lib/utils/prompts';
	import { fly } from 'svelte/transition';
	import Button from './navigation/Button.svelte';
	import { Sparkles, XMark } from 'svelte-hero-icons';
	import Tooltip from './overlays/Tooltip.svelte';

	import Confetti from './Confetti.svelte';

	let showConfetti = $state(false);

	interface Props {
		/**
		 * The current prompt text to potentially enhance
		 */
		prompt: string;

		/**
		 * Callback function called when a prompt is successfully enhanced
		 * @param enhancedPrompt - The enhanced version of the prompt
		 */
		onEnhanced: (enhancedPrompt: string) => void;

		/**
		 * Whether the enhancer button should be disabled
		 */
		isDisabled?: boolean;

		/**
		 * Reason why the button is disabled (for tooltip)
		 */
		disabledReason?: string;

		/**
		 * Callback for when an error occurs during enhancement
		 * @param error - The error that occurred
		 */
		onError?: (error: PromptEnhancementResult) => void;

		/**
		 * Additional CSS classes to apply to the button
		 */
		class?: string;

		/**
		 * Optional context information to help with prompt enhancement
		 */
		context?: PromptEnhancementContext;
	}

	let {
		prompt,
		onEnhanced,
		isDisabled = false,
		disabledReason,
		onError,
		class: className = '',
		context
	}: Props = $props();

	let isEnhancing = $state(false);
	let isAnimating = $state(false);
	let abortController: AbortController | null = $state(null);
	let isHovered = $state(false);

	// Derived state to determine if we should show the enhancer
	let shouldShowEnhancer = $derived(canEnhancePrompt(prompt));

	/**
	 * Cancels the current prompt enhancement
	 */
	function cancelEnhancement(): void {
		if (abortController) {
			abortController.abort();
			abortController = null;
		}
		isEnhancing = false;
		console.log('Prompt enhancement cancelled');
	}

	/**
	 * Enhances the current prompt using the silent exchange API
	 */
	async function enhancePrompt(): Promise<void> {
		// Validate the prompt before attempting enhancement
		if (!canEnhancePrompt(prompt)) {
			const error: PromptEnhancementResult = {
				success: false,
				error: PromptEnhancementError.EMPTY_PROMPT,
				errorMessage: 'Prompt is too short or empty to enhance'
			};
			onError?.(error);
			return;
		}

		if (isEnhancing) return;

		isEnhancing = true;
		abortController = new AbortController();

		try {
			// Send the enhancement request using silent exchange
			const result = await sendSilentExchange({
				message: getInputWithEnhancePrompt(prompt, context)
			});
			if (abortController?.signal.aborted || !isEnhancing) {
				console.log('Enhancement request was cancelled');
				return;
			}

			// Extract the enhanced prompt from the response
			const enhancedPrompt = extractEnhancedPrompt(result.text);

			if (enhancedPrompt) {
				// Trigger success animation
				console.log('Triggering confetti animation');
				isAnimating = true;
				showConfetti = true;
				setTimeout(() => {
					isAnimating = false;
					showConfetti = false;
				}, 700);

				onEnhanced(enhancedPrompt);
			} else {
				console.warn('Enhanced prompt tags not found in response');
				const error: PromptEnhancementResult = {
					success: false,
					error: PromptEnhancementError.PARSING_ERROR,
					errorMessage: 'Failed to parse enhanced prompt from response'
				};
				onError?.(error);
			}
		} catch (err) {
			// Don't show error if it was cancelled
			if (abortController?.signal.aborted) {
				console.log('Enhancement request was cancelled');
				return;
			}

			console.error('Error enhancing prompt:', err);
			const error: PromptEnhancementResult = {
				success: false,
				error: PromptEnhancementError.NETWORK_ERROR,
				errorMessage: err instanceof Error ? err.message : 'Unknown network error'
			};
			onError?.(error);
		} finally {
			isEnhancing = false;
			abortController = null;
		}
	}

	// Determine if the button should be disabled
	let shouldDisable = $state(false);
	let tooltipText = $state('Enhance prompt');

	// Update these values reactively but safely
	$effect(() => {
		shouldDisable = isDisabled || isEnhancing || !shouldShowEnhancer;

		if (disabledReason) {
			tooltipText = disabledReason;
		} else if (isEnhancing) {
			tooltipText = 'Enhancing prompt (click to cancel)';
		} else if (!shouldShowEnhancer) {
			tooltipText = 'Prompt too short to enhance';
		} else {
			tooltipText = 'Enhance prompt';
		}
	});
</script>

<div class="relative">
	{#if shouldShowEnhancer}
		<div
			class=""
			role="button"
			tabindex="-1"
			transition:fly={{ y: 6 }}
			onmouseenter={() => (isHovered = true)}
			onmouseleave={() => (isHovered = false)}
		>
			<Tooltip
				text={tooltipText}
				tooltipClass="whitespace-nowrap"
				maxWidth="300px"
				position="left"
				delay={0}
			>
				<Button
					variant="ghost"
					size="icon-sm"
					icon={isEnhancing ? XMark : Sparkles}
					onclick={isEnhancing ? cancelEnhancement : enhancePrompt}
					disabled={shouldDisable && !isEnhancing}
					loading={isEnhancing && !isHovered}
					title={tooltipText}
					aria-label={tooltipText}
					class="{className} transition-all duration-200 {isAnimating
						? 'scale-110'
						: ''} {isEnhancing ? 'text-red-500 hover:text-red-600' : ''}"
				/>
			</Tooltip>
		</div>
	{/if}

	<!-- Confetti animation -->
	<Confetti trigger={showConfetti} />
</div>
