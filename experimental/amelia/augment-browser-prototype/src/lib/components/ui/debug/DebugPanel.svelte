<script lang="ts">
	import { debugSettings, updateExploratoryMode } from '$lib/stores/global-state.svelte';
	import { Icon, Cog6Tooth, XMark } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Switch from '$lib/components/ui/forms/Switch.svelte';

	interface Props {
		open?: boolean;
		onClose?: () => void;
	}

	let { open = false, onClose }: Props = $props();

	function handleClose() {
		onClose?.();
	}

	function handleExploratoryModeChange(enabled: boolean) {
		updateExploratoryMode(enabled);
	}
</script>

{#if open}
	<!-- Backdrop -->
	<div
		class="fixed inset-0 bg-black/50 z-50"
		onclick={handleClose}
		onkeydown={(e) => e.key === 'Escape' && handleClose()}
		role="button"
		tabindex="-1"
		aria-label="Close debug panel"
	></div>

	<!-- Debug Panel -->
	<div class="fixed top-4 right-4 w-80 bg-white dark:bg-slate-800 rounded-lg shadow-xl border border-slate-200 dark:border-slate-700 z-50">
		<!-- Header -->
		<div class="flex items-center justify-between p-4 border-b border-slate-200 dark:border-slate-700">
			<div class="flex items-center gap-2">
				<Icon src={Cog6Tooth} class="w-5 h-5 text-slate-600 dark:text-slate-400" />
				<h3 class="text-lg font-semibold text-slate-900 dark:text-white">Debug Panel</h3>
			</div>
			<Button
				variant="ghost"
				size="sm"
				onclick={handleClose}
				icon={XMark}
				class="!p-1"
				aria-label="Close debug panel"
			/>
		</div>

		<!-- Content -->
		<div class="p-4 space-y-4">
			<!-- Exploratory Mode -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
					Navigation
				</label>
				<div class="space-y-3">
					<div class="flex items-center justify-between">
						<div class="flex-1">
							<div class="text-sm font-medium text-slate-900 dark:text-white">
								Exploratory Mode
							</div>
							<div class="text-xs text-slate-500 dark:text-slate-400">
								Show Inbox and Explore tabs
							</div>
						</div>
						<Switch
							checked={$debugSettings.exploratoryMode}
							onchange={handleExploratoryModeChange}
						/>
					</div>
				</div>
			</div>

			<!-- Debug Info -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-slate-700 dark:text-slate-300">
					Debug Info
				</label>
				<div class="bg-slate-50 dark:bg-slate-900 rounded-md p-3 text-xs font-mono">
					<div class="space-y-1">
						<div class="text-slate-600 dark:text-slate-400">
							Exploratory Mode: <span class="text-slate-900 dark:text-white">{$debugSettings.exploratoryMode}</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Footer -->
		<div class="p-4 border-t border-slate-200 dark:border-slate-700">
			<div class="text-xs text-slate-500 dark:text-slate-400 text-center">
				Debug settings are stored in global state
			</div>
		</div>
	</div>
{/if}
