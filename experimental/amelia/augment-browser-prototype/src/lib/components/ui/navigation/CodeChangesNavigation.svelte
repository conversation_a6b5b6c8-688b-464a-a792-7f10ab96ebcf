<script lang="ts">
	import { ChevronLeft, ChevronRight } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import type { CleanChatExchangeData } from '$lib/api/unified-client';
	import ButtonGroup from './ButtonGroup.svelte';

	interface Props {
		exchanges: CleanChatExchangeData[];
		currentViewIndex: number;
		onNavigate: (index: number) => void;
		onScrollToExchange?: (exchangeIndex: number) => void;
	}

	let { exchanges, currentViewIndex, onNavigate, onScrollToExchange }: Props = $props();

	// Check if we're viewing all changes or a specific exchange
	let isViewingAllChanges = $derived(currentViewIndex === -1);

	// Navigation functions for exchanges
	function navigatePreviousExchange() {
		if (!isViewingAllChanges && currentViewIndex > 0) {
			// Find previous exchange with changes
			for (let i = currentViewIndex - 1; i >= 0; i--) {
				if (exchanges[i].changedFiles && exchanges[i].changedFiles.length > 0) {
					onNavigate(i);
					// Automatically scroll to the exchange
					if (onScrollToExchange) {
						onScrollToExchange(i);
					}
					break;
				}
			}
		}
	}

	function navigateNextExchange() {
		if (!isViewingAllChanges && currentViewIndex < exchanges.length - 1) {
			// Find next exchange with changes
			for (let i = currentViewIndex + 1; i < exchanges.length; i++) {
				if (exchanges[i].changedFiles && exchanges[i].changedFiles.length > 0) {
					onNavigate(i);
					// Automatically scroll to the exchange
					if (onScrollToExchange) {
						onScrollToExchange(i);
					}
					break;
				}
			}
		}
	}

	// Check if navigation is possible
	let canNavigatePrevious = $derived(() => {
		if (isViewingAllChanges) return false;
		for (let i = currentViewIndex - 1; i >= 0; i--) {
			if (exchanges[i].changedFiles && exchanges[i].changedFiles.length > 0) {
				return true;
			}
		}
		return false;
	});

	let canNavigateNext = $derived(() => {
		if (isViewingAllChanges) return false;
		for (let i = currentViewIndex + 1; i < exchanges.length; i++) {
			if (exchanges[i].changedFiles && exchanges[i].changedFiles.length > 0) {
				return true;
			}
		}
		return false;
	});

	// Get current exchange group number for display (corresponds to chat history grouping)
	// Groups start with user messages, and all subsequent assistant responses belong to that group
	let currentExchangeGroupNumber = $derived(() => {
		if (isViewingAllChanges) {
			// When viewing all, show the first group number that has changes
			for (let i = 0; i < exchanges.length; i++) {
				if (exchanges[i].changedFiles && exchanges[i].changedFiles.length > 0) {
					return calculateGroupNumber(i);
				}
			}
			return 1;
		}
		return calculateGroupNumber(currentViewIndex);
	});

	// Calculate which group an exchange belongs to based on chat history grouping logic
	function calculateGroupNumber(exchangeIndex: number): number {
		let groupNumber = 1; // Groups are numbered starting from 1 for display

		for (let i = 0; i <= exchangeIndex; i++) {
			const exchange = exchanges[i];
			// Check if this exchange has user content (starts a new group)
			const hasUserContent =
				exchange.exchange?.requestNodes?.some(
					(node: any) => node.content && node.content.trim().length > 0
				) ||
				(exchange.exchange?.requestMessage && exchange.exchange.requestMessage.trim().length > 0);

			// If this exchange has user content and it's not the first exchange, increment group number
			if (hasUserContent && i > 0) {
				groupNumber++;
			}
		}

		return groupNumber;
	}

	let totalExchangesWithChanges = $derived.by(() => {
		return exchanges.filter((exchange) => exchange.changedFiles && exchange.changedFiles.length > 0)
			.length;
	});
</script>

<div class="flex flex-wrap items-center justify-between">
	<h3 class="flex-1 text-lg font-semibold whitespace-nowrap text-slate-900 dark:text-slate-100">
		Code Changes
	</h3>

	<!-- {#if totalExchangesWithChanges > 0}
		<div class="flex items-center justify-between">
			<ButtonGroup>
				<Button
					variant={isViewingAllChanges ? 'primary' : 'secondary'}
					size="sm"
					onclick={() => onNavigate(-1)}
				>
					All agent changes
				</Button>

				{#if totalExchangesWithChanges > 1}

					<Button
						class="!py-1 !pr-1"
						variant={isViewingAllChanges ? 'secondary' : 'primary'}
						onclick={() => {
							if (isViewingAllChanges && totalExchangesWithChanges > 0) {
								// Switch to the first exchange with changes
								for (let i = 0; i < exchanges.length; i++) {
									if (exchanges[i].changedFiles && exchanges[i].changedFiles.length > 0) {
										onNavigate(i);
										if (onScrollToExchange) {
											onScrollToExchange(i);
										}
										break;
									}
								}
							}
						}}
					>
						<div class="flex items-center">
							Changes from message {currentExchangeGroupNumber()}

							<ButtonGroup class="ml-3">
								<Button
									variant="secondary"
									size="icon-sm"
									onclick={(e) => {
										e.stopPropagation();
										navigatePreviousExchange();
									}}
									disabled={isViewingAllChanges || !canNavigatePrevious()}
									aria-label="Previous exchange"
									icon={ChevronLeft}
								/>
								<Button
									variant="secondary"
									size="icon-sm"
									onclick={(e) => {
										e.stopPropagation();
										navigateNextExchange();
									}}
									disabled={isViewingAllChanges || !canNavigateNext()}
									aria-label="Next exchange"
									icon={ChevronRight}
								/>
							</ButtonGroup>
						</div>
					</Button>
				{/if}
			</ButtonGroup>
		</div>
	{/if} -->
</div>
