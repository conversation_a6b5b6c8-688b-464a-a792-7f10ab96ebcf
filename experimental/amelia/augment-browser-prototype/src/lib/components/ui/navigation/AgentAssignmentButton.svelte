<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import RemoteAgentStatusIndicator from '$lib/components/ui/feedback/RemoteAgentStatusIndicator.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { ArrowDown, ArrowRight, Icon } from 'svelte-hero-icons';

	interface Props {
		entity: UnifiedEntity;
		isShowingForm?: boolean;
		trigger?: NormalizedTrigger;
		remoteAgent?: CleanRemoteAgent | null;
		hasText?: boolean;
		isAnimating?: boolean;
		isCreatingAgent?: boolean;
		onStartWork?: (entity: UnifiedEntity) => void;
		onAgentClick?: (agent: CleanRemoteAgent) => void;
		size?: 'icon-sm' | 'sm' | 'md' | 'lg';
		variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
	}

	let {
		entity,
		isShowingForm = $bindable(false),
		trigger,
		remoteAgent = null,
		hasText = true,
		isAnimating = false,
		isCreatingAgent = false,
		onStartWork,
		onAgentClick,
		size = 'sm',
		variant = 'secondary'
	}: Props = $props();

	function handleStartWork(e?: MouseEvent) {
		if (e) e.stopPropagation();
		if (onStartWork && entity) {
			onStartWork(entity);
		}
	}

	function handleAgentClick(e?: MouseEvent) {
		if (e) e.stopPropagation();
		if (remoteAgent && onAgentClick) {
			onAgentClick(remoteAgent);
		}
	}
</script>

{#if remoteAgent}
	<Button {variant} {size} onclick={handleAgentClick} class="group/button flex-shrink-0">
		<div class="flex items-center gap-2">
			<RemoteAgentStatusIndicator
				status={remoteAgent.status}
				hasUpdates={remoteAgent.hasUpdates}
				workspaceStatus={remoteAgent.workspaceStatus}
				variant="sleek"
				size="sm"
				isExpanded={false}
			/>
			<span
				class="text-sm font-medium {hasText
					? ''
					: 'flex w-0 opacity-0 transition-all duration-200 group-hover/button:w-[8em] group-hover/button:opacity-100'}"
				>View Agent</span
			>
			<Icon src={ArrowRight} class="h-4 w-4" micro />
		</div>
	</Button>
{:else if entity}
	<Button
		{variant}
		{size}
		onclick={handleStartWork}
		disabled={isAnimating || isCreatingAgent}
		class="group/button flex-shrink-0"
	>
		<div class="flex items-center">
			<div
				class={hasText
					? 'mr-2'
					: 'flex w-0 opacity-0 transition-all duration-200 group-hover/button:w-[8em] group-hover/button:opacity-100'}
			>
				{#if isAnimating || isCreatingAgent}
					<div class="flex items-center gap-2">
						<div
							class="h-4 w-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"
						></div>
						{isCreatingAgent ? 'Creating agent...' : 'Starting work...'}
					</div>
				{:else}
					Assign to Agent
				{/if}
			</div>
			<Icon
				src={ArrowDown}
				class="h-4 w-4 transform transition-transform {isShowingForm ? 'rotate-180' : ''}"
				micro
			/>
		</div>
	</Button>
{/if}
