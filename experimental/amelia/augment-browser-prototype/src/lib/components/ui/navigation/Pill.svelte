<script lang="ts">
	import { Icon } from 'svelte-hero-icons';
	import type { IconSource } from 'svelte-hero-icons';

	interface Props {
		variant?: 'default' | 'blue' | 'green' | 'gray' | 'purple' | 'red' | 'yellow';
		size?: 'xs' | 'sm' | 'md';
		icon?: IconSource;
		iconPosition?: 'left' | 'right';
		href?: string;
		target?: string;
		onclick?: (event: MouseEvent) => void;
		class?: string;
		children?: any;
		'aria-label'?: string;
		[key: string]: any;
	}

	let {
		variant = 'default',
		size = 'sm',
		icon,
		iconPosition = 'left',
		href,
		target,
		onclick,
		class: className = '',
		children,
		...restProps
	}: Props = $props();

	// Base styles for pill shape and layout
	const baseStyles =
		'inline-flex items-center font-medium rounded-full transition-all duration-200 select-none';

	// Variant styles - matching the design from the image
	const variantStyles = {
		default:
			'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700',
		blue: 'bg-sky-100 text-sky-700 border border-sky-200 hover:bg-sky-200 dark:bg-sky-900/50 dark:text-sky-300 dark:border-sky-800 dark:hover:bg-sky-900/70',
		green:
			'bg-green-100 text-green-700 border border-green-200 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-300 dark:border-green-800 dark:hover:bg-green-900/70',
		gray: 'bg-gray-100 text-gray-600 border border-gray-200 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700 dark:hover:bg-gray-700',
		purple:
			'bg-purple-100 text-purple-700 border border-purple-200 hover:bg-purple-200 dark:bg-purple-900/50 dark:text-purple-300 dark:border-purple-800 dark:hover:bg-purple-900/70',
		red: 'bg-red-100 text-red-700 border border-red-200 hover:bg-red-200 dark:bg-red-900/50 dark:text-red-300 dark:border-red-800 dark:hover:bg-red-900/70',
		yellow:
			'bg-yellow-100 text-yellow-700 border border-yellow-200 hover:bg-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:border-yellow-800 dark:hover:bg-yellow-900/70'
	};

	// Size styles
	const sizeStyles = {
		xs: 'px-2 py-0.5 pb-1 text-xs gap-1',
		sm: 'px-2.5 py-1 pb-1.5 text-xs gap-1.5',
		md: 'px-3 py-1.5 pb-2 text-sm gap-2'
	};

	// Icon sizes
	const iconSizes = {
		xs: 'w-3 h-3',
		sm: 'w-3.5 h-3.5',
		md: 'w-4 h-4'
	};

	const pillClass = $derived(
		`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${className}`
	);
	const iconClass = $derived(iconSizes[size]);

	function handleClick(event: MouseEvent) {
		if (onclick) {
			onclick(event);
		}
	}
</script>

{#if href}
	<a {href} {target} class={pillClass} onclick={handleClick} {...restProps}>
		{#if icon && iconPosition === 'left'}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}

		{#if children}
			<span class="truncate leading-none">
				{@render children()}
			</span>
		{/if}

		{#if icon && iconPosition === 'right'}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}
	</a>
{:else}
	<span class={pillClass} onclick={handleClick} {...restProps}>
		{#if icon && iconPosition === 'left'}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}

		{#if children}
			<span class="truncate leading-none">
				{@render children()}
			</span>
		{/if}

		{#if icon && iconPosition === 'right'}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}
	</span>
{/if}
