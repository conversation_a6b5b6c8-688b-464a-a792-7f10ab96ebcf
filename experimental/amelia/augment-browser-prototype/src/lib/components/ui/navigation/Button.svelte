<script lang="ts">
	import { Icon } from 'svelte-hero-icons';
	import type { IconSource } from 'svelte-hero-icons';
	import LoadingIndicator from '../feedback/LoadingIndicator.svelte';

	interface Props {
		variant?:
			| 'primary'
			| 'secondary'
			| 'ghost'
			| 'ghost-light'
			| 'ghost-inline'
			| 'destructive'
			| 'outline'
			| 'success'
			| 'pill-blue'
			| 'pill-green'
			| 'pill-slate';
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'icon-xs' | 'icon-sm' | 'icon-md' | 'icon-lg' | 'pill';
		disabled?: boolean;
		loading?: boolean;
		icon?: IconSource;
		iconPosition?: 'left' | 'right';
		type?: 'button' | 'submit' | 'reset';
		isMultiline?: boolean;
		class?: string;
		onclick?: (event: MouseEvent) => void;
		href?: string;
		element?: HTMLButtonElement | HTMLAnchorElement;
		target?: '_blank' | '_self' | '_parent' | '_top';
		children?: any;
		'aria-label'?: string;
		[key: string]: any;
	}

	let {
		variant = 'primary',
		size = 'md',
		disabled = false,
		loading = false,
		icon,
		iconPosition = 'left',
		type = 'button',
		isMultiline = false,
		class: className = '',
		onclick,
		href,
		element: buttonElement = $bindable(),
		target,
		children,
		...restProps
	}: Props = $props();

	// Base styles - Modern, clean foundation with consistent alignment
	const baseStyles = $derived(
		`inline-flex items-center font-medium transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none select-none shrink-0 cursor-pointer ${isMultiline ? 'text-left' : 'justify-center'}`
	);

	// Variant styles - Vercel-inspired modern design
	const variantStyles = {
		primary:
			'border border-transparent bg-black dark:bg-slate-700 text-white dark:text-white hover:bg-slate-800 dark:hover:bg-slate-600 active:bg-slate-900 dark:active:bg-slate-800 shadow-sm hover:shadow-md',
		secondary:
			'bg-white dark:bg-slate-800 text-slate-900 dark:text-white border border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 hover:bg-slate-50 dark:hover:bg-slate-700 active:bg-slate-100 dark:active:bg-slate-600 shadow-sm hover:shadow-md',
		ghost:
			'border border-transparent text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-800 active:bg-slate-200 dark:active:bg-slate-700 focus:ring-2 focus:ring-blue-500',
		'ghost-light':
			'border border-transparent text-slate-400 dark:text-slate-500 hover:text-slate-700 dark:hover:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 active:bg-slate-200 dark:active:bg-slate-700',
		'ghost-inline':
			'border border-transparent text-slate-500 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white inline-flex items-center gap-1',
		destructive:
			'bg-white dark:bg-slate-800 text-rose-800 dark:text-rose-400 border border-rose-800 dark:border-rose-600 hover:bg-rose-50 dark:hover:bg-rose-900/20 active:bg-rose-50 dark:active:bg-rose-900/30 shadow-sm hover:shadow-md',
		outline:
			'border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:border-slate-400 dark:hover:border-slate-500 hover:bg-slate-50 dark:hover:bg-slate-800 active:bg-slate-100 dark:active:bg-slate-700',
		success:
			'border border-transparent bg-green-600 dark:bg-green-700 text-white hover:bg-green-700 dark:hover:bg-green-600 active:bg-green-800 dark:active:bg-green-500 shadow-sm hover:shadow-md',
		'pill-blue':
			'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800 hover:bg-blue-200 dark:hover:bg-blue-900/50 active:bg-blue-300 dark:active:bg-blue-900/70',
		'pill-green':
			'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800 hover:bg-green-200 dark:hover:bg-green-900/50 active:bg-green-300 dark:active:bg-green-900/70',
		'pill-slate':
			'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-700 hover:bg-slate-200 dark:hover:bg-slate-700 active:bg-slate-300 dark:active:bg-slate-600'
	};

	// Size styles - Consistent padding with proper flex alignment
	const sizeStyles = {
		xs: 'py-1 px-2 text-xs gap-1 rounded-md',
		sm: 'py-1.5 px-3 text-sm gap-1.5 rounded-md',
		md: 'py-2.5 px-4 text-sm gap-2 rounded-md',
		lg: 'py-3 px-6 text-base gap-2 rounded-lg',
		'icon-xs': 'p-[0.1rem] w-5 h-5 min-w-5 rounded-md',
		'icon-sm': 'p-1 w-6 min-w-6 rounded-md',
		'icon-md': 'p-2.5 w-10 min-w-10 rounded-md',
		'icon-lg': 'p-3 w-12 min-w-12 rounded-md',
		pill: 'py-1 px-2 text-xs gap-1 rounded-full'
	};

	// Icon size based on button size - Better proportions
	const iconSizes = {
		xs: 'w-3.5 h-3.5',
		sm: 'w-4 h-4',
		md: 'w-4 h-4',
		lg: 'w-5 h-5',
		'icon-xs': 'w-3 h-3',
		'icon-sm': 'w-4 h-4',
		'icon-md': 'w-5 h-5',
		'icon-lg': 'w-6 h-6',
		pill: 'w-3 h-3'
	};

	// Combine all styles
	const buttonClass = $derived(
		[baseStyles, variantStyles[variant], sizeStyles[size], className].filter(Boolean).join(' ')
	);

	const iconClass = $derived(iconSizes[size]);

	function handleClick(event: MouseEvent) {
		if (disabled || loading) {
			event.preventDefault();
			return;
		}
		onclick?.(event);
	}
</script>

{#if href}
	<a
		{href}
		{target}
		class={buttonClass}
		onclick={handleClick}
		aria-disabled={disabled || loading}
		bind:this={buttonElement}
		{...restProps}
	>
		{#if loading}
			<LoadingIndicator variant="wheel" size="sm" color="slate" class={iconClass} />
		{:else if icon && iconPosition === 'left'}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}

		{#if children}
			<span class={isMultiline ? '' : 'w-full truncate'}>
				{@render children()}
			</span>
		{/if}

		{#if icon && iconPosition === 'right' && !loading}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}
	</a>
{:else}
	<button
		{type}
		class={buttonClass}
		{disabled}
		onclick={handleClick}
		aria-disabled={disabled || loading}
		bind:this={buttonElement}
		{...restProps}
	>
		{#if loading}
			<LoadingIndicator variant="wheel" size="sm" color="slate" class={iconClass} />
		{:else if icon && iconPosition === 'left'}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}

		{#if children}
			<span class={isMultiline ? '' : 'w-full truncate'}>
				{@render children()}
			</span>
		{/if}

		{#if icon && iconPosition === 'right' && !loading}
			<div class="{iconClass} flex-shrink-0">
				<Icon src={icon} mini size="100%" />
			</div>
		{/if}
	</button>
{/if}
