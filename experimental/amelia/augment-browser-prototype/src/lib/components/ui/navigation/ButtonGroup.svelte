<script lang="ts">
	interface Props {
		variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
		size?: 'sm' | 'md' | 'lg';
		class?: string;
		children?: any;
	}

	let { variant = 'secondary', size = 'md', class: className = '', children }: Props = $props();

	// Base styles for the group container
	const baseStyles = 'inline-flex';

	// Size-specific styles
	const sizeStyles = {
		sm: 'text-sm',
		md: 'text-sm',
		lg: 'text-base'
	};

	// Variant styles for the group
	const variantStyles = {
		primary: '',
		secondary: '',
		outline: '',
		ghost: ''
	};

	const groupClass = $derived(
		[baseStyles, sizeStyles[size], variantStyles[variant], className].filter(Boolean).join(' ')
	);
</script>

<!-- Button Group Container -->
<div class="button-group {groupClass}" role="group">
	{@render children()}
</div>

<style>
	/* Button group styles */
	.button-group {
		position: relative;
		display: inline-flex;
		vertical-align: middle;
		flex-wrap: wrap;
	}

	/* Remove border radius and adjust borders for grouped buttons */
	:global(.button-group > *:not(:first-child)) {
		margin-left: -1px;
		border-top-left-radius: 0 !important;
		border-bottom-left-radius: 0 !important;
	}

	:global(.button-group > *:not(:last-child)) {
		border-top-right-radius: 0 !important;
		border-bottom-right-radius: 0 !important;
	}

	/* Ensure hover and focus states appear above adjacent buttons */
	:global(.button-group > *:hover) {
		position: relative;
		z-index: 10;
	}

	:global(.button-group > *:focus) {
		position: relative;
		z-index: 20;
	}

	/* Handle active states */
	:global(.button-group > *:active) {
		position: relative;
		z-index: 15;
	}
</style>
