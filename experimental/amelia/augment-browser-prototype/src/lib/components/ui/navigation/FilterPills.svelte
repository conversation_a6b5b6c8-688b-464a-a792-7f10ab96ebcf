<script lang="ts">
	import CustomIcon from '$lib/components/ui/visualization/CustomIcon.svelte';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { getProviderDisplayName, getEntityTypeDisplayName } from '$lib/config/entity-types';
	import { getConditionConfigForProvider } from '$lib/utils/trigger-form-utils';
	import type { EntityType } from '$lib/types';

	interface Props {
		triggerData: Array<{
			trigger: NormalizedTrigger;
			entities: any[];
			executions: any[];
			totalMatches: number;
			isLoading: boolean;
			error?: string;
		}>;
		onFilterRemove?: (triggerId: string, conditionKey: string) => void;
	}

	let { triggerData, onFilterRemove }: Props = $props();

	// Extract all active filter conditions from triggers using conditionsConfig
	let activeFilters = $derived.by(() => {
		const filters: Array<{
			triggerId: string;
			triggerName: string;
			provider: string;
			entityType: string;
			conditionKey: string;
			conditionLabel: string;
			conditionValue: string;
		}> = [];

		triggerData.forEach(({ trigger }) => {
			const conditions = trigger.conditions;
			const entityType = trigger.entityType || 'unknown';

			// Get condition configuration for this provider/entity type
			const conditionConfig = getConditionConfigForProvider(
				trigger.provider as 'github' | 'linear',
				entityType as EntityType
			);

			// Handle the actual conditions structure
			// Expected structure: { type: 1, github: { entityType: 1, pullRequest: { ... } } }
			if (conditions && typeof conditions === 'object') {
				// Look for provider-specific conditions
				const providerConditions = conditions[trigger.provider as keyof typeof conditions];

				if (providerConditions && typeof providerConditions === 'object') {
					// Look for entity-specific conditions (e.g., pullRequest, workflowRun, issue)
					const entityConditionsKey =
						entityType === 'pull_request'
							? 'pullRequest'
							: entityType === 'workflow_run'
								? 'workflowRun'
								: entityType === 'issue'
									? 'issue'
									: null;

					if (
						entityConditionsKey &&
						providerConditions[entityConditionsKey as keyof typeof providerConditions]
					) {
						const entityConditions =
							providerConditions[entityConditionsKey as keyof typeof providerConditions];

						if (entityConditions && typeof entityConditions === 'object') {
							// Process each condition field
							Object.entries(entityConditions).forEach(([fieldKey, fieldValue]) => {
								if (
									fieldValue !== undefined &&
									fieldValue !== null &&
									fieldValue !== '' &&
									!(Array.isArray(fieldValue) && fieldValue.length === 0)
								) {
									// Find the condition config for this field
									const fieldConfig = conditionConfig.find(
										(config) =>
											config.backendMapping?.field === fieldKey || config.value === fieldKey
									);

									if (fieldConfig && !fieldConfig.isHiddenFromConditionsBuilders) {
										// Format the value for display
										let displayValue: string;
										if (Array.isArray(fieldValue)) {
											displayValue = fieldValue.join(', ');
										} else if (fieldValue === '@me') {
											displayValue = 'Me';
										} else {
											displayValue = String(fieldValue);
										}

										filters.push({
											triggerId: trigger.id,
											triggerName: trigger.name,
											provider: trigger.provider,
											entityType: entityType,
											conditionKey: fieldKey,
											conditionLabel: fieldConfig.label.replace(/\.\.\.$/, ''), // Remove trailing dots
											conditionValue: displayValue
										});
									}
								}
							});
						}
					}
				}
			}
		});

		return filters;
	});

	function handleFilterRemove(filter: any) {
		if (onFilterRemove) {
			onFilterRemove(filter.triggerId, filter.conditionKey);
		}
	}
</script>

<div class="mb-4 flex flex-wrap items-center gap-1.5">
	<!-- Provider and Entity Type -->
	{#each triggerData as { trigger }}
		<div class="mr-4 flex items-center gap-1.5 text-sm text-slate-500 dark:text-slate-400">
			<CustomIcon icon={trigger.provider} size={16} class="flex-shrink-0" />
			<span>
				{getProviderDisplayName(trigger.provider)}
				{trigger.entityType ? getEntityTypeDisplayName(trigger.entityType) : 'unknown'}s
			</span>
		</div>
	{/each}

	<!-- Filter Pills -->
	{#if activeFilters.length > 0}
		{#each activeFilters as filter}
			<button
				type="button"
				class="hover:bg-slate-150 dark:hover:bg-slate-750 inline-flex cursor-pointer items-center gap-1.5 rounded-md border border-slate-200 bg-slate-100 px-2 py-1 text-xs text-slate-700 transition-colors dark:border-slate-700 dark:bg-slate-800 dark:text-slate-300"
				onclick={() => handleFilterRemove(filter)}
				aria-label="Remove {filter.conditionLabel} filter"
			>
				<CustomIcon icon={filter.provider} size={12} class="flex-shrink-0 opacity-60" />
				<span class="font-medium text-slate-600 dark:text-slate-400">{filter.conditionLabel}:</span>
				<span class="text-slate-900 dark:text-slate-100">{filter.conditionValue}</span>
			</button>
		{/each}
	{/if}
</div>
