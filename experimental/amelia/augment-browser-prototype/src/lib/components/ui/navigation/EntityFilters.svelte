<script lang="ts">
	import type { EntityType } from '$lib/providers/types';
	import { Icon, Check } from 'svelte-hero-icons';
	import EntityTypeFilters from './EntityTypeFilters.svelte';

	interface Props {
		allIntegrations: any[];
		selectedProviders: Set<string>;
		selectedEntityTypes: Set<EntityType>;
		entityTypeFilters: Record<string, any>;
		onProviderChange: (providers: Set<string>) => void;
		onEntityTypeChange: (entityTypes: Set<EntityType>) => void;
		onEntityTypeFiltersChange: (filters: Record<string, any>) => void;
	}

	let {
		allIntegrations,
		selectedProviders,
		selectedEntityTypes,
		entityTypeFilters,
		onProviderChange,
		onEntityTypeChange,
		onEntityTypeFiltersChange
	}: Props = $props();

	// Get all available entity types
	const availableEntityTypes = $derived.by(() => {
		const types = new Set<EntityType>();
		allIntegrations.forEach(provider => {
			if (provider.hasEntities) {
				provider.entities?.forEach((entity: any) => {
					types.add(entity.type);
				});
			}
		});
		return Array.from(types);
	});

	function toggleProvider(providerId: string) {
		const newProviders = new Set(selectedProviders);
		if (newProviders.has(providerId)) {
			newProviders.delete(providerId);
		} else {
			newProviders.add(providerId);
		}
		onProviderChange(newProviders);
	}

	function toggleEntityType(entityType: EntityType) {
		const newEntityTypes = new Set(selectedEntityTypes);
		if (newEntityTypes.has(entityType)) {
			newEntityTypes.delete(entityType);
		} else {
			newEntityTypes.add(entityType);
		}
		onEntityTypeChange(newEntityTypes);
	}

	function clearAllFilters() {
		onProviderChange(new Set());
		onEntityTypeChange(new Set());
		onEntityTypeFiltersChange({});
	}

	// Format entity type display name
	function formatEntityTypeName(type: EntityType): string {
		switch (type) {
			case 'issues':
				return 'Issues';
			case 'pull_requests':
				return 'Pull Requests';
			case 'tickets':
				return 'Tickets';
			case 'incidents':
				return 'Incidents';
			case 'tasks':
				return 'Tasks';
			default:
				return String(type).charAt(0).toUpperCase() + String(type).slice(1);
		}
	}

	// Check if any filters are active
	const hasActiveFilters = $derived(
		selectedProviders.size > 0 ||
		selectedEntityTypes.size > 0 ||
		Object.keys(entityTypeFilters).length > 0
	);
</script>

<div class="space-y-4">
	<!-- Clear Filters Button -->
	{#if hasActiveFilters}
		<button
			class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
			onclick={clearAllFilters}
		>
			Clear all filters
		</button>
	{/if}

	<!-- Provider Filters -->
	<div>
		<h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Providers</h4>
		<div class="space-y-1">
			{#each allIntegrations as provider (provider.id)}
				{#if provider.hasEntities}
					<button
						class="w-full flex items-center gap-2 px-2 py-1 text-xs rounded hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
						onclick={() => toggleProvider(provider.id)}
					>
						<div class="w-3 h-3 flex-shrink-0">
							{#if selectedProviders.has(provider.id)}
								<Icon src={Check} micro class="text-blue-600 dark:text-blue-400" />
							{:else}
								<div class="w-3 h-3 border border-gray-300 dark:border-gray-600 rounded-sm"></div>
							{/if}
						</div>
						<span class="text-gray-700 dark:text-gray-300">{provider.name}</span>
					</button>
				{/if}
			{/each}
		</div>
	</div>

	<!-- Entity Type Filters -->
	<div>
		<h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Entity Types</h4>
		<div class="space-y-1">
			{#each availableEntityTypes as entityType (entityType)}
				<button
					class="w-full flex items-center gap-2 px-2 py-1 text-xs rounded hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
					onclick={() => toggleEntityType(entityType)}
				>
					<div class="w-3 h-3 flex-shrink-0">
						{#if selectedEntityTypes.has(entityType)}
							<Icon src={Check} micro class="text-blue-600 dark:text-blue-400" />
						{:else}
							<div class="w-3 h-3 border border-gray-300 dark:border-gray-600 rounded-sm"></div>
						{/if}
					</div>
					<span class="text-gray-700 dark:text-gray-300">{formatEntityTypeName(entityType)}</span>
				</button>
			{/each}
		</div>
	</div>

	<!-- Entity Type Specific Filters -->
	{#each selectedEntityTypes as entityType (entityType)}
		<div>
			<h4 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
				{formatEntityTypeName(entityType)} Filters
			</h4>
			<EntityTypeFilters
				{entityType}
				filters={entityTypeFilters[entityType] || {}}
				onFiltersChange={(filters) => {
					const newEntityTypeFilters = { ...entityTypeFilters };
					if (Object.keys(filters).length === 0) {
						delete newEntityTypeFilters[entityType];
					} else {
						newEntityTypeFilters[entityType] = filters;
					}
					onEntityTypeFiltersChange(newEntityTypeFilters);
				}}
			/>
		</div>
	{/each}
</div>
