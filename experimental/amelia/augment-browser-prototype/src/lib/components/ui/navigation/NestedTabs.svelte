<script lang="ts">
	import { Icon } from 'svelte-hero-icons';

	interface Tab {
		id: string;
		label: string;
		icon: any;
	}

	interface Props {
		tabs: Tab[];
		activeTab: string;
		onTabChange: (tabId: string) => void;
	}

	let { tabs, activeTab, onTabChange }: Props = $props();
</script>

<!-- Nested Tab Navigation - Condensed and part of bottom navigation -->
<div
	class="fixed right-0 bottom-0 left-0 z-30 border-t border-slate-200 bg-white lg:hidden dark:border-slate-950 dark:bg-slate-950"
>
	<div class="flex items-center justify-around divide-x divide-slate-200 dark:divide-slate-700">
		{#each tabs as tab}
			<button
				class="flex min-w-0 flex-1 items-center justify-center gap-1.5 px-2 py-3 pb-3.5 transition-colors {activeTab ===
				tab.id
					? 'text-slate-900 dark:text-slate-100'
					: 'text-slate-400 hover:text-slate-700 dark:text-slate-500 dark:hover:text-slate-300'}"
				onclick={() => onTabChange(tab.id)}
			>
				<Icon src={tab.icon} size="16" class="flex-shrink-0" mini />
				<span class="text-xs font-medium">{tab.label}</span>
			</button>
		{/each}
	</div>
</div>
