<script lang="ts">
	import {
		<PERSON><PERSON>,
		<PERSON><PERSON>ircle,
		ExclamationTriangle,
		ArrowTopRightOnSquare,
		ArrowPath,
		Link
	} from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import {
		getProviderAuthStatus,
		checkAll<PERSON>roviderAuth,
		initiateProviderOAuth,
		disconnect<PERSON><PERSON>ider
	} from '$lib/stores/provider-auth';
	import { getProvider } from '$lib/providers';

	interface Props {
		providerId: string;
		variant?: 'default' | 'compact' | 'inline';
		showLabel?: boolean;
		disabled?: boolean;
		onAuthSuccess?: () => void;
		onAuthError?: (error: string) => void;
	}

	let {
		providerId,
		variant = 'default',
		showLabel = true,
		disabled = false,
		onAuthSuccess,
		onAuthError
	}: Props = $props();

	// Provider auth status
	const providerStatus = getProviderAuthStatus(providerId);
	let isLoading = $state(false);
	let isOAuthInProgress = $state(false);
	let error = $state<string | null>(null);

	// Get provider information from the provider system
	const provider = getProvider(providerId);

	async function handleConnect() {
		if (disabled || isLoading || isOAuthInProgress) return;

		isLoading = true;
		error = null;

		try {
			await initiateProviderOAuth(providerId);

			// For popup-based OAuth (like Linear), set OAuth in progress state
			if (providerId === 'linear') {
				isOAuthInProgress = true;
				isLoading = false; // Stop loading spinner since popup is now open

				// Monitor provider status changes to detect when OAuth completes
				const unsubscribe = providerStatus.subscribe((status) => {
					if (status?.isConfigured && isOAuthInProgress) {
						isOAuthInProgress = false;
						onAuthSuccess?.();
						unsubscribe();
					}
				});

				// Set a timeout to clear OAuth in progress state after 10 minutes
				setTimeout(
					() => {
						if (isOAuthInProgress) {
							isOAuthInProgress = false;
							unsubscribe();
						}
					},
					10 * 60 * 1000
				);
			}
			// OAuth flow will redirect for non-popup providers, so we won't reach here normally
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to start OAuth flow';
			error = errorMessage;
			onAuthError?.(errorMessage);
			console.error(`Failed to connect ${providerId}:`, err);
		} finally {
			if (providerId !== 'linear') {
				isLoading = false;
			}
		}
	}

	async function handleRefresh() {
		if (disabled || isLoading) return;

		isLoading = true;
		error = null;

		try {
			await checkAllProviderAuth();
			onAuthSuccess?.();
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to refresh status';
			error = errorMessage;
			onAuthError?.(errorMessage);
		} finally {
			isLoading = false;
		}
	}

	// Handle disconnect
	async function handleDisconnect() {
		if (isLoading) return;

		isLoading = true;
		error = null;

		try {
			await disconnectProvider(providerId);
			onAuthSuccess?.();
		} catch (err) {
			const errorMessage = err instanceof Error ? err.message : 'Failed to disconnect';
			error = errorMessage;
			onAuthError?.(errorMessage);
		} finally {
			isLoading = false;
		}
	}

	// Reactive values
	const providerName = $derived(provider?.name || $providerStatus?.name || providerId);
	const isConfigured = $derived($providerStatus?.isConfigured || false);
	const needsAuth = $derived($providerStatus?.isAuthenticationRequired || false);
	const hasError = $derived(!!($providerStatus?.error || error));
	const providerIcon = $derived(provider?.icon || Link);

	// Helper to determine if we have provider data
	const hasProviderData = $derived(!!$providerStatus);
</script>

{#if variant === 'compact'}
	<!-- Compact variant - just status icon and connect button -->
	<div class="flex items-center gap-2">
		<!-- <div class="flex items-center gap-1">
			<providerIcon size={16} class="text-gray-600 dark:text-gray-400"></providerIcon>
			{#if isConfigured}
				<Icon src={CheckCircle} class="w-4 h-4 text-green-500" />
			{:else if hasError}
				<Icon src={ExclamationTriangle} class="w-4 h-4 text-red-500" />
			{:else}
				<div class="w-4 h-4 rounded-full bg-gray-300 dark:bg-gray-600"></div>
			{/if}
		</div> -->

		{#if !isConfigured && needsAuth}
			<Button
				variant="outline"
				size="sm"
				onclick={handleConnect}
				loading={isLoading}
				disabled={isOAuthInProgress}
				icon={ArrowTopRightOnSquare}
			>
				{showLabel ? (isOAuthInProgress ? 'Check popup...' : 'Connect') : ''}
			</Button>
		{:else if isConfigured}
			<div class="flex gap-2">
				<Button variant="outline" size="sm" onclick={handleDisconnect} loading={isLoading}>
					Disconnect
				</Button>
				<Button
					variant="ghost"
					size="sm"
					onclick={handleRefresh}
					loading={isLoading}
					icon={ArrowPath}
				></Button>
			</div>
		{/if}
	</div>
{:else if variant === 'inline'}
	<!-- Inline variant - status text with optional action -->
	<div class="flex items-center gap-2 text-sm">
		<Icon src={providerIcon} class="h-4 w-4" />

		<span class="text-gray-600 dark:text-gray-400">{providerName}:</span>

		{#if isConfigured}
			<span class="flex items-center gap-1 text-green-600 dark:text-green-400">
				<Icon src={CheckCircle} class="h-4 w-4" />
				Connected
			</span>
		{:else if hasError}
			<span class="flex items-center gap-1 text-red-600 dark:text-red-400">
				<Icon src={ExclamationTriangle} class="h-4 w-4" />
				Error
			</span>
		{:else if needsAuth}
			<button
				onclick={handleConnect}
				disabled={disabled || isLoading || isOAuthInProgress}
				class="text-blue-600 hover:underline disabled:opacity-50 dark:text-blue-400"
			>
				{isLoading ? 'Connecting...' : isOAuthInProgress ? 'Check popup...' : 'Connect'}
			</button>
		{:else}
			<span class="text-gray-500">Unknown</span>
		{/if}
	</div>
{:else}
	<!-- Default variant - full card -->
	<div class="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
		<div class="flex justify-between max-sm:flex-col md:items-center">
			<div class="flex flex-1 items-center gap-3 pr-3">
				<Icon src={providerIcon} class="h-6 w-6" />

				<div class="flex w-full gap-3">
					<h3 class="flex-1 font-medium text-gray-900 dark:text-white">{providerName}</h3>
					<div class="flex flex-none items-center gap-1">
						{#if isLoading || !hasProviderData}
							<span class="text-sm text-gray-500 dark:text-gray-400">Checking...</span>
						{:else if isOAuthInProgress}
							<span class="text-sm text-blue-600 dark:text-blue-400">Check popup window...</span>
						{:else if isConfigured}
							<Icon src={CheckCircle} class="h-4 w-4 text-emerald-500" micro />
							<span class="text-sm text-emerald-600 dark:text-emerald-400">Connected</span>
						{:else if hasError}
							<Icon src={ExclamationTriangle} class="h-4 w-4 text-red-500" micro />
							<span class="text-sm text-red-600 dark:text-red-400">
								{$providerStatus?.error || error || 'Authentication error'}
							</span>
						{:else if needsAuth}
							<span class="text-sm text-gray-500 dark:text-gray-400">Not connected</span>
						{:else}
							<span class="text-sm text-gray-500 dark:text-gray-400">Not connected</span>
						{/if}
					</div>
				</div>
			</div>

			<div class="flex items-center gap-2">
				{#if !isConfigured}
					<Button
						variant="primary"
						size="sm"
						onclick={handleConnect}
						loading={isLoading}
						disabled={isOAuthInProgress}
						icon={ArrowTopRightOnSquare}
					>
						{isOAuthInProgress ? 'Check popup...' : 'Connect'}
					</Button>
				{:else if isConfigured}
					<Button variant="outline" size="sm" onclick={handleDisconnect} loading={isLoading}>
						Disconnect
					</Button>
					<Button
						variant="ghost"
						size="icon-sm"
						onclick={handleRefresh}
						loading={isLoading}
						title="Refresh connection status"
						icon={ArrowPath}
					></Button>
				{:else if hasError}
					<Button variant="outline" size="sm" onclick={handleRefresh} loading={isLoading}>
						Retry
					</Button>
				{/if}
			</div>
		</div>

		{#if $providerStatus?.configuredButNeedsUpdate}
			<div
				class="mt-3 rounded border border-yellow-200 bg-yellow-50 p-2 text-sm text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200"
			>
				<Icon src={ExclamationTriangle} class="mr-1 inline h-4 w-4" />
				Connection needs to be updated with new permissions.
			</div>
		{/if}
	</div>
{/if}
