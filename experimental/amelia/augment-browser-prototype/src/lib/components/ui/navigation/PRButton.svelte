<script lang="ts">
	import type { Task } from '$lib/types';
	import type { CleanRemoteAgent, CleanChangedFile } from '$lib/api/unified-client';
	import { Icon, ArrowTopRightOnSquare, ArrowPath, Plus } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { hasCodeChanges } from '$lib/utils/task-status';

	interface Props {
		task: Task;
		remoteAgent?: CleanRemoteAgent | null;
		changedFiles?: CleanChangedFile[];
		githubToken?: string;
		onPRCreated?: (pr: any) => void;
		onPRUpdated?: (pr: any) => void;
	}

	let {
		task,
		remoteAgent,
		changedFiles = [],
		githubToken,
		onPRCreated,
		onPRUpdated
	}: Props = $props();

	let isCreatingPR = $state(false);
	let isUpdatingPR = $state(false);
	let isCheckingStatus = $state(false);
	let error = $state<string | null>(null);

	// Check if task has code changes
	const hasChanges = $derived(hasCodeChanges(changedFiles));

	// Check if PR needs updating (has newer changes than when PR was created/updated)
	const needsUpdate = $derived(() => {
		if (!task.prId || !task.prTurnIndex || !remoteAgent) return false;

		// This would need to be calculated based on latest turn index from chat history
		// For now, we'll use a simple check
		return hasChanges;
	});

	// Check if task has an existing PR
	const hasPR = $derived(!!task.prId);

	// Check if PR is merged
	const isPRMerged = $derived(task.prStatus === 'merged');

	async function createPR() {
		if (!githubToken || !task.project?.githubRepo) {
			error = 'GitHub token and repository required';
			return;
		}

		isCreatingPR = true;
		error = null;

		try {
			// Generate a branch name based on task title
			const branchName = `task-${task.id}-${task.title.toLowerCase().replace(/[^a-z0-9]/g, '-').slice(0, 50)}`;

			const response = await fetch(`/api/tasks/${task.id}/pr`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					token: githubToken,
					branchName,
					baseBranch: 'main'
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to create PR');
			}

			const result = await response.json();

			onPRCreated?.(result.pr);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create PR';
		} finally {
			isCreatingPR = false;
		}
	}

	async function updatePR() {
		if (!githubToken || !task.prId) {
			error = 'GitHub token and existing PR required';
			return;
		}

		isUpdatingPR = true;
		error = null;

		try {
			const response = await fetch(`/api/tasks/${task.id}/pr`, {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					token: githubToken
				})
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to update PR');
			}

			const result = await response.json();

			// Update the task in the store
			await updateTask(task.id, {
				prStatus: result.pr.state,
				prTurnIndex: result.task.prTurnIndex
			});

			onPRUpdated?.(result.pr);
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to update PR';
		} finally {
			isUpdatingPR = false;
		}
	}

	async function checkPRStatus() {
		if (!githubToken || !task.prId) return;

		isCheckingStatus = true;
		error = null;

		try {
			const response = await fetch(`/api/tasks/${task.id}/pr?token=${encodeURIComponent(githubToken)}`);

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to check PR status');
			}

			const result = await response.json();

			// Update the task in the store if status changed
			if (result.task.status !== task.status || result.task.prStatus !== task.prStatus) {
				await updateTask(task.id, {
					status: result.task.status,
					prStatus: result.task.prStatus
				});
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to check PR status';
		} finally {
			isCheckingStatus = false;
		}
	}

	// Auto-check PR status when component mounts if PR exists
	$effect(() => {
		if (hasPR && githubToken && !isCheckingStatus) {
			checkPRStatus();
		}
	});
</script>

{#if error}
	<div class="text-xs text-red-600 dark:text-red-400 mb-2">
		{error}
	</div>
{/if}

{#if isPRMerged}
	<!-- PR is merged - show completed status -->
	<Button
		size="xs"
		variant="pill-green"
		icon={ArrowTopRightOnSquare}
		iconPosition="right"
		onclick={() => window.open(task.prUrl, '_blank')}
	>
		PR merged
	</Button>
{:else if hasPR}
	<!-- Existing PR - show update button if needed -->
	<div class="flex items-center gap-1">
		<Button
			size="xs"
			variant="pill-blue"
			icon={ArrowTopRightOnSquare}
			iconPosition="right"
			onclick={() => window.open(task.prUrl, '_blank')}
		>
			PR #{task.prId}
		</Button>

		{#if needsUpdate() && githubToken}
			<Button
				size="xs"
				variant="pill-slate"
				icon={ArrowPath}
				loading={isUpdatingPR}
				onclick={updatePR}
			>
				Update
			</Button>
		{/if}
	</div>
{:else if hasChanges && githubToken}
	<!-- No PR but has changes - show create button -->
	<Button
		size="xs"
		variant="pill-green"
		icon={Plus}
		loading={isCreatingPR}
		onclick={createPR}
	>
		Create PR
	</Button>
{/if}
