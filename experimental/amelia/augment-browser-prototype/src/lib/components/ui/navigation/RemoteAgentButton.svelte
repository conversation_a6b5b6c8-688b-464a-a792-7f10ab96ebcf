<script lang="ts">
	import { Icon, PlayCircle } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import RemoteAgentStatusIndicator from '$lib/components/ui/feedback/RemoteAgentStatusIndicator.svelte';
	import { getAgent } from '$lib/stores/global-state.svelte';
	import SizedDot from '$lib/components/agents/SizedDot.svelte';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	import { getAgentChangedFilesWithDetails, getAgentRawExchanges } from '$lib/utils/agent-data';

	interface Props {
		remoteAgentId: string;
		onCreateAgent?: () => void;
		onRemoveAgent?: () => void;
	}

	let { remoteAgentId, onCreateAgent, onRemoveAgent }: Props = $props();

	// Reactive stores that update when remoteAgentId changes
	let remoteAgentInfo = $derived(getAgent(remoteAgentId));
	let remoteAgent = $derived(remoteAgentInfo.data);
	let isLoading = $derived(remoteAgentInfo.loading);
	let changedFiles = $derived(getAgentChangedFilesWithDetails(remoteAgentId));
	let rawExchangesStore = $derived(getAgentRawExchanges(remoteAgentId));
	let rawExchanges = $derived($rawExchangesStore || []);

	// Get the latest exchange with a summary
	let lastExchangeWithSummary = $derived(
		rawExchanges
			.slice()
			.reverse()
			.find((exchange) => {
				const responseContent =
					exchange.exchange.response?.nodes?.map((node) => node.content).join('') || '';
				return responseContent.includes('<summary>') && responseContent.includes('</summary>');
			})
	);
	let lastResponseSummary = $derived.by(() => {
		if (!lastExchangeWithSummary?.exchange.response) return '';
		const responseContent =
			lastExchangeWithSummary.exchange.response.nodes?.map((node) => node.content).join('') || '';
		const match = responseContent.match(/<summary>(.*?)<\/summary>/s);
		return match ? match[1].trim() : '';
	});

	// No need to fetch here - the centralized store handles all fetching
	// Components just read from the store reactively

	async function handleCreateAgent() {
		if (onCreateAgent) {
			onCreateAgent();
		}
	}
</script>

{#if remoteAgent && remoteAgent}
	<!-- Remote agent exists - show status -->
	<div class="">
		<div class="flex items-center gap-2">
			<RemoteAgentStatusIndicator
				size="sm"
				status={remoteAgent.status}
				hasUpdates={remoteAgent.hasUpdates}
				workspaceStatus={remoteAgent.workspaceStatus}
				isExpanded={false}
			/>

			{#if $changedFiles && $changedFiles.length > 0}
				<div class="flex flex-wrap">
					{#each $changedFiles as file}
						<div
							class="m-0.5 flex items-center gap-1 rounded-xl bg-slate-100 px-2 py-0.5 text-xs text-slate-500"
						>
							{file.path.split('/').pop()}
							<div class="flex">
								<SizedDot type="addition" count={file.linesAdded || 0} />
								<SizedDot type="deletion" count={file.linesDeleted || 0} />
							</div>
						</div>
					{/each}
				</div>
			{/if}
		</div>
		{#if lastResponseSummary}
			<div class="text-slate-500">
				<Markdown content={lastResponseSummary} size="xs" lineClamp={1} />
			</div>
		{/if}

		<!-- Remove agent button -->
		<!-- <Button
			variant="ghost"
			size="icon-sm"
			onclick={handleRemoveAgent}
			class="text-slate-400 dark:text-slate-500 hover:text-red-600 dark:hover:text-red-400"
			aria-label="Remove remote agent"
		>
			<Icon src={Trash} mini class="w-4 h-4" />
		</Button> -->
	</div>
{:else if isLoading}
	<!-- Loading state -->
	<div class="flex items-center gap-2">
		<div class="h-4 w-4 animate-spin rounded-full border-b-2 border-blue-600"></div>
		<span class="text-xs text-slate-500">Loading agent...</span>
	</div>
{:else}
	<!-- No agent - show create button -->
	<Button
		variant="ghost"
		size="icon-sm"
		onclick={handleCreateAgent}
		class="text-slate-400 hover:text-blue-600 dark:text-slate-500 dark:hover:text-blue-400"
		aria-label="Create remote agent"
	>
		<Icon src={PlayCircle} class="h-4 w-4" micro />
	</Button>
{/if}
