<script lang="ts">
	import type { TriggerProvider } from '$lib/types';
	import CardButton from './CardButton.svelte';
	import CardButtonGroup from './CardButtonGroup.svelte';

	interface Props {
		provider: TriggerProvider | null;
		selectedEvent: string;
		onEventChange: (eventName: string) => void;
		disabled?: boolean;
		class?: string;
	}

	let { provider, selectedEvent, onEventChange, disabled = false, class: className = '' }: Props = $props();

	// Show first 5 events as pills, rest as "+ X more"
	const maxVisibleEvents = 5;

	const visibleEvents = $derived(provider?.events.slice(0, maxVisibleEvents) || []);
	const hiddenEventsCount = $derived(provider ? Math.max(0, provider.events.length - maxVisibleEvents) : 0);

	function handleEventClick(eventName: string) {
		if (!disabled) {
			onEventChange(eventName);
		}
	}

	function getFieldCount(event: any): number {
		return event.fields?.length || 0;
	}
</script>

<div class="space-y-3 {className}">
	{#if provider?.events.length === 0}
		<div class="text-sm text-gray-500 dark:text-gray-400 italic">
			No events available for this provider
		</div>
	{:else}
		<CardButtonGroup direction="vertical" class="w-full">
			{#each visibleEvents as event}
				{@const isSelected = selectedEvent === event.name}
				<CardButton
					variant="grouped"
					selected={isSelected}
					{disabled}
					onclick={() => handleEventClick(event.name)}
					ariaLabel={`Select ${event.name} event`}
				>
					<div class="flex items-start justify-between gap-3">
						<div class="flex-1 min-w-0">
							<div class="flex items-center gap-2 mb-1">
								<span class="font-semibold text-gray-900 dark:text-white text-sm">
									{event.name}
								</span>
							</div>
							<p class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
								{event.description}
							</p>
							<!-- {#if fieldCount > 0}
								<div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
									{fieldCount} field{fieldCount !== 1 ? 's' : ''} available
								</div>
							{/if} -->
						</div>
					</div>
				</CardButton>
			{/each}
		</CardButtonGroup>

		<!-- {#if hiddenEventsCount > 0}
			<div class="p-4 border border-dashed border-gray-200 dark:border-gray-600 rounded-lg text-center">
				<span class="text-sm text-gray-500 dark:text-gray-400">
					+ {hiddenEventsCount} more event{hiddenEventsCount !== 1 ? 's' : ''} available
				</span>
			</div>
		{/if} -->
	{/if}


</div>
