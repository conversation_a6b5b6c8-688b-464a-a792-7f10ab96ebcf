<script lang="ts">
	import { Icon, MagnifyingGlass, ChevronDown, ChevronRight, Squares2x2, ListBullet } from 'svelte-hero-icons';
	import Input from '$lib/components/ui/forms/Input.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import ButtonGroup from './ButtonGroup.svelte';
	import CustomIcon from '$lib/components/ui/visualization/CustomIcon.svelte';
	import DashboardFilterForm from '$lib/components/ui/forms/DashboardFilterForm.svelte';
	import Pill from './Pill.svelte';
	import type { DashboardFilters } from '$lib/utils/dashboard-filters.svelte';
	import { createEmptyConditionFilters } from '$lib/utils/condition-filters';
	import { getEntityTypeFromTrigger } from '$lib/utils/entity-conversion';

	interface Props {
		filterManager: any;
		triggers: any[];
		viewMode: 'table' | 'grid';
		availableProviders: string[];
		availableEntityTypes: string[];
		activeConditionPills: string[];
		onTriggerClick: (trigger: any) => void;
		class?: string;
	}

	let {
		filterManager,
		triggers = [],
		viewMode = $bindable<'table' | 'grid'>('table'),
		availableProviders = [],
		availableEntityTypes = [],
		activeConditionPills = [],
		onTriggerClick,
		class: className = ''
	}: Props = $props();

	let showConditions = $state(false);
</script>

<div class="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 mt-3 {className}">
	<!-- Filter Configurations (Settings) -->
	{#if triggers.length > 0}
		<div class="px-6 py-4 bg-slate-50 dark:bg-slate-800/30">
			<div class="flex items-center gap-3 mb-3">
				<h3 class="text-sm font-semibold text-slate-900 dark:text-white">
					Filter Configurations
				</h3>
				<span class="text-xs text-slate-500 dark:text-slate-400">
					Quick filters based on your saved triggers
				</span>
			</div>

			<div class="flex flex-wrap gap-2">
				{#each triggers as trigger}
					{@const isSelected = filterManager.matchesTrigger(trigger)}
					<Button
						variant={isSelected ? "primary" : "outline"}
						size="sm"
						onclick={() => onTriggerClick(trigger)}
						class="flex-shrink-0"
					>
						<div class="flex items-center gap-1.5">
							<CustomIcon icon={trigger.provider} size={16} class={isSelected ? "fill-white" : ""} />
							<span class="text-sm font-medium {isSelected ? 'text-white' : 'text-slate-900 dark:text-white'} truncate">
								{trigger.configuration?.name || 'Unnamed Filter'}
							</span>
							{#if trigger.matchingEntityCount !== undefined}
								<span class="ml-1 px-1.5 py-0.5 text-xs font-medium rounded-full {isSelected ? 'bg-white/20 text-white' : 'bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300'}">
									{trigger.matchingEntityCount}
								</span>
							{/if}
						</div>
					</Button>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Basic Filters -->
	<div class="px-6 py-4">
		<div class="flex flex-wrap gap-x-3 gap-y-3">
			<!-- Search -->
			<div class="flex-1 min-w-[200px]">
				<Input
					icon={MagnifyingGlass}
					type="search"
					inputClass="!py-2.5"
					placeholder="Search for an Action Item..."
					value={filterManager.filters.searchQuery}
					oninput={(value) => {
						filterManager.updateSearch(String(value));
					}}
				/>
			</div>

			<!-- Provider Filter -->
			<div class="flex-shrink-0">
				<ButtonGroup>
					<Button
						onclick={() => {
							filterManager.updateConditionFilters(createEmptyConditionFilters());
							filterManager.updateProvider('all');
							filterManager.updateEntityType('all')
						}}
						color={filterManager.filters.selectedProvider === 'all' ? 'primary' : 'secondary'}
						variant={filterManager.filters.selectedProvider === 'all' ? 'primary' : 'secondary'}
					>
						All Providers
					</Button>
					{#each availableProviders as provider}
						{@const isSelected = filterManager.filters.selectedProvider === provider}
						<Button
							onclick={() => {
								filterManager.updateConditionFilters(createEmptyConditionFilters());
								filterManager.updateProvider(provider);
							}}
							color={isSelected ? 'primary' : 'secondary'}
							variant={isSelected ? 'primary' : 'secondary'}
						>
							<div class="flex items-center gap-1.5">
								<CustomIcon icon={provider} size={16} class={isSelected ? "fill-white" : ""} />
								<span class="capitalize">{provider}</span>
							</div>
						</Button>
					{/each}
				</ButtonGroup>
			</div>

			<!-- Entity Type Filter -->
			{#if availableEntityTypes.length > 0}
				<div class="flex-shrink-0">
					<ButtonGroup>
						<!-- Only show "All" button if there are multiple entity types -->
						{#if availableEntityTypes.length > 1}
							<Button
								onclick={() => {
									filterManager.updateConditionFilters(createEmptyConditionFilters());
									filterManager.updateEntityType('all')
								}}
								color={filterManager.filters.selectedEntityType === 'all' ? 'primary' : 'secondary'}
								variant={filterManager.filters.selectedEntityType === 'all' ? 'primary' : 'secondary'}
							>
								All Types
							</Button>
						{/if}
						{#each availableEntityTypes as entityType}
							<Button
								onclick={() => {
									filterManager.updateConditionFilters(createEmptyConditionFilters());
									filterManager.updateEntityType(entityType)
								}}
								color={filterManager.filters.selectedEntityType === entityType ? 'primary' : 'secondary'}
								variant={filterManager.filters.selectedEntityType === entityType ? 'primary' : 'secondary'}
							>
								{entityType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
							</Button>
						{/each}
					</ButtonGroup>
				</div>
			{/if}

						<div class="flex items-center bg-slate-100 dark:bg-slate-800 rounded-lg p-1">
							<button
								class="p-2 rounded-md transition-colors {viewMode === 'grid'
									? 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm'
									: 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'}"
								onclick={() => viewMode = 'grid'}
								title="Grid view"
							>
								<Icon src={Squares2x2} class="w-4 h-4" />
							</button>
							<button
								class="p-2 rounded-md transition-colors {viewMode === 'table'
									? 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white shadow-sm'
									: 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white'}"
								onclick={() => viewMode = 'table'}
								title="Table view"
							>
								<Icon src={ListBullet} class="w-4 h-4" />
							</button>
						</div>
		</div>
	</div>

	<!-- Advanced Conditions (Collapsible) -->
	{#if filterManager.filters.selectedEntityType !== 'all'}
		<div class="px-6 py-4 bg-slate-50 dark:bg-slate-800/30 border-t border-slate-200 dark:border-slate-700">
			<!-- Header with toggle and clear button -->
			<div class="flex items-center justify-between mb-3">
				<button
					class="flex items-center gap-2 text-left hover:opacity-80 transition-opacity"
					onclick={() => showConditions = !showConditions}
				>
					<Icon src={showConditions ? ChevronDown : ChevronRight} size="16" class="text-slate-500" />
					<h3 class="text-sm font-semibold text-slate-900 dark:text-white">
						{filterManager.filters.selectedEntityType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Filters
					</h3>
					<span class="text-xs text-slate-500 dark:text-slate-400">
						Advanced filtering options
					</span>
				</button>
				{#if Object.keys(filterManager.filters.selectedConditions).length > 0}
					<button
						class="text-xs text-slate-500 hover:text-slate-700 dark:hover:text-slate-300 transition-colors"
						onclick={() => {
							filterManager.updateConditionFilters(createEmptyConditionFilters());
						}}
					>
						Clear all
					</button>
				{/if}
			</div>

			<!-- Pills when collapsed -->
			{#if !showConditions && Object.keys(filterManager.filters.selectedConditions).length > 0}
				<div class="flex flex-wrap gap-1 mb-2">
					{#each activeConditionPills as pill}
						<Pill
							variant="blue"
							size="xs"
							onclick={() => filterManager.removeConditionPill(pill)}
						>
							{pill}
						</Pill>
					{/each}
				</div>
			{/if}

			<!-- Expanded filter form -->
			{#if showConditions}
				<div class="mt-3">
					<DashboardFilterForm
						provider={filterManager.filters.selectedProvider}
						entityType={filterManager.filters.selectedEntityType}
						conditionFilters={filterManager.filters.conditionFilters}
						onUpdate={(filters) => filterManager.updateConditionFilters(filters)}
					/>
				</div>
			{/if}
		</div>
	{/if}
</div>
