import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render } from '@testing-library/svelte';
import RemoteAgentDetailPanel from './RemoteAgentDetailPanel.svelte';
import { RemoteAgentStatus, type CleanRemoteAgent } from '$lib/api/unified-client';

// Mock the data operations
vi.mock('$lib/stores/data-operations.svelte', () => ({
	loadChatHistory: vi.fn()
}));

// Mock the global state
vi.mock('$lib/stores/global-state.svelte', () => ({
	deleteAgent: vi.fn(),
	getTriggerForAgent: vi.fn(() => ({ data: null })),
	getEntityByAgent: vi.fn(() => ({ data: null })),
	getChatExchanges: vi.fn(() => [])
}));

// Mock other dependencies
vi.mock('$lib/stores/toast', () => ({
	addToast: vi.fn()
}));

vi.mock('$lib/utils/session-summary-parser', () => ({
	parseSessionSummaryTitle: vi.fn(() => 'Test Summary'),
	extractSummaryFromResponse: vi.fn(() => 'Extracted summary'),
	extractGeneralSummaryFromResponse: vi.fn(() => 'Extracted general summary')
}));

vi.mock('$lib/utils/time', () => ({
	getRelativeTime: vi.fn(() => '2 minutes ago')
}));

vi.mock('$lib/components/chat-history/utils', () => ({
	processExchangesWithToolFiltering: vi.fn(() => [])
}));

describe('RemoteAgentDetailPanel Chat History Loading', () => {
	const mockAgent: CleanRemoteAgent = {
		id: 'test-agent-123',
		remoteAgentId: 'remote-123',
		status: RemoteAgentStatus.AGENT_COMPLETED,
		createdAt: new Date(),
		updatedAt: new Date(),
		sessionSummary: 'Test session summary',
		generalSummary: 'Test general summary'
	};

	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should load chat history when panel opens with chat tab active', async () => {
		const { loadChatHistory } = await import('$lib/stores/data-operations.svelte');

		render(RemoteAgentDetailPanel, {
			props: {
				agent: mockAgent,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Should load chat history when agent is provided
		expect(loadChatHistory).toHaveBeenCalledWith(mockAgent.id);
	});

	it('should load chat history when switching to chat tab', async () => {
		const { loadChatHistory } = await import('$lib/stores/data-operations.svelte');

		const { component } = render(RemoteAgentDetailPanel, {
			props: {
				agent: mockAgent,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Clear previous calls
		vi.clearAllMocks();

		// Simulate switching to chat tab
		// Note: In a real test, we'd need to interact with the tab buttons
		// For now, we're just verifying the effect logic

		expect(loadChatHistory).toHaveBeenCalledWith(mockAgent.id);
	});

	it('should handle agent without ID gracefully', async () => {
		const { loadChatHistory } = await import('$lib/stores/data-operations.svelte');

		const agentWithoutId = { ...mockAgent, id: undefined } as any;

		render(RemoteAgentDetailPanel, {
			props: {
				agent: agentWithoutId,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Should not call loadChatHistory when agent has no ID
		expect(loadChatHistory).not.toHaveBeenCalled();
	});

	it('should handle null agent gracefully', async () => {
		const { loadChatHistory } = await import('$lib/stores/data-operations.svelte');

		render(RemoteAgentDetailPanel, {
			props: {
				agent: null,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Should not call loadChatHistory when agent is null
		expect(loadChatHistory).not.toHaveBeenCalled();
	});
});

describe('RemoteAgentDetailPanel Summary Display', () => {
	const mockAgent: CleanRemoteAgent = {
		id: 'test-agent-123',
		remoteAgentId: 'remote-123',
		status: RemoteAgentStatus.AGENT_COMPLETED,
		createdAt: new Date(),
		updatedAt: new Date(),
		sessionSummary: 'Test session summary',
		generalSummary: 'Test general summary'
	};

	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should display summary section when summary is available', async () => {
		const { container } = render(RemoteAgentDetailPanel, {
			props: {
				agent: mockAgent,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Should show summary section header
		expect(container.textContent).toContain('Summary');
	});

	it('should show skeleton when no exchanges are available', async () => {
		const { container } = render(RemoteAgentDetailPanel, {
			props: {
				agent: mockAgent,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Should show summary section even when loading
		expect(container.textContent).toContain('Summary');
	});

	it('should handle agent without summary gracefully', async () => {
		const agentWithoutSummary = {
			...mockAgent,
			sessionSummary: undefined,
			generalSummary: undefined
		};

		const { container } = render(RemoteAgentDetailPanel, {
			props: {
				agent: agentWithoutSummary,
				onClose: vi.fn(),
				onAgentDeleted: vi.fn()
			}
		});

		// Should still render the component without errors
		expect(container).toBeTruthy();
	});
});
