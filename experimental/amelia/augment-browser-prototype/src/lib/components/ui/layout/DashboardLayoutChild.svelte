<script lang="ts">
	import { type Snippet } from 'svelte';

	import ConsolidatedHeader from './ConsolidatedHeader.svelte';

	interface DashboardLayoutProps {
		sectionLabel: string; // e.g., 'Dashboard', 'Filters', 'Runs'
		sectionDescription?: string; // e.g., 'Dashboard', 'Filters', 'Runs'

		currentItemId?: string;
		currentItem?: any;

		// Snippets
		indexContent?: Snippet;
		detailContent?: Snippet;
		headerActions?: Snippet<[{ currentItem: any }]>;
		children?: Snippet;
	}

	let {
		sectionLabel,
		sectionDescription,
		currentItemId,
		currentItem,
		indexContent,
		detailContent,
		headerActions,
		children
	}: DashboardLayoutProps = $props();
</script>


			<!-- Content -->
			<main class="w-full max-w-[80em] mx-auto flex flex-col py-6 pt-12 lg:pt-6">
				{#if children}
					{@render children()}
				{:else if detailContent && currentItemId}
					{@render detailContent?.()}
				{:else if indexContent}
					<!-- Index Header -->
					<ConsolidatedHeader
						title={sectionLabel}
						subtitle={sectionDescription}
						type="index"
					>
						{#snippet actions()}
							{#if headerActions}
								{@render headerActions({ currentItem })}
							{/if}
						{/snippet}
					</ConsolidatedHeader>

					<!-- Index Content -->
					<div class="flex-1 overflow-auto">
						{@render indexContent?.()}
					</div>
				{/if}
			</main>
