<script lang="ts">
	import { type Snippet } from 'svelte';
	import ConsolidatedHeader from './ConsolidatedHeader.svelte';
	import { Icon, XMark, ArrowLeft } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	interface DashboardLayoutChildDetailProps {
		// Header content
		title: string;
		subtitle?: string;
		description?: string;
		backText?: string;

		// Navigation
		onClose: () => void;

		// Actions and metadata
		actions?: Snippet;
		metadata?: Snippet;

		// Content
		children?: Snippet;
	}

	let {
		title,
		subtitle,
		description,
		backText = 'Back',
		onClose,
		actions,
		metadata,
		children
	}: DashboardLayoutChildDetailProps = $props();

	// Determine container classes based on layout mode
	let containerClasses = $derived.by(() => {
			return 'h-full w-full flex flex-col bg-white dark:bg-gray-900';
	});

	// Custom close handler that uses the appropriate icon
	function handleClose() {
		onClose();
	}
</script>

<div class="{containerClasses}">
	<!-- Consolidated Header with dynamic close button -->
	<ConsolidatedHeader
		{title}
		{subtitle}
		{description}
		{backText}
		type="detail"
		onClose={handleClose}
	>
		{#snippet actions()}
			{#if actions}
				{@render actions()}
			{/if}
		{/snippet}

		{#snippet metadata()}
			{#if metadata}
				{@render metadata()}
			{/if}
		{/snippet}
	</ConsolidatedHeader>

	<!-- Content Area -->
	{#if children}
		<div class="flex-1">
			{@render children()}
		</div>
	{/if}
</div>
