<script lang="ts">
	import { apiClient, type CleanRemoteAgent } from '$lib/api/unified-client';
	import {
		loadAgents,
		startStreamingAgent,
		updateAgentTitle
	} from '$lib/stores/data-operations.svelte';
	import {
		getEntityByAgent,
		getTriggerForAgent,
		getChatExchanges
	} from '$lib/stores/global-state.svelte';
	import { reactiveRelativeTime } from '$lib/utils/time';
	import { ArrowTopRightOnSquare, Icon, DocumentDuplicate, Check } from 'svelte-hero-icons';
	import { extractBranchFromExchanges } from '$lib/utils/remote-agent-utils';
	import { GitBranch } from '$lib/icons/GitBranchIcon.svelte';
	import RemoteAgentStatusIndicator from '../feedback/RemoteAgentStatusIndicator.svelte';
	import AuggieAvatar from '../visualization/AuggieAvatar.svelte';
	import CustomIcon from '../visualization/CustomIcon.svelte';
	import EditableText from '../EditableText.svelte';
	import RemoteAgentMenu from '../RemoteAgentMenu.svelte';
	import AgentDeletionModal from '../overlays/AgentDeletionModal.svelte';
	import Tooltip from '../overlays/Tooltip.svelte';

	import { addToast } from '$lib/stores/toast';
	import Button from '../navigation/Button.svelte';
	import Pill from '../navigation/Pill.svelte';
	import { GitRepo } from '$lib/icons/GitRepoIcon.svelte';

	interface Props {
		agent: CleanRemoteAgent;
		hasActions?: boolean;
		onAgentDeleted?: () => void;
	}

	let { agent, hasActions, onAgentDeleted }: Props = $props();

	let linkedEntityInfo = $derived(getEntityByAgent(agent.id));
	let linkedEntity = $derived($linkedEntityInfo?.data || null);
	let triggerInfo = $derived(getTriggerForAgent(agent.id));
	let trigger = $derived($triggerInfo?.data || null);

	let relativeUpdateTime = $derived.by(reactiveRelativeTime(agent.updatedAt) as () => string);
	let relativeStartedTime = $derived.by(reactiveRelativeTime(agent.startedAt) as () => string);

	function getGitHubRepo(url: string | undefined): string {
		if (!url) return '';
		const match = url.match(/github\.com\/([^/]+\/[^/]+)/);
		return match ? match[1] : '';
	}

	let githubInfo = $derived(
		agent.githubUrl ? { url: agent.githubUrl, ref: agent.githubRef } : null
	);
	let repo = $derived(getGitHubRepo(githubInfo?.url));

	// Get chat exchanges to extract branch name
	let chatExchangesStore = $derived(agent?.id ? getChatExchanges(agent.id) : null);
	let exchanges = $derived($chatExchangesStore || []);
	let branch = $derived(extractBranchFromExchanges(exchanges));

	// Edit state
	let isEditingTitle = $state(false);

	// Action states
	let isDeleting = $state(false);
	let isPausing = $state(false);
	let isResuming = $state(false);
	let copyFeedback = $state('');
	let showDeletionModal = $state(false);
	let branchCopyFeedback = $state('');
	let repoCopyFeedback = $state('');

	// Menu action handlers
	function handleDelete() {
		if (!agent || isDeleting) return;
		showDeletionModal = true;
	}

	async function handleCopy() {
		try {
			await navigator.clipboard.writeText(agent.id);
			copyFeedback = 'Copied!';
			addToast({
				type: 'success',
				message: 'Agent ID copied to clipboard',
				duration: 2000
			});
			setTimeout(() => {
				copyFeedback = '';
			}, 2000);
		} catch (error) {
			console.error('Failed to copy agent ID:', error);
			addToast({
				type: 'error',
				message: 'Failed to copy agent ID',
				duration: 3000
			});
		}
	}

	function handleRename() {
		isEditingTitle = true;
	}

	async function handleTitleSave(newTitle: string) {
		try {
			await updateAgentTitle(agent.id, newTitle);
			addToast({
				type: 'success',
				message: 'Agent renamed successfully',
				duration: 3000
			});
		} catch (error) {
			console.error('Failed to rename agent:', error);
			addToast({
				type: 'error',
				message: 'Failed to rename agent',
				duration: 3000
			});
		}
	}

	function handlePin() {
		addToast({ type: 'info', message: 'Pin functionality coming soon!' });
	}

	async function handlePause() {
		if (isPausing) return;

		isPausing = true;
		try {
			await apiClient.agents.pause(agent.id);
			addToast({
				type: 'success',
				message: 'Agent paused successfully',
				duration: 3000
			});
		} catch (error) {
			console.error('Failed to pause agent:', error);
			addToast({
				type: 'error',
				message: 'Failed to pause agent',
				duration: 5000
			});
		} finally {
			isPausing = false;
		}
	}

	async function handleResume() {
		if (isResuming) return;

		isResuming = true;
		try {
			await apiClient.agents.resume(agent.id);

			// Ensure streaming is active after resume
			startStreamingAgent(agent.id).catch((error) => {
				console.error(`Failed to start streaming after resume for agent ${agent.id}:`, error);
			});

			addToast({
				type: 'success',
				message: 'Agent resumed successfully',
				duration: 3000
			});
		} catch (error) {
			console.error('Failed to resume agent:', error);
			addToast({
				type: 'error',
				message: 'Failed to resume agent',
				duration: 5000
			});
		} finally {
			isResuming = false;
		}
	}

	function handleMenuClose() {
		copyFeedback = '';
	}

	async function handleBranchCopy() {
		if (!branch) return;

		try {
			await navigator.clipboard.writeText(branch);
			branchCopyFeedback = 'Copied!';
			addToast({
				type: 'success',
				message: 'Branch name copied to clipboard',
				duration: 2000
			});
			setTimeout(() => {
				branchCopyFeedback = '';
			}, 2000);
		} catch (error) {
			console.error('Failed to copy branch name:', error);
			addToast({
				type: 'error',
				message: 'Failed to copy branch name',
				duration: 3000
			});
		}
	}

	async function handleRepoCopy() {
		if (!repo) return;

		try {
			await navigator.clipboard.writeText(repo);
			repoCopyFeedback = 'Copied!';
			addToast({
				type: 'success',
				message: 'Repository name copied to clipboard',
				duration: 2000
			});
			setTimeout(() => {
				repoCopyFeedback = '';
			}, 2000);
		} catch (error) {
			console.error('Failed to copy repository name:', error);
			addToast({
				type: 'error',
				message: 'Failed to copy repository name',
				duration: 3000
			});
		}
	}
</script>

<div class="1 flex-shrink-0 px-5 lg:px-0">
	<AuggieAvatar colorSeed={agent.id} faceSeed={agent.id} size={50} />
</div>
<div class="relative flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
	<div class="flex min-w-0 flex-1 items-start gap-3">
		<!-- Agent Name and Status -->
		<div class="min-w-0 flex-1 gap-1">
			<div class="flex w-full flex-col gap-1 px-5 md:flex-row md:items-center md:gap-5 lg:px-0">
				<div class="flex min-w-0 flex-1 flex-col gap-1 whitespace-nowrap">
					<div class="flex w-full items-center justify-between gap-2">
						<div class="min-w-0 flex-1 overflow-hidden">
							{#if hasActions}
								<EditableText
									value={agent.title || 'Untitled Agent'}
									bind:isEditing={isEditingTitle}
									onSave={handleTitleSave}
									class="text-lg font-semibold text-slate-900 dark:text-slate-100"
								/>
							{:else}
								<span class="text-lg font-semibold text-slate-900 dark:text-slate-100">
									{agent.title || 'Untitled Agent'}
								</span>
							{/if}
						</div>

						{#if hasActions}
							<!-- Kebab Menu -->
							<RemoteAgentMenu
								{agent}
								onClose={handleMenuClose}
								onPin={handlePin}
								onCopy={handleCopy}
								onRename={handleRename}
								onDelete={handleDelete}
								onPause={handlePause}
								onResume={handleResume}
								{isDeleting}
								{isPausing}
								{isResuming}
								{copyFeedback}
							/>
						{/if}
					</div>

					{#if linkedEntity?.title}
						<div class="-mt-0.5 mb-0.5 flex min-w-0 flex-1 items-center gap-1">
							<CustomIcon icon={linkedEntity.providerId} size={13} class="flex-shrink-0 " />
							<span class="truncate text-xs text-slate-500 dark:text-slate-400">
								{`${trigger?.name || '[Deleted trigger]'}`}
							</span>
							<a href={linkedEntity.url} target="_blank" rel="noopener noreferrer">
								<Icon src={ArrowTopRightOnSquare} class="h-4 w-4 text-slate-500" mini />
							</a>
						</div>
					{/if}
					<div class="ml-0.5 flex flex-col flex-wrap gap-1 lg:flex-row lg:items-center lg:gap-x-2">
						<!-- First metadata line: Status and repo -->
						<div class="flex flex-wrap items-center gap-x-2 gap-y-1">
							<RemoteAgentStatusIndicator
								status={agent.status}
								workspaceStatus={agent.workspaceStatus}
								hasUpdates={agent.hasUpdates}
								size="md"
								isExpanded
							/>
							<!-- repo -->
							<div class="group/repo flex items-center">
								<Icon src={GitRepo} class="mr-0.5 mb-[-2px] h-3 w-3 text-slate-500" micro />
								<a
									href={agent.githubUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="text-xs text-slate-500 hover:underline dark:text-slate-400"
								>
									{repo}
								</a>
								<Tooltip text={repoCopyFeedback || 'Copy repository name'} class="flex" delay={0}>
									<Button
										onclick={handleRepoCopy}
										aria-label="Copy repository name"
										icon={repoCopyFeedback ? Check : DocumentDuplicate}
										size="icon-xs"
										variant="ghost-light"
										class="opacity-0 transition-opacity duration-200 group-hover/repo:opacity-100"
									/>
								</Tooltip>
							</div>
							<!-- branch -->
							{#if branch}
								<div class="group/branch flex items-center">
									<Icon src={GitBranch} class="mr-0.5 h-3 w-3 text-slate-500" mini />
									<a
										href={`${agent.githubUrl}/tree/${branch}`}
										target="_blank"
										rel="noopener noreferrer"
										class="text-xs text-slate-500 hover:underline dark:text-slate-400"
									>
										{branch}
									</a>
									<Tooltip text={branchCopyFeedback || 'Copy branch name'} class="flex" delay={0}>
										<Button
											onclick={handleBranchCopy}
											aria-label="Copy branch name"
											icon={branchCopyFeedback ? Check : DocumentDuplicate}
											size="icon-xs"
											variant="ghost-light"
											class="opacity-0 transition-opacity duration-200 group-hover/branch:opacity-100"
										/>
									</Tooltip>
								</div>
							{/if}
						</div>

						<!-- Second metadata line: Timestamps -->
						<div class="flex flex-wrap items-center gap-x-2 gap-y-1">
							{#if agent.startedAt}
								<span class="text-xs text-slate-500 dark:text-slate-400">
									Started {relativeStartedTime}
								</span>
							{/if}
							{#if agent.updatedAt && relativeStartedTime !== relativeUpdateTime}
								<span class="text-xs text-slate-500 dark:text-slate-400">
									{agent.startedAt ? '• ' : ''}Updated {relativeUpdateTime}
								</span>
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Agent Deletion Modal -->
<AgentDeletionModal
	isOpen={showDeletionModal}
	{agent}
	onClose={() => (showDeletionModal = false)}
	{onAgentDeleted}
/>
