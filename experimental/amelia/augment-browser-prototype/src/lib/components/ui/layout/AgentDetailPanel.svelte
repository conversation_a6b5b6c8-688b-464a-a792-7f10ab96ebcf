<script lang="ts">
	import ChatHistoryView from '$lib/components/chat-history/ChatHistoryView.svelte';

	import Notebook from '$lib/components/ui/content/Notebook.svelte';
	import CodeChangesTab from '$lib/components/agents/CodeChangesTab.svelte';
	import RepositoryInfo from '$lib/components/ui/RepositoryInfo.svelte';
	import {
		Icon,
		ChatBubbleLeftRight,
		ChatBubbleLeft,
		BookOpen,
		CodeBracket,
		ListBullet,
		Bars3
	} from 'svelte-hero-icons';
	import { getRelativeTimeForStr } from '$lib/utils/time';

	import { getAgentRawExchanges } from '$lib/utils/agent-data';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';

	interface Props {
		agent: CleanRemoteAgent | null;
		projectId?: string;
		taskId?: string;
		onClose?: () => void;
		onAgentDeleted?: () => void;
	}

	let { agent, projectId, taskId, onClose, onAgentDeleted }: Props = $props();

	// Tab state for switching between chat, plain-chat, notebook, and code changes
	let activeTab: 'chat' | 'plain-chat' | 'notebook' | 'code' = $state('chat');

	// Summary mode toggle for chat tab
	let summaryMode = $state(false);

	const relativeStartedAt = $derived(
		agent?.startedAt ? getRelativeTimeForStr(agent.startedAt.toISOString()) : ''
	);
	const relativeUpdatedAt = $derived(
		agent?.updatedAt ? getRelativeTimeForStr(agent.updatedAt.toISOString()) : ''
	);

	// Get raw chat history for plain chat tab
	let rawChatHistoryStore = $derived(agent ? getAgentRawExchanges(agent.id) : null);
	let rawChatHistory = $derived.by(() => {
		if (!rawChatHistoryStore) return [];
		return $rawChatHistoryStore || [];
	});
</script>

{#if agent}
	<div class="flex w-full flex-col">
		<!-- Header -->
		<!-- <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
			<div class="flex items-center justify-between">
				<div class="flex-1 min-w-0">
					<h2 class="text-xl font-medium text-gray-900 dark:text-white truncate tracking-tight">
						{agent.session_summary ? extractOriginalMessageFromInitial(agent.session_summary).split('\n')[0] : 'Agent Details'}
					</h2>
				</div>
				<div class="flex items-center gap-3 ml-4 flex-shrink-0">
					<RemoteAgentStatusIndicator
						status={agent.status}
						hasUpdates={agent.has_updates}
						isExpanded={true}
						class="scale-110"
					/>
					<Button
						variant="ghost"
						size="icon-sm"
						icon={Trash}
						onclick={deleteAgent}
						class="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
						aria-label="Delete agent"
					/>
					{#if onClose}
						<Button
							variant="ghost"
							size="icon-sm"
							icon={XMark}
							onclick={onClose}
							aria-label="Close"
						/>
					{/if}
				</div>
			</div>
		</div> -->

		<!-- Agent Info Header -->
		<div
			class="border-b border-gray-200 bg-gray-50 px-3 py-3 sm:px-4 lg:px-6 dark:border-gray-700 dark:bg-gray-800/50"
		>
			<!-- Agent Details Grid -->
			<div class="flex flex-wrap items-center gap-4 sm:gap-6 lg:gap-9">
				<!-- Timestamps -->

				<div class="flex gap-3 text-xs italic sm:gap-4 lg:gap-6">
					<div class="text-slate-500 dark:text-white">Created {relativeStartedAt}</div>

					{#if relativeStartedAt !== relativeUpdatedAt}
						<div class="text-slate-500 dark:text-white">Updated {relativeUpdatedAt}</div>
					{/if}
				</div>

				<!-- Repository Info -->
				{#if agent.githubUrl && agent.githubRef}
					<RepositoryInfo
						repositoryUrl={agent.githubUrl}
						gitRef={agent.githubRef}
						size="sm"
						layout="horizontal"
					/>
				{/if}
			</div>
		</div>

		<!-- Tab Navigation -->
		<div class="border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-900">
			<div class="flex items-center justify-between">
				<div class="flex">
					<button
						class="flex items-center gap-1 border-b-2 px-2 py-2 text-xs font-medium transition-colors sm:gap-2 sm:px-3 sm:py-3 sm:text-sm lg:px-4 {activeTab ===
						'chat'
							? 'border-blue-500 text-blue-600 dark:text-blue-400'
							: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
						onclick={() => (activeTab = 'chat')}
					>
						<Icon src={ChatBubbleLeftRight} class="h-3 w-3 sm:h-4 sm:w-4" />
						<span class="hidden sm:inline">Chat History</span>
						<span class="sm:hidden">Chat</span>
					</button>
					<button
						class="flex items-center gap-1 border-b-2 px-2 py-2 text-xs font-medium transition-colors sm:gap-2 sm:px-3 sm:py-3 sm:text-sm lg:px-4 {activeTab ===
						'plain-chat'
							? 'border-blue-500 text-blue-600 dark:text-blue-400'
							: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
						onclick={() => (activeTab = 'plain-chat')}
					>
						<Icon src={ChatBubbleLeft} class="h-3 w-3 sm:h-4 sm:w-4" />
						<span class="hidden sm:inline">Plain Chat</span>
						<span class="sm:hidden">Plain</span>
					</button>
					<button
						class="flex items-center gap-1 border-b-2 px-2 py-2 text-xs font-medium transition-colors sm:gap-2 sm:px-3 sm:py-3 sm:text-sm lg:px-4 {activeTab ===
						'notebook'
							? 'border-blue-500 text-blue-600 dark:text-blue-400'
							: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
						onclick={() => (activeTab = 'notebook')}
					>
						<Icon src={BookOpen} class="h-3 w-3 sm:h-4 sm:w-4" />
						<span class="hidden sm:inline">Notebook</span>
						<span class="sm:hidden">Notes</span>
					</button>
					<button
						class="flex items-center gap-1 border-b-2 px-2 py-2 text-xs font-medium transition-colors sm:gap-2 sm:px-3 sm:py-3 sm:text-sm lg:px-4 {activeTab ===
						'code'
							? 'border-blue-500 text-blue-600 dark:text-blue-400'
							: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
						onclick={() => (activeTab = 'code')}
					>
						<Icon src={CodeBracket} class="h-3 w-3 sm:h-4 sm:w-4" />
						<span class="hidden sm:inline">Code & PR</span>
						<span class="sm:hidden">Code</span>
					</button>
				</div>

				<!-- Summary Mode Toggle (only show when chat tab is active) -->
				{#if activeTab === 'chat'}
					<div class="flex items-center gap-1 px-2 sm:gap-2 sm:px-3 lg:px-4">
						<span class="text-xs text-gray-500 dark:text-gray-400">View:</span>
						<button
							class="flex items-center gap-1 rounded px-2 py-1 text-xs transition-colors {summaryMode
								? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
								: 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'}"
							onclick={() => (summaryMode = !summaryMode)}
							title={summaryMode ? 'Switch to detailed view' : 'Switch to summary view'}
						>
							<Icon src={summaryMode ? Bars3 : ListBullet} class="h-3 w-3" />
							{summaryMode ? 'Summary' : 'Detailed'}
						</button>
					</div>
				{/if}
			</div>
		</div>

		<!-- Content Area -->
		<div class="flex-1 overflow-hidden">
			{#if activeTab === 'chat'}
				<div class="h-full p-3 sm:p-4 lg:p-6">
					<ChatHistoryView exchanges={rawChatHistory} {agent} />
				</div>
			{:else if activeTab === 'plain-chat'}
				<div class="h-full p-3 sm:p-4 lg:p-6">
					<div class="space-y-1">
						<div class="flex w-full justify-start text-left font-mono text-xs">
							{agent.id}
						</div>
						<ChatHistoryView exchanges={rawChatHistory} {agent} />
					</div>
				</div>
			{:else if activeTab === 'notebook'}
				<Notebook {agent} />
			{:else if activeTab === 'code'}
				<CodeChangesTab {agent} {projectId} {taskId} />
			{/if}
		</div>
	</div>
{/if}
