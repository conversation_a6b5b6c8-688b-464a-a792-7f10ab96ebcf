<script lang="ts">
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import {
		ArrowTopRightOnSquare,
		ChatBubbleLeftRight,
		CodeBracket,
		ExclamationTriangle,
		Icon,
		MagnifyingGlass
	} from 'svelte-hero-icons';
	import RemoteAgentStatusIndicator from '$lib/components/ui/feedback/RemoteAgentStatusIndicator.svelte';
	import {
		sendChatMessage,
		subscribeToAgentStreaming,
		unsubscribeFromAgentStreaming
	} from '$lib/stores/data-operations.svelte';
	import {
		deleteAgent,
		getTriggerForAgent,
		getEntityByAgent,
		getChatExchanges,
		getChatSession
	} from '$lib/stores/global-state.svelte';
	import { loadChatHistory } from '$lib/stores/data-operations.svelte';
	import { addToast } from '$lib/stores/toast';
	import { getAgentChangedFilesWithDetails } from '$lib/utils/agent-data';

	import { parseSessionSummaryTitle } from '$lib/utils/session-summary-parser';
	import {
		getExchangesSummary,
		getExchangesGeneralSummary
	} from '$lib/utils/exchange-summary-utils';
	import { getRelativeTime } from '$lib/utils/time';
	import { onMount, onDestroy } from 'svelte';
	import ChatHistoryView from '$lib/components/chat-history/ChatHistoryView.svelte';
	import ConsolidatedHeader from './ConsolidatedHeader.svelte';
	import FileChangesWidget from '$lib/components/ui/widgets/FileChangesWidget.svelte';
	import SummarySkeleton from '$lib/components/ui/data-display/SummarySkeleton.svelte';
	import NextStepsSkeleton from '$lib/components/ui/data-display/NextStepsSkeleton.svelte';
	import FileChangesSkeleton from '$lib/components/ui/data-display/FileChangesSkeleton.svelte';
	import DebugInfoSkeleton from '$lib/components/ui/data-display/DebugInfoSkeleton.svelte';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';
	import AgentChatResponse from '$lib/components/agents/AgentChatResponse.svelte';
	import { RemoteAgentStatus, type CleanRemoteAgent } from '$lib/api/unified-client';
	import SetupScriptWidget from '$lib/components/ui/widgets/SetupScriptWidget.svelte';

	interface Props {
		agent: CleanRemoteAgent | null;
		onClose?: () => void;
		onAgentDeleted?: () => void;
	}

	let { agent, onClose, onAgentDeleted }: Props = $props();

	// Component ID for streaming subscription
	const componentId = `remote-agent-panel-${crypto.randomUUID()}`;

	// Subscribe to streaming when component mounts
	onMount(() => {
		if (agent?.id) {
			console.log(
				`Panel subscribing to streaming for agent ${agent.id} from component ${componentId}`
			);
			subscribeToAgentStreaming(agent.id, componentId);
		}
	});

	// Unsubscribe when component unmounts
	onDestroy(() => {
		if (agent?.id) {
			console.log(
				`Panel unsubscribing from streaming for agent ${agent.id} from component ${componentId}`
			);
			unsubscribeFromAgentStreaming(agent.id, componentId);
		}
	});

	// Also handle agent changes
	$effect(() => {
		if (agent?.id) {
			const agentId = agent.id; // Capture the ID to avoid null reference in cleanup
			console.log(`Panel agent changed, subscribing to streaming for agent ${agentId}`);
			subscribeToAgentStreaming(agentId, componentId);

			return () => {
				console.log(`Panel cleaning up streaming subscription for agent ${agentId}`);
				unsubscribeFromAgentStreaming(agentId, componentId);
			};
		}
	});

	// Tab state for switching between overview and chat history
	let activeTab: 'overview' | 'chat' = $state('overview');

	// Get trigger for this agent using new global state system
	let triggerStore = $derived(agent?.id ? getTriggerForAgent(agent.id) : null);
	let trigger = $derived($triggerStore?.data);

	// Get entity for this agent using new global state system
	let entityStore = $derived(agent?.id ? getEntityByAgent(agent.id) : null);
	let entity = $derived($entityStore?.data);

	// Get chat exchanges for this agent using new global state system
	let chatExchangesStore = $derived(agent?.id ? getChatExchanges(agent.id) : null);
	let rawExchanges = $derived(chatExchangesStore ? $chatExchangesStore : []);

	// Get chat session for streaming data
	let chatSessionStore = $derived(agent?.id ? getChatSession(agent.id) : null);
	let chatSession = $derived($chatSessionStore ? $chatSessionStore.data : null);
	let streamingMessages = $derived(chatSession?.streamingMessages || new Map());

	// Load chat history when agent is available or when switching to chat tab
	$effect(() => {
		if (agent?.id && activeTab === 'chat') {
			console.log(`Loading chat history for agent ${agent.id} (tab: ${activeTab})`);
			loadChatHistory(agent.id);
		}
	});

	// Also load chat history when agent changes (regardless of tab) to ensure it's cached
	$effect(() => {
		if (agent?.id) {
			console.log(`Preloading chat history for agent ${agent.id}`);
			loadChatHistory(agent.id);
		}
	});

	// Derived summaries
	let sessionSummary = $derived(getExchangesSummary(rawExchanges || []));
	let generalSummary = $derived(getExchangesGeneralSummary(rawExchanges || []));

	// Get changed files
	let changedFilesStore = $derived(agent?.id ? getAgentChangedFilesWithDetails(agent.id) : null);
	let changedFiles = $derived.by(() => {
		if (!changedFilesStore) return [];
		return $changedFilesStore || [];
	});

	// Determine next steps
	let nextSteps = $derived.by(() => {
		if (!agent) return [];

		const steps: Array<{
			label: string;
			description: string;
			action: string;
			variant: 'primary' | 'outline';
			icon: any;
		}> = [];
		const hasChanges = changedFiles && changedFiles.length > 0;

		if (agent.status === RemoteAgentStatus.AGENT_RUNNING) {
			steps.push({
				label: 'View Progress',
				description: 'See what the agent is currently working on',
				action: 'chat',
				variant: 'outline',
				icon: ChatBubbleLeftRight
			});
			// steps.push({
			// 	label: 'Delete Agent',
			// 	description: 'Clean up the agent and free up resources',
			// 	action: 'delete',
			// 	variant: 'outline',
			// 	icon: Trash
			// });
		} else if (agent.status === RemoteAgentStatus.AGENT_IDLE) {
			if (hasChanges) {
				steps.push({
					label: 'Create PR',
					description: 'Create a pull request with the changes',
					action: 'pr',
					variant: 'primary',
					icon: ArrowTopRightOnSquare
				});
				steps.push({
					label: 'Review Changes',
					description: 'Examine the code changes made by the agent',
					action: 'changes',
					variant: 'outline',
					icon: CodeBracket
				});
				// steps.push({
				// 	label: 'Delete Agent',
				// 	description: 'Clean up the agent and free up resources',
				// 	action: 'delete',
				// 	variant: 'outline',
				// 	icon: Trash
				// });
			} else {
				steps.push({
					label: 'Check out in VSCode',
					description: 'Open the code changes in VSCode',
					action: 'checkout',
					variant: 'outline',
					icon: MagnifyingGlass
				});
				// steps.push({
				// 	label: 'Delete Agent',
				// 	description: 'Clean up the agent and free up resources',
				// 	action: 'delete',
				// 	variant: 'outline',
				// 	icon: Trash
				// });
				// steps.push({
				// 	label: 'View Chat History',
				// 	description: 'See what the agent accomplished',
				// 	action: 'chat',
				// 	variant: 'outline',
				// 	icon: ChatBubbleLeftRight
				// });
			}
		} else if (agent.status === RemoteAgentStatus.AGENT_FAILED) {
			steps.push({
				label: 'View Error Details',
				description: 'See what went wrong and how to fix it',
				action: 'chat',
				variant: 'outline',
				icon: ExclamationTriangle
			});
			// steps.push({
			// 	label: 'Delete Agent',
			// 	description: 'Clean up the agent and free up resources',
			// 	action: 'delete',
			// 	variant: 'outline',
			// 	icon: Trash
			// });
		}

		return steps;
	});

	// State for expanded sections
	let showChatHistory = $state(false);
	let showChanges = $state(false);
	let isCreatingPR = $state(false);

	function handleAction(action: string) {
		switch (action) {
			case 'chat':
				showChatHistory = !showChatHistory;
				break;
			case 'changes':
				showChanges = !showChanges;
				break;
			case 'delete':
				deleteAgentLocal();
				break;
			case 'pr':
				createPullRequest();
				break;
		}
	}

	async function createPullRequest() {
		if (!agent?.id) {
			console.error('No current agent ID found');
			return;
		}

		isCreatingPR = true;

		try {
			// Send a chat message to the remote agent asking it to create a PR
			await sendChatMessage(
				agent.id,
				'Please create a pull request with all the changes you have made.'
			);

			addToast({
				type: 'success',
				message: 'PR creation request sent successfully',
				duration: 5000
			});

			console.log('PR creation request sent successfully');
		} catch (error) {
			console.error('Error sending PR creation request:', error);

			addToast({
				type: 'error',
				message: 'Failed to send PR creation request',
				duration: 5000
			});
		} finally {
			isCreatingPR = false;
		}
	}

	async function deleteAgentLocal() {
		if (!agent) return;

		try {
			deleteAgent(agent.id);
			onAgentDeleted?.();
			onClose?.();
		} catch (error) {
			// Error handling is done in the optimistic function
			console.error('Failed to delete agent:', error);
		}
	}
</script>

{#if agent}
	<div class="flex h-full w-full flex-col bg-white pt-5 pb-6 dark:bg-gray-900">
		<!-- Consolidated Header -->
		<ConsolidatedHeader
			title={parseSessionSummaryTitle(agent.sessionSummary)}
			type="detail"
			{onClose}
		>
			{#snippet metadata()}
				<div class="flex items-center gap-2">
					<RemoteAgentStatusIndicator
						status={agent.status}
						workspaceStatus={agent.workspaceStatus}
						hasUpdates={agent.hasUpdates}
						size="sm"
						variant="sleek"
						isExpanded={false}
					/>
					<span class="text-sm text-gray-600 dark:text-gray-400">
						{agent.status}
					</span>
					{#if agent.startedAt}
						<span class="text-sm text-gray-500 dark:text-gray-500">
							• Started {getRelativeTime(agent.startedAt)}
						</span>
					{/if}
				</div>
			{/snippet}
		</ConsolidatedHeader>

		<!-- Tab Navigation -->
		<div class="border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-900">
			<div class="flex">
				<button
					class="flex items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors {activeTab ===
					'overview'
						? 'border-blue-500 text-blue-600 dark:text-blue-400'
						: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
					onclick={() => (activeTab = 'overview')}
				>
					<Icon src={MagnifyingGlass} class="h-4 w-4" />
					Overview
				</button>
				<button
					class="flex items-center gap-2 border-b-2 px-4 py-3 text-sm font-medium transition-colors {activeTab ===
					'chat'
						? 'border-blue-500 text-blue-600 dark:text-blue-400'
						: 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}"
					onclick={() => (activeTab = 'chat')}
				>
					<Icon src={ChatBubbleLeftRight} class="h-4 w-4" />
					Chat History
				</button>
			</div>
		</div>

		<!-- Content -->
		<div class="flex-1 overflow-hidden">
			{#if activeTab === 'overview'}
				<div class="h-full space-y-6 overflow-y-auto px-[var(--panel-padding-x)] py-4">
					<!-- Summary Section -->
					{#if generalSummary || sessionSummary}
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Summary</h3>
							<div
								class="rounded-2xl border border-slate-200 bg-white px-3 py-3 dark:border-slate-700 dark:bg-slate-800"
							>
								<div class="px-3 py-3">
									<Markdown content={generalSummary || sessionSummary} />
								</div>
							</div>
						</div>
					{:else if rawExchanges === null || (rawExchanges && rawExchanges.length === 0 && agent?.id)}
						<!-- Show skeleton while loading or when no exchanges yet -->
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Summary</h3>
							<SummarySkeleton />
						</div>
					{/if}

					<!-- Setup Script Widget for setup script agents -->
					{#if agent?.isSetupScriptAgent}
						<div class="space-y-3">
							<SetupScriptWidget agentId={agent.id} />
						</div>
					{/if}

					<!-- Agent State Visualization -->
					<!-- <AgentStateVisualization
				status={agent.status}
				workspaceStatus={agent.workspaceStatus}
				hasUpdates={agent.has_updates}

				showDescription={true}
			/> -->

					<!-- Entity Context -->
					{#if entity}
						<!-- <div class="space-y-3">
					<h3 class="text-sm font-medium text-gray-900 dark:text-white">Linked Task</h3>
					<TaskCard
					trigger={trigger || undefined}
					entity={entity}
					showActions={false}
					/>
				</div> -->
					{:else if entityStore && $entityStore?.loading}
						<!-- Show skeleton while loading entity -->
						<!-- <div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Linked Task</h3>
							<TaskCardSkeleton />
						</div> -->
					{/if}

					<!-- Next Steps -->
					{#if nextSteps.length > 0}
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Next Steps</h3>
							<div class="flex flex-wrap gap-3">
								{#each nextSteps as step}
									<Button
										variant={step.variant}
										size="md"
										onclick={() => handleAction(step.action)}
										class="min-w-[10em] flex-1 justify-start"
										loading={step.action === 'pr' && isCreatingPR}
										disabled={step.action === 'pr' && isCreatingPR}
									>
										<div class="flex items-center gap-3">
											<Icon src={step.icon} class="w-7" />
											<div class="text-left">
												<div class="font-medium">{step.label}</div>
												<div class="text-xs font-normal opacity-60">{step.description}</div>
											</div>
										</div>
									</Button>
								{/each}
							</div>
						</div>
					{:else if agent && !entity && !rawExchanges}
						<!-- Show skeleton while loading data to determine next steps -->
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Next Steps</h3>
							<NextStepsSkeleton />
						</div>
					{/if}

					<!-- File Changes -->
					{#if changedFiles && changedFiles.length > 0}
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Code Changes</h3>
							<FileChangesWidget {changedFiles} isExpanded={showChanges} />
						</div>
					{:else if changedFiles === null || (rawExchanges === null && agent?.id)}
						<!-- Show skeleton while loading file changes -->
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Code Changes</h3>
							<FileChangesSkeleton />
						</div>
					{/if}

					<!-- Agent Details -->
					{#if agent}
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Debug info</h3>
							<div class="space-y-2 text-sm">
								<div class="flex justify-between">
									<span class="text-gray-600 dark:text-gray-400">Agent ID:</span>
									<span class="font-mono text-xs text-gray-900 dark:text-white">{agent.id}</span>
								</div>
								<!-- {#if execution}
							{@const exec = execution}
							<div class="flex justify-between">
								<span class="text-gray-600 dark:text-gray-400">Execution ID:</span>
								<span class="text-gray-900 dark:text-white font-mono text-xs">{exec?.id}</span>
							</div>
							<div class="flex justify-between">
								<span class="text-gray-600 dark:text-gray-400">Trigger ID:</span>
								<span class="text-gray-900 dark:text-white font-mono text-xs">{exec?.trigger_id}</span>
							</div>
						{/if} -->
							</div>
						</div>
					{:else}
						<!-- Show skeleton while loading agent -->
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Debug info</h3>
							<DebugInfoSkeleton />
						</div>
					{/if}
				</div>
			{:else if activeTab === 'chat'}
				<div class="h-full overflow-y-auto px-[var(--panel-padding-x)] py-4">
					<!-- Chat History -->
					{#if agent.status === RemoteAgentStatus.AGENT_STARTING}
						<div class="space-y-3">
							<div class="flex items-center justify-center gap-2 py-8 text-slate-500">
								<LoadingIndicator variant="asterisk" color="blue" />
								Spinning up environment...
							</div>
						</div>
					{:else}
						<div class="space-y-3">
							<h3 class="text-sm font-medium text-gray-900 dark:text-white">Agent Activity</h3>
							<div class="max-h-96 overflow-y-auto">
								<ChatHistoryView
									exchanges={rawExchanges || []}
									autoCollapse={false}
									maxHeight="24rem"
									workspaceStatus={agent.workspaceStatus}
									{streamingMessages}
									{agent}
								/>
							</div>
							<AgentChatResponse agentId={agent.id} />
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>
{:else}
	<div class="flex flex-1 items-center justify-center">
		<div class="text-center">
			<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Agent not found</h3>
			<p class="text-sm text-gray-500 dark:text-gray-400">
				The agent you're looking for doesn't exist or has been deleted.
			</p>
		</div>
	</div>
{/if}
