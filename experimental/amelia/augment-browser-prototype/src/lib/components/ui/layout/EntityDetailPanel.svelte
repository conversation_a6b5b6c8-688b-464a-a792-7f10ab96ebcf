<script lang="ts">
	import { Icon, User, Calendar, Tag, CodeBracket } from 'svelte-hero-icons';
	import { getRelativeTimeForStr } from '$lib/utils/time';
	import { GitBranch } from './CustomIcon.svelte';
	import type { UnifiedEntity } from '$lib/utils/entity-conversion';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	interface Props {
		entity: UnifiedEntity | null;
	}

	let { entity }: Props = $props();
</script>

{#if entity}
	<div class="h-full flex flex-col bg-white dark:bg-slate-900">
		<!-- Content -->
		<div class="flex-1 overflow-y-auto">
			<div class="p-6 space-y-8">
				<!-- Description -->
				{#if entity.description}
					<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
						<h2 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
							<Icon src={CodeBracket} class="w-5 h-5 text-slate-500" />
							Description
						</h2>
						<div class="prose prose-slate dark:prose-invert max-w-none">
							<Markdown content={entity.description} />
						</div>
					</div>
				{/if}

				<!-- Key Details Cards -->
				<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
					<!-- People & Timeline -->
					<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
						<h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
							<Icon src={User} class="w-5 h-5 text-slate-500" />
							People & Timeline
						</h3>
						<div class="space-y-4">
							{#if entity.author}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={User} class="w-4 h-4 text-blue-600 dark:text-blue-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Author</p>
										<p class="text-sm text-slate-600 dark:text-slate-400">{entity.author.name}</p>
									</div>
								</div>
							{/if}

							{#if entity.assignee}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={User} class="w-4 h-4 text-green-600 dark:text-green-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Assignee</p>
										<p class="text-sm text-slate-600 dark:text-slate-400">{entity.assignee.name}</p>
									</div>
								</div>
							{/if}

							{#if entity.createdAt}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={Calendar} class="w-4 h-4 text-purple-600 dark:text-purple-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Created</p>
										<p class="text-sm text-slate-600 dark:text-slate-400">{getRelativeTimeForStr(entity.createdAt)}</p>
									</div>
								</div>
							{/if}

							{#if entity.updatedAt}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={Calendar} class="w-4 h-4 text-orange-600 dark:text-orange-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Last Updated</p>
										<p class="text-sm text-slate-600 dark:text-slate-400">{getRelativeTimeForStr(entity.updatedAt)}</p>
									</div>
								</div>
							{/if}
						</div>
					</div>

					<!-- Technical Details -->
					<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
						<h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
							<Icon src={Tag} class="w-5 h-5 text-slate-500" />
							Technical Details
						</h3>
						<div class="space-y-4">
							{#if entity.metadata.repository}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={GitBranch} class="w-4 h-4 text-slate-600 dark:text-slate-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Repository</p>
										<p class="text-sm text-slate-600 dark:text-slate-400 font-mono">{entity.metadata.repository}</p>
									</div>
								</div>
							{/if}

							{#if entity.metadata.identifier}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={Tag} class="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Identifier</p>
										<p class="text-sm text-slate-600 dark:text-slate-400 font-mono">{entity.metadata.identifier}</p>
									</div>
								</div>
							{/if}

							{#if entity.metadata.team}
								<div class="flex items-start gap-3">
									<div class="w-8 h-8 bg-cyan-100 dark:bg-cyan-900/30 rounded-full flex items-center justify-center flex-shrink-0">
										<Icon src={User} class="w-4 h-4 text-cyan-600 dark:text-cyan-400" />
									</div>
									<div class="min-w-0 flex-1">
										<p class="text-sm font-medium text-slate-900 dark:text-white">Team</p>
										<p class="text-sm text-slate-600 dark:text-slate-400">{entity.metadata.team}</p>
									</div>
								</div>
							{/if}

							<!-- Add more metadata fields if they exist -->
							{#if Object.keys(entity.metadata).length > 3}
								<div class="pt-3 border-t border-slate-200 dark:border-slate-600">
									<details class="group">
										<summary class="text-sm font-medium text-slate-700 dark:text-slate-300 cursor-pointer hover:text-slate-900 dark:hover:text-white">
											Show additional metadata
										</summary>
										<div class="mt-3 space-y-2">
											{#each Object.entries(entity.metadata) as [key, value]}
												{#if key !== 'repository' && key !== 'identifier' && key !== 'team' && key !== 'number' && value}
													<div class="flex justify-between text-sm">
														<span class="text-slate-600 dark:text-slate-400 capitalize">{key.replace('_', ' ')}</span>
														<span class="text-slate-900 dark:text-white font-mono text-xs">{value}</span>
													</div>
												{/if}
											{/each}
										</div>
									</details>
								</div>
							{/if}
						</div>
					</div>
				</div>

				<!-- Labels -->
				{#if entity.labels && entity.labels.length > 0}
					<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-6">
						<h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center gap-2">
							<Icon src={Tag} class="w-5 h-5 text-slate-500" />
							Labels
						</h3>
						<div class="flex flex-wrap gap-2">
							{#each entity.labels as label}
								<span
									class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium border"
									style="background-color: {label.color ? `#${label.color}15` : '#f8fafc'}; border-color: {label.color ? `#${label.color}40` : '#e2e8f0'}; color: {label.color ? `#${label.color}` : '#475569'}"
								>
									{label.name}
								</span>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{:else}
	<div class="flex-1 flex items-center justify-center">
		<div class="text-center">
			<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Entity not found</h3>
			<p class="text-sm text-gray-500 dark:text-gray-400">
				The entity you're looking for doesn't exist or has been deleted.
			</p>
		</div>
	</div>
{/if}
