<script lang="ts">
	import { page } from '$app/state';
	import { type Snippet } from 'svelte';
	import Sidebar from './Sidebar.svelte';
	import { createDashboardRoutes, createExtraRoutes } from '$lib/utils/dashboard-routes';
	import { exploratoryMode } from '$lib/stores/debug-settings';

	interface DashboardLayoutProps {
		children?: Snippet;
	}

	let { children }: DashboardLayoutProps = $props();

	// Get current page from URL
	let currentPage = $derived.by(() => {
		const route = page.route.id || '';
		return route.split('/')[1];
	});

	// Mobile detection
	let isMobile = $state(false);

	$effect(() => {
		if (typeof window !== 'undefined') {
			const checkMobile = () => {
				isMobile = window.innerWidth < 768; // md breakpoint
			};
			checkMobile();
			window.addEventListener('resize', checkMobile);
			return () => window.removeEventListener('resize', checkMobile);
		}
	});

	// Generate routes dynamically based on exploratory mode and mobile state
	let routes = $derived(createDashboardRoutes('default', currentPage, $exploratoryMode, isMobile));
	let actions = $derived(createExtraRoutes('default', currentPage, isMobile, $exploratoryMode));
	const PUBLIC_ROUTES = [
		'/site-login',
		'/api/site-auth',
		'/login',
		'/auth',
		'/api/auth',
		'/githubUserCallback',
		'/linearCallback'
	];
	const isPublicRoute = $derived.by(() => {
		const route = page.route.id || '';
		return PUBLIC_ROUTES.some((r) => route.startsWith(r));
	});
</script>

{#if isPublicRoute}
	<main class="flex-1 overflow-auto">
		{@render children?.()}
	</main>
{:else}
	<!-- Layout Rendering -->
	<div class="relative" style="--sidebar-width: 50px">
		<!-- Sidebar Component (handles both mobile and desktop) -->
		<Sidebar {routes} {actions} />

		<!-- Content -->
		<main class="overflow-autox relative z-[5] flex-1 pb-12 lg:pb-0 lg:pl-[var(--sidebar-width)]">
			<div class="h-full w-full">
				{@render children?.()}
			</div>
		</main>
	</div>
{/if}
