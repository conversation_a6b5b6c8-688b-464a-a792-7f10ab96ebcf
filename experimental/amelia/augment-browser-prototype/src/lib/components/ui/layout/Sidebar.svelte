<script lang="ts">
	import { Icon, Sun, Moon, UserCircle, EllipsisHorizontal, Bars3 } from 'svelte-hero-icons';
	import Logo from '$lib/components/ui/visualization/Logo.svelte';
	import { theme, toggleTheme } from '$lib/stores/theme';
	import { page } from '$app/state';
	import Drawer from '$lib/components/ui/overlays/Drawer.svelte';
	import { fly, crossfade } from 'svelte/transition';
	import { quintOut } from 'svelte/easing';
	import { agents } from '$lib/stores/global-state.svelte';
	import { RemoteAgentStatus, RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';
	import RemoteAgentListItem from '$lib/components/ui/data-display/RemoteAgentListItem.svelte';
	import AuggieAvatar from '$lib/components/ui/visualization/AuggieAvatar.svelte';
	import RemoteAgentTitle from '$lib/components/ui/RemoteAgentTitle.svelte';
	import RemoteAgentStatusIndicator from '$lib/components/ui/feedback/RemoteAgentStatusIndicator.svelte';
	import { goto } from '$app/navigation';

	interface RouteItem {
		name: string;
		label: string;
		href: string;
		icon: any; // Heroicon component
		isActive?: boolean;
	}

	interface SidebarProps {
		// Navigation
		routes?: RouteItem[];
		actions?: RouteItem[];
	}

	let { routes = [], actions = [] }: SidebarProps = $props();

	// Create crossfade instance for agent list animations
	const [send, receive] = crossfade({
		duration: 300,
		easing: quintOut
	});

	let currentAgentId = $derived(page.params.taskId);

	let currentPage = $derived.by(() => {
		const route = page.route.id || '';
		return route.split('/')[1];
	});

	// Mobile drawer state
	let showMobileDrawer = $state(false);

	function openMobileDrawer() {
		showMobileDrawer = true;
	}

	function closeMobileDrawer() {
		showMobileDrawer = false;
	}

	// Close drawer when navigating to a new page
	function handleNavClick() {
		closeMobileDrawer();
	}

	// Filter active remote agents - show running, starting, or recently active agents
	let allActiveAgents = $derived(
		$agents.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
	);

	// Limit to 6 most recent for sidebar space
	let activeAgents = $derived(allActiveAgents.slice(0, 6));

	// Calculate how many additional agents there are beyond the displayed 6
	let additionalAgentsCount = $derived(Math.max(0, allActiveAgents.length - 6));

	// Handle agent click - navigate to agent detail
	function handleAgentClick(agent: any) {
		goto(`/agents/${agent.id}`);
	}
</script>

<!-- Mobile Bottom Navigation (visible on mobile only) -->
<!-- <nav
	class="safe-area-pb fixed right-0 bottom-0 left-0 z-40 bg-slate-100 lg:hidden dark:bg-slate-900"
>
	<div class="flex items-center p-1 pt-0">
		{#each routes as route}
			{@const isActive = route.isActive || currentPage === route.name}
			<a
				href={route.href}
				class="flex min-w-0 flex-1 transform flex-col items-center justify-center px-2 py-2.5 transition-all {isActive ||
				currentPage === route.name
					? '-translate-y- rounded-b-lg bg-white text-slate-900 dark:bg-slate-950 dark:text-slate-100'
					: 'text-slate-500 hover:text-slate-700 dark:text-slate-500 dark:hover:text-slate-300'}"
			>
				<Icon src={route.icon} size="18" class="flex-shrink-0" />
				<span class="text-xs font-medium">{route.label}</span>
			</a>
		{/each}
	</div>
</nav> -->

<!-- mobile frame -->
<!-- <div
	class="pointer-events-none fixed -inset-1 bottom-12.5 z-30 rounded-xl border-8 border-slate-100 lg:hidden dark:border-slate-950"
></div> -->

<!-- Mobile Header with Logo (clickable for settings) -->
<header class="fixed top-0 left-0 z-50 h-12 bg-white lg:hidden dark:bg-slate-900">
	<div class="flex h-full items-center justify-center">
		<button
			onclick={openMobileDrawer}
			class="flex items-center gap-2 rounded-lg px-3 py-2 transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
		>
			<Icon src={Bars3} class="h-4 w-4" micro />
		</button>
	</div>
</header>
<!-- <header class="lg:hidden fixed top-0 left-0 right-0 bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 z-50 h-12">
	<div class="flex items-center justify-center h-full">
		<button
			onclick={openMobileDrawer}
			class="flex items-center gap-2 px-3 py-2 rounded-lg transition-colors hover:bg-slate-100 dark:hover:bg-slate-800"
		>
			<Logo height={20} hasText />
		</button>
	</div>
</header> -->

<!-- Desktop Sidebar (hidden on mobile) -->
<header
	class="sidebar group/sidebar fixed top-0 bottom-0 left-0 z-10 hidden max-w-[var(--sidebar-width)] overflow-hidden border-r border-white bg-white shadow-sm transition-all duration-200 hover:max-w-[300px] lg:block dark:border-slate-700 dark:bg-slate-900"
>
	<div class="h-full w-[300px] pt-5 pb-2 pl-1">
		<div class="flex h-full flex-col items-start gap-6">
			<a href="/agents" class="px-2.5">
				<Logo height={20} hasText />
			</a>
			<nav class="flex w-full flex-1 flex-col items-start justify-start gap-1">
				<!-- Main Routes -->
				{#each routes as route, index (route.href)}
					{@const isActive = route.isActive || currentPage === route.name}
					<a
						transition:fly={{ x: -20, duration: 300, delay: index * 100 }}
						href={route.href}
						class="flex w-full items-center gap-2 px-2.5 py-3 text-sm font-medium transition-all
							{isActive || currentPage === route.name
							? 'text-blue-700 dark:text-blue-300'
							: 'text-slate-600 hover:border-slate-300 hover:text-slate-900 dark:text-slate-400 dark:hover:border-slate-600 dark:hover:text-slate-200'}"
					>
						<Icon src={route.icon} class="h-5 w-5 flex-none" micro />
						<div
							class="-translate-x-2 transform opacity-0 transition-all duration-200 group-hover/sidebar:translate-x-0 group-hover/sidebar:opacity-100"
						>
							{route.label}
						</div>
					</a>
				{/each}

				<!-- Active Remote Agents Section -->
				{#if activeAgents.length > 0}
					<div class="mt-1 w-full">
						<!-- <div class="px-3 pb-2">
							<div
								class="-translate-x-2 transform opacity-0 transition-all duration-200 group-hover/sidebar:translate-x-0 group-hover/sidebar:opacity-100"
							>
								<span class="text-xs font-medium text-slate-500 dark:text-slate-400">
									Active Agents ({activeAgents.length})
								</span>
							</div>
						</div> -->
						<div class="">
							{#each activeAgents as agent (agent.id)}
								<div in:receive={{ key: agent.id }} out:send={{ key: agent.id }}>
									<RemoteAgentListItem
										{agent}
										onAgentClick={handleAgentClick}
										isSelected={agent.id === currentAgentId}
									/>
								</div>
							{/each}
							{#if additionalAgentsCount > 0}
								<div class="px-2.5 py-1.5">
									<a
										href="/agents"
										class="-translate-x-2 transform text-xs text-slate-500 opacity-0 transition-all duration-200 group-hover/sidebar:translate-x-0 group-hover/sidebar:opacity-100 dark:text-slate-400"
									>
										+ {additionalAgentsCount} more
									</a>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</nav>

			<div class="mt-auto flex w-full flex-none flex-col items-start pr-2">
				<!-- Dark Mode Toggle -->
				<button
					onclick={toggleTheme}
					class="group relative cursor-pointer rounded-lg p-3 text-slate-600 transition-all duration-200 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200"
					aria-label="Toggle dark mode"
				>
					<!-- Sun icon (visible in dark mode) -->
					<div
						class="absolute inset-0 flex items-center justify-center transition-all duration-300 {$theme ===
						'dark'
							? 'scale-100 rotate-0 opacity-100'
							: 'scale-75 rotate-90 opacity-0'}"
					>
						<Icon src={Sun} class="h-4 w-4" micro />
					</div>
					<!-- Moon icon (visible in light mode) -->
					<div
						class="absolute inset-0 flex items-center justify-center transition-all duration-300 {$theme ===
						'light'
							? 'scale-100 rotate-0 opacity-100'
							: 'scale-75 -rotate-90 opacity-0'}"
					>
						<Icon src={Moon} class="h-4 w-4" micro />
					</div>
					<!-- Invisible spacer to maintain button size -->
					<div class="h-4 w-4 opacity-0">
						<Icon src={Sun} class="h-4 w-4" micro />
					</div>
				</button>

				{#each actions as route, index (route.href)}
					{@const isActive = route.isActive || currentPage === route.name}
					<a
						transition:fly={{ y: 20, duration: 300, delay: index * 100 }}
						href={route.href}
						class="flex w-full items-center gap-2 rounded-lg px-3 py-3 text-sm font-medium transition-all
						{isActive || currentPage === route.name
							? 'text-blue-700 dark:text-blue-300'
							: 'text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200'}"
					>
						<Icon src={route.icon} class="h-4 w-4 flex-none" mini />
						<div
							class="-translate-x-2 transform opacity-0 transition-all duration-200 group-hover/sidebar:translate-x-0 group-hover/sidebar:opacity-100"
						>
							{route.label}
						</div>
					</a>
				{/each}
			</div>
		</div>
	</div>
</header>

<!-- Mobile Profile/Settings Drawer -->
<Drawer
	open={showMobileDrawer}
	onClose={closeMobileDrawer}
	position="left"
	size="sm"
	title="Augment Actions"
	class="[--panel-padding-x:1rem]"
>
	<div class="flex h-full flex-col">
		<!-- Profile section -->
		<div class="border-b border-slate-200 px-2 py-4 dark:border-slate-700">
			<!-- routes -->
			{#each routes as route, index (route.href)}
				{@const isActive = route.isActive || currentPage === route.name}
				<a
					transition:fly={{ x: -20, duration: 300, delay: index * 100 }}
					href={route.href}
					onclick={handleNavClick}
					class="flex w-full items-center gap-2 px-3 py-3 text-sm font-medium transition-all
							{isActive || currentPage === route.name
						? 'text-blue-700 dark:text-blue-300'
						: 'text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200'}"
				>
					<Icon src={route.icon} class="h-5 w-5 flex-none" micro />
					{route.label}
				</a>
			{/each}

			<!-- Active Remote Agents Section -->
			{#if activeAgents.length > 0}
				<div class="mt-4">
					<!-- <div class="px-3 pb-2">
						<span class="text-xs font-medium text-slate-500 dark:text-slate-400">
							Active Agents ({activeAgents.length})
						</span>
					</div> -->
					<div class="mb-6">
						{#each activeAgents as agent (agent.id)}
							<div
								in:receive={{ key: agent.id }}
								out:send={{ key: agent.id }}
								class="flex w-full items-center gap-3 rounded-lg px-3 py-1 text-sm font-medium text-slate-600 transition-all hover:bg-slate-100 hover:text-slate-900 dark:text-slate-400 dark:hover:bg-slate-800 dark:hover:text-slate-200"
								onclick={() => {
									handleAgentClick(agent);
									handleNavClick();
								}}
								onkeydown={(e) => {
									if (e.key === 'Enter' || e.key === ' ') {
										e.preventDefault();
										handleAgentClick(agent);
										handleNavClick();
									}
								}}
								role="button"
								tabindex="0"
							>
								<div class="flex-shrink-0">
									<AuggieAvatar colorSeed={agent.id} faceSeed={agent.id} size={20} />
								</div>
								<div class="min-w-0 flex-1">
									<RemoteAgentTitle
										{agent}
										size="sm"
										class="!line-clamp-1 truncate text-sm font-normal"
									/>
								</div>
								<div class="flex-shrink-0">
									<RemoteAgentStatusIndicator
										status={agent.status}
										workspaceStatus={agent.workspaceStatus}
										hasUpdates={agent.hasUpdates}
										size="sm"
									/>
								</div>
							</div>
						{/each}
						{#if additionalAgentsCount > 0}
							<a
								href="/agents"
								class="cursor-pointer px-3 py-1 text-xs font-medium text-slate-500 hover:underline dark:text-slate-400"
							>
								+ {additionalAgentsCount} more
							</a>
						{/if}
					</div>
				</div>
			{/if}

			<!-- Settings and Actions -->
			<div class="flex-1 space-y-2 py-2">
				<!-- Dark Mode Toggle -->
				<button
					onclick={() => {
						toggleTheme();
						handleNavClick();
					}}
					class="flex w-full items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium text-slate-600 transition-all hover:bg-slate-100 hover:text-slate-900 dark:text-slate-400 dark:hover:bg-slate-800 dark:hover:text-slate-200"
				>
					<div class="relative h-5 w-5">
						<!-- Sun icon (visible in dark mode) -->
						<div
							class="absolute inset-0 flex items-center justify-center transition-all duration-300 {$theme ===
							'dark'
								? 'scale-100 rotate-0 opacity-100'
								: 'scale-75 rotate-90 opacity-0'}"
						>
							<Icon src={Sun} class="h-5 w-5" micro />
						</div>
						<!-- Moon icon (visible in light mode) -->
						<div
							class="absolute inset-0 flex items-center justify-center transition-all duration-300 {$theme ===
							'light'
								? 'scale-100 rotate-0 opacity-100'
								: 'scale-75 -rotate-90 opacity-0'}"
						>
							<Icon src={Moon} class="h-5 w-5" micro />
						</div>
					</div>
					<span>{$theme === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}</span>
				</button>

				<!-- Actions section -->
				{#each actions as route}
					{@const isActive = route.isActive || currentPage === route.name}
					<a
						href={route.href}
						onclick={handleNavClick}
						class="flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all
						{isActive || currentPage === route.name
							? 'text-blue-700 dark:text-blue-300'
							: 'text-slate-600 hover:bg-slate-100 hover:text-slate-900 dark:text-slate-400 dark:hover:bg-slate-800 dark:hover:text-slate-200'}"
					>
						<Icon src={route.icon} class="h-5 w-5 flex-none" mini />
						<span>{route.label}</span>
					</a>
				{/each}
			</div>
		</div>
	</div></Drawer
>

<style lang="postcss">
	.sidebar :global(.logo-text) {
		opacity: 0;
		transition: opacity 0.2s ease;
		transform: translateX(-2px);
		transition: transform 0.2s ease;
	}
	.sidebar:hover :global(.logo-text) {
		opacity: 1;
		transform: translateX(0);
	}
</style>
