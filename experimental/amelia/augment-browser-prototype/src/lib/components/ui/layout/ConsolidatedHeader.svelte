<script lang="ts">
	import { type Snippet } from 'svelte';
	import { ArrowLeft, Icon, XMark } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	interface ConsolidatedHeaderProps {
		// Header content
		title: string;
		subtitle?: string;
		description?: string;
		backText?: string;

		// Header type
		type: 'index' | 'detail';

		// Detail page specific
		onClose?: () => void;

		// Actions and metadata
		actions?: Snippet;
		metadata?: Snippet;
	}

	let {
		title,
		subtitle,
		description,
		backText = 'Back',
		type,
		onClose,
		actions,
		metadata,
	}: ConsolidatedHeaderProps = $props();

	// Determine header styling based on layout mode and type
	let headerClasses = $derived.by(() => {
		const isDetail = type === 'detail';

			// Header nav layout - minimal header since nav is in top header
			return isDetail
				? 'px-6 py-4'
				: 'px-6 py-3 pb-0';
	});

	let titleClasses = $derived.by(() => {
		const isDetail = type === 'detail';
			return 'text-2xl font-bold text-slate-900 dark:text-white'
	});

	let subtitleClasses = $derived.by(() => {
			return 'text-sm text-slate-600 dark:text-slate-400 mt-1';
	});
</script>

<div class="flex-shrink-0 {headerClasses}">
	<div class="flex items-center justify-between gap-3">
		<div class="flex-1 min-w-0">
			<div class="flex flex-col gap-3">
				<div class="relative flex-1 min-w-0">

				{#if type === 'detail' && onClose}
				<div class="-ml-3.5 mb-2">
					<Button
						icon={ArrowLeft}
						size="sm"
						variant="ghost-inline"
						onclick={onClose}
						aria-label="Close"
					>
						{backText}
					</Button>
					</div>
				{/if}

					<h1 class="{titleClasses} text-slate-900 dark:text-white truncate">
						{title}
					</h1>
					{#if subtitle}
						<p class="{subtitleClasses} truncate">
							{subtitle}
						</p>
					{/if}
				</div>

				{#if metadata}
					<div class="flex-shrink-0">
						{@render metadata()}
					</div>
				{/if}
			</div>
		</div>

			<div class="flex items-center gap-2 flex-shrink-0">
				{#if actions}
					{@render actions()}
					{/if}
			</div>
	</div>

	{#if description}
		<div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
			<Markdown content={description} size="sm" />
		</div>
	{/if}
</div>
