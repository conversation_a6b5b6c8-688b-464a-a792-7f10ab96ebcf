<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { fly } from 'svelte/transition';
	import { Icon, XMark, ChevronLeft, ChevronRight } from 'svelte-hero-icons';

	interface Props {
		isOpen?: boolean;
		initialWidth?: number;
		minWidth?: number;
		maxWidth?: number;
		autoCloseBreakpoint?: number;
		position?: 'left' | 'right';
		children?: import('svelte').Snippet;
	}

	let {
		isOpen = $bindable(true),
		initialWidth = 576, // 36em in pixels (assuming 16px base)
		minWidth = 320,
		maxWidth = 800,
		autoCloseBreakpoint = 800,
		position = 'right',
		children
	}: Props = $props();

	let panelElement: HTMLDivElement;
	let isResizing = $state(false);
	let currentWidth = $state(initialWidth);
	let windowWidth = $state(0);

	// Auto-close on narrow screens
	$effect(() => {
		if (browser && windowWidth > 0 && windowWidth < autoCloseBreakpoint) {
			isOpen = false;
		}
	});

	// Resize functionality
	function startResize(event: MouseEvent) {
		if (!isOpen) return;

		isResizing = true;
		event.preventDefault();

		const startX = event.clientX;
		const startWidth = currentWidth;

		function handleMouseMove(e: MouseEvent) {
			if (!isResizing) return;

			const deltaX = position === 'right' ? startX - e.clientX : e.clientX - startX;
			const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidth + deltaX));
			currentWidth = newWidth;
		}

		function handleMouseUp() {
			isResizing = false;
			document.removeEventListener('mousemove', handleMouseMove);
			document.removeEventListener('mouseup', handleMouseUp);
		}

		document.addEventListener('mousemove', handleMouseMove);
		document.addEventListener('mouseup', handleMouseUp);
	}

	// Toggle panel
	function togglePanel() {
		isOpen = !isOpen;
	}

	const toggleOpenButtonPositionClass = $derived.by(() => {
		if (position === 'right') {
			if (isOpen) return 'left-[1px] hover:w-6 -translate-x-full rounded-r-none';
			return 'left-[1px] hover:w-6 -translate-x-full rounded-r-none';
		} else {
			if (isOpen) return 'right-[1px] hover:w-6 -translate-x-full rounded-l-none';
			return 'right-[1px] hover:w-6 -translate-x-full rounded-l-none';
		}
	});
</script>

<svelte:window bind:innerWidth={windowWidth} />

<!-- Panel Container -->
<div
	bind:this={panelElement}
	class="relative flex h-full {isResizing ? '' : 'transition-all duration-300 ease-in-out'}"
	class:translate-x-full={!isOpen && position === 'right'}
	class:translate-x-[-100%]={!isOpen && position === 'left'}
	style="width: {isOpen ? currentWidth : 0}px;"
>
	<!-- Resize Handle -->
	{#if isOpen}
		<button
			class="group items-centerx absolute top-0 left-[-1.5px] z-30 flex h-full w-1 cursor-col-resize justify-center pt-20 transition-colors hover:bg-blue-500/20"
			class:left-0={position === 'right'}
			class:right-0={position === 'left'}
			onmousedown={startResize}
			aria-label="Resize panel"
		>
			<!-- Resize handle visual indicator -->
			<!-- <div
				class="ml-[-0.5px] h-8 w-0.5 bg-slate-300 transition-colors group-hover:bg-blue-500 dark:bg-slate-600 dark:group-hover:bg-blue-400"
				class:bg-blue-500={isResizing}
				class:dark:bg-blue-400={isResizing}
			></div> -->
		</button>
	{/if}

	<!-- Panel Content -->
	<div
		class="relative z-20 flex h-full w-full flex-col border-slate-200 dark:border-slate-700"
		class:border-lx={position === 'right'}
		class:border-rx={position === 'left'}
		class:lg:shadow-none={isOpen}
	>
		<!-- Panel content -->
		<div class="flex h-full flex-1 flex-col overflow-hidden" style="width: {currentWidth}px;">
			{#if children}
				{@render children()}
			{/if}
		</div>
	</div>
	<!-- Unified Toggle Button -->
	<button
		transition:fly={{ x: position === 'right' ? 30 : -30, duration: 200 }}
		onclick={togglePanel}
		class="absolute bottom-2 {toggleOpenButtonPositionClass} z-10 flex h-8 w-4 -translate-y-1/2 cursor-pointer items-center justify-center rounded-md border border-slate-200 bg-white shadow-lg transition-all hover:bg-slate-50 dark:border-slate-700 dark:bg-gray-900 dark:hover:bg-gray-800"
		aria-label={isOpen ? 'Close panel' : 'Open panel'}
	>
		<Icon
			src={position === 'left' || !isOpen ? ChevronLeft : ChevronRight}
			class="h-3 w-3 text-slate-400 dark:text-slate-500"
			micro
		/>
	</button>
</div>

<style>
	/* Custom scrollbar for the panel content */
	:global(.resizable-panel-content) {
		scrollbar-width: thin;
		scrollbar-color: rgb(203 213 225) transparent;
	}

	:global(.resizable-panel-content::-webkit-scrollbar) {
		width: 6px;
	}

	:global(.resizable-panel-content::-webkit-scrollbar-track) {
		background: transparent;
	}

	:global(.resizable-panel-content::-webkit-scrollbar-thumb) {
		background-color: rgb(203 213 225);
		border-radius: 3px;
	}

	:global(.dark .resizable-panel-content::-webkit-scrollbar-thumb) {
		background-color: rgb(71 85 105);
	}
</style>
