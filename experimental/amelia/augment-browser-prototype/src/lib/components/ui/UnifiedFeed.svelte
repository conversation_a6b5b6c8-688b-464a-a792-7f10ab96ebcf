<script lang="ts">
	import { Icon, Clock } from 'svelte-hero-icons';
	import MatchesSection from './widgets/MatchesSection.svelte';
	import ExecutionsSection from './widgets/ExecutionsSection.svelte';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
	import { shouldShowTriggerSkeleton } from '$lib/stores/global-state.svelte';
	import { setContext } from 'svelte';

	interface Props {
		triggerData: Array<{
			trigger: NormalizedTrigger;
			entities: any[];
			executions: any[];
			totalMatches: number;
			isLoading: boolean;
			error?: string;
		}>;
		onEntityClick: (entity: any, trigger?: any) => void;
		onAgentClick: (agent: any) => void;
	}

	let { triggerData, onEntityClick, onAgentClick }: Props = $props();

	// Hover state for cross-component highlighting
	let hoveredEntityId = $state<string | null>(null);
	let hoveredExecutionId = $state<string | null>(null);

	// Set up hover context for child components
	setContext('hover-context', {
		get hoveredEntityId() {
			return hoveredEntityId;
		},
		get hoveredExecutionId() {
			return hoveredExecutionId;
		},
		setHoveredEntity: (entityId: string | null) => {
			hoveredEntityId = entityId;
		},
		setHoveredExecution: (id: string | null) => {
			hoveredExecutionId = id;
		}
	});

	// Check if any trigger is loading
	let isAnyLoading = $derived(
		$shouldShowTriggerSkeleton || triggerData.some(({ isLoading }) => isLoading)
	);

	// Check if there's any activity
	let hasAnyActivity = $derived(
		triggerData.some(({ entities, executions }) => entities.length > 0 || executions.length > 0)
	);
</script>

<div class="mx-auto w-full p-3 lg:p-6">
	{#if !isAnyLoading && !hasAnyActivity}
		<!-- Empty State -->
		<div class="py-12 text-center">
			<Icon src={Clock} class="mx-auto mb-4 h-12 w-12 text-gray-300 dark:text-gray-600" />
			<h3 class="mb-2 text-lg font-semibold text-gray-900 dark:text-white">No activity yet</h3>
			<p class="text-gray-500 dark:text-gray-400">
				Your activity feed will show matches and agent executions as they happen.
			</p>
		</div>
	{:else}
		<!-- Side by side layout with section components -->
		<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
			<MatchesSection {triggerData} isLoading={isAnyLoading} {onEntityClick} {onAgentClick} />
			<ExecutionsSection {triggerData} isLoading={isAnyLoading} {onAgentClick} />
		</div>
	{/if}
</div>
