<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { getEntityByAgent } from '$lib/stores/global-state.svelte';
	import { extractInitialInstructionsFromSessionSummary } from '$lib/utils/remote-agent-utils';
	import { CommandLine, Icon } from 'svelte-hero-icons';

	interface Props {
		agent: CleanRemoteAgent;
		class?: string;
		size?: 'sm' | 'md' | 'lg';
	}

	let { agent, class: className = '', size = 'md' }: Props = $props();

	let linkedEntityInfo = $derived(getEntityByAgent(agent?.id));
	let isLoadingLinkedEntity = $derived($linkedEntityInfo.loading);
	let extractedInitialInstructions = $derived(
		extractInitialInstructionsFromSessionSummary(agent.sessionSummary)
	);

	// Size classes
	const sizeClasses = {
		sm: 'text-sm leading-tight',
		md: 'text-base leading-tight',
		lg: 'text-lg leading-tight'
	};

	const fontWeightClasses = {
		sm: 'font-medium',
		md: 'font-semibold',
		lg: 'font-bold'
	};
</script>

{#if agent.renamedTitle}
	<!-- Display title -->
	<h3
		class="{sizeClasses[size]} {fontWeightClasses[
			size
		]} line-clamp-3 flex gap-1.5 text-slate-900 dark:text-white {className}"
	>
		{agent.renamedTitle}
	</h3>
{:else if isLoadingLinkedEntity}
	<!-- Loading skeleton for linked entity -->
	<div
		class="animate-pulse rounded bg-slate-200 dark:bg-slate-700 {sizeClasses[size]} {className}"
		style="height: 1.2em; width: 12em;"
	></div>
{:else}
	<!-- Display title -->
	<h3
		class="{sizeClasses[size]} {fontWeightClasses[
			size
		]} line-clamp-3 flex gap-1.5 text-slate-900 dark:text-white {className}"
	>
		{#if agent.isSetupScriptAgent}
			<!-- Setup script agent display -->
			<div class="flex items-center gap-1 text-slate-500 dark:text-slate-400">
				<Icon src={CommandLine} class="h-4 w-4 opacity-60" micro />
				<span class="line-clamp-1">Generate a setup script</span>
			</div>
		{:else}
			<!-- Regular agent display -->
			<div class="line-clamp-1 whitespace-pre-wrap">
				{$linkedEntityInfo.data?.title ||
					extractInitialInstructionsFromSessionSummary(agent.sessionSummary) ||
					'Untitled Agent'}
			</div>
		{/if}
	</h3>
{/if}
