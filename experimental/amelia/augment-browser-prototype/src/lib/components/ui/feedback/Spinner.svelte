<script lang="ts">
	import Logo from '$lib/components/ui/visualization/Logo.svelte';
	import { onMount } from 'svelte';
	import AuggieAvatar from '../visualization/AuggieAvatar.svelte';

	interface Props {
		size?: 'sm' | 'md' | 'lg' | 'xl';
		variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
		message?: string;
	}

	let { size = 'md', variant = 'default', message = '' }: Props = $props();

	// Size variants
	const sizeClasses = {
		sm: 20,
		md: 30,
		lg: 60,
		xl: 100
	};

	let colorSeed = $state(Math.random() + '');
	let faceSeed = $state(Math.random() + '');

	// let's have a random value that updates every second
	const randomize = () => {
		colorSeed = Math.random().toString(36).substring(2, 11);
		faceSeed = Math.random().toString(36).substring(2, 11);
	};

	onMount(() => {
		const interval = setInterval(randomize, 1000);
		return () => clearInterval(interval);
	});
</script>

<div class="flex flex-col items-center justify-center">
	<AuggieAvatar {colorSeed} {faceSeed} size={sizeClasses[size]} />

	<!-- Message -->
	{#if message}
		<p class="mt-4 animate-pulse text-sm text-gray-600 dark:text-gray-400">
			{message}
		</p>
	{/if}
</div>

<style>
	/* Custom animation for a more playful spin */
	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* Gentle bounce for the floating dots */
	@keyframes bounce {
		0%,
		20%,
		53%,
		80%,
		100% {
			transform: translateY(0);
		}
		40%,
		43% {
			transform: translateY(-8px);
		}
		70% {
			transform: translateY(-4px);
		}
		90% {
			transform: translateY(-2px);
		}
	}
</style>
