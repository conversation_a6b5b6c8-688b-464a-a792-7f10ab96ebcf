<script lang="ts">
	import { Check, Icon } from 'svelte-hero-icons';
	import LoadingIndicator from './LoadingIndicator.svelte';
	import { scale, slide } from 'svelte/transition';

	interface Props {
		icon: any; // Component type
		idleLabel: string;
		successLabel: string;
		onSelect: () => Promise<void> | void;
		disabled?: boolean;
	}

	let {
		icon: IconComponent,
		idleLabel,
		successLabel,
		onSelect,
		disabled = false
	}: Props = $props();

	let state = $state<'idle' | 'loading' | 'success'>('idle');
	let timeoutId: ReturnType<typeof setTimeout> | null = null;

	async function handleSelect() {
		if (disabled || state === 'loading') return;

		state = 'loading';

		try {
			await onSelect();
			state = 'success';

			// Reset to idle after 2 seconds
			timeoutId = setTimeout(() => {
				state = 'idle';
			}, 2000);
		} catch (error) {
			console.error('Dropdown action failed:', error);
			state = 'idle';
		}
	}

	function getLabel() {
		switch (state) {
			case 'success':
				return successLabel;
			default:
				return idleLabel;
		}
	}

	// Cleanup timeout on destroy
	$effect(() => {
		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId);
			}
		};
	});
</script>

<button
	class="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-slate-100 dark:hover:bg-slate-800 {disabled
		? 'cursor-not-allowed opacity-50'
		: 'cursor-pointer'}"
	onclick={handleSelect}
	{disabled}
>
	<div class="stable-transition-container">
		{#if state === 'loading'}
			<div transition:scale class="stable-transition-item">
				<LoadingIndicator variant="wheel" size="sm" color="slate" />
			</div>
		{:else if state === 'success'}
			<div transition:scale class="stable-transition-item">
				<Icon src={Check} class="h-4 w-4 text-green-600" micro />
			</div>
		{:else}
			<div transition:scale class="stable-transition-item">
				<Icon src={IconComponent} class="h-4 w-4" micro />
			</div>
		{/if}
	</div>
	<span class={state === 'success' ? 'text-green-600' : ''}>{getLabel()}</span>
</button>
