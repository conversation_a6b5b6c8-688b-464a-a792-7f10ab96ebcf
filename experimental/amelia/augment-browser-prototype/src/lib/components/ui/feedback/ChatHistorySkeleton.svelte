<script lang="ts">
	// Skeleton loader for Chat History panel
	// Add some randomness to make it look more natural
	let messageCount = Math.floor(Math.random() * (6 - 3) + 3); // 3-6 messages
</script>

<!-- Chat History Skeleton -->
<div class="space-y-4 p-4 animate-pulse">
	{#each Array(messageCount) as _, i}
		{@const isUser = i % 3 === 0} <!-- Every 3rd message is from user -->
		{@const messageWidth = Math.random() > 0.5 ? 'w-3/4' : 'w-2/3'}
		{@const messageHeight = Math.floor(Math.random() * (80 - 40) + 40)}

		<div class="flex {isUser ? 'justify-end' : 'justify-start'}">
			<div class="max-w-[80%] {messageWidth}">
				{#if !isUser}
					<!-- Assistant message -->
					<div class="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4">
						<!-- Message header -->
						<div class="flex items-center gap-2 mb-3">
							<div class="relative w-4 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2}s;"></div>
							</div>
							<div class="relative w-16 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2 + 0.1}s;"></div>
							</div>
						</div>

						<!-- Message content -->
						<div class="space-y-2" style="height: {messageHeight}px;">
							{#each Array(Math.floor(messageHeight / 16)) as _, lineIndex}
								{@const lineWidth = lineIndex === Math.floor(messageHeight / 16) - 1 ? 'w-2/3' : 'w-full'}
								<div class="relative {lineWidth} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
									<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2 + 0.2 + (lineIndex * 0.05)}s;"></div>
								</div>
							{/each}
						</div>

						<!-- Tool calls section (sometimes) -->
						{#if Math.random() > 0.6}
							<div class="mt-4 pt-3 border-t border-slate-200 dark:border-slate-700">
								<div class="flex items-center gap-2 mb-2">
									<div class="relative w-3 h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
										<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2 + 0.5}s;"></div>
									</div>
									<div class="relative w-20 h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
										<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2 + 0.6}s;"></div>
									</div>
								</div>
								<div class="relative w-1/2 h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
									<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2 + 0.7}s;"></div>
								</div>
							</div>
						{/if}
					</div>
				{:else}
					<!-- User message -->
					<div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 p-3">
						<div class="space-y-2">
							<div class="relative w-full h-3 bg-blue-200 dark:bg-blue-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2}s;"></div>
							</div>
							<div class="relative w-3/4 h-3 bg-blue-200 dark:bg-blue-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {i * 0.2 + 0.1}s;"></div>
							</div>
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/each}

	<!-- Chat input skeleton -->
	<div class="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
		<div class="relative w-full h-10 bg-slate-200 dark:bg-slate-700 rounded-lg overflow-hidden">
			<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {messageCount * 0.2}s;"></div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
