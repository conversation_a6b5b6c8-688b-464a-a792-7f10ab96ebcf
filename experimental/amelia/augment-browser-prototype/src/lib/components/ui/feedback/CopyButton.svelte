<script lang="ts">
	import { ClipboardDocumentList, Icon } from 'svelte-hero-icons';
	import SuccessfulButton from './SuccessfulButton.svelte';

	interface Props {
		text: string | (() => string | Promise<string>) | undefined;
		tooltip?: string;
		variant?: 'primary' | 'secondary' | 'ghost' | 'outline' | 'ghost-light';
		size?: 'sm' | 'md' | 'lg' | 'icon-sm' | 'icon-md';
		class?: string;
	}

	let {
		text,
		tooltip = 'Copy',
		variant = 'ghost',
		size = 'icon-sm',
		class: className = ''
	}: Props = $props();

	async function handleCopy() {
		if (text === undefined) return undefined;

		try {
			const textToCopy = typeof text === 'string' ? text : await text();
			await navigator.clipboard.writeText(textToCopy);

			// Add a small delay for user feedback
			await new Promise((resolve) => setTimeout(resolve, 500));

			return 'success';
		} catch (error) {
			console.error('Failed to copy text:', error);
			return undefined;
		}
	}
</script>

<SuccessfulButton
	tooltip={{ neutral: tooltip, success: 'Copied!' }}
	onClick={handleCopy}
	{variant}
	{size}
	class={className}
	icon={ClipboardDocumentList}
/>
