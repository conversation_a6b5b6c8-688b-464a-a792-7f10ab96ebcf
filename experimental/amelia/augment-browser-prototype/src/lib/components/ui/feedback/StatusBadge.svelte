<script lang="ts">
	import type { Task } from '$lib/types.js';
	import type { CleanRemoteAgent, CleanChangedFile } from '$lib/api/unified-client';
	import { ArrowTopRightOnSquare, CheckCircle, Link, PauseCircle, PlayCircle } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import ButtonGroup from '../navigation/ButtonGroup.svelte';
	import PRButton from '../navigation/PRButton.svelte';

	interface Props {
		status: Task['status'];
		task?: Task;
		remoteAgent?: CleanRemoteAgent | null;
		changedFiles?: CleanChangedFile[];
		githubToken?: string;
		showPR?: boolean;
		onPRCreated?: (pr: any) => void;
		onPRUpdated?: (pr: any) => void;
	}

	let {
		status,
		task,
		remoteAgent,
		changedFiles,
		githubToken,
		showPR = false,
		onPRCreated,
		onPRUpdated
	}: Props = $props();

	const statusConfig = {
		PENDING: {
			class: 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700',
			icon: PlayCircle,
			label: 'Pending'
		},
		RUNNING: {
			class: 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-800',
			icon: PauseCircle,
			label: 'Running'
		},
		FINISHED: {
			class: 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800',
			icon: CheckCircle,
			label: 'Finished'
		},
		COMPLETED: {
			class: 'bg-emerald-100 dark:bg-emerald-900/50 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800',
			icon: CheckCircle,
			label: 'Completed'
		}
	};

	const config = statusConfig[status] || statusConfig.PENDING;
</script>

<div class="flex items-center gap-2">
	<ButtonGroup variant="secondary">
		<Button size="xs" variant="pill-slate" icon={config.icon}>
			{config.label}
		</Button>

		{#if task && (status === 'FINISHED' || status === 'COMPLETED')}
			<PRButton
				{task}
				{remoteAgent}
				{changedFiles}
				{githubToken}
				{onPRCreated}
				{onPRUpdated}
			/>
		{:else if showPR && status === 'FINISHED'}
			<Button size="xs" variant="pill-green" icon={ArrowTopRightOnSquare} iconPosition="right">
				PR created
			</Button>
		{/if}
	</ButtonGroup>
</div>
