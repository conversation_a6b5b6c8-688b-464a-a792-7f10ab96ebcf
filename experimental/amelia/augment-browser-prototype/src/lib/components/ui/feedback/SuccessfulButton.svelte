<script lang="ts">
	import { Check, Icon } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import LoadingIndicator from './LoadingIndicator.svelte';
	import Tooltip from '../overlays/Tooltip.svelte';
	import { scale } from 'svelte/transition';

	interface Props {
		tooltip?: {
			neutral: string;
			success: string;
		};
		onClick?: () => Promise<string | undefined> | string | undefined;
		variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
		size?: 'sm' | 'md' | 'lg' | 'icon-sm' | 'icon-md';
		class?: string;
		disabled?: boolean;
		icon?: any; // Component type
	}

	let {
		tooltip = { neutral: 'Click', success: 'Success!' },
		onClick,
		variant = 'ghost',
		size = 'icon-sm',
		class: className = '',
		disabled = false,
		icon: IconComponent,
		children
	}: Props = $props();

	let state = $state<'idle' | 'loading' | 'success'>('idle');
	let timeoutId: ReturnType<typeof setTimeout> | null = null;

	async function handleClick() {
		if (disabled || state === 'loading') return;

		state = 'loading';

		try {
			const result = await onClick?.();
			if (result === 'success' || result === undefined) {
				state = 'success';
				// Reset to idle after 2 seconds
				timeoutId = setTimeout(() => {
					state = 'idle';
				}, 2000);
			} else {
				state = 'idle';
			}
		} catch (error) {
			console.error('Button action failed:', error);
			state = 'idle';
		}
	}

	function getTooltip() {
		switch (state) {
			case 'success':
				return tooltip.success;
			default:
				return tooltip.neutral;
		}
	}

	// Cleanup timeout on destroy
	$effect(() => {
		return () => {
			if (timeoutId) {
				clearTimeout(timeoutId);
			}
		};
	});
</script>

<Tooltip text={getTooltip()} placement="top" delay={0}>
	<Button {variant} {size} class={className} onclick={handleClick} {disabled} title={getTooltip()}>
		<div class="stable-transition-container">
			{#if state === 'loading'}
				<div transition:scale class="stable-transition-item">
					<LoadingIndicator variant="wheel" size="sm" color="slate" />
				</div>
			{:else if state === 'success'}
				<div transition:scale class="stable-transition-item">
					<Icon src={Check} class="h-4 w-4 text-green-600" micro />
				</div>
			{:else}
				<div transition:scale class="stable-transition-item">
					<Icon src={IconComponent} class="h-4 w-4" micro />
				</div>
			{/if}
		</div>
		{@render children?.()}
	</Button>
</Tooltip>
