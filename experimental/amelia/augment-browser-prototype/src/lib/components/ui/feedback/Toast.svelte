<script lang="ts">
	import { Icon, XMark, CheckCircle, ExclamationTriangle, InformationCircle, XCircle } from 'svelte-hero-icons';
	import { removeToast } from '$lib/stores/toast';
	import type { Toast } from '$lib/stores/toast';
	import Button from '../navigation/Button.svelte';

	interface Props {
		toast: Toast;
	}

	let { toast }: Props = $props();

	const typeConfig = {
		success: {
			iconClass: 'text-green-500',
			icon: CheckCircle
		},
		error: {
			iconClass: 'text-red-500',
			icon: XCircle
		},
		warning: {
			iconClass: 'text-yellow-500',
			icon: ExclamationTriangle
		},
		info: {
			iconClass: 'text-blue-500',
			icon: InformationCircle
		}
	};

	const config = typeConfig[toast.type];

	function handleClose() {
		removeToast(toast.id);
	}

	function handleAction() {
		if (toast.action) {
			toast.action.handler();
			removeToast(toast.id);
		}
	}
</script>

<div class="flex items-start gap-3 p-4 bg-white border border-gray-200 rounded-lg shadow-lg text-gray-900 animate-in slide-in-from-right-full duration-300 z-[150]">
	<!-- Icon -->
	<div class="flex-shrink-0 mt-0.5">
		<div class="w-5 h-5 {config.iconClass}">
			<Icon src={config.icon} />
		</div>
	</div>

	<!-- Content -->
	<div class="flex-1 min-w-0">
		<p class="text-sm font-medium">{toast.message}</p>
	</div>

	<!-- Action button (if provided) -->
	{#if toast.action}
		<Button
			variant="outline"
			size="sm"
			onclick={handleAction}
			class="flex-shrink-0"
		>
			{toast.action.label}
		</Button>
	{/if}

	<!-- Close button -->
	<Button
		variant="ghost"
		size="icon-sm"
		icon={XMark}
		onclick={handleClose}
		class="flex-shrink-0 text-gray-400"
		aria-label="Close notification"
	/>
</div>
