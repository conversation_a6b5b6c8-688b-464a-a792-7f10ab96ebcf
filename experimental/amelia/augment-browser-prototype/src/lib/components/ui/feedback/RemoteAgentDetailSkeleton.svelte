<script lang="ts">
	// Skeleton loader for RemoteAgentDetailContent - matches the layout structure
	// Add some randomness to make it look more natural
	let titleWidth = Math.random() > 0.5 ? 'w-3/4' : 'w-2/3';
	let summaryHeight = Math.floor(Math.random() * (120 - 80) + 80);
	let codeBlockHeight = Math.floor(Math.random() * (200 - 150) + 150);
</script>

<!-- Agent Detail Content Skeleton -->
<div class="@container flex-1 px-3 lg:px-10 py-4 animate-pulse">
	<!-- Header Section -->
	<div class="relative flex flex-col sm:flex-row sm:items-center sm:justify-between pb-6 gap-4">
		<div class="flex min-w-0 flex-1 items-start gap-3">
			<!-- Agent Name and Status -->
			<div class="min-w-0 flex-1 gap-1">
				<!-- Entity Card Skeleton -->
				<div class="bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-4 mb-4">
					<div class="flex items-center gap-3">
						<!-- Provider icon -->
						<div class="relative w-5 h-5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
						</div>
						<!-- Title -->
						<div class="flex-1">
							<div class="relative {titleWidth} h-5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden mb-2">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
							</div>
							<div class="relative w-1/2 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.1s;"></div>
							</div>
						</div>
						<!-- External link icon -->
						<div class="relative w-4 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.2s;"></div>
						</div>
					</div>
				</div>

				<!-- Status and metadata -->
				<div class="mt-2 ml-2 flex flex-wrap items-center gap-x-2 gap-y-1">
					<!-- Status indicator -->
					<div class="flex items-center gap-2">
						<div class="relative w-3 h-3 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.3s;"></div>
						</div>
						<div class="relative w-20 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.4s;"></div>
						</div>
					</div>
					<!-- Metadata items -->
					<div class="relative w-24 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.5s;"></div>
					</div>
					<div class="relative w-20 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
						<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.6s;"></div>
					</div>
				</div>
			</div>
		</div>

		<!-- Action buttons -->
		<div class="flex items-center gap-2 sm:flex-shrink-0">
			<div class="relative w-8 h-8 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
				<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.7s;"></div>
			</div>
		</div>
	</div>

	<!-- Main Content -->
	<div class="w-full">
		<div class="mb-3">
			<div class="w-full">
				<!-- Summary Card -->
				<div class="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 px-3 py-3 mb-6">
					<div class="px-3 py-3">
						<!-- Summary content lines -->
						<div class="space-y-3" style="height: {summaryHeight}px;">
							<div class="relative w-full h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.8s;"></div>
							</div>
							<div class="relative w-5/6 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 0.9s;"></div>
							</div>
							<div class="relative w-3/4 h-4 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
								<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 1s;"></div>
							</div>
						</div>
					</div>
				</div>

				<!-- Code Changes Section -->
				<div class="bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 px-6 py-6">
					<!-- Section header -->
					<div class="flex items-center gap-2 mb-4">
						<div class="relative w-5 h-5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 1.1s;"></div>
						</div>
						<div class="relative w-32 h-5 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
							<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: 1.2s;"></div>
						</div>
					</div>

					<!-- Code block skeleton -->
					<div class="bg-slate-50 dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-700 p-4" style="height: {codeBlockHeight}px;">
						<div class="space-y-2">
							{#each Array(Math.floor(codeBlockHeight / 20)) as _, i}
								<div class="relative w-{Math.random() > 0.5 ? 'full' : '4/5'} h-3 bg-slate-200 dark:bg-slate-700 rounded overflow-hidden">
									<div class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent" style="animation-delay: {1.3 + (i * 0.1)}s;"></div>
								</div>
							{/each}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}
</style>
