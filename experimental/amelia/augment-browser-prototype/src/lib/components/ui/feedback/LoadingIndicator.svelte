<script lang="ts">
	interface Props {
		size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
		variant?: 'dots' | 'spinner' | 'pulse' | 'wave' | 'orbit' | 'typing' | 'asterisk' | 'wheel';
		color?: 'blue' | 'gray' | 'slate' | 'green' | 'orange' | 'red' | 'purple';
		text?: string;
		class?: string;
	}

	let {
		size = 'md',
		variant = 'dots',
		color = 'blue',
		text,
		class: className = ''
	}: Props = $props();

	// Size mappings
	const sizeClasses = {
		xs: { container: 'gap-0.5', dot: 'w-1 h-1', spinner: 'w-3 h-3', text: 'text-xs' },
		sm: { container: 'gap-1', dot: 'w-1.5 h-1.5', spinner: 'w-4 h-4', text: 'text-xs' },
		md: { container: 'gap-2', dot: 'w-2 h-2', spinner: 'w-6 h-6', text: 'text-sm' },
		lg: { container: 'gap-3', dot: 'w-3 h-3', spinner: 'w-8 h-8', text: 'text-base' },
		xl: { container: 'gap-4', dot: 'w-4 h-4', spinner: 'w-12 h-12', text: 'text-lg' }
	};

	// Color mappings
	const colorClasses = {
		blue: { primary: 'bg-blue-500', secondary: 'bg-blue-300', main: 'text-blue-500' },
		gray: { primary: 'bg-gray-500', secondary: 'bg-gray-300', main: 'text-gray-500' },
		slate: { primary: 'bg-slate-500', secondary: 'bg-slate-300', main: 'text-slate-500' },
		green: { primary: 'bg-green-500', secondary: 'bg-green-300', main: 'text-green-500' },
		orange: { primary: 'bg-orange-500', secondary: 'bg-orange-300', main: 'text-orange-500' },
		red: { primary: 'bg-red-500', secondary: 'bg-red-300', main: 'text-red-500' },
		purple: { primary: 'bg-purple-500', secondary: 'bg-purple-300', main: 'text-purple-500' }
	};

	const sizes = sizeClasses[size] || sizeClasses.md;
	const colors = colorClasses[color];
</script>

<div class="flex items-center {sizes.container} {className}">
	{#if variant === 'dots'}
		<!-- Bouncing Dots -->
		<div class="flex space-x-1">
			<div
				class="{sizes.dot} {colors.primary} animate-bounce rounded-full"
				style="animation-delay: 0ms; animation-duration: 1.4s;"
			></div>
			<div
				class="{sizes.dot} {colors.primary} animate-bounce rounded-full"
				style="animation-delay: 160ms; animation-duration: 1.4s;"
			></div>
			<div
				class="{sizes.dot} {colors.primary} animate-bounce rounded-full"
				style="animation-delay: 320ms; animation-duration: 1.4s;"
			></div>
		</div>
	{:else if variant === 'spinner'}
		<!-- Spinning Circle -->
		<div class="{sizes.spinner} animate-spin {colors.main} border-current border-t-transparent">
			<svg class="h-full w-full" viewBox="-2 -2 14 14" fill="none">
				<path
					d="M0 5A0 0 0 1 0 0 5 5 5 0 1 0 5 0"
					stroke="currentColor"
					stroke-width="2.5"
					stroke-linecap="round"
					vector-effect="non-scaling-stroke"
				></path>
			</svg>
		</div>
	{:else if variant === 'asterisk'}
		<!-- Spinning Asterisk -->
		<div class="{sizes.spinner} animate-spin [animation-duration:12s] {colors.main}">
			<svg class="h-full w-full" viewBox="0 0 24 24" fill="none">
				{#each Array(8) as _, i}
					<line
						x1="12"
						y1="12"
						x2={12 + 8 * Math.cos((i * Math.PI) / 4)}
						y2={12 + 8 * Math.sin((i * Math.PI) / 4)}
						stroke="currentColor"
						stroke-width="2"
						stroke-linecap="round"
					></line>
				{/each}
			</svg>
		</div>
	{:else if variant === 'pulse'}
		<!-- Pulsing Circles -->
		<div class="relative {sizes.spinner}">
			<div class="{sizes.spinner} {colors.secondary} absolute animate-ping rounded-full"></div>
			<div class="{sizes.spinner} {colors.primary} animate-pulse rounded-full"></div>
		</div>
	{:else if variant === 'wave'}
		<!-- Wave Animation -->
		<div class="flex space-x-0.5">
			{#each Array(5) as _, i}
				<div
					class="w-1 {colors.primary} animate-wave rounded-full"
					style="height: {size === 'sm'
						? '8px'
						: size === 'md'
							? '12px'
							: size === 'lg'
								? '16px'
								: '20px'}; animation-delay: {i * 100}ms;"
				></div>
			{/each}
		</div>
	{:else if variant === 'orbit'}
		<!-- Orbiting Dots -->
		<div class="relative {sizes.spinner}">
			<div class="absolute inset-0 animate-spin" style="animation-duration: 2s;">
				<div
					class="{sizes.dot} {colors.primary} absolute top-0 left-1/2 -translate-x-1/2 transform rounded-full"
				></div>
			</div>
			<div
				class="absolute inset-0 animate-spin"
				style="animation-duration: 2s; animation-delay: 0.5s;"
			>
				<div
					class="{sizes.dot} {colors.secondary} absolute top-0 left-1/2 -translate-x-1/2 transform rounded-full"
				></div>
			</div>
			<div
				class="{sizes.dot} {colors.primary} absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transform animate-pulse rounded-full"
			></div>
		</div>
	{:else if variant === 'typing'}
		<!-- Typing Indicator -->
		<div class="flex space-x-1">
			<div class="{sizes.dot} {colors.primary} animate-typing-1 rounded-full"></div>
			<div class="{sizes.dot} {colors.primary} animate-typing-2 rounded-full"></div>
			<div class="{sizes.dot} {colors.primary} animate-typing-3 rounded-full"></div>
		</div>
	{:else if variant === 'wheel'}
		<!-- Wheel Spinner -->
		<div class="wheel-spinner {sizes.spinner} {colors.main}" style="opacity: 0.65;">
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
			<div class="wheel-leaf"></div>
		</div>
	{/if}

	{#if text}
		<span class="{sizes.text} font-medium text-gray-600 dark:text-gray-400">
			{text}
		</span>
	{/if}
</div>

<style>
	/* Custom animations for smoother effects */
	@keyframes wave {
		0%,
		40%,
		100% {
			transform: scaleY(0.4);
		}
		20% {
			transform: scaleY(1);
		}
	}

	.animate-wave {
		animation: wave 1s ease-in-out infinite;
	}

	/* Enhanced bounce animation */
	@keyframes smooth-bounce {
		0%,
		80%,
		100% {
			transform: translateY(0);
		}
		40% {
			transform: translateY(-8px);
		}
	}

	.animate-smooth-bounce {
		animation: smooth-bounce 1.4s ease-in-out infinite;
	}

	/* Typing indicator animations */
	@keyframes typing {
		0%,
		60%,
		100% {
			transform: translateY(0);
			opacity: 0.4;
		}
		30% {
			transform: translateY(-6px);
			opacity: 1;
		}
	}

	.animate-typing-1 {
		animation: typing 1.4s ease-in-out infinite;
		animation-delay: 0ms;
	}

	.animate-typing-2 {
		animation: typing 1.4s ease-in-out infinite;
		animation-delay: 200ms;
	}

	.animate-typing-3 {
		animation: typing 1.4s ease-in-out infinite;
		animation-delay: 400ms;
	}

	/* Smooth orbit animation */
	@keyframes orbit {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	.animate-orbit {
		animation: orbit 2s linear infinite;
	}

	/* Wheel spinner animations */
	.wheel-spinner {
		display: inline-block;
		position: relative;
	}

	.wheel-leaf {
		position: absolute;
		top: 0;
		left: calc(50% - 6.25%);
		width: 12.5%;
		height: 100%;
		animation: wheel-leaf-fade 800ms linear infinite;
	}

	.wheel-leaf::before {
		content: '';
		display: block;
		width: 100%;
		height: 30%;
		border-radius: 2px;
		background-color: currentColor;
	}

	.wheel-leaf:nth-child(1) {
		transform: rotate(0deg);
		animation-delay: -800ms;
	}
	.wheel-leaf:nth-child(2) {
		transform: rotate(45deg);
		animation-delay: -700ms;
	}
	.wheel-leaf:nth-child(3) {
		transform: rotate(90deg);
		animation-delay: -600ms;
	}
	.wheel-leaf:nth-child(4) {
		transform: rotate(135deg);
		animation-delay: -500ms;
	}
	.wheel-leaf:nth-child(5) {
		transform: rotate(180deg);
		animation-delay: -400ms;
	}
	.wheel-leaf:nth-child(6) {
		transform: rotate(225deg);
		animation-delay: -300ms;
	}
	.wheel-leaf:nth-child(7) {
		transform: rotate(270deg);
		animation-delay: -200ms;
	}
	.wheel-leaf:nth-child(8) {
		transform: rotate(315deg);
		animation-delay: -100ms;
	}

	@keyframes wheel-leaf-fade {
		from {
			opacity: 1;
		}
		to {
			opacity: 0.25;
		}
	}
</style>
