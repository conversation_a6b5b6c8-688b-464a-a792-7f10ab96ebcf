<script lang="ts">
	import { slide } from 'svelte/transition';
	import { RemoteAgentStatus, RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';

	interface Props {
		status: RemoteAgentStatus;
		workspaceStatus?: RemoteAgentWorkspaceStatus;
		size?: 'sm' | 'md' | 'lg' | 'xl';
		hasUpdates?: boolean;
		isExpanded?: boolean;
		variant?: 'default' | 'sleek' | 'minimal';
		showAnimation?: boolean;
		class?: string;
	}

	let {
		status,
		workspaceStatus = RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_UNSPECIFIED,
		size = 'sm',
		hasUpdates = false,
		isExpanded = false,
		variant = 'default',
		showAnimation = true,
		class: className = ''
	}: Props = $props();

	// Determine if we should show the label
	let isHovered = $state(false);
	let doShowLabel = $derived(isExpanded || isHovered);

	// The text to display for the status
	let statusText = $derived(getRemoteAgentStatusText(status, workspaceStatus, hasUpdates));
	// The class to apply to the status dot and label
	let statusClass = $derived(statusText);

	function getRemoteAgentStatusText(
		status: RemoteAgentStatus,
		workspaceStatus: RemoteAgentWorkspaceStatus,
		hasUpdates: boolean
	): string {
		if (hasUpdates && status === RemoteAgentStatus.AGENT_IDLE) {
			return 'Unread';
		}
		switch (status) {
			case RemoteAgentStatus.AGENT_IDLE:
				if (workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING) {
					return 'Resuming';
				}
				return 'Idle';
			case RemoteAgentStatus.AGENT_RUNNING:
				return 'Running';
			case RemoteAgentStatus.AGENT_FAILED:
				return 'Failed';
			case RemoteAgentStatus.AGENT_STARTING:
				return 'Starting';
			case RemoteAgentStatus.AGENT_PENDING:
				return 'Pending';
			case RemoteAgentStatus.AGENT_PENDING_DELETION:
				return 'Deleting';
			case RemoteAgentStatus.AGENT_UNSPECIFIED:
				return 'Unknown';
			default:
				return 'Unknown';
		}
	}

	// Enhanced status info with icons and descriptions
	let statusInfo = $derived(
		(() => {
			switch (status) {
				case RemoteAgentStatus.AGENT_STARTING:
					return {
						label: 'Starting',
						description: 'Setting up environment',
						color: 'blue',
						icon: '⚡',
						pulse: true
					};
				case RemoteAgentStatus.AGENT_RUNNING:
					return {
						label: 'Working',
						description: 'Actively processing',
						color: 'blue',
						icon: '⚙️',
						pulse: true
					};
				case RemoteAgentStatus.AGENT_IDLE:
					return {
						label: 'Ready',
						description: 'Waiting for input',
						color: 'green',
						icon: '✓',
						pulse: false
					};
				case RemoteAgentStatus.AGENT_FAILED:
					return {
						label: 'Failed',
						description: 'Encountered an error',
						color: 'red',
						icon: '⚠️',
						pulse: false
					};
				case RemoteAgentStatus.AGENT_PENDING:
					return {
						label: 'Pending',
						description: 'Waiting to start',
						color: 'orange',
						icon: '⏳',
						pulse: false
					};
				case RemoteAgentStatus.AGENT_PENDING_DELETION:
					return {
						label: 'Deleting',
						description: 'Being deleted',
						color: 'red',
						icon: '🗑️',
						pulse: true
					};
				default:
					return {
						label: 'Unknown',
						description: 'Status unclear',
						color: 'gray',
						icon: '?',
						pulse: false
					};
			}
		})()
	);

	const sizeClasses = {
		sm: { dot: 'w-2 h-2', container: 'h-4', text: 'text-xs' },
		md: { dot: 'w-3 h-3', container: 'h-5', text: 'text-sm' },
		lg: { dot: 'w-4 h-4', container: 'h-6', text: 'text-base' },
		xl: { dot: 'w-6 h-6', container: 'h-8', text: 'text-lg' }
	};
	let sizeClass = $derived(sizeClasses[size]);
</script>

{#if variant === 'sleek'}
	<!-- Sleek variant with enhanced visuals -->
	<div
		class="sleek-status-container {sizeClass.container} {className}"
		onmouseenter={() => (isHovered = true)}
		onmouseleave={() => (isHovered = false)}
		role="status"
		aria-label="Agent status: {statusInfo.label} - {statusInfo.description}"
		title="{statusInfo.label}: {statusInfo.description}"
	>
		<div class="relative flex items-center gap-2">
			<!-- Status dot with enhanced styling -->
			<div class="relative">
				<div
					class="status-dot-sleek status-dot-sleek--{statusInfo.color} {sizeClass.dot} {statusInfo.pulse &&
					showAnimation
						? 'animate-pulse'
						: ''}"
				></div>
				{#if statusInfo.pulse && showAnimation}
					<div class="status-ring status-ring--{statusInfo.color} {sizeClass.dot}"></div>
				{/if}
			</div>

			{#if doShowLabel}
				<div
					class="status-label-sleek status-label-sleek--{statusInfo.color} {sizeClass.text}"
					transition:slide={{ duration: 150, axis: 'x' }}
				>
					<span class="status-icon">{statusInfo.icon}</span>
					<span class="status-text">{statusInfo.label}</span>
					{#if size === 'lg' || size === 'xl'}
						<span class="status-description">{statusInfo.description}</span>
					{/if}
				</div>
			{/if}
		</div>
	</div>
{:else if variant === 'minimal'}
	<!-- Minimal variant - just the dot -->
	<div
		class="minimal-status-container {className}"
		role="status"
		aria-label="Agent status: {statusInfo.label}"
		title="{statusInfo.label}: {statusInfo.description}"
	>
		<div
			class="status-dot-minimal status-dot-minimal--{statusInfo.color} {sizeClass.dot} {statusInfo.pulse &&
			showAnimation
				? 'animate-pulse'
				: ''}"
		></div>
	</div>
{:else}
	<!-- Default variant (existing design) -->
	<div
		class="status-indicator-container status-indicator--{size} {className}"
		onmouseenter={() => (isHovered = true)}
		onmouseleave={() => (isHovered = false)}
		role="status"
		aria-label="Agent status: {statusText}"
		title="Status: {statusText}"
	>
		<div class="status-dot status-dot--{statusClass} {sizeClass.dot}"></div>

		{#if doShowLabel}
			<div
				class="status-label status-label--{statusClass}"
				transition:slide={{ duration: 100, axis: 'x' }}
			>
				{statusText}
			</div>
		{/if}
	</div>
{/if}

<style>
	/* ===== SLEEK VARIANT STYLES ===== */
	.sleek-status-container {
		display: flex;
		align-items: center;
		position: relative;
	}

	.status-dot-sleek {
		border-radius: 50%;
		flex-shrink: 0;
		position: relative;
		z-index: 2;
		box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8);
		transition: all 0.2s ease;
	}

	.status-dot-sleek--blue {
		background: linear-gradient(135deg, #3b82f6, #1d4ed8);
	}

	.status-dot-sleek--green {
		background: linear-gradient(135deg, #10b981, #059669);
	}

	.status-dot-sleek--red {
		background: linear-gradient(135deg, #ef4444, #dc2626);
	}

	.status-dot-sleek--orange {
		background: linear-gradient(135deg, #f97316, #ea580c);
	}

	.status-dot-sleek--gray {
		background: linear-gradient(135deg, #9ca3af, #6b7280);
	}

	.status-ring {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 50%;
		opacity: 0.3;
		animation: pulse-ring 2s infinite;
	}

	.status-ring--blue {
		border: 2px solid #3b82f6;
	}

	.status-ring--green {
		border: 2px solid #10b981;
	}

	.status-ring--red {
		border: 2px solid #ef4444;
	}

	.status-ring--orange {
		border: 2px solid #f97316;
	}

	.status-ring--gray {
		border: 2px solid #9ca3af;
	}

	@keyframes pulse-ring {
		0% {
			transform: translate(-50%, -50%) scale(1);
			opacity: 0.3;
		}
		50% {
			transform: translate(-50%, -50%) scale(1.5);
			opacity: 0.1;
		}
		100% {
			transform: translate(-50%, -50%) scale(2);
			opacity: 0;
		}
	}

	.status-label-sleek {
		display: flex;
		align-items: center;
		gap: 0.375rem;
		padding: 0.25rem 0.75rem;
		border-radius: 0.5rem;
		font-weight: 500;
		white-space: nowrap;
		backdrop-filter: blur(8px);
		border: 1px solid rgba(255, 255, 255, 0.1);
		transition: all 0.2s ease;
	}

	.status-label-sleek--blue {
		background: rgba(59, 130, 246, 0.1);
		color: #1e40af;
		border-color: rgba(59, 130, 246, 0.2);
	}

	.status-label-sleek--green {
		background: rgba(16, 185, 129, 0.1);
		color: #065f46;
		border-color: rgba(16, 185, 129, 0.2);
	}

	.status-label-sleek--red {
		background: rgba(239, 68, 68, 0.1);
		color: #991b1b;
		border-color: rgba(239, 68, 68, 0.2);
	}

	.status-label-sleek--orange {
		background: rgba(249, 115, 22, 0.1);
		color: #9a3412;
		border-color: rgba(249, 115, 22, 0.2);
	}

	.status-label-sleek--gray {
		background: rgba(156, 163, 175, 0.1);
		color: #374151;
		border-color: rgba(156, 163, 175, 0.2);
	}

	.status-icon {
		font-size: 0.875em;
	}

	.status-description {
		font-size: 0.75em;
		opacity: 0.8;
		font-weight: 400;
	}

	/* ===== MINIMAL VARIANT STYLES ===== */
	.minimal-status-container {
		display: inline-flex;
		align-items: center;
	}

	.status-dot-minimal {
		border-radius: 50%;
		flex-shrink: 0;
		transition: all 0.2s ease;
	}

	.status-dot-minimal--blue {
		background-color: #3b82f6;
	}

	.status-dot-minimal--green {
		background-color: #10b981;
	}

	.status-dot-minimal--red {
		background-color: #ef4444;
	}

	.status-dot-minimal--orange {
		background-color: #f97316;
	}

	.status-dot-minimal--gray {
		background-color: #9ca3af;
	}

	/* ===== DEFAULT VARIANT STYLES (EXISTING) ===== */
	.status-indicator-container {
		position: relative;
		display: flex;
		align-items: center;
		height: 16px;
		min-width: 8px;
	}
	.status-indicator--sm {
		height: 14px;
	}
	.status-indicator--lg {
		height: 18px;
	}

	.status-dot {
		z-index: 10;
		width: 8px;
		height: 8px;
		border-radius: 50%;
		flex-shrink: 0;
	}

	/* Agent starting or running */
	.status-dot--Starting,
	.status-dot--Running,
	.status-dot--Resuming,
	.status-dot--RunningWithUpdates {
		background-color: #3b82f6; /* blue-500 */
	}

	/* Agent pending */
	.status-dot--Pending {
		background-color: #f97316; /* orange-500 */
	}

	/* Agent idle */
	.status-dot--Unread {
		background-color: #10b981; /* green-500 */
	}
	.status-dot--Idle {
		background-color: #6b7280;
	}

	/* Agent failed */
	.status-dot--Failed {
		background-color: #ef4444; /* red-500 */
	}

	/* Agent unread/has updates */
	.status-dot--Unread {
		background-color: #10b981; /* emerald-500 */
	}

	/* Agent unspecified/unknown */
	.status-dot--Unspecified,
	.status-dot--Unknown {
		background-color: #9ca3af; /* gray-400 */
	}

	.status-label {
		font-size: 11.5px;
		white-space: nowrap;
		padding: 0 8px 0 20px;
		border-radius: 10px;
		height: 20px;
		line-height: 19px;
		margin-left: -16px;
		/* text-transform: uppercase; */
		font-weight: 500;
		/* letter-spacing: 0.06em; */
		z-index: 1;
	}

	.status-indicator--sm .status-label {
		padding: 0 6px 0 16px;
		border-radius: 10px;
		font-size: 10px;
		height: 16px;
		line-height: 16px;
		margin-left: -13px;
	}

	.status-indicator--lg .status-label {
		font-size: 13px;
		height: 24px;
		line-height: 24px;
		padding: 0 10px 0 20px;
		margin-left: -16px;
	}

	.status-label--Starting,
	.status-label--Running,
	.status-label--Resuming,
	.status-label--RunningWithUpdates {
		background-color: #dbeafe; /* blue-100 */
		color: #1e40af; /* blue-800 */
	}

	.status-label--Pending {
		background-color: #fed7aa; /* orange-100 */
		color: #9a3412; /* orange-800 */
	}

	.status-label--Unread {
		background-color: #dcfce7; /* green-100 */
		color: #166534; /* green-800 */
	}

	.status-label--Idle {
		background-color: #f3f4f6; /* gray-100 */
		color: #1f2937; /* gray-800 */
	}

	.status-label--Failed {
		background-color: #fee2e2; /* red-100 */
		color: #991b1b; /* red-800 */
	}

	.status-label--Unread {
		background-color: #d1fae5; /* emerald-100 */
		color: #065f46; /* emerald-800 */
	}

	.status-label--Unspecified,
	.status-label--Unknown {
		background-color: #f3f4f6; /* gray-100 */
		color: #1f2937; /* gray-800 */
	}

	/* ===== DARK MODE STYLES ===== */

	/* Sleek variant dark mode */
	:global(.dark) .status-dot-sleek {
		box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.3);
	}

	:global(.dark) .status-label-sleek--blue {
		background: rgba(59, 130, 246, 0.15);
		color: #93c5fd;
		border-color: rgba(59, 130, 246, 0.3);
	}

	:global(.dark) .status-label-sleek--green {
		background: rgba(16, 185, 129, 0.15);
		color: #6ee7b7;
		border-color: rgba(16, 185, 129, 0.3);
	}

	:global(.dark) .status-label-sleek--red {
		background: rgba(239, 68, 68, 0.15);
		color: #fca5a5;
		border-color: rgba(239, 68, 68, 0.3);
	}

	:global(.dark) .status-label-sleek--orange {
		background: rgba(249, 115, 22, 0.15);
		color: #fdba74;
		border-color: rgba(249, 115, 22, 0.3);
	}

	:global(.dark) .status-label-sleek--gray {
		background: rgba(156, 163, 175, 0.15);
		color: #d1d5db;
		border-color: rgba(156, 163, 175, 0.3);
	}

	/* Default variant dark mode (existing) */
	:global(.dark) .status-label--Starting,
	:global(.dark) .status-label--Running,
	:global(.dark) .status-label--Resuming,
	:global(.dark) .status-label--RunningWithUpdates {
		background-color: rgba(30, 58, 138, 0.3); /* blue-900/30 */
		color: #93c5fd; /* blue-300 */
	}

	:global(.dark) .status-label--Pending {
		background-color: rgba(154, 52, 18, 0.3); /* orange-900/30 */
		color: #fdba74; /* orange-300 */
	}

	:global(.dark) .status-label--Unread {
		background-color: rgba(20, 83, 45, 0.3); /* green-900/30 */
		color: #86efac; /* green-300 */
	}
	:global(.dark) .status-label--Idle {
		background-color: rgba(31, 41, 51, 0.3); /* gray-900/30 */
		color: #d1d5db; /* gray-300 */
	}

	:global(.dark) .status-label--Failed {
		background-color: rgba(127, 29, 29, 0.3); /* red-900/30 */
		color: #fca5a5; /* red-300 */
	}

	:global(.dark) .status-label--Unspecified,
	:global(.dark) .status-label--Unknown {
		background-color: #374151; /* gray-800 */
		color: #d1d5db; /* gray-300 */
	}
</style>
