<script lang="ts">
	import {
		HandThumbUp,
		HandThumbDown,
		ClipboardDocumentList,
		EllipsisHorizontal,
		Icon
	} from 'svelte-hero-icons';
	import { FeedbackRating, type FeedbackState, type TooltipItem } from '$lib/types/feedback';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import CopyButton from './CopyButton.svelte';
	import SuccessfulDropdownItem from './SuccessfulDropdownItem.svelte';
	import DropdownMenu from '$lib/components/ui/DropdownMenu.svelte';
	import { useFeedback } from '$lib/hooks/useFeedback';
	import { slide } from 'svelte/transition';
	import Tooltip from '../overlays/Tooltip.svelte';
	import Textarea from '../forms/Textarea.svelte';

	interface Props {
		requestId?: string;
		feedbackState?: FeedbackState;
		responseText?: string;
		tooltipItems?: TooltipItem[];
	}

	let { requestId, feedbackState, responseText, tooltipItems = [] }: Props = $props();

	const { submitFeedback: submitFeedbackToAPI } = useFeedback();

	let isSubmittingFeedback = $state(false);
	let feedbackNote = $state('');
	let showFeedbackForm = $state(false);
	let selectedRating = $state<FeedbackRating>(
		feedbackState?.selectedRating || FeedbackRating.unset
	);
	let showDropdown = $state(false);

	async function handleFeedbackClick(rating: FeedbackRating) {
		if (rating === selectedRating) {
			showFeedbackForm = !showFeedbackForm;
			selectedRating = FeedbackRating.unset;
			return;
		}
		selectedRating = rating;
		showFeedbackForm = true;
	}

	async function submitFeedback() {
		isSubmittingFeedback = true;
		try {
			await submitFeedbackToAPI(requestId, selectedRating, feedbackNote);
			showFeedbackForm = false;
			feedbackNote = '';
			// Show success message (could be enhanced with a toast system)
			console.log('Thank you for your feedback!');
		} catch (error) {
			console.error('Failed to send feedback:', error);
		} finally {
			isSubmittingFeedback = false;
		}
	}

	function cancelFeedback() {
		showFeedbackForm = false;
		feedbackNote = '';
		selectedRating = FeedbackRating.unset;
	}

	async function copyRequestId() {
		await navigator.clipboard.writeText(requestId || 'unknown');
	}
</script>

<div class="feedback-panel mt-3">
	<div class="flex items-center gap-2">
		<!-- Feedback buttons -->
		<Tooltip text="Good response" position="top" delay={0}>
			<Button
				variant="ghost-light"
				size="icon-sm"
				onclick={() => handleFeedbackClick(FeedbackRating.positive)}
				title="Leave positive feedback"
				class={selectedRating === FeedbackRating.positive
					? 'bg-green-100 hover:!bg-green-200 dark:bg-green-900/20 dark:hover:!bg-green-900/30'
					: ''}
			>
				<Icon
					class={selectedRating === FeedbackRating.positive
						? 'text-green-700 dark:text-green-400'
						: ''}
					src={HandThumbUp}
					micro
				/>
			</Button>
		</Tooltip>

		<Tooltip text="Poor response" position="top" delay={0}>
			<Button
				variant="ghost-light"
				size="icon-sm"
				onclick={() => handleFeedbackClick(FeedbackRating.negative)}
				title="Poor response"
				class={selectedRating === FeedbackRating.negative
					? 'bg-red-100 hover:!bg-red-200 dark:bg-red-900/20 dark:hover:!bg-red-900/30'
					: ''}
			>
				<Icon
					class={selectedRating === FeedbackRating.negative ? 'text-red-700 dark:text-red-400' : ''}
					src={HandThumbDown}
					micro
				/>
			</Button>
		</Tooltip>

		<!-- Copy response button -->
		{#if responseText}
			<CopyButton variant="ghost-light" text={responseText} tooltip="Copy response" />
		{/if}

		{#if requestId}
			<!-- Kebab menu (three dots) -->
			<DropdownMenu
				buttonIcon={EllipsisHorizontal}
				buttonSize="icon-sm"
				buttonVariant="ghost-light"
				buttonAriaLabel="More options"
				bind:open={showDropdown}
			>
				{#snippet children()}
					<!-- Copy request ID option -->
					<SuccessfulDropdownItem
						icon={ClipboardDocumentList}
						idleLabel="Copy request ID"
						successLabel="Copied request ID"
						onSelect={copyRequestId}
					/>

					<!-- Additional tooltip items -->
					{#each tooltipItems as item}
						<SuccessfulDropdownItem
							icon={item.icon}
							idleLabel={item.label}
							successLabel={item.successMessage || 'Done!'}
							onSelect={item.action}
							disabled={item.disabled}
						/>
					{/each}
				{/snippet}
			</DropdownMenu>
		{/if}
	</div>

	<!-- Feedback form -->
	{#if showFeedbackForm}
		<div
			class="mt-3 rounded-lg border border-slate-200 bg-slate-50 p-3 dark:border-slate-700 dark:bg-slate-800/50"
			transition:slide={{ axis: 'y', duration: 200 }}
		>
			<Textarea
				bind:value={feedbackNote}
				placeholder="Enter your feedback..."
				class="w-full"
				rows="3"
				autoFocus
			/>

			<div class="mt-2 flex justify-end gap-2">
				<Button
					variant="ghost-light"
					size="sm"
					onclick={cancelFeedback}
					disabled={isSubmittingFeedback}
				>
					Cancel
				</Button>
				<Button
					variant="primary"
					size="sm"
					onclick={submitFeedback}
					disabled={isSubmittingFeedback}
				>
					{isSubmittingFeedback ? 'Sending...' : 'Share Feedback'}
				</Button>
			</div>
		</div>
	{/if}
</div>
