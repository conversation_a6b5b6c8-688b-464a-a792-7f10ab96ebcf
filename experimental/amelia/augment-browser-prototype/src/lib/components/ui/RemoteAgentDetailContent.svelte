<script lang="ts">
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import { apiClient } from '$lib/api/unified-client';

	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import CodeStoryWidget from '$lib/components/ui/widgets/CodeStoryWidget.svelte';
	import { MagnifyingGlass, Trash, CloudArrowUp, ArrowPath } from 'svelte-hero-icons';
	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';

	// Import utility functions for extracting summaries and changed files
	import {
		getExchangesSummary,
		getExchangesGeneralSummary
	} from '$lib/utils/exchange-summary-utils';
	import { getAgentChangedFilesWithDetails } from '$lib/utils/agent-data';
	import { aggregateChangedFiles } from '$lib/utils/file-aggregation';
	import { convertChangedFileFromAPI } from '$lib/api/unified-client';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import { GitPullRequest } from '$lib/icons/GitPullRequest.svelte';
	import {
		getChatExchanges,
		getEntityByAgentWithRelated,
		ensureEntityWithRelatedLoadedForAgent,
		getRawEntityByAgent,
		ensureRawEntityLoadedForAgent,
		getDetectedPREntityWithRelated,
		getDetectedPRRawEntity,
		ensureDetectedPREntityWithRelatedLoaded,
		ensureDetectedPRRawEntityLoaded
	} from '$lib/stores/global-state.svelte';
	import { addToast } from '$lib/stores/toast';
	import SummarySkeleton from '$lib/components/ui/data-display/SummarySkeleton.svelte';
	import Tooltip from '$lib/components/ui/overlays/Tooltip.svelte';
	import { sendPullRequestMessage } from '$lib/utils/agent-actions';
	import ChatHistoryView from '$lib/components/chat-history/ChatHistoryView.svelte';
	import AgentChatResponse from '$lib/components/agents/AgentChatResponse.svelte';
	import { getChatSession } from '$lib/stores/global-state.svelte';
	import AgentDeletionModal from '$lib/components/ui/overlays/AgentDeletionModal.svelte';
	import { onMount, onDestroy } from 'svelte';
	import {
		subscribeToAgentStreaming,
		unsubscribeFromAgentStreaming
	} from '$lib/stores/data-operations.svelte';
	import {
		extractBranchFromExchanges,
		extractPullRequestCreationInfo,
		// hasFileChangesAfterLatestPush,
		findLatestPushOrPRCreation,
		type PullRequestInfo
	} from '$lib/utils/remote-agent-utils';
	import { detectPRInfo } from '$lib/utils/pr-detection';
	import EntityCard from '$lib/components/ui/data-display/EntityCard.svelte';
	import SetupScriptWidget from './widgets/SetupScriptWidget.svelte';
	import RelatedEntitiesDisplay from '$lib/components/ui/data-display/RelatedEntitiesDisplay.svelte';
	import { GitBranch } from '$lib/icons/GitBranchIcon.svelte';
	import CodeChangesNavigation from '$lib/components/ui/navigation/CodeChangesNavigation.svelte';
	import Modal from '$lib/components/ui/overlays/Modal.svelte';
	import { DocumentText } from 'svelte-hero-icons';
	import { getFilteredChangedFiles } from '$lib/utils/agent-data';
	import Pill from './navigation/Pill.svelte';

	interface Props {
		agent: CleanRemoteAgent;
		onAgentDeleted?: () => void;
		currentViewIndex?: number;
	}

	let { agent, onAgentDeleted, currentViewIndex: propCurrentViewIndex }: Props = $props();

	// Component ID for streaming subscription
	const componentId = `remote-agent-detail-${crypto.randomUUID()}`;

	// Subscribe to streaming when component mounts
	onMount(() => {
		if (agent?.id) {
			console.log(`Subscribing to streaming for agent ${agent.id} from component ${componentId}`);
			subscribeToAgentStreaming(agent.id, componentId);
		}
	});

	// Unsubscribe when component unmounts
	onDestroy(() => {
		if (agent?.id) {
			console.log(
				`Unsubscribing from streaming for agent ${agent.id} from component ${componentId}`
			);
			unsubscribeFromAgentStreaming(agent.id, componentId);
		}
	});

	// Also handle agent changes
	$effect(() => {
		if (agent?.id) {
			const agentId = agent.id; // Capture the ID to avoid null reference in cleanup
			console.log(`Agent changed, subscribing to streaming for agent ${agentId}`);
			subscribeToAgentStreaming(agentId, componentId);

			return () => {
				console.log(`Cleaning up streaming subscription for agent ${agentId}`);
				unsubscribeFromAgentStreaming(agentId, componentId);
			};
		}
	});

	// State for expanded sections
	let showChatHistory = $state(false);
	let showChanges = $state(false);
	let isCreatingPR = $state(false);
	let isDeletingAgent = $state(false);
	let isPushingChanges = $state(false);
	let showFullSummary = $state(false);
	let showDeletionModal = $state(false);
	let currentViewIndex = $state(-1); // -1 = all changes, -2 = since last push, >= 0 = specific exchange

	// Get PR creation info for "created in message" link
	let prCreationInfo = $derived(findPRCreationExchange());

	// Ensure detected PR entities are loaded when needed
	$effect(() => {
		if (prDetectionInfo && !linkedEntityWithRelated) {
			ensureDetectedPREntityWithRelatedLoaded(prDetectionInfo);
			ensureDetectedPRRawEntityLoaded(prDetectionInfo);
		}
	});

	// Get chat session for optimistic messages and streaming
	let chatSessionStore = $derived(agent?.id ? getChatSession(agent.id) : null);
	let chatSession = $derived($chatSessionStore ? $chatSessionStore.data : null);
	let optimisticMessage = $derived(chatSession?.optimisticMessage);
	let isStreaming = $derived(chatSession?.isStreaming || false);
	let streamingContent = $derived(chatSession?.streamingContent || '');
	let streamingMessages = $derived(chatSession?.streamingMessages || new Map());

	// Get raw entity data using global store pattern
	let rawEntityStore = $derived(agent?.id ? getRawEntityByAgent(agent.id) : null);
	let rawEntityData = $derived($rawEntityStore?.data || null);

	// Update currentViewIndex when prop changes
	$effect(() => {
		if (propCurrentViewIndex !== undefined) {
			currentViewIndex = propCurrentViewIndex;
		}
	});
	let showChangesSinceLastPushModal = $state(false);

	// Get changed files based on current view
	let filteredChangedFilesStore = $derived(
		agent?.id ? getFilteredChangedFiles(agent.id, currentViewIndex) : null
	);
	let changedFiles = $derived.by(() => {
		if (!filteredChangedFilesStore) return [];
		return $filteredChangedFilesStore || [];
	});

	// Get all changed files for modal
	let allChangedFilesStore = $derived(agent?.id ? getAgentChangedFilesWithDetails(agent.id) : null);
	let allChangedFiles = $derived.by(() => {
		if (!allChangedFilesStore) return [];
		return $allChangedFilesStore || [];
	});

	// Get chat exchanges for this agent
	let chatExchangesStore = $derived(agent?.id ? getChatExchanges(agent.id) : null);
	let exchanges = $derived($chatExchangesStore || []);
	let sessionSummary = $derived(getExchangesSummary(exchanges));
	let generalSummary = $derived(getExchangesGeneralSummary(exchanges));

	// Get linked entity with related entities for the agent detail page
	let linkedEntityWithRelatedStore = $derived(
		agent?.id ? getEntityByAgentWithRelated(agent.id) : null
	);
	let linkedEntityWithRelated = $derived($linkedEntityWithRelatedStore?.data || null);

	// Check if we have a PR entity specifically (for PR-specific functionality)
	let hasPREntity = $derived.by(() => {
		// Check if linked entity is a PR
		if (
			linkedEntityWithRelated?.entityType === 'pull_request' &&
			linkedEntityWithRelated?.providerId === 'github'
		) {
			return true;
		}

		// Check if we detected a PR from chat history
		const prInfo = detectPRInfo(agent, exchanges, linkedEntityWithRelated || null, rawEntityData);
		return !!prInfo;
	});

	// Get PR number and URL from detection if not available in entity
	let prDetectionInfo = $derived.by(() => {
		if (linkedEntityWithRelated?.entityType === 'pull_request') {
			return null; // Use entity data directly
		}
		return detectPRInfo(agent, exchanges, linkedEntityWithRelated || null, rawEntityData);
	});

	// Get detected PR entity and raw data using caching pattern
	let detectedPREntityStore = $derived(
		prDetectionInfo ? getDetectedPREntityWithRelated(prDetectionInfo) : null
	);
	let detectedPREntity = $derived($detectedPREntityStore?.data || null);

	let pullRequestInfo = $derived(
		(linkedEntityWithRelated?.entityType === 'pull_request' && linkedEntityWithRelated) ||
			detectedPREntity
	);
	$inspect({ pullRequestInfo });
	let branch = $derived(extractBranchFromExchanges(exchanges));

	// Check if there are file changes after the latest push (for modal display)
	// let hasUnpushedChanges = $derived(hasFileChangesAfterLatestPush(exchanges));

	// Get changes since last push for modal using proper aggregation
	let changesSinceLastPush = $derived.by(() => {
		console.log('Exchanges:', exchanges);
		if (!exchanges || exchanges.length === 0) return [];

		const latestPushIndex = findLatestPushOrPRCreation(exchanges);

		// Get exchanges after the last push
		const exchangesAfterPush = exchanges.slice(latestPushIndex + 1);

		// Extract changed files from each exchange after the push
		const exchangeChanges: any[][] = [];
		for (const exchange of exchangesAfterPush) {
			if (exchange.changedFiles && Array.isArray(exchange.changedFiles)) {
				// Convert API format to CleanChangedFile format
				const convertedFiles = exchange.changedFiles.map(convertChangedFileFromAPI);
				exchangeChanges.push(convertedFiles);
			}
		}

		// Use the aggregation function to properly merge changes across exchanges
		return aggregateChangedFiles(exchangeChanges);
	});

	// Find which exchange created a PR (for "created in message" link)
	function findPRCreationExchange(): { exchangeIndex: number; groupNumber: number } | null {
		if (!exchanges || exchanges.length === 0) return null;

		// Search exchanges from latest first to find the most recent PR creation
		for (let i = exchanges.length - 1; i >= 0; i--) {
			const exchange = exchanges[i];
			if (!exchange?.exchange?.responseNodes) continue;

			// Look for GitHub API tool use nodes that create pull requests
			for (const responseNode of exchange.exchange.responseNodes) {
				if (responseNode.type !== 5 || !responseNode.toolUse) continue;

				const toolUse = responseNode.toolUse;

				// Check if this is a GitHub API call
				if (toolUse.toolName !== 'github-api') continue;

				let inputData;
				try {
					inputData = JSON.parse(toolUse.inputJson);
				} catch {
					continue;
				}

				// Verify this is a pull request creation
				if (
					inputData.method === 'POST' &&
					inputData.path?.endsWith('/pulls') &&
					inputData.data?.title
				) {
					// Calculate group number for this exchange (simplified version)
					const groupNumber = i + 1; // Simple 1-based indexing
					return { exchangeIndex: i, groupNumber };
				}
			}
		}

		return null;
	}

	// Trigger loading when agent changes
	$effect(() => {
		if (agent?.id) {
			ensureEntityWithRelatedLoadedForAgent(agent.id);
			ensureRawEntityLoadedForAgent(agent.id);
		}
	});

	let githubInfo = $derived(
		agent.githubUrl ? { url: agent.githubUrl, ref: agent.githubRef } : null
	);

	// Extract repo from GitHub URL
	let repo = $derived(() => {
		if (!agent.githubUrl) return '';
		const match = agent.githubUrl.match(/github\.com\/([^/]+\/[^/]+)/);
		return match ? match[1] : '';
	});

	type Step = {
		label: string;
		description: string;
		action: string;
		variant: 'secondary' | 'secondary';
		icon: any;
		disabled?: boolean;
		tooltipText?: string;
	};

	const steps: Record<string, Step> = {
		createPR: {
			label: 'Create PR',
			description: 'Create a pull request with the changes',
			action: 'pr',
			variant: 'secondary',
			icon: GitPullRequest
		},
		viewPR: {
			label: 'View PR',
			description: 'View the pull request',
			action: 'view-pr',
			variant: 'secondary',
			icon: GitPullRequest
		},
		updatePR: {
			label: 'Update PR with changes',
			description: 'Push recent changes to the pull request',
			action: 'update-pr',
			variant: 'secondary',
			icon: CloudArrowUp
		},
		openBranch: {
			label: 'Open Branch',
			description: 'Open the branch in GitHub',
			action: 'open-branch',
			variant: 'secondary',
			icon: GitBranch
		},
		checkout: {
			label: 'Check out in VSCode',
			description: 'Open the code changes in VSCode',
			action: 'checkout',
			variant: 'secondary',
			icon: MagnifyingGlass,
			tooltipText: 'VSCode checkout is not yet implemented. Click to open in vscode.dev.'
		},
		delete: {
			label: 'Delete Agent',
			description: 'Clean up the agent and free up resources',
			action: 'delete',
			variant: 'secondary',
			icon: Trash
		}
	};
	const hasChanges = $derived(!!changedFiles && changedFiles.length > 0);
	const getSteps = function (hasChanges: boolean, prInfo: PullRequestInfo | null): Step[] {
		return [
			...(hasChanges && prInfo ? [steps.viewPR] : []),
			...(hasChanges && !prInfo ? [steps.createPR] : []),
			// ...(!hasChanges ? [] : [steps.openBranch]),
			steps.checkout,
			steps.delete
		];
	};

	// Determine next steps
	let nextSteps = $derived(getSteps(hasChanges, pullRequestInfo));

	function handleAction(action: string) {
		// Check if the action corresponds to a disabled step
		const step = nextSteps.find((s) => s.action === action);
		if (step?.disabled) {
			return; // Don't execute disabled actions
		}

		switch (action) {
			case 'chat':
				showChatHistory = !showChatHistory;
				break;
			case 'changes':
				showChanges = !showChanges;
				break;
			case 'delete':
				deleteAgentLocal();
				break;
			case 'pr':
				createPullRequest();
				break;
			case 'view-pr':
				viewPR();
				break;
			case 'open-branch':
				if (branch) {
					window.open(`https://github.com/${repo}/tree/${branch}`, '_blank');
				}
				break;
			case 'checkout':
				openInVSCode();
				break;
		}
	}

	function openInVSCode() {
		addToast({
			type: 'info',
			message: `Opening in the browser because we don't have this implemented in the extension yet.`,
			duration: 5000
		});

		// Create VS Code URL to clone and checkout the specific branch
		// const vscodeUrl = `vscode://vscode.dev/github.com/${githubInfo.repositoryUrl}/tree/${githubInfo.gitRef}`;
		const vscodeUrl = `https://vscode.dev/github.com/${repo}/tree/${branch || githubInfo?.ref}`;

		// Open in VS Code
		window.open(vscodeUrl, '_blank');
	}

	// Navigation functions
	function handleNavigateView(viewIndex: number) {
		currentViewIndex = viewIndex;
	}

	function handleScrollToExchange(exchangeIndex: number) {
		// Find the exchange element and scroll to it
		const exchangeElement = document.querySelector(`[data-exchange-index="${exchangeIndex}"]`);
		if (exchangeElement) {
			exchangeElement.scrollIntoView({
				behavior: 'smooth',
				block: 'center'
			});
		}
	}

	function handleViewChangesSinceLastPush() {
		showChangesSinceLastPushModal = true;
	}

	async function createPullRequest() {
		if (!agent?.id) {
			console.error('No current agent ID found');
			return;
		}

		isCreatingPR = true;

		try {
			// Use the new utility function that follows the same pattern as handleSendMessage
			await sendPullRequestMessage(agent.id);
		} catch (error) {
			// Error handling is already done in the utility function
			console.error('Error sending PR creation request:', error);
		} finally {
			isCreatingPR = false;
		}
	}

	async function viewPR() {
		if (!pullRequestInfo?.url) return;
		window.open(pullRequestInfo.url, '_blank');
	}

	/**
	 * Push changes to the linked PR
	 */
	async function pushChangesToPR() {
		if (isPushingChanges || !agent?.id) return;

		isPushingChanges = true;

		try {
			// Get PR number for display
			const prNumber = pullRequestInfo?.prNumber?.toString() || 'the linked PR';
			const prRef = prNumber !== 'the linked PR' ? `#${prNumber}` : prNumber;
			const filesList = changesSinceLastPush
				.map((file) => file.path || file.oldPath || '')
				.filter(Boolean);
			const filesInfo =
				filesList.length > 0
					? ` The following files have been modified: ${filesList.slice(0, 5).join(', ')}${filesList.length > 5 ? ` and ${filesList.length - 5} more` : ''}.`
					: '';

			const message = `Please commit and push the recent file changes to the current branch for PR ${prRef}.${filesInfo} Use git commands via the launch-process tool to stage, commit, and push the changes.`;

			await apiClient.agents.chat({
				remoteAgentId: agent.id,
				message
			});

			console.log('Push request sent to agent');
		} catch (error) {
			console.error('Failed to send push request:', error);
			addToast({
				type: 'error',
				message: error instanceof Error ? error.message : 'Failed to push changes',
				duration: 5000
			});
		} finally {
			isPushingChanges = false;
		}
	}

	function deleteAgentLocal() {
		if (!agent || isDeletingAgent) return;
		showDeletionModal = true;
	}
</script>

<!-- Content -->
<div class="@container flex-1 pb-20">
	{#if agent.status === RemoteAgentStatus.AGENT_STARTING}
		<div class="flex items-center justify-center gap-2 py-8 text-slate-500">
			<LoadingIndicator variant="asterisk" color="blue" />
			Spinning up environment...
		</div>
	{/if}

	<!-- <RemoteAgentHeader {agent} {onAgentDeleted} /> -->
	<div class="mb-3 w-full px-3 py-2 md:px-6 xl:px-10">
		<div class="mb-3 flex flex-wrap gap-2">
			{#each nextSteps as step}
				<div class="flex flex-col">
					{#if step.tooltipText}
						<Tooltip text={step.tooltipText} delay={0}>
							<Button
								variant={step.variant}
								size="md"
								onclick={() => handleAction(step.action)}
								class="h-auto cursor-not-allowed justify-start px-4 py-3 text-left"
								loading={(step.action === 'pr' && isCreatingPR) ||
									(step.action === 'delete' && isDeletingAgent) ||
									(step.action === 'update-pr' && isPushingChanges)}
								disabled={step.disabled ||
									(step.action === 'pr' && isCreatingPR) ||
									(step.action === 'delete' && isDeletingAgent) ||
									(step.action === 'update-pr' && isPushingChanges)}
								icon={step.icon}
							>
								{step.label}
							</Button>
						</Tooltip>
					{:else}
						<Button
							variant={step.variant}
							size="md"
							onclick={() => handleAction(step.action)}
							class="h-auto justify-start px-4 py-3 text-left"
							loading={(step.action === 'pr' && isCreatingPR) ||
								(step.action === 'delete' && isDeletingAgent) ||
								(step.action === 'update-pr' && isPushingChanges)}
							disabled={step.disabled ||
								(step.action === 'pr' && isCreatingPR) ||
								(step.action === 'delete' && isDeletingAgent) ||
								(step.action === 'update-pr' && isPushingChanges)}
							icon={step.icon}
						>
							{step.label}
							{#if step.label === 'View PR' && (detectedPREntity || (linkedEntityWithRelated && linkedEntityWithRelated.entityType === 'pull_request'))}
								<Pill
									variant={(detectedPREntity || linkedEntityWithRelated)?.state === 'open'
										? 'green'
										: 'red'}
									class="-my-1 ml-1"
									size="xs"
								>
									{(detectedPREntity || linkedEntityWithRelated)?.state}
								</Pill>
							{/if}
						</Button>
					{/if}
					<!-- <div class="text-xs opacity-70 font-normal mt-0.5">{step.description}</div> -->
				</div>
			{/each}
		</div>

		{#if generalSummary || sessionSummary}
			<!-- {#if linkedEntity}
						<EntityCard entity={linkedEntity} />
						{/if} -->

			<div class="py-3">
				<div class={showFullSummary ? 'w-full' : 'line-clamp-6 w-full'}>
					<Markdown class="w-full" content={generalSummary || sessionSummary} />
				</div>
				{#if (generalSummary || sessionSummary).length > 500}
					<Button
						variant="ghost"
						size="sm"
						class="mt-3"
						onclick={() => (showFullSummary = !showFullSummary)}
					>
						Show {showFullSummary ? 'less' : 'more'}
					</Button>
				{/if}
			</div>
		{:else if !chatExchangesStore || (exchanges.length === 0 && agent.status !== RemoteAgentStatus.AGENT_STARTING)}
			<!-- Show skeleton while loading exchanges or when no exchanges yet -->
			<div class="py-3">
				<SummarySkeleton />
			</div>
		{/if}
	</div>

	<div class="px-3 md:px-6 xl:px-10">
		<!-- Setup Script Widget for setup script agents -->
		{#if agent.isSetupScriptAgent}
			<div class="mb-3">
				<SetupScriptWidget agentId={agent.id} />
			</div>
		{/if}

		<!-- Entity Card -->
		<!-- {#if hasEntityToDisplay && (linkedEntityWithRelated || detectedPREntity)}
			<div class="mb-8">
				<EntityCard entity={linkedEntityWithRelated || detectedPREntity!}></EntityCard>
			</div>
		{/if} -->

		<!-- Code Story -->
		{#if changedFiles && changedFiles.length > 0}
			<div class="mb-6">
				<!-- File Changes Header with Navigation -->
				<div class="mb-2">
					<CodeChangesNavigation
						{exchanges}
						{currentViewIndex}
						onNavigate={handleNavigateView}
						onScrollToExchange={handleScrollToExchange}
					/>
				</div>

				{#key currentViewIndex}
					<CodeStoryWidget {changedFiles} />
				{/key}
			</div>
		{/if}

		<!-- Related Entities -->
		{#if detectedPREntity}
			<EntityCard entity={detectedPREntity}>
				{#snippet headerActions()}
					<div class="flex flex-1 items-center">
						<!-- Created in message link for PRs -->
						{#if hasPREntity && prCreationInfo}
							<div
								class="mt-[1px] ml-2 flex items-center gap-1 text-sm font-normal whitespace-nowrap"
							>
								<span>• &nbsp;Created in</span>
								<button
									onclick={() => handleScrollToExchange(prCreationInfo!.exchangeIndex)}
									class="cursor-pointer hover:text-slate-700 hover:underline dark:hover:text-slate-300"
								>
									message {prCreationInfo.groupNumber}
								</button>
							</div>
						{/if}

						<!-- File changes since last push for PRs -->
						{#if hasPREntity && changesSinceLastPush.length > 0}
							<Button
								variant="secondary"
								size="sm"
								onclick={handleViewChangesSinceLastPush}
								icon={DocumentText}
								class="mr-3 ml-auto"
								loading={isPushingChanges}
								disabled={isPushingChanges}
							>
								{#if isPushingChanges}
									Syncing...
								{:else}
									{changesSinceLastPush.length} change{changesSinceLastPush.length === 1 ? '' : 's'}
									behind
								{/if}
							</Button>
						{/if}
					</div>
				{/snippet}
			</EntityCard>
		{/if}

		{#if linkedEntityWithRelated}
			<!-- 10px long dashes, using svg -->
			<div
				class="mb-12 -ml-3 w-[calc(100%_+_1.5rem)] text-slate-300 md:-ml-6 md:w-[calc(100%_+_3rem)] xl:-ml-10 xl:w-[calc(100%_+_5rem)] dark:text-slate-600"
				style="height: 1px; background: repeating-linear-gradient(to right, currentColor 0px, currentColor 10px, transparent 10px, transparent 15px);"
			></div>
			<div class="mb-6">
				<RelatedEntitiesDisplay entity={linkedEntityWithRelated}>
					{#snippet headerActions()}
						<div class="flex flex-1 items-center">
							<!-- Created in message link for PRs -->
							{#if hasPREntity && prCreationInfo}
								<div
									class="mt-[1px] ml-2 flex items-center gap-1 text-sm font-normal whitespace-nowrap"
								>
									<span>• &nbsp;Created in</span>
									<button
										onclick={() => handleScrollToExchange(prCreationInfo!.exchangeIndex)}
										class="cursor-pointer hover:text-slate-700 hover:underline dark:hover:text-slate-300"
									>
										message {prCreationInfo.groupNumber}
									</button>
								</div>
							{/if}

							<!-- File changes since last push for PRs -->
							{#if hasPREntity && changesSinceLastPush.length > 0}
								<Button
									variant="secondary"
									size="sm"
									onclick={handleViewChangesSinceLastPush}
									icon={DocumentText}
									class="mr-3 ml-auto"
									loading={isPushingChanges}
									disabled={isPushingChanges}
								>
									{#if isPushingChanges}
										Syncing...
									{:else}
										{changesSinceLastPush.length} change{changesSinceLastPush.length === 1
											? ''
											: 's'}
										behind
									{/if}
								</Button>
							{/if}
						</div>
					{/snippet}
				</RelatedEntitiesDisplay>
			</div>
		{/if}

		<!-- Chat History Section -->
		{#if showChatHistory}
			<div class="mt-6 space-y-4">
				<div
					class="rounded-2xl border border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800"
				>
					<div class="border-b border-slate-200 px-4 py-3 dark:border-slate-700">
						<h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100">Chat History</h3>
					</div>
					<div class="p-4">
						<ChatHistoryView
							{exchanges}
							{optimisticMessage}
							{isStreaming}
							{streamingContent}
							{streamingMessages}
							workspaceStatus={agent.workspaceStatus}
							maxHeight="400px"
							{agent}
						/>
					</div>
				</div>

				<!-- Chat Input -->
				{#if agent?.id}
					<div
						class="rounded-2xl border border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800"
					>
						<div class="p-4">
							<AgentChatResponse agentId={agent.id} />
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
</div>

<!-- Agent Deletion Modal -->
<AgentDeletionModal
	isOpen={showDeletionModal}
	{agent}
	onClose={() => (showDeletionModal = false)}
	{onAgentDeleted}
/>

<!-- Changes Since Last Push Modal -->
<Modal
	isOpen={showChangesSinceLastPushModal}
	title="Changes since last push"
	size="xl"
	onClose={() => (showChangesSinceLastPushModal = false)}
>
	<div class="flex h-full flex-col">
		<!-- Content -->
		<div class="flex-1 p-6">
			<p class="mb-4 text-sm text-slate-600 dark:text-slate-400">
				{changesSinceLastPush.length} file{changesSinceLastPush.length === 1 ? '' : 's'} changed since
				the last push to PR
			</p>
			<CodeStoryWidget changedFiles={changesSinceLastPush} />
		</div>

		<!-- Action Buttons -->
		<div class="flex justify-end gap-3 border-t border-slate-200 px-6 py-4 dark:border-slate-700">
			<Button variant="secondary" onclick={() => (showChangesSinceLastPushModal = false)}>
				Cancel
			</Button>
			<Button
				variant="primary"
				onclick={async () => {
					showChangesSinceLastPushModal = false;
					await pushChangesToPR();
				}}
				icon={ArrowPath}
				disabled={isPushingChanges}
			>
				{isPushingChanges ? 'Syncing...' : 'Sync PR with changes'}
			</Button>
		</div>
	</div>
</Modal>
