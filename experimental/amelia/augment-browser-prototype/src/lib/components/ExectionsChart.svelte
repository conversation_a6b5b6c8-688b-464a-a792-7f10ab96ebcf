<script lang="ts">
	import { globalState } from '$lib/stores/global-state.svelte';
	import type { CleanTriggerExecution } from '$lib/api/unified-client';
	import { getRelativeTime } from '$lib/utils/time';
	import { timeDay, timeDays } from 'd3';

	interface Props {
		executions: CleanTriggerExecution[];
		onExecutionClick?: (execution: CleanTriggerExecution) => void;
	}

	let { executions, onExecutionClick }: Props = $props();

	// State for tooltip
	let hoveredDay: any = $state(null);
	let hoveredExecution: any = $state(null);
	let tooltipPosition = $state({ x: 0, y: 0 });

	// Calculate date range (last 7 days or actual data range, whichever is larger)
	const now = new Date();
	const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

	const dataExtent = $derived(() => {
		if (executions.length === 0) return [sevenDaysAgo, now];
		const dates = executions.map(e => e.startedAt).filter((d): d is Date => d !== undefined);
		const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
		const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));
		return [
			minDate < sevenDaysAgo ? minDate : sevenDaysAgo,
			maxDate > now ? maxDate : now
		];
	});

	const dailyGroups = $derived.by(() => {
		const [startDate, endDate] = dataExtent();
		return timeDays(startDate, endDate).map(day => {
			const executionsForDay = executions.filter(execution => {
				const execDate = execution.startedAt;
				return execDate && timeDay(execDate).getTime() === day.getTime();
			}).sort((a, b) => {
				if (!a.startedAt || !b.startedAt) return 0;
				return b.startedAt.getTime() - a.startedAt.getTime();
			}); // Sort newest first

            const executionsWithData = executionsForDay.map(execution => {
                const agent = $globalState.agents[execution.remoteAgentId || ''];
				const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']; // Default colors

                return {
                    ...execution,
                    agent,
                    colors
                };
            });

			// Calculate status counts for tooltip
			const statusCounts = executionsForDay.reduce((acc, execution) => {
				const status = execution.status;
				if (status === 'COMPLETED') {
					acc.success++;
				} else if (status === 'FAILED') {
					acc.failed++;
				} else if (status === 'RUNNING') {
					acc.running++;
				} else if (status === 'PENDING') {
					acc.pending++;
				}
				return acc;
			}, { success: 0, failed: 0, running: 0, pending: 0 });

			return {
				date: day,
				executions: executionsWithData,
				total: executionsForDay.length,
				...statusCounts
			};
		});
	});

	// Derived values for tooltip positioning
	let isHoveredDayFirstDay = $derived(hoveredDay && dailyGroups.length > 0 && +timeDay(hoveredDay.date) === +timeDay(dailyGroups[0].date));
	let isHoveredDayLastDay = $derived(hoveredDay && dailyGroups.length > 0 && +timeDay(hoveredDay.date).getTime() === +timeDay(dailyGroups[dailyGroups.length - 1].date).getTime());
	let tooltipXOffset = $derived(isHoveredDayFirstDay ? 30 : isHoveredDayLastDay ? -30 : 0);

	function handleColumnMouseEnter(day: any, event: MouseEvent) {
		hoveredDay = day;
		console.log('hoveredDay', hoveredDay);
		hoveredExecution = null; // Reset execution hover when entering column
		const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
		tooltipPosition = {
			x: rect.left + rect.width / 2,
			y: rect.top - 10
		};
	}

	function handleColumnMouseLeave() {
		hoveredDay = null;
		hoveredExecution = null;
	}

	function handleExecutionMouseEnter(execution: CleanTriggerExecution, event: MouseEvent) {
		hoveredExecution = execution;
		// Don't update tooltip position here, let the column handle it
		event.stopPropagation();
	}

	function handleExecutionMouseLeave(event: MouseEvent) {
		hoveredExecution = null;
		event.stopPropagation();
	}

	function getEntityName(execution: any): string {
		if (execution.entity?.title) return execution.entity.metadata?.display_title || execution.entity.title;
		if (execution.agent?.title) {
			return execution.agent.title.split('\n')[0] || 'Agent Task';
		}
		if (execution.entity?.title) return execution.entity.title;
		if (execution.entity?.id) return `Entity ${execution.entity.id}`;
		return execution.id?.slice(0, 8) || 'Unknown';
	}

	function getStatusColor(execution: any): string {
		// Use colors from execution if available, otherwise fall back to status-based colors
		if (execution.colors?.[0]) {
			return execution.colors[0];
		}

		const status = execution.status;
		switch (status) {
			case 'PENDING': return '#f59e0b'; // PENDING - amber
			case 'RUNNING': return '#3b82f6'; // RUNNING - blue
			case 'COMPLETED': return '#10b981'; // COMPLETED - green
			case 'FAILED': return '#ef4444'; // FAILED - red
			default: return '#6b7280'; // gray
		}
	}


</script>

<div class="relative">
	<!-- Chart container -->
	<div class="flex justify-between h-40 p-4 bg-gray-50x dark:bg-gray-900 rounded-lg" onmouseleave={handleColumnMouseLeave} role="application" aria-label="Execution timeline chart">
		{#each dailyGroups as day (day.date.getTime())}
			<div class="h-full flex flex-col items-center flex-1 min-w-0 px-1 py-2 hover:bg-slate-50 dark:hover:bg-slate-800"
					onmouseenter={(e) => handleColumnMouseEnter(day, e)}
					role="button"
					tabindex="0"
					aria-label="Executions for {day.date.toLocaleDateString()}"
					>
				<!-- Column of stacked execution divs -->
				<div
					class="flex-1 flex flex-col justify-end gap-0.5 w-full cursor-pointer transition-opacity hover:opacity-80"
					style="height: {Math.max(day.total * 8, 4)}px; max-height: 80px;"
					role="button"
					tabindex="0"
				>
					{#each day.executions as execution, index (execution.id || `execution-${index}`)}
						<div
							class="w-full rounded-sm flex-shrink-0 cursor-pointer duration-150 border border-transparent hover:border-slate-600 dark:hover:border-slate-600"
							style="background-color: {getStatusColor(execution)}; height: 6px; min-height: 2px;"
							title={getEntityName(execution)}
							onmouseenter={(e) => handleExecutionMouseEnter(execution, e)}
							onmouseleave={handleExecutionMouseLeave}
							onclick={() => onExecutionClick?.(execution)}
							onkeydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { onExecutionClick?.(execution); } }}
							role="button"
							tabindex="0"
						></div>
					{/each}

					<!-- Show a placeholder if no executions -->
					{#if day.total === 0}
						<div class="w-full h-0.5 bg-gray-200 dark:bg-gray-700 rounded-sm opacity-50"></div>
					{/if}
				</div>

				<!-- Date label -->
				<div class="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
					{day.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
				</div>
			</div>
		{/each}
	</div>

	<!-- Tooltip -->
	{#if hoveredDay}
		<div
			class="absolute z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 min-w-64 max-w-80 transition-all duration-150"
			style={`left: ${tooltipPosition.x}px; bottom: 0; transform: translateX(-66%) translateY(100%) translateX(${tooltipXOffset}px);`}
		>
			<div class="font-medium text-sm mb-2">
				{hoveredDay.date.toLocaleDateString('en-US', {
					weekday: 'long',
					month: 'long',
					day: 'numeric'
				})}
			</div>

			<div class="text-sm text-gray-600 dark:text-gray-300 mb-2">
				<div class="flex justify-between">
					{#if hoveredDay.total === 0}
						<div class="mt-1 text-slate-500 italic text-center w-full">No executions</div>
					{:else}
					<span>Total executions:</span>
					<span class="font-medium">{hoveredDay.total}</span>
					{/if}
				</div>
				{#if hoveredDay.success > 0}
					<div class="flex justify-between text-green-600 dark:text-green-400">
						<span>Completed:</span>
						<span class="font-medium">{hoveredDay.success}</span>
					</div>
				{/if}
				{#if hoveredDay.running > 0}
					<div class="flex justify-between text-blue-600 dark:text-blue-400">
						<span>Running:</span>
						<span class="font-medium">{hoveredDay.running}</span>
					</div>
				{/if}
				{#if hoveredDay.pending > 0}
					<div class="flex justify-between text-amber-600 dark:text-amber-400">
						<span>Pending:</span>
						<span class="font-medium">{hoveredDay.pending}</span>
					</div>
				{/if}
				{#if hoveredDay.failed > 0}
					<div class="flex justify-between text-red-600 dark:text-red-400">
						<span>Failed:</span>
						<span class="font-medium">{hoveredDay.failed}</span>
					</div>
				{/if}
			</div>

			{#if hoveredDay.executions.length > 0}
				<div class="border-t border-gray-200 dark:border-gray-600 pt-2">
					<!-- <div class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Entities:</div> -->
					<div class="max-h-60 overflow-y-auto">
						{#each hoveredDay.executions as execution, index (execution.id)}
							<div
								class="flex items-center gap-2 py-1 px-1 rounded transition-colors duration-100 border {
									hoveredExecution?.id === execution.id
										? 'bg-blue-50 dark:bg-blue-900/20 border-slate-600 dark:border-slate-700'
										: 'hover:bg-gray-50 dark:hover:bg-gray-700 border-transparent '
								}"
							>
								<div
									class="w-2 h-2 rounded-full flex-shrink-0"
									style="background-color: {getStatusColor(execution)}"
								></div>
								<span class="text-xs flex-1 {
									hoveredExecution?.id === execution.id
										? 'text-blue-900 dark:text-blue-100'
										: 'text-gray-700 dark:text-gray-300'
								} truncate">
									{getEntityName(execution)}
								</span>
								{#if execution.startedAt}
									<span class="text-xs text-gray-500 dark:text-gray-400 truncate">
										<!-- time ago -->
										{getRelativeTime(execution.startedAt)}
									</span>
								{/if}
							</div>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
