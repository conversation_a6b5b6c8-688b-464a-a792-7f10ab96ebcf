<script lang="ts">
	import type { CleanRemoteAgent, CleanChangedFile } from '$lib/api/unified-client';
	import type { Task } from '$lib/types';
	import FileEditSummary from './FileEditSummary.svelte';
	import PRButton from '../ui/navigation/PRButton.svelte';
	import { getAgentChangedFilesWithDetails } from '$lib/utils/agent-data';
	import { getProviderAuthStatus, initiateProviderOAuth } from '$lib/stores/provider-auth';
	import { githubToken } from '$lib/stores/integrations';
	import { onMount } from 'svelte';
	import { Icon, CodeBracket, ExclamationTriangle } from 'svelte-hero-icons';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { GitBranch } from '$lib/icons/GitBranchIcon.svelte';

	interface Props {
		agent: CleanRemoteAgent;
		projectId?: string;
		taskId?: string;
	}

	let { agent, projectId, taskId }: Props = $props();

	// Get changed files for this agent
	let changedFilesStore = $derived(getAgentChangedFilesWithDetails(agent.id));
	let changedFiles = $derived($changedFilesStore || []);

	// Get GitHub authentication status
	const githubAuthStatus = getProviderAuthStatus('github');
	let isGitHubAuthenticated = $derived($githubAuthStatus?.isConfigured || false);

	// Get task information
	let currentTask = $derived(() => {
		if (!taskId) return null;
		const allTasks = [];
		return allTasks.find((t) => t.id === taskId) || null;
	});

	// Fetch task if we have taskId but no task
	let isLoadingTask = $state(false);

	async function fetchTask() {
		if (!taskId || currentTask() || isLoadingTask) return;

		isLoadingTask = true;
		try {
			const response = await fetch(`/api/tasks/${taskId}`);
			if (response.ok) {
				const taskData = await response.json();
				// Update the tasks store with the fetched task
				// tasks.update(currentTasks => {
				// 	const existingIndex = currentTasks.findIndex(t => t.id === taskId);
				// 	if (existingIndex >= 0) {
				// 		currentTasks[existingIndex] = taskData;
				// 	} else {
				// 		currentTasks.push(taskData);
				// 	}
				// 	return currentTasks;
				// });
			}
		} catch (error) {
			console.error('Failed to fetch task:', error);
		} finally {
			isLoadingTask = false;
		}
	}

	// Fetch task when component mounts if needed
	onMount(() => {
		fetchTask();
	});

	// Watch for taskId changes
	$effect(() => {
		fetchTask();
	});

	async function handleGitHubLogin() {
		try {
			await initiateProviderOAuth('github');
		} catch (error) {
			console.error('GitHub login failed:', error);
		}
	}

	function handlePRCreated(pr: any) {
		console.log('PR created:', pr);
		// The PRButton component handles updating the task store
	}

	function handlePRUpdated(pr: any) {
		console.log('PR updated:', pr);
		// The PRButton component handles updating the task store
	}

	// Check if there are any code changes
	const hasChanges = $derived(changedFiles.length > 0);

	// Get PR status info
	const prInfo = $derived(() => {
		const task = currentTask();
		if (!task) return null;
		return {
			hasPR: !!task.prId,
			prId: task.prId,
			prUrl: task.prUrl,
			prStatus: task.prStatus,
			isPRMerged: task.prStatus === 'merged'
		};
	});
</script>

<div class="flex h-full flex-col bg-white dark:bg-gray-900">
	<!-- Header with PR Status -->
	{#if currentTask()}
		<div
			class="border-b border-gray-200 bg-gray-50 px-[var(--panel-padding-x)] py-4 dark:border-gray-700 dark:bg-gray-800/50"
		>
			<div class="flex items-center justify-between">
				<div class="flex items-center gap-2">
					<Icon src={CodeBracket} class="h-5 w-5 text-gray-500 dark:text-gray-400" />
					<div>
						<h3 class="text-sm font-medium text-gray-900 dark:text-white">
							Code Changes & Pull Request
						</h3>
					</div>
				</div>

				<!-- PR Button -->
				<div class="flex items-center gap-2">
					{#if !isGitHubAuthenticated}
						<div class="flex items-center gap-2">
							<Icon src={ExclamationTriangle} class="h-4 w-4 text-amber-500" />
							<span class="text-xs text-gray-500 dark:text-gray-400">GitHub auth required</span>
							<Button size="xs" variant="outline" onclick={handleGitHubLogin}>
								Connect GitHub
							</Button>
						</div>
					{:else if currentTask()}
						{@const task = currentTask()}
						{#if task}
							<PRButton
								{task}
								remoteAgent={agent}
								{changedFiles}
								githubToken={$githubToken || undefined}
								onPRCreated={handlePRCreated}
								onPRUpdated={handlePRUpdated}
							/>
						{/if}
					{/if}
				</div>
			</div>

			<!-- PR Status Info -->
			{#if prInfo()}
				{@const currentPRInfo = prInfo()}
				{#if currentPRInfo?.hasPR}
					<div class="mt-3 flex items-center gap-4 text-xs text-gray-600 dark:text-gray-300">
						<div class="flex items-center gap-1">
							<Icon src={GitBranch} class="h-3 w-3" />
							<span>PR #{currentPRInfo.prId}</span>
						</div>
						<div class="flex items-center gap-1">
							<div
								class="h-2 w-2 rounded-full {currentPRInfo.prStatus === 'merged'
									? 'bg-purple-500'
									: currentPRInfo.prStatus === 'closed'
										? 'bg-red-500'
										: currentPRInfo.prStatus === 'draft'
											? 'bg-gray-500'
											: 'bg-green-500'}"
							></div>
							<span class="capitalize">{currentPRInfo.prStatus || 'open'}</span>
						</div>
						{#if currentPRInfo.prUrl}
							<a
								href={currentPRInfo.prUrl}
								target="_blank"
								rel="noopener noreferrer"
								class="text-blue-600 hover:underline dark:text-blue-400"
							>
								View on GitHub →
							</a>
						{/if}
					</div>
				{/if}
			{/if}
		</div>
	{/if}

	<!-- Content Area -->
	<div class="flex-1 overflow-auto">
		{#if isLoadingTask}
			<div class="flex h-32 items-center justify-center py-12">
				<div class="text-center">
					<div
						class="mx-auto mb-2 h-6 w-6 animate-spin rounded-full border-b-2 border-blue-500"
					></div>
					<p class="text-sm text-gray-500 dark:text-gray-400">Loading task...</p>
				</div>
			</div>
		{:else if hasChanges}
			<div class="p-[var(--panel-padding-x)]">
				<FileEditSummary
					{changedFiles}
					{projectId}
					{taskId}
					enableNavigation={!!(projectId && taskId)}
					turnIndex={-1}
					isExpanded={true}
				/>
			</div>
		{:else}
			<!-- Empty state -->
			<div class="flex h-full items-center justify-center py-12">
				<div class="max-w-sm text-center">
					<Icon src={CodeBracket} class="mx-auto mb-4 h-12 w-12 text-gray-300 dark:text-gray-600" />
					<h3 class="mb-2 text-sm font-medium text-gray-900 dark:text-white">
						No code changes yet
					</h3>
					<p class="text-xs leading-relaxed text-gray-500 dark:text-gray-400">
						When the agent makes changes to files, they'll appear here. You can then create a pull
						request to review and merge the changes.
					</p>
				</div>
			</div>
		{/if}
	</div>
</div>
