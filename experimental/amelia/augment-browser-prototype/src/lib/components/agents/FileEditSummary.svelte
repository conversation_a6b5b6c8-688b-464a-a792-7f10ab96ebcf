<script lang="ts">
	import type { CleanChangedFile } from '$lib/api/unified-client';
	import { ArrowPath, ChevronDown, ChevronRight, DocumentText, Icon, Minus, Pencil, Plus } from 'svelte-hero-icons';
	import { quintOut } from 'svelte/easing';
	import { slide } from 'svelte/transition';
	import SizedDot from './SizedDot.svelte';

	interface Props {
		changedFiles: CleanChangedFile[];
		isExpanded?: boolean;
		projectId?: string;
		taskId?: string;
		enableNavigation?: boolean;
		turnIndex?: number; // Turn index for specific turn, or -1 for aggregate
	}

	let { changedFiles, isExpanded = true, projectId, taskId, enableNavigation = false, turnIndex = -1 }: Props = $props();

	let localExpanded = $state(isExpanded);
	let selectedFile = $state<CleanChangedFile | null>(null);

	// Get file change type display info
	function getChangeTypeInfo(changeType: string) {
		switch (changeType) {
			case 'added':
				return { icon: Plus, color: 'text-emerald-600 dark:text-emerald-400', bgColor: 'bg-green-50 dark:bg-green-900/20' };
			case 'deleted':
				return { icon: Minus, color: 'text-red-600 dark:text-red-400', bgColor: 'bg-red-50 dark:bg-red-900/20' };
			case 'modified':
				return { icon: Pencil, color: 'text-amber-400 dark:text-amber-600', bgColor: 'bg-blue-50 dark:bg-blue-900/20' };
			case 'renamed':
				return { icon: ArrowPath, color: 'text-purple-600 dark:text-purple-400', bgColor: 'bg-purple-50 dark:bg-purple-900/20' };
			default:
				return { icon: Pencil, color: 'text-slate-600 dark:text-slate-400', bgColor: 'bg-slate-50 dark:bg-slate-900/20' };
		}
	}


	// Get the file path from the changed file object
	function getFilePath(file: CleanChangedFile): string {
		return file.path || 'Unknown file';
	}

	// Get file name from path
	function getFileName(filePath: string | undefined): string {
		if (!filePath) return 'Unknown file';
		return filePath.split('/').pop() || filePath;
	}



	// Group files by directory structure
	interface FileTreeNode {
		path: string;
		files: CleanChangedFile[];
		children: Map<string, FileTreeNode>;
		isRoot?: boolean;
	}

	function buildFileTree(files: CleanChangedFile[]): FileTreeNode {
		const root: FileTreeNode = {
			path: '',
			files: [],
			children: new Map(),
			isRoot: true
		};

		for (const file of files) {
			const filePath = getFilePath(file);
			const parts = filePath.split('/');

			if (parts.length === 1) {
				// File in root directory
				root.files.push(file);
			} else {
				// File in subdirectory
				const dirPath = parts.slice(0, -1).join('/');
				let current = root;

				// Build the directory path
				const pathParts = dirPath.split('/');
				let currentPath = '';

				for (const part of pathParts) {
					currentPath = currentPath ? `${currentPath}/${part}` : part;

					if (!current.children.has(part)) {
						current.children.set(part, {
							path: currentPath,
							files: [],
							children: new Map()
						});
					}
					current = current.children.get(part)!;
				}

				// Add file to the final directory
				current.files.push(file);
			}
		}

		return root;
	}

	let fileTree = $derived(buildFileTree(changedFiles));
	let totalAdditions = $derived(0); // Not available in current API
	let totalDeletions = $derived(0); // Not available in current API

	// For collapsed preview - show first 3 files
	const MAX_PREVIEW_FILES = 3;
	let previewFiles = $derived(changedFiles.slice(0, MAX_PREVIEW_FILES));
	let remainingFilesCount = $derived(Math.max(0, changedFiles.length - MAX_PREVIEW_FILES));

	// Calculate the minimum depth to make all depths relative to the shallowest path
	let minDepth = $derived.by(() => {
		if (changedFiles.length === 0) return 0;

		const depths = changedFiles.map(file => {
			const filePath = getFilePath(file);
			return filePath.split('/').length - 1; // -1 because file name doesn't count as depth
		});

		return Math.min(...depths);
	});



	// Track expanded state for each directory - expand all by default
	function getAllDirectoryPaths(node: FileTreeNode): string[] {
		const paths: string[] = [];
		// Add any non-root directory (whether it has children or files)
		if (!node.isRoot) {
			paths.push(node.path);
		}
		for (const child of node.children.values()) {
			paths.push(...getAllDirectoryPaths(child));
		}
		return paths;
	}

	let expandedDirs = $state(new Set<string>());

	// Update expanded directories when file tree changes
	$effect(() => {
		const allPaths = getAllDirectoryPaths(fileTree);
		expandedDirs = new Set(allPaths);
	});

	// Helper function to count all files in a directory tree (including subdirectories)
	function getTotalFileCount(node: FileTreeNode): number {
		let count = node.files.length;
		for (const child of node.children.values()) {
			count += getTotalFileCount(child);
		}
		return count;
	}

</script>

{#if changedFiles.length > 0}
	<div class="border border-slate-200 dark:border-slate-700 rounded-lg overflow-hidden bg-white dark:bg-slate-800">
		<!-- Header -->
		<div
			class="p-3 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors border-b border-slate-200 dark:border-slate-700"
			role="button"
			tabindex="0"
			onclick={() => { localExpanded = !localExpanded; }}
			onkeydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); localExpanded = !localExpanded; } }}
			aria-label={localExpanded ? 'Collapse file changes' : 'Expand file changes'}
		>
			<div class="flex items-center gap-1">
				<!-- File icon -->
				<Icon src={DocumentText} class="w-4 h-4 text-slate-500 dark:text-slate-400" micro />

				<!-- Summary -->
				<div class="flex-1 min-w-0">
					<div class="flex items-center gap-2 text-xs">
						<span class="flex-1 text-slate-900 dark:text-slate-100">
							{changedFiles.length} file{changedFiles.length !== 1 ? 's' : ''} changed
						</span>

						{#if totalAdditions > 0 || totalDeletions > 0}
							<div class="flex items-center gap-1">
								{#if totalAdditions > 0}
								<SizedDot type="addition" count={totalAdditions} />
								{/if}

								{#if totalDeletions > 0}
								<SizedDot type="deletion" count={totalDeletions} />
								{/if}
							</div>
						{/if}
					</div>
				</div>

				<!-- Expand/collapse icon -->
				<Icon
					src={localExpanded ? ChevronDown : ChevronRight}
					class="w-4 h-4 text-slate-400 transition-transform duration-200"
					micro
				/>
			</div>
		</div>

		<!-- Collapsed preview - show first 3 files in a row -->
		{#if !localExpanded && changedFiles.length > 0}
			<div class="p-3">
				<div class="flex items-center gap-2 flex-wrap">
					{#each previewFiles as file}
						{@const changeInfo = getChangeTypeInfo(file.changeType)}
						{@const filePath = getFilePath(file)}
						<div class="flex items-center gap-1 px-2 py-1 bg-slate-50 dark:bg-slate-700 rounded text-xs">
							<Icon src={changeInfo.icon} class="w-3 h-3 {changeInfo.color}" micro />
							<span class="text-slate-700 dark:text-slate-300">{getFileName(filePath)}</span>
						</div>
					{/each}

					{#if remainingFilesCount > 0}
						<div class="px-2 py-1 bg-slate-100 dark:bg-slate-600 rounded text-xs text-slate-600 dark:text-slate-400">
							+{remainingFilesCount} more
						</div>
					{/if}
				</div>
			</div>
		{/if}

		<!-- Expanded content -->
		{#if localExpanded}
			<div transition:slide={{ duration: 300, easing: quintOut }}>
				<div class="divide-y divide-slate-200 dark:divide-slate-700">
					{#snippet renderFileTreeNode(node: FileTreeNode, depth: number = 0)}


						<!-- Render directory header if not root and has children -->
						{#if !node.isRoot && node.files.length > 0}
							{@const isExpanded = expandedDirs.has(node.path)}
							<div
								class="flex items-center gap-2 px-3 py-[5px] hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer"
								style="padding-left: {Math.max(0, depth - minDepth) * 16 + 12}px"
								role="button"
								tabindex="0"
								 transition:slide={{axis: "y"}}
								onclick={() => {
									if (isExpanded) {
										expandedDirs.delete(node.path);
									} else {
										expandedDirs.add(node.path);
									}
									expandedDirs = new Set(expandedDirs);
								}}
								onkeydown={(e) => {
									if (e.key === 'Enter' || e.key === ' ') {
										e.preventDefault();
										if (isExpanded) {
											expandedDirs.delete(node.path);
										} else {
											expandedDirs.add(node.path);
										}
										expandedDirs = new Set(expandedDirs);
									}
								}}
								aria-label={isExpanded ? `Collapse ${node.path} directory` : `Expand ${node.path} directory`}
							>
								<Icon
									src={isExpanded ? ChevronDown : ChevronRight}
									class="w-3 h-3 text-slate-400"
									micro
								/>
								<span class="text-xs font-medium text-slate-600 dark:text-slate-300">
									{node.path.split('/').pop()}
								</span>
								<!-- <span class="text-xs text-slate-400">
									({getTotalFileCount(node)} files)
								</span> -->
							</div>
						{/if}

						<!-- Render files in this directory -->
						{#if node.isRoot || expandedDirs.has(node.path)}
						<div class="w-full divide-y divide-slate-200 dark:divide-slate-700" transition:slide={{axis: "y"}}>
							{#each node.files as file}
								{@const changeInfo = getChangeTypeInfo(file.changeType)}
								{@const filePath = getFilePath(file)}
								{@const fileId = encodeURIComponent(filePath)}
								{@const indentLevel = Math.max(0, depth + (node.isRoot ? 0 : 1) - minDepth)}
								<div style="padding-left: {indentLevel * 16}px" transition:slide={{axis: "y"}}>
									{#if enableNavigation && projectId && taskId}
										<div class="flex items-center gap-2 px-2 py-0.5 w-full text-left justify-start">
											<!-- Change type badge -->
											<div class="flex-shrink-0">
												<span class="inline-flex items-center py-1 text-xs font-medium rounded-full {changeInfo.color}">
													<Icon src={changeInfo.icon} class="w-3.5" micro />
												</span>
											</div>

											<!-- File name -->
											<div class="flex-1 min-w-0">
												<div class="text-xs text-slate-900 dark:text-slate-100 font-medium">
													{getFileName(filePath)}
												</div>
											</div>

											<!-- Stats not available in current API -->
										</div>
									{:else}
										<!-- Non-navigable file display -->
										<div class="flex items-center gap-2 px-2 py-0.5" transition:slide={{axis: "y"}}>
											<!-- Change type badge -->
											<div class="flex-shrink-0">
												<span class="inline-flex items-center px-1 py-1 text-xs font-medium rounded-full {changeInfo.color}">
													<Icon src={changeInfo.icon} class="w-3.5" micro />
												</span>
											</div>

											<!-- File name -->
											<div class="flex-1 min-w-0">
												<div class="text-xs text-slate-900 dark:text-slate-100 font-medium">
													{getFileName(filePath)}
												</div>
											</div>

											<!-- Stats not available in current API -->
										</div>
									{/if}
								</div>
							{/each}

							<!-- Render child directories -->
							{#each Array.from(node.children.values()) as childNode}
								{@render renderFileTreeNode(childNode, depth + (node.isRoot ? 0 : 1))}
							{/each}
							</div>
						{/if}
					{/snippet}

					{@render renderFileTreeNode(fileTree)}
				</div>
			</div>
		{/if}
	</div>
{/if}
