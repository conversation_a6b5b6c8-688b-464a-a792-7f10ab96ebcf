<script lang="ts">
	import MonacoProvider from '$lib/components/MonacoProvider';
	import { theme } from '$lib/stores/theme';
	import { CursorArrowRipple, Icon } from 'svelte-hero-icons';
	import { fade } from 'svelte/transition';

	interface Props {
		content: string;
		fileName?: string;
		height?: string | number;
		class?: string;
		enableFocusToScroll?: boolean;
	}

	let {
		content,
		fileName,
		height = 300,
		class: className = '',
		enableFocusToScroll = false
	}: Props = $props();

	let focused = $state(false);
	let editorInstance = $state<any>(undefined);
	let hovered = $state(false);
	let showScrollHint = $state(false);
	let scrollDelta = $state(0);

	// Scroll hint threshold (pixels)
	const SCROLL_THRESHOLD = 120;

	// Handle scroll hint logic
	function handleScrollHint() {
		// Reset scroll hint when focused, not hovered, or feature disabled
		if (focused || !hovered || !enableFocusToScroll) {
			showScrollHint = false;
			scrollDelta = 0;
			return;
		}
	}

	// Handle mouse enter
	function handleMouseEnter() {
		hovered = true;
	}

	// Handle mouse leave
	function handleMouseLeave() {
		hovered = false;
		showScrollHint = false;
		scrollDelta = 0;
	}

	// Handle wheel events when not focused
	function handleWheel(event: WheelEvent) {
		if (!hovered || focused || !enableFocusToScroll) return;

		// Accumulate scroll delta
		scrollDelta += Math.abs(event.deltaY);

		// Show hint if threshold reached
		if (scrollDelta >= SCROLL_THRESHOLD && !showScrollHint) {
			showScrollHint = true;
		}
	}

	// Handle click to focus
	function handleClickToFocus() {
		if (editorInstance) {
			editorInstance.focus();
		}
		showScrollHint = false;
		scrollDelta = 0;
	}

	// Watch for focus/hover changes to update scroll hint
	$effect(() => {
		handleScrollHint();
	});

	// Get language from file extension
	function getLanguageFromFileName(fileName: string | undefined): string {
		if (!fileName) return 'plaintext';
		const extension = fileName.split('.').pop()?.toLowerCase();

		switch (extension) {
			case 'js':
			case 'jsx':
				return 'javascript';
			case 'ts':
			case 'tsx':
				return 'typescript';
			case 'svelte':
				return 'html'; // Monaco doesn't have native Svelte support, use HTML
			case 'html':
				return 'html';
			case 'css':
				return 'css';
			case 'scss':
			case 'sass':
				return 'scss';
			case 'json':
				return 'json';
			case 'md':
			case 'markdown':
				return 'markdown';
			case 'py':
				return 'python';
			case 'rs':
				return 'rust';
			case 'go':
				return 'go';
			case 'java':
				return 'java';
			case 'c':
				return 'c';
			case 'cpp':
			case 'cc':
			case 'cxx':
				return 'cpp';
			case 'sh':
			case 'bash':
				return 'shell';
			case 'sql':
				return 'sql';
			case 'xml':
				return 'xml';
			case 'yaml':
			case 'yml':
				return 'yaml';
			default:
				return 'plaintext';
		}
	}

	// Calculate height based on content if not specified
	function calculateHeight(content: string, specifiedHeight?: string | number): number {
		if (typeof specifiedHeight === 'number') return specifiedHeight;
		if (typeof specifiedHeight === 'string') {
			// Try to parse string height (e.g., "300px" -> 300)
			const parsed = parseInt(specifiedHeight, 10);
			if (!isNaN(parsed)) return parsed;
		}

		// Count lines in content
		const lines = content.split('\n').length;
		const lineHeight = 18; // Approximate line height in pixels
		const padding = 40; // Account for padding and borders

		// Calculate height with reasonable bounds
		const calculatedHeight = Math.max(120, Math.min(500, lines * lineHeight + padding));

		return calculatedHeight;
	}

	// Derived values
	let language = $derived(getLanguageFromFileName(fileName));
	let editorHeight = $derived(calculateHeight(content, height));
	let editorTheme = $derived($theme === 'dark' ? 'vs-dark' : 'vs-light');
	let scrollbarOptions = $derived({
		vertical: 'auto',
		horizontal: 'auto',
		verticalScrollbarSize: 8,
		horizontalScrollbarSize: 8,
		alwaysConsumeMouseWheel: false, // Start with false, will be updated by effect
		handleMouseWheel: false // Start with false, will be updated by effect
	});

	// Set up focus/blur listeners when editor instance is available
	$effect(() => {
		if (editorInstance) {
			const handleFocus = () => {
				focused = true;
			};

			const handleBlur = () => {
				focused = false;
			};

			// Add focus/blur event listeners
			const focusDisposable = editorInstance.onDidFocusEditorText(handleFocus);
			const blurDisposable = editorInstance.onDidBlurEditorText(handleBlur);

			// Return cleanup function
			return () => {
				focusDisposable?.dispose();
				blurDisposable?.dispose();
			};
		}
	});

	// Update editor scrollbar options when focus state changes
	$effect(() => {
		if (editorInstance && focused !== undefined) {
			editorInstance.updateOptions({
				scrollbar: {
					vertical: 'auto',
					horizontal: 'auto',
					verticalScrollbarSize: 8,
					horizontalScrollbarSize: 8,
					alwaysConsumeMouseWheel: focused,
					handleMouseWheel: focused
				}
			});
		}
	});
</script>

<div
	class="h-full w-full overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 {className} relative"
	role="region"
	aria-label="Code content viewer"
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
	onwheel={handleWheel}
>
	<!-- Monaco editor container -->
	<div class="w-full bg-white dark:bg-slate-900">
		<MonacoProvider.Root>
			<MonacoProvider.SimpleMonaco
				text={content}
				lang={language}
				pathName={fileName}
				theme={editorTheme}
				height={editorHeight}
				bind:editorInstance
				options={{
					readOnly: true,
					minimap: { enabled: false },
					scrollBeyondLastLine: false,
					wordWrap: 'on',
					lineNumbers: 'on',
					renderLineHighlight: 'none',
					hideCursorInOverviewRuler: true,
					overviewRulerBorder: false,
					scrollbar: scrollbarOptions,
					fontSize: 11.5,
					lineHeight: 18,
					padding: { top: 6, bottom: 6 }
				}}
			/>
		</MonacoProvider.Root>
	</div>

	<!-- Scroll hint overlay -->
	{#if showScrollHint}
		<div
			class="absolute inset-0 flex cursor-pointer items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm transition-opacity duration-200 dark:bg-black/20"
			onclick={handleClickToFocus}
			role="button"
			tabindex="0"
			aria-label="Click to focus and scroll"
			onkeydown={(e) => e.key === 'Enter' && handleClickToFocus()}
			transition:fade={{ duration: 300 }}
		>
			<div
				class="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-3 text-sm font-medium text-gray-700 shadow-lg dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300"
			>
				<Icon src={CursorArrowRipple} class="h-4 w-4" micro />
				Click to focus and scroll
			</div>
		</div>
	{/if}
</div>
