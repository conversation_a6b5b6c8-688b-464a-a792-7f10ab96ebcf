<script lang="ts">
	import { Icon, Book<PERSON><PERSON>, PencilSquare } from 'svelte-hero-icons';
	import type { NotebookNote } from '$lib/types/structured-response';

	interface Props {
		notes: NotebookNote[];
		isExpanded?: boolean;
	}

	let { notes, isExpanded = false }: Props = $props();

	let expanded = $state(isExpanded);

	// Format category name for display
	function formatCategoryName(category: string): string {
		return category.split('_').map(word =>
			word.charAt(0).toUpperCase() + word.slice(1)
		).join(' ');
	}

	// Get category color
	function getCategoryColor(category: string): string {
		const colors: Record<string, string> = {
			'code_architecture': 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300',
			'implementation_details': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
			'user_preferences': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
			'technical_constraints': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300',
			'business_logic': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300',
			'integration_points': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300'
		};
		return colors[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
	}
</script>

<div class="border border-slate-200 dark:border-slate-800 rounded-lg bg-slate-50 dark:bg-slate-950/30 p-4">
	<!-- Header -->
	<button
		class="w-full flex items-center gap-3 text-left"
		onclick={() => expanded = !expanded}
	>
		<div class="flex-shrink-0">
			<Icon src={BookOpen} class="w-5 h-5 text-slate-600 dark:text-slate-400" />
		</div>
		<div class="flex-1">
			<div class="text-sm font-medium text-slate-900 dark:text-slate-100">
				Added {notes.length} finding{notes.length === 1 ? '' : 's'} to notebook
			</div>
			<div class="text-xs text-slate-700 dark:text-slate-300 mt-1">
				{#if expanded}
					Click to collapse
				{:else}
					Click to view findings
				{/if}
			</div>
		</div>
		<div class="flex-shrink-0">
			<Icon
				src={PencilSquare}
				class="w-4 h-4 text-slate-500 dark:text-slate-400 {expanded ? 'rotate-180' : ''} transition-transform duration-200"
			/>
		</div>
	</button>

	<!-- Expanded Content -->
	{#if expanded}
		<div class="mt-4 space-y-3 border-t border-slate-200 dark:border-slate-800 pt-4">
			{#each notes as note}
				<div class="flex items-start gap-3">
					<div class="flex-shrink-0 mt-1">
						<div class="w-2 h-2 rounded-full bg-slate-500"></div>
					</div>
					<div class="flex-1 min-w-0">
						<!-- Category Badge -->
						<div class="mb-2">
							<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium {getCategoryColor(note.category)}">
								{formatCategoryName(note.category)}
							</span>
						</div>

						<!-- Note Content -->
						<div class="text-sm text-gray-900 dark:text-gray-100">
							{note.content}
						</div>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
