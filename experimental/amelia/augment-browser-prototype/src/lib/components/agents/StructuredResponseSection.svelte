<script lang="ts">
	import {
		Icon,
		LightBulb,
		MagnifyingGlass,
		Cog6Tooth,
		BookmarkSquare,
		CheckCircle,
		ArrowRight,
		ExclamationTriangle,
		ChevronDown,
		ChevronRight,
		ListBullet,
		QuestionMarkCircle,
		ChatBubbleLeftRight,
		Check,
		ChatBubbleLeftEllipsis,
		DocumentMagnifyingGlass,
		Play
	} from 'svelte-hero-icons';
	import type {
		StructuredResponseSection,
		StructuredSectionData
	} from '$lib/types/structured-response';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	interface Props {
		section: StructuredResponseSection;
		sectionData?: StructuredSectionData; // Enhanced data with raw text and ending summary
		isExpanded?: boolean;
	}

	let { section, sectionData, isExpanded = false }: Props = $props();

	// Local state for expansion (overrides prop if user interacts)
	let localExpanded = $state(isExpanded);

	const isExpandable = $derived(() => {
		// Plan sections are never expandable - always show all steps
		if (section.type === 'plan') return false;

		return sectionData?.endingSummary !== sectionData?.content;
	});

	// Check if content actually looks like a list (has bullet points, numbers, etc.)
	function isActualList(content: string): boolean {
		const lines = content
			.split('\n')
			.map((line) => line.trim())
			.filter((line) => line.length > 0);

		// If there's only one line, it's not a list
		if (lines.length <= 1) return false;

		// Count how many lines start with list markers
		const listMarkerCount = lines.filter((line) =>
			/^(\d+\.?\s*|-\s*|\*\s*|\+\s*|•\s*)/.test(line)
		).length;

		// If more than half the lines have list markers, treat as a list
		return listMarkerCount > lines.length / 2;
	}

	// Parse content into list items for sections that should be displayed as lists
	function parseListItems(content: string): Array<{ text: string; level: number }> {
		return content
			.split('\n')
			.map((line) => {
				// Count leading spaces/tabs to determine nesting level
				const match = line.match(/^(\s*)/);
				const indentLevel = match ? Math.floor(match[1].length / 4) : 0; // 4 spaces = 1 level

				const trimmedLine = line.trim();
				if (trimmedLine.length === 0) return null;

				// Remove leading numbers, bullets, or dashes
				const text = trimmedLine.replace(/^(\d+\.?\s*|-\s*|\*\s*|\+\s*|•\s*)/, '').trim();

				return text.length > 0 ? { text, level: indentLevel } : null;
			})
			.filter((item) => item !== null) as Array<{ text: string; level: number }>;
	}

	// Check if this section should be rendered as a list
	const isListSection = $derived(() => {
		const potentialListTypes = ['plan', 'noting', 'user_responses'];
		return potentialListTypes.includes(section.type) && isActualList(section.content);
	});

	// Create a truncated version of content for collapsed view
	const truncatedContent = $derived(() => {
		if (!section.content) return '';

		// For very long content, truncate to a reasonable length
		const maxLength = 200;
		if (section.content.length <= maxLength) {
			return section.content;
		}

		// Find a good breaking point (end of sentence or paragraph)
		const truncated = section.content.substring(0, maxLength);
		const lastSentence = truncated.lastIndexOf('.');
		const lastNewline = truncated.lastIndexOf('\n');

		// Use the last sentence or newline as breaking point, otherwise just truncate
		const breakPoint = Math.max(lastSentence, lastNewline);
		if (breakPoint > maxLength * 0.5) {
			// Only use if it's not too short
			return section.content.substring(0, breakPoint + 1);
		}

		return truncated + '...';
	});

	// Get icon and styling for each section type
	const sectionConfig = $derived(() => {
		switch (section.type) {
			case 'thinking':
				return {
					icon: ChatBubbleLeftEllipsis,
					title: 'Thinking',
					bgColor: 'bg-blue-50 dark:bg-blue-900/20',
					borderColor: 'border-slate-200 dark:border-slate-800',
					iconColor: 'text-slate-400 dark:text-slate-400',
					titleColor: 'text-slate-900 dark:text-slate-100',
					wrapperClasses: ''
				};
			case 'researching':
				return {
					icon: DocumentMagnifyingGlass,
					title: 'Researching',
					bgColor: 'bg-purple-50 dark:bg-purple-900/20',
					borderColor: 'border-purple-200 dark:border-purple-800',
					iconColor: 'text-purple-600 dark:text-purple-400',
					titleColor: 'text-purple-900 dark:text-purple-100',
					wrapperClasses: ''
				};
			case 'doing':
				return {
					icon: Play,
					title: 'Doing',
					bgColor: 'bg-green-50 dark:bg-green-900/20',
					borderColor: 'border-green-200 dark:border-green-800',
					iconColor: 'text-green-600 dark:text-green-400',
					titleColor: 'text-green-900 dark:text-green-100',
					wrapperClasses: ''
				};
			case 'noting':
				return {
					icon: BookmarkSquare,
					title: 'Noting',
					bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
					borderColor: 'border-yellow-200 dark:border-yellow-800',
					iconColor: 'text-yellow-600 dark:text-yellow-400',
					titleColor: 'text-yellow-900 dark:text-yellow-100',
					wrapperClasses:
						'py-3 px-4 bg-white shadow rounded border border-slate-100 dark:border-slate-700'
				};
			case 'summary':
				return {
					icon: CheckCircle,
					title: 'Summary',
					bgColor: 'bg-gray-50 dark:bg-gray-800/50',
					borderColor: 'border-gray-200 dark:border-gray-700',
					iconColor: 'text-gray-600 dark:text-gray-400',
					titleColor: 'text-gray-900 dark:text-gray-100',
					wrapperClasses: ''
				};
			case 'plan':
				return {
					icon: ListBullet,
					title: 'Plan',
					bgColor: 'bg-orange-50 dark:bg-orange-900/20',
					borderColor: 'border-orange-200 dark:border-orange-800',
					iconColor: 'text-orange-600 dark:text-orange-400',
					titleColor: 'text-orange-900 dark:text-orange-100',
					wrapperClasses: ''
				};
			case 'user_responses':
				return {
					icon: ArrowRight,
					title: 'Next Steps',
					bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
					borderColor: 'border-indigo-200 dark:border-indigo-800',
					iconColor: 'text-indigo-600 dark:text-indigo-400',
					titleColor: 'text-indigo-900 dark:text-indigo-100',
					wrapperClasses: ''
				};

			case 'question_responses':
				return {
					icon: QuestionMarkCircle,
					title: 'Question Responses',
					bgColor: 'bg-indigo-50 dark:bg-indigo-900/20',
					borderColor: 'border-indigo-200 dark:border-indigo-800',
					iconColor: 'text-indigo-600 dark:text-indigo-400',
					titleColor: 'text-indigo-900 dark:text-indigo-100',
					wrapperClasses: ''
				};

			case 'user_request':
				return {
					icon: ChatBubbleLeftRight,
					title: 'User Request',
					bgColor: 'bg-teal-50 dark:bg-teal-900/20',
					borderColor: 'border-teal-200 dark:border-teal-800',
					iconColor: 'text-teal-600 dark:text-teal-400',
					titleColor: 'text-teal-900 dark:text-teal-100',
					wrapperClasses: ''
				};

			case 'error':
				return {
					icon: ExclamationTriangle,
					title: 'Error',
					bgColor: 'bg-red-50 dark:bg-red-900/20',
					borderColor: 'border-red-200 dark:border-red-800',
					iconColor: 'text-red-600 dark:text-red-400',
					titleColor: 'text-red-900 dark:text-red-100',
					wrapperClasses: ''
				};
			case 'warning':
				return {
					icon: ExclamationTriangle,
					title: 'Warning',
					bgColor: 'bg-amber-50 dark:bg-amber-900/20',
					borderColor: 'border-amber-200 dark:border-amber-800',
					iconColor: 'text-amber-600 dark:text-amber-400',
					titleColor: 'text-amber-900 dark:text-amber-100',
					wrapperClasses: 'text-amber-600 dark:text-amber-400'
				};
			case 'status':
				// Dynamic styling based on status content
				const isDone = section.content.trim().toLowerCase() === 'done';
				return {
					icon: isDone ? CheckCircle : QuestionMarkCircle,
					title: isDone ? 'Ready for PR' : 'Waiting for Input',
					bgColor: isDone
						? 'bg-green-50 dark:bg-green-900/20'
						: 'bg-yellow-50 dark:bg-yellow-900/20',
					borderColor: isDone
						? 'border-green-200 dark:border-green-800'
						: 'border-yellow-200 dark:border-yellow-800',
					iconColor: isDone
						? 'text-green-600 dark:text-green-400'
						: 'text-yellow-600 dark:text-yellow-400',
					titleColor: isDone
						? 'text-green-900 dark:text-green-100'
						: 'text-yellow-900 dark:text-yellow-100',
					wrapperClasses: ''
				};
			default:
				return {
					icon: CheckCircle,
					title: 'Response',
					bgColor: 'bg-gray-50 dark:bg-gray-800/50',
					borderColor: 'border-gray-200 dark:border-gray-700',
					iconColor: 'text-gray-600 dark:text-gray-400',
					titleColor: 'text-gray-900 dark:text-gray-100',
					wrapperClasses: ''
				};
		}
	});
</script>

<div class="py-2">
	<!-- Header - clicking row expands, clicking chevron collapses -->
	<div class="flex items-start gap-3">
		<div class="mt-1 flex-shrink-0">
			<Icon src={sectionConfig().icon} class="w-3.5 {sectionConfig().iconColor}" micro />
		</div>
		{#if section.type === 'plan'}
			{@const listItems = parseListItems(section.content)}
			<div class="min-w-0 flex-1">
				<!-- Plan sections always show all steps with bullets and proper nesting -->
				<div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
					{#each listItems as item}
						<div class="flex items-start gap-2" style="margin-left: {item.level * 16}px;">
							<div
								class="mt-1 h-[1.1em] w-[1.1em] flex-shrink-0 rounded border border-slate-300 bg-white dark:border-slate-700 dark:bg-gray-900"
							></div>

							<span class="flex-1">
								<Markdown content={item.text} size="sm" hasNoPadding />
							</span>
						</div>
					{/each}
				</div>
			</div>
		{:else if section.type === 'question_responses'}
			{@const responseLines = section.content.split('\n').filter((line) => line.trim())}
			<div class="min-w-0 flex-1">
				<!-- Question responses displayed as key-value pairs -->
				<div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
					{#each responseLines as line}
						{@const match = line.match(/^\*\*([^*]+):\*\*\s*(.+)$/)}
						{#if match}
							{@const [, question, response] = match}
							<div class="flex items-start gap-3">
								<span class="flex-shrink-0 font-medium text-indigo-600 dark:text-indigo-400">
									{question.trim()}:
								</span>
								<span class="flex-1 text-gray-700 dark:text-gray-300">
									{response.trim()}
								</span>
							</div>
						{:else}
							<div class="">
								<Markdown content={line} size="sm" hasNoPadding />
							</div>
						{/if}
					{/each}
				</div>
			</div>
		{:else}
			<div
				class="min-w-0 flex-1 cursor-pointer {sectionConfig().wrapperClasses}"
				role="button"
				tabindex="0"
				onclick={() => {
					if (!localExpanded) localExpanded = true;
				}}
				onkeydown={(e) => {
					if ((e.key === 'Enter' || e.key === ' ') && !localExpanded) {
						e.preventDefault();
						localExpanded = true;
					}
				}}
				aria-label="Expand section"
			>
				{#if !localExpanded}
					<!-- Collapsed view - show ending summary if available, otherwise show truncated content -->
					{#if sectionData?.endingSummary}
						<div class="text-sm">
							<Markdown content={sectionData.endingSummary} size="sm" lineClamp={3} hasNoPadding />
						</div>
					{:else if isListSection()}
						<!-- Show first few list items for collapsed list sections -->
						{@const listItems = parseListItems(section.content)}
						<div class="text-sm">
							{#each listItems.slice(0, 2) as item}
								<div class="mb-1 flex items-start gap-2" style="margin-left: {item.level * 16}px;">
									<span class="-mt-0.5 text-gray-400 dark:text-gray-500">•</span>
									<span class="line-clamp-1">
										<Markdown content={item.text} size="xs" lineClamp={1} hasNoPadding />
									</span>
								</div>
							{/each}
							{#if listItems.length > 2}
								<span class="text-xs text-gray-400 dark:text-gray-500"
									>+ {listItems.length - 2} more {section.type === 'noting'
										? 'notes'
										: 'items'}</span
								>
							{/if}
						</div>
					{:else}
						<div class="">
							<Markdown content={truncatedContent()} size="sm" lineClamp={3} hasNoPadding />
						</div>
					{/if}
				{:else}
					<!-- Expanded view - show full content -->
					{#if isListSection()}
						<!-- Render as structured list -->
						{@const listItems = parseListItems(section.content)}
						<div class="space-y-2 text-sm">
							{#each listItems as item}
								<div class="flex items-start gap-3" style="margin-left: {item.level * 16}px;">
									<!-- Use bullet points for non-plan list sections -->
									<span class="mt-1 flex-shrink-0 text-gray-400 dark:text-gray-500">•</span>
									<span class="flex-1">
										<Markdown content={item.text} size="sm" hasNoPadding />
									</span>
								</div>
							{/each}
						</div>
					{:else}
						<!-- Render as markdown for non-list sections -->
						<div class="">
							<Markdown content={section.content} size="sm" hasNoPadding />
						</div>
					{/if}
				{/if}
			</div>
		{/if}
		<!-- Clickable chevron button - only collapses when expanded -->
		{#if isExpandable()}
			<div class="-my-1 mr-4 flex-shrink-0">
				<button
					class="rounded p-1 transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-gray-700"
					onclick={() => {
						localExpanded = !localExpanded;
					}}
					aria-label={localExpanded ? 'Collapse section' : 'Expand section'}
				>
					<Icon
						src={localExpanded ? ChevronDown : ChevronRight}
						class="h-4 w-4 text-gray-400 transition-transform duration-200 dark:text-gray-500"
						micro
					/>
				</button>
			</div>
		{/if}
	</div>
</div>
