<script lang="ts">
	import { onDestroy, tick } from 'svelte';
	import { MonacoContext } from '$lib/components/MonacoProvider';
	import { theme } from '$lib/stores/theme';
	import { fade } from 'svelte/transition';
	import { CursorArrowRipple, Icon } from 'svelte-hero-icons';

	interface Props {
		fileName: string | undefined;
		oldContent?: string;
		newContent?: string;
		height?: string;
		autoFocus?: boolean;
		doShowHeader?: boolean;
		class?: string;
		enableFocusToScroll?: boolean;
	}

	let {
		fileName,
		oldContent = '',
		newContent = '',
		height,
		autoFocus = false,
		doShowHeader,
		class: className = '',
		enableFocusToScroll = false
	}: Props = $props();

	let containerElement: HTMLDivElement;
	let editor: any | null = null;
	let focused = $state(false);
	let hovered = $state(false);
	let showScrollHint = $state(false);
	let scrollDelta = $state(0);

	// Get the Monaco context
	const monacoContext = MonacoContext.getContext();
	const monaco = monacoContext.monaco;

	// Scroll hint threshold (pixels)
	const SCROLL_THRESHOLD = 120;

	// Handle scroll hint logic
	function handleScrollHint() {
		// Reset scroll hint when focused, not hovered, or feature disabled
		if (focused || !hovered || !enableFocusToScroll) {
			showScrollHint = false;
			scrollDelta = 0;
			return;
		}
	}

	// Handle mouse enter
	function handleMouseEnter() {
		hovered = true;
	}

	// Handle mouse leave
	function handleMouseLeave() {
		hovered = false;
		showScrollHint = false;
		scrollDelta = 0;
	}

	// Handle wheel events when not focused
	function handleWheel(event: WheelEvent) {
		if (!hovered || focused || !enableFocusToScroll) return;

		// Accumulate scroll delta
		scrollDelta += Math.abs(event.deltaY);

		// Show hint if threshold reached
		if (scrollDelta >= SCROLL_THRESHOLD && !showScrollHint) {
			showScrollHint = true;
		}
	}

	// Handle click to focus
	function handleClickToFocus() {
		if (editor) {
			const modifiedEditor = editor.getModifiedEditor();
			if (modifiedEditor) {
				modifiedEditor.focus();
			}
		}
		showScrollHint = false;
		scrollDelta = 0;
	}

	// Watch for focus/hover changes to update scroll hint
	$effect(() => {
		handleScrollHint();
	});

	// Function to update the diff editor height based on content
	function updateDiffEditorHeight() {
		if (!editor || !containerElement) return;

		if (height !== undefined) {
			editor.layout();
			return;
		}

		// For diff editors with collapsed regions, we need to calculate based on visible content
		// Use requestAnimationFrame to ensure DOM is ready, then calculate height
		requestAnimationFrame(async () => {
			try {
				const modifiedEditor = editor.getModifiedEditor();
				if (modifiedEditor) {
					// Get the actual rendered height of visible content
					const contentHeight = modifiedEditor.getContentHeight();

					// Get the number of visible lines (accounting for collapsed regions)
					const visibleRanges = modifiedEditor.getVisibleRanges();
					const totalVisibleLines = visibleRanges.reduce((total: number, range: any) => {
						return total + (range.endLineNumber - range.startLineNumber + 1);
					}, 0);

					// Calculate height based on visible lines with a more conservative approach
					const lineHeight = $monaco
						? modifiedEditor.getOption($monaco.editor.EditorOption.lineHeight)
						: 18;
					const calculatedHeight = Math.max(totalVisibleLines * lineHeight, contentHeight);

					// Use more conservative bounds - max 400px instead of 600px
					// Minimum 120px to show at least a few lines
					const finalHeight = Math.max(120, Math.min(400, calculatedHeight));

					height = `${finalHeight}px`;
					await tick();
					editor.layout();
				}
			} catch (error) {
				console.warn('Failed to calculate diff height, using default:', error);
				// Use smaller default height (250px instead of 300px)
				height = '250px';
				editor.layout();
			}
		});
	}

	// Get language from file extension
	function getLanguageFromFileName(fileName: string | undefined): string {
		if (!fileName) return 'plaintext';
		const extension = fileName.split('.').pop()?.toLowerCase();

		switch (extension) {
			case 'js':
			case 'jsx':
				return 'javascript';
			case 'ts':
			case 'tsx':
				return 'typescript';
			case 'svelte':
				return 'svelte';
			case 'html':
				return 'html';
			case 'css':
				return 'css';
			case 'scss':
			case 'sass':
				return 'scss';
			case 'json':
				return 'json';
			case 'md':
			case 'markdown':
				return 'markdown';
			case 'py':
				return 'python';
			case 'rs':
				return 'rust';
			case 'go':
				return 'go';
			case 'java':
				return 'java';
			case 'c':
				return 'c';
			case 'cpp':
			case 'cc':
			case 'cxx':
				return 'cpp';
			case 'sh':
			case 'bash':
				return 'shell';
			case 'sql':
				return 'sql';
			case 'xml':
				return 'xml';
			case 'yaml':
			case 'yml':
				return 'yaml';
			default:
				return 'plaintext';
		}
	}

	// Initialize editor when Monaco is loaded
	$effect(() => {
		if ($monaco && containerElement && !editor) {
			// Configure Monaco environment to disable workers
			// if (typeof window !== 'undefined') {
			// 	(window as any).MonacoEnvironment = {
			// 		getWorker: function (_workerId: string, _label: string) {
			// 			// Return a minimal worker implementation to avoid web worker issues
			// 			return new Worker(
			// 				URL.createObjectURL(
			// 					new Blob(['self.postMessage({});'], { type: 'application/javascript' })
			// 				)
			// 			);
			// 		}
			// 	};
			// }

			// Configure Monaco themes
			$monaco.editor.defineTheme('augment-dark', {
				base: 'vs-dark',
				inherit: true,
				rules: [
					{ token: 'comment', foreground: '6b7280', fontStyle: 'italic' },
					{ token: 'keyword', foreground: '8b5cf6' },
					{ token: 'string', foreground: '10b981' },
					{ token: 'number', foreground: 'f59e0b' },
					{ token: 'type', foreground: '06b6d4' },
					{ token: 'function', foreground: '3b82f6' }
				],
				colors: {
					'editor.background': '#0f172a', // slate-900
					'editor.foreground': '#f1f5f9', // slate-100
					'editor.lineHighlightBackground': '#1e293b', // slate-800
					'editor.selectionBackground': '#334155', // slate-700
					'editorLineNumber.foreground': '#64748b', // slate-500
					'editorLineNumber.activeForeground': '#cbd5e1', // slate-300
					'diffEditor.insertedTextBackground': '#10b98125', // emerald with subtle opacity
					'diffEditor.removedTextBackground': '#ef444425', // red with subtle opacity
					'diffEditor.insertedLineBackground': '#10b98115', // emerald line background
					'diffEditor.removedLineBackground': '#ef444415', // red line background
					'diffEditorGutter.insertedLineBackground': '#10b98130', // emerald gutter
					'diffEditorGutter.removedLineBackground': '#ef444430', // red gutter
					'diffEditor.border': '#374151', // slate-700 for split view border
					'diffEditorOverview.insertedForeground': '#10b981', // emerald for overview ruler
					'diffEditorOverview.removedForeground': '#ef4444', // red for overview ruler
					'scrollbarSlider.background': '#475569',
					'scrollbarSlider.hoverBackground': '#64748b',
					'scrollbarSlider.activeBackground': '#94a3b8'
				}
			});

			$monaco.editor.defineTheme('augment-light', {
				base: 'vs',
				inherit: true,
				rules: [
					{ token: 'comment', foreground: '6b7280', fontStyle: 'italic' },
					{ token: 'keyword', foreground: '7c3aed' },
					{ token: 'string', foreground: '059669' },
					{ token: 'number', foreground: 'd97706' },
					{ token: 'type', foreground: '0891b2' },
					{ token: 'function', foreground: '2563eb' }
				],
				colors: {
					'editor.background': '#ffffff',
					'editor.foreground': '#0f172a', // slate-900
					'editor.lineHighlightBackground': '#f8fafc', // slate-50
					'editor.selectionBackground': '#e2e8f0', // slate-200
					'editorLineNumber.foreground': '#94a3b8', // slate-400
					'editorLineNumber.activeForeground': '#475569', // slate-600
					'diffEditor.insertedTextBackground': '#10b98125', // emerald with subtle opacity
					'diffEditor.removedTextBackground': '#ef444425', // red with subtle opacity
					'diffEditor.insertedLineBackground': '#10b98115', // emerald line background
					'diffEditor.removedLineBackground': '#ef444415', // red line background
					'diffEditorGutter.insertedLineBackground': '#10b98130', // emerald gutter
					'diffEditorGutter.removedLineBackground': '#ef444430', // red gutter
					'diffEditor.border': '#e2e8f0', // slate-300 for split view border
					'diffEditorOverview.insertedForeground': '#10b981', // emerald for overview ruler
					'diffEditorOverview.removedForeground': '#ef4444', // red for overview ruler
					'scrollbarSlider.background': '#cbd5e1',
					'scrollbarSlider.hoverBackground': '#94a3b8',
					'scrollbarSlider.activeBackground': '#64748b'
				}
			});

			// Create the diff editor
			editor = $monaco.editor.createDiffEditor(containerElement, {
				theme: $theme === 'dark' ? 'augment-dark' : 'augment-light',
				readOnly: true,
				automaticLayout: true,
				renderSideBySide: false,
				useInlineViewWhenSpaceIsLimited: true,
				enableSplitViewResizing: true,
				stickyScroll: { enabled: true },
				scrollBeyondLastLine: false,
				minimap: { enabled: false },
				renderOverviewRuler: false,
				renderGutterMenu: false,
				// Prevent scrolling when not focused (only if enableFocusToScroll is true)
				scrollbar: {
					alwaysConsumeMouseWheel: enableFocusToScroll ? focused : true,
					handleMouseWheel: enableFocusToScroll ? focused : true,
					vertical: 'auto',
					horizontal: 'auto',
					verticalScrollbarSize: 8,
					horizontalScrollbarSize: 8
				},
				hideUnchangedRegions: {
					enabled: true,
					revealLineCount: 3,
					minimumLineCount: 3,
					contextLineCount: 3
				},
				// Experimental options to improve diff computation
				experimental: {
					showMoves: true
				},
				fontSize: 11.5,
				lineHeight: 18,
				padding: { top: 6, bottom: 6 }
			});

			// Add focus/blur event listeners to both editors
			const originalEditor = editor.getOriginalEditor();
			const modifiedEditor = editor.getModifiedEditor();

			const handleFocus = () => {
				focused = true;
				// Update scrollbar options when focused (only if enableFocusToScroll is true)
				if (editor && enableFocusToScroll) {
					editor.updateOptions({
						scrollbar: {
							alwaysConsumeMouseWheel: true,
							handleMouseWheel: true,
							vertical: 'auto',
							horizontal: 'auto',
							verticalScrollbarSize: 8,
							horizontalScrollbarSize: 8
						}
					});
				}
			};

			const handleBlur = () => {
				focused = false;
				// Update scrollbar options when not focused (only if enableFocusToScroll is true)
				if (editor && enableFocusToScroll) {
					editor.updateOptions({
						scrollbar: {
							alwaysConsumeMouseWheel: false,
							handleMouseWheel: false,
							vertical: 'auto',
							horizontal: 'auto',
							verticalScrollbarSize: 8,
							horizontalScrollbarSize: 8
						}
					});
				}
			};

			// Add listeners to both editors
			if (originalEditor) {
				originalEditor.onDidFocusEditorText(handleFocus);
				originalEditor.onDidBlurEditorText(handleBlur);
			}
			if (modifiedEditor) {
				modifiedEditor.onDidFocusEditorText(handleFocus);
				modifiedEditor.onDidBlurEditorText(handleBlur);
			}

			// Listen for theme changes
			const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
			const handleThemeChange = (e: MediaQueryListEvent) => {
				if (editor && $monaco) {
					$monaco.editor.setTheme(e.matches ? 'augment-dark' : 'augment-light');
				}
			};
			mediaQuery.addEventListener('change', handleThemeChange);

			// Initial height update
			updateDiffEditorHeight();

			if (autoFocus) {
				setTimeout(() => {
					if (modifiedEditor) {
						modifiedEditor.focus();
					}
				}, 100);
			}

			// Return cleanup function
			return () => {
				mediaQuery.removeEventListener('change', handleThemeChange);
			};
		}
	});

	// Update editor content when props change
	let currentModels: { original: any; modified: any } | null = null;

	$effect(() => {
		if (editor && $monaco) {
			// Dispose previous models properly
			if (currentModels) {
				try {
					// Clear the diff editor model first to prevent disposal errors
					editor.setModel(null);
					// Small delay to ensure the editor has processed the model clearing
					setTimeout(() => {
						if (currentModels) {
							try {
								// Check if models are not already disposed before disposing
								if (!currentModels.original.isDisposed()) {
									currentModels.original.dispose();
								}
								if (!currentModels.modified.isDisposed()) {
									currentModels.modified.dispose();
								}
							} catch (e) {
								console.warn('Error disposing previous models:', e);
							}
						}
					}, 10);
				} catch (e) {
					console.warn('Error clearing editor model:', e);
				}
			}

			const language = getLanguageFromFileName(fileName);

			// Create new models with unique URIs to help Monaco compute diffs
			// Use a timestamp to ensure unique URIs for each diff computation
			const timestamp = Date.now();
			const baseFileName = fileName || 'file';
			const originalUri = $monaco.Uri.parse(
				`inmemory://diff-${timestamp}/original/${baseFileName}`
			);
			const modifiedUri = $monaco.Uri.parse(
				`inmemory://diff-${timestamp}/modified/${baseFileName}`
			);

			const originalModel = $monaco.editor.createModel(oldContent, language, originalUri);
			const modifiedModel = $monaco.editor.createModel(newContent, language, modifiedUri);

			// Set the new models
			editor.setModel({
				original: originalModel,
				modified: modifiedModel
			});

			// Hide line numbers for the original editor
			const originalEditor = editor.getOriginalEditor();
			if (originalEditor) {
				originalEditor.updateOptions({ lineNumbers: 'off' });
			}

			// Listen for content changes to update height
			const modifiedEditor = editor.getModifiedEditor();
			if (modifiedEditor) {
				modifiedEditor.onDidContentSizeChange(() => {
					updateDiffEditorHeight();
				});
			}

			// Force diff computation to ensure colors show up
			// This is especially important when using direct oldContent/newContent
			// setTimeout(() => {
			// 	if (editor) {
			// 		// Force diff recomputation by updating options
			// 		editor.updateOptions({
			// 			ignoreTrimWhitespace: false,
			// 			renderIndicators: true,
			// 			diffAlgorithm: 'advanced',
			// 		});

			// 		// Trigger a layout to ensure diff computation
			// 		editor.layout();
			// 	}
			// }, 100);

			// Store references for cleanup
			currentModels = { original: originalModel, modified: modifiedModel };

			// Update height after setting new content
			setTimeout(() => {
				updateDiffEditorHeight();
			}, 100);

			// Return cleanup function
			return () => {
				if (currentModels && editor) {
					try {
						// Clear the diff editor model first
						editor.setModel(null);
						// Then dispose the individual models if not already disposed
						if (!currentModels.original.isDisposed()) {
							currentModels.original.dispose();
						}
						if (!currentModels.modified.isDisposed()) {
							currentModels.modified.dispose();
						}
					} catch (e) {
						console.warn('Error disposing models in cleanup:', e);
					}
					currentModels = null;
				}
			};
		}
	});

	onDestroy(() => {
		// Dispose editor and models in proper order
		if (editor) {
			try {
				// Clear the diff editor model first to prevent disposal errors
				editor.setModel(null);
				// Then dispose the editor
				editor.dispose();
			} catch (e) {
				console.warn('Error disposing editor:', e);
			}
		}

		// Dispose current models after editor is disposed
		if (currentModels) {
			try {
				// Check if models are not already disposed before disposing
				if (!currentModels.original.isDisposed()) {
					currentModels.original.dispose();
				}
				if (!currentModels.modified.isDisposed()) {
					currentModels.modified.dispose();
				}
			} catch (e) {
				console.warn('Error disposing models in onDestroy:', e);
			}
			currentModels = null;
		}
	});
</script>

<div
	class="h-full w-full overflow-hidden rounded-lg border border-slate-200 dark:border-slate-700 {className} relative"
	role="region"
	aria-label="Code diff viewer"
	onmouseenter={handleMouseEnter}
	onmouseleave={handleMouseLeave}
	onwheel={handleWheel}
>
	{#if doShowHeader}
		<!-- Header -->
		<div
			class="border-b border-slate-200 bg-slate-50 px-4 py-3 dark:border-slate-700 dark:bg-slate-800"
		>
			<div class="flex items-center gap-3">
				<div class="h-2 w-2 rounded-full bg-blue-500"></div>
				<span class="text-sm font-medium text-slate-800 dark:text-slate-200">
					{fileName || 'Unknown file'}
				</span>
				<!-- <span class="text-xs text-slate-500 dark:text-slate-400 bg-slate-200 dark:bg-slate-700 px-2 py-1 rounded-full">
				diff
			</span> -->
			</div>
		</div>
	{/if}

	<!-- Monaco diff editor container -->
	<div
		bind:this={containerElement}
		class="w-full bg-white dark:bg-slate-900"
		style="height: {height}; min-height: 120px;"
	></div>

	<!-- Scroll hint overlay -->
	{#if showScrollHint}
		<div
			class="absolute inset-0 flex cursor-pointer items-center justify-center rounded-lg bg-white/20 backdrop-blur-sm transition-opacity duration-200 dark:bg-black/20"
			onclick={handleClickToFocus}
			role="button"
			tabindex="0"
			aria-label="Click to focus and scroll"
			transition:fade={{ duration: 300 }}
			onkeydown={(e) => e.key === 'Enter' && handleClickToFocus()}
		>
			<div
				class="flex items-center gap-2 rounded-lg border border-slate-200 bg-white px-4 py-3 text-sm font-medium text-slate-700 shadow-lg dark:border-slate-600 dark:bg-slate-800 dark:text-slate-300"
			>
				<Icon src={CursorArrowRipple} class="h-4 w-4" micro />
				Click to focus and scroll
			</div>
		</div>
	{/if}
</div>
