<script lang="ts">
	import { ChatResultNodeType } from '$lib/api/unified-client';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import { extractOriginalMessageFromInitial } from '$lib/types/structured-response';
	import { getRelativeTimeForStr } from '$lib/utils/time';
	import ToolCallDisplay from './ToolCallDisplay.svelte';

	interface Props {
		group: {
			type: 'conversation' | 'agent-only';
			userMessage: any | null;
			agentResponses: any[];
			timestamp: string;
			isLastGroup: boolean;
		};
		agent?: CleanRemoteAgent | null;
		isStreaming?: boolean;
		isLastMessage?: boolean;
	}

	let { group, agent = null, isStreaming = false, isLastMessage = false }: Props = $props();

	// Clean user message by removing instructions and entity type prefixes
	let cleanedUserMessage = $derived.by(() => {
		if (!group.userMessage?.request_message) return '';

		let message = group.userMessage.request_message;

		// Use the proper function to extract original message from initial message
		let cleanedMessage = extractOriginalMessageFromInitial(message);

		// Also remove any "Entity Type:" prefixes that might be added
		cleanedMessage = cleanedMessage.replace(/^Entity Type:\s*[^\n]*\n*/i, '');

		// Trim and return
		return cleanedMessage.trim() || message;
	});

	// Extract tool calls from agent responses
	let toolCalls = $derived.by(() => {
		if (!group.agentResponses || group.agentResponses.length === 0) return [];

		const allToolCalls: any[] = [];

		group.agentResponses.forEach((response) => {
			if (response.exchange?.response_nodes) {
				const toolCallNodes = response.exchange.response_nodes.filter(
					(node: any) => node.type === ChatResultNodeType.TOOL_USE && node.tool_use
				);
				const toolResultNodes = response.exchange.response_nodes.filter(
					(node: any) => node.type === 1 && node.tool_result // TOOL_RESULT = 1
				);

				toolCallNodes.forEach((toolCall: any) => {
					const matchingResult = toolResultNodes.find(
						(result: any) => result.tool_result?.tool_use_id === toolCall.tool_use?.tool_use_id
					);
					allToolCalls.push({
						toolCall,
						toolResult: matchingResult
					});
				});
			}
		});

		return allToolCalls;
	});

	// Extract simple text content from agent responses
	let agentContent = $derived.by(() => {
		if (!group.agentResponses || group.agentResponses.length === 0) return '';

		// Look through all responses to find content, starting from the most recent
		for (let i = group.agentResponses.length - 1; i >= 0; i--) {
			const response = group.agentResponses[i];

			// Prioritize agent's actual response content
			if (response.exchange?.response_text && response.exchange.response_text.trim()) {
				return response.exchange.response_text;
			}

			// Check for turn_summary (agent's summary of what they did)
			if (response.turn_summary && response.turn_summary.trim()) {
				return response.turn_summary;
			}

			// Fallback to any text content we can find
			if (response.response_text && response.response_text.trim()) {
				return response.response_text;
			}
		}

		// If no agent response found, check if there are file changes
		const totalChangedFiles = group.agentResponses.reduce((total, response) => {
			return total + (response.changed_files?.length || 0);
		}, 0);

		if (totalChangedFiles > 0) {
			return `Made ${totalChangedFiles} file change${totalChangedFiles === 1 ? '' : 's'} but provided no text response.`;
		}

		// Check if any exchange exists
		const hasExchange = group.agentResponses.some((response) => response.exchange);
		if (hasExchange) {
			return 'Exchange completed';
		}

		return 'Working...';
	});

	// For streaming: show "Working..." or last partial content
	let streamingContent = $derived.by(() => {
		if (!isStreaming || !isLastMessage) return null;

		if (agentContent && agentContent.length > 0) {
			// Show last sentence or partial content
			const sentences = agentContent.split(/[.!?]+/).filter((s: string) => s.trim().length > 0);
			if (sentences.length > 0) {
				const lastSentence = sentences[sentences.length - 1].trim();
				return lastSentence + '...';
			}
		}

		return 'Working...';
	});
</script>

<div class="space-y-4">
	<!-- User Message -->
	<!-- {#if group.userMessage?.request_message}
		<div class="flex items-start gap-4">
			<div class="w-8 h-8 rounded-lg bg-blue-50 dark:bg-blue-950/50 border border-blue-200/30 dark:border-blue-800/30 flex items-center justify-center flex-shrink-0">
				<Icon src={User} class="w-4 h-4 text-blue-600 dark:text-blue-400" />
			</div>
			<div class="flex-1 min-w-0">
				<div class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
					You
				</div>
				<div class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap leading-relaxed bg-gray-50/50 dark:bg-gray-900/50 rounded-lg p-3">
					{cleanedUserMessage}
				</div>
			</div>
		</div>
	{/if} -->

	<!-- Agent Response -->
	<div class="flex items-start gap-4">
		<!-- <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-50 to-blue-50 dark:from-purple-950/50 dark:to-blue-950/50 border border-purple-200/30 dark:border-purple-800/30 flex items-center justify-center flex-shrink-0">
			<LogoMark width={14} />
		</div> -->
		<div class="min-w-0 flex-1">
			<!-- <div class="flex items-center gap-2 text-sm font-semibold text-gray-900 dark:text-white mb-2">
				<span>Agent</span>
				{#if agent}
					<RemoteAgentStatusIndicator
						status={agent.status}
						hasUpdates={agent.has_updates}
						size="sm"
					/>
				{/if}
			</div> -->

			<!-- Agent Response Content -->
			<div class="space-y-3">
				{#if group.agentResponses && group.agentResponses.length > 0}
					{#each group.agentResponses as response, index}
						<div class="space-y-2">
							{#if response.exchange?.response_nodes}
								<!-- Render response nodes in order (text and tool calls mixed) -->
								{#each response.exchange.response_nodes as node}
									{#if node.type === ChatResultNodeType.TOOL_USE && node.tool_use}
										<!-- Tool call -->
										{@const matchingResult = response.exchange.response_nodes.find(
											(resultNode: any) =>
												resultNode.type === 1 &&
												resultNode.tool_result?.tool_use_id === node.tool_use?.tool_use_id
										)}
										<div class="py-1">
											<ToolCallDisplay size="sm" toolCall={node} toolResult={matchingResult} />
										</div>
									{:else if node.type === ChatResultNodeType.RAW_RESPONSE && node.content}
										<!-- Text content -->
										<div class="">
											<Markdown content={node.content} size="sm" hasNoPadding />
										</div>
									{:else if node.content || node.text}
										<!-- Fallback text content -->
										<div class="">
											<Markdown content={node.content || node.text} size="sm" hasNoPadding />
										</div>
									{/if}
								{/each}
							{:else if response.exchange?.response_text}
								<!-- Fallback to response_text if no nodes -->
								<div class="">
									<Markdown content={response.exchange.response_text} size="sm" hasNoPadding />
								</div>
							{:else if response.response_text}
								<!-- Fallback to top-level response_text -->
								<div class="">
									<Markdown content={response.response_text} size="sm" hasNoPadding />
								</div>
							{:else}
								<div
									class="rounded-lg bg-gray-50/50 p-3 text-sm text-gray-500 italic dark:bg-gray-900/50 dark:text-gray-400"
								>
									No response content available
								</div>
							{/if}
						</div>
					{/each}
				{:else}
					<div
						class="rounded-lg bg-gray-50/50 p-3 text-sm text-gray-500 italic dark:bg-gray-900/50 dark:text-gray-400"
					>
						No responses available
					</div>
				{/if}

				<!-- {#if isStreaming}

											<div class="w-full flex items-center">
												<LoadingIndicator
													variant="asterisk"
													color="blue"
													text="Thinking..."
												/>
											</div>

				{/if}	 -->
			</div>

			<!-- Timestamp -->
			<div class="mt-3 text-xs text-gray-400 dark:text-gray-500">
				{getRelativeTimeForStr(group.timestamp)}
			</div>
		</div>
	</div>
</div>
