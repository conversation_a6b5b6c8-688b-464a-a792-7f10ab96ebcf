<script lang="ts">
	import {
		Icon,
		Cog6Tooth,
		ChevronDown,
		ChevronRight,
		ExclamationTriangle,
		CheckCircle,
		ClipboardDocument,
		InformationCircle
	} from 'svelte-hero-icons';
	import { getToolConfig } from '$lib/utils/tool-configs';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import type { ProcessedNode } from '../chat-history';

	interface Props {
		toolCall: ProcessedNode;
		size?: 'xs' | 'sm' | 'md' | 'lg';
		toolResult?: ProcessedNode; // Optional tool result node
		isExpanded?: boolean;
		isExpandable?: boolean;
	}

	let {
		toolCall,
		toolResult,
		isExpanded = false,
		isExpandable = true,
		size = 'sm'
	}: Props = $props();

	// Extract tool information from the new data structure
	const toolInfo = $derived(() => {
		if (toolCall.tool_use) {
			// New structure with tool_use object
			let input;
			try {
				input = JSON.parse(toolCall.tool_use.input_json);
			} catch {
				input = toolCall.tool_use.input_json;
			}

			// Extract output from tool result if available
			let output = null;
			let hasError = false;
			if (toolResult?.tool_result) {
				const result = toolResult.tool_result;
				hasError = result.is_error;
				// Combine all text content from result nodes
				output = result.content
					.filter((node) => node.text_content)
					.map((node) => node.text_content)
					.join('\n');
			}

			return {
				name: toolCall.tool_use.tool_name,
				input: input,
				output: output,
				isPartial: toolCall.tool_use.is_partial || false,
				hasError: hasError,
				toolUseId: toolCall.tool_use.tool_use_id,
				mcpServerName: toolCall.tool_use.mcp_server_name
			};
		} else {
			// Legacy structure (backward compatibility)
			return {
				name: toolCall.tool_name || 'unknown',
				input: toolCall.tool_input,
				output: toolCall.tool_output,
				isPartial: false,
				hasError: false,
				toolUseId: null,
				mcpServerName: null
			};
		}
	});
	let localExpanded = $state(isExpanded);

	function formatToolInput(input: any): string {
		if (typeof input === 'string') {
			return input;
		}
		return JSON.stringify(input, null, 2);
	}

	function formatToolOutput(output: any): string {
		if (typeof output === 'string') {
			return output;
		}
		return JSON.stringify(output, null, 2);
	}

	// Tool configuration
	const toolConfig = $derived(() => getToolConfig(toolInfo().name));

	function getStatusIcon(hasOutput: boolean, hasError: boolean, isPartial: boolean) {
		if (isPartial) return Cog6Tooth;
		if (hasError) return ExclamationTriangle;
		if (hasOutput) return CheckCircle;
		return CheckCircle;
	}

	function copyToClipboard(text: string) {
		navigator.clipboard.writeText(text);
	}

	const hasOutput = $derived(
		toolInfo().output !== undefined && toolInfo().output !== null && toolInfo().output !== ''
	);
	const hasError = $derived(
		toolInfo().hasError ||
			(typeof toolInfo().output === 'string' && toolInfo().output.toLowerCase().includes('error'))
	);
	const isExpandableLocal = $derived(
		isExpandable &&
			(hasOutput ||
				(toolInfo().input && Object.keys(toolInfo().input).length > 0) ||
				toolInfo().isPartial)
	);
</script>

<div class="relative overflow-hidden rounded bg-white px-2 py-1 dark:bg-slate-800">
	<!-- Tool Call Header -->
	<div
		class="flex items-center gap-1.5 {localExpanded
			? 'border-b border-slate-200 dark:border-slate-700'
			: ''}"
	>
		<!-- <Icon
			src={getStatusIcon(hasOutput, hasError, toolInfo().isPartial)}
			class="absolute top-0 left-2 w-3.5 {toolConfig().statusColor(hasOutput, hasError, toolInfo().isPartial)}"
			micro
		/>	 -->
		<div class="flex-shrink-0">
			<Icon src={toolConfig().icon || InformationCircle} class="h-3.5 w-3.5 text-slate-400" micro />
		</div>
		<div class="min-w-0 flex-1">
			<!-- <h4 class="text-sm font-medium text-slate-900 dark:text-white truncate">
					{toolConfig().displayName}
				</h4> -->
			<div class="text-slate-600 dark:text-slate-400">
				<Markdown
					content={toolConfig().collapsedInfo(toolInfo().input, toolInfo().output)}
					{size}
					hasNoPadding
					lineClamp={1}
					color="gray"
				/>
			</div>
		</div>
		{#if isExpandableLocal}
			<button
				class="flex-shrink-0 rounded p-1 transition-colors duration-200 hover:bg-slate-100 dark:hover:bg-slate-700"
				onclick={() => {
					localExpanded = !localExpanded;
				}}
				aria-label={localExpanded ? 'Collapse tool call' : 'Expand tool call'}
			>
				<Icon
					src={localExpanded ? ChevronDown : ChevronRight}
					class="w-3.5 text-slate-400 transition-transform duration-200"
					micro
				/>
			</button>
		{/if}
	</div>

	<!-- Tool Call Details (Expandable) -->
	{#if localExpanded && isExpandableLocal}
		<div class="space-y-3 p-3">
			<!-- Tool Input -->
			{#if toolInfo().input && Object.keys(toolInfo().input).length > 0}
				<div>
					<div class="mb-2 flex items-center justify-between">
						<h5
							class="text-xs font-medium tracking-wide text-slate-700 uppercase dark:text-slate-300"
						>
							Input
						</h5>
						<button
							class="text-xs text-slate-500 transition-colors hover:text-slate-700 dark:hover:text-slate-300"
							onclick={() => copyToClipboard(formatToolInput(toolInfo().input))}
							title="Copy input"
						>
							<Icon src={ClipboardDocument} class="h-3 w-3" micro />
						</button>
					</div>
					<div class="rounded border bg-slate-100 p-2 dark:bg-slate-900">
						<pre
							class="overflow-x-auto text-xs break-words whitespace-pre-wrap text-slate-800 dark:text-slate-200">{formatToolInput(
								toolInfo().input
							)}</pre>
					</div>
				</div>
			{/if}

			<!-- Tool Output -->
			{#if hasOutput}
				<div>
					<div class="mb-2 flex items-center justify-between">
						<h5
							class="text-xs font-medium tracking-wide text-slate-700 uppercase dark:text-slate-300"
						>
							Output
						</h5>
						<button
							class="text-xs text-slate-500 transition-colors hover:text-slate-700 dark:hover:text-slate-300"
							onclick={() => copyToClipboard(formatToolOutput(toolInfo().output))}
							title="Copy output"
						>
							<Icon src={ClipboardDocument} class="h-3 w-3" micro />
						</button>
					</div>
					<div
						class="rounded border bg-slate-100 p-2 dark:bg-slate-900 {hasError
							? 'border-red-200 dark:border-red-800'
							: ''}"
					>
						{#if toolConfig().outputFormat === 'terminal'}
							<!-- Terminal output with monospace font -->
							<pre
								class="font-mono text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-green-400 dark:text-green-300'} overflow-x-auto rounded bg-slate-900 p-2 whitespace-pre-wrap dark:bg-black">{toolInfo()
									.output}</pre>
						{:else if toolConfig().outputFormat === 'code'}
							<!-- Code output -->
							<pre
								class="font-mono text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-slate-800 dark:text-slate-200'} overflow-x-auto whitespace-pre-wrap">{toolInfo()
									.output}</pre>
						{:else if toolConfig().outputFormat === 'json'}
							<!-- JSON output with syntax highlighting -->
							<pre
								class="font-mono text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-slate-800 dark:text-slate-200'} overflow-x-auto whitespace-pre-wrap">{formatToolOutput(
									toolInfo().output
								)}</pre>
						{:else if toolConfig().outputFormat === 'markdown' && typeof toolInfo().output === 'string'}
							<!-- Markdown output -->
							<Markdown
								content={toolInfo().output}
								{size}
								hasNoPadding
								variant={hasError ? 'muted' : 'default'}
							/>
						{:else if toolConfig().outputFormat === 'search_results'}
							<!-- Search results formatting -->
							<div
								class="text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-slate-800 dark:text-slate-200'}"
							>
								{#if typeof toolInfo().output === 'string'}
									<Markdown
										content={toolInfo().output}
										{size}
										hasNoPadding
										variant={hasError ? 'muted' : 'default'}
									/>
								{:else}
									<pre class="overflow-x-auto whitespace-pre-wrap">{formatToolOutput(
											toolInfo().output
										)}</pre>
								{/if}
							</div>
						{:else if typeof toolInfo().output === 'string' && toolInfo().output.includes('\n')}
							<!-- Multi-line text output -->
							<pre
								class="text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-slate-800 dark:text-slate-200'} overflow-x-auto whitespace-pre-wrap">{toolInfo()
									.output}</pre>
						{:else if typeof toolInfo().output === 'string'}
							<!-- Single-line text output -->
							<div
								class="text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-slate-800 dark:text-slate-200'}"
							>
								{toolInfo().output}
							</div>
						{:else}
							<!-- Fallback JSON output -->
							<pre
								class="text-xs {hasError
									? 'text-red-700 dark:text-red-300'
									: 'text-slate-800 dark:text-slate-200'} overflow-x-auto whitespace-pre-wrap">{formatToolOutput(
									toolInfo().output
								)}</pre>
						{/if}
					</div>
				</div>
			{:else if toolInfo().isPartial}
				<!-- Show partial execution status -->
				<div
					class="rounded border border-yellow-200 bg-yellow-50 p-3 dark:border-yellow-800 dark:bg-yellow-900/20"
				>
					<div class="flex items-center gap-2">
						<div
							class="w-3.5 animate-spin rounded-full border-2 border-yellow-500 border-t-transparent"
						></div>
						<span class="text-sm text-yellow-700 dark:text-yellow-300">Tool is executing...</span>
					</div>
				</div>
			{/if}
		</div>
	{/if}
</div>
