<script lang="ts">
	import {
		getAgentIndicatorConfig,
		formatElapsedTime,
		type ToolUseState
	} from '$lib/utils/agent-status-indicators.svelte';
	import {
		pendingMessagesByAgent,
		getOldestPendingMessageTime
	} from '$lib/utils/optimistic-messages';
	import { type CleanRemoteAgent } from '$lib/api/unified-client';
	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';
	import { slide } from 'svelte/transition';

	interface Props {
		agent: CleanRemoteAgent | null;
		isStreaming?: boolean;
		toolState?: ToolUseState;
		showElapsedTime?: boolean;
		class?: string;
	}

	let {
		agent,
		isStreaming = false,
		toolState = { phase: 'idle' },
		showElapsedTime = true,
		class: className = ''
	}: Props = $props();

	// Check for pending messages using reactive store
	let hasPending = $derived(
		agent
			? $pendingMessagesByAgent.has(agent.id) && $pendingMessagesByAgent.get(agent.id)!.length > 0
			: false
	);
	let messageStartTime = $derived(agent ? getOldestPendingMessageTime(agent.id) : null);

	// Get indicator configuration
	let indicatorConfig = $derived(
		getAgentIndicatorConfig(
			agent,
			isStreaming,
			hasPending,
			toolState,
			messageStartTime || undefined
		)
	);

	// Update elapsed time every second when active
	let elapsedTime = $state(0);
	let intervalId: NodeJS.Timeout | null = null;

	$effect(() => {
		if (indicatorConfig.isActive && messageStartTime) {
			// Update immediately
			elapsedTime = Date.now() - messageStartTime;

			// Set up interval to update every second
			intervalId = setInterval(() => {
				elapsedTime = Date.now() - messageStartTime;
			}, 1000);
		} else {
			// Clear interval when not active
			if (intervalId) {
				clearInterval(intervalId);
				intervalId = null;
			}
			elapsedTime = 0;
		}

		// Cleanup on unmount
		return () => {
			if (intervalId) {
				clearInterval(intervalId);
			}
		};
	});
</script>

{#if indicatorConfig.isActive}
	<div
		class="flex items-center gap-2 text-xs text-slate-600 dark:text-slate-400 {className}"
		transition:slide={{ axis: 'y' }}
	>
		<!-- Loading indicator - use wheel for both resuming and generating -->
		{#if indicatorConfig.showGeneratingResponse || indicatorConfig.showResumingAgent}
			<LoadingIndicator variant="wheel" size="xs" color="slate" />
		{:else if indicatorConfig.showRunningSpacer}
			<LoadingIndicator variant="pulse" size="xs" color="green" />
		{/if}

		<!-- Status text -->
		<span class="">
			{indicatorConfig.indicatorText}
		</span>

		<!-- Elapsed time -->
		{#if showElapsedTime && indicatorConfig.showElapsedTimer && elapsedTime > 0}
			<span class="ml-1 text-xs text-slate-500 dark:text-slate-500">
				({formatElapsedTime(elapsedTime)})
			</span>
		{/if}
	</div>
{/if}

<style>
	/* Add subtle animation for the indicator */
	div {
		animation: fadeIn 0.2s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(-2px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
