<script lang="ts">
	import { onMount } from 'svelte';
	import { CommandLine, DocumentText, Icon, PencilSquare, Sparkles } from 'svelte-hero-icons';
	import Combobox from '$lib/components/ui/forms/Combobox.svelte';
	import { addToast } from '$lib/stores/toast';
	import { apiClient, ChatRequestNodeType } from '$lib/api/unified-client';
	import { listGithubSetupScripts, readGithubSetupScript } from '$lib/utils/github-setup-scripts';
	import type { GithubSetupScript } from '$lib/types';
	import Textarea from '../ui/forms/Textarea.svelte';
	import Button from '../ui/navigation/Button.svelte';

	type SetupScriptTypes = 'basic' | 'auto' | 'manual';

	interface SetupScriptOption {
		value: SetupScriptTypes;
		label: string;
		description: string;
		icon: any;
		disabled?: boolean;
	}

	// Props
	let {
		selectedScript = $bindable(''),
		selectedSetupType = $bindable<SetupScriptTypes>('basic'),
		onScriptSelected,
		onSetupTypeChanged,
		repositoryUrl = '',
		branch = 'main',
		disableAutoGeneration = false,
		onAutoGeneration
	}: {
		selectedScript?: string;
		selectedSetupType?: SetupScriptTypes;
		onScriptSelected?: (script: GithubSetupScript | null) => void;
		onSetupTypeChanged?: (type: SetupScriptTypes) => void;
		repositoryUrl?: string;
		branch?: string;
		disableAutoGeneration?: boolean;
		onAutoGeneration?: () => void;
	} = $props();

	let scripts = $state<GithubSetupScript[]>([]);
	let isLoading = $state(false);
	let isGeneratingScript = $state(false);
	let selectedScriptObj = $state<GithubSetupScript | null>(null);

	// Countdown state for auto-generation
	let isCountingDown = $state(false);
	let countdownSeconds = $state(3);
	let countdownTimer: ReturnType<typeof setInterval> | null = null;

	// Setup type options
	// Combined options: setup types + available scripts
	const comboboxOptions = $derived.by(() => {
		const baseOptions: SetupScriptOption[] = [
			{
				value: 'basic',
				label: 'Use basic environment',
				description: 'Start with a clean environment',
				icon: CommandLine
			},
			{
				value: 'auto',
				label: 'Auto-generate setup script',
				description: 'Let AI create a setup script for your project',
				icon: Sparkles,
				disabled: disableAutoGeneration
			},
			{
				value: 'manual',
				label: 'Write a script by hand',
				description: 'Choose from existing scripts or create new ones',
				icon: PencilSquare
			}
		];

		// Add available scripts as options
		const scriptOptions = scripts.map((script, index) => ({
			value: `script:${script.name}` as any,
			label: script.displayName || script.name,
			description: script.name,
			icon: DocumentText,
			class: index === 0 ? 'border-t border-slate-200 dark:border-slate-700' : ''
		}));

		return [...baseOptions, ...scriptOptions] as any;
	});

	onMount(() => {
		loadScripts();
	});

	// Reload scripts when repository URL or branch changes
	$effect(() => {
		if (repositoryUrl || branch) {
			loadScripts();
		}
	});

	async function loadScripts() {
		if (!repositoryUrl) {
			scripts = [];
			return;
		}

		isLoading = true;
		try {
			// Extract GitHub reference from repository URL
			const githubRef = extractGithubRef(repositoryUrl, branch);
			if (!githubRef) {
				scripts = [];
				return;
			}

			const response = await listGithubSetupScripts(githubRef);
			if (response.error) {
				console.warn('Setup scripts not available:', response.error);
				scripts = [];
			} else {
				scripts = response.scripts;
			}
		} catch (error) {
			console.error('Failed to load setup scripts:', error);
			scripts = [];
		} finally {
			isLoading = false;
		}
	}

	function extractGithubRef(url: string, branch = ''): string | null {
		try {
			// Handle various GitHub URL formats
			const match = url.match(/github\.com[\/:]([^\/]+)\/([^\/\.]+)/);
			if (match) {
				const [, owner, repo] = match;
				return `${owner}/${repo}@${branch}`;
			}
		} catch (error) {
			console.error('Failed to parse GitHub URL:', error);
		}
		return null;
	}

	function handleOptionChange(value: string) {
		if (value.startsWith('script:')) {
			// User selected a script
			const scriptName = value.replace('script:', '');
			const script = scripts.find((s) => s.name === scriptName);
			if (script) {
				selectScript(script);
				selectedSetupType = 'manual'; // Set to manual when script is selected
				onSetupTypeChanged?.('manual');
			}
		} else {
			// User selected a setup type
			const setupType = value as SetupScriptTypes;
			selectedSetupType = setupType;
			onSetupTypeChanged?.(setupType);

			// Clear script selection when changing type
			selectedScriptObj = null;
			selectedScript = '';
			onScriptSelected?.(null);

			// Handle auto-generation with countdown
			if (setupType === 'auto') {
				startCountdown();
			}
		}
	}

	function startCountdown() {
		isCountingDown = true;
		countdownSeconds = 3;

		countdownTimer = setInterval(() => {
			countdownSeconds--;
			if (countdownSeconds <= 0) {
				clearInterval(countdownTimer!);
				countdownTimer = null;
				isCountingDown = false;
				handleAutoGeneration();
			}
		}, 1000);
	}

	function cancelCountdown() {
		if (countdownTimer) {
			clearInterval(countdownTimer);
			countdownTimer = null;
		}
		isCountingDown = false;
		countdownSeconds = 3;
		// Revert selection back to basic
		selectedSetupType = 'basic';
		onSetupTypeChanged?.('basic');
	}

	async function handleAutoGeneration() {
		if (!repositoryUrl) {
			addToast({
				type: 'error',
				message: 'Repository URL is required for auto-generation',
				duration: 5000
			});
			return;
		}

		isGeneratingScript = true;
		try {
			// Create a setup script generation agent
			const response = await apiClient.agents.create({
				initialRequestDetails: {
					requestNodes: [
						{
							id: 1,
							type: ChatRequestNodeType.TEXT,
							textNode: {
								content: 'SETUP_MODE'
							}
						}
					]
				},
				workspaceSetup: {
					startingFiles: {
						githubCommitRef: {
							repositoryUrl: repositoryUrl,
							gitRef: branch
						}
					}
				},
				setupScript: undefined,
				isSetupScriptAgent: true
			});

			// close the drawer
			onAutoGeneration?.();

			addToast({
				type: 'success',
				message: 'Setup script generation started',
				duration: 3000
			});
		} catch (error) {
			console.error('Failed to generate setup script:', error);
			addToast({
				type: 'error',
				message: error instanceof Error ? error.message : 'Failed to generate setup script',
				duration: 5000
			});
		} finally {
			isGeneratingScript = false;
		}
	}

	async function selectScript(script: GithubSetupScript) {
		selectedScriptObj = script;

		// Load the script content
		if (repositoryUrl) {
			const githubRef = extractGithubRef(repositoryUrl, branch);
			if (githubRef) {
				const response = await readGithubSetupScript(githubRef, script.name);
				if (!response.error) {
					selectedScript = response.content;
				}
			}
		}

		onScriptSelected?.(script);
	}
</script>

<!-- Setup Script Picker -->
<div class="">
	<div class="space-y-1">
		<div class="block text-sm font-medium text-slate-900 dark:text-slate-300">Setup script</div>

		<!-- Setup Type Combobox -->
		{#if isLoading}
			<!-- Skeleton loading state -->
			<div class="space-y-3">
				<div class="h-10 w-full animate-pulse rounded-lg bg-slate-200 dark:bg-slate-700"></div>
				<div class="flex space-x-2">
					<div class="h-6 w-20 animate-pulse rounded-md bg-slate-200 dark:bg-slate-700"></div>
					<div class="h-6 w-24 animate-pulse rounded-md bg-slate-200 dark:bg-slate-700"></div>
					<div class="h-6 w-16 animate-pulse rounded-md bg-slate-200 dark:bg-slate-700"></div>
				</div>
			</div>
		{:else}
			<Combobox
				options={comboboxOptions}
				bind:value={selectedSetupType}
				onchange={handleOptionChange}
				placeholder="Select setup type..."
				searchable={false}
				class="w-full"
				containerClass="!shadow-none"
			/>
		{/if}
	</div>

	<!-- Countdown for auto-generation -->
	{#if isCountingDown || isGeneratingScript}
		<div
			class="group relative mt-2 overflow-hidden rounded-lg border border-orange-200/60 bg-gradient-to-r from-orange-50 to-amber-50 p-4 shadow-sm transition-all duration-200 dark:border-orange-800 dark:from-orange-950/30 dark:to-amber-950/30"
		>
			<!-- Animated progress bar -->
			<div
				class="absolute inset-x-0 bottom-0 h-1 bg-gradient-to-r from-orange-400 to-amber-400 transition-all duration-1000 ease-linear"
				style="width: {((3 - countdownSeconds) / 3) * 100}%"
			></div>

			<div class="flex items-center justify-between">
				<div class="flex items-center space-x-3 pl-2">
					<!-- <div class="relative">
						<div
							class="h-5 w-5 animate-spin rounded-full border-2 border-orange-300/30 border-t-orange-500"
						></div>
						<div
							class="absolute inset-0 flex items-center justify-center text-xs font-bold text-orange-600 dark:text-orange-400"
						>
							{countdownSeconds}
						</div>
					</div> -->
					<div>
						<div class="text-sm font-medium text-orange-800 dark:text-orange-200">
							Creating setup script agent for {repositoryUrl.replace('https://github.com/', '')}
						</div>
						{#if isGeneratingScript}
							<div class="mt-1 text-xs text-slate-500 dark:text-slate-400">Kicking off...</div>
						{:else}
							<div class="mt-1 text-xs text-slate-500 dark:text-slate-400">
								Starting in {countdownSeconds} second{countdownSeconds !== 1 ? 's' : ''}
							</div>
						{/if}
					</div>
				</div>
				{#if isGeneratingScript}
					<Icon src={Sparkles} class="h-5 w-5 text-orange-600 dark:text-orange-400" />
				{:else}
					<Button variant="secondary" onclick={cancelCountdown}>Cancel</Button>
				{/if}
			</div>
		</div>
	{/if}

	<!-- Script content editor for manual type -->
	{#if selectedSetupType === 'manual'}
		<Textarea
			bind:value={selectedScript}
			placeholder="#!/bin/bash&#10;# Commands to run before the agent starts working&#10;npm install&#10;npm run build"
			rows={6}
			size="sm"
			class="mt-2 w-full"
			textareaClass="font-mono text-xs"
		/>
	{/if}
</div>
