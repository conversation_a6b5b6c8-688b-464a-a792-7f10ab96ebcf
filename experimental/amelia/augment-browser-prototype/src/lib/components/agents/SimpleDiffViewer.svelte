<script lang="ts">
	interface Props {
		diff: string;
		fileName?: string;
	}

	let { diff, fileName }: Props = $props();

	// Parse diff into lines with their types
	const diffLines = $derived(() => {
		if (!diff) return [];

		const lines = diff.split('\n');
		return lines.map((line, index) => {
			let type: 'context' | 'addition' | 'deletion' | 'header' = 'context';
			let content = line;

			if (line.startsWith('+++') || line.startsWith('---')) {
				type = 'header';
			} else if (line.startsWith('+')) {
				type = 'addition';
				content = line.substring(1); // Remove the + prefix
			} else if (line.startsWith('-')) {
				type = 'deletion';
				content = line.substring(1); // Remove the - prefix
			} else if (line.startsWith(' ')) {
				type = 'context';
				content = line.substring(1); // Remove the space prefix
			}

			return {
				index,
				type,
				content,
				original: line
			};
		});
	});

	// Filter out header lines for cleaner display
	const contentLines = $derived(() => {
		return diffLines.filter(line => line.type !== 'header');
	});
</script>

<div class="bg-gray-50 dark:bg-gray-900 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
	<!-- Header -->
	{#if fileName}
		<div class="px-3 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
			<div class="flex items-center gap-2">
				<span class="text-sm font-medium text-gray-700 dark:text-gray-300">
					{fileName}
				</span>
			</div>
		</div>
	{/if}

	<!-- Diff content -->
	<div class="max-h-96 overflow-y-auto">
		<div class="font-mono text-sm">
			{#each contentLines as line}
				<div
					class="flex {
						line.type === 'addition' ? 'bg-green-50 dark:bg-green-900/20' :
						line.type === 'deletion' ? 'bg-red-50 dark:bg-red-900/20' :
						'bg-white dark:bg-gray-900'
					}"
				>
					<!-- Line type indicator -->
					<div class="w-8 flex-shrink-0 text-center py-1 {
						line.type === 'addition' ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' :
						line.type === 'deletion' ? 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' :
						'text-gray-400 bg-gray-50 dark:bg-gray-800'
					}">
						{line.type === 'addition' ? '+' : line.type === 'deletion' ? '-' : ''}
					</div>

					<!-- Line content -->
					<div class="flex-1 px-3 py-1 {
						line.type === 'addition' ? 'text-green-800 dark:text-green-200' :
						line.type === 'deletion' ? 'text-red-800 dark:text-red-200' :
						'text-gray-700 dark:text-gray-300'
					}">
						<pre class="whitespace-pre-wrap break-all">{line.content || ' '}</pre>
					</div>
				</div>
			{/each}
		</div>
	</div>

	<!-- Empty state -->
	{#if contentLines.length === 0}
		<div class="p-4 text-center text-gray-500 dark:text-gray-400">
			<p class="text-sm">No diff content available</p>
		</div>
	{/if}
</div>

<style>
	/* Ensure proper line height for diff display */
	pre {
		line-height: 1.4;
		margin: 0;
	}
</style>
