<script lang="ts">
	import { tick } from 'svelte';
	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import Textarea from '$lib/components/ui/forms/Textarea.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import PromptEnhancer from '$lib/components/ui/PromptEnhancer.svelte';
	import type { PromptEnhancementResult, PromptEnhancementContext } from '$lib/utils/prompts';
	import {
		sendChatMessage,
		startStreamingAgent,
		stopStreamingAgent,
		interruptAgent
	} from '$lib/stores/data-operations.svelte';
	import {
		getAgent,
		getChatSession,
		getEntityByAgent,
		addOptimisticMessage,
		clearOptimisticMessage
	} from '$lib/stores/global-state.svelte';
	import { addToast } from '$lib/stores/toast';
	import { PaperAirplane, Stop } from 'svelte-hero-icons';

	interface Props {
		agentId: string;
		disabled?: boolean;
	}

	let { agentId, disabled = false }: Props = $props();

	// Get agent data
	let agent = $derived(getAgent(agentId));

	// Get chat session to check streaming status
	let chatSession = $derived(getChatSession(agentId));

	// Get linked entity for context
	let linkedEntityStore = $derived(getEntityByAgent(agentId));
	let linkedEntity = $derived($linkedEntityStore?.data);

	// Message state
	let currentMessage = $state('');
	let isSendingMessage = $state(false);
	let sendError = $state<string | null>(null);
	let textareaElement = $state<HTMLTextAreaElement>();

	// Debug counter to see if messages are being sent
	let messagesSentCount = $state(0);

	// Determine if the component should be disabled based on agent status
	// Don't disable during streaming - allow interruption
	let isDisabled = $derived(
		disabled || !$agent?.data || $agent.data.status === RemoteAgentStatus.AGENT_STARTING
	);

	// Check if agent is currently streaming a response
	let isStreaming = $derived(
		$chatSession?.data?.isStreaming ||
			($chatSession?.data?.streamingMessages && $chatSession.data.streamingMessages.size > 0)
	);

	// Determine button state - show stop if streaming, send if not
	let shouldShowStopButton = $derived(isStreaming && !isSendingMessage);

	async function handleStopStreaming() {
		if (!agent) return;

		try {
			// If there's no content in the prompt box, interrupt the agent
			if (!currentMessage.trim()) {
				console.log(`Interrupting agent ${agentId} (no message content)`);
				await interruptAgent(agentId);
			} else {
				// If there's content, just stop local streaming (user wants to send a message)
				console.log(`Stopping streaming for agent ${agentId} (user has message to send)`);
			}

			// Always stop local streaming
			stopStreamingAgent(agentId);
		} catch (error) {
			console.error('Failed to stop streaming:', error);
			const errorMessage = error instanceof Error ? error.message : 'Failed to stop streaming';
			addToast({
				type: 'error',
				message: errorMessage,
				duration: 3000
			});
		}
	}

	async function handleSendMessage() {
		if (!agent || !currentMessage.trim() || isSendingMessage) {
			return;
		}

		const messageToSend = currentMessage.trim();

		// If currently streaming, stop it first before sending new message
		if (isStreaming) {
			console.log('Interrupting current stream to send new message');
			stopStreamingAgent(agentId, false); // Don't clear optimistic message since we're about to add one
		}

		// Increment debug counter
		messagesSentCount++;

		// Add optimistic message to global state
		addOptimisticMessage(agentId, messageToSend, 'user');

		// Clear the input immediately for better UX
		forceClearInput();
		isSendingMessage = true;
		sendError = null;

		try {
			startStreamingAgent(agentId).catch((error) => {
				console.error(`Failed to start streaming for agent ${agentId}:`, error);
			});
			sendChatMessage($agent.data.id, messageToSend);
		} catch (error) {
			console.error('Failed to send message:', error);

			// Clear optimistic message on error
			clearOptimisticMessage(agentId);

			// Restore the message on error so user can retry
			currentMessage = messageToSend;

			const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
			sendError = errorMessage;
			addToast({
				type: 'error',
				message: errorMessage,
				duration: 5000
			});
		} finally {
			isSendingMessage = false;
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			// If streaming and no message, stop streaming
			// If streaming with message, interrupt and send new message
			// If not streaming, send message normally
			if (shouldShowStopButton && !currentMessage.trim()) {
				handleStopStreaming();
			} else {
				handleSendMessage();
			}
		}
	}

	// Force clear input after sending (helps with mobile keyboard issues)
	function forceClearInput() {
		currentMessage = '';
		// Force a tick to ensure the binding updates
		tick().then(() => {
			if (textareaElement) {
				textareaElement.value = '';
			}
		});
	}

	// Create context for prompt enhancement
	let promptContext = $derived<PromptEnhancementContext>({
		contextType: 'agent-chat',
		agentTitle: $agent?.data?.title,
		agentStatus: $agent?.data?.status ? String($agent.data.status) : undefined,
		sessionSummary: $chatSession?.data?.sessionSummary,
		entityTitle: linkedEntity?.title,
		entityType: linkedEntity?.entityType,
		entityDescription: linkedEntity?.description
	});

	// Prompt enhancer functions
	function handlePromptEnhanced(enhancedPrompt: string) {
		currentMessage = enhancedPrompt;
	}

	function handlePromptEnhancementError(error: PromptEnhancementResult) {
		console.error('Prompt enhancement error:', error);
		addToast({
			type: 'error',
			message: 'Failed to enhance prompt. Please try again.'
		});
	}
</script>

<div class="relative w-full">
	<!-- Error message -->
	{#if sendError}
		<div
			class="mb-4 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-800 dark:bg-red-900/20"
		>
			<p class="text-sm text-red-600 dark:text-red-400">{sendError}</p>
		</div>
	{/if}

	<!-- Message Input -->
	<div class="relative">
		<Textarea
			bind:value={currentMessage}
			bind:textareaElement
			onkeypress={handleKeyPress}
			resize="none"
			placeholder="Ask a follow-up question..."
			disabled={isDisabled}
			rows={5}
			class="w-full"
			autoResize
			textareaClass="max-h-92 pr-20 py-3 text-sm leading-snug !rounded-none focus:!ring-0 !border-slate-200  dark:bg-slate-900 dark:!border-slate-600 focus:!border-slate-200 dark:focus:!border-slate-600 focus:outline-none"
		/>

		<!-- Prompt Enhancer -->
		<div class="absolute top-2 right-3.5">
			<PromptEnhancer
				prompt={currentMessage}
				onEnhanced={handlePromptEnhanced}
				onError={handlePromptEnhancementError}
				{isDisabled}
				context={promptContext}
			/>
		</div>

		<!-- Send/Stop button -->
		<div class="absolute right-2 bottom-3">
			{#if shouldShowStopButton}
				<Button
					variant="destructive"
					size="icon-sm"
					icon={Stop}
					onclick={handleStopStreaming}
					class="h-8 w-8 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md"
					aria-label="Stop streaming"
				/>
			{:else}
				<Button
					variant="primary"
					size="icon-sm"
					icon={PaperAirplane}
					onclick={handleSendMessage}
					disabled={isDisabled || isSendingMessage || !currentMessage.trim()}
					class="h-8 w-8 rounded-lg shadow-sm transition-all duration-200 hover:shadow-md disabled:bg-slate-300 {isSendingMessage
						? 'animate-pulse'
						: ''}"
					aria-label="Send message"
				/>
			{/if}
		</div>
	</div>
</div>
