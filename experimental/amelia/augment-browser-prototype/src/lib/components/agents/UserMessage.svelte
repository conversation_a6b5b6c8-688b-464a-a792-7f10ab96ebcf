<script lang="ts">
	import Markdown from '$lib/components/ui/Markdown.svelte';
	import { extractOriginalMessageFromInitial, extractQuestionResponsesFromUserMessage } from '$lib/types/structured-response';

	interface Props {
		message: string;
		isPending?: boolean;
		showQuestionResponses?: boolean;
	}

	let { message, isPending = false, showQuestionResponses = true }: Props = $props();

	// Extract question responses and user request from the message
	const questionResponses = $derived( extractQuestionResponsesFromUserMessage(message));

	const userRequest = $derived(extractOriginalMessageFromInitial(message));

	// Check if this message has structured content (question responses)
	const hasStructuredContent = $derived(showQuestionResponses && questionResponses.length > 0);
</script>

<div class="w-full flex justify-end">
<div class="mb-5 inline-flex flex-col ml-auto bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-2xl shadow-sm max-w-[70%] {isPending ? 'opacity-90' : ''} {hasStructuredContent ? 'divide-y divide-slate-100 dark:divide-slate-700' : ''}">
	{#if hasStructuredContent && showQuestionResponses}
		<div class="px-4 py-3">
			<div class="space-y-2">
				{#each questionResponses as qr}
					<div class="text-xs gap-1.5 flex flex-col">
						<div class="font-medium text-slate-800 dark:text-slate-200">{qr.question}:</div>
						<div class="text-slate-700 dark:text-slate-300">{qr.response}</div>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- User Request -->
	{#if userRequest}
		<div class="text-slate-500 px-4 py-3">
			<Markdown content={userRequest} size="sm" hasNoPadding />
		</div>
	{:else if !hasStructuredContent}
		<!-- Fallback: show the raw message if no structured content and no extracted request -->
		<div class="text-slate-500 px-4 py-3">
			<Markdown content={message} size="sm" hasNoPadding />
		</div>
	{/if}
</div>
</div>
