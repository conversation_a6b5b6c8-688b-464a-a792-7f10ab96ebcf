<script lang="ts">
	interface Props {
		type: 'addition' | 'deletion';
		count: number;
		maxCount?: number;
		minSize?: number;
		maxSize?: number;
	}

	let {
		type,
		count,
		maxCount = 100,
		minSize = 5,
		maxSize = 15
	}: Props = $props();

	// Linear interpolation function
	function lerp(start: number, end: number, factor: number): number {
		return start + (end - start) * factor;
	}

	// Calculate the size for this count using linear scaling
	const dotSize = $derived(() => {
		const clampedCount = Math.max(1, Math.min(count, maxCount));
		const factor = (clampedCount - 1) / (maxCount - 1);
		return lerp(minSize, maxSize, factor);
	});

	// Get the color based on type
	const color = $derived(() => {
		return type === 'addition' ? '#10b981' : '#ef4444'; // emerald-500 : red-500
	});
</script>

<svg
	width={maxSize}
	height={maxSize}
	viewBox="0 0 {maxSize} {maxSize}"
	class="flex-shrink-0"
>
	<circle
		cx={maxSize / 2}
		cy={maxSize / 2}
		r={dotSize() / 2}
		fill={color()}
	/>
</svg>
