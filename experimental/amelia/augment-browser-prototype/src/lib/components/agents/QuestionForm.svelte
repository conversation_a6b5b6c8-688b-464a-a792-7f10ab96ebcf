<script lang="ts">
	import type { QuestionField, QuestionResponse } from '$lib/types/structured-response';
	import FormField from '../ui/forms/FormField.svelte';
	import { getContext } from 'svelte';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	interface Props {
		questions: QuestionField[];
		disabled?: boolean;
	}

	let { questions, disabled = false }: Props = $props();

	// Get the question context
	const questionContext = getContext<any>('questionResponses');

	// Initialize responses with default values
	function getDefaultValue(question: QuestionField): string | number | boolean {
		if (question.defaultValue !== undefined) {
			return question.defaultValue;
		} else if (question.type === 'boolean') {
			return false;
		} else if (question.type === 'number' || question.type === 'slider') {
			return question.min ?? 0;
		} else {
			return '';
		}
	}

	// Initialize responses immediately with all questions
	let responses = $state<QuestionResponse>(
		questions.reduce((acc, question) => {
			acc[question.id] = getDefaultValue(question);
			return acc;
		}, {} as QuestionResponse)
	);

	// Update responses when questions change
	const updateResponses = () => {
		const newResponses: QuestionResponse = {};
		questions.forEach(question => {
			// Keep existing value if it exists, otherwise use default
			newResponses[question.id] = responses[question.id] !== undefined
				? responses[question.id]
				: getDefaultValue(question);
		});

		responses = newResponses;

		// Update context with values
		if (questionContext) {
			Object.entries(newResponses).forEach(([questionId, value]) => {
				questionContext.updateResponse(questionId, value, questions);
			});
		}
	}
	let questionsJson = $derived(JSON.stringify(questions));
	let lastQuestionsJson = $state(questionsJson);
	$effect(() => {
		if (questionsJson !== lastQuestionsJson) {
			updateResponses();
			lastQuestionsJson = questionsJson;
		}
	});

	function handleFieldChange(questionId: string, value: string | number | boolean) {
		responses[questionId] = value;
		responses = { ...responses }; // Trigger reactivity
		console.log('Field changed:', questionId, value, responses);

		// Update context
		if (questionContext) {
			questionContext.updateResponse(questionId, value, questions);
		}
	}
</script>

<div class="space-y-8 grid grid-cols-1 md:grid-cols-2 gap-8">
	{#each questions as question}
		{#if responses[question.id] !== undefined}
			<FormField
				id={question.id}
				type={question.type}
				bind:value={responses[question.id]}
				placeholder={question.placeholder}
				required={question.required}
				options={question.options}
				min={question.min}
				max={question.max}
				step={question.step}
				{disabled}
				size="sm"
				onchange={(value) => handleFieldChange(question.id, value)}
			>
				{#snippet label()}
				<div class="w-full">
					<Markdown content={question.label} size="sm" hasNoPadding />
				</div>

				{/snippet}
			</FormField>
		{/if}
	{/each}
</div>
