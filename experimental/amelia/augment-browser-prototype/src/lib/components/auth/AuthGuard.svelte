<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { browser } from '$app/environment';
	import { session, auth } from '$lib/stores/auth';
	import Spinner from '$lib/components/ui/feedback/Spinner.svelte';

	let { children } = $props();

	// Routes that don't require authentication
	const publicRoutes = ['/login', '/login/callback', '/login/manual-callback', '/auth/callback', '/auth/redirect'];

	// Track current path reactively
	let currentPath = $state('');

	// Track if we're still initializing the session
	let isInitializing = $state(true);

	// Update current path on mount and navigation
	onMount(() => {
		if (browser) {
			currentPath = window.location.pathname;

			// Listen for navigation changes
			const handleNavigation = () => {
				currentPath = window.location.pathname;
			};

			// Listen for browser back/forward
			window.addEventListener('popstate', handleNavigation);

			// Use a less frequent interval to catch programmatic navigation
			// Reduced from 50ms to 500ms to prevent excessive reactive updates
			const interval = setInterval(() => {
				if (currentPath !== window.location.pathname) {
					currentPath = window.location.pathname;
				}
			}, 500);

			return () => {
				window.removeEventListener('popstate', handleNavigation);
				clearInterval(interval);
			};
		}
	});

	// Check if current route is public
	const isPublicRoute = $derived(publicRoutes.includes(currentPath));

	// Initialize session on mount
	onMount(async () => {
		if (!browser) return;

		try {
			// Refresh session from storage in case it was updated elsewhere
			await auth.refreshSession();
		} catch (error) {
			console.error('Failed to refresh session:', error);
			// Set session to null on error to trigger login redirect
			session.set(null);
		} finally {
			// Mark initialization as complete
			isInitializing = false;
		}
	});

	// Handle authentication redirects when session state changes
	$effect(() => {
		// Don't redirect while still initializing or if not in browser
		if (isInitializing || !browser) return;

		// If session is null (logged out) and not on a public route, redirect to login
		if ($session === null && !isPublicRoute) {
			// Store the current path to redirect back after login
			const returnTo = window.location.pathname + window.location.search;
			if (!['/login', '/logout'].includes(returnTo)) {
				sessionStorage.setItem('auth_return_to', returnTo);
			}
			goto('/login', { replaceState: true });
		}
	});
</script>

{#if isInitializing}
	<!-- Loading state while initializing session -->
	<div class="min-h-screen bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
		<Spinner size="lg" message="Checking authentication..." />
	</div>
{:else if $session || isPublicRoute}
	{@render children()}
{:else}
	<!-- Loading state while redirecting to login -->
	<div class="min-h-screen bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
		<Spinner size="lg" message="Redirecting to login..." />
	</div>
{/if}
