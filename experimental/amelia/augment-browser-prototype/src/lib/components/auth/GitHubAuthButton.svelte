<script lang="ts">
	import { githubSession, githubLoading, githubAuth } from '$lib/stores/github-auth';
	import { addToast } from '$lib/stores/toast';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { Icon } from 'svelte-hero-icons';
	import {
		CheckCircle,
		ExclamationTriangle,
		ArrowRightOnRectangle,
		ArrowLeftOnRectangle
	} from 'svelte-hero-icons/24/outline';

	// Props
	let {
		size = 'md',
		variant = 'outline',
		showStatus = true
	}: {
		size?: 'sm' | 'md' | 'lg';
		variant?: 'primary' | 'outline' | 'ghost';
		showStatus?: boolean;
	} = $props();

	async function handleLogin() {
		try {
			await githubAuth.login();
		} catch (error) {
			console.error('GitHub login failed:', error);
			addToast({
				type: 'error',
				message: error instanceof Error ? error.message : 'GitHub login failed',
				duration: 5000
			});
		}
	}

	function handleLogout() {
		githubAuth.logout();
		addToast({
			type: 'success',
			message: 'Logged out from GitHub',
			duration: 3000
		});
	}

	// Reactive values
	$: isAuthenticated = $githubSession !== null;
	$: isLoading = $githubLoading;
	$: user = $githubSession?.user;
</script>

{#if isAuthenticated && user}
	<div class="flex items-center gap-2">
		{#if showStatus}
			<div class="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
				<Icon src={CheckCircle} class="w-4 h-4" />
				<span>GitHub: {user.login}</span>
			</div>
		{/if}

		<Button
			{size}
			variant="ghost"
			onclick={handleLogout}
			disabled={isLoading}
			title="Logout from GitHub"
		>
			<Icon src={ArrowLeftOnRectangle} class="w-4 h-4" />
			{#if size !== 'sm'}
				Logout
			{/if}
		</Button>
	</div>
{:else}
	<div class="flex items-center gap-2">
		{#if showStatus}
			<div class="flex items-center gap-2 text-sm text-amber-600 dark:text-amber-400">
				<Icon src={ExclamationTriangle} class="w-4 h-4" />
				<span>GitHub: Not connected</span>
			</div>
		{/if}

		<Button
			{size}
			{variant}
			onclick={handleLogin}
			disabled={isLoading}
		>
			<Icon src={ArrowRightOnRectangle} class="w-4 h-4" />
			{#if isLoading}
				Connecting...
			{:else if size !== 'sm'}
				Connect GitHub
			{/if}
		</Button>
	</div>
{/if}

<style>
	/* Add any custom styles here */
</style>
