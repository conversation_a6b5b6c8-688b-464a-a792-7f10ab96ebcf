<script lang="ts">
	import { onMount } from 'svelte';
	import {
		siteAuthStore,
		markSiteAuthenticated,
		checkSiteAuthentication
	} from '$lib/stores/global-state.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Spinner from '../ui/feedback/Spinner.svelte';

	let { children } = $props();

	let password = $state('');
	let error = $state('');
	let loading = $state(false);

	// Subscribe to the site auth store
	let siteAuth = $state($siteAuthStore);

	// Update local state when store changes
	$effect(() => {
		siteAuth = $siteAuthStore;
	});

	// Check authentication on mount
	onMount(() => {
		checkSiteAuthentication();
	});

	async function submitPassword() {
		if (!password) {
			error = 'Please enter the password';
			return;
		}

		loading = true;
		error = '';

		try {
			const response = await fetch('/api/site-auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ password }),
				credentials: 'include'
			});

			if (response.ok) {
				// Mark as authenticated immediately - the cookie is set by the server
				markSiteAuthenticated();
				// Clear the password field
				password = '';
			} else {
				const data = await response.json();
				error = data.error || 'Invalid password';
			}
		} catch (err) {
			error = 'An error occurred. Please try again.';
		} finally {
			loading = false;
		}
	}

	async function handleSubmit(event: SubmitEvent) {
		event.preventDefault();
		await submitPassword();
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			event.preventDefault();
			submitPassword();
		}
	}
</script>

{#if siteAuth.isChecking}
	<!-- Loading state while checking password -->
	<div class="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
		<div class="text-center">
			<Spinner size="lg" />
			<p class="text-gray-600 dark:text-gray-400">Checking access...</p>
		</div>
	</div>
{:else if !siteAuth.isAuthenticated}
	<!-- Password entry form -->
	<div
		class="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8 dark:bg-gray-900"
	>
		<div class="w-full max-w-md space-y-8">
			<div>
				<h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
					Access Required
				</h2>
				<p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
					Please enter the site password to continue
				</p>
			</div>
			<form class="mt-8 space-y-6" onsubmit={handleSubmit}>
				<div>
					<label for="password" class="sr-only">Password</label>
					<input
						id="password"
						name="password"
						type="password"
						bind:value={password}
						onkeydown={handleKeydown}
						placeholder="Site password"
						class="relative block w-full appearance-none rounded-md border border-gray-300 bg-white px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-blue-500 focus:ring-blue-500 focus:outline-none sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-white dark:placeholder-gray-400"
						disabled={loading}
					/>
				</div>

				{#if error}
					<div class="text-center text-sm text-red-600 dark:text-red-400">
						{error}
					</div>
				{/if}

				<div>
					<Button type="submit" variant="primary" {loading} class="w-full">Access Site</Button>
				</div>
			</form>
		</div>
	</div>
{:else}
	<!-- Password verified, show the app -->
	{@render children()}
{/if}
