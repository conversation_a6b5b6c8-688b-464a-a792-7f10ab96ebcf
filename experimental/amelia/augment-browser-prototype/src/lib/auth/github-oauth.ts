/**
 * GitHub OAuth implementation for web applications
 *
 * This provides OAuth authentication with GitHub to automatically
 * obtain user access tokens for GitHub API access.
 */
import {
	generateCodeChallenge,
	generateCodeVerifier,
	generateState
} from '$lib/utils/crypto-utils';

export interface GitHubOAuthConfig {
	clientId: string;
	redirectUri: string;
	scopes: string[];
}

export interface GitHubOAuthState {
	state: string;
	codeVerifier: string;
	codeChallenge: string;
	createdAt: number;
}

export interface GitHubTokenResponse {
	access_token: string;
	token_type: string;
	scope: string;
}

export interface GitHubUser {
	id: number;
	login: string;
	name: string;
	email: string;
	avatar_url: string;
}

export interface GitHubSession {
	accessToken: string;
	tokenType: string;
	scopes: string[];
	user: GitHubUser;
	expiresAt?: number;
}

export class GitHubOAuth {
	private config: GitHubOAuthConfig;
	private readonly STATE_KEY = 'github_oauth_state';
	private readonly SESSION_KEY = 'github_session';

	constructor(config: GitHubOAuthConfig) {
		this.config = config;
	}

	/**
	 * Start the GitHub OAuth authorization flow
	 */
	async startAuthFlow(): Promise<void> {
		// Generate PKCE parameters
		const codeVerifier = generateCodeVerifier();
		const codeChallenge = await generateCodeChallenge(codeVerifier);
		const state = generateState();

		// Store state for verification
		const oauthState: GitHubOAuthState = {
			state,
			codeVerifier,
			codeChallenge,
			createdAt: Date.now()
		};

		sessionStorage.setItem(this.STATE_KEY, JSON.stringify(oauthState));

		// Build authorization URL
		const authUrl = this.buildAuthUrl(state, codeChallenge);

		console.log('Starting GitHub OAuth flow:', authUrl);

		// Redirect to GitHub authorization server
		window.location.href = authUrl;
	}

	/**
	 * Handle the OAuth callback and exchange code for token
	 */
	async handleCallback(): Promise<GitHubSession> {
		const urlParams = new URLSearchParams(window.location.search);
		const code = urlParams.get('code');
		const returnedState = urlParams.get('state');
		const error = urlParams.get('error');

		if (error) {
			throw new Error(`GitHub OAuth error: ${error}`);
		}

		if (!code) {
			throw new Error('No authorization code received');
		}

		// Verify state
		const storedStateJson = sessionStorage.getItem(this.STATE_KEY);
		if (!storedStateJson) {
			throw new Error('No OAuth state found');
		}

		const storedState: GitHubOAuthState = JSON.parse(storedStateJson);

		if (!returnedState || returnedState !== storedState.state) {
			throw new Error('Invalid state parameter');
		}

		// Check state age (max 10 minutes)
		if (Date.now() - storedState.createdAt > 10 * 60 * 1000) {
			throw new Error('OAuth state expired');
		}

		// Exchange code for tokens
		const session = await this.exchangeCodeForTokens(code, storedState.codeVerifier);

		// Clean up
		sessionStorage.removeItem(this.STATE_KEY);

		// Store session on server
		await this.storeSession(session);

		return session;
	}

	/**
	 * Get stored session from server-side cookies
	 */
	async getStoredSession(): Promise<GitHubSession | null> {
		if (typeof window === 'undefined') return null;

		try {
			const response = await fetch('/api/auth/github/session');
			if (!response.ok) return null;

			const data = await response.json();
			const session = data.session;

			if (!session) return null;

			// Check if session is expired
			if (session.expiresAt && Date.now() > session.expiresAt) {
				await this.clearSession();
				return null;
			}

			return session;
		} catch (error) {
			console.error('Failed to get GitHub session:', error);
			return null;
		}
	}

	/**
	 * Clear stored session from server-side cookies
	 */
	async clearSession(): Promise<void> {
		if (typeof window === 'undefined') return;

		try {
			await fetch('/api/auth/github/session', {
				method: 'DELETE'
			});
		} catch (error) {
			console.error('Failed to clear GitHub session:', error);
		}
	}

	/**
	 * Check if user is authenticated
	 */
	async isAuthenticated(): Promise<boolean> {
		const session = await this.getStoredSession();
		return session !== null;
	}

	private buildAuthUrl(state: string, codeChallenge: string): string {
		const params = new URLSearchParams({
			client_id: this.config.clientId,
			redirect_uri: this.config.redirectUri,
			scope: this.config.scopes.join(' '),
			state,
			code_challenge: codeChallenge,
			code_challenge_method: 'S256',
			response_type: 'code'
		});

		return `https://github.com/login/oauth/authorize?${params.toString()}`;
	}

	private async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<GitHubSession> {
		// Exchange code for access token via our backend
		const response = await fetch('/api/auth/github/token', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				code,
				codeVerifier,
				redirectUri: this.config.redirectUri
			})
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
			throw new Error(`Token exchange failed: ${errorData.error || response.statusText}`);
		}

		const tokenData = await response.json();

		// Get user info
		const userResponse = await fetch('https://api.github.com/user', {
			headers: {
				Authorization: `Bearer ${tokenData.access_token}`,
				Accept: 'application/vnd.github.v3+json'
			}
		});

		if (!userResponse.ok) {
			throw new Error('Failed to get user information');
		}

		const user: GitHubUser = await userResponse.json();

		return {
			accessToken: tokenData.access_token,
			tokenType: tokenData.token_type || 'bearer',
			scopes: tokenData.scope ? tokenData.scope.split(',') : this.config.scopes,
			user,
			expiresAt: tokenData.expires_in ? Date.now() + tokenData.expires_in * 1000 : undefined
		};
	}

	private async storeSession(session: GitHubSession): Promise<void> {
		if (typeof window === 'undefined') return;

		try {
			await fetch('/api/auth/github/session', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(session)
			});
		} catch (error) {
			console.error('Failed to store GitHub session:', error);
		}
	}
}

/**
 * Create a GitHub OAuth client instance
 */
export function createGitHubOAuth(config: Partial<GitHubOAuthConfig> = {}): GitHubOAuth {
	const defaultConfig: GitHubOAuthConfig = {
		clientId: 'your-github-oauth-app-client-id', // Must be provided via config parameter
		redirectUri: `${window.location.origin}/auth/github/callback`,
		scopes: ['repo', 'user:email']
	};

	return new GitHubOAuth({ ...defaultConfig, ...config });
}
