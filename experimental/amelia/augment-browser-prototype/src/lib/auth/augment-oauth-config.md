# Augment OAuth Configuration Guide

This document contains all the configuration details extracted from the Augment codebase that your AI agent needs to implement OAuth authentication.

## 🔑 Core OAuth Configuration

### **Authorization Server**
```typescript
const OAUTH_CONFIG = {
  authUrl: 'https://auth-staging.augmentcode.com',
  authorizeEndpoint: '/authorize',
  tokenEndpoint: '/token',
  hostname: '.augmentcode.com', // For tenant URL validation
};
```

### **Client IDs by Platform**
```typescript
const CLIENT_IDS = {
  vscode: 'augment-vscode-extension',
  intellij: 'augment-intellij-plugin',
  vim: 'v', // Shortened for URL length
  web: 'augment-web-ui', // You'll need to register this
};
```

### **OAuth Scopes**
```typescript
const SCOPES = ['email']; // Only scope used across all clients
```

### **OAuth Parameters**
```typescript
const OAUTH_PARAMS = {
  response_type: 'code',
  code_challenge_method: 'S256', // PKCE
  prompt: 'login',
  grant_type: 'authorization_code', // For token exchange
};
```

## 🔐 PKCE Implementation Details

### **Code Verifier Generation**
```typescript
// Generate 128 characters of randomness
const codeVerifier = generateRandomString(128);

// Character set for code verifier
const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
```

### **Code Challenge Generation**
```typescript
// SHA256 hash of code verifier, base64url encoded
const codeChallenge = base64url(sha256(codeVerifier));

// Base64URL encoding (no padding)
function base64url(buffer) {
  return btoa(String.fromCharCode(...new Uint8Array(buffer)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
```

### **State Generation**
```typescript
// For CSRF protection - random string
const state = generateRandomString(32);
// Vim uses 8 bytes (16 chars) to keep URLs short
const vimState = generateRandomString(16);
```

## 🌐 Authorization URL Construction

### **Full Authorization URL**
```typescript
const authUrl = new URL('/authorize', 'https://auth-staging.augmentcode.com');
authUrl.searchParams.set('response_type', 'code');
authUrl.searchParams.set('client_id', clientId);
authUrl.searchParams.set('redirect_uri', redirectUri);
authUrl.searchParams.set('scope', 'email');
authUrl.searchParams.set('state', state);
authUrl.searchParams.set('code_challenge', codeChallenge);
authUrl.searchParams.set('code_challenge_method', 'S256');
authUrl.searchParams.set('prompt', 'login');
```

### **Minimal URL (Vim style)**
```typescript
// Vim drops optional parameters to keep URL short
const minimalParams = {
  response_type: 'code',
  code_challenge: codeChallenge,
  client_id: 'v',
  state: state,
  prompt: 'login'
  // No redirect_uri, scope, or code_challenge_method
};
```

## 🔄 Token Exchange

### **Token Request Body**
```typescript
const tokenRequestBody = {
  grant_type: 'authorization_code',
  client_id: clientId,
  code: authorizationCode,
  redirect_uri: redirectUri,
  code_verifier: codeVerifier,
};
```

### **Token Endpoint**
```typescript
const tokenUrl = `${tenantUrl}token`;
// Note: Uses tenant URL, not auth URL
```

### **Token Response**
```typescript
interface TokenResponse {
  access_token: string;
  token_type: string; // Usually 'Bearer'
  expires_in?: number; // Seconds
  scope?: string;
}
```

## 🏢 Session Management

### **Session Structure**
```typescript
interface AugmentSession {
  accessToken: string;
  tenantURL: string; // Important: This becomes your API base URL
  scopes: string[]; // Always ['email']
  expiresAt?: number; // Optional expiration
}
```

### **Storage Keys**
```typescript
const STORAGE_KEYS = {
  session: 'augment.sessions',
  oauthState: 'augment.oauth-state',
  // Web versions
  webSession: 'augment_session',
  webOauthState: 'augment_oauth_state',
};
```

## 🔍 Callback Handling

### **Expected Callback Parameters**
```typescript
// Success callback
const callbackParams = {
  code: 'authorization_code_here',
  state: 'state_value_here',
  tenant_url: 'https://tenant.augmentcode.com',
};

// Error callback
const errorParams = {
  error: 'access_denied',
  error_description: 'User denied access',
  state: 'state_value_here',
};
```

### **Tenant URL Validation**
```typescript
// Validate tenant URL ends with .augmentcode.com
function validateTenantUrl(tenantUrl) {
  const hostname = new URL(tenantUrl).hostname;
  return hostname.endsWith('.augmentcode.com');
}
```

## 🛡️ Security Considerations

### **State Validation**
```typescript
// Always verify state parameter matches
if (callbackState !== storedState) {
  throw new Error('Invalid OAuth state parameter');
}
```

### **Timeout Handling**
```typescript
const SIGN_IN_TIMEOUT_MINS = 10;
const stateMaxAge = 10 * 60 * 1000; // 10 minutes

// Check state age
if (Date.now() - state.creationTime > stateMaxAge) {
  throw new Error('OAuth state expired');
}
```

### **Secure Storage**
```typescript
// Use secure storage for tokens
// Browser: localStorage/sessionStorage
// Extensions: Secret storage APIs
// Never store in plain text files
```

## 🔧 Platform-Specific Redirect URIs

### **VS Code Extension**
```typescript
const vscodeRedirectUri = `vscode://${extensionId}/auth/result`;
// Example: vscode://augment.vscode-augment/auth/result
```

### **IntelliJ Plugin**
```typescript
const intellijRedirectUri = `http://127.0.0.1:${port}/api/augment/auth/result`;
// Uses local server on random port
```

### **Web Application**
```typescript
const webRedirectUri = `${window.location.origin}/auth/callback`;
// Example: https://your-app.com/auth/callback
```

### **Vim (Special Case)**
```typescript
const vimRedirectUri = ''; // Empty string - handled specially
```

## 📡 API Usage After Authentication

### **API Base URL**
```typescript
// IMPORTANT: Use tenant URL as API base, not auth URL
const apiBaseUrl = session.tenantURL; // e.g., https://tenant.augmentcode.com
```

### **Authorization Header**
```typescript
const headers = {
  'Authorization': `Bearer ${session.accessToken}`,
  'Content-Type': 'application/json',
};
```

## 🚨 Error Handling

### **Common OAuth Errors**
```typescript
const OAUTH_ERRORS = {
  access_denied: 'User denied access',
  invalid_request: 'Invalid OAuth request',
  invalid_client: 'Invalid client ID',
  invalid_grant: 'Invalid authorization code',
  unsupported_grant_type: 'Unsupported grant type',
};
```

### **Network Error Handling**
```typescript
// Add firewall message for token exchange failures
if (tokenExchangeFailed) {
  throw new Error(`If you have a firewall, please add "${tenantURL}" to your allowlist.`);
}
```

## 🎯 Implementation Checklist

Your AI agent should implement:

- ✅ **PKCE flow** with proper code verifier/challenge generation
- ✅ **State validation** for CSRF protection
- ✅ **Secure random generation** using crypto APIs
- ✅ **Proper URL construction** with all required parameters
- ✅ **Token exchange** using tenant URL endpoint
- ✅ **Session storage** with proper structure
- ✅ **Tenant URL validation** against .augmentcode.com
- ✅ **Timeout handling** for OAuth state
- ✅ **Error handling** for all failure cases
- ✅ **API integration** using tenant URL as base

## 🔗 Integration with Remote Agent API

```typescript
// After successful OAuth
const session = await oauth.handleCallback();

// Initialize Remote Agent API
const api = createRemoteAgentAPI({
  baseUrl: session.tenantURL, // Use tenant URL!
  apiKey: session.accessToken,
});

// Now you can create remote agents
const agent = await api.createRemoteAgent(prompt, workspaceSetup);
```

## 📞 Client Registration

To get a proper client ID for your web app:

1. **Contact Augment Support**: <EMAIL>
2. **Provide Details**:
   - Application name and description
   - Your domain(s)
   - Redirect URI(s)
   - Expected usage/scope
3. **Get Official Client ID**: Replace 'augment-web-ui' with official ID

This configuration matches exactly how Augment's official extensions implement OAuth, so your implementation should work seamlessly with their authentication system!
