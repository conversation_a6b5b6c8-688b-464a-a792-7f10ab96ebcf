/**
 * Popup-based OAuth implementation with proper PKCE flow
 */
import {
	generateCodeChallenge,
	generateCodeVerifier,
	generateState
} from '$lib/utils/crypto-utils';

export interface PopupOAuthConfig {
	clientId: string;
	authUrl: string;
	redirectUri?: string;
	scopes: string[];
}

export interface OAuthResult {
	code: string;
	state: string;
	tenantUrl: string;
}

export class PopupOAuth {
	private config: PopupOAuthConfig;
	private codeVerifier: string | null = null;
	private state: string | null = null;

	constructor(config: PopupOAuthConfig) {
		this.config = config;
	}

	/**
	 * Start OAuth flow in a popup window
	 */
	async startAuthFlow(): Promise<OAuthResult> {
		// Generate PKCE parameters
		this.codeVerifier = generateCodeVerifier();
		const codeChallenge = await generateCodeChallenge(this.codeVerifier);
		this.state = generateState();

		// Build authorization URL
		const authUrl = this.buildAuthUrl(codeChallenge, this.state);

		console.log('Starting popup OAuth flow:', authUrl);

		// Open popup window
		const popup = window.open(
			authUrl,
			'oauth_popup',
			'width=600,height=700,scrollbars=yes,resizable=yes'
		);

		if (!popup) {
			throw new Error('Failed to open popup window. Please allow popups for this site.');
		}

		// Wait for popup to complete
		return new Promise((resolve, reject) => {
			const messageHandler = (event: MessageEvent) => {
				// Verify origin
				if (event.origin !== window.location.origin) {
					return;
				}

				if (event.data.type === 'oauth_success') {
					window.removeEventListener('message', messageHandler);

					// Verify state
					if (event.data.state !== this.state) {
						reject(new Error('Invalid state parameter'));
						return;
					}

					resolve({
						code: event.data.code,
						state: event.data.state,
						tenantUrl: event.data.tenantUrl
					});
				} else if (event.data.type === 'oauth_error') {
					window.removeEventListener('message', messageHandler);
					reject(new Error(event.data.error));
				}
			};

			// Listen for messages from popup
			window.addEventListener('message', messageHandler);

			// Check if popup was closed manually
			const checkClosed = setInterval(() => {
				if (popup.closed) {
					clearInterval(checkClosed);
					window.removeEventListener('message', messageHandler);
					reject(new Error('OAuth popup was closed'));
				}
			}, 1000);
		});
	}

	/**
	 * Exchange authorization code for access token
	 */
	async exchangeCodeForToken(code: string, tenantUrl: string): Promise<any> {
		if (!this.codeVerifier) {
			throw new Error('No code verifier available');
		}

		console.log('Exchanging code for token with PKCE...');

		const response = await fetch('/api/auth/token', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				code,
				tenantUrl,
				codeVerifier: this.codeVerifier,
				clientId: this.config.clientId,
				redirectUri: this.config.redirectUri
			})
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
			throw new Error(`Token exchange failed: ${errorData.error || response.statusText}`);
		}

		const tokenResponse = await response.json();
		console.log('Token exchange successful');

		// Clear PKCE data
		this.codeVerifier = null;
		this.state = null;

		return tokenResponse;
	}

	private buildAuthUrl(codeChallenge: string, state: string): string {
		const params = new URLSearchParams({
			response_type: 'code',
			client_id: this.config.clientId,
			redirect_uri: this.config.redirectUri ?? 'http://127.0.0.1/api/augment/auth/result',
			scope: this.config.scopes.join(' '),
			state,
			code_challenge: codeChallenge,
			code_challenge_method: 'S256'
		});

		return `${this.config.authUrl}/authorize?${params.toString()}`;
	}
}

/**
 * Create a popup OAuth client
 */
export function createPopupOAuth(config: PopupOAuthConfig): PopupOAuth {
	return new PopupOAuth(config);
}
