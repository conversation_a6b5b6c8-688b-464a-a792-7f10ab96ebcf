/**
 * Web-compatible OAuth implementation for Augment Code
 *
 * This module provides OAuth authentication for web applications
 * to obtain API tokens for use with the Remote Agent API.
 */

import {
	PUBLIC_TENANT_URL,
	PUBLIC_DEV_TENANT_URL,
	PUBLIC_STAGING_TENANT_URL
} from '$env/static/public';
import {
	generateCodeChallenge,
	generateCodeVerifier,
	generateState
} from '$lib/utils/crypto-utils';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

export interface AugmentOAuthConfig {
	clientId: string;
	redirectUri: string;
	scope?: string[];
	authUrl?: string;
}

export interface OAuthState {
	codeVerifier: string;
	codeChallenge: string;
	state: string;
	creationTime: number;
}

export interface AugmentSession {
	accessToken: string;
	tenantUrl: string;
	scopes: string[];
	expiresAt?: number;
}

export interface TokenResponse {
	access_token: string;
	token_type: string;
	expires_in?: number;
	scope?: string;
}

// ============================================================================
// OAUTH CLIENT CLASS
// ============================================================================

export class AugmentOAuth {
	private config: AugmentOAuthConfig;
	private currentState: OAuthState | null = null;

	constructor(config: Partial<AugmentOAuthConfig> = {}) {
		this.config = {
			clientId: config.clientId || 'augment-web-ui',
			redirectUri: config.redirectUri ?? `${window.location.origin}/auth/callback`,
			scope: config.scope || ['email'],
			authUrl: config.authUrl || 'https://auth-central.dev-igor.us-central1.dev.augmentcode.com'
		};
	}

	// ========================================================================
	// PUBLIC METHODS
	// ========================================================================

	/**
	 * Start the OAuth flow by redirecting to Augment's authorization server
	 */
	async startAuthFlow(): Promise<void> {
		// Generate OAuth state
		const state = await this.generateOAuthState();
		this.currentState = state;

		// Store state in sessionStorage for retrieval after redirect
		sessionStorage.setItem('augment_oauth_state', JSON.stringify(state));

		// Build authorization URL
		const authUrl = this.buildAuthorizationUrl(state);
		console.log('OAuth config:', this.config);
		console.log('Redirecting to:', authUrl);

		// Redirect to authorization server
		window.location.href = authUrl;
	}

	/**
	 * Handle the OAuth callback after redirect
	 */
	async handleCallback(callbackUrl?: string): Promise<AugmentSession> {
		const url = callbackUrl || window.location.href;
		const urlParams = new URLSearchParams(new URL(url).search);

		// Get stored state
		const storedStateJson = sessionStorage.getItem('augment_oauth_state');
		if (!storedStateJson) {
			throw new Error('No OAuth state found in session storage');
		}

		const storedState: OAuthState = JSON.parse(storedStateJson);

		// Verify state parameter
		const returnedState = urlParams.get('state');
		if (!returnedState || returnedState !== storedState.state) {
			throw new Error('Invalid OAuth state parameter');
		}

		// Get authorization code
		const code = urlParams.get('code');
		if (!code) {
			const error = urlParams.get('error');
			const errorDescription = urlParams.get('error_description');
			throw new Error(`OAuth error: ${error} - ${errorDescription}`);
		}

		// Get tenant URL from OAuth response or use environment-specific fallback
		let tenantUrl = urlParams.get('tenant_url') || PUBLIC_TENANT_URL;

		// Override tenant URL based on auth environment to ensure compatibility
		// If using staging auth, use staging tenant URL; if using dev auth, use dev tenant URL
		if (this.config.authUrl?.includes('auth-staging.augmentcode.com')) {
			tenantUrl =
				PUBLIC_STAGING_TENANT_URL || 'https://staging-igor.us-central.api.augmentcode.com/';
			console.log('Using staging tenant URL for staging auth environment:', tenantUrl);
		} else if (this.config.authUrl?.includes('dev-igor.us-central1.dev.augmentcode.com')) {
			tenantUrl = PUBLIC_DEV_TENANT_URL || 'https://dev-igor.us-central.api.augmentcode.com/';
			console.log('Using dev tenant URL for dev auth environment:', tenantUrl);
		}

		if (!tenantUrl) {
			console.warn(
				'No tenant URL returned from OAuth flow, attempting to fetch fallback from server...'
			);

			if (!tenantUrl) {
				throw new Error('No tenant URL returned from OAuth flow and no fallback available');
			}
		}
		console.log('Final tenantUrl:', tenantUrl);

		// Exchange code for token
		const session = await this.exchangeCodeForToken(code, tenantUrl, storedState);
		console.log('session', session);

		// Clean up stored state
		sessionStorage.removeItem('augment_oauth_state');

		// Store session
		this.storeSession(session);

		return session;
	}

	/**
	 * Check if user is authenticated
	 * Note: This method is kept for compatibility but authentication state
	 * should be checked via the auth store which uses server-side sessions
	 */
	isAuthenticated(): boolean {
		// Authentication state is now managed server-side via secure cookies
		// This method always returns false - use the auth store instead
		console.warn('isAuthenticated is deprecated - use the auth store instead');
		return false;
	}

	// ========================================================================
	// PRIVATE METHODS
	// ========================================================================

	private async generateOAuthState(): Promise<OAuthState> {
		// Generate code verifier (random string)
		const codeVerifier = generateCodeVerifier();

		// Generate code challenge (SHA256 hash of verifier, base64url encoded)
		const codeChallenge = await generateCodeChallenge(codeVerifier);

		// Generate state (random string for CSRF protection)
		const state = generateState();

		return {
			codeVerifier,
			codeChallenge,
			state,
			creationTime: Date.now()
		};
	}

	private buildAuthorizationUrl(oauthState: OAuthState): string {
		const params = new URLSearchParams({
			response_type: 'code',
			client_id: this.config.clientId,
			redirect_uri: this.config.redirectUri,
			scope: this.config.scope!.join(' '),
			state: oauthState.state,
			code_challenge: oauthState.codeChallenge,
			code_challenge_method: 'S256',
			prompt: 'login'
		});

		return `${this.config.authUrl}/authorize?${params.toString()}`;
	}

	private async exchangeCodeForToken(
		code: string,
		tenantUrl: string,
		state: OAuthState
	): Promise<AugmentSession> {
		// Validate tenant URL
		this.validateTenantUrl(tenantUrl);

		// Use server-side proxy to avoid CORS issues
		const response = await fetch('/api/auth/token', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'X-Auth-Url': this.config.authUrl || ''
			},
			body: JSON.stringify({
				code,
				tenantUrl,
				codeVerifier: state.codeVerifier,
				clientId: this.config.clientId,
				redirectUri: this.config.redirectUri
			})
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
			throw new Error(`Token exchange failed: ${errorData.error || response.statusText}`);
		}

		const tokenResponse: TokenResponse = await response.json();

		const expiresAt = tokenResponse.expires_in
			? Date.now() + tokenResponse.expires_in * 1000
			: undefined;

		return {
			accessToken: tokenResponse.access_token,
			tenantUrl,
			scopes: this.config.scope!,
			expiresAt
		};
	}

	private storeSession(session: AugmentSession): void {
		// Session storage is now handled server-side via secure cookies
		// This method is kept for compatibility but doesn't store anything locally
		console.log('Session storage is now handled server-side for tenant:', session.tenantUrl);
	}

	private validateTenantUrl(tenantUrl: string): void {
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				throw new Error(`Invalid tenant URL: ${tenantUrl}. Must end with .augmentcode.com`);
			}
		} catch (error) {
			if (error instanceof TypeError) {
				throw new Error(`Invalid tenant URL format: ${tenantUrl}`);
			}
			throw error;
		}
	}
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Create an OAuth client with default configuration
 */
export function createAugmentOAuth(config?: Partial<AugmentOAuthConfig>): AugmentOAuth {
	return new AugmentOAuth(config);
}

/**
 * Quick setup for common use cases
 */
export async function setupAugmentAuth(
	options: {
		clientId?: string;
		redirectUri?: string;
		onSuccess?: (session: AugmentSession) => void;
		onError?: (error: Error) => void;
	} = {}
): Promise<AugmentOAuth> {
	const oauth = createAugmentOAuth({
		clientId: options.clientId,
		redirectUri: options.redirectUri
	});

	// Check if we're on the callback page
	const urlParams = new URLSearchParams(window.location.search);
	if (urlParams.has('code') || urlParams.has('error')) {
		try {
			const session = await oauth.handleCallback();
			options.onSuccess?.(session);
		} catch (error) {
			options.onError?.(error as Error);
		}
	}

	return oauth;
}
