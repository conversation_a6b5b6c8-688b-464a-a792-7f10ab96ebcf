/**
 * Web-optimized OAuth 2.0 implementation for Augment
 *
 * This implementation follows standard web OAuth patterns instead of
 * the desktop-focused flow in augment-oauth-web.ts
 */
import {
	generateCodeChallenge,
	generateCodeVerifier,
	generateState
} from '$lib/utils/crypto-utils';

export interface WebOAuthConfig {
	clientId: string;
	redirectUri: string;
	authUrl: string;
	scopes: string[];
}

export interface OAuthState {
	state: string;
	codeVerifier: string;
	codeChallenge: string;
	createdAt: number;
}

export interface TokenResponse {
	access_token: string;
	token_type: string;
	expires_in?: number;
	refresh_token?: string;
	scope?: string;
}

export interface UserSession {
	accessToken: string;
	tokenType: string;
	expiresAt?: number;
	refreshToken?: string;
	scopes: string[];
}

export class WebOAuth {
	private config: WebOAuthConfig;
	private readonly STATE_KEY = 'oauth_state';
	private readonly SESSION_KEY = 'user_session';

	constructor(config: WebOAuthConfig) {
		this.config = config;
	}

	/**
	 * Start the OAuth authorization flow
	 */
	async startAuthFlow(): Promise<void> {
		// Generate PKCE parameters
		const codeVerifier = generateCodeVerifier();
		const codeChallenge = await generateCodeChallenge(codeVerifier);
		const state = generateState();

		// Store state for verification
		const oauthState: OAuthState = {
			state,
			codeVerifier,
			codeChallenge,
			createdAt: Date.now()
		};

		sessionStorage.setItem(this.STATE_KEY, JSON.stringify(oauthState));

		// Build authorization URL
		const authUrl = this.buildAuthUrl(state, codeChallenge);

		console.log('Starting OAuth flow with URL:', authUrl);

		// Redirect to authorization server
		window.location.href = authUrl;
	}

	/**
	 * Handle the OAuth callback
	 */
	async handleCallback(callbackUrl?: string): Promise<UserSession> {
		const url = new URL(callbackUrl || window.location.href);
		const params = new URLSearchParams(url.search);

		// Check for errors
		const error = params.get('error');
		if (error) {
			const errorDescription = params.get('error_description') || 'Unknown error';
			throw new Error(`OAuth error: ${error} - ${errorDescription}`);
		}

		// Get authorization code
		const code = params.get('code');
		if (!code) {
			throw new Error('No authorization code received');
		}

		// Get and verify state
		const returnedState = params.get('state');
		const storedStateJson = sessionStorage.getItem(this.STATE_KEY);

		if (!storedStateJson) {
			throw new Error('No stored OAuth state found');
		}

		const storedState: OAuthState = JSON.parse(storedStateJson);

		if (!returnedState || returnedState !== storedState.state) {
			throw new Error('Invalid state parameter');
		}

		// Check state age (max 10 minutes)
		if (Date.now() - storedState.createdAt > 10 * 60 * 1000) {
			throw new Error('OAuth state expired');
		}

		// Exchange code for tokens
		const session = await this.exchangeCodeForTokens(code, storedState.codeVerifier);

		// Clean up
		sessionStorage.removeItem(this.STATE_KEY);

		// Store session
		this.storeSession(session);

		return session;
	}

	/**
	 * Check if user is authenticated
	 * Note: This method is kept for compatibility but authentication state
	 * should be checked via the auth store which uses server-side sessions
	 */
	isAuthenticated(): boolean {
		// Authentication state is now managed server-side via secure cookies
		// This method always returns false - use the auth store instead
		console.warn('isAuthenticated is deprecated - use the auth store instead');
		return false;
	}

	// Private methods

	private buildAuthUrl(state: string, codeChallenge: string): string {
		const params = new URLSearchParams({
			response_type: 'code',
			client_id: this.config.clientId,
			redirect_uri: this.config.redirectUri,
			scope: this.config.scopes.join(' '),
			state,
			code_challenge: codeChallenge,
			code_challenge_method: 'S256'
		});

		return `${this.config.authUrl}/authorize?${params.toString()}`;
	}

	private async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<UserSession> {
		const tokenUrl = `${this.config.authUrl}/oauth/token`;

		const body = new URLSearchParams({
			grant_type: 'authorization_code',
			client_id: this.config.clientId,
			code,
			redirect_uri: this.config.redirectUri,
			code_verifier: codeVerifier
		});

		const response = await fetch(tokenUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
				Accept: 'application/json'
			},
			body: body.toString()
		});

		if (!response.ok) {
			const errorText = await response.text();
			throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
		}

		const tokenResponse: TokenResponse = await response.json();

		const expiresAt = tokenResponse.expires_in
			? Date.now() + tokenResponse.expires_in * 1000
			: undefined;

		return {
			accessToken: tokenResponse.access_token,
			tokenType: tokenResponse.token_type,
			expiresAt,
			refreshToken: tokenResponse.refresh_token,
			scopes: this.config.scopes
		};
	}

	private storeSession(session: UserSession): void {
		// Session storage is now handled server-side via secure cookies
		// This method is kept for compatibility but doesn't store anything locally
		console.log('Session storage is now handled server-side for scopes:', session.scopes);
	}
}

/**
 * Create a web OAuth client
 */
export function createWebOAuth(config: WebOAuthConfig): WebOAuth {
	return new WebOAuth(config);
}
