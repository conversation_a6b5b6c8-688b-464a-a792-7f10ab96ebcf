import type { SetupScript, SetupScriptsResponse } from '$lib/types';

/**
 * Fetch all available setup scripts
 */
export async function fetchSetupScripts(): Promise<SetupScriptsResponse> {
	const response = await fetch('/api/setup-scripts');

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
		throw new Error(`Failed to fetch setup scripts: ${errorData.error || response.statusText}`);
	}

	return response.json();
}

/**
 * Create a new setup script
 */
export async function createSetupScript(name: string, content: string): Promise<SetupScript> {
	const response = await fetch('/api/setup-scripts', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({ name, content })
	});

	if (!response.ok) {
		const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
		throw new Error(`Failed to create setup script: ${errorData.error || response.statusText}`);
	}

	return response.json();
}

/**
 * Get setup scripts by location
 */
export function getScriptsByLocation(scripts: SetupScript[], location: SetupScript['location']): SetupScript[] {
	return scripts.filter(script => script.location === location);
}

/**
 * Get template scripts (excluding the generate option)
 */
export function getTemplateScripts(scripts: SetupScript[]): SetupScript[] {
	return scripts.filter(script => script.location === 'template' && !script.isGenerateOption);
}

/**
 * Get user-created scripts (from browser/uploaded)
 */
export function getUserScripts(scripts: SetupScript[]): SetupScript[] {
	return scripts.filter(script => script.location === 'browser' || script.location === 'uploaded');
}

/**
 * Find the generate option
 */
export function getGenerateOption(scripts: SetupScript[]): SetupScript | undefined {
	return scripts.find(script => script.isGenerateOption);
}

/**
 * Validate script content for basic shell script syntax
 */
export function validateScriptContent(content: string): { isValid: boolean; errors: string[] } {
	const errors: string[] = [];

	// Check if content is not empty
	if (!content.trim()) {
		errors.push('Script content cannot be empty');
	}

	// Check for shebang
	if (!content.startsWith('#!')) {
		errors.push('Script should start with a shebang (e.g., #!/bin/bash)');
	}

	// Basic syntax checks
	const lines = content.split('\n');
	for (let i = 0; i < lines.length; i++) {
		const line = lines[i].trim();

		// Skip empty lines and comments
		if (!line || line.startsWith('#')) continue;

		// Check for unclosed quotes (basic check)
		const singleQuotes = (line.match(/'/g) || []).length;
		const doubleQuotes = (line.match(/"/g) || []).length;

		if (singleQuotes % 2 !== 0) {
			errors.push(`Line ${i + 1}: Unclosed single quote`);
		}

		if (doubleQuotes % 2 !== 0) {
			errors.push(`Line ${i + 1}: Unclosed double quote`);
		}
	}

	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * Generate a default script template
 */
export function generateDefaultScript(projectType?: string): string {
	const templates = {
		node: `#!/bin/bash
# Node.js project setup script

echo "Setting up Node.js project..."

# Install dependencies
if [ -f "package.json" ]; then
    echo "Installing npm dependencies..."
    npm install
else
    echo "No package.json found, initializing new project..."
    npm init -y
fi

echo "Node.js setup complete!"`,

		python: `#!/bin/bash
# Python project setup script

echo "Setting up Python project..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install dependencies
if [ -f "requirements.txt" ]; then
    echo "Installing Python dependencies..."
    pip install -r requirements.txt
elif [ -f "pyproject.toml" ]; then
    echo "Installing dependencies with pip..."
    pip install .
fi

echo "Python setup complete!"`,

		generic: `#!/bin/bash
# Generic project setup script

echo "Setting up project..."

# Add your setup commands here
# For example:
# - Install dependencies
# - Set up environment variables
# - Create necessary directories
# - Download required files

echo "Setup complete!"`
	};

	return templates[projectType as keyof typeof templates] || templates.generic;
}
