/**
 * Chat API module for handling chat completions
 */

export interface ChatMessage {
	role: 'user' | 'assistant' | 'system';
	content: string;
}

export interface StreamingChunk {
	type: 'content_delta' | 'error' | 'done';
	text?: string;
	error?: string;
}

export interface SilentExchangeRequest {
	message: string;
	selectedCode?: string;
	path?: string;
	lang?: string;
	prefix?: string;
	suffix?: string;
	model?: string;
	mode?: number; // 0 = CHAT, 1 = AGENT, 2 = CLI_AGENT
}

export interface SilentExchangeResponse {
	text: string;
	unknown_blob_names?: string[];
	checkpoint_not_found?: boolean;
	workspace_file_chunks?: any[];
	incorporated_external_sources?: any[];
	nodes?: any[];
	stop_reason?: string;
}

export interface ChatCompletionResponse {
	id: string;
	object: string;
	created: number;
	model: string;
	choices: Array<{
		index: number;
		message: ChatMessage;
		finish_reason: string;
	}>;
	usage?: {
		prompt_tokens: number;
		completion_tokens: number;
		total_tokens: number;
	};
}

/**
 * Send a streaming chat completion request
 */
export async function sendStreamingChatCompletion(
	message: string,
	onChunk: (chunk: StreamingChunk) => void
): Promise<void> {
	try {
		const response = await fetch('/api/chat/silent-stream', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({ message })
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const reader = response.body?.getReader();
		const decoder = new TextDecoder();

		if (!reader) {
			throw new Error('No response body');
		}

		while (true) {
			const { done, value } = await reader.read();

			if (done) {
				onChunk({ type: 'done' });
				break;
			}

			const chunk = decoder.decode(value);
			const lines = chunk.split('\n');

			for (const line of lines) {
				if (line.startsWith('data: ')) {
					const data = line.slice(6);

					if (data === '[DONE]') {
						onChunk({ type: 'done' });
						return;
					}

					try {
						const parsed = JSON.parse(data);
						if (parsed.choices?.[0]?.delta?.content) {
							onChunk({
								type: 'content_delta',
								text: parsed.choices[0].delta.content
							});
						}
					} catch (e) {
						console.error('Error parsing SSE data:', e);
					}
				}
			}
		}
	} catch (error) {
		onChunk({
			type: 'error',
			error: error instanceof Error ? error.message : 'Unknown error'
		});
		throw error;
	}
}

/**
 * Send a non-streaming chat completion request
 */
export async function sendChatCompletion(messages: ChatMessage[]): Promise<ChatCompletionResponse> {
	const response = await fetch('/api/chat/completion', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({ messages })
	});

	if (!response.ok) {
		throw new Error(`HTTP error! status: ${response.status}`);
	}

	return response.json();
}

/**
 * Extract text content from a chat completion response
 */
export function extractTextFromResponse(response: ChatCompletionResponse): string {
	return response.choices?.[0]?.message?.content || '';
}

/**
 * Send a silent exchange request (non-streaming)
 */
export async function sendSilentExchange(
	request: SilentExchangeRequest
): Promise<SilentExchangeResponse> {
	const response = await fetch('/api/chat/silent', {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(request)
	});

	if (!response.ok) {
		throw new Error(`HTTP error! status: ${response.status}`);
	}

	return response.json();
}

/**
 * Send a streaming silent exchange request
 */
export async function sendSilentExchangeStream(
	request: SilentExchangeRequest,
	onChunk: (chunk: StreamingChunk) => void
): Promise<void> {
	try {
		const response = await fetch('/api/chat/silent-stream', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(request)
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const reader = response.body?.getReader();
		const decoder = new TextDecoder();

		if (!reader) {
			throw new Error('No response body');
		}

		while (true) {
			const { done, value } = await reader.read();

			if (done) {
				onChunk({ type: 'done' });
				break;
			}

			const chunk = decoder.decode(value);
			const lines = chunk.split('\n');

			for (const line of lines) {
				if (line.startsWith('data: ')) {
					const data = line.slice(6);

					if (data === '[DONE]') {
						onChunk({ type: 'done' });
						return;
					}

					try {
						const parsed = JSON.parse(data);
						if (parsed.text) {
							onChunk({
								type: 'content_delta',
								text: parsed.text
							});
						}
					} catch (e) {
						console.error('Error parsing SSE data:', e);
					}
				}
			}
		}
	} catch (error) {
		onChunk({
			type: 'error',
			error: error instanceof Error ? error.message : 'Unknown error'
		});
		throw error;
	}
}
