export interface GitHubRepo {
	owner: string;
	name: string;
	html_url?: string;
	created_at?: string;
	updated_at?: string;
	default_branch?: string;
	description?: string;
	private?: boolean;
	fork?: boolean;
	archived?: boolean;
	disabled?: boolean;
	language?: string;
	stargazers_count?: number;
	forks_count?: number;
}

export interface GitHubBranch {
	name: string;
	commit: {
		sha: string;
		url: string;
	};
	protected: boolean;
}

export interface ListGithubReposResponse {
	repos: GitHubRepo[];
}

export interface ListGithubRepoBranchesRequest {
	repo: {
		owner: string;
		name: string;
	};
	page?: number; // Optional, 1-based pagination
}

export interface ListGithubRepoBranchesResponse {
	branches: GitHubBranch[];
	has_next_page: boolean;
	next_page: number;
}

/**
 * GitHub API client for repository and branch operations
 */
export class GitHubAPI {
	private baseUrl = '/github';

	/**
	 * List user's GitHub repositories
	 */
	async listRepos(): Promise<GitHubRepo[]> {
		try {
			const response = await fetch(`${this.baseUrl}/list-repos`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({})
			});

			if (!response.ok) {
				throw new Error(`Failed to fetch repositories: ${response.statusText}`);
			}

			const data: ListGithubReposResponse = await response.json();
			return data.repos || [];
		} catch (error) {
			console.error('Error fetching GitHub repositories:', error);
			throw error;
		}
	}

	/**
	 * List branches for a specific repository
	 */
	async listBranches(
		owner: string,
		name: string,
		page = 1
	): Promise<ListGithubRepoBranchesResponse> {
		try {
			const request: ListGithubRepoBranchesRequest = {
				repo: { owner, name },
				page
			};

			const response = await fetch(`${this.baseUrl}/list-branches`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(request)
			});

			if (!response.ok) {
				throw new Error(`Failed to fetch branches: ${response.statusText}`);
			}

			const data: ListGithubRepoBranchesResponse = await response.json();
			return data;
		} catch (error) {
			console.error('Error fetching GitHub branches:', error);
			throw error;
		}
	}

	/**
	 * Search repositories by name
	 */
	async searchRepos(query: string): Promise<GitHubRepo[]> {
		const repos = await this.listRepos();
		if (!query.trim()) return repos;

		const searchTerm = query.toLowerCase();
		return repos.filter(
			(repo) =>
				repo.name.toLowerCase().includes(searchTerm) ||
				repo.owner.toLowerCase().includes(searchTerm) ||
				(repo.description || '').toLowerCase().includes(searchTerm)
		);
	}

	/**
	 * Search branches by name
	 */
	async searchBranches(owner: string, name: string, query: string): Promise<GitHubBranch[]> {
		const response = await this.listBranches(owner, name);
		if (!query.trim()) return response.branches;

		const searchTerm = query.toLowerCase();
		return response.branches.filter((branch) => branch.name.toLowerCase().includes(searchTerm));
	}

	/**
	 * Get repository URL from owner and name
	 */
	getRepoUrl(owner: string, name: string): string {
		return `https://github.com/${owner}/${name}`;
	}

	/**
	 * Parse repository URL to extract owner and name
	 */
	parseRepoUrl(url: string): { owner: string; name: string } | null {
		try {
			const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
			if (match) {
				return {
					owner: match[1],
					name: match[2].replace(/\.git$/, '')
				};
			}
			return null;
		} catch {
			return null;
		}
	}
}

// Singleton instance
export const githubAPI = new GitHubAPI();

/**
 * Utility functions for GitHub data formatting
 */
export const GitHubUtils = {
	/**
	 * Format repository for display in combobox
	 */
	formatRepoLabel(repo: GitHubRepo): string {
		return `${repo.owner}/${repo.name}`;
	},

	/**
	 * Format repository description for combobox
	 */
	formatRepoDescription(repo: GitHubRepo): string {
		const parts: string[] = [];

		if (repo.language) {
			parts.push(repo.language);
		}

		if (repo.private) {
			parts.push('Private');
		}

		return parts.join(' • ');
	},

	/**
	 * Format branch for display in combobox
	 */
	formatBranchLabel(branch: GitHubBranch): string {
		return branch.name;
	},

	/**
	 * Format branch description for combobox
	 */
	formatBranchDescription(branch: GitHubBranch): string {
		const parts: string[] = [];

		if (branch.protected) {
			parts.push('Protected');
		}

		parts.push(`SHA: ${branch.commit.sha.substring(0, 7)}`);

		return parts.join(' • ');
	},

	/**
	 * Get repository key for combobox
	 */
	getRepoKey(repo: GitHubRepo): string {
		return `${repo.owner}/${repo.name}`;
	},

	/**
	 * Get branch key for combobox
	 */
	getBranchKey(branch: GitHubBranch): string {
		return branch.name;
	}
};
