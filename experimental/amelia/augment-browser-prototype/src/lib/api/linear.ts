/**
 * Linear API Client
 *
 * Provides methods to fetch issues from Linear using the GraphQL API
 */

import type { LinearIssue, LinearIssueState, LinearLabel, LinearUser } from '$lib/types';

export interface LinearConfig {
	token?: string; // Optional since we use secure proxy
	baseUrl?: string;
}

export class LinearAPI {
	private config: LinearConfig;

	constructor(config: LinearConfig = {}) {
		this.config = {
			baseUrl: 'https://api.linear.app/graphql',
			...config
		};
	}

	/**
	 * Fetch issues from a Linear project
	 */
	async getIssues(
		projectId?: string,
		options: {
			first?: number;
			after?: string;
			filter?: {
				state?: string;
				assignee?: string;
			};
		} = {}
	): Promise<{ issues: LinearIssue[]; hasNextPage: boolean; endCursor?: string }> {
		const { first = 50, after, filter } = options;

		let filterCondition = '';
		if (projectId) {
			filterCondition += `project: { id: { eq: "${projectId}" } }`;
		}
		if (filter?.state) {
			filterCondition += filterCondition
				? `, state: { name: { eq: "${filter.state}" } }`
				: `state: { name: { eq: "${filter.state}" } }`;
		}
		if (filter?.assignee) {
			filterCondition += filterCondition
				? `, assignee: { id: { eq: "${filter.assignee}" } }`
				: `assignee: { id: { eq: "${filter.assignee}" } }`;
		}

		const query = `
			query GetIssues($first: Int!, $after: String, $filter: IssueFilter) {
				issues(first: $first, after: $after, filter: $filter, orderBy: updatedAt) {
					pageInfo {
						hasNextPage
						endCursor
					}
					nodes {
						id
						identifier
						title
						description
						priority
						createdAt
						updatedAt
						url
						state {
							id
							name
							type
						}
						labels {
							nodes {
								id
								name
								color
							}
						}
						assignee {
							id
							name
							email
							avatarUrl
						}
						creator {
							id
							name
							email
							avatarUrl
						}
					}
				}
			}
		`;

		const variables: any = {
			first,
			after
		};

		if (filterCondition) {
			variables.filter = {};
			if (projectId) {
				variables.filter.project = { id: { eq: projectId } };
			}
			if (filter?.state) {
				variables.filter.state = { name: { eq: filter.state } };
			}
			if (filter?.assignee) {
				variables.filter.assignee = { id: { eq: filter.assignee } };
			}
		}

		const response = await this.makeRequest(query, variables);

		if (!response.data?.issues) {
			throw new Error('Failed to fetch issues from Linear');
		}

		const { nodes, pageInfo } = response.data.issues;

		const issues: LinearIssue[] = nodes.map((node: any) => ({
			id: node.id,
			identifier: node.identifier,
			title: node.title,
			description: node.description,
			state: {
				id: node.state.id,
				name: node.state.name,
				type: node.state.type
			},
			priority: node.priority,
			labels: node.labels.nodes.map((label: any) => ({
				id: label.id,
				name: label.name,
				color: label.color
			})),
			assignee: node.assignee
				? {
						id: node.assignee.id,
						name: node.assignee.name,
						email: node.assignee.email,
						avatarUrl: node.assignee.avatarUrl
					}
				: undefined,
			creator: {
				id: node.creator.id,
				name: node.creator.name,
				email: node.creator.email,
				avatarUrl: node.creator.avatarUrl
			},
			createdAt: node.createdAt,
			updatedAt: node.updatedAt,
			url: node.url
		}));

		return {
			issues,
			hasNextPage: pageInfo.hasNextPage,
			endCursor: pageInfo.endCursor
		};
	}

	/**
	 * Get all projects
	 */
	async getProjects(
		options: {
			first?: number;
			after?: string;
		} = {}
	): Promise<{ projects: any[]; hasNextPage: boolean; endCursor?: string }> {
		const { first = 50, after } = options;

		const query = `
			query GetProjects($first: Int!, $after: String) {
				projects(first: $first, after: $after) {
					pageInfo {
						hasNextPage
						endCursor
					}
					nodes {
						id
						name
						description
						url
						state
						color
						icon
						createdAt
						updatedAt
					}
				}
			}
		`;

		const variables = { first, after };
		const response = await this.makeRequest(query, variables);

		if (!response.data?.projects) {
			throw new Error('Failed to fetch projects from Linear');
		}

		const { nodes, pageInfo } = response.data.projects;

		return {
			projects: nodes,
			hasNextPage: pageInfo.hasNextPage,
			endCursor: pageInfo.endCursor
		};
	}

	/**
	 * Get current user information
	 */
	async getViewer() {
		const query = `
			query GetViewer {
				viewer {
					id
					name
					email
					avatarUrl
				}
			}
		`;

		const response = await this.makeRequest(query);

		if (!response.data?.viewer) {
			throw new Error('Failed to fetch user information from Linear');
		}

		return response.data.viewer;
	}

	/**
	 * Make a GraphQL request to Linear API via secure proxy
	 */
	private async makeRequest(query: string, variables: Record<string, any> = {}) {
		// Use secure proxy endpoint instead of direct API access
		const response = await fetch('/api/linear/graphql', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				query,
				variables
			}),
			credentials: 'include' // Include httpOnly cookies for authentication
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
			throw new Error(`Linear API request failed: ${errorData.error || response.statusText}`);
		}

		const data = await response.json();

		if (data.errors || data.graphqlErrors) {
			const errors = data.graphqlErrors || data.errors;
			throw new Error(`Linear API error: ${errors.map((e: any) => e.message).join(', ')}`);
		}

		return data;
	}
}

/**
 * Create a Linear API client instance
 *
 * @deprecated Token parameter is no longer needed. Use createLinearAPI() without parameters.
 */
export function createLinearAPI(config: LinearConfig = {}): LinearAPI {
	if (config.token) {
		console.warn(
			'Linear token parameter is deprecated. Authentication is now handled securely via httpOnly cookies.'
		);
	}
	return new LinearAPI();
}
