/**
 * Unified API Client for Tenant Operations
 *
 * Centralizes all tenant API calls through a single proxy endpoint with:
 * - Consistent session management
 * - Automatic case conversion (camelCase <-> snake_case)
 * - Smart timeout handling
 * - Proper error handling
 * - Type safety
 */

import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
import {
	convertProviderEntityToUnified,
	convertBackendEntityToUnified,
	type UnifiedEntity
} from '$lib/utils/entity-conversion';
import type { TriggerConditions, TriggerConfiguration } from '$lib/types/trigger-enums';
import type { ListGithubSetupScriptsResponse, ReadGithubSetupScriptResponse } from '$lib/types';

// ============================================================================
// TRIGGER UPDATE REQUEST TYPE
// ============================================================================

export interface UpdateTriggerRequest {
	trigger_id: string;
	configuration: TriggerConfiguration;
}

// ============================================================================
// REMOTE AGENT TYPES (migrated from legacy client)
// ============================================================================

export interface GithubCommitRef {
	repositoryUrl: string;
	gitRef: string;
}

export interface RemoteAgentWorkspaceSetup {
	startingFiles?: {
		githubCommitRef?: GithubCommitRef;
	};
}

export enum RemoteAgentStatus {
	AGENT_UNSPECIFIED = 0,
	AGENT_STARTING = 1,
	AGENT_RUNNING = 2,
	AGENT_IDLE = 3,
	AGENT_FAILED = 4,
	AGENT_PENDING = 5,
	AGENT_PENDING_DELETION = 8
}

export enum RemoteAgentWorkspaceStatus {
	REMOTE_AGENT_WORKSPACE_STATUS_UNSPECIFIED = 0,
	REMOTE_AGENT_WORKSPACE_STATUS_RUNNING = 1,
	REMOTE_AGENT_WORKSPACE_STATUS_PAUSING = 2,
	REMOTE_AGENT_WORKSPACE_STATUS_PAUSED = 3,
	REMOTE_AGENT_WORKSPACE_STATUS_RESUMING = 4,
	REMOTE_AGENT_WORKSPACE_STATUS_DELETING = 5
}

export interface RemoteAgentSSHConfig {
	host: string;
	port: number;
	username: string;
	privateKey?: string;
}

export interface RemoteAgent {
	remoteAgentId: string;
	workspaceSetup: RemoteAgentWorkspaceSetup;
	status: RemoteAgentStatus;
	startedAt: string;
	updatedAt: string;
	sessionSummary: string;
	turnSummaries: string[];
	sshConfig?: RemoteAgentSSHConfig;
	isSetupScriptAgent?: boolean;
	hasUpdates?: boolean;
	workspaceStatus: RemoteAgentWorkspaceStatus;
	expiresAt: string;
	title?: string;
}

export interface CreateRemoteAgentResponse {
	remoteAgentId: string;
	status: RemoteAgentStatus;
}

export interface RemoteAgentChatResponse {
	// Empty response - actual chat appears in history stream
}

// ============================================================================
// AGENT LIST STREAMING TYPES
// ============================================================================

/**
 * AgentListUpdateType defines the different types of updates that can be sent in an agent list stream.
 * Each update type corresponds to a specific field in the AgentListUpdate message.
 */
export enum AgentListUpdateType {
	/** Default unspecified value - should not be used */
	AGENT_LIST_UPDATE_TYPE_UNSPECIFIED = 0,

	/** A new agent was created */
	AGENT_LIST_AGENT_ADDED = 1,

	/** An existing agent was updated (status, workspace status, or other properties changed) */
	AGENT_LIST_AGENT_UPDATED = 2,

	/** An agent was deleted. */
	AGENT_LIST_AGENT_DELETED = 3,

	/** Initial state: all current agents (sent when client first connects) */
	AGENT_LIST_ALL_AGENTS = 4
}

/**
 * AgentListUpdate represents a single update in the agent list stream.
 * Only one of the optional fields should be set, determined by the 'type' field.
 * This allows for different types of updates to be sent in the same stream.
 */
export interface AgentListUpdate {
	/** The type of update, which determines which field is set */
	type: AgentListUpdateType;

	/** The timestamp when this update occurred */
	updateTimestamp: string; // ISO string format

	/** For AGENT_LIST_AGENT_ADDED, AGENT_LIST_AGENT_UPDATED, and AGENT_LIST_ALL updates */
	agent?: RemoteAgent;

	/** For AGENT_LIST_AGENT_DELETED updates - just the agent ID */
	deletedAgentId?: string;

	/** For AGENT_LIST_ALL updates - all current agents */
	allAgents?: RemoteAgent[];

	/** The maximum number of total remote agents this user can have */
	maxAgents?: number;

	/** The maximum number of active remote agents this user can have (not including paused agents) */
	maxActiveAgents?: number;
}

export enum FileChangeType {
	added = 0,
	deleted = 1,
	modified = 2,
	renamed = 3
}

export interface ChangedFile {
	newPath?: string;
	oldPath?: string;
	newContents?: string;
	oldContents?: string;
	changeType: string;
	diff?: string;
	linesAdded?: number;
	linesDeleted?: number;
}

export enum ChatRequestNodeType {
	TEXT = 0,
	TOOL_RESULT = 1,
	IMAGE = 2,
	IMAGE_ID = 3,
	IDE_STATE = 4,
	EDIT_EVENTS = 5,
	CHECKPOINT_REF = 6
}

export enum ChatResultNodeType {
	// The raw response from the model.
	// Content is a single string with raw response.
	RAW_RESPONSE = 0,

	// Our guess of what user will ask next.
	// Content is "{question1}\n{question2}".
	SUGGESTED_QUESTIONS = 1,

	// Indication that streaming of the main response finished.
	// Content is always empty.
	MAIN_TEXT_FINISHED = 2,

	// Workspace file chunks used in prompt
	// Every line in content is "{file_path}:{char_start}-{char_end}".
	WORKSPACE_FILE_CHUNKS = 3,

	// Sources that were useful to generate the response
	// Every line in content is "{file_path}".
	RELEVANT_SOURCES = 4,

	// Tool use requested by the AI model.
	// When tool use generation begins, TOOL_USE_START is sent with name and id.
	// When the tool use is fully generated, TOOL_USE will be sent with all fields
	// populated completely.  Real streaming TBD.
	TOOL_USE = 5,

	// Agent memory content
	AGENT_MEMORY = 6,

	TOOL_USE_START = 7,

	// Thinking/reasoning content from the model (e.g., from OpenAI's reasoning_content)
	THINKING = 8
}

export interface ChatRequestText {
	content: string;
}

export interface ChatRequestNode {
	id: number;
	type: ChatRequestNodeType;
	textNode?: ChatRequestText;
	imageUrl?: string;
	filePath?: string;
	fileContent?: string;
}

export interface ChatRequest {
	nodes: ChatRequestNode[];
}

export interface ChatResponseNode {
	type: string;
	content: string;
}

export interface ChatResponse {
	nodes: ChatResponseNode[];
}

export interface Exchange {
	id: string;
	request: ChatRequest;
	response?: ChatResponse;
	createdAt: string;
	updatedAt: string;
}

// Legacy interface - deprecated in favor of CleanChatExchangeData
// export interface RemoteAgentExchange {
// 	exchange: Exchange;
// 	changedFiles: ChangedFile[];
// 	sequenceId: number;
// 	turnSummary?: string;
// 	sessionSummary?: string;
// 	changedFilesSkippedCount?: number;
// }

// ============================================================================
// CHAT HISTORY API TYPES (snake_case format from API)
// ============================================================================

export interface ApiChatRequestNode {
	id: number;
	type: number;
	textNode?: {
		content: string;
	} | null;
	toolResultNode?: any | null;
	imageNode?: any | null;
	ideStateNode?: {
		workspaceFolders: Array<{
			repositoryRoot: string;
			folderRoot: string;
		}>;
		workspaceFoldersUnchanged: boolean;
		currentTerminal: {
			terminalId: number;
			currentWorkingDirectory: string;
		};
	} | null;
	editEventsNode?: any | null;
}

export interface ChatResultToolUse {
	/* eslint-disable @typescript-eslint/naming-convention */
	tool_use_id: string;
	tool_name: string;
	input_json: string;
	mcp_server_name?: string; // MCP server name for MCP tools
	mcp_tool_name?: string; // Original MCP tool name without server suffix
	requestId?: string;
	/* eslint-enable @typescript-eslint/naming-convention */
}

export interface ChatResultAgentMemory {
	content: string;
	isFlushed?: boolean;
}

export interface ChatResultNode {
	/* eslint-disable @typescript-eslint/naming-convention */
	id: number;
	type: ChatResultNodeType;
	content: string;
	tool_use?: ChatResultToolUse;
	agent_memory?: ChatResultAgentMemory;
	requestId?: string;
	/* eslint-enable @typescript-eslint/naming-convention */
}

export interface ApiChatExchange {
	requestMessage: string;
	responseText?: string;
	requestId: string;
	requestNodes: ApiChatRequestNode[];
	responseNodes?: ChatResultNode[];
}

export interface ApiChatExchangeData {
	exchange: ApiChatExchange;
	changedFiles: any[];
	sequenceId: number;
	turnSummary?: string | null;
	finishedAt?: string;
	changedFilesSkipped?: any[];
	changedFilesSkippedCount?: number;
}

export interface ApiChatExchangeUpdate {
	requestId: string;
	sequenceId: number;
	appendedText: string;
	appendedNodes: ApiChatRequestNode[];
	appendedChangedFiles: any[];
}

// ============================================================================
// CLEAN CHAT HISTORY TYPES (camelCase format for client)
// ============================================================================

export interface CleanChatRequestNode {
	id: number;
	type: number;
	textNode?: {
		content: string;
	} | null;
	toolResultNode?: any | null;
	imageNode?: any | null;
	ideStateNode?: {
		workspaceFolders: Array<{
			repositoryRoot: string;
			folderRoot: string;
		}>;
		workspaceFoldersUnchanged: boolean;
		currentTerminal: {
			terminalId: number;
			currentWorkingDirectory: string;
		};
	} | null;
	editEventsNode?: any | null;
}

export interface CleanChatResponseNode {
	id: number;
	type: number;
	content?: string;
	textNode?: {
		content: string;
	} | null;
	toolUse?: {
		toolUseId: string;
		toolName: string;
		inputJson: string;
		mcpServerName?: string;
		mcpToolName?: string;
		requestId?: string;
	};
	agentMemory?: {
		content: string;
		isFlushed?: boolean;
	};
	// Keep snake_case versions for compatibility with existing code
	tool_use?: ChatResultToolUse;
	agent_memory?: ChatResultAgentMemory;
}

export interface CleanChatExchange {
	requestMessage: string;
	responseText?: string;
	requestId: string;
	requestNodes: CleanChatRequestNode[];
	responseNodes?: CleanChatResponseNode[];
}

export interface CleanChatExchangeData {
	exchange: CleanChatExchange;
	changedFiles: any[];
	sequenceId: number;
	turnSummary?: string | null;
	finishedAt?: string;
	changedFilesSkipped?: any[];
	changedFilesSkippedCount?: number;
}

export interface CleanChatExchangeUpdate {
	requestId: string;
	sequenceId: number;
	appendedText: string;
	appendedNodes: (CleanChatRequestNode | CleanChatResponseNode)[];
	appendedChangedFiles: any[];
}

export interface GetRemoteAgentChatHistoryResponse {
	chatHistory: CleanChatExchangeData[];
	sessionSummary?: string;
	remoteAgent?: RemoteAgent;
}

// Simplified chat request interface for data-operations
export interface SimpleChatRequest {
	remoteAgentId: string;
	message: string;
	userGuidelines?: string;
	workspaceGuidelines?: string;
	agentMemories?: string;
}

// Full chat request interface matching API
export interface RemoteAgentChatRequest {
	remoteAgentId: string;
	requestDetails: {
		requestNodes: ChatRequestNode[];
		userGuidelines?: string;
		workspaceGuidelines?: string;
		agentMemories?: string;
		modelId?: string;
	};
}

// ============================================================================
// CLEAN INTERNAL TYPES (for application use)
// ============================================================================

export interface CleanRemoteAgent {
	id: string;
	title: string; // Cleaned from sessionSummary
	renamedTitle?: string; // Renamed from title
	status: RemoteAgentStatus;
	workspaceStatus: RemoteAgentWorkspaceStatus;
	startedAt: Date;
	updatedAt: Date;
	expiresAt: Date;
	sessionSummary: string;
	isSetupScriptAgent: boolean;
	hasUpdates: boolean;
	githubUrl?: string; // Extracted from workspaceSetup
	githubRef?: string; // Extracted from workspaceSetup
	sshConfig?: RemoteAgentSSHConfig;
}

export interface CleanChangedFile {
	path: string; // Normalized from filePath/newPath
	oldPath?: string; // For renames
	content?: string; // Normalized from newContents
	oldContent?: string; // Normalized from oldContents
	changeType: 'added' | 'deleted' | 'modified' | 'renamed' | 'added_then_deleted';
	diff?: string;
	linesAdded?: number;
	linesDeleted?: number;
}

export interface CleanChatMessage {
	id: string;
	role: 'user' | 'assistant';
	content: string;
	timestamp: Date;
	metadata?: {
		sequenceId?: number;
		turnSummary?: string;
		changedFiles?: CleanChangedFile[];
	};
}

export interface CleanChatSession {
	id: string;
	agentId: string;
	messages: CleanChatMessage[];
	exchanges: CleanChatExchangeData[];
	isStreaming: boolean;
	streamingContent: string;
	lastSequenceId: number;
	sessionSummary?: string;
	error: string | null;
}

export interface CleanExecution {
	id: string;
	remoteAgentId?: string;
	status: string;
	errorMessage?: string;
	startedAt: Date;
	completedAt?: Date;
	triggerId: string;
	eventId: string;
	// Parsed from eventPayload
	entityId?: string;
	entityType?: string;
	manualExecution?: boolean;
	// Raw eventPayload for debugging/fallback
	eventPayload?: string;
}

export interface CleanTriggerExecution {
	id: string;
	triggerId: string;
	status: string;
	remoteAgentId?: string;
	startedAt: Date | undefined;
	// Parsed from eventPayload
	entityId?: string;
	entityType?: string;
	manualExecution?: boolean;
	// Raw eventPayload for debugging/fallback
	eventPayload?: string;
}

// ============================================================================
// CONVERSION FUNCTIONS
// ============================================================================

import { extractInitialInstructionsFromSessionSummary } from '$lib/utils/remote-agent-utils';
import { getEntityTypeParameter } from '$lib/utils/dashboard-entity-operations';

/**
 * Convert API RemoteAgent to clean internal format
 */
export function convertRemoteAgentFromAPI(apiAgent: RemoteAgent): CleanRemoteAgent {
	const cleanTitle =
		apiAgent.title ||
		extractInitialInstructionsFromSessionSummary(apiAgent.sessionSummary) ||
		'Untitled Agent';

	return {
		id: apiAgent.remoteAgentId,
		title: cleanTitle,
		renamedTitle: apiAgent.title,
		status: apiAgent.status,
		workspaceStatus: apiAgent.workspaceStatus,
		startedAt: new Date(apiAgent.startedAt),
		updatedAt: new Date(apiAgent.updatedAt),
		expiresAt: new Date(apiAgent.expiresAt),
		sessionSummary: apiAgent.sessionSummary,
		isSetupScriptAgent: apiAgent.isSetupScriptAgent || false,
		hasUpdates: apiAgent.hasUpdates || false,
		githubUrl: apiAgent.workspaceSetup.startingFiles?.githubCommitRef?.repositoryUrl,
		githubRef: apiAgent.workspaceSetup.startingFiles?.githubCommitRef?.gitRef,
		sshConfig: apiAgent.sshConfig
	};
}

/**
 * Convert API ExecutionResult to clean internal format
 */
export function convertExecutionFromAPI(apiExecution: ExecutionResult): CleanExecution {
	// Parse eventPayload JSON if it exists
	let entityId: string | undefined;
	let entityType: string | undefined;
	let manualExecution: boolean | undefined;

	if (apiExecution.eventPayload) {
		try {
			const payload = JSON.parse(apiExecution.eventPayload);
			entityId = payload.entity_id;
			entityType = payload.entity_type;
			manualExecution = payload.manual_execution;
		} catch (error) {
			console.warn('Failed to parse eventPayload JSON:', error);
		}
	}

	return {
		id: apiExecution.executionId,
		remoteAgentId: apiExecution.remoteAgentId,
		status: apiExecution.status,
		errorMessage: apiExecution.errorMessage,
		startedAt: new Date(apiExecution.startedAt),
		completedAt: apiExecution.completedAt ? new Date(apiExecution.completedAt) : undefined,
		triggerId: apiExecution.triggerId,
		eventId: apiExecution.eventId,
		entityId,
		entityType,
		manualExecution,
		eventPayload: apiExecution.eventPayload
	};
}

/**
 * Convert API TriggerExecution to clean internal format
 */
export function convertTriggerExecutionFromAPI(
	apiExecution: TriggerExecution
): CleanTriggerExecution {
	// Parse eventPayload JSON if it exists
	let entityId: string | undefined;
	let entityType: string | undefined;
	let manualExecution: boolean | undefined;

	if (apiExecution.eventPayload) {
		try {
			const payload = JSON.parse(apiExecution.eventPayload);
			entityId = payload.entity_id;
			entityType = payload.entity_type;
			manualExecution = payload.manual_execution;
		} catch (error) {
			console.warn('Failed to parse eventPayload JSON:', error);
		}
	}

	return {
		id: apiExecution.executionId,
		triggerId: apiExecution.triggerId,
		status: apiExecution.status,
		remoteAgentId: apiExecution.remoteAgentId,
		startedAt: apiExecution.startedAt ? new Date(apiExecution.startedAt) : undefined,
		entityId,
		entityType,
		manualExecution,
		eventPayload: apiExecution.eventPayload
	};
}

/**
 * Convert API ChangedFile to clean internal format
 */
export function convertChangedFileFromAPI(apiFile: ChangedFile): CleanChangedFile {
	// Use the path directly from the API file
	const path = apiFile.path || apiFile.newPath || '';

	// Convert string changeType to typed format
	let changeType: 'added' | 'deleted' | 'modified' | 'renamed';
	switch (apiFile.changeType) {
		case 'added':
		case '0':
			changeType = 'added';
			break;
		case 'deleted':
		case '1':
			changeType = 'deleted';
			break;
		case 'modified':
		case '2':
			changeType = 'modified';
			break;
		case 'renamed':
		case '3':
			changeType = 'renamed';
			break;
		default:
			changeType = 'modified';
	}

	return {
		path,
		oldPath: apiFile.oldPath,
		content: apiFile.newContents,
		oldContent: apiFile.oldContents,
		changeType,
		diff: apiFile.diff,
		linesAdded: apiFile.linesAdded,
		linesDeleted: apiFile.linesDeleted
	};
}

// /**
//  * Convert API RemoteAgentExchange to clean chat messages
//  * @deprecated - Use CleanChatExchangeData format instead
//  */
// export function convertExchangeToMessages(exchange: RemoteAgentExchange): CleanChatMessage[] {
// 	const messages: CleanChatMessage[] = [];
//
// 	// Add user message - with null checks
// 	if (exchange.exchange.request?.nodes && exchange.exchange.request.nodes.length > 0) {
// 		const userNode = exchange.exchange.request.nodes[0];
// 		if (userNode.text_node?.content) {
// 			messages.push({
// 				id: `${exchange.exchange.id}-user`,
// 				role: 'user',
// 				content: userNode.text_node.content,
// 				timestamp: new Date(exchange.exchange.createdAt),
// 				metadata: {
// 					sequenceId: exchange.sequenceId,
// 					turnSummary: exchange.turnSummary
// 				}
// 			});
// 		}
// 	}
//
// 	// Add assistant message - with null checks
// 	if (exchange.exchange.response?.nodes && exchange.exchange.response.nodes.length > 0) {
// 		const assistantContent = exchange.exchange.response.nodes
// 			.map((node) => node.content)
// 			.join('\n');
//
// 		messages.push({
// 			id: `${exchange.exchange.id}-assistant`,
// 			role: 'assistant',
// 			content: assistantContent,
// 			timestamp: new Date(exchange.exchange.updatedAt),
// 			metadata: {
// 				sequenceId: exchange.sequenceId,
// 				turnSummary: exchange.turnSummary,
// 				changedFiles: exchange.changedFiles?.map(convertChangedFileFromAPI) || []
// 			}
// 		});
// 	}
//
// 	return messages;
// }

/**
 * Convert API chat exchange data to clean client format
 */
export function convertChatExchangeDataFromAPI(
	apiData: ApiChatExchangeData
): CleanChatExchangeData {
	// Convert response nodes and handle the case where nodes are minimal but exchange has responseText
	let responseNodes: CleanChatResponseNode[] | undefined;
	if (apiData.exchange.responseNodes) {
		responseNodes = apiData.exchange.responseNodes.map(convertChatResultNodeFromAPI);

		// If we have responseText but no response nodes, create one
		if (apiData.exchange.responseText && responseNodes.length === 0) {
			responseNodes = [
				{
					id: 1,
					type: 0, // RAW_RESPONSE
					content: apiData.exchange.responseText,
					textNode: { content: apiData.exchange.responseText }
				}
			];
		}
		// If we have responseText but response nodes don't have content, populate the first text node
		else if (apiData.exchange.responseText && responseNodes.length > 0) {
			const firstTextNode = responseNodes.find(
				(node) => node.type === 0 && (!node.content || node.content.trim() === '')
			);
			if (firstTextNode) {
				firstTextNode.content = apiData.exchange.responseText;
				firstTextNode.textNode = { content: apiData.exchange.responseText };
			}
		}
	}

	return {
		exchange: {
			requestMessage: apiData.exchange.requestMessage,
			responseText: apiData.exchange.responseText,
			requestId: apiData.exchange.requestId,
			requestNodes: apiData.exchange.requestNodes.map(convertChatRequestNodeFromAPI),
			responseNodes
		},
		changedFiles: apiData.changedFiles || [],
		sequenceId: apiData.sequenceId,
		turnSummary: apiData.turnSummary,
		finishedAt: apiData.finishedAt,
		changedFilesSkipped: apiData.changedFilesSkipped || [],
		changedFilesSkippedCount: apiData.changedFilesSkippedCount || 0
	};
}

/**
 * Convert API chat exchange update to clean client format
 */
export function convertChatExchangeUpdateFromAPI(
	apiUpdate: ApiChatExchangeUpdate
): CleanChatExchangeUpdate {
	// Convert appended nodes - these are typically response nodes during streaming
	// Note: For streaming text updates, we don't create new nodes here since the text
	// should be appended to existing response nodes in handleExchangeUpdate
	const convertedNodes = apiUpdate.appendedNodes.map((node) => {
		// Check if this looks like a response node (minimal structure with just id/type)
		// or if it has response-specific fields
		const isResponseNode =
			!node.textNode &&
			!node.toolResultNode &&
			!node.imageNode &&
			!node.ideStateNode &&
			!node.editEventsNode;

		if (isResponseNode) {
			// Convert as response node and populate content from appendedText if needed
			const responseNode = convertChatResultNodeFromAPI(node as any);

			// If the node has no content but we have appendedText, use it
			if ((!responseNode.content || responseNode.content.trim() === '') && apiUpdate.appendedText) {
				responseNode.content = apiUpdate.appendedText;
				responseNode.textNode = { content: apiUpdate.appendedText };
			}

			return responseNode;
		} else {
			// Convert as request node
			return convertChatRequestNodeFromAPI(node);
		}
	});

	return {
		requestId: apiUpdate.requestId,
		sequenceId: apiUpdate.sequenceId,
		appendedText: apiUpdate.appendedText,
		appendedNodes: convertedNodes,
		appendedChangedFiles: apiUpdate.appendedChangedFiles || []
	};
}

/**
 * Convert API chat request node to clean client format
 */
export function convertChatRequestNodeFromAPI(apiNode: ApiChatRequestNode): CleanChatRequestNode {
	return {
		id: apiNode.id,
		type: apiNode.type,
		textNode: apiNode.textNode,
		toolResultNode: apiNode.toolResultNode,
		imageNode: apiNode.imageNode,
		ideStateNode: apiNode.ideStateNode,
		editEventsNode: apiNode.editEventsNode
	};
}

/**
 * Convert API chat result node to clean client format
 * This creates a structure that works well with the ProcessedNode interface
 */
export function convertChatResultNodeFromAPI(apiNode: ChatResultNode): CleanChatResponseNode {
	// Handle both snake_case (original API) and camelCase (after toCamelCase conversion)
	const toolUseData = (apiNode as any).tool_use || (apiNode as any).toolUse;
	const agentMemoryData = (apiNode as any).agent_memory || (apiNode as any).agentMemory;

	// Create the base clean response node structure
	const cleanNode: CleanChatResponseNode = {
		id: apiNode.id,
		type: apiNode.type,
		content: apiNode.content,
		textNode: apiNode.content ? { content: apiNode.content } : undefined,
		tool_use: toolUseData, // Keep snake_case for compatibility
		agent_memory: agentMemoryData // Keep snake_case for compatibility
	};

	// Add camelCase versions for better TypeScript support
	if (toolUseData) {
		cleanNode.toolUse = {
			toolUseId: toolUseData.tool_use_id || toolUseData.toolUseId,
			toolName: toolUseData.tool_name || toolUseData.toolName,
			inputJson: toolUseData.input_json || toolUseData.inputJson,
			mcpServerName: toolUseData.mcp_server_name || toolUseData.mcpServerName,
			mcpToolName: toolUseData.mcp_tool_name || toolUseData.mcpToolName,
			requestId: toolUseData.requestId
		};
	}

	if (agentMemoryData) {
		cleanNode.agentMemory = {
			content: agentMemoryData.content,
			isFlushed: agentMemoryData.isFlushed
		};
	}

	return cleanNode;
}

export interface RequestOptions {
	method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
	body?: any;
	timeout?: number;
}

export interface TriggerExecution {
	executionId: string;
	triggerId: string;
	status: string;
	remoteAgentId?: string;
	eventPayload?: any;
	startedAt: string;
}

export interface CreateAgentRequest {
	initialRequestDetails: {
		requestNodes: ChatRequestNode[];
	};
	workspaceSetup: RemoteAgentWorkspaceSetup;
	setupScript?: string;
	isSetupScriptAgent?: boolean;
	modelId?: string;
	userGuidelines?: string;
	workspaceGuidelines?: string;
	agentMemories?: string;
}

export interface ChatRequest {
	remoteAgentId: string;
	message: string;
	userGuidelines?: string;
	workspaceGuidelines?: string;
	agentMemories?: string;
	modelId?: string;
}

export interface ExecutionResult {
	executionId: string;
	remoteAgentId?: string;
	status: string;
	errorMessage?: string;
	startedAt: string;
	completedAt?: string;
	eventPayload?: string;
	triggerId: string;
	eventId: string;
}

// ============================================================================
// API ERROR CLASS
// ============================================================================

export class APIError extends Error {
	constructor(
		public status: number,
		message: string,
		public details?: string
	) {
		super(message);
		this.name = 'APIError';
	}
}

// ============================================================================
// CASE CONVERSION UTILITIES
// ============================================================================

export function toSnakeCase(obj: any): any {
	if (obj === null || obj === undefined) return obj;
	if (Array.isArray(obj)) return obj.map(toSnakeCase);

	if (typeof obj === 'object' && obj.constructor === Object) {
		const result: any = {};
		for (const [key, value] of Object.entries(obj)) {
			const snakeKey = key.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
			result[snakeKey] = toSnakeCase(value);
		}
		return result;
	}

	return obj;
}

export function toCamelCase(obj: any): any {
	if (obj === null || obj === undefined) return obj;
	if (Array.isArray(obj)) return obj.map(toCamelCase);

	if (typeof obj === 'object' && obj.constructor === Object) {
		const result: any = {};
		for (const [key, value] of Object.entries(obj)) {
			const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
			result[camelKey] = toCamelCase(value);
		}
		return result;
	}

	return obj;
}

// ============================================================================
// UNIFIED API CLIENT
// ============================================================================

export class UnifiedAPIClient {
	private baseUrl = '/api';

	/**
	 * Make a request to the local API endpoints
	 */
	async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
		const { method = 'GET', body, timeout = 30000 } = options;

		try {
			const response = await fetch(`${this.baseUrl}${endpoint}`, {
				method,
				headers: {
					'Content-Type': 'application/json'
				},
				body: body ? JSON.stringify(toSnakeCase(body)) : undefined,
				signal: AbortSignal.timeout(timeout),
				credentials: 'include' // Include cookies for authentication
			});

			if (!response.ok) {
				let errorData: any = {};
				try {
					errorData = await response.json();
				} catch {
					// If JSON parsing fails, try to get text content for debugging
					const textContent = await response.text().catch(() => 'Unknown error');
					console.error('API Error - Non-JSON response:', {
						status: response.status,
						statusText: response.statusText,
						url: response.url,
						contentType: response.headers.get('content-type'),
						textContent: textContent.substring(0, 500) // First 500 chars for debugging
					});
				}

				// Create a clean, user-friendly error message
				let errorMessage = errorData.error;
				if (!errorMessage) {
					// Provide user-friendly messages for common HTTP status codes
					switch (response.status) {
						case 500:
							errorMessage = 'Internal server error - please try again later';
							break;
						case 502:
							errorMessage = 'Service temporarily unavailable - please try again later';
							break;
						case 503:
							errorMessage = 'Service unavailable - please try again later';
							break;
						case 504:
							errorMessage = 'Request timeout - please try again later';
							break;
						case 404:
							errorMessage = 'Resource not found';
							break;
						case 403:
							errorMessage = 'Access denied';
							break;
						case 401:
							errorMessage = 'Authentication required';
							break;
						case 400:
							errorMessage = 'Invalid request';
							break;
						default:
							errorMessage = `HTTP ${response.status}: ${response.statusText}`;
					}
				}

				throw new APIError(response.status, errorMessage, errorData.details);
			}

			let data;
			try {
				data = await response.json();
			} catch (jsonError) {
				// If JSON parsing fails, try to get text content for debugging
				const textContent = await response.text().catch(() => 'Unknown content');
				console.error('API Response - Invalid JSON:', {
					status: response.status,
					url: response.url,
					contentType: response.headers.get('content-type'),
					textContent: textContent.substring(0, 500), // First 500 chars for debugging
					jsonError: jsonError instanceof Error ? jsonError.message : 'Unknown JSON error'
				});
				throw new APIError(response.status, 'Invalid JSON response from API');
			}
			return toCamelCase(data) as T;
		} catch (error) {
			if (error instanceof APIError) {
				throw error;
			}

			// Handle network errors, timeouts, etc.
			const message = error instanceof Error ? error.message : 'Network error';
			throw new APIError(0, message);
		}
	}

	// ============================================================================
	// AGENTS API
	// ============================================================================

	agents = {
		/**
		 * List all remote agents (returns clean types)
		 */
		list: async (): Promise<{
			remoteAgents: CleanRemoteAgent[];
			maxRemoteAgents?: number;
			maxActiveRemoteAgents?: number;
		}> => {
			const response = await this.request<{
				remoteAgents: RemoteAgent[];
				max_remote_agents?: number;
				max_active_remote_agents?: number;
			}>('/remote-agents/list', {
				method: 'POST',
				body: {} // Empty request body as required by API
			});
			// Convert to clean types
			const cleanAgents = (response.remoteAgents || []).map(convertRemoteAgentFromAPI);
			return {
				...toCamelCase(response),
				remoteAgents: cleanAgents
			};
		},

		/**
		 * Get a specific remote agent (returns clean type)
		 */
		get: async (id: string): Promise<CleanRemoteAgent> => {
			const apiAgent = await this.request<RemoteAgent>(`/remote-agents/${id}`, { method: 'GET' });
			return convertRemoteAgentFromAPI(apiAgent);
		},

		/**
		 * List all remote agents (raw API types - for backward compatibility)
		 */
		listRaw: async (): Promise<{ remoteAgents: RemoteAgent[] }> => {
			const response = await this.request<{ remoteAgents: RemoteAgent[] }>('/remote-agents/list', {
				method: 'POST',
				body: {} // Empty request body as required by API
			});
			return { remoteAgents: response.remoteAgents || [] };
		},

		/**
		 * Get a specific remote agent (raw API type - for backward compatibility)
		 */
		getRaw: (id: string): Promise<RemoteAgent> =>
			this.request(`/remote-agents/${id}`, { method: 'GET' }),

		/**
		 * Create a new remote agent
		 */
		create: (data: CreateAgentRequest): Promise<CreateRemoteAgentResponse> =>
			this.request('/remote-agents/create', {
				method: 'POST',
				body: data,
				timeout: 60000 // Longer timeout for creation
			}),

		/**
		 * Delete a remote agent
		 */
		delete: (id: string): Promise<void> =>
			this.request('/remote-agents/delete', {
				method: 'POST',
				body: { remote_agent_id: id }
			}),

		/**
		 * Send chat message to agent
		 */
		chat: (data: SimpleChatRequest): Promise<RemoteAgentChatResponse> => {
			// Convert simple request to full API request format
			const fullRequest: RemoteAgentChatRequest = {
				remoteAgentId: data.remoteAgentId,
				requestDetails: {
					requestNodes: [
						{
							id: 1,
							type: ChatRequestNodeType.TEXT,
							textNode: {
								content: data.message
							}
						}
					],
					userGuidelines: data.userGuidelines,
					workspaceGuidelines: data.workspaceGuidelines,
					agentMemories: data.agentMemories
				}
			};

			return this.request('/remote-agents/chat', {
				method: 'POST',
				body: fullRequest,
				timeout: 300000 // 5 minute timeout for chat
			});
		},

		/**
		 * Get chat history for agent (returns clean types)
		 */
		getChatHistory: async (agentId: string): Promise<CleanChatSession> => {
			const response = await this.request<GetRemoteAgentChatHistoryResponse>(
				'/remote-agents/get-chat-history',
				{
					method: 'POST',
					body: { remoteAgentId: agentId }
				}
			);

			// Convert raw exchanges to clean format
			const exchanges = (response.chatHistory || []).map((exchange) =>
				convertChatExchangeDataFromAPI(exchange as any)
			);

			// Convert exchanges to clean messages
			const messages: CleanChatMessage[] = [];
			// TODO: Implement clean message conversion if needed
			// for (const exchange of exchanges) {
			//   messages.push(...convertCleanExchangeToMessages(exchange));
			// }

			return {
				id: `chat-${agentId}`,
				agentId,
				messages,
				exchanges,
				isStreaming: false,
				streamingContent: '',
				lastSequenceId: Math.max(...(response.chatHistory || []).map((e) => e.sequenceId), 0),
				sessionSummary: response.sessionSummary,
				error: null
			};
		},

		/**
		 * Get chat history for agent (raw API types - for backward compatibility)
		 */
		getChatHistoryRaw: (agentId: string): Promise<GetRemoteAgentChatHistoryResponse> =>
			this.request('/remote-agents/get-chat-history', {
				method: 'POST',
				body: { remoteAgentId: agentId }
			}),

		/**
		 * Update agent title
		 */
		updateTitle: (id: string, title: string): Promise<void> =>
			this.request('/remote-agents/update', {
				method: 'POST',
				body: { remote_agent_id: id, new_title: title }
			}),

		/**
		 * Interrupt agent execution
		 */
		interrupt: (id: string): Promise<void> =>
			this.request('/remote-agents/interrupt', {
				method: 'POST',
				body: { remoteAgentId: id }
			}),

		/**
		 * Pause agent execution
		 */
		pause: (id: string): Promise<void> =>
			this.request('/remote-agents/pause', {
				method: 'POST',
				body: { remote_agent_id: id }
			}),

		/**
		 * Resume agent execution
		 */
		resume: (id: string): Promise<void> =>
			this.request('/remote-agents/resume', {
				method: 'POST',
				body: { remote_agent_id: id }
			})

		// Duplicate chat function removed - using the one above with SimpleChatRequest
	};

	// ============================================================================
	// TRIGGERS API
	// ============================================================================

	triggers = {
		/**
		 * List all triggers
		 */
		list: async (): Promise<NormalizedTrigger[]> => {
			const response = await this.request<{ triggers: NormalizedTrigger[] }>(
				'/remote-agent-actions/triggers/list',
				{ method: 'POST' }
			);
			return response.triggers || [];
		},

		/**
		 * Create a new trigger
		 */
		create: (
			configuration: TriggerConfiguration
		): Promise<{
			triggerId: string;
			executionId: string;
			configuration: TriggerConfiguration;
		}> =>
			this.request('/remote-agent-actions/triggers/create', {
				method: 'POST',
				body: { configuration }
			}),

		/**
		 * Update trigger configuration
		 */
		update: (
			triggerId: string,
			updates: Partial<TriggerConfiguration>
		): Promise<{
			configuration: TriggerConfiguration;
			updatedAt: string;
		}> => {
			const requestBody: UpdateTriggerRequest = {
				trigger_id: triggerId,
				configuration: updates as TriggerConfiguration
			};
			return this.request('/remote-agent-actions/triggers/update', {
				method: 'POST',
				body: requestBody
			});
		},

		/**
		 * Delete a trigger
		 */
		delete: (triggerId: string): Promise<{ success: boolean }> =>
			this.request('/remote-agent-actions/triggers/delete', {
				method: 'POST',
				body: { trigger_id: triggerId }
			}),

		/**
		 * Get executions for a trigger (returns clean types)
		 */
		executions: async (
			triggerId: string
		): Promise<{ executions: CleanTriggerExecution[]; totalCount: number }> => {
			const response = await this.request<{ executions: TriggerExecution[]; totalCount: number }>(
				'/remote-agent-actions/triggers/executions',
				{
					method: 'POST',
					body: { triggerId }
				}
			);
			return {
				executions: response.executions.map(convertTriggerExecutionFromAPI),
				totalCount: response.totalCount
			};
		},

		/**
		 * Execute trigger manually (returns clean type)
		 */
		execute: async (
			triggerId: string,
			entityId: string,
			extraPrompt?: string
		): Promise<CleanExecution> => {
			const apiExecution = await this.request<ExecutionResult>(
				'/remote-agent-actions/triggers/execute-manually',
				{
					method: 'POST',
					body: { triggerId, entityId, extraPrompt }
				}
			);
			return convertExecutionFromAPI(apiExecution);
		},

		/**
		 * Get matching entities for trigger
		 */
		matchingEntities: (
			triggerId: string,
			options?: {
				limit?: number;
				show_dismissed?: boolean;
				event_source?: number;
				offset?: number;
			}
		): Promise<UnifiedEntity[]> =>
			this.request('/remote-agent-actions/triggers/matching-entities', {
				method: 'POST',
				body: {
					trigger_id: triggerId,
					show_dismissed: false, // Default to false
					event_source: 1, // Default event source
					offset: 0, // Default offset
					...(options || {})
				}
			}),

		/**
		 * Get matching entities for trigger config
		 */
		matchingEntitiesForConfig: (
			triggerConfig: TriggerConditions,
			options?: { limit?: number }
		): Promise<UnifiedEntity[]> =>
			this.request('/remote-agent-actions/triggers/matching-entities', {
				method: 'POST',
				body: { conditions: triggerConfig, ...(options || {}) }
			}),

		/**
		 * Dismiss or un-dismiss an entity for a trigger
		 */
		dismissEntity: async (
			triggerId: string,
			entityId: string,
			dismissed: boolean = true
		): Promise<void> => {
			await this.request('/remote-agent-actions/triggers/dismiss-entity', {
				method: 'POST',
				body: {
					trigger_id: triggerId,
					entity_id: entityId,
					dismissed
				}
			});
		}
	};

	// ============================================================================
	// ENTITIES API
	// ============================================================================

	entities = {
		/**
		 * Get entity details (converted to UnifiedEntity)
		 */
		get: async (
			entityId: string,
			entityType: string,
			providerId: string,
			repository?: string,
			includeRelatedEntities = false
		): Promise<UnifiedEntity> => {
			const entityTypeParam = getEntityTypeParameter(providerId, entityType);
			const response = await this.request<{
				githubEntity?: any;
				linearEntity?: any;
				relatedEntities?: any;
			}>('/remote-agent-actions/get-entity-details', {
				method: 'POST',
				body: {
					entityId,
					repository,
					eventSource: providerId === 'github' ? 1 : providerId === 'linear' ? 2 : 3,
					[`${providerId}EntityType`]: entityTypeParam,
					includeRelatedEntities
				}
			});

			const entity = response.githubEntity || response.linearEntity;

			if (!entity) {
				throw new Error(`Entity not found: ${providerId}:${entityType}:${entityId}`);
			}

			return convertBackendEntityToUnified(providerId, entityType, entity);
		},

		/**
		 * Get raw entity details (full backend response)
		 */
		getRaw: async (
			entityId: string,
			entityType: string,
			providerId: string,
			repository?: string,
			includeRelatedEntities = false
		): Promise<{
			githubEntity?: any;
			linearEntity?: any;
			relatedEntities?: any;
		}> => {
			const entityTypeParam = getEntityTypeParameter(providerId, entityType);
			const response = await this.request<{
				githubEntity?: any;
				linearEntity?: any;
				relatedEntities?: any;
			}>('/remote-agent-actions/get-entity-details', {
				method: 'POST',
				body: {
					entityId,
					repository,
					eventSource: providerId === 'github' ? 1 : providerId === 'linear' ? 2 : 3,
					[`${providerId}EntityType`]: entityTypeParam,
					includeRelatedEntities
				}
			});

			const entity = response.githubEntity || response.linearEntity;

			if (!entity) {
				throw new Error(`Entity not found: ${providerId}:${entityType}:${entityId}`);
			}

			return response;
		},

		/**
		 * Execute manual agent on entity (returns clean type)
		 */
		executeAgent: async (
			entityId: string,
			agentConfig: any,
			entityType?: string
		): Promise<CleanExecution> => {
			const apiExecution = await this.request<ExecutionResult>(
				'/remote-agent-actions/execute-manual-agent',
				{
					method: 'POST',
					body: { entityId, agentConfig, entityType }
				}
			);
			return convertExecutionFromAPI(apiExecution);
		}
	};

	// ============================================================================
	// GITHUB SETUP SCRIPTS API
	// ============================================================================

	githubSetupScripts = {
		/**
		 * List available setup scripts in a GitHub repository
		 */
		list: async (githubRef: string): Promise<ListGithubSetupScriptsResponse> => {
			return this.request('/remote-agents/list-github-setup-scripts', {
				method: 'POST',
				body: { githubRef }
			});
		},

		/**
		 * Read the content of a specific setup script from a GitHub repository
		 */
		read: async (githubRef: string, scriptPath: string): Promise<ReadGithubSetupScriptResponse> => {
			return this.request('/remote-agents/read-github-setup-script', {
				method: 'POST',
				body: { githubRef, scriptPath }
			});
		}
	};
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const apiClient = new UnifiedAPIClient();
