import type { Trigger, TriggerProvider, TriggerCondition } from './types';

// Common field operators
const STRING_OPERATORS: TriggerCondition['operator'][] = [
	'equals',
	'not_equals',
	'contains',
	'starts_with',
	'ends_with',
	'in',
	'not_in'
];
const NUMBER_OPERATORS: TriggerCondition['operator'][] = [
	'equals',
	'not_equals',
	'greater_than',
	'less_than'
];
const BOOLEAN_OPERATORS: TriggerCondition['operator'][] = ['equals', 'not_equals'];
const ARRAY_OPERATORS: TriggerCondition['operator'][] = [
	'includes',
	'not_in',
	'exists',
	'not_exists'
];
const EXISTENCE_OPERATORS: TriggerCondition['operator'][] = ['exists', 'not_exists'];

export const TRIGGER_PROVIDERS: TriggerProvider[] = [
	{
		id: 'github',
		name: 'GitHub',
		description: 'Trigger on GitHub repository events like issues, pull requests, and pushes',
		iconName: 'github',
		documentationUrl: 'https://docs.github.com/en/webhooks/webhook-events-and-payloads',
		events: [
			{
				name: 'issues',
				description: 'Activity relating to issues',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on GitHub issues',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'labeled' &&
							'when labeled',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'unlabeled' &&
							'when unlabeled',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'opened' &&
							'when opened',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'closed' &&
							'when closed',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'reopened' &&
							'when reopened',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'edited' &&
							'when edited'
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Action',
						path: 'action',
						type: 'string',
						description: 'The action performed on the issue',
						example: 'opened',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Issue Number',
						path: 'issue.number',
						type: 'number',
						description: 'The issue number',
						example: 1347,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'Issue Title',
						path: 'issue.title',
						type: 'string',
						description: 'The title of the issue',
						example: 'Bug in authentication',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Issue State',
						path: 'issue.state',
						type: 'string',
						description: 'The state of the issue',
						example: 'open',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Labels',
						path: 'issue.labels',
						type: 'array',
						description: 'Array of labels applied to the issue',
						example: ['bug', 'priority:high'],
						allowedOperators: ARRAY_OPERATORS
					},
					{
						name: 'Assignees',
						path: 'issue.assignees',
						type: 'array',
						description: 'Array of users assigned to the issue',
						example: ['octocat'],
						allowedOperators: ARRAY_OPERATORS
					},
					{
						name: 'Author',
						path: 'issue.user.login',
						type: 'string',
						description: 'The username of the issue author',
						example: 'octocat',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Repository',
						path: 'repository.full_name',
						type: 'string',
						description: 'The full name of the repository',
						example: 'octocat/Hello-World',
						allowedOperators: STRING_OPERATORS
					}
				]
			},
			{
				name: 'pull_request',
				description: 'Activity relating to pull requests',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on GitHub pull requests',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'opened' &&
							'when opened',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'closed' &&
							'when closed',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'reopened' &&
							'when reopened',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'edited' &&
							'when edited',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.changed_files')
							?.value &&
							'when the number of changed files is ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.changed_files')
									?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.number')?.value &&
							'for PR #' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.number')?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.title')?.value &&
							'for PRs with the title ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.title')?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.state')?.value ===
							'open' && 'when the PR is open',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.state')?.value ===
							'closed' && 'when the PR is closed',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.state')?.value ===
							'merged' && 'when the PR is merged',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.draft')?.value ===
							true && 'when the PR is a draft',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.draft')?.value ===
							false && 'when the PR is not a draft',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.author.login')?.value &&
							'by ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.author.login')
									?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.base.ref')?.value &&
							'into ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.base.ref')?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.head.ref')?.value &&
							'from ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.head.ref')?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.additions')?.value &&
							'with ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.additions')?.value +
								' additions',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.deletions')?.value &&
							'with ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.deletions')?.value +
								' deletions',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.changed_files')
							?.value &&
							'with ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.changed_files')
									?.value +
								' changed files',
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.labels')?.value &&
							'with the label ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.labels')?.value,
						trigger.parsedConditions?.find((c) => c.field === 'pull_request.assignees')?.value &&
							'assigned to ' +
								trigger.parsedConditions?.find((c) => c.field === 'pull_request.assignees')?.value
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Action',
						path: 'action',
						type: 'string',
						description: 'The action performed on the pull request',
						example: 'opened',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'PR Number',
						path: 'pull_request.number',
						type: 'number',
						description: 'The pull request number',
						example: 42,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'PR Title',
						path: 'pull_request.title',
						type: 'string',
						description: 'The title of the pull request',
						example: 'Fix authentication bug',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'PR State',
						path: 'pull_request.state',
						type: 'string',
						description: 'The state of the pull request',
						example: 'open',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Base Branch',
						path: 'pull_request.base.ref',
						type: 'string',
						description: 'The base branch of the pull request',
						example: 'main',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Head Branch',
						path: 'pull_request.head.ref',
						type: 'string',
						description: 'The head branch of the pull request',
						example: 'feature/auth-fix',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Changes',
						path: 'pull_request.changed_files',
						type: 'number',
						description: 'Number of files changed',
						example: 5,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'Additions',
						path: 'pull_request.additions',
						type: 'number',
						description: 'Number of lines added',
						example: 100,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'Deletions',
						path: 'pull_request.deletions',
						type: 'number',
						description: 'Number of lines deleted',
						example: 50,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'Labels',
						path: 'pull_request.labels',
						type: 'array',
						description: 'Array of labels applied to the pull request',
						example: ['enhancement', 'review-needed'],
						allowedOperators: ARRAY_OPERATORS
					},
					{
						name: 'Author',
						path: 'pull_request.user.login',
						type: 'string',
						description: 'The username of the pull request author',
						example: 'octocat',
						allowedOperators: STRING_OPERATORS
					}
				]
			},
			{
				name: 'push',
				description: 'Git push to a repository',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on GitHub pushes',
						trigger.parsedConditions?.find((c) => c.field === 'ref')?.value &&
							'to ' + trigger.parsedConditions?.find((c) => c.field === 'ref')?.value
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Branch',
						path: 'ref',
						type: 'string',
						description: 'The full git ref that was pushed',
						example: 'refs/heads/main',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Commits Count',
						path: 'commits.length',
						type: 'number',
						description: 'Number of commits in the push',
						example: 3,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'Pusher',
						path: 'pusher.name',
						type: 'string',
						description: 'The username of the person who pushed',
						example: 'octocat',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Repository',
						path: 'repository.full_name',
						type: 'string',
						description: 'The full name of the repository',
						example: 'octocat/Hello-World',
						allowedOperators: STRING_OPERATORS
					}
				]
			}
		]
	},
	{
		id: 'linear',
		name: 'Linear',
		description: 'Trigger on Linear issue and project events',
		iconName: 'linear',
		documentationUrl: 'https://developers.linear.app/docs/webhooks',
		events: [
			{
				name: 'Issue',
				description: 'Activity relating to Linear issues',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on Linear issues',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'create' &&
							'when created',
						trigger.parsedConditions?.find((c) => c.field === 'action')?.value === 'update' &&
							'when updated'
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Action',
						path: 'action',
						type: 'string',
						description: 'The action performed on the issue',
						example: 'create',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Issue ID',
						path: 'data.id',
						type: 'string',
						description: 'The unique identifier of the issue',
						example: 'ISS-123',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Issue Title',
						path: 'data.title',
						type: 'string',
						description: 'The title of the issue',
						example: 'Fix login bug',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Priority',
						path: 'data.priority',
						type: 'number',
						description: 'The priority of the issue (1-4)',
						example: 1,
						allowedOperators: NUMBER_OPERATORS
					},
					{
						name: 'State',
						path: 'data.state.name',
						type: 'string',
						description: 'The state of the issue',
						example: 'In Progress',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Team',
						path: 'data.team.name',
						type: 'string',
						description: 'The team the issue belongs to',
						example: 'Engineering',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Assignee',
						path: 'data.assignee.email',
						type: 'string',
						description: 'The email of the assigned user',
						example: '<EMAIL>',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Labels',
						path: 'data.labels',
						type: 'array',
						description: 'Array of labels applied to the issue',
						example: ['bug', 'urgent'],
						allowedOperators: ARRAY_OPERATORS
					}
				]
			}
		]
	},
	{
		id: 'slack',
		name: 'Slack',
		description: 'Trigger on Slack events like messages and channel activity',
		iconName: 'slack',
		documentationUrl: 'https://api.slack.com/events-api',
		events: [
			{
				name: 'message',
				description: 'A message was posted to a channel',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on Slack messages',
						trigger.parsedConditions?.find((c) => c.field === 'event.channel')?.value &&
							'in ' + trigger.parsedConditions?.find((c) => c.field === 'event.channel')?.value
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Channel',
						path: 'event.channel',
						type: 'string',
						description: 'The channel where the message was posted',
						example: 'C1234567890',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'User',
						path: 'event.user',
						type: 'string',
						description: 'The user who posted the message',
						example: 'U1234567890',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Text',
						path: 'event.text',
						type: 'string',
						description: 'The message text',
						example: 'Hello world!',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Thread Timestamp',
						path: 'event.thread_ts',
						type: 'string',
						description: 'Timestamp of the parent message if this is a thread reply',
						example: '1234567890.123456',
						allowedOperators: EXISTENCE_OPERATORS
					}
				]
			},
			{
				name: 'reaction_added',
				description: 'A reaction was added to a message',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on Slack reactions',
						trigger.parsedConditions?.find((c) => c.field === 'event.reaction')?.value &&
							'when ' +
								trigger.parsedConditions?.find((c) => c.field === 'event.reaction')?.value +
								' is added'
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Reaction',
						path: 'event.reaction',
						type: 'string',
						description: 'The emoji reaction that was added',
						example: 'thumbsup',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'User',
						path: 'event.user',
						type: 'string',
						description: 'The user who added the reaction',
						example: 'U1234567890',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Channel',
						path: 'event.item.channel',
						type: 'string',
						description: 'The channel containing the message',
						example: 'C1234567890',
						allowedOperators: STRING_OPERATORS
					}
				]
			}
		]
	},
	{
		id: 'datadog',
		name: 'Datadog',
		description: 'Trigger on Datadog alerts and monitoring events',
		iconName: 'datadog',
		documentationUrl: 'https://docs.datadoghq.com/integrations/webhooks/',
		events: [
			{
				name: 'alert',
				description: 'A Datadog alert was triggered',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on Datadog alerts',
						trigger.parsedConditions?.find((c) => c.field === 'alert_type')?.value === 'error' &&
							'when an error occurs',
						trigger.parsedConditions?.find((c) => c.field === 'alert_type')?.value === 'warning' &&
							'when a warning occurs'
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Alert Type',
						path: 'alert_type',
						type: 'string',
						description: 'The type of alert',
						example: 'error',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Priority',
						path: 'priority',
						type: 'string',
						description: 'The priority level of the alert',
						example: 'normal',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Title',
						path: 'title',
						type: 'string',
						description: 'The title of the alert',
						example: 'High CPU usage detected',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Tags',
						path: 'tags',
						type: 'array',
						description: 'Tags associated with the alert',
						example: ['env:production', 'service:api'],
						allowedOperators: ARRAY_OPERATORS
					},
					{
						name: 'Host',
						path: 'host',
						type: 'string',
						description: 'The host that triggered the alert',
						example: 'web-server-01',
						allowedOperators: STRING_OPERATORS
					}
				]
			}
		]
	},
	{
		id: 'pagerduty',
		name: 'PagerDuty',
		description: 'Trigger on PagerDuty incident events',
		iconName: 'pagerduty',
		documentationUrl: 'https://developer.pagerduty.com/docs/webhooks/',
		events: [
			{
				name: 'incident.triggered',
				description: 'An incident was triggered',
				getLabel: (trigger: Trigger) => {
					return [
						'Triggered on PagerDuty incidents',
						trigger.parsedConditions?.find((c) => c.field === 'incident.urgency')?.value ===
							'high' && 'when the urgency is high',
						trigger.parsedConditions?.find((c) => c.field === 'incident.urgency')?.value ===
							'low' && 'when the urgency is low'
					]
						.filter(Boolean)
						.join(' ');
				},
				fields: [
					{
						name: 'Incident ID',
						path: 'incident.id',
						type: 'string',
						description: 'The unique identifier of the incident',
						example: 'PINCIDENT123',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Title',
						path: 'incident.title',
						type: 'string',
						description: 'The title of the incident',
						example: 'Database connection timeout',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Urgency',
						path: 'incident.urgency',
						type: 'string',
						description: 'The urgency level of the incident',
						example: 'high',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Service',
						path: 'incident.service.name',
						type: 'string',
						description: 'The service affected by the incident',
						example: 'API Service',
						allowedOperators: STRING_OPERATORS
					},
					{
						name: 'Escalation Policy',
						path: 'incident.escalation_policy.name',
						type: 'string',
						description: 'The escalation policy for the incident',
						example: 'Engineering Escalation',
						allowedOperators: STRING_OPERATORS
					}
				]
			}
		]
	}
	// {
	// 	id: 'cron',
	// 	name: 'Scheduled',
	// 	description: 'Trigger on scheduled intervals using cron expressions',
	// 	iconName: 'cron',
	// 	events: [
	// 		{
	// 			name: 'schedule',
	// 			description: 'Scheduled trigger based on cron expression',
	// 			getLabel: (trigger: Trigger) => {
	// 				return [
	// 					'Triggered on schedule',
	// 					trigger.parsedConditions?.find((c) => c.field === 'schedule')?.value &&
	// 						'at ' + trigger.parsedConditions?.find((c) => c.field === 'schedule')?.value
	// 				]
	// 					.filter(Boolean)
	// 					.join(' ');
	// 			},
	// 			fields: [
	// 				{
	// 					name: 'Schedule',
	// 					path: 'schedule',
	// 					type: 'string',
	// 					description: 'Cron expression for the schedule',
	// 					example: '0 9 * * *',
	// 					allowedOperators: ['equals' as const]
	// 				},
	// 				{
	// 					name: 'Timezone',
	// 					path: 'timezone',
	// 					type: 'string',
	// 					description: 'Timezone for the schedule',
	// 					example: 'UTC',
	// 					allowedOperators: STRING_OPERATORS
	// 				}
	// 			]
	// 		}
	// 	]
	// }
];

// Helper function to get provider by ID
export function getProviderById(id: string): TriggerProvider | undefined {
	return TRIGGER_PROVIDERS.find((provider) => provider.id === id);
}

// Helper function to get event by provider and event name
export function getEventByName(providerId: string, eventName: string) {
	const provider = getProviderById(providerId);
	return provider?.events.find((event) => event.name === eventName);
}

// Helper function to get all available fields for a provider/event combination
export function getAvailableFields(providerId: string, eventName: string) {
	const event = getEventByName(providerId, eventName);
	return event?.fields || [];
}

// Helper function to get operator display name
export function getOperatorDisplayName(operator: string): string {
	const operatorMap: Record<string, string> = {
		equals: 'equals',
		not_equals: 'does not equal',
		contains: 'contains',
		starts_with: 'starts with',
		ends_with: 'ends with',
		greater_than: 'greater than',
		less_than: 'less than',
		includes: 'includes',
		not_in: 'not in',
		in: 'in',
		exists: 'exists',
		not_exists: 'does not exist'
	};
	return operatorMap[operator] || operator;
}
