import { FeedbackRating } from '$lib/types/feedback';

/**
 * Hook for handling feedback submission
 * Currently simulates API calls since no backend is implemented
 */
export function useFeedback() {
	async function submitFeedback(
		requestId: string,
		rating: FeedbackRating,
		note: string
	): Promise<void> {
		try {
			const response = await fetch('/api/feedback', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					requestId: requestId,
					rating: rating,
					note: note
				})
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
			}

			await response.json();
		} catch (error) {
			console.error('Failed to submit feedback:', error);
			throw error;
		}
	}

	return {
		submitFeedback
	};
}
