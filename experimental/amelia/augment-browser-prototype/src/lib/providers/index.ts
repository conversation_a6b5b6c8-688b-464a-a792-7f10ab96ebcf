/**
 * Provider Registry and Utilities
 *
 * This module exports all available providers and provides utility functions
 * for working with the provider system.
 */

import type { Provider, ProviderEntity, EntityType } from './types';
import { githubProvider } from './github';
import { linearProvider } from './linear';
import { scheduleProvider } from './schedule';

// Registry of all available providers
export const PROVIDERS: Provider[] = [githubProvider, linearProvider, scheduleProvider];

// Provider lookup by ID
export const PROVIDERS_BY_ID = PROVIDERS.reduce(
	(acc, provider) => {
		acc[provider.id] = provider;
		return acc;
	},
	{} as Record<string, Provider>
);

/**
 * Get a provider by ID
 */
export function getProvider(id: string): Provider | undefined {
	return PROVIDERS_BY_ID[id];
}

/**
 * Get all providers that support a specific entity type
 */
export function getProvidersByEntityType(entityType: EntityType): Provider[] {
	return PROVIDERS.filter((provider) =>
		provider.entities.some((entity) => entity.type === entityType)
	);
}

/**
 * Get all configured providers for a project
 */
export function getConfiguredProviders(): Provider[] {
	return PROVIDERS.filter((provider) => (provider.isConfigured ? provider.isConfigured() : false));
}

/**
 * Get provider configuration for a project
 */
export function getProviderConfig(providerId: string, project: any): any {
	const provider = getProvider(providerId);
	if (!provider || !provider.getConfig) return null;
	return provider.getConfig(project);
}

/**
 * Check if a provider is configured for a project
 */
export function isProviderConfigured(providerId: string, project: any): boolean {
	const provider = getProvider(providerId);
	if (!provider || !provider.isConfigured) return false;
	return provider.isConfigured(project);
}

/**
 * Get entity configuration for a provider and entity type
 */
export function getEntityConfig(providerId: string, entityType: EntityType) {
	const provider = getProvider(providerId);
	if (!provider) return null;
	return provider.entities.find((entity) => entity.type === entityType);
}

/**
 * Convert a provider entity to a task using the provider's converter
 */
export function convertEntityToTask(entity: ProviderEntity) {
	const provider = getProvider(entity.providerId);
	if (!provider) {
		throw new Error(`Provider ${entity.providerId} not found`);
	}

	const entityConfig = provider.entities.find((e) => e.type === entity.entityType);
	if (!entityConfig) {
		throw new Error(
			`Entity type ${entity.entityType} not supported by provider ${entity.providerId}`
		);
	}

	return entityConfig.converter(entity);
}

/**
 * Check if an entity is linked to a task
 */
export function isEntityLinkedToTask(entity: ProviderEntity, tasks: any[]): boolean {
	const provider = getProvider(entity.providerId);
	if (!provider) return false;

	const entityConfig = provider.entities.find((e) => e.type === entity.entityType);
	if (!entityConfig || !entityConfig.isLinkedToTask) return false;

	return entityConfig.isLinkedToTask(entity, tasks);
}

/**
 * Fetch entities for a provider and entity type
 */
export async function fetchProviderEntities(
	providerId: string,
	entityType: EntityType
): Promise<ProviderEntity[]> {
	const provider = getProvider(providerId);
	if (!provider) {
		throw new Error(`Provider ${providerId} not found`);
	}

	const entityConfig = provider.entities.find((e) => e.type === entityType);
	if (!entityConfig) {
		throw new Error(`Entity type ${entityType} not supported by provider ${providerId}`);
	}

	const entities = await entityConfig.fetchFunction();

	// Add provider context to entities
	return entities.map((entity) => ({
		...entity,
		providerId,
		entityType,
		sourceId: entity.id
	}));
}

// Re-export types and providers
export type {
	Provider,
	ProviderEntity,
	EntityType,
	BaseEntity,
	ProviderEntityConfig
} from './types';
export { githubProvider } from './github';
export { linearProvider } from './linear';
