/**
 * Linear Provider Implementation
 */

import type { LinearIssue } from '$lib/types';
import type { Provider, BaseEntity, ProviderEntityConfig } from './types';
import { createReferenceFromLinearIssue } from '$lib/utils/task-references';
import { fetchLinearIssues, connectLinear, disconnectLinear } from '$lib/stores/integrations';
import { Linear } from '$lib/icons/LinearIcon.svelte';

// Linear Issue entity (extends BaseEntity)
export interface LinearIssueEntity extends BaseEntity {
	identifier: string; // e.g., "ENG-123"
	priority: number;
	creator?: {
		name: string;
		avatarUrl?: string;
	};
}

// Convert Linear API issue to our entity format
function convertLinearIssue(issue: LinearIssue): LinearIssueEntity {
	return {
		id: issue.id,
		title: issue.title,
		description: issue.description,
		url: issue.url,
		state: issue.state.name,
		createdAt: issue.createdAt,
		updatedAt: issue.updatedAt,
		identifier: issue.identifier,
		priority: issue.priority,
		assignee: issue.assignee
			? {
					name: issue.assignee.name,
					avatar: issue.assignee.avatarUrl
				}
			: undefined,
		labels: issue.labels.map((label) => ({
			name: label.name,
			color: label.color
		})),
		creator: issue.creator
	};
}

// Check if Linear issue is linked to a task
function isLinearIssueLinked(entity: LinearIssueEntity, tasks: any[]): boolean {
	return tasks.some((task) =>
		task.references?.some(
			(ref: any) => ref.sourceId === entity.identifier && ref.type === 'LINEAR_ISSUE'
		)
	);
}

// Fetch Linear issues for a project
async function fetchLinearIssuesForProject(config: {
	projectId?: string;
}): Promise<LinearIssueEntity[]> {
	try {
		const result = await fetchLinearIssues(config.projectId);
		return result?.issues?.map(convertLinearIssue) || [];
	} catch (error) {
		console.error('Failed to fetch Linear issues:', error);
		return [];
	}
}

// Convert Linear issue entity to task
function convertLinearIssueToTask(entity: LinearIssueEntity) {
	// Convert back to LinearIssue format for the existing reference function
	const linearIssue: LinearIssue = {
		id: entity.id,
		identifier: entity.identifier,
		title: entity.title,
		description: entity.description,
		state: {
			id: `state-${entity.state}`,
			name: entity.state,
			type:
				entity.state === 'completed'
					? 'completed'
					: entity.state === 'canceled'
						? 'canceled'
						: entity.state === 'started'
							? 'started'
							: 'unstarted'
		},
		priority: entity.priority,
		labels:
			entity.labels?.map((label) => ({
				id: `label-${label.name}`,
				name: label.name,
				color: label.color || '#000000'
			})) || [],
		assignee: entity.assignee
			? {
					id: `user-${entity.assignee.name}`,
					name: entity.assignee.name,
					email: '',
					avatarUrl: entity.assignee.avatar
				}
			: undefined,
		creator: entity.creator
			? {
					id: 'linear-user',
					name: entity.creator.name,
					email: '',
					avatarUrl: entity.creator.avatarUrl
				}
			: {
					id: 'unknown',
					name: 'unknown',
					email: '',
					avatarUrl: undefined
				},
		createdAt: entity.createdAt,
		updatedAt: entity.updatedAt,
		url: entity.url || ''
	};

	const reference = createReferenceFromLinearIssue(linearIssue);

	return {
		title: entity.title,
		description: entity.description || '',
		status: 'PENDING' as const,
		references: [reference]
	};
}

// Linear Issues entity configuration
const linearIssuesConfig: ProviderEntityConfig<LinearIssueEntity> = {
	type: 'issues',
	name: 'Issues',
	icon: 'linear',
	fetchFunction: fetchLinearIssuesForProject,
	converter: convertLinearIssueToTask,
	isLinkedToTask: isLinearIssueLinked
};

// Linear Provider definition
export const linearProvider: Provider = {
	id: 'linear',
	name: 'Linear',
	description: 'Linear project management integration for issues and tasks',
	icon: Linear,
	color: '#5e6ad2',
	documentationUrl: 'https://developers.linear.app/docs',
	entities: [linearIssuesConfig],

	getConfig: () => ({}),

	isConfigured: () => true,

	connect: connectLinear,
	disconnect: disconnectLinear
};
