/**
 * Provider Integration System Types
 *
 * This module defines the interfaces and types for the provider integration system.
 * Providers can be GitHub, Linear, Jira, etc., and each provider can have multiple
 * entity types (issues, PRs, tickets, etc.) that can be converted to tasks.
 */

import type { CreateTaskInput } from '$lib/types';

// Base entity interface that all provider entities must implement
export interface BaseEntity {
	id: string;
	title: string;
	description?: string;
	url?: string;
	state: string;
	createdAt: string;
	updatedAt: string;
	assignee?: {
		name: string;
		avatar?: string;
	};
	labels?: Array<{
		name: string;
		color?: string;
	}>;
}

// Entity types that providers can support
export type EntityType = 'pull_request' | 'workflow_run' | 'issue' | 'schedule';

// Provider connection status
export interface ProviderConnection {
	providerId: string;
	isConnected: boolean;
	token?: string;
	lastSyncAt?: string;
	error?: string;
}

// Entity conversion function type
export type EntityConverter<T extends BaseEntity> = (entity: T) => CreateTaskInput;

// Provider entity configuration
export interface ProviderEntityConfig<T extends BaseEntity = BaseEntity> {
	type: EntityType;
	name: string; // Display name (e.g., "Issues", "Pull Requests")
	icon: string; // Icon name or component
	fetchFunction: () => Promise<T[]>;
	converter: EntityConverter<T>;
	isLinkedToTask?: (entity: T, tasks: any[]) => boolean;
}

// Main provider interface
export interface Provider {
	id: string;
	name: string;
	description: string;
	icon: any; // Svelte component
	color?: string;
	authUrl?: string;
	documentationUrl?: string;
	entities: ProviderEntityConfig<any>[];

	// Provider-specific configuration
	getConfig?: () => any;
	isConfigured?: () => boolean;

	// Connection management
	connect?: (token: string) => Promise<boolean>;
	disconnect?: () => void;
	testConnection?: (token: string) => Promise<boolean>;
}

// Entity with provider context
export interface ProviderEntity extends BaseEntity {
	providerId: string;
	entityType: EntityType;
	sourceId: string; // Original ID from the provider
}

// Entity list state
export interface EntityListState {
	entities: ProviderEntity[];
	isLoading: boolean;
	error?: string;
	lastFetchAt?: string;
}

// Provider state
export interface ProviderState {
	connection: ProviderConnection;
	entities: Record<EntityType, EntityListState>;
}
