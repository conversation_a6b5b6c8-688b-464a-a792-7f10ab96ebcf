/**
 * Schedule Provider Implementation
 *
 * Provides scheduled trigger functionality using cron expressions
 */

import type { Provider, BaseEntity, ProviderEntityConfig } from './types';
import { Clock } from 'svelte-hero-icons';

// Schedule entity represents a scheduled execution
export interface ScheduleEntity extends BaseEntity {
	cronExpression: string;
	timezone?: string;
	nextExecution?: string;
	lastExecution?: string;
	executionCount: number;
	isActive: boolean;
}

// Convert schedule configuration to entity format
function convertScheduleToEntity(config: {
	id: string;
	name: string;
	description?: string;
	cronExpression: string;
	timezone?: string;
	nextExecution?: string;
	lastExecution?: string;
	executionCount?: number;
	isActive?: boolean;
}): ScheduleEntity {
	return {
		id: config.id,
		title: config.name,
		description: config.description || '',
		url: '', // Schedules don't have URLs
		state: config.isActive ? 'active' : 'inactive',
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
		cronExpression: config.cronExpression,
		timezone: config.timezone,
		nextExecution: config.nextExecution,
		lastExecution: config.lastExecution,
		executionCount: config.executionCount || 0,
		isActive: config.isActive !== false,
		assignee: undefined,
		labels: []
	};
}

// Check if schedule is linked to a task (always false for schedules)
function isScheduleLinked(entity: ScheduleEntity, tasks: any[]): boolean {
	return false; // Schedules don't link to existing tasks
}

// Fetch scheduled triggers (this would come from the backend)
async function fetchScheduledTriggers(): Promise<ScheduleEntity[]> {
	// This will be replaced with actual API call to fetch scheduled triggers
	// For now, return empty array
	return [];
}

// Convert schedule entity to task input (for manual execution)
function convertScheduleToTask(entity: ScheduleEntity) {
	return {
		title: `Manual execution: ${entity.title}`,
		description: entity.description || `Manually triggered scheduled task: ${entity.title}`,
		status: 'PENDING' as const,
		references: []
	};
}

// Schedule entity configuration
const scheduleConfig: ProviderEntityConfig<ScheduleEntity> = {
	type: 'schedule' as any, // We'll need to add this to EntityType
	name: 'Scheduled Triggers',
	icon: 'clock',
	fetchFunction: fetchScheduledTriggers,
	converter: convertScheduleToTask,
	isLinkedToTask: isScheduleLinked
};

// Schedule Provider definition
export const scheduleProvider: Provider = {
	id: 'schedule',
	name: 'Schedule',
	description: 'Create scheduled triggers using cron expressions',
	icon: Clock,
	color: '#f59e0b', // Amber color for schedule
	documentationUrl: 'https://crontab.guru/',
	entities: [scheduleConfig],

	getConfig: () => ({}),

	// Schedules don't require external authentication
	isConfigured: () => true,

	// No connection needed for schedules
	connect: undefined,
	disconnect: undefined
};
