/**
 * GitHub Provider Implementation
 */

import type { GitHubIssue, GitHubPullRequest } from '$lib/types';
import type { Provider, BaseEntity, ProviderEntityConfig } from './types';
import {
	createReferenceFromGitHubIssue,
	createReferenceFromGitHubPR
} from '$lib/utils/task-references';
import {
	fetchGitHubIssues,
	fetchGitHubPullRequests,
	connectGitHub,
	disconnectGitHub
} from '$lib/stores/integrations';
import { GitHub } from '$lib/icons/GitHubIcon.svelte';

// GitHub Issue entity (extends BaseEntity)
export interface GitHubIssueEntity extends BaseEntity {
	number: number;
	author?: {
		login: string;
		avatarUrl: string;
	};
}

// GitHub Pull Request entity (extends BaseEntity)
export interface GitHubPREntity extends BaseEntity {
	number: number;
	draft: boolean;
	head: {
		ref: string;
		sha: string;
	};
	base: {
		ref: string;
		sha: string;
	};
	mergedAt?: string;
	author?: {
		login: string;
		avatarUrl: string;
	};
}

// Convert GitHub API issue to our entity format
function convertGitHubIssue(issue: GitHubIssue): GitHubIssueEntity {
	return {
		id: issue.id,
		title: issue.title,
		description: issue.body,
		url: issue.url || '',
		state: issue.state,
		createdAt: issue.createdAt,
		updatedAt: issue.updatedAt,
		number: issue.number,
		assignee: issue.assignees[0]
			? {
					name: issue.assignees[0].login,
					avatar: issue.assignees[0].avatarUrl
				}
			: undefined,
		labels: issue.labels.map((label) => ({
			name: label.name,
			color: `#${label.color}`
		})),
		author: issue.author
			? {
					login: issue.author.login,
					avatarUrl: issue.author.avatarUrl
				}
			: undefined
	};
}

// Convert GitHub API PR to our entity format
function convertGitHubPR(pr: GitHubPullRequest): GitHubPREntity {
	return {
		id: pr.id,
		title: pr.title,
		description: pr.body,
		url: pr.url || '',
		state: pr.state,
		createdAt: pr.createdAt,
		updatedAt: pr.updatedAt,
		number: pr.number,
		draft: pr.draft || false,
		head: pr.head,
		base: pr.base,
		mergedAt: pr.mergedAt,
		author: pr.author
			? {
					login: pr.author.login,
					avatarUrl: pr.author.avatarUrl
				}
			: undefined
	};
}

// Check if GitHub issue is linked to a task
function isGitHubIssueLinked(entity: GitHubIssueEntity, tasks: any[]): boolean {
	return tasks.some((task) =>
		task.references?.some(
			(ref: any) => ref.sourceId === entity.number.toString() && ref.type === 'GITHUB_ISSUE'
		)
	);
}

// Check if GitHub PR is linked to a task
function isGitHubPRLinked(entity: GitHubPREntity, tasks: any[]): boolean {
	return tasks.some((task) =>
		task.references?.some(
			(ref: any) => ref.sourceId === entity.number.toString() && ref.type === 'GITHUB_PR'
		)
	);
}

// Fetch GitHub issues for a project
async function fetchGitHubIssuesForProject(config: { repo: string }): Promise<GitHubIssueEntity[]> {
	if (!config.repo) return [];

	try {
		const result = await fetchGitHubIssues(config.repo, { state: 'open' });
		return result?.issues?.map(convertGitHubIssue) || [];
	} catch (error) {
		console.error('Failed to fetch GitHub issues:', error);
		return [];
	}
}

// Fetch GitHub PRs for a project
async function fetchGitHubPRsForProject(config: { repo: string }): Promise<GitHubPREntity[]> {
	if (!config.repo) return [];

	try {
		const result = await fetchGitHubPullRequests(config.repo, { state: 'open' });
		return result?.pullRequests?.map(convertGitHubPR) || [];
	} catch (error) {
		console.error('Failed to fetch GitHub pull requests:', error);
		return [];
	}
}

// Convert GitHub issue entity to task
function convertGitHubIssueToTask(entity: GitHubIssueEntity) {
	// Convert back to GitHubIssue format for the existing reference function
	const githubIssue: GitHubIssue = {
		id: entity.id,
		number: entity.number,
		title: entity.title,
		body: entity.description,
		state: entity.state as 'open' | 'closed',
		createdAt: entity.createdAt,
		updatedAt: entity.updatedAt,
		url: entity.url || '',
		labels:
			entity.labels?.map((label) => ({
				id: `label-${label.name}`,
				name: label.name,
				color: label.color?.replace('#', '') || '000000',
				description: ''
			})) || [],
		assignees: entity.assignee
			? [
					{
						id: `user-${entity.assignee.name}`,
						login: entity.assignee.name,
						avatarUrl: entity.assignee.avatar || '',
						url: ''
					}
				]
			: [],
		author: entity.author
			? {
					id: `user-${entity.author.login}`,
					login: entity.author.login,
					avatarUrl: entity.author.avatarUrl,
					url: ''
				}
			: {
					id: 'unknown',
					login: 'unknown',
					avatarUrl: '',
					url: ''
				}
	};

	const reference = createReferenceFromGitHubIssue(githubIssue);

	return {
		title: entity.title,
		description: entity.description || '',
		status: 'PENDING' as const,
		references: [reference]
	};
}

// Convert GitHub PR entity to task
function convertGitHubPRToTask(entity: GitHubPREntity) {
	// Convert back to GitHubPullRequest format for the existing reference function
	const githubPR: GitHubPullRequest = {
		id: entity.id,
		number: entity.number,
		title: entity.title,
		body: entity.description || '',
		state: entity.state as 'open' | 'closed' | 'merged',
		draft: entity.draft,
		url: entity.url || '',
		head: entity.head,
		base: entity.base,
		createdAt: entity.createdAt,
		updatedAt: entity.updatedAt,
		mergedAt: entity.mergedAt,
		author: entity.author
			? {
					id: `user-${entity.author.login}`,
					login: entity.author.login,
					avatarUrl: entity.author.avatarUrl,
					url: ''
				}
			: {
					id: 'unknown',
					login: 'unknown',
					avatarUrl: '',
					url: ''
				}
	};

	const reference = createReferenceFromGitHubPR(githubPR);

	return {
		title: entity.title,
		description: entity.description || '',
		status: 'PENDING' as const,
		references: [reference]
	};
}

// GitHub Issues entity configuration
const githubIssuesConfig: ProviderEntityConfig<GitHubIssueEntity> = {
	type: 'issues',
	name: 'Issues',
	icon: 'github',
	fetchFunction: fetchGitHubIssuesForProject,
	converter: convertGitHubIssueToTask,
	isLinkedToTask: isGitHubIssueLinked
};

// GitHub Pull Requests entity configuration
const githubPRsConfig: ProviderEntityConfig<GitHubPREntity> = {
	type: 'pull_requests',
	name: 'Pull Requests',
	icon: 'github',
	fetchFunction: fetchGitHubPRsForProject,
	converter: convertGitHubPRToTask,
	isLinkedToTask: isGitHubPRLinked
};

// GitHub Provider definition
export const githubProvider: Provider = {
	id: 'github',
	name: 'GitHub',
	description: 'GitHub repository integration for issues and pull requests',
	icon: GitHub,
	color: '#24292e',
	documentationUrl: 'https://docs.github.com/en/rest',
	entities: [githubIssuesConfig, githubPRsConfig],

	getConfig: () => ({}),

	isConfigured: () => true,

	connect: connectGitHub,
	disconnect: disconnectGitHub
};
