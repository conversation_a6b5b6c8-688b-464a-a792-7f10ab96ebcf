import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import { buildConditionsFromFilters } from '$lib/utils/condition-filters';
import { loadMatchingEntities } from '$lib/stores/data-operations.svelte';
import {
	GitHubEntityType,
	LinearEntityType,
	type TriggerConditions
} from '$lib/types/trigger-enums';
import { apiClient } from '$lib/api/unified-client';
import { providerEntityTypes } from '$lib/utils/entity-types';
import type { createEntityCache, DashboardFilters } from '$lib/utils/dashboard-filters.svelte';
import { getCurrentRepository, repositoryToUrl } from '$lib/utils/project-repository';

// Provider to event source mapping
const EVENT_SOURCE_MAP: Record<string, number> = {
	github: 1, // EVENT_SOURCE_GITHUB
	linear: 2, // EVENT_SOURCE_LINEAR
	jira: 3 // EVENT_SOURCE_JIRA
};

export interface EntityFetchOptions {
	append?: boolean;
	limit?: number;
}

export interface EntityFetchResult {
	entities: UnifiedEntity[];
	error?: string;
}

/**
 * Build array of [providerId, entityType] combinations based on current filters
 */
export function buildProviderEntityCombinations(
	filters: DashboardFilters,
	availableProviders: string[]
): Array<[string, string]> {
	const combinations: Array<[string, string]> = [];

	const providers =
		filters.selectedProvider === 'all' ? availableProviders : [filters.selectedProvider];

	for (const providerId of providers) {
		const availableEntityTypesForProvider = providerEntityTypes[providerId] || [];
		const entityTypes =
			filters.selectedEntityType === 'all'
				? availableEntityTypesForProvider
				: [filters.selectedEntityType].filter((type) =>
						availableEntityTypesForProvider.includes(type)
					);

		for (const entityType of entityTypes) {
			combinations.push([providerId, entityType]);
		}
	}

	return combinations;
}

/**
 * Create a trigger configuration for fetching entities
 */
export function createTriggerConfigForFetching(
	providerId: string,
	entityType: string,
	filters: DashboardFilters
): TriggerConditions {
	const conditions = buildConditionsFromFilters(filters.conditionFilters, providerId, entityType);

	// Add search query as title_contains filter if provided
	if (filters.searchQuery.trim()) {
		const searchTerm = filters.searchQuery.trim();

		if (providerId === 'github' && entityType === 'pull_request') {
			if (!conditions.github)
				conditions.github = {
					entity_type: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
					pull_request: {}
				};
			if (!conditions.github.pull_request) conditions.github.pull_request = {};
			conditions.github.pull_request.title_contains = searchTerm;
		} else if (providerId === 'linear' && entityType === 'issue') {
			if (!conditions.linear)
				conditions.linear = { entity_type: LinearEntityType.LINEAR_ENTITY_TYPE_ISSUE, issue: {} };
			if (!conditions.linear.issue) conditions.linear.issue = {};
			conditions.linear.issue.title_contains = searchTerm;
		}
	}

	return {
		name: 'Dashboard Filter',
		description: `Temporary trigger for ${providerId} ${entityType}`,
		event_source: EVENT_SOURCE_MAP[providerId] || 1,
		conditions: conditions,
		agent_config: {
			workspace_setup: {
				github_ref: {
					url: repositoryToUrl(getCurrentRepository()),
					ref: 'main'
				}
			}
		},
		enabled: true
	};
}

/**
 * Fetch entities for a specific provider and entity type combination with caching
 */
export async function fetchEntitiesForCombination(
	providerId: string,
	entityType: string,
	filters: DashboardFilters,
	entityCache?: ReturnType<typeof createEntityCache>,
	options: EntityFetchOptions = {}
): Promise<UnifiedEntity[]> {
	const { limit = 50 } = options;

	// Generate cache key for this specific combination
	const cacheKey = generateCombinationCacheKey(providerId, entityType, filters);

	// Check cache first if cache is provided
	if (entityCache) {
		const cachedEntities = entityCache.get(cacheKey);
		if (cachedEntities) {
			const cacheInfo = entityCache.getCacheInfo(cacheKey);
			console.log(`Using cached entities for ${providerId}/${entityType}:`, {
				entityCount: cachedEntities.length,
				cacheAge: cacheInfo?.age,
				isExpired: cacheInfo?.isExpired
			});
			return cachedEntities;
		}
	}

	// Cache miss or no cache - fetch fresh data
	console.log(`Fetching fresh data for ${providerId}/${entityType}...`);

	try {
		const triggerConfig = createTriggerConfigForFetching(providerId, entityType, filters);

		const matchingEntities = await apiClient.triggers.matchingEntitiesForConfig(triggerConfig, {
			limit
		});

		// Cache the results if cache is provided and we got results
		if (entityCache && matchingEntities.length > 0) {
			entityCache.set(cacheKey, matchingEntities, filters);
			console.log(`Cached ${matchingEntities.length} entities for ${providerId}/${entityType}`);
		}

		return matchingEntities;
	} catch (err) {
		console.error(`Failed to fetch entities for ${providerId} ${entityType}:`, err);
		return [];
	}
}

/**
 * Fetch entities for all provider-entity combinations based on current filters
 */
export async function fetchAllEntities(
	filters: DashboardFilters,
	availableProviders: string[],
	entityCache?: ReturnType<typeof createEntityCache>,
	options: EntityFetchOptions = {}
): Promise<EntityFetchResult> {
	try {
		const combinations = buildProviderEntityCombinations(filters, availableProviders);
		console.log('Fetching entities for combinations:', combinations, filters, availableProviders);

		const allMatchingEntities: UnifiedEntity[] = [];

		// Fetch entities for each combination
		for (const [providerId, entityType] of combinations) {
			const entities = await fetchEntitiesForCombination(
				providerId,
				entityType,
				filters,
				entityCache,
				options
			);
			allMatchingEntities.push(...entities);
		}

		// Deduplicate entities by ID to prevent the same entity appearing multiple times
		const uniqueEntities = deduplicateEntities(allMatchingEntities);

		console.log(
			`Fetched ${allMatchingEntities.length} total entities, ${uniqueEntities.length} unique entities`
		);

		return { entities: uniqueEntities };
	} catch (err) {
		console.error('Failed to fetch entities:', err);
		return {
			entities: [],
			error: err instanceof Error ? err.message : 'Failed to load data'
		};
	}
}

/**
 * Fetch entities with caching - caching is now handled at the individual combination level
 */
export async function fetchAllEntitiesWithCache(
	filters: DashboardFilters,
	availableProviders: string[],
	entityCache: ReturnType<typeof createEntityCache>,
	options: EntityFetchOptions = {}
): Promise<EntityFetchResult> {
	// Simply call fetchAllEntities with the cache - caching is handled per combination
	return await fetchAllEntities(filters, availableProviders, entityCache, options);
}

/**
 * Generate cache key for a specific provider-entity combination
 */
export function generateCombinationCacheKey(
	providerId: string,
	entityType: string,
	filters: DashboardFilters
): string {
	return JSON.stringify({
		provider: providerId,
		entityType: entityType,
		conditions: filters.selectedConditions,
		search: filters.searchQuery.trim()
	});
}

/**
 * Generate cache key for entity fetching (legacy - for backward compatibility)
 */
export function generateEntityCacheKey(filters: DashboardFilters): string {
	return JSON.stringify({
		provider: filters.selectedProvider,
		entityType: filters.selectedEntityType,
		conditions: filters.selectedConditions,
		search: filters.searchQuery.trim()
	});
}

/**
 * Deduplicate entities by ID, keeping the first occurrence
 */
export function deduplicateEntities(entities: UnifiedEntity[]): UnifiedEntity[] {
	const seen = new Set<string>();
	const uniqueEntities: UnifiedEntity[] = [];

	for (const entity of entities) {
		if (!seen.has(entity.id)) {
			seen.add(entity.id);
			uniqueEntities.push(entity);
		}
	}

	return uniqueEntities;
}

/**
 * Merge new entities with existing ones, avoiding duplicates
 */
export function mergeEntities(
	existingEntities: UnifiedEntity[],
	newEntities: UnifiedEntity[]
): UnifiedEntity[] {
	const existingIds = new Set(existingEntities.map((e) => e.id));
	const uniqueNewEntities = newEntities.filter((e) => !existingIds.has(e.id));
	return [...existingEntities, ...uniqueNewEntities];
}
