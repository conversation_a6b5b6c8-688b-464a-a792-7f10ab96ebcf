/**
 * Utility functions for parsing and cleaning session summaries from remote agents
 */

import { extractOriginalMessageFromInitial } from '$lib/types/structured-response';

/**
 * Entity type mappings for display names
 */
const ENTITY_TYPE_DISPLAY_NAMES: Record<string, string> = {
	GITHUB_ENTITY_TYPE_WORKFLOW_RUN: 'Workflow Run',
	GITHUB_ENTITY_TYPE_PULL_REQUEST: 'Pull Request',
	GITHUB_ENTITY_TYPE_ISSUE: 'Issue',
	LINEAR_ENTITY_TYPE_ISSUE: 'Linear Issue',
	JIRA_ENTITY_TYPE_ISSUE: 'Jira Issue',
	workflow_run: 'Workflow Run',
	pull_request: 'Pull Request',
	issue: 'Issue',
	linear_issue: 'Linear Issue',
	jira_issue: 'Jira Issue'
};

/**
 * Parse trigger entity syntax from session summary
 * Looks for patterns like "Entity Type: GITHUB_ENTITY_TYPE_WORKFLOW_RUN Entity Id: 15743423034"
 */
function parseTriggerEntitySyntax(text: string): { entityType: string; entityId: string } | null {
	// Match the trigger entity syntax pattern
	// like "Entity Type: GITHUB_ENTITY_TYPE_PULL_REQUEST\nEntity Id: pr-27952\n\n"
	const triggerPattern = /Entity Type:\s*([A-Z_]+)\s+Entity Id:\s*([\w-]+)/i;
	const match = text.match(triggerPattern);

	if (match) {
		const [, entityType, entityId] = match;
		const parsedEntityId = entityId.replace(/^pr-/, '');
		return {
			entityType: entityType.trim(),
			entityId: parsedEntityId.trim()
		};
	}

	return null;
}

/**
 * Convert entity type to display name
 */
function getEntityTypeDisplayName(entityType: string): string {
	return ENTITY_TYPE_DISPLAY_NAMES[entityType] || entityType;
}

/**
 * Clean and parse session summary to extract meaningful title
 *
 * This function:
 * 1. Removes structured response instruction prefixes
 * 2. Parses trigger entity syntax and converts to readable format
 * 3. Returns a clean, user-friendly title
 */
export function parseSessionSummaryTitle(sessionSummary: string | null | undefined): string {
	if (!sessionSummary) {
		return 'Remote Agent Run';
	}

	// First, remove any structured response instructions prefix
	let cleanedSummary = extractOriginalMessageFromInitial(sessionSummary);

	// Check for trigger entity syntax
	const triggerEntity = parseTriggerEntitySyntax(cleanedSummary);
	if (triggerEntity) {
		const displayName = getEntityTypeDisplayName(triggerEntity.entityType);
		return `${displayName} #${triggerEntity.entityId}`;
	}

	// If no trigger syntax found, return the cleaned summary
	// Limit length to keep titles manageable
	const maxLength = 80;
	if (cleanedSummary.length > maxLength) {
		return cleanedSummary.substring(0, maxLength).trim() + '...';
	}

	return cleanedSummary;
}

/**
 * Get a short description from session summary
 * This extracts any additional context beyond the trigger syntax
 */
export function parseSessionSummaryDescription(
	sessionSummary: string | null | undefined
): string | null {
	if (!sessionSummary) {
		return null;
	}

	// Remove structured response instructions prefix
	let cleanedSummary = extractOriginalMessageFromInitial(sessionSummary);

	// Remove trigger entity syntax if present
	const triggerPattern = /Entity Type:\s*[A-Z_]+\s+Entity Id:\s*\w+/i;
	cleanedSummary = cleanedSummary.replace(triggerPattern, '').trim();

	// Remove any leading/trailing punctuation or whitespace
	cleanedSummary = cleanedSummary.replace(/^[:\-\s]+|[:\-\s]+$/g, '');

	// If there's meaningful content left, return it
	if (cleanedSummary.length > 10) {
		const maxLength = 150;
		if (cleanedSummary.length > maxLength) {
			return cleanedSummary.substring(0, maxLength).trim() + '...';
		}
		return cleanedSummary;
	}

	return null;
}

/**
 * Check if a session summary contains trigger entity syntax
 */
export function hasTriggerEntitySyntax(sessionSummary: string | null | undefined): boolean {
	if (!sessionSummary) {
		return false;
	}

	const cleanedSummary = extractOriginalMessageFromInitial(sessionSummary);
	return parseTriggerEntitySyntax(cleanedSummary) !== null;
}

/**
 * Extract content from XML tags in text
 * @param text The text to search in
 * @param tagName The XML tag name to extract (without < >)
 * @returns The content inside the tag, or null if not found
 */
export function extractXMLTagContent(text: string, tagName: string): string | null {
	if (!text || !tagName) return null;

	const regex = new RegExp(`<${tagName}>([\\s\\S]*?)</${tagName}>`, 'i');
	const match = text.match(regex);
	return match ? match[1].trim() : null;
}

/**
 * Extract summary from agent response text
 * Looks for <summary> tags first, then falls back to other methods
 * @param text The response text to extract summary from
 * @returns The extracted summary or null if not found
 */
export function extractSummaryFromResponse(text: string): string | null {
	if (!text) return null;

	// First try to extract from <summary> tag
	const summaryTag = extractXMLTagContent(text, 'summary');
	if (summaryTag) {
		return summaryTag;
	}

	// Fallback: look for summary-like patterns
	// This could be enhanced with more sophisticated parsing
	return null;
}

/**
 * Extract general summary from agent response text
 * Looks for <general_summary> tags first, then falls back to other methods
 * @param text The response text to extract general summary from
 * @returns The extracted general summary or null if not found
 */
export function extractGeneralSummaryFromResponse(text: string): string | null {
	if (!text) return null;

	// First try to extract from <general_summary> tag
	const generalSummaryTag = extractXMLTagContent(text, 'general_summary');
	if (generalSummaryTag) {
		return generalSummaryTag;
	}

	// Fallback: look for general summary patterns
	// This could be enhanced with more sophisticated parsing
	return null;
}
