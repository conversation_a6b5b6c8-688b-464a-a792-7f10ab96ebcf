/**
 * Utility functions for creating remote agents from entities
 */

import type { UnifiedEntity } from './entity-conversion';
import {
	apiClient,
	ChatRequestNodeType,
	type RemoteAgentWorkspaceSetup
} from '$lib/api/unified-client';

function createWorkspaceSetup(repositoryUrl: string, branch: string): RemoteAgentWorkspaceSetup {
	return {
		startingFiles: {
			githubCommitRef: {
				repositoryUrl,
				gitRef: branch
			}
		}
	};
}
import {
	wrapInitialMessageWithLatestInstructions,
	wrapMessageWithPrefixVersion
} from '$lib/types/structured-response';
import { getCurrentRepository, getRepositoryUrlFromEntity } from './project-repository';
import type { DefaultTrigger } from '$lib/default-triggers';

/**
 * Generate entity syntax for remote agent initial message
 * This creates the specific syntax that can be parsed to link back to the original entity
 */
export function generateEntitySyntax(entity: UnifiedEntity): string {
	// Map entity types to the format expected by the parser
	const entityTypeMap: Record<string, string> = {
		pull_request: 'GITHUB_ENTITY_TYPE_PULL_REQUEST',
		workflow_run: 'GITHUB_ENTITY_TYPE_WORKFLOW_RUN',
		issue: entity.providerId === 'github' ? 'GITHUB_ENTITY_TYPE_ISSUE' : 'LINEAR_ENTITY_TYPE_ISSUE'
	};

	const entityType = entityTypeMap[entity.entityType] || entity.entityType.toUpperCase();

	return `Entity Type: ${entityType} Entity Id: ${entity.id}`;
}

/**
 * Create a remote agent for an entity with user instructions
 */
export async function createRemoteAgentForEntity(
	entity: UnifiedEntity,
	userInstructions: string,
	options: {
		repositoryUrl?: string;
		branch?: string;
		userGuidelines?: string;
		workspaceGuidelines?: string;
		/** Prefix version to use for the agent prompt (defaults to latest) */
		prefixVersion?: string;
	} = {}
): Promise<{ remote_agent_id: string }> {
	// Determine repository URL: provided option > entity repository > current project repository
	let repositoryUrl = options.repositoryUrl;

	if (!repositoryUrl) {
		// Try to extract repository from entity
		repositoryUrl = getRepositoryUrlFromEntity(entity) || undefined;
	}

	if (!repositoryUrl) {
		// Fall back to current project repository
		repositoryUrl = getCurrentRepository();
	}

	if (!repositoryUrl) {
		throw new Error(
			'No repository URL could be determined from entity, options, or current project'
		);
	}

	const branch = options.branch || 'main';

	// Create workspace setup
	const workspaceSetup = createWorkspaceSetup(repositoryUrl, branch);

	// Generate entity syntax and combine with user instructions
	const entitySyntax = generateEntitySyntax(entity);
	const fullPrompt = `${entitySyntax}

${userInstructions}`;

	// Wrap with structured response instructions using specified version or latest
	const wrappedPrompt = options.prefixVersion
		? wrapMessageWithPrefixVersion(fullPrompt, options.prefixVersion)
		: wrapInitialMessageWithLatestInstructions(fullPrompt);

	// Create the remote agent
	const response = await apiClient.agents.create({
		initialRequestDetails: {
			requestNodes: [
				{
					id: 1,
					type: ChatRequestNodeType.TEXT,
					textNode: {
						content: wrappedPrompt
					}
				}
			]
		},
		workspaceSetup,
		userGuidelines:
			options.userGuidelines ||
			'Follow the existing code patterns and best practices in the repository.',
		workspaceGuidelines:
			options.workspaceGuidelines ||
			'Maintain consistency with the existing codebase structure and conventions.'
	});

	return { remote_agent_id: response.remoteAgentId };
}

/**
 * Create a remote agent from a default trigger configuration
 */
export async function createRemoteAgentFromTrigger(
	entity: UnifiedEntity,
	defaultTrigger: DefaultTrigger,
	userInstructions?: string,
	options: {
		repositoryUrl?: string;
		branch?: string;
	} = {}
): Promise<{ remote_agent_id: string }> {
	const agentConfig = defaultTrigger.configuration.agent_config;
	const finalInstructions = userInstructions || getDefaultInstructions(entity);

	return createRemoteAgentForEntity(entity, finalInstructions, {
		...options,
		userGuidelines: agentConfig.user_guidelines,
		workspaceGuidelines: agentConfig.workspace_guidelines,
		prefixVersion: defaultTrigger.prefixVersion || agentConfig.prefix_version
	});
}

/**
 * Get default instructions based on entity type and content
 */
export function getDefaultInstructions(entity: UnifiedEntity): string {
	const entityTypeInstructions: Record<string, string> = {
		pull_request: `Your task is to review the provided pull request. Checkout the head branch of the pull request and comments on the following issues:
- Code quality and best practices
- Potential bugs or issues
- Suggestions for improvement
- Testing recommendations
- Suggest ways to reuse existing and avoid code duplications.`,

		workflow_run: `Please analyze this workflow run and help with:
- Understanding any failures or issues
- Optimizing the workflow configuration
- Debugging build or deployment problems
- Improving CI/CD processes
- Fixing the underlying code changes that caused the failure
- Improving the workflow configuration to prevent future failures`,

		issue: `Please implement the needed changes and improvements.`
	};

	return (
		entityTypeInstructions[entity.entityType] ||
		`Please help me work on this ${entity.entityType.replace('_', ' ')} by providing guidance and assistance.`
	);
}

/**
 * Validate that an entity can have a remote agent created for it
 */
export function canCreateRemoteAgentForEntity(entity: UnifiedEntity): boolean {
	// For now, support all entity types
	// Could add specific validation logic here if needed
	return true;
}
