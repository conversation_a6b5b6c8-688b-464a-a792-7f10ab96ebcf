import { browser } from '$app/environment';
import type { GitHubRepo, GitHubBranch } from '$lib/api/github-api';
import { GitHubUtils } from '$lib/api/github-api';

export interface RecentRepo {
	repo: GitHubRepo;
	timestamp: number;
}

export interface RecentBranch {
	repo: GitHubRepo;
	branch: GitHubBranch;
	timestamp: number;
}

const RECENT_REPOS_KEY = 'augment_recent_repos';
const RECENT_BRANCHES_KEY = 'augment_recent_branches';
const MAX_RECENT_ITEMS = 10;
const RECENT_ITEMS_TO_SHOW = 3;
const EXPIRY_DAYS = 30;

/**
 * Add a repository to the recent repos list
 */
export function addRecentRepo(repo: GitHubRepo): void {
	if (!browser) return;

	try {
		const existing = getStoredRecentRepos();
		const repoKey = GitHubUtils.getRepoKey(repo);

		// Remove any existing entry for this repo
		const filtered = existing.filter(item => GitHubUtils.getRepoKey(item.repo) !== repoKey);

		// Add the new entry at the beginning
		const updated: RecentRepo[] = [
			{ repo, timestamp: Date.now() },
			...filtered
		].slice(0, MAX_RECENT_ITEMS); // Keep only the most recent items

		localStorage.setItem(RECENT_REPOS_KEY, JSON.stringify(updated));
	} catch (error) {
		console.warn('Failed to save recent repo:', error);
	}
}

/**
 * Add a repository-branch combination to the recent branches list
 */
export function addRecentBranch(repo: GitHubRepo, branch: GitHubBranch): void {
	if (!browser) return;

	try {
		const existing = getStoredRecentBranches();
		const repoKey = GitHubUtils.getRepoKey(repo);
		const branchKey = GitHubUtils.getBranchKey(branch);

		// Remove any existing entry for this repo-branch combo
		const filtered = existing.filter(item =>
			!(GitHubUtils.getRepoKey(item.repo) === repoKey && GitHubUtils.getBranchKey(item.branch) === branchKey)
		);

		// Add the new entry at the beginning
		const updated: RecentBranch[] = [
			{ repo, branch, timestamp: Date.now() },
			...filtered
		].slice(0, MAX_RECENT_ITEMS); // Keep only the most recent items

		localStorage.setItem(RECENT_BRANCHES_KEY, JSON.stringify(updated));
	} catch (error) {
		console.warn('Failed to save recent branch:', error);
	}
}

/**
 * Get recent repositories (up to 3 most recent)
 */
export function getRecentRepos(): GitHubRepo[] {
	if (!browser) return [];

	try {
		const recent = getStoredRecentRepos();
		return recent
			.slice(0, RECENT_ITEMS_TO_SHOW)
			.map(item => item.repo);
	} catch (error) {
		console.warn('Failed to load recent repos:', error);
		return [];
	}
}

/**
 * Get recent branches for a specific repository (up to 3 most recent)
 */
export function getRecentBranches(repo: GitHubRepo): GitHubBranch[] {
	if (!browser) return [];

	try {
		const recent = getStoredRecentBranches();
		const repoKey = GitHubUtils.getRepoKey(repo);

		return recent
			.filter(item => GitHubUtils.getRepoKey(item.repo) === repoKey)
			.slice(0, RECENT_ITEMS_TO_SHOW)
			.map(item => item.branch);
	} catch (error) {
		console.warn('Failed to load recent branches:', error);
		return [];
	}
}

/**
 * Clear all recent repos and branches
 */
export function clearRecentReposAndBranches(): void {
	if (!browser) return;

	try {
		localStorage.removeItem(RECENT_REPOS_KEY);
		localStorage.removeItem(RECENT_BRANCHES_KEY);
	} catch (error) {
		console.warn('Failed to clear recent repos and branches:', error);
	}
}

/**
 * Get stored recent repos with expiry check
 */
function getStoredRecentRepos(): RecentRepo[] {
	if (!browser) return [];

	try {
		const stored = localStorage.getItem(RECENT_REPOS_KEY);
		if (!stored) return [];

		const data = JSON.parse(stored) as RecentRepo[];
		if (!Array.isArray(data)) return [];

		// Filter out expired items
		const expiryTime = Date.now() - (EXPIRY_DAYS * 24 * 60 * 60 * 1000);
		const filtered = data.filter(item =>
			item.timestamp &&
			item.timestamp > expiryTime &&
			item.repo &&
			typeof item.repo === 'object'
		);

		// Update storage if we filtered out expired items
		if (filtered.length !== data.length) {
			localStorage.setItem(RECENT_REPOS_KEY, JSON.stringify(filtered));
		}

		return filtered;
	} catch (error) {
		console.warn('Failed to load recent repos:', error);
		// Clear corrupted data
		localStorage.removeItem(RECENT_REPOS_KEY);
		return [];
	}
}

/**
 * Get stored recent branches with expiry check
 */
function getStoredRecentBranches(): RecentBranch[] {
	if (!browser) return [];

	try {
		const stored = localStorage.getItem(RECENT_BRANCHES_KEY);
		if (!stored) return [];

		const data = JSON.parse(stored) as RecentBranch[];
		if (!Array.isArray(data)) return [];

		// Filter out expired items
		const expiryTime = Date.now() - (EXPIRY_DAYS * 24 * 60 * 60 * 1000);
		const filtered = data.filter(item =>
			item.timestamp &&
			item.timestamp > expiryTime &&
			item.repo &&
			item.branch &&
			typeof item.repo === 'object' &&
			typeof item.branch === 'object'
		);

		// Update storage if we filtered out expired items
		if (filtered.length !== data.length) {
			localStorage.setItem(RECENT_BRANCHES_KEY, JSON.stringify(filtered));
		}

		return filtered;
	} catch (error) {
		console.warn('Failed to load recent branches:', error);
		// Clear corrupted data
		localStorage.removeItem(RECENT_BRANCHES_KEY);
		return [];
	}
}
