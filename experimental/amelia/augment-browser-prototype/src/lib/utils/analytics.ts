import { track } from '@vercel/analytics';
import { browser } from '$app/environment';

/**
 * Analytics utility functions for tracking user behavior patterns
 * while maintaining privacy compliance
 */

export interface UserAnalyticsContext {
	sessionId?: string;
	userType?: 'internal' | 'external' | 'anonymous';
	feature?: string;
	location?: string;
	metadata?: Record<string, string | number | boolean>;
}

/**
 * Track user interactions with additional context
 */
export function trackUserAction(
	eventName: string,
	context: UserAnalyticsContext = {}
) {
	if (!browser) return;

	const eventData = {
		...context.metadata,
		...(context.userType && { userType: context.userType }),
		...(context.feature && { feature: context.feature }),
		...(context.location && { location: context.location }),
		timestamp: new Date().toISOString()
	};

	track(eventName, eventData);
}

/**
 * Track page navigation patterns
 */
export function trackPageView(
	pageName: string,
	context: UserAnalyticsContext = {}
) {
	trackUserAction('Page View', {
		...context,
		metadata: {
			...context.metadata,
			page: pageName
		}
	});
}

/**
 * Track feature usage patterns
 */
export function trackFeatureUsage(
	featureName: string,
	action: string,
	context: UserAnalyticsContext = {}
) {
	trackUserAction('Feature Usage', {
		...context,
		feature: featureName,
		metadata: {
			...context.metadata,
			action
		}
	});
}

/**
 * Track user workflow patterns
 */
export function trackWorkflow(
	workflowName: string,
	step: string,
	context: UserAnalyticsContext = {}
) {
	trackUserAction('Workflow Step', {
		...context,
		metadata: {
			...context.metadata,
			workflow: workflowName,
			step
		}
	});
}

/**
 * Track errors and issues for pattern analysis
 */
export function trackError(
	errorType: string,
	errorMessage?: string,
	context: UserAnalyticsContext = {}
) {
	trackUserAction('Error Occurred', {
		...context,
		metadata: {
			...context.metadata,
			errorType,
			...(errorMessage && { errorMessage: errorMessage.substring(0, 100) }) // Limit length
		}
	});
}

/**
 * Track performance metrics
 */
export function trackPerformance(
	metricName: string,
	value: number,
	context: UserAnalyticsContext = {}
) {
	trackUserAction('Performance Metric', {
		...context,
		metadata: {
			...context.metadata,
			metric: metricName,
			value
		}
	});
}
