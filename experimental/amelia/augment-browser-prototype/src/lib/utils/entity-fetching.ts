import type { <PERSON><PERSON>ult<PERSON><PERSON><PERSON> } from '$lib/default-triggers';
import type { Trigger } from '$lib/types';
import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import { loadMatchingEntities } from '$lib/stores/data-operations.svelte';

/**
 * Fetches entities for a given trigger, handling both mock mode and real API calls
 * @param trigger - The trigger to fetch entities for (can be <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON>)
 * @returns Promise<UnifiedEntity[]> - Array of matching entities
 */
export async function fetchEntitiesForTrigger(
	trigger: DefaultTrigger | Trigger
): Promise<UnifiedEntity[]> {
	try {
		let matchingEntities: UnifiedEntity[] = [];

		if ('id' in trigger) {
			// This is a default trigger - use the trigger ID
			const entities = await loadMatchingEntities(trigger.id);
			matchingEntities = entities as unknown as UnifiedEntity[];
		} else {
			// This is a backend trigger - use the trigger ID
			const entities = await loadMatchingEntities(trigger.id);
			matchingEntities = entities as unknown as UnifiedEntity[];
		}

		return matchingEntities;
	} catch (err) {
		console.error('Failed to load entities for trigger:', err);
		throw new Error(err instanceof Error ? err.message : 'Failed to load entities');
	}
}

/**
 * Enhanced function to check if entity is linked to a task or remote agent
 * @param entity - The entity to check
 * @param tasks - Array of tasks
 * @param agents - Array of remote agents
 * @param executions - Array of trigger executions
 * @returns boolean - Whether the entity is linked
 */
export function isEntityLinkedToTask(
	entity: UnifiedEntity,
	tasks: any[],
	agents: any[],
	executions: any[]
): boolean {
	// Check if any task has a reference with the same URL or title
	const linkedToTask = tasks.some((task) =>
		task.references?.some((ref: any) => ref.url === entity.url || ref.title === entity?.title)
	);

	// Check if entity is linked through trigger executions and remote agents
	const linkedThroughExecution = executions.some((execution) => {
		// Check if execution has a remote agent
		if (!execution.remote_agent_id) return false;

		// Find the corresponding remote agent
		const agent = agents.find((a) => a.remote_agent_id === execution.remote_agent_id);
		if (!agent) return false;

		// Check if the execution's event payload matches this entity
		try {
			const payload = execution.event_payload ? JSON.parse(execution.event_payload) : null;
			if (payload) {
				// Match by entity ID, URL, or title
				return (
					payload.entity_id === entity.id ||
					payload.entity_url === entity.url ||
					payload.entity_title === entity?.title
				);
			}
		} catch (e) {
			// Ignore JSON parse errors
		}

		return false;
	});

	return linkedToTask || linkedThroughExecution;
}

/**
 * Helper function to find linked task through remote agent
 * @param entity - The entity to find linked task for
 * @param tasks - Array of tasks
 * @param agents - Array of remote agents
 * @param executions - Array of trigger executions
 * @returns The linked task or null
 */
export function findLinkedTask(
	entity: UnifiedEntity,
	tasks: any[],
	agents: any[],
	executions: any[]
) {
	// First check direct task references
	const directLinkedTask = tasks.find((t) =>
		t.references?.some((r: any) => r.url === entity.url || r.title === entity?.title)
	);

	if (directLinkedTask) return directLinkedTask;

	// Then check through remote agent executions
	const execution = executions.find((execution) => {
		if (!execution.remote_agent_id) return false;

		try {
			const payload = execution.event_payload ? JSON.parse(execution.event_payload) : null;
			if (payload) {
				return (
					payload.entity_id === entity.id ||
					payload.entity_url === entity.url ||
					payload.entity_title === entity?.title
				);
			}
		} catch (e) {
			// Ignore JSON parse errors
		}

		return false;
	});

	if (execution) {
		// Find the remote agent
		const agent = agents.find((a) => a.remote_agent_id === execution.remote_agent_id);
		if (agent) {
			// Find the task associated with this remote agent
			return tasks.find((t) => t.remoteAgentId === agent.remoteAgentId);
		}
	}

	return null;
}

/**
 * Enriches entities with linked status and task information
 * @param entities - Array of entities to enrich
 * @param tasks - Array of tasks
 * @param agents - Array of remote agents
 * @param executions - Array of trigger executions
 * @returns Array of enriched entities with isLinked and linkedTask properties
 */
export function enrichEntitiesWithLinkStatus(
	entities: UnifiedEntity[],
	tasks: any[],
	agents: any[],
	executions: any[]
) {
	return entities.map((entity) => {
		const isLinked = isEntityLinkedToTask(entity, tasks, agents, executions);
		const linkedTask = findLinkedTask(entity, tasks, agents, executions);

		return {
			...entity,
			isLinked,
			linkedTask
		};
	});
}
