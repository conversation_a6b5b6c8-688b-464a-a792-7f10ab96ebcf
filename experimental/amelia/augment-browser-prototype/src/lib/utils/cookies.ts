import { browser } from '$app/environment';

/**
 * Get a cookie value by name
 */
export function getCookie(name: string): string | null {
	if (!browser) return null;

	const value = `; ${document.cookie}`;
	const parts = value.split(`; ${name}=`);
	if (parts.length === 2) {
		const cookieValue = parts.pop()?.split(';').shift();
		return cookieValue || null;
	}
	return null;
}

/**
 * Check if the site access cookie is valid
 */
export function hasValidSiteAccess(): boolean {
	if (!browser) return false;

	const siteAccess = getCookie('site-access');
	return siteAccess === 'true';
}

/**
 * Set a cookie
 */
export function setCookie(
	name: string,
	value: string,
	options: {
		path?: string;
		maxAge?: number;
		secure?: boolean;
		sameSite?: 'Strict' | 'Lax' | 'None';
	} = {}
): void {
	if (!browser) return;

	const { path = '/', maxAge, secure = false, sameSite = 'Strict' } = options;

	let cookieString = `${name}=${value}; Path=${path}; SameSite=${sameSite}`;

	if (maxAge !== undefined) {
		cookieString += `; Max-Age=${maxAge}`;
	}

	if (secure) {
		cookieString += '; Secure';
	}

	document.cookie = cookieString;
}
