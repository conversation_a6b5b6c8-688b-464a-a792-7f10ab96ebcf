import { describe, it, expect } from 'vitest';
import {
	extractXMLTagContent,
	extractSummaryFromResponse,
	extractGeneralSummaryFromResponse
} from './session-summary-parser';

describe('session-summary-parser', () => {
	describe('extractXMLTagContent', () => {
		it('should extract content from simple XML tags', () => {
			const text = 'Some text <summary>This is a summary</summary> more text';
			const result = extractXMLTagContent(text, 'summary');
			expect(result).toBe('This is a summary');
		});

		it('should extract content from multiline XML tags', () => {
			const text = `
				<general_summary>
				The user confirmed the changes were satisfactory with a simple "ok" response.
				No further action was needed.
				</general_summary>
			`;
			const result = extractXMLTagContent(text, 'general_summary');
			expect(result).toBe('The user confirmed the changes were satisfactory with a simple "ok" response. \n\t\t\t\tNo further action was needed.');
		});

		it('should be case insensitive', () => {
			const text = 'Some text <SUMMARY>This is a summary</SUMMARY> more text';
			const result = extractXMLTagContent(text, 'summary');
			expect(result).toBe('This is a summary');
		});

		it('should return null if tag not found', () => {
			const text = 'Some text without tags';
			const result = extractXMLTagContent(text, 'summary');
			expect(result).toBe(null);
		});

		it('should return null for empty input', () => {
			expect(extractXMLTagContent('', 'summary')).toBe(null);
			expect(extractXMLTagContent('text', '')).toBe(null);
		});

		it('should handle nested content', () => {
			const text = '<summary>This has <em>nested</em> content</summary>';
			const result = extractXMLTagContent(text, 'summary');
			expect(result).toBe('This has <em>nested</em> content');
		});
	});

	describe('extractSummaryFromResponse', () => {
		it('should extract summary from response with summary tags', () => {
			const response = `
				Some response text here.

				<summary>
				The user confirmed the changes were satisfactory with a simple "ok" response. No further action was needed.
				</summary>

				More text here.
			`;
			const result = extractSummaryFromResponse(response);
			expect(result).toBe('The user confirmed the changes were satisfactory with a simple "ok" response. No further action was needed.');
		});

		it('should return null if no summary tags found', () => {
			const response = 'Just some regular response text without summary tags.';
			const result = extractSummaryFromResponse(response);
			expect(result).toBe(null);
		});
	});

	describe('extractGeneralSummaryFromResponse', () => {
		it('should extract general summary from response with general_summary tags', () => {
			const response = `
				Some response text here.

				<general_summary>
				The task has been completed successfully. The user acknowledged the changes to the README.md file where I added dog emojis (🐕 in the title and 🐶 in the welcome message) and expressed satisfaction with the result.
				</general_summary>

				More text here.
			`;
			const result = extractGeneralSummaryFromResponse(response);
			expect(result).toBe('The task has been completed successfully. The user acknowledged the changes to the README.md file where I added dog emojis (🐕 in the title and 🐶 in the welcome message) and expressed satisfaction with the result.');
		});

		it('should return null if no general_summary tags found', () => {
			const response = 'Just some regular response text without general summary tags.';
			const result = extractGeneralSummaryFromResponse(response);
			expect(result).toBe(null);
		});
	});

	describe('real world example', () => {
		it('should extract both summary and general_summary from a complete response', () => {
			const response = `
				I have successfully implemented the tab functionality in the RemoteAgentDetailPanel.svelte. Here's a summary of what I've accomplished:

				## ✅ **Tab Implementation Complete**

				### **What I Added:**

				1. **Tab State Management**
				   - Added \`activeTab\` state variable with 'overview' and 'chat' options
				   - Defaults to 'overview' tab

				<summary>
				The user confirmed the changes were satisfactory with a simple "ok" response. No further action was needed.
				</summary>

				<general_summary>
				The task has been completed successfully. The user acknowledged the changes to the README.md file where I added dog emojis (🐕 in the title and 🐶 in the welcome message) and expressed satisfaction with the result.
				</general_summary>
			`;

			const summary = extractSummaryFromResponse(response);
			const generalSummary = extractGeneralSummaryFromResponse(response);

			expect(summary).toBe('The user confirmed the changes were satisfactory with a simple "ok" response. No further action was needed.');
			expect(generalSummary).toBe('The task has been completed successfully. The user acknowledged the changes to the README.md file where I added dog emojis (🐕 in the title and 🐶 in the welcome message) and expressed satisfaction with the result.');
		});
	});
});
