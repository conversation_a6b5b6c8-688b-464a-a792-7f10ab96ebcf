/**
 * Shared transition utilities for consistent animations across components
 */

import { crossfade } from 'svelte/transition';
import { quintOut } from 'svelte/easing';

/**
 * Shared crossfade instance for entity rows (MatchRow <-> ExecutionRow)
 * This allows smooth transitions when entities move between different states
 */
export const [sendEntityRow, receiveEntityRow] = crossfade({
	duration: 400,
	easing: quintOut,
	fallback(node, params) {
		const style = getComputedStyle(node);
		const transform = style.transform === 'none' ? '' : style.transform;

		return {
			duration: 300,
			easing: quintOut,
			css: (t) => `
				transform: ${transform} scale(${t});
				opacity: ${t};
			`
		};
	}
});

/**
 * Shared crossfade instance for agent cards
 */
export const [sendAgentCard, receiveAgentCard] = crossfade({
	duration: 600,
	easing: quintOut,
	fallback(node, params) {
		const style = getComputedStyle(node);
		const transform = style.transform === 'none' ? '' : style.transform;

		return {
			duration: 400,
			easing: quintOut,
			css: (t) => `
				transform: ${transform} scale(${t});
				opacity: ${t};
			`
		};
	}
});
