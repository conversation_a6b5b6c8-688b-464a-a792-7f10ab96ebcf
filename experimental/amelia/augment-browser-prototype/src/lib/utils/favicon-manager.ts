/**
 * Favicon Manager
 *
 * Manages switching between normal and notification favicons based on agent states
 */

let currentFavicon: 'normal' | 'notification' = 'normal';

/**
 * Update the favicon to show notification state
 */
export function updateFavicon(hasNotifications: boolean): void {
	const targetFavicon = hasNotifications ? 'notification' : 'normal';

	// Only update if the state has changed
	if (currentFavicon === targetFavicon) {
		return;
	}

	const faviconPath = hasNotifications ? '/favicon-notification.svg' : '/favicon.svg';

	// Find existing favicon link element
	let faviconLink = document.querySelector('link[rel="icon"]') as HTMLLinkElement;

	if (faviconLink) {
		// Update existing favicon
		faviconLink.href = faviconPath;
	} else {
		// Create new favicon link if it doesn't exist
		faviconLink = document.createElement('link');
		faviconLink.rel = 'icon';
		faviconLink.href = faviconPath;
		document.head.appendChild(faviconLink);
	}

	currentFavicon = targetFavicon;

	console.log(`[Favicon] Updated to ${targetFavicon} (${faviconPath})`);
}

/**
 * Get the current favicon state
 */
export function getCurrentFaviconState(): 'normal' | 'notification' {
	return currentFavicon;
}

/**
 * Reset favicon to normal state
 */
export function resetFavicon(): void {
	updateFavicon(false);
}
