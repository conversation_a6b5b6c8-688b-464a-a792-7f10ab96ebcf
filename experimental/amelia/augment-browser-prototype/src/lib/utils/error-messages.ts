/**
 * Centralized error messages and warnings for consistent user experience
 */

export type MessageType = 'error' | 'warning' | 'info';

export interface FormattedMessage {
	message: string;
	type: MessageType;
	actionable?: boolean;
	retryable?: boolean;
	action?: string;
}

export interface ValidationRule {
	check: () => boolean;
	message: string;
	type: 'error' | 'warning';
}

// Enum-like object for error codes to ensure type safety
export const ERROR_CODES = {
	// Agent Limit Errors
	AGENT_LIMIT_REACHED: 'AGENT_LIMIT_REACHED',
	ACTIVE_AGENT_LIMIT_REACHED: 'ACTIVE_AGENT_LIMIT_REACHED',

	// Form Validation Errors
	NO_REPOSITORY_SELECTED: 'NO_REPOSITORY_SELECTED',
	NO_BRANCH_SELECTED: 'NO_BRANCH_SELECTED',
	EMPTY_PROMPT: 'EMPTY_PROMPT',
	NO_WORKSPACE_SELECTED: 'NO_WORKSPACE_SELECTED',
	WORKSPACE_SELECTION_ERROR: 'WORKSPACE_SELECTION_ERROR',

	// GitHub Authentication Errors
	GITHUB_AUTH_NOT_CONFIGURED: 'GITHUB_AUTH_NOT_CONFIGURED',
	GITHUB_AUTH_URL_NOT_FOUND: 'GITHUB_AUTH_URL_NOT_FOUND',
	GITHUB_AUTH_EXPIRED: 'GITHUB_AUTH_EXPIRED',
	GITHUB_AUTH_FAILED: 'GITHUB_AUTH_FAILED',
	GITHUB_NO_ACCESS: 'GITHUB_NO_ACCESS',
	GITHUB_INSUFFICIENT_PERMISSIONS: 'GITHUB_INSUFFICIENT_PERMISSIONS',
	GITHUB_RATE_LIMITED: 'GITHUB_RATE_LIMITED',

	// Repository Access Errors
	REPOSITORY_NOT_FOUND: 'REPOSITORY_NOT_FOUND',
	REPOSITORY_ACCESS_DENIED: 'REPOSITORY_ACCESS_DENIED',
	NO_REMOTE_BRANCHES: 'NO_REMOTE_BRANCHES',
	FETCH_BRANCHES_FAILED: 'FETCH_BRANCHES_FAILED',
	INVALID_REMOTE_URL: 'INVALID_REMOTE_URL',
	FETCH_REMOTE_FAILED: 'FETCH_REMOTE_FAILED',

	// Backend API Errors
	SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
	FEATURE_DISABLED: 'FEATURE_DISABLED',
	CREDITS_DEPLETED: 'CREDITS_DEPLETED',
	INVALID_REQUEST_FORMAT: 'INVALID_REQUEST_FORMAT',
	INVALID_WORKSPACE_SETUP: 'INVALID_WORKSPACE_SETUP',

	// Network and Server Errors
	NETWORK_ERROR: 'NETWORK_ERROR',
	CONNECTION_TIMEOUT: 'CONNECTION_TIMEOUT',
	SERVER_ERROR: 'SERVER_ERROR',
	RATE_LIMITED: 'RATE_LIMITED',
	UNAUTHORIZED: 'UNAUTHORIZED',

	// Generic
	UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

export type ErrorCode = (typeof ERROR_CODES)[keyof typeof ERROR_CODES];

// Warning codes
export const WARNING_CODES = {
	// Agent Limits
	APPROACHING_AGENT_LIMIT: 'APPROACHING_AGENT_LIMIT',
	APPROACHING_ACTIVE_LIMIT: 'APPROACHING_ACTIVE_LIMIT',
	AGENT_LIMIT_REACHED: 'AGENT_LIMIT_REACHED',

	// Form Validation
	LONG_PROMPT: 'LONG_PROMPT',
	INVALID_URL_FORMAT: 'INVALID_URL_FORMAT',

	// GitHub
	GITHUB_AUTH_EXPIRING: 'GITHUB_AUTH_EXPIRING',
	GITHUB_RATE_LIMIT_WARNING: 'GITHUB_RATE_LIMIT_WARNING',
	GITHUB_PERMISSIONS_WARNING: 'GITHUB_PERMISSIONS_WARNING',
	LARGE_REPOSITORY: 'LARGE_REPOSITORY',

	// Performance
	SLOW_NETWORK: 'SLOW_NETWORK',
	HIGH_RESOURCE_USAGE: 'HIGH_RESOURCE_USAGE',

	// Workspace
	WORKSPACE_SETUP_SLOW: 'WORKSPACE_SETUP_SLOW',
	BRANCH_NOT_LATEST: 'BRANCH_NOT_LATEST',
	REMOTE_ACCESS_LIMITED: 'REMOTE_ACCESS_LIMITED'
} as const;

export type WarningCode = (typeof WARNING_CODES)[keyof typeof WARNING_CODES];

/**
 * Programmatic error message mapping
 */
export const ERROR_MESSAGES_MAP: Record<ErrorCode, FormattedMessage> = {
	// Agent Limit Errors
	[ERROR_CODES.AGENT_LIMIT_REACHED]: {
		message:
			"You've reached your agent limit! Consider deleting an older agent to make room for a new one.",
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'delete_oldest_agent'
	},
	[ERROR_CODES.ACTIVE_AGENT_LIMIT_REACHED]: {
		message: 'You have too many active agents running. Try pausing an older agent first.',
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'pause_oldest_agent'
	},

	// Form Validation Errors
	[ERROR_CODES.NO_REPOSITORY_SELECTED]: {
		message: 'Please select a repository to continue.',
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.NO_BRANCH_SELECTED]: {
		message: 'Please select a branch to continue.',
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.EMPTY_PROMPT]: {
		message:
			'Your agent needs instructions! Please provide a prompt describing what you want it to do.',
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.NO_WORKSPACE_SELECTED]: {
		message: 'Please select a workspace first before creating an agent.',
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.WORKSPACE_SELECTION_ERROR]: {
		message:
			"There's an issue with your workspace selection. Please resolve it before creating an agent.",
		type: 'error',
		actionable: true,
		retryable: false
	},

	// GitHub Authentication Errors
	[ERROR_CODES.GITHUB_AUTH_NOT_CONFIGURED]: {
		message: "GitHub integration isn't set up yet. Please authenticate with GitHub to continue.",
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'authenticate_github'
	},
	[ERROR_CODES.GITHUB_AUTH_URL_NOT_FOUND]: {
		message:
			'GitHub authentication is temporarily unavailable. Please contact support if this persists.',
		type: 'error',
		actionable: false,
		retryable: false
	},
	[ERROR_CODES.GITHUB_AUTH_EXPIRED]: {
		message: 'Your GitHub authentication has expired. Please sign in with GitHub again.',
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'reauthenticate_github'
	},
	[ERROR_CODES.GITHUB_AUTH_FAILED]: {
		message: 'GitHub authentication failed. Please try signing in again.',
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'authenticate_github'
	},
	[ERROR_CODES.GITHUB_NO_ACCESS]: {
		message:
			"Your GitHub account doesn't have access to this repository. Please re-authenticate with proper permissions.",
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'reauthenticate_github'
	},
	[ERROR_CODES.GITHUB_INSUFFICIENT_PERMISSIONS]: {
		message:
			"You don't have permission to access this repository. Please check your GitHub permissions or try a different repository.",
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.GITHUB_RATE_LIMITED]: {
		message: 'GitHub API rate limit exceeded. Please wait a few minutes before trying again.',
		type: 'warning',
		actionable: true,
		retryable: true,
		action: 'retry_with_timer'
	},

	// Repository Access Errors
	[ERROR_CODES.REPOSITORY_NOT_FOUND]: {
		message:
			"This repository doesn't exist or you don't have access to it. Please check the repository name and your permissions.",
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.REPOSITORY_ACCESS_DENIED]: {
		message: "You don't have permission to access this repository.",
		type: 'error',
		actionable: true,
		retryable: false
	},
	[ERROR_CODES.NO_REMOTE_BRANCHES]: {
		message:
			"No remote branches found. Please push your current branch to remote with 'git push -u origin <branch>' and try again.",
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'show_git_instructions'
	},
	[ERROR_CODES.FETCH_BRANCHES_FAILED]: {
		message:
			"Couldn't load branches from this repository. Please try again or check your internet connection.",
		type: 'error',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.INVALID_REMOTE_URL]: {
		message:
			"There's an issue with your git remote configuration. Please check your remote URL and try again.",
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'show_git_remote_instructions'
	},
	[ERROR_CODES.FETCH_REMOTE_FAILED]: {
		message:
			"Couldn't fetch from the remote repository. Please check your connection and try again.",
		type: 'error',
		actionable: true,
		retryable: true
	},

	// Backend API Errors
	[ERROR_CODES.SERVICE_UNAVAILABLE]: {
		message:
			'The remote agents service is temporarily unavailable. Please try again in a few minutes.',
		type: 'warning',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.FEATURE_DISABLED]: {
		message: 'Remote agents are currently disabled. Please contact support for more information.',
		type: 'error',
		actionable: false,
		retryable: false
	},
	[ERROR_CODES.CREDITS_DEPLETED]: {
		message:
			"You've reached your account limits. Please upgrade your account to continue using remote agents.",
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'upgrade_account'
	},
	[ERROR_CODES.INVALID_REQUEST_FORMAT]: {
		message: 'There was an issue with the request format. Please try creating the agent again.',
		type: 'error',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.INVALID_WORKSPACE_SETUP]: {
		message:
			"There's an issue with the workspace configuration. Please check your repository and branch selection.",
		type: 'error',
		actionable: true,
		retryable: false
	},

	// Network and Server Errors
	[ERROR_CODES.NETWORK_ERROR]: {
		message: 'Having trouble connecting. Please check your internet connection and try again.',
		type: 'error',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.CONNECTION_TIMEOUT]: {
		message: 'The request took too long to complete. Please try again.',
		type: 'error',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.SERVER_ERROR]: {
		message: 'Our servers are having a moment. Please try again in a few seconds.',
		type: 'error',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.RATE_LIMITED]: {
		message:
			"You're creating agents faster than we can handle! Please wait a moment before creating another.",
		type: 'warning',
		actionable: true,
		retryable: true
	},
	[ERROR_CODES.UNAUTHORIZED]: {
		message: 'Your session has expired. Please refresh the page and sign in again.',
		type: 'error',
		actionable: true,
		retryable: false,
		action: 'refresh_page'
	},

	// Generic
	[ERROR_CODES.UNKNOWN_ERROR]: {
		message: 'Something went wrong. Please try again or contact support if the problem persists.',
		type: 'error',
		actionable: true,
		retryable: true
	}
};

/**
 * Programmatic warning message mapping
 */
export const WARNING_MESSAGES_MAP: Record<WarningCode, FormattedMessage> = {
	// Agent Limits
	[WARNING_CODES.APPROACHING_AGENT_LIMIT]: {
		message: "You're approaching your total agent limit. Consider deleting some older agents.",
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.APPROACHING_ACTIVE_LIMIT]: {
		message:
			"You're approaching the maximum number of active agents. Consider waiting for some to complete.",
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.AGENT_LIMIT_REACHED]: {
		message:
			'You have reached your limit of running remote agents. Delete some older agents to create new ones.',
		type: 'warning',
		actionable: true,
		retryable: false
	},

	// Form Validation
	[WARNING_CODES.LONG_PROMPT]: {
		message:
			'Your instructions are quite long. Consider breaking them into smaller, focused tasks.',
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.INVALID_URL_FORMAT]: {
		message: "The repository URL format doesn't look right. Please check and try again.",
		type: 'warning',
		actionable: true,
		retryable: false
	},

	// GitHub
	[WARNING_CODES.GITHUB_AUTH_EXPIRING]: {
		message: 'Your GitHub authentication will expire soon. Consider re-authenticating.',
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.GITHUB_RATE_LIMIT_WARNING]: {
		message: "You're approaching GitHub's rate limit. Consider slowing down requests.",
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.GITHUB_PERMISSIONS_WARNING]: {
		message: 'You may not have full permissions for this repository.',
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.LARGE_REPOSITORY]: {
		message: 'This repository is quite large. Operations might take longer.',
		type: 'warning',
		actionable: false,
		retryable: false
	},

	// Performance
	[WARNING_CODES.SLOW_NETWORK]: {
		message: 'Your connection seems slow. Operations might take longer than usual.',
		type: 'warning',
		actionable: false,
		retryable: false
	},
	[WARNING_CODES.HIGH_RESOURCE_USAGE]: {
		message: "You're using many resources. Consider reducing active agents.",
		type: 'warning',
		actionable: true,
		retryable: false
	},

	// Workspace
	[WARNING_CODES.WORKSPACE_SETUP_SLOW]: {
		message: 'Workspace setup might take a few moments for large repositories.',
		type: 'warning',
		actionable: false,
		retryable: false
	},
	[WARNING_CODES.BRANCH_NOT_LATEST]: {
		message: 'The selected branch might not have the latest changes.',
		type: 'warning',
		actionable: true,
		retryable: false
	},
	[WARNING_CODES.REMOTE_ACCESS_LIMITED]: {
		message: 'Remote repository access might be limited.',
		type: 'warning',
		actionable: true,
		retryable: false
	}
};

/**
 * Specific error messages for different contexts
 */
export const ERROR_MESSAGES = {
	AGENT_CREATION: {
		GENERIC: 'Something went wrong while creating your agent. Please try again.',
		TOO_MANY_AGENTS:
			"You've reached the maximum number of active agents. Please wait for some to complete or cancel existing ones.",
		TOO_MANY_TOTAL_AGENTS:
			"You've reached your agent limit! Consider deleting an older agent to make room for a new one.",
		INVALID_REPOSITORY:
			'The repository URL is invalid or inaccessible. Please check the URL and try again.',
		INVALID_BRANCH:
			"The specified branch doesn't exist in this repository. Please select a valid branch.",
		MISSING_PERMISSIONS:
			"You don't have permission to access this repository. Please check your GitHub permissions.",
		ENTITY_NOT_FOUND: 'The selected entity is no longer available. Please refresh and try again.',
		EMPTY_PROMPT:
			'Your agent needs instructions! Please provide a prompt describing what you want it to do.',
		NO_REPOSITORY_SELECTED: 'Please select a repository to continue.',
		NO_BRANCH_SELECTED: 'Please select a branch to continue.',
		NO_WORKSPACE_SELECTED: 'Please select a workspace first before creating an agent.',
		WORKSPACE_SELECTION_ERROR:
			"There's an issue with your workspace selection. Please resolve it before creating an agent."
	},
	GITHUB_AUTH: {
		NOT_CONFIGURED:
			"GitHub integration isn't set up yet. Please authenticate with GitHub to continue.",
		URL_NOT_FOUND:
			'GitHub authentication is temporarily unavailable. Please contact support if this persists.',
		EXPIRED: 'Your GitHub authentication has expired. Please sign in with GitHub again.',
		FAILED: 'GitHub authentication failed. Please try signing in again.',
		NO_ACCESS:
			"Your GitHub account doesn't have access to this repository. Please re-authenticate with proper permissions.",
		INSUFFICIENT_PERMISSIONS:
			"You don't have permission to access this repository. Please check your GitHub permissions or try a different repository.",
		RATE_LIMITED: 'GitHub API rate limit exceeded. Please wait a few minutes before trying again.'
	},
	REPOSITORY: {
		NOT_FOUND:
			"This repository doesn't exist or you don't have access to it. Please check the repository name and your permissions.",
		ACCESS_DENIED: "You don't have permission to access this repository.",
		NO_BRANCHES:
			"No remote branches found. Please push your current branch to remote with 'git push -u origin <branch>' and try again.",
		FETCH_BRANCHES_FAILED:
			"Couldn't load branches from this repository. Please try again or check your internet connection.",
		INVALID_REMOTE_URL:
			"There's an issue with your git remote configuration. Please check your remote URL and try again.",
		FETCH_REMOTE_FAILED:
			"Couldn't fetch from the remote repository. Please check your connection and try again."
	},
	BACKEND_API: {
		SERVICE_UNAVAILABLE:
			'The remote agents service is temporarily unavailable. Please try again in a few minutes.',
		FEATURE_DISABLED:
			'Remote agents are currently disabled. Please contact support for more information.',
		CREDITS_DEPLETED:
			"You've reached your account limits. Please upgrade your account to continue using remote agents.",
		INVALID_REQUEST:
			'There was an issue with the request format. Please try creating the agent again.',
		INVALID_WORKSPACE:
			"There's an issue with the workspace configuration. Please check your repository and branch selection."
	},
	ENTITY_OPERATIONS: {
		LOAD_FAILED: 'Failed to load entities. Please refresh the page and try again.',
		TRIGGER_FAILED: 'Failed to execute the trigger. Please try again or contact support.',
		INVALID_ENTITY: 'The selected entity is invalid or has been removed.'
	},
	AUTHENTICATION: {
		SESSION_EXPIRED: 'Your session has expired. Please refresh the page to log in again.',
		INVALID_TOKEN: 'Authentication failed. Please refresh the page and try again.',
		PERMISSION_DENIED: "You don't have permission to perform this action."
	},
	NETWORK: {
		CONNECTION_FAILED: 'Unable to connect to the server. Please check your internet connection.',
		REQUEST_TIMEOUT: 'The request timed out. Please try again.',
		SERVER_UNAVAILABLE: 'The server is temporarily unavailable. Please try again later.',
		GENERAL_ERROR: 'Having trouble connecting. Please check your internet connection and try again.'
	},
	RATE_LIMITING: {
		TOO_MANY_REQUESTS:
			"You're creating agents faster than we can handle! Please wait a moment before creating another.",
		GENERAL:
			"You're delegating like a boss! You can only create so many agents at once. Please wait a moment before creating another."
	},
	SERVER: {
		INTERNAL_ERROR: 'Our servers are having a moment. Please try again in a few seconds.',
		GENERAL: 'Something went wrong on our end. Our team has been notified. Please try again later.'
	}
} as const;

/**
 * Get a formatted error message by error code
 */
export function getErrorMessage(errorCode: ErrorCode): FormattedMessage {
	return ERROR_MESSAGES_MAP[errorCode] || ERROR_MESSAGES_MAP[ERROR_CODES.UNKNOWN_ERROR];
}

/**
 * Get a formatted warning message by warning code
 */
export function getWarningMessage(warningCode: WarningCode): FormattedMessage {
	return WARNING_MESSAGES_MAP[warningCode];
}

/**
 * Sanitize error message to remove HTML content and ensure clean display
 */
export function sanitizeErrorMessage(message: string): string {
	// Remove HTML tags
	const withoutHtml = message.replace(/<[^>]*>/g, '');

	// Remove excessive whitespace and newlines
	const cleaned = withoutHtml.replace(/\s+/g, ' ').trim();

	// If the message looks like it contains HTML entities or is too long, provide a generic message
	if (cleaned.length > 200 || cleaned.includes('&lt;') || cleaned.includes('&gt;')) {
		return 'Service temporarily unavailable - please try again later';
	}

	return cleaned;
}

/**
 * Format error messages from various sources into user-friendly text
 * This is a fallback for when we can't determine the specific error code
 */
export function formatErrorMessage(error: unknown): FormattedMessage {
	let errorText = '';

	// Extract error text from various formats
	if (typeof error === 'string') {
		errorText = error;

		// Try to parse JSON strings
		try {
			const parsed = JSON.parse(error);
			if (parsed.error && parsed.details) {
				const details =
					typeof parsed.details === 'string' ? JSON.parse(parsed.details) : parsed.details;
				errorText = details.error || details.message || parsed.error;
			}
		} catch {
			// Not JSON, use as-is
		}
	} else if (error instanceof Error) {
		errorText = error.message;
	} else if (error && typeof error === 'object') {
		// Handle structured error objects
		const errorObj = error as Record<string, unknown>;
		if (errorObj.error && errorObj.details) {
			try {
				const details =
					typeof errorObj.details === 'string' ? JSON.parse(errorObj.details) : errorObj.details;
				const detailsObj = details as Record<string, unknown>;
				errorText =
					(detailsObj.error as string) ||
					(detailsObj.message as string) ||
					(errorObj.error as string);
			} catch {
				errorText = errorObj.error as string;
			}
		} else if (errorObj.message) {
			errorText = errorObj.message as string;
		} else if (errorObj.error) {
			errorText = errorObj.error as string;
		}
	}

	// Sanitize the error text to remove any HTML content
	const sanitizedText = errorText
		? sanitizeErrorMessage(errorText)
		: ERROR_MESSAGES.AGENT_CREATION.GENERIC;

	// Return the sanitized error text
	return {
		message: sanitizedText,
		type: 'error',
		actionable: true,
		retryable: true
	};
}

/**
 * Helper functions for programmatic error/warning triggering
 */

/**
 * Check if agent limits are reached and return appropriate error codes
 */
export function checkAgentLimits(
	activeCount: number,
	totalCount: number,
	maxActive: number,
	maxTotal: number
): {
	errorCode?: ErrorCode;
	warningCodes: WarningCode[];
} {
	const warningCodes: WarningCode[] = [];

	// Check for hard limits (errors)
	if (totalCount >= maxTotal) {
		return {
			errorCode: ERROR_CODES.AGENT_LIMIT_REACHED,
			warningCodes: [WARNING_CODES.AGENT_LIMIT_REACHED]
		};
	}

	if (activeCount >= maxActive) {
		return {
			errorCode: ERROR_CODES.ACTIVE_AGENT_LIMIT_REACHED,
			warningCodes: [WARNING_CODES.AGENT_LIMIT_REACHED]
		};
	}

	// Check for approaching limits (warnings)
	// const totalThreshold = Math.floor(maxTotal * 0.8); // 80% of limit
	// const activeThreshold = Math.floor(maxActive * 0.8); // 80% of limit

	// if (totalCount >= totalThreshold) {
	// 	warningCodes.push(WARNING_CODES.APPROACHING_AGENT_LIMIT);
	// }

	// if (activeCount >= activeThreshold) {
	// 	warningCodes.push(WARNING_CODES.APPROACHING_ACTIVE_LIMIT);
	// }

	return { warningCodes };
}

/**
 * Validate form fields and return appropriate error codes
 */
export function validateFormFields(data: {
	prompt: string;
	repositoryUrl?: string;
	branch?: string;
	useGitHubIntegration: boolean;
}): {
	errorCodes: ErrorCode[];
	warningCodes: WarningCode[];
} {
	const errorCodes: ErrorCode[] = [];
	const warningCodes: WarningCode[] = [];

	// Check required fields
	if (!data.prompt.trim()) {
		errorCodes.push(ERROR_CODES.EMPTY_PROMPT);
	}

	if (data.useGitHubIntegration) {
		if (!data.repositoryUrl) {
			errorCodes.push(ERROR_CODES.NO_REPOSITORY_SELECTED);
		}

		if (!data.branch) {
			errorCodes.push(ERROR_CODES.NO_BRANCH_SELECTED);
		}
	}

	// Check for warnings
	// if (data.prompt.length > 1000) {
	// 	// Arbitrary threshold for "long" prompt
	// 	warningCodes.push(WARNING_CODES.LONG_PROMPT);
	// }

	return { errorCodes, warningCodes };
}

/**
 * Determine error code from API response
 */
export function determineErrorCodeFromResponse(error: unknown): ErrorCode {
	let errorText = '';

	// Extract error text (same logic as formatErrorMessage)
	if (typeof error === 'string') {
		errorText = error;
		try {
			const parsed = JSON.parse(error);
			if (parsed.error && parsed.details) {
				const details =
					typeof parsed.details === 'string' ? JSON.parse(parsed.details) : parsed.details;
				errorText = details.error || details.message || parsed.error;
			}
		} catch {
			// Not JSON, use as-is
		}
	} else if (error instanceof Error) {
		errorText = error.message;
	} else if (error && typeof error === 'object') {
		const errorObj = error as Record<string, unknown>;
		if (errorObj.error && errorObj.details) {
			try {
				const details =
					typeof errorObj.details === 'string' ? JSON.parse(errorObj.details) : errorObj.details;
				const detailsObj = details as Record<string, unknown>;
				errorText =
					(detailsObj.error as string) ||
					(detailsObj.message as string) ||
					(errorObj.error as string);
			} catch {
				errorText = errorObj.error as string;
			}
		} else if (errorObj.message) {
			errorText = errorObj.message as string;
		} else if (errorObj.error) {
			errorText = errorObj.error as string;
		}
	}

	// Map common error patterns to error codes
	const lowerText = errorText.toLowerCase();

	if (lowerText.includes('maximum number') && lowerText.includes('remote agents')) {
		return ERROR_CODES.AGENT_LIMIT_REACHED;
	}
	if (lowerText.includes('maximum number') && lowerText.includes('active')) {
		return ERROR_CODES.ACTIVE_AGENT_LIMIT_REACHED;
	}
	if (lowerText.includes('too many requests') || lowerText.includes('rate limit')) {
		return ERROR_CODES.RATE_LIMITED;
	}
	if (lowerText.includes('github') && lowerText.includes('auth')) {
		return ERROR_CODES.GITHUB_AUTH_FAILED;
	}
	if (lowerText.includes('repository not found') || lowerText.includes('access denied')) {
		return ERROR_CODES.REPOSITORY_NOT_FOUND;
	}
	if (lowerText.includes('unauthorized') || lowerText.includes('authentication')) {
		return ERROR_CODES.UNAUTHORIZED;
	}
	if (
		lowerText.includes('network') ||
		lowerText.includes('connection') ||
		lowerText.includes('timeout')
	) {
		return ERROR_CODES.NETWORK_ERROR;
	}
	if (
		lowerText.includes('server error') ||
		lowerText.includes('500') ||
		lowerText.includes('502') ||
		lowerText.includes('503')
	) {
		return ERROR_CODES.SERVER_ERROR;
	}

	return ERROR_CODES.UNKNOWN_ERROR;
}

/**
 * Validate form submission and return any blocking errors or warnings
 */
export function validateAgentCreation(data: {
	prompt: string;
	repositoryUrl: string;
	branch: string;
	activeAgentCount: number;
	maxAgents: number;
}): { errors: string[]; warnings: string[] } {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Use the new programmatic validation
	const formValidation = validateFormFields({
		prompt: data.prompt,
		repositoryUrl: data.repositoryUrl,
		branch: data.branch,
		useGitHubIntegration: true
	});

	const limitValidation = checkAgentLimits(
		data.activeAgentCount,
		data.activeAgentCount, // Assuming total = active for backwards compatibility
		data.maxAgents,
		data.maxAgents
	);

	// Convert error codes to messages
	formValidation.errorCodes.forEach((code) => {
		errors.push(getErrorMessage(code).message);
	});

	if (limitValidation.errorCode) {
		errors.push(getErrorMessage(limitValidation.errorCode).message);
	}

	// Convert warning codes to messages
	formValidation.warningCodes.forEach((code) => {
		warnings.push(getWarningMessage(code).message);
	});

	limitValidation.warningCodes.forEach((code) => {
		warnings.push(getWarningMessage(code).message);
	});

	// URL format validation (basic)
	if (data.repositoryUrl && !isValidRepositoryUrl(data.repositoryUrl)) {
		warnings.push(getWarningMessage(WARNING_CODES.INVALID_URL_FORMAT).message);
	}

	return { errors, warnings };
}

/**
 * Basic repository URL validation
 */
function isValidRepositoryUrl(url: string): boolean {
	try {
		const parsed = new URL(url);
		return parsed.hostname === 'github.com' && parsed.pathname.split('/').length >= 3;
	} catch {
		return false;
	}
}
