/**
 * Utility functions for preparing text content for preview display
 * Strips markdown formatting and normalizes text for compact display
 */

/**
 * Strips markdown formatting and normalizes text for preview display
 * Removes headings, code blocks, links, emphasis, and normalizes whitespace
 */
export function stripMarkdownForPreview(text: string): string {
	if (!text || typeof text !== 'string') return '';

	let cleaned = text;

	// Remove code blocks (```...``` and `...`)
	cleaned = cleaned.replace(/```[\s\S]*?```/g, '');
	cleaned = cleaned.replace(/`[^`]*`/g, '');

	// Remove headings (# ## ### etc.)
	cleaned = cleaned.replace(/^#{1,6}\s+/gm, '');

	// Remove horizontal rules
	cleaned = cleaned.replace(/^[-*_]{3,}$/gm, '');

	// Remove links but keep the text [text](url) -> text
	cleaned = cleaned.replace(/\[([^\]]*)\]\([^)]*\)/g, '$1');

	// Remove images ![alt](url)
	cleaned = cleaned.replace(/!\[([^\]]*)\]\([^)]*\)/g, '');

	// Remove emphasis (**bold**, *italic*, __bold__, _italic_)
	cleaned = cleaned.replace(/\*\*([^*]*)\*\*/g, '$1');
	cleaned = cleaned.replace(/\*([^*]*)\*/g, '$1');
	cleaned = cleaned.replace(/__([^_]*)__/g, '$1');
	cleaned = cleaned.replace(/_([^_]*)_/g, '$1');

	// Remove strikethrough ~~text~~
	cleaned = cleaned.replace(/~~([^~]*)~~/g, '$1');

	// Remove blockquotes
	cleaned = cleaned.replace(/^>\s*/gm, '');

	// Remove list markers (- * + and numbered lists)
	cleaned = cleaned.replace(/^[\s]*[-*+]\s+/gm, '');
	cleaned = cleaned.replace(/^[\s]*\d+\.\s+/gm, '');

	// Remove HTML tags
	cleaned = cleaned.replace(/<[^>]*>/g, '');

	// Normalize whitespace
	cleaned = cleaned.replace(/\s+/g, ' ');

	// Remove leading/trailing whitespace
	cleaned = cleaned.trim();

	return cleaned;
}

/**
 * Prepares text for preview display by stripping markdown and truncating
 * @param text - The text to prepare
 * @param maxLength - Maximum length of the result (default: 200)
 * @returns Clean, truncated text suitable for preview
 */
export function prepareTextForPreview(text: string, maxLength: number = 200): string {
	const cleaned = stripMarkdownForPreview(text);

	if (cleaned.length <= maxLength) {
		return cleaned;
	}

	// Truncate at word boundary
	const truncated = cleaned.substring(0, maxLength);
	const lastSpace = truncated.lastIndexOf(' ');

	if (lastSpace > maxLength * 0.8) {
		return truncated.substring(0, lastSpace) + '...';
	}

	return truncated + '...';
}

/**
 * Extracts the first meaningful sentence from text after stripping markdown
 * Useful for getting a concise preview from longer content
 */
export function extractFirstSentenceForPreview(text: string): string {
	const cleaned = stripMarkdownForPreview(text);

	// Split by sentence endings
	const sentences = cleaned.split(/[.!?]+/).filter(s => s.trim().length > 0);

	if (sentences.length === 0) return cleaned;

	// Return first sentence, but ensure it's not too short or too long
	const firstSentence = sentences[0].trim();

	if (firstSentence.length < 20 && sentences.length > 1) {
		// If first sentence is very short, combine with second
		return (firstSentence + '. ' + sentences[1].trim()).substring(0, 200);
	}

	return prepareTextForPreview(firstSentence, 200);
}
