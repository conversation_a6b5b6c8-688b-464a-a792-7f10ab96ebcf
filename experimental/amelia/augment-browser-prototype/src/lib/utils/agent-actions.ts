import { sendChatMessage } from '$lib/stores/data-operations.svelte';
import { addOptimisticMessage, clearOptimisticMessage } from '$lib/stores/global-state.svelte';
import { addToast } from '$lib/stores/toast';

/**
 * Send a pull request creation message to an agent
 * Follows the same optimistic UI pattern as regular chat messages
 */
export async function sendPullRequestMessage(agentId: string): Promise<void> {
	const messageToSend = 'Please create a pull request with the changes you have made.';

	// Add optimistic message to global state
	addOptimisticMessage(agentId, messageToSend, 'user');

	try {
		// Send the chat message
		await sendChatMessage(agentId, messageToSend);

		// Clear optimistic message once real message is sent
		clearOptimisticMessage(agentId);

		addToast({
			type: 'success',
			message: 'PR creation request sent successfully',
			duration: 5000
		});
	} catch (error) {
		console.error('Failed to send PR creation message:', error);

		// Clear optimistic message on error
		clearOptimisticMessage(agentId);

		const errorMessage =
			error instanceof Error ? error.message : 'Failed to send PR creation request';
		addToast({
			type: 'error',
			message: errorMessage,
			duration: 5000
		});

		throw error;
	}
}

/**
 * Send a custom message to an agent with optimistic UI updates
 * Generic function that can be used for various agent actions
 */
export async function sendAgentMessage(
	agentId: string,
	message: string,
	options?: {
		successMessage?: string;
		errorMessage?: string;
		userGuidelines?: string;
		workspaceGuidelines?: string;
		agentMemories?: string;
	}
): Promise<void> {
	const {
		successMessage = 'Message sent successfully',
		errorMessage = 'Failed to send message',
		...sendOptions
	} = options || {};

	// Add optimistic message to global state
	addOptimisticMessage(agentId, message, 'user');

	try {
		// Send the chat message
		await sendChatMessage(agentId, message, sendOptions);

		// Clear optimistic message once real message is sent
		clearOptimisticMessage(agentId);

		addToast({
			type: 'success',
			message: successMessage,
			duration: 5000
		});
	} catch (error) {
		console.error('Failed to send agent message:', error);

		// Clear optimistic message on error
		clearOptimisticMessage(agentId);

		const finalErrorMessage = error instanceof Error ? error.message : errorMessage;
		addToast({
			type: 'error',
			message: finalErrorMessage,
			duration: 5000
		});

		throw error;
	}
}
