/**
 * Simple hash function to convert a string to a consistent numeric value
 * This is used for seeding random generation to ensure consistency
 */
export function stringToHash(str: string): number {
	let hash = 0;
	if (str.length === 0) return hash;

	for (let i = 0; i < str.length; i++) {
		const char = str.charCodeAt(i);
		hash = ((hash << 5) - hash) + char;
		hash = hash & hash; // Convert to 32-bit integer
	}

	return Math.abs(hash);
}

/**
 * Seeded random number generator using a simple LCG (Linear Congruential Generator)
 * This ensures consistent "random" values for the same seed
 */
export class SeededRandom {
	private seed: number;

	constructor(seed: number) {
		this.seed = seed % 2147483647;
		if (this.seed <= 0) this.seed += 2147483646;
	}

	/**
	 * Generate next random number between 0 and 1
	 */
	next(): number {
		this.seed = (this.seed * 16807) % 2147483647;
		return (this.seed - 1) / 2147483646;
	}

	/**
	 * Generate random integer between min (inclusive) and max (exclusive)
	 */
	nextInt(min: number, max: number): number {
		return Math.floor(this.next() * (max - min)) + min;
	}

	/**
	 * Pick a random element from an array
	 */
	pick<T>(array: T[]): T {
		return array[this.nextInt(0, array.length)];
	}
}

/**
 * Convert a string to a seeded random generator
 */
export function stringToSeededRandom(str: string): SeededRandom {
	return new SeededRandom(stringToHash(str));
}
