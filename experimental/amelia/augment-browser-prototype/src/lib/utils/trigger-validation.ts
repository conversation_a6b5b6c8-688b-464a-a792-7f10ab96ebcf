/**
 * Trigger validation utility based on protobuf specification
 * Validates trigger configurations according to specs/remote_agent_actions.proto
 */

// Import enums from trigger-enums to maintain consistency
import {
	EventSource,
	GitHubEntityType,
	LinearEntityType,
	TriggerConditionType
} from '$lib/types/trigger-enums';

// Event Source enum values from protobuf
export const EVENT_SOURCE = {
	UNSPECIFIED: EventSource.EVENT_SOURCE_UNSPECIFIED,
	GITHUB: EventSource.EVENT_SOURCE_GITHUB,
	LINEAR: EventSource.EVENT_SOURCE_LINEAR,
	JIRA: EventSource.EVENT_SOURCE_JIRA
} as const;

// GitHub Entity Type enum values
export const GITHUB_ENTITY_TYPE = {
	UNSPECIFIED: GitHubEntityType.GITHUB_ENTITY_TYPE_UNSPECIFIED,
	PULL_REQUEST: GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST,
	WORKFLOW_RUN: GitHubEntityType.GITHUB_ENTITY_TYPE_WORKFLOW_RUN
} as const;

// Linear Entity Type enum values
export const LINEAR_ENTITY_TYPE = {
	UNSPECIFIED: LinearEntityType.LINEAR_ENTITY_TYPE_UNSPECIFIED,
	ISSUE: LinearEntityType.LINEAR_ENTITY_TYPE_ISSUE
} as const;

// Trigger Condition Type enum values
export const TRIGGER_CONDITION_TYPE = {
	UNSPECIFIED: TriggerConditionType.TRIGGER_CONDITION_TYPE_UNSPECIFIED,
	GITHUB: TriggerConditionType.TRIGGER_CONDITION_GITHUB,
	LINEAR: TriggerConditionType.TRIGGER_CONDITION_LINEAR
} as const;

// Linear priority values
export const LINEAR_PRIORITY = {
	NONE: 0,
	URGENT: 1,
	HIGH: 2,
	MEDIUM: 3,
	LOW: 4
} as const;

// Linear state types
export const LINEAR_STATE_TYPES = [
	'backlog',
	'unstarted',
	'started',
	'completed',
	'canceled'
] as const;

// GitHub activity types for pull requests
export const GITHUB_PR_ACTIVITY_TYPES = [
	'opened',
	'synchronize',
	'ready_for_review',
	'closed',
	'edited',
	'assigned',
	'unassigned',
	'review_requested',
	'review_request_removed',
	'labeled',
	'unlabeled'
] as const;

// GitHub workflow run statuses
export const GITHUB_WORKFLOW_STATUSES = ['queued', 'in_progress', 'completed'] as const;

// GitHub workflow run conclusions
export const GITHUB_WORKFLOW_CONCLUSIONS = [
	'success',
	'failure',
	'cancelled',
	'skipped',
	'timed_out',
	'action_required'
] as const;

export interface ValidationError {
	field: string;
	message: string;
	code: string;
}

export interface ValidationResult {
	isValid: boolean;
	errors: ValidationError[];
}

/**
 * Validates a trigger configuration according to protobuf specification
 */
export function validateTriggerConfiguration(config: any): ValidationResult {
	const errors: ValidationError[] = [];

	// Validate required fields
	if (!config.name || typeof config.name !== 'string' || config.name.trim().length === 0) {
		errors.push({
			field: 'name',
			message: 'Name is required and must be a non-empty string',
			code: 'REQUIRED_FIELD'
		});
	}

	// Validate event_source
	const validEventSources = [
		EVENT_SOURCE.UNSPECIFIED,
		EVENT_SOURCE.GITHUB,
		EVENT_SOURCE.LINEAR,
		EVENT_SOURCE.JIRA
	];
	if (!config.event_source || validEventSources.indexOf(config.event_source) === -1) {
		errors.push({
			field: 'event_source',
			message: `Invalid event_source. Must be one of: ${validEventSources.join(', ')}`,
			code: 'INVALID_ENUM_VALUE'
		});
	}

	// Validate conditions
	if (!config.conditions) {
		errors.push({
			field: 'conditions',
			message: 'Conditions are required',
			code: 'REQUIRED_FIELD'
		});
	} else {
		const conditionErrors = validateTriggerConditions(config.conditions, config.event_source);
		errors.push(...conditionErrors);
	}

	// Validate agent_config
	if (!config.agent_config) {
		errors.push({
			field: 'agent_config',
			message: 'Agent configuration is required',
			code: 'REQUIRED_FIELD'
		});
	} else {
		const agentConfigErrors = validateAgentConfig(config.agent_config);
		errors.push(...agentConfigErrors);
	}

	// Validate optional fields
	if (config.description !== undefined && typeof config.description !== 'string') {
		errors.push({
			field: 'description',
			message: 'Description must be a string',
			code: 'INVALID_TYPE'
		});
	}

	if (config.enabled !== undefined && typeof config.enabled !== 'boolean') {
		errors.push({
			field: 'enabled',
			message: 'Enabled must be a boolean',
			code: 'INVALID_TYPE'
		});
	}

	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * Validates trigger conditions based on the condition type
 */
function validateTriggerConditions(conditions: any, eventSource: number): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate type field
	const validConditionTypes = [
		TRIGGER_CONDITION_TYPE.UNSPECIFIED,
		TRIGGER_CONDITION_TYPE.GITHUB,
		TRIGGER_CONDITION_TYPE.LINEAR
	];
	if (!conditions.type || validConditionTypes.indexOf(conditions.type) === -1) {
		errors.push({
			field: 'conditions.type',
			message: `Invalid condition type. Must be one of: ${validConditionTypes.join(', ')}`,
			code: 'INVALID_ENUM_VALUE'
		});
		return errors; // Can't validate further without valid type
	}

	// Ensure condition type matches event source
	if (eventSource === EVENT_SOURCE.GITHUB && conditions.type !== TRIGGER_CONDITION_TYPE.GITHUB) {
		errors.push({
			field: 'conditions.type',
			message: 'Condition type must be GITHUB (1) when event_source is GITHUB (1)',
			code: 'CONDITION_TYPE_MISMATCH'
		});
	}

	if (eventSource === EVENT_SOURCE.LINEAR && conditions.type !== TRIGGER_CONDITION_TYPE.LINEAR) {
		errors.push(
			{
				field: 'conditions.type',
				message: 'Condition type must be LINEAR (2) when event_source is LINEAR (2)',
				code: 'CONDITION_TYPE_MISMATCH'
			},
			conditions
		);
	}

	// Validate provider-specific conditions
	if (conditions.type === TRIGGER_CONDITION_TYPE.GITHUB) {
		if (!conditions.github) {
			errors.push({
				field: 'conditions.github',
				message: 'GitHub conditions are required when type is GITHUB',
				code: 'REQUIRED_FIELD'
			});
		} else {
			const githubErrors = validateGitHubTriggerConditions(conditions.github);
			errors.push(...githubErrors);
		}
	}

	if (conditions.type === TRIGGER_CONDITION_TYPE.LINEAR) {
		if (!conditions.linear) {
			errors.push({
				field: 'conditions.linear',
				message: 'Linear conditions are required when type is LINEAR',
				code: 'REQUIRED_FIELD'
			});
		} else {
			const linearErrors = validateLinearTriggerConditions(conditions.linear);
			errors.push(...linearErrors);
		}
	}

	return errors;
}

/**
 * Validates GitHub trigger conditions
 */
function validateGitHubTriggerConditions(github: any): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate entity_type
	const validGitHubEntityTypes = [
		GITHUB_ENTITY_TYPE.UNSPECIFIED,
		GITHUB_ENTITY_TYPE.PULL_REQUEST,
		GITHUB_ENTITY_TYPE.WORKFLOW_RUN
	];
	if (!github.entity_type || validGitHubEntityTypes.indexOf(github.entity_type) === -1) {
		errors.push({
			field: 'conditions.github.entity_type',
			message: `Invalid GitHub entity type. Must be one of: ${validGitHubEntityTypes.join(', ')}`,
			code: 'INVALID_ENUM_VALUE'
		});
		return errors; // Can't validate further without valid entity type
	}

	// Validate entity-specific conditions
	if (github.entity_type === GITHUB_ENTITY_TYPE.PULL_REQUEST) {
		if (!github.pull_request) {
			errors.push({
				field: 'conditions.github.pull_request',
				message: 'Pull request conditions are required when entity_type is PULL_REQUEST',
				code: 'REQUIRED_FIELD'
			});
		} else {
			const prErrors = validateGitHubPullRequestConditions(github.pull_request);
			errors.push(...prErrors);
		}
	}

	if (github.entity_type === GITHUB_ENTITY_TYPE.WORKFLOW_RUN) {
		if (!github.workflow_run) {
			errors.push({
				field: 'conditions.github.workflow_run',
				message: 'Workflow run conditions are required when entity_type is WORKFLOW_RUN',
				code: 'REQUIRED_FIELD'
			});
		} else {
			const workflowErrors = validateGitHubWorkflowRunConditions(github.workflow_run);
			errors.push(...workflowErrors);
		}
	}

	return errors;
}

/**
 * Validates GitHub pull request conditions
 */
function validateGitHubPullRequestConditions(pr: any): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate repository format (owner/repo)
	if (pr.repository !== undefined) {
		if (typeof pr.repository !== 'string') {
			errors.push({
				field: 'conditions.github.pull_request.repository',
				message: 'Repository must be a string',
				code: 'INVALID_TYPE'
			});
		} else if (pr.repository.trim() !== '' && !pr.repository.includes('/')) {
			errors.push({
				field: 'conditions.github.pull_request.repository',
				message: 'Repository must be in "owner/repo" format',
				code: 'INVALID_FORMAT'
			});
		}
	}

	// Validate string fields
	const stringFields = [
		'author',
		'assignee',
		'reviewer',
		'title_contains',
		'base_branch',
		'head_branch'
	];
	for (const field of stringFields) {
		if (pr[field] !== undefined && typeof pr[field] !== 'string') {
			errors.push({
				field: `conditions.github.pull_request.${field}`,
				message: `${field} must be a string`,
				code: 'INVALID_TYPE'
			});
		}
	}

	// Validate activity_types array
	if (pr.activity_types !== undefined) {
		if (!Array.isArray(pr.activity_types)) {
			errors.push({
				field: 'conditions.github.pull_request.activity_types',
				message: 'Activity types must be an array',
				code: 'INVALID_TYPE'
			});
		} else {
			for (const activityType of pr.activity_types) {
				if (typeof activityType !== 'string') {
					errors.push({
						field: 'conditions.github.pull_request.activity_types',
						message: 'All activity types must be strings',
						code: 'INVALID_TYPE'
					});
					break;
				}
				if (GITHUB_PR_ACTIVITY_TYPES.indexOf(activityType as any) === -1) {
					errors.push({
						field: 'conditions.github.pull_request.activity_types',
						message: `Invalid activity type "${activityType}". Must be one of: ${GITHUB_PR_ACTIVITY_TYPES.join(', ')}`,
						code: 'INVALID_ENUM_VALUE'
					});
				}
			}
		}
	}

	// Validate labels array
	if (pr.labels !== undefined) {
		if (!Array.isArray(pr.labels)) {
			errors.push({
				field: 'conditions.github.pull_request.labels',
				message: 'Labels must be an array',
				code: 'INVALID_TYPE'
			});
		} else {
			for (const label of pr.labels) {
				if (typeof label !== 'string') {
					errors.push({
						field: 'conditions.github.pull_request.labels',
						message: 'All labels must be strings',
						code: 'INVALID_TYPE'
					});
					break;
				}
			}
		}
	}

	// Validate draft boolean
	if (pr.draft !== undefined && typeof pr.draft !== 'boolean') {
		errors.push({
			field: 'conditions.github.pull_request.draft',
			message: 'Draft must be a boolean',
			code: 'INVALID_TYPE'
		});
	}

	return errors;
}

/**
 * Validates GitHub workflow run conditions
 */
function validateGitHubWorkflowRunConditions(workflow: any): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate repository format (owner/repo)
	if (workflow.repository !== undefined) {
		if (typeof workflow.repository !== 'string') {
			errors.push({
				field: 'conditions.github.workflow_run.repository',
				message: 'Repository must be a string',
				code: 'INVALID_TYPE'
			});
		} else if (workflow.repository.trim() !== '' && !workflow.repository.includes('/')) {
			errors.push({
				field: 'conditions.github.workflow_run.repository',
				message: 'Repository must be in "owner/repo" format',
				code: 'INVALID_FORMAT'
			});
		}
	}

	// Validate string fields
	const stringFields = ['actor', 'triggering_actor', 'event', 'branch', 'title_contains'];
	for (const field of stringFields) {
		if (workflow[field] !== undefined && typeof workflow[field] !== 'string') {
			errors.push({
				field: `conditions.github.workflow_run.${field}`,
				message: `${field} must be a string`,
				code: 'INVALID_TYPE'
			});
		}
	}

	// Validate status
	if (workflow.status !== undefined) {
		if (typeof workflow.status !== 'string') {
			errors.push({
				field: 'conditions.github.workflow_run.status',
				message: 'Status must be a string',
				code: 'INVALID_TYPE'
			});
		} else if (GITHUB_WORKFLOW_STATUSES.indexOf(workflow.status as any) === -1) {
			errors.push({
				field: 'conditions.github.workflow_run.status',
				message: `Invalid status "${workflow.status}". Must be one of: ${GITHUB_WORKFLOW_STATUSES.join(', ')}`,
				code: 'INVALID_ENUM_VALUE'
			});
		}
	}

	// Validate conclusion
	if (workflow.conclusion !== undefined) {
		if (typeof workflow.conclusion !== 'string') {
			errors.push({
				field: 'conditions.github.workflow_run.conclusion',
				message: 'Conclusion must be a string',
				code: 'INVALID_TYPE'
			});
		} else if (GITHUB_WORKFLOW_CONCLUSIONS.indexOf(workflow.conclusion as any) === -1) {
			errors.push({
				field: 'conditions.github.workflow_run.conclusion',
				message: `Invalid conclusion "${workflow.conclusion}". Must be one of: ${GITHUB_WORKFLOW_CONCLUSIONS.join(', ')}`,
				code: 'INVALID_ENUM_VALUE'
			});
		}
	}

	// Validate workflows array
	if (workflow.workflows !== undefined) {
		if (!Array.isArray(workflow.workflows)) {
			errors.push({
				field: 'conditions.github.workflow_run.workflows',
				message: 'Workflows must be an array',
				code: 'INVALID_TYPE'
			});
		} else {
			for (const workflowName of workflow.workflows) {
				if (typeof workflowName !== 'string') {
					errors.push({
						field: 'conditions.github.workflow_run.workflows',
						message: 'All workflow names must be strings',
						code: 'INVALID_TYPE'
					});
					break;
				}
			}
		}
	}

	// Validate that status and conclusion are not both specified (per protobuf comments)
	if (workflow.status !== undefined && workflow.conclusion !== undefined) {
		errors.push({
			field: 'conditions.github.workflow_run',
			message:
				'Cannot specify both status and conclusion - GitHub API uses single status parameter',
			code: 'MUTUALLY_EXCLUSIVE_FIELDS'
		});
	}

	return errors;
}

/**
 * Validates Linear trigger conditions
 */
function validateLinearTriggerConditions(linear: any): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate entity_type
	const validLinearEntityTypes = [LINEAR_ENTITY_TYPE.UNSPECIFIED, LINEAR_ENTITY_TYPE.ISSUE];
	if (!linear.entity_type || validLinearEntityTypes.indexOf(linear.entity_type) === -1) {
		errors.push({
			field: 'conditions.linear.entity_type',
			message: `Invalid Linear entity type. Must be one of: ${validLinearEntityTypes.join(', ')}`,
			code: 'INVALID_ENUM_VALUE'
		});
		return errors; // Can't validate further without valid entity type
	}

	// Validate entity-specific conditions
	if (linear.entity_type === LINEAR_ENTITY_TYPE.ISSUE) {
		if (!linear.issue) {
			errors.push({
				field: 'conditions.linear.issue',
				message: 'Issue conditions are required when entity_type is ISSUE',
				code: 'REQUIRED_FIELD'
			});
		} else {
			const issueErrors = validateLinearIssueConditions(linear.issue);
			errors.push(...issueErrors);
		}
	}

	return errors;
}

/**
 * Validates Linear issue conditions
 */
function validateLinearIssueConditions(issue: any): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate string fields
	const stringFields = ['team', 'creator', 'assignee', 'project', 'title_contains'];
	for (const field of stringFields) {
		if (issue[field] !== undefined && typeof issue[field] !== 'string') {
			errors.push({
				field: `conditions.linear.issue.${field}`,
				message: `${field} must be a string`,
				code: 'INVALID_TYPE'
			});
		}
	}

	// Validate number fields
	const numberFields = ['min_estimate', 'max_estimate'];
	for (const field of numberFields) {
		if (issue[field] !== undefined && typeof issue[field] !== 'number') {
			errors.push({
				field: `conditions.linear.issue.${field}`,
				message: `${field} must be a number`,
				code: 'INVALID_TYPE'
			});
		}
	}

	// Validate estimate range
	if (issue.min_estimate !== undefined && issue.max_estimate !== undefined) {
		if (issue.min_estimate > issue.max_estimate) {
			errors.push({
				field: 'conditions.linear.issue',
				message: 'min_estimate cannot be greater than max_estimate',
				code: 'INVALID_RANGE'
			});
		}
	}

	// Validate string arrays
	const stringArrayFields = ['states', 'state_types', 'labels', 'activity_types'];
	for (const field of stringArrayFields) {
		if (issue[field] !== undefined) {
			if (!Array.isArray(issue[field])) {
				errors.push({
					field: `conditions.linear.issue.${field}`,
					message: `${field} must be an array`,
					code: 'INVALID_TYPE'
				});
			} else {
				for (const item of issue[field]) {
					if (typeof item !== 'string') {
						errors.push({
							field: `conditions.linear.issue.${field}`,
							message: `All ${field} must be strings`,
							code: 'INVALID_TYPE'
						});
						break;
					}
				}
			}
		}
	}

	// Validate state_types values
	if (issue.state_types !== undefined && Array.isArray(issue.state_types)) {
		const validStateTypes = ['backlog', 'unstarted', 'started', 'completed', 'canceled'];
		for (const stateType of issue.state_types) {
			if (validStateTypes.indexOf(stateType) === -1) {
				errors.push({
					field: 'conditions.linear.issue.state_types',
					message: `Invalid state type "${stateType}". Must be one of: ${validStateTypes.join(', ')}`,
					code: 'INVALID_ENUM_VALUE'
				});
			}
		}
	}

	// Validate priorities array
	if (issue.priorities !== undefined) {
		if (!Array.isArray(issue.priorities)) {
			errors.push({
				field: 'conditions.linear.issue.priorities',
				message: 'Priorities must be an array',
				code: 'INVALID_TYPE'
			});
		} else {
			for (const priority of issue.priorities) {
				if (typeof priority !== 'number' || priority % 1 !== 0) {
					errors.push({
						field: 'conditions.linear.issue.priorities',
						message: 'All priorities must be integers',
						code: 'INVALID_TYPE'
					});
					break;
				}
				const validPriorities = [0, 1, 2, 3, 4]; // LINEAR_PRIORITY values
				if (validPriorities.indexOf(priority) === -1) {
					errors.push({
						field: 'conditions.linear.issue.priorities',
						message: `Invalid priority ${priority}. Must be one of: ${validPriorities.join(', ')}`,
						code: 'INVALID_ENUM_VALUE'
					});
				}
			}
		}
	}

	return errors;
}

/**
 * Validates agent configuration
 */
function validateAgentConfig(agentConfig: any): ValidationError[] {
	const errors: ValidationError[] = [];

	// Validate required user_guidelines
	if (
		!agentConfig.user_guidelines ||
		typeof agentConfig.user_guidelines !== 'string' ||
		agentConfig.user_guidelines.trim().length === 0
	) {
		errors.push({
			field: 'agent_config.user_guidelines',
			message: 'User guidelines are required and must be a non-empty string',
			code: 'REQUIRED_FIELD'
		});
	}

	// Validate optional string fields
	const optionalStringFields = ['workspace_guidelines', 'setup_script'];
	for (const field of optionalStringFields) {
		if (agentConfig[field] !== undefined && typeof agentConfig[field] !== 'string') {
			errors.push({
				field: `agent_config.${field}`,
				message: `${field} must be a string`,
				code: 'INVALID_TYPE'
			});
		}
	}

	// Validate starting_nodes array
	if (agentConfig.starting_nodes !== undefined) {
		if (!Array.isArray(agentConfig.starting_nodes)) {
			errors.push({
				field: 'agent_config.starting_nodes',
				message: 'Starting nodes must be an array',
				code: 'INVALID_TYPE'
			});
		}
		// Note: We don't validate the contents of starting_nodes as they can be complex objects
		// and the protobuf spec references remote_agents.AgentConfig which we don't have access to
	}

	// Validate workspace_setup if present
	if (agentConfig.workspace_setup !== undefined) {
		if (typeof agentConfig.workspace_setup !== 'object' || agentConfig.workspace_setup === null) {
			errors.push({
				field: 'agent_config.workspace_setup',
				message: 'Workspace setup must be an object',
				code: 'INVALID_TYPE'
			});
		} else {
			// Helper function to validate github_ref object
			const validateGithubRef = (githubRef: any, fieldPrefix: string) => {
				if (typeof githubRef !== 'object' || githubRef === null) {
					errors.push({
						field: fieldPrefix,
						message: 'GitHub ref must be an object',
						code: 'INVALID_TYPE'
					});
					return;
				}

				// For new direct format, url is required
				// For old nested format, either repository or url is acceptable
				const hasRepository = githubRef.repository !== undefined;
				const hasUrl = githubRef.url !== undefined;
				const isDirectFormat = fieldPrefix === 'agent_config.workspace_setup.github_ref';

				if (isDirectFormat && !hasUrl) {
					errors.push({
						field: fieldPrefix,
						message: 'GitHub ref must have url field',
						code: 'REQUIRED_FIELD'
					});
				} else if (!isDirectFormat && !hasRepository && !hasUrl) {
					errors.push({
						field: fieldPrefix,
						message: 'GitHub ref must have either repository or url field',
						code: 'REQUIRED_FIELD'
					});
				}

				if (hasRepository && typeof githubRef.repository !== 'string') {
					errors.push({
						field: `${fieldPrefix}.repository`,
						message: 'Repository must be a string',
						code: 'INVALID_TYPE'
					});
				}

				if (hasUrl && typeof githubRef.url !== 'string') {
					errors.push({
						field: `${fieldPrefix}.url`,
						message: 'URL must be a string',
						code: 'INVALID_TYPE'
					});
				}

				// Validate ref field
				if (githubRef.ref !== undefined && typeof githubRef.ref !== 'string') {
					errors.push({
						field: `${fieldPrefix}.ref`,
						message: 'Ref must be a string',
						code: 'INVALID_TYPE'
					});
				}
			};

			// Validate new direct github_ref format
			if (agentConfig.workspace_setup.github_ref !== undefined) {
				validateGithubRef(
					agentConfig.workspace_setup.github_ref,
					'agent_config.workspace_setup.github_ref'
				);
			}

			// Validate old starting_files format (for backward compatibility)
			if (agentConfig.workspace_setup.starting_files !== undefined) {
				if (
					typeof agentConfig.workspace_setup.starting_files !== 'object' ||
					agentConfig.workspace_setup.starting_files === null
				) {
					errors.push({
						field: 'agent_config.workspace_setup.starting_files',
						message: 'Starting files must be an object',
						code: 'INVALID_TYPE'
					});
				} else {
					// Validate github_ref if present
					if (agentConfig.workspace_setup.starting_files.github_ref !== undefined) {
						validateGithubRef(
							agentConfig.workspace_setup.starting_files.github_ref,
							'agent_config.workspace_setup.starting_files.github_ref'
						);
					}
				}
			}
		}
	}

	return errors;
}

/**
 * Helper function to format validation errors for display
 */
export function formatValidationErrors(errors: ValidationError[]): string {
	if (errors.length === 0) return '';

	return errors.map((error) => `${error.field}: ${error.message}`).join('\n');
}

/**
 * Helper function to check if a trigger configuration is valid
 */
export function isValidTriggerConfiguration(config: any): boolean {
	const result = validateTriggerConfiguration(config);
	return result.isValid;
}
