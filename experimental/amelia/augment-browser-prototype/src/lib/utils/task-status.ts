import type { Task, TaskUIStatus } from '$lib/types';
import type { CleanRemoteAgent, CleanChangedFile } from '$lib/api/unified-client';
import { RemoteAgentStatus } from '$lib/api/unified-client';
import {
	PlayCircle,
	ExclamationTriangle,
	CheckCircle,
	Clock,
	Cog6Tooth,
	PauseCircle,
	QuestionMarkCircle,
	InformationCircle,
	ChatBubbleLeftRight,
	HandThumbUp
} from 'svelte-hero-icons';
import type { IconSource } from 'svelte-hero-icons';

/**
 * Determines the UI status of a task based on its remote agent status and file changes
 */
export function getTaskUIStatus(
	task: Task | null,
	remoteAgent: CleanRemoteAgent | null,
	changedFiles: CleanChangedFile[] = []
): TaskUIStatus {
	// Check if task is completed (PR merged)
	if (task?.status === 'COMPLETED') {
		return 'completed';
	}

	// No remote agent assigned
	if (!remoteAgent) {
		return 'no_agent';
	}

	// Check agent status
	switch (remoteAgent.status) {
		case RemoteAgentStatus.agentStarting:
			return 'starting';

		case RemoteAgentStatus.agentRunning:
			return 'running';

		case RemoteAgentStatus.agentFailed:
			return 'failed';

		case RemoteAgentStatus.agentPending:
			return 'pending';

		case RemoteAgentStatus.agentIdle:
			// Agent is idle - check if there are any code changes
			if (hasCodeChanges(changedFiles)) {
				return 'idle';
			} else {
				return 'waiting';
			}

		case RemoteAgentStatus.agentUnspecified:
		default:
			return 'no_agent';
	}
}

/**
 * Checks if there are any meaningful code changes in the changed files
 */
export function hasCodeChanges(changedFiles: CleanChangedFile[]): boolean {
	if (!changedFiles || changedFiles.length === 0) {
		return false;
	}

	// Filter out non-meaningful changes (like empty files, config files, etc.)
	const meaningfulChanges = changedFiles.filter((file) => {
		const filePath = file.path || file.oldPath || '';

		// Skip empty files or files without content
		if (!file.content && !file.oldContent) {
			return false;
		}

		// Skip certain file types that might not be meaningful code changes
		const skipPatterns = [
			/\.log$/,
			/\.tmp$/,
			/\.cache$/,
			/node_modules\//,
			/\.git\//,
			/package-lock\.json$/,
			/yarn\.lock$/
		];

		if (skipPatterns.some((pattern) => pattern.test(filePath))) {
			return false;
		}

		return true;
	});

	return meaningfulChanges.length > 0;
}

/**
 * Gets the appropriate icon for a task's UI status
 */
export function getTaskStatusIcon(status: TaskUIStatus): IconSource {
	switch (status) {
		case 'no_agent':
			return PlayCircle;
		case 'running':
			return Cog6Tooth; // Will be replaced with loading spinner
		case 'failed':
			return ExclamationTriangle;
		case 'idle':
			return HandThumbUp;
		case 'waiting':
			return ChatBubbleLeftRight;
		case 'starting':
			return Cog6Tooth; // Will be replaced with loading spinner
		case 'pending':
			return PauseCircle;
		case 'completed':
			return CheckCircle;
		default:
			return PlayCircle;
	}
}

/**
 * Gets the appropriate color classes for a task's UI status
 */
export function getTaskStatusColor(status: TaskUIStatus): string {
	switch (status) {
		case 'no_agent':
			return 'text-slate-300 hover:text-blue-600';
		case 'running':
			return 'text-blue-500';
		case 'failed':
			return 'text-red-500';
		case 'idle':
			return 'text-slate-900';
		case 'waiting':
			return 'text-slate-900';
		case 'starting':
			return 'text-blue-500';
		case 'pending':
			return 'text-slate-500';
		case 'completed':
			return 'text-green-600';
		default:
			return 'text-slate-400';
	}
}

/**
 * Gets the tooltip text for a task's UI status
 */
export function getTaskStatusTooltip(status: TaskUIStatus): string {
	switch (status) {
		case 'no_agent':
			return 'Click to create remote agent';
		case 'running':
			return 'Agent is actively working';
		case 'failed':
			return 'Agent encountered an error';
		case 'idle':
			return 'Agent is ready for work (has made changes)';
		case 'waiting':
			return 'Agent is idle, waiting for instructions';
		case 'starting':
			return 'Agent is starting up...';
		case 'pending':
			return 'Agent is pending assignment';
		case 'completed':
			return 'Task completed (PR merged)';
		default:
			return 'Remote agent status unknown';
	}
}

/**
 * Gets a human-readable label for a task's UI status
 */
export function getTaskStatusLabel(status: TaskUIStatus): string {
	switch (status) {
		case 'no_agent':
			return 'No Agent';
		case 'running':
			return 'Running';
		case 'failed':
			return 'Failed';
		case 'idle':
			return 'Idle';
		case 'waiting':
			return 'Waiting';
		case 'starting':
			return 'Starting';
		case 'pending':
			return 'Pending';
		case 'completed':
			return 'Completed';
		default:
			return 'Unknown';
	}
}

/**
 * Checks if the task status indicates the agent is actively working
 */
export function isTaskStatusActive(status: TaskUIStatus): boolean {
	return status === 'running' || status === 'starting';
}

/**
 * Checks if the task status indicates the agent needs attention
 */
export function isTaskStatusNeedsAttention(status: TaskUIStatus): boolean {
	return status === 'failed' || status === 'waiting';
}

/**
 * Checks if the task status indicates the agent is ready for work
 */
export function isTaskStatusReady(status: TaskUIStatus): boolean {
	return status === 'idle' || status === 'waiting';
}

/**
 * Checks if the task status should show a loading spinner instead of an icon
 */
export function shouldShowLoadingSpinner(status: TaskUIStatus): boolean {
	return status === 'running' || status === 'starting';
}
