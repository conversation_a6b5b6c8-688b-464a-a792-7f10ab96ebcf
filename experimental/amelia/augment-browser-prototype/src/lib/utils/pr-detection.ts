import type { CleanRemoteAgent, CleanChatExchangeData } from '$lib/api/unified-client';
import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import { extractPullRequestCreationInfo, type PullRequestInfo } from './remote-agent-utils';
import { extractEntityKeyFromAgent } from '$lib/stores/global-state.svelte';

/**
 * Enhanced PR information that includes entity data
 */
export interface EnhancedPRInfo extends PullRequestInfo {
	entity?: UnifiedEntity;
	rawEntity?: any; // Raw GitHub/Linear entity data
	source: 'linked_entity' | 'chat_history';
	title?: string;
	state?: string;
	description?: string;
	author?: string;
	createdAt?: string;
	updatedAt?: string;
	mergeable?: boolean;
	draft?: boolean;
	reviewDecision?: string;
	reviewers?: Array<{
		login: string;
		avatar_url?: string;
		state?: string;
	}>;
	labels?: Array<{
		name: string;
		color?: string;
	}>;
}

/**
 * Comprehensive function to detect and get PR information for a remote agent
 * Checks both linked entities and chat history for PR information
 *
 * @param agent - The remote agent to check
 * @param exchanges - Chat exchanges from the agent
 * @param linkedEntity - The linked entity if available
 * @returns Enhanced PR information or null if no PR is found
 */
export function detectPRInfo(
	agent: CleanRemoteAgent,
	exchanges: CleanChatExchangeData[] | null,
	linkedEntity: UnifiedEntity | null,
	rawEntityData?: any
): EnhancedPRInfo | null {
	// First, check if the linked entity is a GitHub PR
	if (linkedEntity && isGitHubPR(linkedEntity)) {
		return createEnhancedPRInfoFromEntity(linkedEntity, rawEntityData);
	}

	// If no linked PR entity, check chat history for PR creation
	if (exchanges) {
		const chatPRInfo = extractPullRequestCreationInfo(exchanges);
		if (chatPRInfo) {
			return {
				...chatPRInfo,
				source: 'chat_history'
			};
		}
	}

	return null;
}

/**
 * Check if an entity is a GitHub Pull Request
 */
function isGitHubPR(entity: UnifiedEntity): boolean {
	return (
		entity.entityType === 'GITHUB_PULL_REQUEST' ||
		entity.type === 'GITHUB_PULL_REQUEST' ||
		(entity.url && entity.url.includes('/pull/'))
	);
}

/**
 * Create enhanced PR info from a GitHub PR entity
 */
function createEnhancedPRInfoFromEntity(
	entity: UnifiedEntity,
	rawEntityData?: any
): EnhancedPRInfo {
	// Extract PR number from URL or entity ID
	const prNumber = extractPRNumber(entity);

	// Build GitHub URLs from entity information
	const { prUrl, repoUrl } = buildGitHubUrls(entity, prNumber);

	// Extract comprehensive PR data from raw entity if available
	let enhancedData = {};
	if (rawEntityData && rawEntityData.pull_request) {
		const pr = rawEntityData.pull_request;
		enhancedData = {
			title: pr.title,
			state: pr.state,
			description: pr.body,
			author: pr.user?.login,
			createdAt: pr.created_at,
			updatedAt: pr.updated_at,
			mergeable: pr.mergeable,
			draft: pr.draft,
			reviewDecision: pr.review_decision,
			reviewers:
				pr.requested_reviewers?.map((reviewer: any) => ({
					login: reviewer.login,
					avatar_url: reviewer.avatar_url,
					state: reviewer.state
				})) || [],
			labels:
				pr.labels?.map((label: any) => ({
					name: label.name,
					color: label.color
				})) || []
		};
	}

	return {
		prNumber,
		prUrl,
		repoUrl,
		entity,
		rawEntity: rawEntityData,
		source: 'linked_entity',
		// Use raw data if available, fallback to entity metadata
		title: enhancedData.title || entity.title,
		state: enhancedData.state || entity.metadata?.state || entity.state,
		description: enhancedData.description || entity.description,
		author: enhancedData.author || entity.metadata?.author?.login || entity.metadata?.user?.login,
		createdAt: enhancedData.createdAt || entity.metadata?.created_at || entity.createdAt,
		updatedAt: enhancedData.updatedAt || entity.metadata?.updated_at || entity.updatedAt,
		mergeable: enhancedData.mergeable ?? entity.metadata?.mergeable,
		draft: enhancedData.draft ?? entity.metadata?.draft,
		reviewDecision: enhancedData.reviewDecision || entity.metadata?.review_decision,
		reviewers: enhancedData.reviewers || [],
		labels: enhancedData.labels || []
	};
}

/**
 * Extract PR number from entity URL or ID
 */
function extractPRNumber(entity: UnifiedEntity): number {
	// Try to extract from URL first
	if (entity.url) {
		const urlMatch = entity.url.match(/\/pull\/(\d+)/);
		if (urlMatch) {
			return parseInt(urlMatch[1], 10);
		}
	}

	// Try to extract from entity ID
	if (entity.id) {
		const idMatch = entity.id.match(/(\d+)$/);
		if (idMatch) {
			return parseInt(idMatch[1], 10);
		}
	}

	// Fallback to parsing the ID as a number
	const numericId = parseInt(entity.id, 10);
	return isNaN(numericId) ? 0 : numericId;
}

/**
 * Build GitHub URLs from entity information
 */
function buildGitHubUrls(
	entity: UnifiedEntity,
	prNumber: number
): { prUrl: string; repoUrl: string } {
	// Try to extract repo info from URL
	if (entity.url) {
		const urlMatch = entity.url.match(/github\.com\/([^\/]+\/[^\/]+)/);
		if (urlMatch) {
			const repoPath = urlMatch[1];
			return {
				prUrl: `https://github.com/${repoPath}/pull/${prNumber}`,
				repoUrl: `https://github.com/${repoPath}`
			};
		}
	}

	// Try to extract from metadata
	if (entity.metadata?.repository) {
		const repo = entity.metadata.repository;
		const repoPath = `${repo.owner?.login || repo.owner}/${repo.name}`;
		return {
			prUrl: `https://github.com/${repoPath}/pull/${prNumber}`,
			repoUrl: `https://github.com/${repoPath}`
		};
	}

	// Fallback - construct from entity ID pattern if possible
	// This is a best-effort approach for cases where we have limited info
	const fallbackRepo = 'unknown/unknown';
	return {
		prUrl: `https://github.com/${fallbackRepo}/pull/${prNumber}`,
		repoUrl: `https://github.com/${fallbackRepo}`
	};
}

/**
 * Get entity key for a PR from enhanced PR info
 * This can be used to fetch the entity if it's not already loaded
 */
export function getPREntityKey(prInfo: EnhancedPRInfo): string | null {
	if (prInfo.entity) {
		// If we have the entity, we can construct the key
		const providerId = 'github'; // GitHub PRs are always from GitHub
		return `${providerId}:GITHUB_PULL_REQUEST:${prInfo.prNumber}`;
	}

	// If we only have chat history info, we can still try to construct a key
	// Extract repo info from URLs
	const repoMatch = prInfo.repoUrl.match(/github\.com\/([^\/]+\/[^\/]+)/);
	if (repoMatch) {
		const repoPath = repoMatch[1];
		return `github:GITHUB_PULL_REQUEST:${prInfo.prNumber}:${repoPath}`;
	}

	return null;
}
