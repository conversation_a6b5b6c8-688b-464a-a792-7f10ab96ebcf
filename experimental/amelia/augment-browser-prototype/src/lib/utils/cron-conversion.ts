/**
 * Cron conversion utilities for natural language to cron expression conversion
 */

import cronstrue from 'cronstrue';
import type { SilentExchangeRequest } from '$lib/api/chat';

export interface CronConversionResult {
	success: boolean;
	cronExpression?: string;
	description?: string;
	error?: string;
}

export interface CronValidationResult {
	isValid: boolean;
	errors: string[];
	warnings: string[];
}

/**
 * Convert natural language description to cron expression using AI
 */
export async function convertDescriptionToCron(
	description: string,
	signal?: AbortSignal
): Promise<CronConversionResult> {
	try {
		const prompt = `Convert this natural language schedule description to a cron expression:
"${description}"

You must respond with ONLY a valid cron expression wrapped in XML tags like this:
<cron-expression>0 9 * * *</cron-expression>

Important rules:
- Use standard 5-field cron format: minute hour day month weekday
- Weekdays: 0=Sunday, 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday
- Use * for "any" value
- Use specific numbers for exact times
- Use ranges like 1-5 for Monday through Friday
- Use lists like 1,3,5 for specific days

Examples:
- "every day at 9am" → <cron-expression>0 9 * * *</cron-expression>
- "every Monday at 2pm" → <cron-expression>0 14 * * 1</cron-expression>
- "every hour" → <cron-expression>0 * * * *</cron-expression>
- "every weekday at 8:30am" → <cron-expression>30 8 * * 1-5</cron-expression>
- "every 15 minutes" → <cron-expression>*/15 * * * *</cron-expression>
- "daily at midnight" → <cron-expression>0 0 * * *</cron-expression>
- "weekly on Friday at 5pm" → <cron-expression>0 17 * * 5</cron-expression>

Respond with ONLY the cron expression in the XML tags, nothing else.`;

		const request: SilentExchangeRequest = {
			message: prompt,
			mode: 'CHAT' // CHAT mode
		};

		const response = await fetch('/api/chat/silent', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(request),
			signal
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const data = await response.json();
		const responseText = data.text || '';

		// Extract cron expression from XML tags
		const cronPattern = /<cron-expression>(.*?)<\/cron-expression>/;
		const match = responseText.match(cronPattern);

		if (match && match[1]) {
			const cronExpression = match[1].trim();

			// Validate the extracted cron expression
			const validation = validateCronExpression(cronExpression);

			if (validation.isValid) {
				// Generate human-readable description
				let humanDescription = '';
				try {
					humanDescription = cronstrue.toString(cronExpression);
				} catch {
					humanDescription = description; // Fallback to original description
				}

				return {
					success: true,
					cronExpression,
					description: humanDescription
				};
			} else {
				return {
					success: false,
					error: `AI generated invalid cron expression: ${validation.errors.join(', ')}`
				};
			}
		} else {
			return {
				success: false,
				error: 'AI response did not contain a valid cron expression format'
			};
		}
	} catch (error) {
		console.error('Cron conversion error:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Failed to convert description'
		};
	}
}

/**
 * Validate a cron expression
 */
export function validateCronExpression(cronExpression: string): CronValidationResult {
	const errors: string[] = [];
	const warnings: string[] = [];

	if (!cronExpression || !cronExpression.trim()) {
		errors.push('Cron expression is required');
		return { isValid: false, errors, warnings };
	}

	const trimmed = cronExpression.trim();
	const parts = trimmed.split(/\s+/);

	// Check if it has exactly 5 parts (minute hour day month weekday)
	if (parts.length !== 5) {
		errors.push(`Cron expression must have exactly 5 fields, got ${parts.length}`);
		return { isValid: false, errors, warnings };
	}

	const [minute, hour, day, month, weekday] = parts;

	// Validate each field
	try {
		validateCronField(minute, 0, 59, 'minute');
		validateCronField(hour, 0, 23, 'hour');
		validateCronField(day, 1, 31, 'day');
		validateCronField(month, 1, 12, 'month');
		validateCronField(weekday, 0, 7, 'weekday'); // 0 and 7 both represent Sunday
	} catch (error) {
		errors.push(error instanceof Error ? error.message : 'Invalid cron field');
	}

	// Try to parse with cronstrue for additional validation
	try {
		cronstrue.toString(trimmed);
	} catch (error) {
		errors.push('Invalid cron expression format');
	}

	// Add warnings for potentially problematic expressions
	if (minute === '*' && hour === '*') {
		warnings.push('This will run every minute - consider if this is intended');
	}

	if (day !== '*' && weekday !== '*') {
		warnings.push('Specifying both day of month and day of week can lead to unexpected behavior');
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings
	};
}

/**
 * Validate individual cron field
 */
function validateCronField(field: string, min: number, max: number, fieldName: string): void {
	if (field === '*') return; // Wildcard is always valid

	// Handle step values (e.g., */5)
	if (field.includes('/')) {
		const [range, step] = field.split('/');
		if (range !== '*' && !isValidRange(range, min, max)) {
			throw new Error(`Invalid ${fieldName} range: ${range}`);
		}
		const stepNum = parseInt(step, 10);
		if (isNaN(stepNum) || stepNum <= 0) {
			throw new Error(`Invalid ${fieldName} step value: ${step}`);
		}
		return;
	}

	// Handle ranges (e.g., 1-5)
	if (field.includes('-')) {
		const [start, end] = field.split('-').map((n) => parseInt(n, 10));
		if (isNaN(start) || isNaN(end) || start < min || end > max || start > end) {
			throw new Error(`Invalid ${fieldName} range: ${field}`);
		}
		return;
	}

	// Handle lists (e.g., 1,3,5)
	if (field.includes(',')) {
		const values = field.split(',').map((n) => parseInt(n, 10));
		for (const value of values) {
			if (isNaN(value) || value < min || value > max) {
				throw new Error(`Invalid ${fieldName} value in list: ${value}`);
			}
		}
		return;
	}

	// Handle single values
	const value = parseInt(field, 10);
	if (isNaN(value) || value < min || value > max) {
		// Special case for weekday: 7 is also valid for Sunday
		if (fieldName === 'weekday' && value === 7) return;
		throw new Error(`Invalid ${fieldName} value: ${field} (must be between ${min} and ${max})`);
	}
}

/**
 * Check if a range string is valid
 */
function isValidRange(range: string, min: number, max: number): boolean {
	if (range === '*') return true;

	if (range.includes('-')) {
		const [start, end] = range.split('-').map((n) => parseInt(n, 10));
		return !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end;
	}

	const value = parseInt(range, 10);
	return !isNaN(value) && value >= min && value <= max;
}

/**
 * Get common cron expression suggestions
 */
export function getCronSuggestions(): string[] {
	return [
		'every day at 9am',
		'every weekday at 8:30am',
		'every Monday at 2pm',
		'every hour',
		'every 30 minutes',
		'daily at midnight',
		'weekly on Friday at 5pm',
		'every 15 minutes during business hours'
	];
}

/**
 * Get common cron presets with their expressions
 */
export function getCronPresets(): Array<{ label: string; value: string; description: string }> {
	return [
		{
			label: 'Every day at 9 AM',
			value: '0 9 * * *',
			description: 'Daily at 9:00 AM'
		},
		{
			label: 'Every weekday at 8:30 AM',
			value: '30 8 * * 1-5',
			description: 'Monday through Friday at 8:30 AM'
		},
		{
			label: 'Every Monday at 2 PM',
			value: '0 14 * * 1',
			description: 'Weekly on Monday at 2:00 PM'
		},
		{
			label: 'Every hour',
			value: '0 * * * *',
			description: 'At the top of every hour'
		},
		{
			label: 'Every 30 minutes',
			value: '*/30 * * * *',
			description: 'Every 30 minutes'
		},
		{
			label: 'Daily at midnight',
			value: '0 0 * * *',
			description: 'Every day at 12:00 AM'
		},
		{
			label: 'Weekly on Friday at 5 PM',
			value: '0 17 * * 5',
			description: 'Every Friday at 5:00 PM'
		}
	];
}
