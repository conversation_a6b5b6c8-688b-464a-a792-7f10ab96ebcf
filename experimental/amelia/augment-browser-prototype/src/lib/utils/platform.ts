/**
 * Platform detection utilities for keyboard shortcuts and OS-specific behavior
 */

/**
 * Detects if the current platform is macOS
 */
export function isMacOS(): boolean {
	if (typeof window === 'undefined') return false;

	return navigator.platform.indexOf('Mac') > -1 || navigator.userAgent.indexOf('Mac') > -1;
}

/**
 * Detects if the current platform is Windows
 */
export function isWindows(): boolean {
	if (typeof window === 'undefined') return false;

	return navigator.platform.indexOf('Win') > -1 || navigator.userAgent.indexOf('Windows') > -1;
}

/**
 * Gets the appropriate modifier key symbol for the current platform
 * @returns '⌘' for macOS, 'Ctrl' for other platforms
 */
export function getModifierKeySymbol(): string {
	return isMacOS() ? '⌘' : 'Ctrl';
}

/**
 * Gets the appropriate modifier key name for the current platform
 * @returns 'Cmd' for macOS, 'Ctrl' for other platforms
 */
export function getModifierKeyName(): string {
	return isMacOS() ? 'Cmd' : 'Ctrl';
}

/**
 * Checks if the correct modifier key is pressed for the current platform
 * @param event - KeyboardEvent to check
 * @returns true if the platform-appropriate modifier key is pressed
 */
export function isModifierKeyPressed(event: KeyboardEvent): boolean {
	return isMacOS() ? event.metaKey : event.ctrlKey;
}

/**
 * Checks if the standard cross-platform modifier key combination is pressed
 * This is the same as the existing pattern used throughout the codebase
 * @param event - KeyboardEvent to check
 * @returns true if either metaKey (Mac) or ctrlKey (Windows/Linux) is pressed
 */
export function isStandardModifierPressed(event: KeyboardEvent): boolean {
	return event.metaKey || event.ctrlKey;
}
