import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
	isMacOS,
	isWindows,
	getModifierKeySymbol,
	getModifierKeyName,
	isModifierKeyPressed,
	isStandardModifierPressed
} from '../platform';

// Mock window and navigator
const mockNavigator = {
	platform: '',
	userAgent: ''
};

Object.defineProperty(global, 'window', {
	value: {},
	writable: true
});

Object.defineProperty(global, 'navigator', {
	value: mockNavigator,
	writable: true
});

describe('Platform utilities', () => {
	beforeEach(() => {
		mockNavigator.platform = '';
		mockNavigator.userAgent = '';
	});

	describe('isMacOS', () => {
		it('should return true for Mac platform', () => {
			mockNavigator.platform = 'MacIntel';
			expect(isMacOS()).toBe(true);
		});

		it('should return true for Mac user agent', () => {
			mockNavigator.userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)';
			expect(isMacOS()).toBe(true);
		});

		it('should return false for non-Mac platforms', () => {
			mockNavigator.platform = 'Win32';
			mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)';
			expect(isMacOS()).toBe(false);
		});
	});

	describe('isWindows', () => {
		it('should return true for Windows platform', () => {
			mockNavigator.platform = 'Win32';
			expect(isWindows()).toBe(true);
		});

		it('should return true for Windows user agent', () => {
			mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)';
			expect(isWindows()).toBe(true);
		});

		it('should return false for non-Windows platforms', () => {
			mockNavigator.platform = 'MacIntel';
			mockNavigator.userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)';
			expect(isWindows()).toBe(false);
		});
	});

	describe('getModifierKeySymbol', () => {
		it('should return ⌘ for macOS', () => {
			mockNavigator.platform = 'MacIntel';
			expect(getModifierKeySymbol()).toBe('⌘');
		});

		it('should return Ctrl for Windows', () => {
			mockNavigator.platform = 'Win32';
			expect(getModifierKeySymbol()).toBe('Ctrl');
		});
	});

	describe('getModifierKeyName', () => {
		it('should return Cmd for macOS', () => {
			mockNavigator.platform = 'MacIntel';
			expect(getModifierKeyName()).toBe('Cmd');
		});

		it('should return Ctrl for Windows', () => {
			mockNavigator.platform = 'Win32';
			expect(getModifierKeyName()).toBe('Ctrl');
		});
	});

	describe('isModifierKeyPressed', () => {
		it('should check metaKey for macOS', () => {
			mockNavigator.platform = 'MacIntel';
			const event = { metaKey: true, ctrlKey: false } as KeyboardEvent;
			expect(isModifierKeyPressed(event)).toBe(true);
		});

		it('should check ctrlKey for Windows', () => {
			mockNavigator.platform = 'Win32';
			const event = { metaKey: false, ctrlKey: true } as KeyboardEvent;
			expect(isModifierKeyPressed(event)).toBe(true);
		});
	});

	describe('isStandardModifierPressed', () => {
		it('should return true for metaKey', () => {
			const event = { metaKey: true, ctrlKey: false } as KeyboardEvent;
			expect(isStandardModifierPressed(event)).toBe(true);
		});

		it('should return true for ctrlKey', () => {
			const event = { metaKey: false, ctrlKey: true } as KeyboardEvent;
			expect(isStandardModifierPressed(event)).toBe(true);
		});

		it('should return true for both keys', () => {
			const event = { metaKey: true, ctrlKey: true } as KeyboardEvent;
			expect(isStandardModifierPressed(event)).toBe(true);
		});

		it('should return false for neither key', () => {
			const event = { metaKey: false, ctrlKey: false } as KeyboardEvent;
			expect(isStandardModifierPressed(event)).toBe(false);
		});
	});
});
