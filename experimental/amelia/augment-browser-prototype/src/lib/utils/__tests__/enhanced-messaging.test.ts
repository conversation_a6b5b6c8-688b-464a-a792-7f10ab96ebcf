import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	createOptimisticMessage,
	markMessageDelivered,
	markMessageFailed,
	hasPendingMessages,
	getOldestPendingMessageTime,
	cancelPendingMessagesForAgent,
	cleanupAllPendingMessages
} from '../optimistic-messages';
import {
	initializeSequenceTracker,
	processSequenceId,
	getLastProcessedSequenceId,
	cleanupSequenceTracker
} from '../sequence-tracking';
import {
	getAgentIndicatorConfig,
	formatElapsedTime,
	getWorkspaceStatusText,
	type ToolUseState
} from '../agent-status-indicators.svelte';
import { getTimeoutForAgent, getTimeoutErrorMessage } from '../optimistic-messages';
import {
	RemoteAgentStatus,
	RemoteAgentWorkspaceStatus,
	type CleanRemoteAgent
} from '$lib/api/unified-client';

// Mock timers
vi.useFakeTimers();

describe('Enhanced Messaging System', () => {
	beforeEach(() => {
		// Clean up any existing state
		cleanupAllPendingMessages();
		vi.clearAllTimers();
	});

	afterEach(() => {
		cleanupAllPendingMessages();
		vi.clearAllTimers();
	});

	describe('Optimistic Messages', () => {
		it('should create and track optimistic messages', () => {
			const agentId = 'test-agent-1';
			const content = 'Test message';

			const messageId = createOptimisticMessage(agentId, content);

			expect(messageId).toBeTruthy();
			expect(hasPendingMessages(agentId)).toBe(true);
			expect(getOldestPendingMessageTime(agentId)).toBeTruthy();
		});

		it('should handle message delivery', () => {
			const agentId = 'test-agent-1';
			const onDelivered = vi.fn();

			const messageId = createOptimisticMessage(agentId, 'Test', 'user', 60000, {
				onDelivered
			});

			markMessageDelivered(messageId);

			expect(hasPendingMessages(agentId)).toBe(false);
			expect(onDelivered).toHaveBeenCalled();
		});

		it('should handle message timeout', () => {
			const agentId = 'test-agent-1';
			const onTimeout = vi.fn();

			createOptimisticMessage(agentId, 'Test', 'user', 1000, {
				onTimeout
			});

			// Fast-forward time to trigger timeout
			vi.advanceTimersByTime(1001);

			expect(hasPendingMessages(agentId)).toBe(false);
			expect(onTimeout).toHaveBeenCalled();
		});

		it('should handle message failure', () => {
			const agentId = 'test-agent-1';
			const onError = vi.fn();

			const messageId = createOptimisticMessage(agentId, 'Test', 'user', 60000, {
				onError
			});

			markMessageFailed(messageId, 'Network error');

			expect(hasPendingMessages(agentId)).toBe(false);
			expect(onError).toHaveBeenCalledWith('Network error');
		});

		it('should cancel all pending messages for an agent', () => {
			const agentId = 'test-agent-1';

			createOptimisticMessage(agentId, 'Message 1');
			createOptimisticMessage(agentId, 'Message 2');

			expect(hasPendingMessages(agentId)).toBe(true);

			cancelPendingMessagesForAgent(agentId);

			expect(hasPendingMessages(agentId)).toBe(false);
		});
	});

	describe('Sequence Tracking', () => {
		it('should initialize and track sequence IDs', () => {
			const agentId = 'test-agent-1';

			initializeSequenceTracker(agentId, 0);

			const result1 = processSequenceId(agentId, 1);
			expect(result1.isValid).toBe(true);
			expect(result1.isDuplicate).toBe(false);
			expect(result1.missedSequenceIds).toEqual([]);

			const result2 = processSequenceId(agentId, 2);
			expect(result2.isValid).toBe(true);
			expect(result2.isDuplicate).toBe(false);

			expect(getLastProcessedSequenceId(agentId)).toBe(2);
		});

		it('should detect duplicate sequence IDs', () => {
			const agentId = 'test-agent-1';

			initializeSequenceTracker(agentId, 0);
			processSequenceId(agentId, 1);

			const result = processSequenceId(agentId, 1);
			expect(result.isDuplicate).toBe(true);
		});

		it('should detect missed sequence IDs', () => {
			const agentId = 'test-agent-1';

			initializeSequenceTracker(agentId, 0);
			processSequenceId(agentId, 1);

			const result = processSequenceId(agentId, 3); // Missing sequence 2
			expect(result.missedSequenceIds).toEqual([2]);
		});

		it('should request resync for too many missed sequences', () => {
			const agentId = 'test-agent-1';

			initializeSequenceTracker(agentId, 0);
			processSequenceId(agentId, 1);

			const result = processSequenceId(agentId, 12); // Missing 10+ sequences
			expect(result.shouldRequestResync).toBe(true);
		});

		it('should cleanup sequence tracker', () => {
			const agentId = 'test-agent-1';

			initializeSequenceTracker(agentId, 0);
			processSequenceId(agentId, 1);

			cleanupSequenceTracker(agentId);

			expect(getLastProcessedSequenceId(agentId)).toBe(0);
		});
	});

	describe('Agent Status Indicators', () => {
		const createMockAgent = (
			status: RemoteAgentStatus,
			workspaceStatus?: RemoteAgentWorkspaceStatus
		): CleanRemoteAgent => ({
			id: 'test-agent',
			title: 'Test Agent',
			status,
			workspaceStatus:
				workspaceStatus || RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING,
			startedAt: new Date(),
			updatedAt: new Date(),
			sessionSummary: 'Test session summary'
		});

		it('should show generating response for running agent', () => {
			const agent = createMockAgent(RemoteAgentStatus.AGENT_RUNNING);
			const config = getAgentIndicatorConfig(agent, false, true);

			expect(config.isActive).toBe(true);
			expect(config.showGeneratingResponse).toBe(true);
			expect(config.indicatorText).toBe('Generating response...');
		});

		it('should show resuming for resuming workspace', () => {
			const agent = createMockAgent(
				RemoteAgentStatus.AGENT_RUNNING,
				RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING
			);
			const config = getAgentIndicatorConfig(agent, false, false);

			expect(config.isActive).toBe(true);
			expect(config.showResumingAgent).toBe(true);
			expect(config.indicatorText).toBe('Resuming remote agent...');
		});

		it('should show tool execution indicator', () => {
			const agent = createMockAgent(RemoteAgentStatus.AGENT_RUNNING);
			const toolState: ToolUseState = { phase: 'running', toolName: 'web_search' };
			const config = getAgentIndicatorConfig(agent, false, false, toolState);

			expect(config.isActive).toBe(true);
			expect(config.showRunningSpacer).toBe(true);
			expect(config.indicatorText).toBe('Running web_search...');
		});

		it('should prioritize resuming over generating response', () => {
			const agent = createMockAgent(
				RemoteAgentStatus.AGENT_RUNNING,
				RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING
			);
			const config = getAgentIndicatorConfig(agent, false, true);

			expect(config.showResumingAgent).toBe(true);
			expect(config.showGeneratingResponse).toBe(false);
			expect(config.indicatorText).toBe('Resuming remote agent...');
		});

		it('should show elapsed timer after 5 seconds', () => {
			const agent = createMockAgent(RemoteAgentStatus.AGENT_RUNNING);
			const messageStartTime = Date.now() - 6000; // 6 seconds ago
			const config = getAgentIndicatorConfig(
				agent,
				false,
				true,
				{ phase: 'idle' },
				messageStartTime
			);

			expect(config.showElapsedTimer).toBe(true);
		});

		it('should not show indicator for idle agent without pending messages', () => {
			const agent = createMockAgent(RemoteAgentStatus.AGENT_IDLE);
			const config = getAgentIndicatorConfig(agent, false, false);

			expect(config.isActive).toBe(false);
		});

		it('should format elapsed time correctly', () => {
			expect(formatElapsedTime(500)).toBe('500ms');
			expect(formatElapsedTime(1500)).toBe('1.5s');
			expect(formatElapsedTime(65000)).toBe('1m 5s');
		});
	});

	describe('Integration Tests', () => {
		it('should work together for complete message flow', () => {
			const agentId = 'test-agent-1';
			const agent = {
				id: agentId,
				name: 'Test Agent',
				status: RemoteAgentStatus.AGENT_RUNNING,
				workspaceStatus: RemoteAgentWorkspaceStatus.WORKSPACE_IDLE,
				createdAt: Date.now(),
				updatedAt: Date.now()
			};

			// Initialize sequence tracking
			initializeSequenceTracker(agentId, 0);

			// Create optimistic message
			const messageId = createOptimisticMessage(agentId, 'Test message');

			// Check status indicator shows generating response
			const config = getAgentIndicatorConfig(agent, false, true);
			expect(config.isActive).toBe(true);
			expect(config.showGeneratingResponse).toBe(true);

			// Process sequence update
			const sequenceResult = processSequenceId(agentId, 1);
			expect(sequenceResult.isValid).toBe(true);

			// Mark message as delivered
			markMessageDelivered(messageId);

			// Verify cleanup
			expect(hasPendingMessages(agentId)).toBe(false);
			expect(getLastProcessedSequenceId(agentId)).toBe(1);
		});
	});
});
