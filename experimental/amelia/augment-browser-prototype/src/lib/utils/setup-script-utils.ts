import { ChatResultNodeType, type CleanChatExchangeData } from '$lib/api/unified-client';

export interface SetupScriptVersion {
	content: string;
	timestamp: string;
	status: string;
	requestId: string;
	id: string;
	testResults?: Array<{
		command: string;
		status: 'success' | 'error' | 'skipped' | 'loading';
		output?: string;
	}>;
	scriptResult?: {
		output?: string;
		status?: 'success' | 'error' | 'skipped';
		exitCode?: number;
	};
}

// Types matching the actual data structure
interface SetupScriptToolResult {
	script_result: {
		command: string;
		output: string;
		status: 'SUCCESS' | 'FAILED' | 'SKIPPED';
		exit_code: number;
	};
	test_results: Array<{
		command: string;
		output: string;
		status: 'SUCCESS' | 'FAILED' | 'SKIPPED';
		exit_code: number;
	}>;
}

/**
 * Extract setup script versions from chat exchanges
 */
export function extractSetupScriptVersions(
	exchanges: CleanChatExchangeData[]
): SetupScriptVersion[] {
	if (!exchanges || exchanges.length === 0) return [];

	const versions: SetupScriptVersion[] = [];

	console.log('Extracting setup script versions from exchanges:', exchanges);

	for (let i = 0; i < exchanges.length; i++) {
		const exchange = exchanges[i];
		// Look for setup script tool uses in response nodes
		const responseNodes = exchange.exchange.responseNodes || [];

		for (const node of responseNodes) {
			const toolUse = node.toolUse;
			if (node.type === ChatResultNodeType.TOOL_USE && toolUse?.toolName === 'setup-script') {
				try {
					const toolInput =
						typeof toolUse.inputJson === 'string'
							? JSON.parse(toolUse.inputJson)
							: toolUse.inputJson;

					if (toolInput?.script_content || toolInput?.content) {
						const scriptContent = toolInput.script_content || toolInput.content;

						// Look for tool results in subsequent exchanges
						const nextExchange = i + 1 < exchanges.length ? exchanges[i + 1] : null;

						versions.push({
							content: scriptContent,
							timestamp: exchange.finishedAt || new Date().toISOString(),
							status: 'completed', // We'll need to track tool use state separately
							requestId: exchange.exchange.requestId,
							id: toolUse.toolUseId || `${exchange.finishedAt}-${Math.random()}`,
							testResults: nextExchange ? extractTestResults(nextExchange, toolUse.toolUseId) : [],
							scriptResult: nextExchange
								? extractScriptResult(nextExchange, toolUse.toolUseId)
								: undefined
						});
					}
				} catch (error) {
					console.warn('Failed to parse setup script tool input:', error);
				}
			}
		}
	}

	return versions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
}

/**
 * Extract test results from tool node
 */
function extractTestResults(
	exchange: CleanChatExchangeData,
	toolUseId: string
): SetupScriptVersion['testResults'] {
	try {
		// Look for tool result in request nodes of subsequent exchanges
		// The tool result comes back as a toolResultNode in the next exchange
		const requestNodes = exchange.exchange.requestNodes || [];

		for (const node of requestNodes) {
			if (node.toolResultNode?.toolUseId === toolUseId) {
				const content = node.toolResultNode.content;
				if (content) {
					const result = JSON.parse(content) as SetupScriptToolResult;
					return (
						result.test_results?.map((test) => ({
							command: test.command,
							status:
								test.status === 'SUCCESS'
									? 'success'
									: test.status === 'FAILED'
										? 'error'
										: 'skipped',
							output: test.output || undefined
						})) || []
					);
				}
			}
		}

		return [];
	} catch (error) {
		console.warn('Failed to extract test results:', error);
		return [];
	}
}

/**
 * Extract script execution results from tool node
 */
function extractScriptResult(
	exchange: CleanChatExchangeData,
	toolUseId: string
): SetupScriptVersion['scriptResult'] {
	try {
		// Look for tool result in request nodes of subsequent exchanges
		const requestNodes = exchange.exchange.requestNodes || [];

		for (const node of requestNodes) {
			if (node.toolResultNode?.toolUseId === toolUseId) {
				const content = node.toolResultNode.content;
				if (content) {
					const result = JSON.parse(content) as SetupScriptToolResult;
					const scriptResult = result.script_result;
					if (scriptResult) {
						return {
							output: scriptResult.output || undefined,
							status:
								scriptResult.status === 'SUCCESS'
									? 'success'
									: scriptResult.status === 'FAILED'
										? 'error'
										: 'skipped',
							exitCode: scriptResult.exit_code || undefined
						};
					}
				}
			}
		}

		return undefined;
	} catch (error) {
		console.warn('Failed to extract script result:', error);
		return undefined;
	}
}

/**
 * Check if an agent has setup scripts based on its exchanges
 */
export function hasSetupScripts(exchanges: CleanChatExchangeData[]): boolean {
	return extractSetupScriptVersions(exchanges).length > 0;
}

/**
 * Get the latest setup script from exchanges
 */
export function getLatestSetupScript(
	exchanges: CleanChatExchangeData[]
): SetupScriptVersion | null {
	const versions = extractSetupScriptVersions(exchanges);
	return versions.length > 0 ? versions[0] : null;
}

/**
 * Save setup script to specified location
 * This is a placeholder - in a real implementation, this would make an API call
 */
export async function saveSetupScript(
	scriptContent: string,
	location: 'home' | 'workspace' | 'git-root',
	filename?: string
): Promise<void> {
	// Placeholder implementation
	console.log(
		'Saving setup script to:',
		location,
		filename,
		'Content length:',
		scriptContent.length
	);

	// In a real implementation, this would:
	// 1. Make an API call to save the script
	// 2. Handle different save locations
	// 3. Generate appropriate filename if not provided
	// 4. Return success/error status

	// For now, just simulate a delay
	await new Promise((resolve) => setTimeout(resolve, 500));
}

/**
 * Open setup script in editor
 * This is a placeholder - in a real implementation, this would open the script in VS Code
 */
export async function openSetupScriptInEditor(scriptContent: string): Promise<void> {
	// Placeholder implementation
	console.log('Opening setup script in editor');

	// In a real implementation, this would:
	// 1. Create a temporary file or use VS Code's scratch file API
	// 2. Open the file in the editor
	// 3. Set appropriate language mode (bash/shell)

	// For now, just copy to clipboard as fallback
	try {
		await navigator.clipboard.writeText(scriptContent);
		console.log('Script copied to clipboard as fallback');
	} catch (error) {
		console.warn('Failed to copy to clipboard:', error);
	}
}

/**
 * Execute setup script
 * This is a placeholder - in a real implementation, this would execute the script
 */
export async function executeSetupScript(scriptContent: string): Promise<{
	success: boolean;
	output?: string;
	error?: string;
}> {
	// Placeholder implementation
	console.log('Executing setup script, length:', scriptContent.length);

	// In a real implementation, this would:
	// 1. Make an API call to execute the script in the agent's environment
	// 2. Stream the output back to the UI
	// 3. Handle success/error states
	// 4. Run verification tests

	// For now, just simulate execution
	await new Promise((resolve) => setTimeout(resolve, 1000));

	return {
		success: true,
		output: 'Script execution simulated successfully'
	};
}
