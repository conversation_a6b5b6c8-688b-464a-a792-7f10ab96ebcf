/**
 * Utility functions for remote agent operations
 */

import type { CleanChatExchangeData } from '$lib/api/unified-client';
import {
	AGENT_WORK_INSTRUCTIONS,
	extractOriginalMessageFromInitial
} from '$lib/types/structured-response';
import { NOTES_GUIDELINE } from '$lib/utils/agent-prefix-versions';

export interface PullRequestInfo {
	prNumber: number;
	prUrl: string;
	repoUrl: string;
}

/**
 * Extract initial instructions from session summary
 * This function cleans up the session summary to get the user's original instructions
 *
 * @param sessionSummary - The raw session summary from the agent
 * @returns The cleaned initial instructions or null if not available
 */
export function extractInitialInstructionsFromSessionSummary(
	sessionSummary: string | null | undefined
): string | null {
	if (!sessionSummary) {
		return null;
	}

	// First, remove any structured response instructions prefix
	let cleanedSummary = extractOriginalMessageFromInitial(sessionSummary);

	// Remove trigger entity syntax if present (e.g., "Entity Type: GITHUB_ENTITY_TYPE_PULL_REQUEST Entity Id: 123")
	const triggerPattern = /Entity Type:\s*[A-Z_]+\s+Entity Id:\s*[\w-]+\s*/gi;
	cleanedSummary = cleanedSummary.replace(triggerPattern, '').trim();

	// Remove any leading/trailing punctuation or whitespace
	cleanedSummary = cleanedSummary.replace(/^[:\-\s]+|[:\-\s]+$/g, '').trim();

	if (cleanedSummary.startsWith(AGENT_WORK_INSTRUCTIONS)) {
		cleanedSummary = cleanedSummary.substring(AGENT_WORK_INSTRUCTIONS.length).trim();
	}
	if (cleanedSummary.startsWith(NOTES_GUIDELINE)) {
		cleanedSummary = cleanedSummary.substring(NOTES_GUIDELINE.length).trim();
	}

	if (cleanedSummary.startsWith('---')) {
		cleanedSummary = cleanedSummary.substring('---'.length).trim();
	}

	return cleanedSummary;
}

/**
 * Extract pull request creation information from chat exchanges
 * Specifically looks for GitHub API tool uses that create pull requests
 *
 * @param exchanges - Array of chat exchanges from the remote agent
 * @returns Pull request information if a PR creation is found, null otherwise
 */
export function extractPullRequestCreationInfo(
	exchanges: CleanChatExchangeData[]
): PullRequestInfo | null {
	if (!exchanges || exchanges.length === 0) return null;

	// Search exchanges from latest first to find the most recent PR creation
	for (let i = exchanges.length - 1; i >= 0; i--) {
		const exchange = exchanges[i];
		if (!exchange?.exchange?.responseNodes) continue;

		// Look for GitHub API tool use nodes that create pull requests
		for (const responseNode of exchange.exchange.responseNodes) {
			if (responseNode.type !== 5 || !responseNode.toolUse) continue;

			const toolUse = responseNode.toolUse;

			// Check if this is a GitHub API call
			if (toolUse.toolName !== 'github-api') continue;

			let inputData;
			try {
				inputData = JSON.parse(toolUse.inputJson);
			} catch {
				continue;
			}

			// Verify this is a pull request creation:
			// 1. Method should be POST
			// 2. Path should end with '/pulls' (without a number)
			// 3. Data should contain a title field
			if (
				inputData.method === 'POST' &&
				inputData.path?.endsWith('/pulls') &&
				inputData.data?.title
			) {
				// Find the corresponding tool result node using the tool use ID
				const toolResultNode = findToolResultNode(exchanges, toolUse.toolUseId);
				if (!toolResultNode) continue;

				// Extract PR information from the tool result
				const prInfo = extractPrInfoFromToolResult(toolResultNode.content);
				if (prInfo) {
					return prInfo;
				}
			}
		}
	}

	return null;
}

/**
 * Extract branch name from chat exchanges
 * Specifically looks for GitHub API tool uses that create pull requests
 *
 * @param exchanges - Array of chat exchanges from the remote agent
 * @returns Branch name if found, null otherwise
 */
export function extractBranchFromExchanges(exchanges: CleanChatExchangeData[]): string | null {
	if (!exchanges || exchanges.length === 0) return null;

	// want to look for tool calls like
	// {
	//     "id": 1,
	//     "type": 5,
	//     "content": "",
	//     "tool_use": {
	//         "tool_use_id": "toolu_vrtx_018dPBuypN9366zKfXiSFbS7",
	//         "tool_name": "launch-process",
	//         "input_json": "{\"command\": \"git checkout -b feature/noodles-blog-post\", \"cwd\": \"/mnt/persist/workspace\", \"wait\": true, \"max_wait_seconds\": 10}",
	//         "is_partial": false
	//     }
	// },

	for (let i = exchanges.length - 1; i >= 0; i--) {
		const exchange = exchanges[i];
		if (!exchange?.exchange?.responseNodes) continue;

		// Look for GitHub API tool use nodes that create pull requests
		for (const responseNode of exchange.exchange.responseNodes) {
			if (responseNode.type !== 5 || !responseNode.toolUse) continue;

			const toolUse = responseNode.toolUse;

			// Check if this is a launch process tool use
			if (toolUse.toolName !== 'launch-process') continue;

			let inputData;
			try {
				inputData = JSON.parse(toolUse.inputJson);
			} catch {
				continue;
			}

			// Verify this is a branch creation:
			// 1. Command should contain 'git checkout -b'
			if (inputData.command?.includes('git checkout -b')) {
				// Extract branch name from command
				const branchName = inputData.command.split(' ').pop();
				if (branchName) {
					return branchName;
				}
			}
		}
	}

	return null;
}

/**
 * Find the tool result node that corresponds to a given tool use ID
 */
function findToolResultNode(exchanges: CleanChatExchangeData[], toolUseId: string): any | null {
	for (const exchange of exchanges) {
		if (!exchange?.exchange?.requestNodes) continue;

		for (const requestNode of exchange.exchange.requestNodes) {
			if (
				requestNode.type === 1 && // Tool result node type
				requestNode.toolResultNode?.toolUseId === toolUseId
			) {
				return requestNode.toolResultNode;
			}
		}
	}

	return null;
}

/**
 * Extract pull request information from GitHub API tool result content
 */
function extractPrInfoFromToolResult(content: string): PullRequestInfo | null {
	if (!content) return null;

	try {
		// The content might be YAML-formatted response from GitHub API
		// Look for key fields: number, url, and extract repo URL from the API URL
		const numberMatch = content.match(/^number:\s*(\d+)$/m);
		const urlMatch = content.match(
			/^url:\s*(https:\/\/api\.github\.com\/repos\/[^/]+\/[^/]+\/pulls\/\d+)$/m
		);

		if (!numberMatch || !urlMatch) return null;

		const prNumber = parseInt(numberMatch[1], 10);
		const apiUrl = urlMatch[1];

		// Convert API URL to web URL
		// From: https://api.github.com/repos/owner/repo/pulls/123
		// To: https://github.com/owner/repo/pull/123
		const webUrlMatch = apiUrl.match(
			/https:\/\/api\.github\.com\/repos\/([^/]+\/[^/]+)\/pulls\/(\d+)/
		);
		if (!webUrlMatch) return null;

		const repoPath = webUrlMatch[1];
		const prUrl = `https://github.com/${repoPath}/pull/${prNumber}`;
		const repoUrl = `https://github.com/${repoPath}`;

		return {
			prNumber,
			prUrl,
			repoUrl
		};
	} catch {
		return null;
	}
}

/**
 * Check if there are file changes after the latest push or PR creation
 * Aggregates all changed files from the last push/PR creation to current state
 *
 * @param exchanges - Array of chat exchanges from the remote agent
 * @returns true if there are file changes after the latest push/PR creation, false otherwise
 */
export function hasFileChangesAfterLatestPush(exchanges: CleanChatExchangeData[]): boolean {
	if (!exchanges || exchanges.length === 0) return false;

	// Find the index of the latest push or PR creation
	const latestPushOrPRIndex = findLatestPushOrPRCreation(exchanges);

	// Aggregate all changed files after the latest push/PR creation
	const changedFilesAfterPush = getChangedFilesAfterIndex(exchanges, latestPushOrPRIndex);

	return changedFilesAfterPush.length > 0;
}

/**
 * Get all changed files after a specific exchange index
 * Aggregates changed files from all exchanges after the given index
 *
 * @param exchanges - Array of chat exchanges
 * @param afterIndex - Index after which to collect changed files (-1 means from beginning)
 * @returns Array of unique changed file paths
 */
export function getChangedFilesAfterIndex(
	exchanges: CleanChatExchangeData[],
	afterIndex: number
): string[] {
	const changedFilePaths = new Set<string>();

	// Start from the exchange after the push/PR creation (or from beginning if no push found)
	const startIndex = afterIndex + 1;

	for (let i = startIndex; i < exchanges.length; i++) {
		const exchange = exchanges[i];
		if (exchange.changedFiles && exchange.changedFiles.length > 0) {
			exchange.changedFiles.forEach((file) => {
				// Add both old and new paths to capture renames and modifications
				if (file.newPath) {
					changedFilePaths.add(file.newPath);
				}
				if (file.oldPath && file.oldPath !== file.newPath) {
					changedFilePaths.add(file.oldPath);
				}
			});
		}
	}

	return Array.from(changedFilePaths);
}

/**
 * Find the index of the latest push operation or PR creation
 * Returns -1 if no push/PR creation is found
 *
 * @param exchanges - Array of chat exchanges
 * @returns Index of the latest push/PR creation, or -1 if none found
 */
export function findLatestPushOrPRCreation(exchanges: CleanChatExchangeData[]): number {
	let latestIndex = -1;

	for (let i = 0; i < exchanges.length; i++) {
		const exchange = exchanges[i];
		if (!exchange?.exchange?.responseNodes) continue;

		for (const responseNode of exchange.exchange.responseNodes) {
			if (responseNode.type !== 5 || !responseNode.toolUse) continue;

			const toolUse = responseNode.toolUse;

			// Check for git push operations
			if (toolUse.toolName === 'launch-process') {
				let inputData;
				try {
					inputData = JSON.parse(toolUse.inputJson);
				} catch {
					continue;
				}

				const command = inputData.command || '';
				if (command.includes('git push') || command.includes('git commit')) {
					latestIndex = i;
				}
			}

			// Check for GitHub API operations that push changes or create PRs
			if (toolUse.toolName === 'github-api') {
				let inputData;
				try {
					inputData = JSON.parse(toolUse.inputJson);
				} catch {
					continue;
				}

				// Check for push-related operations or PR creation
				if (
					inputData.method === 'POST' &&
					(inputData.path?.includes('/commits') ||
						inputData.path?.includes('/git/refs') ||
						inputData.path?.includes('/contents/') ||
						(inputData.path?.endsWith('/pulls') && inputData.data?.title)) // PR creation
				) {
					latestIndex = i;
				}
			}
		}
	}

	return latestIndex;
}

/**
 * Get changed files from a specific exchange
 * @param exchanges - Array of chat exchanges
 * @param exchangeIndex - Index of the exchange to get changes from
 * @returns Array of changed file paths from that specific exchange
 */
export function getChangedFilesFromExchange(
	exchanges: CleanChatExchangeData[],
	exchangeIndex: number
): string[] {
	if (!exchanges || exchangeIndex < 0 || exchangeIndex >= exchanges.length) {
		return [];
	}

	const exchange = exchanges[exchangeIndex];
	if (!exchange.changedFiles || !Array.isArray(exchange.changedFiles)) {
		return [];
	}

	const changedFilePaths = new Set<string>();
	for (const file of exchange.changedFiles) {
		const filePath = file.path || file.oldPath;
		if (filePath) {
			changedFilePaths.add(filePath);
		}
	}

	return Array.from(changedFilePaths);
}

/**
 * Get changed files up to a specific exchange index (inclusive)
 * @param exchanges - Array of chat exchanges
 * @param upToIndex - Index up to which to collect changed files (inclusive)
 * @returns Array of unique changed file paths
 */
export function getChangedFilesUpToIndex(
	exchanges: CleanChatExchangeData[],
	upToIndex: number
): string[] {
	const changedFilePaths = new Set<string>();

	for (let i = 0; i <= upToIndex && i < exchanges.length; i++) {
		const exchange = exchanges[i];
		if (exchange.changedFiles && Array.isArray(exchange.changedFiles)) {
			for (const file of exchange.changedFiles) {
				const filePath = file.path || file.oldPath;
				if (filePath) {
					changedFilePaths.add(filePath);
				}
			}
		}
	}

	return Array.from(changedFilePaths);
}
