import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import { Squares2x2 } from 'svelte-hero-icons';

/**
 * Create select options for entity dropdown
 */
export function createEntitySelectOptions(entities: UnifiedEntity[]) {
	return entities.map((entity) => ({
		value: entity.id,
		label: entity.title,
		description: entity.description || '',
		metadata: {
			provider: entity.providerId,
			type: entity.entityType,
			icon: Squares2x2,
			color: 'text-blue-600 dark:text-blue-400'
		}
	}));
}

/**
 * Navigation helpers
 */
export function createNavigationHelpers(projectId: string, goto: (url: string) => void) {
	return {
		goToEntity: (entityId: string) => {
			goto(`/dashboard/${entityId}`);
		},

		goToDashboard: () => {
			goto(`/dashboard`);
		}
	};
}

/**
 * Check if we should show the "Save as Trigger" button
 */
export function shouldShowSaveAsTrigger(
	selectedProvider: string,
	selectedEntityType: string
): boolean {
	return selectedProvider !== 'all' && selectedEntityType !== 'all';
}

/**
 * Check if we should show entity type filters
 */
export function shouldShowEntityTypeFilters(availableEntityTypes: string[]): boolean {
	return availableEntityTypes.length > 0;
}

/**
 * Check if we should show the "All" button for entity types
 */
export function shouldShowEntityTypeAllButton(availableEntityTypes: string[]): boolean {
	return availableEntityTypes.length > 1;
}

/**
 * Format provider name for display
 */
export function formatProviderName(provider: string): string {
	return provider.charAt(0).toUpperCase() + provider.slice(1);
}

/**
 * Get grid classes for responsive layout
 */
export function getGridClasses(hasDetailView: boolean): string {
	return hasDetailView
		? 'grid gap-4 grid-cols-1'
		: 'grid gap-4 grid-cols-1 sm:grid-cols-2 xl:grid-cols-3';
}

/**
 * Check if entity cache should be used
 */
export function shouldUseCachedEntities(
	filteredEntitiesCount: number,
	hasSession: boolean
): boolean {
	return hasSession && filteredEntitiesCount < 30;
}
