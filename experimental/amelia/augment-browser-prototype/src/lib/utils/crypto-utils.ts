import CryptoJS from 'crypto-js';

/**
 * Crypto utilities with fallback support for environments where crypto.subtle is not available
 * (e.g., mobile browsers, non-HTTPS contexts)
 */

/**
 * Check if crypto.subtle is available in the current environment
 */
function isCryptoSubtleAvailable(): boolean {
	return typeof crypto !== 'undefined' &&
		   crypto.subtle !== undefined &&
		   typeof crypto.subtle.digest === 'function';
}

/**
 * Generate SHA-256 hash with fallback support
 * Uses crypto.subtle when available, falls back to crypto-js for mobile/non-HTTPS environments
 */
export async function sha256(data: string | Uint8Array): Promise<ArrayBuffer> {
	if (isCryptoSubtleAvailable()) {
		// Use native Web Crypto API when available
		const encoder = new TextEncoder();
		const dataToHash = typeof data === 'string' ? encoder.encode(data) : data;
		return await crypto.subtle.digest('SHA-256', dataToHash);
	} else {
		// Fallback to crypto-js for mobile/non-HTTPS environments
		const dataString = typeof data === 'string' ? data : new TextDecoder().decode(data);
		const hash = CryptoJS.SHA256(dataString);

		// Convert crypto-js WordArray to ArrayBuffer
		const hashBytes = new Uint8Array(32);
		for (let i = 0; i < 8; i++) {
			const word = hash.words[i];
			hashBytes[i * 4] = (word >>> 24) & 0xff;
			hashBytes[i * 4 + 1] = (word >>> 16) & 0xff;
			hashBytes[i * 4 + 2] = (word >>> 8) & 0xff;
			hashBytes[i * 4 + 3] = word & 0xff;
		}

		return hashBytes.buffer;
	}
}

/**
 * Generate random values with fallback support
 * Uses crypto.getRandomValues when available, falls back to Math.random for compatibility
 */
export function getRandomValues(array: Uint8Array): Uint8Array {
	if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
		return crypto.getRandomValues(array);
	} else {
		// Fallback using Math.random (less secure but works everywhere)
		for (let i = 0; i < array.length; i++) {
			array[i] = Math.floor(Math.random() * 256);
		}
		return array;
	}
}

/**
 * Convert ArrayBuffer to base64url encoding (used for PKCE)
 */
export function arrayBufferToBase64Url(buffer: ArrayBuffer): string {
	const bytes = new Uint8Array(buffer);
	const base64 = btoa(String.fromCharCode(...bytes));
	return base64
		.replace(/\+/g, '-')
		.replace(/\//g, '_')
		.replace(/=/g, '');
}

/**
 * Generate a random string for OAuth state/verifier
 */
export function generateRandomString(length: number, charset?: string): string {
	const defaultCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
	const chars = charset || defaultCharset;
	const values = new Uint8Array(length);
	getRandomValues(values);
	return Array.from(values, (byte) => chars[byte % chars.length]).join('');
}

/**
 * Generate PKCE code challenge from verifier
 */
export async function generateCodeChallenge(verifier: string): Promise<string> {
	const hash = await sha256(verifier);
	return arrayBufferToBase64Url(hash);
}

/**
 * Generate PKCE code verifier
 */
export function generateCodeVerifier(): string {
	return generateRandomString(128);
}

/**
 * Generate OAuth state parameter
 */
export function generateState(): string {
	return generateRandomString(32);
}
