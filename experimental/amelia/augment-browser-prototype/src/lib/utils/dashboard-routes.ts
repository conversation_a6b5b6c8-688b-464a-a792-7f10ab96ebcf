import {
	ArrowRightOnRectangle,
	ChartBarSquare,
	CloudArrowUp,
	Cog6Tooth,
	GlobeAlt,
	InboxStack,
	UserCircle
} from 'svelte-hero-icons';

export interface RouteItem {
	name: string;
	label: string;
	href: string;
	icon: any; // Heroicon component
	isActive?: boolean;
}

/**
 * Generate standard dashboard routes for a project
 */
export function createDashboardRoutes(
	projectId: string,
	currentPage?: string,
	exploratoryMode: boolean = false,
	isMobile: boolean = false
): RouteItem[] {
	if (!projectId) return [];

	const routes: RouteItem[] = [];

	// Always show agents
	routes.push({
		name: 'agents',
		label: 'Agents',
		href: `/agents`,
		icon: CloudArrowUp,
		isActive: currentPage === 'agents'
	});

	// Only show inbox and explore in exploratory mode
	if (exploratoryMode) {
		routes.push(
			// {
			// 	name: 'agents',
			// 	label: 'Agents',
			// 	href: `/agents`,
			// 	icon: CloudArrowUp,
			// 	isActive: currentPage === 'agents'
			// },
			{
				name: 'inbox',
				label: 'Inbox',
				href: `/inbox`,
				icon: InboxStack,
				isActive: currentPage === 'inbox'
			},
			{
				name: 'explore',
				label: 'Explore',
				href: `/explore`,
				icon: GlobeAlt,
				isActive: currentPage === 'explore'
			}
		);
	}

	// On mobile and not in exploratory mode, add settings to main navigation
	if (isMobile && !exploratoryMode) {
		routes.push({
			name: 'settings',
			label: 'Settings',
			href: `/settings`,
			icon: Cog6Tooth,
			isActive: currentPage === 'settings'
		});
	}

	return routes;
}

/**
 * Generate standard extra routes (settings, etc.)
 */
export function createExtraRoutes(
	projectId: string,
	currentPage?: string,
	isMobile: boolean = false,
	exploratoryMode: boolean = false
): RouteItem[] {
	const routes: RouteItem[] = [];

	// Only include settings in extra routes if not already in main routes
	// (settings is in main routes when on mobile and not in exploratory mode)
	if (!(isMobile && !exploratoryMode)) {
		routes.push({
			name: 'settings',
			label: 'Settings',
			href: `/settings`,
			icon: Cog6Tooth,
			isActive: currentPage === 'settings'
		});
	}

	routes.push({
		name: 'logout',
		label: 'Logout',
		href: '/logout',
		icon: ArrowRightOnRectangle,
		isActive: currentPage === 'logout'
	});

	return routes;
}

/**
 * Generate admin routes for admin sections
 */
export function createAdminRoutes(currentPage?: string): RouteItem[] {
	return [
		{
			name: 'users',
			label: 'Users',
			href: '/admin/users',
			icon: UserCircle,
			isActive: currentPage === 'users'
		},
		{
			name: 'analytics',
			label: 'Analytics',
			href: '/admin/analytics',
			icon: ChartBarSquare,
			isActive: currentPage === 'analytics'
		},
		{
			name: 'settings',
			label: 'Settings',
			href: '/admin/settings',
			icon: Cog6Tooth,
			isActive: currentPage === 'settings'
		}
	];
}

/**
 * Create custom routes with validation
 */
export function createCustomRoutes(
	routes: Omit<RouteItem, 'isActive'>[],
	currentPage?: string
): RouteItem[] {
	return routes.map((route) => ({
		...route,
		isActive: currentPage === route.name
	}));
}
