import GitHubIssue from '$lib/icons/GitHubIssue.svelte';
import GitPullRequest from '$lib/icons/GitPullRequest.svelte';
import Linear from '$lib/icons/LinearIcon.svelte';
import type {
	GitHubIssue as GitHubIssueType,
	GitHubPullRequest,
	LinearIssue,
	Issue,
	CreateTaskReferenceInput,
	TaskReference
} from '$lib/types';
import { Link } from 'svelte-hero-icons';

// ============================================================================
// ENTITY CONFIGURATION SYSTEM
// ============================================================================

/**
 * Entity configuration interface
 */
export interface EntityConfig {
	id: string;
	name: string;
	icon: any;
	color: string;
	description?: string;
	urlPatterns?: RegExp[];
	stateMapping?: Record<string, { label: string; color: string }>;
}

/**
 * Centralized entity configurations
 * This serves as the source of truth for all entity types
 */
export const ENTITY_CONFIGS: Record<string, EntityConfig> = {
	GITHUB_ISSUE: {
		id: 'GITHUB_ISSUE',
		name: 'GitHub Issue',
		icon: GitHubIssue,
		color: 'blue',
		description: 'GitHub repository issue',
		urlPatterns: [/github\.com\/[^/]+\/[^/]+\/issues\/\d+/],
		stateMapping: {
			open: { label: 'Open', color: 'green' },
			closed: { label: 'Closed', color: 'red' }
		}
	},
	GITHUB_PR: {
		id: 'GITHUB_PR',
		name: 'GitHub PR',
		icon: GitPullRequest,
		color: 'purple',
		description: 'GitHub pull request',
		urlPatterns: [/github\.com\/[^\/]+\/[^\/]+\/pull\/\d+/],
		stateMapping: {
			open: { label: 'Open', color: 'green' },
			closed: { label: 'Closed', color: 'red' },
			merged: { label: 'Merged', color: 'purple' },
			draft: { label: 'Draft', color: 'gray' }
		}
	},
	LINEAR_ISSUE: {
		id: 'LINEAR_ISSUE',
		name: 'Linear Issue',
		icon: Linear,
		color: 'indigo',
		description: 'Linear workspace issue',
		urlPatterns: [/linear\.app\/[^/]+\/issue\/[^/]+/],
		stateMapping: {
			backlog: { label: 'Backlog', color: 'gray' },
			unstarted: { label: 'Unstarted', color: 'gray' },
			started: { label: 'Started', color: 'blue' },
			completed: { label: 'Completed', color: 'green' },
			canceled: { label: 'Canceled', color: 'red' }
		}
	},
	EXTERNAL_LINK: {
		id: 'EXTERNAL_LINK',
		name: 'External Link',
		icon: Link,
		color: 'gray',
		description: 'External reference or link'
	}
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get entity configuration by type
 */
export function getEntityConfig(type: string): EntityConfig | null {
	return ENTITY_CONFIGS[type] || null;
}

/**
 * Get all available entity types
 */
export function getAllEntityTypes(): string[] {
	return Object.keys(ENTITY_CONFIGS);
}

/**
 * Get entity type from URL
 */
export function getEntityTypeFromUrl(url: string): string | null {
	for (const [type, config] of Object.entries(ENTITY_CONFIGS)) {
		if (config.urlPatterns) {
			for (const pattern of config.urlPatterns) {
				if (pattern.test(url)) {
					return type;
				}
			}
		}
	}
	return null;
}

/**
 * Get entity display name
 */
export function getEntityDisplayName(type: string): string {
	const config = getEntityConfig(type);
	return config?.name || 'Reference';
}

/**
 * Get entity icon
 */
export function getEntityIcon(type: string): string {
	const config = getEntityConfig(type);
	return config?.icon || 'link';
}

/**
 * Get entity color
 */
export function getEntityColor(type: string): string {
	const config = getEntityConfig(type);
	return config?.color || 'gray';
}

/**
 * Get state configuration for an entity
 */
export function getEntityStateConfig(
	type: string,
	state: string
): { label: string; color: string } | null {
	const config = getEntityConfig(type);
	if (!config?.stateMapping) return null;
	return config.stateMapping[state.toLowerCase()] || null;
}

/**
 * Create a task reference from a GitHub issue
 */
export function createReferenceFromGitHubIssue(issue: GitHubIssueType): CreateTaskReferenceInput {
	return {
		type: 'GITHUB_ISSUE',
		sourceId: issue.number.toString(),
		title: issue.title,
		description: issue.body || undefined,
		url: issue.url,
		state: issue.state,
		metadata: {
			labels: issue.labels,
			assignees: issue.assignees,
			author: issue.author,
			createdAt: issue.createdAt,
			updatedAt: issue.updatedAt
		}
	};
}

/**
 * Create a task reference from a GitHub pull request
 */
export function createReferenceFromGitHubPR(pr: GitHubPullRequest): CreateTaskReferenceInput {
	return {
		type: 'GITHUB_PR',
		sourceId: pr.number.toString(),
		title: pr.title,
		description: pr.body || undefined,
		url: pr.url,
		state: pr.state,
		metadata: {
			draft: pr.draft,
			head: pr.head.ref,
			base: pr.base.ref,
			author: pr.author,
			createdAt: pr.createdAt,
			updatedAt: pr.updatedAt,
			mergedAt: pr.mergedAt
		}
	};
}

/**
 * Create a task reference from a Linear issue
 */
export function createReferenceFromLinearIssue(issue: LinearIssue): CreateTaskReferenceInput {
	return {
		type: 'LINEAR_ISSUE',
		sourceId: issue.identifier,
		title: issue.title,
		description: issue.description || undefined,
		url: issue.url,
		state: issue.state.name,
		metadata: {
			priority: issue.priority,
			labels: issue.labels,
			assignee: issue.assignee,
			creator: issue.creator,
			state: issue.state,
			createdAt: issue.createdAt,
			updatedAt: issue.updatedAt
		}
	};
}

/**
 * Create a task reference from a generic issue
 */
export function createReferenceFromIssue(issue: Issue): CreateTaskReferenceInput {
	return {
		type:
			issue.source === 'github'
				? 'GITHUB_ISSUE'
				: issue.source === 'linear'
					? 'LINEAR_ISSUE'
					: 'URL',
		sourceId: issue.sourceId,
		title: issue.title,
		description: issue.description || undefined,
		url: issue.url,
		state: issue.state,
		metadata: {
			source: issue.source,
			assignee: issue.assignee,
			labels: issue.labels,
			createdAt: issue.createdAt,
			updatedAt: issue.updatedAt
		}
	};
}

/**
 * Parse metadata from a task reference
 */
export function parseReferenceMetadata(reference: TaskReference): any {
	if (!reference.metadata) return null;

	try {
		return JSON.parse(reference.metadata);
	} catch (error) {
		console.warn('Failed to parse reference metadata:', error);
		return null;
	}
}

/**
 * Get a display-friendly reference type name
 * @deprecated Use getEntityDisplayName instead
 */
export function getReferenceTypeName(type: string): string {
	return getEntityDisplayName(type);
}

/**
 * Get an icon for a reference type
 * @deprecated Use getEntityIcon instead
 */
export function getReferenceTypeIcon(type: string): string {
	return getEntityIcon(type);
}

/**
 * Format a reference for display in task description
 */
export function formatReferenceForDescription(reference: CreateTaskReferenceInput): string {
	const typeName = getEntityDisplayName(reference.type);
	const link = reference.url ? `[${reference.title}](${reference.url})` : reference.title;
	return `**${typeName}**: ${link}`;
}
