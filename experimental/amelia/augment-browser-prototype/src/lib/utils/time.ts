export function getRelativeTimeForStr(dateStr: string): string {
	return getRelativeTime(new Date(dateStr));
}

export function getRelativeTime(date: Date, now = new Date()): string {
	if (!date) {
		return 'Unknown time';
	}
	try {
		if (isNaN(date.getTime?.())) {
			return 'Unknown time';
		}

		const diffMs = now.getTime() - date.getTime();
		const diffSecs = Math.floor(diffMs / 1000);
		const diffMins = Math.floor(diffSecs / 60);
		const diffHours = Math.floor(diffMins / 60);
		const diffDays = Math.floor(diffHours / 24);

		if (diffSecs < 60) {
			return `${diffSecs}s ago`;
		} else if (diffMins < 60) {
			return `${diffMins}m ago`;
		} else if (diffHours < 24) {
			return `${diffHours}h ago`;
		} else if (diffDays < 30) {
			return `${diffDays}d ago`;
		} else {
			// Format the date for older entries
			return date.toLocaleDateString();
		}
	} catch (e) {
		console.error('Error formatting date:', e);
		return 'Unknown time';
	}
}

/**
 * Starts a relative time updater that will call the setRelativeTime function
 * with the relative time string.  The interval will adjust based on the age
 * of the date.
 *
 * @param dateStr The date string to update
 * @param setRelativeTime The function to call with the relative time string
 * @returns A function to stop the updater
 *
 * Example:
 * let relativeTime = getRelativeTimeForStr(timestamp);
 * const stopUpdater = startRelativeTimeUpdater(timestamp, (time) => {
 *   relativeTime = time;
 * });
 *
 * onDestroy(() => {
 *   stopUpdater();
 * });
 */
export function startRelativeTimeUpdater(
	dateStr: string,
	setRelativeTime: (relativeTime: string) => void
): () => void {
	let intervalMs = 1000;
	const date = new Date(dateStr);
	const interval = setInterval(() => {
		// if dateStr is more than 1m ago, update every minute
		const diffMins = Math.floor((new Date().getTime() - date.getTime()) / 1000 / 60);
		if (diffMins >= 1) {
			intervalMs = 60 * 1000;
		}
		// if dateStr is more than 1h ago, update every hour
		if (diffMins >= 60) {
			intervalMs = 60 * 60 * 1000;
		}
		// if dateStr is more than 1d ago, update every day
		if (diffMins >= 60 * 24) {
			intervalMs = 24 * 60 * 60 * 1000;
		}
		setRelativeTime(getRelativeTimeForStr(dateStr));
	}, intervalMs);

	return () => clearInterval(interval);
}

/**
 * Starts a countdown timer that will call the setCountdown function
 * with the countdown string.  The interval will adjust based on the time
 * remaining. Usage is similar to startRelativeTimeUpdater.
 *
 * @param retryAt The date to count down to
 * @param setCountdown The function to call with the countdown string
 * @returns A function to stop the updater
 */
export function startCountdown(
	retryAt: Date,
	setCountdown: (countdown: string) => void
): () => void {
	const intervalMs = 1000;
	const interval = setInterval(() => {
		const diffMs = retryAt.getTime() - Date.now();
		if (diffMs <= 0) {
			clearInterval(interval);
			return;
		}
		const diffSecs = Math.floor(diffMs / 1000);
		const diffMins = Math.floor(diffSecs / 60);
		const diffHours = Math.floor(diffMins / 60);
		const diffDays = Math.floor(diffHours / 24);

		if (diffSecs < 60) {
			setCountdown(`${diffSecs}s`);
		} else if (diffMins < 60) {
			setCountdown(`${diffMins}m ${diffSecs % 60}s`);
		} else if (diffHours < 24) {
			setCountdown(`${diffHours}h`);
		} else if (diffDays < 30) {
			setCountdown(`${diffDays}d`);
		} else {
			setCountdown(`1mo`);
		}
	}, intervalMs);

	return () => clearInterval(interval);
}

// Re-export reactive time utilities for convenience
export {
	createReactiveRelativeTime,
	reactiveRelativeTime,
	useReactiveRelativeTime
} from './reactive-time.svelte.js';
