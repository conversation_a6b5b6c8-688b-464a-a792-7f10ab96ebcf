/**
 * Tool configuration system for enhanced tool call display
 */

import { GitHub } from '$lib/icons/GitHubIcon.svelte';
import GleanIcon from '$lib/icons/GleanIcon.svelte';
import JiraIcon from '$lib/icons/JiraIcon.svelte';
import { Linear } from '$lib/icons/LinearIcon.svelte';
import NotionIcon from '$lib/icons/NotionIcon.svelte';
import {
	ChartBar,
	CodeBracketSquare,
	Cog6Tooth,
	CommandLine,
	FolderArrowDown,
	FolderMinus,
	GlobeAlt,
	MagnifyingGlass,
	Pencil
} from 'svelte-hero-icons';
import type { IconSource } from 'svelte-hero-icons';

export interface ToolConfig {
	displayName: string;
	icon: IconSource;
	description: string;
	collapsedInfo: (input: any, output?: any) => string;
	outputFormat: 'text' | 'code' | 'json' | 'markdown' | 'diff' | 'terminal' | 'search_results';
	statusColor: (hasOutput: boolean, hasError: boolean, isPartial: boolean) => string;
	category: 'file' | 'search' | 'api' | 'process' | 'edit' | 'view' | 'other';
}

export const TOOL_CONFIGS: Record<string, ToolConfig> = {
	// File Operations
	view: {
		displayName: 'View File',
		icon: MagnifyingGlass,
		description: 'View file or directory contents',
		collapsedInfo: (input) => {
			const path = input?.path || 'unknown';
			// const type = input?.type || 'file';
			return `Viewed \`${path}\``;
		},
		outputFormat: 'code',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'view'
	},

	'str-replace-editor': {
		displayName: 'Edit File',
		icon: Pencil,
		description: 'Edit file with string replacement',
		collapsedInfo: (input) => {
			const path = input?.path || 'unknown';
			// const command = input?.command || 'edit';
			const str_replace_entries = input?.str_replace_entries || [];
			const insert_line_entries = input?.insert_line_entries || [];
			const prefixes = {
				str_replace: `Replaced \`${str_replace_entries
					.map(
						(e: { old_str: string; new_str: string }) =>
							`${encodeMarkdownSensitiveChars(truncate(e.old_str, 60))} with ${encodeMarkdownSensitiveChars(
								truncate(e.new_str, 20)
							)}`
					)
					.join(', ')}\` in`,
				insert: `Added \`${encodeMarkdownSensitiveChars(
					truncate(
						insert_line_entries
							.map((e: { new_str: string }) => e.new_str)
							.join(', ')
							.trim(),
						20
					)
				)}\` to`,
				delete: `Deleted ${encodeMarkdownSensitiveChars(truncate(input?.old_str, 60))} from`,
				move: `Moved ${encodeMarkdownSensitiveChars(truncate(input?.old_str, 60))} to`
			};
			const prefix = prefixes[(input?.command || 'edit') as keyof typeof prefixes];
			return `${prefix} \`${path}\``;
		},
		outputFormat: 'diff',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-orange-500',
		category: 'edit'
	},

	'save-file': {
		displayName: 'Save File',
		icon: FolderArrowDown,
		description: 'Create or save file to disk',
		collapsedInfo: (input) => {
			const path = input?.path || 'unknown';
			return `Saved \`${path}\``;
		},
		outputFormat: 'text',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'file'
	},

	'remove-files': {
		displayName: 'Delete Files',
		icon: FolderMinus,
		description: 'Remove files from filesystem',
		collapsedInfo: (input) => {
			const paths = input?.file_paths || [];
			const count = Array.isArray(paths) ? paths.length : 1;
			return `Deleted ${count} file${count !== 1 ? 's' : ''} (\`${paths.join(', ')}\`)`;
		},
		outputFormat: 'text',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-orange-600' : 'text-yellow-600',
		category: 'file'
	},

	// Search & Retrieval
	'web-search': {
		displayName: 'Web Search',
		icon: GlobeAlt,
		description: 'Search the internet',
		collapsedInfo: (input, output) => {
			const query = input?.query || 'unknown';
			const encodedQuery = encodeMarkdownSensitiveChars(query);
			const resultCount = output?.results?.length || 0;
			return `Searched for "**${encodedQuery}**" and found ${resultCount} results`;
		},
		outputFormat: 'search_results',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'search'
	},

	'codebase-retrieval': {
		displayName: 'Code Search',
		icon: CodeBracketSquare,
		description: 'Search codebase for relevant code',
		collapsedInfo: (input) => {
			const query = input?.information_request || input?.query || 'unknown';
			const encodedQuery = encodeMarkdownSensitiveChars(query);
			return `Searched codebase for "${encodedQuery}"`;
		},
		outputFormat: 'search_results',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'search'
	},

	'grep-search': {
		displayName: 'Text Search',
		icon: CodeBracketSquare,
		description: 'Search for text patterns in files',
		collapsedInfo: (input) => {
			const query = input?.query || 'unknown';
			const encodedQuery = encodeMarkdownSensitiveChars(query);
			const directory = input?.directory_absolute_path || '';
			const dirName = directory.split('/').pop() || 'files';
			return `Searched for "**${encodedQuery}**" in ${dirName}`;
		},
		outputFormat: 'search_results',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'search'
	},

	// Process & Execution
	'launch-process': {
		displayName: 'Run Command',
		icon: CommandLine,
		description: 'Execute shell command',
		collapsedInfo: (input) => {
			const command = input?.command || 'unknown';
			const encodedCommand = encodeMarkdownSensitiveChars(command);
			return `Ran \`$ ${encodedCommand}\``;
		},
		outputFormat: 'terminal',
		statusColor: (hasOutput, hasError, isPartial) => {
			if (isPartial) return 'text-yellow-500';
			return hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500';
		},
		category: 'process'
	},

	// API & External Services
	'web-fetch': {
		displayName: 'HTTP Request',
		icon: GlobeAlt,
		description: 'Fetch data from web API',
		collapsedInfo: (input) => {
			const url = input?.url || 'unknown';
			const method = input?.method || 'GET';
			return `Fetched data from ${url} (${method})`;
		},
		outputFormat: 'json',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'api'
	},

	'github-api': {
		displayName: 'GitHub API',
		icon: GitHub,
		description: 'GitHub repository operations',
		collapsedInfo: (input) => {
			const path = input?.path || input?.endpoint || 'unknown';
			const method = input?.method || 'GET';
			return `Fetched data from GitHub: ${method} ${path}`;
		},
		outputFormat: 'json',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'api'
	},

	linear: {
		displayName: 'Linear',
		icon: Linear,
		description: 'Linear issue tracking',
		collapsedInfo: (input) => {
			const action = input?.action || input?.query || 'query';
			return `Fetched data from Linear: ${action}`;
		},
		outputFormat: 'json',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'api'
	},

	jira: {
		displayName: 'Jira',
		icon: JiraIcon,
		description: 'Jira project management',
		collapsedInfo: (input) => {
			const action = input?.action || input?.query || 'query';
			return `Fetched data from Jira: ${action}`;
		},
		outputFormat: 'json',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'api'
	},

	notion: {
		displayName: 'Notion',
		icon: NotionIcon,
		description: 'Notion workspace operations',
		collapsedInfo: (input) => {
			const action = input?.action || input?.query || 'query';
			return `Fetched data from Notion: ${action}`;
		},
		outputFormat: 'markdown',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'api'
	},

	glean: {
		displayName: 'Glean Search',
		icon: GleanIcon,
		description: 'Search company knowledge base',
		collapsedInfo: (input) => {
			const query = input?.query || 'unknown';
			const encodedQuery = encodeMarkdownSensitiveChars(query);
			return `Fetched data from Glean: "${encodedQuery}"`;
		},
		outputFormat: 'search_results',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'search'
	},

	// Visualization
	'render-mermaid': {
		displayName: 'Mermaid Diagram',
		icon: ChartBar,
		description: 'Render interactive diagram',
		collapsedInfo: (input) => {
			const title = input?.title || 'diagram';
			return `Made a diagram: ${title}`;
		},
		outputFormat: 'markdown',
		statusColor: (hasOutput, hasError) =>
			hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-blue-500',
		category: 'other'
	}
};

// Fallback configuration for unknown tools
export const DEFAULT_TOOL_CONFIG: ToolConfig = {
	displayName: 'Tool',
	icon: Cog6Tooth,
	description: 'Unknown tool',
	collapsedInfo: (input) => {
		if (typeof input === 'object' && input !== null) {
			const keys = Object.keys(input);
			if (keys.length > 0) {
				return `${keys.length} parameter${keys.length !== 1 ? 's' : ''}`;
			}
		}
		return 'Executed';
	},
	outputFormat: 'text',
	statusColor: (hasOutput, hasError) =>
		hasError ? 'text-red-500' : hasOutput ? 'text-green-500' : 'text-gray-500',
	category: 'other'
};

export function getToolConfig(toolName: string): ToolConfig {
	return TOOL_CONFIGS[toolName] || DEFAULT_TOOL_CONFIG;
}

export function getToolDisplayName(toolName: string): string {
	return getToolConfig(toolName).displayName;
}

export function getToolIcon(toolName: string): string {
	return getToolConfig(toolName).icon;
}

export function getToolCollapsedInfo(toolName: string, input: any, output?: any): string {
	return getToolConfig(toolName).collapsedInfo(input, output);
}

export function getToolStatusColor(
	toolName: string,
	hasOutput: boolean,
	hasError: boolean,
	isPartial: boolean = false
): string {
	return getToolConfig(toolName).statusColor(hasOutput, hasError, isPartial);
}

function truncate(text = '', maxLength: number = 100): string {
	if (text.length <= maxLength) return text;
	return text.slice(0, maxLength) + '...';
}

function encodeMarkdownSensitiveChars(text: string): string {
	return text
		.replace(/</g, '&lt;')
		.replace(/>/g, '&gt;')
		.replace(/&/g, '&amp;')
		.replace(/\*/g, '\\*')
		.replace(/_/g, '\\_')
		.replace(/\[/g, '\\[')
		.replace(/\]/g, '\\]')
		.replace(/\(/g, '\\(')
		.replace(/\)/g, '\\)')
		.replace(/#/g, '\\#')
		.replace(/\+/g, '\\+')
		.replace(/-/g, '\\-')
		.replace(/\./g, '\\.')
		.replace(/!/g, '\\!')
		.replace(/\|/g, '\\|')
		.replace(/`/g, '\\`')
		.replace(/~/g, '\\~')
		.replace(/\^/g, '\\^');
}
