import { describe, it, expect } from 'vitest';
import { extractPullRequestCreationInfo } from './remote-agent-utils';

describe('extractPullRequestCreationInfo', () => {
	it('should return null for empty exchanges', () => {
		expect(extractPullRequestCreationInfo([])).toBeNull();
		expect(extractPullRequestCreationInfo(null as any)).toBeNull();
		expect(extractPullRequestCreationInfo(undefined as any)).toBeNull();
	});

	it('should extract PR info from valid GitHub API tool use', () => {
		const mockExchanges = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/owner/repo/pulls',
									data: {
										title: 'Test PR',
										head: 'feature-branch',
										base: 'main'
									}
								})
							}
						}
					],
					requestNodes: [
						{
							type: 1,
							toolResultNode: {
								toolUseId: 'test-tool-use-id',
								content: `number: 123
url: https://api.github.com/repos/owner/repo/pulls/123
state: open
title: Test PR`
							}
						}
					]
				}
			}
		];

		const result = extractPullRequestCreationInfo(mockExchanges);

		expect(result).toEqual({
			prNumber: 123,
			prUrl: 'https://github.com/owner/repo/pull/123',
			repoUrl: 'https://github.com/owner/repo'
		});
	});

	it('should return null for non-creation GitHub API calls', () => {
		const mockExchanges = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'GET', // Not POST
									path: '/repos/owner/repo/pulls/123',
									data: {}
								})
							}
						}
					]
				}
			}
		];

		const result = extractPullRequestCreationInfo(mockExchanges);
		expect(result).toBeNull();
	});

	it('should return null for paths that are not pull creation endpoints', () => {
		const mockExchanges = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/owner/repo/pulls/123', // Has number, not creation
									data: {
										title: 'Test PR'
									}
								})
							}
						}
					]
				}
			}
		];

		const result = extractPullRequestCreationInfo(mockExchanges);
		expect(result).toBeNull();
	});

	it('should return null when data does not have title', () => {
		const mockExchanges = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/owner/repo/pulls',
									data: {} // No title
								})
							}
						}
					]
				}
			}
		];

		const result = extractPullRequestCreationInfo(mockExchanges);
		expect(result).toBeNull();
	});

	it('should return null when tool result is not found', () => {
		const mockExchanges = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'test-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/owner/repo/pulls',
									data: {
										title: 'Test PR'
									}
								})
							}
						}
					],
					requestNodes: [] // No matching tool result
				}
			}
		];

		const result = extractPullRequestCreationInfo(mockExchanges);
		expect(result).toBeNull();
	});

	it('should return the most recent PR creation when multiple exist', () => {
		const mockExchanges = [
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'old-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/owner/repo/pulls',
									data: { title: 'Old PR' }
								})
							}
						}
					],
					requestNodes: [
						{
							type: 1,
							toolResultNode: {
								toolUseId: 'old-tool-use-id',
								content: `number: 100
url: https://api.github.com/repos/owner/repo/pulls/100`
							}
						}
					]
				}
			},
			{
				exchange: {
					responseNodes: [
						{
							type: 5,
							toolUse: {
								toolUseId: 'new-tool-use-id',
								toolName: 'github-api',
								inputJson: JSON.stringify({
									method: 'POST',
									path: '/repos/owner/repo/pulls',
									data: { title: 'New PR' }
								})
							}
						}
					],
					requestNodes: [
						{
							type: 1,
							toolResultNode: {
								toolUseId: 'new-tool-use-id',
								content: `number: 200
url: https://api.github.com/repos/owner/repo/pulls/200`
							}
						}
					]
				}
			}
		];

		const result = extractPullRequestCreationInfo(mockExchanges);

		expect(result).toEqual({
			prNumber: 200,
			prUrl: 'https://github.com/owner/repo/pull/200',
			repoUrl: 'https://github.com/owner/repo'
		});
	});
});
