/**
 * GitHub URL parsing and manipulation utilities
 */

import type { GitHubRepo, GitHubBranch } from '$lib/api/github-api';
import { GitHubEntityType } from '$lib/types/trigger-enums';

export interface ParsedGitHubUrl {
	owner: string;
	name: string;
	branch?: string;
	path?: string;
}

export interface GitHubUrlParseResult {
	success: boolean;
	data?: ParsedGitHubUrl;
	error?: string;
}

/**
 * Parse a GitHub URL to extract owner, repo, branch, and path information
 * Supports various GitHub URL formats:
 * - https://github.com/owner/repo
 * - https://github.com/owner/repo.git
 * - https://github.com/owner/repo/tree/branch-name
 * - https://github.com/owner/repo/tree/branch-name/path/to/file
 * - **************:owner/repo.git
 * - owner/repo (shorthand)
 */
export function parseGitHubUrl(url: string): GitHubUrlParseResult {
	if (!url || typeof url !== 'string') {
		return {
			success: false,
			error: 'Invalid URL provided'
		};
	}

	const trimmedUrl = url.trim();

	try {
		// Handle shorthand format (owner/repo)
		const shorthandMatch = trimmedUrl.match(/^([a-zA-Z0-9_.-]+)\/([a-zA-Z0-9_.-]+)$/);
		if (shorthandMatch) {
			return {
				success: true,
				data: {
					owner: shorthandMatch[1],
					name: shorthandMatch[2]
				}
			};
		}

		// Handle SSH format (**************:owner/repo.git)
		const sshMatch = trimmedUrl.match(
			/^git@github\.com:([a-zA-Z0-9_.-]+)\/([a-zA-Z0-9_.-]+)(?:\.git)?$/
		);
		if (sshMatch) {
			return {
				success: true,
				data: {
					owner: sshMatch[1],
					name: sshMatch[2]
				}
			};
		}

		// Handle HTTPS URLs
		const urlObj = new URL(trimmedUrl);
		if (urlObj.hostname !== 'github.com') {
			return {
				success: false,
				error: 'URL is not a GitHub URL'
			};
		}

		const pathParts = urlObj.pathname.split('/').filter(Boolean);
		if (pathParts.length < 2) {
			return {
				success: false,
				error: 'Invalid GitHub URL format'
			};
		}

		const owner = pathParts[0];
		const repoName = pathParts[1].replace(/\.git$/, ''); // Remove .git suffix if present

		const result: ParsedGitHubUrl = {
			owner,
			name: repoName
		};

		// Check for branch information (tree/branch-name)
		if (pathParts.length >= 4 && pathParts[2] === 'tree') {
			result.branch = pathParts[3];

			// Check for path information after the branch
			if (pathParts.length > 4) {
				result.path = pathParts.slice(4).join('/');
			}
		}

		return {
			success: true,
			data: result
		};
	} catch (error) {
		return {
			success: false,
			error: 'Failed to parse URL'
		};
	}
}

/**
 * Create a GitHubRepo object from parsed URL data
 */
export function createRepoFromParsedUrl(parsed: ParsedGitHubUrl): GitHubRepo {
	return {
		owner: parsed.owner,
		name: parsed.name,
		description: '',
		private: false,
		fork: false,
		archived: false,
		disabled: false,
		default_branch: parsed.branch || 'main'
	};
}

/**
 * Create a GitHubBranch object from parsed URL data
 */
export function createBranchFromParsedUrl(parsed: ParsedGitHubUrl): GitHubBranch | null {
	if (!parsed.branch) return null;

	return {
		name: parsed.branch,
		commit: {
			sha: '',
			url: ''
		},
		protected: false
	};
}

/**
 * Format a GitHub URL from owner, repo, and optional branch
 */
export function formatGitHubUrl(owner: string, name: string, branch?: string): string {
	const baseUrl = `https://github.com/${owner}/${name}`;
	if (branch) {
		return `${baseUrl}/tree/${branch}`;
	}
	return baseUrl;
}

/**
 * Validate if a string looks like a GitHub URL or shorthand
 */
export function isGitHubUrl(url: string): boolean {
	if (!url || typeof url !== 'string') return false;

	const trimmed = url.trim();

	// Check shorthand format
	// if (/^[a-zA-Z0-9_.-]+\/[a-zA-Z0-9_.-]+$/.test(trimmed)) {
	// 	return true;
	// }

	// Check SSH format
	if (/^git@github\.com:[a-zA-Z0-9_.-]+\/[a-zA-Z0-9_.-]+(?:\.git)?$/.test(trimmed)) {
		return true;
	}

	// Check HTTPS format
	try {
		const urlObj = new URL(trimmed);
		return urlObj.hostname === 'github.com';
	} catch {
		return false;
	}
}

/**
 * Extract GitHub repository and branch information from a trigger
 */
export function extractGitHubInfoFromTrigger(
	trigger: any
): { url?: string; branch?: string; repo?: GitHubRepo; branchObj?: GitHubBranch } | null {
	if (!trigger?.configuration) return null;

	// Try different paths where GitHub info might be stored
	// First try new direct format
	const githubRef = trigger.configuration.agentConfig.workspaceSetup.startingFiles.githubRef;
	if (!githubRef) return null;

	const result: { url?: string; branch?: string; repo?: GitHubRepo; branchObj?: GitHubBranch } = {};

	// Extract URL
	if (githubRef.url) {
		result.url = githubRef.url;
	} else if (githubRef.repository) {
		// Handle repository field that might be a URL or owner/repo format
		if (githubRef.repository.startsWith('https://')) {
			result.url = githubRef.repository;
		} else {
			result.url = `https://github.com/${githubRef.repository}`;
		}
	}

	// Extract branch
	if (githubRef.branch) {
		result.branch = githubRef.branch;
	} else if (githubRef.ref) {
		result.branch = githubRef.ref;
	}

	// If we have a URL, try to parse it for more complete information
	if (result.url) {
		const parseResult = parseGitHubUrl(result.url);
		if (parseResult.success && parseResult.data) {
			result.repo = createRepoFromParsedUrl(parseResult.data);

			// Use branch from URL if not already set
			if (!result.branch && parseResult.data.branch) {
				result.branch = parseResult.data.branch;
			}

			// Create branch object if we have branch info
			if (result.branch) {
				result.branchObj = {
					name: result.branch,
					commit: { sha: '', url: '' },
					protected: false
				};
			}
		}
	}

	return Object.keys(result).length > 0 ? result : null;
}

/**
 * Extract GitHub repository information from a unified entity
 */
export function extractGitHubInfoFromEntity(
	entity: any
): { url?: string; branch?: string; repo?: GitHubRepo; branchObj?: GitHubBranch } | null {
	if (!entity) return null;

	console.log('extractGitHubInfoFromEntity called with entity:', entity);
	const result: { url?: string; branch?: string; repo?: GitHubRepo; branchObj?: GitHubBranch } = {};

	// For GitHub entities, try to extract repository information
	if (entity.providerId === 'github') {
		// Check if repository info is in metadata
		if (entity.metadata?.repository) {
			// Handle different formats of repository data
			if (typeof entity.metadata.repository === 'string') {
				// If it's already a full URL
				if (entity.metadata.repository.startsWith('https://github.com/')) {
					result.url = entity.metadata.repository;
				} else {
					// If it's just the repo name (owner/repo format)
					result.url = `https://github.com/${entity.metadata.repository}`;
				}
			} else if (entity.metadata.repository.html_url) {
				result.url = entity.metadata.repository.html_url;
			}
		}

		// Try to extract from entity URL if available
		if (!result.url && entity.url) {
			const parseResult = parseGitHubUrl(entity.url);
			if (parseResult.success && parseResult.data) {
				result.url = formatGitHubUrl(parseResult.data.owner, parseResult.data.name);
			}
		}

		// Extract branch information based on entity type
		if (entity.type === GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST) {
			// For pull requests, use head.ref (the source branch)
			if (entity.metadata?.head?.ref) {
				result.branch = entity.metadata.head.ref;
			} else if (entity.metadata?.head_ref) {
				result.branch = entity.metadata.head_ref;
			}
		} else if (entity.type === GitHubEntityType.GITHUB_ENTITY_TYPE_WORKFLOW_RUN) {
			// For workflow runs, use head_branch
			if (entity.metadata?.head_branch) {
				result.branch = entity.metadata.head_branch;
			}
		}
	}

	// If we have a URL, create a repo object
	if (result.url) {
		const parseResult = parseGitHubUrl(result.url);
		if (parseResult.success && parseResult.data) {
			result.repo = createRepoFromParsedUrl(parseResult.data);
		}
	}

	// If we have branch info, create a branch object
	if (result.branch) {
		result.branchObj = {
			name: result.branch,
			commit: {
				sha: '',
				url: ''
			},
			protected: false
		};
	}

	return Object.keys(result).length > 0 ? result : null;
}

export function getRepoFromUrl(url: string): { owner: string; name: string } | null {
	const match = url.match(/github\.com\/([^/]+)\/([^/]+)/);
	if (match) {
		return {
			owner: match[1],
			name: match[2]
		};
	}
	return null;
}
