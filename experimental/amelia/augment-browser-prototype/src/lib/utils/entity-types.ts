import { DocumentText } from 'svelte-hero-icons';
import GitPullRequest from '$lib/icons/GitPullRequest.svelte';
import GitWorkflow from '$lib/icons/GitWorkflow.svelte';

export interface EntityTypeConfig {
	id: string;
	apiId: string;
	displayName: string;
	icon: any; // Svelte component or heroicon
	description?: string;
}

export interface ProviderConfig {
	id: string;
	apiId: string;
	displayName: string;
	entityTypes: EntityTypeConfig[];
}

/**
 * Entity type configurations by provider
 */
export const ENTITY_TYPE_CONFIGS: Record<string, ProviderConfig> = {
	github: {
		id: 'github',
		apiId: 'github',
		displayName: 'GitHub',
		entityTypes: [
			{
				id: 'pull_request',
				apiId: 'GITHUB_ENTITY_TYPE_PULL_REQUEST',
				displayName: 'Pull Request',
				icon: GitPullRequest,
				description: 'GitHub pull requests for code review and merging'
			},
			{
				id: 'workflow_run',
				apiId: 'GITHUB_ENTITY_TYPE_WORKFLOW_RUN',
				displayName: 'Workflow Run',
				icon: GitWorkflow,
				description: 'GitHub Actions workflow executions'
			}
		]
	},
	linear: {
		id: 'linear',
		apiId: 'linear',
		displayName: 'Linear',
		entityTypes: [
			{
				id: 'issue',
				apiId: 'LINEAR_ENTITY_TYPE_ISSUE',
				displayName: 'Issue',
				icon: DocumentText,
				description: 'Linear issues and tasks'
			}
		]
	}
};

/**
 * Get entity type configuration by provider and type
 */
export function getEntityTypeConfig(
	providerId: string,
	entityType: string
): EntityTypeConfig | null {
	const provider = ENTITY_TYPE_CONFIGS[providerId];
	if (!provider) return null;

	return (
		provider.entityTypes.find((type) => type.id === entityType || type.apiId === entityType) || null
	);
}

export function getProviderDisplayName(providerId: string): string {
	const provider = ENTITY_TYPE_CONFIGS[providerId];
	return provider ? provider.displayName : providerId;
}

/**
 * Get display name for entity type
 */
export function getEntityTypeDisplayName(entityType = '', providerId?: string): string {
	if (providerId) {
		const config = getEntityTypeConfig(providerId, entityType);
		if (config) return config.displayName;
	}

	// Fallback for legacy entity types or when provider is not specified
	switch (entityType) {
		case 'issues':
		case 'issue':
			return 'Issue';
		case 'pull_requests':
		case 'pull_request':
			return 'Pull Request';
		case 'workflow_run':
			return 'Workflow Run';
		case 'tickets':
			return 'Ticket';
		case 'incidents':
			return 'Incident';
		case 'tasks':
			return 'Task';
		default:
			// Convert snake_case to Title Case
			return entityType.replace?.(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
	}
}

/**
 * Get icon component for entity type
 */
export function getEntityTypeIcon(providerId: string, entityType: string): typeof Document {
	const config = getEntityTypeConfig(providerId, entityType);
	return config?.icon || Document; // Default to Document icon
}

/**
 * Get all available entity types for a provider
 */
export function getProviderEntityTypes(providerId: string): string[] {
	const provider = ENTITY_TYPE_CONFIGS[providerId];
	return provider ? provider.entityTypes.map((type) => type.id) : [];
}

/**
 * Get all available providers
 */
export function getAvailableProviders(): string[] {
	return Object.keys(ENTITY_TYPE_CONFIGS);
}

/**
 * Legacy compatibility - get provider entity types in old format
 */
export const providerEntityTypes = Object.fromEntries(
	Object.entries(ENTITY_TYPE_CONFIGS).map(([providerId, config]) => [
		providerId,
		config.entityTypes.map((type) => type.id)
	])
);
