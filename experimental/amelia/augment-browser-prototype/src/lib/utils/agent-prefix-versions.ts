/**
 * Agent Prefix Versioning System
 *
 * This module provides a versioned system for agent prompt prefixes that maintains
 * backwards compatibility while allowing developers to create new versions.
 */

/**
 * Instructions for summary formatting in agent responses
 */
export const SUMMARY_INSTRUCTIONS = `Please work on the following task. At the end of your response, wrap a brief 2-sentence summary of what you accomplished in <summary> tags like this:

<summary>
Your 2-sentence summary here.
</summary>

Additionally, add a general summary of all your work or assessment in an ending <general_summary> tag like this:

<general_summary>
Your general summary here.
</general_summary>

This will be shown to the user as the main takeaway from your work. For example, if you're reviewing work, put your whole review in the <general_summary> tag. Or if you're implementing a feature, put a summary of the changes you made in the <general_summary> tag.
---

`;

/**
 * Guidelines for notebook note-taking during agent work
 */
export const NOTES_GUIDELINE = `IMPORTANT: As you work, document your findings in a structured notebook using XML tags. When you discover important information about the codebase, implementation details, user preferences, or technical constraints, record them using these XML note tags:

<note category="code_architecture">Brief finding about routes, file structure, patterns, frameworks</note>
<note category="implementation_details">Specific functions, classes, APIs, database schemas discovered</note>
<note category="user_preferences">Design requirements, UI/UX preferences, styling choices</note>
<note category="technical_constraints">Dependencies, limitations, compatibility requirements</note>
<note category="business_logic">Rules, workflows, data validation, user flows</note>
<note category="integration_points">External APIs, services, third-party tools</note>

These notes should capture things like:
- "Routes are defined in src/routes using SvelteKit file-based routing"
- "User prefers Vercel-style design with horizontal layout"
- "Database uses Prisma ORM with SQLite"
- "Components use radix-svelte with slate color variants"
- "Authentication uses OAuth with PKCE SHA256"

Use these note tags throughout your response whenever you discover something noteworthy. These will be extracted and shown in a separate notebook view.`;

export interface AgentPrefixVersion {
	/** Unique version identifier */
	id: string;
	/** Human-readable name for this version */
	name: string;
	/** Description of what this version does */
	description: string;
	/** The prefix content to prepend to messages */
	content: string;
	/** Separator pattern used to divide prefix from user message */
	separator: string;
	/** Regex pattern to detect this version in existing messages */
	detectionPattern: RegExp;
	/** Whether this version is deprecated */
	deprecated?: boolean;
	/** Creation timestamp */
	createdAt: string;
}

/**
 * Registry of all available prefix versions
 */
const PREFIX_VERSIONS = new Map<string, AgentPrefixVersion>();

/**
 * Version 1: Current system with structured responses and notebook notes
 * This maintains backwards compatibility with existing agents
 */
export const PREFIX_V1: AgentPrefixVersion = {
	id: 'v1',
	name: 'Structured Response with Notebook',
	description: 'Full structured response format with notebook note-taking capabilities',
	content: `${SUMMARY_INSTRUCTIONS}\n\n${NOTES_GUIDELINE}`,
	separator: '---',
	detectionPattern:
		/Additionally, add a general summary of all your work[\s\S]*?Use these note tags throughout your response/,
	createdAt: '2024-01-01T00:00:00Z'
};

/**
 * Version 2: Summary-only system without notebook instructions
 * This removes the notebook note-taking to simplify agent responses
 */
export const PREFIX_V2: AgentPrefixVersion = {
	id: 'v2',
	name: 'Summary Only',
	description: 'Simplified format with only summary instructions, no notebook note-taking',
	content: SUMMARY_INSTRUCTIONS,
	separator: '---',
	detectionPattern: /Additionally, add a general summary of all your work[\s\S]*?<general_summary>/,
	createdAt: '2024-07-18T00:00:00Z'
};

// Register built-in versions
PREFIX_VERSIONS.set(PREFIX_V1.id, PREFIX_V1);
PREFIX_VERSIONS.set(PREFIX_V2.id, PREFIX_V2);

/**
 * Default version to use for new agents
 */
export const DEFAULT_PREFIX_VERSION = PREFIX_V2.id;

/**
 * Register a new prefix version
 */
export function registerPrefixVersion(version: AgentPrefixVersion): void {
	if (PREFIX_VERSIONS.has(version.id)) {
		throw new Error(`Prefix version '${version.id}' already exists`);
	}
	PREFIX_VERSIONS.set(version.id, version);
}

/**
 * Get a prefix version by ID
 */
export function getPrefixVersion(versionId: string): AgentPrefixVersion | undefined {
	return PREFIX_VERSIONS.get(versionId);
}

/**
 * Get all available prefix versions
 */
export function getAllPrefixVersions(): AgentPrefixVersion[] {
	return Array.from(PREFIX_VERSIONS.values()).sort((a, b) => a.id.localeCompare(b.id));
}

/**
 * Get all non-deprecated prefix versions
 */
export function getActivePrefixVersions(): AgentPrefixVersion[] {
	return getAllPrefixVersions().filter((v) => !v.deprecated);
}

/**
 * Detect which prefix version was used in a message
 */
export function detectPrefixVersion(messageWithPrefix: string): AgentPrefixVersion | null {
	// Check each version's detection pattern
	for (const version of PREFIX_VERSIONS.values()) {
		if (version.detectionPattern.test(messageWithPrefix)) {
			return version;
		}
	}

	// If no pattern matches, assume it's a message without prefix
	return null;
}

/**
 * Wrap a message with a specific prefix version
 */
export function wrapMessageWithPrefix(
	message: string,
	versionId: string = DEFAULT_PREFIX_VERSION
): string {
	const version = getPrefixVersion(versionId);
	if (!version) {
		throw new Error(`Unknown prefix version: ${versionId}`);
	}

	return `${version.content}\n\n${version.separator}\n\n${message}`;
}

/**
 * Extract the original message from a prefixed message, handling any version
 */
export function extractOriginalMessage(messageWithPrefix: string): string {
	// First, try to detect the version
	const detectedVersion = detectPrefixVersion(messageWithPrefix);

	if (detectedVersion) {
		// Use version-specific extraction
		return extractMessageForVersion(messageWithPrefix, detectedVersion);
	}

	// Fallback: try common patterns for backwards compatibility
	return extractMessageFallback(messageWithPrefix);
}

/**
 * Extract message using version-specific logic
 */
function extractMessageForVersion(messageWithPrefix: string, version: AgentPrefixVersion): string {
	// Look for structured user request first (for user responses)
	const userRequestMatch = messageWithPrefix.match(/=user_request=\s*\n([\s\S]*?)(?:\n=|$)/);
	if (userRequestMatch) {
		return userRequestMatch[1].trim();
	}

	// Look for the version's separator
	const separatorRegex = new RegExp(version.separator);
	const match = messageWithPrefix.match(separatorRegex);
	const separatorIndex = match ? match.index : -1;

	if (separatorIndex === -1 || !match) {
		// No separator found, return original message
		return messageWithPrefix;
	}

	// Return everything after the separator
	return messageWithPrefix.substring(separatorIndex + match[0].length).trim();
}

/**
 * Fallback extraction for messages that don't match any known version
 */
function extractMessageFallback(messageWithPrefix: string): string {
	// Try common separators
	const separators = [/---/, /=user_request=/];

	for (const separator of separators) {
		const match = messageWithPrefix.match(separator);
		if (match && match.index !== undefined) {
			const result = messageWithPrefix.substring(match.index + match[0].length).trim();
			if (result) {
				return result;
			}
		}
	}

	// No separator found, return original
	return messageWithPrefix;
}

/**
 * Create a custom prefix version for developers
 */
export function createCustomPrefixVersion(
	id: string,
	name: string,
	description: string,
	content: string,
	options: {
		separator?: string;
		detectionPattern?: RegExp;
		deprecated?: boolean;
	} = {}
): AgentPrefixVersion {
	const version: AgentPrefixVersion = {
		id,
		name,
		description,
		content,
		separator: options.separator || '---',
		detectionPattern:
			options.detectionPattern ||
			new RegExp(content.slice(0, 50).replace(/[.*+?^${}()|[\]\\]/g, '\\$&')),
		deprecated: options.deprecated || false,
		createdAt: new Date().toISOString()
	};

	registerPrefixVersion(version);
	return version;
}
