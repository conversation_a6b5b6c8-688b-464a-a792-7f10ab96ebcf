/**
 * Creates a debounced version of a function that delays invoking the function
 * until after `delay` milliseconds have elapsed since the last time the
 * debounced function was invoked.
 *
 * @param func The function to debounce
 * @param delay The number of milliseconds to delay
 * @returns A debounced version of the function
 */
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	delay: number
): (...args: Parameters<T>) => void {
	let timeoutId: ReturnType<typeof setTimeout> | null = null;

	return (...args: Parameters<T>) => {
		// Clear the previous timeout if it exists
		if (timeoutId !== null) {
			clearTimeout(timeoutId);
		}

		// Set a new timeout
		timeoutId = setTimeout(() => {
			func(...args);
			timeoutId = null;
		}, delay);
	};
}

/**
 * Creates a throttled version of a function that only invokes the function
 * at most once per every `delay` milliseconds.
 *
 * @param func The function to throttle
 * @param delay The number of milliseconds to throttle
 * @returns A throttled version of the function
 */
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	delay: number
): (...args: Parameters<T>) => void {
	let lastCall = 0;
	let timeoutId: ReturnType<typeof setTimeout> | null = null;

	return (...args: Parameters<T>) => {
		const now = Date.now();

		if (now - lastCall >= delay) {
			// If enough time has passed, call immediately
			lastCall = now;
			func(...args);
		} else {
			// Otherwise, schedule a call for later
			if (timeoutId !== null) {
				clearTimeout(timeoutId);
			}

			timeoutId = setTimeout(() => {
				lastCall = Date.now();
				func(...args);
				timeoutId = null;
			}, delay - (now - lastCall));
		}
	};
}

/**
 * Creates a function that will only execute after being called `count` times.
 * Useful for waiting for multiple async operations to complete.
 *
 * @param func The function to execute
 * @param count The number of times the returned function must be called
 * @returns A function that executes the original after `count` calls
 */
export function after<T extends (...args: any[]) => any>(
	func: T,
	count: number
): (...args: Parameters<T>) => void {
	let callCount = 0;

	return (...args: Parameters<T>) => {
		callCount++;
		if (callCount >= count) {
			func(...args);
		}
	};
}

/**
 * Delays execution for the specified number of milliseconds.
 *
 * @param ms The number of milliseconds to delay
 * @returns A promise that resolves after the delay
 */
export function delay(ms: number): Promise<void> {
	return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Creates a function that can only be called once. Subsequent calls return
 * the result of the first call.
 *
 * @param func The function to wrap
 * @returns A function that can only be called once
 */
export function once<T extends (...args: any[]) => any>(
	func: T
): (...args: Parameters<T>) => ReturnType<T> {
	let called = false;
	let result: ReturnType<T>;

	return (...args: Parameters<T>): ReturnType<T> => {
		if (!called) {
			called = true;
			result = func(...args);
		}
		return result;
	};
}
