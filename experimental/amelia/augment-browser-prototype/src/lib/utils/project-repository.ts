import type { UnifiedEntity } from './entity-conversion';

/**
 * Get current project repository, returns fallback if not available
 * @returns {string} Repository in "owner/repo" format, or fallback
 */
export function getCurrentRepository(): string {
	// TODO: Implement actual repository detection from project context
	// For now, return a sensible fallback
	return 'augmentcode/augment';
}

/**
 * Convert repository name to GitHub URL
 */
export function repositoryToUrl(repository: string): string {
	if (repository.startsWith('https://')) {
		return repository;
	}
	return `https://github.com/${repository}`;
}

/**
 * Extract repository name from GitHub URL
 */
export function urlToRepository(url: string): string {
	if (!url.includes('github.com')) {
		return url;
	}
	const match = url.match(/github\.com[/:]([^/]+\/[^/.]+)/);
	return match ? match[1] : url;
}

/**
 * Extract repository URL from entity metadata
 * Handles different formats of repository data from GitHub entities
 */
export function getRepositoryUrlFromEntity(entity: UnifiedEntity): string | null {
	// For GitHub entities, try to extract repository information
	if (entity.providerId === 'github') {
		// Check if repository info is in metadata
		if (entity.metadata?.repository) {
			// Handle different formats of repository data
			if (typeof entity.metadata.repository === 'string') {
				// If it's already a full URL
				if (entity.metadata.repository.startsWith('https://github.com/')) {
					return entity.metadata.repository;
				}
				// If it's just the repo name (owner/repo format)
				return `https://github.com/${entity.metadata.repository}`;
			}
			// If it's an object with full_name property
			if (
				typeof entity.metadata.repository === 'object' &&
				entity.metadata.repository !== null &&
				'full_name' in entity.metadata.repository
			) {
				return `https://github.com/${(entity.metadata.repository as { full_name: string }).full_name}`;
			}
		}

		// Try to extract from URL if available
		if (entity.url) {
			const match = entity.url.match(/github\.com\/([^/]+\/[^/]+)/);
			if (match) {
				return `https://github.com/${match[1]}`;
			}
		}
	}

	return null;
}
