/**
 * Enhanced optimistic message handling with timeout and cleanup
 * Based on Remote Agents System documentation patterns
 *
 * Optimistic messages are removed when:
 * 1. Successful Delivery - Real exchange arrives via agent-history-stream
 * 2. Timeout - 60s for active agents, 180s for paused agents (except if AGENT_RUNNING)
 * 3. Network/API Errors - Immediate removal with error display
 * 4. Agent Deletion - Remove all pending messages for that agent
 * 5. User Cancellation - Explicit cleanup before sending interrupt
 * 6. Stream Connection Loss - Only remove on permanent failures
 */

import { writable, derived, get } from 'svelte/store';
import {
	RemoteAgentWorkspaceStatus,
	RemoteAgentStatus,
	type CleanRemoteAgent
} from '$lib/api/unified-client';

export interface OptimisticMessageConfig {
	id: string;
	agentId: string;
	content: string;
	type: 'user' | 'assistant';
	timestamp: number;
	timeoutMs: number;
	originalContent?: string; // For matching with real exchanges
	onTimeout?: () => void;
	onDelivered?: () => void;
	onError?: (error: string) => void;
}

export interface PendingMessage {
	config: OptimisticMessageConfig;
	timeoutId: NodeJS.Timeout;
	startTime: number;
	isDelivered: boolean;
	hasTimedOut: boolean;
	isCancelled: boolean;
	lastAgentStatus?: RemoteAgentStatus;
}

// Global pending messages tracking
const pendingMessages = new Map<string, PendingMessage>();

// Create a reactive store that updates when pendingMessages changes
export const pendingMessagesStore = writable(new Map<string, PendingMessage>());

// Helper function to update the store when pendingMessages changes
function updateStore() {
	pendingMessagesStore.set(new Map(pendingMessages));
}

// Create a derived store that tracks pending messages by agent ID
export const pendingMessagesByAgent = derived(pendingMessagesStore, ($pendingMessages) => {
	const byAgent = new Map<string, PendingMessage[]>();
	for (const pending of $pendingMessages.values()) {
		const agentId = pending.config.agentId;
		if (!byAgent.has(agentId)) {
			byAgent.set(agentId, []);
		}
		byAgent.get(agentId)!.push(pending);
	}
	return byAgent;
});

// Create derived stores for common queries
export const hasPendingMessagesStore = derived(
	pendingMessagesByAgent,
	($byAgent) => (agentId: string) => {
		return $byAgent.has(agentId) && $byAgent.get(agentId)!.length > 0;
	}
);

export const getPendingMessagesForAgentStore = derived(
	pendingMessagesByAgent,
	($byAgent) => (agentId: string) => {
		return $byAgent.get(agentId) || [];
	}
);

/**
 * Determine appropriate timeout based on agent workspace status
 * Paused agents need longer timeouts to account for resume time
 */
export function getTimeoutForAgent(agent: CleanRemoteAgent | null): number {
	if (!agent) {
		return 60000; // Default 1 minute
	}

	// Extended timeout for paused agents (includes resume time)
	if (agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED) {
		return 180000; // 3 minutes for paused agents
	}

	// Slightly longer timeout for resuming agents
	if (agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING) {
		return 120000; // 2 minutes for resuming agents
	}

	// Default timeout for running agents
	return 60000; // 1 minute
}

/**
 * Get appropriate error message based on agent state and timeout reason
 */
export function getTimeoutErrorMessage(agent: CleanRemoteAgent | null): string {
	if (!agent) {
		return 'Message timed out. Please try again.';
	}

	switch (agent.workspaceStatus) {
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED:
			return 'Failed to resume workspace. Please try again.';
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING:
			return 'Workspace is taking longer than expected to resume. Please try again.';
		default:
			return 'Message timed out. Please try again.';
	}
}

/**
 * Create an optimistic message with timeout handling
 */
export function createOptimisticMessage(
	agentId: string,
	content: string,
	type: 'user' | 'assistant' = 'user',
	timeoutMs: number = 60000, // 60 seconds default
	callbacks?: {
		originalContent?: string;
		onTimeout?: () => void;
		onDelivered?: () => void;
		onError?: (error: string) => void;
	}
): string {
	const messageId = `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

	const config: OptimisticMessageConfig = {
		id: messageId,
		agentId,
		content,
		type,
		timestamp: Date.now(),
		timeoutMs,
		originalContent: callbacks?.originalContent,
		onTimeout: callbacks?.onTimeout,
		onDelivered: callbacks?.onDelivered,
		onError: callbacks?.onError
	};

	// Set up timeout handler
	const timeoutId = setTimeout(() => {
		handleMessageTimeout(messageId);
	}, timeoutMs);

	const pendingMessage: PendingMessage = {
		config,
		timeoutId,
		startTime: Date.now(),
		isDelivered: false,
		hasTimedOut: false,
		isCancelled: false
	};

	pendingMessages.set(messageId, pendingMessage);
	updateStore(); // Trigger reactivity

	console.log(
		`[OptimisticMessage] Created optimistic message ${messageId} for agent ${agentId} with ${timeoutMs}ms timeout`
	);

	return messageId;
}

/**
 * Mark an optimistic message as delivered
 */
export function markMessageDelivered(messageId: string): void {
	const pendingMessage = pendingMessages.get(messageId);

	if (!pendingMessage) {
		console.warn(
			`[OptimisticMessage] Attempted to mark unknown message as delivered: ${messageId}`
		);
		return;
	}

	if (pendingMessage.hasTimedOut) {
		console.warn(
			`[OptimisticMessage] Attempted to mark timed-out message as delivered: ${messageId}`
		);
		return;
	}

	// Clear timeout
	clearTimeout(pendingMessage.timeoutId);

	// Mark as delivered
	pendingMessage.isDelivered = true;

	// Call delivery callback
	if (pendingMessage.config.onDelivered) {
		try {
			pendingMessage.config.onDelivered();
		} catch (error) {
			console.error(`[OptimisticMessage] Error in onDelivered callback for ${messageId}:`, error);
		}
	}

	// Clean up
	pendingMessages.delete(messageId);
	updateStore(); // Trigger reactivity

	const elapsedMs = Date.now() - pendingMessage.startTime;
	console.log(`[OptimisticMessage] Message ${messageId} delivered after ${elapsedMs}ms`);
}

/**
 * Mark an optimistic message as failed
 */
export function markMessageFailed(messageId: string, error: string): void {
	const pendingMessage = pendingMessages.get(messageId);

	if (!pendingMessage) {
		console.warn(`[OptimisticMessage] Attempted to mark unknown message as failed: ${messageId}`);
		return;
	}

	// Clear timeout
	clearTimeout(pendingMessage.timeoutId);

	// Call error callback
	if (pendingMessage.config.onError) {
		try {
			pendingMessage.config.onError(error);
		} catch (callbackError) {
			console.error(
				`[OptimisticMessage] Error in onError callback for ${messageId}:`,
				callbackError
			);
		}
	}

	// Clean up
	pendingMessages.delete(messageId);
	updateStore(); // Trigger reactivity

	const elapsedMs = Date.now() - pendingMessage.startTime;
	console.log(`[OptimisticMessage] Message ${messageId} failed after ${elapsedMs}ms: ${error}`);
}

/**
 * Handle message timeout with agent status checking
 * Don't timeout if agent status is AGENT_RUNNING
 */
function handleMessageTimeout(messageId: string): void {
	const pendingMessage = pendingMessages.get(messageId);

	if (!pendingMessage) {
		return; // Already cleaned up
	}

	if (pendingMessage.isDelivered || pendingMessage.isCancelled) {
		return; // Already delivered or cancelled
	}

	// Check if we should extend timeout based on agent status
	// Don't timeout if agent is currently running
	if (pendingMessage.lastAgentStatus === RemoteAgentStatus.AGENT_RUNNING) {
		console.log(
			`[OptimisticMessage] Extending timeout for message ${messageId} - agent is running`
		);

		// Extend timeout by another interval
		const extendedTimeoutMs = 60000; // 1 minute extension
		pendingMessage.timeoutId = setTimeout(() => {
			handleMessageTimeout(messageId);
		}, extendedTimeoutMs);

		return;
	}

	// Mark as timed out
	pendingMessage.hasTimedOut = true;

	// Call timeout callback
	if (pendingMessage.config.onTimeout) {
		try {
			pendingMessage.config.onTimeout();
		} catch (error) {
			console.error(`[OptimisticMessage] Error in onTimeout callback for ${messageId}:`, error);
		}
	}

	// Clean up
	pendingMessages.delete(messageId);
	updateStore(); // Trigger reactivity

	const elapsedMs = Date.now() - pendingMessage.startTime;
	console.warn(`[OptimisticMessage] Message ${messageId} timed out after ${elapsedMs}ms`);
}

/**
 * Get pending message info
 */
export function getPendingMessage(messageId: string): PendingMessage | null {
	return pendingMessages.get(messageId) || null;
}

/**
 * Get all pending messages for an agent
 */
export function getPendingMessagesForAgent(agentId: string): PendingMessage[] {
	return Array.from(pendingMessages.values()).filter(
		(pending) => pending.config.agentId === agentId
	);
}

/**
 * Check if an agent has any pending messages
 * Note: This function accesses the reactive store to trigger updates
 */
export function hasPendingMessages(agentId: string): boolean {
	// Use get to read current value from the reactive store
	const currentMessages = get(pendingMessagesStore);
	return Array.from(currentMessages.values()).some(
		(pending: PendingMessage) => pending.config.agentId === agentId
	);
}

/**
 * Get the oldest pending message start time for an agent (for elapsed time calculation)
 */
export function getOldestPendingMessageTime(agentId: string): number | null {
	// Use get to read current value from the reactive store
	const currentMessages = get(pendingMessagesStore);
	const agentMessages = Array.from(currentMessages.values()).filter(
		(pending: PendingMessage) => pending.config.agentId === agentId
	);

	if (agentMessages.length === 0) {
		return null;
	}

	return Math.min(...agentMessages.map((msg) => msg.startTime));
}

/**
 * Cancel all pending messages for an agent
 */
export function cancelPendingMessagesForAgent(agentId: string): void {
	const agentMessages = getPendingMessagesForAgent(agentId);

	agentMessages.forEach((pending) => {
		pending.isCancelled = true;
		clearTimeout(pending.timeoutId);
		pendingMessages.delete(pending.config.id);
	});

	// Update reactive store once after all deletions
	if (agentMessages.length > 0) {
		updateStore();
	}

	if (agentMessages.length > 0) {
		console.log(
			`[OptimisticMessage] Cancelled ${agentMessages.length} pending messages for agent ${agentId}`
		);
	}
}

/**
 * Update agent status for all pending messages of an agent
 * This helps with timeout decisions
 */
export function updateAgentStatusForPendingMessages(
	agentId: string,
	status: RemoteAgentStatus
): void {
	const agentMessages = getPendingMessagesForAgent(agentId);

	agentMessages.forEach((pending) => {
		pending.lastAgentStatus = status;
	});

	if (agentMessages.length > 0) {
		console.log(
			`[OptimisticMessage] Updated agent status to ${status} for ${agentMessages.length} pending messages`
		);
	}
}

/**
 * Try to match and deliver optimistic message by content and timestamp
 * Returns true if a match was found and delivered
 */
export function tryDeliverByContentMatch(
	agentId: string,
	content: string,
	timestamp: number,
	toleranceMs: number = 30000 // 30 second tolerance
): boolean {
	const agentMessages = getPendingMessagesForAgent(agentId);

	// Look for messages with matching content and similar timestamp
	for (const pending of agentMessages) {
		const contentMatches =
			pending.config.originalContent === content || pending.config.content === content;
		const timestampClose = Math.abs(pending.config.timestamp - timestamp) <= toleranceMs;

		if (contentMatches && timestampClose && !pending.isDelivered && !pending.hasTimedOut) {
			markMessageDelivered(pending.config.id);
			return true;
		}
	}

	return false;
}

/**
 * Handle stream connection loss
 * Only remove messages on permanent failures, not temporary disconnections
 */
export function handleStreamConnectionLoss(agentId: string, isPermanent: boolean): void {
	if (!isPermanent) {
		return;
	}

	cancelPendingMessagesForAgent(agentId);
}

/**
 * Handle user cancellation (e.g., interrupt request)
 */
export function handleUserCancellation(agentId: string): void {
	cancelPendingMessagesForAgent(agentId);
}

/**
 * Clean up all pending messages (useful for app shutdown)
 */
export function cleanupAllPendingMessages(): void {
	pendingMessages.forEach((pending) => {
		clearTimeout(pending.timeoutId);
	});

	const count = pendingMessages.size;
	pendingMessages.clear();
	updateStore(); // Trigger reactivity

	if (count > 0) {
		console.log(`[OptimisticMessage] Cleaned up ${count} pending messages`);
	}
}

/**
 * Get statistics about pending messages
 */
export function getPendingMessageStats(): {
	totalPending: number;
	byAgent: Record<string, number>;
	oldestPendingMs: number | null;
} {
	const byAgent: Record<string, number> = {};
	let oldestTime: number | null = null;

	pendingMessages.forEach((pending) => {
		const agentId = pending.config.agentId;
		byAgent[agentId] = (byAgent[agentId] || 0) + 1;

		if (oldestTime === null || pending.startTime < oldestTime) {
			oldestTime = pending.startTime;
		}
	});

	return {
		totalPending: pendingMessages.size,
		byAgent,
		oldestPendingMs: oldestTime ? Date.now() - oldestTime : null
	};
}
