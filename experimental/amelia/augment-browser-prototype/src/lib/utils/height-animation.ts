import { quintOut } from 'svelte/easing';

interface HeightAnimationOptions {
	duration?: number;
	easing?: (t: number) => number;
	delay?: number;
}

/**
 * Svelte action that animates height changes smoothly
 * Based on the slide transition pattern but as a reusable action
 *
 * Usage:
 * <div use:animateHeight={{ duration: 400, easing: quintOut }}>
 *   {#if expanded}
 *     <div>Content that will animate height</div>
 *   {/if}
 * </div>
 */
export function animateHeight(
	node: HTMLElement,
	options: HeightAnimationOptions = {}
) {
	const {
		duration = 400,
		easing = quintOut,
		delay = 0
	} = options;

	let previousHeight = node.offsetHeight;
	let animationId: number | null = null;

	// Set up ResizeObserver to watch for height changes
	const resizeObserver = new ResizeObserver((entries) => {
		for (const entry of entries) {
			const newHeight = entry.contentRect.height;

			// Only animate if height actually changed
			if (newHeight !== previousHeight && previousHeight !== 0) {
				animateToHeight(newHeight);
			}

			previousHeight = newHeight;
		}
	});

	function animateToHeight(targetHeight: number) {
		// Cancel any existing animation
		if (animationId) {
			cancelAnimationFrame(animationId);
		}

		const startHeight = node.offsetHeight;
		const heightDiff = targetHeight - startHeight;
		const startTime = performance.now() + delay;

		// Set initial height to prevent jumping
		node.style.height = `${startHeight}px`;
		node.style.overflow = 'hidden';

		function animate(currentTime: number) {
			const elapsed = currentTime - startTime;

			if (elapsed < 0) {
				animationId = requestAnimationFrame(animate);
				return;
			}

			const progress = Math.min(elapsed / duration, 1);
			const easedProgress = easing(progress);
			const currentHeight = startHeight + (heightDiff * easedProgress);

			node.style.height = `${currentHeight}px`;

			if (progress < 1) {
				animationId = requestAnimationFrame(animate);
			} else {
				// Animation complete - reset to auto height
				node.style.height = 'auto';
				node.style.overflow = '';
				animationId = null;
			}
		}

		animationId = requestAnimationFrame(animate);
	}

	// Start observing
	resizeObserver.observe(node);

	return {
		update(newOptions: HeightAnimationOptions) {
			Object.assign(options, newOptions);
		},
		destroy() {
			resizeObserver.disconnect();
			if (animationId) {
				cancelAnimationFrame(animationId);
			}
			// Reset styles
			node.style.height = '';
			node.style.overflow = '';
		}
	};
}

/**
 * Alternative action for manually triggering height animations
 * Useful when you want more control over when animations happen
 */
export function manualHeightAnimation(
	node: HTMLElement,
	options: HeightAnimationOptions = {}
) {
	const {
		duration = 400,
		easing = quintOut,
		delay = 0
	} = options;

	let animationId: number | null = null;

	function animateTo(targetHeight: number | 'auto') {
		// Cancel any existing animation
		if (animationId) {
			cancelAnimationFrame(animationId);
		}

		const startHeight = node.offsetHeight;

		// If target is 'auto', measure the natural height
		let finalHeight: number;
		if (targetHeight === 'auto') {
			const originalHeight = node.style.height;
			node.style.height = 'auto';
			finalHeight = node.offsetHeight;
			node.style.height = originalHeight;
		} else {
			finalHeight = targetHeight;
		}

		const heightDiff = finalHeight - startHeight;
		const startTime = performance.now() + delay;

		// Set initial height to prevent jumping
		node.style.height = `${startHeight}px`;
		node.style.overflow = 'hidden';

		function animate(currentTime: number) {
			const elapsed = currentTime - startTime;

			if (elapsed < 0) {
				animationId = requestAnimationFrame(animate);
				return;
			}

			const progress = Math.min(elapsed / duration, 1);
			const easedProgress = easing(progress);
			const currentHeight = startHeight + (heightDiff * easedProgress);

			node.style.height = `${currentHeight}px`;

			if (progress < 1) {
				animationId = requestAnimationFrame(animate);
			} else {
				// Animation complete
				if (targetHeight === 'auto') {
					node.style.height = 'auto';
					node.style.overflow = '';
				}
				animationId = null;
			}
		}

		animationId = requestAnimationFrame(animate);
	}

	return {
		update(newOptions: HeightAnimationOptions) {
			Object.assign(options, newOptions);
		},
		animateTo,
		destroy() {
			if (animationId) {
				cancelAnimationFrame(animationId);
			}
			// Reset styles
			node.style.height = '';
			node.style.overflow = '';
		}
	};
}
