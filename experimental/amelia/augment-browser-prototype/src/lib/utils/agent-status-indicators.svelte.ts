import {
	RemoteAgentStatus,
	RemoteAgentWorkspaceStatus,
	type CleanRemoteAgent
} from '$lib/api/unified-client';
import { Play, Pause, ArrowPath, EllipsisHorizontal, Trash } from 'svelte-hero-icons';

export interface AgentIndicatorConfig {
	showGeneratingResponse: boolean;
	showResumingAgent: boolean;
	showRunningSpacer: boolean;
	showElapsedTimer: boolean;
	elapsedTimeMs?: number;
	indicatorText: string;
	isActive: boolean;
}

export interface ToolUseState {
	phase: 'idle' | 'running' | 'complete';
	currentTool?: string;
	startTime?: number;
}

/**
 * Centralized logic for determining what agent status indicator to show
 * Based on the Remote Agents System documentation patterns
 */
export function getAgentIndicatorConfig(
	agent: CleanRemoteAgent | null,
	isStreaming: boolean = false,
	hasPendingMessage: boolean = false,
	toolState: ToolUseState = { phase: 'idle' },
	messageStartTime?: number
): AgentIndicatorConfig {
	const config: AgentIndicatorConfig = {
		showGeneratingResponse: false,
		showResumingAgent: false,
		showRunningSpacer: false,
		showElapsedTimer: false,
		indicatorText: '',
		isActive: false
	};

	// No agent or agent not active
	if (!agent) {
		return config;
	}

	// Calculate elapsed time if we have a start time
	const elapsedTimeMs = messageStartTime ? Date.now() - messageStartTime : 0;
	config.elapsedTimeMs = elapsedTimeMs;

	// Show elapsed timer after 5 seconds
	config.showElapsedTimer = elapsedTimeMs > 5000;

	// Determine if agent is in an active state
	const isAgentActive =
		agent.status === RemoteAgentStatus.AGENT_RUNNING ||
		agent.status === RemoteAgentStatus.AGENT_STARTING ||
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING ||
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED ||
		hasPendingMessage;

	config.isActive = isAgentActive;

	if (!isAgentActive) {
		return config;
	}

	// Priority 1: Workspace is resuming from paused state
	// During resume, the workspace isn't even running yet - agent cannot generate responses
	if (agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING) {
		config.showResumingAgent = true;
		config.indicatorText = 'Resuming remote agent...';
		return config;
	}

	// Priority 2: Agent is actively running tools
	if (toolState.phase === 'running') {
		config.showRunningSpacer = true;
		config.indicatorText = toolState.currentTool
			? `Running ${toolState.currentTool}...`
			: 'Running tools...';
		return config;
	}

	// Priority 3: Agent is generating response (only when workspace is running)
	if (
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING &&
		(agent.status === RemoteAgentStatus.AGENT_RUNNING ||
			agent.status === RemoteAgentStatus.AGENT_STARTING ||
			hasPendingMessage)
	) {
		config.showGeneratingResponse = true;
		config.indicatorText = 'Generating response...';
		return config;
	}

	// if workspace is paused, indicate that
	if (agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED) {
		config.indicatorText = 'Workspace is paused, will resume when you send a message';
		config.isActive = true;
		return config;
	}

	return config;
}

/**
 * Format elapsed time for display
 */
export function formatElapsedTime(elapsedMs: number): string {
	const seconds = Math.floor(elapsedMs / 1000);

	if (seconds < 60) {
		return `${seconds}s`;
	}

	const minutes = Math.floor(seconds / 60);
	const remainingSeconds = seconds % 60;

	if (minutes < 60) {
		return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
	}

	const hours = Math.floor(minutes / 60);
	const remainingMinutes = minutes % 60;
	return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

/**
 * Determine if an agent should show any activity indicator
 */
export function shouldShowActivityIndicator(
	agent: CleanRemoteAgent | null,
	isStreaming: boolean = false,
	hasPendingMessage: boolean = false
): boolean {
	if (!agent) return false;

	return (
		agent.status === RemoteAgentStatus.AGENT_RUNNING ||
		agent.status === RemoteAgentStatus.AGENT_STARTING ||
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING ||
		// isStreaming ||
		hasPendingMessage
	);
}

/**
 * Get a simple status text for an agent
 */
export function getAgentStatusText(agent: CleanRemoteAgent | null): string {
	if (!agent) return 'Unknown';

	switch (agent.status) {
		case RemoteAgentStatus.AGENT_IDLE:
			return 'Idle';
		case RemoteAgentStatus.AGENT_RUNNING:
			return 'Running';
		case RemoteAgentStatus.AGENT_STARTING:
			return 'Starting';
		case RemoteAgentStatus.AGENT_FAILED:
			return 'Failed';
		case RemoteAgentStatus.AGENT_PENDING:
			return 'Pending';
		default:
			return 'Unknown';
	}
}

/**
 * Get workspace status text
 */
export function getWorkspaceStatusText(agent: CleanRemoteAgent | null): string {
	if (!agent) return 'Unknown';

	switch (agent.workspaceStatus) {
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING:
			return 'Running';
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED:
			return 'Paused';
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING:
			return 'Resuming';
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSING:
			return 'Pausing';
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_DELETING:
			return 'Deleting';
		default:
			return 'Unknown';
	}
}

/**
 * Get workspace status icon
 */
export function getWorkspaceStatusIcon(workspaceStatus: RemoteAgentWorkspaceStatus) {
	switch (workspaceStatus) {
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING:
			return Play;
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED:
			return Pause;
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING:
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSING:
			return ArrowPath;
		case RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_DELETING:
			return Trash;
		default:
			return EllipsisHorizontal;
	}
}

/**
 * Check if agent is in a "busy" state where new messages shouldn't be sent
 */
export function isAgentBusy(agent: CleanRemoteAgent | null): boolean {
	if (!agent) return false;

	return (
		agent.status === RemoteAgentStatus.AGENT_RUNNING ||
		agent.status === RemoteAgentStatus.AGENT_STARTING ||
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING
	);
}
