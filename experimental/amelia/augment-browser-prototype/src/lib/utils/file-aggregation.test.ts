import { describe, it, expect } from 'vitest';
import { aggregateChangedFiles, getAggregatedFilePath, hasAggregatedCodeChanges } from './file-aggregation';
import type { CleanChangedFile } from '$lib/api/unified-client';

describe('file-aggregation', () => {
	describe('aggregateChangedFiles', () => {
		it('should return empty array for empty input', () => {
			expect(aggregateChangedFiles([])).toEqual([]);
			expect(aggregateChangedFiles([[]])).toEqual([]);
		});

		it('should handle single exchange with single file', () => {
			const exchange1: CleanChangedFile[] = [
				{
					path: 'src/test.ts',
					content: 'console.log("hello");',
					changeType: 'added'
				}
			];

			const result = aggregateChangedFiles([exchange1]);
			expect(result).toHaveLength(1);
			expect(result[0].path).toBe('src/test.ts');
			expect(result[0].changeType).toBe('added');
			expect(result[0].content).toBe('console.log("hello");');
		});

		it('should aggregate multiple modifications to the same file', () => {
			const exchange1: CleanChangedFile[] = [
				{
					path: 'src/test.ts',
					content: 'console.log("hello");',
					oldContent: '',
					changeType: 'added'
				}
			];

			const exchange2: CleanChangedFile[] = [
				{
					path: 'src/test.ts',
					content: 'console.log("hello world");',
					oldContent: 'console.log("hello");',
					changeType: 'modified'
				}
			];

			const result = aggregateChangedFiles([exchange1, exchange2]);
			expect(result).toHaveLength(1);
			expect(result[0].path).toBe('src/test.ts');
			expect(result[0].changeType).toBe('added'); // Overall it was added
			expect(result[0].content).toBe('console.log("hello world");'); // Final content
			expect(result[0].oldContent).toBe(''); // Original content
		});

		it('should handle file renames', () => {
			const exchange1: CleanChangedFile[] = [
				{
					path: 'src/new-name.ts',
					oldPath: 'src/old-name.ts',
					content: 'console.log("hello");',
					oldContent: 'console.log("hello");',
					changeType: 'renamed'
				}
			];

			const result = aggregateChangedFiles([exchange1]);
			expect(result).toHaveLength(1);
			expect(result[0].path).toBe('src/new-name.ts');
			expect(result[0].oldPath).toBe('src/old-name.ts');
			expect(result[0].changeType).toBe('renamed');
		});

		it('should handle file deletion', () => {
			const exchange1: CleanChangedFile[] = [
				{
					path: 'src/test.ts',
					content: 'console.log("hello");',
					changeType: 'added'
				}
			];

			const exchange2: CleanChangedFile[] = [
				{
					path: 'src/test.ts',
					oldContent: 'console.log("hello");',
					changeType: 'deleted'
				}
			];

			const result = aggregateChangedFiles([exchange1, exchange2]);
			expect(result).toHaveLength(1);
			expect(result[0].path).toBe('src/test.ts');
			expect(result[0].changeType).toBe('deleted'); // Overall it was deleted
		});

		it('should handle multiple files across exchanges', () => {
			const exchange1: CleanChangedFile[] = [
				{
					path: 'src/file1.ts',
					content: 'file1 content',
					changeType: 'added'
				},
				{
					path: 'src/file2.ts',
					content: 'file2 content',
					changeType: 'added'
				}
			];

			const exchange2: CleanChangedFile[] = [
				{
					path: 'src/file1.ts',
					content: 'file1 modified',
					oldContent: 'file1 content',
					changeType: 'modified'
				}
			];

			const result = aggregateChangedFiles([exchange1, exchange2]);
			expect(result).toHaveLength(2);

			const file1 = result.find(f => f.path === 'src/file1.ts');
			const file2 = result.find(f => f.path === 'src/file2.ts');

			expect(file1?.changeType).toBe('added');
			expect(file1?.content).toBe('file1 modified');

			expect(file2?.changeType).toBe('added');
			expect(file2?.content).toBe('file2 content');
		});
	});

	describe('getAggregatedFilePath', () => {
		it('should return path when available', () => {
			const file: CleanChangedFile = {
				path: 'src/test.ts',
				changeType: 'added'
			};
			expect(getAggregatedFilePath(file)).toBe('src/test.ts');
		});

		it('should fallback to oldPath when path is not available', () => {
			const file: CleanChangedFile = {
				path: '',
				oldPath: 'src/old.ts',
				changeType: 'deleted'
			};
			expect(getAggregatedFilePath(file)).toBe('src/old.ts');
		});
	});

	describe('hasAggregatedCodeChanges', () => {
		it('should return false for empty array', () => {
			expect(hasAggregatedCodeChanges([])).toBe(false);
		});

		it('should return true for meaningful code changes', () => {
			const files: CleanChangedFile[] = [
				{
					path: 'src/test.ts',
					content: 'console.log("hello");',
					changeType: 'added'
				}
			];
			expect(hasAggregatedCodeChanges(files)).toBe(true);
		});

		it('should return false for non-meaningful changes', () => {
			const files: CleanChangedFile[] = [
				{
					path: 'package-lock.json',
					content: '{}',
					changeType: 'modified'
				}
			];
			expect(hasAggregatedCodeChanges(files)).toBe(false);
		});
	});
});
