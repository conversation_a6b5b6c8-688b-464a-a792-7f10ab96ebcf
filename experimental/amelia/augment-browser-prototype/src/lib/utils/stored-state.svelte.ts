import { browser } from '$app/environment';

// like Svelte5's $state, but stored in localStorage or sessionStorage
export function storedState<T>(
	storage: 'localStorage' | 'sessionStorage',
	key: string,
	initialValue: T
) {
	let value = $state(initialValue);

	if (browser) {
		try {
			const stored =
				storage === 'localStorage' ? localStorage.getItem(key) : sessionStorage.getItem(key);
			if (stored) {
				value = JSON.parse(stored);
			}
		} catch (error) {
			console.warn('Failed to load stored state:', error);
		}
	}

	return {
		get() {
			return value;
		},
		set(v: T) {
			value = v;
			if (browser) {
				try {
					if (storage === 'localStorage') {
						localStorage.setItem(key, JSON.stringify(v));
					} else if (storage === 'sessionStorage') {
						sessionStorage.setItem(key, JSON.stringify(v));
					}
				} catch (error) {
					console.warn('Failed to store state:', error);
				}
			}
		}
	};
}
