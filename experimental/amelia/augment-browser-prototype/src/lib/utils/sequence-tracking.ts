/**
 * Sequence ID tracking utilities for reliable message delivery
 * Based on Remote Agents System documentation patterns
 */

export interface SequenceTracker {
	agentId: string;
	lastProcessedSequenceId: number;
	pendingSequenceIds: Set<number>;
	missedSequenceIds: Set<number>;
	lastUpdateTime: number;
}

// Global sequence tracking state
const sequenceTrackers = new Map<string, SequenceTracker>();

/**
 * Initialize sequence tracking for an agent
 */
export function initializeSequenceTracker(agentId: string, lastKnownSequenceId: number = 0): void {
	sequenceTrackers.set(agentId, {
		agentId,
		lastProcessedSequenceId: lastKnownSequenceId,
		pendingSequenceIds: new Set(),
		missedSequenceIds: new Set(),
		lastUpdateTime: Date.now()
	});
}

/**
 * Get the sequence tracker for an agent
 */
export function getSequenceTracker(agentId: string): SequenceTracker | null {
	return sequenceTrackers.get(agentId) || null;
}

/**
 * Process a new sequence ID and detect gaps
 */
export function processSequenceId(agentId: string, sequenceId: number): {
	isValid: boolean;
	isDuplicate: boolean;
	missedSequenceIds: number[];
	shouldRequestResync: boolean;
} {
	let tracker = sequenceTrackers.get(agentId);

	if (!tracker) {
		// Initialize tracker if it doesn't exist
		initializeSequenceTracker(agentId, sequenceId - 1);
		tracker = sequenceTrackers.get(agentId)!;
	}

	const result = {
		isValid: true,
		isDuplicate: false,
		missedSequenceIds: [] as number[],
		shouldRequestResync: false
	};

	// Check if this is a duplicate
	if (sequenceId <= tracker.lastProcessedSequenceId) {
		result.isDuplicate = true;
		return result;
	}

	// Check for gaps in sequence
	const expectedSequenceId = tracker.lastProcessedSequenceId + 1;

	if (sequenceId > expectedSequenceId) {
		// We have a gap - mark missing sequence IDs
		for (let i = expectedSequenceId; i < sequenceId; i++) {
			tracker.missedSequenceIds.add(i);
			result.missedSequenceIds.push(i);
		}

		// If we have too many missed sequences, request a resync
		if (tracker.missedSequenceIds.size > 10) {
			result.shouldRequestResync = true;
		}
	}
	// Update tracker
	tracker.lastProcessedSequenceId = sequenceId;
	tracker.lastUpdateTime = Date.now();

	// Remove from pending if it was there
	tracker.pendingSequenceIds.delete(sequenceId);

	return result;
}

/**
 * Mark a sequence ID as pending (sent but not yet confirmed)
 */
export function markSequenceAsPending(agentId: string, sequenceId: number): void {
	const tracker = sequenceTrackers.get(agentId);
	if (tracker) {
		tracker.pendingSequenceIds.add(sequenceId);
	}
}

/**
 * Get the last processed sequence ID for an agent (for reconnection)
 */
export function getLastProcessedSequenceId(agentId: string): number {
	const tracker = sequenceTrackers.get(agentId);
	return tracker?.lastProcessedSequenceId || 0;
}

/**
 * Check if there are any missed sequence IDs that need to be requested
 */
export function getMissedSequenceIds(agentId: string): number[] {
	const tracker = sequenceTrackers.get(agentId);
	return tracker ? Array.from(tracker.missedSequenceIds).sort((a, b) => a - b) : [];
}

/**
 * Clear missed sequence IDs (after successful resync)
 */
export function clearMissedSequenceIds(agentId: string): void {
	const tracker = sequenceTrackers.get(agentId);
	if (tracker) {
		tracker.missedSequenceIds.clear();
	}
}

/**
 * Check for stale pending sequence IDs (timeout after 60 seconds)
 */
export function getStaleSequenceIds(agentId: string, timeoutMs: number = 60000): number[] {
	const tracker = sequenceTrackers.get(agentId);
	if (!tracker) return [];

	const now = Date.now();
	const staleThreshold = now - timeoutMs;

	if (tracker.lastUpdateTime < staleThreshold) {
		return Array.from(tracker.pendingSequenceIds);
	}

	return [];
}

/**
 * Clean up sequence tracker for an agent
 */
export function cleanupSequenceTracker(agentId: string): void {
	sequenceTrackers.delete(agentId);
}

/**
 * Get sequence tracking statistics for debugging
 */
export function getSequenceStats(agentId: string): {
	lastProcessedSequenceId: number;
	pendingCount: number;
	missedCount: number;
	lastUpdateTime: number;
} | null {
	const tracker = sequenceTrackers.get(agentId);
	if (!tracker) return null;

	return {
		lastProcessedSequenceId: tracker.lastProcessedSequenceId,
		pendingCount: tracker.pendingSequenceIds.size,
		missedCount: tracker.missedSequenceIds.size,
		lastUpdateTime: tracker.lastUpdateTime
	};
}

/**
 * Reset sequence tracking for an agent (useful for reconnection)
 */
export function resetSequenceTracker(agentId: string, newSequenceId: number = 0): void {
	const tracker = sequenceTrackers.get(agentId);
	if (tracker) {
		tracker.lastProcessedSequenceId = newSequenceId;
		tracker.pendingSequenceIds.clear();
		tracker.missedSequenceIds.clear();
		tracker.lastUpdateTime = Date.now();
	} else {
		initializeSequenceTracker(agentId, newSequenceId);
	}
}
