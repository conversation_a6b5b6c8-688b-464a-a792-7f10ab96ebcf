import { SvelteDate } from 'svelte/reactivity';
import { getRelativeTime } from './time.js';

/**
 * Creates a reactive relative time that automatically updates based on the age of the date.
 * Uses SvelteDate to ensure reactivity and smart intervals for efficient updates.
 *
 * @param targetDate - The date to calculate relative time from (Date, string, or number)
 * @returns An object with the reactive relative time string and cleanup function
 *
 * @example
 * ```svelte
 * <script>
 *   import { createReactiveRelativeTime } from '$lib/utils/reactive-time.svelte.js';
 *
 *   const timestamp = '2024-01-15T10:30:00Z';
 *   const { relativeTime, cleanup } = createReactiveRelativeTime(timestamp);
 *
 *   onDestroy(cleanup);
 * </script>
 *
 * <p>Updated {relativeTime}</p>
 * ```
 */
export function createReactiveRelativeTime(targetDate: Date | string | number) {
	// Convert input to Date object
	const target = new Date(targetDate);

	if (isNaN(target.getTime())) {
		throw new Error('Invalid date provided to createReactiveRelativeTime');
	}

	// Create reactive current time using SvelteDate
	const currentTime = new SvelteDate();

	// Reactive relative time that updates when currentTime changes
	const relativeTime = $derived((currentTime.getTime(), getRelativeTime(target)));

	// Smart interval management based on age of the date
	let intervalId: ReturnType<typeof setInterval> | null = null;

	function getUpdateInterval(): number {
		const now = Date.now();
		const diffMs = now - target.getTime();
		const diffMins = Math.floor(diffMs / 1000 / 60);

		// Update every second for recent times (< 1 minute)
		if (diffMins < 1) return 1000;

		// Update every minute for times < 1 hour
		if (diffMins < 60) return 60 * 1000;

		// Update every hour for times < 1 day
		if (diffMins < 60 * 24) return 60 * 60 * 1000;

		// Update every day for older times
		return 24 * 60 * 60 * 1000;
	}

	function startUpdating() {
		if (intervalId !== null) {
			clearInterval(intervalId);
		}

		const updateInterval = getUpdateInterval();

		intervalId = setInterval(() => {
			// Update the reactive date
			currentTime.setTime(Date.now());

			// Check if we need to adjust the interval
			const newInterval = getUpdateInterval();
			if (newInterval !== updateInterval) {
				startUpdating(); // Restart with new interval
			}
		}, updateInterval);
	}

	// Start the update cycle
	startUpdating();

	// Cleanup function to clear the interval
	function cleanup() {
		if (intervalId !== null) {
			clearInterval(intervalId);
			intervalId = null;
		}
	}

	return {
		get relativeTime() {
			return relativeTime;
		},
		cleanup
	};
}

/**
 * A simpler version that just returns a reactive relative time string.
 * Automatically manages cleanup when the component is destroyed.
 *
 * @param targetDate - The date to calculate relative time from
 * @returns A reactive relative time string
 *
 * @example
 * ```svelte
 * <script>
 *   import { reactiveRelativeTime } from '$lib/utils/reactive-time.svelte.js';
 *
 *   const timestamp = '2024-01-15T10:30:00Z';
 *   const relativeTime = reactiveRelativeTime(timestamp);
 * </script>
 *
 * <p>Updated {relativeTime}</p>
 * ```
 */
export function reactiveRelativeTime(targetDate: Date | string | number) {
	const target = new Date(targetDate);

	if (isNaN(target.getTime())) {
		return 'Invalid date';
	}

	// Create reactive current time
	const currentTime = new SvelteDate();

	// Set up automatic updates with smart intervals
	$effect(() => {
		let intervalId: ReturnType<typeof setInterval>;

		function updateTime() {
			const now = Date.now();
			const diffMs = now - target.getTime();
			const diffMins = Math.floor(diffMs / 1000 / 60);

			// Determine update frequency based on age
			let intervalMs: number;
			if (diffMins < 1) {
				intervalMs = 1000; // Every second for recent times
			} else if (diffMins < 60) {
				intervalMs = 60 * 1000; // Every minute
			} else if (diffMins < 60 * 24) {
				intervalMs = 60 * 60 * 1000; // Every hour
			} else {
				intervalMs = 24 * 60 * 60 * 1000; // Every day
			}

			// Clear existing interval and set new one
			if (intervalId) clearInterval(intervalId);

			intervalId = setInterval(() => {
				currentTime.setTime(Date.now());
			}, intervalMs);
		}

		// Start updating
		updateTime();

		// Cleanup on effect destruction
		return () => {
			if (intervalId) clearInterval(intervalId);
		};
	});

	// Return reactive relative time that depends on currentTime
	const relativeTime = $derived(() => getRelativeTime(target, currentTime));
	return relativeTime;
}

/**
 * Hook-style function for use in components that need more control.
 * Returns both the reactive time and manual update function.
 *
 * @param targetDate - The date to calculate relative time from
 * @returns Object with reactive time and update function
 */
export function useReactiveRelativeTime(targetDate: Date | string | number) {
	const target = new Date(targetDate);

	if (isNaN(target.getTime())) {
		return {
			relativeTime: 'Invalid date',
			updateNow: () => {}
		};
	}

	const currentTime = new SvelteDate();
	const relativeTime = $derived((currentTime.getTime(), getRelativeTime(target)));

	// Manual update function
	function updateNow() {
		currentTime.setTime(Date.now());
	}

	// Auto-update with smart intervals
	$effect(() => {
		const getInterval = () => {
			const diffMins = Math.floor((Date.now() - target.getTime()) / 1000 / 60);
			if (diffMins < 1) return 1000;
			if (diffMins < 60) return 60 * 1000;
			if (diffMins < 60 * 24) return 60 * 60 * 1000;
			return 24 * 60 * 60 * 1000;
		};

		const intervalId = setInterval(() => {
			currentTime.setTime(Date.now());
		}, getInterval());

		return () => clearInterval(intervalId);
	});

	return {
		get relativeTime() {
			return relativeTime;
		},
		updateNow
	};
}
