/**
 * Exchange Content Utilities
 *
 * Functions for extracting meaningful content from chat exchanges,
 * including handling tool calls, response nodes, and summaries.
 */

import type { CleanChatExchangeData, CleanChatResponseNode } from '$lib/api/unified-client';

export interface ExchangeContentResult {
	/** The extracted content text */
	content: string;
	/** The type of content extracted */
	type: 'text' | 'tool' | 'summary' | 'none';
	/** Whether this is from a tool call */
	isToolCall: boolean;
	/** Tool name if this is a tool call */
	toolName?: string;
}

/**
 * Extract the last meaningful message from a chat exchange
 * Handles tool calls, response nodes, and summaries appropriately
 */
export function extractLastMessageFromExchange(
	exchange: CleanChatExchangeData
): ExchangeContentResult {
	if (!exchange?.exchange) {
		return { content: '', type: 'none', isToolCall: false };
	}

	const { responseNodes, responseText } = exchange.exchange;
	const turnSummary = exchange.turnSummary;

	// First, try to extract from response nodes (most detailed)
	if (responseNodes && responseNodes.length > 0) {
		// Process nodes from last to first to get the most recent content
		for (let i = responseNodes.length - 1; i >= 0; i--) {
			const node = responseNodes[i];
			const result = extractContentFromResponseNode(node);
			if (result.content) {
				return result;
			}
		}
	}

	// Fall back to turn summary if available
	if (turnSummary && turnSummary.trim()) {
		return {
			content: turnSummary.trim(),
			type: 'summary',
			isToolCall: false
		};
	}

	// Fall back to response text if available
	if (responseText && responseText.trim()) {
		return {
			content: responseText.trim(),
			type: 'text',
			isToolCall: false
		};
	}

	// No content found
	return { content: '', type: 'none', isToolCall: false };
}

/**
 * Extract content from a single response node
 */
function extractContentFromResponseNode(node: CleanChatResponseNode): ExchangeContentResult {
	// Handle tool use nodes (type 5)
	if (node.type === 5 && (node.toolUse || node.tool_use)) {
		const toolUse = node.toolUse || node.tool_use;
		if (toolUse) {
			const snippet = createToolCallSnippet(toolUse);
			return {
				content: snippet,
				type: 'tool',
				isToolCall: true,
				toolName: toolUse.toolName || toolUse.tool_name
			};
		}
	}

	// Handle text content nodes (type 0 - RAW_RESPONSE, or any node with text content)
	if (node.content && node.content.trim()) {
		return {
			content: node.content.trim(),
			type: 'text',
			isToolCall: false
		};
	}

	// Handle text nodes
	if (node.textNode?.content && node.textNode.content.trim()) {
		return {
			content: node.textNode.content.trim(),
			type: 'text',
			isToolCall: false
		};
	}

	// No content in this node
	return { content: '', type: 'none', isToolCall: false };
}

/**
 * Create a readable snippet for a tool call
 */
function createToolCallSnippet(toolUse: any): string {
	const toolName = toolUse.toolName || toolUse.tool_name || 'unknown';

	// Handle specific tool types with more descriptive snippets
	switch (toolName) {
		case 'github-api':
			return createGitHubToolSnippet(toolUse);
		case 'str-replace-editor':
			return createFileEditSnippet(toolUse);
		case 'codebase-retrieval':
			return 'Searched codebase for information';
		case 'web-search':
			return 'Searched the web for information';
		case 'browser_navigate_Playwright':
			return 'Navigated to a webpage';
		case 'launch-process':
			return 'Ran a command';
		case 'save-file':
			return 'Created a new file';
		case 'view':
			return 'Viewed file or directory';
		case 'grep-search':
			return 'Searched files for text';
		default:
			return `Used ${toolName} tool`;
	}
}

/**
 * Create a specific snippet for GitHub API tool calls
 */
function createGitHubToolSnippet(toolUse: any): string {
	try {
		const input =
			typeof toolUse.inputJson === 'string' ? JSON.parse(toolUse.inputJson) : toolUse.inputJson;

		if (input?.method && input?.path) {
			const method = input.method.toUpperCase();
			const path = input.path;

			// Handle common GitHub API patterns
			if (method === 'POST' && path.endsWith('/pulls')) {
				return 'Created a pull request';
			}
			if (method === 'GET' && path.includes('/pulls/')) {
				return 'Retrieved pull request information';
			}
			if (method === 'POST' && path.includes('/issues')) {
				return 'Created an issue';
			}
			if (method === 'GET' && path.includes('/issues')) {
				return 'Retrieved issue information';
			}
			if (path.includes('/commits')) {
				return 'Retrieved commit information';
			}

			return `Made ${method} request to GitHub API`;
		}
	} catch {
		// Fall through to default
	}

	return 'Used GitHub API';
}

/**
 * Create a specific snippet for file editing tool calls
 */
function createFileEditSnippet(toolUse: any): string {
	try {
		const input =
			typeof toolUse.inputJson === 'string' ? JSON.parse(toolUse.inputJson) : toolUse.inputJson;

		if (input?.command === 'str_replace') {
			return 'Edited a file';
		}
		if (input?.command === 'insert') {
			return 'Added content to a file';
		}
	} catch {
		// Fall through to default
	}

	return 'Modified a file';
}

/**
 * Extract the last sentence or two from content for preview
 * (Moved from agent-preview store for reusability)
 */
export function extractLastSentence(content: string): string {
	if (!content || !content.trim()) return '';

	// Clean up the content
	const cleaned = content.trim();

	// Split into sentences
	const sentences = cleaned
		.split(/[.!?]+/)
		.map((s) => s.trim())
		.filter((s) => s.length > 0);

	if (sentences.length === 0) {
		// No sentences found, truncate the content
		return truncateText(cleaned, 300);
	}

	// Take the last sentence, or last two if the last one is very short
	const lastSentence = sentences[sentences.length - 1];

	if (lastSentence.length < 30 && sentences.length > 1) {
		const secondToLast = sentences[sentences.length - 2];
		const combined = `${secondToLast}. ${lastSentence}.`;
		return truncateText(combined, 350);
	}

	return truncateText(lastSentence + '.', 350);
}

/**
 * Truncate text to a maximum length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
	if (text.length <= maxLength) return text;
	return text.substring(0, maxLength - 3) + '...';
}
