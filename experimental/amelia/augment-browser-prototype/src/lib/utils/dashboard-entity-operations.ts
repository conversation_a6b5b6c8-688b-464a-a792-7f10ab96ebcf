import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
import { triggers } from '$lib/stores/global-state.svelte';
import { apiClient, ChatRequestNodeType, type CreateAgentRequest } from '$lib/api/unified-client';
import { addToast } from '$lib/stores/toast';
import { getEntityTypeFromTrigger } from '$lib/utils/entity-conversion';
import { get } from 'svelte/store';
import { updateAgent, updateExecutions, globalState } from '$lib/stores/global-state.svelte';
import {
	RemoteAgentStatus,
	RemoteAgentWorkspaceStatus,
	type CleanRemoteAgent,
	type CleanTriggerExecution,
	convertRemoteAgentFromAPI
} from '$lib/api/unified-client';
// Legacy RemoteAgent type removed - using CleanRemoteAgent from unified-client
import { getCurrentRepository, getRepositoryUrlFromEntity } from '$lib/utils/project-repository';
import { wrapInitialMessageWithLatestInstructions } from '$lib/types/structured-response';
import { formatErrorMessage } from '$lib/utils/error-messages';
import { GitHubEntityType, LinearEntityType } from '$lib/types/trigger-enums';
import { dismissEntityAndSync } from '$lib/stores/data-operations.svelte';

/**
 * Format user-friendly error messages from API responses
 */
function formatUserFriendlyError(error: any): string {
	// Create error object in the format expected by formatErrorMessage
	const errorObj = {
		error: error.message || error.error || error,
		details: error.details
	};

	const formatted = formatErrorMessage(errorObj);
	return formatted.message;
}

/**
 * Map entity to the correct entity type parameter for API calls
 */
export function getEntityTypeParameter(
	providerId: string,
	entityType: string
): GitHubEntityType | LinearEntityType {
	if (providerId === 'github') {
		if (entityType === 'pull_request') {
			return GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST;
		} else if (entityType === 'workflow_run') {
			return GitHubEntityType.GITHUB_ENTITY_TYPE_WORKFLOW_RUN;
		} else {
			throw new Error(`Unsupported GitHub entity type: ${entityType}`);
		}
	} else if (providerId === 'linear') {
		if (entityType === 'issue') {
			return LinearEntityType.LINEAR_ENTITY_TYPE_ISSUE;
		} else {
			throw new Error(`Unsupported Linear entity type: ${entityType}`);
		}
	} else if (providerId === 'schedule') {
		return SCHEDULE_ENTITY_TYPE_SCHEDULE;
	} else {
		throw new Error(`Unsupported provider: ${providerId}`);
	}
}

/**
 * Start work on an entity by executing a trigger manually
 */
export async function startWorkOnEntity(entity: UnifiedEntity, triggerId: string): Promise<void> {
	try {
		console.log('Starting work for entity:', entity);

		await apiClient.triggers.execute(triggerId, entity.id);

		console.log('Successfully started work for entity:', entity.id);
	} catch (err) {
		console.error('Failed to start work:', err);
		throw err;
	}
}

/**
 * Check if an entity supports starting work
 */
export function canStartWork(entity: UnifiedEntity): boolean {
	try {
		getEntityTypeParameter(entity.providerId, entity.entityType);
		return true;
	} catch {
		return false;
	}
}

/**
 * Get supported entity types for a provider
 */
export function getSupportedEntityTypes(providerId: string): string[] {
	switch (providerId) {
		case 'github':
			return ['pull_request', 'workflow_run'];
		case 'linear':
			return ['issue'];
		default:
			return [];
	}
}

/**
 * Find a matching trigger for an entity
 */
export function findMatchingTrigger(entity: UnifiedEntity): NormalizedTrigger | undefined {
	const allTriggers = get(triggers);

	// Find a trigger that matches the entity's provider and type
	const matchingTrigger = allTriggers.find((trigger) => {
		const [providerId, entityType] = getEntityTypeFromTrigger(trigger);
		return providerId === entity.providerId && entityType === entity.entityType;
	});

	return matchingTrigger;
}

/**
 * Create a remote agent manually for an entity with user instructions
 * Uses execute trigger manually if trigger exists, otherwise uses execute manual agent
 */
export async function createRemoteAgentManually(
	entity: UnifiedEntity,
	triggerId: string,
	instructions: string,
	options: {
		branch?: string;
		userGuidelines?: string;
		workspaceGuidelines?: string;
		repositoryUrl?: string;
	} = {}
): Promise<{
	agentId: string;
	executionId: string;
	matchingTrigger: NormalizedTrigger | null;
	entity: UnifiedEntity;
}> {
	try {
		console.log('Creating remote agent manually for entity:', entity);

		const wrappedInstructions = wrapInitialMessageWithLatestInstructions(instructions);

		if (!triggerId) {
			throw new Error('No trigger ID provided');
		}

		// Execute trigger manually with instructions as extra prompt
		const result = await apiClient.triggers.execute(triggerId, entity.id, wrappedInstructions);

		const response = {
			remoteAgentId: result.remoteAgentId,
			executionId: result.id,
			status: result.status,
			errorMessage: result.errorMessage
		};
		// } else {
		// 	console.log('No matching trigger found, using execute manual agent');

		// 	// Determine repository URL: entity repository > current project repository
		// 	let repositoryUrl = getRepositoryUrlFromEntity(entity);

		// 	if (!repositoryUrl) {
		// 		// Fall back to current project repository
		// 		try {
		// 			repositoryUrl = `https://github.com/${getCurrentRepository()}`;
		// 		} catch {
		// 			throw new Error('No repository URL could be determined from entity or current project');
		// 		}
		// 	}

		// 	// Create agent config with instructions
		// 	const agentConfig: CreateAgentRequest = {
		// 		initialRequestDetails: {
		// 			requestNodes: [
		// 				{
		// 					id: 1,
		// 					type: ChatRequestNodeType.TEXT,
		// 					textNode: {
		// 						content: wrappedInstructions
		// 					}
		// 				}
		// 			]
		// 		},
		// 		workspaceSetup: {
		// 			startingFiles: {
		// 				githubCommitRef: {
		// 					repositoryUrl: repositoryUrl,
		// 					gitRef: options.branch || 'main'
		// 				}
		// 			}
		// 		},
		// 		userGuidelines: options.userGuidelines,
		// 		workspaceGuidelines: options.workspaceGuidelines
		// 	};

		// 	// Create agent directly since there's no manual execute API
		// 	const result = await apiClient.agents.create(agentConfig);

		// 	response = {
		// 		remoteAgentId: result.remoteAgentId,
		// 		status: 'created'
		// 	};

		if (response.errorMessage) {
			// Format user-friendly error messages
			const errorMessage = formatUserFriendlyError(response.errorMessage);
			throw new Error(errorMessage);
		}

		// Extract agent ID from response
		const agentId = response.remoteAgentId;
		const executionId = response.executionId;

		if (agentId) {
			// Create a minimal remote agent object and add it to the store
			const minimalAgent: CleanRemoteAgent = {
				id: agentId,
				title: `Agent for: ${entity.title}`,
				status: RemoteAgentStatus.AGENT_RUNNING,
				workspaceStatus: RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_UNSPECIFIED,
				startedAt: new Date(),
				updatedAt: new Date(),
				expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
				sessionSummary: `Agent for: ${entity.title}`,
				isSetupScriptAgent: false,
				hasUpdates: false,
				githubUrl: options.repositoryUrl,
				githubRef: options.branch || 'main'
			};

			// Add to the centralized store
			updateAgent(minimalAgent);

			// Create and store the execution that links the agent to the trigger
			if (executionId) {
				const execution: CleanTriggerExecution = {
					id: executionId,
					triggerId: triggerId,
					status: response.status || 'running',
					remoteAgentId: agentId,
					startedAt: new Date(),
					entityId: entity.id,
					entityType: entity.entityType,
					manualExecution: true
				};

				// Get existing executions for this trigger and add the new one
				const currentState = get(globalState);
				const existingExecutions = currentState.executions[triggerId] || [];
				const updatedExecutions = [execution, ...existingExecutions];

				// Store the execution in the global state
				updateExecutions(triggerId, updatedExecutions);
			}

			// TODO: Start polling for the newly created agent when polling is implemented
			// startPollingAgent(agentId);

			console.log('Successfully created remote agent and added to store:', agentId);
		}

		// Automatically dismiss the entity after successful agent creation
		try {
			await dismissEntityAndSync(triggerId, entity.id, true);
			console.log('Entity automatically dismissed after agent creation:', entity.id);
		} catch (dismissError) {
			// Don't fail the agent creation if dismiss fails, just log it
			console.warn('Failed to dismiss entity after agent creation:', dismissError);
		}

		// Return the full response data for the caller to handle
		return {
			agentId: agentId || 'unknown',
			executionId: executionId || 'unknown',
			matchingTrigger: null, // Fixed the commented out line
			entity: entity
		};
	} catch (err) {
		console.error('Failed to create remote agent:', err);

		// Show error toast
		addToast({
			type: 'error',
			message: `Failed to create remote agent: ${err instanceof Error ? err.message : 'Unknown error'}`,
			duration: 8000
		});

		throw err;
	}
}
