/**
 * Utility functions for extracting summaries from chat exchanges
 * Moved from RemoteAgentDetailContent.svelte to be reusable across components
 */

import { processExchangesWithToolFiltering } from '$lib/components/chat-history/utils';
import {
	extractSummaryFromResponse,
	extractGeneralSummaryFromResponse
} from '$lib/utils/session-summary-parser';

/**
 * Extract general summary from exchanges
 * Looks for session summary in raw exchanges first, then searches for <general_summary> tags
 */
export function getExchangesGeneralSummary(exchanges: any[]): string {
	if (!exchanges || exchanges.length === 0) return '';

	// Look for session summary in raw exchanges first
	for (let i = exchanges.length - 1; i >= 0; i--) {
		const exchange = exchanges[i];
		if (exchange.sessionSummary) {
			return exchange.sessionSummary;
		}
	}

	// Process exchanges to get structured data
	const processedExchanges = processExchangesWithToolFiltering(exchanges);

	// Look for <general_summary> tags in assistant responses
	const assistantResponses = processedExchanges
		.flatMap((ex) => ex.assistantNodes)
		.filter((node) => node.content && node.content.trim().length > 0)
		.map((node) => node.content);

	// Search for general_summary tags in responses (most recent first)
	for (let i = assistantResponses.length - 1; i >= 0; i--) {
		const response = assistantResponses[i];
		const generalSummary = extractGeneralSummaryFromResponse(response);
		if (generalSummary) {
			return generalSummary;
		}
	}

	return '';
}

/**
 * Extract session summary from exchanges
 * Looks for explicit session summary in metadata first, then searches for <summary> tags
 */
export function getExchangesSummary(exchanges: any[]): string {
	if (!exchanges || exchanges.length === 0) return '';

	// Look for explicit session summary in metadata
	for (let i = exchanges.length - 1; i >= 0; i--) {
		const exchange = exchanges[i];
		if (exchange.sessionSummary) {
			return exchange.sessionSummary;
		}
	}

	// Process exchanges to get structured data
	const processedExchanges = processExchangesWithToolFiltering(exchanges);

	// Look for <summary> tags in assistant responses
	const assistantResponses = processedExchanges
		.flatMap((ex) => ex.assistantNodes)
		.filter((node) => node.content && node.content.trim().length > 0)
		.map((node) => node.content);

	// Search for summary tags in responses (most recent first)
	for (let i = assistantResponses.length - 1; i >= 0; i--) {
		const response = assistantResponses[i];
		const summary = extractSummaryFromResponse(response);
		if (summary) {
			return summary;
		}
	}

	return '';
}

/**
 * Get the best available summary for an agent from exchanges
 * Prioritizes general summary over session summary
 */
export function getBestExchangeSummary(exchanges: any[]): string {
	const generalSummary = getExchangesGeneralSummary(exchanges);
	if (generalSummary) return generalSummary;

	const sessionSummary = getExchangesSummary(exchanges);
	if (sessionSummary) return sessionSummary;

	return '';
}
