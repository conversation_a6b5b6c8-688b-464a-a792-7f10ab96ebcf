import { browser } from '$app/environment';

// Global theme link element
let currentThemeLink: HTMLLinkElement | null = null;
let currentTheme: string | null = null;

/**
 * Load a highlight.js theme globally
 * This ensures only one theme is loaded at a time across all components
 */
export function loadHighlightTheme(themeName: string): Promise<void> {
	if (!browser) return Promise.resolve();

	// Don't reload if it's the same theme
	if (currentTheme === themeName) return Promise.resolve();

	// Remove existing theme
	if (currentThemeLink) {
		currentThemeLink.remove();
		currentThemeLink = null;
	}

	return new Promise((resolve, reject) => {
		// Create new theme link
		currentThemeLink = document.createElement('link');
		currentThemeLink.rel = 'stylesheet';
		currentThemeLink.href = `https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/${themeName}.min.css`;
		currentThemeLink.setAttribute('data-highlight-theme', themeName);

		// Handle load events
		currentThemeLink.onload = () => {
			currentTheme = themeName;
			resolve();
		};

		currentThemeLink.onerror = () => {
			console.warn(`Failed to load highlight.js theme: ${themeName}`);
			reject(new Error(`Failed to load theme: ${themeName}`));
		};

		document.head.appendChild(currentThemeLink);
	});
}

/**
 * Get the appropriate theme name based on the current theme
 */
export function getHighlightThemeName(isDark: boolean): string {
	return isDark ? 'github-dark' : 'github';
}

/**
 * Preload both light and dark themes to avoid loading delays
 */
export function preloadHighlightThemes() {
	if (!browser) return;

	// Preload both themes
	const themes = ['github', 'github-dark'];
	themes.forEach((theme) => {
		const link = document.createElement('link');
		link.rel = 'preload';
		link.as = 'style';
		link.href = `https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/${theme}.min.css`;
		document.head.appendChild(link);
	});
}
