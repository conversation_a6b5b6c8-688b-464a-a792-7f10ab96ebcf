/**
 * Utility functions for converting between different entity formats
 */

import type { ProviderEntity, EntityList } from '$lib/types';
import type { MatchingEntity, TriggerResult } from '$lib/types';
import { getEntityTypeDisplayName as getConfigDisplayName } from '$lib/utils/entity-types';

/**
 * Unified entity structure for consistent rendering and searching
 * This provides a common interface for all entity types from different providers
 */
export interface UnifiedEntity {
	// Core identification
	id: string;
	title: string;
	description?: string;
	url?: string;

	// Provider and type information
	providerId: string;
	entityType: string;

	// Common entity properties
	state?: string;
	priority?: number | string;
	assignee?: {
		name: string;
		login?: string;
		avatar?: string;
		display_name?: string;
		avatar_url?: string;
		email?: string;
	};
	assignees?: Array<{
		name: string;
		login?: string;
		avatar?: string;
		display_name?: string;
		avatar_url?: string;
		email?: string;
	}>;
	author?: {
		name: string;
		login?: string;
		avatar?: string;
		display_name?: string;
		avatar_url?: string;
		email?: string;
	};
	labels?: Array<{ name: string; color?: string }>;

	// Timestamps
	createdAt?: string;
	updatedAt?: string;

	// Dismissal status
	isDismissed?: boolean;

	// Provider-specific metadata
	metadata: {
		// GitHub specific
		number?: number;
		draft?: boolean;
		repository?: string;
		mergeable?: boolean;

		// Linear specific
		identifier?: string;
		team?: {
			id: string;
			name: string;
			key: string;
			description?: string;
		};

		// Jira specific
		issueType?: string;
		project?: string;

		// Any other provider-specific data
		[key: string]: any;
	};

	// Related entities (when include_related_entities=true)
	relatedEntities?: {
		[relationshipType: string]: UnifiedEntity[];
	};
}

/**
 * Convert a ProviderEntity to UnifiedEntity
 */
const entityMapping: Record<string, Record<string, any>> = {
	github: {
		pull_request: {
			title: (d: any) => d.title,
			description: (d: any) => d.body,
			url: (d: any) => d.html_url,
			updatedAt: (d: any) => d.updated_at
		},
		workflow_run: {
			title: (d: any) => d.display_title,
			description: (d: any) => `From ${d.head_branch} to ${d.head_branch} - ${d.conclusion}`,
			url: (d: any) => d.html_url,
			updatedAt: (d: any) => d.updated_at
		}
	},
	linear: {
		issue: {
			title: (d: any) => {
				return d.title;
			},
			description: (d: any) => d.description,
			url: (d: any) => d.url,
			updatedAt: (d: any) => d.updated_at
		}
	}
};
export function convertProviderEntityToUnified(
	providerId: string,
	entityType: string,
	entity: ProviderEntity
): UnifiedEntity {
	// Handle null or undefined entity
	if (!entity) {
		throw new Error(
			`Entity data is null or undefined for ${providerId}:${entityType}. The entity may not exist or the API response is malformed.`
		);
	}

	const mapping =
		entityMapping[providerId]?.[entityType] || entityMapping['github']['pull_request'];

	// Extract the correct nested data based on entity structure
	let entityData: any;
	if (providerId === 'github' && entityType === 'pull_request' && (entity as any).pullRequest) {
		entityData = (entity as any).pullRequest;
	} else if (providerId === 'linear' && entityType === 'issue' && (entity as any).issue) {
		entityData = (entity as any).issue;
	} else {
		// Fallback to the entity itself for workflow_runs or other types
		entityData = entity;
	}

	return {
		id: entity.id,
		title: mapping.title(entityData),
		description: mapping.description(entityData),
		url: mapping.url(entityData),
		updatedAt: mapping.updatedAt(entityData),
		providerId: providerId,
		entityType: entityType,
		metadata: entity
	};
}

/**
 * Convert any entity type to UnifiedEntity
 */
export function toUnifiedEntity(
	providerId: string,
	entityType: string,
	entity: MatchingEntity
): UnifiedEntity | null {
	if (!entityType || !entity) return null;
	const normalizedEntityType = entityType.replace(/s$/, '');
	return convertProviderEntityToUnified(providerId, normalizedEntityType, entity as any);
}

/**
 * Process related entities from the main entity object
 */
function processRelatedEntities(
	entity: any
): { [relationshipType: string]: UnifiedEntity[] } | undefined {
	if (!entity) return undefined;

	const processedRelated: { [relationshipType: string]: UnifiedEntity[] } = {};

	// Map of entity fields to relationship types
	const relationshipMappings: { [key: string]: string } = {
		reviewComments: 'review_comments',
		requestedReviewers: 'requested_reviewers',
		commits: 'commits',
		assignees: 'assignees',
		labels: 'labels',
		checkSuites: 'check_suites',
		statuses: 'statuses'
		// Add more mappings as needed based on the actual API response
	};

	// Process each potential related entity field
	for (const [fieldName, relationshipType] of Object.entries(relationshipMappings)) {
		// Look for related entities in nested entity type objects (e.g., entity.pullRequest.requestedReviewers)
		let entities = entity[fieldName];

		// If not found at top level, check nested entity type objects
		if (!entities) {
			const nestedObjects = ['pullRequest', 'issue', 'workflowRun', 'workflow_run'];
			for (const nestedKey of nestedObjects) {
				if (entity[nestedKey] && entity[nestedKey][fieldName]) {
					entities = entity[nestedKey][fieldName];
					break;
				}
			}
		}

		if (Array.isArray(entities) && entities.length > 0) {
			const convertedEntities: UnifiedEntity[] = [];

			for (const relatedEntity of entities) {
				let converted: UnifiedEntity | null = null;

				// Convert review comments
				if (fieldName === 'reviewComments' && relatedEntity) {
					// Extract author info - could be in user object or directly on entity
					const authorLogin = relatedEntity.author?.login || relatedEntity.login;
					const authorAvatar =
						relatedEntity.user?.avatar_url ||
						relatedEntity.user?.avatarUrl ||
						relatedEntity.avatarUrl;
					const authorUrl =
						relatedEntity.user?.html_url || relatedEntity.user?.htmlUrl || relatedEntity.htmlUrl;

					converted = {
						id: relatedEntity.id?.toString() || 'unknown',
						title: `Review Comment by ${authorLogin || 'Unknown'}`,
						description: relatedEntity.body || '',
						url: relatedEntity.html_url || relatedEntity.htmlUrl || '',
						providerId: 'github',
						entityType: 'review_comment',
						state: relatedEntity.state || 'active',
						author: relatedEntity.author,
						createdAt: relatedEntity.created_at || relatedEntity.createdAt,
						updatedAt: relatedEntity.updated_at || relatedEntity.updatedAt,
						metadata: relatedEntity
					};
				}
				// Convert requested reviewers
				else if (fieldName === 'requestedReviewers' && relatedEntity) {
					converted = {
						id: relatedEntity.id?.toString() || 'unknown',
						title: `Requested Reviewer: ${relatedEntity.login || 'Unknown'}`,
						description: `Review requested from ${relatedEntity.login}`,
						url: relatedEntity.html_url || '',
						providerId: 'github',
						entityType: 'requested_reviewer',
						state: 'pending',
						author: {
							name: relatedEntity.login,
							login: relatedEntity.login,
							avatar: relatedEntity.avatarUrl,
							htmlUrl: relatedEntity.htmlUrl
						},
						metadata: relatedEntity
					};
				}
				// Convert commits
				else if (fieldName === 'commits' && relatedEntity) {
					converted = {
						id: relatedEntity.sha || 'unknown',
						title: relatedEntity.commit?.message?.split('\n')[0] || 'Commit',
						description: relatedEntity.commit?.message || '',
						url: relatedEntity.html_url || '',
						providerId: 'github',
						entityType: 'commit',
						state: 'committed',
						author: relatedEntity.commit?.author
							? {
									name: relatedEntity.commit.author.name,
									login: relatedEntity.author?.login || relatedEntity.commit.author.name,
									avatar: relatedEntity.author?.avatar_url
								}
							: undefined,
						createdAt: relatedEntity.commit?.author?.date,
						metadata: relatedEntity
					};
				}
				// Convert assignees
				else if (fieldName === 'assignees' && relatedEntity) {
					converted = {
						id: relatedEntity.id?.toString() || relatedEntity.login || 'unknown',
						title: `Assignee: ${relatedEntity.login || relatedEntity.name || 'Unknown'}`,
						description: `Assigned to ${relatedEntity.login || relatedEntity.name}`,
						url: relatedEntity.html_url || '',
						providerId: 'github',
						entityType: 'assignee',
						state: 'assigned',
						author: {
							name: relatedEntity.login || relatedEntity.name,
							login: relatedEntity.login,
							avatar: relatedEntity.avatar_url
						},
						metadata: relatedEntity
					};
				}
				// Convert labels
				else if (fieldName === 'labels' && relatedEntity) {
					converted = {
						id: relatedEntity,
						title: relatedEntity,
						url: '',
						providerId: 'github',
						entityType: 'label',
						state: 'active',
						metadata: relatedEntity
					};
				}

				if (converted) {
					convertedEntities.push(converted);
				}
			}

			if (convertedEntities.length > 0) {
				processedRelated[relationshipType] = convertedEntities;
			}
		}
	}

	return Object.keys(processedRelated).length > 0 ? processedRelated : undefined;
}

/**
 * Convert backend entity format (GitHubEntity/LinearEntity) to UnifiedEntity
 */
export function convertBackendEntityToUnified(
	providerId: string,
	entityType: string,
	entity: any
): UnifiedEntity | null {
	if (!entity) return null;

	// Handle GitHub entities
	if (providerId === 'github') {
		if (entityType === 'pull_request' && (entity.pull_request || entity.pullRequest)) {
			const pr = entity.pull_request || entity.pullRequest;
			return {
				// id: entity.id,
				id: pr.number.toString(),
				title: pr.title,
				description: pr.body,
				url: pr.htmlUrl,
				providerId,
				entityType,
				state: pr.state,
				assignee: pr.requested_reviewers?.[0]
					? {
							name: pr.requested_reviewers[0].login,
							login: pr.requested_reviewers[0].login,
							avatar: pr.requested_reviewers[0].avatar_url
						}
					: undefined,
				assignees: (pr.requested_reviewers || []).map((r: any) => ({
					name: r.login,
					login: r.login,
					avatar: r.avatar_url
				})),
				author: {
					name: pr.user.login,
					login: pr.user.login,
					avatar: pr.user.avatar_url
				},
				labels: pr.labels?.map((label: any) => ({ name: label })) || [],
				createdAt: pr.created_at,
				updatedAt: pr.updated_at,
				isDismissed: entity.is_dismissed || false,
				metadata: {
					number: pr.number,
					draft: pr.draft,
					repository: entity.repository.full_name,
					mergeable: !pr.merged,
					...pr
				},
				relatedEntities: processRelatedEntities(entity)
			};
		}

		if (entityType === 'workflow_run' && (entity.workflow_run || entity.workflowRun)) {
			const wr = entity.workflow_run || entity.workflowRun;
			return {
				id: entity.id,
				title: wr.display_title || wr.name,
				description: `Workflow run on ${wr.head_branch} - ${wr.conclusion || wr.status}`,
				url: `${entity.repository.html_url}/actions/runs/${wr.id}`,
				providerId,
				entityType,
				state: wr.conclusion || wr.status,
				author: {
					name: wr.actor.login,
					login: wr.actor.login,
					avatar: wr.actor.avatar_url
				},
				createdAt: wr.created_at,
				updatedAt: wr.updated_at,
				isDismissed: entity.is_dismissed || false,
				metadata: {
					repository: entity.repository.full_name,
					workflow_id: wr.workflow_id,
					head_branch: wr.head_branch,
					...wr
				},
				relatedEntities: processRelatedEntities(entity)
			};
		}
	}

	// Handle Linear entities
	if (providerId === 'linear' && entityType === 'issue' && entity.issue) {
		const issue = entity.issue;
		return {
			id: entity.id,
			title: issue.title,
			description: issue.description,
			url: issue.url,
			providerId,
			entityType,
			state: issue.state?.name,
			priority: issue.priority,
			assignee: issue.assignee
				? {
						name: issue.assignee.display_name || issue.assignee.name,
						avatar: issue.assignee.avatar_url
					}
				: undefined,
			assignees: (issue.assignees || []).map((a: any) => ({
				name: a.display_name || a.name,
				avatar: a.avatar_url
			})),
			author: issue.creator
				? {
						name: issue.creator.display_name || issue.creator.display_name || issue.creator.name,
						avatar: issue.creator.avatar_url
					}
				: undefined,
			labels: issue.labels?.map((label: any) => ({ name: label.name, color: label.color })) || [],
			createdAt: issue.created_at,
			updatedAt: issue.updated_at,
			isDismissed: entity.is_dismissed || false,
			metadata: {
				identifier: issue.identifier,
				team: entity.team?.key,
				...issue
			},
			relatedEntities: processRelatedEntities(entity)
		};
	}

	// Fallback - return a basic unified entity
	return {
		id: entity.id || 'unknown',
		title: entity.title || 'Unknown Entity',
		description: entity.description,
		url: entity.url,
		providerId,
		entityType,
		metadata: entity,
		relatedEntities: processRelatedEntities(entity)
	};
}

/**
 * Extract provider ID from entity metadata or URL
 */
function extractProviderFromEntity(entity: MatchingEntity): string {
	return ['', 'github', 'linear', 'jira', 'slack'][(entity as any).type] || 'github';
}

/**
 * Extract entity type from entity metadata or URL
 */
function extractEntityTypeFromEntity(entity: MatchingEntity): string {
	if ((entity as any)['pull_request']) {
		return 'pull_request';
	} else if ((entity as any)['issue']) {
		return 'issue';
	}
	return 'issue';
}

/**
 * Create a mock EntityList for use with provider UI components
 */
export function createMockEntityList(entities: ProviderEntity[]): EntityList {
	return {
		id: 'trigger-entities',
		name: 'Trigger Entities',
		description: 'Entities matching trigger conditions',
		projectId: 'mock-project',
		providerId: 'github',
		entityType: 'pull_requests',
		filters: [],
		instructions: 'Mock entity list for trigger entities',
		autoCreateTasks: false,
		isActive: true,
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString()
	};
}

/**
 * Generate mock provider entities for testing
 */
export function generateMockProviderEntities(): ProviderEntity[] {
	return [
		{
			id: 'github-issue-1',
			sourceId: 'github-issue-1',
			providerId: 'github',
			entityType: 'issues',
			title: 'Fix authentication bug in login flow',
			description:
				'Users are experiencing issues logging in with special characters in their passwords. The authentication service is not properly encoding the credentials.',
			url: 'https://github.com/example/repo/issues/123',
			state: 'open',
			assignee: {
				name: 'John Doe',
				avatar: 'https://avatars.githubusercontent.com/u/123456?v=4'
			},
			labels: [
				{ name: 'bug', color: 'd73a49' },
				{ name: 'priority-high', color: 'b60205' }
			],
			createdAt: '2024-01-15T10:30:00Z',
			updatedAt: '2024-01-16T14:20:00Z'
		},
		{
			id: 'github-pr-1',
			sourceId: '456',
			providerId: 'github',
			entityType: 'pull_requests',
			title: 'Add dark mode support to dashboard',
			description:
				'This PR implements dark mode support across the dashboard interface with proper theme switching and persistence.',
			url: 'https://github.com/example/repo/pull/456',
			state: 'open',
			assignee: {
				name: 'Jane Smith',
				avatar: 'https://avatars.githubusercontent.com/u/789012?v=4'
			},
			labels: [
				{ name: 'enhancement', color: 'a2eeef' },
				{ name: 'ui', color: '0075ca' }
			],
			createdAt: '2024-01-14T09:15:00Z',
			updatedAt: '2024-01-16T16:45:00Z'
		},
		{
			id: 'linear-issue-1',
			sourceId: 'linear-issue-1',
			providerId: 'linear',
			entityType: 'issues',
			title: 'Implement user onboarding flow',
			description:
				'Design and implement a comprehensive user onboarding experience for new users including welcome screens, feature tours, and initial setup.',
			url: 'https://linear.app/company/issue/ENG-123',
			state: 'In Progress',
			assignee: {
				name: 'Alice Johnson',
				avatar: 'https://avatars.githubusercontent.com/u/345678?v=4'
			},
			labels: [
				{ name: 'feature', color: '5e6ad2' },
				{ name: 'frontend', color: '0ea5e9' }
			],
			createdAt: '2024-01-13T11:00:00Z',
			updatedAt: '2024-01-16T13:30:00Z'
		}
	];
}

/**
 * Search unified entities by text query
 */
export function searchUnifiedEntities(entities: UnifiedEntity[], query: string): UnifiedEntity[] {
	if (!query.trim()) return entities;

	const searchTerm = query.toLowerCase().trim();

	return entities.filter((entity) => {
		// Search in core fields
		if (entity.title?.toLowerCase().includes(searchTerm)) return true;
		if (entity.description?.toLowerCase().includes(searchTerm)) return true;
		if (entity.state?.toLowerCase().includes(searchTerm)) return true;
		if (entity.providerId?.toLowerCase().includes(searchTerm)) return true;
		if (entity.entityType?.toLowerCase().includes(searchTerm)) return true;

		// Search in people
		if (entity.assignee?.name?.toLowerCase().includes(searchTerm)) return true;
		if (entity.author?.name?.toLowerCase().includes(searchTerm)) return true;

		// Search in labels
		if (entity.labels?.some((label) => label.name?.toLowerCase().includes(searchTerm))) return true;

		// Search in metadata
		if (entity.metadata.identifier?.toLowerCase().includes(searchTerm)) return true;
		if (entity.metadata.repository?.toLowerCase().includes(searchTerm)) return true;
		if (entity.metadata.team?.name?.toLowerCase().includes(searchTerm)) return true;

		return false;
	});
}

/**
 * Filter unified entities by provider
 */
export function filterUnifiedEntitiesByProvider(
	entities: UnifiedEntity[],
	providerId: string
): UnifiedEntity[] {
	if (providerId === 'all') return entities;
	return entities.filter((entity) => entity.providerId === providerId);
}

/**
 * Filter unified entities by entity type
 */
export function filterUnifiedEntitiesByType(
	entities: UnifiedEntity[],
	entityType: string
): UnifiedEntity[] {
	if (entityType === 'all') return entities;
	return entities.filter((entity) => entity.entityType === entityType);
}

/**
 * Filter unified entities by state
 */
export function filterUnifiedEntitiesByState(
	entities: UnifiedEntity[],
	state: string
): UnifiedEntity[] {
	if (state === 'all') return entities;
	return entities.filter((entity) => entity.state?.toLowerCase() === state.toLowerCase());
}

/**
 * Sort unified entities by various criteria
 */
export function sortUnifiedEntities(
	entities: UnifiedEntity[],
	sortBy: 'title' | 'createdAt' | 'updatedAt' | 'state' | 'priority',
	direction: 'asc' | 'desc' = 'desc'
): UnifiedEntity[] {
	return [...entities].sort((a, b) => {
		let comparison = 0;

		switch (sortBy) {
			case 'title':
				comparison = a.title.localeCompare(b.title);
				break;
			case 'createdAt':
				comparison = new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime();
				break;
			case 'updatedAt':
				comparison = new Date(a.updatedAt || 0).getTime() - new Date(b.updatedAt || 0).getTime();
				break;
			case 'state':
				comparison = (a.state || '').localeCompare(b.state || '');
				break;
			case 'priority':
				const aPriority = typeof a.priority === 'number' ? a.priority : 0;
				const bPriority = typeof b.priority === 'number' ? b.priority : 0;
				comparison = aPriority - bPriority;
				break;
		}

		return direction === 'asc' ? comparison : -comparison;
	});
}

/**
 * Get display name for entity type
 * @deprecated Use getEntityTypeDisplayName from entity-types config instead
 */
export function getEntityTypeDisplayName(entityType: string, providerId?: string): string {
	return getConfigDisplayName(entityType, providerId);
}

/**
 * Get provider icon/emoji
 */
export function getProviderIcon(providerId: string): string {
	switch (providerId) {
		case 'github':
			return '🐙';
		case 'linear':
			return '📐';
		case 'jira':
			return '🔷';
		case 'slack':
			return '💬';
		default:
			return '🔗';
	}
}

/**
 * Get state color for display
 */
export function getStateColor(state: string, providerId: string): string {
	// GitHub states
	if (providerId === 'github') {
		switch (state.toLowerCase()) {
			case 'open':
				return 'green';
			case 'closed':
				return 'red';
			case 'merged':
				return 'purple';
			case 'draft':
				return 'gray';
			default:
				return 'gray';
		}
	}

	// Linear states
	if (providerId === 'linear') {
		switch (state.toLowerCase()) {
			case 'backlog':
				return 'gray';
			case 'todo':
				return 'blue';
			case 'in progress':
				return 'yellow';
			case 'in review':
				return 'orange';
			case 'done':
				return 'green';
			case 'canceled':
				return 'red';
			default:
				return 'gray';
		}
	}

	// Default colors
	switch (state.toLowerCase()) {
		case 'open':
		case 'active':
		case 'in progress':
			return 'green';
		case 'closed':
		case 'resolved':
		case 'done':
			return 'blue';
		case 'failed':
		case 'error':
			return 'red';
		default:
			return 'gray';
	}
}

export function getEntityTypeFromTrigger(
	trigger: TriggerResult | { configuration?: any }
): [string, string] {
	const conditions = trigger.configuration?.conditions;
	if (!conditions) {
		return ['github', 'pull_request'];
	}

	// Check the new structure with conditions.type
	if (conditions.type === 1) {
		// TRIGGER_CONDITION_TYPE.GITHUB
		if (conditions.github?.entity_type === 1) {
			return ['github', 'pull_request'];
		} else if (conditions.github?.entity_type === 2) {
			return ['github', 'workflow_run'];
		}
	} else if (conditions.type === 2) {
		// TRIGGER_CONDITION_TYPE.LINEAR
		if (conditions.linear?.entity_type === 1) {
			return ['linear', 'issue'];
		}
	}

	// Fallback to old structure for backward compatibility
	if (conditions['github']?.entity_type === 1) {
		return ['github', 'pull_request'];
	} else if (conditions['github']?.entity_type === 2) {
		return ['github', 'workflow_run'];
	} else if (conditions['linear']?.entity_type === 1) {
		return ['linear', 'issue'];
	}

	return ['github', 'pull_request'];
}
