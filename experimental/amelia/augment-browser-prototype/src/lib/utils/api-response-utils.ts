/**
 * Utility functions for handling API responses safely
 */

/**
 * Safely parse JSON response, handling cases where the response might be plain text
 * This is common when APIs return "Invalid token" or other error messages as plain text
 */
export async function safeJsonParse(response: Response): Promise<any> {
	try {
		const contentType = response.headers.get('content-type');

		if (contentType && contentType.includes('application/json')) {
			return await response.json();
		} else {
			// Handle plain text responses (like "Invalid token")
			const textResponse = await response.text();
			return {
				error: textResponse || 'Invalid response from API',
				details: `Expected JSON but received: ${contentType || 'unknown content type'}`
			};
		}
	} catch (error) {
		// Fallback for any parsing errors
		return {
			error: 'Failed to parse API response',
			details: error instanceof Error ? error.message : 'Unknown parsing error'
		};
	}
}

/**
 * Simple fallback for when you just want to catch JSON parsing errors
 */
export async function jsonWithFallback(response: Response, fallback: any = { error: 'Invalid response from API' }): Promise<any> {
	try {
		return await response.json();
	} catch {
		return fallback;
	}
}
