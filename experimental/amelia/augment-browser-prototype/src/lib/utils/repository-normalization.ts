/**
 * Utility functions for normalizing repository names and handling repository variations
 */

/**
 * Normalize a repository name by removing common suffixes and variations
 * @param repoName - Repository name in format "owner/repo" or "owner/repo.git"
 * @returns Normalized repository name without .git suffix
 */
export function normalizeRepositoryName(repoName: string): string {
	if (!repoName) return '';

	// Remove .git suffix if present
	return repoName.replace(/\.git$/, '');
}

/**
 * Extract repository name from GitHub URL and normalize it
 * @param url - GitHub URL (https://github.com/owner/<NAME_EMAIL>:owner/repo.git)
 * @returns Normalized repository name in "owner/repo" format
 */
export function extractAndNormalizeRepoFromUrl(url: string): string {
	if (!url) return '';

	// Handle different GitHub URL formats
	const patterns = [
		// HTTPS format: https://github.com/owner/repo(.git)?
		/github\.com\/([^\/]+\/[^\/]+?)(?:\.git)?(?:\/|$)/,
		// SSH format: **************:owner/repo(.git)?
		/git@github\.com:([^\/]+\/[^\/]+?)(?:\.git)?$/
	];

	for (const pattern of patterns) {
		const match = url.match(pattern);
		if (match) {
			return normalizeRepositoryName(match[1]);
		}
	}

	return '';
}

/**
 * Group agents by normalized repository name
 * @param agents - Array of agents with githubUrl property
 * @param getRepoFromAgent - Function to extract repo name from agent
 * @returns Map of normalized repo names to agent arrays
 */
export function groupAgentsByNormalizedRepo<T>(
	agents: T[],
	getRepoFromAgent: (agent: T) => string
): Map<string, T[]> {
	const repoGroups = new Map<string, T[]>();

	agents.forEach(agent => {
		const repoName = getRepoFromAgent(agent);
		if (repoName) {
			const normalizedRepo = normalizeRepositoryName(repoName);
			if (!repoGroups.has(normalizedRepo)) {
				repoGroups.set(normalizedRepo, []);
			}
			repoGroups.get(normalizedRepo)!.push(agent);
		}
	});

	return repoGroups;
}

/**
 * Get unique normalized repository names from agents
 * @param agents - Array of agents with githubUrl property
 * @param getRepoFromAgent - Function to extract repo name from agent
 * @returns Array of unique normalized repository names, sorted
 */
export function getUniqueNormalizedRepos<T>(
	agents: T[],
	getRepoFromAgent: (agent: T) => string
): string[] {
	const repoSet = new Set<string>();

	agents.forEach(agent => {
		const repoName = getRepoFromAgent(agent);
		if (repoName) {
			const normalizedRepo = normalizeRepositoryName(repoName);
			repoSet.add(normalizedRepo);
		}
	});

	return Array.from(repoSet).sort();
}

/**
 * Get repository counts by normalized name
 * @param agents - Array of agents with githubUrl property
 * @param getRepoFromAgent - Function to extract repo name from agent
 * @returns Record of normalized repo names to agent counts
 */
export function getNormalizedRepoCounts<T>(
	agents: T[],
	getRepoFromAgent: (agent: T) => string
): Record<string, number> {
	const counts: Record<string, number> = {};

	agents.forEach(agent => {
		const repoName = getRepoFromAgent(agent);
		if (repoName) {
			const normalizedRepo = normalizeRepositoryName(repoName);
			counts[normalizedRepo] = (counts[normalizedRepo] || 0) + 1;
		}
	});

	return counts;
}

/**
 * Check if an agent matches a normalized repository filter
 * @param agent - Agent to check
 * @param getRepoFromAgent - Function to extract repo name from agent
 * @param repositoryFilter - Repository filter string (normalized)
 * @returns True if agent matches the repository filter
 */
export function agentMatchesNormalizedRepo<T>(
	agent: T,
	getRepoFromAgent: (agent: T) => string,
	repositoryFilter: string
): boolean {
	if (!repositoryFilter) return true;

	const agentRepo = getRepoFromAgent(agent);
	if (!agentRepo) return false;

	const normalizedAgentRepo = normalizeRepositoryName(agentRepo);
	const normalizedFilter = normalizeRepositoryName(repositoryFilter);

	return normalizedAgentRepo === normalizedFilter;
}
