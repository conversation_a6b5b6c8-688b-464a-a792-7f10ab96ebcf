/**
 * Tests for agent title generation utilities
 */

import { describe, it, expect } from 'vitest';
import {
	getAgentTitleGenerationPrompt,
	extractGeneratedTitle,
	canGenerateTitle,
	createTitleGenerationContext
} from './agent-title-generation';

describe('Agent Title Generation', () => {
	describe('getAgentTitleGenerationPrompt', () => {
		it('should create a prompt with trigger information', () => {
			const context = {
				triggerName: 'Fix Bug',
				triggerDescription: 'Fix a bug in the codebase',
				triggerInstructions: 'Analyze the issue and provide a fix',
				entityTitle: 'Login Issue #123',
				entityType: 'issue',
				userInstructions: 'Fix the login problem'
			};

			const prompt = getAgentTitleGenerationPrompt(context);

			expect(prompt).toContain('Generate a concise, descriptive title');
			expect(prompt).toContain('3-7 words');
			expect(prompt).toContain('Fix Bug');
			expect(prompt).toContain('Login Issue #123');
			expect(prompt).toContain('<augment-agent-title>');
		});

		it('should handle minimal context', () => {
			const context = {
				userInstructions: 'Update the documentation'
			};

			const prompt = getAgentTitleGenerationPrompt(context);

			expect(prompt).toContain('Update the documentation');
			expect(prompt).toContain('<augment-agent-title>');
		});
	});

	describe('extractGeneratedTitle', () => {
		it('should extract title from properly formatted response', () => {
			const response = `
				Here's a good title for your agent:

				<augment-agent-title>Fix Login Bug</augment-agent-title>

				This title is concise and descriptive.
			`;

			const title = extractGeneratedTitle(response);
			expect(title).toBe('Fix Login Bug');
		});

		it('should handle whitespace around title', () => {
			const response = '<augment-agent-title>  Update Documentation  </augment-agent-title>';
			const title = extractGeneratedTitle(response);
			expect(title).toBe('Update Documentation');
		});

		it('should return null for malformed response', () => {
			const response = 'No tags here, just plain text';
			const title = extractGeneratedTitle(response);
			expect(title).toBeNull();
		});

		it('should return null for empty title', () => {
			const response = '<augment-agent-title></augment-agent-title>';
			const title = extractGeneratedTitle(response);
			expect(title).toBeNull();
		});

		it('should handle case insensitive tags', () => {
			const response = '<AUGMENT-AGENT-TITLE>Fix Bug</AUGMENT-AGENT-TITLE>';
			const title = extractGeneratedTitle(response);
			expect(title).toBe('Fix Bug');
		});
	});

	describe('canGenerateTitle', () => {
		it('should return true for valid context', () => {
			const context = {
				triggerName: 'Fix Bug'
			};
			expect(canGenerateTitle(context)).toBe(true);
		});

		it('should return true for user instructions only', () => {
			const context = {
				userInstructions: 'Update the documentation'
			};
			expect(canGenerateTitle(context)).toBe(true);
		});

		it('should return false for empty context', () => {
			const context = {};
			expect(canGenerateTitle(context)).toBe(false);
		});

		it('should return false for context with only empty strings', () => {
			const context = {
				triggerName: '',
				entityTitle: '',
				userInstructions: ''
			};
			expect(canGenerateTitle(context)).toBe(false);
		});
	});

	describe('createTitleGenerationContext', () => {
		it('should create context from entity and trigger', () => {
			const entity = {
				id: '123',
				title: 'Login Bug #456',
				type: 'issue',
				description: 'Users cannot log in',
				providerId: 'github',
				url: 'https://github.com/repo/issues/456'
			};

			const trigger = {
				id: 'trigger-1',
				name: 'Fix Bug',
				description: 'Fix bugs in the codebase',
				instructions: 'Analyze and fix the issue'
			};

			const context = createTitleGenerationContext(
				entity,
				trigger,
				'Please fix this login issue',
				{
					repositoryUrl: 'https://github.com/repo',
					branch: 'main'
				}
			);

			expect(context.triggerName).toBe('Fix Bug');
			expect(context.triggerDescription).toBe('Fix bugs in the codebase');
			expect(context.triggerInstructions).toBe('Analyze and fix the issue');
			expect(context.entityTitle).toBe('Login Bug #456');
			expect(context.entityType).toBe('issue');
			expect(context.entityDescription).toBe('Users cannot log in');
			expect(context.userInstructions).toBe('Please fix this login issue');
			expect(context.repositoryUrl).toBe('https://github.com/repo');
			expect(context.branch).toBe('main');
		});

		it('should handle undefined entity and trigger', () => {
			const context = createTitleGenerationContext(
				undefined,
				undefined,
				'Create a new feature'
			);

			expect(context.triggerName).toBeUndefined();
			expect(context.entityTitle).toBeUndefined();
			expect(context.userInstructions).toBe('Create a new feature');
		});
	});
});
