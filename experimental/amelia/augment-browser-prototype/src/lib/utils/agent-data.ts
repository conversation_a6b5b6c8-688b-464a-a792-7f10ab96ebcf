/**
 * Utility functions for working with agent data
 */

import { derived } from 'svelte/store';
import { convertChangedFileFromAPI, type CleanChangedFile } from '$lib/api/unified-client';
import { aggregateChangedFiles } from '$lib/utils/file-aggregation';
import { getChatExchanges } from '$lib/stores/global-state.svelte';
import {
	getChangedFilesAfterIndex,
	getChangedFilesFromExchange,
	getChangedFilesUpToIndex,
	findLatestPushOrPRCreation
} from '$lib/utils/remote-agent-utils';

/**
 * Get changed files for an agent from chat exchanges with proper aggregation
 */
export function getAgentChangedFilesWithDetails(agentId: string) {
	const chatExchangesStore = getChatExchanges(agentId);

	return derived(chatExchangesStore, (exchanges: any[]) => {
		if (!exchanges || exchanges.length === 0) return [];

		// Extract changed files from each exchange separately to maintain chronological order
		const exchangeChanges: any[][] = [];

		for (const exchange of exchanges) {
			if (exchange.changedFiles && Array.isArray(exchange.changedFiles)) {
				// Convert API format to CleanChangedFile format
				const convertedFiles = exchange.changedFiles.map(convertChangedFileFromAPI);
				exchangeChanges.push(convertedFiles);
			}
		}

		// Use the aggregation function to properly merge changes across exchanges
		return aggregateChangedFiles(exchangeChanges);
	});
}

/**
 * Get filtered changed files based on view type
 * @param agentId - Agent ID
 * @param viewIndex - View index (-1 = all, >= 0 = specific exchange)
 */
export function getFilteredChangedFiles(agentId: string, viewIndex: number) {
	const chatExchangesStore = getChatExchanges(agentId);

	return derived(chatExchangesStore, (exchanges: any[]) => {
		if (!exchanges || exchanges.length === 0) return [];

		// Extract changed files from each exchange separately
		const exchangeChanges: any[][] = [];
		for (const exchange of exchanges) {
			if (exchange.changedFiles && Array.isArray(exchange.changedFiles)) {
				const convertedFiles = exchange.changedFiles.map(convertChangedFileFromAPI);
				exchangeChanges.push(convertedFiles);
			}
		}

		// All changes (default)
		if (viewIndex === -1) {
			return aggregateChangedFiles(exchangeChanges);
		}

		// Changes from specific exchange
		if (viewIndex >= 0 && viewIndex < exchanges.length) {
			const exchange = exchanges[viewIndex];
			if (exchange.changedFiles && Array.isArray(exchange.changedFiles)) {
				return exchange.changedFiles.map(convertChangedFileFromAPI);
			}
		}

		return [];
	});
}

/**
 * Get raw exchanges for an agent
 */
export function getAgentRawExchanges(agentId: string) {
	return getChatExchanges(agentId);
}
