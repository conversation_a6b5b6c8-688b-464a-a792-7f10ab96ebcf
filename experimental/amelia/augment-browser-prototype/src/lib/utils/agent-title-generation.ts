/**
 * Agent title generation utilities
 *
 * This module provides utilities for generating meaningful titles for remote agents
 * using AI based on trigger context and entity information.
 */

import { sendSilentExchange } from '$lib/api/chat';
import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';

/**
 * Interface for title generation context
 */
export interface TitleGenerationContext {
	triggerName?: string;
	triggerDescription?: string;
	triggerInstructions?: string;
	entityTitle?: string;
	entityType?: string;
	entityDescription?: string;
	userInstructions?: string;
	repositoryUrl?: string;
	branch?: string;
}

/**
 * Creates the title generation prompt template
 * @param context - The context information for generating the title
 * @returns The formatted prompt for the AI title generation request
 */
export function getAgentTitleGenerationPrompt(context: TitleGenerationContext): string {
	const intro = [
		'Generate a concise, descriptive title for a remote agent based on the following context.',
		'The title should be sentence case and 3-7 words that clearly describe what the agent will do.',
		"Make it specific and actionable, avoiding generic terms like 'Agent' or 'Task' or 'Analyze'.",
		"The user will be creating many agents from this trigger, so don't describe the trigger but rather the specific work the agent needs to do and what entity it applies to.",
		'Be as specific as possible, so this title can be identified in a list of agents.',
		'The most important thing to include in the title is info about the entity (if there is one) and the task.',
		'',
		'Context:'
	];

	const contextParts = [
		context.triggerName && `Trigger: ${context.triggerName}`,
		context.triggerDescription && `Trigger Description: ${context.triggerDescription}`,
		context.triggerInstructions && `Trigger Instructions: ${context.triggerInstructions}`,
		context.userInstructions && `User Instructions: ${context.userInstructions}`,
		context.repositoryUrl && `Repository: ${context.repositoryUrl}`,
		context.branch && `Branch: ${context.branch}`,
		context.entityTitle &&
			`Entity (focus on this, always include this info): ${context.entityTitle} (${context.entityType || 'unknown type'})`,
		context.entityDescription && `Entity Description: ${context.entityDescription}`
	];

	const outro = [
		'',
		'IMPORTANT: Wrap your generated title between <augment-agent-title> and </augment-agent-title> tags.',
		'Put only the title inside these tags, nothing else.',
		'',
		'Examples of good titles:',
		'- Always load agents on settings page',
		'- PR review: auth flow changes',
		'- Add local dev instructions to README',
		'- Fix login flow race condition',
		'- Make settings page load faster'
	];

	return [...intro, ...contextParts, ...outro].filter(Boolean).join('\n');
}

/**
 * Extracts the generated title from the AI response
 * @param responseText - The full response text from the AI
 * @returns The extracted title, or null if parsing failed
 */
export function extractGeneratedTitle(responseText: string): string | null {
	const tagPattern = /<augment-agent-title>\s*([\s\S]*?)\s*<\/augment-agent-title>/i;
	const match = responseText.match(tagPattern);

	if (match && match[1]) {
		const extracted = match[1].trim();
		// Validate the title
		if (extracted.length > 0 && extracted.length <= 100) {
			return extracted;
		}
	}

	return null;
}

/**
 * Validates that a title generation context has sufficient information
 * @param context - The context to validate
 * @returns True if the context is sufficient for title generation
 */
export function canGenerateTitle(context: TitleGenerationContext): boolean {
	// Need at least one of these to generate a meaningful title
	return !!(
		context.triggerName ||
		context.triggerDescription ||
		context.triggerInstructions ||
		context.entityTitle ||
		context.userInstructions
	);
}

/**
 * Generates a title for a remote agent using AI
 * @param context - The context information for generating the title
 * @returns Promise that resolves to the generated title or null if generation failed
 */
export async function generateAgentTitle(context: TitleGenerationContext): Promise<string | null> {
	if (!canGenerateTitle(context)) {
		console.warn('Insufficient context for title generation');
		return null;
	}

	try {
		const prompt = getAgentTitleGenerationPrompt(context);
		const result = await sendSilentExchange({ message: prompt });

		const generatedTitle = extractGeneratedTitle(result.text);

		if (generatedTitle) {
			return generatedTitle;
		} else {
			console.warn('Failed to extract title from AI response');
			return null;
		}
	} catch (error) {
		console.error('Error generating agent title:', error);
		return null;
	}
}

/**
 * Creates title generation context from entity and trigger information
 * @param entity - The unified entity
 * @param trigger - The normalized trigger (optional)
 * @param userInstructions - User-provided instructions
 * @param options - Additional options
 * @returns The title generation context
 */
export function createTitleGenerationContext(
	entity?: UnifiedEntity,
	trigger?: NormalizedTrigger,
	userInstructions?: string,
	options: {
		repositoryUrl?: string;
		branch?: string;
	} = {}
): TitleGenerationContext {
	return {
		triggerName: trigger?.name,
		triggerDescription: trigger?.description,
		triggerInstructions: trigger?.instructions,
		entityTitle: entity?.title,
		entityType: entity?.entityType,
		entityDescription: entity?.description,
		userInstructions,
		repositoryUrl: options.repositoryUrl,
		branch: options.branch
	};
}
