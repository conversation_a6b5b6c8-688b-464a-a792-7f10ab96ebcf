/**
 * Utility functions for normalizing trigger data and execution payloads
 */

import type { Trigger, TriggerExecution as PrismaTriggerExecution } from '$lib/types';
import type { TriggerConditions } from '$lib/types/trigger-enums';
import { TriggerExecutionStatus } from '$lib/types/trigger-enums';
import type { TriggerExecution } from '$lib/api/unified-client';
import type { TriggerCondition, TriggerWorkspaceSetup } from '$lib/types';
import type { UnifiedEntity } from './entity-conversion';

/**
 * Entity type to prefix mapping for cleaning entity IDs
 */
const ENTITY_TYPE_PREFIXES: Record<string, string> = {
	GITHUB_ENTITY_TYPE_WORKFLOW_RUN: 'workflow-run-',
	GITHUB_ENTITY_TYPE_PULL_REQUEST: 'pull-request-',
	GITHUB_ENTITY_TYPE_ISSUE: 'issue-',
	LINEAR_ENTITY_TYPE_ISSUE: 'issue-'
};

/**
 * Normalized trigger structure for consistent UI display
 */
export interface NormalizedTrigger {
	id: string;
	name: string;
	description?: string;
	provider: string;
	entityType: string;
	conditionsSummary: string;
	isEnabled: boolean;

	executionCount: number;
	matchingEntityCount?: number; // Count of entities that match this trigger's conditions
	conditions: TriggerConditions;
	parsedConditions?: TriggerCondition[];
	parsedWorkspaceSetup?: TriggerWorkspaceSetup;
	createdAt: Date;
	updatedAt: Date;
	lastExecutionStatus: 'success' | 'error' | 'pending' | 'none';
	lastExecutionAt?: Date;
	executions: NormalizedExecution[];
	configuration: any; // Keep original configuration for advanced use
	originalTrigger: Trigger; // Keep reference to original trigger
	projectId?: string; // Project ID for filtering
}

/**
 * Normalized execution structure with cleaned entity data
 */
export interface NormalizedExecution extends Omit<TriggerExecution, 'entity'> {
	cleanEntityId?: string;
	entityType?: string;
	entity?: UnifiedEntity;
}

/**
 * Extract and clean entity ID from execution payload
 */
export function extractCleanEntityId(execution: TriggerExecution): {
	entityId?: string;
	entityType?: string;
} {
	try {
		const payload = execution.eventPayload ? JSON.parse(execution.eventPayload) : {};
		const rawEntityId = payload.entity_id;
		const entityType = payload.entity_type;

		if (!rawEntityId || !entityType) {
			return {};
		}

		// Remove entity type prefix if it exists
		// replace `pr-`, `pull-request-`, `workflow-run-`, `issue-` with ''
		const prefixes = ['pr-', 'pull-request-', 'workflow-run-', 'issue-'];
		const cleanEntityId = rawEntityId.replace(new RegExp(`^(${prefixes.join('|')})`), '');

		return {
			entityId: cleanEntityId,
			entityType
		};
	} catch (error) {
		console.warn('Failed to extract clean entity ID:', error);
		return {};
	}
}

/**
 * Normalize a single execution with cleaned entity data
 */
export function normalizeExecution(execution: TriggerExecution): NormalizedExecution {
	const { entityId, entityType } = extractCleanEntityId(execution);

	return {
		...execution,
		cleanEntityId: entityId,
		entityType
	};
}

/**
 * Normalize multiple executions with cleaned entity data
 */
export function normalizeExecutions(executions: TriggerExecution[]): NormalizedExecution[] {
	return executions.map(normalizeExecution);
}

/**
 * Get provider from trigger configuration
 */
function getProviderFromTrigger(trigger: any): string {
	// Parse conditions from JSON string or use direct object
	let parsedConditions: any = {};
	try {
		const conditions = trigger.conditions || trigger.configuration?.conditions;
		if (typeof conditions === 'string') {
			parsedConditions = JSON.parse(conditions);
		} else if (conditions && typeof conditions === 'object') {
			parsedConditions = conditions;
		}
	} catch (e) {
		console.warn('Failed to parse trigger conditions:', e);
	}

	// Check event_source first (for sample data with object structure)
	const eventSource = parsedConditions?.event_source as any;
	if (eventSource && typeof eventSource === 'object' && eventSource.type) {
		return eventSource.type;
	}

	// Check event_source enum (for real backend data)
	if (typeof eventSource === 'number') {
		switch (eventSource) {
			case 1:
				return 'github';
			case 2:
				return 'linear';
			case 3:
				return 'jira';
			case 4:
				return 'schedule';
			default:
				return 'unknown';
		}
	}

	// Check conditions structure (fallback)
	if (parsedConditions?.github) return 'github';
	if (parsedConditions?.linear) return 'linear';
	if (parsedConditions?.jira) return 'jira';
	if (parsedConditions?.schedule) return 'schedule';

	return 'unknown';
}

/**
 * Extract entity type from trigger conditions
 */
function getEntityTypeFromTrigger(trigger: any): string {
	// Parse conditions from JSON string or use direct object
	let parsedConditions: any = {};
	try {
		const conditions = trigger.conditions || trigger.configuration?.conditions;
		if (typeof conditions === 'string') {
			parsedConditions = JSON.parse(conditions);
		} else if (conditions && typeof conditions === 'object') {
			parsedConditions = conditions;
		}
	} catch (e) {
		console.warn('Failed to parse trigger conditions:', e);
		return 'unknown';
	}

	// Extract entity type from provider conditions
	if (parsedConditions?.github?.entityType !== undefined) {
		const entityType = parsedConditions.github.entityType;
		switch (entityType) {
			case 1:
				return 'pull_request';
			case 2:
				return 'workflow_run';
			case 3:
				return 'issue';
			default:
				return 'unknown';
		}
	}

	if (parsedConditions?.linear?.entityType !== undefined) {
		const entityType = parsedConditions.linear.entityType;
		switch (entityType) {
			case 1:
				return 'issue';
			default:
				return 'unknown';
		}
	}

	// Fallback: try to infer from conditions structure
	if (parsedConditions?.github?.pullRequest) return 'pull_request';
	if (parsedConditions?.github?.workflowRun) return 'workflow_run';
	if (parsedConditions?.linear?.issue) return 'issue';

	return 'unknown';
}

/**
 * Get entity type and conditions summary from trigger
 */
function getConditionsSummary(trigger: any): string {
	// Parse conditions from JSON string or use direct object
	let parsedConditions: any = {};
	try {
		const conditions = trigger.conditions || trigger.configuration?.conditions;
		if (typeof conditions === 'string') {
			parsedConditions = JSON.parse(conditions);
		} else if (conditions && typeof conditions === 'object') {
			parsedConditions = conditions;
		}
	} catch (e) {
		console.warn('Failed to parse trigger conditions:', e);
	}

	// Check event_source first (for sample data with object structure)
	const eventSource = parsedConditions?.event_source as any;
	if (
		eventSource &&
		typeof eventSource === 'object' &&
		eventSource.type &&
		eventSource.entity_type
	) {
		const provider = eventSource.type;
		const entityType = eventSource.entity_type;
		return `${provider.charAt(0).toUpperCase() + provider.slice(1)} ${entityType}`;
	}

	// Check event_source enum (for real backend data)
	if (typeof eventSource === 'number') {
		switch (eventSource) {
			case 1:
				return 'GitHub Events';
			case 2:
				return 'Linear Events';
			case 3:
				return 'Jira Events';
			default:
				return 'Unknown Events';
		}
	}

	// Check conditions structure (fallback)
	if (!parsedConditions) return 'No conditions';

	const parts = [];
	if (parsedConditions.github) {
		if (parsedConditions.github.pull_request) {
			parts.push('Pull Requests');
		}
		if (parsedConditions.github.workflow_run) {
			parts.push('Workflow Runs');
		}
	}
	if (parsedConditions.linear) {
		parts.push('Linear Issues');
	}
	if (parsedConditions.jira) {
		parts.push('Jira Issues');
	}

	return parts.length > 0 ? parts.join(', ') : 'Custom conditions';
}

/**
 * Get last execution status for a trigger
 */
function getLastExecutionStatus(trigger: any): 'success' | 'error' | 'pending' | 'none' {
	// Use actual execution status if available
	const lastExecutionStatus = trigger.lastExecutionStatus || trigger.last_execution_status;
	if (lastExecutionStatus !== undefined) {
		switch (lastExecutionStatus) {
			case TriggerExecutionStatus.TRIGGER_EXECUTION_STATUS_SUCCESS:
				return 'success';
			case TriggerExecutionStatus.TRIGGER_EXECUTION_STATUS_FAILED:
			case TriggerExecutionStatus.TRIGGER_EXECUTION_STATUS_CANCELLED:
				return 'error';
			case TriggerExecutionStatus.TRIGGER_EXECUTION_STATUS_PENDING:
			case TriggerExecutionStatus.TRIGGER_EXECUTION_STATUS_RUNNING:
				return 'pending';
			default:
				return 'none';
		}
	}

	// Fallback logic
	if (!trigger.configuration?.enabled) return 'none';
	if ((trigger.executionCount || trigger.execution_count || 0) > 0) return 'success';
	return 'none';
}

/**
 * Normalize a single trigger for consistent UI display
 */
export function normalizeTrigger(trigger: any): NormalizedTrigger {
	const provider = getProviderFromTrigger(trigger);
	const entityType = getEntityTypeFromTrigger(trigger);
	const conditionsSummary = getConditionsSummary(trigger);
	const lastExecutionStatus = getLastExecutionStatus(trigger);

	return {
		id: trigger.triggerId || trigger.trigger_id, // Handle both API formats
		name: trigger.configuration?.name || 'Unnamed Filter',
		description: trigger.configuration?.description,
		provider,
		entityType,
		conditionsSummary,
		isEnabled: trigger.isEnabled ?? trigger.configuration?.enabled ?? trigger.enabled ?? false,
		executionCount: trigger.executionCount || trigger.execution_count || 0,
		matchingEntityCount: trigger.matchingEntityCount,
		conditions: trigger.configuration?.conditions || { type: 1 },
		parsedConditions: trigger.configuration?.conditions
			? transformConditionsToLocal(trigger.configuration.conditions)
			: undefined,
		parsedWorkspaceSetup: trigger.configuration?.agent_config?.workspaceSetup
			? transformWorkspaceSetupToLocal(trigger.configuration.agent_config.workspaceSetup)
			: undefined,
		createdAt: new Date(trigger.createdAt || trigger.created_at),
		updatedAt: new Date(trigger.updatedAt || trigger.updated_at),
		lastExecutionStatus,
		lastExecutionAt:
			trigger.lastExecutionAt || trigger.last_execution_at
				? new Date(trigger.lastExecutionAt || trigger.last_execution_at)
				: undefined,
		executions: [],
		configuration: trigger.configuration, // Keep original for advanced use
		originalTrigger: trigger, // Keep reference to original trigger
		projectId: trigger.projectId || trigger.project_id
	};
}

/**
 * Normalize multiple triggers for consistent UI display
 */
export function normalizeTriggers(triggers: any[]): NormalizedTrigger[] {
	return triggers.map(normalizeTrigger);
}

/**
 * Enhanced execution normalization that includes entity mapping
 */
export function enhanceExecutionsWithEntities(
	executions: TriggerExecution[],
	entityMap: Map<string, UnifiedEntity>
): NormalizedExecution[] {
	return executions.map((execution) => {
		const normalized = normalizeExecution(execution);

		// Try to attach entity if we have a clean entity ID
		if (normalized.cleanEntityId && entityMap.has(normalized.cleanEntityId)) {
			normalized.entity = entityMap.get(normalized.cleanEntityId);
		}

		return normalized;
	});
}

// Helper functions to transform backend data to local UI format
export function transformConditionsToLocal(conditions: TriggerConditions): TriggerCondition[] {
	const localConditions: TriggerCondition[] = [];

	if (conditions.github?.pull_request) {
		const pr = conditions.github.pull_request;
		if (pr.author) {
			localConditions.push({
				field: 'github.pull_request.author',
				operator: 'equals',
				value: pr.author
			});
		}
		if (pr.reviewer) {
			localConditions.push({
				field: 'github.pull_request.reviewer',
				operator: 'equals',
				value: pr.reviewer
			});
		}
		if (pr.repository) {
			localConditions.push({
				field: 'github.pull_request.repository',
				operator: 'equals',
				value: pr.repository
			});
		}
		if (pr.assignee) {
			localConditions.push({
				field: 'github.pull_request.assignee',
				operator: 'equals',
				value: pr.assignee
			});
		}
		if (pr.labels && pr.labels.length > 0) {
			localConditions.push({
				field: 'github.pull_request.labels',
				operator: 'contains',
				value: pr.labels
			});
		}
	}

	if (conditions.github?.workflow_run) {
		const wr = conditions.github.workflow_run;
		if (wr.actor) {
			localConditions.push({
				field: 'github.workflow_run.actor',
				operator: 'equals',
				value: wr.actor
			});
		}
		if (wr.conclusion) {
			localConditions.push({
				field: 'github.workflow_run.conclusion',
				operator: 'equals',
				value: wr.conclusion
			});
		}
		if (wr.status) {
			localConditions.push({
				field: 'github.workflow_run.status',
				operator: 'equals',
				value: wr.status
			});
		}
		if (wr.repository) {
			localConditions.push({
				field: 'github.workflow_run.repository',
				operator: 'equals',
				value: wr.repository
			});
		}
	}

	if (conditions.linear?.issue) {
		const issue = conditions.linear.issue;
		if (issue.creator) {
			localConditions.push({
				field: 'linear.issue.creator',
				operator: 'equals',
				value: issue.creator
			});
		}
		if (issue.assignee) {
			localConditions.push({
				field: 'linear.issue.assignee',
				operator: 'equals',
				value: issue.assignee
			});
		}
		if (issue.team) {
			localConditions.push({
				field: 'linear.issue.team',
				operator: 'equals',
				value: issue.team
			});
		}
		if (issue.states && issue.states.length > 0) {
			localConditions.push({
				field: 'linear.issue.states',
				operator: 'in',
				value: issue.states
			});
		}
		if (issue.priorities && issue.priorities.length > 0) {
			localConditions.push({
				field: 'linear.issue.priorities',
				operator: 'in',
				value: issue.priorities
			});
		}
	}

	return localConditions;
}

function transformWorkspaceSetupToLocal(workspaceSetup: any): TriggerWorkspaceSetup {
	return {
		githubRef:
			workspaceSetup.startingFiles?.githubRef?.url || workspaceSetup.startingFiles?.github_ref?.url,
		url:
			workspaceSetup.startingFiles?.githubRef?.url || workspaceSetup.startingFiles?.github_ref?.url,
		ref:
			workspaceSetup.startingFiles?.githubRef?.ref ||
			workspaceSetup.startingFiles?.github_ref?.ref ||
			'main',
		patch: workspaceSetup.patch,
		setupScript: workspaceSetup.setupScript || workspaceSetup.setup_script,
		apiUrl: workspaceSetup.apiUrl || workspaceSetup.api_url
	};
}
