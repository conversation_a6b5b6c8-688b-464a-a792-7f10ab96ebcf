/**
 * Session utilities for getting session data from server-side cookies
 * This replaces direct access to client-side session stores for security
 */

export interface SessionData {
	tenantUrl: string;
	accessToken: string;
	expiresAt?: number;
	isAuthenticated: boolean;
}

/**
 * Get session data from server-side cookies
 * This is the secure way to access session data after moving to server-side storage
 */
export async function getSessionData(cookies?: any): Promise<SessionData | null> {
	const sessionDataRaw = cookies.get('augment_session') || null;

	if (!sessionDataRaw) return null;
	const sessionData = JSON.parse(sessionDataRaw);

	if (!sessionData || !sessionData.accessToken) {
		throw new Error('No authenticated session found. Please log in first.');
	}

	// Check if token is expired
	if (sessionData.expiresAt && Date.now() > sessionData.expiresAt) {
		throw new Error('Access token has expired. Please log in again.');
	}

	return {
		tenantUrl: sessionData.tenantUrl,
		accessToken: sessionData.accessToken,
		expiresAt: sessionData.expiresAt,
		isAuthenticated: true
	};
}

/**
 * SECURITY NOTICE: Direct token access functions have been removed for security reasons.
 *
 * These functions exposed access tokens to client-side JavaScript, which defeats the purpose
 * of httpOnly cookies and creates XSS vulnerabilities.
 *
 * Use the following secure alternatives instead:
 * - For GitHub API calls: Use /api/github/* proxy endpoints
 * - For Linear API calls: Use /api/linear/* proxy endpoints
 * - For server-side code: Use the secure API clients in src/lib/server/api-client.ts
 */

/**
 * @deprecated Use server-side proxy endpoints instead of direct token access
 */
export async function getGitHubAccessToken(): Promise<string | null> {
	console.warn(
		'getGitHubAccessToken is deprecated for security reasons. Use /api/github/* proxy endpoints instead.'
	);
	return null;
}

/**
 * @deprecated Use server-side proxy endpoints instead of direct token access
 */
export async function getLinearAccessToken(): Promise<string | null> {
	console.warn(
		'getLinearAccessToken is deprecated for security reasons. Use /api/linear/* proxy endpoints instead.'
	);
	return null;
}
