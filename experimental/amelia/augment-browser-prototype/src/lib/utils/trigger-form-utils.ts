/**
 * Trigger Form Utilities
 *
 * Utilities for handling trigger form logic, condition management,
 * and backend API format conversion.
 */

import type { EntityType } from '$lib/types';
import { urlToRepository } from './project-repository';
import cronstrue from 'cronstrue/i18n';

// ============================================================================
// ENUMS
// ============================================================================

// Event sources
export enum EventSource {
	EVENT_SOURCE_UNSPECIFIED = 0,
	EVENT_SOURCE_GITHUB = 1,
	EVENT_SOURCE_LINEAR = 2,
	EVENT_SOURCE_JIRA = 3,
	EVENT_SOURCE_SCHEDULE = 4
}

// GitHub entity types
export enum GitHubEntityType {
	GITHUB_ENTITY_TYPE_UNSPECIFIED = 0,
	GITHUB_ENTITY_TYPE_PULL_REQUEST = 1,
	GITHUB_ENTITY_TYPE_WORKFLOW_RUN = 2,
	GITHUB_ENTITY_TYPE_ISSUE = 3
}

// Linear entity types
export enum LinearEntityType {
	LINEAR_ENTITY_TYPE_UNSPECIFIED = 0,
	LINEAR_ENTITY_TYPE_ISSUE = 1
}

// Trigger condition types
export enum TriggerConditionType {
	TRIGGER_CONDITION_TYPE_UNSPECIFIED = 0,
	TRIGGER_CONDITION_GITHUB = 1,
	TRIGGER_CONDITION_LINEAR = 2,
	TRIGGER_CONDITION_SCHEDULE = 3
}

// ============================================================================
// TRIGGER CONFIGURATION TYPES
// ============================================================================

// GitHub Pull Request trigger conditions
export interface GitHubPullRequestTriggerConditions {
	author?: string;
	assignee?: string;
	reviewer?: string;
	base_branch?: string;
	head_branch?: string;
	repository?: string;
	title_contains?: string;
	labels?: string[];
	activity_types?: string[];
	draft?: boolean;
}

// GitHub Workflow Run trigger conditions
export interface GitHubWorkflowRunTriggerConditions {
	actor?: string;
	event?: string;
	status?: string;
	conclusion?: string;
	branch?: string;
	repository?: string;
}

// GitHub Issue trigger conditions
export interface GitHubIssueTriggerConditions {
	author?: string;
	assignee?: string;
	repository?: string;
	title_contains?: string;
	labels?: string[];
	activity_types?: string[];
	state?: string;
}

// GitHub trigger conditions
export interface GitHubTriggerConditions {
	entity_type: GitHubEntityType;
	pull_request?: GitHubPullRequestTriggerConditions;
	workflow_run?: GitHubWorkflowRunTriggerConditions;
	issue?: GitHubIssueTriggerConditions;
}

// Linear Issue trigger conditions
export interface LinearIssueTriggerConditions {
	creator?: string;
	assignee?: string;
	team?: string;
	project?: string;
	title_contains?: string;
	activity_types?: string[];
	min_estimate?: number;
	max_estimate?: number;
	states?: string[];
	state_types?: string[];
	labels?: string[];
	priorities?: number[];
}

// Linear trigger conditions
export interface LinearTriggerConditions {
	entity_type: LinearEntityType;
	issue?: LinearIssueTriggerConditions;
}

// Schedule trigger conditions
export interface ScheduleTriggerConditions {
	cron_expression: string;
	timezone?: string;
	start_date?: string; // ISO 8601
	end_date?: string; // ISO 8601
	description?: string;
}

// Main trigger conditions interface
export interface TriggerConditions {
	type: TriggerConditionType;
	github?: GitHubTriggerConditions;
	linear?: LinearTriggerConditions;
	schedule?: ScheduleTriggerConditions;
}

// Agent configuration interface
export interface AgentConfig {
	user_guidelines: string;
	workspace_guidelines?: string;
	setup_script?: string;
	workspace_setup?: {
		starting_files?: {
			github_ref?: {
				url: string;
				repository?: string;
				ref: string;
			};
		};
	};
	starting_nodes?: any[];
	/** Prefix version to use for agent prompts (optional, defaults to latest) */
	prefix_version?: string;
}

// Main trigger configuration interface
export interface TriggerConfiguration {
	name: string;
	description?: string;
	event_source: EventSource;
	conditions: TriggerConditions;
	agent_config: AgentConfig;
	enabled?: boolean;
}

// Form data interface for the UI
export interface TriggerFormData {
	name: string;
	description?: string;
	userGuidelines: string;
	workspaceGuidelines?: string;
	setupScript?: string;
	repositoryUrl?: string;
	branchSelection: string;
}

// Unified condition interface
export interface Condition {
	type: string;
	value?: string;
}

// Condition option interface for UI
export interface ConditionOption {
	value: string;
	label: string;
	description: string;
	needsValue?: boolean;
	placeholder?: string;
	hint?: string;
	shortcut?: boolean;
	shortcutValue?: string;
	isHiddenFromConditionsBuilders?: boolean;
}

export type ConditionConfig = {
	value: string;
	label: string;
	description: string;
	placeholder?: string;
	hint?: string;
	shortcutValues?: { label: string; value: string }[];
	options?: { label: string; value: string }[];
	width?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full'; // Form field width based on expected content length
	isHiddenFromConditionsBuilders?: boolean;
	// Backend mapping configuration
	backendMapping?: {
		field: string; // The backend field name
		transform?: (value: string) => any; // Optional value transformation
		staticValue?: any; // Static value to set (ignores condition value)
		arrayField?: boolean; // Whether this should be added to an array field
	};
};

type ProviderId = 'github' | 'linear' | 'schedule';

const conditionsConfig: Record<ProviderId, Partial<Record<EntityType, ConditionConfig[]>>> = {
	github: {
		pull_request: [
			// Author conditions
			{
				value: 'repository',
				label: 'Repository...',
				description: 'PRs in specific repository',
				hint: 'Leave empty to monitor all repositories',
				width: 'lg', // Repository names can be long (org/repo-name)
				backendMapping: {
					field: 'repository'
				},
				isHiddenFromConditionsBuilders: true
			},
			{
				value: 'author',
				label: 'Authored by...',
				description: 'PRs I created',
				hint: 'Filter by PR author (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Authored by me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'author'
				}
			},
			{
				value: 'assignee',
				label: 'Assigned to...',
				description: 'PRs assigned to me',
				hint: 'Filter by PR assignee (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Assigned to me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'assignee'
				}
			},
			{
				value: 'reviewer',
				label: 'Requested review from...',
				description: 'PRs where I am a reviewer',
				hint: 'Filter by PR reviewer (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Requested review from me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'reviewer'
				}
			},
			{
				value: 'base_branch',
				label: 'Targeting branch...',
				description: 'PRs targeting specific branch',
				hint: 'Target branch for the PR',
				width: 'md', // Branch names are typically medium length
				shortcutValues: [
					{
						label: 'Targeting main branch',
						value: 'main,master'
					}
				],
				backendMapping: {
					field: 'baseBranch'
				}
			},
			{
				value: 'head_branch',
				label: 'From branch...',
				description: 'PRs from specific branch pattern',
				hint: 'Source branch pattern (e.g., feature/*, bugfix/*)',
				width: 'lg', // Branch patterns can be longer with wildcards
				backendMapping: {
					field: 'headBranch'
				}
			},
			{
				value: 'activity_types',
				label: 'Activity type...',
				description: 'PRs with specific activity types',
				hint: 'Activity types (opened, synchronize, ready_for_review, closed)',
				width: 'lg', // Multiple activity types can be selected
				shortcutValues: [
					{
						label: 'Newly opened',
						value: 'opened'
					},
					{
						label: 'Recently updated',
						value: 'synchronize'
					},
					{
						label: 'Ready for review',
						value: 'ready_for_review'
					},
					{
						label: 'Recently closed',
						value: 'closed'
					}
				],
				options: [
					{ label: 'Opened', value: 'opened' },
					{ label: 'Synchronize', value: 'synchronize' },
					{ label: 'Ready for review', value: 'ready_for_review' },
					{ label: 'Review requested', value: 'review_requested' },
					{ label: 'Closed', value: 'closed' },
					{ label: 'PR Bot check suite failure', value: 'pr_bot_check_suite_failure' },
					{ label: 'PR Bot status failure', value: 'pr_bot_status_failure' }
				],
				backendMapping: {
					field: 'activityTypes',
					arrayField: true
				},
				isHiddenFromConditionsBuilders: true
			},
			{
				value: 'title_contains',
				label: 'Title contains...',
				description: 'PRs with specific title',
				hint: 'Filter by PR title (case-insensitive substring match)',
				width: 'xl', // PR titles can be quite long
				backendMapping: {
					field: 'title_contains'
				}
			},
			{
				value: 'labels',
				label: 'With label...',
				description: 'PRs with specific label',
				hint: 'Filter by PR label (all must be present)',
				width: 'md', // Labels are typically short to medium
				shortcutValues: [
					{
						label: 'Labelled as a Bug',
						value: 'bug'
					},
					{
						label: 'Labelled as a Feature',
						value: 'feature'
					},
					{
						label: 'Labelled as Urgent',
						value: 'urgent'
					},
					{
						label: 'Labelled Augment',
						value: 'augment'
					}
				],
				backendMapping: {
					field: 'labels',
					arrayField: true
				}
			},
			{
				value: 'draft',
				label: 'Draft PRs',
				description: 'PRs that are still a work in progress',
				hint: 'Filter by PR draft status',
				width: 'sm', // Boolean-like field, small width
				backendMapping: {
					field: 'draft'
				}
			}
		],
		workflow_run: [
			// Actor conditions
			{
				value: 'repository',
				label: 'Repository...',
				description: 'Workflows in specific repository',
				hint: 'Leave empty to monitor all repositories',
				width: 'lg', // Repository names can be long (org/repo-name)
				backendMapping: {
					field: 'repository'
				},
				isHiddenFromConditionsBuilders: true
			},
			{
				value: 'actor',
				label: 'Triggered by...',
				description: 'Workflows triggered by me',
				hint: 'Filter by workflow actor (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Triggered by me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'actor'
				}
			},
			{
				value: 'triggering_actor',
				label: 'Triggered for...',
				description: 'Workflows triggered for me',
				hint: 'Filter by workflow triggering actor (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Triggered for me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'triggering_actor'
				}
			},
			{
				value: 'event',
				label: 'Event type...',
				description: 'Workflows triggered by specific event',
				hint: 'Filter by workflow event type (push, pull_request, schedule)',
				width: 'md', // Event types are short to medium
				shortcutValues: [
					{
						label: 'On push events',
						value: 'push'
					},
					{
						label: 'On PR events',
						value: 'pull_request'
					}
				],
				options: [
					{ label: 'Push', value: 'push' },
					{ label: 'Pull Request', value: 'pull_request' },
					{ label: 'Schedule', value: 'schedule' },
					{ label: 'Pull Request review comment', value: 'pull_request_review_comment' },
					{ label: 'Pull Request review', value: 'pull_request_review' },
					{ label: 'Check suite', value: 'check_suite' }
				],
				backendMapping: {
					field: 'event'
				}
			},
			{
				value: 'status',
				label: 'Status...',
				description: 'Workflows with specific status',
				hint: 'Filter by workflow status (queued, in_progress, completed)',
				width: 'md', // Status values are short to medium
				shortcutValues: [
					{
						label: 'Completed workflows',
						value: 'completed'
					},
					{
						label: 'In progress workflows',
						value: 'in_progress'
					}
				],
				options: [
					{ label: 'Queued', value: 'queued' },
					{ label: 'In Progress', value: 'in_progress' },
					{ label: 'Completed', value: 'completed' }
				],
				backendMapping: {
					field: 'status'
				}
			},
			{
				value: 'conclusion',
				label: 'Conclusion...',
				description: 'Workflows with specific conclusion',
				hint: 'Filter by workflow conclusion (success, failure, cancelled, skipped)',
				width: 'md', // Conclusion values are short to medium
				shortcutValues: [
					{
						label: 'Failed workflows',
						value: 'failure'
					},
					{
						label: 'Successful workflows',
						value: 'success'
					}
				],
				options: [
					{ label: 'Success', value: 'success' },
					{ label: 'Failure', value: 'failure' },
					{ label: 'Cancelled', value: 'cancelled' },
					{ label: 'Skipped', value: 'skipped' }
				],
				backendMapping: {
					field: 'conclusion'
				}
			},
			{
				value: 'branch',
				label: 'Branch...',
				description: 'Workflows from specific branch',
				hint: 'Filter by workflow branch',
				width: 'md', // Branch names are typically medium length
				shortcutValues: [
					{
						label: 'On main branch',
						value: 'main,master'
					}
				],
				backendMapping: {
					field: 'branch'
				}
			},
			{
				value: 'title_contains',
				label: 'Title contains...',
				description: 'Workflows with specific title',
				hint: 'Filter by workflow title (case-insensitive substring match)',
				width: 'xl', // Workflow titles can be quite long
				backendMapping: {
					field: 'title_contains'
				}
			}
		]
	},
	linear: {
		issue: [
			// Team conditions
			{
				value: 'team',
				label: 'Team...',
				description: 'Issues in specific team',
				hint: 'Filter by Linear team',
				width: 'md', // Team names are typically medium length
				shortcutValues: [
					{
						label: 'In my team',
						value: '@my_team'
					}
				],
				backendMapping: {
					field: 'team'
				}
			},
			{
				value: 'project',
				label: 'Project...',
				description: 'Issues in specific project',
				hint: 'Filter by Linear project',
				width: 'lg', // Project names can be long
				backendMapping: {
					field: 'project'
				}
			},
			{
				value: 'assignee',
				label: 'Assigned to...',
				description: 'Issues assigned to me',
				hint: 'Filter by Linear assignee (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Assigned to me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'assignee'
				}
			},
			{
				value: 'creator',
				label: 'Created by...',
				description: 'Issues created by',
				hint: 'Filter by Linear creator (@me for yourself)',
				width: 'md', // Usernames are typically medium length
				shortcutValues: [
					{
						label: 'Created by me',
						value: '@me'
					}
				],
				backendMapping: {
					field: 'creator'
				}
			},
			{
				value: 'labels',
				label: 'With label...',
				description: 'Issues with specific label',
				hint: 'Filter by Linear label (all must be present)',
				width: 'md', // Labels are typically short to medium
				shortcutValues: [
					{
						label: 'Labelled as a Bug',
						value: 'bug'
					},
					{
						label: 'Labelled as a Feature',
						value: 'feature'
					},
					{
						label: 'Labelled as Urgent',
						value: 'urgent'
					},
					{
						label: 'Labelled as Augment',
						value: 'augment'
					}
				],
				backendMapping: {
					field: 'labels',
					arrayField: true
				}
			},
			{
				value: 'priority',
				label: 'Priority...',
				description: 'Issues with specific priority',
				hint: 'Filter by Linear priority (1=urgent, 4=low)',
				width: 'sm', // Priority values are short
				options: [
					{ label: 'Urgent', value: '1' },
					{ label: 'High', value: '2' },
					{ label: 'Medium', value: '3' },
					{ label: 'Low', value: '4' },
					{ label: 'Higher priority', value: '1,2' }
				],
				backendMapping: {
					field: 'priority'
				}
			},
			{
				value: 'states',
				label: 'State...',
				description: 'Issues in specific state',
				hint: 'Filter by Linear state (name or type)',
				width: 'md', // State values are short to medium
				shortcutValues: [
					{
						label: 'In Progress',
						value: 'in_progress'
					},
					{
						label: 'Completed',
						value: 'completed'
					},
					{
						label: 'Backlog',
						value: 'backlog'
					}
				],
				options: [
					{ label: 'Backlog', value: 'backlog' },
					{ label: 'Unstarted', value: 'unstarted' },
					{ label: 'Started', value: 'started' },
					{ label: 'Completed', value: 'completed' },
					{ label: 'Canceled', value: 'canceled' }
				],
				backendMapping: {
					field: 'states',
					arrayField: true
				}
			},
			{
				value: 'state_types',
				label: 'State type...',
				description: 'Issues by state type',
				hint: 'Filter by Linear state type (unstarted, started, completed, backlog)',
				width: 'md', // State type values are short to medium
				shortcutValues: [
					{
						label: 'Todo',
						value: 'unstarted'
					},
					{
						label: 'In progress',
						value: 'started'
					},
					{
						label: 'Done',
						value: 'completed'
					},
					{
						label: 'Backlog',
						value: 'backlog'
					}
				],
				options: [
					{ label: 'Backlog', value: 'backlog' },
					{ label: 'Unstarted', value: 'unstarted' },
					{ label: 'Started', value: 'started' },
					{ label: 'Completed', value: 'completed' }
				],
				backendMapping: {
					field: 'state_types',
					arrayField: true
				}
			},
			{
				value: 'title_contains',
				label: 'Title contains...',
				description: 'Issues with specific title',
				hint: 'Filter by Linear title (case-insensitive substring match)',
				width: 'xl', // Issue titles can be quite long
				backendMapping: {
					field: 'titleContains'
				}
			},
			{
				value: 'activity_types',
				label: 'Activity type...',
				description: 'Issues with specific activity types',
				hint: 'Activity types (created, updated, completed, archived)',
				width: 'lg', // Multiple activity types can be selected
				shortcutValues: [
					{
						label: 'Newly created',
						value: 'created'
					},
					{
						label: 'Recently updated',
						value: 'updated'
					}
				],
				options: [
					{ label: 'Created', value: 'created' },
					{ label: 'Updated', value: 'updated' },
					{ label: 'Completed', value: 'completed' },
					{ label: 'Archived', value: 'archived' }
				],
				backendMapping: {
					field: 'activity_types',
					arrayField: true
				},
				isHiddenFromConditionsBuilders: true
			}
		]
	},
	schedule: {
		schedule: [
			{
				value: 'cronExpression',
				label: 'Cron Expression...',
				description: 'Schedule using cron syntax',
				placeholder: '0 9 * * * (daily at 9 AM)',
				hint: 'Use cron syntax: minute hour day month weekday',
				width: 'lg',
				backendMapping: {
					field: 'cronExpression'
				}
			},
			{
				value: 'timezone',
				label: 'Timezone...',
				description: 'Timezone for schedule',
				placeholder: 'UTC, America/New_York',
				hint: 'Timezone for the scheduled execution',
				width: 'md',
				backendMapping: {
					field: 'timezone'
				}
			},
			{
				value: 'startDate',
				label: 'Start Date...',
				description: 'When to start executing this schedule',
				placeholder: '2024-01-01T00:00:00Z',
				hint: 'Optional start date in ISO format',
				width: 'lg',
				backendMapping: {
					field: 'startDate'
				}
			},
			{
				value: 'endDate',
				label: 'End Date...',
				description: 'When to stop executing this schedule',
				placeholder: '2024-12-31T23:59:59Z',
				hint: 'Optional end date in ISO format',
				width: 'lg',
				backendMapping: {
					field: 'endDate'
				}
			},
			{
				value: 'description',
				label: 'Description...',
				description: 'Human-readable schedule description',
				placeholder: 'Every weekday at 9 AM',
				hint: 'Optional description of the schedule',
				width: 'lg',
				backendMapping: {
					field: 'description'
				}
			}
		]
	}
};
/**
 * Get condition configuration for a specific provider and entity type
 */
export function getConditionConfigForProvider(
	providerId: ProviderId,
	entityType: EntityType
): ConditionConfig[] {
	return conditionsConfig[providerId]?.[entityType] || [];
}

/**
 * Convert condition config to UI options for the simple conditions builder
 */
export function getConditionOptionsFromConfig(
	providerId: ProviderId,
	entityType: EntityType
): ConditionOption[] {
	const config = getConditionConfigForProvider(providerId, entityType);
	const options: ConditionOption[] = [];

	for (const conditionConfig of config) {
		// Add shortcut options first
		if (conditionConfig.shortcutValues) {
			for (const shortcut of conditionConfig.shortcutValues) {
				options.push({
					value: conditionConfig.value,
					label: shortcut.label,
					description: conditionConfig.description,
					hint: conditionConfig.hint,
					shortcut: true,
					shortcutValue: shortcut.value,
					isHiddenFromConditionsBuilders: conditionConfig.isHiddenFromConditionsBuilders
				});
			}
		}

		// Add the main option with value input
		options.push({
			value: conditionConfig.value,
			label: conditionConfig.label,
			description: conditionConfig.description,
			needsValue: true,
			placeholder: conditionConfig.placeholder,
			hint: conditionConfig.hint,
			isHiddenFromConditionsBuilders: conditionConfig.isHiddenFromConditionsBuilders
		});

		// Add predefined options if available
		if (conditionConfig.options) {
			for (const option of conditionConfig.options) {
				options.push({
					value: conditionConfig.value,
					label: option.label,
					description: conditionConfig.description,
					hint: conditionConfig.hint,
					shortcut: true,
					shortcutValue: option.value,
					isHiddenFromConditionsBuilders: conditionConfig.isHiddenFromConditionsBuilders
				});
			}
		}
	}

	return options;
}

/**
 * Get condition options for a specific provider and entity type combination (legacy function)
 * @deprecated Use getConditionOptionsFromConfig instead
 */
export function getConditionOptions(selectedCombo: string): ConditionOption[] {
	const [provider, entityType] = selectedCombo.split('-');
	return getConditionOptionsFromConfig(provider as ProviderId, entityType as EntityType);
}

/**
 * Get default instructions based on provider and entity type
 */
export function getDefaultInstructions(provider: string, entityType: string): string {
	const defaults: Record<string, Record<string, string>> = {
		github: {
			pull_request:
				'When a new pull request is created or updated, review the code changes for:\n\n• Code quality and best practices\n• Potential bugs or security issues\n• Performance implications\n• Documentation completeness\n• Test coverage\n\nProvide constructive feedback and suggestions for improvement.',
			workflow_run:
				'When a workflow run completes, analyze the results and:\n\n• Check for build failures or test failures\n• Review deployment status\n• Identify performance regressions\n• Suggest fixes for any issues found\n• Update relevant documentation if needed',
			issue:
				'When a new issue is created or updated:\n\n• Analyze the issue description and requirements\n• Suggest potential solutions or approaches\n• Identify related code areas that might need changes\n• Provide implementation guidance\n• Help prioritize and categorize the issue'
		},
		linear: {
			issue:
				'When a Linear issue is created or updated:\n\n• Review the issue requirements and acceptance criteria\n• Break down complex tasks into smaller subtasks\n• Suggest technical approaches and implementation strategies\n• Identify dependencies and potential blockers\n• Provide estimates and timeline considerations'
		}
	};

	return (
		defaults[provider]?.[entityType] ||
		'Analyze the incoming event and provide relevant assistance based on the context and requirements.'
	);
}

/**
 * Convert string values to arrays for fields that should be arrays
 */
export function convertStringFieldsToArrays(
	data: Record<string, any>,
	providerId: ProviderId,
	entityType: EntityType
): Record<string, any> {
	const result = { ...data };
	const config = getConditionConfigForProvider(providerId, entityType);

	// Find all fields that should be arrays
	const arrayFields = config
		.filter((c) => c.backendMapping?.arrayField)
		.map((c) => c.backendMapping!.field);

	// Convert string values to arrays for array fields
	for (const field of arrayFields) {
		if (result[field] !== undefined && typeof result[field] === 'string') {
			result[field] = [result[field]];
		}
	}

	return result;
}

/**
 * Convert conditions to backend API format using conditionsConfig as source of truth
 */
export function conditionsToBackendFormat(
	conditions: Condition[],
	selectedProvider: string,
	triggerType: string,
	repo?: string
): any {
	const result: any = {};
	const providerId = selectedProvider as ProviderId;
	const entityType = triggerType as EntityType;

	// Get the condition config for this provider and entity type
	const conditionConfigs = getConditionConfigForProvider(providerId, entityType);

	// Set repository for GitHub (from project or conditions)
	if (selectedProvider === 'github') {
		result.repository = repo || '';

		// Check if repository is overridden in conditions
		const repoCondition = conditions.find((c) => c.type === 'repository');
		if (repoCondition?.value) {
			result.repository = repoCondition.value;
		}
	}

	// Process each condition using the config
	if (Array.isArray(conditions)) {
		conditions.forEach((condition) => {
			const { type, value } = condition;

			// Find the config for this condition type
			const config = conditionConfigs.find((c) => c.value === type);

			if (!config?.backendMapping) {
				return; // Skip conditions without backend mapping
			}

			const { field, transform, staticValue, arrayField } = config.backendMapping;

			// Determine the value to use
			let finalValue: any;
			if (staticValue !== undefined) {
				finalValue = staticValue;
			} else if (transform) {
				finalValue = transform(value || '');
			} else {
				finalValue = value || '';
			}

			// Set the field value
			if (arrayField) {
				// Handle array fields
				if (!result[field]) {
					result[field] = [];
				}
				if (Array.isArray(finalValue)) {
					result[field].push(...finalValue);
				} else {
					result[field].push(finalValue);
				}
			} else {
				result[field] = finalValue;
			}
		});
	}

	// Set defaults if not specified (these could also be moved to conditionsConfig)
	if (selectedProvider === 'github' && triggerType === 'pull_request') {
		result.activityTypes = result.activityTypes || ['opened', 'synchronize', 'ready_for_review'];
	} else if (selectedProvider === 'github' && triggerType === 'workflow_run') {
		result.status = result.status || 'completed';
		result.conclusion = result.conclusion || 'failure';
	} else if (selectedProvider === 'linear' && triggerType === 'issue') {
		result.linearActivityTypes = result.linearActivityTypes || ['created', 'updated'];
	}

	// Convert string fields to arrays where needed
	const convertedResult = convertStringFieldsToArrays(result, providerId, entityType);

	return convertedResult;
}

/**
 * Map frontend field names to backend field names using conditionsConfig as source of truth
 */
export function mapFieldsToBackend(updates: any): any {
	const backendUpdates = { ...updates };

	// Handle the common isEnabled -> enabled mapping
	if ('isEnabled' in updates) {
		backendUpdates.enabled = updates.isEnabled;
		delete backendUpdates.isEnabled;
	}

	// TODO: Add more field mappings from conditionsConfig if needed
	// This could be expanded to use conditionsConfig for all field mappings
	// by iterating through all condition configs and their backendMapping.field values

	return backendUpdates;
}

/**
 * Convert conditions from backend format to our unified format using conditionsConfig
 */
export function backendToConditions(
	backendData: any,
	selectedProvider: string,
	triggerType: string
): Condition[] {
	const result: Condition[] = [];

	// Handle nested backend structure - extract the actual conditions data
	let conditionsData = backendData;
	if (backendData && backendData[selectedProvider]) {
		// Try both snake_case and camelCase versions of the trigger type
		const snakeCaseType = triggerType; // e.g., 'pull_request'
		const camelCaseType = triggerType.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()); // e.g., 'pullRequest'

		if (backendData[selectedProvider][snakeCaseType]) {
			conditionsData = backendData[selectedProvider][snakeCaseType];
		} else if (backendData[selectedProvider][camelCaseType]) {
			conditionsData = backendData[selectedProvider][camelCaseType];
		}
	}

	// Get the condition configuration for this provider/entity type
	const config = getConditionConfigForProvider(
		selectedProvider as ProviderId,
		triggerType as EntityType
	);

	// Process each field based on the configuration
	for (const fieldConfig of config) {
		const backendMapping = fieldConfig.backendMapping;
		if (!backendMapping) continue;

		const backendFieldName = backendMapping.field;
		const backendValue = conditionsData[backendFieldName];

		// Skip if no value exists for this field or if it's empty
		if (backendValue === undefined || backendValue === null || backendValue === '') {
			continue;
		}

		// Skip empty arrays
		if (Array.isArray(backendValue) && backendValue.length === 0) {
			continue;
		}

		// Handle array fields (like activity_types, labels)
		if (backendMapping.arrayField && Array.isArray(backendValue)) {
			// For array fields, join the values with commas
			result.push({
				type: fieldConfig.value,
				value: backendValue.join(',')
			});
		} else if (Array.isArray(backendValue)) {
			// If backend value is array but not marked as arrayField, take first value
			result.push({
				type: fieldConfig.value,
				value: backendValue[0]?.toString() || ''
			});
		} else {
			// For regular fields, use the value directly
			result.push({
				type: fieldConfig.value,
				value: backendValue.toString()
			});
		}
	}

	// Return default condition if no conditions were found
	return result.length > 0 ? result : [{ type: 'assignee', value: '@me' }];
}

/**
 * Convert trigger configuration conditions to unified conditions format
 * This handles the specific structure from trigger configuration objects
 */
export function triggerConfigToConditions(
	triggerConfig: any,
	selectedProvider: string,
	triggerType: string
): Condition[] {
	console.log('triggerConfigToConditions called with:', {
		triggerConfig,
		selectedProvider,
		triggerType
	});

	const result: Condition[] = [];

	if (!triggerConfig?.conditions) {
		console.log('No conditions found in trigger config');
		return [{ type: 'assignee', value: '@me' }];
	}

	// Extract the provider-specific conditions
	let providerConditions = triggerConfig.conditions[selectedProvider];
	if (!providerConditions) {
		console.log(`No ${selectedProvider} conditions found`);
		return [{ type: 'assignee', value: '@me' }];
	}

	// Extract the entity-specific conditions
	let entityConditions;
	if (selectedProvider === 'github') {
		// Try both camelCase and snake_case
		entityConditions =
			providerConditions.pullRequest ||
			providerConditions.pull_request ||
			providerConditions.workflowRun ||
			providerConditions.workflow_run;
	} else if (selectedProvider === 'linear') {
		entityConditions = providerConditions.issue;
	} else if (selectedProvider === 'schedule') {
		// For schedule triggers, the entire providerConditions object is the entity conditions
		entityConditions = providerConditions;
	}

	if (!entityConditions) {
		console.log(`No ${triggerType} conditions found for ${selectedProvider}`);
		return [{ type: 'assignee', value: '@me' }];
	}

	console.log('Found entity conditions:', entityConditions);

	// Get the condition configuration for this provider/entity type
	const config = getConditionConfigForProvider(
		selectedProvider as ProviderId,
		triggerType as EntityType
	);

	// Process each field based on the configuration
	for (const fieldConfig of config) {
		const backendMapping = fieldConfig.backendMapping;
		if (!backendMapping) continue;

		const backendFieldName = backendMapping.field;
		const backendValue = entityConditions[backendFieldName];

		console.log(`Checking field ${backendFieldName}:`, backendValue);

		// Skip if no value exists for this field or if it's empty
		if (backendValue === undefined || backendValue === null || backendValue === '') {
			continue;
		}

		// Skip empty arrays
		if (Array.isArray(backendValue) && backendValue.length === 0) {
			continue;
		}

		// Handle array fields (like activity_types, labels)
		if (backendMapping.arrayField && Array.isArray(backendValue)) {
			// For array fields, join the values with commas
			result.push({
				type: fieldConfig.value,
				value: backendValue.join(',')
			});
			console.log(`Added array condition: ${fieldConfig.value} = ${backendValue.join(',')}`);
		} else if (Array.isArray(backendValue)) {
			// If backend value is array but not marked as arrayField, take first value
			result.push({
				type: fieldConfig.value,
				value: backendValue[0]?.toString() || ''
			});
			console.log(`Added first-of-array condition: ${fieldConfig.value} = ${backendValue[0]}`);
		} else {
			// For regular fields, use the value directly
			result.push({
				type: fieldConfig.value,
				value: backendValue.toString()
			});
			console.log(`Added regular condition: ${fieldConfig.value} = ${backendValue}`);
		}
	}

	console.log('Final converted conditions:', result);

	return result;
}

/**
 * Create trigger config for fetching entities
 */
export function createTriggerConfigForFetching(
	conditions: Condition[],
	selectedProvider: string,
	triggerType: string
): any {
	const backendConditions = conditionsToBackendFormat(conditions, selectedProvider, triggerType);

	if (selectedProvider === 'github') {
		// For matching entities endpoint, only send event_source and conditions
		return {
			event_source: 1, // GitHub
			conditions: {
				type: 1,
				github: {
					entity_type: triggerType === 'pull_request' ? 1 : 2,
					...(triggerType === 'pull_request'
						? {
								pull_request: {
									// Ensure repository is in owner/repo format, not full URL
									repository:
										urlToRepository(backendConditions.repository || '') || 'augmentcode/augment',
									...(backendConditions.author && { author: backendConditions.author }),
									...(backendConditions.assignee && { assignee: backendConditions.assignee }),
									...(backendConditions.reviewer && { reviewer: backendConditions.reviewer }),
									...(backendConditions.baseBranch && {
										base_branch: backendConditions.baseBranch
									}),
									...(backendConditions.headBranch && {
										head_branch: backendConditions.headBranch
									}),
									...(backendConditions.activityTypes &&
										backendConditions.activityTypes.length > 0 && {
											activity_types: backendConditions.activityTypes
										})
								}
							}
						: {
								workflow_run: {
									// Ensure repository is in owner/repo format, not full URL
									repository:
										urlToRepository(backendConditions.repository || '') || 'augmentcode/augment',
									...(backendConditions.actor && { actor: backendConditions.actor }),
									...(backendConditions.event && { event: backendConditions.event }),
									...(backendConditions.status && { status: backendConditions.status }),
									...(backendConditions.conclusion && { conclusion: backendConditions.conclusion }),
									...(backendConditions.branch && { branch: backendConditions.branch })
								}
							})
				}
			}
		};
	} else if (selectedProvider === 'linear') {
		const parseStringArray = (str: string): string[] =>
			str
				? str
						.split(',')
						.map((s) => s.trim())
						.filter((s) => s.length > 0)
				: [];
		const parseNumberArray = (str: string): number[] =>
			str
				? str
						.split(',')
						.map((s) => parseInt(s.trim()))
						.filter((n) => !isNaN(n))
				: [];

		// For matching entities endpoint, only send event_source and conditions
		return {
			event_source: 2, // Linear
			conditions: {
				type: 2,
				linear: {
					entity_type: 1, // LINEAR_ENTITY_TYPE_ISSUE
					issue: {
						...(backendConditions.team && { team: backendConditions.team }),
						...(backendConditions.creator && { creator: backendConditions.creator }),
						...(backendConditions.assignee && { assignee: backendConditions.assignee }),
						...(backendConditions.project && { project: backendConditions.project }),
						...(backendConditions.titleContains && {
							title_contains: backendConditions.titleContains
						}),
						...(backendConditions.minEstimate !== undefined && {
							min_estimate: backendConditions.minEstimate
						}),
						...(backendConditions.maxEstimate !== undefined && {
							max_estimate: backendConditions.maxEstimate
						}),
						...(backendConditions.states && { states: parseStringArray(backendConditions.states) }),
						...(backendConditions.stateTypes && {
							state_types: parseStringArray(backendConditions.stateTypes)
						}),
						...(backendConditions.labels && { labels: parseStringArray(backendConditions.labels) }),
						...(backendConditions.priorities && {
							priorities: parseNumberArray(backendConditions.priorities)
						}),
						...(backendConditions.linearActivityTypes && {
							activity_types: backendConditions.linearActivityTypes
						})
					}
				}
			}
		};
	}

	// Fallback for unsupported providers
	return {
		event_source: 1,
		conditions: {
			type: 1,
			github: {
				entity_type: 1,
				pull_request: {
					repository: 'augmentcode/augment'
				}
			}
		}
	};
}

/**
 * Convert conditions to backend format using the centralized config
 */
export function convertConditionsToBackendUsingConfig(
	conditions: Condition[],
	providerId: ProviderId,
	entityType: EntityType,
	projectGitHubRepo?: string
): Record<string, any> {
	const result: Record<string, any> = {};
	const config = getConditionConfigForProvider(providerId, entityType);

	// Set repository for GitHub (from project or conditions)
	if (providerId === 'github' && projectGitHubRepo) {
		result.repository = projectGitHubRepo;
	}

	// Process each condition using the config
	if (Array.isArray(conditions)) {
		conditions.forEach((condition) => {
			const conditionConfig = config.find((c) => c.value === condition.type);
			if (!conditionConfig?.backendMapping) {
				return; // Skip conditions without backend mapping
			}

			const mapping = conditionConfig.backendMapping;
			const value = condition.value || mapping.staticValue;

			if (mapping.transform && condition.value) {
				result[mapping.field] = mapping.transform(condition.value);
			} else if (mapping.arrayField) {
				if (!result[mapping.field]) {
					result[mapping.field] = [];
				}
				if (value !== undefined) {
					result[mapping.field].push(value);
				}
			} else if (value !== undefined) {
				result[mapping.field] = value;
			}
		});
	}

	return result;
}

/**
 * Create a complete trigger configuration using conditionsConfig as source of truth
 */
export function createTriggerConfiguration(
	conditions: Condition[],
	selectedProvider: string,
	triggerType: string,
	formData: TriggerFormData,
	triggerEnabled: boolean = true,
	projectGitHubRepo?: string
): TriggerConfiguration {
	const backendConditions = conditionsToBackendFormat(conditions, selectedProvider, triggerType);

	// Build the base configuration
	const config: TriggerConfiguration = {
		name: formData.name,
		description: formData.description,
		event_source:
			selectedProvider === 'github'
				? EventSource.EVENT_SOURCE_GITHUB
				: selectedProvider === 'linear'
					? EventSource.EVENT_SOURCE_LINEAR
					: EventSource.EVENT_SOURCE_SCHEDULE,
		conditions: buildTriggerConditions(
			selectedProvider,
			triggerType,
			backendConditions,
			projectGitHubRepo
		),
		agent_config: buildAgentConfig(formData, backendConditions, projectGitHubRepo),
		enabled: triggerEnabled
	};

	return config;
}

/**
 * Build trigger conditions based on provider and type
 */
function buildTriggerConditions(
	selectedProvider: string,
	triggerType: string,
	backendConditions: any,
	projectGitHubRepo?: string
): TriggerConditions {
	const providerId = selectedProvider as ProviderId;
	const entityType = triggerType as EntityType;
	const config = getConditionConfigForProvider(providerId, entityType);

	if (selectedProvider === 'github') {
		const entityTypeValue =
			triggerType === 'pull_request'
				? GitHubEntityType.GITHUB_ENTITY_TYPE_PULL_REQUEST
				: GitHubEntityType.GITHUB_ENTITY_TYPE_WORKFLOW_RUN;

		const githubConditions: GitHubTriggerConditions = {
			entity_type: entityTypeValue
		};

		// Build the entity-specific conditions using config mapping
		const entityConditions: Record<string, any> = {};

		// Set repository from project or conditions
		entityConditions.repository = backendConditions.repository || projectGitHubRepo || '';

		// Map all other fields using the configuration
		config.forEach((fieldConfig) => {
			if (fieldConfig.backendMapping) {
				const backendFieldName = fieldConfig.backendMapping.field;
				const backendValue = backendConditions[backendFieldName];

				if (backendValue !== undefined) {
					const { field, transform, staticValue } = fieldConfig.backendMapping;

					let value;
					if (staticValue !== undefined) {
						value = staticValue;
					} else if (transform) {
						value = transform(backendValue);
					} else {
						value = backendValue;
					}

					// Only add non-empty values
					if (value !== undefined && value !== null && value !== '') {
						// Handle array fields that need length check
						if (Array.isArray(value) && value.length === 0) {
							return;
						}
						entityConditions[field] = value;
					}
				}
			}
		});

		// Assign to the correct entity type
		if (triggerType === 'pull_request') {
			githubConditions.pull_request = entityConditions;
		} else {
			githubConditions.workflow_run = entityConditions;
		}

		return {
			type: TriggerConditionType.TRIGGER_CONDITION_GITHUB,
			github: githubConditions
		};
	} else if (selectedProvider === 'linear') {
		// Linear
		const linearConditions: LinearTriggerConditions = {
			entity_type: LinearEntityType.LINEAR_ENTITY_TYPE_ISSUE,
			issue: {}
		};

		// Map all fields using the configuration
		config.forEach((fieldConfig) => {
			if (fieldConfig.backendMapping) {
				const backendFieldName = fieldConfig.backendMapping.field;
				const backendValue = backendConditions[backendFieldName];

				if (backendValue !== undefined) {
					const { field, transform, staticValue } = fieldConfig.backendMapping;

					let value;
					if (staticValue !== undefined) {
						value = staticValue;
					} else if (transform) {
						value = transform(backendValue);
					} else {
						value = backendValue;
					}

					// Only add non-empty values
					if (value !== undefined && value !== null && value !== '') {
						// Handle array fields that need length check
						if (Array.isArray(value) && value.length === 0) {
							return;
						}
						(linearConditions.issue as any)[field] = value;
					}
				}
			}
		});

		return {
			type: TriggerConditionType.TRIGGER_CONDITION_LINEAR,
			linear: linearConditions
		};
	} else {
		// Schedule
		const scheduleConditions: ScheduleTriggerConditions = {
			cron_expression: backendConditions.cronExpression || '0 9 * * *', // Default to 9 AM daily
			timezone: backendConditions.timezone || null, // Always include timezone field, even if null
			start_date: backendConditions.startDate || null, // Always include start_date field, even if null
			end_date: backendConditions.endDate || null, // Always include end_date field, even if null
			description: cronstrue.toString(backendConditions.cronExpression) || null
		};

		return {
			type: TriggerConditionType.TRIGGER_CONDITION_SCHEDULE,
			schedule: scheduleConditions
		};
	}
}

/**
 * Build agent configuration
 */
function buildAgentConfig(
	formData: TriggerFormData,
	backendConditions: any,
	projectGitHubRepo?: string
): AgentConfig {
	// Determine the repository to use
	const repository = backendConditions.repository || projectGitHubRepo || 'augmentcode/augment';

	// Determine the URL to use - ensure consistency with repository
	let url: string;
	if (formData.repositoryUrl) {
		url = formData.repositoryUrl;
	} else {
		url = `https://github.com/${repository}`;
	}

	// Extract repository from URL if needed to ensure consistency
	let finalRepository = repository;
	if (formData.repositoryUrl) {
		const urlMatch = formData.repositoryUrl.match(/github\.com\/([^\/]+\/[^\/]+)/);
		if (urlMatch) {
			finalRepository = urlMatch[1];
		}
	}

	return {
		user_guidelines: formData.userGuidelines,
		...(formData.workspaceGuidelines && { workspace_guidelines: formData.workspaceGuidelines }),
		...(formData.setupScript && { setup_script: formData.setupScript }),
		starting_nodes: [],
		workspace_setup: {
			starting_files: {
				github_ref: {
					url: url,
					repository: finalRepository,
					ref: formData.branchSelection
				}
			}
		}
	};
}
