import { get } from 'svelte/store';
import type { TriggerExecution } from '$lib/api/unified-client';
import { globalState } from '$lib/stores/global-state.svelte';

export interface ExecutionGroup {
	title: string;
	items: TriggerExecution[];
	emptyMessage?: string;
}

/**
 * Groups executions into active and deleted agent categories
 */
export function groupExecutionsByAgentStatus(executions: TriggerExecution[]): ExecutionGroup[] {
	const agentsMap = get(globalState).agents;
	// Note: deletedAgentIds functionality removed - all agents are now in the main store

	const activeExecutions: TriggerExecution[] = [];
	const deletedExecutions: TriggerExecution[] = [];

	executions.forEach((execution) => {
		if (!execution.remoteAgentId) {
			// Skip executions without remoteAgentId entirely
			return;
		}

		// Check if this is an optimistic execution (temporary agent ID created during UI optimistic updates)
		// Optimistic executions use agent IDs that start with 'optimistic-agent-' prefix
		const isOptimisticExecution = execution.remoteAgentId.startsWith('optimistic-agent-');

		// Check if agent exists in store
		const agentExists = agentsMap[execution.remoteAgentId];

		// Check if this is a very recent execution (within last 5 minutes)
		// This handles the case where a real agent was just created but hasn't been added to store yet
		const executionTime = new Date(execution.createdAt).getTime();
		const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
		const isRecentExecution = executionTime > fiveMinutesAgo;

		if (agentExists || isOptimisticExecution || isRecentExecution) {
			// Agent exists in store OR it's an optimistic execution OR it's a recent execution - treat as active
			activeExecutions.push(execution);
		} else {
			// Agent has an ID but doesn't exist in store, isn't optimistic, and isn't recent
			// This is likely an old execution from a deleted agent - treat as deleted
			deletedExecutions.push(execution);
		}
	});

	return [
		{
			title: 'Active Agents',
			items: activeExecutions,
			emptyMessage: 'No agents working at the moment'
		},
		{
			title: 'Deleted Agents',
			items: deletedExecutions,
			emptyMessage: 'None yet'
		}
	];
}

/**
 * Groups executions with a simple active/deleted split, filtering out empty groups
 */
export function groupExecutionsByAgentStatusFiltered(
	executions: TriggerExecution[]
): ExecutionGroup[] {
	const groups = groupExecutionsByAgentStatus(executions);

	// Only return groups that have items
	// Active agents group is always included (even if empty) to show the main content
	// Deleted agents group is only included if it has items
	return groups.filter(
		(group) =>
			group.title === 'Active Agents' ||
			(group.title === 'Deleted Agents' && group.items.length > 0)
	);
}
