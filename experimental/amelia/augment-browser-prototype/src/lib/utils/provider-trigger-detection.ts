/**
 * Provider Trigger Detection Utilities
 *
 * Utilities for detecting when users have triggers for providers that aren't configured
 */

import type { ProviderAuthStatus } from '$lib/stores/provider-auth';

// Simple trigger interface for provider detection
interface TriggerForProviderDetection {
	id: string;
	provider: string;
	name: string;
}

export interface UnconfiguredProviderInfo {
	providerId: string;
	providerName: string;
	triggerCount: number;
	triggers: TriggerForProviderDetection[];
}

/**
 * Detect providers that have triggers but are not configured
 */
export function detectUnconfiguredProvidersWithTriggers(
	triggers: TriggerForProviderDetection[],
	providerAuthStates: Record<string, ProviderAuthStatus>
): UnconfiguredProviderInfo[] {
	// Group triggers by provider
	const triggersByProvider = new Map<string, TriggerForProviderDetection[]>();

	triggers.forEach((trigger) => {
		const providerId = trigger.provider;
		if (!triggersByProvider.has(providerId)) {
			triggersByProvider.set(providerId, []);
		}
		triggersByProvider.get(providerId)!.push(trigger);
	});

	// Find providers with triggers that are not configured
	const unconfiguredProviders: UnconfiguredProviderInfo[] = [];

	triggersByProvider.forEach((providerTriggers, providerId) => {
		const authStatus = providerAuthStates[providerId];

		// Check if provider is not configured but has triggers
		if (authStatus && !authStatus.isConfigured && providerTriggers.length > 0) {
			unconfiguredProviders.push({
				providerId,
				providerName: authStatus.name,
				triggerCount: providerTriggers.length,
				triggers: providerTriggers
			});
		}
	});

	return unconfiguredProviders;
}

/**
 * Check if a specific provider has triggers but is not configured
 */
export function hasTriggersForUnconfiguredProvider(
	providerId: string,
	triggers: TriggerForProviderDetection[],
	providerAuthStates: Record<string, ProviderAuthStatus>
): boolean {
	const authStatus = providerAuthStates[providerId];
	if (!authStatus || authStatus.isConfigured) {
		return false;
	}

	return triggers.some((trigger) => trigger.provider === providerId);
}
