import type { ConditionFilters } from '$lib/utils/condition-filters';

export interface FilterFieldConfig {
	key: keyof ConditionFilters;
	label: string;
	type: 'text' | 'number' | 'select' | 'multiselect';
	placeholder?: string;
	options?: Array<{ value: string; label: string }>;
}

/**
 * GitHub Pull Request filter field configurations
 */
export const githubPullRequestFields: FilterFieldConfig[] = [
	{
		key: 'prRepository',
		label: 'Repository',
		type: 'text',
		placeholder: 'owner/repo (leave empty for all)'
	},
	{
		key: 'prAuthor',
		label: 'Author',
		type: 'text',
		placeholder: '@me, username'
	},
	{
		key: 'prAssignee',
		label: 'Assignee',
		type: 'text',
		placeholder: '@me, username'
	},
	{
		key: 'prReviewer',
		label: 'Reviewer',
		type: 'text',
		placeholder: '@me, username'
	},
	{
		key: 'prBaseBranch',
		label: 'Base Branch',
		type: 'text',
		placeholder: 'main, develop'
	},
	{
		key: 'prHeadBranch',
		label: 'Head Branch',
		type: 'text',
		placeholder: 'feature/*'
	}
];

/**
 * GitHub Workflow Run filter field configurations
 */
export const githubWorkflowRunFields: FilterFieldConfig[] = [
	{
		key: 'workflowRepository',
		label: 'Repository',
		type: 'text',
		placeholder: 'owner/repo (leave empty for all)'
	},
	{
		key: 'workflowActor',
		label: 'Actor',
		type: 'text',
		placeholder: '@me, username'
	},
	{
		key: 'workflowEvent',
		label: 'Trigger Event',
		type: 'text',
		placeholder: 'push, pull_request'
	},
	{
		key: 'workflowStatus',
		label: 'Status',
		type: 'select',
		options: [
			{ value: '', label: 'All' },
			{ value: 'queued', label: 'Queued' },
			{ value: 'in_progress', label: 'In Progress' },
			{ value: 'completed', label: 'Completed' }
		]
	},
	{
		key: 'workflowConclusion',
		label: 'Conclusion',
		type: 'select',
		options: [
			{ value: '', label: 'All' },
			{ value: 'success', label: 'Success' },
			{ value: 'failure', label: 'Failure' },
			{ value: 'cancelled', label: 'Cancelled' },
			{ value: 'skipped', label: 'Skipped' }
		]
	},
	{
		key: 'workflowBranch',
		label: 'Branch',
		type: 'text',
		placeholder: 'main, develop'
	}
];

/**
 * Linear Issue filter field configurations
 */
export const linearIssueFields: FilterFieldConfig[] = [
	{
		key: 'linearTeam',
		label: 'Team',
		type: 'text',
		placeholder: 'AUG, ENG'
	},
	{
		key: 'linearCreator',
		label: 'Creator',
		type: 'text',
		placeholder: '@me, username'
	},
	{
		key: 'linearAssignee',
		label: 'Assignee',
		type: 'text',
		placeholder: '@me, username'
	},
	{
		key: 'linearProject',
		label: 'Project',
		type: 'text',
		placeholder: 'Project name'
	},
	{
		key: 'linearTitleContains',
		label: 'Title Contains',
		type: 'text',
		placeholder: 'bug, feature, urgent'
	},
	{
		key: 'linearActivityTypes',
		label: 'Activity Types',
		type: 'multiselect',
		options: [
			{ value: 'created', label: 'Created' },
			{ value: 'updated', label: 'Updated' },
			{ value: 'completed', label: 'Completed' },
			{ value: 'archived', label: 'Archived' }
		]
	},
	{
		key: 'linearMinEstimate',
		label: 'Min Estimate',
		type: 'number',
		placeholder: '1'
	},
	{
		key: 'linearMaxEstimate',
		label: 'Max Estimate',
		type: 'number',
		placeholder: '8'
	},
	{
		key: 'linearStates',
		label: 'States',
		type: 'text',
		placeholder: 'In Progress, Review'
	},
	{
		key: 'linearStateTypes',
		label: 'State Types',
		type: 'text',
		placeholder: 'started, completed'
	},
	{
		key: 'linearLabels',
		label: 'Labels',
		type: 'text',
		placeholder: 'bug, urgent, frontend'
	},
	{
		key: 'linearPriorities',
		label: 'Priorities',
		type: 'text',
		placeholder: '0, 1, 2'
	}
];

/**
 * Get filter field configurations for a specific provider and entity type
 */
export function getFilterFieldsForEntity(
	provider: string,
	entityType: string
): FilterFieldConfig[] {
	if (provider === 'github' && entityType === 'pull_request') {
		return githubPullRequestFields;
	}
	if (provider === 'github' && entityType === 'workflow_run') {
		return githubWorkflowRunFields;
	}
	if (provider === 'linear' && entityType === 'issue') {
		return linearIssueFields;
	}
	return [];
}

/**
 * Format entity type display name
 */
export function formatEntityTypeDisplayName(entityType: string): string {
	return entityType.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase());
}
