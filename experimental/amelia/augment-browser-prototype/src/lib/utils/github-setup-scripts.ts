import { apiClient } from '$lib/api/unified-client';
import type {
	GithubSetupScript,
	ListGithubSetupScriptsResponse,
	ReadGithubSetupScriptResponse
} from '$lib/types';

/**
 * Parse GitHub reference string into components
 * @param githubRef - Format: "owner/repo@ref" or "owner/repo"
 */
export function parseGithubRef(githubRef: string): {
	owner: string;
	repo: string;
	ref: string;
	isValid: boolean;
} {
	const match = githubRef.match(/^([^/]+)\/([^@]+)(?:@(.+))?$/);

	if (!match) {
		return { owner: '', repo: '', ref: 'main', isValid: false };
	}

	const [, owner, repo, ref = 'main'] = match;
	return { owner, repo, ref, isValid: true };
}

/**
 * Format GitHub reference from components
 */
export function formatGithubRef(owner: string, repo: string, ref: string = 'main'): string {
	return `${owner}/${repo}@${ref}`;
}

/**
 * Extract GitHub reference from repository URL and branch
 */
export function createGithubRefFromRepoAndBranch(
	repositoryUrl: string,
	branch: string
): string | null {
	// Extract owner/repo from GitHub URL
	const match = repositoryUrl.match(/github\.com[/:]([\w-]+)\/([\w.-]+)(?:\.git)?/);
	if (!match) return null;

	const [, owner, repo] = match;
	return formatGithubRef(owner, repo, branch);
}

/**
 * Validate GitHub reference format
 */
export function isValidGithubRef(githubRef: string): boolean {
	return parseGithubRef(githubRef).isValid;
}

/**
 * List available setup scripts from a GitHub repository
 */
export async function listGithubSetupScripts(githubRef: string): Promise<{
	scripts: GithubSetupScript[];
	error?: string;
}> {
	try {
		console.log('Listing GitHub setup scripts for:', githubRef);

		if (!isValidGithubRef(githubRef)) {
			console.error('Invalid GitHub reference format:', githubRef);
			return { scripts: [], error: 'Invalid GitHub reference format' };
		}

		const response = await apiClient.githubSetupScripts.list(githubRef);

		if (response.errorMessage) {
			console.error('GitHub setup scripts error:', response.errorMessage);
			return { scripts: [], error: response.errorMessage };
		}

		// Handle both old format (scripts array) and new format (script_paths array)
		let scripts: GithubSetupScript[] = [];
		if ((response as any).scriptPaths) {
			// New format - convert script paths to GithubSetupScript objects
			const scriptPaths = (response as any).scriptPaths as string[];
			scripts = scriptPathsToGithubSetupScripts(scriptPaths);
		} else {
			console.warn('Response has neither scripts nor scriptPaths:', response);
		}

		return { scripts };
	} catch (error) {
		console.error('Failed to list GitHub setup scripts:', error);

		// Check if this is a backend endpoint not implemented error
		if (error instanceof Error && error.message.includes('Json deser')) {
			return {
				scripts: [],
				error: 'GitHub setup scripts feature is not yet available on this backend'
			};
		}

		return {
			scripts: [],
			error: error instanceof Error ? error.message : 'Failed to fetch setup scripts'
		};
	}
}

/**
 * Read content of a specific setup script from GitHub repository
 */
export async function readGithubSetupScript(
	githubRef: string,
	scriptName: string
): Promise<{
	content: string;
	error?: string;
}> {
	try {
		if (!isValidGithubRef(githubRef)) {
			return { content: '', error: 'Invalid GitHub reference format' };
		}

		const response = await apiClient.githubSetupScripts.read(githubRef, scriptName);

		if (response.errorMessage) {
			return { content: '', error: response.errorMessage };
		}

		return { content: response.content || '' };
	} catch (error) {
		console.error('Failed to read GitHub setup script:', error);

		// Check if this is a backend endpoint not implemented error
		if (error instanceof Error && error.message.includes('Json deser')) {
			return {
				content: '',
				error: 'GitHub setup scripts feature is not yet available on this backend'
			};
		}

		return {
			content: '',
			error: error instanceof Error ? error.message : 'Failed to read setup script'
		};
	}
}

/**
 * Get display name for a setup script (fallback to name if displayName is empty)
 */
export function getSetupScriptDisplayName(script: GithubSetupScript): string {
	return script.displayName || script.name;
}

/**
 * Sort setup scripts by display name
 */
export function sortSetupScripts(scripts: GithubSetupScript[]): GithubSetupScript[] {
	return [...scripts].sort((a, b) =>
		getSetupScriptDisplayName(a).localeCompare(getSetupScriptDisplayName(b))
	);
}

/**
 * Filter setup scripts by search term
 */
export function filterSetupScripts(
	scripts: GithubSetupScript[],
	searchTerm: string
): GithubSetupScript[] {
	if (!searchTerm.trim()) return scripts;

	const term = searchTerm.toLowerCase();
	return scripts.filter(
		(script) =>
			script.name.toLowerCase().includes(term) || script.displayName.toLowerCase().includes(term)
	);
}

/**
 * Check if a setup script content looks valid (basic validation)
 */
export function validateSetupScriptContent(content: string): {
	isValid: boolean;
	warnings: string[];
} {
	const warnings: string[] = [];

	if (!content.trim()) {
		return { isValid: false, warnings: ['Script content is empty'] };
	}

	// Check for shebang
	if (!content.startsWith('#!')) {
		warnings.push('Script should start with a shebang (e.g., #!/bin/bash)');
	}

	// Check for potentially dangerous commands (basic check)
	const dangerousPatterns = [
		/rm\s+-rf\s+\/[^\/\s]*/g, // rm -rf /something
		/sudo\s+rm/g, // sudo rm
		/>\s*\/dev\/sd[a-z]/g // writing to disk devices
	];

	for (const pattern of dangerousPatterns) {
		if (pattern.test(content)) {
			warnings.push('Script contains potentially dangerous commands');
			break;
		}
	}

	return { isValid: true, warnings };
}

/**
 * Extract setup script filename from path
 */
export function extractScriptFilename(scriptPath: string): string {
	return scriptPath.split('/').pop() || scriptPath;
}

/**
 * Convert script path to GithubSetupScript object
 */
export function scriptPathToGithubSetupScript(scriptPath: string): GithubSetupScript {
	const filename = extractScriptFilename(scriptPath);

	// Generate a display name from the filename
	let displayName = filename;

	// Remove common extensions
	displayName = displayName.replace(/\.(sh|bash|zsh|fish|ps1|bat|cmd)$/i, '');

	// Convert kebab-case and snake_case to Title Case
	displayName = displayName.replace(/[-_]/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());

	return {
		name: scriptPath,
		displayName
	};
}

/**
 * Convert array of script paths to array of GithubSetupScript objects
 */
export function scriptPathsToGithubSetupScripts(scriptPaths: string[]): GithubSetupScript[] {
	return scriptPaths.map(scriptPathToGithubSetupScript);
}

/**
 * Generate a default setup script name based on repository
 */
export function generateDefaultScriptName(githubRef: string): string {
	const { repo } = parseGithubRef(githubRef);
	return `setup-${repo}.sh`;
}
