import { browser } from '$app/environment';
import type { GitHubRepo, GitHubBranch } from '$lib/api/github-api';
import { apiClient, type CleanRemoteAgent } from '$lib/api/unified-client';

export interface LastUsedRepoAndBranch {
	repository: GitHubRepo;
	branch: GitHubBranch;
	timestamp: number;
}

const STORAGE_KEY = 'augment_last_repo_branch';

/**
 * Save the last used repository and branch to localStorage
 */
export function saveLastUsedRepoAndBranch(repo: GitHubRepo, branch: GitHubBranch): void {
	if (!browser) return;

	try {
		const data: LastUsedRepoAndBranch = {
			repository: repo,
			branch: branch,
			timestamp: Date.now()
		};
		localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
	} catch (error) {
		console.warn('Failed to save last used repo and branch:', error);
	}
}

/**
 * Load the last used repository and branch from localStorage
 */
export function loadLastUsedRepoAndBranch(): LastUsedRepoAndBranch | null {
	if (!browser) return null;

	try {
		const stored = localStorage.getItem(STORAGE_KEY);
		if (!stored) return null;

		const data = JSON.parse(stored) as LastUsedRepoAndBranch;

		// Validate the data structure
		if (!data.repository || !data.branch || !data.timestamp) {
			return null;
		}

		// Check if the data is not too old (30 days)
		const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;
		if (data.timestamp < thirtyDaysAgo) {
			localStorage.removeItem(STORAGE_KEY);
			return null;
		}

		return data;
	} catch (error) {
		console.warn('Failed to load last used repo and branch:', error);
		// Clear corrupted data
		localStorage.removeItem(STORAGE_KEY);
		return null;
	}
}

/**
 * Clear the last used repository and branch from localStorage
 */
export function clearLastUsedRepoAndBranch(): void {
	if (!browser) return;
	localStorage.removeItem(STORAGE_KEY);
}

/**
 * Extract repository and branch information from a remote agent
 */
function extractRepoAndBranchFromAgent(agent: CleanRemoteAgent): LastUsedRepoAndBranch | null {
	const workspaceSetup = agent.workspaceSetup;
	if (!workspaceSetup) console.error('No workspace setup found for agent', agent);
	if (!workspaceSetup?.githubCommitRef) {
		return null;
	}

	const commitRef = workspaceSetup.githubCommitRef;
	const repositoryUrl = commitRef.url;
	const gitRef = commitRef.ref;

	// Parse repository URL to extract owner and name
	const urlMatch = repositoryUrl.match(/github\.com[\/:]([^\/]+)\/([^\/\.]+)/);
	if (!urlMatch) {
		return null;
	}

	const [, owner, name] = urlMatch;

	// Create GitHubRepo and GitHubBranch objects
	const repository: GitHubRepo = {
		id: 0, // We don't have the actual GitHub ID
		name: name,
		full_name: `${owner}/${name}`,
		owner: owner,
		private: false, // We don't know, assume public
		html_url: repositoryUrl,
		description: null,
		fork: false,
		created_at: '',
		updated_at: '',
		pushed_at: '',
		clone_url: repositoryUrl,
		ssh_url: `**************:${owner}/${name}.git`,
		default_branch: gitRef // Assume the current ref is default
	};

	const branch: GitHubBranch = {
		name: gitRef,
		commit: {
			sha: '',
			url: ''
		},
		protected: false
	};

	return {
		repository,
		branch,
		timestamp: new Date(agent.startedAt).getTime()
	};
}

/**
 * Get repository and branch from the latest remote agent
 */
export async function getLatestAgentRepoAndBranch(): Promise<LastUsedRepoAndBranch | null> {
	try {
		const response = await apiClient.agents.list();

		if (!response.remoteAgents || response.remoteAgents.length === 0) {
			return null;
		}

		// Sort agents by startedAt timestamp (most recent first)
		const sortedAgents = response.remoteAgents.sort(
			(a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime()
		);

		// Try to extract repo and branch from the most recent agents
		for (const agent of sortedAgents) {
			const repoAndBranch = extractRepoAndBranchFromAgent(agent);
			if (repoAndBranch) {
				return repoAndBranch;
			}
		}

		return null;
	} catch (error) {
		console.warn('Failed to get latest agent repo and branch:', error);
		return null;
	}
}
