/**
 * Session migration utility to help transition from localStorage to secure cookies
 */

import { browser } from '$app/environment';

export interface LegacySession {
	accessToken: string;
	tenantUrl: string;
	scopes: string;
	expiresAt?: number;
	refreshToken?: string;
}

export interface LegacyGitHubSession {
	accessToken: string;
	scopes: string[];
	expiresAt?: number;
}

/**
 * Migrate existing localStorage sessions to secure server-side storage
 */
export async function migrateLocalStorageSessions(): Promise<{
	migrated: string[];
	errors: string[];
}> {
	if (!browser) {
		return { migrated: [], errors: [] };
	}

	const migrated: string[] = [];
	const errors: string[] = [];

	try {
		// Migrate Augment session
		const augmentSession = localStorage.getItem('augment_session');
		if (augmentSession) {
			try {
				const sessionData: LegacySession = JSON.parse(augmentSession);

				// Check if session is not expired
				if (!sessionData.expiresAt || sessionData.expiresAt > Date.now()) {
					const response = await fetch('/api/auth/session/migrate', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({
							provider: 'augment',
							session: sessionData
						}),
						credentials: 'include'
					});

					if (response.ok) {
						localStorage.removeItem('augment_session');
						migrated.push('augment');
					} else {
						errors.push('Failed to migrate Augment session');
					}
				} else {
					// Remove expired session
					localStorage.removeItem('augment_session');
				}
			} catch (error) {
				errors.push('Invalid Augment session data');
				localStorage.removeItem('augment_session');
			}
		}

		// Migrate GitHub session
		const githubSession = localStorage.getItem('github_session');
		if (githubSession) {
			try {
				const sessionData: LegacyGitHubSession = JSON.parse(githubSession);

				// Check if session is not expired
				if (!sessionData.expiresAt || sessionData.expiresAt > Date.now()) {
					const response = await fetch('/api/auth/session/migrate', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({
							provider: 'github',
							session: sessionData
						}),
						credentials: 'include'
					});

					if (response.ok) {
						localStorage.removeItem('github_session');
						migrated.push('github');
					} else {
						errors.push('Failed to migrate GitHub session');
					}
				} else {
					// Remove expired session
					localStorage.removeItem('github_session');
				}
			} catch (error) {
				errors.push('Invalid GitHub session data');
				localStorage.removeItem('github_session');
			}
		}

		// Migrate GitHub token (legacy)
		const githubToken = localStorage.getItem('github_token');
		if (githubToken) {
			try {
				const response = await fetch('/api/auth/session/migrate', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						provider: 'github',
						session: {
							accessToken: githubToken,
							scopes: ['repo'], // Default scope
							expiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000 // 30 days
						}
					}),
					credentials: 'include'
				});

				if (response.ok) {
					localStorage.removeItem('github_token');
					migrated.push('github-token');
				} else {
					errors.push('Failed to migrate GitHub token');
				}
			} catch (error) {
				errors.push('Failed to migrate GitHub token');
			}
		}

		// Migrate Linear token (legacy)
		const linearToken = localStorage.getItem('linear_token');
		if (linearToken) {
			try {
				const response = await fetch('/api/auth/session/migrate', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						provider: 'linear',
						session: {
							accessToken: linearToken,
							expiresAt: Date.now() + 30 * 24 * 60 * 60 * 1000 // 30 days
						}
					}),
					credentials: 'include'
				});

				if (response.ok) {
					localStorage.removeItem('linear_token');
					migrated.push('linear-token');
				} else {
					errors.push('Failed to migrate Linear token');
				}
			} catch (error) {
				errors.push('Failed to migrate Linear token');
			}
		}
	} catch (error) {
		errors.push('Migration process failed');
	}

	return { migrated, errors };
}

/**
 * Check if there are any legacy sessions that need migration
 */
export function hasLegacySessions(): boolean {
	if (!browser) return false;

	const legacyKeys = ['augment_session', 'github_session', 'github_token', 'linear_token'];

	return legacyKeys.some((key) => localStorage.getItem(key) !== null);
}

/**
 * Clear all legacy localStorage data
 */
export function clearLegacySessions(): void {
	if (!browser) return;

	const legacyKeys = ['augment_session', 'github_session', 'github_token', 'linear_token'];

	legacyKeys.forEach((key) => {
		localStorage.removeItem(key);
	});
}
