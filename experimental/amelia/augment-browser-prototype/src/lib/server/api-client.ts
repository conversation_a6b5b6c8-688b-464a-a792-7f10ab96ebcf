/**
 * Server-side API client utilities that handle authentication securely
 */

import { augmentSession, githubSession, linearSession } from './session';

export interface ApiClientOptions {
	baseUrl?: string;
	headers?: Record<string, string>;
}

/**
 * Create an authenticated API client for Augment services
 */
export function createAugmentApiClient(request: Request, options: ApiClientOptions = {}) {
	const session = augmentSession.get(request);

	if (!session) {
		throw new Error('No valid Augment session found');
	}

	const { baseUrl = session.tenantUrl, headers = {} } = options;

	return {
		async fetch(endpoint: string, init: RequestInit = {}) {
			const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

			const authHeaders = {
				'Authorization': `Bearer ${session.accessToken}`,
				'Content-Type': 'application/json',
				...headers,
				...init.headers
			};

			return fetch(url, {
				...init,
				headers: authHeaders
			});
		},

		session: {
			tenantUrl: session.tenantUrl,
			scopes: session.scopes,
			expiresAt: session.expiresAt
		}
	};
}

/**
 * Create an authenticated API client for GitHub
 */
export function createGitHubApiClient(request: Request, options: ApiClientOptions = {}) {
	const session = githubSession.get(request);

	if (!session) {
		throw new Error('No valid GitHub session found');
	}

	const { baseUrl = 'https://api.github.com', headers = {} } = options;

	return {
		async fetch(endpoint: string, init: RequestInit = {}) {
			const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

			const authHeaders = {
				'Authorization': `Bearer ${session.accessToken}`,
				'Accept': 'application/vnd.github.v3+json',
				'User-Agent': 'Augment-Browser/1.0',
				...headers,
				...init.headers
			};

			return fetch(url, {
				...init,
				headers: authHeaders
			});
		},

		session: {
			scopes: session.scopes,
			expiresAt: session.expiresAt
		}
	};
}

/**
 * Create an authenticated API client for Linear
 */
export function createLinearApiClient(request: Request, options: ApiClientOptions = {}) {
	const session = linearSession.get(request);

	if (!session) {
		throw new Error('No valid Linear session found');
	}

	const { baseUrl = 'https://api.linear.app', headers = {} } = options;

	return {
		async fetch(endpoint: string, init: RequestInit = {}) {
			const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

			const authHeaders = {
				'Authorization': `Bearer ${session.accessToken}`,
				'Content-Type': 'application/json',
				...headers,
				...init.headers
			};

			return fetch(url, {
				...init,
				headers: authHeaders
			});
		},

		session: {
			expiresAt: session.expiresAt
		}
	};
}

/**
 * Helper to check if any session is expired and needs refresh
 */
export function checkSessionExpiry(request: Request) {
	const sessions = {
		augment: augmentSession.get(request),
		github: githubSession.get(request),
		linear: linearSession.get(request)
	};

	const now = Date.now();
	const expiredSessions: string[] = [];

	Object.entries(sessions).forEach(([provider, session]) => {
		if (session?.expiresAt && session.expiresAt < now) {
			expiredSessions.push(provider);
		}
	});

	return {
		hasExpiredSessions: expiredSessions.length > 0,
		expiredSessions,
		sessions: Object.fromEntries(
			Object.entries(sessions).map(([provider, session]) => [
				provider,
				session ? {
					isValid: !session.expiresAt || session.expiresAt > now,
					expiresAt: session.expiresAt
				} : null
			])
		)
	};
}
