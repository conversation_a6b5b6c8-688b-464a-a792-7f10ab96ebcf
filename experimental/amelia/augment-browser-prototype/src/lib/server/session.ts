/**
 * Server-side session management with secure httpOnly cookies
 */

import type { RequestEvent } from '@sveltejs/kit';
import { dev } from '$app/environment';

export interface AugmentSession {
	accessToken: string;
	tenantUrl: string;
	scopes: string;
	expiresAt?: number;
	refreshToken?: string;
}

export interface GitHubSession {
	accessToken: string;
	scopes: string[];
	expiresAt?: number;
}

export interface LinearSession {
	accessToken: string;
	expiresAt?: number;
}

// Session cookie names
const SESSION_COOKIES = {
	AUGMENT: 'augment_session',
	GITHUB: 'github_session',
	LINEAR: 'linear_session'
} as const;

// Token expiration settings (SOC2 compliance - no forever tokens)
const TOKEN_EXPIRATION = {
	// Default session duration: 7 days (more secure than 30 days)
	DEFAULT_DAYS: 7,
	// Maximum session duration: 30 days (for compatibility)
	MAX_DAYS: 30,
	// Convert to seconds for maxAge
	DEFAULT_SECONDS: 60 * 60 * 24 * 7, // 7 days
	MAX_SECONDS: 60 * 60 * 24 * 30 // 30 days
} as const;

// Default cookie options for secure sessions
const SECURE_COOKIE_OPTIONS = {
	httpOnly: true,
	secure: !dev, // Only secure in production
	sameSite: 'strict' as const,
	path: '/',
	maxAge: TOKEN_EXPIRATION.DEFAULT_SECONDS // 7 days default
};

/**
 * Set a secure session cookie
 */
function setSecureCookie(
	name: string,
	value: string,
	options: Partial<typeof SECURE_COOKIE_OPTIONS> = {}
): string {
	const opts = { ...SECURE_COOKIE_OPTIONS, ...options };

	let cookieString = `${name}=${value}; Path=${opts.path}; SameSite=${opts.sameSite}`;

	if (opts.maxAge) {
		cookieString += `; Max-Age=${opts.maxAge}`;
	}

	if (opts.httpOnly) {
		cookieString += '; HttpOnly';
	}

	if (opts.secure) {
		cookieString += '; Secure';
	}

	return cookieString;
}

/**
 * Parse cookies from request headers
 */
function parseCookies(cookieHeader: string | null): Record<string, string> {
	if (!cookieHeader) return {};

	return cookieHeader.split(';').reduce(
		(cookies, cookie) => {
			const [name, value] = cookie.trim().split('=');
			if (name && value) {
				cookies[name] = decodeURIComponent(value);
			}
			return cookies;
		},
		{} as Record<string, string>
	);
}

/**
 * Get session data from secure cookie
 */
function getSessionFromCookie<T>(request: Request, cookieName: string): T | null {
	const cookieHeader = request.headers.get('cookie');
	const cookies = parseCookies(cookieHeader);
	const sessionData = cookies[cookieName];

	if (!sessionData) return null;

	try {
		const parsed = JSON.parse(sessionData);

		// Check if session is expired
		if (parsed.expiresAt && Date.now() > parsed.expiresAt) {
			return null;
		}

		return parsed;
	} catch {
		return null;
	}
}

/**
 * Augment session management
 */
export const augmentSession = {
	get(request: Request): AugmentSession | null {
		return getSessionFromCookie<AugmentSession>(request, SESSION_COOKIES.AUGMENT);
	},

	set(session: AugmentSession): string {
		// Set expiration to 7 days if not provided (SOC2 compliance)
		if (!session.expiresAt) {
			session.expiresAt = Date.now() + TOKEN_EXPIRATION.DEFAULT_DAYS * 24 * 60 * 60 * 1000;
		}

		const sessionData = JSON.stringify(session);
		return setSecureCookie(SESSION_COOKIES.AUGMENT, encodeURIComponent(sessionData));
	},

	clear(): string {
		return setSecureCookie(SESSION_COOKIES.AUGMENT, '', { maxAge: 0 });
	}
};

/**
 * GitHub session management
 */
export const githubSession = {
	get(request: Request): GitHubSession | null {
		return getSessionFromCookie<GitHubSession>(request, SESSION_COOKIES.GITHUB);
	},

	set(session: GitHubSession): string {
		// Set expiration to 7 days if not provided (SOC2 compliance)
		if (!session.expiresAt) {
			session.expiresAt = Date.now() + TOKEN_EXPIRATION.DEFAULT_DAYS * 24 * 60 * 60 * 1000;
		}

		const sessionData = JSON.stringify(session);
		return setSecureCookie(SESSION_COOKIES.GITHUB, encodeURIComponent(sessionData));
	},

	clear(): string {
		return setSecureCookie(SESSION_COOKIES.GITHUB, '', { maxAge: 0 });
	}
};

/**
 * Linear session management
 */
export const linearSession = {
	get(request: Request): LinearSession | null {
		return getSessionFromCookie<LinearSession>(request, SESSION_COOKIES.LINEAR);
	},

	set(session: LinearSession): string {
		// Set expiration to 7 days if not provided (SOC2 compliance)
		if (!session.expiresAt) {
			session.expiresAt = Date.now() + TOKEN_EXPIRATION.DEFAULT_DAYS * 24 * 60 * 60 * 1000;
		}

		const sessionData = JSON.stringify(session);
		return setSecureCookie(SESSION_COOKIES.LINEAR, encodeURIComponent(sessionData));
	},

	clear(): string {
		return setSecureCookie(SESSION_COOKIES.LINEAR, '', { maxAge: 0 });
	}
};

/**
 * Helper to add session cookie to response headers
 */
export function addSessionCookie(headers: Headers, cookieString: string): void {
	const existingCookies = headers.get('Set-Cookie');
	if (existingCookies) {
		headers.set('Set-Cookie', `${existingCookies}, ${cookieString}`);
	} else {
		headers.set('Set-Cookie', cookieString);
	}
}

/**
 * Helper to get all session data from request
 */
export function getAllSessions(request: Request) {
	return {
		augment: augmentSession.get(request),
		github: githubSession.get(request),
		linear: linearSession.get(request)
	};
}
