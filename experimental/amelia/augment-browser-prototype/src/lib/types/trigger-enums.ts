/**
 * Trigger-related enums and types
 * Separated to avoid circular dependencies
 */

// Event sources
export enum EventSource {
	EVENT_SOURCE_UNSPECIFIED = 0,
	EVENT_SOURCE_GITHUB = 1,
	EVENT_SOURCE_LINEAR = 2,
	EVENT_SOURCE_JIRA = 3
}

// GitHub entity types
export enum GitHubEntityType {
	GITHUB_ENTITY_TYPE_UNSPECIFIED = 0,
	GITHUB_ENTITY_TYPE_PULL_REQUEST = 1,
	GITHUB_ENTITY_TYPE_WORKFLOW_RUN = 2,
	GITHUB_ENTITY_TYPE_ISSUE = 3
}

export type SCHEDULE_ENTITY_TYPE_SCHEDULE = 4;

// Linear entity types
export enum LinearEntityType {
	LINEAR_ENTITY_TYPE_UNSPECIFIED = 0,
	LINEAR_ENTITY_TYPE_ISSUE = 1
}

// Trigger condition types
export enum TriggerConditionType {
	TRIGGER_CONDITION_TYPE_UNSPECIFIED = 0,
	TRIGGER_CONDITION_GITHUB = 1,
	TRIGGER_CONDITION_LINEAR = 2
}

// Trigger execution status
export enum TriggerExecutionStatus {
	TRIGGER_EXECUTION_STATUS_UNSPECIFIED = 0,
	TRIGGER_EXECUTION_STATUS_PENDING = 1,
	TRIGGER_EXECUTION_STATUS_RUNNING = 2,
	TRIGGER_EXECUTION_STATUS_SUCCESS = 3,
	TRIGGER_EXECUTION_STATUS_FAILED = 4,
	TRIGGER_EXECUTION_STATUS_CANCELLED = 5
}

// GitHub Pull Request trigger conditions
export interface GitHubPullRequestTriggerConditions {
	author?: string;
	assignee?: string;
	reviewer?: string;
	base_branch?: string;
	head_branch?: string;
	repository?: string;
	title_contains?: string;
	labels?: string[];
	activity_types?: string[]; // "opened", "synchronize", "ready_for_review", etc.
	draft?: boolean;
	event?: string; // GitHub webhook event type (e.g., "pull_request", "pull_request_review", "pull_request_review_comment")
	actions?: string[]; // Specific actions within the event type (e.g., ["opened", "synchronize"], ["submitted"], ["created", "edited"])
}

// GitHub Workflow Run trigger conditions
export interface GitHubWorkflowRunTriggerConditions {
	actor?: string;
	event?: string;
	status?: string;
	conclusion?: string;
	branch?: string;
	repository?: string;
}

// GitHub trigger conditions
export interface GitHubTriggerConditions {
	entity_type: GitHubEntityType;
	pull_request?: GitHubPullRequestTriggerConditions;
	workflow_run?: GitHubWorkflowRunTriggerConditions;
}

// Linear Issue trigger conditions
export interface LinearIssueTriggerConditions {
	creator?: string;
	assignee?: string;
	team?: string;
	project?: string;
	title_contains?: string;
	activity_types?: string;
	min_estimate?: number;
	max_estimate?: number;
	states?: string[];
	state_types?: string[];
	labels?: string[];
	priorities?: number[];
}

// Linear trigger conditions
export interface LinearTriggerConditions {
	entity_type: LinearEntityType;
	issue?: LinearIssueTriggerConditions;
}

// Main trigger conditions interface
export interface TriggerConditions {
	type: TriggerConditionType;
	github?: GitHubTriggerConditions;
	linear?: LinearTriggerConditions;
}

// Agent configuration interface
export interface AgentConfig {
	user_guidelines: string;
	workspace_guidelines?: string;
	workspace_setup?: {
		starting_files?: {
			github_ref?: {
				url: string;
				ref: string;
			};
		};
	};
}

// Main trigger configuration interface (matches protobuf TriggerConfiguration)
export interface TriggerConfiguration {
	name: string;
	description?: string;
	event_source: EventSource;
	conditions: TriggerConditions;
	agent_config: AgentConfig;
	enabled?: boolean; // defaults to true
	template_id?: string;
	template_name?: string;
	template_json?: string;
}
