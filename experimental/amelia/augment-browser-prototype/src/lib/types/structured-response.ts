/**
 * Types for structured agent responses
 */

import {
	wrapMessageWithPrefix,
	extractOriginalMessage as extractOriginalMessageVersioned,
	DEFAULT_PREFIX_VERSION,
	SUMMARY_INSTRUCTIONS,
	NOTES_GUIDELINE
} from '$lib/utils/agent-prefix-versions';

export interface StructuredResponseSection {
	type:
		| 'thinking'
		| 'researching'
		| 'doing'
		| 'noting'
		| 'summary'
		| 'user_responses'
		| 'plan'
		| 'questions'
		| 'question_responses'
		| 'user_request'
		| 'error'
		| 'status'
		| 'warning';
	content: string;
	timestamp?: string;
}

/**
 * Question field types for form rendering
 */
export type QuestionFieldType = 'text' | 'textarea' | 'select' | 'boolean' | 'number' | 'slider';

export interface QuestionField {
	id: string;
	label: string;
	type: QuestionFieldType;
	required?: boolean;
	placeholder?: string;
	options?: string[]; // For select type
	defaultValue?: string | boolean | number;
	min?: number; // For number and slider types
	max?: number; // For number and slider types
	step?: number; // For number and slider types
}

export interface QuestionResponse {
	[questionId: string]: string | boolean | number;
}

export interface StructuredResponse {
	sections: StructuredResponseSection[];
	isComplete: boolean;
	rawResponse?: string;
}

export interface StructuredSectionData {
	content: string;
	rawText: string;
	endingSummary?: string; // Last sentence that serves as summary for this section
}

export interface QuestionResponseData {
	question: string;
	response: string;
	questionId?: string;
}

export interface BulletListItem {
	text: string;
	level: number;
}

export type NoteCategory =
	| 'code_architecture'
	| 'implementation_details'
	| 'user_preferences'
	| 'technical_constraints'
	| 'business_logic'
	| 'integration_points';

export interface NotebookNote {
	content: string;
	category: NoteCategory;
	timestamp?: string;
	exchangeId?: string;
}

export interface ParsedStructuredResponse {
	thinking?: StructuredSectionData[];
	researching?: StructuredSectionData[];
	doing?: StructuredSectionData[];
	noting?: BulletListItem[]; // Changed to support nesting levels
	notebook_notes?: NotebookNote[]; // New: extracted from XML note tags
	summary?: string;
	user_responses?: string[]; // Keep as strings for next steps (clickable buttons)
	plan?: BulletListItem[]; // Support nesting levels for plans
	questions?: QuestionField[];
	question_responses?: QuestionResponseData[];
	user_request?: string;
	error?: string;
	status?: string; // Work status: "done" or "waiting_for_input"
	warning?: string; // Important warnings for the user (1-2 sentences)
	isComplete: boolean;
	rawResponse?: string;
	sectionOrder?: Array<{ type: string; index: number }>; // Track original section order
}

// NOTES_GUIDELINE is now imported from agent-prefix-versions.ts

/**
 * Legacy guidelines for structured responses (replaced by AGENT_WORK_INSTRUCTIONS)
 */
export const STRUCTURED_RESPONSE_GUIDELINES = `Please structure your responses using section headers. Use the following format with headers like =thinking= followed by a newline:

=thinking=
Your reasoning and analysis about the request. What are you planning to do and why?

=plan=
A bulleted list of specific steps you will take to accomplish the task. Each step should be on its own line starting with a bullet point (- or *).

=researching=
Information you're gathering or looking up. What are you investigating in the codebase or documentation?

=doing=
Actions you're taking or code you're writing. What specific changes or implementations are you making?

=noting=
Important findings, key insights, or things to remember. What did you discover that's important? Keep these brief and only add ones that are surprising and important. Things that a developer would write down in a notebook while working on the task. Each point should be on its own line starting with a bullet point (- or *).

=warning=
Critical information the user needs to know immediately. Use this for important warnings, potential issues, or things that could cause problems if not addressed. Keep it to 1-2 sentences maximum. Only use this for truly important warnings that the user definitely needs to see.

=summary=
A brief 1-3 sentence summary of what you accomplished, or quick tldr of what you explained. The tone should be conversational and friendly, like you're explaining what you're doing to a friend.

=status=
Indicate whether the work is complete and ready for a pull request, or if you're waiting for user input. Use exactly one of these values:
- "done" - The work is complete and ready for a pull request
- "waiting_for_input" - You need more information or decisions from the user before proceeding

=questions=
Clarifying questions you need answers to before proceeding. Format each question as JSON with fields: id, label, type, required (optional), placeholder (optional), options (for select type), defaultValue (optional), min/max/step (for number and slider types). Supported types: text, textarea, select, boolean, number, slider. Each question should be on its own line as valid JSON.

Use defaultValue to pre-fill form fields with sensible defaults:
- For text/textarea: provide a default string value
- For boolean: true or false
- For number/slider: provide a numeric default within min/max range
- For select: provide one of the option values

=user_responses=
Potential phrases or requests the user might respond with next. These should be EXACTLY what the user would type or say, not questions for the user and NOT statements about what you will do next. Each response should be on its own line starting with a bullet point (- or *). Do NOT include questions for the user - use the =questions= section for that instead. Do NOT include statements about what you plan to do - these are suggestions for what the USER might say. Do NOT respond to your questions in here.

Examples of GOOD user_responses:
- "Add error handling to the login function"
- "Create a test for this component"
- "Show me the database schema"
- "Implement the logout functionality"
- "Use OpenAI's vision API for image recognition"
- "Generate alt text when images are uploaded"

Examples of BAD user_responses (these belong in =questions= instead):
- "What error handling do you want?"
- "Which tests should I create?"
- "Do you want me to proceed?"

Examples of BAD user_responses (these are statements about what you will do, not what the user might say):
- "Once I have your answers, I will research the codebase"
- "I will then outline a detailed plan"
- "Based on your preferences, I can suggest specific services"

Guidelines:
- default to =thinking= and make sure to switch to other sections when appropriate
- these sections should be as long as needed, even multiple paragraphs.
- For thinking, researching, and doing, be verbose and think as long as you like. Each of these sections should finish with a single sentence that summarizes the section. For that sentence, assume the user can't see any of the previous content, so the summary should make sense on its own.
- Not all sections are required - use only the ones that are relevant
- Always include a =summary= section with 1-3 sentences about what you did
- If you encounter an error, you can use =error= as a section header
- noting, plan, and user_responses should be bulleted lists, with each point on its own line starting with a bullet point (- or *)
- You can repeat sections if needed (e.g., multiple =doing= sections for different tasks)

Example response format:
=thinking=
This is a complex request that needs careful handling. I need to implement user authentication. Let me analyze what's already in place and what needs to be added.

=plan=
- Research the current authentication system in the codebase
- Install required OAuth packages (passport, passport-google-oauth20, passport-github2)
- Set up OAuth configuration files
- Create OAuth routes and middleware
- Test the authentication flow
- Add error handling for failed attempts

=researching=
Looking at the current auth system in the codebase. I can see there's already a user model and some basic session handling.

=doing=
Installing the required OAuth packages and setting up the configuration files.

=noting=
- The current system uses JWT tokens
- The user table already has the necessary fields

=summary=
Successfully set up OAuth authentication with Google and GitHub providers.

=user_responses=
- Test the OAuth flow with different providers
- Add rate limiting to the authentication endpoints
- Create unit tests for the OAuth middleware
- Show me the user dashboard after login
- Configure the session timeout to 7 days
- Add support for Microsoft OAuth as well

=questions=
{"id": "oauth_providers", "label": "Which OAuth providers do you want to support?", "type": "select", "options": ["Google", "GitHub", "Microsoft", "All"], "required": true, "defaultValue": "Google"}
{"id": "session_duration", "label": "How long should user sessions last?", "type": "text", "placeholder": "e.g., 24 hours, 7 days", "defaultValue": "24 hours"}
{"id": "remember_me", "label": "Should we include a 'Remember Me' option?", "type": "boolean", "defaultValue": true}
{"id": "max_concurrent_sessions", "label": "Maximum concurrent sessions per user", "type": "slider", "min": 1, "max": 10, "step": 1, "defaultValue": 3}
{"id": "session_timeout_hours", "label": "Session timeout (hours)", "type": "slider", "min": 1, "max": 168, "step": 1, "defaultValue": 24}
{"id": "additional_config", "label": "Any additional configuration notes?", "type": "textarea", "placeholder": "Enter any specific requirements...", "defaultValue": "Standard security settings"}
{"id": "enable_2fa", "label": "Enable two-factor authentication by default?", "type": "boolean", "defaultValue": false}
{"id": "api_rate_limit", "label": "API rate limit (requests per minute)", "type": "number", "min": 10, "max": 1000, "defaultValue": 100}

Please use this structured format for all your responses to help organize information clearly.`;

/**
 * Legacy template for backward compatibility (now unused)
 */
export const STRUCTURED_RESPONSE_TEMPLATE = STRUCTURED_RESPONSE_GUIDELINES;

// SUMMARY_INSTRUCTIONS is now imported from agent-prefix-versions.ts

/**
 * Complete agent work instructions combining structured response format and note-taking
 * @deprecated Use the versioned prefix system from agent-prefix-versions.ts instead
 */
export const AGENT_WORK_INSTRUCTIONS = `${SUMMARY_INSTRUCTIONS}\n\n${NOTES_GUIDELINE}`;

/**
 * Wrap the initial user message with structured response instructions
 * This is only used for the very first message during agent creation
 * @deprecated Use wrapMessageWithPrefix from agent-prefix-versions.ts instead
 */
export function wrapInitialMessageWithWorkInstructions(message: string): string {
	return wrapMessageWithPrefix(message, 'v1'); // Use v1 for backwards compatibility
}

/**
 * Wrap the initial user message with simplified instructions for projects workflows
 * This asks for a summary wrapped in <summary> tags instead of full structured responses
 * @deprecated Use wrapMessageWithPrefix with 'v2' version instead
 */
export function wrapInitialMessageWithSummaryInstructions(message: string): string {
	return wrapMessageWithPrefix(message, 'v2');
}

/**
 * Wrap a message with a specific prefix version
 * This is the new recommended way to add prefixes to agent messages
 */
export function wrapMessageWithPrefixVersion(message: string, versionId?: string): string {
	return wrapMessageWithPrefix(message, versionId);
}

/**
 * Wrap a message with the latest prefix version (no notebook instructions)
 * This is recommended for new agents that don't need notebook functionality
 */
export function wrapInitialMessageWithLatestInstructions(message: string): string {
	return wrapMessageWithPrefix(message, DEFAULT_PREFIX_VERSION);
}

/**
 * Extract question responses from a structured user message
 * Returns an array of {question, response} objects
 */
export function extractQuestionResponsesFromUserMessage(
	message: string
): Array<{ question: string; response: string }> {
	const questionResponsesMatch = message.match(/=question_responses=\s*\n([\s\S]*?)(?:\n=|$)/);
	if (!questionResponsesMatch) {
		return [];
	}

	const questionResponsesText = questionResponsesMatch[1].trim();
	const responses: Array<{ question: string; response: string }> = [];

	// Parse each line that matches the format: **Question:** Response
	const lines = questionResponsesText.split('\n').filter((line) => line.trim());
	for (const line of lines) {
		const match = line.match(/^\*\*([^*]+):\*\*\s*(.+)$/);
		if (match) {
			const [, question, response] = match;
			responses.push({
				question: question.trim(),
				response: response.trim()
			});
		}
	}

	return responses;
}

/**
 * Extract summary content from <summary> tags in agent response
 * Returns the content between <summary> and </summary> tags, or null if not found
 */
export function extractSummaryFromTags(responseText: string): string | null {
	const summaryMatch = responseText.match(/<summary>\s*([\s\S]*?)\s*<\/summary>/i);
	return summaryMatch ? summaryMatch[1].trim() : null;
}

/**
 * Extract general summary content from <general_summary> tags in agent response
 * Returns the content between <general_summary> and </general_summary> tags, or null if not found
 */
export function extractGeneralSummaryFromTags(responseText: string): string | null {
	const generalSummaryMatch = responseText.match(
		/<general_summary>\s*([\s\S]*?)\s*<\/general_summary>/i
	);
	return generalSummaryMatch ? generalSummaryMatch[1].trim() : null;
}

/**
 * Extract the original user message from an initial message that contains structured response instructions
 * This is used to display only the user's actual message in the UI
 * Now uses the versioned prefix system for better extraction
 */
export function extractOriginalMessageFromInitial(messageWithInstructions: string): string {
	return extractOriginalMessageVersioned(messageWithInstructions);
}

/**
 * @deprecated Use wrapInitialMessageWithWorkInstructions for initial messages
 */
export function wrapMessageWithStructuredInstructions(
	message: string,
	_useStructuredResponse = true
): string {
	// No longer wrap regular messages - only initial messages are wrapped
	return message;
}

/**
 * @deprecated Use extractOriginalMessageFromInitial for initial messages
 */
export function extractOriginalMessage(messageWithInstructions: string): string {
	// Try the new extraction method first, fallback to original
	return extractOriginalMessageFromInitial(messageWithInstructions);
}
