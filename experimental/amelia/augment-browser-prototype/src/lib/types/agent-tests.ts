export type AgentTestType = 'programmatic' | 'ui' | 'documentation' | 'manual';

export type AgentTestStatus =
	| 'PENDING' // Test generation is queued
	| 'GENERATED' // Test has been generated successfully
	| 'FAILED' // Test generation failed
	| 'EXECUTED' // Test has been executed
	| 'PASSED' // Test execution passed
	| 'FAILED_EXECUTION'; // Test execution failed

// Base AgentTest interface (previously from Prisma)
export interface PrismaAgentTest {
	id: string;
	agentId: string;
	turnIndex: number;
	testType: string;
	title: string;
	description?: string | null;
	status: string;
	testData?: string | null;
	resultData?: string | null;
	createdAt: Date;
	updatedAt: Date;
	executedAt?: Date | null;
}

export interface AgentTest extends PrismaAgentTest {
	testType: AgentTestType;
	status: AgentTestStatus;
	parsedTestData?: any;
	parsedResultData?: any;
}

// Test data structures for different test types
export interface ProgrammaticTestData {
	language: string;
	framework?: string;
	testFiles: Array<{
		filePath: string;
		testCode: string;
		description: string;
	}>;
	setupInstructions?: string;
	runCommand: string;
}

export interface UITestData {
	htmlContent: string;
	cssContent?: string;
	jsContent?: string;
	description: string;
	interactionInstructions?: string[];
	expectedBehavior: string[];
}

export interface DocumentationTestData {
	checklist: Array<{
		id: string;
		description: string;
		category: string;
		required: boolean;
	}>;
	instructions: string;
	validationCriteria: string[];
}

export interface ManualTestData {
	steps: Array<{
		id: string;
		description: string;
		expectedResult: string;
	}>;
	prerequisites?: string[];
	notes?: string;
}

// Test result structures
export interface ProgrammaticTestResult {
	exitCode: number;
	stdout: string;
	stderr: string;
	duration: number;
	passed: boolean;
	failedTests?: string[];
}

export interface UITestResult {
	userFeedback: string;
	issuesFound: string[];
	rating: number; // 1-5 scale
	completedAt: string;
}

export interface DocumentationTestResult {
	checkedItems: string[];
	notes: string;
	overallStatus: 'passed' | 'failed' | 'partial';
	completedAt: string;
}

export interface ManualTestResult {
	completedSteps: Array<{
		stepId: string;
		status: 'passed' | 'failed' | 'skipped';
		notes?: string;
	}>;
	overallStatus: 'passed' | 'failed' | 'partial';
	testerNotes: string;
	completedAt: string;
}

// Test generation request
export interface TestGenerationRequest {
	agentId: string;
	turnIndex: number;
	changedFiles: Array<{
		filePath: string;
		changeType: string;
		newContents?: string;
		oldContents?: string;
		diff?: string;
	}>;
	agentSummary: string;
	taskContext: string;
}

// Test generation response
export interface TestGenerationResponse {
	tests: Array<{
		testType: AgentTestType;
		title: string;
		description: string;
		testData: ProgrammaticTestData | UITestData | DocumentationTestData | ManualTestData;
		priority: number; // 1-5, higher is more important
	}>;
	generationNotes?: string;
}
