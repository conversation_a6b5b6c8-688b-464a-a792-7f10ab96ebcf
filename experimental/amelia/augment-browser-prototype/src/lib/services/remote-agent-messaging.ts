/**
 * Remote Agent Messaging Service
 *
 * TEMPORARILY DISABLED - Being migrated to new unified system
 *
 * This service is being rewritten to work with the new global state system.
 * Chat functionality is temporarily unavailable during this migration.
 */

import { writable } from 'svelte/store';

export interface SendMessageOptions {
	userGuidelines?: string;
	workspaceGuidelines?: string;
	agentMemories?: string;
	modelId?: string;
	timeout?: number;
	retryAttempts?: number;
}

export interface SendMessageError {
	type: 'timeout' | 'network' | 'server' | 'validation' | 'disabled';
	message: string;
	canRetry: boolean;
	originalMessage: string;
	exchangeId?: string;
}

// Store for tracking optimistic messages (placeholder)
export const optimisticMessages = writable<
	Record<string, { id: string; content: string; timestamp: number }>
>({});

/**
 * Send a message to a remote agent (TEMPORARILY DISABLED)
 */
export async function sendMessage(
	agentId: string,
	message: string,
	options: SendMessageOptions = {}
): Promise<void> {
	const error: SendMessageError = {
		type: 'disabled',
		message:
			'Chat functionality is temporarily disabled during migration to the new unified system. Please check back later.',
		canRetry: false,
		originalMessage: message,
		exchangeId: `disabled_${Date.now()}`
	};

	throw error;
}

/**
 * Retry a failed message (TEMPORARILY DISABLED)
 */
export async function retryMessage(
	agentId: string,
	error: SendMessageError,
	options: SendMessageOptions = {}
): Promise<void> {
	throw new Error(
		'Chat functionality is temporarily disabled during migration to the new unified system.'
	);
}

/**
 * Cancel a pending message (TEMPORARILY DISABLED)
 */
export function cancelMessage(agentId: string, exchangeId: string): void {
	// No-op during migration
}

/**
 * Get all pending messages for an agent (TEMPORARILY DISABLED)
 */
export function getPendingMessages(agentId: string): string[] {
	return [];
}

/**
 * Check if an agent has pending messages (TEMPORARILY DISABLED)
 */
export function hasPendingMessages(agentId: string): boolean {
	return false;
}

/**
 * Stop streaming updates for an agent (TEMPORARILY DISABLED)
 */
export function stopStreamingUpdates(agentId: string): void {
	// No-op during migration
}

/**
 * Check if an agent is currently streaming (TEMPORARILY DISABLED)
 */
export function isStreaming(agentId: string): boolean {
	return false;
}

/**
 * Stop all active streams (TEMPORARILY DISABLED)
 */
export function stopAllStreams(): void {
	// No-op during migration
}
