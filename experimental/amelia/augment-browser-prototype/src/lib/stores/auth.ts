import { browser } from '$app/environment';
import {
	createAugmentOAuth,
	type AugmentOAuth,
	type AugmentSession
} from '$lib/auth/augment-oauth-web';
import { get, writable } from 'svelte/store';
import { authUrl } from '$lib/stores/debug-settings';

// Authentication state
export const session = writable<AugmentSession | null | undefined>();
export const isLoading = writable(false);

// OAuth client instance
let oauthClient: AugmentOAuth | null = null;
let currentAuthUrl: string | null = null;

// Initialize OAuth client
function getOAuthClient(): AugmentOAuth {
	const newAuthUrl = get(authUrl);

	// Reset client if auth URL has changed
	if (!oauthClient || currentAuthUrl !== newAuthUrl) {
		currentAuthUrl = newAuthUrl;
		oauthClient = createAugmentOAuth({
			clientId: 'augment-web-ui',
			redirectUri: `${window.location.origin}/auth/callback`,
			scope: ['email'],
			authUrl: newAuthUrl
		});
	}
	return oauthClient;
}

// Session management functions (now using secure server-side cookies)
async function getSessionInfo(): Promise<any> {
	if (!browser) return null;
	try {
		const response = await fetch('/api/auth/session');
		if (response.ok) {
			return await response.json();
		}
		return null;
	} catch (error) {
		console.error('Failed to get session info:', error);
		return null;
	}
}

async function clearSession(): Promise<void> {
	if (!browser) return;
	try {
		await fetch('/api/auth/session', { method: 'DELETE' });
	} catch (error) {
		console.error('Failed to clear session:', error);
	}
}

// Initialize session from server-side cookies on browser load
if (browser) {
	// First check for legacy localStorage sessions and migrate them
	import('$lib/utils/session-migration').then(
		({ migrateLocalStorageSessions, hasLegacySessions }) => {
			if (hasLegacySessions()) {
				console.log('Migrating legacy localStorage sessions...');
				migrateLocalStorageSessions().then(({ migrated, errors }) => {
					if (migrated.length > 0) {
						console.log('Successfully migrated sessions:', migrated);
					}
					if (errors.length > 0) {
						console.warn('Migration errors:', errors);
					}
					// After migration, load current session
					loadCurrentSession();
				});
			} else {
				// No legacy sessions, just load current session
				loadCurrentSession();
			}
		}
	);

	function loadCurrentSession() {
		getSessionInfo()
			.then((sessionInfo) => {
				if (sessionInfo?.augment) {
					console.log('Restored session from server');
					// Convert session info back to AugmentSession format for compatibility
					const augmentSession: AugmentSession = {
						accessToken: '', // Not exposed to client
						tenantUrl: sessionInfo.augment.tenantUrl,
						scopes: sessionInfo.augment.scopes,
						expiresAt: sessionInfo.augment.expiresAt
					};
					session.set(augmentSession);
				} else {
					// No valid session found, set to null to indicate logged out state
					console.log('No session found, user is logged out');
					session.set(null);
				}
			})
			.catch((error) => {
				console.error('Failed to load session:', error);
				// On error, assume logged out
				session.set(null);
			});
	}
}

// Authentication functions
export const auth = {
	/**
	 * Start the OAuth login flow
	 */
	async login(): Promise<void> {
		console.log('Starting auth flow...', browser);
		if (!browser) return;

		isLoading.set(true);
		try {
			const oauth = getOAuthClient();
			console.log('Starting auth flow...', oauth);
			await oauth.startAuthFlow();
		} catch (error) {
			isLoading.set(false);
			console.error('Login failed:', error);
			throw error;
		}
	},

	/**
	 * Handle OAuth callback
	 */
	async handleCallback(callbackUrl?: string): Promise<AugmentSession> {
		if (!browser) throw new Error('Cannot handle callback outside browser');

		isLoading.set(true);
		try {
			// Use the OAuth client to handle the callback properly with PKCE
			const oauth = getOAuthClient();
			const newSession = await oauth.handleCallback(callbackUrl);

			// Session is now stored securely on server via the token endpoint
			// Just update the client-side store with non-sensitive data
			const clientSession: AugmentSession = {
				accessToken: '', // Not stored on client
				tenantUrl: newSession.tenantUrl,
				scopes: newSession.scopes,
				expiresAt: newSession.expiresAt
			};
			session.set(clientSession);
			return clientSession;
		} catch (error) {
			console.error('Callback handling failed:', error);
			throw error;
		} finally {
			isLoading.set(false);
		}
	},

	/**
	 * Logout user
	 */
	async logout(): Promise<void> {
		if (!browser) return;

		// Clear server-side session
		await clearSession();

		// Clear local session state (OAuth client no longer stores sessions locally)
		session.set(null);
	},

	/**
	 * Check if user is authenticated
	 */
	isAuthenticated(): boolean {
		if (!browser) return false;

		const oauth = getOAuthClient();
		return oauth.isAuthenticated();
	},

	/**
	 * Refresh session from server-side storage
	 */
	async refreshSession(): Promise<void> {
		if (!browser) return;

		const sessionInfo = await getSessionInfo();
		console.log('refreshed session', sessionInfo);
		if (sessionInfo?.augment) {
			// Convert session info back to AugmentSession format for compatibility
			const augmentSession: AugmentSession = {
				accessToken: '', // Not exposed to client
				tenantUrl: sessionInfo.augment.tenantUrl,
				scopes: sessionInfo.augment.scopes,
				expiresAt: sessionInfo.augment.expiresAt
			};
			session.set(augmentSession);
		} else {
			session.set(null);
		}
	}
};
