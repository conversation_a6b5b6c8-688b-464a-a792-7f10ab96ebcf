import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

export interface DebugSettings {
	useStaging: boolean;
	exploratoryMode: boolean;
}

// Default debug settings - detect environment for staging default
function getDefaultSettings(): DebugSettings {
	// Default to staging unless we're on localhost
	const isLocalhost =
		typeof window !== 'undefined' &&
		(window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');

	return {
		useStaging: !isLocalhost, // Default to staging except on localhost
		exploratoryMode: false
	};
}

// Auth URLs based on environment
const DEV_AUTH_URL = 'https://auth-central.dev-igor.us-central1.dev.augmentcode.com';
const STAGING_AUTH_URL = 'https://auth-staging.augmentcode.com';

// Load settings from localStorage if available
function loadSettings(): DebugSettings {
	if (!browser) return getDefaultSettings();

	try {
		const stored = localStorage.getItem('debug-settings');
		if (stored) {
			const parsed = JSON.parse(stored);
			return { ...getDefaultSettings(), ...parsed };
		}
	} catch (error) {
		console.warn('Failed to load debug settings from localStorage:', error);
	}

	return getDefaultSettings();
}

// Save settings to localStorage
function saveSettings(settings: DebugSettings) {
	if (!browser) return;

	try {
		localStorage.setItem('debug-settings', JSON.stringify(settings));
	} catch (error) {
		console.warn('Failed to save debug settings to localStorage:', error);
	}
}

// Create the writable store
function createDebugSettingsStore() {
	const { subscribe, set, update } = writable<DebugSettings>(loadSettings());

	return {
		subscribe,
		set: (settings: DebugSettings) => {
			saveSettings(settings);
			set(settings);
		},
		update: (updater: (settings: DebugSettings) => DebugSettings) => {
			update((settings) => {
				const newSettings = updater(settings);
				saveSettings(newSettings);
				return newSettings;
			});
		},
		// Convenience methods
		setUseStaging: (useStaging: boolean) => {
			update((settings) => {
				const newSettings = { ...settings, useStaging };
				saveSettings(newSettings);
				return newSettings;
			});
		},
		setExploratoryMode: (exploratoryMode: boolean) => {
			update((settings) => {
				const newSettings = { ...settings, exploratoryMode };
				saveSettings(newSettings);
				return newSettings;
			});
		},
		reset: () => {
			const defaults = getDefaultSettings();
			saveSettings(defaults);
			set(defaults);
		}
	};
}

export const debugSettings = createDebugSettingsStore();

// Export individual reactive values for convenience
export const useStaging = {
	subscribe: (callback: (value: boolean) => void) => {
		return debugSettings.subscribe((settings) => callback(settings.useStaging));
	}
};

export const exploratoryMode = {
	subscribe: (callback: (value: boolean) => void) => {
		return debugSettings.subscribe((settings) => callback(settings.exploratoryMode));
	}
};

// Derived store for auth URL based on staging setting
export const authUrl = derived(debugSettings, (settings) => {
	return settings.useStaging ? STAGING_AUTH_URL : DEV_AUTH_URL;
});
