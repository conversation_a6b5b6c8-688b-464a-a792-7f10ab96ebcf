/**
 * Provider Authentication Store
 *
 * Manages OAuth authentication state for external providers (GitHub, Linear, Jira, etc.)
 * Follows the OAuth Providers Implementation Guide specification.
 */

import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import { updateProviderConnection } from '$lib/stores/providers';

export interface ProviderAuthStatus {
	providerId: string;
	name: string;
	isConfigured: boolean;
	isAuthenticationRequired: boolean;
	oauthUrl?: string;
	configuredButNeedsUpdate?: boolean;
	lastChecked?: number;
	error?: string;
}

// Remote tools API response types (aligned with actual API response)
export interface RemoteTool {
	tool_definition: Record<string, unknown>;
	remote_tool_id: number; // API returns numeric IDs like 8, 12, etc.
	availability_status: number; // API returns numeric status: 1 = TOOL_AVAILABLE, 2 = TOOL_AUTHENTICATION_REQUIRED
	tool_safety: number;
	oauth_url?: string;
}

export interface RemoteToolResponse {
	tools: RemoteTool[];
}

// Provider authentication states
export const providerAuthStates = writable<Record<string, ProviderAuthStatus>>({});
export const isCheckingProviders = writable(false);
export const lastCheckedAt = writable<number | null>(null);

// Cache for remote tools data to avoid unnecessary API calls
export const remoteToolsCache = writable<{
	data: RemoteToolResponse | null;
	lastFetched: number;
	isValid: boolean;
}>({
	data: null,
	lastFetched: 0,
	isValid: false
});

// Cache validity duration (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

// Derived reactive stores for easier consumption
export const isAnyProviderConfigured = derived(providerAuthStates, ($states) =>
	Object.values($states).some((state) => state.isConfigured)
);

export const configuredProviders = derived(providerAuthStates, ($states) =>
	Object.values($states).filter((state) => state.isConfigured)
);

export const unconfiguredProviders = derived(providerAuthStates, ($states) =>
	Object.values($states).filter((state) => !state.isConfigured)
);

// Reactive function to check if a specific provider is configured
export function createProviderConfiguredStore(providerId: string) {
	return derived(providerAuthStates, ($states) => $states[providerId]?.isConfigured || false);
}

// Simplified provider configuration - no hardcoded IDs or URLs
interface ProviderConfig {
	name: string;
	remoteToolId: number;
	hydrateEndpoint: string;
}

// Provider configurations aligned with spec
export const PROVIDER_CONFIGS: Record<string, ProviderConfig> = {
	github: {
		name: 'GitHub',
		remoteToolId: 8,
		hydrateEndpoint: '/github/hydrate-user-settings'
	},
	linear: {
		name: 'Linear',
		remoteToolId: 12,
		hydrateEndpoint: '/linear/hydrate-settings'
	}
	// jira: {
	// 	name: 'Jira',
	// 	remoteToolId: 13,
	// 	hydrateEndpoint: '/jira/hydrate-settings'
	// },

	// confluence: {
	// 	name: 'Confluence',
	// 	remoteToolId: 14,
	// 	hydrateEndpoint: '/confluence/hydrate-settings'
	// },
	// notion: {
	// 	name: 'Notion',
	// 	remoteToolId: 15,
	// 	hydrateEndpoint: '/notion/hydrate-settings'
	// },
	// supabase: {
	// 	name: 'Supabase',
	// 	remoteToolId: 16,
	// 	hydrateEndpoint: '/supabase/hydrate-settings'
	// },
	// glean: {
	// 	name: 'Glean',
	// 	remoteToolId: 17,
	// 	hydrateEndpoint: '/glean/hydrate-settings'
	// }
};

/**
 * Check authentication status for all providers using Augment's remote tools API
 */
export async function checkAllProviderAuth(): Promise<void> {
	if (!browser) return;

	try {
		isCheckingProviders.set(true);

		// Get all remote tools to check their availability status
		const response = await fetch('/api/remote-agents', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				method: 'POST',
				endpoint: '/agents/list-remote-tools',
				body: {
					tool_id_list: {
						tool_ids: Object.values(PROVIDER_CONFIGS).map((config) => config.remoteToolId)
					}
				}
			}),
			credentials: 'include' // Include cookies for authentication
		});

		if (!response.ok) {
			// Check if it's an authentication error
			if (response.status === 401) {
				console.log('Provider auth check failed: Not authenticated');
				return;
			}
			throw new Error(`Failed to fetch remote tools: ${response.status} ${response.statusText}`);
		}

		// Check if response is actually JSON
		const contentType = response.headers.get('content-type');
		if (!contentType || !contentType.includes('application/json')) {
			console.error('Provider auth API returned non-JSON response:', contentType);
			// Log the actual response for debugging
			const responseText = await response.text();
			console.error('Response body (first 500 chars):', responseText.substring(0, 500));
			return;
		}

		let data: RemoteToolResponse;
		try {
			data = await response.json();
			// console.log('🔧 Remote tools API response:', data);
		} catch (jsonError) {
			console.error('Failed to parse provider auth API response as JSON:', jsonError);
			return;
		}

		const now = Date.now();

		// Cache the remote tools data for future use
		remoteToolsCache.set({
			data,
			lastFetched: now,
			isValid: true
		});

		// Update provider states based on remote tools response
		const newStates: Record<string, ProviderAuthStatus> = {};

		Object.entries(PROVIDER_CONFIGS).forEach(([providerId, config]) => {
			const tool = data.tools.find((t) => t.remote_tool_id === config.remoteToolId);

			// Map numeric availability status to boolean flags (aligned with actual API)
			const isConfigured = tool?.availability_status === 1; // 1 = TOOL_AVAILABLE
			const isAuthenticationRequired = tool?.availability_status === 2; // 2 = TOOL_AUTHENTICATION_REQUIRED
			const hasAuthError = tool?.availability_status === 3; // 3 = TOOL_AUTHENTICATION_ERROR

			newStates[providerId] = {
				providerId,
				name: config.name,
				isConfigured,
				isAuthenticationRequired,
				oauthUrl: tool?.oauth_url,
				lastChecked: now,
				error: hasAuthError ? 'Authentication error' : undefined
			};
		});

		providerAuthStates.set(newStates);
		lastCheckedAt.set(now);
		// console.log('Provider auth states updated:', newStates);

		// Sync with existing provider system
		Object.entries(newStates).forEach(([providerId, status]) => {
			updateProviderConnection(providerId, {
				isConnected: status.isConfigured
			});
		});
	} catch (error) {
		console.error('Failed to check provider authentication:', error);

		// Set error state for all providers
		const errorStates: Record<string, ProviderAuthStatus> = {};
		Object.entries(PROVIDER_CONFIGS).forEach(([providerId, config]) => {
			errorStates[providerId] = {
				providerId,
				name: config.name,
				isConfigured: false,
				isAuthenticationRequired: true,
				error: error instanceof Error ? error.message : 'Unknown error',
				lastChecked: Date.now()
			};
		});

		providerAuthStates.set(errorStates);
	} finally {
		isCheckingProviders.set(false);
	}
}

/**
 * Get OAuth URL for a specific provider using list-remote-tools API
 * Follows the spec implementation pattern
 */
export async function getProviderOAuthUrl(providerId: string): Promise<string | null> {
	if (!browser) return null;

	const config = PROVIDER_CONFIGS[providerId as keyof typeof PROVIDER_CONFIGS];
	if (!config) return null;

	try {
		// Check if we have valid cached data
		const cache = get(remoteToolsCache);
		const now = Date.now();
		const isCacheValid = cache.isValid && cache.data && now - cache.lastFetched < CACHE_DURATION;

		let data: RemoteToolResponse;

		if (isCacheValid) {
			// console.log(`📦 Using cached remote tools data for ${providerId}`);
			data = cache.data!;
		} else {
			// console.log(`🌐 Fetching fresh remote tools data for ${providerId}`);
			// Use list-remote-tools to get OAuth URL for specific provider
			const response = await fetch('/api/remote-agents', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					method: 'POST',
					endpoint: '/agents/tools/list-remote-tools',
					body: {
						tool_id_list: {
							tool_ids: [config.remoteToolId] // Get specific tool
						}
					}
				})
			});

			if (!response.ok) {
				throw new Error(`Failed to get OAuth URL for ${config.name}: ${response.statusText}`);
			}

			data = await response.json();

			// Update cache
			remoteToolsCache.set({
				data,
				lastFetched: now,
				isValid: true
			});
		}
		const tool = data.tools.find((t) => t.remote_tool_id === config.remoteToolId);

		console.log(`🔍 Tool data for ${providerId} (ID ${config.remoteToolId}):`, {
			tool,
			hasOAuthUrl: !!tool?.oauth_url,
			availabilityStatus: tool?.availability_status,
			allTools: data.tools.map((t) => ({
				id: t.remote_tool_id,
				status: t.availability_status,
				hasOAuth: !!t.oauth_url
			}))
		});

		if (tool?.oauth_url) {
			console.log(`✅ OAuth URL found for ${providerId}:`, tool.oauth_url);
			return tool.oauth_url;
		}

		console.log(
			`❌ No OAuth URL for ${providerId}. Status: ${tool?.availability_status}, Has URL: ${!!tool?.oauth_url}`
		);
		return null;
	} catch (error) {
		console.error(`Failed to get OAuth URL for ${config.name}:`, error);
		return null;
	}
}

/**
 * Monitor OAuth popup window and check connection status
 */
function monitorOAuthPopup(popup: Window, providerId: string): void {
	// Check connection status every 2 seconds while popup is open
	const connectionCheckInterval = setInterval(async () => {
		try {
			await checkProviderAuth(providerId);
		} catch (error) {
			console.warn(`Failed to check ${providerId} auth status during popup monitoring:`, error);
		}
	}, 2000);

	// Monitor popup closure
	const popupCheckInterval = setInterval(() => {
		if (popup.closed) {
			// Popup was closed, clean up intervals
			clearInterval(connectionCheckInterval);
			clearInterval(popupCheckInterval);

			// Check connection status one final time after popup closes
			setTimeout(async () => {
				try {
					console.log(`OAuth popup closed for ${providerId}, checking final connection status`);
					await checkProviderAuth(providerId);

					// Also refresh all provider auth to ensure consistency
					await checkAllProviderAuth();
				} catch (error) {
					console.warn(`Failed to check ${providerId} auth status after popup closure:`, error);
				}
			}, 1000); // Wait 1 second to allow any final redirects to complete
		}
	}, 1000);

	// Listen for messages from the popup (OAuth redirect page)
	const messageHandler = async (event: MessageEvent) => {
		// Verify origin for security
		if (event.origin !== window.location.origin) {
			return;
		}

		if (event.data?.type === 'oauth_success') {
			console.log(`OAuth success message received for ${providerId}`);

			// Clean up intervals
			clearInterval(connectionCheckInterval);
			clearInterval(popupCheckInterval);

			// Remove message listener
			window.removeEventListener('message', messageHandler);

			// Handle the OAuth callback
			try {
				await handleProviderOAuthCallback(event.data.code, providerId);
			} catch (error) {
				console.error(`Failed to handle OAuth callback for ${providerId}:`, error);
			}
		} else if (event.data?.type === 'oauth_error') {
			console.error(`OAuth error received for ${providerId}:`, event.data.error);

			// Clean up intervals
			clearInterval(connectionCheckInterval);
			clearInterval(popupCheckInterval);

			// Remove message listener
			window.removeEventListener('message', messageHandler);
		}
	};

	// Add message listener
	window.addEventListener('message', messageHandler);

	// Clean up after 10 minutes if popup is still open (safety measure)
	setTimeout(
		() => {
			clearInterval(connectionCheckInterval);
			clearInterval(popupCheckInterval);
			window.removeEventListener('message', messageHandler);

			if (!popup.closed) {
				console.warn(
					`OAuth popup for ${providerId} still open after 10 minutes, stopping monitoring`
				);
			}
		},
		10 * 60 * 1000
	);
}

/**
 * Initiate OAuth flow for a provider
 * Follows the spec implementation pattern
 */
export async function initiateProviderOAuth(providerId: string): Promise<void> {
	if (!browser) return;

	const config = PROVIDER_CONFIGS[providerId as keyof typeof PROVIDER_CONFIGS];
	if (!config) {
		throw new Error(`Unknown provider: ${providerId}`);
	}

	// Get OAuth URL using the spec-compliant method
	const oauthUrl = await getProviderOAuthUrl(providerId);

	if (!oauthUrl) {
		throw new Error(
			`No OAuth URL available for ${config.name}. Provider may already be configured or not require authentication.`
		);
	}

	// console.log(`Starting OAuth flow for ${config.name}:`, oauthUrl);

	// Store the provider ID and current URL for callback handling
	sessionStorage.setItem('oauth_provider_id', providerId);
	sessionStorage.setItem('oauth_return_url', window.location.href);

	// Open OAuth provider in popup for both GitHub and Linear
	const popup = window.open(
		oauthUrl,
		'oauth_popup',
		'width=600,height=700,scrollbars=yes,resizable=yes'
	);

	if (!popup) {
		throw new Error('Failed to open popup window. Please allow popups for this site.');
	}

	// Monitor the popup for closure and check connection status
	monitorOAuthPopup(popup, providerId);
}

/**
 * Handle OAuth callback for a provider
 */
export async function handleProviderOAuthCallback(
	code: string,
	providerId?: string
): Promise<boolean> {
	if (!browser) return false;

	// Get provider ID from session storage if not provided
	const targetProviderId = providerId || sessionStorage.getItem('oauth_provider_id');
	if (!targetProviderId) {
		throw new Error('No provider ID found for OAuth callback');
	}

	const config = PROVIDER_CONFIGS[targetProviderId as keyof typeof PROVIDER_CONFIGS];
	if (!config) {
		throw new Error(`Unknown provider: ${targetProviderId}`);
	}

	try {
		// Exchange code for token using the direct provider callback API
		const response = await fetch('/api/auth/provider-callback', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				code,
				providerId: targetProviderId
			}),
			credentials: 'include' // Include cookies for authentication
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(
				errorData.error ||
					`Failed to exchange OAuth code for ${config.name}: ${response.statusText}`
			);
		}

		// console.log(`OAuth callback successful for ${config.name}`);

		// Clean up session storage
		sessionStorage.removeItem('oauth_provider_id');

		// Invalidate cache to ensure fresh data
		remoteToolsCache.set({
			data: null,
			lastFetched: 0,
			isValid: false
		});

		// Refresh provider auth status by checking all providers
		await checkAllProviderAuth();

		return true;
	} catch (error) {
		console.error(`OAuth callback failed for ${config.name}:`, error);
		sessionStorage.removeItem('oauth_provider_id');
		throw error;
	}
}

/**
 * Get provider auth status
 */
export function getProviderAuthStatus(providerId: string) {
	return derived(providerAuthStates, ($states) => $states[providerId]);
}

/**
 * Check if a provider is configured
 */
export function isProviderConfigured(providerId: string): boolean {
	const states = get(providerAuthStates);
	return states[providerId]?.isConfigured || false;
}

/**
 * Get all provider configurations
 */
export function getAllProviderConfigs() {
	return PROVIDER_CONFIGS;
}

/**
 * Initialize provider auth state - call this once when the app starts
 */
export async function initializeProviderAuth(): Promise<void> {
	if (typeof window !== 'undefined') {
		// console.log('🚀 initializeProviderAuth called - CLIENT SIDE');
	}
	if (!browser) {
		return;
	}

	try {
		// Add a small delay to ensure session is fully initialized
		await new Promise((resolve) => setTimeout(resolve, 100));

		// Check if we have a valid session before proceeding
		const response = await fetch('/api/auth/session-check', {
			credentials: 'include'
		});

		if (!response.ok) {
			console.log('No valid session available for provider auth initialization');
			return;
		}

		await checkAllProviderAuth();
		// console.log('Provider auth initialization complete');
	} catch (error) {
		console.log('Provider auth initialization failed:', error);
		return;
	}
}

/**
 * Refresh provider auth state - call this when you need to update the state
 */
export async function refreshProviderAuth(): Promise<void> {
	await checkAllProviderAuth();
}

/**
 * Check if provider auth data is stale (older than 5 minutes)
 */
export function isProviderAuthStale(): boolean {
	const lastChecked = get(lastCheckedAt);
	if (!lastChecked) return true;

	const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
	return lastChecked < fiveMinutesAgo;
}

/**
 * Disconnect a provider by calling the Augment API's revoke endpoint
 */
export async function disconnectProvider(providerId: string): Promise<boolean> {
	if (!browser) return false;

	const config = PROVIDER_CONFIGS[providerId as keyof typeof PROVIDER_CONFIGS];
	if (!config) {
		throw new Error(`Unknown provider: ${providerId}`);
	}

	try {
		// Call the provider disconnect API
		const response = await fetch('/api/auth/provider-disconnect', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				providerId
			}),
			credentials: 'include' // Include cookies for authentication
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(
				errorData.error || `Failed to disconnect ${config.name}: ${response.statusText}`
			);
		}

		console.log(`Successfully disconnected ${config.name}`);

		// Invalidate cache to ensure fresh data
		remoteToolsCache.set({
			data: null,
			lastFetched: 0,
			isValid: false
		});

		// Refresh provider auth status by checking all providers
		await checkAllProviderAuth();

		return true;
	} catch (error) {
		console.error(`Failed to disconnect ${config.name}:`, error);
		throw error;
	}
}

/**
 * Check authentication status for a specific provider
 */
export async function checkProviderAuth(providerId: string): Promise<void> {
	if (!browser) return;

	try {
		// sessionData = await getSessionData();
	} catch (error) {
		// console.log('No session available for provider auth check');
		return;
	}

	const config = PROVIDER_CONFIGS[providerId as keyof typeof PROVIDER_CONFIGS];
	if (!config) {
		console.warn(`Unknown provider: ${providerId}`);
		return;
	}

	try {
		// Get specific remote tool to check its availability status
		const response = await fetch('/api/remote-agents', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				method: 'POST',
				endpoint: '/agents/tools/list-remote-tools',
				body: {
					tool_id_list: {
						tool_ids: [config.remoteToolId] // Get specific tool
					}
				}
			})
		});

		if (!response.ok) {
			throw new Error(`Failed to fetch remote tool for ${config.name}: ${response.statusText}`);
		}

		const data: RemoteToolResponse = await response.json();
		const tool = data.tools.find((t) => t.remote_tool_id === config.remoteToolId);

		if (tool) {
			const now = Date.now();
			const isConfigured = tool.availability_status === 1; // 1 = TOOL_AVAILABLE
			const isAuthenticationRequired = tool.availability_status === 2; // 2 = TOOL_AUTHENTICATION_REQUIRED
			const hasAuthError = tool.availability_status === 3; // 3 = TOOL_AUTHENTICATION_ERROR

			// Update the specific provider state
			const currentStates = get(providerAuthStates);
			const updatedStates = {
				...currentStates,
				[providerId]: {
					providerId,
					name: config.name,
					isConfigured,
					isAuthenticationRequired,
					oauthUrl: tool.oauth_url,
					lastChecked: now,
					error: hasAuthError ? 'Authentication error' : undefined
				}
			};

			providerAuthStates.set(updatedStates);

			// Sync with existing provider system
			updateProviderConnection(providerId, {
				isConnected: isConfigured
			});

			// console.log(`Provider auth status updated for ${providerId}:`, updatedStates[providerId]);
		}
	} catch (error) {
		console.error(`Failed to check provider authentication for ${providerId}:`, error);

		// Set error state for this provider
		const currentStates = get(providerAuthStates);
		const updatedStates = {
			...currentStates,
			[providerId]: {
				providerId,
				name: config.name,
				isConfigured: false,
				isAuthenticationRequired: true,
				error: error instanceof Error ? error.message : 'Unknown error',
				lastChecked: Date.now()
			}
		};

		providerAuthStates.set(updatedStates);
	}
}
