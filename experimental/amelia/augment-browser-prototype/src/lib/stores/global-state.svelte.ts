/**
 * Global State Store
 *
 * Single source of truth for all application data with:
 * - Reactive accessors for components
 * - Consistent loading/error states
 * - Smart caching with TTL
 * - Type safety throughout
 */

import { writable, derived, get } from 'svelte/store';
import type {
	CleanRemoteAgent,
	CleanTriggerExecution,
	CleanChatExchangeData
} from '$lib/api/unified-client';
import { apiClient, RemoteAgentStatus } from '$lib/api/unified-client';
import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import { convertBackendEntityToUnified } from '$lib/utils/entity-conversion';
import { getRepoFromUrl } from '$lib/utils/github-url-utils';
import type { EnhancedPRInfo } from '$lib/utils/pr-detection';

// ============================================================================
// CORE TYPES
// ============================================================================

export interface AugmentSession {
	expiresAt?: number;
	isAuthenticated: boolean;
	scopes: string;
	tenantUrl: string;
}

export interface SiteAuth {
	isAuthenticated: boolean;
	isChecking: boolean;
}

export interface ChatMessage {
	id: string;
	role: 'user' | 'assistant' | 'system';
	content: string;
	timestamp: number;
	metadata?: Record<string, any>;
}

export interface OptimisticMessage {
	id: string;
	content: string;
	timestamp: number;
	type: 'user' | 'assistant';
}

export interface StreamingMessage {
	tempId: string;
	content: string;
	isComplete: boolean;
	startTime: number;
	error?: string;
}

export interface ChatSession {
	id: string;
	agentId: string;
	messages: ChatMessage[];
	exchanges: CleanChatExchangeData[];
	isStreaming: boolean;
	streamingContent: string;
	error: string | null;
	lastSequenceId?: number;
	sessionSummary?: string;
	optimisticMessage?: OptimisticMessage;
	streamingMessages: Map<string, StreamingMessage>; // keyed by tempId
}

export interface GlobalState {
	// Session & Auth
	session: AugmentSession | null;
	siteAuth: SiteAuth;

	// Core Data
	agents: Record<string, CleanRemoteAgent>;
	triggers: Record<string, NormalizedTrigger>;
	executions: Record<string, CleanTriggerExecution[]>; // keyed by triggerId
	entities: Record<string, UnifiedEntity>; // keyed by entityKey
	rawEntities: Record<string, any>; // keyed by entityKey-raw, stores raw backend entity data
	triggerMatches: Record<string, string[]>; // keyed by triggerId, values are entityKeys

	// Chat Data
	chatSessions: Record<string, ChatSession>; // keyed by sessionId
	activeChatSessionId: string | null;

	// Loading States
	loading: {
		agents: boolean;
		triggers: boolean;
		executions: Set<string>; // triggerId
		entities: Set<string>; // entityKey
		chat: Set<string>; // sessionId
		chatHistory: Set<string>; // agentId
	};

	// Error States
	errors: {
		agents: string | null;
		triggers: string | null;
		executions: Record<string, string | null>;
		entities: Record<string, string | null>;
		rawEntities: Record<string, string | null>;
		chat: Record<string, string | null>;
	};

	// Cache Metadata
	cache: {
		agents: number;
		triggers: number;
		executions: Record<string, number>;
		entities: Record<string, number>;
		rawEntities: Record<string, number>;
		chatSessions: Record<string, number>;
	};
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: GlobalState = {
	session: null,
	siteAuth: {
		isAuthenticated: false,
		isChecking: true
	},
	agents: {},
	triggers: {},
	executions: {},
	entities: {},
	rawEntities: {},
	triggerMatches: {},
	chatSessions: {},
	activeChatSessionId: null,
	loading: {
		agents: false,
		triggers: false,
		executions: new Set(),
		entities: new Set(),
		chat: new Set(),
		chatHistory: new Set()
	},
	errors: {
		agents: null,
		triggers: null,
		executions: {},
		entities: {},
		rawEntities: {},
		chat: {}
	},
	cache: {
		agents: 0,
		triggers: 0,
		executions: {},
		entities: {},
		rawEntities: {},
		chatSessions: {}
	}
};

// ============================================================================
// CORE STORE
// ============================================================================

export const globalState = writable<GlobalState>(initialState);

// ============================================================================
// REACTIVE ACCESSORS
// ============================================================================

// Session
export const session = derived(globalState, (s) => s.session);

// Core data as arrays
export const agents = derived(globalState, (s) => Object.values(s.agents));
export const triggers = derived(globalState, (s) =>
	Object.values(s.triggers).map((trigger) => ({
		...trigger,
		executions: s.executions[trigger.id] || []
	}))
);
// Normalized triggers without executions (for components that don't need execution data)
export const normalizedTriggers = derived(globalState, (s) => Object.values(s.triggers));
export const entities = derived(globalState, (s) => Object.values(s.entities));

// Loading states
export const isLoadingAgents = derived(globalState, (s) => s.loading.agents);
export const isLoadingTriggers = derived(globalState, (s) => s.loading.triggers);
export const isLoadingEntities = derived(globalState, (s) => s.loading.entities.has('all'));
export const isLoadingTriggerMatches = derived(globalState, (s) =>
	s.loading.entities.has('trigger-matches')
);

// Track initial load states for different data types
let hasTriggersInitialLoad = $state(false);
let hasAgentsInitialLoad = $state(false);
let hasEntitiesInitialLoad = $state(false);

// Check if this is the initial load for triggers
export function isInitialTriggerLoad(): boolean {
	return !hasTriggersInitialLoad;
}

// Mark triggers as having completed initial load
export function markTriggersInitialLoadComplete(): void {
	hasTriggersInitialLoad = true;
}

// Check if this is the initial load for agents
export function isInitialAgentLoad(): boolean {
	return !hasAgentsInitialLoad;
}

// Mark agents as having completed initial load
export function markAgentsInitialLoadComplete(): void {
	hasAgentsInitialLoad = true;
}

// Check if this is the initial load for entities
export function isInitialEntityLoad(): boolean {
	return !hasEntitiesInitialLoad;
}

// Mark entities as having completed initial load
export function markEntitiesInitialLoadComplete(): void {
	hasEntitiesInitialLoad = true;
}

// Derived state for showing trigger skeleton only on first load
export const shouldShowTriggerSkeleton = derived(
	[isLoadingTriggers, isLoadingTriggerMatches],
	([$isLoadingTriggers, $isLoadingTriggerMatches]) =>
		($isLoadingTriggers || $isLoadingTriggerMatches) && isInitialTriggerLoad()
);

// Derived state for showing agent skeleton only on first load
export const shouldShowAgentSkeleton = derived(
	[isLoadingAgents],
	([$isLoadingAgents]) => $isLoadingAgents && isInitialAgentLoad()
);

// Derived state for showing entity skeleton only on first load
export const shouldShowEntitySkeleton = derived(
	[isLoadingEntities],
	([$isLoadingEntities]) => $isLoadingEntities && isInitialEntityLoad()
);

// Check if any data is loading
export const isAnyDataLoading = derived(globalState, (s) => {
	return (
		s.loading.agents ||
		s.loading.triggers ||
		s.loading.executions.size > 0 ||
		s.loading.entities.size > 0 ||
		s.loading.chat.size > 0
	);
});

// Error states
export const agentsError = derived(globalState, (s) => s.errors.agents);
export const triggersError = derived(globalState, (s) => s.errors.triggers);
export const entitiesError = derived(globalState, (s) => s.errors.entities?.all || null);

// ============================================================================
// SPECIFIC ACCESSORS
// ============================================================================

/**
 * Get session auth state
 */
export function getSession() {
	return derived(globalState, (s) => s.session);
}

/**
 * Get a specific agent by ID with loading/error state
 */
export function getAgent(id: string) {
	return derived(globalState, (s) => ({
		data: s.agents[id] || null,
		loading: s.loading.agents,
		error: s.errors.agents
	}));
}

/**
 * Get a specific trigger by ID with loading/error state
 */
export function getTrigger(id: string) {
	return derived(globalState, (s) => ({
		data: s.triggers[id] || null,
		loading: s.loading.triggers,
		error: s.errors.triggers
	}));
}

/**
 * Get executions for a trigger with loading/error state
 */
export function getExecutionsForTrigger(triggerId: string) {
	return derived(globalState, (s) => ({
		data: s.executions[triggerId] || [],
		loading: s.loading.executions.has(triggerId),
		error: s.errors.executions[triggerId] || null
	}));
}

/**
 * Get entity by key with loading/error state
 */
export function getEntity(entityKey: string) {
	return derived(globalState, (s) => ({
		data: s.entities[entityKey] || null,
		loading: s.loading.entities.has(entityKey),
		error: s.errors.entities[entityKey] || null
	}));
}

/**
 * Get entity associated with an agent (pure derived store - no side effects)
 */
export function getEntityByAgent(agentId: string) {
	return derived(globalState, (s) => {
		const agent = s.agents[agentId];
		if (!agent) return { data: null, loading: false, error: null };

		const entityKey = extractEntityKeyFromAgent(agent);
		if (!entityKey) return { data: null, loading: false, error: null };

		// Check if entity exists in store
		const entity = s.entities[entityKey];
		const isLoading = s.loading.entities.has(entityKey);
		const error = s.errors.entities[entityKey] || null;

		return {
			data: entity || null,
			loading: isLoading,
			error
		};
	});
}

/**
 * Ensure entity is loaded for an agent (side effect function)
 * Call this from components to trigger loading when needed
 */
export async function ensureEntityLoadedForAgent(agentId: string): Promise<void> {
	const state = get(globalState);
	const agent = state.agents[agentId];
	if (!agent) return;

	const entityKey = extractEntityKeyFromAgent(agent);
	if (!entityKey) return;

	// Check if entity exists in store
	const entity = state.entities[entityKey];
	const isLoading = state.loading.entities.has(entityKey);
	const error = state.errors.entities[entityKey] || null;

	// If entity doesn't exist and we're not already loading it, trigger loading
	if (!entity && !isLoading && !error) {
		// Extract entity details from the entity key and agent
		const entityDetails = extractEntityDetailsFromAgent(agent);
		const repo = getRepoFromUrl(agent.githubUrl);
		if (entityDetails) {
			const { loadEntity } = await import('./data-operations.svelte');
			await loadEntity(
				entityKey,
				entityDetails.entityId,
				entityDetails.entityType,
				entityDetails.providerId,
				[repo?.owner, repo?.name.replace(/\.git$/, '')].join('/')
			);
		}
	}
}

/**
 * Get entity associated with an agent with related entities included (pure derived store - no side effects)
 * This creates a separate cache key to avoid conflicts with the basic entity loading
 */
export function getEntityByAgentWithRelated(agentId: string) {
	return derived(globalState, (s) => {
		const agent = s.agents[agentId];
		if (!agent) return { data: null, loading: false, error: null };

		const baseEntityKey = extractEntityKeyFromAgent(agent);
		if (!baseEntityKey) return { data: null, loading: false, error: null };

		// Create a separate cache key for entities with related data
		const entityKey = `${baseEntityKey}-with-related`;

		// Check if entity exists in store
		const entity = s.entities[entityKey];
		const isLoading = s.loading.entities.has(entityKey);
		const error = s.errors.entities[entityKey] || null;

		return {
			data: entity || null,
			loading: isLoading,
			error
		};
	});
}

/**
 * Ensure entity with related data is loaded for an agent (side effect function)
 * Call this from components to trigger loading when needed
 */
export async function ensureEntityWithRelatedLoadedForAgent(agentId: string): Promise<void> {
	const state = get(globalState);
	const agent = state.agents[agentId];
	if (!agent) return;

	const baseEntityKey = extractEntityKeyFromAgent(agent);
	if (!baseEntityKey) return;

	// Create a separate cache key for entities with related data
	const entityKey = `${baseEntityKey}-with-related`;

	// Check if entity exists in store
	const entity = state.entities[entityKey];
	const isLoading = state.loading.entities.has(entityKey);
	const error = state.errors.entities[entityKey] || null;

	// If entity doesn't exist and we're not already loading it, trigger loading
	if (!entity && !isLoading && !error) {
		// Extract entity details from the entity key and agent
		const entityDetails = extractEntityDetailsFromAgent(agent);
		const repo = getRepoFromUrl(agent.githubUrl);
		if (entityDetails) {
			const { loadEntity } = await import('./data-operations.svelte');
			await loadEntity(
				entityKey,
				entityDetails.entityId,
				entityDetails.entityType,
				entityDetails.providerId,
				[repo?.owner, repo?.name.replace(/\.git$/, '')].join('/'),
				false, // force
				true // includeRelatedEntities
			);
		}
	}
}

/**
 * Get raw entity data associated with an agent (pure derived store - no side effects)
 * This provides access to the raw backend entity data for enhanced information
 */
export function getRawEntityByAgent(agentId: string) {
	return derived(globalState, (s) => {
		const agent = s.agents[agentId];
		if (!agent) return { data: null, loading: false, error: null };

		const baseEntityKey = extractEntityKeyFromAgent(agent);
		if (!baseEntityKey) return { data: null, loading: false, error: null };

		// Create a separate cache key for raw entity data
		const entityKey = `${baseEntityKey}-raw`;

		// Check if raw entity exists in store
		const rawEntity = s.rawEntities[entityKey];
		const isLoading = s.loading.entities.has(entityKey);
		const error = s.errors.rawEntities[entityKey] || null;

		return {
			data: rawEntity || null,
			loading: isLoading,
			error
		};
	});
}

/**
 * Ensure raw entity data is loaded for an agent (side effect function)
 * Call this from components to trigger loading when needed
 */
export async function ensureRawEntityLoadedForAgent(agentId: string): Promise<void> {
	const state = get(globalState);
	const agent = state.agents[agentId];
	if (!agent) return;

	const baseEntityKey = extractEntityKeyFromAgent(agent);
	if (!baseEntityKey) return;

	// Create a separate cache key for raw entity data
	const entityKey = `${baseEntityKey}-raw`;

	// Check if raw entity exists in store
	const rawEntity = state.rawEntities[entityKey];
	const isLoading = state.loading.entities.has(entityKey);
	const error = state.errors.rawEntities[entityKey] || null;

	// If raw entity doesn't exist and we're not already loading it, trigger loading
	if (!rawEntity && !isLoading && !error) {
		// Extract entity details from the entity key and agent
		const entityDetails = extractEntityDetailsFromAgent(agent);
		const repo = getRepoFromUrl(agent.githubUrl);
		if (entityDetails) {
			const { loadRawEntity } = await import('./data-operations.svelte');
			await loadRawEntity(
				entityKey,
				entityDetails.entityId,
				entityDetails.entityType,
				entityDetails.providerId,
				[repo?.owner, repo?.name.replace(/\.git$/, '')].join('/')
			);
		}
	}
}

/**
 * Get detected PR entity with related entities (pure derived store - no side effects)
 */
export function getDetectedPREntityWithRelated(prInfo: EnhancedPRInfo | null) {
	return derived(globalState, (s) => {
		if (!prInfo || !prInfo.prNumber || !prInfo.repoUrl) {
			return { data: null, loading: false, error: null };
		}

		// Create entity key for detected PR
		const entityKey = `detected-pr-${prInfo.prNumber}-with-related`;

		// Check if entity exists in store
		const entity = s.entities[entityKey];
		const isLoading = s.loading.entities.has(entityKey);
		const error = s.errors.entities[entityKey] || null;

		return {
			data: entity || null,
			loading: isLoading,
			error
		};
	});
}

/**
 * Get detected PR raw entity data (pure derived store - no side effects)
 */
export function getDetectedPRRawEntity(prInfo: EnhancedPRInfo | null) {
	return derived(globalState, (s) => {
		if (!prInfo || !prInfo.prNumber || !prInfo.repoUrl) {
			return { data: null, loading: false, error: null };
		}

		// Create entity key for detected PR raw data
		const entityKey = `detected-pr-${prInfo.prNumber}-raw`;

		// Check if raw entity exists in store
		const rawEntity = s.rawEntities[entityKey];
		const isLoading = s.loading.entities.has(entityKey);
		const error = s.errors.rawEntities[entityKey] || null;

		return {
			data: rawEntity || null,
			loading: isLoading,
			error
		};
	});
}

/**
 * Ensure detected PR entity with related data is loaded (side effect function)
 */
export async function ensureDetectedPREntityWithRelatedLoaded(
	prInfo: EnhancedPRInfo | null
): Promise<void> {
	if (!prInfo || !prInfo.prNumber || !prInfo.repoUrl) return;

	const state = get(globalState);
	const entityKey = `detected-pr-${prInfo.prNumber}-with-related`;

	// Check if entity exists in store
	const entity = state.entities[entityKey];
	const isLoading = state.loading.entities.has(entityKey);
	const error = state.errors.entities[entityKey] || null;

	// If entity doesn't exist and we're not already loading it, trigger loading
	if (!entity && !isLoading && !error) {
		// Extract repository from URL
		const repoMatch = prInfo.repoUrl.match(/github\.com\/([^/]+\/[^/]+)/);
		if (repoMatch) {
			const repository = repoMatch[1].replace(/\.git$/, '');
			const { loadEntity } = await import('./data-operations.svelte');
			await loadEntity(
				entityKey,
				prInfo.prNumber.toString(),
				'pull_request',
				'github',
				repository,
				false, // force
				true // includeRelatedEntities
			);
		}
	}
}

/**
 * Ensure detected PR raw entity data is loaded (side effect function)
 */
export async function ensureDetectedPRRawEntityLoaded(
	prInfo: EnhancedPRInfo | null
): Promise<void> {
	if (!prInfo || !prInfo.prNumber || !prInfo.repoUrl) return;

	const state = get(globalState);
	const entityKey = `detected-pr-${prInfo.prNumber}-raw`;

	// Check if raw entity exists in store
	const rawEntity = state.rawEntities[entityKey];
	const isLoading = state.loading.entities.has(entityKey);
	const error = state.errors.rawEntities[entityKey] || null;

	// If raw entity doesn't exist and we're not already loading it, trigger loading
	if (!rawEntity && !isLoading && !error) {
		// Extract repository from URL
		const repoMatch = prInfo.repoUrl.match(/github\.com\/([^/]+\/[^/]+)/);
		if (repoMatch) {
			const repository = repoMatch[1].replace(/\.git$/, '');
			const { loadRawEntity } = await import('./data-operations.svelte');
			await loadRawEntity(
				entityKey,
				prInfo.prNumber.toString(),
				'pull_request',
				'github',
				repository
			);
		}
	}
}

/**
 * Get execution for an agent
 */
export function getExecutionForAgent(agentId: string) {
	return derived(globalState, (s) => {
		const execution = Object.values(s.executions)
			.flat()
			.find((exec) => exec.remoteAgentId === agentId);
		return execution || null;
	});
}

/**
 * Get trigger that created an agent
 */
export function getTriggerForAgent(agentId: string) {
	return derived(globalState, (s) => {
		const execution = Object.values(s.executions)
			.flat()
			.find((exec) => exec.remoteAgentId === agentId);
		if (!execution) return { data: null, loading: false, error: null };

		return {
			data: s.triggers[execution.triggerId] || null,
			loading: s.loading.triggers,
			error: s.errors.triggers
		};
	});
}

/**
 * Get trigger for an execution
 */
export function getTriggerForExecution(id: string) {
	return derived(globalState, (s) => {
		const execution = Object.values(s.executions)
			.flat()
			.find((exec) => exec.id === id);
		if (!execution) return { data: null, loading: false, error: null };

		return {
			data: s.triggers[execution.triggerId] || null,
			loading: s.loading.triggers,
			error: s.errors.triggers
		};
	});
}

/**
 * Get trigger for an entity
 */
export function getTriggerForEntity(entity: UnifiedEntity) {
	return derived(globalState, (s) => {
		// search trigger matches
		const entityKey = `${entity.entityType}:${entity.id}`;
		const triggerId = Object.entries(s.triggerMatches).find(([_, entityKeys]) =>
			entityKeys.includes(entityKey)
		)?.[0];
		if (!triggerId) return { data: null, loading: false, error: null };

		return {
			data: s.triggers[triggerId] || null,
			loading: s.loading.triggers,
			error: s.errors.triggers
		};
	});
}

/**
 * Get agents filtered by status
 */
export function getAgentsByStatus(status: number) {
	return derived(globalState, (s) =>
		Object.values(s.agents).filter((agent) => agent.status === status)
	);
}

/**
 * Check if there are any agents with changes/updates that should trigger favicon notification
 */
export const hasAgentsWithChanges = derived(globalState, (s) => {
	const agents = Object.values(s.agents);

	// Check for agents that have updates or are in active states
	return agents.some((agent) => {
		// Agent has explicit updates flag
		if (agent.hasUpdates) {
			return true;
		}

		// Agent is currently running (active work)
		if (agent.status === RemoteAgentStatus.AGENT_RUNNING) {
			return true;
		}

		// Agent is idle but has code changes (completed work)
		if (agent.status === RemoteAgentStatus.AGENT_IDLE && agent.changedFiles?.length > 0) {
			return true;
		}

		return false;
	});
});

/**
 * Get triggers filtered by enabled status
 */
export function getEnabledTriggers() {
	return derived(globalState, (s) =>
		Object.values(s.triggers).filter((trigger) => trigger.isEnabled)
	);
}

/**
 * Get matching entities for a specific trigger
 */
export function getMatchingEntitiesForTrigger(triggerId: string) {
	return derived(globalState, (s) => {
		const entityKeys = s.triggerMatches[triggerId] || [];
		return entityKeys
			.map((key) => s.entities[key])
			.filter(Boolean)
			.map((entity) => ({
				...entity,
				triggerId,
				triggerName: s.triggers[triggerId]?.name || 'Unknown Trigger'
			}));
	});
}

/**
 * Get all trigger-entity groups for ready-to-assign entities
 */
export function getTriggerEntityGroups() {
	return derived(globalState, (s) => {
		const groups: Array<{
			trigger: NormalizedTrigger;
			entities: Array<UnifiedEntity & { triggerId: string; triggerName: string }>;
		}> = [];

		const triggers = Object.values(s.triggers);

		for (const trigger of triggers) {
			const entityKeys = s.triggerMatches[trigger.id] || [];
			const matchingEntities = entityKeys
				.map((key) => s.entities[key])
				.filter(Boolean)
				.filter((entity) => !entity.isDismissed) // Only show non-dismissed entities by default
				.map((entity) => ({
					...entity,
					triggerId: trigger.id,
					triggerName: trigger.name
				}));

			// Show all triggers, even ones without matching entities
			groups.push({
				trigger,
				entities: matchingEntities
			});
		}

		return groups;
	});
}

/**
 * Get trigger-entity groups with option to include dismissed entities
 */
export function getTriggerEntityGroupsWithDismissed(showDismissed: boolean = false) {
	return derived(globalState, (s) => {
		const groups: Array<{
			trigger: NormalizedTrigger;
			entities: Array<UnifiedEntity & { triggerId: string; triggerName: string }>;
		}> = [];

		const triggers = Object.values(s.triggers);

		for (const trigger of triggers) {
			const entityKeys = s.triggerMatches[trigger.id] || [];
			const matchingEntities = entityKeys
				.map((key) => s.entities[key])
				.filter(Boolean)
				.filter((entity) => showDismissed || !entity.isDismissed) // Filter based on showDismissed parameter
				.map((entity) => ({
					...entity,
					triggerId: trigger.id,
					triggerName: trigger.name
				}));

			// Show all triggers, even ones without matching entities
			groups.push({
				trigger,
				entities: matchingEntities
			});
		}

		return groups;
	});
}

/**
 * Get chat session for an agent with loading/error state
 */
export function getChatSession(agentId: string) {
	return derived(globalState, (s) => {
		// Find chat session for this agent
		const session = Object.values(s.chatSessions).find((session) => session.agentId === agentId);

		return {
			data: session || null,
			loading: s.loading.chatHistory.has(agentId),
			error: s.errors.chat[agentId] || null
		};
	});
}

/**
 * Get chat exchanges for an agent
 */
export function getChatExchanges(agentId: string) {
	return derived(globalState, (s) => {
		const session = Object.values(s.chatSessions).find((session) => session.agentId === agentId);

		return session?.exchanges || [];
	});
}

// ============================================================================
// STATE UPDATE HELPERS
// ============================================================================

/**
 * Update session data
 */
export function updateSession(session: AugmentSession | null) {
	globalState.update((s) => ({ ...s, session }));
}

/**
 * Update agents data
 */
export function updateAgents(agents: CleanRemoteAgent[]) {
	const agentsMap = keyBy(agents, 'id');
	globalState.update((s) => ({
		...s,
		agents: agentsMap,
		cache: { ...s.cache, agents: Date.now() },
		errors: { ...s.errors, agents: null }
	}));
}

/**
 * Update single agent
 */
export function updateAgent(agent: CleanRemoteAgent) {
	globalState.update((s) => ({
		...s,
		agents: { ...s.agents, [agent.id]: agent }
	}));
}

/**
 * Mark agent as viewed (set hasUpdates to false)
 */
export function markAgentAsViewed(agentId: string) {
	globalState.update((s) => {
		const agent = s.agents[agentId];
		if (agent && agent.hasUpdates) {
			return {
				...s,
				agents: {
					...s.agents,
					[agentId]: { ...agent, hasUpdates: false }
				}
			};
		}
		return s;
	});
}

/**
 * Delete agent
 */
export function deleteAgent(agentId: string) {
	globalState.update((s) => {
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		const { [agentId]: _, ...remainingAgents } = s.agents;
		return {
			...s,
			agents: remainingAgents
		};
	});
}

/**
 * Update triggers data
 */
export function updateTriggers(triggers: NormalizedTrigger[]) {
	const triggersMap = keyBy(triggers, 'id');
	globalState.update((s) => ({
		...s,
		triggers: triggersMap,
		cache: { ...s.cache, triggers: Date.now() },
		errors: { ...s.errors, triggers: null }
	}));
	// Recompute trigger matches after updating triggers (debounced)
	debouncedComputeTriggerMatches();
}

/**
 * Update single trigger
 */
export function updateTrigger(trigger: NormalizedTrigger) {
	console.log('Updating trigger:', trigger);
	globalState.update((s) => ({
		...s,
		triggers: { ...s.triggers, [trigger.id]: trigger }
	}));
}

/**
 * Update executions for a trigger
 */
export function updateExecutions(triggerId: string, executions: CleanTriggerExecution[]) {
	globalState.update((s) => ({
		...s,
		executions: { ...s.executions, [triggerId]: executions },
		cache: {
			...s.cache,
			executions: { ...s.cache.executions, [triggerId]: Date.now() }
		},
		errors: {
			...s.errors,
			executions: { ...s.errors.executions, [triggerId]: null }
		}
	}));
}

/**
 * Update entity
 */
export function updateEntity(entityKey: string, entity: UnifiedEntity) {
	globalState.update((s) => ({
		...s,
		entities: { ...s.entities, [entityKey]: entity },
		cache: {
			...s.cache,
			entities: { ...s.cache.entities, [entityKey]: Date.now() }
		},
		errors: {
			...s.errors,
			entities: { ...s.errors.entities, [entityKey]: null }
		}
	}));
	// Recompute trigger matches after updating entity (debounced)
	debouncedComputeTriggerMatches();
}

/**
 * Update raw entity
 */
export function updateRawEntity(entityKey: string, rawEntity: any) {
	globalState.update((s) => ({
		...s,
		rawEntities: { ...s.rawEntities, [entityKey]: rawEntity },
		cache: {
			...s.cache,
			rawEntities: { ...s.cache.rawEntities, [entityKey]: Date.now() }
		},
		errors: {
			...s.errors,
			rawEntities: { ...s.errors.rawEntities, [entityKey]: null }
		}
	}));
}

/**
 * Set raw entity error
 */
export function setRawEntityError(entityKey: string, error: string | null) {
	globalState.update((s) => ({
		...s,
		errors: {
			...s.errors,
			rawEntities: { ...s.errors.rawEntities, [entityKey]: error }
		}
	}));
}

/**
 * Check if raw entity cache is valid
 */
export function isRawEntityCacheValid(entityKey: string): boolean {
	const state = get(globalState);
	const cacheTime = state.cache.rawEntities[entityKey];
	if (!cacheTime) return false;
	return Date.now() - cacheTime < CACHE_TTL;
}

// Cache for preventing multiple simultaneous calls
let computeTriggerMatchesPromise: Promise<void> | null = null;

// Debounced version to prevent excessive API calls
let computeTriggerMatchesTimeout: NodeJS.Timeout | null = null;
let isComputingTriggerMatches = false;
let lastComputeTime = 0;
const MIN_COMPUTE_INTERVAL = 5000; // Minimum 5 seconds between computations

function debouncedComputeTriggerMatches() {
	const now = Date.now();
	const timeSinceLastCompute = now - lastComputeTime;

	// If already computing or computed recently, don't schedule another call
	if (isComputingTriggerMatches || timeSinceLastCompute < MIN_COMPUTE_INTERVAL) {
		return;
	}

	if (computeTriggerMatchesTimeout) {
		clearTimeout(computeTriggerMatchesTimeout);
	}
	computeTriggerMatchesTimeout = setTimeout(() => {
		lastComputeTime = Date.now();
		isComputingTriggerMatches = true;
		computeTriggerMatches()
			.catch(console.error)
			.finally(() => {
				isComputingTriggerMatches = false;
			});
		computeTriggerMatchesTimeout = null;
	}, 500); // Increased debounce to 500ms
}

/**
 * Compute and update trigger matches using the matching-entities API
 */
export async function computeTriggerMatches() {
	const now = Date.now();
	const timeSinceLastCompute = now - lastComputeTime;

	// Additional safety check - don't run if called too recently
	if (timeSinceLastCompute < MIN_COMPUTE_INTERVAL) {
		return;
	}

	// If already computing, return the existing promise
	if (computeTriggerMatchesPromise) {
		return computeTriggerMatchesPromise;
	}

	lastComputeTime = now;

	// Create the promise and cache it
	computeTriggerMatchesPromise = (async () => {
		try {
			const state = get(globalState);
			// Start with existing trigger matches to preserve good data
			const newTriggerMatches: Record<string, string[]> = { ...state.triggerMatches };
			console.log('Processing', Object.keys(state.triggers).length, 'triggers');

			// Set loading state for matching entities computation
			globalState.update((s) => ({
				...s,
				loading: {
					...s.loading,
					entities: new Set([...s.loading.entities, 'trigger-matches'])
				}
			}));

			// Process all triggers in parallel using the matching-entities API
			const triggerPromises = Object.values(state.triggers).map(async (trigger) => {
				console.log('Processing trigger:', trigger.id, 'enabled:', trigger.isEnabled);

				if (!trigger.isEnabled) {
					return { triggerId: trigger.id, entityKeys: [] };
				}

				if (trigger.id === 'schedule') {
					return { triggerId: trigger.id, entityKeys: [] };
				}

				try {
					// Use the matching-entities API instead of manual filtering
					const response = await apiClient.triggers.matchingEntities(trigger.id, { limit: 50 });

					// Handle different possible response structures
					let entities: any[] = [];
					if (Array.isArray(response)) {
						entities = response;
					} else if (response && typeof response === 'object') {
						// Combine all entity arrays from the response
						const githubEntities = (response as any).githubEntities || [];
						const linearEntities = (response as any).linearEntities || [];
						const entitiesArray = (response as any).entities || [];

						entities = [...githubEntities, ...linearEntities, ...entitiesArray];
					}
					// Convert to entity keys and update the entities store
					const matchingEntityKeys: string[] = [];
					for (const entity of entities) {
						if (entity && entity.id) {
							// The entities are already converted to UnifiedEntity format by the API
							// So we can directly use the providerId and entityType properties
							const providerId = entity.providerId;
							const entityType = entity.entityType;

							if (!providerId || !entityType) {
								console.warn(`Missing providerId or entityType for entity ${entity.id}:`, entity);
								continue;
							}

							const entityKey = `${entityType}:${entity.id}`;
							matchingEntityKeys.push(entityKey);

							// Convert to UnifiedEntity format using the backend entity converter
							const unifiedEntity = convertBackendEntityToUnified(providerId, entityType, entity);

							if (unifiedEntity) {
								// Update the entity in the store if it's not already there
								globalState.update((s) => ({
									...s,
									entities: { ...s.entities, [entityKey]: unifiedEntity }
								}));
							}
						}
					}

					return { triggerId: trigger.id, entityKeys: matchingEntityKeys };
				} catch (error) {
					console.error(`Error fetching matching entities for trigger ${trigger.id}:`, error);
					// Return existing data on error or empty array
					const existingKeys = state.triggerMatches[trigger.id] || [];
					return { triggerId: trigger.id, entityKeys: existingKeys };
				}
			});

			// Wait for all trigger processing to complete in parallel
			const triggerResults = await Promise.allSettled(triggerPromises);

			// Process results and update newTriggerMatches
			triggerResults.forEach((result, index) => {
				const trigger = Object.values(state.triggers)[index];
				if (result.status === 'fulfilled') {
					const { triggerId, entityKeys } = result.value;
					// Only update if we got a successful response
					// For enabled triggers, preserve existing data if API returns empty unexpectedly
					if (entityKeys.length > 0 || !state.triggerMatches[triggerId]) {
						newTriggerMatches[triggerId] = entityKeys;
					} else {
						console.log(
							`API returned empty results for trigger ${triggerId}, preserving existing ${state.triggerMatches[triggerId]?.length || 0} matches`
						);
					}
				} else {
					console.error(`Failed to process trigger ${trigger.id}:`, result.reason);
					// Preserve existing data on error
					if (state.triggerMatches[trigger.id]) {
						newTriggerMatches[trigger.id] = state.triggerMatches[trigger.id];
					} else {
						newTriggerMatches[trigger.id] = [];
					}
				}
			});

			// Update the trigger matches and trigger entity counts in the store
			globalState.update((s) => {
				// Update triggers with their matching entity counts
				const updatedTriggers = { ...s.triggers };
				for (const [triggerId, entityKeys] of Object.entries(newTriggerMatches)) {
					if (updatedTriggers[triggerId]) {
						updatedTriggers[triggerId] = {
							...updatedTriggers[triggerId],
							matchingEntityCount: entityKeys.length
						};
					}
				}

				return {
					...s,
					triggers: updatedTriggers,
					triggerMatches: newTriggerMatches,
					loading: {
						...s.loading,
						entities: new Set([...s.loading.entities].filter((key) => key !== 'trigger-matches'))
					}
				};
			});

			// Mark initial load as complete after both triggers and matches are loaded
			markTriggersInitialLoadComplete();
		} catch (error) {
			console.error('Error in computeTriggerMatches:', error);
			// Clear loading state on error
			globalState.update((s) => ({
				...s,
				loading: {
					...s.loading,
					entities: new Set([...s.loading.entities].filter((key) => key !== 'trigger-matches'))
				}
			}));
		} finally {
			// Clear the promise cache
			computeTriggerMatchesPromise = null;
		}
	})();

	return computeTriggerMatchesPromise;
}

/**
 * Update chat session
 */
export function updateChatSession(session: ChatSession) {
	// Warn if we're losing streaming messages
	const currentState = get(globalState);
	const existingSession = currentState.chatSessions[session.id];
	if (
		existingSession?.streamingMessages &&
		existingSession.streamingMessages.size > 0 &&
		(!session.streamingMessages || session.streamingMessages.size === 0)
	) {
		console.warn('[updateChatSession] WARNING: Losing streaming messages!', {
			existingCount: existingSession.streamingMessages.size,
			newCount: session.streamingMessages?.size || 0
		});
	}

	globalState.update((s) => ({
		...s,
		chatSessions: { ...s.chatSessions, [session.id]: session }
	}));
}

/**
 * Add message to chat session
 */
export function addChatMessage(agentId: string, message: ChatMessage) {
	globalState.update((s) => {
		// Find existing session for this agent
		const existingSession = Object.values(s.chatSessions).find(
			(session) => session.agentId === agentId
		);

		if (existingSession) {
			// Update existing session
			const updatedSession = {
				...existingSession,
				messages: [...existingSession.messages, message]
			};
			return {
				...s,
				chatSessions: { ...s.chatSessions, [existingSession.id]: updatedSession }
			};
		} else {
			// Create new session
			const newSession: ChatSession = {
				id: `session-${agentId}-${Date.now()}`,
				agentId,
				messages: [message],
				exchanges: [],
				isStreaming: false,
				streamingContent: '',
				error: null,
				streamingMessages: new Map()
			};
			return {
				...s,
				chatSessions: { ...s.chatSessions, [newSession.id]: newSession }
			};
		}
	});
}

/**
 * Add optimistic message to chat session
 */
export function addOptimisticMessage(
	agentId: string,
	content: string,
	type: 'user' | 'assistant' = 'user'
) {
	globalState.update((s) => {
		// Find existing session for this agent
		const existingSession = Object.values(s.chatSessions).find(
			(session) => session.agentId === agentId
		);

		if (existingSession) {
			const optimisticMessage: OptimisticMessage = {
				id: `optimistic_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
				content,
				timestamp: Date.now(),
				type
			};

			const updatedSession = {
				...existingSession,
				optimisticMessage
			};

			return {
				...s,
				chatSessions: { ...s.chatSessions, [existingSession.id]: updatedSession }
			};
		}

		return s;
	});
}

/**
 * Clear optimistic message from chat session
 */
export function clearOptimisticMessage(agentId: string) {
	globalState.update((s) => {
		// Find existing session for this agent
		const existingSession = Object.values(s.chatSessions).find(
			(session) => session.agentId === agentId
		);

		if (existingSession && existingSession.optimisticMessage) {
			const updatedSession = {
				...existingSession,
				optimisticMessage: undefined
			};

			return {
				...s,
				chatSessions: { ...s.chatSessions, [existingSession.id]: updatedSession }
			};
		}

		return s;
	});
}

/**
 * Clear streaming state from chat session (used when interrupting agent)
 */
export function clearChatSessionStreaming(agentId: string) {
	globalState.update((s) => {
		// Find existing session for this agent
		const existingSession = Object.values(s.chatSessions).find(
			(session) => session.agentId === agentId
		);

		if (!existingSession) {
			return s;
		}

		const updatedSession = {
			...existingSession,
			isStreaming: false,
			streamingContent: '',
			streamingMessages: new Map(),
			error: null
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [existingSession.id]: updatedSession }
		};
	});
}

/**
 * Set streaming state for chat session
 */
export function setChatSessionStreaming(
	agentId: string,
	isStreaming: boolean,
	streamingContent: string = ''
) {
	globalState.update((s) => {
		// Find existing session for this agent
		const existingSession = Object.values(s.chatSessions).find(
			(session) => session.agentId === agentId
		);

		if (existingSession) {
			const updatedSession = {
				...existingSession,
				isStreaming,
				streamingContent
			};

			return {
				...s,
				chatSessions: { ...s.chatSessions, [existingSession.id]: updatedSession }
			};
		}

		return s;
	});
}

// ============================================================================
// LOADING STATE HELPERS
// ============================================================================

export function setAgentsLoading(loading: boolean) {
	globalState.update((s) => ({
		...s,
		loading: { ...s.loading, agents: loading }
	}));
}

export function setTriggersLoading(loading: boolean) {
	globalState.update((s) => ({
		...s,
		loading: { ...s.loading, triggers: loading }
	}));
}

export function setExecutionsLoading(triggerId: string, loading: boolean) {
	globalState.update((s) => {
		const newExecutions = new Set(s.loading.executions);
		if (loading) {
			newExecutions.add(triggerId);
		} else {
			newExecutions.delete(triggerId);
		}
		return {
			...s,
			loading: { ...s.loading, executions: newExecutions }
		};
	});
}

export function setEntityLoading(entityKey: string, loading: boolean) {
	globalState.update((s) => {
		const newEntities = new Set(s.loading.entities);
		if (loading) {
			newEntities.add(entityKey);
		} else {
			newEntities.delete(entityKey);
		}
		return {
			...s,
			loading: { ...s.loading, entities: newEntities }
		};
	});
}

export function setChatHistoryLoading(agentId: string, loading: boolean) {
	globalState.update((s) => {
		const newChatHistory = new Set(s.loading.chatHistory);
		if (loading) {
			newChatHistory.add(agentId);
		} else {
			newChatHistory.delete(agentId);
		}
		return {
			...s,
			loading: { ...s.loading, chatHistory: newChatHistory }
		};
	});
}

// ============================================================================
// ERROR STATE HELPERS
// ============================================================================

export function setAgentsError(error: string | null) {
	globalState.update((s) => ({
		...s,
		errors: { ...s.errors, agents: error }
	}));
}

export function setTriggersError(error: string | null) {
	globalState.update((s) => ({
		...s,
		errors: { ...s.errors, triggers: error }
	}));
}

export function setExecutionsError(triggerId: string, error: string | null) {
	globalState.update((s) => ({
		...s,
		errors: {
			...s.errors,
			executions: { ...s.errors.executions, [triggerId]: error }
		}
	}));
}

export function setEntityError(entityKey: string, error: string | null) {
	globalState.update((s) => ({
		...s,
		errors: {
			...s.errors,
			entities: { ...s.errors.entities, [entityKey]: error }
		}
	}));
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

function keyBy<T>(array: T[], key: keyof T): Record<string, T> {
	return array.reduce(
		(acc, item) => {
			acc[String(item[key])] = item;
			return acc;
		},
		{} as Record<string, T>
	);
}

/**
 * Get all trigger executions flattened into a single array
 */
export function getFlatTriggerExecutions() {
	return derived(globalState, (s) => {
		// Flatten all executions from all triggers into a single array
		return Object.values(s.executions).flat() as CleanTriggerExecution[];
	});
}

export function extractEntityKeyFromAgent(agent: CleanRemoteAgent): string | null {
	// Parse the agent's title to extract entity information
	// Look for patterns like "Entity Type: GITHUB_PULL_REQUEST Entity Id: 123"
	if (!agent.sessionSummary) return null;

	const entityPattern = /Entity Type:\s*([A-Z_]+)\s+Entity Id:\s*([\w-]+)/i;
	const match = agent.sessionSummary.match(entityPattern);

	if (!match) return null;

	const [, entityType, entityId] = match;

	// Convert entity type to a consistent format and create entity key
	// This should match how entities are keyed in the entities store
	// Map the full entity type names to simple types
	const entityTypeMap: Record<string, string> = {
		GITHUB_ENTITY_TYPE_PULL_REQUEST: 'pull_request',
		GITHUB_ENTITY_TYPE_WORKFLOW_RUN: 'workflow_run',
		GITHUB_ENTITY_TYPE_ISSUE: 'issue',
		LINEAR_ENTITY_TYPE_ISSUE: 'issue',
		JIRA_ENTITY_TYPE_ISSUE: 'issue'
	};

	const normalizedType = entityTypeMap[entityType] || entityType.toLowerCase();
	return `${normalizedType}:${entityId}`;
}

export function extractEntityDetailsFromAgent(agent: CleanRemoteAgent): {
	entityId: string;
	entityType: string;
	providerId: string;
} | null {
	// Parse the agent's sessionSummary to extract entity information
	if (!agent.sessionSummary) return null;

	const entityPattern = /Entity Type:\s*([A-Z_]+)\s+Entity Id:\s*([\w-]+)/i;
	const match = agent.sessionSummary.match(entityPattern);

	if (!match) return null;

	const [, entityType, entityId] = match;

	// Map entity types to provider IDs and normalized types
	const entityTypeMap: Record<string, { providerId: string; entityType: string }> = {
		GITHUB_ENTITY_TYPE_PULL_REQUEST: { providerId: 'github', entityType: 'pull_request' },
		GITHUB_ENTITY_TYPE_WORKFLOW_RUN: { providerId: 'github', entityType: 'workflow_run' },
		GITHUB_ENTITY_TYPE_ISSUE: { providerId: 'github', entityType: 'issue' },
		LINEAR_ENTITY_TYPE_ISSUE: { providerId: 'linear', entityType: 'issue' },
		JIRA_ENTITY_TYPE_ISSUE: { providerId: 'jira', entityType: 'issue' },
		unknown: { providerId: 'schedule', entityType: 'schedule' }
	};

	const mapping = entityTypeMap[entityType];
	if (!mapping) {
		console.warn('Unknown entity type:', entityType);
		return null;
	}

	return {
		entityId,
		entityType: mapping.entityType,
		providerId: mapping.providerId
	};
}

// ============================================================================
// SITE AUTH HELPERS
// ============================================================================

export const siteAuthStore = derived(globalState, (s) => s.siteAuth);

export function markSiteAuthenticated() {
	globalState.update((s) => ({
		...s,
		siteAuth: { isAuthenticated: true, isChecking: false }
	}));
}

export function checkSiteAuthentication() {
	// Check if already authenticated via cookie
	if (typeof window !== 'undefined') {
		fetch('/api/site-auth/check', {
			credentials: 'include'
		})
			.then((res) => res.json())
			.then((data) => {
				globalState.update((s) => ({
					...s,
					siteAuth: { isAuthenticated: data.authenticated, isChecking: false }
				}));
			})
			.catch(() => {
				globalState.update((s) => ({
					...s,
					siteAuth: { isAuthenticated: false, isChecking: false }
				}));
			});
	}
}

// ============================================================================
// STREAMING MESSAGE HELPERS
// ============================================================================

/**
 * Add a streaming message placeholder
 */
export function addStreamingMessage(agentId: string, tempId: string): void {
	console.log('[addStreamingMessage] Adding streaming message:', { agentId, tempId });
	globalState.update((s) => {
		const session = Object.values(s.chatSessions).find((sess) => sess.agentId === agentId);
		if (!session) {
			console.log('[addStreamingMessage] No session found for agent:', agentId);
			return s;
		}

		const streamingMessage: StreamingMessage = {
			tempId,
			content: '',
			isComplete: false,
			startTime: Date.now()
		};

		const updatedStreamingMessages = new Map(session.streamingMessages);
		updatedStreamingMessages.set(tempId, streamingMessage);
		console.log(
			'[addStreamingMessage] Updated streaming messages count:',
			updatedStreamingMessages.size
		);

		const updatedSession = {
			...session,
			streamingMessages: updatedStreamingMessages,
			isStreaming: true
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [session.id]: updatedSession }
		};
	});
}

/**
 * Update streaming message content
 */
export function updateStreamingMessage(agentId: string, tempId: string, content: string): void {
	globalState.update((s) => {
		const session = Object.values(s.chatSessions).find((sess) => sess.agentId === agentId);
		if (!session) return s;

		const streamingMessage = session.streamingMessages.get(tempId);
		if (!streamingMessage) return s;

		const updatedStreamingMessages = new Map(session.streamingMessages);
		updatedStreamingMessages.set(tempId, {
			...streamingMessage,
			content: streamingMessage.content + content
		});

		const updatedSession = {
			...session,
			streamingMessages: updatedStreamingMessages
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [session.id]: updatedSession }
		};
	});
}

/**
 * Complete a streaming message and convert to regular message
 */
export function completeStreamingMessage(
	agentId: string,
	tempId: string,
	finalId: string,
	metadata?: Record<string, any>
): void {
	globalState.update((s) => {
		const session = Object.values(s.chatSessions).find((sess) => sess.agentId === agentId);
		if (!session) return s;

		const streamingMessage = session.streamingMessages.get(tempId);
		if (!streamingMessage) return s;

		// Create the final message
		const finalMessage: ChatMessage = {
			id: finalId,
			role: 'assistant',
			content: streamingMessage.content,
			timestamp: Date.now(),
			metadata
		};

		// Remove from streaming and add to messages
		const updatedStreamingMessages = new Map(session.streamingMessages);
		updatedStreamingMessages.delete(tempId);

		const updatedSession = {
			...session,
			messages: [...session.messages, finalMessage],
			streamingMessages: updatedStreamingMessages,
			isStreaming: updatedStreamingMessages.size > 0
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [session.id]: updatedSession }
		};
	});
}

/**
 * Handle streaming error
 */
export function setStreamingError(agentId: string, tempId: string, error: string): void {
	globalState.update((s) => {
		const session = Object.values(s.chatSessions).find((sess) => sess.agentId === agentId);
		if (!session) return s;

		const streamingMessage = session.streamingMessages.get(tempId);
		if (!streamingMessage) return s;

		const updatedStreamingMessages = new Map(session.streamingMessages);
		updatedStreamingMessages.set(tempId, {
			...streamingMessage,
			error,
			isComplete: true
		});

		const updatedSession = {
			...session,
			streamingMessages: updatedStreamingMessages,
			error
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [session.id]: updatedSession }
		};
	});
}
