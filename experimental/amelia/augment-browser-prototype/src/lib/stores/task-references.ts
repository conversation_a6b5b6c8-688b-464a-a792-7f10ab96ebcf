import { writable } from 'svelte/store';
import type { TaskReference, CreateTaskReferenceInput, UpdateTaskReferenceInput } from '$lib/types';

// Store for task references by task ID
export const taskReferences = writable<Record<string, TaskReference[]>>({});

/**
 * Fetch references for a specific task
 */
export async function fetchTaskReferences(taskId: string): Promise<TaskReference[]> {
	try {
		const response = await fetch(`/api/tasks/${taskId}/references`);

		if (!response.ok) {
			throw new Error('Failed to fetch task references');
		}

		const references = await response.json();

		// Update the store
		taskReferences.update(current => ({
			...current,
			[taskId]: references
		}));

		return references;
	} catch (error) {
		console.error('Error fetching task references:', error);
		throw error;
	}
}

/**
 * Add a new reference to a task
 */
export async function addTaskReference(taskId: string, referenceData: CreateTaskReferenceInput): Promise<TaskReference> {
	try {
		const response = await fetch(`/api/tasks/${taskId}/references`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(referenceData)
		});

		if (!response.ok) {
			throw new Error('Failed to add task reference');
		}

		const newReference = await response.json();

		// Update the store
		taskReferences.update(current => ({
			...current,
			[taskId]: [...(current[taskId] || []), newReference]
		}));

		return newReference;
	} catch (error) {
		console.error('Error adding task reference:', error);
		throw error;
	}
}

/**
 * Update a task reference
 */
export async function updateTaskReference(
	taskId: string,
	referenceId: string,
	updateData: UpdateTaskReferenceInput
): Promise<TaskReference> {
	try {
		const response = await fetch(`/api/tasks/${taskId}/references/${referenceId}`, {
			method: 'PATCH',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify(updateData)
		});

		if (!response.ok) {
			throw new Error('Failed to update task reference');
		}

		const updatedReference = await response.json();

		// Update the store
		taskReferences.update(current => ({
			...current,
			[taskId]: (current[taskId] || []).map(ref =>
				ref.id === referenceId ? updatedReference : ref
			)
		}));

		return updatedReference;
	} catch (error) {
		console.error('Error updating task reference:', error);
		throw error;
	}
}

/**
 * Delete a task reference
 */
export async function deleteTaskReference(taskId: string, referenceId: string): Promise<void> {
	try {
		const response = await fetch(`/api/tasks/${taskId}/references/${referenceId}`, {
			method: 'DELETE'
		});

		if (!response.ok) {
			throw new Error('Failed to delete task reference');
		}

		// Update the store
		taskReferences.update(current => ({
			...current,
			[taskId]: (current[taskId] || []).filter(ref => ref.id !== referenceId)
		}));
	} catch (error) {
		console.error('Error deleting task reference:', error);
		throw error;
	}
}

/**
 * Get references for a specific task from the store
 */
export function getTaskReferences(taskId: string): TaskReference[] {
	let references: TaskReference[] = [];
	taskReferences.subscribe(current => {
		references = current[taskId] || [];
	})();
	return references;
}

/**
 * Clear references for a specific task from the store
 */
export function clearTaskReferences(taskId: string): void {
	taskReferences.update(current => {
		const updated = { ...current };
		delete updated[taskId];
		return updated;
	});
}

/**
 * Clear all references from the store
 */
export function clearAllTaskReferences(): void {
	taskReferences.set({});
}
