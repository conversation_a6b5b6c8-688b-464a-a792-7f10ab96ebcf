/**
 * Data Operations Module
 *
 * Centralized data loading with:
 * - Smart caching with TTL
 * - Request deduplication
 * - Consistent error handling
 * - Session management
 */

import {
	apiClient,
	APIError,
	ChatRequestNodeType,
	convertRemoteAgentFromAPI,
	convertChatExchangeDataFromAPI,
	convertChatExchangeUpdateFromAPI,
	RemoteAgentStatus,
	RemoteAgentWorkspaceStatus,
	toCamelCase,
	toSnakeCase,
	type CleanRemoteAgent,
	type CleanChatExchangeData,
	type CleanChatExchangeUpdate,
	type AgentListUpdate,
	AgentListUpdateType as AgentListUpdateTypeEnum
} from '$lib/api/unified-client';
import { mapFieldsToBackend } from '$lib/utils/trigger-form-utils';
import {
	convertDefaultTriggerToConfiguration,
	getMissingDefaultTriggers
} from '$lib/default-triggers';
import { getWorkspaceStatusText } from '$lib/utils/agent-status-indicators.svelte';
import {
	cancelPendingMessagesForAgent,
	createOptimisticMessage,
	getTimeoutErrorMessage,
	getTimeoutForAgent,
	handleStreamConnectionLoss,
	handleUserCancellation,
	markMessageFailed,
	tryDeliverByContentMatch,
	updateAgentStatusForPendingMessages
} from '$lib/utils/optimistic-messages';
import {
	cleanupSequenceTracker,
	initializeSequenceTracker,
	processSequenceId
} from '$lib/utils/sequence-tracking';
import { normalizeTriggers, normalizeTrigger } from '$lib/utils/trigger-normalization';
import { sanitizeErrorMessage } from '$lib/utils/error-messages';
import { get } from 'svelte/store';
import {
	addChatMessage,
	clearOptimisticMessage,
	clearChatSessionStreaming,
	getSession,
	globalState,
	setAgentsError,
	setAgentsLoading,
	setChatHistoryLoading,
	setChatSessionStreaming,
	setEntityError,
	setEntityLoading,
	setExecutionsError,
	setExecutionsLoading,
	setTriggersError,
	setTriggersLoading,
	updateAgent,
	updateAgents,
	updateChatSession,
	updateEntity,
	updateRawEntity,
	setRawEntityError,
	isRawEntityCacheValid,
	updateExecutions,
	updateSession,
	updateTriggers,
	updateTrigger,
	markTriggersInitialLoadComplete,
	markAgentsInitialLoadComplete,
	markEntitiesInitialLoadComplete
} from './global-state.svelte';
import { updateAgentLimitsConfig } from './agent-limits.svelte';

// Types for streaming updates
interface StreamingUpdate {
	type: number;
	sequenceId?: number;
	exchange?: CleanChatExchangeData;
	exchangeUpdate?: CleanChatExchangeUpdate;
	agent?: any;
}

// ============================================================================
// CONFIGURATION
// ============================================================================

const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const ENTITY_CACHE_TTL = 10 * 60 * 1000; // 10 minutes for entities

// Track ongoing requests to prevent duplicates
const ongoingRequests = new Map<string, Promise<void>>();

// ============================================================================
// SESSION MANAGEMENT
// ============================================================================

/**
 * Initialize session from server and load initial data
 */
export async function initializeSession(): Promise<void> {
	const requestKey = 'initialize-session';

	// Prevent multiple simultaneous session initializations
	if (ongoingRequests.has(requestKey)) {
		await ongoingRequests.get(requestKey);
		return;
	}

	const requestPromise = (async () => {
		try {
			const response = await fetch('/api/auth/session');
			if (response.ok) {
				const sessionData = await response.json();
				updateSession(sessionData.augment || null);

				// Load initial data if authenticated
				if (sessionData.augment?.isAuthenticated) {
					await Promise.all([loadAgents(), loadTriggers(), loadEntities()]);
				} else {
					// Stop agent list streaming if not authenticated
					stopAgentListStream();
				}
			} else {
				updateSession(null);
				stopAgentListStream();
			}
		} catch (error) {
			console.error('Failed to initialize session:', error);
			updateSession(null);
			stopAgentListStream();
		} finally {
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Check if user is authenticated
 */
export function isAuthenticated(): boolean {
	const sessionRaw = getSession();
	const session = get(sessionRaw);
	return !!session?.isAuthenticated;
}

// ============================================================================
// CACHE VALIDATION
// ============================================================================

function isCacheValid(type: 'agents' | 'triggers', customTTL?: number): boolean {
	const state = get(globalState);
	const lastFetch = state.cache[type];
	const ttl = customTTL || CACHE_TTL;

	return lastFetch > 0 && Date.now() - lastFetch < ttl;
}

function isExecutionsCacheValid(triggerId: string): boolean {
	const state = get(globalState);
	const lastFetch = state.cache.executions[triggerId];

	return lastFetch > 0 && Date.now() - lastFetch < CACHE_TTL;
}

function isEntityCacheValid(entityKey: string): boolean {
	const state = get(globalState);
	const lastFetch = state.cache.entities[entityKey];

	return lastFetch > 0 && Date.now() - lastFetch < ENTITY_CACHE_TTL;
}

// ============================================================================
// AGENTS OPERATIONS
// ============================================================================

/**
 * Load all agents with smart caching
 */
export async function loadAgents(force = false): Promise<void> {
	if (!isAuthenticated()) {
		setAgentsError('Authentication required');
		return;
	}

	if (!force && isCacheValid('agents')) {
		return; // Cache is still valid
	}

	const requestKey = 'agents';
	if (ongoingRequests.has(requestKey)) {
		await ongoingRequests.get(requestKey);
		return; // Request already in progress
	}

	const requestPromise = (async () => {
		setAgentsLoading(true);
		setAgentsError(null);

		try {
			const response = await apiClient.agents.list();
			updateAgents(response.remoteAgents || []);

			// Update agent limits from API response if available
			if (response.maxRemoteAgents !== undefined || response.maxActiveRemoteAgents !== undefined) {
				updateAgentLimitsConfig({
					...(response.maxActiveRemoteAgents !== undefined && {
						maxActiveAgents: response.maxActiveRemoteAgents
					}),
					...(response.maxRemoteAgents !== undefined && {
						maxTotalAgents: response.maxRemoteAgents
					})
				});
			}

			// Start agent list streaming if not already active
			if (!agentListStreamActive) {
				startAgentListStream();
			}

			// Mark agents initial load as complete
			markAgentsInitialLoadComplete();
		} catch (error) {
			const rawMessage = error instanceof APIError ? error.message : 'Failed to load agents';
			const message = sanitizeErrorMessage(rawMessage);
			setAgentsError(message);
			console.error('Failed to load agents:', error);
		} finally {
			setAgentsLoading(false);
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Load chat history for multiple agents (for preview purposes)
 * This is used on the agents list page to show meaningful previews
 */
export async function loadAgentPreviews(agentIds: string[]): Promise<void> {
	if (!isAuthenticated() || agentIds.length === 0) {
		return;
	}

	// Load chat history for each agent in parallel, but limit concurrency
	const BATCH_SIZE = 5; // Load 5 agents at a time to avoid overwhelming the API

	for (let i = 0; i < agentIds.length; i += BATCH_SIZE) {
		const batch = agentIds.slice(i, i + BATCH_SIZE);
		const promises = batch.map((agentId) =>
			loadChatHistory(agentId, false).catch((error) => {
				console.warn(`Failed to load chat history for agent ${agentId}:`, error);
				// Don't throw - we want to continue loading other agents
			})
		);

		await Promise.all(promises);
	}
}

/**
 * Create a new agent
 */
export async function createAgent(agentData: any): Promise<string | null> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	try {
		const response = await apiClient.agents.create(agentData);

		// Refresh agents list to include new agent
		await loadAgents(true);

		return response.remoteAgentId;
	} catch (error) {
		const message = error instanceof APIError ? error.message : 'Failed to create agent';
		throw new Error(message);
	}
}

/**
 * Update agent title with optimistic updates
 */
export async function updateAgentTitle(agentId: string, title: string): Promise<void> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	// Get current agent state for potential rollback
	const state = get(globalState);
	const agent = state.agents[agentId];
	if (!agent) {
		throw new Error('Agent not found');
	}

	const originalTitle = agent.title;

	try {
		// Optimistic update - update UI immediately
		updateAgent({ ...agent, title });

		// Make API call
		await apiClient.agents.updateTitle(agentId, title);
	} catch (error) {
		// Rollback optimistic update on failure
		updateAgent({ ...agent, title: originalTitle });

		const message = error instanceof APIError ? error.message : 'Failed to update agent title';
		throw new Error(message);
	}
}

/**
 * Delete an agent
 */
export async function deleteAgent(agentId: string): Promise<void> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	try {
		// Handle user cancellation - clean up pending messages before deletion
		handleUserCancellation(agentId);

		await apiClient.agents.delete(agentId);

		// Refresh agents list
		await loadAgents(true);
	} catch (error) {
		const message = error instanceof APIError ? error.message : 'Failed to delete agent';
		throw new Error(message);
	}
}

export async function deleteAgents(agentIds: string[]): Promise<void> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	try {
		// Handle user cancellation - clean up pending messages before deletion
		for (const agentId of agentIds) {
			handleUserCancellation(agentId);
		}

		const promises = agentIds.map((agentId) => apiClient.agents.delete(agentId));
		await Promise.all(promises);

		// Refresh agents list
		await loadAgents(true);
	} catch (error) {
		const message = error instanceof APIError ? error.message : 'Failed to delete agents';
		throw new Error(message);
	}
}

// ============================================================================
// TRIGGERS OPERATIONS
// ============================================================================

/**
 * Load all triggers with smart caching
 */
export async function loadTriggers(force = false): Promise<void> {
	if (!isAuthenticated()) {
		setTriggersError('Authentication required');
		return;
	}

	if (!force && isCacheValid('triggers')) {
		return; // Cache is still valid
	}

	const requestKey = 'triggers';
	if (ongoingRequests.has(requestKey)) {
		await ongoingRequests.get(requestKey);
		return; // Request already in progress
	}

	// Create and store the promise
	const requestPromise = (async () => {
		setTriggersLoading(true);
		setTriggersError(null);

		try {
			// Always load user's actual triggers
			const triggers = await apiClient.triggers.list();
			const normalizedTriggers = normalizeTriggers(triggers);
			updateTriggers(normalizedTriggers);

			// Check for missing default triggers using the global state
			const missingTriggers = getMissingDefaultTriggers(normalizedTriggers);

			if (missingTriggers.length > 0) {
				console.log(
					`Creating ${missingTriggers.length} missing default triggers:`,
					missingTriggers.map((t) => t.name)
				);

				// Create each missing default trigger via the API
				for (const defaultTrigger of missingTriggers) {
					try {
						const triggerConfig = convertDefaultTriggerToConfiguration(defaultTrigger);
						await apiClient.triggers.create(triggerConfig);
						console.log(`Created default trigger: ${defaultTrigger.name}`);
					} catch (error) {
						console.error(`Failed to create default trigger ${defaultTrigger.name}:`, error);
					}
				}

				// Reload triggers after creating missing defaults and update global state
				const updatedTriggers = await apiClient.triggers.list();
				const updatedNormalizedTriggers = normalizeTriggers(updatedTriggers);
				updateTriggers(updatedNormalizedTriggers);
			}

			// After loading triggers, automatically load executions for each trigger
			// Use the final normalized triggers (which may include newly created ones)
			const finalTriggers =
				missingTriggers.length > 0 ? Object.values(get(globalState).triggers) : normalizedTriggers;

			const executionPromises = finalTriggers.map((trigger: any) =>
				loadExecutions(trigger.id).catch((error) => {
					console.warn(`Failed to load executions for trigger ${trigger.id}:`, error);
				})
			);

			// Wait for all execution loads to complete (but don't fail if some fail)
			await Promise.allSettled(executionPromises);

			// Mark triggers initial load as complete
			markTriggersInitialLoadComplete();
		} catch (error) {
			const message = error instanceof APIError ? error.message : 'Failed to load triggers';
			setTriggersError(message);
			console.error('Failed to load triggers:', error);
		} finally {
			setTriggersLoading(false);
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Load executions for a specific trigger
 */
export async function loadExecutions(triggerId: string, force = false): Promise<void> {
	if (!isAuthenticated()) {
		setExecutionsError(triggerId, 'Authentication required');
		return;
	}

	if (!force && isExecutionsCacheValid(triggerId)) {
		return; // Cache is still valid
	}

	const requestKey = `executions-${triggerId}`;
	if (ongoingRequests.has(requestKey)) {
		await ongoingRequests.get(requestKey);
		return; // Request already in progress
	}

	const requestPromise = (async () => {
		setExecutionsLoading(triggerId, true);
		setExecutionsError(triggerId, null);

		try {
			const executions = await apiClient.triggers.executions(triggerId);
			updateExecutions(triggerId, executions.executions);
		} catch (error) {
			const message = error instanceof APIError ? error.message : 'Failed to load executions';
			setExecutionsError(triggerId, message);
			console.error(`Failed to load executions for trigger ${triggerId}:`, error);
		} finally {
			setExecutionsLoading(triggerId, false);
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Execute a trigger manually
 */
export async function executeTrigger(
	triggerId: string,
	entityId?: string,
	extraPrompt?: string
): Promise<string | null> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	try {
		const result = await apiClient.triggers.execute(triggerId, entityId, extraPrompt);

		// Refresh executions for this trigger
		await loadExecutions(triggerId, true);

		return result.remoteAgentId || null;
	} catch (error) {
		const message = error instanceof APIError ? error.message : 'Failed to execute trigger';
		throw new Error(message);
	}
}

/**
 * Create a new trigger
 */
export async function createTriggerAndSync(triggerConfig: any): Promise<any> {
	// Create a unique request key based on trigger config
	const requestKey = `create-trigger-${JSON.stringify(triggerConfig)}`;

	// Prevent duplicate requests
	if (ongoingRequests.has(requestKey)) {
		console.warn('Duplicate trigger creation request prevented:', triggerConfig.name);
		await ongoingRequests.get(requestKey);
		return;
	}

	const requestPromise = (async () => {
		try {
			// Create trigger via API
			const result = await apiClient.triggers.create(triggerConfig);

			// Update the global store with the new trigger instead of refetching all
			if (result) {
				const normalizedTrigger = normalizeTrigger(result.trigger);
				updateTrigger(normalizedTrigger);
				loadMatchingEntities(normalizedTrigger.id);
			}

			return result;
		} catch (error) {
			console.error('Failed to create trigger:', error);
			throw error;
		} finally {
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Update an existing trigger
 */
export async function updateTriggerAndSync(triggerId: string, updates: any): Promise<any> {
	try {
		// Map frontend field names to backend field names using conditionsConfig as source of truth
		const backendUpdates = mapFieldsToBackend(updates);

		// Update trigger via API
		const result = await apiClient.triggers.update(triggerId, backendUpdates);

		// Update the global store with the updated trigger instead of refetching all
		if (result) {
			const normalizedTrigger = normalizeTrigger(result.trigger);
			updateTrigger(normalizedTrigger);
			loadMatchingEntities(normalizedTrigger.id);
		}

		return result;
	} catch (error) {
		console.error('Failed to update trigger:', error);
		throw error;
	}
}

/**
 * Delete a trigger
 */
export async function deleteTriggerAndSync(triggerId: string): Promise<void> {
	try {
		// Delete via API
		await apiClient.triggers.delete(triggerId);

		// Refresh triggers to get updated list from server
		await loadTriggers(true);
	} catch (error) {
		console.error('Failed to delete trigger:', error);
		throw error;
	}
}

// ============================================================================
// ENTITIES OPERATIONS
// ============================================================================

/**
 * Load all entities from all providers with smart caching
 */
export async function loadEntities(force = false): Promise<void> {
	if (!isAuthenticated()) {
		// Set entities error in global state
		globalState.update((s) => ({
			...s,
			errors: {
				...s.errors,
				entities: { ...s.errors.entities, all: 'Authentication required' }
			}
		}));
		return;
	}

	// Check cache if not forcing
	if (!force && isEntityCacheValid('all')) {
		return;
	}

	// Load triggers first, which will automatically trigger entity loading
	// through the debounced trigger matching system
	try {
		// Set loading state
		globalState.update((s) => ({
			...s,
			loading: {
				...s.loading,
				entities: new Set(['all'])
			},
			errors: {
				...s.errors,
				entities: { ...s.errors.entities, all: null }
			}
		}));

		// Load triggers if not already loaded
		await loadTriggers(force);

		// load entities for each trigger
		const triggerPromises = Object.values(get(globalState).triggers).map(async (trigger) => {
			await loadMatchingEntities(trigger.id);
		});
		await Promise.allSettled(triggerPromises);

		// Mark entities cache as valid since trigger matching will handle the actual loading
		globalState.update((s) => ({
			...s,
			cache: {
				...s.cache,
				entities: { ...s.cache.entities, all: Date.now() }
			}
		}));

		// Mark entities initial load as complete
		markEntitiesInitialLoadComplete();
	} catch (error) {
		console.error('Failed to initiate entity loading:', error);
		globalState.update((s) => ({
			...s,
			errors: {
				...s.errors,
				entities: {
					...s.errors.entities,
					all: error instanceof Error ? error.message : 'Failed to load entities'
				}
			}
		}));
	} finally {
		// Clear loading state
		globalState.update((s) => ({
			...s,
			loading: {
				...s.loading,
				entities: new Set()
			}
		}));
	}
}

/**
 * Load entity with deduplication and caching
 */
export async function loadEntity(
	entityKey: string,
	entityId: string,
	entityType: string,
	providerId: string,
	repository?: string,
	force = false,
	includeRelatedEntities = false
): Promise<void> {
	if (!isAuthenticated()) {
		setEntityError(entityKey, 'Authentication required');
		return;
	}

	// Check if entity already exists and cache is valid
	const state = get(globalState);
	if (!force && state.entities[entityKey] && isEntityCacheValid(entityKey)) {
		return;
	}

	const requestKey = `entity-${entityKey}`;
	if (ongoingRequests.has(requestKey)) {
		await ongoingRequests.get(requestKey);
		return; // Request already in progress
	}

	const requestPromise = (async () => {
		setEntityLoading(entityKey, true);
		setEntityError(entityKey, null);

		try {
			// Transform the entity ID for the API call if needed (remove pr- prefix for GitHub PRs)
			const apiEntityId = transformEntityIdForRetrieval(entityId, entityType, providerId);

			const entity = await apiClient.entities.get(
				apiEntityId,
				entityType,
				providerId,
				repository,
				includeRelatedEntities
			);
			updateEntity(entityKey, entity);
		} catch (error) {
			const message =
				error instanceof APIError
					? error.message
					: `Failed to load entity ${entityType}:${entityId}`;
			setEntityError(entityKey, message);
			console.error(
				`Failed to load entity ${entityKey} (${providerId}:${entityType}:${entityId}):`,
				error
			);
		} finally {
			setEntityLoading(entityKey, false);
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Load raw entity data with deduplication and caching
 */
export async function loadRawEntity(
	entityKey: string,
	entityId: string,
	entityType: string,
	providerId: string,
	repository?: string,
	force = false
): Promise<void> {
	if (!isAuthenticated()) {
		setRawEntityError(entityKey, 'Authentication required');
		return;
	}

	// Check if raw entity already exists and cache is valid
	const state = get(globalState);
	if (!force && state.rawEntities[entityKey] && isRawEntityCacheValid(entityKey)) {
		return;
	}

	const requestKey = `raw-entity-${entityKey}`;
	if (ongoingRequests.has(requestKey)) {
		await ongoingRequests.get(requestKey);
		return; // Request already in progress
	}

	const requestPromise = (async () => {
		setEntityLoading(entityKey, true);
		setRawEntityError(entityKey, null);

		try {
			// Transform the entity ID for the API call if needed (remove pr- prefix for GitHub PRs)
			const apiEntityId = transformEntityIdForRetrieval(entityId, entityType, providerId);

			const rawEntity = await apiClient.entities.getRaw(
				apiEntityId,
				entityType,
				providerId,
				repository,
				true // includeRelatedEntities
			);
			updateRawEntity(entityKey, rawEntity);
		} catch (error) {
			const message =
				error instanceof APIError
					? error.message
					: `Failed to load raw entity ${entityType}:${entityId}`;
			setRawEntityError(entityKey, message);
			console.error(
				`Failed to load raw entity ${entityKey} (${providerId}:${entityType}:${entityId}):`,
				error
			);
		} finally {
			setEntityLoading(entityKey, false);
			ongoingRequests.delete(requestKey);
		}
	})();

	ongoingRequests.set(requestKey, requestPromise);
	await requestPromise;
}

/**
 * Load matching entities for a trigger and store in global state
 * This function loads entities for a specific trigger and stores them in triggerMatches
 * Now delegates to the centralized computeTriggerMatches function to avoid duplicate API calls
 */
export async function loadMatchingEntities(
	triggerId: string,
	options?: { showDismissed?: boolean; limit?: number }
): Promise<any[]> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	try {
		console.log('Loading matching entities for trigger:', triggerId);

		// Make direct API call for this specific trigger
		const response = await apiClient.triggers.matchingEntities(triggerId, {
			limit: 50,
			showDismissed: options?.showDismissed ?? false
		});

		// Handle different possible response structures
		let entities: UnifiedEntity[] = [];
		if (Array.isArray(response)) {
			entities = response;
		} else if (response && typeof response === 'object') {
			// Combine all entity arrays from the response
			const githubEntities = (response as any).githubEntities || [];
			const linearEntities = (response as any).linearEntities || [];
			const entitiesArray = (response as any).entities || [];

			entities = [...githubEntities, ...linearEntities, ...entitiesArray];
		}

		// Update the global store with the loaded entities
		if (entities.length > 0) {
			const { globalState } = await import('./global-state.svelte');

			// Create entity map and entity keys for this trigger
			const newEntities: Record<string, UnifiedEntity> = {};
			const entityKeys: string[] = [];

			entities.forEach((entity) => {
				const entityKey = `${entity.entityType}:${entity.id}`;
				newEntities[entityKey] = entity;
				entityKeys.push(entityKey);
			});

			// Update global store
			globalState.update((s) => ({
				...s,
				entities: { ...s.entities, ...newEntities },
				triggerMatches: { ...s.triggerMatches, [triggerId]: entityKeys }
			}));
		} else {
			// Even if no entities, update the trigger matches to show it's been loaded
			const { globalState } = await import('./global-state.svelte');
			globalState.update((s) => ({
				...s,
				triggerMatches: { ...s.triggerMatches, [triggerId]: [] }
			}));
		}

		return entities;
	} catch (error) {
		console.error('🔥 Error in loadMatchingEntities for trigger:', triggerId, error);
		const message = error instanceof APIError ? error.message : 'Failed to load matching entities';
		throw new Error(message);
	}
}

/**
 * Transform entity ID for API calls based on entity type
 * PR entities need a "pr-" prefix for the dismiss API
 */
function transformEntityIdForDismiss(entityId: string, entity?: any): string {
	// Check if this is a GitHub PR entity that needs the prefix
	if (entity?.providerId === 'github' && entity?.entityType === 'pull_request') {
		// Only add prefix if it's not already there
		return entityId.startsWith('pr-') ? entityId : `pr-${entityId}`;
	}

	// For all other entity types, return the ID as-is
	return entityId;
}

/**
 * Transform entity ID for entity retrieval API calls
 * PR entities need the "pr-" prefix removed for the get entity API
 */
function transformEntityIdForRetrieval(
	entityId: string,
	entityType: string,
	providerId: string
): string {
	// Check if this is a GitHub PR entity that needs the prefix removed
	if (providerId === 'github' && entityType === 'pull_request') {
		// Remove prefix if it exists
		return entityId.startsWith('pr-') ? entityId.slice(3) : entityId;
	}

	// For all other entity types, return the ID as-is
	return entityId;
}

/**
 * Dismiss or un-dismiss an entity for a trigger
 */
export async function dismissEntityAndSync(
	triggerId: string,
	entityId: string,
	dismissed: boolean = true
): Promise<void> {
	if (!isAuthenticated()) {
		throw new Error('Authentication required');
	}

	try {
		// Find the entity in local state to determine its type
		const state = get(globalState);
		const entity = Object.values(state.entities).find((e) => e?.id === entityId);

		// Transform the entity ID for the API call if needed
		const apiEntityId = transformEntityIdForDismiss(entityId, entity);

		// Call the actual API endpoint with the transformed ID
		await apiClient.triggers.dismissEntity(triggerId, apiEntityId, dismissed);

		// Update the entity in local state to reflect the dismissed status
		globalState.update((s) => {
			const updatedEntities = { ...s.entities };
			const updatedTriggerMatches = { ...s.triggerMatches };

			// Find and update the entity with the dismissed status
			for (const [key, entity] of Object.entries(updatedEntities)) {
				if (entity && entity.id === entityId) {
					updatedEntities[key] = {
						...entity,
						isDismissed: dismissed
					};

					// If dismissing, remove from trigger matches immediately
					if (dismissed) {
						const entityKey = `${entity.entityType}:${entity.id}`;
						if (updatedTriggerMatches[triggerId]) {
							updatedTriggerMatches[triggerId] = updatedTriggerMatches[triggerId].filter(
								(key) => key !== entityKey
							);
						}
					}
					break;
				}
			}

			return {
				...s,
				entities: updatedEntities,
				triggerMatches: updatedTriggerMatches
			};
		});
	} catch (error) {
		const message = error instanceof APIError ? error.message : 'Failed to dismiss entity';
		console.error('Failed to dismiss entity:', error);
		throw new Error(message);
	}
}

/**
 * Load chat history for an agent
 */
export async function loadChatHistory(agentId: string, force = false): Promise<void> {
	// Check if chat history is already being loaded for this agent
	const existingLoadPromise = chatHistoryLoading.get(agentId);
	if (existingLoadPromise) {
		console.log(`Chat history already loading for agent ${agentId}, waiting for completion...`);
		await existingLoadPromise;
		return;
	}

	const state = get(globalState);

	// Check if already loading (fallback check)
	if (state.loading.chatHistory.has(agentId)) {
		console.log(`Chat history already loading for agent ${agentId}, skipping...`);
		return;
	}

	// Check if we already have a session and don't need to force reload
	const existingSession = Object.values(state.chatSessions).find(
		(session) => session.agentId === agentId
	);

	// If not forcing and session exists, don't reload
	if (!force && existingSession) return;

	// Even if forcing, skip if there are active streaming messages
	if (existingSession?.streamingMessages && existingSession.streamingMessages.size > 0) {
		return;
	}

	// Create a promise for this loading operation and store it
	const loadingPromise = (async () => {
		setChatHistoryLoading(agentId, true);

		try {
			// Use the clean API to get chat history in the new format
			console.log(`Loading chat history for agent ${agentId} using clean endpoint`);
			const cleanResponse = await apiClient.agents.getChatHistory(agentId);
			const exchanges = cleanResponse.exchanges || [];
			console.log(`Loaded ${exchanges.length} exchanges for agent ${agentId}`);

			// Convert clean exchanges to messages format
			const messages = cleanResponse.messages.map((msg) => ({
				...msg,
				timestamp: msg.timestamp.getTime()
			}));

			// Calculate the last sequence ID from the exchanges
			const lastSequenceId =
				exchanges.length > 0 ? Math.max(...exchanges.map((e) => e.sequenceId)) : 0;

			// Get existing session to preserve streaming messages
			const existingSession = Object.values(state.chatSessions).find(
				(sess) => sess.agentId === agentId
			);

			const session = {
				id: `chat-${agentId}`,
				agentId: agentId,
				messages,
				exchanges, // Clean exchanges in new format
				isStreaming: existingSession?.isStreaming || false,
				streamingContent: existingSession?.streamingContent || '',
				streamingMessages: existingSession?.streamingMessages || new Map(),
				lastSequenceId,
				sessionSummary: cleanResponse.sessionSummary,
				error: null
			};

			console.log(`Updating chat session for agent ${agentId}`, {
				sessionId: session.id,
				agentId: session.agentId,
				lastSequenceId: session.lastSequenceId,
				exchangeCount: session.exchanges.length
			});
			updateChatSession(session);

			// update agent
			if (cleanResponse.remoteAgent) {
				console.log(
					`Updating agent from chat history response`,
					cleanResponse.remoteAgent,
					convertRemoteAgentFromAPI(cleanResponse.remoteAgent)
				);
				updateAgent(convertRemoteAgentFromAPI(cleanResponse.remoteAgent));
			}

			// Mark initial load as complete
			markInitialLoadComplete(agentId);
		} catch (error) {
			console.error('Failed to load chat history:', error);
			// Get existing session to preserve streaming messages even on error
			const existingSession = Object.values(state.chatSessions).find(
				(sess) => sess.agentId === agentId
			);

			// Create empty session on error
			const emptySession = {
				id: `session-${agentId}-${Date.now()}`,
				agentId,
				messages: [],
				exchanges: [],
				isStreaming: existingSession?.isStreaming || false,
				streamingContent: existingSession?.streamingContent || '',
				streamingMessages: existingSession?.streamingMessages || new Map(),
				error: error instanceof Error ? error.message : 'Failed to load chat history'
			};
			updateChatSession(emptySession);

			// Mark initial load as complete even on error
			markInitialLoadComplete(agentId);
		} finally {
			setChatHistoryLoading(agentId, false);
		}
	})();

	// Store the promise and execute it
	chatHistoryLoading.set(agentId, loadingPromise);

	try {
		await loadingPromise;
	} finally {
		// Clean up the promise from the map
		chatHistoryLoading.delete(agentId);
	}
}

/**
 * Send chat message to agent (legacy - non-streaming)
 */
export async function sendChatMessageLegacy(
	agentId: string,
	message: string,
	options?: {
		userGuidelines?: string;
		workspaceGuidelines?: string;
		agentMemories?: string;
	}
): Promise<void> {
	try {
		// Add user message immediately
		const userMessage = {
			id: `msg-${Date.now()}`,
			role: 'user' as const,
			content: message,
			timestamp: Date.now()
		};
		addChatMessage(agentId, userMessage);

		// Send to API - response will come through history polling
		await apiClient.agents.chat({
			remoteAgentId: agentId,
			message,
			...options
		});

		// Note: The assistant response will come through the polling mechanism
		// and will be added to the chat session when the history is updated
	} catch (error) {
		console.error('Failed to send chat message:', error);
		throw error;
	}
}

/**
 * Send chat message to agent with streaming response
 */
export async function sendChatMessage(
	agentId: string,
	message: string,
	options?: {
		userGuidelines?: string;
		workspaceGuidelines?: string;
		agentMemories?: string;
	}
): Promise<void> {
	// Get current agent to determine appropriate timeout
	const currentAgent = get(globalState).agents[agentId];
	const timeoutMs = getTimeoutForAgent(currentAgent);

	// Create optimistic message with workspace-aware timeout handling
	const optimisticMessageId = createOptimisticMessage(
		agentId,
		'Generating response...',
		'assistant',
		timeoutMs,
		{
			originalContent: message, // Store original user message for matching
			onTimeout: () => {
				const errorMessage = getTimeoutErrorMessage(currentAgent);
				console.warn(`[Chat] Message delivery timed out for agent ${agentId}: ${errorMessage}`);
			},
			onDelivered: () => {
				console.log(`[Chat] Message delivered for agent ${agentId}`);
			},
			onError: (error) => {
				console.error(`[Chat] Message delivery failed for agent ${agentId}:`, error);
			}
		}
	);

	try {
		// Add user message immediately
		const userMessage = {
			id: `msg-${Date.now()}`,
			role: 'user' as const,
			content: message,
			timestamp: Date.now()
		};
		addChatMessage(agentId, userMessage);

		// Let's also optimistically update the agent's last activity timestamp and status
		updateAgent({
			...currentAgent,
			updatedAt: new Date(),
			status: RemoteAgentStatus.AGENT_RUNNING,
			workspaceStatus:
				currentAgent.workspaceStatus ===
				RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED
					? RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING
					: currentAgent.workspaceStatus
		});

		// start streaming, in case it's not already streaming
		startStreamingAgent(agentId).catch((error) => {
			console.error(`Failed to start streaming for agent ${agentId}:`, error);
		});

		// Send to regular chat API endpoint - response will come via agent history streaming
		const response = await fetch('/api/remote-agents/chat', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			credentials: 'include',
			body: JSON.stringify(
				toSnakeCase({
					remoteAgentId: agentId,
					requestDetails: {
						requestNodes: [
							{
								id: 1,
								type: ChatRequestNodeType.TEXT,
								textNode: {
									content: message
								}
							}
						]
					},
					message,
					userGuidelines: options?.userGuidelines,
					workspaceGuidelines: options?.workspaceGuidelines,
					agentMemories: options?.agentMemories
				})
			)
		});

		if (!response.ok) {
			throw new Error(`Chat request failed: ${response.status} ${response.statusText}`);
		}

		// Message sent successfully - the response will come through agent history streaming
		console.log('Chat message sent successfully');

		// Don't mark as delivered yet - wait for the actual response to come through streaming
		// The optimistic message will be cleared when handleNewExchange matches the content
	} catch (error) {
		console.error('Failed to send chat message:', error);
		// Clear optimistic message on network error
		clearOptimisticMessage(agentId);

		// Mark optimistic message as failed
		const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
		markMessageFailed(optimisticMessageId, errorMessage);

		throw error;
	}
}

// ============================================================================
// STREAMING FUNCTIONALITY
// ============================================================================

export const activeStreams = new Map<
	string,
	{
		reader?: ReadableStreamDefaultReader<Uint8Array>;
		lastSequenceId: number;
		lastActivity?: number;
		retryCount?: number;
		heartbeatTimeout?: NodeJS.Timeout;
	}
>();

// Track pending stream creation to prevent race conditions
const pendingStreams = new Map<string, Promise<void>>();

// Track initial load states and streaming subscribers
const initialLoadStates = new Map<string, boolean>(); // Track if agent has had initial load
const streamingSubscribers = new Map<string, Set<string>>(); // Track which components are interested in streaming for each agent

// Streaming configuration constants
const STREAM_HEARTBEAT_TIMEOUT = 30000; // 30 seconds
const STREAM_MAX_RETRY_COUNT = 5;
const STREAM_BASE_RETRY_DELAY = 2000; // 2 seconds

// Track chat history loading to prevent race conditions
const chatHistoryLoading = new Map<string, Promise<void>>();

// Agent list streaming state
let agentListStreamActive = false;
let agentListStreamReader: ReadableStreamDefaultReader<Uint8Array> | null = null;
let lastAgentListUpdateTimestamp: string | null = null;
let currentAgentListStreamId: string | null = null;
let agentListStreamLastActivity = Date.now();
let agentListStreamRetryCount = 0;
let agentListStreamStatus: 'disconnected' | 'connecting' | 'connected' | 'error' = 'disconnected';

/**
 * Subscribe a component to streaming for an agent
 */
export function subscribeToAgentStreaming(agentId: string, componentId: string): void {
	if (!streamingSubscribers.has(agentId)) {
		streamingSubscribers.set(agentId, new Set());
	}
	streamingSubscribers.get(agentId)!.add(componentId);

	// Start streaming if not already active
	startStreamingAgent(agentId).catch((error) => {
		console.error(`Failed to start streaming for agent ${agentId}:`, error);
	});
}

/**
 * Unsubscribe a component from streaming for an agent
 */
export function unsubscribeFromAgentStreaming(agentId: string, componentId: string): void {
	const subscribers = streamingSubscribers.get(agentId);
	if (subscribers) {
		subscribers.delete(componentId);

		// Stop streaming if no more subscribers
		if (subscribers.size === 0) {
			streamingSubscribers.delete(agentId);
			stopStreamingAgent(agentId);
		}
	}
}

/**
 * Check if this is the initial load for an agent
 */
export function isInitialLoad(agentId: string): boolean {
	return !initialLoadStates.has(agentId);
}

/**
 * Mark an agent as having completed initial load
 */
export function markInitialLoadComplete(agentId: string): void {
	initialLoadStates.set(agentId, true);
}

/**
 * Check if agent should continue streaming based on status
 */
function shouldContinueStreaming(agentId: string): boolean {
	const state = get(globalState);
	const agent = state.agents[agentId];

	if (!agent) {
		return false;
	}

	// Continue streaming for agents that are active or transitioning to active states
	const shouldStream =
		agent.status === RemoteAgentStatus.AGENT_RUNNING ||
		agent.status === RemoteAgentStatus.AGENT_STARTING ||
		agent.status === RemoteAgentStatus.AGENT_IDLE ||
		agent.status === RemoteAgentStatus.AGENT_RESUMING || // Added missing AGENT_RESUMING status
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING ||
		agent.workspaceStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSING; // Stream during transitions

	console.log(
		`[Streaming] Agent ${agentId} shouldStream: ${shouldStream} (status: ${agent.status}, workspaceStatus: ${agent.workspaceStatus})`
	);
	return shouldStream;
}

/**
 * Start streaming agent history updates
 * Ensures chat history is loaded first to get the correct last sequence ID
 */
export async function startStreamingAgent(agentId: string): Promise<void> {
	// Check if already streaming for this agent
	if (activeStreams.has(agentId)) {
		console.log(`Stream already active for agent ${agentId}, skipping`);
		return;
	}

	// Check if there's already a pending stream creation
	if (pendingStreams.has(agentId)) {
		console.log(`Stream creation already pending for agent ${agentId}, waiting for completion`);
		await pendingStreams.get(agentId);
		return;
	}

	// Check if we should start streaming based on agent status
	if (!shouldContinueStreaming(agentId)) {
		console.log(`Agent ${agentId} should not continue streaming, skipping`);
		return;
	}

	// Stop any existing stream first to prevent duplicates
	stopStreamingAgent(agentId);

	// Create a promise to track this stream creation
	const streamCreationPromise = createStreamForAgent(agentId);
	pendingStreams.set(agentId, streamCreationPromise);

	try {
		await streamCreationPromise;
	} finally {
		// Always clean up the pending promise
		pendingStreams.delete(agentId);
	}
}

/**
 * Internal function to create a stream for an agent
 */
async function createStreamForAgent(agentId: string): Promise<void> {
	// Ensure we have chat history loaded first to get the correct last sequence ID
	const state = get(globalState);
	const existingSession = Object.values(state.chatSessions).find(
		(session) => session.agentId === agentId
	);

	// If no session exists or it's an initial load, load chat history first
	if (!existingSession || isInitialLoad(agentId)) {
		console.log(`Loading chat history first for agent ${agentId} before starting stream`, {
			existingSession: !!existingSession,
			isInitialLoad: isInitialLoad(agentId),
			currentSessions: Object.keys(state.chatSessions)
		});
		try {
			await loadChatHistory(agentId, false);
			console.log(`Chat history loaded for agent ${agentId}, checking session...`);
		} catch (error) {
			console.error(`Failed to load chat history for agent ${agentId}:`, error);
			// Continue with streaming even if history load fails, but use sequence ID 0
		}
	}

	// Get the updated session after potential history load
	const updatedState = get(globalState);
	const session = Object.values(updatedState.chatSessions).find(
		(session) => session.agentId === agentId
	);
	const lastSequenceId = session?.lastSequenceId || 0;

	console.log(`Starting streaming for agent ${agentId} from sequence ID ${lastSequenceId}`, {
		session: session
			? { id: session.id, agentId: session.agentId, lastSequenceId: session.lastSequenceId }
			: undefined,
		chatSessionKeys: Object.keys(updatedState.chatSessions),
		chatSessionAgentIds: Object.values(updatedState.chatSessions).map((s) => s.agentId)
	});

	// Initialize sequence tracker with the last known sequence ID
	initializeSequenceTracker(agentId, lastSequenceId);

	// Start the streaming connection using fetch with ReadableStream
	await startStreamingConnection(agentId, lastSequenceId);
}

/**
 * Start a streaming connection for agent history
 */
async function startStreamingConnection(agentId: string, lastSequenceId: number): Promise<void> {
	try {
		// Double-check that we're not already streaming (race condition protection)
		if (activeStreams.has(agentId)) {
			console.log(`Stream connection already exists for agent ${agentId}, aborting new connection`);
			return;
		}

		console.log(
			`Starting stream connection for agent ${agentId} with sequence ID ${lastSequenceId}`
		);

		// Reserve the slot immediately to prevent race conditions
		const streamEntry = {
			lastSequenceId,
			lastActivity: Date.now(),
			retryCount: 0
		};
		activeStreams.set(agentId, streamEntry);

		const response = await fetch('/api/remote-agents/agent-history-stream', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			credentials: 'include',
			body: JSON.stringify(
				toSnakeCase({
					remoteAgentId: agentId,
					lastProcessedSequenceId: lastSequenceId
				})
			)
		});

		if (!response.ok) {
			throw new Error(`Streaming failed: ${response.status} ${response.statusText}`);
		}

		if (!response.body) {
			throw new Error('No response body for streaming');
		}

		const reader = response.body.getReader();
		const decoder = new TextDecoder();
		let buffer = '';

		// Update the existing entry with the reader and setup heartbeat
		const updatedEntry = {
			reader,
			lastSequenceId,
			lastActivity: Date.now(),
			retryCount: streamEntry.retryCount || 0
		};
		activeStreams.set(agentId, updatedEntry);

		// Setup heartbeat timeout detection
		setupHeartbeatTimeout(agentId);

		while (true) {
			const { done, value } = await reader.read();

			if (done) {
				break;
			}

			// Update last activity timestamp
			updateStreamActivity(agentId);

			buffer += decoder.decode(value, { stream: true });
			const lines = buffer.split('\n');
			buffer = lines.pop() || ''; // Keep incomplete line in buffer

			for (const line of lines) {
				const trimmedLine = line.trim();

				// Handle different SSE message types
				if (trimmedLine.startsWith('data: ')) {
					const dataContent = trimmedLine.slice(6); // Remove 'data: ' prefix

					// Skip heartbeat messages
					if (dataContent === 'heartbeat' || dataContent === '') {
						console.log(`[Streaming] Heartbeat received for agent ${agentId}`);
						continue;
					}

					try {
						const data = JSON.parse(dataContent);
						const parsedData = toCamelCase(data);
						console.log('Streaming data:', parsedData.updates);

						if (parsedData.updates && Array.isArray(parsedData.updates)) {
							parsedData.updates.forEach((update: any) => {
								// Convert API format to clean format before processing
								const cleanUpdate: StreamingUpdate = {
									type: update.type,
									sequenceId: update.sequenceId,
									exchange: update.exchange
										? convertChatExchangeDataFromAPI(update.exchange)
										: undefined,
									exchangeUpdate: update.exchangeUpdate
										? convertChatExchangeUpdateFromAPI(update.exchangeUpdate)
										: undefined,
									agent: update.agent
								};
								processHistoryUpdate(agentId, cleanUpdate);
							});
						}
					} catch (error) {
						console.error('Error parsing streaming data:', error, 'Line:', trimmedLine);
					}
				} else if (trimmedLine.startsWith('event: ')) {
					// Handle SSE event types (for future use)
					const eventType = trimmedLine.slice(7);
					console.log(`[Streaming] Event type: ${eventType} for agent ${agentId}`);
				}
			}
		}
	} catch (error) {
		console.error('Streaming error for agent:', agentId, error);

		// Clean up the stream reference
		activeStreams.delete(agentId);

		// Create a user-friendly error message
		let errorMessage = 'Unknown error';
		let isTemporary = true;

		if (error instanceof Error) {
			if (error.message.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')) {
				errorMessage = 'Connection interrupted. Attempting to reconnect...';
				isTemporary = true;
			} else if (
				error.message.includes('network error') ||
				error.message.includes('TypeError: network error')
			) {
				errorMessage = 'Network error. Retrying connection...';
				isTemporary = true;
			} else if (error.message.includes('timeout')) {
				errorMessage = 'Request timed out. Please try again.';
				isTemporary = true;
			} else if (error.message.includes('401') || error.message.includes('403')) {
				errorMessage = 'Authentication error. Please refresh the page.';
				isTemporary = false;
			} else if (error.message.includes('404')) {
				errorMessage = 'Agent not found.';
				isTemporary = false;
			} else {
				errorMessage = error.message;
				isTemporary =
					!error.message.includes('401') &&
					!error.message.includes('403') &&
					!error.message.includes('404');
			}
		}

		// Add error message to streaming messages for display
		const state = get(globalState);
		const session = state.chatSessions[agentId];
		if (session) {
			const errorStreamingMessage = {
				tempId: `error-${Date.now()}`,
				content: '',
				error: errorMessage,
				isComplete: true,
				startTime: Date.now()
			};

			// Update the session with the error message
			globalState.update((state) => {
				const session = state.chatSessions[agentId];
				if (session) {
					session.streamingMessages.set(errorStreamingMessage.tempId, errorStreamingMessage);
				}
				return state;
			});

			// Remove error message after 5 seconds for temporary errors
			if (isTemporary) {
				setTimeout(() => {
					globalState.update((state) => {
						const session = state.chatSessions[agentId];
						if (session) {
							session.streamingMessages.delete(errorStreamingMessage.tempId);
						}
						return state;
					});
				}, 5000);
			}
		}

		// Determine if this is a permanent failure
		const isPermanentFailure = !isTemporary;

		// Handle stream connection loss
		handleStreamConnectionLoss(agentId, isPermanentFailure);

		// Attempt to reconnect after a delay (only for temporary failures)
		if (!isPermanentFailure) {
			// Get current retry count and calculate exponential backoff
			const streamEntry = activeStreams.get(agentId);
			const retryCount = (streamEntry?.retryCount || 0) + 1;

			// Don't retry if we've exceeded max attempts
			if (retryCount > STREAM_MAX_RETRY_COUNT) {
				console.log(
					`Max retry attempts (${STREAM_MAX_RETRY_COUNT}) exceeded for agent ${agentId}, giving up`
				);
				return;
			}

			// Calculate exponential backoff delay
			const retryDelay = Math.min(
				STREAM_BASE_RETRY_DELAY * Math.pow(2, retryCount - 1),
				30000 // Cap at 30 seconds
			);

			console.log(
				`Attempting to reconnect agent ${agentId} stream in ${retryDelay}ms (attempt ${retryCount}/${STREAM_MAX_RETRY_COUNT})...`
			);

			setTimeout(() => {
				// Only reconnect if we're still supposed to be streaming and authenticated
				const state = get(globalState);
				const shouldReconnect =
					isAuthenticated() &&
					Object.values(state.chatSessions).some((session) => session.agentId === agentId);

				if (shouldReconnect && shouldContinueStreaming(agentId)) {
					console.log(`Reconnecting stream for agent ${agentId}...`);

					// Update retry count before attempting reconnection
					const currentEntry = activeStreams.get(agentId);
					if (currentEntry) {
						activeStreams.set(agentId, { ...currentEntry, retryCount });
					}

					startStreamingAgent(agentId).catch((error) => {
						console.error(`Failed to restart streaming for agent ${agentId}:`, error);
					});
				} else {
					console.log(`Skipping reconnection for agent ${agentId} - no longer needed`);
				}
			}, retryDelay);
		}
	}
}

/**
 * Setup heartbeat timeout detection for a stream
 */
function setupHeartbeatTimeout(agentId: string): void {
	const streamEntry = activeStreams.get(agentId);
	if (!streamEntry) return;

	// Clear any existing timeout
	if (streamEntry.heartbeatTimeout) {
		clearTimeout(streamEntry.heartbeatTimeout);
	}

	// Set up new timeout
	const timeout = setTimeout(() => {
		console.warn(`[Streaming] Heartbeat timeout for agent ${agentId}, reconnecting...`);

		// Mark as connection lost and attempt reconnection
		const entry = activeStreams.get(agentId);
		if (entry) {
			// Clean up current stream
			if (entry.reader) {
				entry.reader.cancel().catch(console.error);
			}
			activeStreams.delete(agentId);

			// Attempt reconnection if still needed
			if (shouldContinueStreaming(agentId)) {
				startStreamingAgent(agentId).catch((error) => {
					console.error(
						`Failed to restart streaming after heartbeat timeout for agent ${agentId}:`,
						error
					);
				});
			}
		}
	}, STREAM_HEARTBEAT_TIMEOUT);

	// Update stream entry with new timeout
	activeStreams.set(agentId, { ...streamEntry, heartbeatTimeout: timeout });
}

/**
 * Update stream activity timestamp and reset heartbeat timeout
 */
function updateStreamActivity(agentId: string): void {
	const streamEntry = activeStreams.get(agentId);
	if (!streamEntry) return;

	// Update last activity timestamp
	streamEntry.lastActivity = Date.now();

	// Reset heartbeat timeout
	setupHeartbeatTimeout(agentId);
}

/**
 * Interrupt agent execution on the backend
 */
export async function interruptAgent(agentId: string): Promise<void> {
	try {
		console.log(`Interrupting agent ${agentId}`);
		await apiClient.agents.interrupt(agentId);
		console.log(`Agent ${agentId} interrupted successfully`);
	} catch (error) {
		console.error(`Failed to interrupt agent ${agentId}:`, error);
		throw error;
	}
}

/**
 * Stop streaming for agent updates
 */
export function stopStreamingAgent(agentId: string, clearOptimistic: boolean = true): void {
	const stream = activeStreams.get(agentId);
	if (stream) {
		console.log(`Stopping stream for agent ${agentId}`);

		// Cancel the reader if it exists
		if (stream.reader) {
			stream.reader.cancel().catch((error) => {
				console.error('Error canceling stream reader:', error);
			});
		}

		// Clear heartbeat timeout
		if (stream.heartbeatTimeout) {
			clearTimeout(stream.heartbeatTimeout);
		}

		activeStreams.delete(agentId);
	} else {
		console.log(`No active stream found for agent ${agentId} to stop`);
	}

	// Clean up sequence tracking
	cleanupSequenceTracker(agentId);

	// Cancel any pending optimistic messages and clear optimistic message only if requested
	if (clearOptimistic) {
		cancelPendingMessagesForAgent(agentId);
		clearOptimisticMessage(agentId);
	}

	// Clear streaming state from chat session
	clearChatSessionStreaming(agentId);
}

/**
 * Stop all streaming
 */
export function stopAllStreaming(): void {
	for (const [agentId] of activeStreams) {
		stopStreamingAgent(agentId);
	}
}

/**
 * Check if streaming is active for an agent
 */
export function isStreamingActive(agentId: string): boolean {
	return activeStreams.has(agentId);
}

/**
 * Get streaming status for all active agents
 */
export function getStreamingStatus(): Record<
	string,
	{ lastSequenceId: number; isActive: boolean }
> {
	const status: Record<string, { lastSequenceId: number; isActive: boolean }> = {};

	for (const [agentId, stream] of activeStreams) {
		status[agentId] = {
			lastSequenceId: stream.lastSequenceId,
			isActive: true
		};
	}

	return status;
}

/**
 * Debug function to detect and clean up potential duplicate streams
 */
export function debugStreamingState(): void {
	console.log('=== STREAMING DEBUG INFO ===');
	console.log('Active streams:', activeStreams.size);

	for (const [agentId, stream] of activeStreams) {
		console.log(`Agent ${agentId}:`, {
			hasReader: !!stream.reader,
			lastSequenceId: stream.lastSequenceId
		});
	}

	console.log('Streaming subscribers:', streamingSubscribers.size);
	for (const [agentId, subscribers] of streamingSubscribers) {
		console.log(`Agent ${agentId} subscribers:`, Array.from(subscribers));
	}

	console.log('Agent list stream state:', {
		active: agentListStreamActive,
		hasReader: !!agentListStreamReader,
		streamId: currentAgentListStreamId
	});

	console.log('=== END STREAMING DEBUG ===');
}

/**
 * Force cleanup all streams - useful for debugging
 */
export function forceCleanupAllStreams(): void {
	console.log('=== FORCE CLEANUP ALL STREAMS ===');

	// Stop agent list stream
	stopAgentListStream();

	// Stop all individual agent streams
	const agentIds = Array.from(activeStreams.keys());
	for (const agentId of agentIds) {
		console.log(`Force stopping stream for agent: ${agentId}`);
		stopStreamingAgent(agentId);
	}

	// Clear all subscribers
	streamingSubscribers.clear();

	console.log('All streams forcefully cleaned up');
}

// Add cleanup on page unload to prevent hanging connections
if (typeof window !== 'undefined') {
	window.addEventListener('beforeunload', () => {
		console.log('Page unloading, cleaning up streams...');
		forceCleanupAllStreams();
	});

	// Also cleanup on visibility change (when tab becomes hidden)
	document.addEventListener('visibilitychange', () => {
		if (document.visibilityState === 'hidden') {
			console.log('Tab hidden, pausing streams...');
			// Don't force cleanup, just stop new connections
			stopAgentListStream();
		} else if (document.visibilityState === 'visible' && isAuthenticated()) {
			console.log('Tab visible, resuming streams...');
			// Restart agent list stream if authenticated
			if (!agentListStreamActive) {
				startAgentListStream();
			}
		}
	});

	// Add debugging functions to global scope
	(window as any).debugStreams = {
		getAgentListStatus: getAgentListStreamStatus,
		startAgentList: startAgentListStream,
		stopAgentList: stopAgentListStream,
		forceCleanup: forceCleanupAllStreams,
		getActiveStreams: () => Array.from(activeStreams.keys())
	};
}

/**
 * Restart streaming for an agent (useful for error recovery)
 */
export function restartStreamingAgent(agentId: string): void {
	stopStreamingAgent(agentId);

	// Small delay before restarting to allow cleanup
	setTimeout(() => {
		startStreamingAgent(agentId).catch((error) => {
			console.error(`Failed to restart streaming for agent ${agentId}:`, error);
		});
	}, 1000);
}

// ============================================================================
// AGENT LIST STREAMING (Real-time status updates for all agents)
// ============================================================================

/**
 * Start streaming agent list updates for real-time status changes
 */
export function startAgentListStream(): void {
	if (agentListStreamActive) {
		return; // Already streaming
	}

	if (!isAuthenticated()) {
		console.warn('Cannot start agent list stream: not authenticated');
		agentListStreamStatus = 'disconnected';
		return;
	}

	// Generate unique stream ID to prevent race conditions
	const streamId = crypto.randomUUID();
	currentAgentListStreamId = streamId;
	agentListStreamActive = true;
	agentListStreamStatus = 'connecting';

	console.log(`Starting agent list stream with ID: ${streamId}`);
	startAgentListStreamConnection(streamId);
}

/**
 * Get current agent list stream status
 */
export function getAgentListStreamStatus(): {
	active: boolean;
	status: 'disconnected' | 'connecting' | 'connected' | 'error';
	streamId: string | null;
	lastActivity: number;
	retryCount: number;
} {
	return {
		active: agentListStreamActive,
		status: agentListStreamStatus,
		streamId: currentAgentListStreamId,
		lastActivity: agentListStreamLastActivity,
		retryCount: agentListStreamRetryCount
	};
}

/**
 * Stop agent list streaming
 */
export function stopAgentListStream(): void {
	console.log(`Stopping agent list stream (ID: ${currentAgentListStreamId})`);

	if (agentListStreamReader) {
		agentListStreamReader.cancel().catch((error) => {
			console.error('Error canceling agent list stream reader:', error);
		});
		agentListStreamReader = null;
	}

	agentListStreamActive = false;
	agentListStreamStatus = 'disconnected';
	currentAgentListStreamId = null;
}

/**
 * Start the actual streaming connection for agent list updates
 */
async function startAgentListStreamConnection(streamId: string): Promise<void> {
	let abortController: AbortController | null = null;

	try {
		const body: { last_update_timestamp?: string } = {};
		if (lastAgentListUpdateTimestamp) {
			body.last_update_timestamp = lastAgentListUpdateTimestamp;
		}

		// Create abort controller for this specific stream
		abortController = new AbortController();

		// Set up timeout that's shorter than server timeout to detect issues early
		const timeoutId = setTimeout(() => {
			if (abortController) {
				console.log(`Agent list stream ${streamId} timeout, aborting...`);
				abortController.abort();
			}
		}, 90000); // 90 seconds - shorter than server's 120 seconds

		const response = await fetch('/api/remote-agents/list-stream', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			},
			credentials: 'include',
			body: JSON.stringify(body),
			signal: abortController.signal
		});

		// Clear timeout if fetch succeeds
		clearTimeout(timeoutId);

		if (!response.ok) {
			throw new Error(`Agent list streaming failed: ${response.status} ${response.statusText}`);
		}

		if (!response.body) {
			throw new Error('No response body for agent list stream');
		}

		// Reset retry count on successful connection
		agentListStreamRetryCount = 0;
		agentListStreamStatus = 'connected';
		console.log(`Agent list stream ${streamId} connected successfully`);

		const reader = response.body.getReader();
		agentListStreamReader = reader;
		const decoder = new TextDecoder();
		let buffer = '';
		let lastDataReceived = Date.now();
		let consecutiveEmptyReads = 0;

		// Set up connection health monitoring
		const healthCheckInterval = setInterval(() => {
			const timeSinceLastData = Date.now() - lastDataReceived;

			// If no data received for 60 seconds, consider connection stale
			if (timeSinceLastData > 60000) {
				console.warn(
					`Agent list stream ${streamId} appears stale, no data for ${timeSinceLastData}ms`
				);
				if (abortController) {
					abortController.abort();
				}
			}
		}, 30000); // Check every 30 seconds

		try {
			while (agentListStreamActive && currentAgentListStreamId === streamId) {
				const { done, value } = await reader.read();

				if (done) {
					console.log(`Agent list stream ${streamId} completed normally`);
					break;
				}

				if (!value || value.length === 0) {
					consecutiveEmptyReads++;
					// If we get too many empty reads, something might be wrong
					if (consecutiveEmptyReads > 10) {
						console.warn(
							`Agent list stream ${streamId} received ${consecutiveEmptyReads} consecutive empty reads`
						);
						break;
					}
					continue;
				}

				consecutiveEmptyReads = 0;
				lastDataReceived = Date.now();

				buffer += decoder.decode(value, { stream: true });
				const lines = buffer.split('\n');
				buffer = lines.pop() || ''; // Keep incomplete line in buffer

				for (const line of lines) {
					if (line.trim()) {
						// Update activity timestamp on any data received
						agentListStreamLastActivity = Date.now();

						try {
							const data = JSON.parse(line);
							const parsedData = toCamelCase(data);

							parsedData.updates.forEach((update: AgentListUpdate) => {
								processAgentListUpdate(update);
							});
						} catch (error) {
							console.error('Error parsing agent list stream data:', error, 'Line:', line);
						}
					}
				}
			}
		} finally {
			clearInterval(healthCheckInterval);
		}
	} catch (error) {
		console.error(`Agent list streaming error for stream ${streamId}:`, error);

		// Only clean up if this is still the current stream
		if (currentAgentListStreamId === streamId) {
			agentListStreamActive = false;
			agentListStreamReader = null;
			currentAgentListStreamId = null;
			agentListStreamStatus = 'error';

			// Determine if this is a recoverable error
			const isRecoverable =
				error instanceof Error &&
				(error.message.includes('ERR_INCOMPLETE_CHUNKED_ENCODING') ||
					error.message.includes('network error') ||
					error.message.includes('TypeError: network error') ||
					error.message.includes('timeout') ||
					error.message.includes('fetch') ||
					error.message.includes('aborted') ||
					error.name === 'AbortError' ||
					error.name === 'TypeError' ||
					error.name === 'NetworkError');

			// Attempt to reconnect after a delay if we're still supposed to be streaming and error is recoverable
			if (isAuthenticated() && isRecoverable) {
				// Exponential backoff: 2s, 4s, 8s, 16s, max 30s
				agentListStreamRetryCount++;
				const baseDelay = error.name === 'AbortError' ? 2000 : 3000;
				const retryDelay = Math.min(baseDelay * Math.pow(2, agentListStreamRetryCount - 1), 30000);

				console.log(
					`Attempting to reconnect agent list stream in ${retryDelay}ms (attempt ${agentListStreamRetryCount})...`
				);
				setTimeout(() => {
					if (!agentListStreamActive && isAuthenticated()) {
						console.log(`Reconnecting agent list stream (attempt ${agentListStreamRetryCount})...`);
						startAgentListStream();
					}
				}, retryDelay);
			} else if (!isRecoverable) {
				console.error(
					'Non-recoverable agent list stream error, not retrying:',
					error instanceof Error ? error.message : error
				);
				agentListStreamRetryCount = 0; // Reset retry count for non-recoverable errors
			}
		} else {
			console.log(
				`Ignoring error for old stream ${streamId}, current stream is ${currentAgentListStreamId}`
			);
		}
	}
}

/**
 * Handle workspace status transitions for monitoring and cleanup
 */
function handleWorkspaceStatusChange(oldAgent: CleanRemoteAgent, newAgent: CleanRemoteAgent): void {
	const agentId = newAgent.id;
	const oldStatus = oldAgent.workspaceStatus;
	const newStatus = newAgent.workspaceStatus;

	// Clear any resume timeouts when workspace becomes running
	if (newStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RUNNING) {
		// Workspace is now running - any pending messages should be processed normally
		console.log(`[Workspace] Agent ${agentId} workspace is now running`);
	}

	// Handle resuming state
	if (newStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING) {
		console.log(`[Workspace] Agent ${agentId} workspace is resuming...`);
		// Could add resume timeout monitoring here if needed
	}

	// Handle paused state
	if (newStatus === RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED) {
		console.log(`[Workspace] Agent ${agentId} workspace is now paused`);
		// Could clean up any active streams or pending operations
	}
}

/**
 * Process an agent list update from streaming
 */
function processAgentListUpdate(update: AgentListUpdate): void {
	// Update agent limits if provided
	if (update.maxAgents !== undefined || update.maxActiveAgents !== undefined) {
		updateAgentLimitsConfig({
			...(update.maxActiveAgents !== undefined && {
				maxActiveAgents: update.maxActiveAgents
			}),
			...(update.maxAgents !== undefined && { maxTotalAgents: update.maxAgents })
		});
	}

	// Update timestamp for incremental updates
	if (update.updateTimestamp) {
		lastAgentListUpdateTimestamp = update.updateTimestamp;
	}

	switch (update.type) {
		case AgentListUpdateTypeEnum.AGENT_LIST_AGENT_ADDED:
			if (update.agent) {
				const agentUpdate = convertRemoteAgentFromAPI(update.agent);
				updateAgent(agentUpdate);
			}
			break;

		case AgentListUpdateTypeEnum.AGENT_LIST_AGENT_UPDATED:
			if (update.agent) {
				const agentUpdate = convertRemoteAgentFromAPI(update.agent);
				const currentState = get(globalState);
				const currentAgent = currentState.agents[agentUpdate.id];

				if (currentAgent) {
					// Track workspace status transitions for debugging and monitoring
					if (currentAgent.workspaceStatus !== agentUpdate.workspaceStatus) {
						// Handle workspace status transitions
						handleWorkspaceStatusChange(currentAgent, agentUpdate);
					}

					// Update agent status for pending messages if status changed
					if (currentAgent.status !== agentUpdate.status) {
						updateAgentStatusForPendingMessages(agentUpdate.id, agentUpdate.status);
					}

					// Merge with existing data to preserve any local state
					updateAgent({ ...currentAgent, ...agentUpdate });

					// Handle streaming state changes based on new status
					if (!shouldContinueStreaming(agentUpdate.id) && activeStreams.has(agentUpdate.id)) {
						stopStreamingAgent(agentUpdate.id);
					} else if (
						shouldContinueStreaming(agentUpdate.id) &&
						!activeStreams.has(agentUpdate.id) &&
						streamingSubscribers.has(agentUpdate.id)
					) {
						startStreamingAgent(agentUpdate.id).catch((error) => {
							console.error(
								`Failed to start streaming for updated agent ${agentUpdate.id}:`,
								error
							);
						});
					}
				} else {
					// Agent doesn't exist locally, add it
					updateAgent(agentUpdate);
				}
			}
			break;

		case AgentListUpdateTypeEnum.AGENT_LIST_AGENT_DELETED:
			if (update.deletedAgentId) {
				// Clean up pending messages for deleted agent
				cancelPendingMessagesForAgent(update.deletedAgentId);

				// Remove agent from state
				globalState.update((s) => {
					const { [update.deletedAgentId!]: deleted, ...remainingAgents } = s.agents;
					return { ...s, agents: remainingAgents };
				});

				// Clean up any streaming for this agent
				stopStreamingAgent(update.deletedAgentId);
				console.log('Agent deleted:', update.deletedAgentId);
			}
			break;

		case AgentListUpdateTypeEnum.AGENT_LIST_ALL_AGENTS:
			if (update.allAgents) {
				const cleanAgents = update.allAgents.map(convertRemoteAgentFromAPI);
				updateAgents(cleanAgents);
				console.log('All agents updated:', cleanAgents.length, 'agents');
			}
			break;

		case AgentListUpdateTypeEnum.AGENT_LIST_UPDATE_TYPE_UNSPECIFIED:
		default:
			console.warn('Unknown or unspecified agent list update type:', update.type);
			break;
	}
}

/**
 * Handle a new complete exchange from streaming
 */
function handleNewExchange(agentId: string, exchangeData: CleanChatExchangeData): void {
	// Try to match and deliver optimistic message by content
	// Extract user message content from request nodes for matching
	const requestNodes = exchangeData.exchange.requestNodes || [];
	const userMessageContent = requestNodes
		.filter((node) => node.type === ChatRequestNodeType.TEXT)
		.map((node) => node.textNode?.content)
		.filter(Boolean)
		.join(' ');

	if (userMessageContent) {
		console.log(
			`[Exchange] Attempting to match optimistic message for agent ${agentId} with content: "${userMessageContent}"`
		);

		const wasMatched = tryDeliverByContentMatch(
			agentId,
			userMessageContent,
			Date.now(), // Use current timestamp since exchange doesn't have createdAt
			300000 // 5 minute tolerance to account for any delays
		);

		if (wasMatched) {
			console.log(`[Exchange] Successfully matched optimistic message for agent ${agentId}`);
			// Only clear optimistic message when we've successfully matched and delivered it
			clearOptimisticMessage(agentId);
		} else {
			console.log(
				`[Exchange] No optimistic message match found for agent ${agentId} with content: "${userMessageContent}"`
			);
		}
	}

	// Update the chat session with the new exchange (using clean format directly)
	globalState.update((s) => {
		const session = Object.values(s.chatSessions).find((sess) => sess.agentId === agentId);
		if (!session) {
			console.warn('No chat session found for agent:', agentId);
			return s;
		}

		// Check if exchange with this sequence ID already exists
		const existingExchangeIndex = session.exchanges.findIndex(
			(ex) => ex.sequenceId === exchangeData.sequenceId
		);

		let updatedExchanges;
		if (existingExchangeIndex >= 0) {
			// Replace existing exchange with the new one (streaming might have more complete data)
			updatedExchanges = [...session.exchanges];
			updatedExchanges[existingExchangeIndex] = exchangeData;
		} else {
			// Add new exchange
			updatedExchanges = [...session.exchanges, exchangeData];
		}

		// Add the new exchange to the session
		const updatedSession = {
			...session,
			exchanges: updatedExchanges,
			lastSequenceId: Math.max(session.lastSequenceId || 0, exchangeData.sequenceId),
			isStreaming: !exchangeData.finishedAt // Only stop streaming if exchange is finished
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [session.id]: updatedSession }
		};
	});
}

/**
 * Handle an exchange update (appending content) from streaming
 */
function handleExchangeUpdate(agentId: string, updateData: CleanChatExchangeUpdate): void {
	// Note: We don't clear optimistic messages here since this is just appending content
	// to existing exchanges, not delivering new complete exchanges

	globalState.update((s) => {
		const session = Object.values(s.chatSessions).find((sess) => sess.agentId === agentId);
		if (!session) {
			console.warn('No chat session found for agent:', agentId);
			return s;
		}

		// Find the exchange to update by requestId or sequenceId
		const exchangeIndex = session.exchanges.findIndex(
			(ex) =>
				ex.exchange.requestId === updateData.requestId || ex.sequenceId === updateData.sequenceId
		);

		if (exchangeIndex === -1) {
			console.warn('Exchange not found for update:', updateData);
			return s;
		}

		// Update the exchange with appended content
		const updatedExchanges = [...session.exchanges];
		const existingExchange = updatedExchanges[exchangeIndex];

		// Handle appended content - either append to existing nodes or add new ones
		if (
			updateData.appendedText ||
			(updateData.appendedNodes && Array.isArray(updateData.appendedNodes))
		) {
			const currentResponseNodes = existingExchange.exchange.responseNodes || [];

			// If we have appended text but no appended nodes, append to the last text response node
			if (
				updateData.appendedText &&
				(!updateData.appendedNodes || updateData.appendedNodes.length === 0)
			) {
				// Find the last text response node to append to
				const lastTextNodeIndex = currentResponseNodes.findLastIndex(
					(node) => node.type === 0 && (node.content || node.textNode?.content)
				);

				if (lastTextNodeIndex >= 0) {
					// Append to existing node
					const updatedNodes = [...currentResponseNodes];
					const lastNode = { ...updatedNodes[lastTextNodeIndex] };
					lastNode.content = (lastNode.content || '') + updateData.appendedText;
					if (lastNode.textNode) {
						lastNode.textNode = {
							...lastNode.textNode,
							content: (lastNode.textNode.content || '') + updateData.appendedText
						};
					} else {
						lastNode.textNode = { content: lastNode.content };
					}
					updatedNodes[lastTextNodeIndex] = lastNode;
					existingExchange.exchange.responseNodes = updatedNodes;
				} else {
					// No existing text node, create a new one
					existingExchange.exchange.responseNodes = [
						...currentResponseNodes,
						{
							id: Date.now(),
							type: 0,
							content: updateData.appendedText,
							textNode: { content: updateData.appendedText }
						}
					];
				}
			} else if (updateData.appendedNodes && updateData.appendedNodes.length > 0) {
				// Merge nodes by ID to avoid duplicates
				const mergedNodes = [...currentResponseNodes];

				for (const appendedNode of updateData.appendedNodes) {
					const existingIndex = mergedNodes.findIndex((node) => node.id === appendedNode.id);

					if (existingIndex >= 0) {
						// Replace existing node with the same ID
						mergedNodes[existingIndex] = appendedNode;
					} else {
						// Add new node if ID doesn't exist
						mergedNodes.push(appendedNode);
					}
				}

				existingExchange.exchange.responseNodes = mergedNodes;
			}
		}

		// Append changed files
		if (updateData.appendedChangedFiles && Array.isArray(updateData.appendedChangedFiles)) {
			existingExchange.changedFiles = [
				...existingExchange.changedFiles,
				...updateData.appendedChangedFiles
			];
		}

		const updatedSession = {
			...session,
			exchanges: updatedExchanges,
			lastSequenceId: Math.max(session.lastSequenceId || 0, updateData.sequenceId),
			isStreaming: true // Still streaming updates
		};

		return {
			...s,
			chatSessions: { ...s.chatSessions, [session.id]: updatedSession }
		};
	});
}

/**
 * Process a history update from streaming
 */
function processHistoryUpdate(agentId: string, update: StreamingUpdate): void {
	// Process sequence ID if present
	if (update.sequenceId) {
		const sequenceResult = processSequenceId(agentId, update.sequenceId);

		if (sequenceResult.isDuplicate) {
			console.log(
				`[Streaming] Ignoring duplicate sequence ID ${update.sequenceId} for agent ${agentId}`
			);
			return;
		}

		if (sequenceResult.missedSequenceIds.length > 0) {
			console.warn(
				`[Streaming] Missed sequence IDs for agent ${agentId}:`,
				sequenceResult.missedSequenceIds
			);
		}

		if (sequenceResult.shouldRequestResync) {
			console.warn(`[Streaming] Too many missed sequences for agent ${agentId}, requesting resync`);
			// TODO: Implement resync logic
		}
	}

	// Handle exchange updates (type 1 = new exchange, type 2 = exchange update)
	console.log(`[Exchange] new update`, update);
	if (update.type === 1 && update.exchange) {
		handleNewExchange(agentId, update.exchange);
	} else if (update.type === 2 && update.exchangeUpdate) {
		handleExchangeUpdate(agentId, update.exchangeUpdate);
	} else if (update.agent) {
		// Merge the agent update with existing agent data to avoid losing properties
		const currentState = get(globalState);
		const parsedNewState = convertRemoteAgentFromAPI(update.agent);
		const currentAgent = currentState.agents[agentId];

		if (currentAgent) {
			// Check for agent status changes that might indicate completion
			const oldStatus = currentAgent.status;
			const newStatus = parsedNewState.status;

			// If agent went from RUNNING to IDLE, try to clear optimistic messages as fallback
			console.log(`[Agent Status] Agent ${agentId}: ${oldStatus} → ${newStatus}`);
			if (
				oldStatus === RemoteAgentStatus.AGENT_RUNNING &&
				newStatus === RemoteAgentStatus.AGENT_IDLE
			) {
				console.log(
					`[Agent Status] Agent ${agentId} went from RUNNING to IDLE, clearing optimistic messages`
				);
				// RUNNING -> IDLE
				// Try to clear any pending optimistic messages for this agent using the optimistic message manager
				cancelPendingMessagesForAgent(agentId);
				// clear optimistic messages
				clearOptimisticMessage(agentId);
			}

			// Merge the update with the existing agent data
			updateAgent({ ...currentAgent, ...parsedNewState });

			// Check if the update contains agent status information
			if (update.agent.status !== undefined) {
				// Check if we should continue or stop streaming based on new status
				if (!shouldContinueStreaming(agentId) && activeStreams.has(agentId)) {
					stopStreamingAgent(agentId);
				} else if (
					shouldContinueStreaming(agentId) &&
					!activeStreams.has(agentId) &&
					streamingSubscribers.has(agentId)
				) {
					// Agent became running again and we have subscribers, restart streaming
					startStreamingAgent(agentId).catch((error) => {
						console.error(`Failed to restart streaming for running agent ${agentId}:`, error);
					});
				}

				// Update chat session streaming state based on agent status
				// Only clear streaming state when agent becomes idle if there are no active streaming operations
				if (update.agent.status === RemoteAgentStatus.AGENT_IDLE) {
					// Get current session to check for active streaming operations
					const state = get(globalState);
					const session = Object.values(state.chatSessions).find(
						(sess) => sess.agentId === agentId
					);
					const currentAgent = state.agents[agentId];

					// Don't clear streaming state if:
					// 1. There are active streaming messages
					// 2. There's an optimistic message waiting
					// 3. The workspace is resuming (agent will likely become active again soon)
					const hasActiveStreamingMessages =
						session?.streamingMessages && session.streamingMessages.size > 0;
					const hasOptimisticMessage = session?.optimisticMessage !== undefined;
					const isWorkspaceResuming =
						currentAgent?.workspaceStatus ===
						RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_RESUMING;

					if (!hasActiveStreamingMessages && !hasOptimisticMessage && !isWorkspaceResuming) {
						setChatSessionStreaming(agentId, false);
					}
				} else if (update.agent.status === RemoteAgentStatus.AGENT_RUNNING) {
					setChatSessionStreaming(agentId, true);
				}
			}
		}
	} else {
		// For other chat/history updates, update the chat session directly
		// Note: With agent list streaming, we no longer need to reload agents
		// for every update as status changes come through the list stream
	}
}

// ============================================================================
// LEGACY POLLING FUNCTIONALITY (Fallback)
// ============================================================================

const pollingIntervals = new Map<string, NodeJS.Timeout>();
const streamHealthChecks = new Map<string, NodeJS.Timeout>();

/**
 * Start polling for agent updates (fallback for when streaming fails)
 */
export function startPollingAgent(agentId: string, intervalMs = 5000): void {
	// Stop existing polling if any
	stopPollingAgent(agentId);

	const poll = async () => {
		try {
			// Reload agent data (force reload to bypass cache)
			await loadAgents(true);

			// Reload chat history to get latest exchanges
			await loadChatHistory(agentId, true);
		} catch (error) {
			console.error('Polling error for agent:', agentId, error);
		}
	};

	// Start polling with longer interval since streaming is primary
	const interval = setInterval(poll, intervalMs);
	pollingIntervals.set(agentId, interval);

	// Do initial poll
	poll();
}

/**
 * Start enhanced polling for status transitions (more frequent polling for running agents)
 */
export function startEnhancedPollingAgent(agentId: string): void {
	// Stop existing polling if any
	stopPollingAgent(agentId);

	let pollCount = 0;
	const maxFastPolls = 12; // 12 * 2.5s = 30 seconds of fast polling

	const poll = async () => {
		try {
			const state = get(globalState);
			const agent = state.agents[agentId];

			// Reload agent data (force reload to bypass cache)
			await loadAgents(true);

			// Reload chat history to get latest exchanges
			await loadChatHistory(agentId, true);

			// Check if agent status changed to idle - if so, we can slow down polling
			const updatedState = get(globalState);
			const updatedAgent = updatedState.agents[agentId];

			if (
				updatedAgent?.status === RemoteAgentStatus.AGENT_IDLE &&
				agent?.status === RemoteAgentStatus.AGENT_RUNNING
			) {
				console.log(`Agent ${agentId} transitioned to IDLE, switching to normal polling`);
				stopPollingAgent(agentId);
				startPollingAgent(agentId, 10000); // Switch to slower polling
				return;
			}

			pollCount++;

			// After fast polling period, switch to normal polling
			if (pollCount >= maxFastPolls) {
				console.log(`Switching agent ${agentId} to normal polling after fast polling period`);
				stopPollingAgent(agentId);
				startPollingAgent(agentId, 8000);
			}
		} catch (error) {
			console.error('Enhanced polling error for agent:', agentId, error);
		}
	};

	// Start with faster polling for running agents (every 2.5 seconds)
	const interval = setInterval(poll, 2500);
	pollingIntervals.set(agentId, interval);

	// Do initial poll
	poll();
}

/**
 * Stop polling for agent updates
 */
export function stopPollingAgent(agentId: string): void {
	const interval = pollingIntervals.get(agentId);
	if (interval) {
		clearInterval(interval);
		pollingIntervals.delete(agentId);
	}
}

/**
 * Stop all polling
 */
export function stopAllPolling(): void {
	for (const [agentId] of pollingIntervals) {
		stopPollingAgent(agentId);
	}
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Clear all cached data
 */
export function clearCache(): void {
	globalState.update((s) => ({
		...s,
		cache: {
			agents: 0,
			triggers: 0,
			executions: {},
			entities: {},
			chatSessions: {}
		}
	}));
}

/**
 * Get cache status for debugging
 */
export function getCacheStatus() {
	const state = get(globalState);
	const now = Date.now();

	return {
		agents: {
			lastFetch: state.cache.agents,
			isValid: isCacheValid('agents'),
			ageMs: state.cache.agents ? now - state.cache.agents : null
		},
		triggers: {
			lastFetch: state.cache.triggers,
			isValid: isCacheValid('triggers'),
			ageMs: state.cache.triggers ? now - state.cache.triggers : null
		},
		executions: Object.keys(state.cache.executions).length,
		entities: Object.keys(state.cache.entities).length
	};
}
