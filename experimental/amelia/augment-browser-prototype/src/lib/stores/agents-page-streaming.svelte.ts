/**
 * Agents Page Streaming Manager
 *
 * Manages streaming subscriptions for agents displayed on the /agents page.
 * Handles subscribing to streaming when agents are visible and cleaning up
 * when they're no longer needed.
 */

import {
	subscribeToAgentStreaming,
	unsubscribeFromAgentStreaming,
	isStreamingActive
} from './data-operations.svelte';
import { RemoteAgentStatus } from '$lib/api/unified-client';
import type { CleanRemoteAgent } from '$lib/api/unified-client';

// Component ID for the agents page
const AGENTS_PAGE_COMPONENT_ID = 'agents-page';

// Track which agents we're currently subscribed to
const subscribedAgents = new Set<string>();

/**
 * Subscribe to streaming for agents that should be streaming
 */
export function subscribeToAgentsStreaming(agents: CleanRemoteAgent[]): void {
	const agentsToSubscribe = agents.filter(shouldSubscribeToAgent);

	for (const agent of agentsToSubscribe) {
		if (!subscribedAgents.has(agent.id)) {
			console.log(`[AgentsPage] Subscribing to streaming for agent: ${agent.id}`);
			subscribeToAgentStreaming(agent.id, AGENTS_PAGE_COMPONENT_ID);
			subscribedAgents.add(agent.id);
		}
	}
}

/**
 * Unsubscribe from streaming for agents that are no longer visible or needed
 */
export function unsubscribeFromAgentsStreaming(currentAgents: CleanRemoteAgent[]): void {
	const currentAgentIds = new Set(currentAgents.map(a => a.id));
	const agentsToUnsubscribe = Array.from(subscribedAgents).filter(
		agentId => !currentAgentIds.has(agentId) ||
		!shouldSubscribeToAgent(currentAgents.find(a => a.id === agentId)!)
	);

	for (const agentId of agentsToUnsubscribe) {
		console.log(`[AgentsPage] Unsubscribing from streaming for agent: ${agentId}`);
		unsubscribeFromAgentStreaming(agentId, AGENTS_PAGE_COMPONENT_ID);
		subscribedAgents.delete(agentId);
	}
}

/**
 * Clean up all streaming subscriptions for the agents page
 */
export function cleanupAgentsPageStreaming(): void {
	console.log(`[AgentsPage] Cleaning up all streaming subscriptions`);

	for (const agentId of subscribedAgents) {
		unsubscribeFromAgentStreaming(agentId, AGENTS_PAGE_COMPONENT_ID);
	}

	subscribedAgents.clear();
}

/**
 * Update streaming subscriptions based on current agents list
 */
export function updateAgentsStreaming(agents: CleanRemoteAgent[]): void {
	// First unsubscribe from agents that are no longer relevant
	unsubscribeFromAgentsStreaming(agents);

	// Then subscribe to new agents that need streaming
	subscribeToAgentsStreaming(agents);
}

/**
 * Get the current subscription status
 */
export function getSubscriptionStatus(): {
	subscribedCount: number;
	subscribedAgents: string[];
} {
	return {
		subscribedCount: subscribedAgents.size,
		subscribedAgents: Array.from(subscribedAgents)
	};
}

/**
 * Force refresh streaming for all subscribed agents
 */
export function refreshAgentsStreaming(): void {
	console.log(`[AgentsPage] Refreshing streaming for ${subscribedAgents.size} agents`);

	// Unsubscribe and resubscribe to refresh connections
	const agentIds = Array.from(subscribedAgents);

	for (const agentId of agentIds) {
		unsubscribeFromAgentStreaming(agentId, AGENTS_PAGE_COMPONENT_ID);
		subscribedAgents.delete(agentId);
	}

	// Small delay before resubscribing
	setTimeout(() => {
		for (const agentId of agentIds) {
			subscribeToAgentStreaming(agentId, AGENTS_PAGE_COMPONENT_ID);
			subscribedAgents.add(agentId);
		}
	}, 100);
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Determine if we should subscribe to streaming for an agent
 */
function shouldSubscribeToAgent(agent: CleanRemoteAgent | undefined): boolean {
	if (!agent) return false;

	// Subscribe to agents that are running or have recent activity
	const isRunning = agent.status === RemoteAgentStatus.AGENT_RUNNING;
	const isIdle = agent.status === RemoteAgentStatus.AGENT_IDLE;
	const hasRecentActivity = agent.hasUpdates;

	// Subscribe if:
	// 1. Agent is currently running (actively working)
	// 2. Agent is idle but has recent updates (might resume)
	// 3. Agent is already streaming (to maintain connection)
	return isRunning || (isIdle && hasRecentActivity) || isStreamingActive(agent.id);
}

/**
 * Debug function to log current streaming state
 */
export function debugAgentsPageStreaming(): void {
	console.log('=== AGENTS PAGE STREAMING DEBUG ===');
	console.log('Subscribed agents:', subscribedAgents.size);

	for (const agentId of subscribedAgents) {
		console.log(`Agent ${agentId}:`, {
			isStreamingActive: isStreamingActive(agentId)
		});
	}

	console.log('=== END AGENTS PAGE STREAMING DEBUG ===');
}

// Expose debug function to window for development
if (typeof window !== 'undefined') {
	(window as any).debugAgentsPageStreaming = debugAgentsPageStreaming;
}
