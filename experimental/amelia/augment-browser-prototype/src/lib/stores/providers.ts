/**
 * Provider State Management
 *
 * This store manages the state of all providers, their connections,
 * and their entities.
 */

import { writable, derived, get } from 'svelte/store';
import type { Project } from '$lib/types';
import type {
	Provider,
	ProviderState,
	ProviderConnection,
	EntityListState,
	ProviderEntity,
	EntityType
} from '$lib/providers/types';
import {
	PROVIDERS,
	getConfiguredProviders,
	fetchProviderEntities,
	getProviderConfig
} from '$lib/providers';
import { githubConnected, linearConnected } from '$lib/stores/integrations';

// Provider states store
export const providerStates = writable<Record<string, ProviderState>>({});

// Current project store (for provider context)
export const currentProject = writable<Project | null>(null);

// Initialize provider states
function initializeProviderStates() {
	const initialStates: Record<string, ProviderState> = {};

	PROVIDERS.forEach((provider) => {
		initialStates[provider.id] = {
			connection: {
				providerId: provider.id,
				isConnected: false
			},
			entities: {
				issues: { entities: [], isLoading: false },
				pull_requests: { entities: [], isLoading: false },
				tickets: { entities: [], isLoading: false },
				incidents: { entities: [], isLoading: false },
				tasks: { entities: [], isLoading: false }
			}
		};
	});

	providerStates.set(initialStates);
}

// Initialize on module load
initializeProviderStates();

// Sync with existing integration stores
githubConnected.subscribe((isConnected) => {
	providerStates.update((states) => ({
		...states,
		github: {
			...states.github,
			connection: {
				...states.github?.connection,
				providerId: 'github',
				isConnected
			}
		}
	}));
});

linearConnected.subscribe((isConnected) => {
	providerStates.update((states) => ({
		...states,
		linear: {
			...states.linear,
			connection: {
				...states.linear?.connection,
				providerId: 'linear',
				isConnected
			}
		}
	}));
});

/**
 * Get provider state
 */
export function getProviderState(providerId: string) {
	return derived(providerStates, ($states) => $states[providerId]);
}

/**
 * Get provider connection status
 */
export function getProviderConnection(providerId: string) {
	return derived(providerStates, ($states) => $states[providerId]?.connection);
}
export function getProviderConnections() {
	return derived(providerStates, ($states) =>
		Object.values($states).map((state) => state.connection)
	);
}

/**
 * Get entities for a provider and entity type
 */
export function getProviderEntities(providerId: string, entityType: EntityType) {
	return derived(
		providerStates,
		($states) => $states[providerId]?.entities[entityType] || { entities: [], isLoading: false }
	);
}

/**
 * Get all entities across all providers
 */
export const allProviderEntities = derived(providerStates, ($states) => {
	const allEntities: ProviderEntity[] = [];

	Object.values($states).forEach((state) => {
		Object.values(state.entities).forEach((entityList) => {
			allEntities.push(...entityList.entities);
		});
	});

	return allEntities;
});

/**
 * Get configured providers for current project
 */
export const configuredProviders = derived([currentProject], ([$currentProject]) => {
	if (!$currentProject) return [];
	return getConfiguredProviders($currentProject);
});

/**
 * Update provider connection
 */
export function updateProviderConnection(
	providerId: string,
	connection: Partial<ProviderConnection>
) {
	providerStates.update((states) => ({
		...states,
		[providerId]: {
			...states[providerId],
			connection: {
				...states[providerId]?.connection,
				providerId,
				...connection
			}
		}
	}));
}

/**
 * Update entity list state
 */
export function updateEntityListState(
	providerId: string,
	entityType: EntityType,
	update: Partial<EntityListState>
) {
	providerStates.update((states) => ({
		...states,
		[providerId]: {
			...states[providerId],
			entities: {
				...states[providerId]?.entities,
				[entityType]: {
					...states[providerId]?.entities[entityType],
					...update
				}
			}
		}
	}));
}

/**
 * Fetch entities for a provider
 */
export async function fetchEntitiesForProvider(providerId: string, entityType: EntityType) {
	// Set loading state
	updateEntityListState(providerId, entityType, { isLoading: true, error: undefined });

	try {
		const entities = await fetchProviderEntities(providerId, entityType);
		updateEntityListState(providerId, entityType, {
			entities,
			isLoading: false,
			lastFetchAt: new Date().toISOString()
		});
	} catch (error) {
		updateEntityListState(providerId, entityType, {
			isLoading: false,
			error: error instanceof Error ? error.message : 'Failed to fetch entities'
		});
	}
}

/**
 * Fetch all entities for configured providers
 */
export async function fetchAllEntities() {
	const providers = getConfiguredProviders();

	const fetchPromises = providers.flatMap((provider) =>
		provider.entities.map((entityConfig) =>
			fetchEntitiesForProvider(provider.id, entityConfig.type)
		)
	);

	await Promise.allSettled(fetchPromises);
}
