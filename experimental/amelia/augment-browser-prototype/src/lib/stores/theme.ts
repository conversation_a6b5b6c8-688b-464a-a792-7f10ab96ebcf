import { writable } from 'svelte/store';
import { browser } from '$app/environment';

type Theme = 'light' | 'dark';

// Get initial theme from localStorage or system preference
function getInitialTheme(): Theme {
	if (!browser) return 'dark';

	const stored = localStorage.getItem('theme') as Theme;
	if (stored) return stored;

	// Check system preference
	if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
		return 'dark';
	}

	return 'light';
}

export const theme = writable<Theme>(getInitialTheme());

// Subscribe to theme changes and update localStorage and document class
if (browser) {
	theme.subscribe((value) => {
		localStorage.setItem('theme', value);

		// Update document class
		if (value === 'dark') {
			document.documentElement.classList.add('dark');
			document.body.classList.add('dark');
		} else {
			document.documentElement.classList.remove('dark');
			document.body.classList.remove('dark');
		}
	});

	// Note: Initial class is now set in app.html to prevent FOUC
	// No need to set it again here since the HTML script handles it
}

export function toggleTheme() {
	theme.update((current) => (current === 'light' ? 'dark' : 'light'));
}
