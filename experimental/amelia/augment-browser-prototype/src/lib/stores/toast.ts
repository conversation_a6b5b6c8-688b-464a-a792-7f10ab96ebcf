import { writable } from 'svelte/store';

export interface Toast {
	id: string;
	message: string;
	type: 'success' | 'error' | 'warning' | 'info';
	duration?: number; // in milliseconds, 0 means no auto-dismiss
	action?: {
		label: string;
		handler: () => void;
	};
}

export const toasts = writable<Toast[]>([]);

let toastId = 0;

export function addToast(toast: Omit<Toast, 'id'>): string {
	const id = `toast-${++toastId}`;
	const newToast: Toast = {
		id,
		...toast,
		duration: toast.duration ?? 5000 // Default 5 seconds
	};

	toasts.update((currentToasts) => [...currentToasts, newToast]);

	// Auto-dismiss if duration is set and > 0
	if (newToast.duration && newToast.duration > 0) {
		setTimeout(() => {
			removeToast(id);
		}, newToast.duration);
	}

	return id;
}

export function removeToast(id: string) {
	toasts.update((currentToasts) => currentToasts.filter((toast) => toast.id !== id));
}

export function clearAllToasts() {
	toasts.set([]);
}

// Convenience functions for different toast types
export function showSuccess(message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) {
	return addToast({ message, type: 'success', ...options });
}

export function showError(message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) {
	return addToast({ message, type: 'error', ...options });
}

export function showWarning(message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) {
	return addToast({ message, type: 'warning', ...options });
}

export function showInfo(message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) {
	return addToast({ message, type: 'info', ...options });
}
