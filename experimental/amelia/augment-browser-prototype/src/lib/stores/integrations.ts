import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import type { GitHubIssue, LinearIssue, Issue } from '$lib/types';

// Integration connection status
export const githubConnected = writable(false);
export const linearConnected = writable(false);

// Integration tokens (stored in secure server-side cookies)
export const githubToken = writable<string | null>(null);
export const linearToken = writable<string | null>(null);

// Issues data
export const githubIssues = writable<GitHubIssue[]>([]);
export const linearIssues = writable<LinearIssue[]>([]);

// Loading states
export const isLoadingGitHub = writable(false);
export const isLoadingLinear = writable(false);

// Error states
export const githubError = writable<string | null>(null);
export const linearError = writable<string | null>(null);

// Combined issues store
export const allIssues = derived([githubIssues, linearIssues], ([$githubIssues, $linearIssues]) => {
	const combined: Issue[] = [];

	// Convert GitHub issues
	$githubIssues.forEach((issue) => {
		combined.push({
			id: `github-${issue.id}`,
			title: issue.title,
			description: issue.body,
			state: issue.state,
			source: 'github',
			sourceId: issue.id,
			url: issue.url,
			assignee: issue.assignees[0]
				? {
						name: issue.assignees[0].login,
						avatar: issue.assignees[0].avatarUrl
					}
				: undefined,
			labels: issue.labels.map((label) => ({
				name: label.name,
				color: `#${label.color}`
			})),
			createdAt: issue.createdAt,
			updatedAt: issue.updatedAt
		});
	});

	// Convert Linear issues
	$linearIssues.forEach((issue) => {
		combined.push({
			id: `linear-${issue.id}`,
			title: issue.title,
			description: issue.description,
			state: issue.state.name,
			source: 'linear',
			sourceId: issue.id,
			url: issue.url,
			assignee: issue.assignee
				? {
						name: issue.assignee.name,
						avatar: issue.assignee.avatarUrl
					}
				: undefined,
			labels: issue.labels.map((label) => ({
				name: label.name,
				color: label.color
			})),
			createdAt: issue.createdAt,
			updatedAt: issue.updatedAt
		});
	});

	// Sort by updated date (most recent first)
	return combined.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
});

// Derived loading state
export const isLoadingIssues = derived(
	[isLoadingGitHub, isLoadingLinear],
	([$isLoadingGitHub, $isLoadingLinear]) => $isLoadingGitHub || $isLoadingLinear
);

// Derived connection status
export const hasConnections = derived(
	[githubConnected, linearConnected],
	([$githubConnected, $linearConnected]) => $githubConnected || $linearConnected
);

// Note: Legacy localStorage initialization removed for security
// Tokens are now managed through secure server-side sessions

// Initialize tokens from server-side session on browser load
if (browser) {
	// Check for existing sessions on load
	fetch('/api/auth/session')
		.then((response) => response.json())
		.then((sessionInfo) => {
			if (sessionInfo.github) {
				githubConnected.set(true);
			}
			if (sessionInfo.linear) {
				linearConnected.set(true);
			}
		})
		.catch((error) => {
			console.error('Failed to check integration sessions:', error);
		});
}

// API functions
export async function connectGitHub(token: string) {
	try {
		// Test the token by making a simple API call
		const response = await fetch('/api/github', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				action: 'testConnection',
				token
			})
		});

		if (!response.ok) {
			throw new Error('Invalid GitHub token');
		}

		githubToken.set(token);
		githubError.set(null);
		return true;
	} catch (error) {
		githubError.set(error instanceof Error ? error.message : 'Failed to connect to GitHub');
		return false;
	}
}

// OAuth-based GitHub connection (called from OAuth callback)
export async function connectGitHubWithOAuth(token: string) {
	return connectGitHub(token);
}

export async function connectLinear(token: string) {
	try {
		// Test the token by making a simple API call
		const response = await fetch('/api/linear', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				action: 'testConnection',
				token
			})
		});

		if (!response.ok) {
			throw new Error('Invalid Linear token');
		}

		linearToken.set(token);
		linearError.set(null);
		return true;
	} catch (error) {
		linearError.set(error instanceof Error ? error.message : 'Failed to connect to Linear');
		return false;
	}
}

export function disconnectGitHub() {
	githubToken.set(null);
	githubIssues.set([]);
	githubError.set(null);
}

export function disconnectLinear() {
	linearToken.set(null);
	linearIssues.set([]);
	linearError.set(null);
}

export async function fetchGitHubIssues(
	repo: string,
	options: {
		state?: 'open' | 'closed' | 'all';
		first?: number;
	} = {}
) {
	let token: string | null = null;
	const unsubscribe = githubToken.subscribe((t) => (token = t));
	unsubscribe();

	if (!repo || !token) return;

	isLoadingGitHub.set(true);
	githubError.set(null);

	try {
		const response = await fetch('/api/github', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				action: 'getIssues',
				repo,
				token,
				...options
			})
		});

		if (!response.ok) {
			throw new Error('Failed to fetch GitHub issues');
		}

		const data = await response.json();
		githubIssues.set(data.issues || []);
		return data;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Failed to fetch GitHub issues';
		githubError.set(errorMessage);
		githubIssues.set([]);
		throw error;
	} finally {
		isLoadingGitHub.set(false);
	}
}

export async function fetchGitHubWorkflowRuns(
	repo: string,
	options: {
		state?: 'open' | 'closed' | 'all';
		first?: number;
	} = {}
) {
	let token: string | null = null;
	const unsubscribe = githubToken.subscribe((t) => (token = t));
	unsubscribe();

	if (!repo || !token) return;

	isLoadingGitHub.set(true);
	githubError.set(null);

	try {
		const response = await fetch('/api/github', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				action: 'getWorkflowRuns',
				repo,
				token,
				...options
			})
		});

		if (!response.ok) {
			throw new Error('Failed to fetch GitHub workflow runs');
		}

		const data = await response.json();
		return data;
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : 'Failed to fetch GitHub workflow runs';
		githubError.set(errorMessage);
		throw error;
	} finally {
		isLoadingGitHub.set(false);
	}
}

export async function fetchGitHubPullRequests(
	repo: string,
	options: {
		state?: 'open' | 'closed' | 'merged' | 'all';
		first?: number;
	} = {}
) {
	let token: string | null = null;
	const unsubscribe = githubToken.subscribe((t) => (token = t));
	unsubscribe();

	if (!repo || !token) return;

	isLoadingGitHub.set(true);
	githubError.set(null);

	try {
		const response = await fetch('/api/github', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				action: 'getPullRequests',
				repo,
				token,
				...options
			})
		});

		if (!response.ok) {
			throw new Error('Failed to fetch GitHub pull requests');
		}

		const data = await response.json();
		return data;
	} catch (error) {
		const errorMessage =
			error instanceof Error ? error.message : 'Failed to fetch GitHub pull requests';
		githubError.set(errorMessage);
		throw error;
	} finally {
		isLoadingGitHub.set(false);
	}
}

export async function fetchLinearIssues(
	projectId?: string,
	options: {
		first?: number;
		filter?: {
			state?: string;
			assignee?: string;
		};
	} = {}
) {
	let token: string | null = null;
	const unsubscribe = linearToken.subscribe((t) => (token = t));
	unsubscribe();

	if (!token) return;

	isLoadingLinear.set(true);
	linearError.set(null);

	try {
		const response = await fetch('/api/linear', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			},
			body: JSON.stringify({
				action: 'getIssues',
				projectId,
				token,
				...options
			})
		});

		if (!response.ok) {
			throw new Error('Failed to fetch Linear issues');
		}

		const data = await response.json();
		linearIssues.set(data.issues || []);
		return data;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Failed to fetch Linear issues';
		linearError.set(errorMessage);
		linearIssues.set([]);
		throw error;
	} finally {
		isLoadingLinear.set(false);
	}
}

// Clear all issues
export function clearIssues() {
	githubIssues.set([]);
	linearIssues.set([]);
	githubError.set(null);
	linearError.set(null);
}

// Derived stores for filtering
export const openIssues = derived(allIssues, ($allIssues) =>
	$allIssues.filter(
		(issue) => issue.state === 'open' || issue.state === 'unstarted' || issue.state === 'started'
	)
);

export const closedIssues = derived(allIssues, ($allIssues) =>
	$allIssues.filter(
		(issue) => issue.state === 'closed' || issue.state === 'completed' || issue.state === 'canceled'
	)
);

export const issuesBySource = derived(allIssues, ($allIssues) => {
	const bySource = {
		github: $allIssues.filter((issue) => issue.source === 'github'),
		linear: $allIssues.filter((issue) => issue.source === 'linear'),
		local: $allIssues.filter((issue) => issue.source === 'local')
	};
	return bySource;
});
