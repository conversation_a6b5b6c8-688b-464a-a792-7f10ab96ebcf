import { derived } from 'svelte/store';
import { agents as agentsList } from './global-state.svelte';
import { RemoteAgentWorkspaceStatus } from '$lib/api/unified-client';

/**
 * Configuration for agent limits and constraints
 */
export interface AgentLimitsConfig {
	maxActiveAgents: number; // Max non-paused agents
	maxTotalAgents: number; // Max total agents (including paused)
	warningThreshold: number; // Percentage of max before showing warnings
}

/**
 * Default configuration - can be overridden by server settings
 */
const DEFAULT_CONFIG: AgentLimitsConfig = {
	maxActiveAgents: 10, // 10 active (non-paused) agents
	maxTotalAgents: 100, // 100 total agents
	warningThreshold: 1
};

/**
 * Agent limits configuration state - fully reactive with Svelte 5
 */
let _agentLimitsConfig = $state<AgentLimitsConfig>(DEFAULT_CONFIG);

/**
 * Reactive getters for the state using Svelte 5 patterns
 */
export const agentLimitsConfig = {
	get current() {
		return _agentLimitsConfig;
	},
	update(newConfig: Partial<AgentLimitsConfig>) {
		_agentLimitsConfig = { ..._agentLimitsConfig, ...newConfig };
	}
};

/**
 * Get current agent statistics - reactive getter function
 */
export function getAgentStats() {
	return derived(agentsList, ($agents) => {
		const currentAgents = $agents || [];

		// Count active agents (non-paused agents)
		const activeAgents = currentAgents.filter(
			(agent) =>
				agent.workspaceStatus !== RemoteAgentWorkspaceStatus.REMOTE_AGENT_WORKSPACE_STATUS_PAUSED
		);

		const maxActiveAgents = _agentLimitsConfig.maxActiveAgents || 50;
		const maxTotalAgents = _agentLimitsConfig.maxTotalAgents || 100;

		// Count total agents
		const totalAgents = currentAgents;

		// Calculate percentages
		const activePercentage = activeAgents.length / maxActiveAgents;
		const totalPercentage = totalAgents.length / maxTotalAgents;

		return {
			activeCount: activeAgents.length,
			maxActive: maxActiveAgents,
			activePercentage,
			totalCount: totalAgents.length,
			maxTotal: maxTotalAgents,
			totalPercentage,
			isAtActiveLimit: activeAgents.length >= maxActiveAgents,
			isAtTotalLimit: totalAgents.length >= maxTotalAgents,
			isNearActiveLimit: activePercentage >= _agentLimitsConfig.warningThreshold,
			isNearTotalLimit: totalPercentage >= _agentLimitsConfig.warningThreshold,
			canCreateAgent: activeAgents.length < maxActiveAgents && totalAgents.length < maxTotalAgents
		};
	});
}

/**
 * Update agent limits configuration (e.g., from server settings)
 */
export function updateAgentLimitsConfig(newConfig: Partial<AgentLimitsConfig>): void {
	agentLimitsConfig.update(newConfig);
}

/**
 * Get validation errors for agent creation
 */
export function getAgentCreationValidation(stats: {
	activeCount: number;
	maxActive: number;
	activePercentage: number;
	totalCount: number;
	maxTotal: number;
	totalPercentage: number;
	isAtActiveLimit: boolean;
	isAtTotalLimit: boolean;
	isNearActiveLimit: boolean;
	isNearTotalLimit: boolean;
	canCreateAgent: boolean;
}): {
	canCreate: boolean;
	errors: string[];
	warnings: string[];
} {
	const errors: string[] = [];
	const warnings: string[] = [];

	// Check hard limits
	if (stats.isAtActiveLimit) {
		errors.push(
			`You've reached the maximum of ${stats.maxActive} active (non-paused) agents. Please pause some agents to create new ones.`
		);
	}

	if (stats.isAtTotalLimit) {
		errors.push(
			`You've reached the maximum of ${stats.maxTotal} total agents. Please delete some agents to create new ones.`
		);
	}

	// Check warning thresholds
	if (stats.isNearActiveLimit && !stats.isAtActiveLimit) {
		warnings.push(
			`You're approaching the limit of ${stats.maxActive} active agents (currently ${stats.activeCount}).`
		);
	}

	if (stats.isNearTotalLimit && !stats.isAtTotalLimit) {
		warnings.push(
			`You're approaching the limit of ${stats.maxTotal} total agents (currently ${stats.totalCount}).`
		);
	}

	return {
		canCreate: stats.canCreateAgent,
		errors,
		warnings
	};
}

/**
 * Initialize the agent limits system
 * Note: Limits are now automatically updated when agents are loaded via loadAgents()
 * which fetches max_remote_agents and max_active_remote_agents from the API
 */
export function initializeAgentLimits(): void {
	// Limits are automatically updated from the remote agents list API response
	// No manual initialization needed
}
