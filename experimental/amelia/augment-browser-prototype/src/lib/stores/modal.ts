import { writable } from 'svelte/store';
import type { UnifiedEntity } from '$lib/utils/entity-conversion';
import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';
import type { GitHubRepo, GitHubBranch } from '$lib/api/github-api';

export interface ModalState {
	isOpen: boolean;
	type: 'agent-creation' | null;
	props?: any;
}

export interface AgentCreationModalProps {
	preselectedEntity?: UnifiedEntity | null;
	preselectedTrigger?: NormalizedTrigger | null;
	preselectedRepository?: GitHubRepo | null;
	preselectedBranch?: GitHubBranch | null;
	preselectedEntityId?: string | null;
	preselectedTriggerId?: string | null;
	onClose?: () => void;
}

export const modalState = writable<ModalState>({
	isOpen: false,
	type: null,
	props: undefined
});

export function openAgentCreationModal(props?: AgentCreationModalProps) {
	modalState.set({
		isOpen: true,
		type: 'agent-creation',
		props
	});
}

export function closeModal() {
	modalState.set({
		isOpen: false,
		type: null,
		props: undefined
	});
}
