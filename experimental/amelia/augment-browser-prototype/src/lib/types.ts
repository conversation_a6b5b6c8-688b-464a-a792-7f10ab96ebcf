// Base database model types (previously from Prisma)
export interface PrismaUser {
	id: string;
	name: string;
	email: string;
	avatar?: string | null;
	createdAt: Date;
	updatedAt: Date;
}

export interface PrismaTask {
	id: string;
	title: string;
	description: string;
	status: string;
	assigneeId?: string | null;
	parentId?: string | null;
	remoteAgentId?: string | null;
	createdAt: Date;
	updatedAt: Date;
	prId?: number | null;
	prStatus?: string | null;
	prTurnIndex?: number | null;
	prUrl?: string | null;
}

export interface PrismaTaskReference {
	id: string;
	taskId: string;
	type: string;
	sourceId: string;
	title: string;
	description?: string | null;
	url?: string | null;
	state?: string | null;
	metadata?: string | null;
	createdAt: Date;
	updatedAt: Date;
}

export interface PrismaTrigger {
	id: string;
	name: string;
	description?: string | null;
	instructions: string;
	isActive: boolean;
	sourceType: string;
	sourceProvider?: string | null;
	sourceEvent?: string | null;
	workspaceSetup?: string | null;
	conditions?: string | null;
	maxPerHour?: number | null;
	debounceSeconds?: number | null;
	createdAt: Date;
	updatedAt: Date;
}

export interface PrismaTriggerExecution {
	id: string;
	triggerId: string;
	status: string;
	taskId?: string | null;
	agentId?: string | null;
	payload?: string | null;
	error?: string | null;
	startedAt: Date;
	completedAt?: Date | null;
}

export type TaskReference = PrismaTaskReference & {
	parsedMetadata?: any; // Parsed JSON metadata
};

export type Task = PrismaTask & {
	assignee?: PrismaUser | null;
	parent?: Task | null;
	subtasks?: Task[];
	references?: TaskReference[];
};

export type User = PrismaUser;

// Define types that aren't enums in Prisma schema
export type TaskStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
export type ReferenceType = 'GITHUB_ISSUE' | 'GITHUB_PR' | 'LINEAR_ISSUE' | 'JIRA_TICKET' | 'URL';
export type TriggerSourceType = 'WEBHOOK' | 'SCHEDULED' | 'API';
export type TriggerExecutionStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';

// Enhanced task status for UI representation
export type TaskUIStatus =
	| 'no_agent' // Task has no remote agent assigned
	| 'running' // Agent is actively working
	| 'failed' // Agent encountered an error
	| 'idle' // Agent is idle and ready for work
	| 'waiting' // Agent is idle but no code changes have been made
	| 'starting' // Agent is starting up
	| 'pending' // Agent is pending assignment
	| 'completed'; // Task is completed (PR merged)

// Helper types for API
export type CreateTaskInput = {
	title: string;
	description: string;
	status?: TaskStatus;
	assigneeId?: string;
	parentId?: string;
	remoteAgentId?: string;
	prId?: number;
	prUrl?: string;
	prStatus?: string;
	prTurnIndex?: number;
	references?: CreateTaskReferenceInput[];
};

export type UpdateTaskInput = Partial<CreateTaskInput>;

// Task Reference types
export type CreateTaskReferenceInput = {
	type: ReferenceType;
	sourceId: string;
	title: string;
	description?: string;
	url?: string;
	state?: string;
	metadata?: any; // Will be JSON stringified
};

export type UpdateTaskReferenceInput = Partial<CreateTaskReferenceInput>;

// GitHub Integration
export interface GitHubIssue {
	id: string;
	number: number;
	title: string;
	body?: string;
	state: 'open' | 'closed';
	labels: GitHubLabel[];
	assignees: GitHubUser[];
	author: GitHubUser;
	createdAt: string;
	updatedAt: string;
	url: string;
}

export interface GitHubLabel {
	id: string;
	name: string;
	color: string;
	description?: string;
}

export interface GitHubUser {
	id: string;
	login: string;
	avatarUrl: string;
	url: string;
}

// Linear Integration
export interface LinearIssue {
	id: string;
	identifier: string; // e.g., "ENG-123"
	title: string;
	description?: string;
	state: LinearIssueState;
	priority: number; // 0-4
	labels: LinearLabel[];
	assignee?: LinearUser;
	creator: LinearUser;
	createdAt: string;
	updatedAt: string;
	url: string;
}

export interface LinearIssueState {
	id: string;
	name: string;
	type: 'backlog' | 'unstarted' | 'started' | 'completed' | 'canceled';
}

export interface LinearLabel {
	id: string;
	name: string;
	color: string;
}

export interface LinearUser {
	id: string;
	name: string;
	email: string;
	avatarUrl?: string;
}

// Combined Issue Type
export interface Issue {
	id: string;
	title: string;
	description?: string;
	state: string;
	source: 'github' | 'linear' | 'local';
	sourceId: string;
	url?: string;
	assignee?: {
		name: string;
		avatar?: string;
	};
	labels: {
		name: string;
		color: string;
	}[];
	createdAt: string;
	updatedAt: string;
}

// Setup Scripts
export interface SetupScript {
	name: string; // Display name like "install-deps.sh"
	path: string; // Virtual path or identifier
	content: string; // Script content
	location: 'browser' | 'uploaded' | 'template';
	isGenerateOption?: boolean; // Special "generate" option
}

export interface SetupScriptsResponse {
	scripts: SetupScript[];
	searchPaths: string[];
	totalFound: number;
}

// GitHub Setup Scripts
export interface GithubSetupScript {
	name: string; // Script filename
	displayName: string; // Human-readable name
}

export interface ListGithubSetupScriptsRequest {
	githubRef: string; // e.g. "owner/repo@ref"
}

export interface ListGithubSetupScriptsResponse {
	scripts: GithubSetupScript[];
	errorMessage?: string;
}

export interface ReadGithubSetupScriptRequest {
	githubRef: string; // e.g. "owner/repo@ref"
	scriptName: string; // Script filename
}

export interface ReadGithubSetupScriptResponse {
	content: string;
	errorMessage?: string;
}

// Triggers
export type TriggerCondition = {
	field: string;
	operator:
		| 'equals'
		| 'contains'
		| 'includes'
		| 'greater_than'
		| 'less_than'
		| 'not_equals'
		| 'starts_with'
		| 'ends_with'
		| 'in'
		| 'not_in'
		| 'exists'
		| 'not_exists';
	value: any;
};

export type TriggerWorkspaceSetup = {
	githubRef?: string;
	url?: string;
	ref?: string;
	patch?: string;
	setupScript?: string;
	apiUrl?: string;
};

export type TriggerRateLimit = {
	maxPerHour?: number;
	debounceSeconds?: number;
};

export type Trigger = PrismaTrigger & {
	executions?: TriggerExecution[];
	parsedConditions?: TriggerCondition[];
	parsedWorkspaceSetup?: TriggerWorkspaceSetup;
};

export type TriggerExecution = PrismaTriggerExecution & {
	trigger?: Trigger;
	task?: Task;
	parsedPayload?: any;
};

export type CreateTriggerInput = {
	name: string;
	description?: string;
	instructions: string;
	isActive?: boolean;
	sourceType: TriggerSourceType;
	sourceProvider?: string;
	sourceEvent?: string;
	workspaceSetup?: TriggerWorkspaceSetup;
	conditions?: TriggerCondition[];
	maxPerHour?: number;
	debounceSeconds?: number;
};

export type UpdateTriggerInput = Partial<CreateTriggerInput>;

// Provider-specific trigger definitions
export type TriggerFieldType = 'string' | 'number' | 'boolean' | 'array' | 'object' | 'date';

export type TriggerField = {
	name: string;
	path: string; // JSONPath to the field in webhook payload
	type: TriggerFieldType;
	description: string;
	example?: any;
	allowedOperators: TriggerCondition['operator'][];
};

export type TriggerEvent = {
	name: string;
	description: string;
	fields: TriggerField[];
	examplePayload?: any;
	getLabel?: (trigger: Trigger) => string;
};

export type TriggerProvider = {
	id: string;
	name: string;
	description: string;
	iconName?: string;
	iconSrc?: any; // For Svelte component icons like Clock
	events: TriggerEvent[];
	webhookUrl?: string; // Template URL for webhook setup
	documentationUrl?: string;
};

// Re-export provider types for convenience
export type {
	Provider,
	ProviderEntity,
	EntityType,
	BaseEntity,
	ProviderEntityConfig,
	ProviderConnection,
	ProviderState,
	EntityListState
} from '$lib/providers/types';

// Import EntityType for use in this file
import type { EntityType } from '$lib/providers/types';

// GitHub Pull Request type
export interface GitHubPullRequest {
	id: string;
	number: number;
	title: string;
	body: string;
	state: 'open' | 'closed' | 'merged';
	draft: boolean;
	url: string;
	head: {
		ref: string;
		sha: string;
	};
	base: {
		ref: string;
		sha: string;
	};
	createdAt: string;
	updatedAt: string;
	mergedAt?: string;
	author: GitHubUser;
}

// Remote Agent Actions API Types
export type ExecuteTriggerManuallyRequest = {
	triggerId: string;
	entityId: string;
	extraPrompt?: string;
};

export type ExecuteTriggerManuallyResponse = {
	executionId: string;
	remoteAgentId?: string;
	status: TriggerExecutionStatus;
	errorMessage?: string;
};

export type ExecuteManualAgentRequest = {
	entityId: string;
	entityType: string;
	agentConfig: any; // AgentConfig type would need to be defined
};

export type ExecuteManualAgentResponse = {
	executionId: string;
	remoteAgentId: string;
	message: string;
};

export type GetEntityDetailsRequest = {
	entityId: string;
	eventSource: string; // 'github', 'linear', etc.
	entityType: string;
	repository?: string;
};

export type GetEntityDetailsResponse = {
	found: boolean;
	entityData?: string; // JSON-encoded entity data
	errorMessage?: string;
};

// Entity Lists (combining triggers and entities concept)
export type EntityListFilter = {
	field: string;
	operator: TriggerCondition['operator'];
	value: any;
};

export type EntityList = {
	id: string;
	name: string;
	description?: string;
	providerId: string;
	entityType: EntityType;
	filters: EntityListFilter[];
	instructions: string;
	autoCreateTasks: boolean;
	isActive: boolean;
	maxPerHour?: number;
	debounceSeconds?: number;
	createdAt: string;
	updatedAt: string;
};

export type CreateEntityListInput = {
	name: string;
	description?: string;
	projectId: string;
	providerId: string;
	entityType: EntityType;
	filters?: EntityListFilter[];
	instructions: string;
	autoCreateTasks?: boolean;
	isActive?: boolean;
	maxPerHour?: number;
	debounceSeconds?: number;
};

export type UpdateEntityListInput = Partial<CreateEntityListInput>;
