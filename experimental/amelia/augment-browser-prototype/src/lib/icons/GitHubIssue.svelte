<script lang="ts" module>
	// Heroicons-compatible export
	export const GitHubIssue = {
		"default": {
			"a": {
				"fill": "none",
				"viewBox": "0 0 24 24",
				"stroke-width": "1.5",
				"stroke": "currentColor",
				"aria-hidden": "true",
				"stroke-linecap": "round",
				"stroke-linejoin": "round",
			},
			"path": [{
				"d": "M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1ZM2.5 12a9.5 9.5 0 0 0 9.5 9.5 9.5 9.5 0 0 0 9.5-9.5A9.5 9.5 0 0 0 12 2.5 9.5 9.5 0 0 0 2.5 12Zm9.5 2a2 2 0 1 1-.001-3.999A2 2 0 0 1 12 14Z"
			}]
		},
		"micro": {
			"a": {
				"viewBox": "0 0 16 16",
				"fill": "currentColor",
				"aria-hidden": "true"
			},
			"path": [{
				"d": "M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"
			},{
				"d": "M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Z"
			}]
		},
		"mini": {
			"a": {
				"viewBox": "0 0 16 16",
				"fill": "currentColor",
				"aria-hidden": "true"
			},
			"path": [{
				"d": "M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"
			},{
				"d": "M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Z"
			}]
		},
		"solid": {
			"a": {
				"viewBox": "0 0 24 24",
				"fill": "currentColor",
				"aria-hidden": "true"
			},
			"path": [{
				"fill-rule": "evenodd",
				"d": "M12 1c6.075 0 11 4.925 11 11s-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1ZM2.5 12a9.5 9.5 0 0 0 9.5 9.5 9.5 9.5 0 0 0 9.5-9.5A9.5 9.5 0 0 0 12 2.5 9.5 9.5 0 0 0 2.5 12Zm9.5 2a2 2 0 1 1-.001-3.999A2 2 0 0 1 12 14Z",
				"clip-rule": "evenodd"
			}]
		}
	};
</script>

<script lang="ts">
	interface Props {
		size?: number | string;
		class?: string;
	}

	let { size = 20, class: className = '' }: Props = $props();
</script>

<svg
	width={size}
	height={size}
	viewBox="0 0 16 16"
	class={className}
	fill="currentColor"
>
<path d="M8 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"></path><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0ZM1.5 8a6.5 6.5 0 1 0 13 0 6.5 6.5 0 0 0-13 0Z"></path>
</svg>
