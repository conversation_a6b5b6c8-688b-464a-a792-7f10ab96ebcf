<script lang="ts">
	interface Props {
		size?: number | string;
		class?: string;
	}

	let { size = 20, class: className = '' }: Props = $props();
</script>

<svg
	width={size}
	class={className}
	fill="currentColor"
	 viewBox="0 0 450 450">
  <path fill="#04ac38" d="M0 0h450v450H0z"/>
  <path fill="#fff" d="M127 297h45v83h-45zM297 86c-24-13-41-15-81-15h-89v187h89c35 0 62-2 85-18 26-17 39-45 39-77 0-35-16-63-43-77Zm-71 132h-54V110h51c47-1 70 16 70 53 0 40-29 55-67 55Z"/>
</svg>
