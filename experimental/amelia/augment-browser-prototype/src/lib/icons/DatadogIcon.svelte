<script lang="ts">
	interface Props {
		size?: number | string;
		class?: string;
	}

	let { size = 20, class: className = '' }: Props = $props();
</script>
<svg
	width={size}
	class={className} viewBox="0 0 229.000000 229.000000">

<g transform="translate(0.000000,229.000000) scale(0.100000,-0.100000)" fill="#642AA8" stroke="none">
<path d="M970 2173 c-492 -58 -897 -106 -899 -108 -5 -6 223 -1845 231 -1857&#10;7 -13 205 9 214 24 3 5 -19 32 -50 62 -63 60 -80 96 -71 146 11 62 53 118 151&#10;202 127 110 167 164 172 237 5 64 -8 121 -39 170 -15 25 -23 31 -29 21 -6 -9&#10;-10 -6 -15 11 -10 31 -35 60 -47 53 -5 -3 -12 3 -15 14 -3 13 -11 18 -19 15&#10;-10 -4 -14 3 -14 23 0 16 -6 50 -14 75 -8 25 -17 75 -21 112 l-7 66 68 -5 c59&#10;-5 76 -2 114 18 25 13 51 34 57 48 17 34 16 113 -2 185 -16 63 -105 288 -111&#10;282 -3 -2 12 -66 31 -141 39 -153 42 -188 20 -244 -20 -48 -84 -92 -136 -92&#10;-58 0 -145 66 -199 150 -42 66 -46 92 -20 141 14 29 18 44 10 49 -6 4 5 18 30&#10;35 22 15 39 33 37 39 -2 6 8 16 22 23 l26 11 -25 1 c-42 2 124 71 171 71 35 0&#10;46 -6 77 -41 39 -43 98 -79 130 -79 30 0 123 41 163 72 44 34 49 34 41 6 -5&#10;-23 -5 -23 16 -4 20 17 22 17 22 2 0 -15 2 -15 30 -1 26 13 29 14 24 0 -5 -12&#10;5 -15 61 -15 88 0 125 19 181 91 74 97 131 93 233 -16 66 -71 121 -172 121&#10;-222 0 -112 -161 30 -205 180 -9 31 -25 67 -36 79 -20 23 -20 23 -14 1 3 -11&#10;10 -55 14 -96 5 -41 16 -89 24 -107 9 -17 13 -38 10 -46 -4 -11 -11 -9 -35 11&#10;-17 14 -49 35 -72 47 -38 19 -33 13 50 -73 107 -110 133 -155 141 -247 6 -60&#10;12 -75 58 -142 29 -41 58 -93 65 -114 15 -48 8 -146 -15 -203 -16 -40 -18 -41&#10;-77 -48 -68 -8 -86 1 -68 35 20 38 1 35 -43 -7 -98 -93 -166 -116 -262 -88&#10;-65 19 -166 68 -235 114 -25 17 -47 26 -48 21 -5 -15 12 -34 94 -99 l72 -59&#10;-36 -6 c-21 -3 -52 -6 -69 -6 -18 0 -33 -4 -33 -8 0 -4 16 -129 35 -277 19&#10;-149 35 -273 35 -277 0 -5 -15 -8 -32 -8 -30 0 -35 5 -56 54 -30 69 -81 129&#10;-139 163 -36 21 -63 27 -123 31 -51 3 -80 1 -84 -7 -5 -7 8 -11 41 -11 126 0&#10;225 -101 269 -274 28 -112 -3 -210 -81 -250 -51 -26 -145 -28 -192 -3 -45 24&#10;-41 7 7 -37 39 -35 43 -36 115 -36 86 0 141 21 173 67 26 39 32 57 46 143 11&#10;70 14 75 38 78 25 3 26 1 38 -84 l12 -86 37 5 c20 3 272 33 561 68 289 34 526&#10;63 527 64 2 1 -17 197 -42 435 -25 239 -45 441 -45 448 0 10 -9 13 -30 10 -16&#10;-3 -30 -4 -30 -2 0 12 -101 1050 -105 1082 -6 41 -7 42 -43 41 -20 -1 -440&#10;-49 -932 -106z m1103 -1452 c15 -189 26 -346 23 -348 -2 -3 -222 -44 -488 -92&#10;l-483 -87 -7 30 c-11 53 -81 666 -77 671 3 2 32 8 66 14 47 7 72 6 109 -5 93&#10;-28 166 -15 232 41 30 25 59 33 290 76 141 26 268 47 282 46 l25 -2 28 -344z"/>
<path d="M1941 865 c-29 -60 -62 -129 -73 -152 l-19 -43 -89 60 c-49 33 -93&#10;60 -97 60 -4 0 -32 -42 -62 -93 -30 -50 -65 -108 -77 -128 l-23 -37 -82 24&#10;c-46 13 -88 24 -94 24 -6 0 -45 -53 -88 -118 -69 -106 -77 -124 -77 -166 0&#10;-40 3 -47 18 -42 9 3 208 40 442 82 234 43 426 78 427 79 3 2 -45 520 -50 545&#10;-2 8 -27 -34 -56 -95z"/>
<path d="M1424 1664 c-24 -10 9 -129 39 -139 7 -2 29 -6 51 -9 l38 -5 -4 51&#10;c-4 42 -11 57 -37 80 -29 26 -59 33 -87 22z"/>
<path d="M1015 1595 c-22 -8 -48 -24 -57 -36 -24 -28 -23 -97 1 -128 l18 -24&#10;69 18 c38 10 75 23 82 28 15 12 16 93 1 102 -6 4 -8 14 -4 23 12 33 -42 41&#10;-110 17z"/>
<path d="M1502 1309 c-122 -20 -147 -67 -66 -119 86 -54 129 -49 164 20 24 47&#10;26 95 4 104 -19 7 -32 6 -102 -5z"/>
</g>
</svg>
