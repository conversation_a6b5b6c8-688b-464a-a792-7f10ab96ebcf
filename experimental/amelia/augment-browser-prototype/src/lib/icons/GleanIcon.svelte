<script lang="ts">
	interface Props {
		size?: number | string;
		class?: string;
	}

	let { size = 20, class: className = '' }: Props = $props();
</script>

<svg
	width={size}
	class={className}
	viewBox="0 0 189 186" fill="none">
<ellipse cx="94.5" cy="93" rx="94.5" ry="93" fill="#343CED"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.349 54.6074L113.939 42.6479L125.576 51.5071L116.057 63.3791C120.971 69.1051 123.929 76.4799 123.929 84.5278C123.929 102.747 108.771 117.516 90.0729 117.516C71.3748 117.516 56.217 102.747 56.217 84.5278C56.217 66.3091 71.3748 51.5399 90.0729 51.5399C95.1737 51.5399 100.011 52.639 104.349 54.6074ZM90.0729 103.348C79.4053 103.348 70.7575 94.9219 70.7575 84.5278C70.7575 74.1337 79.4053 65.7076 90.0729 65.7076C100.741 65.7076 109.388 74.1337 109.388 84.5278C109.388 94.9219 100.741 103.348 90.0729 103.348ZM124.322 112.749C123.484 113.716 122.6 114.64 121.678 115.539C120.755 116.431 119.794 117.286 118.794 118.097C117.801 118.908 116.769 119.674 115.698 120.397C114.634 121.12 113.531 121.805 112.402 122.433C111.28 123.068 110.125 123.646 108.945 124.18C107.771 124.715 106.571 125.199 105.346 125.626C104.127 126.06 102.888 126.437 101.624 126.757C100.379 127.09 99.1085 127.361 97.8249 127.574C96.5542 127.794 95.2642 127.958 93.9613 128.064C92.6777 128.171 91.3812 128.228 90.0718 128.228C88.7624 128.228 87.466 128.171 86.1824 128.064C84.8795 127.958 83.5895 127.794 82.3188 127.574C81.0352 127.361 79.7645 127.09 78.5197 126.757L74.7205 140.571C76.3718 141.005 78.0617 141.369 79.771 141.658C81.4545 141.948 83.1702 142.168 84.9053 142.306C86.6081 142.45 88.3303 142.526 90.0719 142.526C91.8134 142.526 93.5356 142.45 95.2384 142.306C96.9735 142.168 98.6828 141.948 100.373 141.658C102.082 141.369 103.765 141.005 105.423 140.571C107.1 140.144 108.751 139.641 110.364 139.063C111.996 138.491 113.589 137.85 115.15 137.14C116.717 136.429 118.252 135.656 119.742 134.821C121.239 133.978 122.697 133.073 124.109 132.106C125.528 131.144 126.902 130.126 128.224 129.045C129.553 127.964 130.83 126.833 132.049 125.645C133.275 124.457 134.442 123.225 135.558 121.937C136.674 120.655 137.732 119.316 138.725 117.94L126.709 109.738C125.96 110.775 125.161 111.781 124.322 112.749Z" fill="white"/>
</svg>
