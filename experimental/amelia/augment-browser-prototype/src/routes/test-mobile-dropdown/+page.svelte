<script lang="ts">
	import Combobox from '$lib/components/ui/forms/Combobox.svelte';
	import Select from '$lib/components/ui/forms/Select.svelte';
	import MultiSelect from '$lib/components/ui/forms/MultiSelect.svelte';
	import Tooltip from '$lib/components/ui/overlays/Tooltip.svelte';
	import { GitHub } from '$lib/icons/GitHubIcon.svelte';

	// Sample options for testing
	const sampleOptions = [
		{ value: 'option1', label: 'First Option', description: 'This is the first option' },
		{ value: 'option2', label: 'Second Option', description: 'This is the second option' },
		{ value: 'option3', label: 'Third Option', description: 'This is the third option' },
		{ value: 'option4', label: 'Fourth Option', description: 'This is the fourth option' },
		{ value: 'option5', label: 'Fifth Option', description: 'This is the fifth option' },
		{ value: 'option6', label: 'Sixth Option', description: 'This is the sixth option' },
		{ value: 'option7', label: 'Seventh Option', description: 'This is the seventh option' },
		{ value: 'option8', label: 'Eighth Option', description: 'This is the eighth option' },
		{ value: 'option9', label: 'Ninth Option', description: 'This is the ninth option' },
		{ value: 'option10', label: 'Tenth Option', description: 'This is the tenth option' }
	];

	let selectedValue = $state('');
	let selectedValue2 = $state('');
	let selectedValue3 = $state('');
	let selectedSelectValue = $state('');
	let selectedMultiSelectValue = $state([]);

	function handleChange(value: string) {
		selectedValue = value;
		console.log('Selected:', value);
	}

	function handleChange2(value: string) {
		selectedValue2 = value;
		console.log('Selected 2:', value);
	}

	function handleChange3(value: string) {
		selectedValue3 = value;
		console.log('Selected 3:', value);
	}

	function handleSelectChange(value: string) {
		selectedSelectValue = value;
		console.log('Selected Select:', value);
	}

	function handleMultiSelectChange(value: string[]) {
		selectedMultiSelectValue = value;
		console.log('Selected MultiSelect:', value);
	}
</script>

<svelte:head>
	<title>Mobile Dropdown Test</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
	<div class="max-w-md mx-auto space-y-8">
		<div class="text-center">
			<h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
				🚀 Enhanced Mobile Dropdown Test
			</h1>
			<p class="text-gray-600 dark:text-gray-400 text-sm">
				Test the improved mobile dropdown positioning powered by Floating UI.
				All components now automatically handle mobile keyboards, orientation changes, and viewport collisions.
			</p>
		</div>

		<!-- Enhanced Components with Floating UI -->
		<div class="space-y-6 border-2 border-green-200 dark:border-green-800 rounded-lg p-4">
			<h2 class="text-lg font-semibold text-green-900 dark:text-green-100">
				✨ Enhanced Components (Floating UI Powered)
			</h2>

			<!-- Test Case: Combobox with Tooltip -->
			<div class="space-y-2">
				<Tooltip text="This combobox now uses Floating UI for perfect positioning on mobile" placement="top">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						Enhanced Combobox (with tooltip)
					</label>
				</Tooltip>
				<Combobox
					options={sampleOptions}
					value={selectedValue}
					placeholder="Select with enhanced positioning..."
					searchable={true}
					clearable={true}
					icon={GitHub}
					onchange={handleChange}
					class="w-full"
				/>
				{#if selectedValue}
					<p class="text-sm text-gray-600 dark:text-gray-400">
						Selected: {selectedValue}
					</p>
				{/if}
			</div>

			<!-- Test Case: Select Component -->
			<div class="space-y-2">
				<Tooltip text="Enhanced Select component with Floating UI positioning" placement="top">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						Enhanced Select Component
					</label>
				</Tooltip>
				<Select
					options={sampleOptions.map(opt => ({ value: opt.value, label: opt.label }))}
					bind:value={selectedSelectValue}
					placeholder="Choose an option..."
					onchange={handleSelectChange}
					class="w-full"
				/>
				{#if selectedSelectValue}
					<p class="text-sm text-gray-600 dark:text-gray-400">
						Selected: {selectedSelectValue}
					</p>
				{/if}
			</div>

			<!-- Test Case: MultiSelect Component -->
			<div class="space-y-2">
				<Tooltip text="Enhanced MultiSelect component with Floating UI positioning" placement="top">
					<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
						Enhanced MultiSelect Component
					</label>
				</Tooltip>
				<MultiSelect
					options={sampleOptions.map(opt => ({ value: opt.value, label: opt.label }))}
					bind:value={selectedMultiSelectValue}
					placeholder="Choose multiple options..."
					onchange={handleMultiSelectChange}
					class="w-full"
				/>
				{#if selectedMultiSelectValue.length > 0}
					<p class="text-sm text-gray-600 dark:text-gray-400">
						Selected: {selectedMultiSelectValue.join(', ')}
					</p>
				{/if}
			</div>
		</div>

		<!-- Test Cases -->
		<div class="space-y-6">
			<h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
				📱 Test Cases
			</h2>

			<!-- Test Case 1: Top of page -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
					Dropdown at top of page
				</label>
			<Combobox
				options={sampleOptions}
				value={selectedValue}
				placeholder="Select an option..."
				searchable={true}
				clearable={true}
				icon={GitHub}
				onchange={handleChange}
				class="w-full"
			/>
			{#if selectedValue}
				<p class="text-sm text-gray-600 dark:text-gray-400">
					Selected: {selectedValue}
				</p>
			{/if}
		</div>

		<!-- Spacer content to push next dropdown to middle -->
		<div class="space-y-4">
			<div class="h-32 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
				<h3 class="font-medium text-gray-900 dark:text-white mb-2">Sample Content</h3>
				<p class="text-gray-600 dark:text-gray-400 text-sm">
					This is some sample content to create spacing between dropdowns.
					This helps test positioning in different parts of the viewport.
				</p>
			</div>
		</div>

		<!-- Test Case 2: Middle of page -->
		<div class="space-y-2">
			<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
				Dropdown in middle of page
			</label>
			<Combobox
				options={sampleOptions}
				value={selectedValue2}
				placeholder="Select another option..."
				searchable={true}
				clearable={true}
				onchange={handleChange2}
				class="w-full"
			/>
			{#if selectedValue2}
				<p class="text-sm text-gray-600 dark:text-gray-400">
					Selected: {selectedValue2}
				</p>
			{/if}
		</div>

		<!-- More spacer content -->
		<div class="space-y-4">
			<div class="h-32 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
				<h3 class="font-medium text-gray-900 dark:text-white mb-2">More Content</h3>
				<p class="text-gray-600 dark:text-gray-400 text-sm">
					Additional content to test dropdown positioning when the virtual keyboard
					appears on mobile devices.
				</p>
			</div>
			<div class="h-32 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
				<h3 class="font-medium text-gray-900 dark:text-white mb-2">Even More Content</h3>
				<p class="text-gray-600 dark:text-gray-400 text-sm">
					This content pushes the next dropdown towards the bottom of the viewport
					to test positioning when there's limited space below.
				</p>
			</div>
		</div>

		<!-- Test Case 3: Bottom of page -->
		<div class="space-y-2">
			<label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
				Dropdown near bottom of page
			</label>
			<Combobox
				options={sampleOptions}
				value={selectedValue3}
				placeholder="Select a third option..."
				searchable={true}
				clearable={true}
				onchange={handleChange3}
				class="w-full"
			/>
			{#if selectedValue3}
				<p class="text-sm text-gray-600 dark:text-gray-400">
					Selected: {selectedValue3}
				</p>
			{/if}
		</div>
		</div>

		<!-- Instructions -->
		<div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
			<h3 class="font-medium text-green-900 dark:text-green-100 mb-2">Testing Instructions</h3>
			<ul class="text-sm text-green-800 dark:text-green-200 space-y-1">
				<li>• Test on mobile devices or use browser dev tools mobile simulation</li>
				<li>• Try opening each dropdown and typing to trigger the virtual keyboard</li>
				<li>• All components now use <strong>Floating UI</strong> for enhanced positioning</li>
				<li>• Verify dropdowns stay visible and properly positioned</li>
				<li>• Check that dropdowns don't get cut off by the keyboard</li>
				<li>• Test both portrait and landscape orientations</li>
				<li>• Notice automatic repositioning when space is limited</li>
				<li>• Hover over tooltips to see enhanced positioning</li>
				<li>• Try scrolling while dropdowns are open</li>
			</ul>
		</div>

		<!-- Benefits -->
		<div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
			<h3 class="font-medium text-blue-900 dark:text-blue-100 mb-2">✨ Floating UI Benefits</h3>
			<ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
				<li>• <strong>Automatic repositioning</strong> when mobile keyboard opens/closes</li>
				<li>• <strong>Smart collision detection</strong> with automatic flipping and shifting</li>
				<li>• <strong>Performance optimized</strong> with efficient update cycles</li>
				<li>• <strong>Mobile-first design</strong> with visual viewport API support</li>
				<li>• <strong>Unified solution</strong> for all floating elements</li>
				<li>• <strong>Accessibility built-in</strong> with proper ARIA attributes</li>
			</ul>
		</div>

		<!-- Bottom spacer -->
		<div class="h-32"></div>
	</div>
</div>
