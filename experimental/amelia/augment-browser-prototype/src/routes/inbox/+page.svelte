<script lang="ts">
	import { goto } from '$app/navigation';
	import {
		loadAgents,
		loadTriggers,
		createTriggerAndSync
	} from '$lib/stores/data-operations.svelte';
	import {
		agents,
		triggers,
		entities,
		shouldShowTriggerSkeleton,
		getFlatTriggerExecutions,
		getTriggerEntityGroups,
		isLoadingTriggerMatches
	} from '$lib/stores/global-state.svelte';
	import { addToast } from '$lib/stores/toast';
	import { updateTriggerAndSync } from '$lib/stores/data-operations.svelte';
	import { apiClient } from '$lib/api/unified-client';

	import ExecutionRowSkeleton from '$lib/components/ui/data-display/ExecutionRowSkeleton.svelte';
	import MatchRowSkeleton from '$lib/components/ui/data-display/MatchRowSkeleton.svelte';
	import ExpandableSection from '$lib/components/ui/widgets/ExpandableSection.svelte';
	import { Bolt, Cog6Tooth, Icon, Plus, Trash, XCircle, XMark } from 'svelte-hero-icons';

	import FilterPills from '$lib/components/ui/navigation/FilterPills.svelte';
	import TriggerCreateDrawer from '$lib/components/ui/overlays/TriggerCreateDrawer.svelte';
	import UnifiedFeed from '$lib/components/ui/UnifiedFeed.svelte';
	import ExecutionsSection from '$lib/components/ui/widgets/ExecutionsSection.svelte';
	import FeatureSections from '$lib/components/ui/widgets/FeatureSections.svelte';
	import MatchesSection from '$lib/components/ui/widgets/MatchesSection.svelte';

	import Button from '$lib/components/ui/navigation/Button.svelte';
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';

	import type { CleanRemoteAgent } from '$lib/api/unified-client';
	import RemoteAgentDetailDrawer from '$lib/components/ui/overlays/RemoteAgentDetailDrawer.svelte';

	import ExectionsChart from '$lib/components/ExectionsChart.svelte';
	import Markdown from '$lib/components/ui/content/Markdown.svelte';
	import TriggerAutoToggle from '$lib/components/ui/TriggerAutoToggle.svelte';
	import AuggieAvatar from '$lib/components/ui/visualization/AuggieAvatar.svelte';
	import { configuredProviders, providerAuthStates } from '$lib/stores/provider-auth';
	import { slide } from 'svelte/transition';
	import UnconfiguredProviderNotification from '$lib/components/ui/notifications/UnconfiguredProviderNotification.svelte';
	import { detectUnconfiguredProvidersWithTriggers } from '$lib/utils/provider-trigger-detection';
	// Initialize unified data store when page mounts
	import { onMount, setContext } from 'svelte';
	import { storedState } from '$lib/utils/stored-state.svelte';

	onMount(async () => {
		// Note: initializeSession() is now called globally in +layout.svelte
		// This ensures agents, triggers, and entities are loaded globally
		// loadTriggers() automatically loads executions and matching entities
		// via debouncedComputeTriggerMatches(), so no manual loading needed
		console.log('Triggers, executions, and matching entities loaded via global initializeSession');
	});

	let showAgentDrawer = $state(false);
	let selectedAgent = $state<CleanRemoteAgent | null>(null);
	let configTrigger = $state<NormalizedTrigger | null>(null);
	let isConfigDrawerOpen = $state(false);
	let isCreateDrawerOpen = $state(false);
	let isHeaderDismissed = $state(storedState('localStorage', 'isHeaderDismissed', false));
	let isCreatingDefaultTriggers = $state(false);
	let expandedInstructions = $state(new Set<string>());

	// Hover state for cross-component highlighting
	let hoveredEntityId = $state<string | null>(null);
	let hoveredExecutionId = $state<string | null>(null);

	// Set up hover context for child components
	setContext('hover-context', {
		get hoveredEntityId() {
			return hoveredEntityId;
		},
		get hoveredExecutionId() {
			return hoveredExecutionId;
		},
		setHoveredEntity: (entityId: string | null) => {
			hoveredEntityId = entityId;
		},
		setHoveredExecution: (id: string | null) => {
			hoveredExecutionId = id;
		}
	});

	// Derived states
	let allTriggers = $derived($triggers);
	let allEntities = $derived($entities);

	const triggersAndEntities = $derived(getTriggerEntityGroups());

	let executions = $derived(getFlatTriggerExecutions());
	let triggerData = $derived.by(() => {
		return $triggersAndEntities.map(({ trigger, entities }) => {
			// Get entities that match this trigger
			return {
				trigger,
				entities,
				executions: $executions.filter((execution) => execution.triggerId === trigger.id),
				totalMatches: entities.length,
				isLoading: $isLoadingTriggerMatches,
				error: undefined
			};
		});
	});

	let filteredExecutions = $derived.by(() => {
		// Get all executions from all triggers
		return $executions;
	});

	// Detect unconfigured providers with triggers
	let unconfiguredProviders = $derived.by(() => {
		// Convert triggers to the format expected by the detection function
		const simpleTriggers = allTriggers.map((trigger) => ({
			id: trigger.id,
			provider: trigger.provider,
			name: trigger.name
		}));
		return detectUnconfiguredProvidersWithTriggers(simpleTriggers, $providerAuthStates);
	});

	let isAnyTriggerLoading = $derived($shouldShowTriggerSkeleton);

	let hasAnyActivity = $derived.by(() => {
		return allTriggers.some((t) => t.executions && t.executions.length > 0);
	});

	let queueStats = $derived.by(() => {
		// Group entities by provider
		const statsByProvider = new Map<string, { count: number; name: string }>();

		for (const entity of allEntities) {
			const existing = statsByProvider.get(entity.providerId) || {
				count: 0,
				name: entity.providerId
			};
			existing.count++;
			statsByProvider.set(entity.providerId, existing);
		}

		// Convert to array format expected by FeatureSections
		return Array.from(statsByProvider.entries()).map(([providerId, stats]) => ({
			count: stats.count,
			providerId,
			name: stats.name
		}));
	});

	function handleEntityClick(entity: any, trigger?: any) {
		// Navigate to the create route with preselected entity and trigger
		const params = new URLSearchParams();
		if (entity?.id) params.set('entity', entity.id);
		if (trigger?.id) params.set('trigger', trigger.id);

		const queryString = params.toString();
		const url = queryString ? `/inbox/create?${queryString}` : '/inbox/create';
		goto(url);
	}

	function handleAgentClick(agent: any) {
		// Desktop: Open drawer, Mobile: Navigate to full view
		if (window.innerWidth >= 1024) {
			// lg breakpoint
			selectedAgent = agent;
			showAgentDrawer = true;
		} else {
			goto(`/agents/${agent.id}`);
		}
	}

	function handleExecutionClick(execution: any) {
		// Find the agent associated with this execution
		if (execution.remoteAgentId) {
			const agent = $agents.find((a) => a.id === execution.remoteAgentId);
			if (agent) {
				// Desktop: Open drawer, Mobile: Navigate to full view
				if (window.innerWidth >= 1024) {
					// lg breakpoint
					selectedAgent = agent;
					showAgentDrawer = true;
				} else {
					goto(`/agents/${agent.id}`);
				}
			}
		}
	}

	function handleConfigureTrigger(trigger: NormalizedTrigger) {
		configTrigger = trigger;
		isConfigDrawerOpen = true;
	}

	function dismissHeader() {
		isHeaderDismissed.set(true);
	}

	async function createDefaultTriggers() {
		// Prevent double execution
		if (isCreatingDefaultTriggers) {
			return;
		}

		isCreatingDefaultTriggers = true;
		try {
			// Get existing triggers first to check for duplicates
			await loadTriggers(true);
			const existingTriggerNames = new Set($triggers.map((t) => t.name));

			// Create default triggers for common use cases
			const defaultTriggers = [
				{
					name: 'High Priority Issues',
					description: 'Monitor high priority issues across all providers',
					entityType: 'issue',
					isEnabled: true,
					conditions: {
						priority: ['high', 'urgent', 'critical']
					}
				},
				{
					name: 'My Pull Requests',
					description: 'Track pull requests assigned to me',
					entityType: 'pull_request',
					isEnabled: true,
					conditions: {
						assignee: 'me'
					}
				},
				{
					name: 'Recent Comments',
					description: 'Monitor recent comments on issues and PRs',
					entityType: 'comment',
					isEnabled: true,
					conditions: {
						age: '7d'
					}
				}
			];

			// Filter out triggers that already exist
			const newTriggers = defaultTriggers.filter(
				(trigger) => !existingTriggerNames.has(trigger.name)
			);

			if (newTriggers.length === 0) {
				addToast({
					type: 'info',
					message: 'Default triggers already exist'
				});
				return;
			}

			// Create only new triggers
			for (const triggerConfig of newTriggers) {
				await createTriggerAndSync(triggerConfig);
			}

			// Refresh triggers list
			await loadTriggers(true);

			addToast({
				type: 'success',
				message: `Created ${newTriggers.length} new default trigger${newTriggers.length === 1 ? '' : 's'}`
			});
		} catch (error) {
			console.error('Failed to create default triggers:', error);
			addToast({
				type: 'error',
				message: 'Failed to create default triggers'
			});
		} finally {
			isCreatingDefaultTriggers = false;
		}
	}

	async function handleDeleteTrigger(trigger: NormalizedTrigger) {
		if (confirm('Are you sure you want to delete this trigger?')) {
			try {
				// Delete the trigger via API
				await apiClient.triggers.delete(trigger.id);

				// Refresh triggers to reflect the deletion
				await loadTriggers(true);
				handleConfigDrawerClose();

				// Show success feedback (you could add a toast notification here)
				console.log('Trigger deleted successfully');
			} catch (error) {
				console.error('Failed to delete trigger:', error);
				// Show error feedback (you could add a toast notification here)
				alert('Failed to delete trigger. Please try again.');
			}
		}
	}

	function handleConfigDrawerClose() {
		isConfigDrawerOpen = false;
		configTrigger = null;
	}

	async function handleConfigSuccess(result?: any) {
		isConfigDrawerOpen = false;
		configTrigger = null;
		// The store is already updated by updateTriggerAndSync, so no need to call loadTriggers again
		// Just scroll to the updated trigger if we have the result
		if (result?.trigger_id || result?.id) {
			const triggerId = result.trigger_id || result.id;
			scrollToTrigger(triggerId);
		}
	}

	async function handleCreateSuccess(result?: any) {
		isCreateDrawerOpen = false;
		// The store is already updated by createTriggerAndSync, so no need to call loadTriggers again
		// Just scroll to the newly created trigger if we have the result
		if (result?.id) {
			scrollToTrigger(result.id);
		}
	}

	function showHeader() {
		isHeaderDismissed.set(false);
	}

	function toggleInstructionsExpansion(triggerId: string) {
		expandedInstructions = new Set(expandedInstructions);
		if (expandedInstructions.has(triggerId)) {
			expandedInstructions.delete(triggerId);
		} else {
			expandedInstructions.add(triggerId);
		}
	}

	function getDefaultInstructions(provider: string, entityType: string): string {
		return `Monitor ${entityType}s from ${provider}`;
	}

	function getEntityTypeFromConfig(config: any): string {
		return config?.entityType || 'entity';
	}

	function scrollToTrigger(triggerId: string) {
		// Use a small delay to ensure the DOM has updated after fetchTriggers()
		setTimeout(() => {
			const triggerElement = document.getElementById(`trigger-${triggerId}`);
			if (triggerElement) {
				triggerElement.scrollIntoView({
					behavior: 'smooth',
					block: 'center'
				});

				// Add a subtle highlight effect
				triggerElement.style.transition = 'box-shadow 0.3s ease';
				triggerElement.style.boxShadow = '0 0 0 2px rgb(59 130 246 / 0.5)';

				// Remove the highlight after 2 seconds
				setTimeout(() => {
					triggerElement.style.boxShadow = '';
				}, 2000);
			}
		}, 100);
	}

	async function handleToggleEnabled(trigger: NormalizedTrigger) {
		await updateTriggerAndSync(trigger.id, {
			...trigger,
			isEnabled: !trigger.isEnabled
		});
		// Refresh triggers to get updated data
		await loadTriggers(true);
	}
</script>

<svelte:head>
	<title>Overview - Augment Code</title>
</svelte:head>

<div class="flex flex-col">
	{#if !isHeaderDismissed.get()}
		<div
			class="relative mt-1 flex w-full flex-col items-center p-3 pb-6 lg:p-20"
			transition:slide={{ axis: 'y' }}
		>
			<!-- Dismiss Button -->
			<button
				onclick={dismissHeader}
				class="absolute top-6 right-6 p-2 text-gray-400 transition-colors hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
				aria-label="Dismiss header"
			>
				<Icon src={XMark} class="h-5 w-5" />
			</button>

			<div class="mb-3.5 flex items-center font-light text-slate-500 dark:text-slate-400">
				<Icon src={Bolt} class="mr-1.5 w-4" />
				Augment Actions
			</div>
			<h1 class="text-center text-3xl font-medium">Your Work, Queued-Up & Agent-Ready</h1>

			<p
				class="mt-5 max-w-[44em] text-center text-lg leading-relaxed font-light text-slate-600 dark:text-slate-400"
			>
				Actions is your personalized work feed—<br />live filters that pull in tasks from GitHub,
				Linear, and other tools based on criteria you define.
			</p>

			<!-- Three-column feature sections -->
			<FeatureSections {queueStats} />

			<p
				class="mt-4 max-w-[40em] text-center text-lg leading-relaxed font-light text-slate-600 dark:text-slate-400"
			>
				We’ve preloaded a few starter actions, but you can always customize or create your own.
				Scroll down to explore, delegate, and start clearing your backlog.
			</p>

			<div class="mt-8">
				<Button variant="primary" onclick={dismissHeader} size="md">Got it!</Button>
			</div>
		</div>
	{:else}
		<!-- "What's this?" button when header is dismissed -->
		<div class="flex w-full justify-center pt-4" transition:slide={{ axis: 'y' }}>
			<Button
				variant="ghost"
				onclick={showHeader}
				class="group flex items-center gap-2 rounded-lg px-4 py-2 text-sm text-slate-500 transition-all duration-200 hover:bg-slate-50 hover:text-slate-700 dark:text-slate-400 dark:hover:bg-slate-800 dark:hover:text-slate-300"
				icon={Bolt}
			>
				<span class="font-medium">What are Actions?</span>
			</Button>
		</div>
	{/if}

	<!-- Unconfigured Provider Notifications -->
	{#if unconfiguredProviders.length > 0}
		<div class="mx-auto max-w-4xl px-3 pt-8 lg:px-6">
			<UnconfiguredProviderNotification
				{unconfiguredProviders}
				onAuthSuccess={(providerId) => {
					// Refresh provider auth states after successful connection
					console.log(`Provider ${providerId} connected successfully`);
				}}
			/>
		</div>
	{/if}

	<div class="mx-auto pt-16">
		<div class="mb-2 flex items-center justify-center gap-3 text-center">
			<!-- <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
				<Icon src={Bolt} class="w-4 h-4 text-white" />
			</div> -->
			<h2 class="text-2xl font-semibold text-slate-900 dark:text-white">Recent Activity</h2>
		</div>
		<p class="text-center text-slate-600 dark:text-slate-400">
			Your Actions have recently kicked off {filteredExecutions?.length} Agents.
		</p>
	</div>

	<div class="mb-3">
		{#if isAnyTriggerLoading}
			<div class="mt-0.5 px-3 py-8 lg:px-7">
				<div class="h-40 animate-pulse rounded-lg bg-gray-100 dark:bg-gray-700"></div>
			</div>
		{:else}
			<div class="px-3 py-8 lg:px-7">
				<div class="mb-8 flex items-start justify-between">
					<div></div>
				</div>

				<ExectionsChart executions={filteredExecutions} onExecutionClick={handleExecutionClick} />
			</div>
		{/if}
	</div>

	{#if isAnyTriggerLoading || hasAnyActivity}
		<div class="flex-1 p-3 pt-0 lg:p-6">
			<UnifiedFeed
				{triggerData}
				onEntityClick={handleEntityClick}
				onAgentClick={handleAgentClick}
			/>
		</div>
	{/if}

	<!-- Dashboard Content -->
	<div class="flex-1 p-3 lg:p-6">
		<!-- Recent Activity with Filters -->
		<!-- <RecentActivity
			{projectId}
			triggers={$triggers}
			{triggerData}
			filteredExecutions={$filteredExecutions}
			{isLoadingEntities}
			{selectedTriggerId}
			{selectedTrigger}
			{showMoreUnified}
			{filteredEntities}
			{filteredErrors}
			onTriggerSelect={(triggerId) => selectedTriggerId = triggerId}
			onShowMoreUnified={(show) => showMoreUnified = show}
			onEntityClick={(entity, trigger) => {
				selectedTrigger = trigger;
				showEntityDrawer = true;
			}}
			onAgentClick={(agent) => {
				goto(`/agents/${agent.id}`);
			}}
		/> -->

		<!-- Triggers List -->
		<!-- <TriggersList
			triggers={$triggers}
			{triggerData}
			{showMoreMatches}
			{showMoreExecutions}
			onShowMoreMatches={(triggerId, show) => showMoreMatches[triggerId] = show}
			onShowMoreExecutions={(triggerId, show) => showMoreExecutions[triggerId] = show}
			onEntityClick={handleEntityClick}
		/> -->

		<!-- New Grid-based Triggers Layout -->
		<div class="w-full pt-0 lg:p-6">
			{#if isAnyTriggerLoading && triggerData.length === 0}
				<!-- Loading State for Initial Load -->
				<div class="space-y-6">
					{#each Array(3) as _}
						<div class="py-6 transition-shadow duration-200">
							<!-- Full-width row layout -->
							<div class="grid grid-cols-1 gap-6 p-3 lg:grid-cols-[20em_1fr_1fr] lg:p-6">
								<!-- Left Column: Description Skeleton -->
								<div class="flex animate-pulse flex-col lg:col-span-1">
									<!-- Header with Icon and Title -->
									<div class="mb-6 flex items-start gap-4">
										<div class="h-6 w-6 rounded bg-slate-200 dark:bg-slate-700"></div>
										<div class="min-w-0 flex-1">
											<div class="mb-2 h-6 w-3/4 rounded bg-slate-200 dark:bg-slate-700"></div>
											<div class="h-4 w-1/2 rounded bg-slate-200 dark:bg-slate-700"></div>
										</div>
									</div>
									<!-- Description -->
									<div class="mb-6 space-y-2">
										<div class="h-4 w-full rounded bg-slate-200 dark:bg-slate-700"></div>
										<div class="h-4 w-4/5 rounded bg-slate-200 dark:bg-slate-700"></div>
										<div class="h-4 w-3/5 rounded bg-slate-200 dark:bg-slate-700"></div>
									</div>
									<!-- Button -->
									<div class="h-8 w-24 rounded bg-slate-200 dark:bg-slate-700"></div>
								</div>

								<!-- Middle Column: Matches Skeleton -->
								<div class="lg:col-span-1">
									<ExpandableSection
										title="Latest Matches"
										count={0}
										items={[]}
										emptyMessage="Loading matches..."
										isLoading={true}
										skeletonComponent={MatchRowSkeleton}
										skeletonCount={3}
									>
										{#snippet children()}
											<div></div>
										{/snippet}
									</ExpandableSection>
								</div>

								<!-- Right Column: Executions Skeleton -->
								<div class="lg:col-span-1">
									<ExpandableSection
										title="Agents on the job"
										count={0}
										items={[]}
										emptyMessage="Loading executions..."
										isLoading={true}
										skeletonComponent={ExecutionRowSkeleton}
										skeletonCount={2}
									>
										{#snippet children()}
											<div></div>
										{/snippet}
									</ExpandableSection>
								</div>
							</div>
						</div>
					{/each}
				</div>
			{:else if !isAnyTriggerLoading && !hasAnyActivity && $triggers.length === 0}
				<!-- Enhanced Empty State -->
				<div class="py-16 text-center">
					<div class="mx-auto max-w-lg">
						<p class="mb-8 leading-relaxed text-slate-600 dark:text-slate-400">
							Your inbox will show activity from your connected integrations once you create some
							triggers. Triggers help you stay on top of important events like new pull requests,
							issues, and more.
						</p>

						{#if $configuredProviders.length > 0}
							<!-- Show connected integrations and create default triggers -->
							<div class="mb-8">
								<Button
									variant="primary"
									size="lg"
									onclick={createDefaultTriggers}
									disabled={isCreatingDefaultTriggers}
									class="mb-4"
									icon={isCreatingDefaultTriggers ? Cog6Tooth : Plus}
								>
									{#if isCreatingDefaultTriggers}
										Creating Actions...
									{:else}
										Create default Actions
									{/if}
								</Button>

								<p class="mb-6 text-sm text-slate-500 dark:text-slate-400">
									This will create common Actions for your connected integrations like monitoring
									pull requests, issues, and workflow runs.
								</p>
							</div>
						{:else}
							<!-- No integrations connected -->
							<div class="mb-8">
								<p class="mb-6 text-slate-500 dark:text-slate-400">
									First, connect your integrations to get started.
								</p>

								<div class="mb-6 flex items-center justify-center gap-4">
									<Button variant="outline" href="/settings/integrations" icon={Plus}>
										Connect
									</Button>
								</div>
							</div>
						{/if}
					</div>
				</div>
			{:else}
				<!-- Full-width Triggers Layout -->
				<div class="">
					{#each triggerData as { trigger, entities, executions: triggerExecutions, totalMatches, isLoading: triggerLoading, error }}
						<div id="trigger-{trigger.id}" class="py-6 transition-shadow duration-200">
							<!-- Trigger Info Header -->
							<div class="mb-6 p-3 pb-2 lg:p-6">
								<div class="ml-4">
									<AuggieAvatar colorSeed={trigger.id} faceSeed={'blank'} size={36} />
								</div>
								<div class="grid grid-cols-1 gap-6 px-4 lg:grid-cols-2">
									<!-- Left Column: Title and Actions -->
									<div class="min-w-0 flex-1">
										<h3 class="mt-2 mb-3 text-xl font-semibold text-slate-900 dark:text-white">
											{trigger.name || 'Unnamed Filter'}
										</h3>

										<div class="mb-4">
											<FilterPills
												triggerData={[
													{
														trigger,
														entities,
														executions: triggerExecutions,
														totalMatches,
														isLoading: triggerLoading,
														error
													}
												]}
											/>
										</div>

										<!-- Actions Row -->
										<div
											class="flex items-center gap-3 border-t border-slate-100 pt-2 dark:border-slate-800"
										>
											<TriggerAutoToggle
												enabled={trigger.isEnabled}
												onToggle={() => handleToggleEnabled(trigger)}
												variant="compact"
												showLabel
											/>
											<!-- <div class="h-4 w-px bg-slate-300 dark:bg-slate-600"></div> -->
											<Button
												variant="ghost-light"
												size="icon-sm"
												icon={Cog6Tooth}
												onclick={() => handleConfigureTrigger(trigger)}
											>
												<!-- Configure -->
											</Button>
											<Button
												variant="ghost-light"
												size="icon-sm"
												icon={Trash}
												onclick={() => handleDeleteTrigger(trigger)}
											>
												<!-- Delete -->
											</Button>
										</div>
									</div>

									<!-- Right Column: Instructions -->
									<div class="mt-2 lg:pl-4">
										<h4 class="mb-2 text-sm font-medium text-slate-700 dark:text-slate-300">
											Agent Instructions
										</h4>
										<div class="relative">
											<Markdown
												color="gray"
												content={trigger.configuration?.agent_config?.user_guidelines ||
													getDefaultInstructions(
														trigger.provider,
														trigger.entityType || getEntityTypeFromConfig(trigger.configuration)
													)}
												size="sm"
												lineClamp={expandedInstructions.has(trigger.id) ? undefined : 3}
											/>
											{#if !expandedInstructions.has(trigger.id)}
												<button
													class="mt-2 cursor-pointer text-xs text-slate-500 transition-colors hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
													onclick={() => toggleInstructionsExpansion(trigger.id)}
												>
													Show more
												</button>
											{:else}
												<button
													class="mt-2 cursor-pointer text-xs text-slate-500 transition-colors hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
													onclick={() => toggleInstructionsExpansion(trigger.id)}
												>
													Show less
												</button>
											{/if}
										</div>
									</div>
								</div>
							</div>

							<!-- Content Grid -->
							<div class="grid grid-cols-1 gap-6 p-3 pt-0 lg:grid-cols-2 lg:p-6">
								<!-- Middle Column: Matches -->
								<div class="min-w-0 lg:col-span-1">
									{#if error}
										<div class="py-4 text-center">
											<Icon
												src={XCircle}
												class="mx-auto mb-2 h-6 w-6 text-red-300 dark:text-red-600"
											/>
											<p class="text-xs text-red-500 dark:text-red-400">{error}</p>
										</div>
									{:else}
										<MatchesSection
											triggerData={[
												{
													trigger,
													entities,
													executions: triggerExecutions,
													totalMatches,
													isLoading: triggerLoading,
													error
												}
											]}
											allExecutions={filteredExecutions}
											isLoading={triggerLoading}
											showDismissButton={true}
											onEntityClick={handleEntityClick}
											onAgentClick={handleAgentClick}
										/>
									{/if}
								</div>

								<!-- Right Column: Executions -->
								<div class="min-w-0 lg:col-span-1">
									<ExecutionsSection
										triggerData={[
											{
												trigger,
												entities,
												executions: triggerExecutions,
												totalMatches,
												isLoading: triggerLoading,
												error
											}
										]}
										isLoading={triggerLoading}
										onAgentClick={handleAgentClick}
									/>
								</div>
							</div>
						</div>
					{/each}
				</div>
			{/if}

			<!-- Create Action Button at Bottom -->
			<div class="mt-12 mb-8 flex justify-center">
				<Button variant="primary" icon={Plus} onclick={() => (isCreateDrawerOpen = true)} size="md">
					Create new Action
				</Button>
			</div>
		</div>
	</div>
</div>

<!-- Config Drawer -->
<TriggerCreateDrawer
	open={isConfigDrawerOpen}
	existingTrigger={configTrigger}
	editMode={true}
	onClose={handleConfigDrawerClose}
	onSuccess={(result) => handleConfigSuccess(result)}
/>

<!-- Create Action Drawer -->
<TriggerCreateDrawer
	open={isCreateDrawerOpen}
	onClose={() => (isCreateDrawerOpen = false)}
	onSuccess={(result) => handleCreateSuccess(result)}
/>

<!-- Agent Detail Drawer (Desktop only) -->
{#if selectedAgent}
	<RemoteAgentDetailDrawer
		open={showAgentDrawer}
		agent={selectedAgent}
		onClose={() => {
			showAgentDrawer = false;
			selectedAgent = null;
		}}
		onAgentDeleted={async () => {
			showAgentDrawer = false;
			selectedAgent = null;
			// Refresh the remote agents store and reload trigger data to update the UI
			await Promise.all([loadAgents(true), loadTriggers(true)]);
		}}
	/>
{/if}
