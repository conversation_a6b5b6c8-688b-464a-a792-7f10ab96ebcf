<script module lang="ts">
	export type LayoutState = {
		doShowCreateDrawer: boolean;
	};
</script>

<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { session } from '$lib/stores/auth';
	import { onMount } from 'svelte';

	import { Bolt, Icon, MagnifyingGlass } from 'svelte-hero-icons';
	// Trigger utilities
	import type { NormalizedTrigger } from '$lib/utils/trigger-normalization';

	// UI Components
	import DashboardEntityTable from '$lib/components/ui/data-display/DashboardEntityTable.svelte';
	import DashboardEntityTableSkeleton from '$lib/components/ui/data-display/DashboardEntityTableSkeleton.svelte';
	import WorkflowRunCard from '$lib/components/ui/data-display/WorkflowRunCard.svelte';
	import WorkflowRunCardSkeleton from '$lib/components/ui/data-display/WorkflowRunCardSkeleton.svelte';
	import DashboardLayoutChild from '$lib/components/ui/layout/DashboardLayoutChild.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import TriggerCreateDrawer from '$lib/components/ui/overlays/TriggerCreateDrawer.svelte';
	import Masonry from '$lib/components/ui/visualization/Masonry.svelte';
	// Types
	import {
		applyFilters,
		createEntityCache,
		createFilterManager
	} from '$lib/utils/dashboard-filters.svelte';
	import { getEntityTypeFromTrigger, type UnifiedEntity } from '$lib/utils/entity-conversion';

	import { providerEntityTypes } from '$lib/config/entity-types';
	import { getCurrentRepository } from '$lib/utils/project-repository';
	// Dashboard utilities
	import DashboardFilters from '$lib/components/ui/navigation/DashboardFilters.svelte';
	import { createRemoteAgentManually } from '$lib/utils/dashboard-entity-operations';
	import { createEntitySelectOptions } from '$lib/utils/dashboard-ui-helpers';
	// Use triggers from the parent layout (which includes entity counts)
	import Drawer from '$lib/components/ui/overlays/Drawer.svelte';
	import {
		entities,
		entitiesError,
		shouldShowEntitySkeleton,
		shouldShowTriggerSkeleton,
		triggers
	} from '$lib/stores/global-state.svelte';
	// Unified data store
	import { openAgentCreationModal } from '$lib/stores/modal';

	// Initialize data loading
	onMount(async () => {
		// Note: initializeSession() is now called globally in +layout.svelte
		// This ensures entities are loaded globally
		// Note: computeTriggerMatches() will be called automatically when entities are loaded
	});

	// Get entityId from page params
	let entityId = $derived(page.params.entityId || '');

	// Layout state
	let layoutState = $state<LayoutState>({
		doShowCreateDrawer: false
	});

	// Filter management
	const entityCache = createEntityCache();

	const filterManager = createFilterManager(
		() => {
			// Handle filter changes - check cache first, then fetch if needed
			checkAndFetchMoreEntities();
		},
		() => {
			// Handle fetch more request
			// handleFilterChange();
		}
	);

	const availableProviders = ['github', 'linear'];
	let availableEntityTypes = $derived.by(() => {
		if (filterManager.filters.selectedProvider === 'all') {
			return [];
		}
		return providerEntityTypes[filterManager.filters.selectedProvider] || [];
	});

	// UI state
	let viewMode = $state<'table' | 'grid'>('grid');

	// Derived state from global store
	let allEntities = $derived($entities);
	let isLoading = $derived($shouldShowEntitySkeleton || $shouldShowTriggerSkeleton);
	let error = $derived($entitiesError);

	// Computed values for filtering and sorting
	let filteredEntities = $derived(
		applyFilters(allEntities, filterManager.filters).map((entity, index) => {
			return {
				...entity,
				eachId: index
			};
		})
	);

	// Data fetching is now handled by unified data store
	async function checkAndFetchMoreEntities() {
		// No longer needed - unified data store handles all data loading
	}

	// Find current entity
	let currentEntity = $derived(allEntities.find((e) => e.id === entityId) || null);

	// Create options for entity select dropdown
	let entitySelectOptions = $derived(createEntitySelectOptions(allEntities));

	function handleEntityChange(entityId: string) {
		goto(`/explore/${entityId}`);
	}

	function handleEntityClick(entity: UnifiedEntity) {
		goto(`/explore/${entity.id}`);
	}

	function generateMockEntity(
		providerId: string,
		entityType: string,
		index: number
	): UnifiedEntity | null {
		const baseId = `mock-${providerId}-${entityType}-${index}`;
		const now = new Date();
		const createdAt = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000); // Random time in last week

		if (providerId === 'github' && entityType === 'pull_request') {
			return {
				id: baseId,
				providerId: 'github',
				entityType: 'pull_request',
				title: `Fix bug in ${['authentication', 'user interface', 'data processing', 'API integration'][index % 4]} module`,
				description: `This PR addresses critical issues in the ${['auth flow', 'UI components', 'data pipeline', 'REST API'][index % 4]}`,
				state: ['open', 'closed', 'merged'][index % 3],
				createdAt: createdAt.toISOString(),
				updatedAt: new Date(
					createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000
				).toISOString(),
				metadata: {
					number: 100 + index,
					author: {
						login: ['johndoe', 'janedoe', 'alexsmith', 'sarahwilson'][index % 4],
						avatar_url: `https://github.com/${['johndoe', 'janedoe', 'alexsmith', 'sarahwilson'][index % 4]}.png?size=40`
					},
					assignees: [
						{
							login: ['reviewer1', 'reviewer2', 'maintainer'][index % 3],
							avatar_url: `https://github.com/${['reviewer1', 'reviewer2', 'maintainer'][index % 3]}.png?size=40`
						}
					],
					labels: [
						{ name: 'bug', color: 'red' },
						{ name: ['frontend', 'backend', 'api', 'ui'][index % 4], color: 'blue' }
					],
					repository: getCurrentRepository()
				}
			};
		} else if (providerId === 'linear' && entityType === 'issue') {
			return {
				id: baseId,
				providerId: 'linear',
				entityType: 'issue',
				title: `Implement ${['dark mode', 'user settings', 'notification system', 'search functionality'][index % 4]}`,
				description: `Add support for ${['theme switching', 'user preferences', 'real-time notifications', 'advanced search'][index % 4]}`,
				state: ['Todo', 'In Progress', 'Done', 'Backlog'][index % 4],
				createdAt: createdAt.toISOString(),
				updatedAt: new Date(
					createdAt.getTime() + Math.random() * 24 * 60 * 60 * 1000
				).toISOString(),
				metadata: {
					identifier: `AUG-${100 + index}`,
					priority: [1, 2, 3, 4][index % 4],
					assignee: {
						name: ['Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson'][index % 4],
						email: [
							'<EMAIL>',
							'<EMAIL>',
							'<EMAIL>',
							'<EMAIL>'
						][index % 4]
					},
					team: {
						name: ['Frontend', 'Backend', 'Design', 'Product'][index % 4]
					},
					labels: [
						{ name: 'feature', color: 'green' },
						{
							name: ['high-priority', 'enhancement', 'user-request', 'technical'][index % 4],
							color: 'orange'
						}
					]
				}
			};
		}

		return null;
	}

	// Data is now loaded through unified data store

	function handleSaveAsTrigger() {
		// Open trigger creation drawer with current filter configuration
		layoutState.doShowCreateDrawer = true;
	}

	let activeConditionPills = $derived(filterManager.getConditionPills());

	function handleTriggerClick(trigger: NormalizedTrigger) {
		// Apply trigger's filter configuration
		// Extract provider and entity type from trigger configuration
		const [providerId, entityType] = getEntityTypeFromTrigger(trigger);
		filterManager.applyTriggerFilters(
			providerId,
			entityType,
			trigger.configuration?.conditions || {}
		);
	}
</script>

<DashboardLayoutChild
	sectionLabel="Explore Action Items"
	sectionDescription="View and manage Action Items from all connected providers"
	currentItemId={entityId}
	currentItem={currentEntity}
>
	{#snippet headerActions()}
		<div class="m-2 mr-0 flex items-center gap-3">
			<!-- Filter Toggle -->
			<!-- <Button
							variant="outline"
							size="sm"
							onclick={() => showFilters = !showFilters}
						>
							<Icon src={AdjustmentsHorizontal} class="w-4 h-4 mr-2" />
							Filters
						</Button> -->

			<!-- Save as Trigger -->
			{#if filterManager.filters.selectedProvider !== 'all' && filterManager.filters.selectedEntityType !== 'all'}
				<Button variant="primary" size="sm" icon={Bolt} onclick={handleSaveAsTrigger}>
					Save as Trigger
				</Button>
			{/if}
		</div>
	{/snippet}

	{#snippet indexContent()}
		<!-- Dashboard List Content -->
		<div class="flex h-full flex-col overflow-hidden">
			<!-- Header -->
			<!-- Filters Panel -->
			<DashboardFilters
				{filterManager}
				triggers={$triggers}
				bind:viewMode
				{availableProviders}
				{availableEntityTypes}
				{activeConditionPills}
				onTriggerClick={handleTriggerClick}
				class="flex-shrink-0"
			/>

			<!-- Entity List -->
			<div class="flex-1 overflow-auto">
				{#if isLoading}
					{#if viewMode === 'table'}
						<DashboardEntityTableSkeleton />
					{:else}
						<div class="min-w-0">
							<Masonry
								items={Array(6)
									.fill(null)
									.map((_, i) => ({ id: `skeleton-${i}` }))}
								idKey="id"
								minColWidth={entityId ? 800 : 350}
								maxColWidth={entityId ? 1200 : 500}
								gap={16}
								style="padding: 1rem;"
							>
								{#snippet children(item, index)}
									<WorkflowRunCardSkeleton />
								{/snippet}
							</Masonry>
						</div>
					{/if}
				{:else if error}
					<div class="flex flex-1 items-center justify-center p-8">
						<div class="text-center">
							<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">
								Error loading entities
							</h3>
							<p class="mb-4 text-sm text-gray-500 dark:text-gray-400">{error}</p>
							<Button onclick={() => loadEntities(true)}>Try again</Button>
						</div>
					</div>
				{:else if filteredEntities.length === 0}
					<div class="flex flex-1 items-center justify-center p-8">
						<div class="text-center">
							<Icon src={MagnifyingGlass} class="mx-auto mb-4 h-12 w-12 text-gray-400" />
							<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">
								No entities found
							</h3>
							<p class="text-sm text-gray-500 dark:text-gray-400">
								Try adjusting your filters or search terms.
							</p>
						</div>
					</div>
				{:else if viewMode === 'table'}
					<DashboardEntityTable entities={filteredEntities} onEntityClick={handleEntityClick} />
				{:else}
					<!-- Masonry grid layout -->
					<div class="min-w-0">
						<Masonry
							items={filteredEntities}
							idKey="eachId"
							minColWidth={350}
							maxColWidth={500}
							gap={16}
							style="padding: 1rem;"
						>
							{#snippet children(entity, index)}
								<WorkflowRunCard
									{entity}
									isSelected={entity.id === entityId}
									onclick={({ trigger }) => {
										console.log('WorkflowRunCard clicked', entity, trigger);
										const params = new URLSearchParams();
										if (entity?.id) params.set('entity', entity.id);
										if (trigger?.id) params.set('trigger', trigger.id);

										const queryString = params.toString();
										const url = queryString ? `/agents/create?${queryString}` : '/agents/create';
										goto(url);
									}}
								/>
							{/snippet}
						</Masonry>
					</div>
				{/if}
			</div>
		</div>
	{/snippet}
</DashboardLayoutChild>

<Drawer open={!!entityId} position="right" onClose={() => goto(`/explore`)}>
	{@render children?.()}
</Drawer>

<!-- Trigger Creation Drawer -->
<TriggerCreateDrawer
	open={layoutState.doShowCreateDrawer}
	onClose={() => (layoutState.doShowCreateDrawer = false)}
	onSuccess={() => {
		layoutState.doShowCreateDrawer = false;
		// Reload data if needed
	}}
	initialProvider={filterManager.filters.selectedProvider}
	initialEntityType={filterManager.filters.selectedEntityType}
	initialConditions={filterManager.filters.conditionFilters}
/>
