<script lang="ts">
	import AgentScheduler from '$lib/components/ui/forms/AgentScheduler.svelte';

	let enabled = $state(false);
	let cronExpression = $state('0 9 * * *');
	let timezone = $state('UTC');
	let startDate = $state('');
	let endDate = $state('');
	let description = $state('');
	let naturalLanguageInput = $state('');

	function handleToggle(isEnabled: boolean) {
		console.log('Schedule toggled:', isEnabled);
	}
</script>

<div class="p-8 max-w-4xl mx-auto">
	<h1 class="text-2xl font-bold mb-6">Agent Scheduler Test</h1>

	<div class="space-y-6">
		<AgentScheduler
			bind:enabled
			bind:cronExpression
			bind:timezone
			bind:startDate
			bind:endDate
			bind:description
			bind:naturalLanguageInput
			onToggle={handleToggle}
		/>

		<div class="p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
			<h3 class="font-medium mb-2">Current Values:</h3>
			<div class="space-y-1 text-sm">
				<p><strong>Enabled:</strong> {enabled}</p>
				<p><strong>Cron Expression:</strong> {cronExpression}</p>
				<p><strong>Natural Language:</strong> {naturalLanguageInput}</p>
				<p><strong>Description:</strong> {description}</p>
				<p><strong>Timezone:</strong> {timezone}</p>
				{#if startDate}
					<p><strong>Start Date:</strong> {startDate}</p>
				{/if}
				{#if endDate}
					<p><strong>End Date:</strong> {endDate}</p>
				{/if}
			</div>
		</div>

		<div class="text-sm text-gray-600 dark:text-gray-400">
			<p><strong>Test Instructions:</strong></p>
			<ol class="list-decimal list-inside space-y-1 mt-2">
				<li>Toggle the scheduler on</li>
				<li>Try typing a cron expression in the left input (e.g., "0 14 * * 1")</li>
				<li>Try selecting a cron preset from the dropdown</li>
				<li>Try typing natural language in the right input (e.g., "every day at 2pm")</li>
				<li>Try selecting a natural language suggestion from the dropdown</li>
				<li>Verify that changes in one input update the other appropriately</li>
				<li>Check that debouncing works (wait for updates after typing)</li>
			</ol>
		</div>
	</div>
</div>
