import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';

// Handle Chrome DevTools app-specific endpoint
// This prevents 404 errors when Chrome DevTools tries to access this endpoint
export const GET: RequestHandler = async () => {
	// Return an empty JSON object to satisfy Chrome DevTools
	// This is completely optional and doesn't affect app functionality
	return json({});
};
