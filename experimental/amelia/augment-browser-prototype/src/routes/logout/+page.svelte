<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { auth } from '$lib/stores/auth';
	import { addToast } from '$lib/stores/toast';

	onMount(() => {
		// Perform logout
		try {
			auth.logout();

			addToast({
				type: 'success',
				message: 'Successfully logged out',
				duration: 3000
			});

			// Redirect to login page
			goto('/login');
		} catch (error) {
			console.error('Logout error:', error);

			addToast({
				type: 'error',
				message: 'Error during logout',
				duration: 5000
			});

			// Still redirect to login even if there was an error
			goto('/login');
		}
	});
</script>

<svelte:head>
	<title>Logging out - Augment Code</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
	<div class="max-w-md w-full mx-auto">
		<div class="bg-white dark:bg-gray-900 shadow-lg rounded-lg p-8 text-center">
			<!-- Logo/Header -->
			<div class="w-16 h-16 mx-auto mb-4 bg-blue-600 rounded-lg flex items-center justify-center">
				<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
				</svg>
			</div>

			<h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
				Logging out...
			</h1>

			<p class="text-gray-600 dark:text-gray-400">
				Please wait while we sign you out securely.
			</p>

			<!-- Loading spinner -->
			<div class="mt-6">
				<div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		</div>
	</div>
</div>
