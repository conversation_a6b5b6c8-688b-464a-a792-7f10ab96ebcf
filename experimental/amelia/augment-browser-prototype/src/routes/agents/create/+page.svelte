<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { openAgentCreationModal, closeModal, modalState } from '$lib/stores/modal';

	// Extract any query parameters for pre-filling the modal
	let entityId = $derived(page.url.searchParams.get('entity'));
	let triggerId = $derived(page.url.searchParams.get('trigger'));
	let repository = $derived(page.url.searchParams.get('repository'));
	let branch = $derived(page.url.searchParams.get('branch'));

	onMount(() => {
		// Open the modal with any pre-filled data from URL params
		const modalProps: any = {};

		if (entityId) modalProps.preselectedEntityId = entityId;
		if (triggerId) modalProps.preselectedTriggerId = triggerId;
		if (repository) modalProps.preselectedRepository = repository;
		if (branch) modalProps.preselectedBranch = branch;

		openAgentCreationModal(modalProps);

		// Return cleanup function to close modal if component unmounts
		return () => {
			closeModal();
		};
	});

	// Listen for modal state changes and navigate back when modal closes
	$effect(() => {
		if (!$modalState.isOpen && $modalState.type === null) {
			// Modal was closed, navigate back to agents page
			goto('/agents');
		}
	});
</script>

<svelte:head>
	<title>Create Agent - Augment Code</title>
</svelte:head>

<!-- This page intentionally has no visible content -->
<!-- The modal will be rendered by the global ModalManager -->
<!-- When the modal closes, we navigate back to /agents -->

<div class="hidden">
	<!-- Hidden content - the modal is rendered globally -->
	<!-- This ensures the page has some content for SEO/accessibility -->
	<h1>Create New Agent</h1>
	<p>Creating a new agent...</p>
</div>
