<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { RemoteAgentStatus } from '$lib/api/unified-client';
	import AgentChatResponse from '$lib/components/agents/AgentChatResponse.svelte';
	import RemoteAgentDetailContent from '$lib/components/ui/RemoteAgentDetailContent.svelte';
	import ChatHistorySkeleton from '$lib/components/ui/feedback/ChatHistorySkeleton.svelte';
	import RemoteAgentDetailSkeleton from '$lib/components/ui/feedback/RemoteAgentDetailSkeleton.svelte';
	import RemoteAgentHeader from '$lib/components/ui/layout/RemoteAgentHeader.svelte';
	import ResizablePanel from '$lib/components/ui/layout/ResizablePanel.svelte';

	import { groupExchanges, processExchangesWithToolFiltering } from '$lib/components/chat-history';
	import ChatHistoryView from '$lib/components/chat-history/ChatHistoryView.svelte';

	import NestedTabs from '$lib/components/ui/navigation/NestedTabs.svelte';
	import {
		loadAgents,
		loadChatHistory,
		subscribeToAgentStreaming,
		unsubscribeFromAgentStreaming,
		startPollingAgent,
		stopPollingAgent,
		isInitialLoad
	} from '$lib/stores/data-operations.svelte';
	import {
		getAgent,
		getEntityByAgent,
		getChatExchanges,
		getChatSession,
		markAgentAsViewed
	} from '$lib/stores/global-state.svelte';
	import { onDestroy, onMount } from 'svelte';
	import { ArrowLeft, ChatBubbleLeftRight, DocumentText, Icon } from 'svelte-hero-icons';
	import { fly } from 'svelte/transition';

	// Mobile tab state
	let activeTab = $state('overview');

	// Chat panel state
	let isChatPanelOpen = $state(true);

	onMount(async () => {
		// Note: initializeSession() is now called globally in +layout.svelte
		// This ensures agents are loaded and agent list streaming is active
		// Note: computeTriggerMatches() will be called automatically when data is loaded
	});

	// Tab configuration
	const tabs = [
		{ id: 'overview', label: 'Overview', icon: DocumentText },
		{ id: 'chat', label: 'Chat', icon: ChatBubbleLeftRight }
	];

	let id = $derived(page.params.taskId);

	// Find the agent by ID using new global state
	let selectedAgentStore = $derived(getAgent(id));
	let selectedAgent = $derived($selectedAgentStore?.data);

	// Get chat session data for optimistic messages and streaming
	let chatSessionStore = $derived(selectedAgent?.id ? getChatSession(selectedAgent.id) : null);
	let chatSession = $derived($chatSessionStore?.data);

	// Get loading states
	let isLoading = $derived($selectedAgentStore?.loading && !selectedAgent);

	// Get linked entity for this agent
	let linkedEntityStore = $derived(selectedAgent?.id ? getEntityByAgent(selectedAgent.id) : null);
	let linkedEntity = $derived($linkedEntityStore?.data);

	// Note: Entity loading is handled by RemoteAgentDetailContent component
	// which loads both entity with related data and raw entity data

	// Get chat exchanges for this agent
	let chatExchangesStore = $derived(selectedAgent?.id ? getChatExchanges(selectedAgent.id) : null);
	let exchanges = $derived($chatExchangesStore || []);

	// Process exchanges for progress indicator
	let processedExchanges = $derived(processExchangesWithToolFiltering(exchanges));
	let exchangeGroups = $derived(groupExchanges(processedExchanges));

	// Determine what message to show in the chat history panel
	let chatHistoryMessage = $derived(
		(() => {
			// Check if agents are still loading
			if (isLoading) {
				return 'Environment loading';
			}

			// Check if we have a selected agent
			if (!selectedAgent) {
				return 'No agent selected';
			}

			// Check if agent is starting
			if (selectedAgent.status === RemoteAgentStatus.AGENT_STARTING) {
				return 'Environment loading';
			}

			// If agent exists but no exchanges yet
			if (exchanges.length === 0) {
				return 'No history yet';
			}

			// Should not reach here if we have exchanges
			return null;
		})()
	);

	let currentStreamingId = $state<string | null>(null);
	const componentId = 'agent-page';

	onMount(async () => {
		// Load initial data
		await loadAgents();
	});

	// Effect to mark agent as viewed when loaded
	$effect(() => {
		if (selectedAgent && selectedAgent.hasUpdates) {
			markAgentAsViewed(selectedAgent.id);
		}
	});

	// Effect to handle agent ID changes and manage streaming
	$effect(() => {
		// Unsubscribe from previous streaming if any
		if (currentStreamingId && currentStreamingId !== id) {
			console.log('Unsubscribing from streaming for previous agent:', currentStreamingId);
			unsubscribeFromAgentStreaming(currentStreamingId, componentId);
			stopPollingAgent(currentStreamingId);
		}

		// Subscribe to streaming for new agent
		if (id && id !== currentStreamingId) {
			console.log('Subscribing to streaming for new agent:', id);
			currentStreamingId = id;

			// Small delay to avoid race conditions with initial message sends
			setTimeout(() => {
				// Load chat history and start streaming
				loadChatHistory(id).then(() => {
					try {
						subscribeToAgentStreaming(id, componentId);
					} catch (error) {
						console.warn('Streaming failed, falling back to polling:', error);
						startPollingAgent(id);
					}
				});
			}, 100);
		}
	});

	onDestroy(() => {
		// Unsubscribe from streaming when the page is unmounted
		if (currentStreamingId) {
			console.log('Unsubscribing from streaming for agent:', currentStreamingId);
			unsubscribeFromAgentStreaming(currentStreamingId, componentId);
			stopPollingAgent(currentStreamingId);
		}
	});

	// Handle agent deletion by navigating back to agents list
	function handleAgentDeleted() {
		goto(`/agents`);
	}

	// Handle file changes summary button click - update code view
	function handleUpdateCodeView(exchangeIndex: number) {
		// For the full page view, we can trigger a scroll to the overview section
		// or update the RemoteAgentDetailContent if it's visible
		console.log('handleUpdateCodeView called with exchangeIndex:', exchangeIndex);
		// This could be enhanced to communicate with the overview tab if needed
	}

	// Handle progress indicator segment click - scroll to chat history
	function handleSegmentClick(groupId: string) {
		console.log('handleSegmentClick called with groupId:', groupId);

		// First, try to find the specific group element
		const groupElement = document.querySelector(`[data-group-id="${groupId}"]`);
		console.log('Found group element:', groupElement);

		if (groupElement) {
			console.log('Scrolling to specific group');
			groupElement.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});
		} else {
			console.log('Group element not found, trying to scroll to chat history container');
			// Fallback: Find the chat history container using the data attribute
			const chatHistoryElement = document.querySelector('[data-chat-history-container]');
			console.log('Found chat history element:', chatHistoryElement);
			if (chatHistoryElement) {
				console.log('Scrolling to chat history container');
				chatHistoryElement.scrollIntoView({
					behavior: 'smooth',
					block: 'start'
				});
			} else {
				console.warn('Chat history container not found for scroll');
			}
		}
	}
</script>

<svelte:head>
	<title>{`${linkedEntity?.title || selectedAgent?.title || 'Agent Details'} - Augment Code`}</title
	>
</svelte:head>

{#if isLoading}
	<!-- Loading State -->
	<!-- Desktop Layout -->
	<div
		class="relative hidden h-dvh w-full overflow-hidden overscroll-none lg:flex"
		style="--panel-padding-x: 1.5rem;"
	>
		<!-- Main Content Area -->
		<div class="flex min-w-0 flex-1 flex-col overflow-auto bg-slate-50 pb-52 dark:bg-slate-900">
			<a href="/agents" class="flex items-center pt-9 pl-10 text-xs font-medium text-slate-500">
				<Icon src={ArrowLeft} class="mr-1 h-4 w-4" mini />
				All Agents
			</a>
			<!-- Main Content Skeleton -->
			<RemoteAgentDetailSkeleton />
		</div>
		<!-- Chat Panel Skeleton -->
		<ResizablePanel
			bind:isOpen={isChatPanelOpen}
			initialWidth={576}
			minWidth={320}
			maxWidth={800}
			autoCloseBreakpoint={800}
		>
			{#snippet children()}
				<ChatHistorySkeleton />
			{/snippet}
		</ResizablePanel>
	</div>

	<!-- Mobile Layout -->
	<div class="flex h-full flex-col bg-slate-50 pt-13 pb-24 lg:hidden dark:bg-slate-900">
		<!-- Mobile Content Area -->
		<div class="min-h-0 flex-1">
			<div class="h-full overflow-auto">
				<RemoteAgentDetailSkeleton />
			</div>
		</div>
		<!-- Nested Tabs -->
		<NestedTabs {tabs} {activeTab} onTabChange={(tabId) => (activeTab = tabId)} />
	</div>
{:else if selectedAgent}
	<!-- Desktop Layout -->
	<div
		class="relative hidden h-dvh w-full overflow-hidden overscroll-none bg-slate-50 lg:flex dark:bg-slate-900"
		style="--panel-padding-x: 1.5rem;"
	>
		<!-- Main Content Area -->
		<div
			class="flex min-w-0 flex-1 flex-col overflow-auto bg-slate-50 {isChatPanelOpen
				? 'pr-6'
				: 'pr-12'} pb-52 pl-12 dark:bg-slate-900"
		>
			<a href="/agents" class="flex items-center pt-9 pl-10 text-xs font-medium text-slate-500">
				<Icon src={ArrowLeft} class="mr-1 h-4 w-4" mini />
				All Agents
			</a>
			<div class="mt-12 w-full bg-white pt-5 shadow-md dark:bg-slate-800">
				<!-- Main Content -->
				<div class="my-4 -mt-12 px-3 md:px-6 xl:px-10">
					<RemoteAgentHeader agent={selectedAgent} onAgentDeleted={handleAgentDeleted} />
				</div>
				<RemoteAgentDetailContent agent={selectedAgent} onAgentDeleted={handleAgentDeleted} />
			</div>
		</div>

		<!-- Resizable Chat Panel -->
		<ResizablePanel
			bind:isOpen={isChatPanelOpen}
			initialWidth={576}
			minWidth={320}
			maxWidth={800}
			autoCloseBreakpoint={1100}
		>
			{#snippet children()}
				<!-- Entity Context -->
				{#if linkedEntity}
					<!-- Entity context content would go here if needed -->
				{/if}

				<!-- Chat History -->
				<div class="min-h-0 flex-1" data-chat-history-container>
					{#if isLoading && selectedAgent && isInitialLoad(selectedAgent.id)}
						<ChatHistorySkeleton />
					{:else if selectedAgent}
						<div class="h-full w-full" transition:fly={{ y: 20 }}>
							<ChatHistoryView
								{exchanges}
								optimisticMessage={chatSession?.optimisticMessage}
								isStreaming={chatSession?.isStreaming || false}
								streamingContent={chatSession?.streamingContent || ''}
								streamingMessages={chatSession?.streamingMessages || new Map()}
								workspaceStatus={selectedAgent.workspaceStatus}
								agent={selectedAgent}
							/>
						</div>
					{:else if chatHistoryMessage}
						<!-- <div class="p-4 mx-auto text-sm italic text-center text-gray-500">{chatHistoryMessage}</div> -->
					{/if}
				</div>

				<!-- Chat Input -->
				{#if !isLoading && selectedAgent}
					<div class="relative min-h-0 flex-shrink-0">
						<div class="relative m-3">
							<AgentChatResponse agentId={selectedAgent.id} />
						</div>
					</div>
				{/if}
			{/snippet}
		</ResizablePanel>
	</div>

	<!-- Mobile Layout -->
	<div class="flex h-full flex-col bg-slate-50 pt-16 lg:hidden dark:bg-slate-900">
		<!-- Mobile Content Area - Takes most of the screen -->
		<div class="min-h-0 flex-1">
			<a href="/agents" class="mb-2 flex items-center pl-5 text-xs font-medium text-slate-500">
				<Icon src={ArrowLeft} class="mr-1 h-4 w-4" mini />
				All Agents
			</a>
			{#if activeTab === 'overview'}
				<!-- Overview Tab -->
				<div class="h-full overflow-auto pt-3 pb-24">
					{#if isLoading}
						<RemoteAgentDetailSkeleton />
					{:else if selectedAgent}
						<RemoteAgentHeader agent={selectedAgent} onAgentDeleted={handleAgentDeleted} />
						<div class="h-6"></div>
						<RemoteAgentDetailContent agent={selectedAgent} onAgentDeleted={handleAgentDeleted} />
					{/if}
				</div>
			{:else if activeTab === 'chat'}
				<!-- Chat Tab -->
				<div class="flex h-full flex-col">
					{#if isLoading && selectedAgent && isInitialLoad(selectedAgent.id)}
						<ChatHistorySkeleton />
					{:else}
						<!-- Chat History - Scrollable middle section -->
						<div class="flex-1 overflow-auto pb-20" data-chat-history-container>
							{#if selectedAgent}
								<ChatHistoryView
									{exchanges}
									optimisticMessage={chatSession?.optimisticMessage}
									isStreaming={chatSession?.isStreaming || false}
									streamingContent={chatSession?.streamingContent || ''}
									streamingMessages={chatSession?.streamingMessages || new Map()}
									workspaceStatus={selectedAgent.workspaceStatus}
									agent={selectedAgent}
									{exchangeGroups}
									onSegmentClick={handleSegmentClick}
									onUpdateCodeView={handleUpdateCodeView}
								/>
							{:else if chatHistoryMessage}
								<div class="p-4 text-gray-500">{chatHistoryMessage}</div>
							{/if}
						</div>

						<!-- Chat Input - Fixed at bottom of content area -->
						<div class="sticky right-0 bottom-10 left-0 bg-white dark:bg-slate-800">
							<AgentChatResponse agentId={selectedAgent.id} />
						</div>
					{/if}
				</div>
			{/if}
		</div>

		<!-- Nested Tabs -->
		<NestedTabs {tabs} {activeTab} onTabChange={(tabId) => (activeTab = tabId)} />
	</div>
{:else}
	<div class="my-20 flex flex-1 items-center justify-center">
		<div class="text-center">
			<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Agent not found</h3>
			<p class="text-sm text-gray-500 dark:text-gray-400">
				The agent you're looking for doesn't exist or has been deleted.
			</p>
		</div>
	</div>
{/if}
