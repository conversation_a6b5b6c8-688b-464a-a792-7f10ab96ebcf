<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import { session } from '$lib/stores/auth';
	import { onMount } from 'svelte';

	onMount(() => {
		// Only redirect if we're actually on the root page
		if (page.url.pathname === '/') {
			// Redirect based on authentication status
			if ($session) {
				// Check if there's a return URL from before login
				const returnTo = sessionStorage.getItem('auth_return_to');
				if (returnTo) {
					sessionStorage.removeItem('auth_return_to');
					goto(returnTo);
				} else {
					goto('/agents');
				}
			} else {
				goto('/login');
			}
		}
	});
</script>

<div class="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
	<div class="text-center">
		<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-900 dark:border-slate-100 mx-auto mb-4"></div>
		<p class="text-slate-600 dark:text-slate-400">Redirecting to dashboard...</p>
	</div>
</div>
