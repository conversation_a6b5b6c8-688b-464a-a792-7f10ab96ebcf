<script lang="ts">
	import { goto } from '$app/navigation';
	import { auth } from '$lib/stores/auth';
	import { addToast } from '$lib/stores/toast';
	import Button from '$lib/components/ui/navigation/Button.svelte';

	let callbackUrl = '';
	let isProcessing = false;
	let error: string | null = null;

	async function handleSubmit() {
		if (!callbackUrl.trim()) {
			error = 'Please enter the callback URL';
			return;
		}

		isProcessing = true;
		error = null;

		try {
			// Handle the callback with the provided URL
			// Note: Site authentication check removed because if user reached OAuth,
			// they must have already passed through PasswordGuard
			await auth.handleCallback(callbackUrl);

			addToast({
				type: 'success',
				message: 'Successfully authenticated! Welcome to Augment Actions.',
				duration: 5000
			});

			// Redirect to projects
			await goto('/agents');
		} catch (err) {
			console.error('Manual callback error:', err);
			error = err instanceof Error ? err.message : 'Authentication failed';

			addToast({
				type: 'error',
				message: error,
				duration: 10000
			});
		} finally {
			isProcessing = false;
		}
	}

	function handleBack() {
		goto('/login');
	}
</script>

<svelte:head>
	<title>Complete Authentication - Augment Code</title>
</svelte:head>

<div class="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950">
	<div class="mx-auto w-full max-w-2xl px-4">
		<div class="rounded-lg bg-white p-8 shadow-lg dark:bg-gray-900">
			<!-- Header -->
			<div class="mb-8 text-center">
				<div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-blue-600">
					<svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M13 10V3L4 14h7v7l9-11h-7z"
						/>
					</svg>
				</div>
				<h1 class="text-2xl font-bold text-gray-900 dark:text-white">Complete Authentication</h1>
				<p class="mt-2 text-gray-600 dark:text-gray-400">
					Copy the callback URL from your browser and paste it below
				</p>
			</div>

			<!-- Instructions -->
			<div
				class="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20"
			>
				<h3 class="mb-2 text-sm font-medium text-blue-800 dark:text-blue-200">Instructions:</h3>
				<ol class="list-inside list-decimal space-y-1 text-sm text-blue-700 dark:text-blue-300">
					<li>
						After clicking "Sign in with Augment", you'll be redirected to a page that shows an
						error
					</li>
					<li>Copy the entire URL from your browser's address bar</li>
					<li>
						It should look like: <code class="rounded bg-blue-100 px-1 dark:bg-blue-800"
							>http://127.0.0.1/api/augment/auth/result?code=...</code
						>
					</li>
					<li>Paste that URL in the field below and click "Complete Authentication"</li>
				</ol>
			</div>

			<!-- Form -->
			<form on:submit|preventDefault={handleSubmit} class="space-y-6">
				<div>
					<label
						for="callback-url"
						class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
					>
						Callback URL
					</label>
					<textarea
						id="callback-url"
						bind:value={callbackUrl}
						placeholder="http://127.0.0.1/api/augment/auth/result?code=..."
						rows="3"
						class="w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-800 dark:text-white"
						disabled={isProcessing}
					></textarea>
				</div>

				{#if error}
					<div
						class="rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20"
					>
						<div class="flex">
							<div class="flex-shrink-0">
								<svg
									class="h-5 w-5 text-red-400"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
									/>
								</svg>
							</div>
							<div class="ml-3">
								<p class="text-sm text-red-700 dark:text-red-300">{error}</p>
							</div>
						</div>
					</div>
				{/if}

				<div class="flex gap-3">
					<Button
						type="button"
						variant="secondary"
						onclick={handleBack}
						disabled={isProcessing}
						class="flex-1"
					>
						Back to Login
					</Button>
					<Button
						type="submit"
						variant="primary"
						disabled={isProcessing || !callbackUrl.trim()}
						class="flex-1"
					>
						{#if isProcessing}
							<svg
								class="mr-3 -ml-1 h-5 w-5 animate-spin text-white"
								fill="none"
								viewBox="0 0 24 24"
							>
								<circle
									class="opacity-25"
									cx="12"
									cy="12"
									r="10"
									stroke="currentColor"
									stroke-width="4"
								></circle>
								<path
									class="opacity-75"
									fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
								></path>
							</svg>
							Processing...
						{:else}
							Complete Authentication
						{/if}
					</Button>
				</div>
			</form>

			<!-- Help -->
			<div class="mt-8 border-t border-gray-200 pt-6 dark:border-gray-700">
				<h3 class="mb-3 text-sm font-medium text-gray-900 dark:text-white">Need help?</h3>
				<p class="text-sm text-gray-600 dark:text-gray-400">
					If you're having trouble, make sure you've completed the OAuth flow and the URL contains
					the authorization code. The URL should start with <code
						class="rounded bg-gray-100 px-1 dark:bg-gray-800"
						>http://127.0.0.1/api/augment/auth/result</code
					>.
				</p>
			</div>
		</div>
	</div>
</div>
