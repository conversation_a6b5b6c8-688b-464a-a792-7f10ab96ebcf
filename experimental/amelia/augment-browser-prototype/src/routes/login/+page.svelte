<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { auth, session, isLoading } from '$lib/stores/auth';
	import { addToast } from '$lib/stores/toast';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Toggle from '$lib/components/ui/forms/Toggle.svelte';
	import { debugSettings } from '$lib/stores/debug-settings';

	// Redirect if already authenticated
	onMount(() => {
		if ($session !== null) {
			goto('/agents');
		}
	});

	async function handleRedirectLogin() {
		try {
			await auth.login();
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Login failed';

			addToast({
				type: 'error',
				message: errorMessage,
				duration: 5000
			});
		}
	}
</script>

<svelte:head>
	<title>Login - Augment Code</title>
</svelte:head>

<div
	class="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950"
	style="background: url('/agents.webp') no-repeat center center fixed; -webkit-background-size: cover; -moz-background-size: cover; -o-background-size: cover; background-size: cover;"
>
	<div class="mx-auto w-full max-w-md">
		<div class="rounded-lg bg-white p-8 shadow-lg dark:bg-gray-900">
			<!-- Logo/Header -->
			<div class="mb-8 text-center">
				<div class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-lg bg-blue-600">
					<svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M13 10V3L4 14h7v7l9-11h-7z"
						/>
					</svg>
				</div>
				<h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome to Augment Actions</h1>
				<p class="mt-2 text-gray-600 dark:text-gray-400">
					Sign in to access your remote agents and start coding with AI assistance
				</p>
			</div>

			<!-- Login Form -->
			<div class="space-y-6">
				<div class="space-y-4">
					<Button
						variant="primary"
						size="lg"
						class="w-full"
						disabled={$isLoading}
						onclick={handleRedirectLogin}
					>
						Sign in with Augment
					</Button>
				</div>

				<div class="space-y-2 text-center">
					<!-- Dev Server Toggle - Less Obvious -->
					<div class="border-t border-gray-100 pt-7 dark:border-gray-800">
						<div
							class="flex items-center justify-center gap-2 text-xs text-gray-500 dark:text-gray-400"
						>
							<span>Point at Igor's dev server</span>
							<Toggle
								checked={!$debugSettings.useStaging}
								onchange={(checked) => debugSettings.setUseStaging(!checked)}
								size="sm"
								variant="default"
								aria-label="Toggle dev server"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
