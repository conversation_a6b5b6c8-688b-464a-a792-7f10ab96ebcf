<script lang="ts">
	import Markdown from '$lib/components/ui/content/Markdown.svelte';

	const testContent =
		`I'll show you some fun code blocks with dogs in them, then quote the dog section from your README!

Here are some dog-themed code examples:

<augment_code_snippet path="example1.py" mode="EXCERPT">
` +
		'````python\n' +
		`class Dog:
    def __init__(self, name, breed):
        self.name = name
        self.breed = breed

    def bark(self):
        return f"{self.name} says WOOF! 🐕"

    def fetch(self, item):
        return f"{self.name} fetches the {item}!"
` +
		'````' +
		`
</augment_code_snippet>

<augment_code_snippet path="example2.js" mode="EXCERPT">
` +
		'````javascript\n' +
		`const dogBreeds = ['Golden Retriever', 'Labrador', 'Beagle'];

function adoptDog(breed) {
    console.log(\`🐾 Congratulations! You adopted a \${breed}!\`);
    return { breed, happiness: 100, energy: 'high' };
}
` +
		'````' +
		`
</augment_code_snippet>

<augment_code_snippet path="example3.rs" mode="EXCERPT">
` +
		'````rust\n' +
		`struct Dog {
    name: String,
    age: u8,
    is_good_boy: bool,
}

impl Dog {
    fn new(name: String, age: u8) -> Self {
        Dog { name, age, is_good_boy: true }
    }
}
` +
		'````' +
		`
</augment_code_snippet>

Now, here's the dog section from your README:

## 🐕 Dog Appreciation Section

This project is proudly dog-friendly! 🦴

` +
		'```\n' +
		`     /^-----^\\
    /  o   o  \\
   /     ^     \\
  |  \\  ---  /  |
   \\  '-----'  /
    \\         /
     '^-----^'
      WOOF!
` +
		'```' +
		`

Remember: Good code is like a good dog - loyal, reliable, and always there when you need it! 🐾`;
</script>

<div class="container mx-auto max-w-4xl p-8">
	<h1 class="mb-8 text-3xl font-bold">Markdown Parsing Test</h1>

	<div
		class="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-gray-950"
	>
		<h2 class="mb-4 text-xl font-semibold">Test Content:</h2>
		<Markdown content={testContent} />
	</div>
</div>
