<script lang="ts">
	import GitHubRepoCombobox from '$lib/components/github/GitHubRepoCombobox.svelte';
	import type { GitHubRepo } from '$lib/api/github-api';

	let selectedRepo: GitHubRepo | null = null;
	let selectedBranch: string | undefined = undefined;

	function handleRepoSelected(repo: GitHubRepo | null, branch?: string) {
		selectedRepo = repo;
		selectedBranch = branch;
		console.log('Selected repo:', repo);
		console.log('Selected branch:', branch);
	}
</script>

<div class="p-8 max-w-2xl mx-auto">
	<h1 class="text-2xl font-bold mb-6">GitHub Repo Combobox Test</h1>

	<div class="space-y-4">
		<div>
			<label class="block text-sm font-medium mb-2">Select Repository:</label>
			<GitHubRepoCombobox
				placeholder="Search repositories or paste GitHub URL..."
				onRepoSelected={handleRepoSelected}
			/>
		</div>

		{#if selectedRepo}
			<div class="p-4 bg-gray-100 rounded-lg">
				<h3 class="font-medium mb-2">Selected Repository:</h3>
				<p><strong>Owner:</strong> {selectedRepo.owner}</p>
				<p><strong>Name:</strong> {selectedRepo.name}</p>
				{#if selectedRepo.description}
					<p><strong>Description:</strong> {selectedRepo.description}</p>
				{/if}
				{#if selectedBranch}
					<p><strong>Branch:</strong> {selectedBranch}</p>
				{/if}
			</div>
		{/if}

		<div class="text-sm text-gray-600">
			<p><strong>Test Instructions:</strong></p>
			<ol class="list-decimal list-inside space-y-1 mt-2">
				<li>Try searching for existing repositories</li>
				<li>Paste a GitHub URL like: https://github.com/facebook/react</li>
				<li>Paste a GitHub URL with branch: https://github.com/facebook/react/tree/main</li>
				<li>Verify that URL repos appear as regular options with descriptive text</li>
				<li>Verify that the sorting order is preserved (URL repos should appear at top)</li>
			</ol>
		</div>
	</div>
</div>
