import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { SECURITY_PASSWORD } from '$env/static/private';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { password } = await request.json();

		if (password === SECURITY_PASSWORD) {
			// Set a cookie to remember site access
			const headers = new Headers();
			headers.set('Set-Cookie', 'site-access=true; Path=/; SameSite=Strict; Max-Age=86400; Secure'); // 24 hours

			return json({ success: true }, { headers });
		} else {
			return json({ error: 'Invalid password' }, { status: 401 });
		}
	} catch (error) {
		return json({ error: 'Invalid request' }, { status: 400 });
	}
};
