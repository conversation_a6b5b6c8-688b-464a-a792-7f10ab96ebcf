import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { env } from '$env/dynamic/private';

const SITE_PASSWORD = env.SECURITY_PASSWORD || 'augment123'; // Default for development

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const { password } = await request.json();

		if (!password) {
			return json({ success: false, error: 'Password is required' }, { status: 400 });
		}

		if (password === SITE_PASSWORD) {
			// Set a secure cookie to remember authentication
			console.log('[site-auth/login] Setting site-authenticated cookie');
			cookies.set('site-authenticated', 'true', {
				path: '/',
				httpOnly: true,
				secure: process.env.NODE_ENV === 'production',
				sameSite: 'lax', // Changed from 'strict' to 'lax' to allow OAuth redirects
				maxAge: 60 * 60 * 24 * 7 // 7 days
			});

			console.log('[site-auth/login] Cookie set successfully');
			return json({ success: true });
		} else {
			return json({ success: false, error: 'Invalid password' }, { status: 401 });
		}
	} catch (error) {
		console.error('Site auth error:', error);
		return json({ success: false, error: 'Internal server error' }, { status: 500 });
	}
};
