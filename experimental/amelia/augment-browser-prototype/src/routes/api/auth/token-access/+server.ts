/**
 * SECURITY NOTICE: This endpoint has been removed for security reasons.
 *
 * Exposing access tokens to client-side JavaScript defeats the purpose of httpOnly cookies
 * and creates XSS vulnerabilities. All API calls should now use server-side proxy endpoints
 * that handle authentication internally without exposing tokens to the client.
 *
 * Use the following secure alternatives:
 * - GitHub API: /api/github/* endpoints
 * - Linear API: /api/linear/* endpoints
 * - Augment API: /api/remote-agents/* endpoints
 *
 * For server-side code, use the secure API clients in src/lib/server/api-client.ts
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async () => {
	return json(
		{
			error:
				'This endpoint has been removed for security reasons. Use server-side proxy endpoints instead.',
			documentation: 'See SECURITY.md for secure API access patterns'
		},
		{ status: 410 } // 410 Gone - indicates the resource is no longer available
	);
};
