import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';
import { PROVIDER_CONFIGS } from '$lib/stores/provider-auth';

/**
 * Handle OAuth callbacks for external providers (GitHub, Linear, Jira, etc.)
 * This endpoint processes the authorization code and exchanges it for tokens
 * using the Augment API's provider-specific hydrate endpoints.
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const { code, providerId, tenantUrl, accessToken } = await request.json();
		const sessionData = await getSessionData(cookies);
		if (!sessionData) {
			return json({ error: 'Authentication required' }, { status: 401 });
		}

		if (!code) {
			return json({ error: 'Authorization code is required' }, { status: 400 });
		}

		if (!providerId) {
			return json({ error: 'Provider ID is required' }, { status: 400 });
		}

		if (!tenantUrl || !accessToken) {
			return json({ error: 'Authentication required' }, { status: 401 });
		}

		// Use the provider configuration from the auth store
		const config = PROVIDER_CONFIGS[providerId];

		if (!config) {
			return json({ error: `Unknown provider: ${providerId}` }, { status: 400 });
		}

		const hydrateEndpoint = config.hydrateEndpoint;

		// Exchange the authorization code for tokens using the provider's hydrate endpoint
		const response = await fetch(`${tenantUrl}${hydrateEndpoint}`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`
			},
			body: JSON.stringify({ code })
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error(`Provider ${providerId} hydrate failed:`, response.status, errorText);
			return json(
				{ error: `Failed to authenticate with ${providerId}: ${response.statusText}` },
				{ status: response.status }
			);
		}

		const result = await response.json();

		return json({
			success: true,
			providerId,
			message: `Successfully connected to ${providerId}`,
			data: result
		});
	} catch (error) {
		console.error('Provider OAuth callback error:', error);
		return json({ error: 'Internal server error during OAuth callback' }, { status: 500 });
	}
};

/**
 * Handle GET requests for OAuth callbacks (when providers redirect back)
 * This extracts the authorization code from URL parameters and processes it
 */
export const GET: RequestHandler = async ({ url }) => {
	const code = url.searchParams.get('code');
	const state = url.searchParams.get('state');
	const error = url.searchParams.get('error');
	const errorDescription = url.searchParams.get('error_description');

	// Handle OAuth errors
	if (error) {
		console.error('OAuth error:', error, errorDescription);

		// Redirect to a page that can handle the error
		const errorUrl = new URL('/auth/callback-error', url.origin);
		errorUrl.searchParams.set('error', error);
		if (errorDescription) {
			errorUrl.searchParams.set('error_description', errorDescription);
		}

		return new Response(null, {
			status: 302,
			headers: {
				Location: errorUrl.toString()
			}
		});
	}

	if (!code) {
		console.error('No authorization code received');
		const errorUrl = new URL('/auth/callback-error', url.origin);
		errorUrl.searchParams.set('error', 'no_code');
		errorUrl.searchParams.set('error_description', 'No authorization code received');

		return new Response(null, {
			status: 302,
			headers: {
				Location: errorUrl.toString()
			}
		});
	}

	// Redirect to a page that can handle the OAuth callback
	// The frontend will extract the code and complete the OAuth flow
	const callbackUrl = new URL('/auth/provider-callback', url.origin);
	callbackUrl.searchParams.set('code', code);
	if (state) {
		callbackUrl.searchParams.set('state', state);
	}

	return new Response(null, {
		status: 302,
		headers: {
			Location: callbackUrl.toString()
		}
	});
};
