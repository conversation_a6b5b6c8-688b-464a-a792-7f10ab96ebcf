import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const GET: RequestHandler = async ({ cookies }) => {
	try {
		const sessionData = await getSessionData(cookies);

		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'No valid session' }, { status: 401 });
		}

		return json({
			isAuthenticated: true,
			tenantUrl: sessionData.tenantUrl
		});
	} catch (error) {
		return json({ error: 'Session check failed' }, { status: 401 });
	}
};
