import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

/**
 * Set site authentication cookie for OAuth-authenticated users
 * This allows users who have completed OAuth to bypass the site password
 */
export const POST: RequestHandler = async ({ cookies }) => {
	try {
		// Check if user has a valid OAuth session
		const sessionData = await getSessionData(cookies);

		if (!sessionData) {
			return json({ error: 'No valid session found' }, { status: 401 });
		}

		// Set the site authentication cookie
		cookies.set('site-authenticated', 'true', {
			path: '/',
			httpOnly: true,
			secure: true,
			sameSite: 'lax',
			maxAge: 60 * 60 * 24 * 7 // 7 days
		});

		return json({ success: true });
	} catch (error) {
		console.error('Failed to set site auth cookie:', error);
		return json({ error: 'Failed to set site authentication' }, { status: 500 });
	}
};
