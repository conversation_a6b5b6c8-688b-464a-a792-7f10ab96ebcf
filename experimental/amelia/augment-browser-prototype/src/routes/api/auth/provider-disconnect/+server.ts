import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';
import { PROVIDER_CONFIGS } from '$lib/stores/provider-auth';

/**
 * Handle provider disconnection by calling the tenant's revoke endpoints
 * This endpoint processes the provider ID and calls the appropriate revoke endpoint
 * using the user's access token via the tenant URL.
 */
export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const { providerId } = await request.json();

		const sessionData = await getSessionData(cookies);
		if (!sessionData) {
			return json({ error: 'Authentication required' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		if (!providerId) {
			return json({ error: 'Provider ID is required' }, { status: 400 });
		}

		if (!tenantUrl || !accessToken) {
			return json({ error: 'Authentication required' }, { status: 401 });
		}

		// Use the provider configuration from the auth store
		const config = PROVIDER_CONFIGS[providerId];

		if (!config) {
			return json(
				{ error: `Disconnect not supported for provider: ${providerId}` },
				{ status: 400 }
			);
		}

		// Use the unified revoke endpoint with tool_id
		const revokeEndpoint = '/agents/revoke-tool-access';
		// Ensure no double slashes in URL
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const revokeUrl = `${cleanTenantUrl}${revokeEndpoint}`;

		// Call the revoke endpoint with tool_id
		const response = await fetch(revokeUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`
			},
			body: JSON.stringify({
				tool_id: config.remoteToolId
			})
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error(`Provider ${providerId} disconnect failed:`, response.status, errorText);

			// Handle specific error cases
			if (response.status === 401) {
				return json(
					{ error: `Authentication failed when disconnecting ${config.name}` },
					{ status: 401 }
				);
			} else if (response.status === 404) {
				return json(
					{ error: `${config.name} integration not found or already disconnected` },
					{ status: 404 }
				);
			} else {
				return json(
					{ error: `Failed to disconnect ${config.name}: ${response.statusText}` },
					{ status: response.status }
				);
			}
		}

		return json({
			success: true,
			providerId,
			message: `Successfully disconnected from ${config.name}`
		});
	} catch (error) {
		console.error('Provider disconnect error:', error);
		return json({ error: 'Internal server error during provider disconnect' }, { status: 500 });
	}
};
