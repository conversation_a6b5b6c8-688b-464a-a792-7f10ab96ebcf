import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import {
	augmentSession,
	githubSession,
	linearSession,
	addSessionCookie,
	type AugmentSession,
	type GitHubSession,
	type LinearSession
} from '$lib/server/session';

interface MigrationRequest {
	provider: 'augment' | 'github' | 'linear';
	session: any;
}

/**
 * POST /api/auth/session/migrate
 * Migrate legacy localStorage sessions to secure server-side storage
 */
export const POST: RequestHandler = async ({ request }) => {
	try {
		const { provider, session: sessionData }: MigrationRequest = await request.json();

		if (!provider || !sessionData) {
			return json({ error: 'Provider and session data are required' }, { status: 400 });
		}

		const headers = new Headers();
		let sessionCookie: string;

		switch (provider) {
			case 'augment': {
				// Validate Augment session data
				if (!sessionData.accessToken || !sessionData.tenantUrl) {
					return json({ error: 'Invalid Augment session data' }, { status: 400 });
				}

				const augmentSessionData: AugmentSession = {
					accessToken: sessionData.accessToken,
					tenantUrl: sessionData.tenantUrl,
					scopes: sessionData.scopes || '',
					expiresAt: sessionData.expiresAt || Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days (SOC2 compliance)
					refreshToken: sessionData.refreshToken
				};

				sessionCookie = augmentSession.set(augmentSessionData);
				break;
			}

			case 'github': {
				// Validate GitHub session data
				if (!sessionData.accessToken) {
					return json({ error: 'Invalid GitHub session data' }, { status: 400 });
				}

				const githubSessionData: GitHubSession = {
					accessToken: sessionData.accessToken,
					scopes: sessionData.scopes || ['repo'],
					expiresAt: sessionData.expiresAt || Date.now() + 30 * 24 * 60 * 60 * 1000
				};

				sessionCookie = githubSession.set(githubSessionData);
				break;
			}

			case 'linear': {
				// Validate Linear session data
				if (!sessionData.accessToken) {
					return json({ error: 'Invalid Linear session data' }, { status: 400 });
				}

				const linearSessionData: LinearSession = {
					accessToken: sessionData.accessToken,
					expiresAt: sessionData.expiresAt || Date.now() + 30 * 24 * 60 * 60 * 1000
				};

				sessionCookie = linearSession.set(linearSessionData);
				break;
			}

			default:
				return json({ error: 'Invalid provider' }, { status: 400 });
		}

		addSessionCookie(headers, sessionCookie);

		return json(
			{
				success: true,
				provider,
				message: `${provider} session migrated successfully`
			},
			{ headers }
		);
	} catch (error) {
		console.error('Session migration error:', error);
		return json({ error: 'Failed to migrate session' }, { status: 500 });
	}
};
